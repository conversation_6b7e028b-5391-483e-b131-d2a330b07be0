/* $Id$ */

package com.zoho.sheet.integ.gadgets;

import java.net.URL;
import java.util.HashMap;

/**
 * <AUTHOR>
 */

public class GAppsApiHolder implements ZGApiHolder {

	static HashMap gAppsApi = new HashMap();
	
	static {
		
		/* -- API Reference: https://intranet.wiki.zoho.com/gadgets/Google-Apps-APIs.html -- */
		
		//GADGETSURL+/zc/gapps.do?mode=isGUser&zservice=<Service name used in Gadgets>&ticket=<ticket>
		gAppsApi.put("ISGUSER", "isValidGAppsUser"); //NO I18N
		
		//GadgetsUrl+"/zc/gapps.do?mode=getUsers&zservice=<Service name used in Gadgets>&ticket=<ticket>
		gAppsApi.put("GET_USERS", "getUsers"); //NO I18N

		//GadgetsUrl+"/zc/gapps.do?mode=getAdmins&zservice=<Service name used in Gadgets>&ticket=<ticket>
		gAppsApi.put("GET_ADMINS", "getAdmins"); //NO I18N
		

	}
	
	@Override
	public String getMode(String api) {	
		return (String) gAppsApi.get(api);		
	}

	@Override
	public String getApiFormat() {
		return "/zc/gapps.do"; //NO I18N
	}
}
