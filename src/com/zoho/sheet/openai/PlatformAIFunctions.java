package com.zoho.sheet.openai;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.univocity.parsers.csv.CsvParser;
import com.univocity.parsers.csv.CsvParserSettings;
import com.zoho.accounts.AccountsConstants;
import com.zoho.sheet.util.RemoteUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Matcher;

/*
 * <AUTHOR>
 */

/**
 * class which contains all the gpt functions
 */
public class PlatformAIFunctions {

    private static final String BASE_URL = EnginePropertyUtil.getSheetPropertyValue("OPENAI_BASE_URL");  //No I18N

    public enum ResponseStatus {
        API_CONNECTION_ERROR,
        INTERNAL_SERVER_ERROR,
        INVALID_REQUEST,
        INVALID_RESPONSE,
        REQ_LIMIT,
        TOKEN_LIMIT,
        CREDITS_LIMIT,
        API_KEY_INVALID,
        TOKEN_EXCEED,
        SUCCESS,
        CONTENT_EXCEED,
        NO_TOKEN,               // internal
        REQ_TIMEOUT,
        FEATURE_NOT_ENABLED;    // internal

        public static int getResponseCode(ResponseStatus status) {
            int code = 0;
            switch (status) {
                case INVALID_REQUEST:
                case API_KEY_INVALID:
                case TOKEN_EXCEED:
                    code = 401;
                    break;
                case INTERNAL_SERVER_ERROR:
                    code = 500;
                    break;
                case INVALID_RESPONSE:
                case CONTENT_EXCEED:
                    code = 400;
                    break;
                case REQ_LIMIT:
                case TOKEN_LIMIT:
                case CREDITS_LIMIT:
                    code = 429;
                    break;
                case NO_TOKEN:
                    code = 404;
                    break;
                case API_CONNECTION_ERROR:
                    code = 520;
                    break;
                case REQ_TIMEOUT:
                    code = 408;
                    break;
                case SUCCESS:
                    code = 200;
                    break;
            }
            return code;
        }
    }

    public static class ConnectionResponse {
        /**
         * code 401 - Invalid Token ::: token mapping error in PlatformAI
         * code 500 - Internal Server Error ::: API service is down
         * code 400 - Invalid Response from OpenAI
         * code 429 - Rate Limit reached / Insufficient credits
         * code 401 - API key invalid / tokens exceeded
         * code 404 - NO Token Configured yet (ONLY FOR INTERNAL API CALLS)
         * code 403 - Feature not enabled (ONLY FOR INTERNAL API CALLS)
         */
        private final Object responseContent;
        private final ResponseStatus responseStatus;

        public ConnectionResponse(Object responseContent, ResponseStatus responseStatus) {
            this.responseContent = responseContent;
            this.responseStatus = responseStatus;
        }

        public Object getResponseContent() {
            return responseContent;
        }

        public ResponseStatus getResponseStatus() {
            return responseStatus;
        }

        public static ConnectionResponse getErrorResponse(ResponseStatus status) {
            return new ConnectionResponse("", status);
        }
    }

    private static String getAsChatJSON(String userQuery) {
        List<Map<String, String>> request = new ArrayList<>();
        request.add(new HashMap<>() {{
            put("role", "user");    //No I18N
            put("content", userQuery);  //No I18N
        }});
        org.json.JSONObject jsonObject = new org.json.JSONObject(request.get(0));
        return jsonObject.toString();
    }

    private static String getAsSystemJSON(String prompt) {
        List<Map<String, String>> request = new ArrayList<>();
        request.add(new HashMap<>() {{
            put("role", "system");    //No I18N
            put("content", prompt);  //No I18N
        }});
        org.json.JSONObject jsonObject = new org.json.JSONObject(request.get(0));
        return jsonObject.toString();
    }

    private static PlatformAIParams buildParams(List<String> promptList, PlatformAIConnection.FeatureType defaultEndPoint, double temperature, int tokens) {
        PlatformAIParams.ModelParams params;

        if (Objects.requireNonNull(defaultEndPoint) == PlatformAIConnection.FeatureType.CHAT) {
            params = new PlatformAIParams.ChatParams(promptList);
            params.setTemperature(temperature);
        } else {
            params = new PlatformAIParams.DefaultParams(promptList.get(0));
        }

        params.setMax_tokens(tokens);
        return params;
    }

    private static ConnectionResponse fetchResponse(PlatformAIConnection.FeatureType type, PlatformAIParams params, PlatformAIStore paramStore, String funcName) {

        Map<String, String> headerMap = new HashMap<>();
        Map<String, String> paramsMap = new HashMap<>();

        if (paramStore == null) {
            return new ConnectionResponse("", ResponseStatus.NO_TOKEN);
        } else {
            String portalId = paramStore.getPortalId();
            String serviceOrgId = paramStore.getServiceOrgId();
            String tokenId = paramStore.getTokenId();
            headerMap.put(OpenAiConstants.HEADERS.PORTAL_ID, portalId);
            headerMap.put(OpenAiConstants.HEADERS.TOKEN_ID, tokenId);
            headerMap.put(OpenAiConstants.HEADERS.SERVICE_ORG, serviceOrgId);
        }

        headerMap.put(OpenAiConstants.HEADERS.SERVICE, OpenAiConstants.HEADERS.TEAMDRIVE);
        headerMap.put(OpenAiConstants.HEADERS.INTEG_SERVICE, "zoho_sheet");

        if (paramStore.isOAuthType()) {

            PlatformAIAuthManager.OAuth authTokenBean = PlatformAIAuthManager.getAuthToken(paramStore.getServiceOrgId());
            String accessToken = authTokenBean.getUpdatedAccessToken(paramStore);
            headerMap.put(OpenAiConstants.HEADERS.AUTH, "Bearer " + accessToken);
            headerMap.put(OpenAiConstants.HEADERS.ZUID, authTokenBean.getOwnerZUID());

        } else {
            String ticket = IAMUtil.getCurrentTicket();
            try {
                if (IAMUtil.getCurrentRequest() != null) {
                    headerMap.put(OpenAiConstants.HEADERS.REMOTE_IP, IAMUtil.generateRemoteUserIPSignature(IAMUtil.getCurrentRequest()));
                    headerMap.put(AccountsConstants.Z_USERAGENT, IAMUtil.getUserAgent(IAMUtil.getCurrentRequest()));
                    String _X_MDM_Token = RemoteUtils.maskNull(IAMUtil.getCurrentRequest().getHeader(Constants.X_MDM_TOKEN));
                    if(_X_MDM_Token!=null){
                        headerMap.put(Constants.X_MDM_TOKEN,_X_MDM_Token);
                    }
                }
            } catch (Exception ex) {
                PlatformAILogger.writeInLog("[OPEN-AI] Exception occured while creating headerMap", ex);    // No I18N
            }

            paramsMap.put(OpenAiConstants.PARAMS.TICKET, ticket);
        }

        PlatformAIConnection connection = new PlatformAIConnection(BASE_URL, new PlatformAIConnection.FeatureType[]{PlatformAIConnection.FeatureType.OPENAI, type}, headerMap, paramsMap, params, "POST"); //NO I18N
        connection.process();

        int responseCode = connection.getResponseCode();
        String responseContent = connection.getResponseContent();


        JSONObjectWrapper responseObj = new JSONObjectWrapper(responseContent);

        if (responseCode == HttpServletResponse.SC_OK) {
            JSONArrayWrapper response = responseObj.optJSONArray("data");   //NO I18N
            String data;
            if (funcName.equals("Image")) {
                //image does not have data key inside
                data = response.toString();
            } else {
                data = response.getJSONObject(0).getString("data");  //NO I18N
            }
            return Objects.isNull(data) || data.length() < 1 ? new ConnectionResponse("", ResponseStatus.SUCCESS) : new ConnectionResponse(data, ResponseStatus.SUCCESS);
        } else {
            //request failed handle the error
            PlatformAILogger.writeInLog(funcName, responseContent, responseCode);
            return handleFailureCodes(responseCode, responseObj);
        }

    }

    private static ConnectionResponse handleFailureCodes(int responseCode, JSONObjectWrapper responseObj) {
        ResponseStatus responseStatus = null;
        switch (responseCode) {
            case HttpServletResponse.SC_INTERNAL_SERVER_ERROR:
                responseStatus = ResponseStatus.INTERNAL_SERVER_ERROR;
                break;
            case 429:   // too many request code
                String type = responseObj.getJSONObject("error").getJSONObject("api_error").getString("type");
                switch (type) {
                    case "requests":
                        responseStatus = ResponseStatus.REQ_LIMIT;
                        break;
                    case "tokens":
                        responseStatus = ResponseStatus.TOKEN_LIMIT;
                        break;
                    case "insufficient_quota":
                        responseStatus = ResponseStatus.CREDITS_LIMIT;
                        break;
                }
                break;
            case HttpServletResponse.SC_UNAUTHORIZED:
                int code = responseObj.getJSONObject("error").getInt("code");
                if (code == 401) {
                    responseStatus = ResponseStatus.API_KEY_INVALID;
                } else if (code == 400) {
                    responseStatus = ResponseStatus.TOKEN_EXCEED;
                }
                break;
            case 520:
                responseStatus = ResponseStatus.API_CONNECTION_ERROR;   //exception thrown in requestTask
                break;
            case HttpServletResponse.SC_REQUEST_TIMEOUT:
                responseStatus = ResponseStatus.REQ_TIMEOUT;
                break;
            case HttpServletResponse.SC_SERVICE_UNAVAILABLE:
                responseStatus = ResponseStatus.REQ_LIMIT;   // request limit reached response directly from openai
                break;
            case HttpServletResponse.SC_BAD_REQUEST:
                String message = responseObj.getJSONObject("error").optString("message", "");   //No I18N
                if (message.equals("URL_ROLLING_THROTTLES_LIMIT_EXCEEDED")) {   //URL throttle limit from platformai
                    responseStatus = ResponseStatus.REQ_LIMIT;
                } else {
                    String limitMessage = responseObj.getJSONObject("error").optJSONObject("api_error").optString("code");   //No I18N
                    if (limitMessage.equals("context_length_exceeded")) {
                        responseStatus = ResponseStatus.CONTENT_EXCEED;
                    } else {
                        responseStatus = ResponseStatus.INVALID_REQUEST;
                    }
                }
                break;
            default:
                //invalid response case
                responseStatus = ResponseStatus.INVALID_RESPONSE;
                break;
        }
        return new ConnectionResponse("", responseStatus);
    }

    public static ConnectionResponse gptGeneralQuery(String userQuery, PlatformAIStore paramStore, boolean isClientReq) {
        String systemPrompt = getAsSystemJSON(OpenAiConstants.SystemInjections.GENERAL);
        String queryPrompt = getAsChatJSON(userQuery);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(systemPrompt);
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.CREATIVE, OpenAiConstants.MaxTokens.GENERAL_QUERY);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "generalQuery");   //No I18N
    }

    public static ConnectionResponse gptTranslate(String translateQuery, String toLanguage, String srcLanguage, PlatformAIStore paramStore) {
        String promptInject;
        if (Objects.nonNull(srcLanguage)) {
            promptInject = String.format(OpenAiConstants.ServerPromptInjections.TRANSLATE_SOURCE, translateQuery, toLanguage, srcLanguage);
        } else {
            promptInject = String.format(OpenAiConstants.ServerPromptInjections.TRANSLATE, translateQuery, toLanguage);
        }

        String userQuery = getAsChatJSON(promptInject);

        PlatformAIParams params = buildParams(Collections.singletonList(userQuery), PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.TRANSLATE);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "Translate");  //No I18N
    }

    public static ConnectionResponse gptExtract(String extractQuery, File file, PlatformAIStore paramStore, boolean isClientReq) {
        String promptInject = String.format(isClientReq ? OpenAiConstants.ClientPromptInjections.EXTRACT : OpenAiConstants.ServerPromptInjections.EXTRACT, entityQuery, extractQuery);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(getAsSystemJSON(OpenAiConstants.SystemInjections.EXTRACT));
        finalQuery.add(getAsChatJSON(promptInject));

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.EXTRACT);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "Extract");    //No I18N
    }

    public static ConnectionResponse gptGenerateFormula(String formulaQuery, PlatformAIStore paramStore, boolean isClientReq) {
//        formulaQuery = String.format(OpenAiConstants.ClientPromptInjections.FORMULA_GENERATION, formulaQuery);

        String systemPrompt = getAsSystemJSON(OpenAiConstants.SystemInjections.FORMULA_GENERATION);
        String queryPrompt = getAsChatJSON(formulaQuery);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(systemPrompt);
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.FORMULA_GENERATOR);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "generateFormula");    //No I18N
    }

    public static ConnectionResponse gptExplainFormula(String formula, PlatformAIStore paramStore, boolean isClientReq) {
//        formula = String.format(OpenAiConstants.ClientPromptInjections.FORMULA_EXPLAINER, formula);

        String queryPrompt = getAsChatJSON(formula);
        String systemPrompt = getAsSystemJSON(OpenAiConstants.SystemInjections.FORMULA_EXPLAINER);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(systemPrompt);
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.FORMULA_EXPLAINER);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "explainFormula"); //No I18N
    }

    public static ConnectionResponse gptGenerateVBA(String vbaQuery, PlatformAIStore paramStore, boolean isClientReq) {
//        vbaQuery = String.format(OpenAiConstants.ClientPromptInjections.VBA_GENERATOR, vbaQuery);

        String queryPrompt = getAsChatJSON(vbaQuery);
        String systemPrompt = getAsSystemJSON(OpenAiConstants.SystemInjections.VBA_GENERATOR);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(systemPrompt);
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.VBA_GENERATOR);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "generateVBA");    //No I18N
    }

    public static ConnectionResponse gptExplainVBA(String vbaQuery, PlatformAIStore paramStore, boolean isClientReq) {
//        String queryPrompt = getAsChatJSON(String.format(OpenAiConstants.ClientPromptInjections.VBA_EXPLAINER, vbaQuery));
        String queryPrompt = getAsChatJSON(vbaQuery);
        String systemPrompt = getAsSystemJSON(OpenAiConstants.SystemInjections.VBA_EXPLAINER);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(systemPrompt);
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.VBA_EXPLAINER);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "explainVBA");    //No I18N
    }

    public static ConnectionResponse gptTable(String tableQuery, PlatformAIStore paramStore, boolean isClientReq) {

        String systemPrompt = getAsSystemJSON(OpenAiConstants.SystemInjections.GENERATE_TABLE);
        tableQuery = String.format(OpenAiConstants.ServerPromptInjections.TABLE, tableQuery);
        String queryPrompt = getAsChatJSON(tableQuery);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(systemPrompt);
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.GENERATE_TABLE);

        ConnectionResponse connectionResponse = fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "generateTable");//NO I18N
        String response = (String) connectionResponse.getResponseContent();

        if (connectionResponse.getResponseStatus() != ResponseStatus.SUCCESS) {
            return connectionResponse;
        }

        //sometimes invalid comes under quotes
        if (response.equalsIgnoreCase("invalid") || response.equalsIgnoreCase("\"invalid\"")) {   //No I18N
            return new ConnectionResponse("Invalid", ResponseStatus.SUCCESS);   //No I18N
        }

        InputStream inputStream = new ByteArrayInputStream(response.getBytes());
        CsvParserSettings csvParserSettings = new CsvParserSettings();
        csvParserSettings.getFormat().setLineSeparator("\n");   //No I18N
        csvParserSettings.setEscapeUnquotedValues(true);
        csvParserSettings.setQuoteDetectionEnabled(true);
        csvParserSettings.setKeepEscapeSequences(true);
        CsvParser parser = new CsvParser(csvParserSettings);
        List<String[]> strings = parser.parseAll(inputStream);

        return new ConnectionResponse(strings, ResponseStatus.SUCCESS);
    }

    public static ConnectionResponse gptClassify(String query, String categories, PlatformAIStore paramStore) {
        String promptInject = String.format(OpenAiConstants.ServerPromptInjections.CLASSIFY, query, categories);

        String systemPrompt = getAsSystemJSON(OpenAiConstants.SystemInjections.EXTRACT);    // re-using extract system prompt
        String queryPrompt = getAsChatJSON(promptInject);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(systemPrompt);
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.CLASSIFY);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "gptClassify");    //No I18N
    }

    public static ConnectionResponse gptSentimentAnalysis(String query, PlatformAIStore paramStore) {
        String promptInject = String.format(OpenAiConstants.ServerPromptInjections.SENTIMENT, query);
        String queryPrompt = getAsChatJSON(promptInject);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.SENTIMENT);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "gptSentiment");     //No I18N
    }

    public static ConnectionResponse gptTag(String query, String label, PlatformAIStore paramStore) {
        String promptInject = String.format(OpenAiConstants.ServerPromptInjections.LABEL, query, label);

        String systemPrompt = getAsSystemJSON(OpenAiConstants.SystemInjections.EXTRACT);    // re-using extract system prompt
        String queryPrompt = getAsChatJSON(promptInject);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(systemPrompt);
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.DETERMINISTIC, OpenAiConstants.MaxTokens.TAG);
        return fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "gptTag");    //No I18N
    }

    public static ConnectionResponse gptList(String query, PlatformAIStore paramStore) {
        String systemPrompt = getAsSystemJSON(OpenAiConstants.SystemInjections.LIST);
        String queryPrompt = getAsChatJSON(query);

        List<String> finalQuery = new ArrayList<>();
        finalQuery.add(systemPrompt);
        finalQuery.add(queryPrompt);

        PlatformAIParams params = buildParams(finalQuery, PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.CREATIVE, OpenAiConstants.MaxTokens.LIST);
        ConnectionResponse connectionResponse = fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "gptList");//No I18N
        if (connectionResponse.getResponseStatus() != ResponseStatus.SUCCESS) {
            return connectionResponse;
        }

        String responseContent = (String) connectionResponse.getResponseContent();
        List<String> responseArr = Arrays.asList(responseContent.split("\n"));
        return new ConnectionResponse(responseArr, ResponseStatus.SUCCESS);
    }

    public static ConnectionResponse gptFill(List<List<String>> sampleData, List<List<String>> testData, PlatformAIStore paramStore) {
        List<List<String>> fillResponseArr = new ArrayList<>();
        String sampleDataStr = getAsEscapedStr(sampleData);
        String testDataStr = getAsEscapedStr(testData);

        String query = String.format(OpenAiConstants.ServerPromptInjections.FILL, sampleDataStr, testDataStr);
        String queryPrompt = getAsChatJSON(query);

        PlatformAIParams params = buildParams(Collections.singletonList(queryPrompt), PlatformAIConnection.FeatureType.CHAT, OpenAiConstants.Temperature.CREATIVE, OpenAiConstants.MaxTokens.FILL);

        ConnectionResponse connectionResponse = fetchResponse(PlatformAIConnection.FeatureType.CHAT, params, paramStore, "gptFill");//No I18N

        if (connectionResponse.getResponseStatus() != ResponseStatus.SUCCESS) {
            return connectionResponse;
        }

        String responseContent = (String) connectionResponse.getResponseContent();
        int testDataSize = testData.get(0).size();

        String[] rows = responseContent.split("\n");
        for (int i = 0; i < rows.length; i++) {
            String[] splitRowData = rows[i].split(OpenAiConstants.Regex.splitRegex);
            List<String> rowArr = new ArrayList<>();
            for (int j = testDataSize; j < splitRowData.length; j++) {
                String unEscapedRowData = splitRowData[i].replaceAll(OpenAiConstants.Regex.replaceRegex, "|");  //No I18N
                rowArr.add(unEscapedRowData);
            }
            fillResponseArr.add(rowArr);
        }
        return new ConnectionResponse(fillResponseArr, ResponseStatus.SUCCESS);
    }

    private static String getAsEscapedStr(List<List<String>> dataList) {
        StringBuilder sampleDataStr = new StringBuilder();

        for (int i = 0; i < dataList.size(); i++) {
            List<String> tempList = new ArrayList<>();

            for (String data : dataList.get(i)) {
                Matcher matcher = OpenAiConstants.Regex.pipePattern.matcher(data);
                if (matcher.find()) {
                    // pipe operator is present escape it
                    tempList.add(data.replaceAll(OpenAiConstants.Regex.pipeRegex, OpenAiConstants.Regex.replaceRegex));
                } else {
                    tempList.add(data);
                }
            }
            sampleDataStr.append(java.lang.String.join("|", tempList));
            if (i < dataList.size() - 1) {
                sampleDataStr.append("\n");
            }
        }
        return sampleDataStr.toString();
    }
}