package com.zoho.sheet.openai;

import com.adventnet.zoho.websheet.model.util.OpenAIActionConstants;
import com.zoho.accounts.AccountsConstants;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */

public class OpenAiConstants {
    protected static String TABLE_NAME = "AuthToken";  //NO I18N

    protected static class SCOPES {
        protected static final String OPEN_AI = "OPENAI";   //NO I18N
        protected static final String AUTH_SCOPE = "PlatformAI.organizations.all";    //NO I18N
    }

    protected static class DB_KEYS {
        protected static final String TOKEN_ID = "tokenId";     //NO I18N
        protected static final String PORTAL_ID = "portalId";   //NO I18N
        protected static final String REFRESH_TOKEN = "refreshToken";   //NO I18N
        protected static final String ACCESS_TOKEN = "accessToken";     //NO I18N
        protected static final String TIME_UPDATED = "timeUpdated";     //NO I18N
        protected static final String OWNER_ID = "ownerId";     //NO I18N
    }

    protected static class COLUMN_NAMES {
        //Reusing unused old db-space, hence column name will not be appropriate
        protected static final String TOKEN_ID = "AUTHTOKEN";  //NO I18N
        protected static final String FEATURE_NUM = "ZUID";  //NO I18N
        protected static final String SCOPE = "SCOPE";  //NO I18N
    }

    protected static class HEADERS {
        protected static final String SERVICE = "service";  //NO I18N
        protected static final String SERVICE_ORG = "service_org_id";   //NO I18N
        protected static final String ZUID = "zuid";    //NO I18N
        protected static final String PORTAL_ID = "portal_id";  //NO I18N
        protected static final String TOKEN_ID = "token_id";    //NO I18N
        protected static final String TEAMDRIVE = String.valueOf(AccountsConstants.ServiceOrgType.TEAMDRIVE.getType());
        protected static final String INTEG_SERVICE = "integrated_service"; //NO I18N
        protected static final String AUTH = "Authorization";   //NO I18N
        protected static final String REMOTE_IP = "Z-SIGNED_REMOTE_USER_IP";    //NO I18N
    }

    public enum ERRORS {     //error constants for platformAI console actions
        DB_EXCEPTION,
        USER_ERROR,
        KEY_INVALID,
        ADD_FAILURE,
        ADD_MAP_TOKEN,
        INVALID_ACTION,
        UPDATE_FAILURE,
        PORTAL_FAILURE,
        GET_TOKEN_FAILURE,
        DOCS_USER_ERROR,
        TOKEN_NOT_FOUND,
        PERMISSION_DENIED,
        INTEG_DISABLED,
        FEATURE_DISABLED,
        ACCESS_DENIED,
        PARAM_MISMATCH;

        public String getAsJSON() {
            String template = "{%s}";    //No I18N
            return String.format(template, this.name());
        }
    }

    public static class RESPONSE_HEADERS {
        public static final String SERVICE = "services";                  //NO I18N
        public static final String ERROR = "error";                       //NO I18N
        public static final String IS_TOKEN_PRESENT = "isTokenPresent";   //NO I18N
        public static final String DISABLE_FEATURE = "disabledFeature";   //NO I18N
        public static final String ISADMIN = "isAdmin";                   //NO I18N
        public static final String ADMIN_CONTACT = "adminContact";        //NO I18N
        public static final String OPERATION = "operation";               //NO I18N
        public static final String SUCCESS = "success";                   //NO I18N
        public static final String INVALIDKEY = "invalidKey";             //NO I18N
        public static final String RESPONSE ="response";                  //NO I18N
        public static final String IS_DOCUSER = "isDocsUser";             //NO I18N
    }

    protected static class PARAMS {
        protected static final String TICKET = "ticket";    //NO I18N
        protected static final String API_KEY = "api_key";  //NO I18N
    }

    public static class ADMIN_ROLES {
        public static final int SUPER_ADMIN = 32;
        public static final int ADMIN = 31;
    }

    /**
     * order of the feature list is important, sequential
     * if either a new feature is added or deleted, the feature value should be sequential always
     */

    public enum FEATURE_LIST {
        PAUSE_INTEGRATION(0),
        AI_BOT(1),
        AI_FUNCTION(2),
        AI_IMPORT(3);

        private final int featureValue;

        FEATURE_LIST(int featureValue) {
            this.featureValue = featureValue;
        }

        public int getFeatureValue() {
            return featureValue;
        }
    }

    public static Map<Integer, FEATURE_LIST> featureIndex = new HashMap<>();

    static {
        featureIndex.put(OpenAIActionConstants.GPT_GENERAL_QUERY, FEATURE_LIST.AI_BOT);
        featureIndex.put(OpenAIActionConstants.GPT_GENERATE_FORMULA, FEATURE_LIST.AI_BOT);
        featureIndex.put(OpenAIActionConstants.GPT_EXPLAIN_FORMULA, FEATURE_LIST.AI_BOT);
        featureIndex.put(OpenAIActionConstants.GPT_GENERATE_VBA, FEATURE_LIST.AI_BOT);
        featureIndex.put(OpenAIActionConstants.GPT_EXPLAIN_VBA, FEATURE_LIST.AI_BOT);
        featureIndex.put(OpenAIActionConstants.GPT_TABLE, FEATURE_LIST.AI_IMPORT);
        featureIndex.put(OpenAIActionConstants.GPT_EXTRACT_FILE, FEATURE_LIST.AI_BOT);
    }

    protected static class Regex {
        public static String pipeRegex = "\\|";
        public static String replaceRegex = "\\\\\\\\\\|";
        public static Pattern pipePattern = Pattern.compile(pipeRegex);
        public static String splitRegex = "(?<!\\\\)\\|";
    }

    protected static class MaxTokens {
        protected static final int GENERAL_QUERY = 2000;
        protected static final int TRANSLATE = 1000;
        protected static final int EXTRACT = 200;
        protected static final int VBA_GENERATOR = 1500;
        protected static final int VBA_EXPLAINER = 1500;
        protected static final int GENERATE_TABLE = 3000;
        protected static final int EXTRACT_FILE = 4000;
        protected static final int FORMULA_GENERATOR = 500;
        protected static final int FORMULA_EXPLAINER = 500;
        protected static final int CLASSIFY = 200;
        protected static final int SENTIMENT = 200;
        protected static final int TAG = 200;
        protected static final int LIST = 1000;
        protected static final int FILL = 2000;
    }

    protected static class ServerPromptInjections {
        protected static final String TRANSLATE = "Translate %s to %s.Response only should have translated result";                     //No I18N
        protected static final String TRANSLATE_SOURCE = "Translate %s to %s from %s.Response only should have translated result";      //No I18N
        protected static final String EXTRACT = "Extract %s from %s";                       //No I18N
        protected static final String CLASSIFY = "classify %s from %s"; //No I18N
        protected static final String LABEL = "label %s from tags %s";  //No I18N
        protected static final String FILL = "learn the data\n%s\nGenerate the same for following\n%s"; //No I18N
        protected static final String SENTIMENT = "Classify the following sentence %s as positive, negative, or neutral:"; //No I18N
        protected static final String TABLE = "Give tabular data for %s";   //No I18N
        protected static final String EXTRACT_FILE = "Extract structured data from the provided file and convert it to tabular format. Focus on identifying tables, lists, or any structured information that can be organized into rows and columns.";   //No I18N
    }

    protected static class ClientPromptInjections {
        protected static final String EXTRACT = "Extract %s from %s.";                       //No I18N
    }

    protected static class SystemInjections {
        protected static final String GENERAL = "You are a AI assistant.Avoid using terms \"Excel\" and \"Google Sheets\" in the response.";     //No I18N
        protected static final String EXTRACT = "refrain from answering in sentence";      //No I18N
        protected static final String LIST = "The response should be in list. Keep the response short and crisp";   //No I18N
        protected static final String FORMULA_GENERATION = "You are a spreadsheet assistant for zoho sheet, capable of generating only spreadsheet formulas. Return \"invalid\" if the given query is unclear, invalid, or not about spreadsheet formulas. Replace the word spreadsheet in response with \"Zoho Sheet\".If the result is valid, format it in markdown.Avoid terms google and microsoft."; //No I18N
        protected static final String FORMULA_EXPLAINER = "Explain the given zoho sheet formula in markdown format. If the formula is incorrect or if the given question is not about zoho sheet formula, return \"Invalid\" only as result. The answer should never be longer than 200 words. Avoid terms google and microsoft.";   //No I18N
        protected static final String VBA_GENERATOR = "Generate spreadsheet vba macros code only as result.Do not provide any steps or explanation.macro code should be complete.Return only \"Invalid\" as result if the given topic is unclear, or invalid, or not about macros. If the result is valid, format it in markdown. Avoid terms google and microsoft and excel.";  //No I18N
        protected static final String VBA_EXPLAINER = "You will be given a VBA code to explain. Give a brief explanation about the code in less than 200 words. Do not give any result other than the code explanation. Replace the terms excel and microsoft with spreadsheet.Give result as \"Invalid\" if the given text is not a VBA code.";  //No I18N
        protected static final String GENERATE_TABLE = "You are a CSV data generator. Given a topic, generate realistic CSV data with clear headers and values. Use comma as the delimiter and double quotes as the text qualifier. Return only the raw CSV content—no formatting, no code blocks, and no explanations.\n" +    //No I18N
                "Respond with exactly \"Invalid\" (case-sensitive) only if the topic is meaningless or not suited for tabular data (e.g., general facts like “Who is Elon Musk?” or “When did World War II start?”).\n" +   //No I18N
                "Do not return \"Invalid\" for understandable tabular requests, even if informally worded (e.g., “Top 20 country population and flag” is valid).";   //No I18N
        protected static final String EXTRACT_FILE = "You are a data extraction specialist. Analyze the provided image or document and extract any structured data, tables, lists, or organized information. Convert the extracted data into CSV format with appropriate headers and values. Use comma as the delimiter and double quotes as the text qualifier. Return only the raw CSV content—no formatting, no code blocks, and no explanations.\n" +    //No I18N
                "If the file contains multiple tables or data sections, extract the most prominent or complete table. If no structured data is found, respond with exactly \"No Data\" (case-sensitive).\n" +   //No I18N
                "Supported formats: PDF documents, JPEG/JPG images, PNG images. Focus on extracting tabular data, lists, forms, invoices, receipts, or any structured information that can be organized into rows and columns.";   //No I18N
    }
    protected static class SupportedFileFormats {
        protected static final String PDF = "pdf";   //No I18N
        protected static final String JPEG = "jpeg"; //No I18N
        protected static final String JPG = "jpg";   //No I18N
        protected static final String PNG = "png";   //No I18N

        protected static final String[] SUPPORTED_FORMATS = {PDF, JPEG, JPG, PNG};

        public static boolean isSupported(String format) {
            if (format == null) return false;
            String lowerFormat = format.toLowerCase();
            for (String supportedFormat : SUPPORTED_FORMATS) {
                if (supportedFormat.equals(lowerFormat)) {
                    return true;
                }
            }
            return false;
        }
    }

    protected static class Temperature {
        protected static final double DETERMINISTIC = 0.3;
        protected static final double CREATIVE = 0.7;
    }
}

