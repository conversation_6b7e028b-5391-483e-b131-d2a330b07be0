package com.zoho.sheet.openai;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 */

public class PlatformAIRequestTask implements Callable<com.zoho.sheet.openai.PlatformAIRequestTask.TaskResponse> {
    private static final int TIMEOUT = Integer.parseInt(EnginePropertyUtil.getSheetPropertyValue("OPENAI_TIMEOUT"));   //NO I18N

    private final String urlString;
    private final JSONObjectWrapper reqJSON;
    private final Map<String, String> headerMap;
    private final String method;

    public PlatformAIRequestTask(String url, Map<String, String> headerMap, JSONObjectWrapper reqJSON, String method) {
        this.urlString = url;
        this.reqJSON = reqJSON;
        this.headerMap = headerMap;
        this.method = method;
    }

    @Override
    public TaskResponse call() throws Exception {
        return postRequest();
    }

    private TaskResponse postRequest() throws IOException {

        HttpURLConnection connection;
        URL url = new URL(urlString);
        URLConnection conn = url.openConnection();
        connection = (HttpURLConnection) conn;

        OutputStream outputStream = null;
        InputStream inputStream;

        TaskResponse taskResponse;
        try {

            connection.setRequestMethod(method);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setUseCaches(false);
            connection.setConnectTimeout(1000 * TIMEOUT);
            connection.setReadTimeout(1000 * TIMEOUT);
            connection.setRequestProperty("Content-Type", "application/json");        //No I18N
            connection.setRequestProperty("Accept", "application/json");                               //No I18N
            connection.setRequestProperty("Accept-charset", String.valueOf(StandardCharsets.UTF_8));   //No I18N

            for (Map.Entry<String, String> header : headerMap.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }

            if (!reqJSON.isEmpty()) {
                outputStream = connection.getOutputStream();
                outputStream.write(reqJSON.toString().getBytes(StandardCharsets.UTF_8));
            }

            int responseCode = connection.getResponseCode();

            if (responseCode >= 200 && responseCode < 300) {
                inputStream = connection.getInputStream();
            } else {
                inputStream = connection.getErrorStream();
            }
            String response = readInputStream(inputStream);
            inputStream.close();
            taskResponse = new TaskResponse(response, responseCode);
        } catch (SocketTimeoutException ex) {
            taskResponse = new TaskResponse("{}", HttpServletResponse.SC_REQUEST_TIMEOUT);
        } catch (Exception e) {
            taskResponse = new TaskResponse("{}", 520);
        } finally {
            if (Objects.nonNull(outputStream)) {
                outputStream.flush();
                outputStream.close();
            }
            connection.disconnect();
        }

        return taskResponse;
    }

    private String readInputStream(InputStream inputStream) throws IOException {
        StringBuilder builder = new StringBuilder();
        String line;
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        while ((line = reader.readLine()) != null) {
            builder.append(line);
        }

        return builder.toString();
    }

    public static class TaskResponse {
        private final String response;
        private final int responseCode;

        public TaskResponse(String response, int responseCode) {
            this.response = response;
            this.responseCode = responseCode;
        }

        public String getResponse() {
            return response;
        }

        public int getResponseCode() {
            return responseCode;
        }
    }
}

