package com.zoho.sheet.openai;

import com.adventnet.zoho.websheet.model.util.EngineConstants;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */

public class PlatformAIExecutorService {
    private static final ExecutorService EXECUTOR_SERVICE = Executors.newFixedThreadPool(EngineConstants.OPENAI_THREAD_POOL_SIZE);

    public Future<PlatformAIRequestTask.TaskResponse> addRequestTask(PlatformAIRequestTask task) {
        return EXECUTOR_SERVICE.submit(task);
    }

    public PlatformAIRequestTask.TaskResponse getMessage(Future<PlatformAIRequestTask.TaskResponse> message) throws ExecutionException, InterruptedException {
        return message.get();
    }
}
