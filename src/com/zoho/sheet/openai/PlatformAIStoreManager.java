package com.zoho.sheet.openai;

import com.zoho.sheet.openai.beans.PlatformAIDbBean;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */

public class PlatformAIStoreManager {
    private static final Logger LOGGER = Logger.getLogger(PlatformAIStoreManager.class.getName());
    private static final Map<String, PlatformAIStore> STORE_REFERENCE = new ConcurrentHashMap<>();

    public static PlatformAIStore getFormulaStore(String soid) throws OpenAiInternalException {
        //temp dummy object for AI formulas when token is not initialised
        PlatformAIStore dummyObj = new PlatformAIStore(null, null, null, 0);
        if(soid == null){
            return dummyObj;
        }
        PlatformAIStore store = getStore(soid);
        return store == null ? dummyObj : store;
    }

    public static PlatformAIStore getStore(String soid) throws OpenAiInternalException {
        if(soid == null){
            return null;
        }

        Map<String, PlatformAIStore> storeMap = getStoreMap();
        if (!hasStore(soid) && soid != null) {
            // token id not fetched from db yet
            PlatformAIDbBean dbBeanData = PlatformAIDBUtils.getTokenFromDB(soid, OpenAiConstants.SCOPES.OPEN_AI);
            if (Objects.nonNull(dbBeanData)) {
                // token fetched from db
                String tokenId = dbBeanData.getTokenId();
                String portalId = dbBeanData.getPortalId();
                long featureNo = dbBeanData.getFeatureNo();
                PlatformAIStore storeObj = new PlatformAIStore(portalId, tokenId, soid, featureNo);
                addToStoreMap(storeObj);
                return storeObj;
            } else {
                //token is not yet created
                return null;
            }
        }
        return storeMap.get(soid);
    }

    private static boolean hasStore(String soid) {
        return getStoreMap().containsKey(soid);
    }

    private static Map<String, PlatformAIStore> getStoreMap() {
        return STORE_REFERENCE;
    }

    public static void addToStoreMap(PlatformAIStore store) {
        Map<String, PlatformAIStore> storeMap = getStoreMap();
        storeMap.put(store.getServiceOrgId(), store);
    }

    public static void deleteFromStore(String soid) {
        Map<String, PlatformAIStore> storeMap = getStoreMap();
        storeMap.remove(soid);
    }

    public static void cleanStoreMap() {
        Map<String, PlatformAIStore> storeMap = getStoreMap();
        if (Objects.nonNull(storeMap)) {
            int size = storeMap.size();
            LOGGER.log(Level.INFO, "PlatformAI holder size before cleaning {0}", new Object[]{size});
            for (String key : storeMap.keySet()) {
                PlatformAIStore store = storeMap.get(key);
                if (store.isEligibleToClean()) {
                    storeMap.remove(key);
                }
                LOGGER.log(Level.INFO, "PlatformAI Store removed after timeout {0} ::: ", new Object[]{key});
            }
        }
    }
}

