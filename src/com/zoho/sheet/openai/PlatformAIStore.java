package com.zoho.sheet.openai;

import java.util.BitSet;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */

public class PlatformAIStore {

    private final String portalId;
    private final String tokenId;
    private final String serviceOrgId;
    private BitSet bitSet;
    private boolean isOAuthType;
    private long lastUsedTime = System.currentTimeMillis();

    public PlatformAIStore(String portalId, String tokenId, String serviceOrgId, long featureNo) throws OpenAiInternalException {
        this.portalId = portalId;
        this.tokenId = tokenId;
        this.serviceOrgId = serviceOrgId;
        this.isOAuthType = false;

        if(Objects.isNull(portalId)){
            return;
        }

        String binaryString = Long.toBinaryString(featureNo); // Convert to binary string

        OpenAiConstants.FEATURE_LIST[] featureList = OpenAiConstants.FEATURE_LIST.values();
        bitSet = new BitSet(featureList.length);
        int prevFeatures = binaryString.length();
        int currentFeatures = featureList.length;

        if (prevFeatures <= currentFeatures) {
            // a new feature is added, retain the old state and enable the new ones
            for (int i = 0; i < currentFeatures; i++) {
                if (i < prevFeatures) {
                    if (binaryString.charAt(i) == '1') {
                        bitSet.set(i);
                    }
                } else {
                    bitSet.set(i);
                }
            }
        }


        if (prevFeatures != currentFeatures) {
            // first time init after adding a feature or deleting a feature, write new val in DB
            long newFeatureNo = PlatformAIAuthUtils.bitSetToInteger(bitSet);
            PlatformAIDBUtils.updateFeaturesInDB(serviceOrgId, newFeatureNo);
        }
    }

    public boolean isEligibleToClean() {
        long currentTimeMillis = System.currentTimeMillis();
        long minutesDiff = TimeUnit.MILLISECONDS.toMinutes(currentTimeMillis - this.lastUsedTime);
        return minutesDiff >= 30;
    }

    public String getPortalId() {
        lastUsedTime = System.currentTimeMillis();
        return portalId;
    }

    public String getTokenId() {
        lastUsedTime = System.currentTimeMillis();
        return tokenId;
    }

    public String getServiceOrgId() {
        lastUsedTime = System.currentTimeMillis();
        return serviceOrgId;
    }

    public BitSet getFeatureBits() {
        lastUsedTime = System.currentTimeMillis();
        return this.bitSet;
    }

    public void setFeatureBits(BitSet bitset) {
        lastUsedTime = System.currentTimeMillis();
        this.bitSet = bitset;
    }

    public boolean isOAuthType() {
        return isOAuthType;
    }

    public void setOAuthType(boolean oAuthType) {
        isOAuthType = oAuthType;
    }
}
