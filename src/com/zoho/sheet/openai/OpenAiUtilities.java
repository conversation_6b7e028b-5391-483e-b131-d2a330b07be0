package com.zoho.sheet.openai;

import com.zoho.sheet.openai.PlatformAIFunctions.ConnectionResponse;

import java.util.List;
import java.util.function.Function;

/*
 * <AUTHOR>
 */

/**
 * Wrapper class for PlatformAIFunctions.java, used for INTERNAL API calls
 */
public class OpenAiUtilities {
    private static final OpenAiConstants.FEATURE_LIST FEATURE_NAME = OpenAiConstants.FEATURE_LIST.AI_FUNCTION;

    private static ConnectionResponse performGptFunction(PlatformAIStore store, Function<PlatformAIStore, ConnectionResponse> callback) {
        if (store == null) {
            return ConnectionResponse.getErrorResponse(PlatformAIFunctions.ResponseStatus.NO_TOKEN);
        }

        boolean isFeatureEnabled = PlatformAIAuthUtils.isTheFeatureEnabled(store.getFeatureBits(), FEATURE_NAME);
        if (!isFeatureEnabled) {
            return ConnectionResponse.getErrorResponse(PlatformAIFunctions.ResponseStatus.FEATURE_NOT_ENABLED);
        }

        store.setOAuthType(true);      // explicit setting to enable OAuth in request
        return callback.apply(store);
    }

    public static ConnectionResponse gptGeneralQuery(PlatformAIStore aiStore, String query) {
        return performGptFunction(aiStore, store -> PlatformAIFunctions.gptGeneralQuery(query, store, false));
    }

    public static ConnectionResponse gptTranslate(PlatformAIStore aiStore, String translateQuery, String toLanguage, String srcLanguage) {
        return performGptFunction(aiStore, store -> PlatformAIFunctions.gptTranslate(translateQuery, toLanguage, srcLanguage, store));
    }

    public static ConnectionResponse gptExtract(PlatformAIStore aiStore, String extractQuery, String entityQuery) {
        return performGptFunction(aiStore, store -> PlatformAIFunctions.gptExtract(extractQuery, entityQuery, store, false));
    }

    public static ConnectionResponse gptClassify(PlatformAIStore aiStore, String query, String categories) {
        return performGptFunction(aiStore, store -> PlatformAIFunctions.gptClassify(query, categories, store));
    }

    public static ConnectionResponse gptTag(PlatformAIStore aiStore, String query, String tags) {
        return performGptFunction(aiStore, store -> PlatformAIFunctions.gptTag(query, tags, store));
    }

    public static ConnectionResponse gptTable(PlatformAIStore aiStore, String query) {
        return performGptFunction(aiStore, store -> PlatformAIFunctions.gptTable(query, store, false));
    }

    public static ConnectionResponse gptList(PlatformAIStore aiStore, String query) {
        return performGptFunction(aiStore, store -> PlatformAIFunctions.gptList(query, store));
    }

    public static ConnectionResponse gptFill(PlatformAIStore aiStore, List<List<String>> sampleDataList, List<List<String>> fillDataList) {
        return performGptFunction(aiStore, store -> PlatformAIFunctions.gptFill(sampleDataList, fillDataList, store));
    }

    public static ConnectionResponse gptSentiment(PlatformAIStore aiStore, String query) {
        return performGptFunction(aiStore, store -> PlatformAIFunctions.gptSentimentAnalysis(query, store));
    }
}
