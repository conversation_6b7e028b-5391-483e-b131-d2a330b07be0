package com.zoho.sheet.openai.beans;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.action.ZSheetOpenAiAction;
import com.zoho.sheet.openai.PlatformAIAuthUtils;
import com.zoho.zfsng.constants.ZFSNGConstants;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

public class AiRequestBean {
    private final String resourceId;
    private final String zuid;
    private final String zSoid;
    private final String rId;
    private final String wdOrgId;
    private final int actionType;
    private final String apiKey;
    //    private final int featureId;
    private final boolean toggleState;
    private final String query;
    private final boolean isWorkdrive;
    private final JSONObjectWrapper responseObj;

    public AiRequestBean(HttpServletRequest request) {
        String rid = request.getParameter(ZSheetOpenAiAction.RID);
        String wdOrgId = request.getParameter(ZSheetOpenAiAction.WD_ORG);
        String zuid = IAMUtil.getCurrentUser().getZuid();
        String zsoid = request.getParameter(("zsoid"));

        this.rId = rid;
        this.wdOrgId = wdOrgId;
        this.zuid = zuid;
        this.resourceId = rid != null ? rid : wdOrgId;

        if(Objects.nonNull(zsoid)){
            this.zSoid = zsoid;
        }
        else{
            if (Objects.nonNull(rid)) {
                this.zSoid = PlatformAIAuthUtils.getSoidByZuid(zuid, rid);
            } else {
                this.zSoid = PlatformAIAuthUtils.getSoidByWDOrg(zuid, wdOrgId);
            }
        }
        String actionStr = request.getParameter(ZSheetOpenAiAction.ACTION_TYPE);
        this.actionType = actionStr != null ? Integer.parseInt(actionStr) : -1;

        String featureIdStr = request.getParameter(ZSheetOpenAiAction.FEATURE_ID);
//        this.featureId = featureIdStr != null ? Integer.parseInt(featureIdStr) : -1;

        String toggleStr = request.getParameter(ZSheetOpenAiAction.DISABLE);
        this.toggleState = Boolean.parseBoolean(toggleStr);

        this.apiKey = request.getParameter(ZSheetOpenAiAction.API_KEY);
        this.query = request.getParameter(ZSheetOpenAiAction.QUERY);
        this.isWorkdrive = (boolean) request.getAttribute(ZFSNGConstants.IS_TEAM_RESOURCE);
        this.responseObj = new JSONObjectWrapper();
    }


    public String getZuid() {
        return this.zuid;
    }

    public String getResourceId() {
        return this.resourceId;
    }

    public String getzSoid() {
        return this.zSoid;
    }

    public int getActionType() {
        return this.actionType;
    }

    public JSONObjectWrapper getResponseObj() {
        return this.responseObj;
    }

//    public int getFeatureId() {
//        return this.featureId;
//    }

    public boolean getToggleState() {
        return this.toggleState;
    }

    public String getApiKey() {
        return apiKey;
    }

    public String getQuery() {
        return query;
    }

    public boolean isWorkdrive() {
        return isWorkdrive;
    }

    public String getrId() {
        return rId;
    }

    public String getWdOrgId() {
        return wdOrgId;
    }
}
