//$Id$
package	com.zoho.sheet.authorization.grepper.spaces;

import com.adventnet.mfw.bean.BeanUtil;
import com.adventnet.persistence.Persistence;
import com.zoho.sheet.authorization.grasper.ContainerEntityGrasper.ExceptionConstants;
import com.zoho.sheet.authorization.grepper.impl.ContainerEntityGrepperAuthImpl;
import com.zoho.sheet.authorization.grepper.spaces.bean.CredentialBean;
import com.zoho.sheet.authorization.grepper.tables.TableProcessor;
import com.zoho.sheet.authorization.grepper.tables.iterator.TableIterator;
import com.zoho.sheet.util.SheetPersistenceUtils;
/**
 * 
 * Class will switch User spaces.
 *
 */
public	class	SpaceSwitcher	{
	
	private ContainerEntityGrepperAuthImpl		grepper;
	private TableIterator[] 				tableIterators;
	private	String[]						spaceNames;
	private	int								spaceIndex;
	private	int								spaceLength;
	
	public	SpaceSwitcher(ContainerEntityGrepperAuthImpl	grepper)	{
		this.grepper			=	grepper;
	}
	
	public	void	setSpaces(String... spaceNames)	{
		this.spaceNames		=	spaceNames;
		this.spaceLength	=	this.spaceNames.length;
	}
	
	public	void	setTableIterators(TableIterator...	tableIterator)	{
		this.tableIterators	=	tableIterator;
	}

    public	Persistence	getPersistence(String	spaceName) throws Exception	{
    	return	SheetPersistenceUtils.getPersistence(spaceName);//No I18N
    }
    
    public void process(CredentialBean bean)	throws	Exception	{
    		String		spaceName				=	spaceNames[spaceIndex];
    		TableIterator tableIterator			=	tableIterators[spaceIndex];
			Persistence	persistence				=	getPersistence(spaceName);
			boolean		executed				=	false;
			
			while(tableIterator.hasNext())	{
				TableProcessor	tableProcessor	=	tableIterator.next();
				executed						=	tableProcessor.process(bean, persistence, grepper);
				if(executed)	{
					break;
				}
			}
			
			if(!executed)	{
				if(hasNextSpace()) {
					switchSpace(bean);
				} else {
					grepper.publishException(ExceptionConstants.YOU_ARE_NOT_AUTHORIZED);
				}
			}
    }
    
    public	boolean	hasNextSpace()	{
    	return spaceIndex < spaceLength-1;
    }
    
    public	void	switchSpace(CredentialBean bean) throws Exception	{
		spaceIndex++;
		this.process(bean);
    }
}
