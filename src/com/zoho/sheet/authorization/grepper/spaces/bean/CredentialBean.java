//$Id$
package	com.zoho.sheet.authorization.grepper.spaces.bean;


import com.zoho.sheet.util.ResourceMetaInfo;

public	class	CredentialBean	{
	//private	String	docId;
	private ResourceMetaInfo resMetaInfo;
	private	String	loginName;
	private	boolean	fetchContainerEntity;
	private	boolean fetchUserProfileEntity;
	public CredentialBean(ResourceMetaInfo resInfo,	String	loginName,	boolean fetchBookEntity,	boolean	fetchUserProfileEntity) {
		this.resMetaInfo		=	resInfo;
		this.loginName	=	loginName;
		this.fetchContainerEntity	=	fetchBookEntity;
		this.fetchUserProfileEntity	=	fetchUserProfileEntity;
	}
	
	
	
	public	ResourceMetaInfo getResourceMetaInfo()	{
		return	this.resMetaInfo;
	}
	
	public	String	getLoginName()	{
		return	this.loginName;
	}
	
	public	boolean	isContainerEntityNeeded()	{
		return	this.fetchContainerEntity;
	}

	public	boolean	isUserProfileEntityNeeded()	{
		return	this.fetchUserProfileEntity;
	}
	
	public	void	updateContainerEntity(boolean	fetchContainerEntity)	{
		this.fetchContainerEntity	=	fetchContainerEntity;
	}
	
	public	void	updateUserProfileEntity(boolean	fetchUserProfileEntity)	{
		this.fetchUserProfileEntity	=	fetchUserProfileEntity;
	}
	
}