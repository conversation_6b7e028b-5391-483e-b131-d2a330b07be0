//$Id$
package	com.zoho.sheet.authorization;

import java.util.HashMap;
import java.util.Locale;


public class DocIdentityBroadCaster {

	private	static	DocIdentityBroadCaster	broadCaster	=	null;
	private	DocIdentityBroadCaster()	{}
	public	static	DocIdentityBroadCaster	getInstance()	{
			if(broadCaster	==	null)	{
				broadCaster	=	new	DocIdentityBroadCaster();
			}
			return	broadCaster;
	}
	
	private static DocIdentityAssociator associator = new DocIdentityAssociator();
	
	public void setIdentity(DocIdentity credential) {
		associator.set(credential);
	}

	public static DocIdentity getIdentity() {
		return associator.get();
	}

	public void resetIdentity() {
		associator.reset();
	}

	public	void	setLocale(Locale locale) {
		associator.setLocale(locale);
	}

	public static Locale	getLocale() {
		return associator.getLocale();
	}

	public	void	resetLocale() {
		associator.resetLocale();
	}

	public	void	setResourceIdentity(ResourceIdentity resourceIdentity) {
		associator.setResourceIdentity(resourceIdentity);
	}

	public static ResourceIdentity	getResourceIdentity() {
		return associator.getResourceIdentity();
	}

	public	void	resetResourceIdentity() {
		associator.resetResourceIdentity();
	}
	
	@SuppressWarnings("rawtypes")													//No I18N
	private static class DocIdentityAssociator extends ThreadLocal{
    	private static final String IDENTITY_HOLDER = "IDENTITY_HOLDER";				//No I18N
    	private static final String I18N_HOLDER = "I18N_HOLDER";				//No I18N
    	private static final String RESOURCE_IDENTIFIER = "RESOURCE_IDENTIFIER";				//No I18N

		protected synchronized Object initialValue() {
			return new HashMap();
    	}

		@SuppressWarnings("unchecked")												//No I18N
		public void set(DocIdentity credential) {
			HashMap map = (HashMap)super.get();
			map.put(DocIdentityAssociator.IDENTITY_HOLDER, credential);
		}

		public DocIdentity get() {
			HashMap map = (HashMap)super.get();
			DocIdentity credential = (DocIdentity)map.get(DocIdentityAssociator.IDENTITY_HOLDER);
			return credential;
		}

		public void reset() {
			HashMap map = (HashMap)super.get();
			map.clear();
		}


		@SuppressWarnings("unchecked")												//No I18N
		public void setLocale(Locale locale) {
			HashMap map = (HashMap)super.get();
			map.put(DocIdentityAssociator.I18N_HOLDER, locale);
		}

		public Locale getLocale() {
			HashMap map = (HashMap)super.get();
			Locale locale = (Locale)map.get(DocIdentityAssociator.I18N_HOLDER);
			return locale;
		}

		public void resetLocale() {
			HashMap map = (HashMap)super.get();
			if(map.containsKey(DocIdentityAssociator.I18N_HOLDER))	{
				map.remove(DocIdentityAssociator.I18N_HOLDER);
			}
		}
		
		@SuppressWarnings("unchecked")												//No I18N
		public void setResourceIdentity(ResourceIdentity resourceIdentity) {
			HashMap map = (HashMap)super.get();
			map.put(DocIdentityAssociator.RESOURCE_IDENTIFIER, resourceIdentity);
		}

		public ResourceIdentity getResourceIdentity() {
			HashMap map = (HashMap)super.get();
			ResourceIdentity resourceIdentity = (ResourceIdentity)map.get(DocIdentityAssociator.RESOURCE_IDENTIFIER);
			return resourceIdentity;
		}

		public void resetResourceIdentity() {
			HashMap map = (HashMap)super.get();
			if(map.containsKey(DocIdentityAssociator.RESOURCE_IDENTIFIER))	{
				map.remove(DocIdentityAssociator.RESOURCE_IDENTIFIER);
			}
		}
	}
}
