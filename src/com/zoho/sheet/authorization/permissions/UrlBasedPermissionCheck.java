package com.zoho.sheet.authorization.permissions;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.OrgPolicy;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.functions.B;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.util.URLObject;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public abstract class UrlBasedPermissionCheck
{
	private static final Logger LOGGER = Logger.getLogger(UrlBasedPermissionCheck.class.getName());
	private final HttpServletRequest request;
	private final WorkbookContainer container;
	private final String zfsngVersionNo;
	private final UserProfile userProfile;
	// PRIVILEGES WILL BE NULL FOR FOLLOWING CASES
	// 1. CONTAINER IS NULL FOR ACTIONS LIKE IMPORT CASES.
	// 2. CAPABILITIES WILL NOT AVAILABLE IN REQUEST FOR RANGE/SHEET PUBLISH CASES.
	private final JSONObjectWrapper privileges;
	private final String proxyUrl;

	UrlBasedPermissionCheck(HttpServletRequest request, WorkbookContainer container, String zfsngVersionNo, UserProfile userProfile, String proxyUrl) throws Exception
	{
		this.request = request;
		this.container = container;
		this.zfsngVersionNo = zfsngVersionNo;
		this.userProfile = userProfile;
		this.proxyUrl = proxyUrl;
		this.privileges = container != null ? this.getCapabilities(request) : null;
	}

	public static UrlBasedPermissionCheck getInstance(HttpServletRequest request, WorkbookContainer container, String zfsngVersionNo, UserProfile userProfile, String proxyUrl) throws Exception
	{
		URLObject.UrlType urlType = URLObject.getUrlType(proxyUrl);
//		if(urlType == URLObject.UrlType.ACTION)
//		{
//			return new ActionBasedPermissionCheck(request,container, userProfile, proxyUrl);
//		}
		if(urlType == URLObject.UrlType.COPY_DOC)
		{
			return new CopyDocumentPermissionCheck(request, container, zfsngVersionNo, userProfile, proxyUrl);
		}
		else if(urlType == URLObject.UrlType.EXPORT_DOC)
		{
			return new ExportDocumentPermissionCheck(request, container, zfsngVersionNo, userProfile, proxyUrl);
		}
		else if(urlType == URLObject.UrlType.FILE_CONVERSION)
		{
			return new FileConversionPermissionCheck(request, container, zfsngVersionNo, userProfile, proxyUrl);
		}
		else if(urlType == URLObject.UrlType.SEND_DOC)
		{
			return new SendMailDocPermissionCheck(request, container, zfsngVersionNo, userProfile, proxyUrl);
		}
		else if(urlType == URLObject.UrlType.PRINT_DOC)
		{
			return new PrintPermissionCheck(request, container, userProfile, proxyUrl);
		}
		else if(urlType == URLObject.UrlType.FETCH)
		{
			return new FetchDataPermissionCheck(request, container, zfsngVersionNo, userProfile, proxyUrl);
		}
		else if(urlType == URLObject.UrlType.IMPORT)
		{
			return new ImportPermissionCheck(request, container, userProfile, proxyUrl);
		}
		else if(urlType == URLObject.UrlType.THUMBNAIL)
		{
			return new ThumbnailPermissionCheck(request, container, userProfile, proxyUrl);
		}
		return null;
	}

	protected HttpServletRequest getRequest()
	{
		return request;
	}

	public WorkbookContainer getWorkbookContainer()
	{
		return container;
	}

	public String getZfsngVersionNo()
	{
		return zfsngVersionNo;
	}

	public Workbook getWorkbook() throws Exception
	{
		LOGGER.log(Level.INFO, "Fetching workbook for zfsngVersionNo: {0}", zfsngVersionNo);
		if(zfsngVersionNo != null)
		{
			LOGGER.log(Level.INFO, "Adding Workbook to cached books with zfsngVersionNo : {0}", zfsngVersionNo);
			Workbook workbook = container.getWorkbook(zfsngVersionNo);
			ImageBook imageBook = container.getImageBook(zfsngVersionNo);
			Map<Book.BookType, Book> cachedBooks = new HashMap<>();
			cachedBooks.put(Book.BookType.WORKBOOK, workbook);
			cachedBooks.put(Book.BookType.IMAGEBOOK, imageBook);
			container.setBooksCache(cachedBooks, zfsngVersionNo);
		}
		return this.container.getWorkbook(zfsngVersionNo);
	}

	public UserProfile getUserProfile()
	{
		return userProfile;
	}

	public boolean canShare()
	{
		return privileges.getBoolean("canShare");
	}

	public boolean canDownload()
	{
		return privileges.getBoolean("canDownload");
	}

	public boolean isAdmin()
	{
		return privileges.getBoolean("isAdmin");
	}

	public boolean canEdit()
	{
		return privileges.getBoolean("canEdit");
	}

	public boolean isTemplate() throws Exception
	{
		return container.isTemplate();
	}
	public boolean canCreateFiles(JSONObjectWrapper folderCapabilities)
	{
		return folderCapabilities.getBoolean("canCreateFiles");
	}

	public boolean isHiddenDataLocked(Workbook workbook, String zuid) throws Exception
	{
		return RangeUtil.isHiddenDataLocked(workbook, !canEdit(), zuid);
	}

	public boolean isHiddenDataLocked(Sheet sheet, String zuid)
	{
		return RangeUtil.isHiddenDataLocked(sheet, new DataRange(sheet.getAssociatedName(), 0, 0, Utility.MAXNUMOFROWS - 1, Utility.MAXNUMOFCOLS - 1), !canEdit(), zuid);
	}

	public boolean isHiddenDataLocked(Sheet sheet, DataRange dataRange, String zuid)
	{
		return RangeUtil.isHiddenDataLocked(sheet, dataRange, !canEdit(), zuid);
	}

	public UserProfile.AccessType getAccessType()
	{
		return userProfile.getAccessType();
	}

	public boolean isConfirmedUser()
	{
		return IAMUtil.getCurrentUser().isConfirmed();
	}

	private JSONObjectWrapper getCapabilities(HttpServletRequest request) throws Exception
	{
		WorkbookContainer container = getWorkbookContainer();
		if(URLObject.isEndsWithCopy(proxyUrl))
		{
			String rid = request.getParameter(JSONConstants.RID);
			String copyByZuid = request.getParameter("copyby");
			return new JSONObjectWrapper(ZohoFS.getCapabalitiy(copyByZuid, rid));
		}
		else if(URLObject.isEndsWithConversion(proxyUrl))
		{
			// CAPABILITIES WILL NOT AVAILABLE IN IMPORT CASES
			if(request.getParameter("action").equalsIgnoreCase("import"))
			{
				return null;
			}
			String resourceId = request.getParameter("resourceid"); // No I18N
			String downloadBy = request.getParameter(JSONConstants.ZUID);
			return new JSONObjectWrapper(ZohoFS.getCapabalitiy(downloadBy, resourceId));
		}
		if(userProfile.isPublishedAccessType())
		{
			JSONObjectWrapper capabilities = new JSONObjectWrapper();
			capabilities.put("isAdmin", false);
			capabilities.put("canDownload", false);
			capabilities.put("canEdit", false);
			capabilities.put("canShare", false);
			return capabilities;
		}
		String capabilities = (String) this.getRequest().getAttribute(ZFSNGConstants.CAPABILITIES);
		return capabilities != null ? new JSONObjectWrapper(capabilities) : (IAMUtil.getCurrentUser() != null ? new JSONObjectWrapper(ZohoFS.getCapabalitiy(IAMUtil.getCurrentUser().getZuid(), container.getResourceId())) : null);
	}

	protected JSONObjectWrapper getPrivileges()
	{
		return this.privileges;
	}

	protected boolean isExportAllowedOnOrg()
	{
		return IAMUtil.isCurrentUserAllowed(OrgPolicy.Policy.EXPORT_DOCUMENT.getName());
	}

	protected boolean isAllowExportOnPublic() throws Exception
	{
		WorkbookContainer container = this.getWorkbookContainer();
		JSONObjectWrapper additionalInfo = DocumentUtils.getPublicResourceMeta(container.getResourceId(), container.getDocsSpaceId());
		return additionalInfo.getBoolean(Integer.toString(CommandConstants.ALLOW_EXPORT));
	}

	protected String getProxyUrl()
	{
		return proxyUrl;
	}

	protected String getZuid() {
		return this.getUserProfile().getZUserId();
	}

	protected abstract void checkPermissionForAuth() throws Exception;

	protected abstract void checkPermissionForPublic() throws Exception;

	protected abstract void checkPermissionForRangePublic() throws Exception;
}
