package com.zoho.sheet.authorization.permissions;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.PublishRangeUtils;

import javax.servlet.http.HttpServletRequest;

public class PrintPermissionCheck extends UrlBasedPermissionCheck {

    private final String rangeId;
    private final int action;
    private final String printType;
    private final JSONArrayWrapper sheets;
    PrintPermissionCheck(HttpServletRequest request, WorkbookContainer container, UserProfile userProfile, String proxyUrl) throws Exception
    {
        super(request, container, null, userProfile, proxyUrl);
        this.rangeId = request.getAttribute(JSONConstants.RANGE_ID) != null ? (String)request.getAttribute(JSONConstants.RANGE_ID) : null;
        this.action = request.getParameter("action") != null ? Integer.parseInt(request.getParameter("action")) : -1;
        this.printType = request.getParameter("printType") != null ? request.getParameter("printType") : null;
        this.sheets = request.getParameter(JSONConstants.SHEETLIST) != null ? JSONArrayWrapper.fromString(request.getParameter(JSONConstants.SHEETLIST)) : null;
    }

    @Override
    protected void checkPermissionForPublic() throws Exception
    {
        if(!isAllowExportOnPublic())
        {
            throw new ProhibitedActionException(ErrorCode.ERROR_ACTION_RESTRICT);
        }
    }

    @Override
    protected void checkPermissionForRangePublic() throws Exception
    {
        JSONObjectWrapper rangeDetails = PublishRangeUtils.getRangeDetailsFromID(getWorkbookContainer(), rangeId);
        JSONObjectWrapper additionalInfo = !(rangeDetails.optString(Constants.ADDITIONAL_INFO).equals("")) ? new JSONObjectWrapper(rangeDetails.getString(Constants.ADDITIONAL_INFO)) : new JSONObjectWrapper();
        if(!additionalInfo.optBoolean("ALLOWEXPORT"))
        {
            throw new ProhibitedActionException(ErrorCode.ERROR_ACTION_RESTRICT);
        }
        if(!isAllowedOnPublished(rangeDetails))
        {
            throw new ProhibitedActionException(ErrorCode.ERROR_ACTION_RESTRICT);
        }
    }


    @Override
    protected void checkPermissionForAuth() throws Exception
    {
        if(!this.canDownload())
        {
            throw new ProhibitedActionException(ErrorCode.ERROR_ACTION_RESTRICT);
        }
    }


    private boolean isAllowedOnPublished(JSONObjectWrapper rangeDetails) throws Exception
    {
        if(action == ActionConstants.PDF_PRINT_PREVIEW )
        {
            if(printType != null && printType.equals("WORKBOOK")) // here in Sheet publish case, we can't print the workbook
            {
                return false;
            }
            JSONObjectWrapper additionalInfo = new JSONObjectWrapper(rangeDetails.getString(Constants.ADDITIONAL_INFO));
            boolean isSheetPublish = "true".equals(additionalInfo.getString("ISSHEETPUBLISH"));
            if(!isSheetPublish) // we need to check whether this is sheet publish or range publish. We will not allow range publish case for print action. So we added this check.
            {
                return false;
            }
            for(int i=0; i< sheets.length(); i++) // we need to check whether given print sheet name and published sheet name is equal or not
            {
                String sheetId = sheets.getString(i);
                if(!rangeDetails.getString(JSONConstants.ASSOCIATED_SHEET_NAME).equals(sheetId))
                {
                    return false;
                }
            }
        }
        return true;
    }

}
