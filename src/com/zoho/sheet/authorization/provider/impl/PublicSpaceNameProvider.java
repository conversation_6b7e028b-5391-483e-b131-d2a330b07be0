//$Id$
package	com.zoho.sheet.authorization.provider.impl;

import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.authorization.provider.SpaceNameProvider;

public	class	PublicSpaceNameProvider	implements	SpaceNameProvider	{

	@Override
	public String getDocOwner() {
		return Constants.REMOTE_USER;
	}

	public String getDocOwnerId() {
		return null;
	}
	/*SAS18_IMPL AAA comment ~Mani
	 * public String getDocOwnerId() {
		try{
			return DocumentUtils.getAccountId(getDocOwner());
		} catch (Exception e){
			return null;
		}
	}*/
	
}