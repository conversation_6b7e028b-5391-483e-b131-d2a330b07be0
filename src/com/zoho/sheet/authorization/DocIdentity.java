//$Id$

package com.zoho.sheet.authorization;

import com.adventnet.zoho.websheet.model.TabInfo.TabType;
import com.adventnet.zoho.websheet.model.WorkbookContainer;


/*
 * 
 */
public class DocIdentity {
                //We can add other properties as well here, Even User Info, view as well.
                private String  containerIdentity;
                private String  userIdentity;
                private	String	tabIdentity;
                private String  wmsIdentity;
                private String  bookIdentity;		//Will be null except the version view
                private	String	accessIdentity;		//[ Transaction Type]
                
                private	WorkbookContainer	container;
                private	TabType	tabType;		//[ types of tab like cached,non cached]
                
                @SuppressWarnings("unused")					//No I18N		
                private DocIdentity() {}

                public DocIdentity(String documentId, String userId, String tabKey, String rsid, String versionNo, String accessIdentity, TabType tabType)
                {
                    this.containerIdentity  = documentId;
                    this.userIdentity       = userId;
                    this.tabIdentity        = tabKey;
                    this.bookIdentity       = versionNo;
                    this.accessIdentity     = accessIdentity;
                    this.tabType            = tabType;
                    this.wmsIdentity        = rsid;
                }
                
                public DocIdentity(String documentId, String userId, String tabKey, String rsid, String versionNo, String accessIdentity, WorkbookContainer container, TabType tabType)
                {
                    this.containerIdentity  = documentId;
                    this.userIdentity       = userId;
                    this.tabIdentity        = tabKey;
                    this.bookIdentity       = versionNo;
                    this.accessIdentity     = accessIdentity;
                    this.container          = container;
                    this.tabType            = tabType;
                    this.wmsIdentity        = rsid;
                }

                public String getContainerIdentity() {
                        return containerIdentity;
                }

                public  String  getUserIdentity() {
                        return  userIdentity;
                }
                
                public	String	getTabIdentity(){
                		return	tabIdentity;
                }
                
                public String getWmsIdentity() {
                    return wmsIdentity;
                }
                
                public	String	getWorkBookIdentity(){
            		return	bookIdentity;
                }
                
                public	String	getAccessIdentity()	{
                	return	accessIdentity;
                }
                
                public 	WorkbookContainer	getStateLessContainer()	{
                	return	this.container;
                }
                
                //Will come for Non Hashed Url's.
                public	boolean	isStateLessAccess()	{
                	return	this.container	!=	null;
                }
                
                public	TabType	getTabType(){
            		return	this.tabType ;
                }
                
                @Override
                public	String	toString(){
                	return	"  containerIdentity  ::  "+ this.containerIdentity + "  userIdentity :: "+ this.userIdentity  + " tabIdentity  ::  "+tabIdentity + " bookIdentity::  "						// No I18N
                				+this.bookIdentity + "  accessIdentity ::   "+ this.accessIdentity  + "  isStateLessAccess :: " +  (this.container	!=	null)    + " tabType :: "+ this.tabType;             // No I18N  
                	}
}
