/*  $Id$ */
package com.zoho.sheet.monitor;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.logging.Logger;
import com.adventnet.dfs.DFSClient;
import com.adventnet.dfs.DFSClientPool;
import com.adventnet.iam.IAMProxy;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.connection.ConnectionUtils;

import com.zoho.sheet.util.StrutsRequestHandler;
public class DFSMonitor extends StrutsRequestHandler {

	public  static Logger logger = Logger.getLogger(DFSMonitor.class.getName());
	
	public String execute() throws Exception{
		DFSClient dfsClient =   null;
        String ownerZUID    = 	request.getParameter("zuid");
        String blockId 		=   EnginePropertyUtil.getSheetPropertyValue("DFS_MONITORING_BLOCKID"); //No i18n
    	String filePath 	=	EnginePropertyUtil.getSheetPropertyValue("DFS_FILE_PATH"); //No i18n
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        if( ownerZUID == null ){
        	ownerZUID = EnginePropertyUtil.getSheetPropertyValue("DFS_MONITORING_ZUID");//No i18n
        }
        if( ownerZUID == null ){
        	String userName =   request.getUserPrincipal().getName();
        	ownerZUID       =   String.valueOf((IAMProxy.getInstance().getUserAPI().getUser(userName)).getZUID());
        }
        InputStream in = null;
        try {
    	    dfsClient = DFSClientPool.getDFSClientForWrite(ownerZUID, "ZohoSheet", null);//No i18n
    	    if( blockId != null && filePath != null){
    	    	logger.info("[DFS][MONITORING] blockId is null so gettng new block id  "+dfsClient);
    	    	in = dfsClient.read(filePath, blockId);
        	    byte[] buffer  = new byte[1024];
        	    int length;
        	    while ((length = in.read(buffer)) != -1) {
        	        result.write(buffer, 0, length);
        	    }
        	    logger.info("File Conent from block is:: "+ result.toString("UTF-8"));
    	    }else{
    	    	logger.info("[DFS][MONITORING] either blockId or filePath is null. [blockId ]:: "+blockId+" [filePath ]::"+filePath);
    	    }
    	    return null;
        }
        catch(Exception e){
        	logger.info("Exception while writing to DFS file "+e);
        	ConnectionUtils.printLog(e);
        	return null;
        }
        finally
    	{
        	if(in != null){
    	    	in.close();
    	    }
    	    DFSClientPool.returnDFSClient(dfsClient);
    	    
    	    
    	}
     }
}
