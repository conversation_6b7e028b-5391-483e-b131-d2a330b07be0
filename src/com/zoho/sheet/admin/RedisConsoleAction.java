//// $Id$
package com.zoho.sheet.admin;

// import com.zoho.sheet.util.StrutsRequestHandler;
// import com.adventnet.zoho.websheet.model.redis.RedisHelper;

// public class RedisConsoleAction extends StrutsRequestHandler {
	
// 	public String execute () throws Exception {
            
//                 boolean isSheetGuest = request.isUserInRole("SheetGuest");
//                 boolean isSheetAdmin = request.isUserInRole("SheetAdmin");
//                 if(!isSheetGuest && !isSheetAdmin){
//                     return null;
//                 }
                
// 		String key = request.getParameter("key");
// 		String result = "key: '"+key+"' doesnot exist";//No I18N
// 		if(key != null) {
// 			try
// 			{
// 				String type = RedisHelper.type(key);
// 				switch(type){
// 				case "string":
// 					result = RedisHelper.get(key,-1);
// 					break;
// 				case "hash":
// 					result = RedisHelper.hgetAll(key).toString();
// 					break;
// 				case "set":
// 					result = RedisHelper.smembers(key).toString();
// 					break;
// 				case "zset" :
// 					result = RedisHelper.zrangeByScore(key, 0, -1).toString();
// 					break;
// 				case "list":
// 					result = RedisHelper.lrange(key, 0, -1).toString();
// 					//TODO: Need to implement
// 					break;
// 			}
// 			}
// 			catch(Exception e)
// 			{
// 				result = e.toString();
// 			}
			
// 		} else  {
// 			result ="Empty key";//No I18N
// 		}
		
// 		response.getWriter().print(result);//NO OUTPUTENCODING
		
// 		return null;
//
//		String type = RedisHelper.type(key);
//		
//		System.out.println("TYPE :::::"+type+key);
//		if(type != null){
//			switch(type){
//			case "string":
//				result = RedisHelper.get(key,-1);
//				break;
//			case "hash":
//				result = RedisHelper.hgetAll(key).toString();
//				break;
//			case "set":
//				result = RedisHelper.smembers(key).toString();
//				break;
//			case "zset" :
//				result = RedisHelper.zrangeByScore(key, 0, 1000).toString();
//				break;
//			case "list":
//				break;
//			case "none":
//				result = "key: '"+key+"' doesnot exist";
//				break;
//			default:
//				result ="Invalid Key";
//				break;
//			}	
//		}
//		System.out.println("TYPE :::::"+result);
//		res.getWriter().print(result);//NO OUTPUTENCODING
//		
//		return null;

// 	}

// }
