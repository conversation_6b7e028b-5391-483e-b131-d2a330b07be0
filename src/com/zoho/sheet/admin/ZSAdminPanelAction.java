/* $Id$ */
package com.zoho.sheet.admin;

import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import com.adventnet.zoho.websheet.model.field.util.MergeJobUtils;
import com.adventnet.zoho.websheet.model.util.EngineConstants;

import com.adventnet.zoho.websheet.model.util.ZSCreditPointsUtil;
import com.zoho.officeplatform.lib.sku.model.CreditPoints;
import com.zoho.officeplatform.lib.sku.services.CreditPointsUtil;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
import com.zoho.sheet.api.dataapi.handler.UploadAPIHandler;
import com.zoho.sheet.api.dataapi.util.DataAPIHandlerUtil;
import com.zoho.sheet.exception.APIErrorHandler;
import com.zoho.sheet.openai.OpenAiConstants;
import com.zoho.sheet.openai.PlatformAIAuthUtils;
import com.zoho.sheet.openai.PlatformAIConsole;
import com.zoho.sheet.openai.PlatformAIStore;
import com.zoho.sheet.util.I18NUtil;
import com.zoho.sheet.util.WorkDriveInfoUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.servlet.RequestDispatcher;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */

public class ZSAdminPanelAction extends HttpServlet {
    public static final Logger LOGGER = Logger.getLogger(ZSAdminPanelAction.class.getName());

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String method = request.getParameter("method");

        if(EngineConstants.IS_NIC_GRID) {
            if(method!= null) {
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.INVALID_URL, null, null));
            } else {
                request.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(request, response);//No I18N
            }
            return;
        }

        if(method!= null)         //This will be an API request to get feature specific details
        {
            switch(method) {
                case "admin.getdetails":
                    getAdminDetails(request, response);
                    break;
                case "admin.featurestatus.update":
                    ZSAdminPanelUtils.updateFeatureStatus(request, response);
                    break;
                default:
                    break;
            }
            return;
        }

        //Following will be the request to get admin dashboard page with details
        request.setAttribute("USER_LOCALE", I18NUtil.getLocaleAsString(request));   //Note:User locale is sent as it will be used to get localeMsg in client

        String reqPath = request.getRequestURI();
        String[] reqPathArr = reqPath.split("/");
        String zsoid = reqPathArr.length == 5 ? reqPathArr[4] : null;

        User user = IAMUtil.getCurrentUser();
        String zuid = user.getZuid();

        if (zsoid == null || WorkDriveInfoUtils.isUserAdmin(zuid, zsoid)) {
            String displayName = user.getDisplayName();
            String userEmail = user.getPrimaryEmail();
            JSONArray adminDetails = WorkDriveInfoUtils.getUserAdminOrgs(zuid).getJsonArray();
            if (!adminDetails.isEmpty())    //Case - User is an admin in any org
            {
                response.setContentType("text/html;charset=UTF-8");
                PrintWriter out = response.getWriter();
                out.println("<script type = \"text/javascript\">");
                out.println("var adminOrgDetails = " + IAMEncoder.encodeJavaScript(adminDetails));     //No I18N
                out.println("var contactsUrl =" + "\"" + IAMProxy.getContactsServerURL(true) + "\"");  //No I18N
                out.println("var zuid=" + zuid);   //No I18N
                out.println("var displayName=\"" + IAMEncoder.encodeJavaScript(displayName) + "\""); //No I18N
                out.println("var userEmail=\"" + IAMEncoder.encodeJavaScript(userEmail) + "\""); //No I18N
                out.println("var accountUrl =\"" + IAMProxy.getIAMServerURL() + "\"");  //No I18N
                out.println("</script>");

                RequestDispatcher rd = request.getRequestDispatcher("/sheet/appsheet/adminpanel/pages/AdminPanel.jsp");   //No I18N
                rd.include(request, response);
                return;
            }
        }

        //Case - User is not an admin *in any org/in the zsoid org*
        request.getRequestDispatcher("/sheet/appsheet/adminpanel/pages/AdminError.jsp").forward(request, response);   //No I18N
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
    {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            JSONObject jsonObject = null;
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, jsonObject, jsonObject);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
    {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            JSONObject jsonObject = null;
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, jsonObject, jsonObject);
        }
    }

    public void getAdminDetails(HttpServletRequest request, HttpServletResponse response) throws Exception {
        if(!UploadAPIHandler.isAuthRequest(request))
        {
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_ERR, null, null));
            return;
        }

        if(!ZSAdminPanelUtils.isUserAdmin(request))
        {
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_OPERATION_ERR, null, null));
            return;
        }

        String featureName = request.getParameter("feature_name");
        String zsoid = request.getParameter("zsoid");
        JSONObject responseObject = new JSONObject();
        LOGGER.log(Level.INFO, "getAdminDetails :: zsoid : {0} :: featureName : {1}", new Object[]{zsoid, featureName});

        try {
            CreditPoints credits = CreditPointsUtil.getCredits(zsoid);
            if(credits!=null) {         //New User-Directly accessing admin dashboard without accessing merge template
                JSONObjectWrapper jObj = DataAPIHandlerUtil.getUserLicenseAndCreditDetails(zsoid);
                responseObject.put("license_details", jObj.optJSONObject("license_details").getJsonObject());
                responseObject.put("credit_details", jObj.optJSONObject("credit_details").getJsonObject());
            }
            responseObject.put("feature_status", ZSAdminPanelUtils.getFeatureStatusFromDB(zsoid, null));
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "Exception", e);
        }

        org.json.JSONObject creditsUsageJSON = ZSCreditPointsUtil.getMergeJobsCreditsUsage(zsoid);
        switch(featureName)
        {
            case "mergetemplate":
                try {

                    JSONObject filters;
                    if(request.getParameter("filter") == null) {
                        filters = new JSONObject();
                    } else {
                        filters = new JSONObject(request.getParameter("filter"));
                    }
                    
                    if(responseObject.has("license_details") && !filters.has("start_time") && !filters.has("end_time")) {      //For initial call when only the logs between the subscription start and end time is required
                        filters.put("start_time", responseObject.getJSONObject("license_details").getLong("subscription_start_time_in_ms"));
                        filters.put("end_time", responseObject.getJSONObject("license_details").getLong("subscription_end_time_in_ms"));
                    }
                    responseObject.put("merge_jobs", MergeJobUtils.getMergeJobs(zsoid, null, filters, false, null).getJSONArray("merge_jobs"));
                    JSONArray mergeJobsArr = responseObject.getJSONArray("merge_jobs");

                    for(int i = 0; i < mergeJobsArr.length(); i++)
                    {
                        JSONObject jobObj = mergeJobsArr.getJSONObject(i);
                        String jobId = jobObj.getString("job_id");
                        int creditsUsed = creditsUsageJSON.optInt(jobId, 0);
                        jobObj.put("credits_used", creditsUsed);
                    }
                } catch (Exception e) {
                    LOGGER.log(Level.WARNING, "Merge job is null", e);
                }
                break;
            default:
                break;
        }
        responseObject.put("open_ai", getPlatformAIDetails(zsoid));
        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_OK, null, responseObject);


//        User userObj = IAMUtil.getCurrentUser();
//        String spaceId = null;
//        try {
//            List<ProductInfo> userProducts = ZohoFS.getUserProducts(userObj.getZuid());
//            ProductInfo pdtInfo = null;
//            if(!(userProducts == null || userProducts.isEmpty()))
//            {
//                for(ProductInfo tempProductInfo: userProducts)
//                {
//                    if(tempProductInfo.getProductType() == ProductType.TEAMDRIVE.getType())
//                    {
//                        pdtInfo = tempProductInfo;
//                        break;
//                    }
//                }
//                spaceId = pdtInfo.getOwnerId();
//            }
//        } catch (Exception e) {
//            LOGGER.log(Level.WARNING, "Exception while getting the spaceID :: ", e);
//        }
//
//        String ownerName = DocumentUtils.getZUserName(spaceId);
//        try {
//            JSONObject responseJson = MergeConfigurationUtils.getMergeJobs(ownerName, null);
//            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_OK, null, responseJson);
//        } catch (Exception e) {
//            LOGGER.log(Level.WARNING, "Merge job is null", e);
//        }

//        List<String> paymentsAdminZuids;
//        paymentsAdminZuids = getAdminZuids(serviceZid);
//        if(paymentsAdminZuids.contains(currentUserZuid))
//        {
//            String method = request.getParameter("method");
//            JSONObject responseJson = null;
//            try
//            {
//                String ownerName = DocumentUtils.getZUserName(adminZUID);
//                responseJson = MergeConfigurationUtils.getMergeJobs(ownerName, null);
//                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_OK, null, responseJson);
//            }
//            catch(Exception e)
//            {
//                LOGGER.log(Level.INFO, "error in ZSAdminDashboardAction", e);
//                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, null, APIErrorHandler.getErrorObject(APIErrorHandler.INTERNAL_SERVER_ERR, null, null));
//            }
//        }
//        else
//        {
//            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_ADMIN_ERR, null, null));
//        }
    }

//    public static List<String> getAdminZuids(String serviceZid) {
//        long startTime = System.currentTimeMillis();
//        String serviceName = EnginePropertyUtil.getSheetPropertyValue("serviceName");
//        List<String> adminZuids = new ArrayList<String>();
//
//        try {
//            ZIamApi iamApi = ZIamApiImpl.getInstance();
//            long serviceZidLong = Long.valueOf(serviceZid);
//            OrgType orgType = iamApi.getServiceOrgType(serviceName);
//            adminZuids = iamApi.getServiceOrgMembers(orgType, serviceZidLong, com.adventnet.iam.ServiceOrgMember.UserRole.ADMIN.getRoleInt(), 0, 50);
//        } catch (Exception e) {
//            LOGGER.log( Level.WARNING, "Exception - ", e); //No I18N
//        }
//        LOGGER.log(Level.INFO, "Get admin zuids called for serviceZid - {0} : adminZuids - {1} - Time taken - {2}", new Object[]{serviceZid, adminZuids, ( System.currentTimeMillis() - startTime ) }); //No I18N
//
//        return adminZuids;
//    }

    private JSONObject getPlatformAIDetails(String zsoid) throws Exception {
        JSONObjectWrapper responseObj = new JSONObjectWrapper();

        List<Integer> disabledFeatureList = new ArrayList<>();
        PlatformAIStore storeObj = PlatformAIAuthUtils.getParamStore(zsoid);

        List<String> serviceList = PlatformAIConsole.getServiceList(zsoid);
        responseObj.put(OpenAiConstants.RESPONSE_HEADERS.SERVICE, serviceList);

        if (storeObj == null) {
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.IS_TOKEN_PRESENT, false);
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.DISABLE_FEATURE, disabledFeatureList);
        } else {
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.IS_TOKEN_PRESENT, true);
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.DISABLE_FEATURE, PlatformAIAuthUtils.bitsToDisabledList(storeObj.getFeatureBits()));
        }
        return responseObj.getJsonObject();
    }
}
