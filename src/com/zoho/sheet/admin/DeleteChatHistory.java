/*  $Id$ */
package com.zoho.sheet.admin;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.Table;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.util.RemoteUtils;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.zfsng.client.ZohoFS;
public class DeleteChatHistory extends StrutsRequestHandler {
	public static final Logger LOGGER = Logger.getLogger(DeleteChatHistory.class.getName());
	public String execute() throws Exception {
                boolean isSheetAdmin = request.isUserInRole("SheetAdmin");
                if(!isSheetAdmin){
                    return null;
                }

		String code = request.getParameter("justification_code");
		String ticketID = request.getParameter("support_ticket_id");
		String description = request.getParameter("description");
		String category = request.getParameter("category");
		JSONObjectWrapper respObj = new JSONObjectWrapper();

		if(code == null || description == null || ("support".equals(category) && ticketID == null) ||
				"".equals(code) || "".equals(description) || ("support".equals(category) && "".equals(ticketID))) {
			respObj.put("ERROR", "Mandatory fields should not be empty");

			HttpServletResponseWrapper.sendResponse(response,respObj.toString(),HttpServletResponseWrapper.MimeType.TEXT);
			return null;
		}

        String rsid = (String) request.getParameter("rid");
		JSONObjectWrapper responseObject = new JSONObjectWrapper();
		rsid = RemoteUtils.maskNull(rsid);
		if (rsid != null) {
			try {
				String docOwner     = DocumentUtils.getDocOwnerFromRid(rsid);
				LOGGER.info("docOwner :::"+docOwner);
				String docId        = DocumentUtils.getDocumentId(rsid, docOwner);
				LOGGER.info("docOwner :::"+docOwner+" docId :::"+docId);
				Persistence pers    = SheetPersistenceUtils.getPersistence(docOwner);
				SelectQueryImpl selQuery = new SelectQueryImpl(new Table("Chat"));
				selQuery.addSelectColumn(new Column("Chat", "*"));
				Criteria delHistoryCri = new Criteria(new Column("Chat", "DOCUMENT_ID"), docId, QueryConstants.EQUAL);
				selQuery.setCriteria(delHistoryCri);
				DataObject data 		= pers.get(selQuery);
			    if( data.size("Chat") >0 ){
					pers.delete(delHistoryCri);
					responseObject.put("STATUS", "SUCCESS");
					responseObject.put("MSG", "Chat history deleted successfully for rid:"+ rsid);
					response.setContentType("application/json;charset=UTF-8");//No I18N
					response.getWriter().print(responseObject); // NO OUTPUTENCODING

			    }
			    else{
			    	responseObject.put("STATUS", "SUCCESS_NO_DATA_FOUND");
			    	responseObject.put("MSG", "No chat Record Found for rsid :"+ rsid);
					response.setContentType("application/json;charset=UTF-8");//No I18N
			    	response.getWriter().print(responseObject); // NO OUTPUTENCODING
			    }
			    
			    JSONObjectWrapper actionDescription = new JSONObjectWrapper();
				actionDescription.put("description", "DELETE_CHAT_HISTORY");
				
			    AdminPageAudit.addActionToDB(request, ZohoFS.getOwnerZID(rsid), rsid, actionDescription);
				
			} catch (Exception e) {
				responseObject.put("STATUS", "ERROR");
				responseObject.put("MSG", "Exception occured while deleting Chat History for rid :"+rsid);
				LOGGER.info("Exception occured while deleting Chat History for rid ::"+ rsid+" Exception ::"+e );
				response.setContentType("application/json;charset=UTF-8");//No I18N
				HttpServletResponseWrapper.sendResponse(response, responseObject, HttpServletResponseWrapper.MimeType.JSON);
			}

		} else {

			responseObject.put("STATUS", "INFO");
			responseObject.put("MSG", "Please provide a resource rid :");
			response.setContentType("application/json;charset=UTF-8");//No I18N
			HttpServletResponseWrapper.sendResponse(response, responseObject, HttpServletResponseWrapper.MimeType.JSON);
		}

		return null;
	}

}
