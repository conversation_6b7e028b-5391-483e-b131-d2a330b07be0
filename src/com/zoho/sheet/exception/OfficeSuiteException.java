/**
 * $Id$
 **/
package com.zoho.sheet.exception;

import java.util.ArrayList;

import javax.servlet.ServletException;

public class OfficeSuiteException extends ServletException {
	
	ArrayList<ErrorInfo> errorsAsList = new ArrayList<ErrorInfo>();

	public OfficeSuiteException() {
    }

    public OfficeSuiteException(String errorCode) {
        super(errorCode);
    }
    
    public OfficeSuiteException(String errorMessage, int responseCode) {
        super(errorMessage);  
        ErrorInfo error = new ErrorInfo(responseCode);
        this.errorsAsList.add(error);
    }
    
    public OfficeSuiteException(String errorMessage, int responseCode, String[] secondaryErrorMessage) {
        super(errorMessage);  
        ErrorInfo error = new ErrorInfo(responseCode, secondaryErrorMessage);
        this.errorsAsList.add(error);
    }
    
    public class ErrorInfo {
    	private int errorCode = 0;
    	private String secondaryErrorMessage[];
    	
    	public ErrorInfo(int errorCode) {
    		this.errorCode = errorCode;             
        }
    	 
    	public ErrorInfo(int errorCode, String[] secondaryErrorMessage){
    		this.errorCode = errorCode;
    		this.secondaryErrorMessage = secondaryErrorMessage;
    	}

    	public int getErrorCode() {
            return errorCode;
        }

    	public void setErrorCode(int errorCode) {
    		this.errorCode = errorCode;
        }
    	
    	public String[] getSecondaryErrorMessage(){
    		return secondaryErrorMessage;
    	}
    	
    	public void setSecondaryErrorMessage(String[] secondaryErrorMessage){
    		this.secondaryErrorMessage = secondaryErrorMessage;
    	}
    }
    
    public int getErrorCode() {
    	if (this.errorsAsList.size() > 0) {
            return this.errorsAsList.get(0).getErrorCode();
        }
        return -1;
    }
    
    public String[] getSecondaryErrorMessage(){
    	if(this.errorsAsList.size() > 0) {
    		return this.errorsAsList.get(0).getSecondaryErrorMessage();
    	}
    	return null;
    }

}

