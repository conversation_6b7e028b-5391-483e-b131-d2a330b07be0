//$Id$
package com.zoho.sheet.exception; 

import java.util.HashMap;
import java.util.Map;

public class IAMErrorCodes {
	
	public final static Map<String, String> ERROR_MAP = new HashMap<String, String>(); 
	
	static {

		ERROR_MAP.put("DEFAULT", "error.internal"); 

		ERROR_MAP.put("REMOTE_IP_LOCKED", "Error.Internal.Server"); 
		ERROR_MAP.put("INVALID_TICKET", "Error.Internal.Server"); 
		ERROR_MAP.put("AUTHENTICATION_FAILED", "Error.Internal.Server"); 
		ERROR_MAP.put("INVALID_CSRF_TOKEN", "Error.Internal.Server"); 

		ERROR_MAP.put("UNMATCHED_FILE_CONTENT_TYPE", "Import.ExceedsLimit"); 
		ERROR_MAP.put("VIRUS_DETECTED", "Import.ExceedsLimit"); 
		ERROR_MAP.put("INVALID_FILE_EXTENSION", "Import.ExceedsLimit"); 
		ERROR_MAP.put("FILE_SIZE_MORE_THAN_ALLOWED_SIZE", "Import.ExceedsLimit"); 
		ERROR_MAP.put("UNABLE_TO_IMPORT", "Import.ExceedsLimit"); 
		ERROR_MAP.put("MORE_THAN_MAX_LENGTH", "Error.Exceed.Max.Length");
		ERROR_MAP.put("IMAGE_DIMENSIONS_EXCEEDED", "Error.Image.Dimension.Limit.Exceed");

	}

	public static String getMessageKey(String errorCode) {

		if(ERROR_MAP.containsKey(errorCode)) {
			return ERROR_MAP.get(errorCode); 
		} else {
			return ERROR_MAP.get("DEFAULT"); 
		}
	}

//	class IAMExcpetionHandler {
//		
//		String msg = "Error.Internal.Server"; 
//		MsgType msgType = MsgType.ERROR; 
//		DisplayType displayType = DisplayType.DIALOG; 
//		
//		void IAMexceptionHandler(String msg, MsgType msgType, DisplayType displayType) {
//			
//		}
//
//	}
	
}
