package com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.insights;

import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.ZiaSubTaskAbstract;
import com.zoho.sheet.zia2.interfaces.ZiaNLHelper;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class ZiaBotRecommendation extends ZiaSubTaskAbstract {

    public ZiaBotRecommendation(ZiaHolder holder) {
        super(holder);
        this.setSkipLogging(true);
    }

    @Override
    public void execute() throws Exception {
        if (holder.getZiaParams().isRefreshBot() || !holder.getNlHelper().hasBot()) {
            File csvFile = getCSVFile();
            createBot(csvFile);
        }
    }

    private File getCSVFile() throws IOException {
        long startTime = System.currentTimeMillis();
        File csvFile = holder.getTableDataHolder().getAsCsvFile();
        holder.addTimeLine(ZiaConstants.SubTaskName.CSV_GENERATION, System.currentTimeMillis() - startTime, ZiaConstants.TaskType.SUBTASK);
        return csvFile;
    }

    private void createBot(File csvFile) {
        long startTime = System.currentTimeMillis();

        ZiaNLHelper nlHelper = holder.getNlHelper();
        nlHelper.createBot(csvFile);

        holder.addTimeLine(ZiaConstants.SubTaskName.NLP_BOT_CREATION, System.currentTimeMillis() - startTime, ZiaConstants.TaskType.SUBTASK);
    }

}
