package com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.conversion.deciders;

import com.zoho.sheet.zia2.conversionResponse.DataBarResponse;
import com.zoho.sheet.zia2.conversionResponse.ZiaConversionResponse;
import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaDecider;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.statistics.ZiaStatUtils;
import com.zoho.sheet.zia2.utils.ConversionUtils;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.ziabeans.conversions.ConversionHolder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Queue;

/**
 * <AUTHOR> - 13900
 */

public class DataBarFormat implements ZiaDecider {

    private final List<Integer> removeList = new ArrayList<>();
    private static final double MAX_DATARANGE = 70.00;
    private static final int MAXCOLS_LIMIT = 12;
    private int limit;
    private static final double DISTRIBUTION_PER = 60.00;
    private double anomalyRate;

    @Override
    public void analyze(ZiaHolder holder) {
        long startTime = System.currentTimeMillis();
        ConversionHolder conversionHolder = holder.getConversionHolder();
        ZiaTableDataHolder tableDataHolder = holder.getTableDataHolder();
        int rowSize = conversionHolder.getRowSize();
        List<Integer> rankings = conversionHolder.getRankings();
        Queue<ZiaConversionResponse> dataBarResponseQueue = conversionHolder.getDataBarResponseQueue();

        int numericalColSize = conversionHolder.getNumericalCols().size();
        limit = numericalColSize >= MAXCOLS_LIMIT ? 2 : 1;
        anomalyRate = rowSize <= 1000 ? 30.00 : 40.00;

        if (!rankings.isEmpty()) {
            generateDataBarFormat(rankings, dataBarResponseQueue, tableDataHolder, conversionHolder);
        }

        holder.addTimeLine(ZiaConstants.conversionTask.DATABAR, System.currentTimeMillis() - startTime, ZiaConstants.TaskType.SUBTASK);
    }

    private void generateDataBarFormat(List<Integer> numericalCols, Queue<ZiaConversionResponse> dataBarResponseQueue, ZiaTableDataHolder tableDataHolder, ConversionHolder conversionHolder) {
        int count = 0;

        for (int i = 0; i < numericalCols.size() && count < limit; i++) {

            ZiaColumnDataHolder numCol = tableDataHolder.getColumnDataHolderByIndex(numericalCols.get(i));

            if (numCol.isMultiRange()) {
                continue;
            }

            List<Double> columnValues = ZiaStatUtils.fetchNumericalData(numCol, true);
            List<Double> copyColumnValues = new ArrayList<>(columnValues);
            double negativeClusterRatio = numCol.getConversionEssentials().getNegativeClusterRatio();
            double dataRange = ConversionUtils.calcMinMaxDeviation(columnValues);
            List<ArrayList<Double>> quartiles = ConversionUtils.binningHelper(copyColumnValues, 3.0);
            List<Integer> binSizes = new ArrayList<>();

            for (int j = 0; j < 3; j++) {
                binSizes.add(quartiles.get(j).size());
            }

            ZiaRange contentRange = numCol.getColumnContentRange().get(0);
            ZiaRange columnRange = numCol.getColumnRange().get(0);

            if (dataRange <= MAX_DATARANGE && negativeClusterRatio == 0.00 && checkDistribution(binSizes) && checkAnomalyRate(columnValues, conversionHolder.getRowSize())) {
                //deviation is large for databars and  3 quartile's distribution is more or less equal in proportion
                //databar response
                DataBarResponse dbResponse = new DataBarResponse(numCol.getHeaderName(), numCol.getColumnIndex(), contentRange, columnRange);
                // push repsonse in queue
                dataBarResponseQueue.add(dbResponse);
                //delete column
                removeList.add(numCol.getColumnIndex());
                count++;
            }
        }

//        if (count > 0) {
//            filterUsedColumns(conversionHolder);
//        }
    }

    private boolean checkDistribution(List<Integer> binSizes) {
        Collections.sort(binSizes);
        int size = binSizes.size();
        int lowValue = binSizes.get(0);
        int highValue = binSizes.get(size - 1);
        double percentage = 100.00 * lowValue / highValue;
        return percentage >= DISTRIBUTION_PER;
    }

    private boolean checkAnomalyRate(List<Double> columnValues, int rowSize) {
        boolean ascStart = columnValues.get(0) < columnValues.get(1);
        int anomalyCount = 0;

        for (int i = 1; i < columnValues.size() - 1; i++) {
            if (ascStart && columnValues.get(i) > columnValues.get(i + 1) || !ascStart && columnValues.get(i) <= columnValues.get(i + 1)) {
                if (i + 1 != columnValues.size() - 1) {
                    ascStart = columnValues.get(i + 1) < columnValues.get(i + 2);
                    i++;
                }
                anomalyCount++;
            }
        }
        double anomalyPercent = ((double) anomalyCount / rowSize) * 100.00;
        return anomalyPercent >= anomalyRate;
    }

    private void filterUsedColumns(ConversionHolder conversionHolder) {
        ConversionUtils.deleteRankings(conversionHolder, removeList);
    }
}
