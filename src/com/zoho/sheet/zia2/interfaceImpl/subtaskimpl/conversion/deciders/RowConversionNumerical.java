package com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.conversion.deciders;

import com.zoho.sheet.zia2.conversionResponse.RowOutlierResponse;
import com.zoho.sheet.zia2.conversionResponse.RowSkewnessResponse;
import com.zoho.sheet.zia2.conversionResponse.ZiaConversionResponse;
import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaDecider;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.statistics.ZiaStatUtils;
import com.zoho.sheet.zia2.statistics.components.PercentileComponent;
import com.zoho.sheet.zia2.statistics.components.SkewComponent;
import com.zoho.sheet.zia2.utils.ConversionUtils;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.ziabeans.conversions.ConversionHolder;

import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR> - 13900
 */

public class RowConversionNumerical implements ZiaDecider {
    private final List<Integer> removeList = new ArrayList<>();
    private int rowSize;
    private List<Double> colValues = new ArrayList<>();  // contains sorted columnValues
    private static final int MAXCOLS_LIMIT = 12;
    private List<ZiaRange> tableContentRange;
    private int maxRow;
    private static final double LIMITER_PER = 0.2; // 20%

    /*
     * upto 1000 row size allowed outliers percent is 10
     * above 1000 row size allowed outliers percent is 5
     * co-relation value limit is set to max of 0.3
     */
    @Override
    public void analyze(ZiaHolder holder) {
        long startTime = System.currentTimeMillis();
        ConversionHolder conversionHolder = holder.getConversionHolder();
        maxRow = conversionHolder.getRowSize();
        ZiaTableDataHolder tableDataHolder = holder.getTableDataHolder();
        tableContentRange = tableDataHolder.getTableContentRange();
        List<Integer> rankings = conversionHolder.getRankings();
        int numericalColSize = conversionHolder.getNumericalCols().size();


        if (numericalColSize == 1 && rankings.size() == 1) {
            //special case where only one num col is present skip here other deciders will handle it
            return;
        }

        if (!rankings.isEmpty()) { //numerical cols are present
            rowSize = conversionHolder.getRowSize();
            Queue<ZiaConversionResponse> skewnessResponseQueue = conversionHolder.getSkewnessResponseQueue();
            Queue<ZiaConversionResponse> outlierResponseQueue = conversionHolder.getOutlierResponseQueue();
            int limit = numericalColSize >= MAXCOLS_LIMIT ? 2 : 1;
            limit *= 2; //since cols are not sent as pairs limit is doubled
            int rankSize = rankings.size();
            int count = 0;

            for (int i = 0; i < rankSize && count < limit; i++) {

                int skewSize = skewnessResponseQueue.size();
                int outlierSize = outlierResponseQueue.size();
                ZiaColumnDataHolder numCol = tableDataHolder.getColumnDataHolderByIndex(rankings.get(i));
                generateRowConversionNumerical(numCol, conversionHolder);

                if (outlierResponseQueue.size() > outlierSize || skewnessResponseQueue.size() > skewSize) {
                    count++;
                }
            }
            if (count > 0) {
                filterUsedColumns(conversionHolder);
            }
        }
        holder.addTimeLine(ZiaConstants.conversionTask.ROW_NUM, System.currentTimeMillis() - startTime, ZiaConstants.TaskType.SUBTASK);

    }

    private void generateRowConversionNumerical(ZiaColumnDataHolder numCol, ConversionHolder conversionHolder) {
        Queue<ZiaConversionResponse> outlierResponseQueue = conversionHolder.getOutlierResponseQueue();
        Queue<ZiaConversionResponse> skewnessResponseQueue = conversionHolder.getSkewnessResponseQueue();
        if (!generateOutlierConversion(numCol, outlierResponseQueue)) {
            generateSkewnessConversion(numCol, skewnessResponseQueue);
        }
    }

    private void generateSkewnessConversion(ZiaColumnDataHolder columnDataHolder, Queue<ZiaConversionResponse> skewnessResponseQueue) {
        /*
         * adds top 5 or bottom 5 response if eligible else exits doing nothing
         */
        List<Double> columnValues = ZiaStatUtils.fetchNumericalData(columnDataHolder, true);
        double[] columnValuesArr = columnValues.stream().mapToDouble(d -> d).toArray();

        double skewness = new SkewComponent(columnValuesArr).getResult();
        Iterator<ZiaConversionResponse> iterator = skewnessResponseQueue.iterator();
        Set<String> names = new HashSet<>();

        while (iterator.hasNext()) {
            String columnName = iterator.next().getColumnName();
            names.add(columnName);
        }

        int valueLimit = maxRow <= 20 ? (int) (maxRow * LIMITER_PER) : 5;

        ZiaRange contentRange = columnDataHolder.getColumnContentRange().get(0);
        ZiaRange columnRange = columnDataHolder.getColumnRange().get(0);

        if (skewness >= 0.5 && !names.contains(columnDataHolder.getHeaderName()) && valueLimit > 1) {
            //positive skew //top 5
            RowSkewnessResponse roResponse = new RowSkewnessResponse(
                    columnDataHolder.getHeaderName(),
                    columnDataHolder.getColumnIndex(),
                    contentRange,
                    tableContentRange,
                    ZiaConstants.CF_CONDITION_TYPE.FORMULA,
                    ZiaConstants.CF_CONDITION.FORMULA,
                    true,
                    columnRange,
                    columnDataHolder.getConversionEssentials().isMixType(),
                    valueLimit);
            skewnessResponseQueue.add(roResponse);
            removeList.add(columnDataHolder.getColumnIndex());

        } else if (skewness <= -0.5 && !names.contains(columnDataHolder.getHeaderName()) && valueLimit > 1) {
            //negative skew //bottom 5
            RowSkewnessResponse roResponse = new RowSkewnessResponse(
                    columnDataHolder.getHeaderName(),
                    columnDataHolder.getColumnIndex(),
                    contentRange,
                    tableContentRange,
                    ZiaConstants.CF_CONDITION_TYPE.FORMULA,
                    ZiaConstants.CF_CONDITION.FORMULA,
                    false,
                    columnRange,
                    columnDataHolder.getConversionEssentials().isMixType(),
                    valueLimit);
            skewnessResponseQueue.add(roResponse);
            removeList.add(columnDataHolder.getColumnIndex());
        }
    }

    private boolean generateOutlierConversion(ZiaColumnDataHolder columnDataHolder, Queue<ZiaConversionResponse> outlierResponseQueue) {
        /*
         * return true is outlier suggestion can be generated and added it in response list
         * else false
         * innerFence - 1.5iqr range
         * outerFence - 3.0iqr range
         */
        colValues = ZiaStatUtils.fetchNumericalData(columnDataHolder, true);
        Collections.sort(colValues);

        IQR iqrObject = new IQR(colValues);
        double lowerFencePer = iqrObject.lowerFencePer;
        double upperFencePer = iqrObject.upperFencePer;

        if (lowerFencePer == 0 && upperFencePer == 0) {
            return false;
        }

        if (columnDataHolder.getConversionEssentials().isMixType()) {
            // contains improper data
            return false;
        }

        if (columnDataHolder.isMultiRange()) {
            return false;
        }

        ZiaRange columnContentRange = columnDataHolder.getColumnContentRange().get(0);
        ZiaRange columnRange = columnDataHolder.getColumnRange().get(0);
        double maxAllowedPer = rowSize < 1000 ? 10.00 : 5.00; // maximum allowed outlier percent

        if (lowerFencePer <= maxAllowedPer) {
            // checking whether lower-fence top or bottom is applicable
            boolean isUpperRange = iqrObject.lowerFenceBottom <= iqrObject.lowerFenceTop;
            double iqrValue = isUpperRange ? iqrObject.upperInnerFence : iqrObject.lowerInnerFence;
            String condValue = getRoundedOutlier(columnDataHolder, isUpperRange, iqrValue);

            RowOutlierResponse roResponse = new RowOutlierResponse(
                    columnDataHolder.getHeaderName(),
                    columnDataHolder.getColumnIndex(),
                    columnContentRange,
                    tableContentRange,
                    ZiaConstants.CF_CONDITION_TYPE.FORMULA,
                    ZiaConstants.CF_CONDITION.FORMULA,
                    condValue,
                    !isUpperRange,
                    columnRange);
            outlierResponseQueue.add(roResponse);
            removeList.add(columnDataHolder.getColumnIndex());

            return true;
        } else if (upperFencePer <= maxAllowedPer) {
            // checking whether upper-fence top or bottom is applicable
            boolean isUpperRange = iqrObject.upperFenceBottom <= iqrObject.upperFenceTop;
            double iqrValue = isUpperRange ? iqrObject.upperOuterFence : iqrObject.lowerOuterFence;
            String condValue = getRoundedOutlier(columnDataHolder, isUpperRange, iqrValue);

            RowOutlierResponse roResponse = new RowOutlierResponse(
                    columnDataHolder.getHeaderName(),
                    columnDataHolder.getColumnIndex(),
                    columnContentRange,
                    tableContentRange,
                    ZiaConstants.CF_CONDITION_TYPE.FORMULA,
                    ZiaConstants.CF_CONDITION.FORMULA,
                    condValue,
                    !isUpperRange,
                    columnRange);
            outlierResponseQueue.add(roResponse);
            removeList.add(columnDataHolder.getColumnIndex());
            return true;
        }
        return false;
    }

    private String getRoundedOutlier(ZiaColumnDataHolder columnDataHolder, boolean isUpperRange, double iqrValue) {
        int floatIndex = columnDataHolder.getConversionEssentials().getFloatPrecision();
        String condValue;
        // outlier point can be a whole number or double ( depends on the col values)
        condValue = calculateOutlier(colValues, isUpperRange, iqrValue, floatIndex);
        return condValue;
    }

    private String calculateOutlier(List<Double> colValues, boolean isUpperRange, double target, int floatIndex) {
        String roundedOutlier;
        int[] indexes = bSearch(colValues, isUpperRange, target);
        int rightIndex = indexes[0];
        int leftIndex = indexes[1];
        int roundedTarget = (int) Math.round(target);
        String targetStr = String.valueOf(roundedTarget);
        int roundedLen = targetStr.length() - 1;

//        if (targetLength == 0) {
//            //edge case the outlier value is single digit so round off not possible
//            return targetStr;
//        }
//
//        if (rightIndex != leftIndex) {
//            // excluding the edge case where the target is present in data series
//            for (int i = targetLength; i >= 2; i--) {
//                int tenthValue = ConversionUtils.getTenthRoundedValue(roundedTarget, i, isUpperRange);
//                if (!isOutlierValuesDisturbed(colValues, tenthValue, isUpperRange, isUpperRange ? rightIndex : leftIndex)) {
//                    return String.valueOf(tenthValue);
//                }
//            }
//        }
//
//        // all rounded off failed checking 10^1 round off
//        int leastRound = ConversionUtils.getTenthRoundedValue(roundedTarget, 1, isUpperRange);
//        if ((isUpperRange && leastRound > colValues.get(colValues.size() - 1) || (!isUpperRange && leastRound < colValues.get(0)))) {
//            DecimalFormat decimalFormatter = ConversionUtils.getDecimalFormatter(floatIndex);
//            return decimalFormatter.format(target);
//        } else {
//            return String.valueOf(leastRound);
//        }

        if (roundedLen == 0) {
            //single digit outlier
            if ((isUpperRange && roundedTarget > colValues.get(colValues.size() - 1) || (!isUpperRange && roundedTarget < colValues.get(0)))) {
                DecimalFormat decimalFormatter = ConversionUtils.getDecimalFormatter(floatIndex);
                return decimalFormatter.format(target);
            } else {
                return targetStr;
            }
        }

        int leftTenthValue = ConversionUtils.getTenthRoundedValue(roundedTarget, roundedLen - 1 != 0 ? roundedLen - 1 : 1, false);
        int rightTenthValue = ConversionUtils.getTenthRoundedValue(roundedTarget, roundedLen - 1 != 0 ? roundedLen - 1 : 1, true);
        if (isUpperRange) {
            //top outliers
            if (rightTenthValue <= colValues.get(rightIndex)) {
                //rightTenthValue is under outlier range
                roundedOutlier = String.valueOf(rightTenthValue);
            } else if (leftTenthValue > colValues.get(leftIndex)) {
                //leftTenthValue is under outlier range
                roundedOutlier = String.valueOf(leftTenthValue);
            } else {
                //both the values are out of the outlier ranges
                int leftOfRvalue = bSearch(colValues, isUpperRange, rightTenthValue)[1]; //left value
                int rightOfValue = bSearch(colValues, isUpperRange, leftTenthValue)[0]; // right value
                int extraPoints = leftIndex - rightOfValue; // extra points lying ahead of the outlier;
                int deferPoints = leftOfRvalue - rightIndex; // reduced points lying after outlier;
                roundedOutlier = String.valueOf(extraPoints > deferPoints ? rightTenthValue : leftTenthValue);
            }
        } else {
            //bottom outliers vice versa with top outliers case
            if (leftTenthValue >= colValues.get(leftIndex)) {
                //leftTenthValue is under outlier range
                roundedOutlier = String.valueOf(leftTenthValue);
            } else if (rightTenthValue < colValues.get(rightIndex)) {
                //rightTenthValue is under outlier range
                roundedOutlier = String.valueOf(rightTenthValue);
            } else {
                int leftOfRvalue = bSearch(colValues, isUpperRange, rightTenthValue)[1]; //left value
                int rightOfValue = bSearch(colValues, isUpperRange, leftTenthValue)[0]; // right value
                int deferPoints = leftIndex - rightOfValue;
                int extraPoints = leftOfRvalue - rightIndex;
                roundedOutlier = String.valueOf(extraPoints > deferPoints ? rightTenthValue : leftTenthValue);
            }
        }
        return roundedOutlier;
    }

//    private boolean isOutlierValuesDisturbed(List<Double> colValues, int tenthValue, boolean isUpperRange, int closeIndex) {
//        if (isUpperRange && tenthValue > colValues.get(closeIndex)) {
//            return true;
//        } else if (!isUpperRange && tenthValue < colValues.get(closeIndex)) {
//            return true;
//        }
//        return false;
//    }


    private int[] bSearch(List<Double> colValues, boolean isUpperRange, double target) {
        int size = colValues.size();
        int left = isUpperRange ? size / 2 : 0;
        int right = isUpperRange ? size - 1 : size / 2;
        int[] keys = new int[2];

        while (left <= right) {
            int mid = left + (right - left) / 2;
            if (colValues.get(mid) == target) {
                keys[0] = mid;
                keys[1] = mid;
                return keys;
            }
            if (colValues.get(mid) < target) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        // if it arrives here element not found
        keys[0] = left;
        keys[1] = right;
        return keys;
    }

    public class IQR {
        double lowerFencePer, upperFencePer;   //total lower (3IQR + 1.5IQR) percent same for upper
        int lowerFenceBottom, lowerFenceTop, upperFenceBottom, upperFenceTop;  // top and bottom denotes each fence's upper and lower values
        double lowerInnerFence, upperInnerFence, lowerOuterFence, upperOuterFence; // IQR (inner and outer) fence values

        IQR(List<Double> colValues) {
            double[] columnValuesArr = colValues.stream().mapToDouble(d -> d).toArray();
            double q1 = new PercentileComponent(columnValuesArr, 25.00).getResult();
            double q3 = new PercentileComponent(columnValuesArr, 75.00).getResult();
            double iqr = q3 - q1;
            lowerInnerFence = q1 - (1.5 * iqr);
            upperInnerFence = q3 + (1.5 * iqr);
            lowerOuterFence = q1 - (3.0 * iqr);
            upperOuterFence = q3 + (3.0 * iqr);
            findFenceValues();
        }

        private void findFenceValues() {
            int totalLowerFence;
            int totalUpperFence;
            lowerFenceBottom = 0;
            lowerFenceTop = 0;
            upperFenceTop = 0;
            upperFenceBottom = 0;

            for (double val : colValues) {
                if (val < lowerOuterFence) {
                    upperFenceBottom++;
                }
                if (val < lowerInnerFence) {
                    lowerFenceBottom++;
                }
                if (val > upperOuterFence) {
                    upperFenceTop++;
                }
                if (val > upperInnerFence) {
                    lowerFenceTop++;
                }
            }

            totalLowerFence = lowerFenceBottom + lowerFenceTop;
            totalUpperFence = upperFenceBottom + upperFenceTop;
            lowerFencePer = (double) totalLowerFence / (double) rowSize;
            upperFencePer = (double) totalUpperFence / (double) rowSize;
        }
    }

    private void filterUsedColumns(ConversionHolder conversionHolder) {
        ConversionUtils.deleteRankings(conversionHolder, removeList);
    }
}

