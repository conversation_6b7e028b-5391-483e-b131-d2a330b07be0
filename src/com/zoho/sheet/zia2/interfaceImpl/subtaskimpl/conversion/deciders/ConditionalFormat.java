package com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.conversion.deciders;

import com.zoho.sheet.zia2.conversionResponse.ConditionalFormatResponse;
import com.zoho.sheet.zia2.conversionResponse.ZiaConversionResponse;
import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaDecider;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.statistics.ZiaStatUtils;
import com.zoho.sheet.zia2.utils.ConversionUtils;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.ziabeans.conversions.ConversionHolder;
import com.zoho.sheet.zia2.utils.ziabeans.conversions.ZiaConversionEssentials;

import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR> - 13900
 */

public class ConditionalFormat implements ZiaDecider {

    /*
     * cf numerical special case - positive values vs negative values is 70 : 30;
     */
    private static final double UNIQUENESS_HIGH = 25.00;
    private static final double UNIQUENESS_LOW = 0.00;
    private static final double DISTRIBUTION_PER = 60.00;
    private static final double ROW_DIST_RATIO = 40.00;
    private static final int MAX_ALLOWED_UNIQUE = 5;
    private final List<ZiaRange> catRemoveList = new ArrayList<>();
    private final List<Integer> numRemoveList = new ArrayList<>();
    private int limit;
    private int catDupCount = 0;
    private int catUniqueCount = 0;
    private int catSpecialCount = 0;
    private int numCount = 0;
    List<ZiaRange> tableContentRange;

    @Override
    public void analyze(ZiaHolder holder) {
        long startTime = System.currentTimeMillis();
        ConversionHolder conversionHolder = holder.getConversionHolder();
        ZiaTableDataHolder tableDataHolder = holder.getTableDataHolder();
        tableContentRange = tableDataHolder.getTableContentRange();
        List<ZiaColumnDataHolder> categoricalCols = conversionHolder.getCategoricalCols();
        List<Integer> numericalCols = conversionHolder.getRankings();
        int rowSize = conversionHolder.getRowSize();
        limit = conversionHolder.getNumericalCols().size() >= 12 ? 2 : 1;

        generateConditionalFormat(categoricalCols, numericalCols, rowSize, holder, tableDataHolder);
        holder.addTimeLine(ZiaConstants.conversionTask.CONDITIONAL_FORMAT, System.currentTimeMillis() - startTime, ZiaConstants.TaskType.SUBTASK);
//        ConversionUtils.applyResponseInHolder(holder, ZiaConstants.ColumnConversionType.CONDITIONAL_FORMAT_CLASSIC, cfResponseList);
    }

    private void generateConditionalFormat(List<ZiaColumnDataHolder> categoricalCols, List<Integer> numericalCols, int rowSize, ZiaHolder ziaHolder, ZiaTableDataHolder tableDataHolder) {
        ConversionHolder conversionHolder = ziaHolder.getConversionHolder();
        Queue<ZiaConversionResponse> conditionalFormatResponseQueue = conversionHolder.getConditonalFormatResponseQueue();
        for (ZiaColumnDataHolder catCol : categoricalCols) {
            if (!ConversionUtils.isSerialIncludedColumn(catCol.getHeaderName()) && !ziaHolder.getDecisionHelper().isCheckBoxOverlapped(catCol)) {
                generateCategoricalCF(catCol, rowSize, conditionalFormatResponseQueue);
            }
        }

        for (int i = 0; i < numericalCols.size() && numCount < limit; i++) {
            ZiaColumnDataHolder numCol = tableDataHolder.getColumnDataHolderByIndex(numericalCols.get(i));
            if (!ConversionUtils.isSerialIncludedColumn(numCol.getHeaderName())) {
                generateNumericalCF(numCol, conditionalFormatResponseQueue);
            }
        }
        if (numCount > 0) {
            //numerical conversion response was framed
            filterUsedColumns(conversionHolder);
        }
        /*
        commenting it as cat is not need to be removed because cat columns are not used thereafter in the deciders
         */
//        ConversionUtils.removeUsedColumns(catRemoveList, conversionHolder, ZiaConstants.ConversionDataType.CATEGORICAL); //removing used cat
    }

    private void generateNumericalCF(ZiaColumnDataHolder column, Queue<ZiaConversionResponse> conditonalFormatResponseQueue) {

        if (column.isMultiRange()) {
            return;
        }
        // bin and check
        ZiaRange columnRange = column.getColumnRange().get(0);
        ZiaRange contentRange = column.getColumnContentRange().get(0);

        List<Double> columnValues = ZiaStatUtils.fetchNumericalData(column, true);
        List<ArrayList<Double>> binnedList = ConversionUtils.binningHelper(columnValues, 4.0);
        List<Double> firstQuartileList = binnedList.get(0);
        List<Double> secondQuartileList = binnedList.get(1);
        List<Double> thirdQuartileList = binnedList.get(2);
        List<Double> LastQuartileList = binnedList.get(3);
        ZiaConversionEssentials colConvEssentials = column.getConversionEssentials();

        int firstSize = firstQuartileList.size();
        int lastSize = LastQuartileList.size();
        double cVal;
        if (firstSize < lastSize && firstSize != 1) {
            //less than or equal case
            double ratio1 = (double) firstSize / secondQuartileList.size() * 100.00;
            double ratio2 = (double) firstSize / thirdQuartileList.size() * 100.00;

            if (ratio1 < DISTRIBUTION_PER && ratio2 < DISTRIBUTION_PER) {
                cVal = firstQuartileList.get(firstSize - 1);
                int floatIndex = colConvEssentials.getFloatPrecision();
                String condValue;
                if (floatIndex != 0) {
                    DecimalFormat decimalFormatter = ConversionUtils.getDecimalFormatter(floatIndex);
                    condValue = decimalFormatter.format(cVal);
                } else {
                    condValue = String.valueOf((int) Math.ceil(cVal));
                }

                ConditionalFormatResponse cfResponse = new ConditionalFormatResponse(
                        column.getHeaderName(),
                        column.getColumnIndex(),
                        contentRange,
                        tableContentRange,
                        ZiaConstants.CF_CONDITION_TYPE.ANY_VALUE,
                        ZiaConstants.CF_CONDITION.LESS_THAN_EQUAL,
                        condValue,
                        ZiaConstants.ConversionSubType.cfNumerical.toString(),
                        colConvEssentials.isMixType(),
                        columnRange);
                conditonalFormatResponseQueue.add(cfResponse);
                numRemoveList.add(column.getColumnIndex());
                numCount++;
            }
        } else if (lastSize != 1) {
            //greater than or equal case
            double ratio1 = (double) lastSize / secondQuartileList.size() * 100.00;
            double ratio2 = (double) lastSize / thirdQuartileList.size() * 100.00;

            if (ratio1 < DISTRIBUTION_PER && ratio2 < DISTRIBUTION_PER) {
                cVal = LastQuartileList.get(0);
                int floatIndex = colConvEssentials.getFloatPrecision();
                String condValue;
                if (floatIndex != 0) {
                    DecimalFormat decimalFormatter = ConversionUtils.getDecimalFormatter(floatIndex);
                    condValue = decimalFormatter.format(cVal);
                } else {
                    condValue = String.valueOf((int) Math.ceil(cVal));
                }

                ConditionalFormatResponse cfResponse = new ConditionalFormatResponse(
                        column.getHeaderName(),
                        column.getColumnIndex(),
                        contentRange,
                        tableContentRange,
                        ZiaConstants.CF_CONDITION_TYPE.ANY_VALUE,
                        ZiaConstants.CF_CONDITION.GREATER_THAN_EQUAL,
                        condValue,
                        ZiaConstants.ConversionSubType.cfNumerical.toString(),
                        colConvEssentials.isMixType(),
                        columnRange);
                conditonalFormatResponseQueue.add(cfResponse);

                numRemoveList.add(column.getColumnIndex());
                numCount++;
            }
        }
    }

    private void generateCategoricalCF(ZiaColumnDataHolder column, int rowSize, Queue<ZiaConversionResponse> conditonalFormatResponseQueue) {

        if (column.isMultiRange()) {
            return;
        }

        ZiaRange columnRange = column.getColumnRange().get(0);
        ZiaRange contentRange = column.getColumnContentRange().get(0);

        if (catDupCount > limit && catUniqueCount > limit && catSpecialCount > limit) {
            return;
        }
        Map<String, Set<Integer>> uniqueValueIndicesMap = column.getUniqueValueIndicesMap();
        int uniqueValueCount = 0;

        for (Map.Entry<String, Set<Integer>> key : uniqueValueIndicesMap.entrySet()) {
            if (key.getValue().size() == 1) {
                uniqueValueCount++;
            }
        }
        double ratioPer = (double) uniqueValueCount / rowSize * 100;
        if (ratioPer >= 100.00 - UNIQUENESS_HIGH && ratioPer != 100.00 && catDupCount < limit) {
            // highlight duplicate
            ConditionalFormatResponse cfResponse = new ConditionalFormatResponse(
                    column.getHeaderName(),
                    column.getColumnIndex(),
                    contentRange,
                    tableContentRange,
                    ZiaConstants.CF_CONDITION_TYPE.CELL,
                    ZiaConstants.CF_CONDITION.DUPLICATE,
                    ";",
                    ZiaConstants.ConversionSubType.cfDuplicate.toString(),
                    false,
                    columnRange);
            conditonalFormatResponseQueue.add(cfResponse);

            //delete column
            catRemoveList.add(columnRange);
            catDupCount++;
        } else if (ratioPer > UNIQUENESS_LOW && ratioPer <= UNIQUENESS_HIGH && catUniqueCount < limit) {
            // highlight non duplicates (unique)
            ConditionalFormatResponse cfResponse = new ConditionalFormatResponse(
                    column.getHeaderName(),
                    column.getColumnIndex(),
                    contentRange,
                    tableContentRange,
                    ZiaConstants.CF_CONDITION_TYPE.CELL,
                    ZiaConstants.CF_CONDITION.UNIQUE,
                    ";",
                    ZiaConstants.ConversionSubType.cfUnique.toString(),
                    false,
                    columnRange);
            conditonalFormatResponseQueue.add(cfResponse);

            catRemoveList.add(columnRange);
            catUniqueCount++;
        } else {
            // check for special case
            List<Integer> occupancy = new ArrayList<>();
            int uniqueValues = uniqueValueIndicesMap.size();
            for (Map.Entry<String, Set<Integer>> key : uniqueValueIndicesMap.entrySet()) {
                boolean isCheckBoxType = ZiaConstants.CHECKBOX_WORDINGS.contains(key.getKey().toUpperCase());
                if (isCheckBoxType) {
                    return;
                } else {
                    occupancy.add(key.getValue().size());
                }
            }
            double uniqueValueDist = ((double) uniqueValues / rowSize) * 100;
            if (uniqueValueDist < ROW_DIST_RATIO && isValidDistribution(occupancy) && uniqueValues <= MAX_ALLOWED_UNIQUE && catSpecialCount < limit) {
                // generate special case response
                List<String> categoricalNames = new ArrayList<>();
                for (Map.Entry<String, Set<Integer>> key : uniqueValueIndicesMap.entrySet()) {
                    categoricalNames.add(key.getKey());
                }
                ConditionalFormatResponse cfResponse = new ConditionalFormatResponse(
                        column.getHeaderName(),
                        column.getColumnIndex(),
                        contentRange,
                        tableContentRange,
                        ZiaConstants.CF_CONDITION_TYPE.FORMULA,
                        ZiaConstants.CF_CONDITION.FORMULA,
                        String.join("&%", categoricalNames),
                        ZiaConstants.ConversionSubType.cfSpecial.toString(),
                        false,
                        columnRange);
                conditonalFormatResponseQueue.add(cfResponse);

                catRemoveList.add(columnRange);
                catSpecialCount++;
            }
        }
    }

    private boolean isValidDistribution(List<Integer> occupancy) {
        Collections.sort(occupancy);
        int size = occupancy.size();
        int max = occupancy.get(size - 1);
        int min = occupancy.get(0);
        double distributionPer = (double) max / min * 100.00;
        return distributionPer >= DISTRIBUTION_PER;
    }

    private void filterUsedColumns(ConversionHolder conversionHolder) {
        ConversionUtils.deleteRankings(conversionHolder, numRemoveList);
    }

}
