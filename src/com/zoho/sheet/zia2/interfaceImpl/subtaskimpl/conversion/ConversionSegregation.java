package com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.conversion;

import com.zoho.sheet.zia2.conversionResponse.ZiaConversionResponse;
import com.zoho.sheet.zia2.interfaces.ZiaDecider;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Queue;

/**
 * <AUTHOR> - 13900
 */

public class ConversionSegregation implements ZiaDecider {

    /*
     *first four queues in list denotes top prior responses (checkout the order in conversion holder)
     * max no of conversion insight limit is 20;
     */
    @Override
    public void analyze(ZiaHolder holder) {
        long startTime = System.currentTimeMillis();

        List<JSONObject> classicCfResponse = new ArrayList<>();
        List<JSONObject> colorScaleResponse = new ArrayList<>();
        List<JSONObject> iconSetResponse = new ArrayList<>();
        List<JSONObject> dataBarResponse = new ArrayList<>();
        List<Queue<ZiaConversionResponse>> responseQueues = holder.getConversionHolder().getResponseQueues();

        /**
         * first 5 response queues have response of rowid,rowdate,timeseries,rowoutlier && rowskewness
         */
        for (int i = 0; i < 5; i++) {
            Queue<ZiaConversionResponse> currentQueue = responseQueues.get(i);
            while (!currentQueue.isEmpty()) {
                classicCfResponse.add(currentQueue.poll().applyResponse());
            }
        }

        /**
         * adding classicCf response queue in response JSON
         */

        Queue<ZiaConversionResponse> classicCfQueue = responseQueues.get(5);
        while (!classicCfQueue.isEmpty()) {
            classicCfResponse.add(classicCfQueue.poll().applyResponse());
        }

        /**
         * adding colorScale response queue in response JSON
         */

        Queue<ZiaConversionResponse> colorScaleQueue = responseQueues.get(6);
        while (!colorScaleQueue.isEmpty()) {
            colorScaleResponse.add(colorScaleQueue.poll().applyResponse());
        }

        /**
         * adding classicCf response queue in response JSON
         */

        Queue<ZiaConversionResponse> dataBarQueue = responseQueues.get(7);
        while (!dataBarQueue.isEmpty()) {
            dataBarResponse.add(dataBarQueue.poll().applyResponse());
        }

        /**
         * adding iconSet response queue in response JSON
         */

        Queue<ZiaConversionResponse> iconSetQueue = responseQueues.get(8);
        while (!iconSetQueue.isEmpty()) {
            iconSetResponse.add(iconSetQueue.poll().applyResponse());
        }

        /*
        adding misc response queue in classic response JSON
         */

        Queue<ZiaConversionResponse> miscResponseQueue = responseQueues.get(9);
        while (!miscResponseQueue.isEmpty()) {
            classicCfResponse.add(miscResponseQueue.poll().applyResponse());
        }

        Map<String, Object> conversionResponse = holder.getResponseHelper().getConversionSuggestions();

        conversionResponse.put("CLASSICCF", classicCfResponse);
        conversionResponse.put("COLORSCALE", colorScaleResponse);
        conversionResponse.put("DATABAR", dataBarResponse);
        conversionResponse.put("ICONSET", iconSetResponse);


        holder.addTimeLine(ZiaConstants.conversionTask.CONVERSION_SEG, System.currentTimeMillis() - startTime, ZiaConstants.TaskType.SUBTASK);
    }
}
