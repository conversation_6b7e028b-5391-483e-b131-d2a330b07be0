package com.zoho.sheet.zia2.interfaceImpl.subtaskimpl;

import com.zoho.sheet.zia2.interfaceImpl.taskimpl.ZiaTaskAbstract;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;

/**
 * <AUTHOR>
 */

public abstract class ZiaSubTaskAbstract extends ZiaTaskAbstract {

    public ZiaSubTaskAbstract(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public ZiaConstants.TaskType getTaskType() {
        return ZiaConstants.TaskType.SUBTASK;
    }
}
