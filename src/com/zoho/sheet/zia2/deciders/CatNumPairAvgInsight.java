package com.zoho.sheet.zia2.deciders;


import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaDecider;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.utils.*;
import com.zoho.sheet.zia2.utils.ziabeans.conveyor.ZiaCombinationVO;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightDecisionHolder;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;

import java.util.*;

/*
 * author - kailas-pt5058

 */

public class CatNumPairAvgInsight implements ZiaDecider {

    private static final int SUGGEST_LIMIT = 1;
    private static final int CAT_COUNT_GRAPH = 8;
    private static final double DISTRIBUTION_PER = 75.00;
    private static final double SD_LIMIT = 1.5;
    private static final double GRAPH_PER = 60.00;
    private static final int MAX_CAT_PIVOT = 15;

    @Override
    public void analyze(ZiaHolder holder) {
        InsightDecisionHolder insightDecisionHolder = holder.getInsightDecisionHolder();
        List<ZiaCombinationVO> catNumCombinations = insightDecisionHolder.getInsightCombinationMap().get(ZiaConstants.InsightCombination.CAT_TO_NUM);
        ZiaTableDataHolder tableDataHolder = holder.getTableDataHolder();
        List<InsightSuggestion> catNumSuggestions = insightDecisionHolder.getCatNumSuggestions();
        ArrayList<Integer> sumPairs = getSumPairs(catNumSuggestions);
        catNumSuggestions.addAll(averageAnalysis(catNumCombinations, tableDataHolder, sumPairs, insightDecisionHolder.getInsightProperties().getWeightage(ZiaInsightWeightage.Operation.CAT_NUM_AVG)));
    }

    private ArrayList<Integer> getSumPairs(List<InsightSuggestion> catNumSuggestions) {
        ArrayList<Integer> pairList = new ArrayList<>();
        for (InsightSuggestion sugg : catNumSuggestions) {
            if (sugg.getCategoricalColumns().size() == 1) {
                List<Integer> categoricalColumns = sugg.getCategoricalColumns();
                List<Integer> valueColumns = sugg.getValueColumns();
                pairList.addAll(categoricalColumns);
                pairList.addAll(valueColumns);
            }
        }
        return pairList;
    }

    private List<InsightSuggestion> averageAnalysis(List<ZiaCombinationVO> combinations, ZiaTableDataHolder tableDataHolder, ArrayList<Integer> sumPairs, int weightage) {

        List<InsightSuggestion> suggestionList = new ArrayList<>();
        int rowSize = tableDataHolder.getContentRowSize();
        int allowedUniqueValues = rowSize / 2;

        for (int i = 0, catNumSuggestions = combinations.size(), count = 0; i < catNumSuggestions && count < SUGGEST_LIMIT; i++) {
            ZiaCombinationVO combinationVO = combinations.get(i);
            int catCol = combinationVO.getCol1();
            int numCol = combinationVO.getCol2();
            boolean diffPair = checkPair(catCol, numCol, sumPairs);

            if (!diffPair) {
                ZiaColumnDataHolder catColDataHolder = tableDataHolder.getColumnDataHolderByIndex(catCol);
                int uniqueValuesCount = catColDataHolder.getUniqueValuesCount();
//                ArrayList<Double> sumValues = getSummation(catColDataHolder);
//                ArrayList<Double> meanValues = getMean(catDistribution, sumValues);
//                double meanSD = new StdDeviationComponent(meanValues.stream().mapToDouble(Double::doubleValue).toArray()).getResult();
//
//                if (uniqueValuesCount > 1 && uniqueValuesCount <= allowedUniqueValues && uniqueValuesCount <= CAT_COUNT_GRAPH && uniqueValuesCount != rowSize) {
//                    if (isDistributionEqual(catDistribution) && meanSD >= SD_LIMIT) {
//                        if (canDrawGraph(meanValues)) {
//                            suggestionList.add(addSuggestion(catCol, numCol, tableDataHolder, ZiaConstants.RecommendationType.MIXED, weightage));
//                        } else {
//                            suggestionList.add(addSuggestion(catCol, numCol, tableDataHolder, ZiaConstants.RecommendationType.PIVOT, weightage));
//                        }
//                    } else {
//                        suggestionList.add(addSuggestion(catCol, numCol, tableDataHolder, ZiaConstants.RecommendationType.PIVOT, weightage));
//                    }
//                    count++;
//                } else if (uniqueValuesCount > 1 && uniqueValuesCount <= MAX_CAT_PIVOT && uniqueValuesCount != rowSize) {
//                    suggestionList.add(addSuggestion(catCol, numCol, tableDataHolder, ZiaConstants.RecommendationType.PIVOT, weightage));
//                    count++;
//                }


                if (uniqueValuesCount != rowSize) {
                    if (uniqueValuesCount > 1 && uniqueValuesCount <= allowedUniqueValues && uniqueValuesCount <= CAT_COUNT_GRAPH) {
                        List<Integer> catDistribution = ZiaInsightUtils.getCatDistribution(catColDataHolder);
                        if (isDistributionEqual(catDistribution)) {
                            suggestionList.add(addSuggestion(catCol, numCol, tableDataHolder, ZiaConstants.RecommendationType.MIXED, weightage));
                        }
                        else{
                            suggestionList.add(addSuggestion(catCol, numCol, tableDataHolder, ZiaConstants.RecommendationType.PIVOT, weightage));
                        }
                        count++;
                    } else {
                        suggestionList.add(addSuggestion(catCol, numCol, tableDataHolder, ZiaConstants.RecommendationType.PIVOT, weightage));
                        count++;
                    }
                }
            }
        }
        return suggestionList;
    }

    private boolean checkPair(int catCol, int numCol, ArrayList<Integer> sumPairs) {
        for (int i = 0; i < sumPairs.size(); i += 2) {
            if (catCol == sumPairs.get(i) && numCol == sumPairs.get(i + 1)) {
                return true;
            }
        }
        return false;
    }

    private ArrayList<Double> getSummation(ZiaColumnDataHolder columnDataHolder) {
        ArrayList<Double> sumValues = new ArrayList<>();
        Map<String, Set<Integer>> uniqueValueIndicesMap = columnDataHolder.getUniqueValueIndicesMap();
        for (Map.Entry<String, Set<Integer>> element : uniqueValueIndicesMap.entrySet()) {
            Set<Integer> values = element.getValue();
            double sum = 0.00;
            for (Integer rowIndex : values) {
                ZiaContentVO cell = columnDataHolder.getContentVO(rowIndex);
                if (Objects.nonNull(cell) && cell.getCellType().isNumberType()) {
                    double value = Double.parseDouble(cell.getDisplayValue());
                    sum += value;
                }
            }
            sumValues.add(sum);
        }
        return sumValues;
    }


    private ArrayList<Double> getMean(List<Integer> catDistribution, ArrayList<Double> sumValues) {
        for (int i = 0; i < sumValues.size(); i++) {
            Double val = sumValues.get(i);
            Double Temp = val / catDistribution.get(i);
            sumValues.set(i, Temp);
        }
        return sumValues;
    }

    private boolean isDistributionEqual(List<Integer> catDistribution) {
        Collections.sort(catDistribution);
        int size = catDistribution.size();
        int lowValue = catDistribution.get(0);
        int highValue = catDistribution.get(size - 1);
        double percentage = 100.00 * lowValue / highValue;
        return percentage >= DISTRIBUTION_PER;
    }

    private boolean canDrawGraph(ArrayList<Double> meanValues) {
        Collections.sort(meanValues);
        int size = meanValues.size();
        double lowValue = meanValues.get(0);
        double highValue = meanValues.get(size - 1);
        double percentage = 100.00 * lowValue / highValue;
        return percentage <= GRAPH_PER;
    }

    private InsightSuggestion addSuggestion(int catCol, int numCol, ZiaTableDataHolder tableDataHolder, ZiaConstants.RecommendationType chartType, int weightage) {
        InsightSuggestion suggestion = new InsightSuggestion();
        suggestion.setChartType(ZiaConstants.ZiaChartType.AGGREGATED);
        suggestion.addAggregatedOperation(ZiaConstants.AggregationOperation.AVG);
        suggestion.addRecommendationType(chartType);
        suggestion.getCategoricalColumns().add(catCol);
        suggestion.getValueColumns().add(numCol);
        suggestion.setWeightage(weightage);
        suggestion.setTitle(ZiaInsightUtils.getTitle("Average",     //NO I18N
                Collections.singletonList(tableDataHolder.getHeaderNameOfColumn(catCol)),
                Collections.singletonList(tableDataHolder.getHeaderNameOfColumn(numCol))
        ));
        return suggestion;
    }
}

