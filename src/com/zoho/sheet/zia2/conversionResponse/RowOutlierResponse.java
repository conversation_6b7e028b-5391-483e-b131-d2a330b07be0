package com.zoho.sheet.zia2.conversionResponse;

import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaRange;
import org.json.JSONObject;

import java.util.List;

/**
 * <AUTHOR> - 13900
 */

public class RowOutlierResponse extends ZiaConversionResponse {
    private final int condition;
    private final String conditionType;
    private final String value;
    private String formula = "$%s %s %s";  // no I18N
    private final boolean isBottom;
    private final List<ZiaRange> tableRange;

    public RowOutlierResponse(String columnName, int columnIndex, ZiaRange contentRange, List<ZiaRange> tableContentRange, String conditionType, int condition, String value, boolean isBottom, ZiaRange columnRange) {
        setColumnName(columnName);
        setColumnIndex(columnIndex);
        setContentRange(contentRange);
        setColumnRange(columnRange);
        this.conditionType = conditionType;
        this.condition = condition;
        this.value = value;
        this.isBottom = isBottom;
        this.tableRange = tableContentRange;
        createFormula(isBottom);
    }

    private void createFormula(boolean isBottom) {
        ZiaRange contentRange = getContentRange();
        String startCell = contentRange.getTopLeft();
        String operation = isBottom ? "<=" : ">=";
        formula = String.format(formula, startCell, operation, value);
    }

    @Override
    public JSONObject applyResponse() {
        JSONObject response = new JSONObject();
        response.put("type", ZiaConstants.ConversionMainType.classicCf.toString());
        response.put("subType", ZiaConstants.ConversionSubType.rowOutlier.toString());
        response.put("columnName", getColumnName());
        response.put("columnIndex", getColumnIndex());
        response.put("columnRange", getColumnRange().toString());
        //TODO: support multiple range
        response.put("contentRange", tableRange.get(0).toString());
        response.put("cr_ty", conditionType);
        response.put("cond", condition);
        response.put("formula", formula);
        response.put("isBottom", isBottom);
        return response;
    }

}
