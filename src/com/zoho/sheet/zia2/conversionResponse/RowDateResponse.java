package com.zoho.sheet.zia2.conversionResponse;

import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaRange;
import org.json.JSONObject;

import java.util.List;

/**
 * <AUTHOR> - 13900
 */

public class RowDateResponse extends ZiaConversionResponse {

    private String formula;
    private final String dateCount;
    private final String conditionType;
    private final Boolean isAfterDate;
    private final int condition;
    private final List<ZiaRange> tableRange;

    public RowDateResponse(String columnName, int columnIndex, String conditionType, int condition, ZiaRange contentRange, List<ZiaRange> tableContentRange, boolean isAfterDate, String dateCount, ZiaRange columnRange) {
        setColumnName(columnName);
        setColumnIndex(columnIndex);
        setContentRange(contentRange);
        createFormula(isAfterDate);
        setColumnRange(columnRange);
        this.conditionType = conditionType;
        this.condition = condition;
        this.dateCount = dateCount;
        this.isAfterDate = isAfterDate;
        this.tableRange = tableContentRange;
    }

    private void createFormula(boolean isAfterDate) {
        ZiaRange contentRange = getContentRange();
        String startCell = contentRange.getTopLeft();
        if (isAfterDate) {
            // NO I18N
            String afterDateFormula = "$%s > TODAY()";
            formula = String.format(afterDateFormula, startCell);
        } else {
            // NO I18N
            String beforeDateFormula = "$%s < TODAY()";
            formula = String.format(beforeDateFormula, startCell);
        }
    }

    @Override
    public JSONObject applyResponse() {
        JSONObject response = new JSONObject();
        response.put("type", ZiaConstants.ConversionMainType.classicCf.toString());
        response.put("subType", ZiaConstants.ConversionSubType.rowDate.toString());
        response.put("columnName", getColumnName());
        response.put("columnIndex", getColumnIndex());
        //TODO: support multi range
        response.put("contentRange", tableRange.get(0).toString());
        response.put("columnRange", getColumnRange().toString());
        response.put("cr_ty", conditionType);
        response.put("cond", condition);
        response.put("formula", formula);
        response.put("isAfterDate", isAfterDate);
        response.put("afterDateCount", dateCount);
        return response;
    }
}
