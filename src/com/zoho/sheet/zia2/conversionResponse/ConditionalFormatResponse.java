package com.zoho.sheet.zia2.conversionResponse;

import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.ZiaUtilities;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> - 13900
 */

public class ConditionalFormatResponse extends ZiaConversionResponse {
    int condition;
    String conditionType;
    String conditionValue;
    String subType;
    List<String> formula = new ArrayList<>();
    List<ZiaRange> tableRange;

    public ConditionalFormatResponse(String columnName, int columnIndex, ZiaRange contentRange, List<ZiaRange> tableContentRange, String conditionType, int condition, String conditionValue, String subtype, boolean improperData, ZiaRange columnRange) {
        setColumnName(columnName);
        setColumnIndex(columnIndex);
        setContentRange(contentRange);
        setColumnRange(columnRange);
        this.conditionType = conditionType;
        this.condition = condition;
        this.conditionValue = conditionValue;
        this.subType = subtype;
        this.tableRange = tableContentRange;
        if (improperData) {
            createNumFormula();
        }
        if (subtype.equals("cfSpecial")) {
            createCfFormula();
        }
        if (subtype.equals("cfNumSpecial")) {
            createNumericalSpecial();
        }
    }

    private void createNumericalSpecial() {
        ZiaRange contentRange = getContentRange();
        String startCell = contentRange.getTopLeft();
        String formulaTemplate = "$" + startCell + " %s " + "0";
        formula.add(String.format(formulaTemplate, ">="));
        formula.add(String.format(formulaTemplate, "<"));
    }

    private void createCfFormula() {
        ZiaRange contentRange = getContentRange();
        String startCell = contentRange.getTopLeft();
        String formulaTemplate = "$" + startCell + " = \"%s\"";
        String[] catNames = conditionValue.split("&%");
        for (String name : catNames) {
            formula.add(String.format(formulaTemplate, name));
        }
    }

    private void createNumFormula() {
        ZiaRange contentRange = getContentRange();
        String startCell = contentRange.getTopLeft();
        String formulaTemplate = "AND (%s)";   // No I18N
        String formulaCondition = "";
        String operator = condition == 3 ? ">" : "<";
        String numCheckFormula = ";TYPE($" + startCell + ")=1";  // No I18N
        String rangeFormula = "$" + startCell + operator + conditionValue;
        formulaCondition = rangeFormula + numCheckFormula;
        formula.add(String.format(formulaTemplate, formulaCondition));

    }

    @Override
    public JSONObject applyResponse() {
        JSONObject response = new JSONObject();
        response.put("type", ZiaConstants.ConversionMainType.classicCf.toString());
        response.put("columnName", getColumnName());
        response.put("subType", subType);
        response.put("columnIndex", getColumnIndex());
        //TODO: support multi range
        if (subType.equals("cfSpecial")) {
            response.put("contentRange", ZiaUtilities.toRangeString(tableRange));
        } else {
            response.put("contentRange", getContentRange().toString());
        }
        response.put("columnRange", getColumnRange().toString());
        if (!formula.isEmpty()) {
            response.put("cr_ty", ZiaConstants.CF_CONDITION_TYPE.FORMULA);
            response.put("cond", ZiaConstants.CF_CONDITION.FORMULA);
        } else {
            response.put("cr_ty", conditionType);
            response.put("cond", condition);
        }
        if (!formula.isEmpty()) {
            response.put("formula", formula);
        }
        response.put("cval", conditionValue);
        return response;
    }

}
