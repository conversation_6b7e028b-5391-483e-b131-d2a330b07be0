package com.zoho.sheet.zia2.conversionResponse;

import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaRange;
import org.json.JSONObject;

/**
 * <AUTHOR> - 13900
 */

public class CheckBoxResponse extends ZiaConversionResponse {
    public CheckBoxResponse(String columnName, int columnIndex, ZiaRange contentRange, ZiaRange columnRange) {
        setColumnName(columnName);
        setColumnIndex(columnIndex);
        setContentRange(contentRange);
        setColumnRange(columnRange);
    }

    @Override
    public JSONObject applyResponse() {
        JSONObject response = new JSONObject();
        response.put("columnName", getColumnName());
        response.put("columnIndex", getColumnIndex());
        response.put("contentRange", getContentRange().toString());
        response.put("columnRange", getColumnRange().toString());
        response.put("type", ZiaConstants.ConversionMainType.checkbox.toString());
        return response;
    }
}