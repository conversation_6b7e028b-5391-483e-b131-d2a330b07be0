package com.zoho.sheet.zia2.conversionResponse;

import com.zoho.sheet.zia2.utils.ConversionUtils;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaRange;
import org.json.JSONObject;

/**
 * <AUTHOR> - 13900
 */

public class MiscResponse extends ZiaConversionResponse {
    int condition;
    String conditionType;
    String formula = "%s %s %s"; // no I18N
    boolean isTop;
    int valueLimit;

    public MiscResponse(String columnName, int columnIndex, ZiaRange contentRange, String conditionType, int condition, boolean isTop, ZiaRange columnRange, int valueLimit) {
        setColumnName(columnName);
        setColumnIndex(columnIndex);
        setContentRange(contentRange);
        setColumnRange(columnRange);
        this.conditionType = conditionType;
        this.condition = condition;
        this.isTop = isTop;
        this.valueLimit = valueLimit;
        createFormula(isTop);
    }

    private void createFormula(boolean isTop) {
        ZiaRange columnRange = getContentRange();
        String startCell = columnRange.getTopLeft();
        int splitIndex = ConversionUtils.getColumnRangeSplitIndex(startCell);
        String topF = isTop ? " LARGE($%s$%S:$%s$%s;%s)" : " SMALL($%s$%S:$%s$%s;%s)"; // no I18N
        String colAlphabet = startCell.substring(0, splitIndex);
        int startRowIndex = columnRange.getStartRowIndex() + 1;
        int endRowIndex = columnRange.getEndRowIndex() + 1;
        topF = String.format(topF, colAlphabet, startRowIndex, colAlphabet, endRowIndex, valueLimit);
        String operation = isTop ? ">=" : "<=";
        formula = String.format(formula, startCell, operation, topF);
    }

    @Override
    public JSONObject applyResponse() {
        JSONObject response = new JSONObject();
        response.put("type", ZiaConstants.ConversionMainType.classicCf.toString());
        response.put("subType", ZiaConstants.ConversionSubType.misc.toString());
        response.put("columnName", getColumnName());
        response.put("columnIndex", getColumnIndex());
        response.put("contentRange", getContentRange().toString());
        response.put("columnRange", getColumnRange().toString());
        response.put("cr_ty", conditionType);
        response.put("cond", condition);
        response.put("formula", formula);
        response.put("isTop", isTop);
        response.put("valueLimit", valueLimit);
        return response;
    }
}
