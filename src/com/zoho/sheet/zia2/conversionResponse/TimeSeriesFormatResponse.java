package com.zoho.sheet.zia2.conversionResponse;

import com.zoho.sheet.zia2.utils.ConversionUtils;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaRange;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> - 13900
 */

public class TimeSeriesFormatResponse extends ZiaConversionResponse {
    int condition;
    String conditionType;
    String conditionValue;
    String dateCol;
    List<String> formula = new ArrayList<>();

    public TimeSeriesFormatResponse(String columnName, String dateCol, int columnIndex, ZiaRange contentRange, String conditionType, int condition, boolean improperData, boolean missingData, ZiaRange columnRange) {
        setColumnName(columnName);
        setColumnIndex(columnIndex);
        setContentRange(contentRange);
        setColumnRange(columnRange);
        this.conditionType = conditionType;
        this.condition = condition;
        this.dateCol = dateCol;

        List<String> timeSeriesFormula = createTimeSeriesFormula();
        String secondRange = contentRange.getTopLeft();
        String missingFormulaTemplate = "=AND(%s;ISBLANK($%s)=FALSE";   // No I18N
        String improperFormulaTemplate = "=AND(%s;TYPE($%S)=1)";    // No I18N

        for (String f : timeSeriesFormula) {
            if (missingData) {
                f = String.format(missingFormulaTemplate, f, secondRange);
            } else if (improperData) {
                f = String.format(improperFormulaTemplate, f, secondRange);
            }
            formula.add(f);
        }
    }

    private List<String> createTimeSeriesFormula() {
        // formula start index is one index minus content range - which denotes the header
        List<String> formulas = new ArrayList<>();
        ZiaRange contentRange = getContentRange();
        String startCell = contentRange.getTopLeft();
        int splitIndex = ConversionUtils.getColumnRangeSplitIndex(startCell);
        String colAlphabet = startCell.substring(0, splitIndex);
        int startRowIndex = getContentRange().getStartRowIndex();
        String firstRange = colAlphabet + startRowIndex;
        startRowIndex++;
        String secondRange = colAlphabet + startRowIndex;
        String formulaTemplate = firstRange + "%s" + secondRange;   // No I18N
        formulas.add(String.format(formulaTemplate, ">"));
        formulas.add(String.format(formulaTemplate, "<"));
        formulas.add(String.format(formulaTemplate, "="));
        return formulas;
    }

    public JSONObject applyResponse() {
        JSONObject response = new JSONObject();
        response.put("type", ZiaConstants.ConversionMainType.classicCf.toString());
        response.put("subType", ZiaConstants.ConversionSubType.timeSeries.toString());
        response.put("columnName", getColumnName());
        response.put("dateCol", dateCol);
        response.put("columnIndex", getColumnIndex());
        response.put("contentRange", getContentRange().toString());
        response.put("columnRange", getColumnRange().toString());
        response.put("cr_ty", conditionType);
        response.put("cond", condition);
        response.put("cval", "0");
        if (!formula.isEmpty()) {
            response.put("formula", formula);
        }
        return response;
    }
}
