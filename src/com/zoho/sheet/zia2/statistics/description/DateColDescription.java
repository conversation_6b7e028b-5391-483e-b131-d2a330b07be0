package com.zoho.sheet.zia2.statistics.description;

import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.statistics.ZiaStatUtils;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> - 13900
 */

public class DateColDescription extends ZiaColumnDescription {

    private long numberOfDays;
    private long numberOfWeeks;
    private long numberOfMonths;
    private long numberOfYears;
    private Date minDate;
    private Date maxDate;

    public DateColDescription(ZiaColumnDataHolder columnDataHolder) {
        super(columnDataHolder);
        findNumberOfDays(ZiaStatUtils.fetchDateVal(columnDataHolder));
    }

    private void findNumberOfDays(List<Date> dateValues) {
        Date minDate = Collections.min(dateValues);
        Date maxDate = Collections.max(dateValues);

        this.minDate = minDate;
        this.maxDate = maxDate;

        LocalDate minLocalDate = convertToLocalDate(minDate.toString());
        LocalDate maxLocalDate = convertToLocalDate(maxDate.toString());

        // Calculate the number of days between
        numberOfDays = ChronoUnit.DAYS.between(minLocalDate, maxLocalDate);
        numberOfWeeks = ChronoUnit.WEEKS.between(minLocalDate, maxLocalDate);
        numberOfMonths = ChronoUnit.MONTHS.between(minLocalDate, maxLocalDate);
        numberOfYears = ChronoUnit.YEARS.between(minLocalDate, maxLocalDate);
    }

    public static LocalDate convertToLocalDate(String dateStr) {
        DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                .parseCaseInsensitive()
                .appendPattern("EEE MMM dd HH:mm:ss zzz yyyy")  //NO I18N
                .toFormatter();

        ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateStr, formatter);
        return zonedDateTime.toLocalDate();
    }

    public long getNumberOfDays() {
        return numberOfDays;
    }

    public long getNumberOfWeeks() {
        return numberOfWeeks;
    }

    public long getNumberOfMonths() {
        return numberOfMonths;
    }

    public long getNumberOfYears() {
        return numberOfYears;
    }

    public Date getMinDate() {
        return minDate;
    }

    public Date getMaxDate() {
        return maxDate;
    }
}
