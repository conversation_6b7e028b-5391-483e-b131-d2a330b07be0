package com.zoho.sheet.zia2.statistics.components;

/**
 * <AUTHOR> - 13900
 */

public abstract class AbstractNumericalStats {
    public double[] columnValues1 = null;
    public double[] columnValues2 = null;
    public double result;

    public AbstractNumericalStats(double[] columnValues1) {
        this.columnValues1 = columnValues1;
    }

    public AbstractNumericalStats(double[] columnValues1, double[] columnValues2) {
        this.columnValues1 = columnValues1;
        this.columnValues2 = columnValues2;
    }

    abstract void executeStat();

    public double getResult() {
        return this.result;
    }
}
