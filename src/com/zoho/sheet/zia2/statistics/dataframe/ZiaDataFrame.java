package com.zoho.sheet.zia2.statistics.dataframe;

import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.statistics.ZiaStatUtils;
import com.zoho.sheet.zia2.statistics.correlation.CorrConfig;
import com.zoho.sheet.zia2.utils.ZiaConstants;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> - 13900
 */

public class ZiaDataFrame {

    private final List<ZiaColFrame> numericalCols = new ArrayList<>();
    private final List<ZiaColFrame> categoricalCols = new ArrayList<>();
    private final List<ZiaColFrame> dateCols = new ArrayList<>();

    private Set<Integer> priorityCols = new HashSet<>();

    public ZiaDataFrame(List<ZiaColumnDataHolder> columnDataHolderList, CorrConfig config) {
        extractDataColumns(columnDataHolderList, config);
    }

    public ZiaDataFrame(List<ZiaColumnDataHolder> columnDataHolderList, CorrConfig config, List<Integer> priorityCols) {
        extractDataColumns(columnDataHolderList, config);
        this.priorityCols = new HashSet<>(priorityCols);
    }

    private void extractDataColumns(List<ZiaColumnDataHolder> columnDataHolderList, CorrConfig config) {
        for (ZiaColumnDataHolder columnDataHolder : columnDataHolderList) {
            ZiaConstants.ColumnType columnType = columnDataHolder.getColumnType();

            switch (columnType) {
                case NUMERICAL:
                    numericalCols.add(makeNumericalFrame(columnDataHolder));
                    break;
                case CATEGORICAL:
                    categoricalCols.add(makeStrColFrame(columnDataHolder, config, false));
                    break;
                case DATE:
                    dateCols.add(makeStrColFrame(columnDataHolder, config, true));
                    break;
            }
        }
    }

    public Set<Integer> getPriorityCols() {
        return priorityCols;
    }

    public List<ZiaColFrame> getNumericalCols() {
        return numericalCols;
    }

    public List<ZiaColFrame> getCategoricalCols() {
        return categoricalCols;
    }

    public List<ZiaColFrame> getDateCols() {
        return dateCols;
    }

    private NumColFrame makeNumericalFrame(ZiaColumnDataHolder columnDataHolder) {
        double[] numericalData = ZiaStatUtils.fetchNumericalData(columnDataHolder);
        return new NumColFrame(columnDataHolder.getHeaderName(), columnDataHolder.getColumnIndex(), numericalData);
    }

    private ZiaColFrame makeStrColFrame(ZiaColumnDataHolder columnDataHolder, CorrConfig config, boolean isDateCol) {

        if (config.getCategoryMethod() != ZiaConstants.CATEGORICAL_CORR.CRAMER_V) {
            double[] colData = (double[]) ZiaStatUtils.fetchStringData(columnDataHolder, isDateCol, true);
            return new NumColFrame(columnDataHolder.getHeaderName(), columnDataHolder.getColumnIndex(), colData);
        } else {
            String[] colData = (String[]) ZiaStatUtils.fetchStringData(columnDataHolder, isDateCol, true);
            return new CatColFrame(columnDataHolder.getHeaderName(), columnDataHolder.getColumnIndex(), colData);
        }
    }
}
