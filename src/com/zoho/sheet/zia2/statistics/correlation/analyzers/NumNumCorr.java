package com.zoho.sheet.zia2.statistics.correlation.analyzers;

import com.zoho.sheet.zia2.statistics.correlation.CorrConfig;
import com.zoho.sheet.zia2.statistics.correlation.CorrUtils;
import com.zoho.sheet.zia2.statistics.dataframe.NumColFrame;
import com.zoho.sheet.zia2.statistics.dataframe.ZiaDataFrame;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ziabeans.conveyor.ZiaCombinationVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - 13900
 */

public class NumNumCorr implements ICorrelation {

    private final ZiaConstants.NUMERICAL_CORR method;
    private final List<ZiaCombinationVO> relationMatrix = new ArrayList<>();
    private final List<NumColFrame> numericalCols;
    private final Set<Integer> priorityCols;

    public NumNumCorr(ZiaDataFrame df, CorrConfig config) {
        this.numericalCols = df.getNumericalCols().stream().map((numCol)->(NumColFrame) numCol).collect(Collectors.toList());
        this.priorityCols = df.getPriorityCols();
        this.method = config.getNumericalMethod();
        startAnalysis();
    }

    @Override
    public List<ZiaCombinationVO> getRelation() {
        return CorrUtils.sortInDescendingAbsValue(this.relationMatrix, this.priorityCols);
    }

    private void startAnalysis() {
        for (int col1 = 0; col1 < numericalCols.size(); col1++) {
            for (int col2 = col1 + 1; col2 < numericalCols.size(); col2++) {
                NumColFrame numColFrame1 = numericalCols.get(col1);
                NumColFrame numColFrame2 = numericalCols.get(col2);

                double corrVal = CorrUtils.calculateCorrelation(numColFrame1.getValueArr(), numColFrame2.getValueArr(), this.method);
                relationMatrix.add(CorrUtils.makeComboVO(numColFrame1, numColFrame2, corrVal));
            }
        }
    }
}