package com.zoho.sheet.zia2.statistics.correlation.analyzers;

import com.zoho.sheet.zia2.statistics.correlation.CorrConfig;
import com.zoho.sheet.zia2.statistics.correlation.CorrUtils;
import com.zoho.sheet.zia2.statistics.dataframe.NumColFrame;
import com.zoho.sheet.zia2.statistics.dataframe.ZiaColFrame;
import com.zoho.sheet.zia2.statistics.dataframe.ZiaDataFrame;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ziabeans.conveyor.ZiaCombinationVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - 13900
 */

public class DateNumCorr implements ICorrelation {

    private final List<ZiaCombinationVO> relationMatrix = new ArrayList<>();
    private final List<NumColFrame> numericalCols;
    private final List<ZiaColFrame> dateCols;
    private final Set<Integer> priorityCols;

    public DateNumCorr(ZiaDataFrame df, CorrConfig corrConfig) {
        this.numericalCols = df.getNumericalCols().stream().map((numCol) -> (NumColFrame) numCol).collect(Collectors.toList());
        this.priorityCols = df.getPriorityCols();
        this.dateCols = df.getDateCols();

        if (corrConfig.getCategoryMethod().equals(ZiaConstants.CATEGORICAL_CORR.DEFAULT)) {
            doDefaultAnalysis();
        } else {
            doCramerVAnalysis();
        }
    }

    private void doDefaultAnalysis() {
        for (ZiaColFrame dateColFrame : dateCols) {
            for (NumColFrame numColFrame : numericalCols) {
                double[] encodedArr = ((NumColFrame) dateColFrame).getValueArr();
                double[] numValArr = numColFrame.getValueArr();
                double corrVal = CorrUtils.calculateCorrelation(encodedArr, numValArr, null);

                relationMatrix.add(CorrUtils.makeComboVO(dateColFrame, numColFrame, corrVal));
            }
        }
    }

    private void doCramerVAnalysis() {
        //TODO: implement cramer when needed
    }

    @Override
    public List<ZiaCombinationVO> getRelation() {
        return CorrUtils.sortInDescendingAbsValue(this.relationMatrix, this.priorityCols);
    }
}
