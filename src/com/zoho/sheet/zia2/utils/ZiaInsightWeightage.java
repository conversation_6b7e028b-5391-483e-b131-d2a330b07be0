package com.zoho.sheet.zia2.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class ZiaInsightWeightage {

    public enum Operation {
        NUM_NUM,
        NUM_HIST,
        NUM_LINE,
        CAT_NUM_NORMAL,
        CAT_NUM_SUM_1D,
        CAT_NUM_SUM_2D,
        CAT_NUM_SUM_1D_FILTER,
        CAT_NUM_SUM_2D_FILTER,
        CAT_NUM_TOP_FILTER,
        CAT_NUM_AVG,
        CAT_CAT_COUNT_DISTINCT,
        WORDCLOUD,
        COMBO_CHART,
        CAT_COUNT,
        DATE_NUM_SUM,
        DATE_MORE_NUM,
        DATE_CAT_COUNT,
        DATE_NUM_2D
    }

    private static final Map<Operation, Integer> WEIGHTAGE_MAP = new HashMap<>();
    private static final Map<Operation, Integer> WEIGHTAGE_FOR_VERY_SMALL_DATASET = new HashMap<>();

    static {
        WEIGHTAGE_MAP.put(Operation.CAT_NUM_SUM_1D, 100);
        WEIGHTAGE_MAP.put(Operation.CAT_NUM_SUM_2D, 90);
        WEIGHTAGE_MAP.put(Operation.CAT_NUM_AVG, 80);
        WEIGHTAGE_MAP.put(Operation.DATE_NUM_SUM, 80);
        WEIGHTAGE_MAP.put(Operation.CAT_NUM_SUM_1D_FILTER, 75);
        WEIGHTAGE_MAP.put(Operation.CAT_NUM_SUM_2D_FILTER, 75);
        WEIGHTAGE_MAP.put(Operation.CAT_NUM_TOP_FILTER, 70);
        WEIGHTAGE_MAP.put(Operation.DATE_NUM_2D, 70);
        WEIGHTAGE_MAP.put(Operation.CAT_COUNT, 65);
        WEIGHTAGE_MAP.put(Operation.CAT_CAT_COUNT_DISTINCT, 65);
        WEIGHTAGE_MAP.put(Operation.COMBO_CHART, 65);
        WEIGHTAGE_MAP.put(Operation.WORDCLOUD, 60);
        WEIGHTAGE_MAP.put(Operation.CAT_NUM_NORMAL, 60);
        WEIGHTAGE_MAP.put(Operation.DATE_CAT_COUNT, 60);
        WEIGHTAGE_MAP.put(Operation.NUM_HIST, 60);
        WEIGHTAGE_MAP.put(Operation.NUM_LINE, 60);
        WEIGHTAGE_MAP.put(Operation.DATE_MORE_NUM, 55);
        WEIGHTAGE_MAP.put(Operation.NUM_NUM, 50);

        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.COMBO_CHART, 110);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.CAT_NUM_NORMAL, 100);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.NUM_LINE, 100);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.NUM_HIST, 100);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.CAT_NUM_TOP_FILTER, 70);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.CAT_NUM_SUM_1D_FILTER, 60);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.CAT_NUM_SUM_2D_FILTER, 60);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.CAT_NUM_SUM_1D, 50);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.DATE_NUM_2D, 45);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.CAT_NUM_SUM_2D, 40);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.DATE_NUM_SUM, 40);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.DATE_CAT_COUNT, 40);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.CAT_CAT_COUNT_DISTINCT, 40);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.WORDCLOUD, 40);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.DATE_MORE_NUM, 35);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.CAT_NUM_AVG, 30);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.CAT_COUNT, 30);
        WEIGHTAGE_FOR_VERY_SMALL_DATASET.put(Operation.NUM_NUM, 30);
    }

    public static int getWeightage(ZiaConstants.DataSetSize dataSetSize, Operation operation) {
        if (dataSetSize == ZiaConstants.DataSetSize.VERY_SMALL) {
            return WEIGHTAGE_FOR_VERY_SMALL_DATASET.getOrDefault(operation, 30);
        } else {
            return WEIGHTAGE_MAP.getOrDefault(operation, 50);
        }
    }
}
