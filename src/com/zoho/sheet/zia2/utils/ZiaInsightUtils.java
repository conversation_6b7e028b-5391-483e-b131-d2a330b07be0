package com.zoho.sheet.zia2.utils;

import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaColumnIterator;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightConstraints;

import java.sql.Timestamp;
import java.time.Year;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zoho.sheet.zia2.utils.ZiaConstants.*;

/**
 * <AUTHOR>
 */

public class ZiaInsightUtils {

    private static final Logger LOGGER = Logger.getLogger(ZiaInsightUtils.class.getName());

    public static String getTitle(String operationNameOrPrefix, List<String> categorical, List<String> values) {
        return getTitle(operationNameOrPrefix, categorical, values, false, false);
    }

    public static String getTitle(String operationNameOrPrefix, List<String> categorical, List<String> values, boolean includeVersus, boolean ignoreOfKey) {
        StringBuilder builder = new StringBuilder(operationNameOrPrefix);

        StringBuilder cat = new StringBuilder();
        for (int j = 0, size = categorical.size(); j < size; j++) {
            String i = categorical.get(j);
            cat.append(cat.length() == 0 && builder.length() != 0 ? " by " : " ");   //NO I18N

            cat.append(i);
            if (j < size - 1 && cat.length() != 0) {
                cat.append(",");
            }
        }

        StringBuilder num = new StringBuilder();
        if (includeVersus && categorical.isEmpty() && operationNameOrPrefix.isEmpty()) {
            num.append(values.get(0));
            num.append(" vs "); //NO I18N
            num.append(values.get(1));
        } else {
            for (int j = 0, size = values.size(); j < size; j++) {
                String i = values.get(j);
                num.append(!ignoreOfKey && operationNameOrPrefix.length() != 0 && num.length() == 0 ? " of " : " ");  //NO I18N

                num.append(i);
                if (j < size - 1 && num.length() != 0) {
                    num.append(",");
                }
            }
        }

        String title = builder.append(num).append(cat).toString();
        return title.strip();
    }

    public static List<String> getColumnHeadersFromRanges(List<Integer> rangeList, ZiaTableDataHolder tableDataHolder) {
        List<String> list = new ArrayList<>();
        for (int index : rangeList) {
            ZiaColumnDataHolder columnDataHolder = tableDataHolder.getColumnDataHolderByIndex(index);
            list.add(columnDataHolder.getHeaderName());
        }
        return list;
    }

    public static List<Integer> getColumnIndicesByHeader(Collection<String> columnHeader, ZiaTableDataHolder tableDataHolder) {
        List<Integer> indices = new ArrayList<>();

        for (String s : columnHeader) {
            ZiaColumnDataHolder colDataHolder = tableDataHolder.getColumnDataHolderByHeader(s);
            if (Objects.nonNull(colDataHolder)) {
                indices.add(colDataHolder.getColumnIndex());
            }
        }

        return indices;
    }

    public static String convertRangesToRangeString(List<Integer> rangeList, ZiaTableDataHolder tableDataHolder, boolean isSheetNameRequired) {
        StringBuilder str = new StringBuilder();
        for (int index : rangeList) {
            if (str.length() != 0) {
                str.append(";");
            }
            ZiaColumnDataHolder columnDataHolder = tableDataHolder.getColumnDataHolderByIndex(index);

            StringBuilder currRangeLabel = new StringBuilder();
            List<ZiaRange> range = columnDataHolder.getColumnRange();
            for (int i = 0; i < range.size(); i++) {
                if (i != 0) {
                    currRangeLabel.append(";");
                }

                ZiaRange columnRange = range.get(i);
                String rangeLabel = isSheetNameRequired ? columnRange.getFullRangeLabel() : columnRange.toString();
                currRangeLabel.append(rangeLabel);
            }

            str.append(currRangeLabel);
        }

        return str.toString();
    }

    public static String getRangeStringForColumns(Collection<String> columnHeaders, ZiaTableDataHolder tableDataHolder, boolean isContentRange) {
        return getRangeStringForColumns(columnHeaders, tableDataHolder, isContentRange, false);
    }

    public static String getRangeStringForColumns(Collection<String> columnHeaders, ZiaTableDataHolder tableDataHolder, boolean isContentRange, boolean isSheetNameRequired) {
        StringBuilder str = new StringBuilder();
        for (String colHeader : columnHeaders) {
            if (str.length() != 0) {
                str.append(";");
            }

            ZiaColumnDataHolder columnDataHolder = tableDataHolder.getColumnDataHolderByHeader(colHeader);
            List<ZiaRange> columnRangeList = isContentRange ? columnDataHolder.getColumnContentRange() : columnDataHolder.getColumnRange();

            for (int i = 0; i < columnRangeList.size(); i++) {
                if (i != 0) {
                    str.append(";");
                }

                ZiaRange columnRange = columnRangeList.get(i);
                String rangeLabel = isSheetNameRequired ? columnRange.getFullRangeLabel() : columnRange.toString();
                str.append(rangeLabel);
            }

        }

        return str.toString();
    }

    public static String getContentRangeStringForColumns(Collection<Integer> colIndices, ZiaTableDataHolder tableDataHolder) {
        StringBuilder str = new StringBuilder();
        for (Integer colIndex : colIndices) {
            if (str.length() != 0) {
                str.append(";");
            }

            ZiaColumnDataHolder columnDataHolder = tableDataHolder.getColumnDataHolderByIndex(colIndex);
            List<ZiaRange> columnRangeList = columnDataHolder.getColumnContentRange();

            for (int i = 0; i < columnRangeList.size(); i++) {
                if (i != 0) {
                    str.append(";");
                }

                ZiaRange columnRange = columnRangeList.get(i);
                str.append(columnRange.toString());
            }
        }

        return str.toString();
    }

    public static String createUniqueID(ZiaParams ziaParams) {
        List<ZiaRange> tableRange = ziaParams.getTableRanges();
        String timeStamp = new Timestamp(System.currentTimeMillis()).toString();
        return timeStamp + ":" + tableRange.toString();
    }

    public static boolean isSuitableForTop5FilterAgg(int uniqueValuesCount, InsightConstraints constraints) {
        return uniqueValuesCount > 5 && uniqueValuesCount <= constraints.getUniqueValuesRequiredForTop5Agg();
    }

    public static boolean isSuitableForTop5Filtering(int uniqueValuesCount, InsightConstraints constraints) {
        return uniqueValuesCount > 5 && uniqueValuesCount >= constraints.getUniqueValuesRequiredForTop5Conversion();
    }

    public static boolean isValidForHistogram(int size) {
        return (long) Math.ceil(Math.log(size)) + 1 > 1;
    }

    public static List<Integer> getCatDistribution(ZiaColumnDataHolder columnDataHolder) {
        List<Integer> distributionValues = new ArrayList<>();
        Map<String, Set<Integer>> uniqueValueIndicesMap = columnDataHolder.getUniqueValueIndicesMap();
        for (Map.Entry<String, Set<Integer>> element : uniqueValueIndicesMap.entrySet()) {
            distributionValues.add(element.getValue().size());
        }
        return distributionValues;
    }

    public static <K, V extends Comparable<? super V>> LinkedHashMap<K, V> sortMapByValue(Map<K, V> map, int limit, boolean isDescOrder) {
        Comparator<V> order = isDescOrder ? Comparator.reverseOrder() : Comparator.naturalOrder();
        Stream<Map.Entry<K, V>> sorted = map.entrySet().stream().sorted(Map.Entry.comparingByValue(order));

        if (limit != -1) {
            sorted = sorted.limit(limit);
        }

        return sorted.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
    }

    public static ZiaDateAggregationType getChartGrouping(Date minDate, Date maxDate) {
        Calendar minCal = new GregorianCalendar();
        minCal.setTime(minDate);
        Calendar maxCal = new GregorianCalendar();
        maxCal.setTime(maxDate);

        int yrsBtw = maxCal.get(Calendar.YEAR) - minCal.get(Calendar.YEAR);
        int monBtw = Math.abs(maxCal.get(Calendar.MONTH) - minCal.get(Calendar.MONTH) + 1);
        int weekBtw = maxCal.get(Calendar.WEEK_OF_MONTH) - minCal.get(Calendar.WEEK_OF_MONTH) + 1;
        int daysBetween = maxCal.get(Calendar.DAY_OF_YEAR) - minCal.get(Calendar.DAY_OF_YEAR) + 1;

        if (yrsBtw == 1) {
            return ZiaDateAggregationType.MONTH_BY_YEAR;
        } else if (yrsBtw == 2) {
            return ZiaDateAggregationType.QUARTER_BY_YEAR;
        } else if (yrsBtw > 2) {
            return ZiaDateAggregationType.YEAR;
        } else {
            if(monBtw == 2){
                return ZiaDateAggregationType.MONTH;
            }
            else if(monBtw >= 3){
                return ZiaDateAggregationType.QUARTER;
            }
            else{
                if (weekBtw >= 2 && daysBetween > 12) {
                    return ZiaDateAggregationType.DAY_OF_WEEK;
                } else if (daysBetween > 10) {
                    return ZiaDateAggregationType.DAY;
                }
            }
        }

        return ZiaDateAggregationType.NONE;
    }

    public static int getUniquesValuesForDays(long noOfDays) {
        long type;
        if (noOfDays < 10) {
            type = noOfDays;
        } else if (noOfDays < 30) {
            type = noOfDays / 7;        //Converting to weeks
        } else if (noOfDays < 366) {
            type = noOfDays / 30;        //Converting to months
        } else if (noOfDays < 1464) {
            type = 4;                //Converting half-yearly n yearly for 2 years to 4 years
        } else {
            type = (int) Math.ceil((double) noOfDays / 365);    //Converting to years
        }
        return Math.toIntExact(type);
    }

    public static String getGroupingTitle(ZiaDateAggregationType groupingConst) {
        switch (groupingConst) {
            case YEAR:
                return "Yearly";        //NO I18N
            case MONTH:
                return "Monthly";       //NO I18N
            case QUARTER:
                return "Quarterly";     //NO I18N
            case DAY_OF_WEEK:
                return "Weekly";        //NO I18N
            case DAY:
                return "Day-to-day";    //No I18N
            case MONTH_BY_YEAR:
                return "Month-wise Yearly";   //No I18N
            case QUARTER_BY_YEAR:
                return "Quarter-wise Yearly"; //No I18N
            default:
                return "";
        }
    }

    public static boolean checkYearContent(ZiaColumnDataHolder columnDataHolder) {
        ZiaColumnIterator columnIterator = columnDataHolder.getColumnIterator();

        int invalidYearCount = 0;
        int rowCount = 0;
        while (columnIterator.hasNext() && rowCount <= ZiaConstants.NER_CHECK_ROWCOUNT) {
            ZiaContentVO contentVO = columnIterator.next();
            rowCount++;

            if (Objects.isNull(contentVO)) {
                continue;
            }

            String displayValue = contentVO.getDisplayValue();

            try {
                Double.parseDouble(displayValue);   // checking presence of string values in year columns
                if (displayValue.length() != 4) {
                    invalidYearCount++;
                }
            } catch (NumberFormatException ex) {
                invalidYearCount++;
            }
        }
        double ratio = (double) invalidYearCount / ZiaConstants.NER_CHECK_ROWCOUNT;
        return ratio <= ZiaConstants.MISC_COL_RATIO;
    }

    public static boolean checkYearColumns(String colName, ZiaHolder holder, int maxErrorValue) {
        ZiaTableDataHolder tableDataHolder = holder.getTableDataHolder();
        int rowSize = tableDataHolder.getContentRowSize();
        int maxErrorRate = rowSize / maxErrorValue;
        Map<String, Set<Integer>> columnValues = tableDataHolder.getColumnDataHolderByHeader(colName).getUniqueValueIndicesMap();

        return isAYearColumn(rowSize, maxErrorRate, columnValues);
    }

    public static boolean checkYearColumns(int rowSize, int maxErrorValue, Map<String, Set<Integer>> uniqueColumnValues) {
        int maxErrorRate = rowSize / maxErrorValue;
        return isAYearColumn(rowSize, maxErrorRate, uniqueColumnValues);
    }

    private static boolean isAYearColumn(int rowSize, int maxErrorValue, Map<String, Set<Integer>> uniqueColumnValues) {
        int maxErrorRate = rowSize / maxErrorValue;
        int year = Year.now().getValue();
        int minYear = year - 80;
        int maxYear = year + 20;
        int wrongYears = 0;
        for (Map.Entry<String, Set<Integer>> value : uniqueColumnValues.entrySet()) {
            if (!checkValidYear(value.getKey(), minYear, maxYear)) {
                wrongYears += value.getValue().size();
            }
        }
        return wrongYears < maxErrorRate;
    }

    private static boolean checkValidYear(String key, int minYear, int maxYear) {
        try {
            double yearValue = Double.parseDouble(key);
            return yearValue >= minYear && yearValue <= maxYear;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean isLogScaleReq(List<Integer> numCols, ZiaTableDataHolder tableDataHolder) {
        List<Double> meanValues = new ArrayList<>();
        int rowSize = tableDataHolder.getContentRowSize();
        for (int colIndex : numCols) {
            ZiaColumnDataHolder numColHolder = tableDataHolder.getColumnDataHolderByIndex(colIndex);
            Map<String, Set<Integer>> numColValues = numColHolder.getUniqueValueIndicesMap();
            double colSum = 0;
            for (Map.Entry<String, Set<Integer>> cell : numColValues.entrySet()) {
                try {
                    double cellValue = Double.parseDouble(cell.getKey());
                    colSum += cellValue * cell.getValue().size();
                } catch (NumberFormatException e) {
                    LOGGER.log(Level.INFO, "Exception occurred in semi date insight >>> ", e);
                }
            }
            meanValues.add(colSum / rowSize);
        }
        return checkDeviation(meanValues);
    }

    private static boolean checkDeviation(List<Double> meanValues) {
        Collections.sort(meanValues);
        int meanSize = meanValues.size();
        double median;
        if (meanSize % 2 == 0) {
            int index1 = (meanSize / 2) - 1;
            int index2 = meanSize / 2;
            median = meanValues.get(index1) + meanValues.get(index2);
            median /= 2;
        } else {
            int index1 = meanSize / 2;
            median = meanValues.get(index1);
        }
        for (double mean : meanValues) {
            double deviation = mean / median * 100;
            if (deviation < 15 || deviation > 300) {
                return true;
            }
        }
        return false;
    }

    public static boolean isIrrelevantColumn(String columnName) {
        for (String name : columnName.split(" ")) {
            name = ZiaInsightUtils.removeBomCharacter(name);
            if (IRRELEVANT_COLUMNS.contains(name.toUpperCase())) {
                return true;
            }
        }

        for (String name : columnName.split("_")) {
            name = ZiaInsightUtils.removeBomCharacter(name);
            if (IRRELEVANT_COLUMNS.contains(name.toUpperCase())) {
                return true;
            }
        }

        return IRRELEVANT_COLUMNS.contains(columnName.toUpperCase());  //if whole name matches
    }

    public static boolean isStatAppliedCol(String columnName) {
        for (String name : columnName.split(" ")) {
            name = ZiaInsightUtils.removeBomCharacter(name);
            if (STAT_WORDS.contains(name.toUpperCase())) {
                return true;
            }
        }

        for (String name : columnName.split("_")) {
            name = ZiaInsightUtils.removeBomCharacter(name);
            if (STAT_WORDS.contains(name.toUpperCase())) {
                return true;
            }
        }

        return STAT_WORDS.contains(columnName.toUpperCase());  //if whole name matches
    }

    public static boolean containsCalenderWordingInHeader(String columnName) {
        for (String name : columnName.split(" ")) {
            name = ZiaInsightUtils.removeBomCharacter(name);
            if (CALENDAR_WORDINGS.contains(name.toUpperCase())) {
                return true;
            }
        }
        return false;
    }

    public static boolean isYearColumn(String columnName) {
        for (String name : columnName.split(" ")) {
            name = ZiaInsightUtils.removeBomCharacter(name);
            if (YEAR_COLS.contains(name.toUpperCase())) {
                return true;
            }
        }
        return false;
    }

    public static String removeBomCharacter(String input) {
        String bomchar = "\uFEFF";  //No I18N

        if (input.startsWith(bomchar)) {
            input = input.substring(1);
        }
        return input;
    }

}
