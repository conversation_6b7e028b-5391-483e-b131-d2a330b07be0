package com.zoho.sheet.zia2.utils.ziabeans.conversions;

import com.zoho.sheet.zia2.conversionResponse.ZiaConversionResponse;
import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.utils.ZiaRange;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;

/**
 * <AUTHOR> - 13900
 */

public class ConversionHolder {
    private final List<ZiaColumnDataHolder> dataColumns;
    private int rowSize;
    private List<ZiaColumnDataHolder> numericalCols;
    private List<ZiaColumnDataHolder> categoricalCols;
    private List<ZiaColumnDataHolder> dateCols;
    private List<ZiaColumnDataHolder> timeCols;
    private final Queue<ZiaConversionResponse> rowDateQueue = new ArrayDeque<>();
    private final Queue<ZiaConversionResponse> rowIdQueue = new ArrayDeque<>();
    private final Queue<ZiaConversionResponse> outlierResponseQueue = new ArrayDeque<>();
    private final Queue<ZiaConversionResponse> timeSeriesResponseQueue = new ArrayDeque<>();
    private final Queue<ZiaConversionResponse> skewnessResponseQueue = new ArrayDeque<>();
    private final Queue<ZiaConversionResponse> conditonalFormatResponseQueue = new ArrayDeque<>();
    private final Queue<ZiaConversionResponse> colorscaleResponseQueue = new ArrayDeque<>();
    private final Queue<ZiaConversionResponse> dataBarResponseQueue = new ArrayDeque<>();
    private final Queue<ZiaConversionResponse> iconSetResponseQueue = new ArrayDeque<>();
    private final Queue<ZiaConversionResponse> miscResponseQueue = new ArrayDeque<>();
    private List<Integer> rankings = new ArrayList<>();

    public ConversionHolder(List<ZiaColumnDataHolder> dataColumns, int rowSize) {
        this.dataColumns = dataColumns;
        this.rowSize = rowSize;
        this.numericalCols = new ArrayList<>();
        this.categoricalCols = new ArrayList<>();
        this.dateCols = new ArrayList<>();
        this.timeCols = new ArrayList<>();
    }

    public List<ZiaColumnDataHolder> getDataColumns() {
        return dataColumns;
    }

    public void removeColumn(ZiaRange dataRange) {
        for (int i = 0; i < dataColumns.size(); i++) {
            if (dataColumns.get(i).getColumnRange().contains(dataRange)) {
                dataColumns.remove(i);
                break;
            }
        }
    }

    public void removeNumericalColumn(ZiaRange dataRange) {
        for (int i = 0; i < numericalCols.size(); i++) {
            if (numericalCols.get(i).getColumnRange().contains(dataRange)) {
                numericalCols.remove(i);
                break;
            }
        }
    }

    public void removeCategoricalColumn(ZiaRange dataRange) {
        for (int i = 0; i < categoricalCols.size(); i++) {
            if (categoricalCols.get(i).getColumnRange().contains(dataRange)) {
                categoricalCols.remove(i);
                break;
            }
        }
    }

    public void removeDateColumn(ZiaRange dataRange) {
        for (int i = 0; i < dateCols.size(); i++) {
            if (dateCols.get(i).getColumnRange().contains(dataRange)) {
                dateCols.remove(i);
                break;
            }
        }
    }

    public int getRowSize() {
        return rowSize;
    }

    public void setRowSize(int rowSize) {
        this.rowSize = rowSize;
    }

    public List<ZiaColumnDataHolder> getNumericalCols() {
        return numericalCols;
    }

    public void setNumericalCols(List<ZiaColumnDataHolder> numericalCols) {
        this.numericalCols = numericalCols;
    }

    public List<ZiaColumnDataHolder> getCategoricalCols() {
        return categoricalCols;
    }

    public void setCategoricalCols(List<ZiaColumnDataHolder> categoricalCols) {
        this.categoricalCols = categoricalCols;
    }

    public List<ZiaColumnDataHolder> getDateCols() {
        return dateCols;
    }

    public void setDateCols(List<ZiaColumnDataHolder> dateCols) {
        this.dateCols = dateCols;
    }

    public List<ZiaColumnDataHolder> getTimeCols() {
        return timeCols;
    }

    public void setTimeCols(List<ZiaColumnDataHolder> timeCols) {
        this.timeCols = timeCols;
    }

    public List<Queue<ZiaConversionResponse>> getResponseQueues() {
        /*
         * order of insertion in response list determines the order of conversion in segregation
         * changes in order may change the priority order in insight segregation
         */
        List<Queue<ZiaConversionResponse>> responseList = new ArrayList<>();
        responseList.add(this.timeSeriesResponseQueue);
        responseList.add(this.rowIdQueue);
        responseList.add(this.outlierResponseQueue);
        responseList.add(this.rowDateQueue);
        responseList.add(this.skewnessResponseQueue);
        responseList.add(this.conditonalFormatResponseQueue);
        responseList.add(this.colorscaleResponseQueue);
        responseList.add(this.dataBarResponseQueue);
        responseList.add(this.iconSetResponseQueue);
        responseList.add(this.miscResponseQueue);

        return responseList;
    }

    public Queue<ZiaConversionResponse> getRowDateQueue() {
        return this.rowDateQueue;
    }

    public Queue<ZiaConversionResponse> getColorscaleResponseQueue() {
        return colorscaleResponseQueue;
    }

    public Queue<ZiaConversionResponse> getConditonalFormatResponseQueue() {
        return conditonalFormatResponseQueue;
    }

    public Queue<ZiaConversionResponse> getIconSetResponseQueue() {
        return iconSetResponseQueue;
    }

    public Queue<ZiaConversionResponse> getMiscResponseQueue() {
        return miscResponseQueue;
    }

    public Queue<ZiaConversionResponse> getOutlierResponseQueue() {
        return outlierResponseQueue;
    }

    public Queue<ZiaConversionResponse> getRowIdQueue() {
        return rowIdQueue;
    }

    public Queue<ZiaConversionResponse> getSkewnessResponseQueue() {
        return skewnessResponseQueue;
    }

    public Queue<ZiaConversionResponse> getTimeSeriesResponseQueue() {
        return timeSeriesResponseQueue;
    }

    public Queue<ZiaConversionResponse> getDataBarResponseQueue() {
        return dataBarResponseQueue;
    }

    public List<Integer> getRankings() {
        return rankings;
    }

    public void setRankings(List<Integer> rankings) {
        this.rankings = rankings;
    }
}
