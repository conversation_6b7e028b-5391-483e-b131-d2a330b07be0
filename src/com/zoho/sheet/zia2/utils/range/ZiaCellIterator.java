package com.zoho.sheet.zia2.utils.range;

import com.zoho.sheet.zia2.utils.ZiaRange;

import java.util.Iterator;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public class ZiaCellIterator implements Iterator<ZiaCell> {
    private final ZiaRange zRange;
    private int currRowIndex;
    private int currColIndex;
    private ZiaCell startCell = null;
    private VerticalDirection verticalDirection = VerticalDirection.TOP_BOTTOM;
    private HorizontalDirection horizontalDirection = HorizontalDirection.LEFT_RIGHT;

    public enum HorizontalDirection {LEFT_RIGHT, RIGHT_LEFT}

    public enum VerticalDirection {TOP_BOTTOM, BOTTOM_TOP}

    public ZiaCellIterator(ZiaRange range) {
        this.zRange = range;
        this.currRowIndex = range.getStartRowIndex();
        this.currColIndex = range.getStartColIndex();
    }

    public ZiaCellIterator(ZiaRange range, VerticalDirection verticalDirection) {
        this(range);
        this.verticalDirection = verticalDirection;
    }

    public ZiaCellIterator(ZiaRange range, VerticalDirection verticalDirection, HorizontalDirection horizontalDirection) {
        this(range, verticalDirection);
        this.horizontalDirection = horizontalDirection;
    }

    @Override
    public boolean hasNext() {
        ZiaCell currentCell = new ZiaCell(currRowIndex, currColIndex);
        return Objects.isNull(startCell) || !currentCell.equals(startCell);
    }

    @Override
    public ZiaCell next() {
        ZiaCell ziaCell = new ZiaCell(currRowIndex, currColIndex);
        if (Objects.isNull(startCell)) {
            startCell = ziaCell;
        }
        iterateVerticalDirection();
        iterateHorizontalDirection();
        return ziaCell;
    }

    private void iterateHorizontalDirection() {
        switch (this.horizontalDirection) {
            case LEFT_RIGHT:
                this.currColIndex++;
                if (this.currColIndex > this.zRange.getEndColIndex()) {
                    this.currColIndex = this.zRange.getStartColIndex();
                }
                break;
            case RIGHT_LEFT:
                this.currColIndex--;
                if (this.currColIndex < this.zRange.getStartColIndex()) {
                    this.currColIndex = this.zRange.getEndColIndex();
                }
                break;
        }
    }

    private void iterateVerticalDirection() {
        switch (this.verticalDirection) {
            case TOP_BOTTOM:
                this.currRowIndex++;
                if (this.currRowIndex > this.zRange.getEndRowIndex()) {
                    this.currRowIndex = this.zRange.getStartRowIndex();
                }
                break;
            case BOTTOM_TOP:
                this.currRowIndex--;
                if (this.currRowIndex < this.zRange.getStartRowIndex()) {
                    this.currRowIndex = this.zRange.getEndRowIndex();
                }
                break;
        }
    }
}
