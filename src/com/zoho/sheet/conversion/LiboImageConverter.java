//$Id$
package com.zoho.sheet.conversion;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.sun.star.awt.Size;
import com.sun.star.beans.PropertyValue;
import com.sun.star.beans.PropertyVetoException;
import com.sun.star.beans.UnknownPropertyException;
import com.sun.star.beans.XPropertySet;
import com.sun.star.container.XNameContainer;
import com.sun.star.document.XExporter;
import com.sun.star.document.XFilter;
import com.sun.star.drawing.XDrawPage;
import com.sun.star.drawing.XDrawPageSupplier;
import com.sun.star.drawing.XShape;
import com.sun.star.frame.XComponentLoader;
import com.sun.star.frame.XModel;
import com.sun.star.io.BufferSizeExceededException;
import com.sun.star.io.IOException;
import com.sun.star.lang.IllegalArgumentException;
import com.sun.star.lang.WrappedTargetException;
import com.sun.star.lang.XComponent;
import com.sun.star.lang.XMultiServiceFactory;
import com.sun.star.sheet.XSpreadsheet;
import com.sun.star.sheet.XSpreadsheetDocument;
import com.sun.star.uno.AnyConverter;
import com.sun.star.uno.UnoRuntime;
//import com.zoho.sheet.conversion.ConnectionPool;
//import com.zoho.sheet.util.OOConnectionPool;
import com.zoho.sheet.util.OOOutputStream;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * This Converter can be used to Convert Unsupported Images to '.jpg'. <br>
 * This Converter uses Apache OpenOffice APIs.<br>
 * This Converter is used by XLSXParser to convert '.emf' images to '.jpg'
 *
 * <AUTHOR>
 *
 */
public class LiboImageConverter {

    public static class LiboImageConverterHelper {

        private String name;
        private String extension = "";
        private byte[] bytes;
        private int height = 1024;
        private int width = 1024;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        /**
         *
         * @return e.g: .jpg, .png
         */
        public String getExtension() {
            return extension;
        }

        /**
         *
         * @param extension e.g: .emf, .jpg
         */
        public void setExtension(String extension) {
            this.extension = extension;
        }

        public byte[] getBytes() {
            return bytes;
        }

        public void setBytes(byte[] bytes) {
            this.bytes = bytes;
        }

        public int getHeight() {
            return height;
        }

        public void setHeight(int height) {
            this.height = height;
        }

        public int getWidth() {
            return width;
        }

        public void setWidth(int width) {
            this.width = width;
        }

    }

    private final Logger logger = Logger.getLogger(LiboImageConverter.class.getCanonicalName());
    private final String liboTempDir = EnginePropertyUtil.getSheetPropertyValue("LIBOImportLocation");//No I18N

//    private ConnectionPool.ConnectionObject connectionObject;
    private String odsFilePath;

    /**
     *
     * @param connectionObject
     * @param odsFilePath empty '.ods' file with only single sheet named as 'Sheet1'
     * 
     */
//    public LiboImageConverter(ConnectionPool.ConnectionObject connectionObject, String odsFilePath) {
//        this.connectionObject = connectionObject;
//        this.odsFilePath = odsFilePath;
//    }

    /**
     * converts the images and sets new:- imageExtn and bytes to
     * LiboImageConverterHelper
     *
     * @param images
     */
//    public void convertImages(List<LiboImageConverterHelper> images) throws Exception {
//
//        if (images == null || images.isEmpty()) {
//            logger.log(Level.OFF, "[LIBOIMAGECONVERTER] no-image to convert");
//            return;
//        }
//
//        long time = System.currentTimeMillis();
//
//        String liboServerOdsFilePath = liboTempDir + "/temOds" + System.currentTimeMillis() + ".ods";//No I18N
//
//        XComponentLoader officXComponentLoader = connectionObject.getxComponentLoader();
//        XComponent calcXComponent = null;
//
//        try(InputStream odsInputStream = new FileInputStream(new File(this.odsFilePath))) {
//            calcXComponent = getCalcXComponent(officXComponentLoader, odsInputStream, liboServerOdsFilePath);//this is the actual documet at libo-server
//            insertAndConvertImages(calcXComponent, images);
//            ImportExportUtil.deleteAtLIBOServer(connectionObject.getLiboHost(), liboServerOdsFilePath);
//
//            logger.log(Level.OFF, "[LIBOIMAGECONVERTER][TIME-STAMP] :{0}ms , no-of-images-converted:{1}", new Object[]{(System.currentTimeMillis() - time), images.size()});
//        } catch (Exception exception) {
//            Exception ex = new Exception("[LIBOIMAGECONVERTER-EXCEPTION]Exception while image-convertion, Cann't convert Any-Image", exception);
//            throw ex;
//        } finally {
//            ImportExportUtil.closeComponent(calcXComponent);
//        }
//
//    }

//    private XComponent getCalcXComponent(XComponentLoader officXComponentLoader,InputStream odsInputStream, String liboSrcFilePath) throws Exception {
//
//        PropertyValue[] propertyValue = new PropertyValue[2];
//        propertyValue[0] = new com.sun.star.beans.PropertyValue();
//        propertyValue[0].Name = "Hidden";//No I18N
//        propertyValue[0].Value = true;
//
//        final String saveAtLIBOServer = ImportExportUtil.saveAtLIBOServer(connectionObject.getLiboHost(), liboSrcFilePath, odsInputStream);
//
//        XComponent calcXComponent = officXComponentLoader.loadComponentFromURL("file://" +saveAtLIBOServer+File.separator+liboSrcFilePath, "_blank", 0, propertyValue);//No I18N
//
//        return calcXComponent;
//    }

//    private void insertAndConvertImages(XComponent calcXComponent, List<LiboImageConverterHelper> images) throws Exception {
//
//        XSpreadsheetDocument xSpreadsheetDocument = UnoRuntime.queryInterface(XSpreadsheetDocument.class, calcXComponent);
//
//        XDrawPage xDrawPage = getXDrawPage(xSpreadsheetDocument);
//
//        XModel spreadSheetDocXModel = UnoRuntime.queryInterface(XModel.class, xSpreadsheetDocument);
//
//        XMultiServiceFactory spreadSheetXMultiServiceFactory = UnoRuntime.queryInterface(XMultiServiceFactory.class, spreadSheetDocXModel);
//
//        XNameContainer bitmapTableXNameContainer = UnoRuntime.queryInterface(XNameContainer.class, spreadSheetXMultiServiceFactory.createInstance("com.sun.star.drawing.BitmapTable"));
//
//        XExporter graphicXExporter = getGrapphicXExporter();
//
//        for (LiboImageConverterHelper image : images) {
//
//            String lIBOFileName = liboTempDir + "/timage" + System.currentTimeMillis() + image.getExtension();//No I18N
//            String imgID = "temImageId" + System.currentTimeMillis();//No I18N
//
//            try {
//
//                if (image.getBytes() == null || image.getBytes().length == 0) {
//                    throw new Exception("Cann't convert image with empty-bytes");
//                }
//
//                com.sun.star.awt.Size size = new com.sun.star.awt.Size();
//                size.Height = image.getHeight();
//                size.Width = image.getWidth();
//
//                try (InputStream imageInputStream = new ByteArrayInputStream(image.getBytes()); OOOutputStream oos = new OOOutputStream()) {
//
//                    String LIBOHome = ImportExportUtil.saveAtLIBOServer(connectionObject.getLiboHost(), lIBOFileName, imageInputStream);
//
//                    Object imgTemp = spreadSheetXMultiServiceFactory.createInstance("com.sun.star.drawing.GraphicObjectShape");
//                    XShape xImage = UnoRuntime.queryInterface(XShape.class, imgTemp);
//
//                    String sFilePath = "file://" + LIBOHome + File.separator + lIBOFileName;//No I18N
//
//                    bitmapTableXNameContainer.insertByName(imgID, sFilePath);
//
//                    setXshapeProperties(xImage, sFilePath, lIBOFileName, imgID, size);
//
//                    xDrawPage.add(xImage);
//
//
//                    metaFileFormatToPNG(graphicXExporter, xImage, oos);
//
//                    image.setExtension(".png");
//                    image.setBytes(oos.toByteArray());
//                }
//            } catch (Exception exception) {
//                logger.log(Level.WARNING, "[LIBOIMAGECONVERTER-EXCEPTION] image-details:: name:{0}, extension:{1}", new Object[]{image.getName(), image.getExtension()});
//                logger.log(Level.SEVERE, "[LIBOIMAGECONVERTER-EXCEPTION]", exception);
//            } finally {
//                bitmapTableXNameContainer.removeByName(imgID);
//                ImportExportUtil.deleteAtLIBOServer(connectionObject.getLiboHost(), lIBOFileName);
//            }
//        }
//    }

    private String getInternalUrl(XNameContainer xBitmapContainer, String sFilePath, String imgID) throws com.sun.star.uno.Exception, java.lang.Exception {
        xBitmapContainer.insertByName(imgID, sFilePath);
return sFilePath;
//        String internalURL = AnyConverter.toString(xBitmapContainer.getByName(imgID));
//
//        return internalURL;

    }

    private void setXshapeProperties(XShape xImage, String internalURL, String sFilePath, String imgID, Size imgSize) throws UnknownPropertyException, PropertyVetoException, IllegalArgumentException, WrappedTargetException {

        XPropertySet xImgPropSet = UnoRuntime.queryInterface(XPropertySet.class, xImage);
        xImgPropSet.setPropertyValue("AnchorType", com.sun.star.text.TextContentAnchorType.AS_CHARACTER);

        xImgPropSet.setPropertyValue("GraphicURL", internalURL);

        xImage.setSize(imgSize);

        xImgPropSet.setPropertyValue("Title", sFilePath);
        xImgPropSet.setPropertyValue("Name", imgID);

    }

    private void metaFileFormatToPNG(XExporter xExporter, XShape xShape, OOOutputStream oos) throws BufferSizeExceededException, IOException {
        XComponent xComp = UnoRuntime.queryInterface(XComponent.class, xShape);
        xExporter.setSourceDocument(xComp);

        XFilter xFilter = UnoRuntime.queryInterface(XFilter.class, xExporter);

        PropertyValue aProps[] = new PropertyValue[3];
        aProps[0] = new PropertyValue();
        aProps[0].Name = "MediaType"; // No I18N
        aProps[0].Value = "image/png"; // No I18N

        aProps[1] = new PropertyValue();
        aProps[1].Name = "OutputStream"; // No I18N
        aProps[1].Value = oos;

        aProps[2] = new PropertyValue();

        PropertyValue[] filterData = new PropertyValue[1];
        filterData[0] = new PropertyValue();
        filterData[0].Name = "Quality"; // No I18N
        filterData[0].Value = "100%"; // No I18N

        aProps[2].Name = "FilterData"; // No I18N
        aProps[2].Value = filterData;

        xFilter.filter(aProps);

        oos.closeOutput();
    }

    private XDrawPage getXDrawPage(XSpreadsheetDocument xSpreadsheetDocument) throws Exception {
        XSpreadsheet xSpreadsheet = UnoRuntime.queryInterface(XSpreadsheet.class, xSpreadsheetDocument.getSheets().getByName("Sheet1"));
        XDrawPageSupplier xDrawPageSupplier = UnoRuntime.queryInterface(XDrawPageSupplier.class, xSpreadsheet);
        XDrawPage xDrawPage = xDrawPageSupplier.getDrawPage();
        return xDrawPage;
    }

//    private XExporter getGrapphicXExporter() throws Exception {
//    	String sName= "" ;
//		if(ConversionServer.isDistributedModelEnabled) {
//			sName= connectionObject.getsName();
//		} else {
//			sName = connectionObject.getBridgeNo();
//		}
//        Object graphicExportFilter = ConnectionPool.createService(sName, "com.sun.star.drawing.GraphicExportFilter"); // No I18N
//        XExporter graphicXExporter = UnoRuntime.queryInterface(XExporter.class, graphicExportFilter);
//        return graphicXExporter;
//    }
    
}
