/* $Id$ */

package com.zoho.sheet.conversion;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook.PatternSetting;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.StoreFactory;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import com.sun.star.lang.XServiceInfo;
import com.sun.star.sheet.XSpreadsheetDocument;
import com.sun.star.uno.UnoRuntime;
import com.zoho.sheet.action.FileReader;
import com.zoho.sheet.util.AsposeUtils;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ZSStats;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;

import java.io.OutputStream;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;

/**
 * <AUTHOR>
 * New Libo Importer By Mini Server
 * TODO : Remove going to mini server for ODS
 * Called for XLS, and ODS
 */

public class LiboImporterMiniServer extends Importer {

	//	private XComponent xComponent = null;
	//	private XSpreadsheetDocument xSpreadsheetDocument = null;
	//private ConnectionObject connectionObject = null;
	//private XComponent xComponent = null;
	//XSpreadsheetDocument xSpreadsheetDocument = null;
	private PatternSetting patternSetting = PatternSetting.WILDCARD;

	public int importDocument(FileReader fr, RemoteBook remoteBook,boolean isDBUpdateReq,WorkbookContainer container) throws Exception{
		logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Importing RemoteBook::");
		try{
			int updateResult = updateDocument(isDBUpdateReq);
			if(updateResult!=ZFSNGConstants.CONVERSION_SUCCESS){
				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] FAILURE::"+updateResult);
			}
			return updateResult;
		}
		catch (Exception e) {
			logger.log(Level.WARNING, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Error in Importing RemoteBook::",e);
			return ZFSNGConstants.CONVERSION_FAILURE;
			// TODO: handle exception
		}
	}
	public int importDocument() throws Exception{
		logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Importing SheetDocument::");
		try{
			int updateResult = updateDocument(true);
			if(updateResult!=ZFSNGConstants.CONVERSION_SUCCESS){
				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] FAILURE::"+updateResult);
			}
			return updateResult;
		}catch (Exception e) {
			logger.log(Level.WARNING, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Error in Importing SheetDocument::",e);
			return ZFSNGConstants.CONVERSION_FAILURE;
			// TODO: handle exception
		}
		
	}

	
	/*
	 * called for both remote and auth document import*/
	private int updateDocument(boolean isDBUpdateReq) throws Exception{

		//XComponent xComponent = null;
		//XSpreadsheetDocument xSpreadsheetDocument = null;
		WorkbookContainer chunkContainer = null;
		String activesheet = null;
//		boolean updatePublishRange = false;
		long t1 = System.currentTimeMillis();
		try{
			//PreImport Start 
			int preImportsu = preImport();
			if(preImportsu == ZFSNGConstants.CONVERSION_FAILURE){
				ZSStats.increment(ZSStats.IMPORTERROR);
				logger.log(Level.INFO, "ERROR_ON_IMPORT");
				return preImportsu;
			}else if((preImportsu == ZFSNGConstants.CONVERSION_FAILURE_PASS_PROTECTED)){
				ZSStats.increment(ZSStats.IMPORTERROR);
				logger.log(Level.INFO, "PASSWORD Protected");
				return preImportsu;
			}
			long t2 = System.currentTimeMillis();
			//		        Write the file contents in DB
			//		        Read publish DB 
			//		        Create chunkcontainer 
			//		        Delete Publish DB

			//If document Id in null then its a new import and we need to add the DB entries and get a new id,
			//else updating the contents of an existing workbook.

			if(docType == Constants.REMOTE_DOCUMENT){
				if(docName.contains(format)){
					docName = docName.substring(0, docName.indexOf("."));
				}
				if(isDBUpdateReq) {

					updateRemoteDB(remoteBook, docType);
				}
			}
			else if (docType == Constants.SHEET_DOCUMENT || docType == Constants.GDRIVE_DOCUMENT ){
				if (docId == null) {
					docId = DocumentUtils.addNewDocumentToDB(docOwner, docName, importAsTemplate, docType, true, resourceId)[0];
				}
				else{
					logger.log(Level.INFO, "Version Upload in Docs");
					isVersionUpload = true;
				}
				//temporary container to store DocID,DocOwner name etc
				chunkContainer = getChunkContainer();
				this.zuId = chunkContainer.getDocOwnerZUID();
				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] CHUNKCONTAINER CREATED::"+chunkContainer.getResourceId());
				//Read current publish range database
//				if(isVersionUpload){
//					logger.log(Level.INFO, "Retaining publish range,external web data ,review comments on version import");
//					EngineUtils1.clearFragmentsFile(chunkContainer);
//					EngineUtils.removeSheetMetaInfo(Long.valueOf(chunkContainer.getDocOwnerZUID()), docOwner, Long.valueOf(docId));
//				     handleSheetSpecificResources(ImportExportUtil.getDocumentSheetsDO(chunkContainer.getDocOwner(), Long.parseLong(this.docId)), Arrays.asList(xSpreadsheetDocument.getSheets().getElementNames()), chunkContainer.getDocOwner());
//
//				}
				
			}

			//			write sub DB entries(image, charts, buttons)
			long t3 = System.currentTimeMillis();
			if(docType == Constants.REMOTE_DOCUMENT && remoteBook.isNewContentImport()){
				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Existing Remote Document, No new sub DB Entries");
			}
			else if(docType == Constants.REMOTE_DOCUMENT && remoteBook.isExistingDoc()){
				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] CollabEdit Existing Remote Document, No new sub DB Entries");
			}
			long t4 = System.currentTimeMillis();
			if(docType == Constants.REMOTE_DOCUMENT && !remoteBook.isExistingDoc()){
				Store store = null;
				long ownerZUID = Long.parseLong(zuId);
				long remoteBookDocId = Long.parseLong(docId);
				if (ownerZUID == -1){
					store = StoreFactory.getInstance().getDocumentStore(docOwner, remoteBookDocId);
				} else {
					store = StoreFactory.getInstance().getDocumentStore(ownerZUID, docOwner, remoteBookDocId);
				}
				SheetFileInfo sheetfileInfo = store.createFileInfo(remoteBookDocId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ODS);
				long resId = sheetfileInfo.getResourceId();
				HashMap<String, Object> writeInfo=store.write(resId, sheetfileInfo);
				OutputStream os=(OutputStream) writeInfo.get("OS");
				byte[] convertedBytes = this.contentBytes;
				//boolean ISASPOSECONVERSIONENABLED = EngineConstants.ISASPOSECONVERSIONENABLED;
				//boolean ISLIBOMINISERVERENABLED = EngineConstants.ISASPOSECONVERSIONENABLED;
				boolean ISASPOSECONVERSIONODSENABLED = EngineConstants.ISASPOSEODSCONVERSIONENABLED;
	        	//if(ISASPOSECONVERSIONENABLED) {
	        		if(ISASPOSECONVERSIONODSENABLED) {
	        			convertedBytes = AsposeUtils.getSheetAsposeConversion(this.contentBytes, this.format, EngineConstants.FILEEXTN_ODS);
	        			if(convertedBytes == null) {
	        				logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Aspose Conversion Failed for REMOTE:"+resourceId);//No I18N
	        				return ZFSNGConstants.CONVERSION_FAILURE;
	        			}
	        		}
	        	//}
//	        	else if(ISLIBOMINISERVERENABLED){
//	        		convertedBytes = SheetConversion.getConvertedBytes(this.contentBytes, this.format, EngineConstants.FILEEXTN_ODS, true);
//	        	}
	        	os.write(convertedBytes);
				store.finishWrite(writeInfo);

//				RemoteUtils.saveToAPIStore(Long.parseLong(zuId), docOwner, Long.parseLong(docId), docName, xSpreadsheetDocument, true);

				int isFileSizeSupported = ImportExportUtil.isFileSizeSupported(null, null, Long.valueOf(zuId), Long.valueOf(docId), null, docOwner);
				if (isFileSizeSupported != ZFSNGConstants.CONVERSION_SUCCESS) {
					ZSStats.increment(ZSStats.IMPORTERROR);
					logger.log(Level.INFO, "ERROR_ON_IMPORT");
					logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] File size not Supported Error:"+resourceId+":"+docOwner+isFileSizeSupported);//No I18N
					return isFileSizeSupported;
				}
			}
			else if(docType == Constants.REMOTE_DOCUMENT && remoteBook.isExistingDoc()){
				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] CollabEdit Existing Remote Document, No need to save");
			}
			else{
				Store store=StoreFactory.getInstance().getDocumentStore(Long.parseLong(chunkContainer.getDocOwnerZUID()), chunkContainer.getDocOwner(), Long.valueOf(this.docId));
				SheetFileInfo sheetfileInfo=store.getFileInfo(Long.valueOf(this.docId), EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ODS);
				if(sheetfileInfo == null){
					sheetfileInfo = store.createFileInfo(Long.valueOf(this.docId), EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ODS); // Assumed that, it is the first write ~mani
				}
				long resId = sheetfileInfo.getResourceId();
				HashMap<String, Object> writeInfo=store.write(resId, sheetfileInfo);
				OutputStream os=(OutputStream) writeInfo.get("OS");
				byte[] convertedBytes = this.contentBytes;
				//boolean ISASPOSECONVERSIONENABLED = EngineConstants.ISASPOSECONVERSIONENABLED;
				boolean ISASPOSECONVERSIONODSENABLED = EngineConstants.ISASPOSEODSCONVERSIONENABLED;
				//boolean ISLIBOMINISERVERENABLED = EngineConstants.ISASPOSECONVERSIONENABLED;
	        	//if(ISASPOSECONVERSIONENABLED) {
	        		if(ISASPOSECONVERSIONODSENABLED) {
	        			convertedBytes = AsposeUtils.getSheetAsposeConversion(this.contentBytes, this.format, EngineConstants.FILEEXTN_ODS);
	        			if(convertedBytes == null) {
	        				logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Aspose Conversion Failed for Sheet:"+resourceId);//No I18N
	        				return ZFSNGConstants.CONVERSION_FAILURE;
	        			}
	        		}
	        	//}
//	        	else if(ISLIBOMINISERVERENABLED){
//	        		convertedBytes = SheetConversion.getConvertedBytes(this.contentBytes, this.format, EngineConstants.FILEEXTN_ODS, true);
//	        	}
				os.write(convertedBytes);
				store.finishWrite(writeInfo);

//				boolean docSavedStatus = ImportExportUtil.saveDocument(chunkContainer, docName, docId + "", xSpreadsheetDocument, activesheet, null, null, false, null, false);

//				if (!docSavedStatus) {
//					logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Document not saved.");//No I18N
//					ZSStats.increment(ZSStats.IMPORTERROR);
//					logger.log(Level.INFO, "ERROR_ON_IMPORT");
//					return ZFSNGConstants.CONVERSION_FAILURE;
//				} else {
					//Review : check this for remote cases
					//add for remote Documents as well

					int isFileSizeSupported = ImportExportUtil.isFileSizeSupported(null, null, Long.valueOf(zuId), Long.valueOf(docId), null, docOwner);
					if (isFileSizeSupported != ZFSNGConstants.CONVERSION_SUCCESS) {
						logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] File size not Supported Error:"+resourceId+":"+docOwner+isFileSizeSupported);//No I18N
						ZSStats.increment(ZSStats.IMPORTERROR);
						logger.log(Level.INFO, "ERROR_ON_IMPORT");
						return isFileSizeSupported;
					}
//				}

				boolean postImp = postImport(chunkContainer);
				if(postImp == false){
					ZSStats.increment(ZSStats.IMPORTERROR);
					logger.log(Level.INFO, "ERROR_ON_IMPORT");
					return ZFSNGConstants.CONVERSION_FAILURE;
				}
			}
			long t5 = System.currentTimeMillis();
			logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Success "+resourceId+"."+docName+":Importer Total time:"+TimeUnit.MILLISECONDS.toSeconds(t5-t1)+"Seconds:PreImport "+TimeUnit.MILLISECONDS.toSeconds(t2-t1)+" Seconds:"+"Seconds:DB Updates "+TimeUnit.MILLISECONDS.toSeconds(t3-t2)+"Seconds:Images/Charts "+TimeUnit.MILLISECONDS.toSeconds(t4-t3)+"Seconds:Saving Workbook "+TimeUnit.MILLISECONDS.toSeconds(t5-t4));
		}
		/*catch (com.sun.star.lang.DisposedException e) {                                         //works from Patch 1
			//TODO : Have to check the below method (Skeleton method)
			OOConnectionPool.NullifyRemoteContext(null);
			logger.log(Level.WARNING, null, e);
			ClientUtils.logException("docid", "", docOwner, "ImportDocument", "", "Unable to ImportDocument.", e);        //No I18N
			//            logger.info("chunkContainer: "+chunkContainer+" documentId:"+documentId+" docId: "+docId);
			if (chunkContainer != null && docId != null) {
				DocumentUtils.deleteDocumentEntry(chunkContainer, "fromImport", null);//No I18N
			}
			ZSStats.incrementByCustomValue(ZSStats.IMPORTERROR);
			logger.log(Level.INFO, "ERROR_ON_IMPORT");
			return ZFSNGConstants.CONVERSION_FAILURE;
		} */catch (Exception ee) {
			logger.log(Level.WARNING,"Exception during import, import failed", ee);
			ClientUtils.logException("docid", "", docOwner, "ImportDocument", "", "Unable to ImportDocument.", ee);        //No I18N
			// logger.info("chunkContainer: "+chunkContainer+" documentId:"+documentId+" docId: "+docId);
			if (chunkContainer != null && docId != null) {
				DocumentUtils.deleteDocumentEntry(chunkContainer, "fromImport",  null);//No I18N
			}
			ZSStats.incrementByCustomValue(ZSStats.IMPORTERROR);
			logger.log(Level.INFO, "ERROR_ON_IMPORT");
			return ZFSNGConstants.CONVERSION_FAILURE;
			
		}
		ZSStats.increment(ZSStats.IMPORT_SUCCESS);
		ZSStats.incrementByGroup(contentBytes.length, ZSStats.IMPORT_SIZE_RANGE_VALUE_ARRAY, ZSStats.IMPORT_SIZE_RANGE_STATS_ARRAY);
		return ZFSNGConstants.CONVERSION_SUCCESS;

	}
	

	private int preImport() throws Exception{
		//XSpreadsheetDocument xSpreadsheetDocument = null;
	//	ConversionServer cs = new ConversionServer();
		try {
			
		if(docType == Constants.REMOTE_DOCUMENT && remoteBook.isExistingDoc()){
			logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Existing Remote Document, Only DB updates:"+resourceId+":"+docOwner);
		}
		else{

			// Following special chars are replaced in document name - so as to be conssistent across - sheet / writer / show
			docName = docName.replaceAll(Constants.docNameDisallowedCharsRegex, "-");
			//TODO: Moved the checks to Importer itself.
//			if (docType != Constants.REMOTE_DOCUMENT)
//			{
//				long Allowed_File_Size = (Integer.parseInt(Constants.ALLOWED_FILE_SIZE) * ((long) Math.pow(2, 20)));
//				if (contentBytes.length > Allowed_File_Size) {
//					logger.log(Level.INFO,"[CONVERSION:IMPORT] File size not Supported Error:"+format);
//					return ZFSNGConstants.CONVERSION_FAILURE_DOCUMENT_SIZE_TOO_LARGE;
//				}
//			}
			String liboFileName = resourceId + (new SecureRandom().nextInt() * 10000000) + UUID.randomUUID().toString();
			if(!liboFileName.contains(format)){
				liboFileName += "." +format;  // No I18N
			}
			//create XComponent
//			xComponent = createXComponent(liboFileName);
			//create XSpreadsheetDocument
			
//			xSpreadsheetDocument = getXSpreadsheetDocument(xComponent);
			
			
			if(docType == Constants.REMOTE_DOCUMENT && remoteBook.allowEmptyFileContent()){
				//Setting 'sheet name' based on locale only on parsing empty.ods
//				if(super.isEmptyFileUsed){
//					logger.info("Using empty.ods for parsing>>>>");
//					ImportExportUtil.setSheetNameBasedOnLocale(super.lang,xSpreadsheetDocument);
//				}
				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] This Remote Document allows empty contants so set console name");
			}
			//			else{
			//				ImportExportUtil.replaceSpecialCharactersinSheetNames(xSpreadsheetDocument);
			//
			//				for csv and tsv sheet name is same as the temp libo file name so trimming the temporary cherectors.
			//				if(format.equalsIgnoreCase("csv") || format.equalsIgnoreCase("tsv")){
			//					ImportExportUtil.replaceLiboNameinSheetNames(xSpreadsheetDocument, docName, liboFileName, resourceId, docName);
			//				}
			//			}

			//for csv and tsv sheet name is same as the temp libo file name so trimming the temporary cherectors.
//			if(format.equalsIgnoreCase("csv") || format.equalsIgnoreCase("tsv")){
//				ImportExportUtil.replaceLiboNameinSheetNames(xSpreadsheetDocument, liboFileName, resourceId, docName);
//			}
			//Replacing Special Characters from the Sheet Names 
			//Review : if we can allow the special cherectors
			//We didn't have this check for remote documents before.
//			ImportExportUtil.replaceSpecialCharactersinSheetNames(xSpreadsheetDocument);
		}
		//Writing the Document Locale TODO: Remove the logs, once stable
			if(docType != Constants.REMOTE_DOCUMENT){
				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Writing Document Locale:");
				DocumentUtils.writeDocumentLocale(zuId, resourceId , lang, countrycode, patternSetting);
			}
			//logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] RETRY:::"+doRetry);
//		if(!doRetry) {
//			logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Retrying SUCCESS:"+this.connectionObject.getsName());
//			ZSStats.increment(ZSStats.IMPORT_RETRY_SUCCESS);
//		}
//		else {
//			logger.info("Conversion:Import,Status::SUCCESS without RETRY"+this.connectionObject.getsName());
//		}
//		if(ConversionServer.isDistributedModelEnabled) {
//
//			cs.handleConcurrentFailure(this.connectionObject.getsName(),"success");//No I18N
//
//		}
		return ZFSNGConstants.CONVERSION_SUCCESS;
	}catch (Exception ee) {
		logger.log(Level.WARNING,"PROBLEM IN LIBO IMPORTER",ee);
		boolean isEnable = EnginePropertyUtil.getSheetPropertyValue("CONVERSION_SERVER_ENABLE").equals("true");//No I18N
		if("CouldNotCretateXSpreadsheetDocument".equalsIgnoreCase(ee.getMessage())){
			return ZFSNGConstants.CONVERSION_FAILURE;
		}
		if(ee.getMessage().contains("type detection aborted")){
			logger.log(Level.INFO,"[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] If Exception is from Docs sync then either corrept or hidden sheet:"+ee.getMessage());
			return ZFSNGConstants.CONVERSION_FAILURE;
		}
		if(doRetry && isEnable && !"CouldNotCretateXSpreadsheetDocument".equalsIgnoreCase(ee.getMessage())) {
			doRetry = false;
			try {
//				logger.log(Level.INFO,"[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Retrying:");
//				ZSStats.increment(ZSStats.IMPORT_RETRY);
//				if(ConversionServer.isDistributedModelEnabled) {
//
//					cs.handleConcurrentFailure(this.connectionObject.getsName(),"failure");//No I18N
//				}
//			this.connectionObject = ConnectionPool.getRemoteDesktop();
			
			return preImport();
			}
			catch(Exception e) {
				logger.info("Conversion:Import,Status::FAILURE after RETRY");
				ZSStats.increment(ZSStats.IMPORT_RETRY_FAILURE);
//				if(ConversionServer.isDistributedModelEnabled) {
//					cs.handleConcurrentFailure(this.connectionObject.getsName(),"failure");//No I18N
//				}
				throw e;
			}
		}else {
			logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Import Failure:"+ee);
			throw ee;
		}
		
		
	}
		
	}
	//check later and move as common
	private boolean postImport(WorkbookContainer chunkContainer) throws Exception{
		try{

			// Check to stop creating thumbnail if it is already created - thumbnail will create once in Constants.holdontime

			                        
//                        long actionListSize = RedisHelper.hlen(RedisHelper.ACTIONS_LIST + chunkContainer.getResourceKey());
//                        long size = actionListSize > 0 ? actionListSize : RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + chunkContainer.getResourceKey());
//                        List<String> xList = RedisHelper.lrange(RedisHelper.EXECUTED_ACTIONS_LIST+chunkContainer.getResourceKey(), 0, -1);

                        chunkContainer.getWorkbook(null);
                        chunkContainer.getImageBook(null);
                        EngineUtils1.saveAsNewZSheetFragments(chunkContainer, false);
                        
                        List<JSONObjectWrapper> xList = RedisHelper.loadActionsList(chunkContainer.getResourceKey(), -1, chunkContainer.getExecutedActionId());
                        
			EngineUtils1.versionDocument(chunkContainer, "MANUAL", "imported-version", docOwner, zfsngVersionId, xList, true, null, null, null);                                                    //No I18N
		
			if(isRename){
				String newDocName = docName.substring(0,docName.lastIndexOf("."));
				ZohoFS.renameResource(zuId, resourceId, newDocName,importedByZuid);
			}
			
			if("true".equals(EnginePropertyUtil.getSheetPropertyValue("EnableThumbNail")) && ImportExportUtil.checkThumbnailHoldOnTime(chunkContainer)){
				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] CHECKING THUMBNAILS:"+chunkContainer.getResourceId());
//				InputStream fis = ImportExportUtil.getFileInputStream(chunkContainer, null);
//				if(fis!=null){
//					ImportExportUtil.generateThumbnail(fis, this.zuId, resourceId);
//				}

                                EngineUtils1.createThumbNail(chunkContainer);
			}
		}
		catch(Exception e){
			logger.log(Level.WARNING, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] could not complete postImport:"+resourceId+":"+docOwner, e);
		}
		return true;
	}

	//	private int updateDocument1() throws Exception{
	//
	//		XComponent xComponent = null;
	//		XSpreadsheetDocument xSpreadsheetDocument = null;
	//		WorkbookContainer chunkContainer = null;
	//		String activesheet = null;
	//		boolean updatePublishRange = false;
	//
	//		try{
	//			if(docType == Constants.REMOTE_DOCUMENT && remoteBook.isExistingDoc()){
	//				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Existing Remote Document, Only DB updates");
	//			}
	//			else{
	//
	//				docName = docName.replaceAll(Constants.docNameDisallowedCharsRegex, "-");
	//				long Allowed_File_Size = (Integer.parseInt(Constants.ALLOWED_FILE_SIZE) * ((long) Math.pow(2, 20)));
	//				if (contentBytes.length > Allowed_File_Size) {
	//					return ZFSNGConstants.CONVERSION_FAILURE;
	//				}
	//
	//				//create XComponent
	//				xComponent = getSpreadsheetDocComponent(null, format);
	//
	//				//create XSpreadsheetDocument
	//				xSpreadsheetDocument = getXSpreadsheetDocument(xComponent);
	//
	//				if(docType == Constants.REMOTE_DOCUMENT && remoteBook.allowEmptyFileContent()){
	//					//Setting 'sheet name' based on locale
	//					//setSheetNameBasedOnLocale(request, xSpreadsheetDoc);
	//					logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] This Remote Document allows empty contants so set console name");
	//				}
	//				else{
	//					//TODO: ADD these two methods togather
	//					//Replacing Special Characters from the Sheet Names 
	//					//Review : if we can allow the special cherectors
	//					ImportExportUtil.replaceSpecialCharactersinSheetNames(xSpreadsheetDocument);
	//
	//					//for csv and tsv sheet name is same as the temp libo file name so trimming the temporary cherectors.
	//					if(format == EngineConstants.CSV_FILE_FORMAT || format == EngineConstants.TSV_FILE_FORMAT){
	//						ImportExportUtil.replaceLiboNameinSheetNames(xSpreadsheetDocument, docName, liboFileName);
	//					}
	//				}
	//
	//			}
	//
	//			//		        Write the file contents in DB
	//			//		        Read publish DB 
	//			//		        Create chunkcontainer 
	//			//		        Delete Publish DB
	//
	//			//If document Id in null then its a new import and we need to add the DB entries and get a new id,
	//			//else updating the contents of an existing workbook.
	//			if(docType == Constants.REMOTE_DOCUMENT){
	//				updateRemoteDB(remoteBook, docType);
	//			}
	//			else if (docType == Constants.SHEET_DOCUMENT){
	//				if (docId == null) {
	//					docId = DocumentUtils.addNewDocumentToDB(docOwner, docName, false, Constants.SHEET_DOCUMENT, true, resourceId);
	//				}
	//				else{
	//					updatePublishRange = true;
	//				}
	//				//get current container to re write publish range 
	//				chunkContainer = getChunkContainer(resourceId, docName, docOwner);
	//				this.zuId = chunkContainer.getDocOwnerZUID();
	//				//Read current publish range database
	//				if(updatePublishRange){
	//					readPublishSheetRanges(docId, docOwner, chunkContainer);
	//				}
	//				//remove current publish range database
	//				removePublishSheetRanges(docId, docName, docOwner);
	//			}
	//
	//			//			write sub DB entries(image, charts, buttons)
	//			if(docType == Constants.REMOTE_DOCUMENT && remoteBook.isNewContentImport()){
	//				logger.log(Level.INFO, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Existing Remote Document, No new sub DB Entries");
	//			}
	//			else{
	//				//Review: exception should not stop the import flow
	//				try{
	//					GraphUtils.updateChartDetailsinDB(xSpreadsheetDocument, String.valueOf(docId), docOwner, -1);
	//				}catch(Exception e){
	//					logger.log(Level.WARNING, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] CouldNotUpdateChartDetails", e);
	//					//throw new Exception("CouldNotUpdateChartDetails");
	//				}
	//				String bridgeNo = loConnectionObject.getBridgeNo();
	//				try {
	//					//TODO: remove request object from ImageUtils.readImages.
	//					if(docType == Constants.REMOTE_DOCUMENT){
	//						ImageUtils.readImages(bridgeNo, xSpreadsheetDocument, zuId, docOwner, loginName, docId, true, -1, true);
	//					}
	//					else if (docType == Constants.SHEET_DOCUMENT){
	//						ImageUtils.readImages(bridgeNo, xSpreadsheetDocument, Long.parseLong(zuId), docOwner, loginName, docId, true, -1, false);
	//					}
	//				} catch (Exception e) {
	//					logger.log(Level.WARNING, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] CouldNotReadImages", e);
	//					//throw new Exception("CouldNotReadImages");
	//				}
	//				//Method call to move buttons from cell to page.
	//				try {
	//					ImportExportUtil.moveButtonsToPage(xSpreadsheetDocument);
	//				} catch (Exception e) {
	//					logger.log(Level.WARNING, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] CouldNotMoveButtonsToPage", e);
	//					//throw new Exception("CouldNotMoveButtonsToPage");
	//				}
	//
	//				try{
	//					//		add custome properties
	//					/* Adding Custom property and converting the OO Macro to Excel Macro */
	//					ImportExportUtil.addCustomProperty("DocId", docId.toString(), xComponent);//No internationalization
	//					ImportExportUtil.addCustomProperty("DocOwner", docOwner, xComponent);//No internationalization
	//					//TODO need this method for Excel File's import alone
	//					ImportExportUtil.convertOOBasicMacroCodeToExcelMacroCode(xComponent);
	//					ImportExportUtil.addCustomProperty("ZohoSheetVersion", "2.0", xComponent);//No internationalization
	//					ImportExportUtil.saveFreezePaneInfoFromOO(docOwner, docId, xComponent);
	//					activesheet = ImportExportUtil.getActiveSheetNameFromOO(xSpreadsheetDocument);
	//				}
	//				catch (Exception e) {
	//					//throw new Exception("CouldNotAddCustomProperty");
	//					logger.log(Level.WARNING, "[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] CouldNotAddCustomProperty", e);
	//				}
	//			}
	//
	//			if(docType == Constants.REMOTE_DOCUMENT && !remoteBook.isExistingDoc()){
	//				RemoteUtils.saveToAPIStore(Long.parseLong(zuId), docOwner, Long.parseLong(docId), docName, xSpreadsheetDocument, true);
	//			}
	//			else{
	//				boolean docSavedStatus = ImportExportUtil.saveDocument(chunkContainer, docName, docId + "", xSpreadsheetDocument, activesheet, null, null, false, null, false);
	//
	//				if (!docSavedStatus) {
	//					logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Document not saved.");//No I18N
	//					return ZFSNGConstants.CONVERSION_FAILURE;
	//				} else {
	//					//Review : check this for remote cases
	//					//add for remote Documents as well
	//					int isFileSizeSupported = ImportExportUtil.isFileSizeSupported(null, xSpreadsheetDocument, Long.valueOf(zuId), Long.valueOf(docId), null);
	//					if (isFileSizeSupported != ZFSNGConstants.CONVERSION_SUCCESS) {
	//						logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] File not Supported Error:"+isFileSizeSupported);//No I18N
	//						return isFileSizeSupported;
	//					}
	//				}
	//
	//				//rewrite the same database entries removed earlier.
	//				if(updatePublishRange){
	//					reWritePublishSheetRanges(chunkContainer, xSpreadsheetDocument);
	//					//REVIEW :  The below versionDocument call shud be outside this if check
	//					// REVIEW: also avoid during the remote case
	//					EngineUtils1.versionDocument(chunkContainer, "MANUAL", "imported-version", docOwner, zfsngVersionId, RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + chunkContainer.getResourceKey()));                                                    //No I18N
	//				}
	//				// TODO: What is this? // Check to stop creating thumbnail if it is already created - thumbnail will create once in Constants.holdontime
	//
	//			}
	//			ZSStats.incrementByGroup(contentBytes.length, ZSStats.IMPORT_SIZE_RANGE_VALUE_ARRAY, ZSStats.IMPORT_SIZE_RANGE_STATS_ARRAY);
	//		}
	//		catch (com.sun.star.lang.DisposedException e) {                                         //works from Patch 1
	//			//TODO : Have to check the below method (Skeleton method)
	//			OOConnectionPool.NullifyRemoteContext(null);
	//			logger.log(Level.WARNING, null, e);
	//			ClientUtils.logException("docid", "", docOwner, "ImportDocument", "", "Unable to ImportDocument.", e);        //No I18N
	//			//            logger.info("chunkContainer: "+chunkContainer+" documentId:"+documentId+" docId: "+docId);
	//			if (chunkContainer != null && docId != null) {
	//				DocumentUtils.deleteDocumentEntry(chunkContainer, "fromImport", null);//No I18N
	//			}
	//			ZSStats.incrementByCustomValue(ZSStats.IMPORTERROR);
	//			return ZFSNGConstants.CONVERSION_FAILURE;
	//		} catch (Exception ee) {
	//			logger.log(Level.WARNING, null, ee.getMessage());
	//			ClientUtils.logException("docid", "", docOwner, "ImportDocument", "", "Unable to ImportDocument.", ee);        //No I18N
	//			// logger.info("chunkContainer: "+chunkContainer+" documentId:"+documentId+" docId: "+docId);
	//			if (chunkContainer != null && docId != null) {
	//				DocumentUtils.deleteDocumentEntry(chunkContainer, "fromImport",  null);//No I18N
	//			}
	//			ZSStats.incrementByCustomValue(ZSStats.IMPORTERROR);
	//			return ZFSNGConstants.CONVERSION_FAILURE;
	//		}
	//		finally{
	//			//REview :cghage the oo names to libo
	//			ImportExportUtil.closeComponent(xComponent);
	//			OOConnectionPool.returnRemoteDesktop(loConnectionObject.getxComponentLoader());
	//		}
	//		return ZFSNGConstants.CONVERSION_SUCCESS;
	//	}

//	private XComponent createXComponent(String liboFileName) throws Exception{
//
//		InputStream is = null;
//		//String lIBOFileName = docId + (int)(Math.random() * 1000) + docName;
//		XComponent xComponent = null;
//		XComponentLoader xComponentLoader = connectionObject.getxComponentLoader();
//		boolean liboDelete = false;
//
//		String lIBOHost = connectionObject.getLiboHost();
//		String lIBOFilePath = EnginePropertyUtil.getSheetPropertyValue("LIBOImportLocation") + liboFileName; // No I18N
//		logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] File written at LIBO as:"+liboFileName);
//		try{
//			PropertyValue[] loadProps = null;
//			if (format.equalsIgnoreCase("csv") || format.equalsIgnoreCase("tsv")) {                            // No I18N
//				loadProps = new PropertyValue[2];
//			} else {
//				loadProps = new PropertyValue[1];
//			}
//			loadProps[0] = new PropertyValue();
//			loadProps[0].Name = "Hidden";                                                // No I18N
//			loadProps[0].Value = Boolean.TRUE;
//
//			if (format.equalsIgnoreCase("csv")) {
//				loadProps[1] = new PropertyValue();
//				loadProps[1].Name = "FilterOptions";                                        // No I18N
//				loadProps[1].Value = "44,34,76,1,1/1/1/1/1/1/1/1/1/1/1/1/1";                                // No I18N
//			}
//			if (format.equalsIgnoreCase("tsv")) {
//				loadProps[1] = new PropertyValue();
//				loadProps[1].Name = "FilterOptions";                                        // No I18N
//				loadProps[1].Value = "9,34,76,0,1,1/1/1/1/1/1/1";                                // No I18N
//			}
//			//lIBOHost = request.getAttribute(Constants.LIBOHOST).toString();
//			is = new ByteArrayInputStream(contentBytes);
//
//			// TODO: Need to put the below in try-catch and propogate the specific exception
//
//			String LIBOHome = ImportExportUtil.saveAtLIBOServer(lIBOHost, lIBOFilePath, is);
//			String absoluteFileName = LIBOHome + File.separator + lIBOFilePath;
//
//			LifeSpanController lsc =  new LifeSpanController(Thread.currentThread(), "Open Office Component Creation Thread", null, null, null); //NO I18N
//			lsc.control();
//			xComponent = xComponentLoader.loadComponentFromURL("file://"+absoluteFileName, "_blank", 0, loadProps);        //                                // No I18N
//			lsc.cancel();
//			try{
//				ImportExportUtil.deleteAtLIBOServer(lIBOHost, lIBOFilePath);
//				is.close();
//				liboDelete = true;
//			}
//
//			catch (Exception e) {
//				logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] Exception on Deleting at LIBO Server:"+resourceId+":"+docOwner);
//			}
//
//			/*if (xComponent == null) {
//				logger.log(Level.WARNING,"[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] xSpreadsheetComponent IS NULL"+resourceId+":"+docOwner);//No I18N
//				// TODO : Throwing password protected error message here, which is juct an assumption,
//				// find the correct method to check the password protected sheet.
//				throw new Exception("CouldNotCretateXComponent");
//			}
//			*/
//			return xComponent;
//		}
//		catch (Exception e) {
//			logger.log(Level.WARNING, "Could Not create XComponent",e.getMessage());
//			throw e;
//		}
//		finally{
//			if(is != null)
//			{
//				is.close();
//			}
//			if(!liboDelete){
//				ImportExportUtil.deleteAtLIBOServer(lIBOHost, liboFileName);
//			}
//		}
//	}

	//	public static XComponent getSpreadsheetDocComponent(XInputStream inStream, String contentType) throws Exception
	//	{
	//		ZSStats.incrementByCustomValue(ZSStats.OO_COMPONENT_CREATION);
	//
	//		// -- Desktop Object creation -- //
	//		LOConnectionObject loConnectionoObject = new LOConnectionObject();
	//		loConnectionoObject = OOConnectionPool.getRemoteDesktop(null);
	//		XComponentLoader xCompLoader = loConnectionoObject.getxComponentLoader();
	//		//lIBOHost = loConnectionoObject.getLiboHost();
	//		if (xCompLoader == null) {
	//			return null;
	//		}
	//
	//		// -- OO Component Property Settings -- //
	//		PropertyValue[] props;
	//		if(contentType == null) {
	//			props = new PropertyValue[2];
	//		} else  {
	//			String type = contentType.toLowerCase();
	//			boolean isCSV = ("csv".equals(type) || ".csv".equals(type)) ? true : false;//No I18N
	//			boolean isTSV = ("tsv".equals(type) || ".tsv".equals(type)) ? true : false;//No I18N
	//			if (isCSV) {
	//				props = new PropertyValue[4];
	//				props[3] = ImportExportUtil.makePropertyValue("FilterOptions", "44,34,0,1,1/1/1/1/1/1/1");//No I18N
	//			} else if(isTSV) {
	//				props = new PropertyValue[4];
	//				props[3] = ImportExportUtil.makePropertyValue("FilterOptions", "9,34,0,1,1/1/1/1/1/1/1");//No I18N
	//			} else {
	//				props = new PropertyValue[3];
	//			}
	//			props[2] = ImportExportUtil.makePropertyValue("FilterName", (String) ImportExportUtil.getFilterName(contentType));//No I18N
	//		}
	//		props[0] = ImportExportUtil.makePropertyValue("Hidden", Boolean.TRUE);//No I18N
	//		props[1] = ImportExportUtil.makePropertyValue("InputStream", inStream);//No I18N
	//
	//		//LifeSpanController lsc =  new LifeSpanController(Thread.currentThread(), "Open Office Component Creation Thread", null, null, null); //NO I18N
	//		//lsc.control();
	//
	//		XComponent xSpreadsheetComponent = xCompLoader.loadComponentFromURL("private:stream", "_blank", 0, props);//No I18N
	//		//lsc.cancel();
	//
	//		return xSpreadsheetComponent;
	//	}

	public XSpreadsheetDocument getXSpreadsheetDocument(Object obj ) throws Exception {
		XSpreadsheetDocument xSpreadsheetDocument= (XSpreadsheetDocument) UnoRuntime.queryInterface( XSpreadsheetDocument.class, obj );
		if(xSpreadsheetDocument == null){
			logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] XSpreadsheetDocument IS NULL:"+resourceId+":"+docOwner);//No I18N
			throw new Exception("CouldNotCretateXSpreadsheetDocument");
		}
		XServiceInfo xInfo = (com.sun.star.lang.XServiceInfo) UnoRuntime.queryInterface(XServiceInfo.class, xSpreadsheetDocument);
		if (xInfo == null || !xInfo.supportsService("com.sun.star.sheet.SpreadsheetDocument")) {
			logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] XServiceInfo IS NULL:"+resourceId+":"+docOwner);//No I18N
			throw new Exception("CouldNotCreateXServiceInfo");
		}
		return xSpreadsheetDocument;
	}







	//dont make common











	//	public HashMap updateDBvalues(HttpServletRequest request, RemoteBook remoteBook, String documentName) throws Exception{
	//
	//		HashMap resultantMap = new HashMap();
	//
	//		String userName = remoteBook.getUserName();
	//		//logger.info("userName >>>>>>>>>>>> "  +userName);
	//		resultantMap.put("USER_NAME", userName);//No I18N
	//
	//		Persistence pers = (Persistence) BeanUtil.lookup ("Persistence", userName);//No I18N
	//		DataObject DO = pers.constructDataObject();
	//
	//		long current_time = new Date().getTime();
	//
	//		/* -- Documents table update. -- */
	//		HashMap updater = remoteBook.getMapHolder();
	//
	//		String _docName = documentName;
	//		String docName = (_docName != null) ? _docName : ""; 
	//		//String docName = (_docName != null) ? _docName : (docName != null) ? docName : ""; //Empty document name update for remote editor
	//		resultantMap.put("DOCUMENT_NAME", docName);
	//
	//		String docId = null;		
	//		//Remote tables update
	//		HashMap rbm = (HashMap) updater.get("REMOTEBOOKS_TABLE"); //RemoteBooks Map
	//		logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] REMOTEBOOKS_TABLE:"+resourceId+":"+docOwner+ rbm);
	//		Row rbRow = null;
	//		if(rbm != null){
	//			if(remoteBook.isAuthCase()){
	//				long loginZUID = DocumentUtils.getZUID(userName);
	//				String rId = DocumentUtils.addNewDocumentOrTemplateToZFSNG(String.valueOf(loginZUID), docName, ResourceType.DOC_LIBRARY, ResourceType.REMOTEAUTH_WORKBOOK,null);
	//				docId = DocumentUtils.addNewDocumentToDB(userName, docName, false, docType, remoteBook.isAuthCase(), rId);			
	//			}
	//			else{
	//				docId = DocumentUtils.addNewDocumentToDB(userName, docName, false, docType, remoteBook.isAuthCase());
	//			}
	//
	//			rbRow = new Row("RemoteBooks"); //RemoteBooksRow
	//			rbRow.set("API_KEY_ID",  rbm.get("API_KEY_ID"));
	//			rbRow.set("BOOK_NAME", (String) resultantMap.get("DOCUMENT_NAME"));
	//			rbRow.set("SAVE_URL_OR_AGENT_NAME", (String) rbm.get("SAVE_URL_OR_AGENT_NAME"));
	//			rbRow.set("PUSH_FORMAT", (String) rbm.get("PUSH_FORMAT"));
	//			if(rbm.get("HANDBACK_ID") == null){
	//				rbRow.set("HANDBACK_ID", "No HANDBACKID");
	//			} else{
	//				rbRow.set("HANDBACK_ID", (String) rbm.get("HANDBACK_ID"));
	//			}
	//			rbRow.set("CREATED_DATE",new Long(current_time));
	//			rbRow.set("ALLOW_EDIT", (Boolean) rbm.get("ALLOW_EDIT"));
	//			rbRow.set("DOCUMENT_ID", Long.valueOf(docId));
	//			rbRow.set("IS_WMS_SAVE_MODE", (Integer) rbm.get("IS_WMS_SAVE_MODE"));
	//			rbRow.set("LAST_STATUS_TIME", new Long(current_time));
	//
	//			//User document Id setting
	//			Long userDocId = (Long) rbm.get("USER_DOC_ID");
	//			if(userDocId != null) {
	//				rbRow.set("USER_DOC_ID", userDocId);
	//			}else {
	//				if(remoteBook.collabDoc()){
	//					rbRow.set("USER_DOC_ID", rbRow.get("REMOTE_BOOK_ID"));
	//				} else {
	//					rbRow.set("USER_DOC_ID", new Long(-1));
	//				}
	//			}
	//
	//			rbRow.set("SAVE_AS_URL", (String) rbm.get("SAVE_AS_URL"));
	//			rbRow.set("META_DATA", (String) rbm.get("META_DATA"));
	//			DO.addRow(rbRow);
	//		}
	//
	//		//Remote Books State table update
	//		HashMap rbsm = (HashMap) updater.get("REMOTEBOOKS_STATE_TABLE"); //RemoteBooks Map
	//		logger.info("[LIBO-IMPORTER-MINI-SERVER][CONVERSION:IMPORT] REMOTEBOOKS_STATE_TABLE:"+resourceId+":"+docOwner+ rbsm);
	//		Row rbsRow = null;
	//		if(rbsm != null){
	//			rbsRow = new Row("RemoteBooks_State"); //RemoteBooks_State Row
	//			Long remoteBookId = (Long) rbsm.get("REMOTE_BOOK_ID");
	//			rbsRow.set("REMOTE_BOOK_ID", (remoteBookId != null) ? remoteBookId : rbRow.get("REMOTE_BOOK_ID"));
	//			rbsRow.set("REMOTE_USER", ((remoteBook.isAuthCase()) ? remoteBook.getUserName() : rbsm.get("USER_NAME")));
	//			rbsRow.set("COLLABORATION_USER_NAME", rbsm.get("USER_NAME"));
	//			rbsRow.set("MODE", rbsm.get("MODE"));
	//			remoteBook.setMode((Integer) rbsm.get("MODE"));  //NO I18N
	//			rbsRow.set("LAST_ACCESSED_TIME", new Long(current_time));
	//			rbsRow.set("DOC_USER_STATUS", 0);
	//			rbsRow.set("HANDBACK_ID", (String) rbsm.get("HANDBACK_ID"));
	//			DO.addRow(rbsRow);
	//
	//		}
	//
	//		pers.add(DO);
	//		if(remoteBook.isExistingDoc()){
	//			resultantMap.put("DOCUMENT_ID", rbsm.get("DOCUMENT_ID"));//NO I18N
	//			//check with sasi
	//			remoteBook.setDocumentId(Long.parseLong(docId));
	//		}
	//		if(docId != null) {
	//			resultantMap.put("DOCUMENT_ID", Long.valueOf(docId));//NO I18N
	//			resultantMap.put("STORE_FILE", "true"); //NO I18N
	//			//check with sasi
	//			remoteBook.setDocumentId(Long.parseLong(docId));
	//		}
	//
	//		if(rbRow != null) {
	//			resultantMap.put("REMOTE_BOOK_ID", rbRow.get("REMOTE_BOOK_ID"));//NO I18N	
	//			remoteBook.setRemoteBookId((Long) rbRow.get("REMOTE_BOOK_ID")); //NO I18N
	//		}
	//		if(rbsRow != null) {
	//			resultantMap.put("REMOTE_BOOK_STATE_ID", rbsRow.get("REMOTE_BOOK_STATE_ID"));//NO I18N
	//			remoteBook.setRemoteBookStateId((Long) rbsRow.get("REMOTE_BOOK_STATE_ID")); //NO I18N
	//		}
	//
	//		//logger.info("===========>>> resultantMap : " + resultantMap);
	//		return resultantMap;
	//	}




}

