//$Id$
package com.zoho.sheet.conversion;

import java.util.logging.Logger;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;

public class AuthRemoteViewerAction extends StrutsRequestHandler {
	
	
	public static Logger logger = Logger.getLogger(AuthRemoteViewerAction.class.getName()); 
	
	public String execute() throws Exception	{

	
		String 	docId	=	request.getParameter("doc");
//                                System.out.println("docId::AUTH_REMOTE::::" + docId);

		request.setAttribute("devicetype", "handheld");
	
		if(docId != null) {
			request.getRequestDispatcher(DocumentUtils.getDocControllerPath(AccessType.AUTH_REMOTE, docId, null, true, request)).forward(request, response);
		}
		return null;
	}
	

}
