package com.zoho.sheet.conversion;

//import com.sun.star.beans.PropertyValue;
//import com.sun.star.beans.XPropertySet;
//import com.sun.star.bridge.XBridge;
//import com.sun.star.bridge.XBridgeFactory;
//import com.sun.star.connection.XConnection;
//import com.sun.star.connection.XConnector;
//import com.sun.star.frame.XComponentLoader;
//import com.sun.star.frame.XStorable;
//import com.sun.star.io.XInputStream;
//import com.sun.star.lang.XComponent;
//import com.sun.star.lang.XMultiComponentFactory;
//import com.sun.star.uno.UnoRuntime;
//import com.sun.star.uno.XComponentContext;
//import com.sun.star.util.XCloseable;
//
//import java.io.BufferedReader;
//import java.io.IOException;
//import java.io.InputStreamReader;
//import java.net.InetAddress;
//import java.net.UnknownHostException;
//import java.nio.Buffer;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.logging.Level;
//import java.util.logging.Logger;
//
////$Id$
//
///**
// * <AUTHOR>
// *
// */
//
//public class LiboConversion {
//
//	/**
//	 * @param args
//	 */
//	public static final Logger LOGGER = Logger.getLogger(LiboConversion.class.getName());
//
//	private static XConnection connection;
//
//	public static byte[] getConversionBytes(String outFormat, byte[] contentBytes, boolean canRetry) throws Exception {
//		XComponent xComponent = null;
//		OOOutputStream ooos = null;
//		try{
//			String ipAddress = PropertyUtil.getPropertyValue("sofficeIP");
//			String portNumber = PropertyUtil.getPropertyValue("sofficePort");
//
//			LOGGER.log(Level.INFO, "[SheetConverter]Creating LiboConversion Connection : ", new Object[]{});
//
//			XComponentLoader xCompLoader = getConnection(ipAddress, portNumber);
//
//			LOGGER.info("[SheetConverter] Logging componentLoader : " + xCompLoader);
//
//			PropertyValue[] propertyValue = new PropertyValue[2];
//			propertyValue[0] = new PropertyValue();
//			propertyValue[0].Name = "Hidden"; // No I18N
//			propertyValue[0].Value = Boolean.valueOf("TRUE"); //No I18N
//
//			XInputStream oois = new OOInputStream(contentBytes);
//			propertyValue[1] = new PropertyValue();
//			propertyValue[1].Name = "InputStream"; // No I18N
//			propertyValue[1].Value = oois;
//
//			xComponent = xCompLoader.loadComponentFromURL("private:stream", "_blank", 0, propertyValue); // No I18N
//
//			ooos = new OOOutputStream();
//			PropertyValue[] lProperties = new PropertyValue[3];
//			lProperties[1] = new PropertyValue();
//			lProperties[1].Name = "OutputStream"; // No I18N
//			lProperties[1].Value = ooos;
//			if(outFormat!="ods") {
//				lProperties[2] = new PropertyValue();
//				lProperties[2].Name = "FilterName"; // No I18N
//				lProperties[2].Value = getFilterName(outFormat);
//			}
//
//			LOGGER.info("[SheetConverter] Getting xStorable");
//
//			XStorable xStorable = UnoRuntime.queryInterface(XStorable.class, xComponent);
//			xStorable.storeAsURL("private:stream", lProperties); // No I18N
//
//			if(canRetry) {
//				LOGGER.info("[SheetConverter] Conversion successful without Retry");
//			}else {
//				LOGGER.info("[SheetConverter] Conversion successful with Retry");	
//			}
//
//			return ooos.toByteArray();
//		}
//		catch (NullPointerException npe) {
//			if (xComponent != null) {
//				LOGGER.info("[SheetConverter] xComponent is not null by NPE happened");
//			}
//			LOGGER.log(Level.INFO, "[SheetConverter] NPE at connection/conversion {0} ", new Object[]{npe});
//
//			if (canRetry) {
//				LiboConversion.restartLibreoffice();
//				LOGGER.info("[SheetConverter] Restarted libreoffice as connection failed due to NPE and retrying conversion");
//				return getConversionBytes(outFormat, contentBytes, false);
//			}
//		}
//		catch (IllegalArgumentException iae) {
//			LOGGER.log(Level.INFO, "[SheetConverter] IllegalArgumentException at connection/conversion {0} ", new Object[]{iae});
//		}
//		catch (Exception e) {
//			if(e.getMessage().contains("type detection aborted")){
//				LOGGER.log(Level.INFO,"[SheetConverter] This Exception is from Docs sync then either corrept or hidden sheet: ".concat(e.getMessage()));
//			}else {
//				LOGGER.log(Level.INFO, "[SheetConverter] LiboConversion Exception {0}:", new Object[]{e});
//			}
//		}
//		finally {
//			try{
//				if (ooos != null) {
//					LOGGER.info("[SheetConverter] ooos is not null");
//					ooos.close();
//				}
//		// Note: not closing this instance as it is shutting down soffice
//
////				XCloseable xCloseable = null;
////				if(xComponent != null)
////				{
////					LOGGER.info("[SheetConverter] xComp is not null");
////					try
////					{
////						xCloseable = (XCloseable) UnoRuntime.queryInterface(XCloseable.class,xComponent);
////						if (xCloseable != null )
////						{
////							LOGGER.info("[SheetConverter] xclosable.close()");
////						}
////
////						{
////							LOGGER.info("[SheetConverter] xcomponent.dispose()");
////							xComponent.dispose();
////						}
////					}
////					catch(Exception e)
////					{
////						LOGGER.log(Level.WARNING,null,e);
////					}
////				}
//
//
//				if (connection != null) {
//					LOGGER.info("[SheetConverter] Attempting to close xConnection");
//					connection.close();
//					LOGGER.info("[SheetConverter] Closed xConnection");
//				}
//			}
//			catch (Exception e) {
//				LOGGER.info("[SheetConverter]Exception occurred on conversion --" + e);
//			}
//		}
//		return null;
//	}
//
//	private static XComponentLoader getConnection(String serverIP, String port) {
//
//		XBridge bridge = null ;
//		XComponentContext xRemoteContext = null;
//		XComponentLoader officeComponentLoader;
//		XMultiComponentFactory xRemoteServiceManager = null;
//
//		String con = "socket,host=" + serverIP + ",port=" + port; // No I18N
//		LOGGER.info("[SheetConverter] server (libo) ip is : " + serverIP + ", port : " + port);
//
//		try {
//			xRemoteContext = com.sun.star.comp.helper.Bootstrap.createInitialComponentContext(null);
//
//			Object x = xRemoteContext.getServiceManager().createInstanceWithContext(
//					"com.sun.star.connection.Connector", xRemoteContext ); // No I18N
//			LOGGER.info("[SheetConverter] Logging x Object : " + x);
//
//			XConnector xConnector = (XConnector)UnoRuntime.queryInterface(XConnector.class, x);
//
//			connection = xConnector.connect(con);
//
//			if (connection == null) {
//				LOGGER.info("[SheetConverter]Connection is null");
//				return null;
//			}
//
//			Object xx = xRemoteContext.getServiceManager().createInstanceWithContext("com.sun.star.bridge.BridgeFactory", xRemoteContext); // No I18N
//
//			XBridgeFactory xBridgeFactory = (XBridgeFactory)UnoRuntime.queryInterface(XBridgeFactory.class, xx);
//			LOGGER.info("[SheetConverter] Logging xBridgeFactory : " + xBridgeFactory.getExistingBridges().length);
//
//			try {
//				bridge = xBridgeFactory.createBridge("Bridge-1", "urp", connection, null); // No I18N
//				LOGGER.info("[SheetConverter] Logging created bridge : " + bridge);
//			}
//			catch (Exception e) {
//				LOGGER.info("[SheetConverter] Exception with creating bridge : " + e);
//				bridge = xBridgeFactory.getBridge("Bridge-1"); // No I18N
//			}
//
//			x = bridge.getInstance( "StarOffice.ServiceManager"); // No I18N
//			LOGGER.info("[SheetConverter] Logging instance from bridge : " + x);
//
//			// Query the initial object for its main factory interface
//			xRemoteServiceManager = ( XMultiComponentFactory )
//					UnoRuntime.queryInterface( XMultiComponentFactory.class, x );
//
//			//For NumberFormatter Service
//			//serviceManagers[bridgeNo] = xRemoteServiceManager;
//
//			// retrieve the component context (it's not yet exported from the office)
//			// Query for the XPropertySet interface.
//			XPropertySet xProperySet = ( XPropertySet )
//					UnoRuntime.queryInterface( XPropertySet.class, xRemoteServiceManager );
//
//			// Get the default context from the office server.
//			Object oDefaultContext =
//					xProperySet.getPropertyValue( "DefaultContext" ); // No I18N
//
//			// Query for the interface XComponentContext.
//			XComponentContext xOfficeComponentContext =
//					( XComponentContext ) UnoRuntime.queryInterface(
//							XComponentContext.class, oDefaultContext );
//			LOGGER.info("[SheetConverter] Logging componentContext : " + xOfficeComponentContext);
//
//			// now create the desktop service
//			// NOTE: use the office component context here !
//			Object oDesktop = xRemoteServiceManager.createInstanceWithContext(
//					"com.sun.star.frame.Desktop", xOfficeComponentContext ); // No I18N
//			LOGGER.info("[SheetConverter] Logging desktop : " + oDesktop);
//
//			officeComponentLoader = ( XComponentLoader )
//					UnoRuntime.queryInterface( XComponentLoader.class, oDesktop );
//
//			//String available = (null != officeComponentLoader ? "available" : "not available"); // No I18N
//			//logger.info( "remote ServiceManager is " + available );
//			//desktops[bridgeNo] = officeComponentLoader;
//			//inusecount[bridgeNo] = 0;
//			//logger.info("Created Bridge-"+bridgeNo);
//
//			return officeComponentLoader;
//		}
//		catch( Exception e ) { //works from Patch 1
//			LOGGER.log(Level.INFO, "[SheetConverter]LiboConversion getConnection Exception {0}:", new Object[]{e});
//			LOGGER.info("Error :: "+e.getMessage());
//		}
//		return null;
//	}
//
//	private static Object getFilterName(String fileType){
//		// map has list of supported 'push Formats'
//		Map<String, String> FILTER_MAP = new HashMap<String, String>();
//		FILTER_MAP.put("xls", "MS Excel 97");
//		FILTER_MAP.put(".xls", "MS Excel 97");
//		FILTER_MAP.put("xlt", "MS Excel 97");
//		FILTER_MAP.put(".xlt", "MS Excel 97");
//		FILTER_MAP.put("xlsx", "Calc MS Excel 2007 XML");
//		FILTER_MAP.put("xltx", "Calc MS Excel 2007 XML");
//		FILTER_MAP.put("xlsm","Calc MS Excel 2007 VBA XML");		//xlsm
//		FILTER_MAP.put(".xlsx", "MS Excel 2007 XML");
//		FILTER_MAP.put("ods", "calc8");
//		FILTER_MAP.put(".ods", "calc8");
//		FILTER_MAP.put("sxc", "StarOffice XML (Calc)");
//		FILTER_MAP.put(".sxc", "StarOffice XML (Calc)");
//		FILTER_MAP.put("pdf", "calc_pdf_Export");
//		FILTER_MAP.put(".pdf", "calc_pdf_Export");
//		FILTER_MAP.put("csv", "Text - txt - csv (StarCalc)");
//		FILTER_MAP.put(".csv", "Text - txt - csv (StarCalc)");
//		FILTER_MAP.put("tsv", "Text - txt - csv (StarCalc)");
//		FILTER_MAP.put(".tsv", "Text - txt - csv (StarCalc)");
//		return FILTER_MAP.get(fileType);
//	}
//
//	public static boolean isLibreofficeUp() {
//		try {
//			LOGGER.info("[SheetConverter] Checking health of libreoffice/soffice");
//
//			String[] cmd = {"sh", "-c", "ps -ef | grep soffice | grep -v grep"}; // No I18N
//			Process process = Runtime.getRuntime().exec(cmd);
//
//			BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
//			String outputString = reader.readLine();
//
//			LOGGER.info("[SheetConverter] Output for ps command is : " + outputString);
//
//			if (outputString != null) {
//				// If not null, the output for ps command will be there with all the details, so it is up
//				return true;
//			} else {
//				// Note: Printing the error output to just to see if any syntax or runtime exception happened
//				// Otherwise if there are no syntax issues, if the input stream output is null then the process is not running
//
//				BufferedReader err = new BufferedReader(new InputStreamReader(process.getErrorStream()));
//				StringBuilder errorOutput = new StringBuilder();
//				String line;
//				while ((line = err.readLine()) != null) {
//					errorOutput.append(line + "\n");
//				}
//				LOGGER.info("[SheetConverter] Error output for ps command : " + errorOutput);
//			}
//		}
//		catch (Exception ex) {
//			LOGGER.info("[SheetConverter] Exception with checking libreoffice health : " + ex);
//		}
//		return false;
//	}
//
//	public static void restartLibreoffice() throws Exception {
//		try {
//			LOGGER.info("[SheetConverter] Restarting libreoffice/soffice");
//
//			Runtime processRuntime = Runtime.getRuntime();
//			String pathToLiboRestarter = "/home/<USER>/zsheetconverter/scripts/restartLIBO.sh"; // No I18N
//
//			processRuntime.exec("nohup sh " + pathToLiboRestarter); // No I18N
//
//			LOGGER.info("[SheetConverter] Restart libreoffice process execution done");
//
//		}
//		catch (Exception ex) {
//			LOGGER.info("[SheetConverter] Exception with restarting libreoffice : " + ex);
//		}
//
//		// Wait for 2s to make sure soffice up by the time we try conversion again
//		Thread.sleep(2000);
//
//		// CHeck libo health after restart
//		if (!isLibreofficeUp()) {
//			throw new Exception("[SheetConverter] Libreoffice is down after restart");
//		}
//	}
//}
