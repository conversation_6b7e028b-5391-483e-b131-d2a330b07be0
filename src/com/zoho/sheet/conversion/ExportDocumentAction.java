///* $Id$ */

package com.zoho.sheet.conversion;

import com.adventnet.zoho.websheet.model.ErrorCode;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.authorization.DocumentController;
import com.zoho.sheet.authorization.util.AccessControlUtil;
import com.zoho.sheet.parse.RangeAddress;
import com.zoho.sheet.util.*;
import com.zoho.zfsng.client.ZohoFS;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *
 */
public class ExportDocumentAction extends StrutsRequestHandler {

    public static final Logger LOGGER = Logger.getLogger(ExportDocumentAction.class.getName());

    @Override
    public String execute() throws Exception {

        AccessType acessType = CurrentRealm.getAccessIdentity();
        WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();
        UserProfile userProfile = CurrentRealm.getUserProfile();

        String zfsngVersionNum = null;
        try {
            zfsngVersionNum = CurrentRealm.getWorkBookIdentity();
        }catch(Exception e) {
            LOGGER.log(Level.WARNING, "Exception while fetching workbook identity :: ", e);
        }


        String format = (String) request.getParameter("ftype");

        if (format == null) {
            format = (String) request.getParameter("format");
        }

        if(format == null) {
            format = (String) request.getParameter("download");
        }

        if (format == null) {
            format = "xlsx";		//No i18N
        }
        format = RemoteUtils.maskNull(format);

        if (format.indexOf(".") == 0) {
            format = format.substring(1); // removing dot
        }

        if(acessType == AccessType.RANGE_PUBLIC_EXTERNAL || acessType == AccessType.RANGE_PUBLIC_ORG ||
                acessType == AccessType.SHEET_PUBLIC_EXTERNAL || acessType == AccessType.SHEET_PUBLIC_ORG) {
            if(!"csv".equals(format) && !"tsv".equals(format)) {
                format = "csv";		//No i18N
            }
        }

        String password = RemoteUtils.maskNull((String) request.getParameter("password"));
        boolean isExportName = false;

        if ("csv".equalsIgnoreCase(format) || "tsv".equalsIgnoreCase(format)) {
            isExportName = true;
        }
        String userAgent = request.getHeader("user-agent");
        String activeSheetName = (String) request.getParameter("currentSheet");
        if (activeSheetName == null) {
            activeSheetName = (String) request.getParameter("sheetname");
        }

        String rid = null;
        String sheetversionNum = null;
        try {
            if(!container.isRemoteMode()) {
                String zfsngVersionId = ZohoFS.getVersionIDForEditor(container.getDocsSpaceId(), container.getResourceKey(), zfsngVersionNum);
                sheetversionNum = DocumentUtils.getVersionNoforZFSNGVersion(container.getDocId(), container.getDocOwner(), zfsngVersionId);
            }
        }catch (Exception e) {
            LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] ExportDocumentAction Version Num Error", e);
        }

        try {
//            ConnectionPool ConnectionPool = new ConnectionPool();
//            ConnectionObject ConnectionObject = new ConnectionObject();
//            ConnectionObject = ConnectionPool.getRemoteDesktop();

            String docName = container.getDocName();
            if (docName == null || "".equals(docName)) {
                try {
                    docName = LocaleMsg.getMsg("Untitled");
                } catch (Exception le) {
                    docName = "Untitled_Spreadsheet";// No I18N
                }
            }

            rid = container.getResourceId();
            //Exporter exporter = new Exporter(container, format, null, null, ConnectionObject, request, response);
            Exporter exporter = new Exporter(container, format, sheetversionNum, zfsngVersionNum);
            exporter.setActiveSheet(activeSheetName);
            String range = request.getParameter("range");
            if(range != null) {
                exporter.setRangeAddress((String) request.getParameter("range"));
            } else {
                String rangeId = (String) request.getAttribute("rangeId");
                JSONObjectWrapper rangeDetails = rangeId != null ? PublishRangeUtils.getRangeDetailsFromID(container, rangeId) : null;
                if(rangeDetails != null)
                {
                    RangeAddress rangeAddress = new RangeAddress(rangeDetails.getInt(JSONConstants.START_ROW), rangeDetails.getInt(JSONConstants.START_COLUMN),
                            rangeDetails.getInt(JSONConstants.END_ROW), rangeDetails.getInt(JSONConstants.END_COLUMN));
                    exporter.setRangeAddress(rangeAddress);
                    exporter.setAssociatedSheetName((String) rangeDetails.get(JSONConstants.ASSOCIATED_SHEET_NAME));
                }
            }

            exporter.setSheetIndex(Boolean.valueOf(request.getParameter("isSheetIndexBased")));
            exporter.allowSave(false);

            //We don't save the container for remote, scratch, and public modes. So need to create a temp file to export the current edited data.
//            File tempFile = null;
//            try {
//                tempFile = EngineUtils1.createTempODSFile(container, zfsngVersionNum, false, null);
//                exporter.setTempFile(tempFile);
//            } catch (Exception e) {
//                LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] ExportDocumentAction tempFile creation error:" + rid, e);
//                //e.printStackTrace();
//            }
            byte[] content = null;
            try {

                boolean isEnabled = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("enableHeadlessPDF"));  // No I18N
                if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_ZIP)) {
                    content = exporter.getDocumentZipBytes(request);
                } else if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_PDF) && isEnabled) {
                    content = exporter.getDocumentPDFBytes(request);
                } else if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_CSV) || format.equalsIgnoreCase(EngineConstants.FILEEXTN_TSV)){
                    content = exporter.getDocumentCsvTsvBytes();
                } else if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_XLSX)){
                    content = exporter.getXLSXBytes();
                }else if(format.equalsIgnoreCase(EngineConstants.FILEEXTN_XLSM)){
                    content = exporter.getXLSMBytes();
                } 
                else if(format.endsWith("nzsheet")){//No I18N
                    content = ImportExportUtil.getZohoSheet(container, zfsngVersionNum, sheetversionNum);
                }
                else {
                    content = exporter.getDocumentBytes();
                }

//                if (tempFile != null) {
//                    tempFile.delete();
//                }

                if (password != null) {
                    content = ImportExportUtil.encryptDocument(format, content, password);
                }
                try {
                    if (!container.isRemoteMode()) {
                        String referer = request.getHeader("referer");
                        String remoteIpAddress = request.getRemoteAddr();
                        ZohoFS.auditResourceDownloadActivity(container.getResourceId(), userAgent, referer, remoteIpAddress);
                    }
                } catch (Exception e) {
                    LOGGER.log(Level.INFO, "[CONVERSION:EXPORT][Exception] ExportDocumentAction resource audit failed" + rid, e);
                }
            }
            catch(ProhibitedActionException e)
            {
                LOGGER.log(Level.INFO, "[CONVERSION:EXPORT][Exception] Exception while exporting " + rid, e);
                JSONObjectWrapper errObj = ErrorCode.getProhibitedDialogErrorMsg(e);
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                PrintWriter out = response.getWriter();
                out.print(errObj); //NO OUTPUTENCODING
                out.flush();
                return null;
            }
            catch (Exception e) {

                LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] ExportDocumentAction couldn't get the content from Exporter:" + rid, e);
                response.setContentType("text/plain");
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                return null;
            }
            String mimeType = null;
            if (format.equals("xlsx")) {
                mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";// No I18N
            } else if (format.equals("xls")) {
                mimeType = "application/vnd.ms-excel";// No I18N
            } else if (format.equals("csv")) {
                mimeType = "text/csv";// No I18N
            } else if (format.equals("tsv")) {
                mimeType = "text/tsv";// No I18N
            } else if (format.equals("ods")) {
                mimeType = "application/vnd.oasis.opendocument.spreadsheet";// No I18N
            } else if (format.equals("pdf")) {
                mimeType = "application/pdf";// No I18N
            } else if (format.equals("html")) {
                mimeType = "text/html";// No I18N
            }

            response.reset();    //IE with https (secured) connection export issue fix block starts
            response.setContentType(mimeType);
            String printType = exporter.getPrintType();
            String fileName = docName + "." + format;

            if(activeSheetName == null) {
                activeSheetName = exporter.getActiveSheetName();
            }
            if(activeSheetName != null){
                if (isExportName || (printType!=null && "SHEET".equals(printType) && "pdf".equals(format))) {   //No I18N
                    fileName = docName + '_' + activeSheetName + "." + format;      //No I18N
                }
                if(printType!=null && "RANGE".equals(printType) && "pdf".equals(format) && range != null){     //No I18N
                    range = range.replaceAll(":","_");  //No I18N
                    fileName = docName + '_' +activeSheetName + "." + range + "." + format;     //No I18N
                }
            }
            fileName = URLEncoder.encode(fileName, "UTF-8").replace("+", "%20");     // No I18N

            if (userAgent != null && !userAgent.contains("Firefox")) {
                response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\""); // No I18N
            } else {
                response.setHeader("Content-Disposition", "attachment;filename*=\"" + fileName + "\"");// No I18N
            }
            response.setCharacterEncoding("UTF-8");	// No I18N

            String responseType = (String) request.getParameter("responseType");
            if ("base64String".equals(responseType)) {
                response.getWriter().print(Base64.getEncoder().encodeToString(content)); //NO OUTPUTENCODING 
            } else {
                OutputStream out = response.getOutputStream();
                out.write(content); //NO OUTPUTENCODING    
                out.flush();
            }
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] ExportDocumentAction tempFile creation error:" + rid, e);// TODO: handle exception
        }

        return null;
    }
}
