//$Id$
package com.zoho.sheet.conversion;

import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;

import com.adventnet.zoho.websheet.model.zs.ZSZipInputStream;

import com.zoho.sheet.action.FileConversionAction;
import com.zoho.sheet.deluge.OAuthUtils;
import com.zoho.sheet.util.*;
import org.apache.commons.collections4.map.CaseInsensitiveMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.encryption.AccessPermission;
import org.apache.pdfbox.pdmodel.encryption.StandardProtectionPolicy;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.hssf.record.crypto.Biff8EncryptionKey;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.ODFNotOfficeXmlFileException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.poifs.crypt.Decryptor;
import org.apache.poi.poifs.crypt.EncryptionInfo;
import org.apache.poi.poifs.crypt.EncryptionMode;
import org.apache.poi.poifs.crypt.Encryptor;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.CellAddress;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.Table;
import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.UserAPI;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.ErrorCode;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.parser.ODSSizeAndProtectionTransformer;
import com.adventnet.zoho.websheet.model.parser.ODSWorkbookParser;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.EngineUtils1;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.LifeSpanController;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.util.ZSStore;
import com.adventnet.zoho.websheet.model.util.ZSStore.FileExtn;
import com.adventnet.zoho.websheet.model.util.ZSStore.FileName;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.StoreFactory;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.sun.star.awt.Point;
import com.sun.star.beans.PropertyValue;
import com.sun.star.beans.PropertyVetoException;
import com.sun.star.beans.UnknownPropertyException;
import com.sun.star.beans.XPropertyContainer;
import com.sun.star.beans.XPropertySet;
import com.sun.star.chart.XAxisXSupplier;
import com.sun.star.chart.XAxisYSupplier;
import com.sun.star.chart.XChartDocument;
import com.sun.star.chart.XDiagram;
import com.sun.star.container.XIndexAccess;
import com.sun.star.container.XNameAccess;
import com.sun.star.container.XNameContainer;
import com.sun.star.container.XNameReplace;
import com.sun.star.container.XNamed;
import com.sun.star.document.XDocumentProperties;
import com.sun.star.document.XDocumentPropertiesSupplier;
import com.sun.star.document.XEmbeddedObjectSupplier;
import com.sun.star.drawing.XControlShape;
import com.sun.star.drawing.XDrawPage;
import com.sun.star.drawing.XDrawPageSupplier;
import com.sun.star.drawing.XShape;
import com.sun.star.form.XFormsSupplier;
import com.sun.star.frame.XComponentLoader;
import com.sun.star.frame.XController;
import com.sun.star.frame.XModel;
import com.sun.star.frame.XStorable;
import com.sun.star.io.XInputStream;
import com.sun.star.lang.IllegalArgumentException;
import com.sun.star.lang.WrappedTargetException;
import com.sun.star.lang.XComponent;
import com.sun.star.lang.XMultiServiceFactory;
import com.sun.star.lang.XServiceInfo;
import com.sun.star.script.XLibraryContainer;
import com.sun.star.sheet.XCellRangeAddressable;
import com.sun.star.sheet.XCellRangeFormula;
import com.sun.star.sheet.XSheetCellCursor;
import com.sun.star.sheet.XSpreadsheet;
import com.sun.star.sheet.XSpreadsheetDocument;
import com.sun.star.sheet.XSpreadsheetView;
import com.sun.star.sheet.XUsedAreaCursor;
import com.sun.star.table.CellRangeAddress;
import com.sun.star.table.XCellRange;
import com.sun.star.table.XTableChart;
import com.sun.star.table.XTableChartsSupplier;
import com.sun.star.uno.Any;
import com.sun.star.uno.UnoRuntime;
import com.sun.star.util.XCloseable;
import com.zoho.sheet.action.FileReader;
import com.zoho.sheet.connection.ZSConnection;
import com.zoho.sheet.connection.ZSConnectionFactory;
//import com.zoho.sheet.conversion.ConnectionPool.ConnectionObject;
import com.zoho.sheet.deluge.DelugeUtils;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;
import org.json.JSONObject;



/**
 * <AUTHOR>
 *
 */

public class ImportExportUtil {
	public static Logger logger = Logger.getLogger(ImportExportUtil.class.getName());
	
	public static Long getZOID(String userName)
	{
		Long zuid = getZUID(userName);
		if (zuid != -1) {
			return ImportExportUtil.getZOIDFromZuid(String.valueOf(zuid));
		}
		return -1L;
	}
	public static Long getZUID(String userName) {
		return (Long) (DocumentUtils.getZUIDandFullName(userName)[0]);
	}


	// To create thumbnail from ods input stream
	public static void generateThumbnail(InputStream ins, String zuid, String rid) throws ServletException {

		try(ZSZipInputStream zis = new ZSZipInputStream(ins)) {

			ZipEntry entry;
			while ((entry = zis.getNextEntry()) != null) {
				if (entry.getName().contains("thumbnail")) {

					BufferedImage bufferImg = ImageIO.read(zis);

					InputStream imgIns1 = getImageStream(bufferImg, 200, 150);
					updateThumbnail(imgIns1, zuid, rid, "-1", 200, 150);

					InputStream imgIns2 = getImageStream(bufferImg, 300, 170);
					updateThumbnail(imgIns2, zuid, rid, "-1", 300, 170);
				}
			}
		} catch (Exception e) {
			logger.log(Level.WARNING, "[CONVERSION:IMPORT] [THUMBNAIL CREATION] ERROR WHEN GENERATING THUMBNAIL", e);
			throw new ServletException(e.getMessage());
		}
	}
	public static String setSheetNameBasedOnLocale(String lang, XSpreadsheetDocument xSpreadsheetDoc) {
		//Setting 'sheet name' based on locale
		//String lang = (String) request.getParameter("lang");
		lang = (lang != null && ImportExportUtil.getLocaleDetails(lang) != null) ? lang : null;
		if(lang != null){
			try{
				//TODO: Need to change the I18N object gathering
				I18nMessage prop = new I18nMessage(new Locale(lang));
				if(prop != null){
					String sheetName = prop.getMsg("Sheet") + "1";//No I18N
					String defaultSheetName = xSpreadsheetDoc.getSheets().getElementNames()[0];
					XSpreadsheet xSpreadsheet =(XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class,xSpreadsheetDoc.getSheets().getByName(defaultSheetName));
					XNamed xNamed = (XNamed) UnoRuntime.queryInterface(XNamed.class, xSpreadsheet);
					xNamed.setName(sheetName);

					//Code for setting RL-TB to the first sheet in remote api (only for the case where we use empty.ods)
					if(DocumentUtils.isRTLLanguage(lang)){
						XPropertySet set =  UnoRuntime.queryInterface(XPropertySet.class, xSpreadsheet);
						set.setPropertyValue("TableLayout",new Short(com.sun.star.text.WritingMode2.RL_TB));
						logger.info("New sheet created in RTL:::_remoteApi");
					}
					return sheetName;
				}
			} catch (Exception e){
				logger.log(Level.WARNING,null,e);
				return null;
			}
		}
		return null;
	}

	public static boolean checkThumbnailHoldOnTime(WorkbookContainer container) {

		long currTime = System.currentTimeMillis();
		long lastCreatedTime = container.getThumbnailCreatedTime();
		long holdonTime = Constants.THUMBNAIL_HOLD_ON_TIME;

		logger.log(Level.INFO, "[CONVERSION:IMPORT] [THUMBNAIL CREATION] Checking holdontime LastCreated : {0}, CurrentTime : {1}", new Object[]{lastCreatedTime, currTime});
		if ((currTime - lastCreatedTime) > holdonTime) {
			return true;
		}
		return false;
	}

	public static void updateThumbnail(InputStream ins, String zuid, String rid, String version, int width, int height) {
		try {
			if (ins != null) {
				ZohoFS.updateContentForThumbnailImage(zuid, rid, version, ins, width, height);
			}

		} catch (Exception e) {
			logger.log(Level.WARNING, "[CONVERSION:IMPORT] ERROR WHEN UPDATING THUMBNAIL", e);
		}
	}
	public static InputStream getImageStream(BufferedImage bufferImg, int width, int height) throws Exception {

		BufferedImage newImg = null;

		int oWidth = bufferImg.getWidth();
		int oHeight = bufferImg.getHeight();

		if (oWidth > width & oHeight > height) {
			newImg = ImageUtils.cropImage(bufferImg, width, height);
		} else if (oWidth < width & oHeight > height) {
			newImg = ImageUtils.cropImage(bufferImg, oWidth, height);
		} else if (oWidth > width & oHeight < height) {
			newImg = ImageUtils.cropImage(bufferImg, width, oHeight);
		} else if (oWidth < width & oHeight < height) {
			int type = bufferImg.getType() == 0 ? BufferedImage.TYPE_INT_ARGB : bufferImg.getType();
			newImg = ImageUtils.resizeImage(bufferImg, type, width, height);
		}

		InputStream imgIns = null;
		if (newImg != null) {
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			ImageIO.write(newImg, "png", baos); //No i18N
			imgIns = new ByteArrayInputStream(baos.toByteArray());
		}
		return imgIns;
	}

	public static DataObject getDocumentSheetsDO(String docOwner, long docid) throws Exception {
		Persistence persistence = null;
		if (docOwner != null) {
			persistence = SheetPersistenceUtils.getPersistence(docOwner);
		} else {
			persistence = SheetPersistenceUtils.getPersistence();
		}
		SelectQueryImpl sql = new SelectQueryImpl(new Table("DocumentSheets"));//No I18N
		sql.addSelectColumn(new Column(null, "*"));
		Criteria cri = new Criteria(new Column("DocumentSheets", "DOCUMENT_ID"), Long.valueOf(docid), QueryConstants.EQUAL);//No I18N
		sql.setCriteria(cri);
		DataObject dob = persistence.get(sql);
		return dob;
	}


	// public static void addImportDetailsToRedisQueue(String resourceId,String zfsngVersionId,String format,String authorName,String importerZuid,String ownerZuid,int count){
	// 	try {
	// 		logger.info("[CONVERSION] - Resource key addition   the redis queue:::"+resourceId);
	// 		RedisHelper.zadd(redisCSKey, System.currentTimeMillis(), resourceId, -1);
	// 		logger.info("all entries in redis::::"+RedisHelper.zrangeWithScores(redisCSKey,5,500000));

	// 		JSONObject obj = new JSONObject();
	// 		obj.put("zfsngVersionId",zfsngVersionId);
	// 		obj.put("format",format);
	// 		obj.put("authorName",authorName);
	// 		obj.put("importerZuid",importerZuid);
	// 		obj.put("ownerZuid",ownerZuid);
	// 		obj.put("retryCount",count);
	// 		logger.info("Object entries in redis::::"+obj);
	// 		RedisHelper.hset(redisCSKey_details,resourceId,obj.toString());
	// 	}catch (Exception e){

	// 	}
	// }
	// public static void removeImportDetailsFromRedisQueue(String resourceId) {
	// 	try {
	// 		logger.info("[CONVERSION] - Resource key removal from  the redis queue:::"+resourceId);
	// 		RedisHelper.zrem(redisCSKey, resourceId);
	// 		RedisHelper.hdel(redisCSKey_details,resourceId);
	// 	} catch (Exception e) {

	// 	}
	// }

	public static void generateThumbnail(HttpServletRequest request, WorkbookContainer container) throws ServletException {

		try {

			String rid = container.getResourceId();
			String zuid = container.getDocOwnerZUID();
			String html = DocumentUtils.getThumbnailHTML(request, container, null);

                        if (html != null) {

                                InputStream ins = DocumentUtils.getThumbnailStream(html, zuid, 200, 150, "0.5");
                                updateThumbnail(ins, zuid, rid, "-1", 200, 150);

                                InputStream ins1 = DocumentUtils.getThumbnailStream(html, zuid, 300, 170, "0.5");
                                updateThumbnail(ins1, zuid, rid, "-1", 300, 170);

                                logger.log(Level.INFO, "[THUMBNAIL_IMAGE] Thumbnail image conversion from ImportExportUtil");
                        }

		} catch (Exception e) {
			logger.log(Level.WARNING, "[CONVERSION:IMPORT] [THUMBNAIL CREATION] ERROR WHEN GENERATING THUMBNAIL", e);
			throw new ServletException(e.getMessage());
		}
	}

	public static Long getZOIDFromZuid(String zuid)
	{
		try{
			UserAPI usrApi = IAMProxy.getInstance().getUserAPI();
			return usrApi.getZOID(Long.valueOf(zuid));
		} catch (Exception e) {
			logger.log(Level.WARNING, "[CONVERSION:IMPORT] Exception to get ZOID From ZUID", e);
		}
		return -1L;
	}

//	public static byte[] getConvertedBytes(File tempFile, String inputFormat, String outputFormat) {
//		FileInputStream fis = null;
//		byte[] fileBytes = null;
//		try {
//			fis = new FileInputStream(tempFile);
//			logger.info("[CONVERSION] getConvertedBytes: fis :: " + fis);
//			 fileBytes = IOUtils.toByteArray(fis);
//		}
//		catch (FileNotFoundException ex) {
//			logger.info("[CONVERSION] getConvertedByte Exception while reading file : " + ex);
//			return null;
//		}
//		catch (IOException ex) {
//			logger.info("[CONVERSION] getConvertedByte Exception while reading file : " + ex);
//			return null;
//		}
//		finally {
//			try {
//				if(fis!=null) {
//					fis.close();
//				}
//			} catch (IOException e) {
//				logger.info("exception while closing FileInputStream");
//			}
//		}
//		return getConvertedBytes(fileBytes, inputFormat, outputFormat);
//	}

//	public static byte[] getConvertedBytes(byte[] fileBytes, String inputFormat, String outputFormat) {
//		logger.log(Level.INFO,"[CONVERSION] getConvertedBytes {0} {1}:", new Object[]{inputFormat, outputFormat});
//		byte[] convertedBytes = null;
//
//		String protocol = "http", ip, port, endPoint = "miniService", authToken; //No I18N
//		ip = EnginePropertyUtil.getSheetPropertyValue("LiboComponentIP"); //No I18N
//		port = EnginePropertyUtil.getSheetPropertyValue("LiboComponentPort"); //No I18N
//		authToken = EnginePropertyUtil.getSheetPropertyValue("LiboConnectionToken"); //NO I18N
//
//		String url = protocol + "://" + ip + ":" + port + "/" + endPoint;
//		logger.info("[CONVERSION] getConvertedBytes: Connecting to : " + url);
//
//		ArrayList paramNames = new ArrayList();
//		ArrayList paramValues = new ArrayList();
//
//		{
//			paramNames.add("inputFileType");
//			paramValues.add(inputFormat);
//		}
//		{
//			paramNames.add("outputFileType");
//			paramValues.add(outputFormat);
//		}
//
//		try {
//			// Make a http connection with the mini service that serves the Libo conversion
//			ZSConnectionFactory factory = new ZSConnectionFactory(url, "POST"); //No I18N
//			ZSConnection conn = factory.getConnection(true);
//			conn.setContentType("application/x-www-form-urlencoded");
//			conn.setFileBytes(fileBytes, "FileConversion"); //No I18N
//			conn.setParameter(paramNames.toArray(), paramValues.toArray());
//			conn.setRequestHeader(new String[]{"AuthToken"}, new String[]{authToken}); //No I18N
//			conn.process();
//
//			// This is executed after response is received
//			if (conn.getResponseCode() == 200) {
//				logger.info("[CONVERSION] getConvertedByte Response from new libo request is 200");
//				convertedBytes = conn.getByteInputStream();
//			}
//			logger.info("[CONVERSION] getConvertedByte converted bytes after contacting miniservice : " + convertedBytes);
//		}
//		catch (Exception ex) {
//			logger.info("[CONVERSION] getConvertedByte Exception during conversion process : " + ex);
//			return null;
//		}
//
//		return convertedBytes;
//	}
//public static boolean isMiniServerEnabled(){
//		try {
//			String redisVal = RedisHelper.get(RedisHelper.CONVERSION_SERVER_MINI_ENABLE, -1);
//			if ("true".equalsIgnoreCase(redisVal) || EngineConstants.ISLIBOMINISERVERENABLED) {
//				return true;
//			} else {
//				return false;
//			}
//		}catch (Exception e){
//			return false;
//		}
//}
//	public static boolean isLiboServerEnabled(){
//		try {
//			String val = RedisHelper.get(RedisHelper.CONVERSION_SERVER_LIBO_ENABLE, -1);
//			boolean sheetPropVal =	Boolean.parseBoolean( EnginePropertyUtil.getSheetPropertyValue("CONVERSION_SERVER_ENABLE"));//No I18N
//			if ("true".equalsIgnoreCase(val) && sheetPropVal) {
//				return Boolean.parseBoolean(val);
//			}
//		}catch (Exception e){
//
//		}
//		return false;
//	}


//	public static XComponent getXComponent(WorkbookContainer container, String sheetVersionNo, File tempFile, ConnectionObject connectionObject) throws Exception
//	{
//		XComponent xSpreadsheetComponent  = null;
//		XComponentLoader xComponentLoader = null;
//		BufferedInputStream     bis       = null;
//		ByteArrayOutputStream     bos       = null;
//		OOInputStream            oois       = null;
//		InputStream             is           = null;
//		// the below code is stupid thing we have done. Please correct if you see it. We should pass the zfsngVersionId for woekBook and Containers.
//		//String zfsngVersionId = request.getParameter("versionid");
//		String lIBOHost = null;
//		String lIBOFileName = EnginePropertyUtil.getSheetPropertyValue("LIBOExportLocation") + container.getResourceId() +  System.currentTimeMillis() + (int)(Math.random() * 1000) + EngineConstants.ENGINE_ODS_FILE_FORMAT; // No I18N
//		boolean liboDelete = true;
//		try
//		{
//			xComponentLoader = connectionObject.getxComponentLoader();
//			lIBOHost = connectionObject.getLiboHost();
//			//Throwing error if the OO Connection is failed.
//			if(xComponentLoader == null)
//			{
//				throw new ServletException("Connection Error");
//			}
//
//			if(tempFile != null){
//				is = (InputStream) ImportExportUtil.openURL(tempFile);
//			}else{
//				is = getFileInputStream(container, sheetVersionNo);
//			}
//			if(is == null)
//			{
//				throw new ServletException("No such document is available");
//			}
//			//Libre Office changes write Macro Library and Module Names in manifest.xml
//			//reWriteManifestXML commented, versions with macros and older then 05/26/15 and may not export.
//			//is = reWriteManifestXML(is, container, sheetVersionNo);
//			PropertyValue[] loadProps = new PropertyValue[1];
//			loadProps[0]         = new PropertyValue();
//			loadProps[0].Name     = "Hidden";                                                            // No I18N
//			loadProps[0].Value  = Boolean.TRUE;
//			//is = saveTempInputStream(is, container, zfsngVersionId); // should be inside the webapps/websheet/exportdocs
//			String LIBOHome = saveAtLIBOServer(lIBOHost, lIBOFileName, is);
//
//			liboDelete = false;	//Temporary File written at LIBO, Finally need to delete in case of error.
//			String absoluteFileName = LIBOHome + File.separator + lIBOFileName;
//
//			// TODO: No need of this synchronized block .. removed synchronized
//			//			synchronized (xComponentLoader) {
//			xSpreadsheetComponent = xComponentLoader.loadComponentFromURL("file://" + absoluteFileName, "_blank", 0, loadProps);                        // No I18N
//			connectionObject.setxComponent(xSpreadsheetComponent);
//			if(xSpreadsheetComponent==null){
//				logger.log(Level.INFO, "[CONVERSION:EXPORT] COULD NOT CREATE XSPREADSHEETCOMPONENT{0}", container.getResourceId());
//			}
//			//			}
//			// TODO: try - catch - no need of throw exception
//			try{
//				deleteAtLIBOServer(lIBOHost, lIBOFileName);
//				liboDelete = true;
//			}
//			catch(Exception e){
//				logger.log(Level.INFO, "[CONVERSION: EXPORT] Exception on Deleting at LIBO Server{0}", container.getResourceId());
//			}
//			return xSpreadsheetComponent;
//			/*XSpreadsheetDocument xDocument = (XSpreadsheetDocument) UnoRuntime.queryInterface(XSpreadsheetDocument.class, xSpreadsheetComponent);
//            if(xDocument == null)
//            {
//                throw new ServletException("No such document to open");
//            }
//            return xDocument;*/
//		}
//		catch( com.sun.star.lang.DisposedException ex)
//		{
//			OOConnectionPool.nullifyDesktop(xComponentLoader);
//			throw new ServletException("Process Error");
//		}
//		finally
//		{
//			if(is != null)
//			{
//				is.close();
//			}
//			if(bis != null)
//			{
//				bis.close();
//			}
//			if(bos != null)
//			{
//				bos.close();
//			}
//			if(oois != null)
//			{
//				oois.closeInput();
//			}
//			if(!liboDelete){
//				deleteAtLIBOServer(lIBOHost, lIBOFileName);
//			}
//		}
//	}

	//	public static XComponent getXComponentToImport(byte[] contentBytes, String fileName, String format,LOConnectionObject loConnectionObject) throws Exception
	//	{
	//
	//		XComponentLoader xComponentLoader = null;
	//		XComponent xSpreadsheetComponent = null;
	//		//		byte b[] = contentBytes;
	//		String lIBOHost = null;
	//		String lIBOFileName = (int)(Math.random() * 1000) + fileName; //Temprary File Name to save st LIBO Server
	//		String lIBOFilePath = EnginePropertyUtil.getSheetPropertyValue("LIBOImportLocation") + lIBOFileName;  // No I18N
	//		boolean liboDelete = true;
	//		//		if(contentBytes == null){
	//		//			b = ZohoFS.getResourceContentForVersionId(zuid, resourceId, zfsngVersionId, format);
	//		//		}else{
	//		//			b = contentBytes;
	//		//		}
	//		// Following special chars are replaced in document name - so as to be conssistent across - sheet / writer / show
	//		fileName = fileName.replaceAll(Constants.docNameDisallowedCharsRegex, "-");
	//		/* Loading openoffice properties */
	//		xComponentLoader = loConnectionObject.getxComponentLoader();
	//		lIBOHost = loConnectionObject.getLiboHost();
	//
	//		PropertyValue[] loadProps = null;
	//		if (format.equalsIgnoreCase("csv") || format.equalsIgnoreCase("tsv")) {                            // No I18N
	//			loadProps = new PropertyValue[2];
	//		} else {
	//			loadProps = new PropertyValue[1];
	//		}
	//		loadProps[0] = new PropertyValue();
	//		loadProps[0].Name = "Hidden";                                                // No I18N
	//		loadProps[0].Value = Boolean.TRUE;
	//
	//		if (format.equalsIgnoreCase("csv")) {                                                    // No I18N
	//			loadProps[1] = new PropertyValue();
	//			loadProps[1].Name = "FilterOptions";                                        // No I18N
	//			loadProps[1].Value = "44,34,76,1,1/1/1/1/1/1/1/1/1/1/1/1/1";                                // No I18N
	//		}
	//		if (format.equalsIgnoreCase("tsv")) {                                                    // No I18N
	//			loadProps[1] = new PropertyValue();
	//			loadProps[1].Name = "FilterOptions";                                        // No I18N
	//			loadProps[1].Value = "9,34,76,0,1,1/1/1/1/1/1/1";                                // No I18N
	//		}
	//
	//		//lIBOHost = request.getAttribute(Constants.LIBOHOST).toString();
	//		InputStream is = new ByteArrayInputStream(contentBytes);
	//
	//		// TODO: Need to put the below in try-catch and propogate the specific exception
	//		String LIBOHome = ImportExportUtil.saveAtLIBOServer(lIBOHost, lIBOFilePath, is);
	//		String absoluteFileName = LIBOHome + File.separator + lIBOFilePath;
	//
	//		// TODO: No need of this synchronized block .. removed synchronized
	//		//                synchronized (xComponentLoader) {
	//
	//		xSpreadsheetComponent = xComponentLoader.loadComponentFromURL("file://"+absoluteFileName, "_blank", 0, loadProps);                              // No I18N
	//		//                }
	//		try{
	//			ImportExportUtil.deleteAtLIBOServer(lIBOHost, lIBOFilePath);
	//			is.close();
	//			liboDelete = true;
	//		}
	//
	//		catch (Exception e) {
	//			logger.info("[CONVERSION - IMPORT] Exception on Deleting at LIBO Server");
	//		}
	//
	//		if (xSpreadsheetComponent == null) {
	//			logger.info("[CONVERSION - IMPORT] xSpreadsheetComponent IS NULL");//No I18N
	//			// TODO : Throwing password protected error message here, which is juct an assumption,
	//			// find the correct method to check the password protected sheet.
	//			return null;
	//		}
	//
	//		return xSpreadsheetComponent;
	//
	//	}

	//This method was used to rewrite the manifest for ods created by OpenOfffice with Macros.
//	private static InputStream reWriteManifestXML(InputStream is, WorkbookContainer container, String sheetVersionNo) throws Exception{
//		//TODO Pass the ZFSNG version No to get the current version's workbook
//		//String zfsngVersionNo = DocumentUtils.getVersionNoforZFSNGVersion(container.getDocId(), container.getDocOwner(), zfsngVersionId);
//		String zfsngVersionNo = null;
//		if (sheetVersionNo != null){
//			DataObject versionDao = DocumentUtils.getVersionDO(container.getDocId(), container.getDocOwner(), sheetVersionNo);
//			if ( versionDao == null || versionDao.isEmpty()) {
//				return null;
//			}
//			Row row = versionDao.getFirstRow("DocumentVersion"); // No I18N
//			String zfsngVersionId = (String) row.get("RESOURCE_VERSION_ID"); // No I18N
//			try{
//				zfsngVersionNo = (new JSONArray(ZohoFS.getVersionInfoForId(container.getDocsSpaceId(), container.getResourceId(), zfsngVersionId))).getJSONObject(0).getString("version_number");
//				if(zfsngVersionNo == null){
//					return null;
//				}
//			}catch(Exception e){
//				return null;
//			}
//		}
//		Workbook workBook = container.getWorkbook(zfsngVersionNo, true);
//
//		//return d;
//		ZipInputStream zip = null;
//		FileOutputStream fos = null;
//		ZipOutputStream zos = null;
//		BufferedReader    br = null;
//		try{
//			if(workBook.containsMacro() && sheetVersionNo !=null){ // For CSV Export zfsngVersionId is null
//				//long versionTime = DocumentUtils.getVersionTime(container, zfsngVersionId); //Time when this version ODS was written
//				long versionTime = getVersionTime(container, sheetVersionNo);
//				//TODO FInd versionTime from sheetversionNo
//				Date liboReleaseDate = new Date(EnginePropertyUtil.getSheetPropertyValue("LIBOReleaseDate")); // No I18N
//				if(versionTime <= liboReleaseDate.getTime()){ //If the exported version ODS was created before the LibraOffice Launch then rewrite the manifest.xml else  manifest.xml was written at the creation of ODS itself.
//					logger.log(Level.INFO, "[CONVERSION: EXPORT] OLD Version so Rewriting Manifest Version created at: {0}:{1}", new Object[]{versionTime, container.getResourceId()});
//					int BUF_LEN = 1024;
//					int c = 0;
//					String outFile = EngineConstants.TEMPEXPORTDIR;
//					outFile += container.getDocId() + sheetVersionNo + EngineConstants.ENGINE_ODS_FILE_FORMAT;
//					fos = new FileOutputStream(outFile);
//					zos = new ZipOutputStream(fos);
//					//Read verionOds from the temporary location and rewrite with macro module names in manifest.xml
//					zip = new ZipInputStream(is);
//					ZipEntry ze;
//					while((ze = zip.getNextEntry())!=null){
//						byte[] tmpBytes = new byte[BUF_LEN];
//						if(ze.getName().equalsIgnoreCase("META-INF/manifest.xml")){
//							EngineUtils1.writeManifestXML(zos, workBook);
//						}
//						// if error then check ze.getName() doesn't have the hidden and temporary created lock.unlocked files and xmls.
//						else{
//							zos.putNextEntry(ze);
//							while ((c = zip.read(tmpBytes, 0, BUF_LEN)) != -1) {
//								zos.write(tmpBytes, 0, c);
//							}
//						}
//					}
//					is = new FileInputStream(outFile);
//					new File(outFile).delete();
//					return is;
//				}
//			}
//		}
//		catch(Exception ex){
//			logger.log(Level.INFO, "Exception at OOutilsget:XComponent writeManifestXML(){0}:{1}", new Object[]{container.getResourceId(), sheetVersionNo});
//			throw ex;
//		}
//		finally{
//			if(zip != null){
//				zip.close();
//			}
//			if(zos != null){
//				zos.close();
//			}
//			if(fos != null){
//				fos.close();
//			}
//			if(br != null){
//				br.close();
//			}
//		}
//		return is;
//	}

	public static String saveAtLIBOServer(String liboHost, String fileName, InputStream is) throws Exception{
		String userName = "sas";    //UserName  // No I18N
		String host = liboHost; //Remote Machine Host
		String privateKey = System.getProperty("user.home") + EnginePropertyUtil.getSheetPropertyValue("PrivateKeyLocation"); // No I18N
		String LiboHome = null;
		try{
			Properties config = new Properties();
			config.put("StrictHostKeyChecking", "no");
			JSch jsch = new JSch();
			jsch.addIdentity(privateKey);
			Session session=jsch.getSession(userName, host);//SSH2 session //TODO try with no user name
			session.setConfig(config);
			session.connect();

			Channel channel = session.openChannel("sftp");	// No I18N
			channel.connect();
			ChannelSftp channelSftp = (ChannelSftp) channel;
			channelSftp.put(is, fileName);
			LiboHome = channelSftp.getHome();

			channelSftp.exit();
			channel.disconnect();
			session.disconnect();
		}catch(Exception e){
			logger.log(Level.WARNING, "Exception at writing at LIBO Server:"+liboHost+" :: "+fileName+" at LIBO Server", e);
			// TODO: We need to throw the exception from here ...
			// the parent method should handle it and it should be propagated to browser as well with proper error message
			e.printStackTrace();
			// TODO: define the i18n of the below message
			throw new Exception("LIBO.Server.Connection.Failure", e);
		}
		return LiboHome;
	}

	public static void deleteAtLIBOServer(String liboHost, String fileName) throws Exception{
		String host = liboHost; 	//Remote Machine Host
		String userName = "sas";    //UserName // No I18N
		String privateKey = System.getProperty("user.home") + EnginePropertyUtil.getSheetPropertyValue("PrivateKeyLocation"); // No I18N
		//private-key of the app server machine //move to conf
		try{
			Properties config = new Properties();
			config.put("StrictHostKeyChecking", "no");
			JSch jsch = new JSch();
			jsch.addIdentity(privateKey);
			Session session=jsch.getSession(userName, host);//SSH2 session //TODO try with no user name
			//session.setPassword(password);
			session.setConfig(config);
			session.connect();

			Channel channel = session.openChannel("sftp");	// No I18N
			channel.connect();
			ChannelSftp channelSftp = (ChannelSftp) channel;
			channelSftp.rm(fileName);

			channelSftp.exit();
			channel.disconnect();
			session.disconnect();
		}catch(Exception ex){
			String logWaring = "Exception on Deleting file: "+liboHost+" :: "+fileName+" at LIBO Server";	// No I18N
			logger.log(Level.WARNING, logWaring, ex.getMessage());
			//throw new Exception("LIBO.Server.Connection.Failure", e);
		}

	}

	public static String replaceSpecialCharactersinDocumentName (String docName) throws Exception {
		String[]      drop_char_match      = new String[] {"<", ">", "\"", "\\", "/"};
		String newDocName     = DocumentUtils.replace_hyphen_char(docName, drop_char_match, "_", false, false);
		if(newDocName.length() > 255){
			newDocName = newDocName.substring(0,255);
		}
//		logger.log(Level.INFO, "Renamed the Document "+ docName + " to " + newDocName);
		return newDocName;
	}

	public static void replaceSpecialCharactersinSheetNames (XSpreadsheetDocument xSpreadsheetDocument) throws Exception {
		//For To Handle Hidden Sheets.
		String[] sheetnames = xSpreadsheetDocument.getSheets().getElementNames();
		if (sheetnames.length > Utility.MAXNUMOFSHEETS) {
			throw new RuntimeException("number of sheets limit exceeded");//No I18N
		}

		String[] newSheetNames = replaceSpecialCharactersinSheetNames(sheetnames);

//		for(int i = 0; i < sheetnames.length; i++) {
//			logger.log(Level.OFF, "sheet {0} renamed-to {1}", new Object[]{sheetnames[i], newSheetNames[i]});
//		}

		XSpreadsheet xSpreadsheet = null;
		for (int i = 0; i < sheetnames.length; i++) {
			String oldName = sheetnames[i];
			String newName = newSheetNames[i];

			if (!oldName.equals(newName)) {
				xSpreadsheet = (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class, xSpreadsheetDocument.getSheets().getByName(oldName));
				XNamed xNamed = (XNamed) UnoRuntime.queryInterface(XNamed.class, xSpreadsheet);
				xNamed.setName(newName);

				String resettedSheetName = xNamed.getName().trim();

				//Which means that unable to rename the sheetName, which is due to sheet is password protecetd.
				if (!resettedSheetName.equals(newName)) {
					logger.log(Level.OFF, "not able to re-name the sheet {0} to {1}", new Object[]{resettedSheetName, newName});
					throw new Exception("UnableToRenameSheetWithSplChars");
				}
			}
		}
	}

	public static String[] replaceSpecialCharactersinSheetNames(String[] sheetNames) throws Exception {
		List<String> oldSheetNames = new ArrayList<>();

		Set<String> unique_names = new HashSet<>();
		for(String name: sheetNames) {
			unique_names.add(name);
		}

		int suffix = 1;
		for(String name : sheetNames) {
			if(name.trim().isEmpty()) {
				name = "Sheet"+(suffix++);//No I18N
				while(unique_names.contains(name)) {
					name = "Sheet"+(suffix++);//No I18N
				}
				unique_names.add(name);
			}
			oldSheetNames.add(name);
		}

		List<String> newSheetNames = new ArrayList<>();
//		String[] drop_char_match = new String[]{".", "^", "$", "&", "(", ")", "<", ">", "`", "\"", "'", "|", ",", "@", "?", "%", "~", "+", "[", "]", "{", "}", ":", "\\", "/", "=", "#", ";", "!", "*"};//No I18N
		String[] drop_char_match = new String[]{"'", "&", "?", "[", "]", "{", "}", ":", "\\", "/", "*"};//No I18N

		for (int i = 0; i < oldSheetNames.size(); i++) {
			String name = oldSheetNames.get(i);
			if (name.length() > 31) {
				name = name.substring(0, 31);
				/**
				 * Restricting the length of name to 31 here too keep the maximum part of original-name,
				 * please consider the below example where in 3rd example its would be renamed to 01234567890123456789012345678_2
				 * if the length is not reduced here
				 * old: 01234567890123456789012345678912,01234567890123456789012345678912,01234567890123456789012345678_12
				 * new: 0123456789012345678901234567891,01234567890123456789012345678_2,01234567890123456789012345678_1
				 */
				oldSheetNames.set(i, name);
			}
		}

		for (String oldSheetName : oldSheetNames) {

			String sheetNameProspect = DocumentUtils.replace_hyphen_char(oldSheetName, drop_char_match, "_", false, false);

			sheetNameProspect = sheetNameProspect.trim();

			if (sheetNameProspect.length() > 31) {
				sheetNameProspect = sheetNameProspect.substring(0, 31);
			}

			if(!sheetNameProspect.equals(oldSheetName) || newSheetNames.contains(oldSheetName)) {
				int cnt = 1;
				String duplicateSheetName = sheetNameProspect;
				while (newSheetNames.contains(sheetNameProspect) || oldSheetNames.contains(sheetNameProspect)) {
					if (sheetNameProspect.length() == 31) {
						int len = 31 - String.valueOf(cnt).length() - 1;
						duplicateSheetName = duplicateSheetName.substring(0, len);
					}
					sheetNameProspect = duplicateSheetName + "_" + cnt;
					cnt++;
				}
			}
			newSheetNames.add(sheetNameProspect);
		}
		return newSheetNames.toArray(new String[newSheetNames.size()]);
	}

	//TODO: check well
	public static void replaceLiboNameinSheetNames (XSpreadsheetDocument xSpreadsheetDocument, String liboFileName, String resourceId, String docName) throws Exception {
		//Trimming resourceIds from imported worksheets LIBO Changes for CSV, TSC,and some other special cases
		if(liboFileName.contains(".")){
			liboFileName = liboFileName.substring(0, liboFileName.indexOf("."));
		}
		String[]      sheetnames          = xSpreadsheetDocument.getSheets().getElementNames();
		int             totSheets             = sheetnames.length;
		for(int i=0; i < totSheets; i++) {
			String sheetName         = sheetnames[i];
			if(sheetName.contains(resourceId) || sheetName.contains(EngineConstants.FILENAME_REMOTEDOC)){
				docName = docName.substring(0, docName.indexOf(".")).trim();
				//this.liboFileName = this.liboFileName.substring(0, this.liboFileName.indexOf("."));
				XSpreadsheet xSpreadsheet      = (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class,xSpreadsheetDocument.getSheets().getByName(liboFileName));
				XNamed xNamed     = (XNamed) UnoRuntime.queryInterface(XNamed.class,xSpreadsheet);
				xNamed.setName(docName);
			}
		}
	}

	public static Long getVersionTime(WorkbookContainer container, String sheetVersionNo) throws Exception {
		SelectQueryImpl sql = new SelectQueryImpl(new Table("DocumentVersion"));
		sql.addSelectColumn(new Column("DocumentVersion", "*"));
		Criteria cri = new Criteria(new Column("DocumentVersion", "DOCUMENT_ID"), new Long(container.getDocId()), QueryConstants.EQUAL);
		cri = cri.and(new Criteria(new Column("DocumentVersion", "VERSION"), new Double(sheetVersionNo), QueryConstants.EQUAL));
		//SortColumn sortColumn = new SortColumn("DocumentVersion", "VERSION", false);	//No I18N
		//sql.addSortColumn(sortColumn);
		sql.setCriteria(cri);
		Persistence persistence = SheetPersistenceUtils.getPersistence(container.getDocOwner());
		DataObject data = persistence.get(sql);
		if(data.isEmpty()){
			return null;
		} else {
			Row row = data.getRow("DocumentVersion");	//No I18N
			long time = (Long)row.get("VERSION_TIME");
			return time;
		}
	}

	public static void addCustomProperty(String customName, String value, XComponent document) throws Exception {
		XDocumentPropertiesSupplier xDocumentInfoSupplier = (XDocumentPropertiesSupplier)UnoRuntime.queryInterface(XDocumentPropertiesSupplier.class, document);
		XDocumentProperties           xDocumentInfo       = xDocumentInfoSupplier.getDocumentProperties();
		XPropertyContainer       propSet                 = xDocumentInfo.getUserDefinedProperties(); //LibraOffice Changes
		try {
			propSet.addProperty(customName, (short)0, value);
		} catch(com.sun.star.beans.PropertyExistException pe) {
			XPropertySet prop = (XPropertySet)UnoRuntime.queryInterface(XPropertySet.class, propSet);//LibraOffice Changes
			prop.setPropertyValue(customName,value);
		}
	}

	public static String getCustomProperty(String customName, XComponent document) throws Exception
	{
		XDocumentPropertiesSupplier xDocumentInfoSupplier = (XDocumentPropertiesSupplier)UnoRuntime.queryInterface(XDocumentPropertiesSupplier.class, document);
		XDocumentProperties           xDocumentInfo         = xDocumentInfoSupplier.getDocumentProperties();
		//XPropertySet propSet = (XPropertySet)UnoRuntime.queryInterface(XPropertySet.class, xDocumentInfo);
		XPropertyContainer       xPropertyContainer                = xDocumentInfo.getUserDefinedProperties();
		XPropertySet xPropertySet = (XPropertySet) UnoRuntime.queryInterface(XPropertySet.class, xPropertyContainer);
		try
		{
			Object obj = xPropertySet.getPropertyValue(customName);
			if(obj instanceof Any)
			{
				Object temp = ((Any)obj).getObject();
				if(temp == null)
				{
					return null;
				}
				return temp.toString();
			}
			else if(obj instanceof String)
			{
				String value = (String)obj;
				return value;
			}
		}catch(UnknownPropertyException e) {
			logger.log(Level.INFO, "There is no {0} Custom property", customName);
			return null;
		}
		return null;
	}

	public static void removeCustomProperty(XComponent document, String customName) throws Exception
	{
		XDocumentPropertiesSupplier xDocumentInfoSupplier = (XDocumentPropertiesSupplier)UnoRuntime.queryInterface(XDocumentPropertiesSupplier.class, document);
		XDocumentProperties           xDocumentInfo         = xDocumentInfoSupplier.getDocumentProperties();
		XPropertyContainer propSet = (XPropertyContainer)UnoRuntime.queryInterface(XPropertyContainer.class, xDocumentInfo);
		try{
			propSet.removeProperty(customName);
		}catch(Exception pe){
			try {
				XPropertySet prop = (XPropertySet)UnoRuntime.queryInterface(XPropertySet.class, xDocumentInfo);
				propSet.removeProperty(customName);
			} catch(Exception pe1) {
				logger.log(Level.INFO, "There is no {0} Custom property", customName);
			}
		}
	}

	public static String getActiveSheetNameFromOO(XSpreadsheetDocument xDocument) {
		try {
			XModel                  xModel         = (XModel)UnoRuntime.queryInterface(XModel.class, xDocument);
			XController             xController     = xModel.getCurrentController();
			XSpreadsheetView      xView            = (XSpreadsheetView)UnoRuntime.queryInterface(XSpreadsheetView.class, xController );
			XSpreadsheet          xSpreadsheet     = xView.getActiveSheet();
			XNamed                  xNamed            = (XNamed) UnoRuntime.queryInterface(XNamed.class,xSpreadsheet);
			return xNamed.getName();
		} catch(Exception e) {
			return null;
		}
	}

	public static void moveButtonsToPage(XSpreadsheetDocument xSpreadsheetDocument) throws Exception {
		String[] sheetnames = xSpreadsheetDocument.getSheets().getElementNames();
		XSpreadsheet xSpreadsheetx    =    null;
		for(int i = 0; i < sheetnames.length; i++) {
			xSpreadsheetx = (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class, xSpreadsheetDocument.getSheets().getByName(sheetnames[i]));
			moveButtonsToPage(xSpreadsheetx);
		}
	}

	static public XDrawPageSupplier XDrawPageSupplier(Object obj){
		XDrawPageSupplier xDPS = (XDrawPageSupplier)UnoRuntime.queryInterface(XDrawPageSupplier.class,obj);
		return xDPS;
	}
	// buttons
	static public XFormsSupplier XFormsSupplier(Object obj){
		XFormsSupplier XFormsSupplier = (XFormsSupplier)UnoRuntime.queryInterface(XFormsSupplier.class,obj);
		return XFormsSupplier;
	}
	static public XNamed XNamed(Object obj){
		XNamed XNamed = (XNamed)UnoRuntime.queryInterface(XNamed.class,obj);
		return XNamed;
	}
	static public XPropertySet XPropertySet( Object obj ) {
		return (XPropertySet) UnoRuntime.queryInterface( XPropertySet.class, obj );
	}
	public static void moveButtonsToPage(XSpreadsheet xSpreadsheetx) throws Exception {
		XDrawPageSupplier xDrawPageSupplier = XDrawPageSupplier(xSpreadsheetx);
		XDrawPage xDrawPage = xDrawPageSupplier.getDrawPage();
		XFormsSupplier xFormsSupplier = XFormsSupplier(xDrawPage);
		XNameContainer xNameContainer = xFormsSupplier.getForms();
		XIndexAccess xIndexAccess1 = (XIndexAccess) UnoRuntime.queryInterface(XIndexAccess.class, xNameContainer);
		int fromsCountLimit = xIndexAccess1.getCount();
		for(int formsCount=0;formsCount<fromsCountLimit;formsCount++) {
			Object formObj = xIndexAccess1.getByIndex(formsCount);
			if(formObj != null)
			{
				XIndexAccess xIndexAccess2 = (XIndexAccess) UnoRuntime.queryInterface(XIndexAccess.class, formObj);
				int ctrlLimt=xIndexAccess2.getCount();
				int ctrlCount=0;
				while(ctrlCount<ctrlLimt)
				{
					Object ctrl         = xIndexAccess2.getByIndex(ctrlCount);
					XPropertySet xPropertySet3     = (XPropertySet) UnoRuntime.queryInterface(XPropertySet.class, ctrl);
					XNamed xname     = XNamed(ctrl);
					String ctrlName = xname.getName();
					String onClick    = null;
					XShape drawButton = getDrawButton(ctrlName,xDrawPage, true);
					if(drawButton!=null) {
						try{
							onClick=xPropertySet3.getPropertyValue("HelpURL").toString();
						} catch(Exception ne) {
							logger.log(Level.WARNING,null,ne);
						}
						if(onClick == null || (onClick.trim().length() == 0 || onClick.indexOf(".")<0)) {
							XPropertySet drawBtnProps =    XPropertySet(drawButton);
							Object any =  drawBtnProps.getPropertyValue("Anchor");
							try{
								XSpreadsheet tempXSheet = (XSpreadsheet)UnoRuntime.queryInterface(XSpreadsheet.class, any);
								if(tempXSheet == null) {
									Point ctrlPosition = drawButton.getPosition();
									drawBtnProps.setPropertyValue("Anchor",xSpreadsheetx);
									drawButton.setPosition(ctrlPosition);
								}
							} catch(Exception x){
								logger.log(Level.WARNING,"Exception in tempXSheet:",x);
							}
						}
					}
					ctrlCount++;
				}
			}
		}
	}

	public static XShape getDrawButton(String controlname, XDrawPage drawpage, boolean isFromMoveButtonsToPage)
	{
		XShape buttonObj=null;
		try
		{
			int drawsCountLimit = drawpage.getCount();
			for(int i=0;i<drawsCountLimit;i++) {
				Object draw = drawpage.getByIndex(i);
				if ( draw != null)
				{
					try{
						XShape tmpButtonObj = XShape(draw);
						if(tmpButtonObj.getShapeType().equals("com.sun.star.drawing.ControlShape")){
							XControlShape controlshape = XControlShape(draw);
							Object control=controlshape.getControl();
							if(control == null) {
								continue;
							}
							XNamed xname = XNamed(control);
							XPropertySet xPropertySet = (XPropertySet) UnoRuntime.queryInterface(XPropertySet.class, control);
							String defaultctrl = xPropertySet.getPropertyValue("DefaultControl").toString();
							if(defaultctrl.equals("com.sun.star.form.control.CommandButton") && xname.getName().equals(controlname)){
								buttonObj = tmpButtonObj;
								if(isFromMoveButtonsToPage)
								{
									XPropertySet drawBtnProps = XPropertySet(buttonObj);
									Object any =  drawBtnProps.getPropertyValue("Anchor");
									try
									{
										XSpreadsheet tempXSheet = (XSpreadsheet)UnoRuntime.queryInterface(XSpreadsheet.class, any);
										if(tempXSheet == null)
										{
											break;
										}
									}
									catch(Exception x)
									{
										logger.log(Level.WARNING,"Exception in tempXSheet:",x);
									}
								}
								else
								{
									break;
								}
							}
						}
					}catch(Exception e1){
						logger.log(Level.WARNING,null,e1);
					}
				}
			}
		}catch(Exception e){
			logger.log(Level.WARNING,null,e);
		}
		return buttonObj;
	}

	public static void convertOOBasicMacroCodeToExcelMacroCode(XComponent component)
	{

		XPropertySet propertySet = (XPropertySet)UnoRuntime.queryInterface(XPropertySet.class, component);
		if(propertySet == null)
		{
			return;
		}
		Object obj = null;
		try
		{
			Any any = (Any)propertySet.getPropertyValue("BasicLibraries");
			XLibraryContainer libraryContainer = (XLibraryContainer)UnoRuntime.queryInterface(XLibraryContainer.class, any);
			XNameAccess nameAccess = (XNameAccess)UnoRuntime.queryInterface(XNameAccess.class, libraryContainer);
			//TODO try commenting this line

			libraryContainer.loadLibrary("Standard");                //No I18N
			if(nameAccess.hasByName("VBAProject")){
				obj = nameAccess.getByName("VBAProject");            //No I18N
			}else{
				obj = nameAccess.getByName("Standard");                //No I18N
			}
		}
		catch(Exception ex)
		{
			logger.log(Level.WARNING, "There is no macro code to convert in this document");
		}
		if(obj == null) { return;}
		XNameAccess nameAccess = (XNameAccess)UnoRuntime.queryInterface(XNameAccess.class, obj);
		if(nameAccess==null) { return;}
		String[] moduleNames = nameAccess.getElementNames();
		//getVisibleSheetNames(XSpreadsheetDocument xSpreadsheetDocument)
		if(moduleNames == null)
		{
			return;
		}
		for(int i=0; i<moduleNames.length; i++)
		{
			try
			{
				String moduleContent = null;
				if(nameAccess.hasByName(moduleNames[i])){
					moduleContent = (String)nameAccess.getByName(moduleNames[i]);
				}
				if(moduleContent == null)
				{
					continue;
				}
				//Convert Macro code
				StringBuffer buff = new StringBuffer(moduleContent.length());
				String[] lines = moduleContent.split("\n");
				int length = lines.length;
				int j=0;
				if(length > 0 && (lines[0].trim().startsWith("Rem Attribute VBA_ModuleType=VBAModule") == true ||
						lines[0].trim().startsWith("Rem Attribute VBA_ModuleType=VBADocumentModule") == true ||
						lines[0].trim().startsWith("Rem Attribute VBA_ModuleType=VBAFormModule")))
				{
					j++;
				}
				else
				{
					continue;
				}
				for(;j<length; j++)
				{
					if(lines[j].trim().startsWith("Option VBASupport") == true)
					{
						continue;
					}
					else
					{
						buff.append(lines[j].toString());
						buff.append("\n");
					}
				}
				XNameReplace replace = (XNameReplace)UnoRuntime.queryInterface(XNameReplace.class, obj);
				if(replace != null)
				{
					replace.replaceByName(moduleNames[i],buff.toString());
				}
				else
				{
					XNameContainer con = (XNameContainer)UnoRuntime.queryInterface(XNameContainer.class, obj);
					con.removeByName(moduleNames[i]);
					con.insertByName(moduleNames[i],buff.toString());
				}
			}
			catch(Exception ex)
			{
				//logger.log(Level.WARNING,null,ex);
				continue;
			}
		}
	}

	public static void saveFreezePaneInfoFromOO(String docOwner, String docId, XComponent xComp) {
		try {
			String propValue = getCustomProperty("FreezeInfo", xComp);                //No I18N
			XSpreadsheetDocument xSpreadsheetDocument = (XSpreadsheetDocument)UnoRuntime.queryInterface(
					XSpreadsheetDocument.class, xComp);
			if(propValue != null) {
				String[] split = propValue.split("#");                    //No I18N
				for(int s=0;s<split.length;s++) {
					String value = split[s];
					String[] str = value.split("@");
					String sheetName = str[0];
					int fR = Integer.parseInt(str[1]);
					int fC = Integer.parseInt(str[2]);
					String freezeType = "";
					boolean isColsOnlyFreezed = false;
					boolean isRowsOnlyFreezed = false;
					boolean isColsAndRowsFreezed = false;
					int tempFR = fR - 1;
					int tempFC = fC - 1;
					if(tempFR >= 0) {
						isRowsOnlyFreezed = true;
					}
					if(tempFC >= 0) {
						isColsOnlyFreezed = true;
					}
					if(isColsOnlyFreezed && isRowsOnlyFreezed) {
						isColsAndRowsFreezed = true;
						isColsOnlyFreezed = false;
						isRowsOnlyFreezed = false;
					}
					if(isColsOnlyFreezed) {
						freezeType = "COL";                        //No I18N
					} else if(isRowsOnlyFreezed) {
						freezeType = "ROW";                        //No I18N
					} else if(isColsAndRowsFreezed) {
						freezeType = "ALL";                        //No I18N
					}
					//Add Only If The Sheet Name Exists.
					try{
						XSpreadsheet xSpreadsheet = (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class,xSpreadsheetDocument.getSheets().getByName(sheetName));
						if(xSpreadsheet != null) {
							//							DocumentUtils.setFreezedPane(docId, docOwner,  sheetName, freezeType, fR, fC);
						}
					}
					catch(Exception e){
						logger.log(Level.INFO, "This Sheet Does not exist");
						//Excel is trying to read the Freezepane Information of deted sheets from meta.xml
					}

				}
			}
		} catch(Exception e) {
			logger.log(Level.WARNING,null,e);
		}
	}

	public static int isFileSizeSupported (HttpServletRequest request, XSpreadsheetDocument xSpreadsheetDocument, Object ownerZUID, long documentId,  String apiKeyID, String docOwner) {
		try {
			// parse the file and do the check here using odsworkbook parser
			ODSSizeAndProtectionTransformer transformer  = new ODSSizeAndProtectionTransformer();
			ODSWorkbookParser  parser         = new ODSWorkbookParser(transformer, true); // data only mode

			//NON Authenticated Remote  API cases need to read the streams as "remoteapi" to return the correct failure message.
			if(ownerZUID.toString().equalsIgnoreCase("-1")){
				ownerZUID = docOwner;
			}
			InputStream is = ZSStore.getReadStream(ownerZUID, docOwner, documentId, documentId, FileName.DOCUMENT , FileExtn.ODS, null); //In this case no need to send docOwner name ~Mani
			try {
				parser.parse(is, "content.xml"); //No I18N
			} catch(Exception e) {
				Throwable cause = e.getCause();
				if(cause != null) {
					String exceptionMessage = cause.getMessage();
					switch(exceptionMessage) {
					case ErrorCode.ERROR_ROW_LIMIT_EXCEED:
						logger.log(Level.INFO, "No of Rows exceeded MAXNUMOFROWS :: docId: {0}", documentId); //No I18N
						return ZFSNGConstants.CONVERSION_FAILURE_NO_OF_ROWS_EXCEEDED;
					case ErrorCode.ERROR_COLUMN_LIMIT_EXCEED:
						logger.log(Level.INFO, "No of Cols exceeded MAXNUMOFCOLS :: docId: {0}", documentId); //No I18N
						return ZFSNGConstants.CONVERSION_FAILURE_NO_OF_COLUMNS_EXCEEDED;
					case ErrorCode.ERROR_CELL_LIMIT_EXCEED:
						logger.log(Level.INFO, "No of Cells exceeded MAXNUMOFCELLS :: docId: {0}", documentId); //No I18N
						return ZFSNGConstants.CONVERSION_FAILURE_NO_OF_CELLS_EXCEEDED;
					case ErrorCode.ERROR_PASSWORD_PROTECTED_DOCUMENT:
						logger.log(Level.INFO, "Password protected document with Hidden Rows / Columns :: docId: {0}", documentId); //No I18N
						return ZFSNGConstants.CONVERSION_FAILURE_PASS_PROTECTED;
					}
				}
				throw e;
			}
		} catch (Exception e) {
			logger.log(Level.WARNING,"Error while converting/import of document DocId:: "+documentId,e);
			return ZFSNGConstants.CONVERSION_FAILURE;
		}
		return ZFSNGConstants.CONVERSION_SUCCESS;
	}

	public static boolean saveDocument(WorkbookContainer container, String docName, String docId, XSpreadsheetDocument xDocument, String activeSheet, String fileFormat, String versionNo, boolean isSxcDelete, String rowHeight, boolean hasToWriteConfFile) throws Exception {
		FSOOOutputStream fsooos = null;
		InputStream fis = null;
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;
		OutputStream fos = null;
		String docOwner    =    container.getDocOwner();
		long documentId = Long.parseLong(docId);
		if(isSxcDelete)
		{
			// This will convert the sxc directly to ods
			// here we need to convert the OO basic macros to Excel macros hence this call
			checkMigrationCode((XComponent)UnoRuntime.queryInterface(XComponent.class, xDocument));
		}
		else
		{
			// Incase of Macros, if user has defined new or edited existing one
			// then the macros code (module content) will be updated/added
			saveMacroCode(xDocument);
		}
		String tmpfname = null;
		String fname = null;
		try {
			String sFilter = null;
			XServiceInfo xInfo = (XServiceInfo)UnoRuntime.queryInterface(XServiceInfo.class, xDocument);
			if(! xInfo.supportsService("com.sun.star.sheet.SpreadsheetDocument")) {
				return false;
			}
			//TODO: Have to check this
			/*if(fileFormat == null) {
                fileFormat = DocumentUtils.getFileFormat(store, docId, null);
                if(fileFormat == null) {
                    if(EngineConstants.switchengine) {
                        fileFormat = EngineConstants.ENGINE_ODS_FILE_FORMAT;
                    } else {
                        fileFormat = Constants.DEFAULT_FILE_FORMAT;
                    }
                }
            }*/
			//For To Handle Fragmented File.
			//if(EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT.equals(fileFormat)) {
			fileFormat = EngineConstants.ENGINE_ODS_FILE_FORMAT;
			//}
			sFilter = getFilter(fileFormat);
			int hashcode = xDocument.hashCode();
			long currenttime = System.currentTimeMillis();
			fname = Constants.LOCATION + docId + fileFormat;
			tmpfname = Constants.LOCATION + docId + "-" + hashcode + "-" + currenttime + fileFormat + "~";
			if (versionNo != null && !versionNo.equals("")) {
				fname = Constants.LOCATION + docId + "/" + "version" + "/" + docId + "_" + versionNo + fileFormat;            //No I18N
			}
			Store store = null;
			SheetFileInfo sheetfileInfo = null;
			if(versionNo == null) {
				store = StoreFactory.getInstance().getDocumentStore(Long.parseLong(container.getDocOwnerZUID()), container.getDocOwner(),documentId);
				sheetfileInfo = store.getFileInfo(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ODS);
				if(sheetfileInfo == null){
					sheetfileInfo = store.createFileInfo(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ODS); // Assumed that, it is the first write ~mani
				}
			}
			//OutputStream fos = store.write(fname, false);
			/*
			 * Following two line were commented and a new constructor FSOOOutputStream(FileStore fs, String filePath) called
			 * to avoid read time out error.
			 */
			//fos = store.write(tmpfname, false);
			//fsooos = new FSOOOutputStream(fos);
			//fsooos = new FSOOOutputStream(store, tmpfname);
			fsooos = new FSOOOutputStream(store, sheetfileInfo);
			PropertyValue[] lProperties = new PropertyValue[4];
			lProperties[0] = new PropertyValue();
			lProperties[0].Name = "FilterName";                //No I18N
			lProperties[0].Value = sFilter;
			lProperties[1] = new PropertyValue();
			lProperties[1].Name = "Overwrite";                //No I18N
			lProperties[1].Value = Boolean.TRUE;
			lProperties[2] = new PropertyValue();
			lProperties[2].Name = "DocumentTitle";            //No I18N
			lProperties[2].Value = docName;
			lProperties[3] = new PropertyValue();
			lProperties[3].Name = "OutputStream";            //No I18N
			lProperties[3].Value = fsooos;
			XStorable xStore = (XStorable)UnoRuntime.queryInterface(XStorable.class, xDocument);
			//Added To Write In Conf File.
			String[] sheetNames = null;
			ArrayList<Integer> arrList = null;
			boolean isDocumentContainingDynamicFormulae = false;
			//will be false in import Case.
			if(hasToWriteConfFile && activeSheet != null) {
				//sheetNames = DocumentUtils.getSheetNames(xDocument);
				//sheetNames = xDocument.getSheets().getElementNames();
				//For To Handle Hidden SHeets.
				sheetNames = getVisibleSheetNames(xDocument);
				XSpreadsheet xSpreadsheet = (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class, xDocument.getSheets().getByName(activeSheet));
				arrList = getSheetDim(xSpreadsheet);
				//long time1 = System.currentTimeMillis();
				isDocumentContainingDynamicFormulae = isDocumentContainingDynamicFormulae(xDocument);
				//logger.info("Time Taken To Find dynamic formulas coorence=-=-=-=-=-0=->"+(System.currentTimeMillis() - time1));
				//logger.info("isDocumentContainingDynamicFormulae---------------->"+isDocumentContainingDynamicFormulae);
			}
			//Upto This.
			try {
				xStore.storeToURL("private:stream",lProperties);                            //No I18N
				//will be false in import Case.
				//Save It After Saving The Document(Write Only If OO Is Not Killed).
				//                if(hasToWriteConfFile && activeSheet != null) {
				//
				//                        EngineUtils1.writeSheetMetaInfo(container, false, isDocumentContainingDynamicFormulae);
				//                        //EngineUtils.writeSheetMetaInfo(docId , activeSheet, accountid, sheetNames, usedRow, usedCol, rowHeight, isDocumentContainingDynamicFormulae, false); // Last argument is isDoccontainingarrayformula.
				//                }
				//Upto This
			} catch(Exception e) {
				logger.log(Level.WARNING,"[CONVERSION:IMPORT] Document not saved.",e);
				return false;
			}
		} finally {
			if(fsooos != null) {
				fsooos.closeOutput();
			}
			//		}
			//		try {
			//			//            if(!store.exists(tmpfname))
			//			//            {
			//			//                throw new Exception("Problem while saving document");
			//			//            }
			//			//EngineUtils1.writeToOriginalFile(container, tmpfname, fname);
			//		} finally {
			if(fis != null) {
				fis.close();
			}
			if(bis != null) {
				bis.close();
			}
			if(bos != null) {
				bos.close();
			}
			if(fos != null) {
				fos.close();
			}
		}
		//        if(store.exists(tmpfname)) {
		//            store.delete(tmpfname);
		//        }
		//        if (EngineConstants.switchengine && isSxcDelete && store.exists(Constants.LOCATION + docId + Constants.DEFAULT_FILE_FORMAT))
		//        {
		//                store.delete(Constants.LOCATION + docId + Constants.DEFAULT_FILE_FORMAT);
		//                logger.info("Engine: Deleting the sxc format as document saved as ods :docId: "+docId);
		//        }
		return true;
	}

	public static void closeComponent(XComponent comp)
	{
		XCloseable xCloseable = null;
		if(comp != null)
		{
			try
			{
				xCloseable = (XCloseable) UnoRuntime.queryInterface(XCloseable.class,comp);
				if (xCloseable != null )
				{
					xCloseable.close(false);
				}
				else
				{
					comp.dispose();
				}
			}
			catch(Exception e)
			{
				logger.log(Level.WARNING,null,e);
			}
		}
	}

	public static Object getLocaleDetails(String langCode) {
		Map<String, Locale> LOCALE_MAP = new CaseInsensitiveMap();
		LOCALE_MAP.put("en", Locale.ENGLISH);
		LOCALE_MAP.put("fr", Locale.FRENCH);
		LOCALE_MAP.put("de", Locale.GERMAN);
		LOCALE_MAP.put("ja", Locale.JAPANESE);
		LOCALE_MAP.put("it", Locale.ITALIAN);
		LOCALE_MAP.put("zh", Locale.CHINESE);
		LOCALE_MAP.put("es", new Locale("es")); // SPANISH
		LOCALE_MAP.put("bg", new Locale("bg"));    // BULGARIAN
		LOCALE_MAP.put("ca", new Locale("ca")); // CATALAN
		LOCALE_MAP.put("cs", new Locale("cs")); // CZECH
		LOCALE_MAP.put("da", new Locale("da")); // DANISH
		LOCALE_MAP.put("nl", new Locale("nl")); // DUTCH
		LOCALE_MAP.put("eo", new Locale("eo")); // ESPERANTO
		LOCALE_MAP.put("no", new Locale("no")); // NORWEGIAN
		LOCALE_MAP.put("pt", new Locale("pt")); // PORTUGUESE
		LOCALE_MAP.put("ro", new Locale("ro")); // ROMANIAN
		LOCALE_MAP.put("ru", new Locale("ru")); // RUSSIAN
		LOCALE_MAP.put("sv", new Locale("sv")); // SWEDISH
		LOCALE_MAP.put("tr", new Locale("tr")); // TURKISH
		LOCALE_MAP.put("pl", new Locale("pl")); // POLISH
		LOCALE_MAP.put("sr", new Locale("sr")); // Serbian
		LOCALE_MAP.put("hu", new Locale("hu")); // Hungarian
		LOCALE_MAP.put("uk", new Locale("uk")); // Ukranian
		LOCALE_MAP.put("ta", new Locale("ta")); // TAMIL
		LOCALE_MAP.put("ar", new Locale("ar")); // Arabic - Saudi Arabia
		LOCALE_MAP.put("ur", new Locale("pk")); // Urdu - Pakistan
		LOCALE_MAP.put("iw", new Locale("iw")); // Hebrew
		LOCALE_MAP.put("he", new Locale("he")); // Hebrew (jdk17 update- "he" is now the language code for "Hebrew" instead of "iw".)  //No I18N

                LOCALE_MAP.put("as", new Locale("as")); // Assam
                LOCALE_MAP.put("in", new Locale("in")); // Bahasa Indonesia
                LOCALE_MAP.put("ms", new Locale("ms")); // Bahasa Melayu
                LOCALE_MAP.put("bn", new Locale("bn")); // Bengali
                LOCALE_MAP.put("hr", new Locale("hr")); // Croatian (hrvatski)
                LOCALE_MAP.put("gu", new Locale("gu")); // Gujarathi
                LOCALE_MAP.put("hi", new Locale("hi")); // Hindi
                LOCALE_MAP.put("id", new Locale("id")); // Indonesian
                LOCALE_MAP.put("kn", new Locale("kn")); // kannadam
                LOCALE_MAP.put("ko", new Locale("ko")); // korean
                LOCALE_MAP.put("mr", new Locale("mr")); // Marathi
                LOCALE_MAP.put("pa", new Locale("pa")); // punjabi
                LOCALE_MAP.put("te", new Locale("te")); // Telugu
                LOCALE_MAP.put("th", new Locale("th")); // Thai
                LOCALE_MAP.put("vi", new Locale("vi")); // Vietnamese

		//Newly added languages
		LOCALE_MAP.put("en", Locale.UK); // English-British(en_GB)
		LOCALE_MAP.put("fr", Locale.CANADA_FRENCH); // French-Canada(fr_CA)
		LOCALE_MAP.put("fi", new Locale("fi")); // Finnish(fi)
		LOCALE_MAP.put("el", new Locale("el")); // Greek(el)
		LOCALE_MAP.put("si", new Locale("si")); // Sinhala(si)
		LOCALE_MAP.put("fa", new Locale("fa")); // Farsi/Persian(fa)

		return LOCALE_MAP.get(langCode);
	}

	public static String getFilter(String ftype) {
		String sFilter = "MS Excel 97";                    //No I18N
		if(ftype.equals(".xls")) {
			sFilter = "MS Excel 97";                    //No I18N
		}
		else if(ftype.equals(".sxc")) {
			sFilter = "StarOffice XML (Calc)";            //No I18N
		}
		else if(ftype.equals(".ods")) {
			sFilter = "calc8";                            //No I18N
		}
		else if(ftype.equals(".ots")) {
			sFilter = "calc8_template";                    //No I18N
		}
		else if(ftype.equals(".stc")) {
			sFilter = "calc_StarOffice_XML_Calc_Template";        //No I18N
		}
		else if(ftype.equals(".xlt")) {
			sFilter = "MS Excel 97 Vorlage/Template";            //No I18N
		}
		else if(ftype.equals(".vor")) {
			sFilter = "StarCalc 5.0 Vorlage/Template";            //No I18N
		}
		else if(ftype.equals(".sdc")) {
			sFilter = "StarCalc 5.0";                            //No I18N
		}
		else if(ftype.equals(".xml")) {
			sFilter = "MS Excel 2003 XML";                        //No I18N
		}
		else if(ftype.equals(".csv") || ftype.equals(".tsv")) {
			sFilter = "Text - txt - csv (StarCalc)";            //No I18N
		}
		else if(ftype.equals(".html")) {
			sFilter = "HTML (StarCalc)";                                    //No I18N
		}
		else if(ftype.equals(".xhtml")) {
			sFilter = "XHTML Calc File";                        //No I18N
		}
		else if(ftype.equals(".xlsx")) {
			sFilter = "Calc MS Excel 2007 XML";                     //No I18N
		}
		return sFilter;
	}

	public static Object getFilterName(String fileType){
		// map has list of supported 'push Formats'
		Map<String, String> FILTER_MAP = new CaseInsensitiveMap();
		FILTER_MAP.put("xls", "MS Excel 97");
		FILTER_MAP.put(".xls", "MS Excel 97");
		FILTER_MAP.put("xlsx", "MS Excel 2007 XML");
		FILTER_MAP.put(".xlsx", "MS Excel 2007 XML");
		FILTER_MAP.put("ods", "calc8");
		FILTER_MAP.put(".ods", "calc8");
		FILTER_MAP.put("sxc", "StarOffice XML (Calc)");
		FILTER_MAP.put(".sxc", "StarOffice XML (Calc)");
		FILTER_MAP.put("pdf", "calc_pdf_Export");
		FILTER_MAP.put(".pdf", "calc_pdf_Export");
		FILTER_MAP.put("csv", "Text - txt - csv (StarCalc)");
		FILTER_MAP.put(".csv", "Text - txt - csv (StarCalc)");
		FILTER_MAP.put("tsv", "Text - txt - csv (StarCalc)");
		FILTER_MAP.put(".tsv", "Text - txt - csv (StarCalc)");
		FILTER_MAP.put("json", "Text - txt - csv (StarCalc)");
		FILTER_MAP.put("zsheet","zsheet");
		FILTER_MAP.put(".zsheet","zsheet");
		return FILTER_MAP.get(fileType);
	}

	public static void checkMigrationCode(XComponent document) throws Exception
	{
		boolean runMigration = false;
		//Libre Office Code Changes
		XDocumentPropertiesSupplier xDocumentInfoSupplier = (XDocumentPropertiesSupplier)UnoRuntime.queryInterface(XDocumentPropertiesSupplier.class, document);
		XDocumentProperties           xDocumentInfo         = xDocumentInfoSupplier.getDocumentProperties();
		//        XDocumentInfoSupplier xDocumentInfoSupplier = (XDocumentInfoSupplier)UnoRuntime.queryInterface(XDocumentInfoSupplier.class, document);
		//        XDocumentInfo xDocumentInfo = xDocumentInfoSupplier.getDocumentInfo();
		XPropertySet propSet = (XPropertySet)UnoRuntime.queryInterface(XPropertySet.class, xDocumentInfo);
		try
		{
			Object obj = propSet.getPropertyValue("ZohoSheetVersion");
			if(obj instanceof Any)
			{
				Object temp = ((Any)obj).getObject();
				if(temp == null)
				{
					runMigration = true;
				}
			}
			else if(obj instanceof String)
			{
				String value = (String)obj;
				if(value.trim().equals("2.0") == false)
				{
					runMigration = true;
				}
			}
		}
		catch(UnknownPropertyException e)
		{
			runMigration = true;
		}
		if(runMigration == true)
		{
			//Property doesn't exists. Hence run migration code
			ImportExportUtil.addCustomProperty("ZohoSheetVersion", "2.0", document);                    //No I18N
			ImportExportUtil.convertOOBasicMacroCodeToExcelMacroCode(document);
		}
	}

//	public static XComponent getSpreadsheetDocComponent(XInputStream inStream, String contentType, HttpServletRequest request, HttpServletResponse response) throws Exception
//
//	{
//		ZSStats.incrementByCustomValue(ZSStats.OO_COMPONENT_CREATION);
//
//		// -- Desktop Object creation -- //
//		ConnectionPool ConnectionPool=new ConnectionPool();
//		ConnectionObject connectionoObject = new ConnectionObject();
//		connectionoObject = ConnectionPool.getRemoteDesktop();
//		XComponentLoader xCompLoader = connectionoObject.getxComponentLoader();
//		//lIBOHost = loConnectionoObject.getLiboHost();
//		if (xCompLoader == null) {
//			return null;
//		}
//
//		// -- OO Component Property Settings -- //
//		PropertyValue[] props;
//		if(contentType == null) {
//			props = new PropertyValue[2];
//		} else  {
//			String type = contentType.toLowerCase();
//			boolean isCSV = ("csv".equals(type) || ".csv".equals(type)) ? true : false;//No I18N
//			boolean isTSV = ("tsv".equals(type) || ".tsv".equals(type)) ? true : false;//No I18N
//			if (isCSV) {
//				props = new PropertyValue[4];
//				props[3] = makePropertyValue("FilterOptions", "44,34,0,1,1/1/1/1/1/1/1");//No I18N
//			} else if(isTSV) {
//				props = new PropertyValue[4];
//				props[3] = makePropertyValue("FilterOptions", "9,34,0,1,1/1/1/1/1/1/1");//No I18N
//			} else {
//				props = new PropertyValue[3];
//			}
//			props[2] = makePropertyValue("FilterName", (String) getFilterName(contentType));//No I18N
//		}
//		props[0] = makePropertyValue("Hidden", Boolean.TRUE);//No I18N
//		props[1] = makePropertyValue("InputStream", inStream);//No I18N
//
//		LifeSpanController lsc =  new LifeSpanController(Thread.currentThread(), "Libre Office Component Creation Thread", null, request, response); //NO I18N
//		lsc.control();
//
//		XComponent xSpreadsheetComponent = xCompLoader.loadComponentFromURL("private:stream", "_blank", 0, props);//No I18N
//
//		lsc.cancel();
//
//		return xSpreadsheetComponent;
//	}

	public static PropertyValue makePropertyValue(String propertyName, Object value)
	{
		PropertyValue propValue = new PropertyValue();
		propValue.Name = propertyName;
		propValue.Value = value;
		return propValue;
	}

	public static XTableChartsSupplier XTableChartsSupplier( Object obj )
	{
		return (XTableChartsSupplier) UnoRuntime.queryInterface( XTableChartsSupplier.class, obj );
	}
	static public XShape XShape(Object obj)
	{
		XShape xShape = (XShape)UnoRuntime.queryInterface(XShape.class,obj);
		return xShape;
	}
	static public XTableChart XTableChart( Object obj )
	{
		return (XTableChart) UnoRuntime.queryInterface( XTableChart.class, obj );
	}
	static public XEmbeddedObjectSupplier XEmbeddedObjectSupplier( Object obj )
	{
		return (XEmbeddedObjectSupplier) UnoRuntime.queryInterface( XEmbeddedObjectSupplier.class, obj );
	}
	static public XChartDocument XChartDocument( Object obj )
	{
		return (XChartDocument) UnoRuntime.queryInterface( XChartDocument.class, obj );
	}
	public static void setBooleanProperty( Object obj, String propName, boolean value ) throws UnknownPropertyException, PropertyVetoException, IllegalArgumentException, WrappedTargetException
	{
		setProperty(obj, propName, value);
	}
	public static boolean getBooleanProperty( Object obj, String propName ) throws  UnknownPropertyException, WrappedTargetException
	{
		Object value = getProperty( obj, propName );
		if((value != null) && (value instanceof Boolean))
		{
			Boolean booleanValue = (Boolean)value;
			return booleanValue.booleanValue();
		}
		return false;
	}
	public static void setStringProperty(Object obj, String propName, String value) throws UnknownPropertyException, PropertyVetoException, IllegalArgumentException, WrappedTargetException
	{
		setProperty( obj, propName, value );
	}
	public static String getStringProperty(Object obj, String propName) throws UnknownPropertyException, WrappedTargetException
	{
		Object value = getProperty(obj, propName);
		if((value != null) && (value instanceof String))
		{
			String stringValue = (String)value;
			return stringValue;
		}
		return "";
	}
	public static void setProperty(Object obj, String propName, Object value) throws UnknownPropertyException, PropertyVetoException, IllegalArgumentException, WrappedTargetException
	{
		XPropertySet obj_XPropertySet;
		if( obj instanceof XPropertySet )
		{
			// If the right interface was passed in, just typecaset it.
			obj_XPropertySet = (XPropertySet) obj;
		}
		else
		{
			// Get a different interface to the drawDoc.
			// The parameter passed in to us is the wrong interface to the object.
			obj_XPropertySet = XPropertySet( obj );
		}
		// Now just call our sibling using the correct interface.
		setProperty( obj_XPropertySet, propName, value );
	}
	public static void setProperty( XPropertySet obj, String propName, Object value ) throws UnknownPropertyException, PropertyVetoException, IllegalArgumentException, WrappedTargetException
	{
		obj.setPropertyValue( propName, value );
	}
	public static Object getProperty(Object obj, String propName) throws UnknownPropertyException, WrappedTargetException
	{
		XPropertySet obj_XPropertySet;
		if( obj instanceof XPropertySet )
		{
			// We need the XPropertySet interface.
			obj_XPropertySet = (XPropertySet) obj;
		}
		else
		{
			// Get a different interface to the drawDoc.
			// The parameter passed in to us is the wrong interface to the object.
			obj_XPropertySet = XPropertySet( obj );
		}
		// Now just call our sibling using the correct interface.
		return getProperty( obj_XPropertySet, propName );
	}
	public static Object getProperty(XPropertySet obj, String propName) throws UnknownPropertyException, WrappedTargetException
	{
		return obj.getPropertyValue( propName );
	}
	public static CellRangeAddress createCellRangeAddress(XSpreadsheet xSheet, String aRange)
	{
		XCellRangeAddressable xAddr = (XCellRangeAddressable)    UnoRuntime.queryInterface(XCellRangeAddressable.class,xSheet.getCellRangeByName(aRange));
		return xAddr.getRangeAddress();
	}
	static public XControlShape XControlShape(Object obj)
	{
		XControlShape XControlShape = (XControlShape)UnoRuntime.queryInterface(XControlShape.class,obj);
		return XControlShape;
	}
	static public XSpreadsheetDocument XSpreadsheetDocument( Object obj ) {
		return (XSpreadsheetDocument) UnoRuntime.queryInterface( XSpreadsheetDocument.class, obj );
	}
	public static XSpreadsheet XSpreadsheet(Object obj)
	{
		return (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class, obj);
	}
	static public XAxisXSupplier XAxisXSupplier(Object obj)
	{
		return (XAxisXSupplier) UnoRuntime.queryInterface(XAxisXSupplier.class, obj);
	}
	static public XAxisYSupplier XAxisYSupplier(Object obj)
	{
		return (XAxisYSupplier) UnoRuntime.queryInterface(XAxisYSupplier.class, obj);
	}
	static public XMultiServiceFactory XMultiServiceFactory(Object obj)
	{
		return (XMultiServiceFactory) UnoRuntime.queryInterface(XMultiServiceFactory.class, obj);
	}
	static public XDiagram XDiagram(Object obj)
	{
		return (XDiagram) UnoRuntime.queryInterface(XDiagram.class, obj);
	}
	public static com.sun.star.awt.Rectangle makeRectangle( int x, int y, int width, int height )
	{
		com.sun.star.awt.Rectangle rect = new com.sun.star.awt.Rectangle();
		rect.X = x;
		rect.Y = y;
		rect.Width = width;
		rect.Height = height;
		return rect;
	}

	/*
		Since the .ods files stored in our dfs are no more purely ODS files,
		supplying an ODS file from the dfs when asked for ODS export is not right.
		Hence, deprecating this API.
	@Deprecated Use EngineUtils1.createTempODSFile() instead
	public static File getTempODSFile(WorkbookContainer container, String zfsngVersionNo, String sheetVersionNo) throws Exception {
		if("".equals(zfsngVersionNo) || "undefined".equals(zfsngVersionNo) || "".equals(sheetVersionNo) || "undefined".equals(sheetVersionNo)) {
			zfsngVersionNo = null;
			sheetVersionNo = null;
		}

		File tempODSFile = null;

		String rKey = container.getResourceKey();
		String docOwner = container.getDocOwner();
		String docId = container.getDocId();

		logger.log(Level.INFO, "EXPORT :::: ImportExportUtil.getTempODSFile() called for rKey: {0} zfsngVersionNo: {1}", new Object[]{rKey, zfsngVersionNo});

		if(zfsngVersionNo == null) {
			try {
				boolean isActionsPending = RedisHelper.hlen(RedisHelper.ACTIONS_LIST + rKey) > 0;

				// In case if the temp actions are still not migrated to Redis list
				if (!isActionsPending) {
					Long tempActionsResId = container.getFragmentId(FileName.ACTIONSTEMP, false, null);
					if (tempActionsResId > 0) {
						isActionsPending = EngineUtils1.loadActionsList(container, tempActionsResId, FileName.ACTIONSTEMP).size() > 0;
					}
				}

				if(!isActionsPending) {
					zfsngVersionNo = ZohoFS.getTopVersion(container.getDocsSpaceId(), container.getResourceKey(), true, true);
					String zfsngVersionId = ZohoFS.getVersionIDForEditor(container.getDocOwner(), container.getResourceKey(), zfsngVersionNo);
					sheetVersionNo = DocumentUtils.getVersionNoforZFSNGVersion(container.getDocId(), docOwner, zfsngVersionId);
					logger.log(Level.INFO, "EXPORT :::: No pending actions for rKey: {0} So updating versionNo to zfsngVersionNo: {1}", new Object[]{rKey, zfsngVersionNo});
				}
			} catch(Exception e) {
				logger.log(Level.WARNING, "EXPORT :::: Exception when checking for pending actions for rKey: " + rKey, e);
			}
		}

		if(zfsngVersionNo != null) {
			try {
				long resourceId                 =     ZSStore.getVersionId(docOwner, Long.parseLong(docId), sheetVersionNo);
				Store store                     =     container.getStore(resourceId, FileName.VERSION);
				if(container.isFileExist(store, resourceId, FileName.VERSION , FileExtn.ODS)) {
					logger.log(Level.INFO, "EXPORT :::: Using version ODS file for rKey: {0} zfsngVersionNo: {1}", new Object[]{rKey, zfsngVersionNo});
					InputStream versionInputStream = null;
					OutputStream tempODSFileOutputStream = null;
					try {
						versionInputStream = container.getReadStream(store, resourceId, FileName.VERSION, FileExtn.ODS, null);
						String tempODSFilePath = EngineUtils1.createTempFilePath(container.hashCode()) + EngineConstants.ENGINE_ODS_FILE_FORMAT;
						logger.log(Level.INFO, "EXPORT :::: Copying version ODS to temp file: {0}", tempODSFilePath);
						tempODSFile = new File(tempODSFilePath);
						tempODSFileOutputStream = new FileOutputStream(tempODSFile);
						EngineUtils1.copyFile(versionInputStream, tempODSFileOutputStream);
					} catch(Exception e) {
						tempODSFile = null;
						logger.log(Level.WARNING, "EXPORT :::: Exception when getting ODS version file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo + " So discarding tempFile", e);
						try {
							if(tempODSFileOutputStream != null) {
								tempODSFileOutputStream.close();
								// Setting tempODSFileOutputStream to null, so that we don't try to close it again
								tempODSFileOutputStream = null;
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing temp ODS file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e1);
						}
						if(tempODSFile != null) {
							tempODSFile.delete();
						}
					} finally {
						try {
							if(versionInputStream != null) {
								versionInputStream.close();
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing ODS version file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e1);
						}
						try {
							if(tempODSFileOutputStream != null) {
								tempODSFileOutputStream.close();
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing temp ODS file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e1);
						}
					}
				}
			}catch(Exception e) {
				logger.log(Level.WARNING, "EXPORT :::: Exception when checking for ODS version file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e);
			}
		} else {
			try {
				long resourceId = Long.parseLong(docId);
				Store store = container.getStore(resourceId, FileName.DOCUMENT);
				if (container.isFileExist(resourceId, FileName.DOCUMENT, FileExtn.ODS)) {
					logger.log(Level.INFO, "EXPORT :::: Using document ODS file for rKey: {0} zfsngVersionNo: {1}", new Object[]{rKey, zfsngVersionNo});
					InputStream documentInputStream = null;
					OutputStream tempODSFileOutputStream = null;
					try {
						documentInputStream = container.getReadStream(store, resourceId, FileName.DOCUMENT, FileExtn.ODS, null);
						String tempODSFilePath = EngineUtils1.createTempFilePath(container.hashCode()) + EngineConstants.ENGINE_ODS_FILE_FORMAT;
						logger.log(Level.INFO, "EXPORT :::: Copying document ODS to temp file: {0}", tempODSFilePath);
						tempODSFile = new File(tempODSFilePath);
						tempODSFileOutputStream = new FileOutputStream(tempODSFile);
						EngineUtils1.copyFile(documentInputStream, tempODSFileOutputStream);
					} catch(Exception e) {
						tempODSFile = null;
						logger.log(Level.WARNING, "EXPORT :::: Exception when getting ODS document file for rKey: " + rKey + " So discarding tempFile", e);
						try {
							if(tempODSFileOutputStream != null) {
								tempODSFileOutputStream.close();
								// Setting tempODSFileOutputStream to null, so that we don't try to close it again
								tempODSFileOutputStream = null;
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing temp ODS file for rKey: " + rKey, e1);
						}
						if(tempODSFile != null) {
							tempODSFile.delete();
						}
					} finally {
						try {
							if(documentInputStream != null) {
								documentInputStream.close();
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing ODS document file for rKey: " + rKey, e1);
						}
						try {
							if(tempODSFileOutputStream != null) {
								tempODSFileOutputStream.close();
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing temp ODS file for rKey: " + rKey, e1);
						}
					}
				}
			}catch(Exception e) {
				logger.log(Level.WARNING, "EXPORT :::: Exception when checking for ODS document file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e);
			}
		}

		try {
			if(tempODSFile == null) {
				logger.log(Level.INFO, "EXPORT :::: No ODS file available for rKey: {0} zfsngVersionNo: {1} Hence creating it.", new Object[]{rKey, zfsngVersionNo});
				tempODSFile = EngineUtils1.createTempODSFile(container, zfsngVersionNo, false, null);
			}
		} catch (Exception e) {
			logger.log(Level.WARNING, "EXPORT :::: Exception when creating temp ODS File for the rKey: " + container.getResourceKey() + "  zfsngVersionNo: " + zfsngVersionNo, e);
			throw e;
		}

		return tempODSFile;
	}
	*/

	/*
	public static File getTempZSFile(WorkbookContainer container, String zfsngVersionNo, String sheetVersionNo) throws Exception {
		if("".equals(zfsngVersionNo) || "undefined".equals(zfsngVersionNo) || "".equals(sheetVersionNo) || "undefined".equals(sheetVersionNo)) {
			zfsngVersionNo = null;
			sheetVersionNo = null;
		}

		File tempODSFile = null;

		String rKey = container.getResourceKey();
		String docOwner = container.getDocOwner();
		String docId = container.getDocId();

		logger.log(Level.INFO, "EXPORT :::: ImportExportUtil.getTempZSFile() called for rKey: {0} zfsngVersionNo: {1}", new Object[]{rKey, zfsngVersionNo});

		if(zfsngVersionNo == null) {
			try {
				boolean isActionsPending = RedisHelper.hlen(RedisHelper.ACTIONS_LIST + rKey) > 0;

				// In case if the temp actions are still not migrated to Redis list
				if (!isActionsPending) {
					Long tempActionsResId = container.getFragmentId(FileName.ACTIONSTEMP, false, null);
					if (tempActionsResId > 0) {
						isActionsPending = EngineUtils1.loadActionsList(container, tempActionsResId, FileName.ACTIONSTEMP).size() > 0;
					}
				}

				if(!isActionsPending) {
					zfsngVersionNo = ZohoFS.getTopVersion(container.getDocsSpaceId(), container.getResourceKey(), true, true);
					String zfsngVersionId = ZohoFS.getVersionIDForEditor(container.getDocOwner(), container.getResourceKey(), zfsngVersionNo);
					sheetVersionNo = DocumentUtils.getVersionNoforZFSNGVersion(container.getDocId(), docOwner, zfsngVersionId);
					logger.log(Level.INFO, "EXPORT :::: No pending actions for rKey: {0} So updating versionNo to zfsngVersionNo: {1}", new Object[]{rKey, zfsngVersionNo});
				}
			} catch(Exception e) {
				logger.log(Level.WARNING, "EXPORT :::: Exception when checking for pending actions for rKey: " + rKey, e);
			}
		}

		if(zfsngVersionNo != null) {
			try {
				long resourceId                 =     ZSStore.getVersionId(docOwner, Long.parseLong(docId), sheetVersionNo);
				Store store                     =     container.getStore(resourceId, FileName.VERSION);
				if(container.isFileExist(store, resourceId, FileName.VERSION , FileExtn.ODS)) {
					logger.log(Level.INFO, "EXPORT :::: Using version ZS file for rKey: {0} zfsngVersionNo: {1}", new Object[]{rKey, zfsngVersionNo});
					InputStream versionInputStream = null;
					OutputStream tempODSFileOutputStream = null;
					try {
						versionInputStream = container.getReadStream(store, resourceId, FileName.VERSION, FileExtn.ODS, null);
						String tempODSFilePath = EngineUtils1.createTempFilePath(container.hashCode()) + EngineConstants.ENGINE_ODS_FILE_FORMAT;
						logger.log(Level.INFO, "EXPORT :::: Copying version ZS to temp file: {0}", tempODSFilePath);
						tempODSFile = new File(tempODSFilePath);
						tempODSFileOutputStream = new FileOutputStream(tempODSFile);
						EngineUtils1.copyFile(versionInputStream, tempODSFileOutputStream);
					} catch(Exception e) {
						tempODSFile = null;
						logger.log(Level.WARNING, "EXPORT :::: Exception when getting ZS version file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo + " So discarding tempFile", e);
						try {
							if(tempODSFileOutputStream != null) {
								tempODSFileOutputStream.close();
								// Setting tempODSFileOutputStream to null, so that we don't try to close it again
								tempODSFileOutputStream = null;
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing temp ODS file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e1);
						}
						if(tempODSFile != null) {
							tempODSFile.delete();
						}
					} finally {
						try {
							if(versionInputStream != null) {
								versionInputStream.close();
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing ZS version file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e1);
						}
						try {
							if(tempODSFileOutputStream != null) {
								tempODSFileOutputStream.close();
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing temp ZS file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e1);
						}
					}
				}
			}catch(Exception e) {
				logger.log(Level.WARNING, "EXPORT :::: Exception when checking for ZS version file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e);
			}
		} else {
			try {
				long resourceId = Long.parseLong(docId);
				Store store = container.getStore(resourceId, FileName.DOCUMENT);
				if (container.isFileExist(resourceId, FileName.DOCUMENT, FileExtn.ODS)) {
					logger.log(Level.INFO, "EXPORT :::: Using document ZS file for rKey: {0} zfsngVersionNo: {1}", new Object[]{rKey, zfsngVersionNo});
					InputStream documentInputStream = null;
					OutputStream tempODSFileOutputStream = null;
					try {
						documentInputStream = container.getReadStream(store, resourceId, FileName.DOCUMENT, FileExtn.ODS, null);
						String tempODSFilePath = EngineUtils1.createTempFilePath(container.hashCode()) + EngineConstants.ENGINE_ODS_FILE_FORMAT;
						logger.log(Level.INFO, "EXPORT :::: Copying document ODS to temp file: {0}", tempODSFilePath);
						tempODSFile = new File(tempODSFilePath);
						tempODSFileOutputStream = new FileOutputStream(tempODSFile);
						EngineUtils1.copyFile(documentInputStream, tempODSFileOutputStream);
					} catch(Exception e) {
						tempODSFile = null;
						logger.log(Level.WARNING, "EXPORT :::: Exception when getting ZS document file for rKey: " + rKey + " So discarding tempFile", e);
						try {
							if(tempODSFileOutputStream != null) {
								tempODSFileOutputStream.close();
								// Setting tempODSFileOutputStream to null, so that we don't try to close it again
								tempODSFileOutputStream = null;
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing temp ZS file for rKey: " + rKey, e1);
						}
						if(tempODSFile != null) {
							tempODSFile.delete();
						}
					} finally {
						try {
							if(documentInputStream != null) {
								documentInputStream.close();
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing ZS document file for rKey: " + rKey, e1);
						}
						try {
							if(tempODSFileOutputStream != null) {
								tempODSFileOutputStream.close();
							}
						} catch(IOException e1) {
							logger.log(Level.WARNING, "EXPORT :::: Exception when closing temp ZS file for rKey: " + rKey, e1);
						}
					}
				}
			}catch(Exception e) {
				logger.log(Level.WARNING, "EXPORT :::: Exception when checking for ZS document file for rKey: " + rKey + "  zfsngVersionNo: " + zfsngVersionNo, e);
			}
		}

		try {
			if(tempODSFile == null) {
				logger.log(Level.INFO, "EXPORT :::: No ODS file available for rKey: {0} zfsngVersionNo: {1} Hence creating it.", new Object[]{rKey, zfsngVersionNo});
				tempODSFile = EngineUtils1.createTempZSFile(container, zfsngVersionNo, false, null);
			}
		} catch (Exception e) {
			logger.log(Level.WARNING, "EXPORT :::: Exception when creating temp ODS File for the rKey: " + container.getResourceKey() + "  zfsngVersionNo: " + zfsngVersionNo, e);
			throw e;
		}

		return tempODSFile;
	}
	*/

	public static InputStream getFileInputStream(WorkbookContainer container, String versionNo)
	{
		InputStream is                 =     null;
		Store         store             =     null;
		long         resourceId         =    -1;
		try
		{
			String docOwner            =    container.getDocOwner();
			String docId            =    container.getDocId();
			if (versionNo == null || "".equals(versionNo) || "undefined".equals(versionNo))
			{
				versionNo = String.valueOf(DocumentUtils.getLastVersion(docId, docOwner));
			}
			resourceId                 =     ZSStore.getVersionId(docOwner, Long.parseLong(docId), versionNo);
			store                     =     container.getStore(resourceId, FileName.VERSION);
			if(container.isFileExist(store, resourceId, FileName.VERSION , FileExtn.ODS)) //This file exist method will check in DFS alone
			{
				is = container.getReadStream(store, resourceId, FileName.VERSION, FileExtn.ODS, null);
			}else if(container.isFileExist(store, resourceId, FileName.VERSION , FileExtn.SXC)){
				is = container.getReadStream(store, resourceId, FileName.VERSION, FileExtn.SXC, null);
			}
			else
			{
				resourceId = Long.parseLong(docId);
				store = container.getStore(resourceId, FileName.DOCUMENT);
				if (container.isFileExist(resourceId, FileName.DOCUMENT, FileExtn.ODS)) {
					is = container.getReadStream(store, resourceId, FileName.DOCUMENT, FileExtn.ODS, null);
				}else{
					resourceId = ZSStore.getFragmentId(docOwner, Long.parseLong(docId), ZSStore.FILENAME_ORDER);
					if (container.isFileExist(resourceId, FileName.ORDER, FileExtn.JSON)) {
						File tempfile = EngineUtils1.createTempODSFile(container, null, false, null, true);
						is = new FileInputStream(tempfile);
						tempfile.delete();
					}else if(container.isFileExist(Long.parseLong(docId), FileName.DOCUMENT, FileExtn.SXC)){
						store = container.getStore(Long.parseLong(docId), FileName.DOCUMENT);
						is = container.getReadStream(store, Long.parseLong(docId), FileName.DOCUMENT, FileExtn.SXC, null);
					}
				}
			}
			//TODO: Have to check, is there posisble for sxc case.
		}
		catch(Exception e)
		{
			logger.log(Level.WARNING, "ODS File is not available for the docId "+resourceId, e);
		}
		return is;
	}

	public static String getCellRangeAddressString(com.sun.star.sheet.XSheetCellRange xCellRange, boolean bWithSheet )
	{
		String aStr = "";
		if (bWithSheet)
		{
			com.sun.star.sheet.XSpreadsheet xSheet = xCellRange.getSpreadsheet();
			com.sun.star.container.XNamed xNamed = (com.sun.star.container.XNamed)
					UnoRuntime.queryInterface( com.sun.star.container.XNamed.class, xSheet );
			aStr += xNamed.getName() + ".";
		}
		com.sun.star.sheet.XCellRangeAddressable xAddr = (com.sun.star.sheet.XCellRangeAddressable)
				UnoRuntime.queryInterface( com.sun.star.sheet.XCellRangeAddressable.class, xCellRange );
		aStr += getCellRangeAddressString( xAddr.getRangeAddress() );
		return aStr;
	}

	public static void saveMacroCode(XSpreadsheetDocument document)
	{
		XPropertySet propertySet = (XPropertySet)UnoRuntime.queryInterface(XPropertySet.class, document);
		if(propertySet == null)
		{
			return;
		}
		Any any;
		try
		{
			any = (Any)propertySet.getPropertyValue("BasicLibraries");
		}
		catch(Exception ex)
		{
			return;
		}
		if(any == null)
		{
			return;
		}
		XLibraryContainer libraryContainer = (XLibraryContainer)UnoRuntime.queryInterface(XLibraryContainer.class, any);
		if(libraryContainer == null)
		{
			return;
		}
		XNameAccess nameAccess = (XNameAccess)UnoRuntime.queryInterface(XNameAccess.class, libraryContainer);
		if(nameAccess == null)
		{
			return;
		}
		Object obj;
		try
		{
			// this below line is OO 3.0 fix, harmless in OO 2.0
			libraryContainer.loadLibrary("Standard"); // No I18N
			obj = nameAccess.getByName("Standard");        //No I18N
		}
		catch(Exception ex)
		{
			return;
		}
		XNameContainer nameContainer = (XNameContainer)UnoRuntime.queryInterface(XNameContainer.class, obj);
		if(nameContainer == null)
		{
			return;
		}
		nameAccess = (XNameAccess)UnoRuntime.queryInterface(XNameAccess.class, obj);
		if(nameAccess == null)
		{
			return;
		}
		XNameReplace replace = (XNameReplace)UnoRuntime.queryInterface(XNameReplace.class, obj);
		String[] moduleNames = nameAccess.getElementNames();
		if(moduleNames != null)
		{
			for(int i=0; i<moduleNames.length; i++)
			{
				int j=0;
				for(j=0;j<moduleNames[i].length();j++)
				{
					if(!(moduleNames[i].charAt(j)>=0 && moduleNames[i].charAt(j)<=127))
					{
						break;
					}
				}
				if(j!=moduleNames[i].length())
				{
					try{
						nameContainer.removeByName(moduleNames[i]);
					}catch(Exception e){logger.log(Level.WARNING,null,e);}
				}
				else
				{
					Object moduleContent;
					try
					{
						moduleContent = nameAccess.getByName(moduleNames[i]);
						if(replace != null)
						{
							replace.replaceByName(moduleNames[i],moduleContent);
						}
						else
						{
							nameContainer.removeByName(moduleNames[i]);
							nameContainer.insertByName(moduleNames[i],moduleContent);
						}
					}
					catch(Exception e)
					{
						continue;
					}
				}
			}
		}
	}


	public static boolean isDocumentContainingDynamicFormulae(XSpreadsheetDocument xDocument) {
		boolean isDocumentContainingDynamicFormulae = false;
		try {
			String[] sheetnamesArr = xDocument.getSheets().getElementNames();
			for(int i = 0; i < sheetnamesArr.length; i++) {
				XSpreadsheet xSpreadsheet = (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class,xDocument.getSheets().getByName(sheetnamesArr[i]));
				isDocumentContainingDynamicFormulae = isSheetContainingDynamicFormulae(xSpreadsheet);
				if(isDocumentContainingDynamicFormulae) {
					break;
				}
			}
		} catch(Exception e) {
			logger.log(Level.WARNING,null,e);
		}
		return isDocumentContainingDynamicFormulae;
	}

	public static boolean isSheetContainingDynamicFormulae(XSpreadsheet xSheet) {
		boolean isSheetContainingDynamicFormulae = false;
		XSheetCellCursor xCursor = xSheet.createCursor();
		XUsedAreaCursor xUsedCursor = (XUsedAreaCursor)UnoRuntime.queryInterface(XUsedAreaCursor.class, xCursor);
		xUsedCursor.gotoStartOfUsedArea(false);
		xUsedCursor.gotoEndOfUsedArea(true);
		String sLen = getCellRangeAddressString( xCursor, false );
		XCellRange xran = xSheet.getCellRangeByName(sLen);
		XCellRangeFormula xRanFormula = (XCellRangeFormula) UnoRuntime.queryInterface(XCellRangeFormula.class,xran);
		String[][] formulas = xRanFormula.getFormulaArray();
		for(int i=0;i<formulas.length;i++) {
			for(int j=0;j<formulas[i].length;j++) {
				isSheetContainingDynamicFormulae = isDynamicFormulae(formulas[i][j]);
				if(isSheetContainingDynamicFormulae) {
					break;
				}
			}
			if(isSheetContainingDynamicFormulae){
				break;
			}
		}
		return isSheetContainingDynamicFormulae;
	}

	private static boolean isDynamicFormulae(String cellValue) {
		boolean isDynamicFormulae = false;
		if(cellValue.startsWith("=")) {
			String tmpCellValue = cellValue.toUpperCase();
			if((tmpCellValue.contains("TODAY")) || (tmpCellValue.contains("NOW")) || (tmpCellValue.contains("RAND")) || (tmpCellValue.contains("RANDBETWEEN")) || (tmpCellValue.contains("STOCK"))) {
				isDynamicFormulae = true;
			}
		}
		return isDynamicFormulae;
	}

	public static String getCellRangeAddressString(com.sun.star.table.CellRangeAddress aCellRange )
	{
		return     CellUtil.getCellReference( aCellRange.StartColumn, aCellRange.StartRow )
				+ ":"
				+ CellUtil.getCellReference( aCellRange.EndColumn, aCellRange.EndRow );
	}
	public static ArrayList<Integer> getSheetDim(XSpreadsheet xSheet) {
		ArrayList<Integer> arrList = new ArrayList<Integer>();
		int rowDim = 0;
		int colDim = 0;
		if(xSheet != null)
		{
			XSheetCellCursor xCursor = xSheet.createCursor();
			XUsedAreaCursor xUsedCursor = (XUsedAreaCursor) UnoRuntime.queryInterface(XUsedAreaCursor.class, xCursor);
			xUsedCursor.gotoStartOfUsedArea(false);
			xUsedCursor.gotoEndOfUsedArea(true);
			XCellRangeAddressable xCursor_xAddressable =
					(XCellRangeAddressable) UnoRuntime.queryInterface(XCellRangeAddressable.class, xUsedCursor);
			CellRangeAddress addr = xCursor_xAddressable.getRangeAddress();
			/*
                //usedArea including format
                colDim = addr.EndColumn + 1;
                rowDim= addr.EndRow + 1;
			 */
			// Gathering  Used area excluding format (Upto data) by formula array
			int dataRow = 0;
			int dataCol = 0;
			Boolean countRow = false;
			Boolean countCol = false;
			String sLen = "A1:" + CellUtil.getCellReference( addr.EndColumn, addr.EndRow);    //No internationalization
			XCellRange xran = xSheet.getCellRangeByName(sLen);
			//XCellRangeData xCData = (XCellRangeData)UnoRuntime.queryInterface(XCellRangeData.class, xran );
			//Object[][] values =  xCData.getDataArray();
			// Note: Using DataArray we loss formula which has result as empty
			XCellRangeFormula xRanFormula = (XCellRangeFormula) UnoRuntime.queryInterface(XCellRangeFormula.class,xran);
			String[][] values = xRanFormula.getFormulaArray();
			for(int row = 0; row < values.length; row++) {
				for(int col = 0; col < values[row].length; col++){
					if(!values[row][col].equals("")){
						countRow = true;
						countCol = true;
					}
					if(countCol && dataCol < col) {
						dataCol = col;
					}
					countCol = false;
				}
				if(countRow) {
					dataRow = row;
					countRow = false;
				}
			}
			//rowDim = dataRow + 1;
			rowDim = dataRow;
			//colDim = dataCol + 1;
			colDim = dataCol;
			logger.log(Level.INFO, " No. of data rows...{0}   No. of data cols...{1}", new Object[]{rowDim, colDim});
		}
		arrList.add(rowDim);
		arrList.add(colDim);
		return arrList;
	}

	public static String getActiveSheetInfo(XSpreadsheetDocument xDocument) throws Exception {
		//For To Handle Hidden SHeets
		String[] sheetnames = getVisibleSheetNames(xDocument);
		return sheetnames[0];
	}

	public static String[] getVisibleSheetNames(XSpreadsheetDocument xSpreadsheetDocument)  throws Exception {
		String[] sheetnamesArr = xSpreadsheetDocument.getSheets().getElementNames();
		XPropertySet xProp = null;
		List<String> sheetnamesList = new ArrayList<String>();
		for(int i=0;i<sheetnamesArr.length;i++) {
			XSpreadsheet xSpreadsheet = (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class,xSpreadsheetDocument.getSheets().getByName(sheetnamesArr[i]));
			xProp = (XPropertySet) UnoRuntime.queryInterface(XPropertySet.class, xSpreadsheet);
			boolean isVisible = (Boolean) xProp.getPropertyValue("IsVisible");
			if(isVisible) {
				sheetnamesList.add(sheetnamesArr[i]);
			}
		}
		return sheetnamesList.toArray(new String[]{});
	}

	public static XInputStream openURL(File file) throws Exception {
		InputStream is = null;
		BufferedInputStream bis = null;
		ByteArrayOutputStream bos = null;
		try {
			is = new FileInputStream(file);
			bis = new BufferedInputStream(is);
			bos = new ByteArrayOutputStream();
			int BUF_LEN = 1024;
			byte[] bufr = new byte[BUF_LEN];
			int c=0;
			while((c=bis.read(bufr,0,BUF_LEN)) != -1) {
				bos.write(bufr,0,c);
			}
			return new OOInputStream(bos.toByteArray());
		} finally {
			if (is != null)
			{
				is.close();
			}
			if (bis != null)
			{
				bis.close();
			}
			if (bos != null)
			{
				bos.close();
			}
		}
	}


	public static byte[] encryptDocument(String format, byte[] bytes, String password) throws Exception{

		password =  URLDecoder.decode(password);
		if(EngineConstants.XLS_FILE_FORMAT.contains(format)){
			byte[] eBytes = encryptXLS(format, bytes, password);
			return eBytes;
		}else if(EngineConstants.XLSX_FILE_FORMAT.contains(format)){
			byte[] eBytes = encryptXLSX(format, bytes, password);
			return eBytes;
		}else if(EngineConstants.PDF_FILE_FORMAT.contains(format)) {
			byte[] eBytes = encryptPDF(format, bytes, password);
			return eBytes;
		}
		return null;
	}

	public static byte[] encryptXLS(String format, byte[] bytes, String password) throws Exception {

		logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptXLS Started:");
		File tempFile =  new File(EngineConstants.TEMPEXPORTDIR + File.separator + System.currentTimeMillis() + "." + format);
                String canonicalPath = tempFile.getCanonicalPath();
                if(!canonicalPath.startsWith(EngineConstants.TEMPEXPORTDIR)){
                    return null;
                }

		POIFSFileSystem poiFileSystem = null;
		OutputStream fileOut = null;
		HSSFWorkbook workbook = null;

		try {
			ZipSecureFile.setMinInflateRatio(0d);
			poiFileSystem = new POIFSFileSystem(new ByteArrayInputStream(bytes));
			Biff8EncryptionKey.setCurrentUserPassword(password);
			workbook = new HSSFWorkbook(poiFileSystem, true);
			fileOut = new FileOutputStream(tempFile);
			workbook.writeProtectWorkbook(Biff8EncryptionKey.getCurrentUserPassword(), "");
			workbook.write(fileOut);
			byte[] b =  FileUtils.readFileToByteArray(tempFile);
			logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptXLS encrypted:");
			return b;
		} catch (Exception e) {
			logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptXLS error:",e);
			return null;
		} finally {
			try {
				fileOut.close();
				poiFileSystem.close();
				deleteTempFile(tempFile);
				workbook.close();
			} catch (Exception e) {
				logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptXLS Delete error:",e);
			}
		}
	}

	public static byte[] encryptXLSX(String format, byte[] bytes, String password) throws Exception{

		logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptXLSX Started:");
		ByteArrayInputStream in =null;
		POIFSFileSystem fs=null;
		Workbook workbook = null;
		OutputStream os=null;
		File tempFile =  new File(EngineConstants.TEMPEXPORTDIR + File.separator + System.currentTimeMillis() + "." + format);
		String canonicalPath = tempFile.getCanonicalPath();
		if(!canonicalPath.startsWith(EngineConstants.TEMPEXPORTDIR)){
			return null;
		}

		FileOutputStream fos=null;
		try
		{
			ZipSecureFile.setMinInflateRatio(0d);
			in = new ByteArrayInputStream(bytes);
			fs = new POIFSFileSystem();
			EncryptionInfo info = new EncryptionInfo(EncryptionMode.agile);
			Encryptor enc = info.getEncryptor();
			enc.confirmPassword(password);

			// Read in an existing OOXML file
			workbook = new XSSFWorkbook(in);
			os = enc.getDataStream(fs);
			workbook.write(os);
			os.close(); // this is necessary before writing out the FileSystem else it will export an incomplete xlsx.

			fos = new FileOutputStream(tempFile);
			fs.writeFilesystem(fos);
			byte[] b =  FileUtils.readFileToByteArray(tempFile);
			logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptXLS encrypted:");
			return b;
		}
		catch(Exception e)
		{
			logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptXLS error:",e);
			return null;
		}finally{
			try{
				in.close();
				fs.close();
				deleteTempFile(tempFile);
				fos.close();
				workbook.close();
			}catch(Exception e){
				logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptXLS Delete error:",e);
			}
		}

	}


        public static byte[] encryptPDF(String format, byte[] bytes, String password) {
            PDDocument doc = null;
            File tempFile = new File(EngineConstants.TEMPEXPORTDIR + File.separator + System.currentTimeMillis() + "." + format);

            try {
                String canonicalPath = tempFile.getCanonicalPath();
                if(canonicalPath.startsWith(EngineConstants.TEMPEXPORTDIR)){

                    //PDF encryption requires two passwords: the “user password” to open and view the file with restricted permissions, the “owner password” to access the file with all permission.
                    doc = PDDocument.load(bytes);

                    // Define the length of the encryption key.
                    // Possible values are 40 or 128 (256 will be available in PDFBox 2.0).
                    int keyLength = 128;

                    AccessPermission ap = new AccessPermission();

                    // Disable printing, everything else is allowed
                    ap.setCanPrint(false);

                    // Owner password (to open the file with all permissions) is "12345"
                    // User password (to open the file but with restricted permissions, is empty here)
                    StandardProtectionPolicy spp = new StandardProtectionPolicy(password, password, ap);
                    spp.setEncryptionKeyLength(keyLength);
                    spp.setPermissions(ap);
                    doc.protect(spp);

                    doc.save(tempFile);
                    byte[] b = FileUtils.readFileToByteArray(tempFile);
                    logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptPDF encrypted:");
                    return b;
                }

                return null;
            } catch (Exception e) {
                logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptPDF error:", e);
                return null;
            } finally {
                try {
                    doc.close();
                    deleteTempFile(tempFile);
                } catch (Exception e) {
                    logger.log(Level.INFO, "[CONVERSION:EXPORT] encryptPDF Delete error:", e);
                }
            }
        }

	//	public static boolean isEncrypted(byte[] contentBytes) throws Exception{
	//		InputStream myInputStream = null;
	//		org.apache.poi.ss.usermodel.Workbook wb = null;
	//		try
	//		{
	//			myInputStream = new ByteArrayInputStream(contentBytes);
	//			wb = WorkbookFactory.create(myInputStream);
	//			return false;
	//		}
	//		catch(EncryptedDocumentException e){
	//			return true;
	//		}
	//		catch (IOException e){
	//			return false;
	//		}
	//		catch(ODFNotOfficeXmlFileException e){
	//			return false;
	//		}
	//		catch(Exception e){
	//			return false;
	//		}finally{
	//			if(wb!=null){
	//				wb.close();
	//			}
	//			if(myInputStream!=null){
	//				myInputStream.close();
	//			}
	//		}
	//	}

	public static boolean isEncrypted(Object obj) throws Exception{
		InputStream myInputStream = null;
		org.apache.poi.ss.usermodel.Workbook wb = null;
		try
		{
			if(obj instanceof byte[]) {
				myInputStream = new ByteArrayInputStream((byte[])obj);
				wb = WorkbookFactory.create(myInputStream);
				return false;
			}
			else if(obj instanceof File) {
				wb = WorkbookFactory.create((File)obj);
				return false;
			}else {
				return false;
			}
		}
		catch(EncryptedDocumentException e){
			logger.log(Level.INFO, "[CONVERSION:IMPORT] EncryptedDocumentException:",e);
			return true;
		}
		catch(ODFNotOfficeXmlFileException e){
			logger.log(Level.INFO, "[CONVERSION:IMPORT] ODFNotOfficeXmlFileException:",e);
			return false;
		}
		catch (IOException e){
			logger.log(Level.INFO, "[CONVERSION:IMPORT] IOException:",e);
			return false;
		}
		catch(Exception e){
			logger.log(Level.INFO, "[CONVERSION:IMPORT] Exception:",e);
			return false;
		}finally{
			try {
				if(wb!=null){
					wb.close();
				}
				if(myInputStream!=null){
					myInputStream.close();
				}
			}
			catch (Exception e) {
				logger.log(Level.INFO, "[CONVERSION:IMPORT] isEncrypted Finally Exception:",e);
			}
		}
	}

	public static byte[] decryptedDocument(byte[] bytes, String format, String password) throws Exception{

		password =  URLDecoder.decode(password);
		if(EngineConstants.XLS_FILE_FORMAT.contains(format)){
			byte[] eBytes = decryptXLS(bytes, format, password);
			return eBytes;
		}else if(EngineConstants.XLSX_FILE_FORMAT.contains(format)){
			byte[] eBytes = decryptXLSX( bytes, format, password);
			return eBytes;
		}else if (EngineConstants.XLSM_FILE_FORMAT.contains(format)) {
			byte[] eBytes = decryptXLSX(bytes, format, password);
			return eBytes;
		}else if (EngineConstants.PDF_FILE_FORMAT.contains(format)) {
			byte[] eBytes = decryptPDF(bytes, format, password);
			return eBytes;
		}
		return null;
	}

	public static byte[] decryptXLS(byte[] contentBytes, String format, String password) throws Exception{

		ByteArrayInputStream in =null;
		POIFSFileSystem fs=null;
		File tempFile =  new File(EngineConstants.TEMPEXPORTDIR + File.separator + System.currentTimeMillis() + format);
                String canonicalPath = tempFile.getCanonicalPath();
                if(!canonicalPath.startsWith(EngineConstants.TEMPEXPORTDIR)){
                    return null;
                }
		try
		{
			Biff8EncryptionKey.setCurrentUserPassword(password);
			in = new ByteArrayInputStream(contentBytes);
			fs = new POIFSFileSystem(in);
			HSSFWorkbook workbook = new HSSFWorkbook(fs);
			Biff8EncryptionKey.setCurrentUserPassword(null);
			workbook.write(tempFile);
			byte[] b =  FileUtils.readFileToByteArray(tempFile);
			return b;
		}
		catch (EncryptedDocumentException ede) {
			logger.log(Level.INFO, "[CONVERSION:IMPORT] Wrong Password:");
			if(ede.getMessage().contains("Supplied password is invalid")) {
				throw new Exception("Invalid Password");
			}else {
				return null;
			}
		}
		catch(Exception e)
		{
			logger.log(Level.INFO, "[CONVERSION:IMPORT] Exception:"+e);
			return null;
		}finally{
			try{
				in.close();
				fs.close();
				deleteTempFile(tempFile);
			}catch(Exception e){
			}
		}

	}


	public static byte[] decryptXLSX(byte[] contentBytes, String format, String password) throws Exception{
		ByteArrayInputStream in =null;
		POIFSFileSystem fs=null;
		File tempFile =  new File(EngineConstants.TEMPEXPORTDIR + File.separator + System.currentTimeMillis() + format);

                String canonicalPath = tempFile.getCanonicalPath();
                if(!canonicalPath.startsWith(EngineConstants.TEMPEXPORTDIR)){
                    return null;
                }

		FileOutputStream fos= new FileOutputStream(tempFile);
		try
		{
			in = new ByteArrayInputStream(contentBytes);
			fs = new POIFSFileSystem(in);
			EncryptionInfo info = new EncryptionInfo(fs);
			Decryptor decryptor = Decryptor.getInstance(info);

			if (!decryptor.verifyPassword(password)) {
				throw new Exception("Invalid Password");
			}
			XSSFWorkbook workbook = new XSSFWorkbook(decryptor.getDataStream(fs));
			workbook.write(fos);
			byte[] b =  FileUtils.readFileToByteArray(tempFile);
			return b;
		}catch(Exception e)
		{
			if(e.getMessage().contains("Invalid Password")) {
				logger.log(Level.INFO, "[CONVERSION:IMPORT] Wrong Password:");
				throw new Exception("Invalid Password");
			}else {
				logger.log(Level.INFO, "[CONVERSION:IMPORT] Exception:"+e);
				return null;
			}
		}finally{
			try{
				in.close();
				fs.close();
				deleteTempFile(tempFile);
				fos.close();
			}catch(Exception e){
			}
		}
	}

	public static byte[] decryptPDF(byte[] contentBytes, String format, String password) throws Exception{
		File tempFile =  new File(EngineConstants.TEMPEXPORTDIR + File.separator + System.currentTimeMillis() + format);
                String canonicalPath = tempFile.getCanonicalPath();
                if(!canonicalPath.startsWith(EngineConstants.TEMPEXPORTDIR)){
                    return null;
                }

		PDDocument doc = null;
		try{
			doc = PDDocument.load(contentBytes,password);
			doc.setAllSecurityToBeRemoved(true);
			doc.save(tempFile);
			byte[] b =  FileUtils.readFileToByteArray(tempFile);
			return b;
		}catch(Exception e){
			return null;
		}finally{
			try{
				deleteTempFile(tempFile);
				doc.close();
			}catch(Exception e){
			}
		}
	}

	public static File decryptedDocument(File file, String format, String password) throws Exception{

		password =  URLDecoder.decode(password);
		if(EngineConstants.XLS_FILE_FORMAT.contains(format)){
			File dFile = decryptXLS( file, format, password);
			return dFile;
		}else if(EngineConstants.XLSX_FILE_FORMAT.contains(format)){
			File dFile = decryptXLSX( file, format, password);
			return dFile;
		}else if (EngineConstants.XLSM_FILE_FORMAT.contains(format)) {
			File dFile = decryptXLSX( file, format, password);
			return dFile;
		}
		//		else if (EngineConstants.PDF_FILE_FORMAT.contains(format)) {
		//			File dFile = decryptPDF( file, format, password);
		//			return dFile;
		//		}
		return null;
	}

	public static File decryptXLS(File file, String format, String password) throws Exception{

		HSSFWorkbook workbook = null;
		POIFSFileSystem fs=null;
		try
		{
			Biff8EncryptionKey.setCurrentUserPassword(password);
			fs = new POIFSFileSystem(file);
			workbook = new HSSFWorkbook(fs);
			Biff8EncryptionKey.setCurrentUserPassword(null);
			workbook.write(file);
			return file;
		}
		catch (EncryptedDocumentException ede) {
			logger.log(Level.INFO, "[CONVERSION:IMPORT]:"+ede);
			if(ede.getMessage().contains("Supplied password is invalid")) {
				throw new Exception("Invalid Password");
			}else {
				return null;
			}
		}
		catch(Exception e)
		{
			logger.log(Level.INFO, "[CONVERSION:IMPORT] Exception:"+e);
			return null;
		}finally{
			try{
				fs.close();
				workbook.close();
			}catch(Exception e){
			}
		}

	}

	public static File decryptXLSX(File file, String format, String password) throws Exception{
		XSSFWorkbook workbook = null;
		POIFSFileSystem fs=null;
		FileOutputStream fos = null;
		try
		{
			fs = new POIFSFileSystem(file);
			EncryptionInfo info = new EncryptionInfo(fs);
			Decryptor decryptor = Decryptor.getInstance(info);
			if (!decryptor.verifyPassword(password)) {
				throw new Exception("Invalid Password");
			}
			workbook = new XSSFWorkbook(decryptor.getDataStream(fs));
			fos = new FileOutputStream(file);
			workbook.write(fos);
			return file;
		}
		catch(Exception e)
		{
			if(e.getMessage().contains("Invalid Password")) {
				logger.log(Level.INFO, "[CONVERSION:IMPORT] Wrong Password:");
				throw new Exception("Invalid Password");
			}else {
				logger.log(Level.INFO, "[CONVERSION:IMPORT] Exception:"+e);
				return null;
			}
		}finally{
			try{
				fos.close();
				fs.close();
				workbook.close();
			}catch(Exception e){
			}
		}
	}

	public static JSONArrayWrapper exceededLimitXLSX(byte[] contentBytes){
		ByteArrayInputStream in = null;
		XSSFWorkbook xWorkbook = null;
		try
		{
			in = new ByteArrayInputStream(contentBytes);
			xWorkbook = new XSSFWorkbook(in);
			JSONArrayWrapper exceededLimits = new JSONArrayWrapper();
			int totalNoOfCells = 0;
			for(int sheetNo = 0 ; sheetNo < xWorkbook.getNumberOfSheets() ; sheetNo++ ) {
				int lastColNum = 0;
				XSSFSheet xSheet = xWorkbook.getSheetAt(sheetNo);
				int lastRowNum = xSheet.getLastRowNum();
				for(int rowNo = 0 ; rowNo < lastRowNum; rowNo++){
					XSSFRow xRow = xSheet.getRow(rowNo);
					if(xRow!=null) {
						int col = 	xRow.getLastCellNum();
						if(col>lastColNum) {
							lastColNum = col;
						}
					}
				}
				totalNoOfCells += lastRowNum * lastColNum;
				if(lastRowNum > Utility.MAXNUMOFROWS || lastColNum>Utility.MAXNUMOFCOLS){
					JSONObjectWrapper jLimit = new JSONObjectWrapper();
					jLimit.put(Constants.SHEETNAME, xSheet.getSheetName());
					jLimit.put(Constants.NO_OF_ROWS, lastRowNum);
					jLimit.put(Constants.NO_OF_COLUMNS, lastColNum);
					exceededLimits.put(jLimit);
				} else if (totalNoOfCells > Utility.CELLCOUNTLIMIT) {
					JSONObjectWrapper jLimit = new JSONObjectWrapper();
					jLimit.put(Constants.SHEETNAME, xSheet.getSheetName());
					jLimit.put(Constants.NO_OF_CELLS, totalNoOfCells);
					exceededLimits.put(jLimit);
				}
			}
			return exceededLimits;
		}
		catch(Exception e)
		{
			logger.log(Level.INFO, "[CONVERSION:IMPORT] exceededLimitXLSX Exception:"+e);
		}
		finally {
			try{
				xWorkbook.close();
				in.close();
			}catch(Exception e){
			}
		}
		return null;
	}

	public static void writeExceededLimitsInLogs(byte[] contentBytes, String format) {
		ExecutorService executorService = Executors.newSingleThreadExecutor();
		try {
			executorService.submit(() -> {
				long startTime = System.currentTimeMillis();
				try {
					if (EngineConstants.XLSX_FILE_FORMAT.contains(format)) {
						JSONArrayWrapper exceededLimit = exceededLimitXLSX(contentBytes);
						logger.log(Level.INFO, "[CONVERSION:IMPORT] ExceededLimits:" + exceededLimit);
					}
				/*Excel limit of XLS files are 65.536 Rows and 256 Columns.
				else if(EngineConstants.XLS_FILE_FORMAT.contains(format)) {
					exceededLimitXLS(contentBytes);
				}*/
				} catch (Exception e) {
					logger.log(Level.WARNING, "[CONVERSION:IMPORT] ExceededLimits Error:", e);
				} finally {
					logger.log(Level.INFO, "[CONVERSION:IMPORT] Time taken to ExceededLimits:" + (System.currentTimeMillis() - startTime));
				}
			});
		} finally {
			executorService.shutdown();
		}
	}
	
	public static JSONArrayWrapper exceededLimitXLS(byte[] contentBytes) {

		ByteArrayInputStream in = null;
		POIFSFileSystem fs = null;
		try
		{
			in = new ByteArrayInputStream(contentBytes);
			HSSFWorkbook xWorkbook = new HSSFWorkbook(in);
			JSONArrayWrapper lastAddressArray = new JSONArrayWrapper();
			for(int sheetNo = 0 ; sheetNo < xWorkbook.getNumberOfSheets() ; sheetNo++ ) {
				int lastCol = 0;
				HSSFSheet xSheet = xWorkbook.getSheetAt(sheetNo);
				for(int rowNo = 0 ; rowNo < xSheet.getLastRowNum(); rowNo++){
					HSSFRow xrow = xSheet.getRow(rowNo);
					if(xrow!=null) {
						int col = 	xrow.getLastCellNum();
						if(col>lastCol) {
							lastCol = col;
						}
					}
				}
				if(xSheet.getLastRowNum()>Utility.MAXNUMOFROWS || lastCol>Utility.MAXNUMOFCOLS) {
					JSONObjectWrapper lastAddress = new JSONObjectWrapper();
					lastAddress.put("SheetName", xSheet.getSheetName());
					lastAddress.put("Row", xSheet.getLastRowNum());
					lastAddress.put("Column", lastCol);
					lastAddressArray.put(lastAddress);
				}
			}
			return lastAddressArray;
		}
		catch(Exception e)
		{
		}
		return null;

	}

	public static byte[] writeFreezePane(byte[] contentBytes, String format, JSONArrayWrapper freezePanes) throws Exception {
		if(EngineConstants.XLS_FILE_FORMAT.contains(format)){
			byte[] eBytes = writeFreezePaneXLS(contentBytes, format, freezePanes);
			return eBytes;
		}else if(EngineConstants.XLSX_FILE_FORMAT.contains(format)){
			byte[] eBytes = writeFreezePaneXLSX( contentBytes, format, freezePanes);
			return eBytes;
		}
		else if (EngineConstants.XLSM_FILE_FORMAT.contains(format)) {
			byte[] eBytes = writeFreezePaneXLSX( contentBytes, format, freezePanes);
			return eBytes;
		}
		return contentBytes;
	}

	public static byte[] writeFreezePaneXLS(byte[] contentBytes, String format, JSONArrayWrapper freezePanes) throws Exception{
		File tempFile =  new File(EngineConstants.TEMPEXPORTDIR + File.separator + System.currentTimeMillis() + "." + format);
		String canonicalPath = tempFile.getCanonicalPath();
        if(!canonicalPath.startsWith(EngineConstants.TEMPEXPORTDIR)){
            return null;
        }
		POIFSFileSystem poiFileSystem = null;
		OutputStream fileOut = null;
		HSSFWorkbook workbook = null;

		try {
			poiFileSystem = new POIFSFileSystem(new ByteArrayInputStream(contentBytes));
			workbook = new HSSFWorkbook(poiFileSystem, true);
			for(int i=0;i<freezePanes.length();i++) {
				JSONObjectWrapper freezePane = freezePanes.getJSONObject(i);
				String sheetName = freezePane.getString(JSONConstants.SHEET_NAME);

				int startRow = 0; int endRow = 0; int rowSplit = 0;
				int startCol = 0; int endCol = 0; int colSplit = 0;
				try{
					if(freezePane.has(JSONConstants.FREEZE_ROW_RANGE)) {
						//rowSplit = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_ROW_RANGE).getString(JSONConstants.END_ROW)) + 1;
						startRow = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_ROW_RANGE).getString(JSONConstants.START_ROW));
						endRow = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_ROW_RANGE).getString(JSONConstants.END_ROW));
						rowSplit = endRow - startRow + 1;
					}
					if(freezePane.has(JSONConstants.FREEZE_COL_RANGE)) {
						//colSplit = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_COL_RANGE).getString(JSONConstants.END_COLUMN)) + 1;
						startCol = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_COL_RANGE).getString(JSONConstants.START_COLUMN));
						endCol = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_COL_RANGE).getString(JSONConstants.END_COLUMN));
						colSplit = endCol - startCol + 1;
					}
				}catch(Exception e) {}
				HSSFSheet sheet = workbook.getSheet(sheetName);
				//Move the Pane to SR and SC to open the sheet from the same view port when Exported.
				sheet.showInPane(startRow, startCol);
				sheet.createFreezePane(colSplit, rowSplit, endCol+1, endRow+1);
			}
			fileOut = new FileOutputStream(tempFile);
			workbook.write(fileOut);
			byte[] b =  FileUtils.readFileToByteArray(tempFile);
			return b;
		} catch (Exception e) {
			logger.log(Level.INFO, "[CONVERSION:EXPORT] writeFreezePaneXLS Error:",e);
		} finally {
			try {
				if(fileOut != null) {
					fileOut.close();
				}
				if(poiFileSystem != null) {
					poiFileSystem.close();
				}
				deleteTempFile(tempFile);
				if(workbook != null) {
					workbook.close();
				}
			} catch (Exception e) {
				logger.log(Level.INFO, "[CONVERSION:EXPORT] writeFreezePaneXLS Error:",e);
			}
		}
		return contentBytes;
	}

	public static byte[] writeFreezePaneXLSX(byte[] contentBytes, String format, JSONArrayWrapper freezePanes ) throws Exception{

		File tempFile =  new File(EngineConstants.TEMPEXPORTDIR + File.separator + System.currentTimeMillis() + "." + format);
		String canonicalPath = tempFile.getCanonicalPath();
        if(!canonicalPath.startsWith(EngineConstants.TEMPEXPORTDIR)){
            return null;
        }
		OutputStream fileOut = null;
		XSSFWorkbook workbook = null;

		try {
			ByteArrayInputStream in = new ByteArrayInputStream(contentBytes);
			workbook = new XSSFWorkbook(in);
			for(int i=0;i<freezePanes.length();i++) {
				JSONObjectWrapper freezePane = freezePanes.getJSONObject(i);
				String sheetName = freezePane.getString(JSONConstants.SHEET_NAME);
				int startRow = 0; int endRow = 0; int rowSplit = 0;
				int startCol = 0; int endCol = 0; int colSplit = 0;
				try{
					if(freezePane.has(JSONConstants.FREEZE_ROW_RANGE)) {
						//rowSplit = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_ROW_RANGE).getString(JSONConstants.END_ROW)) + 1;
						startRow = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_ROW_RANGE).getString(JSONConstants.START_ROW));
						endRow = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_ROW_RANGE).getString(JSONConstants.END_ROW));
						rowSplit = endRow - startRow + 1;
					}
					if(freezePane.has(JSONConstants.FREEZE_COL_RANGE)) {
						//colSplit = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_COL_RANGE).getString(JSONConstants.END_COLUMN)) + 1;
						startCol = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_COL_RANGE).getString(JSONConstants.START_COLUMN));
						endCol = Integer.parseInt(freezePane.getJSONObject(JSONConstants.FREEZE_COL_RANGE).getString(JSONConstants.END_COLUMN));
						colSplit = endCol - startCol + 1;
					}
				}catch(Exception e) {
				}
				try {
					XSSFSheet sheet = workbook.getSheet(sheetName);
					CellAddress cellAddress = new CellAddress(startRow, startCol);
					//Go to TopLeftCell of viewPort
					sheet.getCTWorksheet().getSheetViews().getSheetViewArray(0).setTopLeftCell(cellAddress.formatAsString());
					//Freeze the no of rows and columns visible on viewport
					sheet.createFreezePane(colSplit,rowSplit);
					//Go to TopLeftCell of RightBottom Pane.
					//sheet.getCTWorksheet().getSheetViews().getSheetViewArray(0).getPane().setTopLeftCell(new CellAddress(endRow+1, endCol+1).toString());
				}catch(Exception e) {
					logger.log(Level.INFO, "[CONVERSION:EXPORT] freezePaneXLSX Error There are no freezePane:");
				}
			}
			fileOut = new FileOutputStream(tempFile);
			workbook.write(fileOut);
			byte[] b =  FileUtils.readFileToByteArray(tempFile);
			return b;
		} catch (Exception e) {
			logger.log(Level.INFO, "[CONVERSION:EXPORT] freezePaneXLSX Error:",e);
		} finally {
			try {
				if(fileOut!=null) {
					fileOut.close();
				}
				deleteTempFile(tempFile);
				if(workbook != null) {
					workbook.close();
				}
			} catch (Exception e) {
				logger.log(Level.INFO, "[CONVERSION:EXPORT] freezePaneXLSX Error:",e);
			}
		}
		return contentBytes;
	}

	public static void deleteTempFile(File tempFile) throws Exception{
		File file = new File(EngineConstants.TEMPEXPORTDIR, tempFile.getName());
		if(file.getCanonicalPath().startsWith(EngineConstants.TEMPEXPORTDIR)) {
			tempFile.delete();
		}
	}

	/**
	 * this function will return the document as it is, in the File Store.
	 * @param container
	 * @param zfsngVersionNum
	 * @param sheetVersionNo
	 * @return
	 * @throws Exception
	 */
	public static byte[] getZohoSheet(WorkbookContainer container, String zfsngVersionNum, String sheetVersionNo) throws Exception {
		logger.log(Level.OFF, "export format : {0} ", new Object[]{".nzsheet"});
		if(EngineConstants.NEW_ZSHEET_FILE_FORMAT_EXPORT_ENABLED) {
			String zfsngVersionNo = zfsngVersionNum;
			String fileFormat = container.getFileFormat();
			if(zfsngVersionNo == null) {
				if(!container.isRemoteMode() && ((EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT.equals(fileFormat)) || EngineConstants.ENGINE_ZS_FRAGMENTED_FILE_FORMAT.equals(fileFormat))) {
					try {
						zfsngVersionNo = ZohoFS.getTopVersion(container.getDocsSpaceId(), container.getResourceKey(), true, true);
					} catch(Exception e) {
						logger.log(Level.INFO, "Engine: Exception while getting Top Version when Parsing workbook :::: RKey: "+ container.getResourceKey(), e);
					}
				}
			}
			long documentId = Long.parseLong(container.getDocId());
			if (zfsngVersionNo != null) {
				long versionResourceId = ZSStore.getVersionId(container.getDocOwner(), documentId, sheetVersionNo);
				Store versionStore = container.getStore(versionResourceId, FileName.VERSION);
				fileFormat = container.getFileFormatForVersion(versionResourceId);

				if(EngineConstants.ENGINE_ZSHEET_FILE_FORMAT.equals(fileFormat)) {
					try(InputStream stream = container.getReadStream(versionStore, versionResourceId, null, ZSStore.FileExtn.ZSHEET, null)) {
						return IOUtils.toByteArray(stream);
					}
				} else if(EngineConstants.ZXLSX_FILE_FORMAT.equals(fileFormat)) {
					try(InputStream stream = container.getReadStream(versionStore, versionResourceId, null, FileExtn.ZXLSX, null)) {
						return IOUtils.toByteArray(stream);
					}
				} else {
					try(InputStream stream = container.getReadStream(versionStore, versionResourceId, null, ZSStore.FileExtn.ODS, null)) {
						return IOUtils.toByteArray(stream);
					}
				}
			} else {
				if(container.isFileExist(documentId, FileName.DOCUMENT, FileExtn.ZSHEET)) {
					Store store = container.getStore(documentId, FileName.DOCUMENT);
					try(InputStream stream = container.getReadStream(store, documentId, null, ZSStore.FileExtn.ZSHEET, null)) {
						return IOUtils.toByteArray(stream);
					}
				} else if(container.isFileExist(documentId, FileName.DOCUMENT, FileExtn.ZXLSX)) {
					Store store = container.getStore(documentId, FileName.DOCUMENT);
					try(InputStream stream = container.getReadStream(store, documentId, null, FileExtn.ZXLSX, null)) {
						return IOUtils.toByteArray(stream);
					}
				} else if(container.isFileExist(documentId, FileName.DOCUMENT, FileExtn.ODS)) {
					Store store = container.getStore(documentId, FileName.DOCUMENT);
					try(InputStream stream = container.getReadStream(store, documentId, null, ZSStore.FileExtn.ODS, null)) {
						return IOUtils.toByteArray(stream);
					}
				} else if(EngineConstants.ENGINE_ZS_FRAGMENTED_FILE_FORMAT.equals(fileFormat)){
					try(ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
						try (ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
							EngineUtils1.writeFragmentsToZSHEET(container, zipOutputStream, false);
							return byteArrayOutputStream.toByteArray();
						}
					}
				} else {
					try(ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
						try (ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
						EngineUtils1.writeFragmentsToODSFile(container, zipOutputStream, false, null);
							return byteArrayOutputStream.toByteArray();
						}
					}
				}
			}
		} else {
			logger.log(Level.OFF, "unsupported export format : .nzsheet ");
			throw new ServletException("Problem while exporting the document");
		}
	}

//	public static boolean isFileSizeSupported(byte[] contentBytes) {
//		try {
//			long Allowed_File_Size = allowedFileSize();
//			if (contentBytes!=null && contentBytes.length > Allowed_File_Size) {
//				logger.log(Level.INFO, "[CONVERSION:IMPORT] File size Not Supported: {0} : {1}", new Object[] {contentBytes.length, Allowed_File_Size});
//				return false;
//			}else {
//				logger.log(Level.INFO,"[CONVERSION:IMPORT] File size Supported");
//				return true;
//			}
//		}catch (Exception e) {
//			logger.log(Level.INFO,"[CONVERSION:IMPORT] isFileSizeSupported File size not Supported Error:",e);
//			return false;
//		}
//	}

//	public static long allowedFileSize() {
//		try {
//			//boolean newFileSize = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("CONVERSION_IMPORT_FILE_SIZE_NEW"));	//NO I18N
//			long allowed_File_Size = (Integer.parseInt(Constants.ALLOWED_FILE_SIZE) * ((long) Math.pow(2, 20)));
////			if(newFileSize) {
////				allowed_File_Size = (Integer.parseInt(Constants.ALLOWED_FILE_SIZE_NEW) * ((long) Math.pow(2, 20)));
////			}
//			// logger commented ~<EMAIL>
////			logger.log(Level.INFO, "[ImportExportUtil] Allowed File Size : {0}", new Object[]{allowed_File_Size});
//			return allowed_File_Size;
//		}catch (Exception e) {
//			logger.log(Level.INFO,"[CONVERSION:IMPORT] allowedFileSize File size not Supported Error:",e);
//			return (Integer.parseInt(Constants.ALLOWED_FILE_SIZE) * ((long) Math.pow(2, 20)));
//		}
//	}

	public static boolean isFileSizeSupportedNew(byte[] contentBytes) {
		try {
			long Allowed_File_Size = (Integer.parseInt(Constants.ALLOWED_FILE_SIZE_NEW) * ((long) Math.pow(2, 20)));
			if (contentBytes!=null && contentBytes.length > Allowed_File_Size) {
				logger.log(Level.INFO, "[CONVERSION:IMPORT] File size Not Supported: {0} : {1}", new Object[] {contentBytes.length, Allowed_File_Size});
				return false;
			}else {
				//logger.log(Level.INFO,"[CONVERSION:IMPORT] File size Supported");
				if(contentBytes.length > 26214400) {
					ZSStats.increment(ZSStats.IMPORT_FORMAT_SIZE_25);
					logger.log(Level.INFO, "[CONVERSION:IMPORT] File size Supported : New Size more then 25 MB: {0}", new Object[] {contentBytes.length});
				}
				else if(contentBytes.length > 10485760) {
					ZSStats.increment(ZSStats.IMPORT_FORMAT_SIZE_10);
					logger.log(Level.INFO, "[CONVERSION:IMPORT] File size Supported : New Size more then 10 MB: {0}", new Object[] {contentBytes.length});
				}
				return true;
			}
		}catch (Exception e) {
			logger.log(Level.INFO,"[CONVERSION:IMPORT] isFileSizeSupportedNew File size not Supported Error:",e);
			return false;
		}
	}

	public static long allowedFileSizeNew() {
		try {
			long allowed_File_Size = (Integer.parseInt(Constants.ALLOWED_FILE_SIZE_NEW) * ((long) Math.pow(2, 20)));
			logger.log(Level.INFO, "[ImportExportUtil] Allowed File Size New: {0}", new Object[]{allowed_File_Size});
			return allowed_File_Size;
		}catch (Exception e) {
			logger.log(Level.INFO,"[CONVERSION:IMPORT] allowedFileSizeNew File size not Supported Error:",e);
			return (Integer.parseInt(Constants.ALLOWED_FILE_SIZE_NEW) * ((long) Math.pow(2, 20)));
		}
	}

	public static char getAutoDelimiter(byte[] contentBytes) throws Exception {
		InputStream is = null;
		BufferedReader br = null;
		char defaultDelimiter = ',';
		String checkFordelimiter = ",;|";
		int occurenceOfDelimiter[] = new int[256];
		int maxOccurence = 0;
		try {
			is = new ByteArrayInputStream(contentBytes);
			br=new BufferedReader(new InputStreamReader(is));
			int noOfLinesToCheck = 10; //check for the maximum no occurrence in first 15 lines
			int lineCounter = 0;

			String line;
			while((line = br.readLine())!=null && lineCounter<noOfLinesToCheck) {
				char maxOccurencePerLine = getMaxOccurence(line, checkFordelimiter);
				occurenceOfDelimiter[maxOccurencePerLine]++;
				lineCounter++;
			}

			for (int k = 0; k < checkFordelimiter.length()	; k++) {
				if (maxOccurence < occurenceOfDelimiter[checkFordelimiter.charAt(k)]) {
					maxOccurence = occurenceOfDelimiter[checkFordelimiter.charAt(k)];
					defaultDelimiter = checkFordelimiter.charAt(k);
				}
			}
			logger.log(Level.INFO, "[CONVERSION:IMPORT] REMOTE.IMPORTER getAutoDelimiter: {0} {1}", new Object[]{defaultDelimiter, maxOccurence});
			return defaultDelimiter;
		}catch (Exception e) {
			logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER getAutoDelimiter error: {0}",new Object[]{e});
			DocumentUtils.printingStackTrace();
			return defaultDelimiter;
		}finally {
			if(is != null) {
				is.close();
			}
			if(br != null) {
				br.close();
			}
		}
	}

	//Read max occurrence in a line
	public static char getMaxOccurence(String line, String checkFordelimiter) throws Exception {
		int occurenceOfDelimiter[] = new int[256];
		char defaultDelimiter = ',';
		try {
			int maxOccurence = 0;

			//Remove Spaces within double quotes
			String[] parts = line.split("\"", -1);
			for (int i = 1; i < parts.length; i += 2) {
				parts[i] = parts[i].replaceAll("\\s", "");
			}

			line = String.join("\"", parts);
			int lineLength = line.length();
			for (int j = 0; j < lineLength; j++) {
				for(char c:checkFordelimiter.toCharArray()) {
					if(line.charAt(j) == c) {
						occurenceOfDelimiter[line.charAt(j)]++;
					}
				}
			}
			for (int k = 0; k < checkFordelimiter.length()	; k++) {
				if (maxOccurence < occurenceOfDelimiter[checkFordelimiter.charAt(k)]) {
					maxOccurence = occurenceOfDelimiter[checkFordelimiter.charAt(k)];
					defaultDelimiter = checkFordelimiter.charAt(k);
				}
			}
			return defaultDelimiter;
		}catch (Exception e) {
			logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER getMaxOccurence error: {0}",new Object[]{e});
			DocumentUtils.printingStackTrace();
			return defaultDelimiter;
		}
	}

	public static char getAutoDelimiterOld(byte[] contentBytes) throws Exception {
		InputStream is = null;
		BufferedReader br = null;
		String checkFordelimiter = ",; 	|"; //check for the these delimiters only.

		int occurenceOfDelimiter[] = new int[256];
		char defaultDelimiter = ',';
		try {
			is = new ByteArrayInputStream(contentBytes);
			br=new BufferedReader(new InputStreamReader(is));
			int noOfLinesToCheck = 15; //check for the maximum no occurrence in first 15 lines
			int lineCounter = 0;
			int maxOccurence = 0;

			StringBuilder alllines = new StringBuilder();
			String line;
			while((line = br.readLine())!=null && lineCounter<noOfLinesToCheck) {
				alllines.append(line);
				lineCounter++;
			}
			//get all lines in one string to collect all double quotes
			line = alllines.toString();
			String[] parts = line.split("\"", -1);
			for (int i = 1; i < parts.length; i += 2) {
				  parts[i] = parts[i].replaceAll("\\s", "");
			}
			line = String.join("\"", parts);
			int lineLength = line.length();
			for (int j = 0; j < lineLength; j++) {
				for(char c:checkFordelimiter.toCharArray()) {
					if(line.charAt(j) == c) {
						occurenceOfDelimiter[line.charAt(j)]++;
					}
				}
			}

			for (int k = 0; k < checkFordelimiter.length()	; k++) {
				if (maxOccurence < occurenceOfDelimiter[checkFordelimiter.charAt(k)]) {
					maxOccurence = occurenceOfDelimiter[checkFordelimiter.charAt(k)];
					defaultDelimiter = checkFordelimiter.charAt(k);
				}
			}
			logger.log(Level.INFO, "[CONVERSION:IMPORT] REMOTE.IMPORTER getAutoDelimiter: {0}", new Object[]{defaultDelimiter});
			return defaultDelimiter;
		}catch (Exception e) {
			logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER getAutoDelimiter error: {0}",new Object[]{e});
			StackTraceElement[] elements = Thread.currentThread().getStackTrace();
        	DocumentUtils.printStackTrace(elements);
			return defaultDelimiter;
		}finally {
			if(is != null) {
				is.close();
			}
			if(br != null) {
				br.close();
			}
		}
	}
	
	public static JSONObjectWrapper importEncryptedRemoteBook(String doc, String password) {
        try {
            String fileFormat = EngineConstants.FILEEXTN_ODS;
			JSONObjectWrapper obj = new JSONObjectWrapper();

            if (!RemoteImporter.isNullOrEmpty(password)) {
            	WorkbookContainer container = CurrentRealm.getContainer();

            	String documentId =null;//container.getDocId();
            	byte[] decryptedFile = new byte[0];
            	//doc = request.getParameter("doc");
            	Map<RemoteUtils.Keys, Object> coreDetails = RemoteUtils.getDecryptedContent(doc);
            	Long remoteBookId = (Long) coreDetails.get(RemoteUtils.Keys.REMOTE_BOOK_ID);
            	JsonObject metadata = RemoteUtils.getMetaDataFromDb(remoteBookId, container.getDocOwner());
            	if(metadata.has("password")){
            		String actualPassword = metadata.get("password").getAsString();
            		actualPassword = OAuthUtils.decryptToken(null,actualPassword);
            		if(actualPassword.equals(password)){
            			obj.put("status",true);
            		}else{
            			obj.put("status",false);
            		}
            		//response.getWriter().println(obj);
            		//return;
            		return obj;
            	}
            	if (metadata.has("RequestParams")) {
            		String data= metadata.get("RequestParams").getAsString();
            		JsonObject tempParams =  new JsonParser().parse(data).getAsJsonObject();
            		//logger.info("[CONVERSION:IMPORT] importEncryptedRemoteBook Request Params:"+tempParams);
            		documentId =  container.getDocId();
            		fileFormat =tempParams.get("encryptedsheetformat").getAsString().replace(".","");//tempParams.get("format").getAsString();
            	}

            	byte[] file = RemoteUtils.readEncryptedSheet(documentId, fileFormat,container.getDocOwner(),Long.parseLong(container.getDocOwnerZUID()));
            	try {
            		decryptedFile = ImportExportUtil.decryptedDocument(file, fileFormat, password);
            	} catch (Exception e) {
            		logger.info("[CONVERSION:IMPORT] importEncryptedRemoteBook Exceptions:"+e);
            		obj.put("status",false);
//            		response.getWriter().println(obj);
//            		return;
            		return obj;
            	}
				container.setWorkbook(null, true, false);
				container.setImageBook(null);
            	Importer importer = ImporterFactory.getImporterInstance(fileFormat, null, null, null, null, null, new JSONObjectWrapper(), true, 0, null);
            	FileReader fr = new FileReader();
            	RemoteBook rb = new RemoteBook();
            	rb.setUserDocId(documentId);
            	rb.setUserName(container.getDocOwner());
            	fr.fileName = "fsdfs";//No I18N
            	fr.fileFormat =fileFormat;
            	fr.fileContent = decryptedFile;

            	int conversionSuccess = importer.importRemoteDocument(fr,rb,false,container);
            	if(conversionSuccess == ZFSNGConstants.CONVERSION_SUCCESS) {
            		password = OAuthUtils.encryptToken(password);
            		metadata.add(Constants.PASSWORD,new JsonParser().parse(password));
            		RemoteUtils.setMetadataToDB(remoteBookId,container.getDocOwner(),metadata);
            		obj.put("status", true);
//            		response.getWriter().println(obj);
//            		return;
            		return obj;
            	}else{
            		obj.put("status",false);
//            		response.getWriter().println(obj);
//            		return;
            		return obj;
            	}
            }
        } catch (Exception e) {
            e.getMessage();
            return null;
        }
        return null;
    }

	public static int isNonLiboSizeSupported(byte[] contentBytes, String format, String delimeter) throws Exception {
		InputStream is = null;
		BufferedReader br = null;
		int noOfRows = 0;
		int maxNoOfCols = 0;
		try {
			is = new ByteArrayInputStream(contentBytes);
			br = new BufferedReader(new InputStreamReader(is));
			String line;
			String splitChar = "";
			if(format.endsWith(EngineConstants.FILEEXTN_CSV)) {
				splitChar = EngineConstants.CSV_DELIMITER;
			}else if (format.endsWith(EngineConstants.FILEEXTN_TSV)) {
				splitChar = EngineConstants.TSV_DELIMITER;
			}

			if(delimeter!=null && delimeter!=""){
			    splitChar = delimeter;
			}
			
			while ((line = br.readLine()) != null) {
				noOfRows++;
				String words[] = line.split(splitChar);
				if(maxNoOfCols < words.length) {
					maxNoOfCols = words.length;
				}
			}
			logger.log(Level.INFO,"[CONVERSION:IMPORT] Non Libo Import {0} {1} :", new Object[]{noOfRows, maxNoOfCols});
			if(maxNoOfCols > Utility.MAXNUMOFCOLS)
			{
				logger.log(Level.INFO,"[CONVERSION:IMPORT] Non Libo Import Failed as Max col limit exceeded");
				return ZFSNGConstants.CONVERSION_FAILURE_NO_OF_COLUMNS_EXCEEDED;
			}

			if(noOfRows * maxNoOfCols > Utility.CELLCOUNTLIMIT)
			{
				logger.log(Level.INFO,"[CONVERSION:IMPORT] Non Libo Import Failed as Max Cell limit exceeded");
				return ZFSNGConstants.CONVERSION_FAILURE_NO_OF_CELLS_EXCEEDED;
			}
		}catch(Exception e) {
			return ZFSNGConstants.CONVERSION_SUCCESS;

		}finally {
			if(is != null) {
				is.close();
			}
			if(br != null) {
				br.close();
			}
		}
		return ZFSNGConstants.CONVERSION_SUCCESS;
	}
//	public static void checkStatusandRetryImport(String resourceId) {
//		try {
//			logger.info("[CONVERSION] - Resource key execution from  the redis queue:::"+resourceId);
//			Long timeout = Long.parseLong("60000");//EnginePropertyUtil.getSheetPropertyValue("CONVERSION_SERVER_TIMEOUT"));//No I18N
//
//
//			double ridscore = RedisHelper.zscore(redisCSKey, resourceId);
//			if (ridscore <= (System.currentTimeMillis() - timeout)) {
//
//				String details = RedisHelper.hget(redisCSKey_details, resourceId);
//				JSONObject obj = new JSONObject(details);
//				logger.info("Object entries in redis::::"+obj);
//				String zfsngVersionId = obj.getString("zfsngVersionId");
//				String importerZuid = obj.getString("importerZuid");
//				String format = obj.getString("format");
//				String ownerZuid = obj.getString("ownerZuid");
//				String docOwner = obj.getString("authorName");
//				String loginName = DocumentUtils.getZUserName(importerZuid);
//				int count = obj.getInt("retryCount") +1 ;
//				addImportDetailsToRedisQueue(resourceId,zfsngVersionId,format,docOwner,importerZuid,ownerZuid,count);
//				HashMap documentdetails = (HashMap) DocumentUtils.getDocumentDetails(resourceId, docOwner);
//
//				String docName = String.valueOf(documentdetails.get("DOCUMENT_NAME"));
//				String docId = String.valueOf(documentdetails.get("DOCUMENT_ID"));
//				String docType = String.valueOf(documentdetails.get("DOCUMENT_TYPE"));
//				DeleteResourceUtils dr = new DeleteResourceUtils();
//				dr.deleteHeadData(docId, docOwner, ownerZuid);
//				dr.deleteVersionData(docId, docOwner, ownerZuid);
//				dr.deleteImages(docId, docOwner, ownerZuid);
//				logger.info("import retry for resource id and count:::;" + resourceId+":::"+count);
//				if(count <=2 ) {
//
//					Importer importer = ImporterFactory.getImporterInstance(format);
//					int conversionSuccess = importer.importSheetDocument(ownerZuid, resourceId, docId, docOwner, loginName, importerZuid, zfsngVersionId, docName, format, Integer.parseInt(docType), null, false);
//					String docsSpaceId = ZohoFS.getOwnerZID(resourceId);
//					logger.info("update message and infor:::;" + docsSpaceId);
//					ZohoFS.updateResourceConversionStatus(docsSpaceId, resourceId, zfsngVersionId, format, "import", conversionSuccess, "");    //NO I18N
//				}
//				else{
//					logger.info("[COVNERSION] More than 2 retry for the resource entry from redis:::"+resourceId);
//				}
//				ImportExportUtil.removeImportDetailsFromRedisQueue(resourceId);
//			}
//		} catch (Exception e) {
//			logger.log(Level.WARNING,"Problem while reImporting the failure entries, Exception :" , e);
//		}
//	}
	// public static void reSyncEntriesFromRedis(){
	// 	try {
	// 		String redisCSKey = EnginePropertyUtil.getSheetPropertyValue("CONVERSION_SERVER_REDISKEY");//No I18N
	// 		String redisCSKey_details = redisCSKey +"_details";//No I18N
	// 		Long timeout = Long.parseLong(EnginePropertyUtil.getSheetPropertyValue("CONVERSION_SERVER_TIMEOUT"));//No I18N
	// 		List<String> ridList =  RedisHelper.zrangeByScore(redisCSKey, 0, System.currentTimeMillis() - timeout);
	// 		logger.log(Level.INFO,"[PRE SYNC]ZohoSheetListener - LazyConversion older then {0} are {1}: ",new Object[]{timeout, ridList.size()});
	// 		for(int i=0;i<ridList.size();i++) {
	// 			String resourceId = ridList.get(i);
	// 			checkStatusandRetryImport(resourceId);
	// 		}
	// 		List<String> ridList1=    RedisHelper.zrangeByScore(redisCSKey, 0, System.currentTimeMillis() - timeout);
	// 		logger.log(Level.INFO,"[POST SYNC]ZohoSheetListener - LazyConversion older then {0} are {1}: ",new Object[]{timeout, ridList1.size()});
	// 	}catch (Exception e){
	// 		logger.log(Level.WARNING,"Problem while reImporting the failure entries, Exception :" , e);
	// 	}
	// }

}
