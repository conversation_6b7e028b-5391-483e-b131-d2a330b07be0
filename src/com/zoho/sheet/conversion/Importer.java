/*$Id$*/
package com.zoho.sheet.conversion;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;


import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.mfw.bean.BeanUtil;
import com.adventnet.persistence.DataAccessException;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.WorkbookContainer.ContainerEntity;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.zoho.sheet.action.FileReader;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.zfsng.client.ZCRM;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceType;
import com.zoho.zfsng.constants.ServiceType;
import com.zoho.zfsng.constants.ZFSNGConstants;
import com.zoho.zfsng.exception.ZFSException;



/**
 * <AUTHOR> Contains common import related methods and fields for
 * Libo, NonLibo, and Xlsx Importer comes Here.
 */
public abstract class Importer {
	protected boolean isVersionUpload=false;
	protected String loginName = null;
	protected String zuId = null;
	protected String zfsngVersionId = null;
	protected String format = null;
	protected int docType = 0;
	//private String liboFileName = null;
	protected byte[] contentBytes = null;
	protected String docId = null;
	protected String docName = null;
	protected String resourceId = null;
	protected String docOwner = null;
	protected String docsSpaceId = null;
	protected String importedByZuid = null;
	protected boolean importAsTemplate=false;
	protected boolean migrateAllVersions=false;

	protected RemoteBook remoteBook = null;
	protected static Logger logger = Logger.getLogger(Importer.class.getName());
	protected String remoteBookId = null;
	protected boolean isEmptyFileUsed = false;
	boolean isRename = false;
	protected String lang = null;
	protected String countrycode = null;
	protected JsonObject sheetsettings = new JsonObject();
	protected JsonObject rbs_settings = new JsonObject();
	protected JsonElement trackFields =null;
	protected JsonElement hideTopBar = null;
	protected JsonElement hideMenuBar = null;
	protected String zfsngVersionNo = null;
	int savemode;
	protected boolean doRetry =true;
	private static Semaphore semaphore = new Semaphore(EngineConstants.MAX_CONVERSION_THREADS, true);

	private void dropDocumentSheetIDMappingFromTable(String tableName,Long documentSheetID,String dbSpace)
	{
		try {
			Persistence pers = SheetPersistenceUtils.getPersistence(dbSpace);
			Criteria crt = new Criteria(new Column(tableName, "DOCUMENTSHEET_ID"), documentSheetID, QueryConstants.EQUAL);//NO I18N
			pers.delete(crt);
		} catch (Exception e) {
			logger.info("Deleting From documentsheets Table Failed........");//NO I18N
		}
		
	}
	
	public void handleSheetSpecificResources(DataObject oldSheetsDataObject,List<String> newSheetNames,String dbSpace) 
	{	
		
		
		Iterator rows;
		try {
			rows = oldSheetsDataObject.getRows("DocumentSheets");
			String [] tableNames={"SheetCharts","StockFormula","SheetImages","PivotRanges","PivotSheets","FreezePanes"};//NO I18N
			while(rows.hasNext())
			{
					Row row=(Row)rows.next();
					
					String oldSheetName=(String)row.get("SHEET_NAME");
					Long oldSheetDocId=(Long)row.get("DOCUMENTSHEET_ID");
					
				if(newSheetNames.contains(oldSheetName))
				{
					//If one of the new sheets has the same name as one of the old sheets			
					//retain sheet specific resources like pub ranges and delete DB entries for charts,pivot,free panes etc as they will be created fresh for the new document anyway
				
					for(String tableName:tableNames)
					{
						dropDocumentSheetIDMappingFromTable(tableName, oldSheetDocId, dbSpace);
					}				
					
				}
				else{
					//drop all Sheet related entries such as freepane, including rangePub etc info ,dropping entry from DocumentSheets will trigger cascade delete				
					dropDocumentSheetIDMappingFromTable("DocumentSheets", oldSheetDocId, dbSpace);	//No I18N
					//cascade delete doesn't handle published resources as there are in public DB	or orgDB,hence deleting here
					deleteExternalPublishedRanges(oldSheetDocId, dbSpace);
					deleteOrgPublishedRanges(oldSheetDocId, dbSpace, ImportExportUtil.getZOIDFromZuid(this.zuId));
					
				}
				
			}
		} catch (Exception e) {
			logger.log(Level.WARNING, "Exception while handling sheet resources in Version Import",e);
			
		}
		
	}
	
	public int importRemoteDocument(FileReader fr, RemoteBook remoteBook,boolean isDbUpdatereq,WorkbookContainer container) throws Exception {
		try {
		boolean permit = semaphore.tryAcquire(5, TimeUnit.SECONDS);
		if(!permit) {
			logger.log(Level.OFF, "server busy resourceId: {0}, importedByZuid: {1}", new Object[]{resourceId, importedByZuid});
			return ZFSNGConstants.CONVERSION_FAILURE_SERVER_BUSY;
		}
		this.remoteBook = remoteBook;
		this.isEmptyFileUsed = fr.isEmptyFileUsed;
		this.zuId = String.valueOf((remoteBook.isAuthCase()) ? DocumentUtils.getZUID(remoteBook.getUserName()) : -1);
		this.docType =Constants.REMOTE_DOCUMENT;
		
		//String fileName = null;
		//Collaborative document can raise a import request for the same document. 
		if (remoteBook.isExistingDoc()) {
			logger.log(Level.INFO, "[CONVERSION:IMPORT] Existing Remote Document, Only DB updates");
		} else {
			if (remoteBook.allowEmptyFileContent()) {
				fr.useEmptyFileContent();
			}
		}
		//Read the contents in byte[]
		//fr.read(request);
		this.docId = (String) remoteBook.getUserDocId();
		this.docOwner = (String) remoteBook.getUserName();
		if (docOwner != null && docOwner != "") {
			this.loginName = this.docOwner;
		} else {
			this.docOwner = Constants.REMOTE_USER;
			this.loginName = Constants.REMOTE_USER;
		}
		this.contentBytes = fr.fileContent;
		this.resourceId = null;
		this.format = fr.fileFormat.replace(".", "").toLowerCase();
		this.lang = fr.lang;
		this.countrycode = fr.countrycode;
		this.sheetsettings=fr.sheetsettings;
		this.rbs_settings = fr.rbs_settings;
		this.savemode =fr.savemode;
		//this.format = this.format.toLowerCase();
		this.docName = (fr.fileName != null) ? fr.fileName : "";
		this.docName = ImportExportUtil.replaceSpecialCharactersinDocumentName(docName);
		if ((docName.indexOf(".") == -1)) {
			this.docName += "." + format;
		}
		if(remoteBook.isExistingDoc()) {
			logger.log(Level.INFO, "[CONVERSION:IMPORT] File size Check, Existing Remote Document, No Size Check Required");
		}
		else if(!ImportExportUtil.isFileSizeSupportedNew(this.contentBytes)) {
			return ZFSNGConstants.CONVERSION_FAILURE_DOCUMENT_SIZE_TOO_LARGE;
		}
		return importDocument(fr, remoteBook,isDbUpdatereq,container);
		} finally {
			semaphore.release();
		}
	}


	public int importSheetDocument(String zuid, String resourceId, String documentId,
			   String docOwner, String loginName, String importedByZuid, String zfsngVersionId, String fileName,
			   String format, int docType, byte[] contentBytes, boolean importAsTemplate, String zfsngVersioNo) throws Exception {
		return importSheetDocument( zuid, resourceId, documentId,
				   docOwner, loginName, importedByZuid, zfsngVersionId, fileName,
				    format,  docType,  contentBytes,  importAsTemplate,  zfsngVersioNo, false);
	}
	public int importSheetDocument(String zuid, String resourceId, String documentId,
								   String docOwner, String loginName, String importedByZuid, String zfsngVersionId, String fileName,
								   String format, int docType, byte[] contentBytes, boolean importAsTemplate, String zfsngVersioNo, boolean allVersionMigration) throws Exception {
		try { boolean permit = semaphore.tryAcquire(5, TimeUnit.SECONDS);
		if(!permit) {
			logger.log(Level.OFF, "server busy resourceId: {0}, importedByZuid: {1}", new Object[]{resourceId, importedByZuid});
			return ZFSNGConstants.CONVERSION_FAILURE_SERVER_BUSY;
		}

		this.docId = documentId;
		this.docName = fileName;
		this.resourceId = resourceId;
		this.zfsngVersionId = zfsngVersionId;
		this.docOwner = docOwner;
		this.format = format.toLowerCase();
		this.docType = docType;
		this.zuId = zuid;
		this.migrateAllVersions = allVersionMigration;
		this.importedByZuid = importedByZuid;
		this.loginName = loginName;
		this.importAsTemplate=importAsTemplate;
		this.zfsngVersionNo = zfsngVersioNo;
		String newDocName = ImportExportUtil.replaceSpecialCharactersinDocumentName(docName);
		if(!newDocName.equals(docName)){
			this.isRename = true;
		}
		this.docName = newDocName;
		if (contentBytes == null) {
			this.contentBytes = ZohoFS.getResourceContentForVersionId(ZohoFS.getOwnerZID(resourceId), resourceId, zfsngVersionId, format);
		} else {
			this.contentBytes = contentBytes;
		}

		if(!ImportExportUtil.isFileSizeSupportedNew(this.contentBytes)) {
			return ZFSNGConstants.CONVERSION_FAILURE_DOCUMENT_SIZE_TOO_LARGE;
		}

		return importDocument();
		} finally {
			semaphore.release();
		}
	}
	//used for Remote API imports . Remote/Auth remote/Excel Viewer/Sratch view and  Import Menu->Insert as new sheet etc uses remote API internally
	protected abstract int importDocument(FileReader fr, RemoteBook remoteBook,boolean isDBUpdateReq,WorkbookContainer container) throws Exception;

	//used for ordinary Authenticated document import / Import menu , Docs Import , Gdrive Import
	protected abstract int importDocument() throws Exception;






	protected WorkbookContainer getChunkContainer() throws Exception {
		WorkbookContainer chunkContainer = null;
		try {
			ContainerEntity entity = WorkbookContainer.getEntity();
			entity.docOwner = this.docOwner;
			entity.resourceId = this.resourceId;
			
	        try {
				if(this.docType != Constants.REMOTE_DOCUMENT) {
					if(this.resourceId != null){
						entity.docsSpaceId = ZohoFS.getOwnerZID(this.resourceId);
					}else {
					entity.docsSpaceId = String.valueOf(DocumentUtils.getZUID(this.docOwner));
					}
				}else {
					if(this.resourceId != EngineConstants.FILENAME_REMOTEDOC){
						entity.docsSpaceId = ZohoFS.getOwnerZID(this.resourceId);
					}
				}
	        } catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			//entity.docOwnerId        =    String.valueOf(docOwnerId);
			entity.documentId = String.valueOf(this.docId);
			entity.documentName = this.docName;
			
			chunkContainer = new WorkbookContainer(entity);
		} catch (java.lang.Exception e) {
			logger.log(Level.WARNING, null, e);
		}
		return chunkContainer;
	}

	/**
	 * Add entries to RemoteBooks,RemoteBooks state and Documents table
	 *
	 * @param remoteBook
	 * @param docType
	 * @throws Exception
	 */
	protected void updateRemoteDB(RemoteBook remoteBook, int docType) throws Exception {

		//HashMap resultantMap = new HashMap();
		//String userName = remoteBook.getUserName();
		//logger.info("userName >>>>>>>>>>>> "  +userName);
		//resultantMap.put("USER_NAME", userName);//No I18N
		
		long current_time = System.currentTimeMillis();
		/* -- Documents table update. -- */
		HashMap updater = remoteBook.getMapHolder();

		//String _docName = documentName;
		//String docName = (_docName != null) ? _docName : ""; 
		//String docName = (_docName != null) ? _docName : (docName != null) ? docName : ""; //Empty document name update for remote editor
		//resultantMap.put("DOCUMENT_NAME", docName);
		//String docId = null;		
		//Remote tables update
		HashMap rbm = (HashMap) updater.get("REMOTEBOOKS_TABLE"); //RemoteBooks Map
		logger.log(Level.INFO, "[CONVERSION:IMPORT] REMOTEBOOKS_TABLE : {0} : {1} : {2} ", new Object[]{resourceId, docOwner, docId});
		Row rbRow = null;
		
		if (rbm != null) {
			if (remoteBook.isAuthCase()) {
				String libraryID = null;
				long loginZUID = DocumentUtils.getZUID(docOwner);
				long ownerZuid =loginZUID;
				//Storing file in attachment lirary of User in ZSOID
				try {
					if (rbm.containsKey("ZSOID") && !"-1".equals(rbm.get("ZSOID").toString())) {
						ownerZuid = Long.valueOf(rbm.get("ZSOID").toString());
						libraryID = ZCRM.getUserAttachmentLibrary(rbm.get("ZSOID").toString(), ""+loginZUID);	//No I18N
					}else if (rbm.containsKey("SAVE_URL_OR_AGENT_NAME") && (rbm.get("SAVE_URL_OR_AGENT_NAME").toString().contains("crm") || rbm.get("SAVE_URL_OR_AGENT_NAME").toString().contains("WebSheetSync"))) {//No I18N
						String handBackId = rbm.containsKey("HANDBACK_ID")?rbm.get("HANDBACK_ID").toString():null;//No I18N
						String zsoid = null;
						try {
							JsonObject jsonobj = new JsonParser().parse(handBackId).getAsJsonObject(); 
							zsoid = jsonobj.get("zgid").toString();
							zsoid = zsoid.replace("\"", "");
						}catch(Exception e) {
							if(handBackId.contains(";")) {
								zsoid = handBackId.split(";")[0];
							}else {
								logger.info("zsoid reset");
								ownerZuid=loginZUID;
								libraryID=null;
								zsoid=null;
							}
						}
						logger.info("zsoid ...."+zsoid);
						logger.info("handbackid ...."+rbm.get("HANDBACK_ID"));
						
						if (zsoid != null) {
							ownerZuid = Long.valueOf(zsoid);
							libraryID = ZCRM.getUserAttachmentLibrary(zsoid, ""+loginZUID);	//No I18N
						}
					}else if (rbm.containsKey("HANDBACK_ID") && rbm.get("HANDBACK_ID").toString().split(";").length >1) {//No I18N
						String zsoid = rbm.containsKey("HANDBACK_ID")?rbm.get("HANDBACK_ID").toString().split(";")[0]:null;//No I18N
						logger.info("zsoid ...."+zsoid);
						
						if (zsoid != null) {
							ownerZuid = Long.valueOf(zsoid);
							libraryID = ZCRM.getUserAttachmentLibrary(zsoid, ""+loginZUID);	//No I18N
						}
					}else {
						libraryID=null;
						ownerZuid=loginZUID;
					}
				}catch (Exception e) {
					// TODO: handle exception
					logger.log(Level.WARNING, "[CONVERSION:IMPORT] Problem while getting LIBRARY ID :" +loginZUID, e);
					libraryID=null;
					ownerZuid=loginZUID;
				}
				
				logger.info("LIBRARY ID ..."+libraryID);
				String rId = null;
				try {
					rId = DocumentUtils.addNewDocumentOrTemplateToZFSNG(String.valueOf(ownerZuid), String.valueOf(loginZUID), docName, ResourceType.DOC_LIBRARY, ResourceType.REMOTEAUTH_WORKBOOK, libraryID, ServiceType.SHEET, null, "zsheet");//No I18N
				}catch (ZFSException e) {
					logger.log(Level.WARNING, "[CONVERSION:IMPORT] Problem while creating auth remote RID ", e);
					if (libraryID != null) {
						rId = DocumentUtils.addNewDocumentOrTemplateToZFSNG(String.valueOf(loginZUID), docName, ResourceType.DOC_LIBRARY, ResourceType.REMOTEAUTH_WORKBOOK,null);
					}
					// TODO: handle exception
				}
				
				this.resourceId = rId;
				this.docsSpaceId = ZohoFS.getOwnerZID(rId);
				this.zuId = this.docsSpaceId;	
				this.docOwner = DocumentUtils.getZUserName(this.docsSpaceId);
				remoteBook.setSpaceName(this.docOwner);
				this.loginName = this.docOwner;
				
				
				docId = DocumentUtils.addNewDocumentToDB(docOwner, docName, importAsTemplate, docType, remoteBook.isAuthCase(), rId)[0];
			} else {
				docId = DocumentUtils.addNewDocumentToDB(docOwner, docName, importAsTemplate, docType, remoteBook.isAuthCase())[0];
			}

			Persistence pers = (Persistence) BeanUtil.lookup("Persistence", this.docOwner);//No I18N
			DataObject DO = pers.constructDataObject();

			rbRow = new Row("RemoteBooks"); //RemoteBooksRow
			rbRow.set("API_KEY_ID", rbm.get("API_KEY_ID"));
			rbRow.set("BOOK_NAME", docName);
			rbRow.set("SAVE_URL_OR_AGENT_NAME", (String) rbm.get("SAVE_URL_OR_AGENT_NAME"));
			rbRow.set("PUSH_FORMAT", (String) rbm.get("PUSH_FORMAT"));
			//				if(rbm.get("HANDBACK_ID") == null){
			//					rbRow.set("HANDBACK_ID", "No HANDBACKID");s
			//				} else{
			rbRow.set("HANDBACK_ID", (String) rbm.get("HANDBACK_ID"));
			//				}
			rbRow.set("CREATED_DATE", current_time);
			rbRow.set("ALLOW_EDIT", (Boolean) rbm.get("ALLOW_EDIT"));
			rbRow.set("DOCUMENT_ID", Long.valueOf(docId));
			if(savemode != -1){
				rbRow.set("IS_WMS_SAVE_MODE", savemode);
				
			}else {
			rbRow.set("IS_WMS_SAVE_MODE", (Integer) rbm.get("IS_WMS_SAVE_MODE"));
			
			}
			rbRow.set("LAST_STATUS_TIME", current_time);

			//User document Id setting
			String userDocId = (String) rbm.get("USER_DOC_ID");
			if (userDocId != null) {
				rbRow.set("USER_DOC_ID", userDocId);
			} else {
				if (remoteBook.collabDoc()) {
					rbRow.set("USER_DOC_ID", rbRow.get("REMOTE_BOOK_ID"));
				} else {
					rbRow.set("USER_DOC_ID", "");
				}
			}

			rbRow.set("SAVE_AS_URL", (String) rbm.get("SAVE_AS_URL"));
			//rbs_settings.add("save_url",  (String) rbm.get("SAVE_AS_URL"));
			rbRow.set("META_DATA",sheetsettings.toString());
                        rbRow.set("PERSISTENCE", rbm.get("PERSISTENCE") == null ? false : (Boolean) rbm.get("PERSISTENCE"));
			DO.addRow(rbRow);
			pers.add(DO);
			
			logger.info("[CONVERSION:IMPORT] rbRow : "+rbRow.get("REMOTE_BOOK_ID"));
			
		}

		//Remote Books State table update
		HashMap rbsm = (HashMap) updater.get("REMOTEBOOKS_STATE_TABLE"); //RemoteBooks Map
                // Don't print rbsm as it contain url having authtoken.
		logger.log(Level.INFO, "[CONVERSION:IMPORT] REMOTEBOOKS_STATE_TABLE :{0}:{1}", new Object[]{resourceId, docOwner});
		Row rbsRow = null;
		Persistence pers = (Persistence) BeanUtil.lookup("Persistence", this.docOwner);//No I18N
		
		DataObject DO = pers.constructDataObject();
		if (rbsm != null) {
			rbsRow = new Row("RemoteBooks_State"); //RemoteBooks_State Row
			Long remoteBookId = (Long) rbsm.get("REMOTE_BOOK_ID");
			logger.info("[CONVERSION:IMPORT] remoteBookId : "+remoteBookId);
			rbsRow.set("REMOTE_BOOK_ID", (remoteBookId != null) ? remoteBookId : rbRow.get("REMOTE_BOOK_ID"));
			rbsRow.set("REMOTE_USER", ((remoteBook.isAuthCase()) ? remoteBook.getfullName() : rbsm.get("USER_NAME")));
			rbsRow.set("COLLABORATION_USER_NAME", rbsm.get("USER_NAME"));
			rbsRow.set("MODE", rbsm.get("MODE"));

			logger.info("Save_url_Params read-----"+rbs_settings);

			rbsRow.set("META_DATA",rbs_settings.toString() );
			remoteBook.setMode((Integer) rbsm.get("MODE"));  //NO I18N
			rbsRow.set("LAST_ACCESSED_TIME", new Long(current_time));
			rbsRow.set("DOC_USER_STATUS", 0);
			rbsRow.set("HANDBACK_ID", (String) rbsm.get("HANDBACK_ID"));
			logger.info("[CONVERSION:IMPORT] rbsRow : "+rbsRow);
			DO.addRow(rbsRow);

		}
		//docId will be an encrypted ID for a document whose's first opening mode was collabedit.
		pers.add(DO);
		
		if (remoteBook.isExistingDoc() || docId != null) {
			remoteBook.setDocumentId(docId);
		}
		//			if(docId != null) {
		//				//resultantMap.put("DOCUMENT_ID", Long.valueOf(docId));//NO I18N
		//				//resultantMap.put("STORE_FILE", "true"); //NO I18N
		//				//check with sasi
		//				remoteBook.setDocumentId(docId);
		//			}

		if (rbRow != null) {
			//resultantMap.put("REMOTE_BOOK_ID", rbRow.get("REMOTE_BOOK_ID"));//NO I18N	
			remoteBook.setRemoteBookId((Long) rbRow.get("REMOTE_BOOK_ID")); //NO I18N
			this.remoteBookId = String.valueOf(remoteBook.getRemoteBookId());
		}
		if (rbsRow != null) {
			//resultantMap.put("REMOTE_BOOK_STATE_ID", rbsRow.get("REMOTE_BOOK_STATE_ID"));//NO I18N
			remoteBook.setRemoteBookStateId((Long) rbsRow.get("REMOTE_BOOK_STATE_ID")); //NO I18N
		}

		//logger.info("===========>>> resultantMap : " + resultantMap);
		//return docId;
	}


	private void deleteExternalPublishedRanges(long documentSheetID, String owner) {
		Criteria cri = new Criteria(new Column("RangePublish", "SHEET_ID"), documentSheetID, QueryConstants.EQUAL);//No I18N
		Persistence pers = SheetPersistenceUtils.getPersistence("Public");//No I18N
		try {
			pers.delete(cri);
		} catch (DataAccessException e) {
			logger.log(Level.WARNING, "[CONVERSION:IMPORT] Problem while deleting org published ranges:" + resourceId + ":" + docOwner, e.getMessage());
		}
	}

	private void deleteOrgPublishedRanges(long documentSheetID, String owner, long zoid) {
		Criteria cri = new Criteria(new Column("RangePublish", "SHEET_ID"), documentSheetID, QueryConstants.EQUAL);//No I18N
		String space;
		if (zoid != -1) {
			space = Constants.CORPORATEDBPREFIX + zoid;
		} else {
			return;
		}
		Persistence pers = SheetPersistenceUtils.getPersistence(space);
		try {
			pers.delete(cri);
		} catch (DataAccessException e) {
			logger.log(Level.WARNING, "[CONVERSION:IMPORT] Problem while getting org published ranges :" + resourceId + ":" + docOwner, e.getMessage());
		}
	}
public void setContentBytes(byte[] contentBytes){
	this.contentBytes = contentBytes;
}
public void setFormat(String format){
	this.format = format;
}

}
