//<%-- $Id$ --%>
package com.zoho.sheet.conversion;

import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.LifeSpanController;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.sun.star.beans.XPropertySet;
import com.sun.star.bridge.XBridge;
import com.sun.star.bridge.XBridgeFactory;
import com.sun.star.connection.XConnection;
import com.sun.star.connection.XConnector;
import com.sun.star.frame.XComponentLoader;
import com.sun.star.lang.EventObject;
import com.sun.star.lang.XComponent;
import com.sun.star.lang.XEventListener;
import com.sun.star.lang.XMultiComponentFactory;
import com.sun.star.uno.UnoRuntime;
import com.sun.star.uno.XComponentContext;
//import com.zoho.sheet.conversion.ConversionServer;
//import com.zoho.sheet.util.OOConnectionPool;

//public  class ConnectionPool {
//	public static HashMap desktop_map = new HashMap();
//	public static Logger logger = Logger.getLogger(ConnectionPool.class.getName());
//	
//	private static String ip_value = null;
//	private static String port_value = null;
//	private static XBridge
//	
//	Factory xBridgeFactory;
//	private static XEventListener listeners;
//	private XComponentLoader desktops;
//	private static HashMap listeners_map = new HashMap();
//
//	private static HashMap servicemanagers_map = new HashMap();
//	private static HashMap redis_details = new HashMap();
//	private static int count = 0;
//	private int serviceId;
//
//	public static void initPool() {
//		 List<String> servers = new ArrayList();
//		try {
//			logger.info("CONVERSION SERVERS initpool Creating Libo Connections");
//			XComponentContext xRemoteContext = com.sun.star.comp.helper.Bootstrap.createInitialComponentContext(null);
//			// Object x =
//			// xRemoteContext.getServiceManager().createInstanceWithContext("com.sun.star.connection.Connector",
//			// xRemoteContext );
//			// xConnector =
//			// (XConnector)UnoRuntime.queryInterface(XConnector.class, x);
//			Object x = xRemoteContext.getServiceManager().createInstanceWithContext("com.sun.star.bridge.BridgeFactory", xRemoteContext); // No I18N
//			xBridgeFactory = (XBridgeFactory) UnoRuntime.queryInterface(XBridgeFactory.class, x);
//			if (xBridgeFactory == null) {
//				logger.info("CONVERSION SERVERS ================>"	+ "bridge factory is null");
//				return;
//			}
//			redis_details = (HashMap) RedisHelper.hgetAll(ConversionServer.redisKey+ConversionServer.groupName);
//			Iterator itr = redis_details.entrySet().iterator();
//			while (itr.hasNext()) {
//
//				Map.Entry entry = (Map.Entry) itr.next();
//				ip_value = (String) entry.getKey();
//				if(!servers.contains(ip_value)){
//					servers.add(ip_value);
//				}
//				
//				port_value = (String) entry.getValue();
//				String[] ports = port_value.split(":");
//
//				for (String _ports : ports) {
//
//					count++;
//					desktop_map.put(ip_value + ":" + _ports,createDesktop(ip_value + ":" + _ports));
//
//				}
//			}
//			createLiboDir(servers, EnginePropertyUtil.getSheetPropertyValue("LIBOImportLocation"));//explored Directory location to save the Import file at LibreOffice Machine // No I18N
//			createLiboDir(servers, EnginePropertyUtil.getSheetPropertyValue("LIBOExportLocation"));//explored Directory location to save the Export file at LibreOffice Machine // No I18N
//			createLiboDir(servers, EnginePropertyUtil.getSheetPropertyValue("ImageExportLocation"));//explored Directory location to save the Images at LibreOffice Machine // No I18N
//			
//		} catch (Exception e) {
//			logger.log(Level.WARNING, "Problem in redis object" + e);
//		}
//	}
//
//	public static Map getterDesktop() {
//		return desktop_map;
//	}
//
//	public static XComponentLoader createDesktop(String sName) throws Exception {
//		
//		
//		XBridge bridge = null;
//		XComponentContext xRemoteContext = null;
//		com.sun.star.frame.XComponentLoader officeComponentLoader;
//		XMultiComponentFactory xRemoteServiceManager = null;
//		String host_details[] = sName.split(":");
//		String con = "socket,host=" + host_details[0] + ",port="+ host_details[1]; // No I18N
//
//		try {	
//			xRemoteContext = com.sun.star.comp.helper.Bootstrap.createInitialComponentContext(null);
//
//			Object x = xRemoteContext.getServiceManager().createInstanceWithContext("com.sun.star.connection.Connector", xRemoteContext); //No I18N
//
//			XConnector xConnector = (XConnector) UnoRuntime.queryInterface(XConnector.class, x);
//
//			logger.info("CONVERSION SERVERS createDesktop trying connection with:"+con);
//			XConnection connection = xConnector.connect(con);
//
//			if (connection == null) {logger.info("CONVERSION SERVERS ================>"+ "Connection is null");
//				return null;
//			}
//
//			// this is the bridge that you will dispose
//			
//			bridge = xBridgeFactory.createBridge(sName, "urp", connection, null); // No I18N
//			
//
//			XComponent xComponent = (XComponent) UnoRuntime.queryInterface(XComponent.class, bridge);
//
//			listeners = new ConnectionListeners(sName);
//
//			listeners_map.put(sName, listeners);
//			xComponent.addEventListener(listeners);
//
//			// get the remote instance
//			x = bridge.getInstance("StarOffice.ServiceManager"); // No I18N
//
//			// Query the initial object for its main factory interface
//			xRemoteServiceManager = (XMultiComponentFactory) UnoRuntime.queryInterface(XMultiComponentFactory.class, x);
//
//			// For NumberFormatter Service
//			servicemanagers_map.put(sName, xRemoteServiceManager);
//
//			// retrieve the component context (it's not yet exported from the
//			// office)
//			// Query for the XPropertySet interface.
//			XPropertySet xProperySet = (XPropertySet) UnoRuntime.queryInterface(XPropertySet.class, xRemoteServiceManager);
//
//			// Get the default context from the office server.
//			Object oDefaultContext = xProperySet.getPropertyValue("DefaultContext"); // No I18N
//
//			// Query for the interface XComponentContext.
//			XComponentContext xOfficeComponentContext = (XComponentContext) UnoRuntime.queryInterface(XComponentContext.class, oDefaultContext);
//			// now create the desktop service
//			// NOTE: use the office component context here !
//			Object oDesktop = xRemoteServiceManager.createInstanceWithContext("com.sun.star.frame.Desktop", xOfficeComponentContext); //No I18N
//		
//
//			officeComponentLoader = (XComponentLoader) UnoRuntime.queryInterface(XComponentLoader.class, oDesktop);
//
//			String available = (null != officeComponentLoader ? "available": "not available"); // No I18N
//
//			logger.info("CONVERSION SERVERS ================>"+ "Created Bridge-" + sName);
//
//			return officeComponentLoader;
//		} catch (UnknownHostException uhe) {
//			logger.info("CONVERSION SERVERS createDesktop UnknownHostException:" + uhe);
//			xRemoteContext = null;
//			throw uhe;
//		}
//		catch (Exception e) { // works from Patch 1
//			//logger.log(Level.WARNING, null, e);
//			logger.info("CONVERSION SERVERS createDesktop Exceptions:" + e);
//		//	 e.printStackTrace();
//			xRemoteContext = null;
//			throw e;
//		}
//	}
//	public static ConnectionObject getRemoteDesktop() throws Exception {
//		boolean doRetry = true;
//		boolean isEnable = EnginePropertyUtil.getSheetPropertyValue("CONVERSION_SERVER_ENABLE").equals("true");//No I18N
//		try {
//			return getConversionServerDesktop();
//		}
//		catch(Exception e){
//			logger.log(Level.WARNING,"Problem while getting Libo server",e);
//			if(doRetry && isEnable) {
//				doRetry =false;
//				 return getConversionServerDesktop();
//			}else {
//				//logger.info("Conversion:Import,Status::Failure after RETRY");
//				throw e;
//			}
//		}
//	}
//
//	public synchronized static ConnectionObject getConversionServerDesktop() throws Exception {
//		
////		if(!ConversionServer.isDistributedModelEnabled) {
////			ConnectionObject connection=OOConnectionPool.getRemoteDesktop();
////			return connection;
////		} else {
//			XComponentLoader XCL = null;
//			ConnectionObject ConnectionObject = new ConnectionObject();
//			Set<String> Keys = RedisHelper.hkeys(ConversionServer.redisKey+ConversionServer.groupName);
//			int req_counter = 0;
//			int ip_no;
//			int port_no;
//			if (desktop_map.isEmpty()) {
//				logger.info("CONVERSION SERVERS desktop_map is Empty so calling init to recreate the Libo Connections");
//				initPool();
//			}
//	
//			if (RedisHelper.hget(ConversionServer.redisKey+"counter", ConversionServer.redisKey+ConversionServer.groupName) != null) {
//	
////				List<Object> pipelineResult = RedisHelper.pipeline(ConversionServer.redisKey+"counter",ConversionServer.redisKey+ConversionServer.groupName);//No I18N
////				ArrayList lst = (ArrayList) pipelineResult.get(3);
////				req_counter = Integer.parseInt(lst.get(0).toString());
//				Long val = RedisHelper.hincrBy(ConversionServer.redisKey+"counter",ConversionServer.redisKey+ConversionServer.groupName,1); //No I18N
//				req_counter = Integer.parseInt(val.toString());
//			}
//			Set<String> ip_redis = RedisHelper.hkeys(ConversionServer.redisKey+ConversionServer.groupName);
//			String ips[] = new String[ip_redis.size()];
//			int ip_cnt = (int) RedisHelper.hlen(ConversionServer.redisKey+ConversionServer.groupName);
//		//	logger.info("$$$$$$$$$$$$$"+ip_cnt+ RedisHelper.hlen(redisKey+groupName));
//			String server_selected = null;
//			int cnt = 0;
//			for (String ip : ip_redis) {
//				ips[cnt++] = ip;
//			}
//	
//			int rounds_cnt = (int) (req_counter / ip_cnt);
//	
//			if (rounds_cnt >= 1) {
//	
//				if (ip_cnt == 1) {
//	
//					ip_no = 0;
//	
//				} else {
//					ip_no = (int) (req_counter % ip_cnt);
//				}
//				String ports[] = RedisHelper.hget(ConversionServer.redisKey+ConversionServer.groupName, ips[ip_no]).split(":");
//				int port_cnt = ports.length;
//				port_no = rounds_cnt % port_cnt;
//	
//				server_selected = ips[ip_no] + ":" + ports[port_no];
//	
//			} else {
//	
//				if (ip_cnt == 1) {
//	
//					ip_no = 0;
//	
//				} else {
//					ip_no = req_counter;
//				}
//				String portredis = RedisHelper.hget(ConversionServer.redisKey+ConversionServer.groupName, ips[ip_no]);
//	
//				if (portredis.contains(":")) {
//					String ports[] = portredis.split(":");
//					server_selected = ips[req_counter] + ":" + ports[0];
//				} else {
//	
//					server_selected = ips[req_counter] + ":" + portredis;
//				}
//			}
//			
//			try {
//			if (!desktop_map.containsKey(server_selected)) {
//				
//					
//					XComponentContext xRemoteContext = com.sun.star.comp.helper.Bootstrap.createInitialComponentContext(null);
//					// Object x =
//					// xRemoteContext.getServiceManager().createInstanceWithContext("com.sun.star.connection.Connector",
//					// xRemoteContext );
//					// xConnector =
//					// (XConnector)UnoRuntime.queryInterface(XConnector.class, x);
//					Object x = xRemoteContext.getServiceManager().createInstanceWithContext("com.sun.star.bridge.BridgeFactory", xRemoteContext); // No I18N
//					xBridgeFactory = (XBridgeFactory) UnoRuntime.queryInterface(XBridgeFactory.class, x);
//					if (xBridgeFactory == null) {
//						logger.info("CONVERSION SERVERS ================>"	+ "bridge factory is null");
//					}
//					logger.info("CONVERSION SERVERS ================>"+ "+---------- Using Desktop " + server_selected);
//					XCL=createDesktop(server_selected);
//					
//	
//					ConnectionObject.setLiboHost(ips[ip_no]);
//					desktop_map.put(server_selected,XCL);
//					ConnectionObject.setxComponentLoader(XCL);
//					ConnectionObject.setsName(server_selected);
//					return ConnectionObject;
//				} 
//			 else {
//	
//				logger.info("CONVERSION SERVERS ================>"+ "+---------- Using Desktop " + server_selected);
//				ConnectionObject.setLiboHost(ips[ip_no]);
//				XCL = (XComponentLoader) desktop_map.get(server_selected);
//				ConnectionObject.setxComponentLoader(XCL);
//				ConnectionObject.setsName(server_selected);
//				return ConnectionObject;
//	
//			}
//			//return ConnectionObject;
//			}catch(Exception e) {
//				logger.log(Level.WARNING,"Problem while getting Libo Server",e);
//				throw e;
//			}
////		}
//		
//	}
//	public synchronized static String getConversionMiniServerDesktop() throws Exception {
//
//
//			Set<String> Keys = RedisHelper.hkeys(ConversionServer.redisKey+ConversionServer.groupName_mini);
//			int req_counter = 0;
//			int ip_no;
//			int port_no;
//
//
//			if (RedisHelper.hget(ConversionServer.redisKey+"counter", ConversionServer.redisKey+ConversionServer.groupName_mini) != null) {
//
////				List<Object> pipelineResult = RedisHelper.pipeline(ConversionServer.redisKey+"counter",ConversionServer.redisKey+ConversionServer.groupName);//No I18N
////				ArrayList lst = (ArrayList) pipelineResult.get(3);
////				req_counter = Integer.parseInt(lst.get(0).toString());
//				Long val = RedisHelper.hincrBy(ConversionServer.redisKey+"counter",ConversionServer.redisKey+ConversionServer.groupName_mini,1); //No I18N
//				req_counter = Integer.parseInt(val.toString());
//			}
//			Set<String> ip_redis = RedisHelper.hkeys(ConversionServer.redisKey+ConversionServer.groupName_mini);
//			String ips[] = new String[ip_redis.size()];
//			int ip_cnt = (int) RedisHelper.hlen(ConversionServer.redisKey+ConversionServer.groupName_mini);
//			//	logger.info("$$$$$$$$$$$$$"+ip_cnt+ RedisHelper.hlen(redisKey+groupName));
//			String server_selected = null;
//			int cnt = 0;
//			for (String ip : ip_redis) {
//				ips[cnt++] = ip;
//			}
//
//			int rounds_cnt = (int) (req_counter / ip_cnt);
//
//			if (rounds_cnt >= 1) {
//
//				if (ip_cnt == 1) {
//
//					ip_no = 0;
//
//				} else {
//					ip_no = (int) (req_counter % ip_cnt);
//				}
//				String ports[] = RedisHelper.hget(ConversionServer.redisKey+ConversionServer.groupName_mini, ips[ip_no]).split(":");
//				int port_cnt = ports.length;
//				port_no = rounds_cnt % port_cnt;
//
//				server_selected = ips[ip_no] + ":" + ports[port_no];
//
//			} else {
//
//				if (ip_cnt == 1) {
//
//					ip_no = 0;
//
//				} else {
//					ip_no = req_counter;
//				}
//				String portredis = RedisHelper.hget(ConversionServer.redisKey+ConversionServer.groupName_mini, ips[ip_no]);
//
//				if (portredis.contains(":")) {
//					String ports[] = portredis.split(":");
//					server_selected = ips[req_counter] + ":" + ports[0];
//				} else {
//
//					server_selected = ips[req_counter] + ":" + portredis;
//				}
//			}
//
//
//		return server_selected;
//
//	}
//
//
//	public synchronized void nullifyDesktop(XComponentLoader loader) {
//
//		if (loader == null) {
//			return;
//		}
//
//		if (desktop_map.containsValue(loader)) {
//			Iterator itr = desktop_map.entrySet().iterator();
//			while (itr.hasNext()) {
//				Map.Entry entry = (Map.Entry) itr.next();
//				String loader_value = (String) entry.getValue();
//				if (loader_value.equals(loader)) {
//					desktop_map.remove(entry.getKey());
//					break;
//				}
//
//			}
//
//		}
//	}
//
//	public synchronized static Object createService(String sName, String serviceName) throws Exception {
////		if(ConversionServer.isDistributedModelEnabled) {
//			XMultiComponentFactory oServiceManager_xMCF = (XMultiComponentFactory) servicemanagers_map.get(sName);
//			// XPropertySet xMCF_props = Utility.XPropertySet(oServiceManager_xMCF);
//			XPropertySet xMCF_props = (XPropertySet) UnoRuntime.queryInterface(XPropertySet.class, oServiceManager_xMCF);
//			Object oDefaultContext = xMCF_props.getPropertyValue("DefaultContext"); // No
//																					// I18N
//			// XComponentContext oDefaultContext_XCC = Utility.XComponentContext(oDefaultContext);
//			XComponentContext oDefaultContext_XCC = (XComponentContext) UnoRuntime.queryInterface(XComponentContext.class, oDefaultContext);
//			return oServiceManager_xMCF.createInstanceWithContext(serviceName,oDefaultContext_XCC);
//			
////		} 
////		else {
////			return OOConnectionPool.createService(Integer.parseInt(sName),serviceName);
////		}
//	}
//
//	private boolean isBridgeActive(String sName) {
//		XBridge xb = xBridgeFactory.getBridge(sName); 
//		return (xb != null);
//	}
//
//	private void createLiboDir(String[] servers, String liboDir) {
//
//		String userName = "sas"; // user account // No I18N
//		String privateKey = System.getProperty("user.home")+ EnginePropertyUtil.getSheetPropertyValue("PrivateKeyLocation");//No I18N 
//		// private-key of the app server machine move to conf
//																		
//		for (String server : servers) {
//			try {
//				String host = server;
//				java.util.Properties config = new java.util.Properties();
//				config.put("StrictHostKeyChecking", "no");
//				JSch jsch = new JSch();
//				jsch.addIdentity(privateKey);
//				Session session = jsch.getSession(userName, host);// SSH2 session
//																	
//				session.setConfig(config);
//				session.connect();
//
//				Channel channel = session.openChannel("sftp"); // No I18N
//				channel.connect();
//
//				ChannelSftp channelSftp = (ChannelSftp) channel;
//				String[] folders = liboDir.split("/");
//				for (String folder : folders) {
//					if (folder.length() > 0) {
//						try {
//							channelSftp.cd(folder);
//						} catch (SftpException e) {
//							channelSftp.mkdir(folder);
//							channelSftp.cd(folder);
//						}
//					}
//				}
//				logger.info("CONVERSION SERVERS ================>"+ "Directory created " + liboDir);
//				channelSftp.exit();
//				channel.disconnect();
//				session.disconnect();
//			} catch (Exception e) {
//				logger.info("CONVERSION SERVERS ================>"+ "Error in creating directory :" + e);
//			}
//		}
//
//	}
//
//	static class ConnectionListeners implements XEventListener {
//
//		private String sName;
//
//		ConnectionListeners(String sName) {
//			this.sName = sName;
//		}
//
//		public void disposing(EventObject event) {
//			getterDesktop().remove(sName);
//
//		}
//	}
//
//	public synchronized XComponentLoader reconnect(XComponentLoader loader)
//
//			throws Exception {
//
//		if (desktop_map.containsValue(loader)) {
//			Iterator itr = desktop_map.entrySet().iterator();
//			while (itr.hasNext()) {
//				Map.Entry entry = (Map.Entry) itr.next();
//				String loader_value = (String) entry.getValue();
//				if (loader_value.equals(loader)) {
//					return createDesktop((String) entry.getKey());
//
//				}
//
//			}
//
//		}
//		return null;
//	}
//	 public static void createLiboDir(List<String> servers, String liboDir){
//			LifeSpanController lsc = null;
//			String userName = "sas";    //user account // No I18N
//			String privateKey = System.getProperty("user.home") + EnginePropertyUtil.getSheetPropertyValue("PrivateKeyLocation"); //private-key of the app server machine //move to conf // No I18N
//			for(String server : servers){
//				try{
//					String host = server;
//					java.util.Properties config = new java.util.Properties(); 
//					config.put("StrictHostKeyChecking", "no");
//					JSch jsch = new JSch();
//					jsch.addIdentity(privateKey);
//					logger.info("TIME STAMP ON CREATE"+System.currentTimeMillis());
//					lsc =  new LifeSpanController(Thread.currentThread(), "Open Office Component Creation Thread", (long) 2000, null, null); //NO I18N
//					lsc.control();
//					Session session=jsch.getSession(userName, host);//SSH2 session //TODO try with no user name
//					session.setConfig(config);
//					session.connect();	
//
//					Channel channel = session.openChannel("sftp");	 // No I18N
//					channel.connect();
//					ChannelSftp channelSftp = (ChannelSftp) channel;
//					String[] folders = liboDir.split( "/" );
//					for ( String folder : folders ) {
//						if ( folder.length() > 0 ) {
//							try {
//								channelSftp.cd( folder );
//							}
//							catch ( SftpException e ) {
//								channelSftp.mkdir( folder );
//								channelSftp.cd( folder );
//							}
//						}
//					}
//					logger.info("[CONVERSION:SERVER] Directory created "+liboDir);
//					channelSftp.exit();
//					channel.disconnect();
//					session.disconnect();
//					logger.info("TIME STAMP ON End suceess"+System.currentTimeMillis());
//					
//				}catch(Exception e){
//					logger.info("[CONVERSION:SERVER] Error in creating directory :"+e);
//				}
//				finally{
//					logger.info("TIME STAMP ON failure end"+System.currentTimeMillis());
//					
//					lsc.cancel();
//				}
//			}
//		}
//
//	
//
////	public static class ConnectionObject {
////		private XComponent xComponent;
////		private XComponentLoader xComponentLoader;
////		private String liboHost;
////		private String sName;
////		private String bridgeNo;
////
////		public XComponent getxComponent() {
////			return xComponent;
////		}
////
////		public void setxComponent(XComponent xComponent) {
////			this.xComponent = xComponent;
////		}
////
////		public XComponentLoader getxComponentLoader() {
////			return xComponentLoader;
////		}
////
////		public void setxComponentLoader(XComponentLoader xComponentLoader) {
////			this.xComponentLoader = xComponentLoader;
////		}
////
////		public String getLiboHost() {
////			return liboHost;
////		}
////
////		public void setLiboHost(String liboHost) {
////			this.liboHost = liboHost;
////		}
////
////		public String getsName() {
////			return sName;
////		}
////
////		public void setsName(String sName) {
////			this.sName = sName;
////		}
////		public String getBridgeNo() {
////			return bridgeNo;
////		}
////
////		public void setBrigeNo(String bridgeNo) {
////			this.bridgeNo = bridgeNo;
////		}
////
////	}
//
//}
