package com.zoho.sheet.conversion;

import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;


public class ImportEncryptedRemoteBookAction extends StrutsRequestHandler {
    Logger logger = Logger.getLogger(RemoteImporter.class.getName());
    private static String locale = null;

    public String execute() throws Exception {
    	String password = request.getParameter("password");

//        password =  URLDecoder.decode(password);

        String doc = request.getParameter("doc");
//        JSONObject objResponse = ImportExportUtil.importEncryptedRemoteBook(doc, password);
//        response.getWriter().println(objResponse);
    	//VerifyPasswordUtil.verfiyPassword(request,response);
        //JSONObject obj = VerifyPasswordUtil.verfiyPassword(doc, password);
        JSONObjectWrapper obj = ImportExportUtil.importEncryptedRemoteBook(doc, password);
    	response.getWriter().println(obj);
        return null;
        
    }
}
