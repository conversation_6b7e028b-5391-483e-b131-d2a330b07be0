/* $Id$ */
package com.zoho.sheet.conversion;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipOutputStream;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;

import com.adventnet.zoho.websheet.model.util.DCMigrationUtil;
import org.apache.commons.io.IOUtils;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.ErrorCode;
import com.adventnet.zoho.websheet.model.ImageBook;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.WorkbookSettings;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.EngineUtils1;
import com.adventnet.zoho.websheet.model.util.LifeSpanController;
import com.adventnet.zoho.websheet.model.writer.xlsx.WorkbookToXML;
import com.adventnet.zoho.websheet.model.writer.xlsx.XlsxConversionStats;
import com.adventnet.zoho.websheet.model.writer.xlsx.image.XlsxWriterImageContainer;
import com.sun.star.lang.XComponent;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
//import com.zoho.sheet.conversion.ConnectionPool.ConnectionObject;
import com.zoho.sheet.parse.RangeAddress;
import com.zoho.sheet.util.AsposeUtils;
import com.zoho.sheet.util.HtmlConstructor;
import com.zoho.sheet.util.PDFController;
import com.zoho.sheet.util.RemoteUtils;
import com.zoho.sheet.util.ZSStats;

/**
 * <AUTHOR>
 *
 */
public class Exporter {
	/* -- Static Block ---*/

	public static final Logger LOGGER = Logger.getLogger(Exporter.class.getName());
	public static String defaultFormat = "xlsx"; //NO I18N
	public static int useCounter =0;

	private WorkbookContainer container = null;
	private String format = null;
	String printType = null;
	String activeSheetName = null; //Used for CSV & TSV format
	String associatedSheetName = null;
	String sheetVersionNo = null;
	String zfsngVersionNum = null;
	String zfsngVersionId = null;
	boolean isTopVersionDCMigration = false;
	boolean isAllVersionMigration = false;
	// explicitly ask to save and create another version when exporting.
	// Don't save when exporting a non native format. 
	Boolean allowSave = true;
	Boolean isSheetIndexBased = false;
	RangeAddress rangeAddress = null;
	File tempFile = null;
	//ConnectionObject connectionObject = null;
	//ConnectionPool connectionpool = null;
	//HttpServletRequest request =null;
	//HttpServletResponse response = null;
	/* -- Constructors -- */
	private boolean isExportedByXlsxWriter = false;
	private boolean isCloneRequired = true;
	private static Semaphore semaphore = new Semaphore(EngineConstants.MAX_CONVERSION_THREADS, true);

	public Exporter(WorkbookContainer container, String format) {
		LOGGER.log(Level.INFO, "[CONVERSION:EXPORT]{0}", container.getResourceId());
		this.container = container;
		//		this.userName = userName;
		//		this.setExportFormat(format);
		this.format = format;
	}

	public Exporter(WorkbookContainer container, String format, String sheetName) {
		LOGGER.log(Level.INFO, "[CONVERSION:EXPORT]{0}", container.getResourceId());
		this.container = container;
		this.format = format;
		this.activeSheetName = RemoteUtils.maskNull(sheetName);
		//		this.userName = userName;
		//		this.setExportFormat(format);
		//		this.setActiveSheet(sheetName);
	}
	/*public Exporter(WorkbookContainer container, String format, String sheetVersionNum, String zfsngVersionNum, ConnectionObject connectionObject) {
		logger.info("[CONVERSION:EXPORT]"+container.getResourceId());
		this.container = container;
		this.connectionObject = connectionObject;
		this.sheetVersionNo = sheetVersionNum;
                this.zfsngVersionNum = zfsngVersionNum;
		this.format = format;
		this.request =null;
		this.response = null;
	}*/

	public Exporter(WorkbookContainer container, String format, String sheetVersionNum, String zfsngVersionNum) {
		LOGGER.log(Level.INFO, "[CONVERSION:EXPORT]{0}", container.getResourceId());
		this.container = container;
		this.sheetVersionNo = sheetVersionNum;
		this.zfsngVersionNum = zfsngVersionNum;
		this.format = format;
	}

	public Exporter(WorkbookContainer container, String format, String sheetVersionNum, String zfsngVersionNumm, String zfsngVersionId, boolean isTopVerionDCMigration, boolean isAllVersionMigration) {
		LOGGER.log(Level.INFO, "[CONVERSION:EXPORT]{0}", container.getResourceId());
		this.container = container;
		this.sheetVersionNo = sheetVersionNum;
		this.zfsngVersionNum = zfsngVersionNumm;
		this.format = format;
		this.zfsngVersionId = zfsngVersionId;
		this.isTopVersionDCMigration = isTopVerionDCMigration;
		this.isAllVersionMigration = isAllVersionMigration;
	}
	
	public Exporter(WorkbookContainer container, String format, String sheetVersionNum, String zfsngVersionNum, String sheetName) {
		LOGGER.log(Level.INFO, "[CONVERSION:EXPORT]{0}", container.getResourceId());
		this.container = container;
		this.sheetVersionNo = sheetVersionNum;
		this.zfsngVersionNum = zfsngVersionNum;
		this.format = format;
		this.activeSheetName = sheetName;
	}
	
	//public Exporter(WorkbookContainer container, String format, String sheetVersionNum, String zfsngVersionNum, ConnectionObject connectionObject, HttpServletRequest request, HttpServletResponse response) {
//	public Exporter(WorkbookContainer container, String format, String sheetVersionNum, String zfsngVersionNum, ConnectionObject connectionObject) {
//		LOGGER.log(Level.INFO, "[CONVERSION:EXPORT]{0}", container.getResourceId());
//		this.container = container;
//		this.connectionObject = connectionObject;
//		this.sheetVersionNo = sheetVersionNum;
//		this.zfsngVersionNum = zfsngVersionNum;
//		this.format = format;
//		//            this.request =request;
//		//            this.response = response;
//		//		this.setExportFormat(format);
//	}
	
//	public Exporter(WorkbookContainer container, String format, String sheetVersionNum, String zfsngVersionNum, ConnectionObject connectionObject, String sheetName) {
//		LOGGER.log(Level.INFO, "[CONVERSION:EXPORT]{0}", container.getResourceId());
//		this.container = container;
//		this.connectionObject = connectionObject;
//		this.sheetVersionNo = sheetVersionNum;
//		this.zfsngVersionNum = zfsngVersionNum;
//		this.format = format;
//		this.activeSheetName = sheetName;
//	}
	//	/*-- Methods --*/
	//
	public void allowSave(Boolean allowSave) {
		this.allowSave = allowSave;
	}
	
	public void setAssociatedSheetName(String asn) {
		this.associatedSheetName = asn;
	}
	
	public String getAssociatedSheetName() {
		return this.associatedSheetName;
	}
	//
	//	public void setUserAgent(String supplier) {
	//		this.userAgent = supplier;
	//	}
	//
	public void setTempFile(File file) {
		this.tempFile = file;
	}

	public void setPrintType(String printType){
		this.printType = printType;
	}

	public String getPrintType(){
		return this.printType;
	}

	//
	//		public void setResponse(HttpServletResponse response) {
	//			this.response = response;
	//			if (response != null) {
	//				this.allowResponseSettings = true;
	//			}
	//		}
	//	
	//		public HttpServletResponse getResponse() {
	//			return this.response;
	//		}
	//
	//	private void setExportFormat(String supplier) {
	//		//TODO: Need to add the validation.
	//		// Not valid then set 'defaultFormat'
	//		if (supplier != null) {
	//			if (supplier.indexOf(".") == 0) {
	//				supplier = supplier.substring(1); // removing dot
	//			}
	//			this.format = supplier;
	//		} else {
	//			this.format = (supplier != null) ? supplier : defaultFormat;
	//		}
	//	}
	//
	public void setActiveSheet(String supplier) {
		this.activeSheetName = supplier;
	}
	public void setSheetVersionNo(String sheetVersionNo) {
		this.sheetVersionNo = sheetVersionNo;
	}
	
	public String getActiveSheetName() {
		return this.activeSheetName;
	}
	//	public void setzfsngVersionId(String zfsngVersionId) {
	//		this.zfsngVersionId = zfsngVersionId;
	//	}
	//	
	//	public LOConnectionObject getLoConnectionObject() {
	//		return loConnectionObject;
	//	}
	//
	//	public void setLoConnectionObject(LOConnectionObject loConnectionObject) {
	//		this.loConnectionObject = loConnectionObject;
	//	}

	//	private String getMimeType() {
	//		String defaultMimeType = "application/vnd.ms-excel"; // No I18N
	//		if ("ods".equalsIgnoreCase(this.format)) {
	//			return "application/vnd.oasis.opendocument.spreadsheet"; // No I18N
	//		} else if ("xlsx".equalsIgnoreCase(this.format)) {
	//			return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"; //No I18N
	//		} else if ("html".equalsIgnoreCase(this.format)) {
	//			return "text/html"; //NO I18N
	//		} else if ("pdf".equalsIgnoreCase(this.format)) {
	//			return "application/pdf"; //NO I18N
	//		} else if ("tsv".equalsIgnoreCase(this.format)) {
	//			return "text/tsv"; //NO I18N
	//		} else if ("csv".equalsIgnoreCase(this.format)) {
	//			return "text/csv"; //NO I18N
	//		} else if ("zip".equalsIgnoreCase(this.format)) {
	//			return "application/zip";    //NO I18N
	//		}
	//		return defaultMimeType;
	//	}

	public void setSheetIndex(Boolean supplier) {
		this.isSheetIndexBased = supplier;
	}

	public void setRangeAddress(Object supplier) {
		if (supplier instanceof String) {
			String _range = RemoteUtils.maskNull((String) supplier);
			if (_range != null) {
				this.rangeAddress = RangeAddress.forLabel(_range);
			}
		} else if (supplier instanceof RangeAddress) {
			this.rangeAddress = (RangeAddress) supplier;
		}
		//logger.info("[CONVERSION:EXPORT]" + this.rangeAddress);
	}

	/////generic export code

        public byte[] getDocumentZipBytes(HttpServletRequest request) throws Exception {

//            Map<Keys, Number> coreDetails = RemoteUtils.getDecryptedContent(request.getParameter("doc"));
//            int spaceCounter = (int) coreDetails.get(Keys.SPACECOUNTER);
//            String docOwner = Constants.REMOTE_USERS[spaceCounter];
            long t1 = System.currentTimeMillis();
        	LOGGER.info("[CONVERSION : EXPORT] Using ZIP Conversion:");
            ZSStats.increment(ZSStats.EXPORT_FORMAT_ZIP);
            byte[] content = null;
			HashMap properties = new HashMap();
			properties.put("addLocalURL", true);
            HtmlConstructor hc = new HtmlConstructor(container, properties, request);
            Workbook workbook = container.getWorkbook(null);
            String[] sheetNames = workbook.getSheetNames(false);
            
            for (String sheetName : sheetNames) {
                
                Sheet sheet = workbook.getSheet(sheetName);
                hc.setRange(null);
                hc.setSheetName(sheetName);
                hc.parseSheetCharts(workbook, sheet);
                hc.parseSheetImages(sheet);
                hc.parseSheetButtons(sheet);
				hc.parseSheetSlicers(workbook, sheet);
                
                hc.parseHTML();
            }
            
            try {
                content = hc.getDocumentBytes();
                long t2 = System.currentTimeMillis();
    			LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] Success Format:{0}:{1} Conversion {2}", new Object[]{format, container.getResourceId(), t2-t1});
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "[CONVERSION:EXPORT] [GEN HTML] Unable to get Content of the Spreadsheet... ", container.getResourceId());
            }
            return content;
        }
        
          public byte[] getDocumentPDFBytes() throws Exception { 	LOGGER.info("[CONVERSION : EXPORT] Using PDF Conversion:");
            Workbook workbook = this.container.getWorkbook(null);
            workbook.recalculateDynamicFormulas();
            WorkbookSettings wbSettings = workbook.getWorkbookSettings();
			  JSONObjectWrapper wbPrintSettings = wbSettings.getPrintSetting();
            return getDocumentPDFBytes(null, wbPrintSettings);
        }
	public byte[] getDocumentPDFBytes(JSONObjectWrapper wbPrintSettings) throws Exception { 	LOGGER.info("[CONVERSION : EXPORT] Using PDF Conversion:");
		Workbook workbook = this.container.getWorkbook(null);
		workbook.recalculateDynamicFormulas();


		return getDocumentPDFBytes(null, wbPrintSettings);
	}
        
        public byte[] getDocumentPDFBytes(HttpServletRequest request) throws Exception {
            LOGGER.info("[CONVERSION : EXPORT] Using PDF Conversion:");
            Workbook workbook = this.container.getWorkbook(null);
            workbook.recalculateDynamicFormulas();
            WorkbookSettings wbSettings = workbook.getWorkbookSettings();
			JSONObjectWrapper clientPDFOptions = request.getParameter("page_settings") != null ? new JSONObjectWrapper(request.getParameter("page_settings")) : wbSettings.getPrintSetting();
            return getDocumentPDFBytes(request, clientPDFOptions);
        }

	/**
	 *
	 * @param request
	 * @param pdfOptions null, if default pdf options has to be used for export.
	 * @return
	 * @throws Exception
	 */
	public byte[] getDocumentPDFBytes(HttpServletRequest request, JSONObjectWrapper pdfOptions) throws Exception {
            //NOTE : Pdf options will not be in its current format for API case, so splitted the method
            long t1 = System.currentTimeMillis();
            ZSStats.increment(ZSStats.EXPORT_FORMAT_PDF);
            LOGGER.info("[CONVERSION : EXPORT] Using PDF Conversion:");
            Workbook workbook = this.container.getWorkbook(null);
            PDFController pdfController = new PDFController(this.container, pdfOptions);

            String printType = "WORKBOOK";         // No I18N
            if(pdfOptions != null && pdfOptions.has("printType")){
                printType = pdfOptions.getString("printType");
            }
            
            if(request != null) {
                printType = ((String) request.getAttribute("printType")) != null ? ((String) request.getAttribute("printType")) : printType;  
            }
			setPrintType(printType);
			byte[] pdfBytes = null;
           
            if(printType != null && !printType.equals("WORKBOOK")) {
                
                String sheetName = null;
                if (request != null) {
                    sheetName = request.getParameter("currentSheet");
                }
                if (sheetName == null) {
                    sheetName = this.activeSheetName;
                }

                if (sheetName == null) {

                    sheetName = workbook.getActiveSheetName();
                    if (sheetName == null) {

                        Sheet sheet = workbook.getSheet(0);
                        sheetName = sheet.getName();
                    }
                }
                
                switch (printType) {
               
                    case "RANGE":
                        pdfBytes = pdfController.getRangeAsPDF(sheetName, this.rangeAddress, request);
                        break;

                    case "SHEET":
                        pdfBytes = pdfController.getSheetAsPDF(sheetName, request);
                        break;

                    case "WORKBOOK":
                        pdfBytes = pdfController.getWorkBookAsPDF(request);
                        break;

                    default:
                        pdfBytes = pdfController.getWorkBookAsPDF(request);
                        break;
                }
               
            } else {
               
                pdfBytes = pdfController.getWorkBookAsPDF(request);
               
            }
            long t2 = System.currentTimeMillis();
            LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] Success Format:{0}:{1} Conversion {2}", new Object[]{format, container.getResourceId(), t2-t1});
            return pdfBytes;
        }

	public byte[] getDocumentCsvTsvBytes() throws Exception {
		try {
			long t1 = System.currentTimeMillis();
			LOGGER.info("[CONVERSION : EXPORT] Using Csv Tsv Conversion:");
			Workbook workBook = container.getWorkbook(zfsngVersionNum);
			boolean isCsv = ("csv".equalsIgnoreCase(this.format));    // No I18N
			boolean isTsv = ("tsv".equalsIgnoreCase(this.format));    // No I18N
			//StringBuffer outputData = new StringBuffer();
			byte[] outputByte = null;
			Sheet sheetToExport=null;
			//no sheetName given , export first sheet as csv
			
			if(this.associatedSheetName != null) {
				this.activeSheetName = workBook.getSheetByAssociatedName(this.associatedSheetName).getName();
			}  
			
			if (this.activeSheetName == null && this.associatedSheetName == null) {
				this.activeSheetName =  workBook.getSheet(0).getName();
			}
			
			if (this.isSheetIndexBased) {
				int _sheetIdx = Integer.parseInt(this.activeSheetName);
				this.activeSheetName = workBook.getSheet(_sheetIdx-1).getName(); // OO Sheet index strats with 0 (zero)
			}

			sheetToExport=workBook.getSheet(activeSheetName);
			if (this.rangeAddress == null) {

				setRangeAddress(new RangeAddress(0, 0, sheetToExport.getUsedRowIndex(), sheetToExport.getUsedColumnIndex()));
			}
			if (isCsv) {
				ZSStats.increment(ZSStats.EXPORT_FORMAT_CSV);
				outputByte=EngineUtils1.getCSVData(sheetToExport, rangeAddress.startRow, rangeAddress.startCol, rangeAddress.endRow, rangeAddress.endCol, false);					
			} else if (isTsv) {	
				ZSStats.increment(ZSStats.EXPORT_FORMAT_TSV);
				outputByte=EngineUtils1.getTSVData(sheetToExport, rangeAddress.startRow, rangeAddress.startCol, rangeAddress.endRow, rangeAddress.endCol,false);
			}
			long t2 = System.currentTimeMillis();
			LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] Success Format:{0}:{1} Conversion {2}", new Object[]{format, container.getResourceId(), t2-t1});
			return outputByte;
		
		}catch (Exception e) {
			LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] getLiboDocumentBytes Error:",e);
			return null;
		}
	}
	
	public byte[] getXLSXBytes(String sheetName) throws Exception{
		LOGGER.log(Level.INFO, "[CONVERSION : EXPORT] Using XLSX  Conversion Single Sheet {0}:", new Object[]{sheetName});
		return getXLSXBytes_internal(sheetName);
	}

	public byte[] getXLSXBytes() throws Exception{
		return getXLSXBytes_internal(null);
	}
	
	private byte[] getXLSXBytes_internal(String sheetName) throws Exception{
		try {
			boolean permit = semaphore.tryAcquire(5, TimeUnit.SECONDS);
			if(!permit) {
				LOGGER.log(Level.OFF, "server busy resourceId: {0}", new Object[]{container.getResourceKey()});
				throw new RuntimeException("server busy");//No I18N
			}

			final XlsxConversionStats xlsxConversionStats = new XlsxConversionStats(System.currentTimeMillis());
			ZSStats.increment(ZSStats.EXPORT_FORMAT_XLSX);
			LOGGER.info("[CONVERSION : EXPORT] Using XLSX Conversion:");
			if(EngineConstants.XLSX_WRITER_ENABLED) {
				try {
					WorkbookContainer container = this.container;
					Workbook workBook = container.getWorkbook(zfsngVersionNum);
					ImageBook imageBook = container.getImageBook(zfsngVersionNum);
					if(this.isCloneRequired) {
						workBook = workBook.clone();
						imageBook = imageBook.clone();
					}
					LOGGER.log(Level.INFO, "[CONVERSION : EXPORT] getXLSXBytes ZohoCorp User Trying XLSX-Writer");

					Set<String> sheetsToBeParsed = new HashSet<>();

					if(RemoteUtils.maskNull(sheetName) != null) {
						try {
							Sheet sheet = workBook.getSheet(sheetName);
							sheetsToBeParsed.add(sheet.getAssociatedName());
						} catch(Exception e) {
							LOGGER.log(Level.INFO, "[CONVERSION : EXPORT] Using XLSX  Conversion Single Sheet Exception : {0}", new Object[]{e});
						}
					} else {
						for(Sheet sheet : workBook.getSheetList()) {
							sheetsToBeParsed.add(sheet.getAssociatedName());
						}
					}

					for(Sheet sheet : workBook.getSheetList()) {
						if(sheet.getName().length() > 31) {
							Set<String> existing_names = new HashSet<>();
							for(Sheet sheet1 : workBook.getSheetList()) {
								existing_names.add(sheet1.getName());
							}
							sheet.rename(sort_name(sheet.getName(), existing_names));
						}
					}

					for(Sheet sheet : workBook.getSheetList()) {
						final String name = sheet.getName();
						boolean isNameChanged = false;
						StringBuilder stringBuilder = new StringBuilder();
						for(int i = 0; i < name.length(); i++) {
							if(i == 0) {
								while(i < name.length() && name.charAt(i) == '\'') {
									stringBuilder.append("_");
									i++;
								}
								if(i == name.length()) {
									break;
								}
							}
							final char c = name.charAt(i);
							switch(c) {
								case ':':
								case '\\':
								case '/':
								case '?':
								case '*':
								case '[':
								case ']':
									isNameChanged = true;
									stringBuilder.append("_");
									break;
								default:
									stringBuilder.append(c);
							}
						}
						if(isNameChanged) {
							sheet.rename(stringBuilder.toString());
						}
					}

					try(ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
						try(ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
							XlsxWriterImageContainer xlsxWriterImageContainer = new XlsxWriterImageContainer(container, this.sheetVersionNo, workBook, imageBook, sheetsToBeParsed);
							WorkbookToXML workbookToXML = new WorkbookToXML(zipOutputStream, workBook, xlsxWriterImageContainer, xlsxConversionStats, sheetsToBeParsed);
							workbookToXML.write();
							zipOutputStream.close();
							return byteArrayOutputStream.toByteArray();
						}
					}
				} catch(ProhibitedActionException e) {
					LOGGER.log(Level.OFF, String.join("", "xlsx-writer exception in writing ", container.getResourceKey()), e);
					throw e;
				} catch(Exception e) {
					LOGGER.log(Level.OFF, "TODO:Conversion handle this case Exporter getXLSXBytes_internal1:", e);
					//					LOGGER.log(Level.OFF, String.join("", "xlsx-writer exception in writing ", container.getResourceKey(), " ", xlsxConversionStats.toString()), e);
					//					this.tempFile = EngineUtils1.createTempODSFile(container, zfsngVersionNum, false, null, false);
					//					return getLiboDocumentBytes();
					return null;
				} finally {
					LOGGER.log(Level.INFO, "[CONVERSION : EXPORT] getXLSXBytes xlsx-writer written: {0} : {1}", new Object[]{container.getResourceKey(), xlsxConversionStats.toString()});
				}
			} else {
				LOGGER.info("TODO:Conversion handle this case Exporter getXLSXBytes_internal2:");
				//				this.tempFile = EngineUtils1.createTempODSFile(container, zfsngVersionNum, false, null, false);
				//				return getLiboDocumentBytes();
				return null;
			}
		} finally {
			semaphore.release();
		}
	}

	public byte[] getXLSMBytes() throws Exception{
		LOGGER.info("[CONVERSION : EXPORT] Using XLSM Conversion:");
		try {
			byte[] contenyBytes = getXLSXBytes_internal(null);
			try {
				boolean permit = semaphore.tryAcquire(5, TimeUnit.SECONDS);
				if(! permit) {
					LOGGER.log(Level.OFF, "server busy resourceId: {0}", new Object[]{container.getResourceKey()});
					throw new RuntimeException("server busy");//No I18N
				}
				contenyBytes = AsposeUtils.addMacroModules(contenyBytes, container);
			} finally {
				semaphore.release();
			}
			return contenyBytes;
		}catch (Exception e) {
			LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] Exporter getXLSMBytes Exception", e);
		}
		return null;
	}
	
	private static String sort_name(String name, Set<String> existing_names) throws Exception {
		if (name.length() > 31) {
			int substitutes_length = 0;
			while (substitutes_length < 31) {
				name = name.substring(0, 31 - substitutes_length);
				if (!existing_names.contains(name)) {
					return name;
				}
				for (int i = 1; i < substitutes_length*10; i++) {
					String temp_next_name = name + i;
					if (!existing_names.contains(temp_next_name)) {
						return temp_next_name;
					}
				}
				substitutes_length++;
			}
			throw new Exception("unable to find the name for renaming");
		}
		return name;
	}

	public byte[] getDocumentBytes() throws Exception {
		byte[] bytes = null;
		try {
			boolean isZs = EngineConstants.ZSHEET_FILE_FORMAT.substring(1).equalsIgnoreCase(this.format);
			
			if(this.format != null && this.format.endsWith("nzsheet")) {//No I18N
				return ImportExportUtil.getZohoSheet(this.container, this.zfsngVersionNum, this.sheetVersionNo);
			}
			if(isZs) {
				File zsFile = EngineUtils1.createTempZSFileWithTopChartDBFile(container, zfsngVersionNum, false, null, true);
				try(FileInputStream zsFileIS = new FileInputStream(zsFile)) {
					byte[] byteArrayZSFiles = IOUtils.toByteArray(zsFileIS);
					if(isAllVersionMigration) {
						byteArrayZSFiles= DCMigrationUtil.export_dc_migration_files(byteArrayZSFiles, container, this.zfsngVersionId, this.isTopVersionDCMigration, this.isAllVersionMigration);
					}
					return byteArrayZSFiles;
				} finally {
					if (zsFile != null) {
						if (zsFile.exists()) {
							zsFile.delete();
						}
					}
				}
			}
			if(EngineConstants.FILEEXTN_CSV.equalsIgnoreCase(this.format) || EngineConstants.FILEEXTN_TSV.equalsIgnoreCase(this.format)) {
				LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] getDocumentBytes TODO: Call CSV TSV Export, File format is {0}", format);
				return getDocumentCsvTsvBytes();
			}
			
			//boolean ISASPOSECONVERSIONENABLED = EngineConstants.ISASPOSECONVERSIONENABLED;
			boolean ISASPOSEODSCONVERSIONENABLED = EngineConstants.ISASPOSEODSCONVERSIONENABLED;
			
			//if(ISASPOSECONVERSIONENABLED) {
				if("ods".equalsIgnoreCase(this.format)) {
					LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] using Aspose ODS conversion , File format is {0}", format);
					bytes = getDocumentOdsBytes();
					if(ISASPOSEODSCONVERSIONENABLED) {
						try {
							boolean permit = semaphore.tryAcquire(5, TimeUnit.SECONDS);
							if(! permit) {
								LOGGER.log(Level.OFF, "server busy resourceId: {0}", new Object[]{container.getResourceKey()});
								throw new RuntimeException("server busy");//No I18N
							}
							bytes = AsposeUtils.getSheetAsposeConversion(bytes, EngineConstants.ODS_FILE_FORMAT, format);
						} finally {
							semaphore.release();
						}
					}
				}
				else {
					isExportedByXlsxWriter = true;
					LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] using Aspose XLSX conversion , File format is {0}", format);
					bytes = getXLSXBytes();
					try {
						boolean permit = semaphore.tryAcquire(5, TimeUnit.SECONDS);
						if(! permit) {
							LOGGER.log(Level.OFF, "server busy resourceId: {0}", new Object[]{container.getResourceKey()});
							throw new RuntimeException("server busy");//No I18N
						}
						bytes = AsposeUtils.getSheetAsposeConversion(bytes, EngineConstants.FILEEXTN_XLSX, format);
					} finally {
						semaphore.release();
					}
				}
			//}
//			else {
//				boolean IsLiboMiniServerEnabled = ImportExportUtil.isMiniServerEnabled();
//				boolean IsLiboServerEnabled = ImportExportUtil.isLiboServerEnabled();
//				LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] Using Libo exporter , IsLiboMiniServerEnabled:{0} IsLiboServerEnabled:{1}", new Object[]{ IsLiboMiniServerEnabled, IsLiboServerEnabled});
//
//				if (IsLiboMiniServerEnabled && IsLiboServerEnabled) {
//					if (++useCounter % 10 == 0) {
//						bytes = getLiboDocumentBytesMiniServer();
//					} else {
//						bytes = getLiboDocumentBytes();
//					}
//				} else if (IsLiboMiniServerEnabled) {
//					bytes = getLiboDocumentBytesMiniServer();
//				} else {
//					LOGGER.info("TODO:Conversion handle this case Exporter getDocumentBytes:");
//					//bytes = getLiboDocumentBytes();
//				}
//			}
		}catch(ProhibitedActionException e)
		{
			LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] getDocumentBytes Error:",e);
			throw e;
		}
		catch(Exception e) {
			LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] getDocumentBytes Error:",e);
			return null;
		}
		if(!isExportedByXlsxWriter) {
			try {
				boolean POI_EXPORT = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("POI_EXPORT"));    //NO I18N
				if (POI_EXPORT) {
					bytes = exportFreezePane(bytes);
				}
			} catch (Exception e) {
				LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] exportFreezePane Error:", e);
				return bytes;
			}
		}
		LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] {0} Success", new Object[]{container.getResourceId()});
		return bytes;
	}	

	private byte[] getDocumentOdsBytes() throws Exception{
		LOGGER.info("[CONVERSION:EXPORT] getDocumentOdsBytes");
		LifeSpanController lsc =  new LifeSpanController(Thread.currentThread(), "Open Office Component Creation Thread", null, null, null); //NO I18N
		lsc.control();
		if(this.format == null){
			this.format = defaultFormat;
		}else if (this.format.indexOf(".") == 0) {
			this.format = this.format.substring(1); // removing dot
		}

		//boolean isVersionExport = false;

		try {
			WorkbookContainer container = this.container;
			File tempODSFile = this.tempFile;
			if (container == null) {
				throw new ServletException("Problem while exporting the document");
			}

			String docName = this.container.getDocName();

			if (docName == null || "".equals(docName)) {
				try {
					docName = "Untitled";// No I18N
				} catch (Exception le) {
					docName = "Untitled_Spreadsheet";// No I18N
				}
			}

			boolean isXls = ("xls".equalsIgnoreCase(this.format));    // No I18N
			if(isXls) {
				ZSStats.increment(ZSStats.EXPORT_FORMAT_XLS);
			}
			boolean isOds = ("ods".equalsIgnoreCase(this.format));    // No I18N
			if(isOds) {
				ZSStats.increment(ZSStats.EXPORT_FORMAT_ODS);
			}
			boolean isZs = EngineConstants.ZSHEET_FILE_FORMAT.substring(1).equalsIgnoreCase(this.format);

			if(this.format != null && this.format.endsWith("nzsheet")) {//No I18N
				return ImportExportUtil.getZohoSheet(this.container, this.zfsngVersionNum, this.sheetVersionNo);
			}

			if(isZs) {
				File zsFile = EngineUtils1.createTempZSFileWithTopChartDBFile(container, zfsngVersionNum, false, null, false);
				try(FileInputStream zsFileIS = new FileInputStream(zsFile)) {
					return IOUtils.toByteArray(zsFileIS);
				} finally {
					if (zsFile != null) {
						if (zsFile.exists()) {
							zsFile.delete();
						}
					}
				}
			}
			Workbook workBook = container.getWorkbook(zfsngVersionNum).clone();
			ImageBook imageBook = container.getImageBook(zfsngVersionNum).clone();


			if(tempODSFile == null)
			{
				LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] EXPORT :::: tempODSFile not set. So invoking EngineUtils1.createODSFileWithImageAndChart()");
				tempODSFile = EngineUtils1.createODSFileWithImageAndChart(container, this.sheetVersionNo, workBook, imageBook, false, null, (this.format == null || this.format.endsWith("ods")));//No I18N
				//tempODSFile = EngineUtils1.createTempODSFile(workBook, imageBook, false, null, (this.format == null || this.format.endsWith("ods")));//No I18N
			}
			LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] EXPORT Exporting as Format :{0}", new Object[]{this.format});
			//boolean ISASPOSECONVERSIONENABLED = EngineConstants.ISASPOSECONVERSIONENABLED;
			LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] getDocumentOdsBytes exporting as format :{0}", new Object[]{this.format});
			//if(ISASPOSECONVERSIONENABLED) {
				FileInputStream odsFileIS = new FileInputStream(tempODSFile);
				return IOUtils.toByteArray(odsFileIS);
			//}
//			else if(this.format.equalsIgnoreCase(EngineConstants.FILEEXTN_XLS) || this.format.equalsIgnoreCase(EngineConstants.FILEEXTN_ODS)) {
//				LOGGER.info("[CONVERSION:EXPORT] logging before making connection");
//				// Note: Check this.format is the output format
//				String inputFormat = "ods", outputFormat = this.format; //No I18N
//				byte[] contentBytes = SheetConversion.getConvertedBytes(tempODSFile, inputFormat, outputFormat, true);
//				if(contentBytes == null) {
//					LOGGER.info("[CONVERSION:EXPORT] Export Failed");
//					throw new Exception(ZSStats.EXPORTERROR);
//				}
//				return contentBytes;
//			}
//			else {
//				FileInputStream odsFileIS = new FileInputStream(tempODSFile);
//				return IOUtils.toByteArray(odsFileIS);
//			}

		} catch (Exception e) {
			ZSStats.incrementByCustomValue(ZSStats.EXPORTERROR);
			LOGGER.log(Level.WARNING, "[CONVERSION:EXPORT]", e);
			return null;
		}
	}

	
//	private byte[] getLiboDocumentBytes() throws Exception{
//		//ZSStats.increment(ZSStats.EXPORTS);
//		LifeSpanController lsc =  new LifeSpanController(Thread.currentThread(), "Open Office Component Creation Thread", null, null, null); //NO I18N
//		lsc.control();
//		if(this.format == null){
//			this.format = defaultFormat;
//		}else if (this.format.indexOf(".") == 0) {
//			this.format = this.format.substring(1); // removing dot
//		}
//
//		//TODO: Authorization check have to add
//		//TODO: Increase document public count
//		//Get the param
//		XComponent xComponent = null;
//		OOOutputStream ooos = null;
//		String sFilter = null;
//		DataObject imagesDO = null;
//		DataObject freezePanesDO = null;
//		//		String dir = AppResources.getProperty("server.dir");
//		String filePath = null;
//		boolean isVersionExport = false;
//
//		try {
//			//String versionNo = this.sheetVersionNo;
//			WorkbookContainer container = this.container;
//			File tempODSFile = this.tempFile;
//			if (container == null) {
//				throw new ServletException("Problem while exporting the document");
//			}
//                        /*
//                         * Avoiding this save completely
//                         * Previously, during an export, if the state to export is not a versioned state, we were creating a version and passing the version file to conversion server
//                         * But now instead, we write to a temp file and pass the temp file to conversion server
//                         * 
//                        
//                        else if (sheetVersionNo == null && this.allowSave) {
//				//Saving the document once before doing export.
//				if (container.getWorkbookForSave() == null) {
//
//					//                                        boolean isActionsPending = RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST+container.getResourceKey()) > 0 || 
//					boolean isActionsPending = RedisHelper.hlen(RedisHelper.ACTIONS_LIST+container.getResourceKey()) > 0;
//
//					// In case if the temp actions are still not migrated to Redis list
//					if (!isActionsPending) {
//						Long tempActionsResId = container.getFragmentId(FileName.ACTIONSTEMP, false, null);
//						if (tempActionsResId > 0) {
//							isActionsPending = EngineUtils1.loadActionsList(container, tempActionsResId, FileName.ACTIONSTEMP).size() > 0;
//						}
//					}
//					if (isActionsPending) {
//						LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] The Workbook object is null:", container.getResourceId());
//						container.getWorkbook(null);
//					}
//				}
//                                this.container.save(null, null, true);
//			}
//                        
//                         */
//                        
//			String docId = this.container.getDocId();
//			String docName = this.container.getDocName();
//
//			if (docName == null || "".equals(docName)) {
//				try {
//					//					LocaleMsg localeMsg = (LocaleMsg) request.getSession().getAttribute("LocaleMsg");
//					//					I18nMessage i18nMsg = (I18nMessage) localeMsg;
//					//					docName = i18nMsg.getMsg("Untitled");
//					docName = "Untitled";// No I18N
//				} catch (Exception le) {
//					docName = "Untitled_Spreadsheet";// No I18N
//				}
//			}
//
//			String docOwner = this.container.getDocOwner();
//			String docOwnerId = this.container.getDocOwnerId();
//			boolean isCsv = ("csv".equalsIgnoreCase(this.format));    // No I18N
//			boolean isTsv = ("tsv".equalsIgnoreCase(this.format));    // No I18N
//			boolean isXlsx = ("xlsx".equalsIgnoreCase(this.format));    // No I18N
//			if(isXlsx) {
//				LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] XLSX Libo Export TODO: Call getXLSXBytes");
//			}
//			boolean isXls = ("xls".equalsIgnoreCase(this.format));    // No I18N
//			if(isXls) {
//				ZSStats.increment(ZSStats.EXPORT_FORMAT_XLS);
//			}
//			boolean isOds = ("ods".equalsIgnoreCase(this.format));    // No I18N
//			if(isOds) {
//				ZSStats.increment(ZSStats.EXPORT_FORMAT_ODS);
//			}
//			boolean isPdf = ("pdf".equalsIgnoreCase(this.format));    // No I18N
//			boolean isZs = EngineConstants.ZSHEET_FILE_FORMAT.substring(1).equalsIgnoreCase(this.format);
//
//			if(this.format != null && this.format.endsWith("nzsheet")) {//No I18N
//				return ImportExportUtil.getZohoSheet(this.container, this.zfsngVersionNum, this.sheetVersionNo);
//			}
//			//			boolean isHtml = ("html".equalsIgnoreCase(this.format));    // No I18N
//			//			boolean isZip = ("zip".equalsIgnoreCase(this.format));    // No I18N
//			boolean updateImages = (isXlsx || isXls || isOds || isPdf);
//
//			//			Creating the response moved to respective action class.
//			//			if (this.allowResponseSettings) {
//			//				this.response.reset();    //IE with https (secured) connection export issue fix block starts
//			//				//logger.info("Mime Type:: "  + this.getMimeType());
//			//				this.response.setContentType(this.getMimeType());
//			//				this.response.setCharacterEncoding("UTF-8"); // No I18N
//			//				// This is to prevent browser - IE8 from showing "open" as an download option for security purposes - Added by Srini
//			//				if (isHtml || isPdf) {
//			//					response.setHeader("X-Download-Options", "noopen");    //No I18N
//			//				}
//			//				String fileName = ClientUtils.encodeFileName((((isCsv || isTsv) && this.activeSheetName != null) ? docName + '_' + this.activeSheetName : docName) + "." + this.format);
//			//				//logger.info("FileName : "  +fileName);
//			//				if (this.userAgent != null && this.userAgent.indexOf("Firefox") == -1) {
//			//					this.response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\""); // No I18N
//			//				} else {
//			//					this.response.setHeader("Content-Disposition", "attachment;filename*=UTF-8\'\'" + fileName); // No I18N
//			//				}
//			//			}
//                        if(isZs) {
//                            File zsFile = EngineUtils1.createTempZSFileWithTopChartDBFile(container, zfsngVersionNum, false, null);
//                            try(FileInputStream zsFileIS = new FileInputStream(zsFile)) {
//								return IOUtils.toByteArray(zsFileIS);
//							} finally {
//								if (zsFile != null) {
//									if (zsFile.exists()) {
//										zsFile.delete();
//									}
//								}
//							}
//                        }
//						Workbook workBook = container.getWorkbook(zfsngVersionNum).clone();
//						ImageBook imageBook = container.getImageBook(zfsngVersionNum).clone();
//
////			if(isXlsx && EngineConstants.XLSX_WRITER_ENABLED && WorkbookToXML.allFeaturesExportable(workBook, imageBook, container.getResourceKey())) {
////				try(ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
////					final XlsxConversionStats xlsxConversionStats = new XlsxConversionStats();
////					try(ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
////						XlsxWriterImageContainer xlsxWriterImageContainer = new XlsxWriterImageContainer(container, this.sheetVersionNo, workBook, imageBook);
////						WorkbookToXML workbookToXML = new WorkbookToXML(zipOutputStream, workBook, xlsxWriterImageContainer, xlsxConversionStats);
////						workbookToXML.write();
////						zipOutputStream.close();
////						LOGGER.log(Level.OFF, "xlsx-writer written {0}, {1}", new Object[]{container.getResourceKey(), xlsxConversionStats.toString()});
////						this.isExportedByXlsxWriter = true;
////						return byteArrayOutputStream.toByteArray();
////					}catch (Exception e) {
////						LOGGER.log(Level.OFF, String.join("xlsx-writer exception in writing ", container.getResourceKey(), " ", xlsxConversionStats.toString()), e);
////					}
////				}
////			}
//
//			//For CSV and TSV we are getting file content directly from workbook
//
//            if (isCsv || isTsv) {
//            	LOGGER.info("[CONVERSION : EXPORT] Using wrong Csv Tsv Conversion, Change This flow:");
//            	StackTraceElement[] elements = Thread.currentThread().getStackTrace();
//            	DocumentUtils.printStackTrace(elements);
//				//StringBuffer outputData = new StringBuffer();
//				byte[] outputByte = null;
//				Sheet sheetToExport=null;
//				//no sheetName given , export first sheet as csv
//				if (this.activeSheetName == null) {
//					this.activeSheetName =  workBook.getSheet(0).getName();
//				}
//				if (this.isSheetIndexBased) {
//					int _sheetIdx = Integer.parseInt(this.activeSheetName);
//					this.activeSheetName = workBook.getSheet(_sheetIdx-1).getName(); // OO Sheet index strats with 0 (zero)
//				}
//
//				sheetToExport=workBook.getSheet(activeSheetName);
//				if (this.rangeAddress == null) {
//
//					setRangeAddress(new RangeAddress(0, 0, sheetToExport.getUsedRowIndex(), sheetToExport.getUsedColumnIndex()));
//				}
//				if (isCsv) {
//					outputByte=EngineUtils1.getCSVData(sheetToExport, rangeAddress.startRow, rangeAddress.startCol, rangeAddress.endRow, rangeAddress.endCol, false);					
//				} else if (isTsv) {				
//					outputByte=EngineUtils1.getTSVData(sheetToExport, rangeAddress.startRow, rangeAddress.startCol, rangeAddress.endRow, rangeAddress.endCol,false);
//				}
//
//				//////////////////
//				return outputByte;
//			}
//
//                        if(tempODSFile == null)
//                        {
//                            LOGGER.log(Level.INFO, "EXPORT :::: tempODSFile not set. So invoking EngineUtils1.createTempODSFile()");
//                            tempODSFile = EngineUtils1.createTempODSFile(workBook, imageBook, false, null, (this.format == null || this.format.endsWith("ods")));//No I18N
//                            //tempODSFile = ImportExportUtil.getTempODSFile(container, sheetVersionNo);
//                        }
//                        filePath = tempODSFile.getAbsolutePath();
//			//InputStream stream = (InputStream) ImportExportUtil.openURL(tempODSFile);
//
//
//			//Need to change  , don't even create and pass a loconnection for export when not using Libo office
//			if(connectionObject == null)//if loConnectionObject in null than create a default connection
//
//			{
//				LOGGER.info("[CONVERSION : EXPORT] Using Libo Conversion:");
//				connectionObject = connectionpool.getRemoteDesktop();
//			}
//			ConversionServer cs = new ConversionServer();
//			boolean isRetry = false;
//			long t1 = System.currentTimeMillis();
//			try{
//				xComponent = ImportExportUtil.getXComponent(container, sheetVersionNo, tempODSFile, connectionObject);
//				if(ConversionServer.isDistributedModelEnabled) {
//					cs.handleConcurrentFailure(this.connectionObject.getsName(),"success");//No I18N
//				}
//			}catch (Exception xce) {
//				ZSStats.increment(ZSStats.EXPORT_RETRY);
//				LOGGER.info("[CONVERSION : EXPORT] Retry module"+ xce.getMessage());
//				String msg = xce.getMessage();
//				if(ConversionServer.isDistributedModelEnabled && ("Connection Error".equalsIgnoreCase(msg) || "Process Error".equalsIgnoreCase(msg) || "LIBO.Server.Connection.Failure".equalsIgnoreCase(msg))) {
//					isRetry = true;
//					connectionObject = connectionpool.getRemoteDesktop();
//					xComponent = ImportExportUtil.getXComponent(container, sheetVersionNo, tempODSFile, connectionObject);
//					ZSStats.increment(ZSStats.EXPORT_RETRY_SUCCESS);
//				} else {
//					ZSStats.increment(ZSStats.EXPORT_RETRY_FAILURE);
//					if(ConversionServer.isDistributedModelEnabled) {
//						cs.handleConcurrentFailure(this.connectionObject.getsName(),"failure");//No I18N
//					}
//					throw xce;
//				}
//
//			}
//
//			long t2 = System.currentTimeMillis();
//
//			String liboHost = connectionObject.getLiboHost();
//			if (xComponent != null) {
//
//				XSpreadsheetDocument xDocument = (XSpreadsheetDocument) UnoRuntime.queryInterface(XSpreadsheetDocument.class, xComponent);
//
//				if (xDocument != null) {
//					//CSV and TSV Export
//
//
//					//TODO : Have to handle the charts in the export
//					//					if(sheetVersionNo == null || "null".equals(sheetVersionNo) || "".equals(sheetVersionNo) || "undefined".equals(sheetVersionNo)){
//					//						sheetVersionNo = String.valueOf(DocumentUtils.getLastVersion(docId, docOwner));
//					//					}
//
//					//					String versionId = versionNo;
//					//					if (versionNo == null || "null".equals(versionNo) || "".equals(versionNo) || "undefined".equals(versionNo)) {
//					//						versionId = String.valueOf(DocumentUtils.getLastVersion(docId, docOwner));
//					//					}
//
//					// Find out possible filter name.
//					PropertyValue[] lProperties = new PropertyValue[3];
//					//XServiceInfo xInfo = (com.sun.star.lang.XServiceInfo) UnoRuntime.queryInterface(XServiceInfo.class, xDocument);
//					//if (xInfo.supportsService("com.sun.star.sheet.SpreadsheetDocument")) {
//
//					if (isPdf) {
//						sFilter = new String("calc_pdf_Export"); // No I18N    
//						lProperties = new PropertyValue[4];
//					} else {
//						
//						sFilter = ImportExportUtil.getFilter("." + this.format); //NO I18N
//					}
//					// Have the code to save macro, freezepane, chart and images here
//					//// charts
//					//Saving charts once before exporting to update the last chart change.
//					try{
//						Workbook workbook = container.getWorkbookForSave();
//						ChartUtils.saveCharts(workbook, docOwnerId);
//					}catch(Exception e){
//						LOGGER.log(Level.WARNING, "[CONVERSION:EXPORT] Couldn't save the last chart changes: {0}", e);
//					}
//					try{
//						ChartImportExport cIE  = new ChartImportExport(xDocument, docId, docOwner,( sheetVersionNo != null ? Double.parseDouble(sheetVersionNo) : 0.0));
//						cIE.exportCharts();
//					}catch(Exception e){
//						LOGGER.log(Level.WARNING, "[CONVERSION:EXPORT] Couldn't export Chart: {0}", e);
//					}
//
//					// Freeze Panes // Moved to Apatche POI methods 
//					//freezePanesDO = DocumentUtils.getFreezePanesDO(new Long(docId), container.getDocOwnerZUID());
//					//saveFreezePaneInfo(xComponent, freezePanesDO);
//					
//					////////Images
//					boolean isGetDetailsFromDB;
//					long documentId = Long.parseLong(container.getDocId());
//					isVersionExport = sheetVersionNo != null && !"null".equals(sheetVersionNo) && !"".equals(sheetVersionNo) && !"undefined".equals(sheetVersionNo); // No I18N
//					if(isVersionExport)
//					{
//						long resId = ZSStore.getVersionId(container.getDocOwner(), documentId, sheetVersionNo);
//						Store store = container.getStore(resId, FileName.VERSION);
//						isGetDetailsFromDB = !ImageUtils.isImageFileExistsInZippedFormatFile(container, store, resId);
//					}
//					else
//					{
//						if(container.isFileExist(documentId, FileName.DOCUMENT, ZSStore.FileExtn.ODS) || container.isFileExist(documentId, FileName.DOCUMENT, ZSStore.FileExtn.ZSHEET))
//						{
//							Store store = container.getStore(documentId, FileName.DOCUMENT);
//							isGetDetailsFromDB = !ImageUtils.isImageFileExistsInZippedFormatFile(container, store, documentId);
//						}
//						else{
//							DataObject dataObj = ZSStore.getFragmentDO(container.getDocOwner(), documentId);
//							isGetDetailsFromDB = (ZSStore.getFragmentId(dataObj, ZSStore.FILENAME_IMAGES_META_INFO) == -1) && (ZSStore.getFragmentId(dataObj, ZSStore.FILENAME_IMAGES) == -1);
//						}
//					}
//					if (updateImages) {
//						//Need a image version file exist check
//						if(isGetDetailsFromDB)
//						{
//							if (isVersionExport) { //Need to read the image version DB.
//								imagesDO = ImageUtils.getOldVersionImagesDO(new Long(docId), null, docOwner, sheetVersionNo);
//								ImageUtils.insertVersionImagesFromOLDDB(container, imagesDO, xComponent, liboHost);
//							}
//							else {
//								imagesDO = ImageUtils.getOldImagesDO(new Long(docId), docOwner);
//								ImageUtils.insertImageFromOLDDB(container, imagesDO, xComponent, liboHost); 
//							}
//						}
//						else
//						{
//							imagesDO = ImageUtils.getImagesDO(new Long(docId), docOwner);
//							Map<String, List<ImageUtils.ExportImage>> imageToBeExported = ImageUtils.convertImageBookForExport(container, workBook, imageBook, imagesDO);
//							if (isVersionExport) { //Need to read the image version file.
//								ImageUtils.insertVersionImage(container, imageToBeExported, zfsngVersionNum, imagesDO, xComponent, liboHost);
//							}
//							else {
//								ImageUtils.insertImage(container, imageToBeExported, imagesDO, xComponent, liboHost);
//							}
//						}
//					}
//					/////////////////////////////////////
//
//
//					ooos = new OOOutputStream();
//					lProperties[0] = new com.sun.star.beans.PropertyValue();
//					lProperties[0].Name = "FilterName";                                                                                // No I18N
//					lProperties[0].Value = sFilter;
//					lProperties[1] = new com.sun.star.beans.PropertyValue();
//					lProperties[1].Name = "OutputStream";                                                                                // No I18N    
//					lProperties[1].Value = ooos;
//					lProperties[2] = new com.sun.star.beans.PropertyValue();
//					lProperties[2].Name = "DocumentTitle";                                                                                // No I18N
//					lProperties[2].Value = docName;
//					if (isPdf) {
//						lProperties[3] = new com.sun.star.beans.PropertyValue();
//						lProperties[3].Name = "CompressionMode";                                                                        // No I18N
//						lProperties[3].Value = "1";
//					}
//					//check this code well
//					// TODO: If macro doesn't work, uncomment the line below.
//					//OOUtils.saveMacroCode(xDocument);
//					com.sun.star.frame.XStorable xStore = (com.sun.star.frame.XStorable) UnoRuntime.queryInterface(
//							com.sun.star.frame.XStorable.class, xDocument);
//					try {
//						xStore.storeToURL("private:stream", lProperties);                                                                // No I18N
//					} catch (IOException ie) {
//						LOGGER.log(Level.WARNING, "[CONVERSION:EXPORT]", ie);
//						throw new ServletException("Unable to Export the Document");
//					}
//					long t3 = System.currentTimeMillis();
//					if(isRetry) {
//						LOGGER.log(Level.INFO, "[CONVERSION : EXPORT],Status:::Success after retry");
//					}
//					else {
//						LOGGER.log(Level.INFO, "[CONVERSION : EXPORT],Status:::Success Without retry");
//					}
//					LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] Success Format:{0}:{1} Exporter {2} Conversion {3}", new Object[]{format, container.getResourceId(), t3-t1, t2-t1});
//					return ooos.toByteArray();
//				}
//			}
//			// }  // close of if (xInfo.supportsService("com.sun.star.sheet.SpreadsheetDocument"))  
//		} catch (Exception e) {
//			ZSStats.incrementByCustomValue(ZSStats.EXPORTERROR);
//			LOGGER.log(Level.WARNING, "[CONVERSION:EXPORT]", e);
//			throw e;
//		} finally {
//
//			lsc.cancel();
//			if (ooos != null) {
//				ooos.close();
//			}
//			if (filePath != null) {
//				File f = new File(filePath);
//				if (f.exists()) {
//					f.delete();
//				}
//			}
//			ImportExportUtil.closeComponent(xComponent);
//		}
//		return null;
//	}

	public JSONObjectWrapper getDocumentAsJson() throws Exception
	{
		ZSStats.increment(ZSStats.EXPORT_FORMAT_JSON);
		JSONObjectWrapper docJson = null;
		Workbook workbook = container.getWorkbook(null);

		if(this.activeSheetName == null)
		{
			docJson = new JSONObjectWrapper(DataAPIResponse.getWorkbookAsJson(workbook));
		}
		else
		{
			Sheet sheet = workbook.getSheet(this.activeSheetName);
			if(sheet == null)
			{
				throw new Exception(ErrorCode.ERROR_SHEET_MODIFIED_OR_DELETED);
			}
			docJson = new JSONObjectWrapper();
			JSONArrayWrapper tableJson = new JSONArrayWrapper((this.rangeAddress == null) ? DataAPIResponse.getTableJson(sheet, 0, 0, sheet.getUsedRowIndex(), sheet.getUsedColumnIndex()) : DataAPIResponse.getTableJson(sheet, rangeAddress.startRow, rangeAddress.startCol, rangeAddress.endRow, rangeAddress.endCol));
			docJson.put(sheet.getName(), tableJson);
		}
		return docJson;
	}
	
	private byte[] exportFreezePane(byte[] bytes){
		LifeSpanController lsc =  new LifeSpanController(Thread.currentThread(), "POI Component Thread", null, null, null); //NO I18N
		lsc.control();
		String docId = this.container.getDocId();
		String docOwner = this.container.getDocOwner();
		try{
			Workbook workbook = container.getWorkbook(this.zfsngVersionNum);
			JSONArrayWrapper freezePanes = DocumentUtils.getFreezePaneInfo(workbook);
			boolean isFormatSupported = EngineConstants.IMPORT_DECRYPTION_SUPPORTED_FORMATS.contains(format);
			if(freezePanes!=null && freezePanes.length()>0 && isFormatSupported) {
				LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] Document has FreezePane so writing them by POI:");
				byte[] content = ImportExportUtil.writeFreezePane(bytes, format, freezePanes);
				return content;
			}
			//LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] Freeze Pane:"+hasFreezePane);
		}
		catch (Exception e) {
			LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] Freeze Pane Error:",e);
		}
		lsc.cancel();
		LOGGER.log(Level.INFO, "[CONVERSION:EXPORT] FreezePane Written:");
		return bytes;
	}


	//	public byte[] getDocumentBytes(HttpServletRequest request) throws Exception {
	//
	//		//TODO: Authorization check have to add
	//		//TODO: Increase document public count
	//		//Get the param
	//		XComponent xComponent = null;
	//		OOOutputStream ooos = null;
	//		String sFilter = null;
	//		DataObject imagesDO = null;
	//		DataObject freezePanesDO = null;
	//		String dir = AppResources.getProperty("server.dir");
	//		String filePath = null;
	//		boolean isVersionExport = false;
	//
	//		try {
	//			String versionNo = this.versionLabel;
	//			WorkbookContainer container = this.container;
	//			File tempODSFile = this.tempFile;
	//			if (container == null) {
	//				throw new ServletException("Problem while exporting the document");
	//			} else if (versionNo == null && this.allowSave) {
	//				//Saving the document before doing export.
	//				if (container.getWorkbookForSave() == null) {
	//					long size = RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + container.getResourceId());
	//					// Incase if the temp actions are still not migrated to Redis list
	//					if (size < 1) {
	//						Long tempActionsResId = container.getFragmentId(FileName.ACTIONSTEMP, false, null);
	//						if (tempActionsResId > 0) {
	//							size = EngineUtils1.loadActionsList(container, tempActionsResId, FileName.ACTIONSTEMP).size(); 
	//						}
	//					}
	//					if (size > 0) {
	//						logger.log(Level.INFO, "Exort Document Save. DOCID: {0} - The Workbook object is null so creating the workbook.", container.getDocId());
	//						container.getWorkbook(null);
	//					}
	//				}
	//				this.container.save(null, null, true);
	//			}
	//			String docId = this.container.getDocId();
	//			String docName = this.container.getDocName();
	//
	//			if (docName == null || "".equals(docName)) {
	//				try {
	//					LocaleMsg localeMsg = (LocaleMsg) request.getSession().getAttribute("LocaleMsg");
	//					I18nMessage i18nMsg = (I18nMessage) localeMsg;
	//					docName = i18nMsg.getMsg("Untitled");
	//				} catch (Exception le) {
	//					docName = "Untitled_Spreadsheet";// No I18N
	//				}
	//			}
	//
	//			String docOwner = this.container.getDocOwner();
	//			String docOwnerId = this.container.getDocOwnerId();
	//			boolean isCsv = ("csv".equalsIgnoreCase(this.format));    // No I18N
	//			boolean isTsv = ("tsv".equalsIgnoreCase(this.format));    // No I18N
	//			boolean isXlsx = ("xlsx".equalsIgnoreCase(this.format));    // No I18N
	//			boolean isXls = ("xls".equalsIgnoreCase(this.format));    // No I18N
	//			boolean isOds = ("ods".equalsIgnoreCase(this.format));    // No I18N
	//			boolean isPdf = ("pdf".equalsIgnoreCase(this.format));    // No I18N
	//			boolean isHtml = ("html".equalsIgnoreCase(this.format));    // No I18N
	//			boolean isZip = ("zip".equalsIgnoreCase(this.format));    // No I18N
	//			boolean updateImages = (isXlsx || isXls || isOds || isPdf);
	//			if (this.allowResponseSettings) {
	//				this.response.reset();    //IE with https (secured) connection export issue fix block starts
	//				//logger.info("Mime Type:: "  + this.getMimeType());
	//				this.response.setContentType(this.getMimeType());
	//				this.response.setCharacterEncoding("UTF-8"); // No I18N
	//				// This is to prevent browser - IE8 from showing "open" as an download option for security purposes - Added by Srini
	//				if (isHtml || isPdf) {
	//					response.setHeader("X-Download-Options", "noopen");    //No I18N
	//				}
	//				String fileName = ClientUtils.encodeFileName((((isCsv || isTsv) && this.activeSheetName != null) ? docName + '_' + this.activeSheetName : docName) + "." + this.format);
	//				//logger.info("FileName : "  +fileName);
	//				if (this.userAgent != null && this.userAgent.indexOf("Firefox") == -1) {
	//					this.response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\""); // No I18N
	//				} else {
	//					this.response.setHeader("Content-Disposition", "attachment;filename*=UTF-8\'\'" + fileName); // No I18N
	//				}
	//			}
	//			if (isZip) {
	//				byte[] content = null;
	//				HtmlConstructor hc = new HtmlConstructor(container, null);
	//				Workbook workbook = container.getWorkbook(null);
	//				String[] sheetNames = workbook.getSheetNames(false);
	//				for (int i = 0; i < sheetNames.length; i++) {
	//					hc.setRange(null);
	//					hc.setSheetName(sheetNames[i]);
	//					hc.parseSheetObjects(null);
	//					hc.parseHTML(request);
	//				}
	//				try {
	//					content = hc.getDocumentBytes(request);
	//				} catch (Exception e) {
	//					logger.log(Level.WARNING, "[GEN HTML] Unable to get Content of the Spreadsheet... ", e);
	//				}
	//				return content;
	//			}
	//			LOConnectionObject loConnectionObject = OOUtils.getXComponent(request, container, versionNo, this.zfsngVersionId, tempODSFile);
	//			xComponent = loConnectionObject.getXComponent();
	//			String liboHost = loConnectionObject.getLiboHost();
	//			if (xComponent != null) {
	//				XSpreadsheetDocument xDocument = (XSpreadsheetDocument) UnoRuntime.queryInterface(XSpreadsheetDocument.class, xComponent);
	//				if (xDocument != null) {
	//					//CSV and TSV Export
	//					if (isCsv || isTsv) {
	//						StringBuffer outputData = new StringBuffer();
	//						InputStream stream = null;
	//						if (tempODSFile != null) {
	//							stream = (InputStream) OOUtils.openURL(tempODSFile);
	//						} else {
	//							stream = OOUtils.getFileInputStream(container, versionNo);
	//						}
	//						if (this.activeSheetName == null) {
	//							setActiveSheet(OOUtils.getActiveSheetInfo(xDocument));
	//						}
	//						if (this.isSheetIndexBased) {
	//							int _sheetIdx = Integer.parseInt(this.activeSheetName);
	//							setActiveSheet(xDocument.getSheets().getElementNames()[(_sheetIdx - 1)]); // OO Sheet index strats with 0 (zero) 
	//						}
	//						if (this.rangeAddress == null) {
	//							Object sheet = xDocument.getSheets().getByName(this.activeSheetName);
	//							XSpreadsheet xSpreadsheet = (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class, sheet);
	//							ArrayList<?> arrList = OOUtils.getSheetDim(xSpreadsheet);
	//							int usedRowIndex = (Integer) arrList.get(0);
	//							int usedColIndex = (Integer) arrList.get(1);
	//							setRangeAddress(new RangeAddress(0, 0, usedRowIndex, usedColIndex));
	//						}
	//						if (isCsv) {
	//							CsvRangeTransformer transFormer = new CsvRangeTransformer(this.activeSheetName, this.rangeAddress);
	//							outputData = transFormer.transfromODS(stream);
	//						} else if (isTsv) {
	//							TsvRangeTransformer transFormer = new TsvRangeTransformer(this.activeSheetName, this.rangeAddress);
	//							outputData = transFormer.transfromODS(stream);
	//						}
	//
	//						//////////////////
	//						return outputData.toString().getBytes();
	//					}
	//
	//					//TODO : Have to handle the charts in the export
	//					String versionId = versionNo;
	//					if (versionNo == null || "null".equals(versionNo) || "".equals(versionNo) || "undefined".equals(versionNo)) {
	//						versionId = String.valueOf(DocumentUtils.getLastVersion(docId, docOwner));
	//					}
	//
	//					// Find out possible filter name.
	//					PropertyValue[] lProperties = new PropertyValue[3];
	//					//XServiceInfo xInfo = (com.sun.star.lang.XServiceInfo) UnoRuntime.queryInterface(XServiceInfo.class, xDocument);
	//					//if (xInfo.supportsService("com.sun.star.sheet.SpreadsheetDocument")) {
	//
	//					if (isPdf) {
	//						sFilter = new String("calc_pdf_Export"); // No I18N    
	//						lProperties = new PropertyValue[4];
	//					} else {
	//						sFilter = OOUtils.getFilter("." + this.format); //NO I18N
	//					}
	//					// Have the code to save macro, freezepane and images here
	//					//// charts
	//					GraphUtils.updateVersionChartDetailsInObject(xDocument, docId, versionId, docOwner);
	//					//// Freeze Panes
	//					////TODO: Freezepanes not exported
	//					freezePanesDO = DocumentUtils.getFreezePanesDO(new Long(docId), docOwner);
	//					saveFreezePaneInfo(xComponent, freezePanesDO);
	//
	//					////////Images
	//					if (updateImages) {
	//						if (versionNo != null && !"null".equals(versionNo) && !"".equals(versionNo) && !"undefined".equals(versionNo)) {
	//							imagesDO = ImageUtils.getVersionImagesDO(new Long(docId), null, docOwner, versionNo);
	//							isVersionExport = true;
	//						} else {
	//							imagesDO = ImageUtils.getImagesDO(new Long(docId), docOwner);
	//						}
	//					}
	//
	//					if (isVersionExport) {
	//						ImageUtils.insertVersionImages(container, imagesDO, xComponent, liboHost);
	//					} else {
	//						ImageUtils.insertImage(container, imagesDO, xComponent, liboHost);
	//					}
	//					/////////////////////////////////////
	//
	//
	//					ooos = new OOOutputStream();
	//					lProperties[0] = new com.sun.star.beans.PropertyValue();
	//					lProperties[0].Name = "FilterName";                                                                                // No I18N
	//					lProperties[0].Value = sFilter;
	//					lProperties[1] = new com.sun.star.beans.PropertyValue();
	//					lProperties[1].Name = "OutputStream";                                                                                // No I18N    
	//					lProperties[1].Value = ooos;
	//					lProperties[2] = new com.sun.star.beans.PropertyValue();
	//					lProperties[2].Name = "DocumentTitle";                                                                                // No I18N
	//					lProperties[2].Value = docName;
	//					if (isPdf) {
	//						lProperties[3] = new com.sun.star.beans.PropertyValue();
	//						lProperties[3].Name = "CompressionMode";                                                                        // No I18N
	//						lProperties[3].Value = "1";
	//					}
	//					//check this code well
	//					// TODO: If macro doesn't work, lets uncomment the below line
	//					//OOUtils.saveMacroCode(xDocument);
	//					com.sun.star.frame.XStorable xStore = (com.sun.star.frame.XStorable) UnoRuntime.queryInterface(
	//							com.sun.star.frame.XStorable.class, xDocument);
	//					try {
	//						xStore.storeToURL("private:stream", lProperties);                                                                // No I18N
	//					} catch (IOException ie) {
	//						logger.log(Level.WARNING, null, ie);
	//						throw new ServletException("Unable to Export the Document");
	//					}
	//					return ooos.toByteArray();
	//				}
	//			}
	//			// }  // close of if (xInfo.supportsService("com.sun.star.sheet.SpreadsheetDocument"))  
	//		} catch (Exception e) {
	//			ZSStats.incrementByCustomValue(ZSStats.EXPORTERROR);
	//			logger.log(Level.WARNING, null, e);
	//		} finally {
	//			if (ooos != null) {
	//				ooos.close();
	//			}
	//			if (filePath != null) {
	//				File f = new File(filePath);
	//				if (f.exists()) {
	//					f.delete();
	//				}
	//			}
	//			OOUtils.closeComponent(xComponent);
	//		}
	//		return null;
	//	}

	//        OLD ASPOSE METHOD
	//        public byte[] getDocumentBytes(HttpServletRequest request) throws Exception {
	//
	//            //TODO: Authorization check have to add
	//            //TODO: Increase document public count
	//            //Get the param
	//            XComponent xComponent = null;
	//            OOOutputStream ooos = null;
	//            String sFilter = null;
	//            DataObject imagesDO = null;
	//            DataObject freezePanesDO = null;
	//            String dir = AppResources.getProperty("server.dir");
	//            String filePath = null;
	//            boolean isVersionExport = false;
	//            
	//            try {
	//                    String versionNo = this.versionLabel;
	//                    WorkbookContainer container = this.container;
	//                    File tempODSFile = this.tempFile;
	//                    if (container == null) {
	//                            throw new ServletException("Problem while exporting the document");
	//                    } else if (versionNo == null && this.allowSave) {
	//                            // Document save call.
	//                            //Saving the document before doing export.
	//                            if (container.getWorkbookForSave() == null) {
	//
	//                                    long size = RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + container.getResourceId());
	//
	//                                    // Incase if the temp actions are still not migrated to Redis list
	//                                    if (size < 1) {
	//                                            Long tempActionsResId = container.getFragmentId(FileName.ACTIONSTEMP, false, null);
	//                                            if (tempActionsResId > 0) {
	//                                                    size = EngineUtils1.loadActionsList(container, tempActionsResId, FileName.ACTIONSTEMP).size(); 
	//
	//                                            }
	//                                    }
	//                                    if (size > 0) {
	//                                            logger.log(Level.INFO, "Exort Document Save. DOCID: {0} - The Workbook object is null so creating the workbook.", container.getDocId());
	//                                            container.getWorkbook(null);
	//                                    }
	//                            }
	//                            this.container.save(null, null);
	//                    }
	//                    String docId = this.container.getDocId();
	//                    String docName = this.container.getDocName();
	//
	//                    if (docName == null || "".equals(docName)) {
	//                            try {
	//                                    LocaleMsg localeMsg = (LocaleMsg) request.getSession().getAttribute("LocaleMsg");
	//                                    I18nMessage i18nMsg = (I18nMessage) localeMsg;
	//                                    docName = i18nMsg.getMsg("Untitled");
	//                            } catch (Exception le) {
	//                                    docName = "Untitled_Spreadsheet";// No I18N
	//                    }
	//
	//                    String docOwner = this.container.getDocOwner();
	//                    String docOwnerId = this.container.getDocOwnerId();
	//                    boolean isCsv = ("csv".equalsIgnoreCase(this.format));    // No I18N
	//                    boolean isTsv = ("tsv".equalsIgnoreCase(this.format));    // No I18N
	//                    boolean isXlsx = ("xlsx".equalsIgnoreCase(this.format));    // No I18N
	//                    boolean isXls = ("xls".equalsIgnoreCase(this.format));    // No I18N
	//                    boolean isOds = ("ods".equalsIgnoreCase(this.format));    // No I18N
	//                    boolean isPdf = ("pdf".equalsIgnoreCase(this.format));    // No I18N
	//                    boolean isHtml = ("html".equalsIgnoreCase(this.format));    // No I18N
	//                    boolean isZip = ("zip".equalsIgnoreCase(this.format));    // No I18N
	//                    boolean updateImages = (isXlsx || isXls || isOds || isPdf);
	//                    if (this.allowResponseSettings) {
	//                            this.response.reset();    //IE with https (secured) connection export issue fix block starts
	//                            //logger.info("Mime Type:: "  + this.getMimeType());
	//                            this.response.setContentType(this.getMimeType());
	//                            this.response.setCharacterEncoding("UTF-8"); // No I18N
	//                            // This is to prevent browser - IE8 from showing "open" as an download option for security purposes - Added by Srini
	//                            if (isHtml || isPdf) {
	//                                    response.setHeader("X-Download-Options", "noopen");    //No I18N
	//                            }
	//                            String fileName = ClientUtils.encodeFileName((((isCsv || isTsv) && this.activeSheetName != null) ? docName + '_' + this.activeSheetName : docName) + "." + this.format);
	//                            //logger.info("FileName : "  +fileName);
	//                            if (this.userAgent != null && this.userAgent.indexOf("Firefox") == -1) {
	//                                    this.response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\""); // No I18N
	//                            } else {
	//                                    this.response.setHeader("Content-Disposition", "attachment;filename*=UTF-8\'\'" + fileName); // No I18N
	//                            }
	//                    }
	//                    if (isZip) {
	//                            byte[] content = null;
	//                            HtmlConstructor hc = new HtmlConstructor(container, null);
	//                            Workbook workbook = container.getWorkbook(null);
	//                            String[] sheetNames = workbook.getSheetNames(false);
	//                            for (int i = 0; i < sheetNames.length; i++) {
	//                                    hc.setRange(null);
	//                                    hc.setSheetName(sheetNames[i]);
	//                                    hc.parseSheetObjects(null);
	//                                    hc.parseHTML(request);
	//                            }
	//                            try {
	//                                    content = hc.getDocumentBytes(request);
	//                            } catch (Exception e) {
	//                                    logger.log(Level.WARNING, "[GEN HTML] Unable to get Content of the Spreadsheet... ", e);
	//                            }
	//                            return content;
	//                    }
	//                    xComponent = OOUtils.getXComponent(request, container, versionNo, this.zfsngVersionId, tempODSFile);
	//                    if (xComponent != null) {
	//                            XSpreadsheetDocument xDocument = (XSpreadsheetDocument) UnoRuntime.queryInterface(XSpreadsheetDocument.class, xComponent);
	//                            if (xDocument != null) {
	//                                    //CSV and TSV Export
	//                                    if (isCsv || isTsv) {
	//                                            StringBuffer outputData = new StringBuffer();
	//                                            InputStream stream = null;
	//                                            if (tempODSFile != null) {
	//                                                    stream = (InputStream) OOUtils.openURL(tempODSFile);
	//                                            } else {
	//                                                    stream = OOUtils.getFileInputStream(container, versionNo);
	//                                            }
	//                                            if (this.activeSheetName == null) {
	//                                                    setActiveSheet(OOUtils.getActiveSheetInfo(xDocument));
	//                                            }
	//                                            if (this.isSheetIndexBased) {
	//                                                    int _sheetIdx = Integer.parseInt(this.activeSheetName);
	//                                                    setActiveSheet(xDocument.getSheets().getElementNames()[(_sheetIdx - 1)]); // OO Sheet index strats with 0 (zero) 
	//                                            }
	//                                            if (this.rangeAddress == null) {
	//                                                    Object sheet = xDocument.getSheets().getByName(this.activeSheetName);
	//                                                    XSpreadsheet xSpreadsheet = (XSpreadsheet) UnoRuntime.queryInterface(XSpreadsheet.class, sheet);
	//                                                    ArrayList<?> arrList = OOUtils.getSheetDim(xSpreadsheet);
	//                                                    int usedRowIndex = (Integer) arrList.get(0);
	//                                                    int usedColIndex = (Integer) arrList.get(1);
	//                                                    setRangeAddress(new RangeAddress(0, 0, usedRowIndex, usedColIndex));
	//                                            }
	//                                            if (isCsv) {
	//                                                    CsvRangeTransformer transFormer = new CsvRangeTransformer(this.activeSheetName, this.rangeAddress);
	//                                                    outputData = transFormer.transfromODS(stream);
	//                                            } else if (isTsv) {
	//                                                    TsvRangeTransformer transFormer = new TsvRangeTransformer(this.activeSheetName, this.rangeAddress);
	//                                                    outputData = transFormer.transfromODS(stream);
	//                                            }
	//                                            return outputData.toString().getBytes();
	//                                    }
	//                                    //TODO : Have to handle the charts in the export
	//                                    String versionId = versionNo;
	//                                    if (versionNo == null || "null".equals(versionNo) || "".equals(versionNo) || "undefined".equals(versionNo)) {
	//                                            versionId = String.valueOf(DocumentUtils.getLastVersion(docId, docOwner));
	//                                    }
	//                                    GraphUtils.updateVersionChartDetailsInObject(xDocument, docId, versionId, docOwner);
	//                                    freezePanesDO = DocumentUtils.getFreezePanesDO(new Long(docId), docOwner);
	//                                    saveFreezePaneInfo(xComponent, freezePanesDO);
	//                                    // Find out possible filter name.
	//                                    PropertyValue[] lProperties = new PropertyValue[3];
	//                                    XServiceInfo xInfo = (com.sun.star.lang.XServiceInfo) UnoRuntime.queryInterface(XServiceInfo.class, xDocument);
	//                                    if (xInfo.supportsService("com.sun.star.sheet.SpreadsheetDocument")) {
	//                                            try {
	//                                                    if (updateImages) {
	//                                                            if (versionNo != null && !"null".equals(versionNo) && !"".equals(versionNo) && !"undefined".equals(versionNo)) {
	//                                                                    imagesDO = ImageUtils.getVersionImagesDO(new Long(docId), null, docOwner, versionNo);
	//                                                                    isVersionExport = true;
	//                                                            } else {
	//                                                                    imagesDO = ImageUtils.getImagesDO(new Long(docId), docOwner);
	//                                                            }
	//                                                    }
	//                                            } catch (Exception e) {
	//                                                    logger.log(Level.WARNING, null, e);
	//                                            }
	//                                            boolean usingAspose = (isXlsx || (imagesDO != null && !imagesDO.isEmpty()) || (freezePanesDO != null && !freezePanesDO.isEmpty()));
	//                                            if (isXlsx) {
	//                                                    sFilter = "MS Excel 97"; //NO I18N
	//                                            } else if (isPdf) {
	//                                                    if (usingAspose) {
	//                                                            sFilter = new String("calc8"); // No I18N    
	//                                                    } else {
	//                                                            sFilter = new String("calc_pdf_Export"); // No I18N    
	//                                                            lProperties = new PropertyValue[4];
	//                                                    }
	//                                            } else {
	//                                                    sFilter = OOUtils.getFilter("." + this.format); //NO I18N
	//                                            }
	//                                            ooos = new OOOutputStream();
	//                                            lProperties[0] = new com.sun.star.beans.PropertyValue();
	//                                            lProperties[0].Name = "FilterName";                                                                                // No I18N
	//                                            lProperties[0].Value = sFilter;
	//                                            lProperties[1] = new com.sun.star.beans.PropertyValue();
	//                                            lProperties[1].Name = "OutputStream";                                                                                // No I18N    
	//                                            lProperties[1].Value = ooos;
	//                                            lProperties[2] = new com.sun.star.beans.PropertyValue();
	//                                            lProperties[2].Name = "DocumentTitle";                                                                                // No I18N
	//                                            lProperties[2].Value = docName;
	//                                            if (isPdf && !usingAspose) {
	//                                                    lProperties[3] = new com.sun.star.beans.PropertyValue();
	//                                                    lProperties[3].Name = "CompressionMode";                                                                        // No I18N
	//                                                    lProperties[3].Value = "1";
	//                                            }
	//                                            OOUtils.saveMacroCode(xDocument);
	//                                            com.sun.star.frame.XStorable xStore = (com.sun.star.frame.XStorable) UnoRuntime.queryInterface(
	//                                                    com.sun.star.frame.XStorable.class, xDocument);
	//                                            try {
	//                                                    xStore.storeToURL("private:stream", lProperties);                                                                // No I18N
	//                                            } catch (IOException ie) {
	//                                                    logger.log(Level.WARNING, null, ie);
	//                                                    throw new ServletException("Unable to Export the Document");
	//                                            }
	//                                            if (usingAspose) {
	//                                                    InputStream fis = null;
	//                                                    BufferedInputStream bis = null;
	//                                                    ByteArrayOutputStream bos = null;
	//                                                    try {
	//                                                            docId = String.valueOf(System.currentTimeMillis()) + "_" + userName; //NO I18N
	//                                                            //Loading Aspose workbook
	//                                                            com.aspose.cells.Workbook wb = new com.aspose.cells.Workbook();
	//                                                            wb.open(new ByteArrayInputStream(ooos.toByteArray()));
	//                                                            try {    //Update images in Object.
	//                                                                    if (imagesDO != null && !imagesDO.isEmpty()) {
	//                                                                            if (isVersionExport) {
	//                                                                                    AsposeUtils.updateVersionImages(container, wb, imagesDO);
	//                                                                            } else {
	//                                                                                    AsposeUtils.updateImages(container, wb, imagesDO);
	//                                                                            }
	//                                                                    }
	//                                                                    //Update freezepanes in Object.
	//                                                                    if (freezePanesDO != null && !freezePanesDO.isEmpty()) {
	//                                                                            AsposeUtils.updateFreezepanes(xComponent, wb, freezePanesDO, Long.valueOf(container.getDocId()), container.getDocOwner());
	//                                                                    }
	//                                                            } catch (Exception e) {
	//                                                                    logger.log(Level.WARNING, null, e);
	//                                                            }
	//                                                            filePath = dir + File.separator + "webapps" + File.separator + "websheet" + File.separator + "exportdocs";                        //No internationalization
	//                                                            File f = new File(filePath);
	//                                                            if (!f.exists()) {
	//                                                                    logger.log(Level.INFO, "DIR Creation for XLSX format export in sheet dir with name exportdocs : {0}", f.mkdir());
	//                                                            }
	//                                                            filePath = filePath + File.separator + docId + "." + this.format;
	//                                                            int fileFormatType = AsposeUtils.getAsposeFileFormatType("." + this.format);
	//                                                            wb.save(filePath, fileFormatType);
	//                                                            fis = new FileInputStream(filePath);
	//                                                            bis = new BufferedInputStream(fis);
	//                                                            bos = new ByteArrayOutputStream();
	//                                                            int BUF_LEN = 1024;
	//                                                            byte[] bufr = new byte[BUF_LEN];
	//                                                            int c = 0;
	//                                                            while ((c = bis.read(bufr, 0, BUF_LEN)) != -1) {
	//                                                                    bos.write(bufr, 0, c);
	//                                                            }
	//                                                            return bos.toByteArray();
	//                                                    } catch (Exception e) {
	//                                                            if (filePath != null) {
	//                                                                    File f = new File(filePath);
	//                                                                    if (f.exists()) {
	//                                                                            f.delete();
	//                                                                    }
	//                                                            }
	//                                                            if (isXlsx) {
	//                                                                    com.aspose.cells.Workbook wb = new com.aspose.cells.Workbook();
	//                                                                    wb.open(new ByteArrayInputStream(ooos.toByteArray()), FileFormatType.EXCEL2003);
	//                                                                    filePath = dir + File.separator + "webapps" + File.separator + "websheet" + File.separator + "exportdocs";//No internationalization
	//                                                                    File f = new File(filePath);
	//                                                                    if (!f.exists()) {
	//                                                                            logger.log(Level.INFO, "DIR Creation for XLSX format export in sheet dir with name exportdocs : {0}", f.mkdir());
	//                                                                    }
	//                                                                    docId = String.valueOf(System.currentTimeMillis()) + "_" + userName; //NO I18N
	//                                                                    filePath = filePath + File.separator + docId + "." + this.format;
	//                                                                    wb.save(filePath, FileFormatType.EXCEL2007);
	//                                                                    fis = new FileInputStream(filePath);
	//                                                                    bis = new BufferedInputStream(fis);
	//                                                                    bos = new ByteArrayOutputStream();
	//                                                                    int BUF_LEN = 1024;
	//                                                                    byte[] bufr = new byte[BUF_LEN];
	//                                                                    int c = 0;
	//                                                                    while ((c = bis.read(bufr, 0, BUF_LEN)) != -1) {
	//                                                                            bos.write(bufr, 0, c);
	//                                                                    }
	//                                                                    return bos.toByteArray();
	//                                                            } else {
	//                                                                    return ooos.toByteArray();
	//                                                            }
	//                                                    } finally {
	//                                                            if (fis != null) {
	//                                                                    fis.close();
	//                                                            }
	//                                                            if (bis != null) {
	//                                                                    bis.close();
	//                                                            }
	//                                                            if (bos != null) {
	//                                                                    bos.close();
	//                                                            }
	//                                                            if (ooos != null) {
	//                                                                    ooos.close();
	//                                                            }
	//                                                            if (filePath != null) {
	//                                                                    File f = new File(filePath);
	//                                                                    if (f.exists()) {
	//                                                                            f.delete();
	//                                                                    }
	//                                                            }
	//                                                    }
	//                                            } else {
	//                                                    return ooos.toByteArray();
	//                                            }
	//                                    }
	//                            }
	//                    }
	//            } catch (Exception e) {
	//            		ZSStats.incrementByCustomValue(ZSStats.EXPORTERROR);
	//                    logger.log(Level.WARNING, null, e);
	//            } finally {
	//                    if (ooos != null) {
	//                            ooos.close();
	//                    }
	//                    if (filePath != null) {
	//                            File f = new File(filePath);
	//                            if (f.exists()) {
	//                                    f.delete();
	//                            }
	//                    }
	//                    OOUtils.closeComponent(xComponent);
	//            }
	//            return null;
	//    }
	private void saveFreezePaneInfo(XComponent xComp, DataObject freezePanesDO) {
		try {
			if (xComp != null) {
				String value = "";
				if (freezePanesDO != null && !freezePanesDO.isEmpty()) {
					Iterator docSheetsItr = freezePanesDO.getRows("DocumentSheets");
					while (docSheetsItr.hasNext()) {
						Row docSheetRow = (Row) docSheetsItr.next();
						Long docSheetId = (Long) docSheetRow.get("DOCUMENTSHEET_ID");
						String sheetName = (String) docSheetRow.get("SHEET_NAME");
						Row freezeRow = freezePanesDO.getRow("FreezePanes", new Criteria(new Column("FreezePanes", "DOCUMENTSHEET_ID"), docSheetId, QueryConstants.EQUAL));        // No I18N
						int freezedRow = Integer.parseInt(freezeRow.get("FREEZED_ROW").toString());
						int freezedCol = Integer.parseInt(freezeRow.get("FREEZED_COLUMN").toString());
						value += sheetName + "@" + freezedRow + "@" + freezedCol + "#";
					}
					ImportExportUtil.removeCustomProperty(xComp, "FreezeInfo");                                                //No I18N
					if (!"".equals(value)) {
						value = value.substring(0, value.length() - 1);
						ImportExportUtil.addCustomProperty("FreezeInfo", value, xComp);
					}
				}
			}
		} catch (Exception e) {
			LOGGER.log(Level.INFO, "Error while setting the freeze pane info in XComponent");
		}
	}

	public void setCloneRequired(boolean cloneRequired) {
		this.isCloneRequired = cloneRequired;
	}

	//	private byte[] validateEmptyByte(byte[] content) throws Exception {
	//		String c = new String(content);
	//		if (c.trim().length() == 0 || c == null || c.equalsIgnoreCase("null")) {
	//			throw new Exception("EMPTY BYTE"); //NO I18N
	//		}
	//		return content;
	//	}
}
