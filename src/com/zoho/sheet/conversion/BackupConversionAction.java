///* $Id$ */

package com.zoho.sheet.conversion;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.WorkbookContainer.ContainerEntity;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.api.APIError;
import com.zoho.zfsng.client.ZohoFS;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *
 */
public class BackupConversionAction extends HttpServlet {

	public static final Logger LOGGER = Logger.getLogger(BackupConversionAction.class.getName());

	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		try {
			process(req, resp);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			LOGGER.info(" error" + e);
		}
	}

	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		try {
			process(req, resp);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			LOGGER.info(" error" + e);
		}
	}

	protected void doPut(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		APIError.getInst().UnSupportedOpErr(null, resp, "Http PUT method is not supported");//No I18N
	}

	protected void doDelete(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		APIError.getInst().UnSupportedOpErr(null, resp, "Http DELETE method is not supported");//No I18N
	}

	public void process(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		String rid = null;
		String docsSpaceId = null;
		JSONObjectWrapper obj = new JSONObjectWrapper();
        String sheetSpaceId = null;
		String zfsngVersionId = null;
		try {
		String[] urlSegment = request.getRequestURI().split("/");
		rid = urlSegment[urlSegment.length - 1];	
		try {
			sheetSpaceId = ZohoFS.getSpaceId(rid);
			docsSpaceId = ZohoFS.getOwnerZID(rid);
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Exception while getting ownerinfo for resourceid ... " + rid, e);

		}
        String format = (String) request.getParameter("format");
        zfsngVersionId = request.getParameter("versionid");
        
		String documentId = DocumentUtils.getDocumentId(rid, sheetSpaceId);
		ContainerEntity entity = WorkbookContainer.getEntity();
		entity.docOwner = sheetSpaceId;
		entity.resourceId = rid;
		entity.docsSpaceId = docsSpaceId;

		entity.documentId = documentId;
		WorkbookContainer container = new WorkbookContainer(entity);
		String sheetversionNum = null;
		String zfsngVersionNo = null;
		if(zfsngVersionId != null) {
			zfsngVersionNo = (new JSONArrayWrapper(ZohoFS.getVersionInfoForId(container.getDocsSpaceId(), rid, zfsngVersionId))).getJSONObject(0).getString("version_number");
			sheetversionNum = DocumentUtils.getVersionNoforZFSNGVersion(container.getDocId(), container.getDocOwner(), zfsngVersionId);
		}
		
        
        
		Exporter exporter = new Exporter(container, format, sheetversionNum, zfsngVersionNo);
		byte[] content = exporter.getXLSXBytes();
		boolean exportResult = false;

		if (content == null) {
			LOGGER.log(Level.INFO, "[CONVERSION] EXPORT DOCUMENT BYTES NULL. rid : {0} ", rid);
		} else {
			try {
				if(zfsngVersionId == null) {
					zfsngVersionNo = ZohoFS.getTopVersion(docsSpaceId, rid, true);
					zfsngVersionId = ZohoFS.getVersionIDForEditor(docsSpaceId, rid, zfsngVersionNo);
				}
						
				exportResult = ZohoFS.updateVersionContent(docsSpaceId, rid, zfsngVersionId, content, format, false);
			} catch (Exception e) {
				LOGGER.log(Level.WARNING, "[BackupConversion] Couldn't update content {0}", e);
			}
		}
		if (exportResult) {
			obj.put("RESULT", "TRUE");
			obj.put("MESSAGE", "Content Updated Successfully");
			obj.put("VERSION_ID", zfsngVersionId);
		} else {
			obj.put("RESULT", "FALSE");
			obj.put("MESSAGE", "Content Update Failed");
			obj.put("VERSION_ID", zfsngVersionId);
			response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
		}
		
		response.getWriter().println(obj);
		//String zfsngVersionNo = ZohoFS.getTopVersion(docsSpaceId, rid, true);
        /*try(OutputStream out = response.getOutputStream())
        {
            out.write(content);
        }*/
        }catch(Exception e) {
        	LOGGER.log(Level.WARNING, "[BackupConversion] Couldn't backup content {0}", e);
                        obj.put("RESULT", "FALSE");
                        obj.put("MESSAGE", "Content Update Failed");
                        obj.put("VERSION_ID", zfsngVersionId);
                        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    	return;
    }
}
