/* $Id$ */
package com.zoho.sheet.conversion;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.Table;
import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.iam.UserAPI;
import com.adventnet.persistence.DataAccessException;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.ear.encryptagent.EncryptAgent;
import com.zoho.sheet.action.TableMetaHandler;
import com.zoho.sheet.exception.OfficeSuiteException;
import com.zoho.sheet.exception.ZohoSheetException;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.RemoteUtils;
import com.zoho.sheet.util.RemoteUtils.ErrorConstants;
import com.zoho.sheet.util.SheetPersistenceUtils;



/**
 * <AUTHOR>
 *
 */
public class ImportRemoteBookAction extends RemoteImporter {

	Logger logger = Logger.getLogger(ImportRemoteBookAction.class.getName());

	public static int spaceCounter = 0;
	@Override
	public RemoteBook getRemoteBook(HttpServletRequest request){
		RemoteBook exHolder = RemoteBook.getInstance();
		if(request.getRequestURI().contains("/internal/authremote")){//No I18N

			exHolder.skipAPIKeyValidtion(true);
			exHolder.setAuthCase(true);
			try {
				String zuid = request.getHeader("zuid");
				IAMProxy proxy = IAMProxy.getInstance();
				UserAPI userAPI = proxy.getUserAPI();
				Long longZuid = Long.valueOf(zuid);
				User user = userAPI.getUserFromZUID(zuid);
//					String userName = DocumentUtils.getZUserName(zuid);
				exHolder.setUserName(user.getLoginName());
				exHolder.setfullName(user.getFullName());

			}catch(Exception e){
				logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER INTERNAL REQUEST ZUID ERROR -- PROCESSING AS REMOTE");
				spaceCounter = RemoteUtils.generateSpaceCount(spaceCounter);
				exHolder.setUserName(Constants.REMOTE_USERS[spaceCounter]);
				exHolder.setSpaceName(Constants.REMOTE_USERS[spaceCounter]);

			}
		}
		else if(request.getRequestURI().contains("/authremotedoc")) {
			exHolder.skipAPIKeyValidtion(true);
			exHolder.setAuthCase(true);
			exHolder.setUserName(request.getUserPrincipal().getName());
			exHolder.setfullName(IAMUtil.getCurrentUser().getDisplayName());
		}


		else {
			spaceCounter = RemoteUtils.generateSpaceCount(spaceCounter);
			exHolder.setUserName(Constants.REMOTE_USERS[spaceCounter]);
			exHolder.setSpaceName(Constants.REMOTE_USERS[spaceCounter]);
		}
		if(request.getRequestURI().contains("/internal/remotedoc")){
			exHolder.skipAPIKeyValidtion(true);
		}
		exHolder.allowEmptyContent(true);
		//exHolder.skipAPIKeyValidtion(true);
		exHolder.setUserDocId(getUserDocId(request));
		return exHolder;
	}

	public String getUserDocId(HttpServletRequest request){
		String _userDocId = null;
		String document_info=request.getParameter("document_info");
		if(document_info!=null){
			JSONObjectWrapper _document_info = new JSONObjectWrapper(document_info);
			if(_document_info.has("document_id")){
				_userDocId	 = _document_info.getString("document_id");
			}
			//        	format = _document_info.getString("save_format");
		}else{
			_userDocId  = RemoteUtils.maskNull((String) request.getParameter("documentid"));
		}
		return _userDocId;
	}
	@Override
	public Long getApiKeyId(HttpServletRequest request){
		try{
			//APIKeyId gathered from ZAPI team.
			String apikeyid = (String) request.getAttribute("apikeyid");//No I18N;
			logger.info("apikeyid >>>>>  " + apikeyid);
			if(apikeyid != null){
				return Long.parseLong(apikeyid);
			}
			return null;

		} catch (Exception e){
			logger.log(Level.WARNING,null,e);
			logger.info("Problem while getting APIKeyId. apiKey: " + request.getParameter("apikey"));
			return null;
		}
	}

	@Override
	public void throwError(int errorCode, String[] secErrMsg) throws Exception{
		throw new ZohoSheetException("APIERROR", errorCode, secErrMsg); //NO I18N		
	}
	@Override
	public void throwError(int errorCode,String msg, String[] secErrMsg) throws Exception{
		throw new OfficeSuiteException(msg, errorCode, secErrMsg);
	}

	@Override
	public void throwError(int errorCode, String[] secErrMsg, HttpServletResponse response) throws Exception{
		JSONObjectWrapper responseObj = new JSONObjectWrapper();
		responseObj.put(JSONConstants.ERROR_MESSAGE, errorCode);
		logger.log(Level.INFO, "[CONVERSION:IMPORT] REMOTE.IMPORTER RETURNING :", responseObj.toString());
		response.getWriter().print(responseObj);
	}

	@Override
	public HashMap validateInputParams(RemoteBook holder, HttpServletRequest request){
		/* -- Input Params -- */
		HashMap ResultantMap = new HashMap();
		Boolean isOfficeSuite = request.getRequestURI().contains("officeapi");//No I18N
		Row rmbRow = null;
		String userDocId = null;
		Long remoteBookId = null;
		Long documentId = null;
		String conType = request.getContentType();
		// TODO: Need to correct the apikey settings for AuthRemote, since CRM and MAIL with same userDocId cause collaboration. Currently they are not passing any id and using normal edit mode.
		Long apiKeyId = holder.isAPIKeyValidationSkipped() ? 877 : Long.parseLong((String) request.getAttribute("apikeyid"));
		String format = RemoteUtils.maskNull((String) request.getParameter("format"));
		String _userDocId = RemoteUtils.maskNull((String) request.getParameter("documentid"));
		String url = RemoteUtils.maskNull((String) request.getParameter("url"));
		String mode = RemoteUtils.maskNull((String) request.getParameter("mode"));
		String outputType = RemoteUtils.maskNull((String) request.getParameter("output"));
		String saveURL = RemoteUtils.maskNull((String) request.getParameter("saveurl"));
		String agentName = RemoteUtils.maskNull((String) request.getParameter("agentname"));
		String handbackId = RemoteUtils.maskNull((String) request.getParameter("id"));
		String saveAsURL = RemoteUtils.maskNull((String) request.getParameter("saveasurl"));
		String integrationMetaData = RemoteUtils.maskNull((String) request.getParameter("metadata"));
		String pushtype = RemoteUtils.maskNull((String) request.getParameter("pushtype"));
		String spaceid = RemoteUtils.maskNull((String) request.getParameter("spaceid"));
		String userName = replaceSplChar(RemoteUtils.maskNull((String) request.getParameter("username")));
		String saveType=RemoteUtils.maskNull((String) request.getParameter("savetype"));
		String theme=RemoteUtils.maskNull((String) request.getParameter("theme"));
		boolean persistence = Boolean.valueOf(request.getParameter("pers"));
		boolean isTableView = Boolean.valueOf(request.getParameter("istableview"));
		int rowCount = request.getParameter("rowCount") != null ? Integer.parseInt((String) request.getParameter("rowCount")) :-1;
		int colCount =request.getParameter("colCount") != null ? Integer.parseInt((String) request.getParameter("colCount")):-1;
		int maxRowCount =request.getParameter("maxRowCount") != null ? Integer.parseInt((String) request.getParameter("maxRowCount")):-1;
		boolean isAllowInsert = Boolean.valueOf(request.getParameter("isallowinsert"));

		holder.setRow(rowCount);
		holder.setCol(colCount);
		holder.setmaxRow(maxRowCount);
		holder.setIstableview(isTableView);
		holder.setisAllowInsert(isAllowInsert);
		holder.setTheme(theme);
		String spaceName = null;
		if(holder.isAuthCase) {
			spaceName = holder.getUserName();
		}else {
			spaceName = Constants.REMOTE_USERS[spaceCounter];
			holder.setSpaceName(spaceName);
		}
		if(isOfficeSuite){
			String callback_settings=request.getParameter("callback_settings");
			if(callback_settings!=null){
				JSONObjectWrapper _callback_settings = new JSONObjectWrapper(callback_settings);
				if(_callback_settings.has("save_url")){
					saveURL = _callback_settings.getString("save_url");
				}
				if(_callback_settings.has("save_format")){
					format = _callback_settings.getString("save_format");
				}
				if(_callback_settings.has("context_info")){
					handbackId = _callback_settings.getString("context_info");
				}
				if(_callback_settings.has("savetype")){
					saveType = _callback_settings.getString("savetype");
				}
			}
			String document_info=request.getParameter("document_info");
			if(document_info!=null){
				JSONObjectWrapper _document_info = new JSONObjectWrapper(document_info);
				if(_document_info.has("document_id")){
					_userDocId = _document_info.getString("document_id");
					logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER DOCUMENT ID:{0} {1}",new Object[]{_userDocId, spaceCounter});
				}
				//        	format = _document_info.getString("save_format");
			}
			String user_info=request.getParameter("user_info");
			if(user_info!=null){
				JSONObjectWrapper _user_info = new JSONObjectWrapper(user_info);
				// 	saveURL = _user_info.getString("user_id");
				if(_user_info.has("display_name")){
					userName = replaceSplChar(_user_info.getString("display_name"));
				}
			}
		}
		//For collab we are setting spacecounter as 0 by default
		if (mode != null && mode.startsWith("collab")) {
			spaceCounter = 0;
			spaceName = Constants.REMOTE_USERS[spaceCounter];
			holder.setSpaceName(spaceName);
		}

		if(_userDocId != null) {

			userDocId =  _userDocId;
			spaceCounter = 0;
			spaceName = Constants.REMOTE_USERS[spaceCounter];
			logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER REMOTE DOCUMENT WITH DOCUMENT ID: {0} {1} {2}",new Object[]{userDocId, spaceCounter, spaceName});
			holder.setSpaceName(spaceName);


			//String encInpId = com.adventnet.iam.CryptoUtil.decrypt("zohosheet_secret", _userDocId.trim()); //No I18N
			String encInpId = "";
			try{
				String refreshToken = EnginePropertyUtil.getSheetPropertyValue("EAR_REFRESH_TOKEN");	//No I18N
				encInpId = EncryptAgent.getInstance().decrypt("zohosheet_secret", _userDocId.trim(), refreshToken, false);	//No I18N
			}catch (Exception ear) {
				logger.log(Level.INFO, "Ear Encryption Failed, Assigning the user provided value ", ear.getMessage());
				encInpId = _userDocId;
			}

if(encInpId != null && !"".equalsIgnoreCase(encInpId)) {
	userDocId = encInpId;
}



		}
		logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER User doc ID...:{0} {1}",new Object[]{userDocId, spaceCounter});
		if(userDocId != null) {
			try {
				//rmbRow = RemoteUtils.fetchRemoteBookRow(null, userDocId, apiKeyId, true, Constants.REMOTE_USERS[spaceCounter]);
				rmbRow = RemoteUtils.fetchRemoteBooksRowUsingUserDocId(userDocId, apiKeyId);
				if(rmbRow != null) {
					holder.setAsExistingDoc();
					remoteBookId = (Long) rmbRow.get("REMOTE_BOOK_ID");
					documentId = (Long) rmbRow.get("DOCUMENT_ID");
					holder.setRemoteBookId((Long) rmbRow.get("REMOTE_BOOK_ID")); //NO I18N
					logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER EXISTING DOCUMENT WITH DOCUMENT ID: {0} {1} {2} {3}",new Object[]{userDocId, spaceCounter, spaceName, userName});
					//set the spaceCounter same as the existing document owner.
					try {
						Persistence pers;
						String docOwner = null;
						Criteria docIDCri = new Criteria(new Column("Documents","DOCUMENT_ID"),documentId,QueryConstants.EQUAL);
						for(String remoteUser:Constants.REMOTE_USERS_FOR_DOC_ID){

							pers = SheetPersistenceUtils.getPersistence(remoteUser);
							DataObject dObj = pers.get("Documents",docIDCri);
							if(!dObj.isEmpty()) {
								docOwner =  (String) dObj.getRow("Documents").get("AUTHOR_NAME");
							}
						}

						spaceName = docOwner;
						spaceCounter = RemoteUtils.getSpaceCounter(spaceName);
						holder.setSpaceName(spaceName);
					}catch (Exception e) {
						logger.info("Existing document.... Changing DocOwner Exception:" +e);
					}
					logger.info("Existing document.... DocOwner Changed:" +spaceName+"::"+spaceCounter);
				}
			} catch (DataAccessException e) {
				logger.info("Internal server error. Unable to get the RemoteBookRow for " + userDocId + " & apikeyId: " + apiKeyId);
			}
		}

		/* -- Validation -- */
		//contentType validation
		if(conType != null && conType.indexOf("application/x-www-form-urlencoded") == -1 && conType.indexOf("multipart/form-data") == -1){
			return getErrMap(ResultantMap, ErrorConstants.INVALID_CONTENT_TYPE, null, "The Conent-Type header [" + request.getContentType() + "] set in your HttpRequest is wrong."); //NO I18N
		}

		//pushFormat validation
		if(format != null) {
			if(format.indexOf(".") == 0){
				format = format.substring(1); // removing dot
			}
			if(ImportExportUtil.getFilterName(format) == null)	{
				return getErrMap(ResultantMap, 2832, "format", "Value specified for format parameter is incorrect " +format);//NO I18N
			}
		} else {
			format = "xls";//NO I18N
		}

		//user document id validation
		

		/* Malformed URL validation for the 'application/x-www-form-urlencoded' content type 
		   ['multipart/form-data' contentType was handled by IAM/Security filter] */
		if((conType == null || conType.indexOf("application/x-www-form-urlencoded") != -1) && url != null) {
			try {
				URL fileUrl = new URL(url);
			} catch (MalformedURLException e) {
				return getErrMap(ResultantMap, 2832, "url", "Value specified for 'url' parameter is incorrect."+url); //NO I18N
			}
		}

		/* -- Settings -- */
		//Existing doc setting



		//mode validation & setting
		//-- Default mode setting based on output type

		if(mode == null) {
			mode  = ("view".equalsIgnoreCase(outputType) || "viewurl".equalsIgnoreCase(outputType)) ? "view" : "normaledit"; //No I18N
		}

		//-- Mode setting for no save url & no agent name case
		if(saveURL == null){
			mode = ("collabedit".equalsIgnoreCase(mode) || "collabview".equalsIgnoreCase(mode)) ? "collabview": mode;//No I18N
		}



		//ZAPI - Commercial(View Only) plan
		logger.info("### ZAPI: Valid param: " + request.getAttribute("valid"));
		if(request.getAttribute("valid") != null && (int)request.getAttribute("valid") == 10) {
			mode = "view";//NO I18N
		}
		if(isOfficeSuite){
			String permissions=request.getParameter("permissions");
			String document_info=request.getParameter("document_info");

			Boolean isedit = true;
			if(permissions!=null){
				JSONObjectWrapper _permissions = new JSONObjectWrapper(permissions);
				if(_permissions.has("document.edit")){
					isedit =_permissions.getBoolean("document.edit");
				}
			}if(request.getRequestURI().contains("preview")){//NO I18N
				if(document_info!=null) {
					JSONObjectWrapper _document_info = new JSONObjectWrapper(document_info);
					if (_document_info.has("document_id")) {
						mode = "collabview";//NO I18N
					} else {
						mode = "view";//NO I18N

					}
				}else {
					mode = "view";//NO I18N

				}

			}else if(holder.existingDoc && isedit){
				mode = "collabedit";//NO I18N
			}else if(isedit){
				if(document_info!=null){
					JSONObjectWrapper _document_info = new JSONObjectWrapper(document_info);
					if(_document_info.has("document_id")){
						mode  = "collabedit";//NO I18N
					}
				}

			}else if (!isedit){
				mode = "collabview";//NO I18N
			}else{
				mode = "normaledit";//NO I18N
			}
			if(holder.isExistingDoc()) {
				try{
				int existMode = RemoteUtils.fetchModeforExistingRemoteBook(remoteBookId, Constants.REMOTE_USERS[spaceCounter]);

				if ((existMode == 3 || rmbRow.get("SAVE_URL_OR_AGENT_NAME") == null) && (!"collabview".equalsIgnoreCase(mode) || !"view".equalsIgnoreCase(mode))) {

				RemoteUtils.updateSaveUrlinRemoteBook(remoteBookId,spaceName,saveURL);

				}
				} catch (Exception me) {
					logger.info("problem while setting MODE for the user" + mode);
				}
			}
		}else{
			if(holder.isExistingDoc() ){
				try{
					if("collabedit".equalsIgnoreCase(mode) || "collabview".equalsIgnoreCase(mode)) {
						int existMode = RemoteUtils.fetchModeforExistingRemoteBook(remoteBookId, Constants.REMOTE_USERS[spaceCounter]);
						if(existMode == 1) {
							mode = "view";//NO I18N
						}
					} else if("normaledit".equalsIgnoreCase(mode)) {
						int existMode = RemoteUtils.fetchModeforExistingRemoteBook(remoteBookId, Constants.REMOTE_USERS[spaceCounter]);
						if(existMode == 2 || existMode == 3) {
							mode = "collabedit";//NO I18N
						} else if(existMode == 1) {
							mode = "view";//NO I18N
						}
					}
				} catch (Exception me){
					logger.info("problem while setting MODE for the user" + mode);
					mode = "view"; //NO I18N
				}
			}

		}
		holder.setDocMode(mode);
		int modeValue = RemoteUtils.getModeValue(mode);
		logger.info("Mode: " + mode + " :: " + modeValue);
		//WMS_Annon user name creation for collaboration - is not needed during import

		//handbackId is mandatory except 'view' mode. >>> ALLOWED it due to other service (consistency)
		/*if(!mode.equalsIgnoreCase("view") && handbackId == null){
			return getErrMap(ResultantMap, 2848, null, "Missing 'Id' parameter value");//NO I18N
		}*/

		//Collab Document setting
		holder.setCollabDoc(("collabedit".equalsIgnoreCase(mode) || "collabview".equalsIgnoreCase(mode) || userDocId != null) ? true : false);//NO I18N

		//Edit permission decision
		boolean allowEdit = ("view".equalsIgnoreCase(mode) && !holder.collabDoc()) ? false : true;//NO I18N

		//Setting WMS SAVE MODE
		int wmsSaveMode = 0; //Default save/push URL mode
		if(agentName != null && agentName.length() != 0){
			wmsSaveMode = 1; // Agent save mode
		}else if(pushtype != null && pushtype.equalsIgnoreCase("param")){
			wmsSaveMode = 10; // ZCreator integration.
		}
		if("changes_only".equalsIgnoreCase(saveType)) {
			wmsSaveMode=2; // JSON import - Enhanced remote API
		}
		//resuing WMS Save Mode parameter to indicate Remote Document Enchanced view , Lead to confusion ,use meaningful names : (


		//Setting user name - NOTE: This name will be used only for DB update //TODO: Need to check it again
		if(userName == null) {
			userName = "Guest"; //NO I18N
		}
		if(handbackId == null){
			handbackId =  " ";//NO I18N //RemoteBooks_Stats table HANDBACK_ID is not null column.thus updating empty on it.Need to handle it.
		}
		//Setting 'RemoteDocData' Object
		RemoteDocData rdd = new RemoteDocData();
		rdd.apiKeyId = apiKeyId;
		rdd.saveUrl = saveURL;
		rdd.agentName = agentName;
		rdd.pushFormat = format;
		rdd.handBackId = handbackId;
		rdd.isExistingDoc = holder.isExistingDoc();
		rdd.allowEdit = allowEdit;
		rdd.saveAsUrl = saveAsURL;
		rdd.integrationMetaData = integrationMetaData;
		rdd.wmsSaveMode = wmsSaveMode;
		rdd.userDocId = userDocId;
		rdd.remoteBookId = remoteBookId;
		rdd.documentId = documentId;
		rdd.userName = userName;
		rdd.mode = modeValue;
		rdd.spaceName = spaceName;

		rdd.spaceid=spaceid;

		rdd.persistence = persistence;
		ResultantMap.put("REMOTE_BOOK_OBJECT", rdd);
		logger.info("VALIDATION RESULT::: " + ResultantMap+"::"+spaceName);
		return ResultantMap;
	}

	@Override
	public HashMap getDBUpdater(HashMap validatorResMap){

		TableMetaHandler tmh = new TableMetaHandler();
		HashMap tmpMap = new HashMap();

		RemoteDocData rbObj = (RemoteDocData) validatorResMap.get("REMOTE_BOOK_OBJECT");
		validatorResMap.put("USER_NAME", rbObj.userName);
		validatorResMap.put("SPACE_NAME", rbObj.spaceName);

		boolean isExistingDoc = Boolean.valueOf(rbObj.isExistingDoc);
		if(!isExistingDoc){
			validatorResMap.put("API_KEY_ID", (Long) rbObj.apiKeyId);
			//Giving first preference to agent Name				
			validatorResMap.put("SAVE_URL_OR_AGENT_NAME", (rbObj.agentName != null) ? (String) rbObj.agentName : (String) rbObj.saveUrl);
			validatorResMap.put("PUSH_FORMAT", (String)rbObj.pushFormat);
			validatorResMap.put("HANDBACK_ID", (String)rbObj.handBackId);
			validatorResMap.put("ALLOW_EDIT", rbObj.allowEdit);
			validatorResMap.put("IS_WMS_SAVE_MODE", rbObj.wmsSaveMode);
			validatorResMap.put("USER_DOC_ID", rbObj.userDocId);
			validatorResMap.put("MODE", rbObj.mode);
			validatorResMap.put("META_DATA", rbObj.integrationMetaData);
			validatorResMap.put("PERSISTENCE", rbObj.persistence);

			validatorResMap.put("ZSOID", (rbObj.spaceid != null) ?rbObj.spaceid:"-1");

			HashMap remoteMap = (HashMap) tmh.getRemoteBooksInstance().getValue(validatorResMap);

			if(RemoteUtils.isValidHashMap(remoteMap)){
				tmpMap.putAll(remoteMap);
			}
		} else {
			validatorResMap.put("HANDBACK_ID", (String)rbObj.handBackId);
			validatorResMap.put("REMOTE_BOOK_ID", rbObj.remoteBookId);
			validatorResMap.put("DOCUMENT_ID", rbObj.documentId);
			validatorResMap.put("MODE", rbObj.mode);
		}
		//remoteBookState table update
		HashMap remoteStateMap  = (HashMap) tmh.getRemoteBooksStateInstance().getValue(validatorResMap);

		if(RemoteUtils.isValidHashMap(remoteStateMap)){
			tmpMap.putAll(remoteStateMap);
		}

		return tmpMap;
	}

	@Override
	public void doDepdProcess(RemoteBook holder){
		if(holder.isAuthCase()){

			// Authenticate remote API scheduler code
			try{
				//TODO: Need to Handle DFS
				logger.info("AUTH_REMOTE SPACE "+Constants.REMOTE_USERS[spaceCounter]);
				Persistence pers = SheetPersistenceUtils.getPersistence(Constants.REMOTE_USERS[spaceCounter]);
				logger.info("AUTH_REMOTE SPACE "+Constants.REMOTE_USERS[spaceCounter]);
				logger.info("AUTH_REMOTE USERNAME "+holder.getUserName());
				SelectQueryImpl sql = new SelectQueryImpl(new Table("AuthRemoteUser"));  // NO I18N
				Criteria cri =  new Criteria(new Column("AuthRemoteUser","USER_NAME"),holder.getSpaceName(),QueryConstants.EQUAL);  // NO I18N
				sql.addSelectColumn(new Column("AuthRemoteUser" , "*" ));  // NO I18N
				sql.setCriteria(cri);
				DataObject dobj = pers.get(sql);

				if(dobj.isEmpty()){
					Long zuid = DocumentUtils.getZUID(holder.getSpaceName());
					DataObject userDO = pers.constructDataObject();
					Row userRow = new Row("AuthRemoteUser");  // NO I18N
					userRow.set("USER_NAME", holder.getSpaceName());  // NO I18N	
					userRow.set("AUTHOR_ZUID", zuid);
					userDO.addRow(userRow);
					pers.add(userDO);
				}
			} catch (Exception e){
				logger.info("Problem while updating " + holder.getSpaceName() +" entry in AuthRemoteUser table." + e); // NO I18N
			}
		}
	}

	@Override
	public void generateResponse(RemoteBook holder, HttpServletRequest request, HttpServletResponse response){

		String toEncryptStr = ""+holder.getRemoteBookId()+"_"; //No I18N
		String spaceval = "-1_";
		if(holder.getRemoteBookStateId() != null) {
			toEncryptStr += holder.getRemoteBookStateId();
		}
		toEncryptStr += "_" + holder.getMode();//No I18N

		//TODO: Wrong SpcaeCounter Issue Fix,Remove these logs and use space name alone, when stable.
		logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER URI: {0}",new Object[]{spaceCounter});
		try{
			String spaceName = holder.getSpaceName();
			int spaceCounter1 = RemoteUtils.getSpaceCounter(spaceName);
			if(!holder.isAuthCase && spaceCounter1 != 5 && spaceCounter1 != spaceCounter) {
				spaceCounter = spaceCounter1;
			}

		}catch (Exception e) {
			logger.log(Level.INFO,e.getMessage());
		}
		toEncryptStr = toEncryptStr +"_" + (holder.isAuthCase? spaceval+ DocumentUtils.getZUID(holder.getSpaceName()):spaceCounter);
		//toEncryptStr += "_" + spaceCounter;//No I18N
		logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER URI: {0}",new Object[]{toEncryptStr});

		String encrypt = ClientUtils.getEncryptedValue(toEncryptStr, true);
		String uri = ((holder.isAuthCase()) ? "ar/" + encrypt : "officeapi/v1/" + encrypt);	//No I18N
//                String uri = ((holder.isAuthCase()) ? "sheeteditor.do" : "editor.do") + "?doc=" + encrypt;	//No I18N
		//Locale setting
		String lang = request.getParameter("lang");
		lang = (lang != null && ImportExportUtil.getLocaleDetails(lang) != null) ? lang : null;
		String countryCode = RemoteUtils.maskNull((String) request.getParameter("countrycode"));
		if(lang != null) {
			uri += "?lang=" + lang;//No I18N
			if(countryCode != null){
				uri += "&countrycode=" + countryCode;//No I18N
			}
		}
		// Secret key evaluation was done by API team. Below code used for local testing
		String sKey = RemoteUtils.maskNull((String) request.getParameter("skey"));

		String scheme = request.getScheme()+"://";//No I18N		
		if(sKey != null || holder.isAuthCase()) {
			scheme = "https://"; //No I18N		
		}

		uri = "/sheet/"+uri;       //No I18N
		String reqUrl = request.getRequestURL().toString();
		String servername = request.getServerName();
		String sheetUrl = EnginePropertyUtil.getSheetPropertyValue("SHEET_SERVER_URL"); //NO I18N
		String url ="";
		if(!reqUrl.contains(sheetUrl)&& request.getAttribute("whiteLabelSettings") != null){
			try{
				URL netUrl = new URL(reqUrl);
				String host = netUrl.getHost();
				String settings = request.getAttribute("whiteLabelSettings").toString();
				logger.log(Level.INFO,"White label settings:::::"+settings+":::"+host);
				JSONObjectWrapper _settings = (settings != null) ?new JSONObjectWrapper(settings):new JSONObjectWrapper();
				JSONArrayWrapper customDomains =new JSONArrayWrapper();
				boolean flag = false;
				if(_settings.has("customDomains")){
					customDomains = _settings.getJSONArray("customDomains");
					logger.log(Level.INFO,"White label settings:::::");
					for (int i = 0; i < customDomains.length(); i++) {
						logger.log(Level.INFO,"White label settings:::::"+customDomains.get(i).toString()+":::"+host);
						String _url = customDomains.get(i).toString();
						if (_url.contains(host)){
							flag = true;
							url = _url+uri ;
							logger.log(Level.INFO,"Custom domain url:::::"+url);
							break;
						}
					}


				}
				else{
					if(!host.contains("sheet.zoho")){
						url = host +uri;
						flag = true;
					}
				}

				if(!flag){
					url = scheme + sheetUrl+uri;
				}
			}catch(Exception e){
				logger.log(Level.WARNING,"Problem in Office suite api URI modification ",e);
				url = scheme + ClientUtils.getServerURL(request, false, uri, false, false);

			}
		}else if(!reqUrl.contains(sheetUrl)){
			url = scheme +servername + uri;
		}
		else{
			url = scheme + ClientUtils.getServerURL(request, false, uri, false, false);
		}
		logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER FINAL URL: {0} {1}",new Object[]{toEncryptStr, url});

		String userDocId = (holder.getUserDocId() != null) ? holder.getUserDocId() : ClientUtils.getEncryptedValue(holder.getRemoteBookId().toString(), false);

		String outputType = RemoteUtils.maskNull((String) request.getParameter("output"));

		String deviceType	=	request.getParameter("devicetype");




		try{
			if("handheld".equals(deviceType)) {

				JSONObjectWrapper responseObj = new JSONObjectWrapper();
				responseObj.put("doc", encrypt);
				response.getWriter().print(responseObj); // NO OUTPUTENCODING

			} else {
				if(("editor".equalsIgnoreCase(outputType) || "view".equalsIgnoreCase(outputType))) {
					// open the editor
					response.sendRedirect(url);
				} else {
					if("id".equalsIgnoreCase(outputType)) {
						response.getWriter().write(encrypt); //NO OUTPUTENCODING
					} else {
						// send back the url
						String docIdPrint = "DOCUMENTID=" + userDocId;//No I18N

						//				if (returnResponseAsString) {
						//					return ("RESULT=TRUE" + "URL=" + url + extraParams);//No I18N
						//				} else {
						if( request.getRequestURI().contains("officeapi")){
							String key = request.getRequestURI().contains("preview")?"preview_url" :"document_url"; //No I18N
							JSONObjectWrapper resp = new JSONObjectWrapper();
							resp.put("document_id", userDocId);
							String testurl = request.getRequestURL().toString();
							String respDeleteURL = testurl.contains("preview") ? testurl.replace("/preview", ""):testurl;//No I18N
							resp.put("document_delete_url", respDeleteURL+"/"+userDocId);
							String encryptStr = null;
							if(holder.getRemoteBookStateId() != null) {
								encryptStr = holder.getRemoteBookStateId().toString();
								encryptStr += "_" + spaceCounter;//No I18N
							}
							HashMap details = holder.getMapHolder();
							RemoteDocData data = null;
							if(details.containsKey("REMOTEBOOKS_STATE_TABLE")){
								HashMap rmbsDetails = (HashMap) details.get("REMOTEBOOKS_STATE_TABLE");
								data= rmbsDetails.containsKey("REMOTE_BOOK_OBJECT")?(RemoteDocData) rmbsDetails.get("REMOTE_BOOK_OBJECT") :null;//No I18N

							}
							HashMap rmbDetails = details!= null&& details.containsKey("REMOTEBOOKS_TABLE") ? (HashMap) details.get("REMOTEBOOKS_TABLE") : null;//No I18N
							String saveUrl = rmbDetails != null && rmbDetails.containsKey("SAVE_URL_OR_AGENT_NAME") ?(String) rmbDetails.get("SAVE_URL_OR_AGENT_NAME"):null;//No I18N
							saveUrl = saveUrl != null ?saveUrl :(data != null? data.saveUrl :null);
							if(saveUrl != null){

								resp.put("save_url", respDeleteURL+"/"+encrypt+"/save");
							}
							resp.put("download_url",respDeleteURL+"/"+encrypt+"/download");

							String _encrypt = encryptStr != null ? ClientUtils.getEncryptedValue(encryptStr, true):null;

							respDeleteURL = respDeleteURL.replace("spreadsheet", "session");
							resp.put("session_delete_url", respDeleteURL+"/"+_encrypt);
							resp.put("session_id", _encrypt);
							resp.put(key,url);

							respDeleteURL = respDeleteURL.replace("session", "");
							String gridURl = respDeleteURL + encrypt + "?zview=rgrid";  //No I18N
							resp.put("gridview_url",gridURl);

							response.setContentType("application/json");
							response.getWriter().println(resp);

						}else if(request.getRequestURI().contains("/internal/authremotedoc") ){//No I18N

							JSONObjectWrapper resp = new JSONObjectWrapper();
							resp.put("document_id", encrypt);
							resp.put("url", url);
							//resp.put("slice_range",);
							response.getWriter().println(resp);
						}else{

							response.setContentType("text/plain");//No I18N
							response.getWriter().println("RESULT=TRUE");//No I18N
							response.getWriter().println("URL=" + url);  //NO OUTPUTENCODING //No I18N
							//response.getWriter().println("DOC="+encrypt);//No I18N

							if(holder.collabDoc()) {
								response.getWriter().println(docIdPrint);
							}
						}
					}
				}
			}
		}catch (Exception e){
			logger.info("Problem while generating response."+e);
		}
	}

	public class RemoteDocData {

		public Long apiKeyId = null;
		public String userName = null;
		public String spaceName = null;
		public String spaceid = null;
		public int mode = -1;
		public String pushFormat = null;
		public String userDocId = null;
		public Long remoteBookId = null;
		public Long documentId = null;
		public boolean isExistingDoc = false;
		public boolean allowEdit = false;
		public String saveUrl = null;
		public String agentName = null;
		public String saveAsUrl = null;
		public String integrationMetaData = null;
		public String handBackId = null;
		public int wmsSaveMode = 0;
		public boolean persistence = false;

	}

	public HashMap getErrMap(HashMap hm, Integer errCode, String secErrMsg, String loggerMsg){
		hm.put("IS_ERROR", "YES"); //No I18N
		hm.put("eC", ((errCode == null) ? 2832 : errCode)); //NO I18N
		if(secErrMsg != null) {
			hm.put("secErrMsg", secErrMsg.split(",")); //NO I18N
		}
		if(loggerMsg != null) {
			logger.info(loggerMsg);
		}
		return hm;
	}
	public String replaceSplChar(String loginName){
		if( loginName == null ){
			return null;
		}
		Pattern pt = Pattern.compile("[^0-9a-zA-Z_\\-\\+\\.\\$@\\?\\,\\:\\'\\/\\!\\[\\]\\|&amp\\(\\)\\p{L}\\. \'-]",Pattern.UNICODE_CHARACTER_CLASS);
		Matcher match= pt.matcher(loginName);
		while(match.find())
		{
			String s = match.group();
			loginName=loginName.replaceAll("\\"+s, " ");
		}
		return loginName;
	}
}
