// $Id$
package com.zoho.sheet.conversion;

import java.io.*;
import java.text.DateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.util.*;

import com.adventnet.zoho.websheet.store.Store;
import com.zoho.sheet.util.*;
import org.apache.commons.collections4.BidiMap;
import org.apache.commons.collections4.bidimap.DualHashBidiMap;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Table;
import com.adventnet.ds.query.*;
import com.adventnet.mfw.bean.BeanUtil;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;

import com.adventnet.zoho.websheet.model.style.ColumnStyle;
import com.adventnet.zoho.websheet.model.style.Style;
import com.adventnet.zoho.websheet.model.style.TableStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.DefaultPatternUtil;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.ConditionalFormatOperator.ConditionType;
import com.google.gson.*;
import com.zoho.sheet.conversion.TableColHeader.ColType;


import javax.servlet.http.HttpServletRequest;
import java.io.Reader;

/**
 * Used to parse JSON and copy values to a sheet
 *
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
public class JsonTableParser {
	private static Logger logger = Logger.getLogger(RedisHelper.class.getName());
	Reader jsonReader = null;
	private TableColHeader columnHeaders = null;// columnHeaders represented as
	// a tree of ColHeaders
	private int noOfCols = -1;
	//private int maxRow = -1;
	private int noOfValueRows = -1;
	private int colHeaderDepth = -1; // total number of header rows
	private static String redisKey = "Sheet:json:"; // No I18N

	private WorkbookContainer container = null;
	private JsonArray errorJsonArray = null;
	private JsonArray rowerrorJsonArray = null;
	private JsonArray notesJsonArray = null;
	// private JsonObject header = new JsonObject();

	private  BidiMap rowIndex = new DualHashBidiMap();
	private  BidiMap rowIndex_added = new DualHashBidiMap();
	private  BidiMap displayNameMap = new DualHashBidiMap();
	private  List checkBoxMap = new ArrayList();
	private  HashMap<String, Integer> colIndex = new HashMap();
	private  HashMap mandatoryMap = new HashMap();
	private  Set resultSet = null;

	private JsonObject jsonHeaderObject = new JsonObject();
	private JsonObject ui_settings = null;// new JsonObject();

	private JSONArrayWrapper jsonModifiedObject = new JSONArrayWrapper();
	private JSONArrayWrapper jsonAddedObject = new JSONArrayWrapper();
	private JSONObjectWrapper pickList = new JSONObjectWrapper();
	private JSONObjectWrapper pickList_Default = new JSONObjectWrapper();
	private JSONObjectWrapper viewSettings = new JSONObjectWrapper();
	private JsonArray rowLockIndex = new JsonArray();
	private JsonArray colhideIndex = new JsonArray();
	private JsonArray rowhideIndex = new JsonArray();
	private JSONObjectWrapper dateCol = new JSONObjectWrapper();
	private JSONObjectWrapper picklistCol = new JSONObjectWrapper();
	private JSONObjectWrapper data_counter = new JSONObjectWrapper();
	private HashMap colWidthMap = new HashMap<>();

	private boolean contentStatus = false;

	private int cellIndex_sR = -1;
	private int cellIndex_sC = -1;
	private boolean isAllowInsert = true;
	// private JsonObject displayname = new JsonObject();
	// private RemoteBook remotebook = null;
	// private String remoteBookID = null;
	// private String userName = null;

	// public JsonTableParser(Reader jsonFileReader) {
	// this.jsonReader = jsonFileReader;
	// }
	public JsonTableParser(Reader jsonFileReader) {
		this.jsonReader = jsonFileReader;

		// this.remotebook = remotebook;

	}

	public JsonTableParser() {
		// TODO Auto-generated constructor stub
	}

	public void copyJsonTableToSheet(WorkbookContainer container, int index,RemoteBook remoteBook) throws Exception {
		Workbook workbook = container.getWorkbook(null);
		Sheet sheet = workbook.getSheet(index);
		this.container = container;
		this.isAllowInsert = remoteBook != null ?remoteBook.isAllowInsert :true;

		copyJsonTableToSheet(container,remoteBook);

		if (this.errorJsonArray != null) {
			decideErroDenotationMethod(container, this.errorJsonArray,true,false,false);

			//this.writeErrorJsoninDFS(container);
		}
		if(this.rowerrorJsonArray != null) {
			setRowError(container,this.rowerrorJsonArray);
		}


		if (this.notesJsonArray != null) {
			setNotes(sheet);
		}
//		if(this.ui_settings != null){
//
//		}




		// if(this.displayname != null){

		setDetails(container);


		// }

	}

	private JsonElement parseJSONFileToJSonObject() {
		JsonParser jsonParser = new JsonParser();
		return jsonParser.parse(this.jsonReader);// parsing using IN MEMORY
		// model ,need to later
		// parse as stream
	}

	private void copyJsonTableToSheet(WorkbookContainer container,RemoteBook remoteBook) throws Exception {
		JsonElement recievedJson = parseJSONFileToJSonObject();

		JsonArray values = null;
		Workbook workbook = container.getWorkbook(null);
		Sheet sheet = workbook.getSheetByAssociatedName("0#");

		// check if JSON has only values or header +values
		// both cases value can be of two formats, can be [ [],[],[] ...] or
		// [{},{},{}...] format

		if (recievedJson.isJsonArray()) { // headerless
			values = recievedJson.getAsJsonArray();

			if (isArrayOfArrays(values)) {
				this.colHeaderDepth = 0;// headerless and of array of arrays
				// format ,no headers available
				this.noOfCols = maxColSize(values);
			} else {

				this.columnHeaders = getColHeadersFromValues(values);
				this.noOfCols = columnHeaders.getNoOfLeaves();
				this.colHeaderDepth = 1;// no nested values allowed , when
				this.noOfValueRows = values.size();
				// headers are not given and we detect
				// them , here there will be only one
				// level header
			}

		} else { // json with header + values
			JsonObject jsonObject = recievedJson.getAsJsonObject();
			JsonArray headerJsonArray = null;
			//if(jsonObject.has(Constants.JSON_COL_HEADERS)){
			headerJsonArray = jsonObject.get(Constants.JSON_COL_HEADERS).getAsJsonArray();
			constructHeaderJson(headerJsonArray);
			//}
			if (jsonObject.get(Constants.JSON_ERROR_DENOTATION) != null) {
				errorJsonArray = jsonObject.get(Constants.JSON_ERROR_DENOTATION).getAsJsonArray();
			}
			if (jsonObject.get(Constants.JSON_NOTES_DENOTATION) != null) {
				notesJsonArray = jsonObject.get(Constants.JSON_NOTES_DENOTATION).getAsJsonArray();
			}
			if(jsonObject.has(Constants.JSON_ROW_INDEX_LOCK)){
				this.rowLockIndex = jsonObject.get(Constants.JSON_ROW_INDEX_LOCK).getAsJsonArray();
			}
			if(jsonObject.has(Constants.JSON_ROW_INDEX_HIDE)){
				this.rowhideIndex = jsonObject.get(Constants.JSON_ROW_INDEX_HIDE).getAsJsonArray();
			}
			if(jsonObject.has(Constants.JSON_COL_INDEX_HIDE)){
				this.colhideIndex = jsonObject.get(Constants.JSON_COL_INDEX_HIDE).getAsJsonArray();
			}
			if(jsonObject.has(Constants.JSON_ROW_INDEX_ERROR)){
				this.rowerrorJsonArray = jsonObject.get(Constants.JSON_ROW_INDEX_ERROR).getAsJsonArray();
			}
			if(jsonObject.has(Constants.JSON_UI_SETTINGS)){
				this.ui_settings = new JsonObject();
				this.ui_settings = jsonObject.getAsJsonObject(Constants.JSON_UI_SETTINGS);
				setUISettings(container);
			}

			if (notesJsonArray != null) {
				JsonObject node = new JsonObject();
				// node.add("name", "Notes");
				node.addProperty("name", "Notes");// No I18N
				headerJsonArray.add(node);

			}
			if(jsonObject.has(Constants.JSON_VALUES)) {
				values = jsonObject.get(Constants.JSON_VALUES).getAsJsonArray();
				this.noOfValueRows = values.size();


			}

			this.columnHeaders = getColHeaderTree(headerJsonArray);
			this.noOfCols = columnHeaders.getNoOfLeaves();
		}

		//this.noOfValueRows = values.size();

		if (this.noOfCols > Utility.MAXNUMOFCOLS || colHeaderDepth + noOfValueRows > Utility.MAXNUMOFROWS) {
			throw new Exception("Column size exceeds max col size or max row size");
		}
		int maxRow = remoteBook != null ? remoteBook.getmaxRow() :this.noOfValueRows ;
		if (this.columnHeaders != null) {
			RangeUtil.setPattern(sheet,0,0,0,this.noOfCols,DefaultPatternUtil.getDefaultPattern(sheet.getWorkbook(), Type.STRING
			));

			setColHeadersToSheet(container, 0, this.columnHeaders, sheet,maxRow);
		}
		boolean tableview  = remoteBook != null ?remoteBook.getistableview():false;
		if(tableview) {


			int row = remoteBook.getRow()!= -1 ?remoteBook.getRow() : (this.noOfValueRows >0 ? this.noOfValueRows : 1);
			int col = remoteBook.getCol()!= -1 ?remoteBook.getCol() -1  :this.noOfCols -1;

			//int maxRow = remoteBook.getmaxRow();
			String theme = remoteBook.theme;

			container.setJsonMaxRow(maxRow);
			logger.info("my test max row print here:::"+container.getJsonMaxRow()+"::::"+maxRow);
			String tableName ="DataTable";//No I18N
			// ActionUtil.getNewTableName(workbook); // YOU CAN ALSO SET ANY UNIQUE NAME YOU WANT
			int tableID = workbook.registerAndGetNewTableID();
			DataRange range = new DataRange(sheet.getAssociatedName(), 0, 0, maxRow, col);
			com.adventnet.zoho.websheet.model.Table table = TableUtil.createTable(sheet, range, tableName, tableID, null,false, false, maxRow, false, null);
			TableStyle tableStyle;
			table.setAutoFillExpression(false);

			if(this.ui_settings != null && this.ui_settings.has("table")){
				JsonObject tablesettings = this.ui_settings.get("table").getAsJsonObject();
				String rowColor = "";
				String borderColor = "";
				String fontColor = "";
//
//
//				}
				if(tablesettings.has("rowColor")){
					rowColor = tablesettings.get("rowColor").getAsString();
				}
				if(tablesettings.has("borderColor")){
					borderColor =  tablesettings.get("borderColor").getAsString();
				}
				if(tablesettings.has("fontColor")){
					fontColor =  tablesettings.get("fontColor").getAsString();
				}
				//tableStyle = TableUtil.addAndGetTableStyleForSheetView(workbook,rowColor,borderColor);
				tableStyle = TableUtil.addAndGetTableStyleForSheetView(workbook,rowColor,borderColor,fontColor);
				if(tablesettings.has("bandedRows")) {
					boolean bandedRows = tablesettings.get("bandedRows").getAsBoolean();

					table.setShowRowStripes(bandedRows);
				}


			}
			else if(theme != null && !"".equalsIgnoreCase(theme)) {
				tableStyle = TableUtil.addAndGetTableStyleForSheetView(workbook);
				table.setShowRowStripes(false);
			}else {
				tableStyle = new TableStyle(workbook.getUniqueTableStyleName(tableName), false);
			}
			table.setTableStyleName(tableStyle.getName(), true);
			workbook.addTableStyle(tableStyle, true);
		}

		if (this.pickList != null && !this.pickList.isEmpty()) {
			setPickList(container,maxRow);
		}
		if(values != null) {
			copyValuesToSheet(sheet, values);
		}
		if (this.columnHeaders != null) {
		//setFormattingToTable(sheet);
			ActionUtil.freezePaneRows(sheet, 0, this.colHeaderDepth - 1);
			RangeUtil.protectRange(sheet, 0, 0, this.colHeaderDepth - 1, this.noOfCols - 1,
					Protection.createPermissionMap(new HashSet<>(java.util.Arrays.asList("-9999")), null, null, null, null, null, null),
					isAllowInsert,true);
		}


		// ActionUtil.calculateOptimalRowHeight(sheet, 0,
		// sheet.getUsedRowIndex(), true);
		// FTS
		//
		setOptimalColumnWidthPlus(sheet, 0, sheet.getUsedColumnIndex(), false);
		ActionUtil.calculateOptimalRowHeight(sheet, 0, 65535, true);

	}

	/**
	 * //Construct a ColHeader tree
	 *
	 * /* root(NoColname) | / | \ Column 1 Column 2 Column 3 | / \ Col2-SubCol1
	 * Col2-SubCol2
	 *
	 **/
	private TableColHeader getColHeaderTree(JsonArray jsonArray) throws Exception {
		List<TableColHeader> headers = new ArrayList<>();
		for (JsonElement jsonHeaderElement : jsonArray) {
			JsonObject jsonHeaderObject = jsonHeaderElement.getAsJsonObject();
			headers.add(parseHeaderNode(jsonHeaderObject));
		}

		TableColHeader treeRoot = new TableColHeader(null);// nameless header is
		// root
		treeRoot.setSubHeaders(headers);
		// now set width and depth of each node
		countLeaves(treeRoot);
		setDepth(0, treeRoot);
		trickeDownNodeProperties(null, null, false, treeRoot);
		return treeRoot;
	}

	/**
	 * Recursively parse individual headers in JSON and construct a header
	 * object with type,pattern ...properties for each and return a tree
	 * structure
	 *
	 * @return
	 */
	private TableColHeader parseHeaderNode(JsonObject jsonHeaderObject) {
		TableColHeader header;

		String name = jsonHeaderObject.get(Constants.JSON_COL_NAME).getAsString();
		ColType colType = null;
		boolean lockCol = false;
		boolean hideCol = false;
		boolean ischeckbox = false;
		String pattern = null;
		boolean isprimarykey = false;
		boolean ismandatory = false;
		String isMandatoryNote = null ;
		JsonArray predefined_values = null;
		String note = null;
		String regex = null;
		boolean isDate = false;
		boolean isInclude = false;
		boolean isUnique = false;
		boolean isHide = false;
		boolean showDVHint = true;
		int colWidth = -1;
		int maxLength = -1;
		if (jsonHeaderObject.has(Constants.JSON_COL_IS_PK)
				&& jsonHeaderObject.get(Constants.JSON_COL_IS_PK).getAsString().equalsIgnoreCase("true")) {
			isprimarykey = true;
			lockCol = true;

		}
		if (jsonHeaderObject.has(Constants.JSON_COL_IS_TEXT)) {
			String type = jsonHeaderObject.get(Constants.JSON_COL_IS_TEXT).getAsString();

			if (type.equalsIgnoreCase("true")) {
				colType = ColType.TEXT;
			}
		}

		if (jsonHeaderObject.has(Constants.JSON_COL_PATTERN)) {
			pattern = jsonHeaderObject.get(Constants.JSON_COL_PATTERN).getAsString();
		}

		if (jsonHeaderObject.has(Constants.JSON_COL_LOCK)
				&& jsonHeaderObject.get(Constants.JSON_COL_LOCK).getAsString().equalsIgnoreCase("true")) {
			lockCol = true;
		}
		if (jsonHeaderObject.has(Constants.JSON_COL_HIDE)
				&& jsonHeaderObject.get(Constants.JSON_COL_HIDE).getAsString().equalsIgnoreCase("true")) {
			hideCol = true;
		}
		if (jsonHeaderObject.has(Constants.JSON_CHECKBOX)
				&& jsonHeaderObject.get(Constants.JSON_CHECKBOX).getAsString().equalsIgnoreCase("true")) {
			ischeckbox = true;

		}

		if (jsonHeaderObject.has(Constants.JSON_COL_IS_MANDATORY)) {
			String isMandatory = jsonHeaderObject.get(Constants.JSON_COL_IS_MANDATORY).getAsString();

			if (isMandatory.equalsIgnoreCase("true")) {
				//name = name;
				ismandatory = true;
				this.mandatoryMap.put(name, true);
				if(jsonHeaderObject.has(Constants.JSON_COL_MANDATORYNOTE)){
					isMandatoryNote = jsonHeaderObject.get(Constants.JSON_COL_MANDATORYNOTE).getAsString();
				}
				// this.displayname.add(name +"*",
				// jsonHeaderObject.get(Constants.JSON_COL_NAME));
			}
		}
		if (jsonHeaderObject.has(Constants.JSON_HEADER_DV)) {

			JsonArray _pickList = jsonHeaderObject.getAsJsonArray(Constants.JSON_HEADER_DV);
			this.pickList.put(name, _pickList.toString());
			if(jsonHeaderObject.has(Constants.JSON_PICKLIST_DEFAULT)){
				this.pickList_Default.put(name,jsonHeaderObject.get(Constants.JSON_PICKLIST_DEFAULT).getAsString());
			}

		}
		if (jsonHeaderObject.has(Constants.JSON_VIEW_SETTINGS)) {

			JsonArray settings = jsonHeaderObject.getAsJsonArray(Constants.JSON_VIEW_SETTINGS);
			this.viewSettings.put(name, settings.toString());

		}
		if (jsonHeaderObject.has(Constants.JSON_HEADER_NOTE)) {
			note = jsonHeaderObject.get(Constants.JSON_HEADER_NOTE).getAsString();

		}
		if (jsonHeaderObject.has(Constants.JSON_DISPLAY_NAME)) {
			// displayname.add(name,jsonHeaderObject.get(Constants.JSON_DISPLAY_NAME));
			//this.displayNameMap.put(name, jsonHeaderObject.get(Constants.JSON_DISPLAY_NAME).getAsString());
			String dName = jsonHeaderObject.get(Constants.JSON_DISPLAY_NAME).getAsString();
			//boolean val = this.displayNameMap.containsKey(dName);
			this.displayNameMap.put(name,dName);

		}
		if (jsonHeaderObject.has(Constants.JSON_HEADER_DV)) {
			predefined_values = jsonHeaderObject.get(Constants.JSON_HEADER_DV).getAsJsonArray();
		}
		if (jsonHeaderObject.has(Constants.JSON_COL_WIDTH)) {

			colWidth = jsonHeaderObject.get(Constants.JSON_COL_WIDTH).getAsInt();
		}

		if (jsonHeaderObject.has(Constants.JSON_COL_IS_DATE)) {
			isDate = jsonHeaderObject.get(Constants.JSON_COL_IS_DATE).getAsBoolean();

		}
		if (jsonHeaderObject.has(Constants.JSON_HEADER_INCLUDE)) {
			isInclude = jsonHeaderObject.get(Constants.JSON_HEADER_INCLUDE).getAsBoolean();

		}
		if (jsonHeaderObject.has(Constants.JSON_COL_IS_UNIQUE)) {
			isUnique = jsonHeaderObject.get(Constants.JSON_COL_IS_UNIQUE).getAsBoolean();

		}
		if (jsonHeaderObject.has(Constants.JSON_COL_INDEX_HIDE)) {
			isHide = jsonHeaderObject.get(Constants.JSON_COL_INDEX_HIDE).getAsBoolean();

		}
		if (jsonHeaderObject.has(Constants.JSON_COL_IS_REGEX)) {
			regex = jsonHeaderObject.get(Constants.JSON_COL_IS_REGEX).getAsString();

		}
		if(jsonHeaderObject.has(Constants.JSON_DV_HINT_SHOW)){
			showDVHint =jsonHeaderObject.get(Constants.JSON_DV_HINT_SHOW).getAsBoolean();
		}
		if(jsonHeaderObject.has(Constants.JSON_COL_MAX_LENGTH)){
			maxLength = jsonHeaderObject.get(Constants.JSON_COL_MAX_LENGTH).getAsInt();
		}
		//logger.info("My test message 12"+ismandatory+"::::"+isMandatoryNote);
		header = new TableColHeader(name, colType, lockCol, pattern, isprimarykey, ismandatory,isMandatoryNote, note, predefined_values,
				isDate,ischeckbox,isUnique,regex,isHide,isInclude,maxLength,showDVHint,colWidth);

		if (jsonHeaderObject.has(Constants.JSON_SUB_HEADERS)) {
			JsonArray subHeadersJson = jsonHeaderObject.get(Constants.JSON_SUB_HEADERS).getAsJsonArray();

			for (JsonElement jsonHeaderElement : subHeadersJson) {
				JsonObject subheaderJson = jsonHeaderElement.getAsJsonObject();
				header.addSubHeader(parseHeaderNode(subheaderJson));
			}
		}

		return header;
	}

	/**
	 * Standard Tree depth
	 */
	private void setDepth(int depth, TableColHeader node) { // preOrder
		// traversal

		node.setDepth(depth);
		if (depth > this.colHeaderDepth) {
			this.colHeaderDepth = depth;// depth of deepest node =tree depth
		}

		for (TableColHeader child : node.getSubHeaders()) {
			setDepth(depth + 1, child);
		}

	}

	/**
	 * Count No of leaves present in this node,if node is leaf ,number of leaves
	 * is 1
	 *
	 */
	private int countLeaves(TableColHeader node) { // postOrder traversal
		if (node.isLeaf()) {
			node.setNoOfLeaves(1);
			return 1;
		}
		int noOfLeaves = 0;
		for (TableColHeader subNode : node.getSubHeaders()) {
			noOfLeaves += countLeaves(subNode);
		}
		node.setNoOfLeaves(noOfLeaves);
		return noOfLeaves;
	}

	/**
	 * //If a node has no pattern type etc set take parent's pattern,type,lock
	 */

	private void trickeDownNodeProperties(TableColHeader.ColType parentColType, String parentPatternString,
										  boolean isParentLocked, TableColHeader node) {
		// preorder traversal
		if (node.getColType() == null)// if child has no colType take parent col
		// type applied
		{

			node.setColType(parentColType);
		}

		if (node.getPatternString() == null)// if no pattern set parent pattern
		{
			node.setPatternString(parentPatternString);
		}

		if (!node.isColLocked()) {// if lock present do nothing , if no lock and
			// parent locked lock child

			node.setColLocked(isParentLocked);
		}

		for (TableColHeader child : node.getSubHeaders()) {
			trickeDownNodeProperties(node.getColType(), node.getPatternString(), node.isColLocked(), child);
		}
	}

	/**
	 * Iterate over all value objects and detect column names
	 *
	 * @param valueArray
	 * @return
	 */
	private TableColHeader getColHeadersFromValues(JsonArray valueArray) {
		// no nested structure allowed in headerless JSON ,but using still
		// returning a Column header tree

		List<TableColHeader> headerList = new ArrayList<>();
		Set<String> columnNanmes = new HashSet<>();// using set here to detect
		// new columns as they are
		// encountered

		for (int i = 0; i < valueArray.size(); i++) {
			JsonObject jsonObject = valueArray.get(i).getAsJsonObject();

			for (Map.Entry<String, JsonElement> feilds : jsonObject.entrySet()) {
				String fieldName = feilds.getKey();
				if (columnNanmes.add(fieldName)) {
					// if first time field is encountered create a new
					// TableColHeader
					TableColHeader node = new TableColHeader(fieldName);
					node.setColIndex(columnNanmes.size() - 1);
					headerList.add(node);
				}
			}

		}
		TableColHeader root = new TableColHeader(null);
		root.setSubHeaders(headerList);
		countLeaves(root);
		setDepth(0, root);
		return root;
	}

	/**
	 *
	 * Sets the column Headers and apply ColumnProperties to Sheet Columns,merge
	 * cells //invoke with colIndex 0, root node
	 *
	 **/
	private void setColHeadersToSheet(WorkbookContainer container, int colIndex, TableColHeader node, Sheet sheet,int maxRow) {
		// preorder traversal
		try {
			int rowIndex = node.getDepth() - 1;


			//logger.info("displayname map fromjson:::::"+this.displayNameMap);
			if (node.getName() != null && rowIndex != -1)// ignore root node
			{
				ActionUtil.mergeCellRange(container, sheet, rowIndex, colIndex, rowIndex, colIndex + node.getNoOfLeaves() - 1,
						ActionConstants.MERGE_ACROSS);
				//	ActionUtil.setRangeBold(sheet, rowIndex, colIndex, rowIndex, colIndex, null);
				// columnIndex.put(node.getName(), colIndex);
				String cellValue = node.getName();

				if (this.displayNameMap.containsKey(cellValue)) {
					cellValue = this.displayNameMap.get(cellValue).toString();
					//cellValue = this.displayNameMap.get(cellValue).toString();
				}

				Cell cell = sheet.getCell(rowIndex, colIndex);
				//List<RichStringProperties> richStringProperties = EngineUtils1.setLink(cellValue, sheet.getWorkbook());
				ActionUtil.setCellValue(cell, cellValue, null,EngineConstants.DEFAULT_LOCALE,'.',',',null, null);
				this.colIndex.put(cellValue, colIndex);
				//logger.info("My test message 1"+node.getisMandatory()+"::::"+node.getisMandatoryNote());
				if (node.getisMandatory()) {
					//logger.info("My test message 1");
					String note = node.getisMandatoryNote();
					Annotation annotation = new Annotation();

					cell = sheet.getCell(rowIndex, colIndex);

					cell.setAnnotation(annotation);
					//	logger.info("My test message 2"+note);
					if(note != null ){
						annotation.setContent(note);
					}else{
						annotation.setContent(LocaleMsg.getMsg("JsonParser.MandatoryField"));// NO I18N
					}

				}
				if(node.getisCheckbox()){
					List<DataRange> listOfRanges = new ArrayList();
					this.checkBoxMap.add(colIndex);
					listOfRanges.add(new DataRange(sheet.getAssociatedName(), 1, colIndex, maxRow, colIndex));

					ActionUtil.addCheckbox(sheet.getWorkbook(), listOfRanges);
				}
				if(node.getMaxLength() != -1){
					boolean showDVHint = node.getShowHint();
					int maxLength = node.getMaxLength();
					String[] args = new String[] {maxLength+""};
					String msg = LocaleMsg.getDynamicMsg("JsonParser.Maxlength",args);//"Enter text with length less than or equal to "+maxLength;//No I18N
					String msg1 =LocaleMsg.getDynamicMsg("JsonParser.MaxlengthError",args);//"The entered text value length is greater than " +maxLength;//No I18N
					String _dvJson = "{\"cr_nu\":4,\"cr_ty\":\"TEXT_LENGTH\",\"cv_hi\":{\"is_dis\":"+showDVHint+",\"v\":\""+msg+"\"},\"cv_err\":{\"is_dis\":true,\"v\":\""+msg1+"\",\"msgt\":\"STOP\"},\"cv_abc\":true,\"cv_ldt\":\"NO\",\"val1\":"+maxLength+",\"asn\":\"0#\"}";//No I18N
					List<DataRange> listOfRanges = new ArrayList();
					JSONObjectWrapper dvJson = new JSONObjectWrapper(_dvJson);
					listOfRanges.add(new DataRange(sheet.getAssociatedName(), 1, colIndex,maxRow, colIndex));
					DataValidationUtils.setValidation(sheet.getWorkbook(), listOfRanges, dvJson);

				}

				/**
				 * setting pattern,type etc only for leaves , pattern ,type from
				 * parents nodes has already been set in leaves in trickle...()
				 * methods
				 **/
				boolean isDate = node.getIsDate();
				if (node.isLeaf())// for leaf nodes must do vertical merge and
				// update col Index

				{
					ActionUtil.mergeCellRange(container, sheet, rowIndex, colIndex, (this.colHeaderDepth - 1), colIndex,
							ActionConstants.MERGE_DOWN);
					node.setColIndex(colIndex);
					//	logger.info("Node Is primary key ->"+node.getPrimaryKey());
//					if (node.getPrimaryKey() == true) {
//						RangeUtil.protectRange(sheet, 0, colIndex, Utility.MAXNUMOFROWS - 1, colIndex,
//								Protection.createPermissionMap(new HashSet<>(java.util.Arrays.asList("-9999")), null,
//										null, null, null),
//								true);
//
//						// ActionUtil.setColumnsWidth(sheet, colIndex, colIndex,
//						// "0.0111in", true, null);
//					}
					if (node.getColType() == ColType.TEXT) {
						RangeUtil.setCHPattern(sheet, colIndex, colIndex,
								DefaultPatternUtil.getDefaultPattern(sheet.getWorkbook(), Type.STRING
								));
					}
					if(node.isHide) {
						List<DataRange> listOfRanges = new ArrayList();

						listOfRanges.add(new DataRange(sheet.getAssociatedName(), 1, colIndex, maxRow, colIndex));
						ActionUtil.hideColumns(sheet, listOfRanges);
					}
					//logger.info("Node Is colLocked key ->"+node.isColLocked());
					if (node.isColLocked()) {
						RangeUtil.protectRange(sheet, 0, colIndex, Utility.MAXNUMOFROWS -1 , colIndex,
								Protection.createPermissionMap(new HashSet<>(java.util.Arrays.asList("-9999")), null, null, null, null, null, null),
								true,true);
					}

					if (node.getPatternString() != null) {
						ZSPattern colPattern = StyleActionUtil.getPatternFormat(sheet.getWorkbook(), "CUSTOM", // No I18N
								EngineConstants.DEFAULT_LOCALE, EngineConstants.DEFAULT_LOCALE, node.getPatternString(),

								false);
						RangeUtil.setPattern(sheet, 1, colIndex, maxRow, colIndex, colPattern);
						//RangeUtil.setCHPattern(sheet, colIndex, colIndex, colPattern);
						if(isDate){
//							JsonReader reader = new JsonReader(new StringReader(node.getPatternString()));
//							reader.setLenient(true);

							this.dateCol.put(String.valueOf(colIndex),node.getPatternString());
						}

					}
					if(node.getColWidth() != -1){
						colWidthMap.put(colIndex,node.getColWidth());
					}

					String _note = node.getNote();

					if (_note != null && !_note.isEmpty()) {
						cell = sheet.getCell(rowIndex, colIndex);

						Annotation annotation = new Annotation();
						cell.setAnnotation(annotation);
						annotation.setContent(_note);
					}
					JsonArray _values = node.getPredefined();
					if (_values != null) {
						String testVal = "";
						for (JsonElement key : _values) {
							testVal += key.toString() + ",";
						}
						// testVal = testVal.replaceAll(",","\\\n");
						String output = testVal.replaceAll(",", "\\\\\\\\n");
						output = output.replace("[", "").replace("]", "");
						output = output.replace("\"", "");
						JSONObjectWrapper testJSON = new JSONObjectWrapper();
						boolean showDVHint = node.getShowHint();
						testJSON.put("cr_nu", -1);
						testJSON.put("cr_ty", "LIST");
						testJSON.put("cv_hi",
								new JSONObjectWrapper().put("is_dis", showDVHint).put("v", LocaleMsg.getMsg("DataVal.LIST")));//Enter an item from the given list."));
						testJSON.put("cv_err",
								new JSONObjectWrapper().put("is_dis", true)
										.put("v",LocaleMsg.getMsg("DataVal.StopErrorMsg"))
										.put("msgt", "STOP"));
						testJSON.put("cv_abc", true);
						testJSON.put("cv_ldt", "UNSORTED");
						testJSON.put("val1", output);
						testJSON.put("asn", "0#");

						String _dvJson = "{\"cr_nu\":-1,\"cr_ty\":\"LIST\",\"cv_hi\":{\"is_dis\":"+showDVHint+",\"v\":\""+ LocaleMsg.getMsg("DataVal.LIST")+"\"},\"cv_err\":{\"is_dis\":true,\"v\":\""+LocaleMsg.getMsg("DataVal.StopErrorMsg")+"\",\"msgt\":\"STOP\"},\"cv_abc\":true,\"cv_ldt\":\"UNSORTED\",\"val1\":\""	+ Utility.getEncodedString(output) + "\",\"asn\":\"0#\"}";// NO I18N
						List<DataRange> listOfRanges = new ArrayList();
						JSONObjectWrapper dvJson = new JSONObjectWrapper(_dvJson);
						listOfRanges.add(new DataRange(sheet.getAssociatedName(), 1, colIndex, maxRow, colIndex));
						//DataValidationUtils.setValidation(sheet.getWorkbook(), listOfRanges, dvJson);
					}

					if (isDate) {
						ContentValidation cv = new ContentValidation(sheet, 1, colIndex, ConditionType.DATE, ConditionalFormatOperator.IS_VALID_DATE, null);
						cv.setErrorMessage(new DVErrorMessage(ErrorCode.MsgType.STOP, true, "isDate",LocaleMsg.getMsg("JsonParser.InvalidDate")));// No I18N

						RangeUtil.setContentValidation(sheet, Arrays.asList(new DataRange(sheet.getAssociatedName(), 1, colIndex, maxRow, colIndex)), cv);
					}
					if (node.getisUnique()) {
						String name = sheet.getName();
						DataRange range = new DataRange(sheet.getAssociatedName(), 1, colIndex,maxRow,colIndex);
						String ran ;

						ran = (CellUtil.getCellReference(sheet.getName(), 1,colIndex, true, true, true) + ":" + CellUtil.getCellReference(maxRow, colIndex, true, true)).substring(1);
						String cell1 = sheet.getCell(1,colIndex).getCellRef();

						String formula = "COUNTIF("+ran+";"+cell1 +")=1" ;//NoI18N "COUNT(UNIQUE("+ran+"))=COUNT("+ran+")";//No I18N
						ContentValidation cv = new ContentValidation(sheet, 1, colIndex,
								ConditionType.FORMULA, -1, formula);



						DVErrorMessage dv = new DVErrorMessage(ErrorCode.MsgType.STOP, true, "Unique",LocaleMsg.getMsg("CFormat.Condition.Duplicate"));// No I18N
						cv.setErrorMessage(dv);
						cv.setIsAllowEmptyCell(true);
						List<DataRange> listOfRanges = new ArrayList();
						listOfRanges.add(range);
						RangeUtil.setContentValidation(sheet, listOfRanges, cv);
					}
					if (node.getRegex() != null) {
						String regex = node.getRegex();
						boolean showDVHint = node.getShowHint();
						String name = sheet.getName();
						Range range = new Range(sheet, 0, colIndex,maxRow,colIndex);
						String ran  = range.toAbsoluteA1String();
						//logger.info("My range print heree:::"+ran);
						String _dvJson = "{\"cr_nu\":35,\"cr_ty\":\"TEXT\",\"cv_hi\":{\"is_dis\":"+showDVHint+",\"v\":\""+LocaleMsg.getDynamicMsg("DataVal.TEXT35",new String[]{regex})+"\"},\"cv_err\":{\"is_dis\":true,\"v\":\""+LocaleMsg.getMsg("DataVal.StopErrorMsg")+"\",\"msgt\":\"STOP\"},\"cv_abc\":true,\"cv_ldt\":\"NO\",\"val1\":\""+regex+"\",\"asn\":\"0#\"}";//No I18N
						List<DataRange> listOfRanges = new ArrayList();
						JSONObjectWrapper dvJson = new JSONObjectWrapper(_dvJson);
						listOfRanges.add(new DataRange(sheet.getAssociatedName(), 1, colIndex,maxRow, colIndex));
						DataValidationUtils.setValidation(sheet.getWorkbook(), listOfRanges, dvJson);


					}
//				if (node.getisMandatory()) {
//					String name = sheet.getName();
//					Range range = new Range(sheet, 0, colIndex,10000,colIndex);
//					String ran  = range.toAbsoluteA1String();
//					logger.info("My range print heree:::"+ran);
//					ran = (CellUtil.getCellReference(sheet.getName(), 0,colIndex, true, true, true) + ":" + CellUtil.getCellReference(10000, colIndex, true, true)).substring(1);
//					String cell = sheet.getCell(0,colIndex).getCellRef();
//					String formula = "NOT(ISBLANK("+cell+"))" ;//NoI18N "COUNT(UNIQUE("+ran+"))=COUNT("+ran+")";//No I18N
//					logger.info("My range print heree:::"+formula);
//					ContentValidation cv = new ContentValidation(sheet, 0, colIndex,
//							ConditionType.FORMULA, -1, formula);
//
//
//					DVErrorMessage dv = new DVErrorMessage(ErrorCode.MsgType.STOP, true, "Mandatory","This field cannot be empty");// No I18N
//					cv.setErrorMessage(dv);
//					//cv.setIsAllowEmptyCell(true);
//					List<Range> listOfRanges = new ArrayList();
//					listOfRanges.add(range);
//					RangeUtil.setContentValidation(listOfRanges, cv);
//				}
				}
			}

			for (TableColHeader childNode : node.getSubHeaders()) {
				setColHeadersToSheet(container, colIndex, childNode, sheet, maxRow);
				colIndex += childNode.getNoOfLeaves();
			}

		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in JSON Import", e);

		}

	}

	private void copyValuesToSheet(Sheet sheet, JsonArray values) {

		if (isArrayOfArrays(values)) {
			// if value is just an array iterate through and paste to cell
			setArrayTypeValues(values, sheet);

		} else {
			// if values is an array of jsonObjects
			// set the values to the sheet using header and value trees
			for (int i = 0; i < noOfValueRows; i++) {
				setJsonObjectTypeVals(columnHeaders, sheet, i + colHeaderDepth, values.get(i).getAsJsonObject());
			}
		}

	}

	// used when value array is of json object rows
	private void setJsonObjectTypeVals(TableColHeader headerNode, Sheet sheet, int rowIndex,
									   JsonElement jsonValueNode) {
		try {
			if (jsonValueNode == null) {
				return;
			}

			String colName = headerNode.getName();

			// handle root specially ,go down each node in root

			if (colName == null && headerNode.getDepth() == 0) {
				for (TableColHeader childNode : headerNode.getSubHeaders()) {
					JsonObject jsonValueObject = jsonValueNode.getAsJsonObject();// Definitely
					// Json
					// Object,not
					// JsonPrimitives

					setJsonObjectTypeVals(childNode, sheet, rowIndex, jsonValueObject.get(childNode.getName()));
					// Thread.dumpStack();
				}

				return;
			}

			if (headerNode.isLeaf())// need to set values for leaves alone,else go
			// down further

			{
				// if not null alone paste
				if (jsonValueNode != null && !jsonValueNode.isJsonNull()) { // particular
					// column
					// value
					// is
					// missing
					// or
					// given
					// as
					// null
					// type
					// in
					// json
					Cell cell = sheet.getCell(rowIndex, headerNode.getColIndex());
					String cellValue = jsonValueNode.getAsString();
					// Thread.dumpStack();

					//List<RichStringProperties> richStringProperties = EngineUtils1.setLink(cellValue, sheet.getWorkbook());

					ActionUtil.setCellValue(cell, cellValue, null,EngineConstants.DEFAULT_LOCALE,'.',',',null, null);
				}

			}

			else { // header not a leaf so traverse down
				for (TableColHeader childNode : headerNode.getSubHeaders()) {
					JsonObject nonleaveValueNode = jsonValueNode.getAsJsonObject();
					setJsonObjectTypeVals(childNode, sheet, rowIndex, nonleaveValueNode.get(childNode.getName()));
				}

			}
		}catch(Exception e) {
			logger.log(Level.WARNING, "Problem in JSON Import", e);
		}

	}

	private void constructIndexMap(Sheet sheet, int pkColIndex, JsonArray errorJson) {
		if (errorJson != null) {
			Set rowIndexFind = new HashSet();
			Set columnIndexFind = new HashSet();
			for (int i = 0; i < errorJson.size(); i++) {
				JsonElement element = errorJson.get(i);
				String row;
				String column;
				if (element.isJsonObject()) {
					JsonObject _object = element.getAsJsonObject();
					if (_object.has("uniquekey")) {
						row = _object.get("uniquekey").getAsString();
						rowIndexFind.add(row);
						column = _object.get("columnname").getAsString();
						columnIndexFind.add(column);
					} else {
						Set<Entry<String, JsonElement>> entrySet = _object.entrySet();
						for (Map.Entry<String, JsonElement> entry : entrySet) {
							JsonArray jsonArray = entry.getValue().getAsJsonArray();
							row = entry.getKey();
							rowIndexFind.add(row);
							for (int j = 0; j < jsonArray.size(); j++) {
								JsonArray _jsonArray = jsonArray.get(j).getAsJsonArray();
								column = _jsonArray.get(0).getAsString();
								columnIndexFind.add(column);
							}
						}
					}
				} else {
					JsonArray jsonArray = element.getAsJsonArray();
					row = jsonArray.get(0).getAsString();
					rowIndexFind.add(row);
					if(jsonArray.size()>1) {
						column = jsonArray.get(1).getAsString();
						columnIndexFind.add(column);
					}


				}

			}
			this.rowIndex.putAll(findAList(sheet, rowIndexFind, 1, pkColIndex, "rowindexsearch"));// No I18N

			// findAList(sheet,columnIndexFind,0,0,"columnindexsearch");//No
			// I18N

		}

	}

	private void constructIndexMapNew(Sheet sheet,int pkColIndex,JsonArray
			errorJson)
	{
		if(errorJson != null){
			Set rowIndexFind = new HashSet<>();
			Set columnIndexFind = new HashSet<>();
			for (int i = 0; i < errorJson.size(); i++) {
				JsonObject element = errorJson.get(i).getAsJsonObject();
				Set<Entry<String, JsonElement>> entrySet = element.entrySet();
				for(Map.Entry<String,JsonElement> entry : entrySet){
//	 System.out.println("My print here:::"+entry.getKey()+":::::"+errorJson);
					//logger.info("My print here:::"+entry.getKey()+":::::"+errorJson);
					String row = entry.getKey();
					rowIndexFind.add(row.toString());
					//logger.info("My print fdfdfd here:::"+element.get(row));
					JsonArray _element = element.get(row).getAsJsonArray();


					columnIndexFind.add(_element.get(0).getAsString());
				}
			}
			this.rowIndex.putAll(findAList(sheet,rowIndexFind,0,pkColIndex,"rowindexsearch"));//No I18N
			this.colIndex.putAll(findAList(sheet,rowIndexFind,0,0,"columnindexsearch"));//No I18N


		}


	}
//	private void setPickList(WorkbookContainer container) {
//		try {
//
//			Workbook workbook = container.getWorkbook(null);
//			Sheet sheet = workbook.getSheet(0);
//			if (this.pickList != null && !this.pickList.isEmpty()) {
//				if (this.colIndex != null) {
//					this.getAllColIndex(container);
//				}
//				Iterator itr = this.pickList.keys();
//				// Set set =this.pickList;
//
//				// this.pickList.keys()
//
//				while (itr.hasNext()) {
//					JSONArray pickListJson = new JSONArray();
//					String key = itr.next().toString();
//					String _pickList = this.pickList.getString(key);
//					List<DataRange> listOfDataRanges = new ArrayList<>();
//
//					JSONArray pL = new JSONArray(_pickList);
//					JsonArray pl = new JsonParser().parse(pL.toString()).getAsJsonArray();
//					JsonElement element = pl.get(0);
//
//					if (element.isJsonArray()) {
//						for (int i = 0; i	 < pL.length(); i++) {
//							JSONObject _pL = new JSONObject();
//							JSONArray pL1 = (JSONArray) pL.get(i);
//							String val = pL1.getString(0);
//							String id = pL1.getString(1);
//							String val1 = (URLEncoder.encode(val, "UTF-8").replace("+", "%20"));//No I18N
//							_pL.put("dv", val1 );
//							_pL.put("s", "-");
//							_pL.put("id", id);
//							pickListJson.put(_pL);
//						}
//					}else{
//						for (int i = 0; i	 < pL.length(); i++) {
//							JSONObject _pL = new JSONObject();
//							String val = pL.getString(i);
//							String val1 = (URLEncoder.encode(val, "UTF-8").replace("+", "%20"));//No I18N
//							_pL.put("dv", val1 );
//							_pL.put("s", "-");
//							//_pL.put("id", i);
//							pickListJson.put(_pL);
//						}
//					}
//					// "dvJSON:
//					// {\"cr_nu\":-1,\"cr_ty\":\"LIST\",\"cv_hi\":{\"is_dis\":true,\"v\":\"Enter%20an%20item%20from%20the%20given%20list.\"},\"cv_err\":{\"is_dis\":true,\"v\":\"The
//					// entered value violates the data validation rules set on
//					// the
//					// cell.\",\"msgt\":\"STOP\"},\"cv_abc\":true,\"cv_ldt\":\"UNSORTED\",\"val1\":"+_pickList+",\"asn\":\"0#\"}");//No
//					// I18N
//
//					// if(this.columnIndex != null &&
//					// !this.columnIndex.containsKey(key)){
//					// set.add(key);
//					// }else{
//					// List<Range> rangeList = new ArrayList<Range>();
//					//
//					// Range range = new
//					// Range(sheet,0,this.columnIndex.get(key),Utility.MAXNUMOFROWS,this.columnIndex.get(key));
//					// rangeList.add(range);
//					int id = ActionUtil.createPickList(workbook, null, pickListJson, -1, -1);
//					int col = -1;
//					if(this.colIndex.containsKey(key)){
//						col = this.colIndex.get(key);
//					}else {
//						if(this.displayNameMap.containsKey(key)){
//							String val = (String) this.displayNameMap.get(key);
//							if(this.colIndex.containsKey(val)){
//								col = this.colIndex.get(val);
//							}
//						}
//					}
//					DataRange range = new DataRange(sheet.getAssociatedName(), 1, col,
//							sheet.getUsedRowIndex() + 1000, col);
//					listOfDataRanges.add(range);
//					picklistCol.put(col+"", true);
//					ActionUtil.applyPicklist(workbook, listOfDataRanges, id);
//					// DataValidationUtils.setValidation(container.getDocId(),rangeList
//					// ,pickListJson);
//					// }
//
//				}
//			}
//
//		} catch (Exception e) {
//			logger.log(Level.WARNING, "Problem in JSON Import", e);
//		}
//
//	}

	private void setPickList(WorkbookContainer container,int maxRow) {
		try {

			Workbook workbook = container.getWorkbook(null);
			Sheet sheet = workbook.getSheetByAssociatedName("0#");
			String _picklistDefault = "";
			if (this.pickList != null && !this.pickList.isEmpty()) {
				if (this.colIndex == null) {
					this.getAllColIndex(container);
				}
				Iterator itr = this.pickList.keys();
				// Set set =this.pickList;

				// this.pickList.keys()

				while (itr.hasNext()) {
					JSONArrayWrapper pickListJson = new JSONArrayWrapper();
					String key = itr.next().toString();
					String _pickList = this.pickList.getString(key);
					if(pickList_Default.has(key)){
						_picklistDefault = pickList_Default.getString(key);
					}else{
						_picklistDefault = "";
					}

					List<DataRange> listOfDataRanges = new ArrayList<>();

					JSONArrayWrapper pL = new JSONArrayWrapper(_pickList);
					JsonArray pl = new JsonParser().parse(pL.toString()).getAsJsonArray();
					JsonElement element = pl.get(0);
					//for (int i = 0; i	 < pL.length(); i++) {
//						JSONObject _pL = new JSONObject();
//						String val  = pL.getString(i);
//						String val1 = (URLEncoder.encode(val, "UTF-8").replace("+", "%20"));//No I18N
//						_pL.put("dv", val1 );
//						_pL.put("s", "-");
//						_pL.put("id", i);
//						pickListJson.put(_pL);
//					}
					if (element.isJsonArray()) {
						for (int i = 0; i	 < pL.length(); i++) {
							JSONObjectWrapper _pL = new JSONObjectWrapper();
							JsonArray pL1 = pl.get(i).getAsJsonArray();
							String val = pL1.size() >1 ?pL1.get(1).getAsString() :_picklistDefault;
							String bgColor = pL1.size() >2? pL1.get(2).getAsString() :null;
							String textColor = pL1.size() >3 ?pL1.get(3).getAsString():null;
							String id = pL1.get(0).getAsString();
							//String val1 = (URLEncoder.encode(val, "UTF-8").replace("+", "%20"));//No I18N
							_pL.put(JSONConstants.DISPLAY_VALUE, val );
							//_pL.put("s", "-");
							_pL.put(JSONConstants.ID, id);
							if(bgColor != null) {
								JSONObjectWrapper bg = new JSONObjectWrapper();
										bg.put("hexColor",bgColor);
								_pL.put(JSONConstants.BACKGROUNDCOLOR,bg);
							}
							if (textColor != null) {
								JSONObjectWrapper txt = new JSONObjectWrapper();
								txt.put("hexColor",textColor);
								_pL.put(JSONConstants.TEXTCOLOR,txt);
							}
							pickListJson.put(_pL);
						}
					}else{
						for (int i = 0; i	 < pL.length(); i++) {
							JSONObjectWrapper _pL = new JSONObjectWrapper();
							String val = pL.getString(i);
							_pL.put("dv", val);
							pickListJson.put(_pL);
						}
					}
					int id = PicklistUtil.createPicklistForJsonTableParser(workbook,pickListJson, false);
					int col = -1;
					if(this.colIndex.containsKey(key)){
						col = this.colIndex.get(key);
					}else {
						if(this.displayNameMap.containsKey(key)){
							String val = (String) this.displayNameMap.get(key);
							if(this.colIndex.containsKey(val)){
								col = this.colIndex.get(val);

							}
						}
					}
					picklistCol.put(col+"", true);
					DataRange range = new DataRange(sheet.getAssociatedName(), 1, col,
							maxRow, col);
					listOfDataRanges.add(range);

					PicklistUtil.applyPicklist(workbook, listOfDataRanges, id);
					// DataValidationUtils.setValidation(container.getDocId(),rangeList
					// ,pickListJson);
					// }

				}
			}
		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in JSON Import", e);
		}

	}

	private void setRowError(WorkbookContainer container,JsonArray errors){
		try {
			if (errors != null && !errors.isJsonNull()) {
				Workbook workbook = container.getWorkbook(null);
				Sheet sheet = workbook.getSheetByAssociatedName("0#");
				String docId = container.getDocId();
				Set set = new HashSet();

				Map apicolindex = this.getAllColIndexAPIname(container);

				for(int i=0;i<errors.size();i++){
					JsonArray ele = errors.get(i).getAsJsonArray();
					String id = ele.get(0).getAsString();
					set.add(id);
				}
				findAList(sheet,set,0,0,"rowindexsearch");//No I18N

				for (int i = 0; i < errors.size(); i++) {
					JsonArray ele = errors.get(i).getAsJsonArray();
					String id = ele.get(0).getAsString();
					String colname = ele.get(1).getAsString();
					String msg = ele.get(2).getAsString();
					//ActionUtil.
					int row = (int) this.rowIndex.get(id);

					List<DataRange> listOfDataRanges = new ArrayList<>();
					DataRange dataRanges = new DataRange(sheet.getAssociatedName(), row, 0, row, sheet.getUsedColumnIndex());
					listOfDataRanges.add(dataRanges);
					ZSColor bgColor = ZSColor.getInstance("#FCE6A4");
					StyleActionUtil.setRangeColor(workbook,listOfDataRanges, bgColor);
					int col = (int) apicolindex.get(colname);
					Cell cell =	 sheet.getCell(row,col);
					Annotation annotation = new Annotation();
					cell.setAnnotation(annotation);
					annotation.setContent(msg);
					if (RedisHelper.hexists(redisKey + docId + ":save", id) ){
						String _value = RedisHelper.hget(redisKey + docId + ":save", id);// NO I18N
						RedisHelper.hset(redisKey + docId + ":edit", id,_value);// NO I18N
					}

				}
			}
		}catch (Exception e){
			logger.log(Level.WARNING, "Problem in JSON Import", e);
		}
	}


	// private void setTheme(){
	// if(!this.viewSettings.isEmpty()){
	// String theme = this.viewSettings.get("theme").toString();
	// }
	// }
	private JsonObject decideErroDenotationMethod(WorkbookContainer container, JsonArray errorJson,boolean setCF,boolean iserrorAppend,boolean isadded) {

		if(errorJson != null && errorJson.size() > 0){
			JsonElement element = errorJson.get(0);

			logger.info("Inside decision ::::"+errorJson);
			if (element.isJsonObject()) {
				JsonObject _object = element.getAsJsonObject();
				if (!_object.has("uniquekey")) {
					setErrorDenotationNew(container, errorJson,setCF,iserrorAppend,isadded);
				} else {
					return setErrorDenotation(container, errorJson,setCF,iserrorAppend,isadded);
				}
			} else {
				return setErrorDenotation(container, errorJson,setCF,iserrorAppend,isadded);
			}
		}
		return new JsonObject();
	}

	private JsonObject setErrorDenotation(WorkbookContainer container, JsonArray errorJson,boolean setCF,boolean iserrorappend,boolean isadded) {
		JsonObject errorJsonInDB = new JsonObject();
		HashMap KeyMap = new HashMap();
		String docId = container.getDocId();
//		String cellIndex = null;

		try {
			Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");
//			if (this.displayNameMap == null || this.displayNameMap.isEmpty()) {
//				this.displayNameMap = this.disthis.getDisplayName(container);
//			}

//			if (this.rowIndex == null || this.rowIndex.isEmpty()) {

			constructIndexMap(sheet, 0, errorJson);

//			}

			if (this.colIndex == null || this.colIndex.isEmpty()) {
				this.getAllColIndex(container);
				logger.info("col name map 123:::"+this.colIndex);
			}
			if(this.errorJsonArray != null){
				KeyMap = this.constructKeyMAp();
				logger.info("row name map :::"+KeyMap);
			}

			for (int i = 0; i < errorJson.size(); i++) {
				JsonElement element = errorJson.get(i);
				int row = 0;
				int column = 0;
				String errorType = "error";// No I18N
				String msg = "";
				if (element.isJsonObject()) {
					JsonObject _object = element.getAsJsonObject();
					String key =_object.get("uniquekey").getAsString();
					logger.info("testinggg print here::::"+key);
					row = this.rowIndex.containsKey(key) ?(int) this.rowIndex.get(key) :-1;
					String colName = _object.get("columnname").getAsString();
					if (this.displayNameMap.containsKey(colName)) {
						colName = this.displayNameMap.get(colName).toString();
					}
					column = (int) this.colIndex.get(colName);
					if (_object.has("type")) {
						errorType = _object.get("type").getAsString();
					}
					msg = _object.get("message").getAsString();

				} else {
					JsonArray jsonArray = element.getAsJsonArray();
					String temp = jsonArray.get(0).getAsString();
					row = (int) this.rowIndex.get(temp);
					if (jsonArray.size() > 1) {
						String temp1 = jsonArray.get(1).getAsString();
						column = this.colIndex.get(temp1);
						if (this.displayNameMap.containsKey(temp1)) {
							temp1 = this.displayNameMap.get(temp1).toString();
						}


						msg = jsonArray.get(2).getAsString();
						if (jsonArray.size() >= 4) {
							errorType = jsonArray.get(3).getAsString();
						}
					}
				}
				Cell cell1 = sheet.getCell(row, 0);
				if (RedisHelper.hexists(redisKey + docId + ":save_modified", cell1.getContent())) {
					String _value = RedisHelper.hget(redisKey + docId + ":save_modified", cell1.getContent());// NO I18N
					logger.info("_value::::" + _value);
					RedisHelper.hset(redisKey + docId + ":edit_modified", cell1.getContent(),_value);// NO I18N
				}


//
				logger.info("is added print here:::"+isadded);
				if(isadded) {

					logger.info("is added print here1:::"+cell1.getContent());
//					ActionUtil.setCellValue(cell1, "");
					try {
						logger.info("is added print here2:::"+RedisHelper.hexists(redisKey + docId + ":save", cell1.getContent()));
						if (RedisHelper.hexists(redisKey + docId + ":save", cell1.getContent())) {
							String _value = RedisHelper.hget(redisKey + docId + ":save", cell1.getContent());// NO I18N
							logger.info("_value::::" + _value);
							RedisHelper.hset(redisKey + docId + ":edit", cell1.getContent(),_value);// NO I18N
						}
					}catch (Exception e){
						logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e);
					}
				}
				Cell cell  = sheet.getCell(row, column);
				String sheetname = sheet.getName();
				String cellId = cell.toString();
				String[] _cellid = cellId.split(":");
				String range = sheetname + "." + _cellid[0];

				if(setCF){

					//{"rules":[{"cr_ty":"FORMULA","cstyle":{"bgColor":{"hexColor":"#F6D9BA"},"bold":0,"italic":0,"underline":"none","strike":0},"cond":-1,"cval":"IF(support.D3%3Dsupport.D3)"}]}
					JSONObjectWrapper cfJson_info = new JSONObjectWrapper(
							"{\"cr_ty\":\"FORMULA\",\"cstyle\":{\"bgColor\":{\"hexColor\":\"#FCFACD\"},\"bold\":0,\"italic\":0,\"underline\":\"none\",\"strike\":0},\"cond\":-1,\"cval\":\"IF("+_cellid[0]+"="+_cellid[0]+")\"}]}");// No I18N

					JSONObjectWrapper cfJson_error = new JSONObjectWrapper(
							"{\"cr_ty\":\"FORMULA\",\"cstyle\":{\"bgColor\":{\"hexColor\":\"#FBD1CF\"},\"bold\":0,\"italic\":0,\"underline\":\"none\",\"strike\":0},\"cond\":-1,\"cval\":\"IF("+_cellid[0]+"="+_cellid[0]+")\"}]}");// No I18N
					JSONObjectWrapper cfJson_warning = new JSONObjectWrapper(
							"{\"cr_ty\":\"FORMULA\",\"cstyle\":{\"bgColor\":{\"hexColor\":\"#FDE7CC\"},\"bold\":0,\"italic\":0,\"underline\":\"none\",\"strike\":0},\"cond\":-1,\"cval\":\"IF("+_cellid[0]+"="+_cellid[0]+")\"}]}");// No I18N

					//	"{\r\n  \"cond1\": -1,\r\n  \"cr_ty1\": \"FORMULA\",\r\n  \"cval1\": \"if(" + _cellid[0] + "="+ _cellid[0]+ ")\",\r\n  \"cstyle1\": \" #FBD1CF~none~false~false~none~false\",\r\n  \"noofrules\": 1\r\n  }");// No I18N
					JSONObjectWrapper cfJson = new JSONObjectWrapper();
					JSONArrayWrapper array = new JSONArrayWrapper();
					if ("info".equalsIgnoreCase(errorType)) {

						array.put(cfJson_info);

					} else if ("error".equalsIgnoreCase(errorType)) {
						array.put(cfJson_error);
					} else if ("warning".equalsIgnoreCase(errorType)) {
						array.put(cfJson_warning);
					}
					cfJson.put("rules", array);
					if(this.cellIndex_sR == -1){
						this.cellIndex_sR = cell.getRowIndex();
						this.cellIndex_sC = cell.getColumnIndex();
					}

					DataRange applyRange = new DataRange(sheet.getAssociatedName(), cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex());
					ConditionFormatUtils.addConditionalStyles(sheet, cfJson, Arrays.asList(applyRange),
							ActionConstants.CONDITIONAL_FORMAT_APPLY);
					Annotation annotation = new Annotation();
					cell.setAnnotation(annotation);
					annotation.setContent(msg);



				}else{
					JsonArray list = new JsonArray();

					if (errorJsonInDB.has(errorType)) {
						list = errorJsonInDB.get(errorType).getAsJsonArray();

					}
					JsonArray _list = new JsonArray();

					_list.add(range);
					_list.add(msg);
					_list.add(row);
					list.add(_list);
					errorJsonInDB.add(errorType, list);
				}

				// if(iserrorappend){
				// 	if(this.errorJsonArray != null){

				// 	}
				// }

			}
			if(iserrorappend){

				this.errorJsonArray.addAll(errorJson);

			}



			return errorJsonInDB;
		} catch (Exception e) {
			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e);
		}
		return errorJsonInDB;
	}
//	private JsonObject constructErrorResponse(WorkbookContainer container,JsonArray errorJson){
//		JsonObject response = this.decideErroDenotationMethod(container, errorJson, false,false,false);
//		return response;
//
//	}

//	public  JsonObject readErrorJsonFromDFS(WorkbookContainer container,boolean isResponse) throws Exception {
//		InputStream stream = null;
//		 BufferedReader reader = null;
//		 String errorStr = "";
//		 String rId = container.getResourceId();
//
//		 Long resourceId = container.getFragmentId(FileName.ERRORJSON, false, null);
//		 String docID = container.getDocId();
//
//		try {
//			stream = container.getReadStream(resourceId,FileName.ERRORJSON , FileExtn.JSON, null);
//			reader = new BufferedReader(new InputStreamReader(stream));
//           String str = null;
//           while ((str = reader.readLine()) != null) {
//               errorStr += str;
//           }
//
//           this.errorJsonArray = new JsonParser().parse(errorStr).getAsJsonArray();
//           if(isResponse){
//	          // constructErrorResponse(container, this.errorJsonArray);
//	           return  constructErrorResponse(container, this.errorJsonArray);
//           }
//           return new JsonObject();
//
//       } catch (Exception e) {
//           //e.printStackTrace();
//       	logger.log(Level.WARNING, "ERROR RESOURCE_ID: " + container.getResourceKey() + " Error while loading ErrorrJson : ", e);
//       }
//		finally{
//			 stream.close();
//	           reader.close();
//		}
//		return null;
//	}
//	private void writeErrorJsoninDFS(WorkbookContainer container) throws Exception {
//
//			Map writeInfo = null;
//			PrintWriter writer = null;
//			try
//			{
//			Long resourceId = container.getFragmentId(FileName.ERRORJSON, true, null);
//			String docID = container.getDocId();
//				writeInfo = container.getWriteInfo(resourceId, FileName.ERRORJSON, FileExtn.JSON, null);
//				OutputStream os = (OutputStream) writeInfo.get("OS");
//				writer = new PrintWriter(os);
//				writer.println(this.errorJsonArray);
//
//			}
//			catch(Exception ex)
//			{
//				if(writeInfo != null)
//				{
//					writeInfo.put("fileWritten", false);
//				}
//				logger.log(Level.WARNING, "Error while writing the JSON Error data to file.", ex);
//				throw new Exception("Problem while writing Error json data document", ex);
//			}
//			finally
//			{
//				if(writer != null)
//				{
//					writer.close();
//				}
//				if(writeInfo != null)
//				{
//					container.finishWrite(writeInfo);
//				}
//			}
//		}

	// used when value array is a array of json arrays
	private void setArrayTypeValues(JsonArray valueArray, Sheet sheet) {
		try {

			int rowIndex = colHeaderDepth;
			for (JsonElement row : valueArray) {
				JsonArray jsonRowArray = row.getAsJsonArray();
				int colIndex = 0;
				JsonElement pkVal = null;
				for (JsonElement column : jsonRowArray) {

					if (!column.isJsonNull()) {
						Cell cell = sheet.getCell(rowIndex, colIndex);
						String cellValue = column.getAsString();
						if (colIndex == 0) {

							this.rowIndex.put(cellValue, rowIndex);
							pkVal =column;

						}
						//logger.info("My column ::::"+colIndex+":::"+this.checkBoxMap+":::"+this.picklistCol+":::"+cellValue);
						if(this.checkBoxMap != null && !this.checkBoxMap.isEmpty() && this.checkBoxMap.contains(colIndex)){
							//logger.info("checkbox");
							List<DataRange> listOfRanges = new ArrayList();

							listOfRanges.add(new DataRange(sheet.getAssociatedName(), rowIndex, colIndex, rowIndex, colIndex));
							if("true".equalsIgnoreCase(cellValue)){
								ActionUtil.setCheckboxValue(sheet.getWorkbook(), listOfRanges, 1);
							}else{
								ActionUtil.setCheckboxValue(sheet.getWorkbook(), listOfRanges, 0);
							}
						}else if(this.picklistCol!= null && this.picklistCol.has((colIndex +""))){
							//	logger.info("picklist");
							int picklistId = sheet.getPicklistIDForCell(rowIndex, colIndex);
							Workbook workbook = sheet.getWorkbook();
							Picklist picklist  = workbook.getPicklist(picklistId);
							int id;
							try {
								id = picklist.getEqualItemID(cellValue);
								if( id != -1 ) {
									Value value = picklist.getActualValue(id);
									//
									//							//value.getValue().toString()
									//								ValueWithID val = (ValueWithID) value;
									//ActionUtil.setCellValue(cell, 	value.getValue().toString(), id);
									ActionUtil.setCellValue(cell, value.getValue().toString(), Arrays.asList(id),EngineConstants.DEFAULT_LOCALE,'.',',', DateUtil.DateFormatType.YMD, null);

								}else {
									ActionUtil.setCellValue(cell, cellValue, null,EngineConstants.DEFAULT_LOCALE,'.',',', DateUtil.DateFormatType.YMD, null);

								}
							} catch (NumberFormatException nfe) {
								id = -1;
								ActionUtil.setCellValue(cell, cellValue, null,EngineConstants.DEFAULT_LOCALE,'.',',', null, null);

							}



							//						if(id != -1) {
							//						    ActionUtil.setCellValue(cell, 	value.getValue().toString(), id);
							//						}
							//
							//						int itemID = picklist.getEqualItemID(valueID);
							//
							//
							//						if(itemID != -1) {
							//						    ActionUtil.setCellValue(cell, cellValue, itemID);
							//						}
						}
						else{
							if(colIndex != 0 && this.dateCol.has((colIndex +""))){
								//logger.info("dateCol");
								String colPattern = this.dateCol.get(colIndex+"").toString();
								try{
									//logger.log(Level.OFF, "Pattern:{0}, value:{1}", new Object[]{colPattern, cellValue});
									if(!cellValue.isEmpty()){
										CellImpl cellimpl = (CellImpl) cell;
										ActionUtil.setCellValue(cell, cellValue, null,EngineConstants.DEFAULT_LOCALE,'.',',',null, null);


									}
								}catch(Exception e ){
									logger.log(Level.WARNING, "Problem in date Pattern: "+colPattern+",value: "+cellValue,e);
								}
							}else{
								//logger.info("value:::"+cellValue);
								ActionUtil.setCellValue(cell, cellValue, null,EngineConstants.DEFAULT_LOCALE,'.',',',null, null);

							}
							//ActionUtil.validateAndAddHyperLink(sheet.getWorkbook(), cell, cellValue);


							//List<RichStringProperties> richStringProperties = EngineUtils1.setLink(cellValue, sheet.getWorkbook());
							ActionUtil.setCellValue(cell, cellValue, null,EngineConstants.DEFAULT_LOCALE,'.',',',null, null);

						}
					}
					colIndex++;
				}
				if(this.rowLockIndex != null&& pkVal!= null && this.rowLockIndex.contains(pkVal)){
					RangeUtil.protectRange(sheet, rowIndex, 0, rowIndex, colIndex-1,
							Protection.createPermissionMap(new HashSet<>(java.util.Arrays.asList("-9999")), null,
									null, null, null, null, null),
							isAllowInsert,true);


				}
				rowIndex++;
			}
		}catch(Exception e) {
			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e);
		}
	}

	private int maxColSize(JsonArray jsonArray) {
		int maxNoOfColsInRow = -1;
		for (JsonElement jsonRowArray : jsonArray) {
			int noOfColsInRow = jsonRowArray.getAsJsonArray().size();
			if (noOfColsInRow > maxNoOfColsInRow) {
				maxNoOfColsInRow = noOfColsInRow;
			}
		}
		return maxNoOfColsInRow;
	}

	/** check if values are given as array of arrays or array of objects **/
	private boolean isArrayOfArrays(JsonArray jsonArray) {
		if (jsonArray.size() == 0) {
			return true;// to avoid array index out of bounds
		}

		return jsonArray.get(0).isJsonArray();
	}

	private void setFormattingToTable(Sheet sheet) throws Exception {
		try{
			//	if(this.ui_settings != null && this.ui_settings.isEmpty()) {
				StyleActionUtil.setRangeBold(sheet, 0, 0, this.colHeaderDepth - 1, this.noOfCols - 1, EngineConstants.CHARWEIGHT_BOLD);

				ActionUtil.setRowsHeight(sheet, 0, 0, "26pt", true, null);// NO I18N
				StyleActionUtil.setRangeFontSize(sheet, 0, 0, 0, sheet.getUsedColumnIndex(), "12pt");// NO I18N

				StyleActionUtil.setRangeValign(sheet, 0, 0, 0, this.noOfCols - 1, "middle");// No I18N

				StyleActionUtil.setRangeHalign(sheet, 0, 0, 0, this.noOfCols - 1, "center");// No I18N


				if (this.colHeaderDepth > 1) {
					StyleActionUtil.setRangeBorder(sheet, 0, 0, this.colHeaderDepth - 1, this.noOfCols - 1,
							ActionConstants.BORDER_ALL, null);
					StyleActionUtil.setRangeBold(sheet, 0, 0, this.colHeaderDepth - 1, this.noOfCols - 1, null);
					StyleActionUtil.setRangeValign(sheet, 0, 0, this.colHeaderDepth - 1, this.noOfCols - 1, "middle");// No I18N

					StyleActionUtil.setRangeHalign(sheet, 0, 0, this.colHeaderDepth - 1, this.noOfCols - 1, "center");// No I18N

				} // borders only if header are more than one level

			//	ActionUtil.setRangeColor(sheet, 0, 0, this.colHeaderDepth - 1, this.noOfCols - 1, ZSColor.getInstance(" #f4f5f4"));// No I18N

				// ActionUtil.setRangeTextColor(sheet, 0, 0,
				// this.colHeaderDepth-1,this.noOfCols - 1, "#fff");//No I18N
			//}
			RangeUtil.protectRange(sheet, 0, 0, this.colHeaderDepth - 1, this.noOfCols - 1,
					Protection.createPermissionMap(new HashSet<>(java.util.Arrays.asList("-9999")), null, null, null, null, null, null),
					isAllowInsert,true);
			// ActionUtil.createFilterView(sheet, null, this.colHeaderDepth - 1, 0,
			// (this.colHeaderDepth - 1) + this.noOfValueRows, this.noOfCols - 1,
			// true, true);
			// FilterRange filterRange = new FilterRange(sheet,
			// sheet.getCell(this.colHeaderDepth - 1, 0),
			// sheet.getCell((this.colHeaderDepth - 1) + this.noOfValueRows,
			// this.noOfCols - 1), true, true);
			// sheet.setDefaultFilterView(new FilterView(filterRange));

			if (this.colHeaderDepth <= 5) {
				ActionUtil.freezePaneRows(sheet, 0, this.colHeaderDepth - 1);
			}

			ActionUtil.freezePaneRows(sheet, 0, this.colHeaderDepth - 1);

			// Apllying two conditional formats , one for ODD rows and one for EVEN
			// rows .
//		Range cfApplyRange = new Range(sheet, this.colHeaderDepth, 0, Utility.MAXNUMOFROWS - 1, this.noOfCols - 1);
//		String cfApplyRangeString = cfApplyRange.getCompleteRangeString();
//
//		// even formula
//		String forumalaLeftSubString_1 = "AND(ISEVEN(ROW());LEN(CONCATENATE(";// No I18N
//
//		String formulaRightSubString_1 = "))>0)";// No I18N
//		// odd forumla
//		String forumalaLeftSubString_2 = "AND(ISODD(ROW());LEN(CONCATENATE(";// No I18N
//																				//
//		String formulaRightSubString_2 = "))>0)";// No I18N
//
//		String formulaStartCellString = sheet.getCell(this.colHeaderDepth, 0).getCellRef();
//		String formulaEndCellString = sheet.getCell(this.colHeaderDepth, this.noOfCols - 1).getCellRef();
//
//		String formulaString1 = forumalaLeftSubString_1 + "$" + formulaStartCellString + ":" + "$"
//				+ formulaEndCellString + formulaRightSubString_1;
//
//		String formulaString2 = forumalaLeftSubString_2 + "$" + formulaStartCellString + ":" + "$"
//				+ formulaEndCellString + formulaRightSubString_2;
//
//		JSONObject cfJson1 = new JSONObject(
//				"{\r\n    \"cond1\": -1,\r\n  \"cval1\":"+formulaString1+",\r\n    \"cr_ty1\": \"FORMULA\",\r\n    \"cstyle1\": \"#C8E4FB~none~false~false~none~false\",\r\n    \"noofrules\": 1\r\n  }");// No I18N
//		JSONObject cfNewJSON1 = ThemeJsonConvertor.getCFNewModelJSON(cfJson1, ActionConstants.CONDITIONAL_FORMAT_APPLY);
//		//cfNewJSON1.getJSONObject(JSONConstants.RULES).put(JSONConstants.CRITERIA_VALUE, formulaString1);
//
//		JSONObject cfJson2 = new JSONObject(
//				"{\r\n    \"cond1\": -1,\r\n  \"cval1\":"+formulaString2+",\r\n    \"cr_ty1\": \"FORMULA\",\r\n    \"cstyle1\": \"#EEF5FB~none~false~false~none~false\",\r\n    \"noofrules\": 1\r\n  }");//No I18N
//		//cfNewJSON1.getJSONObject(JSONConstants.RULES).put(JSONConstants.CRITERIA_VALUE, formulaString2);
//		JSONObject cfNewJSON2 = ThemeJsonConvertor.getCFNewModelJSON(cfJson2, ActionConstants.CONDITIONAL_FORMAT_APPLY);


		}catch(Exception e){
			logger.info("Problem while setting formats::::"+e);
		}
//		ConditionFormatUtils.addConditionalStyles(cfNewJSON1, Arrays.asList(cfApplyRange), ActionConstants.CONDITIONAL_FORMAT_APPLY);
//		ConditionFormatUtils.addConditionalStyles(cfNewJSON2,Arrays.asList(cfApplyRange), ActionConstants.CONDITIONAL_FORMAT_APPLY);


		// ConditionFormatUtils.addConditionalStyles(cfJson1,
		// Arrays.asList(cfApplyRange),
		// ActionConstants.CONDITIONAL_FORMAT_APPLY);
		// ConditionFormatUtils.addConditionalStyles(cfJson2,Arrays.asList(cfApplyRange),
		// ActionConstants.CONDITIONAL_FORMAT_APPLY);

	}

	/**
	 * adding 10 px to optimal width,temporary fix for optimal width being too
	 * tight
	 **/
	private JSONArrayWrapper setOptimalColumnWidthPlus(Sheet sheet, int startCol, int endCol, boolean isPivotCall) {
		List<Style.Property> propNames = new ArrayList();
		List<Object> propValues = new ArrayList();
		propNames.add(ColumnStyle.Property.USEOPTIMALCOLUMNWIDTH);
		propValues.add("true");// No I18N
		JSONArrayWrapper jsonArray = new JSONArrayWrapper();
		RangeUtil.setColumnStyleProperty(sheet, startCol, endCol, propNames, propValues);

		// List<ColumnHeader> columns = RangeUtil.getColumnHeaders(sheet,
		// startCol, endCol);
		// ColumnHeader colHead;
		int prevStartCol = startCol;
		int preClientWidth = -1;
		// Should not iterate till used column. Even a column beyond used column
		// might have set with different column width.
		// int usedEndCol = Math.min(endCol, sheet.getColNum());
		for (int i = startCol; i <= endCol; i++) {
			// colHead = (ColumnHeader)columns.get(i);
			int clientWidth = ColumnHeader.getOptimalWidth(sheet, i);
			if(colWidthMap.containsKey(i)){
				clientWidth = (int) colWidthMap.get(i);
			}

			if (i != startCol) {
				if (clientWidth != preClientWidth) {
					ActionUtil.setColumnsWidth(sheet, prevStartCol, i - 1, EngineUtils1.convertPixelsToInches(
							String.valueOf(preClientWidth + 30), EngineConstants.COLDPI), true, null);
					jsonArray.put(ResponseUtil.getColWidthJson(prevStartCol, i - 1, preClientWidth + 30));
					preClientWidth = clientWidth;
					prevStartCol = i;
				}
			} else {
				preClientWidth = clientWidth;
			}
		}
		ActionUtil.setColumnsWidth(sheet, prevStartCol, endCol,
				EngineUtils1.convertPixelsToInches(String.valueOf(preClientWidth + 30), EngineConstants.COLDPI), true,
				null);
		// if(pkcolIndex != -1){
		//
		// //sActionUtil.setRangeWrap(sheet, 0, pkcolIndex,
		// Utility.MAXNUMOFROWS, pkcolIndex, 1, true);
		// // ActionUtil.setColumnsWidth(sheet, pkcolIndex, pkcolIndex,
		// "0.0111in", true, null);//No I18N
		//
		// }
		jsonArray.put(ResponseUtil.getColWidthJson(prevStartCol, endCol, preClientWidth + 30));
		return jsonArray;
	}
	public static void revertidforerror(WorkbookContainer container,Sheet sheet ,int rowIndex){
		String docId = container.getDocId();
		RangeIterator rIterator = new RangeIterator(sheet, rowIndex, 0, rowIndex, sheet.getUsedColumnIndex(),
				RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, true, true);
		boolean isrevertreq = true;
		String uuid = null ;
		Cell cl1 = null;

		while (rIterator.hasNext()) {
			ReadOnlyCell rCell = rIterator.next();
			int col = rCell.getColIndex();
			Cell cl = sheet.getCell(rowIndex, col);
			if(col == 0){
				cl1= cl;
				uuid = cl.getValue().getValue().toString();
			}else{
				if (cl.getValue().getValue() != null) {
					isrevertreq = false;
				}
			}
		}if(isrevertreq){
			try {
				RedisHelper.hdel(redisKey + docId + ":edit",uuid); //No I18N

				ActionUtil.setCellValue(cl1, "", null,EngineConstants.DEFAULT_LOCALE,'.',',',null, null);

			} catch (Exception e) {
				logger.info("redis revert update :::"+ uuid+":::"+e);
				// TODO Auto-generated catch block

			}
		}
//						if(isMandatoryfield){
	}


	public  boolean redisHandler(WorkbookContainer container, Sheet sheet,  List<Range> destRange,List<Cell> formulaCells,List<DataRange> listOfSourceDataRanges,String doc,String rmName) {
		try {

			if(this.jsonHeaderObject ==null || this.jsonHeaderObject.entrySet().isEmpty() ){
				getDetails(container);
			}
			int maxRow = container.getJsonMaxRow();
			int trackCount = container.getjsontrackCount();
			int maxCol = getAllColIndex(container);


			HashMap<Integer,List> changedRange = new HashMap();
			if(destRange != null && !destRange.isEmpty()) {
				for (int i = 0; i < destRange.size(); i++) {
					Range range = destRange.get(i);
					int sR = range.getStartRowIndex();
					int sC = range.getStartColIndex();
					int eR = range.getEndRowIndex();
					int eC = range.getEndColIndex();
					if ((eR - sR) > trackCount - 1) {
						return false;
					}
					for (int row = sR; (row <= eR && row<=maxRow); row++) {
						List colList = changedRange.containsKey(row) ? changedRange.get(row) : new ArrayList();
						for (int _col = sC; (_col <= eC && _col <= maxCol); _col++) {
							int col = _col;
							if (!colList.contains(_col)) {
								colList.add(_col);
							}
						}
						changedRange.put(row, colList);
					}

				}

			}if(formulaCells != null && !formulaCells.isEmpty()) {
				for (int i = 0; i < formulaCells.size(); i++) {

					Cell cell = formulaCells.get(i);
					int rw = cell.getRowIndex();
					int cl = cell.getColumnIndex();
					if(rw <=maxRow && cl<=maxCol) {
						List colList = changedRange.containsKey(rw) ? changedRange.get(rw) : new ArrayList();
						if (!colList.contains(cl)) {
							colList.add(cl);
						}
						changedRange.put(rw, colList);
					}
				}
			}
			if(listOfSourceDataRanges != null && !listOfSourceDataRanges.isEmpty()){
				for (int i = 0; i < listOfSourceDataRanges.size(); i++) {
					DataRange range = listOfSourceDataRanges.get(i);
					int sR = range.getStartRowIndex();
					int sC = range.getStartColIndex();
					int eR = range.getEndRowIndex();
					int eC = range.getEndColIndex();
					if ((eR - sR) > trackCount - 1) {
						return false;
					}
					for (int row = sR; (row <= eR && row<=maxRow); row++) {
						List colList = changedRange.containsKey(row) ? changedRange.get(row) : new ArrayList();
						for (int _col = sC; (_col <= eC && _col <= maxCol); _col++) {
							int col = _col;
							if (!colList.contains(_col)) {
								colList.add(_col);
							}
						}
						changedRange.put(row, colList);
					}

				}
			}
//			logger.log(Level.INFO,"test row details::::"+startRow+":::"+startCol+":::"+endRow+":::"+endCol);
			// int pkColIndex = 0;
			String docId = container.getDocId();
			JsonObject header = this.jsonHeaderObject;//getHeader(container);


			// JsonObject a = jsonHeaderObject;
			for (Map.Entry<Integer,List> entry : changedRange.entrySet()){
				int row = entry.getKey();
				String celValue = "";
				String colList = entry.getValue().toString();
				colList = colList.replace("[","");
				colList = colList.replace("]","");
				Cell cell = sheet.getCell(row, 0);
				Object _celValue = cell.getValue().getValue();
				if (_celValue != null) {
					celValue = _celValue.toString();
					if (RedisHelper.hexists(redisKey + docId + ":edit_modified", celValue)) {
						String _value = RedisHelper.hget(redisKey + docId + ":edit_modified", celValue);// NO I18N
						//
						String[] val = _value.split(",");

						RedisHelper.hset(redisKey + docId + ":edit_modified", celValue, _value + "," + colList);// No I18N

						//
					} else if (RedisHelper.hexists(redisKey + docId + ":edit", celValue)) {
						String _value = RedisHelper.hget(redisKey + docId + ":edit", celValue);// NO I18N
						RedisHelper.hset(redisKey + docId + ":edit", celValue, _value + "," + colList);// No I18N
						//

					} else {
						RedisHelper.hset(redisKey + docId + ":edit_modified", celValue, colList);// No I18N
					}
				} else {
					String uId = UUID.randomUUID().toString();
					RedisHelper.hset(redisKey + docId + ":edit", uId, colList);// No I18N
					cell.setValue(Value.getInstance(Type.STRING, uId));
					rowIndex_added.put(uId, row);
				}
			}


			long count1 = RedisHelper.hlen(redisKey + docId + ":edit_modified");// No I18N
			long count2 =  RedisHelper.hlen(redisKey + docId + ":edit") ;// No I18N
			logger.info("counterkdjssggsd);;:"+count1+":::"+count2 +":::"+trackCount);

			if(count1 >trackCount-1 || count2 >trackCount-1){
				String collab_id = null;
				Long zuid = null;
				String userName =container.getDocOwner();
				Persistence pers = SheetPersistenceUtils.getPersistence(userName);
				Criteria collabIdCri = new Criteria(new Column("CollabDocuments","DOCUMENT_ID"),docId,QueryConstants.EQUAL);
				DataObject dataObj = pers.get("CollabDocuments",collabIdCri);
				if(!dataObj.isEmpty()){
					Row rbrow =  dataObj.getRow("CollabDocuments");
					collab_id = (String) rbrow.get("COLLAB_ID");
					zuid = (Long) rbrow.get("AUTHOR_ZUID");
					logger.info("counterkdjssggsd);;:"+collab_id);
				}

				logger.info("counterkdjssggsd)123;;:"+rmName+":::"+doc);
				// IAMUtil.getcurrent
				if(rmName != null){
					MessagePropagator.sendDirectUpdateMessage(collab_id, 9876 ,rmName);
				}
			}

		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem In Cache Generation", e);
			// TODO Auto-generated catch block

		}
		return true;

	}

	public JSONObjectWrapper saveChangedOnly(WorkbookContainer container, String pushFormat) {
		Workbook workbook = null;
		SpreadsheetSettings actualSettings = null;
		try {
			JSONObjectWrapper obj = new JSONObjectWrapper();
			workbook= container.getWorkbook(null);
			actualSettings= workbook.getSpreadsheetSettings();
			workbook.setSpreadsheetSettings(SpreadsheetSettings.defaultSpreadsheetSettings);

			workbook.regenerateContent();
			Sheet sheet = workbook.getSheetByAssociatedName("0#");
			String docId = container.getDocId();
			String docOwner = container.getDocOwner();
			String trackFields = "false";
			boolean onlyeditedcol = true;
			logger.info("test header objecdt print here:::"+this.jsonHeaderObject);
			if(this.jsonHeaderObject ==null || this.jsonHeaderObject.entrySet().isEmpty()){
				getDetails(container);
			}

			logger.info("my docid ::: "+docId);
			//logger.info("my save ::: "+docId);
			logger.info("my edit ::: "+RedisHelper.hgetAll(redisKey + docId + ":edit"));
			logger.info("my edit mod ::: "+RedisHelper.hgetAll(redisKey + docId + ":edit_modified"));
			if (RedisHelper.exists(redisKey + docId + ":save")) {
				RedisHelper.del(redisKey + docId + ":save");// No I18N
			}
			if (RedisHelper.exists(redisKey + docId + ":save_modified")) {
				RedisHelper.del(redisKey + docId + ":save_modified");// No I18N
			}
			if (RedisHelper.exists(redisKey + docId + ":edit")) {// NO I18N
				logger.info("save changed  update :::"+RedisHelper.hgetAll(redisKey + docId + ":edit") );
				RedisHelper.rename(redisKey + docId + ":edit", redisKey + docId + ":save");// No I18N
				//
			}
			if (RedisHelper.exists(redisKey + docId + ":edit_modified")) {// NO I18N
				logger.info("save changed  update :::"+RedisHelper.hgetAll(redisKey + docId + ":edit_modified") );									//
				RedisHelper.rename(redisKey + docId + ":edit_modified", redisKey + docId + ":save_modified");// NO I18N
				//
			}
			HashMap saveonly = (HashMap) RedisHelper.hgetAll(redisKey + docId + ":save");// No I18N
			//
			HashMap saveonly_modified = (HashMap) RedisHelper.hgetAll(redisKey + docId + ":save_modified");// No I18N
			//
			JsonObject headerObject = this.jsonHeaderObject;
			int maxCol = this.getAllColIndex(container);
			BidiMap displayName = this.displayNameMap;//getDisplayName(container);

			Set set = RedisHelper.hkeys(redisKey + docId + ":save");// No I18N
			Set searchset = new HashSet();
			searchset.addAll(set);

			Set _set = RedisHelper.hkeys(redisKey + docId + ":save_modified");// NO I18N
			//
			// Iterator itr = _set.iterator();
			searchset.addAll(_set);
			// while(itr.hasNext()){
			// set.add(itr.next());
			// }

			Persistence pers = (Persistence) BeanUtil.lookup("Persistence", docOwner);// No
			logger.info("doc id fromjson:::::"+docId);						// I18N
			SelectQueryImpl sql = new SelectQueryImpl(new Table("RemoteBooks"));
			Criteria crt = new Criteria(new Column("RemoteBooks", "DOCUMENT_ID"), docId, QueryConstants.EQUAL); // NO
			// I18N
			sql.setCriteria(crt);
			sql.addSelectColumn(new Column(null, "*"));
			DataObject dobj = pers.get(sql);
			String handbackID = "";

			if (!dobj.isEmpty()) {
				Iterator iterator = dobj.getRows("RemoteBooks");
				while (iterator.hasNext()) {
					Row row = (Row) iterator.next();
					JsonObject settings = new JsonParser().parse((String) row.get("META_DATA")).getAsJsonObject();
					//logger.info("my metadata print here::"+settings);
					trackFields = settings.get("trackfields").toString();
					handbackID = (String) row.get("HANDBACK_ID");
					if(settings.has("onlyeditedcol")){
						String _onlyeditedcol =settings.get("onlyeditedcol").toString();
						onlyeditedcol = "true".equalsIgnoreCase(_onlyeditedcol) ? true :false;
						//logger.info("my metadata print here::"+onlyeditedcol);
					}
				}
			}
			Map findHash = findAList(sheet, searchset, 0, 0, "rowindexsearch");// No I18N
			List mandatorylist = getMandatory(headerObject, displayName);
			List includelist = getHeaderInclude(headerObject, displayName);
			//logger.info("my mandatory before print here::"+set +":::"+searchset+"::::"+_set);
			logger.info("my set :::"+set);
			if(set != null && !set.isEmpty()) {
				logger.info("my set 1:::"+set);
				this.resultSet = set;


				if (this.checkForMandatory(container, headerObject, displayName, this.resultSet, findHash, mandatorylist, true,maxCol)) {
					//logger.info("my mandatory success print here::"+set);
					JSONArrayWrapper header = getRowData(container, 0, sheet.getUsedColumnIndex(), true);
					if (this.resultSet != null && !this.resultSet.isEmpty()) {

						// I18N
						// checkForMandatory(container, headerObject, displayName, set, findHash);
						for (Object key : this.resultSet) {
							int row = (int) findHash.get(key);
							String editedList = saveonly.get(key).toString();
							logger.info("my mandatory add print here::" + editedList);
							if (onlyeditedcol) {
								JSONObjectWrapper json = this.getRowData_Foreditedcol(container, row,maxCol , editedList, mandatorylist, includelist);
								Iterator keys = json.keys();
								boolean isadd = false;
								while (keys.hasNext()) {
									String _key = keys.next().toString();
									if (!includelist.contains(_key)) {
										String _val = json.get(_key).toString();
										if (_val != null && !_val.isEmpty() && !"".equalsIgnoreCase(_val)) {
											isadd = true;
										}
									}
								}
								if (isadd) {
									jsonAddedObject.put(json);
								}
							} else {
								JSONArrayWrapper json = getRowData(container, row, sheet.getUsedColumnIndex(), false);
								jsonAddedObject.put(json);

							}
							//String _key = saveonly.get(key).toString();
							//if ("added".equalsIgnoreCase(saveonly.get(key).toString())) {

						}
					}
				}
				else {
					if (RedisHelper.exists(redisKey + docId + ":save")) {

						RedisHelper.rename(redisKey + docId + ":save", redisKey + docId + ":edit");// No I18N
						//
					}
					if (RedisHelper.exists(redisKey + docId + ":save_modified")) {// NO I18N
						//logger.info("save changed  update :::"+RedisHelper.hgetAll(redisKey + docId + ":edit_modified") );									//
						RedisHelper.rename(redisKey + docId + ":save_modified", redisKey + docId + ":edit_modified");// NO I18N
						//
					}
					JSONObjectWrapper man_obj = new JSONObjectWrapper();

					man_obj.put("mandatorycheckfailed", true);

					workbook.setSpreadsheetSettings(actualSettings);
					workbook.regenerateContent();
					return man_obj;
				}
			}
			if(_set != null && !_set.isEmpty()){
				this.resultSet = _set;
				if(this.checkForMandatory(container, headerObject, displayName, _set, findHash,mandatorylist,false,maxCol)) {
					//Set resultset =set;
					if (this.resultSet != null && !this.resultSet.isEmpty()) {
						//Map findHash = findAList(sheet, _set, 0, 0, "rowindexsearch");// No I18N
						// I18N
						//checkForMandatory(container, headerObject, displayName, _set, findHash);


						for (Object key : this.resultSet) {
							int row = (int) findHash.get(key);
							String[] columns = new String[Utility.MAXNUMOFCOLS];
							for (int i = 0; i < maxCol; i++) {
								columns[i] = sheet.getCell(0, i).getValue().getValue().toString();
							}
							// String fieldValue = (String)
							// saveonly_modified.get(key);
							String fields = ((String) saveonly_modified.get(key));
							if (onlyeditedcol) {

								JSONObjectWrapper json = this.getRowData_Foreditedcol(container, row, maxCol, fields, mandatorylist, includelist);
								if(json !=null) {
									jsonModifiedObject.put(json);


								}

							} else {
								JSONArrayWrapper jsonData = getRowData(container, row, maxCol, false);


								jsonModifiedObject.put(new JsonParser().parse(jsonData.toString()));
							}
						}


					}


				}else {
					if (RedisHelper.exists(redisKey + docId + ":save")) {

						RedisHelper.rename(redisKey + docId + ":save", redisKey + docId + ":edit");// No I18N
						//
					}
					if (RedisHelper.exists(redisKey + docId + ":save_modified")) {// NO I18N
						//logger.info("save changed  update :::"+RedisHelper.hgetAll(redisKey + docId + ":edit_modified") );									//
						RedisHelper.rename(redisKey + docId + ":save_modified", redisKey + docId + ":edit_modified");// NO I18N
						//
					}
					JSONObjectWrapper man_obj = new JSONObjectWrapper();
					man_obj.put("mandatorycheckfailed", true);
					workbook.setSpreadsheetSettings(actualSettings);
					workbook.regenerateContent();
					return man_obj;
				}
			}
			JSONObjectWrapper fileObj =  constructJsonFile(container, pushFormat,handbackID,displayName);
			workbook.setSpreadsheetSettings(actualSettings);
			workbook.regenerateContent();
			return fileObj;

		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.log(Level.WARNING, "Problem in constructing response", e);

		}
		workbook.setSpreadsheetSettings(actualSettings);
		workbook.regenerateContent();
		return null;
	}

	private boolean checkForMandatory(WorkbookContainer container, JsonObject header, BidiMap displayName, Set dataset,
									  Map findHash,List mandatorylist,boolean isadded,int maxCol) {
		try {

			Workbook workbook =  container.getWorkbook(null);

			Sheet sheet =workbook.getSheetByAssociatedName("0#");
			//List list = getMandatory(header, displayName);

			if (findHash == null || findHash.isEmpty()) {
				findHash = findAList(sheet, dataset, 0, 0, "rowindexsearch");// No I18N
				// I18N
			}
			if (this.colIndex == null) {
				this.getAllColIndex(container);
			}
			Set _dataset = new HashSet<String>();
			_dataset.addAll(dataset);
			Iterator itr = _dataset.iterator();
			while (itr.hasNext()) {
				String key = itr.next().toString();
				int row = findHash.containsKey(key) ? (int) findHash.get(key) :-1;
				for (int i = 0; i < mandatorylist.size(); i++) {
					if (row != -1) {
						String head = mandatorylist.get(i).toString();
						//this.displayNameMap.get(key)
						int col = this.colIndex.get(head);
						Cell cell = sheet.getCell(row, col);
						Object obj = cell.getValue().getValue();
						boolean process = false;
						if ((obj == null || "".equals(obj))) {
							//if (isadded) {
								for (int j = 1; j <= maxCol; j++) {
									Cell cell1 = sheet.getCell(row, j);

									Object obj1 = cell1.getValue().getValue();
									if (obj1 != null && !"".equals(obj1)) {
										if(!(CellUtil.isCheckboxPresent(sheet, row, j) && "false".equalsIgnoreCase(obj1.toString()))) {
											process = true;
											break;
										}
									}
								}
//							}else {
//								process = true;
//							}
							logger.info("process:::::"+process);
							if (process) {
								JSONObjectWrapper cfJson = new JSONObjectWrapper();
								JSONArrayWrapper array = new JSONArrayWrapper();
								String cellId = cell.toString();
								String[] _cellid = cellId.split(":");
								JSONObjectWrapper cfJson_error = new JSONObjectWrapper(
										"{\"cr_ty\":\"FORMULA\",\"cstyle\":{\"bgColor\":{\"hexColor\":\"#FBD1CF\"},\"bold\":0,\"italic\":0,\"underline\":\"none\",\"strike\":0},\"cond\":-1,\"cval\":\"IF(" + _cellid[0] + "=" + _cellid[0] + ")\"}]}");// No I18N

								array.put(cfJson_error);

								cfJson.put("rules", array);
								DataRange applyRange = new DataRange(sheet.getAssociatedName(), cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex());
								ConditionFormatUtils.addConditionalStyles(sheet, cfJson, Arrays.asList(applyRange),
										ActionConstants.CONDITIONAL_FORMAT_APPLY);
								Annotation annotation = new Annotation();
								cell.setAnnotation(annotation);
								annotation.setContent(LocaleMsg.getMsg("JsonParser.MandatoryCheck"));// NO I18N
								return false;
							} else {
								if(isadded) {
									this.resultSet.remove(key);
									Cell cell1 = sheet.getCell(row, 0);
									ActionUtil.setCellValue(cell1, "", null,EngineConstants.DEFAULT_LOCALE,'.',',',null, null);

									ConditionFormatUtils.clearConditionalStyleFormat(sheet, row, 0, row, sheet.getUsedColumnIndex());
									List<DataRange> listOfDataRanges = new ArrayList<>();
									DataRange dataRanges = new DataRange(sheet.getAssociatedName(), row, 0, row, sheet.getUsedColumnIndex());
									listOfDataRanges.add(dataRanges);
									ActionUtil.cellCommentAction(workbook,listOfDataRanges,null,"remove",null,null,null,null);//No I18N

								}

							}

						}
					}else{
						this.resultSet.remove(key);
					}
				}
			}
			return true;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.log(Level.WARNING, "Problem in constructing response", e);
			return false;
		}


	}

	private List getMandatory(JsonObject header, BidiMap displayName) {

		Set<Entry<String, JsonElement>> entrySet = header.entrySet();
		List result = new ArrayList();
		for (Map.Entry<String, JsonElement> entry : entrySet) {
			String key = entry.getKey();
			JsonObject value = entry.getValue().getAsJsonObject();
			if (value != null && value.has(Constants.JSON_COL_IS_MANDATORY)
					&& value.get(Constants.JSON_COL_IS_MANDATORY).getAsBoolean()) {
				if (displayName.containsKey(key)) {
					result.add(displayName.get(key));
				} else {

					result.add(key);
				}
			}
		}
		return result;
	}
	private List getHeaderInclude(JsonObject header, BidiMap displayName) {

		Set<Entry<String, JsonElement>> entrySet = header.entrySet();
		List result = new ArrayList();
		for (Map.Entry<String, JsonElement> entry : entrySet) {
			String key = entry.getKey();
			JsonObject value = entry.getValue().getAsJsonObject();
			if (value != null && value.has(Constants.JSON_HEADER_INCLUDE)
					&& value.get(Constants.JSON_HEADER_INCLUDE).getAsBoolean()) {
				if (displayName.containsKey(key)) {
					result.add(displayName.get(key));
				} else {

					result.add(key);
				}
			}
		}
		return result;
	}

	private JSONObjectWrapper constructJsonFile(WorkbookContainer container, String pushFormat,String handbackID,BidiMap displayName) {
		try {
			//logger.info("myhandback id fromjson:::::"+handbackID);
			Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");
			int colIndex = 0;// Integer.parseInt(RedisHelper.hget(container.getDocId()+"pkcolIndex",
			// "pkcolIndex"));
			JSONArrayWrapper headerJson = getRowData(container, 0, sheet.getUsedColumnIndex(), true);

			JSONArrayWrapper header = new JSONArrayWrapper();
			//logger.info("displayName id fromjson:::::"+displayName);
			if(displayName != null &&  !displayName.isEmpty()){
				for(int i  =0 ;i<headerJson.length();i++){
					JSONObjectWrapper obj = new JSONObjectWrapper();
					String key = headerJson.getString(i);
					obj.put(Constants.JSON_COL_NAME, key);
					if(displayName.containsKey(key)){
						String value = (String) displayName.get(key);
						obj.put(Constants.JSON_DISPLAY_NAME, value);
					}
					header.put(obj);
				}
			}
			//JSONArray csvResponse = new JSONArray();
//			if ("csv".equalsIgnoreCase(pushFormat)) {
//				csvResponse.put(headerJson);
//				csvResponse.put(jsonModifiedObject);
//				csvResponse.put(jsonAddedObject);
//				return csvResponse;
//			} else {
			JSONObjectWrapper respJson = new JSONObjectWrapper();

			if((jsonModifiedObject != null && jsonModifiedObject.length()>0) ||(jsonAddedObject != null && jsonAddedObject.length()>0)) {

				contentStatus= true;
			}


			respJson.put("modified_data", jsonModifiedObject);
			data_counter.put("modified_count", jsonModifiedObject.length());

			logger.info("added not empty");
			respJson.put("added_data", jsonAddedObject);
			data_counter.put("added_count", jsonAddedObject.length());



			logger.log(Level.INFO,"content status ",contentStatus);


			if(header != null && !header.isEmpty()){
				respJson.put("headers", header)	;
			}else{
				respJson.put("headers",headerJson.toString());
			}

//				if(handbackID != null && handbackID!= ""){
//					respJson.put("handbackID", handbackID);
//				}

			//respJson.put("displayNameMap", displayName);
			//}
			logger.info("respJson id fromjson:::::"+respJson);
			return respJson;
			//}
		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in constructing response", e);
		}
		return null;

	}
	public JSONObjectWrapper getCount(){
		return this.data_counter;
	}
	public boolean getContentStatus(){
		return this.contentStatus;
	}

	public JSONArrayWrapper getRowData(WorkbookContainer container, int rowIndex, int colIndex, boolean isHeader) {
		try {
			Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");

			String value = "";
			int i = 0;
			BidiMap obj = null;
			JSONObjectWrapper objresponse = new JSONObjectWrapper();
			JSONArrayWrapper response = new JSONArrayWrapper();
			JsonObject header = this.jsonHeaderObject;//getHeader(container);
			RangeIterator rIterator = new RangeIterator(sheet, rowIndex, 0, rowIndex, colIndex,
					RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, true, true);

			obj = this.displayNameMap;// getDisplayName(container);


			while (rIterator.hasNext()) {
				ReadOnlyCell rCell = rIterator.next();
				int col = rCell.getColIndex();
				Cell cl = sheet.getCell(0, col);

				if (cl.getValue().getValue() != null) {
					String val = cl.getValue().getValue().toString();
					if (header.has(val) || obj.containsValue(val)) {
						Cell cell = rCell.getCell();




						if(cell.getValue() instanceof PicklistValue) {
							PicklistValue pvalue = (PicklistValue) cell.getValue();
							PicklistItem item = pvalue.getPicklistItems().get(0);
							Value pActualValue = item.getActualValue();

							if(pActualValue instanceof ValueWithID) {
								String id = ((ValueWithID) pActualValue).getID();
								response.put(id);
							}else {
								value = rCell.getCell().getContent();
								response.put(value);
							}


						}
						else if (rCell.getCell().getValue().getValue() != null) {
							value = rCell.getCell().getContent();//rCell.getCell().getValue().getValueString(LocaleUtil.getLocale("en", "US"));// No I18N

							if (isHeader && obj != null && obj.containsValue(value)) {
								response.put(obj.getKey(value));
								//} else if(){



							}else {

								response.put(value);
							}
							//} else {
//							if(isMandatoryfield){
//
//								return null;
//							}
						}
						else if (rCell.getCell().getValue().getValue() == null && rowIndex == 0) {
							continue;
						} else {
							response.put("");
						}
					}
				}

			}
			return response;
		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in constructing response", e);
		}
		return null;
	}
	public JSONObjectWrapper getRowData_Foreditedcol(WorkbookContainer container, int rowIndex,int colIndex, String listCol,List mandatorylist,List includeList) {
		try {
			// 	logger.info("my msg :::::"+listCol);
			Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");
			ArrayList<Integer> colList = new ArrayList<Integer>();
			logger.info("collist print here::::"+listCol);
			String[] _val = listCol.split(",");
			for(int j=0;j<_val.length;j++){
				int _temp= Integer.parseInt((_val[j].trim()));
				colList.add(_temp);
			}
			logger.info("collist print here::::"+colList);

			int i = 0;
			BidiMap obj = null;
			JSONObjectWrapper dateList = null;
//			JSONObject response = new JSONObject();
//			org.json.JSONObject response = new org.json.JSONObject();
			JSONObjectWrapper response = new JSONObjectWrapper();
			RangeIterator rIterator = new RangeIterator(sheet, rowIndex, 0, rowIndex, colIndex,
					RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, true, true);

			obj = this.displayNameMap;//getDisplayName(container);
			dateList = this.dateCol;//this.getDateList(container);
			logger.info("My date list print here:::"+dateList);

//			JsonParser parser = new JsonParser();
//			JsonArray colList = parser.parse(listCol).getAsJsonArray();


			while (rIterator.hasNext()) {
				//	logger.info("my msg 123434:::::"+rIterator.hasNext()+":::"+rowIndex+":::"+colIndex);
				String value = "";
				ReadOnlyCell rCell = rIterator.next();
				int col = rCell.getColIndex();
				int row = rCell.getRowIndex();
				Cell cl = sheet.getCell(0, col);
				Cell cel = null;

				String clValue = cl.getValue().getValue() != null ?cl.getValue().getValue().toString():"";





//				JsonElement ele = parser.parse(col+"").getAsJsonPrimitive();
				if( colList.contains(col)|| includeList.contains(clValue) || col == 0){
					String val = cl.getValue().getValue().toString();
					Object test = rCell.getCell().getValue();
					DateFormat dateFormat;

					if (cl.getValue().getValue() != null  ) {
						if (test instanceof PicklistValue) {
							PicklistValue pvalue = (PicklistValue) test;
							PicklistItem item = pvalue.getPicklistItems().get(0);
							Value pActualValue = item.getActualValue();

							if (pActualValue instanceof ValueWithID) {
								value = ((ValueWithID) pActualValue).getID();
							}else{
								value = rCell.getCell().getContent();//   ((CellImpl) cel);.getPattern(2).formatValue(container.getWorkbook(null), cel.getValue()).getContent();
							}

						}

						else if(dateList != null && dateList.has(col+"") && row!=0) {
							String val1 = dateList.get(col+"").toString();
							if(val1.contains("hh") || val1.contains("HH")) {
								dateFormat = sheet.getWorkbook().getDateFormatToWrite(Type.DATETIME);
							}else {
								//logger.info("my  vale list ----"+value);
								dateFormat = sheet.getWorkbook().getDateFormatToWrite(Type.DATE);
							}
							if(rCell.getCell().getValue().getValue()!= null) {
								value = dateFormat.format(rCell.getCell().getValue().getValue());
							
							}
						}


						else if(rCell.getCell().getValue().getValue() != null) {
							value = (rCell.getCell().getContent()).toString();
							//value = rCell.getCell().getValue().getValueString(SpreadsheetSettings.defaultSpreadsheetSettings);
						}else {
							value = "";
						}
						String header = (String) obj.getKey(val);
						if(header == null){
							logger.info("info...");
						}
						response.put(header,value);

						//logger.info("my msg :::::"+header+":::"+value);



//						}else {
//							String val = cl.getValue().getValue().toString();
//							String header = (String) obj.getKey(val);
//							response.put(header, "");
					}
				}
			}
			//response.keys();


			logger.info("my  respinsse list from get row data ----"+response);
			return response;
		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in constructing response", e);
		}
		return null;
	}

	public  int getAllColIndex(WorkbookContainer container) {
		try {
			JsonObject header = this.jsonHeaderObject;//getHeader(container);
			BidiMap displayname = this.displayNameMap;//getDisplayName(container);
			Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");
			int maxCol = -1;
			RangeIterator rIterator = new RangeIterator(sheet, 0, 0, 0, sheet.getUsedColumnIndex(),
					RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, false, true);
			while (rIterator.hasNext()) {
				ReadOnlyCell rCell = rIterator.next();
				if (rCell.getCell().getValue().getValue() != null) {
					String value = rCell.getCell().getValue().getValue().toString();
//					logger.info("value ::::"+value);
//					logger.info("header ::::"+header);
//					logger.info("displayname ::::"+displayname);
					if (value != null && (header.has(value) || displayname.containsValue(value))) {

						this.colIndex.put(value, rCell.getColIndex());
						maxCol = rCell.getColIndex();
					}

				}
			}
			return maxCol;
		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in colindex  construction", e);
		}
		return -1;
	}
	public  HashMap getAllColIndexAPIname(WorkbookContainer container) {
		HashMap map = new HashMap();
		try {
			JsonObject header = this.jsonHeaderObject;//getHeader(container);

			BidiMap displayname = this.displayNameMap;//getDisplayName(container);
			Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");
			int maxCol = -1;
			RangeIterator rIterator = new RangeIterator(sheet, 0, 0, 0, sheet.getUsedColumnIndex(),
					RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, false, true);
			while (rIterator.hasNext()) {
				ReadOnlyCell rCell = rIterator.next();
				if (rCell.getCell().getValue().getValue() != null) {
					String value = rCell.getCell().getValue().getValue().toString();
					logger.info("value ::::"+value);
//					logger.info("header ::::"+header);
					logger.info("displayname ::::"+displayname);
					if (value != null && (header.has(value) || displayname.containsValue(value))) {

						map.put(displayname.getKey(value), rCell.getColIndex());

					}

				}
			}
			return map;
		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in colindex  construction", e);
		}
		return map;
	}


	public Map findAList(Sheet sheet, Set set, int rowIndex, int colIndex, String searchOption) {
		// int rowIndex = sheet.getUsedRowIndex();
		int startRow = 0;
		int endRow = sheet.getUsedRowIndex();
		int startCol = 0;
		int endCol = sheet.getUsedColumnIndex();
		if ("rowindexsearch".equalsIgnoreCase(searchOption)) {
			startCol = colIndex;
			endCol = colIndex;
		}
		// else if("columnindexsearch".equalsIgnoreCase(searchOption)){
		// startRow =rowIndex;
		// endRow =rowIndex;
		// }
		Map map = new HashMap();
		if ("colindexsearch".equalsIgnoreCase(searchOption)) {
			this.getAllColIndex(container);
			return this.colIndex;
		}
		RangeIterator rIterator = new RangeIterator(sheet, startRow, startCol, endRow, endCol,
				RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, false, true);
		while (rIterator.hasNext()) {
			ReadOnlyCell rCell = rIterator.next();
			if (rCell.getCell().getValue().getValue() != null) {
				String value = rCell.getCell().getValue().getValue().toString();
				if (set.contains(value) && "rowindexsearch".equalsIgnoreCase(searchOption)) {
					map.put(value, rCell.getRowIndex());
					this.rowIndex.put(value,rCell.getRowIndex());

				}

			}
		}
		return map;
	}
	public JSONObjectWrapper responseHandler(WorkbookContainer container,HashMap respMessage){
		JsonParser parser = new JsonParser();
		JsonElement ele = parser.parse(respMessage.get("message").toString());
		return this.responseHandler(container,ele);
	}
	public JSONObjectWrapper responseHandler(WorkbookContainer container,HttpServletRequest request){

		byte[] fileContent = null;
		JsonParser parser = new JsonParser();
		String data = request.getParameter("message");
		JsonElement ele = parser.parse(data);
		return this.responseHandler(container, ele);
//		FileInputStream fis= null;
//		ArrayList<UploadedFileItem> filesList = (ArrayList<UploadedFileItem>) request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST);
//		for(UploadedFileItem uploadedFileItem : filesList) {
//			try {
//				/* -- Setting file Content -- */
//				File f = uploadedFileItem.getUploadedFile();
//				fis = new FileInputStream(f);
//				fileContent = ClientUtils.readStream(fis);
//				if (fileContent == null) {
//					String c = new String(fileContent);
//					JsonElement ele = parser.parse(c);
//					return this.responseHandler(container, ele);
//				}
//			} catch (Exception e) {
//
//			}
//			finally {
//				try {
//					fis.close();
//
//				} catch(Exception e) {
//
//				}
//			}
//		}
//		return null;
	}
	public JSONObjectWrapper responseHandler(WorkbookContainer container, JsonElement ele) {
		JSONObjectWrapper resp = new JSONObjectWrapper();

		try {

//			logger.info("My response print here::::" + respMessage);
//			JsonParser parser = new JsonParser();
//			JsonElement ele = parser.parse(respMessage.get("message").toString());
			Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");
			logger.info("test print patch here :::"+this.jsonHeaderObject);
			if(this.jsonHeaderObject ==null || this.jsonHeaderObject.entrySet().isEmpty() ){
				getDetails(container);
			}
			if(ele.isJsonObject()) {
				JsonObject response = ele.getAsJsonObject();

				logger.info("My response print here::::" + response);

				String docId = container.getDocId();
				logger.info("save changed  update :::" + RedisHelper.hgetAll(redisKey + docId + ":save"));
				logger.info("save changed  update :::" + RedisHelper.hgetAll(redisKey + docId + ":save_modified"));
				if (response.has("success")) {
					JsonObject success = response.get("success").getAsJsonObject();
					if (success.has("modified_data")) {
						JsonArray modifiedSuccess = success.get("modified_data").getAsJsonArray();
						successHandling(container, modifiedSuccess);
					}
					if (success.has("added_data")) {
						JsonArray addedSuccess = success.get("added_data").getAsJsonArray();
						addedDataHandler(container, addedSuccess);
					}
				}

				if (response.has("failure")) {

					JsonObject failure = response.get("failure").getAsJsonObject();
					//Sheet sheet = container.getWorkbook(null)..getSheetByAssociatedName("0#");
					//readErrorJsonFromDFS(container,false);


					if (failure.has("added_data")) {
						JsonArray addedError = failure.get("added_data").getAsJsonArray();
						decideErroDenotationMethod(container, addedError, true, true,true);

					}
					if (failure.has("modified_data")) {
						JsonArray errorjson = failure.get("modified_data").getAsJsonArray();

						decideErroDenotationMethod(container, errorjson, true, true,false);
					}
				}


				if(response.has(Constants.JSON_ROW_INDEX_ERROR)){


					setRowError(container,response.get(Constants.JSON_ROW_INDEX_ERROR).getAsJsonArray());
					logger.info("my row error print here:::");
					resp.set("rowerror",true);
					logger.info("my row error print here:::"+resp);
				}
				//Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");
				if (response.has("dependent_values")) {
					JsonArray values = response.get("dependent_values").getAsJsonArray();

					JsonObject obj = new JsonObject();
					HashMap apicolIndex = new HashMap<>();
					//if(this.colIndex == null || this.colIndex.isEmpty()){

					apicolIndex = this.getAllColIndexAPIname(container);

					//}
					Set set = new HashSet<>();
					for(int i=0;i< values.size();i++){

						JsonObject valObj = values.get(i).getAsJsonObject();

						for (Entry<String, JsonElement> _object : valObj.entrySet()) {

							set.add(_object.getKey().toString());

						}
//


					}

					Map result = this.findAList(sheet,set,0,0,"rowindexsearch");//No I18N
					logger.info("myd ependent values2:::"+result+":::"+this.rowIndex);
//
					for (int i = 0; i < values.size(); i++) {
						logger.info("myd i1:::"+i+":::");
						JsonObject valObj = values.get(i).getAsJsonObject();

						for (Entry<String, JsonElement> _object : valObj.entrySet()) {

							String key = _object.getKey();
							JsonArray depVal = _object.getValue().getAsJsonArray();
							for (int j = 0; j < depVal.size(); j++) {

								JsonArray _val = depVal.get(j).getAsJsonArray();
								logger.info("add fdfd  update123 :::" + j+"::::"+apicolIndex);

								String colVal = _val.get(0).getAsString();
								String val = _val.get(1).getAsString();
								int row = result.containsKey(key)?(int) result.get(key):-1;
								int col = apicolIndex.containsKey(colVal)?(int) apicolIndex.get(colVal):-1;
								logger.info("add changed  update1 :::" + row +":::"+col);
								if(row != -1 && col!= -1) {

									Cell cell = sheet.getCell(row, col);
									ActionUtil.setCellValue(cell, val, null,EngineConstants.DEFAULT_LOCALE,'.',',',null, null);

									logger.info("add changed  update1 :::" + val);
								}
							}


						}
					}

				}
				if(response.has("message")){
					String msg = response.get("message").getAsString();
					resp.put("message",msg);
				}
				logger.info("My status print here:::"+response.has("status"));
				if(response.has("status")){
					logger.info("My status print here:::"+response.get("status"));
					String msg = response.get("status").getAsString();
					resp.put("status",msg);

				}

				if(this.cellIndex_sR != -1){
					resp.put("row",this.cellIndex_sR);
					resp.put("col",this.cellIndex_sC);
					this.cellIndex_sR = -1;
					this.cellIndex_sC = -1;
				}
				//
				logger.info("My status print here:::"+resp);
				return resp;

				//sthis.writeErrorJsoninDFS(container);


				// respMessage.get("mess")
				// String success = respMessage.getString("success");
				// String message = respMessage.getString("message");
				// JsonArray errorjson = (JsonArray) respMessage.get("failure");
				// JsonArray success = (JsonArray) respMessage.get("success");
				// //this.errorJsonArray = errorjson;
				// String mode = respMessage.getString("mode");
				// setErrorDenotationNew(container.getWorkbook(null).getSheet(0),errorjson);
				// //
				// doSuccessAction(container.getWorkbook(null).getSheet(0),errorjson,mode);

			}

		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in JSON import", e);
		}
		return resp;
	}
	public HashMap constructKeyMAp(){
		String key = null;

		HashMap keyList = new HashMap();
		if(this.errorJsonArray != null){
			for(int  i=0;i<this.errorJsonArray.size();i++){
				JsonElement element = this.errorJsonArray.get(i);

				if (element.isJsonObject()) {
					JsonObject _object = element.getAsJsonObject();
					key = _object.get("uniquekey").getAsString();

				} else {
					JsonArray jsonArray = element.getAsJsonArray();
					key = jsonArray.get(0).getAsString();
				}
				keyList.put(key, i);

			}
		}
		return keyList;
	}

	public void successHandling(WorkbookContainer container, JsonArray success) {
		try {
			//List<DataRange> listOfDataRanges = new ArrayList();
			List cellRanges = new ArrayList();
			Workbook workbook = container.getWorkbook(null);
			Sheet sheet = workbook.getSheetByAssociatedName("0#");
			Set set = new HashSet<>();
			for (int i = 0; i < success.size(); i++) {
				set.add(success.get(i).getAsString());

			}
			if(this.errorJsonArray != null && this.errorJsonArray.isJsonNull()){
				for(int i=0;i<this.errorJsonArray.size();i++){
					JsonElement element = this.errorJsonArray.get(i);

					if (element.isJsonObject()) {
						JsonObject _object = element.getAsJsonObject();
						String key = _object.get("uniquekey").getAsString();
						if(set.contains(key)){
							this.errorJsonArray.remove(i);
						}


					} else {
						JsonArray jsonArray = element.getAsJsonArray();
						String key = jsonArray.get(0).getAsString();
						if(set.contains(key)){
							this.errorJsonArray.remove(i);
						}
					}
				}
			}




			Map result = findAList(sheet, set, 0, 0, "rowindexsearch");// No I18N
			// I18N
			for (int j = 0; j < success.size(); j++) {
				int rowIndex = (int) result.get(success.get(j).getAsString());
				//Cell cell =sheet.getCell(rowIndex, colIndex)
				ConditionFormatUtils.clearConditionalStyleFormat(sheet, rowIndex, 0, rowIndex, sheet.getUsedColumnIndex());

				List<DataRange> listOfDataRanges = new ArrayList<>();
				DataRange dataRanges = new DataRange(sheet.getAssociatedName(), rowIndex, 0, rowIndex, sheet.getUsedColumnIndex());
				listOfDataRanges.add(dataRanges);
				ActionUtil.cellCommentAction(workbook,listOfDataRanges,null,"remove",null,null,null,null);//No I18N
				logger.info("patch print::");
				ZSColor bgColor = ZSColor.getInstance("transparent");
				StyleActionUtil.setRangeColor(workbook,listOfDataRanges,bgColor);



//
//
			}


			//ActionUtil.deleteRow(container.getWorkbook(null), listOfDataRanges, false);
		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in JSON Save", e);
		}

	}

	private void addedDataHandler(WorkbookContainer container, JsonArray added) {
		try {
			Set keySet = null;
			// int pkColIndex=
			// Integer.parseInt(RedisHelper.hget(container.getDocId()+"pkcolIndex","pkcolIndex"));
			HashMap<String,String> idMap = new HashMap();
			JsonElement element = added.get(0);
			if(element.isJsonObject()) {
				for (int i = 0; i < added.size(); i++) {

					JsonObject addedEntity = added.get(i).getAsJsonObject();
					//idMap.put(addedEntity.get(0), addedEntity.get(1));
					for (Entry<String, JsonElement> _object : addedEntity.entrySet()) {
						String key = _object.getKey();
						idMap.put(_object.getKey(), _object.getValue().getAsString());
					}

				}
			}else{
				for (int i = 0; i < added.size(); i++) {

					JsonArray addedEntity = added.get(i).getAsJsonArray();
					idMap.put(addedEntity.get(0).getAsString(), addedEntity.get(1).getAsString());
				}
			}
			keySet = idMap.keySet();
			Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");
			Map find = null;
			if(rowIndex_added != null){
				find = findAList(sheet, keySet, 0, 0, "rowindexsearch");// No I18N
			}	else{
				find = rowIndex_added;
			}
			Iterator itr = idMap.entrySet().iterator();
			while (itr.hasNext()) {
				Map.Entry pair = (Map.Entry) itr.next();
				String id =(String) pair.getKey();
				String val = pair.getValue() +"";
				String val1 = (String) idMap.get(id);
				logger.info("add changed  update :::"+id+"::::"+val+"::::"+val1);
				int row = (int)find.get(id);
				logger.info("add changed  update1 :::"+row);
				Cell cell = sheet.getCell(row, 0);
				ActionUtil.setCellValue(cell, val1, null,EngineConstants.DEFAULT_LOCALE,'.',',',null, null);

				logger.info("add changed  update 2:::"+cell.getValue());
				rowIndex.put(val, row);
				ConditionFormatUtils.clearConditionalStyleFormat(sheet, row, 0, row, sheet.getUsedColumnIndex());
				List<DataRange> listOfDataRanges = new ArrayList<>();
				DataRange dataRanges = new DataRange(sheet.getAssociatedName(), row, 0, row, sheet.getUsedColumnIndex());
				listOfDataRanges.add(dataRanges);
				ActionUtil.cellCommentAction(sheet.getWorkbook(),listOfDataRanges,null,"remove",null,null,null,null);//No I18N
				ZSColor bgColor = ZSColor.getInstance("transparent");
				StyleActionUtil.setRangeColor(sheet.getWorkbook(),listOfDataRanges,bgColor);
//
//
				if(rowIndex.containsKey(id)){
					rowIndex_added.remove(id);
				}

			}
		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in jsonimport", e);

		}
	}

	private void setNotes(Sheet sheet) {
		try {
			this.constructIndexMap(sheet, 0, notesJsonArray);
			if (notesJsonArray != null) {
				for (int i = 0; i < notesJsonArray.size(); i++) {
					JsonElement element = notesJsonArray.get(i);
					String uniqueKey;
					String msg = null;
					int row = 0;
					if (element.isJsonObject()) {
						JsonObject _object = element.getAsJsonObject();
						uniqueKey = _object.get("uniquekey").toString();
						msg = _object.get("msg").toString();
						if (this.rowIndex.containsKey(uniqueKey)) {
							row = (int) this.rowIndex.get(uniqueKey);
							// }else{
							//
							//// //TODO need to discuss and solve this case
							//
						}
					} else {
						JsonArray jsonArray = element.getAsJsonArray();
						uniqueKey = jsonArray.get(0).getAsString();
						row = (int) this.rowIndex.get(uniqueKey);

						msg = jsonArray.get(1).getAsString();

					}

					Cell cell = sheet.getCell(row, sheet.getUsedColumnIndex());

					cell.setValue(new ValueI(Type.STRING, msg));

				}

			}
		} catch (Exception e) {
			logger.log(Level.WARNING, "Problem in jsonimport", e);

		}
	}

	private JsonObject setErrorDenotationNew(WorkbookContainer container, JsonArray errorJson,boolean setCF,boolean iserrorAppend,boolean isadded) {
		logger.info("Inside setErrorDenotationNew"+errorJson);
		JsonObject errorJsonInDB= new JsonObject();
		String docId = container.getDocId();
		try {
			Sheet sheet = container.getWorkbook(null).getSheetByAssociatedName("0#");

			constructIndexMapNew(sheet, 0, errorJson);
			for (int i = 0; i < errorJson.size(); i++) {
				int row = 0;
				int column = 0;
				String errorType = "error";// No I18N
				String msg = "";
				JsonObject object = errorJson.get(i).getAsJsonObject();
				for (Entry<String, JsonElement> _object : object.entrySet()) {
					String key = _object.getKey();
					JsonArray value = _object.getValue().getAsJsonArray();
					row = (int) this.rowIndex.get(key);
					for (int j = 0; j < value.size(); j++) {

						JsonArray _value = value.get(j).getAsJsonArray();
						String temp = value.get(0).getAsString();
						column = this.colIndex.get(temp);
						msg = _value.get(1).getAsString();
						if (_value.size() >= 3) {
							errorType = _value.get(2).getAsString();
						}

						Cell cell = sheet.getCell(row, column);
						String sheetname = sheet.getName();
						String cellId = cell.toString();
						String[] _cellid = cellId.split(":");
						String range = sheetname + "." + _cellid[0];
						if(isadded) {
							Cell cell1 = sheet.getCell(row, 0);
							//ActionUtil.setCellValue(cell1, "");
							try {
								if (RedisHelper.hexists(redisKey + docId + ":save", cell1.getContent())) {
									String val = RedisHelper.hget(redisKey + docId + ":save", cell1.getContent());// NO I18N
									logger.info("_value::::" + val);
									RedisHelper.hset(redisKey + docId + ":edit", cell1.getContent(),val);// NO I18N
								}
							}catch (Exception e){
								logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e);
							}
						}
						if(setCF){
							JSONObjectWrapper cfJson = new JSONObjectWrapper(
									"{\r\n  \"cond1\": -1,\r\n  \"cr_ty1\": \"FORMULA\",\r\n  \"cval1\": \"if(" + _cellid[0]+ "="// No I18N
											+ _cellid[0]	+ ")\",\r\n  \"cstyle1\": \" #FBD1CF~none~false~false~none~false\",\r\n  \"noofrules\": 1\r\n  }"); // No I18N
							// No I18N

							if ("info".equalsIgnoreCase(errorType)) {
								cfJson.put("cstyle1", "#FCFACD~none~false~false~none~false");
							} else if ("error".equalsIgnoreCase(errorType)) {
								cfJson.put("cstyle1", " #FBD1CF~none~false~false~none~false");
							} else if ("warning".equalsIgnoreCase(errorType)) {
								cfJson.put("cstyle1", "#FDE7CC~none~false~false~none~false");
							}
							if(this.cellIndex_sR ==-1){
								this.cellIndex_sR = cell.getRowIndex();
								this.cellIndex_sC =cell.getColumnIndex();
							}
							DataRange applyRange = new DataRange(sheet.getAssociatedName(), cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex());
							ConditionFormatUtils.addConditionalStyles(sheet, cfJson, Arrays.asList(applyRange),
									ActionConstants.CONDITIONAL_FORMAT_APPLY);
							Annotation annotation = new Annotation();
							cell.setAnnotation(annotation);
							annotation.setContent(msg);
						}else{
							JsonArray list = new JsonArray();

							if (errorJsonInDB.has(errorType)) {
								list = errorJsonInDB.get(errorType).getAsJsonArray();

							}
							JsonArray _list = new JsonArray();

							_list.add(range);
							_list.add(msg);
							_list.add(row);
							list.add(_list);
							errorJsonInDB.add(errorType, list);

						}



					}

					return errorJsonInDB;
				}
			}
		} catch (Exception e) {
			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e);
		}
		return errorJsonInDB;
	}

	private void doSuccessAction(Sheet sheet, JsonArray succesJson, String mode) {
		switch (mode) {
			case "remove":
				for (int i = 0; i < succesJson.size(); i++) {
					String value = succesJson.get(i).getAsString();

				}
				break;
			case "modify":
				break;
		}
	}


	private void setDetails(WorkbookContainer workbookcontainer) {
		try {
			String userName = workbookcontainer.getDocOwner();
			Long remoteBookId = Long.parseLong(workbookcontainer.getResourceKey());

			JsonObject metadata = new JsonObject();

			if (this.jsonHeaderObject != null && !this.jsonHeaderObject.isJsonNull()) {
				metadata.add("Header", this.jsonHeaderObject);
			}
			if(this.errorJsonArray != null){
				metadata.add("fetchError", new JsonParser().parse("true"));
			}

			if (this.displayNameMap != null) {

				metadata.add("displayname", getDisplayNameAsJsonObject(this.displayNameMap));
			}
			if (this.mandatoryMap != null) {

				metadata.add("mandatory",  getDisplayNameAsJsonObject(this.mandatoryMap));
			}
			logger.info("My metadata while set list print here34234:::"+this.dateCol);
			if (this.dateCol != null) {
				logger.info("My metadata while set list print here:sdfsdfs::"+this.dateCol);
				metadata.add("dateList",  new JsonParser().parse(this.dateCol.toString()).getAsJsonObject());
			}
//				if(this.maxRow != -1) {
//					metadata.add("maxRow",new JsonParser().parse(this.maxRow+""));
//				}
//				if(this.dateCol)
			logger.info("My metadata while set list print here:::"+metadata);

			writeJsonToDFS(container,metadata);
			workbookcontainer.setJsonCache(new JSONObjectWrapper(metadata.toString()));
		}
		catch (Exception e) {
			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e);

		}
		// HashMap updater = this.remotebook.getMapHolder();

	}
	private void getDetails(WorkbookContainer workbookcontainer) {
		try {
			JsonObject metadata = new JsonObject();
			JSONObjectWrapper cache = workbookcontainer.getJsonCache();
			//JSONObject tempCache = new JSONObject();

			if(cache == null || cache.isEmpty()) {
				metadata = readJsonFromDFS(workbookcontainer);
				if(metadata == null || metadata.isJsonNull()){
					metadata = getDetailsFromDB(workbookcontainer);
				}
			}else if(this.jsonHeaderObject == null || this.jsonHeaderObject.entrySet().isEmpty()){
				metadata = new JsonParser().parse(cache.toString()).getAsJsonObject();
			}
			logger.info("My metadata list print here:::"+metadata);
//			String userName = workbookcontainer.getDocOwner();
//			Long remoteBookId = Long.parseLong(workbookcontainer.getResourceKey());
//
//			JsonObject metadata = new JsonObject();
			if(metadata.has("Header")){
				this.jsonHeaderObject = metadata.get("Header").getAsJsonObject();
				//tempCache.put("Header",this.jsonHeaderObject.toString());
			}


//			if(this.errorJsonArray != null){
//				metadata.add("fetchError", new JsonParser().parse("true"));
//			}
			if(metadata.has("displayname")){
				JsonObject displayName = metadata.get("displayname").getAsJsonObject();

				Gson gson = new Gson();

				this.displayNameMap = gson.fromJson(displayName, this.displayNameMap.getClass());


				//tempCache.put("displayname", displayNameMap);
				//workbookcontainer.setJsonCache(cache);
				//this.displayNameMap =
			}

			if(metadata.has("mandatory")){
				JsonObject mandatory = metadata.get("mandatory").getAsJsonObject();

				Gson gson = new Gson();

				this.mandatoryMap = gson.fromJson(mandatory, this.mandatoryMap.getClass());


				//tempCache.put("mandatory", mandatoryMap);
				//workbookcontainer.setJsonCache(cache);
				//this.displayNameMap =
			}

			if(metadata.has("dateList")){

				this.dateCol = new JSONObjectWrapper(metadata.get("dateList").toString());


				//cache.put("mandatory", mandatoryMap);
				//workbookcontainer.setJsonCache(cache);
				//this.displayNameMap =
			}
//			if(metadata.has("maxRow")){
//				this.maxRow = metadata.get("maxRow").getAsInt();
//			}
			if(cache == null || cache.isEmpty()) {
				workbookcontainer.setJsonCache(new JSONObjectWrapper(metadata.toString()));
			}

		}
		catch (Exception e) {
			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e);

		}
		// HashMap updater = this.remotebook.getMapHolder();

	}

	private JsonObject getDetailsFromDB(WorkbookContainer workbookcontainer){
		try {
			Row rmbRow = RemoteUtils.fetchRemoteBookRow(Long.parseLong(workbookcontainer.getResourceKey()), null, false,
					workbookcontainer.getDocOwner());
			if (rmbRow != null) {
				String meta_info = (String) rmbRow.get("META_DATA");
				if(meta_info!= null){

					JsonObject metadata = new JsonParser().parse(meta_info).getAsJsonObject();
					return metadata;


				}
			}

		} catch (Exception e) {

			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e.getCause());
		}
		return null;
	}
	private  void writeJsonToDFS(WorkbookContainer container,JsonObject data) throws Exception {
		BufferedWriter bw = null;
		Map writeInfo = null;
		try {

			Long resourceId = container.getFragmentId(ZSStore.FileName.JSON_HEADER, true, null);
			writeInfo = container.getWriteInfo(resourceId, ZSStore.FileName.JSON_HEADER, ZSStore.FileExtn.JSON, null);
			OutputStream os = (OutputStream) writeInfo.get("OS"); //No I18N
			bw = new BufferedWriter(new OutputStreamWriter(os));
			bw.write(data.toString());
		} catch (Exception e) {
			if (writeInfo != null) {
				writeInfo.put("fileWritten", false); //No I18N
			}

			logger.log(Level.WARNING, "[JSON PARSER] : Error while writing the JSON  file to DFS.", e); //No I18N
			throw new Exception("Problem while saving document", e); //No I18N
		} finally {
			if (bw != null) {
				try {
					bw.close();
				} catch (IOException ex) {
					logger.log(Level.WARNING, null, ex);
				}
			}
			container.finishWrite(writeInfo);
		}


	}
	private static JsonObject readJsonFromDFS(WorkbookContainer container) throws Exception
	{

		long docID = Long.parseLong(container.getDocId());
		DataObject dataObj = ZSStore.getFragmentDO(container.getDocOwner(), docID);
		Long[] fragmentResourceIds = ZSStore.getAllFragmentIdS(dataObj);
		Store store = container.getStore(fragmentResourceIds, ZSStore.FileName.JSON_HEADER);

		BufferedReader br = null;
		InputStream in = null;
		try
		{
			long fragmentId = ZSStore.getFragmentId(dataObj, ZSStore.FileName.JSON_HEADER.toString().toLowerCase());
			//LOGGER.log(Level.INFO, "*************fragment ID : {0}", fragmentId);
			if(fragmentId > 0)
			{
				in = container.getReadStream(store, fragmentId, ZSStore.FileName.JSON_HEADER, ZSStore.FileExtn.JSON, null);
				if(in != null)
				{
					br = new BufferedReader(new InputStreamReader(in));
					String data  =  br.readLine();
					return new JsonParser().parse(data).getAsJsonObject();
				}
			}
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "[JSON PARSER] : Error while reading the JSON  file From DFS: {0}", e);  //No I18N
			throw new Exception("Problem while parsing document", e); //No I18N
		}
		finally
		{
			if(br != null)
			{
				try
				{
					br.close();
				}
				catch (IOException ex)
				{
					logger.log(Level.WARNING, null, ex);
				}
			}
			if(in != null)
			{
				try
				{
					in.close();
				}
				catch (IOException ex)
				{
					logger.log(Level.WARNING, null, ex);
				}
			}
		}
		return null;
	}


	private static JsonObject getDisplayNameAsJsonObject(Map<String, Object> displayName) {
		// Iterator itr = displayName.entrySet().iterator();
		JsonObject obj = new JsonObject();
		Gson gsonObj = new Gson();
		String jsonStr = gsonObj.toJson(displayName);
		obj = new JsonParser().parse(jsonStr).getAsJsonObject();

		// while(itr.hasNext()){
		// String key = (String) itr.next();
		// String value =(String) displayName.get(key);
		// obj.addProperty(key, new JsonParser().parse(value).getAsString());
		// }
		return obj;
	}

//	private static BidiMap getDisplayName(WorkbookContainer workbookcontainer) {
//		JSONObject cache = workbookcontainer.getJsonCache();
//		if (!cache.isEmpty() && cache.has("displayname") && cache.get("displayname") != null) {
//			String val = cache.get("displayname").toString();
//			// JsonObject obj = new JsonObject(val);
//			Gson gson = new Gson();
//
//			BidiMap displayNameMap = new DualHashBidiMap();
//
//			displayNameMap = gson.fromJson(val, displayNameMap.getClass());
//
//			return displayNameMap;
//		} else {
//			return getDisplayNameinDB(workbookcontainer);
//		}
//
//	}

//	private static JsonObject getDateList(WorkbookContainer workbookcontainer) {
//		JSONObject cache = workbookcontainer.getJsonCache();
//		if (!cache.isEmpty() && cache.has("dateList") && cache.get("dateList") != null) {
//			String val = cache.get("dateList").toString();
//			JsonObject obj = new JsonParser().parse(val).getAsJsonObject();
//
//
//
//			return obj;
//		} else {
//			return getDateListinDb(workbookcontainer);
//		}
//
//	}
//	private static JsonObject getDateListinDb(WorkbookContainer workbookcontainer) {
//		try {
//			Row rmbRow = RemoteUtils.fetchRemoteBookRow(Long.parseLong(workbookcontainer.getResourceKey()), null, false,
//					workbookcontainer.getDocOwner());
//			if (rmbRow != null) {
//				String meta_info = (String) rmbRow.get("META_DATA");
//				JsonObject metadata = new JsonParser().parse(meta_info).getAsJsonObject();
//				if (metadata.has("dateList")) {
//					JsonObject dateList = metadata.getAsJsonObject("dateList"); // No I18N
//					// I18N
//					JSONObject cache = workbookcontainer.getJsonCache();
//					cache.put("dateList", dateList.toString());
//					workbookcontainer.setJsonCache(cache);
//					return dateList;
//
//				}
//			}
//
//		} catch (Exception e) {
//
//			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e.getCause());
//		}
//		return null;
//		// HashMap updater = this.remotebook.getMapHolder();
//
//	}
//
////	private static BidiMap getDisplayNameinDB(WorkbookContainer workbookcontainer) {
////
////		try {
////			Row rmbRow = RemoteUtils.fetchRemoteBookRow(Long.parseLong(workbookcontainer.getResourceKey()), null, false,
////					workbookcontainer.getDocOwner());
//			if (rmbRow != null) {
//				String meta_info = (String) rmbRow.get("META_DATA");
//				JsonObject metadata = new JsonParser().parse(meta_info).getAsJsonObject();
//				if (metadata.has("displayname")) {
//
//					// metadata = metadata.getAsJsonObject("displayname");//NO
//					// I18N
//					// BidiMap displayNameMap = (BidiMap)
//					// metadata.get("displayname");
//					JsonObject _metadata = metadata.getAsJsonObject("displayname");// No I18N
//					// I18N
//					Gson gson = new Gson();
//					// Type type = new TypeToken<BidiMap>(){}.getType();
//					BidiMap displayNameMap = new DualHashBidiMap();
//					displayNameMap = gson.fromJson(_metadata, displayNameMap.getClass());
//
//					JSONObject cache = workbookcontainer.getJsonCache();
//					cache.put("displayname", displayNameMap);
//					workbookcontainer.setJsonCache(cache);
//					return displayNameMap;
//				}
//				return null;
//
//			}
//		} catch (Exception e) {
//
//			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e);
//		}
//		return null;
//		// HashMap updater = this.remotebook.getMapHolder();
//
//	}



//	private static JsonObject getHeader(WorkbookContainer workbookcontainer) {
//		try {
//			JSONObject cache = workbookcontainer.getJsonCache();
//			if (!cache.isEmpty() && cache.has("header")) {
//				JSONObject meta_info = (JSONObject) cache.get("header");
//				JsonObject metadata = new JsonParser().parse(meta_info.toString()).getAsJsonObject();
//				return metadata;
//			} else {
//				return readJsonToDFS(workbookcontainer);
//			}
//		}catch (Exception e){
//			logger.log(Level.WARNING,"[JSON PARSER] Problem while reading header",e);
//		}
//	return null;
//	}

//	private static JsonObject getHeaderinDB(WorkbookContainer workbookcontainer) {
//
//		try {
//			Row rmbRow = RemoteUtils.fetchRemoteBookRow(Long.parseLong(workbookcontainer.getResourceKey()), null, false,
//					workbookcontainer.getDocOwner());
//			if (rmbRow != null) {
//				String meta_info = (String) rmbRow.get("META_DATA");
//				JsonObject metadata = new JsonParser().parse(meta_info).getAsJsonObject();
//				if (metadata.has("Header")) {
//					JsonObject header = metadata.getAsJsonObject("Header"); // No I18N
//					// I18N
//					JSONObject cache = workbookcontainer.getJsonCache();
//					cache.put("header", header.toString());
//					workbookcontainer.setJsonCache(cache);
//					return header;
//
//				}
//			}
//
//		} catch (Exception e) {
//
//			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e.getCause());
//		}
//		return null;
//		// HashMap updater = this.remotebook.getMapHolder();
//
//	}

	private void constructHeaderJson(JsonArray headerJsonArray) {
		if(this.jsonHeaderObject ==null){
			this.jsonHeaderObject = new JsonObject();
		}
		for (int i = 0; i < headerJsonArray.size(); i++) {
			JsonObject _obj = headerJsonArray.get(i).getAsJsonObject();
			this.jsonHeaderObject.add(_obj.get("name").getAsString(), _obj);
		}
	}
	private void setUISettings(WorkbookContainer container){
		try {
			Workbook workbook = container.getWorkbook(null);
			String fontname = null;
			String fontsize = null;
			Sheet sheet  = workbook.getSheetByAssociatedName("0#");
			int freezeCol = -1;
			int rowHeight = -1;
			CellStyle defaultCellStyle = workbook.getDefaultCellStyle();
//
//
			if(this.ui_settings.has("row_height")){
				 rowHeight = this.ui_settings.get("row_height").getAsInt();
				workbook.setMinRowHeight(rowHeight);

				//ActionUtil.setRowsHeight(sheet,0,0, rowHeight+"pt",true,"");


			}
			if(this.ui_settings.has("Font_Name")) {
				fontname = this.ui_settings.get("Font_Name").getAsString();
				defaultCellStyle.setProperty(TextStyle.Property.FONTNAME, fontname);
			}
			if(this.ui_settings.has("Font_Size")) {
				fontsize = this.ui_settings.get("Font_Size").getAsString();
				defaultCellStyle.setProperty(TextStyle.Property.FONTSIZE, fontsize);

			}
			if(this.ui_settings.has("Freeze_Columns")){
				freezeCol = this.ui_settings.get("Freeze_Columns").getAsInt();
				ActionUtil.freezePaneCols(workbook.getSheetByAssociatedName("0#"),1, freezeCol);

			}

			if(this.ui_settings.has("halign")){
				String halign = this.ui_settings.get("halign").getAsString();
				StyleActionUtil.setRangeHalign(sheet,0,0,1000,20,halign);
			}
			if(this.ui_settings.has("valign")){
				String valign = this.ui_settings.get("valign").getAsString();
				StyleActionUtil.setRangeValign(sheet,0,0,1000,20,valign);
			}

			if(this.ui_settings.has("zoom")){
				int zoom = this.ui_settings.get("zoom").getAsInt();
				ActionUtil.setSheetZoom(workbook,sheet.getName(),zoom);
			}
			if(this.ui_settings.has("indent")){
				int indent = this.ui_settings.get("indent").getAsInt();
				StyleActionUtil.setRangeIndent(sheet, 0, 0, Utility.MAXNUMOFROWS-1, Utility.MAXNUMOFCOLS -1,indent, 0, 0);
			}
			StyleActionUtil.setRangeWrap(sheet,0,1,1000,20,"2");



//			CellStyle defaultCellStyle = workbook.getDefaultCellStyle();
//			defaultCellStyle.setProperty(TextStyle.Property.FONTNAME, fontname);
//			defaultCellStyle.setProperty(TextStyle.Property.FONTSIZE, fontsize);


			defaultCellStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
			StyleActionUtil.setRangeTextColor(sheet,0,0,Utility.MAXNUMOFROWS-1,Utility.MAXNUMOFCOLS-1,ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
		}catch (Exception e){
			logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e.getCause());
		}

	}
	public static boolean getSaveStatus(WorkbookContainer container) {
		try {

			String docId = container.getDocId();
			if ((RedisHelper.exists(redisKey + docId + ":edit_modified") && RedisHelper.hlen(redisKey + docId + ":edit_modified") > 0) || (RedisHelper.exists(redisKey + docId + ":edit") && RedisHelper.hlen(redisKey + docId + ":edit") > 0)) {
				logger.info("Get Save Status::::true");
				return true;
			}
		} catch (Exception e) {
				logger.log(Level.WARNING, "PROBLEM IN JSON IMPORT", e.getCause());
		}
		logger.info("Get Save Status::::false");
		return false;
	}

}
