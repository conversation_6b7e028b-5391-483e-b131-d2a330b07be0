package com.zoho.sheet.conversion;

import java.io.*;
import java.util.ArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletResponse;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.WorkbookContainer.ContainerEntity;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.ZSStore;
import com.adventnet.zoho.websheet.model.util.ZSStore.FileName;
import com.adventnet.zoho.websheet.store.Store;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.iam.security.UploadedFileItem;

import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.zfsng.constants.ResourceStatus;
import com.zoho.zfsng.constants.ResourceType;


public class DCMigrationAction extends StrutsRequestHandler {

    public static final Logger LOGGER = Logger.getLogger(DCMigrationAction.class.getName());

    public static JSONArrayWrapper readCsvFile(UploadedFileItem fileItem, boolean allVersionMigration, boolean isTopVersion,String versionId, HttpServletResponse response) throws IOException {
        JSONArrayWrapper dcMigrationArray = new JSONArrayWrapper();
        JSONObjectWrapper dcMigrationContents;
        File file = fileItem.getUploadedFile();
        FileInputStream fis = new FileInputStream(file);
        String desc = "";
        int rType;
        int chartExt;
        int fileSts = 0;
        Store store = null;
        boolean isSxc = false;
        boolean isValid = true;
        String docOwner = "";
        String documentId = "";

        try (BufferedReader br = new BufferedReader(new InputStreamReader(fis))) {
            String rid;

            boolean expStatus = true;

            while ((rid = br.readLine()) != null) {
                String[] ridList = rid.split(",");
                for(int i=0; i < ridList.length;i++)
                {
                    dcMigrationContents = new JSONObjectWrapper();
                    try{
                        ResourceInfo resInfo = ZohoFS.getResourceInfo(ridList[i]);
                        if (resInfo == null) {
                            isValid = false;
                            desc = "Seems File is not available"; // No i18N
                        } else {
                            docOwner = DocumentUtils.getZUserName(resInfo.getSpaceId());
                            documentId = DocumentUtils.getDocumentId(ridList[i], docOwner);
                            if (documentId == null) {
                                isValid = false;
                                desc = "Document not found in DB"; // No i18N
                            }
                        }
                        if (!isValid) {
                            dcMigrationContents.put("rid", ridList[i]);
                            dcMigrationContents.put("fileStatus", 0);
                            dcMigrationContents.put("isSXC", "Extension not found"); // No i18N
                            dcMigrationContents.put("desc", desc); // No i18N
                            dcMigrationContents.put("chartExt", "chart extension not found"); // No i18N
                            dcMigrationArray.put(dcMigrationContents);
                            continue;
                        }

                        dcMigrationContents.put("rid",ridList[i]);
                        switch (resInfo.getStatus()){
                            case ResourceStatus.ACTIVE:
                            case ResourceStatus.TRASHED:
                            case ResourceStatus.TRASHED_BY_PARENT:
                            case ResourceStatus.TRASHED_ALL:
                            case ResourceStatus.DELETED:
                            case ResourceStatus.DELETED_BY_PARENT:
                            case ResourceStatus.PERMANENTALY_DELETED:
                            case ResourceStatus.PERMANENTALY_DELETED_BY_PARENT:
                            case ResourceStatus.DELETED_BY_SHARED:
                            case ResourceStatus.DELETED_ALL:
                                fileSts = resInfo.getStatus();
                                break;
                        }
                        dcMigrationContents.put("fileStatus", fileSts);

                        ContainerEntity entity = WorkbookContainer.getEntity();
                        entity.docOwner = docOwner;
                        entity.resourceId = ridList[i];
                        entity.docsSpaceId = resInfo.getOwner();
                        entity.creatorZuid = resInfo.getCreator();
                        entity.documentId = documentId;
                        entity.documentName = resInfo.getName();
                        WorkbookContainer container = new WorkbookContainer(entity);


                        if (versionId == null) {
                            String zfsngVersionNo = ZohoFS.getTopVersion(resInfo.getOwner(), ridList[i], true);
                            versionId = ZohoFS.getVersionIDForEditor(resInfo.getOwner(), ridList[i], zfsngVersionNo);
                            store = container.getStore(Long.parseLong(versionId), FileName.VERSION);
                            isSxc = container.isFileExist(store, Long.parseLong(versionId), FileName.VERSION , ZSStore.FileExtn.SXC);
                        }
                        dcMigrationContents.put("isSXC", isSxc);
    //                        String versionInfo = null;
    //                        try {
    //                            versionInfo = ZohoFS.getVersionInfoForId(resInfo.getOwner(), ridList[i], versionId);
    //                            if(versionInfo == null) {
    //                                LOGGER.info("versionInfo is null");
    //                                versionInfo = ZohoFS.getVersionInfoForIdWithDeleted(resInfo.getOwner(), ridList[i], versionId);
    //                            }
    //
    //
    //                        } catch (Exception ex) {
    //                            LOGGER.log(Level.WARNING, "Exception on DC migration ", ex);
    //                            return null;
    //                        }

                        byte[] zsheetContent = null;
    //                        try {
    //                            String zfsngVersionNo = (new JSONArrayWrapper(versionInfo)).getJSONObject(0).getString("version_number");
    //                            String versionNum = DocumentUtils.getVersionNoforZFSNGVersion(documentId, docOwner , versionId);
    //                            Exporter exporter = new Exporter(container, EngineConstants.ZSHEET_FILE_FORMAT.substring(1), versionNum, zfsngVersionNo, versionId, isTopVersion, allVersionMigration);
    //                            zsheetContent = exporter.getDocumentBytes();
    //                            dcMigrationContents.put("expStatus",expStatus);
    //                        } catch (Exception ex) {
    //                            dcMigrationContents.put("expStatus",false);
    //                            desc = "[ZSHEET_EXPORT] Exception on DC migration"; // No i18N
    //                            LOGGER.log(Level.WARNING,"[ZSHEET_EXPORT] Exception on DC migration "+ridList[i],ex);
    //                        }

                        if (zsheetContent == null) {
                            expStatus = false;
                            dcMigrationContents.put("expStatus",expStatus);
                            desc = "Exported Content is null or export failed"; // No i18N
                            LOGGER.log(Level.INFO, "Exported Content is null or export failed");
                        }
                        dcMigrationContents.put("desc",desc);

                        rType = resInfo.getResourceType();
                        chartExt = (rType == ResourceType.SHOW_CHART_WORKBOOK) ? rType : 0;

                        dcMigrationContents.put("chartExt",chartExt); // No i18N
                        dcMigrationArray.put(dcMigrationContents);
                    } catch (Exception e) {
                        desc = "Seems File is not available"; // No i18N
                        dcMigrationContents.put("rid", ridList[i]);
                        dcMigrationContents.put("fileStatus", 0);
                        dcMigrationContents.put("isSXC", "Extension not found"); // No i18N
                        dcMigrationContents.put("desc", desc); // No i18N
                        dcMigrationContents.put("chartExt", "chart extension not found"); // No i18N
                        dcMigrationArray.put(dcMigrationContents);
                        LOGGER.log(Level.WARNING, "Exception on DC migration ", ridList[i]);
                    }
                }

            }
        }catch (Exception e) {
            LOGGER.log(Level.INFO, "Error reading CSV file: ", e);
        } finally {
            if(fis != null){
                fis.close();
            }
        }
        return  dcMigrationArray;
    }

    public String execute() throws Exception {
        try {
            String versionId = request.getParameter("versionid");

            ArrayList< UploadedFileItem>uploadedFilesList = (ArrayList< UploadedFileItem>) request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST);
            UploadedFileItem uploadedFile = uploadedFilesList.get(0);

            boolean allVersionMigration = Boolean.parseBoolean(request.getParameter("migrate_all_versions"));
            boolean isTopVersion = Boolean.parseBoolean(request.getParameter("is_top_version"));
            HttpServletResponseWrapper.sendResponse(response, readCsvFile(uploadedFile, allVersionMigration, isTopVersion, versionId, response), HttpServletResponseWrapper.MimeType.JSON);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            LOGGER.log(Level.WARNING, "Exception on DC migration ", e);
        }
        return null;

    }
}
