//$Id$
package com.zoho.sheet.conversion;

import java.io.OutputStream;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.StoreFactory;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import com.zoho.sheet.action.FileReader;
import com.zoho.sheet.action.TableMetaHandler;
import com.zoho.sheet.api.APIError;
import com.zoho.sheet.exception.OfficeSuiteException;
import com.zoho.sheet.exception.ZohoSheetException;
import com.zoho.sheet.util.RemoteUtils;
import com.zoho.zfsng.constants.ZFSNGConstants;


public class RemoteImporter extends StrutsRequestHandler {
	
	Logger logger = Logger.getLogger(RemoteImporter.class.getName());
	private  String locale;
	
	public String execute() throws Exception {
//		try{
		logger.log(Level.INFO, "[CONVERSION:IMPORT] REMOTE.IMPORTER");
		if(Constants.IS_DISASTER_RECOVERY_SERVER){
			throwError(2502);
			return null;
		}
		
		String referer = request.getHeader("referer");
		String modeParam = request.getParameter("mode");
		String devicetype = request.getParameter("devicetype");
		String sheetUrl = EnginePropertyUtil.getSheetPropertyValue("SHEET_SERVER_URL"); //NO I18N
		String depicationBlogUrl = EnginePropertyUtil.getSheetPropertyValue("VIEW_DEPRICATION_INFO_BLOG_URL"); //NO I18N
		
		if(request.getRequestURI()!=null && (request.getRequestURI().contains("/view.do") || request.getRequestURI().contains("/importview.do"))) {
			try{
				if(!request.getRequestURI().contains("/importview.do")) {
					if(referer != null){
						if(referer.indexOf(sheetUrl) == -1){
							response.sendRedirect(depicationBlogUrl);
							return null;
						}
					}else{
						response.sendRedirect(depicationBlogUrl);
						return null;
					}
				}else {
					if(!"handheld".equals(modeParam)){
						if(referer != null){
							if(referer.indexOf(sheetUrl) == -1){
								response.sendRedirect(depicationBlogUrl);
								return null;
							}
						}else{
							response.sendRedirect(depicationBlogUrl);
							return null;
						}
					}
				}
			} catch(Exception e) {
				logger.info("Exception occured while checking param : "+e);
			}
		}
		
		RemoteBook remoteBook = getRemoteBook(request); 
		logger.info(remoteBook.toString());
		if((validateAPIKey(remoteBook, request) == null)) {
			throwError(2890);
		}
		
		String columnHeads = null;
		JSONObjectWrapper columnHeadsJson = new JSONObjectWrapper();
        Character txtQualifier = null;
        Character thousandSeptr = null;
        Character decimalSeptr = null;
        boolean includeFirstRow = true;
        int importFromRow = 0;
        String decimalSeptrParam = null;
        String thousandSeptrParam = null;
        String txtQualifierParam = null;
        String columnHeadsParam = null;
        String includeFirstRowParam = null;
        String importFromRowParam = null;
       
        String datepattern = request.getParameter("datepattern");
		String delimiter = request.getParameter("delimiter");
		String dateFormat = request.getParameter("dateFormat");
		txtQualifierParam = request.getParameter("txtQualifier");
		decimalSeptrParam = request.getParameter("decimalSeptr");
        thousandSeptrParam = request.getParameter("thousandSeptr");
        columnHeadsParam = request.getParameter("columnHeads");
        includeFirstRowParam = request.getParameter("includeFirstRow");
        importFromRowParam = request.getParameter("importFromRow");
        txtQualifier = txtQualifierParam != null ? txtQualifierParam.toString().charAt(0) : '\"';
		thousandSeptr = thousandSeptrParam != null ? thousandSeptrParam.toString().charAt(0) : null;
        decimalSeptr = decimalSeptrParam != null ? decimalSeptrParam.toString().charAt(0) : null;
        includeFirstRow = includeFirstRowParam != null ? Boolean.getBoolean(includeFirstRowParam.toString()) : true;
        importFromRow = importFromRowParam != null ? Integer.parseInt(importFromRowParam.toString()) : 0;
        columnHeads = columnHeadsParam != null ? columnHeadsParam : "{}";

        if (!columnHeads.equals("{}")) {
        	columnHeadsJson.put(JSONConstants.IMP_COLUMN_HEAD, columnHeadsParam);
        }
		logger.log(Level.INFO, "1.... [CONVERSION:IMPORT] REMOTE.IMPORTER");
		HashMap resMap = checkInputParams(remoteBook, request);
		logger.log(Level.INFO, "2...[CONVERSION:IMPORT] REMOTE.IMPORTER");
		
		this.locale = request.getParameter("locale");
		String password = request.getParameter("password");
		try{
			remoteBook.setUpdator(getDBUpdater(resMap));
			try {
					HashMap updater = remoteBook.getMapHolder();
					HashMap rbsm = (HashMap) updater.get("REMOTEBOOKS_STATE_TABLE");
					if(!remoteBook.isAuthCase) {
						if(remoteBook.isExistingDoc()) {
							remoteBook.setUserName(rbsm.get("SPACE_NAME").toString());
						}else if(!remoteBook.getUserName().equalsIgnoreCase(rbsm.get("SPACE_NAME").toString())) {
							remoteBook.setUserName(rbsm.get("SPACE_NAME").toString());
						}
					}
				}catch (Exception e) {
					logger.info("[CONVERSION:IMPORT] REMOTE.IMPORTER" + e);
				}
			logger.info(remoteBook.toString());
			
			FileReader fr = new FileReader(request, remoteBook.allowEmptyFileContent); //allowing empty content for scratch mode
			if(request.getRequestURI().contains("officeapi") ){
				String mode = remoteBook.getDocMode();
				if(fr.isEmptyFileUsed && ("view".equalsIgnoreCase(mode)  || "collabview".equalsIgnoreCase(mode))){
						//remoteBook.allowEmptyContent(false);
					String[] jsonArray = {"Mandatory parameter(s) missing","document"};//NO I18N	
					throwError(2837,"OFFICEAPI_ERROR",jsonArray);//NO I18N	
				}
			}
			
			try {

					if(fr.fileFormat.equals(EngineConstants.CSV_FILE_FORMAT) && isNullOrEmpty(delimiter)) {	//If No @param delimiter then try to read auto delimiter for TSV and CSV.
						delimiter = Character.toString(ImportExportUtil.getAutoDelimiter(fr.fileContent));
					}else if(fr.fileFormat.equals(EngineConstants.TSV_FILE_FORMAT) && isNullOrEmpty(delimiter)){
						delimiter = "\t";//NO I18N	
					}
			}catch (Exception e) {
				logger.log(Level.INFO,"[CONVERSION:IMPORT] REMOTE.IMPORTER Auto Delimiter Exception:",e);
			}
			
			boolean isFormatSupported = EngineConstants.IMPORT_DECRYPTION_SUPPORTED_FORMATS.contains(fr.fileFormat);
			String _uri = request.getRequestURI();
			if(!isNullOrEmpty(password)) {
				try{
					logger.log(Level.INFO,"[CONVERSION:NEW IMPORT] Remote Import with PASSWORD:",_uri);
					fr.fileContent = ImportExportUtil.decryptedDocument(fr.fileContent,fr.fileFormat,password);
					logger.log(Level.INFO, "[CONVERSION:NEW IMPORT] FILE-DECRYPTION DONE");
				}catch (Exception e) {
					logger.log(Level.INFO, "[CONVERSION:NEW IMPORT] FILE-DECRYPTION WRONGE PASSWORD:");
					
					if(_uri.contains("importview")){
					throwError(APIError.CONVERSION_FAILURE_INCORRECT_PASSWORD, null, response);
					}else{
						throwError(2508, null);
					}
					return null;
				}
			}
			//Importer importer = ImporterFactory.getImporterInstance(request,fr.fileFormat);
			Importer importer = ImporterFactory.getImporterInstance(fr.fileFormat, delimiter, dateFormat, txtQualifier, decimalSeptr, thousandSeptr, columnHeadsJson, includeFirstRow, importFromRow,datepattern);
			int conversionSuccess = importer.importRemoteDocument(fr, remoteBook,true,null);
			logger.info("[CONVERSION:IMPORT] REMOTE.IMPORTER CONVERSION.SUCCESS:" + conversionSuccess);
			
			int conversionErrorCode = -1;
			String message = null;
			switch(conversionSuccess){
				case ZFSNGConstants.CONVERSION_FAILURE:
				case ZFSNGConstants.CONVERSION_FAILURE_DOCUMENT_CORRUPTED:

					if(isNullOrEmpty(password)) {
						boolean isEncrypted = ImportExportUtil.isEncrypted(fr.fileContent);
						if(isEncrypted && isFormatSupported) {
							if(_uri != null &&_uri.contains("importview") || "handheld".equals(modeParam) || "handheld".equals(devicetype)){
								throwError(APIError.CONVERSION_FAILURE_PASS_PROTECTED, null, response);
							}else{
								try {
									//boolean isThisMacroEnabled = EngineConstants.ISNEWMACROENABLED;
									//if(isThisMacroEnabled) {
									logger.log(Level.INFO, "[CONVERSION:NEW IMPORT] Encrypted Sheet Ask For PASSWORD:");
									//Read all param as JOSNObject and save as sheetsetting in DB
									//Save Encrypted Content in DFS
									//import empty content
									byte[] contentBytes = fr.fileContent; 
									String encryptedSheetFileFormat = fr.fileFormat.replace(".","");
									JSONObjectWrapper tempParams = getEncryptedSheetParam(request, fr.fileFormat);
									remoteBook.allowEmptyFileContent = true;
									fr.setEmptyFileContent();
									fr.fileFormat=Constants.ODS_FILE_FORMAT;
									fr.sheetsettings.addProperty("isEncrypted",isEncrypted);	//NO I18N
									fr.sheetsettings.addProperty("RequestParams",tempParams.toString());	//NO I18N
									fr.sheetsettings.addProperty("isencrypted",true);	//NO I18N

									importer = ImporterFactory.getImporterInstance(fr.fileFormat, delimiter, dateFormat, txtQualifier, decimalSeptr, thousandSeptr, columnHeadsJson, includeFirstRow, importFromRow,datepattern);
									conversionSuccess = importer.importRemoteDocument(fr, remoteBook, true, null);
									//remoteBook = RemoteUtils.updateRemoteDB(remoteBook, fr.sheetsettings);
									Long ownerZuid = (remoteBook.isAuthCase()) ? Long.parseLong(importer.docsSpaceId) : -1;
									String docOwner = remoteBook.isAuthCase ? importer.docOwner: remoteBook.getUserName();

									saveEncryptedSheet(docOwner,  Long.parseLong(importer.docId), encryptedSheetFileFormat, contentBytes,ownerZuid);
									logger.log(Level.INFO, "[CONVERSION:NEW IMPORT] ASKING FOR PASSWORD:", importer.docId);
									generateResponse(remoteBook, request, response);
								}catch(Exception e) {
									logger.log(Level.INFO, "[CONVERSION:NEW IMPORT] Encrypted Sheet Exception:",e);
									throwError(2508, null);
								}
								//}
							}
							return null;
						}
					}
					conversionErrorCode =2502;
					break;
				case ZFSNGConstants.CONVERSION_FAILURE_NO_OF_COLUMNS_EXCEEDED:
					conversionErrorCode =2505;
					break;
				case ZFSNGConstants.CONVERSION_FAILURE_NO_OF_ROWS_EXCEEDED:
					conversionErrorCode =2506;
					break;
				case ZFSNGConstants.CONVERSION_FAILURE_NO_OF_CELLS_EXCEEDED:
					conversionErrorCode =2507;
					break;
				case ZFSNGConstants.CONVERSION_FAILURE_PASS_PROTECTED:
					conversionErrorCode =2508;
					break;
				case ZFSNGConstants.CONVERSION_FAILURE_DOCUMENT_SIZE_TOO_LARGE:
					conversionErrorCode =2509;
					break;
				default:
			}
			
			if(conversionSuccess !=  ZFSNGConstants.CONVERSION_SUCCESS){
				String[]  secErrMsg = null;
				throw new ZohoSheetException("APIERROR", conversionErrorCode,secErrMsg);//No I18N
				
			}
//			if(conversionSuccess == ZFSNGConstants.CONVERSION_FAILURE) { 
//				throw Exception(2502);
//			}
//			else if(conversionSuccess == ZFSNGConstants.CONVERSION_FAILURE_NO_OF_COLUMNS_EXCEEDED) { 
//				throwError(2505);
//			}else if(conversionSuccess == ZFSNGConstants.CONVERSION_FAILURE_NO_OF_ROWS_EXCEEDED){
//				throwError(2506);
//			}else if(conversionSuccess == ZFSNGConstants.CONVERSION_FAILURE_NO_OF_CELLS_EXCEEDED){
//				throwError(2507);
//			}else if(conversionSuccess == ZFSNGConstants.CONVERSION_FAILURE_PASS_PROTECTED){
//				throwError(2508);
//			}else if(conversionSuccess == ZFSNGConstants.CONVERSION_FAILURE_DOCUMENT_SIZE_TOO_LARGE){
//				throwError(2509);
//			}
			doDepdProcess(remoteBook);
			generateResponse(remoteBook, request, response);
		
		}catch (ZohoSheetException e){
				logger.log(Level.WARNING, "[CONVERSION:IMPORT] REMOTE.IMPORTER", e);
				throwError(e.getErrorCode());
			
		
		}catch (OfficeSuiteException oe){
			logger.log(Level.WARNING, "[CONVERSION:IMPORT] REMOTE.IMPORTER ERROR", oe.getErrorCode());
			throwError(oe.getErrorCode(),oe.getMessage(),oe.getSecondaryErrorMessage());
		
		}catch (Exception ex){
			logger.log(Level.WARNING, "[CONVERSION:IMPORT] REMOTE.IMPORTER", ex);
			throwError(2502);
		}
		logger.log(Level.INFO, "[CONVERSION:IMPORT] REMOTE.IMPORTER");
		return null;
	
	}
	
	public RemoteBook getRemoteBook(HttpServletRequest request) {
		return RemoteBook.getInstance();
	}

	Object validateAPIKey(RemoteBook remoteBook, HttpServletRequest request) {
		return (!remoteBook.skipAPIKeyValidation)? getApiKeyId(request): true;
	}

	public Long getApiKeyId(HttpServletRequest request){
		return new Long(Constants.ZS_REMOTE_APIKEYID);
	}
	
	public HashMap checkInputParams(RemoteBook remoteBook, HttpServletRequest request) throws Exception {
		if(!remoteBook.skipInputParamValidation){
			//validating the input parameters value [It will be available only in remote API case.]
			HashMap resMap = validateInputParams(remoteBook, request);
			if(resMap != null && !resMap.isEmpty() && ("YES").equals(resMap.get("IS_ERROR"))) {  //NO I18N
				throwError((Integer)resMap.get("eC"), (resMap.get("secErrMsg") != null) ? (String[]) resMap.get("secErrMsg") : null); //No I18N
			}
			return resMap;
		}
		return null;
	}
//	public  String getLocale() {
//		return this.locale;
//	}

	public HashMap validateInputParams(RemoteBook remoteBook, HttpServletRequest request) {
		return null;
	}

	public HashMap getDBUpdater(HashMap validatorResMap){
		return (HashMap) new TableMetaHandler(validatorResMap).getValue();
	}

	public void doDepdProcess(RemoteBook remoteBook) {
		//Used for Call back
	}
	
	public void generateResponse(RemoteBook remoteBook, HttpServletRequest request, HttpServletResponse response) {
		int resType = 0;//getResponseType();
		//logger.info("ResponseType: " + resType);
		//response.setContentType("text/html");//No I18N
		
		/*switch(resType){
			// Request forwarding
			case 0:
				request.getRequestDispatcher(request.getParameter("proxyURL") + ".do").forward(request, response);   //NO I18N
			case 1:
				String _url = getRedirectURL(docId, viewParam, request);
				writeResponse(response, _url);
				//response.getWriter().println(_url);
				break;
			default:
				String url = getRedirectURL(docId, viewParam, null);
				logger.info("Redirect URL ===>>> " + url);
				response.sendRedirect(url);
		}*/
		
	}
	
	public void writeResponse(HttpServletResponse response, String responseString) {
		// Used to check the request attributes and it's values.
		/*Enumeration en = request.getAttributeNames();
		while(en.hasMoreElements()) {
			String elem = en.nextElement().toString();
			logger.info(elem + " :: " + request.getAttribute(elem));
		}*/
		
	
		try{
			response.getWriter().println(responseString);  // NO OUTPUTENCODING
		}catch (Exception e){
			logger.info("Problem while writting response. "+ e);
		}
	}

	public String getResponse(HttpServletRequest request){
		return "continue";//No I18N
	}

	public void throwError(int errorCode)  throws Exception {
		throwError(errorCode, null);
	}
	public void throwError(int errorCode, String[] secErrMsg) throws Exception{	}
	public void throwError(int errorCode,String msg,String[] secErrMsg) throws Exception{	}
	
	public void throwError(int errorCode, String[] secErrMsg, HttpServletResponse response) throws Exception{	
		
	}
	
	
	public static boolean isNullOrEmpty(String str) {
        if(str != null && !str.isEmpty() && !str.equalsIgnoreCase("null")) {
            return false;
        }
        return true;
    }
	
	public static boolean isCsvOrTsv(String fileFormat) {
        if(fileFormat != null && !fileFormat.isEmpty() && !fileFormat.equalsIgnoreCase("null")) {
        	if(fileFormat.equals(EngineConstants.CSV_FILE_FORMAT) || fileFormat.equals(EngineConstants.TSV_FILE_FORMAT)) {
        		return true;
        	}
        }
        return false;
    }
	
	public static JSONObjectWrapper getEncryptedSheetParam(HttpServletRequest request, String fileFormat) throws Exception{
		Enumeration<String> parameterNames = request.getParameterNames();
		JSONObjectWrapper requestParams = new JSONObjectWrapper();
		String requestURL = request.getRequestURL().toString();
		requestParams.put("RequestURL", requestURL);
        requestParams.put("encryptedsheetformat", fileFormat); 
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            String paramValue = request.getParameter(paramName);
            requestParams.put(paramName, paramValue);
        }
        return requestParams;
	}
	
	public long saveTempSheet(RemoteBook remoteBook, FileReader fr) throws Exception {
		String docOwner = Constants.REMOTE_USER;
		remoteBook.setUserName(docOwner);
		remoteBook = RemoteUtils.updateRemoteDB(remoteBook, fr.sheetsettings);
		long documentId = Long.parseLong(remoteBook.getDocumentId());
		fr.fileFormat = fr.fileFormat.replace(".", "").toLowerCase();
		Long ownerZuid = (remoteBook.isAuthCase()) ? DocumentUtils.getZUID(remoteBook.getUserName()) : -1;

		saveEncryptedSheet(docOwner, documentId, fr.fileFormat, fr.fileContent,ownerZuid);
		logger.log(Level.INFO, "[CONVERSION:IMPORT] REMOTE.IMPORTER Temp Sheet saved to ask for the password as {0} {1} {2} {3}", new Object[]{docOwner, documentId, fr.fileFormat, fr.fileContent.length});
		return documentId;
	}
	
	private static void saveEncryptedSheet(String docOwner, long documentId, String format, byte[] b,Long ownerZUID) throws Exception{
		Store store = null;
		if (ownerZUID == -1){
			store = StoreFactory.getInstance().getDocumentStore(docOwner, Long.valueOf(documentId));
		} else {
			store = StoreFactory.getInstance().getDocumentStore(ownerZUID, docOwner, Long.valueOf(documentId));
		}

		//Store store = StoreFactory.getInstance().getDocumentStore(docOwner, Long.valueOf(documentId));
		SheetFileInfo sheetfileInfo = store.getFileInfo(Long.valueOf(documentId), EngineConstants.FILENAME_REMOTEDOC, format);
	    if (sheetfileInfo == null) {
	        sheetfileInfo = store.createFileInfo(Long.valueOf(documentId), EngineConstants.FILENAME_REMOTEDOC, format);
	    }
	    long resId = sheetfileInfo.getResourceId();
	    HashMap<String, Object> writeInfo = store.write(resId, sheetfileInfo);
	    OutputStream os = (OutputStream) writeInfo.get("OS");
	    os.write(b);
	    store.finishWrite(writeInfo);
	}

}
