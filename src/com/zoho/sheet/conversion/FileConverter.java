//$Id$
package com.zoho.sheet.conversion;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.EOFException;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;

import com.adventnet.zoho.websheet.model.*;
import com.zoho.sheet.chartengine.ChartEngine;
import com.zoho.sheet.chartengine.ChartEngineManager;
import com.zoho.sheet.chartengine.CommonChartEngineUtils;
import org.apache.commons.io.IOUtils;
import com.zoho.sheet.api.dataapi.handler.DownloadAPIHandler;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.iam.security.UploadedFileItem;
import com.adventnet.zoho.websheet.model.WorkbookContainer.ContainerEntity;
import com.adventnet.zoho.websheet.model.parser.MacroParser;
import com.adventnet.zoho.websheet.model.parser.SettingsParser;
import com.adventnet.zoho.websheet.model.util.ActionUtil;
import com.adventnet.zoho.websheet.model.util.CSVReader;
import com.adventnet.zoho.websheet.model.util.DateUtil.DateFormatType;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.EngineUtils1;
import com.adventnet.zoho.websheet.model.util.StreamUtil;
import com.adventnet.zoho.websheet.model.util.ZSStore;
import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXParserAgent;
import com.adventnet.zoho.websheet.model.zsparser.OrderFileReader;
import com.adventnet.zoho.websheet.model.zsparser.ZSWorkbookParser;
import com.adventnet.zoho.websheet.model.zsparser.ZSWorkbookTransformer;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sun.star.beans.PropertyValue;
import com.sun.star.frame.XComponentLoader;
import com.sun.star.io.IOException;
import com.sun.star.lang.XComponent;
import com.sun.star.sheet.XSpreadsheetDocument;
import com.sun.star.sheet.XSpreadsheets;
import com.sun.star.uno.UnoRuntime;
import com.zoho.conf.Configuration;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.chart.ChartImportExport;
import com.zoho.sheet.chart.ChartUtils;
import com.zoho.sheet.chart.TablePrediction;
import com.zoho.sheet.connection.ConnectionUtils;
import com.zoho.sheet.knitcharts.utils.ChartEngineUtils;

import com.zoho.sheet.util.*;

import org.apache.commons.io.IOUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;


//import com.zoho.sheet.conversion.ConnectionPool.ConnectionObject;

import com.zoho.sheet.util.AsposeUtils;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.GraphUtils;
import com.zoho.sheet.util.OOInputStream;
import com.zoho.sheet.util.OOOutputStream;
import com.zoho.sheet.util.RemoteUtils;
import com.zoho.sheet.util.ZSLogger;
import org.json.JSONObject;



public class FileConverter {
	public static final Logger LOGGER = Logger.getLogger(FileConverter.class.getName());
	//public static  final boolean ISASPOSECONVERSIONENABLED =  EngineConstants.ISASPOSECONVERSIONENABLED;
	RemoteBook remoteBook;
	//static WorkbookContainer container =null;
	String docId = null;
	String docName =null;
	String format =null; //Converted Format
	OOOutputStream ooos = null;
	XComponent xComponent = null;
	byte[] fileContent;
	String fileName = null;
	String dirName = null;
	String fileFormat;	//Read File Format
	String delimiter = null;
	String dateFormat = null;
	String sheetName;
	File tempFile = null;
	String outSheetName =null;
	int outSheetIndex = -1;
	String isMultiple ="false";
	String charset = null;
	JSONObjectWrapper pdf_settings = new JSONObjectWrapper();

	public FileConverter() {
		// TODO Auto-generated constructor stub
	}

//	public byte[] getworbookByte( HttpServletRequest request) throws Exception {
//		String outputOp = request.getParameter("output_options");
//		LOGGER.info("[CONVERSION:FILECONVERSTER]"+outputOp);
//		if(outputOp != null){
//			JsonObject outputObj =  new JsonParser().parse(outputOp).getAsJsonObject();
//			if(outputObj.has("format")){
//				this.format = outputObj.get("format").getAsString();
//			}
//			if(outputObj.has("document_name")){
//				this.fileName = outputObj.get("document_name").getAsString();
//			}
//		}else{
//			this.format = request.getParameter("format");
//
//			this.dirName = System.currentTimeMillis()+"";
//			this.fileName =  (String) request.getParameter("workbookname");
//			//this.fileName = this.fileName != null ? this.dirName +"_"+ this.fileName : null;
//			this.isMultiple = (String) request.getParameter("ismultiple");
//			this.outSheetName = (String) request.getParameter("sheetname");
//		}
//		String sheetIndex =request.getParameter("sheetindex");
//		if(sheetIndex != null && sheetIndex != ""){
//			this.outSheetIndex = Integer.parseInt(sheetIndex);
//		}
//		FileInputStream fis =null;
//		FileInputStream _fis = null;
//		int index = 0;
//		LOGGER.info("[CONVERSION:FILECONVERSTER]"+this.format);
//		WorkbookContainer container = null; //getNewContainerAndWorkBook();
//		ArrayList<UploadedFileItem> filesList = (ArrayList<UploadedFileItem>)request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST);
//
//		if (request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST) != null ) {
//			for(UploadedFileItem uploadedFileItem : filesList) {
//				try {
//					LOGGER.info("[CONVERSION:FILECONVERSTER]"+uploadedFileItem.getFileName());
//
//					File f = uploadedFileItem.getUploadedFile();
//					fis = new FileInputStream(f);
//					fileContent = ClientUtils.readStream(fis);
//					String filename = uploadedFileItem.getFileName();
//					LOGGER.info("My file Name print here::::"+filename);
//					this.sheetName  = filename.substring(0,filename.lastIndexOf("."));
//					this.fileFormat = (filename.substring(filename.lastIndexOf('.') + 1)).toLowerCase();
//					//String[] fileDetails = uploadedFileItem.getFileName().lastIndexOf(".");
//					//this.sheetName = fileDetails[0];
//					//this.fileFormat = fileDetails[1];
//					if("csv".equalsIgnoreCase(fileFormat) || "tsv".equalsIgnoreCase(fileFormat)){//TODO check csv tsv
//						_fis = new FileInputStream(f);
//						String charset = ConnectionUtils.getCharEncoding(_fis);
//						String ss = new String(fileContent, charset);
//						fileContent = ss.getBytes();
//						container =  getNewContainerAndWorkBook();
//						this.copyDelimeterSeperatedDataToWorkbook(container,index++,fileContent,sheetName);
//					}
//					else if("zsheet".equalsIgnoreCase(fileFormat)){
//
//						container = getNewContainerAndWorkBook();
//						Workbook wb =this.parse(container, fileContent);
//						container.setWorkbook(wb,false,false);
//						tempFile = EngineUtils1.createTempODSFile(container, null, false, null, (this.format != null && this.format.endsWith("ods")));//No I18N
//						byte[] responseBytes = getDocumentBytes(tempFile,container);
//						LOGGER.info("[CONVERSION:FILECONVERSTER] Completed:"+responseBytes.length);
//						return responseBytes;
//
//					}
//					else if("json".equalsIgnoreCase(fileFormat) ){
//						BufferedReader bufferedReader=new BufferedReader(new InputStreamReader(new ByteArrayInputStream(fileContent)));
//						try{
//							container =  getNewContainerAndWorkBook();
//							JsonTableParser jsonTableParser=new JsonTableParser(bufferedReader);
//							jsonTableParser.copyJsonTableToSheet(container,index++,null);//passing container because freeze panes requires container
//
//						}
//						catch(Exception exception)
//						{
//							LOGGER.log(Level.WARNING, "exception in copyping json to sheet", exception);
//						}
//					}else if("xlsx".equalsIgnoreCase(fileFormat)){ //No I18N
//						container =  getNewContainerAndWorkBook();
//						Workbook workbook = new Workbook();
////						Workbook workbook = container.getWorkbook(null);
//						XLSXParserAgent xlsxpa = new XLSXParserAgent(workbook);
//						xlsxpa.parseXlsxDocument(this.fileContent);
////						xlsxpa.makeDbEntries(container);
//						//Workbook wrkbook = container.getWorkbook(null);
//						StringBuffer outputData = null;
//						byte[] outputByte = null;
//						if(("csv".equalsIgnoreCase(format)) || "tsv".equalsIgnoreCase(format)){
//							if("true".equalsIgnoreCase(isMultiple)){
//								Sheet[] sheetList =  workbook.getSheets();
//								for(Sheet _sheet :sheetList){
//									if ("csv".equalsIgnoreCase(this.format)) {
//										outputByte=EngineUtils1.getCSVData(_sheet,0, 0,_sheet.getUsedRowIndex(),_sheet.getUsedColumnIndex(),false);
//									} else if ("tsv".equalsIgnoreCase(this.format)) {
//										outputByte=EngineUtils1.getTSVData(_sheet, 0, 0, _sheet.getUsedRowIndex(), _sheet.getUsedColumnIndex(),false);
//									}
//									this.fileWriter(this.dirName+"_"+this.sheetName,_sheet.getName(), outputData.toString().getBytes());
//
//								}
//								return this.zipWriteandGet(this.dirName+"_"+this.sheetName);
//							}
//							else{
//								Sheet sheetToExport;
//								if(this.outSheetName != null && !this.outSheetName.isEmpty()){
//									sheetToExport = workbook.getSheet(this.outSheetName);
//								}else if(this.outSheetIndex != -1){
//
//									sheetToExport = workbook.getSheet(this.outSheetIndex);
//								}else{
//									sheetToExport =workbook.getSheet(0);
//								}
//								if ("csv".equalsIgnoreCase(this.format)) {
//									outputByte=EngineUtils1.getCSVData(sheetToExport,0, 0,sheetToExport.getUsedRowIndex(),sheetToExport.getUsedColumnIndex(),false);
//								} else if ("tsv".equalsIgnoreCase(this.format)) {
//									outputByte=EngineUtils1.getTSVData(sheetToExport, 0, 0, sheetToExport.getUsedRowIndex(), sheetToExport.getUsedColumnIndex(),false);
//								}
//								return outputData.toString().getBytes();
//							}
//						}else if("json".equalsIgnoreCase(this.format)){//No I18N
//
//							return getJSONDocumentBytes(workbook,filename);
//						}
//
//
//					}
//					if("json".equalsIgnoreCase(this.format)){//No I18N
//
//						return getJSONDocumentBytes(container.getWorkbook(null),this.sheetName);
//					}
//					else {
//						//Convert rest of the formats using LibreOffice - xls, xlsb
////						if(container != null){
////							tempFile = EngineUtils1.createTempODSFile(container.getWorkbook(null), container.getImageBook(null), false, null);
////						}
//						byte[] responseBytes = getDocumentBytes(tempFile,container);
//						LOGGER.info("[CONVERSION:FILECONVERSTER] Completed:"+responseBytes.length);
//						return responseBytes;
//					}
//					//if()
//				}catch(Exception e){
//					LOGGER.log(Level.WARNING,"PROBLEM IN CONVERSION",e);
//				//	e.printStackTrace();
//				}finally {
//					try {
//						fis.close();
//						if(_fis != null){
//							_fis.close();
//						}
//					} catch(Exception e) {
//						LOGGER.log(Level.WARNING, "[FILECONVERSION] Problem in closing the stream", e);//No I18N
//					}
//				}
//			}
//			//Convert rest of the formats using LibreOffice - xls, xlsb
//			if("csv".equalsIgnoreCase(fileFormat) || "tsv".equalsIgnoreCase(fileFormat) ||"json".equalsIgnoreCase(fileFormat)){
//				tempFile = EngineUtils1.createTempODSFile(container, null, false, null, (this.format != null && this.format.endsWith("ods")));//No I18N
//			}
//			byte[] responseBytes = getDocumentBytes(tempFile,container);
//
//			try {
//				String password = request.getParameter("password");
//				LOGGER.info("[CONVERSION:FILECONVERSTER] Encrypted Export Enabled {0}"+new Object[]{format});
//				if (password != null) {
//					responseBytes = ImportExportUtil.encryptDocument(format, responseBytes, password);
//				}
//			}catch (Exception e) {
//				LOGGER.info("[CONVERSION:FILECONVERSTER] Encrypted Export Error {0}"+new Object[]{e});
//			}
//
//			LOGGER.info("[CONVERSION:FILECONVERSTER] Completed"+responseBytes.length);
//			return responseBytes;
//		}else{
//
//				try {
//					String url = RemoteUtils.maskNull((String) request.getParameter("url"));
//					if(url != null) {
//						HashMap hm = new HashMap();
////                hm = new WOHTTPClient().getMap(url);
//						String authHeaderType = request.getParameter("authHeaderType");
//						if (request.getParameter("authHeaderType") != null && "oauth".equals(authHeaderType)) {
//							hm = ConnectionUtils.getFileMapRequestWithOAuth(url, request);
//						} else {
//							hm = ConnectionUtils.getFileMap(url);
//						}
//						fileContent = (byte[]) hm.get("BYTE");
//
//						/* -- Setting FILE NAME and FILE FORMAT -- */
//						String filename = (String) hm.get("FILE_NAME");
//						this.fileFormat = (String) hm.get("FILE_EXTENSION");
//						LOGGER.info("My file Name print here::::" + filename);
//						this.sheetName = filename.substring(0, filename.lastIndexOf("."));
//						//String[] fileDetails = uploadedFileItem.getFileName().lastIndexOf(".");
//						//this.sheetName = fileDetails[0];
//						//this.fileFormat = fileDetails[1];
//						if ("csv".equalsIgnoreCase(fileFormat) || "tsv".equalsIgnoreCase(fileFormat)) {//TODO check csv tsv
//							//_fis = new FileInputStream(f);
////							String charset = ConnectionUtils.getCharEncoding(_fis);
////							String ss = new String(fileContent, charset);
////							fileContent = ss.getBytes();
//							container = getNewContainerAndWorkBook();
//							this.copyDelimeterSeperatedDataToWorkbook(container, index++, fileContent, sheetName);
//						} else if ("zsheet".equalsIgnoreCase(fileFormat)) {
//
//							container = getNewContainerAndWorkBook();
//							Workbook wb = this.parse(container, fileContent);
//							container.setWorkbook(wb, false, false);
//							tempFile = EngineUtils1.createTempODSFile(container, null, false, null, (this.format != null && this.format.endsWith("ods")));//No I18N
//							byte[] responseBytes = getDocumentBytes(tempFile, container);
//							LOGGER.info("[CONVERSION:FILECONVERSTER] Completed:" + responseBytes.length);
//							return responseBytes;
//
//						} else if ("json".equalsIgnoreCase(fileFormat)) {
//							BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(fileContent)));
//							try {
//								container = getNewContainerAndWorkBook();
//								JsonTableParser jsonTableParser = new JsonTableParser(bufferedReader);
//								jsonTableParser.copyJsonTableToSheet(container, index++, null);//passing container because freeze panes requires container
//
//							} catch (Exception exception) {
//								LOGGER.log(Level.WARNING, "exception in copyping json to sheet", exception);
//							}
//						} else if ("xlsx".equalsIgnoreCase(fileFormat)) { //No I18N
//							container = getNewContainerAndWorkBook();
//							Workbook workbook = new Workbook();
//							XLSXParserAgent xlsxpa = new XLSXParserAgent(workbook);
//							xlsxpa.parseXlsxDocument(this.fileContent);
//							//Workbook wrkbook = container.getWorkbook(null);
//							StringBuffer outputData = null;
//							byte[] outputByte = null;
//							if (("csv".equalsIgnoreCase(format)) || "tsv".equalsIgnoreCase(format)) {
//								if ("true".equalsIgnoreCase(isMultiple)) {
//									Sheet[] sheetList = workbook.getSheets();
//									for (Sheet _sheet : sheetList) {
//										if ("csv".equalsIgnoreCase(this.format)) {
//											outputByte = EngineUtils1.getCSVData(_sheet, 0, 0, _sheet.getUsedRowIndex(), _sheet.getUsedColumnIndex(), false);
//										} else if ("tsv".equalsIgnoreCase(this.format)) {
//											outputByte = EngineUtils1.getTSVData(_sheet, 0, 0, _sheet.getUsedRowIndex(), _sheet.getUsedColumnIndex(), false);
//										}
//										this.fileWriter(this.dirName + "_" + this.sheetName, _sheet.getName(), outputData.toString().getBytes());
//
//									}
//									return this.zipWriteandGet(this.dirName + "_" + this.sheetName);
//								} else {
//									Sheet sheetToExport;
//									if (this.outSheetName != null && !this.outSheetName.isEmpty()) {
//										sheetToExport = workbook.getSheet(this.outSheetName);
//									} else if (this.outSheetIndex != -1) {
//
//										sheetToExport = workbook.getSheet(this.outSheetIndex);
//									} else {
//										sheetToExport = workbook.getSheet(0);
//									}
//									if ("csv".equalsIgnoreCase(this.format)) {
//										outputByte = EngineUtils1.getCSVData(sheetToExport, 0, 0, sheetToExport.getUsedRowIndex(), sheetToExport.getUsedColumnIndex(), false);
//									} else if ("tsv".equalsIgnoreCase(this.format)) {
//										outputByte = EngineUtils1.getTSVData(sheetToExport, 0, 0, sheetToExport.getUsedRowIndex(), sheetToExport.getUsedColumnIndex(), false);
//									}
//									return outputData.toString().getBytes();
//								}
//							} else if ("json".equalsIgnoreCase(this.format)) {//No I18N
//
//								return getJSONDocumentBytes(workbook, filename);
//							}
//
//
//						}
//						if ("json".equalsIgnoreCase(this.format)) {//No I18N
//
//							return getJSONDocumentBytes(container.getWorkbook(null), this.sheetName);
//						} else {
//							//Convert rest of the formats using LibreOffice - xls, xlsb
////						if(container != null){
////							tempFile = EngineUtils1.createTempODSFile(container.getWorkbook(null), container.getImageBook(null), false, null);
////						}
//							byte[] responseBytes = getDocumentBytes(tempFile, container);
//							LOGGER.info("[CONVERSION:FILECONVERSTER] Completed:" + responseBytes.length);
//							return responseBytes;
//						}
//						//if()
//					}
//				}catch(Exception e){
//					LOGGER.log(Level.WARNING,"PROBLEM IN CONVERSION",e);
////					e.printStackTrace();
//				}finally {
//					try {
//						fis.close();
//						if(_fis != null){
//							_fis.close();
//						}
//					} catch(Exception e) {
//						LOGGER.log(Level.WARNING, "[FILECONVERSION] Problem in closing the stream", e);//No I18N
//					}
//				}
//			if("csv".equalsIgnoreCase(fileFormat) || "tsv".equalsIgnoreCase(fileFormat) ||"json".equalsIgnoreCase(fileFormat)){
//				tempFile = EngineUtils1.createTempODSFile(container, null, false, null, (this.format != null && this.format.endsWith("ods")));//No I18N
//			}
//			byte[] responseBytes = getDocumentBytes(tempFile,container);
//
//			try {
//				String password = request.getParameter("password");
//				LOGGER.info("[CONVERSION:FILECONVERSTER] Encrypted Export Enabled {0}"+new Object[]{format});
//				if (password != null) {
//					responseBytes = ImportExportUtil.encryptDocument(format, responseBytes, password);
//				}
//			}catch (Exception e) {
//				LOGGER.info("[CONVERSION:FILECONVERSTER] Encrypted Export Error {0}"+new Object[]{e});
//			}
//
//			LOGGER.info("[CONVERSION:FILECONVERSTER] Completed"+responseBytes.length);
//			return responseBytes;
//
//		}
//
////		return null;
//
//	}
	public byte[] getJSONDocumentBytes(Workbook workbook,String filename){
		Sheet[] sheetList =  workbook.getSheets();
		StringBuffer outputData = null;
		if("true".equalsIgnoreCase(isMultiple)){
			for(Sheet _sheet :sheetList){

				this.fileWriter(this.dirName+"_"+filename,_sheet.getName(), sheetToJSONByte(_sheet));

			}
			return this.zipWriteandGet(this.dirName+"_"+filename);

		}else{
			return sheetToJSONByte(workbook.getSheet(0));
		}



	}
	public byte[] sheetToJSONByte(Sheet sheet)
	{
		TablePrediction predict = new TablePrediction(sheet,0,0,0,0);
		Range range = predict.getTableRange();
		JSONObjectWrapper response =new JSONObjectWrapper();
		JSONArrayWrapper values= new JSONArrayWrapper();
		if(range.getRowSize()*range.getColSize() > 1) {

			//filteredRange = predict.doFilterRange(range,"COLS");//No I18n
			int rangeStartRowIndex = range.getStartRowIndex();
			int rangeStartColIndex = range.getStartColIndex();
			int rangeEndRowIndex = range.getEndRowIndex();
			int rangeEndColIndex = range.getEndColIndex();
			for(int i=rangeStartRowIndex;i<rangeEndRowIndex;i++){
				JSONArrayWrapper _resp = new JSONArrayWrapper();
				for(int j=rangeStartColIndex;j<rangeEndColIndex;j++){
					Cell _cell = sheet.getCell(i,j);
					_resp.put(_cell.getValue().getValue());
				}
				if(i==0){
					response.put("headers", _resp);
				}else{
					values.put(_resp);
				}
			}
			response.put("values", values);
			return response.toString().getBytes();
		}
		return null;
	}
//	public byte[] getDocumentBytes(File tempODSfile,WorkbookContainer container) throws Exception {
//		try {
////			InputStream stream = null;
//			if(this.format == null){
//				this.format = EngineConstants.FILEEXTN_XLSX;
//			}else if (this.format.indexOf(".") == 0) {
//				this.format = this.format.substring(1); // removing dot
//			}
////			boolean isEnabled = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("enableHeadlessPDF"));  // No I18N
////			isEnabled = true;
////			byte[] content = null;
////			Exporter exporter = new Exporter(container, this.format,null, null, null);
////			exporter.setTempFile(tempODSfile);
//			if(this.format == EngineConstants.FILEEXTN_XLSX && EngineConstants.XLSX_WRITER_REMOTE_SAVE_BACK_ENABLED) {
//				LOGGER.info("[CONVERSION:FILECONVERSTER] XLSX-WRITER");
//				Exporter exporter = new Exporter(container, this.format,null, null, null);
//				return exporter.getXLSXBytes();
//			}
//
////			else if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_PDF) && isEnabled) {
////                content = exporter.getDocumentPDFBytes();
////            } else if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_CSV) || format.equalsIgnoreCase(EngineConstants.FILEEXTN_TSV)){
////            	content = exporter.getDocumentCsvTsvBytes();
////            } else {
////            	content = exporter.getDocumentBytes();
////            }
////			String sFilter = null;
////			String filePath = null;
//			if(EngineConstants.ISLIBOMINISERVERENABLED){
//
//				return SheetConversion.getConvertedBytes(this.fileContent,this.fileFormat,this.format,true);
//			}else{
//				ConnectionPool connectionpool =new ConnectionPool();
//				ConnectionObject	connectionObject = connectionpool.getRemoteDesktop();
//				xComponent = getXComponent(connectionObject,tempODSfile);
//				String liboHost = connectionObject.getLiboHost();
//				XSpreadsheetDocument xDocument = (XSpreadsheetDocument) UnoRuntime.queryInterface(XSpreadsheetDocument.class, xComponent);
//
//				if("csv".equalsIgnoreCase(this.format) || "tsv".equalsIgnoreCase(this.format)){
//					return this.getLiboZipOutStream(xDocument);
//				}
//	//			if("zsheet".equalsIgnoreCase(this.format){
//	//				return this.writeZSheetFile(container, workBook, imageBook)
//	//			}
//
//					return this.getLiboOutStream(xDocument);
//			}
////			return content;
//		} catch (Exception e) {
//			LOGGER.log(Level.WARNING, "[FILECONVERSION]", e);
//			throw e;
//		} finally {
//			if (ooos != null) {
//				ooos.close();
//			}
//			if(tempODSfile != null){
//				tempODSfile.delete();
//			}
//			ImportExportUtil.closeComponent(xComponent);
//		}
//
//	}
	private byte[] getLiboZipOutStream(XSpreadsheetDocument xDocument ){
		try{
			XSpreadsheets sheets = xDocument.getSheets();
			String[] sheetName = sheets.getElementNames();
			int length = sheetName.length;


			if("true".equalsIgnoreCase(isMultiple)){
				for(String _sheetName :sheetName){

					byte[] filebytes = this.getLiboOutStream(xDocument);
					this.fileWriter("liboTest", _sheetName, filebytes);//No I18N
					sheets.moveByName(_sheetName,(short) length++);
				}
				return this.zipWriteandGet("liboTest");//No I18N
			}else if(this.outSheetName != null && !this.outSheetName.isEmpty()){
				for(String _sheetName :sheetName){
					if(_sheetName.equalsIgnoreCase(this.outSheetName)){
						return this.getLiboOutStream(xDocument);
					}else{
						sheets.moveByName(_sheetName,(short) length++);
					}
				}
			}else if(this.outSheetIndex != -1){
				for(int i=0;i<length;i++){
					if(i==this.outSheetIndex){
						return this.getLiboOutStream(xDocument);
					}else{
						sheets.moveByName(sheetName[i],(short) length++);

					}
				}
			}else{
				return this.getLiboOutStream(xDocument);
			}

		}catch(Exception e){
			LOGGER.log(Level.WARNING,"PROBLEM IN MULTIPLE",e);
		}
		return null;

	}

//	private  XComponent getXComponent(ConnectionObject connectionObject,File tempODSfile) throws Exception
//	{
//		try{
//			XComponent xSpreadsheetComponent  = null;
//			XComponentLoader xComponentLoader = null;
//			BufferedInputStream     bis       = null;
//			ByteArrayOutputStream     bos       = null;
//			OOInputStream            oois       = null;
//			InputStream stream  = null;
//			// the below code is stupid thing we have done. Please correct if you see it. We should pass the zfsngVersionId for woekBook and Containers.
//			//String zfsngVersionId = request.getParameter("versionid");
//			String lIBOHost = null;
//			String lIBOFileName = EnginePropertyUtil.getSheetPropertyValue("LIBOExportLocation") +  System.currentTimeMillis() + (int)(Math.random() * 1000) + EngineConstants.ENGINE_ODS_FILE_FORMAT; // No I18N
//			boolean liboDelete = true;
//			try
//			{
//				xComponentLoader = connectionObject.getxComponentLoader();
//				lIBOHost = connectionObject.getLiboHost();
//				//Throwing error if the OO Connection is failed.
//				if(xComponentLoader == null)
//				{
//					throw new ServletException("Connection Error");
//				}
//
//				PropertyValue[] loadProps = new PropertyValue[1];
//				loadProps[0]         = new PropertyValue();
//				loadProps[0].Name     = "Hidden";                                                            // No I18N
//				loadProps[0].Value  = Boolean.TRUE;
//				//is = saveTempInputStream(is, container, zfsngVersionId); // should be inside the webapps/websheet/exportdocs
//				if(tempODSfile == null){
//					stream  = new ByteArrayInputStream(fileContent);
//				}else {
//
//					stream = (InputStream) ImportExportUtil.openURL(tempODSfile);
//				}
//				String LIBOHome = ImportExportUtil.saveAtLIBOServer(lIBOHost, lIBOFileName, stream);
//
//				liboDelete = false;	//Temporary File written at LIBO, Finally need to delete in case of error.
//				String absoluteFileName = LIBOHome + File.separator + lIBOFileName;
//
//				// TODO: No need of this synchronized block .. removed synchronized
//				//			synchronized (xComponentLoader) {
//				xSpreadsheetComponent = xComponentLoader.loadComponentFromURL("file://" + absoluteFileName, "_blank", 0, loadProps);                        // No I18N
//				connectionObject.setxComponent(xSpreadsheetComponent);
//				if(xSpreadsheetComponent==null){
//					LOGGER.log(Level.INFO, "[FILECONVERSION] COULD NOT CREATE XSPREADSHEETCOMPONENT{0}");
//				}
//				//			}
//				// TODO: try - catch - no need of throw exception
//				try{
//					ImportExportUtil.deleteAtLIBOServer(lIBOHost, lIBOFileName);
//					liboDelete = true;
//				}
//				catch(Exception e){
//					LOGGER.log(Level.INFO, "[FILECONVERSION] Exception on Deleting at LIBO Server{0}", container.getResourceId());
//				}
//				return xSpreadsheetComponent;
//			}
//			catch(Exception ex)
//			{
//				LOGGER.log(Level.WARNING,"[FILECONVERSION] Problem in COnvert",ex);
//				throw new ServletException(ex.getMessage());
//			}
//			finally
//			{
//				if(stream != null)
//				{
//					stream.close();
//				}
//				if(bis != null)
//				{
//					bis.close();
//				}
//				if(bos != null)
//				{
//					bos.close();
//				}
//				if(oois != null)
//				{
//					oois.closeInput();
//				}
//				if(!liboDelete){
//					ImportExportUtil.deleteAtLIBOServer(lIBOHost, lIBOFileName);
//				}
//			}
//		}catch(Exception e){
//			LOGGER.log(Level.WARNING,"problem in xcomponent"+e);
//		}
//		return xComponent;
//	}

	private WorkbookContainer getNewContainerAndWorkBook() throws Exception
	{
		WorkbookContainer container=null;

		if(this.fileName == null || "".equalsIgnoreCase(this.fileName)){
			this.fileName = "Untitled_Spreadsheet";	//No I18N
		}

		String[] details =  DocumentUtils.addNewDocumentToDB("sheetremoteapi0",fileName,false,0,false);//No I18N
		ContainerEntity entity = WorkbookContainer.getEntity();
		entity.documentName = this.fileName;
		entity.docOwner = "sheetremoteapi0";//No I18N
		entity.remoteBookId = details[0];
		entity.documentId = details[0];
		//entity.docOwnerId = 	"sheetremoteapi0";


		container = new WorkbookContainer(entity);
		Workbook workbook=EngineUtils1.createNewWorkbook(container);
		container.setWorkbook(workbook,false,false);
		ImageBook imb = new ImageBook();
		container.setImageBook(imb);
		return container;
	}
	private void  copyDelimeterSeperatedDataToWorkbook(WorkbookContainer container,int sheetIndex,byte[] fileContent,String sheetName) throws Exception
	{	//ToDo change  method to pass field and text delimiters
		long parseStartTime=System.currentTimeMillis();

		Workbook workbook=container.getWorkbook(null);
		if(sheetIndex>0){
			workbook.addSheet(sheetName,sheetIndex);
		}
		Sheet sheet=workbook.getSheet(sheetIndex);//get the first sheet and set it's name to docName
		if(sheetIndex == 0){
			sheet.setName(sheetName);
		}
		BufferedReader bufferedReader=new BufferedReader(new InputStreamReader(new ByteArrayInputStream(fileContent)));
		LOGGER.info("[CONVERSION:FILECONVERSTER] CSV/TSV");

		char seperator = 0;
		if(this.delimiter != null)
		{
			switch(this.delimiter){
				case "|":
					seperator = '|';
					break;
				case ";":
					seperator = ';';
					break;
				case "^":
					seperator = '^';
					break;
				case "~":
					seperator = '~';
					break;
				case ",":
					seperator = ',';
					break;
				case "\t":
					seperator = '\t';
					break;
				default:
					if(this.fileFormat.equalsIgnoreCase("csv"))// No I18N
					{
						seperator=',';
					}
					else if(fileFormat.equalsIgnoreCase("tsv"))// No I18N
					{
						seperator='\t';
					}
					break;

			}
		}
		else if(fileFormat.equalsIgnoreCase("csv"))// No I18N
		{
			seperator=',';
		}
		else if(fileFormat.equalsIgnoreCase("tsv"))// No I18N
		{
			seperator='\t';
		}
		CSVReader csvReader=new CSVReader(bufferedReader, seperator, '\"', true, false);
		int rowNo=0;
		int maxColNo=0;
		//csv file can be like
		/*   , , ,hi
		 * 	 ,
		 * 	 , ,
		 * 	 , , ,endFeild
		 */
		//For used cell count we still need to take as 4*4=16 not 11

		DateFormatType dateType = getDateFormatType();

		try{
			try
			{
				while(true)//read until EOF Exception thrown
				{
					Vector<String> row = csvReader.getAllFieldsInLine();//returns a single row
					int colNo=0;
					for(String cellValue:row)
					{
						Cell cell=sheet.getCell(rowNo, colNo);

						//List<RichStringProperties> richStringProperties = EngineUtils1.setLink(cellValue, sheet.getWorkbook());
						ActionUtil.setCellValue(cell, cellValue, null,null,null,null,null, null);
						colNo++;
					}
					rowNo++;
					if(colNo>maxColNo)
					{
						maxColNo=colNo;
					}

				}

			}
			catch(EOFException eof)
			{
				// CSVreader normally throws a EOF exception and terminates
				LOGGER.log(Level.INFO, "[CONVERSION:FILECONVERSTER] CSV Reader finished reading");
			}

			LOGGER.log(Level.INFO, "[CONVERSION:FILECONVERSTER]  ");
			//make all columns resize to fit the content,update formulaes
			ActionUtil.setOptimalColumnWidth(sheet, 0,sheet.getUsedColumnIndex());

			ActionUtil.calculateOptimalRowHeight(sheet, 0, sheet.getUsedRowIndex(), true);
			workbook.updateCellDependencies();
			workbook.recalculateFormulaCells();
		}
		catch (Exception e)
		{
			//Any other Exception indicates a problem
			LOGGER.log(Level.WARNING,"[CONVERSION:FILECONVERSTER] Problem  in copying data from file ",e);
		}
		finally {//need to close reader even if method call return in catch block
			try{
				if(csvReader != null) {
					csvReader.close();
				}
			}
			catch(Exception e)
			{
				LOGGER.log(Level.WARNING, "[CONVERSION:FILECONVERSTER] Error is closing CSVReader Stream",e);
			}


		}


	}
	private DateFormatType getDateFormatType()
	{
		if(this.dateFormat != null)
		{
			switch(this.dateFormat)
			{
				case "MDY":
					return DateFormatType.MDY;
				case "DMY":
					return DateFormatType.DMY;
				case "YMD":
					return DateFormatType.YMD;
				default:
					return null;
			}
		}
		return null;
	}


	public String getMimeType(){
		String mimeType = null;

		if(this.format.equals("xlsx")) {
			mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";// No I18N
		}
		else if(this.format.equals("xls")) {
			mimeType = "application/vnd.ms-excel";// No I18N
		}else if("true".equals(this.isMultiple)){
			mimeType = "application/zip";// No I18N
		}
		else if(this.format.equals("csv")) {
			mimeType = "text/csv";// No I18N
		}
		else if(this.format.equals("tsv")) {
			mimeType = "text/tsv";// No I18N
		}
		else if(this.format.equals("ods")) {
			mimeType = "application/vnd.oasis.opendocument.spreadsheet";// No I18N
		}
		else if(this.format.equals("pdf")) {
			mimeType = "application/pdf";// No I18N
		}
		else if(this.format.equals("html")) {
			mimeType = "text/html";// No I18N
		}
		else if(this.format.equals("zip")) {
			mimeType = "application/zip";// No I18N
		}
		return mimeType;

	}

	public String getFileName(){
		if((this.fileName == null) || "".equals(this.fileName)){
			return "Untitled_Spreadsheet";//No I18N
		}else if(this.fileName.contains(".")){
			String[] _fileName = this.fileName.split("\\.");
			return _fileName[0];
		}else{
			return fileName;
		}
		//	return ((this.fileName == null) || "".equals(this.fileName))?"Untitled_Spreadsheet":this.fileName;
	}
	public String getFormat(){
		if("true".equalsIgnoreCase(isMultiple)){
			this.format = "zip";//No I18N
		}
		return this.format;
	}
	//		private byte[] getOutputStream(XSpreadsheet  xSheet,String sheetName,ConnectionObject connectionObject) throws Exception{
	//			String sFilter = null;
	//			//XSpreadsheetDocument document =(XSpreadsheetDocument) UnoRuntime.queryInterface(XSpreadsheetDocument.class, xComponent);
	//			if (xSheet != null) {
	//				PropertyValue[] lProperties = new PropertyValue[3];
	//				if ("pdf".equalsIgnoreCase(this.format)) {
	//					sFilter = new String("calc_pdf_Export"); // No I18N
	//					lProperties = new PropertyValue[4];
	//				} else {
	//					sFilter = ImportExportUtil.getFilter("." + this.format); //NO I18N
	//				}
	//				ooos = new OOOutputStream();
	//				lProperties[0] = new com.sun.star.beans.PropertyValue();
	//				lProperties[0].Name = "FilterName";                                                                                // No I18N
	//				lProperties[0].Value = sFilter;
	//				lProperties[1] = new com.sun.star.beans.PropertyValue();
	//				lProperties[1].Name = "OutputStream";                                                                                // No I18N
	//				lProperties[1].Value = ooos;
	//				lProperties[2] = new com.sun.star.beans.PropertyValue();
	//				lProperties[2].Name = "DocumentTitle";                                                                                // No I18N
	//				lProperties[2].Value = sheetName;
	//				if ("pdf".equalsIgnoreCase(this.format)) {
	//					lProperties[3] = new com.sun.star.beans.PropertyValue();
	//					lProperties[3].Name = "CompressionMode";                                                                        // No I18N
	//					lProperties[3].Value = "1";
	//				}
	//
	//				XComponentLoader xComponentLoader = connectionObject.getxComponentLoader();
	//				PropertyValue[] loadProps = new PropertyValue[0];
	//		        XComponent xSpreadsheetComponent = xComponentLoader.loadComponentFromURL("private:factory/scalc", "_blank", 0, loadProps);
	//		        XSpreadsheetDocument xSpreadsheetDocument = (XSpreadsheetDocument)UnoRuntime.queryInterface(XSpreadsheetDocument.class, xSpreadsheetComponent);
	//
	//		        XSpreadsheets xSpreadsheets = xSpreadsheetDocument.getSheets();
	//	            xSpreadsheets.insertNewByName(sheetName, (short)0);
	//	            Object sheet = xSpreadsheets.getByName(sheetName);
	//	            XSpreadsheet xSpreadsheet = UnoRuntime.queryInterface(
	//	                XSpreadsheet.class, sheet);
	//	            xSpreadsheet = xSheet;
	//
	//	            com.sun.star.frame.XStorable xStore = (com.sun.star.frame.XStorable) UnoRuntime.queryInterface(
	//	            		com.sun.star.frame.XStorable.class,xSpreadsheetDocument );
	//				try {
	//					xStore.storeToURL("private:stream", lProperties);                                                                // No I18N
	//				} catch (IOException ie) {
	//					LOGGER.log(Level.WARNING, "[FILECONVERSION]", ie);
	//					throw new ServletException("Unable to convert the Document");
	//				}
	//				return ooos.toByteArray();
	//			}
	//			return null;
	//		}
	//		//private byte[] getAsZip(){

	private byte[] getLiboOutStream(XSpreadsheetDocument xDocument ) throws ServletException{

		PropertyValue[] lProperties = new PropertyValue[3];
		String sFilter;
		if(this.fileFormat.equals("zsheet")) {

			try{
				JSONObjectWrapper chartsDBJson = parseChartsFromZsheet(fileContent);
				ChartImportExport cIE  = new ChartImportExport(xDocument, null, null,0.0);
				cIE.exportCharts(chartsDBJson);
			}catch(Exception e){
				LOGGER.log(Level.WARNING, "[CONVERSION:EXPORT] Couldn't export Chart: {0}", e);
			}

		}
		if ("pdf".equalsIgnoreCase(this.format)) {
			sFilter = new String("calc_pdf_Export"); // No I18N
			lProperties = new PropertyValue[4];
		} else {
			sFilter = ImportExportUtil.getFilter("." + this.format); //NO I18N
		}
		ooos = new OOOutputStream();
		lProperties[0] = new com.sun.star.beans.PropertyValue();
		lProperties[0].Name = "FilterName";                                                                                // No I18N
		lProperties[0].Value = sFilter;
		lProperties[1] = new com.sun.star.beans.PropertyValue();
		lProperties[1].Name = "OutputStream";                                                                                // No I18N
		lProperties[1].Value = ooos;
		lProperties[2] = new com.sun.star.beans.PropertyValue();
		lProperties[2].Name = "DocumentTitle";                                                                                // No I18N
		lProperties[2].Value = docName;
		if ("pdf".equalsIgnoreCase(this.format)) {
			lProperties[3] = new com.sun.star.beans.PropertyValue();
			lProperties[3].Name = "CompressionMode";                                                                        // No I18N
			lProperties[3].Value = "1";
		}
		com.sun.star.frame.XStorable xStore = (com.sun.star.frame.XStorable) UnoRuntime.queryInterface(
				com.sun.star.frame.XStorable.class, xDocument);
		try {
			xStore.storeToURL("private:stream", lProperties);                                                                // No I18N
		} catch (IOException ie) {
			LOGGER.log(Level.WARNING, "[CONVERSION:FILECONVERSTER]", ie);
			throw new ServletException("Unable to convert the Document");
		}

		return ooos.toByteArray();
	}
	private void fileWriter(String dirName, String sheetName,byte[] outputData){
		FileOutputStream out =null;
		try {
			String parentDir = Configuration.getString("app.home") + File.separator + "bin"+ File.separator+ "converterdocs" + File.separator;//No I18N
			File f = new File(parentDir);
			if (!f.exists()) {
				f.mkdir();
			}

			String dir = parentDir + dirName;
			File f1 = new File(dir);
			String f1CanonicalPath = f1.getCanonicalPath();
			if(f1CanonicalPath.startsWith(parentDir)){

				f1.mkdir();
//                        Files.createFile("/karthiTest/"+sheetNames[i]+"."+this.format);

				File file = new File(dir + "/" + sheetName+"."+this.format);
				file.createNewFile();
				String canonicalPath = file.getCanonicalPath();
				if(canonicalPath.startsWith(parentDir)){

					out = new FileOutputStream(file.getAbsolutePath());
					out.write(outputData);
					out.close();
				}
			}

		}catch (Exception e) {
			// TODO Auto-generated catch block
			LOGGER.log(Level.WARNING,"[CONVERSION:FILECONVERSTER] PROBLEM IN FILE WRIRTER",e);
			//e.printStackTrace();
		}
		finally {
			try {
				out.close();
			} catch(Exception e) {
				LOGGER.log(Level.WARNING, "[CONVERSION:FILECONVERSTER] Problem in closing the stream", e);//No I18N
			}
		}

	}
	private byte[] zipWriteandGet(String dirName){

		String parentDir = Configuration.getString("app.home") + File.separator + "bin" + File.separator + "converterdocs" + File.separator;     //No I18N
		String dir = parentDir + dirName;
		FileOutputStream fos = null;
		FileInputStream fis = null;
		try{
			File outputFile = new File(parentDir + this.fileName+".zip");     //No I18N
			String canonicalPath = outputFile.getCanonicalPath();
			if(canonicalPath.startsWith(parentDir)) {

				fos = new FileOutputStream(outputFile);
				ZipOutputStream zos = new ZipOutputStream(fos);
				String[] fileList = new File(dir).list();

				for(String _fileName : fileList){

					File file = new File(dir+"/"+_fileName);
					String canonicalPath1 = file.getCanonicalPath();
					if(canonicalPath1.startsWith(parentDir)){

						fis = new FileInputStream(file);
						ZipEntry zipEntry = new ZipEntry(_fileName);
						zos.putNextEntry(zipEntry);
						byte[] bytes = new byte[1024];
						int length;
						while ((length = fis.read(bytes)) >= 0) {
							zos.write(bytes, 0, length);
						}
						zos.closeEntry();
						fis.close();
					}
				}

				zos.close();
				fos.close();
			}

			RemoteUtils.deleteUploadFile(dir, parentDir);

			File f = new File(parentDir + this.fileName+".zip");
			String canonicalPath1 = f.getCanonicalPath();
			if(canonicalPath1.startsWith(parentDir)){

				Path path = Paths.get(parentDir + this.fileName+".zip");
				byte[] data = Files.readAllBytes(path);
				return data;
			}

			return null;

		} catch(Exception e){
			LOGGER.log(Level.WARNING,"[CONVERSION:FILECONVERSTER]  Problem in Converting to ZIP file",e);
		}
		finally {
			try {
				fos.close();
				fis.close();
			} catch(Exception e) {
				LOGGER.log(Level.WARNING, "[CONVERSION:FILECONVERSTER] Problem in closing the stream", e);//No I18N
			}
		}

		return null;
	}

	private static Workbook parse(WorkbookContainer workbookContainer, byte[] zsheetBytes) throws Exception {
		final Workbook workBook = parse(workbookContainer.getWorkbook(null),workbookContainer.getResourceId(), zsheetBytes);

		MacroParser mParser = new MacroParser(workBook);
		mParser.parse(zsheetBytes);
		// for settings parser
		SettingsParser sParser = new SettingsParser();
		try (InputStream inputStream = new ByteArrayInputStream(zsheetBytes)) {
			workBook.setWorkbookSettings(sParser.parse(inputStream));
		}

		JSONObjectWrapper chartsJson = parseChartsFromZsheet(zsheetBytes);

		ChartEngineManager.setChartEngineVersion(workbookContainer, workBook);
		ChartEngineManager.getChartEngine(workbookContainer).populateWorkbookFromJSON(workbookContainer, workBook, chartsJson);


		return workBook;
	}

	private static JSONObjectWrapper parseChartsFromZsheet(byte[] bytes) throws Exception {

		try(ZipInputStream zin = new ZipInputStream(new ByteArrayInputStream(bytes))) {
			boolean isCharFile = false;
			ZipEntry zEntry;
			while((zEntry = zin.getNextEntry()) != null) {
				if(ZSImporter.CHART_FILE_NAME.equals(zEntry.getName())) {
					isCharFile = true;
					break;
				}
			}
			if(isCharFile) {
				LOGGER.log(Level.INFO, "[ZSEET_IMPORT] CHART DB JSON file available");
				final int CHART_FILE_READ_BUFFER_SIZE = 1024;
				StringBuilder sBuff = new StringBuilder();
				byte[] buffer = new byte[CHART_FILE_READ_BUFFER_SIZE];
				int read;
				while ((read = zin.read(buffer, 0, CHART_FILE_READ_BUFFER_SIZE)) >= 0) {
					sBuff.append(new String(buffer, 0, read));
				}
				JSONObjectWrapper chartJson = new JSONObjectWrapper(sBuff.toString());
				return chartJson;
				//GraphUtils.writerChartsJSONToDB(chartJson, container.getDocId(), container.getDocOwner());
			} else {

				LOGGER.log(Level.INFO, "[ZSEET_IMPORT] CHART DB JSON file not available");
				return null;
			}
		} catch(Exception e) {
			LOGGER.log(Level.WARNING, "[ZSEET_IMPORT-EXCEPTION] Exception in writing Charts", e);
			return null;
		}
	}
	private static Workbook parse(Workbook workBook,String resource_id, byte[] zsheetBytes) throws Exception {


		ZSWorkbookTransformer transformer = new ZSWorkbookTransformer();
		workBook.setParsing(true);
		transformer.constructWorkbook(workBook, null);

		ZSWorkbookParser parser = new ZSWorkbookParser(transformer);

		InputStream stream = null;
		ZipInputStream zipInputStream = null;
		try {
			final long orderJsonStarted = System.currentTimeMillis();
			stream = new ByteArrayInputStream(zsheetBytes); //It may be version doc or normal ZSHEET file. ~Mani.
			//	sheetFilesList = EngineUtils.getInstance().readSheetOrderFile(store, resourceId, EngineConstants.FILENAME_ORDER);
			zipInputStream = new ZipInputStream(stream);
			String orderEntryName = ZSStore.FileName.ORDER.toString().toLowerCase() + "." + ZSStore.FileExtn.JSON.toString().toLowerCase();
			List<String> traversedEntries = StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, orderEntryName); //sheetorder.json
			if (traversedEntries.isEmpty() || !orderEntryName.equals(traversedEntries.get(traversedEntries.size() - 1))) {
				LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]  traversedEntries : {0}", new Object[]{traversedEntries});
				throw new Exception("Entry not available in stream for: " + orderEntryName);
			}
			OrderFileReader orderFileReader = new OrderFileReader(zipInputStream);

			List<JSONObjectWrapper> sheetFilesList = orderFileReader.getOrderJSON();
			List<String> sheetToParseList = new ArrayList();
			for (JSONObjectWrapper jObj : sheetFilesList) {
				String fileName = jObj.getString("fn");
				sheetToParseList.add(fileName);
			}

			final long orderJsonEndedStableObjectStarted = System.currentTimeMillis();
			String stylesEntryName = ZSStore.FILENAME_STABLEOBJECTS + "." + ZSStore.FileExtn.XML.toString().toLowerCase();
			if (traversedEntries.contains(stylesEntryName)) { //styles.xml
				LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]getting stream again for {0}", ZSStore.STABLEOBJECTS_XML);
				StreamUtil.close(zipInputStream);
				StreamUtil.close(stream);
				stream = new ByteArrayInputStream(zsheetBytes); //It may be version doc or normal ZSHEET file. ~Mani.
				zipInputStream = new ZipInputStream(stream);
				traversedEntries = new ArrayList<>();
			}
			traversedEntries.addAll(StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, stylesEntryName)); //styles.xml
			if (traversedEntries.isEmpty() || !stylesEntryName.equals(traversedEntries.get(traversedEntries.size() - 1))) {
				LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]  traversedEntries : {0}", new Object[]{traversedEntries});
				throw new Exception("Entry not available in stream for: " + stylesEntryName);
			}
			parser.parseStream(zipInputStream);


			final long stableObjectEndedAdhocObjectsStarted = System.currentTimeMillis();
			// Reads astyles
			String astylesEntryName = ZSStore.FileName.ADHOCOBJECTS.toString().toLowerCase() + "." + ZSStore.FileExtn.XML.toString().toLowerCase();
			if (traversedEntries.contains(astylesEntryName)) { //astyles.xml
				LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]getting stream again for {0}", ZSStore.ADHOCOBJECTS_XML);
				StreamUtil.close(zipInputStream);
				StreamUtil.close(stream);
				stream = new ByteArrayInputStream(zsheetBytes); //It may be version doc or normal ZSHEET file. ~Mani.
				zipInputStream = new ZipInputStream(stream);
				traversedEntries = new ArrayList<>();
			}
			traversedEntries.addAll(StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, astylesEntryName)); //astyles.xml
			if (traversedEntries.isEmpty() || !astylesEntryName.equals(traversedEntries.get(traversedEntries.size() - 1))) {
				LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]  traversedEntries : {0}", new Object[]{traversedEntries});
				throw new Exception("Entry not available in stream for: " + astylesEntryName);
			}
			parser.parseStream(zipInputStream);


			final long adhocObjectsEndedSheetsStarted = System.currentTimeMillis();
			Map<String, Long> sheetTs = new LinkedHashMap();

			for (String name : sheetToParseList) {
				final long sheetStarted = System.currentTimeMillis();

				String sheetContentEntryName = name + File.separator + name + "." + ZSStore.FileExtn.XML.toString().toLowerCase();
				if (traversedEntries.contains(sheetContentEntryName)) { // 0#/content.xml
					LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]getting stream again for {0}", sheetContentEntryName);
					StreamUtil.close(zipInputStream);
					StreamUtil.close(stream);
					stream = new ByteArrayInputStream(zsheetBytes); //It may be version doc or normal ZSHEET file. ~Mani.
					zipInputStream = new ZipInputStream(stream);
					traversedEntries = new ArrayList<>();
				}
				traversedEntries.addAll(StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, sheetContentEntryName)); // 0#/content.xml
				if (traversedEntries.isEmpty() || !sheetContentEntryName.equals(traversedEntries.get(traversedEntries.size() - 1))) {
					LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]  traversedEntries : {0}", new Object[]{traversedEntries});
					throw new Exception("Entry not available in stream for: " + sheetContentEntryName);
				}
				parser.parseStream(zipInputStream);

				final long sheetEndedTableStarted = System.currentTimeMillis();
				sheetTs.put(name, sheetEndedTableStarted - sheetStarted);

				try {
					String tablesEntryName = name + File.separator + ZSStore.FileName.TABLES.toString().toLowerCase() + "." + ZSStore.FileExtn.XML.toString().toLowerCase();
					if (traversedEntries.contains(tablesEntryName)) { // 0#/tables.xml
						LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]getting stream again for {0}", tablesEntryName);
						StreamUtil.close(zipInputStream);
						StreamUtil.close(stream);
						stream = new ByteArrayInputStream(zsheetBytes); //It may be version doc or normal ZSHEET file. ~Mani.
						zipInputStream = new ZipInputStream(stream);
						traversedEntries = new ArrayList<>();
					}
					traversedEntries.addAll(StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, tablesEntryName)); // 0#/tables.xml
					if (!traversedEntries.isEmpty() && tablesEntryName.equals(traversedEntries.get(traversedEntries.size() - 1))) {
						parser.parseStream(zipInputStream);
					}
				} catch (Exception e) {
					LOGGER.log(Level.SEVERE, "SKIPPING TABLES PARSING!!! RESOURCE_KEY: " + resource_id + " Exception when parsing tables for Fragmented Sheet: " + name, e);
				}

				sheetTs.put(name + "_tables", System.currentTimeMillis() - sheetEndedTableStarted);

				final long tableEndedFilterStarted = System.currentTimeMillis();
				try {
					String tablesEntryName = name + File.separator + ZSStore.FileName.FILTERS.toString().toLowerCase() + "." + ZSStore.FileExtn.XML.toString().toLowerCase();
					if (traversedEntries.contains(tablesEntryName)) { // 0#/tables.xml
						LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]getting stream again for {0}", tablesEntryName);
						StreamUtil.close(zipInputStream);
						StreamUtil.close(stream);
						stream = new ByteArrayInputStream(zsheetBytes); //It may be version doc or normal ZSHEET file. ~Mani.
						zipInputStream = new ZipInputStream(stream);
						traversedEntries = new ArrayList<>();
					}
					traversedEntries.addAll(StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, tablesEntryName)); // 0#/tables.xml
					if (!traversedEntries.isEmpty() && tablesEntryName.equals(traversedEntries.get(traversedEntries.size() - 1))) {
						parser.parseStream(zipInputStream);
					}
				} catch (Exception e) {
					LOGGER.log(Level.SEVERE, "SKIPPING FILTERS PARSING!!! RESOURCE_KEY: " + resource_id + " Exception when parsing filters for Fragmented Sheet: " + name, e);
				}

				sheetTs.put(name + "_filters", System.currentTimeMillis() - tableEndedFilterStarted);
			}

			final long pivotStarted = System.currentTimeMillis();
			String pivotsEntryName = ZSStore.FileName.PIVOTS.toString().toLowerCase() + "." + ZSStore.FileExtn.XML.toString().toLowerCase();
			if (traversedEntries.contains(pivotsEntryName)) { //pivots.xml
				LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]getting stream again for {0}", ZSStore.PIVOTS_XML);
				StreamUtil.close(zipInputStream);
				StreamUtil.close(stream);
				stream = new ByteArrayInputStream(zsheetBytes); //It may be version doc or normal ZSHEET file. ~Mani.
				zipInputStream = new ZipInputStream(stream);
				traversedEntries = new ArrayList<>();
			}
			traversedEntries.addAll(StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, pivotsEntryName)); //pivots.xml
			if (traversedEntries.isEmpty() || !pivotsEntryName.equals(traversedEntries.get(traversedEntries.size() - 1))) {
				LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[READ-FORMAT-INKLING][ZSHEET]  traversedEntries : {0}", new Object[]{traversedEntries});
				throw new Exception("Entry not available in stream for: " + pivotsEntryName);
			}
			parser.parseStream(zipInputStream);


			final long pivotEnded = System.currentTimeMillis();
			//////
			//}
			ZSLogger.log(LOGGER, Level.INFO, "RESOURCE_KEY: {0} >>> Time taken in ZSHEET File parser \n\torderJson: {1}\n\tstableObjects: {2}\n\tadhocObjects: {3}\n\tsheets: {4}\n\t\tsplits: {5}\n\tpivots: {6}",
					new Object[]{
							resource_id,
							(orderJsonEndedStableObjectStarted - orderJsonStarted),//orderJson
							(stableObjectEndedAdhocObjectsStarted - orderJsonEndedStableObjectStarted),//stableObjects
							(adhocObjectsEndedSheetsStarted - stableObjectEndedAdhocObjectsStarted),//adhocObjects
							(pivotStarted - adhocObjectsEndedSheetsStarted),//sheets
							sheetTs,//splits
							(pivotEnded - pivotStarted)//pivots
					},
					new ZSLogger.ZSCustomAppLogFieldKey[]{
							ZSLogger.ZSCustomAppLogFieldKey.TIME_TAKEN
					},
					new Object[]{
							(pivotEnded - orderJsonStarted)
					});

			transformer.endWorkbook();
			Sheet sheets[] = workBook.getSheets();
			for (int i = 0; i < sheets.length; i++) {
				// getAssociatedName() now will generate a new name if the associatedName is
				// already not set. Calling getAssociatedName() here is enough and we must call
				// it here
				sheets[i].getAssociatedName();
			}
			/////////////////////////////
			workBook.setIsAllFragmentsModified(true);
			return workBook;
		} catch (Exception e) {
			LOGGER.log(Level.SEVERE, e.getMessage(), e);
			throw (new Exception("Document.Parse.Error", e));
		} finally {
			workBook.setParsing(false);
			try {
				if (zipInputStream != null) {
					StreamUtil.close(zipInputStream);
				}
				if (stream != null) {
					StreamUtil.close(stream);
				}
			} catch (Exception e1) {
				LOGGER.log(Level.WARNING, null, e1);
			}
		}
	}

	private static byte[] writeZSheetFile(WorkbookContainer container) throws Exception {
		/*
		 * Workbook and ImageBook will be in state "versionNo"
		 * But Charts will be in state "top"
		 */
		Workbook workBook = container.getWorkbook(null);
		ImageBook imageBook = container.getImageBook(null);
//		JSONObjectWrapper chartDBJsonObject = CommonChartEngineUtils.getChartsDataFromTopVersion(container.getDocOwner(), Long.parseLong(container.getDocId()));
		LOGGER.log(Level.OFF, "[WRITE-FORMAT-INKLING][OLD-ZSHEET]EngineUtils1.createTempZSFileWithTopChartDBFile() called for ", container.getResourceKey());
		//return EngineUtils1.createTempZSHEETFile(container, workBook, imageBook, chartDBJsonObject, true, container.getDocOwnerZUID(), false);
		File zsFile = EngineUtils1.createTempZSFileWithTopChartDBFile(container,null,true,null, false);
		try(FileInputStream zsFileIS = new FileInputStream(zsFile)) {
			return IOUtils.toByteArray(zsFileIS);
		} finally {
			if (zsFile != null) {
				if (zsFile.exists()) {
					zsFile.delete();
				}
			}
		}
	}


	private byte[] getCSVorTSVBytes(WorkbookContainer container){
		try {
			Workbook workbook = container.getWorkbook(null);
			byte[] outputByte = null;
			if ("true".equalsIgnoreCase(this.isMultiple)) {
				Sheet[] sheetList = workbook.getSheets();
				for (Sheet _sheet : sheetList) {
					if ("csv".equalsIgnoreCase(this.format)) {
						outputByte = EngineUtils1.getCSVData(_sheet, 0, 0, _sheet.getUsedRowIndex()-1, _sheet.getUsedColumnIndex()-1, false);
					} else if ("tsv".equalsIgnoreCase(this.format)) {
						outputByte = EngineUtils1.getTSVData(_sheet, 0, 0, _sheet.getUsedRowIndex()-1, _sheet.getUsedColumnIndex()-1, false);
					}
					this.fileWriter(this.dirName + "_" + this.sheetName, _sheet.getName(), outputByte);

				}
				return this.zipWriteandGet(this.dirName + "_" + this.sheetName);
			} else {
				Sheet sheetToExport;
				if (this.outSheetName != null && !this.outSheetName.isEmpty()) {
					sheetToExport = workbook.getSheet(this.outSheetName);
				} else if (this.outSheetIndex != -1) {
					sheetToExport = workbook.getSheet(this.outSheetIndex);
				} else {
					sheetToExport = workbook.getSheet(0);
				}
				if ("csv".equalsIgnoreCase(this.format)) {
					return EngineUtils1.getCSVData(sheetToExport, 0, 0, sheetToExport.getUsedRowIndex(), sheetToExport.getUsedColumnIndex()-1, false);
				} else if ("tsv".equalsIgnoreCase(this.format)) {
					return EngineUtils1.getTSVData(sheetToExport, 0, 0, sheetToExport.getUsedRowIndex(), sheetToExport.getUsedColumnIndex()-1, false);
				}

			}
		}catch (Exception e){

		}
		return  null;
	}

	public byte[] convertDocument( HttpServletRequest request) throws Exception {
		String outputOp = request.getParameter("output_options");
		if(outputOp != null){
			JsonObject outputObj =  new JsonParser().parse(outputOp).getAsJsonObject();
			if(outputObj.has("format")){
				this.format = outputObj.get("format").getAsString();
			}
			if(outputObj.has("document_name")){
				this.fileName = outputObj.get("document_name").getAsString();
			}
		}else{
			this.format = request.getParameter("format");
			this.dirName = System.currentTimeMillis()+"";
			this.fileName =  (String) request.getParameter("workbookname");
			this.isMultiple = (String) request.getParameter("ismultiple");
			this.outSheetName = (String) request.getParameter("sheetname");
			this.pdf_settings = request.getParameter("page_settings")!= null?new JSONObjectWrapper(request.getParameter("page_settings")):null;

		}
		String sheetIndex =request.getParameter("sheetindex");
		if(sheetIndex != null && sheetIndex != ""){
			this.outSheetIndex = Integer.parseInt(sheetIndex);
		}
		FileInputStream fis =null;
		int index = 0;
		LOGGER.info("[CONVERSION:FILECONVERSTER]"+this.format);
		WorkbookContainer container = null; //getNewContainerAndWorkBook();
		ArrayList<UploadedFileItem> filesList = (ArrayList<UploadedFileItem>)request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST);
		int i =0;
		if (request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST) != null ) {
			for (UploadedFileItem uploadedFileItem : filesList) {
				try {
					File f = uploadedFileItem.getUploadedFile();
					fis = new FileInputStream(f);
					fileContent = ClientUtils.readStream(fis);
					String filename = uploadedFileItem.getFileName();

					this.sheetName  = filename.substring(0,filename.lastIndexOf("."));
					this.fileFormat = (filename.substring(filename.lastIndexOf('.') + 1)).toLowerCase();
					this.charset = ConnectionUtils.getCharEncoding(fis);

					if(this.fileName == null || "".equalsIgnoreCase(this.fileName)){
						this.fileName = fileName; //TODO : This should be filename
					}
					LOGGER.info("[CONVERSION:FILECONVERSTER]: Converting {0} to {1}"+new Object[]{this.fileFormat, this.format});
// 					if("xls".equalsIgnoreCase(this.fileFormat) ||"ods".equalsIgnoreCase(this.fileFormat)){
// 						if(ISASPOSECONVERSIONENABLED){
// 							LOGGER.info("[CONVERSION:FILECONVERSTER] Converting "+this.fileFormat+" to "+this.format);
// 							return AsposeUtils.getSheetAsposeConversion(fileContent, this.fileFormat, this.format);
// 						}
// //						else{
// //							ConnectionPool connectionpool =new ConnectionPool();
// //							ConnectionObject	connectionObject = connectionpool.getRemoteDesktop();
// //							xComponent = getXComponent(connectionObject,null);
// //							String liboHost = connectionObject.getLiboHost();
// //							XSpreadsheetDocument xDocument = (XSpreadsheetDocument) UnoRuntime.queryInterface(XSpreadsheetDocument.class, xComponent);

// //							if("csv".equalsIgnoreCase(this.format) || "tsv".equalsIgnoreCase(this.format)){
// //								return this.getLiboZipOutStream(xDocument);
// //							}
// //							return this.getLiboOutStream(xDocument);
// //						}

// 					}else{
						container= getNewContainerAndWorkBook();
						importDocument(container,this.fileFormat,i);
						byte[] responseBytes =  exportDocument(container,format);
						try {
							String password = request.getParameter("password");
							LOGGER.info("[CONVERSION:FILECONVERSTER] Encrypted Export Enabled {0}"+new Object[]{format});
							if (password != null) {
								responseBytes = ImportExportUtil.encryptDocument(format, responseBytes, password);
							}
						}catch (Exception e) {
							LOGGER.info("[CONVERSION:FILECONVERSTER] Encrypted Export Error {0}"+new Object[]{e});

						}
						return responseBytes;
					//}

				} catch (Exception e) {
							LOGGER.info("[CONVERSION:FILECONVERSTER] Encrypted Export Error {0}"+new Object[]{e});
				}finally {
					try {
						fis.close();
						if(container!=null) {
							RemoteUtils.removeRemoteDoc(Long.parseLong(container.getDocId()),null,container.getDocOwner());
						}
					} catch(Exception e) {
						LOGGER.log(Level.WARNING, "[FILECONVERSION] Problem in closing the stream", e);//No I18N
					}
				}
				i++;
			}
		}
		return null;
	}
	private  void importDocument(WorkbookContainer container,String inputFormat,int index){
		try{
			Workbook workbook = container.getWorkbook(null);
			switch(inputFormat){
			case "xls":
			case "ods":

				LOGGER.info("[CONVERSION:FILECONVERSTER] Converting "+this.fileFormat+" to xlsx");

					fileContent = AsposeUtils.getSheetAsposeConversion(fileContent, this.fileFormat,"xlsx" );//No i18N
				case "xlsx":
					// Removing default sheet first to avoid inserting copied sheet with the same name.
					String shName= workbook.getSheet(0).getName();
					if(shName!=null) {
						workbook.removeSheet(shName);
					}
					try {
						XLSXParserAgent xlsxpa = new XLSXParserAgent(workbook);
						xlsxpa.parseXlsxDocument(this.fileContent);
						xlsxpa.makeDbEntries(container, new ConcurrentHashMap<>(), true);
					} finally {
						LOGGER.log(Level.OFF, "FEATURES_NOT_PARSED :{0}, {1}", new Object[]{XLSXParserAgent.FEATURES_NOT_PARSED.get(), container.getResourceKey()});
						XLSXParserAgent.FEATURES_NOT_PARSED.remove();
					}

					break;
				case "zsheet":
					Workbook wb = this.parse(container, fileContent);
					container.setWorkbook(wb, false, false);
					break;
				case "csv":
				case "tsv":

					String ss = new String(this.fileContent, this.charset);
					this.fileContent = ss.getBytes();
					this.copyDelimeterSeperatedDataToWorkbook(container,index++,fileContent,sheetName);
					break;
				case "json":
					BufferedReader bufferedReader=new BufferedReader(new InputStreamReader(new ByteArrayInputStream(fileContent)));
					JsonTableParser jsonTableParser=new JsonTableParser(bufferedReader);
					jsonTableParser.copyJsonTableToSheet(container,index++,null);//passing container because freeze panes requires container
					break;

			}
		}catch (Exception e){

		}
	}

	private byte[] exportDocument(WorkbookContainer container,String outputformat){
		try{
			LOGGER.info("[CONVERSION:FILECONVERSTER] FileConverter exportDocument exporting as:"+outputformat);
			Workbook workbook = container.getWorkbook(null);
			Exporter exporter= new Exporter(container, outputformat);
			switch (outputformat){
				case "xlsx":
					return exporter.getXLSXBytes();
				case "zsheet":
					return writeZSheetFile(container);
				case "pdf":
					if(pdf_settings==null || pdf_settings.isEmpty()) {
						JSONObjectWrapper settings = new JSONObjectWrapper();
						settings.set("addGridLines", false);
						settings.set("pageOrder", "overDown");
						return exporter.getDocumentPDFBytes(settings);
					}
					JSONObject _pdf_settings=DownloadAPIHandler.getPDFOptions(pdf_settings.getJsonObject());
					return exporter.getDocumentPDFBytes(new JSONObjectWrapper(_pdf_settings));
				case "csv":
				case "tsv":
					return getCSVorTSVBytes(container);
				case "json":
					return getJSONDocumentBytes(workbook,this.fileName);
				case "xls":
				case "ods":
					File tempOds = EngineUtils1.createODSFileWithImageAndChart(container,null,workbook,container.getImageBook(null),true,null,false);
					exporter.setTempFile(tempOds);
					return exporter.getDocumentBytes();
			}
		}catch (Exception e){
			LOGGER.info("[CONVERSION:FILECONVERSTER] FileConverter exportDocument Exceptions:"+e);
		}
		return null;
	}
}




