/*$Id$*/
package com.zoho.sheet.conversion;

import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.zoho.websheet.model.Protection;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Workbook.PatternSetting;
import com.adventnet.zoho.websheet.model.WorkbookContainer.ContainerEntity;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.pivot.PivotUtil;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.model.xlsxaparser_.*;
import com.adventnet.zoho.websheet.model.zs.ZSZipInputStream;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import com.aspose.cells.CellsException;
import com.zoho.sheet.action.FileReader;
import com.zoho.sheet.util.AsposeUtils;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.sheet.util.ZSStats;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;


/**
 *
 * <AUTHOR>
 */
public class XlsxImporter extends Importer {
    private static final Logger LOGGER = Logger.getLogger(XlsxImporter.class.getName());

    protected List<String> sheetNames = null;
    private PatternSetting patternSetting = PatternSetting.WILDCARD;
    private boolean isAsposeConversion = false;
    private static final String MACROS_ENTRY = "macros.prop";//No I18N
    private static final String XLSX_FILE_ENTRY = "xlsx_file.xlsx";//No I18N

    @Override
    public int importDocument(FileReader fr, RemoteBook remoteBook,boolean isDBUpdateReq,WorkbookContainer container) throws Exception {
        logger.log(Level.INFO, "[XLSX-IMPORTER][CONVERSION:IMPORT][REMOTE]start... {0}", this.resourceId);
        long startTime = System.currentTimeMillis();
        WorkbookContainer newContainer = null;
        boolean isDocInsertedIntoDB = false;

        try {
            removeExtensionFromRemoteDocumentName();

            if (docType == Constants.REMOTE_DOCUMENT && remoteBook.isExistingDoc()) {//remote collab 2nd user joining , do nothing
                logger.log(Level.INFO, "[XLSX-IMPORTER] [CONVERSION:IMPORT] Existing Remote Document, Only DB updates:" + resourceId + ":" + docOwner);
            } else {
                //Was there in old importer ,so maintaining the logs
                if (docType == Constants.REMOTE_DOCUMENT && remoteBook.allowEmptyFileContent()) {
                    logger.log(Level.INFO, "[XLSX-IMPORTER] [CONVERSION:IMPORT] This Remote Document allows empty contants so set console name");
                }
            }

            if(!isFileSizeAllowed()) {
                return ZFSNGConstants.CONVERSION_FAILURE_DOCUMENT_SIZE_TOO_LARGE;
            }

            writeDocumentLocale();
if(isDBUpdateReq) {
    updateRemoteDB(remoteBook, docType);
}

            isDocInsertedIntoDB = true;
            if (!remoteBook.isExistingDoc())//for Existing Doc don't import again ( Remote  Collab CASE)
            {
                if(container != null){
                    newContainer =container;
                }else {
                    newContainer = getNewContainer(); //container to copy data and save
                }
//                XLSXParserAgent xlsxParserAgent = parse(new Workbook(), newContainer, this.contentBytes, this.zuId);
//                this.sheetNames = xlsxParserAgent.getSheetNames();

                Map<String, String> macroNameToContentMap = asposeConversion();//xlsx,xls,ods changed to xlsx bytes here, and format changed to zxlsx
                XlsxScanResponse xlsxScanResponse = XLSXParser.pre_parse_xlsx_scan(this.contentBytes);
                WorkbookContainer effectivelyFinalContainer = newContainer;

//                AtomicBoolean postImportSuccess = new AtomicBoolean(false);
//                Thread thread = new Thread(()-> {
//                    try {
//                        XlsxImporter.postImport(effectivelyFinalContainer, effectivelyFinalContainer, effectivelyFinalContainer.getDocOwner(), null, new Object[]{this.contentBytes, macroNameToContentMap});
//                        postImportSuccess.set(true);
//                    } catch(Exception e) {
//                        LOGGER.log(Level.OFF, effectivelyFinalContainer.getResourceKey(), e);
//                    }
//                });
//                thread.start();

                Store store = newContainer.getStore(Long.valueOf(docId), ZSStore.FileName.DOCUMENT);
                SheetFileInfo sheetfileInfo = store.getFileInfo(Long.valueOf(docId), EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ZXLSX);
                if(sheetfileInfo == null){
                    logger.log(Level.INFO, "[XLSX_IMPORTER] CREATING FILE INFO for document.*");
                    sheetfileInfo = store.createFileInfo(Long.valueOf(docId), EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ZXLSX);
                }
                long resId = sheetfileInfo.getResourceId();
                HashMap<String, Object> writeInfo = store.write(resId, sheetfileInfo);
                OutputStream os = (OutputStream) writeInfo.get("OS");
                write_zxlsx(os, this.contentBytes, macroNameToContentMap);
                store.finishWrite(writeInfo);

//                thread.join();

//                if(!postImportSuccess.get()) {
//                    LOGGER.log(Level.OFF, "post import failed");
//                    throw new XlsxLimitException("post import failed", ZFSNGConstants.CONVERSION_FAILURE);//No I18N
//                }

//                new Thread(()-> {
//                    try {
//                        writeSearchFileAndThumbnail(this.contentBytes, effectivelyFinalContainer);
//                    } catch(Exception e) {
//                        LOGGER.log(Level.OFF,effectivelyFinalContainer.getResourceKey(), e);
//                    }
//                }).start();

                //set sheet name
//                newContainer.getWorkbook(null);
//                newContainer.getImageBook(null);
//
//                long saveStartTime = System.currentTimeMillis();
//                if (!newContainer.saveAsOneFile(null, null, true, false)) {
//                    logger.log(Level.INFO, "[XLSX-IMPORTER-TIME-STATS][SAVE-FAILED] :{0}ms ", new Object[]{(System.currentTimeMillis() - startTime)});
//                    Exception import_save_failed = new Exception("Import Save failed");
//                    throw import_save_failed;
//                }
//                logger.log(Level.INFO, "[XLSX-IMPORTER-TIME-STATS][SAVE] :{0}ms", new Object[]{(System.currentTimeMillis() - saveStartTime)});
//                            writeDocumentsToZSFragments(newContainer);
            }
            logger.log(Level.INFO, "[XLSX-IMPORTER-TIME-STATS][REMOTE][XLSX-IMPORTER-SUCCESS] :{0}ms Document Id={1} ", new Object[]{(System.currentTimeMillis() - startTime), this.docId});
            if(isAsposeConversion) {
            	ZSStats.increment(ZSStats.IMPORT_ASPOSE_SUCCESS);
            }
            ZSStats.increment(ZSStats.IMPORT_SUCCESS);
            ZSStats.incrementByGroup(this.contentBytes.length, ZSStats.IMPORT_SIZE_RANGE_VALUE_ARRAY, ZSStats.IMPORT_SIZE_RANGE_STATS_ARRAY);
            return ZFSNGConstants.CONVERSION_SUCCESS;
        } catch(XlsxLimitException e) {
            logger.log(Level.OFF, "", e);
            return e.getZFSNGConstants();
        }  catch(ZipException zipException) {
            logger.log(Level.OFF, "", zipException);
            return ZFSNGConstants.CONVERSION_FAILURE;
        }	catch (com.aspose.cells.CellsException ae ) {
        	logger.log(Level.INFO, "[XLSX-IMPORTER-EXCEPTION] importRemoteDocument CellsException ", ae);
        	return ZFSNGConstants.CONVERSION_FAILURE;
        }
        catch (Throwable e) {
            ZSStats.increment(ZSStats.IMPORT_XLSX_PARSER_FAILURE);
            logger.log(Level.OFF, "", e);
            if(isDocInsertedIntoDB) {
                deleteDocument(newContainer);
            }

            int liboImportCode = importRemoteDocumentThroughLibo(fr, remoteBook,newContainer);
            if(liboImportCode > ZFSNGConstants.CONVERSION_NOTHAPPENED) {
                ZSStats.increment(ZSStats.IMPORT_XLSX_FAIL_LIBO_SUCCESS);
            }
            return liboImportCode;
        }
    }

    private void writeSearchFileToDocStore(List<ValueInterface> sharedStrings, WorkbookContainer container) {
        Function<Set<String>, String> toCSV = (s) -> {
            Writer writer = new StringWriter();
            try {
                CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT);
                for (String content : s) {
                    if(content != null) {
                        csvPrinter.print(content);
                    }
                }
            } catch (Exception e) {
                logger.log(Level.OFF, "", e);
            }
            return writer.toString();
        };

        if(sharedStrings != null && !sharedStrings.isEmpty()) {
            Instant now = Instant.now();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            try(ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
                zipOutputStream.putNextEntry(new ZipEntry("xlsx_sharedStrings.csv"));//No I18N
                Set<String> set = new HashSet<>();
                for(ValueInterface sharedString : sharedStrings) {
                    set.add(((ZSString)(sharedString.getValue().getRawValue())).getBaseStringValue());
                }
                zipOutputStream.write(toCSV.apply(set).getBytes());
            } catch(IOException e) {
                LOGGER.log(Level.OFF, "", e);
            }
            Instant now1 = Instant.now();
            container.writeSearchFileToDocumentStore(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()));
            LOGGER.log(Level.OFF, "writing xlsx_sharedStrings.csv took {0}, writing to doc store took {1}", new Object[]{Duration.between(now, now1).toMillis(), Duration.between(now1, Instant.now()).toMillis()});
        }
    }


    @Override
    public int importDocument() throws Exception {
        logger.log(Level.INFO, "[XLSX-IMPORTER][CONVERSION:IMPORT] Importing: {0}", this.resourceId);
        long startTime = System.currentTimeMillis();
        WorkbookContainer oldContainer = null;
        WorkbookContainer newContainer = null;

        boolean isDocInsertedIntoDB = false;

        try {
            //doupt: below line is useless here
            removeExtensionFromRemoteDocumentName();

            //doupt: below lines is useless here
            if (docType == Constants.REMOTE_DOCUMENT && remoteBook.isExistingDoc()) {//remote collab 2nd user joining , do nothing
                logger.log(Level.INFO, "[XLSX-IMPORTER] [CONVERSION:IMPORT] Existing Remote Document, Only DB updates:" + resourceId + ":" + docOwner);
            } else {
                //Was there in old importer ,so maintaining the logs
                if (docType == Constants.REMOTE_DOCUMENT && remoteBook.allowEmptyFileContent()) {
                    logger.log(Level.INFO, "[XLSX-IMPORTER] [CONVERSION:IMPORT] This Remote Document allows empty contants so set console name");
                }
            }

//			doubt: below size-check is not required as for non-remote file size is already checked
//          Size check removed from here as it is already there in importer and there were no prints of the check here.
//          boolean fileSizeAllowed = isFileSizeAllowed();

//            if(!fileSizeAllowed) {
//                xlsxImportCode = ZFSNGConstants.CONVERSION_FAILURE_DOCUMENT_SIZE_TOO_LARGE;
//            } else {
            writeDocumentLocale();
            //Updating DB Entries
            if (docId == null) {//new Document import
                docId = DocumentUtils.addNewDocumentToDB(docOwner, docName, importAsTemplate, docType, true, resourceId)[0];
                isDocInsertedIntoDB = true;
            } else {//special case , import over checkedout version in docs,need to read ,delete and republish ranges etc
                logger.log(Level.INFO, "[XLSX-IMPORTER] UPDATEPUBLISHRANGE set true");
                isVersionUpload = true;
            }

            oldContainer = getChunkContainer();//just used to pass around zuid,docid etc
            this.zuId = oldContainer.getDocOwnerZUID();

            newContainer = getNewContainer(); //container to copy data and save

//            XLSXParserAgent xlsxParserAgent = parse(new Workbook(), newContainer, this.contentBytes, this.zuId);
//            this.sheetNames = xlsxParserAgent.getSheetNames();
//            XMLFile thumbnail = xlsxParserAgent.getThumbnail();
//            //set sheet name
//

            Map<String, String> macroNameToContentMap = asposeConversion();
            XlsxScanResponse xlsxScanResponse = XLSXParser.pre_parse_xlsx_scan(this.contentBytes);

            if (isVersionUpload) {
                EngineUtils1.clearFragmentsFile(oldContainer);
                try {
                    oldContainer.deleteFile(Long.valueOf(docId), ZSStore.FileName.DOCUMENT, ZSStore.FileExtn.ODS, null);
                    oldContainer.deleteFile(Long.valueOf(docId), ZSStore.FileName.DOCUMENT, ZSStore.FileExtn.ZSHEET, null);
                    oldContainer.deleteFile(Long.valueOf(docId), ZSStore.FileName.DOCUMENT, ZSStore.FileExtn.ZXLSX, null);
                    XlsxImporter.deleteXlsxMetaFileFromDocumentStore(oldContainer);
                } catch(Exception e) {
                    logger.log(Level.OFF, "", e);
                }
                EngineUtils.removeSheetMetaInfo(Long.valueOf(oldContainer.getDocOwnerZUID()), docOwner, Long.valueOf(docId));
                this.sheetNames = XLSXParser.getSheetNamesFromWorkbookXml(this.contentBytes);
                handleSheetSpecificResources(ImportExportUtil.getDocumentSheetsDO(oldContainer.getDocOwner(), Long.parseLong(this.docId)), sheetNames, oldContainer.getDocOwner());
            }
            WorkbookContainer effectivelyFinalContainer = newContainer;
            AtomicBoolean postImportSuccess = new AtomicBoolean(false);
            Thread thread = new Thread(()-> {
                try {
                    XlsxImporter.postImport(effectivelyFinalContainer, effectivelyFinalContainer, effectivelyFinalContainer.getDocOwner(), this.zfsngVersionId, new Object[]{this.contentBytes, macroNameToContentMap});
                    postImportSuccess.set(true);
                } catch(Exception e) {
                    LOGGER.log(Level.OFF, effectivelyFinalContainer.getResourceKey(), e);
                }
            });
            thread.start();

            Store store = oldContainer.getStore(Long.valueOf(docId), ZSStore.FileName.DOCUMENT);
            SheetFileInfo sheetfileInfo = store.getFileInfo(Long.valueOf(docId), EngineConstants.FILENAME_DOCUMENT,EngineConstants.FILEEXTN_ZXLSX );
            if(sheetfileInfo == null){
                logger.log(Level.INFO, "[XLSX_IMPORTER] CREATING FILE INFO for document.*");
                sheetfileInfo = store.createFileInfo(Long.valueOf(docId), EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ZXLSX);
            }
            long resId = sheetfileInfo.getResourceId();
            HashMap<String, Object> writeInfo = store.write(resId, sheetfileInfo);
            OutputStream os = (OutputStream) writeInfo.get("OS");
            write_zxlsx(os, this.contentBytes, macroNameToContentMap);
            store.finishWrite(writeInfo);

            thread.join();

            if(!postImportSuccess.get()) {
                LOGGER.log(Level.OFF, "post import failed");
                throw new XlsxLimitException("post import failed", ZFSNGConstants.CONVERSION_FAILURE);//No I18N
            }

            new Thread(()-> {
                try {
                    writeSearchFileAndThumbnail(this.contentBytes, effectivelyFinalContainer);
                } catch(Exception e) {
                    LOGGER.log(Level.OFF,effectivelyFinalContainer.getResourceKey(), e);
                }
            }).start();

//            EngineUtils1.clearFragmentsFile(newContainer);
//            newContainer.deleteFile(Long.valueOf(docId), ZSStore.FileName.DOCUMENT, ZSStore.FileExtn.XLSX, null);
//            newContainer.getWorkbook(null);
//            newContainer.getImageBook(null);
//
//            //Save and versioning done seperately to prevent error when zfsngversion_id is already present(conversion call from docs)
//            //need to check xlist for import over version in docs case
//            long time = System.currentTimeMillis();
//            if (!newContainer.saveAsOneFile(null, null, true, true)) //label null will prevent versioning
//            {
//                logger.log(Level.INFO, "[XLSX-IMPORTER-TIME-STATS][SAVE-FAILED] :{0}ms ", new Object[]{(System.currentTimeMillis() - startTime)});
//                Exception import_save_failed = new Exception("Import Save failed");
//                throw import_save_failed;
//            }
//            logger.log(Level.INFO, "[XLSX-IMPORTER-TIME-STATS][SAVE] :{0}ms", new Object[]{(System.currentTimeMillis() - time)});
////                        writeDocumentsToZSFragments(newContainer);
//
//            postImport(oldContainer, newContainer, thumbnail, loginName, zfsngVersionId);

            if (isRename) {
                int i = docName.lastIndexOf(".");
                String newDocName = docName;
                if(i != -1) {
                    newDocName = docName.substring(0, i);
                }
                ZohoFS.renameResource(zuId, resourceId, newDocName, importedByZuid);
            }

            logger.log(Level.INFO, "[XLSX-IMPORTER-TIME-STATS][XLSX-IMPORTER-SUCCESS] :{0}ms Document Id={1} ", new Object[]{(System.currentTimeMillis() - startTime), this.docId});
            if(isAsposeConversion) {
            	ZSStats.increment(ZSStats.IMPORT_ASPOSE_SUCCESS);
            }
            ZSStats.increment(ZSStats.IMPORT_SUCCESS);
            ZSStats.incrementByGroup(this.contentBytes.length, ZSStats.IMPORT_SIZE_RANGE_VALUE_ARRAY, ZSStats.IMPORT_SIZE_RANGE_STATS_ARRAY);
            return ZFSNGConstants.CONVERSION_SUCCESS;
        } catch(XlsxLimitException e) {
            logger.log(Level.OFF, "", e);
            return e.getZFSNGConstants();
        } catch(ZipException zipException) {
            logger.log(Level.OFF, "", zipException);
            return ZFSNGConstants.CONVERSION_FAILURE;
        }	catch (CellsException ae ) {
        	logger.log(Level.INFO, "[XLSX-IMPORTER-EXCEPTION] importSheetDocument CellsException", ae);
        	return ZFSNGConstants.CONVERSION_FAILURE;
        }catch (Throwable e) {
            logger.log(Level.OFF, "", e);
            ZSStats.increment(ZSStats.IMPORT_XLSX_PARSER_FAILURE);
            if (isVersionUpload) {
                logger.log(Level.INFO, "[XLSX-IMPORTER-EXCEPTION] Sheet Parser Error while importing version in checkedout Sheet , reverting to previous version");
                String topVersion = ZohoFS.getTopVersion(this.docOwner, this.resourceId);
                newContainer.revertToVersion(topVersion);//In case import failes Document will be unopenable because botwh fragment and head are absent , so revert to previous version
            }
            if(isDocInsertedIntoDB) {
                deleteDocument(newContainer);
            }
            int liboImportCode = importDocumentThroughLibo();
            if(liboImportCode > ZFSNGConstants.CONVERSION_NOTHAPPENED) {
                ZSStats.increment(ZSStats.IMPORT_XLSX_FAIL_LIBO_SUCCESS);
            }
            return liboImportCode;
        }
    }

    private void writeSearchFileAndThumbnail(byte[] bytes, WorkbookContainer container) throws Exception {
        List<ValueInterface> sharedStrings = new ArrayList<>();
        byte[] thumbnail = null;
        String thumbnailExtension = null;
        StringJoiner stringJoiner = new StringJoiner("\n");//No I18N
        try {
            int filesParsed = 0;
            ZipInputStream zipInputStream = new ZSZipInputStream(new ByteArrayInputStream(bytes));
            for(ZipEntry ze = zipInputStream.getNextEntry(); ze != null; ze = zipInputStream.getNextEntry()) {
                if(ze.getName().endsWith("sharedStrings.xml")) {
                    filesParsed++;
                    Instant now = Instant.now();
                    sharedStrings = XLSXParser.parseSharedStrings(zipInputStream);
                    stringJoiner.add(ze.getName() + ":" + Duration.between(now, Instant.now()).toMillis());
                } else if(ze.getName().contains("thumbnail")) {
                    filesParsed++;
                    Instant now = Instant.now();
                    thumbnail = IOUtils.toByteArray(zipInputStream);
                    thumbnailExtension = ze.getName().substring(ze.getName().lastIndexOf(".") + 1);
                    stringJoiner.add(ze.getName() + ":" + Duration.between(now, Instant.now()).toMillis());
                }
                if(filesParsed > 1) {
                    break;
                }
            }
            zipInputStream.close();

            writeSearchFileToDocStore(sharedStrings, container);

            DataObject versionDO = DocumentUtils.getVersionDO(container.getDocId(), container.getDocOwner());
            long versionResourceId = (Long) versionDO.getFirstRow("DocumentVersion").get("VERSION_ID");//No I18N
            container.copySearchFileFromDocumentToVersionStore(versionResourceId);

            if(thumbnail != null) {
                Instant now = Instant.now();
                if("true".equals(EnginePropertyUtil.getSheetPropertyValue("EnableThumbNail")) && DocumentUtils.checkThumbnailHoldOnTime(container)) {
                    if(container.getResourceKey() != null && ! container.isRemoteMode() && ! container.isGdriveDoc()) {
                        try(ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(thumbnail)) {
                            ZohoFS.addOrUpdateExtractedImage(container.getDocsSpaceId(), container.getResourceId(), null, byteArrayInputStream, thumbnailExtension);
                            container.setThumbnailCreatedTime(System.currentTimeMillis());
                            stringJoiner.add("thumbnail" + ":" + Duration.between(now, Instant.now()).toMillis());
                        } catch(Exception e) {
                            logger.log(Level.OFF, "[XLSX-IMPORTER] exception while using thumbnail found in docProps", e);
                        }
                    }
                }
            }
        } finally {
            LOGGER.log(Level.OFF, "{0}, {1}", new Object[]{container.getResourceKey(), stringJoiner.toString()});
        }
    }

    private void removeExtensionFromRemoteDocumentName() {
        if (docType == Constants.REMOTE_DOCUMENT) {
            if (docName.contains(format)) {
                docName = docName.substring(0, docName.lastIndexOf("."));
            }
        }
    }

    private void writeDocumentLocale() {
        //This function shouldn't fail-import as it is not mandatory to writeDocumentLocale

        //Writing the Document Locale TODO: Remove the logs, once stable
        try {
        	if(docType != Constants.REMOTE_DOCUMENT){
	            logger.log(Level.INFO, "[CONVERSION:IMPORT] Writing Document Locale:");
	            DocumentUtils.writeDocumentLocale(zuId, resourceId, lang, countrycode, patternSetting);
        	}
        } catch (Throwable e) {
            logger.log(Level.INFO, "[CONVERSION:IMPORT] Couldn't Write Document Locale:" , e);
        }
    }

    private boolean isFileSizeAllowed() {
        //For non remote API content bytes can come from docs , need to check size
        if (docType != Constants.REMOTE_DOCUMENT) {
            long Allowed_File_Size = ImportExportUtil.allowedFileSizeNew();
            if (contentBytes.length > Allowed_File_Size) {
            	logger.log(Level.INFO, "[CONVERSION:XlsxImporter] file size greater then allowed size, please move this code to the action class:");
            	StackTraceElement[] elements = Thread.currentThread().getStackTrace();
            	DocumentUtils.printStackTrace(elements);
            	logger.log(Level.INFO,"[CONVERSION:IMPORT] File size not Supported Error:");
                return false;
            }
        }
        return true;
    }

    /**
     * Rewrites published ranges Creates version Generates thumbnail Update
     * stats Called only for import sheet document , not import remote
     *
     * @param chunkContainer Container with docid,rid etc
     * @param loginName
     * @param zfsngVersionId
     * @param container
     * @return ZFSNGConversionConstants SUCCESS or FAILURE
     * @throws Exception
     */
    public static void postImport(WorkbookContainer chunkContainer, WorkbookContainer container, String loginName, String zfsngVersionId, Object[] zxlsx_objects) throws Exception {
        //TODO check and move as common method to Importer class
        long beforeVersion = System.currentTimeMillis();
                List<JSONObjectWrapper> xList = RedisHelper.loadActionsList(chunkContainer.getResourceKey(), -1, chunkContainer.getExecutedActionId());
                EngineUtils1.versionDocument(container, "MANUAL", "imported-version", loginName, zfsngVersionId, xList, true, null, zxlsx_objects, null);//No I18N

        logger.log(Level.OFF, "[XLSX-IMPORTER] versionDocument took : {0}", new Object[]{System.currentTimeMillis() - beforeVersion});

            // Check to stop creating thumbnail if it is already created - thumbnail will create once in Constants.holdontime
//            if (thumbnail != null) {
//                if ("true".equals(EnginePropertyUtil.getSheetPropertyValue("EnableThumbNail")) && DocumentUtils.checkThumbnailHoldOnTime(container)) {
//
//                    if (container.getResourceKey() != null && !container.isRemoteMode() && !container.isGdriveDoc()) {
//                        try(ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(thumbnail.getBytes())) {
//                            ZohoFS.addOrUpdateExtractedImage(container.getDocsSpaceId(), container.getResourceId(), null, byteArrayInputStream, thumbnail.getFileExtension());
//                            container.setThumbnailCreatedTime(System.currentTimeMillis());
//                            logger.log(Level.OFF, "[XLSX-IMPORTER] using thumbnail found in docProps");
//                        } catch (Exception e) {
//                            logger.log(Level.OFF, "[XLSX-IMPORTER] exception while using thumbnail found in docProps", e);
//                        }
//                    }
//                }
//            }
//            else {
//                long afterVersionBeforeThumbnail = System.currentTimeMillis();
//                logger.log(Level.OFF, "[XLSX-IMPORTER] thumbnail creation.");
//                try {
//                    EngineUtils1.createThumbNail(container);
//                } finally {
//                    logger.log(Level.OFF, "[XLSX-IMPORTER] thumbnail creation took : {0}", new Object[]{System.currentTimeMillis() - afterVersionBeforeThumbnail});
//                }
//            }
    }
//    private static void addMacrosToContainer(WorkbookContainer wbContainer,Map<String, String> macrosArray){
//         MacroInterface macroInterface = wbContainer.getMacroInterface();
//          Workbook workBook = null;
//           try {
//               workBook = wbContainer.getWorkbook(null);
//            if (macroInterface == null)
//            {
//                Map requestProps = constructMacroInterfaceProps(wbContainer, null, 1, 1, 1, 1, workBook.getSheet(0).getAssociatedName());
//                macroInterface = new MacroInterfaceImpl(wbContainer, (HashMap) requestProps);
//                wbContainer.setMacroInterface(macroInterface);
//            }
//            logger.log(Level.INFO,"[XLSX-IMPORTER] adding macros - interface created");
//
//            //workBook = wbContainer.getWorkbook(null);
//            ModuleManager mgr = ((MacroInterfaceImpl) macroInterface).getModuleManager();
//            //JSONArray macrosArray = new JSONArray();
//            MacroActionUtil.updateChangedModules(macroInterface, macrosArray, workBook);
//// MacroActionUtil.updateChangedModules(macroInterface, constructMacroNameToContentMap(macrosArray), workBook);
//        } catch (Exception e) {
//            logger.log(Level.INFO,"[XLSX-IMPORTER] exception while adding macros ",e);
//            throw new RuntimeException(e);
//
//        }
//       }
//    private static Map<String, String> constructMacroNameToContentMap(JSONArray mParamsArray){
//        Map<String, String> macroNameToContentMap = new HashMap<>();
//        String mname ="Macros"; //No I18N
//        String mcontent ="Sub SetData()\n" +                                //No I18N
//                "    'MsgBox 243\n" +                                       //No I18N
//                "    Range(\"A1\").Value = Time()\n" +                      //No I18N
//                "    Range(\"A2\").Value = Range(\"A1\").Value * 2\n" +     //No I18N
//                "    Range(\"A3\").Value = Range(\"A2\").Value + Date\n" +  //No I18N
//                "    Range(\"A4\").Value = Range(\"A4\").Value + 1\n" +     //No I18N
//                "    Range(\"A5\").Value = \"Done\"\n" +                    //No I18N
//                "End Sub\n" +                                               //No I18N
//                "";
//        macroNameToContentMap.put(mname, mcontent);
//        return macroNameToContentMap;
//    }
    private static void addMacrosToworkbook(Workbook workbook, Map<String, String> macroNameToContentMap){
                //VBAProject
        if (macroNameToContentMap != null)
            {
                MacroLibrary macroLibrary =null;
                try{
                    macroLibrary =workbook.getDefaultMacroLibrary();
                    if(macroLibrary==null){
                         macroLibrary =new MacroLibrary("VBAProject");  //No I18N
                        workbook.addMacroLibrary(macroLibrary);
                    }
                }catch (Exception e){
                   logger.log(Level.INFO,"[XLSX-IMPORTER] exception in adding macros ",e);
                   throw new RuntimeException(e);
                }

                workbook.setIsMacrosChanged(true);
                for (Map.Entry<String, String> macroNameToContentEntry : macroNameToContentMap.entrySet())
                {
                    String mname = macroNameToContentEntry.getKey();
                    String mcontent = macroNameToContentEntry.getValue(); //should have already been decoded
                    MacroModule macroModule = new MacroModule(mname);
                    macroModule.setScriptCode( mcontent);
                    macroLibrary.addMacroModule(macroModule);
                    //mgr.insertModule(module);
                    //changedModuleNames.put(mname);
                }

            }
    }

    public static Map constructMacroInterfaceProps(WorkbookContainer container, String workbookIdentity, int startRow, int startCol, int endRow, int endCol, String activeSheetName) {
        HashMap requestProps = new HashMap();
        DataRange activeSelection = null;
        Sheet activeSheet = null;
        if (activeSheetName != null) {
            try {
                activeSheet = container.getWorkbook(workbookIdentity).getSheetByAssociatedName(activeSheetName);
                if (startRow != -1) {
                    activeSelection = new DataRange(activeSheet.getAssociatedName(), startRow, startCol, endRow, endCol);
                }
            } catch (Exception ex) {
                logger.log(Level.INFO, null, ex);
            }
        }

       // if (userProfile != null) {
            requestProps.put("loginName", container.getCreatorFullName());
            requestProps.put("zuid",container.getDocOwner());
            requestProps.put("userEmailId", container.getCreatorEmailId());
            requestProps.put("userFullName", container.getCreatorFullName());
       // }
        if (activeSheet != null) {
            requestProps.put("currentSheetName", activeSheet.getName());
        }
        //LOGGER.log(Level.INFO, "sheet.getAssociatedName():  {0}", sheet.getAssociatedName());
        if (activeSelection != null) {
            requestProps.put("currentSelection", activeSelection);
        }
        requestProps.put("docOwner", container.getDocOwner());
        requestProps.put("creatorEmailId", container.getCreatorEmailId());
        requestProps.put("creatorFullName", container.getCreatorFullName());
        requestProps.put("docId", container.getDocId());
        requestProps.put("docName", container.getDocName());
        //TODO : NEED TO HANDLE REMOTE/PUBLIC VIEW
        //UserProfile userProfile = wbContainer.getUserProfile(DocumentUtils.getZUID());
        return requestProps;
    }
    private WorkbookContainer getNewContainer() {
        WorkbookContainer container = null;
        ContainerEntity entity = WorkbookContainer.getEntity();
        entity.docOwner = this.docOwner;
        entity.resourceId = this.resourceId;
        entity.documentId = this.docId;
        entity.documentName = this.docName;

        try {
			if(this.docType != Constants.REMOTE_DOCUMENT) {
				if(this.resourceId != null){
					entity.docsSpaceId = ZohoFS.getOwnerZID(this.resourceId);
				}else {
				entity.docsSpaceId = String.valueOf(DocumentUtils.getZUID(this.docOwner));
				}
			}else {
				if(this.resourceId != null){
					entity.docsSpaceId = ZohoFS.getOwnerZID(this.resourceId);
				}
			}
        } catch (Exception e) {
			logger.log(Level.OFF, "", e);
		}
        if (this.docType == Constants.REMOTE_DOCUMENT) {
            entity.remoteBookId = remoteBookId;
        }
        container = new WorkbookContainer(entity);
        return container;
    }
    private Map<String, String>  asposeConversion() throws Throwable {
        Map<String, String> macroNameToContentMap = new HashMap<>();
        try{
    		if(!this.format.contains(EngineConstants.FILEEXTN_XLSX)) {
                logger.log(Level.INFO, "[XLSX-IMPORTER] Aspose Conversion {0}", this.format);
                if(this.format.equals(EngineConstants.FILEEXTN_ODS)){
                    logger.log(Level.INFO, "[XLSX-IMPORTER] Reading macros from ODS:", this.format);
                    //                    if(AsposeUtils.isODSfileHasMacros(contentBytes)){
                    macroNameToContentMap=AsposeUtils.readMacrosFromODSFile(contentBytes);
                    //                    }else{
                    //                        logger.log(Level.INFO, "[XLSX-IMPORTER] Reading macros from ODS [There is no macros] in ", this.format);
                    //                    }
                }else if(this.format.equals(EngineConstants.FILEEXTN_XLS) || this.format.equals(EngineConstants.FILEEXTN_XLSM)){
                    logger.log(Level.INFO, "[XLSX-IMPORTER] Reading macros from XLS and XLSM:", this.format);
                    macroNameToContentMap=AsposeUtils.getMacroModulesUsingAspose(contentBytes);
                }
                this.contentBytes = AsposeUtils.getSheetAsposeConversion(contentBytes, this.format, EngineConstants.XLSX_FILE_FORMAT);
                isAsposeConversion = true;
                this.format = EngineConstants.ZXLSX_FILE_FORMAT;
            }
        } catch(com.aspose.cells.CellsException ae) {
            logger.log(Level.OFF, "[XLSX-IMPORTER] Aspose Conversion Exception", ae);
            if(ae.getMessage().contains("password")) {
                throw new XlsxLimitException(XLSXException.CAUSETYPE.FILE_CORRUPTED.toString(), ZFSNGConstants.CONVERSION_FAILURE_DOCUMENT_CORRUPTED);
            }
        }
        catch (Exception e) {
            logger.log(Level.INFO, "[XLSX-IMPORTER] Aspose Conversion Exception", e);
        }

        return macroNameToContentMap;
    }

//    private static void writeMacroMapToSheetContainer(WorkbookContainer container, Map<String, String> macroNameToContentMap) {
//        logger.log(Level.INFO,"[XLSX-IMPORTER] Trying to add macro to zoho sheet");
//        try {
//            if(!macroNameToContentMap.isEmpty()) {
//                addMacrosToContainer(container, macroNameToContentMap);
//                logger.log(Level.INFO,"[XLSX-IMPORTER] writing macros to zoho sheet");
//            }
//        }catch (Exception e){
//            logger.log(Level.INFO,"[XLSX-IMPORTER]  exception in adding macros in parse");
//        }
//    }
    private static void writeMacroMapToSheetWorkbook(Workbook workbook, Map<String, String> macroNameToContentMap) {
        logger.log(Level.INFO,"[XLSX-IMPORTER] Trying to add macro to zoho sheet workbook");
        try {
            if(!macroNameToContentMap.isEmpty()) {
                addMacrosToworkbook(workbook, macroNameToContentMap);
                logger.log(Level.INFO,"[XLSX-IMPORTER] writing macros to zoho sheet workbook");
            }
        }catch (Exception e){
            logger.log(Level.INFO,"[XLSX-IMPORTER]  exception in adding macros in parse workbook");
        }
    }

    public static XLSXParserAgent parse(Workbook workbook, WorkbookContainer container, byte[] contentBytes, String zuId, Map<String, String> existingXmlImagePathToUrlMap, boolean addCharts) throws Throwable {
        long time = System.currentTimeMillis();
        workbook.setPatternSetting(PatternSetting.WILDCARD);
        workbook.setParsing(true);
            XLSXParserAgent xlsxpa = new XLSXParserAgent(workbook);
            try {
                xlsxpa.parseXlsxDocument(contentBytes);

                xlsxpa.makeDbEntries(container, existingXmlImagePathToUrlMap, addCharts);

                xlsxpa.addProtectionToRangesAndSheet(getProtection(zuId));

            /*Adding FreezePanes From WorkbookSetting to DB.
            WorkbookSettings workbookSettings = workbook.getWorkbookSettings();
            Sheet[] sheets = workbook.getSheets();
            for(int i = 0; i < sheets.length; i++) {
                String sheetName = sheets[i].getName();
                int freezePaneRow = workbookSettings.getFreezePaneRow(sheetName);
                int freezePaneColumn = workbookSettings.getFreezePaneColumn(sheetName);
                if(!(freezePaneRow == 0 && freezePaneColumn == 0)) {
                    DocumentUtils.setFreezedPane(container.getDocId(), container.getDocOwner(), sheetName, "JUNK", freezePaneRow, freezePaneColumn); //No I18N
                }
            }*/
            } catch(Exception e) {
                workbook.setParsing(false);
                throw e;
            } finally {
                LOGGER.log(Level.OFF, "FEATURES_NOT_PARSED :{0}, {1}", new Object[]{XLSXParserAgent.FEATURES_NOT_PARSED.get(), container.getResourceKey()});
                XLSXParserAgent.FEATURES_NOT_PARSED.remove();
            }


        CellStyle defaultCellStyle = workbook.getDefaultCellStyle();
        String fontName = defaultCellStyle.getPropertyAsString(TextStyle.Property.FONTNAME, workbook.getTheme());
        String fontSize = defaultCellStyle.getPropertyAsString(TextStyle.Property.FONTSIZE, workbook.getTheme());
        Object fontColor = defaultCellStyle.getProperty(TextStyle.Property.COLOR);

        SpreadsheetSettingsUtil.updateSpreadsheetSettings(container, workbook, fontName, fontSize, (ZSColor) fontColor, null, container.getWorkbookAdditionalInfo());

            workbook.setParsing(false);
            workbook.setIsPartialLoaded(false);
            workbook.setIsReadyToRender(true);

            if(! EngineConstants.DEFAULT_LOCALE.equals(workbook.getAccountsLocale())) {
                try {
                    PivotUtil.refreshAllPivot(workbook);
                } catch(Exception e) {
                    LOGGER.log(Level.INFO, "[Pivot] error while xlsxImport for Non-English Locale", e);
                }
            }
            logger.log(Level.INFO, "[XLSX-IMPORTER-TIME-STATS] parsing, saving images, chart and re-name sheets :{0}ms", new Object[]{(System.currentTimeMillis() - time)});
            return xlsxpa;
        }

    private String[] getSheetNames(WorkbookContainer container) {
        Workbook workbook = container.getWorkbookForSave();
        Sheet[] sheets = workbook.getSheets();
        String[] sheetNames = new String[sheets.length];
        int i = 0;
        for (Sheet sheet : sheets) {
            sheetNames[i++] = sheet.getName();
        }
        return sheetNames;
    }

    private static Protection getProtection(String zuId) {
        Set<String> unAuthUsers = new HashSet<>();
        unAuthUsers.add(zuId);
        return new Protection(Protection.createPermissionMap(null, unAuthUsers, null, null, true, null, null));

    }

    private boolean deleteDocument(WorkbookContainer newContainer) {
        //this function shouldn't fail-import, So catching Throwable
        try {
            if (newContainer != null && docId != null) {
                DocumentUtils.deleteDocumentEntry(newContainer, "fromImport", null);//No I18N
            }
        } catch (Throwable throwable) {
            logger.log(Level.OFF, "", throwable);
            return false;
        }
        return true;
    }

    private int importRemoteDocumentThroughLibo(FileReader fr, RemoteBook remoteBook,WorkbookContainer container) {
        //this function shouldn't throw any exception as it is just a precaution and it's ok if it fails, so catching throwable

        int liboImportStatus = ZFSNGConstants.CONVERSION_NOTHAPPENED;
        try {
            Importer importer = ImporterFactory.getOdsImporter();
            liboImportStatus = importer.importRemoteDocument(fr, remoteBook,true,container);
        } catch (Throwable throwable) {
            liboImportStatus = ZFSNGConstants.CONVERSION_FAILURE;
            logger.log(Level.OFF, "", throwable);
        }
        return liboImportStatus;
    }

    private int importDocumentThroughLibo() {
        int liboImportStatus = ZFSNGConstants.CONVERSION_NOTHAPPENED;
        try {
            Importer importer = ImporterFactory.getOdsImporter();
            if (!this.isVersionUpload) {
                this.docId = null;//this document is deleted from Db, So passing null
            }
            liboImportStatus = importer.importSheetDocument(zuId, resourceId, docId, docOwner, loginName, importedByZuid, zfsngVersionId, docName, format, docType, contentBytes, importAsTemplate, null);
        } catch (Throwable throwable) {
            liboImportStatus = ZFSNGConstants.CONVERSION_FAILURE;
            logger.log(Level.OFF, "", throwable);
        }
        return liboImportStatus;
    }
    public static Workbook getWorkbookWithoutChartsAndImages(byte[] xlsx_bytes) throws Exception {
        final Instant now = Instant.now();
        Workbook workbook = new Workbook();
        workbook.setParsing(true);
        try {
            XLSXParserAgent xlsxpa = new XLSXParserAgent(workbook);
            xlsxpa.parseXlsxDocument(xlsx_bytes);
        } finally {
            LOGGER.log(Level.OFF, "FEATURES_NOT_PARSED :{0}, time: {1}ms", new Object[]{XLSXParserAgent.FEATURES_NOT_PARSED.get(), Duration.between(now, Instant.now()).toMillis()});
            XLSXParserAgent.FEATURES_NOT_PARSED.remove();
        }
        workbook.setParsing(false);
        workbook.setIsPartialLoaded(false);
        workbook.setIsReadyToRender(true);
        return workbook;
    }

    public static void parseXlsxFromDocStore(WorkbookContainer container, Workbook workBook, long documentId) throws Exception {
        /**
         * workbook created from file in doc store
         * charts written to db
         * workbook written as nzsheet in doc store
         * searchFile written to doc store
         * nzsheet written as imported-version in version store
         * thumbnail written, if it does not exist in xlsx file, it is generated and then written
         */
        InputStream xlsxMetaFileFromDocStore = getXlsxMetaFileInDocumentStore(container);
        Store documentStore = container.getStore(documentId, ZSStore.FileName.DOCUMENT);
        try(InputStream stream= container.getReadStream(documentStore, documentId, null, ZSStore.FileExtn.ZXLSX, null)){
            try {
                Map<String, String> existingXmlImagePathToUrlMap = new ConcurrentHashMap<>();
                if(xlsxMetaFileFromDocStore != null) {
                    JSONObjectWrapper jsonObject = null;
                    try(ZipInputStream zipInputStream = new ZipInputStream(xlsxMetaFileFromDocStore)) {
                        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(zipInputStream));
                        String str;
                        while ((str = bufferedReader.readLine()) != null) {
                            jsonObject = new JSONObjectWrapper(str);
                        }
                    }
                    LOGGER.log(Level.OFF, "XlsxMetaFile :{0}", new Object[]{jsonObject == null ? null :jsonObject.toString()});
                    if(jsonObject != null) {
                        Iterator iterator = jsonObject.names().iterator();
                        while(iterator.hasNext()) {
                            String next = (String) iterator.next();
                            existingXmlImagePathToUrlMap.put(next, jsonObject.getString(next));
                        }
                    }
                }

                AbstractMap.SimpleEntry<byte[], Map<String, String>> zxlsx = read_zxlsx(stream);
                XLSXParserAgent xlsxParserAgent = XlsxImporter.parse(workBook, container, zxlsx.getKey(), container.getDocOwnerZUID(), existingXmlImagePathToUrlMap, xlsxMetaFileFromDocStore == null);
                //no need to save as, xlsx will be parsed from version.
                //if it is parsed here, it will be for revert action to previous xlsx version.

//                long saveStartTime = System.currentTimeMillis();
//                if (! container.saveAsOneFile(null, null, true, false)) {
//                    LOGGER.log(Level.INFO, "[XLSX-IMPORTER-TIME-STATS][SAVE-FAILED] :{0}ms ", new Object[]{(System.currentTimeMillis() - saveStartTime)});
//                    Exception import_save_failed = new Exception("Import Save failed");
//                    throw import_save_failed;
//                }
//                LOGGER.log(Level.INFO, "[XLSX-IMPORTER-TIME-STATS][SAVE] :{0}ms", new Object[]{(System.currentTimeMillis() - saveStartTime)});
//                XlsxImporter.postImport(container, container, xlsxParserAgent.getThumbnail(), container.getDocOwner(), null, null);

                //todo: avoid setting macro for version preview
                //writeMacroMapToSheetContainer(container, zxlsx.getValue());
                writeMacroMapToSheetWorkbook(workBook, zxlsx.getValue());
                if(xlsxMetaFileFromDocStore == null) {
                    JSONObjectWrapper jsonObject = new JSONObjectWrapper();
                    for(Map.Entry<String, String> kv : existingXmlImagePathToUrlMap.entrySet()) {
                        jsonObject.put(kv.getKey(), kv.getValue());
                    }
                    EngineUtils1.LOGGER.log(Level.OFF, "XlsxMetaFile :{0}", new Object[]{jsonObject.toString()});
                    byte[] xlsxMetaZipFileBytes = getXlsxMetaZipFileBytes(jsonObject, xlsxParserAgent.getImagesJSONArray());
                    XlsxImporter.writeXlsxMetaFileToDocumentStore(new ByteArrayInputStream(xlsxMetaZipFileBytes), container);


                    if(!container.isRemoteMode()) {
                        //write XlsxMetaFile to ver 1.0 and add charts to version db, if not written.
                        String zfsngVersionNo = "1.0";//No I18N
                        String[] versionDetails = DocumentUtils.getVersionDetailsforZFSNGVersion(container.getDocId(), container.getDocOwner(), ZohoFS.getVersionIDForEditor(container.getDocsSpaceId(), container.getResourceKey(), zfsngVersionNo));
                        if (versionDetails == null) {
                            LOGGER.log(Level.INFO, "Engine: Parsing workbook :::: RKey: {0}  ~~~  version trying to parse is not available .... zfsng_version : {1}", new Object[]{
                                    container.getResourceKey(), zfsngVersionNo
                            });
                        } else {
                            long versionResourceId = Long.valueOf(versionDetails[1]);
                            LOGGER.log(Level.INFO, "Engine: Parsing workbook writing xlsxMetaFileToVersionStore :::: RKey: {0}  ~~~  Using ZFSNGVersion: {1}  ~~~  versionResourceId: {2}", new Object[]{container.getResourceKey(), zfsngVersionNo, versionResourceId});
                            String fileFormat = container.getFileFormatForVersion(versionResourceId);
                            if(EngineConstants.ZXLSX_FILE_FORMAT.equals(fileFormat)) {
                                try(InputStream xlsxMetaFileFromVersionStream = getXlsxMetaFileFromVersion(versionResourceId, container)) {
                                    if(xlsxMetaFileFromVersionStream == null) {
                                        writeXlsxMetaFileToVersion(versionResourceId, new ByteArrayInputStream(xlsxMetaZipFileBytes), container);
                                        Persistence persistence = SheetPersistenceUtils.getPersistence(container.getDocOwner());
                                        EngineUtils1.copyChartsFromChartsToChartVersionTable(container, persistence, container.getDocId(), container.getDocOwner(), versionResourceId);
                                    }
                                }
                            }
                        }
                    }
                    //
                }
            } catch(Throwable e) {
                throw new RuntimeException(e);
            }
        }
    }

    private static byte[] getXlsxMetaZipFileBytes(JSONObjectWrapper jsonObject, JSONArrayWrapper image_meta_info) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try(ZipOutputStream zipOutputStream = new ZipOutputStream(os)) {
            zipOutputStream.putNextEntry(new ZipEntry("images.json"));//No I18N
            zipOutputStream.write(jsonObject.toString().getBytes());
            zipOutputStream.putNextEntry(new ZipEntry("images_meata_info.json"));//No I18N
            zipOutputStream.write(image_meta_info.toString().getBytes());
        }
        return os.toByteArray();
    }

    public static WorkbookSettings parseXlsxFromVersionStore(WorkbookContainer container, Workbook workBook, Store versionStore, long versionResourceId) throws Throwable {
        WorkbookSettings wbSettings =  null;
        try(InputStream stream = container.getReadStream(versionStore, versionResourceId, null, ZSStore.FileExtn.ZXLSX, null)) {
            Map<String, String> existingXmlImagePathToUrlMap = new ConcurrentHashMap<>();
            InputStream xlsxMetaFileFromVersionStream = getXlsxMetaFileFromVersion(versionResourceId, container);
            EngineUtils1.LOGGER.log(Level.OFF, "xlsxMetaFileFromVersionStream null {0}", new Object[]{xlsxMetaFileFromVersionStream == null});
            if(xlsxMetaFileFromVersionStream != null) {
                StringBuilder jsonObject = new StringBuilder();
                try(ZipInputStream zipInputStream = new ZipInputStream(xlsxMetaFileFromVersionStream)) {
                    for(ZipEntry zipEntry = zipInputStream.getNextEntry(); zipEntry != null; zipEntry = zipInputStream.getNextEntry()) {
                        if(zipEntry.getName().equals("images.json")) {
                            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(zipInputStream));
                            String str;
                            while ((str = bufferedReader.readLine()) != null) {
                                jsonObject.append(str);
                            }
                            break;
                        }
                    }
                }
//                EngineUtils1.LOGGER.log(Level.OFF, "XlsxMetaFile :{0}", new Object[]{jsonObject == null ? null :jsonObject.toString()});
                if(!jsonObject.toString().isEmpty()) {
                    JSONObjectWrapper jsonObject1 = new JSONObjectWrapper(jsonObject.toString());
                    Iterator iterator = new JSONObjectWrapper(jsonObject1).names().iterator();
                    while(iterator.hasNext()) {
                        String next = (String) iterator.next();
                        existingXmlImagePathToUrlMap.put(next, jsonObject1.getString(next));
                    }
                }
            }

            AbstractMap.SimpleEntry<byte[], Map<String, String>> zxlsx = read_zxlsx(stream);
            XLSXParserAgent xlsxParserAgent = parse(workBook, container, zxlsx.getKey(), container.getDocOwnerZUID(), existingXmlImagePathToUrlMap, xlsxMetaFileFromVersionStream == null);
            xlsxParserAgent.getThumbnail();//save thumbnail
            wbSettings = workBook.getWorkbookSettings();
            //todo: avoid setting macro for version preview
            //writeMacroMapToSheetContainer(container, zxlsx.getValue());
            writeMacroMapToSheetWorkbook(workBook, zxlsx.getValue());
            if(xlsxMetaFileFromVersionStream == null) {
                JSONObjectWrapper jsonObject = new JSONObjectWrapper();
                for(Map.Entry<String, String> kv : existingXmlImagePathToUrlMap.entrySet()) {
                    jsonObject.put(kv.getKey(), kv.getValue());
                }
                EngineUtils1.LOGGER.log(Level.OFF, "XlsxMetaFile :{0}", new Object[]{jsonObject.toString()});
                byte[] xlsxMetaZipFileBytes = XlsxImporter.getXlsxMetaZipFileBytes(jsonObject, xlsxParserAgent.getImagesJSONArray());
                writeXlsxMetaFileToVersion(versionResourceId, new ByteArrayInputStream(xlsxMetaZipFileBytes), container);
                writeXlsxMetaFileToDocumentStore(new ByteArrayInputStream(xlsxMetaZipFileBytes), container);
                Persistence persistence = SheetPersistenceUtils.getPersistence(container.getDocOwner());
                //charts are added to version only on first parse, they won't be added for revert-version.
                EngineUtils1.copyChartsFromChartsToChartVersionTable(container, persistence, container.getDocId(), container.getDocOwner(), versionResourceId);
            } else {
                xlsxMetaFileFromVersionStream.close();
            }

        }
        return wbSettings;
    }

    public static boolean deleteXlsxMetaFileFromDocumentStore(WorkbookContainer container) {
        try {
            Long docId = Long.valueOf(container.getDocId());
            Store store = container.getStore(docId, ZSStore.FileName.DOCUMENT);
            SheetFileInfo sheetfileInfo = store.getFileInfo(docId, ZSStore.FileName.XLSX_META.toString(), EngineConstants.FILEEXTN_ZIP);
            if (sheetfileInfo == null) {
                return false;
            }
            store.delete(sheetfileInfo.getResourceId(), ZSStore.FileName.XLSX_META.toString(), EngineConstants.FILEEXTN_ZIP);
            return true;
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "write of search_file to docstore failed : ".concat(container.getResourceId()), e);//No I18N
            return false;
        }
    }
    private static boolean writeXlsxMetaFileToDocumentStore(InputStream in, WorkbookContainer container) {
        if(in == null) {
            return true;
        }

        try {
            Long docId = Long.valueOf(container.getDocId());
            Store store = container.getStore(docId, ZSStore.FileName.DOCUMENT);
            SheetFileInfo sheetfileInfo = store.getFileInfo(docId, ZSStore.FileName.XLSX_META.toString(), EngineConstants.FILEEXTN_ZIP);
            if (sheetfileInfo == null) {
                LOGGER.log(Level.INFO, "creating file info for search_file, resourceid: {0}", new Object[]{container.getResourceId()});//No I18N
                sheetfileInfo = store.createFileInfo(docId, ZSStore.FileName.XLSX_META.toString(), EngineConstants.FILEEXTN_ZIP);
            }

            HashMap<String, Object> writeInfo = store.write(sheetfileInfo.getResourceId(), sheetfileInfo);
            try (OutputStream os = (OutputStream) writeInfo.get("OS")){
                IOUtils.copy(in, os);
            } finally {
                store.finishWrite(writeInfo);
            }
            return true;
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "write of search_file to docstore failed : ".concat(container.getResourceId()), e);//No I18N
            return false;
        }
    }

    private static InputStream getXlsxMetaFileInDocumentStore(WorkbookContainer container) {
        try {
            Long docId = Long.valueOf(container.getDocId());
            Store store = container.getStore(docId, ZSStore.FileName.DOCUMENT);
            SheetFileInfo sheetfileInfo = store.getFileInfo(docId, ZSStore.FileName.XLSX_META.toString(), EngineConstants.FILEEXTN_ZIP);
            if (sheetfileInfo == null) {
                LOGGER.log(Level.OFF, "search_file not present in document store {0}", container.getResourceId());//No I18N
                return null;
            } else {
                return store.read(sheetfileInfo.getResourceId(), ZSStore.FileName.XLSX_META.toString(), EngineConstants.FILEEXTN_ZIP);
            }
        } catch (Exception e) {
            LOGGER.log(Level.OFF, container.getResourceId(), e);
            return null;
        }
    }

    public static String getMetaInfoFromXlsxMetaFileInDocumentStore(WorkbookContainer container) throws IOException {
        InputStream xlsxMetaFileFromVersionStream = XlsxImporter.getXlsxMetaFileInDocumentStore(container);
        EngineUtils1.LOGGER.log(Level.OFF, "xlsxMetaFileFromVersionStream null {0}", new Object[]{xlsxMetaFileFromVersionStream == null});
        if(xlsxMetaFileFromVersionStream != null) {
            StringBuilder jsonArray = new StringBuilder();
            try(ZipInputStream zipInputStream = new ZipInputStream(xlsxMetaFileFromVersionStream)) {
                zipInputStream.getNextEntry();
                for(ZipEntry zipEntry = zipInputStream.getNextEntry(); zipEntry != null; zipEntry = zipInputStream.getNextEntry()) {
                    if(zipEntry.getName().equals("images_meata_info.json")) {
                        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(zipInputStream));
                        String str;
                        while((str = bufferedReader.readLine()) != null) {
                            jsonArray.append(str);
                        }
                        break;
                    }
                }
            }
            return jsonArray.toString();
        }
        return "";
    }

    public static String getMetaInfoFromXlsxMetaFileInVersion(WorkbookContainer container, long versionId) throws IOException {
        String metaInfo = "";
        InputStream xlsxMetaFileFromVersionStream = XlsxImporter.getXlsxMetaFileFromVersion(versionId, container);
        EngineUtils1.LOGGER.log(Level.OFF, "xlsxMetaFileFromVersionStream null {0}", new Object[]{xlsxMetaFileFromVersionStream == null});
        if(xlsxMetaFileFromVersionStream != null) {
            StringBuilder jsonArray = new StringBuilder();
            try(ZipInputStream zipInputStream = new ZipInputStream(xlsxMetaFileFromVersionStream)) {
                for(ZipEntry zipEntry = zipInputStream.getNextEntry(); zipEntry != null; zipEntry = zipInputStream.getNextEntry()) {
                    if(zipEntry.getName().equals("images_meata_info.json")) {
                        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(zipInputStream));
                        String str;
                        while((str = bufferedReader.readLine()) != null) {
                            jsonArray.append(str);
                        }
                        break;
                    }
                }
            }
            metaInfo = jsonArray.toString();
        }
        return metaInfo;
    }

    public static boolean copyXlsxMetaFileFromDocumentToVersionStore(long versionId, WorkbookContainer container) {
        try(InputStream inputStream = getXlsxMetaFileInDocumentStore(container)) {
            if(inputStream != null) {
                return writeXlsxMetaFileToVersion(versionId, inputStream, container);
            }
        } catch (Exception e) {
            LOGGER.log(Level.OFF, String.join("", container.getResourceId(), String.valueOf(versionId)), e);
        }
        return false;
    }

    public static boolean copyXlsxMetaFileFromVersionToDocumentStore(long versionId, WorkbookContainer container) {
        try {
            try(InputStream is = getXlsxMetaFileFromVersion(versionId, container)) {
                if(is != null) {
                    return writeXlsxMetaFileToDocumentStore(is, container);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.OFF, String.join("", container.getResourceId(), String.valueOf(versionId)), e);
        }
        return false;
    }

    private static InputStream getXlsxMetaFileFromVersion(long versionId, WorkbookContainer container) {
        try {
            Store store = container.getStore(versionId, ZSStore.FileName.VERSION);
            return container.getReadStream(store, versionId, ZSStore.FileName.V_XLSX_META, ZSStore.FileExtn.ZIP, null);
        } catch (Exception e) {
            LOGGER.log(Level.OFF, String.join("", container.getResourceId(), String.valueOf(versionId)), e);
        }
        return null;
    }

    private static boolean writeXlsxMetaFileToVersion(long versionId, InputStream inputStream1, WorkbookContainer container) {
        try(InputStream inputStream = inputStream1) {
            if(inputStream != null) {
                Map writeInfo = container.getWriteInfo(versionId, ZSStore.FileName.V_XLSX_META, ZSStore.FileExtn.ZIP, null);
                try(OutputStream os = (OutputStream) writeInfo.get("OS")) {
                    IOUtils.copy(inputStream, os);
                }
                container.finishWrite(writeInfo);
                return true;
            }
        } catch (Exception e) {
            LOGGER.log(Level.OFF, String.join("", container.getResourceId(), String.valueOf(versionId)), e);
        }
        return false;
    }

    public static void write_zxlsx(OutputStream outputStream,  byte[] xlsx_bytes, Map<String, String> macros_prop) throws IOException {
        try(ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream)) {
            zipOutputStream.putNextEntry(new ZipEntry(XLSX_FILE_ENTRY));
            zipOutputStream.write(xlsx_bytes);
            if(macros_prop != null && !macros_prop.isEmpty()) {
                zipOutputStream.putNextEntry(new ZipEntry(MACROS_ENTRY));
                Properties properties = new Properties();
                properties.putAll(macros_prop);
                properties.store(zipOutputStream, "");
            }
        }
    }

    private static AbstractMap.SimpleEntry<byte[], Map<String, String>> read_zxlsx(InputStream inputStream) throws IOException {
        byte[] xlsx_bytes = null;
        Map<String, String> macros_prop = new HashMap<>();
        try(ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            ZipEntry nextEntry = zipInputStream.getNextEntry();
            while(nextEntry != null) {
                if(nextEntry.getName().equals(MACROS_ENTRY)) {
                    Properties properties = new Properties();
                    properties.load(zipInputStream);
                    for(Map.Entry<Object, Object> kv : properties.entrySet()) {
                        macros_prop.put((String) kv.getKey(), (String) kv.getValue());
                    }
                } else if(nextEntry.getName().equals(XLSX_FILE_ENTRY)){
                    xlsx_bytes = IOUtils.toByteArray(zipInputStream);
                }
                nextEntry = zipInputStream.getNextEntry();
            }
        }
        return new AbstractMap.SimpleEntry<>(xlsx_bytes, macros_prop);
    }
}
