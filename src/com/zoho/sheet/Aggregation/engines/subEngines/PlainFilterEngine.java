package com.zoho.sheet.Aggregation.engines.subEngines;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ValueI;
import com.adventnet.zoho.websheet.model.ZArrayI;
import com.adventnet.zoho.websheet.model.query.model.DataAggregatorResult;
import com.adventnet.zoho.websheet.model.query.model.GroupByCriteria;
import com.adventnet.zoho.websheet.model.query.model.GroupValueFilter;
import com.adventnet.zoho.websheet.model.query.model.TabularData;
import com.zoho.sheet.Aggregation.AggregationEngine;
import com.zoho.sheet.Aggregation.engines.AbstractClusterEngine;
import com.zoho.sheet.Aggregation.model.ClusterColumn;
import com.zoho.sheet.Aggregation.model.FilterCriteria;
import com.zoho.sheet.Aggregation.model.GroupingColumn;
import com.zoho.sheet.Aggregation.model.ValueColumn;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public class PlainFilterEngine extends AbstractClusterEngine {
    // ex - top 5 pay by employee
    AggregationEngine engine;

    public PlainFilterEngine(AggregationEngine engine) {
        this.engine = engine;
    }

    @Override
    public void fireEngine(TabularData tabularData) {
        /* Deps from builder */
        FilterCriteria filterObj = engine.getFilterCriteria();

        ZArrayI filterColRange = filterObj.getFilterColRange();
        DataAggregatorResult startNode = tabularData.getRootNode();
        GroupByCriteria filterColCriteria = new GroupByCriteria(filterColRange, GroupByCriteria.GroupByOperation.DEFAULT);  //default grouping type

        GroupValueFilter.GroupFilterOperator filterOperator = filterObj.getFilterOperator().asValueFilter();
        int filterLimit = filterObj.getFilterValue();
        GroupValueFilter filterVal = new GroupValueFilter(filterOperator, new ValueI(Cell.Type.FLOAT, filterLimit));

        List<DataAggregatorResult> groupedNodes = tabularData.getGroupedNodes(startNode, filterColCriteria, filterVal);

        int processedRows = 0;
        for (int i = 0; i < groupedNodes.size() && processedRows < filterLimit; i++) {
            processedRows = createRowData(groupedNodes.get(i), processedRows);
        }
    }

    private int createRowData(DataAggregatorResult dataNode, int processedRows) {
        List<Integer> rowIndices = dataNode.getRows();

        for (int j = 0; j < rowIndices.size(); j++) {
            List<Value> otherRowData = createAdjacentValues(rowIndices.get(j));
            engine.getAggResult().add(otherRowData);
            processedRows++;
        }
        return processedRows;
    }

    private List<Value> createAdjacentValues(int rowIndex) {
        List<Value> rowData = new ArrayList<>();

        List<ValueColumn> valueColumns = engine.getValueColumns();
        GroupingColumn axisColumn = engine.getAxisColumn();
        GroupingColumn legendColumn = engine.getLegendColumn();

        if (valueColumns.isEmpty() && Objects.isNull(axisColumn) && Objects.isNull(legendColumn)) {
            iterateAllCols(rowData, rowIndex);
        } else {
            iterateSingleCol(axisColumn, rowData, rowIndex);    //add axis Col values
            iterateSingleCol(legendColumn, rowData, rowIndex);   //add legend col values

            //add Value col values
            for (ValueColumn valueCol : valueColumns) {
                iterateSingleCol(valueCol, rowData, rowIndex);
            }
        }
        return rowData;
    }

    private void iterateAllCols(List<Value> rowData, int rowIndex) {
        ZArrayI tableRange = engine.getTableRange();

        for (int col = 0; col < tableRange.getColSize(); col++) {
            rowData.add(tableRange.getValue(rowIndex, col));
        }
    }

    private void iterateSingleCol(ClusterColumn column, List<Value> rowData, int rowIndex) {
        if (Objects.nonNull(column)) {
            ZArrayI colRange = column.getColRange();
            for (int i = 0; i < colRange.getColSize(); i++) {
                rowData.add(colRange.getValue(rowIndex, i));
            }
        }
    }
}