package com.zoho.sheet.Aggregation.engines.subEngines;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ValueI;
import com.adventnet.zoho.websheet.model.ZArrayI;
import com.adventnet.zoho.websheet.model.query.model.*;
import com.zoho.sheet.Aggregation.AggEngineConstants;
import com.zoho.sheet.Aggregation.AggregationEngine;
import com.zoho.sheet.Aggregation.engines.AbstractClusterEngine;
import com.zoho.sheet.Aggregation.engines.AggEngineHelper;
import com.zoho.sheet.Aggregation.model.FilterCriteria;
import com.zoho.sheet.Aggregation.model.GroupingColumn;
import com.zoho.sheet.Aggregation.model.ValueColumn;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public class Filter1DEngine extends AbstractClusterEngine {
    //handles cases which has one value
    AggregationEngine engine;

    public Filter1DEngine(AggregationEngine engine) {
        this.engine = engine;
    }

    @Override
    public void fireEngine(TabularData tabularData) {
        /* Deps from builder */
        GroupingColumn axisColumn = engine.getAxisColumn();
        GroupingColumn legendColumn = engine.getLegendColumn();

        if (Objects.nonNull(axisColumn) && Objects.isNull(legendColumn)) {
            // one axis && no legend - pivot like table
            applyFiltration(tabularData, axisColumn);
        } else {
            applyFiltration(tabularData, legendColumn);
        }
    }

    private void applyFiltration(TabularData tabularData, GroupingColumn clusterColumn) {
        List<ValueColumn> valueColumns = engine.getValueColumns();
        FilterCriteria filterCriteria = engine.getFilterCriteria();

        ZArrayI filterColRange = filterCriteria.getFilterColRange();
        int filterLimit = filterCriteria.getFilterValue();

        AggEngineConstants.FilterCriteria filterOperator = filterCriteria.getFilterOperator();
        GroupValueFilter.GroupFilterOperator valueFilter = filterOperator.asValueFilter();

        GroupByCriteria.GroupByOperation byOperation = clusterColumn.getGrpCriteria().asGroupByOperation();
        GroupByCriteria groupByCriteria = new GroupByCriteria(clusterColumn.getColRange(), byOperation);

        Summary.SummarizeOperation summaryOperation = engine.getValueOperation().asSummaryOperation();
        Summary summary = new Summary(filterColRange, summaryOperation);
        GroupSummaryFilter summaryFilter = new GroupSummaryFilter(summary, valueFilter, new ValueI(Cell.Type.FLOAT, filterLimit));

        List<DataAggregatorResult> groupedNodes = tabularData.getGroupedNodes(tabularData.getRootNode(), groupByCriteria, summaryFilter);
        int processedRows = 0;
        for (int i = 0; i < groupedNodes.size() && processedRows < filterLimit; i++) {
            addRowData(valueColumns, groupedNodes.get(i));
        }
    }

    private void addRowData(List<ValueColumn> valueColumns, DataAggregatorResult node) {
        Summary.SummarizeOperation summaryOperation = engine.getValueOperation().asSummaryOperation();
        List<Value> applySummarization = AggEngineHelper.applySummarization(valueColumns, summaryOperation, node);

        List<Value> row = new ArrayList<>();
        row.add(node.getValue());
        row.addAll(applySummarization);
        engine.getAggResult().add(row);
    }
}