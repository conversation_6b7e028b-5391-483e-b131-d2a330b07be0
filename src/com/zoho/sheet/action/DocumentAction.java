/*$Id$*/
 /*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.zoho.sheet.action;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.DocumentActionManager;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DocumentAction extends StrutsRequestHandler {

    public static Logger logger = Logger.getLogger(DocumentAction.class.getName());

    public String execute() throws Exception {
//        logger.info("!!!!!!!!!!!!!DocumentAction!!!!!!!!!!!!!!!!!!!!");

        int action = (request.getParameter("action") != null) ? Integer.parseInt(request.getParameter("action")) : -1;
        String loginName = (IAMUtil.getCurrentUser() != null) ? IAMUtil.getCurrentUser().getLoginName() : null;

        try {
            JSONObjectWrapper actionObject = null;
            if (action == ActionConstants.OVERWRITE_API_PREPROCESS) {
                actionObject = ActionObject.getActionObject(request);
            }
            if(action == ActionConstants.SEND_WMS_NOTIFICATION){
                loginName = request.getParameter("action_by"); //No I18N
                logger.log(Level.INFO, ":: SendWMSCollabMessageAction :: internal action >>> SEND_WMS_NOTIFICATION");//NO I18N
            }
            WorkbookContainer container = CurrentRealm.getContainer();
//            logger.log(Level.INFO, "container", container.getDocId());
//            JSONObject responseJson = DocumentActionManager.doAction(container, request);
//            out.println(responseJson);
            String sResponse = DocumentActionManager.doAction(action, container, CurrentRealm.getContainerIdentity(), CurrentRealm.getAccessIdentity(), loginName, request, actionObject);
            HttpServletResponseWrapper.sendResponse(response, sResponse, HttpServletResponseWrapper.MimeType.TEXT);

        } catch (Exception e) {
            logger.log(Level.WARNING, null, e);
        }
        return null;
    }
}
