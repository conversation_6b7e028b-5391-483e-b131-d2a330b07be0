/* $Id$ */
package com.zoho.sheet.action;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.zoho.sheet.util.CellJsonConstructor;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.zfsng.constants.ZFSNGConstants;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Locale;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PrintAction extends StrutsRequestHandler {

    public static final Logger LOGGER = Logger.getLogger(PrintAction.class.getName());
    @Override
    public String execute() throws Exception {

        try {
            // HTML Construction
            UserProfile.AccessType accessType = CurrentRealm.getAccessIdentity();
            org.json.JSONObject previlages;
            int action = Integer.parseInt(request.getParameter("action"));

            boolean canDownload;
            if (request.getAttribute(ZFSNGConstants.CAPABILITIES) != null) {
                previlages = new org.json.JSONObject(((String) request.getAttribute(ZFSNGConstants.CAPABILITIES)));
                canDownload = previlages.getBoolean("canDownload");
            } else {
                canDownload = true;
            }

            if (canDownload) {
                WorkbookContainer wbContainer = CurrentRealm.getContainer();
                Workbook workbook = wbContainer.getWorkbook(null);
                WorkbookSettings wbSettings = workbook.getWorkbookSettings();
                PrintWriter out = response.getWriter();

                switch(action) {
                    case ActionConstants.PRINT_SETTING_GET:
                        JSONObjectWrapper wbPrintSettings = wbSettings.getPrintSetting();
                        if(wbPrintSettings != null)
                        {
                            HttpServletResponseWrapper.sendResponse(response, wbPrintSettings, HttpServletResponseWrapper.MimeType.JSON);
                        }
                        else
                        {
                            HttpServletResponseWrapper.sendResponse(response, "null", HttpServletResponseWrapper.MimeType.TEXT);    // NO I18N
                        }
                        break;
                    case ActionConstants.PRINT_SETTING_SET:
                        JSONObjectWrapper printSettings = request.getParameter("printOptions") != null ? new JSONObjectWrapper(request.getParameter("printOptions")) : new JSONObjectWrapper();    //NO I18N
                        wbSettings.setPrintSetting(printSettings);
                        break;
                    case ActionConstants.PDF_PRINT_PREVIEW:
                        
                        String isSPVersion = request.getParameter("isSPVersion");
                        JSONObjectWrapper respRangeObj = findExactRange(request, response, accessType, wbContainer);
                        JSONObjectWrapper respPrintSettingsObj = wbSettings.getPrintSetting();
                        WorkbookAdditionalInfo additionalInfo = wbContainer.getWorkbookAdditionalInfo();
                        Locale wbLocale = additionalInfo.getSpreadsheetSettings().getLocale();
                        String currentLocale = wbLocale.toString();
                        
                        if(isSPVersion != null && isSPVersion.equals("true")) {
                            JSONObjectWrapper respObj = new JSONObjectWrapper();
                            respObj.put("range", respRangeObj);     //NO I18N
                            respObj.put("settings", (respPrintSettingsObj != null) ? respPrintSettingsObj : null);      //NO I18N
                            respObj.put("locale", currentLocale);     //NO I18N

                            HttpServletResponseWrapper.sendResponse(response, respObj, HttpServletResponseWrapper.MimeType.JSON);

                        } else {

                            HttpServletResponseWrapper.sendResponse(response, respRangeObj, HttpServletResponseWrapper.MimeType.JSON);
                        }
                        
                        break;
                }            
            }
        } catch(ProhibitedActionException e) {
            LOGGER.log(Level.INFO, "[PRINTACTION][Exception] Exception while fetching data ", e);
            JSONObjectWrapper errObj = ErrorCode.getProhibitedDialogErrorMsg(e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            PrintWriter out = response.getWriter();
            out.print(errObj); //NO OUTPUTENCODING
        }
        return null;
    }
    
    
    private JSONObjectWrapper findExactRange(HttpServletRequest request, HttpServletResponse response, UserProfile.AccessType accessType, WorkbookContainer wbContainer) {
        JSONObjectWrapper ranges = new JSONObjectWrapper();
        try {
            String printType = request.getParameter("printType");         // pt = printType;
            JSONArrayWrapper sheets = JSONArrayWrapper.fromString(request.getParameter(JSONConstants.SHEETLIST));       // sn = sheet name;
            String rangeDtl = request.getParameter("range");              // ro = range options

            boolean addImage = Boolean.valueOf(request.getParameter("addImage"));         // ai = add image;
            boolean addChart = Boolean.valueOf(request.getParameter("addChart"));          // ac = add chart;
            boolean addButton = Boolean.valueOf(request.getParameter("addButton"));        // ab = add button;
            boolean addGridLine = Boolean.valueOf(request.getParameter("addGridLine"));      // ag = add gridlines;
            boolean addSlicer = Boolean.valueOf(request.getParameter("addSlicer"));

            WorkbookContainer container = CurrentRealm.getContainer();
            Workbook workbook = container.getWorkbook(null);
            
            for(int i = 0; i < sheets.length(); i++) {
                JSONObjectWrapper eRange = new JSONObjectWrapper();

                String sheetID = sheets.getString(i);
                Sheet sheet = workbook.getSheetByAssociatedName(sheetID);
                String sheetName = sheet.getName();
                String[] range = rangeDtl.split(":");

                int startRow = Integer.parseInt(range[0]);
                int startCol = Integer.parseInt(range[1]);
                int endRow = Integer.parseInt(range[2]);
                int endCol = Integer.parseInt(range[3]);

                // Setting HTML Properties
                HashMap htmlProp = new HashMap();
                htmlProp.put("addChart", addChart);
                htmlProp.put("addImage", addImage);
                htmlProp.put("addButton", addButton);
                htmlProp.put("addGridLines", addGridLine);
                htmlProp.put("addSlicer", addSlicer);

                // HTML Construction
                CellJsonConstructor cellJsonConstructor = new CellJsonConstructor(container, htmlProp);
                cellJsonConstructor.setSheetName(sheetName);

                HashMap<String, Integer> exactRange = cellJsonConstructor.getExactRange();

                int sr = exactRange.get("startRow");
                int sc = exactRange.get("startCol");
                int er = exactRange.get("endRow");
                int ec = exactRange.get("endCol");

                boolean isRangePrint = false; 

                if(!"RANGE".equals(printType)) {
                    startRow = sr;
                    endRow = er;
                    startCol = sc;
                    endCol = ec;
                } else {

                    isRangePrint = true;
                    if((startRow == 0 && endRow == (Utility.MAXNUMOFROWS - 1)) && endRow > er) {
                        endRow = er;
                    }

                    if((startCol == 0 && endCol == (Utility.MAXNUMOFCOLS - 1)) && endCol > ec) {
                        endCol = ec;
                    }
                }

                if(startRow > endRow || startCol > endCol) {

                    //Handle this case - Print empty page in this case
                    LOGGER.log(Level.INFO, "[PRINT_CONTROLLER]Canvas Print calling print range outside used row/col - Print empty page");   //NO I18N

                } else {
                    if(!isRangePrint) {

                        HashMap<String, Integer> rangeProp = getMergedEndRange(sheet, startRow, startCol, endRow, endCol);

                        startRow = rangeProp.get("startRow");
                        endRow = rangeProp.get("endRow");
                        startCol = rangeProp.get("startCol");
                        endCol = rangeProp.get("endCol");
                    }
                }

                eRange.put("startRow", startRow);
                eRange.put("startCol", startCol);
                eRange.put("endRow", endRow);
                eRange.put("endCol", endCol);
                
                ranges.put(sheetID, eRange);
            }

        } catch (Exception e) {
            LOGGER.log(Level.INFO, "[PRINT_CONTROLLER] Exception in finding exact print range... ", e);
        }
        
        return ranges;
        
    }
    
    private HashMap<String, Integer> getMergedEndRange(Sheet sheet, int sR, int sC, int eR, int eC) {

        HashMap<String, Integer> rangeProp = new HashMap();

        int newEndRow = eR;
        int newEndCol = eC;

        if (sheet != null) {

            for (int i = sR; i < eR; i++) {

                Cell parentCell = sheet.getMergeParentCell(i, eC);
                if (parentCell != null) {

                    int rowIdx = parentCell.getRowIndex();
                    int colIdx = parentCell.getColumnIndex();
                    int[] mergeDtl = sheet.getMergeCellSpans(parentCell);

                    if (mergeDtl[0] > 1 && ((mergeDtl[0] + rowIdx) - newEndRow) > 1) {
                        newEndRow = (mergeDtl[0] - 1) + rowIdx;
                    }

                    if (mergeDtl[1] > 1 && ((mergeDtl[1] + colIdx) - newEndCol) > 1) {
                        newEndCol = (mergeDtl[1] - 1) + colIdx;
                    }
                }

            }

            for (int j = sC; j <= eC; j++) {

                Cell parentCell = sheet.getMergeParentCell(eR, j);
                if (parentCell != null) {

                    int rowIdx = parentCell.getRowIndex();
                    int colIdx = parentCell.getColumnIndex();
                    int[] mergeDtl = sheet.getMergeCellSpans(parentCell);
                    if (mergeDtl[0] > 1 && ((mergeDtl[0] + rowIdx) - newEndRow) > 1) {
                        newEndRow = (mergeDtl[0] - 1) + rowIdx;
                    }

                    if (mergeDtl[1] > 1 && ((mergeDtl[1] + colIdx) - newEndCol) > 1) {
                        newEndCol = (mergeDtl[1] - 1) + colIdx;
                    }
                }
            }
        }

        rangeProp.put("startRow", sR);
        rangeProp.put("startCol", sC);
        rangeProp.put("endRow", newEndRow);
        rangeProp.put("endCol", newEndCol);

        return rangeProp;
    }

}
