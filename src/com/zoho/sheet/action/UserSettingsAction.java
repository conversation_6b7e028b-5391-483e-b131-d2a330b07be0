/* $Id$ */
package com.zoho.sheet.action;

import com.adventnet.wms.api.WmsApi;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.sheet.util.UserSettings;

import java.util.logging.Level;
import java.util.logging.Logger;

public class UserSettingsAction extends StrutsRequestHandler
{
    private static final Logger LOGGER = Logger.getLogger(UserSettingsAction.class.getName());

    public String execute() throws Exception
    {
        String userName = request.getUserPrincipal().getName();
        String action = (String) request.getParameter("action");            // get | set
        JSONObjectWrapper respObj = new JSONObjectWrapper();

        try {

            if ("SET".equalsIgnoreCase(action)) {
                String columnName = ((String) request.getParameter("columnName")).toUpperCase();    // Name of the column to be inserted
                String value = (String) request.getParameter("value");
                String sheet_tiding = (String) request.getParameter("sheet_tiding");
                
                switch (columnName) {

                    case "NEW_VERSION":

                        String addRedisKey = RedisHelper.HOEPN_ZUIDS_NO;
                        String removeRedisKey = RedisHelper.HOEPN_ZUIDS_YES;
                        String isNewClient = "false";
                        if ("TRUE".equalsIgnoreCase(value)) {
                            addRedisKey = RedisHelper.HOEPN_ZUIDS_YES;
                            removeRedisKey = RedisHelper.HOEPN_ZUIDS_NO;
                            isNewClient = "true";
                        }

                        UserSettings.set(userName, UserSettings.Settings.NEW_VERSION, null, isNewClient);

                        break;

                    case "VIEW":
                        
                        String key = ((String) request.getParameter("key")).toUpperCase();
                        switch (key) {

                            case "SIDEPANELTEXTDISPLAY":
                                UserSettings.set(userName, UserSettings.Settings.VIEW, "SidePanelTextDisplay", value);
                                break;
                            case "CHATBAR":
                                UserSettings.set(userName, UserSettings.Settings.VIEW, "chatBarView", value);
                                break;
                            case "TIDINGS":
                                UserSettings.set(userName, UserSettings.Settings.VIEW, "tidingsView", sheet_tiding);
                                break;
                            case "DARKMODE":
                                if(value!=null && ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value) || "sysDefault".equalsIgnoreCase(value))){
                                    UserSettings.set(userName, UserSettings.Settings.VIEW, "darkModeView", value);
                                    String userZUID=String.valueOf(DocumentUtils.getZUID(userName));
                                    JSONObjectWrapper msgBody = new JSONObjectWrapper();
                                    JSONObjectWrapper message = new JSONObjectWrapper();
                                    message.put("zuid", userZUID);
                                    message.put("drk", value);
                                    msgBody.put("DARKMODE", message);
                                    WmsApi.sendCustomMessage(String.valueOf(userZUID), msgBody.toString());
                                }
                                break;
                            case "OVERRIDE_BROWSER_SHORTCUT":
                                if(value != null && ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value))) {
                                    UserSettings.set(userName, UserSettings.Settings.VIEW, "overrideBrowserShortcut", value);
                                    String userZUID=String.valueOf(DocumentUtils.getZUID(userName));
                                    JSONObjectWrapper msgBody = new JSONObjectWrapper();
                                    JSONObjectWrapper message = new JSONObjectWrapper();
                                    message.put("zuid", userZUID);
                                    message.put("ovrbs", Boolean.parseBoolean(value));
                                    msgBody.put("OVERRIDE_BROWSER_SHORTCUT", message);
                                    WmsApi.sendCustomMessage(String.valueOf(userZUID), msgBody.toString());
                                }
                                break;
                        }
                        break;
                    case "GRID_SETTING":
                         String respKey = ((String) request.getParameter("key")).toUpperCase();
                        switch (respKey) {
                            case "AUTOFILL":
                                if(value != null && ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value))) {
                                    UserSettings.set(userName, UserSettings.Settings.GRID_SETTING, "autoCompleteView", value);
                                    String userZID = String.valueOf(DocumentUtils.getZUID(userName));
                                    JSONObjectWrapper msgContent = new JSONObjectWrapper();
                                    JSONObjectWrapper msge = new JSONObjectWrapper();
                                    msge.put("zuid", userZID);
                                    msge.put("AutoFill", Boolean.parseBoolean(value));
                                    msgContent.put("AutoComplete", msge);
                                    WmsApi.sendCustomMessage(String.valueOf(userZID), msgContent.toString());
                                }
                                break;
                            case "TEXTTONUM":
                                if(value != null && ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value))) {
                                    UserSettings.set(userName, UserSettings.Settings.GRID_SETTING, "textToNumView", value);
                                    String userZID = String.valueOf(DocumentUtils.getZUID(userName));
                                    JSONObjectWrapper msgContent = new JSONObjectWrapper();
                                    JSONObjectWrapper msge = new JSONObjectWrapper();
                                    msge.put("zuid", userZID);
                                    msge.put("TextToNum", Boolean.parseBoolean(value));
                                    msgContent.put("TextToNumber", msge);
                                    WmsApi.sendCustomMessage(String.valueOf(userZID), msgContent.toString());
                                }
                                break;
                        }
                        break;
                }
                
                respObj.put("STATUS", "SUCCESS");

            } else if ("GET".equalsIgnoreCase(action)) {
                JSONArrayWrapper columnNames = JSONArrayWrapper.fromString(request.getParameter("columnNames"));

                if(columnNames != null){
                    for (int i=0; i < columnNames.length(); i++) {
                        String columnName = columnNames.getString(i);
                        switch (columnName) {

                            case "NEW_VERSION":
                                respObj.put("NEW_VERSION",UserSettings.get(userName, UserSettings.Settings.NEW_VERSION));
                                break;

                            case "VIEW":
                                respObj.put("VIEW",UserSettings.get(userName, UserSettings.Settings.VIEW));
                                break;
                            case "GRID_SETTING":
                                respObj.put("GRID_SETTING",UserSettings.get(userName, UserSettings.Settings.GRID_SETTING));
                                break;
                        }
                    }
                }
            }


        } catch (Exception e) {

            LOGGER.log(Level.WARNING, "Problem in userSettings ", e);
            respObj.put("STATUS", "FAILURE");

        }

        HttpServletResponseWrapper.sendResponse(response, respObj, HttpServletResponseWrapper.MimeType.JSON);
        return null;
    }
}
