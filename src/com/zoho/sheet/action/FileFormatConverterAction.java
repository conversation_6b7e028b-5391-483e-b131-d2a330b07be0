//$Id$
package com.zoho.sheet.action;

import com.zoho.sheet.conversion.FileConverter;
import com.zoho.sheet.util.StrutsRequestHandler;

import java.io.OutputStream;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FileFormatConverterAction extends StrutsRequestHandler {
	
	 private static final Logger LOGGER = Logger.getLogger(FileFormatConverterAction.class.getName());
	 @Override
     
     public String execute() throws Exception {
    	try{ 
    		byte[] content =null;
    		FileConverter converter = new FileConverter();
//    		content =  converter.getworbookByte(request);

    		content = converter.convertDocument(request);

    		if(content != null){
	    		String mimeType = converter.getMimeType();
	 			response.reset();    //IE with https (secured) connection export issue fix block starts
	 			response.setContentType(mimeType);
	 			response.setCharacterEncoding("UTF-8"); // No I18N
	
	 			String fileName = converter.getFileName()+"."+converter.getFormat();//ClientUtils.encodeFileName(((isExportName && activeSheetName != null) ? docName + '_' + activeSheetName : docName) + "." + format,userAgent);
	 			response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\""); // No I18N
				
	 			response.setCharacterEncoding("UTF-8");	// No I18N
	    		 OutputStream out = response.getOutputStream();
	 			 out.write(content); //NO OUTPUTENCODING    
	 			 out.flush();
    		}

	    		
		}catch(Exception e){
    		LOGGER.log(Level.WARNING,"PROBLEM in file conveter",e);
    	}
    	 
    	 return null;			
     }
    

}
