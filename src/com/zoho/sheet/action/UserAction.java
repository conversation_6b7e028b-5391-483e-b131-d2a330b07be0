 /* $Id$ */
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.zoho.sheet.action;

 import com.adventnet.iam.IAMUtil;
 import com.adventnet.iam.User;
 import com.adventnet.zoho.websheet.model.*;
 import com.adventnet.zoho.websheet.model.TabInfo.TabType;
 import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
 import com.adventnet.zoho.websheet.model.field.MergeConfiguration;
 import com.adventnet.zoho.websheet.model.field.util.MergeConfigurationUtils;
 import com.adventnet.zoho.websheet.model.field.util.MergeJobUtils;
 import com.adventnet.zoho.websheet.model.field.util.MergeSourceUtils;
 import com.adventnet.zoho.websheet.model.field.util.MergeTemplateUtils;
 import com.adventnet.zoho.websheet.model.response.ActionResponseObject;
 import com.adventnet.zoho.websheet.model.response.ResponseObject;
 import com.adventnet.zoho.websheet.model.response.VersionBasedResponseAnalyzer;
 import com.adventnet.zoho.websheet.model.response.UserInfo;
 import com.adventnet.zoho.websheet.model.style.zstheme.ZSThemesLibrary;
 import com.adventnet.zoho.websheet.model.util.*;
 import com.zoho.sheet.action.externaldata.actionmanager.ExternalDataActionManager;
 import com.zoho.sheet.action.externaldata.actionmanager.FlowActionManager;
 import com.zoho.sheet.action.externaldata.metastorage.DBActionManager;
 import com.zoho.sheet.admin.ZSAdminPanelUtils;
 import com.zoho.sheet.authorization.permissions.ActionPermissionCheck;
 import com.zoho.sheet.deluge.DelugeUtils;
 import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
 import com.zoho.sheet.exception.APIErrorHandler;
 import com.zoho.sheet.mail.SendMailHelper;
 import com.zoho.sheet.navigator.constants.NavUserActionConstant;
 import com.zoho.sheet.util.*;
 import com.zoho.zfsng.client.ResourceInfo;
 import com.zoho.zfsng.client.ZohoFS;
 import com.zoho.zfsng.constants.ResourceType;

 import javax.servlet.http.HttpServletResponse;
 import java.net.URLEncoder;
 import java.util.*;
 import java.util.logging.Level;
 import java.util.logging.Logger;

 import static com.adventnet.zoho.websheet.model.ErrorCode.*;
 import static com.adventnet.zoho.websheet.model.ErrorCode.DisplayType.BANNER;
 import static com.adventnet.zoho.websheet.model.ErrorCode.DisplayType.DIALOG;

 /**
 *
 * <AUTHOR>
 */
public class UserAction extends StrutsRequestHandler {

    /**************
     *
     * This actions will not modify the workbook object
     * Response to be sent to the particular user only and not to any collaborators
     * actions are: AUTO_SUM, ALT_EQUAL_RANGE, COPY_TO_CLIP, PICK_LIST
     * No view port is required
     * No UNDO is required
     * Response will not be generated via ResponseObject
     * Response to be constructed in this action class itself
     * No WMS API will be used for sending the response, i.e., response will be written to the output stream directly
     * 
     *
     */
    public static final Logger LOGGER = Logger.getLogger(UserAction.class.getName());

    @Override
    public String execute() throws Exception {
        long startTime = System.currentTimeMillis();
        //String actionMode = "";
//        String currentSheet = "";
//        int action = Integer.parseInt(request.getParameter("action"));

        JSONObjectWrapper jObj = null;                         // we will pass this json object to doAction
        JSONObjectWrapper responseJson = new JSONObjectWrapper();
        Workbook workbook = null;
        WorkbookContainer wbContainer = null;
        HashMap responseMap = null;                     // the response from doAction
        List<Cell> cells = null;
        ResponseObject respObj = null;
        Cell vCell = null;
        UserProfile uProfile = null;

        //actionMode = request.getParameter("actionMode");
        try
        {
            boolean isWebClient = request.getParameter("isWebClient") != null ? Boolean.valueOf(request.getParameter("isWebClient")) : false;

	wbContainer = CurrentRealm.getContainer();

        jObj = ActionObject.getActionObject(request);
        try
        {
            ActionPermissionCheck permissionCheck = ActionPermissionCheck.getInstance(wbContainer, jObj);
            if(permissionCheck != null)
            {
                permissionCheck.checkPermission();
            }
        }
        catch(ProhibitedActionException e)
        {
            throw e;
        }
        catch(Exception e)
        {
            LOGGER.log(Level.SEVERE, "[AUTHORIZATION_FILTER][Exception] Authorization check has been bypassed.", e); //No I18N
        }

        int action = jObj.getInt(JSONConstants.ACTION);
        long externalLinkId = jObj.optLong(JSONConstants.EXTERNAL_SHARE_LINK_ID);
        //version history will be viewed in parse error document case
        if(action != ActionConstants.JSP_VERSION_VIEW_DIALOG)
        {
            workbook = wbContainer.getWorkbook(CurrentRealm.getWorkBookIdentity());
        }

        UserProfile userProfile = wbContainer.getUserProfile(jObj.getString("zuid"));

        Sheet sheet =  jObj.has(JSONConstants.CURRENT_ACTIVE_SHEET)? workbook.getSheetByAssociatedName(jObj.getString(JSONConstants.CURRENT_ACTIVE_SHEET)) : jObj.has(JSONConstants.SHEETLIST) ? workbook.getSheetByAssociatedName(ActionJsonUtil.getFirstSheetFromJsonArray(jObj.optJSONArray(JSONConstants.SHEETLIST))):null;
        HashSet<Cell> 	dependentCells;
        List<Range> rangeDependents;
	switch(action) {
	case ActionConstants.AUTO_SUM:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    JSONObjectWrapper rangeStatus = (JSONObjectWrapper)responseMap.get("range");
                    if(rangeStatus != null)
                    {
                        responseJson = rangeStatus;
                    }
                    break;
                case ActionConstants.ALT_EQUAL_RANGE:
	responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    Range cellRange = (Range)responseMap.get("range");
                    if(cellRange!=null)
                    {
                        responseJson.put("sr", cellRange.getStartRowIndex());
                        responseJson.put("sc", cellRange.getStartColIndex());
                        responseJson.put("er", cellRange.getEndRowIndex());
                        responseJson.put("ec", cellRange.getEndColIndex());
                    }
                    break;
                case ActionConstants.PICK_LIST:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("result", (JSONArrayWrapper)responseMap.get("range"));
                    break;
                case ActionConstants.GET_DATA_FROM_TABLE:
                	responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                	responseJson.put("result", responseMap.get("dataArray"));
                	break;
                case ActionConstants.FIND:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    ReadOnlyCell rCell = (ReadOnlyCell)responseMap.get("range");
                    if(rCell != null)
                    {
                        responseJson.put("row", rCell.getRowIndex());
                        responseJson.put("col", rCell.getColIndex());
                        responseJson.put("sheet", rCell.getCell().getRow().getSheet().getName());
                    }else
                    {
                        responseJson.put("row", -1);
                        responseJson.put("col", -1);
                    }
                    if(responseMap.containsKey(JSONConstants.NO_OF_MATCH)) {
                        responseJson.put(JSONConstants.NO_OF_MATCH, responseMap.get(JSONConstants.NO_OF_MATCH));
                    }
                    if(responseMap.containsKey(JSONConstants.MATCH_AT)) {
                        responseJson.put(JSONConstants.MATCH_AT, responseMap.get(JSONConstants.MATCH_AT));
                    }
                    break;
                case ActionConstants.GET_NEWLY_ADDED_USER:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    JSONObjectWrapper newUserObj = (JSONObjectWrapper) responseMap.get("newUserDetails");
                    responseJson.put("usr", newUserObj);
                    break;
                case ActionConstants.TEXTTOCOLUMNS_PREVIEW:
                     responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                     responseJson.put("result1", (JSONArrayWrapper)responseMap.get("range1"));
                     responseJson.put("result2",(String)responseMap.get("range2"));
                     // System.out.println("UserAction...." + responseMap.get("range3"));
                     responseJson.put("result3", responseMap.get("range3"));
                break;

                case ActionConstants.GOAL_SEEK:
                    if(!workbook.isDependenciesUpdated())
                    {
                        workbook.updateCellDependencies();
                    }
	Sheet vCellSheet = workbook.getSheet(jObj.getString("vcsn"));
	vCell = vCellSheet.getCell(jObj.getString("vc"));
                    
                    String value = vCell.getFormula();
                    cells = new ArrayList<Cell>();
                    cells.add(vCell);
                    
                    try
                    {
                        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                        responseJson = (JSONObjectWrapper)responseMap.get("responseJson");
                    } 
                    catch (Exception gce)
                    {
                        LOGGER.log(Level.WARNING, "ActionManager throws error on Goal Seek :: message: {0}", gce.getMessage());
                        responseJson.put("message", LocaleMsg.getMsg("GoalSeek.Msg.Error"));
                        response.setContentType("application/json");// No I18N
                        HttpServletResponseWrapper.sendResponse(response,responseJson, HttpServletResponseWrapper.MimeType.JSON);
                        return null;
                    }
                    
                    if(responseJson.has("message"))
                    {
                        String[] args = {jObj.getString("tc")};
                        String message = responseJson.getString("message");
                        responseJson.set("message", LocaleMsg.getMsg(message));
                        if(("GoalSeek.Msg.Success".equals(message))||("GoalSeek.Msg.NotSuccess".equals(message)))
                        {
                            responseJson.set("message", I18nMessage.replaceParams(responseJson.getString("message"),args));
                        }
                        if(responseJson.has("solution"))
                        {
                            jObj.put("sr", vCell.getRowIndex());
                            jObj.put("sc", vCell.getColumnIndex());
                            jObj.put("er", vCell.getRowIndex());
                            jObj.put("ec", vCell.getColumnIndex());
                            ActionUtil.ResultObject resultObject =	ActionUtil.getDependentCells(workbook, cells, null, false);
                            dependentCells = resultObject.getResultCells();
                            rangeDependents = resultObject.getResultRanges();
                            respObj = new ActionResponseObject(wbContainer, workbook, jObj, cells, rangeDependents, null, dependentCells, null, null, false);
                            //responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, uProfile.getViewPort(jObj.getString("rsid"),TabType.NON_CACHED)));
                            //String tabTypeStr = request.getParameter("tabType");
                            //TabType tabType = ClientUtils.getTabType(tabTypeStr);
                            uProfile = CurrentRealm.getUserProfile();
                            
                            TabType tabType = CurrentRealm.getTabType();
                            String tabKey = CurrentRealm.getTabIdentity();
                            String rsid = CurrentRealm.getWmsIdentity();
                            TabInfo tabInfo = uProfile.getTabInfo(tabKey, rsid, tabType);
                            UserInfo userInfo = new UserInfo(userProfile.getZUserId(), null, userProfile.getPermissionType(), tabKey, tabInfo.getResponseVersion());

//                            if (tabType != TabType.CACHED) {// old client
//                                    //responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer,
//                                    //uProfile.getViewPort(jObj.getString("rsid"), TabType.NON_CACHED)));
//                                    responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, tabInfo.getViewPort()));
//                            } else
//                            {
                            Object newResp = VersionBasedResponseAnalyzer.getResponse(respObj, null, userInfo); // documentResponse is enough
                            responseJson.put("clientUpdatJson", newResp);
//                            }
                        }
                        ActionUtil.setCellValue(vCell, value);
                        ReEvaluate.getReEvaluateDependents(null, cells, true);
                    }
                    else
                    {
                        responseJson.put("message", LocaleMsg.getMsg("GoalSeek.Msg.Error"));
                        LOGGER.log(Level.WARNING, "message is not returned by goal seek function");
                    }
                    break;
                case ActionConstants.GOAL_SEEK_CANCEL:
                    vCell = workbook.getSheet(jObj.getString("vcsn")).getCell(jObj.getString("vc"));
                    cells = new ArrayList<Cell>();
                    cells.add(vCell);
                    jObj.put("sr", vCell.getRowIndex());
                    jObj.put("sc", vCell.getColumnIndex());
                    jObj.put("er", vCell.getRowIndex());
                    jObj.put("ec", vCell.getColumnIndex());
                    ActionUtil.ResultObject resultObject =	ActionUtil.getDependentCells(workbook, cells, null, false);
                    dependentCells = resultObject.getResultCells();
                    rangeDependents = resultObject.getResultRanges();
                    respObj = new ActionResponseObject(wbContainer, workbook, jObj, cells, rangeDependents, null,  dependentCells, null, null, false);
                    uProfile = wbContainer.getUserProfile(jObj.getString("zuid"));
                    //responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, uProfile.getViewPort(jObj.getString("rsid"),TabType.NON_CACHED)));
//                    responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, uProfile.getTabInfo(CurrentRealm.getTabIdentity(), CurrentRealm.getWmsIdentity(), TabType.NON_CACHED).getViewPort()));
                    break;
                case ActionConstants.NAMEDRANGE_READ:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    Range range = (Range)responseMap.get("range");
                    if(range != null)
                    {
                        responseJson.put("sr", range.getStartRowIndex());
                        responseJson.put("sc", range.getStartColIndex());
                        responseJson.put("er", range.getEndRowIndex());
                        responseJson.put("ec", range.getEndColIndex());
                        responseJson.put("sn",  range.getSheet().getName());
                    }
                    break;
                case    ActionConstants.NAMEDRANGE_DETAILS_READ:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("namedRanges", responseMap.get("namedRanges"));//No I18N
                    break;
                case ActionConstants.CTRL_NAVIGATION:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("row", responseMap.get("row"));
                    responseJson.put("col", responseMap.get("col"));
                    break;
                case ActionConstants.LIST_SHEET_NAMES:
                    String[] sheets = workbook.getSheetNames(true);
                    JSONArrayWrapper responseArray = JSONArrayWrapper.fromArray(sheets);
                    responseJson.put("sl", responseArray);
                    break;
                case ActionConstants.SORT_DETAILS:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("response_array", responseMap.get("responseArray"));
                    break;
                case ActionConstants.CONDITIONAL_FORMAT_READ_RANGE:
                case ActionConstants.CONDITIONAL_FORMAT_READ_RANGE_FOR_MOBILE:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    if(responseMap.containsKey(("dtype"))){
                    	responseJson.put("DATATYPE", responseMap.get("dtype"));
                    }
                   responseJson.put("CONDITIONALFORMAT", responseMap.get("responsecf"));
                   break;
                case ActionConstants.WEBDATA_LIST:                	
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("WEBDATA_LIST", responseMap.get("webDataList").toString());
                   break;
                case ActionConstants.PROCESS_SPELL_CHECK:
                    responseMap = UserActionManager.doAction(wbContainer, workbook,jObj);
                    responseJson.put("SPL_CHK_RESP",responseMap.get("12001"));
                    break;
                case ActionConstants.DO_ATD_ACTION:
                    responseMap = UserActionManager.doAction(wbContainer, workbook,jObj);
                    responseJson.put("ATD_RESPONSE",responseMap.get("ATDResponse"));
                    break;
                case ActionConstants.CONDITIONAL_FORMAT_READ_DATATYPE:                	
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    if(responseMap.containsKey(("dtype"))){
                    	responseJson.put("DATATYPE", responseMap.get("dtype"));
                    }
                   responseJson.put("CONDITIONALFORMAT", responseMap.get("responsecf"));
                   break;
                case ActionConstants.CONDITIONAL_FORMAT_READ_SHEET:
                case ActionConstants.CONDITIONAL_FORMAT_READ_WORKBOOK:
                case ActionConstants.CONDITIONAL_FORMAT_READ_SHEET_FOR_MOBILE:
                case ActionConstants.CONDITIONAL_FORMAT_READ_WORKBOOK_FOR_MOBILE:
//                case ActionConstants.CONDITIONAL_FORMAT_READ_EDITOBJ:                	                	
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("CONDITIONALFORMAT", responseMap.get("responsecf"));                    
                	break;
                case ActionConstants.HIDDEN_ROW_DATA:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    //responseJson.put("row", jObj.getInt("sr"));
                    responseJson.put("data", (JSONObjectWrapper)responseMap.get("responseJson"));
                    break;
                case ActionConstants.READ_PIVOT_FIELDS:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("data", (JSONArrayWrapper)responseMap.get("responseJson"));
                    break;
                 case ActionConstants.READ_POSSIBLE_PIVOTS:
                    responseMap = UserActionManager.doAction(wbContainer, workbook,jObj);
                    responseJson.put("pivots",responseMap.get("pivots"));
                    responseJson.put("status",responseMap.get("status"));
                    break;
                case ActionConstants.GET_PIVOT_FORMULA:
                    responseMap = UserActionManager.doAction(wbContainer, workbook,jObj);
                    responseJson.put("data",responseMap.get("pivotFormula"));
                break;
                case ActionConstants.UPDATE_SLICER_SCROLL:
                case ActionConstants.UPDATE_TIMELINE_SCROLL:
                    UserActionManager.doAction(wbContainer, workbook,jObj);
                    break;
                case ActionConstants.READ_PIVOT_FIELD_MIN_MAX:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("data", responseMap.get("responseJson"));
                    break;
                case ActionConstants.READ_RECOMMENDED_PIVOT:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("data", (JSONObjectWrapper)responseMap.get("responseJson"));
                    break;
                case ActionConstants.READ_PIVOT_FILTER_DATA:
                case ActionConstants.DETECT_PIVOT_RANGE:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    //responseJson.put("data", (JSONObjectWrapper)responseMap.get("responseJson"));
                    responseJson = (JSONObjectWrapper)responseMap.get("responseJson");
                    break;
                case ActionConstants.READ_RANGE_DETAILS:
//                    Sheet sheet = workbook.getSheet(currentSheet);
                    //Sheet sheet = workbook.getSheetByAssociatedName(jObj.getString(JSONConstants.ASSOCIATED_SHEET_NAME));
                    //int startCol = jObj.getInt("sc");
                    //int endCol = jObj.getInt("ec");
                    JSONObjectWrapper singleRange = ActionJsonUtil.getFirstRangeFromJsonArray(jObj.optJSONArray(JSONConstants.RANGELIST));
                    int startCol = singleRange.getInt(JSONConstants.START_COLUMN);
                    int endCol = singleRange.getInt(JSONConstants.END_COLUMN);
                    if(endCol <= sheet.getUsedColumnIndex() || startCol <= sheet.getUsedColumnIndex())
                    {
                        jObj.put("is_vc", true);
                        jObj.put("is_sc", true);
                    }
                    else if(endCol < sheet.getColNum() || startCol < sheet.getColNum())
                    {
                        jObj.put("is_sc", true);
                    }
                    ActionUtil.ResultObject resultObject1 =	ActionUtil.getDependentCells(workbook, null, null, false);
                    dependentCells = resultObject1.getResultCells();
                    rangeDependents = resultObject1.getResultRanges();
                    respObj = new ActionResponseObject(wbContainer, workbook, jObj, null, rangeDependents, null, dependentCells, null, null, false);
                    uProfile = wbContainer.getUserProfile(jObj.getString("zuid"));
                    // update the viewPort
                    ViewPort viewPort = null;
                    if (uProfile != null) {
                        //viewPort = uProfile.getViewPort(jObj.getString("rsid"),TabType.NON_CACHED);
                        viewPort = uProfile.getTabInfo(CurrentRealm.getTabIdentity(), CurrentRealm.getWmsIdentity(), TabType.CACHED).getViewPort();
                        viewPort.updateViewport(sheet.getAssociatedName(),endCol);
                    }
//                    responseJson.put("response", respObj.getJSON(wbContainer, viewPort));
                    break;
                case ActionConstants.GET_TEMPLATES:
                    JSONArrayWrapper jsonAry = DocumentUtils.getTemplates(request, CurrentRealm.getUserProfile().getZUserId());
                    String totalCountNeeded = request.getParameter("totalCountNeeded");
        	    	boolean isTotalCountNeeded = Boolean.parseBoolean(totalCountNeeded != null ? totalCountNeeded : "false");
                    if (jsonAry != null && !jsonAry.isEmpty()) {
                        responseJson.put("templateInfo", jsonAry);
                        if(isTotalCountNeeded) {
                        	int templatesCount = DocumentUtils.getTemplatesCount(request, CurrentRealm.getUserProfile().getZUserId());
                        	responseJson.put("totalCount", templatesCount);	
                        }
                    }
                	break;
                case ActionConstants.SEND_MAIL:
                		JSONObjectWrapper mailResponse = (new SendMailHelper(request)).getMailResponse();
                    responseJson.put("status", mailResponse.toString());
                		break;
//                case ActionConstants.SEND_SPREADSHEET_IN_MAIL:
//                    org.json.JSONObjectWrapper previlages = new org.json.JSONObjectWrapper(((String) request.getAttribute(ZFSNGConstants.CAPABILITIES)));
//                    boolean _canEdit = true;
//                    boolean _canDownload = previlages.getBoolean("canDownload"); 
//                    if (previlages.has("canEdit")) {
//                    	_canEdit = previlages.getBoolean("canEdit");//NO I18N
//                    }
//                    boolean isDataHidden = RangeUtil.isHiddenDataLocked(workbook, !_canEdit, jObj.getString("zuid"));
//                    if(_canDownload && ! isDataHidden) {
//                        JSONObjectWrapper mailResp = (new MailDocument(request)).getMailResponse();
//                        responseJson.put("status", mailResp.toString());
//                    }else {
//                        JSONObjectWrapper errorObject = new JSONObjectWrapper();
//                        errorObject.put("ErrorMSGKey", "SendAsAttachmentNotAllowed");
//                        responseJson.put("ERROR", errorObject.toString());
//                    }
//                        break;
                case ActionConstants.IMAGE_CHECKDELETE:
                    String imgId = jObj.getString("img_id");
                    boolean isExists = ImageUtils.isImageExistsInAnyDocument(Long.valueOf(imgId), jObj.getString(JSONConstants.USER_NAME));
                    responseJson.put("is_exists", isExists);
                    break;
                case ActionConstants.FORMATCELLS_CLIENT:
                	 try
                     {
                         responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                         responseJson = (JSONObjectWrapper)responseMap.get("responseJson");
                     } 
                     catch (Exception gce)
                     {
                         LOGGER.log(Level.WARNING, "user ActionManager throws error on format cells  :: message: {0}", gce.getMessage());
                       //  responseJson.put("message", localeMsg.getMsg("GoalSeek.Msg.Error"));
                        // out.println(responseJson); //NO OUTPUTENCODING
                        // return null;
                     }
                	
                	break;
        case ActionConstants.NUMBER_FORMAT_LIST:

                responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = (JSONObjectWrapper)responseMap.get("responseJson");
                break;

        case ActionConstants.NUMBER_FORMAT_PREVIEW:
                responseMap = UserActionManager.doAction(wbContainer, workbook,jObj);
                responseJson = (JSONObjectWrapper) responseMap.get("responseJson");
                break;

        case ActionConstants.CUSTOM_FORMAT_MANAGE:
                responseMap = UserActionManager.doAction(wbContainer, workbook,jObj);
                responseJson = (JSONObjectWrapper) responseMap.get("responseJson");
            break;
		case ActionConstants.GET_HTML_WEBDATA:
                	String loc = jObj.getString("loc");                	
                	JSONObjectWrapper dataObj = ExternalDataActionManager.getTableDataPreView(loc, jObj.optString(JSONConstants.URLTYPE));
                    if(dataObj.has("totalRow"))
                    {
                        responseJson.put("totalRow", dataObj.get("totalRow"));
                    }
                    if(dataObj.has("noDataFound"))
                    {
                        responseJson.put("noDataFound", true);
                    }
                	responseJson.put("webdataXML", dataObj.get("tableData"));
			        break;

		case ActionConstants.GET_CONNECTED_LIST:
			responseJson.put("respMessage", ExternalDataActionManager.getConnectedList(jObj, wbContainer));
            break;
        case ActionConstants.GET_SERVICE_CONNECTED_DOC_LIST:
            responseJson.put("respMessage", ExternalDataActionManager.getServiceConnectedDocList(jObj, wbContainer.getDocOwner()));
            break;
		case ActionConstants.GET_MODULES_FIELDS_LIST:
			responseJson.put("respMessage", ExternalDataActionManager.getModuleFieldList(jObj.getInt(JSONConstants.SERVICE_CONSTANT), jObj.optString(JSONConstants.CONNECTION_UNIQUE_KEY, null), jObj.optString(JSONConstants.DB_NAME, null), jObj.optString(JSONConstants.DB_OWNER_NAME, null), jObj.optString(JSONConstants.MODULE_NAME, null), jObj.getString(JSONConstants.USER_NAME), jObj.getString(JSONConstants.ZUID), wbContainer.getDocOwner()));
			break;
        case ActionConstants.GET_SERVICE_ORG_LIST:
			responseJson.put("respMessage", ExternalDataActionManager.getOrgList(jObj.getString(JSONConstants.USER_NAME), jObj.getString(JSONConstants.ZUID), jObj.getInt(JSONConstants.SERVICE_CONSTANT), jObj.optString(JSONConstants.CONNECTION_UNIQUE_KEY), wbContainer.getDocOwner()));
			break;
		case ActionConstants.AUTHENTICATE_SERVICE:
			//jObj.put("docOwner", wbContainer.getDocOwner());
			responseJson.put("respMessage", ExternalDataActionManager.authenticateService( jObj.getString(JSONConstants.USER_NAME), jObj.getString(JSONConstants.ZUID), jObj.getLong(JSONConstants.FLOW_ORGID), jObj.getInt(JSONConstants.SERVICE_CONSTANT), wbContainer.getDocOwner(), wbContainer.getResourceId(), jObj.optString(JSONConstants.RSID, null)));

			break;

		case ActionConstants.UPDATE_CONNECTED_SERVICE:

            responseJson.put("respMessage",  FlowActionManager.updateConnectedService(jObj.getInt(JSONConstants.SERVICE_CONSTANT), jObj.getString(JSONConstants.USER_NAME), jObj.getString(JSONConstants.ZUID), jObj.getString(JSONConstants.CONNECTION_ID), jObj.getLong(JSONConstants.FLOW_ORGID), wbContainer.getDocOwner()));
			//DBActionManager.updateConnectedService(jObj.getString(JSONConstants.USER_NAME), jObj.getString(JSONConstants.CONNECTION_ID), jObj.getString(JSONConstants.SERVICE_NAME),true);
			break;
        case ActionConstants.GET_CONNECTION_STATUS:
            responseJson.put("respMessage", FlowActionManager.getConnectionStatus(jObj));

            break;
		case ActionConstants.UPDATE_CONNECTION:
            jObj.put("docOwner", wbContainer.getDocOwner());
            responseJson.put("respMessage", FlowActionManager.updateService(jObj));

			break;
        case ActionConstants.CANCEL_DATA_LINK:
            jObj.put("docOwner", wbContainer.getDocOwner());
            DBActionManager.deleteWebData(wbContainer.getDocOwner(), wbContainer.getDocOwnerZUID(), jObj.getLong(JSONConstants.WEBDATA_ID), wbContainer.getDocId());
            responseJson.put("respMessage", "success");

            break;

        case ActionConstants.CLOUD_DATA_OBSTRUCTS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("respMessage", responseMap.get(JSONConstants.RANGES));
            break;

        case ActionConstants.GET_SCHEDULER_DETAILS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("SCHEDULER_DETAILS", responseMap.get("schedulerInfoList"));
            break;

		/*case ActionConstants.CALL_SERVICE_API:
			jObj.put("docOwner", wbContainer.getDocOwner());
			String docOwnerMailId = DocumentUtils.getZuserEmailId(wbContainer.getDocOwner());
			jObj.put("docOwnerMailId", docOwnerMailId);
			LOGGER.info("docOwnerMailId: "+docOwnerMailId);
			
			responseJson.put("respMessage", ImportCloudDBUtil.callServiceAPI(jObj));
			//DBUtil.updateConnectedService(jObj);
			//responseJson.put("respMessage",;
			break;*/
        case ActionConstants.FETCH_DOCUMENTSETTINGS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("documentSettings", responseMap.get("responseJson"));
            break;
        case ActionConstants.FETCH_DOCUMENT_SETTINGS_NEW:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            JSONObjectWrapper documentSettings = (JSONObjectWrapper) responseMap.get("responseJson");
            responseJson.put(JSONConstants.SPREADSHEET_SETTINGS_NEW, documentSettings);
            break;
        case ActionConstants.FETCH_CUSTOM_COLORS:
            try
            {
                responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = new JSONObjectWrapper(responseMap);
            }
            catch(Exception e)
            {
                LOGGER.log(Level.SEVERE, "[CUSTOM_COLORS][Exception] Exception while retrieving custom color for " + wbContainer.getResourceKey(), e);
                responseJson = ErrorCode.getErrorMessage(ERROR_CUSTOM_COLORS_FETCH, null, MsgType.WARNING, BANNER);
            }
            break;
        case ActionConstants.UPDATE_CUSTOM_COLORS:

            try
            {
                responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = new JSONObjectWrapper(responseMap);
            }
            catch(Exception e)
            {
                LOGGER.log(Level.SEVERE, "[CUSTOM_COLORS][Exception] Exception while updating custom color for " + wbContainer.getResourceKey(), e);
                responseJson = ErrorCode.getErrorMessage(ERROR_CUSTOM_COLORS_SAVE, null, MsgType.WARNING, BANNER);
            }

            break;
                case ActionConstants.SOLVER:
                    singleRange = ActionJsonUtil.getFirstRangeFromJsonArray(jObj.optJSONArray(JSONConstants.RANGELIST));
                    int startRow = singleRange.getInt(JSONConstants.START_ROW);
                    startCol = singleRange.getInt(JSONConstants.START_COLUMN);
                    int endRow = singleRange.getInt(JSONConstants.END_ROW);
                    endCol = singleRange.getInt(JSONConstants.END_COLUMN);
                    String message;
                    if (!(workbook.isDependenciesUpdated()))
                    {
                      workbook.updateCellDependencies();
                    }
                    JSONObjectWrapper inJSON = new JSONObjectWrapper();
                    Sheet vCellsSheet = workbook.getSheet(jObj.getString("vcsns"));
                    Range variablesRange = new Range(vCellsSheet,startRow, startCol, endRow, endCol);
                    String previousValuesString = ActionUtil.getValueFromVariableRange(variablesRange);
                    try {
                      responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                      inJSON = (JSONObjectWrapper)responseMap.get("solveResponseJson");
                    } catch (Exception e) {
                      LOGGER.log(Level.WARNING, null, e.getMessage());
                      responseJson.put("message", LocaleMsg.getMsg("Solver.Error"));
                      response.setContentType("application/json");// No I18N
                        HttpServletResponseWrapper.sendResponse(response,responseJson, HttpServletResponseWrapper.MimeType.JSON);
                      return null; 
                    }
                    uProfile = CurrentRealm.getUserProfile();
                    TabType tabType = CurrentRealm.getTabType();
                    String tabId = CurrentRealm.getTabIdentity();
                    TabInfo tabInfo = uProfile.getTabInfo(tabId, CurrentRealm.getWmsIdentity(), tabType);

                    if (inJSON.has("solution")) {
                        message = inJSON.getString("solution");
                        String sendNewValue = ActionUtil.getValueFromVariableRange(variablesRange);

                        responseJson.set("solution", LocaleMsg.getMsg(message));
                        responseJson.put("oldValues", URLEncoder.encode(previousValuesString));
                        responseJson.put("variableCellValues", URLEncoder.encode(sendNewValue));
                        responseJson.put("resultValue", inJSON.getString("resultValue"));
                        ActionUtil.ResultObject resultObject2 =	ActionUtil.getDependentCells(workbook, null, Arrays.asList(variablesRange), false);
                        dependentCells = resultObject2.getResultCells();
                        rangeDependents = resultObject2.getResultRanges();
                        rangeDependents.add(variablesRange);
                        respObj = new ActionResponseObject(wbContainer, workbook, jObj, null, rangeDependents, null, dependentCells, null, null, false);
                        //String		tabTypeStr		=		request.getParameter("tabType");
                        //TabType	tabType			=		ClientUtils.getTabType(tabTypeStr);

                        //uProfile = CurrentRealm.getUserProfile();
//                        if(tabType!=TabType.CACHED){//old client
//                            //responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, uProfile.getViewPort(jObj.getString("rsid"),TabType.NON_CACHED)));
//                            responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, tabInfo.getViewPort()));
//                        }
//                        else
//                        {
                        UserInfo userInfo = new UserInfo(userProfile.getZUserId(), null, userProfile.getPermissionType(), tabId, tabInfo.getResponseVersion());
                        Object newResp= VersionBasedResponseAnalyzer.getResponse(respObj, null, userInfo); // documentResponse is enough
                        responseJson.put("clientUpdatJson",newResp);
//                        }
                     
                    }
                    else if ((inJSON.has("exception")) || (inJSON.has("exceptionformula")) || (inJSON.has("noNumber")) || (inJSON.has("exceptionformulaConstraint"))) {
                      if (inJSON.has("exception")) {
                        message = inJSON.getString("exception");
                        responseJson.put("exception", LocaleMsg.getMsg(message));
                        ActionUtil.setValueToVariableRange(variablesRange, previousValuesString);
                        ActionUtil.ResultObject resultObject3 =	ActionUtil.getDependentCells(workbook, null, Arrays.asList(variablesRange), false);
                        dependentCells = resultObject3.getResultCells();
                        rangeDependents = resultObject3.getResultRanges();
                        rangeDependents.add(variablesRange);
                        respObj = new ActionResponseObject(wbContainer, workbook, jObj, null, rangeDependents, null, dependentCells, null, null, false);
                        uProfile = CurrentRealm.getUserProfile();
                        //responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, uProfile.getViewPort(jObj.getString("rsid"),TabType.NON_CACHED)));
//                        responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, tabInfo.getViewPort()));
                      }
                      else if (inJSON.has("noNumber")) {
                        message = inJSON.getString("noNumber");
                        responseJson.put("noNumber", LocaleMsg.getMsg(message));
                      } else if (inJSON.has("exceptionformula")) {
                        message = inJSON.getString("exceptionformula");
                        responseJson.put("exceptionformula", LocaleMsg.getMsg(message));
                      } else {
                        String[] args = { inJSON.getString("label") };
                        message = inJSON.getString("exceptionformulaConstraint");
                        inJSON.set("exceptionformulaConstraint", LocaleMsg.getMsg(message));
                        responseJson.put("exceptionformulaConstraint", I18nMessage.replaceParams(inJSON.getString("exceptionformulaConstraint"), args)); }
                    }
                    ActionUtil.setValueToVariableRange(variablesRange, previousValuesString);
                    ReEvaluate.getReEvaluateDependents(Arrays.asList(variablesRange), Collections.<Cell>emptyList(), true);
                    break;
                case ActionConstants.SOLVER_CANCEL:
                    singleRange = ActionJsonUtil.getFirstRangeFromJsonArray(jObj.optJSONArray(JSONConstants.RANGELIST));
                    startRow = singleRange.getInt(JSONConstants.START_ROW);
                    startCol = singleRange.getInt(JSONConstants.START_COLUMN);
                    endRow = singleRange.getInt(JSONConstants.END_ROW);
                    endCol = singleRange.getInt(JSONConstants.END_COLUMN);
                    Sheet varCellsSheet = workbook.getSheet(jObj.getString("vcsns"));
                    Range variablesRanges = new Range(varCellsSheet,startRow, startCol, endRow, endCol);
                    ActionUtil.ResultObject resultObj =	ActionUtil.getDependentCells(workbook, null, Arrays.asList(variablesRanges), false);
                    dependentCells = resultObj.getResultCells();
                    rangeDependents = resultObj.getResultRanges();
                    rangeDependents.add(variablesRanges);
                    respObj = new ActionResponseObject(wbContainer, workbook, jObj, null, rangeDependents, null, dependentCells, null, null, false);
                    uProfile = wbContainer.getUserProfile(jObj.getString("zuid"));
                    //responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, uProfile.getViewPort(jObj.getString("rsid"),TabType.NON_CACHED)));
//                    responseJson.put("clientUpdateJson", respObj.getJSON(wbContainer, uProfile.getTabInfo(CurrentRealm.getTabIdentity(), CurrentRealm.getWmsIdentity(), CurrentRealm.getTabType()).getViewPort()));
                    break;

                case ActionConstants.GET_USER_CONTACT:
                    String searchString = request.getParameter("searchString");
                    int startIndex = Integer.parseInt(request.getParameter("startIndex"));
                    String  iamApiMode = request.getParameter("iamApiMode");
                    jObj.put("searchString", searchString);
                    jObj.put("startIndex",startIndex);
                    jObj.put("iamApiMode", iamApiMode);
                      responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                      responseJson.put("USER_CONTACT", responseMap.get("USER_CONTACT"));
//                      responseJson.put("USER_CONTACT", responseMap.get("ORG_MEMBERS"));
                    break;
                /*case ActionConstants.GET_ORG_MEMBERS:
                    LOGGER.info("GET_ORG_MEMBERS");
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    LOGGER.info("GET_ORG_MEMBERS::"+responseMap);
                    responseJson.put("ORG_MEMBERS", responseMap.get("ORG_MEMBERS"));
                    break;*/
                        
                case ActionConstants.PIVOT_REFRESH:
                	jObj.put("down", wbContainer.getDocOwner());
                	  responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                	  responseJson.put("pvtr", responseMap.get("pvtr"));
                	break;
                case ActionConstants.PIVOT_EDIT:
                	jObj.put("down", wbContainer.getDocOwner());
                	responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                	 responseJson.put("pvtr", responseMap.get("pvtr"));
                	break;
                case ActionConstants.PIVOT_GETRANGE:
                	jObj.put("down", wbContainer.getDocOwner());
                	responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                	 responseJson.put("pvtr", responseMap.get("pvtr"));
                	break;
                case ActionConstants.PIVOT_UPDATEPROPERTIES:
                	jObj.put("down", wbContainer.getDocOwner());
                	//jObj.put("docid", wbContainer.getDocId());
                	responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                	 responseJson.put("pvtr", responseMap.get("pvtr"));
                	break;
            case ActionConstants.CHANGE_DOC_STATUS:
                // change the new doc status (201) to normal spreadsheet document and make a save call
                wbContainer.changeIsNewDocStatus(CurrentRealm.getUserProfile().getUserName());
                break;
	case ActionConstants.GET_FORMULA_SUGGEST_INFO: 
		responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
		responseJson.put(JSONConstants.FORMULA_SUGGEST_INFO, responseMap.get(JSONConstants.FORMULA_SUGGEST_INFO)); 
		break;
        case ActionConstants.DATA_CLEANING_INFO:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(JSONConstants.DATA_CLEANING_RESPONSE,responseMap.get(JSONConstants.DATA_CLEANING_RESPONSE));
            break;
        case ActionConstants.Z_TRANSLATION:
        case ActionConstants.Z_TRANSLATION_GET_DATA:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(JSONConstants.Z_TRANSLATION_RESP, responseMap.get(JSONConstants.Z_TRANSLATION_RESP));
            break;
        case ActionConstants.ZIA_2:
            responseMap = UserActionManager.doAction(wbContainer, workbook,jObj);
            responseJson.put(JSONConstants.ZIA_2_RESP,responseMap.get(JSONConstants.ZIA_2_RESP));
            break;
        case ActionConstants.COLUMN_STATS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(JSONConstants.COLUMN_STATS_RESP,responseMap.get(JSONConstants.COLUMN_STATS_RESP));
            break;
        case ActionConstants.ZIA_RESET:
        case ActionConstants.ZIA_SAVE:
        case ActionConstants.ZIA_REFRESH:
        case ActionConstants.ZIA_OPEN:
                responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = (JSONObjectWrapper) responseMap.get(JSONConstants.ZIA_META);
                break;
        case ActionConstants.ZIA_RECONNECT:
                responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = (JSONObjectWrapper) responseMap.get(JSONConstants.ZIA_META);
                break;
        case ActionConstants.ZIA_FILTER_HEADER:
        case ActionConstants.ZIA_PRIORITY_HEADER:
                responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = (JSONObjectWrapper) responseMap.get(JSONConstants.ZIA_META);
                break;
        case ActionConstants.ZIA_RECOMMEND_QUERIES:
                responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = (JSONObjectWrapper) responseMap.get(JSONConstants.ZIA_QUERY);
                break;
        case ActionConstants.ZIA_CLOSE:
                UserActionManager.doAction(wbContainer, workbook, jObj);
                break;
        case ActionConstants.ZIA_SYNC:
                responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = (JSONObjectWrapper) responseMap.get(JSONConstants.ZIA_SYNC);
                break;
        case ActionConstants.DETECT_TABLE:
                responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = (JSONObjectWrapper) responseMap.get(JSONConstants.DETECT_TABLE_DETAILS);
                break;
        case ActionConstants.CHART_META_LIST:{
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(ChartActionConstants.JSONConstants.CHART_META, responseMap.get(ChartActionConstants.JSONConstants.CHART_META));
            break;
        }
        case ActionConstants.FETCH_NAV_OBJECTS_DETAILS: {
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(NavUserActionConstant.NAV_OBJ_DETAILS, responseMap.get(NavUserActionConstant.NAV_OBJ_DETAILS));
            break;
        }
        case ActionConstants.COPY_CHART_STYLES:
        case ActionConstants.COPY_CHART:
        case ActionConstants.RECOMMEND_CHARTS: {
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(ChartActionConstants.JSONConstants.RESP, responseMap.get(ChartActionConstants.JSONConstants.RESP));
            break;
        }
        case ActionConstants.CLEAR_CHART_RECOMMENDATION_CACHE: {
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            break;
        }
        case ActionConstants.CHART_SAVE:
		responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
		break;
	case ActionConstants.CHART_UPDATE:
	case ActionConstants.CHART_REFRESH:	
		responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
		
		JSONObjectWrapper chartJson	=	new JSONObjectWrapper();
		chartJson.put("a", action);
		chartJson.put("chartdetails", responseMap.get("chartDetails"));
		responseJson.put(String.valueOf(ActionConstants.CHART), chartJson);
		break;
	case ActionConstants.CHART_DETAILS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("response_array", responseMap.get("chartDetails"));
            break;
        case ActionConstants.ADD_CHART_TO_IMAGE_LIBRARY:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("status", responseMap.get("status"));
            break;
        case ActionConstants.SERVERCLIP_COPY_CHART:
            UserActionManager.doAction(wbContainer, workbook, jObj);
            return null;
	case ActionConstants.OPEN_EXPLORE:
		responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
        responseJson.put("recList", responseMap.get("recList"));
		break;
	case ActionConstants.NL_QUERY:
		responseMap  = UserActionManager.doAction(wbContainer, workbook, jObj);
                responseJson = (JSONObjectWrapper)responseMap.get(JSONConstants.ZIA_QUERY);
		break;
        case ActionConstants.GET_CHART_RECOMMENDATION:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("response_array", responseMap.get("chartDetails"));
            break;
	case ActionConstants.PIVOT_CHART_DETAILS:
        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
        responseJson.put("response_array", responseMap.get("chartDetails"));
        break;
	case ActionConstants.CHART_PREVIEW:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put("chartOptions", responseMap.get("co"));
			break;	
	case ActionConstants.FETCH_DECLARATIVE_TABLE:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put("decTable", responseMap.get("decTable"));
		break;
	case ActionConstants.CLEAR_CHART_DETAILS:
		responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
		responseJson.put("cleared", responseMap.get("clearedStatus"));
	break;
        case ActionConstants.CHART_CELL_REF:
                        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                        responseJson.put("cellRef", responseMap.get("crv"));
                        break;
        case ActionConstants.VIRTUAL_CHART:
                        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                        responseJson.put("VIRTUAL_CHART", true);
                        break;
        case ActionConstants.PIVOT_CHART_BUTTON_DETAILS:
                        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                        responseJson.put("pivotBottonRef", responseMap.get("pbd"));
                        break;
        case ActionConstants.CHART_COPY_STYLE:
                        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                        responseJson.put("copiedStyle", responseMap.get("ccs"));
                        break;
        
//        case ActionConstants.CHART_QUICK_STYLE:
//                        responseMap = UserActionManager.doAction(workbook, jObj);
//                        responseJson.put("chartStyle", responseMap.get("cso"));
//                        break;
	case ActionConstants.READ_PROTECTED_SHEETS:
        UserProfile.AccessType accessType = CurrentRealm.getAccessIdentity();
        if(!(accessType == UserProfile.AccessType.RANGE_PUBLIC_EXTERNAL || accessType == UserProfile.AccessType.RANGE_PUBLIC_ORG ||
                accessType == UserProfile.AccessType.SHEET_PUBLIC_EXTERNAL || accessType == UserProfile.AccessType.SHEET_PUBLIC_ORG)) {
                LOGGER.log(Level.INFO, "MANAGE_LOCKED_SHEETS: {0}", wbContainer.getResourceKey());
                responseArray = CellProtectionUtil.getProtectedSheetsInfoJson(wbContainer);
                responseJson.put(JSONConstants.PROTECTED_SHEETS, responseArray);
            } else {
                LOGGER.log(Level.SEVERE, "HACKING ATTEMPT >>>> {0}", CurrentRealm.getUserProfile().getZUserId()); // No I18n
                throw new Exception();
            }
            break;
        case ActionConstants.READ_PROTECTED_RANGES:
            accessType = CurrentRealm.getAccessIdentity();
            if(!(accessType == UserProfile.AccessType.RANGE_PUBLIC_EXTERNAL || accessType == UserProfile.AccessType.RANGE_PUBLIC_ORG ||
                    accessType == UserProfile.AccessType.SHEET_PUBLIC_EXTERNAL || accessType == UserProfile.AccessType.SHEET_PUBLIC_ORG)) {
                LOGGER.log(Level.INFO, "MANAGE_LOCKED_RANGES: {0}", wbContainer.getResourceKey());
                responseArray = CellProtectionUtil.getProtectedRangesInfoJson(wbContainer, jObj.getString("currentSheet"), jObj.getBoolean("valSh"));
                responseJson.put(JSONConstants.SHEETLIST, ActionJsonUtil.addSheetInJsonArray(null, sheet.getAssociatedName(), true));
                responseJson.put(JSONConstants.PROTECTED_RANGES, responseArray);
            } else {
                LOGGER.log(Level.SEVERE, "HACKING ATTEMPT >>>> {0}", CurrentRealm.getUserProfile().getZUserId()); // No I18n
                throw new Exception();
            }
            break;  
        case ActionConstants.IS_LOCK_EXISTS:
        	String id = jObj.getString(JSONConstants.ZUID);
            boolean isExternalUser = false;
            if(externalLinkId != 0l) {
                isExternalUser = true;
            }
            responseJson.put(JSONConstants.IS_SHEET_PROTECTED, sheet.isLocked(id, externalLinkId));

        	responseJson.put(JSONConstants.HAS_PROTECTED_RANGES, !(sheet.getProtectionRangeMap().isEmpty()));
        	boolean dhpr = false;
        	for(Sheet sheetob : workbook.getSheetList()){
        		Map<Protection, List<DataRange>> protectionRangeMap = sheetob.getProtectionRangeMap();
        		if(!sheetob.getProtectionRangeMap().isEmpty()){
                    if(sheetob.isLocked(JSONConstants.ZUID, externalLinkId)){
                        dhpr = true;
                        break;
                    }

        			for(Protection tmpProtection : protectionRangeMap.keySet())
        			{
        				List<DataRange> ranges = protectionRangeMap.get(tmpProtection);
        				for(DataRange range1 : ranges)
        				{
        					boolean isAuthorised = isExternalUser ? tmpProtection.isAuthorizedExternalShareLink(externalLinkId) : tmpProtection.isAuthorized(jObj.getString(JSONConstants.ZUID), false);
        					if(!isAuthorised){
        						dhpr = true;
        						if(sheet.getName().equalsIgnoreCase(sheetob.getName())){
        							responseJson.put(JSONConstants.HAS_PROTECTED_RANGES, true);
        						}
        						break;
        					}
        				}
        			}
        		}
        	}
        	responseJson.put("dhpr", dhpr);
    		break;
        case ActionConstants.PROTECT_RANGE_DIALOG:
                uProfile = CurrentRealm.getUserProfile();
                boolean isSheet = jObj.getBoolean("isSheet");
//                boolean isNew = jObj.getBoolean("isNew");
                boolean isExtend = jObj.getBoolean("isExtend");
                Protection protection = null;
                if (isSheet == true) {
                    protection = sheet.getProtection();
                } else if (isExtend == true) {

                    protection = RangeUtil.getProtection(sheet, jObj.getInt("exSr"), jObj.getInt("exSc"), jObj.getInt("exEr"), jObj.getInt("exEc"));
                    if (protection == null) {
                        protection = sheet.getProtection();
                    }
                }

                if (protection == null && !isSheet) {
                    singleRange = ActionJsonUtil.getFirstRangeFromJsonArray(jObj.optJSONArray(JSONConstants.RANGELIST));
                    protection = RangeUtil.getProtection(sheet, singleRange.getInt(JSONConstants.START_ROW), singleRange.getInt(JSONConstants.START_COLUMN), singleRange.getInt(JSONConstants.END_ROW), singleRange.getInt(JSONConstants.END_COLUMN));
//                        isNew = (protectedRange == null);
                }
                HashMap sheetSharedDetails = CellProtectionUtil.getSheetSharedDetails(wbContainer, sheet, isSheet);
                JSONObjectWrapper prJson = new JSONObjectWrapper();
                ResourceInfo resinfo = null;
                String libInfo = null;
                
                try {
                	resinfo = ZohoFS.getResourceInfo(wbContainer.getResourceId());
                	libInfo = ZohoFS.getLibraryInfo(wbContainer.getDocsSpaceId(), wbContainer.getResourceId());
                } catch (Exception e) {
                	LOGGER.log(Level.WARNING, "Problem while getting ZohoFS ResourceInfo");
                }
                
    			JSONObjectWrapper libObj = new JSONObjectWrapper(libInfo);
    			int libType = (Integer) libObj.get("LIBRARY_TYPE");
    			
                if(!ZohoFS.isNewUI(wbContainer.getResourceId()) && libType != ResourceType.CRM_LIBRARY) {
                		prJson.put("owner", CellProtectionUtil.getOwnerLockPermission(wbContainer.getDocOwnerFullName(), wbContainer.getDocOwnerZUID(), protection, isSheet, sheet));
                } else if(libType == ResourceType.MYSPACE_LIBRARY || libType == ResourceType.TEMPLATE_LIBRARY) {
            			prJson.put("owner", CellProtectionUtil.getOwnerLockPermission(DocumentUtils.getZFullName(resinfo.getCreator()), resinfo.getCreator(), protection, true, sheet));
                }
                CellProtectionUtil.getPRReadWriteUsersLockPermission(sheetSharedDetails, protection, prJson);
                CellProtectionUtil.updatePRReadCommentUsersLockPermission(sheetSharedDetails, protection, prJson);
                CellProtectionUtil.updateLinkShareUsersLockPermission(sheetSharedDetails, protection, prJson, isSheet, sheet);
                CellProtectionUtil.updatePRReadOnlyUsersLockPermission(sheetSharedDetails, protection, prJson);
                CellProtectionUtil.updateExternalSharedLinksLockPermission(sheetSharedDetails, protection, prJson, true, sheet);
                responseJson.put(JSONConstants.PROTECTED_RANGES, prJson);
                break;
	     case ActionConstants.JSP_VERSION_VIEW_DIALOG:
			        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			        responseJson.put("result", responseMap);
			        break;
             case ActionConstants.READ_MERGE_CONFIGURATION:
                 MergeConfiguration mergeConfiguration;
                 if(ZSAdminPanelUtils.isFeaturePermitted(wbContainer.getDocOwner(), "mergetemplate")) {
                     mergeConfiguration = workbook.getMergeConfiguration();
                     responseJson = (mergeConfiguration != null) ? new JSONObjectWrapper(mergeConfiguration.getAsJSONObject()) : new JSONObjectWrapper(MergeConfigurationUtils.setMergeSettingsInWorkbook(wbContainer));
                     String rid = wbContainer.getResourceId();
                     String spaceId = ZohoFS.getSpaceId(rid);
                     String ownerName = DocumentUtils.getZUserName(spaceId);
                     long docID = Long.parseLong(DocumentUtils.getDocumentId(rid, ownerName));
                     responseJson.put("is_any_merge_job_scheduled", MergeJobUtils.hasScheduledMergeJob(ownerName, docID));
                     responseJson.put("spreadsheet_date_format", wbContainer.getWorkbookForSave().getSpreadsheetSettings().getDateInputFormat());
                 } else {
                     responseJson = APIErrorHandler.getErrorObjectWrapper(APIErrorHandler.FEATURE_DISABLED_ERR, null, null);
                 }
                 break;
             case ActionConstants.READ_MERGE_SOURCE:
                 mergeConfiguration = workbook.getMergeConfiguration();
                 if(mergeConfiguration == null)
                 {
                     MergeConfigurationUtils.setMergeSettingsInWorkbook(wbContainer);
                 }
                 if(mergeConfiguration != null)
                 {
                    responseJson = new JSONObjectWrapper(MergeSourceUtils.readSourceWorkbook(mergeConfiguration, jObj.getJsonObject()));
                 }
                 break;
             case ActionConstants.UPDATE_MERGE_CONFIGURATION:
                 boolean reset = jObj.optBoolean("reset", false);   //No I18N
                 boolean isSaveRequired = jObj.optBoolean("is_save_required", true);   //No I18N
                 JSONObjectWrapper mergeConfigObj = jObj.getJSONObject(JSONConstants.MERGE_CONFIGURATION);
                 if(reset)
                 {
                     MergeConfigurationUtils.resetMergeConfiguration(wbContainer, mergeConfigObj.getJsonObject());
                 }
                 MergeConfigurationUtils.updateMergeConfiguration(workbook, mergeConfigObj.getJsonObject());
                 if(isSaveRequired) {
                    MergeConfigurationUtils.saveMergeSettings(wbContainer);
                 }
                 break;
             case ActionConstants.IMPORT_MERGE_SOURCE:
                 responseJson = new JSONObjectWrapper(MergeSourceUtils.importMergeSource(request, wbContainer, jObj.getJsonObject()));
                 break;
            case ActionConstants.GENERATE_MERGE_PREVIEW:
                boolean mergePreviewStatus = false;
                if(jObj.has("field_data_obj"))
                {
                    mergePreviewStatus = MergeTemplateUtils.generateMergePreview(wbContainer, workbook, jObj.getJSONObject("field_data_obj").getJsonObject());
                }
                else
                {
                    wbContainer.setMergePreviewWorkbook(null);
                }
                responseJson.put(JSONConstants.IS_MERGE_PREVIEW_GENERATED, mergePreviewStatus);
                break;
             case ActionConstants.FORM_PUBLISH:
                 UserActionManager.doAction(wbContainer, workbook, jObj);
                 break;
             case ActionConstants.FORM_UNPUBLISH:
                 UserActionManager.doAction(wbContainer, workbook, jObj);
                 break;
             case ActionConstants.GET_VERSION_DETAILS:
                 responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                 responseJson.put("result", responseMap);
                 break;
	            case ActionConstants.DATA_VALIDATION_READLIST:                	
	            case ActionConstants.DATA_VALIDATION_READSHEET:
	            case ActionConstants.DATA_VALIDATION_READWORKBOOK:
	            case ActionConstants.DATA_VALIDATION_READRANGE:
	                	responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
	                    responseJson.put("dataval", responseMap.get("dval"));              
	                	break;
	            case ActionConstants.VALIDATE_INPUT:
                case ActionConstants.VALIDATE_INPUT_CF:
                        if(action == ActionConstants.VALIDATE_INPUT_CF)
                        {
                            //For finding sheetlist and rangelist of old client.
                            boolean FromNewClient = (request.getParameter(JSONConstants.SHEETLIST)!= null || request.getParameter(JSONConstants.RANGELIST) != null) ? true : false;
                            if(!FromNewClient)
                            {
                                ThemeJsonConvertor.getRangeListForOldClient(jObj, sheet);
                            }
                        }
                        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                        responseJson.put("valinp", responseMap.get("valinp"));
                        break;
                    case ActionConstants.GET_ACTION_LIST:
                        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                        responseJson.put("auditTrailDialog", responseMap);
                        break;

        case ActionConstants.SERVERCLIP_CUT_RANGE:
            jObj.put("host", request.getServerName());
            responseMap = 	UserActionManager.doAction(wbContainer, workbook, jObj);
            boolean isHtml =  responseMap.containsKey("isHtml") ? (Boolean)responseMap.get("isHtml") : false; //NO I18N
            if(!isHtml)
            {
                response.setContentType("text/plain");
            }
            tabType = CurrentRealm.getTabType();
            //  SHIRU - IS_NEW_CLIENT - TO DIFFERENTIATE WEB AND MOBILE REQUEST
            if(tabType == TabType.CACHED && !isWebClient)
            {
                JSONObjectWrapper	res	=	new		JSONObjectWrapper();
                if(responseMap.containsKey("sheetserverclip")) {
                    res.put(Integer.toString( CommandConstants.SERVERCLIP_INFO ), (JSONObjectWrapper)responseMap.get("sheetserverclip"));
                }
                HttpServletResponseWrapper.sendResponse(response,res, HttpServletResponseWrapper.MimeType.HTML);
            }
            else
            {
                String result  =  (String)responseMap.get("range");
                HttpServletResponseWrapper.sendResponse(response,result, HttpServletResponseWrapper.MimeType.HTML);
            }
            return null;
        case ActionConstants.SERVERCLIP_REMOVE:
        case ActionConstants.SERVERCLIP_COPY_SHEET:
            responseMap = 	UserActionManager.doAction(wbContainer, workbook, jObj);
            JSONObjectWrapper	res	=	new		JSONObjectWrapper();
            if(responseMap.containsKey("sheetserverclip")) {
                res.put(Integer.toString( CommandConstants.SERVERCLIP_INFO ), (JSONObjectWrapper)responseMap.get("sheetserverclip"));
            }
            HttpServletResponseWrapper.sendResponse(response,res, HttpServletResponseWrapper.MimeType.HTML);
            return null;
                
	            case ActionConstants.SHEET_COPY_CREATE_NEW_SPREADSHEET:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("newRID", responseMap.get("newRID"));
	            	break;
	            case ActionConstants.SERVERCLIP_GET_REFSHEETS:
	            	responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
	            	responseJson.put("sheetList", responseMap.get("sheetList"));
	            	responseJson.put("refsheetList", responseMap.get("refsheetList"));
	            	break;
	            case ActionConstants.SERVERCLIP_PASTE_SHEET_CANCEL:
	            	break;
                case ActionConstants.GET_MARKDOWN_DATA:
                case ActionConstants.GET_TSV_DATA:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    String dataResult  = null;
                    String dataError = null;
                    if (responseMap != null) {
                        if(responseMap.containsKey("error")){ //NO I18N
                            dataError = (String) responseMap.get("error");//NO I18N
                        }else{
                            dataResult = (String)responseMap.get("range");//NO I18N
                        }
                    }

                    boolean isTranslation = jObj.optBoolean(JSONConstants.IS_Z_TRANSLATION, false);
                    int maxAllowedChars = isTranslation ? 100000 : 50000;

                    JSONObjectWrapper	dataResp  =	   new	JSONObjectWrapper();
                    if(dataError != null || Objects.nonNull(dataResult) && dataResult.length() >= maxAllowedChars){
                        dataResp.put(Integer.toString(CommandConstants.ERROR), "CONVERSION.LIMIT_EXCEED"); //NO I18N
                    }else{
                        dataResp.put(Integer.toString(CommandConstants.DATA), dataResult);
                    }
                    HttpServletResponseWrapper.sendResponse(response,dataResp, HttpServletResponseWrapper.MimeType.TEXT);
                    return null;
	            case ActionConstants.SERVERCLIP_COPY_RANGE:
                case ActionConstants.COPY_TO_CLIP:
                
                    UserProfile.PermissionType permission = userProfile.getPermissionType();
                    if(permission == UserProfile.PermissionType.READ || permission == UserProfile.PermissionType.READ_COMMENT)
                    {
                        List<DataRange> dataRanges = ActionJsonUtil.getListOfDataRanges(jObj.optJSONArray(JSONConstants.SHEETLIST), jObj.optJSONArray(JSONConstants.RANGELIST));
                        List<DataRange> toAdd = new ArrayList<>();
                        for(DataRange dataRange : dataRanges)
                        {
                            toAdd.addAll(RangeUtil.getVisibleRanges(sheet, dataRange.getStartRowIndex(), dataRange.getStartColIndex(), dataRange.getEndRowIndex(), dataRange.getEndColIndex()));
                        }
                        jObj.put(JSONConstants.RANGELIST, new JSONArrayWrapper());
                        for (DataRange dataRange : toAdd) {
                            jObj.put(JSONConstants.RANGELIST, ActionJsonUtil.addRangeInJsonArray(jObj.optJSONArray(JSONConstants.RANGELIST), dataRange.getStartRowIndex(), dataRange.getStartColIndex(), dataRange.getEndRowIndex(), dataRange.getEndColIndex(), true));
                        }
                    }
                    jObj.put("host", request.getServerName());
                    jObj.put(JSONConstants.EXECUTED_ACTION_ID_WHILE_COPY, wbContainer.getExecutedActionId());
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    if(!jObj.has(JSONConstants.ONLY_CLIP_TO_SERVER)){
	                    String result  =  (String)responseMap.get("range");
	                    isHtml =  responseMap.containsKey("isHtml") ? (Boolean)responseMap.get("isHtml") : false; //NO I18N
                        HttpServletResponseWrapper.MimeType responseType = HttpServletResponseWrapper.MimeType.HTML;
	                    if(!isHtml)
	                    {
                            responseType = HttpServletResponseWrapper.MimeType.TEXT;
//	                        response.setContentType("text/plain");
	                    }
	                    //String tabTypeStr		=		request.getParameter("tabType");
	                    //TabType tabType			=		ClientUtils.getTabType(tabTypeStr);
                            tabType = CurrentRealm.getTabType();

                        //  SHIRU - IS_NEW_CLIENT - TO DIFFERENTIATE WEB AND MOBILE REQUEST
                        if(tabType == TabType.CACHED && !isWebClient)
                        {

                            JSONObjectWrapper	clipBoardResp  =	   new		JSONObjectWrapper();
                            JSONObjectWrapper	resp			   =	   new		JSONObjectWrapper();
                            resp.put(Integer.toString(CommandConstants.DATA), result);
                            //hardCoded as of now, later will change it
                            resp.put(Integer.toString(CommandConstants.TYPE), "TEXT");
                            clipBoardResp.put(Integer.toString(CommandConstants.COPY_TO_CLIP), resp);
                            if(responseMap.containsKey("serverclip")) {
                                clipBoardResp.put(Integer.toString( CommandConstants.SERVERCLIP_INFO), (JSONObjectWrapper)responseMap.get("serverclip"));
                            }

                            result		=	clipBoardResp.toString();
                        }
                        HttpServletResponseWrapper.sendResponse(response, result, responseType);
//	                    out.println(result);//NO OUTPUTENCODING
                    }
                    
                    
                    return null;
                case ActionConstants.SERVERCLIP_CLIPBOARD_COMPARE:
                	responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                	responseJson.put(JSONConstants.IS_SAME_CONTENT, responseMap.get("isSame"));
                	break;
	            case ActionConstants.DATA_HIGHLIGHT_INVALID:
	            		responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
               	 		responseJson.put("invalidCells", responseMap.get("invalidCells"));
	            	    break;
                    case ActionConstants.GET_INVALID_CELLS:
                            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
               	 		responseJson.put("invalidCells", responseMap.get("invalidCells"));
	            	    break;
                    case ActionConstants.CHART_GET_OPTIONS:
                            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                            responseJson.put("co", responseMap.get("co"));
	            	    break;
               /* case ActionConstants.DATA_VALIDATION_READRANGE:                	
                    responseMap = UserActionManager.doAction(workbook, jObj);
                	responseJson.put("DATATYPE", responseMap.get("dtype"));
                	responseJson.put("CONDITIONALFORMAT", responseMap.get("responsecf"));
                	break;*/
	             case ActionConstants.RESET_HIGHLIGHT_INVALID:
            	 
            		 responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
           	 		
            	     break;
		case	ActionConstants.CELLSTYLES_DEFINITION : 
            		responseMap		=			UserActionManager.doAction(wbContainer, workbook, jObj);
            		responseJson.put(Integer.toString(CommandConstants.CELLSTYLES_DEFINITION), responseMap.get(JSONConstants.CELLSTYLES_DEFINITION));
            		break;
		case ActionConstants.SERVERCLIP_CHECK_LOCALE:
			responseMap		=			UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.IS_SAME_LOCALE, responseMap.get(JSONConstants.IS_SAME_LOCALE));
			break;
		case ActionConstants.RESET_ANNOTE_POSITION:
                        responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                        break;
		case ActionConstants.DRE_GETALL:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
			break;
		case ActionConstants.DRE_CREATE:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
		break;
		case ActionConstants.DRE_CREATEANDEXECUTE:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
			break;
		case ActionConstants.DRE_UPDATE:
			
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
			break;
		case ActionConstants.DRE_EXECUTE:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
			break;
		case ActionConstants.DRE_UPDATEANDEXECUTE:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
			break;
		case ActionConstants.DRE_DELETE:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			DelugeUtils.gridActionTrigger(jObj,responseMap);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
			
			break;
		case ActionConstants.DRE_GETDETAIL:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
			
		break;
		case ActionConstants.DRE_GETNAME:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
			
		break;
		case ActionConstants.DRE_CONNECTIONS:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put(JSONConstants.DELUGE_RESPONSE, responseMap.get(JSONConstants.DELUGE_RESPONSE));
			
		break;
		case ActionConstants.DATA_CLEANSING:
             responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			 responseJson.put("clusters", responseMap.get("clusters").toString());
	        break;
		 case ActionConstants.FIND_TABLE_RANGE:
                     responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                     responseJson.put("tableRangeLabel", responseMap.get("tableRangeLabel").toString());
                     break;

                case ActionConstants.READ_DATA_DUPLICATES:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("tableRange", responseMap.get("tableRange").toString());
                break;
                case ActionConstants.READ_FREQUENCYY_TABLE:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("duplicatesInfo", responseMap.get("duplicatesInfo").toString());
                break;
                case ActionConstants.DATA_DUPLICATES_INFO:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("duplicatesInfo", responseMap.get("duplicatesInfo").toString());
                break;
                    case ActionConstants.GET_DATA_DUPLICATES:
                    responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
                    responseJson.put("duplicatesRows", responseMap.get("duplicateRows").toString());
                break;
		case ActionConstants.COUNT_EMPTY_CELLS:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put("count", responseMap.get("count"));
			 break;
        case ActionConstants.PICKLIST_INTERSECTION:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("picklist", responseMap.get("picklist"));
            break;
        case ActionConstants.PICKLIST_MANAGE:
            responseMap = UserActionManager.doAction(wbContainer, workbook,jObj);
            responseJson.put("picklists", responseMap.get("picklists"));
        case ActionConstants.PICKLIST_READ:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("values", responseMap.get("values"));
            responseJson.put("isLimitExceeded",responseMap.get("isLimitExceeded"));
            break;
        case ActionConstants.PICKLIST_RANGES:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("picklist", responseMap.get("picklist"));
            break;
        case ActionConstants.SPILL_OBSTRUCTING_CELLS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(JSONConstants.RANGES, responseMap.get(JSONConstants.RANGES));
            break;
		case ActionConstants.DRIVE_STATUS:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put("DRIVE_STATUS", responseMap.get("DRIVE_STATUS"));
			 break;
        case ActionConstants.WORKFLOW_META:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("WORKFLOW_META", responseMap.get("WORKFLOW_META"));
            break;
        case ActionConstants.FETCH_WORKFLOW_LIST:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("FETCH_WORKFLOW_LIST", responseMap.get("FETCH_WORKFLOW_LIST"));
            break;
        case ActionConstants.FETCH_WORKFLOW_STATUS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("FETCH_WORKFLOW_STATUS", responseMap.get("WORKFLOW_STATUS"));
            break;
		case ActionConstants.DOCUMENT_PROPERTIES:
			responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
			responseJson.put("DOCUMENT_PROPERTIES", responseMap.get("DOCUMENT_PROPERTIES"));
			 break;
        case ActionConstants.LINKED_WORKBOOKS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(DataAPIConstants.KEY_RESPONSE, responseMap.get(DataAPIConstants.KEY_RESPONSE));
            break;
        case ActionConstants.MARK_AS_FAVOURITE:
            
            boolean isFavorite = Boolean.valueOf(request.getParameter("favourite"));
            String rid = request.getParameter("rid");
            jObj.put("favorite", isFavorite);
            jObj.put("rid", rid);
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(DataAPIConstants.KEY_RESPONSE, responseMap.get(DataAPIConstants.KEY_RESPONSE));
            break;
	case ActionConstants.GET_THEMES:
                    responseJson.put("THEMES", ZSThemesLibrary.readZSThemes());
                    break;
        case ActionConstants.FETCH_CELL_EDIT_HISTORY:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            JSONObjectWrapper resJson = new JSONObjectWrapper(responseMap);
            HttpServletResponseWrapper.sendResponse(response,resJson, HttpServletResponseWrapper.MimeType.JSON);
            return null;
        case ActionConstants.STORE_THUMBNAIL:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("THUMBNAIL", responseMap.get("STATUS"));
            break;

        case ActionConstants.PREDICT_TABLE_RANGE:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put("range", responseMap.get("range"));
            responseJson.put(JSONConstants.CONTAINS_HEADER, responseMap.get(JSONConstants.CONTAINS_HEADER));
            responseJson.put(JSONConstants.IS_TABLE_PRESENT, responseMap.get(JSONConstants.IS_TABLE_PRESENT));
            break;
        
        case ActionConstants.SEND_ACCOUNT_CONFIRMATION_MAIL:
        	jObj.put("remoteAddress",  request.getRemoteAddr());
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(DataAPIConstants.KEY_RESPONSE, responseMap.get(DataAPIConstants.KEY_RESPONSE));
            break;
            
        case ActionConstants.CHECK_ACCOUNT_CONFIRMATION_STATUS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(DataAPIConstants.KEY_RESPONSE, responseMap.get(DataAPIConstants.KEY_RESPONSE));
            break;

        case ActionConstants.TABLE_CREATE_CHECK:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson.put(JSONConstants.CREATE, responseMap.get(JSONConstants.CREATE));
            responseJson.put(JSONConstants.ERROR_MESSAGE, responseMap.get(JSONConstants.ERROR_MESSAGE));
            break;
        case ActionConstants.FETCH_USER_SPREADSHEET_SETTINGS:
        case ActionConstants.SAVE_USER_SPREADSHEET_SETTINGS:
            responseMap = UserActionManager.doAction(wbContainer, workbook, jObj);
            responseJson = new JSONObjectWrapper(responseMap);
            break;
//		case ActionConstants.IMPORT_CANCEL:
//			responseMap		=			UserActionManager.doAction(workbook, jObj);
//			break;
            	}
         //	LOGGER.log(Level.INFO, "Response JSON from UserAction: {0} :::: ResponseJSON: {1}", new Object[]{jObj, responseJson});
            long timeTaken = System.currentTimeMillis() - startTime;
            if(timeTaken > 1000)
            {
                LOGGER.log(Level.INFO, "[USER_ACTION] Total time taken to execute user action is {0} ms", new Object[]{timeTaken});
            }
            HttpServletResponseWrapper.sendResponse(response,responseJson, HttpServletResponseWrapper.MimeType.JSON);
        }
        catch (DataConnection.DataConnectionFailedException dfe)
        {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            JSONObjectWrapper errorObj = ErrorCode.getErrorMessage(dfe.getMessage(), null, MsgType.WARNING, DisplayType.DIALOG, dfe.getParams());
            response.getWriter().print(errorObj); // NO OUTPUTENCODING
        }
        catch (ProhibitedActionException e)
        {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            JSONObjectWrapper errObj = ErrorCode.getProhibitedDialogErrorMsg(e);
            HttpServletResponseWrapper.sendResponse(response, errObj, HttpServletResponseWrapper.MimeType.JSON);
        }
        catch(Exception ex)
        {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            LOGGER.log(Level.WARNING, "[USER_ACTION][Exception] Exception while executing user action.", ex);
            String errMsg = ex.getMessage();
            JSONObjectWrapper errObj = ErrorCode.getErrorMessage(ErrorCode.ERROR_ACTION_EXECUTION, null, MsgType.ERROR, DisplayType.BANNER);
            if(errMsg != null)
            {
                if(ErrorCode.isDefined(errMsg))
                {
                    errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.ERROR, DisplayType.BANNER);
                    String rid = CurrentRealm.getContainer() != null ? CurrentRealm.getContainer().getResourceId() : "";
                    LOGGER.log(Level.INFO, "[ZS_ERROR_BANNER][USER_ACTION][{0}][RID:{1}][ URL:{2}]", new Object[]{errMsg, rid, request.getRequestURL()});
                    if(ErrorCode.DIALOG_ERRORS.contains(errMsg))
                    {
                        if(errMsg.equals(ErrorCode.ERROR_IMAGE_LIMIT_EXCEED))
                        {
                            String[] params = new String[1];
                            params[0] = String.valueOf(EngineConstants.MAXIMUM_NO_OF_IMAGES);
                            errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.WARNING, DisplayType.DIALOG, params);
                        }
                        else if(errMsg.equals(ErrorCode.ERROR_IMAGE_SIZE_EXCEED))
                        {
                            String[] params = new String[1];
                            params[0] = String.valueOf(EngineConstants.ALLOWED_IMAGE_FILE_SIZE_IN_MB);
                            errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.WARNING, DisplayType.DIALOG, params);
                        }
                        else if(errMsg.equals(ErrorCode.ERROR_IMAGE_TOTAL_SIZE_EXCEED))
                        {
                            String[] params = new String[1];
                            params[0] = String.valueOf(EngineConstants.ALLOWED_TOTAL_IMAGE_FILE_SIZES_IN_MB);
                            errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.WARNING, DisplayType.DIALOG, params);
                        }
                    }
                    else if(errMsg.contains(ErrorCode.COPYCLIP_DOES_NOT_EXIST) || errMsg.contains(ErrorCode.ERROR_SHEET_LIMIT_EXCEED) || errMsg.contains(ErrorCode.COPYCLIP_SOURCE_DOCUMENT_TRASHED) || errMsg.contains(ErrorCode.COPYCLIP_SOURCE_DOCUMENT_NOT_IN_SHARED_STATE) || errMsg.contains(ErrorCode.COPYCLIP_SOURCE_SHEET_DOES_NOT_EXIST) || errMsg.contains(ErrorCode.ERROR_NO_DEFINED_COLUMN))
                    {
                        String[] eMsgSplit = errMsg.split("#");
                        errObj = ErrorCode.getErrorMessage(eMsgSplit[0], null, MsgType.WARNING, DIALOG, eMsgSplit[1]);
                    }
                    else if(errMsg.contains(ERROR_CELL_EDIT_HISTORY))
                    {
                        errObj = ErrorCode.getErrorMessage(ERROR_CELL_EDIT_HISTORY, null, MsgType.ERROR, BANNER);
                    }
                }
                else if(errMsg.contains(ERROR_USER_SETTINGS_FETCH) || errMsg.contains(ERROR_USER_SETTINGS_SAVE))
                {
                    errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.ERROR, BANNER);
                }
            }
            HttpServletResponseWrapper.sendResponse(response,errObj, HttpServletResponseWrapper.MimeType.JSON);
        }
        return null;
    }
    public static Locale getAccountLocale() {
		User userObj = IAMUtil.getCurrentUser();
		if (userObj != null) {
			String _localeInfo = userObj.getLocaleInfo();
			String languageCode = null;
			String countryCode = null;
			try {
				if (_localeInfo != null) {
					String[] tempDocLocale = _localeInfo.split("\\|");
					countryCode = (tempDocLocale[0] == null ? EngineConstants.DEFAULT_LOCALE.getCountry() : tempDocLocale[0]);
					languageCode = (tempDocLocale[1] == null ? EngineConstants.DEFAULT_LOCALE.getLanguage() : tempDocLocale[1]);
					Locale accountLocale = LocaleUtil.getLocale(languageCode, countryCode);
					return accountLocale;
				}
			} catch (Exception e) {
				LOGGER.info(" Unable to get Locale from IAM... " + e);
			}
		}
		return null;
	}
    
}
