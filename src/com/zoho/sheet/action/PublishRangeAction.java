/* $Id$ */
package com.zoho.sheet.action;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.filter.DefaultFilterView;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.exception.ExceptionHandler;
import com.zoho.sheet.util.StrutsRequestHandler;
import org.apache.commons.lang.ArrayUtils;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQuery;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.Table;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.zoho.sheet.parse.RangeAddress;
import com.zoho.sheet.parse.html.HtmlRange;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.SheetPersistenceUtils;

public class PublishRangeAction extends StrutsRequestHandler {

    public static final Logger LOGGER = Logger.getLogger(PublishRangeAction.class.getName());

    @Override
    public String execute() throws Exception {
        String sheetname;
        String fetchPublishRangeData = request.getParameter("fetchhtmldata");
        fetchPublishRangeData = "true";
        
        String encurl = (String) request.getParameter(JSONConstants.RANGE_ID);
        if(encurl == null){
            encurl = (String) request.getAttribute(JSONConstants.RANGE_ID);
        }
        
        try {
            if (encurl != null && "true".equals(fetchPublishRangeData)) {
                WorkbookContainer container = CurrentRealm.getContainer();
                Workbook workBook = container.getWorkbook(null);
                if (!workBook.isDependenciesUpdated()) {
                    workBook.updateCellDependencies();
                }
                workBook.recalculateDynamicFormulas();

                String publishType = null;

                Criteria cri = new Criteria(new Column("RangePublish", "ENCRYPTED_URL"), encurl, QueryConstants.EQUAL);
                Persistence pers = SheetPersistenceUtils.getPersistence("Public"); //NO I18N
                SelectQuery sql = new SelectQueryImpl(new Table("RangePublish"));
                sql.addSelectColumn(new Column(null, "*"));
                sql.setCriteria(cri);
                DataObject dataObject = pers.get(sql);

                long zoid = DocumentUtils.getZOID();// UserUtils.ZOID(request); -- Roseline

                boolean isPublic = false;
                if (!dataObject.isEmpty()) {
                    isPublic = true;
                    publishType = "external"; //No I18N
                }

                if (!isPublic && zoid > -1) {
                    pers = SheetPersistenceUtils.getPersistence(Constants.CORPORATEDBPREFIX + zoid);
                    sql = new SelectQueryImpl(new Table("RangePublish"));
                    sql.addSelectColumn(new Column(null, "*"));
                    sql.setCriteria(cri);
                    dataObject = pers.get(sql);
                    if (!dataObject.isEmpty()) {
                        isPublic = true;
                        publishType = "org"; //No I18N
                    }
                }

                Boolean isMobileDevicePublishRequest = Boolean.parseBoolean(request.getParameter("isHandheldDevice"));

                if (!isPublic) {
                    request.setAttribute(Constants.PUB_RANGE, false);
                    // This check has to be done in another place as the request don't pass the filter level upto this action class
                    // If it is a mhandheld request, then construct an error object with Status 500 with appropriate message as response
                    if (isMobileDevicePublishRequest) {
                        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                        ExceptionHandler.handle(request, response, ErrorCode.ERRORMSG_PUBLISH_LINK_REMOVED);
                        return null;
                    }
                    else {
                        return "failure";//No I18N
                    }
                } else {
                    Long docId = (Long) dataObject.getFirstValue("RangePublish", "DOCUMENT_ID");
                    long sheet_id = (Long) dataObject.getFirstValue("RangePublish", "SHEET_ID");
                    LOGGER.info("[EMOJI PUBLISHSHEET TEST] "+encurl+" sheetID"+sheet_id);
                    String SHARE_OWNER = (String) dataObject.getFirstValue("RangePublish", "SHARE_OWNER");
                    String RANGE_SROW = String.valueOf((Long) dataObject.getFirstValue("RangePublish", "RANGE_SROW"));
                    String RANGE_SCOL = String.valueOf((Long) dataObject.getFirstValue("RangePublish", "RANGE_SCOL"));
                    String RANGE_EROW = String.valueOf((Long) dataObject.getFirstValue("RangePublish", "RANGE_EROW"));
                    String RANGE_ECOL = String.valueOf((Long) dataObject.getFirstValue("RangePublish", "RANGE_ECOL"));
                    String ADDITIONAL_INFO = (String) dataObject.getFirstValue("RangePublish", "ADDITIONAL_INFO");	//NO I18N

                    sheetname = DocumentUtils.getSheetName(sheet_id, SHARE_OWNER);
                    LOGGER.info("[EMOJI PUBLISHSHEET TEST] "+encurl+" sheetname"+sheetname);
                    Criteria cri1 = new Criteria(new Column("Documents", "DOCUMENT_ID"), docId, QueryConstants.EQUAL);
                    cri1 = cri1.and(new Column("Documents", "DEL_TIME"), (long) 0, QueryConstants.EQUAL);
                    Persistence pers1 = SheetPersistenceUtils.getPersistence(SHARE_OWNER);
                    SelectQuery sql1 = new SelectQueryImpl(new Table("Documents"));
                    sql1.addSelectColumn(new Column(null, "*"));
                    sql1.setCriteria(cri1);
                    DataObject dataObject1 = pers1.get(sql1);

                    if (dataObject1.isEmpty()) {
                        request.setAttribute(Constants.PUB_RANGE, false);
                        return "failure";//No I18N
                    }
                    String docNm = (String) dataObject1.getFirstValue("Documents", "DOCUMENT_NAME");
                    request.setAttribute(Constants.ACTIVESHEETINDEX, sheet_id);
                    request.setAttribute(Constants.CURRENTSHEETNAME, sheetname);
                    request.setAttribute(Constants.DOCUMENT_NAME, docNm);
                    request.setAttribute(Constants.DOCUMENT_ID, docId.toString());
                    request.setAttribute("SHARE_OWNER", SHARE_OWNER);
                    request.setAttribute(Constants.VIEW, Constants.PUBLISH_RANGE_VIEW);

                    //OpenDocBean ob = (OpenDocBean)BeanUtil.lookup("OpenDocBean", SHARE_OWNER);
                                        /* long accId = SasUtil.getInstance().getAccountId(SHARE_OWNER);					
					 ownerZUID = UserUtils.getOwnerZUID(request, SHARE_OWNER, docId);
					 long ownerZUID = DocumentUtils.getZUID(SHARE_OWNER);
					 String fileFormat = DocumentUtils.getFileFormat(ownerZUID,SHARE_OWNER, docId, null);
					 boolean isSXC = true;
					 boolean isODS = false;
					 boolean isFragmentedFile = false;

					 if(EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT.equals(fileFormat)) {
					 isFragmentedFile = true;
					 } else if(EngineConstants.ENGINE_ODS_FILE_FORMAT.equals(fileFormat)) {
					 isODS = true;
					 } else {
					 isSXC = true;
					 }

					 Workbook workBook = null;
					 if(isODS || isFragmentedFile) {
					 //workBook = ob.getWorkBook(String.valueOf(docid), SHARE_OWNER, String.valueOf(accId), docName, request, null, currenttime, null, false, String.valueOf(accId), null, false);
					 workBook = EngineUtils.getInstance().getWorkBook(ownerZUID, SHARE_OWNER, docid, docName, request, null, currenttime, null, false, null, false);
						
					 DocumentUtils.updateUserTimeZone(request, workBook);
					 }
					 SubmitCellAction.updateWebdataToSXC (null, ownerZUID, docid, docName, SHARE_OWNER, null, request, workBook, true); -- commented by Roseline*/
                    int sR = Integer.parseInt(RANGE_SROW);
                    int sC = Integer.parseInt(RANGE_SCOL);
                    int eR = Integer.parseInt(RANGE_EROW);
                    int eC = Integer.parseInt(RANGE_ECOL);
                    
                    JSONObjectWrapper json = null;
                    if(ADDITIONAL_INFO != null && !"".equals(ADDITIONAL_INFO)) {
                        json = new JSONObjectWrapper(ADDITIONAL_INFO);
                    }

                    boolean isBrowserIE = false;
                    String browser_info = request.getHeader("User-Agent");
                    if (browser_info.contains("MSIE")) {
                        isBrowserIE = true;
                    }

                    if (workBook != null) {

                        Sheet sheetObj = workBook.getSheet(sheetname);
                        LOGGER.info("[EMOJI PUBLISHSHEET TEST] "+encurl+" sheetObj"+sheetObj);
                        request.setAttribute(JSONConstants.ASSOCIATED_SHEET_NAME, sheetObj.getAssociatedName());
                        int usedRow = sheetObj.getUsedRowIndex();
                        int usedCol = sheetObj.getUsedColumnIndex();

                        if (sR < usedRow && eR > usedRow) {
                            eR = usedRow;

                        } else if(sR >= usedRow && eR > usedRow) {
                            eR = sR;
                        }

                        if (sC < usedCol && eC > usedCol) {
                            eC = usedCol;

                        } else if(sC >= usedCol && eC > usedCol) {
                            eC = sC;
                        }

                        RangeAddress addr = new RangeAddress(sR, sC, eR, eC);

                        //StringBuffer csvData = EngineUtils.getInstance().getCSVData(sheetObj, sR, sC, eR, eC);
                        //request.setAttribute("CsvRange", csvData);
                        Iterator<ReadOnlyRow> unHiddenRows = sheetObj.getUnHiddenRows(sR, eR);
                        DefaultFilterView defaultFilterViewObj = sheetObj.getDefaultFilterView();
                        FilterView defaultFilterView = defaultFilterViewObj != null ? defaultFilterViewObj.createFilterView(sheetObj) : null;
                        List<Integer> visibleRows = new ArrayList<>();
                        while(unHiddenRows.hasNext())
                        {
                            int rowIndex = unHiddenRows.next().getRowIndex();
                            if (defaultFilterView == null)
                            {
                                visibleRows.add(rowIndex);
                            }
                            else
                            {
                                if(!defaultFilterView.isFilteredRow(rowIndex))
                                {
                                    visibleRows.add(rowIndex);
                                }
                            }
                        }
                        int rowsAry[] = ArrayUtils.toPrimitive(visibleRows.toArray(new Integer[]{}));
                        Set<Integer> colsAryList = RangeUtil.getVisibleCols(sheetObj, sC, eC).toSet();
                        List colsAry = new ArrayList();
                        colsAry.addAll(colsAryList);
                        request.setAttribute(Constants.ROWSARY, rowsAry);
                        request.setAttribute(Constants.COLSARY, colsAry);

//                        HashMap hashMap = EngineUtils.getInstance().readSheetDetails(container, CurrentRealm.getWorkBookIdentity(), sheetObj, rowsAry, sC, eC, true, false);

//                        HtmlRange obj = new HtmlRange(addr, hashMap);
//                        request.setAttribute("HtmlRange", obj);

                        //EngineUtils.getInstance().removeWorkBook((String) request.getAttribute(EngineConstants.CURRENTTIME), request);
                    }
                    request.setAttribute(Constants.STARTROW_TOP, 0L);
                    request.setAttribute(Constants.STARTROW, sR);
                    request.setAttribute(Constants.ENDROW, eR);
                    request.setAttribute(Constants.STARTCOL, sC);
                    request.setAttribute(Constants.ENDCOL, eC);
                    request.setAttribute(Constants.IS_BROWSER_IE, isBrowserIE);
                    request.setAttribute(Constants.SHOWFORMULAS, false);
                    
//                    if("grid".equals(request.getParameter("mode"))) {
//                        if(sR == 0 && sC == 0 && eR == Utility.MAXNUMOFROWS - 1 && eC == Utility.MAXNUMOFCOLS - 1) {
//                        		request.setAttribute("isSheetGrid", true);
//                        }
//                        	request.setAttribute("isRangeGrid", true);
//                        request.setAttribute(JSONConstants.RANGE_ID, encurl);
//                        request.getRequestDispatcher("gridView.do").forward(request, response);   //NO I18N
//                        return null;
//                    }

                    String url = null;

                    // Adding this below code exclusively to support to view published sheet/range on mobile device
                    if (isMobileDevicePublishRequest) {
                        request.setAttribute("devicetype", "mhandheld");//it may overrides the existing one
                        url = "mpproxyopen.do";

                        JSONObjectWrapper publishInfo = new JSONObjectWrapper();
                        boolean isSheetGrid = (json != null && json.has("ISSHEETPUBLISH")) ? Boolean.valueOf(json.getString("ISSHEETPUBLISH")) : false;

                        publishInfo.put(Integer.toString(CommandConstants.IS_SHEET_PUBLISH), isSheetGrid);
                        publishInfo.put(Integer.toString(CommandConstants.PUBLISH_TYPE), publishType);

                        publishInfo.put(Integer.toString(CommandConstants.ALLOW_INTERACTIONS), (json != null && json.has("ALLOWINTERACTIONS")) ? Boolean.valueOf(json.getString("ALLOWINTERACTIONS")) : false);
                        publishInfo.put(Integer.toString(CommandConstants.ALLOW_EXPORT), (json != null && json.has("ALLOWEXPORT")) ? Boolean.valueOf(json.getString("ALLOWEXPORT")) : false);
                        publishInfo.put(Integer.toString(CommandConstants.SHOW_FORMULAS), (json != null && json.has("SHOWFORMULAS")) ? Boolean.valueOf(json.getString("SHOWFORMULAS")) : false);
                        publishInfo.put(Integer.toString(CommandConstants.HIDE_GRID), (json != null && json.has("HIDEGRIDLINES")) ? Boolean.valueOf(json.getString("HIDEGRIDLINES")) : false);
                        publishInfo.put(Integer.toString(CommandConstants.HIDE_FORMULABAR), (json != null && json.has("HIDEFORMULABAR")) ? Boolean.valueOf(json.getString("HIDEFORMULABAR")) : false);
                        publishInfo.put(Integer.toString(CommandConstants.HIDE_HEADERS), (json != null && json.has("HIDEHEADERS")) ? Boolean.valueOf(json.getString("HIDEHEADERS")) : false);

                        if (!isSheetGrid) {
                            publishInfo.put(Integer.toString(CommandConstants.START_ROW), sR);
                            publishInfo.put(Integer.toString(CommandConstants.END_ROW), eR);
                            publishInfo.put(Integer.toString(CommandConstants.START_COL), sC);
                            publishInfo.put(Integer.toString(CommandConstants.END_COL), eC);

                            // For Range Publish, Header and FormulaBar is not shown by default and user is not given the option to choose them.
                            // So sending the default value as true
                            publishInfo.put(Integer.toString(CommandConstants.HIDE_FORMULABAR), true);
                            publishInfo.put(Integer.toString(CommandConstants.HIDE_HEADERS), true);
                        }
                        else {
                            if (IAMUtil.getCurrentUser() != null) {
                                String userZUID = IAMUtil.getCurrentUser().getZuid();
                                boolean isDataHidden = RangeUtil.isHiddenDataLocked(workBook.getSheet(sheetname), new DataRange(workBook.getSheet(sheetname).getAssociatedName(), 0, 0, Utility.MAXNUMOFROWS - 1, Utility.MAXNUMOFCOLS - 1), true, userZUID);
                                publishInfo.put(Integer.toString(CommandConstants.IS_DATA_HIDDEN), isDataHidden);
                            }
                        }

                        String docOwner = json.getString("SHARED_BY");
                        String viewCount = RedisHelper.hget(RedisHelper.PUBLIC_VIEW_COUNT, container.getResourceId());
                        if (viewCount == null) {
                            viewCount = "1";
                        }

                        publishInfo.put(Integer.toString(CommandConstants.OWNER), docOwner);
                        publishInfo.put(Integer.toString(CommandConstants.PUBLISHED_VIEWS), viewCount);

                        request.setAttribute("PublishInfo", publishInfo);
                        request.getRequestDispatcher(url).forward(request, response);
                        return null;
                    }
                    
                    String mode = request.getParameter("type");
                    if("grid".equals(mode)) {
                    	request.setAttribute("isRangeGrid", true);
                    	request.setAttribute("isSheetGrid", (json != null && json.has("ISSHEETPUBLISH")) ? Boolean.valueOf(json.getString("ISSHEETPUBLISH")) : false);
                    	
                    	request.setAttribute("allowInteractions", (json != null && json.has("ALLOWINTERACTIONS")) ? Boolean.valueOf(json.getString("ALLOWINTERACTIONS")) : false);
                        request.setAttribute("allowExport", (json != null && json.has("ALLOWEXPORT")) ? Boolean.valueOf(json.getString("ALLOWEXPORT")) : false);
                        request.setAttribute("showFormulas", (json != null && json.has("SHOWFORMULAS")) ? Boolean.valueOf(json.getString("SHOWFORMULAS")) : false);
                        request.setAttribute("hideGridlines", (json != null && json.has("HIDEGRIDLINES")) ? Boolean.valueOf(json.getString("HIDEGRIDLINES")) : false);
                        request.setAttribute("hideFormulabar", (json != null && json.has("HIDEFORMULABAR")) ? Boolean.valueOf(json.getString("HIDEFORMULABAR")) : false);
                        request.setAttribute("hideHeaders", (json != null && json.has("HIDEHEADERS")) ? Boolean.valueOf(json.getString("HIDEHEADERS")) : false);
                        
                        
                        request.setAttribute(JSONConstants.RANGE_ID, encurl);
                        request.getRequestDispatcher("gridView.do").forward(request, response);   //NO I18N
                        return null;
                    }
                }
            }

        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Exception in PublishRangeAction.java :: ", e);
            request.setAttribute(Constants.PUB_RANGE, false);
        }
        return "continue";//No I18N
    }
}
