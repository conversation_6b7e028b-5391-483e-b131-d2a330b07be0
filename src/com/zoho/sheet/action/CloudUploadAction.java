/* $Id$ */
/**
 * 
 */
package com.zoho.sheet.action;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.ErrorCode;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.accounts.AccountsConstants;
import com.zoho.sheet.conversion.Exporter;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.RemoteUtils;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.Charset;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR>
 *
 */
public class CloudUploadAction extends StrutsRequestHandler {
	 public final static Logger LOGGER = Logger.getLogger(CloudUploadAction.class.getName());
	 public String execute() throws Exception {
		 String folderId 		= request.getParameter("folderId"); 		// NO I18N
		 String source 			= request.getParameter("source"); 		// NO I18N
		 String documentName 	= request.getParameter("docName"); 		// NO I18N
         String versionNumber   = request.getParameter("versionNo");        // NO I18N
		 String accountId 		= request.getParameter("accountId");
         String teamDriveID 		= request.getParameter("teamDriveId");
		 byte[] contentBytes = null;
		 ContentType contentType = null;
		 JSONObjectWrapper responseJson = new JSONObjectWrapper();

         try {
        	 		
	        	 	boolean canDownload = true;
	 			org.json.JSONObject previlages = null;
	 			WorkbookContainer container = CurrentRealm.getContainer();
	 			
	 			if(request.getAttribute(ZFSNGConstants.CAPABILITIES) != null){
					previlages = new org.json.JSONObject(((String) request.getAttribute(ZFSNGConstants.CAPABILITIES)));
					canDownload = previlages.getBoolean("canDownload");				
				}
	 			
	 			if (!canDownload) {
					LOGGER.info("[CONVERSION:EXPORT] Export is not allowed for this user. rid : "+container.getResourceId());// TODO: handle exception
					response.setContentType("text/plain");
					response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
					return null;
				}
	 			
                if(documentName != null && !documentName.isEmpty()) {
                	documentName = Utility.getDecodedString(documentName);
                }
                
	 			String sheetVersionNo = null;
                if(versionNumber != null) {
                    String zfsngVersionId = ZohoFS.getVersionIDForEditor(container.getDocsSpaceId(), container.getResourceKey(), versionNumber);
                    sheetVersionNo = DocumentUtils.getVersionNoforZFSNGVersion(container.getDocId(), container.getDocOwner(), zfsngVersionId);                    
//                    exporter.setSheetVersionNo(sheetVersionNo);
                }
                Exporter exporter = new Exporter(container,".xlsx", sheetVersionNo, versionNumber);	//No i18N
                exporter.allowSave(false);
                contentBytes = exporter.getXLSXBytes();

                contentType = ContentType.create("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");	//No i18N
                String gadgetUrl 	= "https://"+EnginePropertyUtil.getSheetPropertyValue("ZohoGadgetsURL");	//No i18N
                String postURL;

                switch (source){
                    case JSONConstants.GADGET_GDRIVE_KEY:
                        postURL = "/zgsyncoauth.do"; //No I18N
                        break;

                    case JSONConstants.GADGET_DROPBOX_KEY:
                        postURL = "/api/dropbox/v1/upload"; //No I18N
                        break;

                    case JSONConstants.GADGET_TEAMDRIVE_KEY:
                        postURL = "/api/google/v1/drive/teamdrives/"+teamDriveID+"/folders/"+folderId+"/file"; //No I18N
                        break;

                    default:
                        postURL = "/clouduploader.do"; //No I18N

                }
                
                MultipartEntityBuilder builder = MultipartEntityBuilder.create();
                Charset UTF8Charset = Charset.forName("UTF-8");	//No i18N
                builder.addBinaryBody("datafile", contentBytes, contentType, documentName);	//No i18N  
                builder.addPart("ticket", new StringBody(IAMUtil.getCurrentTicket(), ContentType.TEXT_PLAIN )); // No I18N
                builder.addPart("zservice", new StringBody("zsheet", ContentType.TEXT_PLAIN ));	//No i18N

             switch (source){
                 case JSONConstants.GADGET_DROPBOX_KEY:
                     builder.addPart("accountId", new StringBody(accountId, ContentType.TEXT_PLAIN));	//No i18N
                     builder.addPart("response_type", new StringBody("gadgets", ContentType.TEXT_PLAIN));	//No i18N
                     builder.addPart("folderPath", new StringBody(folderId, ContentType.TEXT_PLAIN));	//No i18N
                     builder.addTextBody("fileName", documentName+".xlsx", ContentType.create("text/plain", UTF8Charset));	//No i18N
                     builder.addPart("source", new StringBody(source, ContentType.TEXT_PLAIN ));	//No i18N
                     break;
                 case JSONConstants.GADGET_TEAMDRIVE_KEY:
                     builder.addTextBody("fileName", documentName+".xlsx", ContentType.create("text/plain", UTF8Charset));	//No i18N
                     builder.addTextBody("uploadType", "multipart", ContentType.TEXT_PLAIN);	//No i18N
                     builder.addPart("response_type", new StringBody("gadgets", ContentType.TEXT_PLAIN));	//No i18N
                     break;
                 default:
                     builder.addPart("source", new StringBody(source, ContentType.TEXT_PLAIN ));	//No i18N
                     builder.addPart("folderId", new StringBody(folderId, ContentType.TEXT_PLAIN));	//No i18N
                     builder.addTextBody("docName", JSONConstants.GADGET_BOX_KEY.equals(source) || JSONConstants.GADGET_SKYDRIVE_KEY.equals(source) ? documentName+".xlsx" : documentName, ContentType.create("text/plain", UTF8Charset));	//No i18N
                     builder.addPart("mode", new StringBody("upload", ContentType.TEXT_PLAIN ));	//No i18N

             }
                
                HttpEntity httpEntity = null;
                if ( builder != null ) {
                			builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
                			builder.setCharset(Charset.forName("UTF-8"));	//No i18N
                			httpEntity = builder.build();
                }
                
                gadgetUrl += postURL;
//                ZSConnection zsconn = (new ZSConnectionFactory(gadgetUrl, "GET")).getConnection();		//No i18N
//                if(gadgetUrl.startsWith("https")) {	
//					zsconn.setSSLFactoryType("SSL");	//No i18N
//				}
//
//				zsconn.process();
//				int responseCode = zsconn.getResponseCode();
				
                HttpPost postMethod = new HttpPost(gadgetUrl);
                if ( httpEntity != null ) {
                    postMethod.setEntity(httpEntity);
                } 
                
               postMethod.setHeader("Z-SIGNED_REMOTE_USER_IP", IAMUtil.generateRemoteUserIPSignature(IAMUtil.getCurrentRequest()));//No i18N
               postMethod.setHeader(AccountsConstants.Z_USERAGENT, IAMUtil.getUserAgent(IAMUtil.getCurrentRequest()));
               String _X_MDM_Token = RemoteUtils.maskNull(request.getHeader(Constants.X_MDM_TOKEN));
               if(_X_MDM_Token!=null){
                 postMethod.setHeader(Constants.X_MDM_TOKEN,_X_MDM_Token);
               }
               CloseableHttpClient httpClient = null;               
   
               HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
               httpClient = httpClientBuilder.build();
               CloseableHttpResponse finalResponse = httpClient.execute(postMethod);
                
               int statusCode = finalResponse.getStatusLine().getStatusCode();
               HttpEntity entity = finalResponse.getEntity();
               JSONObjectWrapper responseObj = new JSONObjectWrapper(EntityUtils.toString(entity, "UTF-8"));// No i18N
               LOGGER.log(Level.INFO, "[CLOUD_UPLOAD][Exception] Export file to cloud drive response : {0} ", new Object[] {responseObj});
               if(statusCode == 200 || statusCode == 201) {
            	   		responseJson.put("status", "success");   	//No i18N         
                        HttpServletResponseWrapper.sendResponse(response, responseJson, HttpServletResponseWrapper.MimeType.JSON);
               } else {
            	   		LOGGER.log(Level.WARNING, "[CLOUD_UPLOAD][Exception] Cannot export file to cloud drive. ResponseCode is not 200 : {0}, documentName : {1}, statusCode : {2} ", new Object[] {source, documentName, statusCode});
            	   		responseJson.put("status", "error");   	//No i18N         
                        HttpServletResponseWrapper.sendResponse(response, responseJson, HttpServletResponseWrapper.MimeType.JSON);
               }
         }
		 catch(ProhibitedActionException e)
		 {
			 LOGGER.log(Level.WARNING, "[CLOUD_UPLOAD][Exception] Exception occurred while uploading file into cloud source : " + source + ", documentName : " + documentName, e);
			 response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
			 JSONObjectWrapper errObj = ErrorCode.getProhibitedDialogErrorMsg(e);
			 errObj.put("status", "error"); // No i18N
             HttpServletResponseWrapper.sendResponse(response, responseJson, HttpServletResponseWrapper.MimeType.JSON);
		 }
		 catch(Exception e)
         {
	         LOGGER.log(Level.WARNING, "[CLOUD_UPLOAD][Exception] Exception occurred while uploading file into cloud source : " + source + ", documentName : " + documentName, e);
	         responseJson.put("status", "error");            //No i18N
             HttpServletResponseWrapper.sendResponse(response, responseJson, HttpServletResponseWrapper.MimeType.JSON);
         }
		 return null;
	 }
}
