/*$Id$*/
package com.zoho.sheet.action;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.logs.common.util.logging.OnDemandSearchLogger;
import com.zoho.sheet.chartengine.ChartEngineManager;
import com.zoho.sheet.util.*;
import com.zoho.sheet.util.WidgetReader.Task;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceStatus;
import com.zoho.zfsng.constants.ResourceType;
import com.zoho.zfsng.constants.TeamConstants;
import com.zoho.zfsng.constants.ZFSNGConstants;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 *
 */
/*
 * PURPOSE :    Responsible for openDocument for new client
 *
 */
public class HOpenDocumentAction extends HDocumentRenderer {

    public static Logger logger = Logger.getLogger(HOpenDocumentAction.class.getName());

    @Override
    protected DocumentRenderPolicy getPolicy(HttpServletRequest request) {

        DocumentRenderPolicy renderPolicy = new DocumentRenderPolicy();
        renderPolicy.allowForwarding = true;
        renderPolicy.allowPostProcess = true;
        renderPolicy.checkFileFormat = true;
        renderPolicy.fetchCommentCount = true;
        renderPolicy.fetchFreezeInfo = true;
        renderPolicy.fetchWidgetInfo = true;
        renderPolicy.redirectExport = true;
        renderPolicy.regenerateChart = true;
        renderPolicy.migratetoDFS = true;
        renderPolicy.allowGoToReference = true;
        return renderPolicy;
    }

    @Override
    protected void updateOnLoadResponse(HttpServletRequest request) throws Exception {
        FetchDataAction.updateOnLoadResponse(request, false);
    }

    @Override
    protected void postProcessor(HttpServletRequest request, WorkbookContainer container, UserProfile userProfile) throws Exception {

        boolean allowToWrite = !(container.isTrashed() || userProfile.getPermissionType() == UserProfile.PermissionType.READ || userProfile.getPermissionType() == UserProfile.PermissionType.READ_COMMENT);

        User currentUser = IAMUtil.getCurrentUser();
        boolean isLoggedIn = (currentUser != null);
        String viewType = container.isTrashed() ? Constants.TRASH_VIEW : userProfile.getUserName().equals(container.getDocOwner()) ? Constants.OPEN_DOC_VIEW : Constants.SHARE_VIEW;
        String isDevMode = EnginePropertyUtil.getSheetPropertyValue("isDevelopmentMode");       // NO i18N
        String mode = request.getParameter("mode");     // NO i18N
        Boolean isHandhelds = mode != null && (mode.equals("android") || mode.equals("ios"));       // NO i18N
        boolean isGdriveDoc = container.isGdriveDoc();

        String zuid = userProfile.getZUserId();

        boolean isPreview = false;
        boolean isPrintPreview = false;
        if (request.getAttribute("isPreview") != null) {
            isPreview = (boolean) request.getAttribute("isPreview");
            isPrintPreview = (boolean) request.getAttribute("isPrintPreview");
        }
        boolean isTemplate = container.isTemplate();

        boolean iscdview = false;
        if("cdview".equals(request.getParameter("zview"))) {
            iscdview = true;
        }

        if (request.getAttribute("cdview") != null) {
            iscdview = Boolean.parseBoolean((String) request.getAttribute("cdview"));
        }

        if(request.getParameter("version") != null) {
            iscdview = true;
        }
        boolean isOwner = (zuid.equals(container.getDocOwnerZUID()));

        //Adding the user's ZUID to ORDERED_USERS_LIST in additionalResourceInfo if it is the user's first open
        if (currentUser != null && allowToWrite) {                         // for link share case we dont need to add user in list
            container.updateOrderedAuthorsList(zuid);
        }

        logger.log(Level.INFO, "CAPABILITIES::{0} ,  IS_EXTERNAL_SHARE ::  {1}",new Object[]{request.getAttribute(ZFSNGConstants.CAPABILITIES), request.getAttribute(ZFSNGConstants.IS_EXTERNAL_SHARE)} );

        if (!isHandhelds) {

            List<Integer> hideList = new ArrayList<>();
            ArrayList disabledList = new ArrayList();
            String folderName = "";         // NO i18N

            org.json.JSONObject previlages = new org.json.JSONObject(((String) request.getAttribute(ZFSNGConstants.CAPABILITIES)));
            boolean isTeamDrive = (boolean) request.getAttribute(ZFSNGConstants.IS_TEAM_RESOURCE);
            boolean isExternalShare = false;
            boolean isNonZohoUser = false;
            boolean isPublicLinkShare = false;

            if (request.getAttribute(ZFSNGConstants.IS_EXTERNAL_SHARE) != null) {
                isExternalShare = (boolean) request.getAttribute(ZFSNGConstants.IS_EXTERNAL_SHARE);
            }
            
            if (request.getAttribute(ZFSNGConstants.IS_NON_ZOHO_USER) != null) {
            	isNonZohoUser = (boolean) request.getAttribute(ZFSNGConstants.IS_NON_ZOHO_USER);
            }
            
            if (request.getAttribute(ZFSNGConstants.ISONLY_SHARED_PUBLIC) != null) {
                isPublicLinkShare = (boolean) request.getAttribute(ZFSNGConstants.ISONLY_SHARED_PUBLIC);
                request.setAttribute("IS_PUBLIC_LINK_SHARE", isPublicLinkShare);
                OnDemandSearchLogger.log(logger,Level.INFO,"IS_PUBLIC_LINK_SHARE ...{0}", request.getAttribute(ZFSNGConstants.ISONLY_SHARED_PUBLIC));
            }

            try {
                //Zillum Check Start

                if(isTeamDrive && currentUser != null) {
                    String orgDetails = ZohoFS.getOrgDetails(zuid, container.getResourceId());
                    if(orgDetails != null){
                        JSONObjectWrapper orgInfo = new JSONObjectWrapper(orgDetails);
                        if(orgInfo.has("EDITION")){
                            request.setAttribute(Constants.IS_WD_PERSONAL_EDITION, orgInfo.getString("EDITION").equals(TeamConstants.USEREDITION_PERSONAL));
                        }
                        if(orgInfo.has("ORG_ID")){
                            String orgId = orgInfo.getString("ORG_ID");
                            JSONObjectWrapper licenseInfo = WorkDriveInfoUtils.getOrgLicenseInfo(orgId);
                            if(licenseInfo.has("plan_type") && licenseInfo.getInt("plan_type") == 21) { //Zillum plan check
                                hideList.add(FeatureConstants.SAVE_AS_MERGE_TEMPLATE);
                                hideList.add(FeatureConstants.MERGE_FIELDS);
                                hideList.add(FeatureConstants.IMPORT_FROM_CLOUD_DRIVES);
                                hideList.add(FeatureConstants.EXPORT_TO_CLOUD_DRIVES);
                                request.setAttribute(Constants.IS_ZILLUM_PLAN, true);
                            }
                        }
                    }
                }

                //Zillum Check End
            }catch (Exception e){
                logger.log(Level.WARNING,"Exception while checking Zillum Plan {0}", e);
            }

            boolean haveWriteSaveSharePermission = false;
            if (Constants.ISFUJIXEROX) {
                hideList.add(FeatureConstants.DELUGE);
                hideList.add(FeatureConstants.CREATE_FORM);
            }

            String _isSendAsAttachmentEnabledProp = RemoteUtils.maskNull(EnginePropertyUtil.getSheetPropertyValue("isSendAsAttachmentEnabled"));// NO I18N
            boolean _isSendAsAttachmentEnabled = _isSendAsAttachmentEnabledProp == null ? false : "true".equals(_isSendAsAttachmentEnabledProp);
            boolean _isNotConfirmedUser = false;
            if ( currentUser!= null && !currentUser.isConfirmed()) {
                _isNotConfirmedUser = true;
            }
            if (_isSendAsAttachmentEnabled) {
                if (!previlages.getBoolean("canDownload")) {
                    disabledList.add(FeatureConstants.SEND_AS_ATTACHMENT);
                }else {
                    if( _isNotConfirmedUser || isExternalShare || isNonZohoUser) {
                        hideList.add(FeatureConstants.SEND_AS_ATTACHMENT);
                    }
                }
            } else {
                hideList.add(FeatureConstants.SEND_AS_ATTACHMENT);
            }

            int status = 0;
            String libInfo = null;
            int libtype = ResourceType.DOC_LIBRARY;

            try {
                status = ZohoFS.getResourceStatus(container.getDocsSpaceId(), container.getResourceId());
                libInfo = ZohoFS.getLibraryInfo(container.getDocsSpaceId(), container.getResourceId());

                int resourceType = ZohoFS.getResourceInfo(container.getResourceId()).getResourceType();
                OnDemandSearchLogger.log(logger,Level.INFO,"after zohofs call :: resourceType : {0}", resourceType);
                request.setAttribute("RESOURCE_TYPE", resourceType);
            } catch (Exception e) {
                logger.log(Level.WARNING, "Problem while getting ZohoFS ResourceInfo or resource type", e);
            }

            if (request.getAttribute(ZFSNGConstants.LIBTYPE) != null) {
                libtype = (Integer) request.getAttribute(ZFSNGConstants.LIBTYPE);
                OnDemandSearchLogger.log(logger,Level.INFO,"LIBTYPE FROM REQUEST::" + libtype);
            } else {
                JSONObjectWrapper libObj = new JSONObjectWrapper(libInfo);
                libtype = (Integer) libObj.get("LIBRARY_TYPE");
            }

            boolean canDownload = previlages.getBoolean("canDownload");
            boolean canCopy = previlages.getBoolean("canCopy");
            boolean canMove = previlages.getBoolean("canMove");
            boolean canRename = previlages.getBoolean("canRename");
            boolean canShare = previlages.getBoolean("canShare");
            boolean canPublish = previlages.getBoolean("canPublish");
            boolean canOrgPublish = previlages.getBoolean("canOrgPublish");
            boolean canFavorite = previlages.getBoolean("canFavourite");
            boolean canCreateComment = previlages.getBoolean("canCreateComment");
            boolean canCheckin = previlages.getBoolean("canCheckin");
            boolean canCheckout = previlages.getBoolean("canCheckout");
            boolean canEdit = previlages.getBoolean("canEdit");
            boolean canMarkAsFinal = previlages.getBoolean("canMarkAsFinal");

            boolean isAdmin = previlages.getBoolean("isAdmin"); //No I18N 
            boolean canWrite = previlages.getBoolean("canEdit"); //No I18N
            boolean canRead = previlages.getBoolean("canRead"); //No I18N
            boolean canTrash = previlages.getBoolean("canTrash"); //No I18N

            JSONObjectWrapper finalInfo = null;
            if(canMarkAsFinal || canCheckin && canEdit) {
                finalInfo = DocFinalAction.getDocFinalInfoWithTime(container);
                request.setAttribute("FINAL_INFO", finalInfo.toString());
            }

            if (!canDownload) {
                disabledList.add(FeatureConstants.EXPORT);
                disabledList.add(FeatureConstants.SEND_AS_ATTACHMENT);
                hideList.add(FeatureConstants.EXPORT_TO_CLOUD_DRIVES);
                disabledList.add(FeatureConstants.SAVEAS);
                disabledList.add(FeatureConstants.MAKE_A_COPY);
                disabledList.add(FeatureConstants.PRINT);
            }
            if(!canTrash) {
                disabledList.add(FeatureConstants.MOVE_TO_TRASH);
            }
            if (!canCopy) {
                disabledList.add(FeatureConstants.SAVEAS);
                disabledList.add(FeatureConstants.MAKE_A_COPY);
            }

            if (!canMove) {
                disabledList.add(FeatureConstants.MOVE_TO_FOLDER);
            }

            if (isTeamDrive) {

                if (libtype == ResourceType.MYSPACE_LIBRARY || (status != ResourceStatus.DRAFT && status != ResourceStatus.INITIATE)) {
                    hideList.add(FeatureConstants.DRIVE_STATUS);
                }

                boolean isWorkFlowEnabledOrg = WorkDriveInfoUtils.isWorkFlowEnabledResourceId(container.getResourceId());

                if(!isWorkFlowEnabledOrg) {
                    hideList.add(FeatureConstants.WORKFLOW);
                }
                request.setAttribute("WD_ORG_ID", WorkDriveInfoUtils.getOrgId(container.getResourceId()));
                hideList.add(FeatureConstants.CREATE_FORM);
                hideList.add(FeatureConstants.PROPERTIES);
                hideList.add(FeatureConstants.SAVE_TO_MYACCOUNT);

            } else {

                hideList.add(FeatureConstants.DRIVE_STATUS);
                if (!isOwner) {
                    hideList.add(FeatureConstants.DELUGE);
                }
                hideList.add(FeatureConstants.SAVE_TO_MYACCOUNT);
            }
            String spaceId = (String) request.getAttribute(ZFSNGConstants.SPACE_ID);
            if(!spaceId.equals(zuid)) {
                hideList.add(FeatureConstants.MANAGE_FORM);
            }
            String lockedBy = userProfile.getZUserId();
            String lockedStatus = "false";
            if (request.getAttribute(ZFSNGConstants.LOCK_STATUS) != null) {
                lockedStatus = (String) request.getAttribute(ZFSNGConstants.LOCK_STATUS);
            }
            if(!canEdit) {
                request.setAttribute(Constants.DEFAULT_READ_USER, true);
            }else {
                request.setAttribute(Constants.DEFAULT_READ_USER, false);
            }
            if ("true".equals(lockedStatus)) {
                lockedBy = (String) request.getAttribute(ZFSNGConstants.LOCKED_BY);
                if(isTeamDrive){
                    if(!lockedBy.equals(zuid)) {
                        String role = WorkDriveInfoUtils.getSharedRoleForUser(container.getResourceId(), zuid);
                        /* ROLE9 is FileShare - Edit permission and ROLE10 is FileShare - Share Permission */
                        /* ROLE5 is FolderShare - Organize permission , ROLE3 is FolderShare - Edit Permission */
                        /* ROLE1 is Workspace - Admin permission , ROLE2 is Workspace - Organizer Permission and ROLE3 is Workspace - Editor Permission*/
                        if(role != null) {
                            if(role.equals("ROLE9") || role.equals("ROLE10") || role.equals("ROLE5") || role.equals("ROLE3") || role.equals("ROLE1") || role.equals("ROLE2")) {
                                request.setAttribute(Constants.DEFAULT_READ_USER, false);
                            }
                        }
                    }else{
                        request.setAttribute(Constants.DEFAULT_READ_USER, false);
                    }
                }
            }

            JSONObjectWrapper activeWorkFlows = WorkDriveInfoUtils.getWorkflowInfo(container.getResourceId());

            if (!lockedBy.equals(userProfile.getZUserId())) {

                folderName = "READ_ONLY";    // NO i18N

            } else if (previlages.getBoolean("canEdit") || canMarkAsFinal) {

                folderName = "OWNER";           // NO i18N
                haveWriteSaveSharePermission = true;

                if (!canRename) {
                    hideList.add(FeatureConstants.RENAME);
                }

                if (!canShare && !isTemplate) {
                    hideList.add(FeatureConstants.LOCK_CELLS);
                    haveWriteSaveSharePermission = false;
                }

                if (!canPublish) {
                    hideList.add(FeatureConstants.CHART_PUBLISH);
                }

                if (!canOrgPublish && !canPublish) {
                    hideList.add(FeatureConstants.PUBLISH);
                    hideList.add(FeatureConstants.PUBLISH_RANGE);
                }

                if (!canCheckin && !canCheckout && (finalInfo != null && !finalInfo.getBoolean(JSONConstants.FINAL_STATUS))) {
                    hideList.add(FeatureConstants.CHECKIN_CHECKOUT);
                }

                if (!isOwner) {
                    hideList.add(FeatureConstants.CREATE_FORM);
                }

            } else if(!activeWorkFlows.isEmpty() && canShare) {
                folderName = "WORKFLOW_VIEW";    // NO i18N
            } else {

                folderName = "READ_COMMENT";    // NO i18N
            }
            
            if(isPublicLinkShare || isExternalShare || isNonZohoUser) {
                hideList.add(FeatureConstants.SHEET_PROPERTIES);
                hideList.add(FeatureConstants.NOTIFY_COLLABORATOR);
            }
            
            if (isExternalShare || isNonZohoUser) {
                hideList.add(FeatureConstants.OPEN);
                hideList.add(FeatureConstants.NEW_FILE);
                hideList.add(FeatureConstants.MY_TEMPLATES);
                hideList.add(FeatureConstants.SAVEAS);
                hideList.add(FeatureConstants.MAKE_A_COPY);
                hideList.add(FeatureConstants.IMPORT_FROM_CLOUD_DRIVES);
                hideList.add(FeatureConstants.IMPORT);
                hideList.add(FeatureConstants.SIGNIN_SIGNUP);
                hideList.add(FeatureConstants.COLLABORATORS);
                hideList.add(FeatureConstants.COPY_SHEET);
                hideList.add(FeatureConstants.PASTE_SHEET);
                hideList.add(FeatureConstants.SHARE);
                hideList.add(FeatureConstants.RENAME);
                hideList.add(FeatureConstants.AUDIT_TRAIL);
                hideList.add(FeatureConstants.CREATE_VERSION);
                hideList.add(FeatureConstants.VIEW_VERSION);
                hideList.add(FeatureConstants.CELL_EDIT_HISTORY);
                hideList.add(FeatureConstants.SPREADSHEET_SETTINGS);
                hideList.add(FeatureConstants.DELUGE);
                hideList.add(FeatureConstants.DATA_CONNECTION);
                hideList.add(FeatureConstants.LINK_SPREADSHEET);
                hideList.add(FeatureConstants.MOVE_TO_TRASH);
            }

			if (isNonZohoUser) { //for non zoho users - spreadsheet shared to nonZoho user and accessed via otp case
                hideList.add(FeatureConstants.COLLAB_CHAT);
                hideList.add(FeatureConstants.CHART_PUBLISH);
                hideList.add(FeatureConstants.PUBLISH);
                hideList.add(FeatureConstants.PUBLISH_RANGE);
                hideList.add(FeatureConstants.MERGE_FIELDS);
                hideList.add(FeatureConstants.OPENAI);
                hideList.add(FeatureConstants.CELL_EDIT_HISTORY);
                hideList.add(FeatureConstants.MOVE_TO_FOLDER);
                hideList.add(FeatureConstants.SPELLING);
			}
			
			

            if (!canFavorite) {
                hideList.add(FeatureConstants.FAVORITE);
            }

            if (!canCreateComment) {
                hideList.add(FeatureConstants.COMMENTS);
            }

            if (isGdriveDoc) {

                hideList.add(FeatureConstants.NEW_FILE);
                hideList.add(FeatureConstants.OPEN);
                hideList.add(FeatureConstants.MY_TEMPLATES);
                hideList.add(FeatureConstants.SAVE_TO_MYACCOUNT);
                hideList.add(FeatureConstants.IMPORT_FROM_CLOUD_DRIVES);
                hideList.add(FeatureConstants.IMPORT);
                hideList.add(FeatureConstants.SPREADSHEET_SETTINGS);
                hideList.add(FeatureConstants.CREATE_FORM);

                hideList.add(FeatureConstants.PROPERTIES);
                hideList.add(FeatureConstants.MOVE_TO_FOLDER);
                hideList.add(FeatureConstants.SIGNIN_SIGNUP);
                hideList.add(FeatureConstants.COLLABORATORS);

                hideList.add(FeatureConstants.FAVORITE);
                hideList.add(FeatureConstants.SHARE);
                hideList.add(FeatureConstants.PUBLISH);
                hideList.add(FeatureConstants.CHART_PUBLISH);
                hideList.add(FeatureConstants.PUBLISH_RANGE);
                hideList.add(FeatureConstants.CHECKIN_CHECKOUT);
                hideList.add(FeatureConstants.DATA_CONNECTION);
            }

            UserProfile.PermissionType role = userProfile.getPermissionType();

            if (iscdview) {
                folderName = canEdit ? "VERSION_READ_WRITE" : "VERSION_READ_ONLY"; // NO i18N
                request.setAttribute("cdview", "true");
            }
            
            if(request.getAttribute(ZFSNGConstants.IS_CUSTOM_DOMAIN_ENABLED) != null){
                OnDemandSearchLogger.log(logger,Level.INFO,"[CustomDomains]::zuid::{0}::",request.getAttribute(ZFSNGConstants.IS_CUSTOM_DOMAIN_ENABLED));
                request.setAttribute("customDomains", request.getAttribute(ZFSNGConstants.IS_CUSTOM_DOMAIN_ENABLED).toString());
            }

            if (libtype == ResourceType.CRM_LIBRARY) {

                request.setAttribute("isCRMLibResource", true);

                OnDemandSearchLogger.log(logger,Level.INFO,"CRM_LIBRARY::canShare::{0}isAdmin::{1}", new Object[]{canShare, isAdmin});

                lockedStatus = "false";
                lockedBy = userProfile.getZUserId();
                if (request.getAttribute(ZFSNGConstants.LOCK_STATUS) != null) {
                    lockedStatus = (String) request.getAttribute(ZFSNGConstants.LOCK_STATUS);
                }

                if ("true".equals(lockedStatus)) {
                    lockedBy = (String) request.getAttribute(ZFSNGConstants.LOCKED_BY);
                }
                if (!lockedBy.equals(userProfile.getZUserId())) {
                    folderName = "WORKSPACE_READ_ONLY"; //NO I18N
                } else {
                    if (canShare || isAdmin) {
                        folderName = "WORKSPACE_READ_WRITE_SHARE"; //NO I18N
                        hideList.remove((Object) FeatureConstants.LOCK_CELLS);
                        haveWriteSaveSharePermission = true;
                    } else if (canWrite) {
                        folderName = "WORKSPACE_READ_WRITE"; //NO I18N
                    } else if (canRead) {
                        folderName = "WORKSPACE_READ_ONLY"; //NO I18N
                    }
                }
            }


            if (isTemplate) {
                if(previlages.getBoolean("canEdit")) {
                    folderName = "TEMPLATE";        // NO i18N
                }else {
                    folderName = "TEMPLATE_READ_ONLY";        // NO i18N
                }

            }

            if(isPreview && isPrintPreview){
                folderName = "PRINT_PREVIEW"; //NO I18N
            }
            else if (isPreview) {
                folderName = "PREVIEW"; //NO I18N
            }

            if (Constants.IS_DISASTER_RECOVERY_SERVER) {

                folderName = "READ_ONLY";       // No i18N
            }

            int libType = ResourceType.WORKBOOK_NATIVE;
            if (request.getAttribute(ZFSNGConstants.LIBTYPE) != null) {
                libType = (Integer) request.getAttribute(ZFSNGConstants.LIBTYPE);
            }
            int resourceType = ResourceType.WORKBOOK_NATIVE;
            if (request.getAttribute(ZFSNGConstants.RESOURCE_TYPE) != null) {
                resourceType = (Integer) request.getAttribute(ZFSNGConstants.RESOURCE_TYPE);
            }
            if(resourceType == ResourceType.WORKBOOK_NON_NATIVE  && ( libType != ResourceType.CRM_LIBRARY && libType != ResourceType.WS_TEMPLATE_LIBRARY)) {
                folderName="PREVIEW"; // No i18N
            }

            
            FeatureController.getHideList(container, allowToWrite, zuid, isLoggedIn, folderName, !haveWriteSaveSharePermission, hideList);

            if ("no".equals(request.getParameter("top")) && !"gridv1".equals(request.getParameter("zview"))) { //temp added. should be removed once cliq updated the preview url.
                hideList.add(FeatureConstants.TOP_BAR);
            }

            FeatureController.addFeatureListToAllView(hideList);

            boolean isDarkModeEnabled = ClientUtils.isFeatureEnabled("isDarkModeEnabled", "darkmodeEnablingOrgIds");    // NO i18N
//            boolean isSysDefaultModeEnabled = ClientUtils.isFeatureEnabled("isSysDefaultModeEnabled",null);    // NO i18N
            JSONObjectWrapper userJson = isLoggedIn ? UserSettings.get(currentUser, UserSettings.Settings.VIEW) : new JSONObjectWrapper();
            String isDark = null;
            Boolean isCraftGridView = (Boolean) request.getAttribute("isCraftGrid");
            String view = request.getParameter("zview");
            if("slice".equalsIgnoreCase(view) || request.getParameter("table_id") != null) {
                if(previlages.getBoolean("canEdit")) {
                    folderName = "SLICE_GRID";  //NO I18N
                } else {
                    folderName = "SLICE_GRID_RO";  //NO I18N
                }
            }

            if("gridv1".equalsIgnoreCase(view)) {
                folderName = "CRAFT_GRID_WTB"; //NO I18N
                request.setAttribute("isCraftGrid", false);
                hideList.add(FeatureConstants.DATA_CLEANING);
                hideList.add(FeatureConstants.ZIA);
                isDark = request.getParameter("isDarkMode");
            }

            if("grid".equalsIgnoreCase(view)) {
                folderName = "CRAFT_GRID"; //NO I18N
                request.setAttribute("isCraftGrid", true);
            }


            if (isDarkModeEnabled && userJson.has("drk") && !"grid".equalsIgnoreCase(view) && !"gridv1".equalsIgnoreCase(view) && !"slice".equalsIgnoreCase(view) && (null == isCraftGridView || false == isCraftGridView)) {
                isDark = (String) userJson.get("drk");
            }
//            if("sysDefault".equals(isDark) && !isSysDefaultModeEnabled){
//                isDark = "false"; //No I18N
//            }

            Boolean isOverrideBrowser = true;
            if(userJson.has("ovrbs")) {
                String value = (String) userJson.get("ovrbs");
                isOverrideBrowser = Boolean.parseBoolean(value);
            }

            request.setAttribute("IS_THIRD_GEN_CHART_ENABLED", ChartEngineManager.getIsThirdGenChartEnabled(container));            // NO I18N
            request.setAttribute("HIDE_FEATURE_LIST", hideList);
            request.setAttribute("DISABLE_FEATURE_LIST", disabledList);
            request.setAttribute(Constants.VIEW, viewType);     //OPEN, TRASH, SHARED
            request.setAttribute(Constants.ALLOW_TO_WRITE, allowToWrite);
            request.setAttribute("USER_LOCALE", I18NUtil.getLocaleAsString(request));
            request.setAttribute("IS_DEV_MODE", isDevMode);
            request.setAttribute("FOLDER", folderName);
            request.setAttribute("ANONUSER", userProfile.getZUserId());     //From Wms//No I18N
            request.setAttribute(Constants.IS_TEAM_RESOURCE, String.valueOf(isTeamDrive));
            request.setAttribute("DARKMODE", isDark);
            request.setAttribute("OVERRIDE_BROWSER_SHORTCUT", isOverrideBrowser);

            if (isLoggedIn) {
                // To check walkthrough is viewed or not
                request.setAttribute("isWalkthroughViewed", true);
                // To check whether the newversion tour ripple is viewed or not
                request.setAttribute("isTourRippleViewed", ClientUtils.getTourRippleStatus(zuid));

                //To check whether the user seen the newly added feature announcement or not
                request.setAttribute("isNewAnnouncementViewed", ClientUtils.getNewAnnouncementViewStatus(zuid));
            }

        }
    }

    @Override
    protected String getForwardName(HttpServletRequest request) {
        String forwardName = "continue";	//No I18N
        String mode = request.getParameter("mode");
        String zview = request.getParameter("zview");
        String version = request.getParameter("version");
//        String version = request.getParameter("version");
        logger.info("versionversion"+request.getAttribute("version"));
        if ("read".equals(mode)) {
            forwardName = "readonly";	//No I18N
        } else if ("android".equals(mode)) {
            forwardName = "androidhandler";		//No I18N
        } else if ("version".equals(mode) || "cdview".equals(zview) || version != null ){
            forwardName = "versionview";        //No I18N
        }
        return forwardName;
    }

    protected WidgetReaderObtainer obtainWidgetReaderObtainer(WorkbookContainer container, UserProfile userProfile) throws Exception {
        return new WidgetReaderObtainerIBaseDocImpl(container, userProfile);
    }

    protected WidgetReader getWidgetReaderFromFactory(Task task) throws Exception {

        return WidgetReaderFactory.getIReader(new Task(true, true, true, true));
    }
}
