/* $Id$ */
package com.zoho.sheet.action;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ErrorCode.DisplayType;
import com.adventnet.zoho.websheet.model.ErrorCode.MsgType;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.HttpServletResponseWrapper.*;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.sheet.util.WebFormUtils;
import com.zoho.zfsng.client.ZohoFS;

import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.api.dataapi.DataAPIListener;
import java.util.logging.Level;
import java.util.logging.Logger;

public final class SheetAction extends StrutsRequestHandler {

        public final static Logger LOGGER = Logger.getLogger(SheetAction.class.getName());

        @Override
        public String execute() throws Exception {

                /*
                * All sheet related actions We can put every other actions here where
                * UNDO is not required and but workbook is modified TODO: these actions
                * too needs to be added in the queue
                 */
                
                LOGGER.log(Level.INFO, "--- sheet action ---");
                WorkbookContainer container = null;

                // TODO: need to change
                JSONObjectWrapper jObj = null;

                try {

                        container = CurrentRealm.getContainer();

                        String docOwner = container.getDocOwner();
                        UserProfile up = CurrentRealm.getUserProfile();
                        String zuid = up.getZUserId();

                        Workbook workbook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());

                        String resourceId = container.getResourceId();
                        String oldSheetName = request.getParameter("oldSheetName");
                        String newSheetName = request.getParameter("newSheetName");
                        String sheetName = oldSheetName;

                        if (newSheetName == null) {
                                String currentSheet = null;
                                //remove this check,once multirange in server side is ready
                                if (request.getParameter("sheetList") != null) {
                                        JSONArrayWrapper sheetList = JSONArrayWrapper.fromString(request.getParameter("sheetList"));
                                        currentSheet = ((JSONArrayWrapper) sheetList.getJSONArray(0)).getString(0);
                                } else {
                                        currentSheet = request.getParameter("currentSheet");
                                }
                                String sheetCode1 = currentSheet;
                                String currentSheetName1 = workbook.getSheetByAssociatedName(sheetCode1).getName();
                                sheetName = currentSheetName1;
                        }

                        String action = request.getParameter("action");

                        if (Integer.parseInt(action) == ActionConstants.SHEET_REMOVE) {
                                String docId = container.getDocId();
                                String sheetid = String.valueOf(DocumentUtils.getDocumentSheetId(Long.valueOf(docId), sheetName, docOwner, false));
                                JSONObjectWrapper formInfo = WebFormUtils.isSheetContainsForm(docId, sheetName, docOwner);

                                boolean isSheetContainsForm = (boolean) formInfo.get("isFormExists");
                                LOGGER.info("[FORMS] Is Sheet Contains Forms  while deleting sheet. >>  " + isSheetContainsForm);				//No I18N
                                if (isSheetContainsForm) 
                                {
                                        String formRid = request.getParameter("formRid");			//No I18N
                                        LOGGER.info("[FORMS]IS FORM DELETED >> " + formRid);//No I18N
                                        formRid = Utility.getDecodedString(formRid);
                                        String ownerZUID = container.getDocOwnerZUID();
                                        if (!ownerZUID.equals(String.valueOf(zuid))) 
                                        {
                                                throw new Exception("This sheet contains form, Which can be deleted by owner only");	//No I18N
                                        }
                                        if (formRid != null) 
                                        {
                                                LOGGER.info(" [FORMS]Form delete URL >> " + formRid);//No I18N

                                                boolean isFormDeleted = ZohoFS.deleteResource(ownerZUID, formRid, zuid);  //WebFormUtils.deleteForm(formRid);

                                                request.setAttribute("isFormDeleted", isFormDeleted);
                                                request.setAttribute(JSONConstants.FORM_RID, formRid);
                                                if (!isFormDeleted) 
                                                {
                                                        throw new Exception("Error While deleting the Forms");	//No I18N
                                                }
                                        }
                                }
                                if (!sheetid.equals("null")) 
                                {
                                        if (resourceId != null && !resourceId.equals("0")) 
                                        {
                                                DocumentUtils.UpdateorRemoveSheetBasedPermission(sheetid, container.getDocOwnerZUID(), resourceId);
                                        }
                                }
                        }
                        
                        jObj = ActionObject.getActionObject(request);

                        ContainerListener listener = null;
                        JSONObjectWrapper respJson = null;
                        int queueActionId = -1;

//                        if (!DocumentUtils.hasListenerProcessAccess(jObj)) //new client
//                        {
//                                queueActionId = container.addActionToQueue(jObj);
//                        } else {
                                try {
                                        // Passing as -1 as the ActionId will be set in addActionToQueue() method
                                        listener = new DataAPIListener(-1);
                                        synchronized (listener) {
                                                queueActionId = container.addActionToQueue(jObj, listener);

                                                try {
                                                        listener.wait(Constants.LISTENER_WAITING_TIME);
                                                } catch (InterruptedException ie) {
                                                        LOGGER.log(Level.INFO, "listener interupted");
                                                }
                                        }
                                        respJson = listener.getMessage();
                                } finally {
                                        container.removeListener(listener);
                                }
//                        }

                        if (queueActionId == -1) //error while adding action to queue
                        {
                                throw new Exception();
                        }

                        if (respJson == null) //timeout occurs and the action not executed
                        {
                                LOGGER.log(Level.INFO, "timeout case or non webbrowser case {0}" , queueActionId);
                                respJson = new JSONObjectWrapper();
                        }
                        long dateVal = System.currentTimeMillis();
                        respJson.put("timeStamp", dateVal);
                        respJson.put("qaid", queueActionId);
//                        response.getWriter().println(respJson); //NO OUTPUTENCODING
                        HttpServletResponseWrapper.sendResponse(response, respJson, MimeType.JSON);
                } catch (IllegalStateException ise) {
                        JSONObjectWrapper errObj = ErrorCode.getErrorMessage(
                                ErrorCode.ERROR_TOO_MANY_ACTIONS, null, MsgType.WARNING,
                                DisplayType.DIALOG);
                        HttpServletResponseWrapper.sendResponse(response, errObj, MimeType.JSON);
                } catch (Exception e) {
                        JSONObjectWrapper errObj;
                        String rid = container != null ? container.getResourceId() : "";
                        LOGGER.log(Level.INFO, "Exception caught in SheetAction: ", e);
                        if (e.getMessage() != null && ErrorCode.isDefined(e.getMessage())) {
                                if (e.getMessage().equals(ErrorCode.ERROR_SHEET_LIMIT_EXCEED) || e.getMessage().equals(ErrorCode.ERROR_SHEET_LOCKED)) {
                                        errObj = ErrorCode.getErrorMessage(e.getMessage(), null,
                                                MsgType.ERROR, DisplayType.DIALOG);
                                } else {
                                        errObj = ErrorCode.getErrorMessage(e.getMessage(), null, MsgType.ERROR, DisplayType.BANNER);
                                        LOGGER.log(Level.INFO, "[ZS_ERROR_BANNER][SHEET_ACTION][" + e.getMessage() + "][RID:" + rid + "][ URL:" + request.getRequestURL() + "]");
                                }
                                HttpServletResponseWrapper.sendResponse(response, errObj, MimeType.JSON);
                        } else {
                                errObj = ErrorCode.getErrorMessage(ErrorCode.ERROR_ADD_ACTION_TO_QUEUE, null, MsgType.ERROR, DisplayType.BANNER);
                                LOGGER.log(Level.INFO, "[ZS_ERROR_BANNER][SHEET_ACTION][" + ErrorCode.ERROR_ADD_ACTION_TO_QUEUE + "][RID:" + rid + "][ URL:" + request.getRequestURL() + "]");
                                HttpServletResponseWrapper.sendResponse(response, errObj, MimeType.JSON);
                        }
                }

                return null;
        }

}
