/* $Id$ */
/**
 *
 */
package com.zoho.sheet.action;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.zoho.sheet.connection.ZSConnection;
import com.zoho.sheet.connection.ZSConnectionFactory;
import com.zoho.sheet.conversion.ImportExportUtil;
import com.zoho.sheet.util.StrutsRequestHandler;

import javax.servlet.ServletException;
import java.io.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *
 */
public class FileReaderAction extends StrutsRequestHandler {
	private static final Logger LOGGER = Logger.getLogger(WebFormAction.class.getName());

	private static final int MAX_LINE_COUNT = 15;
	@Override
	public String execute()throws IOException, ServletException {

		String url = request.getParameter("url");
		int action = Integer.parseInt(request.getParameter("action"));
		String wholeContentParam = request.getParameter("wholeContent");
		boolean getWholeContent = wholeContentParam != null ? Boolean.parseBoolean(wholeContentParam.toString()) : false;
		JSONObjectWrapper result = new JSONObjectWrapper();
		switch(action) {
			case ActionConstants.GET_FILE_CONTENT_FROM_URL:
				result.put("content", getUrlContents(url, getWholeContent));
				break;
		}

		response.getWriter().print(result);	//NO OUTPUTENCODING
		return null;
	}

	private static String getUrlContents(String urlString, boolean getWholeContent)
	{
		StringBuilder content = new StringBuilder();
		try
		{

			ZSConnection zsconn = (new ZSConnectionFactory(urlString, "GET")).getConnection();		//No i18N
			zsconn.setFileSizeLimit(ImportExportUtil.allowedFileSizeNew());
			if(urlString.startsWith("https")) {
				zsconn.setSSLFactoryType("SSL");	//No i18N
			}
			zsconn.process();
			boolean isFileSizeExceeded = zsconn.isFileSizeExceeded();
			if(isFileSizeExceeded) {
				return "fileSizeExceeded";		//No i18N
			}

			InputStream is = new ByteArrayInputStream(zsconn.getByteInputStream());
			BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(is));

			String line;
			int count = 0;
			while ((line = bufferedReader.readLine()) != null)
			{
				if(!line.trim().equals("")) {
					count++;
					content.append(line + "\n");

					if(!getWholeContent && (count > FileReaderAction.MAX_LINE_COUNT)) {
						break;
					}
				}
			}
			bufferedReader.close();
		}
		catch(Exception e)
		{
			LOGGER.warning("Exception while getting URL contents in FileReaderAction"+e);
		}

		if(content.length() > 0) {
			return content.toString().trim();
		}
		return null;
	}
}
