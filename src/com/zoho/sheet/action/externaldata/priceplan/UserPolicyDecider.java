//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.priceplan;

import com.zoho.zfsng.client.ZohoFS;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR>
 *
 */
public class UserPolicyDecider {
	private static Logger logger = Logger.getLogger(UserPolicyDecider.class.getName());

//	public static HashMap getPolicy(String resourceId, String zuidstr){
//		int planId;
//		try {
//			if (ZSCreditPointsUtil.isWDFreeUser(ZohoFS.getSpaceId(resourceId))) {
//				planId = DCPlanConstructor.PlanID.DCP_0;
//			}
//			else
//			{
//				planId = DCPlanConstructor.PlanID.DCP_1;
//			}
//		}catch (Exception e)
//		{
//			planId = DCPlanConstructor.PlanID.DCP_0;
//			logger.log(Level.INFO, "[Data-Connection] assigning basic plan to user >> {0} error while getting userPlanINFO", new Object[]{zuidstr});
//		}
//
//		return DCPlanConstructor.getDCPlan(planId).planDetails;
//	}

	public static HashMap getPolicy(String resourceId, String zuidstr){
		int planId;
		JSONObject LicenseAPIDetails = null;
		try {
			LicenseAPIDetails =  ZohoFS.getLicenseAPIDetails(resourceId, Long.parseLong(zuidstr), "ZohoSheet", null); //NO I18N
			long spaceQuantity = Long.parseLong(String.valueOf(LicenseAPIDetails.get("ALLOWED_SPACE_IN_BYTES")));
			long spaceInGB = spaceQuantity/**********;
			planId = getplanID(spaceInGB);
		}
		catch (Exception e)
		{
			planId = DCPlanConstructor.PlanID.DCP_0;
			logger.log(Level.INFO, "[Data-Connection] assigning basic plan to user >> {0} ALLOWED_SPACE_IN_BYTES >> {1} >> error while getting STORAGE SPACE >> {2}", new Object[]{zuidstr, LicenseAPIDetails != null ? LicenseAPIDetails.get("ALLOWED_SPACE_IN_BYTES") : "LicenseAPIDetails is null" ,e});
		}

		return DCPlanConstructor.getDCPlan(planId).planDetails;
	}


	public static int getplanID( long spaceQuantity){

		if(spaceQuantity > 5) {
			return DCPlanConstructor.PlanID.DCP_1;
		}else {
			return DCPlanConstructor.PlanID.DCP_0; //Free
		}

	}

}
