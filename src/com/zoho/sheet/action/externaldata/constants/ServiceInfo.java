//$Id$
package com.zoho.sheet.action.externaldata.constants;

import com.zoho.sheet.action.externaldata.core.parser.ContentParser;

public interface ServiceInfo {
	
	public String getName();
	public String getDomainURL();
	public String getURL(int serviceActionConstant);
	public String getMethod(int serviceActionConstant);
	public ContentParser getContentParser();
	public String getCode(int serviceCode);
	public String getRecordIdLabel(String module);
	//public Map getModulesFields();
	public boolean isInternalService();
	
	
}
