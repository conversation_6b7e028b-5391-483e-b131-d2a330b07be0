//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.constants;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.action.externaldata.constants.impl.*;
import com.zoho.sheet.action.externaldata.core.extend.app.ZohoSalesIQ;


/**
 * <AUTHOR>
 *
 */
public class ConstantHolder {
	
	 private final static HashMap<Integer, ServiceInfo> APP_INFO = new LinkedHashMap<>();

	   
	    static {
	    	APP_INFO.put(ServiceConstant.ZFLOW, new ZohoFlowConst());
	    	/***1000-5000 -- Cloud APP*/
	    	/***1000-1100-CRM*/
	    	APP_INFO.put(ServiceConstant.ZCRM, new ZohoCRMConst());
	    	APP_INFO.put(ServiceConstant.SALESFORCE, new SalesForceConst());
			APP_INFO.put(ServiceConstant.ZBIGIN, new ZohoBiginConst());
	    	/***1101-1200-Accounting*/
	    	APP_INFO.put(ServiceConstant.ZBOOKS, new ZohoBooksConst());
	    	APP_INFO.put(ServiceConstant.ZCREATOR, new ZohoCreatorConst());
	    	APP_INFO.put(ServiceConstant.QUICKBOOKS, new QuickBooksConst());
	    	APP_INFO.put(ServiceConstant.FRESHBOOKS, new FreshBooksConst());
			APP_INFO.put(ServiceConstant.ZEXPENSE, new ZohoExpenseConst());
			APP_INFO.put(ServiceConstant.ZINVENTORY, new ZohoInventoryConst());
			APP_INFO.put(ServiceConstant.ZINVOICE, new ZohoInvoiceConst());
			APP_INFO.put(ServiceConstant.ZBILLING, new ZohoBillingConst());
	    	/***1201-1300-Marketing*/
			APP_INFO.put(ServiceConstant.ZCAMPAIGNS, new ZohoCampaignsConstant());
			APP_INFO.put(ServiceConstant.ZSALESIQ, new ZohoSalesIQConst());
			APP_INFO.put(ServiceConstant.ZMARKETINGAUTOMATION, new ZohoMarketingAutomationConst());
			APP_INFO.put(ServiceConstant.ZBACKSTAGE, new ZohoBackstageConst());
	    	/***1301-1400-Ecommerce*/
			APP_INFO.put(ServiceConstant.ZCOMMERCE, new ZohoCommerceConst());
	    	/***1401-1500-Esupport*/
			APP_INFO.put(ServiceConstant.ZDESK, new ZohoDeskConstant());
	    	/***1501-1600-ProjectManagement*/
			APP_INFO.put(ServiceConstant.ZPROJECTS, new ZohoProjectsConst());
			APP_INFO.put(ServiceConstant.ZBUGTRACKER, new ZohoBugTrackerConst());
			APP_INFO.put(ServiceConstant.ZSPRINTS, new ZohoSprintsConst());
	    	/***1601-1700-Shipping*/
	    	/***1701-1800-Human Resources*/
			APP_INFO.put(ServiceConstant.ZRECRUIT, new ZohoRecruitConst());
			APP_INFO.put(ServiceConstant.ZPEOPLE, new ZohoPeopleConst());
	    	//-------------------------------------------------
	    	/***5001-5500 -- Cloud DATABASE*/
	    	APP_INFO.put(ServiceConstant.ZANALYTICS, new ZohoAnalyticsConst());
	    	/***5501-6000 -- Cloud STORAGE*/
			APP_INFO.put(ServiceConstant.ZFORM, new ZohoFormsConst());
			APP_INFO.put(ServiceConstant.ZSURVEY, new ZohoSurveyConst());
			APP_INFO.put(ServiceConstant.ZSHEET, new ZohoSheetConst());
	    	/***6001-6500 -- Cloud WAREHOUSE*/
	    }

		public enum FilterType {
			//Field Types
			NONE(0),
			TEXT(1),
			NUMBER(2),
			DATE(3),
			TIME(4),
			DROP_DOWN(5);

			private final int typeId;

			FilterType(int typeId) {
				this.typeId = typeId;
			}

			public int getTypeId() {
				return typeId;
			}
		}

		public enum FilterOperator{

			EQUALS(0),
			NOT_EQUALS(12),

			//Text Conditions
			CONTAINS(1),
			BEGINNING_WITH(2),
			NOT_CONTAINS(13),

			//Number Conditions
			GREATER_EQUAL(3),
			GREATER_THAN(4),
			LESS_EQUAL(5),
			LESS_THAN(6),

			//Date-Time Conditions
			BEFORE(7),
			AFTER(8),
			BETWEEN(9),
			IS_EMPTY(10),
			IS_NOT_EMPTY(11);

			private final int opId;

			FilterOperator(int opId) {
				this.opId = opId;
			}

			public static FilterOperator getFilterOperator(int id)
			{
				for (FilterOperator value : FilterOperator.values()) {
					if(value.getOperatorId() == id)
					{
						return value;
					}
				}
				throw new IllegalArgumentException("Invalid Operator"); //No I18N
			}

			public int getOperatorId() {
				return opId;
			}
		}
	    
	    
	    public static ServiceInfo getServiceInfo(int serviceConstant) {
	    	return APP_INFO.get(serviceConstant);
	    }

		public final static String DATA_CONNECTION = "DataConnection"; //No i18n
		public final static String EAR_SERVICE_KEY = "zohosheet_pwd_secret"; //No i18n
		public final static String CLIENT_ID = EnginePropertyUtil.getDataConnectionInfo("CLIENT_ID"); //No i18n

		public final static String[] FLOW_ORG_TOKEN_SCOPES = new String[]{"ZohoFlow.organizations.CREATE", "ZohoFlow.organizations.UPDATE", "ZohoFlow.connections.CREATE", "ZohoFlow.users.CREATE", "ZohoFlow.users.READ", "ZohoFlow.connections.READ", "ZohoFlow.services.READ"}; //No i18n

		public static final String MODULES = "module";
		public static final String FIELDS = "field";
		public static final String FIELD_TYPE = "field_type";
		public static final String FILTER_OPTIONS = "filt_ops";
		public static final String AUTHORIZATION = "Authorization";

		// Sheet Pre-defined Limit
		public static final int MAX_RECORD_LIMIT = 10000;
		public static final int MAX_LINK_PER_AUTHSERVICE = 25;
		public static final int MAX_LINK_PER_UNAUTHSERVICE = 100;
		public static final int MAX_LINK_PER_DOC_UNAUTH = 25;
		public static final int MAX_LINK_PER_DOC_AUTH = 25;
		public static final int MAX_REFRESH_LIMIT_IN_24HRS = 50;

		public static final int MAX_FILTER_CRITERIA_LIMIT = 5;

	   

}
