package com.zoho.sheet.action.externaldata.constants.impl;

import com.zoho.sheet.action.externaldata.constants.AppServiceInfo;
import com.zoho.sheet.action.externaldata.constants.ConstantHolder;
import com.zoho.sheet.action.externaldata.constants.ServiceActionConstants;
import com.zoho.sheet.action.externaldata.constants.ServiceCode;

import java.util.*;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;
import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.FIELD_TYPE;

public class ZohoCommerceConst extends AppServiceInfo {

    public ZohoCommerceConst() {
        name = "ZCOMMERCE"; //NO I18N
        displayName = "Zoho Commerce"; //NO I18N
        this.isInternalService = true;
        this.scopes = "[\"ZohoCommerce.sitesIndex.Read\", \"ZohoCommerce.items.ALL\", \"ZohoCommerce.coupons.READ\", \"ZohoCommerce.settings.READ\", \"ZohoCommerce.salesorders.READ\"]";  //NO I18N

        url.put(ServiceActionConstants.FETCH_DATA, "store/api/v1/{0}");
        url.put(ServiceActionConstants.GET_ORG, "zs-site/api/v1/index/sites");

        // method Type
        method.put(ServiceActionConstants.FETCH_DATA, "GET");
        method.put(ServiceActionConstants.GET_ORG, "GET");

        code.put(ServiceCode.UNAUTHORISED_ACCESS,"57");

        Map moduleFields;

        //---Products
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("products", "Products"));
        moduleFields.put(FIELDS, PRODUCTS.keySet());
        Map<String, List<String>> productsFilterOptions = new HashMap<>();
        productsFilterOptions.put("filter_by", Arrays.asList("Status.All", "Status.Active", "Status.Inactive", "ItemType.All", "ItemType.Inventory", "ItemType.NonInventory"));
        moduleFields.put(FILTER_OPTIONS, productsFilterOptions);
        moduleFields.put(FIELD_TYPE, PRODUCTS.values());
        moduleFieldList.add(moduleFields);
        moduleToRecordIdLabel.put("products", "product_id");

        //---VARIANTS
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("variants", "Variants"));
        moduleFields.put(FIELDS, VARIANTS.keySet());
        Map<String, List<String>> variantsFilterOptions = new HashMap<>();
        variantsFilterOptions.put("status", Arrays.asList("active", "Inactive"));
        variantsFilterOptions.put("item_type", Arrays.asList("ItemType.Inventory", "ItemType.NonInventory"));
        moduleFields.put(FILTER_OPTIONS, variantsFilterOptions);
        moduleFields.put(FIELD_TYPE, VARIANTS.values());
        moduleFieldList.add(moduleFields);
        moduleToRecordIdLabel.put("variants", "variant_id");

        //---CATEGORIES
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("categories", "Categories"));
        moduleFields.put(FIELDS, CATEGORIES.keySet());
        moduleFields.put(FIELD_TYPE, CATEGORIES.values());
        moduleFieldList.add(moduleFields);
        moduleToRecordIdLabel.put("categories", "category_id");

        //---COUPONS
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("coupons", "Coupons"));
        moduleFields.put(FIELDS, COUPONS.keySet());
        moduleFields.put(FIELD_TYPE, COUPONS.values());
        moduleFieldList.add(moduleFields);
        moduleToRecordIdLabel.put("coupons", "coupon_id");

        //---TAXRULES
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("settings/taxrules", "Tax Rules"));
        moduleFields.put(FIELDS, TAXRULES.keySet());
        moduleFields.put(FIELD_TYPE, TAXRULES.values());
        moduleFieldList.add(moduleFields);
        moduleToRecordIdLabel.put("settings/taxrules", "rule_id");

        //---SALESORDERS
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("salesorders", "Sales Orders"));
        moduleFields.put(FIELDS, SALESORDERS.keySet());
        Map<String, List<String>> salesordersFilterOptions = new HashMap<>();
        salesordersFilterOptions.put("filter_by", Arrays.asList("Status.All", "Status.Draft", "Status.Confirmed", "Status.ToBePacked", "Status.ToBeShipped", "Status.Shipped", "Status.ToBeInvoiced", "Status.Closed", "Status.Void"));
        salesordersFilterOptions.put("order_status", Arrays.asList("draft", "pending", "confirmed", "closed", "void", "onhold", "cancelled"));
        salesordersFilterOptions.put("invoiced_status", Arrays.asList("partially_invoiced", "invoiced", "paid"));
        salesordersFilterOptions.put("paid_status", Arrays.asList("unpaid","partially_paid", "paid"));
        salesordersFilterOptions.put("shipped_status", Arrays.asList("pending", "partially_shipped", "shipped", "fulfilled"));
        salesordersFilterOptions.put("return_status", Arrays.asList("initiated", "partially_returned", "returned"));
        salesordersFilterOptions.put("return_shipment_status", Arrays.asList("receive_pending", "partially_received", "received"));
        salesordersFilterOptions.put("refund_status", Arrays.asList("refund_pending", "partially_refunded", "refunded"));
        salesordersFilterOptions.put("is_return_waiting_for_approval", Arrays.asList("true", "false"));
        salesordersFilterOptions.put("is_cancel_item_waiting_for_approval", Arrays.asList("true", "false"));
        moduleFields.put(FILTER_OPTIONS, salesordersFilterOptions);
        moduleFields.put(FIELD_TYPE, SALESORDERS.values());
        moduleFieldList.add(moduleFields);
        moduleToRecordIdLabel.put("salesorders", "salesorder_id");
    }

    private static final Map<String, ConstantHolder.FilterType> PRODUCTS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> VARIANTS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> CATEGORIES  = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> COUPONS  = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> TAXRULES  = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> SALESORDERS  = new LinkedHashMap<>();

    static {
        PRODUCTS.put("product_id", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("name", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("product_type", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("brand", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("manufacturer", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("unit", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("description", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("product_short_description", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("product_description", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("url", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("variant_type", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("is_returnable", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("document_id", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("alter_text", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("status", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("min_rate", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("max_rate", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("variant_count", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("overall_stock", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("created_time", ConstantHolder.FilterType.NONE);
        PRODUCTS.put("last_modified_time", ConstantHolder.FilterType.NONE);

        VARIANTS.put("variant_id", ConstantHolder.FilterType.NONE);
        VARIANTS.put("name", ConstantHolder.FilterType.NONE);
        VARIANTS.put("product_id", ConstantHolder.FilterType.NONE);
        VARIANTS.put("product_name", ConstantHolder.FilterType.NONE);
        VARIANTS.put("brand", ConstantHolder.FilterType.NONE);
        VARIANTS.put("category_id", ConstantHolder.FilterType.NONE);
        VARIANTS.put("purchase_rate", ConstantHolder.FilterType.NONE);
        VARIANTS.put("rate", ConstantHolder.FilterType.NONE);
        VARIANTS.put("label_rate", ConstantHolder.FilterType.NONE);
        VARIANTS.put("sku", ConstantHolder.FilterType.NONE);
        VARIANTS.put("minimum_order_quantity", ConstantHolder.FilterType.NONE);
        VARIANTS.put("maximum_order_quantity", ConstantHolder.FilterType.NONE);
        VARIANTS.put("reorder_level", ConstantHolder.FilterType.NONE);
        VARIANTS.put("document_id", ConstantHolder.FilterType.NONE);
        VARIANTS.put("document_name", ConstantHolder.FilterType.NONE);
        VARIANTS.put("is_combo_product", ConstantHolder.FilterType.NONE);
        VARIANTS.put("status", ConstantHolder.FilterType.NONE);
        VARIANTS.put("stock_on_hand", ConstantHolder.FilterType.NONE);
        VARIANTS.put("actual_available_stock", ConstantHolder.FilterType.NONE);
        VARIANTS.put("available_stock", ConstantHolder.FilterType.NONE);
        VARIANTS.put("available_sale_stock", ConstantHolder.FilterType.NONE);
        VARIANTS.put("committed_stock", ConstantHolder.FilterType.NONE);
        VARIANTS.put("track_serial_number", ConstantHolder.FilterType.NONE);
        VARIANTS.put("track_batch_number", ConstantHolder.FilterType.NONE);
        VARIANTS.put("last_modified_time", ConstantHolder.FilterType.NONE);

        CATEGORIES.put("category_id", ConstantHolder.FilterType.NONE);
        CATEGORIES.put("name", ConstantHolder.FilterType.NONE);
        CATEGORIES.put("visibility", ConstantHolder.FilterType.NONE);
        CATEGORIES.put("sibling_order", ConstantHolder.FilterType.NONE);
        CATEGORIES.put("parent_category_id", ConstantHolder.FilterType.NONE);
        CATEGORIES.put("depth", ConstantHolder.FilterType.NONE);
        CATEGORIES.put("created_time", ConstantHolder.FilterType.NONE);

        COUPONS.put("coupon_id", ConstantHolder.FilterType.NONE);
        COUPONS.put("coupon_name", ConstantHolder.FilterType.NONE);
        COUPONS.put("coupon_code", ConstantHolder.FilterType.NONE);
        COUPONS.put("status", ConstantHolder.FilterType.NONE);
        COUPONS.put("is_active", ConstantHolder.FilterType.NONE);
        COUPONS.put("is_active_formatted", ConstantHolder.FilterType.NONE);
        COUPONS.put("discount_type", ConstantHolder.FilterType.NONE);
        COUPONS.put("discount_type_formatted", ConstantHolder.FilterType.NONE);
        COUPONS.put("discount_value", ConstantHolder.FilterType.NONE);
        COUPONS.put("discount_value_formatted", ConstantHolder.FilterType.NONE);
        COUPONS.put("redemption_count", ConstantHolder.FilterType.NONE);

        TAXRULES.put("rule_id", ConstantHolder.FilterType.NONE);
        TAXRULES.put("tax_id", ConstantHolder.FilterType.NONE);
        TAXRULES.put("tax_name", ConstantHolder.FilterType.NONE);
        TAXRULES.put("tax_percentage", ConstantHolder.FilterType.NONE);
        TAXRULES.put("is_tax_overridden", ConstantHolder.FilterType.NONE);
        TAXRULES.put("total_states", ConstantHolder.FilterType.NONE);
        TAXRULES.put("configured_states", ConstantHolder.FilterType.NONE);
        TAXRULES.put("country_code", ConstantHolder.FilterType.NONE);
        TAXRULES.put("country_name", ConstantHolder.FilterType.NONE);

        SALESORDERS.put("salesorder_id", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("salesorder_number", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("zcrm_potential_id", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("zcrm_potential_name", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("customer_id", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("customer_name", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("email", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("order_status", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("invoiced_status", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("paid_status", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("shipped_status", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("refund_status", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("return_status", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("return_shipment_status", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("reference_number", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("date", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("shipment_date", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("shipment_days", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("currency_id", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("currency_code", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("total", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("bcy_total", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("Notes", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("is_emailed", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("is_possible_fraud_transaction", ConstantHolder.FilterType.NONE);
        SALESORDERS.put("fraud_transaction_reason", ConstantHolder.FilterType.NONE);
    }
}
