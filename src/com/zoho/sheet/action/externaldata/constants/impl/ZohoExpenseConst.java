//$Id$

package com.zoho.sheet.action.externaldata.constants.impl;

import java.util.*;
import com.zoho.sheet.action.externaldata.constants.AppServiceInfo;
import com.zoho.sheet.action.externaldata.constants.ConstantHolder.FilterType;
import com.zoho.sheet.action.externaldata.constants.ServiceActionConstants;
import com.zoho.sheet.action.externaldata.constants.ServiceCode;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;


/**
* <AUTHOR>
*
*/
public class ZohoExpenseConst extends AppServiceInfo {

	private static final Set<String> MODULES_HAS_CUSTOM_FIELDS = new HashSet<>(Arrays.asList("expenses", "users", "contacts",  "projects")); //No i18n

	private static final Map<String, FilterType> TRIPS= new LinkedHashMap<>();
	private static final Map<String, FilterType> EXPENSES= new LinkedHashMap<>();
	private static final Map<String, FilterType> EXPENSE_REPORTS= new LinkedHashMap<>();
	private static final Map<String, FilterType> EXPENSE_CATEGORIES= new LinkedHashMap<>();
	private static final Map<String, FilterType> USER= new LinkedHashMap<>();
	private static final Map<String, FilterType> CONTACTS= new LinkedHashMap<>();
	private static final Map<String, FilterType> PROJECTS= new LinkedHashMap<>();

	static
	{
		TRIPS.put("trip_id", FilterType.NONE);
		TRIPS.put("trip_number", FilterType.TEXT);
		TRIPS.put("is_round_trip", FilterType.NONE);
		TRIPS.put("is_international", FilterType.NONE);
		TRIPS.put("is_visa_required", FilterType.NONE);
		TRIPS.put("business_purpose", FilterType.TEXT);
		TRIPS.put("description", FilterType.NONE);
		TRIPS.put("currency_id", FilterType.NONE);
		TRIPS.put("currency_code", FilterType.NONE);
		TRIPS.put("budget_amount", FilterType.NUMBER);
		TRIPS.put("bcy_budget_amount", FilterType.NONE);
		TRIPS.put("price_precision", FilterType.NONE);
		TRIPS.put("start_date", FilterType.DATE);
		TRIPS.put("end_date", FilterType.DATE);
		TRIPS.put("departure", FilterType.NONE);
		TRIPS.put("destination_country", FilterType.TEXT);
		TRIPS.put("destination_city", FilterType.TEXT);
		TRIPS.put("meal_preference", FilterType.NONE);
		TRIPS.put("seat_preference", FilterType.NONE);
		TRIPS.put("is_billable", FilterType.NONE);
		TRIPS.put("customer_id", FilterType.NONE);
		TRIPS.put("customer_name", FilterType.TEXT);
		TRIPS.put("project_id", FilterType.NONE);
		TRIPS.put("project_name", FilterType.TEXT);
		TRIPS.put("status", FilterType.NONE);
		TRIPS.put("submitted_date", FilterType.DATE);
		TRIPS.put("last_submitted_date", FilterType.NONE);
		TRIPS.put("approver_id", FilterType.NONE);
		TRIPS.put("approver_name", FilterType.NONE);
		TRIPS.put("approver_email", FilterType.NONE);
		TRIPS.put("submitted_to_id", FilterType.NONE);
		TRIPS.put("submitted_to_name", FilterType.NONE);
		TRIPS.put("submitted_to_email", FilterType.NONE);
		TRIPS.put("submitted_by", FilterType.NONE);
		TRIPS.put("submitter_name", FilterType.NONE);
		TRIPS.put("submitter_email", FilterType.NONE);
		TRIPS.put("submitter_employee_no", FilterType.NONE);
		TRIPS.put("submitter_department_name", FilterType.NONE);
		TRIPS.put("employee_number", FilterType.NONE);
		TRIPS.put("department_id", FilterType.NONE);
		TRIPS.put("next_approver_id", FilterType.NONE);
		TRIPS.put("next_approver_email", FilterType.NONE);
		TRIPS.put("next_approver_name", FilterType.NONE);
		TRIPS.put("approver_employee_no", FilterType.NONE);
		TRIPS.put("approver_department_name", FilterType.NONE);
		TRIPS.put("previous_approver_id", FilterType.NONE);
		TRIPS.put("previous_approver_name", FilterType.NONE);
		TRIPS.put("previous_approver_email", FilterType.NONE);
		TRIPS.put("previous_approver_employee_no", FilterType.NONE);
		TRIPS.put("previous_approver_department_name", FilterType.NONE);
		TRIPS.put("created_time", FilterType.NONE);
		TRIPS.put("created_date", FilterType.NONE);
		TRIPS.put("last_modified_time", FilterType.NONE);
		TRIPS.put("created_by_id", FilterType.NONE);
		TRIPS.put("created_by_name", FilterType.NONE);
		TRIPS.put("created_by_email", FilterType.NONE);
		TRIPS.put("policy_id", FilterType.NONE);
		TRIPS.put("policy_name", FilterType.NONE);
		TRIPS.put("user_id", FilterType.NONE);
		TRIPS.put("user_name", FilterType.NONE);
		TRIPS.put("user_email", FilterType.NONE);
		TRIPS.put("user_employee_no", FilterType.NONE);
		TRIPS.put("user_department_name", FilterType.NONE);

		EXPENSES.put("expense_id", FilterType.NONE);
		EXPENSES.put("date", FilterType.DATE);
		EXPENSES.put("description", FilterType.TEXT);
		EXPENSES.put("created_by", FilterType.NONE);
		EXPENSES.put("created_by_name", FilterType.NONE);
		EXPENSES.put("employee_number", FilterType.NONE);
		EXPENSES.put("currency_code", FilterType.NONE);
		EXPENSES.put("paid_through_account_id", FilterType.NONE);
		EXPENSES.put("paid_through_account_name", FilterType.NONE);
		EXPENSES.put("bcy_total", FilterType.NONE);
		EXPENSES.put("bcy_subtotal", FilterType.NONE);
		EXPENSES.put("total", FilterType.NONE);
		EXPENSES.put("total_without_tax", FilterType.NONE);
		EXPENSES.put("is_billable", FilterType.NONE);
		EXPENSES.put("reference_number", FilterType.NONE);
		EXPENSES.put("due_days", FilterType.NONE);
		EXPENSES.put("merchant_name", FilterType.NONE);
		EXPENSES.put("status", FilterType.NONE);
		EXPENSES.put("created_time", FilterType.NONE);
		EXPENSES.put("last_modified_time", FilterType.NONE);
		EXPENSES.put("receipt_name", FilterType.NONE);
		EXPENSES.put("mileage_type", FilterType.NONE);
		EXPENSES.put("mileage_rate", FilterType.NONE);
		EXPENSES.put("mileage_unit", FilterType.NONE);
		EXPENSES.put("report_name", FilterType.NONE);
		EXPENSES.put("is_receipt_only", FilterType.NONE);
		EXPENSES.put("distance", FilterType.NONE);
		EXPENSES.put("per_diem_rate", FilterType.NONE);
		EXPENSES.put("per_diem_days", FilterType.NONE);
		EXPENSES.put("per_diem_id", FilterType.NONE);
		EXPENSES.put("per_diem_name", FilterType.NONE);
		EXPENSES.put("expense_type", FilterType.NONE);
		EXPENSES.put("location", FilterType.NONE);
		EXPENSES.put("receipt_type", FilterType.NONE);
		EXPENSES.put("policy_violated", FilterType.NONE);
		EXPENSES.put("comments_count", FilterType.NONE);
		EXPENSES.put("report_status", FilterType.NONE);
		EXPENSES.put("receipt_status", FilterType.NONE);
		EXPENSES.put("price_precision", FilterType.NONE);
		EXPENSES.put("is_uncategorized", FilterType.NONE);
		EXPENSES.put("is_expired", FilterType.NONE);
		EXPENSES.put("gl_code", FilterType.NONE);
		EXPENSES.put("exchange_rate", FilterType.NONE);
		EXPENSES.put("start_reading", FilterType.NONE);
		EXPENSES.put("end_reading", FilterType.NONE);
		EXPENSES.put("payment_mode", FilterType.NONE);
		EXPENSES.put("customer_id", FilterType.NONE);
		EXPENSES.put("customer_name", FilterType.TEXT);
		EXPENSES.put("project_name", FilterType.TEXT);
		EXPENSES.put("transaction_description", FilterType.NONE);
		EXPENSES.put("tax_name", FilterType.NONE);
		EXPENSES.put("tax_percentage", FilterType.NONE);
		EXPENSES.put("amount", FilterType.NUMBER);
		EXPENSES.put("is_inclusive_tax", FilterType.NONE);
		EXPENSES.put("vehicle_type", FilterType.NONE);
		EXPENSES.put("vehicle_id", FilterType.NONE);
		EXPENSES.put("fuel_type", FilterType.NONE);
		EXPENSES.put("engine_capacity_range", FilterType.NONE);
		EXPENSES.put("is_personal", FilterType.NONE);
		EXPENSES.put("policy_id", FilterType.NONE);
		EXPENSES.put("policy_name", FilterType.NONE);
		EXPENSES.put("is_reimbursable", FilterType.NONE);
		EXPENSES.put("reimbursement_reference", FilterType.NONE);
		EXPENSES.put("reimbursement_date", FilterType.NONE);
		EXPENSES.put("reimbursement_paid_through_account_id", FilterType.NONE);
		EXPENSES.put("reimbursement_paid_through_account_name", FilterType.NONE);
		EXPENSES.put("reimbursement_currency_id", FilterType.NONE);
		EXPENSES.put("reimbursement_currency_code", FilterType.NONE);

		EXPENSE_REPORTS.put("report_id", FilterType.NONE);
		EXPENSE_REPORTS.put("report_name", FilterType.NONE);
		EXPENSE_REPORTS.put("report_number", FilterType.TEXT);
		EXPENSE_REPORTS.put("description", FilterType.TEXT);
		EXPENSE_REPORTS.put("start_date", FilterType.DATE);
		EXPENSE_REPORTS.put("end_date", FilterType.DATE);
		EXPENSE_REPORTS.put("status", FilterType.NONE);
		EXPENSE_REPORTS.put("is_archived", FilterType.NONE);
		EXPENSE_REPORTS.put("due_date", FilterType.DATE);
		EXPENSE_REPORTS.put("submitted_date", FilterType.DATE);
		EXPENSE_REPORTS.put("approved_date", FilterType.DATE);
		EXPENSE_REPORTS.put("last_submitted_date", FilterType.NONE);
		EXPENSE_REPORTS.put("currency_code", FilterType.NONE);
		EXPENSE_REPORTS.put("total", FilterType.NONE);
		EXPENSE_REPORTS.put("reimbursable_total", FilterType.NONE);
		EXPENSE_REPORTS.put("non_reimbursable_total", FilterType.NONE);
		EXPENSE_REPORTS.put("reimbursement_date", FilterType.NONE);
		EXPENSE_REPORTS.put("approver_name", FilterType.NONE);
		EXPENSE_REPORTS.put("approver_email", FilterType.NONE);
		EXPENSE_REPORTS.put("submitted_to_name", FilterType.NONE);
		EXPENSE_REPORTS.put("submitted_to_email", FilterType.NONE);
		EXPENSE_REPORTS.put("submitter_name", FilterType.NONE);
		EXPENSE_REPORTS.put("submitter_email", FilterType.NONE);
		EXPENSE_REPORTS.put("created_time", FilterType.NONE);
		EXPENSE_REPORTS.put("last_modified_time", FilterType.NONE);
		EXPENSE_REPORTS.put("created_by_name", FilterType.NONE);
		EXPENSE_REPORTS.put("comments_count", FilterType.NONE);
		EXPENSE_REPORTS.put("policy_violated", FilterType.NONE);
		EXPENSE_REPORTS.put("uncategorized_expense_count", FilterType.NONE);
		EXPENSE_REPORTS.put("customer_name", FilterType.TEXT);
		EXPENSE_REPORTS.put("project_name", FilterType.TEXT);
		EXPENSE_REPORTS.put("policy_name", FilterType.NONE);
		EXPENSE_REPORTS.put("due_days", FilterType.NONE);

		EXPENSE_CATEGORIES.put("category_id", FilterType.NONE);
		EXPENSE_CATEGORIES.put("category_name", FilterType.TEXT);
		EXPENSE_CATEGORIES.put("gl_code", FilterType.NONE);
		EXPENSE_CATEGORIES.put("description", FilterType.NONE);
		EXPENSE_CATEGORIES.put("status", FilterType.NONE);
		EXPENSE_CATEGORIES.put("attachment_file_name", FilterType.NONE);
		EXPENSE_CATEGORIES.put("flat_amount", FilterType.NONE);
		EXPENSE_CATEGORIES.put("is_maximum_amount_required", FilterType.NONE);
		EXPENSE_CATEGORIES.put("maximum_allowed_amount", FilterType.NONE);
		EXPENSE_CATEGORIES.put("is_description_required", FilterType.NONE);
		EXPENSE_CATEGORIES.put("is_receipt_required", FilterType.NONE);
		EXPENSE_CATEGORIES.put("is_mileage", FilterType.NONE);
		EXPENSE_CATEGORIES.put("is_perdiem_account", FilterType.NONE);
		EXPENSE_CATEGORIES.put("receipt_required_amount", FilterType.NONE);
		EXPENSE_CATEGORIES.put("can_override_settings", FilterType.NONE);
		EXPENSE_CATEGORIES.put("is_custom", FilterType.NONE);
		EXPENSE_CATEGORIES.put("is_super_parent", FilterType.NONE);
		EXPENSE_CATEGORIES.put("is_enabled", FilterType.NONE);
		EXPENSE_CATEGORIES.put("category_icon", FilterType.NONE);
		EXPENSE_CATEGORIES.put("can_delete", FilterType.NONE);
		EXPENSE_CATEGORIES.put("category_type", FilterType.NONE);
		EXPENSE_CATEGORIES.put("parent_account_id", FilterType.NONE);
		EXPENSE_CATEGORIES.put("parent_account_name", FilterType.NONE);
		EXPENSE_CATEGORIES.put("depth", FilterType.NONE);
		EXPENSE_CATEGORIES.put("is_child_present", FilterType.NONE);
		EXPENSE_CATEGORIES.put("child_count", FilterType.NONE);

		USER.put("user_id", FilterType.NONE);
		USER.put("name", FilterType.TEXT);
		USER.put("email", FilterType.TEXT);
		USER.put("role_id", FilterType.NONE);
		USER.put("user_role", FilterType.NONE);
		USER.put("user_type", FilterType.NONE);
		USER.put("department_id", FilterType.NONE);
		USER.put("department_name", FilterType.TEXT);
		USER.put("employee_number", FilterType.TEXT);
		USER.put("policy_id", FilterType.NONE);
		USER.put("policy_name", FilterType.NONE);
		USER.put("can_approve", FilterType.NONE);
		USER.put("status", FilterType.NONE);
		USER.put("default_approver_id", FilterType.NONE);
		USER.put("default_approver_name", FilterType.NONE);
		USER.put("default_approver_email", FilterType.NONE);
		USER.put("is_from_crm", FilterType.NONE);
		USER.put("is_current_user", FilterType.NONE);
		USER.put("photo_url", FilterType.NONE);
		USER.put("approves_to_id", FilterType.NONE);
		USER.put("approves_to_name", FilterType.NONE);
		USER.put("approves_to_email", FilterType.NONE);
		USER.put("submission_amount_limit", FilterType.NONE);
		USER.put("approval_amount_limit", FilterType.NONE);
		USER.put("created_time", FilterType.NONE);
		USER.put("last_modified_time", FilterType.NONE);
		USER.put("user_mail_in_id", FilterType.NONE);

		CONTACTS.put("contact_id", FilterType.NONE);
		CONTACTS.put("contact_name", FilterType.TEXT);
		CONTACTS.put("company_name", FilterType.TEXT);
		CONTACTS.put("customer_name", FilterType.NONE);
		CONTACTS.put("vendor_name", FilterType.NONE);
		CONTACTS.put("contact_type", FilterType.NONE);
		CONTACTS.put("status", FilterType.NONE);
		CONTACTS.put("portal_status", FilterType.NONE);
		CONTACTS.put("source", FilterType.NONE);
		CONTACTS.put("is_linked_with_zohocrm", FilterType.NONE);
		CONTACTS.put("first_name", FilterType.NONE);
		CONTACTS.put("last_name", FilterType.NONE);
		CONTACTS.put("email", FilterType.NONE);
		CONTACTS.put("phone", FilterType.NONE);
		CONTACTS.put("mobile", FilterType.NONE);
		CONTACTS.put("website", FilterType.NONE);
		CONTACTS.put("language_code", FilterType.NONE);
		CONTACTS.put("language_code_formatted", FilterType.NONE);
		CONTACTS.put("currency_id", FilterType.NONE);
		CONTACTS.put("currency_code", FilterType.NONE);
		CONTACTS.put("outstanding_receivable_amount", FilterType.NONE);
		CONTACTS.put("outstanding_receivable_amount_bcy", FilterType.NONE);
		CONTACTS.put("outstanding_payable_amount", FilterType.NONE);
		CONTACTS.put("outstanding_payable_amount_bcy", FilterType.NONE);
		CONTACTS.put("unused_credits_receivable_amount", FilterType.NONE);
		CONTACTS.put("unused_credits_receivable_amount_bcy", FilterType.NONE);
		CONTACTS.put("unused_credits_payable_amount", FilterType.NONE);
		CONTACTS.put("unused_credits_payable_amount_bcy", FilterType.NONE);
		CONTACTS.put("last_modified_time", FilterType.NONE);

		PROJECTS.put("project_id", FilterType.NONE);
		PROJECTS.put("project_name", FilterType.TEXT);
		PROJECTS.put("customer_id", FilterType.NONE);
		PROJECTS.put("customer_name", FilterType.TEXT);
		PROJECTS.put("description", FilterType.NONE);
		PROJECTS.put("status", FilterType.NONE);
		PROJECTS.put("created_time", FilterType.NONE);
	}



		public ZohoExpenseConst(){
			name = "ZEXPENSE"; //NO I18N
			this.isInternalService = true;
			displayName = "Zoho Expense"; //NO I18N
			scopes =  "[\"ZohoExpense.fullaccess.ALL\"]"; //NO I18N

			url.put(ServiceActionConstants.GET_ORG,"v1/organizations");
			url.put(ServiceActionConstants.FETCH_DATA,"v1/{0}");
			url.put(ServiceActionConstants.GET_FIELDS,"v1/settings/fields?entity={0}");
			url.put(ServiceActionConstants.GET_DELETED_RECORDS, "v1/reports/deletedtransactions");

			// Method Type
			method.put(ServiceActionConstants.GET_ORG,"GET");
			method.put(ServiceActionConstants.FETCH_DATA,"GET");
			method.put(ServiceActionConstants.GET_FIELDS,"GET");
			method.put(ServiceActionConstants.GET_DELETED_RECORDS, "GET");

			List moduleList = null;
			Map moduleFields = new LinkedHashMap<>();
			//---
			moduleList = new ArrayList(2);
			moduleList.add("reports/expensedetails");
			moduleList.add("Expenses");

			moduleFields.put(MODULES, moduleList);
			moduleFields.put(FIELDS, EXPENSES.keySet());
			moduleFields.put(FIELD_TYPE, EXPENSES.values());
			Map<String,List<String>> expensesFilterOptions = new HashMap<>();
			expensesFilterOptions.put("status", Arrays.asList("submitted", "approved", "rejected"));
			moduleFields.put(FILTER_OPTIONS, expensesFilterOptions);
			moduleFieldList.add(moduleFields);
			moduleToRecordIdLabel.put("expenses", "expense_id");
			//---

			//---
			moduleFields = new LinkedHashMap<>();
			moduleList = new ArrayList(2);
			moduleList.add("expensereports");
			moduleList.add("Expense Reports");

			moduleFields.put(MODULES, moduleList);
			moduleFields.put(FIELDS, EXPENSE_REPORTS.keySet());
			moduleFields.put(FIELD_TYPE, EXPENSE_REPORTS.values());
			Map<String,List<String>> expensesReportsFilterOptions = new HashMap<>();
			expensesReportsFilterOptions.put("status", Arrays.asList("submitted", "approved", "rejected", "reimbursed"));
			moduleFields.put(FILTER_OPTIONS, expensesReportsFilterOptions);
			moduleFieldList.add(moduleFields);
			moduleToRecordIdLabel.put("expensereports", "report_id");
			//---

			moduleFields = new LinkedHashMap<>();
			moduleList = new ArrayList(2);
			moduleList.add("trips");
			moduleList.add("Trips");

			moduleFields.put(MODULES, moduleList);
			moduleFields.put(FIELDS, TRIPS.keySet());
			moduleFields.put(FIELD_TYPE, TRIPS.values());
			Map<String,List<String>> tripsFilterOptions = new HashMap<>();
			tripsFilterOptions.put("status", Arrays.asList("submitted", "approved", "rejected", "closed", "cancelled"));
			moduleFields.put(FILTER_OPTIONS, tripsFilterOptions);
			moduleFieldList.add(moduleFields);
			moduleToRecordIdLabel.put("trips", "trip_id");

			//---
			moduleFields = new LinkedHashMap<>();
			moduleList = new ArrayList(2);
			moduleList.add("expensecategories");
			moduleList.add("Expense categories");

			moduleFields.put(MODULES, moduleList);
			moduleFields.put(FIELDS, EXPENSE_CATEGORIES.keySet());
			moduleFields.put(FIELD_TYPE, EXPENSE_CATEGORIES.values());
			moduleFieldList.add(moduleFields);
			moduleToRecordIdLabel.put("expensecategories", "category_id");
			//---
			moduleFields = new LinkedHashMap<>();
			moduleList = new ArrayList(2);
			moduleList.add("users");
			moduleList.add("Users");

			moduleFields.put(MODULES, moduleList);
			moduleFields.put(FIELDS, USER.keySet());
			moduleFields.put(FIELD_TYPE, USER.values());
			moduleFieldList.add(moduleFields);
			moduleToRecordIdLabel.put("users", "user_id");
			//---
			moduleFields = new LinkedHashMap<>();
			moduleList = new ArrayList(2);
			moduleList.add("contacts");
			moduleList.add("Contacts");

			moduleFields.put(MODULES, moduleList);
			moduleFields.put(FIELDS, CONTACTS.keySet());
			moduleFields.put(FIELD_TYPE, CONTACTS.values());
			moduleFieldList.add(moduleFields);
			moduleToRecordIdLabel.put("contacts", "contact_id");
			//---
			moduleFields = new LinkedHashMap<>();
			moduleList = new ArrayList(2);
			moduleList.add("projects");
			moduleList.add("Projects");

			moduleFields.put(MODULES, moduleList);
			moduleFields.put(FIELDS, PROJECTS.keySet());
			moduleFields.put(FIELD_TYPE, PROJECTS.values());
			moduleFieldList.add(moduleFields);
			moduleToRecordIdLabel.put("projects", "project_id");

			//Codes
			code.put(ServiceCode.UNAUTHORISED_ACCESS,"57");

			filterOperatorMap.put(FilterOperator.EQUALS, "");

			//Text Operators
			filterOperatorMap.put(FilterOperator.CONTAINS, "_contains");
			filterOperatorMap.put(FilterOperator.BEGINNING_WITH, "_startswith");

			//Number Operators
			filterOperatorMap.put(FilterOperator.GREATER_EQUAL, "_greater_equals");
			filterOperatorMap.put(FilterOperator.GREATER_THAN, "_greater_than");
			filterOperatorMap.put(FilterOperator.LESS_EQUAL, "_less_equals");
			filterOperatorMap.put(FilterOperator.LESS_THAN, "_less_than");

			//Date Operators
			filterOperatorMap.put(FilterOperator.BEFORE, "_before");
			filterOperatorMap.put(FilterOperator.AFTER, "_after");
		}

	public boolean hasCustomFields(String moduleName) {
		return MODULES_HAS_CUSTOM_FIELDS.contains(moduleName);
	}
}
