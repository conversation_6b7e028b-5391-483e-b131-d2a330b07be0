package com.zoho.sheet.action.externaldata.constants.impl;

import com.zoho.sheet.action.externaldata.constants.AppServiceInfo;
import com.zoho.sheet.action.externaldata.constants.ConstantHolder;
import com.zoho.sheet.action.externaldata.constants.ServiceActionConstants;
import com.zoho.sheet.action.externaldata.constants.ServiceCode;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;

public class ZohoMarketingAutomationConst extends AppServiceInfo {

    public ZohoMarketingAutomationConst() {
        name = "ZMARKETINGAUTOMATION"; //NO I18N
        displayName = "Zoho Marketing Automation"; //NO I18N
        this.isInternalService = true;
        this.multiConnectionSupport = true;
        this.scopes = "[\"ZohoMarketingHub.journey.READ\", \"ZohoMarketingHub.lead.ALL\", \"ZohoMarketingHub.campaign.ALL\"]";  //NO I18N

        url.put(ServiceActionConstants.FETCH_DATA, "/v1/{0}");
        url.put(ServiceActionConstants.GET_ORG, "/v1/admin/orginfo");

        // method Type
        method.put(ServiceActionConstants.FETCH_DATA, "GET");
        method.put(ServiceActionConstants.GET_ORG, "GET");

        code.put(ServiceCode.UNAUTHORISED_ACCESS,"57");

        Map moduleFields;

        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("recentcampaigns", "Recent Campaigns"));
        moduleFields.put(FIELDS, RECENT_CAMPAIGNS.keySet());
        moduleFields.put(FIELD_TYPE, RECENT_CAMPAIGNS.values());
        moduleFieldList.add(moduleFields);
    }

    private static final Map<String, ConstantHolder.FilterType> RECENT_CAMPAIGNS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> RECENT_SENT_CAMPAIGNS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> LAST_CAMPAIGN_REPORT = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> MAILING_LISTS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> LEAD_TAGS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> JOURNEYS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> TOPICS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> PRODUCTS = new LinkedHashMap<>();


    static
    {
        RECENT_CAMPAIGNS.put("campaign_key", FilterType.NONE);
        RECENT_CAMPAIGNS.put("campaign_name", FilterType.NONE);
        RECENT_CAMPAIGNS.put("campaign_preview", FilterType.NONE);
        RECENT_CAMPAIGNS.put("campaign_status", FilterType.NONE);
        RECENT_CAMPAIGNS.put("created_date_string", FilterType.NONE);
        RECENT_CAMPAIGNS.put("created_time", FilterType.NONE);

        RECENT_SENT_CAMPAIGNS.put("sent_time", FilterType.NONE);
        RECENT_SENT_CAMPAIGNS.put("created_time", FilterType.NONE);
        RECENT_SENT_CAMPAIGNS.put("campaign_key", FilterType.NONE);
        RECENT_SENT_CAMPAIGNS.put("campaign_name", FilterType.NONE);

        LAST_CAMPAIGN_REPORT.put("campaign_name", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("forward_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("hardbounce_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("unsent_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("bounce_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("unique_clicked_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("unopened", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("unsubscribe_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("spams_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("spam_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("delivered_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("delivered_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("complaints_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("unopened_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("autoreply_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("softbounce_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("opens_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("unique_clicks_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("unsub_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("complaints_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("unsent_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("bounces_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("open_percent", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("clicksperopenrate", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("forwards_count", FilterType.NONE);
        LAST_CAMPAIGN_REPORT.put("emails_sent_count", FilterType.NONE);

        MAILING_LISTS.put("listunino", FilterType.NONE);
        MAILING_LISTS.put("listname", FilterType.NONE);
        MAILING_LISTS.put("zuid", FilterType.NONE);
        MAILING_LISTS.put("owner", FilterType.NONE);
        MAILING_LISTS.put("noofunsubcnt", FilterType.NONE);
        MAILING_LISTS.put("noofleads", FilterType.NONE);
        MAILING_LISTS.put("noofbouncecnt", FilterType.NONE);
        MAILING_LISTS.put("lockstatus", FilterType.NONE);
        MAILING_LISTS.put("date", FilterType.NONE);
        MAILING_LISTS.put("created_date", FilterType.NONE);
        MAILING_LISTS.put("is_public", FilterType.NONE);

        LEAD_TAGS.put("tagowner", FilterType.NONE);
        LEAD_TAGS.put("tag_name", FilterType.NONE);
        LEAD_TAGS.put("tag_desc", FilterType.NONE);
        LEAD_TAGS.put("tagged_contact_count", FilterType.NONE);
        LEAD_TAGS.put("is_crm_tag", FilterType.NONE);
        LEAD_TAGS.put("zuid", FilterType.NONE);
        LEAD_TAGS.put("tag_created_time", FilterType.NONE);

        JOURNEYS.put("created_time", FilterType.NONE);
        JOURNEYS.put("journey_key", FilterType.NONE);
        JOURNEYS.put("triggerkey", FilterType.NONE);
        JOURNEYS.put("journey_name", FilterType.NONE);

        TOPICS.put("topicId", FilterType.NONE);
        TOPICS.put("topicName", FilterType.NONE);
        TOPICS.put("primaryList", FilterType.NONE);

        PRODUCTS.put("product_id", FilterType.NONE);
        PRODUCTS.put("product_name", FilterType.NONE);
    }
}
