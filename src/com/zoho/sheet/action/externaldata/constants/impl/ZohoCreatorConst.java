//$Id$

package com.zoho.sheet.action.externaldata.constants.impl;

import java.util.EnumMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.AppServiceInfo;
import com.zoho.sheet.action.externaldata.constants.ConstantHolder;
import com.zoho.sheet.action.externaldata.constants.ServiceActionConstants;
import com.zoho.sheet.action.externaldata.constants.ServiceCode;
import com.zoho.sheet.action.externaldata.core.parser.ContentParser;
import com.zoho.sheet.action.externaldata.core.parser.DCParserFactory;


/**
* <AUTHOR>
*
*/
public class ZohoCreatorConst extends AppServiceInfo {
	
	

		public ZohoCreatorConst(){
			name = "ZCREATOR"; //NO I18N
			this.isInternalService = true;
			displayName = "Zoho Creator"; //NO I18N
			scopes =  "[\"ZohoCreator.dashboard.read\",\"ZohoCreator.meta.application.read\",\"ZohoCreator.meta.form.read\",\"ZohoCreator.report.READ\",\"ZohoCreator.meta.report.read\",\"ZohoCreator.form.READ\",\"ZohoCreator.data.READ\"]"; //NO I18N

			contentParser = DCParserFactory.getContentParser(ExternalDataUtils.FTYPE.CSV);

			url.put(ServiceActionConstants.GET_OWNDBLIST, "api/v2/applications?complete=true");
			url.put(ServiceActionConstants.GET_MODULES, "api/v2/{0}/{1}/forms");
			//url.put(ServiceActionConstants.GET_FIELDS,"api/v2/{0}/{1}/report/{2}/columns");
			url.put(ServiceActionConstants.GET_FIELDS,"api/v2/{0}/{1}/form/{2}/fields?id_required=true");
			url.put(ServiceActionConstants.FETCH_DATA,"api/v2/{0}/{1}/form/{2}/records");
			url.put(ServiceActionConstants.GET_DELETED_RECORDS,"api/v2/{0}/{1}/form/{2}/deletedrecords");
//			url.put(ServiceActionConstants.FETCH_DATA,"api/v2/{0}/{1}/report/{2}");

			// Method Type
			method.put(ServiceActionConstants.GET_OWNDBLIST,"GET");
			method.put(ServiceActionConstants.GET_SHAREDDBLIST,"GET");
			method.put(ServiceActionConstants.GET_MODULES,"GET");
			method.put(ServiceActionConstants.GET_FIELDS,"GET");
			method.put(ServiceActionConstants.FETCH_DATA,"GET");
			method.put(ServiceActionConstants.GET_DELETED_RECORDS,"GET");

			//Codes

			code.put(ServiceCode.UNAUTHORISED_ACCESS,"1030");
			code.put(ServiceCode.INVALID_SCOPE,"2945");

			filterOperatorMap.put(ConstantHolder.FilterOperator.EQUALS, "==");
			filterOperatorMap.put(ConstantHolder.FilterOperator.NOT_EQUALS, "!=");

			//Text Operators
			filterOperatorMap.put(ConstantHolder.FilterOperator.CONTAINS, ".contains");
			filterOperatorMap.put(ConstantHolder.FilterOperator.BEGINNING_WITH, ".startsWith");

			//Number Operators
			filterOperatorMap.put(ConstantHolder.FilterOperator.GREATER_EQUAL, ">=");
			filterOperatorMap.put(ConstantHolder.FilterOperator.GREATER_THAN, ">");
			filterOperatorMap.put(ConstantHolder.FilterOperator.LESS_EQUAL, "<=");
			filterOperatorMap.put(ConstantHolder.FilterOperator.LESS_THAN, "<");

			//Date Operators
			filterOperatorMap.put(ConstantHolder.FilterOperator.BEFORE, "<");
			filterOperatorMap.put(ConstantHolder.FilterOperator.AFTER, ">");

		}
}
