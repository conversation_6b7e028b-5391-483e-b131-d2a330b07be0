package com.zoho.sheet.action.externaldata.constants.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.AppServiceInfo;
import com.zoho.sheet.action.externaldata.constants.ServiceActionConstants;
import com.zoho.sheet.action.externaldata.core.parser.ContentParser;
import com.zoho.sheet.action.externaldata.core.parser.DCParserFactory;
import com.zoho.sheet.action.externaldata.core.parser.JSONParser.ParseFieldContent;
import org.json.JSONObject;

import java.util.*;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.MODULES;
import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.FIELDS;

/**
 * Created by krishnan-zt276 on  10/03/21
 */
public class FreshBooksConst extends AppServiceInfo {

    public final Map<String, Map<String, ParseFieldContent<JSONObjectWrapper>>> parseMap = new HashMap<>();

    public FreshBooksConst() {

        name = "FRESHBOOKS"; //NO I18N
        this.isInternalService = false;
        displayName = "FreshBooks"; //NO I18N

        contentParser = DCParserFactory.getContentParser(ExternalDataUtils.FTYPE.JSON);

        url.put(ServiceActionConstants.FETCH_DATA, "accounting/account/{0}/{1}/{2}");
        url.put(ServiceActionConstants.GET_OWNDBLIST,"auth/api/v1/users/me");

        method.put(ServiceActionConstants.FETCH_DATA,"GET");
        method.put(ServiceActionConstants.GET_OWNDBLIST,"GET");

        Map moduleFields = new LinkedHashMap<>();
        List moduleList = new ArrayList(2);
        moduleList.add("invoices");
        moduleList.add("invoices");

        Map<String, ParseFieldContent<JSONObjectWrapper>> fieldParse = new HashMap<>();
        fieldParse.put("invoiceid", invoice -> String.valueOf(invoice.optInt("invoiceid")));
        fieldParse.put("id", invoice -> String.valueOf(invoice.optInt("id")));
        fieldParse.put("accounting_systemid", invoice -> invoice.optString("accounting_systemid"));
        fieldParse.put("accountid", invoice -> invoice.optString("accountid"));
        fieldParse.put("amount", invoice -> invoice.getJSONObject("amount").optString("amount"));
        fieldParse.put("paid", invoice -> invoice.getJSONObject("paid").optString("amount"));
        fieldParse.put("outstanding", invoice -> invoice.getJSONObject("outstanding").optString("amount"));
        fieldParse.put("discount_total", invoice -> invoice.getJSONObject("discount_total").optString("amount"));
        fieldParse.put("description", invoice -> invoice.optString("description"));
        fieldParse.put("current_organization", invoice -> invoice.optString("current_organization"));
        fieldParse.put("due_date", invoice -> invoice.optString("due_date"));
        fieldParse.put("date_paid", invoice -> invoice.optString("date_paid"));

        parseMap.put("invoices", fieldParse);

        moduleFields.put(MODULES, moduleList);
        moduleFields.put(FIELDS, fieldParse.keySet());
        moduleFieldList.add(moduleFields);

        moduleFields = new LinkedHashMap<>();
        moduleList = new ArrayList(2);
        moduleList.add("expenses");
        moduleList.add("expenses");

        fieldParse = new HashMap<>();
        fieldParse.put("categoryid", expense -> String.valueOf(expense.optInt("categoryid")));
        fieldParse.put("markup_percent", expense -> expense.optString("markup_percent"));
        fieldParse.put("projectid", expense -> String.valueOf(expense.optInt("projectid")));
        fieldParse.put("clientid", expense -> String.valueOf(expense.optInt("clientid")));
        fieldParse.put("taxPercent1", expense -> expense.optString("taxPercent1"));
        fieldParse.put("taxName1", expense -> expense.optString("taxName1"));
        fieldParse.put("taxPercent2", expense -> expense.optString("taxPercent2"));
        fieldParse.put("taxName2", expense -> expense.optString("taxName2"));
        fieldParse.put("isduplicate", expense -> String.valueOf(expense.optBoolean("isduplicate")));
        fieldParse.put("profileid", expense -> String.valueOf(expense.optInt("profileid")));
        fieldParse.put("account_name", expense -> expense.optString("account_name"));
        fieldParse.put("transactionid", expense -> String.valueOf(expense.optInt("transactionid")));
        fieldParse.put("invoiceid", expense -> String.valueOf(expense.optInt("invoiceid")));
        fieldParse.put("id", expense -> String.valueOf(expense.optInt("id")));
        fieldParse.put("taxAmount2", expense -> expense.getJSONObject("taxAmount2").optString("amount"));
        fieldParse.put("taxAmount1", expense -> expense.getJSONObject("taxAmount1").optString("amount"));
        fieldParse.put("status", expense -> String.valueOf(expense.optInt("status")));
        fieldParse.put("bank_name", expense -> expense.optString("bank_name"));
        fieldParse.put("updated", expense -> expense.optString("updated"));
        fieldParse.put("vendor", expense -> expense.optString("vendor"));
        fieldParse.put("ext_systemid", expense -> String.valueOf(expense.optInt("ext_systemid")));
        fieldParse.put("staffid", expense -> String.valueOf(expense.optInt("staffid")));
        fieldParse.put("date", expense -> expense.optString("date"));
        fieldParse.put("has_receipt", expense -> String.valueOf(expense.optBoolean("has_receipt")));
        fieldParse.put("accounting_systemid", expense -> expense.optString("accounting_systemid"));
        fieldParse.put("notes", expense -> expense.optString("notes"));
        fieldParse.put("ext_invoiceid", expense -> String.valueOf(expense.optInt("ext_invoiceid")));
        fieldParse.put("amount", expense -> expense.getJSONObject("amount").optString("amount"));
        fieldParse.put("expenseid", expense -> String.valueOf(expense.optInt("expenseid")));
        fieldParse.put("compounded_tax", expense -> String.valueOf(expense.optBoolean("compounded_tax")));
        fieldParse.put("accountid", expense -> String.valueOf(expense.optInt("accountid")));

        parseMap.put("expenses", fieldParse);

        moduleFields.put(MODULES, moduleList);
        moduleFields.put(FIELDS, fieldParse.keySet());
        moduleFieldList.add(moduleFields);

        moduleFields = new LinkedHashMap<>();
        moduleList = new ArrayList(2);
        moduleList.add("estimates");
        moduleList.add("estimates");

        fieldParse = new HashMap<>();
        fieldParse.put("estimateid", estimates -> estimates.optString("estimateid"));
        fieldParse.put("id", estimates -> String.valueOf(estimates.optInt("id")));
        fieldParse.put("accounting_systemid", estimates -> estimates.optString("accounting_systemid"));
        fieldParse.put("ui_status", estimates -> estimates.optString("ui_status"));
        fieldParse.put("status", estimates -> estimates.optString("status"));
        fieldParse.put("amount", estimates -> estimates.getJSONObject("amount").optString("amount"));
        fieldParse.put("discount_total", estimates -> estimates.getJSONObject("discount_total").optString("amount"));
        fieldParse.put("description", estimates -> estimates.optString("description"));
        fieldParse.put("current_organization", estimates -> estimates.optString("current_organization"));
        fieldParse.put("invoiced", estimates -> String.valueOf(estimates.optBoolean("invoiced")));

        parseMap.put("estimates", fieldParse);

        moduleFields.put(MODULES, moduleList);
        moduleFields.put(FIELDS, fieldParse.keySet());
        moduleFieldList.add(moduleFields);

        moduleFields = new LinkedHashMap<>();
        moduleList = new ArrayList(2);
        moduleList.add("staffs");
        moduleList.add("staffs");

        fieldParse = new HashMap<>();
        fieldParse.put("fax", staffs -> staffs.optString("fax"));
        fieldParse.put("rate", staffs -> staffs.optString("rate"));
        fieldParse.put("num_logins", staffs -> String.valueOf(staffs.optInt("num_logins")));
        fieldParse.put("id", staffs -> String.valueOf(staffs.optInt("id")));
        fieldParse.put("note", staffs -> staffs.optString("note"));
        fieldParse.put("display_name", staffs -> staffs.optString("display_name"));
        fieldParse.put("lname", staffs -> staffs.optString("lname"));
        fieldParse.put("mob_phone", staffs -> staffs.optString("mob_phone"));
        fieldParse.put("last_login", staffs -> staffs.optString("last_login"));
        fieldParse.put("home_phone", staffs -> staffs.optString("home_phone"));
        fieldParse.put("email", staffs -> staffs.optString("email"));
        fieldParse.put("username", staffs -> staffs.optString("username"));
        fieldParse.put("updated", staffs -> staffs.optString("updated"));
        fieldParse.put("p_province", staffs -> staffs.optString("p_province"));
        fieldParse.put("p_city", staffs -> staffs.optString("p_city"));
        fieldParse.put("p_code", staffs -> staffs.optString("p_code"));
        fieldParse.put("p_country", staffs -> staffs.optString("p_country"));
        fieldParse.put("accounting_systemid", staffs -> staffs.optString("accounting_systemid"));
        fieldParse.put("bus_phone", staffs -> staffs.optString("bus_phone"));
        fieldParse.put("signup_date", staffs -> staffs.optString("signup_date"));
        fieldParse.put("language", staffs -> staffs.optString("language"));
        fieldParse.put("level", staffs -> String.valueOf(staffs.optInt("level")));
        fieldParse.put("userid", staffs -> String.valueOf(staffs.optInt("userid")));
        fieldParse.put("p_street2", staffs -> staffs.optString("p_street2"));
        fieldParse.put("fname", staffs -> staffs.optString("fname"));
        fieldParse.put("organization", staffs -> staffs.optString("organization"));
        fieldParse.put("p_street", staffs -> staffs.optString("p_street"));
        fieldParse.put("currency_code", staffs -> staffs.optString("currency_code"));

        parseMap.put("staffs", fieldParse);

        moduleFields.put(MODULES, moduleList);
        moduleFields.put(FIELDS, fieldParse.keySet());
        moduleFieldList.add(moduleFields);

        moduleFields = new LinkedHashMap<>();
        moduleList = new ArrayList(2);
        moduleList.add("clients");
        moduleList.add("clients");

        fieldParse = new HashMap<>();
        fieldParse.put("accounting_systemid", clients -> clients.optString("accounting_systemid"));
        fieldParse.put("allow_late_fees", clients -> String.valueOf(clients.optBoolean("allow_late_fees")));
        fieldParse.put("allow_late_notifications", clients -> String.valueOf(clients.optBoolean("allow_late_notifications")));
        fieldParse.put("bus_phone", clients -> clients.optString("bus_phone"));
        fieldParse.put("company_industry", clients -> clients.optString("company_industry"));
        fieldParse.put("company_size", clients -> clients.optString("company_size"));
        fieldParse.put("currency_code", clients -> clients.optString("currency_code"));
        fieldParse.put("email", clients -> clients.optString("email"));
        fieldParse.put("fax", clients -> clients.optString("fax"));
        fieldParse.put("fname", clients -> clients.optString("fname"));
        fieldParse.put("home_phone", clients -> clients.optString("home_phone"));
        fieldParse.put("id", clients -> String.valueOf(clients.optInt("id")));
        fieldParse.put("language", clients ->  clients.optString("language"));
        fieldParse.put("last_activity", clients ->  clients.optString("last_activity"));
        fieldParse.put("last_login", clients ->  clients.optString("last_login"));
        fieldParse.put("level", clients -> String.valueOf(clients.optInt("level")));
        fieldParse.put("lname", clients ->  clients.optString("lname"));
        fieldParse.put("mob_phone", clients ->  clients.optString("mob_phone"));
        fieldParse.put("note", clients ->  clients.optString("note"));
        fieldParse.put("notified", clients -> String.valueOf(clients.optBoolean("notified")));
        fieldParse.put("num_logins", clients -> String.valueOf(clients.optInt("num_logins")));
        fieldParse.put("organization", clients ->  clients.optString("organization"));
        fieldParse.put("p_city", clients ->  clients.optString("p_city"));
        fieldParse.put("p_code", clients ->  clients.optString("p_code"));
        fieldParse.put("p_country", clients ->  clients.optString("p_country"));
        fieldParse.put("p_province", clients ->  clients.optString("p_province"));
        fieldParse.put("p_street", clients ->  clients.optString("p_street"));
        fieldParse.put("p_street2", clients ->  clients.optString("p_street2"));
        fieldParse.put("pref_email", clients -> String.valueOf(clients.optBoolean("pref_email")));
        fieldParse.put("pref_gmail", clients -> String.valueOf(clients.optBoolean("pref_gmail")));
        fieldParse.put("s_city", clients -> clients.optString("s_city"));
        fieldParse.put("s_code", clients -> clients.optString("s_code"));
        fieldParse.put("s_country", clients -> clients.optString("s_country"));
        fieldParse.put("s_province", clients -> clients.optString("s_province"));
        fieldParse.put("s_street", clients -> clients.optString("s_street"));
        fieldParse.put("s_street2", clients -> clients.optString("s_street2"));
        fieldParse.put("signup_date", clients -> clients.optString("signup_date"));
        fieldParse.put("updated", clients -> clients.optString("updated"));
        fieldParse.put("userid", clients -> String.valueOf(clients.optInt("userid")));
        fieldParse.put("username", clients -> clients.optString("username"));
        fieldParse.put("vat_name", clients -> clients.optString("vat_name"));
        fieldParse.put("vat_number", clients -> clients.optString("vat_number"));


        parseMap.put("clients", fieldParse);

        moduleFields.put(MODULES, moduleList);
        moduleFields.put(FIELDS, fieldParse.keySet());
        moduleFieldList.add(moduleFields);

        moduleFields = new LinkedHashMap<>();
        moduleList = new ArrayList(2);
        moduleList.add("taxes");
        moduleList.add("taxes");

        fieldParse = new HashMap<>();
        fieldParse.put("accounting_systemid", taxes -> taxes.optString("accounting_systemid"));
        fieldParse.put("updated", taxes -> taxes.optString("updated"));
        fieldParse.put("name", taxes -> taxes.optString("name"));
        fieldParse.put("number", taxes -> taxes.optString("number"));
        fieldParse.put("taxid", taxes -> String.valueOf(taxes.optInt("taxid")));
        fieldParse.put("amount", taxes -> taxes.optString("amount"));
        fieldParse.put("compound", taxes -> String.valueOf(taxes.optBoolean("compound")));
        fieldParse.put("id", taxes -> String.valueOf(taxes.optInt("id")));

        parseMap.put("taxes", fieldParse);

        moduleFields.put(MODULES, moduleList);
        moduleFields.put(FIELDS, fieldParse.keySet());
        moduleFieldList.add(moduleFields);

        moduleFields = new LinkedHashMap<>();
        moduleList = new ArrayList(2);
        moduleList.add("payments");
        moduleList.add("payments");

        fieldParse = new HashMap<>();
        fieldParse.put("orderid", payments -> String.valueOf(payments.optInt("orderid")));
        fieldParse.put("accounting_systemid", payments -> payments.optString("accounting_systemid"));
        fieldParse.put("updated", payments -> payments.optString("updated"));
        fieldParse.put("invoiceid", payments -> String.valueOf(payments.optInt("invoiceid")));
        fieldParse.put("creditid", payments -> String.valueOf(payments.optInt("creditid")));
        fieldParse.put("amount", payments -> payments.getJSONObject("amount").optString("amount"));
        fieldParse.put("clientid", payments -> String.valueOf(payments.optInt("clientid")));
        fieldParse.put("logid", payments -> String.valueOf(payments.optInt("logid")));
        fieldParse.put("note", payments -> payments.optString("note"));
        fieldParse.put("overpaymentid", payments -> String.valueOf(payments.optInt("overpaymentid")));
        fieldParse.put("gateway", payments -> payments.optString("gateway"));
        fieldParse.put("date", payments -> payments.optString("date"));
        fieldParse.put("transactionid", payments -> String.valueOf(payments.optInt("transactionid")));
        fieldParse.put("from_credit", payments -> String.valueOf(payments.optBoolean("from_credit")));
        fieldParse.put("type", payments -> payments.optString("type"));
        fieldParse.put("send_client_notification", payments -> String.valueOf(payments.optBoolean("send_client_notification")));
        fieldParse.put("id", payments -> String.valueOf(payments.optInt("id")));

        parseMap.put("payments", fieldParse);

        moduleFields.put(MODULES, moduleList);
        moduleFields.put(FIELDS, fieldParse.keySet());
        moduleFieldList.add(moduleFields);

        moduleFields = new LinkedHashMap<>();
        moduleList = new ArrayList(2);
        moduleList.add("invoice_profiles");
        moduleList.add("invoice_profiles");

        fieldParse = new HashMap<>();
        fieldParse.put("id", invoice_profiles -> String.valueOf(invoice_profiles.optInt("id")));
        fieldParse.put("ownerid", invoice_profiles -> String.valueOf(invoice_profiles.optInt("ownerid")));
        fieldParse.put("updated", invoice_profiles -> invoice_profiles.optString("updated"));
        fieldParse.put("discount_total", invoice_profiles -> invoice_profiles.getJSONObject("discount_total").optString("amount"));
        fieldParse.put("occurrences_to_date", invoice_profiles -> String.valueOf(invoice_profiles.optInt("occurrences_to_date")));
        fieldParse.put("amount", invoice_profiles -> invoice_profiles.getJSONObject("amount").optString("amount"));
        fieldParse.put("profileid", invoice_profiles -> String.valueOf(invoice_profiles.optInt("profileid")));

        parseMap.put("invoice_profiles", fieldParse);

        moduleFields.put(MODULES, moduleList);
        moduleFields.put(FIELDS, fieldParse.keySet());
        moduleFieldList.add(moduleFields);
    }

    public Map<String, Map<String, ParseFieldContent<JSONObjectWrapper>>> getParseMap() {
        return parseMap;
    }
}
