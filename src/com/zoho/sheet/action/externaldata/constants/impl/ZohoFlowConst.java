//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.constants.impl;

import java.util.LinkedHashMap;
import java.util.Map;

import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.action.externaldata.constants.ServiceInfo;

import com.zoho.sheet.action.externaldata.core.parser.ContentParser;


/**
 * <AUTHOR>
 *
 */
public class ZohoFlowConst implements ServiceInfo{
	
	public static final String NAME = "ZFLOW"; //NO I18N
	//private static final String DOMAIN_URL = "http://sudalai-1027.csez.zohocorpin.com:8080/"; //NO I18N
	//private static final String DOMAIN_URL = "https://flow.csez.zohocorpin.com/"; //NO I18N
	//private static final String domainURL_dev = "https://flow.csez.zohocorpin.com/"; //NO I18N
	//private static final String domainURL_local = "https://flow.localzoho.com/"; //NO I18N
	
	private static final String SERVICE_NAME = "ZohoSheet"; //NO I18N
	
	//public static final String URL = "ZohoSheet"; //NO I18N
	
	
	public static final int CREATE_ORG = 0;
	public static final int CREATE_CONNECTION = 1;
	public static final int GET_ACCESS_TOKEN = 2;
	public static final int DELETE_CONNECTION = 3;
	public static final int EDIT_CONNECTION = 4;
	public static final int CONNECTION_STATUS = 5;
	public static final int REVOKE_CONNECTION = 6;
	public static final int GET_ALL_SERVICES = 7;
	public static final int GET_CONNECTION_SCOPE = 8;
	public static final int ADD_USER = 9;
//	public static final int ENABLE_ORG = 10;
//	public static final int DISABLE_ORG = 11;

	
	private static final Map <Integer, String> URL = new LinkedHashMap<>();
	private static final Map <Integer, String> METHOD = new LinkedHashMap<>();
			
	static {
		URL.put(CREATE_ORG,"api/org?");
		URL.put(CREATE_CONNECTION,"api/connections/new?");
		URL.put(GET_ACCESS_TOKEN,"api/connections/{0}/authDetails?");
		URL.put(REVOKE_CONNECTION,"api/connections/{0}/revoke?");
		URL.put(DELETE_CONNECTION,"api/connections/{0}?");
		URL.put(EDIT_CONNECTION,"api/connections/{0}?");
		URL.put(CONNECTION_STATUS,"api/connections/{0}/status?");
		URL.put(GET_ALL_SERVICES,"api/services?");
		URL.put(GET_CONNECTION_SCOPE,"api/connections/{0}?");
		URL.put(ADD_USER,"api/users/add?");
//		URL.put(ENABLE_ORG,"api/org/enable?");
//		URL.put(DISABLE_ORG,"api/org/disable?");
	}
	
	static {
		METHOD.put(CREATE_ORG, "POST");
		METHOD.put(CREATE_CONNECTION, "POST");
		METHOD.put(GET_ACCESS_TOKEN, "POST");
		METHOD.put(REVOKE_CONNECTION, "POST");
		METHOD.put(DELETE_CONNECTION, "DELETE");
		METHOD.put(EDIT_CONNECTION, "POST");
		METHOD.put(CONNECTION_STATUS, "GET");
		METHOD.put(GET_ALL_SERVICES, "GET");
		METHOD.put(GET_CONNECTION_SCOPE, "GET");
		METHOD.put(ADD_USER, "POST");
//		METHOD.put(ENABLE_ORG, "POST");
//		METHOD.put(DISABLE_ORG, "POST");
	}
	
	@Override
	public String getDomainURL() {
		
		return EnginePropertyUtil.getDataConnectionInfo(this.NAME).split(",")[0];
		//return this.DOMAIN_URL;
	}


	@Override
	public String getURL(int serviceActionConstant) {
		return URL.get(serviceActionConstant);
	}
	
	
	

	@Override
	public String getMethod(int serviceActionConstant) {
		return METHOD.get(serviceActionConstant);
	}

	@Override
	public ContentParser getContentParser() {
		return null;
	}

	@Override
	public String getCode(int serviceCode) {
		return null;
	}

	@Override
	public String getRecordIdLabel(String module) {
		return null;
	}

	public boolean isInternalService()
	{
		return true;
	}


	@Override
	public String getName() {
		// TODO Auto-generated method stub
		return null;
	}


	

}
