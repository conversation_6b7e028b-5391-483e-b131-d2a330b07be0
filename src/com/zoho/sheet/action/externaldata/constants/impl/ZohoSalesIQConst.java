package com.zoho.sheet.action.externaldata.constants.impl;

import com.zoho.sheet.action.externaldata.constants.AppServiceInfo;
import com.zoho.sheet.action.externaldata.constants.ConstantHolder;
import com.zoho.sheet.action.externaldata.constants.ServiceActionConstants;
import com.zoho.sheet.action.externaldata.constants.ServiceCode;

import java.util.*;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;

public class ZohoSalesIQConst extends AppServiceInfo {

    private static final Map<String, ConstantHolder.FilterType> OPERATORS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> DEPARTMENTS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> APPS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> CONVERSATIONS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> FEEDBACKS = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> ARTICLES = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> ARTICLE_CATEGORIES = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> CANNED_RESPONSES = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> CANNED_RESPONSE_CATEGORIES = new LinkedHashMap<>();
    private static final Map<String, ConstantHolder.FilterType> BLOCKED_IPS = new LinkedHashMap<>();

    static {

        OPERATORS.put("id", ConstantHolder.FilterType.NONE);
        OPERATORS.put("first_name", ConstantHolder.FilterType.NONE);
        OPERATORS.put("last_name", ConstantHolder.FilterType.NONE);
        OPERATORS.put("nick_name", ConstantHolder.FilterType.NONE);
        OPERATORS.put("role", ConstantHolder.FilterType.NONE);
        OPERATORS.put("email_id", ConstantHolder.FilterType.NONE);
        OPERATORS.put("is_owner", ConstantHolder.FilterType.NONE);
        OPERATORS.put("is_chat_enabled", ConstantHolder.FilterType.NONE);
        OPERATORS.put("is_enabled", ConstantHolder.FilterType.NONE);
        OPERATORS.put("wms_id", ConstantHolder.FilterType.NONE);
        OPERATORS.put("status_code", ConstantHolder.FilterType.NONE);
        OPERATORS.put("status_message", ConstantHolder.FilterType.NONE);
        OPERATORS.put("zuid", ConstantHolder.FilterType.NONE);
        OPERATORS.put("is_confirmed", ConstantHolder.FilterType.NONE);
        OPERATORS.put("is_deleted", ConstantHolder.FilterType.NONE);
        OPERATORS.put("image_url", ConstantHolder.FilterType.NONE);
        OPERATORS.put("maximum_concurrent_chat", ConstantHolder.FilterType.NONE);
        OPERATORS.put("created_time", ConstantHolder.FilterType.NONE);
        OPERATORS.put("modifed_time", ConstantHolder.FilterType.NONE);


        DEPARTMENTS.put("id", ConstantHolder.FilterType.NONE);
        DEPARTMENTS.put("name", ConstantHolder.FilterType.NONE);
        DEPARTMENTS.put("display_name", ConstantHolder.FilterType.NONE);
        DEPARTMENTS.put("description", ConstantHolder.FilterType.NONE);
        DEPARTMENTS.put("created_by", ConstantHolder.FilterType.NONE);
        DEPARTMENTS.put("is_enabled", ConstantHolder.FilterType.NONE);
        DEPARTMENTS.put("is_public", ConstantHolder.FilterType.NONE);
        DEPARTMENTS.put("is_system_generated", ConstantHolder.FilterType.NONE);
        DEPARTMENTS.put("created_time", ConstantHolder.FilterType.NONE);
        DEPARTMENTS.put("modified_time", ConstantHolder.FilterType.NONE);

        APPS.put("id", ConstantHolder.FilterType.NONE);
        APPS.put("name", ConstantHolder.FilterType.NONE);
        APPS.put("description", ConstantHolder.FilterType.NONE);
        APPS.put("enabled", ConstantHolder.FilterType.NONE);
        APPS.put("waiting_time", ConstantHolder.FilterType.NONE);
        APPS.put("created_time", ConstantHolder.FilterType.NONE);
        APPS.put("modified_time", ConstantHolder.FilterType.NONE);

        CONVERSATIONS.put("id", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("reference_id", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("department", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("app_name", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("owner", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("attender", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("participants", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("visitor", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("type", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("language", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("status", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("status_code", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("chat_duration", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("question", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("notes", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("attended_time", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("start_time", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("end_time", ConstantHolder.FilterType.NONE);
        CONVERSATIONS.put("last_modified_time", ConstantHolder.FilterType.NONE);

        FEEDBACKS.put("id", ConstantHolder.FilterType.NONE);
        FEEDBACKS.put("visitor", ConstantHolder.FilterType.NONE);
        FEEDBACKS.put("conversation", ConstantHolder.FilterType.NONE);
        FEEDBACKS.put("question", ConstantHolder.FilterType.NONE);
        FEEDBACKS.put("feedback", ConstantHolder.FilterType.NONE);
        FEEDBACKS.put("ratings", ConstantHolder.FilterType.NONE);
        FEEDBACKS.put("attender_id", ConstantHolder.FilterType.NONE);
        FEEDBACKS.put("department_id", ConstantHolder.FilterType.NONE);
        FEEDBACKS.put("average_response_time", ConstantHolder.FilterType.NONE);
        FEEDBACKS.put("end_time", ConstantHolder.FilterType.NONE);

        ARTICLES.put("id", ConstantHolder.FilterType.NONE);
        ARTICLES.put("title", ConstantHolder.FilterType.NONE);
        ARTICLES.put("creator", ConstantHolder.FilterType.NONE);
        ARTICLES.put("modifier", ConstantHolder.FilterType.NONE);
        ARTICLES.put("enabled", ConstantHolder.FilterType.NONE);
        ARTICLES.put("published", ConstantHolder.FilterType.NONE);
        ARTICLES.put("department_id", ConstantHolder.FilterType.NONE);
        ARTICLES.put("created_time", ConstantHolder.FilterType.NONE);
        ARTICLES.put("modified_time", ConstantHolder.FilterType.NONE);

        ARTICLE_CATEGORIES.put("id", ConstantHolder.FilterType.NONE);
        ARTICLE_CATEGORIES.put("name", ConstantHolder.FilterType.NONE);
        ARTICLE_CATEGORIES.put("order", ConstantHolder.FilterType.NONE);
        ARTICLE_CATEGORIES.put("articles_count", ConstantHolder.FilterType.NONE);
        ARTICLE_CATEGORIES.put("is_default", ConstantHolder.FilterType.NONE);

        CANNED_RESPONSES.put("id", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("creator", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("modifier", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("department_id", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("content", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("usage_count", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("public", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("enabled", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("associate_with_all_departments", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("last_used_time", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("created_time", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSES.put("modified_time", ConstantHolder.FilterType.NONE);

        CANNED_RESPONSE_CATEGORIES.put("id", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSE_CATEGORIES.put("owner", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSE_CATEGORIES.put("owner_id", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSE_CATEGORIES.put("name", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSE_CATEGORIES.put("is_default", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSE_CATEGORIES.put("created_time", ConstantHolder.FilterType.NONE);
        CANNED_RESPONSE_CATEGORIES.put("modified_time", ConstantHolder.FilterType.NONE);

        BLOCKED_IPS.put("id", ConstantHolder.FilterType.NONE);
        BLOCKED_IPS.put("app", ConstantHolder.FilterType.NONE);
        BLOCKED_IPS.put("ip", ConstantHolder.FilterType.NONE);
        BLOCKED_IPS.put("status", ConstantHolder.FilterType.NONE);
        BLOCKED_IPS.put("blocked_by", ConstantHolder.FilterType.NONE);
        BLOCKED_IPS.put("no_of_comments", ConstantHolder.FilterType.NONE);
        BLOCKED_IPS.put("modified_time", ConstantHolder.FilterType.NONE);
    }

    public ZohoSalesIQConst() {

        name = "ZSALESIQ"; //NO I18N
        this.isInternalService = true;
        displayName = "Zoho SalesIQ"; //NO I18N
        scopes =  "[\"SalesIQ.chattranscript.READ\",\"SalesIQ.portals.READ\",\"SalesIQ.conversations.READ\",\"SalesIQ.apps.READ\",\"SalesIQ.chatdetails.READ\",\"SalesIQ.articles.READ\",\"SalesIQ.operators.READ\",\"SalesIQ.departments.READ\", \"SalesIQ.feedbacks.READ\", \"SalesIQ.cannedresponses.READ\", \"SalesIQ.blockedips.READ\"]"; //NO I18N

        code.put(ServiceCode.UNAUTHORISED_ACCESS,"1002");

        url.put(ServiceActionConstants.GET_ORG,"v2/portals");
        url.put(ServiceActionConstants.FETCH_DATA,"v2/{0}/{1}");

        // Method Type
        method.put(ServiceActionConstants.GET_ORG,"GET");
        method.put(ServiceActionConstants.FETCH_DATA,"GET");

        Map moduleFields;

        //---OPERATORS
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("operators", "Operators"));
        moduleFields.put(FIELDS, OPERATORS.keySet());
        moduleFields.put(FIELD_TYPE, OPERATORS.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("operators",20);

        //---DEPARTMENTS
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("departments", "Departments"));
        moduleFields.put(FIELDS, DEPARTMENTS.keySet());
        moduleFields.put(FIELD_TYPE, DEPARTMENTS.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("departments",20);

        //---APPS
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("apps", "Apps"));
        moduleFields.put(FIELDS, APPS.keySet());
        moduleFields.put(FIELD_TYPE, APPS.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("apps",20);

        //---CONVERSATIONS
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("conversations", "Conversations"));
        moduleFields.put(FIELDS, CONVERSATIONS.keySet());
        moduleFields.put(FIELD_TYPE, CONVERSATIONS.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("conversations",99);

        //---FEEDBACKS
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("feedbacks", "Feedbacks"));
        moduleFields.put(FIELDS, FEEDBACKS.keySet());
        moduleFields.put(FIELD_TYPE, FEEDBACKS.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("feedbacks",99);

        //---ARTICLES
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("articles", "Articles"));
        moduleFields.put(FIELDS, ARTICLES.keySet());
        moduleFields.put(FIELD_TYPE, ARTICLES.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("articles",99);

        //---ARTICLE_CATEGORIES
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("articlecategories", "Article Categories"));
        moduleFields.put(FIELDS, ARTICLE_CATEGORIES.keySet());
        moduleFields.put(FIELD_TYPE, ARTICLE_CATEGORIES.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("articlecategories",99);

        //---CANNED_RESPONSES
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("cannedresponses", "Canned Responses"));
        moduleFields.put(FIELDS, CANNED_RESPONSES.keySet());
        moduleFields.put(FIELD_TYPE, CANNED_RESPONSES.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("cannedresponses",99);

        //---CANNED_RESPONSE_CATEGORIES
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("cannedresponsecategories", "Canned Response Categories"));
        moduleFields.put(FIELDS, CANNED_RESPONSE_CATEGORIES.keySet());
        moduleFields.put(FIELD_TYPE, CANNED_RESPONSE_CATEGORIES.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("cannedresponsecategories",99);

        //---BLOCKED_IPS
        moduleFields = new LinkedHashMap<>();
        moduleFields.put(MODULES, Arrays.asList("blockedips", "Blocked IPs"));
        moduleFields.put(FIELDS, BLOCKED_IPS.keySet());
        moduleFields.put(FIELD_TYPE, BLOCKED_IPS.values());
        moduleFieldList.add(moduleFields);
        fetchLimit.put("blockedips",20);


    }
}
