//$Id$
/**
 *
 */
package com.zoho.sheet.action.externaldata.actionmanager;

import java.net.HttpURLConnection;
import java.net.URLDecoder;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.ear.common.util.EARException;
import com.zoho.security.api.wrapper.URLWrapper;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.Task.TaskManager;
import com.zoho.sheet.action.externaldata.connectionHelper.impl.ZohoFlow;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.constants.impl.ZohoFlowConst;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.action.externaldata.core.objprovider.FactoryFinder;
import com.zoho.sheet.action.externaldata.datastorage.RWManager;
import com.zoho.sheet.action.externaldata.metastorage.DBActionManager;
import com.zoho.sheet.action.externaldata.priceplan.DCPlanConstructor;
import com.zoho.sheet.action.externaldata.priceplan.UserPolicyDecider;
import com.zoho.sheet.admin.ZSAdminPanelUtils;
import com.zoho.sheet.connection.ZSConnection;
import com.zoho.sheet.connection.ZSConnectionFactory;
import com.zoho.sheet.connection.ZSheetUrlWrapperConnection;
import com.zoho.sheet.util.*;
import com.zoho.zfsng.client.ZohoFS;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import org.htmlparser.Node;
import org.htmlparser.NodeFilter;
import org.htmlparser.Parser;
import org.htmlparser.filters.*;
import org.htmlparser.nodes.TagNode;
import org.htmlparser.tags.TableRow;
import org.htmlparser.tags.TableTag;
import org.htmlparser.util.EncodingChangeException;
import org.htmlparser.util.NodeList;
import org.htmlparser.util.ParserException;

import javax.net.ssl.HttpsURLConnection;


/**
 * <AUTHOR>
 *
 */
public class ExternalDataActionManager {
	private static Logger logger = Logger.getLogger(ExternalDataActionManager.class.getName());


	public static JSONObjectWrapper authenticateService(String webOwner, String webOwnerZUID, long flowOrgID, int serviceConstant ,String zsoid, String resourceId, String rsid) throws DataConnection.DataConnectionFailedException, EARException {

		HashMap policyDetails = UserPolicyDecider.getPolicy(resourceId,  webOwnerZUID);

		DataObject connectionDObj = DBActionManager.getConnectionListDBObj(webOwner, webOwnerZUID, flowOrgID);
		int connectionCount = connectionDObj.size("ConnectedServices");
		Integer maxCountByPolicy = (Integer) policyDetails.get(DCPlanConstructor.DCAction.CONNECTION_COUNT);
		if(maxCountByPolicy <= connectionCount){
			throw new DataConnection.DataConnectionFailedException(ErrorCode.CONNECTION_EXCEED, maxCountByPolicy.toString());
		}
		try {
			if(connectionCount < 1) {
				ZohoFlow flowConnector = new ZohoFlow();
				flowConnector.addOrgUser(zsoid, webOwnerZUID);
			}
		}
		catch (Exception e) {
			logger.log(Level.WARNING, "[Data Connection] error occurred while adding user to flowOrg, ZSOID : {0} ZUID : {1}, {2}", new Object[]{zsoid, webOwnerZUID, e});
		}

        JSONObjectWrapper resp = FlowActionManager.authenticateService(webOwner,webOwnerZUID, flowOrgID, Long.parseLong(zsoid), resourceId, serviceConstant, rsid);
		DataObject connectedWebDataDObj = DBActionManager.getOwnerActiveDataLinks(zsoid , webOwner, serviceConstant);
		int connectedLinkCount = connectedWebDataDObj.isEmpty() ? 0 : connectedWebDataDObj.size("WebData");
		resp.put(JSONConstants.CONNECTED_LINK_COUNT, connectedLinkCount);
		return resp;
	}

	public static JSONObjectWrapper getConnectedList(JSONObjectWrapper jObj, WorkbookContainer container) {

		Long flowOrgID;
        try {
			String zsoid = ZohoFS.getResourceInfo(container.getResourceId()).getOwner();
			flowOrgID = DBActionManager.getFlowOrgID_FromDB(zsoid, jObj.getString(JSONConstants.USER_NAME), jObj.getString(JSONConstants.ZUID));
        } catch (Exception e) {
            logger.log(Level.WARNING, "[Data Connection] error while fetching FlowOrgID", e);
			throw new NullPointerException();
        }

		JSONObjectWrapper toRet = new JSONObjectWrapper();
		HashMap policyDetails = UserPolicyDecider.getPolicy(container.getResourceId(),  jObj.getString(JSONConstants.ZUID));
		Integer maxLinkCountByPolicy = (Integer) policyDetails.get(DCPlanConstructor.DCAction.LINK_COUNT);
		Boolean hasHourlyScheduler = (Boolean) policyDetails.get(DCPlanConstructor.DCAction.HOURLY_SCHEDULER);
		toRet.put("max_link_cnt", maxLinkCountByPolicy);
		toRet.put("has_hrly_scheduler", hasHourlyScheduler);
		toRet.put(JSONConstants.FLOW_ORGID, flowOrgID);
		boolean is_connections_enabled = ExternalDataUtils.isDataConnectionEnable(container.getDocOwner(), jObj.getString(JSONConstants.USER_NAME), jObj.getString(JSONConstants.ZUID));
		toRet.put("is_data_connection_enabled", is_connections_enabled);
		try {
			toRet.put("is_Admin", ExternalDataUtils.isWorkDriveAdmin(Long.parseLong(container.getDocOwner()), Long.parseLong(jObj.getString(JSONConstants.ZUID))));
		} catch (Exception e) {
			logger.log(Level.WARNING, "[Data Connection] Error occurred while fetching AdminStatus for ZSOID: {0} >>> ZUID: {1}, {2}", new Object[]{container.getDocOwner(), jObj.getString(JSONConstants.ZUID), e});
		}

		ZohoFlowConst flowInfo = (ZohoFlowConst)ConstantHolder.getServiceInfo(ServiceConstant.ZFLOW);
		String flowURL = flowInfo.getDomainURL();
		//We Should not hardcore here.~Mani
		if(flowURL.contains("flowapi")){
			flowURL = flowURL.replace("flowapi","flow");
		}
		toRet.put("flowURL", flowURL);

		DataObject dObj = DBActionManager.getConnectionListDBObj(jObj.getString(JSONConstants.USER_NAME), jObj.getString(JSONConstants.ZUID), flowOrgID);
		if(dObj==null || dObj.isEmpty()) {
			return toRet;
		}
		ExternalDataUtils.checkActiveDocDataLink(container.getDocOwner(), container.getDocOwnerZUID(), jObj.getString(JSONConstants.USER_NAME));
		JSONArrayWrapper connectedServicesList = new JSONArrayWrapper();
		try {
			Iterator connectedServiceRows = dObj.getRows("ConnectedServices");
			Map<Integer, List<String>> serviceConstantToConnectionIDs = new HashMap<>();
			while(connectedServiceRows.hasNext())
			{
				Row row = (Row)connectedServiceRows.next();
				if (row != null && (int)row.get("SERVICE_CONSTANT") > 0) {
					int serviceConstant = (int)row.get("SERVICE_CONSTANT");
					JSONObjectWrapper respObj = new JSONObjectWrapper();

					ServiceInfoImpl serviceInfo = ((ServiceInfoImpl) ConstantHolder.getServiceInfo(serviceConstant));
					if(serviceInfo == null)
					{
						continue;
					}

					String connectionID = ""+row.get("CONNECTION_ID");
					respObj.put(JSONConstants.CONNECTION_ID, connectionID);
					respObj.put(JSONConstants.SERVICE_NAME, row.get("SERVICE_NAME"));
					respObj.put(JSONConstants.DISPLAY_NAME, serviceInfo.getDisplayName());
					respObj.put(JSONConstants.SERVICE_CONSTANT, serviceConstant);
					respObj.put(JSONConstants.CONNECTION_UNIQUE_KEY, row.get("CONNECTION_UNIQUE_KEY"));
					respObj.put(JSONConstants.MULTI_CONNECTION_SUPPORT, serviceInfo.isMultiConnectionSupport());

					if(!serviceInfo.isMultiConnectionSupport())
					{
						serviceConstantToConnectionIDs.computeIfAbsent(serviceConstant, k -> new ArrayList<>()).add(connectionID);
					}


					respObj.put("authorize_url", row.get("AUTHORIZE_URL"));

					respObj.put("is_auth", row.get("AUTHORIZED"));
					DataObject connectedWebDataDObj = DBActionManager.getOwnerActiveDataLinks(container.getDocOwner() , jObj.getString(JSONConstants.USER_NAME), serviceConstant);
					int connectedLinkCount = connectedWebDataDObj.isEmpty() ? 0 : connectedWebDataDObj.size("WebData");
					respObj.put(JSONConstants.CONNECTED_LINK_COUNT, connectedLinkCount);




					int fetchCount = DBActionManager.getFetchCountInfo(jObj.getString(JSONConstants.USER_NAME), serviceConstant);
					if(fetchCount >= ConstantHolder.MAX_REFRESH_LIMIT_IN_24HRS)
					{
						WorkbookAdditionalInfo additionalInfo =container.getWorkbookAdditionalInfo();
						long time = DBActionManager.getFirstConnectedTime(jObj.getString(JSONConstants.USER_NAME), serviceConstant) + 86400000;
						respObj.put(JSONConstants.FETCH_ENABLE_TIME, ClientUtils.getUserTimeZoneTime1(time, additionalInfo.getSpreadsheetSettings().getLocale(), additionalInfo.getTimeZone()));
					}
					connectedServicesList.put(respObj);
				}
			}
			for (Map.Entry<Integer, List<String>> entry : serviceConstantToConnectionIDs.entrySet())
			{
				List<String> connectionIDs = entry.getValue();
				int connectionIDsCount = connectionIDs.size();
				if(connectionIDsCount > 1)
				{
					logger.log(Level.INFO, "[Data Connection] duplicate connections for >>> ZUID >> {0} ServiceConst >> {1} ConnectionIDs >> {2}", new Object[]{jObj.optString("zuid"), entry.getKey(), entry.getValue()});
					ZohoFlow flowConnector = new ZohoFlow();
					for(int i = 0; i < connectionIDsCount - 1; i++)
					{
						long connectionID = Long.parseLong(connectionIDs.get(i));
						JSONArrayWrapper respArray = flowConnector.revokeConnection(entry.getKey(), connectionID, flowOrgID, jObj.getString(JSONConstants.ZUID), jObj.getString(JSONConstants.USER_NAME));
						JSONObjectWrapper respObj = respArray.getJSONObject(0);
						if ("success".equals(respObj.optString("status")) || respObj.optInt("code", -1) == 2026) {
							DBActionManager.deleteConnection(jObj.getString(JSONConstants.USER_NAME), jObj.getString(JSONConstants.ZUID), flowOrgID, connectionID);
							flowConnector.deleteConnection(entry.getKey(), connectionID, flowOrgID, jObj.getString(JSONConstants.ZUID), jObj.getString(JSONConstants.USER_NAME));
							logger.log(Level.INFO, "[Data Connection] deleted connectionId : {0}", connectionID);
						}
					}
				}
			}
		}catch(Exception e) {
			logger.log(Level.WARNING,"getConnectedList ",e);
		}
		toRet.put("ConnectedServices", connectedServicesList);
		return toRet;
	}
	public static JSONObjectWrapper getServiceConnectedDocList(JSONObjectWrapper jObj, String docOwner) {
		int serviceConstant = jObj.getInt(JSONConstants.SERVICE_CONSTANT);
		DataObject connectedWebDataDObj = DBActionManager.getOwnerActiveDataLinks(docOwner, jObj.getString(JSONConstants.USER_NAME), serviceConstant);
		JSONObjectWrapper resp = new JSONObjectWrapper();
		JSONObjectWrapper docLinkInfo = new JSONObjectWrapper();
		Map<String, Set<String>> docToOrgInfo = new HashMap<>();
		try{
			JSONObjectWrapper docJson = null;
			int connectedLinkCount = connectedWebDataDObj.isEmpty() ? 0 : connectedWebDataDObj.size("WebData");
			resp.put(JSONConstants.CONNECTED_LINK_COUNT, connectedLinkCount);
			Iterator iter = connectedWebDataDObj.getRows( "WebData");
			while(iter.hasNext()){
				Row webdataRow = (Row)iter.next();
				String documentId = String.valueOf( webdataRow.get("DOCUMENT_ID"));
				docJson = docLinkInfo.optJSONObject(documentId);
				if(docJson == null){
					try {
						DataObject dataObject = DocumentUtils.getDocumentsDO(documentId, docOwner);
						if (!dataObject.isEmpty()) {
							Row documentRow 	= dataObject.getRow("Documents");
							String resourceId = (String) documentRow.get("RESOURCE_ID");
							docJson = new JSONObjectWrapper();
							String document_name = (String) documentRow.get("DOCUMENT_NAME");
							docJson.put("resourceId",resourceId);
							docJson.put("document_name",document_name);
							docJson.put("linkCount",1);
							docJson.put("openLink","/sheet/open/"+resourceId);
							if(connectedWebDataDObj.containsTable("FetchMetaInfo"))
							{
								Row fetchMetaRow = connectedWebDataDObj.getRow( "FetchMetaInfo", webdataRow);
								if(fetchMetaRow != null) {
									Object orgInfo = fetchMetaRow.get("SERVICE_ORG_ID");
									if (orgInfo != null) {
										docToOrgInfo.computeIfAbsent(documentId, k -> new HashSet<>()).add(orgInfo.toString());
									}
								}
							}
						}
					} catch (Exception e) {
						logger.log(Level.WARNING, "[Data Connection] Error occurred WEBDATA_ID : {0} >>> {1}", new Object[]{webdataRow.get("WEBDATA_ID"), e});
					}
				}else{
					int linkCount = docJson.getInt("linkCount");
					docJson.put("linkCount",linkCount+1);
					if(connectedWebDataDObj.containsTable("FetchMetaInfo")) {
						Row fetchMetaRow = connectedWebDataDObj.getRow( "FetchMetaInfo", webdataRow);
						if(fetchMetaRow != null) {
							Object orgInfo = fetchMetaRow.get("SERVICE_ORG_ID");
							if (orgInfo != null) {
								docToOrgInfo.computeIfAbsent(documentId, k -> new HashSet<>()).add(orgInfo.toString());
							}
						}
					}
				}
				docLinkInfo.put(documentId, docJson);
			}

			for(Map.Entry<String, Set<String>> orgInfo : docToOrgInfo.entrySet()) {
				docJson = docLinkInfo.getJSONObject(orgInfo.getKey());
				docJson.put("orgList", orgInfo.getValue());
			}
			resp.put(JSONConstants.CONNECTED_DOC_LINK_COUNT, docLinkInfo);
		}catch(Exception e){
			logger.log(Level.WARNING, "[Data Connection] Error occurred ServiceConstant : {0} >>> {1}", new Object[]{serviceConstant, e});
		}
		return resp;
	}

	/*public static JSONObject getServiceConnectedDocList(JSONObject jObj, String docOwner) {
		DataObject connectedWebDataDObj = DBActionManager.getAllOwnerDataLinks(docOwner, jObj.getString(JSONConstants.USER_NAME), jObj.getInt(JSONConstants.SERVICE_CONSTANT));
		JSONObject resp = new JSONObject();
		try{
			JSONObject docJson = null;
			Iterator iter = connectedWebDataDObj.getRows( "WebData");
			while(iter.hasNext()){
				Row webdataRow = (Row)iter.next();
				String documentId = String.valueOf( webdataRow.get("DOCUMENT_ID"));
				int errorCode = (int)webdataRow.get("ERROR_CODE");
				boolean isInActiveState = (errorCode == DCErrorCode.INACTIVE_DATA_LINK || errorCode == DCErrorCode.IGNORE_INACTIVE_ERROR);
				docJson = resp.optJSONObject(documentId);
				if(docJson == null){
					try {
						DataObject dataObject = DocumentUtils.getDocumentsDO(documentId, docOwner);
						if (!dataObject.isEmpty()) {
							Row documentRow 	= dataObject.getRow("Documents");
							String resourceId = (String) documentRow.get("RESOURCE_ID");
							docJson = new JSONObject();
							String document_name = (String) documentRow.get("DOCUMENT_NAME");
							docJson.put("resourceId",resourceId);
							docJson.put("document_name",document_name);
							docJson.put("linkCount",1);
							if(isInActiveState)
							{
								docJson.put("inActivelinkCount",1);
							}
							docJson.put("openLink","/sheet/open/"+resourceId);
						}
					} catch (Exception e) {
						logger.log(Level.WARNING, null, e);
					}
				}else{
					int linkCount = docJson.getInt("linkCount");
					docJson.put("linkCount",linkCount+1);
					if(isInActiveState)
					{
						int inActivelinkCount = docJson.optInt("inActivelinkCount", 0);
						docJson.put("inActivelinkCount",inActivelinkCount+1);
					}
				}
				resp.put(documentId, docJson);
			}
		}catch(Exception e){
			logger.info("Exception "+e.getMessage());
		}
		return resp;
	}*/

	public static List<String> getOrgList(String fetchUserName, String fetchUserZUID, int serviceConst, String connectionUniqueKey, String docOwner) throws Exception {

		CloudService service = FactoryFinder.getService(serviceConst);
		ServiceBean bean = service.getServiceBean();
		bean.setFlowOrgID(DBActionManager.getFlowOrgID_FromDB(docOwner, fetchUserName, fetchUserZUID));
		bean.setFetchOwner(fetchUserName);
		bean.setZUID(fetchUserZUID);
		bean.setConnectionUniqueKey(connectionUniqueKey);
		return service.getOrgList();
	}

	public static JSONObjectWrapper getModuleFieldList(int serviceConstant, String serviceOrg, String serviceDatabaseName, String serviceDBOwner, String moduleName, String currentUser, String userZUID, String docOwner) throws Exception {
		CloudService service = FactoryFinder.getService(serviceConstant);
		long flowOrgID = DBActionManager.getFlowOrgID_FromDB(docOwner, currentUser, userZUID);
		service.bean.setFlowOrgID(flowOrgID);
		try {
			if(((ServiceInfoImpl)service.getServiceInfo()).isMultiConnectionSupport()){
				service.bean.setConnectionUniqueKey(serviceOrg);
			}
			else
			{
				service.bean.setService_OrgID(serviceOrg);
			}
			return service.getModulesFields(currentUser, userZUID, serviceDatabaseName, serviceDBOwner, moduleName);
		} catch (DataConnection.DataConnectionFailedException e) {
			if(ErrorCode.CONNECTION_REMOVED_RECONNECT.equals(e.getMessage())) {
				logger.log(Level.INFO,"[Data Connection] connection revoked for serviceConstant {0}",new Object[]{serviceConstant});
				FlowActionManager.deleteOrRevokeConnection("revoke", currentUser, userZUID, flowOrgID, serviceConstant, serviceOrg, docOwner); //NO I18N
			}
			else if(ErrorCode.CONNECTION_EXPIRED.equals(e.getMessage()))
			{
				logger.log(Level.INFO,"[Data Connection] connection expired for serviceConstant {0}",new Object[]{serviceConstant});
				FlowActionManager.deleteOrRevokeConnection("delete", currentUser, userZUID, flowOrgID, serviceConstant, serviceOrg, docOwner); //NO I18N
			}
			else if(ErrorCode.CONNECTION_NOT_EXIST.equals(e.getMessage()))
			{
				logger.log(Level.INFO,"[Data Connection] connection deleted for serviceConstant {0}",new Object[]{serviceConstant});
			}
			throw e;
		}
	}


	public static JSONArrayWrapper fetchStoreData(CloudService service, String docOwner, String docOwnerZUID, String fetchOwner, String subAction, long webDataID, Long docId, int errorCode, JSONObjectWrapper properties) throws Exception{


		//Object result = service.fetchData(service_OrgID, dbOwnerName, dbName, moduleName, fieldList, criteria, limit, webOwner, webOwnerZUID);
		if("scheduleRefresh".equals(subAction) || "refresh".equalsIgnoreCase(subAction) && webDataID != -1)
		{
			long lastFetchedTime = DBActionManager.getFetchedTime(docOwner, String.valueOf(webDataID));
			service.bean.setLastFetchedTime(lastFetchedTime);
		}
		Object result = null;
		try {
			result = service.fetchData();
		}
		catch (DataConnection.DataConnectionFailedException dcfe)
		{
			if(dcfe.getMessage() != null && !dcfe.getMessage().isEmpty())
			{
				throw dcfe;
			}

			if("scheduleRefresh".equals(subAction))
			{
				DBActionManager.updateErrorCode(docOwner, String.valueOf(webDataID), DCErrorCode.DATA_FETCH_ERROR);
				throw new DataConnection.DataConnectionFailedException("Connection.Unable.fetch"); //NO I18N
			}
			else
			{
				throw new DataConnection.DataConnectionFailedException("Connection.Unavailable"); //NO I18N
			}

		}

		if(result == null)
		{
			throw new NullPointerException();
		}

		List<String> deletedRecords = Collections.emptyList();
		if(("scheduleRefresh".equalsIgnoreCase(subAction) || "refresh".equalsIgnoreCase(subAction))&& result instanceof Map)
		{
			deletedRecords = service.getDeletedRecords();
		}

		//JSONArray fieldList = JSONArray.fromArray(service.getFields().toArray());
		//colSize = fieldList.length();
		String serviceName = null;
		if(service.bean.getServiceConstant() > 1000){
			serviceName = service.getServiceInfo().getName();
		}else{//UNAUTH service Fetch
			serviceName = service.bean.getRequestURL();
		}

		logger.log(Level.INFO, "[Data Connection] actionType >>> {0}, fetched ServiceConst >>> {1}, rowCount >>> {2}, colCount >>> {3}", new Object[]{subAction, service.bean.getServiceConstant(), service.bean.getRowCount(), service.bean.getColCount()});

		long fetchTime = -1l;
		if(webDataID == -1) {
//			startRow = CellUtil.getRow(startCell);
//			startCol = CellUtil.getColumn(startCell);
			String webdata_Type = service.bean.getURLType();
			/*if(webdata_Type.equals("csvsave")){
				webdata_Type = "csv";
			}else if(webdata_Type.equals("rsssave")){
				webdata_Type = "rss";
			}else if(webdata_Type.equals("htmlsave")){
				webdata_Type = "html";
			}*/
			if(DBActionManager.getDocActiveDataLinkInfo(docOwner, String.valueOf(docId)).size("WebData") >= Integer.parseInt(EnginePropertyUtil.getSheetPropertyValue("LinkedDataLimitPerDoc")))
			{
				throw new ProhibitedActionException(ErrorCode.LINKED_DATA_LIMIT_EXCEED_PER_DOC);
			}
			long [] webData = DBActionManager.registerNewWebDataAndGetCreatedTime( docOwner, docOwnerZUID, fetchOwner, docId, serviceName, service.bean.getServiceConstant(), webdata_Type, service.bean.isMaxRowReached() ? DCErrorCode.MAX_RECORD_REACHED : 0, DCLiveStatusCode.NOT_PASTED, properties);
			webDataID = webData[0];
			fetchTime = webData[1];

		}else{
			fetchTime = DBActionManager.updateFetchedTime(docOwner, webDataID, service.bean.isMaxRowReached(), errorCode);

		}
		/*if(cellRow != null){
			startRow = ((Long)cellRow.get("START_ROW")).intValue();
			startCol = ((Long)cellRow.get("START_COLUMN")).intValue();
			rowSize = ((Long)cellRow.get("END_ROW")).intValue() - startRow + 1;
			colSize  = ((Long)cellRow.get("END_COLUMN")).intValue() - startCol + 1;

			oldSize.set("rowSize",rowSize);
			oldSize.set("colSize",colSize);
		}*/
		RWManager.store(webDataID, docOwner, docOwnerZUID,  docId, service, deletedRecords, subAction, result);

		JSONArrayWrapper respArray = new JSONArrayWrapper();
		respArray.put(serviceName);
		respArray.put(webDataID);

		int rowSize = service.getServiceBean().getRowCount();
		int colSize = service.getServiceBean().getColCount();
//
//		if(result instanceof List) {
//			rowSize = ((List) result).size();
//		}else if(result instanceof Map)
//		{
//			rowSize = ((Map) result).size();
//		}

		JSONObjectWrapper newSize = new JSONObjectWrapper();
		//JSONObject oldSize = new JSONObject();
		newSize.set("rowSize",rowSize);
		newSize.set("colSize",colSize);
		//respArray.put(oldSize);
		respArray.put(newSize);
//		respArray.put(ExternalDataUtils.calculateTableRangesFromServiceBean(startRow,startCol, service.bean));
		respArray.put(fetchTime);
		logger.info("[IMPORT_CLOUD_DATA] webDataID Fetched Data Stored in DFS "+webDataID);
		return respArray;
	}
	/***
	 * We are Fetching data based on user request
	 *	this method is used for initialization/add and edit case
	 */

	public static void requestFetch(String docId, String docOwner, String docOwnerZUID, String profileUserName, String profileZUserId, JSONObjectWrapper jObj) throws Exception {

		int serviceConstant =  -1;
		String service_OrgID = null;
		String databaseName = null;
		String moduleName = null;
		JSONArrayWrapper fieldList = null;
		String urlType = null;
		JSONArrayWrapper criteria = null;
		int limit = -1;
		long webDataID = -1;
		long document_ID = -1;
		String unAuthFetchURL = null;
		String selectTbls = null;
		String fetchOwner = null;
		String fetchOwnerZUID = null;
		Row webDataRow 	= null;
		DataObject  fetchDO = null;
		JSONObjectWrapper serviceSpecificParam = null;
		Map tlist = null;
		JSONArrayWrapper delimiters = null;

		String subAction = jObj.optString(JSONConstants.SUB_ACTION);
		urlType = jObj.optString(JSONConstants.URLTYPE);
		serviceConstant =  jObj.optInt(JSONConstants.SERVICE_CONSTANT, -1);
		int errorCode = 0;

		ZSStats.increment(ZSStats.DATA_CONNECTION);

		if(!subAction.equals("add")){
			try{
				webDataID = Long.parseLong(jObj.getString(JSONConstants.WEBDATA_ID));
				fetchDO     = DBActionManager.getMetaInfo(webDataID, docOwner);
			}catch(Exception e){
				logger.info("[DataConnection-Exception] FetchRequest subAction "+subAction+" urlType: "+urlType+" serviceConstant: "+serviceConstant);
				throw e;
			}
				webDataRow 	= fetchDO.getRow("WebData");//No I18N
				urlType = (String)webDataRow.get("WEBDATA_TYPE");
				//dataCellRow	 	= fetchDO.getRow("WebData_Cell");//No I18N
				fetchOwner     = (String)webDataRow.get("WEBDATA_OWNER");
				webDataRow 	= fetchDO.getRow("WebData");//No I18N
				if(urlType.equals("authService") && !subAction.equals("scheduleRefresh")){
					ExternalDataUtils.checkForAuthorization(subAction, fetchOwner, profileUserName, profileZUserId, docId, docOwner, (String)webDataRow.get("PROPERTIES"));
				}
				errorCode = Integer.parseInt(""+webDataRow.get("ERROR_CODE"));
				if(errorCode>0){
					jObj.put("errorCode",errorCode);
				}
		}
		if(subAction.equals("add")||subAction.equals("edit")){
			if(subAction.equals("add") ){
				ExternalDataUtils.checkDataLinkLimitation(docOwner, docId, profileUserName, profileZUserId, serviceConstant);
			}

			if(urlType.equals("authService") ){

				if( (JSONArrayWrapper)jObj.optJSONArray(JSONConstants.SERVICE_ORG_ID)!=null){
					service_OrgID = ((JSONArrayWrapper)jObj.optJSONArray(JSONConstants.SERVICE_ORG_ID)).getString(0);
				}

				if( (JSONArrayWrapper)jObj.optJSONArray(JSONConstants.DB_NAME)!=null){
					databaseName = ((JSONArrayWrapper)jObj.optJSONArray(JSONConstants.DB_NAME)).getString(0);
				}
				moduleName = jObj.optJSONArray(JSONConstants.MODULE_NAME).getString(0);
				fieldList = ((JSONArrayWrapper)jObj.optJSONArray(JSONConstants.FIELD_LIST));
				criteria = ((JSONArrayWrapper)jObj.optJSONArray(JSONConstants.CRITERIA_LIST));
				if(criteria != null && criteria.length() > ConstantHolder.MAX_FILTER_CRITERIA_LIMIT)
				{
					throw new IllegalArgumentException("max criteria reached"); //No I18N
				}
			}else{
				serviceConstant = (serviceConstant <= 0) ? ExternalDataUtils.getUnauthServiceConstant(urlType) : serviceConstant;
				unAuthFetchURL = jObj.optString(JSONConstants.UNAUTH_FETCHURL);
				selectTbls = jObj.optString(JSONConstants.SELECTED_TBL_COLS, null);
				if(urlType.equals("htmlsave"))
				{
					if("nochange".equals(selectTbls)) {
						Iterator itr = fetchDO.getRows("WebData_TablesList");
						//Map tablelist = new HashMap();
						tlist = new LinkedHashMap();
						while(itr.hasNext())
						{
							Row row = (Row)itr.next();
							tlist.put(Integer.parseInt((String)row.get("WEBDATA_TABLE")),row.get("WEBDATA_TABLECOLUMNS"));
						}
					}
					else {
						JSONObjectWrapper tblListObj = JSONObjectWrapper.fromString(selectTbls);
						int tblcnt = tblListObj.getInt("tblcnt");
						tlist = new LinkedHashMap();
						for (int x = 0; x < tblcnt; x++) {
							if (tblListObj.has("" + x)) {
								String colList = tblListObj.getString("" + x);
								tlist.put(x, colList);
							}
						}
					}
				}
				else if(jObj.has(JSONConstants.DELIMITERS))
				{
					if("nochange".equals(jObj.getString(JSONConstants.DELIMITERS)))
					{
						Row row = fetchDO.getRow("WebData_TablesList");
						Object dbValue = row.get("DELIMITERS");
						if(dbValue != null) {
							delimiters = new JSONArrayWrapper(String.valueOf(dbValue));
							jObj.put(JSONConstants.DELIMITERS, delimiters);
						}
					}
					else {
						delimiters = jObj.getJSONArray(JSONConstants.DELIMITERS);
						if("nochange".equals(unAuthFetchURL))
						{
							unAuthFetchURL = (String)webDataRow.get("SITE_URL");
						}
					}
				}
			}
			document_ID = Long.parseLong(docId);
			fetchOwner = profileUserName;
			fetchOwnerZUID = profileZUserId;
			serviceSpecificParam = ExternalDataUtils.getServiceSpecificParam( jObj);
		}else{//Refresh and scheduler
			try {
			//DataObject  fetchDO     = DBActionManager.getMetaInfo(webDataID, docOwner);
			//webDataRow 	= fetchDO.getRow("WebData");//No I18N
			urlType = (String)webDataRow.get("WEBDATA_TYPE");
			if(urlType.equals("csv") || urlType.equals("rss") || urlType.equals("html")){
				urlType = urlType + "save";
			}
			//dataCellRow	 	= fetchDO.getRow("WebData_Cell");//No I18N
			jObj.put(JSONConstants.URLTYPE, urlType);
			document_ID = (long)webDataRow.get("DOCUMENT_ID");
//				if(jObj.optString(JSONConstants.SUB_ACTION).equals("scheduleRefresh") && document_ID == 0){ //non migrated scheduler
//					com.adventnet.persistence.Row webDataCell_row  = DBActionManager.getWebDataCellInfo(docOwner,  webDataID);
//					document_ID = (long)webDataCell_row.get("DOCUMENT_ID");
//				}
			if(urlType.equals("authService") || urlType.equals("authenticated"))  {
				urlType = urlType.equals("authenticated")?"authService":urlType;
				Row 	    fetchMetaInfo 	= null;
					fetchMetaInfo 	= fetchDO.getRow("FetchMetaInfo");//No I18N
					//webOwner     = (String)webDataRow.get("WEBDATA_OWNER");
					serviceConstant = (int)fetchMetaInfo.get("SERVICE_CONSTANT");
					String service_OrgStr = (String)fetchMetaInfo.get("SERVICE_ORG_ID");
					if(service_OrgStr != null) {
						service_OrgID = new JSONArrayWrapper(service_OrgStr).optString(0, null);
					}
					fetchOwnerZUID  = ""+fetchMetaInfo.get("DATA_OWNER_ZUID");
					String databaseNameStr = (String)fetchMetaInfo.get("DATABASE_NAME");
					if(databaseNameStr != null) {
						databaseName = new JSONArrayWrapper(databaseNameStr).optString(0, null);
					}
					moduleName = new JSONArrayWrapper((String) fetchMetaInfo.get("MODULE_NAME")).optString(0);
					serviceSpecificParam =  JSONObjectWrapper.fromString((String) fetchMetaInfo.get("SERVICE_SPECIFIC_PARAMS"));
					String fieldListStr = (String)fetchMetaInfo.get("FIELD_LIST");
					fieldList = new JSONArrayWrapper(fieldListStr);

					String criteriaStr = (String)fetchMetaInfo.get("CRITERIA");
					if(criteriaStr != null) {
						criteria = new JSONArrayWrapper(criteriaStr);
					}
					limit = (int)fetchMetaInfo.get("LIMIT");
				if(subAction.equals("refresh")){
					int counnected_count = DBActionManager.getUpdatedFetchCountInfo(docId, docOwner, fetchOwner, serviceConstant, FactoryFinder.getService(serviceConstant).getServiceInfo().getName(), Long.parseLong(fetchOwnerZUID));
					if(counnected_count >= ConstantHolder.MAX_REFRESH_LIMIT_IN_24HRS){
						WorkbookAdditionalInfo additionalInfo = (new WorkbookContainer(Long.parseLong(docId), docOwner)).getWorkbookAdditionalInfo();
						long time = DBActionManager.getFirstConnectedTime(profileUserName, serviceConstant) + 86400000;
						throw new DataConnection.DataConnectionFailedException(ErrorCode.LINKED_DATA_FETCH_LIMIT_EXCEED_PER_SERVICE, ((ServiceInfoImpl) Objects.requireNonNull(FactoryFinder.getService(serviceConstant)).getServiceInfo()).getDisplayName(), ClientUtils.getUserTimeZoneTime1(time, additionalInfo.getSpreadsheetSettings().getLocale(), additionalInfo.getTimeZone()));
					}
				}


			}else {
				serviceConstant = (serviceConstant <= 0) ? ExternalDataUtils.getUnauthServiceConstant(urlType) : serviceConstant;
				unAuthFetchURL = (String)webDataRow.get("SITE_URL");
				if(urlType.equals("htmlsave")){
					Iterator itr = fetchDO.getRows("WebData_TablesList");
					//Map tablelist = new HashMap();
					tlist = new LinkedHashMap();
					while(itr.hasNext())
					{
						Row row = (Row)itr.next();
						tlist.put(Integer.parseInt((String)row.get("WEBDATA_TABLE")),row.get("WEBDATA_TABLECOLUMNS"));
					}
				}
				else if(fetchDO.containsTable("WebData_TablesList"))
				{

					Row row = fetchDO.getRow("WebData_TablesList");
					Object dbValue = row.get("DELIMITERS");
					if(dbValue != null) {
						delimiters = new JSONArrayWrapper(String.valueOf(dbValue));
						jObj.put(JSONConstants.DELIMITERS, delimiters);
					}
				}
			}
			}catch(Exception e) {
				logger.log(Level.WARNING,"[DataConnection-Exception] FetchRequest subAction "+subAction+" urlType: "+urlType+" serviceConstant: "+serviceConstant, e);
				throw e;
			}
		}

		logger.info("[Data Connection] FetchRequest subAction "+subAction+" urlType: "+urlType+" serviceConstant: "+serviceConstant);

		jObj.put(JSONConstants.SERVICE_CONSTANT, serviceConstant);
		CloudService service = FactoryFinder.getService(serviceConstant);
		try {
		ServiceBean bean = service.getServiceBean();
		bean.setFetchOwner(fetchOwner);
		bean.setZUID(fetchOwnerZUID);
		bean.setFlowOrgID(DBActionManager.getFlowOrgID_FromDB(docOwner, fetchOwner, fetchOwnerZUID));
		if(service_OrgID != null){
			bean.setService_OrgID(service_OrgID);
		}
		if("authService".equals(urlType) && ((ServiceInfoImpl)service.getServiceInfo()).isMultiConnectionSupport()){
			bean.setConnectionUniqueKey(service_OrgID);
		}
//		if(dbOwnerName != null){
//			bean.setApplicationOwnerName(dbOwnerName);
//		}

		if(serviceSpecificParam!=null && !serviceSpecificParam.isEmpty()) {
			bean.setServiceSpecificParam(serviceSpecificParam);
		}

		if(databaseName!=null) {
			bean.setDatabaseName(databaseName);
		}
		if(moduleName!=null) {
			bean.setModuleName(moduleName);
		}
		if(fieldList!=null) {
			bean.setFieldList(fieldList.isEmpty() ? JSONArrayWrapper.fromArray(service.getFields().toArray()) : fieldList);
		}
		if(unAuthFetchURL!=null) {
			bean.setRequestURL(unAuthFetchURL);
		}
		if(tlist!=null) {
			bean.setHtmlTableList(tlist);
		}

		if(delimiters != null)
		{
			bean.setDelimiters(delimiters);
		}

		bean.setURLType(urlType);
		bean.setCriteriaList(criteria);
		bean.setLimit(limit);

		JSONArrayWrapper infoArray = null;
			// below check is to avoid fetch for retry actions
			if(!jObj.has("newSize")) {
				if(subAction.equals("edit")){
					//dataCellRow = DBActionManager.getWebDataCellInfo(docOwner, webDataID);
					if(urlType.equals("authService") ){
						if(fieldList!=null) {
							if(!moduleName.equals("nochange")){
								webDataID = Long.parseLong(jObj.getString(JSONConstants.WEBDATA_ID));

								//CloudService service, String docOwner, String docOwnerZUID, String webOwner, long webDataID,  Row cellRow, Long docId
								infoArray = fetchStoreData(service, docOwner, docOwnerZUID, null, subAction,  webDataID, document_ID, errorCode, jObj.optJSONObject("properties")); //No I18N
							}
						}
					}else{
						if(!unAuthFetchURL.equals("nochange")) {
							infoArray = fetchStoreData(service, docOwner, docOwnerZUID, null, subAction, webDataID, document_ID, errorCode, jObj.optJSONObject("properties"));  //No I18N
						}
					}
				}else{

					infoArray = fetchStoreData(service, docOwner, docOwnerZUID, profileUserName, subAction, webDataID, document_ID, errorCode, jObj.optJSONObject("properties"));  //No I18N
					webDataID = Long.parseLong(infoArray.getString(1));
					jObj.put(JSONConstants.WEBDATA_ID, webDataID);
				}
			}

			if(infoArray!=null){
				String serviceName = infoArray.getString(0);
				JSONObjectWrapper newSize = infoArray.getJSONObject(2);
//				JSONArray tableRanges = infoArray.getJSONArray(3);
				long fetchTime = infoArray.getLong(3);

				jObj.put("serviceName", serviceName);
				jObj.put("newSize",newSize);
				jObj.put("fetchTime",fetchTime);
				if(!service.bean.isMaxRowReached() && errorCode == DCErrorCode.MAX_RECORD_REACHED)
				{
					jObj.put("errorCode", 0);
				}
				else if(service.bean.isMaxRowReached())
				{
					jObj.put("errorCode", DCErrorCode.MAX_RECORD_REACHED);
					if("add".equals(subAction))
					{
						jObj.put("throwMaxRowReachedError", true);
					}
				}

			}
			else
			{
				//// for retry case.
				if(service.bean.getServiceConstant() > 1000){
					jObj.put("serviceName", service.getServiceInfo().getName());
				}else{//UNAUTH service Fetch
					jObj.put("serviceName", service.bean.getRequestURL());
				}
			}
			jObj.put("webOwner",fetchOwner);

			if(service.bean.getServiceSpecificParam() != null)
			{
				jObj.put("serviceSpecificParam", service.bean.getServiceSpecificParam());
			}

			if(service.bean.getColFormats() != null)
			{
				jObj.put(JSONConstants.COL_FORMAT, service.bean.getColFormats());
			}

			/*if((!jObj.has(JSONConstants.RANGELIST)) && dataCellRow != null)
			{
				int startRow = ((Long)dataCellRow.get("START_ROW")).intValue();
				int startCol = ((Long)dataCellRow.get("START_COLUMN")).intValue();
				jObj.put(JSONConstants.RANGELIST, ActionJsonUtil.addRangeInJsonArray(null, startRow, startCol, startRow, startCol, false));
			}*/

		}catch (DataConnection.DataConnectionFailedException dce)
		{
			if("scheduleRefresh".equals(subAction))
			{
//				if(errorCode == DCErrorCode.DATA_FETCH_ERROR || ErrorCode.CONNECTION_REMOVED_RECONNECT.equalsIgnoreCase(dce.getMessage()))
//				{
//					logger.log(Level.INFO, "[Data Connection] disabling Data Link : {0}", new Object[]{webDataID});
//					TaskManager.enableOrDisable(webDataID, docOwner, false);
//				}
				logger.log(Level.INFO, "[Data Connection] schedule refresh failed webdata :{0}, {1}", new Object[]{webDataID, dce});
				DBActionManager.updateErrorCode(docOwner, String.valueOf(webDataID), DCErrorCode.DATA_FETCH_ERROR);
				throw new DataConnection.DataConnectionFailedException("Connection.Unable.fetch"); //NO I18N
			}
			else {
				if(ErrorCode.CONNECTION_REMOVED_RECONNECT.equals(dce.getMessage())) {
					logger.log(Level.INFO,"[Data Connection] connection revoked for serviceConstant {0}",new Object[]{serviceConstant});
					FlowActionManager.deleteOrRevokeConnection("revoke", service.bean.getFetchOwner(), service.bean.getZUID(), service.bean.getFlowOrgID(), service.bean.getServiceConstant(), service.bean.getService_OrgID(), docOwner); //NO I18N
				}
				else if(ErrorCode.CONNECTION_EXPIRED.equals(dce.getMessage()))
				{
					logger.log(Level.INFO,"[Data Connection] connection expired for serviceConstant {0}",new Object[]{serviceConstant});
					FlowActionManager.deleteOrRevokeConnection("delete", service.bean.getFetchOwner(), service.bean.getZUID(), service.bean.getFlowOrgID(), service.bean.getServiceConstant(), service.bean.getService_OrgID(), docOwner); //NO I18N
				}
				else if(ErrorCode.CONNECTION_NOT_EXIST.equals(dce.getMessage()))
				{
					logger.log(Level.INFO,"[Data Connection] connection deleted for serviceConstant {0}",new Object[]{serviceConstant});
				}
				throw dce;
			}
		}
		catch(Exception e) {
			logger.log(Level.WARNING,"[IMPORT_CLOUD_DATA EXCEPTION] FetchRequest subAction "+subAction+" urlType: "+urlType+" serviceConstant: "+serviceConstant, e);
			if(ErrorCode.LINKED_DATA_LIMIT_EXCEED_PER_DOC.equals(e.getMessage())) {
				//logger.log(Level.WARNING,"[IMPORT_CLOUD_DATA EXCEPTION] LINKED_DATA_LIMIT_EXCEED_PER_DOC webDataID: "+ webDataID +"subAction "+subAction+" urlType: "+urlType+" serviceConstant: "+serviceConstant+" "+e.getMessage());
				throw e;
			}
		}
		logger.info("[IMPORT_CLOUD_DATA] DataFetched webDataID: "+ webDataID +"subAction "+subAction+" urlType: "+urlType+" serviceConstant: "+serviceConstant);

	}



	public static void doDB_SchedulerAction(String docOwner, String docOwnerZUID,  String docId,JSONObjectWrapper actionJson, TimeZone userTimeZone) throws Exception {
		//logger.info("	initializeFetchData called "+actionJson);
		String subAction = actionJson.optString(JSONConstants.SUB_ACTION);
		String urlType = actionJson.optString(JSONConstants.URLTYPE);
		long webDataID = Long.parseLong(actionJson.optString(JSONConstants.WEBDATA_ID));
		String profileUserName = actionJson.getString(JSONConstants.USER_NAME);
		String profileZUserId = actionJson.getString(JSONConstants.ZUID);

		if(!subAction.equals("add")){
			DataObject webdatadDO     = DBActionManager.getWebDataDO(docOwner, webDataID);
			Row webDataRow 	= webdatadDO.getRow("WebData");//No I18N
			String fetchOwner     = (String)webDataRow.get("WEBDATA_OWNER");
			if(urlType.equals("authService") && !subAction.equals("scheduleRefresh")){
				ExternalDataUtils.checkForAuthorization(subAction, fetchOwner, profileUserName, profileZUserId, docId, docOwner, (String)webDataRow.get("PROPERTIES"));
			}
		}
		int serviceConstant =  actionJson.optInt(JSONConstants.SERVICE_CONSTANT);
		String service_OrgID = actionJson.optJSONArray(JSONConstants.SERVICE_ORG_ID) != null ? actionJson.optJSONArray(JSONConstants.SERVICE_ORG_ID).toString() : null;
		String DBName = actionJson.optJSONArray(JSONConstants.DB_NAME) != null ? actionJson.optJSONArray(JSONConstants.DB_NAME).toString() : null;
		String moduleName = actionJson.optString(JSONConstants.MODULE_NAME);
		String serviceName = actionJson.optString("serviceName"); //No I18N

		JSONArrayWrapper fieldList = ((JSONArrayWrapper)actionJson.optJSONArray(JSONConstants.FIELD_LIST));
		String schedulerInterval = actionJson.optString(JSONConstants.SCHEDULER_INTERVAL);
		if(schedulerInterval.contains("hr-webData") || schedulerInterval.contains("hr-Repetition"))
		{
			HashMap policyDetails = UserPolicyDecider.getPolicy(DocumentUtils.getResourceId(docId, docOwner),  profileZUserId);
			Boolean hasHourlyScheduler = (Boolean) policyDetails.get(DCPlanConstructor.DCAction.HOURLY_SCHEDULER);
			if(!hasHourlyScheduler)
			{
				throw new IllegalArgumentException("User Ineligible for Hourly Scheduler >>>> "+schedulerInterval); //No I18N
			}
		}


		String criteria = actionJson.optString(JSONConstants.CRITERIA_LIST, null);
		int limit = -1;
		String fieldListStr = null;
		if(fieldList!=null) {
			fieldListStr = fieldList.toString();
		}
		try {
			switch (subAction) {
				case "add":
					if (urlType.equals("authService")) {
						JSONObjectWrapper serviceSpecificParam = ExternalDataUtils.getServiceSpecificParam(actionJson);
						DBActionManager.addorUpdateMetaInfo(true, docOwner, profileUserName, Long.parseLong(profileZUserId), webDataID, serviceConstant, serviceName, service_OrgID, DBName, moduleName, fieldListStr, criteria, limit, serviceSpecificParam != null  ? serviceSpecificParam.toString() : null);
						actionJson.remove("serviceSpecificParam");
					} else if (!urlType.equalsIgnoreCase("rsssave")) {
						//addorUpdateHTMLTableInfo(boolean  addNew, String docOwner, long webDataID, String tblList)
						DBActionManager.addorUpdateWebData_TablesList_INFO(true, "htmlsave".equalsIgnoreCase(urlType), docOwner, webDataID, actionJson.optString(JSONConstants.SELECTED_TBL_COLS), actionJson.has(JSONConstants.DELIMITERS) ? actionJson.getJSONArray(JSONConstants.DELIMITERS).toString() : null); //NO I18N
					}
					TaskManager.addOrUpdateOrDisable(webDataID, schedulerInterval, docOwner, userTimeZone);
					break;
				case "edit":
					if (urlType.equals("authService")) {
						if (fieldList != null) {
							fieldListStr = fieldList.toString();
							if (!moduleName.equals("nochange")) {
								DBActionManager.addorUpdateMetaInfo(false, docOwner, null, null, webDataID, serviceConstant, serviceName, service_OrgID, DBName, moduleName, fieldListStr, criteria, limit, null);
							}
						}
						if(actionJson.has("properties"))
						{
							DBActionManager.updateWebDataProperties(docOwner, actionJson.getJSONObject("properties"), webDataID);
						}
					} else
					{

						if(!"nochange".equals(actionJson.optString(JSONConstants.UNAUTH_FETCHURL,"nochange")))
						{
							DBActionManager.updateWebDataSiteURL(docOwner, actionJson.getString(JSONConstants.UNAUTH_FETCHURL), webDataID);
						}

						if (!urlType.equalsIgnoreCase("rsssave") && (!actionJson.optString(JSONConstants.SELECTED_TBL_COLS, "nochange").equals("nochange") || !actionJson.optString(JSONConstants.DELIMITERS, "nochange").equals("nochange"))) {
							DBActionManager.addorUpdateWebData_TablesList_INFO(false, "htmlsave".equalsIgnoreCase(urlType), docOwner, webDataID, actionJson.optString(JSONConstants.SELECTED_TBL_COLS), actionJson.has(JSONConstants.DELIMITERS) ? actionJson.getJSONArray(JSONConstants.DELIMITERS).toString() : null);//NO I18N
						}
					}
					if (!schedulerInterval.equals("nochange")) {
						TaskManager.addOrUpdateOrDisable(webDataID, schedulerInterval, docOwner, userTimeZone);
					}
					break;
				case "delete":
					try {
						DBActionManager.deleteWebData(docOwner, docOwnerZUID, webDataID, docId);
					} catch (Exception e) {
						logger.info("ErrorOccured while deleting WebData " + webDataID + e);
					}
					break;
				case "enable":
				case "disable":
					TaskManager.enableOrDisable(webDataID, docOwner, "enable".equals(subAction)); //No I18N
					break;
				case "ignore":
					updateErrorOnDB(actionJson.getString(JSONConstants.WEBDATA_ID), docOwner, actionJson.optInt("errorCode", DCErrorCode.IGNORE_ERROR)); //No I18N
					break;
				case "reActivate":
					ExternalDataUtils.reActivateDataLink(docId, docOwner, profileUserName, profileZUserId, webDataID);
					actionJson.put("liveStatusCode", DCLiveStatusCode.ACTIVE);
					break;
				/*case "reActivateAll":
					ExternalDataUtils.reActivateAllDataLinks(docId, docOwner, profileUserName);
					break;*/
			}
		}
		catch (DataConnection.DataConnectionFailedException dfe)
		{
			if(dfe.getMessage().equals(ErrorCode.LINKED_DATA_LIMIT_EXCEED_PER_DOC))
			{
				actionJson.put("isDocLimitExceed", true);
			}
			throw dfe;
		}
		catch(Exception e) {
			logger.log(Level.INFO, "[Data-Connection] {0} error in doDB_SchedulerAction {1}", new Object[]{subAction, e});
		}
	}


	public static JSONObjectWrapper getTableDataPreView(String loc, String urlType) throws Exception
	{
		JSONObjectWrapper toRet = null;
		if("htmlsave".equalsIgnoreCase(urlType)) {
			StringBuffer buff = new StringBuffer(128);
			toRet = new JSONObjectWrapper();
			String webString = getTablesFromHtml(loc, LocaleUtil.getMyLocale());
			if (webString != null) {

				if(webString.equalsIgnoreCase("NoTableAvailable"))
				{
					I18nMessage i18nProp=new I18nMessage(LocaleUtil.getMyLocale());
					webString = "<div class=\"wid100 txt_cen fnt-darkgrey ZIEmptyMsg\">"+i18nProp.getMsg("Connection.No.Data")+"</div>";//No I18N;
					toRet.put("noDataFound", true);
				}
				buff.append("<websheet>\n");
				buff.append("<sheet><![CDATA[").append(webString).append("]]></sheet>\n");
				buff.append("</websheet>\n");

				//logger.info("string is ... "+buff.toString());

			}
			toRet.put("tableData",buff.toString());
		}
		else if("csvsave".equalsIgnoreCase(urlType))
		{
			CloudService service = FactoryFinder.getService(ServiceConstant.UNAUTH_CSV);
			service.bean.setRequestURL(URLDecoder.decode(loc, "UTF-8"));
			service.bean.setPreview(true);
			toRet = new JSONObjectWrapper();
			toRet.put("tableData",service.fetchData());
			toRet.put("totalRow",service.bean.getRowCount());
		}
		return toRet;
	}

	public static String getTablesFromHtml(String url, Locale userloc) throws Exception
	{
		//logger.info("url to parse is ... "+url);
		Parser pr = null;
		if(url != null && !url.equals(""))
		{
			url = URLDecoder.decode(url, "UTF-8");
			url = url.trim();
			url = url.replace(" ", "%20");
		}

		String fileValidity = ClientUtils.checkFileSize(url);
		if(fileValidity.equals("fileSizeExceeded")) {
			return "Connection.LinkSize.Exceed";       //No I18N
		} else if(fileValidity.equals("connectionError")) {
			return "UrlNotReachable";   //No I18N
		}

		try
		{
			ZSConnection httpCon  =  (new ZSConnectionFactory(url, "GET", false, false)).getConnectionObj(); //no i18n

			if(httpCon instanceof ZSheetUrlWrapperConnection) {
				httpCon.setReadTimeout(30000);
				httpCon.setConnectionTimeout(30000);
				httpCon.process();
				Object urlConnectionObj = ((ZSheetUrlWrapperConnection) httpCon).getURLConnectionObj();
				if (urlConnectionObj instanceof HttpsURLConnection) {
					pr = new Parser((HttpsURLConnection) urlConnectionObj);
				} else if (urlConnectionObj instanceof HttpURLConnection) {
					pr = new Parser((HttpURLConnection) urlConnectionObj);
				}
			}
		}
		catch(ParserException pe)
		{
			logger.log(Level.WARNING,null,"Exception whiile getting table data ");
			return "UrlNotReachable";   //No I18N
		}

		if (pr == null)
		{
			logger.log(Level.WARNING, null, "Exception whiile getting table data ");
			return "UrlNotReachable";   //No I18N
		}

		NodeFilter nf =  new TagNameFilter("table");//No I18N
		nf = new AndFilter(nf,new NotFilter(new HasChildFilter(nf,true)));
		nf = new OrFilter(nf,new TagNameFilter("rss"));//No I18N
		NodeList nl;
		try
		{
			nl = pr.extractAllNodesThatMatch(nf);
		}
		catch (EncodingChangeException ece)
		{
			// reset the parser
			pr.reset ();
			// try again with the encoding now in force
			nl = pr.parse (nf);
		}
		String datatbl="";
		int visibleCount = 0;
		boolean isTableAvailable = false;
		I18nMessage i18nProp=new I18nMessage(userloc);
		for(int i=0;i<nl.size();i++)
		{
			TableTag tbl = (TableTag)nl.elementAt(i);
			String tblStyl = tbl.getAttribute("style");
			//if(tblStyl != null && (tblStyl.equalsIgnoreCase("display:none") || tblStyl.equalsIgnoreCase("visibility:hidden")))
			if(tblStyl != null && tblStyl.contains("display") && tblStyl.contains("none"))
			{
				continue;
			}
			TableRow[] rowArr = tbl.getRows();
//                        boolean isNewClient = ClientUtils.isNewClientUser();
			//write the table....
			if(rowArr.length>1)
			{
				isTableAvailable = true;
				visibleCount++;
//                                if(isNewClient){
//                                    datatbl = datatbl.concat("<table class=\"extDataContainer\" width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\"><tr><td><table border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><tr><td width=\"22\"><input id=\"scraptbl_"+i+"\" value=\"scraptbl_"+i+"\" type=\"checkbox\"></td><td class=\"name\"><label for=\"scraptbl_"+i+"\">"+i18nProp.getMsg("HtmlTable")+visibleCount+"</label></td></tr></table></td></tr>");
				datatbl = datatbl.concat("<table class=\"extDataContainer\" width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\"><tr><td><table border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><tr><label class=\"ZsSelectionBox mr10 p5\"><input id=\"scraptbl_"+i+"\" name=\"selTable\" value=\"scraptbl_"+i+"\" type=\"radio\"><span for=\"scraptbl_"+i+"\">"+i18nProp.getMsg("HtmlTable")+visibleCount+"</span></label></tr></table></td></tr>");
				datatbl = datatbl.concat("<tr><td> <div class=\"extDataDIV\"><table width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\" class=\"extDataTable\">");
//                                } else {
//                                    datatbl = datatbl.concat("<table class=\"extDataContainer\" width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\"><tr><td><table border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><tr><td width=\"22\"><input id=\"scraptbl_"+i+"\" value=\"scraptbl_"+i+"\" type=\"checkbox\" onClick=\"toggleExtDataFields("+i+", this.checked)\"></td><td class=\"name\"><label for=\"scraptbl_"+i+"\">"+i18nProp.getMsg("HtmlTable")+visibleCount+"</label></td></tr></table></td></tr>");
//                                    datatbl = datatbl.concat("<tr><td> <div class=\"extDataDIV\"><table width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\" class=\"extDataTable\">");
//                                }
			}
			else
			{
//                            if(isNewClient){
//                                datatbl = datatbl.concat("<table style=\"display:none\" class=\"extDataContainer\" width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\"><tr><td><table border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><tr><td width=\"22\"><input id=\"scraptbl_"+i+"\" value=\"scraptbl_"+i+"\" type=\"checkbox\"></td><td class=\"name\"><label for=\"scraptbl_"+i+"\">"+i18nProp.getMsg("HtmlTable")+"</label></td></tr></table></td></tr>");
				datatbl = datatbl.concat("<table style=\"display:none\" class=\"extDataContainer\" width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\"><tr><td><table border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><tr><label class=\"ZsSelectionBox mr10 p5\"><input id=\"scraptbl_"+i+"\" name=\"selTable\" value=\"scraptbl_"+i+"\" type=\"radio\"><span for=\"scraptbl_"+i+"\">"+i18nProp.getMsg("HtmlTable")+"</span></label></tr></table></td></tr>");
				datatbl = datatbl.concat("<tr><td> <div class=\"extDataDIV\"><table width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\" class=\"extDataTable\">");
//                            } else {
//                                datatbl = datatbl.concat("<table style=\"display:none\" class=\"extDataContainer\" width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\"><tr><td><table border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><tr><td width=\"22\"><input id=\"scraptbl_"+i+"\" value=\"scraptbl_"+i+"\" type=\"checkbox\" onClick=\"toggleExtDataFields("+i+", this.checked)\"></td><td class=\"name\"><label for=\"scraptbl_"+i+"\">"+i18nProp.getMsg("HtmlTable")+"</label></td></tr></table></td></tr>");
//				datatbl = datatbl.concat("<tr><td> <div class=\"extDataDIV\"><table width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"0\" class=\"extDataTable\">");
//                            }

			}

			//table header...
			boolean colHeader = false;
			int noOfRows = 4;
			int tblRowSize = 4;
			int tblColSize = 0;
			NodeList header = new NodeList();
			NodeFilter thfilter = new TagNameFilter ("th");//No I18N
			NodeFilter rf = new TagNameFilter ("tr");//No I18N
			thfilter = new AndFilter(thfilter,new NotFilter(new HasParentFilter(rf,false)));
			tbl.collectInto(header,thfilter);
			if(header.size()>0)
			{
				noOfRows--;
				tblRowSize--;
				tblColSize = header.size();
				datatbl = datatbl.concat("\n");
				datatbl = datatbl.concat("<tr class=\"header\">");
				for(int hdr=0;hdr<header.size();hdr++)
				{
					//datatbl = datatbl.concat("<td ");
					datatbl = datatbl.concat("<td id=\"scraptbl_"+i+"_"+hdr+"_colspan\" ");
					String colSpan = ((TagNode)header.elementAt(hdr)).getAttribute("colspan");
					if(colSpan != null && !colSpan.isEmpty())
					{
						datatbl = datatbl.concat("colspan=\""+colSpan+"\"");
					}
					datatbl = datatbl.concat("><table border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><label class=\"ZsSelectionBox mr10 p5\">");
					datatbl = datatbl.concat("<input id=\"scraptbl_"+i+"_"+hdr+"\" value=\"checkbox\" type=\"checkbox\" onClick=\"deSelectExtDataTables(this)\">");
					datatbl = datatbl.concat("<span> &nbsp;");
					datatbl = datatbl.concat(header.elementAt(hdr).toPlainTextString().trim());
					datatbl = datatbl.concat("</span>");
					datatbl = datatbl.concat("</label>");
					datatbl = datatbl.concat("</table>");
					datatbl = datatbl.concat("</td>");
				}
				datatbl = datatbl.concat("\n");
				datatbl = datatbl.concat("</tr>");
			}
			try
			{
				//for(int rc=0; rc<rowArr.length;rc++)
				for(int rc=0; rc<noOfRows;rc++)
				{
					datatbl = datatbl.concat("\n");
					//TableRow tr = tbl.getRow(rc);
					TableRow tr = rowArr[rc];
					String rowStyl = tr.getAttribute("style");
					tblRowSize--;
					if(rowStyl != null && rowStyl.indexOf("display") != -1 && rowStyl.indexOf("none") != -1)
					{
						continue;
					}
					if(!colHeader)
					{
						datatbl = datatbl.concat("<tr class=\"header\">");
					}
					else
					{
						datatbl = datatbl.concat("<tr class=\"oddRow\">");
					}
					NodeList rowChild = new NodeList();
					NodeFilter rowfilter = new TagNameFilter ("th");//No I18N
					rowfilter = new OrFilter(rowfilter,new TagNameFilter("td"));//No I18N
					tr.collectInto(rowChild,rowfilter);
					//logger.info("rowChild size is ... "+rowChild.size());
					for(int child=0; child<rowChild.size(); child++)
					{
						TagNode tg = (TagNode)rowChild.elementAt(child);
						Node pNode = tg.getParent();
						IsEqualFilter iEqFilter = new IsEqualFilter(pNode);
						//check for immediatechild
						if(!iEqFilter.accept(tr))
						{
							continue;
						}
						String colStyl = tg.getAttribute("style");
						if(colStyl != null && colStyl.indexOf("display") != -1 && colStyl.indexOf("none") != -1)
						{
							continue;
						}
						datatbl = datatbl.concat("\n");
						if(!colHeader)
						{
							//datatbl = datatbl.concat("<td id=\"scraptbl_"+i+"_"+child+"_colspan");
							datatbl = datatbl.concat("<td id=\"scraptbl_"+i+"_"+child+"_colspan\" ");
							String colSpan = (tg.getAttribute("colspan"));
							String rowSpan = (tg.getAttribute("rowspan"));
							if(colSpan != null && !colSpan.isEmpty())
							{
								datatbl = datatbl.concat("colspan=\""+colSpan+"\"");
							}
							if(rowSpan != null)
							{
								datatbl = datatbl.concat("rowspan=\""+rowSpan+"\"");
							}

							datatbl = datatbl.concat(" ><table border=\"0\" cellspacing=\"0\" cellpadding=\"0\"><label class=\"ZsSelectionBox mr10 p5\">");
							datatbl = datatbl.concat("<input id=\"scraptbl_"+i+"_"+child+"\" value=\"checkbox\" type=\"checkbox\" onClick=\"deSelectExtDataTables(this)\">");
							datatbl = datatbl.concat("<span> ");
							String val = tg.toPlainTextString();
							val = val.trim();
							//logger.info("child is ... "+val);
							datatbl = datatbl.concat("&nbsp;");
							datatbl = datatbl.concat(val);
							datatbl = datatbl.concat("</span>");
							datatbl = datatbl.concat("</label>");
							datatbl = datatbl.concat("</table>");
							datatbl = datatbl.concat("</td>");
						}
						else
						{
							datatbl = datatbl.concat("<td ");
							String val = tg.toPlainTextString();
							val = val.trim();
							//logger.info("child is ... "+val);
							String colSpan = (tg.getAttribute("colspan"));
							String rowSpan = (tg.getAttribute("rowspan"));

							if(colSpan != null && !colSpan.isEmpty())
							{
								datatbl = datatbl.concat("colspan=\""+colSpan+"\"");
							}

							if(rowSpan != null)
							{
								datatbl = datatbl.concat("rowspan=\""+rowSpan+"\"");
							}

							datatbl = datatbl.concat("> &nbsp;");
							datatbl = datatbl.concat(val);
							datatbl = datatbl.concat("</td>");
						}
						tblColSize = child;
					}
					colHeader = true;
					datatbl = datatbl.concat("\n");
					datatbl = datatbl.concat("</tr>");

				}
			}catch(Exception e)
			{
				logger.log(Level.WARNING,null,e);
				//datatbl = datatbl.concat("<td>&nbsp;</td>");
			}
			//blankrows if the table has less than 4 rows...
			for(int extrows=0; extrows<tblRowSize; extrows++)
			{
				datatbl = datatbl.concat("<tr class=\"oddRow\">");
				for(int cols=0; cols<=tblColSize;cols++)
				{
					datatbl = datatbl.concat("<td> &nbsp;");
					datatbl = datatbl.concat("</td>");
				}
				datatbl = datatbl.concat("</tr>");
			}
			//default last row...
			datatbl = datatbl.concat("<tr class=\"oddRow\">");
			for(int cols=0; cols<=tblColSize;cols++)
			{
				datatbl = datatbl.concat("<td>.....</td>");
			}
			datatbl = datatbl.concat("</tr>");

			datatbl = datatbl.concat("\n");
			datatbl = datatbl.concat("</table>");
			datatbl = datatbl.concat("</div></td></tr></table>");
		}
		//logger.info("data parse is ... "+datatbl);
		if(isTableAvailable)
		{
			return datatbl;
		}
		else
		{
			logger.info("No parsable table available...");
			return "NoTableAvailable";//No I18N
		}
	}

	public static void updateErrorOnDB(String webDataID, String docOwner, int errorCode) {
		try{
			DBActionManager.updateErrorCode(docOwner, webDataID, errorCode);
		}catch(Exception e){
			logger.log(Level.WARNING,"Exception on update error code: "+errorCode,e);
		}
	}

	public static void duplicateSheetLinkedData(Sheet fromSheet, Long docID, String docOwner, String docOwnerZuid, JSONObjectWrapper jsonObj) throws Exception {
		Map<String,String> webdataIdsMap = DBActionManager.duplicateDB_Meta(docID, docID, docOwner, docOwnerZuid, fromSheet.getWorkbook().getDataConnection().getDataConnectionsFromSheet(fromSheet).keySet());
		if(!webdataIdsMap.isEmpty()) {
			jsonObj.put(JSONConstants.WEBDATA_IDS, webdataIdsMap);
		}
	}

	public static void copySheetLinkedDataToDocument(Sheet fromSheet, Long fromDocID, Long toDocID, String docOwner, String docOwnerZuid, JSONObjectWrapper jsonObj) throws Exception {
		Map<String,String> webdataIdsMap = DBActionManager.duplicateDB_Meta(fromDocID, toDocID, docOwner, docOwnerZuid, fromSheet.getWorkbook().getDataConnection().getDataConnectionsFromSheet(fromSheet).keySet());
		if(!webdataIdsMap.isEmpty()) {
			jsonObj.put(JSONConstants.WEBDATA_IDS, webdataIdsMap);
		}
	}

	public static void copyLinkedDataSheetsToDestinationWorkbook(Workbook srcWorkbook, Workbook destWorkbook, Long fromDocID, Long toDocID, String docOwner, String docOwnerZuid, Map<String, String> sheetListMap) throws Exception {
		Collection<String> webDataIdsToCopy = new ArrayList<>();
		for (Map.Entry<String, DataRange> entry : srcWorkbook.getDataConnection().getDataConnections().entrySet()) {
			if (sheetListMap.containsKey(srcWorkbook.getSheetByAssociatedName(entry.getValue().getAssociatedSheetName()).getName())) {
				webDataIdsToCopy.add(entry.getKey());
			}
		}
		Map<String,String> webdataIdsMap = DBActionManager.duplicateDB_Meta(fromDocID, toDocID, docOwner, docOwnerZuid, webDataIdsToCopy);
		for (Map.Entry<String, String> entry : webdataIdsMap.entrySet()) {
			DataRange webDataRange = srcWorkbook.getDataConnection().getDataConnectionRange(entry.getValue());
			destWorkbook.getDataConnection().addOrUpdateDataConnection(destWorkbook, entry.getKey(), new DataRange(destWorkbook.getSheet(sheetListMap.get(srcWorkbook.getSheetByAssociatedName(webDataRange.getAssociatedSheetName()).getName())).getAssociatedName(), webDataRange.getStartRowIndex(), webDataRange.getStartColIndex(), webDataRange.getEndRowIndex(), webDataRange.getEndColIndex()));
		}

	}


}
