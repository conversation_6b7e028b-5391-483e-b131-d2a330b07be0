//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.datastorage;

import java.io.*;
import java.nio.charset.Charset;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.StoreFactory;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import com.zoho.sheet.action.externaldata.constants.ConstantHolder;
import com.zoho.sheet.action.externaldata.constants.DCLiveStatusCode;
import com.zoho.sheet.action.externaldata.constants.ServiceConstant;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.parser.CSVParser;
import com.zoho.sheet.action.externaldata.metastorage.DBActionManager;
//import com.zoho.sheet.action.externaldataanalysis.impl.ZFlow;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;

/**
 * <AUTHOR>
 *
 */
public class RWManager {
	public static Logger logger = Logger.getLogger(RWManager.class.getName());
	 public static void store(long webDataID, String spaceId, String docOwnerZUID, Long docId, CloudService service, List<String> deletedRecords, String subAction, Object result)throws Exception {
		 byte[] bytearray = null;
		 //List<String>
		 if(result instanceof  Map)
		 {
			 if("scheduleRefresh".equalsIgnoreCase(subAction) || "refresh".equalsIgnoreCase(subAction))
			 {
				 Map<String, String> storedData = RWManager.getCSVasMap(docOwnerZUID, spaceId, docId, webDataID);
				 deletedRecords.forEach(storedData::remove);
				 Map<String, String> newResultMap = new LinkedHashMap<>();
				 if(storedData.entrySet().iterator().hasNext())
				 {
					 Map.Entry<String,String> headerRow = storedData.entrySet().iterator().next();
					 newResultMap.put(headerRow.getKey(), headerRow.getValue());
				 }
				 for(Map.Entry<String, String> entry : ((Map<String, String>) result).entrySet())
				 {
					 if(storedData.get(entry.getKey()) != null)
					 {
						 storedData.put(entry.getKey(), entry.getValue());
					 }
					 else
					 {
						 newResultMap.put(entry.getKey(), entry.getValue());
					 }
				 }
				 newResultMap.putAll(storedData);
				 result = newResultMap;
			 }
			 StringBuilder sb = new StringBuilder();
			 int rowSize = 0;
			 boolean maxRowReached = false;
			 for (Map.Entry<String, String> entry : ((Map<String, String>) result).entrySet()) {
				 sb.append(entry.getKey()).append(",").append(entry.getValue()).append("\n");
				 rowSize++;
				 if(rowSize == ConstantHolder.MAX_RECORD_LIMIT)
				 {
					 maxRowReached = true;
					 break;
				 }
			 }
			 service.bean.setRowCount(rowSize);
			 service.bean.setMaxRowReached(maxRowReached);
			 bytearray = sb.toString().getBytes();
		 }
		 else if(result instanceof List) {
			 bytearray = ((List<String>)result).stream().collect(Collectors.joining("\n")).getBytes(); //NO I18N
			 service.bean.setRowCount(((List<String>)result).size());
		 }else  if(result instanceof String) {
			 bytearray = ((String)result).getBytes(Charset.forName("UTF-8"));//NO I18N
			 InputStream inputFS = new ByteArrayInputStream(((String) result).getBytes());
			 BufferedReader br = new BufferedReader(new InputStreamReader(inputFS));
			 service.bean.setRowCount(((Long)(br.lines().count())).intValue());
		 }
		 
			InputStream wdis = null;
			BufferedInputStream wdbis = null;
			BufferedOutputStream wdbos = null;
			OutputStream wdfos = null;
			//try {
				wdis = new ByteArrayInputStream(bytearray); 
				Store store = StoreFactory.getInstance().getWebDataStore(docOwnerZUID, spaceId, docId, webDataID);
				SheetFileInfo fileInfo = store.getFileInfo(webDataID, EngineConstants.FILENAME_WEBDATA, EngineConstants.FILEEXTN_CSV);
				if(fileInfo==null) {
					fileInfo = store.createFileInfo(webDataID, EngineConstants.FILENAME_WEBDATA, EngineConstants.FILEEXTN_CSV);
				}
				HashMap writeInfo = null;
				
				try {					  
					wdbis = new BufferedInputStream(wdis);
					writeInfo = store.write(webDataID, fileInfo);
					wdfos = (OutputStream)writeInfo.get("OS");
					wdbos = new BufferedOutputStream(wdfos);
					int BUF_LEN = 1024;
					byte[] bufr = new byte[BUF_LEN];
					int c = 0;
					while((c=wdbis.read(bufr,0,BUF_LEN)) != -1)
					{
						wdbos.write(bufr,0,c);
					}

				}catch(Exception e) {
					logger.log(Level.INFO,"Error Occured while writing webdata: ");
				}finally {
					if(wdis != null)
					{
						wdis.close();
					}
					if(wdbis != null)
					{
						wdbis.close();
					}
					if(wdbos != null)
					{
						wdbos.close();
					}					
					store.finishWrite(writeInfo);
				}
				
				
				logger.info("csv file saved : ");
			//}catch(Exception e) {
				//e.printStackTrace();
			//}
	 }

	 public static DataRange updateSheet(Workbook workBook, String docOwnerZUID, String docOwner, long docID, JSONObjectWrapper actionJson, Sheet sheetObj, int startRow, int startCol) throws Exception {
		 long webDataID = Long.parseLong(actionJson.optString(JSONConstants.WEBDATA_ID));
		 logger.info("[IMPORT_CLOUD_DATA] update Data in Sheet " + webDataID);
		 boolean firstTime = false;
		 int endRow = -1;
		 int endCol = -1;
		 String sheetName = sheetObj.getName();
		 String oldWebDataSheetName = null;
		 DataRange rangeObj = workBook.getDataConnection().getDataConnectionRange(String.valueOf(webDataID));
		 if (rangeObj == null && startRow != -1 && startCol != -1) {
			 firstTime = true;
		 } else {

			 try {

				 int cellPositionX = rangeObj.getStartColIndex();
				 int cellPositionY = rangeObj.getStartRowIndex();
				 int endcellPositionX = rangeObj.getEndColIndex();
				 int endcellPositionY = rangeObj.getEndRowIndex();
				 oldWebDataSheetName = workBook.getSheetByAssociatedName(rangeObj.getAssociatedSheetName()).getName();
				 if (sheetName.equals(oldWebDataSheetName) && startRow == cellPositionY && startCol == cellPositionX) {
					 //// Clearing Links In Cell for refresh (Note: New Links will set while setting data in cell)
					 for (int r = cellPositionY; r <= endcellPositionY; r++) {
						 for (int c = cellPositionX; c <= endcellPositionX; c++) {
							 Cell cell = sheetObj.getCellReadOnly(r, c);
							 if (cell != null) {
								 cell.setLink(null);
							 }
						 }
					 }
				 }

			 } catch (Exception e) {
				 logger.info("Exception Occurred: " + webDataID + " " + e);
			 }


		 }

		 BufferedReader br = null;
		 InputStream fis = null;

		 try {
			 Store store = StoreFactory.getInstance().getWebDataStore(docOwnerZUID, docOwner, docID, webDataID);

			 fis = store.read(webDataID, EngineConstants.FILEEXTN_CSV);
		 } catch (Exception e) {
			 logger.info("Exception Occurred:  " + e);
		 }
		 if (fis == null) {
			 return null;
		 }
		 br = new BufferedReader(new InputStreamReader(fis));
		 CSVReader reader = new CSVReader(br, ',', '\"', true, true);


		 int colLength = -1;
		 // read all csv data before updating into cell to find the no. of rows/cols to insert/delete
		 List<Vector> csvDataList = new ArrayList<Vector>();
		 while (true) {
			 try {
				 Vector lineData = reader.getAllFieldsInLine();
				 csvDataList.add(lineData);
				 int size = lineData.size();
				 if (size > colLength) {
					 colLength = size;
				 }
			 } catch (java.io.EOFException eof) {
				 break;
			 } catch (java.io.IOException io) {
				 logger.log(Level.WARNING, null, io);
				 if (reader != null) {
					 reader.close();
				 }
				 throw io;
			 }
		 }

		 try {
			 if (reader != null) {
				 reader.close();
			 }
		 } catch (Exception e) {

			 logger.log(Level.WARNING, "Exception while closing stream in constructwebdata.");
		 }

		 if (actionJson.has("newSize")) {
			 JSONObjectWrapper newSize = actionJson.getJSONObject("newSize");
			 endRow = startRow + (newSize.optInt("rowSize") - 1); // No I18N
			 endCol = startCol + (newSize.optInt("colSize") - 1); // No I18N
		 } else {
			 endRow = startRow + csvDataList.size() - 1;
			 endCol = startCol + colLength - 1;
		 }

		 JSONObjectWrapper colFormats = actionJson.optJSONObject(JSONConstants.COL_FORMAT);
		 int serviceConstant = actionJson.optInt(JSONConstants.SERVICE_CONSTANT);
		 for (int rowIndex = startRow, listIndex = 0; rowIndex <= endRow && listIndex < csvDataList.size(); rowIndex++, listIndex++) {
			 Vector lineData = csvDataList.get(listIndex);
			 int size = lineData.size();
			 int j = (serviceConstant == ServiceConstant.ZEXPENSE || serviceConstant == ServiceConstant.ZINVOICE || serviceConstant == ServiceConstant.ZCREATOR) ? 1 : 0;
			 for (int colIndex = startCol; j < size && colIndex <= endCol; j++, colIndex++) {
				 if (serviceConstant == ServiceConstant.UNAUTH_HTML || serviceConstant == ServiceConstant.UNAUTH_RSS) {
					 ActionUtil.detectAndSetCSVValue(sheetObj, rowIndex, colIndex, lineData.get(j).toString(), '.', ',', null, false, firstTime);
				 } else {

					 String cellValue = Utility.getDecodedString(lineData.get(j).toString());

					 char groupSep = ',';
					 char deciSep = '.';
					 DateUtil.DateFormatType dateFormatType = null;

					 if (serviceConstant == ServiceConstant.ZANALYTICS && colFormats != null) {
						 JSONObjectWrapper colFormat = colFormats.optJSONObject(String.valueOf(j));

						 if (colFormat != null) {
							 String groupSepStr = colFormat.optString("gs", ","); //No I18N
							 String deciSepStr = colFormat.optString("ds", "."); //No I18N

							 if (!groupSepStr.isEmpty()) {
								 groupSep = groupSepStr.charAt(0);
							 }
							 if (!deciSepStr.isEmpty()) {
								 deciSep = deciSepStr.charAt(0);
							 }

							 if (colFormat.has("df")) {
								 String dateFormat = colFormat.optString("df"); //No I18N
								 try {
									 dateFormatType = DateUtil.DateFormatType.valueOf(dateFormat);
								 } catch (IllegalArgumentException e) {
									 // Handle invalid date format as Text : 20-02-2025
									 cellValue = "'" + cellValue;
								 }
							 }
						 }
					 }
					 ActionUtil.detectAndSetCSVValue(sheetObj, rowIndex, colIndex, cellValue, deciSep, groupSep, dateFormatType, false, firstTime);
				 }
			 }
		 }

		 if (startRow == endRow) //has header only
		 {
			 endRow++;
			 if (!firstTime) {
				 RangeUtil.clearContent(sheetObj, endRow, startCol, endRow, endCol);
			 }
		 }

		 // To update column header names in the table.
		 Table table = TableUtil.getTableAtRange(workBook, new DataRange(sheetObj.getAssociatedName(), startRow, startCol, endRow, endCol), false);
		 if (table != null) {
			 table.makeColumnHeaderUnique();
		 }

		 DataRange newDataRange = new DataRange(sheetObj.getAssociatedName(), startRow, startCol, endRow, endCol);
		 if (workBook != null) {
			 ResponseUtil.getRowHeightXML(sheetObj, startRow, endRow);
			 if (actionJson.optString(JSONConstants.SUB_ACTION).equals("scheduleRefresh") && rangeObj == null) {
				 //migrated or nonmigrated old scheduler webdata.
				 DBActionManager.addOrUpdateModifiedRange(workBook, docOwner, sheetObj, webDataID, startRow, startCol, endRow, endCol);
			 } else {
				 if (!newDataRange.equals(rangeObj)) {
					 workBook.getDataConnection().addOrUpdateDataConnection(workBook, String.valueOf(webDataID), newDataRange);
				 }
			 }
			 if (!actionJson.optBoolean("fromundoredo")) {
				 DBActionManager.updateLiveStatusCode(docOwner, String.valueOf(webDataID), DCLiveStatusCode.ACTIVE);
			 }
		 }
		 logger.info("WebData update :: " + webDataID + " StartRow::" + startRow + ", StartCol::" + startCol + ", EndRow::" + endRow + ", EndCol::" + endCol);
		 return newDataRange;
	 }

	public static Map<String,String> getCSVasMap(String docOwnerZUID, String docOwner, long docID, long webDataID)
	{
		InputStream fis;
		try {
			Store store = StoreFactory.getInstance().getWebDataStore(docOwnerZUID,  docOwner, docID, webDataID);
			fis = store.read(webDataID, EngineConstants.FILEEXTN_CSV);
		}catch(Exception e) {
			logger.info("[DATA-CONNECTION]Exception Occurred@getCSVasMap:  "+e);
			return null;
		}

		return CSVParser.getParsedDataMap(fis);
	}


}
