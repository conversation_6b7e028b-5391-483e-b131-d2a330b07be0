//$Id$
/**
 *
 */
package com.zoho.sheet.action.externaldata.core.extend.app;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;

/**
 * <AUTHOR>
 *
 */
public class ZohoInventory extends CloudService{


	private static Logger logger = Logger.getLogger(ZohoInventory.class.getName());

	private boolean has_more_page;

	public ZohoInventory() {
		this.bean = new ServiceBean();
		this.bean.setServiceConstant(ServiceConstant.ZINVENTORY);
	}


	@Override
	public ServiceInfo getServiceInfo() {
		return ConstantHolder.getServiceInfo(ServiceConstant.ZINVENTORY);
	}

	@Override
	public void setAPIURL() {
		String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
		this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
		//this.apiurl = setPreciseQueryParam(this.apiurl);
		String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
		this.bean.setRequestURL(requestURL);
		this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));
	}

	@Override
	public void setPreciseHeader(ZSConnection httpCon) {
		switch(this.bean.getServiceActionConstant()){
			case ServiceActionConstants.GET_ORG:
			case ServiceActionConstants.FETCH_DATA:
			case ServiceActionConstants.GET_FIELDS:
			case ServiceActionConstants.GET_DELETED_RECORDS:
				String[] paramNames = {this.bean.getAccessTokenParamName()};
				String[] paramValues = {this.bean.getAccessToken()};
				httpCon.setRequestHeader(paramNames, paramValues);
				break;
		}
	}


	@Override
	public void setPreciseFormData(ZSConnection httpCon) {

				/*switch(this.bean.getServiceActionConstant()){
				case ServiceActionConstants.FETCH_DATA:
					String[] paramNames = {"organization_id"};//NO I18N
					String[] paramValues = {this.bean.getOrganizationID()};
					logger.info("paramNames: "+paramNames);
					logger.info("paramValues: "+this.bean.getOrganizationID());
					httpCon.setParameter(paramNames, paramValues);
					break;
				default :
					break;

				}	*/

	}

	@Override
	public Object parseData(Object resp) {
		JSONObjectWrapper respMessage = null;
		respMessage = new JSONObjectWrapper((String) resp);

		List resultArr = null;
		String application_owner = "";
		switch(this.bean.getServiceActionConstant())
		{
			case ServiceActionConstants.GET_ORG: //GET_ORG:
				resultArr = new ArrayList();
				JSONArrayWrapper orgArray = respMessage.getJSONArray("organizations");
				for (int i = 0; i < orgArray.length(); i++){
					JSONObjectWrapper orgObj = orgArray.optJSONObject(i);
					// user status values form the service are ACTIVE = 1,INACTIVE = 2,DELETED = 3;
					if(!orgObj.optBoolean("is_org_active") || orgObj.optInt("user_status") != 1)
					{
						continue;
					}
					boolean isInventoryOrg = false;
					for (Object appName : orgObj.optJSONArray("AppList").getJsonArray()) {
						if ("inventory".equals(appName)) {
							isInventoryOrg = true;
							break;
						}
					}
					if(!isInventoryOrg) {
						for (Object appName : orgObj.optJSONArray("org_joined_app_list").getJsonArray()) {
							if ("inventory".equals(appName)) {
								isInventoryOrg = true;
								break;
							}
						}
					}
					if(!isInventoryOrg)
					{
						continue;
					}
					String organization_id = orgObj.getString("organization_id");
					String name = Utility.getEncodedString(orgObj.getString("name"));
					List app = new ArrayList(2);
					app.add(organization_id);
					app.add(name);
					resultArr.add(app);
				}
				break;

			case ServiceActionConstants.GET_FIELDS:
				resultArr = ((ServiceInfoImpl)this.getServiceInfo()).getDefaultFields(this.bean.getModuleName());
				JSONArrayWrapper fields = respMessage.optJSONArray("fields"); //NO I18N
				//Adding CustomFields to result Array
				if(fields != null)
				{
					for (int i = 0; i < fields.length(); i++)
					{
						JSONObjectWrapper fieldObj = fields.optJSONObject(i);
						String fieldId = Utility.getEncodedString(fieldObj.getString("api_name"));
						String dataType = fieldObj.optString("data_type"); //No I18N
						if(!"non_pii".equalsIgnoreCase(fieldObj.optString("pii_type", "non_pii")) || "image".equalsIgnoreCase(fieldId)  || "multiline".equalsIgnoreCase(dataType) || "multiselect".equalsIgnoreCase(dataType) || "attachment".equalsIgnoreCase(dataType))
						{
							continue;
						}
						String fieldName = Utility.getEncodedString(fieldObj.getString("field_name_formatted"));
						List fieldArr = getFieldArr(fieldObj.optString("data_type_formatted"), fieldId, fieldName);  //NO I18N
						resultArr.add(fieldArr);
					}
				}
				break;

			case ServiceActionConstants.FETCH_DATA:
				resultArr = new ArrayList();
				JSONArrayWrapper dataArray = respMessage.optJSONArray(getFetchResponseKey(this.bean.getModuleName()));
				has_more_page = respMessage.optJSONObject("page_context").optBoolean("has_more_page");  //NO I18N
				if(dataArray == null || dataArray.length() == 0)
				{
					return resultArr;
				}

				String [] fieldLinkNames = bean.getFieldLinkNames();
				for (int i = 0; i < dataArray.length(); i++) {
					JSONObjectWrapper rowObj = dataArray.optJSONObject(i);
					String[] fieldRow = new String[fieldLinkNames.length];

					int j = 0;
					for (String field : fieldLinkNames) {
						Object content = rowObj.opt(field);
//					if(content instanceof JSONObject)
//					{
//						content = ((JSONObject) content).optString("display_value", ((JSONObject) content).optString("url"));  //NO I18N
//					}
						fieldRow[j++] = content != null ? Utility.getEncodedString(content.toString()) : "";
					}
					resultArr.add(String.join(",", fieldRow));
				}
//			case ServiceActionConstants.GET_DELETED_RECORDS:
//			{
//				dataArray = respMessage.optJSONArray("deletedtransactions");
//				resultArr = new ArrayList();
//				for (int i = 0; i < dataArray.length(); i++) {
//					JSONObject rowObj = dataArray.optJSONObject(i);
//					resultArr.add(rowObj.optString("transaction_id"));
//				}
//			}
//			break;


		}
		return resultArr;
	}

	private static List getFieldArr(String field_data_type, String fieldId, String fieldName) {
		int fieldType = FilterType.NONE.getTypeId();
		if("Text Box (Single Line)".equalsIgnoreCase(field_data_type) || "Email".equalsIgnoreCase(field_data_type) || "Url".equalsIgnoreCase(field_data_type) || "Phone".equalsIgnoreCase(field_data_type) || "AutoGenerate Number".equalsIgnoreCase(field_data_type))
		{
			fieldType = FilterType.TEXT.getTypeId();
		}
		else if("number".equalsIgnoreCase(field_data_type) || "Decimal".equalsIgnoreCase(field_data_type) || "Amount".equalsIgnoreCase(field_data_type) || "Percent".equalsIgnoreCase(field_data_type))
		{
			fieldType = FilterType.NUMBER.getTypeId();
		}
		else if("date".equalsIgnoreCase(field_data_type) || "Date and Time".equalsIgnoreCase(field_data_type))
		{
			fieldType = FilterType.DATE.getTypeId();
		}

		List fieldArr = new ArrayList<String>();

		fieldArr.add(fieldId);
		fieldArr.add(fieldName);
		fieldArr.add(fieldType);
		return fieldArr;
	}

	private String getFetchResponseKey(String moduleName)
	{
		switch (moduleName)
		{
			case "compositeitems":
				return "composite_items"; //NO I18N
			case "inventoryadjustments":
				return "inventory_adjustments"; //NO I18N
			case "transferorders":
				return "transfer_orders"; //NO I18N
		}
		return moduleName;
	}

	@Override
	public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {
		JSONObjectWrapper resp = new JSONObjectWrapper();
		List moduleFieldsList = null;
		if(this.bean.getModuleName() != null && !this.bean.getModuleName().isEmpty())
		{
			moduleFieldsList = new ArrayList();
			List fieldList = getFields();
			if (fieldList != null) {
				moduleFieldsList.add(fieldList);
			}
			resp.put("selectDataContent", moduleFieldsList);
		}
		else if(this.bean.getService_OrgID() != null && !this.bean.getService_OrgID().isEmpty())
		{
			moduleFieldsList = ((ServiceInfoImpl) getServiceInfo()).getModulesFields();
			resp.put("selectDataContent", moduleFieldsList);
		}
		else {
			this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
			setCommonDetails();
			setAccessToken();
			setAPIURL();
			List orgList = (List) sendRequest();
			resp.put("orgList", orgList == null ? Collections.emptyList() : orgList);
			if(orgList != null) {
				moduleFieldsList = new ArrayList();
				for (int i = 0; i < orgList.size(); i++) {
					Object org = orgList.get(i);
					JSONObjectWrapper orgModuleFieldList = new JSONObjectWrapper();
					orgModuleFieldList.put("org", (org));
					if(i == 0) {
						orgModuleFieldList.put("moduleFields", ((ServiceInfoImpl) getServiceInfo()).getModulesFields());
					}
					moduleFieldsList.add(orgModuleFieldList);
				}
			}
			resp.put("selectDataContent", moduleFieldsList.toString());
		}
		return resp;
	}

	@Override
	public List getFields()throws DataConnection.DataConnectionFailedException {
		String moduleName = this.bean.getModuleName();
		String customFieldEntityName = getCustomFieldEntityName(moduleName);
		if(customFieldEntityName == null) {
			return ((ServiceInfoImpl) getServiceInfo()).getDefaultFields(moduleName);
		}
		this.setCommonDetails();
		this.setAccessToken();
		ArrayList URLParam = new ArrayList();
		URLParam.add(this.bean.getService_OrgID());
		URLParam.add(customFieldEntityName);
		this.bean.setDynamicURLParam(URLParam);
		this.bean.setServiceActionConstant(ServiceActionConstants.GET_FIELDS);
		setAPIURL();
		return (List)sendRequest();
	}

//	public List getDeletedRecords() throws DataConnection.DataConnectionFailedException {
//		this.bean.setServiceActionConstant(ServiceActionConstants.GET_DELETED_RECORDS);
//		setCommonDetails();
//		setAccessToken();
//		setAPIURL();
//		return (List)sendRequest();
//	}

	@Override
	public void setPreRequestProcess() {
		ArrayList URLParam = new ArrayList();
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);

		switch(this.bean.getServiceActionConstant()) {
			case ServiceActionConstants.FETCH_DATA:
				if(this.bean.getCriteriaList().length()>0) {
					StringBuilder queryStr = new StringBuilder();
					for(int i=0;i<this.bean.getCriteriaList().length();i++){
						JSONArrayWrapper criteria = this.bean.getCriteriaList().getJSONArray(i);
						int logicOptr = criteria.getInt(0);
						String fieldLinkName = Utility.getDecodedString(criteria.getString(1));
						int fieldTypeId = criteria.getInt(2);
						int operatorID = criteria.getInt(3);
						String value1 = criteria.getString(4);

						try {
							if(i > 0)
							{
								queryStr.append("&");
							}
							if(fieldTypeId == FilterType.DATE.getTypeId() && ConstantHolder.FilterOperator.getFilterOperator(operatorID) == FilterOperator.BETWEEN)
							{
								String value2 = criteria.getString(5);
								if(!"null".equals(value1) && !"null".equals(value2))
								{
									value1 = ExternalDataUtils.getFormatedDate(Long.parseLong(value1), "yyyy-MM-dd"); //No I18N
									value2 = ExternalDataUtils.getFormatedDate(Long.parseLong(value2), "yyyy-MM-dd"); //No I18N
									queryStr.append(fieldLinkName).append("_start");
									queryStr.append("=").append(value1).append("&");
									queryStr.append(fieldLinkName).append("_end");
									queryStr.append("=").append(value2);
								}
							}
							else {
								queryStr.append(fieldLinkName).append(((ServiceInfoImpl) getServiceInfo()).getFilterOperatorString(ConstantHolder.FilterOperator.getFilterOperator(operatorID)));
								if (!"null".equals(value1)) {
									if (fieldTypeId == FilterType.DATE.getTypeId()) {
										value1 = ExternalDataUtils.getFormatedDate(Long.parseLong(value1), "yyyy-MM-dd"); //No I18N
									}
									queryStr.append("=").append(value1);
								}
							}
						}
						catch (Exception e)
						{
							logger.log(Level.INFO, "Exception Occurred in Criteria", e);
						}
					}
					if(!queryStr.toString().isEmpty()){
						this.bean.setCriteriaString(queryStr.toString());
					}
				}
				break;
		}

	}

	public String setPreciseQueryParam(String  url, int pageNo) {
		switch (this.bean.getServiceActionConstant()) {
			case ServiceActionConstants.FETCH_DATA:
				url = url + "?organization_id="+this.bean.getService_OrgID() + "&page=" + pageNo;
				if(this.bean.getCriteriaList().length()>0) {
					url = url + "&"+ this.bean.getCriteriaString();
				}
//				if(this.bean.isFetchModifiedRecords())
//				{
//					url += "&last_modified_time="+ExternalDataUtils.getEncoded_UTCTime(this.bean.getLastFetchedTime());
//				}
//				break;
//			case ServiceActionConstants.GET_DELETED_RECORDS:
//				url = url + "?page=" + pageNo;
//				url = url + "&filter_by=TransactionType."+this.bean.getModuleName()+"&deleted_time_start="+ExternalDataUtils.getEncoded_UTCTime(this.bean.getLastFetchedTime())+"&deleted_time_end="+ExternalDataUtils.getEncoded_UTCTime(System.currentTimeMillis());
				break;
			default:
				break;

		}
		return url;
	}

	@Override
	public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
		this.setPreRequestProcess();
		setAccessToken();

		setAPIURL();
		String urlStr = this.bean.getRequestURL();
		int page_no = 1;
		ArrayList resultArr = new ArrayList<>();

		String[] fieldHeaders = bean.getFieldHeaders();
		resultArr.add(String.join(",", fieldHeaders));
		has_more_page = true;
		while (has_more_page && page_no * 200 <= MAX_RECORD_LIMIT) {
			String reqURL = setPreciseQueryParam(urlStr, page_no);
			this.bean.setRequestURL(reqURL);

			ArrayList THR_Content = (ArrayList) sendRequest();
			if (THR_Content != null && !THR_Content.isEmpty()) {
				resultArr.addAll(THR_Content);
			} else {
//				if (!this.bean.isFetchModifiedRecords()) {
					this.has_more_page = false;
//				}
			}
			page_no++;
		}

		this.bean.setMaxRowReached(has_more_page);
		this.bean.setRowCount(resultArr.size());
		this.bean.setColCount(fieldHeaders.length);
		return resultArr;
	}

	@Override
	public String parseCode(String resp) {
		return new JSONObjectWrapper(resp).optString("code");//NO I18N
	}

	private static String getCustomFieldEntityName(String moduleName)
	{
		switch (moduleName) {
			case "contacts":
				return "contact"; // NO I18N
			case "items":
				return "item"; // NO I18N
			case "inventoryadjustments":
				return "inventory_adjustment"; // NO I18N
			case "transferorders":
				return "transfer_order"; // NO I18N
			case "packages":
				return "package"; // NO I18N
			case "invoices":
				return "invoice"; // NO I18N
			case "retainerinvoices":
				return "retainer_invoice"; // NO I18N
			case "customerpayments":
				return "customer_payment"; // NO I18N
			case "salesreturns":
				return "salesreturn"; // NO I18N
			case "salesorders":
				return "salesorder"; // NO I18N
			case "creditnotes":
				return "creditnote"; // NO I18N
			case "purchaseorders":
				return "purchaseorder"; // NO I18N
			case "bills":
				return "bill"; // NO I18N
			case "vendorcredits":
				return "vendor_credit"; // NO I18N
			case "users":
				return "users"; // NO I18N
		}
		return null;
	}
}
