//$Id$
/**
 *
 */
package com.zoho.sheet.action.externaldata.core.extend.app;

import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.action.externaldata.core.parser.CSVParser;
import com.zoho.sheet.connection.ZSConnection;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONWrapperException;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.MAX_RECORD_LIMIT;

/**
 * <AUTHOR>
 *
 */
public class ZohoCreator extends CloudService{


	private static Logger logger = Logger.getLogger(ZohoCreator.class.getName());

	public ZohoCreator() {
		this.bean = new ServiceBean();
		this.bean.setServiceConstant(ServiceConstant.ZCREATOR);
	}


	@Override
	public ServiceInfo getServiceInfo() {
		return ConstantHolder.getServiceInfo(ServiceConstant.ZCREATOR);
	}

	@Override
	public void setAPIURL() {
		String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
		this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
		//this.apiurl = setPreciseQueryParam(this.apiurl);
		String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
		this.bean.setRequestURL(requestURL);
		this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));
	}



	@Override
	public void setPreciseHeader(ZSConnection httpCon) {
		String[] paramNames = null;
		String[] paramValues = null;
		switch(this.bean.getServiceActionConstant()){
			case ServiceActionConstants.GET_OWNDBLIST:
			case ServiceActionConstants.GET_MODULES:
			case ServiceActionConstants.GET_FIELDS:
			case ServiceActionConstants.GET_DELETED_RECORDS:
			case ServiceActionConstants.FETCH_DATA:
				paramNames = new String[]{this.bean.getAccessTokenParamName(),"AGENT-TYPE"}; //NO I18N
				paramValues = new String[]{this.bean.getAccessToken(),"ZohoSheet"};//NO I18N
				httpCon.setRequestHeader(paramNames, paramValues);
				break;
		}
	}
	//public String setPreciseQueryParam(String  url, int from, int limit, String criteria) {
	public String setPreciseQueryParam(String  url) throws DataConnection.DataConnectionFailedException {
		switch(this.bean.getServiceActionConstant()){

			case ServiceActionConstants.FETCH_DATA:
				//url = url + "?" + (from > -1 ? "from=" + from : "") "from=" + from + "&limit=" + limit + (criteria != null ? "&criteria=" + criteria : "");
				url = url + "?fieldCompIds="+String.join(",", this.bean.getFieldLinkNames());
				if(this.bean.getFetchBatchMap()!=null){
						if(this.bean.getFetchBatchMap().containsKey("record_cursor")){
							url = url +"&record_cursor="+this.bean.getFetchBatchMap().get("record_cursor");
						}
				}
				if(this.bean.getCriteriaList().length()>0) {
					url = url + "&criteria="+ this.bean.getCriteriaString();
					if(this.bean.isFetchModifiedRecords())
					{
						url = url + Utility.getEncodedString( " && Modified_Time>=") + "'"+ExternalDataUtils.getEncoded_Time(this.bean.getLastFetchedTime(), getApplicationTimeZone(this.bean.getDatabaseName()),   getApplicationDateFormat(this.bean.getDatabaseName())+" HH:mm:ss")+"'";
					}
				}
				else if(this.bean.isFetchModifiedRecords()) {
					url = url + "&criteria="+ Utility.getEncodedString( "Modified_Time>=") + "'"+ExternalDataUtils.getEncoded_Time(this.bean.getLastFetchedTime(), getApplicationTimeZone(this.bean.getDatabaseName()),   getApplicationDateFormat(this.bean.getDatabaseName())+" HH:mm:ss")+"'";
				}
				break;
			case ServiceActionConstants.GET_DELETED_RECORDS:
				url = url + "?lastSyncTime="+ExternalDataUtils.getEncoded_Time(this.bean.getLastFetchedTime(), getApplicationTimeZone(this.bean.getDatabaseName()), "dd-MMM-yyyy HH:mm:ss");
			default :
				break;

		}
		return url;

	}


	@Override
	public void setPreciseFormData(ZSConnection httpCon) {

				/*switch(this.bean.getServiceActionConstant()){
				case ServiceActionConstants.FETCH_DATA:
					String[] paramNames = {"organization_id"};//NO I18N
					String[] paramValues = {this.bean.getOrganizationID()};
					logger.info("paramNames: "+paramNames);
					logger.info("paramValues: "+this.bean.getOrganizationID());
					httpCon.setParameter(paramNames, paramValues);
					break;
				default :
					break;

				}	*/

	}

	@Override
	public Object parseData(Object resp) {
		JSONObjectWrapper respMessage = null;

		try{
			if(this.bean.getServiceActionConstant() != ServiceActionConstants.FETCH_DATA) {
				respMessage = new JSONObjectWrapper((String)resp);
			}
		}catch (JSONWrapperException e){
			logger.log(Level.WARNING,"[DataConnection Creator Parse] ",e);
		}
		catch (Exception e){
			throw e;
		}
		List resultArr = null;
		String application_owner = "";
		switch(this.bean.getServiceActionConstant()){
			case ServiceActionConstants.GET_OWNDBLIST:
			/*application_owner = respMessage.getJSONObject("result").getString("application_owner");
			respMessage = respMessage.getJSONObject("result");
		case ServiceActionConstants.GET_SHAREDDBLIST:*/
				//JSONArray appsList = respMessage.getJSONObject("application_list").getJSONArray("applications");
				JSONArrayWrapper appsList = respMessage.optJSONArray("applications");//NO I18N
				resultArr = new ArrayList();
				if(appsList == null)
				{
					break;
				}
				String link_name ;
				String application_name;
				String timeZone;
				String dateFormat;
				for (int i = 0; i < appsList.length(); i++){
					JSONObjectWrapper appObj = appsList.getJSONObject(i);
					if(appObj.has("environments") && !appObj.getJSONArray("environments").toList().contains("production"))
					{
						continue;
					}
					link_name = Utility.getEncodedString(appObj.getString("link_name"));
					application_name = Utility.getEncodedString(appObj.getString("application_name"));
					application_owner = appObj.getString("workspace_name");
					timeZone = appObj.getString("time_zone");
					dateFormat = appObj.getString("date_format");
					List app = new ArrayList(7);
					app.add(link_name); //0
					app.add(application_name); //1
					app.add(application_owner); //2
					app.add(appObj.optInt("category") == 2); //3
					app.add(appObj.optBoolean("is_enabled") && appObj.optBoolean("is_editable")); //4
					app.add(timeZone); //5
					app.add(dateFormat); //6
					resultArr.add(app);
				}
				break;
			case ServiceActionConstants.GET_MODULES:
				JSONArrayWrapper forms = respMessage.optJSONArray("forms"); //No I18N
				resultArr = new ArrayList();
				if(forms != null) {
					for (int i = 0; i < forms.length(); i++) {
						JSONObjectWrapper form = forms.getJSONObject(i);
						if (form.getInt("type") == 1) {
							String formLinkName = Utility.getEncodedString(form.getString("link_name"));
							String formDisplayName = Utility.getEncodedString(form.getString("display_name"));
							resultArr.add(Arrays.asList(formLinkName, formDisplayName));
						}
					}
				}
				else
				{
					logger.log(Level.WARNING,"[Data-Connection] [ZohoCreator] error response while fetching modules >> {0}",new Object[]{respMessage});
				}
				break;
			case ServiceActionConstants.GET_FIELDS:
				JSONArrayWrapper fields = respMessage.optJSONArray("fields"); //No I18N
//				JSONArray fields = respMessage.getJSONArray("columns");

				resultArr = new ArrayList();
				if(fields != null) {
					for (int i = 0; i < fields.length(); i++) {
						JSONObjectWrapper field = fields.getJSONObject(i);
						String linkName = fields.getJSONObject(i).getString("link_name");
						String fieldID = field.optString("form_component_id"); //No I18N
						String dispName = Utility.getEncodedString(field.getString("display_name"));

						List app = new ArrayList(2);
						app.add(fieldID);
						app.add(dispName);
						app.add(getFieldType(field.getInt("type")));
						app.add(linkName);
						resultArr.add(app);
					}
				}
				else
				{
					logger.log(Level.WARNING,"[Data-Connection] [ZohoCreator] error response while fetching fields >> {0}",new Object[]{respMessage});
				}
				break;
			case ServiceActionConstants.FETCH_DATA:

				if(respMessage != null){
					resultArr = new ArrayList();
					JSONArrayWrapper dataArray = respMessage.optJSONArray("data"); //No I18N

					if(dataArray == null || dataArray.length() == 0)
					{
						return resultArr;
					}

					String [] fieldLinkNames = bean.getFieldLinkNames();
					resultArr = new ArrayList<String>();

//			resultArr.add(String.join(",", bean.getFieldHeaders()));
					for (int i = 0; i < dataArray.length(); i++) {
						JSONObjectWrapper rowObj = dataArray.optJSONObject(i);
						String[] fieldRow = new String[fieldLinkNames.length];

						int j = 0;
						for (String field : fieldLinkNames) {
							Object content = rowObj.opt(field);
							if(content instanceof JSONObjectWrapper)
							{
								content = ((JSONObjectWrapper) content).optString("display_value", ((JSONObjectWrapper) content).optString("url"));  //NO I18N
							}
							fieldRow[j++] = content != null ? Utility.getEncodedString(content.toString()) : "";
						}
						resultArr.add(String.join(",", fieldRow));
					}
				}else{
						Map<String, String> resultMap = ((CSVParser)getServiceInfo().getContentParser()).getParsedDataMap(new ByteArrayInputStream(((String)resp).getBytes()));
						resultMap.remove("ID");
						return resultMap;
						//this.bean.setRowCount(resultArr.size());
						//this.bean.setColCount(fieldHeaders.size());

				}

				break;

			case ServiceActionConstants.GET_DELETED_RECORDS:
				JSONArrayWrapper dataResult = respMessage.optJSONArray("record_ids"); //NO I18N
				resultArr = new ArrayList();
				for (int i = 0; i < dataResult.length(); i++) {
					resultArr.add(""+dataResult.get(i));
				}
				break;
		}
		return resultArr;
	}

	private int getFieldType(int serviceFieldType)
	{
		int fieldType = ConstantHolder.FilterType.TEXT.getTypeId();
		if(serviceFieldType >= 5 && serviceFieldType <=9 )
		{
			fieldType = ConstantHolder.FilterType.NUMBER.getTypeId();
		}
		else if(serviceFieldType == 10 || serviceFieldType == 11)
		{
			fieldType = ConstantHolder.FilterType.DATE.getTypeId();
		}
		else if(serviceFieldType == 34)
		{
			fieldType = ConstantHolder.FilterType.TIME.getTypeId();
		}
		return fieldType;
	}
	@Override
	public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {
		List resList = new ArrayList();
		List modules = new ArrayList();
		List DBList = null;


		if(this.bean.getDatabaseName()!=null && !this.bean.getDatabaseName().equals("")){ // Partial get Select DATA
			int j = 0;
			ArrayList URLParam = new ArrayList();
			URLParam.add(this.getApplicationOwnerName(this.bean.getDatabaseName()));
			URLParam.add(this.bean.getDatabaseName());
			this.bean.setDynamicURLParam(URLParam);



			if(this.bean.getModuleName()!=null &&  !this.bean.getModuleName().equals("")) {//Only Fields Name Fetching

				Map moduleFields = new LinkedHashMap<>();
				resList = new ArrayList();
				List<List> appformFieldList = getFields();
				moduleFields.put("field", appformFieldList);
				resList.add(moduleFields);

			}else{
				setCommonDetails();
				setAccessToken();
				this.bean.setServiceActionConstant(ServiceActionConstants.GET_MODULES);
				setAPIURL();

				List<List> formReportList = (List) sendRequest();
				resList = new ArrayList();
				for (List moduleList : formReportList) {
					Map moduleFields = new LinkedHashMap<>();

					String reportLinkName = (String) moduleList.get(0);
					this.bean.setModuleName(reportLinkName);

					if(j==0){
						List<List> appformFieldList = getFields();
						moduleFields.put("module", moduleList);
						moduleFields.put("field", appformFieldList);
						resList.add(moduleFields);
					}else{
						moduleFields.put("module", moduleList);
						//moduleFields.put("field", Empty);
						resList.add(moduleFields);
					}
					j++;
				}
			}

		}else{ //Initial get Select DATA
			ArrayList moduleFieldsList = null;
			this.bean.setServiceActionConstant(ServiceActionConstants.GET_OWNDBLIST);
			setCommonDetails();
			setAccessToken();
			setAPIURL();
			List<List> appList = (List) sendRequest();
			if (appList == null) {
				appList = Collections.emptyList();
			}
			int i = 0, j = 0;
			for (List appObj : appList) {
				Map DBmodules = new LinkedHashMap<>();
				String appLinkName = (String) appObj.get(0);
				String appName = (String) appObj.get(1);
				String appOwnerName = (String) appObj.get(2);
				boolean isShared = (boolean) appObj.get(3);
				boolean hasAllPermission = (boolean)appObj.get(4);

				if(!hasAllPermission){
					DBList = new ArrayList(3);
					DBList.add(appLinkName);
					DBList.add(appName);
					DBList.add(appOwnerName);
					DBmodules.put("database", DBList);
					DBmodules.put("isShared", isShared);
					DBmodules.put("has_permission_error", true);
					modules.add(DBmodules);
				}
				else {
					if (i == 0) {
						this.bean.setDatabaseName(appLinkName);
						ArrayList URLParam = new ArrayList();
						URLParam.add(appOwnerName);
						URLParam.add(appLinkName);
						this.bean.setDynamicURLParam(URLParam);

						this.bean.setServiceActionConstant(ServiceActionConstants.GET_MODULES);
						setAPIURL();

						List<List> formReportList = (List) sendRequest();
						moduleFieldsList = new ArrayList();
						j = 0;
						for (List moduleList : formReportList) {
							Map moduleFields = new LinkedHashMap<>();

							String reportLinkName = (String) moduleList.get(0);
							this.bean.setModuleName(reportLinkName);

							if (j == 0) {
								List<List> appformFieldList = getFields();
								moduleFields.put("module", moduleList);
								moduleFields.put("field", appformFieldList);
								moduleFieldsList.add(moduleFields);
							} else {
								moduleFields.put("module", moduleList);
								moduleFieldsList.add(moduleFields);
							}
							j++;
						}
					} else {
						Map moduleFields = new LinkedHashMap<>();
						moduleFieldsList = new ArrayList();
						moduleFieldsList.add(moduleFields);

					}

					DBList = new ArrayList(3);
					DBList.add(appLinkName);
					DBList.add(appName);
					DBList.add(appOwnerName);
					DBmodules.put("database", DBList);
					DBmodules.put("isShared", isShared);
					DBmodules.put("moduleFields", moduleFieldsList);
					modules.add(DBmodules);
				}


				i++;
			}
			resList.addAll(modules);
		}

//		}
		JSONObjectWrapper resp = new JSONObjectWrapper();
		resp.put("selectDataContent", resList);
		return resp;

	}
	public String getApplicationOwnerName(String databaseName) throws DataConnection.DataConnectionFailedException {
		if(this.bean.getServiceSpecificParam() != null && this.bean.getServiceSpecificParam().has("appOwnerName"))
		{
			return this.bean.getServiceSpecificParam().getString("appOwnerName");
		}
		return getApplicationMeta(databaseName).optString("appOwnerName"); //No I18N
	}

	public String getApplicationTimeZone(String databaseName) throws DataConnection.DataConnectionFailedException {
		if(this.bean.getServiceSpecificParam() != null && this.bean.getServiceSpecificParam().has("timeZone"))
		{
			return this.bean.getServiceSpecificParam().getString("timeZone");
		}
		return getApplicationMeta(databaseName).optString("timeZone"); //No I18N
	}

	public String getApplicationDateFormat(String databaseName) throws DataConnection.DataConnectionFailedException {
		if(this.bean.getServiceSpecificParam() != null && this.bean.getServiceSpecificParam().has("dateFormat"))
		{
			return this.bean.getServiceSpecificParam().getString("dateFormat");
		}
		return getApplicationMeta(databaseName).optString("dateFormat"); //No I18N
	}

	public JSONObjectWrapper getApplicationMeta(String databaseName) throws DataConnection.DataConnectionFailedException {
		if(this.bean.getServiceSpecificParam() != null)
		{
			return this.bean.getServiceSpecificParam();
		}

		int actualAction = this.bean.getServiceActionConstant();
		this.bean.setServiceActionConstant(ServiceActionConstants.GET_OWNDBLIST);
		setCommonDetails();
		setAccessToken();
		setAPIURL();
		List<List> appList = (List) sendRequest();

		List metaList = (List) appList.stream().filter(ap -> ap.get(0).equals(databaseName)).findFirst().get();
		JSONObjectWrapper serviceSpecificParam = this.bean.getServiceSpecificParam();
		if(serviceSpecificParam == null)
		{
			serviceSpecificParam = new JSONObjectWrapper();
		}
		this.bean.setServiceActionConstant(actualAction);
		serviceSpecificParam.put("appOwnerName", metaList.get(2));
		serviceSpecificParam.put("timeZone", metaList.get(5));
		serviceSpecificParam.put("dateFormat", metaList.get(6));
		this.bean.setServiceSpecificParam(serviceSpecificParam);
		return serviceSpecificParam;
	}

	public List getFields() throws DataConnection.DataConnectionFailedException {
		this.setCommonDetails();
		this.setAccessToken();
		this.setPreRequestProcess();

		this.bean.setServiceActionConstant(ServiceActionConstants.GET_FIELDS);
		setAPIURL();
		return (List) sendRequest();
	}
	public List<String> getDeletedRecords() throws DataConnection.DataConnectionFailedException {
		this.bean.setServiceActionConstant(ServiceActionConstants.GET_DELETED_RECORDS);
		setCommonDetails();
		setAccessToken();
		setAPIURL();
		this.bean.setRequestURL(setPreciseQueryParam(this.bean.getRequestURL()));
		return (List<String>)sendRequest();
	}
	@Override
	public void setPreRequestProcess() throws DataConnection.DataConnectionFailedException {


		ArrayList URLParam = new ArrayList();
		URLParam.add(this.getApplicationOwnerName(this.bean.getDatabaseName()));
		URLParam.add(this.bean.getDatabaseName());
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);
		StringBuilder queryStr = new StringBuilder();
		switch(this.bean.getServiceActionConstant()) {


			case ServiceActionConstants.FETCH_DATA:
				if(this.bean.getCriteriaList().length()>0) {
					//url = url + "&criteria="+ Utility.getEncodedString(this.bean.getCriteriaString());

					for(int i=0;i<this.bean.getCriteriaList().length();i++){
						JSONArrayWrapper criteria = this.bean.getCriteriaList().getJSONArray(i);
						int logicOptr = criteria.getInt(0);
						String fieldLinkName = Utility.getDecodedString(criteria.getString(1));
						int fieldTypeId = criteria.getInt(2);
						int operatorID = criteria.getInt(3);
						String value1 = Utility.getDecodedString(criteria.getString(4));
//						String value2 = criteria.getString(5);

						try {
							if(logicOptr == 0)
							{
								queryStr.append(" && ");
							} else if (logicOptr == 1) {
								queryStr.append(" || ");
							}
							queryStr.append(fieldLinkName).append(((ServiceInfoImpl)getServiceInfo()).getFilterOperatorString(ConstantHolder.FilterOperator.getFilterOperator(operatorID)));
							if(!"null".equals(value1))
							{
								if(fieldTypeId == ConstantHolder.FilterType.TEXT.getTypeId())
								{
									value1 = "\""+value1+"\"";
								}
								else if(fieldTypeId == ConstantHolder.FilterType.DATE.getTypeId())
								{
									value1 = ExternalDataUtils.getFormatedDate(Long.parseLong(value1),  getApplicationDateFormat(this.bean.getDatabaseName())); 
									value1 = "\""+value1+"\"";
								}
								queryStr.append("(").append(value1).append(")");
							}
						}
						catch (Exception e)
						{
							logger.log(Level.INFO, "Exception Occurred in Criteria", e);
						}

					}
					if(!queryStr.toString().isEmpty()){
						this.bean.setCriteriaString(Utility.getEncodedString(queryStr.toString()));
					}
				}
				break;

		}

	}

	@Override
	public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
		this.setPreRequestProcess();
		setAccessToken();

		setAPIURL();
		String urlStr = this.bean.getRequestURL();
		boolean existContent = true;
		Map<String, String> result = new LinkedHashMap<>();
		int loopCount = 0;
		String [] fieldHeaders = bean.getFieldHeaders();
		if(!this.bean.isFetchModifiedRecords()) {
			result.put("ID", String.join(",", fieldHeaders));
		}
		while(existContent && loopCount < MAX_RECORD_LIMIT){
			String reqURL = setPreciseQueryParam(urlStr);
			this.bean.setRequestURL(reqURL);

			Map<String, String>  THR_Content = (Map<String, String> ) sendRequest();
			if(THR_Content !=null && !THR_Content.isEmpty()){
				result.putAll(THR_Content);
			}else if(!this.bean.isFetchModifiedRecords()){
				existContent = false;
			}

			if(this.bean.getFetchBatchMap() == null){
				existContent = false;
			}
			result.putAll(THR_Content);
			loopCount = loopCount + 1000;
		}

		this.bean.setMaxRowReached(existContent);
		this.bean.setRowCount(result.size());
		this.bean.setColCount(fieldHeaders.length);
		return result;

	}
	/*public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
		this.setPreRequestProcess();
		setAccessToken();

		setAPIURL();
		String urlStr = this.bean.getRequestURL();
		boolean existContent = true;
		int from = 1;
		List resultArr = new ArrayList();

		String [] fieldHeaders = bean.getFieldHeaders();

		resultArr.add(String.join(",", fieldHeaders));

		while(existContent){
			String reqURL = setPreciseQueryParam(urlStr, from, 200, null);
			this.bean.setRequestURL(reqURL);

			List THR_Content = (List<String>)sendRequest();
			if(THR_Content !=null && !THR_Content.isEmpty()){
				if(from > MAX_FETCH_LIMIT)
				{
					this.bean.setMaxRowReached(existContent);
					break;
				}
				resultArr.addAll(THR_Content);
			}else{
				existContent = false;
			}
			from = from + 200;

		}


		//this.apiurl = setPreciseQueryParam(this.apiurl);

		this.bean.setRowCount(resultArr.size());
		this.bean.setColCount(fieldHeaders.length);
		return resultArr;
	}*/

	@Override

	public String parseCode(String resp) {
		try {
			JSONObjectWrapper responseJSON = new JSONObjectWrapper(resp);
				JSONObjectWrapper errorJson = responseJSON.optJSONObject("error"); //NO I18N
				return errorJson != null ? errorJson.optString("code") : responseJSON.optString("code", "");//NO I18N
		}
		catch (JSONWrapperException ignored){}
		return "";
	}

	public void setBatchIdentifierMap(HashMap headerRespHashMap){

		HashMap fetchBatchMap = null;
		if (headerRespHashMap != null) {

				if(headerRespHashMap.containsKey("record_cursor")){
					fetchBatchMap = new HashMap();
					fetchBatchMap.put("record_cursor",headerRespHashMap.get("record_cursor"));
				}
			}
		this.bean.setFetchBatchMap(fetchBatchMap);
		}


}
