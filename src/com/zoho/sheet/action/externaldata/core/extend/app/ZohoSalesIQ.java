package com.zoho.sheet.action.externaldata.core.extend.app;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;

import java.util.*;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;

public class ZohoSalesIQ  extends CloudService {

    public ZohoSalesIQ() {
        this.bean = new ServiceBean();
        this.bean.setServiceConstant(ServiceConstant.ZSALESIQ);
    }

    @Override
    public ServiceInfo getServiceInfo() {
        return ConstantHolder.getServiceInfo(ServiceConstant.ZSALESIQ);
    }

    @Override
    public void setAPIURL() {
        String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
        this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
        String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
        this.bean.setRequestURL(requestURL);
        this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));
    }

    @Override
    public void setPreciseHeader(ZSConnection httpCon) {
        switch(this.bean.getServiceActionConstant()){
            case ServiceActionConstants.GET_ORG:
            case ServiceActionConstants.FETCH_DATA:
                String[] paramNames = {this.bean.getAccessTokenParamName()};
                String[] paramValues = {this.bean.getAccessToken()};
                httpCon.setRequestHeader(paramNames, paramValues);
                break;
        }
    }

    public String setPreciseQueryParam(String  url, int from, int limit) {
        switch(this.bean.getServiceActionConstant()){
            case ServiceActionConstants.FETCH_DATA:
                url = url + "?index=" + from + "&limit=" + limit;
                break;
        }
        return url;
    }

    @Override
    public void setPreciseFormData(ZSConnection httpCon) {

    }

    @Override
    public Object parseData(Object resp) {
        JSONObjectWrapper respMessage = new JSONObjectWrapper((String)resp);
        List resultArr = null;

        switch (this.bean.getServiceActionConstant()){
            case ServiceActionConstants.FETCH_DATA :
                JSONArrayWrapper dataResult = respMessage.optJSONArray("data"); // No I18N
                resultArr = new ArrayList<String>();
                if (Objects.isNull(dataResult) || dataResult.isEmpty()) {
                    return resultArr;
                }

                String[] fieldLinkNames = this.bean.getFieldLinkNames();
                int fieldCount = fieldLinkNames.length;

                for (int i = 0; i < dataResult.length(); i++) {
                    JSONObjectWrapper rowObj = dataResult.optJSONObject(i);
                    String[] fieldRow = new String[fieldCount];

                    for (int j = 0; j < fieldCount; j++) {
                        String field = fieldLinkNames[j];
                        Object content = rowObj.opt(field);

                        if (content instanceof JSONObjectWrapper) {
                            if (((JSONObjectWrapper)content).isEmpty()) {
                                content = "";
                            } else {
                                content = ((JSONObjectWrapper)content).has("name") ? ((JSONObjectWrapper)content).optString("name") // No I18N
                                        : ((JSONObjectWrapper)content).optString(((JSONObjectWrapper)content).keys().next());
                            }
                        }

                        fieldRow[j] = JSONObjectWrapper.NULL.equals(content) ? "" : Utility.getEncodedString(content.toString());
                    }

                    resultArr.add(String.join(",", fieldRow));
                }
                break;
            case ServiceActionConstants.GET_ORG:
                resultArr = new ArrayList();
                JSONArrayWrapper orgArray = respMessage.getJSONArray("data");
                if(orgArray.isEmpty()){
                    return resultArr;
                }
                for (int i = 0; i < orgArray.length(); i++) {
                    List orgMeta = new ArrayList<String>();
                    JSONObjectWrapper rowObj = orgArray.optJSONObject(i);
                    String id = rowObj.optString("screenname");//NO I18N
                    String name = Utility.getEncodedString(rowObj.optString("name"));//NO I18N
                    orgMeta.add(id);
                    orgMeta.add(name);
                    resultArr.add(orgMeta);
                }
                break;
        }

        return resultArr;
    }

    @Override
    public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {
        JSONObjectWrapper resp = new JSONObjectWrapper();
        this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
        setCommonDetails();
        setAccessToken();
        setAPIURL();
        List orgList = (List)sendRequest();
        resp.put("orgList", orgList);
        resp.put("selectDataContent", ((ServiceInfoImpl)getServiceInfo()).getModulesFields());
        return resp;
    }

    @Override
    public List getFields() throws DataConnection.DataConnectionFailedException {
        String moduleName = this.bean.getModuleName();
        for(Object moduleField : ((ServiceInfoImpl)getServiceInfo()).getModulesFields()) {
            if (((List) ((Map) moduleField).get(MODULES)).get(0).equals(moduleName)) {
                return (List)((Map) moduleField).get(FIELDS);
            }
        }
        return Collections.emptyList();
    }

    @Override
    public void setPreRequestProcess() throws DataConnection.DataConnectionFailedException {
        List URLParam = new ArrayList();
        URLParam.add(this.bean.getService_OrgID());
        URLParam.add(this.bean.getModuleName());
        this.bean.setDynamicURLParam(URLParam);
    }

    @Override
    public String parseCode(String resp) {
        return new JSONObjectWrapper(resp).optString("errorCode");//NO I18N
    }

    public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
        this.setPreRequestProcess();
        setAccessToken();

        setAPIURL();
        String urlStr = this.bean.getRequestURL();
        int from = 1;
        List resultArr = new ArrayList();

        String [] fieldHeaders = bean.getFieldHeaders();

        resultArr.add(String.join(",", fieldHeaders));

        Integer limit = ((ServiceInfoImpl) getServiceInfo()).fetchLimit.get(this.bean.getModuleName());

        if(limit == null)
        {
            if(this.bean.getCriteriaString() != null) {
                urlStr = urlStr +this.bean.getCriteriaString().replaceFirst("&", "?");
                this.bean.setRequestURL(urlStr);
            }
            List THR_Content = (List<String>)sendRequest();
            resultArr.addAll(THR_Content);
        }

        boolean has_more_page = true;
        while(limit != null && has_more_page){
            String reqURL = setPreciseQueryParam(urlStr, from, limit);
            this.bean.setRequestURL(reqURL);

            List THR_Content = (List<String>)sendRequest();
            if(THR_Content !=null && !THR_Content.isEmpty()){
                if(from > MAX_RECORD_LIMIT)
                {
                    this.bean.setMaxRowReached(has_more_page);
                    break;
                }
                resultArr.addAll(THR_Content);
            }
            else
            {
                has_more_page = false;
            }
            from = from + limit;
        }
        this.bean.setRowCount(resultArr.size());
        this.bean.setColCount(fieldHeaders.length);
        return resultArr;
    }
}
