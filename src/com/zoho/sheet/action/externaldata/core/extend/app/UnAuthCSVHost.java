//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.core.extend.app;


import com.zoho.sheet.action.externaldata.constants.ConstantHolder;
import com.zoho.sheet.action.externaldata.constants.ServiceConstant;
import com.zoho.sheet.action.externaldata.constants.ServiceInfo;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;
import com.zoho.sheet.util.textimport.ContentParser;
import com.zoho.sheet.util.textimport.PasteTextListener;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *
 */
public class UnAuthCSVHost extends CloudService{

	private static Logger logger = Logger.getLogger(UnAuthCSVHost.class.getName());

	public UnAuthCSVHost() {
		this.bean = new ServiceBean();
		this.bean.setServiceConstant(ServiceConstant.UNAUTH_CSV);
		this.bean.setMethod("GET"); //NO I18N
	}
	@Override
	public void setAPIURL() {}
	@Override
	public void setPreciseHeader(ZSConnection httpCon) {}
	@Override
	public void setPreciseFormData(ZSConnection httpCon) {}
	@Override
	public Object parseData(Object resp) {

		if(this.bean.isPreview())
		{
			StringBuilder toRet = new StringBuilder();
			String [] dataArray = ((String) resp).trim().split("\n");
			for(int i = 0; i < Math.min(dataArray.length, 16); i++)
			{
				toRet.append(dataArray[i]).append("\n");
			}
			this.bean.setRowCount(dataArray.length);
			return toRet.toString();
		}
		else {
			JSONArrayWrapper delimiters = this.bean.getDelimiters();
			if(delimiters == null)
			{
				// Handling added for CSV Data URLS backward compatibility.
				delimiters = new JSONArrayWrapper();
				delimiters.put(",");
			}
			char [] delimiterArr = new char[delimiters.length()];
			String otherDelimiter = "";
			for(int i = 0; i < delimiters.length(); i++)
			{
				String delimiter = delimiters.getString(i);
				if(delimiter.length() == 1) {
					delimiterArr[i] = delimiter.charAt(0);
				}
				else
				{
					otherDelimiter = delimiter;
				}
			}

			List resultArr = new ArrayList<String>();
			int colLength = 0;
			ContentParser parser = new ContentParser(delimiterArr, '\"', false, 0, 1, true); //NO I18N
			parser.addOtherDelimiter(otherDelimiter);
			PasteTextListener listener = new PasteTextListener();
			parser.addListener(listener);
			parser.parseContent((String)resp);
			JSONArrayWrapper parsedDataAsJSON = listener.getParsedDataAsJSON();
			this.bean.setRowCount(parsedDataAsJSON.length());

			for(int r = 0; r < parsedDataAsJSON.length(); r++)
			{
				JSONArrayWrapper rowData = parsedDataAsJSON.getJSONArray(r);
				if (colLength < rowData.length()) {
					colLength = rowData.length();
				}
				String[] fieldRow = new String[rowData.length()];
				int k = 0;
				for (int c = 0; c < rowData.length(); c++)
				{
					fieldRow[k++] = rowData.getString(c);
				}
				resultArr.add(String.join(",", fieldRow));
			}
			this.bean.setColCount(colLength);
			return resultArr;
		}

//		CSVReader reader = new CSVReader(new StringReader((String)resp),',','\"',true,true);
//		int colLength = -1;
//		List<Vector> csvDataList = new ArrayList<Vector>();
//		while(true)
//		{
//			try
//			{
//				Vector lineData = reader.getAllFieldsInLine();
//				//logger.info("lineData: "+lineData);
//				csvDataList.add(lineData);
//				int size = lineData.size();
//				if(size > colLength)
//				{
//					colLength = size;
//				}
//			}
//			catch(java.io.EOFException eof)
//			{
//				break;
//			}
//			catch (java.io.IOException io)
//			{
//				logger.log(Level.WARNING,null,io);
//				if(reader != null) {
//					try {
//						reader.close();
//					} catch (IOException e) {
//						logger.log(Level.WARNING, "Exception while closing stream in constructwebdata.");
//					}
//				}
//			}
//		}
//
//		try
//		{
//			if(reader != null) {
//				reader.close();
//			}
//		} catch (Exception e) {
//
//			logger.log(Level.WARNING, "Exception while closing stream in constructwebdata.");
//		}
//
//		this.bean.setRowCount(csvDataList.size());
//		this.bean.setColCount(colLength);

//		return resp;
	}
	@Override
	public ServiceInfo getServiceInfo() {
		return ConstantHolder.getServiceInfo(ServiceConstant.UNAUTH_CSV);
	}


	@Override
	public JSONObjectWrapper getSelectDataContent() {return null;}
	@Override
	public List getFields() {return null;}
	@Override
	public void setPreRequestProcess() {}
	@Override
	public Object fetchServiceContent() {return null;}

	@Override
	public String parseCode(String resp) {
		return null;
	}


}
