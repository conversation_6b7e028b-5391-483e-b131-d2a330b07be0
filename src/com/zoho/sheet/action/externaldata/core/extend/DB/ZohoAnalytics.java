//$Id$
package com.zoho.sheet.action.externaldata.core.extend.DB;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.action.externaldata.core.parser.CSVParser;
import com.zoho.sheet.connection.ZSConnection;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONWrapperException;

public class ZohoAnalytics extends CloudService {
	
	private static Logger logger = Logger.getLogger(ZohoAnalytics.class.getName());

	public ZohoAnalytics() {
		this.bean = new ServiceBean();
		this.bean.setServiceConstant(ServiceConstant.ZANALYTICS);
	}
	
	@Override
	public ServiceInfo getServiceInfo() {
		return ConstantHolder.getServiceInfo(ServiceConstant.ZANALYTICS);
	}
	@Override
	public void setAPIURL() {
		String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
		this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
		this.apiurl = setPreciseQueryParam(this.apiurl);
		String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
		this.bean.setRequestURL(requestURL);
		this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));		
	}

	@Override
	public void setPreciseHeader(ZSConnection httpCon) {
		String[] paramNames = new String[3];
		String[]  paramValues = new String[3];
		switch(this.bean.getServiceActionConstant()){
			case ServiceActionConstants.FETCH_DATA:
			case ServiceActionConstants.GET_MODULES:
			case ServiceActionConstants.GET_PERMISSION:
				paramNames [2] = "ZANALYTICS-ORGID"; //NO I18N
				paramValues [2] = this.bean.getService_OrgID();
			case ServiceActionConstants.GET_FIELDS:
			case ServiceActionConstants.GET_OWNDBLIST:
			case ServiceActionConstants.GET_ORG:
				paramNames [0] = "Authorization"; //NO I18N
				paramValues [0] = this.bean.getAccessToken();
				paramNames [1] = "X-ZOHO-SERVICE"; //NO I18N
				paramValues [1] = "ZohoSheet"; //NO I18N
				break;
			}
		httpCon.setRequestHeader(paramNames, paramValues);
	}
	
	public String setPreciseQueryParam(String  url) {
		switch(this.bean.getServiceActionConstant()){
			case ServiceActionConstants.GET_FIELDS:
				url = url +"?CONFIG="+Utility.getEncodedString("{withInvolvedMetaInfo:true}");
				break;
			case ServiceActionConstants.FETCH_DATA:
				JSONObjectWrapper config = new JSONObjectWrapper();
				if(this.bean.getFieldLinkNames().length > 0)
				{
					config.put("selectedColumns", this.bean.getFieldLinkNames());
				}
				if(this.bean.getCriteriaString() != null && !this.bean.getCriteriaString().isEmpty())
				{
					config.put("criteria", this.bean.getCriteriaString());
				}

				if(!config.isEmpty())
				{
					url += "?CONFIG="+Utility.getEncodedString(config.toString());
				}
				break;
			default :
				break;
			
		}	
		return url;

}

	@Override
	public void setPreciseFormData(ZSConnection httpCon) {
	}

	@Override
	public String parseCode(String resp) {
		try {
			JSONObjectWrapper responseJSON = new JSONObjectWrapper(resp);
			if (responseJSON.has("response")) {
				JSONObjectWrapper errorJson = responseJSON.getJSONObject("response").optJSONObject("error"); //NO I18N
				return errorJson != null ? errorJson.optString("code") : "";//NO I18N
			}
			else if(responseJSON.has("data")){
				return responseJSON.getJSONObject("data").optString("errorCode", ""); //NO I18N
			}
		}
		catch (JSONWrapperException ignored){}
		return "";
	}

	@Override
	public Object parseData(Object resp) {
		JSONObjectWrapper respMessage = null;
		if(this.bean.getServiceActionConstant() != ServiceActionConstants.FETCH_DATA) {
			respMessage = new JSONObjectWrapper((String)resp);
		}
		
		List resultArr = null;
		switch(this.bean.getServiceActionConstant()){
		case ServiceActionConstants.GET_OWNDBLIST:
			resultArr = new ArrayList();
			if(!respMessage.has("data"))
			{
				return resultArr;
			}
			JSONObjectWrapper workSpaceObj = respMessage.getJSONObject("data");
			JSONArrayWrapper ownAppsList = workSpaceObj.optJSONArray("ownedWorkspaces"); //NO I18N
			JSONArrayWrapper sharedAppsList = workSpaceObj.optJSONArray("sharedWorkspaces"); //NO I18N

			JSONArrayWrapper [] appList = new JSONArrayWrapper[]{ownAppsList, sharedAppsList};

			boolean isShared = false;
			for(JSONArrayWrapper apps : appList) {
				for (int i = 0; i < apps.length(); i++) {
					JSONObjectWrapper appObj = apps.getJSONObject(i);
					String link_name = appObj.getString("workspaceId");
					String application_name = Utility.getEncodedString(appObj.getString("workspaceName"));
					String orgId = appObj.getString("orgId");
					List app = new ArrayList(4);
					app.add(link_name);
					app.add(application_name);
					app.add(orgId);
					app.add(isShared);
					resultArr.add(app);
				}
				isShared = true;
			}
				
			break;
		case ServiceActionConstants.GET_MODULES: 
			JSONArrayWrapper viewsList  = null;
			JSONObjectWrapper resultObj = respMessage.optJSONObject("data"); //NO I18N
			if(resultObj != null)
			{
				viewsList = resultObj.optJSONArray("views"); //NO I18N
			}
			resultArr = new ArrayList();
			if(viewsList == null)
			{
				return resultArr;
			}
			for (int i = 0; i < viewsList.length(); i++){
				String viewType = viewsList.getJSONObject(i).getString("viewType");
				if("Table".equals(viewType) || "QueryTable".equals(viewType) || "PipelineTable".equals(viewType)) {
					
					JSONObjectWrapper tableView = viewsList.getJSONObject(i);
					String viewName = Utility.getEncodedString(tableView.getString("viewName"));
					String viewId = tableView.getString("viewId");
					resultArr.add(Arrays.asList(viewId, viewName));
				}
			}
			break;
			case ServiceActionConstants.GET_FIELDS:
				resultObj = respMessage.has("data") ? respMessage.getJSONObject("data").optJSONObject("views") : null; //NO I18N
				resultArr = new ArrayList();
				if(resultObj == null)
				{
					return resultArr;
				}
				String viewType = resultObj.getString("viewType");
				if("Table".equals(viewType) || "QueryTable".equals(viewType) || "PipelineTable".equals(viewType)) {
					JSONArrayWrapper columnsList = resultObj.getJSONArray("columns");
					List colInfoList;
					Map formatInfoList;
					for(int j = 0; j < columnsList.length(); j++) {
						JSONObjectWrapper colInfo = columnsList.getJSONObject(j);
						colInfoList = new ArrayList();
						formatInfoList = new HashMap();
						String columnName = Utility.getEncodedString(colInfo.getString("columnName"));
						colInfoList.add(columnName);
						colInfoList.add(columnName);
						colInfoList.add(getFieldType(colInfo.getString("dataTypeName")));
						colInfoList.add(null);

						if(colInfo.has("dateFormat")) {
							formatInfoList.put("df", ExternalDataUtils.getDateFormatType(colInfo.getString("dateFormat").toLowerCase()));
						}
						if (colInfo.has("thousandSeparator")) {
							formatInfoList.put("gs", colInfo.getString("thousandSeparator"));
						}
						if (colInfo.has("decimalSeparator")) {
							formatInfoList.put("ds", colInfo.getString("decimalSeparator"));
						}

						resultArr.add(Arrays.asList(colInfoList, formatInfoList));
					}
				}
				break;
			case ServiceActionConstants.FETCH_DATA:
				resultArr = ((CSVParser)getServiceInfo().getContentParser()).getParsedData(((String)resp).getBytes(), true);
				if(resultArr.size() > ConstantHolder.MAX_RECORD_LIMIT)
				{
					this.bean.setMaxRowReached(true);
					String headerLine = (String) resultArr.get(0);
					resultArr = resultArr.subList(resultArr.size() - ConstantHolder.MAX_RECORD_LIMIT, resultArr.size());
					resultArr.add(0,headerLine);
				}
				this.bean.setRowCount(resultArr.size());
				this.bean.setColCount(this.bean.getFieldHeaders().length);
				break;
			case ServiceActionConstants.GET_ORG:
				resultArr = new ArrayList();
				JSONObjectWrapper orgInfo = respMessage.optJSONObject("data").optJSONObject("workspaces"); //NO I18N
				resultArr.add(orgInfo.get("orgId"));
				break;
			case ServiceActionConstants.GET_PERMISSION:
				resultArr = new ArrayList();
				JSONObjectWrapper permissions = respMessage.optJSONObject("data").optJSONObject("permissions"); //NO I18N
				resultArr.add(permissions.get("export"));
				break;
	}
		return resultArr;
	}

	private int getFieldType(String serviceFieldType) {
		switch (serviceFieldType.toUpperCase()) {
			case "NUMBER":
			case "POSITIVE NUMBER":
			case "DECIMAL NUMBER":
			case "CURRENCY":
			case "PERCENT":
			case "AUTO NUMBER":
			case "AUTO":
				return ConstantHolder.FilterType.NUMBER.getTypeId();
			case "DATE":
				return ConstantHolder.FilterType.DATE.getTypeId();
		}
		return ConstantHolder.FilterType.TEXT.getTypeId();
	}
	
	@Override
	public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {
	
		List resList = new ArrayList();
		setCommonDetails();
		if(this.bean.getDatabaseName()!=null && !this.bean.getDatabaseName().equals("")) {

			if(this.bean.getModuleName()!=null &&  !this.bean.getModuleName().equals(""))
			{
				Map moduleFields = new LinkedHashMap<>();
				boolean hasPermission = hasAllPermissions();
				if(hasPermission) {
					moduleFields.put("field", getFields());
				}
				else
				{
					moduleFields.put("has_permission_error", true);
				}
				resList.add(moduleFields);
			}
			else {
				ArrayList URLParam = new ArrayList();
				URLParam.add(this.bean.getDatabaseName());
				this.bean.setDynamicURLParam(URLParam);
				this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
				setAccessToken();
				setAPIURL();
				this.bean.setService_OrgID(((List) sendRequest()).get(0).toString());
				this.bean.setServiceActionConstant(ServiceActionConstants.GET_MODULES);
				setAPIURL();
				List<List> modulesList = (List) sendRequest();
				int i = 0;
				for (List module : modulesList) {
					Map<String, Object> moduleFields = new LinkedHashMap<>();
					if (i == 0) {
						this.bean.setModuleName((String) module.get(0));
						boolean hasPermission =hasAllPermissions();
						if(hasPermission) {
							moduleFields.put("field", getFields());
						}
						else
						{
							moduleFields.put("has_permission_error", true);
						}
					}
					moduleFields.put("module", module);
					i++;
					resList.add(moduleFields);
				}
			}
		}else {
			this.bean.setServiceActionConstant(ServiceActionConstants.GET_OWNDBLIST);
			setAccessToken();
			setAPIURL();
			List<List> appList = (List) sendRequest();

			List DBList = null;
			int appIndex = 0;
			if (appList != null) {
				for (List appObj : appList) {
					Map DBmodules = new LinkedHashMap<>();
					String appLinkName = (String) appObj.get(0);
					String appName = (String) appObj.get(1);
					if(appIndex == 0) {
						this.bean.setService_OrgID((String) appObj.get(2));
						ArrayList URLParam = new ArrayList();
						URLParam.add(appLinkName);
						this.bean.setDynamicURLParam(URLParam);
						this.bean.setServiceActionConstant(ServiceActionConstants.GET_MODULES);
						setAPIURL();
						List<List> modulesList = (List) sendRequest();
						List moduleFieldsList = new ArrayList();
						int moduleIndex = 0;
						for (List module : modulesList) {
							Map<String, Object> moduleFields = new LinkedHashMap<>();
							if (moduleIndex == 0) {
								this.bean.setDatabaseName(appLinkName);
								this.bean.setModuleName((String) module.get(0));
								boolean hasPermission = hasAllPermissions();
								if(hasPermission) {
									moduleFields.put("field", getFields());
								}
								else
								{
									moduleFields.put("has_permission_error", true);
								}
							}
							moduleFields.put("module", module);
							moduleIndex++;
							moduleFieldsList.add(moduleFields);
						}
						DBmodules.put("moduleFields", moduleFieldsList);
					}
					appIndex++;
					DBList = new ArrayList(2);
					DBList.add(appLinkName);
					DBList.add(appName);
					DBmodules.put("isShared", appObj.get(3));
					DBmodules.put("database", DBList);

					resList.add(DBmodules);
				}
			}
		}

		JSONObjectWrapper resp = new JSONObjectWrapper();
		resp.put("selectDataContent", resList);
			return resp;
	}

	public List getFields() throws DataConnection.DataConnectionFailedException {
		this.setCommonDetails();
		this.setAccessToken();
		ArrayList URLParam = new ArrayList();
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);

		this.bean.setServiceActionConstant(ServiceActionConstants.GET_FIELDS);
		setAPIURL();

		List fieldsInfo = new ArrayList();

		for (Object fieldMeta : (List) sendRequest())
		{
			fieldsInfo.add(((List)fieldMeta).get(0));
		}

		return fieldsInfo;
	}

	public boolean hasAllPermissions() throws DataConnection.DataConnectionFailedException {
		this.setCommonDetails();
		this.setAccessToken();
		if(this.bean.getService_OrgID() == null) {
			ArrayList URLParam = new ArrayList();
			URLParam.add(this.bean.getDatabaseName());
			this.bean.setDynamicURLParam(URLParam);
			this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
			setAPIURL();
			this.bean.setService_OrgID(((List) sendRequest()).get(0).toString());
		}
		ArrayList URLParam = new ArrayList();
		URLParam.add(this.bean.getDatabaseName());
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);

		this.bean.setServiceActionConstant(ServiceActionConstants.GET_PERMISSION);
		setAPIURL();
		return (boolean)((List) sendRequest()).get(0);
	}
	@Override
	public void setPreRequestProcess() {
		List URLParam = new ArrayList();
		URLParam.add(this.bean.getDatabaseName());
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);
		logger.info("setPreRequestProcess: "+URLParam);

		switch(this.bean.getServiceActionConstant()) {
			case ServiceActionConstants.FETCH_DATA:
				StringBuilder criteriaBuilder = new StringBuilder();
				if (this.bean.getCriteriaList().length() > 0) {
					for (int i = 0; i < this.bean.getCriteriaList().length(); i++) {
						JSONArrayWrapper criteria = this.bean.getCriteriaList().getJSONArray(i);
						int logicOptr = criteria.getInt(0);
						String fieldLinkName = Utility.getDecodedString(criteria.getString(1));
						int fieldTypeId = criteria.getInt(2);
						int operatorID = criteria.getInt(3);
						String value1 = Utility.getDecodedString(criteria.getString(4));
//						String value2 = criteria.getString(5);
						try {
							if (logicOptr == 0) {
								criteriaBuilder.append(" and ");
							} else if (logicOptr == 1) {
								criteriaBuilder.append(" or ");
							}
							criteriaBuilder.append("(");
							ConstantHolder.FilterOperator filterOperator = ConstantHolder.FilterOperator.getFilterOperator(operatorID);
							criteriaBuilder.append("\"").append(fieldLinkName).append("\"").append(((ServiceInfoImpl) getServiceInfo()).getFilterOperatorString(filterOperator));
							if (!"null".equals(value1)) {
								if(fieldTypeId == ConstantHolder.FilterType.TEXT.getTypeId())
								{
									if(filterOperator == ConstantHolder.FilterOperator.BEGINNING_WITH)
									{
										value1 = value1+"%";
									}
									else if(filterOperator == ConstantHolder.FilterOperator.CONTAINS || filterOperator == ConstantHolder.FilterOperator.NOT_CONTAINS)
									{
										value1 = "%"+value1+"%";
									}
								}
								if (fieldTypeId == ConstantHolder.FilterType.DATE.getTypeId()) {
									value1 = ExternalDataUtils.getFormatedDate(Long.parseLong(value1), "yyyy-MM-dd hh:mm:ss"); //No I18N
								}
								criteriaBuilder.append("'").append(value1).append("'").append(")");
							}
						} catch (Exception e) {
							logger.log(Level.INFO, "Exception Occurred in Criteria", e);
						}

					}
				}
				this.bean.setCriteriaString(criteriaBuilder.toString());
				break;
		}

	}

	public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {

		this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
		setAccessToken();
		setAPIURL();
		this.bean.setService_OrgID(((List) sendRequest()).get(0).toString());

		this.bean.setServiceActionConstant(ServiceActionConstants.GET_FIELDS);
		ArrayList URLParam = new ArrayList();
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);

		this.bean.setServiceActionConstant(ServiceActionConstants.GET_FIELDS);
		setAPIURL();
		JSONObjectWrapper colFormats = new JSONObjectWrapper();
        List fieldsList = (List) sendRequest();
        for (int i = 0; i < fieldsList.size(); i++) {
            Object fieldMeta = fieldsList.get(i);
            Map formatInfo = (Map) ((List) fieldMeta).get(1);
            if (!formatInfo.isEmpty()) {
                colFormats.put(String.valueOf(i), formatInfo);
            }
        }
		this.bean.setColFormats(colFormats);

		this.bean.setServiceActionConstant(ServiceActionConstants.FETCH_DATA);
		URLParam = new ArrayList();
		URLParam.add(this.bean.getDatabaseName());
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);
		setAPIURL();
		return sendRequest();

	}
	

}
