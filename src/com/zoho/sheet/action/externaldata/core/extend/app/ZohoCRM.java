//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.core.extend.app;


import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.MAX_RECORD_LIMIT;

/**
 * <AUTHOR>
 *
 */
public class ZohoCRM extends CloudService{

	private boolean has_more_page;
	
	private static Logger logger = Logger.getLogger(ZohoCRM.class.getName());

	public ZohoCRM() {
		this.bean = new ServiceBean();
		this.bean.setServiceConstant(ServiceConstant.ZCRM);
		
		
		//setAPIURL();
		
		
		
	}
	public void setBean() {
//		this.bean.setFieldsName(actionJSON.getLong(JSONConstants.));
	}

	@Override
	public void setPreciseHeader(ZSConnection httpCon) {
		switch(this.bean.getServiceActionConstant()){
			case ServiceActionConstants.GET_ORG_LIST:
			case ServiceActionConstants.GET_ORG:
		case ServiceActionConstants.FETCH_DATA:
		case ServiceActionConstants.GET_MODULES:
		case ServiceActionConstants.GET_FIELDS:
			String[] paramNames = {this.bean.getAccessTokenParamName(), "X-ZOHO-SERVICE"}; //No I18N
			String[] paramValues = {this.bean.getAccessToken(), "ZohoSheet"}; //No I18N
			httpCon.setRequestHeader(paramNames, paramValues);
			break;
		}		
	}

	@Override
	public void setPreciseFormData(ZSConnection httpCon) {
		switch (this.bean.getServiceActionConstant()) {
			case ServiceActionConstants.FETCH_DATA:
				JSONObjectWrapper selectQuery = new JSONObjectWrapper(this.bean.getCriteriaString());
				selectQuery.put("select_query", selectQuery.getString("select_query")+" LIMIT "+ this.bean.getLimit()+" OFFSET "+ this.bean.getOffset());
				httpCon.setRawData(selectQuery.toString());
				break;
		}
	}
	
	public List<String> parseData(Object resp) {
		
		JSONObjectWrapper respMessage = new JSONObjectWrapper((String)resp);
		List resultArr = null;
		switch(this.bean.getServiceActionConstant()){
			case ServiceActionConstants.FETCH_DATA:
				JSONArrayWrapper dataResult = respMessage.optJSONArray("data"); //No I18N
//				JSONObject info = respMessage.optJSONObject("info");  //NO I18N
//				has_more_page = info.optBoolean("more_records");  //NO I18N
				HashMap <String, String> pageToken = new HashMap<>();
//				pageToken.put("page_token", info.optString("next_page_token"));
				this.bean.setFetchBatchMap(pageToken);
				resultArr = new ArrayList<String>();
				if(dataResult == null || dataResult.length() == 0)
				{
					return resultArr;
				}
				String[] fieldLinkNames = this.bean.getFieldLinkNames();
				JSONArrayWrapper rowData = null;
				String[] fieldRow = new String[fieldLinkNames.length];
				for (int i = 0; i < dataResult.length(); i++) {
					JSONObjectWrapper rowObj = dataResult.optJSONObject(i);
					for (int j = 0; j < fieldLinkNames.length; j++) {
						String field = fieldLinkNames[j];
						//String content = rowObj.getString(field);
						Object content = rowObj.opt(field);
						if(content instanceof JSONObjectWrapper)
						{
							Iterator<String> keys = ((JSONObjectWrapper)content).keys();
							// get some_name_i_wont_know in str_Name
							String firstKey=keys.next();
							// get the value i care about
							 content = ((JSONObjectWrapper)content).optString(firstKey);
							//content = ((JSONObject) content).optString("display_value", ((JSONObject) content).optString("url"));  //NO I18N
						}
						fieldRow[j] = (content != null && !(JSONObjectWrapper.NULL.equals(content))) ? Utility.getEncodedString(content.toString()) : "";
						//rowData.put(content);
					}
					String record = String.join(",", fieldRow);
					resultArr.add(record);
				}
				this.has_more_page = respMessage.optJSONObject("info", new JSONObjectWrapper()).optBoolean("more_records"); //NO I18N
				break;
			case ServiceActionConstants.GET_MODULES: 
				//logger.info("GET_MODULESFIELDS_LIST ");
				JSONArrayWrapper modulesArray = respMessage.getJSONArray("modules");
				resultArr = new ArrayList<String>();
				List moduleList = null;
				for(int i=0;i<modulesArray.length();i++) {
					JSONObjectWrapper modulesObj = modulesArray.getJSONObject(i);
					String linkName = Utility.getEncodedString(modulesObj.getString("api_name"));
					String name = Utility.getEncodedString(modulesObj.getString("module_name"));
					
					boolean apiSupport = modulesObj.getBoolean("api_supported");
					if(apiSupport) {
						if(!modulesObj.optBoolean("viewable"))
						{
							logger.info("[Data Connection] [ZCRM] viewable permission false for module " + linkName);
							continue;
						}
						moduleList = new ArrayList(3);
						moduleList.add(linkName);
						moduleList.add(name);
						moduleList.add(modulesObj.optBoolean("viewable"));
						resultArr.add(moduleList);
					}
					
				}
				break;
			case ServiceActionConstants.GET_FIELDS: 
				//logger.info("GET_MODULESFIELDS_LIST ");
				List fieldArr = null;
				JSONArrayWrapper fieldsArray = respMessage.getJSONArray("fields");
				resultArr = new ArrayList<List>();
				for(int i=0;i<fieldsArray.length();i++) {
					JSONObjectWrapper fieldsObj = fieldsArray.getJSONObject(i);
					String fieldName = Utility.getEncodedString(fieldsObj.getString("display_label"));
					String field_data_type = fieldsObj.getString("data_type");
					String fieldId = fieldsObj.getString("api_name");

					if("ownerlookup".equalsIgnoreCase(field_data_type))
					{
						fieldArr = new ArrayList<String>();
						int fieldType = getFieldType(this.bean.getModuleName(), field_data_type);
						fieldArr.add(fieldId+".id");
						fieldArr.add(fieldName+ " ID");
						fieldArr.add(fieldType);
						resultArr.add(fieldArr);

						fieldArr = new ArrayList<String>();
						fieldType = getFieldType(this.bean.getModuleName(), field_data_type);
						fieldArr.add(fieldId+".email");
						fieldArr.add(fieldName+ " Email");
						fieldArr.add(fieldType);
						resultArr.add(fieldArr);

						fieldArr = new ArrayList<String>();
						fieldType = getFieldType(this.bean.getModuleName(), field_data_type);
						fieldArr.add(fieldId+".first_name");
						fieldArr.add(fieldName+ " First Name");
						fieldArr.add(fieldType);
						resultArr.add(fieldArr);

						fieldArr = new ArrayList<String>();
						fieldType = getFieldType(this.bean.getModuleName(), field_data_type);
						fieldArr.add(fieldId+".last_name");
						fieldArr.add(fieldName+ " Last Name");
						fieldArr.add(fieldType);
						resultArr.add(fieldArr);
						continue;
					}
					if("lookup".equalsIgnoreCase(field_data_type) || "userlookup".equalsIgnoreCase(field_data_type))
					{
						JSONObjectWrapper lookupObj = fieldsObj.optJSONObject("lookup"); //NO I18N
						if (lookupObj != null && !lookupObj.optString("api_name", "").isEmpty()) {
							fieldArr = new ArrayList<String>();
							int fieldType = getFieldType(this.bean.getModuleName(), field_data_type);
							fieldArr.add(fieldId + ".id");
							fieldArr.add(fieldName + " ID");
							fieldArr.add(fieldType);
							resultArr.add(fieldArr);
						}

						fieldArr = new ArrayList<String>();
						fieldArr.add(fieldId);
						fieldArr.add(fieldName);
						fieldArr.add(ConstantHolder.FilterType.NONE.getTypeId());
						resultArr.add(fieldArr);
						continue;
					}
					field_data_type = Utility.getEncodedString(field_data_type);
					fieldId = Utility.getEncodedString(fieldId);
					if(fieldsObj.optInt("ui_type") != 999 && isCOQLSupportedField(this.bean.getModuleName(), fieldId, field_data_type)) {
						fieldArr = new ArrayList<String>();
						int fieldType = getFieldType(this.bean.getModuleName(), field_data_type); 
						fieldArr.add(fieldId);
						fieldArr.add(fieldName);
						fieldArr.add(fieldType);
						resultArr.add(fieldArr);
					}
				}
				break;
			case ServiceActionConstants.GET_ORG_LIST:
				JSONArrayWrapper orgListArray = respMessage.optJSONArray("organizations"); //No I18N
				resultArr = new ArrayList<String>();
				List orgList = null;
				for(int i=0; i<orgListArray.length(); i++) {
					JSONObjectWrapper orgDetailObj = orgListArray.getJSONObject(i);
					String linkName = orgDetailObj.getString("id");
					String name = Utility.getEncodedString(orgDetailObj.getString("name"));
					orgList = new ArrayList(2);
					orgList.add(linkName);
					orgList.add(name);
					resultArr.add(orgList);
				}
					break;
			case ServiceActionConstants.GET_ORG:

				JSONArrayWrapper orgDetailArray = respMessage.optJSONArray("org"); //No I18N
				resultArr = new ArrayList<String>();
				orgList = null;
				JSONObjectWrapper orgDetailObj = orgDetailArray.getJSONObject(0);
				String linkName = orgDetailObj.getString("zgid");
				String name = Utility.getEncodedString(orgDetailObj.optString("company_name", "-")); //No i18n
				orgList = new ArrayList(2);
				orgList.add(linkName);
				orgList.add(name);
				resultArr.add(orgList);
				break;
		}
		return resultArr;
	}
	private int getFieldType(String moduleName, String serviceFieldType) {
		if(!isFilterField(moduleName))
		{
			return ConstantHolder.FilterType.NONE.getTypeId();
		}
		switch (serviceFieldType) {
			case "integer":
			case "currency":
			case "percent":
			case "double":
			case "decimal":
			case "autonumber":
			case "long":
			case "bigint":
				return ConstantHolder.FilterType.NUMBER.getTypeId();
			case "datetime":
			case "date":
				return ConstantHolder.FilterType.DATE.getTypeId();
			case "time_range":
				return ConstantHolder.FilterType.TIME.getTypeId();
			case "multiselectlookup":
			case "multiuserlookup":
			case "multiselectpicklist":
			case "multimodulelookup":
			case "fileupload":
			case "imageupload":
			case "file_upolad_optionlist":
				return ConstantHolder.FilterType.NONE.getTypeId();
		}
		return ConstantHolder.FilterType.TEXT.getTypeId();
	}

	//Ref : https://www.zoho.com/crm/developer/docs/api/v5/COQL-Limitations.html
	private boolean isCOQLSupportedField(String moduleName, String fieldName, String fieldType)
	{
		if(("Participants".equalsIgnoreCase(fieldName) && "Events".equalsIgnoreCase(moduleName)) || (("Pricing_Details".equalsIgnoreCase(fieldName) || "Product_Details".equalsIgnoreCase(fieldName)) && ("Price_Books".equalsIgnoreCase(moduleName) || "Quotes".equalsIgnoreCase(moduleName))))
		{
			return false;
		}

		switch (fieldType) {
			case "multiselectlookup":
			case "multiuserlookup":
			case "multimodulelookup":
			case "fileupload":
			case "imageupload":
			case "file_upolad_optionlist":
			case "subform":
				return false;
		}

		return true;
	}

	private boolean isFilterField(String moduleName)
	{
		return !"Tags".equalsIgnoreCase(moduleName) && !"Notes".equalsIgnoreCase(moduleName) && !"Attachments".equalsIgnoreCase(moduleName); //No I18N
	}
	public ServiceInfo getServiceInfo() {
		return ConstantHolder.getServiceInfo(ServiceConstant.ZCRM);
	}
	@Override
	public void setAPIURL() {
		String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
		this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
		
		String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
		this.bean.setRequestURL(requestURL);
		this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));
		
	}
	@Override
	public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {
		
		ArrayList moduleFieldsList =  null;

		if(this.bean.getModuleName()!=null && !this.bean.getModuleName().equals("")){
			moduleFieldsList = new ArrayList();
			try {
				List fieldList = getFields();
				if (fieldList != null) {
					moduleFieldsList.add(fieldList);
				}
			}catch (Exception e)
			{
				logger.log(Level.INFO,"error occurred while fetching module {0}",new Object[]{this.bean.getModuleName()});
				Map<String, Object> moduleFields = new LinkedHashMap<>();
				moduleFields.put("has_permission_error", true);
				moduleFieldsList.add(moduleFields);
			}
		}else{
			this.bean.setServiceActionConstant(ServiceActionConstants.GET_MODULES);
			setCommonDetails();
			setAccessToken();
			setAPIURL();
			List<List> modulesList = (List)sendRequest();
			moduleFieldsList = new ArrayList();
			int i = 0;
			for(List moduleList : modulesList) {
				Map<String, Object> moduleFields = new LinkedHashMap<>();
				if((boolean) moduleList.get(2)) {
					if (i == 0) {
						this.bean.setModuleName((String) moduleList.get(0));
						try {
							List fieldList = getFields();
							moduleFields.put("field", fieldList);
						}
						catch (Exception e)
						{
							logger.log(Level.INFO,"error occurred while fetching module {0}",new Object[]{this.bean.getModuleName()});
							continue;
						}
					}
				}
				else
				{
					moduleFields.put("has_permission_error", true);
				}
				//if(fieldList!=null && fieldList.size()>0) {
				moduleFields.put("module", Arrays.asList(moduleList.get(0), moduleList.get(1)));
				moduleFieldsList.add(moduleFields);
				//}
				i++;
			}
		}



		JSONObjectWrapper resp = new JSONObjectWrapper();
		resp.put("selectDataContent", moduleFieldsList);
		return resp;
			

	}

	public List getFields() throws DataConnection.DataConnectionFailedException {
		this.setCommonDetails();
		this.setAccessToken();
		ArrayList URLParam = new ArrayList();
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);

		this.bean.setServiceActionConstant(ServiceActionConstants.GET_FIELDS);
		setAPIURL();
		//setCommonDetails();
		return (List)sendRequest();
	}

	@Override
	public void setPreRequestProcess() {
		// TODO Auto-generated method stub
		List URLParam = this.bean.getDynamicURLParam();
		if(URLParam == null) {
			URLParam = new ArrayList();
			URLParam.add(this.bean.getModuleName());
			this.bean.setDynamicURLParam(URLParam);

		}else{
			URLParam.add(this.bean.getModuleName());
		}
		switch(this.bean.getServiceActionConstant()) {
			case ServiceActionConstants.FETCH_DATA:
				StringBuilder criteriaBuilder = new StringBuilder();
				if(this.bean.getCriteriaList().length()>0) {
					criteriaBuilder.append("where ");
					criteriaBuilder.append("(".repeat(this.bean.getCriteriaList().length()));
					for(int i=0;i<this.bean.getCriteriaList().length();i++){
						JSONArrayWrapper criteria = this.bean.getCriteriaList().getJSONArray(i);
						int logicOptr = criteria.getInt(0);
						String fieldLinkName = Utility.getDecodedString(criteria.getString(1));
						int fieldTypeId = criteria.getInt(2);
						int operatorID = criteria.getInt(3);
						try {
							if(logicOptr == 0)
							{
								criteriaBuilder.append(" and ");
							} else if (logicOptr == 1) {
								criteriaBuilder.append(" or ");
							}
							ConstantHolder.FilterOperator filterOperator = ConstantHolder.FilterOperator.getFilterOperator(operatorID);
							criteriaBuilder.append(fieldLinkName).append(" ").append(((ServiceInfoImpl)getServiceInfo()).getFilterOperatorString(filterOperator));
							if(filterOperator == ConstantHolder.FilterOperator.IS_EMPTY || filterOperator == ConstantHolder.FilterOperator.IS_NOT_EMPTY)
							{
								criteriaBuilder.append(")");
								continue;
							}
							String value1 = Utility.getDecodedString(criteria.getString(4));
							if(!"null".equals(value1))
							{
								if(fieldTypeId == ConstantHolder.FilterType.TEXT.getTypeId())
								{
									if(filterOperator == ConstantHolder.FilterOperator.BEGINNING_WITH)
									{
										value1 = value1+"%";
									}
									else if(filterOperator == ConstantHolder.FilterOperator.CONTAINS || filterOperator == ConstantHolder.FilterOperator.NOT_CONTAINS)
									{
										value1 = "%"+value1+"%";
									}
									value1 = "\""+value1+"\"";
								}
								else if(fieldTypeId == ConstantHolder.FilterType.DATE.getTypeId())
								{
									value1 = ExternalDataUtils.getFormatedDate(Long.parseLong(value1),  "yyyy-MM-dd'T'HH:mm:ss'+00:00'"); //No I18N
									value1 = "\""+value1+"\"";
								}
								criteriaBuilder.append(" ").append(value1).append(")");
							}
						}
						catch (Exception e)
						{
							logger.log(Level.INFO, "Exception Occurred in Criteria", e);
						}

					}
				}
				String criteriaStr = criteriaBuilder.toString();
				JSONObjectWrapper queryObj = new JSONObjectWrapper();
				String query = "select " + String.join(", ", this.bean.getFieldLinkNames()) + //No I18N
						" from " + this.bean.getModuleName() + //No I18N
						(criteriaStr.isEmpty() ? " Where id is not null" : " "+criteriaStr); //No I18N
				queryObj.put("select_query", query);
				this.bean.setCriteriaString(queryObj.toString());
				break;


//		StringBuilder queryStr = new StringBuilder();
//		switch(this.bean.getServiceActionConstant()) {
//			case ServiceActionConstants.FETCH_DATA:
//				if(this.bean.getCriteriaList().length()>0) {
//					for(int i=0;i<this.bean.getCriteriaList().length();i++){
//						JSONArray criteria = this.bean.getCriteriaList().getJSONArray(i);
//						int logicOptr = criteria.getInt(0);
//						String fieldLinkName = criteria.getString(1);
//						int fieldTypeId = criteria.getInt(2);
//						String operatorID = criteria.getString(3);
//						String value1 = criteria.getString(4);
////						String value2 = criteria.getString(5);
//
//						try {
//							if(logicOptr == 0)
//							{
//								queryStr.append(" and ");
//							} else if (logicOptr == 1) {
//								queryStr.append(" or ");
//							}
//							queryStr.append("(");
//							queryStr.append(fieldLinkName).append(":").append(((ServiceInfoImpl)getServiceInfo()).getFilterOperatorString(ConstantHolder.FilterOperator.getFilterOperator(operatorID))).append(":");
//							if(!"null".equals(value1))
//							{
//								if(fieldTypeId == ConstantHolder.FieldType.TEXT.getTypeId())
//								{
//									value1 = "\""+value1+"\"";
//								}
//								else if(fieldTypeId == ConstantHolder.FieldType.DATE.getTypeId())
//								{
//									value1 = ExternalDataUtils.getFormatedDate(value1, "dd/MM/yyyy",  "yyyy-MM-dd'T'HH:mm:ss'+0000'"); //No I18N
//									value1 = "\""+value1+"\"";
//								}
//								queryStr.append(value1).append(")");
//							}
//						}
//						catch (Exception e)
//						{
//							logger.log(Level.INFO, "Exception Occurred in Criteria", e);
//						}
//
//					}
//					if(!queryStr.toString().isEmpty()){
//						this.bean.setCriteriaString(Utility.getEncodedString(queryStr.toString()));
//					}
//				}
//			break;
		}
	}

//	public String setPreciseQueryParam(String  url, int pageNo) {
//		switch (this.bean.getServiceActionConstant()) {
//			case ServiceActionConstants.FETCH_DATA:
//				url = url + (this.bean.getCriteriaList().length()>0 ? "/search" : "") + "?fields=" + String.join(",", this.bean.getFieldLinkNames());
//				String page_token;
//				if(this.bean.getFetchBatchMap() != null && (page_token = (String) this.bean.getFetchBatchMap().get("page_token")) != null)
//				{
//					url = url + "&page_token=" + page_token;
//				}else
//				{
//					url = url + "&page=" + pageNo;
//				}
//				if(this.bean.getCriteriaList().length()>0) {
//					url = url + "&criteria="+ this.bean.getCriteriaString();
//				}
//				break;
//			default:
//				break;
//
//		}
//		return url;
//	}

	@Override
	public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
		this.setPreRequestProcess();
		setAccessToken();

		setAPIURL();
		String urlStr = this.bean.getRequestURL();
		int page_no = 1;
		List resultArr = new ArrayList();

		String [] fieldHeaders = bean.getFieldHeaders();

		resultArr.add(String.join(",", fieldHeaders));
		this.bean.setLimit(200);
		has_more_page = true;
		while(has_more_page && page_no * 200 <= MAX_RECORD_LIMIT){
			this.bean.setOffset(((page_no - 1) * 200));
//			String reqURL = setPreciseQueryParam(urlStr, page_no);
//			this.bean.setRequestURL(reqURL);

			List THR_Content = (List<String>)sendRequest();
			if(THR_Content !=null && !THR_Content.isEmpty()){
				resultArr.addAll(THR_Content);
			}
			else
			{
				has_more_page = false;
			}
			page_no++;
		}

		this.bean.setMaxRowReached(has_more_page);
		this.bean.setRowCount(resultArr.size());
		this.bean.setColCount(fieldHeaders.length);
		return resultArr;
	}

	@Override
	public String parseCode(String resp) {
		return new JSONObjectWrapper(resp).optString("code");//NO I18N
	}

	public List<String> getOrg() throws Exception {
		this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
		if(this.bean.getConnectionID() <= 0){
			setCommonDetails();
		}

		setAccessToken();
		setAPIURL();
		return (List)sendRequest();
	}
	public List<String> getOrgList() throws Exception {
		this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG_LIST);
		this.setCommonDetails();
		setAccessToken();
		setAPIURL();
		return (List)sendRequest();
	}
}
