//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.core.extend.app;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.JSONWrapperException;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import org.json.JSONException;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;
import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.FIELD_TYPE;

/**
 * <AUTHOR>
 *
 */
public class ZohoProjects extends CloudService{


	private static Logger logger = Logger.getLogger(ZohoProjects.class.getName());

	public ZohoProjects() {
		this.bean = new ServiceBean();
		this.bean.setServiceConstant(ServiceConstant.ZPROJECTS);
	}
	

	@Override
	public ServiceInfo getServiceInfo() {
		return ConstantHolder.getServiceInfo(ServiceConstant.ZPROJECTS);
	}

	@Override
	public void setAPIURL() {
		String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
		this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
		//this.apiurl = setPreciseQueryParam(this.apiurl);
		String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
		this.bean.setRequestURL(requestURL);
		this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));
	}

	@Override
	public void setPreciseHeader(ZSConnection httpCon) {
		switch(this.bean.getServiceActionConstant()){
			case ServiceActionConstants.GET_ORG:
			case ServiceActionConstants.GET_OWNDBLIST:
			case ServiceActionConstants.FETCH_DATA:
				String[] paramNames = {this.bean.getAccessTokenParamName(),"X-ZOHO-SERVICE"};//NO I18N
				String[] paramValues = {this.bean.getAccessToken(),"ZohoSheet"};//NO I18N
				httpCon.setRequestHeader(paramNames, paramValues);
				break;
		}
	}
	public String setPreciseQueryParam(String  url, int from, int limit) {
		switch(this.bean.getServiceActionConstant()){

			case ServiceActionConstants.FETCH_DATA:
				url = url + "?index=" + from + "&range=" + limit+"&sort_column=created_time&sort_order=descending";
				if(this.bean.getCriteriaList().length()>0) {
					url = url + this.bean.getCriteriaString();
				}
				break;

			default :
				break;

		}
		return url;

	}


	@Override
	public void setPreciseFormData(ZSConnection httpCon) {

	}
	@Override
	public String parseCode(String resp) {
		try {
			JSONObjectWrapper errorJson = new JSONObjectWrapper(resp).optJSONObject("error"); //NO I18N
			return errorJson != null ?errorJson.optString("code") : "";//NO I18N
		}catch (JSONWrapperException | JSONException ignored)
		{
			return "";
		}
	}
	@Override
	public Object parseData(Object resp) {
		List resultArr = null;
		switch(this.bean.getServiceActionConstant())
		{
			case ServiceActionConstants.GET_ORG:
				JSONArrayWrapper portalArray = new JSONArrayWrapper((String)resp);
				resultArr = new ArrayList();
				if(portalArray.isEmpty()){
					return resultArr;
				}

				ArrayList portalList = null;
				for (int i = 0; i < portalArray.length(); i++) {
					portalList = new ArrayList<String>();
					JSONObjectWrapper rowObj = portalArray.optJSONObject(i);
					String id = rowObj.optString("zsoid");//NO I18N
					String name = Utility.getEncodedString(rowObj.optString("portal_name"));//NO I18N
					portalList.add(id);
					portalList.add(name);
					resultArr.add(portalList);
				}
				break;
			case ServiceActionConstants.GET_OWNDBLIST: //GetProject list

				JSONObjectWrapper respMessage = new JSONObjectWrapper((String)resp);
				resultArr = new ArrayList();
				ArrayList projectList = null;
				JSONArrayWrapper projectArray = respMessage.getJSONArray("projects");

				if(projectArray.isEmpty()){
					return resultArr;
				}

				for (int i = 0; i < projectArray.length(); i++) {
					projectList = new ArrayList<String>();
					JSONObjectWrapper rowObj = projectArray.optJSONObject(i);
					String id = rowObj.optString("id");//NO I18N
					String name = Utility.getEncodedString(rowObj.optString("name"));//NO I18N
					projectList.add(id);
					projectList.add(name);
					resultArr.add(projectList);
				}
				break;

		case ServiceActionConstants.FETCH_DATA:
			respMessage = new JSONObjectWrapper((String)resp);
			resultArr = new ArrayList();
			JSONArrayWrapper dataArray = respMessage.optJSONArray(this.bean.getModuleName());

			if(dataArray == null || dataArray.isEmpty())
			{
				return resultArr;
			}

			String [] fieldLinkNames = bean.getFieldLinkNames();
			resultArr = new ArrayList<String>();

//			resultArr.add(String.join(",", bean.getFieldHeaders()));
//			String citiesCommaSeparated = Arrays.stream(this.bean.getFieldHeaders()).collect(Collectors.joining(","));
//			resultArr.add(citiesCommaSeparated);
			for (int i = 0; i < dataArray.length(); i++) {
				JSONObjectWrapper rowObj = dataArray.optJSONObject(i);
				String[] fieldRow = new String[fieldLinkNames.length];

				int j = 0;
				for (String field : fieldLinkNames) {
					Object content = rowObj.opt(field);
					if(content instanceof JSONObjectWrapper)
					{
						Iterator<String> keys = ((JSONObjectWrapper)content).keys();
						String firstKey=keys.next();
						content = ((JSONObjectWrapper)content).optString(firstKey);
					}
					fieldRow[j++] = content != null ? Utility.getEncodedString(content.toString()) : "";
				}
				resultArr.add(String.join(",", fieldRow));
			}
			break;
		
		
	}
		return resultArr;
	}

	@Override
	public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {

		this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
		setCommonDetails();
		setAccessToken();
		setAPIURL();
		List<List> orgList = (List) sendRequest();
		List<List> projectList = new ArrayList<>();

		if (orgList == null) {
			orgList = Collections.emptyList();
		}
		for (List orgInfo : orgList) {
			this.bean.setServiceActionConstant(ServiceActionConstants.GET_OWNDBLIST);
			ArrayList URLParam = new ArrayList();
			URLParam.add(orgInfo.get(0));
			this.bean.setDynamicURLParam(URLParam);
			setAPIURL();
			List projects = (List) sendRequest();
			if(projects == null)
			{
				projects = Collections.emptyList();
			}
			projectList.add(projects);
		}


		JSONObjectWrapper resp = new JSONObjectWrapper();
		resp.put("selectDataContent", ((ServiceInfoImpl)getServiceInfo()).getModulesFields());
		resp.put("DBList", projectList);
		resp.put("orgList", orgList);
		return resp;
	}

	@Override
	public List getFields() {
		String moduleName = this.bean.getModuleName();
		for(Object moduleField : ((ServiceInfoImpl)getServiceInfo()).getModulesFields()) {
			if (((List) ((Map) moduleField).get(MODULES)).get(0).equals(moduleName)) {
				return (List)((Map) moduleField).get(FIELDS);
			}
		}
		return Collections.emptyList();
	}


	@Override
	public void setPreRequestProcess() {
		ArrayList URLParam = new ArrayList();
		URLParam.add(this.bean.getService_OrgID());
		URLParam.add(this.bean.getDatabaseName());
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);

		StringBuilder queryStr = new StringBuilder();
		switch(this.bean.getServiceActionConstant()) {


			case ServiceActionConstants.FETCH_DATA:
				if(this.bean.getCriteriaList().length()>0) {
					//url = url + "&criteria="+ Utility.getEncodedString(this.bean.getCriteriaString());

					for(int i=0;i<this.bean.getCriteriaList().length();i++){
						JSONArrayWrapper criteria = this.bean.getCriteriaList().getJSONArray(i);
						int logicOptr = criteria.getInt(0);
						String fieldLinkName = Utility.getDecodedString(criteria.getString(1));
						int fieldTypeId = criteria.getInt(2);
						String operatorID = criteria.getString(3);
						String value1 = criteria.getString(4);
//						String value2 = criteria.getString(5);

						try {
							queryStr.append("&").append(fieldLinkName).append("=").append(value1);
						}
						catch (Exception e)
						{
							logger.log(Level.INFO, "Exception Occurred in Criteria", e);
						}

					}
					if(!queryStr.toString().isEmpty()){
						this.bean.setCriteriaString(queryStr.toString());
					}
				}
			break;
		}
		
	}

	@Override
	public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
		this.setPreRequestProcess();
		setAccessToken();

		setAPIURL();
		String urlStr = this.bean.getRequestURL();
		boolean existContent = true;
		int from = 1;
		List resultArr = new ArrayList();

		String [] fieldHeaders = bean.getFieldHeaders();

		resultArr.add(String.join(",", fieldHeaders));

		while(existContent){
			String reqURL = setPreciseQueryParam(urlStr, from, 200);
			this.bean.setRequestURL(reqURL);

			List THR_Content = (List<String>)sendRequest();
			if(THR_Content !=null && !THR_Content.isEmpty()){
				if(from > MAX_RECORD_LIMIT)
				{
					this.bean.setMaxRowReached(existContent);
					break;
				}
				resultArr.addAll(THR_Content);
			}else{
				existContent = false;
			}
			from = from + 200;
		}
		this.bean.setRowCount(resultArr.size());
		this.bean.setColCount(fieldHeaders.length);
		return resultArr;
	}



}
