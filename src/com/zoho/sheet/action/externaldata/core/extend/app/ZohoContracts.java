package com.zoho.sheet.action.externaldata.core.extend.app;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;

import java.util.*;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;

public class ZohoContracts  extends CloudService {

    public ZohoContracts() {
        this.bean = new ServiceBean();
        this.bean.setServiceConstant(ServiceConstant.ZCONTRACTS);
    }

    @Override
    public ServiceInfo getServiceInfo() {
        return ConstantHolder.getServiceInfo(ServiceConstant.ZCONTRACTS);
    }

	@Override
	public void setAPIURL() {
		String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
		this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
		String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
		this.bean.setRequestURL(requestURL);
		this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));
	}

	@Override
	public void setPreciseHeader(ZSConnection httpCon) {
		switch(this.bean.getServiceActionConstant()){
			case ServiceActionConstants.GET_ORG:
            case ServiceActionConstants.FETCH_DATA:
                httpCon.setRequestHeader(new String[]{this.bean.getAccessTokenParamName()}, new String[]{this.bean.getAccessToken()});
				break;
		}
	}

    @Override
    public void setPreciseFormData(ZSConnection httpCon) {

    }

    @Override
    public Object parseData(Object resp) {
		JSONObjectWrapper respMessage = new JSONObjectWrapper((String)resp);
		switch (this.bean.getServiceActionConstant()){
			case ServiceActionConstants.GET_ORG:
				List resultArr = new ArrayList();
				JSONArrayWrapper orgs = respMessage.getJSONArray("organizationinfo");
				Iterator orgsItr = orgs.iterator();

				while (orgsItr.hasNext())
				{
					JSONObjectWrapper orgObj = (JSONObjectWrapper) orgsItr.next();
					resultArr.add(Arrays.asList(orgObj.getString("zoid"), orgObj.getString("name")));
				}
				return resultArr;
			case ServiceActionConstants.FETCH_DATA:
				JSONArrayWrapper dataResult = respMessage.optJSONArray(this.bean.getModuleName());
				String[] fieldLinkNames = this.bean.getFieldLinkNames();
				resultArr = new ArrayList<String>();
				if(dataResult == null || dataResult.isEmpty())
				{
					return resultArr;
				}
				String[] fieldRow = new String[fieldLinkNames.length];
				for (int i = 0; i < dataResult.length(); i++) {
					JSONObjectWrapper rowObj = dataResult.optJSONObject(i);
					for (int j = 0; j < fieldLinkNames.length; j++) {
						String field = fieldLinkNames[j];
						String content = rowObj.optString(field);
						fieldRow[j] = Utility.getEncodedString(content);
					}
					String recordStr = String.join(",", fieldRow);
					resultArr.add(recordStr);
				}
				return resultArr;
		}

		return null;
    }

    @Override
    public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {
		JSONObjectWrapper resp = new JSONObjectWrapper();
		this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
		setCommonDetails();
		setAccessToken();
		setAPIURL();
		List orgList = (List)sendRequest();
		resp.put("orgList", orgList);
		resp.put("selectDataContent", ((ServiceInfoImpl)getServiceInfo()).getModulesFields());
		return resp;
    }

	@Override
	public List getFields() throws DataConnection.DataConnectionFailedException {
		String moduleName = this.bean.getModuleName();
		for(Object moduleField : ((ServiceInfoImpl)getServiceInfo()).getModulesFields()) {
			if (((List) ((Map) moduleField).get(MODULES)).get(0).equals(moduleName)) {
				return (List)((Map) moduleField).get(FIELDS);
			}
		}
		return Collections.emptyList();
	}


	@Override
	public void setPreRequestProcess() {
		List URLParam = new ArrayList();
		URLParam.add(this.bean.getModuleName());
		this.bean.setDynamicURLParam(URLParam);
	}

//	@Override
//	public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
//        setAccessToken();
//        setAPIURL();
//        setPreRequestProcess();
//        String urlStr = this.bean.getRequestURL();
//        int index = 1;
//        List resultArr = new ArrayList();
//        String [] fieldHeaders = bean.getFieldHeaders();
//        resultArr.add(String.join(",", fieldHeaders));
//        this.bean.setLimit(200);
//        boolean has_more_data = true;
//        while(has_more_data && index  <= MAX_RECORD_LIMIT){
//            this.bean.setRequestURL(urlStr +"?range="+index+"&offset="+200); //NO I18N
//            List THR_Content = (List<String>)sendRequest();
//            if(THR_Content !=null && !THR_Content.isEmpty()){
//                resultArr.addAll(THR_Content);
//            }
//            else
//            {
//                has_more_data = false;
//            }
//            index += 200;
//        }
//        this.bean.setRowCount(resultArr.size());
//        this.bean.setColCount(fieldHeaders.length);
//        return resultArr;
//    }

	@Override
	public String parseCode(String resp) {
		return new JSONObjectWrapper(resp).optString("code");//NO I18N
	}

}
