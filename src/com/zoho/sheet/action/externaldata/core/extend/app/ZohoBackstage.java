package com.zoho.sheet.action.externaldata.core.extend.app;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;

import java.util.*;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;

public class ZohoBackstage extends CloudService {

    public ZohoBackstage() {
        this.bean = new ServiceBean();
        this.bean.setServiceConstant(ServiceConstant.ZBACKSTAGE);
    }

    private boolean has_more_page;
    @Override
    public ServiceInfo getServiceInfo() {
        return ConstantHolder.getServiceInfo(ServiceConstant.ZBACKSTAGE);
    }

    @Override
    public void setAPIURL() {
        String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
        this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
        String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
        this.bean.setRequestURL(requestURL);
        this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));
    }

    @Override
    public void setPreciseHeader(ZSConnection httpCon) {
        switch(this.bean.getServiceActionConstant()){
            case ServiceActionConstants.FETCH_DATA:
            case ServiceActionConstants.GET_OWNDBLIST:
            case ServiceActionConstants.GET_ORG:
                httpCon.setRequestHeader(new String[]{this.bean.getAccessTokenParamName()}, new String[]{this.bean.getAccessToken()});
                break;
        }
    }

    public String setPreciseQueryParam(String  url, int pageNo, int per_page) {
        switch (this.bean.getServiceActionConstant()) {
            case ServiceActionConstants.FETCH_DATA:
                url = url + "?page=" + pageNo + "&per_page=" + per_page;
                break;
        }
        return url;
    }

    @Override
    public void setPreciseFormData(ZSConnection httpCon) {

    }

    @Override
    public Object parseData(Object resp) {
        JSONObjectWrapper respMessage = new JSONObjectWrapper((String)resp);
        List resultArr = null;

        switch (this.bean.getServiceActionConstant()){
            case ServiceActionConstants.FETCH_DATA :
                JSONArrayWrapper dataResult = respMessage.optJSONArray(this.bean.getModuleName());
                if (dataResult == null || dataResult.isEmpty()) {
                    return new ArrayList<>();
                }

                resultArr = new ArrayList<>();
                String[] fieldLinkNames = this.bean.getFieldLinkNames();
                String[] fieldRow = new String[fieldLinkNames.length];

                for (int i = 0; i < dataResult.length(); i++) {
                    JSONObjectWrapper rowObj = dataResult.optJSONObject(i);

                    for (int j = 0; j < fieldLinkNames.length; j++) {
                        String field = fieldLinkNames[j];
                        Object content = rowObj.opt(field);

                        if (content instanceof JSONObjectWrapper) {
                            if (((JSONObjectWrapper)content).has("email")) {
                                content = ((JSONObjectWrapper)content).optString("email"); // No I18N
                            } else if (((JSONObjectWrapper)content).has("purchaser_email")) {
                                content = ((JSONObjectWrapper)content).optString("purchaser_email"); // No I18N
                            } else {
                                Iterator<String> keys = ((JSONObjectWrapper)content).keys();
                                content = keys.hasNext() ? ((JSONObjectWrapper)content).optString(keys.next()) : "";
                            }
                        }

                        fieldRow[j] = (JSONObjectWrapper.NULL.equals(content)) ? "" : Utility.getEncodedString(content.toString());
                    }

                    resultArr.add(String.join(",", fieldRow));
                }
                JSONObjectWrapper pageObj = respMessage.optJSONObject("pagination"); // No I18N
                if (pageObj != null) {
                    this.has_more_page = pageObj.optBoolean("has_more_items", false);  // No I18N
                }
                break;
            case ServiceActionConstants.GET_OWNDBLIST:
                resultArr = new ArrayList();
                JSONArrayWrapper eventsArray = respMessage.getJSONArray("events");
                if(eventsArray.isEmpty()){
                    return resultArr;
                }
                for (int i = 0; i < eventsArray.length(); i++) {
                    List eventMeta = new ArrayList<String>();
                    JSONObjectWrapper rowObj = eventsArray.optJSONObject(i);
                    String id = rowObj.optString("id");//NO I18N
                    String name = Utility.getEncodedString(rowObj.optString("name"));//NO I18N
                    eventMeta.add(id);
                    eventMeta.add(name);
                    resultArr.add(eventMeta);
                }
                break;
            case ServiceActionConstants.GET_ORG:
                resultArr = new ArrayList();
                JSONArrayWrapper orgArray = respMessage.getJSONArray("portals");
                if(orgArray.isEmpty()){
                    return resultArr;
                }
                for (int i = 0; i < orgArray.length(); i++) {
                    List orgMeta = new ArrayList<String>();
                    JSONObjectWrapper rowObj = orgArray.optJSONObject(i);
                    String id = rowObj.optString("id");//NO I18N
                    String name = Utility.getEncodedString(rowObj.optString("name"));//NO I18N
                    orgMeta.add(id);
                    orgMeta.add(name);
                    resultArr.add(orgMeta);
                }
                break;
        }

        return resultArr;
    }

    @Override
    public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {
        this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
        setCommonDetails();
        setAccessToken();
        setAPIURL();
        List<List> orgList = (List) sendRequest();
        List<List> eventList = new ArrayList<>();

        if (orgList == null || orgList.isEmpty()) {
            orgList = Collections.emptyList();
        }
        for (List orgInfo : orgList) {
            this.bean.setServiceActionConstant(ServiceActionConstants.GET_OWNDBLIST);
            ArrayList URLParam = new ArrayList();
            URLParam.add(orgInfo.get(0));
            this.bean.setDynamicURLParam(URLParam);
            setAPIURL();
            List events = (List) sendRequest();
            if(events == null)
            {
                events = Collections.emptyList();
            }
            eventList.add(events);
        }

        JSONObjectWrapper resp = new JSONObjectWrapper();
        resp.put("selectDataContent", ((ServiceInfoImpl)getServiceInfo()).getModulesFields());
        resp.put("DBList", eventList);
        resp.put("orgList", orgList);
        return resp;
    }

    @Override
    public List getFields() throws DataConnection.DataConnectionFailedException {
        String moduleName = this.bean.getModuleName();
        for(Object moduleField : ((ServiceInfoImpl)getServiceInfo()).getModulesFields()) {
            if (((List) ((Map) moduleField).get(MODULES)).get(0).equals(moduleName)) {
                return (List)((Map) moduleField).get(FIELDS);
            }
        }
        return Collections.emptyList();
    }

    @Override
    public void setPreRequestProcess() throws DataConnection.DataConnectionFailedException {

        switch (this.bean.getServiceActionConstant())
        {
            case ServiceActionConstants.GET_MODULES:
                this.bean.setDynamicURLParam(Arrays.asList(this.bean.getService_OrgID(), this.bean.getDatabaseName()));
                break;
            case ServiceActionConstants.GET_FIELDS:
            case ServiceActionConstants.FETCH_DATA:
                this.bean.setDynamicURLParam(Arrays.asList(this.bean.getService_OrgID(), this.bean.getDatabaseName(), this.bean.getModuleName()));
                break;
        }

    }

    @Override
    public String parseCode(String resp) {
        return new JSONObjectWrapper(resp).optString("status_code");//NO I18N
    }

    @Override
    public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
        this.setPreRequestProcess();
        setAccessToken();

        setAPIURL();
        String urlStr = this.bean.getRequestURL();
        List<String> resultArr = new LinkedList<>();

        String[] fieldHeaders = bean.getFieldHeaders();
        resultArr.add( String.join(",", fieldHeaders));

        boolean hasPagination = ("attendees".equalsIgnoreCase(this.bean.getModuleName()) || "orders".equalsIgnoreCase(this.bean.getModuleName()) || "ticket_classes".equalsIgnoreCase(this.bean.getModuleName())); //No I18N

        if(!hasPagination) {
            List content = (List) sendRequest();
            if (content != null && !content.isEmpty()) {
                resultArr.addAll(content);
            }
        }
        else {
            this.has_more_page = true;
            int per_page = 200;
            int page_no = 1;
            while (has_more_page && page_no * per_page <= MAX_RECORD_LIMIT) {
                String reqURL = setPreciseQueryParam(urlStr, page_no, per_page);
                this.bean.setRequestURL(reqURL);
                List content = (List) sendRequest();
                if (content != null && !content.isEmpty()) {
                    resultArr.addAll(content);
                } else {
                    this.has_more_page = false;
                }
                page_no++;
            }
            this.bean.setMaxRowReached(has_more_page);
        }
        this.bean.setRowCount(resultArr.size());
        this.bean.setColCount(fieldHeaders.length);
        return resultArr;
    }
}
