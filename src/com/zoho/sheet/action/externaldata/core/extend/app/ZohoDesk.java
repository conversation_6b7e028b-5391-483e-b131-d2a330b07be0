//$Id$
package com.zoho.sheet.action.externaldata.core.extend.app;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;

import java.util.*;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.*;

/**
 * Created by krishnan-zt276 on  22/07/21
 */
public class ZohoDesk extends CloudService {

    public ZohoDesk() {
        this.bean = new ServiceBean();
        this.bean.setServiceConstant(ServiceConstant.ZDESK);
    }

    @Override
    public ServiceInfo getServiceInfo() {
        return ConstantHolder.getServiceInfo(ServiceConstant.ZDESK);
    }

    @Override
    public void setAPIURL() {
        String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
        this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
        String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
        this.bean.setRequestURL(requestURL);
        this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));
    }



    @Override
    public void setPreciseHeader(ZSConnection httpCon) {
        String[] paramNames = new String[2];
        String[] paramValues = new String[2];
        switch (this.bean.getServiceActionConstant())
        {
            case ServiceActionConstants.FETCH_DATA:
                paramNames[1] = "orgId";  //No I18N
                paramValues[1] = this.bean.getService_OrgID();
            case ServiceActionConstants.GET_ORG:
                paramNames[0] = AUTHORIZATION;
                paramValues[0] = this.bean.getAccessToken();
                break;
        }
        httpCon.setRequestHeader(paramNames, paramValues);
    }

    @Override
    public void setPreciseFormData(ZSConnection httpCon) {

    }

    public String setPreciseQueryParam(String  url, int from, int limit)
    {
        switch(this.bean.getServiceActionConstant()){

            case ServiceActionConstants.FETCH_DATA:
                url = url + "?from=" + from + "&limit=" + limit;
                if(this.bean.getCriteriaString() != null) {
                    url = url +this.bean.getCriteriaString();
                }
                break;

            default :
                break;

        }
        return url;
    }
    @Override
    public String parseCode(String resp) {
        return new JSONObjectWrapper(resp).optString("errorCode");//NO I18N
    }
    @Override
    public Object parseData(Object resp) {
        JSONObjectWrapper respMessage = new JSONObjectWrapper((String)resp);
        List resultArr = null;

        switch (this.bean.getServiceActionConstant()){
            case ServiceActionConstants.FETCH_DATA :
                JSONArrayWrapper dataResult = "teams".equals(this.bean.getModuleName()) ? respMessage.optJSONArray("teams") : respMessage.optJSONArray("data"); //No I18N
                String[] fieldLinkNames = this.bean.getFieldLinkNames();
                resultArr = new ArrayList<String>();
                if(dataResult == null || dataResult.length() == 0)
                {
                    return resultArr;
                }
//                String recordStr = String.join(",", this.bean.getFieldHeaders());
//                resultArr.add(recordStr);
                String[] fieldRow = new String[fieldLinkNames.length];
                for (int i = 0; i < dataResult.length(); i++) {
                    JSONObjectWrapper rowObj = dataResult.optJSONObject(i);
                    for (int j = 0; j < fieldLinkNames.length; j++) {
                        String field = fieldLinkNames[j];
                        Object content = rowObj.opt(field);
                        if(content instanceof JSONObjectWrapper)
                        {
                            Iterator<String> keys = ((JSONObjectWrapper)content).keys();
                            content = ((JSONObjectWrapper)content).optString(keys.next());
                        }
                        if(JSONObjectWrapper.NULL.equals(content))
                        {
                            fieldRow[j] = "";
                        }else{
                            String strContent = Utility.getEncodedString(String.valueOf(content));
                            fieldRow[j] = Utility.getEncodedString(content.toString());

                        }
                    }
                    String recordStr = String.join(",", fieldRow);
                    resultArr.add(recordStr);
                }
                break;
            case ServiceActionConstants.GET_ORG:
                resultArr = new ArrayList();
                JSONArrayWrapper orgArray = respMessage.getJSONArray("data");

                if(orgArray.length() == 0){
                    return resultArr;
                }

                for (int i = 0; i < orgArray.length(); i++) {
                    ArrayList orgList = new ArrayList<String>();
                    JSONObjectWrapper rowObj = orgArray.optJSONObject(i);
                    String id = rowObj.optString("id");//NO I18N
                    String name = Utility.getEncodedString(rowObj.optString("companyName"));//NO I18N
                    orgList.add(id);
                    orgList.add(name);
                    resultArr.add(orgList);
                }
                break;
        }

        return resultArr;
    }

    @Override
    public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {
        JSONObjectWrapper resp = new JSONObjectWrapper();
        this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
        setCommonDetails();
        setAccessToken();
        setAPIURL();
        List orgList = (List)sendRequest();
        resp.put("orgList", orgList);
        resp.put("selectDataContent", ((ServiceInfoImpl)getServiceInfo()).getModulesFields());
        return resp;
    }

    @Override
    public List getFields() {
        String moduleName = this.bean.getModuleName();
        for(Object moduleField : ((ServiceInfoImpl)getServiceInfo()).getModulesFields()) {
            if (((List) ((Map) moduleField).get(MODULES)).get(0).equals(moduleName)) {
                return (List)((Map) moduleField).get(FIELDS);
            }
        }
        return Collections.emptyList();
    }

    @Override
    public void setPreRequestProcess() {
        ArrayList URLParam = new ArrayList();
        URLParam.add(this.bean.getModuleName());
        this.bean.setDynamicURLParam(URLParam);

        StringBuilder queryStr = new StringBuilder();
        switch(this.bean.getServiceActionConstant()) {
            case ServiceActionConstants.FETCH_DATA:
                if(this.bean.getCriteriaList().length()>0) {

                    for(int i=0;i<this.bean.getCriteriaList().length();i++){
                        JSONArrayWrapper criteria = this.bean.getCriteriaList().getJSONArray(i);
                        int logicOptr = criteria.getInt(0);
                        String fieldLinkName = Utility.getDecodedString(criteria.getString(1));
                        int fieldTypeId = criteria.getInt(2);
                        int operatorID = criteria.getInt(3);
                        String value1 = Utility.getDecodedString(criteria.getString(4));
//						String value2 = criteria.getString(5);

                        FilterOperator operator = ConstantHolder.FilterOperator.getFilterOperator(operatorID);
                        if(fieldTypeId == FilterType.TEXT.getTypeId())
                        {
                            if(operator == FilterOperator.BEGINNING_WITH)
                            {
                                value1 = value1+"*";
                            } else if (operator == FilterOperator.CONTAINS) {
                                value1 = "*"+value1+"*";
                            }
                            if("agents".equals(this.bean.getModuleName()))
                            {
                                queryStr.append("&").append("fieldName").append("=").append(fieldLinkName);
                            }
                            queryStr.append("&").append("searchStr");
                        }
                        else
                        {
                            queryStr.append("&").append(fieldLinkName);
                        }
                        queryStr.append("=").append(Utility.getEncodedString(value1));
                    }
                    if(!queryStr.toString().isEmpty()){
                        this.bean.setCriteriaString(queryStr.toString());
                    }
                }
                break;
        }
    }

    @Override
    public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
        this.setPreRequestProcess();
        setAccessToken();

        setAPIURL();
        String urlStr = this.bean.getRequestURL();
        boolean existContent = true;
        int from = 1;
        List resultArr = new ArrayList();

        String [] fieldHeaders = bean.getFieldHeaders();

        resultArr.add(String.join(",", fieldHeaders));

        Integer limit = ((ServiceInfoImpl) getServiceInfo()).fetchLimit.get(this.bean.getModuleName());

        if(limit == null)
        {
            if(this.bean.getCriteriaString() != null) {
                urlStr = urlStr +this.bean.getCriteriaString().replaceFirst("&", "?");
                this.bean.setRequestURL(urlStr);
            }
            List THR_Content = (List<String>)sendRequest();
            resultArr.addAll(THR_Content);
        }

        while(limit != null && existContent){
            String reqURL = setPreciseQueryParam(urlStr, from, limit);
            this.bean.setRequestURL(reqURL);

            List THR_Content = (List<String>)sendRequest();
            if(THR_Content !=null && !THR_Content.isEmpty()){
                if(from > MAX_RECORD_LIMIT)
                {
                    this.bean.setMaxRowReached(existContent);
                    break;
                }
                resultArr.addAll(THR_Content);
            }else{
                existContent = false;
            }
            from = from + limit;
        }
        this.bean.setRowCount(resultArr.size());
        this.bean.setColCount(fieldHeaders.length);
        return resultArr;
    }
}
