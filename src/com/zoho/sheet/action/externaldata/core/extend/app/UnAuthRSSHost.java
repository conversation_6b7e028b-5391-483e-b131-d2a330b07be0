//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.core.extend.app;


import com.adventnet.iam.security.DummyEntityResolver;
import com.adventnet.iam.security.SecurityUtil;
import com.zoho.sheet.action.externaldata.constants.ConstantHolder;
import com.zoho.sheet.action.externaldata.constants.ServiceConstant;
import com.zoho.sheet.action.externaldata.constants.ServiceInfo;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;
import com.zoho.sheet.connection.ZSheetUrlWrapperConnection;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;

import javax.net.ssl.HttpsURLConnection;
import javax.xml.parsers.DocumentBuilder;
import java.net.HttpURLConnection;
import java.text.FieldPosition;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *
 */
public class UnAuthRSSHost extends CloudService{

	private static Logger logger = Logger.getLogger(UnAuthRSSHost.class.getName());

	public UnAuthRSSHost() {
		this.bean = new ServiceBean();
		this.bean.setServiceConstant(ServiceConstant.UNAUTH_RSS);
		this.bean.setMethod("GET"); //NO I18N
	}
	@Override
	public void setPreciseHeader(ZSConnection httpCon) {}
	@Override
	public void setPreciseFormData(ZSConnection httpCon) {}
	@Override
	public List<String> parseData(Object httpCon) {
		List resultArr = new ArrayList<String>();
		StringBuffer rowData = new StringBuffer();
		List rssContent = new ArrayList<String>();
		try{
			//StringBuffer pagedata = new StringBuffer();




			DocumentBuilder docBuilder = SecurityUtil.getDocumentBuilder();
			Document doc = null;
			docBuilder.setEntityResolver(new DummyEntityResolver());


			Object URLConnectionObj = ((ZSheetUrlWrapperConnection)httpCon).getURLConnectionObj();
			if (URLConnectionObj instanceof HttpsURLConnection) {
				doc = docBuilder.parse(((HttpsURLConnection)URLConnectionObj).getInputStream());
			}else if (URLConnectionObj instanceof HttpURLConnection){
				doc = docBuilder.parse(((HttpURLConnection)URLConnectionObj).getInputStream());
			}

			org.w3c.dom.Node rootNode = doc.getDocumentElement();
			String nName = rootNode.getNodeName();

			if(nName.equals("rss"))
			{

				//float version = Float.parseFloat(rootNode.getAttributes().getNamedItem("version").getNodeValue());
				//for custom nodes...
				List customList = new ArrayList();
				NamedNodeMap nList = rootNode.getAttributes();
				for(int nl=0; nl<nList.getLength();nl++)
				{
					org.w3c.dom.Node custNode = nList.item(nl);
					String custNdName = custNode.getNodeName();
					if(custNdName.startsWith("xmlns:"))
					{
						String cnName = custNdName.substring(6,custNdName.length());
						customList.add(cnName);
					}
				}
				org.w3c.dom.Node channelNode = null;
				for(int chn=0; chn<rootNode.getChildNodes().getLength();chn++)
				{
					org.w3c.dom.Node tempNode = rootNode.getChildNodes().item(chn);
					if((tempNode.getNodeName()).equals("channel"))
					{
						channelNode = tempNode;
						break;
					}
				}
				//here


				if(channelNode != null)
				{
					boolean isAuthor = false;
					boolean isLink = false;
					boolean ispDate = false;
					boolean isDesc = false;
					String chnTitle = "";
					String chnLink = "";
					String chnDate = "";
					String cHeaderStr = "";
					//for the customnodes...
					List customNodes = new ArrayList();

					org.w3c.dom.NodeList cNodes =channelNode.getChildNodes();
					for(int i=0; i<cNodes.getLength();i++)
					{
						org.w3c.dom.Node cNode = (org.w3c.dom.Node)cNodes.item(i);
						if(cNode.getNodeName().equals("title"))
						{
							try
							{
								chnTitle = cNode.getChildNodes().item(0).getNodeValue();
								chnTitle = chnTitle.replaceAll("\"","\"\"");
							}catch(Exception e)
							{
								logger.info("Exception in parsing title node");
								//e.printStackTrace();
							}
						}
						else if(cNode.getNodeName().equals("link"))
						{
							try
							{
								chnLink = cNode.getChildNodes().item(0).getNodeValue();
								chnLink = chnLink.replaceAll("\"","\"\"");
							}catch(Exception e)
							{
								logger.info("Exception in parsing link node");
								//e.printStackTrace();
							}
						}
						else if(cNode.getNodeName().equals("pubDate") || cNode.getNodeName().equals("lastBuildDate") || cNode.getNodeName().equals("dc:date"))
						{
							try
							{
								chnDate = cNode.getChildNodes().item(0).getNodeValue();
								chnDate = chnDate.replaceAll("\"","\"\"");
								chnDate = convertDateFormat(chnDate);
							}catch(Exception e)
							{
								logger.info("Exception in parsing pubDate node");
								//e.printStackTrace();
							}
						}
						else if(cNode.getNodeName().equals("item"))
						{
							String title = "";
							String link ="";
							String author ="";
							String pdate ="";
							String desc ="";
							//String cNodeStr="";
							HashMap custValArray = new HashMap(25);
							org.w3c.dom.NodeList itcNodes = cNode.getChildNodes();
							for(int j=0; j<itcNodes.getLength();j++)
							{
								org.w3c.dom.Node itcNode = (org.w3c.dom.Node)itcNodes.item(j);
								if(itcNode.getNodeName().equals("title"))
								{
									try
									{
										title = itcNode.getChildNodes().item(0).getNodeValue();
										title = title.replaceAll("\"","\"\"");
									}
									catch(Exception e)
									{
										logger.info("Exception in parsing title node");
										//e.printStackTrace();
									}
								}
								else if(itcNode.getNodeName().equals("link"))
								{
									try
									{
										isLink = true;
										link = itcNode.getChildNodes().item(0).getNodeValue();
										link = link.replaceAll("\"","\"\"");
									}
									catch(Exception e)
									{
//										isLink = false;
										logger.info("Exception in parsing link node");
										//e.printStackTrace();
									}
								}
								else if(itcNode.getNodeName().equals("dc:creator") || itcNode.getNodeName().equals("author"))
								{
									try
									{
										isAuthor = true;
										author = itcNode.getChildNodes().item(0).getNodeValue();
										if(author == null)
										{
											author = itcNode.getChildNodes().item(0).getTextContent();
										}
										author = author.replaceAll("\"","\"\"");
									}
									catch(Exception e)
									{
//										isAuthor = false;
										logger.info("Exception in parsing quthor node");
										//e.printStackTrace();
									}
								}
								else if(itcNode.getNodeName().equals("pubDate") || itcNode.getNodeName().equals("dc:date"))
								{
									try
									{
										ispDate = true;
										pdate = itcNode.getChildNodes().item(0).getNodeValue();
										pdate = pdate.replaceAll("\"","\"\"");
										pdate = convertDateFormat(pdate);
									}
									catch(Exception e)
									{
//										ispDate = false;
										logger.info("Exception in parsing pubDate node");
										//e.printStackTrace();
									}
								}
								else if(itcNode.getNodeName().equals("description"))
								{
									try
									{
										isDesc = true;
										desc = itcNode.getChildNodes().item(0).getNodeValue();
										//desc = desc.replaceAll("\n"," ");
										desc = desc.replaceAll("\\<.*?\\>"," ");
										desc = desc.replaceAll("\"","\"\"");
									}
									catch(Exception e)
									{
//										isDesc = false;
										logger.info("Exception in parsing description node");
										//e.printStackTrace();
									}
								}
								else
								{
									//check for custom nodes here...
									for(int cNCount=0; cNCount<customList.size(); cNCount++)
									{
										String checkName = (String)customList.get(cNCount);
										if(itcNode.getNodeName().startsWith(checkName+":"))
										{
											if(cHeaderStr.equals(""))
											{
												cHeaderStr = cHeaderStr+"\""+itcNode.getNodeName()+"\"";
												customNodes.add(itcNode.getNodeName());
											}
											else if(cHeaderStr.indexOf(itcNode.getNodeName()) == -1)
											{
												cHeaderStr = cHeaderStr+",";
												cHeaderStr = cHeaderStr+"\""+itcNode.getNodeName()+"\"";
												customNodes.add(itcNode.getNodeName());
											}
											String custVal ="";
											if(itcNode.getChildNodes().item(0) != null)
											{
												custVal = itcNode.getChildNodes().item(0).getNodeValue();
											}
											if(custVal== null)
											{
												custVal="";
											}
											//custVal = custVal.replaceAll("\n"," ");
											custVal = custVal.replaceAll("\\<.*?\\>"," ");
											custVal = custVal.replaceAll("\"","\"\"");
											for(int s=custValArray.size();s<customNodes.indexOf(itcNode.getNodeName());s++)
											{
												custValArray.put(s,"");
											}
											custValArray.put(customNodes.indexOf(itcNode.getNodeName()),custVal);
										/*
										   if(cNodeStr.equals(""))
										   {
										   cNodeStr = cNodeStr + "\""+custVal+"\"";
										   }
										   else
										   {
										   cNodeStr = cNodeStr+",";
										   cNodeStr = cNodeStr+"\""+custVal+"\"";
										   }
										 */
										}
									}
								}
							}
							//pagedata.append("\""+title+"\"");
							rowData.append("\""+title+"\"");
							if(isDesc)
							{
								/*pagedata.append(",");
								pagedata.append("\""+desc+"\"");*/
								rowData.append(",");
								rowData.append("\""+desc+"\"");
							}
							if(isLink)
							{
								/*pagedata.append(",");
								pagedata.append("\""+link+"\"");*/
								rowData.append(",");
								rowData.append("\""+link+"\"");
							}
							if(isAuthor)
							{
								/*pagedata.append(",");
								pagedata.append("\""+author+"\"");*/
								rowData.append(",");
								rowData.append("\""+author+"\"");
							}
							if(ispDate)
							{
								rowData.append(",");
								rowData.append("\""+pdate+"\"");
							}
						/*
						if(!cNodeStr.equals(""))
						{
							pagedata.append(",");
							pagedata.append(cNodeStr);
						}
						 */
							for(int cc=0;cc<customNodes.size();cc++)
							{
								//pagedata.append(",");
								rowData.append(",");
								try
								{
									if(custValArray.get(cc) == null)
									{
										//pagedata.append("\"\"");
										rowData.append("\"\"");
									}
									else
									{
										//pagedata.append("\""+custValArray.get(cc)+"\"");
										rowData.append("\""+custValArray.get(cc)+"\"");
									}
								}
								catch(Exception e)
								{
									//pagedata.append("");
									rowData.append("");
								}
							}
							//pagedata.append("\n");
							rssContent.add(rowData.toString());
							rowData.setLength(0);

						}
					}

					StringBuffer chnHeader = new StringBuffer();

					chnHeader.append("\"Feed Title : \",\""+chnTitle+"\"\n");
					resultArr.add("\"Feed Title : \",\""+chnTitle+"\"");

					chnHeader.append("\"Link : \",\""+chnLink+"\"\n");
					resultArr.add("\"Link : \",\""+chnLink+"\"");

					chnHeader.append("\"Published Date : \",\""+chnDate+"\"\n");
					resultArr.add("\"Published Date : \",\""+chnDate+"\"");
					chnHeader.append("\n");

					resultArr.add("");

					StringBuffer header = new StringBuffer();
					header.append("\"Title\"");
					int colSize = 1;
					if(isDesc)
					{
						colSize +=1;
						header.append(",\"Description\"");
					}
					if(isLink)
					{
						colSize +=1;
						header.append(",\"Link\"");
					}
					if(isAuthor)
					{
						colSize +=1;
						header.append(",\"Author\"");
					}
					if(ispDate)
					{
						colSize +=1;
						header.append(",\"Published Date\"");
					}
					if(!cHeaderStr.equals(""))
					{
						colSize +=1;
						header.append(","+cHeaderStr);
					}
					resultArr.add(header.toString());
					//header.append("\n");


					resultArr.addAll(rssContent);
					this.bean.setColCount(colSize);

					//pagedata = header.append(pagedata);
					/*StringBuffer chnHeader = new StringBuffer();
					chnHeader.append("\"Feed Title : \",\""+chnTitle+"\"\n");
					chnHeader.append("\"Link : \",\""+chnLink+"\"\n");
					chnHeader.append("\"Published Date : \",\""+chnDate+"\"\n");
					chnHeader.append("\n");*/
				//	pagedata = chnHeader.append(pagedata);
				}
			}
			else if(nName.equals("rdf:RDF"))
			{
				//for custom nodes...
				List customList = new ArrayList();
				NamedNodeMap nList = rootNode.getAttributes();
				for(int nl=0; nl<nList.getLength();nl++)
				{
					org.w3c.dom.Node custNode = nList.item(nl);
					String custNdName = custNode.getNodeName();
					if(custNdName.startsWith("xmlns:"))
					{
						String cnName = custNdName.substring(6,custNdName.length());
						customList.add(cnName);
					}
				}
				//for the customnodes...
				List customNodes = new ArrayList();
				boolean isAuthor = false;
				boolean isLink = false;
				boolean ispDate = false;
				boolean isDesc = false;
				String chnTitle = "";
				String chnLink = "";
				String chnDate = "";
				String cHeaderStr="";
				org.w3c.dom.NodeList cNodes =rootNode.getChildNodes();
				for(int i=0; i<cNodes.getLength();i++)
				{
					org.w3c.dom.Node cNode = (org.w3c.dom.Node)cNodes.item(i);
					if(cNode.getNodeName().equals("channel"))
					{
						org.w3c.dom.NodeList itcNodes = cNode.getChildNodes();
						for(int j=0; j<itcNodes.getLength();j++)
						{
							org.w3c.dom.Node itcNode = (org.w3c.dom.Node)itcNodes.item(j);
							if(itcNode.getNodeName().equals("title"))
							{
								try
								{
									chnTitle = itcNode.getChildNodes().item(0).getNodeValue();
									chnTitle = chnTitle.replaceAll("\"","\"\"");
								}catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing title node");
								}
							}
							else if(itcNode.getNodeName().equals("link"))
							{
								try
								{
									chnLink = itcNode.getChildNodes().item(0).getNodeValue();
									chnLink = chnLink.replaceAll("\"","\"\"");
								}catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing link node");
								}
							}
							else if(itcNode.getNodeName().equals("pubDate") || itcNode.getNodeName().equals("lastBuildDate") || itcNode.getNodeName().equals("dc:date"))
							{
								try
								{
									chnDate = itcNode.getChildNodes().item(0).getNodeValue();
									chnDate = chnDate.replaceAll("\"","\"\"");
									chnDate = convertDateFormat(chnDate);
								}catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing pubDate node");
								}
							}
						}
					}
					else if(cNode.getNodeName().equals("item"))
					{
						String title = "";
						String link ="";
						String author ="";
						String pdate ="";
						String desc ="";
						//String cNodeStr="";
						HashMap custValArray = new HashMap(25);
						org.w3c.dom.NodeList itcNodes = cNode.getChildNodes();
						for(int j=0; j<itcNodes.getLength();j++)
						{
							org.w3c.dom.Node itcNode = (org.w3c.dom.Node)itcNodes.item(j);
							if(itcNode.getNodeName().equals("title"))
							{
								try
								{
									title = itcNode.getChildNodes().item(0).getNodeValue();
									title = title.replaceAll("\"","\"\"");
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing title node");
								}
							}
							else if(itcNode.getNodeName().equals("link"))
							{
								try
								{
									isLink = true;
									link = itcNode.getChildNodes().item(0).getNodeValue();
									link = link.replaceAll("\"","\"\"");
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing link node");
								}
							}
							else if(itcNode.getNodeName().equals("dc:creator") || itcNode.getNodeName().equals("author"))
							{
								try
								{
									isAuthor = true;
									author = itcNode.getChildNodes().item(0).getNodeValue();
									author = author.replaceAll("\"","\"\"");
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing author node");
								}
							}
							else if(itcNode.getNodeName().equals("pubDate") || itcNode.getNodeName().equals("dc:date"))
							{
								try
								{
									ispDate = true;
									pdate = itcNode.getChildNodes().item(0).getNodeValue();
									pdate = pdate.replaceAll("\"","\"\"");
									pdate = convertDateFormat(pdate);
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing pubData node");
								}
							}
							else if(itcNode.getNodeName().equals("description"))
							{
								try
								{
									isDesc = true;
									desc = itcNode.getChildNodes().item(0).getNodeValue();
									//desc = desc.replaceAll("\n"," ");
									desc = desc.replaceAll("\\<.*?\\>"," ");
									desc = desc.replaceAll("\"","\"\"");
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing description node");
								}
							}
							else
							{
								//check for custom nodes here...
								for(int cNCount=0; cNCount<customList.size(); cNCount++)
								{
									String checkName = (String)customList.get(cNCount);
									if(itcNode.getNodeName().startsWith(checkName+":"))
									{
										if(cHeaderStr.equals(""))
										{
											cHeaderStr = cHeaderStr+"\""+itcNode.getNodeName()+"\"";
											customNodes.add(itcNode.getNodeName());
										}
										else if(cHeaderStr.indexOf(itcNode.getNodeName()) == -1)
										{
											cHeaderStr = cHeaderStr+",";
											cHeaderStr = cHeaderStr+"\""+itcNode.getNodeName()+"\"";
											customNodes.add(itcNode.getNodeName());
										}
										String custVal ="";
										if(itcNode.getChildNodes().item(0) != null)
										{
											custVal = itcNode.getChildNodes().item(0).getNodeValue();
										}
										if(custVal== null)
										{
											custVal="";
										}
										//custVal = custVal.replaceAll("\n"," ");
										custVal = custVal.replaceAll("\\<.*?\\>"," ");
										custVal = custVal.replaceAll("\"","\"\"");
										for(int s=custValArray.size();s<customNodes.indexOf(itcNode.getNodeName());s++)
										{
											custValArray.put(s,"");
										}
										custValArray.put(customNodes.indexOf(itcNode.getNodeName()),custVal);
									/*
									if(cNodeStr.equals(""))
									{
										cNodeStr = cNodeStr + "\""+custVal+"\"";
									}
									else
									{
										cNodeStr = cNodeStr+",";
										cNodeStr = cNodeStr+"\""+custVal+"\"";
									}
									 */
									}
								}
							}
						}
						//pagedata.append("\""+title+"\"");
						rowData.append("\""+title+"\"");
						if(isDesc)
						{
							/*pagedata.append(",");
							pagedata.append("\""+desc+"\"");*/
							rowData.append(",");
							rowData.append("\""+desc+"\"");
						}
						if(isLink)
						{
							/*pagedata.append(",");
							pagedata.append("\""+link+"\"");*/
							rowData.append(",");
							rowData.append("\""+link+"\"");
						}
						if(isAuthor)
						{
							/*pagedata.append(",");
							pagedata.append("\""+author+"\"");*/
							rowData.append(",");
							rowData.append("\""+author+"\"");
						}
						if(ispDate)
						{
							/*pagedata.append(",");
							pagedata.append("\""+pdate+"\"");*/
							rowData.append(",");
							rowData.append("\""+pdate+"\"");
						}
					/*
					if(!cNodeStr.equals(""))
					{
						pagedata.append(",");
						pagedata.append(cNodeStr);
					}
					 */
						for(int cc=0;cc<customNodes.size();cc++)
						{
							//pagedata.append(",");
							rowData.append(",");
							try
							{
								//logger.info("val is ... "+custValArray.get(cc));
								if(custValArray.get(cc) == null)
								{
									//pagedata.append("\"\"");
									rowData.append("\"\"");
								}
								else
								{
									//pagedata.append("\""+custValArray.get(cc)+"\"");
									rowData.append("\""+custValArray.get(cc)+"\"");
								}
							}
							catch(Exception e)
							{
								//logger.log(Level.WARNING,null,e);

								//pagedata.append("");
								rowData.append("");
							}
						}
						//pagedata.append("\n");
						rowData.append("\n");
					}
				}

				StringBuffer chnHeader = new StringBuffer();
				//chnHeader.append("\"Feed Details\"\n");
				//chnHeader.append("\"Feed Title : \",\""+chnTitle+"\"\n");
				resultArr.add("\"Feed Title : \",\""+chnTitle+"\"");
				//chnHeader.append("\"Link : \",\""+chnLink+"\"\n");
				resultArr.add("\"Link : \",\""+chnLink+"\"");
				//chnHeader.append("\"Published Date : \",\""+chnDate+"\"\n");
				resultArr.add("\"Published Date : \",\""+chnDate+"\"");
				//chnHeader.append("\n");
				resultArr.add("");

				StringBuffer header = new StringBuffer();
				int colSize = 1;
				header.append("\"Title\"");
				if(isDesc)
				{
					colSize += 1;
					header.append(",\"Description\"");
				}
				if(isLink)
				{
					colSize += 1;
					header.append(",\"Link\"");
				}
				if(isAuthor)
				{
					colSize += 1;
					header.append(",\"Author\"");
				}
				if(ispDate)
				{
					colSize += 1;
					header.append(",\"Published Date\"");
				}
				if(!cHeaderStr.equals(""))
				{
					colSize += 1;
					header.append(","+cHeaderStr);
				}
				resultArr.add(header.toString());
				//header.append("\n");
				//pagedata = header.append(pagedata);
				/*StringBuffer chnHeader = new StringBuffer();
				//chnHeader.append("\"Feed Details\"\n");
				chnHeader.append("\"Feed Title : \",\""+chnTitle+"\"\n");
				chnHeader.append("\"Link : \",\""+chnLink+"\"\n");
				chnHeader.append("\"Published Date : \",\""+chnDate+"\"\n");
				chnHeader.append("\n");*/
				//pagedata = chnHeader.append(pagedata);
				resultArr.addAll(rssContent);
				this.bean.setColCount(colSize);
			}
			else if(nName.equals("feed"))
			{
				//pagedata.append("\"Title\",\"Link\",\"Author\",\"Publish Date\",\"Summary\"\n");
				//for custom nodes...
				List customList = new ArrayList();
				NamedNodeMap nList = rootNode.getAttributes();
				for(int nl=0; nl<nList.getLength();nl++)
				{
					org.w3c.dom.Node custNode = nList.item(nl);
					String custNdName = custNode.getNodeName();
					if(custNdName.startsWith("xmlns:"))
					{
						String cnName = custNdName.substring(6,custNdName.length());
						customList.add(cnName);
					}
				}
				//for the customnodes...
				List customNodes = new ArrayList();
				boolean isAuthor = false;
				boolean isLink = false;
				boolean ispDate = false;
				boolean isSumm = false;
				String chnTitle = "";
				String chnLink = "";
				String chnDate = "";
				String cHeaderStr="";
				org.w3c.dom.NodeList cNodes = rootNode.getChildNodes();
				String author ="";
				for(int i=0; i<cNodes.getLength();i++)
				{
					org.w3c.dom.Node cNode = (org.w3c.dom.Node)cNodes.item(i);
					if(cNode.getNodeName().equals("title"))
					{
						try
						{
							chnTitle = cNode.getChildNodes().item(0).getNodeValue();
							chnTitle = chnTitle.replaceAll("\"","\"\"");
						}catch(Exception e)
						{
							//logger.log(Level.WARNING, null, e);
							logger.info("Exception in parsing title node");
						}
					}
					else if(cNode.getNodeName().equals("link"))
					{
						try
						{
							chnLink = "";
							chnLink = cNode.getAttributes().getNamedItem("href").getNodeValue(); //No I18N
							chnLink = chnLink.replaceAll("\"","\"\"");
						}catch(Exception e)
						{
							//logger.log(Level.WARNING, null, e);
							logger.info("Exception in parsing link node");
						}
					}
					else if(cNode.getNodeName().equals("published") || cNode.getNodeName().equals("created") || cNode.getNodeName().equals("updated"))
					{
						try
						{
							chnDate = cNode.getChildNodes().item(0).getNodeValue();
							chnDate = chnDate.replaceAll("\"","\"\"");
							chnDate = convertDateFormat(chnDate);
						}catch(Exception e)
						{
							//logger.log(Level.WARNING, null, e);
							logger.info("Exception in parsing created node");
						}
					}
					else if(cNode.getNodeName().equals("author"))
					{
						org.w3c.dom.NodeList authcNodes = cNode.getChildNodes();
						for(int k=0; k<authcNodes.getLength();k++)
						{
							org.w3c.dom.Node authcNode = (org.w3c.dom.Node)authcNodes.item(k);
							if(authcNode.getNodeName().equals("name"))
							{
								try
								{
									isAuthor = true;
									author = authcNode.getChildNodes().item(0).getNodeValue();
									author = author.replaceAll("\"","\"\"");
									//logger.info("author is ... "+author);
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing name node");
								}
							}
						}
					}
					if(cNode.getNodeName().equals("entry"))
					{
						org.w3c.dom.NodeList itcNodes = cNode.getChildNodes();
						String title = "";
						String link ="";
						String pdate ="";
						String summary ="";
						//String cNodeStr="";
						HashMap custValArray = new HashMap(25);
						for(int j=0; j<itcNodes.getLength();j++)
						{
							org.w3c.dom.Node itcNode = (org.w3c.dom.Node)itcNodes.item(j);
							if(itcNode.getNodeName().equals("title"))
							{
								try
								{
									title = itcNode.getChildNodes().item(0).getNodeValue();
									title = title.replaceAll("\"","\"\"");
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing title node");
								}
							}
							else if(itcNode.getNodeName().equals("link"))
							{
								try
								{
									isLink = true;
									link = itcNode.getAttributes().getNamedItem("href").getNodeValue();//No I18N
									link = link.replaceAll("\"","\"\"");
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing link node");
								}
							}
							else if(itcNode.getNodeName().equals("summary"))
							{
								try
								{
									isSumm = true;
									summary = itcNode.getChildNodes().item(0).getNodeValue();
									//summary = summary.replaceAll("\n"," ");
									summary = summary.replaceAll("\\<.*?\\>"," ");
									summary = summary.replaceAll("\"","\"\"");
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing summary node");
								}
							}
							else if(itcNode.getNodeName().equals("published") || itcNode.getNodeName().equals("created") || itcNode.getNodeName().equals("updated"))
							{
								try
								{
									ispDate = true;
									pdate = itcNode.getChildNodes().item(0).getNodeValue();
									pdate = pdate.replaceAll("\"","\"\"");
									pdate = convertDateFormat(pdate);
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
									logger.info("Exception in parsing published node");
								}
							}
							else if(itcNode.getNodeName().equals("author"))
							{
								try
								{
									org.w3c.dom.NodeList authcNodes = itcNode.getChildNodes();
									for(int k=0; k<authcNodes.getLength();k++)
									{
										org.w3c.dom.Node authcNode = (org.w3c.dom.Node)authcNodes.item(k);
										if(authcNode.getNodeName().equals("name"))
										{
											try
											{
												isAuthor = true;
												author = authcNode.getChildNodes().item(0).getNodeValue();
												author = author.replaceAll("\"","\"\"");
											}
											catch(Exception e)
											{
												//logger.log(Level.WARNING, null, e);
												logger.info("Exception in parsing name node");
											}
										}
									}
								}
								catch(Exception e)
								{
									//logger.log(Level.WARNING, null, e);
								}
							}
							else
							{
								//check for custom nodes here...
								for(int cNCount=0; cNCount<customList.size(); cNCount++)
								{
									String checkName = (String)customList.get(cNCount);
									if(itcNode.getNodeName().startsWith(checkName+":"))
									{
										if(cHeaderStr.equals(""))
										{
											cHeaderStr = cHeaderStr+"\""+itcNode.getNodeName()+"\"";
											customNodes.add(itcNode.getNodeName());
										}
										else if(cHeaderStr.indexOf(itcNode.getNodeName()) == -1)
										{
											cHeaderStr = cHeaderStr+",";
											cHeaderStr = cHeaderStr+"\""+itcNode.getNodeName()+"\"";
											customNodes.add(itcNode.getNodeName());
										}
										String custVal ="";
										if(itcNode.getChildNodes().item(0) != null)
										{
											custVal = itcNode.getChildNodes().item(0).getNodeValue();
										}
										if(custVal== null)
										{
											custVal="";
										}
										//custVal = custVal.replaceAll("\n"," ");
										custVal = custVal.replaceAll("\\<.*?\\>"," ");
										custVal = custVal.replaceAll("\"","\"\"");
										for(int s=custValArray.size();s<customNodes.indexOf(itcNode.getNodeName());s++)
										{
											custValArray.put(s,"");
										}
										custValArray.put(customNodes.indexOf(itcNode.getNodeName()),custVal);
									/*
									if(cNodeStr.equals(""))
									{
										cNodeStr = cNodeStr + "\""+custVal+"\"";
									}
									else
									{
										cNodeStr = cNodeStr+",";
										cNodeStr = cNodeStr+"\""+custVal+"\"";
									}
									 */
									}
								}
							}
						}
						//pagedata.append("\""+title+"\"");
						rowData.append("\""+title+"\"");
						if(isSumm)
						{
							/*pagedata.append(",");
							pagedata.append("\""+summary+"\"");*/
							rowData.append(",");
							rowData.append("\""+summary+"\"");
						}
						if(isLink)
						{
							/*pagedata.append(",");
							pagedata.append("\""+link+"\"");*/
							rowData.append(",");
							rowData.append("\""+link+"\"");
						}
						if(isAuthor)
						{
							/*pagedata.append(",");
							pagedata.append("\""+author+"\"");*/
							rowData.append(",");
							rowData.append("\""+author+"\"");
						}
						if(ispDate)
						{
							/*pagedata.append(",");
							pagedata.append("\""+pdate+"\"");*/
							rowData.append(",");
							rowData.append("\""+pdate+"\"");
						}
					/*
					   if(!cNodeStr.equals(""))
					   {
					   pagedata.append(",");
					   pagedata.append(cNodeStr);
					   }
					 */
						for(int cc=0;cc<customNodes.size();cc++)
						{
							//pagedata.append(",");
							rowData.append(",");
							try
							{
								//logger.info("val is ... "+custValArray.get(cc));
								if(custValArray.get(cc) == null)
								{
									//pagedata.append("\"\"");
									rowData.append("\"\"");
								}
								else
								{
//									pagedata.append("\""+custValArray.get(cc)+"\"");
									rowData.append("\""+custValArray.get(cc)+"\"");
								}
							}
							catch(Exception e)
							{
								//pagedata.append("");
								rowData.append("");
							}
						}
						//pagedata.append("\n");
						rowData.append("\n");
					}
				}

				StringBuffer chnHeader = new StringBuffer();
				/*chnHeader.append("\"Feed Details\"\n");
				chnHeader.append("\"Feed Title : \",\""+chnTitle+"\"\n");
				chnHeader.append("\"Link : \",\""+chnLink+"\"\n");
				chnHeader.append("\"Published Date : \",\""+chnDate+"\"\n");
				chnHeader.append("\n");*/
				resultArr.add("\"Feed Title : \",\""+chnTitle+"\"");
				resultArr.add("\"Link : \",\""+chnLink+"\"");
				resultArr.add("\"Published Date : \",\""+chnDate+"\"");
				resultArr.add("");

				StringBuffer header = new StringBuffer();
				int colSize = 1;
				header.append("\"Title\"");
				if(isSumm)
				{
					colSize += 1;
					header.append(",\"Summary\"");
				}
				if(isLink)
				{
					colSize += 1;
					header.append(",\"Link\"");
				}
				if(isAuthor)
				{
					colSize += 1;
					header.append(",\"Author\"");
				}
				if(ispDate)
				{
					colSize += 1;
					header.append(",\"Published Date\"");
				}
				if(!cHeaderStr.equals(""))
				{
					colSize += 1;
					header.append(","+cHeaderStr);
				}
				resultArr.add(header.toString());
				resultArr.addAll(rssContent);
				this.bean.setColCount(colSize);
				//header.append("\n");
				//pagedata = header.append(pagedata);
				/*StringBuffer chnHeader = new StringBuffer();
				//chnHeader.append("\"Feed Details\"\n");
				chnHeader.append("\"Feed Title : \",\""+chnTitle+"\"\n");
				chnHeader.append("\"Link : \",\""+chnLink+"\"\n");
				chnHeader.append("\"Published Date : \",\""+chnDate+"\"\n");
				chnHeader.append("\n");*/
				//pagedata = chnHeader.append(pagedata);
			}
			//logger.info("in getRssData ... "+pagedata.toString());

		}catch (Exception e){
			logger.log(Level.WARNING, "[Data-Connection] exception in parsing RSS/Feed data", e);
		}
		//return pagedata;
		this.bean.setRowCount(resultArr.size());
		return resultArr;
		}



	@Override
	public ServiceInfo getServiceInfo() {
		return ConstantHolder.getServiceInfo(ServiceConstant.UNAUTH_RSS);
	}
	@Override
	public void setAPIURL() {}

	@Override
	public JSONObjectWrapper getSelectDataContent() {return null;}
	@Override
	public List getFields() {return null;}
	@Override
	public void setPreRequestProcess() {}
	@Override
	public Object fetchServiceContent() {return null;}

	@Override
	public String parseCode(String resp) {
		return null;
	}


	private static String convertDateFormat(String date)
	{
		Date theDate = null;
		try
		{
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
			theDate=df.parse(date);
		}
		catch(ParseException pe0)
		{
			try
			{
				SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
				theDate=df.parse(date);
			}
			catch(ParseException pe1)
			{
				try
				{
					theDate = new Date(date);
				}
				catch(Exception pe2)
				{
					//logger.log(Level.WARNING, null, pe2);
					logger.info("Exception in parsing date format");
				}
			}
		}
		try
		{
			FieldPosition fp = new FieldPosition(0);
			SimpleDateFormat f = new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");
			StringBuffer temp  = new StringBuffer("");
			StringBuffer sb = f.format(theDate,temp,fp);
			date = sb.toString();
		}
		catch(Exception e)
		{
			//logger.log(Level.WARNING, null, e);
			logger.info("Exception in parsing date format");
		}
		return date;
	}
}
