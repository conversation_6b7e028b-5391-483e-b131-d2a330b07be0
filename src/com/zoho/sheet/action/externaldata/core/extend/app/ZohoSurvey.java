package com.zoho.sheet.action.externaldata.core.extend.app;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.ConstantHolder;
import com.zoho.sheet.action.externaldata.constants.ServiceActionConstants;
import com.zoho.sheet.action.externaldata.constants.ServiceConstant;
import com.zoho.sheet.action.externaldata.constants.ServiceInfo;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.action.externaldata.core.parser.CSVParser;
import com.zoho.sheet.connection.ZSConnection;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.ByteArrayInputStream;
import java.util.*;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.MAX_RECORD_LIMIT;


public class ZohoSurvey extends CloudService {

    public ZohoSurvey() {
        this.bean = new ServiceBean();
        this.bean.setServiceConstant(ServiceConstant.ZSURVEY);
    }

    @Override
    public ServiceInfo getServiceInfo() {
        return ConstantHolder.getServiceInfo(ServiceConstant.ZSURVEY);
    }

    @Override
    public void setAPIURL() {
        String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
        this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());
        String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
        this.bean.setRequestURL(requestURL);
        this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));
    }

    @Override
    public void setPreciseHeader(ZSConnection httpCon) {
        switch(this.bean.getServiceActionConstant()){
            case ServiceActionConstants.GET_ORG:
            case ServiceActionConstants.GET_MODULES:
            case ServiceActionConstants.GET_FIELDS:
                httpCon.setRequestHeader(new String[]{this.bean.getAccessTokenParamName()}, new String[]{this.bean.getAccessToken()});
                break;
            case ServiceActionConstants.FETCH_DATA:
                httpCon.setRequestHeader(new String[]{this.bean.getAccessTokenParamName(), "X-Accept-Content"}, new String[]{this.bean.getAccessToken(), "csv"}); //No I18N
                break;
        }
    }

    @Override
    public void setPreciseFormData(ZSConnection httpCon) {

    }

    @Override
    public Object parseData(Object resp) {

        switch (this.bean.getServiceActionConstant()) {
            case ServiceActionConstants.GET_ORG:
                Map<String[], String[][]> orgDepMeta = new LinkedHashMap<>();
                JSONArray orgArray = new JSONArray((String) resp);
                int orgArrayLength = orgArray.length();

                for (int i = 0; i < orgArrayLength; i++) {
                    JSONObject orgJSON = orgArray.getJSONObject(i);
                    if(!"allow".equalsIgnoreCase(orgJSON.optString("accessLevel")))
                    {
                        continue;
                    }
                    String portalId = orgJSON.optString("portalId", ""); //No I18N
                    String portalName = orgJSON.optString("portalName", ""); //No I18N

                    JSONArray departmentsArr = orgJSON.optJSONArray("departments"); //No I18N
                    int deptArrayLength = departmentsArr != null ? departmentsArr.length() : 0;
                    String[][] departmentsMeta = new String[deptArrayLength][];

                    int k = 0;
                    for (int j = 0; j < deptArrayLength; j++) {
                        JSONObject departmentsObj = departmentsArr.getJSONObject(j);
                        if(!"accepted".equalsIgnoreCase(departmentsObj.optString("status")))
                        {
                            continue;
                        }
                        String depId = departmentsObj.optString("groupUniqueId", ""); //No I18N
                        String depName = departmentsObj.optString("name", ""); //No I18N
                        departmentsMeta[k++] = new String[]{depId, depName};
                    }
                    orgDepMeta.put(new String[]{portalId, portalName}, departmentsMeta);
                }

                return orgDepMeta;

            case ServiceActionConstants.GET_MODULES:
                JSONArray surveyArray = new JSONArray((String) resp);
                List<List<String>> moduleArr = new ArrayList<>();
                for (int i = 0; i < surveyArray.length(); i++) {
                    JSONObject surveyJSON = surveyArray.getJSONObject(i);
                    if ("survey".equalsIgnoreCase(surveyJSON.getString("type"))) {
                        String id = surveyJSON.getString("id");
                        String name = surveyJSON.getString("name");
                        moduleArr.add(Arrays.asList(id, name));
                    }
                }
                return moduleArr;

            case ServiceActionConstants.GET_FIELDS:
                JSONArray pagesArray = new JSONObject((String) resp).getJSONArray("pages");
                List<List<String>> fieldsArr = new ArrayList<>();

                for (int i = 0; i < pagesArray.length(); i++) {
                    JSONArray questionsArray = pagesArray.getJSONObject(i).getJSONArray("questions");

                    for (int j = 0; j < questionsArray.length(); j++) {
                        JSONObject questionObj = questionsArray.getJSONObject(j);
                        if(questionObj.has("fields"))
                        {
                            JSONArray fieldsArray = questionObj.getJSONArray("fields");
                            for(int k = 0; k < fieldsArray.length(); k++) {
                                JSONObject fieldObj = fieldsArray.getJSONObject(k);
                                String id = fieldObj.getString("id");
                                String msg = fieldObj.getString("msg");
                                if (!msg.isEmpty()) {
                                    msg = msg.replaceAll("<[^>]+>", "");
                                }
                                fieldsArr.add(Arrays.asList(id, msg));
                            }
                        }
                        else
                        {
                            String id = questionObj.getString("id");
                            String msg = questionObj.getString("msg");

                            if (!msg.isEmpty()) {
                                msg = msg.replaceAll("<[^>]+>", "");
                            }
                            fieldsArr.add(Arrays.asList(id, msg));
                        }
                    }
                }
                if(!fieldsArr.isEmpty())
                {
                    // NOTE: The order cannot be changed because we receive data from the survey in this order.
                    fieldsArr.add(0,Arrays.asList("Collector", "Collector"));
                    fieldsArr.add(0,Arrays.asList("Time taken", "Time taken"));
                    fieldsArr.add(0,Arrays.asList("Completion time", "Completion time"));
                    fieldsArr.add(0,Arrays.asList("Start time", "Start time"));
                    fieldsArr.add(0,Arrays.asList("Survey URL accessed by the respondent", "Survey URL accessed by the respondent"));
                    fieldsArr.add(0,Arrays.asList("Response status", "Response status"));
                    fieldsArr.add(0,Arrays.asList("IP address", "IP address"));
                    fieldsArr.add(0,Arrays.asList("Response ID", "Response ID"));
                }
                return fieldsArr;

            case ServiceActionConstants.FETCH_DATA:
//                JSONArray dataArray = new JSONArray((String) resp);
//                List<String> result = new ArrayList<>();
//                for (int i = 0; i < dataArray.length(); i++) {
//                    Map<String, String> idToAnswer = new HashMap<>();
//                    JSONArray pages = dataArray.getJSONObject(i).getJSONArray("pages");
//
//                    for (int j = 0; j < pages.length(); j++) {
//                        JSONArray questions = pages.getJSONObject(j).getJSONArray("questions");
//
//                        for (int k = 0; k < questions.length(); k++) {
//                            JSONObject question = questions.getJSONObject(k);
//                            String id = question.getString("id");
//                            String answer = question.getString("answer");
//                            idToAnswer.put(id, answer);
//                        }
//                    }
//                    List<String> answers = new ArrayList<>();
//                    for (String fieldIds : this.bean.getFieldLinkNames()) {
//                        answers.add(idToAnswer.getOrDefault(fieldIds, ""));
//                    }
//                    result.add(String.join(",", answers));
//                }

//                return result;

                return ((CSVParser)getServiceInfo().getContentParser()).getParsedData(Arrays.asList(this.bean.getFieldHeaders()), new ByteArrayInputStream(((String)resp).getBytes()),false);


        }

        return null;
    }

    @Override
    public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {

        JSONObjectWrapper response = new JSONObjectWrapper();

        // Process Organization Data
        String serviceOrgId = this.bean.getService_OrgID();
        if (serviceOrgId == null || serviceOrgId.isEmpty()) {
            this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
            setCommonDetails();
            setAccessToken();
            setAPIURL();

            Map<String[], String[][]> orgDepMeta = (Map<String[], String[][]>)sendRequest();
            if (orgDepMeta != null) {
                int i = 0;
                List orgList = new ArrayList<>();
                List DBList = new ArrayList<>();
                for (Map.Entry<String[], String[][]> entry : orgDepMeta.entrySet()) {
                    orgList.add(entry.getKey());
                    DBList.add(entry.getValue());
                    if(i==0)
                    {
                        i++;
                        this.bean.setService_OrgID(entry.getKey()[0]);
                        this.bean.setDatabaseName(entry.getValue()[0][0]);
                    }
                }
                response.put("orgList", orgList);
                response.put("DBList", DBList);
            }
        }

        List<Object> moduleFieldsList = new ArrayList<>();
        if((this.bean.getService_OrgID() != null && !this.bean.getService_OrgID().isEmpty()) && (this.bean.getDatabaseName() !=null && !this.bean.getDatabaseName().isEmpty())) {
            // Process Module Data
            String moduleName = this.bean.getModuleName();

            if (moduleName != null && !moduleName.isEmpty()) {
                this.bean.setModuleName(moduleName);
                List<Object> fieldList = getFields();
                if (fieldList != null) {
                    moduleFieldsList.add(fieldList);
                }
            } else {
                this.bean.setServiceActionConstant(ServiceActionConstants.GET_MODULES);
                setCommonDetails();
                setAccessToken();
                this.setPreRequestProcess();
                setAPIURL();

                List modulesList = (List) sendRequest();
                if (modulesList != null) {
                    for (int i = 0; i < modulesList.size(); i++) {
                        List moduleList = (List) modulesList.get(i);
                        Map<String, Object> moduleFields = new LinkedHashMap<>();

                        if (i == 0) {
                            this.bean.setModuleName((String) moduleList.get(0));
                            List<Object> fieldList = getFields();
                            if (fieldList != null) {
                                moduleFields.put("field", fieldList);
                            }
                        }

                        moduleFields.put("module", moduleList);
                        moduleFieldsList.add(moduleFields);
                    }
                }
            }
        }

        response.put("selectDataContent", moduleFieldsList);
        return response;
    }

    @Override
    public List getFields() throws DataConnection.DataConnectionFailedException {
        this.setCommonDetails();
        this.setAccessToken();
        this.bean.setServiceActionConstant(ServiceActionConstants.GET_FIELDS);
        this.setPreRequestProcess();
        setAPIURL();
        return (List) sendRequest();
    }

    @Override
    public void setPreRequestProcess() throws DataConnection.DataConnectionFailedException {

        switch (this.bean.getServiceActionConstant())
        {
            case ServiceActionConstants.GET_MODULES:
                this.bean.setDynamicURLParam(Arrays.asList(this.bean.getService_OrgID(), this.bean.getDatabaseName()));
                break;
            case ServiceActionConstants.GET_FIELDS:
            case ServiceActionConstants.FETCH_DATA:
                this.bean.setDynamicURLParam(Arrays.asList(this.bean.getService_OrgID(), this.bean.getDatabaseName(), this.bean.getModuleName()));
                break;
        }

    }

    public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
        setAccessToken();
        setAPIURL();
        setPreRequestProcess();
        String urlStr = this.bean.getRequestURL();
        int index = 1;
        List resultArr = new ArrayList();
        String [] fieldHeaders = bean.getFieldHeaders();
        resultArr.add(String.join(",", fieldHeaders));
        this.bean.setLimit(500);
        boolean has_more_data = true;
        while(has_more_data && index  <= MAX_RECORD_LIMIT){
            this.bean.setRequestURL(urlStr +"?range="+index+"&offset="+500); //NO I18N
            List THR_Content = (List<String>)sendRequest();
            if(THR_Content !=null && !THR_Content.isEmpty()){
                resultArr.addAll(THR_Content);
            }
            else
            {
                has_more_data = false;
            }
            index += 500;
        }
        this.bean.setRowCount(resultArr.size());
        this.bean.setColCount(fieldHeaders.length);
        return resultArr;
    }

    @Override
    public String parseCode(String resp) {
        try {
            JSONObject res =  new JSONObject(resp);
            return res.optString("errorcode", ""); //No I18N
        }
        catch (Exception e){
            return "";
        }
    }
}
