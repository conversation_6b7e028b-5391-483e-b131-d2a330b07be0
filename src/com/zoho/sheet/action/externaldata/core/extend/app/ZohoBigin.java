//$Id$
package com.zoho.sheet.action.externaldata.core.extend.app;

import com.adventnet.zoho.websheet.model.DataConnection;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.*;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.ServiceBean;
import com.zoho.sheet.connection.ZSConnection;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.zoho.sheet.action.externaldata.constants.ConstantHolder.MAX_RECORD_LIMIT;

/**
 * Created by k<PERSON>hnan-zt276 on  28/07/21
 */
public class ZohoBigin extends CloudService {

    private boolean has_more_page;

    private static Logger logger = Logger.getLogger(ZohoBigin.class.getName());

    public ZohoBigin() {
        this.bean = new ServiceBean();
        this.bean.setServiceConstant(ServiceConstant.ZBIGIN);




    }
    public void setBean() {
    }

    @Override
    public void setPreciseHeader(ZSConnection httpCon) {
        switch(this.bean.getServiceActionConstant()){
            case ServiceActionConstants.GET_ORG_LIST:
            case ServiceActionConstants.GET_ORG:
            case ServiceActionConstants.FETCH_DATA:
            case ServiceActionConstants.GET_MODULES:
            case ServiceActionConstants.GET_FIELDS:
                String[] paramNames = {this.bean.getAccessTokenParamName()};
                String[] paramValues = {this.bean.getAccessToken()};
                httpCon.setRequestHeader(paramNames, paramValues);
                break;
        }
    }

    @Override
    public void setPreciseFormData(ZSConnection httpCon) {
        // TODO Auto-generated method stub
    }

    public List<String> parseData(Object resp) {

        JSONObjectWrapper respMessage = new JSONObjectWrapper((String)resp);
        List resultArr = null;
        switch(this.bean.getServiceActionConstant()){
            case ServiceActionConstants.FETCH_DATA:
                resultArr = new ArrayList<String>();
                JSONArrayWrapper dataResult = respMessage.optJSONArray("data"); //No I18N
                has_more_page = respMessage.optJSONObject("info").optBoolean("more_records");  //NO I18N
                if(dataResult == null || dataResult.length() == 0)
                {
                    return resultArr;
                }
                String[] fieldLinkNames = this.bean.getFieldLinkNames();
                String[] fieldRow = new String[fieldLinkNames.length];
                for (int i = 0; i < dataResult.length(); i++) {
                    JSONObjectWrapper rowObj = dataResult.optJSONObject(i);
                    for (int j = 0; j < fieldLinkNames.length; j++) {
                        String field = fieldLinkNames[j];
                        Object content = rowObj.opt(field);
                        if(content instanceof JSONObjectWrapper)
                        {
                            Iterator<String> keys = ((JSONObjectWrapper)content).keys();
                            // get some_name_i_wont_know in str_Name
                            String firstKey=keys.next();
                            // get the value i care about
                            content = ((JSONObjectWrapper)content).optString(firstKey);
                            //content = ((JSONObject) content).optString("display_value", ((JSONObject) content).optString("url"));  //NO I18N
                        }
                        fieldRow[j] = (content != null && !(JSONObjectWrapper.NULL.equals(content))) ? Utility.getEncodedString(content.toString()) : "";
                    }
                    String recordStr = Arrays.stream(fieldRow).collect(Collectors.joining(","));
                    resultArr.add(recordStr);
                }
                break;
            case ServiceActionConstants.GET_MODULES:
                JSONArrayWrapper modulesArray = respMessage.getJSONArray("modules");
                resultArr = new ArrayList<String>();
                List moduleList = null;
                for(int i=0;i<modulesArray.length();i++) {
                    JSONObjectWrapper modulesObj = modulesArray.getJSONObject(i);
                    String linkName = Utility.getEncodedString(modulesObj.getString("api_name"));
                    String name = Utility.getEncodedString(modulesObj.getString("module_name"));

                    boolean apiSupport = modulesObj.getBoolean("api_supported");
                    if(apiSupport) {
                        moduleList = new ArrayList(2);
                        moduleList.add(linkName);
                        moduleList.add(name);
                        resultArr.add(moduleList);
                    }

                }
                break;
            case ServiceActionConstants.GET_FIELDS:
                List fieldArr = null;
                JSONArrayWrapper fieldsArray = respMessage.getJSONArray("fields");
                resultArr = new ArrayList<List>();
                for(int i=0;i<fieldsArray.length();i++) {
                    fieldArr = new ArrayList<String>();
                    JSONObjectWrapper fieldsObj = fieldsArray.getJSONObject(i);
                    String fieldId = Utility.getEncodedString(fieldsObj.getString("api_name"));
                    String fieldName = Utility.getEncodedString(fieldsObj.getString("display_label"));
                    int fieldType = getFieldType(this.bean.getModuleName(), fieldsObj.optString("data_type")); //NO I18N
                    fieldArr.add(fieldId);
                    fieldArr.add(fieldName);
                    fieldArr.add(fieldType);
                    resultArr.add(fieldArr);
                }
                break;
            case ServiceActionConstants.GET_ORG_LIST:
                resultArr = new ArrayList<String>();
                List orgList = null;
                if(Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("isDevelopmentMode"))) {
                    JSONArrayWrapper orgListArray = respMessage.optJSONArray("organizations"); //No I18N
                    for (int i = 0; i < orgListArray.length(); i++) {
                        JSONObjectWrapper orgDetailObj = orgListArray.getJSONObject(i);
                        String linkName = orgDetailObj.getString("id");
                        String name = Utility.getEncodedString(orgDetailObj.getString("name"));
                        orgList = new ArrayList(2);
                        orgList.add(linkName);
                        orgList.add(name);
                        resultArr.add(orgList);
                    }
                }
                else {
                    JSONObjectWrapper orgsJSON = respMessage.getJSONObject("data").getJSONObject("orgs");
                    Iterator orgIdsItr = orgsJSON.keys();
                    while (orgIdsItr.hasNext()) {
                        String linkName = String.valueOf(orgIdsItr.next());
                        JSONObjectWrapper orgDetailObj = orgsJSON.getJSONObject(linkName);
                        String name = Utility.getEncodedString(orgDetailObj.getString("portalName"));
                        orgList = new ArrayList(2);
                        orgList.add(linkName);
                        orgList.add(name);
                        resultArr.add(orgList);
                    }
                }
                break;
            case ServiceActionConstants.GET_ORG:

                JSONArrayWrapper orgDetailArray = respMessage.optJSONArray("org"); //No I18N
                resultArr = new ArrayList<String>();
                orgList = null;
                JSONObjectWrapper orgDetailObj = orgDetailArray.getJSONObject(0);
                String linkName = orgDetailObj.getString("zgid");
                String name = Utility.getEncodedString(orgDetailObj.getString("company_name"));
                orgList = new ArrayList(2);
                orgList.add(linkName);
                orgList.add(name);
                resultArr.add(orgList);
                break;
        }
        return resultArr;
    }

    private boolean isFilterSupported(String moduleName)
    {
        return "Contacts".equalsIgnoreCase(moduleName) || "Pipelines".equalsIgnoreCase(moduleName) || "Accounts".equalsIgnoreCase(moduleName) || "Products".equalsIgnoreCase(moduleName) || "Tasks".equalsIgnoreCase(moduleName) || "Events".equalsIgnoreCase(moduleName) || "Calls".equalsIgnoreCase(moduleName) || "Notes".equalsIgnoreCase(moduleName); //NO I18N
    }

    private int getFieldType(String moduleName, String serviceFieldType) {
        if(!isFilterSupported(moduleName))
        {
            return ConstantHolder.FilterType.NONE.getTypeId();
        }
        switch (serviceFieldType) {
            case "integer":
            case "currency":
            case "percent":
            case "double":
            case "decimal":
            case "autonumber":
            case "long":
            case "bigint":
                return ConstantHolder.FilterType.NUMBER.getTypeId();
            case "datetime":
            case "date":
                return ConstantHolder.FilterType.DATE.getTypeId();
            case "time_range":
                return ConstantHolder.FilterType.TIME.getTypeId();
        }
        return ConstantHolder.FilterType.TEXT.getTypeId();
    }
    public ServiceInfo getServiceInfo() {
        return ConstantHolder.getServiceInfo(ServiceConstant.ZBIGIN);
    }
    @Override
    public void setAPIURL() {
        String url = getServiceInfo().getURL(this.bean.getServiceActionConstant());
        this.apiurl = ExternalDataUtils.replaceDynamicMsg(url, this.bean.getDynamicURLParam());

        String requestURL = getServiceInfo().getDomainURL() + this.apiurl;
        this.bean.setRequestURL(requestURL);
        this.bean.setMethod(getServiceInfo().getMethod(this.bean.getServiceActionConstant()));

    }
    @Override
    public JSONObjectWrapper getSelectDataContent() throws DataConnection.DataConnectionFailedException {
        ArrayList moduleFieldsList =  null;

        if(this.bean.getModuleName()!=null && !this.bean.getModuleName().equals("")){
            moduleFieldsList = new ArrayList();
            this.bean.setModuleName(this.bean.getModuleName());
            List fieldList = getFields();
            moduleFieldsList.add(fieldList);
        }else{
            this.bean.setServiceActionConstant(ServiceActionConstants.GET_MODULES);
            setCommonDetails();
            setAccessToken();
            setAPIURL();
            List<List> modulesList = (List)sendRequest();
            moduleFieldsList = new ArrayList();
            int i = 0;
            for(List moduleList : modulesList) {
                Map<String, Object> moduleFields = new LinkedHashMap<>();
                if(i==0){
                    this.bean.setModuleName((String)moduleList.get(0));
                    List fieldList = getFields();
                    moduleFields.put("field", fieldList);
                }
                moduleFields.put("module", moduleList);
                moduleFieldsList.add(moduleFields);
                i++;
            }
        }
        JSONObjectWrapper resp = new JSONObjectWrapper();
        resp.put("selectDataContent", moduleFieldsList);
        return resp;

    }

    public List getFields() throws DataConnection.DataConnectionFailedException {
        this.setCommonDetails();
        this.setAccessToken();
        ArrayList URLParam = new ArrayList();
        URLParam.add(this.bean.getModuleName());
        this.bean.setDynamicURLParam(URLParam);

        this.bean.setServiceActionConstant(ServiceActionConstants.GET_FIELDS);
        setAPIURL();
        setCommonDetails();
        return (List)sendRequest();
    }

    @Override
    public void setPreRequestProcess() {
        // TODO Auto-generated method stub
        List URLParam = this.bean.getDynamicURLParam();
        if(URLParam == null) {
            URLParam = new ArrayList();
            URLParam.add(this.bean.getModuleName());
            this.bean.setDynamicURLParam(URLParam);

        }else{
            URLParam.add(this.bean.getModuleName());
        }

        StringBuilder queryStr = new StringBuilder();
        switch(this.bean.getServiceActionConstant()) {
            case ServiceActionConstants.FETCH_DATA:
                if(this.bean.getCriteriaList().length()>0) {
                    for(int i=0;i<this.bean.getCriteriaList().length();i++){
                        JSONArrayWrapper criteria = this.bean.getCriteriaList().getJSONArray(i);
                        int logicOptr = criteria.getInt(0);
                        String fieldLinkName = Utility.getDecodedString(criteria.getString(1));
                        int fieldTypeId = criteria.getInt(2);
                        int operatorID = criteria.getInt(3);
                        String value1 = criteria.getString(4);
//						String value2 = criteria.getString(5);

                        try {
                            if(logicOptr == 0)
                            {
                                queryStr.append(" and ");
                            } else if (logicOptr == 1) {
                                queryStr.append(" or ");
                            }
                            queryStr.append("(");
                            queryStr.append(fieldLinkName).append(":").append(((ServiceInfoImpl)getServiceInfo()).getFilterOperatorString(ConstantHolder.FilterOperator.getFilterOperator(operatorID))).append(":");
                            if(!"null".equals(value1))
                            {
                                if(fieldTypeId == ConstantHolder.FilterType.TEXT.getTypeId())
                                {
                                    value1 = "\""+value1+"\"";
                                }
                                else if(fieldTypeId == ConstantHolder.FilterType.DATE.getTypeId())
                                {
                                    value1 = ExternalDataUtils.getFormatedDate(Long.parseLong(value1),"yyyy-MM-dd'T'HH:mm:ss'+0000'"); //No I18N
                                    value1 = "\""+value1+"\"";
                                }
                                queryStr.append(value1).append(")");
                            }
                        }
                        catch (Exception e)
                        {
                            logger.log(Level.INFO, "Exception Occurred in Criteria", e);
                        }

                    }
                    if(!queryStr.toString().isEmpty()){
                        this.bean.setCriteriaString(Utility.getEncodedString(queryStr.toString()));
                    }
                }
                break;
        }
    }

    public String setPreciseQueryParam(String  url, int pageNo) {
        switch (this.bean.getServiceActionConstant()) {
            case ServiceActionConstants.FETCH_DATA:
                if(this.bean.getCriteriaList().length()>0) {
                    url = url + "/search?page=" + pageNo;
                    url = url + "&criteria="+ this.bean.getCriteriaString();
                }
                else
                {
                    url = url + "?page=" + pageNo;
                }
                break;
            default:
                break;

        }
        return url;
    }

    @Override
    public Object fetchServiceContent() throws DataConnection.DataConnectionFailedException {
        this.setPreRequestProcess();
        setAccessToken();

        setAPIURL();
        String urlStr = this.bean.getRequestURL();
        int page_no = 1;
        List resultArr = new ArrayList();

        String [] fieldHeaders = bean.getFieldHeaders();

        resultArr.add(String.join(",", fieldHeaders));

        has_more_page = true;
        while(has_more_page && page_no * 200 <= MAX_RECORD_LIMIT){
            String reqURL = setPreciseQueryParam(urlStr, page_no);
            this.bean.setRequestURL(reqURL);

            List THR_Content = (List<String>)sendRequest();
            if(THR_Content !=null && !THR_Content.isEmpty()){
                resultArr.addAll(THR_Content);
            }
            else
            {
                has_more_page = false;
            }
            page_no++;
        }
        this.bean.setMaxRowReached(has_more_page);
        this.bean.setRowCount(resultArr.size());
        this.bean.setColCount(fieldHeaders.length);

        return resultArr;
    }

    @Override
    public String parseCode(String resp) {
        return new JSONObjectWrapper(resp).optString("code");//NO I18N
    }

    public List<String> getOrg() throws Exception {
        this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG);
        if(this.bean.getConnectionID() <= 0){
            setCommonDetails();
        }

        setAccessToken();
        setAPIURL();
        return (List)sendRequest();
    }
    public List<String> getOrgList() throws Exception {
        this.bean.setServiceActionConstant(ServiceActionConstants.GET_ORG_LIST);
        this.setCommonDetails();
        setAccessToken();
        setAPIURL();
        return (List)sendRequest();
    }
}
