//$Id$
package com.zoho.sheet.action.externaldata.core.parser;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR> on 23/03/21
 */
public class J<PERSON>NParser extends ContentParser{


    @Override
    public Object getParsedData(List<String> fieldNames, InputStream inputFS, boolean includeFirstRow) {
        return null;
    }

    @Override
    public Object getParsedData(byte[] content, boolean includeFirstRow) {
        return null;
    }

    public interface ParseFieldContent<R extends JSONObjectWrapper>{
        String parse(R resp);
    }
}
