//$Id$
package com.zoho.sheet.action.externaldata.core.parser;

import com.zoho.sheet.action.externaldata.ExternalDataUtils;

/**
 * <AUTHOR> on 23/03/21
 */
public class DCParserFactory {
    public static ContentParser getContentParser(ExternalDataUtils.FTYPE ftype) {
        if (ExternalDataUtils.FTYPE.CSV == ftype) {
            return new CSVParser();
        } else if (ExternalDataUtils.FTYPE.JSON == ftype){
            return new JSONParser();
        }
        return null;
    }
}
