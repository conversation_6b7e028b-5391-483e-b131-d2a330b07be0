//$Id$
package com.zoho.sheet.action.externaldata.core.parser;

import com.adventnet.zoho.websheet.model.util.CSVReader;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.util.ClientUtils;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR> on 23/03/21
 */
public class CSVParser extends ContentParser{

    private static final char COMMA = ',';
    private static final char QUOTE = '\"';

    public static Map<String, String> getParsedDataMap(InputStream inputFS) {
        BufferedReader br = new BufferedReader(new InputStreamReader(inputFS));
        Map<String, String> csvMap = new LinkedHashMap<>();
        CSVReader reader = new CSVReader(br,COMMA,QUOTE,true,true);

        while(true) {
            try {
                Vector<String> lineData = reader.getAllFieldsInLine();
                csvMap.put(lineData.get(0), lineData.stream()
                        .skip(1)// Skips the first element
                        .map(val -> QUOTE + val + QUOTE)
                        .collect(Collectors.joining(String.valueOf(COMMA))));
            } catch (java.io.EOFException eof) {
                break;
            } catch (java.io.IOException io) {
                try {
                    reader.close();
                } catch (IOException ignored) {}
                break;
            }
        }
        return csvMap;
    }


    public List <String> getParsedData(byte[] content, boolean includeFirstRow) {

        List<String> inputList = new ArrayList<>();
        InputStream inputFS = new ByteArrayInputStream(content);
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputFS))) {
            String line = reader.readLine().replace("\uFEFF", "");
            if(includeFirstRow){
                inputList.add(line);
            }
            while ((line = reader.readLine()) != null) {
                inputList.add(line);
            }
        } catch (IOException ignored) {
        }

        return inputList;
    }

        public Object getParsedData(List<String> fieldNames, InputStream inputFS, boolean includeFirstRow) {
            BufferedReader br = new BufferedReader(new InputStreamReader(inputFS));
            CSVReader reader = new CSVReader(br,COMMA,QUOTE,true,true);

            boolean parsingHeader = true;
            List<Integer> fetchIndexes = List.of();
            List<String> result = new LinkedList<>();

            if (includeFirstRow) {
                result.add(String.join(String.valueOf(COMMA), fieldNames));
            }
            while (true) {
                try {
                    Vector<String> lineData = reader.getAllFieldsInLine();

                    // Parse header and fetch the indices of relevant fields
                    if (parsingHeader) {
                        fetchIndexes = IntStream.range(0, lineData.size())
                                .filter(i -> fieldNames.stream()
                                        .anyMatch(fn -> Utility.getDecodedString(fn).equals(lineData.get(i))))
                                .boxed()
                                .collect(Collectors.toList());
                        parsingHeader = false; // Header processed, continue to next line
                        continue;
                    }

                    // Build the string for selected data fields
                    String selectedData = fetchIndexes.stream()
                            .map(i -> QUOTE + lineData.get(i) + QUOTE)
                            .collect(Collectors.joining(","));
                    result.add(selectedData);

                } catch (java.io.EOFException eof) {
                    break; // End of file reached
                } catch (java.io.IOException io) {
                    // Ensure reader is closed on IOException
                    try {
                        reader.close();
                    } catch (IOException ignored) {
                        // Ignored as there's nothing more we can do here
                    }
                    break;
                }
            }
            return result;
    }
//    public List <String> getParsedData(ArrayList<String> fieldLinkNames, ArrayList<String> fieldHeaders, byte[] content, boolean includeFirstRow) {
//
//        List <String> inputList = new ArrayList<String> ();
//        try{
//           // File inputF = new File(inputFilePath);
//            InputStream inputFS = new ByteArrayInputStream(content);
//            BufferedReader br = new BufferedReader(new InputStreamReader(inputFS));
//
//
//            //fetchIndex.add(6);
//
//            // skip the header of the csv
//            /*(List<List>) br.lines().skip(1).map(mapToArray).map(f -> {
//                return IntStream.range(0, f.length)
//                        .filter(i-> fetchIndex.contains(i))
//                        .mapToObj(i-> f[i]);
//
//
//
//            });*/
//            List<String>  columns = br.lines().findFirst()
//                    .map((line) -> Arrays.asList(line.replaceAll("\uFEFF", "").split(",")))
//                    .get();
//            List<Integer> fetchIndexes = IntStream.range(0, columns.size()).filter(i-> fieldLinkNames.contains(columns.get(i))).boxed().collect(Collectors.toList());
//
//
//            inputList = br.lines().map(mapToArray)
//                    .map(f -> IntStream.range(0, f.length)
//                    .filter(fetchIndexes::contains)
//                    .mapToObj(i-> f[i])
//                    .collect(Collectors.joining(","))).collect(Collectors.toList());
//            if(inputList.size() > ConstantHolder.MAX_RECORD_LIMIT)
//            {
//                inputList = inputList.subList(inputList.size() - ConstantHolder.MAX_RECORD_LIMIT, inputList.size());
//            }
//            if(includeFirstRow){
//                inputList.add(0, String.join(",", fieldHeaders));
//            }
//
//            //Arrays.stream(f).toArray()[i]
//            //br.lines().skip(1).map(line -> Arrays.asList(line.split(","))).collect(Collectors.toList());
//            //inputList = br.lines().skip(1).map.(mapToPerson).collect(Collectors.toList());
//
//            br.close();
//        } catch (IOException e) {
//
//        }
//
//        return inputList ;
//    }
//
//    public static Function<String, String[]> mapToArray = (lines) -> {
//        if(lines.contains("\"")){
//            String[] line = lines.split("");
//            ArrayList<String> newArrayList = new ArrayList<String>();
//            String subString = "";
//            for(int i=0;i<line.length;i++){
//                if(line[i].equals("\"")){
//                    for(int j=i; j<line.length; j++){
//                        subString  += line[j];
//                        if(j!=i && line[j].equals("\"")) {
//                            i=j;
//                            if (i == line.length - 1) {
//                                newArrayList.add(subString);
//                            }
//                            break;
//                        }
//
//                    }
//                    // subString  += line[i];
//                }else if(line[i].equals(",")){
//                    newArrayList.add(subString);
//                    subString = "";
//
//                }else {
//                    subString += line[i];
//                    if (i == line.length - 1) {
//                        newArrayList.add(subString);
//                    }
//                }
//            }
//            //newArrayList.stream().forEach(f-> );
//           /* for(int fetchIndex : fetchIndexes){
//
//            }*/
//            return newArrayList.toArray(new String[0]);
//        }else{
//            String[] line = lines.split(COMMA);
//            return line;
//        }
//
//    };
}
