//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.core.objprovider;

import com.zoho.sheet.action.externaldata.constants.ServiceConstant;
import com.zoho.sheet.action.externaldata.core.CloudService;
import com.zoho.sheet.action.externaldata.core.extend.app.*;
import com.zoho.sheet.action.externaldata.core.extend.humanresources.ZohoPeople;


/**
 * <AUTHOR>
 *
 */
public class AppFactory extends ServiceObjectFactory {
	public AppFactory(int serviceConstant) {
		super(serviceConstant);
	}
	@Override
	
	CloudService getService() {
		CloudService service = null;
		switch(serviceConstant) {
			case ServiceConstant.UNAUTH_CSV://APP
				service = new UnAuthCSVHost();
				break;
			case ServiceConstant.UNAUTH_HTML://APP
				service = new UnAuthHTMLHost();
				break;
			case ServiceConstant.UNAUTH_RSS://APP
				service = new UnAuthRSSHost();
				break;
			case ServiceConstant.ZCRM://APP
				service = new ZohoCRM();
				break;
			case ServiceConstant.ZBOOKS://APP
				service = new ZohoBooks();
				break;
			case ServiceConstant.ZCREATOR://APP
				service = new ZohoCreator();
				break;
			case ServiceConstant.QUICKBOOKS://APP
				service = new QuickBooks();
				break;
			case ServiceConstant.SALESFORCE://APP
				service = new SalesForce();
				break;
			case ServiceConstant.FRESHBOOKS:
				service = new FreshBooks();
				break;
			case ServiceConstant.ZCAMPAIGNS:
				service = new ZohoCampaigns();
				break;
			case ServiceConstant.ZDESK:
				service = new ZohoDesk();
				break;
			case ServiceConstant.ZBIGIN:
				service = new ZohoBigin();
				break;
			case ServiceConstant.ZEXPENSE:
				service = new ZohoExpense();
				break;
			case ServiceConstant.ZINVENTORY:
				service = new ZohoInventory();
				break;
			case ServiceConstant.ZBILLING:
				service = new ZohoBilling();
				break;
			case ServiceConstant.ZPROJECTS:
				service = new ZohoProjects();
				break;
			case ServiceConstant.ZBUGTRACKER:
				service = new ZohoBugTracker();
				break;
			case ServiceConstant.ZSPRINTS:
				service = new ZohoSprints();
				break;
			case ServiceConstant.ZINVOICE:
				service = new ZohoInvoice();
				break;
			case ServiceConstant.ZCOMMERCE:
				service = new ZohoCommerce();
				break;
			case ServiceConstant.ZRECRUIT:
				service = new ZohoRecruit();
				break;
			case ServiceConstant.ZPEOPLE:
				service = new ZohoPeople();
				break;
			case ServiceConstant.ZSALESIQ:
				service = new ZohoSalesIQ();
				break;
			case ServiceConstant.ZBACKSTAGE:
				service = new ZohoBackstage();
				break;

		}
		return service;
	}
}
