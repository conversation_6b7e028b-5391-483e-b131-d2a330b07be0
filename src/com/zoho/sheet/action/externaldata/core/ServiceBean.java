//$Id$
/**
 * 
 */
package com.zoho.sheet.action.externaldata.core;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * <AUTHOR>
 *
 */
public class ServiceBean {
	
	private long serviceID;
	private long connectionID;

	private int serviceConstant = -1;
	private int serviceActionConstant = -1;
	private String  urlType;
	private String requestURL;
	private String method;
	private String service_OrgID;
	private String databaseName;
	private String moduleName;
	private JSONArrayWrapper criteriaList;


	private String criteriaString;
	private int limit;
	private int offset;
	private long lastFetchedTime = -1;
	private List dynamicURLParam;
	private JSONArrayWrapper fieldList;
	private String[] fieldHeaders;
	private String[] fieldLinkNames;



	private Map htmlTableList;
	private JSONArrayWrapper delimiters;
	private Map htmlTableRowCountList;

	private long flowOrgID;
	private String accessToken;
	private String accessTokenParamName;
	private String accessTokenType;
	private Map rTokenMap;
//	private String applicationOwnerName;

	private String organizationID;



	private String fetchOwner;
	private String ZUID;
	private String emailId;




	private int rowCount;
	private int colCount;

	private boolean isMaxRowReached;


	private HashMap fetchBatchMap;



	private JSONObjectWrapper serviceSpecificParam;



	private String connectionUniqueKey;

	private boolean isPreview;

	private JSONObjectWrapper colFormats;



//	public String getApplicationOwnerName() {
//		return applicationOwnerName;
//	}
//
//	public void setApplicationOwnerName(String applicationOwnerName) {
//		this.applicationOwnerName = applicationOwnerName;
//	}

	/**
	 * @return the refreshToken
	 */
	public String getAccessToken() {
		return accessToken;
	}

	/**
	 * @param accessToken the refreshToken to set
	 */
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	/**
	 * @return the refreshTokenParamName
	 */
	public String getAccessTokenParamName() {
		return accessTokenParamName;
	}

	/**
	 * @param accessTokenParamName the refreshTokenParamName to set
	 */
	public void setAccessTokenParamName(String accessTokenParamName) {
		this.accessTokenParamName = accessTokenParamName;
	}

	/**
	 * @return the refreshTokenType
	 */
	public String getAccessTokenType() {
		return accessTokenType;
	}

	/**
	 * @param accessTokenType the refreshTokenType to set
	 */
	public void setAccessTokenType(String accessTokenType) {
		this.accessTokenType = accessTokenType;
	}

	/**
	 * @return the rTokenMap
	 */
	public Map getrTokenMap() {
		return rTokenMap;
	}

	/**
	 * @param rTokenMap the rTokenMap to set
	 */
	public void setrTokenMap(Map rTokenMap) {
		this.rTokenMap = rTokenMap;
	}

	public String getURLType() {
		return urlType;
	}

	public void setURLType(String urlType) {
		this.urlType = urlType;
	}

	/**
	 * @return the serviceActionConstant
	 */
	public int getServiceActionConstant() {
		return serviceActionConstant;
	}

	/**
	 * @param serviceActionConstant the serviceActionConstant to set
	 */
	public void setServiceActionConstant(int serviceActionConstant) {
		this.serviceActionConstant = serviceActionConstant;
	}

	/**
	 * @return the requestURL
	 */
	public String getRequestURL() {
		return requestURL;
	}

	/**
	 * @param requestURL the requestURL to set
	 */
	public void setRequestURL(String requestURL) {
		this.requestURL = requestURL;
	}

	/**
	 * @return the method
	 */
	public String getMethod() {
		return method;
	}

	/**
	 * @param method the method to set
	 */
	public void setMethod(String method) {
		this.method = method;
	}


	public int getServiceConstant() {
		return serviceConstant;
	}


	public void setServiceConstant(int serviceConstant) {
		this.serviceConstant = serviceConstant;
	}
	
	/**
	 * @return the connectionID
	 */
	public long getConnectionID() {
		return connectionID;
	}

	/**
	 * @param connectionID the connectionID to set
	 */
	public void setConnectionID(long connectionID) {
		this.connectionID = connectionID;
	}

	/**
	 * @return the serviceID
	 */
	public long getServiceID() {
		return serviceID;
	}

	/**
	 * @param serviceID the serviceID to set
	 */
	public void setServiceID(long serviceID) {
		this.serviceID = serviceID;
	}

	public String getService_OrgID() {
		return service_OrgID;
	}

	public void setService_OrgID(String service_OrgID) {
		this.service_OrgID = service_OrgID;
	}
	/**
	 * @return the fieldList
	 */
	public JSONArrayWrapper getFieldList() {
		return fieldList;
	}

	/**
	 * @param fieldList the fieldList to set
	 */
	public void setFieldList(JSONArrayWrapper fieldList) {
		if(fieldList.isEmpty())
		{
			return;
		}
		this.fieldList = fieldList;
		String[] fieldHeaderRow = new String[fieldList.length()];
		String[] fieldLinkNames = new String[fieldList.length()];
		for (int j = 0; j < fieldList.length(); j++){
			String fieldLinkName = Utility.getDecodedString(fieldList.getJSONArray(j).getString(0));
			String field = fieldList.getJSONArray(j).getString(1);
			fieldHeaderRow[j] = field;
			fieldLinkNames[j] = fieldLinkName;
		}
		this.fieldHeaders = fieldHeaderRow;
		this.fieldLinkNames = fieldLinkNames;
	}

	/**
	 * @return the queryParam
	 */
	public List getDynamicURLParam() {
		return dynamicURLParam;
	}


	public void setDynamicURLParam(List dynamicURLParam) {
		this.dynamicURLParam = dynamicURLParam;
	}


	public String[] getFieldHeaders() {
		return fieldHeaders;
	}

	/**
	 * @return the fieldLinkNames
	 */
	public String[] getFieldLinkNames() {
		return fieldLinkNames;
	}

	public JSONArrayWrapper getDelimiters() {
		return delimiters;
	}

	public void setDelimiters(JSONArrayWrapper delimiters) {
		this.delimiters = delimiters;
	}

	public Map getHtmlTableList() {
		return htmlTableList;
	}

	public void setHtmlTableList(Map htmlTableList) {
		this.htmlTableList = htmlTableList;
	}

	public Map getHtmlTableRowCountList() {
		return htmlTableRowCountList;
	}

	public void setHtmlTableRowCountList(Map htmlTableRowCountList) {
		this.htmlTableRowCountList = htmlTableRowCountList;
	}

	/**
	 * @return the flowOrgID
	 */
	public long getFlowOrgID() {
		return flowOrgID;
	}

	/**
	 * @param flowOrgID the flowOrgID to set
	 */
	public void setFlowOrgID(long flowOrgID) {
		this.flowOrgID = flowOrgID;
	}

	/**
	 * @return the moduleName
	 */
	public String getModuleName() {
		return moduleName;
	}

	/**
	 * @param moduleName the moduleName to set
	 */
	public void setModuleName(String moduleName) {
		this.moduleName = Utility.getDecodedString(moduleName);
	}

	/**
	 * @return the databaseName
	 */
	public String getDatabaseName() {
		return databaseName;
	}

	/**
	 * @param databaseName the databaseName to set
	 */
	public void setDatabaseName(String databaseName) {
		this.databaseName = Utility.getDecodedString(databaseName);
	}

	/**
	 * @return the criteria
	 */
	public JSONArrayWrapper getCriteriaList() {
		return criteriaList;
	}

	/**
	 * @param criteriaList the criteria to set
	 */
	public void setCriteriaList(JSONArrayWrapper criteriaList) {
		this.criteriaList = criteriaList;
	}


	public String getCriteriaString() {
		return criteriaString;
	}

	public void setCriteriaString(String criteriaString) {
		this.criteriaString = criteriaString;
	}

	public int getOffset() {
		return offset;
	}

	public void setOffset(int offset) {
		this.offset = offset;
	}

	/**
	 * @return the limit
	 */
	public int getLimit() {
		return limit;
	}

	/**
	 * @param limit the limit to set
	 */
	public void setLimit(int limit) {
		this.limit = limit;
	}

	/**
	 * @return the organizationID
	 */
	public String getOrganizationID() {
		return organizationID;
	}

	/**
	 * @param organizationID the organizationID to set
	 */
	public void setOrganizationID(String organizationID) {
		this.organizationID = organizationID;
	}

	public String getFetchOwner() {
		return fetchOwner;
	}

	public void setFetchOwner(String fetchOwner) {
		this.fetchOwner = fetchOwner;
	}
	/**
	 * @return the zUID
	 */
	public String getZUID() {
		return ZUID;
	}

	/**
	 * @param zUID the zUID to set
	 */
	public void setZUID(String zUID) {
		ZUID = zUID;
	}

	/**
	 * @return the emailId
	 */
	public String getEmailId() {
		return emailId;
	}

	/**
	 * @param emailId the emailId to set
	 */
	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public int getRowCount() {
		return rowCount;
	}

	public void setRowCount(int rowCount) {
		this.rowCount = rowCount;
	}
	public int getColCount() {
		return colCount;
	}

	public void setColCount(int colCount) {
		this.colCount = colCount;
	}

	public boolean isMaxRowReached() {
		return isMaxRowReached;
	}

	public void setMaxRowReached(boolean maxRowReached) {
		isMaxRowReached = maxRowReached;
	}

	public long getLastFetchedTime() {
		return lastFetchedTime;
	}

	public void setLastFetchedTime(long lastFetchedTime) {
		this.lastFetchedTime = lastFetchedTime;
	}

	public boolean isFetchModifiedRecords()
	{
		return lastFetchedTime != -1;
	}

	public HashMap getFetchBatchMap() {
		return fetchBatchMap;
	}

	public void setFetchBatchMap(HashMap fetchBatchMap) {
		this.fetchBatchMap = fetchBatchMap;
	}

	public JSONObjectWrapper getServiceSpecificParam() {
		return serviceSpecificParam;
	}

	public void setServiceSpecificParam(JSONObjectWrapper serviceSpecificParam) {
		this.serviceSpecificParam = serviceSpecificParam;
	}
	public String getConnectionUniqueKey() {
		return connectionUniqueKey;
	}

	public void setConnectionUniqueKey(String connectionUniqueKey) {
		this.connectionUniqueKey = connectionUniqueKey;
	}

	public boolean isPreview() {
		return isPreview;
	}

	public void setPreview(boolean preview) {
		isPreview = preview;
	}

	public JSONObjectWrapper getColFormats() {
		return colFormats;
	}

	public void setColFormats(JSONObjectWrapper colFormats) {
		this.colFormats = colFormats;
	}
}
