package com.zoho.sheet.action.externaldata.connectionHelper;

import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.MessagePropagator;
import com.zoho.sheet.action.externaldata.actionmanager.FlowActionManager;
import com.zoho.sheet.action.externaldata.metastorage.DBActionManager;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.zfsng.client.ZohoFS;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FlowConnectionResponseHandler extends StrutsRequestHandler {

    private static final Logger LOGGER = Logger.getLogger(FlowActionManager.class.getName());

    /**
     * Handles the OAuth connection response and updates the connected service.
     * Expected response:
     * '{"status_message":"We have successfully handled the oauth","code":0,"credential_id":"5581000000625001","status":true,"type":"connectionHandler"}'
     * NOTE : credential_id and connectionID are not same here
     */
    @Override
    public String execute() {
        String zuid = request.getParameter(JSONConstants.ZUID);
        String rID = request.getParameter(JSONConstants.RID);
        String rsID = request.getParameter(JSONConstants.RSID);
        String connectionID = request.getParameter(JSONConstants.CONNECTION_ID);

        LOGGER.log(Level.INFO, "[Data Connection] FlowConnectionResponseHandler called for RID: {0}, ConnectionID: {1}", new Object[]{rID, connectionID});

        try {
            String zsoid = DocumentUtils.getDocOwnerFromRid(rID);
            String colabID = ZohoFS.getCollabId(zsoid, rID);

            String flowResponse = readRequestBody();
            LOGGER.log(Level.INFO, "[Data Connection] Flow connection authentication response: {0}", flowResponse);

            JSONObjectWrapper respObj = new JSONObjectWrapper(flowResponse);

            if (respObj.optBoolean("status")) {
                String connectionOwner = DocumentUtils.getZUserName(zuid);
                long flowOrgID = DBActionManager.getFlowOrgID_FromDB(zsoid, connectionOwner, zuid);
                Row connectionRow = DBActionManager.getConnection(connectionOwner, zuid, flowOrgID, connectionID);

                respObj = FlowActionManager.updateConnectedService(
                        connectionRow.getInt("SERVICE_CONSTANT"),
                        connectionOwner, zuid, connectionID, flowOrgID, zsoid
                );
            }

            if (rsID != null) {
                MessagePropagator.sendDataConnection_ConnectionResponse(colabID, rsID, zuid, respObj.toString());
            } else {
                LOGGER.log(Level.WARNING, "[Data Connection] Session ID is null. RID: {0}, ConnectionID: {1}", new Object[]{rID, connectionID});
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[Data Connection] Error processing connection response", e);
        }

        return null;
    }

    /**
     * Reads the entire request body into a String.
     */
    private String readRequestBody() {
        StringBuilder body = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Failed to read request body", e);
        }
        return body.toString();
    }
}
