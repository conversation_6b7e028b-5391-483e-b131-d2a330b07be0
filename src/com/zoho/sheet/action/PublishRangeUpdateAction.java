/* $Id$ */
package com.zoho.sheet.action;

import java.io.PrintWriter;
import java.text.SimpleDateFormat;

import javax.servlet.http.*;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.Table;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.ds.query.SelectQuery;
import com.adventnet.persistence.DataObject;
import com.zoho.sheet.util.*;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.zfsng.constants.ZFSNGConstants;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.logging.Logger;
import java.util.Date;
import java.util.logging.Level;

public class PublishRangeUpdateAction extends StrutsRequestHandler {

    public static Logger logger = Logger.getLogger(PublishRangeUpdateAction.class.getName());

    public String execute()
            throws Exception {
        PrintWriter out = response.getWriter();
        org.json.JSONObject previlages = new org.json.JSONObject(((String) request.getAttribute(ZFSNGConstants.CAPABILITIES)));
        
        boolean canPublish = previlages.getBoolean("canPublish");
        boolean canOrgPublish = previlages.getBoolean("canOrgPublish");

        if(!canOrgPublish && !canPublish){
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return null;
        }
        
        try {

            WorkbookContainer container = CurrentRealm.getContainer();
            String doc_id = container.getDocId();
            String docOwner = container.getDocOwner();

            String id = request.getParameter("id");
            String type = request.getParameter("mode");
            String sheetName = request.getParameter("sheetName");
            String str_scol = request.getParameter("startCol");
            String str_srow = request.getParameter("startRow");
            String str_ecol = request.getParameter("endCol");
            String str_erow = request.getParameter("endRow");
            String from = request.getParameter("from");
            String to = request.getParameter("to");
            String pubType = request.getParameter("type");

            Long sheetId = null;
            Persistence pers = null;

            // Code for Org Support
            long zoid = DocumentUtils.getZOID();

            switch (type) {
                case "Remove" :
                case "Update" :
                    if (zoid > -1 && "org".equals(from)) {
                        if (!canOrgPublish) {
                            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                            return null;
                        }
                        pers = SheetPersistenceUtils.getPersistence(Constants.CORPORATEDBPREFIX + zoid);
                    }
                    else {
                        if (!canPublish) {
                            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                            return null;
                        }
                        pers = SheetPersistenceUtils.getPersistence("Public");//No I18N
                    }
                    RedisHelper.del(RedisHelper.PUBLISHED_RANGES + id);
                    break;
                case "ChangeOptions" :
                    if (zoid > -1 && "org".equals(pubType)) {
                        if (!canOrgPublish) {
                            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                            return null;
                        }
                        pers = SheetPersistenceUtils.getPersistence(Constants.CORPORATEDBPREFIX + zoid);
                    }
                    else {
                        if (!canPublish) {
                            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                            return null;
                        }
                        pers = SheetPersistenceUtils.getPersistence("Public");//No I18N
                    }
                    break;
                case "ChangeType" :
                    if (zoid > -1 && "org".equals(from)) {
                        if ((!canPublish)) {
                            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                            return null;
                        }
                        pers = SheetPersistenceUtils.getPersistence(Constants.CORPORATEDBPREFIX + zoid);
                    }
                    else {
                        if (!canOrgPublish) {
                            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                            return null;
                        }
                        pers = SheetPersistenceUtils.getPersistence("Public");//No I18N
                    }
                    RedisHelper.del(RedisHelper.PUBLISHED_RANGES + id);
                    break;
            }

            DataObject dobj = null;
            //  Persistence pers1 = SheetPersistenceUtils.getPersistence();
            SelectQuery sql = new SelectQueryImpl(new Table("RangePublish"));
            sql.addSelectColumn(new Column(null, "*"));
            Criteria cri = new Criteria(new Column("RangePublish", "ENCRYPTED_URL"), id, QueryConstants.EQUAL);
            Criteria cri1 = new Criteria(new Column("RangePublish", "DOCUMENT_ID"), doc_id, QueryConstants.EQUAL);
            sql.setCriteria(cri1);
            
            if(type != null && type.equals("bulkremove")) {
            	String removeIDStr = request.getParameter("rangeIDList");

                JSONArrayWrapper removeIDList = new JSONArrayWrapper(removeIDStr);

            	Persistence pers1 = null, pers2 = null;
            	pers1 = SheetPersistenceUtils.getPersistence(Constants.CORPORATEDBPREFIX + zoid);
            	pers2 = SheetPersistenceUtils.getPersistence("Public");//No I18N
            	int len = removeIDList.length();
            	for(int i = 0; i < len; i++) {
            		if("org".equals(removeIDList.getJSONObject(i).getString("type"))) {
            			id = removeIDList.getJSONObject(i).getString("id"); 
            			cri = new Criteria(new Column("RangePublish", "ENCRYPTED_URL"), id, QueryConstants.EQUAL);
            			pers1.delete(cri);
            		}
            		else {
            			id = removeIDList.getJSONObject(i).getString("id"); 
            			cri = new Criteria(new Column("RangePublish", "ENCRYPTED_URL"), id, QueryConstants.EQUAL);
            			pers2.delete(cri);
            		}
            	}
            }
            else if (type != null && type.equals("Remove")) {
                pers.delete(cri);
                dobj = pers.get(sql);
                int cnt = dobj.size("RangePublish");
                out.print(cnt);

            } else if (type != null && type.equals("Update")) {
                String sheetID = GraphUtils.getDocumentSheetID(Long.parseLong(doc_id), sheetName,docOwner, true);
                if (sheetID != null) {
                    sheetId = Long.valueOf(sheetID);
                }

                long scol = -1;
                long srow = -1;
                long ecol = -1;
                long erow = -1;
                if (str_scol != null) {
                    scol = Long.parseLong(str_scol);
                }
                if (str_srow != null) {
                    srow = Long.parseLong(str_srow);
                }
                if (str_ecol != null) {
                    ecol = Long.parseLong(str_ecol);
                }
                if (str_erow != null) {
                    erow = Long.parseLong(str_erow);
                }

                sql.setCriteria(cri);
                dobj = pers.get(sql);
                if (!dobj.isEmpty()) {
                    Row row = dobj.getFirstRow("RangePublish");//No I18N
                    row.set("RANGE_SROW", srow);
                    row.set("RANGE_SCOL", scol);
                    row.set("RANGE_EROW", erow);
                    row.set("RANGE_ECOL", ecol);
                    row.set("SHEET_ID", sheetId);
                    dobj.updateRow(row);
                    pers.update(dobj);
                }
            } else if("ChangeOptions".equals(type)) {
                sql.setCriteria(cri);
                dobj = pers.get(sql);
                if (!dobj.isEmpty()) {
                    Row row = dobj.getFirstRow("RangePublish");//No I18N
                    String addInfo = (String)row.get("ADDITIONAL_INFO");
                    JSONObjectWrapper json;
                    if("".equals(addInfo)) {
                    		json = new JSONObjectWrapper();
                    } else {
                    		json = new JSONObjectWrapper((String)row.get("ADDITIONAL_INFO"));
                    }
                    String propName = request.getParameter("propertyName");
                    String propValue = request.getParameter("propertyValue");
                    json.put(propName, propValue);
                    row.set("ADDITIONAL_INFO", json.toString());
                    dobj.updateRow(row);
                    pers.update(dobj);
                }
            } else if (type != null && type.equals("ChangeType")) {

                sql.setCriteria(cri);
                dobj = pers.get(sql);
                
                Row row = dobj.getFirstRow("RangePublish");//No I18N
                //get 
                Long shareID = (Long) row.get("SHARE_ID");
                String shareOwner = (String) row.get("SHARE_OWNER");
                Long zuid = (Long) row.get("AUTHOR_ZUID");
                Long sheetID = (Long) row.get("SHEET_ID");
                Long sCol = (Long) row.get("RANGE_SCOL");
                Long sRow = (Long) row.get("RANGE_SROW");
                Long eCol = (Long) row.get("RANGE_ECOL");
                Long eRow = (Long) row.get("RANGE_EROW");
                String addInfo = (String)row.get("ADDITIONAL_INFO");
                JSONObjectWrapper json;
                if("".equals(addInfo)) {
                		json = new JSONObjectWrapper();
                } else {
                		json = new JSONObjectWrapper((String)row.get("ADDITIONAL_INFO"));
                }
                
                String encryptURL = (String) row.get("ENCRYPTED_URL");
                Long documentID = (Long) row.get("DOCUMENT_ID");
                

                Persistence pers1 = null;
                if (zoid > -1 && "org".equals(to)) {   
                    pers1 = SheetPersistenceUtils.getPersistence(Constants.CORPORATEDBPREFIX + zoid);
                } else {
                    pers1 = SheetPersistenceUtils.getPersistence("Public");//No I18N
				}

                //update
                DataObject dobj1 = pers1.constructDataObject();
                Row row1 = new Row("RangePublish");
                
                Long date = System.currentTimeMillis();
                row1.set("SHARE_OWNER", shareOwner);
                row1.set("AUTHOR_ZUID", zuid);
                row1.set("DOCUMENT_ID", documentID);
                row1.set("SHEET_ID", sheetID);
                row1.set("PUBLISHED_DATE", date);
                row1.set("ENCRYPTED_URL", encryptURL);
                row1.set("RANGE_SROW", sRow);
                row1.set("RANGE_SCOL", sCol);
                row1.set("RANGE_EROW", eRow);
                row1.set("RANGE_ECOL", eCol);
                row1.set("ADDITIONAL_INFO", json.toString());

                dobj1.addRow(row1);
                pers1.add(dobj1);

                //remove
                pers.delete(cri);
                
                SimpleDateFormat sf = new SimpleDateFormat("dd MMM yyyy");
                Date date1 = new Date(date);
                String publishDate=sf.format(date1);
                JSONObjectWrapper respObj = new JSONObjectWrapper();
                respObj.put("Publish_Date", publishDate);
                HttpServletResponseWrapper.sendResponse(response, respObj, HttpServletResponseWrapper.MimeType.JSON);
            }

        } catch (Exception e) {
            logger.log(Level.WARNING, null, e);
        }
        return null;
    }
}
