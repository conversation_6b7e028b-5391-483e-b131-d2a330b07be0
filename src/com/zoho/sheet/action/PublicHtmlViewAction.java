//$Id$
package com.zoho.sheet.action;

import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletRequest;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.DocumentRenderPolicy;
import com.zoho.sheet.util.WidgetReader;
import com.zoho.sheet.util.WidgetReaderFactory;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ShareConstants;

public class PublicHtmlViewAction extends DocumentRenderer {
	public static Logger logger = Logger.getLogger(PublicHtmlViewAction.class.getName()); 
        @Override
        protected WidgetReader getWidgetReader(DocumentRenderPolicy policy) throws Exception {
                
		//TODO: Need to render from parser. Temp loaded WORKBOOK
                //active info handling
                WorkbookContainer container = CurrentRealm.getContainer();
                UserProfile userProfile = CurrentRealm.getUserProfile();
                //Added temporarily. Will be removed after the testing. By Kirubha.
                if (userProfile == null) {
                        throw new Exception("Userprofile is null for the document id " + container.getDocId() + " for the user " + IAMUtil.getCurrentUser().getLoginName());
                }
//                WidgetReaderObtainer obtainer = new WidgetReaderObtainerBaseDocImpl(container, userProfile);
                WidgetReader reader = WidgetReaderFactory.getReader(new WidgetReader.Task(true, true, true, true));
//                WidgetReader reader = obtainer.getWidgetReader();
                return reader;
        }

        /*Things, which can be update in server:
         * 		this.docId = docId;
         * 		this.docName = String.valueOf(map.get("DOCUMENT_NAME"));
         * 		this.docOwner = String.valueOf(map.get("AUTHOR_NAME"));
         * 		this.docOwnerId = String.valueOf(map.get("ACCOUNT_ID"));
         * 		this.collabId = map.get("COLLAB_ID")==null?null:String.valueOf(map.get("COLLAB_ID"));
         */

        /*
         * Things to be updated	in server	2:,
         *
         * 	this.accId		=	accId;				//apiKeyId
         *	this.zUserId	=	zUID;				//annonUserName
         *	this.userName	=	annonName;		[Any Way, this going to be store in DB(COLLABORATION_USER_NAME), so it won't be a problem].
         *	this.accessType =	accessType;	[This will be decided, based on mode].
         *	this.userType	=	userType;		[Need to consider, do we need this].
         *	this.tabObjectMap =	new HashMap<String, TabObject>();[No Need].
         */
        @Override
        protected void postProcessor(HttpServletRequest request, WorkbookContainer container, UserProfile profile) throws Exception {

                //TODO:	Things with bean should get persisted.
                String modeChanged = request.getParameter("modeChanged");//No I18N
                if ("true".equalsIgnoreCase(modeChanged)) {//No I18N
                        request.setAttribute(Constants.LOCKED_USER, "Some user");//No I18N
                }
                String mode = request.getParameter("mode");
                if (mode != null) {
                        request.setAttribute("mode", mode);
                        request.setAttribute(Constants.VIEW, "HTMLVIEW");
                } else {
                        request.setAttribute(Constants.VIEW, Constants.PUBLIC_VIEW);
                }

                ResourceInfo resInfo = ZohoFS.getResourceInfo(container.getResourceId());
                if (resInfo != null && !(resInfo.getName()).equals(container.getDocName())) {
                        container.rename(resInfo.getName());
                }


                request.setAttribute("PUBLIC_AUTHOR_NAME", container.getDocOwner());
                request.setAttribute(Constants.DOCUMENT_ID, container.getDocId());
                request.setAttribute(Constants.DOCUMENT_NAME, container.getDocName());
                request.setAttribute("fileName", container.getDocName());
                //Do we need this?
                request.setAttribute(Constants.FILE_FORMAT, container.getFileFormat());

                boolean isBrowserIE = false;
                String browser_info = request.getHeader("User-Agent");
                if (browser_info.indexOf("MSIE") != -1) {
                        isBrowserIE = true;
                }
                request.setAttribute(Constants.IS_BROWSER_IE, isBrowserIE);
                request.setAttribute(Constants.DOC_AUTHOR_NAME, container.getDocOwner());
                request.setAttribute("PUBLIC_DOC", container.getDocId());
                request.setAttribute(Constants.ISDOCUMENT_OWNER, false);
                request.setAttribute("DOCUMENT_NAME_URL", container.getDocUrl());

                /*
                 * UserProfile:
                 */
                request.setAttribute(Constants.ALLOW_TO_WRITE, true);
                request.setAttribute("DISPLAYUSER", profile.getUserName());	//From Wms//No I18N
                request.setAttribute("ANONUSER", profile.getZUserId());		//From Wms//No I18N

                request.setAttribute("COLLABORATE", true);//No I18N
                if(IAMUtil.getCurrentUser() != null){
                request.setAttribute("USER_LANGUAGE", IAMUtil.getCurrentUser().getLanguage());
                }
                else{
                	 request.setAttribute("USER_LANGUAGE", null);	
                }
                
                String url = DocumentUtils.getProxyURL(request);
                if ("publichtmlview".equals(url)) {
                        int shareType = ShareConstants.SHAREDTYPE_PUBLIC;
                        Long viewCount = 1L;
                        String sharedTo = "public"; //No I18N
                        if (request.getAttribute("IS_SEMI_PUBLISHED") != null && "true".equals((String) request.getAttribute("IS_SEMI_PUBLISHED"))) {
                                shareType = ShareConstants.SHAREDTYPE_PUBLIC_URL;
                        } else {
                                AccessType accType = CurrentRealm.getAccessIdentity();
                                if (accType == AccessType.PUBLIC_ORG) {
                                        shareType = ShareConstants.SHAREDTYPE_ORG_PUBLISH;
                                        sharedTo = String.valueOf(DocumentUtils.getZOID(container.getDocOwner()));
                                }
                        }
                        /*try {
                                String resourcePerm = ZohoFS.getResourcePermission(container.getDocOwnerZUID(), container.getResourceId(), "-1", shareType);
                                JSONObject shareDetailObj = new JSONArray(resourcePerm).getJSONObject(0);
                                JSONObject addInfoObj = (JSONObject) shareDetailObj.get("additional_info");
                                if (!addInfoObj.isEmpty() && addInfoObj.has("VIEWED")) {
                                        viewCount = (Integer) addInfoObj.get("VIEWED");
                                        viewCount += 1;
                                        addInfoObj.remove("VIEWED");
                                }
                                addInfoObj.put("VIEWED", viewCount);
                                ZohoFS.updatePermission(container.getDocOwnerZUID(), container.getResourceId(), "-1", sharedTo, shareType, Constants.VIEWER, false, addInfoObj.toString());//No I18N
                                request.setAttribute("viewCount", new Long(viewCount));
                        } catch (Exception e) {
                                request.setAttribute("viewCount", new Long(viewCount));
                        }*/
                        try{
                                viewCount = RedisHelper.hincrBy(RedisHelper.PUBLIC_VIEW_COUNT, container.getResourceId(), 1);
                                if(viewCount == 1)
                                {
                                    String resourcePerm = ZohoFS.getResourcePermission(container.getDocsSpaceId(), container.getResourceId(), "-1", shareType);
                                    logger.log(Level.INFO, "[PUBLISHED] resourcePerm = {0} ::: RID = {1} ::: SHARETYPE = {2}", new Object[]{resourcePerm, container.getResourceId(), shareType});
                                    JSONObjectWrapper shareDetailObj = new JSONArrayWrapper(resourcePerm).getJSONObject(0);
                                    JSONObjectWrapper addInfoObj = shareDetailObj.getJSONObject("additional_info");
                                    if(!addInfoObj.isEmpty()){
                                            viewCount = addInfoObj.getLong("VIEWED");   //NO I18N
                                            viewCount += 1;
                                    }
                                    RedisHelper.hset(RedisHelper.PUBLIC_VIEW_COUNT, container.getResourceId(), String.valueOf(viewCount));
                                }
                                request.setAttribute("viewCount", viewCount);
                        }catch(Exception e){
                                logger.log(Level.INFO, "Exception while fetching public view count : ", e);
                        }
                }

        }

        @Override
        protected String getForwardName(HttpServletRequest request) throws Exception {
                
                String url = DocumentUtils.getProxyURL(request);
                String loop = request.getParameter("printloop");
                if (!"publichtmlview".equals(url)) {
                        if ("true".equals(loop)) {
                                return "continueLoop";  //No I18N
                        }
                        return "viewhtml";//No I18N

                } else {
                        return "view";//No I18N
                }
        }

        @Override
        protected DocumentRenderPolicy getPolicy(HttpServletRequest request) throws Exception {
                
                DocumentRenderPolicy renderPolicy = new DocumentRenderPolicy();
                renderPolicy.allowForwarding = !"true".equalsIgnoreCase(request.getParameter("reconnect"));
                renderPolicy.allowPostProcess = true;
                renderPolicy.checkFileFormat = true;	//Need to confirm.
                renderPolicy.fetchFreezeInfo = true;
                renderPolicy.fetchWidgetInfo = true;
                String url = DocumentUtils.getProxyURL(request);
                if (!"publichtmlview".equals(url)) {
                        renderPolicy.fetchContainerInfo = true;
                }
                return renderPolicy;
        }
}
