/* $Id$ */
package com.zoho.sheet.action;
import com.adventnet.iam.IAMUtil;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;

import javax.servlet.http.HttpServletResponse;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ShareConstants;
import com.zoho.zfsng.constants.ZFSNGConstants;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
public class PublicDocsAction extends StrutsRequestHandler {
        public static Logger logger = Logger.getLogger(PublicDocsAction.class.getName());
        public String execute() throws Exception {
                if (Constants.IS_DISASTER_RECOVERY_SERVER) {
                        return null;
                }
                
                if (!IAMUtil.getCurrentUser().isConfirmed()) {
                        PrintWriter pw = response.getWriter();
                        pw.write("Unconfirmed user"); //No I18N
                        return null;
                }
                WorkbookContainer container = CurrentRealm.getContainer();
                String loginName = request.getUserPrincipal().getName();//CurrentRealm.getUserProfile().getUserName();
                String rid = container.getResourceId();
                
                String html = request.getParameter("html");
                String publicType = request.getParameter("type");
                String PrevType = request.getParameter("prevType");
                String allowExportStr = request.getParameter("allowExport");
                String showFormulasStr = request.getParameter("showFormulas");
                String allowInteractionStr = request.getParameter("allowInteraction");
                String hideGridlinesStr = request.getParameter("hideGridlines");
                String hideFormulabarStr = request.getParameter("hideFormulabar");
                String hideHeadersStr = request.getParameter("hideHeaders");
                String propertyName = request.getParameter("propertyName");
                String propertyValue = request.getParameter("propertyValue");
                
                String CAPABILITIES = (String) request.getAttribute(ZFSNGConstants.CAPABILITIES);

                JSONObjectWrapper zfsngCapabilities = new JSONObjectWrapper(CAPABILITIES);
                boolean canPublish = zfsngCapabilities.getBoolean("canPublish");
                boolean canOrgPublish = zfsngCapabilities.getBoolean("canOrgPublish");
                
                String responseMsg = "";
                
                boolean isCurrentRidTeamResource = false;
                if (rid != null) {
                    try {
                    	isCurrentRidTeamResource = ZohoFS.isNewUI(rid);
                    	logger.info("isTeamResource .." + rid + " | currRid :" + rid);
                    } catch (Exception e) {
                        logger.log(Level.WARNING, null, e);
                    }
                }
                
                long zoid = DocumentUtils.getZOID(loginName);

                // If the user doesn't have any publish capabilities, then its unauthorized
                if(!canOrgPublish && !canPublish){
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    return null;
                }

                if(!canPublish) {
                    // If the user doesn't have external publish capability but tries to, then its unauthorized
                    if("external".equals(publicType)){
                        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        return null;
                    }
                }
                if (!canOrgPublish) {
                    // If the user doesn't have org publish capability but tries to, then its unauthorized
                    if("org".equals(publicType)) {
                        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        return null;
                    }
                }
                
                // Code for Org Support
//                if (zoid != -1) {
//                	
//                        boolean allowPublish = IAMUtil.isCurrentUserAllowed(OrgPolicy.PUBLISH_DOCUMENT);
//                        boolean allowOrgPublish = DocumentUtils.getCustomPolicy(zoid, "PUBLISH_WITHIN_ORG");    //No I18N
//                            
//                        if(!allowPublish) {
//                            
//                            if("external".equals(publicType) || ("org".equals(publicType) && !allowOrgPublish)){
//                                
//                                responseMsg = "Not Authorized"; //No I18N
//                                
//                                PrintWriter pw = httpRes.getWriter();
//                                pw.write(responseMsg); //NO OUTPUTENCODING
//                                
//                                return null;
//                            }
//                        }
//                }
                
                
                if (html == null) {
                        html = "";
                }
                boolean allowExport = false;
                if (allowExportStr != null) {
                        try {
                                allowExport = Boolean.valueOf(allowExportStr);
                        } catch (Exception e) {
                                allowExport = false;
                        }
                }
                
                boolean showFormulas = false;
                if (showFormulasStr != null) {
                        try {
                                showFormulas = Boolean.valueOf(showFormulasStr);
                        } catch (Exception e) {
                                showFormulas = false;
                        }
                }
                boolean allowInteraction = false;
                if (allowInteractionStr != null) {
                        try {
                        	allowInteraction = Boolean.valueOf(allowInteractionStr);
                        } catch (Exception e) {
                        	allowInteraction = false;
                        }
                }
                
                boolean hideGridlines = false;
                if (hideGridlinesStr != null) {
                        try {
                        	hideGridlines = Boolean.valueOf(hideGridlinesStr);
                        } catch (Exception e) {
                        	hideGridlines = false;
                        }
                }
                boolean hideFormulabar = false;
                if (hideFormulabarStr != null) {
                        try {
                        	hideFormulabar = Boolean.valueOf(hideFormulabarStr);
                        } catch (Exception e) {
                        	hideFormulabar = false;
                        }
                }
                
                boolean hideHeaders = false;
                if (hideHeadersStr != null) {
                        try {
                        	hideHeaders = Boolean.valueOf(hideHeadersStr);
                        } catch (Exception e) {
                        	hideHeaders = false;
                        }
                }
                JSONObjectWrapper additionalInfo = new JSONObjectWrapper();
                additionalInfo.put("VIEWED", 0); 
                additionalInfo.put(JSONConstants.SHOWFORMULAS, Boolean.toString(showFormulas));
                additionalInfo.put(JSONConstants.ALLOWEXPORT, Boolean.toString(allowExport));
                additionalInfo.put(JSONConstants.ALLOWINTERACTIONS, Boolean.toString(allowInteraction));
                additionalInfo.put(JSONConstants.HIDEGRIDLINES, Boolean.toString(hideGridlines));
                additionalInfo.put(JSONConstants.HIDEFORMULABAR, Boolean.toString(hideFormulabar));
                additionalInfo.put(JSONConstants.HIDEHEADERS, Boolean.toString(hideHeaders));
                
                String sharePermission = Constants.VIEWER;
                int shareType = ShareConstants.SHAREDTYPE_PUBLIC;
                String shareTo = "public";              //No I18N
                if (!"external".equals(publicType)) {
                        shareType = ShareConstants.SHAREDTYPE_ORG_PUBLISH;
                        if(isCurrentRidTeamResource) {
                        		shareTo = container.getDocsSpaceId();
                        }else {
                        		shareTo = String.valueOf(zoid);
                        }
                }
                if (html.equals("Make Public")) {
                			String docsSpaceId = container.getDocsSpaceId(); 
                			ZohoFS.publishResource(docsSpaceId, rid, shareType, sharePermission, "-1", additionalInfo.toString());
                            
//                			additionalInfo = new JSONObject(ZohoFS.getResourceAdditionalInfo(container.getDocsSpaceId(), rid));
//                			JSONObject pubMeta = new JSONObject();
//                			pubMeta.put("SHOWFORMULAS", Boolean.toString(showFormulas));
//                			pubMeta.put("ALLOWEXPORT", Boolean.toString(allowExport));
//                			pubMeta.put("ALLOWINTERACTIONS", Boolean.toString(allowInteraction));
//                			pubMeta.put("HIDEGRIDLINES", Boolean.toString(hideGridlines));
//                			pubMeta.put("HIDEFORMULABAR", Boolean.toString(hideFormulabar));
//                			pubMeta.put("HIDEHEADERS", Boolean.toString(hideHeaders));
//                            
//                			pubMeta.put(JSONConstants.PUBLISHED_TYPE, publicType);
//                			
                		    //ZohoFS.shareResource(container.getDocsSpaceId(), rid, "-1", shareTo, shareType, sharePermission, false, additionalInfo.toString());
                        //Removing org publish while publishing to external world - venkat
                			String resourcePerm = ZohoFS.getResourcePermission(container.getDocsSpaceId(), rid, "-1", shareTo.equals("public") ? "public" : isCurrentRidTeamResource ? container.getDocsSpaceId() : String.valueOf(zoid));    //No I18N
                        JSONArrayWrapper permissionArray = new JSONArrayWrapper(resourcePerm);
                        JSONObjectWrapper permObject = permissionArray.getJSONObject(0);
                			String str=permObject.getString("shared_time");
                            
                        SimpleDateFormat sf = new SimpleDateFormat("dd MMM yyyy");
                        Date date = new Date(Long.parseLong(str));
                        String publishDate=sf.format(date);
                        JSONObjectWrapper respObj = new JSONObjectWrapper();
                        respObj.put("Publish_Date", publishDate);
                        
//                        pubMeta.put(JSONConstants.PUBLISHED_DATE, publishDate);
//                        additionalInfo.put(JSONConstants.PUBLISHED_META, pubMeta);
//                        ZohoFS.updateResourceAdditionalInfo(docsSpaceId, rid, additionalInfo.toString());
                                
                        if ("public".equals(shareTo) && zoid != -1) {
	                        	try {
//	                        		   String resourcePerm = ZohoFS.getResourcePermission(container.getDocsSpaceId(), rid, "-1", isCurrentRidTeamResource?container.getDocsSpaceId():String.valueOf(zoid));
	                        		   if(resourcePerm != null) {
	                        			   ZohoFS.removePublish(container.getDocsSpaceId(), rid, ShareConstants.SHAREDTYPE_ORG_PUBLISH, "-1");
	                        			   //ZohoFS.deletePermission(container.getDocsSpaceId(), rid, "-1", String.valueOf(zoid), ShareConstants.SHAREDTYPE_ORG_PUBLISH);
	                        			   RedisHelper.hdel(RedisHelper.PUBLIC_VIEW_COUNT, rid);
	                        		   }
	                        	}catch (Exception e) {
	                        		logger.log(Level.WARNING, null, e);
							}
                        }
                        
//                        String serverUrl = ClientUtils.getServerURL(httpReq, true, "", false, true);
//                        responseMsg = IAMEncoder.encodeURL(serverUrl + "published.do?rid=" + rid);//No I18N
                        responseMsg = respObj.toString();
                } else if (html.equals("Change Options")) {
                    changePublishOptions(container.getDocsSpaceId(), rid, zoid, isCurrentRidTeamResource, shareTo, sharePermission, shareType, propertyName, propertyValue);
                }else if(html.equals("Change Type")){
                	
                	
                	int prevShareType = ShareConstants.SHAREDTYPE_PUBLIC;
                    String prevShareTo = "public";              //No I18N
                    if (!"external".equals(PrevType)) {
                            prevShareType = ShareConstants.SHAREDTYPE_ORG_PUBLISH;
                            if(isCurrentRidTeamResource)
                            {
                            		prevShareTo = container.getDocsSpaceId();
                            }else {
                            		prevShareTo = String.valueOf(zoid);
                            }
                    }
                    
                     String resourcePerm = ZohoFS.getResourcePermission(container.getDocsSpaceId(), rid, "-1", prevShareType);
                     JSONObjectWrapper shareDetailObj = new JSONArrayWrapper(resourcePerm).getJSONObject(0);
                     JSONObjectWrapper addInfoObj = (JSONObjectWrapper) shareDetailObj.get("additional_info");
                    
                      //ZohoFS.deletePermission(container.getDocsSpaceId(), rid, "-1", prevShareTo, prevShareType);
                      try {
                    	  	ZohoFS.removePublish(container.getDocsSpaceId(), rid, prevShareType, "-1");
                      }catch(Exception e) {
                    	  	logger.log(Level.WARNING, null, e);
                      }
                      RedisHelper.hdel(RedisHelper.PUBLIC_VIEW_COUNT, rid);
                      
                     //ZohoFS.shareResource(container.getDocsSpaceId(), rid, "-1", shareTo, shareType, sharePermission, false, addInfoObj.toString());
                     ZohoFS.publishResource(container.getDocsSpaceId(), rid, shareType, sharePermission, "-1", addInfoObj.toString());  
                    
//                    String docsSpaceId = container.getDocsSpaceId();
//					String additionalInfoStr = ZohoFS.getResourceAdditionalInfo(docsSpaceId, rid);
//					additionalInfo = new JSONObject(additionalInfoStr);
//					JSONObject pubMeta = new JSONObject();	
//					if(additionalInfo.has(JSONConstants.PUBLISHED_META)) {
//						pubMeta = new JSONObject(additionalInfo.getString(JSONConstants.PUBLISHED_META));
//						pubMeta.put(JSONConstants.PUBLISHED_TYPE, publicType);
//					} else {		//Migration.
//						String resourcePerm = ZohoFS.getResourcePermission(container.getDocsSpaceId(), rid, "-1", shareType);
//                        JSONObject shareDetailObj = new JSONArray(resourcePerm).getJSONObject(0);
//                        JSONObject addInfoObj = (JSONObject) shareDetailObj.get("additional_info");
                        //Migrating the old meta.
                        
//            				pubMeta.put("ALLOWEXPORT", addInfoObj.getString("ALLOWEXPORT"));
//            				pubMeta.put("SHOWFORMULAS", addInfoObj.getString("SHOWFORMULAS"));
//            				pubMeta.put(JSONConstants.PUBLISHED_TYPE, publicType);
            			
            				//removing property from additional info - migration.
//                        addInfoObj.remove("SHOWFORMULAS");
//                        addInfoObj.remove("ALLOWEXPORT");
//                        ZohoFS.updatePermission(container.getDocsSpaceId(), rid, "-1", shareTo, shareType, sharePermission, false, addInfoObj.toString());
//					}	
                    
                    String resourcePerm1 = ZohoFS.getResourcePermission(container.getDocsSpaceId(), rid, "-1", shareTo.equals("public") ? "public" : isCurrentRidTeamResource ? container.getDocsSpaceId() : String.valueOf(zoid));    //No I18N
					JSONArrayWrapper permissionArray = new JSONArrayWrapper(resourcePerm1);
                    JSONObjectWrapper permObject = permissionArray.getJSONObject(0);
            			String str=permObject.getString("shared_time");
					SimpleDateFormat sf = new SimpleDateFormat("dd MMM yyyy");
                    Date date = new Date(Long.parseLong(str));
                    String publishDate=sf.format(date);
                    JSONObjectWrapper respObj = new JSONObjectWrapper();
                    respObj.put("Publish_Date", publishDate);
//                    pubMeta.put(JSONConstants.PUBLISHED_DATE, publishDate);
                    responseMsg = respObj.toString();
                    
//                    additionalInfo.put(JSONConstants.PUBLISHED_META, pubMeta);
//                    ZohoFS.updateResourceAdditionalInfo(docsSpaceId, rid, additionalInfo.toString());
                } else if (html.equals("Remove Public")) {
                        ZohoFS.removePublish(container.getDocsSpaceId(), rid, shareType, "-1");
                       // ZohoFS.deletePermission(container.getDocsSpaceId(), rid, "-1", shareTo, shareType);
                        RedisHelper.hdel(RedisHelper.PUBLIC_VIEW_COUNT, rid);
                        
                } else {
                		try {
                		    ZohoFS.removePublish(container.getDocsSpaceId(), rid, shareType, "-1");
                        //ZohoFS.deletePermission(container.getDocsSpaceId(), rid, "-1", shareTo, shareType);
                        RedisHelper.hdel(RedisHelper.PUBLIC_VIEW_COUNT, rid);
                        responseMsg = "Public Sharing for this document is removed";//No I18N
                		}catch (Exception e) {
                			logger.log(Level.WARNING, null, e);
					}
                        
                }
                HttpServletResponseWrapper.sendResponse(response, responseMsg, HttpServletResponseWrapper.MimeType.TEXT);
                return null;
        }

    public static String changePublishOptions(String owner, String rId, long zoid, boolean isCurrentRidTeamResource, String shareTo, String sharePermission, int shareType, String propertyName, String propertyValue) throws Exception{
        String responseMsg = "";
        String resourcePerm = ZohoFS.getResourcePermission(owner, rId, "-1", shareTo.equals("public") ? "public" : isCurrentRidTeamResource ? owner : String.valueOf(zoid));    //No I18N
        JSONObjectWrapper shareDetailObj = new JSONArrayWrapper(resourcePerm).getJSONObject(0);
        String additionalInfo = shareDetailObj.has("additional_info") ? shareDetailObj.getString("additional_info") : "NA"; //No I18N
        JSONObjectWrapper addInfoObj =  "NA".equals(additionalInfo) ? new JSONObjectWrapper() : new JSONObjectWrapper(additionalInfo); //No I18N

        switch (propertyName) {
            case "ALLOWEXPORT":
            case "SHOWFORMULAS":
                addInfoObj.put(propertyName, propertyValue);
                break;
            case "ALLOWINTERACTIONS":
                addInfoObj.put(JSONConstants.ALLOWINTERACTIONS, propertyValue);
                break;
            case "HIDEGRIDLINES":
                addInfoObj.put(JSONConstants.HIDEGRIDLINES, propertyValue);
                break;
            case "HIDEFORMULABAR":
                addInfoObj.put(JSONConstants.HIDEFORMULABAR, propertyValue);
                break;
            case "HIDEHEADERS":
                addInfoObj.put(JSONConstants.HIDEHEADERS, propertyValue);
                break;
        }
        ZohoFS.updatePermission(owner, rId, "-1", shareTo, shareType, sharePermission, false, addInfoObj.toString());

        if (resourcePerm != null) {
            JSONArrayWrapper permissionArray = new JSONArrayWrapper(resourcePerm);
            JSONObjectWrapper permObject = permissionArray.getJSONObject(0);
            String str = permObject.getString("shared_time");
            SimpleDateFormat sf = new SimpleDateFormat("dd MMM yyyy");
            Date date = new Date(Long.parseLong(str));
            String publishDate = sf.format(date);
            JSONObjectWrapper respObj = new JSONObjectWrapper();
            respObj.put("Publish_Date", publishDate);
            responseMsg = respObj.toString();
        }
        return responseMsg;
    }
}
