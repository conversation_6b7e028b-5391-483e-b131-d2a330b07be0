/* $Id$ */
package com.zoho.sheet.action;

import com.adventnet.ds.query.*;
import com.adventnet.mfw.bean.BeanUtil;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.ReadOnlyPersistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import com.zoho.sheet.util.StrutsRequestHandler;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Iterator;
import java.util.logging.Logger;

public class BannerRequestAction extends StrutsRequestHandler {

        public static final Logger LOGGER = Logger.getLogger(BannerRequestAction.class.getName());
        @Override
        public String execute() throws Exception {

                int action = Integer.parseInt(request.getParameter("action"));//No I18N

                response.setContentType("application/json;charset=UTF-8");

                final String bannerName = "bannerName";//No I18N

                if (action == ActionConstants.REQUEST_BANNER) //No I18N
                {

                        try {
                        		JSONObjectWrapper banner = new JSONObjectWrapper();
                        		
                        		banner = checkRefreshBanner(request);
                        		
                                if(banner == null){
                        		String showBannerVal = BannerRequestAction.getShowBanner();
                                if (showBannerVal != "") {

                                        if ((request.getParameter(bannerName).equals("null") ||request.getParameter(bannerName).isEmpty()|| Long.parseLong(showBannerVal.split(":")[0].trim()) > Long.parseLong(request.getParameter(bannerName))) && Boolean.parseBoolean(showBannerVal.split(":")[1]) == true) {
                                                //fetch banner
                                                banner = BannerRequestAction.getBanner();
                                                if (!banner.isEmpty()) {
                                                        if (showBannerVal.contains(":")) {
                                                                banner.put(bannerName, showBannerVal.split(":")[0]);
                                                        }
                                                }

                                        }
                                }
                                }  
                                
                                if(banner!=null){
                                	response.getWriter().write(banner.toString());      //NO OUTPUTENCODING      
                                }
                                else
                                {
                                    response.getWriter().write((new JSONObjectWrapper()).toString());
                                	return null;
                                }
                        } catch (Exception e) {
                                response.getWriter().write("error");//No I18N

                                LOGGER.info("Post doc load error in fetching banner" + e); //No I18N
                        }

                }

                return null;

        }
        private JSONObjectWrapper parseDate(String buildLabel)
        {
        	JSONObjectWrapper parsedData = new JSONObjectWrapper();
        	String BuildLabel[] = buildLabel.split("_");
        	parsedData.put("date", BuildLabel[0]+"/"+BuildLabel[1]+"/"+BuildLabel[2]);
        	parsedData.put("buildNo",BuildLabel.length == 4 ? 0 : BuildLabel[3]);
        	return parsedData;
        }
        
        private JSONObjectWrapper checkRefreshBanner(HttpServletRequest request){
        	try {			
        		String entireclientBuildVersion = (String) request.getParameter("bv");
        		String isMandatory = RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_MANDATORY_BANNER, -1);
        		String isHiddenRefresh = RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_HIDDEN_REFRESH_BANNER, -1);
			if("true".equals(isMandatory) || "true".equals(isHiddenRefresh))
        		{
        			if(entireclientBuildVersion != null && EngineConstants.version.equalsIgnoreCase(entireclientBuildVersion)){
        				return setAnnouncementBar();
        			}
        		}
    						
    			String entireBuildVersionInredis = RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_BUILD_VERSION_IN_REDIS, -1);			    	
    			
    			JSONObjectWrapper clientBuildData = parseDate(entireclientBuildVersion);
    			Date clientBuildVersionDate = new Date(String.valueOf(clientBuildData.get("date")));
    			
    			JSONObjectWrapper RedisBuildData = parseDate(entireBuildVersionInredis);
    			Date buildVersionInredisDate =  new Date(String.valueOf(RedisBuildData.get("date")));
    			if(clientBuildVersionDate.after(buildVersionInredisDate))
    			{				
    				return null;
    			}
    			else
    			{
    				if(clientBuildVersionDate.equals(buildVersionInredisDate))
    				{
    					if(Integer.parseInt(clientBuildData.getString("buildNo")) == Integer.parseInt(RedisBuildData.getString("buildNo")) ||
    							Integer.parseInt(clientBuildData.getString("buildNo")) > Integer.parseInt(RedisBuildData.getString("buildNo")))
    					{						
    						return null;
    					}		
    					else
    					{    				
    						return setAnnouncementBar();
    					}
    				}
    				else if(clientBuildVersionDate.before(buildVersionInredisDate)){					
    					return setAnnouncementBar();
    				}
    			}
    		} catch (Exception e) {
    			
    		}
			return null;
        }
        
        private static JSONObjectWrapper setAnnouncementBar()
        {
        	JSONObjectWrapper banner = new JSONObjectWrapper();
			try 
			{
				banner.put("BANNER_TYPE", RedisHelper.get(RedisHelper.ADMIN_ANNOUNCEMENT_LEVEL, -1));
				banner.put("BANNER_MSG", RedisHelper.get(RedisHelper.ADMIN_ANNOUNCEMENT_MSG, -1));
				banner.put("isRefreshNeeded", RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_MANDATORY_BANNER, -1));
                                banner.put("isHiddenRefresh", RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_HIDDEN_REFRESH_BANNER, -1));
			} 
			catch (Exception e) {
				LOGGER.info("Error while setting announcement bar in BannerRequestAction : "+e);				
			}			
			return banner;			     	 
        }
       
        public static String getShowBanner() {

                //to read showBanner variable globalsettings table.value returned is of the form "bannerName(int):boolean"
                String showbanner = "";
                final String tabName = "GlobalSettings";//No I18N
                final String col1 = "VARIABLE_NAME";//No I18N
                final String col2 = "VARIABLE_VALUE";//No I18N
                final String reqVariable = "showBanner";//No I18N

                try {

                        ReadOnlyPersistence per = (ReadOnlyPersistence) BeanUtil.lookup("BannerPersistence", "public");
                        SelectQueryImpl selQuery = new SelectQueryImpl(new Table(tabName));
                        selQuery.addSelectColumn(new Column(tabName, col1));
                        selQuery.addSelectColumn(new Column(tabName, col2));
                        Criteria cri = new Criteria(new Column(tabName, col1), reqVariable, QueryConstants.EQUAL);
                        selQuery.setCriteria(cri);
                        DataObject gsDO = per.get(selQuery);
                        if (gsDO != null && !gsDO.isEmpty()) {
                                Iterator itr = gsDO.getRows(tabName);

                                while (itr.hasNext()) {
                                        Row infoRow = (Row) itr.next();

                                        String var_nm = infoRow.get(col1).toString();
                                        String var_val = infoRow.get(col2).toString();
                                        if (var_nm.equals(reqVariable)) {
                                                showbanner = var_val;

                                        }
                                }
                        }

                } catch (Exception e) {
                        LOGGER.info("Error in getting showBanner value::" + e); //No I18N
                }
                return showbanner;
        }

        public static JSONObjectWrapper getBanner() {
		     //Reads banner from cached persistence for current user locale,if not available for default locale="en"

                final String tabName = "BannerManager";//No I18N
                final String col1 = "BANNER_LOCALE";//No I18N
                final String col2 = "BANNER_MSG";//No I18N
                final String col3 = "BANNER_LEARN";//No I18N
                final String col4 = "BANNER_LINK"; //No I18N
                final String col5 = "BANNER_TYPE"; //No I18N
                
                String currLanguageCode = LocaleUtil.getLanguage();
                String defaultLanguageCode = "en";  //No I18N

                JSONObjectWrapper banner = new JSONObjectWrapper();

                try {
                        //fetching banner for current locale
                        ReadOnlyPersistence cachedPer = (ReadOnlyPersistence) BeanUtil.lookup("BannerPersistence", "public");//No I18N

                        SelectQueryImpl selQuery = new SelectQueryImpl(new Table(tabName));
                        selQuery.addSelectColumn(new Column(tabName, col1));
                        selQuery.addSelectColumn(new Column(tabName, col2));
                        selQuery.addSelectColumn(new Column(tabName, col3));
                        selQuery.addSelectColumn(new Column(tabName, col4));
                        selQuery.addSelectColumn(new Column(tabName, col5));
                        
                        Criteria cri = new Criteria(new Column(tabName, col1), currLanguageCode, QueryConstants.EQUAL);
                        Criteria cri2 = new Criteria(new Column(tabName, col1), defaultLanguageCode, QueryConstants.EQUAL);
                        selQuery.setCriteria(cri.or(cri2));
                        DataObject bnDO = cachedPer.get(selQuery);

                        if (!bnDO.isEmpty()) {
                                Iterator itr = bnDO.getRows(tabName);
                                while (itr.hasNext()) {
                                        Row infoRow = (Row) itr.next();
                                        String data1 = infoRow.get(col2).toString(); //msg
                                        String data2 = infoRow.get(col3).toString();//learn more text
                                        String data3 = infoRow.get(col4).toString();//learn more link
                                        String data4 = infoRow.get(col5).toString();//Banner type
                                        
                                        if (data1 != null) {
                                                banner.put(col2, infoRow.get(col2).toString());
                                        }
                                        if (data2 != null) {
                                                banner.put(col3, infoRow.get(col3).toString());
                                        }
                                        if (data3 != null) {
                                                banner.put(col4, infoRow.get(col4).toString());
                                        }
                                        if (data4 != null) {
                                                banner.put(col5, infoRow.get(col5).toString());
                                        }

                                }

                        }

                } catch (Exception e) {
                        LOGGER.info("Error in fetching announcement banner" + e); //No I18N

                }            
                return banner;
        }

}
