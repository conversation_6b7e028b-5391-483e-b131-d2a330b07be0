/* $Id$ */
package com.zoho.sheet.action;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.zoho.websheet.model.response.ResponseObject;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.response.viewport.Constraints;
import com.adventnet.zoho.websheet.model.response.viewport.ConstraintsImpl;
import com.adventnet.zoho.websheet.model.response.viewport.ContemporaryViewPort;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.zoho.sheet.util.*;
import com.adventnet.wms.api.CollaborationApi;
import com.adventnet.zoho.websheet.model.UserProfile.PermissionType;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.TabInfo.TabType;
import com.adventnet.zoho.websheet.model.ErrorCode.DisplayType;
import com.adventnet.zoho.websheet.model.ErrorCode.MsgType;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.zfsng.client.ZohoFS;

import java.util.Enumeration;
import java.util.Hashtable;
import java.util.logging.Level;
import java.util.logging.Logger;

public class JoinCollabAction extends StrutsRequestHandler {

        public static final Logger LOGGER = Logger.getLogger(JoinCollabAction.class.getName());

        @Override
        public String execute() throws java.io.IOException, ServletException {
                String wmsId = request.getParameter("sid");
                String rawSid = request.getParameter("rsid");
                String utid = request.getParameter("utid");
                String tabKey = (utid == null) ? rawSid : utid;
                String oldRawSid = request.getParameter("oldrsid");
                String tabTypeStr = request.getParameter("tabType");

                try {
                        WorkbookContainer wbContainer = CurrentRealm.getContainerWithoutTrace();

                        //This is added as dummy code to get correct lastExecutedId, we have to do better fix.
                        //Lastexecutedid is 0, if workbook is not ready, and we are trying to access.
                        Workbook workBook = wbContainer.getWorkbook(CurrentRealm.getWorkBookIdentity());
                        UserProfile profile = CurrentRealm.getUserProfile();
                        String currentSheet = request.getParameter("currentSheet");
                        TabType tabType = ClientUtils.getTabType(tabTypeStr);
                        ResponseObject.ResponseVersion responseVersion = (ResponseObject.ResponseVersion) request.getAttribute(JSONConstants.RESPONSE_VERSION_NUMBER);

                        PermissionType pType = profile.getPermissionType();

                        String tempcontaineridentity = request.getParameter("temp_container_identity");
                        boolean joincollab = true;
                        AccessType accesstype = CurrentRealm.getAccessIdentity();
                        boolean isReConnect = oldRawSid != null ? true : false;


                        //Action Execution Id as well, To render the missed data.
                        LOGGER.log(Level.INFO, "== JoinCollabAction==  RESOURCE_ID: {0}, rawSid : {1}", new String[]{wbContainer.getResourceKey(), rawSid});
                        
                        if (accesstype == AccessType.AUTH || accesstype == AccessType.AUTH_REMOTE || accesstype == AccessType.REMOTE || tempcontaineridentity != null) {
                                Boolean isCollabLimitExceeded = false;
                                //Have to get the view port as well.
                                //Collaboration limit
                                if (!isReConnect) {
                                        String collabId = null;
                                        if (accesstype == AccessType.AUTH_REMOTE || accesstype == AccessType.REMOTE) {//Remote API 
                                                collabId = RemoteUtils.fetchCollabId(Long.valueOf(wbContainer.getDocId()), wbContainer.getDocOwner());
                                        } else {
                                        		//TEAM-DRIVE: TEMPORARY CODE. Need to check.
                                                collabId = ZohoFS.getCollabId(wbContainer.getDocsSpaceId(), wbContainer.getResourceId());//DocumentUtils.getCollabId(wbContainer.getDocId(), wbContainer.getDocOwner());
                                        }
                                        isCollabLimitExceeded = isCollaboratorLimitReached(collabId,pType);
                                        if(isCollabLimitExceeded){
                                                JSONObjectWrapper errObj = new JSONObjectWrapper();
                                                if(PermissionType.READ == pType || PermissionType.READ_COMMENT == pType){
                                                        errObj.put("isRo", true);
                                                }else{
                                                        errObj = ErrorCode.getErrorMessage(ErrorCode.ERROR_COLLABORATORS_LIMIT_EXCEEDED, ErrorCode.ERROR_COLLABORATORS_LIMIT_EXCEEDED, MsgType.ERROR, DisplayType.BANNER, null, null);
                                                }
                                                LOGGER.log(Level.INFO, "[ZS_ERROR_BANNER][RO JOIN_COLLAB] : {0} :: RID : {1} pType : {2}", new Object[]{ErrorCode.ERROR_COLLABORATORS_LIMIT_EXCEEDED, wbContainer.getResourceId(),pType});
                                                HttpServletResponseWrapper.sendResponse(response, errObj, HttpServletResponseWrapper.MimeType.JSON);
                                                return null;
                                        }
                                }
                                wbContainer.registerToCollaboration(profile, tabKey, rawSid, wmsId, tabType, responseVersion);
                        } else {
                                joincollab = false;
                        }
                        
                        if (profile.isWmsRawSessionIdAvailable(oldRawSid)) { // if it is reconnect we will update rsid here.
                                profile.replaceWmsRawSessionId(tabKey, oldRawSid, rawSid);
                        } else {
                                updateViewPortOrConstraintsToProfile(request, response, isReConnect); // while joincollab we will update the rsid in tabInfo here.
                                if (request.getAttribute("IS_LOCKED") != null && (boolean)request.getAttribute("IS_LOCKED") && !(accesstype == AccessType.PUBLIC_EXTERNAL || accesstype == AccessType.PUBLIC_ORG)){
                                        JSONObjectWrapper json = new JSONObjectWrapper();
                                        json.put("IS_LOCKED", true);
                                        json.put("LOCKED_BY", (String)request.getAttribute("LOCKED_BY"));
                                        json.put("maxPermission", (String)request.getAttribute("maxPermission"));
                                        json.put("LOGIN_USER", (String)request.getAttribute("LOGIN_USER"));
                                        json.put(JSONConstants.EXECUTED_ACTION_ID, wbContainer.getExecutedActionId());
                                        HttpServletResponseWrapper.sendResponse(response, json, HttpServletResponseWrapper.MimeType.JSON);
//                                        response.getWriter().write(json.toString());  //NO OUTPUTENCODING
                                        return null;
                                }
                        }
                        JSONObjectWrapper json = new JSONObjectWrapper();
                        json.put(JSONConstants.EXECUTED_ACTION_ID, wbContainer.getExecutedActionId());
//                        json.put(JSONConstants.RELOAD_TILES, RedisHelper.hlen(RedisHelper.ACTIONS_LIST+wbContainer.getResourceKey()) > 0); //fixed in client
                        json.put(JSONConstants.COLLAB_ID, wbContainer.getCollabId());
                        //TODO : need not send it to client 
                        if (!profile.isSheetBasedShare() && profile.getPermissionType() == UserProfile.PermissionType.READ) {
                                json.put(JSONConstants.READ_ONLY, true);
                        }
                        //Reconnection handling.
                        if (isReConnect) {
                                json.put(JSONConstants.IS_RECONNECT, isReConnect);
                        }
                        HttpServletResponseWrapper.sendResponse(response, json, HttpServletResponseWrapper.MimeType.JSON);
//                        response.getWriter().write(json.toString()); // NO OUTPUTENCODING
                } catch (Exception ex) {
                        LOGGER.log(Level.WARNING, "ERROR DOCID: {0} :: USERID : {1} :: RSID : {2} >>> Error in JoinCollabAction.", new Object[]{CurrentRealm.getContainerIdentity(), CurrentRealm.getUserProfile().getZUserId(), rawSid});
                        LOGGER.log(Level.WARNING, "ERROR DOCID: " + CurrentRealm.getContainerIdentity() + " Error in JoinCollabAction.", ex);
                }
                return null;
        }

        public static boolean isCollaboratorLimitReached(String collabId,PermissionType pType) throws Exception{
                Hashtable collabUserTable = CollaborationApi.getSessions(collabId);
                int cCnt = 0;
                if (!collabUserTable.isEmpty()) {
                        Enumeration e = collabUserTable.keys();
                        while (e.hasMoreElements()) {
                                String userId = (String) e.nextElement();
                                String userSession = collabUserTable.get(userId).toString();
                                if (!"[]".equals(userSession.trim())) {
                                        cCnt += userSession.split(",").length; //NO I18N
                                }
                        }
                }
                LOGGER.log(Level.INFO, "[MAX USER CHECK] Collaborator cnt:: {0}", cCnt);
                String propertyKey = (pType == PermissionType.READ || pType == PermissionType.READ_COMMENT) ? "MAX_RO_COLLABORATORS_LIMIT" : "MAX_COLLABORATORS_LIMIT"; //No I18N

                String maxUsers = EnginePropertyUtil.getSheetPropertyValue(propertyKey);
                if (maxUsers != null && !"-1".equals(maxUsers)) {
                        int maxLimit = Integer.parseInt(maxUsers);
                        if (cCnt > maxLimit) {
                                return true;
                        }
                }
                return false;

        }
        public static void updateViewPortOrConstraintsToProfile(HttpServletRequest request, HttpServletResponse response, boolean isReConnect) throws Exception
        {
                try
                {

                        UserProfile profile = CurrentRealm.getUserProfile();
                        //String 	rawSid       				= 	request.getParameter("rsid");
                        String tabKey = CurrentRealm.getTabIdentity();
                        String rsid = CurrentRealm.getWmsIdentity();
                        TabInfo tabInfo = profile.getTabInfo(tabKey, rsid, TabType.CACHED);
                        //while doing reconnect, we are coming with dummy viewport , so avoiding to update viewport
                        if (!isReConnect)
                        {

                                String associatedSheetName = request.getParameter("currentSheet");
                                String ranges = request.getParameter("ranges");

                                JSONObjectWrapper metaJson = new JSONObjectWrapper(request.getParameter("meta"));
                                String celMetaKey = Integer.toString(CommandConstants.CELL_META);
                                int cellOffsetBit = metaJson.has(celMetaKey) ? (Integer) metaJson.get(celMetaKey) : 0;

                                if (ranges != null && !ranges.isEmpty())
                                {
                                        ContemporaryViewPort contemporaryViewPort = new ContemporaryViewPort();	//new		ContemporaryViewPort(null, null, null);

                                        JSONArrayWrapper rangesAry = new JSONArrayWrapper(ranges);
                                        WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();
                                        contemporaryViewPort.updateAreaToViewPort(associatedSheetName, rangesAry);
                                        boolean showFormula = container.isShowFormulas();

                                        Constraints constraints = new ConstraintsImpl(contemporaryViewPort, showFormula, cellOffsetBit);

                                        //profile.setConstraints(rawSid, constraints,TabType.CACHED);
                                        //profile.getTabInfo(tabKey, rsid, TabType.CACHED).setConstraints(constraints);
                                        tabInfo.setConstraints(constraints);
//                        tabInfo.setWmsRSID(rsid);
                                }
                                tabInfo.setWmsRSID(rsid);   //rsid should be set irrespective of ranges

                        }
                } catch (Exception e)
                {
                        // TODO: handle exception

                        LOGGER.log(Level.INFO, "Exception in JoinCollabActionFixer", e);

                }

        }
}
