package com.zoho.sheet.action;

import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.iam.security.UploadedFileItem;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.ocr.OcrConstants;
import com.zoho.sheet.ocr.OcrRequest;
import com.zoho.sheet.ocr.OcrUtils;
import com.zoho.sheet.ocr.beans.ClusterResponse;
import com.zoho.sheet.util.StrutsRequestHandler;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ZiaOcrAction extends StrutsRequestHandler {
    private final Boolean isBenchMarkOn = Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("BENCHMARK_OCR"));   //No I18N
    private static final Logger LOGGER = Logger.getLogger(ZiaOcrAction.class.getName());

    public String execute() throws IOException {

            Long reqTime, parseTime;

        if (!Boolean.valueOf(EnginePropertyUtil.getSheetPropertyValue("IS_OCR_ENABLED"))) {
            return null;
        }

        List<UploadedFileItem> imageFiles = (ArrayList<UploadedFileItem>) request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST);

        UploadedFileItem imageItem = imageFiles.get(0);
        String apiKey = request.getParameter("apiKey");
        File imageFile = imageItem.getUploadedFile();

        response.setContentType("application/json");    //NO I18N
        JSONObjectWrapper responseObj = new JSONObjectWrapper();

        if (isLargeFile(imageFile)){
            responseObj.put("message", "File is larger than max size"); //No I18N
            response.setStatus(HttpServletResponse.SC_REQUEST_ENTITY_TOO_LARGE);
            response.getWriter().write(responseObj.toString());
            return null;
        }

        try{
            long startTime = System.currentTimeMillis();

            OcrRequest ocrRequest = new OcrRequest(imageFile, apiKey);
            OcrRequest.Response respObj = ocrRequest.getResponseObj();

            reqTime = System.currentTimeMillis() - startTime;

            int statusCode = respObj.getResponseCode();
            if(statusCode == 200){

                startTime = System.currentTimeMillis();

                ClusterResponse ocrResponse = OcrUtils.parseOcrData(respObj);

                parseTime = System.currentTimeMillis() - startTime;

                responseObj.put("data", ocrResponse.getGridData());
                responseObj.put("boundingRect", ocrResponse.getStrokeData());
                responseObj.put("statusCode", statusCode);

                if(isBenchMarkOn){
                    responseObj.put("rt", reqTime);
                    responseObj.put("pt", parseTime);
                }
            }
            else{
                responseObj = OcrUtils.parseOcrError(respObj);
                responseObj.put("statusCode", statusCode);
            }

            writeResponseToClient(responseObj, response);
        }
        catch (Exception e){
            String err = "Error in OCR ::: " + e.getMessage();  //No I18N
            LOGGER.log(Level.WARNING, err);
            responseObj.put("error", "500");    //No I18N
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write(responseObj.toString());
        }
        return null;
    }

    private boolean isLargeFile(File imageFile) {
        long fileSize = imageFile.length();
        return fileSize > OcrConstants.MAX_FILE_SIZE;
    }

    private static void writeResponseToClient(JSONObjectWrapper responseObject, HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK); // all response set to 200
        response.getWriter().write(responseObject.toString());
    }
}
