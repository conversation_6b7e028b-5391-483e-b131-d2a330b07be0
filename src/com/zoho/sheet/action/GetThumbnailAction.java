/*$Id$*/
package com.zoho.sheet.action;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.EngineUtils;
import static com.adventnet.zoho.websheet.model.util.EngineUtils1.writeThumbnailHtml;
import com.adventnet.zoho.websheet.model.util.ZSStore;
import com.zoho.sheet.util.StrutsRequestHandler;
import java.io.BufferedInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

import java.util.logging.Logger;
import java.util.logging.Level;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.servlet.ServletOutputStream;

public class GetThumbnailAction extends StrutsRequestHandler {

    public final static Logger LOGGER = Logger.getLogger(GetThumbnailAction.class.getName());

    @Override
    public String execute() throws Exception {
        InputStream is = null;
        BufferedInputStream bis = null;
        String rid = request.getParameter("rid");
        
        ServletOutputStream sout = null;
        try {
            int BUF_LEN = 8192;
            WorkbookContainer container = EngineUtils.creatNewWorkbookContainerFromRSID(rid);
            is = container.getThumbnailImage();
            
            if (is == null) {
                
                if(container.isTemplate()) {
                    
                    Workbook workBook = container.getWorkbook(null);
                    Sheet sheets[] = workBook.getSheets();
                    
                    Long thumbnail_html_id = container.getFragmentId(ZSStore.FileName.THUMBNAIL_HTML, true, null);
                    Map writeInfo = container.getWriteInfo(thumbnail_html_id, ZSStore.FileName.THUMBNAIL_HTML, ZSStore.FileExtn.ZSF, null);
                    OutputStream os = (OutputStream) writeInfo.get("OS");
                    try (ZipOutputStream zos = new ZipOutputStream(os)) {
                        zos.putNextEntry(new ZipEntry(ZSStore.THUMBNAIL_HTML));
                        Sheet firstSheet = sheets[0];
                        writeThumbnailHtml(zos, firstSheet, container);
                    }
                    container.finishWrite(writeInfo);
                    
                    RedisHelper.lpush(RedisHelper.THUMBNAIL_RID_LIST, rid, -1);
                }
                return null;
            }
            bis = new BufferedInputStream(is, BUF_LEN);
            response.addHeader("Cache-Control", "max-age=36000");    //No I18N
            response.setContentType("image/png");
            sout = response.getOutputStream();
            
            int c;
            byte bufr[] = new byte[BUF_LEN];
            while ((c = bis.read(bufr, 0, BUF_LEN)) != -1) {
                sout.write(bufr, 0, c);
            }
        } catch (FileNotFoundException fnfe) {
            LOGGER.log(Level.WARNING, "FILE NOT FOUND >>> Error in Displaying Thumbnail Image", fnfe);
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Error in Displaying Thumbnail Image", e);
        } finally {
            if (bis != null) {
                bis.close();
            }
            if (is != null) {
                is.close();
            }
            if (sout != null) {
                sout.flush();
                sout.close();
            }
        }
        return null;
    }
}
