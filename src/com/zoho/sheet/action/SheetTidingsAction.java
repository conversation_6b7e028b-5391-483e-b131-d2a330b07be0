/* $Id$ */
 /*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.zoho.sheet.action;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQuery;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.SortColumn;
import com.adventnet.ds.query.Table;
import com.adventnet.mfw.bean.BeanUtil;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.ReadOnlyPersistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import com.zoho.sheet.util.SheetPersistenceUtils;
import java.io.PrintWriter;
import java.util.Date;
import java.util.Iterator;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.zoho.sheet.util.StrutsRequestHandler;
/**
 *
 * <AUTHOR>
 */
public class SheetTidingsAction extends StrutsRequestHandler {

    public static final Logger LOGGER = Logger.getLogger(SheetTidingsAction.class.getName());
    String tabName = "SheetTidings";//No I18N
    String col1 = "TIMESTAMP";//No I18N
    String col2 = "HEADER";//No I18N
    String col3 = "MESSAGE";//No I18N
    String col4 = "IMG_LINK"; //No I18N
    String col5 = "LINK"; //No I18N
    String col6 = "LINK_TEXT";//No I18N
    String col7 = "TYPE"; //No I18N
    String col8 = "IS_POPUP";   //No I18N

    Persistence per = SheetPersistenceUtils.getPersistence("Public"); //No I18N
    @Override
    public String execute() throws Exception {
        JSONObjectWrapper resp = new JSONObjectWrapper();
        response.setContentType("application/json"); //No I18N
        response.setCharacterEncoding("UTF-8"); //No I18N 
        resp.put(Integer.toString(CommandConstants.TIPSANDTRICKS_RESP), fetchTidings("Tips_and_Tricks", 4));
        resp.put(Integer.toString(CommandConstants.POPUP_CONTENT), getNewContent());
        resp.put(Integer.toString(CommandConstants.BANNER_RESP), requestBanner(request, response));
        PrintWriter out = response.getWriter();
        out.print(resp);
        return null;
    }

    public String requestBanner(HttpServletRequest request, HttpServletResponse response) {
        final String bannerName = "bannerName";//No I18N
        try {
            JSONObjectWrapper banner = new JSONObjectWrapper();

            String showBannerVal = BannerRequestAction.getShowBanner();
            if (!"".equals(showBannerVal)) {

                if ((request.getParameter(bannerName).equals("null") || request.getParameter(bannerName).isEmpty() || Long.parseLong(showBannerVal.split(":")[0].trim()) > Long.parseLong(request.getParameter(bannerName))) && Boolean.parseBoolean(showBannerVal.split(":")[1]) == true) {
                    //fetch banner
                    banner = BannerRequestAction.getBanner();
                    if (!banner.isEmpty()) {
                        if (showBannerVal.contains(":")) {
                            banner.put(bannerName, showBannerVal.split(":")[0]);
                        }
                    }
                }
            }

            if (banner != null) {

                return (banner.toString());      //NO OUTPUTENCODING     

            } else {

                return ((new JSONObjectWrapper()).toString());
            }
        } catch (Exception e) {
            LOGGER.info("Post doc load error in fetching banner" + e); //No I18N
        }
        return null;
    }

    private JSONObjectWrapper parseDate(String buildLabel) {
        JSONObjectWrapper parsedData = new JSONObjectWrapper();
        String BuildLabel[] = buildLabel.split("_");
        parsedData.put("date", BuildLabel[0] + "/" + BuildLabel[1] + "/" + BuildLabel[2]);
        parsedData.put("buildNo", BuildLabel.length == 4 ? 0 : BuildLabel[3]);
        return parsedData;
    }

    private JSONObjectWrapper checkRefreshBanner(HttpServletRequest request) {
        try {
            String entireclientBuildVersion = (String) request.getParameter("bv");
            String isMandatory = RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_MANDATORY_BANNER, -1);
            String isHiddenRefresh = RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_HIDDEN_REFRESH_BANNER, -1);

            if ("true".equals(isMandatory) || "true".equals(isHiddenRefresh)) {
                if (entireclientBuildVersion != null && EngineConstants.version.equalsIgnoreCase(entireclientBuildVersion)) {
                    return setAnnouncementBar();
                }
            }

            String entireBuildVersionInredis = RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_BUILD_VERSION_IN_REDIS, -1);

            JSONObjectWrapper clientBuildData = parseDate(entireclientBuildVersion);
            Date clientBuildVersionDate = new Date(String.valueOf(clientBuildData.get("date")));

            JSONObjectWrapper RedisBuildData = parseDate(entireBuildVersionInredis);
            Date buildVersionInredisDate = new Date(String.valueOf(RedisBuildData.get("date")));
            if (clientBuildVersionDate.after(buildVersionInredisDate)) {
                return null;
            } else {
                if (clientBuildVersionDate.equals(buildVersionInredisDate)) {
                    if (Integer.parseInt(clientBuildData.getString("buildNo")) == Integer.parseInt(RedisBuildData.getString("buildNo")) || Integer.parseInt(clientBuildData.getString("buildNo")) > Integer.parseInt(RedisBuildData.getString("buildNo"))) {
                        return null;
                    } else {
                        return setAnnouncementBar();
                    }
                } else if (clientBuildVersionDate.before(buildVersionInredisDate)) {
                    return setAnnouncementBar();
                }
            }
        } catch (Exception e) {
            LOGGER.info("Error in checkrefreshBanner: " + e); //No I18N
        }
        return null;
    }

    private static JSONObjectWrapper setAnnouncementBar() {
        JSONObjectWrapper banner = new JSONObjectWrapper();
        try {
            banner.put("BANNER_TYPE", RedisHelper.get(RedisHelper.ADMIN_ANNOUNCEMENT_LEVEL, -1));
            banner.put("BANNER_MSG", RedisHelper.get(RedisHelper.ADMIN_ANNOUNCEMENT_MSG, -1));
            banner.put("isRefreshNeeded", RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_MANDATORY_BANNER, -1));
            banner.put("isHiddenRefresh", RedisHelper.get(RedisHelper.ADMIN_RELOADCLIENT_HIDDEN_REFRESH_BANNER, -1));
        } catch (Exception e) {
            LOGGER.info("Error while setting announcement bar in BannerRequestAction : " + e);    //No I18N               
        }
        return banner;
    }

    public static String getShowBanner() {
        //to read showBanner variable globalsettings table.value returned is of the form "bannerName(int):boolean"
        String showbanner = "";
        final String tabName = "GlobalSettings";//No I18N
        final String col1 = "VARIABLE_NAME";//No I18N
        final String col2 = "VARIABLE_VALUE";//No I18N
        final String reqVariable = "showBanner";//No I18N

        try {
            ReadOnlyPersistence per = (ReadOnlyPersistence) BeanUtil.lookup("BannerPersistence", "public"); //No I18N
            SelectQueryImpl selQuery = new SelectQueryImpl(new Table(tabName));
            selQuery.addSelectColumn(new Column(tabName, col1));
            selQuery.addSelectColumn(new Column(tabName, col2));
            Criteria cri = new Criteria(new Column(tabName, col1), reqVariable, QueryConstants.EQUAL);
            selQuery.setCriteria(cri);
            DataObject gsDO = per.get(selQuery);
            if (gsDO != null && !gsDO.isEmpty()) {
                Iterator itr = gsDO.getRows(tabName);
                while (itr.hasNext()) {
                    Row infoRow = (Row) itr.next();
                    String var_nm = infoRow.get(col1).toString();
                    String var_val = infoRow.get(col2).toString();
                    if (var_nm.equals(reqVariable)) {
                        showbanner = var_val;
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.info("Error in getting showBanner value::" + e); //No I18N
        }
        return showbanner;
    }

    public static JSONObjectWrapper getBanner() {
        //Reads banner from cached persistence for current user locale,if not available for default locale="en"
        final String tabName = "BannerManager";//No I18N
        final String col1 = "BANNER_LOCALE";//No I18N
        final String col2 = "BANNER_MSG";//No I18N
        final String col3 = "BANNER_LEARN";//No I18N
        final String col4 = "BANNER_LINK"; //No I18N
        final String col5 = "BANNER_TYPE"; //No I18N

        String currLanguageCode = LocaleUtil.getLanguage();
        String defaultLanguageCode = "en";  //No I18N

        JSONObjectWrapper banner = new JSONObjectWrapper();

        try {
            //fetching banner for current locale
            ReadOnlyPersistence cachedPer = (ReadOnlyPersistence) BeanUtil.lookup("BannerPersistence", "public");//No I18N

            SelectQueryImpl selQuery = new SelectQueryImpl(new Table(tabName));
            selQuery.addSelectColumn(new Column(tabName, col1));
            selQuery.addSelectColumn(new Column(tabName, col2));
            selQuery.addSelectColumn(new Column(tabName, col3));
            selQuery.addSelectColumn(new Column(tabName, col4));
            selQuery.addSelectColumn(new Column(tabName, col5));

            Criteria cri = new Criteria(new Column(tabName, col1), currLanguageCode, QueryConstants.EQUAL);
            Criteria cri2 = new Criteria(new Column(tabName, col1), defaultLanguageCode, QueryConstants.EQUAL);
            selQuery.setCriteria(cri.or(cri2));
            DataObject bnDO = cachedPer.get(selQuery);

            if (!bnDO.isEmpty()) {
                Iterator itr = bnDO.getRows(tabName);
                while (itr.hasNext()) {
                    Row infoRow = (Row) itr.next();
                    String data1 = infoRow.get(col2).toString(); //msg
                    String data2 = infoRow.get(col3).toString();//learn more text
                    String data3 = infoRow.get(col4).toString();//learn more link
                    String data4 = infoRow.get(col5).toString();//Banner type

                    if (data1 != null) {
                        banner.put(col2, infoRow.get(col2).toString());
                    }
                    if (data2 != null) {
                        banner.put(col3, infoRow.get(col3).toString());
                    }
                    if (data3 != null) {
                        banner.put(col4, infoRow.get(col4).toString());
                    }
                    if (data4 != null) {
                        banner.put(col5, infoRow.get(col5).toString());
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.info("Error in fetching announcement banner" + e); //No I18N
        }
        return banner;
    }

    public JSONObjectWrapper getNewContent() {

        JSONObjectWrapper popupContent = new JSONObjectWrapper();
        try {
            SelectQuery sql = new SelectQueryImpl(new Table(tabName));
            sql.addSelectColumn(Column.getColumn(tabName, "*"));
            Criteria cri1 = new Criteria(Column.getColumn(tabName, col8), 1, QueryConstants.EQUAL);
//            Criteria cri2 = new Criteria(Column.getColumn(tabName, col7),"Announcement",QueryConstants.EQUAL);
//            Criteria cri3 = cri1.and(cri2);
            sql.setCriteria(cri1);
            DataObject gsDO = per.get(sql);
            Iterator itr = gsDO.getRows(tabName);
            while (itr.hasNext()) {
                Row infoRow = (Row) itr.next();
                String timestamp = infoRow.get(col1).toString();
                String header = infoRow.get(col2).toString();
                String message = infoRow.get(col3).toString();
                String img_link = infoRow.get(col4).toString();
                String link = infoRow.get(col5).toString();
                Object link_text_obj = infoRow.get(col6);
                String link_text = link_text_obj != null ? link_text_obj.toString() : "";
                String type = infoRow.get(col7).toString();
                JSONObjectWrapper obj = new JSONObjectWrapper();
                obj.put("Timestamp", timestamp);
                obj.put("Header", header);
                obj.put("Message", message);
                obj.put("Image_link", img_link);
                obj.put("Link", link);
                obj.put("Link_text", link_text);
                obj.put("Type", type);
                popupContent.put(timestamp, obj);
            }
        } catch (Exception e) {
            LOGGER.info("In getting popupcontent :" + e);//No I18N
        }
        return popupContent;
    }

    public JSONObjectWrapper fetchTidings(String type, int n) {
        JSONObjectWrapper Tidings = new JSONObjectWrapper();
        try {
            SelectQuery sq = new SelectQueryImpl(new Table(tabName));
            sq.addSelectColumn(Column.getColumn(tabName, "*"));
            Criteria cri1 = new Criteria(new Column(tabName, col7), type, QueryConstants.EQUAL);
            sq.setCriteria(cri1);
            sq.addSortColumn(new SortColumn(Column.getColumn(tabName, col1), false));
            DataObject bnDO = per.get(sq);
            int count = 0;
            if (!bnDO.isEmpty()) {
                Iterator itr = bnDO.getRows(tabName);
                int s = bnDO.size(tabName);
                n = s < n ? s : n;
                while (count < n) {
                    Row infoRow = (Row) itr.next();
                    String timestamp = infoRow.get(col1).toString();
                    String header = infoRow.get(col2).toString();
                    String message = infoRow.get(col3).toString();
                    String img_link = infoRow.get(col4).toString();
                    String link = infoRow.get(col5).toString();
                    Object link_text_obj = infoRow.get(col6);
                    String link_text = link_text_obj != null ? link_text_obj.toString() : "";
                    String index = type.concat(Integer.toString(count));
                    JSONObjectWrapper json = new JSONObjectWrapper();
                    json.put("Timestamp", timestamp);
                    json.put("Header", header);
                    json.put("Msg", message);
                    json.put("Img_link", img_link);
                    json.put("Link", link);
                    json.put("Link_text", link_text);
                    Tidings.put(index, json);
                    count++;
                }
            }
        } catch (Exception e) {
            LOGGER.info("Error in fetching Tidings: " + e);   //No I18N
        }
        return Tidings;
    }
}
