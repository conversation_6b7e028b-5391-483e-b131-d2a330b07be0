/* $Id$ */

package com.zoho.sheet.action;

import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletException;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.adventnet.zoho.websheet.model.ErrorCode;
import com.adventnet.zoho.websheet.model.ErrorCode.DisplayType;
import com.adventnet.zoho.websheet.model.ErrorCode.MsgType;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.UserProfile.PermissionType;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.LocaleMsg;
import com.zoho.sheet.util.ZSStats;

/**
 * <AUTHOR>
 */
/*
 * Purpose :- It is mapped URL "/h/a/rid" to land in right server & Start the workbook creation .. 
 */
public class CookbookAction extends StrutsRequestHandler {

    public static final Logger LOGGER = Logger.getLogger(CookbookAction.class.getName());

    @Override
    public String execute() throws java.io.IOException, ServletException {

        try {

            WorkbookContainer container = CurrentRealm.getContainer();

            container.getWorkbook(CurrentRealm.getWorkBookIdentity());
            request.setAttribute("fromCookbook", true);    //NO I18N
            request.getRequestDispatcher(DocumentUtils.getDocControllerPath(UserProfile.AccessType.AUTH, container.getResourceId(), "ropen", true, request)).forward(request, response);//No I18N


        } catch (Exception ex) {

            LOGGER.log(Level.WARNING, "Exception in cookBookAction >> ", ex);
            if (ex != null && ex.getMessage().equals(ErrorCode.ERROR_DOCUMENT_PARSE))  //Throwing exception for parse error, for any other type of error it will show internal server error (default).
            {
                ZSStats.incrementByCustomValue(ZSStats.PARSEERROR);

                UserProfile uProfile = CurrentRealm.getUserProfile();
                PermissionType permissionType = uProfile.getPermissionType();
                if (permissionType == PermissionType.READ_WRITE_SAVE_SHARE || permissionType == PermissionType.READ_WRITE_SAVE_SHARE_DELETE || permissionType == PermissionType.READ_WRITE_SAVE)   //Owner or co owner or sharedUser, show banner for revert version
                {
                    String supportMailId = LocaleMsg.getMsg("SupportMailId");
                    String supportMailLink = "<a href='mailto:" + supportMailId + "'>" + supportMailId + "</a>";
                    String[] params = {"<a href='javascript:AuditTrail.revertVersionOnError();'>", "</a>", "<a href='javascript:AuditTrail.showVersionHistory();'>", "</a>", supportMailLink};
                    response.getWriter().print(ErrorCode.getErrorMessage(ErrorCode.ERROR_DOCUMENT_PARSE, ErrorCode.ERROR_DOCUMENT_PARSE, MsgType.ERROR, DisplayType.BANNER, params));   //NO OUTPUTENCODING
                    String rid = CurrentRealm.getContainer() != null ? CurrentRealm.getContainer().getResourceId() : "";
                    LOGGER.log(Level.INFO, "[ZS_ERROR_BANNER][COOK_BOOK][" + ErrorCode.ERROR_DOCUMENT_PARSE + "][RID:" + rid + "][ URL:" + request.getRequestURL() + "]");
                } else  // read  or read-comment  users ,show banner to contact owner
                {
                    WorkbookContainer container = CurrentRealm.getContainer();
                    if (container != null) {
                        String docOwnerMailId = DocumentUtils.getZuserEmailId(container.getDocOwner());
                        String ownerMailLink = "<a href='mailto:" + docOwnerMailId + "'>" + docOwnerMailId + "</a>";
                        String[] params = {ownerMailLink};

                        response.getWriter().print(ErrorCode.getErrorMessage(ErrorCode.ERROR_DOCUMENT_PARSE_READONLY, ErrorCode.ERROR_DOCUMENT_PARSE_READONLY, MsgType.ERROR, DisplayType.BANNER, params));    //NO OUTPUTENCODING
                        LOGGER.log(Level.INFO, "[ZS_ERROR_BANNER][COOK_BOOK][" + ErrorCode.ERROR_DOCUMENT_PARSE_READONLY + "][RID:" + container.getResourceId() + "][ URL:" + request.getRequestURL() + "]");

                    } else {
                        LOGGER.log(Level.SEVERE, "Container is null", ex);
                    }
                }
            }
        }

        return null;
    }

}
