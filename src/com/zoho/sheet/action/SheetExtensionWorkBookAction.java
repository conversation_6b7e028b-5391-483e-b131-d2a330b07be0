//$Id$
package com.zoho.sheet.action;

import javax.servlet.ServletException;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import org.apache.commons.io.IOUtils;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.iam.security.UploadedFileItem;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.StoreFactory;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceType;

public class SheetExtensionWorkBookAction extends StrutsRequestHandler {
	public static final Logger LOGGER = Logger.getLogger(SheetExtensionWorkBookAction.class.getName());

	public String execute() throws java.io.IOException, ServletException {
		FileInputStream fis = null;
		BufferedInputStream bis = null;
		BufferedOutputStream out = null;
		try {
			User user = IAMUtil.getCurrentUser();
			String ownerId = "" + user.getZUID();
			int actionConstant = Integer.parseInt(request.getParameter("action"));
			byte b[] = null;
			File file = null;
			String loginName = null;
			switch (actionConstant) {
			case ActionConstants.STORE_TEMP_FILE:
				String fileExt = null;
				String fileName = null;
				SheetFileInfo sheetfileInfo = null;

				ArrayList<UploadedFileItem> uploadedFilesList = (ArrayList<UploadedFileItem>) request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST);
				if (uploadedFilesList != null && uploadedFilesList.size() > 0) {
					for (UploadedFileItem uploadedFileItem : uploadedFilesList) {
						fileName = uploadedFileItem.getFileName();
						fileExt = fileName.substring(fileName.lastIndexOf("."));
						fileExt = fileExt.toLowerCase();
						file = uploadedFileItem.getUploadedFile();
						fis = new FileInputStream(file);
						b = ClientUtils.readStream(fis);
					}
				}
				loginName = DocumentUtils.getZUserName(ownerId);
				String[] docDetails = DocumentUtils.addNewDocumentToDB(loginName, ownerId, fileName, false, Constants.NEW_DOCUMENT, true, null, null, null, false, ResourceType.WORKBOOK_NATIVE);
				String documentId = docDetails[0];
				String rid = docDetails[1];
				ownerId = ZohoFS.getOwnerZID(rid);
				loginName = DocumentUtils.getZUserName(ownerId);
				Store store = StoreFactory.getInstance().getDocumentStore(Long.parseLong(ownerId), loginName, Long.valueOf(documentId));
				sheetfileInfo = store.getFileInfo(Long.valueOf(documentId), EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_CSV);
				if (sheetfileInfo == null) {
					sheetfileInfo = store.createFileInfo(Long.valueOf(documentId), EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_CSV); // Assumed that, it is the first write ~mani
				}
				long resId = sheetfileInfo.getResourceId();
				HashMap<String, Object> writeInfo = store.write(resId, sheetfileInfo);
				OutputStream os = (OutputStream) writeInfo.get("OS");
				os.write(b);
				store.finishWrite(writeInfo);
				
				JSONObjectWrapper j = new JSONObjectWrapper();
				j.put("docId", documentId);
				j.put("resId", rid);
				HttpServletResponseWrapper.sendResponse(response, j, HttpServletResponseWrapper.MimeType.JSON);

				break;
			case ActionConstants.GET_TEMP_FILE:
				byte[] buffer = new byte[8192];
				String rId = request.getParameter("rId");
				long docId = Long.parseLong(request.getParameter("docId"));
				ownerId = ZohoFS.getOwnerZID(rId);
				store = StoreFactory.getInstance().getDocumentStore(Long.parseLong(ownerId), DocumentUtils.getZUserName(ownerId), docId);
				sheetfileInfo = store.getFileInfo(docId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_CSV);
				InputStream i = store.read(docId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_CSV);
				File f = new File("tempFile");
				OutputStream outputStream = new FileOutputStream(f);
				IOUtils.copy(i, outputStream);
				fis = new FileInputStream(f);
				bis = new BufferedInputStream(fis);
				int count;
				out = new BufferedOutputStream(response.getOutputStream());
				while ((count = bis.read(buffer)) > 0) {
					out.write(buffer, 0, count);
				}
				break;
			}

		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Error in SheetExtensionWorkBookAction :: ", e);
		} finally {
			if (out != null) {
				out.close();
			}
			if (fis != null) {
				fis.close();
			}
			if (bis != null) {
				bis.close();
			}
		}
		return null;
	}
}
