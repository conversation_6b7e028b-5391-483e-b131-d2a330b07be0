package com.zoho.sheet.action;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.connection.ZSConnection;
import com.zoho.sheet.connection.ZSConnectionFactory;
import com.zoho.sheet.conversion.Exporter;
import com.zoho.sheet.conversion.ImportExportUtil;
import com.zoho.sheet.mail.MailUtil;
import com.zoho.sheet.mail.ZSMail;
import com.zoho.sheet.parse.HtmlParserConstant;
import com.zoho.sheet.util.*;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;
import org.apache.http.HttpHeaders;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

public class SendMailDocumentAction extends StrutsRequestHandler {

	public static Logger logger = Logger.getLogger(SendMailDocumentAction.class.getName());
//	public JSONObject responseObject = new JSONObject();

	public String execute() throws Exception {
		JSONObjectWrapper responseObject = new JSONObjectWrapper();

//		Step 1.	Get a sender details like name, email, zuid.
//		Step 2. Get a receiver details like email, message, format type.
//		Step 3. Get file content from workbook.
	//  Step 4. Upload the file saved locally in step3 to transmail via upload api. 
//		Step 5. Send the mail with uploaded file upload id ( from Step 4)
		response.setContentType("text/plain");
		
	   JSONObjectWrapper previlages = new JSONObjectWrapper(((String) request.getAttribute(ZFSNGConstants.CAPABILITIES)));
       boolean _canEdit = true;
       boolean _canDownload = previlages.getBoolean("canDownload"); 
       if (previlages.has("canEdit")) {
          _canEdit = previlages.getBoolean("canEdit");//NO I18N

       }
		WorkbookContainer container = CurrentRealm.getContainer();
		Workbook workbook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());
       long _zuid         = IAMUtil.getCurrentUser().getZUID();

    	   	String sharetype = request.getParameter("sharetype"); 
    	   	String publishURL = null, publishType = null, publishName = null;
			boolean isFileNeeded = true;
			if("shareURL".equals(sharetype)) {
				isFileNeeded = false;
				publishURL 	= request.getParameter("publishURL");
				publishType = request.getParameter("publishType");
				publishName = request.getParameter("publishName");
			}
    	       	   	
			String toMailList = RemoteUtils.maskNull( (String) request.getParameter("emailids"));
			
			String sheetName  = RemoteUtils.maskNull((String) request.getParameter("sheetName"));
			String message    = RemoteUtils.maskNull((String) request.getParameter("message"));

			if( message != null ) {
				message = URLDecoder.decode(message, HtmlParserConstant.CHARTSET);
				Document doc = Jsoup.parse(message, HtmlParserConstant.CHARTSET);
				if( doc.text().length() == 0 ){
					message = null ;
				}
			}

			String subject   = RemoteUtils.maskNull((String) request.getParameter("subject"));
			
			boolean sendCopy = false;
			String password = null, fileFormat = null, imageContext = null, sheeturl = null, defualtLogoUrl = null;

			
			if(isFileNeeded) {
				sendCopy  = "true".equals(RemoteUtils.maskNull( (String)request.getParameter("sendCopy")));
				password   = RemoteUtils.maskNull((String) request.getParameter("password"));
				fileFormat = RemoteUtils.maskNull((String) request.getParameter("format"));
				imageContext = ClientUtils.getPath(ClientUtils.ResourceType.COMMON_IMAGE, request);
				sheeturl  = "https://"+EnginePropertyUtil.getSheetPropertyValue("ZohoSheetURL");//NO I18N
	//			LOGGER.info(" imageContext ::"+imageContext);
				defualtLogoUrl = sheeturl+imageContext+"sheet_logo.svg"; //NO I18N
				if(fileFormat == null) {
					fileFormat = "xlsx"; //NO I18N
				}
			}
			
			User currUser     = IAMUtil.getCurrentUser();
			long zoid         = IAMUtil.getCurrentUser().getZOID();
//			zoid = -1;

			JSONArrayWrapper mailToJSONArray = new JSONArrayWrapper();
			String senderMailId = currUser.getContactEmail();
			String senderName   = RemoteUtils.maskNull(currUser.getDisplayName());
			senderName = senderName == null ? senderMailId.split("@")[0]:senderName;

			if( toMailList != null ) {
				if ( toMailList.indexOf(",") > -1 && toMailList.split(",").length > 1) {
					String mailArray[] = toMailList.split(",");
					for (int i = 0; i < mailArray.length; i++) {
						mailToJSONArray.put(RemoteUtils.maskNull(mailArray[i]));
					}
				} else {
					mailToJSONArray.put(toMailList);
				}
				
				if( sendCopy) {
					mailToJSONArray.put(senderMailId);
				}
			  boolean _isSpamMail = 	isSpamMail(senderMailId, subject, message);
				
				if ( !mailToJSONArray.isEmpty() && !  _isSpamMail ) {
					
					if(isFileNeeded) {
						try
						{
							byte[] fileContent = getDocumentFileContent(container,fileFormat,sheetName,password);
							if( fileContent != null && fileContent.length > 0 ) {
								String docName    = container.getDocName();
								String fileUploaId   = 	TransMailUtil.transMailFileUpload(fileContent,docName+"."+fileFormat);
								if(fileUploaId != null ) {
									JSONObjectWrapper mailDetail = new JSONObjectWrapper();
									mailDetail.put(MailUtil.TRANSMAIL_MAIL_FROM, senderMailId);
									mailDetail.put(MailUtil.FROM, senderMailId);
									mailDetail.put(MailUtil.TO, mailToJSONArray);
									if(subject == null) {
										subject = getMailSubject( senderName, docName,fileFormat);
									}
									mailDetail.put(MailUtil.SUBJECT, subject);

									String _orgLogoUrl = null;

									if(zoid != -1) {
										_orgLogoUrl ="https://"+EnginePropertyUtil.getSheetPropertyValue("DOMAIN_CONTACTS_ZOHO_COM")+ "/static/file?t=org&amp;ID=" + zoid+ "&amp;fs=thumb"; //NO I18N
									}else {
										_orgLogoUrl = defualtLogoUrl;
									}
									//				            LOGGER.info(" [[_orgLogoUrl]]"+_orgLogoUrl);
									String messageWitTemplate = getMessgeWithTemplate(message, docName+"."+fileFormat, senderName, _orgLogoUrl,senderMailId);
									//				            LOGGER.info(" [[messageWitTemplate]]"+messageWitTemplate);
									mailDetail.put(MailUtil.MESSAGE, messageWitTemplate );
									mailDetail.put(MailUtil.MESSAGE_TYPE, MailUtil.MESSAGE_TYPE_HTML);
									//				            mailDetail.put("TEMPLATE_TYPE", "DEFAULT");
									JSONArrayWrapper attachmentId = new JSONArrayWrapper();
									attachmentId.put(fileUploaId);
									mailDetail.put("ATTACHMENT_ID", attachmentId);
									responseObject = (new ZSMail(null, mailDetail, MailUtil.TRANSMAIL_APPLICATION_API)).send();
									boolean isPasswordProtected = password != null ? true : false ;
									String _rid = container.getResourceId();
									emailAudit(_rid,toMailList,senderMailId,sendCopy,isPasswordProtected,fileFormat);
								}
							}
						}
						catch(ProhibitedActionException e)
						{
							logger.log(Level.SEVERE, "[SEND_MAIL][Exception] Exception while sending mail.", e);
							JSONObjectWrapper errObj = ErrorCode.getProhibitedDialogErrorMsg(e);
							response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
							HttpServletResponseWrapper.sendResponse(response,errObj, HttpServletResponseWrapper.MimeType.JSON);
							return null;
						}
						catch (Exception e)
						{
							logger.log(Level.SEVERE, "[SEND_MAIL][Exception] Exception while sending mail.", e);
							JSONObjectWrapper errObj = ErrorCode.getErrorMessage(ErrorCode.ERROR_ACTION_EXECUTION, null, ErrorCode.MsgType.ERROR, ErrorCode.DisplayType.BANNER);
							response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
							HttpServletResponseWrapper.sendResponse(response,errObj, HttpServletResponseWrapper.MimeType.JSON);
							return null;
						}
					}
					else {
						JSONObjectWrapper mailDetail = new JSONObjectWrapper();
			            mailDetail.put(MailUtil.TRANSMAIL_MAIL_FROM, senderMailId);
			            mailDetail.put(MailUtil.FROM, senderMailId);
			            mailDetail.put(MailUtil.TO, mailToJSONArray);
			            senderName = currUser.getFullName();
			            subject = getMailSubjectForURLShare(senderName, publishType, publishName);
			            String messageWithFormat = getMessageForURLSharing(senderName, publishURL, publishType, publishName, message);
			            
			            mailDetail.put(MailUtil.SUBJECT, subject);
			            mailDetail.put(MailUtil.MESSAGE, messageWithFormat);
			            mailDetail.put(MailUtil.MESSAGE_TYPE, MailUtil.MESSAGE_TYPE_HTML);
			            responseObject = (new ZSMail(null, mailDetail, MailUtil.TRANSMAIL_APPLICATION_API)).send();
					}
				}
				if(_isSpamMail) {
					   JSONObjectWrapper errorObject = new JSONObjectWrapper();
			           errorObject.put("ErrorMSGKey", "SpamCheckAlertMessage");
			           responseObject.put("ERROR", errorObject.toString());
					   HttpServletResponseWrapper.sendResponse(response,responseObject, HttpServletResponseWrapper.MimeType.JSON);
			           return null;
				}
			}
//			try {
//					String userAgent = request.getHeader("user-agent");
//					String referer = request.getHeader("referer");
//					String remoteIpAddress = request.getRemoteAddr();
//					ZohoFS.auditResourceDownloadActivity(container.getResourceId(), userAgent, referer, remoteIpAddress);
//			}
//			catch(Exception e) {
//				logger.log(Level.INFO, " Exception occured while adding Aduit log for rid  {0}", new Object[]{container.getResourceId(),e});
//			}
			responseObject.put("status", DataAPIConstants.SUCCESS);
		HttpServletResponseWrapper.sendResponse(response,responseObject, HttpServletResponseWrapper.MimeType.JSON);
		return null;
    }

	public static void emailAudit(String rid, String toMailList, String senderMailId , boolean sendCopy , boolean isPasswordProtected , String fileFormat) throws Exception {
		try
		{
		JSONObjectWrapper _emailAttachmentEventInfo = new JSONObjectWrapper();
		JSONObjectWrapper _emailAttachmentEvent = new JSONObjectWrapper();
		JSONObjectWrapper _downloadEvent = new JSONObjectWrapper();
		JSONObjectWrapper _auditinfo = new JSONObjectWrapper();
		JSONObjectWrapper _attributes = new JSONObjectWrapper();
		JSONObjectWrapper _data = new JSONObjectWrapper();
		JSONObjectWrapper _payLoad = new JSONObjectWrapper();
		_emailAttachmentEventInfo.put("isScheduledAction", false);
		_emailAttachmentEventInfo.put("selfCopy", sendCopy);
		_emailAttachmentEventInfo.put("isPasswordProtected", isPasswordProtected);
		_emailAttachmentEventInfo.put("fileExtension", fileFormat);
		_emailAttachmentEventInfo.put("toEmailId", toMailList);
		_emailAttachmentEventInfo.put("fromEmailId", senderMailId);
		_emailAttachmentEvent.put("emailAttachmentEvent", _emailAttachmentEventInfo);
		_downloadEvent.put("downloadEvent", _emailAttachmentEvent);
		_auditinfo.put("auditinfo", _downloadEvent);
		_attributes.put("resource_id", rid);
		_attributes.put("event_time", System.currentTimeMillis());
		_attributes.put("action_type", "1");
		_attributes.put("auditinfo", _downloadEvent);
		_data.put("attributes", _attributes);
		_data.put("type", "events");
		_payLoad.put("data", _data);
		String _docsApiUrl = "https://".concat(EnginePropertyUtil.getSheetPropertyValue("DOMAIN_WORKDRIVE_API")).concat("/api/v1/events");//No i18N
		ZSConnection zsconn = (new ZSConnectionFactory(_docsApiUrl, "POST")).getConnection();//No i18N
		String _remoteUserIpSignature = IAMUtil.generateRemoteUserIPSignature(IAMUtil.getCurrentRequest());
		String _userAgent = IAMUtil.getUserAgent(IAMUtil.getCurrentRequest());
		zsconn.setRemoteIpWithSignature(_remoteUserIpSignature);
		zsconn.setZUserAgent(_userAgent);
		String token="";
		if(IAMUtil.getCurrentTokenType() == IAMUtil.TokenType.OAUTHTOKEN)
		{
			token="Zoho-oauthtoken "+IAMUtil.getCurrentTicket(); //No i18N
		}
		else
		{
			token = "Zoho-ticket "+IAMUtil.getCurrentTicket(); //No i18N
		}
		
		String headerKey[]={"Authorization"}; //No i18N
		String headerValue[]={token};
		zsconn.setRequestHeader(headerKey,headerValue);
		zsconn.setContentType("application/json");
        zsconn.setWrittingBytes(_payLoad.toString());
		zsconn.setAuthentication();
		zsconn.process();
		}
		catch(Exception e)
		{
			logger.log(Level.INFO, "[EMAIL_AUDIT_LOG][Exception] Exception while auditing email document :{0} ", new Object[]{e});
		}
	}
        public static byte[] getDocumentFileContent( WorkbookContainer workBookContainer, String fileFormat, String sheetName, String password) throws Exception {
		
		Exporter exporter = null;
		if(sheetName != null ) {
			exporter = new Exporter(workBookContainer, fileFormat, sheetName);
		}else {
			exporter = new Exporter(workBookContainer, fileFormat);
		}
		
		/* -- Get File content -- */
		try {
			logger.log(Level.INFO, "[CONVERSION:EXPORT] SendMailDocumentAction getDocumentFileContent {0}", new Object[]{fileFormat});
			byte[] content = null;
			boolean isEnabled = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("enableHeadlessPDF"));  // No I18N
            if (fileFormat.equalsIgnoreCase(EngineConstants.FILEEXTN_PDF) && isEnabled) {
                content = exporter.getDocumentPDFBytes();
            } else if (fileFormat.equalsIgnoreCase(EngineConstants.FILEEXTN_CSV) || fileFormat.equalsIgnoreCase(EngineConstants.FILEEXTN_TSV)){
            	content = exporter.getDocumentCsvTsvBytes();
            } else if (fileFormat.equalsIgnoreCase(EngineConstants.FILEEXTN_XLSX)){
            	content = exporter.getXLSXBytes();
            } else {
            	content = exporter.getDocumentBytes();
            }
            if( password != null) {
            	content = ImportExportUtil.encryptDocument(fileFormat, content, password);
			}
			return content;
		}
		catch(Exception e)
		{
			logger.log(Level.INFO, "[CONVERSION:EXPORT][Exception] Exception while getting document file content", e);
			throw e;
		}
	}
	
	public static String getMailSubject( String senderName, String fileName, String fileFormat) {
		return LocaleMsg.getDynamicMsg("SendMail.MailSubject",new String[] {senderName,fileName+ "." + fileFormat }); //NO I18N
	}

	
	private String getMailSubjectForURLShare(String senderName, String publishType, String publishName) {
		String subject = null;
		switch(publishType){
			case "document" : 
				subject = publishName + " - Published URL";	//NO I18N
				break;
			case "sheet" : 
				subject = publishName + " - Published sheet URL";	//NO I18N
				break;
			case "range" : 
				subject = "Published range URL";	//NO I18N
				break;
		}
		return subject;
	}
	
	private String getMailBodyMessage(String message,String fileName, String fileFormat,String orgId) throws Exception {
		String msg = null;
		if (message != null) {
			msg = message.replaceAll("\n", "<br>");// // No I18N
			msg = "<div><div style='background-color: rgb(236,236,236);padding: 30.0px 0;font-family: Lucida Grande , Segoe UI , Arial , sans-serif;font-size: 12.0px;'><div style='width: 647.0px;min-height: 120.0px;margin: 0 auto;padding-top: 1.0px;background-color: rgb(68,142,28);'><div style='height: 100.0%;margin-left: 8.0px;width: 642.0px;min-height: 120.0px;background-color: rgb(255,255,255);'><div style='height: 85.0px;'><img src='https://" //NO I18N
					+ EnginePropertyUtil.getSheetPropertyValue("DOMAIN_CONTACTS_ZOHO_COM")//NO I18N
					+ "/static/file?t=org&amp;ID=" + orgId //NO I18N
					+ "&amp;fs=thumb' style='float: right;margin: 28.0px 55.0px 0 0;height: 26.0px;'></div><div style='padding: 0 55.0px;margin: 0;line-height: 1.5em;'><p style='margin: 15.0px 0;'>"
					+ LocaleMsg.getDynamicMsg("SendMail.MailDefaultMsg",new String[] { "<b>",fileName + "." + fileFormat, "</b>" }) //NO I18N
					+ "<br><br>" + msg
					+ "</p><p style='margin: 15.0px 0;'> </p><p style='margin: 15.0px 0;'> </p><p style='margin: 15.0px 0;'></p></div><div style='margin: 0 auto;padding: 50.0px 0 5.0px 0;'><div style='margin: 0 auto;margin-bottom: 10.0px;width: 440.0px;height: 1.0px;border-top: 1.0px solid rgb(245,245,245);border-bottom: 1.0px solid rgb(245,245,245);'></div><p style='margin: 7.0px 0;text-align: center;font-size: 11.0px;color: rgb(138,138,138);'>If you think this is SPAM, please report to <a href='mailto:<EMAIL>' target='_blank' mailid='abuse%40zoho.com' subj=''><EMAIL></a>.</p><p style='margin: 7.0px 0;text-align: center;font-size: 11.0px;color: rgb(183,183,183);'>Zoho Corporation 4141 Hacienda Drive Pleasanton, CA 94588, USA.</p></div></div></div></div></div>";
		} else {
			msg = "<div><div style='background-color: rgb(236,236,236);padding: 30.0px 0;font-family: Lucida Grande , Segoe UI , Arial , sans-serif;font-size: 12.0px;'><div style='width: 647.0px;min-height: 120.0px;margin: 0 auto;padding-top: 1.0px;background-color: rgb(68,142,28);'><div style='height: 100.0%;margin-left: 5.0px;width: 642.0px;min-height: 120.0px;background-color: rgb(255,255,255);'><div style='height: 85.0px;'><img src='https://" //NO I18N
					+ EnginePropertyUtil.getSheetPropertyValue("DOMAIN_CONTACTS_ZOHO_COM")//NO I18N
					+ "/static/file?t=org&amp;ID=" + orgId//NO I18N
					+ "&amp;fs=thumb' style='float: right;margin: 28.0px 55.0px 0 0;height: 26.0px;'></div><div style='padding: 0 55.0px;margin: 0;line-height: 1.5em;'><p style='margin: 15.0px 0;'>"
					+ LocaleMsg.getDynamicMsg("SendMail.MailDefaultMsg", //NO I18N
							new String[] { "<b>",fileName + "." + fileFormat, "</b>" })
					+ "</p><p style='margin: 15.0px 0;'> </p><p style='margin: 15.0px 0;'> </p><p style='margin: 15.0px 0;'></p></div><div style='margin: 0 auto;padding: 50.0px 0 5.0px 0;'><div style='margin: 0 auto;margin-bottom: 10.0px;width: 440.0px;height: 1.0px;border-top: 1.0px solid rgb(245,245,245);border-bottom: 1.0px solid rgb(245,245,245);'></div><p style='margin: 7.0px 0;text-align: center;font-size: 11.0px;color: rgb(138,138,138);'>If you think this is SPAM, please report to <a href='mailto:<EMAIL>' target='_blank' mailid='abuse%40zoho.com' subj=''><EMAIL></a>.</p><p style='margin: 7.0px 0;text-align: center;font-size: 11.0px;color: rgb(183,183,183);'>Zoho Corporation 4141 Hacienda Drive Pleasanton, CA 94588, USA.</p></div></div></div></div></div>";
		}
		return msg;
	}
	

	
	public static String  getMessgeWithTemplate(String mailMessage, String docName, String senderName, String logoUrl, String senderEmail) {
		String sheeturl  = "https://"+EnginePropertyUtil.getSheetPropertyValue("ZohoSheetURL");//NO I18N
		String messageString = "<table";//NO I18N
		String iosImgPath = sheeturl+"/sheet/images/IOS.png";//NO I18N
//		String iosAppSourceLink = EnginePropertyUtil.getSheetPropertyValue("ZSHEET_APPSTORE_URL");//NO I18N
		String androidImgPath = sheeturl+"/sheet/images/Android.png";//NO I18N
		String androidAppSourceLink =EnginePropertyUtil.getSheetPropertyValue("PLAYSTORE_URL");//NO I18N
		String contactUsLink = Constants.rebrand_Properties.getProperty("ContactUsURL");
		String zohoPrivacyLink = Constants.rebrand_Properties.getProperty("PrivacyPolicyURL");
		String reportAbuseLink = EnginePropertyUtil.getSheetPropertyValue("ReportAbuseURL");//NO I18N
		String contactUs = LocaleMsg.getMsg("SendMail.ContactUs");//NO I18N
		String reportAbuse=LocaleMsg.getMsg("PublishedView.ReportAbuse");//NO I18N
		String privacyPolicy=LocaleMsg.getMsg("ExcelViewer.Footer.ExtraLink.PrivacyPolicy");//NO I18N
		String _defaultMessage = LocaleMsg.getDynamicMsg("SendMail.MailDefaultMsg", new String[] {senderEmail, "<span style='font-weight: bold'>",docName, "</span>" });
		String message = mailMessage == null ? _defaultMessage : mailMessage;
		String hello = LocaleMsg.getMsg("SendMail.Hello");        //No I18N
		String sheetUrl = EnginePropertyUtil.getSheetPropertyValue("AboutZohoSheetURL");//NO I18N
		messageString = messageString.concat(" style='font-family: inherit;font-size: inherit;width: 100%;background: #f6f6f6;table-layout: fixed;color: #1d1d1d;padding: 20px 15px;box-sizing: border-box;border: 1px solid #eee' cellpadding='0' cellspacing='0'>") ;
		messageString = messageString.concat("<tr><td><table style='border-spacing: 0;margin: 0 0 10px'><tr><td>");
		messageString = messageString.concat("<a href='"+sheetUrl+"' target='_blank'><img src='"+logoUrl+"' width='85px'></a></td></tr></table>");
		messageString = messageString.concat("<table style='border-spacing: 0;margin: 0 0 5px;background: #fff;border: 1px solid #e9e9e9;padding: 30px 20px;width: 100%;box-sizing: border-box;vertical-align: top;min-height: 200px'>");
		if(mailMessage == null) {
			messageString = messageString.concat("<tr><td style='vertical-align: top;'><div><p style='margin: 0 0 10px'> <span>" + hello + "</span> <span>,</span></p>");
		}
		else{
			messageString = messageString.concat("<tr><td style='vertical-align: top;'><div>");
		}
		messageString = messageString.concat("<p>"+ message+" </p></div></td></tr></table>");
		messageString = messageString.concat("<table style='width: 100%;color: #6D6D6D;font-size: 12px'>");
		messageString = messageString.concat("<tr><td> This e-mail is generated from Zoho Sheet service and sent to you by "+ senderEmail+ ". If you think this is spam, please do <NAME_EMAIL>.</td></tr><tr><td>");
		messageString = messageString.concat( "<a style='margin-right: 5px;padding: 0 5px 0 0;border-right: 1px solid #979797;display: inline-block;color: #6D6D6D;text-decoration: none;' href='"+contactUsLink+"' target='_blank'>"+ contactUs+"</a>");
		messageString = messageString.concat( "<a style='margin-right: 5px;padding: 0 5px 0 0;border-right: 1px solid #979797;display: inline-block;color: #6D6D6D;text-decoration: none;' href='"+zohoPrivacyLink+"' target='_blank'>"+ privacyPolicy+"</a>");
		messageString = messageString.concat( "<a style='margin-right: 5px;padding: 0 5px 0 0;display: inline-block;color: #6D6D6D;text-decoration: none;' href='"+reportAbuseLink+"' target='_blank'>"+ reportAbuse+"</a></td>");
		messageString = messageString.concat( " </tr></table></td></tr></table>");

		return messageString;
		
	}
	public boolean isSpamMail(String from , String subject , String message ){
		  boolean _isSpam = false ;
		  try{
			  String _spamCheckDomainUrl = EnginePropertyUtil.getSheetPropertyValue("spamCheckDomainUrl"); //No I18N
			  String _projectAppAccessKey = EnginePropertyUtil.getSheetPropertyValue("projectAppAccessKey"); //No I18N //"hGcK4Ikw4";  //No I18N
			  String _iscSignature = SecurityUtil.sign();
			  JSONObjectWrapper _dataParams = new JSONObjectWrapper();
			  _dataParams.put("FROM", from);
			  _dataParams.put("SUBJECT", subject);
			  _dataParams.put("event", "mail_content_check");
			  _dataParams.put("content", message);
			  Object[] paramName = {"content" ,"event","data_params" }; //NO I18N
			  Object[] paramVal = {message,"mail_content_check",_dataParams}; //NO I18N
			  String _spamCheckApiUrl = _spamCheckDomainUrl.concat("/api/v1/project-apps/").concat(_projectAppAccessKey).concat("/spam-check/internal/?iscsignature=").concat(_iscSignature);
			  ZSConnection zsconn =  (new ZSConnectionFactory(_spamCheckApiUrl,"POST")).getConnection(true);//No I18N
			  zsconn.setParameter(paramName, paramVal);
			  zsconn.process();
			  HashMap _respMap = zsconn.getResponseMap();
			  // logger.log(Level.INFO, "[invokeSpamCheck ][ getResponseMap::] :{0}", _respMap);
//		  logger.log(Level.INFO, "[invokeSpamCheck ][ RESP_MESSAGE:] :{0}", _respMap.containsKey("RESP_MESSAGE"));
			  if( _respMap != null && _respMap.containsKey("RESP_MESSAGE")) {
//			  logger.log(Level.INFO, "[inssssssss _label::] :");
				  JSONObjectWrapper  _respObject =  new JSONObjectWrapper((String) _respMap.get("RESP_MESSAGE"));
//			  logger.log(Level.INFO, "[_respObject _label::] :{0}",_respObject);
				  String _label = _respObject.has("LABEL") ? _respObject.getString("LABEL"): null;
//			  logger.log(Level.INFO, "[invokeSpamCheck _label::] :{0}", _label);
				  if( _label != null  && ( "JUNK".equalsIgnoreCase(_label) || "SPAM".equalsIgnoreCase(_label)) ) {
					  _isSpam = true;
				  }
			  }
		  }catch (Exception e){
			  logger.log(Level.WARNING,"Exception occurred while checking isSpamMail :: {0}",e);
		  }
		  return _isSpam ;
	}
	
	public String getMessageForURLSharing(String senderName, String publishURL, String publishType, String publishName, String message) {
		String newline = "<br>";
		String msg = "";
		switch(publishType) {
			case "document" :
				msg = msg.concat(senderName + " has shared the published link of the file \"" + publishName + "\" with you.");
				break;
			case "sheet" :
				msg = msg.concat(senderName + " has shared the published link of the sheet \"" + publishName + "\" with you.");
				break;
			case "range" :
				msg = msg.concat(senderName + " has shared the published link of a spreadsheet range with you.");
				break;
		}
		msg = msg.concat(newline + "Published link : " + publishURL);
		if(message != null) {
			msg = msg.concat(newline + IAMEncoder.encodeHTML(message));
		}
		msg = msg.concat(newline + newline + "Note : Any changes made to the published version will not be saved to the original file.");
		return msg;
	}
}
