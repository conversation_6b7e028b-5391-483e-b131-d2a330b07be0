/* $Id$ */
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.zoho.sheet.action;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ContainerSynchronizer;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;

import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletException;

/**
 *
 * <AUTHOR>
 */
public class InternalServerCallAction extends StrutsRequestHandler {

    public static Logger logger = Logger.getLogger(InternalServerCallAction.class.getName());

    public String execute() throws java.io.IOException, ServletException {
        String zfsngVersionNo = null;
//        String loginName = null;
        String zfsngVersionId = null;
        JSONObjectWrapper responseJSON = new JSONObjectWrapper();
        if(Constants.IS_DISASTER_RECOVERY_SERVER){
            responseJSON.put("RESULT", "false");
            responseJSON.put("MESSAGE", "version revert falied");
			HttpServletResponseWrapper.sendResponse(response, responseJSON, HttpServletResponseWrapper.MimeType.JSON);
            return null;
        }
        String rid = request.getParameter("rid");
        String reqUri = request.getRequestURI();
        if(reqUri.contains("version")){
	        try {
	                zfsngVersionId = request.getParameter("version_id");
					String zuid = request.getParameter("zuid");
					String currentUserZuid = request.getParameter("currentuserid");
	                String docsSpaceId = ZohoFS.getOwnerZID(rid);
	                if (!ZFSNGConstants.VERSION_UNSAVED.equals(zfsngVersionId)) {
	                	String versionInfoForId = ZohoFS.getVersionInfoForId(docsSpaceId, rid, zfsngVersionId);
	                	JSONArrayWrapper versionInfo = JSONArrayWrapper.fromString(versionInfoForId);
	                    zfsngVersionNo = versionInfo.getJSONObject(0).optString("version_number"); //No I18N
	                }
	                String url = ClientUtils.getServerURL(request, false, null, false, true);
	                String ticket = IAMUtil.getCurrentTicket();
	
	                //internal revert call to documentaction.
	                JSONObjectWrapper paramsJson = new JSONObjectWrapper();
	                paramsJson.put("action", ActionConstants.CHECKIN_CHECKOUT);
	                paramsJson.put("zfsngVersionNo", zfsngVersionNo);
	                paramsJson.put("proxyURL", "internalservercall");//No I18N
	                paramsJson.put("iscsignature", SecurityUtil.sign());
	                paramsJson.put("ticket", ticket);
	                paramsJson.put("rid", rid);
	                paramsJson.put("RMNAME", -1);
					paramsJson.put("zuid", currentUserZuid != null && !currentUserZuid.isEmpty() ? currentUserZuid : zuid);
	                String isRevertComplete = ContainerSynchronizer.postURLConnection(url, rid, paramsJson.getJsonObject());
	                if (isRevertComplete != null && isRevertComplete.trim().equals("true")) {
	                    responseJSON.put("RESULT", "true");
	                    responseJSON.put("MESSAGE", "version revert success");
	                } else {
	                    responseJSON.put("RESULT", "false");
	                    responseJSON.put("MESSAGE", "version revert falied");
	                }
	        } catch (Exception e) {
	            responseJSON.put("RESULT", "false");
	            responseJSON.put("MESSAGE", "version revert falied");
	            logger.log(Level.WARNING, "error in revert version on check in check out case :: " + e.getMessage());
	        }
			HttpServletResponseWrapper.sendResponse(response, responseJSON, HttpServletResponseWrapper.MimeType.JSON);
        }else if(reqUri.contains("/operation")){//No I18N
        		try{
            		if(RedisHelper.hlen(RedisHelper.ACTIONS_LIST + rid) > 0) {
					JSONObjectWrapper paramsJson = new JSONObjectWrapper();
					paramsJson.put("action", ActionConstants.CREATE_VERSION);
					paramsJson.put("rid", rid);
					paramsJson.put("proxyURL", "internalservercall");//No I18N
					paramsJson.put("iscsignature", SecurityUtil.sign());
					paramsJson.put("RMNAME", -1);
					String url = ClientUtils.getServerURL(request, false, null, false, true);
					String isSaved = ContainerSynchronizer.postURLConnection(url, rid, paramsJson.getJsonObject());
					if (isSaved != null && isSaved.trim().equals("true")) {
						responseJSON.put("RESULT", "true");
						responseJSON.put("MESSAGE", "Saved Successfully");
					} else {
					    responseJSON.put("RESULT", "false");
					    responseJSON.put("MESSAGE", "delta save falied");
					}
            		}else {
            			responseJSON.put("RESULT", "true");
    					responseJSON.put("MESSAGE", "No delta changes to save");
            		}
    			}catch(Exception e){
    				responseJSON.put("RESULT", "false");
    				responseJSON.put("MESSAGE", "error while saving");
    				logger.log(Level.WARNING, "error while saving :: " + e);
    			}
			HttpServletResponseWrapper.sendResponse(response, responseJSON, HttpServletResponseWrapper.MimeType.JSON);
        }
        return null;
        
    }
}