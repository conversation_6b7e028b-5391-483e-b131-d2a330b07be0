/* $Id$ */
package com.zoho.sheet.action;


import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.util.LocaleMsg;
import com.zoho.zfsng.client.ZohoFS;


/**
 * <AUTHOR>
 *
 */
public class CheckInCheckOutAction {
	private static final Logger LOGGER = Logger.getLogger(CheckInCheckOutAction.class.getName());
	
	public static JSONObjectWrapper checkoutSpreadsheet(WorkbookContainer container, UserProfile profile, String resourceId) {
		JSONObjectWrapper responseObj = new JSONObjectWrapper();
		try {
			String currentUserZuid = profile.getZUserId();
			String versionLabel = LocaleMsg.getMsg("VersionLabel.BeforeCheckOut");
			container.save(versionLabel, profile.getUserName(), true);
			boolean isLocked = ZohoFS.lockResource(container.getDocsSpaceId(), resourceId, currentUserZuid, ZohoFS.getTopVersion(container.getDocsSpaceId(), resourceId));
			responseObj.put("result", isLocked);
			responseObj.put("lockedByZuid", currentUserZuid);
			responseObj.put("lockedBy", DocumentUtils.getZFullName(currentUserZuid));
		} catch(Exception e) {
			responseObj.put("result", false);
			e.printStackTrace();
		}
		return responseObj;
	}
	
	public static JSONObjectWrapper checkinSpreadsheet(WorkbookContainer container, UserProfile profile, String resourceId) {
		JSONObjectWrapper responseObj = new JSONObjectWrapper();
		try
		{
			String currentUserZuid = profile.getZUserId();
			String versionLabel = LocaleMsg.getMsg("VersionLabel.BeforeCheckIn");
			container.save(versionLabel, profile.getUserName(), true);
			boolean isUnLocked = ZohoFS.unLockResource(container.getDocsSpaceId(), resourceId, currentUserZuid);
			responseObj.put("result", isUnLocked);
		}
		catch(Exception e)
		{
			responseObj.put("result", false);
			e.printStackTrace();
		}
		return responseObj;
	}	
}
