/*$Id:$*/
package com.zoho.sheet.action;

import com.adventnet.wms.api.CollaborationApi;
import com.adventnet.wms.api.WmsApi;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.UserProfile.PermissionType;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.authorization.grasper.impl.DocumentAuthorizer;
import com.zoho.sheet.authorization.util.AuthorizationUtil;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.zfsng.client.ResourcePermissionInfo;
import com.zoho.zfsng.client.ZohoFS;
import java.util.logging.Logger;

public class WmsNotification extends StrutsRequestHandler {
    
	public static final Logger LOGGER = Logger.getLogger(WmsNotification.class.getName());
        
        @Override
		public String execute() throws Exception {
        String userName = request.getParameter("userIdentity");
        String userIdentity=String.valueOf(DocumentUtils.getZUID(userName)); 
  
        WorkbookContainer container = CurrentRealm.getContainer();
        String resourceId=container.getResourceId();

        UserProfile	profile	 =	container.getUserProfile(userIdentity);

        PermissionType currPermType=null;
        ResourcePermissionInfo rPerm=AuthorizationUtil.getResourcePermissions(userIdentity, resourceId);
        String RPerms=String.valueOf(rPerm);
        JSONObjectWrapper ResPerm=new JSONObjectWrapper(RPerms);

        int permLevel = -1;
        String action=request.getParameter("action");
        String lock_status=null;
        JSONObjectWrapper respObj = new JSONObjectWrapper();
        respObj.put(JSONConstants.DIRECT_UPDATE, true); //NO I18N
        respObj.put(JSONConstants.DOC_ID, container.getDocId()); //NO I18N
        respObj.put(JSONConstants.COLLAB_ID, ZohoFS.getCollabId(container.getDocsSpaceId(), resourceId)); 
        respObj.put(JSONConstants.LOGIN_NAME, container.getDocOwner()); //NO I18N
        respObj.put(JSONConstants.RECEIVER, profile.getUserName()); //NO I18N
        respObj.put(JSONConstants.ACTION_BY, request.getParameter("actionBy")); //NO I18N
        String wmsRawSessionIds[] = profile.getWmsRawSessionIds();
        PermissionType prevPermType = null;
        if(ResPerm.has("maxpermission"))
		{
			int maxPerm=  Integer.parseInt(ResPerm.getString("maxpermission"));
        	//String capabilities = ZohoFS.getCapabalitiy(userIdentity, container.getResourceId());
			PermissionType userPerm= AuthorizationUtil.getPermissionType(maxPerm);
			currPermType=userPerm;
			if (userPerm==PermissionType.READ_WRITE_SAVE)
			{
				permLevel = Constants.READ_WRITE_PERMISSION_LEVEL;
				
			}
			else if(userPerm==PermissionType.READ_WRITE_SAVE_SHARE)
			{
				 permLevel = Constants.CO_OWNER_PERMISSION_LEVEL;
			}
			else if(userPerm==PermissionType.READ)
			{
				permLevel = Constants.READ_ONLY_PERMISSION_LEVEL;
			}
			else if(userPerm==PermissionType.READ_COMMENT)
			{
				permLevel = Constants.READ_COMMENT_PERMISSION_LEVEL;
			}
		 }

	     prevPermType=profile.getPermissionType();
	     
	     if(Integer.parseInt(action) == ActionConstants.CHECKIN_CHECKOUT)
	     {
	    	 	respObj.put(JSONConstants.ACTION, ActionConstants.CHECKIN_CHECKOUT);
	    	 	if(ResPerm.has("lock_status"))
	    	 	{
	    	 		respObj.put("locked_by", DocumentUtils.getZFullName(ResPerm.getString("locked_by")));
	    	 	}
	    	 	for (int j = 0; j < wmsRawSessionIds.length; j++)
	        {
	    	 		String wmsRawSessionId = wmsRawSessionIds[j];
	    	 		CollaborationApi.sendMessage(profile.getZUserId(), profile.getZUserId(), container.getCollabId(), profile.getZUserId(), wmsRawSessionId, respObj);
	        }
	     }

		 else if(!Constants.IS_DISASTER_RECOVERY_SERVER && profile.getAccessType() != AccessType.PUBLIC_EXTERNAL && profile.getAccessType() != AccessType.PUBLIC_ORG && profile.getAccessType() != AccessType.RANGE_PUBLIC_EXTERNAL && profile.getAccessType() != AccessType.RANGE_PUBLIC_ORG && profile.getAccessType() != AccessType.SHEET_PUBLIC_EXTERNAL && profile.getAccessType() != AccessType.SHEET_PUBLIC_ORG && profile.getPermissionType() !=currPermType )
		 {
			  respObj.put(JSONConstants.ACTION, ActionConstants.SHARE_PERMISSION_CHANGE); //NO I18N
			  if(DocumentAuthorizer.getPermissionLevel(currPermType) != -1)
			  {
	        		  profile.setPermissionType(currPermType);
	        		  respObj.put(JSONConstants.PERMISSION_TO, permLevel); //NO I18N
	        		  respObj.put(JSONConstants.PERMISSION_FROM, DocumentAuthorizer.getPermissionLevel(prevPermType)); //NO I18N	
              }
			  for (int j = 0; j < wmsRawSessionIds.length; j++)
              {
                  String wmsRawSessionId = wmsRawSessionIds[j];
                  CollaborationApi.sendMessage(profile.getZUserId(), profile.getZUserId(), container.getCollabId(), profile.getZUserId(), wmsRawSessionId, respObj);
              }
		 }
		 else if(action.equals(String.valueOf(ActionConstants.SHARE_REMOVE_BYOWNER)))
        	 {
		     if(permLevel != -1)
		     {
		          respObj.put(JSONConstants.PERMISSION_TO, permLevel); //NO I18N
		          respObj.put(JSONConstants.PERMISSION_FROM, DocumentAuthorizer.getPermissionLevel(prevPermType)); //NO I18N
		     }
			 respObj.put(JSONConstants.ACTION, ActionConstants.SHARE_REMOVE_BYOWNER); //NO I18N
	         WmsApi.sendCustomMessage(profile.getUserName(), respObj);
        	 }
         	    
	/*	if(lock_status!=null && (profile.getPermissionType() != PermissionType.NONE))
		{
        		for (int j = 0; j < wmsRawSessionIds.length; j++)
            {
        			String wmsRawSessionId = wmsRawSessionIds[j];
        			if("true".equals(lock_status))
        			{ 	
        				if(DocumentAuthorizer.getPermissionLevel(currPermType) != -1)
        				{
        					respObj.put(JSONConstants.PERMISSION_TO, Constants.READ_ONLY_PERMISSION_LEVEL); //NO I18N
        					respObj.put(JSONConstants.PERMISSION_FROM, DocumentAuthorizer.getPermissionLevel(prevPermType)); //NO I18N
        			    }
        	            	if(DocumentAuthorizer.getPermissionLevel(prevPermType)!=Constants.READ_ONLY_PERMISSION_LEVEL)
        	            {
        	            		CollaborationApi.sendMessage(profile.getZUserId(), profile.getZUserId(), container.getCollabId(), profile.getZUserId(), wmsRawSessionId, respObj);
        	            }
        			 }
				 else if("false".equals(lock_status))
			     {
					 if(DocumentAuthorizer.getPermissionLevel(currPermType) != -1)
					 {
						   respObj.put(JSONConstants.PERMISSION_TO, permLevel); //NO I18N
						   respObj.put(JSONConstants.PERMISSION_FROM, Constants.READ_ONLY_PERMISSION_LEVEL); //NO I18N
						   if(DocumentAuthorizer.getPermissionLevel(currPermType)!=Constants.READ_ONLY_PERMISSION_LEVEL)
						   {
							   CollaborationApi.sendMessage(profile.getZUserId(), profile.getZUserId(), container.getCollabId(), profile.getZUserId(), wmsRawSessionId,respObj);
						   }
					 }
			     }	
            	}
      	}		*/

		HttpServletResponseWrapper.sendResponse(response, respObj, HttpServletResponseWrapper.MimeType.JSON);
        return null;
	}
}
