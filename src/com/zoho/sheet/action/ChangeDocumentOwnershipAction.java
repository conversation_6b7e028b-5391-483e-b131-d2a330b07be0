/* $Id$ */
package com.zoho.sheet.action;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceStatus;

import java.io.PrintWriter;
import java.util.logging.Level;
import java.util.logging.Logger;


public final class ChangeDocumentOwnershipAction extends StrutsRequestHandler {

	public final static Logger LOGGER = Logger.getLogger(ChangeDocumentOwnershipAction.class.getName());
    @Override
	public String execute() throws Exception {
		try{
			PrintWriter out = response.getWriter();
			JSONObjectWrapper obj = new JSONObjectWrapper();
			//boolean stopChangeOwner = true;
			if(Constants.IS_DISASTER_RECOVERY_SERVER){
				
				obj.put("RESULT","false");
				obj.put("MESSAGE","change owner failed");
				LOGGER.info("obj ..."+obj.toString());//No I18N
				out.println(obj); // NO OUTPUTENCODING
				return null;
			}
			String resourceId = request.getParameter("rid");
			String fromUserId = request.getParameter("oldowner");
			String toUserId = request.getParameter("newowner");
			ResourceInfo resInfo = ZohoFS.getResourceInfo(resourceId);

			if(resInfo.getStatus() != ResourceStatus.CHANGE_OWNER_PROGRESS){
				LOGGER.info("CHANGEOWNER : resourcestatus is incorrect. RID:"+resourceId+" fromUserId:"+fromUserId+" toUserId:"+toUserId);//No I18N
				obj.put("RESULT","false");
				obj.put("MESSAGE","change owner failed");	
				out.println(obj); // NO OUTPUTENCODING 
				return null;
			}
			/*
			Long zoid = -1l;//DocumentUtils.getZOID(fromUserId);
			String newowner = DocumentUtils.getZUserName(toUserId);
			
            JSONObject paramsJson = new JSONObject();
            paramsJson.put("action", ActionConstants.CHANGE_OWNER);
            paramsJson.put("proxyURL", "internalservercall");
            paramsJson.put("iscsignature", SecurityUtil.sign());	
            paramsJson.put("docName", resInfo.getName());
            paramsJson.put("toUser", newowner);
            paramsJson.put("zoid", ""+zoid);
            paramsJson.put("retainAccess", retainAccess);
            String url = ClientUtils.getServerURL(request, false, null, false, true);
            String newDocID = ContainerSynchronizer.postURLConnection(url, resourceId, paramsJson);*/
			String retainAccess = request.getParameter("RETAIN_ACCESS");
			String currentSpaceName = DocumentUtils.getZUserName(fromUserId);
			WorkbookContainer container = new WorkbookContainer(resourceId, currentSpaceName);
            String newDocId = DocumentUtils.changeOwner(container, null, resInfo.getName(), DocumentUtils.getZUserName(toUserId), "-1", "", "true".equals(retainAccess)?true:false,null);
			if(newDocId != null){
				obj.put("RESULT","true");
				obj.put("MESSAGE","change owner successfull");
			}else{
				obj.put("RESULT","false");
				obj.put("MESSAGE","change owner failed");					
			}
			out.println(obj); // NO OUTPUTENCODING
		} catch (Exception e) {
			LOGGER.log(Level.WARNING,null,e);
		}
        return null;
    }
}
