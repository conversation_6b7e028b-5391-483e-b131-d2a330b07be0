/*$Id$*/
package com.zoho.sheet.action.v2;

import com.adventnet.zoho.websheet.macros.engineimpl.MacroInterfaceImpl;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.TabInfo.TabType;
import com.adventnet.zoho.websheet.model.response.ActionResponseObject;
import com.adventnet.zoho.websheet.model.response.ResponseObject;
import com.adventnet.zoho.websheet.model.response.VersionBasedResponseAnalyzer;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.action.ActionInfo;
import com.zoho.sheet.action.ActionObject;
import com.zoho.sheet.authorization.AppUtil;
import com.zoho.sheet.chart.ChartUtils;
import com.zoho.sheet.util.*;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
/**
 *
 * <AUTHOR> N J (ZT-0049)
 */
public class PostDocLoadAction extends StrutsRequestHandler
{
	private static final Logger LOGGER = Logger.getLogger(PostDocLoadAction.class.getName());

	private String currentSheet;
	private boolean isCurrentVersion;
	private String rows;
	private String columns;
	private int scrolledRow;
	private int scrolledCol;
	private int freezedRow;
	private boolean readCharts;

	public String getCurrentSheet()
	{
		return currentSheet;
	}

	public void setCurrentSheet(String currentSheet)
	{
		this.currentSheet = currentSheet;
	}

	public boolean isCurrentVersion()
	{
		return isCurrentVersion;
	}

	public void setCurrentVersion(boolean currentVersion)
	{
		isCurrentVersion = currentVersion;
	}

	public String getRows()
	{
		return rows;
	}

	public void setRows(String rows)
	{
		this.rows = rows;
	}

	public String getColumns()
	{
		return columns;
	}

	public void setColumns(String columns)
	{
		this.columns = columns;
	}

	public int getScrolledRow()
	{
		return scrolledRow;
	}

	public void setScrolledRow(int scrolledRow)
	{
		this.scrolledRow = scrolledRow;
	}

	public int getScrolledCol()
	{
		return scrolledCol;
	}

	public void setScrolledCol(int scrolledCol)
	{
		this.scrolledCol = scrolledCol;
	}

	public int getFreezedRow()
	{
		return freezedRow;
	}

	public void setFreezedRow(int freezedRow)
	{
		this.freezedRow = freezedRow;
	}

	public boolean isReadCharts()
	{
		return readCharts;
	}

	public void setReadCharts(boolean readCharts)
	{
		this.readCharts = readCharts;
	}

	@Override
	public String execute() throws Exception
	{
		long start = System.currentTimeMillis();
		long timeTakenForFormulaCells = 0L;
		long timeTakenForMacroResponse = 0L;
		long timeTakenForResponse = 0L;

		WorkbookContainer container = CurrentRealm.getContainer();
		String workbookVersion = CurrentRealm.getWorkBookIdentity();
		Workbook workbook = container.getWorkbook(workbookVersion);
		String currentSheetName = getCurrentSheet();
		String collabId = container.getCollabId();
		String docId = container.getDocId();
		String tabKey = CurrentRealm.getTabIdentity();
		String rsid = CurrentRealm.getWmsIdentity();
		UserProfile uProfile = CurrentRealm.getUserProfile();

		List<Cell> formulaCells = new ArrayList<>();
		List<Range> rangeDependents = new ArrayList<>();
		MacroResponse macroResponse = null;
		JSONObjectWrapper newClientResponse = null;


		boolean isCurrentVersion = this.isCurrentVersion();
		boolean isVersionView = workbookVersion != null || isCurrentVersion;
		container.setIsJustCreated(false);

		JSONObjectWrapper actionJson = ActionObject.getActionObject(request);
		try
		{
			if(this.getRows() != null)
			{
				updateViewPortToProfile();
			}
			ActionManager.REEVALUATE_T.set(0L);
			ActionManager.CUSTOM_FUNCTION_EVALUATION_TIME.set(0L);
			sendFormulaDetails(container, workbook, uProfile, isVersionView, formulaCells, rangeDependents);
			timeTakenForFormulaCells = System.currentTimeMillis() - start;
			if(!isVersionView)
			{
				try
				{
					if(workbook.containsMacro())
					{
						start = System.currentTimeMillis();
						macroResponse = sendMacroDetails(container, workbookVersion, uProfile, currentSheetName, rsid, tabKey, request);
						timeTakenForMacroResponse = System.currentTimeMillis() - start;
					}
				}
				catch(Exception e)
				{
					LOGGER.log(Level.WARNING, "[POST_DOC_LOAD][Exception] Exception while sending macro details.", e);
				}
			}
			long currentExShLinkId = 0L;
			if(request.getAttribute(ZFSNGConstants.IS_EXTERNAL_SHARE) != null)
			{
				JSONObjectWrapper linkInfo = new JSONObjectWrapper(((String) request.getAttribute(ZFSNGConstants.LINK_INFORMATION)));
				if(linkInfo.has("LINK_ID"))
				{
					currentExShLinkId = (long) linkInfo.get("LINK_ID");
				}
			}
			start = System.currentTimeMillis();
			newClientResponse = updateNewClientResponse(container, workbook, currentExShLinkId);
			// Sending Macro and DynamicFormulaCells Response to all NewClients via wms.
			updateAllNewClients(container, formulaCells, rangeDependents, macroResponse, actionJson);
			timeTakenForResponse = System.currentTimeMillis() - start;
		}
		catch(Exception e)
		{
			LOGGER.log(Level.SEVERE, "[POST_DOC_LOAD][Exception] Exception while executing PostDocLoadAction", e);
			String errorMsg = ErrorCode.getErrorMessage(ErrorCode.ERROR_DOCUMENT_LOAD, null, ErrorCode.MsgType.ERROR, ErrorCode.DisplayType.BANNER).toString();
			sendErrorMessage(response, ErrorCode.ERROR_DOCUMENT_LOAD, errorMsg);
			return NONE;
		}
		finally
		{
			if(timeTakenForFormulaCells > 500 || ActionManager.REEVALUATE_T.get() > 500 || ActionManager.CUSTOM_FUNCTION_EVALUATION_TIME.get() > 500 || timeTakenForMacroResponse > 500 || timeTakenForResponse > 500)
			{
				LOGGER.log(Level.INFO, "[POST_DOC_LOAD] Time taken\nFormula cells: {0}ms \nReEvaluate: {1}ms\nCustom Function Evaluation: {2}ms\nMacro: {3}ms\nResponse Generation: {4}ms\n\n", new Object[]{timeTakenForFormulaCells, ActionManager.REEVALUATE_T.get(), ActionManager.CUSTOM_FUNCTION_EVALUATION_TIME.get(), timeTakenForMacroResponse, timeTakenForResponse});   // No I18N
			}
			ActionManager.REEVALUATE_T.remove();
			ActionManager.CUSTOM_FUNCTION_EVALUATION_TIME.remove();
		}
		sendResponse(response, newClientResponse);
		return NONE;
	}

	private JSONObjectWrapper getMacroOpenEventActionJson(WorkbookContainer container, Sheet sheet, UserProfile uProfile, String rsid, String tabKey)
	{
		ActionInfo actionInfo = ActionObject.getActionInfo(ActionConstants.MACRO_RUN);
		JSONObjectWrapper actionJson = actionInfo.toJSON();

		actionJson.put(JSONConstants.SHEETLIST,ActionJsonUtil.addSheetInJsonArray(null,sheet.getAssociatedName(), true));
		actionJson.put(JSONConstants.RANGELIST, ActionJsonUtil.addRangeInJsonArray(null, 0, 0, 0, 0, true));
		actionJson.put(JSONConstants.ZUID, uProfile.getZUserId());
		actionJson.put(JSONConstants.USER_NAME, uProfile.getUserName());
		actionJson.put("modN", "ThisWorkbook");
		actionJson.put("macN", "Workbook_Open");
		actionJson.put(JSONConstants.RSID, rsid);
		actionJson.put(JSONConstants.UNIQUE_TAB_ID, tabKey);
		return actionJson;
	}

	private MacroResponse sendMacroDetails(WorkbookContainer container, String workbookIdentity, UserProfile uProfile, String sheetName, String rsid, String tabKey, HttpServletRequest request) throws Exception
	{
		// to create macro Interdace
		String loginName = uProfile.getUserName();
		Workbook workBook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());
		Sheet sheet = workBook.getSheet(sheetName);
		MacroInterface macroInterface = container.getMacroInterface();
		if(macroInterface == null)
		{
			Map requestProps = ClientUtils.constructMacroInterfaceProps(container, request, sheet.getAssociatedName(), workbookIdentity, uProfile);
			macroInterface = new MacroInterfaceImpl(container, (HashMap)requestProps);
			container.setMacroInterface(macroInterface);
		}
		MacroResponse macroResponse = null;
		if(sheet.getWorkbook().isEnableEvents() && macroInterface.hasOpenEvent())
		{
			// to fire event
			//commenting macroInterface.fireWorkbook_OpenEvent as we are adding it to queue in container
//                    macroResponse = macroInterface.fireWorkbook_OpenEvent(sheet, rsid, uProfile);
//                    if(macroResponse != null && !macroResponse.isEmpty())
//                    {
//                        ResponseObject resObj = new ActionResponseObject(container, workBook, actionJson, macroResponse);
//                        //ActionExecutor.sendResponse(container, resObj, uProfile.getZUserId(), rsid);
//                        //ActionExecutor.sendResponse(container, resObj, null, actionJson, uProfile.getZUserId(), rsid);
//                    }
			JSONObjectWrapper macroActionJson = getMacroOpenEventActionJson(container, sheet, uProfile, rsid, tabKey);
			container.addActionToQueue(macroActionJson);
		}
		return macroResponse;
	}

	//Evaluates dynamic formulas of all sheets and send to all collaborators along with array formula details of the current sheet
	private void sendFormulaDetails(WorkbookContainer container, Workbook workbook, UserProfile uProfile, boolean isVersionView, List<Cell> allFormulaCells, List<Range> rangeDependents) throws Exception
	{
		boolean isRegenerateContent = isVersionView;
		boolean isUpdateResourceAdditionalInfo = false;
		try
		{
			if (!isRegenerateContent)// && !workbook.getFunctionLocale().equals(EngineConstants.DEFAULT_LOCALE))
			{
				if (RemoteUtils.isRemoteDocument(container.getDocOwner()))
				{
					// For any public document or remote document, regenerate should be called if locale is not default.
					isRegenerateContent = true;
				}
				// Checking whther the file format is ods and has only one version. then it is an imported file.
				else if (container.isFileExist(Long.parseLong(container.getDocId()), ZSStore.FileName.DOCUMENT, ZSStore.FileExtn.ODS) || container.isFileExist(Long.parseLong(container.getDocId()), ZSStore.FileName.DOCUMENT, ZSStore.FileExtn.ZSHEET))
				{
					// Need to cll regenerate content even for files that have more than one version.
					// When a file is checked out and checked in through an import, or on zoho sync, need to call regenerate content.


					// For any imported file, first open should call regenerateContent and isUpdateResourceAdditionalInfo.
//                    int versionCount = ZohoFS.getVersionCount(container.getDocOwnerZUID(), container.getResourceId());
//                    if (versionCount == 1)
//                    {
					isRegenerateContent = true;
					isUpdateResourceAdditionalInfo = true;
					//}
				}
			}

			if (isRegenerateContent)
			{
				//Regenerating content
				if (!workbook.isDependenciesUpdated())
				{
					workbook.updateCellDependencies();
				}

				//            allFormulaCells = workbook.regenerateContent();
				allFormulaCells.addAll(workbook.regenerateContent());
				if (isUpdateResourceAdditionalInfo)
				{

					//Writing Locale to Resource info
					JSONObjectWrapper additionalInfo = new JSONObjectWrapper();
					additionalInfo.put(Constants.DOCUMENT_LOCALE, workbook.getFunctionLocale().toString());
					additionalInfo.put(Constants.DOCUMENT_TIMEZONE, workbook.getUserTimezone().getID());
					ZohoFS.updateResourceAdditionalInfo(container.getDocsSpaceId(), container.getResourceId(), additionalInfo.toString());
					//ZohoFS.updateResourceAdditionalInfo(container.getDocsSpaceId(), container.getResourceId(), Constants.DOCUMENT_LOCALE, workbook.getFunctionLocale().toString());
					//ZohoFS.updateResourceAdditionalInfo(container.getDocsSpaceId(), container.getResourceId(), Constants.DOCUMENT_TIMEZONE, workbook.getUserTimezone().getID());
				}
			}
			else
			{
				if (!workbook.isDependenciesUpdated()) {
					workbook.updateCellDependencies();
				}

				// To reevaluate filter formula cells for this (newly joined) user
				ActionUtil.ResultObject resultObject = ActionUtil.getDependentCells(workbook, null, null, true);
				allFormulaCells.addAll(resultObject.getResultCells());
				rangeDependents.addAll(resultObject.getResultRanges());
				if (workbook.hasDynamicFormulas()) {
					// allFormulaCells = workbook.recalculateDynamicFormulas();
					allFormulaCells.addAll(workbook.recalculateDynamicFormulas());
				}
			}

		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "[POST_DOC_LOAD][Exception] Exception while recalculating formula cells.", e);
		}

		boolean hasStockFormula = workbook.hasStockFormulas();
		UserProfile.AccessType accessType = uProfile.getAccessType();
		if(isVersionView || accessType == UserProfile.AccessType.PUBLIC_EXTERNAL || accessType == UserProfile.AccessType.PUBLIC_ORG
			|| accessType == UserProfile.AccessType.RANGE_PUBLIC_EXTERNAL || accessType == UserProfile.AccessType.RANGE_PUBLIC_ORG
			|| accessType == UserProfile.AccessType.SHEET_PUBLIC_EXTERNAL || accessType == UserProfile.AccessType.SHEET_PUBLIC_ORG)
		{
		}
		else
		{
			if(hasStockFormula)
			{
				LOGGER.log(Level.INFO, "Stock Formula in this spreadsheet -RID : {0} ", container.getResourceId());
				ZSStats.incrementByCustomValue(ZSStats.STOCK_FORMULA);
			}
		}
	}

	protected void sendResponse(HttpServletResponse response, Object data)
	{
		HttpServletResponseWrapper.sendResponse(response, data, HttpServletResponseWrapper.MimeType.JSON);
	}

	protected void sendErrorMessage(HttpServletResponse response, String errorCode, String errorMessage)
	{
		JSONObjectWrapper errorStr = new JSONObjectWrapper();
		errorStr.put("mk", errorCode);    //No I18N
		errorStr.put(Integer.toString(CommandConstants.ERRORMSG), errorMessage);    //DUMMY

		response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
		sendResponse(response, errorStr);
	}

	private JSONObjectWrapper updateNewClientResponse(WorkbookContainer container, Workbook workbook, long currentExShLinkId)  throws Exception
	{
		ResponseObject responseObject = new ActionResponseObject(container, workbook, new JSONObjectWrapper().put("isPostDoc", true));
		return (JSONObjectWrapper) VersionBasedResponseAnalyzer.getResponse(responseObject, null, AppUtil.getCurrentUserInfo());
	}

	private void updateAllNewClients(WorkbookContainer container, List<Cell> formulaCells, List<Range> rangeDependents, MacroResponse macroResponse, JSONObjectWrapper actionJson) throws Exception
	{
		Workbook workbook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());
		if((formulaCells != null  && !formulaCells.isEmpty())|| macroResponse != null)
		{
			JSONObjectWrapper affectedChartDetails = ChartUtils.getAffectedChartsList(workbook, true);
			ResponseObject responseObject = new ActionResponseObject(container, workbook, actionJson, formulaCells, rangeDependents, macroResponse, null, affectedChartDetails,
					workbook.getChartsContainer().getModifiedChartListAndClear(actionJson), Boolean.FALSE);
			MessagePropagator.dispatchMessage_ThroughWorker(container, responseObject, actionJson, null);
		}
	}

	protected void updateViewPortToProfile() throws Exception
	{
		String currentSheet = this.getCurrentSheet();
		String rows = this.getRows();
		String columns = this.getColumns();
		String tabKey = CurrentRealm.getTabIdentity();
		if(tabKey != null)
		{
			String rsid = CurrentRealm.getWmsIdentity();
			WorkbookContainer wbContainer = CurrentRealm.getContainer();
			UserProfile profile = CurrentRealm.getUserProfile();
			Sheet viewSheet = wbContainer.getWorkbook(CurrentRealm.getWorkBookIdentity()).getSheet(currentSheet);
			int scrolledRows = this.getScrolledRow();
			int scrolledColumns = this.getScrolledCol();
			int freezedRows = this.getFreezedRow();
			boolean isVersionView = CurrentRealm.getWorkBookIdentity() != null;
			ViewPort viewPort = new ViewPort(viewSheet.getAssociatedName(), rows, columns, freezedRows, scrolledRows, scrolledColumns, isVersionView);
			TabInfo tabInfo = profile.getTabInfo(tabKey, rsid, TabType.NON_CACHED);
			tabInfo.setViewPort(viewPort);
		}
	}
}