//$Id$
package com.zoho.sheet.action;

import com.adventnet.ds.query.*;
import com.adventnet.persistence.DataAccessException;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.task.FetchStockData;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.sheet.util.StrutsRequestHandler;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

import javax.servlet.ServletOutputStream;
import java.io.*;
import java.net.URLDecoder;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.DeflaterOutputStream;

import static com.adventnet.zoho.websheet.task.FetchStockData.IEX_API_URL;

public class ZohoSheetStockDataApi extends StrutsRequestHandler {
    private static final Logger LOGGER = Logger.getLogger(ZohoSheetStockDataApi.class.getName());
    private static final String ZOHO_STOCK_API_KEY = "ZOHO_STOCK_API_KEY";//No I18N
    public static final String KEY_APIKEY = "apikey";//No I18N
    public static final String VALUE_APIKEY = "4f139c0682964014bdad9353e6e19b3f";//No I18N
    public static final String KEY_SYMBOLS = "symbols";//No I18N
    public static final String KEY_ADD = "add";//No I18N
    public static final String KEY_TYPE = "type";//No I18N
    public static final String COL_IS_ENABLED = "IS_ENABLED";//No I18N

    @Override
    public String execute() throws Exception {
        LOGGER.log(Level.OFF,"[ZohoSheetStockDataApi]");
        final Instant now = Instant.now();
        ServletOutputStream outputStream = response.getOutputStream();
        try {
            String apiKey = request.getParameter(KEY_APIKEY);
            if (apiKey == null || !apiKey.equals(EnginePropertyUtil.getEnginePropertyValue(ZOHO_STOCK_API_KEY))) {
                throw new Exception("Incorrect Api Key");
            }
            final String type = request.getParameter(KEY_TYPE);
            if(type.equals(KEY_SYMBOLS)) {
                outputStream.write(allSymbolsCSV());
                outputStream.flush();
            } else if(type.equals(KEY_ADD)){
                final String symbols = URLDecoder.decode(request.getParameter(KEY_SYMBOLS),"UTF-8");//No I18N
                final List<String> requested_symbols = Arrays.asList(symbols.split(","));
                LOGGER.log(Level.OFF, "adding symbols {0}", requested_symbols);
                Map<String, String> standard_iex_symbols = new HashMap<>();
                for(int i=0; i<requested_symbols.size(); i+=2){
                    standard_iex_symbols.put(requested_symbols.get(i), requested_symbols.get(i+1));
                }
                boolean isFetchFromIEX = EnginePropertyUtil.getEnginePropertyValue(IEX_API_URL).startsWith("https://cloud.iexapis.com");//NoI18N
                if(isFetchFromIEX) {
                    Set<String> symbolsFromDB=FetchStockData.getSymbolsFromDB(false);
                    Set<String> existingSymbolsInDB = new HashSet<>();
                    for(String string : standard_iex_symbols.keySet()){
                        if(symbolsFromDB.contains(string)) {
                            existingSymbolsInDB.add(string);
                        }
                    }
                    if(!existingSymbolsInDB.isEmpty()) {
                        LOGGER.log(Level.OFF, "symbols already present {0}", existingSymbolsInDB);
                        for(String existingSymbol : existingSymbolsInDB){
                            standard_iex_symbols.remove(existingSymbol);
                        }
                    }
                    if(!standard_iex_symbols.isEmpty()) {
                        Persistence publicDB = SheetPersistenceUtils.getPersistence(FetchStockData.PUBLIC_DB);
                        DataObject stockDO = publicDB.constructDataObject();
                        for (Map.Entry<String, String> symbol_not_in_db_standardIEX : standard_iex_symbols.entrySet()) {
                            Row row = new com.adventnet.persistence.Row(FetchStockData.STOCKDATA_TABLE_DB);
                            row.set(FetchStockData.SYMBOL_COL, symbol_not_in_db_standardIEX.getKey());
                            if(symbol_not_in_db_standardIEX.getKey().equals(symbol_not_in_db_standardIEX.getValue())){
                                row.set(FetchStockData.DATA_PROVIDER_SYMBOL, "IEX");//No I18N
                            } else {
                                row.set(FetchStockData.DATA_PROVIDER_SYMBOL, symbol_not_in_db_standardIEX.getValue());
                            }
                            long date = new Date().getTime();
                            row.set("LAST_ACCESSED", date);//No I18N
                            row.set("LAST_UPDATED", date);//No I18N
                            row.set(COL_IS_ENABLED, 0);
                            stockDO.addRow(row);
                        }
                        publicDB.add(stockDO);
                        LOGGER.log(Level.OFF, "symbols added {0}", standard_iex_symbols);
                    }
                } else {
                    LOGGER.log(Level.OFF, "requested symbols not added to DB as it's not US DC");
                }
            } else {
                final String symbols = URLDecoder.decode(request.getParameter(KEY_SYMBOLS),"UTF-8");//No I18N
                final HashSet<String> requested_symbols = new HashSet<>(Arrays.asList(symbols.split(",")));
                fetchDisabledSymbolsAndUpdateDB(requested_symbols);
                outputStream.write(quotesCSV(requested_symbols));
                outputStream.flush();
            }
        } catch (DataAccessException e) {
            LOGGER.severe("[ZohoSheetStockDataApi]: Incorrect Api Key");//No I18N
            outputStream.write("{}".getBytes());
            outputStream.flush();
        } finally {
            LOGGER.log(Level.OFF, "time :{0}ms", Duration.between(now, Instant.now()).toMillis());
        }
        return null;
    }

    private void fetchDisabledSymbolsAndUpdateDB(Set<String> requested_symbols) throws Exception {
        Persistence publicDB = SheetPersistenceUtils.getPersistence(FetchStockData.PUBLIC_DB);
        Criteria criteria = new Criteria(new Column(FetchStockData.STOCKDATA_TABLE_DB, FetchStockData.SYMBOL_COL), requested_symbols.toArray(), QueryConstants.IN, false)
                .and(new Criteria(new Column(FetchStockData.STOCKDATA_TABLE_DB, COL_IS_ENABLED),0, QueryConstants.EQUAL, false));
        DataObject rowsDataObject = publicDB.get(FetchStockData.STOCKDATA_TABLE_DB, criteria);
        Iterator iterator = rowsDataObject.getRows(FetchStockData.STOCKDATA_TABLE_DB);
        Set<String> disabled_symbols = new HashSet<>();
        while (iterator.hasNext()) {
            Row row = (Row) iterator.next();
            String symbol = (String) row.get(FetchStockData.SYMBOL_COL);
            disabled_symbols.add(symbol);
        }
        if (disabled_symbols.isEmpty()) {
            return;
        }

        FetchStockData.batchAndFetchFrom12Data_AndUpdateDB(new ArrayList<>(disabled_symbols), false);
    }

    private static byte[] quotesCSV(Set<String> symbols) throws DataAccessException, IOException {
        Persistence publicDB = SheetPersistenceUtils.getPersistence(FetchStockData.PUBLIC_DB);
        Criteria criteria = new Criteria(new Column(FetchStockData.STOCKDATA_TABLE_DB, FetchStockData.SYMBOL_COL), symbols.toArray(), QueryConstants.IN, false);
        criteria.and(new Criteria(new Column(FetchStockData.STOCKDATA_TABLE_DB, COL_IS_ENABLED),1, QueryConstants.EQUAL));
        DataObject rowsDataObject = publicDB.get(FetchStockData.STOCKDATA_TABLE_DB, criteria);
        Writer writer = new StringWriter();
        CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT);
        Iterator iterator = rowsDataObject.getRows(FetchStockData.STOCKDATA_TABLE_DB);
        int i = 0;
        while (iterator.hasNext()) {
            Row row = (Row) iterator.next();
            String symbol = (String) row.get(FetchStockData.SYMBOL_COL);
            csvPrinter.print(symbol);
            for (FetchStockData.Attribute attribute : FetchStockData.Attribute.values()) {
                csvPrinter.print(maskNull(row.get(FetchStockData.Attribute.toString(attribute))));
            }
            csvPrinter.println();
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try (DeflaterOutputStream deflaterOutputStream = new DeflaterOutputStream(os)) {
            deflaterOutputStream.write(writer.toString().getBytes());
        }
        return os.toByteArray();
    }
    private static byte[] allSymbolsCSV() throws DataAccessException, IOException {
        Writer writer = new StringWriter();
        CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT);
        final Set<String> symbolsFromDB = FetchStockData.getSymbolsFromDB(false);
        for(String symbol : symbolsFromDB) {
            csvPrinter.print(symbol);
            csvPrinter.println();
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try (DeflaterOutputStream deflaterOutputStream = new DeflaterOutputStream(os)) {
            deflaterOutputStream.write(writer.toString().getBytes());
        }
        return os.toByteArray();
    }
    private static Object maskNull(Object o) {
        return o == null ? "" : o;//No I18N
    }
}
