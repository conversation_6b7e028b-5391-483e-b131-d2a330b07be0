/* $Id$ */
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.zoho.sheet.action;

import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import com.zoho.sheet.listingpage.SheetListingAction;
import com.zoho.sheet.util.*;
import com.zoho.zfsng.client.ProductInfo;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ShareConstants;
import java.io.PrintWriter;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class DynamicRequestAction extends StrutsRequestHandler
{

	private static final Logger LOGGER = Logger.getLogger(DynamicRequestAction.class.getName());


	public String execute() throws Exception
	{

		PrintWriter writer = response.getWriter();
		JSONObjectWrapper responseObject = new JSONObjectWrapper();
		String resourceId = request.getParameter("rid");
		ProductInfo pInfo = ZohoFS.getProductInfo(resourceId);

		JSONArrayWrapper subActions = new JSONArrayWrapper(request.getParameter("sub_actions"));
		WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();

		Workbook workBook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());

		WorkbookAdditionalInfo additionalInfo = container.getWorkbookAdditionalInfo();
		SpreadsheetSettings spreadsheetSettings = additionalInfo.getSpreadsheetSettings();
		TimeZone timezone = additionalInfo.getTimeZone();
		Locale wbLocale = spreadsheetSettings.getLocale();
		Locale currencyLocale = spreadsheetSettings.getCurrencyLocale();

		DecimalFormatSymbols decimalFormatSymbol = new DecimalFormatSymbols(wbLocale);


		for(int i = 0; i < subActions.length(); i++)
		{

			switch(subActions.getInt(i))
			{

				case FeatureConstants.THOUSAND_SEPARATOR:
					responseObject.put(Integer.toString(CommandConstants.THOUSAND_SEPARATOR), spreadsheetSettings.getThousandSeparator());
					break;

				case FeatureConstants.CURRENCY:
					JSONObjectWrapper currencyFormat = getCurrencyFormats(decimalFormatSymbol, currencyLocale);
					responseObject.put(String.valueOf(CommandConstants.CURRENCY), currencyFormat);
					break;

				case FeatureConstants.DATE:

					JSONObjectWrapper dateFormatsJson = FormatCellsUtils.getDateFormats(wbLocale, timezone); //this method used in 2 places so changed to Utils.
					responseObject.put(String.valueOf(CommandConstants.DATE), dateFormatsJson);
					break;

				case FeatureConstants.OPEN:
					getLastOpenedSpreadSheetList(responseObject, container.getResourceId());
					break;

				case FeatureConstants.SPREADSHEET_SETTINGS:
					getSpreadsheetSettings(wbLocale, workBook, responseObject);
					break;

				case FeatureConstants.SPREADSHEET_PROPERTIES:
					buildSpreadsheetProperties(wbLocale, timezone, resourceId, responseObject);
					break;
			}
		}
		writer.print(responseObject.toString());                 //NO OUTPUTENCODING
		response.setContentType("application/json");                  //No I18N

		return null;
	}

	private static void buildSpreadsheetProperties(Locale locale, TimeZone timezone, String resourceId, JSONObjectWrapper responseObject)
	{
		JSONObjectWrapper spreadsheetPropertiesObj = new JSONObjectWrapper();

		try
		{
			ResourceInfo resourceInfo = ZohoFS.getResourceInfo(resourceId);
			String ownerZUID = resourceInfo.getOwner();
			String docOwnerName = DocumentUtils.getZDisplayName(ownerZUID);
			String getTopVersion = ZohoFS.getTopVersion(ownerZUID, resourceId, true);
			getTopVersion = (getTopVersion != null) ? getTopVersion : "Version not yet Created"; //NO I18N

			SimpleDateFormat dateFormat = (SimpleDateFormat) DateFormat.getDateTimeInstance(DateFormat.MEDIUM, DateFormat.SHORT, locale);
			dateFormat.setTimeZone(timezone);
			String createdTime = dateFormat.format(resourceInfo.getCreatedTime());

			spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.DOCUMENT_OWNER), docOwnerName + "/" + ownerZUID);
			spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.TOPVERSION), getTopVersion);
			spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.DOC_CREATED_TIME), createdTime);

			getPublishedInfo(ownerZUID, resourceId, spreadsheetPropertiesObj, dateFormat, resourceInfo);
			String lastOpenedTime = dateFormat.format(resourceInfo.getLastOpenedTime());
			spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.DOC_LAST_OPENED_TIME), lastOpenedTime);

			responseObject.put(Integer.toString(CommandConstants.SPREADSHEET_PROPERTIES), spreadsheetPropertiesObj);

		}
		catch(Exception ex)
		{
			Logger.getLogger(DynamicRequestAction.class.getName()).log(Level.SEVERE, null, ex);
		}
	}

	private static void getPublishedInfo(String ownerZuid, String resourceId, JSONObjectWrapper spreadsheetPropertiesObj, SimpleDateFormat dateFormat, ResourceInfo resourceInfo)
	{

		JSONObjectWrapper storeDetails = new JSONObjectWrapper();
		Long currentUSERZUID = DocumentUtils.getZUID();
		try
		{

			int shareCount = ZohoFS.getResourcePermissionsCount(ownerZuid, resourceId, "-1");
			String sharedDetails = ZohoFS.getResourcePermissions(ownerZuid, resourceId, "-1", 0, shareCount);

			if(sharedDetails != null)
			{
				JSONArrayWrapper jAry = new JSONArrayWrapper(sharedDetails);
				List<String> slist = new ArrayList<String>();

				for(int i = 0; i < jAry.length(); i++)
				{
					JSONObjectWrapper sharedDtl = (JSONObjectWrapper) jAry.get(i);
					String sUzuid = sharedDtl.getString("shared_to");
					String sType = getSharedType(sharedDtl.getInt("shared_type"));
					String srole = sharedDtl.getString("role");

					if((currentUSERZUID.toString().equals(ownerZuid)))
					{
						String sUserName = DocumentUtils.getZDisplayName(sUzuid);

						if(sType.equals("PERSONAL_SHARE"))
						{
							slist.add(sUserName + "/" + sUzuid + " - " + srole);
						}

						published_Info(sType, srole, sharedDetails, spreadsheetPropertiesObj);
					}
					else if(currentUSERZUID.toString().equals(sUzuid))
					{
						spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.SHARED_TO), ("Me - " + srole));
					}
				}

				if(!slist.isEmpty())
				{
					spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.SHARED_DETAILS), slist);
					String lastModifiedBy_zuid = resourceInfo.getModifiedBy();
					String lastModifiedBy = DocumentUtils.getZDisplayName(lastModifiedBy_zuid);
					String modifiedTime = dateFormat.format(resourceInfo.getStatusChangeTime());
					spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.RECENTLY_MODIFIED_BY), lastModifiedBy + "/" + lastModifiedBy);
					spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.RECENT_MODIFICATION_TIME), modifiedTime);
				}
			}

		}
		catch(Exception ex)
		{
			Logger.getLogger(DynamicRequestAction.class.getName()).log(Level.SEVERE, null, ex);
		}
	}

	private static void published_Info(String sType, String srole, String sharedDetails, JSONObjectWrapper spreadsheetPropertiesObj)
	{
		JSONArrayWrapper keys_published = new JSONArrayWrapper();
		JSONObjectWrapper published_Details = new JSONObjectWrapper();

		if(sType.equals("EXTERNAL_PUBLISH") || sType.equals("ORG_PUBLISH"))
		{

			JSONObjectWrapper shareDetailObj = new JSONArrayWrapper(sharedDetails).getJSONObject(0);
			JSONObjectWrapper addInfoObjStr = shareDetailObj.getJSONObject("additional_info");
			String viewed = addInfoObjStr.getString("VIEWED");

			spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.PUBLISHED_VIEWS), viewed); //key

		}
		else if(sType.equals("ORG_SHARE") || sType.equals("LINK_SHARE") || sType.equals("LINK_SHARE_PASSWORD"))
		{

			spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.PUBLISHED_TO), sType); //key
			spreadsheetPropertiesObj.put(Integer.toString(CommandConstants.PUBLISHED_AS), srole);//key
		}
	}

	private static String getSharedType(int type)
	{

		String sharedType = "";
		switch(type)
		{
			case ShareConstants.SHAREDTYPE_PERSONAL:
				sharedType = "PERSONAL_SHARE";  // No i18N
				break;

			case ShareConstants.SHAREDTYPE_ORG:
				sharedType = "ORG_SHARE";       // No i18N
				break;

			case ShareConstants.SHAREDTYPE_GROUP:
				sharedType = "GROUP_SHARE";     // No i18N
				break;

			case ShareConstants.SHAREDTYPE_ORG_PUBLISH:
				sharedType = "ORG_PUBLISH";     // No i18N
				break;

			case ShareConstants.SHAREDTYPE_PUBLIC:
				sharedType = "EXTERNAL_PUBLISH";        // No i18N
				break;

			case ShareConstants.SHAREDTYPE_ORG_URL:
				sharedType = "ORG_SHARE";       // No i18N
				break;

			case ShareConstants.SHAREDTYPE_PUBLIC_URL:
				sharedType = "EXTERNAL_SHARE";  // No i18N
				break;

			case ShareConstants.SHAREDTYPE_ANYONE_WITHLINK_SHARE:
				sharedType = "LINK_SHARE";      // No i18N
				break;

			case ShareConstants.SHAREDTYPE_ANYONE_WITHLINK_PASSWORD_SHARE:
				sharedType = "LINK_SHARE_PASSWORD";     // No i18N
				break;

		}
		return sharedType;
	}

	private static void getSpreadsheetSettings(Locale locale, Workbook workBook, JSONObjectWrapper responseObject)
	{

		JSONObjectWrapper sPsettings = new JSONObjectWrapper();

		JSONObjectWrapper localeSettings = buildLocaleSettings(locale);

		sPsettings.put("COUNTRY", localeSettings.getJSONArray("country"));
		sPsettings.put("SELECTED_COUNTRY", localeSettings.getString("selected_country"));

		sPsettings.put("LANGUAGE", localeSettings.getJSONArray("language"));
		sPsettings.put("SELECTED_LANGUAGE", localeSettings.getString("selected_language"));

		String lastTimeZone = (String) workBook.getUserTimezone().getID();
		JSONObjectWrapper timez = getTimeZone(lastTimeZone);
		sPsettings.put("TIMEZONE", timez.getJSONArray("timezone"));
		sPsettings.put("SELECTED_TIMEZONE", timez.getString("selected_timezone"));

		responseObject.put(String.valueOf(CommandConstants.SPREADSHEET_SETTINGS), sPsettings);
	}

	private static JSONObjectWrapper buildLocaleSettings(Locale locale)
	{

		String currentLocale = locale.toString();

		String[] splitLocale = currentLocale.split("_");

		String storeInfo = "";
		JSONObjectWrapper jsObj = new JSONObjectWrapper();
		for(int k = 0; k < splitLocale.length; k++)
		{

			if(k == 0)
			{
				storeInfo = "language"; //NO I18N
			}
			else
			{
				storeInfo = "country";  //NO I18N
			}

			String localeCode = splitLocale[k];


			boolean isLocaleMatched = false;
			JSONObjectWrapper collections = new JSONObjectWrapper();
			String selectedKey = "";

			Locale[] loc = Locale.getAvailableLocales();

			SortedMap<String, String> sortedKeysMap = new TreeMap<String, String>();

			String code = "";

			for(int i = 0; i < loc.length; i++)
			{

				code = ("country".equals(storeInfo)) ? loc[i].getCountry() : loc[i].getLanguage(); //NO I18N

				if(code != null && !code.equals(""))

				{
					String key = code;

					String value = ("country".equals(storeInfo)) ? loc[i].getDisplayCountry() : loc[i].getDisplayLanguage(); //NO I18N

					sortedKeysMap.put(value, key);
					if(key.equals(localeCode))
					{
						isLocaleMatched = true;
					}
				}
			}

			if(!isLocaleMatched)
			{
				Locale newLocale = new Locale("", localeCode);
				sortedKeysMap.put(newLocale.getDisplayCountry(), localeCode);
			}
			JSONArrayWrapper temp = new JSONArrayWrapper();
			for(String key : sortedKeysMap.keySet())
			{
				String localeCountryDisplay = sortedKeysMap.get(key);

				if(localeCountryDisplay.equals(localeCode))
				{
					selectedKey = localeCountryDisplay;
				}
				collections.put(localeCountryDisplay, key);
				temp.put(localeCountryDisplay + "_" + key);
			}

			jsObj.put(storeInfo, temp);
			jsObj.put("selected_" + storeInfo, selectedKey);
		}

		return jsObj;
	}

	private static JSONObjectWrapper getTimeZone(String lastTimeZone)
	{
		java.util.Date date = new java.util.Date();
		String TimeZoneIds[] = TimeZone.getAvailableIDs();
		String minut = null;
		int check = 0;
		JSONArrayWrapper timez = new JSONArrayWrapper();
		String selected_timez = "";

		for(int i = 0; i < TimeZoneIds.length; i++)
		{

			TimeZone tz = TimeZone.getTimeZone(TimeZoneIds[i]);
			int rawOffset = tz.getRawOffset();
			int hour = rawOffset / (60 * 60 * 1000);
			DecimalFormat df = new DecimalFormat("00");
			int minu = rawOffset / (60 * 1000);
			int min = minu % 60;
			if(min == 0)
			{
				minut = "00";
			}
			else
			{
				if(min < 0)
				{
					min = (min * (-1));
				}
				minut = Integer.toString(min);
			}
			String minuts = df.format(Integer.parseInt(minut));
			String tzName = tz.getDisplayName(tz.inDaylightTime(date), TimeZone.LONG);
			String value = "";
			if(TimeZoneIds[i].equals(lastTimeZone))
			{

				if(hour > -1)
				{
					selected_timez = TimeZoneIds[i];
					value = "( GMT +" + hour + ":" + minuts + " ) " + tzName + " ( " + TimeZoneIds[i] + " )"; //NO I18N
					timez.put(TimeZoneIds[i].toString() + "[" + value.toString());
				}
				else
				{
					selected_timez = TimeZoneIds[i];
					value = "( GMT " + hour + ":" + minuts + " ) " + tzName + " ( " + TimeZoneIds[i] + " )";  //NO I18N
					timez.put(TimeZoneIds[i].toString() + "[" + value.toString());
				}

			}
			if(!TimeZoneIds[i].equals(lastTimeZone))
			{

				if(hour > -1)
				{
					value = "( GMT +" + hour + ":" + minuts + " ) " + tzName + " ( " + TimeZoneIds[i] + " )";  //NO I18N
					timez.put(TimeZoneIds[i].toString() + "[" + value.toString());
				}
				else
				{
					value = "( GMT " + hour + ":" + minuts + " ) " + tzName + " ( " + TimeZoneIds[i] + " )";  //NO I18N
					timez.put(TimeZoneIds[i].toString() + "[" + value.toString());
				}
			}


		}

		JSONObjectWrapper jsObj = new JSONObjectWrapper();
		jsObj.put("timezone", timez);
		jsObj.put("selected_timezone", selected_timez);

		return jsObj;
	}

	public static void getLastOpenedSpreadSheetList(JSONObjectWrapper responseObject, String resourceId) {

		JSONObjectWrapper jsObj = new JSONObjectWrapper();

		try {
			String zuid = CurrentRealm.getUserProfile().getZUserId();
			if (zuid.contains("$")) {
				return;
			}

			JSONArrayWrapper resultArry = new JSONArrayWrapper();
			String orgDetails = ZohoFS.getOrgDetails(zuid, resourceId);
			String memberId;
			if (orgDetails != null) {
				JSONObjectWrapper orgInfo = new JSONObjectWrapper(orgDetails);
				JSONObjectWrapper memberIdInfo = WorkDriveInfoUtils.getMemberId(orgInfo.getString("ORG_ID"));
				memberId = memberIdInfo.getString("memberId");
			}else {
				JSONObjectWrapper orgInfo = WorkDriveInfoUtils.getLastViewedOrgInfo(zuid);
				memberId = orgInfo.getString("memberId");

			}

			JSONArrayWrapper recentSS = SheetListingAction.getRecentSpreadsheets(0, 10, memberId);

				for (int i = 0; i < recentSS.length(); i++) {

					JSONObjectWrapper jObj = new JSONObjectWrapper();
					JSONObjectWrapper obj = recentSS.getJSONObject(i);
					String rsid = obj.getString("id");
					JSONObjectWrapper attributes = obj.getJSONObject("attributes");
					jObj.put("name", attributes.getString("name"));
					jObj.put("rsid", rsid);
					if (resourceId != null && resourceId.equals(rsid)) {
						continue;
					}
					resultArry.put(jObj);
				}


			jsObj.put("docList", resultArry);
			responseObject.put(String.valueOf(FeatureConstants.OPEN), jsObj);
		} catch (Exception e) {

			LOGGER.log(Level.WARNING, "Exception in getRecentDocumentList", e);
		}
	}

	public static JSONObjectWrapper getCurrencyFormats(DecimalFormatSymbols decimalFormatSymbol, Locale wbLocale)
	{
		JSONObjectWrapper jsObj = new JSONObjectWrapper();

		String currencySymbol = LocaleUtil.getCurrencySymbol(decimalFormatSymbol.getCurrency().getCurrencyCode());
		String currencySymbol_HTML = IAMEncoder.encodeHTML(currencySymbol);
		String currencyCode = decimalFormatSymbol.getCurrency().getCurrencyCode();
		String currencyName = "CURRENCYNAME." + currencyCode;//No I18N
		JSONArrayWrapper localUtilCurrencySymbols = new JSONArrayWrapper();
		JSONObjectWrapper currencySymbols = new JSONObjectWrapper();

		currencySymbols.put("INR", LocaleUtil.getCurrencySymbol("INR"));
		currencySymbols.put("USD", LocaleUtil.getCurrencySymbol("USD"));
		currencySymbols.put("EUR", LocaleUtil.getCurrencySymbol("EUR"));
		currencySymbols.put("CNY", LocaleUtil.getCurrencySymbol("CNY"));
		currencySymbols.put("GBP", LocaleUtil.getCurrencySymbol("GBP"));
		localUtilCurrencySymbols.put(currencySymbols);

		jsObj.put(Integer.toString(CommandConstants.CURRENCYSYMBOL), currencySymbol);
		jsObj.put(Integer.toString(CommandConstants.LOCALUTILCURSYMBOLS), localUtilCurrencySymbols);
		jsObj.put(Integer.toString(CommandConstants.CURRENCYSYMBOL_HTML), currencySymbol_HTML);
		jsObj.put(Integer.toString(CommandConstants.CURRENCYNAME), currencyName);
		jsObj.put(Integer.toString(CommandConstants.WORKBOOKLOCALE), wbLocale.toString());

		return jsObj;
	}

	private static void getDecimalSeparator(DecimalFormatSymbols decimalFormatSymbol, JSONObjectWrapper responseObject)
	{
		JSONObjectWrapper jsObj = new JSONObjectWrapper();
		String thousandSeparator = Character.toString(decimalFormatSymbol.getGroupingSeparator());
		responseObject.put(Integer.toString(CommandConstants.DECIMAL_SEPARATOR), thousandSeparator);
	}

}
