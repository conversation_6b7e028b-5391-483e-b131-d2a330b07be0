/* $Id$ */
package com.zoho.sheet.action.filter.v1;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.filter.*;
import com.adventnet.zoho.websheet.model.filter.executor.FilterAction;
import com.adventnet.zoho.websheet.model.filter.executor.FilterActionExecutor;
import com.adventnet.zoho.websheet.model.filter.operator.FilterLogicalOperator;
import com.adventnet.zoho.websheet.model.filter.operator.FilterOperator;
import com.adventnet.zoho.websheet.model.filter.operator.FilterSpecialOperator;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.util.LocaleMsg;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.adventnet.zoho.websheet.model.filter.FilterType.*;
import static com.adventnet.zoho.websheet.model.filter.operator.FilterOperator.EQUALS;
import static com.adventnet.zoho.websheet.model.util.EngineConstants.*;
import static com.adventnet.zoho.websheet.model.util.JSONConstants.*;
import static com.adventnet.zoho.websheet.model.zs.ZSModelConstants.EMPTY;
import static com.adventnet.zoho.websheet.model.zs.ZSModelConstants.TRANSPARENT;

/**
 * This class {@code FilterDataRetriever} is used to retrieve the cell content like values, cell color,
 * font color specified by an column index of a filter view
 * <AUTHOR> N J ( ZT-0049 )
 */
public class FilterDataRetriever extends FilterActionExecutor
{
	private final Logger logger = Logger.getLogger(FilterDataRetriever.class.getName());
	/**
	 * Index of the column in the sheet range.
	 */
	private final int index;
	/**
	 * Index of the column in the sheet.
	 */
	private final int columnIndex;
	/**
	 * List of indexes that is selected from the list. A value of 1 indicates the index in the corresponding
	 * content or color is selected.
	 */
	private List<Integer> selectedItemIndexes = new ArrayList<>();
	/**
	 * TreeMap is used in order to sort based on the value object in the cell.
	 * List of Cell Content mapping is used as there can be same value object for different cell contents.
	 */
	private final Map<Value, TreeSet<String>> cellContents = new TreeMap<>(this.getComparator());
	/**
	 * Cell Background colors
	 */
	private Set<JSONObject> backgroundColors = new LinkedHashSet<>();
	/**
	 * Cell Font Colors
	 */
	private Set<JSONObject> fontColors = new LinkedHashSet<>();
	private org.json.JSONObject responseObject = null;

	private final List<Object> filteredData = new ArrayList<>();
	private FilterType appliedFilterType = null;
	private DataType dataType = DataType.TEXT;

	private FilterDataRetriever(FilterView view, int index, int columnIndex)
	{
		super(view, columnIndex);
		this.index = index;
		this.columnIndex = columnIndex;
	}

	public static FilterDataRetriever getInstance(FilterView view, int index, int columnIndex)
	{
		return new FilterDataRetriever(view, index, columnIndex);
	}

	protected int getIndex()
	{
		return this.index;
	}

	protected int getColumnIndex()
	{
		return columnIndex;
	}

	private JSONObject getResponseObject()
	{
		return responseObject;
	}

	private Comparator<Value> getComparator()
	{
		return new ZSComparators.ValueComparator(false, false);
	}

	private Comparator<String> getContentComparator()
	{
		return Comparator.comparing(String::toLowerCase);
	}

	private Collection<? extends Object> getFilteredList(FilterType appliedFilterType)
	{
		switch(appliedFilterType)
		{
			case FILTER_BY_VALUE:
				Set<String> cellContents = new LinkedHashSet<>();
				this.cellContents.values().forEach(cellContents::addAll);
				return cellContents;
			case FILTER_BY_CELL_COLOR:
				return this.backgroundColors;
			case FILTER_BY_FONT_COLOR:
				return this.fontColors;
			default:
				return new ArrayList<>();
		}
	}

	private Set<JSONObject> createNewHashSet(JSONObject firstElement, Set<JSONObject> hashSet)
	{
		Set<JSONObject> newHashSet = new LinkedHashSet<>();
		newHashSet.add(firstElement);
		newHashSet.addAll(hashSet);
		return newHashSet;
	}

	private boolean isExist(Set<JSONObject> jsonObjects, JSONObject objectToCheck)
	{
		for(JSONObject jsonObject : jsonObjects)
		{
			if(jsonObject.similar(objectToCheck))
			{
				return true;
			}
		}
		return false;
	}

	private void updateSelectedIndexes(Object content, int indexToAdd, FilterType filterType)
	{
		if(appliedFilterType != null && appliedFilterType.equals(filterType))
		{
			// TODO - SHIRU REMOVE HEX CODE COMPARISON CHECK ONCE MOBILE APP SUPPORTS THEME COLORS ON FILTERS
			if(appliedFilterType.isColorFilter())
			{
				boolean isExist = false;
				for(Object colorObj : this.filteredData)
				{
					String filteredHexCode = ZSColor.getHexColor((ZSColor)colorObj, this.getZSTheme());
					String hexCode = ZSColor.getHexColor((ZSColor)content, this.getZSTheme());
					if(filteredHexCode.equalsIgnoreCase(hexCode))
					{
						isExist = true;
						break;
					}
				}
				if(indexToAdd != -1)
				{
					this.selectedItemIndexes.add(indexToAdd, isExist ? 1 : 0);
				}
				else
				{
					this.selectedItemIndexes.add(isExist ? 1 : 0);
				}
			}
			else
			{
				this.selectedItemIndexes.add(this.filteredData.contains(content) ? 1 : 0);
			}
		}
	}

	private void updateBackgroundColors(ReadOnlyCell readOnlyCell)
	{
		Map<Integer, ZSColor> cellStyleMap = this.getHierarchialCellStyleMap(readOnlyCell);
		if(this.backgroundColors.size() < MAX_FILTER_COLORS_LIST)
		{
			ZSColor cellBackgroundColor = cellStyleMap.get(BACKGROUND_COLOR);
			JSONObject colorJSON = ZSColor.getColorJSON(cellBackgroundColor, getZSTheme());
			if(!isExist(this.backgroundColors, colorJSON))
			{
				int index = -1;
				// SET NO FILL(TRANSPARENT) AS EMPTY IN TOP INDEX OF THE LIST
				if(cellBackgroundColor.getHexColorToWrite() != null && cellBackgroundColor.getHexColorToWrite().equals(TRANSPARENT) && !this.backgroundColors.contains(colorJSON))
				{
					this.backgroundColors = this.createNewHashSet(colorJSON, this.backgroundColors);
					index = 0;
				}
				// TODO - CHANGE EMPTY TO TRANSPARENT ONCE CLIENT HAS HANDLED THE CHANGES
				// SET DEFAULT BACKGROUND COLOR TO TOP INDEX ONLY IF NO FILL(TRANSPARENT) IS NOT PRESENT
				else if(cellBackgroundColor.equals(this.getDefaultBackgroundColor()))
				{
					this.backgroundColors = this.createNewHashSet(ZSColor.getColorJSON(this.getDefaultBackgroundColor(), getZSTheme()), this.backgroundColors);
					index = 0;
				}
				else
				{
					this.backgroundColors.add(colorJSON);
				}
				this.updateSelectedIndexes(cellBackgroundColor, index, FILTER_BY_CELL_COLOR);
			}
		}
	}

	private void updateFontColors(ReadOnlyCell readOnlyCell)
	{
		if(this.fontColors.size() < MAX_FILTER_COLORS_LIST)
		{
			Map<Integer, ZSColor> cellStyleMap = this.getHierarchialCellStyleMap(readOnlyCell);
			ZSColor cellFontColor = cellStyleMap.get(FONT_COLOR);
			JSONObject colorJSON = ZSColor.getColorJSON(cellFontColor, getZSTheme());
			if(!isExist(this.fontColors, colorJSON))
			{
				int index = -1;
				if(cellFontColor.equals(this.getDefaultFontColor()))
				{
					this.fontColors = this.createNewHashSet(colorJSON, this.fontColors);
					index = 0;
				}
				else
				{
					this.fontColors.add(colorJSON);
				}
				this.updateSelectedIndexes(cellFontColor, index, FILTER_BY_FONT_COLOR);
			}
		}
	}

	private Boolean isRowMatch(int rowIndex, BitSet visibleRows, BitSet filteredRowsSet)
	{
		return (visibleRows.get(rowIndex) || !filteredRowsSet.get(rowIndex));
	}

	private BitSet getVisibleRowsBeforeExecution()
	{
		FilterView filterView = this.getFilterView();
		FilterAction action = new FilterAction(filterView, this.getFilterView().excludeFilterConditionForColumn(this.getIndex()));
		BitSet visibleBitSets = this.getVisibleRowsBitSet();
		visibleBitSets.or(filterView.getBitSet());
		return action.process(visibleBitSets);
	}

	private void populateCellList()
	{
		FilterView filterView = this.getFilterView();
		Sheet sheet = this.getSheet();
		int startIndex = filterView.getStartRowIndexWithoutHeader();
		int columnIndex = this.getColumnIndex();
		BitSet filteredRowsSet = filterView.getBitSet();
		Boolean isExecuteConditions = filterView.hasFilterConditionOnColumn(this.getIndex());
		BitSet visibleSetBeforeExecution = new BitSet();
		BitSet visibleBitSet = new BitSet();
		if(isExecuteConditions)
		{
			visibleSetBeforeExecution = this.getVisibleRowsBeforeExecution();
			if(!visibleSetBeforeExecution.isEmpty())
			{
				// THIS IS TO ENSURE THAT DATA CHANGED IN A CELL AFTER APPLYING FILTER NEEDS TO BE SHOWN IN FILTER DIALOG LIST
				// ITERATE ALL UN HIDDEN ROWS AND SHOW SELECTED VALUES IN FILTER DIALOG LIST EVEN THOUGH VALUE OF CELL HAS BEEN CHANGED
				visibleBitSet = this.getVisibleRowsBitSet();
				visibleBitSet.or(this.getFilterView().getBitSet());
			}
		}
		else
		{
			visibleBitSet = this.getVisibleRowsBitSet();
		}
		int rowIndex = visibleBitSet.nextSetBit(startIndex);
		while(rowIndex != -1)
		{
			boolean isMatch = isExecuteConditions ? isRowMatch(rowIndex, visibleSetBeforeExecution, filteredRowsSet) : true;
			if(isMatch)
			{
				ReadOnlyCell readOnlyCell = this.getReadOnlyCell(rowIndex, columnIndex);
				if (readOnlyCell != null)
				{
					if(this.cellContents.size() < MAX_FILTER_ITEMS_LIST)
					{
						Value cellValue = this.getCellValue(readOnlyCell.getCell());
						String cellContent = this.getCellContent(readOnlyCell.getCell()).trim();
						TreeSet<String> cellContents = this.cellContents.computeIfAbsent(cellValue, contents -> new TreeSet<>(this.getContentComparator()));
						cellContents.add(cellContent);
						updateBackgroundColors(readOnlyCell);
						updateFontColors(readOnlyCell);
					}
				}
			}
			rowIndex += sheet.getReadOnlyRowFromShell(rowIndex).getRowsRepeated();
			rowIndex = visibleBitSet.nextSetBit(rowIndex);
		}

	}

	private void updateDataType() {
		int textCount = 0;
		int numberCount = 0;
		int dateCount = 0;

		// check only first 30 cells for data type detection
		int maxCellsForDataTypeDetection = Math.min(30, this.getFilterView().getEndRowIndex() - this.getFilterView().getStartRowIndex() + 1);

		for (int i = getStartRowIndex(); i <= maxCellsForDataTypeDetection + getStartRowIndex(); i++)
		{
			ReadOnlyCell readOnlyCell = this.getReadOnlyCell(i, columnIndex);
			if (readOnlyCell != null) {
				Cell cell = readOnlyCell.getCell();
				if (cell != null) {
					Cell.Type contentType = cell.getContentType();

					// Update counts based on content type
					if (contentType.isDateType()) {
						dateCount++;
					} else if (contentType.isNumberType()) {
						numberCount++;
					} else {
						textCount++;
					}
				} else {
					textCount++;
				}
			}
		}


		// Set the data type based on the highest count
		int maxCount = Math.max(dateCount, Math.max(textCount, numberCount));
		if (maxCount == numberCount) {
			this.dataType = DataType.NUMBER;
		} else if (maxCount == dateCount) {
			this.dataType = DataType.DATE;
		} else {
			this.dataType = DataType.TEXT;
		}
		responseObject.put(FILTER_DATA_TYPE_NEW, this.dataType.name());
	}

	private String getHeaderValue()
	{
		//Getting the column name if the header cell has empty value.
		Cell cell = this.getFilterView().getHeaderCell(this.getColumnIndex());
		String headerValue = cell.getContent();

		if(headerValue == null || EMPTY.equals(headerValue))
		{
			headerValue = "(" + LocaleMsg.getMsg("Filter.column") + " " + CellUtil.getColumnReference(this.getColumnIndex()) + ")";
		}
		else if(headerValue.length() > 20)
		{
			headerValue = headerValue.substring(0, 17) + "...";
		}
		return headerValue;
	}

	private DataType getDataType()
	{
		return RangeUtil.getDataType(Arrays.asList(new Range(this.getSheet(),this.getStartRowIndex(), this.getColumnIndex(), getFilterView().getEndRowIndexWithoutFooter(), this.getColumnIndex())));
	}

	private void postResponse(Boolean isDataNeeded)
	{
		org.json.JSONObject responseObject = this.getResponseObject();
		if(isDataNeeded)
		{
			Collection<?> cellDataList = this.getFilteredList(FILTER_BY_VALUE);
			Collection<?> cellColorList = this.getFilteredList(FILTER_BY_CELL_COLOR);
			Collection<?> fontColorList = this.getFilteredList(FILTER_BY_FONT_COLOR);

			if(appliedFilterType != null)
			{
				// UPDATE SELECTED INDEX AS CELL CONTENT WILL BE SORTED BASED ON LINKED HASHSET
				if(appliedFilterType.equals(FILTER_BY_VALUE))
				{
					this.selectedItemIndexes = new ArrayList<>();
					for(Object content : cellDataList)
					{
						this.updateSelectedIndexes(content,-1, FILTER_BY_VALUE);
					}
				}
				boolean isValueAvailable = this.selectedItemIndexes.contains(1);
				FilterType aFT = (!isValueAvailable && this.filteredData.size() <=2 && appliedFilterType.equals(FILTER_BY_VALUE)) ? CUSTOM_FILTER : appliedFilterType;
				responseObject.put(APPLIED_FILTER_TYPE, aFT.getKey());
				responseObject.put(NEW_APPLIED_FILTER_TYPE,  aFT.getId());
			}
			
			// TODO - SHIRU - TO BE REMOVED ONCE MOBILE APP SUPPORTS THEMES IN FILTERS
			Set<String> cellColorHexCodes = new LinkedHashSet<>();
			Iterator<?> iterator = cellColorList.iterator();
			while(iterator.hasNext())
			{
				JSONObject colorJSON = (JSONObject) iterator.next();
				String color = colorJSON.getString(Integer.toString(CommandConstants.HEX_COLOR));
				cellColorHexCodes.add(color.equalsIgnoreCase(TRANSPARENT) ? "" : color);
			}
			Set<String> textColorHexCodes = new LinkedHashSet<>();
			iterator = fontColorList.iterator();
			while(iterator.hasNext())
			{
				JSONObject colorJSON = (JSONObject) iterator.next();
				textColorHexCodes.add(colorJSON.getString(Integer.toString(CommandConstants.HEX_COLOR)));
			}
			responseObject.put(CELL_DATA, new JSONArray(cellDataList));
			responseObject.put(CELL_COLOR_LIST, cellColorHexCodes);
			responseObject.put(TEXT_COLOR_LIST, textColorHexCodes);
			responseObject.put(FILTER_SELECTED_ITEM_INDEXES, this.selectedItemIndexes);
			responseObject.put(FILTER_MAX_DATA, this.cellContents.size() >= MAX_FILTER_ITEMS_LIST);
			responseObject.put(FILTER_MAX_CELL_COLOR, this.backgroundColors.size() >= MAX_FILTER_COLORS_LIST);
			responseObject.put(FILTER_MAX_TEXT_COLOR, this.fontColors.size() >= MAX_FILTER_COLORS_LIST);

			// TODO - SHIRU - TO BE REMOVED ONCE MOBILE APP SUPPORTS THEMES IN FILTERS

			//  NEW RESPONSE
			responseObject.put(Integer.toString(CommandConstants.FILTER_CELL_COLOR_LIST), cellColorList);
			responseObject.put(Integer.toString(CommandConstants.FILTER_TEXT_COLOR_LIST), fontColorList);
			responseObject.put(Integer.toString(CommandConstants.FILTER_SELECTED_INDEXES), selectedItemIndexes);
			responseObject.put(Integer.toString(CommandConstants.FILTER_MAX_DATA), this.cellContents.size() >= MAX_FILTER_ITEMS_LIST);
			responseObject.put(Integer.toString(CommandConstants.FILTER_MAX_CELL_COLOR), this.backgroundColors.size() >= MAX_FILTER_COLORS_LIST);
			responseObject.put(Integer.toString(CommandConstants.FILTER_MAX_TEXT_COLOR), this.fontColors.size() >= MAX_FILTER_COLORS_LIST);


			// TODO - SHIRU - TO BE REMOVED ONCE MOBILE APP SUPPORTS THEMES IN FILTERS
		}
	}

	private void updateFilterConditions()
	{
		ZSTheme theme = this.getZSTheme();
		JSONObject responseObject = this.getResponseObject();
		FilterOperator operator1 = FilterOperator.NONE;
		FilterOperator operator2 = FilterOperator.NONE;
		Object value1 = null;
		Object value2 = null;
		String sValue = "";
		String category = "";
		String sType = "";

		FilterLogicalOperator filterLogicalOperator = FilterLogicalOperator.AND;
		boolean isSpecialCondition = false;
		List<JSONObject> appliedFilterValues = new ArrayList<>();
		Map<Filter, Map<Integer, List<FilterCondition>>> filterConditionsMap = this.getFilterView().getColumnFilterConditions(this.getIndex());
		for(Map.Entry<Filter, Map<Integer, List<FilterCondition>>> entry : filterConditionsMap.entrySet())
		{
			Map<Integer, List<FilterCondition>> filterConditions = entry.getValue();
			int i = 0;
			Filter filter = entry.getKey();
			filterLogicalOperator = filter.getType();
			// IT IS CONSIDERED TO BE CUSTOM FILTER EXCEPT SPECIAL CONDITIONS FOR A COLUMN INDEX IF THE FILTER CONDITION LIST SIZE IS MORE THAN 1
			for(FilterCondition filterCondition : filterConditions.get(this.getIndex()))
			{
				FilterOperator filterOperator = filterCondition.getOperator();
				appliedFilterType = filter.isAndFilter() || !EQUALS.equals(filterOperator) ? CUSTOM_FILTER : filterCondition.getFilterType();
				Object filterConditionValue = filterCondition.getValue();
//				String filterValue = filterConditionValue.equals(EMPTY) ? (appliedFilterType.equals(FILTER_BY_CELL_COLOR) ? TRANSPARENT : appliedFilterType.equals(FILTER_BY_FONT_COLOR) ? DEFAULT_TEXTCOLOR_HEXVALUE : filterConditionValue ) : filterConditionValue;

				//// This handle is for mobile client. once mobile client supports picklist filter, this can be removed
				if (filterOperator == FilterOperator.HAS_ANY_OF || filterOperator == FilterOperator.HAS_ALL_OF) {
					filterOperator = FilterOperator.CONTAINS;
				} else if (filterOperator == FilterOperator.HAS_NONE_OF) {
					filterOperator = FilterOperator.DOES_NOT_CONTAIN;
				} else if (filterOperator == FilterOperator.IS_EXACTLY) {
					filterOperator = FilterOperator.EQUALS;
				}
				if (filterConditionValue instanceof PicklistValue) {
					filterConditionValue = ((PicklistValue) filterConditionValue).getValueString(SpreadsheetSettings.defaultSpreadsheetSettings);
				}
				////

				filteredData.add(filterConditionValue);
				//String operator = filterOperator.toString();
				if(filterOperator.isSpecialOperator())
				{
					isSpecialCondition = true;
					FilterSpecialOperator fso = filterOperator.getFilterSpecialOperator();
					sType = fso.getType();
					category = fso.getCategory();
					sValue = (String)filterConditionValue;
				}
				if(!isSpecialCondition)
				{
					if(i == 0)
					{
						operator1 = filterOperator;
						value1 = filterConditionValue;
					}
					else
					{
						operator2 = filterOperator;
						value2 = filterConditionValue;
					}
				}
				i++;

				// NEW RESPONSE
				JSONObject appliedFilterValue = new JSONObject();
				appliedFilterValue.put(Integer.toString(CommandConstants.FILTER_OPERATOR), filterOperator.getOperatorText());
				appliedFilterValue.put(Integer.toString(CommandConstants.FILTER_VALUE), appliedFilterType.isColorFilter() ? ZSColor.getColorJSON((ZSColor) filterConditionValue, theme) : filterConditionValue);
				if(filterOperator.isSpecialOperator())
				{
					FilterSpecialOperator filterSpecialOperator = FilterSpecialOperator.getFilterSpecialOperator(filterOperator);
					appliedFilterValue.put(Integer.toString(CommandConstants.FILTER_SPECIAL_CONDITION), true);
					appliedFilterValue.put(Integer.toString(CommandConstants.FILTER_SPECIAL_OPERATOR_ID), filterSpecialOperator.getId());
					appliedFilterValue.put(Integer.toString(CommandConstants.FILTER_SPECIAL_OPERATOR_TYPE), filterSpecialOperator.getType());
					appliedFilterValue.put(Integer.toString(CommandConstants.FILTER_SPECIAL_OPERATOR_CATEGORY), filterSpecialOperator.getCategory());
				}
				appliedFilterValues.add(appliedFilterValue);
			}
		}
		if(appliedFilterType != null)
		{
			responseObject.put(APPLIED_FILTER_TYPE,  appliedFilterType.getKey());
			responseObject.put(Integer.toString(CommandConstants.APPLIED_FILTER_TYPE),  appliedFilterType.toString());
			if(appliedFilterType.isColorFilter())
			{
				responseObject.put(FILTER_VALUE_11, FilterType.getData(appliedFilterType, ZSColor.getHexColor((ZSColor) value1, theme)));
			}
			else
			{
				responseObject.put(FILTER_VALUE_11, FilterType.getData(appliedFilterType, (String)value1));
				responseObject.put(FILTER_VALUE_22, FilterType.getData(appliedFilterType, (String)value2));
			}
		}
		else
		{
			responseObject.put(FILTER_VALUE_11, "");
			responseObject.put(FILTER_VALUE_22, "");
		}

		responseObject.put(FILTER_TYPE_1, filterLogicalOperator.toString());
		responseObject.put(FILTER_OPERATOR_1, operator1.getOperatorInWords());
		responseObject.put(FILTER_OPERATOR_2, operator2.getOperatorInWords());

		responseObject.put(FILTER_SPECIAL_CONDITION, isSpecialCondition);
		responseObject.put(FILTER_SPECIAL_OPERATOR_TYPE, sType);
		responseObject.put(FILTER_SPECIAL_OPERATOR_CATEGORY, category);
		responseObject.put(FILTER_SPECIAL_OPERATOR_VALUE, sValue);


		// NEW RESPONSE
		responseObject.put(Integer.toString(CommandConstants.FILTER_LOGICAL_OPERATOR), filterLogicalOperator.getId());
		responseObject.put(Integer.toString(CommandConstants.FILTER_APPLIED_VALUES), appliedFilterValues);
	}

	private void preprocess()
	{
		this.responseObject = new JSONObject();
		responseObject.put(FILTER_COLUMN_INDEX, this.getColumnIndex());
		responseObject.put(FILTER_HEADER_VALUE, this.getHeaderValue());
		responseObject.put(FILTER_INDEX, this.getIndex());
		responseObject.put(FILTER_MAXIMUM_CRITERIA, MAX_FILTER_CRITERIA);


		// NEW RESPONSE
//		responseObject.put(Integer.toString(CommandConstants.FILTER_COLUMN_INDEX), this.getColumnIndex());
//		responseObject.put(Integer.toString(CommandConstants.FILTER_HEADER_VALUE), this.getHeaderValue());
//		responseObject.put(Integer.toString(CommandConstants.FILTER_INDEX), this.getIndex());
//		responseObject.put(Integer.toString(CommandConstants.FILTER_MAXIMUM_CRITERIA), MAX_FILTER_CRITERIA);
	}

	public JSONObject getResponse(boolean isDataNeeded)
	{
		long startTime = System.currentTimeMillis();

		this.preprocess();
		this.updateDataType();
		this.updateFilterConditions();
		this.populateCellList();
		this.postResponse(isDataNeeded);

		long timeTaken = System.currentTimeMillis() - startTime;
		logger.log(Level.INFO, "[FILTERS] Total time taken to retrieve filter data : {0} ms", timeTaken);

		return this.getResponseObject();
	}
}
