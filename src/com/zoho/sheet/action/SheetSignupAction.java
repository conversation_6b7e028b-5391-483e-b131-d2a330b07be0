/* $Id$ */
package com.zoho.sheet.action;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ContainerSynchronizer;
import com.zoho.sheet.util.StrutsRequestHandler;
import org.json.JSONObject;

import javax.servlet.ServletException;
import java.util.logging.Level;
import java.util.logging.Logger;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
/**
 *
 * <AUTHOR>
 */
public class SheetSignupAction extends StrutsRequestHandler
{

    public static Logger logger = Logger.getLogger(SheetSignupAction.class.getName());

    public String execute() throws Exception {

        JSONObject paramsJson = new JSONObject();

        String url = ClientUtils.getServerURL(request, false, null, false, true);
        try
        {

            String ticket = (request.getParameter("ticket") != null) ? request.getParameter("ticket") : IAMUtil.getCurrentTicket();
            String doc = request.getParameter("doc");

//            String docName = (request.getParameter("docName") == null || request.getParameter("docName").isEmpty()) ? LocaleMsg.getMsg("Untitled") : request.getParameter("docName");
            String docName = request.getParameter("docName");

            paramsJson.put("action", ActionConstants.SAVE_TO_MYACCOUNT);
            paramsJson.put("ticket", ticket);
            paramsJson.put("fromSignUp",true);


            if(docName != null && !docName.isEmpty())
            {
                paramsJson.put("sheetName", docName);
            }
            paramsJson.put("proxyURL", "copyfromsignup");
            paramsJson.put("iscsignature", SecurityUtil.sign());

            UserProfile.AccessType accesstype = UserProfile.AccessType.REMOTE;
            if(doc == null)
            {
                doc = request.getParameter("temp_container_identity");
                paramsJson.put("temp_container_identity", doc);
//                String rid = doc.contains("_") ? doc.split("_")[1] : doc;
//                paramsJson.put("rid", rid);

                doc = doc.contains("_") ? doc.split("_")[1] : doc;
                paramsJson.put("rid", doc);
                accesstype = DocumentUtils.getAccessIdentity(request.getParameter("viewIdentity"));

            }
            else
            {
                if(DocumentUtils.isValidResourceId(doc))
                {
                    accesstype = UserProfile.AccessType.AUTH;
                    paramsJson.put("rid", doc);
                }
                else
                {
	                	paramsJson.put("doc", doc);
	                	paramsJson.put("remoteView", true);
                }
            }

            String result = ContainerSynchronizer.postURLConnection(url, doc, paramsJson, accesstype);
            logger.log(Level.INFO, "SHEET SIGNUP CALL :: >> RESULT : {0}",result);

            if (result != null)
            {
                JSONObject json = new JSONObject(result);
                String resourceId = json.has("di") ? json.getString("di") : null;
                logger.log(Level.INFO, "resource id in sheetsignup : {0}", resourceId);

                response.sendRedirect("/sheet/open/"+resourceId);   //No I18N
            }
        } catch (Exception e)
        {
            logger.log(Level.INFO, "Error in SHEET SIGNUP CALL action ", e);
            throw new ServletException(e.getMessage());
        }
        return null;
    }
}
