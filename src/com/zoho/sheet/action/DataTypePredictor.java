/* $Id$ */
package com.zoho.sheet.action;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.analyse.DataType;
import com.adventnet.zoho.websheet.model.analyse.DiscreteDataType;
import com.adventnet.zoho.websheet.model.analyse.TypeDetecter;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import com.zoho.sheet.util.I18nMessage;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 * <AUTHOR>
 *
 */
public class DataTypePredictor {

    private static final Logger LOGGER = Logger.getLogger(DataTypePredictor.class.getName());
    private static final HashMap<String, Pattern> REG_EX = new HashMap<>();

//		Predictable data types are..
//		EMAIL,
//		WEBSITE,
//		DECIMAL,
//		DATE,
//		TIME,
//		DATETIME,
//		DROPDOWN,
//		RADIO,
//		CHECKBOXs
//		DECISION_BOX,
//		SINGLE_LINE,
//		MULTI_LINE,
//      CURRENCY
// 		PERCENTAGE
	
	
	static
	{			
		REG_EX.put("EMAIL", Pattern.compile("^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@"+ "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$"));
		REG_EX.put("WEBSITE", Pattern.compile("^(http[s]?://)?[A-Za-z0-9]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$"));
	}
	public static JSONObjectWrapper findDataTypes(Workbook workbook, int sR,int sC,int eR,int eC,String asn){
		return findDataTypes(workbook, sR, sC, eR, eC, asn, true);
	}
	public static JSONObjectWrapper findDataTypes(Workbook workbook, int sR,int sC,int eR,int eC,String asn, boolean isCol)
	{
		Sheet sheet = null;
		JSONObjectWrapper resultJson = new JSONObjectWrapper();
		try
		{
			sheet = workbook.getSheetByAssociatedName(asn);
		}
		catch(Exception e)
		{
			LOGGER.log(Level.INFO, "Error while fetching sheet object{0}", e);
		}

		Predicate<ReadOnlyCell> emailPredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null) {
				return true;
			} 
			Type type = cell.getContentType();
			if(type == Type.STRING && cell.getValueObject() instanceof String){
				String valueString = (String) cell.getValue().getValue();
				return checkWithRegEx(valueString,REG_EX.get("EMAIL"));
			}
			return false;							
		};	
		
		Predicate<ReadOnlyCell> websitePredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null) {
				return true;
			} 
			Type type = cell.getContentType();
			if(type == Type.STRING && cell.getValueObject() instanceof String){
				String valueString = (String) cell.getValue().getValue();
				return checkWithRegEx(valueString,REG_EX.get("WEBSITE"));
			}
			return false;							
		};
		
		Predicate<ReadOnlyCell> floatPredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null)
			{
				return true;
			}
			else
			{				
				Type type = cell.getContentType();
				return type == Type.FLOAT;
			}
			
		};
		
		Predicate<ReadOnlyCell> integerPredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null)
			{
				return true;
			}
			else
			{	
				if(cell.getContentType() == Type.FLOAT)
				{
					try {
						double num = FunctionUtil.objectToNumber(cell.getValue().getValue()).doubleValue();
						return Math.floor(num) == num;
					}catch(EvaluationException e)
					{
						LOGGER.log(Level.INFO, "Exception while getting number value : ", e);
					}
				}
				return false;
			}
			
		};
		
		Predicate<ReadOnlyCell> currencyPredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null)
			{
				return true;
			}
			else
			{
				return cell.getContentType() == Type.CURRENCY;
			}				
		};
		
		Predicate<ReadOnlyCell> datePredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null)
			{
				return true;
			}
			else
			{
				return cell.getContentType() == Type.DATE;
			}				
		};
		
		Predicate<ReadOnlyCell> timePredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null)
			{
				return true;
			}
			else
			{
				return cell.getContentType() == Type.TIME;
			}
		};
		
		Predicate<ReadOnlyCell> dateAndTimePredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null)
			{
				return true;
			}
			else
			{
				return cell.getContentType() == Type.DATETIME;
			}				
		};
		
		Predicate<ReadOnlyCell> dropDownPredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null){
				return true;
			}
			Type type = cell.getContentType();
			if(type== Type.ERROR){
				return false;
			}
			if(type == Type.STRING && cell.getValueObject() instanceof String){
				String valueString = (String) (cell.getValue().getValue());
				return !valueString.contains("\n") && !valueString.contains(",");//No I18N
			}
			return true;
		};
		
		Predicate<ReadOnlyCell> radiobuttonPredicate = dropDownPredicate;
		
		Predicate<ReadOnlyCell> checkboxPredicate = 	(ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null){
				return true;
			}
			Type type = cell.getContentType();
			if(type== Type.ERROR){
				return false;
			}
			if(type == Type.STRING && cell.getValueObject() instanceof String){
				String valueString = (String) (cell.getValue().getValue());
				return !valueString.contains("\n");//No I18N
			}
			return true;	
		};
						
		Predicate<ReadOnlyCell> decisionboxPredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();			
			if(cell == null || cell.getValueObject() == null)
			{
				return true;
			}
			return cell.getContentType() == Type.BOOLEAN;
		};
		
		Predicate<ReadOnlyCell> percentagePredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			if(cell == null || cell.getValueObject() == null) {
				return true;
			}
			return cell.getContentType() == Type.PERCENTAGE;
		};	
					
		Predicate<ReadOnlyCell> singleLinePredicate = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();			
			if(cell == null || cell.getValueObject() == null){
				return true;
			}
			Type type = cell.getContentType();
			if(type== Type.ERROR){
				return true;
			}
			if(type == Type.STRING && cell.getValueObject() instanceof String){
				String valueString = (String) cell.getValue().getValue();
				return !valueString.contains("\n") && valueString.length() < 25;//No I18N
			}			
			return true;								
		};
		
		Predicate<ReadOnlyCell> multiLinePredicate = (ReadOnlyCell roCell) -> {
			return true;					
		};
		
		Predicate<ReadOnlyCell> emptyPredicate = (ReadOnlyCell roCell) -> {
			Cell cell = roCell.getCell();			
			return (cell == null || cell.getValue().equals(Value.EMPTY_VALUE) || (cell.getContentType() == Type.STRING && "".equals(cell.getValueObject())));
		};
		
		Function<ReadOnlyCell, Set<Value>> checkboxSplitFunction = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			Value value = cell == null ? null : cell.getValue();
			
			if(value == null || value.equals(Value.EMPTY_VALUE))
			{
				return new HashSet<>();
			}
			Type type = value.getType();
			if(type == Type.STRING && cell.getValueObject() instanceof String)
			{
				String valueString = (String) value.getValue();
				String[] values = valueString.split(",");
				return Arrays.stream(values).map(str -> Value.getInstance(Type.STRING, str)).collect(Collectors.toSet());				
			}
			return new HashSet<>(Arrays.asList(value));
		};
		
		Function<ReadOnlyCell, Set<Value>> dropDownSplitFunction = (ReadOnlyCell roCell) -> {
			Cell cell= roCell.getCell();
			Value valueObj = cell == null ? null : cell.getValue();
			if(valueObj == null || valueObj.equals(Value.EMPTY_VALUE))
			{
				return new HashSet<>();
			}
			return new HashSet<>(Arrays.asList(valueObj));
		};
		
		Function<ReadOnlyCell, Set<Value>> radiobuttonSplitFunction = dropDownSplitFunction;
		
		DataType<ReadOnlyCell> floatType = new DataType<>("DECIMAL",floatPredicate);//No I18N
		DataType<ReadOnlyCell> integerType = new DataType<>("NUMBER",integerPredicate);//No I18N
		DataType<ReadOnlyCell> emailType = new DataType<>("EMAIL",emailPredicate);//No I18N
		DataType<ReadOnlyCell> websiteType = new DataType<>("WEBSITE",websitePredicate);//No I18N
		DataType<ReadOnlyCell> dateType = new DataType<>("DATE",datePredicate);//No I18N
		DataType<ReadOnlyCell> timeType = new DataType<>("TIME",timePredicate);//No I18N
		DataType<ReadOnlyCell> currencyType = new DataType<>("CURRENCY",currencyPredicate);//No I18N
		DataType<ReadOnlyCell> dateAndTimeType = new DataType<>("DATETIME",dateAndTimePredicate);//No I18N
		DataType<ReadOnlyCell> decisionboxType = new DataType<>("DECISION_BOX",decisionboxPredicate);//No I18N
		DataType<ReadOnlyCell> percentageType = new DataType<>("PERCENTAGE",percentagePredicate);//No I18N
		DataType<ReadOnlyCell> multiLineType = new DataType<>("MULTI_LINE",multiLinePredicate);//No I18N
		DataType<ReadOnlyCell> singleLineType = new DataType<>("SINGLE_LINE",singleLinePredicate);//No I18N
		DataType<ReadOnlyCell> emptyType = new DataType<>("EMPTY",emptyPredicate);//No I18N
		
		DiscreteDataType<ReadOnlyCell, Value> dropdownType = new DiscreteDataType<>("DROPDOWN", dropDownPredicate, dropDownSplitFunction,10);//No I18N
		DiscreteDataType<ReadOnlyCell, Value> checkboxType = new DiscreteDataType<>("CHECKBOX", checkboxPredicate, checkboxSplitFunction,5);//No I18N
		DiscreteDataType<ReadOnlyCell, Value> radioButtonType = new DiscreteDataType<>("RADIO", radiobuttonPredicate, radiobuttonSplitFunction,4);//No I18N
		
		Set<DataType<ReadOnlyCell>> dataTypeSet = new HashSet<>(Arrays.asList(emptyType, floatType,integerType,emailType,currencyType,websiteType,dateType,timeType,dateAndTimeType,decisionboxType,percentageType,multiLineType,singleLineType,dropdownType,checkboxType,radioButtonType));
		
                Set<DiscreteDataType<ReadOnlyCell, ?>> acceptOnlyWithDuplicatesSet = new HashSet<>(Arrays.asList(dropdownType, checkboxType, radioButtonType));
                
		Map<DataType<ReadOnlyCell>,Integer> priorities = new HashMap<>();
		priorities.put(multiLineType, 0);
		priorities.put(singleLineType, 1);
		priorities.put(dropdownType, 2);
		priorities.put(checkboxType, 3);		
		priorities.put(radioButtonType, 4);
		priorities.put(decisionboxType, 5);
		priorities.put(currencyType, 6);
		priorities.put(dateAndTimeType, 7);
		priorities.put(timeType, 8);
		priorities.put(dateType, 9);
		priorities.put(percentageType, 10);
		priorities.put(websiteType, 11);
		priorities.put(emailType, 12);		
		priorities.put(floatType, 13);
		priorities.put(integerType, 14);
		priorities.put(emptyType, 15);
		
		
		
		Map<DataType<ReadOnlyCell>, Set<DataType<ReadOnlyCell>>> exclusionSets = new HashMap<>();
	    Map<DataType<ReadOnlyCell>, Set<DataType<ReadOnlyCell>>> inclusionSets = new HashMap<>();
	    
	    inclusionSets.put(emailType, new HashSet<>(Arrays.asList(singleLineType,dropdownType,radioButtonType,checkboxType,multiLineType)));
	    inclusionSets.put(websiteType, new HashSet<>(Arrays.asList(singleLineType,dropdownType,radioButtonType,checkboxType,multiLineType)));
	    inclusionSets.put(floatType, new HashSet<>(Arrays.asList(singleLineType,dropdownType,radioButtonType,checkboxType,multiLineType)));
	    inclusionSets.put(dateType, new HashSet<>(Arrays.asList(singleLineType,dropdownType,radioButtonType,checkboxType,multiLineType)));
	    inclusionSets.put(timeType, new HashSet<>(Arrays.asList(singleLineType,dropdownType,radioButtonType,checkboxType,multiLineType)));
	    inclusionSets.put(dateAndTimeType, new HashSet<>(Arrays.asList(singleLineType,dropdownType,radioButtonType,checkboxType,multiLineType)));
	    inclusionSets.put(currencyType, new HashSet<>(Arrays.asList(singleLineType,dropdownType,radioButtonType,checkboxType,multiLineType)));
	    inclusionSets.put(dropdownType, new HashSet<>(Arrays.asList(singleLineType,radioButtonType,checkboxType,multiLineType)));
	    inclusionSets.put(radioButtonType, new HashSet<>(Arrays.asList(singleLineType,dropdownType,checkboxType,multiLineType)));
	    inclusionSets.put(checkboxType, new HashSet<>(Arrays.asList(singleLineType,multiLineType)));
	    inclusionSets.put(decisionboxType, new HashSet<>(Arrays.asList(singleLineType,multiLineType,radioButtonType,dropdownType,checkboxType)));
	    inclusionSets.put(percentageType, new HashSet<>(Arrays.asList(singleLineType,multiLineType,radioButtonType,dropdownType,checkboxType)));
	    inclusionSets.put(singleLineType, new HashSet<>(Arrays.asList(checkboxType,multiLineType)));	    
	    
	    exclusionSets.put(emailType, new HashSet<>(Arrays.asList(websiteType,floatType,dateType,timeType,dateAndTimeType,currencyType,decisionboxType,percentageType)));
	    exclusionSets.put(websiteType, new HashSet<>(Arrays.asList(emailType,floatType,dateType,timeType,dateAndTimeType,currencyType,decisionboxType,percentageType)));
	    exclusionSets.put(floatType, new HashSet<>(Arrays.asList(emailType,websiteType,dateType,timeType,dateAndTimeType,currencyType,decisionboxType,percentageType)));
	    exclusionSets.put(dateType, new HashSet<>(Arrays.asList(emailType,floatType,websiteType,timeType,dateAndTimeType,currencyType,decisionboxType,percentageType)));
	    exclusionSets.put(timeType, new HashSet<>(Arrays.asList(emailType,floatType,websiteType,dateType,dateAndTimeType,currencyType,decisionboxType,percentageType)));
	    exclusionSets.put(dateAndTimeType, new HashSet<>(Arrays.asList(emailType,floatType,websiteType,dateType,timeType,currencyType,decisionboxType,percentageType)));
	    exclusionSets.put(currencyType, new HashSet<>(Arrays.asList(emailType,floatType,websiteType,dateType,timeType,dateAndTimeType,decisionboxType,percentageType)));
	    exclusionSets.put(percentageType, new HashSet<>(Arrays.asList(emailType,floatType,websiteType,dateType,timeType,dateAndTimeType,decisionboxType,currencyType)));
	    exclusionSets.put(dropdownType, new HashSet<>());
	    exclusionSets.put(radioButtonType, new HashSet<>());
	    exclusionSets.put(checkboxType, new HashSet<>());
	    exclusionSets.put(decisionboxType, new HashSet<>(Arrays.asList(emailType,websiteType,floatType,dateType,timeType,dateAndTimeType,currencyType,percentageType)));
	    exclusionSets.put(singleLineType, new HashSet<>());
	    exclusionSets.put(multiLineType, new HashSet<>());
	    exclusionSets.put(emptyType, new HashSet<>(Arrays.asList(websiteType,floatType,dateType,timeType,dateAndTimeType,currencyType,decisionboxType,percentageType,emailType, integerType, multiLineType,singleLineType,dropdownType,checkboxType,radioButtonType)));
	    
		TypeDetecter<ReadOnlyCell> detect = new TypeDetecter<>(dataTypeSet, exclusionSets, inclusionSets, new HashMap<>(), priorities, acceptOnlyWithDuplicatesSet, emptyPredicate);
		int startRow, startCol, endRow, endCol;
        if (isCol) {
            startRow = sR;
            startCol = sC;
            endRow = eR;
            endCol = eC;
        } else {
            startRow = sC;
            startCol = sR;
            endRow = eC;
            endCol = eR;
        }
		Sheet finalSheet = sheet;
		IntStream.rangeClosed(startCol, endCol).forEach(colNum -> {
				JSONObjectWrapper dataType;
					dataType = checkDataType(colNum, startRow, endRow, finalSheet, detect, isCol);
					resultJson.put(String.valueOf(colNum), dataType);
		});
	
		return resultJson;
	}

	private static JSONObjectWrapper checkDataType(int colNumber,int startingRow,int endingRow,Sheet sheet,TypeDetecter<ReadOnlyCell> detect, boolean isCol) throws JSONWrapperException
	{
		JSONObjectWrapper json = new JSONObjectWrapper();
		Range range = (isCol) ? new Range(sheet, startingRow + 1, colNumber, endingRow, colNumber) : new Range(sheet, colNumber, startingRow + 1, colNumber, endingRow);
		DataType<ReadOnlyCell> finalDataType = detect.detectType(range.iterator());
		Set<String> valueSet = new HashSet<>();
		if(finalDataType instanceof DiscreteDataType){
			valueSet = ((DiscreteDataType<ReadOnlyCell, Value>) finalDataType).getUniqueValueSet().stream().map(value -> value.getValueString(SpreadsheetSettings.defaultSpreadsheetSettings)).collect(Collectors.toSet());
		}
		
		String header = null;
        Cell cell = (isCol) ? sheet.getReadOnlyCellFromShell(startingRow, colNumber).getCell() : sheet.getReadOnlyCellFromShell(colNumber, startingRow).getCell();
        Object valueObject = cell == null ? null : cell.getValue().getValue();
        if (valueObject != null) {
            header = valueObject.toString();
        }else{
            header = (isCol) ? getColumnHeader(colNumber, sheet) : "Row " + (colNumber + 1);// No I18N
        	//header = "";// No I18N
        }
        if (isCol) {
            json.put("colHead", header);
            json.put("label", getColumnHeader(colNumber, sheet));
        } else {
            json.put("rowHead", header);
            json.put("label", "Row " + (colNumber + 1));	//No I18N
        }
        
        String dataTypeName = finalDataType.getName();
        json.put("type", dataTypeName);
        json.put("values", valueSet);
        return json;
    }

    private static boolean checkWithRegEx(String value, Pattern pattern) {
        Matcher matcher = pattern.matcher(value);
        return matcher.matches();
    }
    
    
    private static String getColumnHeader(int col, Sheet sheet) {
        I18nMessage i18NMessage = new I18nMessage(sheet.getWorkbook().getFunctionLocale());
        String columnRef = CellUtil.getColumnReference(col);
        return i18NMessage.getMsg("column") + " " + columnRef;
    }
}
