/*$Id:$*/

package com.zoho.sheet.action;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.SharedUserInfoUtil;
import com.zoho.sheet.util.StrutsRequestHandler;

import java.io.PrintWriter;
import java.util.logging.Logger;

public class SharedUserInfoAction extends StrutsRequestHandler {

	public static Logger logger = Logger.getLogger(SharedUserInfoAction.class.getName());

	public String execute() throws Exception {
		
		String performerId = String.valueOf(IAMUtil.getCurrentUser().getZUID());
		WorkbookContainer wbcontainer = CurrentRealm.getContainer();
		String docOwnerZUID = wbcontainer.getDocOwnerZUID();
		PrintWriter out = response.getWriter();
		SharedUserInfoUtil SharedUserInfo = new SharedUserInfoUtil(request.getParameter("rid"), performerId, wbcontainer.getDocsSpaceId());
		out.println(SharedUserInfo.get(request.getParameter("rid"), performerId, docOwnerZUID, 0)); //NO OUTPUTENCODING
		return null;
	}
}
