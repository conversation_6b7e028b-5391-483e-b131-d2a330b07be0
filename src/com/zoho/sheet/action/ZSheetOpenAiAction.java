package com.zoho.sheet.action;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.OpenAIActionConstants;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.openai.*;
import com.zoho.sheet.openai.beans.AiRequestBean;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.logging.Logger;

/**
 * author - kailas.mk
 */
public class ZSheetOpenAiAction extends StrutsRequestHandler {
    public static final Logger LOGGER = Logger.getLogger(ZSheetOpenAiAction.class.getName());

    private static final boolean IS_OPENAI_ENABLED = ClientUtils.isFeatureEnabled("IS_OPENAI_ENABLED", "OPENAIENABLED_ORG_ID");//NO I18N
    public static final String ACTION_TYPE = "actionType";      //NO I18N
    public static final String API_KEY = "apiKey";              //NO I18N
    public static final String RID = "rid";                     //NO I18N
    public static final String WD_ORG = "wdOrgId";              //NO I18N
    public static final String QUERY = "query";                 //NO I18N
    public static final String FEATURE_ID = "featureId";        //NO I18N
    public static final String DISABLE = "disable";             //NO I18N

    @Override
    public String execute() throws Exception {
        JSONObjectWrapper responseObj = new JSONObjectWrapper();
        try {

            AiRequestBean reqBean = new AiRequestBean(request);
            int actionType = reqBean.getActionType();
            responseObj = reqBean.getResponseObj();

            if (reqBean.isWorkdrive() && (reqBean.getResourceId() == null || (reqBean.getWdOrgId() != null && reqBean.getrId() != null))) {
                // masked req, Param mismatch (either rid and wdOrgId is null or both are present)
                throw new OpenAiInternalException(OpenAiConstants.ERRORS.PARAM_MISMATCH.name(), HttpServletResponse.SC_BAD_REQUEST);
            }

            if (Objects.equals(reqBean.getzSoid(), "")) {
                throw new OpenAiInternalException(OpenAiConstants.ERRORS.ACCESS_DENIED.name(), HttpServletResponse.SC_UNAUTHORIZED);
            }

            if(reqBean.isWorkdrive()){
                // docs user filter layer
                filterDocsUserRequest(reqBean);
                // Filter non-admin user layer
                filterNonAdminUser(reqBean);
            }

            switch (actionType) {
                case OpenAIActionConstants.FETCH_USER_DETAILS:
                    handleFetchUserRequest(reqBean);
                    break;
                case OpenAIActionConstants.TOGGLE_FEATURE:
                    handleToggleFeatureRequest(reqBean);
                    break;
                case OpenAIActionConstants.ADD_TOKEN:
                    handleAddTokenRequest(reqBean);
                    break;
                case OpenAIActionConstants.API_KEY_REMOVE:
                    handleApiKeyRemoveRequest(reqBean);
                    break;
                case OpenAIActionConstants.ADMIN_DETAILS:
                    handleAdminDetailsRequest(reqBean);
                    break;
                case OpenAIActionConstants.CONTINUE_INTEG:
                    handleContinueIntegRequest(reqBean);
                    break;
                default:
                    handleGptRequest(reqBean);
                    break;
            }
            writeResponseToClient(responseObj, response); // flush the response to client

        } catch (OpenAiInternalException ex) {
            responseObj.put("error", ex.getMessage());
            writeResponseToClient(responseObj, response);
        } catch (Exception e) {
            //unhandled exception occurred
            PlatformAILogger.writeInLog("Exception Occurred in OpenAI Action", e);   //No I18N
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.ERROR, PlatformAIFunctions.ResponseStatus.INTERNAL_SERVER_ERROR.name());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

            HttpServletResponseWrapper.sendResponse(response, responseObj, HttpServletResponseWrapper.MimeType.JSON);
        }
        return null;
    }

    private static void writeResponseToClient(JSONObjectWrapper responseObject, HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK); // all response set to 200
        HttpServletResponseWrapper.sendResponse(response, responseObject, HttpServletResponseWrapper.MimeType.JSON);
    }

    private static void handleFetchUserRequest(AiRequestBean reqBean) throws Exception {
        /*
         * This action is called only once
         * isToken present is checked from DB not from PlatformAI other services
         */
        String zSoid = reqBean.getzSoid();
        JSONObjectWrapper responseObj = reqBean.getResponseObj();

        List<Integer> disabledFeatureList = new ArrayList<>();
        PlatformAIStore storeObj = PlatformAIAuthUtils.getParamStore(zSoid);

        List<String> serviceList = PlatformAIConsole.getServiceList(zSoid);
        responseObj.put(OpenAiConstants.RESPONSE_HEADERS.SERVICE, serviceList);

        if (storeObj == null) {
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.IS_TOKEN_PRESENT, false);
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.DISABLE_FEATURE, disabledFeatureList);
        } else {
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.IS_TOKEN_PRESENT, true);
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.DISABLE_FEATURE, PlatformAIAuthUtils.bitsToDisabledList(storeObj.getFeatureBits()));
        }
    }

    private void handleAdminDetailsRequest(AiRequestBean reqBean) throws OpenAiInternalException {
        String zuid = reqBean.getZuid();
        String zSoid = reqBean.getzSoid();
        JSONObjectWrapper responseObj = reqBean.getResponseObj();

        boolean isAdmin = PlatformAIConsole.isUserAdmin(zuid, zSoid);
        responseObj.put(OpenAiConstants.RESPONSE_HEADERS.ISADMIN, isAdmin);
        if (!isAdmin) {
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.ADMIN_CONTACT, PlatformAIAuthUtils.getAdminContact(zSoid, zuid));
        }
    }

    private void handleContinueIntegRequest(AiRequestBean reqBean) throws OpenAiInternalException {
        String zSoid = reqBean.getzSoid();
        PlatformAIConsole.continueInteg(zSoid);
        reqBean.getResponseObj().put(OpenAiConstants.RESPONSE_HEADERS.OPERATION, OpenAiConstants.RESPONSE_HEADERS.SUCCESS);
    }

    private static void handleToggleFeatureRequest(AiRequestBean reqBean) throws OpenAiInternalException {
        String zSoid = reqBean.getzSoid();
        boolean toggleState = reqBean.getToggleState();
        int featureId = 0;
        PlatformAIConsole.toggleFeatureList(zSoid, toggleState, featureId);
        reqBean.getResponseObj().put(OpenAiConstants.RESPONSE_HEADERS.OPERATION, OpenAiConstants.RESPONSE_HEADERS.SUCCESS);
    }

    private static void handleAddTokenRequest(AiRequestBean reqBean) throws Exception {
        String zSoid = reqBean.getzSoid();
        String apiKey = reqBean.getApiKey();
        JSONObjectWrapper responseObj = reqBean.getResponseObj();
        boolean status = PlatformAIConsole.addKey(apiKey, zSoid);
        if (status) { // apikey is valid and token added successfully
            // create Auth Token and add it in db;
//            PlatformAIConsole.generateOAuthRefreshToken(zSoid);
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.OPERATION, OpenAiConstants.RESPONSE_HEADERS.SUCCESS);
        } else {
            //invalid api key
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.ERROR, OpenAiConstants.RESPONSE_HEADERS.INVALIDKEY);
        }
    }

    private static void handleApiKeyRemoveRequest(AiRequestBean reqBean) throws Exception {
        PlatformAIConsole.deleteApiKey(reqBean.getzSoid());
        reqBean.getResponseObj().put(OpenAiConstants.RESPONSE_HEADERS.OPERATION, OpenAiConstants.RESPONSE_HEADERS.SUCCESS);
    }

    private void handleGptRequest(AiRequestBean reqBean) throws OpenAiInternalException {
        JSONObjectWrapper responseObject = reqBean.getResponseObj();
        String zSoid = reqBean.getzSoid();
        int actionType = reqBean.getActionType();

        OpenAiConstants.FEATURE_LIST featureName = OpenAiConstants.featureIndex.get(actionType);
        PlatformAIStore aiStore = PlatformAIAuthUtils.getParamStore(zSoid);

        boolean isFeatureEnabled;
        boolean isIntegPaused;

        if (aiStore == null) {
            responseObject.put(OpenAiConstants.RESPONSE_HEADERS.ERROR, OpenAiConstants.ERRORS.TOKEN_NOT_FOUND.name());
            return;
        }

        isFeatureEnabled = PlatformAIAuthUtils.isTheFeatureEnabled(aiStore.getFeatureBits(), featureName);
        isIntegPaused = PlatformAIAuthUtils.isTheFeatureEnabled(aiStore.getFeatureBits(), OpenAiConstants.FEATURE_LIST.PAUSE_INTEGRATION);

        //feature level filter
        if (!isIntegPaused) {
            responseObject.put(OpenAiConstants.RESPONSE_HEADERS.ERROR, OpenAiConstants.ERRORS.INTEG_DISABLED.name());
            return;
        }
        if (!isFeatureEnabled) {
            responseObject.put(OpenAiConstants.RESPONSE_HEADERS.ERROR, OpenAiConstants.ERRORS.FEATURE_DISABLED.name());
            return;
        }

        String query = decodeString(reqBean.getQuery());
        PlatformAIFunctions.ConnectionResponse connectionResponse;

        switch (actionType) {
            case OpenAIActionConstants.GPT_GENERAL_QUERY:
                connectionResponse = PlatformAIFunctions.gptGeneralQuery(query, aiStore, true);
                break;
            case OpenAIActionConstants.GPT_GENERATE_FORMULA:
                connectionResponse = PlatformAIFunctions.gptGenerateFormula(query, aiStore, true);
                break;
            case OpenAIActionConstants.GPT_EXPLAIN_FORMULA:
                connectionResponse = PlatformAIFunctions.gptExplainFormula(query, aiStore, true);
                break;
            case OpenAIActionConstants.GPT_GENERATE_VBA:
                connectionResponse = PlatformAIFunctions.gptGenerateVBA(query, aiStore, true);
                break;
            case OpenAIActionConstants.GPT_EXPLAIN_VBA:
                connectionResponse = PlatformAIFunctions.gptExplainVBA(query, aiStore, true);
                break;
            case OpenAIActionConstants.GPT_TABLE:
                connectionResponse = PlatformAIFunctions.gptTable(query, aiStore, true);
                break;
            case OpenAIActionConstants.GPT_EXTRACT_FILE:
                connectionResponse = PlatformAIFunctions.gptExtract(query, aiStore, true);
            default:
                throw new OpenAiInternalException(OpenAiConstants.ERRORS.INVALID_ACTION.name(), HttpServletResponse.SC_BAD_REQUEST);
        }

        setQueryResponse(connectionResponse, responseObject);

        LOGGER.info("[OPEN-AI-USAGE] GPT request handled :: Action Constant : " + actionType);
    }

    private static void setQueryResponse(PlatformAIFunctions.ConnectionResponse connectionResponse, JSONObjectWrapper responseObj) {
        PlatformAIFunctions.ResponseStatus status = connectionResponse.getResponseStatus();
        Object responseContent = connectionResponse.getResponseContent();
        int statusCode = PlatformAIFunctions.ResponseStatus.getResponseCode(status);
        if (statusCode == HttpServletResponse.SC_OK) {
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.RESPONSE, responseContent);
        } else {
            // cases where no exception, but invalid responses from openai connections refer PlatformAIFunctions.java
            responseObj.put(OpenAiConstants.RESPONSE_HEADERS.ERROR, status.name());
        }
    }

    private void filterNonAdminUser(AiRequestBean reqBean) throws OpenAiInternalException {
        int actionType = reqBean.getActionType();
        String zSoid = reqBean.getzSoid();
        String zuid = reqBean.getZuid();
        switch (actionType) {
            case OpenAIActionConstants.TOGGLE_FEATURE:
            case OpenAIActionConstants.ADD_TOKEN:
            case OpenAIActionConstants.API_KEY_REMOVE:
            case OpenAIActionConstants.CONTINUE_INTEG:
                boolean isUserAdmin = PlatformAIConsole.isUserAdmin(zuid, zSoid);
                if (!isUserAdmin) {
                    throw new OpenAiInternalException(OpenAiConstants.ERRORS.PERMISSION_DENIED.name(), HttpServletResponse.SC_UNAUTHORIZED);
                }
                break;
        }
    }

    private static void filterDocsUserRequest(AiRequestBean reqBean) throws OpenAiInternalException {
        boolean isWorkdrive = reqBean.isWorkdrive();
        String wdOrgId = reqBean.getWdOrgId();
        if (!isWorkdrive &&  wdOrgId == null) {
            // req is from open sheet
            throw new OpenAiInternalException(OpenAiConstants.ERRORS.DOCS_USER_ERROR.name(), HttpServletResponse.SC_UNAUTHORIZED);
        }
        else if(wdOrgId != null){
            // req is from listing page
            if(wdOrgId.equals(" ")){
                throw new OpenAiInternalException(OpenAiConstants.ERRORS.DOCS_USER_ERROR.name(), HttpServletResponse.SC_UNAUTHORIZED);
            }
        }
    }

    private static String decodeString(String query) {
        return query != null ? Utility.getDecodedString(query) : "";
    }
}