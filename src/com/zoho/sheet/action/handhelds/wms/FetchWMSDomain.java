package com.zoho.sheet.action.handhelds.wms;

import java.util.Hashtable;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.adventnet.iam.IAMUtil;
import com.adventnet.wms.api.WebMessanger;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;

/**
 * <AUTHOR>
 * <p>
 * To get the domain and subdomain from wms . Used for establishing pex connection in handhelds , Mac and Windows
 */

public class FetchWMSDomain extends StrutsRequestHandler {
    public static Logger logger = Logger.getLogger(FetchWMSDomain.class.getName());

    public static final String ERROR = "ERROR";//No i18N
    public static final String ERROR_MSG = "ERROR_FETCHING_WMS_DOMAIN";//No i18N
    public static final String ERROR_MSG_1 = "ERROR_ZUID_EMPTY";//No i18N

    @Override
    public String execute() throws Exception {

        JSONObjectWrapper responseJSON = new JSONObjectWrapper();

        String currentUserName = request.getParameter("rmUserName");

        if ((IAMUtil.getCurrentUser() == null) && (currentUserName == null || currentUserName.isEmpty())) {
            // return error
            logger.info("Exception  --  user info empty : ");
            responseJSON.put(ERROR, ERROR_MSG_1);
        }

        if ((currentUserName == null || currentUserName.isEmpty()) && (IAMUtil.getCurrentUser() != null)) {
            // auth case
            currentUserName = DocumentUtils.getZUID().toString();
        }
        if (currentUserName != null && !currentUserName.isEmpty()) {

            try {

                Hashtable wmsDomainInfo = WebMessanger.getWmsDomain(request.getServerName(), currentUserName.toString());

                if (!wmsDomainInfo.isEmpty()) {
                    String domain = null;
                    String subdomain = null;

                    if (wmsDomainInfo.containsKey("domain")) {
                        domain = (String) wmsDomainInfo.get("domain");
                    }
                    if (wmsDomainInfo.containsKey("subdomain")) {
                        subdomain = (String) wmsDomainInfo.get("subdomain");
                    }
                    logger.info("WMS Fetch Success  --  domain  : " + domain + "  " + subdomain);

                    if (domain != null && !domain.isEmpty()) {
                        responseJSON.put("domain", domain);
                    }
                    if (subdomain != null && !subdomain.isEmpty()) {
                        responseJSON.put("subdomain", subdomain);
                    }
                }
            } catch (Exception e) {
                logger.info("Exception  --  exception on fetching domain from WMS  : " + e);
                responseJSON.put(ERROR, ERROR_MSG);
            }
        }

        if (responseJSON.isEmpty()) {
            logger.info("Exception  --  exception on fetching domain and subdomain from WMS  : ");
            responseJSON.put(ERROR, ERROR_MSG);
        }

        response.getWriter().println(responseJSON.toString());
        return null;
    }
}
