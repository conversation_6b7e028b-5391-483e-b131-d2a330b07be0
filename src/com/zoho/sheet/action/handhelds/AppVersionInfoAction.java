/* $Id$ */

package com.zoho.sheet.action.handhelds;

import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ClientUtils.ResourceType;

public class AppVersionInfoAction extends StrutsRequestHandler {

	public static Logger logger = Logger.getLogger(AppVersionInfoAction.class.getName());

	@Override
	public String execute() throws Exception {

		String mode = request.getParameter("mode");

		String appVersion = "";
		boolean isNewUrlSupported = false;
		boolean isNameRangeUpdated = false;

		/** FOR IOS **/
		String fontTTFVersion = "";

		/** IPAD **/

		boolean isIpadAppReleased = false;
		boolean isIpadForceUpdate = false;
		String ipadAppStoreUrl = "";
		String ipadAppVersion = "";

		/** IPHONE **/
		boolean isIphoneAppReleased = false;
		boolean isIphoneForceUpdate = false;
		String iphoneAppStoreUrl = "";
		String iphoneAppVersion = "";


		/** FOR ANDROID **/
		String forcedVersion = "";
		JSONObjectWrapper chartCompjsPath = null;

		/** FOR MAC **/
		String testFlightMacAppUrl = "";
		String appStoreMacAppUrl = "";

		/** WEBFONTS URL **/
		String webFontsPath = null;

		/** RICH TEXT FORMAT **/
		boolean isRichTextReleased = false;

		/** SPECIAL CHARACTERS IN SHEET NAME **/
		boolean isSheetNameWithSpecialCharactersReleased = false ;

		/** MARK AS FINAL RELEASE **/
		boolean isMarkAsFinalReleased = false;

		if(mode != null) {
			if(mode.equals("ios")) {

				appVersion = EnginePropertyUtil.getHandheldPropertyValue("IOS_VERSION"); //No I18N
				fontTTFVersion = EnginePropertyUtil.getHandheldPropertyValue("IOS_FONT_TTF_VERSION"); //No I18N

				/** IPAD **/
				isIpadAppReleased = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_IPAD_APP_RELEASED")); //No I18N
				ipadAppStoreUrl = EnginePropertyUtil.getHandheldPropertyValue("IPAD_APP_STORE_URL"); //No I18N
				ipadAppVersion = EnginePropertyUtil.getHandheldPropertyValue("IPAD_APP_VERSION"); //No I18N
				isIpadForceUpdate = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_IPAD_FORCE_UPDATE")); //No I18N

				/** IPHONE **/
				isIphoneAppReleased = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_IPHONE_APP_RELEASED")); //No I18N
				iphoneAppStoreUrl = EnginePropertyUtil.getHandheldPropertyValue("IPHONE_APP_STORE_URL"); //No I18N
				iphoneAppVersion = EnginePropertyUtil.getHandheldPropertyValue("IPHONE_APP_VERSION"); //No I18N
				isIphoneForceUpdate = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_IPHONE_FORCE_UPDATE")); //No I18N

				isRichTextReleased = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_RICH_TEXT_FORMAT_RELEASED"));//No I18N

			} else if(mode.equals("android")) {
				appVersion = EnginePropertyUtil.getHandheldPropertyValue("ANDROID_VERSION");	//No I18N
				forcedVersion = EnginePropertyUtil.getHandheldPropertyValue("ANDROID_FORCED_VERSION");	//No I18N

				// added to get JS file path and Build Version
				chartCompjsPath =  new JSONObjectWrapper();
				chartCompjsPath.put("jsBuildVersion", EngineConstants.jsVersion);
				chartCompjsPath.put("jsCompressionPath", ClientUtils.getPath(ResourceType.CHART_APPSHEET_JS ,request));
				chartCompjsPath.put("securityJSPath",ClientUtils.getPath(ResourceType.SECURITY_JS, request));
				chartCompjsPath.put("chartCompressionPath", ClientUtils.getPayloadLink(request, ResourceType.APPSHEET_JS, "chart_compressed.js")); // No I18N
				chartCompjsPath.put("knitchartsCompressionPath", ClientUtils.getPayloadLink(request, ResourceType.APPSHEET_JS, "knitchart_compressed.js")); // No I18N
				chartCompjsPath.put("knitcharts_charts", ClientUtils.getPayloadLink(request, ResourceType.UNCOMPRESSED_JS, "Chart/node/charts.js")); // No I18N
				chartCompjsPath.put("knitcharts_vendors", ClientUtils.getPayloadLink(request, ResourceType.UNCOMPRESSED_JS, "Chart/node/vendors.js")); // No I18N

			}else if(mode.equals("macOS")){

					appVersion = EnginePropertyUtil.getHandheldPropertyValue("CURRENT_MAC_APP_VERSION");//NO I18N
					forcedVersion = EnginePropertyUtil.getHandheldPropertyValue("MIN_REQUIRED_MAC_APP_VERSION");//NO I18N

					fontTTFVersion = EnginePropertyUtil.getHandheldPropertyValue("IOS_FONT_TTF_VERSION"); //No I18N

					isRichTextReleased = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_RICH_TEXT_FORMAT_RELEASED"));//No I18N

					testFlightMacAppUrl = EnginePropertyUtil.getHandheldPropertyValue("TEST_FLIGHT_MAC_APP_URL");//NO I18N
					appStoreMacAppUrl = EnginePropertyUtil.getHandheldPropertyValue("MAC_APPSTORE_URL");//NO I18N

			}else if(mode.equals("windows")){
					appVersion = EnginePropertyUtil.getHandheldPropertyValue("WINDOWS_VERSION");//NO I18N
					forcedVersion = EnginePropertyUtil.getHandheldPropertyValue("WINDOWS_FORCE_UPDATE_VERSION");//NO I18N

			}

			webFontsPath = EnginePropertyUtil.getSheetPropertyValue("WEBFONTS_STATIC_SERVER_URL"); //NO I18N
			isNewUrlSupported = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_NEW_URL_SUPPORTED"));//No I18N
			isSheetNameWithSpecialCharactersReleased = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_SHEET_NAME_SPECIAL_CHARACTERS_RELEASED"));//No I18N
			isNameRangeUpdated = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_NAME_RANGE_UPDATED"));//No I18N
			isMarkAsFinalReleased = Boolean.parseBoolean(EnginePropertyUtil.getHandheldPropertyValue("IS_MARK_AS_FINAL_RELEASED"));//No I18N

		}
		JSONObjectWrapper versionObj = new JSONObjectWrapper();

		versionObj.put("version", appVersion);  // need to remove this for ios
		versionObj.put("isNewUrl", isNewUrlSupported);
		versionObj.put("isSheetNameWithSpecialCharactersReleased", isSheetNameWithSpecialCharactersReleased);
		versionObj.put("isNameRangeUpdated",isNameRangeUpdated);
		versionObj.put("isMarkAsFinalReleased", isMarkAsFinalReleased);
		if(webFontsPath!=null && !webFontsPath.isEmpty()) {
			versionObj.put("webFontsPath", webFontsPath);
		}

		/** FOR IOS  **/

		/** Font Version **/
		if(!fontTTFVersion.isEmpty()) {
			versionObj.put("fontTTFVersion", fontTTFVersion);
		}

		/** Ipad AppStoreUrl **/
		if(!ipadAppStoreUrl.isEmpty()) {
			versionObj.put("ipadAppStoreUrl", ipadAppStoreUrl);
		}

		/** Iphone AppStoreUrl **/
		if(!iphoneAppStoreUrl.isEmpty()) {
			versionObj.put("iphoneAppStoreUrl", iphoneAppStoreUrl);
		}

		/** Ipad/Iphone Release Status  and Version **/
		if(mode!=null && mode.equals("ios")) {

			versionObj.put("isIpadAppReleased", isIpadAppReleased);
			versionObj.put("ipadAppVersion", ipadAppVersion);
			versionObj.put("isIpadForceUpdate", isIpadForceUpdate);

			versionObj.put("isIphoneAppReleased", isIphoneAppReleased);
			versionObj.put("iphoneAppVersion", iphoneAppVersion);
			versionObj.put("isIphoneForceUpdate", isIphoneForceUpdate);
			versionObj.put("richtextformatreleased", isRichTextReleased);
		}

		/** FOR ANDROID **/

		/**  Forced Version **/
		if(!forcedVersion.isEmpty()) {
			versionObj.put("fVersion", forcedVersion);
		}

		/**  Chart JS Static Server Path **/
		if(chartCompjsPath!=null && !chartCompjsPath.isEmpty()) {
			versionObj.put("jsFileInfo", chartCompjsPath);
		}

		/** FOR MAC **/

		if(!testFlightMacAppUrl.isEmpty()){
			versionObj.put("testFlightMacAppUrl", testFlightMacAppUrl);
		}
		if(!appStoreMacAppUrl.isEmpty()){
			versionObj.put("appStoreMacAppUrl", appStoreMacAppUrl);
		}


		response.getWriter().print(versionObj.toString());	//NO OUTPUTENCODING

		return null;
	}
}
