/* $Id$ */
package com.zoho.sheet.action.handhelds;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ErrorCode.DisplayType;
import com.adventnet.zoho.websheet.model.ErrorCode.MsgType;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.action.ActionObject;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.sheet.util.textimport.ContentParser;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceType;
import com.zoho.zfsng.constants.ServiceType;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 
 * <AUTHOR>
 * 
 * PURPOSE: To create a workbook for a new OCR data_from_picture
 *
 */

public class DataFromPictureToWorkBook extends StrutsRequestHandler {
	
	
	 public static final Logger LOGGER = Logger.getLogger(DataFromPictureToWorkBook.class.getName());
	 public final static String ERROR_MSG_DOC_NAME_LENGTH_EXCEEDED = "Document name can contain only a maximum of 255 characters";//No I18N
	 public final static String ERROR_MSG_SHEET_NAME_LENGTH_EXCEEDED = "Sheet name can contain a maximum of 31 characters";//No I18N

		public String execute() throws Exception{

			try {
		    	
		    		User 		currentuser 		= 	IAMUtil.getCurrentUser();
		    		
		    		int 		libType 			= 	ResourceType.DOC_LIBRARY;
		    		
		    		long zuid = currentuser.getZUID();
		    		
		    	    String 		libraryId 			= 	ZohoFS.getMyFolderId(""+zuid);		    	    
		    	   
		    	    String 		ownerId 			= 	""+zuid;
		    	    	    	    
		    	    JSONObjectWrapper 	respObj				=	new JSONObjectWrapper();
			    	JSONArrayWrapper 	sheetList 			= 	new JSONArrayWrapper();
			    	JSONArrayWrapper	tempSheetListAry	=	new JSONArrayWrapper();
			    	JSONArrayWrapper 	rangeList 			= 	new JSONArrayWrapper();
			    	JSONArrayWrapper	tempRangeListAry	=	new JSONArrayWrapper();
			    	
			    	JSONObjectWrapper 	range				=	getDummyRangeJson();

			    	tempSheetListAry.put("0#");
			    	sheetList.put(tempSheetListAry);
			    	tempRangeListAry.put(range);
			    	rangeList.put(tempRangeListAry);
			    	
			    	String documentName = request.getParameter("documentName");
			    	String sheetName = request.getParameter("sheetName");
			    	
			    	try {
			    		 if(documentName!=null && !documentName.isEmpty()){
			    			 documentName =  URLDecoder.decode(documentName, "UTF-8"); // NO I18N
			    		 }
			    		 
			    		 if(sheetName!=null && !sheetName.isEmpty()){
			    			 sheetName =  URLDecoder.decode(sheetName, "UTF-8"); // NO I18N
			    		 }
			    	}catch(Exception e) {
			    		   LOGGER.log(Level.WARNING, "[DATA FROM PICTURE]Error while decoding file name", e);
			                
			    	}
			    	
			    	if(documentName!=null && !documentName.isEmpty() && documentName.length()>255){
			    		response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
			    		respObj.put("error", ERROR_MSG_DOC_NAME_LENGTH_EXCEEDED);
			    		response.getWriter().write(respObj.toString());
			    		return null;
			    	}
			    	
			    	if(sheetName!=null && !sheetName.isEmpty() && sheetName.length()>31){
			    		response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
			    		respObj.put("error", ERROR_MSG_SHEET_NAME_LENGTH_EXCEEDED);
			    		response.getWriter().write(respObj.toString());
			    		return null;
			    	}
			    	
			    	
		    	    String rid = DocumentUtils.addNewDocumentOrTemplateToZFSNG(ownerId, ""+zuid, (documentName == null || documentName.isEmpty())? "Untitled spreadsheet" : documentName, libType, ResourceType.WORKBOOK_NATIVE, libraryId, ServiceType.SHEET, null);

			    //    String loginName = DocumentUtils.getZUserName(ownerId);

		    	    ownerId = ZohoFS.getOwnerZID(rid);
		    	    String spaceName = DocumentUtils.getZUserName(ownerId);

			    	List<DataRange> listOfDataRanges=ActionJsonUtil.getListOfDataRangesFromJsonArray(sheetList,rangeList);
			    	LOGGER.info("[DataFromPic] check for listofdataranges>>"+listOfDataRanges);
			    	List<List<String>> pictureData= getPictureData(request);

				WorkbookContainer container = EngineFactory.getNewWorkbookContainer(spaceName, rid, currentuser);

			        Workbook workbook=container.getWorkbook(null);
			        
			        if(sheetName!=null && !sheetName.isEmpty()){
			        	Sheet sheet = workbook.getSheetByAssociatedName(listOfDataRanges.get(0).getAssociatedSheetName());
			        	sheet.setName(sheetName);
			        }
			        
			    	ActionUtil.copyPasteImageContent(workbook,listOfDataRanges, pictureData , null, null, 0, false, true); 

			    	container.save("DataFromPicture", spaceName, true);//No I18N

			    	respObj.put("rid", rid); //No I18N			    	
			        response.getWriter().write(respObj.toString());
			      
	        } catch (Exception e) {
	        	
	            LOGGER.log(Level.WARNING, "[DataFromPic]Error while creating newWorkbook for datafrompicture", e); 
	            
	            String 		errorMsg 	=	e.getMessage();
	            JSONObjectWrapper	errorObj	= 	ErrorCode.getErrorMessage(errorMsg, null, MsgType.ERROR, DisplayType.BANNER);

        		response.getWriter().println(errorObj);
	        }

			return null;

		}
		
		private JSONObjectWrapper	getDummyRangeJson() {
	    	/*
	    	 * As client is not providing any range list so we are making on assumptions
	    	 */
	    	  
	        JSONObjectWrapper rangelist = new JSONObjectWrapper();
	        
	        rangelist.put(JSONConstants.START_ROW,0);
	        rangelist.put(JSONConstants.START_COLUMN,0);
	        rangelist.put(JSONConstants.END_ROW,0);
	        rangelist.put(JSONConstants.END_COLUMN,0);
	        
	        return rangelist;
	    }
		
		private	List<List<String>> 	getPictureData (HttpServletRequest request) {

    	 	String		pictureData		=	request.getParameter("cellContent");
    	 	JSONObjectWrapper 	jObj 			= 	new JSONObjectWrapper();
            ContentParser parser 		= 	new ContentParser('\t');
            
            jObj.put("isHtmlData", true);
            jObj.put("isFormatApply", false);

            try {
				ActionObject.getDataUsingContentParser(parser, jObj, pictureData,0,0);
			} catch (Exception e) {
				LOGGER.log(Level.WARNING, "[DataFromPic]Error while parsing data", e);
			}
    	    JSONArrayWrapper 			pictureDataAry	=	JSONArrayWrapper.fromString(jObj.getString(JSONConstants.VALUE));

			return ActionJsonUtil.constructListOfStringList(pictureDataAry);
    }
	   
}
