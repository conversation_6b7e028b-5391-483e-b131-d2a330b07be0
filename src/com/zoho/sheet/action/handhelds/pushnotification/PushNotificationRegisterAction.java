/* $Id$ */

package com.zoho.sheet.action.handhelds.pushnotification;

import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.zoho.pns.api.IAMOAuth;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.pushnotification.ACServerUtil;
import com.zoho.sheet.pushnotification.deviceWrapper.ACSDeviceWrapper;
import com.zoho.sheet.pushnotification.deviceWrapper.impl.ACSAndroidDevice;
import com.zoho.sheet.pushnotification.deviceWrapper.impl.ACSIOSDevice;

/***
 *
 * <AUTHOR>
 *
 * PURPOSE :  Used to register the user device (ios/android) to the AcsApi upon login of the user.
 *
 */
public class PushNotificationRegisterAction extends StrutsRequestHandler {

    public static Logger logger = Logger.getLogger(PushNotificationRegisterAction.class.getName());

    private static String serviceName = "ZohoSheet";    //NO I18N
    private static String scope = "ClientAPI";    //NO I18N

    @Override
    public String execute() throws Exception {

        ACSDeviceWrapper deviceWrapper;
        String zuid = DocumentUtils.getZUID().toString();

        JSONObjectWrapper respObj = new JSONObjectWrapper();
        JSONObjectWrapper errorObj = new JSONObjectWrapper();

        String deviceType = request.getParameter("deviceType");//NO I18N

        IAMOAuth authInfo = getAuthInfo(request, zuid, deviceType);

        if (authInfo != null) {

            switch (deviceType) {
                case "android":
                    deviceWrapper = new ACSAndroidDevice(authInfo);
                    break;

                case "ios":
                    deviceWrapper = new ACSIOSDevice(authInfo);
                    break;

                default:
                    deviceWrapper = null;
            }

            try {
                deviceWrapper.setDeviceInfo(request);
            } catch (Exception e) {
                logger.log(Level.WARNING, "[PUSHNOTIFICATION][ACS] Error while setting deviceInfo", e);

                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

                errorObj.put("error_code", "INTERNAL_SERVER_ERROR"); // NO OUTPUTENCODING

                response.getWriter().println(errorObj);

            }

            int action = Integer.parseInt(request.getParameter("action"));//NO I18N

            switch (action) {

                case ActionConstants.PUSH_NOTIFICATION_REGISTER:
                    String respStatus;
                    try {
                        respStatus = ACServerUtil.registerDeviceforNotification(zuid, deviceWrapper);
                        respObj.put("respStatus", respStatus);
                        response.getWriter().println(respObj);
                    } catch (Exception e) {

                        logger.log(Level.WARNING, "[PUSHNOTIFICATION][ACS] Error while registering device with ACSAPI", e);
                        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                        errorObj.put("error_code", "INTERNAL_SERVER_ERROR");        // NO OUTPUTENCODING
                        response.getWriter().println(errorObj);
                    }
                    break;

                case ActionConstants.PUSH_NOTIFICATION_UNREGISTER:
                    try {

                        respStatus = ACServerUtil.unregisterDeviceforNotification(zuid, deviceWrapper);
                        respObj.put("respStatus", respStatus);
                        response.getWriter().println(respObj);

                    } catch (Exception e) {

                        logger.log(Level.WARNING, "[PUSHNOTIFICATION][ACS] Error while unregistering with ACSAPI", e);
                        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                        errorObj.put("error_code", "INTERNAL_SERVER_ERROR");        // NO OUTPUTENCODING
                        response.getWriter().println(errorObj);
                    }
                    break;
            }
        } else {
            logger.log(Level.WARNING, "[PUSHNOTIFICATION][ACS] Error while getting client info . Auth key is null or empty. Check!!!!");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            errorObj.put("error_code", "INTERNAL_SERVER_ERROR");        // NO OUTPUTENCODING
            response.getWriter().println(errorObj);

        }


        return null;
    }


    private IAMOAuth getAuthInfo(HttpServletRequest request, String zuid, String deviceType) {
        String authkey = request.getHeader("Authorization");//NO I18N
        String useragent = request.getHeader("User-Agent");//NO I18N
        String ip = request.getParameter("ip");//NO I18N

        IAMOAuth auth = null;

        if (authkey != null && !authkey.isEmpty() && (deviceType.equals("android") || deviceType.equals("ios"))) {
            if (useragent == null || useragent.isEmpty()) {

                switch (deviceType) {

                    case "android":
                        useragent = "ZSheet/AndroidApp"; //NO I18N
                        break;

                    case "ios":
                        useragent = "ZSheet/iOSApp"; // NO I18N
                        break;

                    case "windows":
                        useragent = "ZSheet/WindowsApp"; // NO I18N
                        break;

                    default:
                        break;
                }
            }

            auth = new IAMOAuth(zuid, authkey);
            auth.setServiceName(serviceName);
            auth.setUserAgent(useragent);
            auth.setIP(ip);
            auth.setScope(scope);


        }
        return auth;

    }

}
