/* $Id$ */
package com.zoho.sheet.action.handhelds.pushnotification;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.response.ActionResponseObject;
import com.adventnet.zoho.websheet.model.response.ResponseObject;
import com.adventnet.zoho.websheet.model.response.VersionBasedResponseAnalyzer;
import com.adventnet.zoho.websheet.model.response.UserInfo;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.model.util.response.MessageBean;
import com.zoho.sheet.authorization.AppUtil;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.HttpServletResponseWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.logging.Logger;

/***
 *
 * <AUTHOR>
 *
 * PURPOSE :  Used to handle external comment action in case of direct reply and like action in push notification.
 *
 */
public class DiscussionAPI extends StrutsRequestHandler {
    public static Logger logger = Logger.getLogger(DiscussionAPI.class.getName());

    @Override
    public String execute() throws Exception {

        JSONObjectWrapper actionJson = getActionJson(request);

        String resourceId = actionJson.getString(JSONConstants.RID);

        JSONObjectWrapper disResponseObject = ActionUtil.discussionNotifyAction(Integer.parseInt(request.getParameter("action")), actionJson);
        WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();
        Workbook workbook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());
        
        //Creating Response for New Clients
        ResponseObject responseObject = new ActionResponseObject(container, workbook, actionJson, disResponseObject);

        //Sending Message to clients
        UserInfo currentUserInfo = AppUtil.getCurrentUserInfo();

        List<UserInfo> userInfoList = MessagePropagator.getUserInfoList(container);
        for(UserInfo userInfo : userInfoList)
        {
            JSONObjectWrapper resourceData = (JSONObjectWrapper) VersionBasedResponseAnalyzer.getResponse(responseObject, null, userInfo);
            MessageBean messageBean = new MessageBean();
            messageBean.setuserInfo(userInfo);
            messageBean.setWmsRawSessionId(userInfo.getRsid());
            messageBean.setHandHeldsResp(resourceData);
            MessagePropagator.sendWMSResponse(container, actionJson, messageBean);
            if(userInfo.getUtid().equals(currentUserInfo.getUtid()))
            {
                HttpServletResponseWrapper.sendResponse(response, resourceData, HttpServletResponseWrapper.MimeType.JSON);
            }
        }

        return null;
    }


    private JSONObjectWrapper getActionJson(HttpServletRequest request) {

        String action = request.getParameter("action");
        String content = request.getParameter("content");
        String cid = request.getParameter("cid");
        String disId = request.getParameter("disId");
        String resourceId = request.getParameter("rid");
        String zuid = DocumentUtils.getZUID().toString();
        String username = DocumentUtils.getZFullName(zuid);
        String lang = IAMUtil.getCurrentUser().getLanguage();

        JSONObjectWrapper action_Json = new JSONObjectWrapper();
        action_Json.put(JSONConstants.ACTION, action);
        action_Json.put(JSONConstants.MESSAGE, content);
        action_Json.put(JSONConstants.RID, resourceId);
        //No JSONConstants used in actionObject
        action_Json.put("cid", cid);
        action_Json.put("disId", disId);

        action_Json.put("lang", lang);
        action_Json.put(JSONConstants.ZUID, zuid);
        action_Json.put(JSONConstants.USER_NAME, username);

        return action_Json;
    }

}
