/* $Id$ */

package com.zoho.sheet.action.handhelds;

import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.zfsng.client.ZohoFS;


/**
 * <AUTHOR>
 */

/**
 * 	This is used iOS app as they are caching the spreadsheet in client, so after opening in client, just we are updating opening time in server
 *
 */
public class UpdateSpreadsheetOpenTime extends StrutsRequestHandler {


    public static Logger logger = Logger.getLogger(UpdateSpreadsheetOpenTime.class.getName());

    @Override
    public String execute() throws Exception {

        JSONObjectWrapper respObj = new JSONObjectWrapper();
        Boolean isOpenTimeUpdated = false;

        String uri = request.getRequestURI();

        String rid = request.getParameter("rid");
        String userAgent = request.getHeader("user-agent");
        String refererUrl = request.getHeader("referer");
        Boolean isPublicAccess = false;

        if (uri.contains("published")) {
            isPublicAccess = true;
        } else {
            isPublicAccess = false;
        }

        try {

            if (request.getUserPrincipal() != null) {
                String username = request.getUserPrincipal().getName();
                String zuid = String.valueOf(DocumentUtils.getZUID(username));
                // ZohoFS.updateLastOpenedTime(zuid, rid);
                ZohoFS.auditResourceOpenActivity(rid, userAgent, refererUrl, request.getRemoteAddr(), isPublicAccess, zuid);
                isOpenTimeUpdated = true;
            }

        } catch (Exception e) {

            isOpenTimeUpdated = false;
            logger.log(Level.WARNING, "[UpdateSpreadsheetOpenTime] Exception while updating spreadsheet open time >> ", e);
        }

        respObj.put("isOpenTimeUpdated", isOpenTimeUpdated);

        response.getWriter().write(respObj.toString());            //NO OUTPUTENCODING

        return null;
    }

}
