/* $Id$ */
package com.zoho.sheet.action.handhelds;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.ZSStore;
import com.adventnet.zoho.websheet.model.util.ZSStore.FileExtn;
import com.adventnet.zoho.websheet.model.util.ZSStore.FileName;


/**
 * <AUTHOR>
 */

public class FetchExecutedActionInfo extends StrutsRequestHandler {

    public static Logger logger = Logger.getLogger(FetchExecutedActionInfo.class.getName());

    @Override
    public String execute() throws Exception{
        try {
            String rid = request.getParameter("rid");    //No I18N
            String docOwnerZUID = DocumentUtils.getDocOwnerZuidFromRid(rid);
            String docOwner = DocumentUtils.getDocOwnerFromRid(rid);
            String documentId = DocumentUtils.getDocumentId(rid, docOwner);
            Long actionsResId = ZSStore.getFragmentId(docOwner, Long.valueOf(documentId), Long.valueOf(docOwnerZUID), FileName.ACTIONS, true, null);

            List actionsList = loadActionsList(docOwnerZUID, docOwner, documentId, actionsResId, FileName.ACTIONS);
            JSONObjectWrapper jObj = new JSONObjectWrapper();
            if (!actionsList.isEmpty()) {
                JSONObjectWrapper action = new JSONObjectWrapper((String) actionsList.get(actionsList.size() - 1));
                jObj.put("LastSavedId", action.getInt("aid"));    //No I18N
            } else {
                jObj.put("LastSavedId", "{}");    //No I18N
            }

            if (RedisHelper.hlen(RedisHelper.ACTIONS_LIST + rid) > 0) {
                Set<String> set = RedisHelper.hkeys(RedisHelper.ACTIONS_LIST + rid);
                jObj.put("LastExecutedActionID", Integer.parseInt(Collections.max(set, (String s1, String s2) ->    //No I18N
                {
                    Long l1 = Long.parseLong(s1);
                    Long l2 = Long.parseLong(s2);
                    return (l1 < l2 ? -1 : (l1 == l2 ? 0 : 1));
                })));
            } else {
                jObj.put("LastExecutedActionID", "{}");    //No I18N
            }
            JSONObjectWrapper responseObj = new JSONObjectWrapper();
            responseObj.put("ActionInfo", jObj);    //No I18N
            response.getWriter().print(responseObj.toString());    //NO OUTPUTENCODING
            return null;
        } catch (Exception e) {
            logger.log(Level.WARNING, "Error on fetching executed action info", e);
            return null;
        }

    }

    private static Object getSpace(String docOwnerZUID, String docOwner) {
        long ownerZUID = Long.valueOf(docOwnerZUID);
        Object spaceName;
        if (ownerZUID != -1L) {
            spaceName = ownerZUID;
        } else {
            spaceName = docOwner;
        }
        return spaceName;
    }

    private static List loadActionsList(String docOwnerZUID, String docOwner, String docId, Long resId, FileName name) throws Exception {
        List list = new ArrayList();
        if (resId > 0 && ZSStore.isFileExist(getSpace(docOwnerZUID, docOwner), docOwner, Long.valueOf(docId), resId, name, FileExtn.JSON)) {
            try {
                InputStream stream = ZSStore.getReadStream(getSpace(docOwnerZUID, docOwner), docOwner, Long.valueOf(docId), resId, name, FileExtn.JSON, null);
                BufferedReader reader = new BufferedReader(new InputStreamReader(stream));
                String str = null;
                while ((str = reader.readLine()) != null) {
                    list.add(str);
                }
                stream.close();
                reader.close();
            } catch (Exception e) {
                //e.printStackTrace();
//                    LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: " + container.getResourceKey() + " Error while loading actions : ", e);
            }
        }
        return list;
    }

}
