/* $Id$ */
package com.zoho.sheet.action.handhelds.gadgetinterface;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.adventnet.zoho.websheet.model.ErrorCode;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.connection.ZSConnection;
import com.zoho.sheet.connection.ZSConnectionFactory;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.RemoteUtils;
import com.zoho.sheet.util.StrutsRequestHandler;
import org.apache.commons.io.FilenameUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;

import static java.util.logging.Level.INFO;

/***
 *
 * <AUTHOR>
 *
 * PURPOSE : Used to extract the functionalities of the Dropbox version2 APIs
 *
 */

public class CloudDriveInterface extends StrutsRequestHandler {


    public static final Logger LOGGER = Logger.getLogger(CloudDriveInterface.class.getName());

    private String zservice = EnginePropertyUtil.getSheetPropertyValue("serviceName"); //NO I18N

    @Override
    public String execute() throws Exception {

        String cloudDrive = request.getParameter("cloudDrive");

        String operation = request.getParameter("operation");

        HttpURLConnection connection;

        String requestMethod = getRequestMethod(operation, cloudDrive);

        if (operation.equals(GadgetConstants.UPLOAD_SPREADSHEET)) {
            exportToCloudDrive(request, response, cloudDrive);
            return null;
        }

        URL url = null;

        if (requestMethod.equals("GET") || requestMethod.equals("DELETE")) {
            String folderId = request.getParameter("folderId") != null ? request.getParameter("folderId") : "0";
            if (folderId.isEmpty()) {
                folderId = "0";
            }
            url = new URL(getCloudURL(cloudDrive, operation, folderId) + getParameters(cloudDrive, operation, request));
        } else if (requestMethod.equals("POST")) {
            url = new URL(getCloudURL(cloudDrive, operation, "0"));
        }

        connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod(requestMethod);

        // set authorization header
        String auth = request.getHeader("Authorization");
        connection.setRequestProperty("Authorization", auth); //NO I18N
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded"); //NO I18N

        // set parameters
        if (requestMethod.equals("POST")) {

            connection.setDoOutput(true);
            DataOutputStream dataWriter = new DataOutputStream(connection.getOutputStream());
            try {
                dataWriter.writeBytes(getParameters(cloudDrive, operation, request));

            } finally {
                dataWriter.flush();
                dataWriter.close();
            }
        }
        // get response
        InputStream inputStream;

        BufferedReader reader = null;
        StringBuilder responseBuilder = new StringBuilder();
        String doc_id = null;

        if (operation.equals(GadgetConstants.DOWNLOAD_SPREADSHEET)) {
            String downloadurl = url.toString();
            doc_id = uploadFileToRemote(downloadurl, request, cloudDrive);
        }

        // success case
        try {

            inputStream = connection.getInputStream();

            reader = new BufferedReader(new InputStreamReader(inputStream));

            String line;

            while ((line = reader.readLine()) != null) {
                responseBuilder.append(line);
            }

        } catch (IOException e) {
            // error case
            try {
                inputStream = connection.getErrorStream();

                reader = new BufferedReader(new InputStreamReader(inputStream));

                String line;

                while ((line = reader.readLine()) != null) {
                    responseBuilder.append(line);
                }
            } catch (Exception ee) {
                // LOGGER.log(Level.WARNING, "[GADGET MOBILE INTERFACE]Error while getting errorstream ", ee);
            }
        } finally {
            if (reader != null) {
                reader.close();
            }
        }

        responseParser(cloudDrive, responseBuilder.toString(), operation, response, doc_id);

        return null;
    }

    /*
       Used to generate respective URLs for the cloudServices
        */
    private String getCloudURL(String cloudDrive, String operation, String folderId) {

        String url = EnginePropertyUtil.getSheetPropertyValue("ZohoGadgetsURL");//NO I18N
        if (!url.contains("https://")) { // NO I18N
            url = "https://" + url;// NO I18N
        }
        /*
        Used to construct respective URIs for the cloudServices
         */
        switch (cloudDrive) {
            case JSONConstants.GADGET_DROPBOX_KEY:
                url += EnginePropertyUtil.getSheetPropertyValue("DropBoxBaseUrl"); //NO I18N
                switch (operation) {
                    case GadgetConstants.GETTING_CLOUDDRIVE_ACCOUNTS:
                        url += "getAllAccountsInfo"; //NO I18N
                        break;
                    case GadgetConstants.LISTING_FILES_FOLDERS:
                        url += "getFilesFolders"; // NO I18N
                        break;
                    case GadgetConstants.DOWNLOAD_SPREADSHEET:
                        url += "download?"; // NO I18N
                        break;
                    case GadgetConstants.UPLOAD_SPREADSHEET:
                        url += "upload"; //No I18N
                        break;
                    case GadgetConstants.REVOKE_ACCOUNT_PERMISSION:
                        url += GadgetConstants.REVOKE_DROPBOX_ACCOUNT_PERMISSION.toString();
                        break;
                }
                break;
            case JSONConstants.GADGET_BOX_KEY:
                url += EnginePropertyUtil.getSheetPropertyValue("BoxBaseUrl"); //NO I18N
                switch (operation) {
                    case GadgetConstants.GETTING_CLOUDDRIVE_ACCOUNTS:
                        url += "auth/details?";//No I18N
                        break;
                    case GadgetConstants.LISTING_FILES_FOLDERS:
                        url += "folders/" + folderId + "/items?";//No I18N
                        break;
                    case GadgetConstants.DOWNLOAD_SPREADSHEET:
                        url += "download?";//No I18N
                        break;
                    case GadgetConstants.UPLOAD_SPREADSHEET:
                        url += "folders/" + folderId + "/file";//No I18N
                        break;
                    case GadgetConstants.REVOKE_ACCOUNT_PERMISSION:
                        url += GadgetConstants.REVOKE_ACCOUNT_PERMISSION.toString() + "?";//No I18N
                        break;
                }
                break;
        }

        return url;
    }

    /*
    Used to assign HTTPMethods for respective APIs
     */
    private String getRequestMethod(String operation, String cloudDrive) {

        if (cloudDrive.equals(JSONConstants.GADGET_DROPBOX_KEY)) {
            switch (operation) {
                case GadgetConstants.GETTING_CLOUDDRIVE_ACCOUNTS:
                case GadgetConstants.LISTING_FILES_FOLDERS:
                case GadgetConstants.REVOKE_ACCOUNT_PERMISSION:
                    return "POST"; // NO I18N

                case GadgetConstants.DOWNLOAD_SPREADSHEET:
                    return "GET"; // NO I18N


            }
        } else if (cloudDrive.equals(JSONConstants.GADGET_BOX_KEY)) {
            switch (operation) {
                case GadgetConstants.GETTING_CLOUDDRIVE_ACCOUNTS:
                case GadgetConstants.LISTING_FILES_FOLDERS:
                case GadgetConstants.DOWNLOAD_SPREADSHEET:
                    return "GET"; // NO I18N
                case GadgetConstants.REVOKE_ACCOUNT_PERMISSION:
                    return "DELETE"; // NO I18N
            }
        }
        return "POST";        //NO I18N
    }

    /*
    Used  to construct params for the url
     */
    private String getParameters(String cloudDrive, String operation, HttpServletRequest request) {

        String parameters = "";

        switch (cloudDrive) {
            case JSONConstants.GADGET_DROPBOX_KEY:
                switch (operation) {
                    case GadgetConstants.GETTING_CLOUDDRIVE_ACCOUNTS:
                        parameters += getUrlParam("zservice", GadgetConstants.SERVICE_NAME);        //NO I18N
                        parameters += getUrlParam("response_type", GadgetConstants.RESPONSE_TYPE);    //NO I18N
                        break;
                    case GadgetConstants.LISTING_FILES_FOLDERS:
                        parameters += getUrlParam("zservice", GadgetConstants.SERVICE_NAME);        //NO I18N
                        parameters += getUrlParam("response_type", GadgetConstants.RESPONSE_TYPE);    //NO I18N
                        parameters += getUrlParam("accountId", request.getParameter("accountId"));    //NO I18N
                        parameters += getUrlParam("folderPath", request.getParameter("folderId"));    //NO I18N
                        //parameters += getUrlParam("limit", request.getParameter("limit"));	//NO I18N
                        break;
                    case GadgetConstants.DOWNLOAD_SPREADSHEET:
                        parameters += getUrlParam("zservice", GadgetConstants.SERVICE_NAME);        //NO I18N
                        parameters += getUrlParam("response_type", GadgetConstants.RESPONSE_TYPE);        //NO I18N
                        parameters += getUrlParam("accountId", request.getParameter("accountId"));        //NO I18N
                        parameters += getUrlParam("filePath", request.getParameter("fileId"));        //NO I18N
                        break;
                    case GadgetConstants.REVOKE_ACCOUNT_PERMISSION:
                        parameters += getUrlParam("zservice", GadgetConstants.SERVICE_NAME);        //NO I18N
                        parameters += getUrlParam("response_type", GadgetConstants.RESPONSE_TYPE);        //NO I18N
                        parameters += getUrlParam("accountId", request.getParameter("accountId"));        //NO I18N
                        break;
                }
                break;
            case JSONConstants.GADGET_BOX_KEY:
                switch (operation) {
                    case GadgetConstants.GETTING_CLOUDDRIVE_ACCOUNTS:
                        parameters += getUrlParam("zservice", GadgetConstants.SERVICE_NAME);        //NO I18N
                        parameters += getUrlParam("response_type", GadgetConstants.RESPONSE_TYPE);    //NO I18N
                        break;
                    case GadgetConstants.LISTING_FILES_FOLDERS:
                        parameters += getUrlParam("zservice", GadgetConstants.SERVICE_NAME);        //NO I18N
                        parameters += getUrlParam("response_type", GadgetConstants.RESPONSE_TYPE);    //NO I18N
                        parameters += getUrlParam("email", request.getParameter("accountId"));    //NO I18N
                        break;
                    case GadgetConstants.DOWNLOAD_SPREADSHEET:
                        parameters += getUrlParam("zservice", GadgetConstants.SERVICE_NAME);        //NO I18N
                        parameters += getUrlParam("response_type", GadgetConstants.RESPONSE_TYPE);        //NO I18N
                        parameters += getUrlParam("fileId", request.getParameter("fileId"));        //NO I18N
                        parameters += getUrlParam("fileName", request.getParameter("fileName"));        //NO I18N
                        parameters += getUrlParam("email", request.getParameter("accountId"));        //NO I18N
                        break;
                    case GadgetConstants.REVOKE_ACCOUNT_PERMISSION:
                        parameters += getUrlParam("zservice", GadgetConstants.SERVICE_NAME);        //NO I18N
                        parameters += getUrlParam("response_type", GadgetConstants.RESPONSE_TYPE);        //NO I18N
                        parameters += getUrlParam("email", request.getParameter("accountId"));        //NO I18N
                        break;
                }
                break;
        }
        return parameters.substring(0, parameters.length() - 1);
    }

    private String getUrlParam(String keyName, String keyValue) {
        return keyName + "=" + keyValue + "&";
    }

    /**
     * @param gadgetResponse response from gadget which needs to be modified because of so much redundant data
     * @param operation      to classify based on action
     * @param response       response object sent to write the parsed response
     * @param doc_id         Sent only in download action
     * @throws Exception IOException handled for response.getWriter() case.
     */
    private void responseParser(String cloudDrive, String gadgetResponse, String operation, HttpServletResponse response, String doc_id) throws Exception {

        JSONObjectWrapper respObj = new JSONObjectWrapper();

        JSONObjectWrapper respJSON = new JSONObjectWrapper();

        JSONObjectWrapper respmsg = new JSONObjectWrapper();

        JSONArrayWrapper respAry = new JSONArrayWrapper();

        if (gadgetResponse == null || gadgetResponse.isEmpty()) { // empty response

            String message = "Internal Server Error"; // NO I18N

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // empty response , should not happen
            respObj.put("error_code", "500"); // NO I18N
            respObj.put("message", message);
            response.getWriter().println(respObj);
            return;
        }

        switch (cloudDrive) {

            case JSONConstants.GADGET_DROPBOX_KEY:

                switch (operation) {

                    case GadgetConstants.GETTING_CLOUDDRIVE_ACCOUNTS:

                        respJSON = new JSONObjectWrapper(gadgetResponse);

                        respObj = checkForError(gadgetResponse, response);

                        if (respObj.has("message")) { // error case

                            response.getWriter().println(respObj);

                        } else { // response case

                            JSONArrayWrapper respDetailsAry = new JSONArrayWrapper();
                            String account_id = null;
                            String email = null;

                            if (respJSON.has("response")) {

                                respAry = respJSON.getJSONArray("response");

                                for (int i = 0; i < respAry.length(); i++) {

                                    respmsg = respAry.getJSONObject(i);

                                    if (respmsg.has("account_id")) {
                                        account_id = respmsg.getString("account_id");
                                    }

                                    if (respmsg.has("email")) {
                                        email = respmsg.getString("email");
                                    }

                                    JSONObjectWrapper respDetails = new JSONObjectWrapper();

                                    if (account_id != null && !account_id.isEmpty()) {
                                        respDetails.put("account_id", account_id);
                                    }
                                    if (email != null && !email.isEmpty()) {
                                        respDetails.put("email", email);
                                    }

                                    if (respDetails.length() > 0) {
                                        respDetailsAry.put(respDetails);
                                    }
                                }
                            }

                            response.getWriter().println(respDetailsAry);
                        }
                        break;

                    case GadgetConstants.LISTING_FILES_FOLDERS:

                        respJSON = new JSONObjectWrapper(gadgetResponse);

                        respObj = checkForError(gadgetResponse, response);

                        if (respObj.has("message")) { // error case

                            response.getWriter().println(respObj);

                        } else { // response case


                            //                    String cursor = respmsg.getString("cursor");
                            //                    String has_more = respmsg.getString("has_more");
                            //                    respObj.put("cursor", cursor);
                            //                    respObj.put("has_more", has_more);
                            //                    respObj.put("entries", entries);


                            JSONArrayWrapper entries = new JSONArrayWrapper();

                            if (respJSON.has("response")) {

                                respmsg = respJSON.getJSONObject("response");

                                if (respmsg.has("entries")) {
                                    String filename = null;
                                    String fileExtn = null;

                                    JSONObjectWrapper entryObj;

                                    respAry = respmsg.getJSONArray("entries");

                                    for (int i = 0; i < respAry.length(); i++) {

                                        JSONObjectWrapper modifiedObject = new JSONObjectWrapper();

                                        entryObj = respAry.getJSONObject(i);

                                        String tag = null, id = null;

                                        if (entryObj.has(".tag")) {
                                            tag = entryObj.getString(".tag");
                                        }
                                        if (entryObj.has("path_display")) {
                                            id = entryObj.getString("path_display");
                                        }
                                        if (entryObj.has("name")) {
                                            filename = entryObj.getString("name");
                                        }

                                        String server_modified_time = entryObj.has("server_modified") ? entryObj.getString("server_modified") : null;

                                        long time = -1;

                                        if (server_modified_time != null && !server_modified_time.isEmpty()) {
                                            time = convertTimeToMilliSeconds(server_modified_time);

                                        }

                                        if (tag != null && id != null && filename != null && !tag.isEmpty() && !id.isEmpty() && !filename.isEmpty()) {

                                            if (tag.equals("folder")) {
                                                modifiedObject.put("isFolder", true);
                                                modifiedObject.put("id", id);
                                                modifiedObject.put("name", filename);
                                                if (time != -1) {
                                                    modifiedObject.put("modifiedTime", time);
                                                }
                                                entries.put(modifiedObject);
                                            }

                                            if (tag.equals("file")) {

                                                fileExtn = FilenameUtils.getExtension(filename);

                                                if (fileExtn.equals("xlsx") || fileExtn.equals("xls") || fileExtn.equals("csv") || fileExtn.equals("tsv") || fileExtn.equals("ods")) {

                                                    modifiedObject.put("id", id);
                                                    modifiedObject.put("name", filename);
                                                    modifiedObject.put("isFolder", false);
                                                    if (time != -1) {
                                                        modifiedObject.put("modifiedTime", time);
                                                    }
                                                    entries.put(modifiedObject);
                                                }
                                            }
                                        }

                                    }
                                }
                            }

                            response.getWriter().println(entries);

                        }
                        break;

                    case GadgetConstants.DOWNLOAD_SPREADSHEET:
                        response.getWriter().println(doc_id);
                        break;
                    case GadgetConstants.REVOKE_ACCOUNT_PERMISSION:

                        respJSON = new JSONObjectWrapper(gadgetResponse);
                        respObj = checkForError(gadgetResponse, response);

                        if (respObj.has("message")) {

                            response.getWriter().println(respObj);

                        } else {

                            String status = null;
                            JSONObjectWrapper result = new JSONObjectWrapper();

                            if (respJSON != null && !respJSON.isEmpty() && respJSON.has("status")) {
                                status = respJSON.getString("status");
                            }
                            result.put("status", (status != null && !status.isEmpty()) ? status : "failure");

                            response.getWriter().println(result);
                        }
                        break;
                }
                break;

            case JSONConstants.GADGET_BOX_KEY:

                switch (operation) {

                    case GadgetConstants.GETTING_CLOUDDRIVE_ACCOUNTS:

                        respJSON = new JSONObjectWrapper(gadgetResponse);
                        respObj = checkForError(gadgetResponse, response);

                        if (respObj.has("message")) {

                            response.getWriter().println(respObj);

                        } else {

                            JSONArrayWrapper respDetailsAry = new JSONArrayWrapper();

                            if (respJSON.has("response")) {

                                JSONObjectWrapper resObject = respJSON.getJSONObject("response");

                                if (resObject.has("authentication")) {

                                    respAry = resObject.getJSONArray("authentication");

                                    String email = null;

                                    String account_id = null;

                                    for (int i = 0; i < respAry.length(); i++) {


                                        respmsg = respAry.getJSONObject(i);

                                        JSONObjectWrapper accountObj = new JSONObjectWrapper();


                                        if (respmsg.has("ID")) {
                                            account_id = respmsg.getString("ID");
                                        }

                                        if (respmsg.has("EMAIL")) {
                                            email = respmsg.getString("EMAIL");
                                        }


                                        if (account_id != null && !account_id.isEmpty()) {
                                            accountObj.put("account_id", account_id);
                                        }

                                        if (email != null && !email.isEmpty()) {

                                            accountObj.put("email", email);
                                        }

                                        respDetailsAry.put(accountObj);
                                    }
                                }
                            }
                            response.getWriter().println(respDetailsAry);
                        }
                        break;

                    case GadgetConstants.LISTING_FILES_FOLDERS:

                        respJSON = new JSONObjectWrapper(gadgetResponse);
                        respObj = checkForError(gadgetResponse, response);

                        if (respObj.has("message")) {

                            response.getWriter().println(respObj);

                        } else {

                            JSONArrayWrapper entries = new JSONArrayWrapper();

                            if (respJSON.has("response")) {

                                respmsg = respJSON.getJSONObject("response");

                                if (respmsg.has("entries")) {

                                    respAry = respmsg.getJSONArray("entries");

                                    String filename = null;
                                    String fileExtn = null;
                                    JSONObjectWrapper entryObj;

                                    for (int i = 0; i < respAry.length(); i++) {

                                        JSONObjectWrapper modifiedObj = new JSONObjectWrapper();
                                        entryObj = respAry.getJSONObject(i);

                                        String tag = null, id = null;

                                        if (entryObj.has("type")) {
                                            tag = entryObj.getString("type");
                                        }
                                        if (entryObj.has("id")) {
                                            id = entryObj.getString("id");
                                        }
                                        if (entryObj.has("name")) {
                                            filename = entryObj.getString("name");
                                        }

                                        String server_modified_time = entryObj.has("server_modified") ? entryObj.getString("server_modified") : null;
                                        long time = -1;

                                        if (server_modified_time != null && !server_modified_time.isEmpty()) {
                                            time = convertTimeToMilliSeconds(server_modified_time);

                                        }
                                        if (tag != null && id != null && filename != null && !tag.isEmpty() && !id.isEmpty() && !filename.isEmpty()) {

                                            if (tag.equals("folder")) {
                                                modifiedObj.put("isFolder", true);
                                                modifiedObj.put("name", filename);
                                                modifiedObj.put("id", id);
                                                if (time != -1) {
                                                    modifiedObj.put("modifiedTime", time);
                                                }
                                                entries.put(modifiedObj);
                                            }

                                            if (tag.equals("file")) {

                                                fileExtn = FilenameUtils.getExtension(filename);

                                                if (fileExtn.equals("xlsx") || fileExtn.equals("xls") || fileExtn.equals("csv") || fileExtn.equals("tsv") || fileExtn.equals("ods")) {
                                                    modifiedObj.put("isFolder", false);
                                                    modifiedObj.put("name", filename);
                                                    modifiedObj.put("id", id);
                                                    if (time != -1) {
                                                        modifiedObj.put("modifiedTime", time);
                                                    }
                                                    entries.put(modifiedObj);
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            response.getWriter().println(entries);
                        }
                        break;

                    case GadgetConstants.DOWNLOAD_SPREADSHEET:
                        response.getWriter().println(doc_id);

                        break;
                    case GadgetConstants.REVOKE_ACCOUNT_PERMISSION:

                        String status = null;
                        respObj = checkForError(gadgetResponse, response);

                        if (respObj.has("message")) {
                            response.getWriter().println(respObj);
                        } else {

                            JSONObjectWrapper result = new JSONObjectWrapper();
                            respJSON = new JSONObjectWrapper(gadgetResponse);
                            if (respJSON != null && !respJSON.isEmpty() && respJSON.has("status")) {
                                status = respJSON.getString("status");
                            }
                            result.put("status", (status != null && !status.isEmpty()) ? status : "failure");

                            response.getWriter().println(result);

                        }
                        break;
                }
                break;
        }
    }

    private long convertTimeToMilliSeconds(String time) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'"); // NO I18N

        try {
            //formatting the dateString to convert it into a Date
            Date date = sdf.parse(time);

            Calendar calendar = Calendar.getInstance();
            //Setting the Calendar date and time to the given date and time
            calendar.setTime(date);
            return calendar.getTimeInMillis();
        } catch (Exception e) {
            // e.printStackTrace();
        }
        return -1;
    }

    private JSONObjectWrapper checkForError(String gadgetResponse, HttpServletResponse response) {

        JSONObjectWrapper responseObj = new JSONObjectWrapper();

        String message = "Internal Server Error"; // NO I18N

        try {

            JSONObjectWrapper gadgetUrlJSON = new JSONObjectWrapper(gadgetResponse);

            if (gadgetUrlJSON.has("status") && gadgetUrlJSON.getString("status").equals("success") && gadgetUrlJSON.getString("code").equals("200")) {
                response.setStatus(HttpServletResponse.SC_OK);

            } else if (gadgetUrlJSON.has("status") && gadgetUrlJSON.getString("status").equals("error")) {

                JSONObjectWrapper respmsg = (gadgetUrlJSON.has("response")) ? gadgetUrlJSON.getJSONObject("response") : new JSONObjectWrapper();
                message = (respmsg.isEmpty() || !respmsg.has("message")) ? "Internal Server Error" : respmsg.getString("message"); // No I18N

                if (gadgetUrlJSON.has("code") && gadgetUrlJSON.getString("code").equals("400")) {
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    responseObj.put("error_code", gadgetUrlJSON.getString("code"));
                    responseObj.put("message", message);
                } else if (gadgetUrlJSON.has("code") && gadgetUrlJSON.getString("code").equals("401")) {
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    responseObj.put("error_code", gadgetUrlJSON.getString("code"));
                    responseObj.put("message", message);
                } else if (gadgetUrlJSON.has("code") && gadgetUrlJSON.getString("code").equals("409")) {
                    response.setStatus(HttpServletResponse.SC_CONFLICT);
                    responseObj.put("error_code", gadgetUrlJSON.getString("code"));
                    responseObj.put("message", message);
                } else {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    responseObj.put("error_code", "500"); // NO I18N
                    responseObj.put("message", message);
                }
            }
        } catch (Exception e) {

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // empty response , should not happen
            responseObj.put("error_code", "500"); // NO I18N
            responseObj.put("message", message);
        }

        return responseObj;
    }

    /***
     *
     * @param gadgetsDownloadUrl
     *                      gadgetsDownloadUrl - sent to get the bytes of file data from the gadgets server to sheetserver by import api through xurl.
     *                                           It is framed in this
     * @param request
     *               request is sent to get the authentication and set in the header.
     *               Authorization header should be in the format of oauth_token type in order to support mobile connection.
     *
     *               fileName is also derived from the fileId param in the request.
     * @param cloudDrive
     *              used to get the filename (to download the file) because of different parameters in various  cloudServices.
     * @return
     *              JSONObjectWrapper - {
     *                  "doc": doc_id of the downloaded remote document
     *              }
     */
    private String uploadFileToRemote(String gadgetsDownloadUrl, HttpServletRequest request, String cloudDrive) {

        JSONObjectWrapper errObj = new JSONObjectWrapper();

        String url = ClientUtils.getServerURL(request, true, "authremotedoc.do", false, true);//NO I18N

        try {

            HttpClient client = HttpClientBuilder.create().build();
            String auth = request.getHeader("Authorization");
            HttpPost gadgetRequest = new HttpPost(url);

            gadgetRequest.setHeader("Authorization", auth);//NO I18N

            String filename = null;
            if (cloudDrive.equals(JSONConstants.GADGET_DROPBOX_KEY)) {
                filename = request.getParameter("fileId").substring(1);
            } else if (cloudDrive.equals(JSONConstants.GADGET_BOX_KEY)) {
                filename = request.getParameter("fileName");
            }

            try {
                filename = URLDecoder.decode(filename, "UTF-8"); // NO I18N
            } catch (Exception e) {
                // LOGGER.log(Level.WARNING, "[CLOUD INTERFACE]Error while decoding file name", e);

            }

            List<BasicNameValuePair> urlParameters = new ArrayList<>();
            urlParameters.add(new BasicNameValuePair("devicetype", "handheld"));
            urlParameters.add(new BasicNameValuePair("authHeaderType", "oauth"));
            urlParameters.add(new BasicNameValuePair("xurl", gadgetsDownloadUrl));
            urlParameters.add(new BasicNameValuePair("filename", filename));

            gadgetRequest.setEntity(new UrlEncodedFormEntity(urlParameters));

            HttpResponse gadgetResponse = client.execute(gadgetRequest);

            BufferedReader reader = new BufferedReader(new InputStreamReader(gadgetResponse.getEntity().getContent()));
            String responseStr;
            StringBuilder gadgetResponseData = new StringBuilder();

            while ((responseStr = reader.readLine()) != null) {

                gadgetResponseData.append(responseStr);
            }
            return gadgetResponseData.toString();

        } catch (Exception e) {

            // LOGGER.log(Level.WARNING, "[CLOUD INTERFACE]Error while completing authentication", e);

            errObj.put("error_code", ErrorCode.ERROR_INTERNAL_SERVER);

            return errObj.toString();
        }

    }

    /***
     *
     * @param request
     *          rid - resourcekey of the authenticated remote document
     *          doc - doc_id of the authenticated remote document
     *          currentSheet - name of the currentSheet e.g., Sheet1
     *          ftype - file format type (extension) of the file to be downloaded
     *
     * @return
     *          returns the EXPORT_URL which is used to get the contentBytes of the authenticated remotefile.
     */
    private URL constructExportUrl(HttpServletRequest request) {

        try {

            String rid = request.getParameter("rid");
            String doc = request.getParameter("doc");
            String currentSheet = request.getParameter("currentSheet");
            String fileType = request.getParameter("ftype");

            String documentName = request.getParameter("fileName");

            String host = ClientUtils.getServerURL(request, false, null, false, false);//NO I18N

            if (fileType == null || fileType.isEmpty()) {
                int index = documentName.lastIndexOf(".");
                if (index != -1) {
                    fileType = documentName.substring(index + 1, documentName.length());
                } else {
                    fileType = "xlsx"; //NO I18N
                }
            }

            URIBuilder builder = new URIBuilder();

            builder.setScheme("https");//NO I18N
            builder.setHost(host);
            builder.setPath("/sheet/" + UserProfile.ACCESS_TYPE_AUTH_REMOTE + "/" + doc);                            //NO I18N
            builder.addParameter("doc", doc);//NO I18N
            builder.addParameter("rid", rid);//NO I18N
            builder.addParameter("currentSheet", currentSheet);//NO I18N
            builder.addParameter("currSheetName", currentSheet);//NO I18N
            builder.addParameter("ftype", fileType);//NO I18N
            builder.addParameter("proxyURL", "mexport");//NO I18N
            return builder.build().toURL();

        } catch (Exception ex) {
            // LOGGER.log(Level.WARNING, "[GADGET INTERFACE]Error while constructing export URL", ex);
        }
        return null;
    }

    /***
     *
     * @param request
     *              request is sent to get the authentication and set in the header
     *              Authorization header should be in the format of oauth_token type in order to support mobile connection.
     * @return
     *              It returns the file bytes in byte[] format in order to upload  in cloud Service
     */
    private byte[] getDocBytes(HttpServletRequest request) {

        HttpURLConnection connection;
        JSONObjectWrapper respObj = new JSONObjectWrapper();

        try {

            URL url = constructExportUrl(request);

            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");//NO I18N

            // set authorization header
            String auth = request.getHeader("Authorization");
            connection.setRequestProperty("Authorization", auth); //NO I18N
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded"); //NO I18N

            InputStream inputStream = connection.getInputStream();
            byte[] buf = new byte[1024];
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                bos.write(buf, 0, len);
            }
            return bos.toByteArray();

        } catch (Exception ex) {
            // LOGGER.log(Level.WARNING, "[GADGET INTERFACE]Error while downloading remote file", ex);

            respObj.put("status", 0);
            respObj.put("msgkey", ErrorCode.ERROR_INTERNAL_SERVER);
        }
        return null;
    }

    /***
     *
     * @param request
     *              cloudDrive - "dropbox | box " - name of cloudService to wich the file has to be uploded
     *              fileName - fileName by which the file has to be uploded in cloudService.
     *              folderPath - path to the folder to which the file has to be uploded in cloud Service
     *              accountId - accountId of the cloud Service
     * @param response
     *              JSONObjectWrapper - {
     *                  "statuscode"    =   200,
     *                  "message"       =   Sheet_Uploded_to_Cloud_Drive_Successfully ,
     *                  "status"        =   success/failure
     *              }
     * @param cloudDrive
     *              used to set the parameters for different cloudDrives
     * @throws IOException
     *              IOException handled for response.getWriter() case.
     */
    private void exportToCloudDrive(HttpServletRequest request, HttpServletResponse response, String cloudDrive) throws IOException {

        String documentName = request.getParameter("fileName");        // NO I18N
        String folderId = request.getParameter("folderId");        // NO I18N
        String accountId = request.getParameter("accountId");    //  No I18N
        String fileId = request.getParameter("fileId");       //No I18N
        String uploadMode = request.getParameter("uploadMode");       //No I18N
//            String boxEmail         = request.getParameter("boxEmail");     //  No I18N
//            String folderPath       = request.getParameter("folderId");   //  No I18N

        byte[] contentBytes = getDocBytes(request);
        String gadgetUrl = getCloudURL(cloudDrive, GadgetConstants.UPLOAD_SPREADSHEET, folderId);

        HashMap map;
        HashMap resMap = new HashMap();
        String message = null;
        String[] names = null;
        String[] values = null;

        try {

            if (cloudDrive.equals(JSONConstants.GADGET_DROPBOX_KEY)) {
                names = new String[]{"zservice", "response_type", "accountId", "fileName", "folderPath", "uploadMode"};//No I18N
                values = new String[]{GadgetConstants.SERVICE_NAME, GadgetConstants.RESPONSE_TYPE, accountId, documentName, folderId, uploadMode};

            } else if (cloudDrive.equals(JSONConstants.GADGET_BOX_KEY)) {
                names = new String[]{"zservice", "response_type", "email", "fileName", "fileId", "uploadMode"};//No I18N
                values = new String[]{GadgetConstants.SERVICE_NAME, GadgetConstants.RESPONSE_TYPE, accountId, documentName, fileId, uploadMode};

            }

            String tempParams = null;
            for (int i = 0; i < names.length; i++) {
                if (values[i] != null) {
                    String tempParam = names[i] + "=" + values[i];//NO I18N

                    tempParams = tempParams == null ? tempParam : tempParams + "&" + tempParam;//NO I18N
                }
            }
            gadgetUrl += "?" + tempParams;

            ZSConnection zsconn = (new ZSConnectionFactory(gadgetUrl, "POST")).getConnection(true);//No I18N

            String auth = request.getHeader("Authorization");

            zsconn.setFileBytes(contentBytes, documentName);
            zsconn.setContentType("application/x-www-form-urlencoded");//NO I18N
            //zsconn.setParameter( names, values );
            zsconn.setOAuthHeader(auth);
            zsconn.process();
            map = zsconn.getResponseMap();
            int statusCode = (int) map.get("RESP_CODE");
            String repMessage = map.get("RESP_MESSAGE") != null ? (String) map.get("RESP_MESSAGE") : null;
            // LOGGER.info("[gadget] responseMap" + map);
            if (map.isEmpty() || statusCode == 111) {
                message = "Unable_to_Connect_to_cloudDrive";//No I18N
            } else {
                //status code setting
                // LOGGER.info("HTTP RESPONSE CODE : " + statusCode);
                resMap.put("statuscode", statusCode);
                // response message setting
                if (repMessage != null) {

                    try {
                        String resFormatStr = repMessage;
                        resFormatStr = RemoteUtils.getResponseFormattedString(resFormatStr);

                        if (resFormatStr != null && !"".equals(resFormatStr)) {
                            message = resFormatStr;
                        } else {
                            message = null;
                        }
                    } catch (Exception re) {
                        // LOGGER.log(Level.WARNING, " Problem while parsing the exception ", re);
                    }
                }

                if (message == null) {
                    if (statusCode == 200 || statusCode == 201) {
                        resMap.put("status", "success");
                        message = "Sheet_Uploded_to_Cloud_Drive_Successfully";//No I18N
                    } else if (statusCode == 409) {
                        resMap.put("status", "failure");
                        message = "File_Name_Collision_Occurs.";//No I18N
                    } else {
                        // LOGGER.info("HTTP POST EXCE : RESPONSE .FAILED statusCode : " + statusCode);
                        resMap.put("status", "failure");
                        message = "Unable_to_Upload_Spreadsheet";//No I18N
                    }
                }
            }
        } catch (Exception e) {
            // LOGGER.info("Exception occured while remote upload " + e);
            String exceptionMessage = e.toString();
            StackTraceElement[] elements = e.getStackTrace();
            for (int iterator = 1; iterator <= elements.length; iterator++) {
                String tempMessage = "[" + elements[iterator - 1].getClassName() + "]{" + elements[iterator - 1].getMethodName() + "}(" + elements[iterator - 1].getLineNumber() + ")\n";
                exceptionMessage = exceptionMessage + tempMessage;
            }
            resMap.put("status", "failure");
            message = "Unable_to_Upload_Spreadsheet.Server_error_occured";//No I18N
            // LOGGER.log(INFO, exceptionMessage);
        }
        resMap.put("message", message);
        response.getWriter().println(resMap);
    }
}
