//$Id$
package com.zoho.sheet.action.recast;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.paste.FillPaste;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.google.common.collect.ImmutableMap;

/**
 * 
 * <AUTHOR> N J ( ZT-0049 )
 * <AUTHOR>
 * <br>
 * <br>
 * An <b>ActionMouldFactory</b> class consist of list of mould actions that need to be re-casted at run time based on the 
 * operations.
 *<p>
 * Any new action that needs to be re-casted has to be added in {@link ActionMouldFactory#ACTION_MOULDS_FACTORY_MAP} with ActionConstants
 * as a key and NewActionMould as value, provided NewActionMould class is implemented for that action.
 *</p>
 * <p>
 * Refer Example : {@link CopyPasteActionMould}
 * </p>
 */
public final class ActionMouldFactory
{
	static final ImmutableMap<Integer, Function<JSONObjectWrapper, ActionMould>> ACTION_MOULDS_FACTORY_MAP;

	static
	{
		Map<Integer, Function<JSONObjectWrapper, ActionMould>> ActionMouldMap = new HashMap<>();


		ActionMouldMap.put(ActionConstants.FILLSERIES, feedActionParams->
		{
			FillPaste.FillSeriesType fillSeriesType = FillPaste.FillSeriesType.valueOf(feedActionParams.getString(JSONConstants.FILL_SERIES_TYPE));
			return new FillSeriesRecast(fillSeriesType);
		});

		ActionMouldMap.put(ActionConstants.COPY_PASTE, feedActionParams ->
		{
			ActionConstants.PasteSpecialEnum pasteSpecialType = ActionConstants.PasteSpecialEnum.valueOf(feedActionParams.getString(JSONConstants.PASTESPECIAL_TYPE));
			//				boolean isPasteTranspose = feedActionParams.has(JSONConstants.PASTESPECIAL_TRANSPOSE) ? Boolean.valueOf(feedActionParams.getString(JSONConstants.PASTESPECIAL_TRANSPOSE)) : Boolean.FALSE;
			return new CopyPasteActionMould(pasteSpecialType);
		});

		ActionMouldMap.put(ActionConstants.SYSTEMCLIP_PASTE, feedActionParams ->
		{
			String formatToAdopt = feedActionParams.getString(JSONConstants.FORMAT_TO_ADOPT);
			boolean isAdoptSourceFormat = "source_format".equals(formatToAdopt); //No I18N
			return new SystemClipPasteActionMould(isAdoptSourceFormat);
		});

		ActionMouldMap.put(ActionConstants.INSERT_ROW, feedActionParams ->
		{
			int style = feedActionParams.getInt("style");
			return new InsertStyleActionMould(style);
		});

		ActionMouldMap.put(ActionConstants.INSERT_COL, feedActionParams ->
		{
			int style = feedActionParams.getInt("style");
			return new InsertStyleActionMould(style);
		});

		ActionMouldMap.put(ActionConstants.INSERT_CELL_LEFT, feedActionParams ->
		{
			int style = feedActionParams.getInt("style");
			return new InsertStyleActionMould(style);
		});

		ActionMouldMap.put(ActionConstants.INSERT_CELL_TOP, feedActionParams ->
		{
			int style = feedActionParams.getInt("style");
			return new InsertStyleActionMould(style);
		});

		ActionMouldMap.put(ActionConstants.INSERT_CELL_LEFT, feedActionParams ->
		{
			int style = feedActionParams.getInt("style");
			return new InsertStyleActionMould(style);
		});


		ACTION_MOULDS_FACTORY_MAP = ImmutableMap.copyOf(ActionMouldMap);
	}

	public static ActionMould getActionMould(JSONObjectWrapper mouldActionJson)
	{
		int feedAction = mouldActionJson.getInt(JSONConstants.FEED_ACTION);
		JSONObjectWrapper feedActionParamsJson = mouldActionJson.getJSONObject(JSONConstants.FEED_ACTION_PARAMS);
		return ACTION_MOULDS_FACTORY_MAP.get(feedAction).apply(feedActionParamsJson);
	}
}