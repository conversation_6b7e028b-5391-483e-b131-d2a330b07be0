
/* $Id$ */
package com.zoho.sheet.action;

import java.text.*;
import java.util.Iterator;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQuery;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.SortColumn;
import com.adventnet.ds.query.Table;
import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.persistence.DataObject;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import com.zoho.sheet.util.StrutsRequestHandler;
import java.util.Locale;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.SheetPersistenceUtils;
import java.util.Date;
import java.util.TimeZone;
import java.util.logging.Logger;
import java.util.logging.Level;


//TODO: public view code is commented - need to do later

public class DocCommentLoad extends StrutsRequestHandler
{
	public static final Logger LOGGER = Logger.getLogger(DocCommentLoad.class.getName());
    @Override
	public String execute() throws Exception
    {
		try
		{
            Locale locale = LocaleUtil.getMyLocale();
			DateFormat dateFormat =null;
            Date date = null;
            if(locale.getLanguage().equals("zh"))
            {
                dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm a z", locale);
            }
            else if(locale.getLanguage().equals("ja"))
            {
                dateFormat = new SimpleDateFormat("yyyy '&#x5E74;' M '&#x6708;' dd '&#x65E5;' ahh:mm z", locale);
            }
            else
            {
                dateFormat = new SimpleDateFormat("MMM, dd yyyy hh:mm a z", locale);
            }
			
			dateFormat.setTimeZone(TimeZone.getTimeZone(ClientUtils.getCurrentUserTimezone(request)));
            WorkbookContainer container = CurrentRealm.getContainer();
            String docId = container.getDocId();
			String docOwner = container.getDocOwner();
//			String view = (String) request.getParameter("view");
			String docName =  "";
            Persistence pers = null;

            if(docOwner != null && !docOwner.equals(""))
            {
                pers = SheetPersistenceUtils.getPersistence(docOwner);
            }
            else
            {
                pers = SheetPersistenceUtils.getPersistence();
            }
//				docName =  DocumentUtils.getDocumentName(new Long(Long.parseLong(doc_id)),OWNER_NAME);
            docName = container.getDocName();
//				long docid = Long.parseLong(docId);
            Row row = new Row("DocumentComments");
            Criteria cri = new Criteria(new Column("DocumentComments", "DOCUMENT_ID"), Long.valueOf(docId), QueryConstants.EQUAL);
//				if (Constants.PUBLIC_VIEW.equals(view))
//                {
//					cri = cri.and (new Criteria(new Column("DocumentComments","APPROVED"), 1,QueryConstants.EQUAL));
//				}
            //logger.info("view..."+view);
            String authorName[];
            String comments[];
            String commentId[];
            String commentDate[];
            int i = 0;
            int cnt = 0;
            SelectQuery sql=null;
            sql = new SelectQueryImpl(new Table("DocumentComments"));
            sql.addSelectColumn(new Column(null,"*"));
            sql.setCriteria(cri);
            sql.addSortColumn(new SortColumn("DocumentComments", "COMMENT_DATE", true));//No internationalization
            DataObject dataObject = pers.get(sql);
            cnt = dataObject.size("DocumentComments");
            if (cnt > 0)
            {
                Iterator itr = dataObject.getRows("DocumentComments");
                authorName = new String[cnt];
                comments = new String[cnt];
                commentId = new String[cnt];
                commentDate = new String[cnt];
                row = new Row("DocumentComments");
                while(itr.hasNext())
                {
                    row = (Row) itr.next();
//                    long comm_id = (Long) row.get("COMMENT_ID");
                    commentId[i] = String.valueOf(row.get("COMMENT_ID"));
                    date = new Date((Long) row.get("COMMENT_DATE"));
                    String val = (String) row.get("COMMENT");
//                    String auth_name = (String) row.get("AUTHORED_BY");
                    authorName[i] = (String) row.get("AUTHORED_BY");
                    comments[i] = val;
//                    commentId[i] = String.valueOf(comm_id);
                    commentDate[i] = dateFormat.format(date);
                    i = i + 1;
                }
                request.setAttribute("COMMENT",comments);
                request.setAttribute("COMMENTID",commentId);
                request.setAttribute("COMMENTDATE",commentDate);
                request.setAttribute("AUTHOREDBY",authorName);
                request.setAttribute("CURRENT_DOC_OWNER",docOwner);
                request.setAttribute(Constants.DOCUMENT_NAME,docName);
                return "continue";    //No I18N
            }
            else
            {
                request.setAttribute("CURRENT_DOC_OWNER",docOwner);
                request.setAttribute(Constants.DOCUMENT_NAME,docName);
                return "zerocount";    //No I18N
            }
		}
		catch(Exception e)
        {
			LOGGER.log(Level.WARNING,null,e);
			return "failure";    //No I18N
		}
	}
}
