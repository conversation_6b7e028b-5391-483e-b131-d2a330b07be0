package com.zoho.sheet.datarefineries.datacleaning.interfaceImpl;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.RangeIterator;
import com.adventnet.zoho.websheet.model.ReadOnlyCell;
import com.zoho.sheet.datarefineries.datacleaning.interfaces.DCColumnValueIterator;
import com.zoho.sheet2app.dc.tableInterface.Value;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class DCColumnValueIteratorImpl implements DCColumnValueIterator {
    private final Map<Integer, String> contentMap;
    private final List<Integer> rowIndices;
    private int currRow = 0;

    public DCColumnValueIteratorImpl(Map<Integer, String> contentMap, List<Integer> rowIndices) {
        this.contentMap = contentMap;
        this.rowIndices = rowIndices;
    }

    @Override
    public boolean hasNext() {
        return currRow < this.rowIndices.size();
    }

    @Override
    public Value next() {
        return new Value(contentMap.getOrDefault(rowIndices.get(currRow),""), currRow++);
    }
}
