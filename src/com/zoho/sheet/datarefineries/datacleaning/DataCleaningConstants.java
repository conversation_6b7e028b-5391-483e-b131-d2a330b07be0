package com.zoho.sheet.datarefineries.datacleaning;

public class DataCleaningConstants {
    /*Operation Names*/
    public static final String TABLE_SUGGESTION_OPERATION_NAME = "TABLE_SUGGESTION";        //No I18N
    public static final String DEDUP_OPERATION_NAME = "DEDUP";                              //No I18N
    public static final String UNIQUE_OPERATION_NAME = "UNIQUE";                            //No I18N
    public static final String EXPLORE_OPERATION_NAME = "EXPLORE";                          //No I18N
    public static final String FIND_DATA_TYPE_OPERATION_NAME = "FIND_DATA_TYPE";            //No I18N
    public static final String MISSING_VALUE_OPERATION_NAME = "MISSING_VALUE";              //No I18N
    public static final String FIX_INCONSISTENCY_OPERATION_NAME = "FIX_INCONSISTENCY";      //No I18N
    public static final String STANDARDIZE_PH_NUM_OPERATION_NAME = "STANDARDIZE_PH_NUM";    //No I18N
    public static final String MULTIPLE_DATA_TYPES_OPERATION_NAME = "MULTIPLE_DATA_TYPES";  //No I18N
    public static final String SPLIT_COLUMN_OPERATION_NAME = "SPLIT_COLUMN";                //No I18N
    public static final String MERGE_COLUMNS_OPERATION_NAME = "MERGE_COLUMNS";              //No I18N
    public static final String HEADERS_OPERATION_NAME = "HEADERS";                          //No I18N

    /*sheet 2 app dc constants keys*/
    public static final String COLUMN_ID_KEY = "colId";                     //No I18N
    public static final String INCONSISTENCIES_KEY = "inconsistencies";     //No I18N
    public static final String MISSING_KEY = "missing";                     //No I18N
    public static final String TABLE_PROPERTIES_KEY = "tableProperties";    //No I18N
    public static final String SIMILAR_KEY = "similarMatch";                //No I18N
    public static final String ENTITY_MAP_KEY = "entityMap";                //No I18N
    public static final String DUPLICATES_KEY = "duplicates";               //No I18N
    public static final String TYPE_NAME_KEY = "typeName";                  //No I18N
    public static final String TYPE_DATA_KEY = "typeData";                  //No I18N
    public static final String COUNT_KEY = "count";                         //No I18N
    public static final String MAX_COUNT_KEY = "maxCount";                  //No I18N
    public static final String WORD_FREQUENCY_KEY = "wordFrequency";        //No I18N
    public static final String ROWS_KEY = "rows";                           //No I18N
    public static final String VALUE_KEY = "value";                         //No I18N
    public static final String CLUSTER_KEY = "cluster";                     //No I18N
    public static final String BY_DATA_KEY = "byData";                      //No I18N
    public static final String BY_COUNT_KEY = "byCount";                    //No I18N

    /* data cleaning data type names */
    public static final String MISSING_TAG = "MISSING";     //No I18N
    public static final String IMAGE_TAG = "IMAGE";         //No I18N

}
