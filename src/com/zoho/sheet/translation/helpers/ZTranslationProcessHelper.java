package com.zoho.sheet.translation.helpers;

import com.zoho.sheet.translation.bean.ZTranslateDataPacket;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static com.zoho.sheet.translation.ZTranslationExceptions.*;

public class ZTranslationProcessHelper {
    private final List<ZTranslateDataWrapper> dataWrapperList;
    private final List<ZTranslateDataPacket> dataPackets;


    public ZTranslationProcessHelper() {
        this.dataWrapperList = new ArrayList<>();
        this.dataPackets = new ArrayList<>();
    }

    public boolean isProcessDone() {
        for (ZTranslateDataWrapper wrapper : dataWrapperList) {
            FutureTask<?> futureTask = wrapper.getFutureTask();
            if (!futureTask.isDone()) {
                return false;
            }
        }

        return true;
    }

    public boolean isAllProcessSuccess() {
        for (ZTranslateDataWrapper zTranslateDataWrapper : dataWrapperList) {
             if (zTranslateDataWrapper.getStatus() != ZTranslateDataWrapper.Status.SUCCESS) {
                 return false;
             }
        }

        return true;
    }

    public List<String> getResult() throws TranslationNotReadyException {

        if (!isProcessDone()) {
            throw new TranslationNotReadyException();
        }
        List<String> results = new ArrayList<>();
        for (ZTranslateDataPacket dataPacket : this.dataPackets) {
            results.addAll(dataPacket.getTranslationData().getTranslatedSentence());
        }

        return results;
    }

    public String getDetectedLang() throws TranslationNotReadyException, TranslationErrorException {
        if (!isProcessDone()) {
            throw new TranslationNotReadyException();
        }
        if (this.dataPackets.isEmpty()) {
            throw new TranslationErrorException("Error at detect lang due to empty data packet");   //NO I18N
        }

        return this.dataPackets.get(0).getDetectedLang();
    }

    public List<String> awaitGetResult(long limitInSeconds) throws ExecutionException, InterruptedException, TimeoutException, TranslationNotReadyException {

        if (limitInSeconds == -1) {
            for (ZTranslateDataWrapper dataWrapper : dataWrapperList) {
                dataWrapper.getFutureTask().get();
            }
        }

        else {
            for (ZTranslateDataWrapper dataWrapper : dataWrapperList) {
                dataWrapper.getFutureTask().get(limitInSeconds, TimeUnit.SECONDS);
            }
        }

        return this.getResult();
    }

    public void addDataWrapperList(ZTranslateDataWrapper dataWrapper) {
        this.dataWrapperList.add(dataWrapper);
    }

    public void addAllDataWrapperList(Collection<ZTranslateDataWrapper> dataWrappers) {
        this.dataWrapperList.addAll(dataWrappers);
    }

    public void addDataPackets(ZTranslateDataPacket dataPacket) {
        this.dataPackets.add(dataPacket);
    }

    public void addAllDataPackets(Collection<ZTranslateDataPacket> dataPackets) {
        this.dataPackets.addAll(dataPackets);
    }

    public List<ZTranslateDataWrapper> getDataWrapperList() {
        return dataWrapperList;
    }

    public List<ZTranslateDataPacket> getDataPackets() {
        return dataPackets;
    }
}
