package com.zoho.sheet.translation;

import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.translation.bean.ZTranslationData;
import com.zoho.sheet.translation.bean.ZTranslationInfo;
import com.zoho.sheet.translation.helpers.ZTranslationProcessHelper;
import com.zoho.sheet.translation.interfaces.ZTranslationRequestHandler;
import com.zoho.sheet.translation.impl.ZTranslationRequestHandlerImpl;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

public class ZiaTranslationApiBot {
    private static final long AWAIT_LIMIT = Long.parseLong(EnginePropertyUtil.getEnginePropertyValue("SP_TRANSLATION_REQ_TIMEOUT")); //NO I18N

    public ZiaTranslationApiBot() {
    }

    public ApiResult translate(List<String> contents, String toLang, String srcLang) throws ExecutionException, InterruptedException, TimeoutException, ZTranslationExceptions.TranslationNotReadyException, ZTranslationExceptions.TranslationErrorException {
        ZTranslationRequestHandler requestHandler = ZTranslationRequestHandlerImpl.getInstance();
        ZTranslationInfo info = new ZTranslationInfo(toLang, srcLang);
        ZTranslationData translationData = new ZTranslationData(info);
        translationData.addContents(contents);

        ZTranslationProcessHelper processHelper = requestHandler.processTranslation(info, translationData);
        List<String> results = processHelper.awaitGetResult(AWAIT_LIMIT);
        String detectedLang = processHelper.getDetectedLang();

        return new ApiResult(results, detectedLang);
    }

    public static class ApiResult {

        private final List<String> results;
        private final String detectedLang;

        public ApiResult(List<String> results, String detectedLang) {
            this.results = results;
            this.detectedLang = detectedLang;
        }

        public List<String> getResults() {
            return results;
        }

        public String getDetectedLang() {
            return detectedLang;
        }
    }
}
