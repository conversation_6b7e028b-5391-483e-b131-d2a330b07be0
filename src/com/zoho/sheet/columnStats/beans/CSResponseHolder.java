package com.zoho.sheet.columnStats.beans;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> - 13900
 */

public class CSResponseHolder {
    private int statusCode = 200;
    private String tableRange;
    private final List<JSONObjectWrapper> columnResponse = new ArrayList<>();

    public List<JSONObjectWrapper> getColumnResponse() {
        return columnResponse;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public String getTableRange() {
        return tableRange;
    }

    public void setTableRange(String tableRange) {
        this.tableRange = tableRange;
    }
}
