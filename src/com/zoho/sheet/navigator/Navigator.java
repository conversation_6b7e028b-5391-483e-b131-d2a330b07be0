package com.zoho.sheet.navigator;

/**
 * Navigator container class. This class should contain data related to navigator panel
 * <AUTHOR>
 */
public class Navigator implements Cloneable{

    private boolean needToRefreshNavigator;

    private int navigatorMetaVersion = 0;

    public void navigatorMetaChanged() {
        if(navigatorMetaVersion == 65536) {
            navigatorMetaVersion = 0;
        } else {
            navigatorMetaVersion++;
        }
    }

    public int getNavigatorMetaVersion() {
        return navigatorMetaVersion;
    }

    public boolean isNeedToRefreshNavigator() {
        return needToRefreshNavigator;
    }

    public void setNeedToRefreshNavigator(boolean needToRefreshNavigator) {
        this.needToRefreshNavigator = needToRefreshNavigator;
    }

    @Override
    public Navigator clone() {
        try {
            Navigator clone = (Navigator) super.clone();

            clone.needToRefreshNavigator = needToRefreshNavigator;
            clone.navigatorMetaVersion = navigatorMetaVersion;

            return clone;
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException();
        }
    }
}
