package com.zoho.sheet.navigator.actionhandlers;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.navigator.NavObject;
import com.zoho.sheet.navigator.NavObjectType;
import com.zoho.sheet.navigator.dataholder.NavUserActionDataHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Navigator user action handler
 */
public class NavUserActionHandler {

    /**
     * Fetch all the navigator objects in the current workbook
     * @param navUserActionDataHolder  User Action Data holder
     * @return list of ole objects
     */
    public static List<NavObject> fetchNavObjects(NavUserActionDataHolder navUserActionDataHolder) {
        Workbook workbook = navUserActionDataHolder.getWorkbook();

        return workbook.getSheetList().stream().map(NavUserActionHandler::fetchNavObjects)
                .flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * Fetch all the navigator objects in the given sheet
     * @param sheet Sheet instance
     * @return list of ole objects in the sheet
     */
    public static List<NavObject> fetchNavObjects(Sheet sheet) {
        List<NavObject> navObjectList = new ArrayList<>();

        // fetching charts
        navObjectList.addAll(getCharts(sheet.getWorkbook(), sheet.getAssociatedName()));

        return navObjectList;
    }

    private static List<NavObject> getCharts(Workbook workbook, String associatedSheetName) {
        List<NavObject> navObjectList = new ArrayList<>();
        ChartContainer chartContainer = workbook.getChartsContainer();

        chartContainer.forEachChart(associatedSheetName, (chart) -> {
            SheetMeta sheetMeta = chart.getSheetMeta();

            NavObject navObject = new NavObject()
                    .setType(NavObjectType.CHART)
                    .setId(chart.getChartID())
                    .setAssociatedSheetName(associatedSheetName)
                    .setName(SheetChartGetterAPI.getChartName(sheetMeta))
                    .setVisible(ChartUtils.requireNonNullElse(SheetChartGetterAPI.getChartVisibility(sheetMeta), Boolean.TRUE));
            navObjectList.add(navObject);
        });

        return navObjectList;
    }

}
