package com.zoho.sheet.navigator.utils;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.function.Function;

public class NavUtils {

    @SuppressWarnings("unchecked")                                  // NO I18N
    public static <T> T getOrCompute(JSONObjectWrapper parentJSON, String key, Function<String, T> producer) {
        if(parentJSON.has(key)) {
            return (T) parentJSON.get(key);
        }
        T newValue = producer.apply(key);
        parentJSON.put(key, newValue);

        return newValue;
    }

}
