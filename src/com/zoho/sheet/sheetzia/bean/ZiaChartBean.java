package com.zoho.sheet.sheetzia.bean;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */

public class ZiaChartBean {

    private InsightSuggestion.ZiaFilter filter;
    private ZiaChartAggregation aggregation;
    private final String rangeId;
    private final String dataRange;    // lululabi.A33:I51
    private List<String> xCat = new ArrayList<>();
    private List<String> yCol = new ArrayList<>();
    private String title = "";
    private String cR = "HORIZONTAL";  // No I18N
    private boolean colWise = true;
    private String chartType = null;

    public ZiaChartBean(String rangeId, String dataRange) {
        this.rangeId = rangeId;
        this.dataRange = dataRange;
    }

    public String getRangeId() {
        return rangeId;
    }

    public String getDataRange() {
        return dataRange;
    }

    public List<String> getxCat() {
        return xCat;
    }

    public void setxCat(List<String> xCat) {
        this.xCat = xCat;
    }

    public boolean isColWise() {
        return colWise;
    }

    public void setColWise(boolean colWise) {
        this.colWise = colWise;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getyCol() {
        return yCol;
    }

    public void setyCol(List<String> yCol) {
        this.yCol = yCol;
    }

    public String getcR() {
        return cR;
    }

    public void setcR(String cR) {
        this.cR = cR;
    }

    public InsightSuggestion.ZiaFilter getFilter() {
        return filter;
    }

    public ZiaChartAggregation getAggregation() {
        return aggregation;
    }

    public void setFilter(InsightSuggestion.ZiaFilter filter) {
        this.filter = filter;
    }

    public void setAggregation(ZiaChartAggregation aggregation) {
        this.aggregation = aggregation;
    }

    public String getChartType() {
        return chartType;
    }

    public void setChartType(String chartType) {
        this.chartType = chartType;
    }

    public JSONObjectWrapper toJSON() {
        JSONObjectWrapper jsonObject = JSONObjectWrapper.toJSONObject(this);
        Iterator keys = jsonObject.keys();
        List<String> keysToRemove = new ArrayList<>();
        while (keys.hasNext()) {
            String key = (String) keys.next();
            if (jsonObject.get(key) == null) {
                keysToRemove.add(key);
            }
        }

        for (String keyToRemove : keysToRemove) {
            jsonObject.remove(keyToRemove);
        }

        return jsonObject;
    }

    public static class ZiaChartAggregation {
        private final int dateOperations;     //
        private final String numericColumns;  //lululabi.F33:F51
        private final String type;            //SUM
        private final boolean enabled;        //true
        @SuppressWarnings("SpellCheckingInspection")    //NO I18N
        private final String colheaders;      //lululabi.C33:C51

        public ZiaChartAggregation(int dateOperations, String numericColumns, String type, boolean enabled, String colheaders) {
            this.dateOperations = dateOperations;
            this.numericColumns = numericColumns;
            this.type = type;
            this.enabled = enabled;
            this.colheaders = colheaders;
        }

        public int getDateOperations() {
            return dateOperations;
        }

        public String getNumericColumns() {
            return numericColumns;
        }

        public String getType() {
            return type;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public String getColheaders() {
            return colheaders;
        }
    }
}
