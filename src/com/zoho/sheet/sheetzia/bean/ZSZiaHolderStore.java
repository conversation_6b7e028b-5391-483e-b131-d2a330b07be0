package com.zoho.sheet.sheetzia.bean;


import com.zoho.sheet.zia2.interfaces.ZiaNLHelper;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */

public class ZSZiaHolderStore {
    private ZiaNLHelper nlHelper;
    private final String holderID;
    private final long createdOn = System.currentTimeMillis();

    public ZSZiaHolderStore(String holderID) {
        this.holderID = holderID;
    }

    public ZSZiaHolderStore setNlHelper(ZiaNLHelper nlHelper) {
        this.nlHelper = nlHelper;
        return this;
    }

    public ZiaNLHelper getNlHelper() {
        return nlHelper;
    }

    public String getHolderID() {
        return holderID;
    }

    public long getCreatedOn() {
        return createdOn;
    }

    public boolean isEligibleToClean() {
        long currentTimeMillis = System.currentTimeMillis();
        long minutesDiff = TimeUnit.MILLISECONDS.toMinutes(currentTimeMillis - this.createdOn);
        return minutesDiff >= 15;
    }
}
