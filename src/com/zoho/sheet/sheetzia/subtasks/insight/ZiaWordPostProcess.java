package com.zoho.sheet.sheetzia.subtasks.insight;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.nlp.exception.NLPException;
import com.zoho.nlp.insights.DescriptiveInsights;
import com.zoho.sheet.sheetzia.ZSZiaConstants;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.ZiaSubTaskAbstract;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> - 13900
 */

public class ZiaWordPostProcess extends ZiaSubTaskAbstract {

    private final List<JSONObjectWrapper> chartNarration = new ArrayList<>();
    private final List<JSONObjectWrapper> pivotNarration = new ArrayList<>();
    private final List<JSONObjectWrapper> chartNPivotNarration = new ArrayList<>();

    public ZiaWordPostProcess(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() throws Exception {
        ZSZiaHolder ziaHolder = (ZSZiaHolder) holder;
        Map<String, List<DescriptiveInsights>> wordInsightMap = ziaHolder.getWordInsights();

        //order should be same as ZiaInsightPostProcess
        appendWordInsight(wordInsightMap.get(ZiaConstants.RecommendationType.CHART.name()), chartNarration);
        appendWordInsight(wordInsightMap.get(ZiaConstants.RecommendationType.PIVOT.name()), pivotNarration);
        appendWordInsight(wordInsightMap.get(ZiaConstants.RecommendationType.MIXED.name()), chartNPivotNarration);

        JSONObjectWrapper narrationResp = new JSONObjectWrapper();
        narrationResp.put(ZiaConstants.RecommendationType.CHART.toString(), chartNarration);
        narrationResp.put(ZiaConstants.RecommendationType.PIVOT.toString(), pivotNarration);
        narrationResp.put(ZiaConstants.RecommendationType.MIXED.toString(), chartNPivotNarration);
        ziaHolder.getResponseHolder().setWordSenseResponse(narrationResp);

    }

    private void appendWordInsight(List<DescriptiveInsights> insightList, List<JSONObjectWrapper> responseList) throws NLPException {
        if(Objects.isNull(insightList)) {
            return;
        }

        for (int i = 0; i < insightList.size(); i++) {
            DescriptiveInsights insight = insightList.get(i);
            org.json.JSONObject insightJSON = insight.toJSON();
            JSONObjectWrapper insightObj = new JSONObjectWrapper(insightJSON);

            JSONObjectWrapper obj = new JSONObjectWrapper();
            obj.put(ZSZiaConstants.NarrationKeys.NARRATION, insightObj);
            responseList.add(obj);
        }
    }
}
