package com.zoho.sheet.sheetzia.subtasks.insight;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.ext.functions.RangeFunctions;
import com.singularsys.jep.EvaluationException;
import com.zoho.sheet.knitcharts.chartsdatatable.CacheDataTablePool;
import com.zoho.sheet.sheetzia.ZSZiaConstants;
import com.zoho.sheet.sheetzia.ZSZiaEssentials;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.ZSZiaUtilities;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.ZiaSubTaskAbstract;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ZiaParams;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */

public class ZSInsightPostProcess extends ZiaSubTaskAbstract {

    private final Logger logger = Logger.getLogger(this.getClass().getName());
    private final List<JSONObjectWrapper> chartResp = new ArrayList<>();
    private final List<JSONObjectWrapper> pivotResp = new ArrayList<>();
    private final List<JSONObjectWrapper> chartNPivotResp = new ArrayList<>();

    private final List<JSONObjectWrapper> chartMeta = new ArrayList<>();
    private final List<JSONObjectWrapper> pivotMeta = new ArrayList<>();
    private final List<JSONObjectWrapper> chartNPivotMeta = new ArrayList<>();

    private long chartTime = 0;
    private long pivotTime = 0;

    private boolean includeWordInsight;

    public ZSInsightPostProcess(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() throws Exception {
        ZiaParams ziaParams = holder.getZiaParams();
        ZSZiaHolder holder = (ZSZiaHolder) this.holder;
        ZSZiaEssentials essentials = holder.getEssentials();
        Sheet sheet = essentials.getSheet();
        this.includeWordInsight = essentials.isIncludeWordInsight();

        Map<ZiaConstants.RecommendationType, List<InsightSuggestion>> segregatedSuggestions = holder.getResponseHelper().getInsightSuggestions();
        createChartObjects(segregatedSuggestions.get(ZiaConstants.RecommendationType.CHART));
        createPivotObjects(segregatedSuggestions.get(ZiaConstants.RecommendationType.PIVOT));
        createChartNPivotObjects(segregatedSuggestions.get(ZiaConstants.RecommendationType.MIXED));

        JSONObjectWrapper response = new JSONObjectWrapper();
        response.put(ZiaConstants.RecommendationType.CHART.toString(), chartResp);
        response.put(ZiaConstants.RecommendationType.PIVOT.toString(), pivotResp);
        response.put(ZiaConstants.RecommendationType.MIXED.toString(), chartNPivotResp);
        response.put(ZSZiaConstants.ResponseConstant.RECOMMENDED_QUERY, holder.getInsightDecisionHolder().getRecommendedQueries());

        List<ZiaRange> tableRanges = ziaParams.getTableRanges();

        boolean skipHiddenRows = essentials.isSkipHiddenRows();
        List<Range> tableZSRange = ZSZiaUtilities.toZSRange(sheet, tableRanges);
        response.put(ZSZiaConstants.ResponseConstant.TABLE_AGGREGATION, getTableAggregationResponse(tableZSRange, skipHiddenRows));

        holder.getResponseHolder().setInsightResponse(response);

        if(!this.includeWordInsight){
            JSONObjectWrapper insightInfo = new JSONObjectWrapper();
            insightInfo.put(ZiaConstants.RecommendationType.CHART.toString(), chartMeta);
            insightInfo.put(ZiaConstants.RecommendationType.PIVOT.toString(), pivotMeta);
            insightInfo.put(ZiaConstants.RecommendationType.MIXED.toString(), chartNPivotMeta);
            holder.getResponseHolder().setInsightInfo(insightInfo);
        }

        holder.addTimeLine("ZSChart", chartTime, ZiaConstants.TaskType.SUBTASK);    //No I18N
        holder.addTimeLine("ZSPivot", pivotTime, ZiaConstants.TaskType.SUBTASK);    //No I18N
    }

    private JSONObjectWrapper getTableAggregationResponse(List<Range> tableRange, boolean skipHiddenRow) {
        JSONObjectWrapper out = null;
        try {
            out = RangeFunctions.getStatusBarFunctionResults(tableRange, skipHiddenRow);
        } catch (EvaluationException ignored) {
        }
        return out;
    }

    private void createChartObjects(List<InsightSuggestion> insightSuggestions) {
        long startTime = System.currentTimeMillis();

        CacheDataTablePool dataTablePool = new CacheDataTablePool();
        for (InsightSuggestion insightSuggestion : insightSuggestions) {
            JSONObjectWrapper chartObj = ZSZiaUtilities.getAsChartObject(dataTablePool, insightSuggestion, (ZSZiaHolder) holder);

            if (Objects.nonNull(chartObj)) {
                chartResp.add(chartObj);

                if(!this.includeWordInsight){
                    chartMeta.add(ZSZiaUtilities.getSuggestionMeta(insightSuggestion));
                }
            }
        }

        chartTime += System.currentTimeMillis() - startTime;
    }

    private void createPivotObjects(List<InsightSuggestion> insightSuggestions) {
        long startTime = System.currentTimeMillis();

        for (InsightSuggestion insightSuggestion : insightSuggestions) {
            try {
                JSONObjectWrapper pivotObj = ZSZiaUtilities.getAsPivotObject(insightSuggestion, (ZSZiaHolder) holder);

                if (Objects.nonNull(pivotObj)) {
                    pivotResp.add(pivotObj);

                    if(!this.includeWordInsight){
                        pivotMeta.add(ZSZiaUtilities.getSuggestionMeta(insightSuggestion));
                    }
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "Zia Pivot object error >>>> ", e);
            }
        }

        pivotTime += System.currentTimeMillis() - startTime;
    }

    //Mixed objects
    private void createChartNPivotObjects(List<InsightSuggestion> insightSuggestions) {
        CacheDataTablePool dataTablePool = new CacheDataTablePool();
        for (InsightSuggestion insightSuggestion : insightSuggestions) {

            long startTime = System.currentTimeMillis();
            JSONObjectWrapper chartObject = ZSZiaUtilities.getAsChartObject(dataTablePool, insightSuggestion, (ZSZiaHolder) holder);
            chartTime += System.currentTimeMillis() - startTime;

            startTime = System.currentTimeMillis();
            JSONObjectWrapper pivotObject = ZSZiaUtilities.getAsPivotObject(insightSuggestion, (ZSZiaHolder) holder);
            pivotTime += System.currentTimeMillis() - startTime;

            JSONObjectWrapper obj = new JSONObjectWrapper();

            if (Objects.nonNull(chartObject) && Objects.nonNull(pivotObject)) {
                obj.put(ZiaConstants.RecommendationType.CHART.toString(), chartObject);
                obj.put(ZiaConstants.RecommendationType.PIVOT.toString(), pivotObject);
                chartNPivotResp.add(obj);

                if(!this.includeWordInsight){
                    chartNPivotMeta.add(ZSZiaUtilities.getSuggestionMeta(insightSuggestion));
                }
            } else if (Objects.nonNull(pivotObject)) {
                obj.put(ZiaConstants.RecommendationType.PIVOT.toString(), pivotObject);
                pivotResp.add(pivotObject);

                if(!this.includeWordInsight){
                    pivotMeta.add(ZSZiaUtilities.getSuggestionMeta(insightSuggestion));
                }
            } else if (Objects.nonNull(chartObject)) {
                obj.put(ZiaConstants.RecommendationType.CHART.toString(), chartObject);
                chartResp.add(chartObject);

                if(!this.includeWordInsight){
                    chartMeta.add(ZSZiaUtilities.getSuggestionMeta(insightSuggestion));
                }
            }
        }
    }
}
