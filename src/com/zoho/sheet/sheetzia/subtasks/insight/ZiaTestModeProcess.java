package com.zoho.sheet.sheetzia.subtasks.insight;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.ZiaSubTaskAbstract;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ZiaTestModeProcess extends ZiaSubTaskAbstract {

    public ZiaTestModeProcess(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() throws Exception {
        ZSZiaHolder zsHolder = (ZSZiaHolder) this.holder;

        List<String> chartTitles = new ArrayList<>();
        List<String> pivotTitles = new ArrayList<>();
        List<String> mixedTitles = new ArrayList<>();

        Map<ZiaConstants.RecommendationType, List<InsightSuggestion>> segregatedSuggestions = holder.getResponseHelper().getInsightSuggestions();
        makeTitles(segregatedSuggestions.get(ZiaConstants.RecommendationType.CHART), chartTitles);
        makeTitles(segregatedSuggestions.get(ZiaConstants.RecommendationType.PIVOT), pivotTitles);
        makeTitles(segregatedSuggestions.get(ZiaConstants.RecommendationType.MIXED), mixedTitles);

        JSONObjectWrapper testRespone = new JSONObjectWrapper();
        testRespone.put(ZiaConstants.RecommendationType.CHART.toString(), chartTitles);
        testRespone.put(ZiaConstants.RecommendationType.PIVOT.toString(), pivotTitles);
        testRespone.put(ZiaConstants.RecommendationType.MIXED.toString(), mixedTitles);

        zsHolder.getResponseHolder().setInsightResponse(testRespone);
    }

    private void makeTitles(List<InsightSuggestion> insightSuggestions, List<String> titleList) {
        insightSuggestions.forEach(insightSuggestion -> {
            titleList.add(insightSuggestion.getTitle());
        });
    }
}
