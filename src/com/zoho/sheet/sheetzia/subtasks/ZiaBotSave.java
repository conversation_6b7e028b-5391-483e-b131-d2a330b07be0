package com.zoho.sheet.sheetzia.subtasks;

import com.zoho.sheet.sheetzia.ZSZiaConstants;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.ZSZiaStoreManager;
import com.zoho.sheet.sheetzia.bean.ZSZiaHolderStore;
import com.zoho.sheet.zia2.interfaceImpl.taskimpl.ZiaTaskAbstract;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ZiaParams;

/**
 * <AUTHOR>
 */

public class ZiaBotSave extends ZiaTaskAbstract {

    public ZiaBotSave(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() throws Exception {
        ZSZiaHolder zsZiaHolder = (ZSZiaHolder) holder;

        ZiaTableDataHolder tableDataHolder = holder.getTableDataHolder();
        String holderId = zsZiaHolder.getHolderId();
        int noOfCell = tableDataHolder.getContentRowSize() * tableDataHolder.getColSize();

        if (noOfCell >= ZSZiaConstants.BOT_SAVE_LIMIT) {
            ZSZiaHolderStore store = new ZSZiaHolderStore(holderId)
                    .setNlHelper(holder.getNlHelper());
            ZSZiaStoreManager.addToStoreMap(store);
        }
    }
}
