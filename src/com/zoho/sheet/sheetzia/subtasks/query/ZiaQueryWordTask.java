package com.zoho.sheet.sheetzia.subtasks.query;

import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.raz.ZSRazUtilities;
import com.zoho.sheet.sheetzia.raz.model.RazChartMeta;
import com.zoho.sheet.sheetzia.tasks.ZiaWordInsightTask;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.ZiaSubTaskAbstract;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class ZiaQueryWordTask extends ZiaSubTaskAbstract {
    public ZiaQueryWordTask(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() throws Exception {
        ZSZiaHolder holder = (ZSZiaHolder) this.holder;
        Map<ZiaConstants.RecommendationType, List<InsightSuggestion>> querySuggestions = holder.getResponseHelper().getQuerySuggestions();
        Set<ZiaConstants.RecommendationType> recommendationTypes = querySuggestions.keySet();

        if (recommendationTypes.contains(ZiaConstants.RecommendationType.CHART) || recommendationTypes.contains(ZiaConstants.RecommendationType.PIVOT)) {

            InsightSuggestion suggestionObj = null;
            if(recommendationTypes.contains(ZiaConstants.RecommendationType.PIVOT)){
                suggestionObj = querySuggestions.get(ZiaConstants.RecommendationType.PIVOT).get(0);
            }
            else{
                suggestionObj = querySuggestions.get(ZiaConstants.RecommendationType.CHART).get(0);
            }

            RazChartMeta razChartMeta = ZSRazUtilities.insightToRazChart(suggestionObj);
            holder.getEssentials().setChartMeta(razChartMeta);

            ZiaWordInsightTask ziaWordInsightTask = new ZiaWordInsightTask(holder, true);
            ziaWordInsightTask.execute();
        }
    }
}
