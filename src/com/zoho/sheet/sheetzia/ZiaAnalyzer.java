package com.zoho.sheet.sheetzia;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.sheetzia.bean.ZiaResponseError;
import com.zoho.sheet.sheetzia.bean.ZiaResponseHolder;
import com.zoho.sheet.sheetzia.interfaces.ZiaExecutorService;
import com.zoho.sheet.sheetzia.tasks.*;
import com.zoho.sheet.zia2.interfaces.ZiaTask;
import com.zoho.sheet.zia2.utils.ZiaParams;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.ziabeans.ZiaTimestamp;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

public class ZiaAnalyzer {

    private static final Logger LOGGER = Logger.getLogger(ZiaAnalyzer.class.getName());
    private static final boolean IS_TASK_LOG_REQUIRED = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("IS_TASK_LOG_REQUIRED")); //NO I18N
    private final ZSZiaHolder holder;
    private final ZiaExecutorService executorService;


    @SuppressWarnings("unused") //No I18N
    public ZiaAnalyzer(ZSZiaHolder holder) {
        this.holder = holder;
        this.executorService = new ZiaExecutorServiceImpl();
    }

    public ZiaAnalyzer(ZiaParams ziaParams, ZSZiaEssentials essentials) {
        this.holder = new ZSZiaHolder(ziaParams);
        this.holder.setEssentials(essentials);

        this.holder.setDecisionHelper(new ZiaDecisionHelperImpl(essentials.getSheet()));
        this.executorService = new ZiaExecutorServiceImpl();
    }

    public void process() {
        try {
            this.executorService.execute(new TableDetectionTask(holder));

            if (holder.isMiscellaneousTable()) {
                holder.getResponseHolder().setStatusCode(501);
                return;
            }
            ZSZiaEssentials.Includes includes = holder.getEssentials().getIncludes();

            this.executorService.execute(new DataStoreTask(holder));

            List<ZiaTask> analysingTasks = new ArrayList<>();

            if (includes.equals(ZSZiaEssentials.Includes.ALL) || includes.equals(ZSZiaEssentials.Includes.INSIGHT)) {
                analysingTasks.add(new ZiaTableInsightTask(holder));
            }
            if (includes.equals(ZSZiaEssentials.Includes.ALL) || includes.equals(ZSZiaEssentials.Includes.REFINE)) {
                analysingTasks.add(new ZiaExploreTask(holder));
            }
            if (includes.equals(ZSZiaEssentials.Includes.ALL) || includes.equals(ZSZiaEssentials.Includes.CONVERSIONS)) {
                analysingTasks.add(new ZiaConversionTask(holder));
            }
            if (includes.equals(ZSZiaEssentials.Includes.QUERY)) {
                analysingTasks.add(new ZiaQueryTask(holder));
            }
            if (includes.equals(ZSZiaEssentials.Includes.WORDSENSE)) {
                analysingTasks.add(new ZiaWordInsightTask(holder, true));
            }

            if (includes.equals(ZSZiaEssentials.Includes.ALL)) {
                List<Future<?>> futures = this.executorService.executeAsyncAll(analysingTasks);
                this.executorService.awaitAllAsyncTask(futures);
            } else {
                this.executorService.executeAll(analysingTasks);
            }

            List<ZiaTask> endTasks = new ArrayList<>();
            endTasks.add(new ZiaMetaTask(holder));
            this.executorService.executeAll(endTasks);

            holder.getResponseHolder().setStatusCode(200);
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Exception occurred in zia", e);
            holder.getResponseHolder().setStatusCode(400);
        }

    }

    public JSONObjectWrapper getResponse() {
        ZiaResponseHolder ziaResponse = holder.getResponseHolder();

        int statusCode = ziaResponse.getStatusCode();
        ZiaResponseHolder currResponse;
        if (statusCode == 200) {
            currResponse = ziaResponse;

            String tablesRange = holder.getZiaParams().getTableRanges().stream().map(ZiaRange::toString).collect(Collectors.joining(":"));
            currResponse.setTableRange(tablesRange);
        } else {
            ZiaResponseError errorResponse = new ZiaResponseError(statusCode);

            if (statusCode == 501) {
                errorResponse.setMessage("not suitable table detected"); // NO I18N
            }

            String tablesRange = holder.getZiaParams().getTableRanges().stream().map(ZiaRange::toString).collect(Collectors.joining(":"));
            errorResponse.setTableRange(tablesRange);

            return JSONObjectWrapper.toJSONObject(errorResponse);
        }
        JSONObjectWrapper response = JSONObjectWrapper.toJSONObject(ziaResponse);

        if (!IS_TASK_LOG_REQUIRED || Objects.isNull(response)) {
            return response;
        }

        List<ZiaTimestamp> mainTaskTimeLine = holder.getMainTaskTimeLine();
        List<ZiaTimestamp> subTaskTimeLine = holder.getSubTaskTimeLine();

        response.put("mtt", mainTaskTimeLine);
        response.put("stt", subTaskTimeLine);

        return response;
    }
}

