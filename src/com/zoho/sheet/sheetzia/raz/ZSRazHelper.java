package com.zoho.sheet.sheetzia.raz;

import com.zoho.nlp.criteria.CriteriaType;
import com.zoho.nlp.dataholder.format.NumberFormat;
import com.zoho.nlp.datamodel.meta.NLPColumn;
import com.zoho.nlp.datamodel.meta.NLPTable;
import com.zoho.nlp.insights.dataholder.InsightCriteria;
import com.zoho.nlp.insights.dataholder.InsightDimension;
import com.zoho.nlp.insights.dataholder.InsightMeasure;
import com.zoho.nlp.insights.unit.UnitType;
import com.zoho.nlp.translation.datastructure.StandardCriteria;
import com.zoho.nlp.translation.datastructure.topbot.TopBottomCriteria;
import com.zoho.sheet.sheetzia.ZSZiaConstants;
import com.zoho.sheet.sheetzia.raz.model.RazChartMeta;

/**
 * <AUTHOR> - 13900
 */

public class ZSRazHelper {
    public static InsightDimension toInsightDimension(RazChartMeta.RazColumn dataColumn, NLPTable razTableMeta, NLPColumn[] razColumnsMeta) {

        int columnIndex = dataColumn.getColumnIndex();
        NLPColumn razColumMeta = findNLPColumn(columnIndex, razColumnsMeta);
        ZSZiaConstants.ColumnAggregation grpCriteria = dataColumn.getGrpCriteria();

        return new InsightDimension(0L, razTableMeta.getTableID(), razColumMeta.getColumnID(), razTableMeta.getTableName(),
                razColumMeta.getColumnName(), razColumMeta.getColumnType(), grpCriteria.asDimensionFunc());  // cluster id is always zero
    }

    public static InsightMeasure toInsightMeasure(int valCol, NLPTable razTableMeta, NLPColumn[] razColumnsMeta, ZSZiaConstants.ChartAggregation aggOperation) {
        NLPColumn razColumMeta = findNLPColumn(valCol, razColumnsMeta);

        NumberFormat numberFormat = new NumberFormat(3, UnitType.THOUSANDS_MILLIONS_BILLIONS);

        InsightMeasure insightMeasure = new InsightMeasure(0L, razTableMeta.getTableID(), razColumMeta.getColumnID(), razTableMeta.getTableName(),
                razColumMeta.getColumnName(), razColumMeta.getColumnType(), aggOperation.asMeasureFunc());// cluster id is always zero
        insightMeasure.setFormat(numberFormat);
        return insightMeasure;
    }

    public static InsightCriteria toInsightCriteria(RazChartMeta chartMeta, NLPTable razTableMeta, NLPColumn[] razColumnsMeta, ZSZiaConstants.ChartAggregation aggOperation) {
        RazChartMeta.RazChartFilter chartFilter = chartMeta.getChartFilter();

        int filterCol = chartFilter.getFilterCol();
        int filterLimit = chartFilter.getFilterLimit();
        ZSZiaConstants.FilterOperation filterOperation = chartFilter.getFilterOperation();

        NLPColumn filterColumn = findNLPColumn(filterCol, razColumnsMeta);
        StandardCriteria standardCriteria = toStandardCriteria(filterLimit, filterOperation.asFilterFunc());

        InsightCriteria filterCriteria = new InsightCriteria(0L, razTableMeta.getTableID(), filterColumn.getColumnID(), razTableMeta.getTableName(), filterColumn.getColumnName(),
                filterColumn.getColumnType(), aggOperation.asMeasureFunc(), CriteriaType.RANKING, standardCriteria, true);
        filterCriteria.setCriteriaValues(getCriteriaVal(filterLimit, filterOperation));

        return filterCriteria;
    }

    private static NLPColumn findNLPColumn(int colIndex, NLPColumn[] razColumnsMeta) {
        for (NLPColumn nlpColumn : razColumnsMeta) {
            if (nlpColumn.getColumnID() == (long) colIndex) {
                return nlpColumn;
            }
        }
        return null;
    }

    private static StandardCriteria toStandardCriteria(int filterLimit, TopBottomCriteria.TopBottomType filterType) {
        TopBottomCriteria fCriteria = new TopBottomCriteria();

        fCriteria.setNumericValue(filterLimit);
        fCriteria.setTypeOfValue(TopBottomCriteria.TopBottomValueType.VALUE);
        fCriteria.setTypeTopBottom(filterType);

        return fCriteria;
    }

    private static String[] getCriteriaVal(int limit, ZSZiaConstants.FilterOperation filterOperation) {
        if (filterOperation.equals(ZSZiaConstants.FilterOperation.TOP)) {
            return new String[]{"Top " + limit};    //No I18N
        } else {
            return new String[]{"Bottom " + limit}; //No I18N
        }
    }
}
