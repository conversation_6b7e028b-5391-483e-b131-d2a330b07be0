package com.zoho.sheet.sheetzia.raz.model;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.sheetzia.ZSZiaConstants;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR> - 13900
 */

public class RazChartMeta {
    private String chartTitle;  //only used as key in zia;
    private List<Integer> valueColumns = new ArrayList<>();
    private RazColumn axisColumn = null;
    private RazColumn legendColumn = null;
    private ZSZiaConstants.ChartAggregation aggOperation;
    private RazChartFilter chartFilter = null;

    public String getChartTitle() {
        return chartTitle;
    }

    public void setChartTitle(String chartTitle) {
        this.chartTitle = chartTitle;
    }

    public List<Integer> getValueColumns() {
        return valueColumns;
    }

    public void setValueColumns(List<Integer> valueColumns) {
        this.valueColumns = valueColumns;
    }

    public RazColumn getAxisColumn() {
        return axisColumn;
    }

    public void setAxisColumn(RazColumn axisColumn) {
        this.axisColumn = axisColumn;
    }

    public RazColumn getLegendColumn() {
        return legendColumn;
    }

    public void setLegendColumn(RazColumn legendColumn) {
        this.legendColumn = legendColumn;
    }

    public ZSZiaConstants.ChartAggregation getAggOperation() {
        return aggOperation;
    }

    public void setAggOperation(ZSZiaConstants.ChartAggregation aggOperation) {
        this.aggOperation = aggOperation;
    }

    public RazChartFilter getChartFilter() {
        return chartFilter;
    }

    public void setChartFilter(RazChartFilter chartFilter) {
        this.chartFilter = chartFilter;
    }

    public JSONObjectWrapper toJson() {
        JSONObjectWrapper metaJson = new JSONObjectWrapper();

        metaJson.put(ZSZiaConstants.MetaKeys.chartTitle, chartTitle);

        if (!valueColumns.isEmpty()) {
            metaJson.put(ZSZiaConstants.MetaKeys.valueColumns, valueColumns);
        }

        Optional.ofNullable(axisColumn).ifPresent(axisCol -> {
            JSONObjectWrapper colObj = new JSONObjectWrapper();
            colObj.put(ZSZiaConstants.MetaKeys.columnIndex, axisCol.getColumnIndex());
            colObj.put(ZSZiaConstants.MetaKeys.grpCrtiteria, axisCol.getGrpCriteria().name());
            metaJson.put(ZSZiaConstants.MetaKeys.axisColumn, colObj);
        });

        Optional.ofNullable(legendColumn).ifPresent(legendCol -> {
            JSONObjectWrapper colObj = new JSONObjectWrapper();
            colObj.put(ZSZiaConstants.MetaKeys.columnIndex, legendCol.getColumnIndex());
            colObj.put(ZSZiaConstants.MetaKeys.grpCrtiteria, legendCol.getGrpCriteria().name());
            metaJson.put(ZSZiaConstants.MetaKeys.legendColumn, colObj);
        });

        Optional.ofNullable(aggOperation).ifPresent(aggOp -> metaJson.put(ZSZiaConstants.MetaKeys.aggOperation, aggOp.name()));

        Optional.ofNullable(chartFilter).ifPresent(chartFilter -> {
            JSONObjectWrapper filterObj = new JSONObjectWrapper();
            filterObj.put(ZSZiaConstants.MetaKeys.filterCol, chartFilter.getFilterCol());
            filterObj.put(ZSZiaConstants.MetaKeys.filterLimit, chartFilter.getFilterLimit());
            filterObj.put(ZSZiaConstants.MetaKeys.filterOperation, chartFilter.getFilterOperation().name());

            metaJson.put(ZSZiaConstants.MetaKeys.chartFilter, filterObj);
        });

        return metaJson;
    }

    public static RazChartMeta jsonToObj(JSONObjectWrapper metaJson) {
        RazChartMeta razChartMeta = new RazChartMeta();

        String chartTitle = metaJson.getString(ZSZiaConstants.MetaKeys.chartTitle);
        razChartMeta.setChartTitle(chartTitle);

        JSONArrayWrapper colArr = metaJson.optJSONArray(ZSZiaConstants.MetaKeys.valueColumns);
        List<Integer> valCols = Optional.ofNullable(colArr)
                .map(arr -> IntStream.range(0, arr.length())
                        .map(arr::getInt)
                        .boxed()
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        razChartMeta.setValueColumns(valCols);


        setRazColumn(metaJson, ZSZiaConstants.MetaKeys.axisColumn, razChartMeta::setAxisColumn);
        setRazColumn(metaJson, ZSZiaConstants.MetaKeys.legendColumn, razChartMeta::setLegendColumn);

        String aggOperation = metaJson.optString(ZSZiaConstants.MetaKeys.aggOperation, null);
        Optional.ofNullable(aggOperation).ifPresent(aggOp -> razChartMeta.setAggOperation(ZSZiaConstants.ChartAggregation.valueOf(aggOp)));

        JSONObjectWrapper chartFilter = metaJson.optJSONObject(ZSZiaConstants.MetaKeys.chartFilter);
        setRazFilter(chartFilter, razChartMeta::setChartFilter);

        return razChartMeta;
    }

    //Util
    private static void setRazColumn(JSONObjectWrapper metaObj, String key, Consumer<RazColumn> consumer) {
        JSONObjectWrapper colObj = metaObj.optJSONObject(key);
        Optional.ofNullable(colObj)
                .ifPresent(col -> {
                    int colIndex = col.getInt(ZSZiaConstants.MetaKeys.columnIndex);
                    String grpCriteria = col.getString(ZSZiaConstants.MetaKeys.grpCrtiteria);
                    RazColumn legendCol = new RazColumn(colIndex, ZSZiaConstants.ColumnAggregation.valueOf(grpCriteria));
                    consumer.accept(legendCol);
                });
    }

    //Util
    private static void setRazFilter(JSONObjectWrapper filterObj, Consumer<RazChartFilter> consumer) {
        Optional.ofNullable(filterObj)
                .ifPresent(obj -> {
                    int filterCol = obj.getInt(ZSZiaConstants.MetaKeys.filterCol);
                    int filterLimit = obj.getInt(ZSZiaConstants.MetaKeys.filterLimit);
                    String filterOp = obj.getString(ZSZiaConstants.MetaKeys.filterOperation);

                    if (ZSZiaConstants.FilterOperation.valueOf(filterOp) == ZSZiaConstants.FilterOperation.NONE) {
                        return;
                    }

                    RazChartFilter filter = new RazChartFilter(filterCol, filterLimit, ZSZiaConstants.FilterOperation.valueOf(filterOp));
                    consumer.accept(filter);
                });

    }

    public static class RazChartFilter {
        private final int filterCol;
        private final int filterLimit;
        private final ZSZiaConstants.FilterOperation filterOperation;

        public RazChartFilter(int filterCol, int filterLimit, ZSZiaConstants.FilterOperation filterOperation) {
            this.filterCol = filterCol;
            this.filterLimit = filterLimit;
            this.filterOperation = filterOperation;
        }

        public int getFilterCol() {
            return filterCol;
        }

        public int getFilterLimit() {
            return filterLimit;
        }

        public ZSZiaConstants.FilterOperation getFilterOperation() {
            return filterOperation;
        }
    }

    public static class RazColumn {
        private final int columnIndex;    //zero-based index respective to range (like in tableDataHolder)
        private final ZSZiaConstants.ColumnAggregation grpCriteria;

        public RazColumn(int columnIndex, ZSZiaConstants.ColumnAggregation criteria) {
            this.columnIndex = columnIndex;
            this.grpCriteria = criteria;
        }

        public ZSZiaConstants.ColumnAggregation getGrpCriteria() {
            return grpCriteria;
        }

        public int getColumnIndex() {
            return columnIndex;
        }
    }
}
