package com.zoho.sheet.sheetzia.raz.pool;

import com.zoho.sheet.sheetzia.raz.model.ZSRazHolder;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> - 13900
 */

public class ZSRazPool {
    /**
     * A pool layer from which raz-related data can be retrieved by RAZ-data impl classes
     */
    private static final Map<String, ZSRazHolder> RAZ_POOL = new ConcurrentHashMap<>();

    public static ZSRazHolder getBotFromPool(String dbId) {
        return RAZ_POOL.get(dbId);
    }

    public static void setBotInPool(String dbId, ZSRazHolder razHolder) {
        RAZ_POOL.put(dbId, razHolder);
    }

    public static void deleteBotFromPool(String dbId) {
        RAZ_POOL.remove(dbId);
    }

}
