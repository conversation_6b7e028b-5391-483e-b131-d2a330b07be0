package com.zoho.sheet.sheetzia.raz.pool;

import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.raz.model.ZSRazHolder;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.ZiaSubTaskAbstract;
import com.zoho.sheet.zia2.utils.ZiaHolder;

/**
 * <AUTHOR> - 13900
 */

public class SaveBotInPool extends ZiaSubTaskAbstract {

    public SaveBotInPool(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() {
        ZSZiaHolder ziaHolder = (ZSZiaHolder) this.holder;
        ZSRazHolder razHolder = ziaHolder.getRazHolder();
        String dbId = ziaHolder.getHolderId();
        ZSRazPool.setBotInPool(dbId, razHolder);
    }
}
