package com.zoho.sheet.sheetzia.raz.botTasks;

import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.ZSZiaStoreManager;
import com.zoho.sheet.sheetzia.bean.ZSRazHolderStore;
import com.zoho.sheet.sheetzia.raz.model.ZSRazHolder;
import com.zoho.sheet.sheetzia.raz.pool.ZSRazPool;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.ZiaSubTaskAbstract;
import com.zoho.sheet.zia2.utils.ZiaHolder;

import java.util.Objects;

/**
 * <AUTHOR> - 13900
 */

public class ZiaRazBotFetch extends ZiaSubTaskAbstract {
    String holderId;

    public ZiaRazBotFetch(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute(){
        ZSZiaHolder zsZiaHolder = (ZSZiaHolder) holder;
        this.holderId = zsZiaHolder.getHolderId();

        if (hasBot() && !holder.getZiaParams().isRefreshBot()) {
            setRazBot();
        } else {
            // new bot should be prepared, so remove corresponding cache
            // cache removal helps in identifying Raz model modifications. check raz meta and raz data connector
            ZSZiaStoreManager.delRazStoreMap(this.holderId);
        }
    }

    private void setRazBot() {
        ZSRazHolderStore razStore = ZSZiaStoreManager.getRazStore(holderId);
        ZSRazHolder razHolder = razStore.getRazHolder();
        ZSZiaHolder zsHolder = (ZSZiaHolder) this.holder;
        zsHolder.setRazHolder(razHolder);
        ZSRazPool.setBotInPool(zsHolder.getHolderId(), razHolder);
    }

    private boolean hasBot() {
        return Objects.nonNull(holderId) && ZSZiaStoreManager.hasRazStore(holderId);
    }


}
