package com.zoho.sheet.sheetzia.raz.datapipes;

import com.zoho.nlp.datamodel.configurations.ConfigurationsHolder;
import com.zoho.nlp.datamodel.meta.NLPColumn;
import com.zoho.nlp.datamodel.meta.NLPTable;
import com.zoho.nlp.datamodel.meta.NLPTableRelation;
import com.zoho.nlp.raz.datamodel.meta.RAZDMMeta;
import com.zoho.sheet.sheetzia.ZSZiaStoreManager;
import com.zoho.sheet.sheetzia.raz.ZSRazUtilities;

/**
 * <AUTHOR> - 13900
 */

public class RazMetaConnectorImpl extends RAZDMMeta<String> {

    @Override
    public Long[] getNLPClusterIDs(String dbId) {
        return new Long[]{0L};  //no clusterIds :: default 0
    }

    @Override
    public NLPTable[] getNLPTables(String dbId, Long clusterId) {
        return ZSRazUtilities.getRazNLPTables(dbId);
    }

    @Override
    public NLPColumn[] getNLPColumns(String dbId, Long clusterId, Long tableId) {
        return ZSRazUtilities.getRazNLPColumns(dbId);
    }

    @Override
    public NLPTableRelation[] getNLPTableRelations(String dbId, Long clusterId) {
        //no table relations yet
        return new NLPTableRelation[0];
    }

    @Override
    public Boolean isDBMetaModified(Long createdTime, String dbId) {
        //checks if the table structure has changed
        //Check if bot is present in cache layer. if not present data is modified
        return ZSZiaStoreManager.hasRazStore(dbId);
    }

    @Override
    public Boolean isDBConfigurationsModified(Long creatTime, String dbId) {
        return false;
    }

    @Override
    public ConfigurationsHolder getConfigurations(String dbId) {
        return new ConfigurationsHolder();
    }
}
