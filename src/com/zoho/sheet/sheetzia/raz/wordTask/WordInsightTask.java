package com.zoho.sheet.sheetzia.raz.wordTask;

import com.zoho.nlp.insights.DescriptiveInsights;
import com.zoho.nlp.insights.InsightService;
import com.zoho.nlp.insights.configuration.DescriptiveIOConf;
import com.zoho.nlp.insights.dataholder.InsightCriteria;
import com.zoho.nlp.insights.dataholder.InsightDimension;
import com.zoho.nlp.insights.dataholder.InsightMeasure;
import com.zoho.nlp.insights.engine.InsightSQLiteEngine;
import com.zoho.nlp.insights.group.InsightPriority;
import com.zoho.sheet.sheetzia.ZSZiaConstants;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.raz.ZSRazUtilities;
import com.zoho.sheet.sheetzia.raz.model.RazChartMeta;
import com.zoho.sheet.sheetzia.raz.model.ZSRazHolder;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.ZiaSubTaskAbstract;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ZiaResponseHelper;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> - 13900
 */

public class WordInsightTask extends ZiaSubTaskAbstract {
    private final boolean isSingleInsight;
    private DescriptiveIOConf insightConf;

    public WordInsightTask(ZiaHolder holder, boolean isSingleInsight) {
        super(holder);

        this.isSingleInsight = isSingleInsight;
    }

    @Override
    public void execute() throws Exception {
        /*
         * dependencies need
         * 1. raz tableMeta
         * 2. raz columnMeta
         * 3. raz ClusterColumnMap
         * 4. raz dbDetails
         * */

        ZSZiaHolder ziaHolder = (ZSZiaHolder) this.holder;
        ZSRazHolder razHolder = ((ZSZiaHolder) holder).getRazHolder();//Place razHolder

        prepareInsightConf(razHolder);

        if (this.isSingleInsight) {
            List<RazChartMeta> chartMetaList = new ArrayList<>() {{
                add(ziaHolder.getEssentials().getChartMeta());
            }};

            prepareWordInsight(chartMetaList, ziaHolder, razHolder, ZSZiaConstants.NarrationKeys.DEFAULT);
        } else {
            ZiaResponseHelper responseHelper = ziaHolder.getResponseHelper();
            Map<ZiaConstants.RecommendationType, List<InsightSuggestion>> suggestionMap = responseHelper.getInsightSuggestions();

            suggestionMap.forEach((type, insightSuggestionList) -> {
                List<InsightSuggestion> insightSuggestions = suggestionMap.get(type);
                List<RazChartMeta> chartMetaList = new ArrayList<>();

                insightSuggestions.forEach(insightSuggestion -> {
                    RazChartMeta chartMeta = ZSRazUtilities.insightToRazChart(insightSuggestion);
                    chartMetaList.add(chartMeta);
                });

                prepareWordInsight(chartMetaList, ziaHolder, razHolder, type.name());
            });
        }
    }

    private void prepareInsightConf(ZSRazHolder razHolder) {
        DescriptiveIOConf insightConf = new DescriptiveIOConf(InsightService.SHEETS, "en", "IN", razHolder.getUserTimeZone());    //NO I18N

        //Default Values
        insightConf.setMinCategoryValues(5);
        insightConf.setMaxForecastPoints(5);

        this.insightConf = insightConf;
    }

    private void prepareWordInsight(List<RazChartMeta> chartMetaList, ZSZiaHolder ziaHolder, ZSRazHolder razHolder, String responseKeyName) {

        List<DescriptiveInsights> wordInsights = new ArrayList<>();

        for (RazChartMeta chartMeta : chartMetaList) {
            InsightDimension[] dimensions = ZSRazUtilities.chartMetaToDimension(chartMeta, razHolder);
            InsightMeasure[] measures = ZSRazUtilities.chartMetaToMeasure(chartMeta, razHolder);
            InsightCriteria[] criteria = ZSRazUtilities.chartMetaToCriteria(chartMeta, razHolder);

            ziaHolder.getEssentials().setChartMeta(chartMeta);

            try {
                if (chartMeta.getAggOperation() == ZSZiaConstants.ChartAggregation.ACTUAL || dimensions.length == 2) {
                    //insight has two dimensions
                    insightConf.setInsightPriority(InsightPriority.DEFAULT);
                } else {
                    insightConf.setInsightPriority(InsightPriority.MEDIUM);
                }

                InsightSQLiteEngine insightEngine = new InsightSQLiteEngine(razHolder.getDbDetails(), insightConf, ziaHolder);
                DescriptiveInsights insights = insightEngine.getInsights(dimensions, measures, criteria);   //insight criteria is for filtering ex. only east in region

                wordInsights.add(insights);
            } catch (Exception e) {
                logger.log(Level.WARNING, "[ZIA][RAZ] Exception occurred while creating word insights :: chartMeta {0}", chartMeta.toJson().toString());
            }
        }

        ziaHolder.getWordInsights().put(responseKeyName, wordInsights);
    }
}
