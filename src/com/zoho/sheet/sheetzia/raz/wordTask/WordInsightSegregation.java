package com.zoho.sheet.sheetzia.raz.wordTask;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.nlp.insights.DescriptiveInsights;
import com.zoho.sheet.sheetzia.ZSZiaConstants;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.ZiaSubTaskAbstract;
import com.zoho.sheet.zia2.utils.ZiaHolder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - 13900
 */

public class WordInsightSegregation extends ZiaSubTaskAbstract {

    private final JSONObjectWrapper narrationResponse = new JSONObjectWrapper();

    public WordInsightSegregation(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() throws Exception {
        ZSZiaHolder zsZiaHolder = (ZSZiaHolder) holder;
        Map<String, List<DescriptiveInsights>>  wordInsights = zsZiaHolder.getWordInsights();

        DescriptiveInsights insights = wordInsights.get(ZSZiaConstants.NarrationKeys.DEFAULT).get(0);
        org.json.JSONObject insightsJSON = insights.toJSON();
        JSONObjectWrapper insightJson = new JSONObjectWrapper(insightsJSON.toString());
        narrationResponse.put(ZSZiaConstants.NarrationKeys.NARRATION, insightJson);
        zsZiaHolder.getResponseHolder().setWordSenseResponse(narrationResponse);
    }
}
