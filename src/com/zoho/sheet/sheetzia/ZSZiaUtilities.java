package com.zoho.sheet.sheetzia;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.pivot.PivotComponent;
import com.adventnet.zoho.websheet.model.pivot.PivotField;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.nlp.function.Function;
import com.zoho.sheet.action.ZSheetOpenAiAction;
import com.zoho.sheet.chart.AggregationConstants;
import com.zoho.sheet.chart.ChartUtility;
import com.zoho.sheet.chart.ChartUtils;
import com.zoho.sheet.chartengine.ChartEngineManager;
import com.zoho.sheet.knitcharts.chartsdatatable.CacheDataTablePool;
import com.zoho.sheet.sheetzia.bean.ZiaChartBean;
import com.zoho.sheet.sheetzia.raz.ZSRazUtilities;
import com.zoho.sheet.sheetzia.raz.model.RazChartMeta;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.utils.*;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;
import org.joda.time.DateTime;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */

public class ZSZiaUtilities {

    public static ZiaRange toColumnRange(ZiaRange tableRange, int columnIndex, boolean isFirstRowAsLabel) {
        if (isFirstRowAsLabel) {
            return new ZiaRange(
                    Math.min(tableRange.getStartRowIndex() + 1, tableRange.getEndRowIndex()),
                    columnIndex,
                    tableRange.getEndRowIndex(),
                    columnIndex
            );
        } else {
            return toColumnRange(tableRange, columnIndex);
        }
    }

    public static ZiaRange toColumnRange(ZiaRange tableRange, int columnIndex) {
        return new ZiaRange(
                tableRange.getStartRowIndex(),
                columnIndex,
                tableRange.getEndRowIndex(),
                columnIndex
        );
    }


    public static Range toZSRange(Sheet sheet, ZiaRange range) {
        return new Range(sheet, range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
    }

    public static List<Range> toZSRange(Sheet sheet, List<ZiaRange> rangeList) {
        return rangeList.stream().map(i -> toZSRange(sheet, i)).collect(Collectors.toList());
    }

    public static DataRange toZSDataRange(ZiaRange range, Sheet sheet) {
        return new DataRange(sheet.getAssociatedName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
    }

    public static ZiaRange toZiaRange(Range range) {
        return new ZiaRange(range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
    }

    public static String getCellContent(Sheet sheet, int rowIndex, int colIndex) {
        Cell cell = sheet.getCell(rowIndex, colIndex);
        return cell != null ? cell.getContent() : "";
    }

    public static Cell.Type getCellType(Sheet sheet, int rowIndex, int colIndex) {
        Cell cell = sheet.getCell(rowIndex, colIndex);
        return cell != null ? cell.getType() : Cell.Type.STRING;
    }

    public static ZiaContentVO.Type cellTypeToZiaContentType(Cell.Type cellType) {
        switch (cellType) {
            case FLOAT:
            case SCIENTIFIC:
            case FRACTION:
                return ZiaContentVO.Type.NUMBER;
            case PERCENTAGE:
                return ZiaContentVO.Type.PERCENTAGE;
            case CURRENCY:
                return ZiaContentVO.Type.CURRENCY;
            case DATE:
                return ZiaContentVO.Type.DATE;
            case TIME:
                return ZiaContentVO.Type.TIME;
            case DATETIME:
                return ZiaContentVO.Type.DATETIME;
            case STRING:
                return ZiaContentVO.Type.STRING;
            case BOOLEAN:
                return ZiaContentVO.Type.BOOLEAN;
            default:
                return ZiaContentVO.Type.UNDEFINED;
        }
    }

    public static String getHeader(Sheet sheet, ZiaRange colRange, boolean isFirstRecordAsLabel, boolean isSeriesInRow) {
        String header = "";

        if (isFirstRecordAsLabel) {
            header = getCellContent(sheet, colRange.getStartRowIndex(), colRange.getStartColIndex());
        }

        if (header == null || header.isEmpty()) {
            if (isSeriesInRow) {
                return "Row " + ZiaUtilities.getCharFromNum(colRange.getStartRowIndex() + 1);   //NO I18N
            }
            return "Column " + ZiaUtilities.getCharFromNum(colRange.getStartColIndex() + 1);    //NO I18N
        } else {
            return header;
        }
    }

    public static JSONObjectWrapper analyseZia(Sheet sheet, JSONObjectWrapper actionJSON) {
        JSONObjectWrapper paramsObj = actionJSON.getJSONObject(JSONConstants.ZIA_PARAMS);

        ZiaParams ziaParams = toZiaParams(sheet, paramsObj);
        ZSZiaEssentials essentials = toEssentials(sheet, paramsObj);
        ZiaAnalyzer analyzer = new ZiaAnalyzer(ziaParams, essentials);

        analyzer.process();
        return analyzer.getResponse();
    }

    private static ZSZiaEssentials toEssentials(Sheet sheet, JSONObjectWrapper paramsObj) {
        JSONArrayWrapper activeCellObj = paramsObj.getJSONArray(JSONConstants.CURRENT_ACTIVE_CELL);
        ZiaRange activeCell = parseRangeObj(activeCellObj);

        ZSZiaEssentials ziaEssentials = new ZSZiaEssentials(sheet, activeCell);

        ziaEssentials.setTableDetectionRequired(paramsObj.optBoolean(ZSZiaConstants.RequestConstant.DETECT_TABLE, false));
        ziaEssentials.setRefineMetaRequired(paramsObj.optBoolean(ZSZiaConstants.RequestConstant.REFINE_META, false));

        String includesName = paramsObj.optString(ZSZiaConstants.RequestConstant.INCLUDES);
        if (ZSZiaEssentials.Includes.isExist(includesName)) {
            ZSZiaEssentials.Includes includes = ZSZiaEssentials.Includes.valueOf(includesName);
            ziaEssentials.setIncludes(includes);
        }

        JSONArrayWrapper selectedColsArr = paramsObj.optJSONArray(ZSZiaConstants.RequestConstant.SELECTED_COLS);
        if (selectedColsArr != null) {
            List<Integer> selectedCols = new ArrayList<>();
            for (int i = 0; i < selectedColsArr.length(); i++) {
                selectedCols.add(selectedColsArr.getInt(i));
            }
            ziaEssentials.setSelectedColumns(selectedCols);
        }

        JSONObjectWrapper insightChartMeta = paramsObj.optJSONObject(ZSZiaConstants.RequestConstant.RAZ_INSIGHT_META);

        Optional.ofNullable(insightChartMeta)
                .ifPresent(meta -> {
                    RazChartMeta razChartMeta = RazChartMeta.jsonToObj(meta);
                    ziaEssentials.setChartMeta(razChartMeta);
                });

        return ziaEssentials;
    }

    private static List<ZiaRange> parseZiaRange(JSONArrayWrapper rangeArr) {
        Object startRange = rangeArr.get(0);

        if(startRange instanceof JSONArrayWrapper){
            //multiple ranges
            List<ZiaRange> tableRanges =  new ArrayList<>();

            for (int i = 0; i < rangeArr.length(); i++) {
                JSONArrayWrapper range = rangeArr.getJSONArray(i);

                ZiaRange ziaRange = parseRangeObj(range);
                tableRanges.add(ziaRange);
            }
            return tableRanges;
        }
        else{
            return Collections.singletonList(parseRangeObj(rangeArr));
        }
    }

    private static ZiaRange parseRangeObj(JSONArrayWrapper rangeObj){
        int startRow = rangeObj.optInt(0);
        int startCol = rangeObj.optInt(1);
        int endRow = rangeObj.optInt(2);
        int endCol = rangeObj.optInt(3);

        return new ZiaRange(startRow, startCol, endRow, endCol);
    }

    public static ZiaParams toZiaParams(Sheet sheet, JSONObjectWrapper paramsObj) {
        ZiaParams params = new ZiaParams();

        JSONArrayWrapper tableRangeArr = paramsObj.optJSONArray(ZSZiaConstants.RequestConstant.TABLE_RANGE);
        if (tableRangeArr != null) {
            params.setTableRanges(parseZiaRange(tableRangeArr));
        }
//        params.setHolderID(paramsObj.optString(ZSZiaConstants.RequestConstant.ZIA_HOLDER_ID, null));
        params.setRefreshBot(paramsObj.optBoolean(ZSZiaConstants.RequestConstant.REFRESH_ZIA_BOT, true));

        String userQuery = paramsObj.optString(ZSZiaConstants.RequestConstant.USER_QUERY);
        params.setUserQuery(userQuery);

        params.setLocale(sheet.getWorkbook().getLocale());
        return params;
    }

    public static int toAggregationConst(ZiaConstants.ZiaDateAggregationType aggregationType) {
        switch (aggregationType) {
            case YEAR:
                return AggregationConstants.YEAR;
            case QUARTER:
                return AggregationConstants.QUARTER;
            case MONTH:
                return AggregationConstants.MONTH;
            case DAY:
                return AggregationConstants.DAY;
            case DAY_OF_WEEK:
                return AggregationConstants.DAY_OF_WEEK;
            case QUARTER_BY_YEAR:
                return AggregationConstants.QUARTER_BY_YEAR;
            case MONTH_BY_YEAR:
                return AggregationConstants.MONTH_BY_YEAR;
            case HALF_YEARLY:
                return AggregationConstants.HALF_YEARLY;
            case WEEK:
                return AggregationConstants.WEEK;
            default:
                return AggregationConstants.NONE;
        }
    }

    public static ZiaContentVO getZiaContentVO(Cell cell) {
        Cell.Type contentType = cell.getContentType();
        Object value = cell.getValue().getValue();
        String actualValue = value.toString();

        if (contentType.isDateType()) {
            if (value instanceof Date) {
                actualValue = String.valueOf(((Date) value).getTime());
            } else if (value instanceof DateTime) {
                actualValue = String.valueOf(((DateTime) value).toDate().getTime());
            } else {
                actualValue = String.valueOf(DateUtil.convertNumberToDate((Number) value).getTime());
            }
        }
        if (contentType.isNumberType()) {
            if (value instanceof Double) {
                actualValue = String.valueOf((double) value);
            }
        }

        return new ZiaContentVO(ZSZiaUtilities.cellTypeToZiaContentType(contentType), cell.getContent(), actualValue);
    }

    public static JSONObjectWrapper getAsChartObject(CacheDataTablePool dataTablePool, InsightSuggestion insightSuggestion, ZSZiaHolder holder) {
        return ChartEngineManager.getChartEngine(holder.getEssentials().getSheet().getWorkbook()).convertZiaSuggestionToChart(holder, insightSuggestion, dataTablePool);
    }

    public static JSONObjectWrapper convertChartBeanToObj(Sheet sheet, String asn, ZiaChartBean chartBean, boolean logScale, boolean legendReq) {
//        JSONObjectWrapper userRequestedChart = null;
//        try {
//            JSONObjectWrapper chartEssentials = chartBean.toJSON();
//            Map<String, Object> chartInputs = ChartUtility.convertJsonObjectToMap(chartEssentials, asn, sheet);
//            userRequestedChart = ChartUtils.getUserRequestedChart(sheet.getWorkbook(), chartInputs, false);
//            if ((Objects.nonNull(userRequestedChart))) {
//                JSONObjectWrapper co = userRequestedChart.getJSONObject("co"); //NO I18N
//                JSONArrayWrapper series = co.optJSONArray("series");   //NO I18N
//
//                if (Objects.isNull(series) || series.isEmpty()) {
//                    return null;
//                }
//
//                if (logScale) {
//                    co.getJSONArray("yAxis").getJSONObject(0).set("type", "logarithmic");
//                }
//
//                if (legendReq) {
//                    co.getJSONObject("legend").set("enabled", true);
//                    userRequestedChart.set("legendPos", 1);
//                } else {
//                    co.getJSONObject("legend").set("enabled", false);
//                    userRequestedChart.set("legendPos", 0);
//                }
//            }
//        } catch (Exception ignored) {
//        }
//        return userRequestedChart;
        return ChartEngineManager.getChartEngine(sheet.getWorkbook()).convertZiaChartBeanToChart(sheet, chartBean, asn, logScale, legendReq);
    }


    public static JSONObjectWrapper getAsPivotObject(InsightSuggestion insightSuggestion, ZSZiaHolder holder) {
        JSONObjectWrapper currPivotObj = new JSONObjectWrapper();
        ZiaParams ziaParams = holder.getZiaParams();
        ZSZiaEssentials essentials = holder.getEssentials();

        List<ZiaRange> tableRanges = ziaParams.getTableRanges();
        Sheet sheet = essentials.getSheet();

        if (tableRanges.size() != 1) {
            return null;
        }
        ZiaRange tableRange = tableRanges.get(0);


        currPivotObj.put(JSONConstants.SOURCE_START_ROW, tableRange.getStartRowIndex());
        currPivotObj.put(JSONConstants.SOURCE_START_COLUMN, tableRange.getStartColIndex());
        currPivotObj.put(JSONConstants.SOURCE_END_ROW, tableRange.getEndRowIndex());
        currPivotObj.put(JSONConstants.SOURCE_END_COLUMN, tableRange.getEndColIndex());
        currPivotObj.put(JSONConstants.ASSOCIATED_SOURCE_SHEET_NAME, sheet.getAssociatedName());

        List<JSONObjectWrapper> pivotFunctions = new ArrayList<>();
        for (String operation : insightSuggestion.getAggregatedOperation()) {
            operation = getPivotFunctionName(operation);
            pivotFunctions.add(new JSONObjectWrapper().put(operation, "none"));
        }

        currPivotObj.put(JSONConstants.FUNCTION, pivotFunctions);

        List<Integer> valueColumns = insightSuggestion.getValueColumns();
        List<Integer> categoricalColumns = new ArrayList<>(insightSuggestion.getCategoricalColumns());
        List<Integer> dateColumns = insightSuggestion.getDateColumns();
        currPivotObj.put(JSONConstants.DATA_FIELDS, valueColumns);

        // Pivot Defaults
        currPivotObj.put(JSONConstants.TITLE, insightSuggestion.getTitle());
        currPivotObj.put(JSONConstants.SELECT_ALL, true);
        currPivotObj.put(JSONConstants.IS_DUMMY_PIVOT, true);
        currPivotObj.put(JSONConstants.PIVOTTEMPLATE_ID, "2tc1");    //No I18N
        currPivotObj.put(JSONConstants.PIVOT_TOTALOPTION, Arrays.asList(1, 1, 1));
        currPivotObj.put(JSONConstants.PAGE_FIELDS, Collections.emptyList());
        currPivotObj.put(JSONConstants.GROUP_MAP, new HashMap<>());

        List<Integer> rowCols = new ArrayList<>();
        if (!dateColumns.isEmpty()) {
            rowCols.addAll(dateColumns);
        }

        if (categoricalColumns.size() > 1) {

            rowCols.addAll(categoricalColumns.subList(1, categoricalColumns.size()));
            currPivotObj.put(JSONConstants.COLUMN_FIELDS, Collections.singletonList(categoricalColumns.get(0)));
            currPivotObj.put(JSONConstants.ROW_FIELDS, rowCols);
        } else {

            rowCols.addAll(categoricalColumns);

            currPivotObj.put(JSONConstants.ROW_FIELDS, rowCols);
            currPivotObj.put(JSONConstants.COLUMN_FIELDS, Collections.emptyList());
        }

        if (insightSuggestion.getRecommendationTypes().contains(ZiaConstants.RecommendationType.MIXED)) {
            JSONArrayWrapper sort = new JSONArrayWrapper();
            JSONObjectWrapper sortJson = new JSONObjectWrapper();
            sortJson.put(JSONConstants.PIVOT_FIELD_TYPE, "row");    //NO I18N
            sortJson.put(JSONConstants.HEADER_INDEX, 0);
            sortJson.put(JSONConstants.PIVOT_DATAFIELDINDEX, 0);
            sortJson.put(JSONConstants.ASCENDING_SORT, false);
            sort.put(sortJson);
            currPivotObj.put(JSONConstants.PIVOT_SORT_DETAILS, sort);
        }

        InsightSuggestion.ZiaFilter ziaFilter = insightSuggestion.getFilter();
        if (Objects.nonNull(ziaFilter)) {
            JSONArrayWrapper filters = toFilterJSON(ziaFilter);
            currPivotObj.put(JSONConstants.PIVOT_FILTER_DETAILS, filters);
        }

        if (!dateColumns.isEmpty() && insightSuggestion.getDateGrouping() != ZiaConstants.ZiaDateAggregationType.NONE) {
            JSONObjectWrapper groupJson = new JSONObjectWrapper();
            JSONArrayWrapper rowGroupIndex = new JSONArrayWrapper();
            groupJson.put(JSONConstants.ROW_FIELDS, rowGroupIndex);
            groupJson.put(JSONConstants.COLUMN_FIELDS, new JSONArrayWrapper());
            groupJson.put(JSONConstants.PAGE_FIELDS, new JSONArrayWrapper());
            groupJson.put(JSONConstants.DATA_FIELDS, new JSONArrayWrapper());
            currPivotObj.put(JSONConstants.GROUP_MAP, groupJson);
            rowGroupIndex.put(ZSZiaUtilities.toAggregationConst(insightSuggestion.getDateGrouping()));
        }

        String dataRangeStr = "";
        List<Integer> dataRangeCols = new ArrayList<>();
        dataRangeCols.addAll(valueColumns);
        dataRangeCols.addAll(categoricalColumns);

        for (Integer valueColumn : dataRangeCols) {
            String rangeStr = ZSZiaUtilities.toColumnRange(tableRange, valueColumn).toString();
            dataRangeStr = dataRangeStr.isEmpty() ? rangeStr : String.join(";", dataRangeStr, rangeStr);
        }
        currPivotObj.put(ZSZiaConstants.ResponseConstant.DATA_RANGE_STR, dataRangeStr);

        try {
            Workbook workbook = sheet.getWorkbook();

            PivotComponent pivotComponent = ActionUtil.createRecommendedPivotModel(workbook, currPivotObj, false);
            JSONObjectWrapper pivotActionJson = pivotComponent.populatePivotGrid(workbook);
            pivotActionJson.put(JSONConstants.PIVOT_ACTION_JSON, currPivotObj);
            pivotActionJson.put(ZSZiaConstants.ResponseConstant.TITLE, insightSuggestion.getTitle());
            return pivotActionJson;

        } catch (Exception exception) {
            return null;
        }
    }

    private static JSONArrayWrapper toFilterJSON(InsightSuggestion.ZiaFilter ziaFilter) {
        String type = ziaFilter.getType().toLowerCase() + " values";    //NO I18N

        JSONArrayWrapper filters = new JSONArrayWrapper();
        JSONObjectWrapper filterJSON = new JSONObjectWrapper();

        filterJSON.put(JSONConstants.PIVOT_FIELD_TYPE, "row");  //NO I18N
        filterJSON.put(JSONConstants.HEADER_INDEX, 0);
        filterJSON.put(JSONConstants.PIVOT_DATAFIELDINDEX, 0);
        filterJSON.put(JSONConstants.PIVOT_FILTER_TYPE, 2);
        filterJSON.put(JSONConstants.FILTER_CRITERIA_1, type);
        filterJSON.put(JSONConstants.FILTER_VALUE_1, ziaFilter.getLimit());
        filterJSON.put(JSONConstants.SELECT_ALL, false);

        filters.put(filterJSON);
        return filters;
    }

    public static String getAsPivotFunc(Function function) {
        switch (function) {
            case SUM:
                return ZiaConstants.AggregationOperation.SUM;
            case AVERAGE:
                return ZiaConstants.AggregationOperation.AVG;
            case COUNT:
                return ZiaConstants.AggregationOperation.COUNT;
            case DISTINCT_COUNT:
                return PivotField.Function.COUNTNUMS.name();    //countnums is the function name in pivot
            case MINIMUM:
                return ZiaConstants.AggregationOperation.MIN;
            case MAXIMUM:
                return ZiaConstants.AggregationOperation.MAX;
            case MEDIAN:
                return ZiaConstants.AggregationOperation.MEDIAN;
        }
        return ZiaConstants.AggregationOperation.ACTUAL;
    }

    public static int getAsPivotGroupFunc(Function function) {
        switch (function) {
            case YEAR:
                return AggregationConstants.YEAR;
            case QUARTER:
                return AggregationConstants.QUARTER;
            case MONTH:
                return AggregationConstants.MONTH;
            case DAY:
                return AggregationConstants.DAY;
            case WEEKDAY:
                return AggregationConstants.DAY_OF_WEEK;
            case ABS_QUARTER:
                return AggregationConstants.QUARTER_BY_YEAR;
            case ABS_MONTH:
                return AggregationConstants.MONTH_BY_YEAR;
        }
        return AggregationConstants.NONE;
    }

    public static String getChartFunctionName(String aggregationOperation) {
        return aggregationOperation.toUpperCase();
    }

    private static String getPivotFunctionName(String aggregationOperation) {
        if (aggregationOperation.equals(ZiaConstants.AggregationOperation.COUNT_DISTINCT)) {
            return ZiaConstants.AggregationOperation.COUNT.toLowerCase();
        }
        return aggregationOperation.toLowerCase();
    }

    public static List<Integer> getFilteredColumns(ZSZiaEssentials essentials, ZiaRange tableRange) {
        List<Integer> filterColumns = new ArrayList<>();
        List<Integer> selectedColumns = essentials.getSelectedColumns();

        List<Integer> catCols = new ArrayList<>();
        List<Integer> numCols = new ArrayList<>();
        List<Integer> dateCols = new ArrayList<>();

        for (Integer col : selectedColumns) {
            ZiaConstants.ColumnType columnType = identifyColumnType(col, tableRange, essentials);
            if (columnType.equals(ZiaConstants.ColumnType.CATEGORICAL)) {
                catCols.add(col);
            } else if (columnType.equals(ZiaConstants.ColumnType.NUMERICAL)) {
                numCols.add(col);
            } else if (columnType.equals(ZiaConstants.ColumnType.DATE)) {
                dateCols.add(col);
            }
        }

        Map<ZiaConstants.ColumnType, List<Integer>> colMap = new HashMap<>() {{
            put(ZiaConstants.ColumnType.CATEGORICAL, catCols);
            put(ZiaConstants.ColumnType.NUMERICAL, numCols);
        }};

        ZiaConstants.DataSetNature dataNature = ZiaUtilities.getDataSetNatureBased(colMap);
        ZSZiaConstants.colLimit colLimiter = ZSZiaConstants.COL_LIMIT_MAP.get(dataNature);

        int maxCatCols = colLimiter.getMaxCatCols();
        int maxDateCols = colLimiter.getMaxDateCols();
        int maxNumCols = colLimiter.getMaxNumCols();

        filterColumns.addAll(catCols.subList(0, Math.min(catCols.size(), maxCatCols)));
        filterColumns.addAll(numCols.subList(0, Math.min(numCols.size(), maxNumCols)));
        filterColumns.addAll(dateCols.subList(0, Math.min(dateCols.size(), maxDateCols)));

        return filterColumns;
    }

    public static List<List<ZiaRange>> getFilteredSplitColumns(ZSZiaEssentials essentials, List<List<ZiaRange>> tableRange) {
        List<List<ZiaRange>> splitColumns = new ArrayList<>();

        List<List<ZiaRange>> catCols = new ArrayList<>();
        List<List<ZiaRange>> numCols = new ArrayList<>();
        List<List<ZiaRange>> dateCols = new ArrayList<>();

        for(List<ZiaRange> colRange : tableRange){
            ZiaRange firstSpiltRange = colRange.get(0);
            int colIndex = firstSpiltRange.getStartColIndex();
            ZiaConstants.ColumnType columnType = identifyColumnType(colIndex, firstSpiltRange, essentials);

            if (columnType.equals(ZiaConstants.ColumnType.CATEGORICAL)) {
                catCols.add(colRange);
            } else if (columnType.equals(ZiaConstants.ColumnType.NUMERICAL)) {
                numCols.add(colRange);
            } else if (columnType.equals(ZiaConstants.ColumnType.DATE)) {
                dateCols.add(colRange);
            }
        }
        Map<ZiaConstants.ColumnType, List<Integer>> colMap = new HashMap<>() {{
            put(ZiaConstants.ColumnType.CATEGORICAL, IntStream.range(0, catCols.size()).boxed().collect(Collectors.toList()));
            put(ZiaConstants.ColumnType.NUMERICAL, IntStream.range(0, numCols.size()).boxed().collect(Collectors.toList()));
        }};

        ZiaConstants.DataSetNature dataNature = ZiaUtilities.getDataSetNatureBased(colMap);
        ZSZiaConstants.colLimit colLimiter = ZSZiaConstants.COL_LIMIT_MAP.get(dataNature);

        int maxCatCols = colLimiter.getMaxCatCols();
        int maxDateCols = colLimiter.getMaxDateCols();
        int maxNumCols = colLimiter.getMaxNumCols();

        splitColumns.addAll(tableRange.subList(0, Math.min(catCols.size(), maxCatCols)));
        splitColumns.addAll(tableRange.subList(0, Math.min(numCols.size(), maxNumCols)));
        splitColumns.addAll(tableRange.subList(0, Math.min(dateCols.size(), maxDateCols)));

        return splitColumns;
    }

    /*
    * Iterate the first 5 cells and determine the columnType - used for filtering columns before columnHolders are created
    * */
    private static ZiaConstants.ColumnType identifyColumnType(int colIndex, ZiaRange tableRange, ZSZiaEssentials essentials) {

        //Datatype count order - Numerical, DateTypes, Categorical, Undefined/Error types
        final List<Integer> dtypeCounts = new ArrayList<>() {{
            add(0); //numCount
            add(0); //dateCount
            add(0); //catCount
            add(0); //undefined
        }};

        Consumer<Cell.Type> checkCellType = (Cell.Type type) -> {
            switch (type) {
                case FLOAT:
                case SCIENTIFIC:
                case FRACTION:
                case PERCENTAGE:
                case CURRENCY:
                    dtypeCounts.set(0, dtypeCounts.get(0) + 1);
                    break;
                case DATE:
                case TIME:
                case DATETIME:
                    dtypeCounts.set(1, dtypeCounts.get(1) + 1);
                    break;
                case STRING:
                case BOOLEAN:
                    dtypeCounts.set(2, dtypeCounts.get(2) + 1);
                    break;
                default:
                    dtypeCounts.set(3, dtypeCounts.get(3) + 1);
                    break;
            }
        };

        boolean isFrl = essentials.isFirstRecordAsLabel();
        int startRowIndex = tableRange.getStartRowIndex();
        startRowIndex = isFrl ? startRowIndex++ : startRowIndex;

        int endRowIndex = Math.min(tableRange.getEndRowIndex(), ZSZiaConstants.MAX_ROWS_TO_CHECK);
        endRowIndex = isFrl ? endRowIndex++ : endRowIndex;

        Sheet sheet = essentials.getSheet();
        for (int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {

            Cell cell = sheet.getCell(rowIndex, colIndex);

            if (cell != null) {
                if (cell.isImage()) {
                    dtypeCounts.set(3, dtypeCounts.get(3) + 1);
                } else if (sheet.isCoveredUnderMerge(rowIndex, colIndex)) {
                    checkCellType.accept(cell.getType());
                } else if (isEmpty(cell)) {
                    dtypeCounts.set(3, dtypeCounts.get(3) + 1);
                } else {
                    if (cell.getType() != Cell.Type.ERROR) {
                        checkCellType.accept(cell.getType());
                    }
                }
            }
        }

        ZiaConstants.ColumnType[] colTypes = new ZiaConstants.ColumnType[]{ZiaConstants.ColumnType.NUMERICAL, ZiaConstants.ColumnType.DATE,
                ZiaConstants.ColumnType.CATEGORICAL, ZiaConstants.ColumnType.UNDEFINED};    //same order as dType count list

        return colTypes[dtypeCounts.indexOf(Collections.max(dtypeCounts))];
    }

    public static List<Integer> generateSampleSplitRows(List<Integer> rowIndices, int colSize){
        List<Integer> sampledIndices = generateSampleRowIndcies(0, rowIndices.size(), colSize);

        List<Integer> newRows = new ArrayList<>(sampledIndices.size());

        for(int index: sampledIndices) {
            newRows.add(index, rowIndices.get(index));
        }
        return newRows;
    }

    public static List<Integer> generateSampleRowIndcies(int startRow, int endRow, int colSize) {
        /* example
         * startRow = 0
         * endRow = 15,00,000
         * col size = 5
         * maxRowSize = 10,00,000
         * each row part will have 33K rows
         * first part from 0 to 33K
         * mid part from 58K to 91K
         * last part from 117K to 150K
         * */

        List<Integer> rowIndices = new ArrayList<>();
        int maxCellCount = ZSZiaConstants.MAX_ZIA_CELL_LIMIT;

        int splitRatio = 3;
        int neededRows = maxCellCount / colSize;
        int rowPart = neededRows / splitRatio;

        //first row part
        rowIndices.addAll(IntStream.rangeClosed(startRow, rowPart).boxed().collect(Collectors.toList()));

        //last part
        int preEndIndex = endRow - rowPart;
        rowIndices.addAll(IntStream.rangeClosed(preEndIndex, endRow).boxed().collect(Collectors.toList()));

        //middle part
        int midRowindex = (startRow + endRow) / 2;
        rowIndices.addAll(IntStream.rangeClosed(midRowindex - (rowPart / 2), midRowindex + (rowPart / 2)).boxed().collect(Collectors.toList()));
        return rowIndices;
    }

    public static boolean isEmpty(Cell cell) {
        Predicate<Cell> emptyValuePredicate = (c) -> c.getValue().getValue() == " ";
        return EmptyCellPredicates.EMPTY_CELL_PREDICATE1.test(cell) || emptyValuePredicate.test(cell);
    }

    public static JSONObjectWrapper getSuggestionMeta(InsightSuggestion suggestion) {
        RazChartMeta chartMeta = ZSRazUtilities.insightToRazChart(suggestion);
        return chartMeta.toJson();
    }

    public static String makeZiaHolderId(ZSZiaHolder ziaholder) {
        String zuid = IAMUtil.getCurrentUser().getZuid();
        String rid = IAMUtil.getCurrentRequest().getParameter(ZSheetOpenAiAction.RID);
        String associatedSheetName = ziaholder.getEssentials().getSheet().getAssociatedName();

        ZiaTableDataHolder tableDataHolder = ziaholder.getTableDataHolder();
        List<ZiaRange> tableRanges = tableDataHolder.getTableRange();
        String rangeStr = tableRanges.stream().map(ZiaRange::toString).collect(Collectors.joining());

        return rid + ZSZiaConstants.DB_DELIMITER + zuid + ZSZiaConstants.DB_DELIMITER + associatedSheetName + ZSZiaConstants.DB_DELIMITER + rangeStr; //ex. wqdahfg + 1234 + 0# + A1:B1
    }
}
