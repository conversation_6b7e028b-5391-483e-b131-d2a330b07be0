package com.zoho.sheet.sheetzia;

import com.zoho.sheet.sheetzia.interfaces.ZiaExecutorService;
import com.zoho.sheet.zia2.interfaces.ZiaTask;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */

public class ZiaExecutorServiceImpl implements ZiaExecutorService {
    private static final ExecutorService EXECUTOR_SERVICE = Executors.newFixedThreadPool(2);

    @Override
    public void executeAll(List<ZiaTask> ziaTaskList) throws Exception {
        for (ZiaTask ziaTask : ziaTaskList) {
            execute(ziaTask);
        }
    }

    @Override
    public List<Future<?>> executeAsyncAll(List<ZiaTask> ziaTaskList) {
        List<Future<?>> futures = new ArrayList<>();
        for (ZiaTask ziaTask : ziaTaskList) {
            futures.add(executeAsync(ziaTask));
        }
        return futures;
    }

    @Override
    public void awaitAllAsyncTask(List<Future<?>> futureList) throws ExecutionException, InterruptedException {
        for (Future<?> future : futureList) {
            future.get();
        }
    }

    @Override
    public void awaitAsyncTask(Future<?> future) throws ExecutionException, InterruptedException {
        future.get();
    }

    @Override
    public void execute(ZiaTask ziaTask) throws Exception {
        ziaTask.run();
    }

    @Override
    public Future<?> executeAsync(ZiaTask ziaTask) {
        return EXECUTOR_SERVICE.submit(ziaTask);
    }
}
