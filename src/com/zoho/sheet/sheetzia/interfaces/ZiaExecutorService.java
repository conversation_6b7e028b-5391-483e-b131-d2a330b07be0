package com.zoho.sheet.sheetzia.interfaces;

import com.zoho.sheet.zia2.interfaces.ZiaTask;
import com.zoho.sheet.zia2.utils.exceptions.ZiaException;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */

public interface ZiaExecutorService {

    void executeAll(List<ZiaTask> ziaTaskList) throws Exception;

    List<Future<?>> executeAsyncAll(List<ZiaTask> ziaTaskList);

    void awaitAllAsyncTask(List<Future<?>> futureList) throws ExecutionException, InterruptedException, ZiaException;

    void awaitAsyncTask(Future<?> future) throws ExecutionException, InterruptedException, ZiaException;

    void execute(ZiaTask ziaTask) throws Exception;

    Future<?> executeAsync(ZiaTask ziaTask);

}
