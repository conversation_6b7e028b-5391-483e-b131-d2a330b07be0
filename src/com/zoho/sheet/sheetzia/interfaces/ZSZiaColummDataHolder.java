package com.zoho.sheet.sheetzia.interfaces;

import com.zoho.nlp.datamodel.meta.NLPColumn;
import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.utils.ZiaContentVO;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> - 13900
 */

public interface ZSZiaColummDataHolder extends ZiaColumnDataHolder {

    Map<ZiaContentVO.Type, Integer> getDataTypeCount();

    Set<Integer> getImageIndices();

    NLPColumn toRazColumn();
}
