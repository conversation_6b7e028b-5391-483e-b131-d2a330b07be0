package com.zoho.sheet.sheetzia.dataType;

import com.zoho.sheet.sheetzia.interfaces.ZSZiaColummDataHolder;
import com.zoho.sheet.zia2.utils.ZiaContentVO;

import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;

public class ZiaDTypeUtils {
    public static final class PREDICATES {
        public static final Predicate<ZSZiaColummDataHolder> IS_DROP_COLUMN = (columnDataHolder) -> {
            Map<ZiaContentVO.Type, Integer> dataTypeCount = getDataTypeCount(columnDataHolder);
            int rowSize = columnDataHolder.getRowIndices().size();

            int errorCells = Optional.ofNullable(dataTypeCount.get(ZiaContentVO.Type.UNDEFINED)).orElse(0);
            int emptyCells = columnDataHolder.getMissingIndices().size();
            int imageCells = columnDataHolder.getImageIndices().size();
            int miscCells = emptyCells + imageCells + errorCells;

            return findRatio(miscCells, rowSize) >= DTypeConstants.DROP_RATIO;
        };

        public static final Predicate<ZSZiaColummDataHolder> IS_SINGLE_TYPE = (columnDataHolder) -> getDataTypeCount(columnDataHolder).size() == 1;

        public static final Predicate<ZSZiaColummDataHolder> IS_DOUBLE_TYPE = (columnDataHolder) -> getDataTypeCount(columnDataHolder).size() == 2;

        public static final Predicate<ZSZiaColummDataHolder> HAS_NUMERICAL_TYPE = (columnDataHolder) -> {
            Map<ZiaContentVO.Type, Integer> dataTypeCount = getDataTypeCount(columnDataHolder);

            return DTypeConstants.NUMERICAL_TYPES.stream()
                    .anyMatch(dataTypeCount::containsKey);
        };

        public static final Predicate<ZSZiaColummDataHolder> HAS_DATE_TYPE = (columnDataHolder) -> {
            Map<ZiaContentVO.Type, Integer> dataTypeCount = getDataTypeCount(columnDataHolder);

            return DTypeConstants.DATE_TYPES.stream()
                    .anyMatch(dataTypeCount::containsKey);
        };

        public static final Predicate<ZSZiaColummDataHolder> HAS_TIME_TYPE = (columnDataHolder) -> {
            Map<ZiaContentVO.Type, Integer> dataTypeCount = getDataTypeCount(columnDataHolder);

            return dataTypeCount.containsKey(ZiaContentVO.Type.TIME);
        };

        public static final Predicate<ZSZiaColummDataHolder> HAS_CAT_TYPE = (columnDataHolder) -> {
            Map<ZiaContentVO.Type, Integer> dataTypeCount = getDataTypeCount(columnDataHolder);

            return DTypeConstants.CAT_TYPES.stream()
                    .anyMatch(dataTypeCount::containsKey);
        };

        private static Map<ZiaContentVO.Type, Integer> getDataTypeCount(ZSZiaColummDataHolder columnDataHolder) {
            return columnDataHolder.getDataTypeCount();
        }
    }

    public static double findRatio(int colCount, int rowCount) {
        return (double) colCount / rowCount * 100;
    }

    public static int getTotalNumericalCells(Map<ZiaContentVO.Type, Integer> dataTypeCount) {
        int totalNumericalCells = 0;
        for (ZiaContentVO.Type type : DTypeConstants.NUMERICAL_TYPES) {
            totalNumericalCells += dataTypeCount.getOrDefault(type, 0);
        }
        return totalNumericalCells;
    }

    public static int getTotalDateCells(Map<ZiaContentVO.Type, Integer> dataTypeCount) {
        int totalDateCells = 0;
        for (ZiaContentVO.Type type : DTypeConstants.DATE_TYPES) {
            totalDateCells += dataTypeCount.getOrDefault(type, 0);
        }
        return totalDateCells;
    }
}
