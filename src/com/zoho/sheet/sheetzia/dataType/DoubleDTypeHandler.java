package com.zoho.sheet.sheetzia.dataType;

import com.zoho.sheet.sheetzia.interfaces.ZSZiaColummDataHolder;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaContentVO;

import java.util.Map;

/**
 * <AUTHOR> - 13900
 */

class DoubleDTypeHandler {
    private final Map<ZiaContentVO.Type, Integer> dataTypeCount;
    private final int rowSize;

    public DoubleDTypeHandler(ZSZiaColummDataHolder columnDataHolder) {
        this.dataTypeCount = columnDataHolder.getDataTypeCount();
        this.rowSize = columnDataHolder.getRowIndices().size();
    }

    public ZiaConstants.ColumnType numCatHandler() {
        int numericalCellCount = ZiaDTypeUtils.getTotalNumericalCells(dataTypeCount);
        double ratio = ZiaDTypeUtils.findRatio(numericalCellCount, rowSize);
        return ratio >= DTypeConstants.NUMCAT_RATIO ? ZiaConstants.ColumnType.NUMERICAL : ZiaConstants.ColumnType.CATEGORICAL;
    }

    public ZiaConstants.ColumnType numDateHandler() {
        int numericalCellCount = ZiaDTypeUtils.getTotalNumericalCells(dataTypeCount);
        double ratio = ZiaDTypeUtils.findRatio(numericalCellCount, rowSize);
        return ratio >= DTypeConstants.NUMDATE_RATIO ? ZiaConstants.ColumnType.NUMERICAL : ZiaConstants.ColumnType.DATE;
    }

    public ZiaConstants.ColumnType numTimeHandler() {
        int numericalCellCount = ZiaDTypeUtils.getTotalNumericalCells(dataTypeCount);
        double ratio = ZiaDTypeUtils.findRatio(numericalCellCount, rowSize);
        return ratio >= DTypeConstants.NUMTIME_RATIO ? ZiaConstants.ColumnType.NUMERICAL : ZiaConstants.ColumnType.TIME;
    }

    public ZiaConstants.ColumnType dateCatHandler() {
        int dateCellCount = ZiaDTypeUtils.getTotalDateCells(dataTypeCount);
        double ratio = ZiaDTypeUtils.findRatio(dateCellCount, rowSize);
        return ratio >= DTypeConstants.DATCAT_RATIO ? ZiaConstants.ColumnType.DATE : ZiaConstants.ColumnType.CATEGORICAL;
    }

    public ZiaConstants.ColumnType dateTimeHandler() {
        int dateCellCount = ZiaDTypeUtils.getTotalDateCells(dataTypeCount);
        double ratio = ZiaDTypeUtils.findRatio(dateCellCount, rowSize);
        return ratio >= DTypeConstants.DATTIME_RATIO ? ZiaConstants.ColumnType.DATE : ZiaConstants.ColumnType.TIME;
    }
}

