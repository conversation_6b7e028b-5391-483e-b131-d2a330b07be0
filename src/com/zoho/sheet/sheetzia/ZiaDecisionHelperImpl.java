package com.zoho.sheet.sheetzia;

import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaDecisionHelper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class ZiaDecisionHelperImpl implements ZiaDecisionHelper {

    private final Sheet sheet;

    public ZiaDecisionHelperImpl(Sheet sheet) {
        this.sheet = sheet;
    }

    @Override
    public boolean isCheckBoxOverlapped(ZiaColumnDataHolder columnDataHolder) {
        List<DataRange> checkboxRangeList = sheet.getCheckboxRangeList();
        for (DataRange cRange : checkboxRangeList) {
            List<Range> zsRanges = ZSZiaUtilities.toZSRange(sheet, columnDataHolder.getColumnContentRange());

            for (Range cZSRange : zsRanges) {
                if (cRange.isInRange(cZSRange.toDataRange())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean isDropdownOverlapped(ZiaColumnDataHolder columnDataHolder) {

        Map<Integer, List<DataRange>> picklistRangeMap = sheet.getPicklistRangeMap();
        for (Map.Entry<Integer, List<DataRange>> mapELe : picklistRangeMap.entrySet()) {
            List<DataRange> pickListRanges = mapELe.getValue();

            for (DataRange pRange : pickListRanges) {
                List<Range> pZSRanges = ZSZiaUtilities.toZSRange(sheet, columnDataHolder.getColumnContentRange());

                for (Range pZSRange : pZSRanges) {
                    if (pRange.isInRange(pZSRange.toDataRange())) {
                        return true;
                    }
                }
            }
        }

        // check if it collides with the present data validation ranges
        List<DataRange> contentValidationRanges = sheet.getContentValidationRanges();
        for (DataRange cRange : contentValidationRanges) {
            List<Range> cZSRanges = ZSZiaUtilities.toZSRange(sheet, columnDataHolder.getColumnContentRange());

            for (Range cZSRange : cZSRanges) {
                if (cRange.isInRange(cZSRange.toDataRange())) {
                    return true;
                }
            }
        }

        return false;
    }
}
