package com.zoho.sheet.sheetzia.table;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.util.EmptyCellPredicates;
import com.zoho.nlp.datamodel.meta.NLPColumn;
import com.zoho.sheet.sheetzia.ZSZiaEssentials;
import com.zoho.sheet.sheetzia.ZSZiaUtilities;
import com.zoho.sheet.sheetzia.dataType.ZiaDTypeIdentifier;
import com.zoho.sheet.sheetzia.interfaces.ZSZiaColummDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaColumnIterator;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaContentVO;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.range.ZiaCell;
import com.zoho.sheet.zia2.utils.ziabeans.conversions.ZiaConversionEssentials;

import java.util.*;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */

public class ZiaSplitColumnDataHolderImpl implements ZSZiaColummDataHolder {
    private final Sheet sheet;
    private final List<ZiaRange> columnRanges;
    private final List<ZiaRange> contentRanges;
    private final String headerName;
    private final List<Integer> selectedIndices;
    private final ZSZiaEssentials.SeriesIn seriesIn;
    private final int maxUsedRows;

    private final Set<Integer> imageIndices;
    private final Set<Integer> mergedCellIndices;
    private final Set<Integer> missingValueIndices;
    private final LinkedHashMap<String, Set<Integer>> uniqueValuesIndicesMap;
    private final Map<Integer, ZiaContentVO> contentMap;
    private final Map<ZiaContentVO.Type, Integer> dataTypeCount;
    private final ZiaConversionEssentials conversionEssentials = new ZiaConversionEssentials();

    private ZiaConstants.ColumnType columnType;

    public ZiaSplitColumnDataHolderImpl(Sheet sheet, List<ZiaRange> columnRanges, List<ZiaRange> contentRanges, String headerName, List<Integer> selectedIndices, ZSZiaEssentials.SeriesIn seriesIn) {
        //TODO: remove conversion and other deps - compare with normal column builder
        this.sheet = sheet;
        this.columnRanges = columnRanges;
        this.contentRanges = contentRanges;
        this.headerName = headerName;
        this.selectedIndices = selectedIndices;
        this.seriesIn = seriesIn;

        this.imageIndices = new LinkedHashSet<>();
        this.mergedCellIndices = new LinkedHashSet<>();
        this.missingValueIndices = new LinkedHashSet<>();
        this.dataTypeCount = new HashMap<>();
        this.uniqueValuesIndicesMap = new LinkedHashMap<>();
        this.contentMap = new HashMap<>();

        this.maxUsedRows = initiateContent();
        this.columnType = ZiaDTypeIdentifier.detectDatatype(this);
    }

    private int initiateContent() {
        int maxUsedRowsIndex = 0;

        HashSet<Integer> selectedIndices = new HashSet<>(this.selectedIndices);
        for (ZiaRange contentRange : contentRanges) {
            maxUsedRowsIndex = contentRange.getStartRowIndex();
            boolean isSeriesInRow = seriesIn == ZSZiaEssentials.SeriesIn.ROWS;

            for (ZiaCell ziaCell : contentRange) {
                int rowIndex = ziaCell.getRowIndex();
                int colIndex = ziaCell.getColIndex();
                int index = isSeriesInRow ? colIndex : rowIndex;

                if (!selectedIndices.contains(index)) {
                    continue;
                }

                Cell cell = sheet.getCell(rowIndex, colIndex);
                ZiaContentVO content = null;

                if (Objects.nonNull(cell)) {
                    if (cell.isImage()) {
                        this.imageIndices.add(index);
                    } else if (sheet.isCoveredUnderMerge(rowIndex, colIndex)) {
                        this.mergedCellIndices.add(index);
                    } else if (ZSZiaUtilities.isEmpty(cell)) {
                        this.missingValueIndices.add(index);
                    } else {
                        if (cell.getType() != Cell.Type.ERROR) {
                            content = ZSZiaUtilities.getZiaContentVO(cell);
                        }
                    }
                }

                if (Objects.nonNull(content)) {
                    contentMap.put(index, content);
                    String actualContent = content.getContent();

                    Set<Integer> indices = uniqueValuesIndicesMap.getOrDefault(actualContent, new HashSet<>());
                    indices.add(rowIndex);

                    uniqueValuesIndicesMap.put(actualContent, indices);
                    maxUsedRowsIndex = rowIndex;

                    updateCellTypeCount(content.getCellType());
                }
            }
        }

        return maxUsedRowsIndex;
    }

    @Override
    public int getColumnIndex() {
        ZiaRange ziaRange = this.columnRanges.get(0);
        return this.seriesIn == ZSZiaEssentials.SeriesIn.ROWS ? ziaRange.getStartRowIndex() : ziaRange.getStartColIndex();
    }

    @Override
    public void setColumnType(ZiaConstants.ColumnType columnType) {
        this.columnType = columnType;
    }

    @Override
    public ZiaConstants.ColumnType getColumnType() {
        return this.columnType;
    }

    @Override
    public String getHeaderName() {
        return this.headerName;
    }

    @Override
    public ZiaContentVO getContentVO(int rowIndex) {
        return this.contentMap.get(rowIndex);
    }

    @Override
    public LinkedHashMap<String, Set<Integer>> getUniqueValueIndicesMap() {
        return this.uniqueValuesIndicesMap;
    }

    @Override
    public int getUniqueValuesCount() {
        return this.getUniqueValueIndicesMap().size();
    }

    @Override
    public int getMiscellaneousCellCount() {
        return this.mergedCellIndices.size() + this.missingValueIndices.size() + this.imageIndices.size();
    }

    @Override
    public Set<Integer> getMissingIndices() {
        return this.missingValueIndices;
    }

    @Override
    public List<Integer> getRowIndices() {
        return this.selectedIndices;
    }

    @Override
    public List<ZiaRange> getColumnContentRange() {
        return this.contentRanges;
    }

    @Override
    public List<ZiaRange> getColumnRange() {
        return this.columnRanges;
    }

    @Override
    public ZiaConversionEssentials getConversionEssentials() {
        return this.conversionEssentials;
    }

    @Override
    public ZiaColumnIterator getColumnIterator() {
        return new ZiaColumnIteratorImpl(this);
    }

    @Override
    public boolean isPriorityCol() {
        //TODO: implement when priorities are given in zia
        return false;
    }

    @Override
    public Map<ZiaContentVO.Type, Integer> getDataTypeCount() {
        return this.dataTypeCount;
    }

    @Override
    public Set<Integer> getImageIndices() {
        return this.imageIndices;
    }

    @Override
    public NLPColumn toRazColumn() {
        //TODO: support multirange support in raz
        return null;
    }

    private void updateCellTypeCount(ZiaContentVO.Type cellType) {
        Integer typeCount = dataTypeCount.getOrDefault(cellType, 0);
        dataTypeCount.put(cellType, typeCount + 1);
    }

    @Override
    public String toString() {
        return this.headerName;
    }
}
