package com.zoho.sheet.sheetzia.table;

import com.adventnet.zoho.websheet.model.Sheet;
import com.zoho.nlp.datamodel.meta.NLPTable;
import com.zoho.sheet.sheetzia.ZSZiaConstants;
import com.zoho.sheet.sheetzia.ZSZiaEssentials;
import com.zoho.sheet.sheetzia.ZSZiaUtilities;
import com.zoho.sheet.sheetzia.builders.ZiaSplitColumnDataHolderBuilder;
import com.zoho.sheet.sheetzia.interfaces.ZSZiaTableDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaParams;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.ZiaUtilities;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */

public class ZiaSplitTableDataHolderImpl implements ZSZiaTableDataHolder {
    /*  supports split ranges in zia and transposed data tables
    * used in auto chart recommendation currently
    * */

    private final List<ZiaRange> tableRanges;
    private final List<ZiaRange> tableContentRanges;
    private final List<ZiaColumnDataHolder> splitColumnDataHolders;
    private final List<Integer> actualRowIndices;
    private final Map<Integer, Integer> actualIndexMap;
    private final Map<String, Integer> actualHeaderMap;
    private final Map<String, Integer> csvHeaderMap;
    private final int rowSize;
    private final int colSize;

    public ZiaSplitTableDataHolderImpl(ZiaParams params, ZSZiaEssentials essentials) {
        this.tableRanges = params.getTableRanges();
        this.tableContentRanges = toContentRange(this.tableRanges, essentials.isFirstRecordAsLabel(), essentials.isSeriesInRow());

        int[] tableDimension = getTableDimension(essentials, params.getTableRanges());
        this.rowSize = tableDimension[0];
        this.colSize = tableDimension[1];

        List<List<ZiaRange>> colRanges = tableRangeToColRange(params.getTableRanges(), essentials.getAppendingWay(), essentials.isSeriesInRow());
        this.actualRowIndices = initiateActualRowIndices(colRanges, essentials);
        this.splitColumnDataHolders = createColumnHolders(colRanges, actualRowIndices, essentials);
        this.actualIndexMap = getActualIndexMap();
        this.actualHeaderMap = getActualHeaderMap();
        this.csvHeaderMap = getCSVHeaderMap();

    }

    @Override
    public ZiaColumnDataHolder getColumnDataHolderByIndex(int colIndex) {
        return this.getColumnDataHolder(this.actualIndexMap.get(colIndex));
    }

    @Override
    public ZiaColumnDataHolder getColumnDataHolderByHeader(String headerName) {
        int colIndex = -1;

        if (actualHeaderMap.containsKey(headerName)) {
            colIndex = actualHeaderMap.get(headerName);
        } else if (csvHeaderMap.containsKey(headerName)) {
            colIndex = csvHeaderMap.get(headerName);
        }

        if (colIndex == -1) {
            return null;
        }
        return getColumnDataHolder(colIndex);
    }

    @Override
    public List<ZiaColumnDataHolder> getAllColumnDataHolders() {
        return this.splitColumnDataHolders;
    }

    @Override
    public void setColumnType(int index, ZiaConstants.ColumnType colType) {
        ZiaColumnDataHolder columnDataHolder = this.splitColumnDataHolders.get(index);
        columnDataHolder.setColumnType(colType);
    }

    @Override
    public List<Integer> getRowIndices() {
        return this.actualRowIndices;
    }

    @Override
    public String getHeaderNameOfColumn(int actualColIndex) {
        ZiaColumnDataHolder columnDataHolder = getColumnDataHolderByIndex(actualColIndex);
        return Objects.isNull(columnDataHolder) ? "" : columnDataHolder.getHeaderName();
    }

    @Override
    public File getAsCsvFile() throws IOException {
        return ZiaUtilities.convertColumnDataHolderToCsv(this.getAllColumnDataHolders(), actualRowIndices);
    }

    @Override
    public List<ZiaRange> getTableRange() {
        return this.tableRanges;
    }

    @Override
    public List<ZiaRange> getTableContentRange() {
        return this.tableContentRanges;
    }

    @Override
    public Double getUniqueValueRatio(int actualColIndex) {
        ZiaColumnDataHolder columnDataHolder = getColumnDataHolderByIndex(actualColIndex);
        return (double) columnDataHolder.getUniqueValuesCount() / this.getContentRowSize();
    }

    @Override
    public Double getMiscellaneousValueRatio(int actualColIndex) {
        ZiaColumnDataHolder columnDataHolder = getColumnDataHolderByIndex(actualColIndex);
        return (double) columnDataHolder.getMiscellaneousCellCount() / this.getContentRowSize();
    }

    @Override
    public int getRowSize() {
        return this.rowSize;
    }

    @Override
    public int getContentRowSize() {
        return this.rowSize;
    }

    @Override
    public int getColSize() {
        return this.colSize;
    }

    @Override
    public Iterator<ZiaColumnDataHolder> iterator() {
        return this.splitColumnDataHolders.iterator();
    }

    private List<Integer> initiateActualRowIndices(List<List<ZiaRange>> colRangesGroup, ZSZiaEssentials essentials) {
        Sheet sheet = essentials.getSheet();
        List<Integer> rowIndices = new ArrayList<>();

        if (colRangesGroup.isEmpty()) {
            return rowIndices;
        }

        List<ZiaRange> firstColGroup = colRangesGroup.get(0);
        boolean skipHiddenRows = essentials.isSkipHiddenRows();
        boolean seriesInRow = essentials.isSeriesInRow();
        boolean firstRecordAsLabel = essentials.isFirstRecordAsLabel();

        for (int i = 0; i < firstColGroup.size(); i++) {
            ZiaRange colSplitRange = firstColGroup.get(i);
            List<Integer> collect;
            if (seriesInRow) {
                int startColIndex = colSplitRange.getStartColIndex();
                if (i == 0 && firstRecordAsLabel) {
                    startColIndex++;
                }

                int endColIndex = Math.min(colSplitRange.getEndColIndex(), sheet.getUsedColumnIndex());
                IntStream intStream = IntStream.rangeClosed(startColIndex, endColIndex);
                if (skipHiddenRows) {
                    intStream = intStream.filter(sheet::isColumnVisible);
                }
                collect = intStream.boxed().collect(Collectors.toList());
            } else {
                int startRowIndex = colSplitRange.getStartRowIndex();
                if (i == 0 && firstRecordAsLabel) {
                    startRowIndex++;
                }

                int endRowIndex = Math.min(colSplitRange.getEndRowIndex(), sheet.getUsedRowIndex());
                IntStream intStream = IntStream.rangeClosed(startRowIndex, endRowIndex);
                if (skipHiddenRows) {
                    intStream = intStream.filter(sheet::isRowVisible);
                }
                collect = intStream.boxed().collect(Collectors.toList());
            }

            rowIndices.addAll(collect);
        }

        return rowIndices;
    }

    private ZiaColumnDataHolder getColumnDataHolder(int relativeIndex) {
        return this.splitColumnDataHolders.get(relativeIndex);
    }

    private Map<Integer, Integer> getActualIndexMap() {
        Map<Integer, Integer> map = new HashMap<>();

        for (int i = 0; i < splitColumnDataHolders.size(); i++) {
            ZiaColumnDataHolder ziaColumnDataHolder = splitColumnDataHolders.get(i);
            map.put(ziaColumnDataHolder.getColumnIndex(), i);
        }

        return map;
    }

    private Map<String, Integer> getActualHeaderMap() {
        Map<String, Integer> map = new HashMap<>();

        for (int i = 0; i < splitColumnDataHolders.size(); i++) {
            map.put(splitColumnDataHolders.get(i).getHeaderName(), i);
        }

        return map;
    }

    private Map<String, Integer> getCSVHeaderMap() {
        Map<String, Integer> headerMap = new HashMap<>();

        for (int i = 0; i < splitColumnDataHolders.size(); i++) {
            String headerName = splitColumnDataHolders.get(i).getHeaderName();
            headerName = headerName.replaceAll(ZiaUtilities.REPLACE_PATTERN, "");
            headerMap.put(headerName, i);
        }

        return headerMap;
    }

    private List<ZiaColumnDataHolder> createColumnHolders(List<List<ZiaRange>> colRanges, List<Integer> actualRowIndices, ZSZiaEssentials essentials) {
        List<ZiaColumnDataHolder> columnDataHolders = new ArrayList<>();

        Sheet sheet = essentials.getSheet();
        boolean seriesInRow = essentials.isSeriesInRow();
        boolean firstRecordAsLabel = essentials.isFirstRecordAsLabel();
        ZSZiaEssentials.SeriesIn seriesIn = essentials.getSeriesIn();

        int colSize = colRanges.size();
        int rowSize = actualRowIndices.size();

        if(colSize > ZSZiaConstants.MAX_COLS_LIMIT){
            colRanges = ZSZiaUtilities.getFilteredSplitColumns(essentials, colRanges);
        }

        if(rowSize > ZSZiaConstants.MAX_ZIA_CELL_LIMIT){
            actualRowIndices = ZSZiaUtilities.generateSampleSplitRows(actualRowIndices, colRanges.size());
        }

        for (List<ZiaRange> colRange : colRanges) {
            List<ZiaRange> contentRange = getContentRange(colRange, firstRecordAsLabel, seriesInRow);
            ZiaRange firstRange = colRange.get(0);
            String header = ZSZiaUtilities.getHeader(sheet, firstRange, firstRecordAsLabel, seriesInRow);

            ZiaSplitColumnDataHolderBuilder builder = new ZiaSplitColumnDataHolderBuilder();
            ZiaSplitColumnDataHolderImpl splitColumnDataHolder = builder
                    .setSeriesIn(seriesIn)
                    .setHeaderName(header)
                    .setContentRanges(contentRange)
                    .setSelectedIndices(actualRowIndices)
                    .createZiaSplitColumnDataHolderImpl(sheet, colRange);

            columnDataHolders.add(splitColumnDataHolder);
        }


        return columnDataHolders;
    }

    private List<ZiaRange> getContentRange(List<ZiaRange> colRanges, boolean isFirstRecordAsLabel, boolean isSeriesInRow) {
        List<ZiaRange> contentRanges = new ArrayList<>();
        for (ZiaRange colRange : colRanges) {
            if (isFirstRecordAsLabel) {
                int rowIndex = colRange.getStartRowIndex();
                int colIndex = colRange.getStartColIndex();

                if (isSeriesInRow) {
                    colIndex++;
                } else {
                    rowIndex++;
                }

                contentRanges.add(new ZiaRange(rowIndex, colIndex, colRange.getEndRowIndex(), colRange.getEndColIndex()));
            } else {
                contentRanges.add(colRange);
            }
        }

        return contentRanges;
    }

    private static int[] getTableDimension(ZSZiaEssentials essentials, List<ZiaRange> tableRanges) {
        Sheet sheet = essentials.getSheet();
        boolean firstRecordAsLabel = essentials.isFirstRecordAsLabel();
        boolean isSeriesInCols = essentials.getSeriesIn() == ZSZiaEssentials.SeriesIn.COLS;
        boolean isAppendVertical = essentials.getAppendingWay() == ZSZiaEssentials.AppendingWay.VERTICAL;

        int rowSize = 0;
        int colSize = 0;

        if (isAppendVertical) {
            for (ZiaRange tableRange : tableRanges) {
                rowSize += Math.min(tableRange.getRowSize(), sheet.getUsedRowIndex());
            }

            colSize = Math.min(tableRanges.get(0).getColSize(), sheet.getUsedColumnIndex());
        }

        if (!isAppendVertical) {
            for (ZiaRange tableRange : tableRanges) {
                colSize += Math.min(tableRange.getColSize(), sheet.getUsedColumnIndex());
            }

            rowSize = Math.min(tableRanges.get(0).getRowSize(), sheet.getUsedRowIndex());
        }

        if (firstRecordAsLabel) {
            if (isSeriesInCols) {
                rowSize--;
            } else {
                colSize--;
            }
        }

        return isSeriesInCols ? new int[]{rowSize, colSize} : new int[]{colSize, rowSize};
    }

    private static List<ZiaRange> toContentRange(List<ZiaRange> tableRanges, boolean isFirstRecordAsLabel, boolean isSeriesInRow) {
        if (!isFirstRecordAsLabel) {
            return tableRanges;
        }

        List<ZiaRange> contentTableRange = new ArrayList<>();
        for (int i = 0; i < tableRanges.size(); i++) {
            ZiaRange tableRange = tableRanges.get(i);
            if (i == 0) {
                ZiaRange firstRange = tableRanges.get(i);
                int startColIndex = firstRange.getStartColIndex();
                int startRowIndex = firstRange.getStartRowIndex();
                if (isSeriesInRow) {
                    startColIndex++;
                } else {
                    startRowIndex++;
                }

                contentTableRange.add(new ZiaRange(startRowIndex, startColIndex, firstRange.getEndRowIndex(), firstRange.getEndColIndex()));
            } else {
                contentTableRange.add(tableRange);
            }
        }

        return contentTableRange;
    }

    private static List<List<ZiaRange>> tableRangeToColRange(List<ZiaRange> tableRanges, ZSZiaEssentials.AppendingWay appendingWay, boolean isSeriesInRows) {
        List<List<ZiaRange>> tableAsColRanges = new ArrayList<>();

        for (int i = 0; i < tableRanges.size(); i++) {
            ZiaRange tableRange = tableRanges.get(i);

            List<ZiaRange> currColRanges = ZiaUtilities.toColRange(tableRange, isSeriesInRows);
            if (appendingWay == ZSZiaEssentials.AppendingWay.VERTICAL) {
                if (isSeriesInRows) {
                    currColRanges.forEach(temp -> createAndAppendColRange(tableAsColRanges, temp));
                } else {
                    appendColRange(tableAsColRanges, currColRanges);
                }
            } else {
                if (isSeriesInRows) {
                    appendColRange(tableAsColRanges, currColRanges);
                } else {
                    currColRanges.forEach(temp -> createAndAppendColRange(tableAsColRanges, temp));
                }
            }
        }

        return tableAsColRanges;
    }


    private static void appendColRange(List<List<ZiaRange>> tableAsColRanges, List<ZiaRange> currColRange) {
        for (int index = 0; index < currColRange.size(); index++) {
            ZiaRange ziaRange = currColRange.get(index);

            if (index < tableAsColRanges.size()) {
                List<ZiaRange> list = tableAsColRanges.get(index);
                list.add(ziaRange);
            } else {
                createAndAppendColRange(tableAsColRanges, ziaRange);
            }
        }
    }

    private static void createAndAppendColRange(List<List<ZiaRange>> tableAsColRanges, ZiaRange ziaRange) {
        List<ZiaRange> tempArr = new ArrayList<>();
        tempArr.add(ziaRange);
        tableAsColRanges.add(tempArr);
    }

// TODO: uncomment and implement before chart merge
//    private File getOptimisedCSVFile() throws IOException {
//        List<ZiaColumnDataHolder> columnDataHolderList = getAllColumnDataHolders();
//        List<ZiaColumnDataHolder> filteredColDataHolders = new ArrayList<>();
//
//        boolean hasMoreRows = this.actualRowIndices.size() >= (int) (Utility.MAXNUMOFROWS * 0.08);
//
//        if (columnDataHolderList.size() > 5 || hasMoreRows) {
//            for (ZiaColumnDataHolder columnDataHolder : columnDataHolderList) {
//                ZiaConstants.ColumnType dataType = columnDataHolder.getColumnType();
//                int actualColumnIndex = columnDataHolder.getColumnIndex();
//                double miscellaneousValueRatio = this.getMiscellaneousValueRatio(actualColumnIndex);
//                double uniqueValueRatio = this.getUniqueValueRatio(actualColumnIndex);
//
//                if (miscellaneousValueRatio > 0.5 || (dataType.equals(ZiaConstants.ColumnType.UNDEFINED) && uniqueValueRatio > 0.5)) {
//                    continue;
//                }
//
//                filteredColDataHolders.add(columnDataHolder);
//            }
//        } else {
//            filteredColDataHolders.addAll(columnDataHolderList);
//        }
//
//        List<Integer> sampleRowIndices;
//        if (hasMoreRows) {
//            sampleRowIndices = ZiaUtilities.generateListOfRandomInteger(0, this.actualRowIndices.size(), 5000)
//                    .stream().map(this.actualRowIndices::get)
//                    .collect(Collectors.toList());
//        } else {
//            sampleRowIndices = this.actualRowIndices;
//        }
//
//        return ZiaUtilities.convertColumnDataHolderToCsv(filteredColDataHolders, sampleRowIndices);
//    }

    public NLPTable toRazTable() {
        //TODO: implement multirange support for raz
        return null;
    }
}