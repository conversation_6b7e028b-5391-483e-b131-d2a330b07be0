package com.zoho.sheet.sheetzia.table;

import com.adventnet.zoho.websheet.model.Sheet;
import com.zoho.sheet.sheetzia.ZSZiaUtilities;
import com.zoho.sheet.zia2.interfaces.ZiaTableProperties;
import com.zoho.sheet.zia2.utils.ZiaContentVO;
import com.zoho.sheet.zia2.utils.ZiaRange;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class ZiaTablePropertiesImpl extends ZiaTableProperties {
    private final ZiaRange tableRange;
    private final List<ZiaContentVO.Type> rowDataTypeList;
    private final List<ZiaContentVO.Type> colDataTypeList;

    public ZiaTablePropertiesImpl(Sheet sheet, ZiaRange tableRange) {
        this.tableRange = tableRange;
        this.rowDataTypeList = createRowDataTypeList(sheet, tableRange);
        this.colDataTypeList = createColDataTypeList(sheet, tableRange);
    }

    @Override
    public List<ZiaContentVO.Type> getRowDataTypeList() {
        return this.rowDataTypeList;
    }

    @Override
    public List<ZiaContentVO.Type> getColDataTypeList() {
        return this.colDataTypeList;
    }

    @Override
    public int getStartColIndex() {
        return tableRange.getStartColIndex();
    }

    @Override
    public int getStartRowIndex() {
        return tableRange.getStartRowIndex();
    }

    @Override
    public int getColSize() {
        return tableRange.getColSize();
    }

    @Override
    public int getRowSize() {
        return tableRange.getRowSize();
    }

    @Override
    public ZiaRange getRange() {
        return this.tableRange;
    }

    private List<ZiaContentVO.Type> createColDataTypeList(Sheet sheet, ZiaRange tableRange) {
        List<ZiaContentVO.Type> temp = new ArrayList<>();
        for (int colIndex = tableRange.getStartColIndex(); colIndex <= tableRange.getEndColIndex(); colIndex++) {
            Map<ZiaContentVO.Type, Integer> countMap = new HashMap<>();

            for (int rowIndex = tableRange.getStartRowIndex(); rowIndex <= tableRange.getEndRowIndex(); rowIndex++) {
                addToCountMap(sheet, colIndex, rowIndex, countMap);
            }

            ZiaContentVO.Type type = getDataTypeOnComparingCountNType(countMap);
            temp.add(type);
        }

        return temp;
    }

    private List<ZiaContentVO.Type> createRowDataTypeList(Sheet sheet, ZiaRange tableRange) {
        List<ZiaContentVO.Type> temp = new ArrayList<>();
        for (int rowIndex = tableRange.getStartRowIndex(); rowIndex <= tableRange.getEndRowIndex(); rowIndex++) {
            Map<ZiaContentVO.Type, Integer> countMap = new HashMap<>();

            for (int colIndex = tableRange.getStartColIndex(); colIndex <= tableRange.getEndColIndex(); colIndex++) {
                addToCountMap(sheet, rowIndex, colIndex, countMap);
            }

            ZiaContentVO.Type type = getDataTypeOnComparingCountNType(countMap);
            temp.add(type);
        }

        return temp;
    }

    private static void addToCountMap(Sheet sheet, int i, int j, Map<ZiaContentVO.Type, Integer> countMap) {
        ZiaContentVO ziaContentVO = ZSZiaUtilities.getZiaContentVO(sheet.getCell(i, j));
        ZiaContentVO.Type cellType = ziaContentVO.getCellType();
        Integer count = countMap.getOrDefault(cellType, 0);
        countMap.put(cellType, ++count);
    }

    private static ZiaContentVO.Type getDataTypeOnComparingCountNType(Map<ZiaContentVO.Type, Integer> countMap) {
        return countMap.entrySet().stream().sorted((i1, i2) -> {
            int compare = Integer.compare(i1.getValue(), i2.getValue());

            if (compare != 0) {
                return compare;
            }

            ZiaContentVO.Type type1 = i1.getKey();
            ZiaContentVO.Type type2 = i2.getKey();

            return type1.compareTo(type2);
        }).map(Map.Entry::getKey).findFirst().orElse(ZiaContentVO.Type.UNDEFINED);
    }
}
