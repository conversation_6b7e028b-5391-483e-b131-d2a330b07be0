package com.zoho.sheet.sheetzia;

import com.zoho.nlp.function.Function;
import com.zoho.nlp.translation.datastructure.topbot.TopBottomCriteria;
import com.zoho.sheet.zia2.utils.ZiaConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class ZSZiaConstants {

    public final static int MAX_COLS_LIMIT = 25;            //max cols for zia to handle
    public final static int MAX_WORD_CELL_LIMIT = 200000;   //max cells for wording insights
    public final static int MAX_ZIA_CELL_LIMIT = 1000000;   //max cells for zia to handle
    public final static int MAX_ROWS_TO_CHECK = 5;          //max no of rows to check dataType in filter columns
    public final static int BOT_SAVE_LIMIT = 10000;         //max cells for bots to cached

    public static class colLimit {
        private int maxNumCols;
        private int maxCatCols;
        private int maxDateCols;

        colLimit(int maxNumCols, int maxCatCols, int maxDateCols) {
            this.maxNumCols = maxNumCols;
            this.maxCatCols = maxCatCols;
            this.maxDateCols = maxDateCols;
        }

        public int getMaxNumCols() {
            return maxNumCols;
        }

        public int getMaxCatCols() {
            return maxCatCols;
        }

        public int getMaxDateCols() {
            return maxDateCols;
        }
    }

    public static final Map<ZiaConstants.DataSetNature, colLimit> COL_LIMIT_MAP = new HashMap<ZiaConstants.DataSetNature, colLimit>() {{
        put(ZiaConstants.DataSetNature.HIGHLY_NUMERICAL, new colLimit(10, 5, 2));
        put(ZiaConstants.DataSetNature.HIGHLY_CATEGORICAL, new colLimit(5, 10, 2));
        put(ZiaConstants.DataSetNature.BALANCED, new colLimit(7, 7, 2));
    }};

    public enum ExploreSubCategories {
        MISSING_VALUE("MV"),        //NO I18N
        FIX_INCONSISTENCY("INCS"),  //NO I18N
        DEDUP("DD"),                //NO I18N
        SPELL_ERR("SPE"),           //NO I18N
        FORMULA_ERR("FRE"),         //NO I18N
        MULTIPLE_DATA_TYPES("DFE"); //NO I18N

        private final String responseNaming;

        ExploreSubCategories(String responseNaming) {
            this.responseNaming = responseNaming;
        }

        public String getResponseNaming() {
            return responseNaming;
        }
    }

    public static class RequestConstant {
        public static final String TABLE_RANGE = "tr";   //NO I18N
        public static final String INCLUDES = "i";       //NO I18N
        public static final String DETECT_TABLE = "dt";  //NO I18N
        public static final String SELECTED_COLS = "sc"; //NO I18N
        public static final String REFINE_META = "rm";   //NO I18N
        public static final String USER_QUERY = "uq";    //NO I18N
        public static final String TABLE_AGG_REQUIRED = "tar";  //NO I18N
        public static final String ZIA_HOLDER_ID = "zhid";  //NO I18N
        public static final String REFRESH_ZIA_BOT = "rzb"; //NO I18N
        public static final String RAZ_INSIGHT_META = "im"; //NO I18N
        public static final String VERBOSITY = "vb";    //NO I18N
    }

    public static class ResponseConstant {
        public static final String RECOMMENDED_QUERY = "REC_QUERY"; //NO I18N
        public static final String TITLE = "title";                 //NO I18N
        public static final String DATA_RANGE_STR = "dataRangeStr"; //NO I18N
        public static final String VISUALIZATION = "visualization"; //NO I18N
        public static final String DATA = "data";                   //NO I18N
        public static final String NATURAL_STRING = "naturalString"; //NO I18N
        public static final String TABLE_AGGREGATION = "tableAgg"; //NO I18N
        public static final String INSIGHT_META = "insightMeta";   //NO I18N
    }

    public static class ExploreCategories {
        public static final String DATA_CLEANING = "DC"; //NO I18N
        public static final String DATA_QUALITY = "DQ";  //NO I18N
    }

    public static class ExploreConst {
        public static final String TOTAL_COUNT = "totalCount";               //NO I18N
        public static final String DATA = "data";

        public static final String EXPLORE_RESPONSE = "exploreResponse";     //NO I18N
        public static final String DC_TABLE_DATATYPE = "dcTableDataType";    //NO I18N
        public static final String TABLE_HEADERS = "tableHeaders";           //NO I18N
    }

    public static final String DB_DELIMITER = "|";

    public static final HashMap<String, String> RAZ_PROPS = new HashMap<>() {{
        put("NLP_DM_META_CLASS", "com.zoho.sheet.sheetzia.raz.datapipes.RazMetaConnectorImpl");             //No I18N
        put("NLP_DM_DATA_CLASS", "com.zoho.sheet.sheetzia.raz.datapipes.RazDataConnectorImpl");             //No I18N
        put("RAZ_DATA_PROVIDER", "com.zoho.sheet.sheetzia.raz.datapipes.RazDataProviderImpl");          //No I18N
        put("INSIGHTS_DATA_MODEL", "com.zoho.nlp.raz.datamodelapi.ZiaInsightsDataModelAPI");        //No I18N
        put("APPPROPERTY_RAZ_INSIGHTS_FORECAST_API", "com.zoho.nlp.insights.timeseries.ZLabsForecastAPI");   //No I18N
        put("APPPROPERTY_RAZ_INSIGHTS_OUTLIER_API", "com.zoho.nlp.computationengine.Finder.ZLabsOutlierAPI");   //No I18N
    }};

    public static class MetaKeys {
        public static String chartTitle = "chartTitle";     //NO I18N
        public static String valueColumns = "valCols";      //NO I18N
        public static String axisColumn = "axisColumn";     //NO I18N
        public static String legendColumn = "legendColumn"; //NO I18N
        public static String columnIndex = "colIndex";      //NO I18N
        public static String grpCrtiteria = "grpCriteria";  //NO I18N
        public static String aggOperation = "aggOperation"; //NO I18N
        public static String chartFilter = "chartFilter";   //NO I18N
        public static String filterCol = "filterCol";       //NO I18N
        public static String filterLimit = "filterLimit";   //NO I18N
        public static String filterOperation = "filterOperation";   //NO I18N
    }

    public enum ColumnAggregation {
        ACTUAL,
        YEAR,
        QUARTER,
        MONTH,
        DAY,
        DAY_OF_WEEK,
        QUARTER_BY_YEAR,
        MONTH_BY_YEAR,
        NONE;

        public Function asDimensionFunc() {
            switch (this) {
                case YEAR:
                    return Function.YEAR;
                case QUARTER:
                    return Function.QUARTER;
                case MONTH:
                    return Function.MONTH;
                case DAY:
                    return Function.FULLDATE;
                case DAY_OF_WEEK:
                    return Function.WEEKDAY;
                case QUARTER_BY_YEAR:
                    return Function.ABS_QUARTER;
                case MONTH_BY_YEAR:
                    return Function.ABS_MONTH;
                default:
                    return Function.ACTUAL;
            }
        }
    }

    public enum ChartAggregation {
        SUM,
        AVERAGE,
        COUNT,
        COUNT_DISTINCT,
        MIN,
        MAX,
        MEDIAN,
        ACTUAL;

        public Function asMeasureFunc() {
            switch (this) {
                case SUM:
                    return Function.SUM;
                case AVERAGE:
                    return Function.AVERAGE;
                case COUNT:
                    return Function.COUNT;
                case COUNT_DISTINCT:
                    return Function.DISTINCT_COUNT;
                case MIN:
                    return Function.MINIMUM;
                case MAX:
                    return Function.MAXIMUM;
                case MEDIAN:
                    return Function.MEDIAN;
                default:
                    return Function.ACTUAL;
            }
        }
    }

    public enum FilterOperation {
        TOP,
        BOTTOM,
        NONE;

        public TopBottomCriteria.TopBottomType asFilterFunc() {
            switch (this) {
                case TOP:
                    return TopBottomCriteria.TopBottomType.TOP;
                default:
                    return TopBottomCriteria.TopBottomType.BOTTOM;
            }
        }
    }

    public static class NarrationKeys {
        public static final String NARRATION = "narrations";    //No I18N
        public static final String DEFAULT = "default";     //No I18N
    }

    public enum ZSColumnType {
        /**
         * denotes a integer or decimal type
         **/
        NUMBER,
        /**
         * denotes a percentage column
         **/
        PERCENTAGE,
        /**
         * denotes a currency column
         **/
        CURRENCY,
        /**
         * denotes a boolean column
         **/
        BOOLEAN,
        /**
         * denotes a date / dateTime column - expect year grouping
         **/
        DATE,
        /**
         * denotes a Time column
         **/
        TIME,
        /**
         * denotes a year grouped date column
         **/
        DATE_YEAR,
        /**
         * denotes a string content with <= 25 characters and no newline
         **/
        SINGLE_LINE,
        /**
         * denotes a email
         **/
        EMAIL,
        /**
         * denotes a url column
         **/
        URL,
        /**
         * denotes a string content with > 25 characters and newline
         **/
        MULTI_LINE,
        /**
         * denotes a  column
         **/
        /**
         * denotes a error column
         **/
        UNDEFINED;
    }

}
