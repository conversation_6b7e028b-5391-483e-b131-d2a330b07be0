package com.zoho.sheet.sheetzia.tasks;

import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.table.ZiaSplitTableDataHolderImpl;
import com.zoho.sheet.zia2.interfaceImpl.taskimpl.ZiaTaskAbstract;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.utils.ZiaHolder;

/**
 * <AUTHOR>
 */

public class MultiRangeDataStoreTask extends ZiaTaskAbstract {
    // not in use
    public MultiRangeDataStoreTask(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() throws Exception {
        ZSZiaHolder holder = (ZSZiaHolder) this.holder;
        ZiaTableDataHolder tableDataHolder = new ZiaSplitTableDataHolderImpl(holder.getZiaParams(), holder.getEssentials());
        holder.setTableDataHolder(tableDataHolder);
    }
}
