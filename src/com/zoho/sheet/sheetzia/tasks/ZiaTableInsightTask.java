package com.zoho.sheet.sheetzia.tasks;

import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.ZiaExecutorServiceImpl;
import com.zoho.sheet.sheetzia.interfaces.ZiaExecutorService;
import com.zoho.sheet.sheetzia.subtasks.insight.ZSInsightPostProcess;
import com.zoho.sheet.sheetzia.subtasks.insight.ZiaTestModeProcess;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.insights.ZiaInsightDecision;
import com.zoho.sheet.zia2.interfaceImpl.subtaskimpl.insights.ZiaTableInsightPreProcess;
import com.zoho.sheet.zia2.interfaceImpl.taskimpl.ZiaInsightSegregation;
import com.zoho.sheet.zia2.interfaceImpl.taskimpl.ZiaTaskAbstract;
import com.zoho.sheet.zia2.interfaces.ZiaTask;
import com.zoho.sheet.zia2.utils.ZiaHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> - 13900
 */

public class ZiaTableInsightTask extends ZiaTaskAbstract {
    private static final boolean IS_ZIA_TEST_MODE = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("IS_ZIA_TEST_MODE")); //NO I18N

    private final ZiaExecutorService executorService;

    public ZiaTableInsightTask(ZiaHolder holder) {
        super(holder);
        executorService = new ZiaExecutorServiceImpl();
    }

    @Override
    public void execute() throws Exception {
        List<ZiaTask> taskList = new ArrayList<>();

        taskList.add(new ZiaTableInsightPreProcess(holder));
        taskList.add(new ZiaInsightDecision(holder));
        taskList.add(new ZiaInsightSegregation(holder));

        if (!IS_ZIA_TEST_MODE) {
            taskList.add(new ZSInsightPostProcess(holder));
        }
        else{
            taskList.add(new ZiaTestModeProcess(holder));
        }

        ZSZiaHolder zsZiaHolder = (ZSZiaHolder) holder;

        if (zsZiaHolder.getEssentials().isIncludeWordInsight() && !IS_ZIA_TEST_MODE) {
            taskList.add(new ZiaWordInsightTask(holder, false));
        }

        executorService.executeAll(taskList);
    }
}
