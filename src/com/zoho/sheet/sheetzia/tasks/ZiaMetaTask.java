package com.zoho.sheet.sheetzia.tasks;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Range;
import com.zoho.sheet.datarefineries.datacleaning.DataCleaningBotCreator;
import com.zoho.sheet.datarefineries.datacleaning.DataCleaningBotParams;
import com.zoho.sheet.datarefineries.datacleaning.DataCleaningConstants;
import com.zoho.sheet.datarefineries.datacleaning.interfaceImpl.DataCleanerBotImpl;
import com.zoho.sheet.datarefineries.datacleaning.interfaces.DataCleanerBot;
import com.zoho.sheet.sheetzia.ZSZiaConstants;
import com.zoho.sheet.sheetzia.ZSZiaEssentials;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.bean.ZiaResponseHolder;
import com.zoho.sheet.zia2.interfaceImpl.taskimpl.ZiaTaskAbstract;
import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ZiaParams;
import com.zoho.sheet2app.dc.smartSuggestions.constants.DataTypeNames;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class ZiaMetaTask extends ZiaTaskAbstract {

    public ZiaMetaTask(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() throws Exception {
        ZSZiaHolder holder = (ZSZiaHolder) this.holder;
        ZiaResponseHolder responseHolder = holder.getResponseHolder();
        ZiaParams ziaParams = holder.getZiaParams();
        ZSZiaEssentials essentials = holder.getEssentials();

        if (responseHolder.getStatusCode() == 200) {
            JSONObjectWrapper headers = getHeaders(essentials);
            responseHolder.setHeaders(headers);
            responseHolder.setFrl(essentials.isFirstRecordAsLabel());

//            if (essentials.isRefineMetaRequired()) {
//                List<Range> tableRange = ZSZiaUtilities.toZSRange(essentials.getSheet(), ziaParams.getTableRanges());
//                JSONObject refineMeta = getRefineMeta(tableRange, essentials.isFirstRecordAsLabel());
//
//                if (Objects.nonNull(refineMeta)) {
//                    responseHolder.setRefineMeta(refineMeta);
//                }
//            }

            if (essentials.isRefineMetaRequired()) {
                JSONObjectWrapper refineMeta = new JSONObjectWrapper();

                JSONObjectWrapper dataTypes = new JSONObjectWrapper();
                for (ZiaColumnDataHolder columnDataHolder : holder.getTableDataHolder().getAllColumnDataHolders()) {
                    ZiaConstants.ColumnType columnType = columnDataHolder.getColumnType();
                    JSONObjectWrapper refineDataType = getRefineDataType(columnType);

                    dataTypes.put(String.valueOf(columnDataHolder.getColumnIndex()), refineDataType);
                }

                refineMeta.put(ZSZiaConstants.ExploreConst.DC_TABLE_DATATYPE, dataTypes);
                refineMeta.put(ZSZiaConstants.ExploreConst.TABLE_HEADERS, headers);
                responseHolder.setRefineMeta(refineMeta);
            }
        }
    }

    private JSONObjectWrapper getRefineDataType(ZiaConstants.ColumnType columnType) {
        JSONObjectWrapper dataType = new JSONObjectWrapper();

        String byCount = "byCount"; //No I18N
        String byData = "byData";   //No I18N

        switch (columnType) {
            case NUMERICAL:
                dataType.put(byCount, DataTypeNames.NUMBER.name());
                dataType.put(byData, DataTypeNames.NUMBER.name());
                break;
            case DATE:
            case SEMIDATE:
                dataType.put(byCount, DataTypeNames.DATE_TIME.name());
                dataType.put(byData, DataTypeNames.DATE_TIME.name());
                break;
            case TIME:
                dataType.put(byCount, DataTypeNames.TIME.name());
                dataType.put(byData, DataTypeNames.TIME.name());
                break;
            default:
                dataType.put(byCount, DataTypeNames.SINGLE_LINE.name());
                dataType.put(byData, DataTypeNames.SINGLE_LINE.name());
                break;
        }
        return dataType;
    }

    private JSONObjectWrapper getHeaders(ZSZiaEssentials essentials) {
        ZiaTableDataHolder tableDataHolder = holder.getTableDataHolder();
        JSONObjectWrapper obj = new JSONObjectWrapper();

        for (int selectedColumn : essentials.getSelectedColumns()) {
            ZiaColumnDataHolder columnDataHolder = tableDataHolder.getColumnDataHolderByIndex(selectedColumn);
            obj.put(String.valueOf(selectedColumn), columnDataHolder.getHeaderName());
        }
        return obj;
    }

    private JSONObjectWrapper getRefineMeta(List<Range> tableRanges, boolean isFrl) {
        if (tableRanges.size() != 1) {
            return null;
        }
        Range tableRange = tableRanges.get(0);

        JSONObjectWrapper response = new JSONObjectWrapper();
        DataCleaningBotParams botParams = new DataCleaningBotParams(tableRange, new ArrayList<>());
        botParams.setFirstRowAsLabel(isFrl);
        botParams.setSkipHiddenCols(true);
        botParams.setSkipHiddenRows(true);
        botParams.setIncludeHeaders(true);
//        botParams.setFindDataTypes(true);

        DataCleanerBot dataCleanerBot = new DataCleanerBotImpl(botParams);
        DataCleaningBotCreator instance = DataCleaningBotCreator.getInstance();

        JSONObjectWrapper dcResponse = instance.getResponse(dataCleanerBot);

        if (dcResponse.optBoolean("status")) {  //NO I18N
            parseDCMeta(dcResponse, response);
        }
        return response;
    }

    private void parseDCMeta(JSONObjectWrapper dataCleaningInfo, JSONObjectWrapper refineMetaObj) {
        if (dataCleaningInfo.has(DataCleaningConstants.HEADERS_OPERATION_NAME)) {
            refineMetaObj.put(ZSZiaConstants.ExploreConst.TABLE_HEADERS, dataCleaningInfo.getJSONObject(DataCleaningConstants.HEADERS_OPERATION_NAME));
        }
        if (dataCleaningInfo.has(DataCleaningConstants.FIND_DATA_TYPE_OPERATION_NAME)) {
            refineMetaObj.put(ZSZiaConstants.ExploreConst.DC_TABLE_DATATYPE, dataCleaningInfo.getJSONObject(DataCleaningConstants.FIND_DATA_TYPE_OPERATION_NAME));
        }
    }
}
