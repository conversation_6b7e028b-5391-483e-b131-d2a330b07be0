package com.zoho.sheet.sheetzia.tasks;

import com.adventnet.iam.IAMUtil;
import com.zoho.sheet.sheetzia.ZSZiaConstants;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.ZiaExecutorServiceImpl;
import com.zoho.sheet.sheetzia.interfaces.ZiaExecutorService;
import com.zoho.sheet.sheetzia.raz.botTasks.CreateRazBot;
import com.zoho.sheet.sheetzia.raz.botTasks.ZiaRazBotFetch;
import com.zoho.sheet.sheetzia.raz.botTasks.ZiaRazBotSave;
import com.zoho.sheet.sheetzia.raz.pool.DeleteBotInPool;
import com.zoho.sheet.sheetzia.raz.pool.SaveBotInPool;
import com.zoho.sheet.sheetzia.raz.wordTask.WordInsightSegregation;
import com.zoho.sheet.sheetzia.raz.wordTask.WordInsightTask;
import com.zoho.sheet.sheetzia.subtasks.insight.ZiaWordPostProcess;
import com.zoho.sheet.zia2.interfaceImpl.taskimpl.ZiaTaskAbstract;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaTask;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ZiaRange;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - 13900
 */

public class ZiaWordInsightTask extends ZiaTaskAbstract {
    private static final Logger LOGGER = Logger.getLogger(ZiaWordInsightTask.class.getName());
    private final ZiaExecutorService executorService;
    private final boolean isSingleInsight;

    public ZiaWordInsightTask(ZiaHolder holder, boolean isSingleInsight) {
        super(holder);

        executorService = new ZiaExecutorServiceImpl();
        this.isSingleInsight = isSingleInsight;
    }

    @Override
    public void execute() throws Exception {
        try {
            List<ZiaTask> taskList = new ArrayList<>();

            taskList.add(new ZiaRazBotFetch(holder));
            taskList.add(new CreateRazBot(holder));
            taskList.add(new SaveBotInPool(holder));
            taskList.add(new WordInsightTask(holder, this.isSingleInsight));

            if (this.isSingleInsight) {
                taskList.add(new WordInsightSegregation(holder));
            } else {
                taskList.add(new ZiaWordPostProcess(holder));
            }
            taskList.add(new ZiaRazBotSave(holder));
            taskList.add(new DeleteBotInPool(holder));

            executorService.executeAll(taskList);
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "[ZIA][RAZ] Exception in ZiaWordInsightTask :: " + e.getMessage() + " :: " + Arrays.toString(e.getStackTrace()));
        }
    }
}
