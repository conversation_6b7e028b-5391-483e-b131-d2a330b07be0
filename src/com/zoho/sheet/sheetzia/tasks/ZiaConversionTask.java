package com.zoho.sheet.sheetzia.tasks;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.zia2.interfaceImpl.taskimpl.ZiaConversionInsights;
import com.zoho.sheet.zia2.utils.ZiaHolder;
import com.zoho.sheet.zia2.utils.ZiaResponseHelper;

import java.util.Map;

/**
 * <AUTHOR> - 13900
 */

public class ZiaConversionTask extends ZiaConversionInsights {
    public ZiaConversionTask(ZiaHolder holder) {
        super(holder);
    }

    @Override
    public void execute() throws Exception {
        super.execute();

        ZiaResponseHelper responseHelper = holder.getResponseHelper();
        Map<String, Object> conversionSuggestions = responseHelper.getConversionSuggestions();

        ZSZiaHolder holder = (ZSZiaHolder) this.holder;
        if (!conversionSuggestions.isEmpty()) {
            holder.getResponseHolder().setConversionResponse(new JSONObjectWrapper(conversionSuggestions));
        }
    }
}
