package com.zoho.sheet.sheetzia.builders;

import com.adventnet.zoho.websheet.model.Sheet;
import com.zoho.sheet.sheetzia.ZSZiaEssentials;
import com.zoho.sheet.sheetzia.table.ZiaSplitColumnDataHolderImpl;
import com.zoho.sheet.zia2.utils.ZiaRange;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class ZiaSplitColumnDataHolderBuilder {
    private List<ZiaRange> contentRanges;
    private String headerName;
    private List<Integer> selectedIndices;
    private ZSZiaEssentials.SeriesIn seriesIn;

    public ZiaSplitColumnDataHolderBuilder() {
        this.headerName = "";
        this.contentRanges = new ArrayList<>();
        this.selectedIndices = new ArrayList<>();
        this.seriesIn = ZSZiaEssentials.SeriesIn.COLS;
    }

    public ZiaSplitColumnDataHolderBuilder setContentRanges(List<ZiaRange> contentRanges) {
        this.contentRanges = contentRanges;
        return this;
    }

    public ZiaSplitColumnDataHolderBuilder setHeaderName(String headerName) {
        this.headerName = headerName;
        return this;
    }

    public ZiaSplitColumnDataHolderBuilder setSelectedIndices(List<Integer> selectedIndices) {
        this.selectedIndices = selectedIndices;
        return this;
    }

    public ZiaSplitColumnDataHolderBuilder setSeriesIn(ZSZiaEssentials.SeriesIn seriesIn) {
        this.seriesIn = seriesIn;
        return this;
    }

    public ZiaSplitColumnDataHolderImpl createZiaSplitColumnDataHolderImpl(Sheet sheet, List<ZiaRange> columnRanges) {
        return new ZiaSplitColumnDataHolderImpl(sheet, columnRanges, contentRanges, headerName, selectedIndices, seriesIn);
    }
}