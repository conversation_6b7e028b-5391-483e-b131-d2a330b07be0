package com.zoho.sheet.sheetzia.builders;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.util.NLPUtil;
import com.zoho.sheet.zia2.interfaces.ZiaColumnDataHolder;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ZiaInsightUtils;
import com.zoho.sheet.zia2.utils.ZiaQueryUtils;
import com.zoho.sheet.zia2.utils.ZiaUtilities;
import org.json.JSONObject;

import java.util.*;

import static com.zoho.sheet.zia2.utils.ZiaConstants.ZiaQueryOperations.*;

/**
 * <AUTHOR>
 */

@SuppressWarnings("SpellCheckingInspection")    //NO I18N
public class ZiaFormulaBuilder {
    private final Sheet sheet;
    private final ZiaTableDataHolder tableDataHolder;
    private final List<Integer> operationCols;
    private final List<Integer> columns;
    private final JSONArrayWrapper conditions;
    private final JSONArrayWrapper originalConditions;
    private final String operation;
    private String formula = "";
    private JSONObjectWrapper answer = null;
    private Cell.Type formatType = null;
    private final boolean arrayFormula = false;

    public ZiaFormulaBuilder(Sheet sheet, List<Integer> operationCols, List<Integer> columns, JSONArrayWrapper conditions, String operation, ZiaTableDataHolder tableDataHolder) {
        this.sheet = sheet;
        this.operationCols = operationCols;
        this.originalConditions = conditions;
        this.conditions = preprocess(conditions);
        this.operation = operation;
        this.tableDataHolder = tableDataHolder;
        this.columns = columns;
    }

    private JSONArrayWrapper preprocess(JSONArrayWrapper conditions) {
        JSONArrayWrapper alteredConditions = new JSONArrayWrapper();
        Map<FormulaGrouper, Set<String>> andClause = new LinkedHashMap<>();
        Map<FormulaGrouper, Set<String>> orClause = new LinkedHashMap<>();
        Map<FormulaGrouper, Set<String>> operationValueMap = new LinkedHashMap<>();

        for (int i = 0; i < conditions.length(); i++) {
            JSONObjectWrapper condition = conditions.getJSONObject(i);

            String column = condition.getString(ZiaConstants.ZiaQueryNLPConst.COLUMN);
            String comp = condition.getString(ZiaConstants.ZiaQueryNLPConst.COMP);
            String value = condition.getString(ZiaConstants.ZiaQueryNLPConst.VALUE);

            FormulaGrouper formulaGrouper = new FormulaGrouper(column, comp);

            Set<String> operationBlock = operationValueMap.getOrDefault(formulaGrouper, new LinkedHashSet<>());

            if (!operationValueMap.containsKey(formulaGrouper)) {
                Set<String> clause = andClause.getOrDefault(formulaGrouper, new LinkedHashSet<>());
                clause.add(value);
                andClause.put(formulaGrouper, clause);
                operationBlock.add(value);
            } else {
                if (formulaGrouper.isEqualTo()) {
                    Set<String> clause = orClause.getOrDefault(formulaGrouper, new LinkedHashSet<>());
                    clause.add(value);
                    orClause.put(formulaGrouper, clause);
                } else {
                    Set<String> clause = andClause.getOrDefault(formulaGrouper, new LinkedHashSet<>());
                    clause.add(value);
                    andClause.put(formulaGrouper, clause);
                }
            }
            operationValueMap.put(formulaGrouper, operationBlock);
        }

        List<JSONObjectWrapper> alteredAndClause = new ArrayList<>();

        for (FormulaGrouper formulaGrouper : andClause.keySet()) {
            Set<String> values = andClause.get(formulaGrouper);
            for (String value : values) {
                JSONObjectWrapper obj = new JSONObjectWrapper();
                obj.put(ZiaConstants.ZiaQueryNLPConst.COLUMN, formulaGrouper.column);
                obj.put(ZiaConstants.ZiaQueryNLPConst.COMP, formulaGrouper.comparator);
                obj.put(ZiaConstants.ZiaQueryNLPConst.VALUE, value);
                alteredAndClause.add(obj);
            }
        }

        if (orClause.size() != 0) {
            for (FormulaGrouper formulaGrouper : orClause.keySet()) {
                Set<String> values = orClause.get(formulaGrouper);
                for (String value : values) {
                    JSONArrayWrapper array = new JSONArrayWrapper(alteredAndClause);
                    JSONObjectWrapper obj = new JSONObjectWrapper();
                    obj.put(ZiaConstants.ZiaQueryNLPConst.COLUMN, formulaGrouper.column);
                    obj.put(ZiaConstants.ZiaQueryNLPConst.COMP, formulaGrouper.comparator);
                    obj.put(ZiaConstants.ZiaQueryNLPConst.VALUE, value);
                    array.put(obj);
                    alteredConditions.put(array);
                }
            }
        } else if (alteredAndClause.size() != 0) {
            alteredConditions.put(alteredAndClause);
        }

        return alteredConditions;
    }

    public String build() {
        boolean hasOperation = Objects.nonNull(operation);
        if (hasOperation) {
            formulaWithOperation();
        } else {
            formulaWithoutOperation();
        }

        return getFormula();
    }

    private void formulaWithOperation() {
        boolean isConditional = conditions.length() != 0;
        Integer operationCol = operationCols.get(0);
        ZiaColumnDataHolder columnDataHolder = tableDataHolder.getColumnDataHolderByIndex(operationCol);
        String colHeaderName = columnDataHolder.getHeaderName();
        String opOnRange = ZiaInsightUtils.getContentRangeStringForColumns(Collections.singletonList(operationCol), tableDataHolder);

        switch (operation) {
            case SUM:
                createSummationFormula(opOnRange, isConditional);
                break;
            case MAXIMUM:
                createMaximumFormula(opOnRange, isConditional);
                break;
            case MINIMUM:
                createMinimumFormula(opOnRange, isConditional);
                break;
            case AVERAGE:
                createAvgFormula(opOnRange, isConditional);
                break;
            case COUNT:
            case COUNTA:
                createCountFormula(opOnRange, isConditional);
                break;
            case MEDIAN:
                createMedianFormula(opOnRange, isConditional);
                break;
            case MODE:
                createModeFormula(opOnRange, isConditional);
                break;
            case COUNT_DISTINCT:
                createCountDistinctFormula(opOnRange, isConditional);
                break;
            case STD_DEV:
                createStdDeviationFormula(opOnRange, isConditional);
                break;
            case UNIQUE:
            case DISTINCT:
                createFormulaForDistinctValue(opOnRange, isConditional);
                break;
            case PERCENTAGE:
                createPercentageFormula(opOnRange, colHeaderName, isConditional);
                break;
            case CORR:
                String opOnRange2 = ZiaInsightUtils.getContentRangeStringForColumns(Collections.singletonList(operationCols.get(1)), tableDataHolder);
                createCorrFormula(opOnRange, opOnRange2, isConditional);
                break;
        }
    }

    private void formulaWithoutOperation() {
        if (this.conditions.isEmpty()) {
            return;
        }
        if (this.columns.size() != 0) {
            String opOnRange = ZiaInsightUtils.getContentRangeStringForColumns(this.columns, tableDataHolder);
            this.formula = "=" + String.join("+", createFilterFormulaBlocks(opOnRange));
        } else {
            String tableRangeStr = ZiaUtilities.toRangeString(tableDataHolder.getTableContentRange());
            this.formula = "=" + createTableFilterFormula(tableRangeStr);
        }
    }

    private void createSummationFormula(String opOnRange, boolean isConditional) {

        if (!isConditional) {
            this.formula = "=SUM(" + opOnRange + ")";   //NO I18N
        } else {
            List<String> formulaBlocks = new ArrayList<>();

            for (int i = 0; i < this.conditions.length(); i++) {
                StringBuilder block = new StringBuilder();
                JSONArrayWrapper blockObj = this.conditions.getJSONArray(i);

                String conditionString = constructTestValueConditionString(blockObj);
                block
                        .append("SUMIFS(")  //NO I18N
                        .append(opOnRange)
                        .append(";")
                        .append(conditionString)
                        .append(")");
                formulaBlocks.add(block.toString());
            }

            this.formula = "=" + String.join("+", formulaBlocks);
        }

    }

    private void createAvgFormula(String opOnRange, boolean isConditional) {

        if (!isConditional) {
            this.formula = "=AVERAGE(" + opOnRange + ")";   //NO I18N
        } else {
            List<String> formulaBlocks = new ArrayList<>();

            for (int i = 0; i < this.conditions.length(); i++) {
                StringBuilder block = new StringBuilder();
                JSONArrayWrapper blockObj = this.conditions.getJSONArray(i);

                String conditionString = constructTestValueConditionString(blockObj);
                block
                        .append("AVERAGEIFS(")  //NO I18N
                        .append(opOnRange)
                        .append(";")
                        .append(conditionString)
                        .append(")");
                formulaBlocks.add(block.toString());
            }

            this.formula = "=" + String.join("+", formulaBlocks);
        }
    }

    private void createCountFormula(String opOnRange, boolean isConditional) {

        if (!isConditional) {
            this.formula = "=COUNTA(" + opOnRange + ")";    //NO I18N
        } else {
            List<String> formulaBlocks = new ArrayList<>();

            for (int i = 0; i < this.conditions.length(); i++) {
                StringBuilder block = new StringBuilder();
                JSONArrayWrapper blockObj = this.conditions.getJSONArray(i);

                String conditionString = constructTestValueConditionString(blockObj);
                block
                        .append("COUNTIFS(")    //NO I18N
                        .append(conditionString)
                        .append(";")
                        .append(opOnRange)
                        .append(";\"<>\"")
                        .append(")");
                formulaBlocks.add(block.toString());
            }

            this.formula = "=" + String.join("+", formulaBlocks);
        }
    }

    private void createMaximumFormula(String opOnRange, boolean isConditional) {

        if (!isConditional) {
            this.formula = "=MAX(" + opOnRange + ")";   //NO I18N
        } else {
            List<String> formulaBlocks = new ArrayList<>();

            for (int i = 0; i < this.conditions.length(); i++) {
                StringBuilder block = new StringBuilder();
                JSONArrayWrapper blockObj = this.conditions.getJSONArray(i);

                String conditionString = constructTestValueConditionString(blockObj);
                block
                        .append("MAXIFS(")  //NO I18N
                        .append(opOnRange)
                        .append(";")
                        .append(conditionString)
                        .append(")");
                formulaBlocks.add(block.toString());
            }

            this.formula = "=MAX(" + String.join("+", formulaBlocks) + ")";  //NO I18N
        }

    }

    private void createMinimumFormula(String opOnRange, boolean isConditional) {

        if (!isConditional) {
            this.formula = "=MIN(" + opOnRange + ")";   //NO I18N
        } else {
            List<String> formulaBlocks = new ArrayList<>();

            for (int i = 0; i < this.conditions.length(); i++) {
                StringBuilder block = new StringBuilder();
                JSONArrayWrapper blockObj = this.conditions.getJSONArray(i);

                String conditionString = constructTestValueConditionString(blockObj);
                block
                        .append("MINIFS(")  //NO I18N
                        .append(opOnRange)
                        .append(";")
                        .append(conditionString)
                        .append(")");
                formulaBlocks.add(block.toString());
            }

            this.formula = "=MIN(" + String.join("+", formulaBlocks) + ")";  //NO I18N
        }

    }

    private void createMedianFormula(String opOnRange, boolean isConditional) {
        if (!isConditional) {
            this.formula = "=MEDIAN(" + opOnRange + ")";    //NO I18N
        } else {
            this.formula = "=MEDIAN(" + String.join("+", createFilterFormulaBlocks(opOnRange)) + ")"; //NO I18N
        }
    }

    private void createModeFormula(String opOnRange, boolean isConditional) {
        if (!isConditional) {
            this.formula = "=INDEX(" + opOnRange + ";MODE(MATCH(" + opOnRange + ";" + opOnRange + ";0)))"; //NO I18N
        } else {
            this.formula = "=MODE(" + String.join("+", createFilterFormulaBlocks(opOnRange)) + ")"; //NO I18N
        }
    }

    private void createCountDistinctFormula(String opOnRange, boolean isConditional) {
        if (!isConditional) {
            this.formula = "=SUMPRODUCT(1/COUNTIF(" + opOnRange + ";" + opOnRange + "))";   //NO I18N
        }
    }

    private void createStdDeviationFormula(String opOnRange, boolean isConditional) {
        if (!isConditional) {
            this.formula = "=STDEV(" + opOnRange + ")";  //NO I18N
        } else {
            this.formula = "=STDEV(" + String.join("+", createFilterFormulaBlocks(opOnRange)) + ")"; //NO I18N
        }
    }

    private void createFormulaForDistinctValue(String opOnRange, boolean isConditional) {
        if (!isConditional) {
            this.formula = "=UNIQUE(" + opOnRange + ")";    //NO I18N
        } else {
            this.formula = "=UNIQUE(" + String.join("+", createFilterFormulaBlocks(opOnRange)) + ")";   //NO I18N
        }
    }

    private void createPercentageFormula(String opOnRange, String operationCol, boolean isConditional) {
        if (isConditional) {
            JSONObjectWrapper firstCondition = this.originalConditions.getJSONObject(0);
            List<String> formulaBlocks = new ArrayList<>();
            boolean isConditionHasOperation = firstCondition.getString(ZiaConstants.ZiaQueryNLPConst.COLUMN).equals(operationCol);

            for (int i = 0; i < this.conditions.length(); i++) {
                StringBuilder block = new StringBuilder();
                JSONArrayWrapper blockObj = this.conditions.getJSONArray(i);

                String conditionString = constructTestValueConditionString(blockObj);
                if (!isConditionHasOperation) {
                    block
                            .append("SUMIFS(")  //NO I18N
                            .append(opOnRange)
                            .append(";")
                            .append(conditionString)
                            .append(")");
                } else {
                    block
                            .append("COUNTIFS(")    //NO I18N
                            .append(conditionString)
                            .append(";")
                            .append(opOnRange)
                            .append(";\"<>\"")
                            .append(")");
                }
                formulaBlocks.add(block.toString());
            }

            this.formula = String.join("+", formulaBlocks);

            if (formulaBlocks.size() > 1) {
                this.formula = "(" + this.formula + ")";
            }
            this.formula += (isConditionHasOperation ? "/COUNTA(" : "/SUM(") + opOnRange + ")"; //NO I18N
            this.formula = "=" + this.formula;

            this.formatType = Cell.Type.PERCENTAGE;
        }
    }

    private void createCorrFormula(String op1, String op2, boolean isConditional) {
        if (!isConditional) {
            this.formula = "=CORREL(" + op1 + ";" + op2 + ")";  //NO I18N
            this.formatType = Cell.Type.FLOAT;
        }
    }

    private List<String> createFilterFormulaBlocks(String opOnRange) {
        List<String> formulaBlocks = new ArrayList<>();

        for (int i = 0; i < this.conditions.length(); i++) {
            JSONArrayWrapper blockObj = this.conditions.getJSONArray(i);
            formulaBlocks.add(createFilterFormula(opOnRange, blockObj));
        }
        return formulaBlocks;
    }

    private String createTableFilterFormula(String tableRangeStr) {
        List<String> formulaBlocks = new ArrayList<>();

        formulaBlocks.add(tableRangeStr);
        for (int i = 0; i < this.conditions.length(); i++) {
            JSONArrayWrapper blockObj = this.conditions.getJSONArray(i);
            StringBuilder ifCondition = new StringBuilder("IFS(");  //NO I18N

            for (int j = 0, blockLength = blockObj.length(); j < blockLength; j++) {
                if (j != 0) {
                    ifCondition.append(";");
                }
                String condition = ZiaQueryUtils.mapConditionToFilterFormulaString(blockObj.getJSONObject(j).getJsonObject(), tableDataHolder);
                ifCondition.append(condition);
                ifCondition.append(";TRUE");
            }

            ifCondition.append(")");
            formulaBlocks.add(ifCondition.toString());
        }

        return "FILTER(" + String.join(";", formulaBlocks) + ")"; //NO I18N
    }

    private String createFilterFormula(String opOnRange, JSONArrayWrapper blockObj) {
        StringBuilder builder = new StringBuilder("FILTER(");   //NO I18N
        builder.append(opOnRange).append(";");

        for (int j = 0; j < blockObj.length(); j++) {
            if (j != 0) {
                builder.append(";");
            }
            JSONObject condition = blockObj.getJSONObject(j).getJsonObject();
            String conditionFltrFormula = ZiaQueryUtils.mapConditionToFilterFormulaString(condition, tableDataHolder);
            builder.append(conditionFltrFormula);
        }
        builder.append(")");


        return builder.toString();
    }

    private String constructTestValueConditionString(JSONArrayWrapper blockObj) {
        StringBuilder conditionBuilder = new StringBuilder();
        for (int j = 0; j < blockObj.length(); j++) {
            if (j != 0) {
                conditionBuilder.append(";");
            }
            JSONObject condition = blockObj.getJSONObject(j).getJsonObject();
            conditionBuilder.append(ZiaQueryUtils.mapConditionToFormulaString(condition, tableDataHolder));
        }
        return conditionBuilder.toString();
    }

    public JSONObjectWrapper getAnswer() {
        if (Objects.isNull(answer) && !formula.isEmpty()) {
            answer = NLPUtil.executeFormula(sheet.getWorkbook(), sheet, formula, arrayFormula, getFormatTypeString());
            return answer;
        }
        return answer;
    }

    public String getFormula() {
        return formula;
    }

    public Cell.Type getFormatType() {
        return formatType;
    }

    public String getFormatTypeString() {
        return Objects.isNull(formatType) ? null : formatType.name();
    }

    public boolean isArrayFormula() {
        return arrayFormula;
    }

    private static class FormulaGrouper {
        private final String column;
        private final String comparator;
        private final boolean isEqualTo;

        public FormulaGrouper(String column, String comparator) {
            this.column = column;
            this.comparator = comparator;
            this.isEqualTo = comparator.equals("=");
        }

        public String getComparator() {
            return comparator;
        }

        public String getColumn() {
            return column;
        }

        public boolean isEqualTo() {
            return isEqualTo;
        }
    }
}
