// $Id$
package com.zoho.sheet.zia;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.nlp.NLInteractorImpl;
import com.adventnet.zoho.websheet.model.util.NLPUtil;
import com.google.common.util.concurrent.AtomicDouble;
import com.zlabs.nlp.bots.application.zsheets.helper.SheetCodeGenerator;
import com.zoho.sheet.chart.AggregationConstants;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.util.I18nMessage;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
public class ZDeciderHelper1 {

	private List<String> categorical ;
	private List<String> numerical;
	private List<String> dateCol;
	private Map<String, Integer> daysBetween;
	private Map<String, Double> correlMap;
	private Map<String, Double> skewnessQuotient;
	private Sheet sheet;
	private String seriesIn;
	private boolean priorityColumnsSelected;
	private Map lookupTable;
	private List<String> semiColumns;
	private JSONArrayWrapper combinations = new JSONArrayWrapper();
	private Map<String,Double> correlationMap = new HashMap<String, Double>();
	private Map<String,JSONArrayWrapper> catToNumCorrelMap = new HashMap<String, JSONArrayWrapper>();
	private Map<String,Double> numToCatCorrelMap = new HashMap<String, Double>();
	private Map<String, Double> catToCatCorrelMap = new HashMap<String, Double>();
	private Map<String, Double> numToNumCorrelMap = new HashMap<String, Double>();
	private Map<String, JSONArrayWrapper> dateToNumCorrelMap = new HashMap<String, JSONArrayWrapper>();
	private String dateColumn;
	private final static String NLP_CORREL_DELIMITER = ":::::";
	private int totalRows;
	private int totalColumns;
	private boolean singleCategory;
	private boolean singleNumber;
	private boolean noNumberColumns;
	private boolean noCategoryColumns;
	private boolean aggFilterApplied;
	private boolean normalFilterApplied;
	private DatasetProperties datasetProperty;
	private static List<String> irrelevantColumns = new ArrayList<String>();
	private static List<String> numbersDisQualifiedForSum = new ArrayList<String>();
	private static List<String> irrelevantPriorityColumns = new ArrayList<String>();
	private Map<String, Integer> uniqueValuesInHeader = new HashMap<String, Integer>();
	private List<String> bestNumberColumns = new ArrayList<String>();
	private String highlySkewedNumColumn = null;
	private Date minDate = null;
	private Date maxDate = null;
	private PriorityColumns priorityColumns;
	private FilterColumns filterColumns;
	private NLInteractorImpl n;
	static{
		irrelevantColumns.add("GENDER");
	}
	static {
		numbersDisQualifiedForSum.add("AGE");
	}
	static{
		irrelevantPriorityColumns.add("S.NO");
		irrelevantPriorityColumns.add("SNO");
		irrelevantPriorityColumns.add("ZIPCODE");
		irrelevantPriorityColumns.add("COUNTRYCODE");
		irrelevantPriorityColumns.add("ID");
		irrelevantPriorityColumns.add("PHONENUMBER");
		irrelevantPriorityColumns.add("PHNUMBER");
		irrelevantPriorityColumns.add("MOBILE");
		irrelevantPriorityColumns.add("ROWID");
		irrelevantPriorityColumns.add("ORDERID");
	}
	public DatasetProperties getDatasetProperty() {
		return datasetProperty;
	}
	public void setDatasetProperty(DatasetProperties datasetProperty) {
		this.datasetProperty = datasetProperty;
	}
	enum DatasetNature{
		highlyCategorical,	//has lot of categorical columns
		highlyNumerical,	// has lot of numerical columns
		balanced			// its a balanced dataset with more or less equal number of categories and numbers.
	}
	enum DatasetSize{
		small,	// 0-99 rows
		medium,	// 100-999 rows
		large	// >1000 rows
	}
	
	private final class FilterColumns {
		List<String> filterCategorical = new ArrayList<String>();
		List<String> filterNumerical = new ArrayList<String>();
		List<String> filterSemi = new ArrayList<String>();
		List<String> filterSkip = new ArrayList<String>();
		List<String> filterDate = new ArrayList<String>();
		JSONArrayWrapper filterCombinations = new JSONArrayWrapper();
		
		public void relaxUniqueValuesConditionsForFilterColumns(){
			datasetProperty.childUniqueValuesForAggregation = (totalRows/2 < 50)?totalRows/2:50;
			datasetProperty.parentUniqueValuesForAggregation = (totalRows/2 < 50)?totalRows/2:50;
		}
		
		public void segregateFilterColumnsBasedOnAnalysis(List<String> filterColumns, List<String> columns, String key){
			List<String> prioritySubset = this.subsetOfFilterColumn(filterColumns, columns);
			if(key.equals("categorical")){
				filterCategorical.addAll(prioritySubset);
			}else if (key.equals("numerical")){
				List<String> filterCatColumns = new CopyOnWriteArrayList<String>();
				filterCatColumns.addAll(prioritySubset);
				for(int i=0; i<filterCatColumns.size(); i++){
					String header = prioritySubset.get(i);
					boolean catColumn = checkIfValidCatgeoricalColumn(header);
					if(catColumn){
						filterCategorical.add(header);
						filterCatColumns.remove(header);
					}
				}
				filterNumerical.addAll(filterCatColumns);
			} else if(key.equals("date")){
				filterDate.addAll(prioritySubset);
			} else if(key.equals("semi")){
				filterSemi.addAll(prioritySubset);
			} else if(key.equals("skip")){
				filterSkip.addAll(prioritySubset);
			}
		}
		
		public List<String> subsetOfFilterColumn(List<String> compareList1, List<String> compareList2){
			List<String> prioritySubSet = new ArrayList<String>();
			if(compareList1 != null && compareList2 == null){
				return compareList1;
			}
			if(compareList2 == null || compareList1 == null){
				return prioritySubSet;
			}
			for (String item : compareList1) {
			    if (compareList2.contains(item) || compareList2.contains(item.toUpperCase().replaceAll("\\s+",""))) {
			    	prioritySubSet.add(item);
			    }
			}
			return prioritySubSet;
		}
		public void analyzeNLPDataAndGenerateCombinations(NLInteractorImpl  n){
			JSONArrayWrapper numberColumnCombinations = this.generateNumberColumnCombinations();
			JSONArrayWrapper countColumnCombinations = this.generateCountColumnCombinations();
			JSONArrayWrapper singleDepthIndexCombinations = this.generateSingleDepthIndexCombinations();
			JSONArrayWrapper doubleDepthIndexCombinations = this.generateDoubleDepthIndexCombinations();
			JSONArrayWrapper semiCombinations = this.generateSemiColumnCombinations();
			JSONArrayWrapper dateColumnCombinations = this.generateDateColumnCombinations();
			JSONArrayWrapper doubleDepthDate = this.generateDoubleDepthDateCombinations();
			
			for(int i=0;i< dateColumnCombinations.length(); i++){
				this.filterCombinations.put(dateColumnCombinations.getJSONObject(i));
			}
			for(int i=0;i< numberColumnCombinations.length(); i++){
				this.filterCombinations.put(numberColumnCombinations.getJSONObject(i));
			}
			for(int i=0;i< countColumnCombinations.length(); i++){
				this.filterCombinations.put(countColumnCombinations.getJSONObject(i));
			}
			for(int i=0;i< singleDepthIndexCombinations.length(); i++){
				this.filterCombinations.put(singleDepthIndexCombinations.getJSONObject(i));
			}
			for(int i=0;i< doubleDepthIndexCombinations.length(); i++){
				this.filterCombinations.put(doubleDepthIndexCombinations.getJSONObject(i));
			}
			for(int i=0;i< semiCombinations.length(); i++){
				this.filterCombinations.put(semiCombinations.getJSONObject(i));
			}
			for(int i=0;i< doubleDepthDate.length(); i++){
				this.filterCombinations.put(doubleDepthDate.getJSONObject(i));
			}
		}
		
		
		
		
		private JSONArrayWrapper generateNumberColumnCombinations(){
			JSONArrayWrapper numberColumnCombinations = new JSONArrayWrapper();
			if(filterNumerical.size()>1){
				filterNumerical.forEach(colheader -> {
					AtomicDouble bestCorrelValue = new AtomicDouble(0.0);
					AtomicReference<String> bestNumCol = new AtomicReference<String>();
					numToNumCorrelMap.forEach((String correlKey, Double value) -> {
						if(correlKey.startsWith(colheader)){
							if(value > bestCorrelValue.get()){
								bestCorrelValue.set(value);
								bestNumCol.set(correlKey.split(NLP_CORREL_DELIMITER)[1]);
							}
						}
					});
					JSONObjectWrapper combination = new JSONObjectWrapper();
					combination.put("num", Arrays.asList(colheader, bestNumCol.get()));
					numberColumnCombinations.put(combination);
				});
			}else if(filterNumerical.size()>0){
				JSONObjectWrapper combination = new JSONObjectWrapper();
				combination.put("num", filterNumerical);
				numberColumnCombinations.put(combination);
			}
			return numberColumnCombinations;
		}
		
		private JSONArrayWrapper generateCountColumnCombinations(){
			JSONArrayWrapper countColumnCombinations = new JSONArrayWrapper();
			int threshold = Math.min(datasetProperty.parentUniqueValuesForAggregation, totalRows);
			if(filterCategorical.size() > 0){
				filterCategorical.forEach(colheader -> {
					int uqValues = getTotalUniqueValuesInColumn(colheader);
					if(uqValues < threshold){
						JSONObjectWrapper combination = new JSONObjectWrapper();
						combination.put("cat", Arrays.asList(colheader));
						countColumnCombinations.put(combination);
					}
				});
			}
			
			if(filterDate.size() > 0){
				int dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
				filterDate.forEach(colheader -> {
					//int uqValues = getTotalUniqueValuesInColumn(colheader);
					if(dateGrouping != 0){
						JSONObjectWrapper combination = new JSONObjectWrapper();
						combination.put("cat", Arrays.asList(colheader));
						combination.put("dateGrouping", dateGrouping);
						combination.put("dateCol", true);
						countColumnCombinations.put(combination);
					}
				});
			}
			return countColumnCombinations;
		}
		
		
		private JSONArrayWrapper generateSingleDepthIndexCombinations(){
			JSONArrayWrapper singleDepthIndexCombinations = new JSONArrayWrapper();
			if(filterCategorical.size() > 0 && filterNumerical.size() > 0){
				filterCategorical.forEach(colHeader -> {
					JSONObjectWrapper singleDepthIndex = new JSONObjectWrapper();
					singleDepthIndex.put("cat", Arrays.asList(colHeader));
					singleDepthIndex.put("num", filterNumerical);
					singleDepthIndexCombinations.put(singleDepthIndex);
				});
			}
			return singleDepthIndexCombinations;
		}
		
		private JSONArrayWrapper generateDoubleDepthIndexCombinations(){
			JSONArrayWrapper doubleDepthIndexCombinations = new JSONArrayWrapper();
			if(filterCategorical.size() == 2 && filterNumerical.size() > 0){
				JSONObjectWrapper doubleDepthIndex = new JSONObjectWrapper();
				doubleDepthIndex.put("cat", filterCategorical);
				doubleDepthIndex.put("num", filterNumerical);
				doubleDepthIndexCombinations.put(doubleDepthIndex);
			}
			
			return doubleDepthIndexCombinations;
		}
		
		private JSONArrayWrapper generateSemiColumnCombinations() {
			JSONArrayWrapper semicolumnCombinations = new JSONArrayWrapper();
			if(filterSemi.size() > 0 && filterNumerical.size() > 0) {
				filterSemi.forEach(colHeader -> {
					JSONObjectWrapper semiColumnComb = new JSONObjectWrapper();
					semiColumnComb.put("cat", Arrays.asList(colHeader));
					semiColumnComb.put("num", filterNumerical);
					semicolumnCombinations.put(semiColumnComb);
				});
			}
			return semicolumnCombinations;
		}
		
		private JSONArrayWrapper generateDateColumnCombinations(){
			JSONArrayWrapper dateColumnCombinations = new JSONArrayWrapper();

			if(filterDate.size() > 0 && filterNumerical.size() > 0){
				filterDate.forEach(colHeader -> {
					JSONObjectWrapper singleDepthIndex = new JSONObjectWrapper();
					int dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
					singleDepthIndex.put("cat", Arrays.asList(colHeader));
					singleDepthIndex.put("num", filterNumerical);
					singleDepthIndex.put("dateCol", true);
					singleDepthIndex.put("dateGrouping", dateGrouping);
					dateColumnCombinations.put(singleDepthIndex);
				});
			}
			return dateColumnCombinations;
		}
		
		private JSONArrayWrapper generateDoubleDepthDateCombinations(){
			JSONArrayWrapper dateColumnCombinations = new JSONArrayWrapper();
			if(filterDate.size() > 0 && filterCategorical.size() > 0 && filterNumerical.size() == 0){
				JSONObjectWrapper dateColumnCombo = new JSONObjectWrapper();
				int dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
				dateColumnCombo.put("cat", Arrays.asList(filterDate.get(0), filterCategorical.get(0)));
				dateColumnCombo.put("dateGrouping", dateGrouping);
				dateColumnCombinations.put(dateColumnCombo);
			}
			return dateColumnCombinations;
		}
	}
	private final class PriorityColumns  {
		List<String> priorityCategorical = new ArrayList<String>();
		List<String> priorityNumerical = new ArrayList<String>();
		List<String> prioritySemi = new ArrayList<String>();
		List<String> prioritySkip = new ArrayList<String>();
		List<String> priorityDate = new ArrayList<String>();
		JSONArrayWrapper priorityCombinations = new JSONArrayWrapper();
		int totalThresholdForAggregation = 50;
		
		public void relaxUniqueValuesConditionsForPriorityColumns(){
			datasetProperty.childUniqueValuesForAggregation = (totalRows/2 < 50)?totalRows/2:50;
			datasetProperty.parentUniqueValuesForAggregation = (totalRows/2 < 50)?totalRows/2:50;
		}
		
		public void segregatePriorityColumnsBasedOnAnalysis(List<String> priorityColumns, List<String> columns, String key){
			//columns = (columns == null) ? irrelevantPriorityColumns : columns;
			List<String> prioritySubset = this.subsetOfPriorityColumn(priorityColumns, columns);
			if(key.equals("categorical")){
				priorityCategorical.addAll(prioritySubset);
			}else if (key.equals("numerical")){
				List<String> priorityCatColumns = new CopyOnWriteArrayList<String>();
				priorityCatColumns.addAll(prioritySubset);
				for(int i=0; i<priorityCatColumns.size(); i++){
					String header = prioritySubset.get(i);
					boolean catColumn = checkIfValidCatgeoricalColumn(header);
					if(catColumn){
						priorityCategorical.add(header);
						priorityCatColumns.remove(header);
					}
				}
				priorityNumerical.addAll(priorityCatColumns);
			} else if(key.equals("date")){
				priorityDate.addAll(prioritySubset);
			} else if(key.equals("semi")){
				prioritySemi.addAll(prioritySubset);
			} else if(key.equals("skip")){
				prioritySkip.addAll(prioritySubset);
			}
		}
		
		public void analyzeNLPDataAndGenerateCombinations(NLInteractorImpl  n){
			JSONArrayWrapper numberColumnCombinations = this.generateNumberColumnCombinations();
			JSONArrayWrapper countColumnCombinations = this.generateCountColumnCombinations();
			JSONArrayWrapper singleDepthIndexCombinations = this.generateSingleDepthIndexCombinations();
			JSONArrayWrapper doubleDepthIndexCombinations = this.generateDoubleDepthIndexCombinations();
			JSONArrayWrapper semiCombinations = this.generateSemiColumnCombinations();
			JSONArrayWrapper dateColumnCombinations = this.generateDateColumnCombinations();
			JSONArrayWrapper doubleDepthDate = this.generateDoubleDepthDateCombinations();
			
			for(int i=0;i< dateColumnCombinations.length(); i++){
				this.priorityCombinations.put(dateColumnCombinations.getJSONObject(i));
			}
			for(int i=0;i< numberColumnCombinations.length(); i++){
				this.priorityCombinations.put(numberColumnCombinations.getJSONObject(i));
			}
			for(int i=0;i< countColumnCombinations.length(); i++){
				this.priorityCombinations.put(countColumnCombinations.getJSONObject(i));
			}
			for(int i=0;i< singleDepthIndexCombinations.length(); i++){
				this.priorityCombinations.put(singleDepthIndexCombinations.getJSONObject(i));
			}
			for(int i=0;i< doubleDepthIndexCombinations.length(); i++){
				this.priorityCombinations.put(doubleDepthIndexCombinations.getJSONObject(i));
			}
			for(int i=0;i< semiCombinations.length(); i++){
				this.priorityCombinations.put(semiCombinations.getJSONObject(i));
			}
			for(int i=0;i< doubleDepthDate.length(); i++){
				this.priorityCombinations.put(doubleDepthDate.getJSONObject(i));
			}
		}
		
		public boolean isInPriorityColumn(List<String> compareList1, List<String> compareList2){
			if(compareList2 == null || compareList1 == null){
				return false;
			}
			for (String item : compareList1) {
			    if (compareList2.contains(item)) {
			    	return true;
			    }
			}
			return false;
		}
		
		public List<String> subsetOfPriorityColumn(List<String> compareList1, List<String> compareList2){
			List<String> prioritySubSet = new ArrayList<String>();
			if(compareList1 != null && compareList2 == null){
				return compareList1;
			}
			if(compareList2 == null || compareList1 == null){
				return prioritySubSet;
			}
			for (String item : compareList1) {
			    if (compareList2.contains(item) || compareList2.contains(item.toUpperCase().replaceAll("\\s+",""))) {
			    	prioritySubSet.add(item);
			    }
			}
			return prioritySubSet;
		}
		
		private JSONArrayWrapper generateNumberColumnCombinations(){
			JSONArrayWrapper numberColumnCombinations = new JSONArrayWrapper();
			if(priorityNumerical.size()>1){
				priorityNumerical.forEach(colheader -> {
					AtomicDouble bestCorrelValue = new AtomicDouble(0.0);
					AtomicReference<String> bestNumCol = new AtomicReference<String>();
					numToNumCorrelMap.forEach((String correlKey, Double value) -> {
						if(correlKey.startsWith(colheader)){
							if(value > bestCorrelValue.get()){
								bestCorrelValue.set(value);
								bestNumCol.set(correlKey.split(NLP_CORREL_DELIMITER)[1]);
							}
						}
					});
					JSONObjectWrapper combination = new JSONObjectWrapper();
					combination.put("num", Arrays.asList(colheader, bestNumCol.get()));
					numberColumnCombinations.put(combination);
				});
			}else if(priorityNumerical.size()>0){
				JSONObjectWrapper combination = new JSONObjectWrapper();
				combination.put("num", priorityNumerical);
				numberColumnCombinations.put(combination);
			}
			return numberColumnCombinations;
		}
		
		private JSONArrayWrapper generateCountColumnCombinations(){
			JSONArrayWrapper countColumnCombinations = new JSONArrayWrapper();
			int threshold = Math.min(datasetProperty.parentUniqueValuesForAggregation, totalRows);
			if(priorityCategorical.size() > 0){
				priorityCategorical.forEach(colheader -> {
					int uqValues = getTotalUniqueValuesInColumn(colheader);
					if(uqValues < threshold){
						JSONObjectWrapper combination = new JSONObjectWrapper();
						combination.put("cat", Arrays.asList(colheader));
						countColumnCombinations.put(combination);
					}
				});
			}else{
				if(this.prioritySkip.size() == 1 && irrelevantPriorityColumns.contains(this.prioritySkip.get(0).toUpperCase().replaceAll("\\s+",""))){	
						AtomicInteger index = new AtomicInteger(0);
						categorical.forEach(colheader -> {
							if(index.get() < 2){
								int uqValues = getTotalUniqueValuesInColumn(colheader);
								if(uqValues < threshold){
									JSONObjectWrapper combination = new JSONObjectWrapper();
									combination.put("cat", Arrays.asList(colheader));
									combination.put("titleLabel", "Count Unique of "+prioritySkip.get(0));
									countColumnCombinations.put(combination);
									index.getAndIncrement();
								}
							}
						});
				}
			}
			return countColumnCombinations;
		}
		
		
		private JSONArrayWrapper generateSingleDepthIndexCombinations(){
			JSONArrayWrapper singleDepthIndexCombinations = new JSONArrayWrapper();
			JSONArrayWrapper catNumAnalysis = null;
			if(priorityCategorical.size() > 0){
				if(priorityCategorical.size() == 1){
					catNumAnalysis = doRelationShipAnalysisMultipleNumCols(priorityCategorical, numerical, priorityCategorical.size(), catToNumCorrelMap, n, true);
				}else{
					catNumAnalysis = doRelationShipAnalysisMultipleNumCols(priorityCategorical, numerical, priorityCategorical.size(), catToNumCorrelMap, n, false);
				}
				for( int i =0; i<catNumAnalysis.length(); i++){
					JSONObjectWrapper catNumobejct = catNumAnalysis.getJSONObject(i);
					singleDepthIndexCombinations.put(catNumobejct);
				}
			}
			
			if(priorityCategorical.size() == 1 && priorityNumerical.size() > 0){
				String catColumn = priorityCategorical.get(0);
				List<String> validNumCols = new ArrayList<String>();
				priorityNumerical.forEach(colheader -> {
					if(!irrelevantPriorityColumns.contains(colheader.toUpperCase().replaceAll("\\s+",""))){
						validNumCols.add(colheader);
						JSONObjectWrapper combination = new JSONObjectWrapper();
						combination.put("cat", Arrays.asList(catColumn));
						combination.put("num", Arrays.asList(colheader));
						singleDepthIndexCombinations.put(combination);
					}
				});
				if(validNumCols.size() > 1){
					JSONObjectWrapper combination = new JSONObjectWrapper();
					int uqValues = getTotalUniqueValuesInColumn(catColumn);
					boolean isSuitableForAggregation = isSuitableForAggregation(uqValues);
					if(isSuitableForAggregation || uqValues < 25){
						combination.put("cat", Arrays.asList(catColumn));
						combination.put("num", validNumCols);
						combination.put("doubleSeries", true);
						singleDepthIndexCombinations.put(combination);
					}
					
				}
			}
			
			if(priorityNumerical.size() > 0 && priorityCategorical.size() == 0){
				int maxlimit = priorityNumerical.size() == 1 ? 2: 1;
					for(int i=0;i<maxlimit && i<categorical.size();i++){
						priorityCategorical.add(categorical.get(i));
					}
					priorityNumerical.forEach(colheader -> {
//					AtomicDouble bestCorrelValue = new AtomicDouble(0.0);
//					AtomicReference<String> bestCatCol = new AtomicReference<String>();
//					numToCatCorrelMap.forEach((String correlKey, Double value) -> {
//						if(correlKey.startsWith(colheader)){
//							if(value > bestCorrelValue.get()){
//								bestCorrelValue.set(value);
//								bestCatCol.set(correlKey.split(NLP_CORREL_DELIMITER)[1]);
//							}
//						}
//					});
//					if(bestCatCol.get() != null && !bestCatCol.get().equals("")){
//						JSONObjectWrapper combination = new JSONObjectWrapper();
//						combination.put("cat", Arrays.asList(bestCatCol.get()));
//						combination.put("num", Arrays.asList(colheader));
//						singleDepthIndexCombinations.put(combination);
//					}
					for(int i=0; i<this.priorityCategorical.size(); i++){
						String catColumn = this.priorityCategorical.get(i);
						//if(!catColumn.equals(bestCatCol.get())){
							JSONObjectWrapper combination = new JSONObjectWrapper();
							combination.put("cat", Arrays.asList(catColumn));
							combination.put("num", Arrays.asList(colheader));
							singleDepthIndexCombinations.put(combination);
						//}
					}
				});
			}
			
			return singleDepthIndexCombinations;
		}
		
		private JSONArrayWrapper generateDoubleDepthIndexCombinations(){
			JSONArrayWrapper doubleDepthIndexCombinations = new JSONArrayWrapper();
			JSONObjectWrapper combination = new JSONObjectWrapper();
			if(priorityCategorical.size() > 1){
				JSONArrayWrapper catNumAnalysis = doRelationShipAnalysisMultipleNumCols(priorityCategorical, numerical, 1, catToNumCorrelMap, n, true);
				JSONObjectWrapper catNumObject = catNumAnalysis.getJSONObject(0);
				String colheader1 = priorityCategorical.get(0);
				String colheader2 = priorityCategorical.get(1);
				int totalUnqValues = getTotalUniqueValuesInColumn(colheader1) * getTotalUniqueValuesInColumn(colheader2);
				if(totalUnqValues < this.totalThresholdForAggregation){
					combination.put("cat", Arrays.asList(colheader1, colheader2));
					if(priorityNumerical.size() > 0){
						combination.put("num", Arrays.asList(priorityNumerical));
					}else{
						combination.put("num", Arrays.asList(catNumObject.getJSONArray("num")));
					}
					
					combination.put("uniqueValues", totalUnqValues);
				}
			}
			return doubleDepthIndexCombinations;
		}
		
		private JSONArrayWrapper generateSemiColumnCombinations() {
			JSONArrayWrapper semicolumnCombinations = new JSONArrayWrapper();
			JSONArrayWrapper catNumAnalysis = null;
			if(prioritySemi.size() > 0){
				if(prioritySemi.size() == 1){
					catNumAnalysis = doRelationShipAnalysisMultipleNumCols(prioritySemi, numerical, prioritySemi.size(), catToNumCorrelMap, n, true);
				}else{
					catNumAnalysis = doRelationShipAnalysisMultipleNumCols(prioritySemi, numerical, prioritySemi.size(), catToNumCorrelMap, n, false);
				}
				for( int i =0; i<catNumAnalysis.length(); i++){
					JSONObjectWrapper catNumobejct = catNumAnalysis.getJSONObject(i);
					semicolumnCombinations.put(catNumobejct);
				}
			}
			if(prioritySemi.size() == 1 && priorityNumerical.size() > 0){
				String catColumn = prioritySemi.get(0);
				List<String> validNumCols = new ArrayList<String>();
				priorityNumerical.forEach(colheader -> {
					if(!irrelevantPriorityColumns.contains(colheader.toUpperCase().replaceAll("\\s+",""))){
						validNumCols.add(colheader);
						JSONObjectWrapper combination = new JSONObjectWrapper();
						combination.put("cat", Arrays.asList(catColumn));
						combination.put("num", Arrays.asList(colheader));
						semicolumnCombinations.put(combination);
					}
				});
				if(validNumCols.size() > 1){
					JSONObjectWrapper combination = new JSONObjectWrapper();
					int uqValues = getTotalUniqueValuesInColumn(catColumn);
					boolean isSuitableForAggregation = isSuitableForAggregation(uqValues);
					if(isSuitableForAggregation || uqValues < 25){
						combination.put("cat", Arrays.asList(catColumn));
						combination.put("num", validNumCols);
						combination.put("doubleSeries", true);
						semicolumnCombinations.put(combination);
					}
					
				}
			}
			if(priorityNumerical.size() > 0 && prioritySemi.size() == 0){
				int maxlimit = priorityNumerical.size() == 1 ? 2: 1;
					for(int i=0;i<maxlimit && i<semiColumns.size();i++){
						prioritySemi.add(semiColumns.get(i));
					}
					priorityNumerical.forEach(colheader -> {

					for(int i=0; i<this.prioritySemi.size(); i++){
						String catColumn = this.prioritySemi.get(i);
							JSONObjectWrapper combination = new JSONObjectWrapper();
							combination.put("cat", Arrays.asList(catColumn));
							combination.put("num", Arrays.asList(colheader));
							semicolumnCombinations.put(combination);
					}
				});
			}
			
			
			return semicolumnCombinations;
		}
		
		private JSONArrayWrapper generateDateColumnCombinations(){
			JSONArrayWrapper dateColumnCombinations = new JSONArrayWrapper();
			JSONArrayWrapper catNumAnalysis = null;
			
			if(priorityDate.size() > 0){
				int dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
				catNumAnalysis = doRelationShipAnalysisMultipleNumCols(priorityDate, numerical, priorityDate.size(), dateToNumCorrelMap, n, false);
				for( int i =0; i<catNumAnalysis.length(); i++){
					JSONObjectWrapper catNumobejct = catNumAnalysis.getJSONObject(i);
					catNumobejct.put("dateCol", true);
					catNumobejct.put("dateGrouping", dateGrouping);
					dateColumnCombinations.put(catNumobejct);
				}
			}
			if(priorityDate.size() == 1 && priorityNumerical.size() > 0){
				int dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
				String catColumn = priorityCategorical.get(0);
				List<String> validNumCols = new ArrayList<String>();
				priorityNumerical.forEach(colheader -> {
					if(!irrelevantPriorityColumns.contains(colheader.toUpperCase().replaceAll("\\s+",""))){
						validNumCols.add(colheader);
						JSONObjectWrapper combination = new JSONObjectWrapper();
						combination.put("cat", Arrays.asList(catColumn));
						combination.put("num", Arrays.asList(colheader));
						combination.put("dateCol", true);
						combination.put("dateGrouping", dateGrouping);
						dateColumnCombinations.put(combination);
					}
				});
				if(validNumCols.size() > 1){
					JSONObjectWrapper combination = new JSONObjectWrapper();
					combination.put("cat", Arrays.asList(catColumn));
					combination.put("num", validNumCols);
					combination.put("dateCol", true);
					combination.put("dateGrouping", dateGrouping);
					dateColumnCombinations.put(combination);
				}
			}
			
			if(priorityDate.size()==1 && priorityCategorical.size() ==1){
				int dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
				JSONObjectWrapper catNumObject = catNumAnalysis.getJSONObject(0);
				JSONArrayWrapper numbers = catNumObject.getJSONArray("num");
				if(getTotalUniqueValuesInColumn(priorityDate.get(0)) * getTotalUniqueValuesInColumn(priorityCategorical.get(0)) < this.totalThresholdForAggregation){
					JSONObjectWrapper combination = new JSONObjectWrapper();
					combination.put("cat", Arrays.asList(priorityDate.get(0), priorityCategorical.get(0)));
					combination.put("num", numbers);
					combination.put("dateCol", true);
					combination.put("dateGrouping", dateGrouping);
					dateColumnCombinations.put(combination);
				}
			}
			
			if(priorityDate.size() == 0 && priorityNumerical.size() > 0 && dateCol.size() > 0){
				int dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
				priorityDate.add(dateCol.get(0));
				priorityNumerical.forEach(colheader -> {
					for(int i=0; i<this.priorityDate.size(); i++){
						String catColumn = this.priorityDate.get(i);
						JSONObjectWrapper combination = new JSONObjectWrapper();
						combination.put("cat", Arrays.asList(catColumn));
						combination.put("num", Arrays.asList(colheader));
						combination.put("dateCol", true);
						combination.put("dateGrouping",dateGrouping);
						dateColumnCombinations.put(combination);
					}
				});
			}
			
			return dateColumnCombinations;
		}
		
		private JSONArrayWrapper generateDoubleDepthDateCombinations(){
			JSONArrayWrapper dateColumnCombinations = new JSONArrayWrapper();
			JSONArrayWrapper catNumAnalysis = null;
//			if(priorityDate.size() > 0){
//				int dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
//				catNumAnalysis = doRelationShipAnalysisMultipleNumCols(priorityDate, numerical, priorityDate.size(), dateToNumCorrelMap, n, false);
//				for( int i =0; i<catNumAnalysis.length(); i++){
//					JSONObjectWrapper catNumobejct = catNumAnalysis.getJSONObject(i);
//					catNumobejct.put("dateGrouping", dateGrouping);
//					dateColumnCombinations.put(catNumobejct);
//				}
//			}
			if(priorityDate.size() == 1 && priorityNumerical.size() > 0 && categorical.size() > 0){
				int dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
				String catColumn = priorityCategorical.size() > 0 ? priorityCategorical.get(0) : categorical.get(0);
				List<String> validNumCols = new ArrayList<String>();
				priorityNumerical.forEach(colheader -> {
					if(!irrelevantPriorityColumns.contains(colheader.toUpperCase().replaceAll("\\s+",""))){
						validNumCols.add(colheader);
						JSONObjectWrapper combination = new JSONObjectWrapper();
						combination.put("cat", Arrays.asList(catColumn));
						combination.put("num", Arrays.asList(colheader));
						combination.put("dateCol", true);
						combination.put("dateGrouping", dateGrouping);
						dateColumnCombinations.put(combination);
					}
				});
				if(validNumCols.size() > 1){
					JSONObjectWrapper combination = new JSONObjectWrapper();
					combination.put("cat", Arrays.asList(catColumn));
					combination.put("num", validNumCols);
					combination.put("dateCol", true);
					dateColumnCombinations.put(combination);
				}
			}
			
			return dateColumnCombinations;
		}
				
	}
	
	private final class DatasetProperties {
		int parentUniqueValuesForAggregation;
		int childUniqueValuesForAggregation;
		int uniqueValuesForAggFilter;
		int uniqueValuesForNormalFilter;
		int datasetCatUniqueValuesLimit;
		int datasetNumUniqueValuesLimit;
		int uniqueValuesThresholdForDataset = 50;
		int countUniquesThreshold = 50;
		int unqiueValuesThresholdForCount;
		int minimumValuesThreshold = 5;
		boolean generateOnlyCountRecs = false;
		boolean generateOnlyNumRecs = false;
		int recommendationLimit = 2;
		DatasetNature datasetNature;
		DatasetSize datasetSize;
		
		public DatasetProperties(DatasetNature datasetNature, DatasetSize datasetSize){
			this.datasetNature = datasetNature;
			this.datasetSize = datasetSize;
			computeBestConditionsForDataset();
		}
		
		public void computeBestConditionsForDataset(){
			switch(this.datasetSize){
				case small:
//					int uniqueValuesLimit = totalRows/4;
					int uniqueValuesLimit = totalRows/2;
					int countUnqiueValues = totalRows/3;
					minimumValuesThreshold = Math.min(minimumValuesThreshold, totalRows-1);
					this.parentUniqueValuesForAggregation = (uniqueValuesLimit < minimumValuesThreshold) ? minimumValuesThreshold : uniqueValuesLimit;   
					this.childUniqueValuesForAggregation = this.parentUniqueValuesForAggregation;
					this.unqiueValuesThresholdForCount = (countUnqiueValues < minimumValuesThreshold) ?  minimumValuesThreshold : uniqueValuesLimit; 
					break;
				case medium:
//					uniqueValuesLimit = totalRows/40;
					uniqueValuesLimit = totalRows/20;
					countUnqiueValues = totalRows/30;
					this.parentUniqueValuesForAggregation = (uniqueValuesLimit < uniqueValuesThresholdForDataset) ? uniqueValuesThresholdForDataset : uniqueValuesLimit;   
					this.childUniqueValuesForAggregation = this.parentUniqueValuesForAggregation;
					this.unqiueValuesThresholdForCount = (countUnqiueValues < countUniquesThreshold) ?  countUniquesThreshold : countUnqiueValues;
					break;
				case large:
//					uniqueValuesLimit = totalRows/400;
					uniqueValuesLimit = totalRows/200;
					countUnqiueValues = totalRows/300;
					this.parentUniqueValuesForAggregation = (uniqueValuesLimit < uniqueValuesThresholdForDataset) ? uniqueValuesThresholdForDataset : uniqueValuesLimit;   
					this.childUniqueValuesForAggregation = this.parentUniqueValuesForAggregation;
					this.unqiueValuesThresholdForCount = (countUnqiueValues < countUniquesThreshold) ?  countUniquesThreshold : countUnqiueValues;
					break;
			}
			switch(this.datasetNature) {
				case highlyCategorical:
					datasetCatUniqueValuesLimit = 1;
					datasetNumUniqueValuesLimit = 1;
					break;
				case highlyNumerical:
					datasetCatUniqueValuesLimit = 1;
					datasetNumUniqueValuesLimit = 2;
					break;
				case balanced:
					datasetCatUniqueValuesLimit = 1;
					datasetNumUniqueValuesLimit = 1;
					break;
			}
			this.uniqueValuesForAggFilter = 15;
			this.uniqueValuesForNormalFilter = 30;
			if(noNumberColumns){
				generateOnlyCountRecs = true;
			}else if(noCategoryColumns){
				generateOnlyNumRecs = true;
			}
			if(generateOnlyCountRecs || generateOnlyNumRecs){
				recommendationLimit = 4;
			}
		}
	}
	
	public String getDateColumn() {
		return dateColumn;
	}
	public void setDateColumn(String dateColumn) {
		this.dateColumn = dateColumn;
	}
	public Map<String, Double> getNumToNumCorrelMap() {
		return numToNumCorrelMap;
	}
	public void setNumToNumCorrelMap(Map<String, Double> numToNumCorrelMap) {
		this.numToNumCorrelMap = numToNumCorrelMap;
	}
	public Map<String, JSONArrayWrapper> getCatToNumCorrelMap() {
		return catToNumCorrelMap;
	}
	public void setCatToNumCorrelMap(Map<String, JSONArrayWrapper> catToNumCorrelMap) {
		this.catToNumCorrelMap = catToNumCorrelMap;
	}
	public Map<String, Double> getCatToCatCorrelMap() {
		return catToCatCorrelMap;
	}
	public void setCatToCatCorrelMap(Map<String, Double> catToCatCorrelMap) {
		this.catToCatCorrelMap = catToCatCorrelMap;
	}
	public Map<String, Double> getCorrelationMap() {
		return correlationMap;
	}
	public void setCorrelationMap(Map<String, Double> correlationMap) {
		this.correlationMap = correlationMap;
	}
	public JSONArrayWrapper getCombinations() {
		return combinations;
	}
	public void setCombinations(JSONArrayWrapper combinations) {
		this.combinations = combinations;
	}
	public Sheet getSheet() {
		return sheet;
	}
	public void setSheet(Sheet sheet) {
		this.sheet = sheet;
	}
	public Map getLookupTable() {
		return lookupTable;
	}
	public void setLookupTable(Map lookupTable) {
		this.lookupTable = lookupTable;
	}
	public List<String> getSemiColumns() {
		return semiColumns;
	}
	public void setSemiColumns(List<String> semiColumns) {
		this.semiColumns = semiColumns;
	}
	public List<String> getCategorical() {
		return categorical;
	}
	public void setCategorical(List<String> categorical) {
		this.categorical = categorical;
	}
	public List<String> getNumerical() {
		return numerical;
	}
	public void setNumerical(List<String> numerical) {
		this.numerical = numerical;
	}
	public List<String> getDateCol() {
		return dateCol;
	}
	public void setDateCol(List<String> dateCol) {
		this.dateCol = dateCol;
	}
	public Map<String, Integer> getDaysBetween() {
		return daysBetween;
	}
	public void setDaysBetween(Map<String, Integer> daysBetween) {
		this.daysBetween = daysBetween;
	}
	public  Date getMaxDate(){
		return maxDate;
	}
	public void setMaxDate(Date maxDate){
		this.maxDate = maxDate;
	}
	public  Date getMinDate(){
		return minDate;
	}
	public void setMinDate(Date minDate){
		this.minDate = minDate;
	}
	public Map<String, Double> getCorrelMap() {
		return correlMap;
	}
	public void setCorrelMap(Map<String, Double> correlMap) {
		this.correlMap = correlMap;
	}
	public Map<String, Double> getSkewnessQuotient() {
		return skewnessQuotient;
	}
	public void setSkewnessQuotient(Map<String, Double> skewnessQuotient) {
		this.skewnessQuotient = skewnessQuotient;
	}
	public Map<Integer, JSONArrayWrapper> fallBackModel(List<String> categorical, List<String> numerical, List<String> date, Map lookupTable, Sheet sheet){
		this.lookupTable = lookupTable;
		this.sheet = sheet;
		Map<Integer, JSONArrayWrapper> finalSortedCombinations = new HashMap<Integer, JSONArrayWrapper>();
		if(numerical != null && numerical.size() > 0){
			List<String> tempNumerical = new CopyOnWriteArrayList<String>();
			tempNumerical.addAll(numerical);
			tempNumerical.forEach(colheader -> {
				if(colheader.toUpperCase().contains("ID") || colheader.toUpperCase().contains("NO")){
					tempNumerical.remove(colheader);
				}
			});
			
			numerical.clear();
			numerical.addAll(tempNumerical);
		}
		datasetSize();
		JSONArrayWrapper combinations = new JSONArrayWrapper();
		if(categorical.size()>0){
			combinations = generateSimpleCombinations(categorical, numerical);
		}else if(date.size() > 0){
			combinations = generateSimpleCombinations(date, numerical);
		}
		finalSortedCombinations.put(ZiaConstants.ZiaResponseConstants.CHARTS, combinations);
		return finalSortedCombinations;
	}
	
	private boolean checkIfDataSuitableForAggregation(String colheader){
		boolean suitabelForAggregation = false;
		int uqValues = getTotalUniqueValuesInColumn(colheader);
		if(this.seriesIn.equals("COLS") && this.totalRows > 10){
			suitabelForAggregation = (uqValues < 0.5*this.totalRows);
		}else if(this.seriesIn.equals("ROWS") && this.totalColumns > 10){
			suitabelForAggregation = (uqValues < 0.5*this.totalColumns);
		}
		return suitabelForAggregation;
	}
	private JSONArrayWrapper generateSimpleCombinations(List<String> categorical, List<String> numerical){
		JSONArrayWrapper combinations = new JSONArrayWrapper();
		AtomicInteger index = new AtomicInteger(0);
		if(numerical.size()>0){
			categorical.forEach(category->{
				if(index.get() < 2){
					boolean isSuitableForAggregation = checkIfDataSuitableForAggregation(category);
					JSONObjectWrapper catNumObject = new JSONObjectWrapper();
					catNumObject.put("cat", Arrays.asList(category));
					catNumObject.put("num", numerical);
					if(isSuitableForAggregation){
						catNumObject.put("aggregatedChart", true);
						String operation = "SUM";	//No I18N
						catNumObject.put("operation", Arrays.asList(operation));
					}else{
						catNumObject.put("normalChart", true);
					}
					
//					if(this.getTotalUniqueValuesInColumn(category) > 15){
//						JSONObjectWrapper filter = new JSONObjectWrapper();
//						filter.put("type", "TOP");
//						filter.put("limit", 5);
//						filter.put("limitBy", "");
//						catNumObject.put("filter", filter);
//					}
					combinations.put(catNumObject);
				}
				index.getAndIncrement();
			});
		}
		
		index.set(0);
		categorical.forEach(category -> {
			if(index.get() < 2){
				JSONObjectWrapper catNumObject = new JSONObjectWrapper();
				catNumObject.put("cat", Arrays.asList(category));
				catNumObject.put("aggregatedChart", true);
				String operation = "COUNT";	//No I18N
				catNumObject.put("operation", Arrays.asList(operation));
//				if(this.getTotalUniqueValuesInColumn(category) > 15){
//					JSONObjectWrapper filter = new JSONObjectWrapper();
//					filter.put("type", "TOP");
//					filter.put("limit", 5);
//					filter.put("limitBy", "");
//					catNumObject.put("filter", filter);
//				}
				combinations.put(catNumObject);
			}
			index.getAndIncrement();
		});
		return combinations;
	}
	private List<String> setPriorityDetails(List<Range> priorityColRanges, List<Range> priorityRowRanges){
		List<String> priorityColumns = new ArrayList<String>();
		if(this.seriesIn.equals("COLS")){
			if(priorityColRanges != null){
				priorityColRanges.forEach(range -> {
					String firstCellValue = NLPUtil.getCellValue(this.sheet, range);
					priorityColumns.add(firstCellValue);
				});
			}
		}else{
			if(priorityRowRanges != null){
				priorityRowRanges.forEach(range -> {
					String firstCellValue = NLPUtil.getCellValue(this.sheet, range);
					priorityColumns.add(firstCellValue);
				});
			}
		}
		return priorityColumns;
	}
	
	
	private List<String> setFilterColumnDetails(List<Range> filterColumn){
		List<String> filterColumns = new ArrayList<String>();
		if(filterColumn != null){
			filterColumn.forEach(range -> {
				String firstCellValue = NLPUtil.getCellValue(this.sheet, range);
				filterColumns.add(firstCellValue);
			});
		}
		return filterColumns;
	}
	public Map<Integer, JSONArrayWrapper> getSampleCombinations(List originalCategorical, List numerical, List dateCol, NLInteractorImpl n,
			Sheet sheet,Map lookUpTable, Range tableRange, List semiColumns, List<Range> priorityColRanges, List<Range> priorityRowRanges, 
			List<Range> filterColumns){
		this.lookupTable = lookUpTable;
		this.sheet = sheet;
		this.dateCol = dateCol;
		this.n = n;
		this.priorityColumnsSelected = ((priorityColRanges!= null && priorityColRanges.size() > 0) ||(priorityRowRanges != null && priorityRowRanges.size() > 0));
		datasetSize();
		preProcess(n, semiColumns, originalCategorical, numerical);
		generalInformationAboutData();
		highlySkewedNumColumn = getHighestSkewedColumn(n);
		categorizeMyDataSet();
		postProcess();
		List<String> priorityColumns = setPriorityDetails(priorityColRanges, priorityRowRanges);
		List<String> filterCols = setFilterColumnDetails(filterColumns);
		if(priorityColumns != null && priorityColumns.size() > 0){
			this.priorityColumns = new PriorityColumns();
			this.priorityColumns.relaxUniqueValuesConditionsForPriorityColumns();
			this.priorityColumns.segregatePriorityColumnsBasedOnAnalysis(priorityColumns, this.categorical, "categorical");	//No I18N
			this.priorityColumns.segregatePriorityColumnsBasedOnAnalysis(priorityColumns, this.numerical, "numerical");	//No I18N
			this.priorityColumns.segregatePriorityColumnsBasedOnAnalysis(priorityColumns, this.semiColumns, "semi");	//No I18N
			this.priorityColumns.segregatePriorityColumnsBasedOnAnalysis(priorityColumns, null, "skip");	//No I18N
			this.priorityColumns.segregatePriorityColumnsBasedOnAnalysis(priorityColumns, this.dateCol, "date");	//No I18N
			this.priorityColumns.analyzeNLPDataAndGenerateCombinations(n);
			this.combinations = this.priorityColumns.priorityCombinations;
		}else if(filterCols != null && filterCols.size() > 0){
			this.filterColumns = new FilterColumns();
			this.filterColumns.relaxUniqueValuesConditionsForFilterColumns();
			this.filterColumns.segregateFilterColumnsBasedOnAnalysis(filterCols, this.categorical, "categorical");	//No I18N
			this.filterColumns.segregateFilterColumnsBasedOnAnalysis(filterCols, this.numerical, "numerical");	//No I18N
			this.filterColumns.segregateFilterColumnsBasedOnAnalysis(filterCols, this.semiColumns, "semi");	//No I18N
			this.filterColumns.segregateFilterColumnsBasedOnAnalysis(filterCols, null, "skip");	//No I18N
			this.filterColumns.segregateFilterColumnsBasedOnAnalysis(filterCols, this.dateCol, "date");	//No I18N
			this.filterColumns.analyzeNLPDataAndGenerateCombinations(n);
			this.combinations = this.filterColumns.filterCombinations;
		}else{
			analyzeNLPDataAndGenerateCombinations(n);
		}
		normalChartRecommendations();
		aggregationCombinations(n);
		filterModule();
		JSONArrayWrapper sortedCombinations = sortCombinations();
		Map<Integer, JSONArrayWrapper> finalSortedCombinations = new HashMap<Integer, JSONArrayWrapper>();
		finalSortedCombinations.put(ZiaConstants.ZiaResponseConstants.MIXED, new JSONArrayWrapper());
		finalSortedCombinations.put(ZiaConstants.ZiaResponseConstants.CHARTS, new JSONArrayWrapper());
		for(int i = 0, count=0; i<sortedCombinations.length() ; i++, count++){
			JSONObjectWrapper combo = sortedCombinations.getJSONObject(i);
			if(combo.has("aggregatedChart")){
				finalSortedCombinations.get(ZiaConstants.ZiaResponseConstants.MIXED).put(combo);
			}else{
				finalSortedCombinations.get(ZiaConstants.ZiaResponseConstants.CHARTS).put(combo);
			}
		}
		return finalSortedCombinations;
	}
	private void postProcess(){
		// removing unwanted categorical columns.
			List<String> tempCat = new CopyOnWriteArrayList<String>();
			tempCat.addAll(this.categorical);
			tempCat.forEach(colHeader -> {
				if(getTotalUniqueValuesInColumn(colHeader) <= this.getDatasetProperty().datasetCatUniqueValuesLimit){
					tempCat.remove(colHeader);
				}
			});
			List<String> semis = new CopyOnWriteArrayList<String>();
			semis.addAll(this.semiColumns);
			semis.forEach(colheader -> {
				if(colheader.toString().toUpperCase().contains("YEAR") || colheader.toString().toUpperCase().contains("MONTH") || 	//No I18N
				colheader.toString().toUpperCase().contains("QUARTER")){	//No I18N
					if(getTotalUniqueValuesInColumn(colheader) <= this.getDatasetProperty().datasetCatUniqueValuesLimit){
						semis.remove(colheader);
					}
				}
			});
			this.categorical.clear();
			this.categorical.addAll(tempCat);
			this.semiColumns.clear();
			this.semiColumns.addAll(semis);
			if(this.semiColumns != null){
				this.semiColumns.forEach(colheader -> {
					JSONArrayWrapper tempArray = new JSONArrayWrapper();
					this.numerical.forEach(numberColumn -> {
						String correlKey = colheader+NLP_CORREL_DELIMITER+numberColumn;
						JSONObjectWrapper tempObject = new JSONObjectWrapper();
						tempObject.put("name", numberColumn);
						if(!Double.isNaN(correlationMap.get(correlKey))){
							tempObject.put("corr", correlationMap.get(correlKey));
						}
						else{
							tempObject.put("corr", 0);
						}
						tempArray.put(tempObject);
					});
					sortArray(tempArray);
					this.catToNumCorrelMap.put(colheader, tempArray);
				});
			}
			
	}
	private void categorizeMyDataSet(){
		DatasetProperties datasetProperties = null;
		DatasetSize datasetSize = null;
		DatasetNature datasetNature = null;
		if(this.totalRows < 100){
			datasetSize = DatasetSize.small;
		}else if(this.totalRows >= 100 && this.totalRows < 1000){
			datasetSize = DatasetSize.medium;
		}else if(this.totalRows >= 1000){
			datasetSize = DatasetSize.large;
		}
		int totalDateColumns = (dateColumn != null) ? 1 : 0;
		//int totalDateColumns =  0;
		long totalValidsemiColumns = this.semiColumns.parallelStream().filter(colheader -> colheader.contains("YEAR") || colheader.contains("MONTH") || colheader.contains("QUARTER")	//No I18N
		).count();
		int totalCategoricalColumns = categorical.size() + totalDateColumns + (int)totalValidsemiColumns;
		int totalNumberColumns = numerical.size();
		int diff = Math.abs(totalCategoricalColumns - totalNumberColumns);
		if(totalCategoricalColumns > totalNumberColumns && diff > totalCategoricalColumns/2) {
			datasetNature = DatasetNature.highlyCategorical;
		}else if(totalNumberColumns > totalCategoricalColumns && diff > totalNumberColumns/2) {
			datasetNature = DatasetNature.highlyNumerical;
		}else {
			datasetNature = DatasetNature.balanced;
		}
		
		datasetProperties = new DatasetProperties(datasetNature, datasetSize);
		this.setDatasetProperty(datasetProperties);
	}
	private void preProcess(NLInteractorImpl n, List semiColumns, List originalCatgeorical, List numericColumns){
		List<String> colHeaderSkipList = n.getCodeGenerator().getColumnHeaderSkipList();
		// remove skip headers
		colHeaderSkipList.forEach(colHeader ->{
			if(colHeader.toUpperCase().contains("YEAR") || colHeader.toUpperCase().contains("MONTH") || colHeader.toUpperCase().contains("QUARTER")) {
				semiColumns.add(colHeader);
			}
			originalCatgeorical.remove(colHeader);
			numericColumns.remove(colHeader);
		});
		if(this.priorityColumnsSelected){
			List tempNumbers = new CopyOnWriteArrayList<>();
			tempNumbers.addAll(numericColumns);
			tempNumbers.forEach(colHeader -> {
				boolean catColumn = checkIfValidCatgeoricalColumn(colHeader.toString());
				if(catColumn){
					semiColumns.add(colHeader);
					tempNumbers.remove(colHeader);
				}
				
			});
			numericColumns.clear();
			numericColumns.addAll(tempNumbers);
		}
		
		Map<String, Double> correlationMap = n.getCodeGenerator().getCorrelationMap();
		this.correlationMap = correlationMap;
		this.categorical=originalCatgeorical;
		this.numerical = numericColumns;
		
		this.semiColumns = semiColumns;

		// date API
		Map<String, Integer> dateMap = n.getCodeGenerator().getNumberOfDays();
		dateMap = dateMap.entrySet().stream().sorted(Map.Entry.comparingByValue(Collections.reverseOrder())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
		setDaysBetween(dateMap);
		setMinDate(n.getCodeGenerator().getMinDate());
		setMaxDate(n.getCodeGenerator().getMaxDate());
		List<String> dateList = new CopyOnWriteArrayList<>(dateMap.keySet());
		String highestValKey = (dateList!= null && dateList.size()>0)?dateList.get(0):null;
		dateList.forEach(colheader -> {
			if(getTotalUniqueValuesInColumn(colheader) <= 1){
				dateList.remove(colheader);
			}
		});
		this.dateCol = dateList;
		if(dateList.size() > 0){
			this.setDateColumn(highestValKey);
		}
	}
	private JSONArrayWrapper generateNumberColumnCombinations(){
		JSONArrayWrapper numberColumnCombinations = new JSONArrayWrapper();
		if(numerical.size()>1){
			String numToNumCombination = new ArrayList<String>(this.numToNumCorrelMap.keySet()).get(0);
			String[] numbers = numToNumCombination.split(NLP_CORREL_DELIMITER);
			JSONObjectWrapper combination = new JSONObjectWrapper();
			String numberColumn = !this.highlySkewedNumColumn.isEmpty()?this.highlySkewedNumColumn:numbers[0];
			combination.put("num", Arrays.asList(numberColumn));
			numberColumnCombinations.put(combination);
			if(this.datasetProperty.datasetNature.name().equals("highlyNumerical") && this.numToNumCorrelMap.size()>1){
				for(int i=1; i<2; i++){
					numToNumCombination = new ArrayList<String>(this.numToNumCorrelMap.keySet()).get(i);
					numbers = numToNumCombination.split(NLP_CORREL_DELIMITER);
					combination = new JSONObjectWrapper();
					numberColumn = !this.highlySkewedNumColumn.isEmpty()?this.highlySkewedNumColumn:numbers[0];
					combination.put("num", Arrays.asList(numberColumn));
					numberColumnCombinations.put(combination);
				}
			}
		}else if(this.numerical.size()==1){
			JSONObjectWrapper combination = new JSONObjectWrapper();
			String numberColumn = this.numerical.get(0);
			combination.put("num", Arrays.asList(numberColumn));
			numberColumnCombinations.put(combination);
		}
		return numberColumnCombinations;
	}
	private JSONArrayWrapper generateCountColumnCombinations() {
		//final int maxlimit = this.datasetProperty.datasetNature.name().equals("highlyCategorical") ? 2 : 1;
		final int maxlimit = this.datasetProperty.recommendationLimit;
		int datasetSize = Math.min(this.totalRows-1, 10);
		int totaluniqueValuesAllowed = Math.max(this.datasetProperty.unqiueValuesThresholdForCount, datasetSize);
		final boolean vhighlyCatgeorical = (categorical.size() > 4)?true:false;
		JSONArrayWrapper countCombinations = new JSONArrayWrapper();
		if(categorical.size()>0){
			AtomicInteger index = new AtomicInteger(0);
			categorical.forEach(colheader -> {
				if(index.get() < maxlimit){
					int uqValues = getTotalUniqueValuesInColumn(colheader);
					if(uqValues < totaluniqueValuesAllowed){
						if(vhighlyCatgeorical){
							//if(index.get() == 0 || uqValues > 3){
								JSONObjectWrapper combination = new JSONObjectWrapper();
								if(uqValues <= 3){  
									combination.put("lowWeight", true);
								}
								combination.put("cat", Arrays.asList(colheader));
								countCombinations.put(combination);
								index.getAndIncrement();
							//}
						}else{
							JSONObjectWrapper combination = new JSONObjectWrapper();
							if(uqValues <= 3){  
								combination.put("lowWeight", true);
							}
							combination.put("cat", Arrays.asList(colheader));
							countCombinations.put(combination);
							index.getAndIncrement();
						}
					}
				}
			});
		}
		return countCombinations;
	}
	private JSONArrayWrapper generateDoubleDepthIndexCombinations(NLInteractorImpl n){
		JSONArrayWrapper doubleDepthIndexCombos = new JSONArrayWrapper();
		int maxDoubleDepthIndexCategoriesThreshold = this.datasetProperty.uniqueValuesThresholdForDataset;
		int parentDoubleDepthIndexThreshold = (this.datasetProperty.parentUniqueValuesForAggregation/2<this.datasetProperty.minimumValuesThreshold)? this.datasetProperty.minimumValuesThreshold: this.datasetProperty.parentUniqueValuesForAggregation/2;
		int childDoubleDepthIndexThreshold = this.datasetProperty.childUniqueValuesForAggregation;
			if(numerical.size()>0 && categorical.size()>1){
				JSONArrayWrapper catNumAnalysis = doRelationShipAnalysisMultipleNumCols(categorical, numerical, categorical.size(), this.catToNumCorrelMap, n, false);
				int count = 0;
				JSONObjectWrapper doubleDepthIndex = new JSONObjectWrapper();
				if(catNumAnalysis.length() < 2){
					return doubleDepthIndexCombos;
				}
				for(int i = 0;i< catNumAnalysis.length() && count < 2; i++){
					JSONObjectWrapper catNumObject = catNumAnalysis.getJSONObject(i);
					int totalUnqiueValues = catNumObject.getInt("uniqueValues");
					if(doubleDepthIndex.isEmpty() && totalUnqiueValues > parentDoubleDepthIndexThreshold){
						continue;
					}else{
						if(!doubleDepthIndex.isEmpty()){
							if(totalUnqiueValues < childDoubleDepthIndexThreshold && 
									doubleDepthIndex.getInt("uniqueValues")*totalUnqiueValues <= maxDoubleDepthIndexCategoriesThreshold){
								doubleDepthIndex.getJSONArray("cat").put(catNumObject.getJSONArray("cat").get(0));
								//doubleDepthIndex.getJSONArray("num").put(catNumObject.getJSONArray("num").get(0));
								doubleDepthIndex.put("uniqueValues", doubleDepthIndex.getInt("uniqueValues")*totalUnqiueValues);
							}else{
								doubleDepthIndex = new JSONObjectWrapper();
							}
						}else{
							doubleDepthIndex.put("cat", catNumObject.get("cat"));
							doubleDepthIndex.put("num", catNumObject.get("num"));
							doubleDepthIndex.put("corr", catNumObject.get("corr"));
							doubleDepthIndex.put("uniqueValues", catNumObject.get("uniqueValues"));
						}
						
					}
					count++;
				}
				if(!doubleDepthIndex.isEmpty()){
					doubleDepthIndexCombos.put(doubleDepthIndex);
				}
			}
			return doubleDepthIndexCombos;
	}
	private JSONArrayWrapper generateSingleDepthIndexCombinations(NLInteractorImpl n, boolean generateAll){
		JSONArrayWrapper singleDepthIndexCombinations = new JSONArrayWrapper();
		int maxlimit = (generateAll)?3:2;
		if(numerical.size()>0 && categorical.size()>0){
			JSONArrayWrapper catNumAnalysis = doRelationShipAnalysisMultipleNumCols(categorical, numerical, maxlimit, this.catToNumCorrelMap, n, generateAll);
			for(int i=0, count = 0; i<catNumAnalysis.length() && count<maxlimit; i++){
				singleDepthIndexCombinations.put(catNumAnalysis.getJSONObject(i));
				count++;
			}
			if(!singleDepthIndexCombinations.isEmpty() && singleDepthIndexCombinations.length()>1){
				sortArray(singleDepthIndexCombinations);
			}
		}
		return singleDepthIndexCombinations;
	}
	
	
	private JSONArrayWrapper generateSingleDepthHighWeightedCombinations(NLInteractorImpl n){
		JSONArrayWrapper singleDepthIndexCombinations = new JSONArrayWrapper();
		if(numerical.size()>0 && categorical.size()>0){
			JSONArrayWrapper catNumAnalysis = doRelationShipAnalysisMultipleNumCols(categorical, numerical, 3, this.catToNumCorrelMap, n, false);
			if(catNumAnalysis.length() > 2){
				singleDepthIndexCombinations.put(catNumAnalysis.getJSONObject(2));
			}
		}
		return singleDepthIndexCombinations;
	}
	
	private JSONObjectWrapper generateNormalChartDateCombinations(JSONObjectWrapper dateColumnCombination){
		JSONObjectWrapper catNumObject = new JSONObjectWrapper();
		catNumObject.put("cat", Arrays.asList(this.getDateColumn()));
		catNumObject.put("num", dateColumnCombination.getString("num"));
		return catNumObject;
	}
	
	private JSONArrayWrapper generateDateColumnCombinations(int doubleDepthIndexLength, NLInteractorImpl n, boolean generateAll){
		int maxDoubleDepthIndexCategoriesThreshold = this.datasetProperty.uniqueValuesThresholdForDataset;
		int parentDoubleDepthIndexThreshold = (this.datasetProperty.parentUniqueValuesForAggregation/2<this.datasetProperty.minimumValuesThreshold)? this.datasetProperty.minimumValuesThreshold: this.datasetProperty.parentUniqueValuesForAggregation/2;
		int childDoubleDepthIndexThreshold = this.datasetProperty.childUniqueValuesForAggregation;
		JSONArrayWrapper dateColumnCombinations = new JSONArrayWrapper();
		// Date columns
		if(dateCol.size()>0 && numerical.size()>0 && this.daysBetween != null && this.getDateColumn() != null){
			int dateGrouping = AggregationConstants.NONE;
			if(getMinDate()!= null &&  getMaxDate() != null){
				dateGrouping = NLPUtil.getDateGrouping(getMinDate(), getMaxDate());//getDateGrouping(this.daysBetween.get(this.getDateColumn()));
			}
			 
			int totalUniqueValuesInDate = getUnqiueValuesInDate(this.daysBetween.get(this.getDateColumn()));
			dateGrouping = dateGrouping==14 ? 2 : dateGrouping;
		    dateGrouping = dateGrouping==15 ? 5 : dateGrouping;
		    JSONArrayWrapper catNumAnalysis = doRelationShipAnalysisMultipleNumCols(this.dateCol, numerical, dateCol.size(), this.dateToNumCorrelMap,n, generateAll);
		    if(totalUniqueValuesInDate > this.datasetProperty.parentUniqueValuesForAggregation){
		    	JSONObjectWrapper dateCombintaion = generateNormalChartDateCombinations(catNumAnalysis.getJSONObject(0));
		    	dateColumnCombinations.put(dateCombintaion);
		    	return dateColumnCombinations;
		    }
			
			
			if(generateAll){
				for(int i=0;i<3;i++){
					JSONObjectWrapper combination = new JSONObjectWrapper();
					combination.put("dateGrouping", dateGrouping);
					combination.put("cat", Arrays.asList(this.getDateColumn()));
					combination.put("dateCol", true);
					JSONObjectWrapper catNumObject = catNumAnalysis.getJSONObject(i);
					catNumObject.put("cat", Arrays.asList(this.getDateColumn()));
					combination.put("num", catNumObject.get("num"));
					dateColumnCombinations.put(combination);
				}
				return dateColumnCombinations;
			}
			JSONObjectWrapper combination = new JSONObjectWrapper();
			combination.put("dateGrouping", dateGrouping);
			combination.put("cat", Arrays.asList(this.getDateColumn()));
			combination.put("dateCol", true);
			JSONObjectWrapper catNumObject = catNumAnalysis.getJSONObject(0);
			catNumObject.put("cat", Arrays.asList(this.getDateColumn()));
			combination.put("num", catNumObject.get("num"));
			dateColumnCombinations.put(combination);
			if(doubleDepthIndexLength == 0 && categorical.size() > 0 && totalUniqueValuesInDate < parentDoubleDepthIndexThreshold){
				JSONObjectWrapper ddCombination = new JSONObjectWrapper();
				catNumAnalysis = doRelationShipAnalysisMultipleNumCols(categorical, numerical, categorical.size(), this.catToNumCorrelMap, n, false);
				catNumObject = catNumAnalysis.getJSONObject(0);
				int catUniqueValues = catNumObject.getInt("uniqueValues");
				if(catUniqueValues < childDoubleDepthIndexThreshold && 
						totalUniqueValuesInDate*catUniqueValues < maxDoubleDepthIndexCategoriesThreshold){
					ddCombination.put("cat", Arrays.asList(this.getDateColumn(), catNumObject.getJSONArray("cat").get(0)));
					ddCombination.put("num", catNumObject.get("num"));
					ddCombination.put("uniqueValues", totalUniqueValuesInDate*catUniqueValues);
					ddCombination.put("dateGrouping", dateGrouping);
					ddCombination.put("dateCol", true);
					//dateColumnCombinations = new JSONArrayWrapper();
					dateColumnCombinations.put(ddCombination);
				}
			}
		}
		return dateColumnCombinations;
	}
	private JSONArrayWrapper generateSemiColumnCombinations(NLInteractorImpl n)  {
		JSONArrayWrapper semiCombinations = new JSONArrayWrapper();
			List<String> semis = new ArrayList<String>();
			if(this.semiColumns != null) {
				this.semiColumns.forEach(semiColumn -> {
					if(semiColumn.toUpperCase().contains("YEAR") || semiColumn.toUpperCase().contains("QUARTER") || semiColumn.toUpperCase().contains("MONTH")){
						semis.add(semiColumn);
					}
				});
			}
			if(semis.size()> 0 && this.numerical.size()>0){
				JSONArrayWrapper semiNumAnalysis = doRelationShipAnalysisMultipleNumCols(semis, numerical, semis.size(), this.catToNumCorrelMap, n, false);
				for(int i = 0;i< semiNumAnalysis.length(); i++){
					JSONObjectWrapper semiNumObject =  semiNumAnalysis.getJSONObject(i);
					semiNumObject.put("semi", true);
					//this.combinations.put(semiNumObject);
					semiCombinations.put(semiNumObject);
				}
			}
			return semiCombinations;
	}
	private void filterCombinationsBasedOnDatasetNature(JSONArrayWrapper numberColumnCombinations, JSONArrayWrapper countColumnCombinations, JSONArrayWrapper doubleDepthIndexCombinations,
			JSONArrayWrapper singleDepthIndexCombinations, JSONArrayWrapper dateColumnCombinations, JSONArrayWrapper semiColumnCombinations, JSONArrayWrapper singleDepthIndexHighWeightedCombinations){
		switch(this.datasetProperty.datasetNature){
			case balanced:
				if(numberColumnCombinations.length() > 0){
					this.combinations.put(numberColumnCombinations.get(0));
				}
				if(countColumnCombinations.length() > 0){
					this.combinations.put(countColumnCombinations.get(0));
				}
				if(doubleDepthIndexCombinations.length() > 0){
					this.combinations.put(doubleDepthIndexCombinations.get(0));
				}
				for(int i=0;i< singleDepthIndexCombinations.length(); i++){
					if(!singleDepthIndexCombinations.getJSONObject(i).has("skip")){
						this.combinations.put(singleDepthIndexCombinations.get(i));
					}
				}
				if(singleDepthIndexHighWeightedCombinations.length() > 0 && singleDepthIndexCombinations.length() > 0){
					if(singleDepthIndexHighWeightedCombinations.getJSONObject(0).getDouble("corr") >= singleDepthIndexCombinations.getJSONObject(0).getDouble("corr")) {
						this.combinations.put(singleDepthIndexHighWeightedCombinations.get(0));
					}
				}
				if(dateColumnCombinations.length() > 0){
					this.combinations.put(dateColumnCombinations.get(0));
				}
				if(doubleDepthIndexCombinations.length() == 0){
					if(semiColumnCombinations.length() > 0){
						this.combinations.put(semiColumnCombinations.get(0));
					}
				}
				
			break;
			case highlyCategorical:
				int totalCombinations = countColumnCombinations.length() + singleDepthIndexCombinations.length() + numberColumnCombinations.length() + dateColumnCombinations.length();
				if(totalCombinations < 4){
					for(int i=0;i<countColumnCombinations.length(); i++){
						this.combinations.put(countColumnCombinations.get(i));
					}
					for(int i=0;i< singleDepthIndexCombinations.length(); i++){
						this.combinations.put(singleDepthIndexCombinations.get(i));
					}
					if(doubleDepthIndexCombinations.length()> 0){
						this.combinations.put(doubleDepthIndexCombinations.get(0));
					}
					if(numberColumnCombinations.length()>0){
						this.combinations.put(numberColumnCombinations.get(0));
					}
					
					if(dateColumnCombinations.length()>0){
						this.combinations.put(dateColumnCombinations.get(0));
					}
					if(semiColumnCombinations.length()>0){
						this.combinations.put(semiColumnCombinations.get(0));
					}
				}else{
					if(numberColumnCombinations.length() > 0){
						this.combinations.put(numberColumnCombinations.get(0));
					}
					if(countColumnCombinations.length() > 0){
						for(int i=0;i<countColumnCombinations.length(); i++){
							this.combinations.put(countColumnCombinations.get(i));
						}
					}
					if(doubleDepthIndexCombinations.length() > 0){
						this.combinations.put(doubleDepthIndexCombinations.get(0));
					}
					if(singleDepthIndexCombinations.length() > 0){
						this.combinations.put(singleDepthIndexCombinations.get(0));
						if(singleDepthIndexCombinations.length() > 1){
							JSONObjectWrapper averageOperation =  singleDepthIndexCombinations.getJSONObject(1);
							if(averageOperation.has("operationToApply") && averageOperation.getString("operationToApply").equals("average")){
								this.combinations.put(averageOperation);
							}
						}
					}
					if(dateColumnCombinations.length() > 0){
						this.combinations.put(dateColumnCombinations.get(0));
					}
					if(doubleDepthIndexCombinations.length() == 0 && semiColumnCombinations.length() > 0){
						this.combinations.put(semiColumnCombinations.get(0));
					}
				}
			break;
			case highlyNumerical:
				totalCombinations = numberColumnCombinations.length() + singleDepthIndexCombinations.length() + countColumnCombinations.length() + dateColumnCombinations.length();
				boolean generateAll = singleDepthIndexCombinations.length()>2 || dateColumnCombinations.length() > 2;
				if(generateAll || totalCombinations < 4){
					for(int i=0;i<countColumnCombinations.length(); i++){
						this.combinations.put(countColumnCombinations.get(i));
					}
					for(int i=0;i< singleDepthIndexCombinations.length(); i++){
						this.combinations.put(singleDepthIndexCombinations.get(i));
					}
					//for(int i=0;i<numberColumnCombinations.length(); i++){
					if(numberColumnCombinations.length() > 0){
						this.combinations.put(numberColumnCombinations.get(0));
					}
					//}
					for(int i=0;i<dateColumnCombinations.length(); i++){
						this.combinations.put(dateColumnCombinations.get(i));
					}
					for(int i=0;i<semiColumnCombinations.length(); i++){
						this.combinations.put(semiColumnCombinations.get(i));
					}
				}else{
					if(numberColumnCombinations.length() > 0){
						//for(int i=0;i<numberColumnCombinations.length(); i++){
							this.combinations.put(numberColumnCombinations.getJSONObject(0));
						//}
					}
					if(countColumnCombinations.length() > 0){
						this.combinations.put(countColumnCombinations.get(0));
					}
					if(doubleDepthIndexCombinations.length() > 0){
						this.combinations.put(doubleDepthIndexCombinations.get(0));
					}
					if(singleDepthIndexCombinations.length() > 0){
						for(int i=0;i<singleDepthIndexCombinations.length(); i++){
							this.combinations.put(singleDepthIndexCombinations.get(i));
						}
					}
					if(dateColumnCombinations.length() > 0){
						for(int i=0; i<dateColumnCombinations.length();i++){
							this.combinations.put(dateColumnCombinations.get(i));
						}
					}
					if(semiColumnCombinations.length() > 0){
						this.combinations.put(semiColumnCombinations.get(0));
					}
				}
			break;
		}
	}
	private void analyzeNLPDataAndGenerateCombinations(NLInteractorImpl n){
		JSONArrayWrapper numberColumnCombinations = generateNumberColumnCombinations();
		JSONArrayWrapper countColumnCombinations = generateCountColumnCombinations();
		JSONArrayWrapper doubleDepthIndexCombinations = null;
		doubleDepthIndexCombinations = generateDoubleDepthIndexCombinations(n);
		JSONArrayWrapper singleDepthIndexCombinations = null;
		if(this.datasetProperty.datasetNature.name().equals("highlyNumerical")){
			singleDepthIndexCombinations = generateSingleDepthIndexCombinations(n, true);
		}else{
			singleDepthIndexCombinations = generateSingleDepthIndexCombinations(n, false);
		}
		
		//JSONArrayWrapper singleDepthIndexHighWeightedCombinations = generateSingleDepthHighWeightedCombinations(n);
		JSONArrayWrapper singleDepthIndexHighWeightedCombinations = new JSONArrayWrapper();
		JSONArrayWrapper dateColumnCombinations = null;
		if(noNumberColumns && this.datasetProperty.datasetNature.name().equals("highlyCategorical")){
			dateColumnCombinations = generateDoubleDepthCountCombinations(doubleDepthIndexCombinations.length());
		}else{
			dateColumnCombinations = null;
			if(this.datasetProperty.datasetNature.name().equals("highlyNumerical") && noCategoryColumns && this.getDateCol().size() == 1){
				dateColumnCombinations = generateDateColumnCombinations(doubleDepthIndexCombinations.length(),  n, true);
			}else{
				dateColumnCombinations = generateDateColumnCombinations(doubleDepthIndexCombinations.length(),  n, false);
			}
		}
		if(dateColumnCombinations.length() > 1){doubleDepthIndexCombinations.put(dateColumnCombinations.get(1));}
		JSONArrayWrapper semiColumnCombinations = generateSemiColumnCombinations(n);
		filterCombinationsBasedOnDatasetNature(numberColumnCombinations, countColumnCombinations, doubleDepthIndexCombinations,
				singleDepthIndexCombinations, dateColumnCombinations, semiColumnCombinations, singleDepthIndexHighWeightedCombinations);
		
	}
	private JSONArrayWrapper generateDoubleDepthCountCombinations(int doubleDepthIndexLength){
		int maxDoubleDepthIndexCategoriesThreshold = this.datasetProperty.uniqueValuesThresholdForDataset;
		int parentDoubleDepthIndexThreshold = (this.datasetProperty.parentUniqueValuesForAggregation/2<this.datasetProperty.minimumValuesThreshold)? this.datasetProperty.minimumValuesThreshold: this.datasetProperty.parentUniqueValuesForAggregation/2;
		int childDoubleDepthIndexThreshold = this.datasetProperty.childUniqueValuesForAggregation;
		JSONArrayWrapper dateColumnCombinations = new JSONArrayWrapper();
		if(dateCol.size()>0 && numerical.size() == 0 && this.daysBetween != null && this.getDateColumn() != null){
			 int dateGrouping = NLPUtil.getDateGrouping(getMinDate(),getMaxDate());
				int totalUniqueValuesInDate = getUnqiueValuesInDate(this.daysBetween.get(this.getDateColumn()));
				dateGrouping = dateGrouping==14 ? 2 : dateGrouping;
			    dateGrouping = dateGrouping==15 ? 5 : dateGrouping;
			    if(totalUniqueValuesInDate > this.datasetProperty.parentUniqueValuesForAggregation){
			    	return dateColumnCombinations;
			    }
			    JSONObjectWrapper combination = new JSONObjectWrapper();
				combination.put("dateGrouping", dateGrouping);
				combination.put("cat", Arrays.asList(this.getDateColumn()));
				combination.put("dateCol", true);
				combination.put("uniqueValues", totalUniqueValuesInDate);
				dateColumnCombinations.put(combination);
				String topCategoricalColumn = getTopCategoricalColumn();
				int totalUnqiueValuesInCatgeory = getTotalUniqueValuesInColumn(topCategoricalColumn);
				if(doubleDepthIndexLength == 0 && categorical.size() > 0 && totalUniqueValuesInDate < parentDoubleDepthIndexThreshold){
					JSONObjectWrapper ddcombination = new JSONObjectWrapper();
					ddcombination.put("dateGrouping", dateGrouping);
					ddcombination.put("dateCol", true);
					if(totalUnqiueValuesInCatgeory < childDoubleDepthIndexThreshold && totalUniqueValuesInDate*totalUnqiueValuesInCatgeory < maxDoubleDepthIndexCategoriesThreshold){
						ddcombination.put("cat", Arrays.asList(this.getDateColumn(), topCategoricalColumn));
						ddcombination.put("uniqueValues", totalUniqueValuesInDate*totalUnqiueValuesInCatgeory);
					}
					dateColumnCombinations.put(ddcombination);
				}
		 }
		return dateColumnCombinations;
		
	}
	private String getTopCategoricalColumn(){
		AtomicReference<String> topCatgeory = new AtomicReference<String>();
		AtomicInteger index = new AtomicInteger(0);
		this.categorical.forEach(colheader -> {
			if(index.get() < 1 && !this.irrelevantColumns.contains(colheader)){
				topCatgeory.set(colheader);
				index.getAndIncrement();
			}
		});
		return topCatgeory.toString();
	}
	private void normalChartRecommendations(){
		for(int i=0;i < this.combinations.length(); i++){
			JSONObjectWrapper currObject = this.combinations.getJSONObject(i);
			if(!currObject.has("num")){
				continue;
			}
			if(currObject.has("dateCol")){
				int dGrouping = currObject.has("dateGrouping") ? currObject.getInt("dateGrouping") : 0;
				if(dGrouping != 0){
					continue;
				}
			}
			if(!currObject.has("cat")){
				currObject.put("normalChart", true);
				continue;
			}
			if(currObject.has("semi")){
				currObject.put("normalChart", true);
				continue;
			}
			if(currObject.has("cat")){
				JSONArrayWrapper catgeoyrColumn = currObject.getJSONArray("cat");
				for(int j =0;j< catgeoyrColumn.length(); j++){
					String category = catgeoyrColumn.getString(j);
					int uniqueValues = getTotalUniqueValuesInColumn(category);
					if(!isSuitableForAggregation(uniqueValues)){
						currObject.put("normalChart", true);
					}
				}
			}
		}
	}
	
	private void aggregationCombinations(NLInteractorImpl n){
		for(int i=0;i < this.combinations.length(); i++){
			JSONObjectWrapper currObject = this.combinations.getJSONObject(i);
			if(currObject.has("semi")){
				continue;
			}
			if(!currObject.has("num")){
				String operation = "COUNT";	//No I18N
				currObject.put("operation", Arrays.asList(operation));
				int uniqueValues = getTotalUniqueValuesInColumn(currObject.getJSONArray("cat").getString(0));
				currObject.put("uniqueValues", uniqueValues);
				currObject.put("aggregatedChart", true);
				continue;
			}
			if(currObject.has("dateCol")){
				int dGrouping = currObject.has("dateGrouping") ? currObject.getInt("dateGrouping") : 0;
				if(dGrouping != 0){
					String operation = "SUM";	//No I18N
					currObject.put("operation", Arrays.asList(operation));
					currObject.put("aggregatedChart", true);
					continue;
				}
			}
			
			if(currObject.has("cat")){
				JSONArrayWrapper catgeoyrColumn = currObject.getJSONArray("cat");
				JSONArrayWrapper numberColumns = currObject.has("num") ? currObject.getJSONArray("num"): null;
				for(int j =0;j< catgeoyrColumn.length(); j++){
					String category = catgeoyrColumn.getString(j);
					int uniqueValues = getTotalUniqueValuesInColumn(category);
					if(isSuitableForAggregation(uniqueValues)){
						currObject.put("uniqueValues", uniqueValues);
						currObject.put("aggregatedChart", true);
						String operation = "COUNT";	//No I18N
						String numberColumn = numberColumns.getString(0);
						if(numberColumn != null){
							operation = choosingAggregateOperation(n.getCodeGenerator(), numberColumn, currObject);
						}
						currObject.put("operation", Arrays.asList(operation));
					}else{
						currObject.put("normalChart", true);
					}
				}
					
					
			}
		}
	}
	
	private void filterModule(){
		for(int i=0;i < this.combinations.length(); i++){
			JSONObjectWrapper currObject = this.combinations.getJSONObject(i);
			
			if(currObject.has("normalChart")){
				if(this.totalRows > this.datasetProperty.uniqueValuesForNormalFilter && currObject.has("cat")){
					JSONObjectWrapper filter = new JSONObjectWrapper();
					filter.put("type", "TOP");
					filter.put("limit", 5);
					filter.put("limitBy", "");
					currObject.put("filter", filter);
					normalFilterApplied = true;
				}
			}else if(currObject.has("aggregatedChart") && !currObject.getJSONArray("operation").getString(0).equals("COUNT")){
				int uniqueValues = currObject.has("cat") && currObject.has("uniqueValues") ? currObject.getInt("uniqueValues"): -1;
				if(uniqueValues > this.datasetProperty.uniqueValuesForAggFilter){
					JSONObjectWrapper filter = new JSONObjectWrapper();
					filter.put("type", "TOP");
					filter.put("limit", 5);
					filter.put("limitBy", "");
					currObject.put("filter", filter);
					aggFilterApplied = true;
				}
			}
				
		}
	}
	
	private String choosingAggregateOperation(SheetCodeGenerator codeGenerator, String colName, JSONObjectWrapper currentObject){
			// TODO Auto-generated method stub
//			String opType = "";
//			Map skewness = codeGenerator.getSkewnessQuotient();
//
//			int index = 0;
//			//skewness.values().toArray()
//			//Collections.so
//			Arrays.sort(skewness.values().toArray());
//
//			//Collections.sort(skewness.values().toArray());
//			for (Object key : skewness.keySet()) {
//				index++;
//				if (key.toString().equals(colName)) {
//					break;
//				}
//			}
//			if (index > (skewness.size() / 2)) {
//				opType = "SUM";      //NO I18N
//			} else {
//				opType = "AVERAGE";      //NO I18N
//			}
		String opType = "SUM";	//No I18N
		opType = (currentObject.has("operationToApply"))?currentObject.getString("operationToApply").toUpperCase():opType;	//No I18N
			return opType;	
	}
	
	private JSONArrayWrapper sortCombinations(){
		boolean isDoubleDepthIndexAvailable = false;
		Map<Integer, Integer> orderedCombinations = new HashMap<Integer, Integer>();
		for(int i=0;i < this.combinations.length(); i++){
			JSONObjectWrapper currObject = this.combinations.getJSONObject(i);
			if(currObject.has("aggregatedChart") && currObject.has("filter")){
				orderedCombinations.put(i, 100);
			}
			if(currObject.has("normalChart") && currObject.has("filter") && !currObject.has("semi")){
				orderedCombinations.put(i, 90);
			}
			
			if(currObject.has("aggregatedChart") && !currObject.has("filter")){
				if(currObject.getJSONArray("operation").getString(0).equals("SUM")){
					if(currObject.getJSONArray("cat").length()>1){
						isDoubleDepthIndexAvailable = true;
						orderedCombinations.put(i, 75);
					}else{
						orderedCombinations.put(i, 80);
					}
					
				}else{
					if(currObject.has("lowWeight")){
						orderedCombinations.put(i, 30);
					}else{
						orderedCombinations.put(i, 70);
					}
					
				}
			}
			if(currObject.has("aggregatedChart") && currObject.has("dateCol")){
				orderedCombinations.put(i, 60);
			}
			if(currObject.has("normalChart") && !currObject.has("filter") && currObject.has("num") && currObject.has("cat")){
				orderedCombinations.put(i, 55);
			}
			if(currObject.has("normalChart") && !currObject.has("filter") && currObject.has("num") && !currObject.has("cat")){
				orderedCombinations.put(i, 50);
			}
			if(currObject.has("normalChart") && currObject.has("semi")){
				if(isDoubleDepthIndexAvailable){
					orderedCombinations.put(i, 10);
				}else{
					orderedCombinations.put(i, 30);
				}
				
			}
			
		}
		orderedCombinations = orderedCombinations.entrySet().stream().sorted(Map.Entry.comparingByValue(Collections.reverseOrder())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
		JSONArrayWrapper newCombinations = new JSONArrayWrapper();
		orderedCombinations.forEach((Integer key, Integer value) -> {
			newCombinations.put(this.combinations.get(key));
		});
		return newCombinations;
	}
	private int getDateGrouping(int days) {
		int type = 0;
		if (days == 0) {
			type = 0;
		} else if (days < 10) {
			type = AggregationConstants.DAY;
		} else if (days >= 10 && days < 30) {
			type = AggregationConstants.WEEK;
		} else if (days >= 30 && days < 366) {
			type = AggregationConstants.MONTH;
		} else if (days >= 366 && days < 732) {
			type = AggregationConstants.QUARTER;
		} else if (days >= 732 && days < 1464) {
			type = AggregationConstants.HALF_YEARLY;
		} else if (days >= 1464) {
			type = AggregationConstants.YEAR;
		}
		return type;
	}
	
	private int getUnqiueValuesInDate(int days) {
		int type = 0;
		if (days == 0) {
			type = 0;
		} else if (days < 10) {
			type = days;
		} else if (days >= 10 && days < 30) {
			type = 7;
		} else if (days >= 30 && days < 366) {
			type = days/30;
		} else if (days >= 366 && days < 732) {
			type = 4;
		} else if (days >= 732 && days < 1464) {
			type = 4;
		} else if (days >= 1464) {
			type = days/365;
		}
		return type;
	}
	
	private JSONArrayWrapper doRelationShipAnalysis(List<String> categories, List<String> numbers, int maxLimit, Map<String, Double> mapTOCompare){
		JSONArrayWrapper bestNumForEachCat = new JSONArrayWrapper();
		AtomicInteger index = new AtomicInteger(0);
		AtomicDouble corrValue = new AtomicDouble(-1);
		AtomicReference<String> bestNumCol = new AtomicReference<String>();
		bestNumCol.set(this.getNumerical().get(0));
		if(categories != null){
			categories.forEach(colheader -> {
				if(this.datasetProperty.datasetNature.name().equals("highlyCategorical") && irrelevantColumns.contains(colheader.toString().toUpperCase())){
					index.getAndIncrement();
				}else{
					if(index.get()<maxLimit){
						numbers.forEach(numberCol -> {
							String key = colheader+NLP_CORREL_DELIMITER+numberCol;
							double tempCorrVal = mapTOCompare.get(key);
							if(tempCorrVal>corrValue.get()){
								corrValue.set(tempCorrVal);
								bestNumCol.set(numberCol);
							}
						});
						index.getAndIncrement();
						JSONObjectWrapper combination = new JSONObjectWrapper();
						combination.put("uniqueValues", getTotalUniqueValuesInColumn(colheader));
						combination.put("cat", Arrays.asList(colheader));
						combination.put("num", Arrays.asList(bestNumCol.get()));
						combination.put("corr", corrValue.get());
						bestNumForEachCat.put(combination);
					}
				}
				
			});
		}
		return bestNumForEachCat;
	}
	private JSONArrayWrapper doRelationShipAnalysisMultipleNumCols(List<String> categories, List<String> numbers, int maxLimit, Map<String, JSONArrayWrapper> mapTOCompare, NLInteractorImpl n, boolean generateAll){
		JSONArrayWrapper bestNumForEachCat = new JSONArrayWrapper();
		AtomicInteger index = new AtomicInteger(0);
		AtomicDouble corrValue = new AtomicDouble(-1);
		AtomicReference<String> bestNumCol = new AtomicReference<String>();
		if(this.getNumerical().size()>0){
			bestNumCol.set(this.getNumerical().get(0));
		}
		
		
		if(categories != null){
			categories.forEach(colheader -> {
				if(this.datasetProperty.datasetNature.name().equals("highlyCategorical") && irrelevantColumns.contains(colheader.toString().toUpperCase())){
					index.getAndIncrement();
				}else{
					if(index.get()<maxLimit){
						JSONArrayWrapper tempArray = mapTOCompare.get(colheader);
						int count=0;
						
						if(!generateAll){
							for(int i=0; i<3 && i < tempArray.length(); i++){
								JSONObjectWrapper tempObject = tempArray.getJSONObject(i);
								if(this.bestNumberColumns.contains(tempObject.getString("name"))){
									count++;
								}
							}
							if(this.bestNumberColumns.size() > 0 && count == 2){
								String numberCol = getBestNumberColumn(n, tempArray);
								bestNumCol.set(numberCol);
								corrValue.set(this.correlationMap.get(colheader+NLP_CORREL_DELIMITER+numberCol));
							}else{
								bestNumCol.set((highlySkewedNumColumn.isEmpty())?tempArray.getJSONObject(0).getString("name"):highlySkewedNumColumn);
								corrValue.set(this.correlationMap.get(colheader+NLP_CORREL_DELIMITER+tempArray.getJSONObject(0).getString("name")));
							}
							index.getAndIncrement();
							JSONObjectWrapper combination = new JSONObjectWrapper();
							combination.put("uniqueValues", getTotalUniqueValuesInColumn(colheader));
							combination.put("cat", Arrays.asList(colheader));
							combination.put("num", Arrays.asList(bestNumCol.get()));
							combination.put("corr", corrValue.get());
							bestNumForEachCat.put(combination);
							String numColumnDisqualifiedForSum = checkIfDisqualifiedNumberAvailable(tempArray);
							if(numColumnDisqualifiedForSum != null){
								combination = new JSONObjectWrapper();
								combination.put("cat", Arrays.asList(colheader));
								combination.put("uniqueValues", getTotalUniqueValuesInColumn(colheader));
								combination.put("num", Arrays.asList(numColumnDisqualifiedForSum));
								combination.put("operationToApply", "average");
								combination.put("corr", corrValue.get());
								bestNumForEachCat.put(combination);
							}
						}else{
							for(int i=0; i<3 && i < tempArray.length(); i++){
								JSONObjectWrapper tempObject = tempArray.getJSONObject(i);
								String numberCol = tempObject.getString("name");
								bestNumCol.set(numberCol);
								JSONObjectWrapper combination = new JSONObjectWrapper();
								combination.put("uniqueValues", getTotalUniqueValuesInColumn(colheader));
								combination.put("cat", Arrays.asList(colheader));
								combination.put("num", Arrays.asList(bestNumCol.get()));
								combination.put("corr", tempObject.get("corr"));
								if(numbersDisQualifiedForSum.contains(numberCol.toUpperCase())){
									combination.put("operationToApply", "average");
								}
								bestNumForEachCat.put(combination);
							}
							
						}
						
					}
				}
				
			});
		}
		return bestNumForEachCat;
	}
	
	private String getBestNumberColumn(NLInteractorImpl n, JSONArrayWrapper tempArray){
		Map skewness = n.getCodeGenerator().getSkewnessQuotient();
		String bestNumberCol = tempArray.getJSONObject(0).getString("name");
		String currentCol = tempArray.getJSONObject(0).getString("name");
		if(tempArray.length() > 1){
			for(int i=0;i<2 && i<tempArray.length()-1; i++){
				currentCol = tempArray.getJSONObject(i).getString("name");
				for(int j=1;j<3 && j<tempArray.length(); j++){
					String colheader1 = tempArray.getJSONObject(j).getString("name");
					if(this.bestNumberColumns.contains(currentCol) && this.bestNumberColumns.contains(colheader1)){
						if (Double.parseDouble(skewness.get(currentCol).toString()) < Double.parseDouble(skewness.get(colheader1).toString())){
							bestNumberCol =  colheader1;
						}
					}
				}
			}
			
		}
		return bestNumberCol;
		
	}
	
	
	private String checkIfDisqualifiedNumberAvailable(JSONArrayWrapper tempArray){
		for(int i=0;i<tempArray.length(); i++){
			String colHeader = tempArray.getJSONObject(i).getString("name");
			if(numbersDisQualifiedForSum.contains(colHeader.toUpperCase())){
				return colHeader;
			}
		}
		return null;
	}
	

	private String getHighestSkewedColumn(NLInteractorImpl n){
		Map skewness = n.getCodeGenerator().getSkewnessQuotient();
		if(this.bestNumberColumns.size() ==2){
			String bestNumberCol1 = this.bestNumberColumns.get(0);
			String bestNumberCol2 = this.bestNumberColumns.get(1);
			double skewness1 = Double.parseDouble(skewness.get(bestNumberCol1).toString());
			double skewness2 = Double.parseDouble(skewness.get(bestNumberCol2).toString()); 
			if (skewness1 > 0 && skewness2 > 0 && skewness1 > skewness2){
				return bestNumberCol1;
			}else if(skewness1 > 0 && skewness2 > 0){
				return bestNumberCol2;
			}
		}
		return "";
	} 
	
	private void sortArray(JSONArrayWrapper bestNumForEachCat){
		for(int i=0;i<bestNumForEachCat.length()-1; i++){
			for(int j=i+1; j<bestNumForEachCat.length(); j++){
				JSONObjectWrapper obj1 = bestNumForEachCat.getJSONObject(i);
				JSONObjectWrapper obj2 = bestNumForEachCat.getJSONObject(j);
				if(obj1.getDouble("corr") < obj2.getDouble("corr")){
					double diff = obj2.getDouble("corr") - obj1.getDouble("corr");
					if(diff > 0.03){
						obj1.put("skip", true);
					}
					swapObjects(i, j, bestNumForEachCat);
				}
			}
		}
	}
	private void swapObjects(int i, int j, JSONArrayWrapper bestNumForEachCat){
		JSONObjectWrapper temp = bestNumForEachCat.getJSONObject(i);
		bestNumForEachCat.put(i, bestNumForEachCat.getJSONObject(j));
		bestNumForEachCat.put(j, temp);
		
	}
	private int getTotalUniqueValuesInColumn(Object columnHeader){
		int uniqueValues = 0;
		String colName = '"'+columnHeader.toString()+'"';	//No I18N
		if(uniqueValuesInHeader.get(colName) != null){
			uniqueValues = uniqueValuesInHeader.get(colName);
		}else{
			JSONObjectWrapper colObject = (JSONObjectWrapper) lookupTable.get(colName);
			String colRangeString =  colObject.getString("range");
			Range colRange = Chart.getRangesFromString(sheet, colRangeString).get(0);
			int colIndex = (this.seriesIn.equals("COLS"))?colRange.getStartColIndex():colRange.getStartRowIndex();
			int startIndex = this.seriesIn.equals("COLS")?colRange.getStartRowIndex()+1 : colRange.getStartColIndex()+1;
			int endIndex = this.seriesIn.equals("COLS")?colRange.getEndRowIndex() : colRange.getEndColIndex();
			int itrIndex = this.seriesIn.equals("COLS")?colIndex:colRange.getStartRowIndex();
			uniqueValues = NLPUtil.totalUniqueValuesInRange(startIndex,endIndex, itrIndex, sheet, (this.seriesIn.equals("COLS")));	//No I18N
			uniqueValuesInHeader.put(colName, uniqueValues);
		}
		return uniqueValues;
	}
	private boolean isSuitableForAggregation(int uniqueValues){
		int length = (int) Math.log10(totalRows)-1;
		int threshold = (length>0) ? 10^length : 10;
		if(uniqueValues < this.datasetProperty.parentUniqueValuesForAggregation){
			return true;
		}
		return false;
	}
	private void datasetSize(){
		String firstKey = new ArrayList<String>(this.lookupTable.keySet()).get(0);
		JSONObjectWrapper rangeDetails = (JSONObjectWrapper)this.lookupTable.get(firstKey);
		String rangeString = rangeDetails.getString("range");
		Range colRange = Chart.getRangesFromString(sheet, rangeString).get(0);
		if(colRange.getColSize() == 1 && colRange.getRowSize() > 1){
			this.seriesIn = "COLS";	//No I18N
		}else{
			this.seriesIn = "ROWS"; //No I18N
		}
		this.totalRows = (this.seriesIn.equals("COLS")) ? colRange.getRowSize() : colRange.getColSize();
		this.totalColumns = this.lookupTable.size();
	}
	private void generalInformationAboutData(){
		this.singleCategory = (this.categorical.size() ==1);
		this.singleNumber = this.getNumerical().size() == 1;
		this.noNumberColumns = this.getNumerical().size()==0;
		this.noCategoryColumns = this.getCategorical().size()==0;
		List<String> numericalTemp = new CopyOnWriteArrayList<String>();
		numericalTemp.addAll(this.numerical);
		numericalTemp.forEach(numberCol -> {
			if(getTotalUniqueValuesInColumn(numberCol) <= 3){
				numericalTemp.remove(numberCol);
			}
			if(numberCol.toUpperCase().contains("YEAR") && checkIfYearColumn(numberCol)){
				this.semiColumns.add(numberCol);
				numericalTemp.remove(numberCol);
			}
		});
		this.numerical.clear();
		this.numerical.addAll(numericalTemp);
		Map<String,Double> correlMap = new HashMap<String, Double>();
		correlationMap.forEach(correlMap::putIfAbsent);
		
		for (Object key : correlationMap.keySet()) {
			String correlkey = key.toString();
			String reverseKey = "";
			String splittedArray[] = correlkey.split(NLP_CORREL_DELIMITER);
			if (splittedArray[0].equals(splittedArray[1])) {
				correlMap.remove(correlkey);
				continue;
			}
			 if(categorical.contains(splittedArray[0]) && categorical.contains(splittedArray[1])){
				reverseKey = splittedArray[1]+NLP_CORREL_DELIMITER+splittedArray[0];
				this.catToCatCorrelMap.put(correlkey, correlMap.get(correlkey));
				correlMap.remove(correlkey);
				correlMap.remove(reverseKey);
			}else if(this.numerical.contains(splittedArray[0]) && this.numerical.contains(splittedArray[1])){
				reverseKey = splittedArray[1]+NLP_CORREL_DELIMITER+splittedArray[0];
				if(correlMap.get(correlkey) != null){
					this.numToNumCorrelMap.put(correlkey, correlMap.get(correlkey));
				}
				correlMap.remove(correlkey);
				correlMap.remove(reverseKey);
			} else if(this.numerical.contains(splittedArray[0]) && (this.categorical.contains(splittedArray[1]) || this.semiColumns.contains(splittedArray[1]))){
				this.numToCatCorrelMap.put(correlkey, correlMap.get(correlkey));
			}
		}
		
		
		this.categorical.forEach(colheader -> {
			JSONArrayWrapper tempArray = new JSONArrayWrapper();
			this.numerical.forEach(numberColumn -> {
				String correlKey = colheader+NLP_CORREL_DELIMITER+numberColumn;
				JSONObjectWrapper tempObject = new JSONObjectWrapper();
				tempObject.put("name", numberColumn);
				tempObject.put("corr", correlMap.get(correlKey));
				tempArray.put(tempObject);
			});
			sortArray(tempArray);
			this.catToNumCorrelMap.put(colheader, tempArray);
		});
		
		this.dateCol.forEach(colheader -> {
			JSONArrayWrapper tempArray = new JSONArrayWrapper();
			this.numerical.forEach(numberColumn -> {
				String correlKey = colheader+NLP_CORREL_DELIMITER+numberColumn;
				JSONObjectWrapper tempObject = new JSONObjectWrapper();
				tempObject.put("name", numberColumn);
				tempObject.put("corr", correlMap.get(correlKey));
				tempArray.put(tempObject);
			});
			sortArray(tempArray);
			this.dateToNumCorrelMap.put(colheader, tempArray);
		});
		this.numToNumCorrelMap = this.numToNumCorrelMap.entrySet().stream().sorted(Map.Entry.comparingByValue(Collections.reverseOrder())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
		if(this.numToNumCorrelMap.keySet().size() > 0){
			String numToNumCombination = new ArrayList<String>(this.numToNumCorrelMap.keySet()).get(0);
			String[] colNames = numToNumCombination.split(NLP_CORREL_DELIMITER);
			bestNumberColumns.add(colNames[0]);
			bestNumberColumns.add(colNames[1]);
		}
		
		
	}
	private boolean checkIfYearColumn(String colHeader){
		String colName = '"'+colHeader.toString()+'"';	//No I18N
		JSONObjectWrapper colObject = (JSONObjectWrapper) lookupTable.get(colName);
		String colRangeString =  colObject.getString("range");
		Range colRange = Chart.getRangesFromString(sheet, colRangeString).get(0);
		int colIndex = colRange.getStartColIndex();
		boolean yearColumn = NLPUtil.checkIfYearColumn(colRange.getStartRowIndex()+1,colRange.getStartRowIndex()+10, colIndex, sheet);
		return yearColumn;
   
	}
	
	
	private boolean checkIfValidCatgeoricalColumn(String colHeader){
		boolean catColumn = false;
		String colName = '"'+colHeader.toString()+'"';	//No I18N
		JSONObjectWrapper colObject = (JSONObjectWrapper) lookupTable.get(colName);
		String colRangeString =  colObject.getString("range");
		Range colRange = Chart.getRangesFromString(sheet, colRangeString).get(0);
		int colIndex = colRange.getStartColIndex();
		int uqValues = getTotalUniqueValuesInColumn(colHeader);
		if(uqValues < 0.03*this.totalRows){
			catColumn = true;
			catColumn = NLPUtil.checkIfValidCatgeoricalColumn(colRange.getStartRowIndex()+1,colRange.getStartRowIndex()+10, colIndex, sheet);
			Map<String, Double> skewnessQuotient = this.n.getCodeGenerator().getSkewnessQuotient();
			if(skewnessQuotient.get(colHeader) >= -1 && skewnessQuotient.get(colHeader) <= 1){
				catColumn = true;
			}else{
				catColumn = false;
			}
		}
		return catColumn;
   
	}
	private String getDateTitle(int dateOperation){
		I18nMessage I18N = new I18nMessage(sheet.getWorkbook().getFunctionLocale());
		String title = "";
		switch(dateOperation){
    	case AggregationConstants.WEEK:
        case AggregationConstants.DAY_OF_WEEK:
            title += I18N.getMsg("Zia.Year.Submenu.Week")+" ";
            break;
        case AggregationConstants.MONTH_BY_YEAR:
        case AggregationConstants.MONTH:
            title += I18N.getMsg("Zia.Year.Submenu.Month")+" ";
            break;
        case AggregationConstants.QUARTER_BY_YEAR:
        case AggregationConstants.HALF_YEARLY:
        case AggregationConstants.QUARTER:
            title += I18N.getMsg("Zia.Year.Submenu.Quarter")+" ";
            break;
        case AggregationConstants.YEAR:
        	title += I18N.getMsg("Zia.Year.Submenu.Year")+" ";
        	break;
    }
		return title;
	}
	
}
