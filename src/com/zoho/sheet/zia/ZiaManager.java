// $Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.zoho.sheet.zia;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.zoho.sheet.chart.TableConstraints;
import com.zoho.sheet.chart.TablePrediction;
import com.zoho.sheet.util.CurrentRealm;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.zoho.sheet.chart.Chart.encloseSheetNameWithApostrophe;
import static com.zoho.sheet.chart.Chart.isRangeContainValidSheetName;
import static com.zoho.sheet.chart.ChartUtils.dataRange;

/**
 *
 * <AUTHOR>
 */
public class ZiaManager {

    private static final Logger LOGGER = Logger.getLogger(ZiaManager.class.getName());

    public enum Modules {
        ZIA,
        CHART,
        PIVOT,
        FORMULA
    }

    public static List<Range> getRangesFromRangeString(Sheet sheet, String rangeStr) {
        try {
            StringTokenizer strtok = new StringTokenizer(rangeStr, ";");
            Workbook workBook = sheet.getWorkbook();
            List<Range> ranges = new ArrayList<>();

            while (strtok.hasMoreTokens()) {
                Range range = null;
                String baseCellAddress = CellUtil.getCellReference(sheet, 0, 0, true, false, false);
                String cellRange = (String) strtok.nextToken();

                cellRange = encloseSheetNameWithApostrophe(cellRange);
                NamedExpression namedExp = workBook.getNamedExpression(cellRange);
                cellRange = isRangeContainValidSheetName(workBook, cellRange);
                if (namedExp == null && cellRange != null) {
                    try {
                        range = new Range(workBook, cellRange, baseCellAddress, CellReference.ReferenceMode.A1, false);
                    } catch (SheetEngineException ex) {
                        LOGGER.log(Level.INFO, "Invalid Data Range {0}", ex);
                    }

                } else if (namedExp != null) {
                    range = NamedExpression.getRange(namedExp, sheet, 0, 0);

                }
                if (range != null) {
                    ranges.add(range);
                }

            }
            return ranges;
        } catch (Exception ex) {
            LOGGER.log(Level.INFO, "[ZIA] >>>>>>>>>> Parsing Error in ZIA Model with sheet {0} and Data Ranges {1}", new Object[]{sheet.getName(), rangeStr});
            ex.printStackTrace();
        }
        return null;
    }

    public static Range doFilterRannge(Range predictedRange) {
        TablePrediction tp = new TablePrediction();
        Range newlyEvaluatedRange = tp.doFilterRange(predictedRange, "COLS");    //No I18N
        return newlyEvaluatedRange;
    }

    public static JSONObjectWrapper getTable(Sheet sheet, List<Range> ranges) {
        boolean isSingleCellSelection = _isSingleCellSelection(ranges);
        if (!isSingleCellSelection) {
            JSONObjectWrapper pattern = new JSONObjectWrapper();
            pattern.put(TableConstraints.TABLE_PREDICTION_TYPE, "filter");
            int length = ranges.size();
            List<Range> newRangeList = new ArrayList<>();
            for (int i = 0; i < length; i++) {
                Range range = ranges.get(i);
                TablePrediction predict = new TablePrediction(range);
                newRangeList.add(doFilterRannge(predict.getTableRange()));
            }
            ranges.clear();
            ranges = newRangeList;
        } else {
            Range range = ranges.get(0);
            TablePrediction predict = new TablePrediction(range);
            ranges.clear();
            if (predict.isIntersectsTable()) {
                ranges.add(predict.getTableRange());
            } else {
                ranges.add(doFilterRannge(predict.getTableRange()));
            }
        }

        JSONObjectWrapper response = new JSONObjectWrapper();
        response.put("dataRange", dataRange(ranges));
        return response;
    }

    private static boolean _isSingleCellSelection(List<Range> ranges) {
        if (ranges.size() > 1) {
            return false;
        }
        Range range = ranges.get(0);
        boolean isCellSelection = (range.getStartColIndex() == range.getEndColIndex()) && (range.getStartRowIndex() == range.getEndRowIndex());
        if (!isCellSelection) {
            try {
                Cell cell = range.getSheet().getReadOnlyCellFromShell(range.getStartRowIndex(), range.getStartColIndex()).getCell();
                int mergeRowSpan = range.getSheet().getMergeCellSpans(cell)[0];
                int mergeColSpan = range.getSheet().getMergeCellSpans(cell)[1];
                int rowLength = range.getRowSize();
                int colLength = range.getColSize();
                return (mergeRowSpan == rowLength) && (mergeColSpan == colLength);
            } catch (Exception ex) {
                LOGGER.log(Level.WARNING, "[ZIA] Exception while reading merge Cell Properties");
                return false;
            }
        }
        return isCellSelection;
    }

    private static boolean _isSingleCellSelection(List<Range> ranges, boolean doNotCheckForMergeCell) {
        if (doNotCheckForMergeCell) {
            if (ranges.size() > 1) {
                return false;
            }
            Range range = ranges.get(0);
            return (range.getStartColIndex() == range.getEndColIndex())
                    && (range.getStartRowIndex() == range.getEndRowIndex());
        } else {
            return _isSingleCellSelection(ranges);
        }
    }

    public static JSONObjectWrapper getErrorResponse(Sheet activeSheet, int errorCode) {
        JSONObjectWrapper newClientResponse = new JSONObjectWrapper();

        JSONObjectWrapper DOCUMENT_META = new JSONObjectWrapper();
        JSONObjectWrapper SHEET_META = new JSONObjectWrapper();
        JSONObjectWrapper ZIA_ERROR_META = new JSONObjectWrapper();
        JSONObjectWrapper ACTIVE_SHEET_META = new JSONObjectWrapper();
        JSONObjectWrapper ZIA_META = new JSONObjectWrapper();

        ZIA_ERROR_META.put("statusCode", errorCode);
        //ACTIVE_SHEET_META.put(Integer.toString(CommandConstants.ERROR), ZIA_ERROR_META);
        ZIA_META.put(Integer.toString(CommandConstants.ERROR), ZIA_ERROR_META);
        ACTIVE_SHEET_META.put(Integer.toString(CommandConstants.ZIA), ZIA_META);
        SHEET_META.put(Integer.toString(CommandConstants.SHEET_META), (new JSONObjectWrapper().put(activeSheet.getAssociatedName(), ACTIVE_SHEET_META)));

        DOCUMENT_META.put(CurrentRealm.getContainerWithoutTrace().getResourceKey(), SHEET_META);

        newClientResponse.put(Integer.toString(CommandConstants.DOCUMENT_META), DOCUMENT_META);
        newClientResponse.put(Integer.toString(CommandConstants.IS_NEW_CLIENT_RESPONSE), true);

        return newClientResponse;
    }
}

