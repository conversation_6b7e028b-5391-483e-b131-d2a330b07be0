package com.zoho.sheet.zia.spellcheck;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.Locale;

public class MDSupportedLangBean {

    private static final String LANG_CODE = "language_code";               //No I18N
    private static final String LANG_CODE_OLD = "language_code_old";          //No I18N
    private static final String LANG_NAME = "language_name";               //No I18N
    private static final String LANG_NAME_IN_ENG = "language_name_in_english";    //No I18N
    private static final String LANG_COUNTRY_CODE = "language_country_code";       //No I18N
    private static final String LANG_COUNTRY = "language_country";            //No I18N
    private static final String LANG_COUNTRY_IN_ENG = "language_country_in_english";  //No I18N

    private final JSONObjectWrapper supportedLangObj;
    private final String langCode;
    private final String langCodeOld;
    private final String langName;
    private final String langNameInEng;
    private final String langCountryCode;
    private final String langCountry;
    private final String langCountryInEng;
    private final Locale locale;

    public MDSupportedLangBean(JSONObjectWrapper supportedLangObj) {
        this.supportedLangObj = supportedLangObj;

        this.langCode = supportedLangObj.optString(LANG_CODE);
        this.langCodeOld = supportedLangObj.optString(LANG_CODE_OLD);
        this.langName = supportedLangObj.optString(LANG_NAME);
        this.langNameInEng = supportedLangObj.optString(LANG_NAME_IN_ENG);
        this.langCountryCode = supportedLangObj.optString(LANG_COUNTRY_CODE);
        this.langCountry = supportedLangObj.optString(LANG_COUNTRY);
        this.langCountryInEng = supportedLangObj.optString(LANG_COUNTRY_IN_ENG);

        this.locale = convertStringToLocale(langCode);

    }

    public static Locale convertStringToLocale(String localeStr) {
        Locale locale = null;
        if(localeStr != null && !localeStr.isEmpty()) {

            String[] split = localeStr.split("[-_]");

            if (split.length >= 2) {
                locale = new Locale(split[0], split[1]);
            } else if (split.length == 1) {
                locale = new Locale(split[0]);
            }

        }
        return locale;
    }

    public JSONObjectWrapper getAsJSONObj() {
        return supportedLangObj;
    }

    public JSONObjectWrapper getMinifiedJSONObject() {
        if(langName == null || langCode == null){
            return null;
        }
        return new JSONObjectWrapper().put(LANG_NAME,langName).put(LANG_CODE,langCode);
    }

    public String getLangCode() {
        return langCode;
    }

    public String getLangCodeOld() {
        return langCodeOld;
    }

    public String getLangName() {
        return langName;
    }

    public String getLangNameInEng() {
        return langNameInEng;
    }

    public String getLangCountryCode() {
        return langCountryCode;
    }

    public String getLangCountry() {
        return langCountry;
    }

    public String getLangCountryInEng() {
        return langCountryInEng;
    }

    public Locale getLocale() {
        return locale;
    }

    @Override
    public String toString() {
        return "MDSupportedLangBean{" + "langCode='" + langCode + '\'' + ", langName='" + langName + '\'' + '}';     //No I18N
    }
}
