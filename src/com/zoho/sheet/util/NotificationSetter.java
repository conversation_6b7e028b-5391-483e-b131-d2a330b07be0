//$Id$
package com.zoho.sheet.util;


import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * <AUTHOR>
 * @Notes Used to set the notificaion
 */
public class NotificationSetter extends Notifier {

	public NotificationSetter(Long docId, String docOwner,String docOwnerZUID, String sender, int notificationType) throws Exception {
		super(docId, docOwner, docOwnerZUID, sender, notificationType);
	}
	
	public NotificationSetter(Long docId, String docOwner, String docOwnerZuid, String sender,
			JSONObjectWrapper notificationSettings) throws Exception {
		// TODO Auto-generated constructor stub
		super(docId, docOwner, docOwnerZuid, sender, notificationSettings);
	}

	@Override
	protected boolean updateParticipants(String members) throws Exception {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	protected String getMailSubject(String member, String mailId) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected String getMailMessage(String member, String mailId, String fullName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected boolean allowSenderNotification(String member) throws Exception {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	protected boolean updateUser(int action, String members) throws Exception {
		// TODO Auto-generated method stub
		return false;
	}

	

}
