/* $Id$ */
package com.zoho.sheet.util;

import com.adventnet.ds.query.*;
import com.adventnet.iam.User;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;

import java.util.logging.Level;
import java.util.logging.Logger;

public class UserSettings {

    public static final Logger LOGGER = Logger.getLogger(UserSettings.class.getName());

    public static enum Settings {
        NEW_VERSION,
        PRINT,
        VIEW,
        CUSTOM_TOOLBAR,
        LISTING_PAGE,
        GRID_SETTING
    }

    public static Boolean set(String userName, Settings setting, String key, String value) {
        /* settings -  User settings ENum Object
    	 * userName - user Name for SAS-DB access
    	 * Value - Value to be updated in Settings table
    	 * */
        try {

            String tableName = "UserSettings"; //NO I18N
            Long zuid = DocumentUtils.getZUID(userName);
            Long currentTime = System.currentTimeMillis();

            /* -- GET CALL -- */
            Persistence persistence = SheetPersistenceUtils.getPersistence(userName);

            SelectQueryImpl sql = new SelectQueryImpl(new Table(tableName));
            sql.addSelectColumn(new Column(tableName, "*"));
            sql.setCriteria(new Criteria(new Column(tableName, "ZUID"), zuid, QueryConstants.EQUAL));
            DataObject data = persistence.get(sql);
            
            Boolean hasData = !data.isEmpty();

            /* -- Operation -- */
            switch (setting) {

//                case NEW_VERSION:
//
//                    // REDIS UPDATE
//                    Boolean enableNewClient = "true".equalsIgnoreCase(value);
//
//                    String addRedisKey = enableNewClient ? RedisHelper.HOEPN_ZUIDS_YES : RedisHelper.HOEPN_ZUIDS_NO;
//                    String removeRedisKey = enableNewClient ? RedisHelper.HOEPN_ZUIDS_NO : RedisHelper.HOEPN_ZUIDS_YES;
//                    RedisHelper.sadd(addRedisKey, zuid.toString(), -1);
//                    RedisHelper.srem(removeRedisKey, zuid.toString());
//
//                    //Announcement/Walkthorugh control key
////                    if (RedisHelper.hget(RedisHelper.WALK_THROUGH_NEWVERSION_ZUIDS, zuid.toString()) == null) {
////                        RedisHelper.hset(RedisHelper.WALK_THROUGH_NEWVERSION_ZUIDS, zuid.toString(), "false", -1);
////                    }
//
//                    // DATABASE UPDATE
//                    /*  New Version / New Client settings - DATA:
//		             	VERSION - ver: old (0) / new (1)
//		             	NEW CLIENT ENABLED ON - nceo : [Server Time (PDT)]
//		             	LAST NEW CLIENT SWITCHED TIME - ncst : [Server Time (PDT)]
//		             	LAST OLD CLIENT SWITCHED TIME - ocst : [Server Time (PDT)]
//		             	NEW CLIENT SWITCH COUNT: ncsc : [count]
//		             	OLD CLIENT SWITCH COUNT: ocsc : [count]
//                     */
//                    Persistence pub_persistence = SheetPersistenceUtils.getPersistence(Constants.PUBLIC_SPACE);
//
//                    JSONObject dataJson = new JSONObject();
//                    if (hasData) {
//                        Row data_row = data.getFirstRow(tableName);
//                        String val = (String) data_row.get("NEW_VERSION");
//                        dataJson = new JSONObject(val);
//                    }
//
//                    int verVal = enableNewClient ? 1 : 0;
//                    String switchTimeKey = enableNewClient ? "ncst" : "ocst"; //NO I18N
//                    String counterKey = enableNewClient ? "ncsc" : "ocsc"; //NO I18N
//                    dataJson.set("ver", verVal);
//                    dataJson.set(switchTimeKey, currentTime);
//                    if (dataJson.has(counterKey)) {
//                        int _count = (int) dataJson.get(counterKey) + 1;
//                        dataJson.set(counterKey, _count);
//                    } else {
//                        dataJson.set(counterKey, 1);
//                    }
//                    if (enableNewClient && !dataJson.has("nceo")) {
//                        dataJson.put("nceo", currentTime);
//                    }
//
//                    if (hasData) {
//
//                        UpdateQuery uq = new UpdateQueryImpl(tableName);
//                        Criteria cri = new Criteria(new Column(tableName, "ZUID"), zuid, QueryConstants.EQUAL);
//                        uq.setCriteria(cri);
//                        uq.setUpdateColumn("NEW_VERSION", dataJson);
//                        uq.setUpdateColumn("LAST_MODIFIED_TIME", currentTime);
//                        persistence.update(uq);
//                        pub_persistence.update(uq);
//
//                    } else {
//
//                        Row row = new Row(tableName);
//                        row.set("ZUID", zuid);
//                        row.set("NEW_VERSION", dataJson);
//                        row.set("LAST_MODIFIED_TIME", currentTime);
//                        DataObject dobj = persistence.constructDataObject();
//                        dobj.addRow(row);
//                        persistence.update(dobj);
//
//                        Row pub_row = new Row(tableName);
//                        pub_row.set("ZUID", zuid);
//                        pub_row.set("NEW_VERSION", dataJson);
//                        pub_row.set("LAST_MODIFIED_TIME", currentTime);
//                        DataObject pub_dobj = pub_persistence.constructDataObject();
//                        pub_dobj.addRow(pub_row);
//                        pub_persistence.update(pub_dobj);
//
//                    }
//
//                    break;

                case PRINT:
                    break;

                case VIEW:

                    /* View settings - DATA:
                        SIDE PANEL TEXT + OPTION DISPLAY - sptd : 0 (no) / 1 (yes)
                        CHAT BAR SHOW/HIDE view - cbv: 0 (hide), 1(show)
		    		  
                     */
                    JSONObjectWrapper dataViewJson = new JSONObjectWrapper();

                    if (hasData) {
                        Row data_row = data.getFirstRow(tableName);
                        String val = (String) data_row.get("VIEW");
                        if(val != null){
                            dataViewJson = new JSONObjectWrapper(val);
                        }
                    }

                    if ("SidePanelTextDisplay".equalsIgnoreCase(key)) {
                        dataViewJson.put("sptd", "true".equalsIgnoreCase(value));
                    } else if ("chatBarView".equalsIgnoreCase(key)) {
                        dataViewJson.put("cbv", "true".equalsIgnoreCase(value));
                    } else if("tidingsView".equalsIgnoreCase(key)){
                        dataViewJson.put("stv", value);
                    } else if("darkModeView".equalsIgnoreCase(key)){
                        dataViewJson.put("drk",value);
                    } else if("overrideBrowserShortcut".equalsIgnoreCase(key)){
                        dataViewJson.put("ovrbs",value);
                    }

                    if (hasData) {

                        UpdateQuery uq = new UpdateQueryImpl(tableName);
                        Criteria cri = new Criteria(new Column(tableName, "ZUID"), zuid, QueryConstants.EQUAL);
                        uq.setCriteria(cri);
                        uq.setUpdateColumn("VIEW", dataViewJson.toString());
                        uq.setUpdateColumn("LAST_MODIFIED_TIME", currentTime);
                        persistence.update(uq);

                    } else {

                        Row row = new Row(tableName);
                        row.set("ZUID", zuid);
                        row.set("VIEW", dataViewJson.toString());
                        row.set("LAST_MODIFIED_TIME", currentTime);
                        DataObject dobj = persistence.constructDataObject();
                        dobj.addRow(row);
                        persistence.update(dobj);

                    }

                    break;

                case CUSTOM_TOOLBAR:
                    break;
                case LISTING_PAGE:
                    JSONObjectWrapper listingPageMeta = new JSONObjectWrapper();

                    if (hasData) {
                        Row data_row = data.getFirstRow(tableName);
                        String val = (String) data_row.get("LISTING_PAGE");
                        if(val != null){
                            listingPageMeta = new JSONObjectWrapper(val);
                        }
                    }

                    if ("WorkdriveInfoCue".equalsIgnoreCase(key)) {
                        listingPageMeta.put("wdCue", "true".equalsIgnoreCase(value));
                    }

                    if (hasData) {

                        UpdateQuery uq = new UpdateQueryImpl(tableName);
                        Criteria cri = new Criteria(new Column(tableName, "ZUID"), zuid, QueryConstants.EQUAL);
                        uq.setCriteria(cri);
                        uq.setUpdateColumn("LISTING_PAGE", listingPageMeta.toString());
                        uq.setUpdateColumn("LAST_MODIFIED_TIME", currentTime);
                        persistence.update(uq);

                    } else {

                        Row row = new Row(tableName);
                        row.set("ZUID", zuid);
                        row.set("LISTING_PAGE", listingPageMeta.toString());
                        row.set("LAST_MODIFIED_TIME", currentTime);
                        DataObject dobj = persistence.constructDataObject();
                        dobj.addRow(row);
                        persistence.update(dobj);

                    }
                    break;
                case GRID_SETTING:
                    JSONObjectWrapper dataSettingsJson = new JSONObjectWrapper();

                    if (hasData) {
                        Row data_row = data.getFirstRow(tableName);
                        String val = (String) data_row.get("GRID_SETTING");
                        if(val != null){
                            dataSettingsJson = new JSONObjectWrapper(val);
                        }
                    }

                    if ("autoCompleteView".equalsIgnoreCase(key)) {
                        dataSettingsJson.put("atf", "true".equalsIgnoreCase(value));
                    }
                    if ("textToNumView".equalsIgnoreCase(key)) {
                        dataSettingsJson.put("ttn", "true".equalsIgnoreCase(value));
                    }

                    if (hasData) {

                        UpdateQuery uq = new UpdateQueryImpl(tableName);
                        Criteria cri = new Criteria(new Column(tableName, "ZUID"), zuid, QueryConstants.EQUAL);
                        uq.setCriteria(cri);
                        uq.setUpdateColumn("GRID_SETTING", dataSettingsJson);
                        uq.setUpdateColumn("LAST_MODIFIED_TIME", currentTime);
                        persistence.update(uq);

                    } else {

                        Row row = new Row(tableName);
                        row.set("ZUID", zuid);
                        row.set("GRID_SETTING", dataSettingsJson);
                        row.set("LAST_MODIFIED_TIME", currentTime);
                        DataObject dobj = persistence.constructDataObject();
                        dobj.addRow(row);
                        persistence.update(dobj);

                    }
                    break;
            }

            return true;

        } catch (Exception pe) {
            
            LOGGER.log(Level.WARNING, "Problem while updating UserSettings", pe);
            return false;
        }
    }

    public static JSONObjectWrapper get(String userName, Settings setting)
    {
        return get(DocumentUtils.getZUserObject(userName), setting);
    }

    public static JSONObjectWrapper get(User user, Settings setting)
    {
        JSONObjectWrapper dataViewJson = new JSONObjectWrapper();
        try {

            String userName = user.getLoginName();
            Long zuid = user.getZUID();

            String tableName = "UserSettings"; //NO I18N

            /* -- GET CALL -- */
            Persistence persistence = SheetPersistenceUtils.getPersistence(userName);

            SelectQueryImpl sql = new SelectQueryImpl(new Table(tableName));
            sql.addSelectColumn(new Column(tableName, "*"));
            sql.setCriteria(new Criteria(new Column(tableName, "ZUID"), zuid, QueryConstants.EQUAL));
            DataObject data = persistence.get(sql);

            Boolean hasData = !data.isEmpty();

            /* -- Operation -- */
            switch (setting) {

                case NEW_VERSION:
                    break;
                case VIEW:

                    if (hasData) {

                        Row data_row = data.getFirstRow(tableName);
                        String val = (String) data_row.get("VIEW");
                        if(val != null){
                            dataViewJson = new JSONObjectWrapper(val);
                        }

                        return dataViewJson;
                    }
                    break;
                case GRID_SETTING:
                    if (hasData) {

                        Row data_row = data.getFirstRow(tableName);
                        String val = (String) data_row.get("GRID_SETTING");
                        if(val != null){
                            dataViewJson = new JSONObjectWrapper(val);
                        }

                        return dataViewJson;
                    }

                    break;
                case LISTING_PAGE:
                    if (hasData) {

                        Row data_row = data.getFirstRow(tableName);
                        String val = (String) data_row.get("LISTING_PAGE");
                        if(val != null){
                            dataViewJson = new JSONObjectWrapper(val);
                        }

                        return dataViewJson;
                    }

                    break;
            }

        } catch(Exception e){

            LOGGER.log(Level.WARNING, "Problem while getting data from UserSettings ", e);

        }
        return dataViewJson;
    }
}
