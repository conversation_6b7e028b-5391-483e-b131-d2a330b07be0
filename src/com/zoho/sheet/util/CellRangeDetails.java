//$Id$
package com.zoho.sheet.util;


import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public abstract class CellRangeDetails {

        protected String[][] cellValues = null;
        protected String[][] cellFormulas = null;
        protected String[][] cellStyles = null;
        protected String[][] cellAnnots = null;
        protected String[][] cellDiscussions = null;
        protected String[][] cellBottomBorders = null;
        protected String[][] cellTopBorders = null;
        protected String[][] cellLeftBorders = null;
        protected String[][] cellRightBorders = null;
        protected String[][] colSpan = null;
        protected String[][] cellHyper = null;
        protected String[] colWidth = null;
        protected String[] rowHeight = null;
        protected String[] columnLeft = null;
        protected String[] rowTop = null;
        protected String[][] literalColSpan = null;
        protected String[][] literalRowSpan = null;

        //For Column Merge details.
        protected String colMergeDetails = "{}";
        protected JSONObjectWrapper colMergeDetailsJson = new JSONObjectWrapper();

        protected JSONObjectWrapper dataInformation = new JSONObjectWrapper();
        //It will be changed, if a covered cell in a reading range.
        protected String coveredCellInfo = "[]";

        protected String conditionalFormatInfo = null;
        public JSONObjectWrapper[][] cellVariables = null;

        //Will Use After Vertical Merging
        protected String[][] rowSpan = null;

        protected String activesheetname = null;
        //protected int startRow;
        //protected int endRow;
        protected int[] rowsAry;
        protected int startCol;
        protected int endCol;

        protected Object documentObject = null;

        //Used Only At Init In Row Height JSON(In flow of parse Impl)
        protected String rowHeightJSON = null;

        protected boolean showFormulas;

        protected boolean showCondFormat;//Used only during parser case - Roseline

        protected String associatedSheetName;

        protected String[][] dValidation = null; //DataValidation

        protected String checkboxInfo = null;
        
        protected int defaultRowHeight;
        protected int defaultColumnWidth;
        
        protected String defaultFontName;
        protected String defaultFontSize;
        
        protected JSONObjectWrapper freezepane = null;

        public CellRangeDetails(Object documentObject, String activesheetname, int[] rowsAry, int startCol, int endCol, boolean showFormulas, String associatedSheetName) throws Exception {
                this.activesheetname = activesheetname;
                this.rowsAry = rowsAry;
                this.startCol = startCol;
                this.endCol = endCol;
                this.documentObject = documentObject;
                this.showFormulas = showFormulas;
                this.associatedSheetName = associatedSheetName;
                init();
                processDocument();
        }

        private void init() {

                int rowCount = rowsAry.length; //endRow - startRow+1;
                int colCount = endCol - startCol + 1;
                this.cellValues = new String[rowCount][colCount];
                this.cellFormulas = new String[rowCount][colCount];
                this.cellStyles = new String[rowCount][colCount];
                this.cellAnnots = new String[rowCount][colCount];
                this.cellDiscussions = new String[rowCount][colCount];
                this.cellBottomBorders = new String[rowCount][colCount];
                this.cellTopBorders = new String[rowCount][colCount];
                this.cellLeftBorders = new String[rowCount][colCount];
                this.cellRightBorders = new String[rowCount][colCount];
                this.colSpan = new String[rowCount][colCount];
                this.literalColSpan = new String[rowCount][colCount];
                this.literalRowSpan = new String[rowCount][colCount];
                this.cellVariables = new JSONObjectWrapper[(int) rowCount + 1][(int) colCount + 1];
                this.cellHyper = new String[rowCount][colCount];
                this.colWidth = new String[colCount];
                this.rowHeight = new String[rowCount];
                this.columnLeft = new String[colCount];
                this.rowTop = new String[colCount];
                this.dValidation = new String[rowCount][colCount];//DataValidation 
        }

        /*Cell Values*/
        public int[] getRowList() {
                return this.rowsAry;
        }

        public String getCellValue(int row, int col) {
                return this.cellValues[row][col];
        }

        protected void setCellValue(int row, int col, String cellValue) {
                this.cellValues[row][col] = cellValue;
        }

        /*Cell Formulas*/
        public String getCellFormula(int row, int col) {
                return this.cellFormulas[row][col];
        }

        public void setCellFormula(int row, int col, String formula) {
                this.cellFormulas[row][col] = formula;
        }

        /*public Object getCellFormulas() {
				return this.cellFormulas;
		}*/

 /*Cell Styles*/
        protected void setCellStyle(int row, int col, String style) {
                this.cellStyles[row][col] = style;
        }

        public String getCellStyle(int row, int col) {
                return this.cellStyles[row][col];
        }


        /*Cell Annots*/
        protected String setCellAnnot(int row, int col) {
                return this.cellAnnots[row][col];
        }

        public String getCellAnnot(int row, int col) {
                return this.cellAnnots[row][col];
        }

        /* Data Validation */
        protected String setDataValidation(int row, int col) {
                return this.dValidation[row][col];
        }

        public String getDataValidation(int row, int col) {
                return this.dValidation[row][col];
        }

        /*Cell Bottom Borders*/
        protected void setCellBottomBorder(int row, int col, String bottomBorder) {
                this.cellBottomBorders[row][col] = bottomBorder;
        }

        public String getCellBottomBorder(int row, int col) {
                return this.cellBottomBorders[row][col];
        }

        /*Cell Top Borders*/
        protected void setCellTopBorder(int row, int col, String topBorder) {
                this.cellTopBorders[row][col] = topBorder;
        }

        public String getCellTopBorder(int row, int col) {
                return this.cellTopBorders[row][col];
        }


        /*Cell Left Borders*/
        protected void setCellLeftBorder(int row, int col, String leftBorder) {
                this.cellLeftBorders[row][col] = leftBorder;
        }

        public String getCellLeftBorder(int row, int col) {
                return this.cellLeftBorders[row][col];
        }


        /*Cell Right Borders*/
        protected void setCellRightBorder(int row, int col, String rightBorder) {
                this.cellRightBorders[row][col] = rightBorder;
        }

        public String getCellRightBorder(int row, int col) {
                return this.cellRightBorders[row][col];
        }


        /*Col Span*/
        protected void setColSpan(int row, int col, String colSpan) {
                this.colSpan[row][col] = colSpan;
        }

        public String getColSpan(int row, int col) {
                return this.colSpan[row][col];
        }


        /*Actual colSpan Col Span*/
        protected void setLiteralColSpan(int row, int col, String colSpan) {
                this.literalColSpan[row][col] = colSpan;
        }

        public String getLiteralColSpan(int row, int col) {
                return this.literalColSpan[row][col];
        }

        /*Actual rowSpan */
        protected void setLiteralRowSpan(int row, int col, String rowSpan) {
                this.literalRowSpan[row][col] = rowSpan;
        }

        public String getLiteralRowSpan(int row, int col) {
                return this.literalRowSpan[row][col];
        }

        /*Hyper Links*/
        protected void setCellHyper(int row, int col, String cellHyper) {
                this.cellHyper[row][col] = cellHyper;
        }

        public String getCellHyper(int row, int col) {
                return this.cellHyper[row][col];
        }


        /*Sheet Name*/
        public String getSheetName() {
                return this.activesheetname;
        }

        /*Column Width*/
        protected void setColWidth(int col, String width) {
                this.colWidth[col] = width;
        }

        public String getColWidth(int col) {
                return this.colWidth[col];
        }


        /*Row Heights*/
        protected void setRowHeight(int row, String height) {
                this.rowHeight[row] = height;
        }

        public String getRowHeight(int row) {
                return this.rowHeight[row];
        }

        /*Column Left*/
        protected void setColumnLeft(int col, String columnLeft) {
                this.columnLeft[col] = columnLeft;
        }

        public String getColumnLeft(int col) {
                return this.columnLeft[col];
        }

        /*Row Top*/
        protected void setRowTop(int row, String rowTop) {
                this.rowTop[row] = rowTop;
        }

        public String getRowTop(int row) {
                return this.rowTop[row];
        }

        //For Vertical Merging
        protected void setRowSpan(int row, int col, String rowSpan) {
                this.rowSpan[row][col] = rowSpan;
        }

        public String getRowSpan(int row, int col) {
                return this.rowSpan[row][col];
        }

        /*Requested Cell Range*/
        public int startRow() {
                return this.rowsAry[0];
        }

        public int endRow() {
                return this.rowsAry[rowsAry.length];
        }

        //Used When Vertical Tiling.
        public int startCol() {
                return this.startCol;
        }

        public int endCol() {
                return this.endCol;
        }

        public String getConditionalFormatInfo() {
                return this.conditionalFormatInfo;
        }

        public String getColMergeDetails() {
                return this.colMergeDetails;
        }

        public JSONObjectWrapper getColMergeDetailsJson() {
                return this.colMergeDetailsJson;
        }

        public String getRowHeightDetails() {
                return this.rowHeightJSON;
        }

        public String getCoveredCellInfo() {
                return this.coveredCellInfo;
        }

        public String getCheckboxInfo() {
                return this.checkboxInfo;
        }

        public String getDataInformation() {
                return this.dataInformation.toString();
        }

        public JSONObjectWrapper getDataInformationJson() {
                return this.dataInformation;
        }
        
        public int getDefaultRowHeight() {
            return this.defaultRowHeight;
        }
        
        public int getDefaultColumnWidth() {
            return this.defaultColumnWidth;
        }
        
        public String getDefaultFontName() {
            return this.defaultFontName;
        }
        
        public String getDefaultFontSize() {
            return this.defaultFontSize;
        }
        
        public void setDefaultFontSize(String defaultFontSize) {
            this.defaultFontSize = defaultFontSize;
        }

        //Method Used To Set Details
        protected abstract void processDocument() throws Exception;

        protected void setValue(Object object, int row, int col, String value) {
                ((String[][]) object)[row][col] = value;
        }

        protected void setValue(Object object, int unit, String value) {
                ((String[]) object)[unit] = value;
        }

        protected Long getValue(Object object, int row) {
                return Long.valueOf((String) (((String[]) object)[row]));
        }

        protected String getValue(Object object, int row, int col) {
                String value = "";//No internationalization
                if (object != null) {
                        value = (String) (((String[][]) object)[row][col]);
                }
                return value;
        }

}
