/* $Id$ */
package com.zoho.sheet.util;

import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.iam.UserAPI;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import java.util.Locale;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;

public class I18NUtil {

        private static final Logger LOGGER = Logger.getLogger(I18NUtil.class.getName());

        public static Locale getLocale(HttpServletRequest request) {

                // Normal Login case - with locale in url
                String localeCode = request.getParameter("locale");
                Locale locale = null;

                if (localeCode != null) {
                        int index = localeCode.indexOf("_");
                        if (index > 0) {
                                locale = LocaleUtil.getLocale(localeCode.substring(0, index), localeCode.substring(index + 1, localeCode.length()));
                        } else {
                                locale = new Locale(localeCode);
                        }
                }

                // Remote case
                String lCode = request.getParameter("lang");
                String cCode = request.getParameter("countrycode");

                if (lCode != null && cCode != null) {
                        locale = LocaleUtil.getLocale(lCode, cCode);
                } else if (lCode != null) {
                        locale = new Locale(lCode);
                }

                if (locale == null) {

                        boolean isLoggedIn = (IAMUtil.getCurrentUser() != null);
                        if (isLoggedIn) {
                                try {
                                        String loginName = request.getUserPrincipal().getName();
                                        long zuid = DocumentUtils.getZUID(loginName);
                                        IAMProxy proxy = IAMProxy.getInstance();
                                        UserAPI uapi = proxy.getUserAPI();
                                        User userObj = uapi.getUserFromZUID(""+zuid);

                                        String langCode = userObj.getLanguage();
                                        String countryCode = userObj.getCountry();

                                        if (langCode != null && countryCode != null) {
                                                locale = LocaleUtil.getLocale(langCode, countryCode);
                                        } else if (langCode != null) {
                                                locale = new Locale(langCode);
                                        }
                                } catch (Exception e) {

                                        LOGGER.log(Level.WARNING, "ERROR occured when getting locale from IAM");
                                }
                        } else {
                                locale = request.getLocale();
                        }
                }

                return locale;
        }

        public static String getLocaleAsString(HttpServletRequest request) {

                Locale locale = getLocale(request);
                String localeToUse = ClientUtils.getSupportedLocale(locale);
                
                return localeToUse;
        }
}
