/* $Id$ */
package com.zoho.sheet.util;

import com.zoho.components.server.view.*;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;

public class ViewFactoryImpl implements ViewFactory {

    public static Logger logger = Logger.getLogger(ViewFactoryImpl.class.getName());
    public String componentsPath;

    @Override
    public HashMap<String, String> getLocaleMap(HttpServletRequest request) throws Exception {

        HashMap<String, String> info = new HashMap<>();
        return info;
    }

    @Override
    public String getResourcesPath() {
        return getClassesPath();
    }

    @Override
    public HashMap<String, String[]> getResourcesDirectories() {
        HashMap<String, String[]> resourcesDirectories = new HashMap<>();
        String[] clientDirs = {"components", "fcomponents/js", "fcomponents/topbar"};	//No i18n
        String[] serverDirs = {"fcomponents/resources"};       //No i18n

        resourcesDirectories.put("client", clientDirs);	//No i18n
        resourcesDirectories.put("server", serverDirs); //No i18n

        logger.log(Level.INFO, "[View Factory IMPL] resource dir : client dir {0}", Arrays.toString(clientDirs));
        logger.log(Level.INFO, "[View Factory IMPL] resource dir : serverDirs dir {0}", Arrays.toString(serverDirs));


        return resourcesDirectories;

    }

    @Override
    public HashMap<String, String[][]> getLanguageOptions() {
        HashMap lang = new HashMap();

        String[][] i18n_languages = {{"en", "US"}, {"ja", "Japanese"}, {"zh", "CN"}, {"fr", "FR"}, {"de", "DE"}, {"es", "Spanish"}, {"pl", "PL"}, {"ko", "KR"}, {"pt", "BR"}, {"hu", "HU"}, {"da", "DK"}, {"sv", "SE"}, {"tr", "TR"}, {"nl", "NL"}, {"it", "IT"}, {"zh", "TW"}, {"ru", "RU"},{"bg","BG"},{"hr", "HR"}, {"ar", "AE"}, {"cs", "CZ"}, {"eo", "Esperanto"}, {"id", "ID"}, {"in", "IN"}, {"iw", "IW"}, {"ms", "MS"}, {"th", "TH"}, {"uk", "UK"}, {"ur", "UR"}, {"vi", "VI"}}; //No i18n
        String[][] i18n_multi_lang_codes = {{"pt", "BR"}, {"zh", "TW"}}; //No I18N

        lang.put("LANGUAGES", i18n_languages);
        lang.put("MULTI_LANG_CODES", i18n_multi_lang_codes);

        return lang;
    }

    @Override
    public String getThirdPartyViewPath() {
        return null;
    }

    @Override
    public boolean checkRole(HashMap elementRoleInfo, HttpServletRequest request) throws Exception {
        return false;
    }

    public void setClassesPath(String componentsPath) {
        this.componentsPath = componentsPath.substring(0, componentsPath.length() - 1);
    }

    public String getClassesPath() {
        return componentsPath;
    }
}
