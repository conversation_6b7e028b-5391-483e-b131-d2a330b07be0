/* $Id$ */
package com.zoho.sheet.util;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletRequest;

import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.zoho.instrument.Stats;
import com.zoho.zfsng.constants.ZFSNGConstants;

/**
 * <AUTHOR>
 * @Notes STATS process
 */
public class ZSStats {
	
	
	private static final Logger LOGGER = Logger.getLogger(ZSStats.class.getName());
	
	public static final String OPENED = "OPENED"; //NO I18N
	public static final String APP_OPENED = "APP_OPENED"; //NO I18N
	public static final String PUBLISH_RANGE_VIEW = "PUBLISH_RANGE_VIEW";//NO I18N
	public static final String ORG_PUBLISH_RANGE_VIEW = "ORG_PUBLISH_RANGE_VIEW";//NO I18N
	public static final String IPHONE_VIEW = "IPHONE_VIEW";//NO I18N
	public static final String PUBLISHED_VIEW = "PUBLISHED_VIEW";//NO I18N
	public static final String PUBLISHED_VIEW_EMBED = "PUBLISHED_VIEW_EMBED";//NO I18N
	public static final String PUBLISHED_VIEW_HTML = "PUBLISHED_VIEW_HTML";//NO I18N
	public static final String PUBLISHED_VIEW_SPREADSHEET = "PUBLISHED_VIEW_SPREADSHEET";//NO I18N
	public static final String ORG_PUBLIC_VIEW = "ORG_PUBLIC_VIEW";//NO I18N
	public static final String EXT_PUBLIC_VIEW = "EXT_PUBLIC_VIEW";//NO I18N
	public static final String GDRIVE_VIEW = "GDRIVE_VIEW";//NO I18N
	public static final String GDRIVE_VIEW_READ_DOC = "GDRIVE_VIEW_READ_DOC";//NO I18N
	public static final String GDRIVE_VIEW_NEW_DOC = "GDRIVE_VIEW_NEW_DOC";//NO I18N
	public static final String GADGETS_SERVER_CALL = "GADGETS_SERVER_CALL";//NO I18N
	public static final String GADGETS_SERVER_CALL_FAILURE = "GADGETS_SERVER_CALL_FAILURE";//NO I18N
	
	public static final String IMPORT_DOCS="IMPORT_DOCS";//NO I18N
	public static final String IMPORT_IMPORT_MENU="IMPORT_IMPORT_MENU";
	public static final String IMPORT_REMOTE="IMPORT_REMOTE";//NO I18N
	private static final String IMPORT_PARTNERS_REMOTE_VIEW = "IMPORT_PARTNERS_REMOTE_VIEW";// NO I18N
	public static final String IMPORT_AUTH_REMOTE="IMPORT_AUTH_REMOTE";//NO I18N
	public static final String IMPORT_GDRIVE="IMPORT_GDRIVE";
	public static final String IMPORT_DATA_API="IMPORT_DATA_API";
	public static final String IMPORT_EXCEL_VIEWER="IMPORT_EXCEL_VIEWER";
	public static final String IMPORT_SCRATCH_VIEW="IMPORT_SCRATCH_VIEW";
	public static final String IMPORT_GADGET_VIEW = " IMPORT_GADGET_VIEW";
	
	public static final String IMPORT_CALLS = "IMPORT_CALLS";//NO I18N
	public static final String IMPORT_SUCCESS="IMPORT_SUCCESS";
	public static final String IMPORT_ASPOSE_SUCCESS="IMPORT_ASPOSE_SUCCESS";
	public static final String IMPORTERROR = "IMPORTERROR";//NO I18N
	public static final String IMPORT_LIBREOFFICE="IMPORT_LIBREOFFICE";
	public static final String IMPORT_XLSX_PARSER="IMPORT_XLSX_PARSER";
	public static final String IMPORT_ASPOSE_XLSX_PARSERS="IMPORT_ASPOSE_XLSX_PARSERS";
	public static final String IMPORT_ASPOSE_ODS_PARSERS="IMPORT_ASPOSE_ODS_PARSERS";
	public static final String IMPORT_ZS = "IMPORT_ZS";
	public static final String IMPORT_XLSX_PARSER_FAILURE="IMPORT_XLSX_PARSER_FAILURE";
	public static final String IMPORT_XLSX_FAIL_LIBO_SUCCESS="IMPORT_XLSX_FAIL_LIBO_SUCCESS";
	public static final String IMPORT_SHEET_PARSERS="IMPORT_SHEET_PARSERS";
	public static final String IMPORT_RETRY="IMPORT_RETRY";
	public static final String IMPORT_RETRY_SUCCESS="IMPORT_RETRY_SUCCESS";
	public static final String IMPORT_RETRY_FAILURE="IMPORT_RETRY_FAILURE";
	public static final String IMPORT_DATAAPI_V2_MAIL = "IMPORT_DATAAPI_V2_MAIL";

	
	public static final String IMPORT_FORMAT_XLSX = "IMPORT_FORMAT_XLSX";//NO I18N
	public static final String IMPORT_FORMAT_XLSM = "IMPORT_FORMAT_XLSM";//NO I18N
	public static final String IMPORT_FORMAT_XLTX = "IMPORT_FORMAT_XLTX";//NO I18N
	public static final String IMPORT_FORMAT_XLTM = "IMPORT_FORMAT_XLTM";//NO I18N
	public static final String IMPORT_FORMAT_XLS = "IMPORT_FORMAT_XLS";//NO I18N
	public static final String IMPORT_FORMAT_XLT = "IMPORT_FORMAT_XLT";//NO I18N
	public static final String IMPORT_FORMAT_ODS = "IMPORT_FORMAT_ODS";//NO I18N
	public static final String IMPORT_FORMAT_CSV = "IMPORT_FORMAT_CSV";//NO I18N
	public static final String IMPORT_FORMAT_TSV = "IMPORT_FORMAT_TSV";//NO I18N
	public static final String IMPORT_FORMAT_SXC = "IMPORT_FORMAT_SXC";//NO I18N
	public static final String IMPORT_FORMAT_JSON="IMPORT_JSON";//NO I18N
	public static final String IMPORT_FORMAT_JSON_FAILURE="IMPORT_JSON_FAILURE";//NO I18N
	public static final String IMPORT_FORMAT_JSON_SUCCESS="IMPORT_JSON_SUCCESS";//NO I18N
	public static final String IMPORT_FORMAT_CONVERSION="IMPORT_FORMAT_CONVERSION";//NO I18N
	public static final String IMPORT_FORMAT_LAZY_CONVERSION="IMPORT_FORMAT_LAZY_CONVERSION";//NO I18N
	public static final String IMPORT_FORMAT_SIZE_10="IMPORT_FORMAT_SIZE_10";//NO I18N
	public static final String IMPORT_FORMAT_SIZE_20="IMPORT_FORMAT_SIZE_20";//NO I18N
	public static final String IMPORT_FORMAT_SIZE_25="IMPORT_FORMAT_SIZE_25";//NO I18N
	
	public static final String EXPORTS = "EXPORTS";//NO I18N
	public static final String EXPORT_DOCS = "EXPORT_DOCS";//NO I18N
	public static final String EXPORT_FORMAT_XLSX = "EXPORT_FORMAT_XLSX";//NO I18N
	public static final String EXPORT_FORMAT_XLSM = "EXPORT_FORMAT_XLSM";//NO I18N
	public static final String EXPORT_FORMAT_XLS = "EXPORT_FORMAT_XLS";//NO I18N
	public static final String EXPORT_FORMAT_ODS = "EXPORT_FORMAT_ODS";//NO I18N
	public static final String EXPORT_FORMAT_CSV = "EXPORT_FORMAT_CSV";//NO I18N
	public static final String EXPORT_FORMAT_TSV = "EXPORT_FORMAT_TSV";//NO I18N
	public static final String EXPORT_FORMAT_PDF = "EXPORT_FORMAT_PDF";//NO I18N
	public static final String EXPORT_FORMAT_HTML = "EXPORT_FORMAT_HTML";//NO I18N
	public static final String EXPORT_FORMAT_JSON = "EXPORT_FORMAT_JSON";//NO I18N
	public static final String EXPORT_FORMAT_ZIP = "EXPORT_FORMAT_ZIP";//NO I18N
	public static final String EXPORT_RETRY="EXPORT_RETRY";
	public static final String EXPORT_RETRY_SUCCESS="EXPORT_RETRY_SUCCESS";
	public static final String EXPORT_RETRY_FAILURE="EXPORT_RETRY_FAILURE";
	
	
	public static final String GDRIVE_IMPORTERROR = "GDRIVE_IMPORTERROR";//NO I18N
	public static final String FORMSUBMIT = "FORMSUBMIT";//NO I18N
	public static final String PARSEERROR = "PARSEERROR";//NO I18N
	public static final String EXPORTERROR = "EXPORTERROR";//NO I18N
	public static final String UNSUPPORTED_MACRO_CALLS="UNSUPPORTED_MACRO_";//NO I18N
	
	public static final Integer[] SHEET_RANGE_VALUE_ARRAY = {0,1,3,7,15,25,50,100};
	public static final String[] SHEET_RANGE_STATS_ARRAY = {"SHEET_RANGE_0_1", "SHEET_RANGE_2_3", "SHEET_RANGE_4_7", "SHEET_RANGE_8_15", "SHEET_RANGE_16_25", "SHEET_RANGE_26_50", "SHEET_RANGE_51_100", "SHEET_RANGE_100+"};//NO I18N
	
	public static final Integer[] COLUMN_RANGE_VALUE_ARRAY = {0,6,15,25,50,80,120,180};
	public static final String[] COLUMN_RANGE_STATS_ARRAY = {"COLUMN_RANGE_0_6", "COLUMN_RANGE_7_15","COLUMN_RANGE_16_25","COLUMN_RANGE_26_50","COLUMN_RANGE_51_80","COLUMN_RANGE_81_120","COLUMN_RANGE_121_180","COLUMN_RANGE_180+"}; //NO I18N
	
	public static final Integer[] ROW_RANGE_VALUE_ARRAY = {0,10,50,100,200,500,1000,2500,5000,10000,25000};
	public static final String[] ROW_RANGE_STATS_ARRAY = {"ROW_RANGE_0_10", "ROW_RANGE_11_50", "ROW_RANGE_51_100", "ROW_RANGE_101_200", "ROW_RANGE_201_500", "ROW_RANGE_501_1000", "ROW_RANGE_1001_2500", "ROW_RANGE_2501_5000", "ROW_RANGE_5001_10000", "ROW_RANGE_10001_25000", "ROW_RANGE_25000+"}; //NO I18N
	
	public static final Integer[] IMPORT_SIZE_RANGE_VALUE_ARRAY = {0,102400,512000,1048576,2097152,5242880,10485760}; // 0, 100KB, 500KB, 1MB, 2MB, 5MB, 10MB
	public static final String[] IMPORT_SIZE_RANGE_STATS_ARRAY = {"IMPORT_SIZE_RANGE_0_100KB", "IMPORT_SIZE_RANGE_100KB_500KB", "IMPORT_SIZE_RANGE_500KB_1MB", "IMPORT_SIZE_RANGE_1MB_2MB", "IMPORT_SIZE_RANGE_2MB_5MB", "IMPORT_SIZE_RANGE_5MB_10MB", "IMPORT_SIZE_RANGE_10MB+"};//NO I18N
	
	public static final String SHARE_OPERATIONS = "SHARE_OPERATIONS"; //NO I18N
	public static final String SHARE_NEW = "SHARE_NEW"; //NO I18N
	public static final String SHARE_CHANGE_PERMISSION = "SHARE_CHANGE_PERMISSION"; //NO I18N
	public static final String SHARE_REMOVE = "SHARE_REMOVE"; //NO I18N
	
	public static final String OO_COMPONENT_CREATION = "OO_COMPONENT_CREATION";
	
	private static final String LOG_STRING = "[MI STATS] "; // NO I18N

	private static final String ZDB_TABLE_DESIGN_VIEW = "ZDB_TABLE_DESIGN_VIEW"; // NO I18N
	private static final String ZDB_DATABASE_ACTION = "ZDB_DATABASE_ACTION"; // NO I18N
	private static final String ZDB_TABLE_GRID = "ZDB_TABLE_GRID"; // NO I18N
	private static final String ZDB_TABLE_DATA_ACTION = "ZDB_TABLE_DATA_ACTION"; // NO I18N
	private static final String ZDB_DISPLAY_CHART = "ZDB_DISPLAY_CHART"; // NO I18N
	private static final String ZDB_TABLE_DATA = "ZDB_TABLE_DATA"; // NO I18N
	private static final String ZDB_VIEW = "ZDB_VIEW"; // NO I18N
	private static final String ZDB_GRAPH = "ZDB_GRAPH"; // NO I18N
	private static final String ZDB_FILTER_ACTION = "ZDB_FILTER_ACTION"; // NO I18N
	private static final String ZDB_TABLE_PREVIEW = "ZDB_TABLE_PREVIEW"; // NO I18N
	public static final String AUTH_WORKBOOKS = "AUTHORIZED_WORKBOOKS";
	public static final String REMOTE_WORKBOOKS = "REMOTE_WORKBOOKS";
	public static final String PUBLIC_WORKBOOKS = "PUBLIC_WORKBOOKS";
	public static final String WORKBOOKS = "WORKBOOKS";
	public static final String CREATE_AUTH_WORKBOOKS = "CREATE_AUTH_WORKBOOKS";
	public static final String CREATE_REMOTE_WORKBOOKS = "CREATE_REMOTE_WORKBOOKS";
	public static final String CREATE_PUBLIC_WORKBOOKS = "CREATE_PUBLIC_WORKBOOKS";
	public static final String CREATE_WORKBOOKS = "CREATE_WORKBOOKS";
	
	public static final String PDF_GENERATION = "PDF_GENERATION";
	public static final String PDF_GENERATION_TYPE_PDF = "PDF_GENERATION_TYPE_PDF";
	public static final String PDF_GENERATION_TYPE_IMAGE = "PDF_GENERATION_TYPE_IMAGE";
	public static final String PDF_GENERATION_FAILURE = "PDF_GENERATION_FAILURE";
	public static final String PDF_GENERATION_FAILURE_TYPE_PDF = "PDF_GENERATION_FAILURE_TYPE_PDF";
	public static final String PDF_GENERATION_FAILURE_TYPE_IMAGE = "PDF_GENERATION_FAILURE_TYPE_IMAGE";
	
        public static final String THUMBNAIL_GENERATION = "THUMBNAIL_GENERATION";
        public static final String THUMBNAIL_GENERATION_FAILURE = "THUMBNAIL_GENERATION_FAILURE";
        public static final String THUMBNAIL_GENERATION_SUCCESS = "THUMBNAIL_GENERATION_SUCCESS";
	
	public static final String WMS_REREGISTER = "WMS_REREGISTER";
	
	public static final String TOUR_BUBBLE_DISPLAY = "TOUR_BUBBLE_DISPLAY";
	public static final String TOUR_REPLY = "TOUR_REPLY";
	public static final String TOUR_READMORE = "TOUR_READMORE";
	public static final String TOUR_GOTIT = "TOUR_GOTIT";
	public static final String TOUR_CALLOUT = "TOUR_CALLOUT";
        
        public static final String STOCK_FORMULA = "STOCK_FORMULA";

	public static final String DATA_CONNECTION = "DATA_CONNECTION";//NO I18N

	public static final String SCHEDULE_CONNECTION_CALL = "SCHEDULE_CONNECTION_CALL";//NO I18N

        //Excluding below actions from adding to MI monitoring for time being. Need to go through all the actions once and add more items to the list. - Venkat  
        public static final Set<String> EXCLUDE_ACTION_LIST=new HashSet<String>(Arrays.asList(
    			
    			"SYNC_CHECK",				//NO I18N
    			"JOIN_COLLAB",				//NO I18N
    			"HJOIN_COLLAB",				//NO I18N
    			"HCLOSE_DOCUMENT",			//NO I18N
    			"GET_WMS_USERS",				//NO I18N
    			"LOAD_IMAGE",				//NO I18N
    			"CLOSE_DOCUMENT",			//NO I18N
    			"USER_PRESENCE",				//NO I18N
    			"SCROLL",					//NO I18N
    			"REQUEST_BANNER",			//NO I18N
    			"GET_SHEET_TABCOLORS",		//NO I18N
    			"POST_DOC_LOAD",				//NO I18N
    			"LOAD_BOOK",					//NO I18N
    			"UPDATE_VIEWPORT",			//NO I18N
    			"GET_FORMULA_SUGGEST_INFO"	//NO I18N
    			
    			));
    public static final String SWITCH_TO_NEWVERSION = "SWITCH_TO_NEWVERSION";
    		
    	

private static Map<String, Set<String>> import_url_map=new HashMap<String, Set<String>>();
static{
	
	
		Set<String> IMPORT_IMPORT_MENU_URLS=new HashSet<String>(Arrays.asList("/import.do"));//NO I18N
		Set<String> IMPORT_EXCEL_VIEWER_URLS=new HashSet<String>(Arrays.asList("/view","/view.do"));//NO I18N
		Set<String> IMPORT_GADGET_VIEW_URLS=new HashSet<String>(Arrays.asList("/gadget"));//NO I18N
		Set<String> IMPORT_REMOTE_PARTNER_URLS=new HashSet<String>(Arrays.asList(
				"/remotedoc.do",//NO I18N
				"/remotedoc.im",//NO I18N
				"/remotedoc.sas",//NO I18N
				"/remotedocImport.do",//NO I18N
				"/remotedocImport.im",//NO I18N
				"/remotedocImport.sas"));//NO I18N
		Set<String> IMPORT_AUTHER_REMOTE_URLS=new HashSet<String>(Arrays.asList(
				"/authremotedoc.im",//NO I18N
				"/authremotedoc.sas",//NO I18N
				"/authremotedoc.do"));//NO I18N
		Set<String> IMPORT_SCRATCH_VIEW_URLS=new HashSet<String>(Arrays.asList("/scratch.do","/scratch"));//NO I18N
		
		
		import_url_map.put(IMPORT_IMPORT_MENU,IMPORT_IMPORT_MENU_URLS);
		import_url_map.put(IMPORT_PARTNERS_REMOTE_VIEW, IMPORT_REMOTE_PARTNER_URLS);
		import_url_map.put(IMPORT_AUTH_REMOTE, IMPORT_AUTHER_REMOTE_URLS);
		import_url_map.put(IMPORT_EXCEL_VIEWER, IMPORT_EXCEL_VIEWER_URLS);
		import_url_map.put(IMPORT_GADGET_VIEW, IMPORT_GADGET_VIEW_URLS);
		import_url_map.put(IMPORT_SCRATCH_VIEW, IMPORT_SCRATCH_VIEW_URLS);
	}

	public static String getActionName(String actionName){
	/* Used to set the Action Name in URL for instrumentation */
		int actionConst = getActionConstant(actionName);
		if (actionConst != -1) {
        	 return getActionName(actionConst);
		}
		return null;
	}
	
	private static String getActionName(int actionConst) {
		//String actionName = (String) actionMap.get(actionConst);
		
		String actionName = (String) ActionConstants.getActionName(actionConst);
		if (actionName == null || "".equals(actionName)) {
			LOGGER.info(LOG_STRING + "Not regsitered ActionConstants action: " + actionConst);
		}
		return actionName;
	}
	
	public static void increment(String statsName){
		if(statsName != null && !"".equals(statsName.trim())){
			//LOGGER.info(LOG_STRING + "STATS UPDATE: " + statsName);
			Stats.increment(statsName, System.currentTimeMillis(), 1L);
		}
	}
	
	public static void incrementByValue(String statsName, Long stats){
		if(statsName != null && !"".equals(statsName.trim())){
			//LOGGER.info(LOG_STRING + "STATS UPDATE: " + statsName);
			Stats.increment(statsName, System.currentTimeMillis(), stats);
		}
	}

	public static void meter(String statsName, Long stats, Boolean isJVMLevel){
		if(statsName != null && !"".equals(statsName.trim())){
			//LOGGER.info(LOG_STRING + "STATS UPDATE: " + statsName +" :: count: " + stats +" , JVMLevel: " + isJVMLevel);
			Stats.meter(((isJVMLevel) ? Stats.Scope.JVM : Stats.Scope.GLOBAL), statsName, System.currentTimeMillis(), stats);
		}
	}
	
	private static int getActionConstant(String actionName){
		int actionConst = -1;
		if(actionName != null) {
			try{
				actionConst = Integer.parseInt(actionName);
			}catch (Exception e){
				LOGGER.info("Non action constant action");
			}
		}
		return actionConst;
	}
	
	public static void incrementByActionConst(String name){
	/* @name: It should be ActionConstants */
		int actionConst = getActionConstant(name);
		if (actionConst != -1) {
	        	String actionName = getActionName(actionConst);
	        	if(actionName != null && !EXCLUDE_ACTION_LIST.contains(actionName)) {
	        		increment(actionName);
	        	}
        }		
	}

	public static void increamentByURI(HttpServletRequest request){

		String uri = request.getRequestURI().substring(request.getContextPath().length(), request.getRequestURI().length());
		//LOGGER.info(LOG_STRING + " URI: " + uri);

		if(uri.contains("/ropen.do")){
			increment(ZSStats.OPENED);
		} else if (uri.contains("/hopen.do")){
			increment(ZSStats.OPENED);
			increment(ZSStats.APP_OPENED);
		
		} else if (uri.contains("/publishrange.do")){
			increment(ZSStats.PUBLISH_RANGE_VIEW);
		} else if (uri.contains("/publishrange.org")){
			increment(ZSStats.ORG_PUBLISH_RANGE_VIEW);
		} else if (uri.contains("/riphone.do")){
			increment(ZSStats.IPHONE_VIEW);
		} else if (uri.contains("/published.do")){
			increment(ZSStats.PUBLISHED_VIEW);
			String mode = (String) request.getParameter("mode");
			if("embed".equalsIgnoreCase(mode)) {
				increment(ZSStats.PUBLISHED_VIEW_EMBED);
			} else if ("html".equalsIgnoreCase(mode)) {
				increment(ZSStats.PUBLISHED_VIEW_HTML);
			} else {
				increment(ZSStats.PUBLISHED_VIEW_SPREADSHEET);
			}
			//TODO: Need to test in Grid Setup
			if(request.getAttribute(ZFSNGConstants.IS_ORGPUBLISHED) != null){
				increment(ZSStats.ORG_PUBLIC_VIEW);
			} else if (request.getAttribute(ZFSNGConstants.IS_PUBLISHED) != null){
				increment(ZSStats.EXT_PUBLIC_VIEW);
			}
		//TODO: Need to test in Grid Setup	
		} else if (uri.contains("/gdrive.do")){
			increment(ZSStats.GDRIVE_VIEW);
			String mode = (String) request.getParameter("mode");
			if("readDoc".equalsIgnoreCase(mode)) {
				increment(ZSStats.GDRIVE_VIEW_READ_DOC);
			} else if ("newDoc".equalsIgnoreCase(mode)) {
				increment(ZSStats.GDRIVE_VIEW_NEW_DOC);
			}
		//TODO: Need to test in Grid Setupa
		} else if (uri.contains("/conversion")){
			String action = (String) request.getParameter("action");
			String format = (String) request.getParameter("format");
			if("import".equalsIgnoreCase(action)) {
				increment(ZSStats.IMPORT_DOCS);
			} else if ("export".equalsIgnoreCase(action)) {		
				increment(ZSStats.EXPORT_DOCS);
//				incrementByActionConst(ActionConstants.EXPORT_DOCUMENT +""); // Other export Actions are by ActionConstants
//				if("xlsx".equalsIgnoreCase(format)) {
//					increment(ZSStats.EXPORT_FORMAT_XLSX);
//				} else if ("xls".equalsIgnoreCase(format)) {
//					increment(ZSStats.EXPORT_FORMAT_XLS);
//				} else if ("ods".equalsIgnoreCase(format)) {
//					increment(ZSStats.EXPORT_FORMAT_ODS);
//				} else if ("csv".equalsIgnoreCase(format)) {
//					increment(ZSStats.EXPORT_FORMAT_CSV);
//				} else if ("tsv".equalsIgnoreCase(format)) {
//					increment(ZSStats.EXPORT_FORMAT_TSV);
//				} else if ("pdf".equalsIgnoreCase(format)) {
//					increment(ZSStats.EXPORT_FORMAT_PDF);
//				} else if ("html".equalsIgnoreCase(format)) {
//					increment(ZSStats.EXPORT_FORMAT_HTML);
//				} else if ("xlsm".equalsIgnoreCase(format)) {
//					increment(ZSStats.EXPORT_FORMAT_XLSM);
//				}
			}
	
		}
		
		else if(containsImportURL(uri,IMPORT_IMPORT_MENU))
		{
			increment(ZSStats.IMPORT_IMPORT_MENU);
		}
		else if(containsImportURL(uri,IMPORT_PARTNERS_REMOTE_VIEW))
		{
			increment(ZSStats.IMPORT_PARTNERS_REMOTE_VIEW);
			increment(ZSStats.IMPORT_REMOTE);//Remote is PARTNER +AUTH usage
		}
		else if(containsImportURL(uri,IMPORT_AUTH_REMOTE))
		{
			increment(ZSStats.IMPORT_AUTH_REMOTE);
			increment(ZSStats.IMPORT_REMOTE);

		}
		else if(containsImportURL(uri,IMPORT_EXCEL_VIEWER))
		{
			increment(ZSStats.IMPORT_EXCEL_VIEWER);
		}
		else if(containsImportURL(uri,IMPORT_GADGET_VIEW))
		{
			increment(ZSStats.IMPORT_GADGET_VIEW);
		}
		else if(containsImportURL(uri,IMPORT_SCRATCH_VIEW))
		{
			increment(ZSStats.IMPORT_SCRATCH_VIEW);
		}
	//griveimport.do  again gets redirected internally again if import is required ,can't capture here ,stats moved to GdriveImportAction class
	//uploadBook.do has varying uri ,,stats moved to GdriveImportAction class
	//for conversion call import we check whether param =import or export so DOCS_IMPORT stats not done here but done above

		else if (uri.contains("/api/private/xml/sharenotifier") || uri.contains("/api/private/json/sharenotifier")){
			increment(ZSStats.SHARE_OPERATIONS);
			String event = (String) request.getParameter("event");
			if("add".equalsIgnoreCase(event)){
				increment(ZSStats.SHARE_NEW);
			} else if("change".equalsIgnoreCase(event)) {
				increment(ZSStats.SHARE_CHANGE_PERMISSION);
			} else if("remove".equalsIgnoreCase(event)){
				increment(ZSStats.SHARE_REMOVE);
			}
		
		// Zoho DB actions
		} else if (uri.contains("/ZDBTableDesignView.ve")){
			increment(ZSStats.ZDB_TABLE_DESIGN_VIEW);
		} else if (uri.contains("/ZDBDatabaseActionMI.ma")){
			increment(ZSStats.ZDB_DATABASE_ACTION);
		} else if (uri.contains("/ZDBTableGrid.ve")){
			increment(ZSStats.ZDB_TABLE_GRID);
		} else if (uri.contains("/ZDBGridActionMI.ma")){
			increment(ZSStats.ZDB_TABLE_DATA_ACTION);
		} else if (uri.contains("/DisplayChart.chart")){
			increment(ZSStats.ZDB_DISPLAY_CHART);
		} else if (uri.contains("/ZDBTableDataAction.ma")){
			increment(ZSStats.ZDB_TABLE_DATA);
		} else if (uri.contains("/ZDBAnalysisView.cc")){
			increment(ZSStats.ZDB_VIEW);
		} else if (uri.contains("/ZAGraphView.cc")){
			increment(ZSStats.ZDB_GRAPH);
		} else if (uri.contains("/ZAFilterUI.cc")){
			increment(ZSStats.ZDB_FILTER_ACTION);
		} else if (uri.contains("/ZDBVisualAnalysisAction.ma")){
			increment(ZSStats.ZDB_TABLE_PREVIEW);
		} else if (uri.contains("/zstour.do")) {
			int action = Integer.parseInt(request.getParameter("action"));
			switch(action){
				case 1: increment(ZSStats.TOUR_BUBBLE_DISPLAY);break; // Tour info populated
				case 2: increment(ZSStats.TOUR_REPLY);break; //Reply action
				case 3: increment(ZSStats.TOUR_READMORE);break; //read More action
				case 4: increment(ZSStats.TOUR_GOTIT);break; // got it option
				case 5: increment(ZSStats.TOUR_CALLOUT);break; // comment tour option
				default: break;
			}
		} else if (uri.contains("/usersettings.do")){
			increment(ZSStats.SWITCH_TO_NEWVERSION);
		}
			
	}
	/**
	 * check if the URL  is any one of the allowed URI for this  import call
	 *Multiple URL possible for same import action ,ex: remotedoc.im ,remotedoc.sas,remotedocImport.do
	 * @param uri
	 * @param import_url_type
	 * @return
	 */
	private static boolean containsImportURL(String uri,String import_url_type)
	{ 
		//should'nt hard code context path,used because sometimes context path ain't obtained properly from request.getContextPath
		if(uri.startsWith("/sheet"))
		{
			uri=uri.substring("/sheet".length());//NO I18N
		}
		return import_url_map.get(import_url_type).contains(uri);
	}
	
	public static void incrementByCustomValue(String zs_stats_const){
		increment(zs_stats_const);
	}

	public static void incrementByGroup(int value, Integer[] stats_value, String[] stats_name) {
		int size = stats_value.length;
		for(int i = 0; i <= size; i++) {
			if(i == (size-1) && value > stats_value[i]){
				increment(stats_name[i]);
				break;
			}
			
			int start = stats_value[i] == 0 ? stats_value[i] : (stats_value[i]+1);
			int end = stats_value[i+1];
			
			if(value >= start && value <= end){
				increment(stats_name[i]);
				break;
			}
			
		}
	}
	
	
}
