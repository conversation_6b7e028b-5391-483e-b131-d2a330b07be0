//$Id$//
package com.zoho.sheet.util;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.ArrayList;

import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import org.apache.commons.codec.binary.Base64;
import org.apache.tika.Tika;
import org.apache.tika.mime.MimeTypes;

import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.iam.security.UploadedFileItem;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.transmail.FileInfo;
import com.transmail.Files;
import com.transmail.Response;
import com.transmail.NameConstants.Authorization;
import com.zoho.conf.Configuration;
import com.zoho.sas.container.AppResources;

public class TransMailUtil {
	
	public   static final Logger LOGGER 	= 	Logger.getLogger(TransMailUtil.class.getName());
	private  String transMailServerUrl 		=   EnginePropertyUtil.getSheetPropertyValue("TRANSMAIL_API_URL");// NO I18N
	private  String authtoken          		=	EnginePropertyUtil.getSheetPropertyValue("TRANSMAIL_AUTH_TOKEN");// NO I18N
	private  String scope 					=   EnginePropertyUtil.getSheetPropertyValue("TRANSMAIL_API_SCOPE");// NO I18N
	private  String project_Key				=   EnginePropertyUtil.getSheetPropertyValue("TRANSMAIL_USER_PROJECT_KEY");// NO I18N
	private  String uploadMethod			=   Constants.TRANMAIL_ATTACH_ENCODE_METHODE; // Can be ENCODE or UPLOAD 
	private JSONObjectWrapper mailDetailsJObject 	= 	new JSONObjectWrapper();
	
	public TransMailUtil(){
		try{
			this.authtoken 		=  	URLEncoder.encode(this.authtoken,"UTF-8");// No I18N
			this.scope 			= 	URLEncoder.encode(this.scope,"UTF-8");// No I18N
			this.project_Key	= 	URLEncoder.encode(this.project_Key,"UTF-8");// No I18N
		}
		catch(Exception e){
			
		}
	}
//	public TransMailUtil(HttpServletRequest request,JSONObject mailDetails,String uploadMethod){
//		try{
//			this.authtoken 		=  	URLEncoder.encode(this.authtoken,"UTF-8");// No I18N
//			this.scope 			= 	URLEncoder.encode(this.scope,"UTF-8");// No I18N
//			this.project_Key	= 	URLEncoder.encode(this.project_Key,"UTF-8");// No I18N
//		}
//		catch(Exception e){
//
//		}
//		if(uploadMethod !=null && !uploadMethod.isEmpty()){
//			this.uploadMethod = uploadMethod;
//		}
//		if(! mailDetails.isNullObject() && !mailDetails.isEmpty() ){
//			mailDetails.put( "Project-Key", this.project_Key );
//			this.mailDetailsJObject = mailDetails;
//		}
//		processAttachment(request);
//	}
//	private void processAttachment(HttpServletRequest request){
//		if(this.uploadMethod.equals(Constants.TRANMAIL_ATTACH_ENCODE_METHODE)){
//
//			try{
//				JSONArray _attachmentJArray = getAttchmentDetails(request);
//				if(_attachmentJArray != null && !_attachmentJArray.isEmpty()){
//					this.mailDetailsJObject.put("Attachments",_attachmentJArray);
//				}
//			}catch(Exception e){
//				LOGGER.info("Exception occured while UploadAttchment  "+e);
//			}
//		}
//		else if(this.uploadMethod.equals("UPLOAD")){
//			try{
//				JSONArray _attachmentJArray = getUploadAttchmentDetails(request);
//				if(_attachmentJArray != null && !_attachmentJArray.isEmpty()){
//					this.mailDetailsJObject.put("AttachmentsID",_attachmentJArray);
//				}
//			}catch(Exception e){
//				LOGGER.info("Exception occured whileencoding Attchment  "+e);
//			}
//		}
//	}

//	public  void dispatch(){
//		try{
//			List<NameValuePair> nvPairList 	= 	new ArrayList<>();
//			HttpClient httpClient 			= 	new HttpClient();
//			String _transMailServerUrl      =   transMailServerUrl+"json/sendmail";// NO I18N
//			PostMethod post 		  		= 	new PostMethod(_transMailServerUrl);
//			String mailDetails  			= 	this.mailDetailsJObject.toString();
//			nvPairList.add(new NameValuePair("authtoken", this.authtoken));// No I18N
//			nvPairList.add(new NameValuePair("scope", this.scope));// No I18N
//			nvPairList.add(new NameValuePair("mailDetails", mailDetails + ""));// No I18N
//			NameValuePair[] nvpair = new NameValuePair[nvPairList.size()];
//			for (int i = nvPairList.size() - 1; i >= 0; i--)
//			{
//			    nvpair[i] = nvPairList.get(i);
//			}
//			post.addParameters(nvpair);
//			JSONObject jObj = new JSONObject(mailDetails);
//			int statusCode = httpClient.executeMethod(post);
//			LOGGER.info("dispatch statusCode ::::::"+statusCode+" Response ::"+post.getResponseBodyAsString());
//		}
//		catch(Exception e){
//			LOGGER.info("Exception while sending mail httpRes ::::::"+e);
//		}
//
//	}
	private static JSONArrayWrapper getAttchmentDetails(HttpServletRequest request) throws Exception {
		String fileName = null;
		JSONArrayWrapper attachmentJSONArray = new JSONArrayWrapper();
		if (request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST) != null && ((ArrayList<UploadedFileItem>) request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST)).size() > 0) {
			ArrayList<UploadedFileItem> filesList = (ArrayList<UploadedFileItem>) request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST);
			for (UploadedFileItem uploadedFileItem : filesList) {
				InputStream fis = null;
				BufferedInputStream bis = null;
				ByteArrayOutputStream bos = null;
				OutputStream out = null;
				JSONObjectWrapper attatchmentJObj = new JSONObjectWrapper();
				try {
					fileName = uploadedFileItem.getFileName();
					File sourceFile = uploadedFileItem.getUploadedFile();
					attatchmentJObj.put("Name", fileName);
					attatchmentJObj.put("Content", encodeImage(sourceFile.getAbsolutePath()));
					attatchmentJObj.put("ContentType",uploadedFileItem.getDetectedContentType());
					attachmentJSONArray.put(attatchmentJObj);
				} catch (Exception e) {
					LOGGER.info(" Exception while uploading documnet :::"+e );
					return null;
				} finally {
					if (fis != null) {
						fis.close();
					}
					if (bis != null) {
						bis.close();
					}
					if (bos != null) {
						bos.close();
					}
					if (out != null) {
						out.close();
					}
				}
			}
			
		}
		return attachmentJSONArray;
	}
	
	
	
//	private  JSONArray getUploadAttchmentDetails(HttpServletRequest request) throws Exception {
////            System.out.println("getUploadAttchmentDetails >>> transmail : " + request);
//		String fileName = null;
//		JSONArray uploadJArray = new JSONArray();
//		if (request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST) != null && ((ArrayList<UploadedFileItem>) request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST)).size() > 0) {
//			ArrayList<UploadedFileItem> filesList = (ArrayList<UploadedFileItem>) request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST);
////			                 System.out.println("filesList1 :" + filesList);
//			for (UploadedFileItem uploadedFileItem : filesList) {
//				try {
//					fileName = uploadedFileItem.getFileName();
//					File sourceFile = uploadedFileItem.getUploadedFile();
////                                        System.out.println("fileName1 : " +fileName);
////                                        System.out.println("sourceFile1 : "+ sourceFile);
//					JSONObject uploadedId = uploadFile(new File(sourceFile.getAbsolutePath()),fileName);
//					if(uploadedId.has("UploadID")){
//						uploadJArray.put(uploadedId.get("UploadID"));
//					}
//				} catch (Exception e) {
////                                    System.out.println("exception1!!!!");
//					LOGGER.info(" Exception while uploading documnet :::"+e );
//					return null;
//				}
//			}
//
//		}
//		return uploadJArray;
//	}
	
	private static String encodeImage(String fileName) throws IOException
    {
		File file = new File(fileName);
		InputStream fis = new FileInputStream(file);
		long length = file.length();
		byte[] bytes = new byte[(int) length];
		int offset = 0;
		int numRead = 0;
		while (offset < bytes.length && (numRead = fis.read(bytes, offset, bytes.length - offset)) >= 0)
		{
		    offset += numRead;
		}
		if(offset < bytes.length) { throw new IOException("Could not completely read file " + file.getName()); }
		fis.close();
		byte[] encoded = Base64.encodeBase64(bytes);
		return new String(encoded);
    }
	
	
//	private  JSONObject  uploadFile(File file, String fileName){
//    	try{
//
//    		  String fName 			= 	saveImg(file,fileName);
//    		  String dir 			= 	AppResources.getProperty("server.dir") + File.separator + "bin" + File.separator + "feedbackattachments";// NO I18N
//    		  file 					= 	new File(dir+File.separator+fName);
//    		  project_Key			= 	URLEncoder.encode(project_Key, "UTF-8");// NO I18N
//			  authtoken				= 	URLEncoder.encode(authtoken, "UTF-8");// NO I18N
//			  String filetype		= 	URLEncoder.encode("attachment", "UTF-8");// NO I18N
//			  String contenttype	=	URLEncoder.encode(URLConnection.guessContentTypeFromName(fileName), "UTF-8");// NO I18N
//    		  String _transMailServerUrl 				= 	transMailServerUrl+"file/upload/multipart?Project-Key="+project_Key+"&authtoken="+authtoken+"&filetype="+filetype+"&contenttype="+contenttype;// NO I18N
//	    	  HttpClient client 				= 	new HttpClient();
//	    	  PostMethod post					=	new PostMethod(_transMailServerUrl);
//	    	  Part[] parts 						= 	new Part[1];
//			  parts[0] 							=  	new FilePart("file", file);// NO I18N
//			  MultipartRequestEntity mpEntity	=	new MultipartRequestEntity(parts,post.getParams());
//	    	  post.setRequestEntity(mpEntity);
//	    	  int statusCode = client.executeMethod(post);
//	    	  LOGGER.info("upload resposne ::"+post.getResponseBodyAsString());
//	    	  return (new JSONObject(post.getResponseBodyAsString()));
//
//    	}
//    	catch(Exception e){
//    		LOGGER.info(" Exception  while multopartupload::"+e);
//    	}
//    	return null;
//
//    }
	
	//SAVE the image temporary in server 
	private static String saveImg(File sourceFile,String fileName){
		try{
			InputStream fis = null;
			BufferedInputStream bis = null;
			ByteArrayOutputStream bos = null;
			OutputStream out = null;
			fis = new FileInputStream(sourceFile);
			String dir = Configuration.getString("app.home") + File.separator + "bin" + File.separator + "feedbackattachments";//No internationalization
			File destFile = new File(dir);
			if (!destFile.exists()) {
				LOGGER.info("DIR Creation for feedback form attachmentst in websheet dir with name feedbackattachments : " + destFile.mkdir()); //No internationalization
			}
			fileName = System.currentTimeMillis() + "_" + fileName;
			String filePath = dir + File.separator + fileName;
                        
                        File f = new File(filePath);
                        String canonicalPath = f.getCanonicalPath();
                        if(canonicalPath.startsWith(dir)) {
                            
                            bis = new BufferedInputStream(fis);
                            bos = new ByteArrayOutputStream();
                            int BUF_LEN = 1024;
                            byte[] bufr = new byte[BUF_LEN];
                            int c = 0;
                            while ((c = bis.read(bufr, 0, BUF_LEN)) != -1) {
                                    bos.write(bufr, 0, c);
                            }
                            byte[] byteArr = bos.toByteArray();
                            out = new FileOutputStream(filePath);
                            out.write(byteArr);
                        }
		}
		catch(Exception e){
			e.printStackTrace();
		}
		return fileName;
	}
	
	public  void reportError(String documentId, String subject, Exception exception){
		ByteArrayOutputStream bw = null;
        PrintWriter pw = null;
		try
		{
		    bw = new ByteArrayOutputStream();
		    pw = new PrintWriter(bw);

		    exception.printStackTrace(pw);
		    pw.flush();
		    // get the exception trace
		    String content = bw.toString();
		    reportError(documentId,subject,exception);
		}
		catch (Exception exp)
		{
			LOGGER.log(Level.WARNING,"Engine: Error while Sending Mail.",exp);
		    //exp.printStackTrace();
		}
		finally
		{
		    try
		    {
			if(pw != null)
			{
			    pw.close();
			}
			if(bw != null)
			{
			    bw.close();
			}
		    }
		    catch(IOException ioe)
		    {}
		}
	}
//	public  void reportError(String documentId, String subject, String exception){
//
////		if(!EngineConstants.isReportError)
////		{
////	            LOGGER.log(Level.WARNING,null,exception);
////	            return;
////		}
//		LOGGER.log(Level.WARNING,"reportError ::::::");
//		if(documentId!=null && !documentId.isEmpty()){
//	        subject = "Doc Id: "+documentId+" :: "+subject; //No I18N
//		}
//	       try
//			{
//
//			    try
//				{
//				    String contentType = "text/plain"; //No I18N
//
//				    // Server IPAddress for sender
////				    String fromUserEmail = InetAddress.getLocalHost().getHostName()+"@"+EnginePropertyUtil.getSheetPropertyValue("DomainName"); //No I18N
//				    String fromUserEmail = "<EMAIL>";//No I18N
//				    String mailIds = "vasee<<EMAIL>>,ramesh<<EMAIL>>,mani<<EMAIL>>,abinaya<<EMAIL>>"; //No I18N
////				    String mailIds = "nilam<<EMAIL>>,sahu<<EMAIL>>,tiku<<EMAIL>>,sasi<<EMAIL>>"; //No I18N
//				    JSONObject mailDetail = new JSONObject();
//				    mailDetail.put("MailFrom", fromUserEmail);
//					mailDetail.put("From", fromUserEmail);
//					mailDetail.put("To",mailIds);
//					mailDetail.put("Subject",subject);
//					mailDetail.put("TextBody",exception);
//					mailDetail.put( "Project-Key", EnginePropertyUtil.getSheetPropertyValue("TRANSMAIL_BUG_PROJECT_KEY") );
//					this.mailDetailsJObject = mailDetail;
//					this.dispatch();
//				}
//				catch (Exception exp)
//				{
//					LOGGER.log(Level.WARNING,"Engine: Error while Sending Mail.",exp);
//				    //exp.printStackTrace();
//				}
//
//			}
//			catch (Exception exp)
//			{
//				LOGGER.log(Level.WARNING,"Engine: Error while Sending Mail.",exp);
//			    //exp.printStackTrace();
//			}
//	}
	
	
	public static String transMailFileUpload(byte[] fileContent, String fileName) {
		final Tika _mimeTika = new Tika(new MimeTypes());
	    String mimeType     = _mimeTika.detect(fileContent);
        String authtoken    = EnginePropertyUtil.getSheetPropertyValue("zst_authtoken"); //NO I18N 
        String mailAgentKey = EnginePropertyUtil.getSheetPropertyValue("APPLICATION_MAIL_AGENT_KEY"); //No I18N
        try {
            Files client = new Files(mailAgentKey, Authorization.AUTH_TOKEN, authtoken);
            FileInfo fileInfo = new FileInfo( new ByteArrayInputStream(fileContent),fileName,mimeType);
            client.setContent(fileInfo);

            Response respo = client.uploadSync();
            JSONObjectWrapper responseObject = new JSONObjectWrapper(respo.getEntity());
            if (responseObject.has("file_cache_key")) {
                return responseObject.getString("file_cache_key");
            } else {
                LOGGER.log(Level.INFO, "Feedback Attachment: There is problem while uploading file to the tramsmail, filePath:  {0}", responseObject);
                return null;
            }

        } catch (Exception e) {
            LOGGER.log(Level.INFO, "Feedback Attachment: Exception occured while attaching file :{0}", e);
            return null;
        }
        
    }
	
	
	
	

}
