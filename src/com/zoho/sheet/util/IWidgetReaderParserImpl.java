/* $Id$ */

package com.zoho.sheet.util;

import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.EngineConstants;


/**
 * <AUTHOR>
 *
 */
public class IWidgetReaderParserImpl {

	/*
	Task	task;
	public IWidgetReaderParserImpl( Task task) throws NumberFormatException, Exception {
		
		this.task	=	task;
	}
	
	@Override
	public WidgetInfo getWidgetInfo() throws Exception {
		
		WorkbookContainer	container	=	CurrentRealm.getContainer();
		String 	version					=	CurrentRealm.getWorkBookIdentity();
		String	sheetName				=	task.getSheetName();
		String	fileFormat				=	(version == null) ?	container.getFileFormat() : container.getFileFormatForVersion(version);
		
		DocumentInitiator loader		=	ClientUtils.getDocumentInitiator(container, fileFormat, sheetName, task.readSheetNames, task.readUsedRange, 
																					task.readRowDimension, task.readColDimension, 
																						task.getUsedRowIndex(), task.getUsedColIndex());
		IWidgetInfo	iwidgetInfo			=	new IWidgetInfo();
		
		updateWidgetInfo(task, iwidgetInfo);
		
        loader.updateIWidgetInfo(iwidgetInfo);
        
		//validateTask(widgetInfo, this.task);
		
		return iwidgetInfo;
	}

	@Override
	public void setUserInputSheetName(String sheetName, String[] sheetNames) throws Exception {
            this.task.setSheetNames(sheetNames);
            this.task.setActiveSheetName(sheetName);
		
//		if(this.task.sheetNames() != null) {
//			for(String _sheetName : this.task.sheetNames()){
//				if(_sheetName.equalsIgnoreCase(sheetName)){
//					this.task.setActiveSheetName(_sheetName);
//					break;
//				}
//			}
//		}

	}
	
	private void updateWidgetInfo(Task task, WidgetInfo info)	{
		
				IWidgetInfo		iwidgetInfo		=	(IWidgetInfo)info;
				
				 	iwidgetInfo.setSheetNames(task.sheetNames());
					iwidgetInfo.setActiveSheetName(task.getSheetName());
                                        iwidgetInfo.setTStyles(task.getTStyles());
					iwidgetInfo.setActiveRanges(task.getActiveRanges());
					iwidgetInfo.setActiveCell(task.getActiveCell());
					iwidgetInfo.setFilterInfo(task.getFilterInfo());
					iwidgetInfo.setColumnHeaders(task.getColumnStyles());
					iwidgetInfo.setRowHeaders(task.getRowstyles());
					iwidgetInfo.setHiddenColumns(task.getHiddenColumns());
					iwidgetInfo.setHiddenRows(task.getHiddenRows());
					iwidgetInfo.setPersistedposition(task.getPersistedposition());
					iwidgetInfo.setUsedColIndex(task.getUsedColIndex());
					iwidgetInfo.setUsedRowIndex(task.getUsedRowIndex());
					iwidgetInfo.setSheetInfo(task.getSheetInfo());
                                        iwidgetInfo.setZoom(task.getSheetZoom());
					iwidgetInfo.setSheetView(task.getSheetView());
					iwidgetInfo.setFreezepanes(task.getFreezePanes());
    }
	*/
}
