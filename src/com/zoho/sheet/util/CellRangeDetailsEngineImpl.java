//$Id$
package com.zoho.sheet.util;

//NOTE: There May Be Chance Of Issue While Reading Borders
//Have To Optimize Code.

import java.util.HashMap;

import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.util.EngineConstants;

import com.adventnet.zoho.websheet.model.util.EngineUtils;
import com.adventnet.zoho.websheet.model.util.SheetVariables;
import java.util.logging.Logger;


public class CellRangeDetailsEngineImpl extends CellRangeDetails{

    public final static Logger LOGGER = Logger.getLogger(CellRangeDetailsEngineImpl.class.getName());

	public CellRangeDetailsEngineImpl(WorkbookContainer container, String activesheetname, int [] rowsAry, int startCol, int endCol, boolean showFormulas, String associatedSheetName)throws Exception{
		super(container,activesheetname, rowsAry,startCol,endCol, showFormulas, associatedSheetName);
	}

	/*
		To Set Details.
	*/
    @Override
	protected void processDocument() throws Exception{

		WorkbookContainer container = (WorkbookContainer)documentObject;
                Workbook workbook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());
		Sheet sheet;
		if(this.associatedSheetName	!=	null)	{
			
			sheet = workbook.getSheetByAssociatedName(this.associatedSheetName);
            this.activesheetname = sheet.getName();
		}	else	{
			sheet = workbook.getSheet(this.activesheetname);
		}
                
                CellStyle defaultCellStyle = workbook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME);
                this.defaultFontName = defaultCellStyle.getPropertyAsString_deep(TextStyle.Property.FONTNAME, workbook);
                this.defaultFontSize = defaultCellStyle.getPropertyAsString_deep(TextStyle.Property.FONTSIZE, workbook);
                
                this.defaultRowHeight = workbook.getDefaultRowHeight();
                this.defaultColumnWidth = workbook.getDefaultColumnWidth();

		HashMap<?, ?> sheetDetails = EngineUtils.getInstance().readSheetDetails(container, CurrentRealm.getWorkBookIdentity(), sheet, rowsAry, startCol, endCol, false, showFormulas);
		SheetVariables sheetvar = 	(SheetVariables)sheetDetails.get("SHEETVARIABLES");
		cellValues = sheetvar.sheetvalues;
		cellFormulas = sheetvar.sheetformulas;
		cellStyles = sheetvar.sheetstyles;
		cellAnnots = sheetvar.sheetannots;
		cellBottomBorders = sheetvar.cellBottomBorders;
		cellTopBorders = sheetvar.cellTopBorders;
		
		cellLeftBorders = sheetvar.cellLeftBorders;
		cellRightBorders = sheetvar.cellRightBorders;
		colSpan = sheetvar.colspan;
		rowSpan = sheetvar.rowspan;
		cellHyper = sheetvar.hyper;
		colWidth = sheetvar.colWidth;
		rowHeight = sheetvar.rowHeight;
		literalColSpan	=   sheetvar.literalColspan;
        literalRowSpan  =   sheetvar.literalRowspan;
        
		//For Column Merge Details.
		colMergeDetails = sheetvar.mergeAry.toString();
                                // having this to avoid redundant parsing in sheettable.jsp.
                                // colMergeDetails should be removed and this(colMergeDetailsJson) should be used instead asap
                                colMergeDetailsJson = sheetvar.mergeAry;        
		dataInformation = sheetvar.dataObject;
		coveredCellInfo	=	sheetvar.coveredCellInfo;
		
		dValidation = sheetvar.dValidation;//DataValidation
				conditionalFormatInfo = sheetvar.conditionalFormatInfo;
                checkboxInfo = sheetvar.checkboxInfo;
                freezepane = sheetvar.freezepane;
	}
}
