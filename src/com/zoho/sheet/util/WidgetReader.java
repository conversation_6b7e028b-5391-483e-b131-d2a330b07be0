/* $Id$ */
package com.zoho.sheet.util;

import com.adventnet.zoho.websheet.model.FocusObject;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.WorkbookSettings;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import java.util.logging.Level;
import java.util.logging.Logger;


public interface WidgetReader {
	public WidgetInfo getWidgetInfo()  throws Exception;
	public void setUserInputSheetName(String sheetName, String[] sheetNames)  throws Exception;
	public static Logger LOGGER = Logger.getLogger(WidgetReader.class.getName());
	public static class Task {
		boolean readSheetNames;
		boolean readUsedRange; 
		boolean readRowDimension;
		boolean readColDimension;
 

		private String	activeSheetName;	private	int  usedRowIndex;	private	int	usedColIndex; private String[] sheetNames; String rowDimensionStrng;
                private int activeRowIndex; private int activeColIndex; private int positionBtm; private int positionLft;
                
         //Introducing new  members for new hcache.properties
                private      JSONArrayWrapper	activeRanges;
                private      JSONArrayWrapper	sheetInfo;
                private      String	sheetView;
                private		JSONArrayWrapper	activeCell;
                private		JSONArrayWrapper	persistedposition;
                private		JSONObjectWrapper	filterInfo;
                private 	JSONArrayWrapper	rowstyles;
                private 	JSONArrayWrapper	columnStyles;
                private 	JSONArrayWrapper	hiddenRows;
                private 	JSONArrayWrapper	hiddenColumns;
                private     JSONObjectWrapper freezePanes;
           
                private         int             sheetZoomVal;
		private     JSONObjectWrapper          tStyles;
                
		public Task(boolean readSheetNames, boolean readUsedRange, boolean readRowDimension, boolean readColDimension) {
			this.readSheetNames			=	readSheetNames;
			this.readUsedRange			=	readUsedRange;
			this.readRowDimension			=	readRowDimension;
			this.readColDimension			=	readColDimension;
		}
		
		public Task(){}
		
		
		public void	setActiveSheetName(String	sheetName) { 
			this.activeSheetName	=	sheetName;
		}
		
		public void	setUsedArea(int usedRowIndex, int usedColIndex){
			this.usedRowIndex	=	usedRowIndex;
			this.usedColIndex	=	usedColIndex;
		}
                public void setCursorPosition(int activeRowIndex, int activeColIndex, int positionBtm, int positionLft){
			this.activeRowIndex	=	activeRowIndex;
			this.activeColIndex	=	activeColIndex;
                        this.positionBtm        =       positionBtm;
                        this.positionLft        =       positionLft;
		}
                
                 public void setCursorPosition(WorkbookContainer container) {
                    try {
                            Workbook workbook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());

                           if (container.getCurrCursorPosSheetCodeName() != null) {

                                String sheetName = workbook.getSheetByAssociatedName(container.getCurrCursorPosSheetCodeName()).getName();

                                this.activeSheetName = sheetName;
                            }
                            this.setSheetNames(workbook.getSheetNames(true));


                            WorkbookSettings wbSettings = workbook.getWorkbookSettings();
                            this.activeRowIndex = wbSettings.getActiveRowIndex(activeSheetName);
                            this.activeColIndex = wbSettings.getActiveColIndex(activeSheetName);
                            this.positionBtm = wbSettings.getPositionBottom(activeSheetName);
                            this.positionLft = wbSettings.getPositionLeft(activeSheetName);
                            //for new client
                            this.activeCell = new JSONArrayWrapper().put(this.activeRowIndex).put(this.activeColIndex);
                            JSONArrayWrapper pane0Rect = new JSONArrayWrapper().put(this.positionBtm).put(this.positionLft);
                            JSONArrayWrapper pane1Rect = new JSONArrayWrapper("[0,0]");
                            this.persistedposition = new JSONArrayWrapper().put(pane0Rect).put(pane1Rect);



                    } catch (Exception ex) {
                        LOGGER.log(Level.WARNING, "RESOURCE_ID: " + container.getResourceKey() + " Exception while setting activeSheetName in task : ", ex);
                    }

        //                    FocusObject focusObj = container.getFocusObject();
        //                    if(focusObj != null)
        //                    {
        //						this.activeRowIndex	=	focusObj.getRowIndex();
        //						this.activeColIndex	=	focusObj.getColIndex();
        //                        this.positionBtm        =       focusObj.getPosBottom();
        //                        this.positionLft        =       focusObj.getPosLeft();
        //                        
        //                        //for new client
        //                        this.activeCell = new JSONArrayWrapper().put(this.activeRowIndex).put(this.activeColIndex);
        //                        JSONArrayWrapper pane0Rect = new JSONArrayWrapper().put(this.positionBtm).put(this.positionLft);
        //                        JSONArrayWrapper pane1Rect = new JSONArrayWrapper("[0,0]");
        //                        this.persistedposition = new JSONArrayWrapper().put(pane0Rect).put(pane1Rect);
        //                        
        //                        try {
        //                        	 Workbook workbook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());
        //                        	if(container.getCurrCursorPosSheetCodeName() != null)
        //                    		{
        //	                                                       
        //	                            String sheetName = workbook.getSheetByAssociatedName(container.getCurrCursorPosSheetCodeName()).getName();
        //                            
        //                            	this.activeSheetName    =       sheetName;
        //                            }
        //                            this.setSheetNames(workbook.getSheetNames(true));
        //                        
        //                        } catch (Exception ex) {
        //                            LOGGER.log(Level.WARNING, "RESOURCE_ID: "+container.getResourceKey()+" Exception while setting activeSheetName in task : ", ex);
        //                        }                        
        //                    }                    
                }
                
		public void setSheetNames(String[] sheetNames){
			this.sheetNames	=	sheetNames;
		}
                
                public void setSheetInfo(JSONArrayWrapper sheetInfo){
                    this.sheetInfo = sheetInfo;
                }
                public JSONArrayWrapper getSheetInfo(){
                    return this.sheetInfo;
                }
                
                public void setSheetView(String view){
                    this.sheetView = view;
                }
                
                public String getSheetView(){
                    return this.sheetView;
                }
                
                public void setFreezePanes(JSONObjectWrapper freezepanes) {
                    this.freezePanes = freezepanes;
                }
                
                public JSONObjectWrapper getFreezePanes(){
                    return this.freezePanes;
                }
		
		public void setRowDimensions(String rowDimensionStrng)	{
			this.rowDimensionStrng			=	rowDimensionStrng;
		}
		
		
		String getSheetName() {
			return this.activeSheetName;
		}
		
		int getUsedRowIndex() {
			return this.usedRowIndex;
		}
                int getActiveRowIndex(){
                    return this.activeRowIndex;
                }
                int getActiveColIndex(){
                    return this.activeColIndex;
                }
                int getPositionBottom(){
                    return this.positionBtm;
                }
                int getPositionLeft(){
                    return this.positionLft;
                }
		
		int getUsedColIndex() {
			return this.usedColIndex;
		}
		
		String[] sheetNames() {
			return this.sheetNames;
		}
		
		String getRowDimensionStrng() {
			return	rowDimensionStrng;
		}
		
		public JSONArrayWrapper getActiveRanges() {
			return activeRanges;
		}

		public void setActiveRanges(JSONArrayWrapper activeRanges) {
			this.activeRanges = activeRanges;
		}

		public JSONArrayWrapper getActiveCell() {
			return activeCell;
		}

		public void setActiveCell(JSONArrayWrapper activeCell) {
			this.activeCell = activeCell;
		}

		 public	JSONArrayWrapper getPersistedposition() {
			return persistedposition;
		}

		 public void setPersistedposition(JSONArrayWrapper persistedposition) {
			this.persistedposition = persistedposition;
		}

		public JSONObjectWrapper getFilterInfo() {
			return filterInfo;
		}

		public void setFilterInfo(JSONObjectWrapper filterInfo) {
			this.filterInfo = filterInfo;
		}

		public JSONArrayWrapper getRowstyles() {
			return rowstyles;
		}

		public void setRowstyles(JSONArrayWrapper rowstyles) {
			this.rowstyles = rowstyles;
		}

		public JSONArrayWrapper getColumnStyles() {
			return columnStyles;
		}

		public void setColumnStyles(JSONArrayWrapper columnStyles) {
			this.columnStyles = columnStyles;
		}

		public JSONArrayWrapper getHiddenRows() {
			return hiddenRows;
		}

		public void setHiddenRows(JSONArrayWrapper hiddenRows) {
			this.hiddenRows = hiddenRows;
		}

		public JSONArrayWrapper getHiddenColumns() {
			return hiddenColumns;
		}

		public void setHiddenColumns(JSONArrayWrapper hiddenColumns) {
			this.hiddenColumns = hiddenColumns;
		}
                
                public void setSheetZoom(int zVal) {
                    this.sheetZoomVal = zVal;
                }
                
                public int getSheetZoom() {
                    return this.sheetZoomVal;
                }
                
                public void setTStyles(JSONObjectWrapper tStyles) {
                    this.tStyles = tStyles;
                }
                
                public JSONObjectWrapper getTStyles() {
                    return this.tStyles;
                }
                
 	}
	
	
}
