//$ID$
package com.zoho.sheet.compression.playground;

import com.zoho.sheet.compression.image.ImageCompression;
import com.zoho.sheet.compression.image.ThumbnailGenerator;
import com.zoho.sheet.compression.image.util.ImageUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ImageCompressionPlayground {

    private static final Logger LOGGER = Logger.getLogger(ImageCompressionPlayground.class.getName());

    public static void main(String[] args) throws IOException {

        String imagePath = "/Users/<USER>/Desktop/ImagesCompression/sample.jpg";    //NO I18N
        String outputPath = "/Users/<USER>/Desktop/ImagesCompression/img_compressed.jpg";   //NO I18N
        String thumbnailPath = "/Users/<USER>/Desktop/ImagesCompression/img_thumbnail.jpg";     //NO I18N

        long startTime = System.currentTimeMillis();
        byte[] compressedBytes = ImageCompression.compressImage(new File(imagePath));
        if (compressedBytes != null) {
            LOGGER.log(Level.INFO, "Image compressed successfully"); //NO I18N

            try (FileOutputStream fileOutputStream = new FileOutputStream(outputPath)) {
                fileOutputStream.write(compressedBytes);
            } catch (IOException e) {
                LOGGER.log(Level.INFO, "Error in writing compressed image to file");     //NO I18N
            }

            try(FileInputStream fis = new FileInputStream(imagePath))
            {
                byte[] thumbnailBytes = ThumbnailGenerator.generateThumbnail(ImageUtil.getImageData(fis));
                try (FileOutputStream fileOutputStream = new FileOutputStream(thumbnailPath)) {
                    fileOutputStream.write(thumbnailBytes);
                } catch (IOException e) {
                    LOGGER.log(Level.INFO, "Error in writing compressed image to file");   //NO I18N
                }
            }


        } else {
            LOGGER.log(Level.INFO, "Error in compressing image");   //NO I18N
        }
        long endTime = System.currentTimeMillis();
        LOGGER.log(Level.INFO, "Time taken: {0} ms", new Object[]{endTime - startTime});   //NO I18N
    }
}
