/* $Id$ */
package com.zoho.sheet.chart.engine;

import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ZArrayI;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.query.model.DataAggregatorResult;
import com.adventnet.zoho.websheet.model.query.model.Summary.SummarizeOperation;
import com.adventnet.zoho.websheet.model.query.model.GroupByCriteria;
import com.adventnet.zoho.websheet.model.query.model.Summary;
import com.adventnet.zoho.websheet.model.query.model.TabularData;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.JepException;
import com.zoho.sheet.chart.CellProperties;
import com.zoho.sheet.util.I18nMessage;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import java.util.logging.Logger;

public class AggregationTreeTraversal {
	
	
	public static final Logger LOGGER = Logger.getLogger(AggregationTreeTraversal.class.getName());
	private String seriesIn;
	private boolean firstRowAsLabel;
	private boolean firstColAsLabel;
	private List<Integer> numericColumns;
	private List<Integer> groupByColumns;
	private int colSize;
	private Sheet sheet;
	private String aggregationOperation;
	private Map<Integer, String> groupByOperationsMap;
	private Transformation transformation;
	private final Map<Integer, String> numberToMonthMap = new HashMap<Integer, String>();
	private final Map<Integer, String> numberToQuarterMap = new HashMap<Integer, String>();
	private final Map<Integer, String> numberToWeekDayMap = new HashMap<Integer, String>();

	// Variables for TabularData
	private List<GroupByCriteria> groupByCriterias;
	private TabularData tabularData;
	private List<ZArrayI> summaryRanges;
	
	enum Transformation{
		GROUPING,
		TRANSFORM
	}
	
	private void initEssentialDetails() {
		numberToMonthMap.put(1, "Jan");
		numberToMonthMap.put(2, "Feb");
		numberToMonthMap.put(3, "Mar");
		numberToMonthMap.put(4, "Apr");
		numberToMonthMap.put(5, "May");
		numberToMonthMap.put(6, "Jun");
		numberToMonthMap.put(7, "Jul");
		numberToMonthMap.put(8, "Aug");
		numberToMonthMap.put(9, "Sep");
		numberToMonthMap.put(10, "Oct");
		numberToMonthMap.put(11, "Nov");
		numberToMonthMap.put(12, "Dec");
		
		numberToQuarterMap.put(1, "Q1");
		numberToQuarterMap.put(2, "Q2");
		numberToQuarterMap.put(3, "Q3");
		numberToQuarterMap.put(4, "Q4");
		
		numberToWeekDayMap.put(1, "Sun");
		numberToWeekDayMap.put(2, "Mon");
		numberToWeekDayMap.put(3, "Tue");
		numberToWeekDayMap.put(4, "Wed");
		numberToWeekDayMap.put(5, "Thu");
		numberToWeekDayMap.put(6, "Fri");
		numberToWeekDayMap.put(7, "Sat");
	}
	public AggregationTreeTraversal(Sheet sheet, String seriesIn, boolean firstRowAsLabel, boolean firstColAsLabel, List<Integer> numericColumns, List<Integer> groupByColumns, int colSize, List<GroupByCriteria> groupByCriterias, TabularData tabularData, List<ZArrayI> summaryRanges) {
		this.sheet = sheet;
		this.seriesIn = seriesIn;
		this.firstRowAsLabel = firstRowAsLabel;
		this.firstColAsLabel = firstColAsLabel;
		this.numericColumns = numericColumns;
		this.groupByColumns = groupByColumns;
		this.colSize = colSize;

		this.groupByCriterias = groupByCriterias;
		this.tabularData = tabularData;
		this.summaryRanges = summaryRanges;
		initEssentialDetails();
	}
	
	public Object[][] traverseResultToForm2DArray(Object[][] unAggregatedFinalAry, String aggregationOperation, Map<Integer, String> groupByOperationsMap){
		Object[][] finalAry = null;
		DataAggregatorResult dataAggregatorResult = null;
		if(tabularData == null){
			return finalAry;
		}else {
			dataAggregatorResult = tabularData.getRootNode();
		}


		this.aggregationOperation = aggregationOperation;
		this.groupByOperationsMap = groupByOperationsMap;
		this.transformation = Transformation.GROUPING;
		if(this.groupByOperationsMap!= null && this.groupByOperationsMap.size() ==2) {
			this.transformation = Transformation.TRANSFORM;
		}

		List<DataAggregatorResult> children = tabularData.getGroupedNodes(dataAggregatorResult, groupByCriterias.get(0));
		List<Object[]> finalList = new ArrayList<Object[]>();
		int colSize = (this.aggregationOperation.equals("COUNT")) ? this.groupByColumns.size()+1 : this.numericColumns.size() + this.groupByColumns.size();
		
		traverseNodesBasedOnTransformationType(colSize, children, finalList, unAggregatedFinalAry);
		
		if(finalList == null || finalList.size() == 0){
			return null;
		}
		finalAry = construct2DArrayFromList(finalList, finalAry, unAggregatedFinalAry);
		
		return finalAry;
	}
	
	private void traverseNodesBasedOnTransformationType(int colSize, List<DataAggregatorResult> children, List<Object[]> finalList, Object[][] unAggregatedFinalAry) {
		if(this.transformation == Transformation.GROUPING) {
			this.colSize = colSize;
			doGrouping(1, children, finalList);
		}else{
			int totalUniqueValues = getTotalUniqueValuesInHeader(this.groupByColumns.get(1), unAggregatedFinalAry);
			colSize = (this.aggregationOperation.equals("COUNT")) ? totalUniqueValues : totalUniqueValues * this.numericColumns.size();
			this.colSize = colSize+1;
			doTransformation(1, children, finalList);
		}
	}
	
	private Object[][] construct2DArrayFromList(List<Object[]> finalList, Object[][] finalAry, Object[][] unAggregatedFinalAry) {
		int rowSize = finalList.size();
		if(this.transformation == Transformation.GROUPING) {
			rowSize = (this.firstRowAsLabel) ? finalList.size() +1 : rowSize;
		}
		finalAry = new Object[rowSize][this.colSize];
		int startRowIndex = 0, startColIndex=0;
		if(this.firstRowAsLabel) {
			if(this.transformation != Transformation.TRANSFORM) {
				startRowIndex = 1;
				Object[] headers = new Object[colSize];
				constructHeadersForTheArray(unAggregatedFinalAry, aggregationOperation, headers);
				finalAry[0] = headers;	//assign first row as headers.
			}else{
				finalAry[0][0] = unAggregatedFinalAry[0][this.groupByColumns.get(0)];
			}
		}
		finalAry = convertListInto2DArray(finalAry, finalList, startRowIndex, startColIndex);
		return finalAry;
	}
	
	
	private Object[][] convertListInto2DArray(Object[][] finalAry, List<Object[]> finalList, int startRowIndex, int startColIndex) {
		for(int i=0;i<finalList.size();i++, startRowIndex++) {
			Object[] column = finalList.get(i);
			for(int j=0;j<column.length; j++, startColIndex++){
				if(this.transformation == Transformation.TRANSFORM && i==0 && j==0) {
					continue;
				}
				finalAry[startRowIndex][j] = column[j]; 
			}
		}
		return finalAry;
	}
	
	private void doGrouping(int treeDepth, List<DataAggregatorResult> children, List<Object[]> finalList) {
		int index = 0;
		int rowIndex = 0;
		
		index = 0;
		rowIndex = index;
		int cIndex = 0;
		while(index  < children.size()){
			rowIndex = traverseIntoChildren1(treeDepth, children.get(index),0, finalList, rowIndex, cIndex);
			index++;
		}
	}
	
	
	private void doTransformation(int treeDepth, List<DataAggregatorResult> children, List<Object[]> finalList) {
		int index = 0;
		int rowIndex = 1;
		int cIndex = 0;
		List<String> columnFieldValues = new ArrayList<String>();
		finalList.add(new Object[this.colSize]);
		while(index  < children.size()){
			rowIndex = traverseIntoChildrenForTransformation(treeDepth, children.get(index),0, finalList, rowIndex, cIndex, columnFieldValues);
			index++;
		}
	}
	
	
	private void AddDataToFinalList(int rowIndex, CellProperties cp, List<Object[]> finalList, int colIndex) {
		if(finalList.size() > rowIndex) {
			Object[] column = finalList.get(rowIndex);
			column[colIndex] = cp;
		}else{
			Object[] column = new Object[this.colSize];
			column[colIndex] = cp;
			finalList.add(column);
		}
	}
	
	
	private int traverseIntoChildren1(int treeDepth, DataAggregatorResult currentLeaf, int level, List<Object[]> finalList, int rowIndex, int colIndex){
			List<DataAggregatorResult> childOfChildNodes = tabularData.getGroupedNodes(currentLeaf, groupByCriterias.get(treeDepth));
			if(childOfChildNodes.size() > 0) {
				constructValueObject(currentLeaf, finalList, level, rowIndex, colIndex);
				colIndex++;
				int sIndex = 0;
				level++;
				while(sIndex < childOfChildNodes.size()){
					rowIndex = traverseIntoChildren1(treeDepth + 1, childOfChildNodes.get(sIndex), level, finalList, rowIndex, colIndex);
					sIndex++;
				}
			}else{
				constructValueObject(currentLeaf, finalList, level, rowIndex, colIndex);
				colIndex++;
				constructFinalAry(currentLeaf, finalList, rowIndex, colIndex);
				colIndex = 0;
				rowIndex++;
			}
			return rowIndex;
		}
	
	
	private int traverseIntoChildrenForTransformation(int treeDepth, DataAggregatorResult currentLeaf, int level, List<Object[]> finalList, int rowIndex, int colIndex ,List<String> columnFieldValues){
		List<DataAggregatorResult> childOfChildNodes = tabularData.getGroupedNodes(currentLeaf, groupByCriterias.get(treeDepth));
		if(childOfChildNodes.size() > 0) {
			constructValueObject(currentLeaf, finalList, level, rowIndex, 0);
			colIndex++;
			int sIndex = 0;
			level++;
			while(sIndex < childOfChildNodes.size()){
				colIndex = setValuesInColumnField(treeDepth + 1, childOfChildNodes.get(sIndex), level, finalList, rowIndex, colIndex, columnFieldValues);
				sIndex++;
			}
			rowIndex++;
		}
		return rowIndex;
	}
	
	private int setValuesInColumnField(int treeDepth, DataAggregatorResult currentLeaf, int level, List<Object[]> finalList, int rowIndex, int colIndex, List<String> columnFieldValues){
		String compStr = currentLeaf.getValue() != null ? currentLeaf.getValue().toString() : "";    //No I18N
		int initSize = (columnFieldValues.size() == 0) ? 1 : columnFieldValues.size()+1;
		int computedSize = columnFieldValues.size()*this.numericColumns.size() + 1;
		int headerIndex = !this.aggregationOperation.equals("COUNT") ? computedSize : initSize;
		colIndex = headerIndex;
		if(!columnFieldValues.contains(compStr)) {
			if(!this.aggregationOperation.equals("COUNT")) {
				for(int i=0;i<this.numericColumns.size();i++) {
					constructValueObject(currentLeaf, finalList, level, 0, headerIndex);
					headerIndex++;
				}
			}
			columnFieldValues.add(compStr);
		}else{
			headerIndex = colIndex = (this.aggregationOperation.equals("COUNT")) ? columnFieldValues.indexOf(compStr)+1 : columnFieldValues.indexOf(compStr) * this.numericColumns.size()+1;
		}
			List<DataAggregatorResult> children = tabularData.getGroupedNodes(currentLeaf, groupByCriterias.get(treeDepth));
			if(children != null && children.size() > 0){
				return headerIndex;
			}else{
				constructFinalAry(currentLeaf, finalList, rowIndex, colIndex);
			}
			colIndex = (this.aggregationOperation.equals("COUNT")) ? colIndex + 1 : colIndex + this.numericColumns.size();
		return colIndex;
	}

	private void constructValueObject(DataAggregatorResult currentLeaf, List<Object[]> finalList, int level, int rowIndex, int colIndex){
		Value groupByValue = currentLeaf.getValue();
		String dateOperation = this.groupByOperationsMap.get(this.groupByColumns.get(level)).equals("NONE") ? "NONE" : this.groupByOperationsMap.get(this.groupByColumns.get(level)).split("-")[1];//No I18N
		String labelValue = transformGroupByClause(dateOperation, groupByValue);
		CellProperties groupBy = CellProperties.generateCellProperties(labelValue);
		AddDataToFinalList(rowIndex, groupBy, finalList, colIndex);
	}
	
	private void constructFinalAry(DataAggregatorResult currentLeaf, List<Object[]> finalList, int rowIndex, int colIndex) {
		if(this.aggregationOperation.equals("COUNT")) {
			CellProperties cp = getCellPropertiesForCount(currentLeaf);
			AddDataToFinalList(rowIndex, cp, finalList, colIndex);
		}else{
			AtomicInteger cIndex = new AtomicInteger(colIndex);
			this.numericColumns.forEach(numericColumn -> {

				Summary summary = new Summary(summaryRanges.get(numericColumn), SummarizeOperation.valueOf(aggregationOperation));
                Value summaryValue = currentLeaf.getSummarizedValue(summary);

				
				CellProperties cp = CellProperties.generateCellProperties(summaryValue);
				AddDataToFinalList(rowIndex, cp, finalList, cIndex.get());
				//if(this.transformation == Transformation.TRANSFORM){
					cIndex.getAndIncrement();
				//}
			});
		}
	}

	
	private String transformGroupByClause(String dateOperation, Value groupByValue){
		if(dateOperation.equals("NONE")) {
			return groupByValue.getValue() != null ? groupByValue.getValue().toString() : "";
		}
		if(groupByValue ==  null || groupByValue.getValue() == null) {
			return "";
		}
		String groupByLabel = "";	//No I18N
		switch(dateOperation) {
			case "MONTH":
				groupByLabel = numberToMonthMap.get(Double.valueOf(groupByValue.getValue().toString()).intValue());
				break;
			case "QUARTER":
				groupByLabel = numberToQuarterMap.get(Double.valueOf(groupByValue.getValue().toString()).intValue());
				break;
//			case "QUARTER_BY_YEAR":
//				String[] groups = groupByValue.getValue().toString().split("-");
//				groupByLabel = groups[0]+numberToQuarterMap.get(Double.valueOf(groups[1]).intValue());
//				break;
//			case "MONTH_BY_YEAR":
//				groups = groupByValue.getValue().toString().split("-");
//				groupByLabel = groups[0]+numberToMonthMap.get(Double.valueOf(groups[1]).intValue());
//				break;
//			case "DAY_OF_WEEK":
//				groupByLabel = numberToWeekDayMap.get(Double.valueOf(groupByValue.getValue().toString()).intValue());
//				break;
			default:
				groupByLabel = groupByValue.getValue() != null ? groupByValue.getValue().toString() : "";	//No I18N
		}
		return groupByLabel;
	}
	private CellProperties getCellPropertiesForCount(DataAggregatorResult dataAggregatorResult) {

        Summary summary = new Summary(summaryRanges.get(1), SummarizeOperation.valueOf(aggregationOperation));
        Value summaryValue = dataAggregatorResult.getSummarizedValue(summary);
		int noOfRows = 0;
		CellProperties cp = null;
		if(summaryValue == null){
			try{
				noOfRows = dataAggregatorResult.getRowsBitSet().cardinality();
			}catch(Exception ex){
				noOfRows = 0;
			}
			cp = CellProperties.generateCellProperties(Double.valueOf(noOfRows), Type.FLOAT);	
		}
		else{
			cp = CellProperties.generateCellProperties(summaryValue);
		}
		return cp;
	}
	
	private void constructHeadersForTheArray(Object[][] unAggregatedFinalAry, String aggregationOperation, Object[] headers){
		String concatenatedHeaderName = "";
		if(aggregationOperation.equals("COUNT")) {
			int i=0;
			for(;i<this.groupByColumns.size(); i++) {
				CellProperties cp = (CellProperties)unAggregatedFinalAry[0][this.groupByColumns.get(i)];
				headers[i] = cp;
				concatenatedHeaderName+= cp.get(1);
			}
			I18nMessage i18nMsg = new I18nMessage(sheet.getWorkbook().getSpreadsheetSettings().getLocale());
			String title = i18nMsg.getMsg("Chart.Aggregation.Legends.countof") + "" + concatenatedHeaderName;
			CellProperties cp = CellProperties.generateCellProperties(title);
			headers[i] = cp;
		}else{
			int startIndex = 0;
			for(int i=0;i<this.groupByColumns.size(); i++, startIndex++) {
				headers[startIndex] = unAggregatedFinalAry[0][this.groupByColumns.get(i)];
			}
			for(int i=0;i<this.numericColumns.size(); i++, startIndex++){
				headers[startIndex] = unAggregatedFinalAry[0][this.numericColumns.get(i)];
			}
		}
	}
	private int getTotalUniqueValuesInHeader(int index, Object[][] finalAry){
		Set listOfUniqueValues = new HashSet();
		if(this.seriesIn.equals("COLS")) {
			for(int i=1;i<finalAry.length; i++){
				CellProperties cp = (CellProperties)finalAry[i][index];
				Object value = cp!= null && cp.get(1) != null ? cp.get(1) : "";
				listOfUniqueValues.add(value);
			}
		}else{
			Object[] currentRow = finalAry[index];
			for(int i=1;i<currentRow.length; i++){
				CellProperties cp = (CellProperties)currentRow[i];
				Object value = cp!= null && cp.get(1) != null ? cp.get(1) : "";
				listOfUniqueValues.add(value);
			}
		}
		return listOfUniqueValues.size();
	}
}
