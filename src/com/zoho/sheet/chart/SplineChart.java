/*  $Id$ */

package com.zoho.sheet.chart;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;



public class <PERSON><PERSON><PERSON><PERSON><PERSON> extends Chart 
{
	String lineStyle = "long dash";										//No I18N
	
	public SplineChart(Workbook workbook, String chartId, String majorType, String sheetName, String dataRange, String seriesIn, boolean firstRowAsLabel, boolean firstColAsLabel, JSONObjectWrapper chartMetaInfo, boolean isPivotChart, String pivotId) throws Exception
	{
		super(workbook, chartId, majorType, sheetName, "spline", dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);													//No I18N
	}
	
	public void setLineStyle()
	{
		this.plotOptions.getJSONObject("spline").put("dashStyle", this.lineStyle);
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return this.getMajorType();								//No I18N
	}
        
    @Override
        public  void constructChartOptions(Sheet sheet, JSONObjectWrapper chartOptions)
		{
            String title 		= 	chartOptions.has("title")    	? chartOptions.getString("title")    : "";
                        
			String subTitle 	= 	chartOptions.has("subTitle") 	? chartOptions.getString("subTitle") : "";
			int    legendPos	=  	chartOptions.has("lpos")   	? chartOptions.getInt("lpos")   : 1;
                        boolean isFloating      =       chartOptions.has("isFloat")     ? chartOptions.getBoolean("isFloat") : false; 
                        boolean isExpand    =       chartOptions.has("ae")         ? chartOptions.getBoolean("ae")     : false;
			this.setChartObject(chartOptions);
			this.setCredits(false, null, null);
			this.setTitle(title);
			this.setSubTitle(subTitle);
			this.setToolTip(chartOptions);
			this.setXAxis(chartOptions);
			this.setYAxis(chartOptions);
			this.setPlotOptions(chartOptions);
                        this.setLegend(legendPos, isFloating);
                        this.setAutoExpand(isExpand);
                        setSpecificProperties(sheet, chartOptions);
			//this.setSeries(sheet); 
		}

//    @Override
//    public void setLegend(int position, boolean isFloating) {
//                          this.legend		=		new JSONObjectWrapper();
//                         this.legend.put("floating", isFloating);
//                         
//                         String maxHeight   = 		"55";
//                         int	yVal		=     0;
//                         
//                         if(position == 0)
//                         {
//                                 this.legend.put("enabled", false);
//                                 return;
//                         }
//
//                         //Bottom - default	
//                         String layout		=		"horizontal";										//No I18N
//                         String	halign		=		"center";											//No I18N
//                         String valign		=		"bottom";											//No I18N
//
//                         if(position == 2)			//Top
//                         {
//                                layout 		= 		"horizontal";										//No I18N
//                                halign 		= 		"center";											//No I18N
//                                valign 		= 		"top";												//No I18N
//                                
//                                boolean hasTitle		=	(!"".equals(this.title.getString("text")));
//                                boolean hasSubTitle		=	(!"".equals(this.subTitle.getString("text")));
//                                yVal					=	(hasTitle && hasSubTitle) ? 40 : (hasTitle || hasSubTitle) ? 25 : 0;
//                         }
//                         else if(position == 3)                 //Right
//                        {
//                               layout 		= 		"vertical";											//No I18N	
//                               halign 		= 		"right";											//No I18N
//                               valign 		= 		"middle";											//No I18N
//                               maxHeight       = null;  
//                        }
//                        else if(position ==	4)              //Left
//                        {
//                               layout 		= 		"vertical";											//No I18N
//                               halign 		= 		"left";												//No I18N
//                               valign 		= 		"middle";											//No I18N		
//                               maxHeight       = null;
//                        }
//                        else if(position ==  5) // top-right
//                        {
//                               layout 		= 		"vertical";											//No I18N	
//                               halign 		= 		"right";											//No I18N
//                               valign 		= 		"top";											//No I18N
//                               maxHeight       = null; 
//                        }
//
//                         this.legend.put("layout", 			layout);
//                         this.legend.put("align", 			halign);
//                         this.legend.put("verticalAlign", 	valign);
//                         this.legend.put("enabled", true);
//                         this.legend.put("y", 	yVal);
//                         this.legend.put("maxHeight", maxHeight);
//                         this.legend.put("itemStyle", getLegendItemStyleJson());
//                         setModified(true);  
//	
//    }
    @Override
    public void setSpecificProperties(Sheet sheet, JSONObjectWrapper chartOptions)
    {
    	this.chart.put("polar", false);
        if(chartOptions.has("inverted")) {
                this.chart.put("inverted", chartOptions.has("inverted") ? chartOptions.getBoolean("inverted") : false);
        }
    	 String     threshold       =   chartOptions.has("th") ? chartOptions.getString("th") : " ";
        if(chartOptions.has("isMarker"))
        {
             this.setMarker(chartOptions.getBoolean("isMarker"));
        }
        if(chartOptions.has("tl") && chartOptions.has("tlType") && chartOptions.has("order"))
               {
                this.setTrendLineDetails(chartOptions.getBoolean("tl"),chartOptions.getString("tlType"),chartOptions.getInt("order")); //No I18N
               }
        if( threshold != null && !" ".equals(threshold)){
                this.plotOptions.getJSONObject("series").put("threshold",     Float.parseFloat(threshold));
                this.plotOptions.getJSONObject("series").put("negativeColor", "#"+ChartConstants.CHART_THRESHOLD_COLOR);
            }
    }

//    @Override
//    public boolean isRange(Sheet sheet, String title, String forRange) {
//        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
//    }
	
}
