/*  $Id$ */

package com.zoho.sheet.chart;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class <PERSON><PERSON><PERSON> extends Chart 
{
	boolean showInLegend;
	
	public PieChart(Workbook workbook, String chartId, String majorType, String sheetName, String dataRange, String seriesIn, boolean firstRowAsLabel, boolean firstColAsLabel, JSONObjectWrapper chartMetaInfo, boolean isPivotChart, String pivotId) throws Exception
	{
		super(workbook, chartId, majorType, sheetName, "pie", dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);		//No I18N						
	}
	
        
        @Override
	public String getType() {
		// TODO Auto-generated method stub
		return this.getMajorType();									//No I18N
	}
        
        
        
    @Override
        public  void constructChartOptions(Sheet sheet, JSONObjectWrapper chartOptions)
        {
            String title 		= 	chartOptions.has("title")    	? chartOptions.getString("title")    : "";
                        
			String subTitle 	= 	chartOptions.has("subTitle") 	? chartOptions.getString("subTitle") : "";
			int    legendPos	=  	chartOptions.has("lpos")   	? chartOptions.getInt("lpos")   : 1;
                        boolean isFloating      =       chartOptions.has("isFloat")     ? chartOptions.getBoolean("isFloat") : false; 
                        boolean isExpand    =       chartOptions.has("ae")         ? chartOptions.getBoolean("ae")     : false;
			this.setChartObject(chartOptions);
			this.setCredits(false, null, null);
			this.setTitle(title);
			this.setSubTitle(subTitle);
			this.setToolTip(chartOptions);
                        this.setPlotOptions(chartOptions);
			this.setLegend(legendPos, isFloating);
			this.setXAxis(chartOptions);
			this.setYAxis(chartOptions);
                        this.setAutoExpand(isExpand);
                        setSpecificProperties(sheet, chartOptions);
			
			//this.setSeries(sheet);
        }

//    	@Override
//        public void setLegend(int position, boolean isFloating) {
//                    this.legend		=		new JSONObjectWrapper();
//                    String layOut	=	"horizontal";		//No I18N
//                    String hAlign	=	"center";			//No I18N
//                    String vAlign	=	"bottom";			//No I18N
//
//                    if(position == 0)
//                    {	
//                            this.plotOptions.getJSONObject("pie").put("showInLegend", false);		//No I18N
//                    }
//                    else
//                    {
//                            this.plotOptions.getJSONObject("pie").put("showInLegend", true);		//No I18N
//
//                            if(position == 3)
//                            {
//                                    layOut	=	"vertical";		//No I18N
//                                    hAlign	=	"right";		//No I18N
//                                    vAlign	=	"middle";		//No I18N
//                            }
//                            else if(position == 2)
//                            {
//                                    layOut	=	"horizontal";	//No I18N
//                                    hAlign	=	"center";		//No I18N
//                                    vAlign	=	"top";			//No I18N
//                            }
//                            else if(position == 4)
//                            {
//                                    layOut	=	"vertical";		//No I18N
//                                    hAlign	=	"left";			//No I18N
//                                    vAlign	=	"middle";		//No I18N
//                            }
//
//                            this.legend.put("layout", layOut);	//No I18N
//                            this.legend.put("align", hAlign);	//No I18N
//                            this.legend.put("verticalAlign", vAlign);	//No I18N
//                            this.legend.put("itemStyle", getLegendItemStyleJson());
//                            this.legend.put(("symbolWidth"), 10);
//                            this.legend.put(("symbolHeight"), 10);
//                    }
//        }
    @Override
    public void setSpecificProperties(Sheet sheet, JSONObjectWrapper chartOptions)
    {
            this.chart.put("polar", false);
            this.plotOptions.getJSONObject("pie").put("allowPointSelect", true);
            this.chart.put("panKey", "");
            this.chart.put("panning", "");
            JSONArrayWrapper pData=new JSONArrayWrapper();//position value
            
            //this.plotOptions.getJSONObject("pie").getJSONObject("dataLabels").put("distance", 10);

             if(this.getMajorType().contains(ChartConstants.SEMI_DOUGHNUT_CHART) || this.getMajorType().contains(ChartConstants.SEMI_PIE_CHART)){
                this.plotOptions.getJSONObject("pie").put("startAngle" ,-90);
                this.plotOptions.getJSONObject("pie").put("endAngle" ,90);
                pData.put("50%");
                pData.put("75%");
                this.plotOptions.getJSONObject("pie").put("center" ,pData);
                this.plotOptions.getJSONObject("pie").put("size" ,"100%");
                this.plotOptions.getJSONObject("pie").put("borderWidth", 1);
                this.plotOptions.getJSONObject("pie").put("borderColor", "#f0f0f0");
            }else if(this.getMajorType().contains(ChartConstants.DOUGHNUT_CHART) && !this.getMajorType().equalsIgnoreCase("DOUGHNUT3D")){
                this.plotOptions.getJSONObject("pie").put("borderWidth", 1);
                this.plotOptions.getJSONObject("pie").put("borderColor", "#f0f0f0");
            }
            if(this.getMajorType().contains("3D"))
            {
            	JSONObjectWrapper	json3D	=	new JSONObjectWrapper();
            	json3D.put("enabled", true);
            	json3D.put("alpha", 45);
            	json3D.put("beta", 0);
            	this.chart.put("options3d", json3D);
            	this.plotOptions.getJSONObject("pie").put("depth",35);
            }
            
            if(this.getMajorType().contains("SEMI")) {
                this.setSliceStartAngle(270);
            }else {
                this.setSliceStartAngle(0);
            }
    }

//    @Override
//    public boolean isRange(Sheet sheet, String title, String forRange) {
//        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
//    }
        
}
