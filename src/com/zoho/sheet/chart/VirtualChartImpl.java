/*  $Id$ */

package com.zoho.sheet.chart;

import com.adventnet.zoho.websheet.model.Sheet;
import static com.zoho.sheet.chart.Chart.LOGGER;
import java.util.logging.Level;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 *
 * <AUTHOR>
 */
    //Virtual_Sheet_Chart_Integration 
    public class VirtualChartImpl extends Chart {

        public VirtualChartImpl(Sheet sheet, String chartId, String dataRangeStr, String majorType) {
            super(sheet, chartId, dataRangeStr, majorType);
        }
        @Override
        public void constructChartOptions(Sheet sheet, JSONObjectWrapper chartOptions) {
            LOGGER.log(Level.INFO,"[Show_Sheet_Chart_Integration] >> Construct Chart Options is not supported yet.");
        }
        @Override
        public void setSpecificProperties(Sheet sheet, JSONObjectWrapper chartOptions) {
            LOGGER.log(Level.INFO,"[Show_Sheet_Chart_Integration] >> Set Specific chart properties is not supported yet.");
        }
    }
