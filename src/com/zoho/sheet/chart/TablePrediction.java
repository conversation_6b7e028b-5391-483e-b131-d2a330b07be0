/*  $Id$ */
 /*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.zoho.sheet.chart;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.action.DataTypePredictor;
import com.zoho.sheet.util.I18nMessage;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.StreamSupport;

/**
 *
 * <AUTHOR>
 */
public class TablePrediction {

    public static final Logger LOGGER = Logger.getLogger(TablePrediction.class.getName());
    private int startRow, startCol, endRow, endCol, maxLimit = 0, currentLimit, labelDepth, headerDepth, sStartRow, sStartCol, sEndRow, sEndCol, defaultLabelDepth;
    private Sheet sheet;
    private Object cellAry[][]; //table cell value 
    private Range tRange = null; //TableRange
    private Range headerRange = null;
    private Range labelRange = null;
    private Range valueRange = null;
    private JSONArrayWrapper headerObject = null; // header list construction
    private JSONArrayWrapper labelObject = null;
    private JSONObjectWrapper tableResponse = null;
    private JSONObjectWrapper tDataType = null;
    private int maxDepth = 3; // detecting datatype
    private boolean topAdjacent, leftAdjacent, rightAdjacent, bottomAdjacent;
    private PredictionType type = PredictionType.NORMAL;
    private boolean isTrimNeeded = true, isDCDT = true, isCellSelection = true, isSkipLabel, isUserConfig = false, isNeededRData, isNeededHeaderData, isNeededLabelData, isSkipUnorderHiddenCells, isFirstRowLabel = true; // DCDT- detecting column data type

    // temporary variables added for filtering range
    private int topRowIndex;    
    private int leftColIndex;
    private int rightColIndex;
    private int bottomRowIndex;

    private Table intersectedTable;

    private int timeOut = -1;   // DISABLED BY DEFAULT

    private long startTime = System.currentTimeMillis();

    enum CellLeveLSelection {
        LEFT, RIGHT, TOP, BOTTOM, TOP_RIGHT, TOP_LEFT, BOTTOM_RIGHT, BOTTOM_LEFT
    }

    public enum PredictionType
    {
        NORMAL("normal"), FILTER("filter"), NONE("none");   // No I18N

        String type;
        PredictionType(String type)
        {
            this.type = type;
        }

        @Override
        public String toString()
        {
            return this.type;
        }
    }

    public TablePrediction()
    {
    }

    public TablePrediction(Range range) {
        if (range != null) {
            this.sStartRow = this.startRow = range.getStartRowIndex();
            this.sStartCol = this.startCol = range.getStartColIndex();
            this.sEndRow = this.endRow = range.getEndRowIndex();
            this.sEndCol = this.endCol = range.getEndColIndex();
            this.setSheet(range.getSheet());
            this.tRange = range;
            this.setIntersectingTable();
        }
    }

    public TablePrediction(Sheet sheet, int sRow, int sCol, int eRow, int eCol) {
        this.sStartCol = this.startCol = sCol;
        this.sStartRow = this.startRow = sRow;
        this.sEndCol = this.endCol = eCol;
        this.sEndRow = this.endRow = eRow;
        this.setSheet(sheet);
        this.setIntersectingTable();
    }

    public String getTable() {
        if(!predictTable())
        {
            return null;
        }
        this.tRange = new Range(this.sheet, this.startRow, this.startCol, this.endRow, this.endCol);
        return this.tRange.getRangeStringForClient();
    }
    public Range getTableRange() {
        if(!predictTable())
        {
            return null;
        }
        this.tRange = new Range(this.sheet, this.startRow, this.startCol, this.endRow, this.endCol);
        return this.tRange;
    }

    public JSONObjectWrapper getDeclarativeTable() {
        if(!predictTable())
        {
            return null;
        }
        this.tRange = new Range(this.sheet, this.startRow, this.startCol, this.endRow, this.endCol);
        contructingDeclarativeTable();
        constructingTableResponse();
        return tableResponse;
    }
//    private void setChartRelated(boolean isChartRelated)
//    {
//        this.isChartRelated = isChartRelated;
//    }
//    
//    private boolean isChartRelated()
//    {
//       return this.isChartRelated;
//    }

    private static String getColumnHeader(int col, Sheet sheet) {
        I18nMessage i18NMessage = new I18nMessage(sheet.getWorkbook().getSpreadsheetSettings().getLocale());
        String columnRef = CellUtil.getColumnReference(col);
        return i18NMessage.getMsg("column") + " " + columnRef;
    }
    public static Map getDataTypeColumnsFromDTPredictor(Range currRange, String seriesIn){
    	Map columns = new HashMap();
    	List<String> categorical = new ArrayList<String>();
    	List<String> numerical = new ArrayList<String>();
    	List<String> date = new ArrayList<String>();
    	List<String> semiColumns = new ArrayList<String>();
    	JSONObjectWrapper dtTypes = null;
    	if(seriesIn.equals("COLS")){
    		int endIndex = (currRange.getStartRowIndex()+10 < currRange.getEndRowIndex()) ? currRange.getStartRowIndex()+10 : currRange.getEndRowIndex();
    		dtTypes = DataTypePredictor.findDataTypes(currRange.getSheet().getWorkbook(), currRange.getStartRowIndex(), currRange.getStartColIndex(), endIndex, currRange.getEndColIndex(), currRange.getSheet().getAssociatedName());
    	}else{
    		int endIndex = (currRange.getStartColIndex()+10 < currRange.getEndColIndex()) ? currRange.getStartColIndex()+10 : currRange.getEndColIndex();
    		dtTypes = DataTypePredictor.findDataTypes(currRange.getSheet().getWorkbook(), currRange.getStartRowIndex(), currRange.getStartColIndex(), currRange.getEndRowIndex(), endIndex, currRange.getSheet().getAssociatedName(), false);
    	}
    	
    	Iterator itr = dtTypes.keys();
    	while(itr.hasNext()){
    		String key = (String)itr.next();
    		JSONObjectWrapper dataType = dtTypes.getJSONObject(key);
    		String type = dataType.getString("type");
    		String colHeader = dataType.has("label")? dataType.getString("label") :null;
    		if(colHeader != null){
    			if(type.equals("NUMBER")){
    				semiColumns.add(colHeader);
    				numerical.add(colHeader);
    			}else if(type.equals("DECIMAL") || type.equals("CURRENCY") || type.equals("PERCENTAGE")){
        			numerical.add(colHeader);
        		}else if(type.equals("DATE") || type.equals("DATETIME")){
        			date.add(colHeader);
        		}else if(!type.equals("EMPTY")){
        			categorical.add(colHeader);
        		}
    		}
    		
    	}
    	columns.put("categorical", categorical);
    	columns.put("numerical", numerical);
    	columns.put("date", date);
    	columns.put("semi", semiColumns);
    	return columns;
    }
    public JSONObjectWrapper getTableRangeDetails() {
        if(!predictTable())
        {
            return null;
        }
        this.tRange = new Range(this.sheet, this.startRow, this.startCol, this.endRow, this.endCol);
        return contructDeclarativeTableDetails();
    }
    
    private Range getFilterRange(Sheet sheet){
        return new Range(sheet, this.topRowIndex, this.leftColIndex, this.bottomRowIndex, this.rightColIndex);
    }
    
    public JSONObjectWrapper contructDeclarativeTableDetails() {
        JSONObjectWrapper tableDetails = new JSONObjectWrapper();
        tableDetails.put(TableConstraints.TABLE_RANGE, this.tRange.getRangeStringForClient());
        tableDetails.put(TableConstraints.IS_FIRST_ROW_AS_LABEL, isFirstRowLabel(this.startCol, this.endCol));
        return tableDetails;
    }
    public boolean isFirstRowLabel(int startCol, int endCol){
        int stringTypeCount = 0, otherTypeCount = 0;
        for (int i = startCol; i <= endCol; i++) {
           ReadOnlyCell cell = this.sheet.getReadOnlyCellFromShell(this.startRow, i);
           if (cell != null && cell.getCell() != null && cell.getCell().getValue().getType() == Type.STRING) {
               stringTypeCount++;
           } else {
               otherTypeCount++;
           }
        }
        return stringTypeCount >= otherTypeCount;
    }
    public static File getCSVFile(Object[][] table, String delimiter, String seriesIn, Sheet sheet, Range currRange) {
        File file = null;
        try {
            if (table == null) {
                return file;
            }
            JSONObjectWrapper dtTypes = DataTypePredictor.findDataTypes(currRange.getSheet().getWorkbook(), currRange.getStartRowIndex(), currRange.getStartColIndex(), currRange.getStartRowIndex()+10, currRange.getEndColIndex(), currRange.getSheet().getAssociatedName());
            StringBuilder csvbuff = new StringBuilder();
            int endCol = (seriesIn.equals("COLS")) ? table[0].length - 1 : table.length - 1;
            boolean firstRow = false;
            if (seriesIn.equals("COLS")) {
                for (int row = 0; row < table.length; row++) {
                    firstRow = (row == 0) ? true : false;
                    for (int col = 0; col < table[0].length; col++) {
                    	int key = currRange.getStartColIndex()+col;
                    	String dataType = dtTypes.getJSONObject(String.valueOf(key)).getString("type");
                        CellProperties cp = (CellProperties) table[row][col];
                        String content = "";
                        String displayContent = "";
                      if (!firstRow && ((cp != null && cp.get(0) != null && cp.isTypeNumber()) || (dataType.equals("NUMBER") ||  dataType.equals("DECIMAL") || dataType.equals("CURRENCY") || dataType.equals("PERCENTAGE")))) {
                      //  if((cp != null && cp.get(0) != null && cp.isTypeNumber())){
                            try {
                                Double.parseDouble(cp.get(0).toString());
                                content = cp.get(0).toString();
                            } catch (NumberFormatException nfe) {
                                content = "0";
                            } catch (NullPointerException nfe) {
                                content = "0";
                            }
                        } else if (cp != null && cp.get(0) != null && cp.isTypeError()) {
                            content = "";
                        } else if (cp != null && cp.get(0) != null) {
                        	displayContent = cp.get(1) != null ? cp.get(1).toString() : "";
                            content = cp.get(0).toString();
                        }
                        if (firstRow) {
                            content = displayContent.isEmpty() ? getColumnHeader(key, sheet) : displayContent;
                        }
                        content = '"' + content.replaceAll("\"", "").replaceAll(",", "").replaceAll("\\n", "") + '"';   // No I18N
                        csvbuff.append(content);
                        if (col != endCol) {
                            csvbuff.append(delimiter);
                        }
                    }
                    csvbuff.append("\n");   // No I18N
                }
            } else {
                for (int col = 0; col < table[0].length; col++) {
                    firstRow = (col == 0) ? true : false;
                    for (int row = 0; row < table.length; row++) {
                        CellProperties cp = (CellProperties) table[row][col];
                        String content = "";
                        String displayContent = "";
                        if ((cp != null && cp.get(0) != null && cp.isTypeNumber())) {
                            try {
                                Double.parseDouble(cp.get(0).toString());
                                content = cp.get(0).toString();
                            } catch (NumberFormatException nfe) {
                                content = "";
                            }
                        } else if (cp != null && cp.get(0) != null && cp.isTypeError()) {
                            content = "";
                        } else if (cp != null && cp.get(0) != null) {
                        	displayContent = cp.get(1) != null ? cp.get(1).toString() : "";
                            content = cp.get(0).toString();
                        }
                        if (firstRow) {
                            content = displayContent.isEmpty() ? "Row " + (row + 1) : displayContent;	//No I18N
                        }
                        content = '"' + content.replaceAll("\"", "").replaceAll("\\n", "") + '"';   // No I18N
                        csvbuff.append(content);
                        if (row != endCol) {
                            csvbuff.append(delimiter);
                        }
                    }
                    csvbuff.append("\n");   // No I18N
                }
            }
            file = File.createTempFile("range", ".csv");		// NO I18N
            BufferedWriter bw = new BufferedWriter(new FileWriter(file));
            bw.write(csvbuff.toString());
            bw.close();

        } catch (IOException ex) {
            LOGGER.log(Level.WARNING, "Error while generating csv buffer {0}", ex);
        }
        return file;
    }

    private void setMaxLimit(int limit) {
        this.maxLimit = limit;
    }

    private int getMaxLimit() {
        return this.maxLimit;
    }

    private void setTrimNeeded(boolean flag) {
        this.isTrimNeeded = flag;
    }

    private boolean isTrimNeeded() {
        return this.isTrimNeeded && this.isCellSelection && isTableExpand();
    }

    private boolean isTableExpand() {
        return !(this.sStartRow == this.startRow && this.sStartCol == this.startCol && this.sEndRow == this.endRow && this.sEndCol == this.endCol);
    }

    private void setDCDTNeeded(boolean flag) {
        this.isDCDT = flag;
    }

    private boolean isDCDTNeeded() {
        return this.isDCDT;
    }

    private void setMaxDepth(int depth) {
        this.maxDepth = depth;
    }

    private void setType(String tType) {
        if (tType.equalsIgnoreCase("filter")) {
            this.type = PredictionType.FILTER;
        } else if (tType.equalsIgnoreCase("none")) {
            this.type = PredictionType.NONE;
        } else {
            this.type = PredictionType.NORMAL;
        }
    }

    private PredictionType getType() {
        return this.type;
    }

    private void isCellSelection(boolean isc) {
        this.isCellSelection = isc;
    }

    private boolean isCellSelection() {
        return this.isCellSelection;
    }

    private void isSkipLabelRangeDetection(boolean isc) {
        this.isSkipLabel = isc;
    }

    private boolean isSkipLabelRangeDetection() {
        return this.isSkipLabel;
    }

    public void applyPattern(JSONObjectWrapper pattern) {
        if (pattern != null) {
            this.setMaxLimit(pattern.has(TableConstraints.MAX_LIMIT) ? pattern.getInt(TableConstraints.MAX_LIMIT) : 0);
            this.setTrimNeeded(pattern.has(TableConstraints.TRIM) ? pattern.getBoolean(TableConstraints.TRIM) : true);
            this.setDCDTNeeded(pattern.has(TableConstraints.DETECTING_DATA_TYPE) ? pattern.getBoolean(TableConstraints.DETECTING_DATA_TYPE) : true);
            this.isCellSelection(pattern.has(TableConstraints.CELL_SELECTION) ? pattern.getBoolean(TableConstraints.CELL_SELECTION) : false);
            this.isSkipLabelRangeDetection(pattern.has(TableConstraints.SKIP_LABEL) ? pattern.getBoolean(TableConstraints.SKIP_LABEL) : false);
            this.setMaxDepth(pattern.has(TableConstraints.DETECTION_DEPTH) ? pattern.getInt(TableConstraints.DETECTION_DEPTH) : 3);
            this.setType(pattern.has(TableConstraints.TABLE_PREDICTION_TYPE) ? pattern.getString(TableConstraints.TABLE_PREDICTION_TYPE) : "normal"); //NO I18N
            this.setConfigByUser(pattern.has(TableConstraints.USE_USER_CONFIG) ? pattern.getBoolean(TableConstraints.USE_USER_CONFIG) : false);
            this.setDefaultHeaderDepth(pattern.has(TableConstraints.DEFAULT_HEADER_DEPTH) ? pattern.getInt(TableConstraints.DEFAULT_HEADER_DEPTH) : 0);
            this.isNeededRangeData(pattern.has(TableConstraints.RANGE_DATA) ? pattern.getBoolean(TableConstraints.RANGE_DATA) : false);
            this.isNeededHeaderData(pattern.has(TableConstraints.HEADER_DATA) ? pattern.getBoolean(TableConstraints.HEADER_DATA) : false);
            this.isNeededLabelData(pattern.has(TableConstraints.LABEL_DATA) ? pattern.getBoolean(TableConstraints.LABEL_DATA) : false);
            this.isSkipHiddenCells(pattern.has(TableConstraints.SKIP_HIDDEN_CELLS_IN_PATTERN_OVERFLOW) ? pattern.getBoolean(TableConstraints.SKIP_HIDDEN_CELLS_IN_PATTERN_OVERFLOW) : false);
            this.isFirstRowLabel(pattern.has(TableConstraints.IS_FIRST_ROW_AS_LABEL) ? pattern.getBoolean(TableConstraints.IS_FIRST_ROW_AS_LABEL) : true);
            this.setTimeOut(pattern.optInt(TableConstraints.TIME_OUT, -1));
        }
    }

    private void isSkipHiddenCells(boolean hide) {
        this.isSkipUnorderHiddenCells = hide;
    }
    
    private void isFirstRowLabel(boolean frl) {
        this.isFirstRowLabel = frl;
    }
    
    protected boolean isSkipHiddenCells() {
        return this.isSkipUnorderHiddenCells;
    }

    private void setConfigByUser(boolean mode) {
        this.isUserConfig = mode;
    }

    private boolean isConfigByUser() {
        return this.isUserConfig;
    }

    private void setDefaultHeaderDepth(int depth) {
        this.setHeaderDepth(depth);
    }

    private void isNeededRangeData(boolean mode) {
        this.isNeededRData = mode;
    }

    private void isNeededHeaderData(boolean mode) {
        this.isNeededHeaderData = mode;
    }

    private void isNeededLabelData(boolean mode) {
        this.isNeededLabelData = mode;
    }

    private boolean isNeededLabelData() {
        return this.isNeededLabelData;
    }

    private boolean isNeededHeaderData() {
        return this.isNeededHeaderData;
    }

    private boolean isNeededRangeData() {
        return this.isNeededRData;
    }

    private void setSelectionType() {
        this.isCellSelection = (this.startRow == this.endRow && this.startCol == this.endCol);
    }

    private boolean getSelectiontype() {
        return this.isCellSelection;
    }

    private boolean isNeededToRevokePrediction(int startCol, int endCol, int startRow, int endRow) {
        DataRange dataRange = new DataRange(sheet.getAssociatedName(), startRow, startCol, endRow, endCol);
        if(TableUtil.isIntersectsWithAnyTable(sheet, dataRange, true))
        {
            return true;
        }
        if (this.getMaxLimit() == 0) {
            return false;
        }
        return (endCol - startCol + 1) * (endRow - startRow + 1) >= this.getCurrentLimit();
    }

    private int evaluateCurrentLimitOrElse(int startCol, int startRow, int endCol, int endRow) {
        if (this.getMaxLimit() > 0) {
            this.currentLimit = (this.maxLimit - ((endCol - startCol + 1) * (endRow - startRow + 1)));
        }
        return this.currentLimit;
    }

    private int getCurrentLimit() {
        return this.currentLimit;
    }

    private void setCurrentLimit(int limit) {
        this.currentLimit = limit;
    }
//    public String predictTableOrElse(Sheet sheet,JSONObjectWrapper pattern)
//    {
//        applyPattern(pattern);
//        return getTable();
//    }

    private void setSheet(Sheet sheet) {
        this.sheet = sheet;
    }

    private boolean predictTable() {
        this.setSelectionType();
        this.evaluateCurrentLimitOrElse(startCol, startRow, endCol, endRow);
        boolean isTableRange = isTableRange();

        if(!isTableRange) {
            if (this.getType().equals(PredictionType.NORMAL)) {
                this.startCol = findStartColIndex();
                this.endCol = findEndColIndex();
                this.startRow = ExpandTableUpward();
                this.endRow = ExpandTableDownward();
                boolean isElapsed = expandTableWithAdjacentOrElse(this.startCol, this.startRow, this.endCol, this.endRow);
                if(!isElapsed)
                {
                    return false;
                }
            } else if (this.getType().equals(PredictionType.FILTER)) {
                if (this.isCellSelection()) {
                    this.startCol = findStartColIndex();
                    this.endCol = findEndColIndex();
                    this.startRow = ExpandTableUpward();
                    this.endRow = ExpandTableDownward();
                    boolean isElapsed = expandTableWithAdjacentOrElse(this.startCol, this.startRow, this.endCol, this.endRow);
                    if(!isElapsed)
                    {
                        return false;
                    }
                } else {
                    this.endRow = ExpandTableDownward();
                }
            }
            if (this.isTrimNeeded() && !this.getType().equals(PredictionType.NONE)) {
                removeExtraPadding();
            }
        }
        return true;
    }

    private void removeExtraPadding() {
        CellLeveLSelection cellSelc = getSelectionDirection();
        switch (cellSelc) {
            case LEFT:
                trimLeft();
                break;
            case RIGHT:
                trimRight();
                break;
            case TOP:
                trimTop();
                break;
            case BOTTOM:
                trimBottom();
                break;
            case TOP_LEFT:
                trimLeft();
                trimTop();
                break;
            case TOP_RIGHT:
                trimRight();
                trimTop();
                break;
            case BOTTOM_LEFT:
                trimLeft();
                trimBottom();
                break;
            case BOTTOM_RIGHT:
                trimRight();
                trimBottom();
                break;

        }
    }

    private boolean isColEmpty(int startRow, int endRow, int sourceCol) {
        for (int i = startRow; i <= endRow; i++) {
            ReadOnlyCell cell = this.sheet.getReadOnlyCellFromShell(i, sourceCol);
            if (cell != null && cell.getCell() != null && (cell.getCell().getValue().getValue() != null || sheet.isCoveredUnderMerge(i, sourceCol))) {
                return false;
            }
        }
        return true;
    }

    private boolean isRowEmpty(int startCol, int endCol, int sourceRow) {
        for (int i = startCol; i <= endCol; i++) {
            ReadOnlyCell cell = this.sheet.getReadOnlyCellFromShell(sourceRow, i);
            if (cell != null && cell.getCell() != null && (cell.getCell().getValue().getValue() != null || sheet.isCoveredUnderMerge(sourceRow, i))) {
                return false;
            }
        }
        return true;
    }

    private void trimBottom() {
        if (this.isRowEmpty(this.startCol, this.endCol, this.endRow)) {
            this.endRow--;
        }
    }

    private void trimLeft() {
        if (this.isColEmpty(this.startRow, this.endRow, this.startCol)) {
            this.startCol++;
        }
    }

    private void trimRight() {
        if (this.isColEmpty(this.startRow, this.endRow, this.endCol)) {
            this.endCol--;
        }
    }

    private void trimTop() {
        if (this.isRowEmpty(this.startCol, this.endCol, this.startRow)) {
            this.startRow++;
        }
    }

    private CellLeveLSelection getSelectionDirection() {
        if (this.sStartCol >= this.endCol && this.sStartRow > this.startRow && this.endRow > this.sEndRow) {
            return CellLeveLSelection.RIGHT;
        } else if (this.sStartCol >= this.endCol && this.sStartRow <= this.startRow) {
            return CellLeveLSelection.TOP_RIGHT;
        } else if (this.sStartCol > this.startCol && this.sStartRow >= this.endRow && this.endCol > this.sEndCol) {
            return CellLeveLSelection.BOTTOM;
        } else if (this.sStartCol >= this.endCol && this.sEndRow <= this.endRow) {
            return CellLeveLSelection.BOTTOM_RIGHT;
        } else if (this.sStartCol <= this.startCol && this.sEndRow >= this.endRow) {
            return CellLeveLSelection.BOTTOM_LEFT;
        } else if (this.sStartCol <= this.startCol && this.sStartRow <= this.startRow) {
            return CellLeveLSelection.TOP_LEFT;
        } else if (this.sStartCol >= this.startCol && this.sStartRow <= this.startRow && this.endCol > this.sEndCol) {
            return CellLeveLSelection.TOP;
        } else {
            return CellLeveLSelection.LEFT;
        }
    }

    private boolean isTopAdjacentAvailable() {
        return this.topAdjacent;
    }

    private boolean isBottomAdjacentAvailable() {
        return this.bottomAdjacent;
    }

    private boolean isLeftAdjacentAvailable() {
        return this.leftAdjacent;
    }

    private boolean isRightAdjacentAvailable() {
        return this.rightAdjacent;
    }

    private void resetAdjacent() {
        this.leftAdjacent = this.rightAdjacent = this.topAdjacent = this.bottomAdjacent = false;
    }

    private boolean hasNext() {
        return this.isTopAdjacentAvailable() || this.isBottomAdjacentAvailable() || this.isLeftAdjacentAvailable() || this.isRightAdjacentAvailable();
    }

    private boolean isTimeOut(long startTime)
    {
        long endTime = System.currentTimeMillis();
        if(this.getTimeOut() != -1 && ( (endTime - startTime) > this.getTimeOut() ))
        {
            LOGGER.log(Level.SEVERE, "[TablePrediction][Timeout] Table Prediction has taken more time than permitted time limit");
            return true;
        }
        return false;
    }
    private boolean expandTableWithAdjacentOrElse(int sC, int sR, int eC, int eR)
    {
        do
        {
            if(this.isTimeOut(startTime))
            {
                return false;
            }
            this.resetAdjacent();
            if(isTopAdjacentAvailable(sC, sR - 1, eC, eR))
            {
                this.startRow = sR - 1;
                sR = this.startRow = ExpandTableUpward();
            }
            if(isBottomAdjacentAvailable(sC, sR, eC, eR + 1))
            {
                this.endRow = eR + 1;
                eR = this.endRow = ExpandTableDownward();
            }
            if(isLeftAdjacentAvailable(sC - 1, sR, eC, eR))
            {
                sC--;

            }
            if(isRightAdjacentAvailable(sC, sR, eC + 1, eR))
            {
                eC++;
            }
            this.startCol = sC;
            this.endCol = eC;
            this.startRow = sR;
            this.endRow = eR;
        }
        while(this.hasNext());
        return true;
    }

    private boolean isLeftAdjacentAvailable(int sC, int sR, int eC, int eR) {
        if (!this.isNeededToRevokePrediction(sC, eC, sR, eR) && sC >= 0) {
            int startI, endI;
            startI = (sR > 0) ? --sR : sR;
            endI = (eR < Utility.MAXNUMOFROWS - 1) ? ++eR : eR;
            for (int i = startI; i <= endI; i++) {
                ReadOnlyCell cell = this.sheet.getReadOnlyCellFromShell(i, sC);
                if (cell != null && cell.getCell() != null && (cell.getCell().getValue().getValue() != null || sheet.isCoveredUnderMerge(i, sC))) {
                    this.leftAdjacent = !this.leftAdjacent;
                    break;
                }
            }
        }
        return this.leftAdjacent;
    }

    private boolean isRightAdjacentAvailable(int sC, int sR, int eC, int eR) {
        if (!this.isNeededToRevokePrediction(sC, eC, sR, eR) && eC <= Utility.MAXNUMOFCOLS - 1) {
            int startI, endI;
            startI = (sR > 0) ? --sR : sR;
            endI = (eR < Utility.MAXNUMOFROWS - 1) ? ++eR : eR;
            for (int i = startI; i <= endI; i++) {
                ReadOnlyCell cell = this.sheet.getReadOnlyCellFromShell(i, eC);
                if (cell != null && cell.getCell() != null && (cell.getCell().getValue().getValue() != null || sheet.isCoveredUnderMerge(i, eC))) {
                    this.rightAdjacent = !this.rightAdjacent;
                    break;
                }
            }
        }
        return this.rightAdjacent;
    }

    private boolean isTopAdjacentAvailable(int sC, int sR, int eC, int eR) {
        if (!this.isNeededToRevokePrediction(sC, eC, sR, eR) && sR >= 0) {
            for (int i = sC; i <= eC; i++) {
                ReadOnlyCell cell = this.sheet.getReadOnlyCellFromShell(sR, i);
                if (cell != null && cell.getCell() != null && (cell.getCell().getValue().getValue() != null || sheet.isCoveredUnderMerge(sR, i))) {
                    this.topAdjacent = !this.topAdjacent;
                    break;
                }
            }
        }
        return this.topAdjacent;
    }

    private boolean isBottomAdjacentAvailable(int sC, int sR, int eC, int eR) {
        if (!this.isNeededToRevokePrediction(sC, eC, sR, eR) && eR <= Utility.MAXNUMOFROWS - 1) {
            for (int i = sC; i <= eC; i++) {
                ReadOnlyCell cell = this.sheet.getReadOnlyCellFromShell(eR, i);
                if (cell != null && cell.getCell() != null && (cell.getCell().getValue().getValue() != null || sheet.isCoveredUnderMerge(eR, i))) {
                    this.bottomAdjacent = !this.bottomAdjacent;
                    break;
                }
            }
        }
        return this.bottomAdjacent;
    }
//    private void expandLeftAdjacent(int sC,int sR,int eC,int eR)
//    {
//        
//    }

    private int ExpandTableUpward() {
        int start = this.startCol;
        int end = this.endCol;
        int topIndex = this.startRow;
        int min = topIndex;
        for (; start <= end && !this.isNeededToRevokePrediction(startCol, endCol, min, endRow); start++) {
            topIndex = findTopIndex(min, start);
            min = (min < topIndex) ? min : topIndex;
        }
        return min;
    }

    private int findTopIndex(int baseIndex, int colIndex) {
        for (; baseIndex >= 0 && !this.isNeededToRevokePrediction(startCol, endCol, baseIndex, endRow); baseIndex--) {
            Cell currPos = (baseIndex >= 0) ? this.sheet.getReadOnlyCellFromShell(baseIndex, colIndex).getCell() : null;
            Cell currRight = (baseIndex >= 0 && colIndex < Utility.MAXNUMOFCOLS - 2) ? this.sheet.getReadOnlyCellFromShell(baseIndex, colIndex + 1).getCell() : null;
            if (!(currPos != null && (currPos.getValue().getValue() != null)) && !(currRight != null && (currRight.getValue().getValue() != null))) {
                baseIndex++;
                break;
            }

        }
        return baseIndex < 0 ? ++baseIndex : this.isNeededToRevokePrediction(startCol, endCol, startRow, baseIndex) ? ++baseIndex : baseIndex;
    }

    private int ExpandTableDownward() {
        int start = this.startCol;
        int end = this.endCol;
        int bottomIndex = this.endRow;
        int max = bottomIndex;
        for (; start <= end && !this.isNeededToRevokePrediction(startCol, endCol, startRow, max); start++) {
            bottomIndex = findBottomIndex(max, start);
            max = (max >= bottomIndex) ? max : bottomIndex;
        }
        return max;
    }

    private int findBottomIndex(int baseIndex, int colIndex) {
        for (; baseIndex <= Utility.MAXNUMOFROWS - 1 && !this.isNeededToRevokePrediction(startCol, endCol, startRow, baseIndex); baseIndex++) {
            Cell currPos = this.sheet.getReadOnlyCellFromShell(baseIndex, colIndex).getCell();
            Cell currRightPos = (baseIndex < Utility.MAXNUMOFROWS && colIndex < Utility.MAXNUMOFCOLS - 2) ? this.sheet.getReadOnlyCellFromShell(baseIndex, colIndex + 1).getCell() : this.sheet.getReadOnlyCellFromShell(baseIndex, colIndex).getCell();
            if (!(currPos != null && (currPos.getValue().getValue() != null)) && !(currRightPos != null && (currRightPos.getValue().getValue() != null))) {
                baseIndex--;
                break;
            }

        }
        if (this.isNeededToRevokePrediction(startCol, endCol, startRow, baseIndex)) {
            baseIndex--;
        }
        return baseIndex > Utility.MAXNUMOFROWS - 1 ? --baseIndex : baseIndex;
    }

    private int findStartColIndex() {
        int iIndex, sRow, sCol, eRow, eCol;
        sRow = this.startRow;
        sCol = this.startCol;
        eCol = this.endCol;
        eRow = this.endRow;
        Cell curPos;
        Cell curTopPos;
        Cell curBotPos;

        for (iIndex = this.startCol; (iIndex >= 0 && !this.isNeededToRevokePrediction(iIndex, endCol, startRow, endRow)); iIndex--) {
            curPos = sheet.getReadOnlyCellFromShell(sRow, iIndex).getCell();
            curTopPos = (sRow > 0) ? sheet.getReadOnlyCellFromShell(sRow - 1, iIndex).getCell() : sheet.getReadOnlyCellFromShell(sRow, iIndex).getCell();
            curBotPos = (sRow < Utility.MAXNUMOFROWS - 2) ? sheet.getReadOnlyCellFromShell(sRow + 1, iIndex).getCell() : sheet.getReadOnlyCellFromShell(sRow, iIndex).getCell();
            if (!(curPos != null && (curPos.getValue().getValue() != null || sheet.isCoveredUnderMerge(sRow, iIndex)))) {
                if (!(curTopPos != null && curTopPos.getValue().getValue() != null)) {
                    if (!(curBotPos != null && curBotPos.getValue().getValue() != null)) {
                        iIndex += (this.startCol == iIndex) ? 0 : 1;
                        break;
                    } else {
                        sRow++;
                    }
                } else {
                    sRow--;
                }
            }
        }
        if(this.isNeededToRevokePrediction(iIndex, endCol, startRow, endRow))
        {
            ++iIndex;
        }
        return iIndex < 0 ? ++iIndex : iIndex;
    }

    private int findEndColIndex() {
        int iIndex, sRow, sCol, eRow, eCol;
        sRow = this.startRow;
        sCol = this.startCol;
        eCol = this.endCol;
        eRow = this.endRow;
        Cell curPos;
        Cell curTopPos;
        Cell curBotPos;

        for (iIndex = this.endCol; (iIndex <= Utility.MAXNUMOFCOLS - 1 && !this.isNeededToRevokePrediction(startCol, iIndex, startRow, endRow)); iIndex++) {
            curPos = sheet.getReadOnlyCellFromShell(eRow, iIndex).getCell();
            curTopPos = (eRow > 0) ? sheet.getReadOnlyCellFromShell(eRow - 1, iIndex).getCell() : sheet.getReadOnlyCellFromShell(eRow, iIndex).getCell();
            curBotPos = (eRow < Utility.MAXNUMOFROWS - 2) ? sheet.getReadOnlyCellFromShell(eRow + 1, iIndex).getCell() : sheet.getReadOnlyCellFromShell(eRow, iIndex).getCell();

            if (!(curPos != null && (curPos.getValue().getValue() != null || sheet.isCoveredUnderMerge(eRow, iIndex)))) {
                if (!(curTopPos != null && curTopPos.getValue().getValue() != null)) {
                    if (!(curBotPos != null && curBotPos.getValue().getValue() != null)) {
                        iIndex -= (this.endCol == iIndex) ? 0 : 1;
                        break;
                    } else {
                        eRow++;
                    }
                } else {
                    eRow--;
                }
            }

        }
        if(iIndex >= Utility.MAXNUMOFCOLS || this.isNeededToRevokePrediction(startCol, iIndex, startRow, endRow))
        {
            --iIndex;
        }
        return iIndex;
    }

    private static void addRangeValues(List<List<Object>> temp, List<List<Object>> finalAry, int inserIndex, String seriesIn) {

//        int totalRows = temp.length;
//        int totalCols = temp[0].length;
//        if (seriesIn.equals("HORIZONTAL")) //No I18N
//        {
//            for (int sR = 0; sR < totalRows; sR++) {
//                for (int sC = 0, c = inserIndex; sC < totalCols; sC++, c++) {
//                    finalAry[sR][c] = temp[sR][sC];
//                }
//            }
//        } else {
//            for (int sR = 0, r = inserIndex; sR < totalRows; sR++, r++) {
//                System.arraycopy(temp[sR], 0, finalAry[r], 0, totalCols);
//            }
//        }
        int totalRows = temp.size();
        int totalCols = (temp.get(0) != null) ? temp.get(0).size() : 0;
        if (seriesIn.equals("HORIZONTAL")) //No I18N
        {
            for (int sR = 0; sR < totalRows; sR++) {
                List<Object> finalCurrentRow = (finalAry.size() > sR && finalAry.get(sR) != null) ? finalAry.get(sR) : new ArrayList<Object>();
                List<Object> tempRow = (temp.get(sR) != null) ? temp.get(sR) : null;
                if (tempRow != null) {
                    if (inserIndex > 0 && finalCurrentRow.size() < inserIndex) {
                        for (int i = finalCurrentRow.size(); i < inserIndex; i++) {
                            finalCurrentRow.add(null);
                        }
                    }
                    finalCurrentRow.addAll(tempRow);
                }
                if (finalAry.size() <= sR) {
                    finalAry.add(finalCurrentRow);
                }
            }
        } else {
            if (inserIndex > 0) {
                for (int i = 0; i < finalAry.size(); i++) {
                    List<Object> currentRow = finalAry.get(i);
                    if (currentRow != null && currentRow.size() < totalCols) {
                        int diff = totalCols - currentRow.size();
                        if (diff > 0) {
                            for (int j = 0; j < diff; j++) {
                                currentRow.add(null);
                            }
                        }
                    }
                }
            }
            for (int sR = 0; sR < totalRows; sR++) {
                List<Object> newRow = new ArrayList<Object>();
                List<Object> tempRow = (temp.get(sR) != null) ? temp.get(sR) : null;
                if (tempRow != null) {
                    for (int sC = 0; sC < totalCols; sC++) {
                        newRow.add(tempRow.get(sC));
                    }
                    finalAry.add(newRow);
                }

            }

        }

    }

    public static Object[][] getFinalTableDataArray(Sheet sheet, List<Range> dataRanges, String appendingWay, boolean isIncludeHiddenCells) {
        if (sheet == null || dataRanges == null) {
            return null;
        }
        appendingWay = appendingWay != null ? appendingWay : "Vertically"; //NO I18N
        List<Range> ranges = new ArrayList<>();
        int[] insertArray = new int[dataRanges.size()];
        int totalRows = 0, totalCols = 0, dataRang = 0;
        for (int i = 0; i < dataRanges.size(); i++) {
            String associatedSheetName = dataRanges.get(i).getSheet().getAssociatedName();
            Sheet rangeSheet = sheet.getWorkbook().getSheetByAssociatedName(associatedSheetName);
            int startRow = dataRanges.get(i).getStartRowIndex();
            int startCol = dataRanges.get(i).getStartColIndex();
            int endRow = dataRanges.get(i).getEndRowIndex();
            int endCol = dataRanges.get(i).getEndColIndex();

            int rowSize = 0;
            endRow = Math.max(startRow, Math.min(rangeSheet.getUsedRowIndex(), endRow));
            endCol = Math.max(startCol, Math.min(rangeSheet.getUsedColumnIndex(), endCol));
            if (!isIncludeHiddenCells) {
                for (int sR = startRow; sR <= endRow; sR++) {
                    if(!RowUtil.isFilteredRow(rangeSheet.getReadOnlyRowFromShell(sR).getRow()))
                    {
                        rowSize++;
                    }
                }
            }

            Range range = new Range(rangeSheet, startRow, startCol, endRow, endCol);
            ranges.add(range);
            insertArray[dataRang] = (!isIncludeHiddenCells) ? rowSize : range.getRowSize();
            if (appendingWay.equals("HORIZONTAL")) {
                totalRows = (isIncludeHiddenCells) ? Math.max(totalRows, range.getRowSize()) : Math.max(totalRows, rowSize);
                totalCols = totalCols + range.getColSize();
            } else {
                totalCols = Math.max(totalCols, range.getColSize());
                totalRows = (isIncludeHiddenCells) ? totalRows + range.getRowSize() : totalRows + rowSize;
            }
            dataRang++;

        }
        if (totalRows == 0 || totalCols == 0) {
            return null;
        }
        try {
            List<List<Object>> finalList = new ArrayList<>();
            int insertIndex = 0, aryIndex = 0;
            for (Range range : ranges) {
                List<List<Object>> temp = range.getRangeValues(isIncludeHiddenCells, false);	//passing false will not skip the empty rows in the data range.
                if (appendingWay.equals("HORIZONTAL")) {
                    if (temp != null) {
                        addRangeValues(temp, finalList, insertIndex, "HORIZONTAL");  //No I18N
                    }
                    insertIndex = insertIndex + range.getColSize();

                } else {
                    if (temp != null) {
                        addRangeValues(temp, finalList, insertIndex, "VERTICAL");   //No I18N
                    }
                    insertIndex = insertIndex + insertArray[aryIndex];  //range.getRowSize(); 
                }
                aryIndex++;

            }
            if(finalList == null || finalList.isEmpty()){
            	return null;
            }
            Object[][] finalAry = new Object[finalList.size()][finalList.get(0).size()];
            for (int i = 0; i < finalList.size(); i++) {
                List<Object> currentRow = finalList.get(i);
                if (currentRow != null) {
                    for (int j = 0; j < currentRow.size(); j++) {
                        finalAry[i][j] = currentRow.get(j);
                    }
                }

            }
            return finalAry;
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "[TABLE PREDICTION] Constructing Table Generated Exception {0}", e);
        }
        return null;
    }

    protected void contructTableCellMatrix() {
        if (this.tRange != null) {
            List<Range> RangeList = new ArrayList<>();
            RangeList.add(this.tRange);
            this.cellAry = TablePrediction.getFinalTableDataArray(sheet, RangeList, "HORIZONTAL", true); //NO I18N
        }
    }

//   private void constructTableHeaderCellMatrix(){
//       if(this.tRange != null && this.headerRange != null){
//         List<Range> RangeList = new ArrayList<>();
//         RangeList.add(this.headerRange);
//         this.cellAry = TablePrediction.getFinalTableDataArray(sheet, RangeList, "HORIZONTAL" , true); //NO I18N
//       }
//   }
//   
//   private void constructTableLabelCellMatrix(){
//       if(this.tRange != null && this.labelRange != null){
//         List<Range> RangeList = new ArrayList<>();
//         RangeList.add(this.labelRange);
//         this.cellAry = TablePrediction.getFinalTableDataArray(sheet, RangeList, "HORIZONTAL" , true); //NO I18N
//       }
//   }
    public static boolean isNeededToIncludeInGroupCategory(Object[][] data, int colIndex, int startRow, int endRow) {
        CellProperties cell;
        int cString = 0, cInt = 0;
        for (; startRow < endRow; startRow++) {
            cell = (CellProperties) data[startRow][colIndex];
            if (cell != null && cell.get(0) != null && cell.get(0) instanceof Number && !(cell.get(5) != null && cell.get(5) == Type.STRING)) {
                cInt++;
            } else if (cell != null && cell.get(1) != null && !(cell.get(1) instanceof Number)) {
                cString++;
            }
        }

        return (cString > cInt);
    }

    private int getCategoryLastIndex() {
        int result = 0;
        int length = this.cellAry[0].length;
        for (int i = 0; i < length; i++) {
            if (!TablePrediction.isNeededToIncludeInGroupCategory(this.cellAry, i, 1, this.cellAry.length)) {
                break;
            }
            result++;
        }
        return result;
    }

    private int getHeaderLastIndex() {
        int result = 0;
        int currDepth;
        int length = this.cellAry[0].length;
        for (int i = this.labelDepth; i < length; i++) {
            currDepth = getCurrentHeaderDepth(i);
            result = result == 0 ? Math.max(result, currDepth) : Math.min(result, currDepth);
        }
        return result;
    }

    private int getCurrentHeaderDepth(int colIndex) {
        int depth = 0, incr;
        CellProperties cell;
        int length = this.cellAry.length;
        for (int i = 0; i < length; i++) {
            incr = 1;
            cell = (CellProperties) this.cellAry[i][colIndex];
            if (!(cell != null && cell.get(1) != null && (cell.get(5) == null || (cell.get(5) != null && cell.get(5) == Type.FLOAT)))) {
                if ((i >= depth)) {
                    incr = (i - depth + 1);
                }
                depth += incr;
            } else {
                break;
            }
        }
        return depth;
    }

    private void splitTableRange() {
        if (this.cellAry == null) {
            this.contructTableCellMatrix();
        }
        if (!this.isSkipLabelRangeDetection()) {
            this.setLabelDepth(this.getCategoryLastIndex());
        }
        this.setHeaderDepth(this.isConfigByUser() ? this.getHeaderDepth() : this.getHeaderLastIndex());
        this.setLabelRange();
        this.setHeaderRange();
        this.setValueRange();
    }

    private void setLabelRange() {
        if (this.getLabelDepth() > 0) {
            int endColdepth = this.startCol + this.getLabelDepth() - 1;
            int startRowDepth = this.startRow + this.getHeaderDepth();
            this.labelRange = new Range(this.sheet, startRowDepth, this.startCol, this.endRow, endColdepth);
        }
    }

    private void setHeaderRange() {
        if (this.getHeaderDepth() > 0) {
            int startDepth = this.startCol + this.getLabelDepth();
            int depth = this.getHeaderDepth() - 1;
            this.headerRange = new Range(this.sheet, this.startRow, startDepth, this.startRow + depth, this.endCol);
        }
    }

    private void setValueRange() {
        int ldepth = this.getLabelDepth();
        int startColDepth = this.startCol + this.getLabelDepth();
        int hdepth = this.getHeaderDepth();
        int startRowDepth = this.startRow + hdepth;
        this.valueRange = new Range(this.sheet, startRowDepth, startColDepth, this.endRow, this.endCol);
    }

    protected JSONArrayWrapper getHeaderList() {
        if (this.cellAry == null) {
            this.contructTableCellMatrix();
        }
        if (this.tRange != null && this.getHeaderDepth() > 0) {
            constructHeaderList();
            return getNewHeaderList();
        }
        return null;
    }

    private JSONArrayWrapper getLabelList() {
        if (this.cellAry == null) {
            this.contructTableCellMatrix();
        }
        if (this.tRange != null) {
            constructLabelList();
            return getNewLabelList();
        }
        return null;
    }

    private Range getHeaderRange() {
        return this.headerRange;
    }

    private Range getLabelRange() {
        return this.labelRange;
    }

    private Range getValueRange() {
        return this.valueRange;
    }

    /*
    constructing Label Grouped Object 
     categories = [{
                   name : "label Name",
                   categories : [{
                }]
              ......
              ......
        }]
     */
    public static int getGroupedRowSpan(Object[][] finalAry, int noOfSeries, int rowIndex, int colIndex, int totalRows) {
        int rowSpan = 1;
        for (int sR = rowIndex, count = 0; count < noOfSeries && sR < totalRows; sR++, count++) {

            CellProperties cp = (CellProperties) finalAry[sR][colIndex];

            if (cp != null && (String.valueOf(cp.get(1)).equals("null") || String.valueOf(cp.get(1)).length() == 0)) {
                if (sR > rowIndex) {
                    rowSpan++;
                }
            } else if (sR > rowIndex) {
                break;
            }
        }
        return rowSpan;

    }

    public JSONArrayWrapper constructSubCategoryObject(Object[][] finalAry, int noOfSeris, int length, int rowIndex, int colIndex, int totalRows) {

        JSONArrayWrapper category = new JSONArrayWrapper();
        int incr = 1;
        for (int sR = rowIndex, count = 0; count < noOfSeris; count += incr, sR += incr) {
            JSONObjectWrapper subCategory = new JSONObjectWrapper();
            CellProperties cp = (CellProperties) finalAry[sR][colIndex];
            if (length > 1) {
                incr = (cp != null && (Boolean) cp.get(3)) ? ((Integer) cp.get(4)) : getGroupedRowSpan(finalAry, noOfSeris - count, sR, colIndex, totalRows);
                subCategory.put("name", (cp != null && cp.get(1) != null) ? cp.get(1) : "  ");
                subCategory.put("categories", constructSubCategoryObject(finalAry, incr, length - 1, sR, colIndex + 1, totalRows));
                category.put(subCategory);
            } else {
                incr = 1;
                category.put((cp != null && cp.get(1) != null) ? cp.get(1) : "  ");
            }

        }

        return category;
    }

    public JSONArrayWrapper constructGroupedCategory(Object[][] finalAry, int noOfSeries, int length, int depth) {
        int incr = 1;
        JSONArrayWrapper finalCategory = new JSONArrayWrapper();
        for (int sR = depth; sR < noOfSeries; sR += incr) {
            JSONObjectWrapper category = new JSONObjectWrapper();
            CellProperties cp = (CellProperties) finalAry[sR][0];

            if (length > 1) {
                incr = (cp != null && (Boolean) cp.get(3)) ? ((Integer) cp.get(4)) : getGroupedRowSpan(finalAry, noOfSeries, sR, 0, noOfSeries);
                category.put("name", (cp != null && cp.get(1) != null) ? cp.get(1) : "  ");
                category.put("categories", constructSubCategoryObject(finalAry, incr, length - 1, sR, 1, noOfSeries));
                finalCategory.put(category);
            } else {
                incr = 1;
                finalCategory.put((cp != null && cp.get(1) != null) ? cp.get(1) : "  ");
            }

        }
        return finalCategory;
    }

    /*
    constructing Header Grouped Object 
     Headers = [{
                   name : "header Name",
                   Headers : [{
                }]
              ......
              ......
        }]
     */
    public static int getGroupedColSpan(Object[][] finalAry, int noOfSeries, int rowIndex, int colIndex, int totalCols) {
        int colSpan = 1;
        for (int sC = colIndex, count = 0; count < noOfSeries && sC < totalCols; sC++, count++) {

            CellProperties cp = (CellProperties) finalAry[rowIndex][sC];

            if (cp != null && (String.valueOf(cp.get(1)).equals("null") || String.valueOf(cp.get(1)).length() == 0)) {
                if (sC > colIndex) {
                    colSpan++;
                }
            } else if (sC > colIndex) {
                break;
            }
        }
        return colSpan;

    }

    public JSONArrayWrapper constructSubHeaderObject(Object[][] finalAry, int noOfSeris, int length, int rowIndex, int colIndex, int totalCols) {

        JSONArrayWrapper header = new JSONArrayWrapper();
        int incr = 1;
        int rowIncr = 1;
        for (int sC = colIndex, count = 0; count < noOfSeris; count += incr, sC += incr) {
            JSONObjectWrapper subHeader = new JSONObjectWrapper();
            CellProperties cp = (CellProperties) finalAry[rowIndex][sC];
            if (length > 1) {
                incr = (cp != null && ((Integer) cp.get(6)) > 1) ? ((Integer) cp.get(6)) : getGroupedColSpan(finalAry, noOfSeris - count, rowIndex, sC, totalCols);
                subHeader.put("name", (cp != null && cp.get(1) != null) ? cp.get(1) : "  ");
                subHeader.put("colIndex", sC + this.startCol);
                rowIncr = cp != null && this.isSkipHiddenCells() ? ((Integer) cp.get(4)) : 1;
                //subHeader.put("headers", constructSubHeaderObject(finalAry, incr, length - 1, rowIndex + rowIncr, sC, totalCols));
                header.put(subHeader);
            } else {
                incr = (cp != null && this.isSkipHiddenCells()) ? ((Integer) cp.get(6)) : 1;
//               category.put((cp != null && cp.get(1) !=null) ? cp.get(1) : "  "); //follow the same structure same as head node
                JSONObjectWrapper leafNode = new JSONObjectWrapper();
                leafNode.put("name", (cp != null && cp.get(1) != null) ? cp.get(1) : "  ");
                leafNode.put("colIndex", sC + this.startCol);
                header.put(leafNode);
            }

        }

        return header;
    }

    public JSONArrayWrapper constructHeaderCategory(Object[][] finalAry, int noOfSeries, int length, int depth) {
        int incr = 1;
        JSONArrayWrapper finalHeader = new JSONArrayWrapper();
        int rowIncr = 1;
        for (int sC = depth; sC < noOfSeries; sC += incr) {
            JSONObjectWrapper subHeader = new JSONObjectWrapper();
            CellProperties cp = (CellProperties) finalAry[0][sC];

            if (length > 1) {
                incr = (cp != null && ((Integer) cp.get(6)) > 1) ? ((Integer) cp.get(6)) : getGroupedColSpan(finalAry, noOfSeries, 0, sC, noOfSeries);
                subHeader.put("name", (cp != null && cp.get(1) != null) ? cp.get(1) : "  ");
                subHeader.put("colIndex", sC + this.startCol);
                rowIncr = cp != null && this.isSkipHiddenCells() ? ((Integer) cp.get(4)) : 1;
                subHeader.put("headers", constructSubHeaderObject(finalAry, incr, length - 1, rowIncr, sC, noOfSeries));
                finalHeader.put(subHeader);
            } else {
                incr = (cp != null && this.isSkipHiddenCells()) ? ((Integer) cp.get(6)) : 1;
//               finalHeader.put((cp != null && cp.get(1) !=null) ? cp.get(1) : "  "); //follow the same structure same as head node
                JSONObjectWrapper leafNode = new JSONObjectWrapper();
                leafNode.put("name", (cp != null && cp.get(1) != null) ? cp.get(1) : "  ");
                leafNode.put("colIndex", sC + this.startCol);
                finalHeader.put(leafNode);
            }

        }
        return finalHeader;
    }

    private void constructHeaderList() {
        this.headerObject = this.constructHeaderCategory(this.cellAry, this.cellAry[0].length, this.getHeaderDepth(), this.getLabelDepth());
    }

    private void constructLabelList() {
        this.labelObject = this.constructGroupedCategory(this.cellAry, this.cellAry.length, this.getLabelDepth(), this.getHeaderDepth());
    }

    protected JSONArrayWrapper getNewHeaderList() {
        return this.headerObject;
    }

    private JSONArrayWrapper getNewLabelList() {
        return this.labelObject;
    }

    private void setLabelDepth(int depth) {
        this.labelDepth = depth;
    }

    private void setHeaderDepth(int depth) {
        this.headerDepth = depth;
    }

    protected int getLabelDepth() {
        return this.labelDepth;
    }

    protected int getHeaderDepth() {
        return this.headerDepth;
    }

    private void findTableDataTypeColumnWise() {
        int depth = Math.min(this.cellAry.length, this.maxDepth);
        int sIndex = Math.max(0, this.getHeaderDepth() - 1);
        int startRowIndex = (this.startRow + sIndex);
        int endRowIndex = (this.startRow + depth);
        this.tDataType = DataTypePredictor.findDataTypes(this.sheet.getWorkbook(), startRowIndex, this.startCol, endRowIndex, this.endCol, this.sheet.getAssociatedName());
    }

    private void contructingDeclarativeTable() {
        this.contructTableCellMatrix();
        this.splitTableRange();
        if (this.isDCDTNeeded()) {
            this.findTableDataTypeColumnWise();
        }
    }

    private void constructingTableResponse() {
        this.tableResponse = new JSONObjectWrapper();
        JSONObjectWrapper splitRange = new JSONObjectWrapper();
        splitRange.put(TableConstraints.HEADER_RANGE, this.getHeaderRange() != null ? this.getHeaderRange().toDataRange() : null);
        splitRange.put(TableConstraints.LABEL_RANGE, this.getLabelRange() != null ? this.getLabelRange().toDataRange() : null);
        splitRange.put(TableConstraints.VALUE_RANGE, this.getValueRange() != null ? this.getValueRange().toDataRange() : null);

        //TODO Adding split range list into table response
        this.tableResponse.put(TableConstraints.TABLE_RANGE, this.tRange.toDataRange());
        this.tableResponse.put(TableConstraints.SPLIT_RANGE, splitRange);
        this.tableResponse.put(TableConstraints.TABLE_COLUMN_DATA_TYPE, this.tDataType);

        if (this.isNeededRangeData()) {
            JSONObjectWrapper rangeData = new JSONObjectWrapper();
            rangeData.put(TableConstraints.HEADER_RANGE_DATA, this.isNeededHeaderData() ? this.getHeaderList() : null);
            rangeData.put(TableConstraints.LABEL_RANGE_DATA, this.isNeededLabelData() ? this.getLabelList() : null);
            this.tableResponse.put(TableConstraints.TABLE_RANGE_DATA, rangeData);
        }

    }

    protected void initializeTableResponse() {
        this.tableResponse = new JSONObjectWrapper();
    }

    protected void setPropertyInTableResponse(String key, Object value) {
        this.tableResponse.put(key, value);
    }

    protected JSONObjectWrapper getTableResponse() {
        return this.tableResponse;
    }

    protected Object[][] getTableData() {
        return this.cellAry;
    }

    protected void setPropertiesForAggregation(int headerIndex) {
        this.setLabelDepth(1);
        if(this.cellAry == null){
        	return;
        }
        if (headerIndex == -1) {
            this.setHeaderDepth(this.getHeaderLastIndex());
        } else {
            this.setHeaderDepth(headerIndex);
        }

        this.setHeaderRange();
    }

    protected void setHeaderObject(JSONArrayWrapper headerObject) {
        this.headerObject = headerObject;
    }

    protected void setRange(Range range) {
        if (range != null) {
            this.sStartRow = this.startRow = range.getStartRowIndex();
            this.sStartCol = this.startCol = range.getStartColIndex();
            this.sEndRow = this.endRow = range.getEndRowIndex();
            this.sEndCol = this.endCol = range.getEndColIndex();
            this.setSheet(range.getSheet());
            this.tRange = range;
        }
    }

    public Range doFilterRange(Range predictedRange, String seriesIn) {
        this.topRowIndex    = predictedRange.getStartRowIndex();
        this.rightColIndex  = predictedRange.getEndColIndex();
        this.leftColIndex   = predictedRange.getStartColIndex();
    	this.bottomRowIndex = predictedRange.getEndRowIndex();
        
        boolean isEvaluated = this.evaluateNewTopIndex(this.getFilterRange(predictedRange.getSheet()), seriesIn);
        if(!isEvaluated)
        {
            return null;
        }
        isEvaluated = this.evaluateNewBottomIndex(this.getFilterRange(predictedRange.getSheet()), seriesIn);
        if(!isEvaluated)
        {
            return null;
        }
        isEvaluated = this.evaluateNewRightColIndex(this.getFilterRange(predictedRange.getSheet()), seriesIn);
        if(!isEvaluated)
        {
            return null;
        }
        isEvaluated = this.evaluateNewLeftColIndex(this.getFilterRange(predictedRange.getSheet()), seriesIn);
        if(!isEvaluated)
        {
            return null;
        }
    	Range newlyEvaluatedRange = this.getFilterRange(predictedRange.getSheet());
        return newlyEvaluatedRange;
    }
    
    private boolean evaluateNewTopIndex(Range predictedRange, String seriesIn)
    {
        // using spliterator to stream the values in range. for top traversal.
        if(seriesIn.equals("COLS"))
        {
            for(int i = predictedRange.getStartRowIndex(); i <= predictedRange.getEndRowIndex(); i++)
            {
                if(this.isTimeOut(startTime))
                {
                    return false;
                }
                Range rangeToEvaluate = new Range(predictedRange.getSheet(), i, predictedRange.getStartColIndex(), i, predictedRange.getEndColIndex());
                int colSize = predictedRange.getColSize();
                Spliterator<ReadOnlyCell> spitIterator = rangeToEvaluate.spliterator();
                long count = StreamSupport.stream(spitIterator, true).filter((ReadOnlyCell rCell) -> {
                    boolean isCellNotNull = rCell != null && rCell.getCell() != null && rCell.getCell().getValue().getValue() != null;
                    boolean isMergedCell = rCell != null && rCell.getSheet().getMergeParentCell(rCell.getRowIndex(), rCell.getColIndex()) != null;
                    return isCellNotNull && !isMergedCell;
                }).count();
                if(count >= Math.round(0.25 * colSize))
                {
                    topRowIndex = i;
                    break;
                }
            }
        }
        else
        {
            for(int i = predictedRange.getStartColIndex(); i <= predictedRange.getEndColIndex(); i++)
            {
                if(this.isTimeOut(startTime))
                {
                    return false;
                }
                Range rangeToEvaluate = new Range(predictedRange.getSheet(), predictedRange.getStartRowIndex(), i, predictedRange.getEndRowIndex(), i);
                int rowSize = predictedRange.getRowSize();
                Spliterator<ReadOnlyCell> spitIterator = rangeToEvaluate.spliterator();
                long count = StreamSupport.stream(spitIterator, true).filter((ReadOnlyCell rCell) -> {
                    boolean isCellNotNull = rCell != null && rCell.getCell() != null && rCell.getCell().getValue().getValue() != null;
                    boolean isMergedCell = rCell != null && rCell.getSheet().getMergeParentCell(rCell.getRowIndex(), rCell.getColIndex()) != null;
                    return isCellNotNull && !isMergedCell;
                }).count();
                if(count >= Math.round(0.25 * rowSize))
                {
                    leftColIndex = i;
                    break;
                }
            }
        }
        return true;
    }
    
    
    private boolean evaluateNewBottomIndex(Range predictedRange, String seriesIn){
    	
    	// using spliterator to stream the values in range. for bottom traversal.
    	if(seriesIn.equals("COLS")){
    		for(int i=predictedRange.getEndRowIndex();i>=predictedRange.getStartRowIndex(); i--){

                if(this.isTimeOut(startTime))
                {
                    return false;
                }
    			Range rangeToEvaluate = new Range(predictedRange.getSheet(), i, predictedRange.getStartColIndex(), i, predictedRange.getEndColIndex());
    			int colSize = predictedRange.getColSize();
        		Spliterator<ReadOnlyCell> spitIterator = rangeToEvaluate.spliterator();
        		long count = StreamSupport.stream(spitIterator, true).filter((ReadOnlyCell rCell)->{
            		return rCell != null && rCell.getCell() != null && rCell.getCell().getValue().getValue() != null;
        		}).count();
        		if(count >= Math.round(0.25*colSize)){
        			bottomRowIndex = i;
        			break;
        		}
    		}
    		Range rangeToEvaluate = new Range(predictedRange.getSheet(), bottomRowIndex, predictedRange.getStartColIndex(), bottomRowIndex, predictedRange.getEndColIndex());
    		int colSize = predictedRange.getColSize();
    		Spliterator<ReadOnlyCell> spitIterator = rangeToEvaluate.spliterator();
    		long count = StreamSupport.stream(spitIterator, true).filter((ReadOnlyCell rCell)->{
        		return rCell != null && rCell.getCell() != null && rCell.getCell().isFormula() && (rCell.getCell().getFormula().contains("SUM") || rCell.getCell().getFormula().contains("AVERAGE"));	//No I18N
    		}).count();
    		if((int)count > 0 && (int)count == colSize-1) {
    			bottomRowIndex--;
    		}
    	}else{
    		for(int i=predictedRange.getEndColIndex();i>=predictedRange.getStartColIndex(); i--){

                if(this.isTimeOut(startTime))
                {
                    return false;
                }
    			Range rangeToEvaluate = new Range(predictedRange.getSheet(), predictedRange.getStartRowIndex(), i, predictedRange.getEndRowIndex(), i);
    			int rowSize = predictedRange.getRowSize();
        		Spliterator<ReadOnlyCell> spitIterator = rangeToEvaluate.spliterator();
        		long count = StreamSupport.stream(spitIterator, true).filter((ReadOnlyCell rCell)->{
            		return rCell != null && rCell.getCell() != null && rCell.getCell().getValue().getValue() != null;
        		}).count();
        		if(count >= Math.round(0.25*rowSize)){
        			rightColIndex = i;
        			break;
        		}
    		}
    	}
        return true;
    }
    
    private boolean evaluateNewRightColIndex(Range predictedRange, String seriesIn){
    	// right side traversal to eliminate fully/more than 3/4 empty columns.
    	if(seriesIn.equals("COLS")){
    		for(int i=predictedRange.getEndColIndex();i>=predictedRange.getStartColIndex(); i--){

                if(this.isTimeOut(startTime))
                {
                    return false;
                }
    			Range rangeToEvaluate = new Range(predictedRange.getSheet(), predictedRange.getStartRowIndex(), i, predictedRange.getEndRowIndex(), i);
    			int rowSize = predictedRange.getRowSize();
        		Spliterator<ReadOnlyCell> spitIterator = rangeToEvaluate.spliterator();
        		long count = StreamSupport.stream(spitIterator, true).filter((ReadOnlyCell rCell)->{
        			return rCell != null && rCell.getCell() != null && rCell.getCell().getValue().getValue() != null;
        		}).count();
        		if(count >= Math.round(0.25*rowSize)){
        			rightColIndex = i;
        			break;
        		}
    		}
    	}else{
    		for(int i=predictedRange.getEndRowIndex();i>=predictedRange.getStartRowIndex(); i--){

                if(this.isTimeOut(startTime))
                {
                    return false;
                }
    			Range rangeToEvaluate = new Range(predictedRange.getSheet(),i, predictedRange.getStartColIndex(), i, predictedRange.getStartColIndex());
    			int colSize = predictedRange.getColSize();
        		Spliterator<ReadOnlyCell> spitIterator = rangeToEvaluate.spliterator();
        		long count = StreamSupport.stream(spitIterator, true).filter((ReadOnlyCell rCell)->{
        			return rCell != null && rCell.getCell() != null && rCell.getCell().getValue().getValue() != null;
        		}).count();
        		if(count >= Math.round(0.25*colSize)){
        			bottomRowIndex = i;
        			break;
        		}
    		}
    	}
		return true;
    }
    
    private boolean evaluateNewLeftColIndex(Range predictedRange, String seriesIn){
    	// left side traversal to eliminate fully/more than 3/4 empty columns.
    	if(seriesIn.equals("COLS")){
    		for(int i=predictedRange.getStartColIndex();i<=predictedRange.getEndColIndex(); i++){
                if(this.isTimeOut(startTime))
                {
                    return false;
                }
    			Range rangeToEvaluate = new Range(predictedRange.getSheet(), predictedRange.getStartRowIndex(), i, predictedRange.getEndRowIndex(), i);
    			int rowSize = predictedRange.getRowSize();
        		Spliterator<ReadOnlyCell> spitIterator = rangeToEvaluate.spliterator();
        		long count = StreamSupport.stream(spitIterator, true).filter((ReadOnlyCell rCell)->{
        			return rCell != null && rCell.getCell() != null && rCell.getCell().getValue().getValue() != null;
        		}).count();
        		if(count >= Math.round(0.75*rowSize)){
        			leftColIndex = i;
        			break;
        		}
    		}
    	}else{
    		for(int i=predictedRange.getStartRowIndex();i<=predictedRange.getEndRowIndex(); i++){

                if(this.isTimeOut(startTime))
                {
                    return false;
                }
    			Range rangeToEvaluate = new Range(predictedRange.getSheet(), i,predictedRange.getStartColIndex(), i, predictedRange.getStartColIndex());
    			int colSize = predictedRange.getColSize();
        		Spliterator<ReadOnlyCell> spitIterator = rangeToEvaluate.spliterator();
        		long count = StreamSupport.stream(spitIterator, true).filter((ReadOnlyCell rCell)->{
        			return rCell != null && rCell.getCell() != null && rCell.getCell().getValue().getValue() != null;
        		}).count();
        		if(count >= Math.round(0.75*colSize)){
        			topRowIndex = i;
        			break;
        		}
    		}
    	}
		return true;
    }

    private boolean isTableRange() {


        if(this.intersectedTable != null) {
            this.startRow = intersectedTable.getStartRowIndex();
            this.startCol = intersectedTable.getStartColIndex();
            this.endRow = intersectedTable.isFooterRowShown() ? intersectedTable.getEndRowIndex() - 1 : intersectedTable.getEndRowIndex();
            this.endCol = intersectedTable.getEndColIndex();
            return true;
        }

        return false;
    }

    private void setIntersectingTable() {
        DataRange range = new DataRange(this.sheet.getAssociatedName(), this.startRow, this.startCol, this.endRow, this.endCol);
        List<Table> intersectedTables = TableUtil.getIntersectingTables(this.sheet.getWorkbook(), Arrays.asList(range), true);

        if(intersectedTables.size() == 1)
        {
            this.intersectedTable = intersectedTables.get(0);
        }
    }

    public boolean isIntersectsTable() {
        return this.intersectedTable != null;
    }

    public int getTimeOut()
    {
        return timeOut;
    }

    public void setTimeOut(int timeOut)
    {
        this.timeOut = timeOut;
    }
}
