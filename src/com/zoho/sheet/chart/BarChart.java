/*  $Id$ */

package com.zoho.sheet.chart;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;


  
public class Bar<PERSON>hart extends Chart 
{
	
	public BarChart(Workbook workbook, String chartId, String chartType, String sheetName, String dataRange, String seriesIn, boolean firstRowAsLabel, boolean firstColAsLabel, JSONObjectWrapper chartMetaInfo, boolean isPivotChart, String pivotId) throws Exception
	{
		super(workbook, chartId, chartType, sheetName, "bar", dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);						//No I18N
	}

	@Override
	public String getType() {
		return this.getMajorType();				//No I18N
	}
        
        @Override
        public  void constructChartOptions(Sheet sheet, JSONObjectWrapper chartOptions)
		{
            String title 		= 	chartOptions.has("title")    	? chartOptions.getString("title")    : "";
                        
			String subTitle 	= 	chartOptions.has("subTitle") 	? chartOptions.getString("subTitle") : "";
			int    legendPos	=  	chartOptions.has("lpos")   	? chartOptions.getInt("lpos")   : 1;
            boolean isFloating      =       chartOptions.has("isFloat")     ? chartOptions.getBoolean("isFloat") : false; 
            boolean isExpand    =       chartOptions.has("ae")         ? chartOptions.getBoolean("ae")     : false;           
			this.setChartObject(chartOptions);
			this.setCredits(false, null, null);
			this.setTitle(title);
			this.setSubTitle(subTitle);
			this.setToolTip(chartOptions);
			this.setAutoExpand(isExpand);
			this.setXAxis(chartOptions);
			this.setYAxis(chartOptions);
			this.setPlotOptions(chartOptions);
            this.setLegend(legendPos, isFloating);
            setSpecificProperties(sheet, chartOptions);
			//this.setSeries(sheet);
		}
    
    @Override
    public void setSpecificProperties(Sheet sheet, JSONObjectWrapper chartOptions)
    {
            this.chart.put("polar", false);
            this.plotOptions.getJSONObject("bar").put("borderWidth", 0);
            String     threshold       =   chartOptions.has("th") ? chartOptions.getString("th") :null ;
            this.plotOptions.getJSONObject("series").put("pointPadding", 0.07);
            this.chart.put("spacingLeft", 30);
            this.xAxis.getJSONObject("title").put("offset", 30);
            this.xAxis.put("reversed", true);
            if(this.getMajorType().contains("STACKED"))
            {
                String stackType = this.getMajorType().contains("PERCENT") ? "percent" : "normal"; //No I18N
                this.plotOptions.getJSONObject("bar").put("stacking", stackType);
                this.yAxis.getJSONObject(0).put("reversedStacks", false); 
            }
            if(chartOptions.has("tl") && chartOptions.has("tlType") && chartOptions.has("order"))
               {
                this.setTrendLineDetails(chartOptions.getBoolean("tl"),chartOptions.getString("tlType"),chartOptions.getInt("order")); //No I18N
               }
            if(this.getMajorType().contains("3D"))
            {
            	JSONObjectWrapper	json3D	=	new JSONObjectWrapper();
            	json3D.put("enabled", true);
            	json3D.put("alpha", 10);
            	json3D.put("beta", 5);
            	json3D.put("depth", 40);
            	this.chart.put("options3d", json3D);
                this.xAxis.put("gridLineWidth", 0);
            }
            if(this.getMajorType().contains("GROUPEDBAR")){
               this.chart.put("customChartType", "groupedChart"); //NO I18N
            }
            if(threshold != null && !(this.getMajorType().contains("GROUPEDBAR") || this.getMajorType().contains("STACKED"))){
                this.plotOptions.getJSONObject("series").put("threshold",     Float.parseFloat(threshold));
                this.plotOptions.getJSONObject("series").put("negativeColor", "#"+ChartConstants.CHART_THRESHOLD_COLOR);
                }
    }

//    @Override
//    public boolean isRange(Sheet sheet, String title, String forRange) {
//        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
//    }
}
