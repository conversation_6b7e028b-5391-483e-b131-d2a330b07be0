/*  $Id$ */

package com.zoho.sheet.chart;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;



public class Co<PERSON><PERSON>hart extends Chart 
{
	
	public boolean allPie = false;
	protected boolean isAllPie() {
		return allPie;
	}


	protected void setAllPie(List<String> comboChartTypesList, int length) {
		int count = Collections.frequency(comboChartTypesList, "PIE");	//No I18N
        count += Collections.frequency(comboChartTypesList, "DOUGHNUT");	//No I18N
        count += Collections.frequency(comboChartTypesList, "SEMIDOUGHNUT");	//No I18N
        if(count >= comboChartTypesList.size()-length) {
        	this.allPie = true;
        }
	}


	public ComboChart(Workbook workbook, String chartId, String majorType, String sheetName, String dataRange, String seriesIn, boolean firstRowAsLabel, boolean firstColAsLabel, JSONObjectWrapper chartMetaInfo, boolean isPivotChart, String pivotId) throws Exception
	{
		super(workbook, chartId, majorType, sheetName, "line", dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);						//No I18N
	}
	

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return this.getMajorType();										//No I18N
	}
        
        @Override
        public  void constructChartOptions(Sheet sheet, JSONObjectWrapper chartOptions)
		{
                    String title 		= 	chartOptions.has("title")    	? chartOptions.getString("title")    : "";
                        
			String subTitle 	= 	chartOptions.has("subTitle") 	? chartOptions.getString("subTitle") : "";
			int    legendPos	=  	chartOptions.has("lpos")   	? chartOptions.getInt("lpos")   : 1;
                        boolean isFloating      =       chartOptions.has("isFloat")     ? chartOptions.getBoolean("isFloat") : false; 
                        boolean isExpand    =       chartOptions.has("ae")         ? chartOptions.getBoolean("ae")     : false;
//                        if(this.getNoOfAxis()==1 && this.isSingleYAxis()) {
//                        	this.setNoOfAxis(chartOptions.has("noOfAxis")?chartOptions.getInt("noOfAxis"):calculateNoOfSeries());
//                        }
			this.setChartObject(chartOptions);
			this.setCredits(false, null, null);
			this.setTitle(title);
			this.setSubTitle(subTitle);
			this.setToolTip(chartOptions);
			this.setAutoExpand(isExpand);
			this.setXAxis(chartOptions);
			this.setYAxis(chartOptions);
			this.setPlotOptions(chartOptions);
                        this.setLegend(legendPos, isFloating);
                        setSpecificProperties(sheet, chartOptions);
                        
                        
			//this.setSeries(sheet);
		}

//    @Override
//    public void setLegend(int position, boolean isFloating) 
//    {
//                         this.legend		=		new JSONObjectWrapper();
//                         this.legend.put("floating", isFloating);
//                         int	yVal		=     0;
//                         if(position == 0)
//                         {
//                                 this.legend.put("enabled", false);
//                                 return;
//                         }
//
//                         //Bottom - default	
//                         String layout		=		"horizontal";										//No I18N
//                         String	halign		=		"center";											//No I18N
//                         String valign		=		"bottom";											//No I18N
//
//                         if(position == 2)			//Top
//                         {
//                                layout 		= 		"horizontal";										//No I18N
//                                halign 		= 		"center";											//No I18N
//                                valign 		= 		"top";												//No I18N
//                            boolean hasTitle		=	(!"".equals(this.title.getString("text")));
//                            boolean hasSubTitle		=	(!"".equals(this.subTitle.getString("text")));
//                        
//                           yVal					=	(hasTitle && hasSubTitle) ? 40 : (hasTitle || hasSubTitle) ? 25 : 0;
//               
//                         }
//                         else if(position == 3)                 //Right
//                        {
//                               layout 		= 		"vertical";											//No I18N	
//                               halign 		= 		"right";											//No I18N
//                               valign 		= 		"middle";											//No I18N
//                              
//                        }
//                        else if(position ==	4)              //Left
//                        {
//                               layout 		= 		"vertical";											//No I18N
//                               halign 		= 		"left";												//No I18N
//                               valign 		= 		"middle";											//No I18N		
//                              
//                        }
//                        else if(position ==  5) // top-right
//                        {
//                               layout 		= 		"vertical";											//No I18N	
//                               halign 		= 		"right";											//No I18N
//                               valign 		= 		"top";											//No I18N
//                               
//                        }
//
//                         this.legend.put("layout", 		layout);
//                         this.legend.put("align", 		halign);
//                         this.legend.put("verticalAlign", 	valign);
//                         this.legend.put("enabled", true);
//                         this.legend.put("y", 	yVal);  
//                         this.legend.put("itemStyle", getLegendItemStyleJson()); 
//    }
    @Override
    public void setSpecificProperties(Sheet sheet, JSONObjectWrapper chartOptions)
    {
    	if(chartOptions.has("tl") && chartOptions.has("tlType") && chartOptions.has("order"))
        {
         this.setTrendLineDetails(chartOptions.getBoolean("tl"),chartOptions.getString("tlType"),chartOptions.getInt("order")); //No I18N
        }
        
        String     threshold       =   chartOptions.has("th") ? chartOptions.getString("th") : null;
        if(threshold != null){
                this.plotOptions.getJSONObject("series").put("threshold",     Float.parseFloat(threshold));
                this.plotOptions.getJSONObject("series").put("negativeColor", "#"+ChartConstants.CHART_THRESHOLD_COLOR);
        }
        
           if(chartOptions.has("isMarker"))
            {
                 this.setMarker(chartOptions.getBoolean("isMarker"));
            }
           if(chartOptions.has("comboChartTypes")) {
        	   JSONArrayWrapper comboChartTypes = chartOptions.getJSONArray("comboChartTypes");
        	   String[] comboChartTypesArr = new String[comboChartTypes.length()];
        	   for(int i=0;i<comboChartTypes.length(); i++) {
        		   JSONObjectWrapper optionsTOSet = null;
        		   comboChartTypesArr[i] = comboChartTypes.getString(i);
        		   switch(comboChartTypes.getString(i)) {
        		   	case "COL":
        		   		optionsTOSet = new JSONObjectWrapper();
        		   		optionsTOSet.put("chartType", "column");
        		   		optionsTOSet.put("zIndex", "3");
        		   		optionsTOSet.put("borderWidth", 0);
        		   		break;
        		   	case "STACKEDCOL":
        		   		optionsTOSet = new JSONObjectWrapper();
        		   		optionsTOSet.put("chartType", "column");
        		   		optionsTOSet.put("zIndex", "3");
        		   		optionsTOSet.put("borderWidth", 0);
        		   		optionsTOSet.put("stacking", "normal");
       	    	   		break;
        	    	case "BAR":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "bar");
        		   		optionsTOSet.put("zIndex", "3");
        		   		optionsTOSet.put("borderWidth", 0);
        		   		break;
        	    	case "STACKEDBAR":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "bar");
        		   		optionsTOSet.put("zIndex", "3");
        		   		optionsTOSet.put("borderWidth", 0);
        		   		optionsTOSet.put("stacking", "normal");
       	    	   		break;
        	    	case "XYLINE":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "line");
        		   		optionsTOSet.put("zIndex", "4");
        		   		optionsTOSet.put("index", "0");
       	    	   		break;
        	    	case "STEPCHART":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "line");
        		   		optionsTOSet.put("zIndex", "4");
        		   		optionsTOSet.put("index", "0");
        		   		optionsTOSet.put("step", true);
       	    	   		break;
        	    	case "SPLINE":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "spline");
        		   		optionsTOSet.put("zIndex", "5");
       	    	   		break;
        	    	case "PIE":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "pie");
        		   		optionsTOSet.put("zIndex", "2");
        		   		optionsTOSet.put("size", "30%");
        		   		optionsTOSet.put("allowPointSelect", true);
        		   		optionsTOSet.put("borderWidth", 0);
        		   		optionsTOSet.put("borderColor", "#f0f0f0");
       	    	   		break;
        	    	case "DOUGHNUT":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "pie");
        		   		optionsTOSet.put("zIndex", "2");
        		   		optionsTOSet.put("size", "30%");
        		   		optionsTOSet.put("allowPointSelect", true);
        		   		optionsTOSet.put("borderWidth", 0);
        		   		optionsTOSet.put("borderColor", "#f0f0f0");
        		   		optionsTOSet.put("major", "doughnut");
       	    	   		break;
        	    	case "SEMIDOUGHNUT":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "pie");
        		   		optionsTOSet.put("zIndex", "2");
        		   		optionsTOSet.put("size", "30%");
        		   		optionsTOSet.put("allowPointSelect", true);
        		   		optionsTOSet.put("borderWidth", 0);
        		   		optionsTOSet.put("borderColor", "#f0f0f0");
        		   		optionsTOSet.put("major", "semidoughnut");
       	    	   		break;
        	    	case "XYAREA":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "area");
        		   		optionsTOSet.put("zIndex", "1");
        		   		break;
        	    	case "XYSTACKEDAREA":
        	    		optionsTOSet = new JSONObjectWrapper();
        	    		optionsTOSet.put("chartType", "area");
        		   		optionsTOSet.put("zIndex", "1");
        		   		optionsTOSet.put("stacking", "normal");
       	    	   		break;
        	    	default:
        	    		LOGGER.log(Level.INFO,"Entered Default case in combo chart type switch. Unknown chart type");
        	    	
        		   }
        		   if(optionsTOSet != null) {
        			   setChartPropertiesInPlotOptions(optionsTOSet);
        		   }
        	   }
        	   this.setComboChartTypes(comboChartTypesArr);
           } else{
        	   this.setComboChartTypes(new String[]{"COL"});	//No I18N
           }
           JSONArrayWrapper lineStyles = chartOptions.has("lineStyles")?ChartUtils.formatLineStylesToArray(chartOptions.get("lineStyles")):null;
           if(lineStyles == null) {
        	  lineStyles =  chartOptions.has("co")?(chartOptions.getJSONObject("co").has("lineStyles")?ChartUtils.formatLineStylesToArray(chartOptions.getJSONObject("co").get("lineStyles")):null):null;
           }
      	  this.setLineStyleForSeries(lineStyles);
    }
    protected void setChartPropertiesInPlotOptions(JSONObjectWrapper optionsToSet) {
    	String chartType = (optionsToSet != null)?optionsToSet.getString("chartType"):null;
    	JSONObjectWrapper chartTypeObject = null;
    	if(chartType != null) {
    		Iterator<String> keysIterator = optionsToSet.keys();
    		if(this.plotOptions.has(chartType)) {
        		chartTypeObject = this.plotOptions.getJSONObject(chartType);
        	} else{
        		chartTypeObject = new JSONObjectWrapper();
        	}
        	while(keysIterator.hasNext()) {
        		String key = keysIterator.next();
        		Object value = optionsToSet.get(key);
        		chartTypeObject.put(key, value);
        	}
        	this.plotOptions.put(chartType, chartTypeObject);
    	}
    }
    public void setChartTypesForComboChart(JSONObjectWrapper chartOptions) {
    	if(chartOptions != null) {
    		JSONObjectWrapper comboChartTypes = chartOptions.has("comboChartTypes")?chartOptions.getJSONObject("comboChartTypes"):null;
        	String[] comboChartTypesArr = null;
        	if(comboChartTypes != null){
        		comboChartTypesArr = new String[comboChartTypes.length()];
        		for(int i=0;i<comboChartTypes.length();i++) {
					String key = String.valueOf(i);
        			comboChartTypesArr[i]=comboChartTypes.has(key) ? comboChartTypes.getString(key) : "";		// NO I18N
        		}
        	} else {
        		comboChartTypesArr = new String[]{"COL"};	//No I18N
        	}
        	
     	   this.setComboChartTypes(comboChartTypesArr);
        	 JSONArrayWrapper lineStyles =  chartOptions.has("co")?(chartOptions.getJSONObject("co").has("lineStyles")?ChartUtils.formatLineStylesToArray(chartOptions.getJSONObject("co").get("lineStyles")):null):null;
     	  this.setLineStyleForSeries(lineStyles);
    	}
    	
    }

//    @Override
//    public boolean isRange(Sheet sheet, String title, String forRange) {
//        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
//    }
}
