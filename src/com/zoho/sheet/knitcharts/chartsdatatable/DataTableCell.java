package com.zoho.sheet.knitcharts.chartsdatatable;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import com.singularsys.jep.EvaluationException;
import com.zoho.sheet.knitcharts.constants.ChartConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.DataTableUtils;
import com.zoho.sheet.knitcharts.utils.DataUtils;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

/**
 * Data Table's fundamental building block
 * <AUTHOR>
 */
public class DataTableCell {
    /* value of the cell */
    private Value value;
    /* cell content type */
    private DataTableCellType type;
    /* cell content pattern */
    private ZSPattern pattern;
    /* function locale */
    private Locale locale;
    /* cell content */
    private String cellContent;
    /* currency symbol */
    private String currencySymbol;
    /* cell's url */
    private String url;
    /* Spread Sheet settings */
    private SpreadsheetSettings spreadsheetSettings;

    /**
     * Protected constructor for empty cell.
     */
    protected DataTableCell(){
    }

    public DataTableCell(Cell cell, SpreadsheetSettings spreadsheetSettings) {
        CellImpl cellImpl = (CellImpl) cell;
        value = cell.getValue();
        url = cellImpl.getLink();
        type = getType(cellImpl);
        pattern = cellImpl.getPattern(2);
        locale = cell.getFunctionLocale();
        cellContent = cell.getContent();
        currencySymbol = type == DataTableCellType.CURRENCY ? ChartUtils.requireNonNullElse(DataUtils.getCurrentSymbol(pattern, locale), "") : "";        // NO I18N
        this.spreadsheetSettings = spreadsheetSettings;
    }

    public DataTableCell(DataTableCell cell, Value value){
        this.value = value;
        type = cell.getType();
        pattern = cell.pattern;
        locale = cell.locale;
        currencySymbol = cell.getCurrencySymbol();
        spreadsheetSettings = cell.spreadsheetSettings;
    }

    public DataTableCell(Value value, Cell.Type type, ZSPattern pattern, Locale locale, SpreadsheetSettings spreadsheetSettings) {
        this.value = value;
        this.type = pattern == DataStyleConstants.EMPTY_PATTERN ? DataTableCellType.getInstance(type) :
                DataTableCellType.getInstance(pattern.getType());
        this.pattern = pattern;
        this.locale = locale;
        this.currencySymbol = DataUtils.getCurrentSymbol(pattern, locale);
        this.spreadsheetSettings = spreadsheetSettings;
    }

    private DataTableCellType getType(CellImpl cell) {
        if(cell.getLink() != null) { return DataTableCellType.URL; }
        Object value = cell.getValue().getValue();
        Cell.Type contentType = cell.getContentType();

        if(contentType == Cell.Type.FLOAT && value instanceof Number) {
            Number number = (Number) value;
            if(number.doubleValue() - number.intValue() == 0.0D) {
                int intValue = number.intValue();
                if(intValue <= 9999 && intValue >= 1000) {
                    return DataTableCellType.YEAR;
                }
            }
        } else if(contentType == Cell.Type.STRING && value instanceof String) {
            if(((String) value).isEmpty())  { return DataTableCellType.UNDEFINED; }
        }

        return DataTableCellType.getInstance(contentType);
    }

    public Value getValue() {
        return value;
    }

    public DataTableCellType getType() {
        return type;
    }

    public ZSPattern getPattern() {
        return pattern;
    }

    public Locale getLocale(){
        return locale;
    }

    public String getCurrencySymbol(){
        return currencySymbol;
    }

    public Date convertToDate() {
        try{
            return FunctionUtil.objectToDate(value.getValue());
        }catch (EvaluationException | NullPointerException | IllegalArgumentException e){
            return new Date(0);
        }
    }

    public Number convertToNumber() {
        try {
            return FunctionUtil.objectToNumber(value.getValue());
        } catch (EvaluationException e) {
            return 0;
        }
    }

    public String convertToString(Workbook workbook) {
        if(url != null) {
            return url;
        }if(cellContent == null) {
            return pattern.formatValue(workbook, value).getContent();
        }
        return cellContent;
    }

    public String formatDate() {
        Date date = convertToDate();
        try {
            boolean hasMilliSeconds = date.getTime() % 1000 != 0;
            SimpleDateFormat dateFormat = new SimpleDateFormat(ChartUtils.getDateFormat(type.getCellType(),
                    pattern, spreadsheetSettings, hasMilliSeconds));
            return dateFormat.format(date);
        }catch (NullPointerException | IllegalArgumentException e) {
            return date.toString();
        }
    }

    public boolean isEmptyCell(){
        return false;
    }

    public Value getValueForAggregation() {
        if(type != DataTableCellType.getInstance(value.getType())) {               // if cell type is not same as value type, then we have change to cell type before giving to aggregation engine
            if(type.isNumberType()) {
                return new ValueI(type.getCellType(), convertToNumber());
            } else if(type.isDateType()) {
                return new ValueI(type.getCellType(), convertToDate());
            } else {
                return new ValueI(Cell.Type.STRING, String.valueOf(value.getValue()));
            }
        }
        return value;
    }

    public String getValueObjectForHeader(Workbook workbook) {
        if(cellContent == null) {
            // execution will come to this branch when data table cell created from aggregation or Data table cell constructed from formula
            return pattern.formatValue(workbook, value).getContent();
        }
        return cellContent;
    }

    public Object getValueForLabel(Workbook workbook) {
        JSONObjectWrapper dataPoint = new JSONObjectWrapper();

        return dataPoint.put(ChartConstants.DataPoint.VALUE, getValueObjectForHeader(workbook));
    }

    public Object getValueObject(Workbook workbook) {
        JSONObjectWrapper dataPoint = new JSONObjectWrapper();

        if(type == DataTableCellType.PERCENTAGE) {
            double value = convertToNumber().doubleValue();

            dataPoint.put(ChartConstants.DataPoint.VALUE, (value * 100));
            dataPoint.put(ChartConstants.DataPoint.DECIMALS, DataTableUtils.getDecimalPlaces(workbook, getValidateDisplayValue()));
            dataPoint.put(ChartConstants.DataPoint.LABEL, convertToString(workbook));
        }else if(type.isNumberType()) {
            dataPoint.put(ChartConstants.DataPoint.VALUE, convertToNumber());
            dataPoint.put(ChartConstants.DataPoint.DECIMALS, DataTableUtils.getDecimalPlaces(workbook, getValidateDisplayValue()));
            dataPoint.put(ChartConstants.DataPoint.LABEL, convertToString(workbook));
        } else if(type.isDateType()) {
            dataPoint.put(ChartConstants.DataPoint.VALUE, formatDate());
            dataPoint.put(ChartConstants.DataPoint.LABEL, convertToString(workbook));
        } else {
            dataPoint.put(ChartConstants.DataPoint.VALUE, convertToString(workbook));
        }
        return dataPoint;
    }

    private Object getValidateDisplayValue() {
        if(Objects.isNull(cellContent)) {
            return value.getValue();
        } else {
            return cellContent;
        }
    }

    public SpreadsheetSettings getSpreadsheetSettings() {
        return spreadsheetSettings;
    }
}
