package com.zoho.sheet.knitcharts.chartsdatatable;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.DataJoinType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Column reader.
 * Used for reading the specified column from the list of references
 * <AUTHOR>
 */

public class ColumnReader {

    private final Matrix matrix;

    private List<List<DataTableCell>> data;

    private final int columnSize;

    public ColumnReader(Workbook workbook, SheetMeta sheetMeta, ChartType chartType,
                        List<Reference> references, int columnSize) {
        matrix = Matrix.getInstance(workbook, sheetMeta, chartType, references, true);
        this.columnSize = columnSize;
    }

    public List<DataTableCell> read(int columnIndex) {
        if(data == null) {
            data = matrix.getData();
        }
        if(columnIndex < 0 || columnIndex >= columnSize) { return CollectionsUtils.listOf(); }
        List<DataTableCell> column = new ArrayList<>();

        for(List<DataTableCell> row: data) {
            if(row.size() > columnIndex) {
                column.add(row.get(columnIndex));
            } else {
                column.add(new EmptyTableCell());
            }
        }

        return column;
    }
}
