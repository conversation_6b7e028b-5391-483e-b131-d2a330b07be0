package com.zoho.sheet.knitcharts.chartsdatatable;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.DataJoinType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Cache Data table pool must be used for caching original data for entire range.
 * Independent of any chart
 * <AUTHOR>
 */
public final class CacheDataTablePool {

    private final Map<String, CacheDataTable> cacheDataTableMap = new HashMap<>();

    public CacheDataTable getCacheDataTable(Workbook workbook, DataJoinType dataJoinType,
                                            List<String> dataSources, String asn, boolean skipHiddenCells) {
        String name = getTableName(dataJoinType, dataSources, asn, skipHiddenCells);
        CacheDataTable cacheDataTable = cacheDataTableMap.get(name);

        if (cacheDataTable == null) {
            cacheDataTable = new CacheDataTable(workbook, dataJoinType, dataSources, asn, skipHiddenCells);
            cacheDataTableMap.put(name, cacheDataTable);
        }

        return cacheDataTable;
    }


    private String getTableName(DataJoinType dataJoinType, List<String> dataSources,
                                String asn, boolean skipHiddenCells) {
        return String.format("%s%s%s%s", ChartUtils.join(dataSources, ";"), dataJoinType.getValue(),                 // NO I18N
                asn, skipHiddenCells);
    }
}
