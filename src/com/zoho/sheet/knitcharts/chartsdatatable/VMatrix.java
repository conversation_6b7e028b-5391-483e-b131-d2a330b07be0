package com.zoho.sheet.knitcharts.chartsdatatable;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.utils.DataTableUtils;

import java.util.List;

public class VMatrix extends Matrix {


    public VMatrix(Workbook workbook, SheetMeta sheetMeta, ChartType chartType, List<Reference> references, boolean isFullTableRequired) {
        super(workbook, sheetMeta, chartType, references, isFullTableRequired);
    }

    @Override
    protected void accumulateRows(List<List<DataTableCell>> accumulator, Reference reference) {
        List<List<DataTableCell>> rows = readRows(getRowIterator(reference));

        if(rows.isEmpty()) { return; }
        accumulator.addAll(rows);
        DataTableUtils.addColumnPadding(accumulator);

        if(SheetChartGetterAPI.getSeriesInType(getSheetMeta()) == SeriesInType.ROWS) {
            setReadSeriesCount(accumulator.size());
        } else {
            setReadSeriesCount(accumulator.get(0).size());
        }

    }
}
