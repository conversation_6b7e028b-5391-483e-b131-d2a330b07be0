package com.zoho.sheet.knitcharts.chartsdatatable;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.List;

public class RowLimitedRowIterator extends ReferenceRowIterator{

    private final int limit;

    private int rowRead = 0;

    public RowLimitedRowIterator(Workbook workbook, Reference reference, boolean skipHiddenCells, int limit) {
        super(workbook, reference, skipHiddenCells);
        this.limit = limit;
    }

    @Override
    public boolean hasNext() {
        return rowRead < limit && super.hasNext();
    }

    @Override
    public List<DataTableCell> next() {
        List<DataTableCell> row = super.next();
        rowRead++;

        return row;
    }
}
