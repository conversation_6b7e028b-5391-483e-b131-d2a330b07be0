package com.zoho.sheet.knitcharts.chartsdatatable;

import com.adventnet.zoho.websheet.model.Cell;

public enum DataTableCellType {

    /** Denotes a numeric value, with or without decimals */
    FLOAT,

    // Though a scientific number is also of type FLOAT, we need to identify it as a scientific number in order to set the appropriate Format.
    SCIENTIFIC,

    // Though a Fraction is also of type FLOAT, we need to identify it as a Fraction in order to set the appropriate Format.
    FRACTION,

    /** Denotes a percentage value, with or without decimals */
    PERCENTAGE,

    /** Denotes a currency value, with or without decimals */
    CURRENCY,

    /** Denotes a date */
    DATE,

    /** Denotes a time  */
    TIME,

    /** Denotes a DateTime value  */
    DATETIME,

    /** Denotes a text value, or a type of value derived from text */
    STRING,

    /** Denotes a boolean value, i.e. true or false */
    BOOLEAN,

    /** Denotes an error, e.g. a type mismatch */
    ERROR,

    URL,

    YEAR,

    /** Denotes a value of undefined type */
    UNDEFINED;


    public boolean isDateType() {
        return this == DATE
                || this == TIME
                || this == DATETIME;
    }

    public boolean isNumberType()
    {
        return this == FLOAT
                || this == YEAR
                || this == PERCENTAGE
                || this == CURRENCY
                || this == FRACTION
                || this == BOOLEAN
                || this == SCIENTIFIC;
    }

    public Cell.Type getCellType() {
        switch(this) {
            case YEAR:
            case FLOAT: {
                return Cell.Type.FLOAT;
            }
            case SCIENTIFIC: {
                return Cell.Type.SCIENTIFIC;
            }
            case FRACTION: {
                return Cell.Type.FRACTION;
            }
            case PERCENTAGE: {
                return Cell.Type.PERCENTAGE;
            }
            case CURRENCY: {
                return Cell.Type.CURRENCY;
            }
            case DATE: {
                return Cell.Type.DATE;
            }
            case TIME: {
                return Cell.Type.TIME;
            }
            case DATETIME: {
                return Cell.Type.DATETIME;
            }
            case STRING:
            case URL: {
                return Cell.Type.STRING;
            }
            case BOOLEAN: {
                return Cell.Type.BOOLEAN;
            }
            case ERROR: {
                return Cell.Type.ERROR;
            }
        }
        return Cell.Type.UNDEFINED;
    }

    public static DataTableCellType getInstance(Cell.Type cellType) {
        switch (cellType) {
            case FLOAT: {
                return FLOAT;
            }
            case SCIENTIFIC: {
                return SCIENTIFIC;
            }
            case FRACTION: {
                return FRACTION;
            }
            case PERCENTAGE: {
                return PERCENTAGE;
            }
            case CURRENCY: {
                return CURRENCY;
            }
            case DATE: {
                return DATE;
            }
            case TIME: {
                return TIME;
            }
            case DATETIME: {
                return DATETIME;
            }
            case STRING: {
                return STRING;
            }
            case BOOLEAN: {
                return BOOLEAN;
            }
            case ERROR: {
                return ERROR;
            }
        }
        return UNDEFINED;
    }


}
