package com.zoho.sheet.knitcharts.chartsdatatable;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.utils.ChartActivityMonitor;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.aggregation.ChartAggregationWrapper;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.DataMeta;
import com.zoho.sheet.knitcharts.metaconstructor.datameta.DataMetaConstructor;
import com.zoho.sheet.knitcharts.metaconstructor.sheetmeta.SheetMetaConstructor;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.reference.ReferencePool;
import com.zoho.sheet.knitcharts.reference.ReferenceType;
import com.zoho.sheet.knitcharts.reference.referrer.*;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.utils.DataTableUtils;
import com.zoho.sheet.knitcharts.utils.DataUtils;

import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Chart's Data Table
 * <AUTHOR>
 */
public class DataTable {

    private final List<String> dataSources;

    private final List<Reference> references;
    /* Original data cache */
    private SoftReference<List<List<DataTableCell>>> originalData;
    /* Manipulated data cache */
    private SoftReference<List<List<DataTableCell>>> manipulatedData;
    /* Cached Data meta */
    private SoftReference<DataMeta> dataMeta;
    /* Cached series data meta */
    private SoftReference<com.zoho.sheet.knitcharts.chart.sheetchart.meta.datameta.DataMeta> seriesDataMeta;

    private final int referenceID;

    /**
     * Constructs Data Table instance with the given references and Data Sources
     * @param references list of references
     * @param dataSources list of data sources<br>
     * NOTE: This constructor only be used from CacheDataTable
     * @see CacheDataTable
     */
    DataTable(List<Reference> references, List<String> dataSources, List<List<DataTableCell>> originalData) {
        this.references = references;
        this.dataSources = dataSources;
        cacheOriginalData(originalData);
        referenceID = 0;
    }

    /**
     * Default Constructor used widely
     * @param workbook Workbook instance
     * @param referenceID Chart's reference ID
     * @param dataSources Chart's Data Source
     */

    public DataTable(Workbook workbook, int referenceID, List<String> dataSources) {
        ReferencePool referencePool = workbook.getChartsContainer().getReferencePool();
        this.dataSources = new ArrayList<>();
        this.references = new ArrayList<>();
        this.referenceID = referenceID;

        for (int i = 0; i < dataSources.size(); i++) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.CHART_DATA_SOURCE, Key.getKey(i));
            Reference reference = referencePool.addReference(workbook, dataSources.get(i), referrer, true);

            if(reference.getReferenceType() == ReferenceType.INVALID) { continue; }

            this.references.add(reference);
            this.dataSources.add(reference.getReferenceName(workbook));
        }
    }

    /**
     * Constructor used only when data source update/chart creation
     * @param workbook Workbook instance
     * @param referenceID Chart's Reference ID
     * @param dataSources Data Sources
     * @param asn Associated sheet name
     */
    public DataTable(Workbook workbook, int referenceID, List<String> dataSources, String asn) {
        ReferencePool referencePool = workbook.getChartsContainer().getReferencePool();
        this.dataSources = new ArrayList<>();
        this.references = new ArrayList<>();
        this.referenceID = referenceID;

        for (int i = 0; i < dataSources.size(); i++) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.CHART_DATA_SOURCE, Key.getKey(i));
            Reference reference = referencePool.addReference(workbook, dataSources.get(i), referrer, asn, true);

            if(reference.getReferenceType() == ReferenceType.INVALID) { continue; }

            this.references.add(reference);
            this.dataSources.add(reference.getReferenceName(workbook));
        }
    }

    public List<String> getDataSources() {
        return dataSources;
    }

    private boolean isOriginalDataCacheAvailable() {
        return originalData != null && originalData.get() != null;
    }

    private boolean isManipulatedDataCacheAvailable() {
        return manipulatedData != null && manipulatedData.get() != null;
    }

    private void cacheOriginalData(List<List<DataTableCell>> originalData) {
        this.originalData = new SoftReference<>(originalData);
    }

    private void cacheManipulatedData(List<List<DataTableCell>> manipulatedData) {
        this.manipulatedData = new SoftReference<>(manipulatedData);
    }

    public synchronized List<List<DataTableCell>> getOriginalData(Workbook workbook, SheetMeta sheetMeta, ChartType chartType) {
        ChartActivityMonitor.ChartActivity chartActivity = ObjectsSupplier.getInstance().getChartActivityMonitor().getChartActivity(
                "DATA_TABLE_CONSTRUCTION", null, null, null, null                           // NO I18N
        );
        chartActivity.start();
        try {
            if(isOriginalDataCacheAvailable()) {
                return originalData.get();
            }

            Matrix matrix = Matrix.getInstance(workbook, sheetMeta, chartType, references, false);
            List<List<DataTableCell>> data = matrix.getData();

            data = DataUtils.isCompletelyEmpty(data) ? Collections.emptyList() : data;
            cacheOriginalData(data);
            chartActivity.completed();
            return data;
        } catch (Exception e) {
            chartActivity.failed(e);
            return Collections.emptyList();
        }
    }

    public synchronized List<List<DataTableCell>> getData(Workbook workbook, SheetMeta sheetMeta, ChartType chartType) {
        ChartActivityMonitor.ChartActivity chartActivity = ObjectsSupplier.getInstance().getChartActivityMonitor().getChartActivity(
                "DATA_TABLE_CONSTRUCTION", null, null, null, null                           // NO I18N
        );
        try {
            if(isManipulatedDataCacheAvailable()) {
                return manipulatedData.get();
            }

            List<List<DataTableCell>> tableCells = getOriginalData(workbook, sheetMeta, chartType);

            SeriesInType seriesInType = SheetChartAPI.getSeriesIn(sheetMeta);
            boolean fcl = SheetChartAPI.getFirstColLabel(sheetMeta);
            boolean frl = SheetChartAPI.getFirstRowLabel(sheetMeta);
            int maxSeriesCount = DataUtils.getMaxSeriesCount(chartType, seriesInType == SeriesInType.COLUMNS ? fcl : frl);

            if(!tableCells.isEmpty() && SheetChartAPI.isDataManipulationPossible(sheetMeta) && chartType != ChartType.WORD_CLOUD){
                ChartAggregationWrapper wrapper = new ChartAggregationWrapper(workbook, sheetMeta, tableCells);
                List<List<DataTableCell>> aggregatedCells = wrapper.execute();

                List<List<DataTableCell>> result = DataTableUtils.filterExcessData(sheetMeta, chartType, seriesInType, maxSeriesCount, aggregatedCells, false);
                cacheManipulatedData(result);

                return result;
            }else{
                return DataTableUtils.filterExcessData(sheetMeta, chartType, seriesInType, maxSeriesCount, tableCells, false);
            }
        } catch (Exception e) {
            chartActivity.failed(e);
            return Collections.emptyList();
        }
    }

    public int getReferenceID() {
        return referenceID;
    }

    public List<Reference> getReferences() {
        return references;
    }

    public void updateDataSources(Workbook workbook, List<String> updateDataSources) {
        ReferencePool referencePool = workbook.getChartsContainer().getReferencePool();

        for(int idx = 0; idx < dataSources.size(); idx++) {
            referencePool.removeReference(new KeyReferrer(referenceID, ReferrerType.CHART_DATA_SOURCE, Key.getKey(idx)));
        }

        dataSources.clear();
        references.clear();

        for(int idx = 0; idx < updateDataSources.size(); idx++) {
            Reference reference = referencePool.addReference(workbook, updateDataSources.get(idx),
                    new KeyReferrer(referenceID, ReferrerType.CHART_DATA_SOURCE, Key.getKey(idx)), true);

            if(reference.getReferenceType() == ReferenceType.INVALID) { continue; }
            dataSources.add(reference.getReferenceName(workbook));
            references.add(reference);
        }
    }

    public void updateDataSources(Workbook workbook, List<String> updateDataSources, String asn) {
        ReferencePool referencePool = workbook.getChartsContainer().getReferencePool();

        for(int idx = 0; idx < dataSources.size(); idx++) {
            referencePool.removeReference(new KeyReferrer(referenceID, ReferrerType.CHART_DATA_SOURCE, Key.getKey(idx)));
        }

        dataSources.clear();
        references.clear();

        for(int idx = 0; idx < updateDataSources.size(); idx++) {
            Reference reference = referencePool.addReference(workbook, updateDataSources.get(idx),
                    new KeyReferrer(referenceID, ReferrerType.CHART_DATA_SOURCE, Key.getKey(idx)), asn, true);

            if(reference.getReferenceType() == ReferenceType.INVALID) { continue; }
            dataSources.add(reference.getReferenceName(workbook));
            references.add(reference);
        }
    }

    public synchronized DataMeta constructDataMeta(Workbook workbook, SheetMeta sheetMeta, ChartType chartType) {
        if(isDataMetaCacheAvailable()) { return dataMeta.get(); }
        DataMeta dataMeta = new DataMeta();

        DataMetaConstructor.getConstructor(sheetMeta, chartType)
                .constructDataMeta(dataMeta, this, sheetMeta, workbook, chartType);

        cacheDataMeta(dataMeta);
        return dataMeta;
    }

    public synchronized com.zoho.sheet.knitcharts.chart.sheetchart.meta.datameta.DataMeta constructSeriesDataMeta(Workbook workbook, SheetMeta sheetMeta, ChartType chartType) {
        if(isSeriesDataMetaCacheAvailable()) { return seriesDataMeta.get(); }
        com.zoho.sheet.knitcharts.chart.sheetchart.meta.datameta.DataMeta dataMeta =
                SheetMetaConstructor.constructDataMeta(workbook, sheetMeta, this, chartType);

        cacheSeriesDataMeta(dataMeta);
        return dataMeta;
    }

    private void cacheDataMeta(DataMeta dataMeta) {
        this.dataMeta = new SoftReference<>(dataMeta);
    }

    private void cacheSeriesDataMeta(com.zoho.sheet.knitcharts.chart.sheetchart.meta.datameta.DataMeta dataMeta) {
        this.seriesDataMeta = new SoftReference<>(dataMeta);
    }

    private boolean isDataMetaCacheAvailable() {
        return dataMeta != null && dataMeta.get() != null;
    }

    private boolean isSeriesDataMetaCacheAvailable() {
        return seriesDataMeta != null && seriesDataMeta.get() != null;
    }

    public void invalidateOriginalDataCache() {
        originalData = null;
    }

    public void invalidateManipulatedDataCache() {
        manipulatedData = null;
    }

    public void invalidateDataMetaCache() {
        dataMeta = null;
    }

    public void invalidateSeriesDataMetaCache() {
        seriesDataMeta = null;
    }
}
