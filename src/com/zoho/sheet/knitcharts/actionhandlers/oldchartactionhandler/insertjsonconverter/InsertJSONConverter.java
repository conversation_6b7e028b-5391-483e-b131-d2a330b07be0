package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.insertjsonconverter;

import com.adventnet.zoho.websheet.model.Workbook;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.DataSack;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.PivotChartSheetMeta;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ChartException;
import com.zoho.sheet.knitcharts.chart.ProtoChart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.io.IOException;

import static com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants.JSONConstant.*;

/**
 * Converter for converting old chart insert action JSONWrapper to new SheetMeta and FrameworkMeta
 * <AUTHOR>
 */
public class InsertJSONConverter {

    private static OldChartInsertJSON convertToOldJSONObject(String oldChartJSON){
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(oldChartJSON, OldChartInsertJSON.class);
        }catch (IOException e){
            throw new ChartException(e.getMessage());
        }
    }

    /**
     * Converts necessary data from old chart insert action
     * @param insertJSON Old chart insert action JSONWrapper
     * @param protoChart Proto Chart Object
     */
    public static void convertInsertChartJSON(Workbook workbook, JSONObjectWrapper insertJSON, ProtoChart protoChart){
        OldChartInsertJSON oldChartInsertJSON = convertToOldJSONObject(insertJSON.toString());
        DataSack dataSack = new DataSack();
        ChartMeta chartMeta = new ChartMeta();
        SheetMeta sheetMeta = getSheetMeta(insertJSON);
        SheetMetaConverter.convert(workbook, sheetMeta, oldChartInsertJSON,
                insertJSON.optString(OldChartActionConstants.JSONConstant.CHART_TYPE), protoChart.sheetName, dataSack);
        FrameworkMetaConverter.convert(oldChartInsertJSON, chartMeta, dataSack.get(DataSack.Data.SERIES_COUNT));
        protoChart.chartMeta = chartMeta;
        protoChart.sheetMeta = sheetMeta;
    }

    private static SheetMeta getSheetMeta(JSONObjectWrapper insertJSON) {
        if(insertJSON.has(IS_PIVOT_CHART) && insertJSON.getBoolean(IS_PIVOT_CHART)) {
            return new PivotChartSheetMeta();
        }
        return new SheetMeta();
    }

}
