package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.insertjsonconverter;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font.DefaultFontJSONConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font.Font;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.*;
import com.zoho.sheet.knitcharts.dataconverter.secondgen.OldColourThemes;
import com.zoho.sheet.knitcharts.miscellaneous.StateFullLambda;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Tuple;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.*;

/**
 * Framework chart meta converter for old chart insert action
 * <AUTHOR>
 */
public class FrameworkMetaConverter {

    static class ChartDataConverter {
        static void convert(OldChartInsertJSON oldChartInsertJSON, ChartMeta chartMeta) {
            OldChartInsertJSON.ActionList actionList = oldChartInsertJSON.getActionList();
            OldChartInsertJSON.ActionList.Chart chart = actionList.getChart();
            OldChartInsertJSON.ActionList.ColorTheme colorTheme = actionList.getColorTheme();
            FrameworkChartAPI.updateChartType(chartMeta, ChartUtils.getChartForOldChartName(oldChartInsertJSON.chartType));
            FrameworkChartAPI.updateChartFontFamily(chartMeta, chart.getStyle().fontFamily);
            FrameworkChartAPI.updateChartAxisInvertedStatus(chartMeta, chart.inverted);
            FrameworkChartAPI.updateAnimationStatus(chartMeta, oldChartInsertJSON.animation);
            FrameworkChartAPI.updateChartGradientStatus(chartMeta, actionList.getGradient().enabled);
            Object baseColor = colorTheme.baseColor;

            if(baseColor instanceof Collections) {
                FrameworkChartAPI.setChartColors(chartMeta, ChartUtils.typeCast(baseColor));
            } else if(baseColor instanceof JSONArrayWrapper) {
                FrameworkChartAPI.setChartColors(chartMeta, ChartUtils.JSONArrayToList(ChartUtils.typeCast(baseColor)));
            } else if(colorTheme.name != null) {
                FrameworkChartAPI.setChartColors(chartMeta, OldColourThemes.getOldThemeColors(colorTheme.name));
            }

            convertBackgroundColor(chartMeta, chart.bgColor);
            FrameworkChartAPI.updateChartBorderColor(chartMeta, chart.borderColor);
            FrameworkChartAPI.updateTooltipStatus(chartMeta, oldChartInsertJSON.tooltip);
        }

        private static void convertBackgroundColor(ChartMeta chartMeta, String color) {
            if(color == null) { return; }
            Tuple<String, Double> colorNOpacity = ChartUtils.splitColorAndOpacity(color);

            FrameworkChartAPI.updateChartBGColor(chartMeta, colorNOpacity.getFirstParam());
            FrameworkChartAPI.updateChartBackgroundOpacity(chartMeta, colorNOpacity.getSecondParam());
        }
    }


    private static class TitleConverter {

        public static void convert(OldChartInsertJSON insertJSON, ChartMeta chartMeta) {
            String title = ChartUtils.doubleDecode(insertJSON.title);
            Font font = new Font(new DefaultFontJSONConstants());
            OldChartInsertJSON.TextStyle style = insertJSON.getChartTitle().getStyle();

            font.setFontColor(insertJSON.getDefaultColorsState().titleColorState ? null : style.color);
            font.setFontSize(style.fontSize);
            font.setFontStyle(style.fontStyle);
            font.setFontWeight(style.fontWeight);
            FrameworkChartAPI.updateChartTitleStatus(chartMeta, ChartUtils.isNonEmptyString(title));
            FrameworkChartAPI.updateChartTitle(chartMeta, title);
            FrameworkChartAPI.updateChartTitleFont(chartMeta, font);
            FrameworkChartAPI.updateChartTitleHAlign(chartMeta, insertJSON.titleAlign);
        }

    }

    private static class SubtitleConverter {
        public static void convert(OldChartInsertJSON insertJSON, ChartMeta chartMeta) {
            String subtitle = ChartUtils.doubleDecode(insertJSON.subTitle);
            Font font = new Font(new DefaultFontJSONConstants());
            OldChartInsertJSON.TextStyle style = insertJSON.getChartSubTitle().getStyle();

            font.setFontColor(insertJSON.getDefaultColorsState().subtitleColorState ? null : style.color);
            font.setFontSize(style.fontSize);
            font.setFontStyle(style.fontStyle);
            font.setFontWeight(style.fontWeight);
            FrameworkChartAPI.updateChartSubTitleStatus(chartMeta, ChartUtils.isNonEmptyString(subtitle));
            FrameworkChartAPI.updateChartSubTitle(chartMeta, subtitle);
            FrameworkChartAPI.updateChartSubtitleFont(chartMeta, font);
            FrameworkChartAPI.updateChartSubTitleHAlign(chartMeta, insertJSON.subTitleAlign);
        }
    }

    static class SeriesPropsDataConverter {

        static void convert(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON, int seriesCount){
            convertDataLabels(chartMeta, oldChartInsertJSON);
            convertTrendLines(chartMeta, oldChartInsertJSON, seriesCount);
            convertDataProperties(chartMeta, oldChartInsertJSON);
            convertMarkers(chartMeta, oldChartInsertJSON);
            convertThreshold(chartMeta, oldChartInsertJSON);
            convertAreaOpacityAndLineType(chartMeta, oldChartInsertJSON);
            convertSortData(chartMeta, oldChartInsertJSON);
            convertSeriesColors(chartMeta, oldChartInsertJSON, seriesCount);
            convertBorderColors(chartMeta, oldChartInsertJSON);
            convertSpecialChartTypesData(chartMeta, oldChartInsertJSON);
            convertRaceChartOptions(chartMeta, oldChartInsertJSON);
            convertBoxPlotOptions(chartMeta, oldChartInsertJSON);
            convertMultipleYAxisOption(chartMeta, oldChartInsertJSON, seriesCount);
            convertRelationParing(chartMeta);
        }

        /**
         * Setting series relation to Single so that it matches Old Chart Behaviour
         * @param chartMeta ChartMeta instance
         */
        private static void convertRelationParing(ChartMeta chartMeta){
            ChartType chartType = FrameworkChartAPI.getChartType(chartMeta);

            if(chartType == ChartType.SCATTER || chartType == ChartType.SCATTER_LINE || chartType == ChartType.SCATTER_LINE_MARKERS) {
                FrameworkChartAPI.updateSeriesRelation(chartMeta, SeriesRelationType.SINGLE);
            }
        }

        private static void convertRaceChartOptions(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            // converting race chart data
            OldChartInsertJSON.RaceChartOptions raceChartOptions = oldChartInsertJSON.getRaceChartOptions();

            FrameworkChartAPI.updateSharedSeriesCumulateStatus(chartMeta, raceChartOptions.cumulate);
            FrameworkChartAPI.updateSharedSeriesDecimals(chartMeta, raceChartOptions.allowDecimals);
            FrameworkChartAPI.updateSortOrder(chartMeta, StateFullLambda.retrieveSortOrderType(raceChartOptions.order),0);
            FrameworkChartAPI.updateXAxisMaxWithIndex(chartMeta, raceChartOptions.count, 0);
            FrameworkChartAPI.updateAnimationDuration(chartMeta, raceChartOptions.duration);
        }

        private static void convertMultipleYAxisOption(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON, int seriesCount) {
            if(oldChartInsertJSON.isSingleYaxis == null || !oldChartInsertJSON.isSingleYaxis){ return; }

            for(int seriesIndex = 0; seriesIndex < seriesCount; seriesIndex++){
                if(seriesIndex == 0) {
                    FrameworkChartAPI.updateYAxisType(chartMeta, YAxisType.PRIMARY, seriesIndex);
                }else {
                    FrameworkChartAPI.updateYAxisType(chartMeta, YAxisType.SECONDARY, seriesIndex);
                }
            }
        }

        private static void convertBoxPlotOptions(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            OldChartInsertJSON.BoxPlotOptions boxPlotOptions = oldChartInsertJSON.getBoxPlotOptions();
            List<Boolean> showMean = boxPlotOptions.getShowMean();
            List<Boolean> showOutliers = boxPlotOptions.getShowOutliers();
            List<String> meanColor = boxPlotOptions.getMeanColor();
            List<String> outliersColor = boxPlotOptions.getOutliersColor();
            List<String> medianColor = boxPlotOptions.getMedianColor();
            List<String> whiskersColor = boxPlotOptions.getWhiskersColor();

            for(int i=0; i<showMean.size(); i++){
                FrameworkChartAPI.updateMeanMarkerStatus(chartMeta, showMean.get(i), i);
            }

            for(int i=0; i<showOutliers.size(); i++){
                FrameworkChartAPI.updateOutlierStatus(chartMeta, showOutliers.get(i), i);
            }

            for(int i=0; i<medianColor.size(); i++){
                FrameworkChartAPI.updateMedianColor(chartMeta, medianColor.get(i), i);
            }

            for(int i=0; i<whiskersColor.size(); i++){
                FrameworkChartAPI.updateWhiskersColor(chartMeta, whiskersColor.get(i), i);
            }

            for(int i=0; i<meanColor.size(); i++){
                FrameworkChartAPI.updateMeanColor(chartMeta, meanColor.get(i), i);
            }

            for(int i=0; i<outliersColor.size(); i++){
                FrameworkChartAPI.updateOutliersColor(chartMeta, outliersColor.get(i), i);
            }

            FrameworkChartAPI.updateGroupHeaders(chartMeta, boxPlotOptions.groupHeaders);
        }

        private static void convertDataLabels(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON) {
            OldChartInsertJSON.TextStyle dataLabels = oldChartInsertJSON.getActionList().getColorObject().getDataLabels();
            Font font = new Font(new DefaultFontJSONConstants());

            font.setFontColor(oldChartInsertJSON.getDefaultColorsState().dataLabelsColorState ? null : dataLabels.color);
            font.setFontSize(dataLabels.fontSize);
            font.setFontStyle(dataLabels.fontStyle);
            font.setFontWeight(dataLabels.fontWeight);
            FrameworkChartAPI.updateSharedSeriesDataLabelsStatus(chartMeta, !Objects.equals("N", oldChartInsertJSON.labelFormat));          // NO I18N
            FrameworkChartAPI.addSharedSeriesDataLabelsFormats(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrieveDataLabelFormat, oldChartInsertJSON.labelFormat));
            FrameworkChartAPI.updateSharedSeriesDataLabelsPosition(chartMeta, ChartUtils.produceIfNotNull(ChartUtils::getNewDataLabelsPosition, oldChartInsertJSON.labelPosition));
            FrameworkChartAPI.updateSharedSeriesDataLabelsFont(chartMeta, font);
        }

        private static void convertTrendLines(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON, int seriesCount){
            List<OldChartInsertJSON.ActionList.ColorObject.SeriesColor> seriesColors = oldChartInsertJSON.getActionList().getColorObject().getSeriesColors();
            List<OldChartInsertJSON.LineStyle> lineStyles = oldChartInsertJSON.getLineStyles();
            for(OldChartInsertJSON.LineStyle lineStyle : lineStyles){
                if(lineStyle == null || lineStyle.seriesIndex < seriesCount) { continue; }
                int seriesIndex = seriesCount - lineStyle.seriesIndex;
                FrameworkChartAPI.updateTrendLineStyleTypeWithIndex(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrievLineStyleType, lineStyle.lineStyleType), seriesIndex);
            }
            for(OldChartInsertJSON.ActionList.ColorObject.SeriesColor seriesColor : seriesColors){
                Integer oldSeriesIndex = ChartUtils.safeParseToInteger(Objects.toString(seriesColor.seriesIndex));
                if(oldSeriesIndex == null || oldSeriesIndex < seriesCount) { continue; }
                Tuple<String, Double> colorNOpacity = ChartUtils.splitColorAndOpacity(seriesColor.color);
                int seriesIndex = seriesCount - oldSeriesIndex;


                FrameworkChartAPI.updateTrendLineColorWithIndex(chartMeta, colorNOpacity.getFirstParam(), seriesIndex);
                FrameworkChartAPI.updateTrendLineColorOpacity(chartMeta, colorNOpacity.getSecondParam(), seriesIndex);
            }
            FrameworkChartAPI.updateSharedSeriesTrendLineStatus(chartMeta, getTrendlineStatus(oldChartInsertJSON));
            FrameworkChartAPI.updateSharedSeriesTrendLineType(chartMeta, ChartUtils.produceIfNotNull(ChartUtils::getNewTrendLineType, oldChartInsertJSON.trendlineType));
            FrameworkChartAPI.updateSharedSeriesTrendLineOrder(chartMeta, oldChartInsertJSON.degree);
        }

        private static boolean getTrendlineStatus(OldChartInsertJSON oldChartInsertJSON){
            boolean status = oldChartInsertJSON.trendline != null && oldChartInsertJSON.trendline;
            status = status && !(Objects.equals(oldChartInsertJSON.trendlineType, "none"));       // NO I18N

            return status;
        }

        private static void convertDataProperties(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            List<OldChartInsertJSON.ActionList.ColorObject.PointColor> pointColors = oldChartInsertJSON.getActionList().getColorObject().getPointColors();

            for(OldChartInsertJSON.ActionList.ColorObject.PointColor pointColor: pointColors){
                List<OldChartInsertJSON.ActionList.ColorObject.PointColor.Point> points = pointColor.getPoints();

                for(OldChartInsertJSON.ActionList.ColorObject.PointColor.Point point: points){
                    Tuple<String, Double> colorNOpacity = ChartUtils.splitColorAndOpacity(point.color);

                    FrameworkChartAPI.updateSeriesDataPropertyColor(chartMeta, colorNOpacity.getFirstParam(), pointColor.seriesIndex, point.pointIndex);
                    FrameworkChartAPI.updateSeriesDataPropertyOpacity(chartMeta, colorNOpacity.getSecondParam(), pointColor.seriesIndex, point.pointIndex);
                }
            }
        }

        private static void convertMarkers(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            List<OldChartInsertJSON.MarkerObject> markerObjects = oldChartInsertJSON.getMarkerObjects();

            for(OldChartInsertJSON.MarkerObject markerObject : markerObjects){
                int seriesIndex = markerObject.seriesIndex;

                FrameworkChartAPI.updateSeriesMarkerStatusWithIndex(chartMeta, markerObject.enabled, seriesIndex);
                FrameworkChartAPI.updateSeriesMarkerBorderColorWithIndex(chartMeta, markerObject.borderColor, seriesIndex);
                FrameworkChartAPI.updateSeriesMarkerColorWithIndex(chartMeta, markerObject.fillColor, seriesIndex);
                FrameworkChartAPI.updateSeriesMarkerShapeWithIndex(chartMeta, ChartUtils.produceIfNotNull(ChartUtils::getNewMarkerShapeType, markerObject.symbol), seriesIndex);
                FrameworkChartAPI.updateSeriesMarkerSizeWithIndex(chartMeta, markerObject.size, seriesIndex);
                FrameworkChartAPI.updateSeriesMarkerStatusWithIndex(chartMeta, markerObject.enabled, seriesIndex);
            }
        }

        private static void convertThreshold(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            FrameworkChartAPI.updateSharedSeriesThreshold(chartMeta, oldChartInsertJSON.threshold);
            FrameworkChartAPI.updateSharedSeriesThresholdColor(chartMeta, oldChartInsertJSON.getActionList().getColorObject().thresholdColor);
        }

        private static void convertAreaOpacityAndLineType(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            List<OldChartInsertJSON.SeriesProps.AreaOpacity> areaOpacities = oldChartInsertJSON.getSeriesProps().getAreaOpacity();
            List<OldChartInsertJSON.SeriesProps.LineType> lineTypes = oldChartInsertJSON.getSeriesProps().getLineStyle();

            for(OldChartInsertJSON.SeriesProps.AreaOpacity areaOpacity: areaOpacities){
                FrameworkChartAPI.updateSeriesAreaOpacityWithIndex(chartMeta, areaOpacity.opacity, areaOpacity.seriesIndex);
            }

            for(OldChartInsertJSON.SeriesProps.LineType lineType: lineTypes){
                FrameworkChartAPI.updateSeriesLineTypeWithIndex(chartMeta, LineStyleType.retrieveByValue(lineType.type), lineType.seriesIndex);
            }
        }

        private static void convertSortData(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            ChartType chartType = FrameworkChartAPI.getChartType(chartMeta);
            FrameworkChartAPI.updateSortStatus(chartMeta, oldChartInsertJSON.isDataSorted || chartType == ChartType.RACE_BAR, 0);
            FrameworkChartAPI.updateSortOrder(chartMeta, ChartUtils.produceIfNotNullAndEmpty(StateFullLambda::retrieveSortOrderType, oldChartInsertJSON.sortingOrder), 0);
            FrameworkChartAPI.updateSortKey(chartMeta, ChartUtils.produceIfNotNullAndEmpty(StateFullLambda::retrieveSortByKey, oldChartInsertJSON.sortByKey), 0);
        }

        private static void convertSeriesColors(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON, int seriesCount){
            if(ChartUtils.isSingleSeriesChart(FrameworkChartAPI.getChartType(chartMeta)))  {
                convertSingleSeriesColors(chartMeta, oldChartInsertJSON);
                return;
            }
            List<OldChartInsertJSON.ActionList.ColorObject.SeriesColor> seriesColors = oldChartInsertJSON.getActionList().getColorObject().getSeriesColors();

            for(OldChartInsertJSON.ActionList.ColorObject.SeriesColor seriesColor : seriesColors){
                Integer seriesIndex = ChartUtils.safeParseToInteger(Objects.toString(seriesColor.seriesIndex));
                if(seriesIndex == null || seriesIndex >= seriesCount) { continue; }
                Tuple<String, Double> colorWithOpacity = ChartUtils.splitColorAndOpacity(seriesColor.color);
                FrameworkChartAPI.updateSeriesColorWithIndex(chartMeta, colorWithOpacity.getFirstParam(), seriesIndex);
                FrameworkChartAPI.updateSeriesColorOpacity(chartMeta, colorWithOpacity.getSecondParam(), seriesIndex);
                // In old charts target color is last series color
                if(seriesIndex == seriesCount - 1){ FrameworkChartAPI.updateSharedSeriesTargetColor(chartMeta, seriesColor.color); }
            }
        }

        private static void convertSingleSeriesColors(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON) {
            List<OldChartInsertJSON.ActionList.ColorObject.SeriesColor> seriesColors = oldChartInsertJSON.getActionList().getColorObject().getSeriesColors();

            for(OldChartInsertJSON.ActionList.ColorObject.SeriesColor seriesColor : seriesColors){
                Tuple<String, Double> colorWithOpacity = ChartUtils.splitColorAndOpacity(seriesColor.color);
                Integer dataPointIndex = ChartUtils.safeParseToInteger(Objects.toString(seriesColor.seriesIndex));
                if(dataPointIndex == null) { continue; }

                FrameworkChartAPI.updateSeriesDataPropertyColor(chartMeta, colorWithOpacity.getFirstParam(), 0, dataPointIndex);
                FrameworkChartAPI.updateSeriesDataPropertyOpacity(chartMeta, colorWithOpacity.getSecondParam(), 0, dataPointIndex);
            }
        }

        private static void convertBorderColors(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            List<OldChartInsertJSON.BorderObject> borderObjects = oldChartInsertJSON.getBorderObjects();

            for(OldChartInsertJSON.BorderObject borderObject : borderObjects){
                FrameworkChartAPI.updateSeriesBorderColorWithIndex(chartMeta, getBorderColor(borderObject.borderColor), borderObject.seriesIndex);
            }
        }

        /**
         * Method to get corrected border colour. Old chart meta may contain transparent, DEFAULT_BORDER as border colours
         * @param color Border color
         * @return Corrected border colour.
         */
        private static String getBorderColor(String color){
            if(color == null || color.isEmpty() || Objects.equals(color, "transparent")) { return null; }               // NO I18N

            if(color.equals("DEFAULT_BORDER")) { return "auto"; }           // NO I18N

            return color;
        }

        private static void convertSpecialChartTypesData(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            ChartType chartType = FrameworkChartAPI.getChartType(chartMeta);

            if(chartType == ChartType.PIE || chartType == ChartType.SEMI_PIE || chartType == ChartType.DONUT || chartType == ChartType.SEMI_DONUT) {
                // converting pie chart data
                FrameworkChartAPI.updateSharedSeriesStartAngle(chartMeta, oldChartInsertJSON.sliceStartAngle);
            } else {
                // converting parliament chart data
                FrameworkChartAPI.updateSharedSeriesStartAngle(chartMeta, oldChartInsertJSON.startAngle);
            }
            FrameworkChartAPI.updateSharedSeriesEndAngle(chartMeta, oldChartInsertJSON.endAngle);
        }
    }

    static class CaptionDataConverter {
        static void convert(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            OldChartInsertJSON.TextStyle captionStyle = oldChartInsertJSON.getCaptionStyle();
            Font font = new Font(new DefaultFontJSONConstants());

            font.setFontColor(oldChartInsertJSON.getDefaultColorsState().captionColorState ? null : captionStyle.color);
            font.setFontStyle(captionStyle.fontStyle);
            font.setFontSize(captionStyle.fontSize);
            font.setFontWeight(captionStyle.fontWeight);
            FrameworkChartAPI.updateCaptionFontStyle(chartMeta, font);
        }
    }

    static class DonutTotalDatalabelsConverter{
        static void convert(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            OldChartInsertJSON.TextStyle captionStyle = oldChartInsertJSON.getCaptionStyle();
            Font font = new Font(new DefaultFontJSONConstants());
            ChartType chartType = FrameworkChartGetterAPI.getChartType(chartMeta);

            font.setFontColor(captionStyle.color);
            font.setFontStyle(captionStyle.fontStyle);
            font.setFontSize(captionStyle.fontSize);
            font.setFontWeight(captionStyle.fontWeight);
            FrameworkChartAPI.updateTotalDatalabelsFont(chartMeta, font);
            convertTotalDatalabelsStatus(chartMeta, chartType, oldChartInsertJSON);
        }

        private static void convertTotalDatalabelsStatus(ChartMeta chartMeta, ChartType chartType, OldChartInsertJSON oldChartInsertJSON) {
            boolean status = chartType == ChartType.DONUT || chartType == ChartType.SEMI_DONUT || chartType == ChartType.DONUT_PARLIAMENT;

            FrameworkChartAPI.updateDonutTotalDatalabelsStatus(chartMeta, status && Objects.requireNonNullElse(oldChartInsertJSON.dataLabelTotal, false));
        }
    }

    static class LegendDataConverter {
        static void convert(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            OldChartInsertJSON.TextStyle legendStyle = oldChartInsertJSON.getLegendStyle();
            Font font = new Font(new DefaultFontJSONConstants());

            font.setFontColor(oldChartInsertJSON.getDefaultColorsState().legendColorState ? null : legendStyle.color);
            font.setFontStyle(legendStyle.fontStyle);
            font.setFontSize(legendStyle.fontSize);
            font.setFontWeight(legendStyle.fontWeight);
            FrameworkChartAPI.updateLegendStatus(chartMeta, getLegendStatusType(oldChartInsertJSON.legendPosition));
            FrameworkChartAPI.updateChartLegendPosition(chartMeta, getLegendPositionType(oldChartInsertJSON.legendPosition));
            FrameworkChartAPI.updateChartLegendFont(chartMeta, font);
        }

        static LegendStatusType getLegendStatusType(Integer position) {
            if(position == null){ return null; }
            return position == 0 ? LegendStatusType.OFF : LegendStatusType.ON;
        }

        static LegendPositionType getLegendPositionType(Integer position) {
            if(position == null){ return null; }
            switch (position) {
                case 2 : return LegendPositionType.TOP;
                case 3 : return LegendPositionType.RIGHT;
                case 4 : return LegendPositionType.LEFT;
                case 5 : return LegendPositionType.TOP_RIGHT;
            }
            return LegendPositionType.BOTTOM;
        }
    }

    static class XAxisDataConverter {

        public static void convert(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            FrameworkChartAPI.updateXAxisInvertedStatusWithIndex(chartMeta, oldChartInsertJSON.reversed, 0);
            convertLabelData(chartMeta, oldChartInsertJSON);
            convertTitleData(chartMeta, oldChartInsertJSON);
            convertGLData(chartMeta, oldChartInsertJSON);
        }

        private static void convertLabelData(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON) {
            OldChartInsertJSON.TextStyle style = oldChartInsertJSON.getActionList().getXAxis().getStyle();
            Font font = new Font(new DefaultFontJSONConstants());

            font.setFontColor(oldChartInsertJSON.getDefaultColorsState().xLabelsColorState ? null : style.color);
            font.setFontStyle(style.fontStyle);
            font.setFontSize(style.fontSize);
            font.setFontWeight(style.fontWeight);
            FrameworkChartAPI.updateXAxisLabelsFontStyleWithIndex(chartMeta, font, 0);
            FrameworkChartAPI.updateXAxisLabelStatusWithIndex(chartMeta, oldChartInsertJSON.xLabel, 0);
            FrameworkChartAPI.updateXAxisLabelsStaggerLinesWithIndex(chartMeta,  ChartUtils.produceIfNotNull(Objects::toString, oldChartInsertJSON.sline), 0);
            FrameworkChartAPI.updateXAxisLabelSlantAngleWithIndex(chartMeta, ChartUtils.produceIfNotNull(Objects::toString, oldChartInsertJSON.slant), 0);
        }

        private static void convertTitleData(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            Font font = new Font(new DefaultFontJSONConstants());
            String title = ChartUtils.doubleDecode(oldChartInsertJSON.xTitle);
            OldChartInsertJSON.TextStyle style = oldChartInsertJSON.getActionList().getXAxis().getStyle();

            font.setFontColor(oldChartInsertJSON.getDefaultColorsState().xTitleColorState ? null : style.color);
            font.setFontStyle(style.fontStyle);
            font.setFontSize(style.fontSize);
            font.setFontWeight(style.fontWeight);
            FrameworkChartAPI.updateChartXAxisTitleFontWithIndex(chartMeta, font, 0);
            FrameworkChartAPI.updateChartXAxisTitleWithIndex(chartMeta, title, 0);
            FrameworkChartAPI.updateChartXAxisTitleStatusWithIndex(chartMeta, ChartUtils.isNonEmptyString(title), 0);
        }

        private static void convertGLData(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            List<OldChartInsertJSON.ChartGridlines.XAxis> xAxisList = oldChartInsertJSON.getChartGridlines().getXAxis();
            OldChartInsertJSON.DefaultColorsState colorsState = oldChartInsertJSON.getDefaultColorsState();
            if(xAxisList.isEmpty()) { return; }

            OldChartInsertJSON.ChartGridlines.XAxis xAxis = xAxisList.get(0);
            GridLineCountType countType = Objects.equals(xAxis.majorGridlineCountType, "auto") ? GridLineCountType.AUTO
                    : ChartUtils.produceIfNotNull(StateFullLambda::retrieveGridLineCount, xAxis.majorGridlineCount);
            FrameworkChartAPI.updateXAxisMajorGLStatus(chartMeta, xAxis.majorGridlineEnabled, 0);
            FrameworkChartAPI.updateXAxisMajorGLCount(chartMeta, countType, 0);
            FrameworkChartAPI.updateXAxisMajorGLColor(chartMeta, colorsState.xGridLinesColorState ? null : xAxis.majorGridlineColor, 0);
            FrameworkChartAPI.updateXAxisMajorGLOpacity(chartMeta, colorsState.xGridLinesColorState || Objects.isNull(xAxis.majorGridlineColor) ? null : 1d, 0);
            FrameworkChartAPI.updateXAxisMajorGLType(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrievLineStyleType, xAxis.majorGLLineType), 0);
            FrameworkChartAPI.updateXAxisMinorGLStatus(chartMeta, xAxis.minorGridlineEnabled, 0);
            FrameworkChartAPI.updateXAxisMinorGLColor(chartMeta, colorsState.xMinorGridLinesColorState ? null : xAxis.minorGridlineColor, 0);
            FrameworkChartAPI.updateXAxisMinorGLOpacity(chartMeta, colorsState.xMinorGridLinesColorState || Objects.isNull(xAxis.minorGridlineColor) ? null : 1d, 0);
            FrameworkChartAPI.updateXAxisMinorGLType(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrievLineStyleType, xAxis.minorGLLineType), 0);
        }

    }

    static class YAxisDataConverter {
        private static Map<String, YAxisUnitFormatType> oldToNewYAxisUnitFormat;

        private static YAxisUnitFormatType getNewYAxisUnitFormat(OldChartInsertJSON.YAxis yAxes){

            if(oldToNewYAxisUnitFormat == null){
                oldToNewYAxisUnitFormat = CollectionsUtils.mapOf(
                        CollectionsUtils.mapEntry("DEFAULT", YAxisUnitFormatType.AUTO),          // NO I18N
                        CollectionsUtils.mapEntry("THOUSAND", YAxisUnitFormatType.THOUSAND),          // NO I18N
                        CollectionsUtils.mapEntry("MILLION", YAxisUnitFormatType.MILLION),          // NO I18N
                        CollectionsUtils.mapEntry("BILLION", YAxisUnitFormatType.BILLION),          // NO I18N
                        CollectionsUtils.mapEntry("1", YAxisUnitFormatType.NONE),          // NO I18N
                        CollectionsUtils.mapEntry("0.001", YAxisUnitFormatType.SCALE_0_001),          // NO I18N
                        CollectionsUtils.mapEntry("0.01", YAxisUnitFormatType.SCALE_0_01),          // NO I18N
                        CollectionsUtils.mapEntry("0.1", YAxisUnitFormatType.SCALE_0_1),          // NO I18N
                        CollectionsUtils.mapEntry("1000", YAxisUnitFormatType.SCALE_1000),          // NO I18N
                        CollectionsUtils.mapEntry("1000000", YAxisUnitFormatType.SCALE_1000000)          // NO I18N
                );
            }
            String unitFormat = Objects.equals(yAxes.conversionType, "SCALES") ? yAxes.scaleFactor == null ? null :         // NO I18N
                    String.valueOf(yAxes.scaleFactor) : yAxes.unitFactor;
            if(unitFormat == null) { return YAxisUnitFormatType.NONE; }
            return oldToNewYAxisUnitFormat.getOrDefault(unitFormat, YAxisUnitFormatType.CUSTOM);
        }


        static void convert(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            List<OldChartInsertJSON.YAxis> yAxis = oldChartInsertJSON.getYAxis();
            OldChartInsertJSON.DefaultColorsState colorsState = oldChartInsertJSON.getDefaultColorsState();

            for(int idx = 0; idx < yAxis.size(); idx++){
                OldChartInsertJSON.YAxis yAxes = yAxis.get(idx);

                FrameworkChartAPI.updateYAxisScaleTypeWithIndex(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrieveYAxisScaleType, yAxes.scaleType), idx);
                FrameworkChartAPI.updateYAxisPrefix(chartMeta, yAxes.prefix, idx);
                FrameworkChartAPI.updateYAxisSuffix(chartMeta, yAxes.suffix, idx);
                convertPlotLines(chartMeta, yAxes.getPlotLines(), idx);
                convertLabels(chartMeta, yAxes.getLabels(), colorsState, idx);
                FrameworkChartAPI.updateYAxisRangeMinValue(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrieveInteger, yAxes.min), idx);
                FrameworkChartAPI.updateYAxisRangeMaxValue(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrieveInteger, yAxes.max), idx);
                FrameworkChartAPI.updateYAxisRangeInterval(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrieveInteger, yAxes.tickInterval), idx);
                FrameworkChartAPI.updateYAxisLogBaseValueWithIndex(chartMeta, yAxes.logBaseValue, idx);
                convertTitle(chartMeta, yAxes.getTitle(), colorsState, idx);
                FrameworkChartAPI.updateYAxisUnitFormat(chartMeta, getNewYAxisUnitFormat(yAxes), idx);
                FrameworkChartAPI.updateYAxisUnitFormatCustomValue(chartMeta, Objects.requireNonNullElse(yAxes.scaleFactor, 1d), idx);
            }
            convertStackLabels(chartMeta, oldChartInsertJSON);
            processGLDataFromYAxis(oldChartInsertJSON);
            convertGridLines(chartMeta, oldChartInsertJSON);
        }

        private static void convertPlotLines(ChartMeta chartMeta, List<OldChartInsertJSON.YAxis.YAxisPlotLines> plotLines, int yAxisIdx){
            for(int idx = 0; idx < plotLines.size(); idx++){
                OldChartInsertJSON.YAxis.YAxisPlotLines plotLine = plotLines.get(idx);
                OldChartInsertJSON.YAxis.YAxisPlotLines.YAxisPlotLinesLabels label = plotLine.getLabel();
                OldChartInsertJSON.YAxis.YAxisLabels.YAxisLabelsStyle style = label.getStyle();
                Font font = new Font(new DefaultFontJSONConstants());

                font.setFontColor(style.color);
                font.setFontStyle(style.fontStyle);
                font.setFontSize(style.fontSize);
                font.setFontWeight(style.fontWeight);
                FrameworkChartAPI.updatePlotLineValue(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrieveDouble, plotLine.value), yAxisIdx, idx);
                FrameworkChartAPI.updatePlotLineTitle(chartMeta, label.text, yAxisIdx, idx);
                FrameworkChartAPI.updatePlotLineColor(chartMeta, plotLine.color, yAxisIdx, idx);
                FrameworkChartAPI.updatePlotLineLineType(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrievLineStyleType, plotLine.lineStyleType), yAxisIdx, idx);
                FrameworkChartAPI.updatePlotLineFontStyle(chartMeta, font, yAxisIdx, idx);
            }
        }

        private static void convertLabels(ChartMeta chartMeta, OldChartInsertJSON.YAxis.YAxisLabels yAxisLabels, OldChartInsertJSON.DefaultColorsState colorsState, int yAxisIdx){
            OldChartInsertJSON.YAxis.YAxisLabels.YAxisLabelsStyle style = yAxisLabels.getStyle();
            Font font = new Font(new DefaultFontJSONConstants());

            font.setFontColor(colorsState.yLabelColorState ? null : style.color);
            font.setFontStyle(style.fontStyle);
            font.setFontSize(style.fontSize);
            font.setFontWeight(style.fontWeight);
            FrameworkChartAPI.updateYAxisLabelStatusWithIndex(chartMeta, yAxisLabels.enabled, yAxisIdx);
            FrameworkChartAPI.updateYAxisLabelFontStyleWithIndex(chartMeta, font, yAxisIdx);
        }

        private static void convertTitle(ChartMeta chartMeta, OldChartInsertJSON.YAxis.Title yAxisTitle, OldChartInsertJSON.DefaultColorsState colorsState, int yAxisIdx){
            OldChartInsertJSON.TextStyle style = yAxisTitle.getStyle();
            Font font = new Font(new DefaultFontJSONConstants());

            String title = ChartUtils.doubleDecode(yAxisTitle.text);
            font.setFontColor(colorsState.yTitleColorState ? null : style.color);
            font.setFontStyle(style.fontStyle);
            font.setFontSize(style.fontSize);
            font.setFontWeight(style.fontWeight);
            FrameworkChartAPI.updateChartYAxisTitleWithIndex(chartMeta, title, yAxisIdx);
            FrameworkChartAPI.updateChartYAxisTitleFontWithIndex(chartMeta, font, yAxisIdx);
            FrameworkChartAPI.updateChartYAxisTitleStatusWithIndex(chartMeta, ChartUtils.isNonEmptyString(title), yAxisIdx);
        }

        private static void convertStackLabels(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            OldChartInsertJSON.TextStyle dataLabelsTotal = oldChartInsertJSON.getActionList().getColorObject().getDataLabelsTotal();
            int count = oldChartInsertJSON.getYAxis().size();
            Font font = new Font(new DefaultFontJSONConstants());

            font.setFontColor(oldChartInsertJSON.getDefaultColorsState().totalDataLabelsColorState ? null : dataLabelsTotal.color);
            font.setFontStyle(dataLabelsTotal.fontStyle);
            font.setFontSize(dataLabelsTotal.fontSize);
            font.setFontWeight(dataLabelsTotal.fontWeight);
            FrameworkChartAPI.updateYAxisStackLabelsStatusWithCount(chartMeta, oldChartInsertJSON.dataLabelTotal, count);
            FrameworkChartAPI.updateYAxisStackLabelFontStyleWithCount(chartMeta, font, count);
        }

        private static void convertGridLines(ChartMeta chartMeta, OldChartInsertJSON oldChartInsertJSON){
            List<OldChartInsertJSON.ChartGridlines.YAxis> yAxisGridLines = oldChartInsertJSON.getChartGridlines().getYAxis();
            OldChartInsertJSON.DefaultColorsState colorsState = oldChartInsertJSON.getDefaultColorsState();
            if(yAxisGridLines.isEmpty()) { return; }
            OldChartInsertJSON.ChartGridlines.YAxis yAxis = yAxisGridLines.get(0);
            GridLineCountType countType = Objects.equals(yAxis.majorGridlineCountType, "auto") ? GridLineCountType.AUTO
                    : ChartUtils.produceIfNotNull(StateFullLambda::retrieveGridLineCount, yAxis.majorGridlineCount);
            FrameworkChartAPI.updateYAxisMajorGLStatus(chartMeta, yAxis.majorGridlineEnabled, 0);
            FrameworkChartAPI.updateYAxisMajorGLCount(chartMeta, countType, 0);
            FrameworkChartAPI.updateYAxisMajorGLColor(chartMeta, colorsState.gridLinesColorState ? null : yAxis.majorGridlineColor, 0);
            FrameworkChartAPI.updateYAxisMajorGLOpacity(chartMeta, colorsState.gridLinesColorState || Objects.isNull(yAxis.majorGridlineColor) ? null : 1d, 0);
            FrameworkChartAPI.updateYAxisMajorGLType(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrievLineStyleType, yAxis.majorGLLineType), 0);
            FrameworkChartAPI.updateYAxisMinorGLStatus(chartMeta, yAxis.minorGridlineEnabled, 0);
            FrameworkChartAPI.updateYAxisMinorGLColor(chartMeta, colorsState.minorGridLinesColorState ? null : yAxis.minorGridlineColor, 0);
            FrameworkChartAPI.updateYAxisMinorGLOpacity(chartMeta, colorsState.minorGridLinesColorState || Objects.isNull(yAxis.minorGridlineColor) ? null : 1d, 0);
            FrameworkChartAPI.updateYAxisMinorGLType(chartMeta, ChartUtils.produceIfNotNull(StateFullLambda::retrievLineStyleType, yAxis.minorGLLineType), 0);
        }

        private static void processGLDataFromYAxis(OldChartInsertJSON oldChartInsertJSON){
            List<OldChartInsertJSON.YAxis> yAxisList = oldChartInsertJSON.getYAxis();
            List<OldChartInsertJSON.ChartGridlines.YAxis> yAxisGridLines = oldChartInsertJSON.getChartGridlines().getYAxis();
            if(yAxisList.isEmpty() || yAxisGridLines.isEmpty()) { return; }
            OldChartInsertJSON.YAxis firstYAxis = yAxisList.get(0);
            OldChartInsertJSON.ChartGridlines.YAxis yAxisGL = yAxisGridLines.get(0);
            boolean isMajorGLEnabled = firstYAxis.gridLineWidth != null && firstYAxis.gridLineWidth > 0;
            boolean isMinorGLEnabled = firstYAxis.minorTickInterval != null && firstYAxis.minorTickInterval.equalsIgnoreCase("auto");
            String majorGLColor = firstYAxis.gridLineColor, minorGLColor = firstYAxis.minorGridLineColor;

            yAxisGL.majorGridlineEnabled = ChartUtils.requireNonNullElse(yAxisGL.majorGridlineEnabled, isMajorGLEnabled);
            yAxisGL.minorGridlineEnabled = ChartUtils.requireNonNullElse(yAxisGL.minorGridlineEnabled, isMinorGLEnabled);
            yAxisGL.majorGridlineColor = ChartUtils.requireNonNullElse(yAxisGL.majorGridlineColor, majorGLColor);
            yAxisGL.minorGridlineColor = ChartUtils.requireNonNullElse(yAxisGL.minorGridlineColor, minorGLColor);
        }
    }

    public static void convert(OldChartInsertJSON insertJSON, ChartMeta chartMeta, int seriesCount) {
        ChartDataConverter.convert(insertJSON, chartMeta);
        TitleConverter.convert(insertJSON, chartMeta);
        SubtitleConverter.convert(insertJSON, chartMeta);
        SeriesPropsDataConverter.convert(chartMeta, insertJSON, seriesCount);
        CaptionDataConverter.convert(chartMeta, insertJSON);
        DonutTotalDatalabelsConverter.convert(chartMeta, insertJSON);
        LegendDataConverter.convert(chartMeta, insertJSON);
        XAxisDataConverter.convert(chartMeta, insertJSON);

        YAxisDataConverter.convert(chartMeta, insertJSON);
    }

}
