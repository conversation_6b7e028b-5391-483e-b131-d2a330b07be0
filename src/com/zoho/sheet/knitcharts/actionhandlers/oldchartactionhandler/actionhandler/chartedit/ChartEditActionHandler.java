package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.chartedit;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTable;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.OldChartActionDataHolder;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.DataJoinType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class ChartEditActionHandler {

    public static void handle(OldChartActionDataHolder dataHolder){

        updateSeriesInType(dataHolder);
        updateFirstRowColLabelStatus(dataHolder);
        updateDataSources(dataHolder);
        updateIncludeHiddenCellsStatus(dataHolder);
        updateDataJoinType(dataHolder);
    }

    private static void updateSeriesInType(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String seriesIn = ChartUtils.optFromJSONObject(actionJSON, OldChartActionConstants.JSONConstant.SERIES_IN);
        SeriesInType seriesInType = ChartUtils.getSeriesInTypeForOldSeriesIn(seriesIn);


        SheetChartAPI.updateSeriesIn(sheetMeta, seriesInType);
    }

    private static void updateFirstRowColLabelStatus(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Boolean fcl = ChartUtils.toBoolean(actionJSON.get(OldChartActionConstants.JSONConstant.FIRST_COL_LABEL));
        Boolean frl = ChartUtils.toBoolean(actionJSON.get(OldChartActionConstants.JSONConstant.FIRST_ROW_LABEL));

        SheetChartAPI.updateFirstRowAsLabelStatus(sheetMeta, frl);
        SheetChartAPI.updateFirstColAsLabelStatus(sheetMeta, fcl);
    }

    private static void updateDataSources(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        ChartContainer chartContainer = workbook.getChartsContainer();
        int referenceID = chartContainer.getReferenceID(dataHolder.getChartID());
        DataTable dataTable = new DataTable(workbook, referenceID, SheetChartGetterAPI.getDataSources(sheetMeta), dataHolder.getActiveCellBean().getAssociatedSheetName());
        String updatedDataSources = actionJSON.getString(OldChartActionConstants.JSONConstant.DATA_RANGE);

        dataTable.updateDataSources(workbook, CollectionsUtils.listOf(updatedDataSources.split(";")), dataHolder.getActiveCellBean().getAssociatedSheetName());
        SheetChartAPI.updateDataSources(sheetMeta, dataTable.getDataSources());
    }

    public static void updateIncludeHiddenCellsStatus(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Boolean ihc = ChartUtils.toBoolean(actionJSON.get(OldChartActionConstants.JSONConstant.INCLUDE_HIDDEN_CELLS));

        SheetChartAPI.updateIncludeHiddenCells(sheetMeta, ihc);
    }

    public static void updateDataJoinType(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String appendingWay = actionJSON.has(OldChartActionConstants.JSONConstant.DATA_JOIN_TYPE) ?
                actionJSON.getString(OldChartActionConstants.JSONConstant.DATA_JOIN_TYPE) :
                actionJSON.optString(OldChartActionConstants.JSONConstant.COMBINE_RANGE, "HORIZONTAL");         // NO I18N
        DataJoinType dataJoinType = DataJoinType.retrieveByValue(appendingWay.toLowerCase());

        SheetChartAPI.updateDataJoinType(sheetMeta, dataJoinType);
    }

}
