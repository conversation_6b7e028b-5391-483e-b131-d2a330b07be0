package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Sheet;

import java.util.Map;

public class OldChartUserActionDataHolder extends OldChartActionDataHolder {

    private final Map<String, Object> responseMap;

    public OldChartUserActionDataHolder(Sheet activeSheet, int actionConstant, JSONObjectWrapper actionJSON, Map<String, Object> responseMap, boolean isFromUndoRedo) {
        super(activeSheet, actionConstant, actionJSON, isFromUndoRedo);
        this.responseMap = responseMap;
    }

    public Map<String, Object> getResponseMap() {
        return responseMap;
    }
}
