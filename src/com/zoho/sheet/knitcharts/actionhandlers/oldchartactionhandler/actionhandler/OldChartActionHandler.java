package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.knitcharts.chart.ProtoChart;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.api.ChartsAPIEndpoint;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.pojo.PublicChartPOJO;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsDAO;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsPublicDAO;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionBiConsumer;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.chartstyles.ChartPasteStyleActionHandler;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.PublicChartType;
import com.zoho.sheet.knitcharts.utils.ChartActivityMonitor;
import com.zoho.sheet.knitcharts.utils.ChartCreator;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.ActionDataHolder;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.miscellaneous.exception.UnsupportedChartActionException;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.chartedit.ChartEditActionHandler;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.quickedit.ChartQuickEditHandler;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.quickstyle.ChartQuickStyleHandler;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.insertjsonconverter.InsertJSONConverter;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.utils.PublicChartUtils;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.zoho.sheet.knitcharts.utils.ChartUtils.toChartsInteger;

/**
 * Handles old chart requests
 */
public class OldChartActionHandler {

    private static Map<ChartsInteger, FunctionBiConsumer<OldChartActionDataHolder, WorkbookContainer>> actionHandlerMap;

    private static final Logger LOGGER = Logger.getLogger(OldChartActionHandler.class.getName());

    private static void initHandlerMap(){
        actionHandlerMap = Collections.unmodifiableMap(new HashMap<>() {{
            put(toChartsInteger(ActionConstants.CHART_NEW), OldChartActionHandler::handleChartInsert);
            put(toChartsInteger(ActionConstants.CHART_DELETE), OldChartActionHandler::handleChartDelete);
            put(toChartsInteger(ActionConstants.CHART_MOVE), OldChartActionHandler::handleChartMove);
            put(toChartsInteger(ActionConstants.CHART_RESIZE), OldChartActionHandler::handleChartResize);
            put(toChartsInteger(ActionConstants.CHART_QUICK_STYLE), OldChartActionHandler::handleChartQuickStyle);
            put(toChartsInteger(ActionConstants.CHART_QUICK_EDIT), OldChartActionHandler::handleChartQuickEdit);
            put(toChartsInteger(ActionConstants.CHART_EDIT), OldChartActionHandler::handleChartEdit);
            put(toChartsInteger(ActionConstants.CHART_MOVE_TO_NEW_SHEET), OldChartActionHandler::handleMoveToOtherSheet);
            put(toChartsInteger(ActionConstants.CHART_PUBLISH), OldChartActionHandler::handleChartPublish);
            put(toChartsInteger(ActionConstants.CHART_UNPUBLISH), OldChartActionHandler::handleChartUnPublish);
            put(toChartsInteger(ActionConstants.CHART_PASTE_STYLE), OldChartActionHandler::handlePasteChartStyles);
        }});
    }

    private static FunctionBiConsumer<OldChartActionDataHolder, WorkbookContainer> getActionHandler(int actionConstant){
        if(actionHandlerMap == null){ initHandlerMap(); }
        FunctionBiConsumer<OldChartActionDataHolder, WorkbookContainer> handler = actionHandlerMap.get(toChartsInteger(actionConstant));

        if(handler == null){
            throw new UnsupportedChartActionException(String.format("%s: %d", ErrorMessages.INVALID_CHART_ACTION, actionConstant));                                     // NO I18N
        }
        return handler;
    }

    public static void handle(Sheet activeSheet, int actionConstant, JSONObjectWrapper actionJSON, WorkbookContainer container){
        com.zoho.sheet.knitcharts.utils.ChartActivityMonitor.ChartActivity chartActivity = com.zoho.sheet.knitcharts.supplier.ObjectsSupplier.getInstance().getChartActivityMonitor()
                .getChartActivity("Old chart action", actionJSON.optString(JSONConstants.RID), actionJSON.optString(JSONConstants.ZUID), actionJSON.toString(), actionJSON.has(OldChartActionConstants.JSONConstant.CHART_ID) ?         // NO I18N
                        actionJSON.getString(OldChartActionConstants.JSONConstant.CHART_ID) :
                        actionJSON.optString(OldChartActionConstants.JSONConstant.ID));
        boolean isFromUndoRedo = actionJSON.has(ChartActionConstants.JSONConstants.FROM_UNDO) &&
                actionJSON.getBoolean(ChartActionConstants.JSONConstants.FROM_UNDO);
        try {
            chartActivity.start();
            OldChartActionDataHolder dataHolder = new OldChartActionDataHolder(activeSheet, actionConstant, actionJSON, isFromUndoRedo);
            getActionHandler(actionConstant).consume(dataHolder, container);

            ChartUtils.getChartFromActionDataHolder(dataHolder).setSaveRequired(true);
            chartActivity.completed();
        }catch (Exception e){
            chartActivity.failed(e);
            LOGGER.log(Level.SEVERE, String.format("[Old chart action handler]: Unable to process old chart action\n%s", e.getMessage()));
        }
    }

    public static void handleDBAction(Sheet activeSheet, int actionConstant, JSONObjectWrapper actionJSON, WorkbookContainer container){
        ChartActivityMonitor.ChartActivity chartActivity = com.zoho.sheet.knitcharts.supplier.ObjectsSupplier.getInstance().getChartActivityMonitor()
                .getChartActivity("Old chart DB action", actionJSON.optString(JSONConstants.RID), actionJSON.optString(JSONConstants.ZUID), actionJSON.toString(), actionJSON.has(OldChartActionConstants.JSONConstant.CHART_ID) ?         // NO I18N
                        actionJSON.getString(OldChartActionConstants.JSONConstant.CHART_ID) :
                        actionJSON.optString(OldChartActionConstants.JSONConstant.ID));
        boolean isFromUndoRedo = actionJSON.has(ChartActionConstants.JSONConstants.FROM_UNDO) &&
                actionJSON.getBoolean(ChartActionConstants.JSONConstants.FROM_UNDO);
        try {
            chartActivity.start();
            OldChartActionDataHolder dataHolder = new OldChartActionDataHolder(activeSheet, actionConstant, actionJSON, isFromUndoRedo);
            switch (actionConstant) {
                case ActionConstants.CHART_NEW:{
                    handleChartInsertDB(dataHolder, container);
                    break;
                }
                case ActionConstants.CHART_DELETE:{
                    handleChartDeleteDB(dataHolder, container);
                    break;
                }
                case ActionConstants.CHART_MOVE_TO_NEW_SHEET: {
                    handleMoveToOtherSheetDB(dataHolder, container);
                    break;
                }
                case ActionConstants.CHART_PUBLISH:{
                    handleChartPublishDB(dataHolder, container);
                    break;
                }
                case ActionConstants.CHART_UNPUBLISH:{
                    handleChartUnPublishDB(dataHolder, container);
                    break;
                }
            }
            chartActivity.completed();
        }catch (Exception e){
            chartActivity.failed(e);
            LOGGER.log(Level.SEVERE, String.format("[Old chart action handler]: Unable to process old chart action\n%s", e.getMessage()));
        }
    }

    static void handleChartInsert(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        com.zoho.sheet.knitcharts.chart.ProtoChart protoChart = createProtoChart(actionDataHolder);

        ChartCreator.createChart(actionDataHolder.getActiveSheet().getWorkbook(), protoChart);
    }

    static void handleChartInsertDB(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        com.zoho.sheet.knitcharts.chart.ProtoChart protoChart = createProtoChart(actionDataHolder);
        ChartsAPIEndpoint endpoint = com.zoho.sheet.knitcharts.supplier.ObjectsSupplier.getInstance().getChartsAPIEndpoint(actionDataHolder.getActiveSheet().getWorkbook(),
                container.getDocOwner(), Long.parseLong(container.getDocId()));
        endpoint.insertChartInDB(protoChart);
    }

    static void handleChartDelete(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = ChartUtils.getChartContainer(actionDataHolder);

        chartContainer.removeChart(actionDataHolder.getActiveSheet().getAssociatedName(), actionDataHolder.getChartID());
    }

    static void handleChartDeleteDB(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        ChartsAPIEndpoint endpoint = ObjectsSupplier.getInstance().getChartsAPIEndpoint(actionDataHolder.getActiveSheet().getWorkbook(),
                container.getDocOwner(), Long.parseLong(container.getDocId()));
        endpoint.deleteChartFromDB(actionDataHolder.getChartID());
    }

    static void handleChartMove(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(actionDataHolder);
        JSONObjectWrapper actionJSON = actionDataHolder.getChartJSON();

        updatePositionDetails(sheetMeta, actionJSON);
    }

    static void handleChartResize(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(actionDataHolder);
        JSONObjectWrapper actionJSON = actionDataHolder.getChartJSON();

        updatePositionDetails(sheetMeta, actionJSON);
        SheetChartAPI.updateHeight(sheetMeta, Double.parseDouble(actionJSON.getString(OldChartActionConstants.JSONConstant.HEIGHT)));
        SheetChartAPI.updateWidth(sheetMeta, Double.parseDouble(actionJSON.getString(OldChartActionConstants.JSONConstant.WIDTH)));
    }

    static void handleChartQuickEdit(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        ChartQuickEditHandler.handle(actionDataHolder);
    }

    static void handleChartQuickStyle(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        ChartQuickStyleHandler.handle(actionDataHolder);
    }

    static void handleChartEdit(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        ChartEditActionHandler.handle(actionDataHolder);
    }

    static void handleMoveToOtherSheet(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        ChartContainer chartContainer = actionDataHolder.getActiveSheet().getWorkbook().getChartsContainer();
        JSONObjectWrapper actionJSON = actionDataHolder.getActionJSON();
        String targetSheetName = actionJSON.getString(OldChartActionConstants.JSONConstant.NEW_SHEET_NAME);
        chartContainer.moveChart(actionDataHolder.getChartID(), targetSheetName);

    }

    static void handleMoveToOtherSheetDB(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        JSONObjectWrapper actionJSON = actionDataHolder.getActionJSON();
        String targetSheetName = actionJSON.getString(OldChartActionConstants.JSONConstant.NEW_SHEET_NAME);
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        Workbook workbook = actionDataHolder.getActiveSheet().getWorkbook();

        dao.updateSheetID(actionDataHolder.getChartID(), workbook.getSheetByAssociatedName(targetSheetName).getName());
    }

    static void handleChartPublish(OldChartActionDataHolder actionDataHolder, WorkbookContainer container) {
        JSONObjectWrapper actionJSON = actionDataHolder.getActionJSON();
        String publicChartName = actionJSON.getString(OldChartActionConstants.JSONConstant.PUBLIC_CHART_NAME);
        ChartsPublicDAO publicDAO = ChartsPublicDAO.getDefaultPublicDAO(Constants.PUBLIC_SPACE);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(actionDataHolder);

        PublicChartPOJO bean = publicDAO.readPublicChart(publicChartName);
        SheetChartAPI.setPublicSpaceChartID(sheetMeta, bean.getPublicSpaceChartID());
        SheetChartAPI.setPublicChartName(sheetMeta, publicChartName);
        SheetChartAPI.setPublicChartType(sheetMeta, PublicChartType.EXTERNAL);
    }

    static void handleChartPublishDB(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        JSONObjectWrapper actionJSON = actionDataHolder.getActionJSON();
        if(actionJSON.has(OldChartActionConstants.JSONConstant.PUBLIC_CHART_NAME)){
            String publicChartName = actionJSON.getString(OldChartActionConstants.JSONConstant.PUBLIC_CHART_NAME);
            ChartsPublicDAO publicDAO = ChartsPublicDAO.getDefaultPublicDAO(Constants.PUBLIC_SPACE);

            PublicChartPOJO bean = publicDAO.readPublicChart(publicChartName);
            long docID = Long.parseLong(container.getDocId());
            String docOwnerID = container.getDocOwner();
            ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);

            dao.publishChart(actionDataHolder.getChartID(), publicChartName, Constants.PUBLIC_SPACE, new Date().getTime());
            publicDAO.publishChart(actionDataHolder.getChartID(), bean.getPublicSpaceChartID(), publicChartName, docOwnerID, DocumentUtils.getZUID(docOwnerID));
            PublicChartUtils.doFollowUpPublishTasks(actionDataHolder.getActiveSheet().getWorkbook(), actionDataHolder.getChartID(), bean.getPublicSpaceChartID(), Constants.PUBLIC_SPACE);
        } else {
            long docID = Long.parseLong(container.getDocId());
            String docOwnerID = container.getDocOwner();
            ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);

            String publicChartName = UUID.randomUUID().toString() + System.currentTimeMillis() + ((int) (Math.random() * 1000));
            publicChartName = publicChartName.replace("-", "");     // NO I18N

            String publicSpace = PublicChartUtils.getPublicSpace(PublicChartType.EXTERNAL, docOwnerID);
            ChartsPublicDAO publicDAO = ChartsPublicDAO.getDefaultPublicDAO(publicSpace);

            dao.publishChart(actionDataHolder.getChartID(), publicChartName, publicSpace, new Date().getTime());
            long publicSpaceChartID = publicDAO.publishChart(actionDataHolder.getChartID(), publicChartName, docOwnerID, DocumentUtils.getZUID(docOwnerID));
            actionJSON.put(OldChartActionConstants.JSONConstant.PUBLIC_CHART_NAME, publicChartName);
            PublicChartUtils.doFollowUpPublishTasks(actionDataHolder.getActiveSheet().getWorkbook(), actionDataHolder.getChartID(), Long.toString(publicSpaceChartID), publicSpace);
        }
    }

    static void handleChartUnPublish(OldChartActionDataHolder actionDataHolder, WorkbookContainer container) {
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(actionDataHolder);

        SheetChartAPI.clearPublicDetails(sheetMeta);
    }

    static void handleChartUnPublishDB(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        ChartsAPIEndpoint endpoint = new ChartsAPIEndpoint(actionDataHolder.getActiveSheet().getWorkbook(),
                container.getDocOwner(), Long.parseLong(container.getDocId()));

        endpoint.unPublishChart(actionDataHolder.getChartID());

    }

    static void handlePasteChartStyles(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        ChartPasteStyleActionHandler.handle(actionDataHolder);
    }

    private static void updatePositionDetails(SheetMeta sheetMeta, JSONObjectWrapper actionJSON){
        SheetChartAPI.updateXCoordinate(sheetMeta, Double.parseDouble(actionJSON.getString(OldChartActionConstants.JSONConstant.LEFT)));
        SheetChartAPI.updateYCoordinate(sheetMeta, Double.parseDouble(actionJSON.getString(OldChartActionConstants.JSONConstant.TOP)));
        SheetChartAPI.updateStartRow(sheetMeta, Double.parseDouble(actionJSON.getString(OldChartActionConstants.JSONConstant.START_ROW)));
        SheetChartAPI.updateStartColumn(sheetMeta, Double.parseDouble(actionJSON.getString(OldChartActionConstants.JSONConstant.START_COL)));
        SheetChartAPI.updateStartRowDiff(sheetMeta, Double.parseDouble(actionJSON.getString(OldChartActionConstants.JSONConstant.START_ROW_DIFF)));
        SheetChartAPI.updateStartColumnDiff(sheetMeta, Double.parseDouble(actionJSON.getString(OldChartActionConstants.JSONConstant.START_COL_DIFF)));
    }

    private static com.zoho.sheet.knitcharts.chart.ProtoChart createProtoChart(ActionDataHolder dataHolder){
        com.zoho.sheet.knitcharts.chart.ProtoChart protoChart = createProtoChartWithoutID(dataHolder);

        protoChart.chartID = dataHolder.getChartID();
        return protoChart;
    }

    private static com.zoho.sheet.knitcharts.chart.ProtoChart createProtoChartWithoutID(ActionDataHolder dataHolder){
        com.zoho.sheet.knitcharts.chart.ProtoChart protoChart = new ProtoChart();

        protoChart.sheetName = dataHolder.getActiveSheet().getName();
        protoChart.sheetID = dataHolder.getActiveSheet().getAssociatedName();
        InsertJSONConverter.convertInsertChartJSON(dataHolder.getActiveSheet().getWorkbook(), dataHolder.getChartJSON(), protoChart);

        return protoChart;
    }

    public static void handlePreProcessor(Sheet activeSheet, int actionConstant, JSONObjectWrapper actionJSON, WorkbookContainer container){
        ChartActivityMonitor.ChartActivity chartActivity = com.zoho.sheet.knitcharts.supplier.ObjectsSupplier.getInstance().getChartActivityMonitor()
                .getChartActivity("Old chart preprocessor action", actionJSON.optString(JSONConstants.RID), actionJSON.optString(JSONConstants.ZUID), actionJSON.toString(), actionJSON.has(OldChartActionConstants.JSONConstant.CHART_ID) ?         // NO I18N
                        actionJSON.getString(OldChartActionConstants.JSONConstant.CHART_ID) :
                        actionJSON.optString(OldChartActionConstants.JSONConstant.ID));
        boolean isFromUndoRedo = actionJSON.has(ChartActionConstants.JSONConstants.FROM_UNDO) &&
                actionJSON.getBoolean(ChartActionConstants.JSONConstants.FROM_UNDO);
        try {
            chartActivity.start();
            OldChartActionDataHolder dataHolder = new OldChartActionDataHolder(activeSheet, actionConstant, actionJSON, isFromUndoRedo);
            if (actionConstant == ActionConstants.CHART_NEW) {
                handleChartInsertPreprocessor(dataHolder, container);
            }
            chartActivity.completed();
        }catch (Exception e){
            chartActivity.failed(e);
            LOGGER.log(Level.SEVERE, String.format("[Old chart action handler]: Unable to process old chart action\n%s", e.getMessage()));
        }
    }

    static void handleChartInsertPreprocessor(OldChartActionDataHolder actionDataHolder, WorkbookContainer container){
        com.zoho.sheet.knitcharts.chart.ProtoChart protoChart = createProtoChartWithoutID(actionDataHolder);
        ChartsAPIEndpoint endpoint = com.zoho.sheet.knitcharts.supplier.ObjectsSupplier.getInstance().getChartsAPIEndpoint(actionDataHolder.getActiveSheet().getWorkbook(),
                container.getDocOwner(), Long.parseLong(container.getDocId()));

        String chartID = endpoint.addNewChartInDB(protoChart);
        actionDataHolder.getActionJSON().put(OldChartActionConstants.JSONConstant.CHART_ID, chartID);
    }

}
