package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.useraction;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.action.AddChartToImageLibraryAction;
import com.zoho.sheet.chartengine.SecondGenChartEngine;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.OldChartUserActionDataHolder;
import com.zoho.sheet.knitcharts.actionhandlers.useraction.ChartUserActionHandler;
import com.zoho.sheet.knitcharts.backwardconverter.converter.ChartsBCManager;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.constants.ChartConstants;
import com.zoho.sheet.knitcharts.mobileresponsegenerator.MobileResponseGenerator;
import com.zoho.sheet.knitcharts.responsegenerator.useraction.ChartUserActionResponseGenerator;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.utils.ChartActivityMonitor;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.PublicChartUtils;

import java.util.List;
import java.util.Map;

public class OldChartUserActionHandler {

    private static void handle(OldChartUserActionDataHolder dataHolder) throws Exception {
        switch (dataHolder.getActionConstant()) {
            case ActionConstants.CHART_COPY_STYLE: {
                handleChartStyleCopyPaste(dataHolder);
                break;
            }
            case ActionConstants.CHART_PREVIEW: {
                handleChartPreview(dataHolder);
                break;
            }
            case ActionConstants.ADD_CHART_TO_IMAGE_LIBRARY: {
                handleAddChartToImageLibrary(dataHolder);
                break;
            }
            case ActionConstants.GET_CHART_RECOMMENDATION: {
                handleChartRecommendation(dataHolder);
                break;
            }
        }
    }

    public static void handle(Sheet sheet, JSONObjectWrapper actionJson, Map<String, Object> responseMap, int action) {
        ChartActivityMonitor.ChartActivity activity = ObjectsSupplier.getInstance().getChartActivityMonitor().getChartActivity(
                "Old Chart User Action", null, null, actionJson.toString(), null            // NO I18N
        );
        try {
            OldChartUserActionDataHolder dataHolder = new OldChartUserActionDataHolder(sheet, action, actionJson, responseMap, false);
            handle(dataHolder);
        } catch (Exception e) {
            activity.failed(e);
        }
    }

    private static void handleChartStyleCopyPaste(OldChartUserActionDataHolder dataHolder) throws Exception {
        String userZUID = dataHolder.getActionJSON().getString(JSONConstants.ZUID);
        Chart knitChart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        String chartID = knitChart.getChartID();

        JSONObjectWrapper stylesJSON = knitChart.getStylesJSON();

        stylesJSON.put(OldChartActionConstants.JSONConstant.IS_NEW_CHART_STYLES, true);
        com.zoho.sheet.chart.ChartUtils.writeChartStyleInfoIntoRedis(userZUID, chartID, stylesJSON);
        com.zoho.sheet.chart.ChartUtils.notifyActionClientForServerClip(userZUID, chartID);
    }

    private static void handleChartPreview(OldChartUserActionDataHolder dataHolder) {
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        Chart knitChart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        String chartID = knitChart.getChartID();

        com.zoho.sheet.chart.Chart oldChart = ChartsBCManager.convert(workbook, chartID,
                knitChart.getApiMeta().getChartMeta(), knitChart.getSheetMeta(), knitChart.getAssociatedSheetName());
        workbook.addChart(knitChart.getAssociatedSheetName(), knitChart.getChartID(), oldChart);
        new SecondGenChartEngine().handleChartPreview(dataHolder.getActiveSheet(), dataHolder.getActionJSON(), dataHolder.getResponseMap());
        workbook.removeChart(knitChart.getAssociatedSheetName(), knitChart.getChartID());
    }

    private static void handleAddChartToImageLibrary(OldChartUserActionDataHolder dataHolder) {
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        String chartID = dataHolder.getActionJSON().getString("chartID");                       // NO I18N
        String rid = dataHolder.getActionJSON().getString("rid");                               // NO I18N

        Chart chart = workbook.getChartsContainer().getChart(chartID);
        String chartTitle = FrameworkChartGetterAPI.getChartTitle(workbook, chart.getSheetMeta(), chart.getApiMeta().getChartMeta());
        byte[] imageBytes = PublicChartUtils.getChartImageForPublicUse(dataHolder.getActiveSheet().getWorkbook(), chartID);
        AddChartToImageLibraryAction.uploadImageToImageLibrary(imageBytes, chartTitle, ChartConstants.FileExtensions.PNG, rid);
    }

    private static void handleChartRecommendation(OldChartUserActionDataHolder dataHolder) {
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        List<JSONObjectWrapper> recommendedCharts = ChartUserActionHandler.getInstance().recommendCharts(dataHolder.getActiveSheet(), dataHolder.getActionJSON());
        JSONObjectWrapper response = MobileResponseGenerator.generateRecommendationResponse(workbook, recommendedCharts, dataHolder.getActiveSheet().getAssociatedName());

        dataHolder.getResponseMap().put(ChartActionConstants.JSONConstants.RESP, response);
    }

}
