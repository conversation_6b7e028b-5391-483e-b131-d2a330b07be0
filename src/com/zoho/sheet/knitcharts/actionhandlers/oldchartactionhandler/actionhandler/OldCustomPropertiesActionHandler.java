package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Tuple;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import static com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants.JSONConstant.*;

/**
 * Custom properties action handler for old chart actions
 * <AUTHOR>
 */
public class OldCustomPropertiesActionHandler {

    /**
     * Extracts Theme accent and tone percent from the action JSONWrapper
     * @param actionJSON Action JSONWrapper object
     * @param key ColorProps key
     * @return Color Theme options
     */
    private static Tuple<String, Double> getColorThemeOptionsForOldAction(JSONObjectWrapper actionJSON, String key){
        JSONObjectWrapper colorPropsJSON = actionJSON.getJSONArray(OldChartActionConstants.JSONConstant.ACTION_LIST)
                .getJSONObject(0).optJSONObject(OldChartActionConstants.JSONConstant.COLOR_PROPS);
        if(colorPropsJSON == null) { return null; }

        JSONObjectWrapper customPropsUserColorProps = colorPropsJSON.optJSONObject(key);
        if(customPropsUserColorProps == null) { return null; }

        String accent = ChartUtils.optFromJSONObject(customPropsUserColorProps, OldChartActionConstants.JSONConstant.THEME);
        String sTone = ChartUtils.optStringFromJSONObject(customPropsUserColorProps, OldChartActionConstants.JSONConstant.TONE_PERCENT);
        Double tone = sTone == null ? null : Double.parseDouble(sTone) / 100;

        return new Tuple<>(accent, tone);
    }

    private static Tuple<String, Double> getColorThemeOptionsFromPath(JSONObjectWrapper actionJSON, String path){
        JSONObjectWrapper colorPropsJSON = actionJSON.getJSONArray(OldChartActionConstants.JSONConstant.ACTION_LIST)
                .getJSONObject(0).optJSONObject(OldChartActionConstants.JSONConstant.COLOR_PROPS);
        if(colorPropsJSON == null) { return null; }

        JSONObjectWrapper customPropsUserColorProps = null;
        JSONObjectWrapper prevJSON = colorPropsJSON;
        String[] pathTokens = path.split("\\.");
        for(String token: pathTokens){
            if(prevJSON == null) { break; }
            customPropsUserColorProps = ChartUtils.optFromJSONObject(prevJSON, token);
            prevJSON = customPropsUserColorProps;
        }
        if(customPropsUserColorProps == null) { return null; }

        String accent = ChartUtils.optFromJSONObject(customPropsUserColorProps, OldChartActionConstants.JSONConstant.THEME);
        String sTone = ChartUtils.optStringFromJSONObject(customPropsUserColorProps, OldChartActionConstants.JSONConstant.TONE_PERCENT);
        Double tone = sTone == null ? null : Double.parseDouble(sTone) / 100;

        return new Tuple<>(accent, tone);
    }

    public static void updateTitleColorThemeOptions(OldChartActionDataHolder dataHolder, String key){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setTitleColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setTitleColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateSubtitleColorThemeOptions(OldChartActionDataHolder dataHolder, String key){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSubtitleColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setSubtitleColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateDataLabelsColorThemeOptions(OldChartActionDataHolder dataHolder, String key){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSharedSeriesDataLabelsFontColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setSharedSeriesDataLabelsFontColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateLegendColorThemeOptions(OldChartActionDataHolder dataHolder, String key){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setLegendFontColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setLegendFontColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateXAxisLabelsColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int xIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setXAxisLabelsFontColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), xIndex);
            SheetChartAPI.setXAxisLabelsFontColorTone(sheetMeta, colorThemeOptions.getSecondParam(), xIndex);
        }
    }

    public static void updateYAxisLabelsColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int yIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.%d.color", key, yIndex);                                                            // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setYAxisLabelsFontColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), yIndex);
            SheetChartAPI.setYAxisLabelsFontColorTone(sheetMeta, colorThemeOptions.getSecondParam(), yIndex);
        }
    }

    public static void updateXAxisTitleColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int xIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setXAxisTitleFontColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), xIndex);
            SheetChartAPI.setXAxisTitleFontColorTone(sheetMeta, colorThemeOptions.getSecondParam(), xIndex);
        }
    }

    public static void updateYAxisTitleColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int yIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.%d.color", key, yIndex);                                                            // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setYAxisTitleFontColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), yIndex);
            SheetChartAPI.setYAxisTitleFontColorTone(sheetMeta, colorThemeOptions.getSecondParam(), yIndex);
        }
    }

    public static void updateCaptionColorThemeOptions(OldChartActionDataHolder dataHolder, String key){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setCaptionFontColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setCaptionFontColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateTotalDataLabelsColorThemeOptions(OldChartActionDataHolder dataHolder, String key){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setTotalDataLabelsFontColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setTotalDataLabelsFontColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateXAxisMajorGLColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int xIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setXAxisMajorGLColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), xIndex);
            SheetChartAPI.setXAxisMajorGLColorTone(sheetMeta, colorThemeOptions.getSecondParam(), xIndex);
        }
    }

    public static void updateYAxisMajorGLColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int yIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setYAxisMajorGLColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), yIndex);
            SheetChartAPI.setYAxisMajorGLColorTone(sheetMeta, colorThemeOptions.getSecondParam(), yIndex);
        }
    }

    public static void updateXAxisMinorGLColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int xIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setXAxisMinorGLColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), xIndex);
            SheetChartAPI.setXAxisMinorGLColorTone(sheetMeta, colorThemeOptions.getSecondParam(), xIndex);
        }
    }

    public static void updateYAxisMinorGLColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int yIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setYAxisMinorGLColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), yIndex);
            SheetChartAPI.setYAxisMinorGLColorTone(sheetMeta, colorThemeOptions.getSecondParam(), yIndex);
        }
    }

    public static void updateChartBackgroundColorThemeOptions(OldChartActionDataHolder dataHolder, String key){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setBackgroundColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setBackgroundColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateChartBorderColorThemeOptions(OldChartActionDataHolder dataHolder, String key){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setChartBorderColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setChartBorderColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateSeriesColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int sIndex, int seriesCount){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.%d.color", key, sIndex);                                                            // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            if(sIndex >= seriesCount){
                SheetChartAPI.setTrendlineColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), sIndex % seriesCount);
                SheetChartAPI.setTrendlineColorTone(sheetMeta, colorThemeOptions.getSecondParam(), sIndex % seriesCount);
            } else if(sIndex == seriesCount - 1){
                SheetChartAPI.setTargetColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), sIndex);
                SheetChartAPI.setTargetColorTone(sheetMeta, colorThemeOptions.getSecondParam(), sIndex);
            } else {
                SheetChartAPI.setSeriesColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), sIndex);
                SheetChartAPI.setSeriesColorTone(sheetMeta, colorThemeOptions.getSecondParam(), sIndex);
            }
        }
    }

    public static void updateSharedSeriesThresholdColorThemeOptions(OldChartActionDataHolder dataHolder, String key){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, key);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSharedSeriesNegativeColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setSharedSeriesNegativeColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateSeriesBorderColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int sIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.%d.color", key, sIndex);                                                            // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSeriesBorderColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), sIndex);
            SheetChartAPI.setSeriesBorderColorTone(sheetMeta, colorThemeOptions.getSecondParam(), sIndex);
        }
    }

    public static void updateSeriesBorderColorThemeOptionsWithCount(OldChartActionDataHolder dataHolder, String key, int sCount){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.0.color", key);                                                            // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSeriesBorderColorAccentWithCount(sheetMeta, colorThemeOptions.getFirstParam(), sCount);
            SheetChartAPI.setSeriesBorderColorToneWithCount(sheetMeta, colorThemeOptions.getSecondParam(), sCount);
        }
    }

    public static void updatePlotlineLabelColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int yIndex, int plIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.%d.%d.color", key, yIndex, plIndex);                                       // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setYAxisPlotlineLabelFontColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), yIndex, plIndex);
            SheetChartAPI.setYAxisPlotlineLabelFontColorTone(sheetMeta, colorThemeOptions.getSecondParam(), yIndex, plIndex);
        }
    }

    public static void updatePlotlineColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int yIndex, int plIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.%d.%d.color", key, yIndex, plIndex);                                       // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setYAxisPlotlineColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), yIndex, plIndex);
            SheetChartAPI.setYAxisPlotlineColorTone(sheetMeta, colorThemeOptions.getSecondParam(), yIndex, plIndex);
        }
    }

    public static void updateMarkerFillColorThemeOptions(OldChartActionDataHolder dataHolder, String key, int seriesIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.%d.color", key, seriesIndex);                                       // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setMarkerColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), seriesIndex);
            SheetChartAPI.setMarkerColorTone(sheetMeta, colorThemeOptions.getSecondParam(), seriesIndex);
        }
    }

    public static void updateMarkerBorderColorThemeOptions(OldChartActionDataHolder dataHolder, int seriesIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.%d.color", MARKER_BORDER_COLOR, seriesIndex);                                       // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setMarkerBorderColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), seriesIndex);
            SheetChartAPI.setMarkerBorderColorTone(sheetMeta, colorThemeOptions.getSecondParam(), seriesIndex);
        }
    }

    public static void updateMarkerFillColorThemeOptionsWithCount(OldChartActionDataHolder dataHolder, int seriesCount){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.0.color", MARKER_FILL_COLOR);                                       // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setMarkerColorAccentWithCount(sheetMeta, colorThemeOptions.getFirstParam(), seriesCount);
            SheetChartAPI.setMarkerColorToneWithCount(sheetMeta, colorThemeOptions.getSecondParam(), seriesCount);
        }
    }

    public static void updateMarkerBorderColorThemeOptionsWithCount(OldChartActionDataHolder dataHolder, int seriesCount){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        String path = String.format("%s.0.color", MARKER_BORDER_COLOR);                                       // NO I18N
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsFromPath(actionJSON, path);

        if(colorThemeOptions != null) {
            SheetChartAPI.setMarkerBorderColorAccentWithCount(sheetMeta, colorThemeOptions.getFirstParam(), seriesCount);
            SheetChartAPI.setMarkerBorderColorToneWithCount(sheetMeta, colorThemeOptions.getSecondParam(), seriesCount);
        }
    }

    public static void updateStackLabelsColorThemeOptions(OldChartActionDataHolder dataHolder, int yIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, STACK_LABELS);

        if(colorThemeOptions != null) {
            SheetChartAPI.setYAxisStackLabelsColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), yIndex);
            SheetChartAPI.setYAxisStackLabelsColorTone(sheetMeta, colorThemeOptions.getSecondParam(), yIndex);
        }
    }

    public static void updateSeriesMeanColorThemeOptions(OldChartActionDataHolder dataHolder, int seriesIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, MEAN_COLOR);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSeriesMeanColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), seriesIndex);
            SheetChartAPI.setSeriesMeanColorTone(sheetMeta, colorThemeOptions.getSecondParam(), seriesIndex);
        }
    }

    public static void updateSeriesMedianColorThemeOptions(OldChartActionDataHolder dataHolder, int seriesIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, MEDIAN_COLOR);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSeriesMedianColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), seriesIndex);
            SheetChartAPI.setSeriesMedianColorTone(sheetMeta, colorThemeOptions.getSecondParam(), seriesIndex);
        }
    }

    public static void updateSeriesOutliersColorThemeOptions(OldChartActionDataHolder dataHolder, int seriesIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, OUTLIERS_COLOR);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSeriesOutlierColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), seriesIndex);
            SheetChartAPI.setSeriesOutlierColorTone(sheetMeta, colorThemeOptions.getSecondParam(), seriesIndex);
        }
    }

    public static void updateSeriesWhiskersColorThemeOptions(OldChartActionDataHolder dataHolder, int seriesIndex){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, WHISKERS_COLOR);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSeriesWhiskerColorAccent(sheetMeta, colorThemeOptions.getFirstParam(), seriesIndex);
            SheetChartAPI.setSeriesWhiskerColorTone(sheetMeta, colorThemeOptions.getSecondParam(), seriesIndex);
        }
    }

    public static void updateSharedSeriesMeanColorThemeOptions(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, MEAN_COLOR);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSharedSeriesMeanColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setSharedSeriesMeanColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateSharedSeriesMedianColorThemeOptions(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, MEDIAN_COLOR);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSharedSeriesMedianColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setSharedSeriesMedianColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateSharedSeriesOutliersColorThemeOptions(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, OUTLIERS_COLOR);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSharedSeriesOutliersColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setSharedSeriesOutliersColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

    public static void updateSharedSeriesWhiskersColorThemeOptions(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper actionJSON = dataHolder.getActionJSON();
        Tuple<String, Double> colorThemeOptions = getColorThemeOptionsForOldAction(actionJSON, WHISKERS_COLOR);

        if(colorThemeOptions != null) {
            SheetChartAPI.setSharedSeriesWhiskerColorAccent(sheetMeta, colorThemeOptions.getFirstParam());
            SheetChartAPI.setSharedSeriesWhiskerColorTone(sheetMeta, colorThemeOptions.getSecondParam());
        }
    }

}
