package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.chartstyles;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OldChartStyleJSON {

    public XAxis xAxis;
    public DefaultColorsState defaultColorsState;
    public DefaultColors defaultColors;
    public FontStyles caption;
    @JsonProperty("chartSubTitle") public TitleStyles subtitle;                             // NO I18N
    public BoxPlotOptions boxPlotOptions;
    public YAxis yAxis;
    public ColorObject colorObject;
    @JsonProperty("chartTitle") public TitleStyles title;                                   // NO I18N
    @JsonProperty("chartLegend") public FontStyles legend;                                  // NO I18N
    @JsonProperty("borderObject") public SeriesBorderColors seriesBorderColors;             // NO I18N
    public Chart chart;
    @JsonProperty("chartGridlines") public ChartGridlines gridlines;                        // NO I18N

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FontStyles {
        public String color;
        public String fontSize;
        public String fontWeight;
        public String fontStyle;
    }

    public static class XAxis {
        public FontStyles labelStyle;
        @JsonProperty("style") public FontStyles titleStyles;                               // NO I18N

        public FontStyles getLabelStyle() {
            return labelStyle == null ? new FontStyles() : labelStyle;
        }

        public FontStyles getTitleStyles() {
            return titleStyles == null ? new FontStyles() : titleStyles;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DefaultColorsState{
        @JsonProperty("X_GRID_LINES")                                                                                           // NO I18N
        public boolean xGridLinesColorState;
        @JsonProperty("X_MINOR_GRID_LINES")                                                                                     // NO I18N
        public boolean xMinorGridLinesColorState;
        @JsonProperty("SUBTITLE")                                                                                               // NO I18N
        public boolean subtitleColorState;
        @JsonProperty("LEGEND")                                                                                                 // NO I18N
        public boolean legendColorState;
        @JsonProperty("TOTAL_DATALABELS")                                                                                       // NO I18N
        public boolean totalDataLabelsColorState;
        @JsonProperty("X_TITLE")                                                                                                // NO I18N
        public boolean xTitleColorState;
        @JsonProperty("GRID_LINES")                                                                                             // NO I18N
        public boolean gridLinesColorState;
        @JsonProperty("DataLabels")                                                                                             // NO I18N
        public boolean dataLabelsColorState;
        @JsonProperty("TITLE")                                                                                                  // NO I18N
        public boolean titleColorState;
        @JsonProperty("Y_TITLE")                                                                                                // NO I18N
        public boolean yTitleColorState;
        @JsonProperty("X_LABELS")                                                                                               // NO I18N
        public boolean xLabelsColorState;
        @JsonProperty("MINOR_GRID_LINES")                                                                                       // NO I18N
        public boolean minorGridLinesColorState;
        @JsonProperty("CAPTION")                                                                                                // NO I18N
        public boolean captionColorState;
        @JsonProperty("Y_LABELS")                                                                                               // NO I18N
        public boolean yLabelColorState;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DefaultColors{
        @JsonProperty("X_GRID_LINES")                                                                                           // NO I18N
        public String xGridLinesColor;
        @JsonProperty("X_MINOR_GRID_LINES")                                                                                     // NO I18N
        public String xMinorGridLinesColor;
        @JsonProperty("SUBTITLE")                                                                                               // NO I18N
        public String subTitleColor;
        @JsonProperty("LEGEND")                                                                                                 // NO I18N
        public String legendColor;
        @JsonProperty("TOTAL_DATALABELS")                                                                                       // NO I18N
        public String totalDataLabelsColor;
        @JsonProperty("X_TITLE")                                                                                                // NO I18N
        public String xTitleColor;
        @JsonProperty("GRID_LINES")                                                                                             // NO I18N
        public String gridLinesColor;
        @JsonProperty("DATALABELS")                                                                                             // NO I18N
        public String dataLabelsColor;
        @JsonProperty("TITLE")                                                                                                  // NO I18N
        public String titleColor;
        @JsonProperty("X_LABELS")                                                                                               // NO I18N
        public String xLabelsColor;
        @JsonProperty("MINOR_GRID_LINES")                                                                                       // NO I18N
        public String minorGridLinesColor;
        @JsonProperty("CAPTION")                                                                                                // NO I18N
        public String captionColor;
        @JsonProperty("Y_LABELS")                                                                                               // NO I18N
        public String yLabelsColor;
    }


    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TitleStyles {
        public FontStyles style;

        public FontStyles getStyle() {
            return style == null ? new FontStyles() : style;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BoxPlotOptions {
        public List<String> outliersColor;
        public List<String> meanColor;
        public List<String> whiskersColor;
        public List<String> medianColor;

        public List<String> getOutliersColor() {
            return outliersColor == null ? Collections.emptyList() : outliersColor;
        }

        public List<String> getMeanColor() {
            return meanColor == null ? Collections.emptyList() : meanColor;
        }

        public List<String> getWhiskersColor() {
            return whiskersColor == null ? Collections.emptyList() : whiskersColor;
        }

        public List<String> getMedianColor() {
            return medianColor == null ? Collections.emptyList() : medianColor;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class YAxes  {
        public String gridLineColor;
        public FontStyles labelStyles;
        public String index;
        public FontStyles style;

        public FontStyles getStyle() {
            return style == null ? new FontStyles() : style;
        }

        public FontStyles getLabelStyles() {
            return labelStyles == null ? new FontStyles() : labelStyles;
        }

        public String getIndex() {
            return index == null ? "0" : index;                             // NO I18N
        }
    }

    public static class YAxisDeserializer extends StdDeserializer<YAxis> {

        public YAxisDeserializer() {
            this(null);
        }

        @Override
        public YAxis deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) {
            try {
                YAxis yAxis = new YAxis();
                ArrayNode yAxisNode = jsonParser.getCodec().readTree(jsonParser);
                for(JsonNode yAxesNode : yAxisNode) {
                    YAxes yAxes = new JsonMapper().readValue(yAxesNode.traverse(), YAxes.class);
                    yAxis.addYAxes(yAxes);
                }
                return yAxis;
            } catch (Exception e) {
                return new YAxis();
            }
        }

        public YAxisDeserializer(Class<?> vc) {
            super(vc);
        }


    }

    @JsonDeserialize(using = YAxisDeserializer.class)
    public static class YAxis {
        private final List<YAxes> yAxesList = new ArrayList<>();

        public List<YAxes> getYAxesList() {
            return yAxesList;
        }

        public void addYAxes(YAxes yAxes) {
            yAxesList.add(yAxes);
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ColorObject {
        public FontStyles dataLabels;
        public SeriesColors seriesColors;
        public Gradient gradient;
        public ColorTheme colorTheme;
        public FontStyles dataLabelsTotal;
        public String thresholdColor;
        public PointColors pointColors;

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Gradient{
            public Boolean enabled;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class ColorTheme {
            @JsonProperty("basecolor") public Object baseColor;             // NO I18N
            public String name;
            public Boolean isReversed;
        }

        public FontStyles getDataLabelsTotal() {
            return dataLabelsTotal == null ? new FontStyles() : dataLabelsTotal;
        }

        public SeriesColors getSeriesColors() {
            return seriesColors == null ? new SeriesColors() : seriesColors;
        }

        public Gradient getGradient() {
            return gradient == null ? new Gradient() : gradient;
        }
        public ColorTheme getColorTheme() {
            return colorTheme == null ? new ColorTheme() : colorTheme;
        }

        public FontStyles getDataLabels() {
            return dataLabels == null ? new FontStyles() : dataLabels;
        }

        public PointColors getPointColors() {
            return pointColors == null ? new PointColors() : pointColors;
        }
    }

    public static class SeriesColorsDeserializer extends StdDeserializer<SeriesColors> {

        public SeriesColorsDeserializer() {
            this(null);
        }

        public SeriesColorsDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public SeriesColors deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
            try {
                 SeriesColors seriesColors = new SeriesColors();
                 ArrayNode seriesColorsNode = jsonParser.getCodec().readTree(jsonParser);

                 for(JsonNode seriesColorNode : seriesColorsNode) {
                     SeriesColor seriesColor = new JsonMapper().readValue(seriesColorNode.traverse(), SeriesColor.class);
                     seriesColors.addColor(seriesColor);
                 }
                 return seriesColors;
            } catch (Exception e) {
                return new SeriesColors();
            }
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SeriesColor {
        public String color;
        public Integer sIndex;
    }

    @JsonDeserialize(using = SeriesColorsDeserializer.class)
    public static class SeriesColors {
        private final List<SeriesColor> colors = new ArrayList<>();

        public List<SeriesColor> getColors() {
            return colors;
        }

        public void addColor(SeriesColor color) {
            colors.add(color);
        }
    }

    public static class SeriesBorderColorsDeserializer extends StdDeserializer<SeriesBorderColors> {

        public SeriesBorderColorsDeserializer() {
            this(null);
        }

        public SeriesBorderColorsDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public SeriesBorderColors deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
            try {
                SeriesBorderColors seriesBorderColors = new SeriesBorderColors();
                ObjectNode borderColorsNode = jsonParser.getCodec().readTree(jsonParser);
                for(Iterator<String> iterator = borderColorsNode.fieldNames(); iterator.hasNext();) {
                    String key = iterator.next();
                    int seriesIndex = Integer.parseInt(key);
                    String color = borderColorsNode.get(key).asText();
                    SeriesBorderColor borderColor = new SeriesBorderColor(seriesIndex, color);

                    seriesBorderColors.addColor(borderColor);
                }
                return seriesBorderColors;
            } catch (Exception e) {
                return new SeriesBorderColors();
            }
        }


    }

    @JsonDeserialize(using = SeriesBorderColorsDeserializer.class)
    public static class SeriesBorderColors {

        private final List<SeriesBorderColor> colors = new ArrayList<>();

        public List<SeriesBorderColor> getColors() {
            return colors;
        }
        public void addColor(SeriesBorderColor color) {
            colors.add(color);
        }
    }

    public static class SeriesBorderColor {

        private final int seriesIndex;

        private final String color;

        public SeriesBorderColor(int seriesIndex, String color) {
            this.seriesIndex = seriesIndex;
            this.color = color;
        }

        public int getSeriesIndex() {
            return seriesIndex;
        }

        public String getColor() {
            return color;
        }
    }

    public static class Chart {
        public String backgroundColor;
        public String borderColor;
        public String plotBackgroundColor;
        public Style style;

        public static class Style {
            public String fontFamily;
            public Boolean isFromTheme;
        }

        public Style getStyle() {
            return style == null ? new Style() : style;
        }

    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ChartGridlines {
        public AxisGridlines yAxis;
        public AxisGridlines xAxis;

        public AxisGridlines getyAxis() {
            return yAxis == null ? new AxisGridlines() : yAxis;
        }

        public AxisGridlines getxAxis() {
            return xAxis == null ? new AxisGridlines() : xAxis;
        }
    }

    public static class AxisGridlinesDeserializer extends StdDeserializer<AxisGridlines> {
        public AxisGridlinesDeserializer() {
            this(null);
        }

        public AxisGridlinesDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public AxisGridlines deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
            try {
                AxisGridlines axisGridlines = new AxisGridlines();
                ArrayNode axisGridlinesNode = jsonParser.getCodec().readTree(jsonParser);
                for(JsonNode axesGridlinesNode : axisGridlinesNode) {
                    AxesGridlines axesGridlines = new JsonMapper().readValue(axesGridlinesNode.traverse(), AxesGridlines.class);

                    axisGridlines.addGridline(axesGridlines);
                }
                return axisGridlines;
            } catch (Exception e) {
                return new AxisGridlines();
            }
        }


    }

    @JsonDeserialize(using = AxisGridlinesDeserializer.class)
    public static class AxisGridlines {
        private final List<AxesGridlines> gridlines = new ArrayList<>();

        public List<AxesGridlines> getGridlines() {
            return gridlines;
        }

        public void addGridline(AxesGridlines gridline) {
            gridlines.add(gridline);
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AxesGridlines {
        public Boolean majorGridlineEnabled;
        public Boolean minorGridlineEnabled;
        public String minorGridlineColor;
        public String majorGridlineColor;
        public String majorGridlineCountType;
        public Integer majorGridlineCount;
    }

    public static class PointColorsDeserializer extends StdDeserializer<PointColors> {
        public PointColorsDeserializer(Class<?> vc) {
            super(vc);
        }

        public PointColorsDeserializer() {
            this(null);
        }

        @Override
        public PointColors deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
            try {
                PointColors pointColors = new PointColors();
                ArrayNode pointColorsNode = jsonParser.getCodec().readTree(jsonParser);

                for(JsonNode pointColorNode : pointColorsNode) {
                    PointColor pointColor = new JsonMapper().readValue(pointColorNode.traverse(), PointColor.class);

                    pointColors.addColor(pointColor);
                }
                return pointColors;
            } catch (Exception e) {
                return new PointColors();
            }
        }

    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonDeserialize(using = PointColorsDeserializer.class)
    public static class PointColors {
        private final List<PointColor> pointColors = new ArrayList<>();

        public List<PointColor> getPointColors() {
            return pointColors;
        }

        public void addColor(PointColor pointColor) {
            pointColors.add(pointColor);
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PointColor {
        @JsonProperty("sIndex") public Integer seriesIndex;                                     // NO I18N
        public Points points;

        public Points getPoints() {
            return points == null ? new Points() : points;
        }
    }

    public static class PointsDeserializer extends StdDeserializer<Points> {

        public PointsDeserializer() {
            this(null);
        }

        public PointsDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public Points deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) {
            try {
                Points points = new Points();
                ArrayNode pointsNode = (ArrayNode) deserializationContext.readTree(jsonParser);

                for(JsonNode pointNode : pointsNode) {
                    Point point = new JsonMapper().readValue(pointNode.traverse(), Point.class);
                    points.addPoint(point);
                }

                return points;
            } catch (Exception e) {
                return new Points();
            }
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonDeserialize(using = PointsDeserializer.class)
    public static class Points {
        private final List<Point> points = new ArrayList<>();

        public List<Point> getPoints() {
            return points;
        }

        public void addPoint(Point point) {
            points.add(point);
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Point {
        public String color;
        @JsonProperty("pIndex") public Integer dataPropsIndex;                                      // NO I18N
    }

    public XAxis getxAxis() {
        return xAxis == null ? new XAxis() : xAxis;
    }

    public DefaultColorsState getDefaultColorsState() {
        return defaultColorsState == null ? new DefaultColorsState() : defaultColorsState;
    }

    public DefaultColors getDefaultColors() {
        return defaultColors == null ? new DefaultColors() : defaultColors;
    }

    public FontStyles getCaption() {
        return caption == null ? new FontStyles() : caption;
    }

    public TitleStyles getSubtitle() {
        return subtitle == null ? new TitleStyles() : subtitle;
    }

    public BoxPlotOptions getBoxPlotOptions() {
        return boxPlotOptions == null ? new BoxPlotOptions() : boxPlotOptions;
    }

    public YAxis getyAxis() {
        return yAxis == null ? new YAxis() : yAxis;
    }

    public ColorObject getColorObject() {
        return colorObject == null ? new ColorObject() : colorObject;
    }

    public TitleStyles getTitle() {
        return title == null ? new TitleStyles() : title;
    }

    public FontStyles getLegend() {
        return legend == null ? new FontStyles() : legend;
    }

    public SeriesBorderColors getSeriesBorderColors() {
        return seriesBorderColors == null ? new SeriesBorderColors() : seriesBorderColors;
    }

    public Chart getChart() {
        return chart == null ? new Chart() : chart;
    }

    public ChartGridlines getGridlines() {
        return gridlines == null ? new ChartGridlines() : gridlines;
    }
}
