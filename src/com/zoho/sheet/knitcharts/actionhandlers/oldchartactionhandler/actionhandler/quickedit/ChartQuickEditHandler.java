package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.quickedit;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.DataLabelPositionType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.LegendPositionType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.SortOrderType;
import com.zoho.sheet.knitcharts.miscellaneous.StateFullLambda;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionConsumer;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.OldChartActionDataHolder;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;

import java.util.Objects;

/**
 * Chart Quick edit action handler
 * <AUTHOR>
 */
public class ChartQuickEditHandler {

    private static LegendPositionType[] legendPositionMap;

    /**
     * Handles Old Chart Quick edit action
     * @param dataHolder Data holder the action
     */
    public static void handle(OldChartActionDataHolder dataHolder){
        int subAction = dataHolder.getActionJSON().getInt(OldChartActionConstants.JSONConstant.SUB_ACTION);
        FunctionConsumer<OldChartActionDataHolder> actionHandler = ChartQuickEditHandlerMap.map(subAction);

        // Null check is added for development purpose once development completed it should be removed.
        if(actionHandler != null){
            actionHandler.consume(dataHolder);
        }
    }

    static void updateLegend(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);
        LegendPositionType position = getLegendPosition(Integer.parseInt(value));

        FrameworkChartAPI.updateLegendStatus(chartMeta, position != null);
        FrameworkChartAPI.updateChartLegendPosition(chartMeta, position);
    }

    static void updateDataLabelFormat(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String formatString = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        FrameworkChartAPI.addSharedSeriesDataLabelsFormats(chartMeta, StateFullLambda.retrieveDataLabelFormat(formatString));
        FrameworkChartAPI.updateSharedSeriesDataLabelsStatus(chartMeta, !Objects.equals("N", formatString));        // NO I18N
    }

    static void updateDataLabelPosition(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);
        DataLabelPositionType positionType = ChartUtils.getNewDataLabelsPosition(Integer.parseInt(value));

        FrameworkChartAPI.updateSharedSeriesDataLabelsPosition(chartMeta, positionType);
    }

    static void updateTooltipStatus(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        FrameworkChartAPI.updateTooltipStatus(chartMeta, Boolean.parseBoolean(value));
    }

    static void updateAnimationStatus(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        FrameworkChartAPI.updateAnimationStatus(chartMeta, Boolean.parseBoolean(value));
    }

    static void updateTrendlineType(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        if(value.equals("none")){
            FrameworkChartAPI.updateSharedSeriesTrendLineType(chartMeta, null);
            FrameworkChartAPI.updateSharedSeriesTrendLineStatus(chartMeta, false);
        }else{
            FrameworkChartAPI.updateSharedSeriesTrendLineType(chartMeta, ChartUtils.getNewTrendLineType(value));
            FrameworkChartAPI.updateSharedSeriesTrendLineStatus(chartMeta, true);
        }
    }

    static void updateTrendlineOrderValue(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        FrameworkChartAPI.updateSharedSeriesTrendLineOrder(chartMeta, Integer.parseInt(value));
    }

    static void updateSortOrder(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        FrameworkChartAPI.updateSortOrder(chartMeta, SortOrderType.retrieveByValue(value), 0);
    }

    static void updateCumulationStatus(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        FrameworkChartAPI.updateSharedSeriesCumulateStatus(chartMeta, Boolean.parseBoolean(value));
    }

    static void updateChartType(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        updateChartOrSeriesType(chartMeta, value);
    }

    static void updateAutoExpandStatus(OldChartActionDataHolder dataHolder){
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);
        boolean status = Boolean.parseBoolean(value);

        SheetChartAPI.updateAutoExpand(sheetMeta, status);
    }

    static void updateTotalDataLabelsStatus(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        FrameworkChartAPI.updateYAxisStackLabelsStatus(chartMeta, ChartUtils.toBoolean(value), 0);
    }

    static void updateGroupHeadersStatus(OldChartActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String value = dataHolder.getActionJSON().getString(OldChartActionConstants.JSONConstant.VALUE);

        FrameworkChartAPI.updateGroupHeaders(chartMeta, ChartUtils.toBoolean(value));
    }


    /**
     * Retrieves new Chart legend position for the given old chart legend position.
     * @param position old chart legend position
     * @return new chart legend position
     */
    private static LegendPositionType getLegendPosition(int position){
        if(legendPositionMap == null){
            legendPositionMap = new LegendPositionType[]{null, LegendPositionType.BOTTOM, LegendPositionType.TOP, LegendPositionType.RIGHT,
            LegendPositionType.LEFT, LegendPositionType.TOP_RIGHT};
        }
        return legendPositionMap[position];
    }

    private static void updateChartOrSeriesType(ChartMeta chartMeta, String value){
        ChartType chartType = FrameworkChartAPI.getChartType(chartMeta);
        String[] chartTypes = value.split(",");             // NO I18N

        if(chartType == ChartType.COMBO && chartTypes.length > 1) {
            for(int i = 0; i < chartTypes.length; i++){
                FrameworkChartAPI.updateSeriesChartType(chartMeta, ChartUtils.getChartForOldChartName(chartTypes[i]), i);
            }
        } else {
            FrameworkChartAPI.updateChartType(chartMeta, ChartUtils.getChartForOldChartName(value));
        }
    }
}
