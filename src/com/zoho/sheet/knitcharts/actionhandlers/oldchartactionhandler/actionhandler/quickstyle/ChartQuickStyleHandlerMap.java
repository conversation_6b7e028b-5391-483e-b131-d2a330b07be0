package com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.quickstyle;

import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionConsumer;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.actionhandler.OldChartActionDataHolder;

import static com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants.QuickStyleSubActionConstant.*;
import static com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants.QuickStyleSubActionConstant.DATALABEL_TOTALS_STYLE;

public class ChartQuickStyleHandlerMap {

    public static FunctionConsumer<OldChartActionDataHolder> map(int subAction){
        switch (subAction) {
            case CHART_TITLE: return ChartQuickStyleHandler::updateChartTitle;
            case CHART_TITLE_STYLE: return ChartQuickStyleHandler::updateChartTitleStyle;
            case CHART_TITLE_POSITION: return ChartQuickStyleHandler::updateChartTitlePosition;
            case CHART_SUBTITLE_POSITION: return ChartQuickStyleHandler::updateChartSubtitlePosition;
            case CHART_SUBTITLE: return ChartQuickStyleHandler::updateChartSubtitle;
            case CHART_SUBTITLE_STYLE: return ChartQuickStyleHandler::updateChartSubtitleStyle;
            case CHART_LEGEND_STYLE: return ChartQuickStyleHandler::updateLegendStyle;
            case UPDATE_DATALABEL_STYLE: return ChartQuickStyleHandler::updateDataLabelsStyle;
            case CHART_BACKGROUND_COLOR: return ChartQuickStyleHandler::updateChartBackgroundColor;
            case CHART_BORDER_COLOR: return ChartQuickStyleHandler::updateChartBorderColor;
            case GRADIENT: return ChartQuickStyleHandler::updateGradientStatus;
            case REVERSE: return ChartQuickStyleHandler::updateReverseStatus;
            case ENABLE_LABELS: return ChartQuickStyleHandler::enableLabels;
            case CHART_SLANT: return ChartQuickStyleHandler::updateXLabelsSlantAngle;
            case STAGGER_LINES: return ChartQuickStyleHandler::updateXLabelsStaggerLines;
            case CHART_XAXIS_LABEL_STYLE: return ChartQuickStyleHandler::updateXLabelsStyle;
            case CHART_GRIDLINES: return ChartQuickStyleHandler::updateChartGridlines;
            case CHART_DEFAULT: return ChartQuickStyleHandler::updateChartDefaults;
            case COLOR_PALLATE: return ChartQuickStyleHandler::updateChartColors;
            case CHART_YAXIS_LABEL_STYLE: return ChartQuickStyleHandler::updateYAxisLabelStyle;
            case LOGARITHMIC_SCALE: return ChartQuickStyleHandler::updateYAxisScaleType;
            case BASE_VALUE: return ChartQuickStyleHandler::updateYAxisBaseValue;
            case PLOTLINE_TITLE: return ChartQuickStyleHandler::updatePlotlineTitle;
            case ADD_PLOTLINE: return ChartQuickStyleHandler::updatePlotlineValue;
            case PLOTLINE_TITLE_STYLE: return ChartQuickStyleHandler::updatePlotlineTitleStyle;
            case PLOTLINE_LABEL_STYLE: return ChartQuickStyleHandler::updatePlotlineLineStyle;
            case MIN: return ChartQuickStyleHandler::updateMinValue;
            case MAX: return ChartQuickStyleHandler::updateMaxValue;
            case INTERVAL: return ChartQuickStyleHandler::updateIntervalValue;
            case UPDATE_SCALE_FACTOR: return ChartQuickStyleHandler::updateYAxisScaleFactor;
            case UPDATE_NUMBER_FORMAT: return ChartQuickStyleHandler::updateNumberFormat;
            case THRESHOLD_VALUE: return ChartQuickStyleHandler::updateThresholdValue;
            case THRESHOLD_COLOR: return ChartQuickStyleHandler::updateThresholdColor;
            case SERIES_COLOR: return ChartQuickStyleHandler::updateSeriesColor;
            case SERIES_BORDER: return ChartQuickStyleHandler::updateSeriesBorder;
            case MARKER_ENABLED: return ChartQuickStyleHandler::updateMarkerStatus;
            case MARKER_SYMBOL: return ChartQuickStyleHandler::updateMarkerShape;
            case MARKER_SIZE: return ChartQuickStyleHandler::updateMarkerSize;
            case MARKER_FILL_COLOR: return ChartQuickStyleHandler::updateMarkerFillColor;
            case MARKER_BORDER_COLOR: return ChartQuickStyleHandler::updateMarkerBorderColor;
            case SERIES_PROPS: return ChartQuickStyleHandler::updateSeriesProps;
            case SLICE_START_ANGLE: return ChartQuickStyleHandler::updateSliceStartAngle;
            case START_ANGLE: return ChartQuickStyleHandler::updateStartAngle;
            case END_ANGLE: return ChartQuickStyleHandler::updateEndAngle;
            case ENABLE_CAPTION: return ChartQuickStyleHandler::updateCaptionStatus;
            case BAR_COUNT: return ChartQuickStyleHandler::updateBarCount;
            case XAXIS_TITLE: return ChartQuickStyleHandler::updateXAxisTitle;
            case YAXIS_TITLE: return ChartQuickStyleHandler::updateYAxisTitle;
            case XAXIS_TITLE_STYLE: return ChartQuickStyleHandler::updateXAxisTitleStyle;
            case YAXIS_TITLE_STYLE: return ChartQuickStyleHandler::updateYAxisTitleStyle;
            case PLOTLINE_REMOVE: return ChartQuickStyleHandler::removePlotLines;
            case CAPTION_FONTCOLOR:
            case CAPTION_FONTSTYLE:
            case CAPTION_FONTWEIGHT:
            case CAPTION_FONTSIZE:
                return ChartQuickStyleHandler::updateCaptionStyle;
            case DATALABEL_TOTALS_STYLE: return ChartQuickStyleHandler::updateTotalDataLabelsFontStyle;
            case FILTER: return ChartQuickStyleHandler::updateFilterOptions;
            case AGGREGATION: return ChartQuickStyleHandler::updateAggregationOptions;
            case SHOW_OUTLIERS: return ChartQuickStyleHandler::updateShowOutliers;
            case SHOW_MEAN: return ChartQuickStyleHandler::updateMeanMarkerStatus;
            case MEAN_COLOR: return ChartQuickStyleHandler::updateMeanColor;
            case MEDIAN_COLOR: return ChartQuickStyleHandler::updateMedianColor;
            case OUTLIERS_COLOR: return ChartQuickStyleHandler::updateOutliersColor;
            case WHISKERS_COLOR: return ChartQuickStyleHandler::updateWhiskersColor;
            case MULTIPLE_Y_AXES: return ChartQuickStyleHandler::updateMultipleYAxisStatus;
            case CHART_STYLE_RESET: return ChartQuickStyleHandler::resetChartStyles;
        }
        return null;
    }


}
