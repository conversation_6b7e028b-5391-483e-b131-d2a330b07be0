package com.zoho.sheet.knitcharts.actionhandlers.useraction;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.utils.ChartsDataGenerator;
import com.zoho.sheet.knitcharts.chartcache.ChartCacheInterceptor;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsDAO;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ChartException;
import com.zoho.sheet.knitcharts.recommendation.ChartRecommendationAnalyzer;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class ChartUserActionHandler {

    /* Singleton instance */
    private static ChartUserActionHandler instance;

    private ChartUserActionHandler() {}

    /**/
    public List<JSONObjectWrapper> recommendCharts(Sheet activeSheet, JSONObjectWrapper actionJSON) {
        List<String> rangesString = ChartUtils.base64Decode(ChartUtils.JSONArrayToList(actionJSON.getJSONArray(ChartActionConstants.JSONConstants.DATA_SOURCES)));
        ChartRecommendationAnalyzer analyzer = new ChartRecommendationAnalyzer(activeSheet);

        try {
            List<Range> ranges = rangesString.stream()
                    .map(rangeString -> {
                        try {
                            return new Range(activeSheet.getWorkbook(), rangeString, null, CellReference.ReferenceMode.A1, false);
                        } catch (SheetEngineException e) {
                            throw new ChartException(e.getMessage());
                        }
                    })
                    .collect(Collectors.toList());

            return analyzer.recommendCharts(ranges);
        }
        catch (ChartException e) {
            return Collections.emptyList();
        }
    }

    public JSONObjectWrapper copyChartStyle(Sheet activeSheet, JSONObjectWrapper actionJSON) {
        ChartContainer chartContainer = activeSheet.getWorkbook().getChartsContainer();
        String chartID = actionJSON.getString(ChartActionConstants.JSONConstants.CHART_ID);

        Chart chart = chartContainer.getChart(chartID);
        JSONWrapper chartMetaStyles = chart.getApiMeta().getChartMeta().extractStyles();
        JSONWrapper sheetMetaStyles = chart.getSheetMeta().extractStyles();

        JSONObjectWrapper stylesJSON = new JSONObjectWrapper();
        ChartUtils.putIfNotNull(stylesJSON, ChartActionConstants.JSONConstants.CHART_META, chartMetaStyles);
        ChartUtils.putIfNotNull(stylesJSON, ChartActionConstants.JSONConstants.SHEET_META, sheetMetaStyles);

        return chart.getStylesJSON();
    }

    public JSONObjectWrapper copyChart(WorkbookContainer container, Sheet activeSheet, JSONObjectWrapper actionJSON) {
        ChartContainer chartContainer = activeSheet.getWorkbook().getChartsContainer();
        String chartID = actionJSON.getString(ChartActionConstants.JSONConstants.CHART_ID);
        String externalChartID = UUID.randomUUID().toString() + System.currentTimeMillis() + ((int) (Math.random() * 1000));
        String resourceID = container.getResourceId();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(container.getDocOwner(), Long.parseLong(container.getDocId()));
        Chart chart = chartContainer.getChart(chartID);
        JSONObjectWrapper chartJSON = ChartsDataGenerator.getChartMetaWithoutData(chart);

        dao.updateChartExternalID(chartID, externalChartID);

        if(chartJSON.has(ChartActionConstants.JSONConstants.SHEET_META)) {
            chartJSON.getJSONObject(ChartActionConstants.JSONConstants.SHEET_META)
                    .remove(SheetChartMetaConstants.PublicChart.KEY);
        }

        chartJSON.put(ChartActionConstants.JSONConstants.EXTERNAL_CHART_ID, externalChartID);
        chartJSON.put(ChartActionConstants.JSONConstants.RID, resourceID);

        return chartJSON;
    }

    public void clearChartRecommendationCache(WorkbookContainer container, Sheet activeSheet, JSONObjectWrapper actionJSON) {
        String userID = actionJSON.getString(JSONConstants.ZUID);
        String rID = container.getResourceKey();
        String asn = activeSheet.getAssociatedName();
        long timeStamp = actionJSON.getLong(ChartActionConstants.JSONConstants.RECOMMENDATION_CACHE_TIME_STAMP);

        ChartCacheInterceptor.clearRecommendationCache(userID, rID, asn, timeStamp);
    }

    public static ChartUserActionHandler getInstance() {
        if (instance == null) {
            instance = new ChartUserActionHandler();
        }
        return instance;
    }
}
