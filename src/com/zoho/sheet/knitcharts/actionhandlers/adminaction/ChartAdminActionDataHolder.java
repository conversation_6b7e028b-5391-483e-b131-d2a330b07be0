package com.zoho.sheet.knitcharts.actionhandlers.adminaction;

import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Data holder implementation for Chart Admin Action
 * <AUTHOR>
 */
public class ChartAdminActionDataHolder {

    private Map<String, String> paramsMap;

    public ChartAdminActionDataHolder(HttpServletRequest req) {
        paramsMap = new HashMap<>();
        for(String paramName: CollectionsUtils.toIterable(req.getParameterNames().asIterator())) {
            paramsMap.put(paramName, req.getParameter(paramName));
        }
    }


    public String getParam(String paramName) {
        return paramsMap.get(paramName);
    }


}
