package com.zoho.sheet.knitcharts.actionhandlers.newchartactionhandler;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.chart.BoxPlotChart;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.LegendPositionType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.MarkerShapeType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.TrendLineType;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.constants.ChartsBCConstants;
import com.zoho.sheet.knitcharts.constants.FrameworkChartActionConstants;
import com.zoho.sheet.knitcharts.constants.SheetChartActionConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.ChartsBCUtils;

import static com.zoho.sheet.knitcharts.constants.FrameworkChartActionConstants.SubActionConstants.*;


public class FrameworkChartActionHandler {

    private static void passItToHandler(Workbook workbook, JSONObjectWrapper chartJSON, int subActionConstant) {
        switch (subActionConstant) {
            case TITLE_EDIT: {
                updateChartTitle(workbook, chartJSON);
                break;
            }
            case TITLE_STYLE: {
                updateChartTitleStyles(workbook, chartJSON);
                break;
            }
            case SUBTITLE_EDIT: {
                updateChartSubtitle(workbook, chartJSON);
                break;
            }
            case SUBTITLE_STYLE: {
                updateChartSubtitleStyle(workbook, chartJSON);
                break;
            }
            case CHART_BORDER_COLOR_EDIT: {
                handleBorderColor(workbook, chartJSON);
                break;
            }
            case X_AXIS_TITLE_EDIT: {
                updateXAxisTitle(workbook, chartJSON);
                break;
            }
            case X_AXIS_TITLE_STYLE: {
                updateXAxisTitleStyles(workbook, chartJSON);
                break;
            }
            case Y_AXIS_TITLE_EDIT: {
                updateYAxisTitle(workbook, chartJSON);
                break;
            }
            case Y_AXIS_TITLE_STYLE: {
                updateYAxisTitleFontStyles(workbook, chartJSON);
                break;
            }
            case X_AXIS_LABEL_STATUS: {
                updateXAxisLabelsStatus(workbook, chartJSON);
                break;
            }
            case X_AXIS_LABEL_STYLE: {
                updateXAxisLabelsFontStyles(workbook, chartJSON);
                break;
            }
            case Y_AXIS_LABEL_STATUS: {
                updateYAxisLabelsStatus(workbook, chartJSON);
                break;
            }
            case Y_AXIS_LABEL_STYLE: {
                updateYAxisLabelsFontStyles(workbook, chartJSON);
                break;
            }
            case Y_AXIS_PLOTLINE_EDIT: {
                updatePlotlineOptions(workbook, chartJSON);
                break;
            }
            case Y_AXIS_REMOVE_PLOTLINE: {
                removePlotlines(workbook, chartJSON);
                break;
            }
            case LEGEND_STATUS_EDIT: {
                updateLegendStatus(workbook, chartJSON);
                break;
            }
            case LEGEND_POSITION: {
                updateLegendPosition(workbook, chartJSON);
                break;
            }
            case DATALABELS_FORMAT: {
                updateDataLabelsFormat(workbook, chartJSON);
                break;
            }
            case DATALABELS_STATUS_EDIT: {
                updateDataLabelsStatus(workbook, chartJSON);
                break;
            }
            case TRENDLINE_STATUS: {
                updateTrendlineStatus(workbook, chartJSON);
                break;
            }
            case SERIES_STYLE: {
                updateSeriesStyles(workbook, chartJSON);
                break;
            }
            case SERIES_DATAPROPERTY_EDIT: {
                updateDataPropertyStyles(workbook, chartJSON);
                break;
            }
            case CHART_BG_COLOR_EDIT: {
                updateChartBgColor(workbook, chartJSON);
                break;
            }
            case CHART_BG_COLOR_OPACITY_EDIT: {
                updateChartBgOpacity(workbook, chartJSON);
                break;
            }
            case TRENDLINE_EDIT: {
                updateTrendlineOptions(workbook, chartJSON);
                break;
            }
            case TRENDLINE_STYLE: {
                updateTrendlineStyles(workbook, chartJSON);
                break;
            }
            case MARKER_STATUS: {
                updateMarkerStatus(workbook, chartJSON);
                break;
            }
            case MARKER_STYLE: {
                updateMarkerStyles(workbook, chartJSON);
                break;
            }
            case X_AXIS_INVERTED: {
                updateXAxisInverted(workbook, chartJSON);
                break;
            }
            case GROUP_HEADERS_STATUS_EDIT: {
                updateGroupHeadersStatus(workbook, chartJSON);
                break;
            }
        }
    }

    public static void handle(Workbook workbook, JSONObjectWrapper actionJSON) {
        JSONArrayWrapper chartJSONs = ChartUtils.optFromJSONObject(actionJSON, ChartActionConstants.JSONConstants.CHART_JSON);
        ChartUtils.forEach(chartJSONs, (obj) -> {
            JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
            int subActionConstants = ChartUtils.optIntegerFromJSONObject(chartJSON, ChartActionConstants.JSONConstants.SUB_ACTION);
            passItToHandler(workbook, chartJSON, subActionConstants);
        });
    }

    private static void handleBorderColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(chart == null) { return; }
        String color = ChartsBCUtils.getColorFromChartJSON(chartJSON, SheetChartActionConstants.JSONConstants.BORDER_COLOR);
        JSONObjectWrapper propsJSON = ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON,
                SheetChartActionConstants.JSONConstants.BORDER_COLOR, ChartsBCConstants.Constants.DEFAULT_BORDER_COLOR);

        chart.setChartBorderColor(new JSONObjectWrapper().put("color", color));                                // NO I18N
        chart.setColorPropForBorderColor(new JSONObjectWrapper().put("borderColor", propsJSON));           // NO I18N
    }

    private static void updateChartTitle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(chart == null) { return; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        boolean enabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED, false);
        String title = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT, enabled ? "Chart Title": "");       // NO I18N

        Sheet sheet = workbook.getSheetByAssociatedName(chart.getSheetName());
        chart.updateChartTitle(sheet, new JSONObjectWrapper().put("title", Utility.getDecodedString(title)));            // NO I18N
    }

    private static void updateChartTitleStyles(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateChartTitleColor(workbook, chartJSON);
        updateChartTitleHAlign(workbook, chartJSON);
        updateChartTitleFontSize(workbook, chartJSON);
        updateChartTitleFontStyle(workbook, chartJSON);
        updateChartTitleFontWeight(workbook, chartJSON);
    }

    private static void updateChartTitleHAlign(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.H_ALIGN)) { return; }
        String hAlign = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.H_ALIGN);

        Sheet sheet = workbook.getSheetByAssociatedName(chart.getSheetName());
        chart.updateChartTitlePosition(sheet, new JSONObjectWrapper().put("align", hAlign));       // NO I18N
        chart.updateChartSubTitlePosition(sheet, new JSONObjectWrapper().put("align", hAlign));    // NO I18N
    }

    private static void updateChartTitleColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }
        String color = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);

        JSONObjectWrapper prevStylesJSON = chart.getChartTitleObject();
        if(prevStylesJSON == null) { prevStylesJSON = new JSONObjectWrapper(); }
        if(!prevStylesJSON.has("style")) { prevStylesJSON.put("style", new JSONObjectWrapper()); }                 // NO I18N

        JSONObjectWrapper propsJSON = ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR,
                ChartsBCConstants.Constants.DEFAULT_TITLE_COLOR);

        prevStylesJSON.getJSONObject("style").put("color", color);                        // NO I18N
        chart.updateChartTitleStyle(prevStylesJSON);
        chart.setDefaultColorsState("TITLE", ChartsBCUtils.isDefaultColor(color));           // NO I18N
        chart.setColorPropForTitleColor(new JSONObjectWrapper().put("title", propsJSON));      // NO I18N
    }

    private static void updateChartTitleFontSize(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }
        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);

        JSONObjectWrapper prevStylesJSON = chart.getChartTitleObject();
        if(prevStylesJSON == null) { prevStylesJSON = new JSONObjectWrapper(); }
        if(!prevStylesJSON.has("style")) { prevStylesJSON.put("style", new JSONObjectWrapper()); }                 // NO I18N

        prevStylesJSON.getJSONObject("style").put("fontSize", fontSize);                                       // NO I18N
        chart.updateChartTitleStyle(prevStylesJSON);
    }

    private static void updateChartTitleFontWeight(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }
        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);

        JSONObjectWrapper prevStylesJSON = chart.getChartTitleObject();
        if(prevStylesJSON == null) { prevStylesJSON = new JSONObjectWrapper(); }
        if(!prevStylesJSON.has("style")) { prevStylesJSON.put("style", new JSONObjectWrapper()); }                         // NO I18N

        prevStylesJSON.getJSONObject("style").put("fontWeight", fontWeight);                                       // NO I18N
        chart.updateChartTitleStyle(prevStylesJSON);
    }

    private static void updateChartTitleFontStyle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }
        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);

        JSONObjectWrapper prevStylesJSON = chart.getChartTitleObject();
        if(prevStylesJSON == null) { prevStylesJSON = new JSONObjectWrapper(); }
        if(!prevStylesJSON.has("style")) { prevStylesJSON.put("style", new JSONObjectWrapper()); }                 // NO I18N

        prevStylesJSON.getJSONObject("style").put("fontStyle", fontStyle);                                       // NO I18N
        chart.updateChartTitleStyle(prevStylesJSON);
    }

    private static void updateChartSubtitle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(chart == null) { return; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        boolean enabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED, false);
        String title = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT, enabled ? "subtitle": "");       // NO I18N

        Sheet sheet = workbook.getSheetByAssociatedName(chart.getSheetName());
        chart.updateChartSubtitle(sheet, new JSONObjectWrapper().put("title", Utility.getDecodedString(title)));            // NO I18N
    }

    private static void updateChartSubtitleStyle(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateChartSubtitleFontColor(workbook, chartJSON);
        updateChartSubtitleFontWeight(workbook, chartJSON);
        updateChartSubtitleFontStyle(workbook, chartJSON);
        updateChartSubtitleFontSize(workbook, chartJSON);
    }

    private static void updateChartSubtitleFontColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }
        String fontColor = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
        boolean isDefaultColor = ChartsBCUtils.isDefaultColor(fontColor);
        JSONObjectWrapper propsJSON = ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON,
                FrameworkChartActionConstants.JSONConstants.FONT_COLOR, ChartsBCConstants.Constants.DEFAULT_TITLE_COLOR);

        JSONObjectWrapper prevStylesJSON = chart.getChartSubTitleObject();
        if(prevStylesJSON == null) { prevStylesJSON = new JSONObjectWrapper(); }
        if(!prevStylesJSON.has("style")) { prevStylesJSON.put("style", new JSONObjectWrapper()); }             // NO I18N
        prevStylesJSON.getJSONObject("style").put("color", fontColor);                                  // NO I18N

        chart.updateChartSubtitleStyle(prevStylesJSON);               // NO  I18N
        chart.setDefaultColorsState("SUBTITLE", isDefaultColor);                            // NO I18N
        chart.setColorPropForSubTitleColor(new JSONObjectWrapper().put("subtitle", propsJSON));            // NO I18N
    }

    private static void updateChartSubtitleFontWeight(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }
        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);

        JSONObjectWrapper prevStylesJSON = chart.getChartSubTitleObject();
        if(prevStylesJSON == null) { prevStylesJSON = new JSONObjectWrapper(); }
        if(!prevStylesJSON.has("style")) { prevStylesJSON.put("style", new JSONObjectWrapper()); }             // NO I18N
        prevStylesJSON.getJSONObject("style").put("fontWeight", fontWeight);                            // NO I18N

        chart.updateChartSubtitleStyle(prevStylesJSON);               // NO  I18N
    }

    private static void updateChartSubtitleFontStyle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }
        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);

        JSONObjectWrapper prevStylesJSON = chart.getChartSubTitleObject();
        if(prevStylesJSON == null) { prevStylesJSON = new JSONObjectWrapper(); }
        if(!prevStylesJSON.has("style")) { prevStylesJSON.put("style", new JSONObjectWrapper()); }             // NO I18N
        prevStylesJSON.getJSONObject("style").put("fontStyle", fontStyle);                            // NO I18N

        chart.updateChartSubtitleStyle(prevStylesJSON);               // NO  I18N
    }

    private static void updateChartSubtitleFontSize(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }
        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);

        JSONObjectWrapper prevStylesJSON = chart.getChartSubTitleObject();
        if(prevStylesJSON == null) { prevStylesJSON = new JSONObjectWrapper(); }
        if(!prevStylesJSON.has("style")) { prevStylesJSON.put("style", new JSONObjectWrapper()); }             // NO I18N
        prevStylesJSON.getJSONObject("style").put("fontSize", fontSize);                            // NO I18N

        chart.updateChartSubtitleStyle(prevStylesJSON);               // NO  I18N
    }

    private static void updateXAxisTitle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null) { return; }
        boolean enabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED, false);
        String title = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT, enabled ? "X Title":"");       // NO I18N
        Sheet sheet = workbook.getSheetByAssociatedName(chart.getSheetName());

        chart.updateXAXISTitle(sheet, new JSONObjectWrapper().put("title", title));        // No I18N
    }

    private static void updateXAxisTitleStyles(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateXAxisTitleFontColor(workbook, chartJSON);
        updateXAxisTitleFontStyle(workbook, chartJSON);
        updateXAxisTitleFontSize(workbook, chartJSON);
        updateXAxisTitleFontWeight(workbook, chartJSON);
    }

    private static void updateXAxisTitleFontColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }
        String fontColor = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
        boolean isDefaultColor = ChartsBCUtils.isDefaultColor(fontColor);
        JSONObjectWrapper propsJSON = ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON,
                FrameworkChartActionConstants.JSONConstants.FONT_COLOR, ChartsBCConstants.Constants.DEFAULT_AXES_TITLE_COLOR);

        JSONObjectWrapper xAxis = chart.getXAxis();
        JSONObjectWrapper xAxisTitle = ChartUtils.computeIfAbsent(xAxis, "title", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper titleStyleJSON = ChartUtils.computeIfAbsent(xAxisTitle, "style", key -> new JSONObjectWrapper());   // NO I18N

        titleStyleJSON.put("color", fontColor);                                         // NO I18N
        chart.setDefaultColorsState("X_TITLE", isDefaultColor);                     // NO I18N
        chart.updateXAXISTitleStyle(new JSONObjectWrapper().put("style", titleStyleJSON));         // NO I18N
        chart.setColorPropForXTitleColor(new JSONObjectWrapper().put("xTitle", propsJSON));    // NO I18N
    }

    private static void updateXAxisTitleFontStyle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }
        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);

        JSONObjectWrapper xAxis = chart.getXAxis();
        JSONObjectWrapper xAxisTitle = ChartUtils.computeIfAbsent(xAxis, "title", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper titleStyleJSON = ChartUtils.computeIfAbsent(xAxisTitle, "style", key -> new JSONObjectWrapper());   // NO I18N

        titleStyleJSON.put("fontStyle", fontStyle);                                         // NO I18N
        chart.updateXAXISTitleStyle(new JSONObjectWrapper().put("style", titleStyleJSON));         // NO I18N
    }

    private static void updateXAxisTitleFontSize(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }
        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);

        JSONObjectWrapper xAxis = chart.getXAxis();
        JSONObjectWrapper xAxisTitle = ChartUtils.computeIfAbsent(xAxis, "title", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper titleStyleJSON = ChartUtils.computeIfAbsent(xAxisTitle, "style", key -> new JSONObjectWrapper());   // NO I18N

        titleStyleJSON.put("fontSize", fontSize);                                         // NO I18N
        chart.updateXAXISTitleStyle(new JSONObjectWrapper().put("style", titleStyleJSON));         // NO I18N
    }

    private static void updateXAxisTitleFontWeight(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }
        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);

        JSONObjectWrapper xAxis = chart.getXAxis();
        JSONObjectWrapper xAxisTitle = ChartUtils.computeIfAbsent(xAxis, "title", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper titleStyleJSON = ChartUtils.computeIfAbsent(xAxisTitle, "style", key -> new JSONObjectWrapper());   // NO I18N

        titleStyleJSON.put("fontWeight", fontWeight);                                         // NO I18N
        chart.updateXAXISTitleStyle(new JSONObjectWrapper().put("style", titleStyleJSON));         // NO I18N
    }

    private static void updateYAxisTitle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(chart == null) { return; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        boolean enabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED, false);
        String title = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT, enabled ? "Y Title": "");       // NO I18N
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);

        Sheet sheet = workbook.getSheetByAssociatedName(chart.getSheetName());
        chart.updateYAXISTitle(sheet, new JSONObjectWrapper().put("title", title).put("index", yAxisIndex));       // NO I18N
    }

    private static void updateYAxisTitleFontStyles(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateYAxisTitleFontSize(workbook, chartJSON);
        updateYAxisTitleFontWeight(workbook, chartJSON);
        updateYAxisTitleFontStyle(workbook, chartJSON);
        updateYAxisTitleColor(workbook, chartJSON);
    }

    private static void updateYAxisTitleFontSize(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }
        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }
        JSONObjectWrapper yAxes = yAxis.getJSONObject(yAxisIndex);
        JSONObjectWrapper yAxesTitle = ChartUtils.computeIfAbsent(yAxes, "title", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(yAxesTitle, "style", key -> new JSONObjectWrapper());        // NO I18N

        styleJSON.put("fontSize", fontSize);                                            // NO I18N
        chart.updateYAXISTitleStyle(new JSONObjectWrapper().put("style", styleJSON).put("index", yAxisIndex));          // NO I18N
    }

    private static void updateYAxisTitleFontWeight(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }
        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }
        JSONObjectWrapper yAxes = yAxis.getJSONObject(yAxisIndex);
        JSONObjectWrapper yAxesTitle = ChartUtils.computeIfAbsent(yAxes, "title", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(yAxesTitle, "style", key -> new JSONObjectWrapper());        // NO I18N

        styleJSON.put("fontWeight", fontWeight);                                            // NO I18N
        chart.updateYAXISTitleStyle(new JSONObjectWrapper().put("style", styleJSON).put("index", yAxisIndex));          // NO I18N
    }

    private static void updateYAxisTitleFontStyle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }
        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }
        JSONObjectWrapper yAxes = yAxis.getJSONObject(yAxisIndex);
        JSONObjectWrapper yAxesTitle = ChartUtils.computeIfAbsent(yAxes, "title", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(yAxesTitle, "style", key -> new JSONObjectWrapper());        // NO I18N

        styleJSON.put("fontStyle", fontStyle);                                            // NO I18N
        chart.updateYAXISTitleStyle(new JSONObjectWrapper().put("style", styleJSON).put("index", yAxisIndex));          // NO I18N
    }

    private static void updateYAxisTitleColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }
        String fontColor = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }
        JSONObjectWrapper yAxes = yAxis.getJSONObject(yAxisIndex);
        JSONObjectWrapper yAxesTitle = ChartUtils.computeIfAbsent(yAxes, "title", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(yAxesTitle, "style", key -> new JSONObjectWrapper());        // NO I18N
        JSONObjectWrapper propsJSON = new JSONObjectWrapper().put("yTitle",                                               // NO I18N
                new JSONObjectWrapper().put(String.valueOf(yAxisIndex),
                new JSONObjectWrapper().put("color", ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON,
                        FrameworkChartActionConstants.JSONConstants.FONT_COLOR, ChartsBCConstants.Constants.DEFAULT_AXES_TITLE_COLOR))));

        styleJSON.put("color", fontColor);                                            // NO I18N
        chart.updateYAXISTitleStyle(new JSONObjectWrapper().put("style", styleJSON).put("index", yAxisIndex));          // NO I18N
        chart.setDefaultColorsState("Y_TITLE", ChartsBCUtils.isDefaultColor(fontColor));                                             // NO I18N
        chart.setColorPropForYTitleColor(propsJSON);
    }

    private static void updateXAxisLabelsStatus(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(chart == null) { return; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        boolean enabled = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        chart.updateLabels(new JSONObjectWrapper().put("isYAxis", false).put("labels", enabled));          // NO I18N
    }

    private static void updateXAxisLabelsFontStyles(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateXAxisLabelsFontColor(workbook, chartJSON);
        updateXAxisLabelsFontWeight(workbook, chartJSON);
        updateXAxisLabelsFontStyle(workbook, chartJSON);
        updateXAxisLabelsFontSize(workbook, chartJSON);
    }

    private static void updateXAxisLabelsFontColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }
        String fontColor = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
        JSONObjectWrapper xAxis = chart.getXAxis();
        JSONObjectWrapper labelsJSON = ChartUtils.computeIfAbsent(xAxis, "labels", key -> new JSONObjectWrapper());           // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(labelsJSON, "style", key -> new JSONObjectWrapper());        // NO I18N
        JSONObjectWrapper propsJSON = new JSONObjectWrapper().put("xLabels", ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON,         // NO I18N
                FrameworkChartActionConstants.JSONConstants.FONT_COLOR, ChartsBCConstants.Constants.DEFAULT_DATALABELS_COLOR));

        styleJSON.put("color", fontColor);                                          // NO I18N
        chart.updateXAxisLabelsStyle(new JSONObjectWrapper().put("style", styleJSON));         // NO I18N
        chart.setDefaultColorsState("X_LABELS", ChartsBCUtils.isDefaultColor(fontColor));      // NO I18N
        chart.setColorPropForXLabelsColor(propsJSON);
    }

    private static void updateXAxisLabelsFontWeight(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }
        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);
        JSONObjectWrapper xAxis = chart.getXAxis();
        JSONObjectWrapper labelsJSON = ChartUtils.computeIfAbsent(xAxis, "labels", key -> new JSONObjectWrapper());           // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(labelsJSON, "style", key -> new JSONObjectWrapper());        // NO I18N

        styleJSON.put("fontWeight", fontWeight);                                          // NO I18N
        chart.updateXAxisLabelsStyle(new JSONObjectWrapper().put("style", styleJSON));         // NO I18N
    }

    private static void updateXAxisLabelsFontStyle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }
        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);
        JSONObjectWrapper xAxis = chart.getXAxis();
        JSONObjectWrapper labelsJSON = ChartUtils.computeIfAbsent(xAxis, "labels", key -> new JSONObjectWrapper());           // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(labelsJSON, "style", key -> new JSONObjectWrapper());        // NO I18N

        styleJSON.put("fontStyle", fontStyle);                                          // NO I18N
        chart.updateXAxisLabelsStyle(new JSONObjectWrapper().put("style", styleJSON));         // NO I18N
    }

    private static void updateXAxisLabelsFontSize(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }
        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);
        JSONObjectWrapper xAxis = chart.getXAxis();
        JSONObjectWrapper labelsJSON = ChartUtils.computeIfAbsent(xAxis, "labels", key -> new JSONObjectWrapper());           // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(labelsJSON, "style", key -> new JSONObjectWrapper());        // NO I18N

        styleJSON.put("fontSize", fontSize);                                          // NO I18N
        chart.updateXAxisLabelsStyle(new JSONObjectWrapper().put("style", styleJSON));         // NO I18N
    }

    private static void updateYAxisLabelsStatus(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(chart == null) { return; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        boolean enabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED, true);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);

        chart.updateLabels(new JSONObjectWrapper().put("isYAxis", true)                // NO I18N
                .put("labels", enabled)                                         // NO I18N
                .put("index", yAxisIndex));                                     // NO I18N
    }

    private static void updateYAxisLabelsFontStyles(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateYAxisLabelsFontSize(workbook, chartJSON);
        updateYAxisLabelsFontColor(workbook, chartJSON);
        updateYAxisLabelsFontWeight(workbook, chartJSON);
        updateYAxisLabelsFontStyle(workbook, chartJSON);
    }

    private static void updateYAxisLabelsFontSize(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }
        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }
        JSONObjectWrapper yAxes = yAxis.getJSONObject(yAxisIndex);
        JSONObjectWrapper yAxeslabels = ChartUtils.computeIfAbsent(yAxes, "labels", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(yAxeslabels, "style", key -> new JSONObjectWrapper());        // NO I18N

        styleJSON.put("fontSize", fontSize);
        chart.updateYAXISLabelStyle(new JSONObjectWrapper().put("style", styleJSON).put("index", yAxisIndex));                 // NO I18N
    }

    private static void updateYAxisLabelsFontColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }
        String fontColor = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }
        JSONObjectWrapper yAxes = yAxis.getJSONObject(yAxisIndex);
        JSONObjectWrapper yAxeslabels = ChartUtils.computeIfAbsent(yAxes, "labels", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(yAxeslabels, "style", key -> new JSONObjectWrapper());        // NO I18N
        JSONObjectWrapper propsJSON = new JSONObjectWrapper().put("yLabels",                              // NO I18N
                new JSONObjectWrapper().put(String.valueOf(yAxisIndex), new JSONObjectWrapper().put("color", ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(),            // NO I18N
                        chartJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR, ChartsBCConstants.Constants.DEFAULT_DATALABELS_COLOR))));

        styleJSON.put("color", fontColor);
        chart.updateYAXISLabelStyle(new JSONObjectWrapper().put("style", styleJSON).put("index", yAxisIndex));                 // NO I18N
        chart.setDefaultColorsState("Y_LABELS", ChartsBCUtils.isDefaultColor(fontColor));                              // NO I18N
        chart.setColorPropForYLabelsColor(propsJSON);
    }

    private static void updateYAxisLabelsFontWeight(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }
        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }
        JSONObjectWrapper yAxes = yAxis.getJSONObject(yAxisIndex);
        JSONObjectWrapper yAxeslabels = ChartUtils.computeIfAbsent(yAxes, "labels", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(yAxeslabels, "style", key -> new JSONObjectWrapper());        // NO I18N

        styleJSON.put("fontWeight", fontWeight);
        chart.updateYAXISLabelStyle(new JSONObjectWrapper().put("style", styleJSON).put("index", yAxisIndex));                 // NO I18N
    }

    private static void updateYAxisLabelsFontStyle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }
        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }
        JSONObjectWrapper yAxes = yAxis.getJSONObject(yAxisIndex);
        JSONObjectWrapper yAxeslabels = ChartUtils.computeIfAbsent(yAxes, "labels", key -> new JSONObjectWrapper());            // NO I18N
        JSONObjectWrapper styleJSON = ChartUtils.computeIfAbsent(yAxeslabels, "style", key -> new JSONObjectWrapper());        // NO I18N

        styleJSON.put("fontStyle", fontStyle);
        chart.updateYAXISLabelStyle(new JSONObjectWrapper().put("style", styleJSON).put("index", yAxisIndex));                 // NO I18N
    }

    private static void updatePlotlineOptions(Workbook workbook, JSONObjectWrapper chartJSON) {
        updatePlotlineTitle(workbook, chartJSON);
        updatePlotlineValue(workbook, chartJSON);
    }

    private static void updatePlotlineTitle(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.TEXT)) { return; }
        String title = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TEXT);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        int plIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX,
                0);

        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }

        JSONObjectWrapper titleUpdateJSON = new JSONObjectWrapper()
                .put("isYAxis", true)                               // NO I18N
                .put("index", yAxisIndex)                           // NO I18N
                .put("title", title)                                // NO I18N
                .put("plIndex", plIndex);                           // NO I18N

        chart.updateplotLineTitle(titleUpdateJSON);
    }

    private static void updatePlotlineValue(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.VALUE)) { return; }
        String value = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.VALUE);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        int plIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX,
                0);

        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }

        JSONObjectWrapper valueUpdateJSON = new JSONObjectWrapper()
                .put("isYAxis", true)                                       // NO I18N
                .put("index", yAxisIndex)                                   // NO I18N
                .put("plValue", value)                                      // NO I18N
                .put("plIndex", plIndex);                                   // NO I18N
        chart.addPlotLines(valueUpdateJSON);
    }

    private static void removePlotlines(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null) { return; }
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON,
                FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        int plIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX,
                -1);

        JSONArrayWrapper yAxis = chart.getYAxis();
        if(yAxis.length() <= yAxisIndex) {
            while(yAxis.length() <= yAxisIndex) { yAxis.put(new JSONObjectWrapper()); }
        }
        JSONObjectWrapper removeJSON = new JSONObjectWrapper()
                .put("isYAxis", true)                           // NO I18N
                .put("index", yAxisIndex)                       // NO I18N
                .put("plIndex", plIndex);                       // NO I18N
        chart.removePlotline(removeJSON);
    }

    private static void updateLegendStatus(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX)) { return; }
        boolean enabled = valueJSON.optBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED, false);

        chart.setLegend(enabled ? 1 : 0, chart.isFloatLegend());
    }

    private static void updateLegendPosition(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(chart == null) { return; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        LegendPositionType positionType = LegendPositionType.retrieveByValue(valueJSON.optString(FrameworkChartActionConstants.JSONConstants.POSITION, "bottom"));      // NO I18N

        chart.setLegend(ChartsBCUtils.getOldLegendPosition(positionType), chart.isFloatLegend());
    }

    private static void updateDataLabelsStatus(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX)) { return; }
        ChartType chartType = ChartUtils.getChartForOldChartName(chart.getType());
        boolean enabled = valueJSON.optBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED, false);

        if(chartType == ChartType.PIE || chartType == ChartType.PIE_PARLIAMENT || chartType == ChartType.SEMI_PIE
     || chartType == ChartType.DONUT || chartType == ChartType.DONUT_PARLIAMENT || chartType == ChartType.SEMI_DONUT ||
        chartType == ChartType.FUNNEL || chartType == ChartType.WEIGHTED_FUNNEL) {
            chart.setDataLabel(enabled ? "L_P" : "N");       // NO I18N
        } else {
            chart.setDataLabel(enabled ? "V" : "N");       // NO I18N
        }
    }

    private static void updateDataLabelsFormat(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX)) { return; }
        chart.setDataLabel(ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.FORMAT, "N"));       // NO I18N
    }

    private static void updateTrendlineStatus(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX)) { return; }
        boolean enabled = valueJSON.optBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED, false);
        chart.setTrendLineDetails(true, enabled ? "linear" : "none", chart.getTrendlineDegree());            // NO I18N
    }

    private static void updateSeriesStyles(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateSeriesColors(workbook, chartJSON);
        updateSeriesBorderColor(workbook, chartJSON);
    }

    private static void updateSeriesColors(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_COLOR)) { return; }
        String color = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.SERIES_COLOR);
        int seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX, 0);

        JSONObjectWrapper propsJSON = new JSONObjectWrapper().put("seriesColor", new JSONObjectWrapper().put(String.valueOf(seriesIndex),        // NO I18N
                new JSONObjectWrapper().put("color",                                                                                   // NO I18N
                        ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON, FrameworkChartActionConstants.JSONConstants.SERIES_COLOR, ""))));             // NO I18N

        JSONObjectWrapper updateJSON = new JSONObjectWrapper()
                .put("isSeriesLevel", true)             // NO I18N
                .put("sIndex", seriesIndex)             // NO I18N
                .put("cValue", color)                   // NO II18N
                .put("pIndex", 0)                       // NO I18N
                .put("colorProps", propsJSON);          // NO I18N
        chart.updateSeriesColor(updateJSON);
    }

    private static void updateSeriesBorderColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_COLOR)) { return; }
        String color = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR);
        int seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX, -1);
        int propsIndex = seriesIndex == -1 ? 0 : seriesIndex;
        int seriesCount = ChartsBCUtils.getSeriesCount(chart);

        JSONObjectWrapper propsJSON = new JSONObjectWrapper()
                .put("seriesBorderColor", new JSONObjectWrapper()                                                                      // NO I18N
                .put(String.valueOf(propsIndex), new JSONObjectWrapper()
                .put("color", ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(),                                         // NO I18N
                        chartJSON, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR, ""))));                // NO I18N

        chart.setBorderColor(new JSONObjectWrapper()
                .put("borderColor", color)                      // NO I18N
                .put("index", seriesIndex)                      // NO I18N
                .put("seriesCount", seriesCount)                // NO I18N
        );
        chart.setColorPropForSeriesBorderColor(propsJSON);
    }

    private static void updateDataPropertyStyles(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateDataPropertyColors(workbook, chartJSON);
    }

    private static void updateDataPropertyColors(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.COLOR)) { return; }
        String color = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.COLOR);
        int seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX, 0);
        int dataPropertyIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX, 0);

        JSONObjectWrapper updateJSON = new JSONObjectWrapper()
                .put("isSeriesLevel", false)             // NO I18N
                .put("sIndex", seriesIndex)             // NO I18N
                .put("cValue", color)                   // NO II18N
                .put("pIndex", dataPropertyIndex);                       // NO I18N
        chart.updateSeriesColor(updateJSON);
    }

    private static void updateChartBgColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.BG_COLOR)) { return; }
        String bgColor = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.BG_COLOR);
        JSONObjectWrapper propsJSON = new JSONObjectWrapper().put("bgColor",                                     // NO I18N
                ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON, FrameworkChartActionConstants.JSONConstants.BG_COLOR,
                        ChartsBCConstants.Constants.DEFAULT_CHART_BG_COLOR));

        chart.setChartPlotBackgroundColor(new JSONObjectWrapper().put("pbc", bgColor));                    // NO I18N
        chart.setColorPropForBgColor(propsJSON);
    }

    private static void updateChartBgOpacity(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }
        Double opacity = ChartUtils.optDoubleFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.OPACITY, 1D);
        String bgColor = ChartUtils.optFromJSONObject(chart.getChartObject(), "plotBackgroundColor", ChartsBCConstants.Constants.DEFAULT_CHART_BG_COLOR);       // NO I18N
        JSONObjectWrapper propsJSON = new JSONObjectWrapper().put("bgColor",                                     // NO I18N
                ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON, FrameworkChartActionConstants.JSONConstants.BG_COLOR,
                        ChartsBCConstants.Constants.DEFAULT_CHART_BG_COLOR));

        String color = ChartsBCUtils.getColorWithOpacity(bgColor, opacity);
        chart.setChartPlotBackgroundColor(new JSONObjectWrapper().put("pbc", color));                    // NO I18N
        chart.setChartBackgroundColor(new JSONObjectWrapper().put("cbc", color));                    // NO I18N
        chart.setColorPropForBgColor(propsJSON);
    }

    private static void updateTrendlineOptions(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateTrendlineOrder(workbook, chartJSON);
        updateTrendlineType(workbook, chartJSON);
    }

    private static void updateTrendlineType(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.TYPE) ||
                valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX)) { return; }
        TrendLineType trendLineType = TrendLineType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

        chart.setTrendLineDetails(chart.isTrendLine(), ChartsBCUtils.getOldTrendlineTypeName(trendLineType), chart.getTrendlineDegree());
    }

    private static void updateTrendlineOrder(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.ORDER) ||
                valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX)) { return; }
        int order = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.TYPE);

        chart.setTrendLineDetails(chart.isTrendLine(), chart.getTrendLineType(), order);
    }

    private static void updateTrendlineStyles(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateTrendlineStyleType(workbook, chartJSON);
        updateTrendlineColor(workbook, chartJSON);
    }

    private static void updateTrendlineStyleType(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_TYPE) ||
                !valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX)) { return; }
        String lineType = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_TYPE);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        chart.updateLineStyle(new JSONObjectWrapper()
                .put("dashStyle", lineType)                 // NO I18N
                .put("sIndex", seriesIndex)                 // NO I18N
        );
    }

    private static void updateTrendlineColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR) ||
                !valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX)) { return; }
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String color = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);
        JSONObjectWrapper propsJSON = new JSONObjectWrapper().put("seriesColor", new JSONObjectWrapper().put(String.valueOf(seriesIndex),        // NO I18N
                new JSONObjectWrapper().put("color",                                                                                   // NO I18N
                        ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(), chartJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR, ""))));             // NO I18N

        JSONObjectWrapper stylesJSON = new JSONObjectWrapper()
                .put("sIndex", seriesIndex)                         // NO I18N
                .put("cValue", color)                               // NO I18N
                .put("pIndex", 0)                                   // NO I18N
                .put("isSeriesLevel", true)                         // NO I18N
                .put("colorProps", propsJSON);                      // NO I18N

        chart.updateSeriesColor(stylesJSON);
        chart.setColorPropForSeriesColor(propsJSON);
    }

    private static void updateMarkerStatus(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(chart == null) { return; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        int seriesCount = ChartsBCUtils.getSeriesCount(chart);
        int seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX, -1);
        boolean status = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED, false);

        chart.setMarkerEnabled(new JSONObjectWrapper()
                .put("enabled", status)                 // NO I18N
                .put("index", seriesIndex)              // NO I18N
                .put("seriesCount", seriesCount)        // NO I18N
        );
    }

    private static void updateMarkerStyles(Workbook workbook, JSONObjectWrapper chartJSON) {
        updateMarkerShape(workbook, chartJSON);
        updateMarkerSize(workbook, chartJSON);
        updateMarkerColor(workbook, chartJSON);
        updateMarkerBorderColor(workbook, chartJSON);
    }

    private static void updateMarkerShape(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_SHAPE)) { return; }
        MarkerShapeType markerShapeType = MarkerShapeType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.MARKER_SHAPE));
        int seriesCount = ChartsBCUtils.getSeriesCount(chart);
        int seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX, -1);

        chart.setMarkerSymbol(new JSONObjectWrapper()
                .put("symbol", ChartsBCUtils.getOldMarkerShape(markerShapeType))                   // NO I18N
                .put("index", seriesIndex)                                                      // NO I18N
                .put("seriesCount", seriesCount)                                                // NO I18N
        );
    }

    private static void updateMarkerSize(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_SIZE)) { return; }
        int size = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.MARKER_SIZE);
        int seriesCount = ChartsBCUtils.getSeriesCount(chart);
        int seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX, -1);

        chart.setMarkerSize(new JSONObjectWrapper()
                .put("size", size)                                                              // NO I18N
                .put("index", seriesIndex)                                                      // NO I18N
                .put("seriesCount", seriesCount)                                                // NO I18N
        );
    }

    private static void updateMarkerColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_COLOR)) { return; }
        String color = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.MARKER_COLOR);
        int seriesCount = ChartsBCUtils.getSeriesCount(chart);
        int seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX, -1);
        int propsIndex = seriesIndex == -1 ? 0 : seriesIndex;

        JSONObjectWrapper propsJSON = new JSONObjectWrapper()
                .put("markerFillColor", new JSONObjectWrapper()                                    // NO I18N
                .put(String.valueOf(propsIndex), new JSONObjectWrapper()
                .put("color", ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(),         // NO I18N
                                        chartJSON, FrameworkChartActionConstants.JSONConstants.MARKER_COLOR, ""))));                                    // NO I18N

        chart.setMarkerFillColor(new JSONObjectWrapper()
                .put("fillColor", color)                        // NO I18N
                .put("index", seriesIndex)                      // NO I18N
                .put("seriesCount", seriesCount)                // NO I18N
        );

        chart.setColorPropForMarkerFillColor(propsJSON);
    }

    private static void updateMarkerBorderColor(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);

        if(chart == null || !valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_BORDER_COLOR)) { return; }
        String color = ChartsBCUtils.getColorFromChartJSON(chartJSON, FrameworkChartActionConstants.JSONConstants.MARKER_BORDER_COLOR);
        int seriesCount = ChartsBCUtils.getSeriesCount(chart);
        int seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX, -1);
        int propsIndex = seriesIndex == -1 ? 0 : seriesIndex;

        JSONObjectWrapper propsJSON = new JSONObjectWrapper()
                .put("markerBorderColor", new JSONObjectWrapper()                                    // NO I18N
                        .put(String.valueOf(propsIndex), new JSONObjectWrapper()
                                .put("color", ChartsBCUtils.getPropsJSONFromChartJSON(workbook.getTheme(),         // NO I18N
                                        chartJSON, FrameworkChartActionConstants.JSONConstants.MARKER_BORDER_COLOR, ""))));                                    // NO I18N

        chart.setMarkerBorderColor(new JSONObjectWrapper()
                .put("borderColor", color)                        // NO I18N
                .put("index", seriesIndex)                      // NO I18N
                .put("seriesCount", seriesCount)                // NO I18N
        );

        chart.setColorPropForMarkerFillColor(propsJSON);
    }

    private static void updateXAxisInverted(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(chart == null) { return; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        boolean isInverted = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_INVERTED, false);

        chart.updateReverse(new JSONObjectWrapper()
                .put("reverse", isInverted)                 // NO I18N
                .put("index", 0)                            // NO I18N
                .put("isYAxis", false)                      // NO I18N
        );
    }

    private static void updateGroupHeadersStatus(Workbook workbook, JSONObjectWrapper chartJSON) {
        Chart chart = ChartsBCUtils.getChartFromChartJSON(workbook, chartJSON);

        if(!(chart instanceof BoxPlotChart)) { return; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        BoxPlotChart boxPlotChart = (BoxPlotChart) chart;
        Boolean status = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED, true);

        boxPlotChart.setGroupHeaders(status);
    }

}
