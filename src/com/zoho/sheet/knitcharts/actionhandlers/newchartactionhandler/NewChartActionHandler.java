package com.zoho.sheet.knitcharts.actionhandlers.newchartactionhandler;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.knitcharts.backwardconverter.converter.ChartsBCManager;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.constants.ChartsBCConstants;
import com.zoho.sheet.knitcharts.constants.SheetChartActionConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.ChartsBCUtils;

import java.util.logging.Level;
import java.util.logging.Logger;

public class NewChartActionHandler {

    private static final Logger LOGGER = Logger.getLogger(NewChartActionHandler.class.getName());

    private static void insertChartForUndoRedo(WorkbookContainer container, Workbook workbook, Chart chart, String chartID) {
        try {
            String sheetName = workbook.getSheetByAssociatedName(chart.getSheetName()).getName();
            JSONObjectWrapper chartProperties = com.zoho.sheet.chart.ChartUtils.readChartProperties(chart);

            chartProperties.put("fromundoredo", true);          // NO I18N
            chartProperties.put("chartId", chartID); // NO I18N
            chartProperties.put(JSONConstants.ASSOCIATED_SHEET_NAME, chart.getSheetName());
            try {
                com.zoho.sheet.chart.ChartUtils.addNewChart(container, sheetName, chartProperties);
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Exception occurred while adding new chart.", e);              // NO I18N
            }
        }catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[CHARTS_2.0] Exception occurred while executing new action", e);      // NO I18N
        }
    }

    public static void insertChartDBAction(WorkbookContainer container, Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartsBCConstants.JSONConstants.CHART_JSON);
            Workbook workbook = sheet.getWorkbook();
            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                String chartID = chartJSON.getString(ChartsBCConstants.JSONConstants.CHART_ID);
                JSONObjectWrapper newChartJSON = new JSONObjectWrapper();

                ChartUtils.copyObject(chartJSON, newChartJSON, FrameworkChartMetaConstants.ChartMeta.KEY);
                ChartUtils.copyObject(chartJSON, newChartJSON, SheetChartMetaConstants.KEY);

                Chart chart = ChartsBCManager.convert(sheet.getWorkbook(),
                        chartID, new JSONObjectWrapper().put(ChartsBCConstants.JSONConstants.NEW_CHART_OPTIONS, newChartJSON),
                        sheet.getName());

                workbook.addChart(sheet.getAssociatedName(), chartID, chart);

                ChartsBCUtils.updateEssentialParamsForInsert(workbook, chart);
                insertChartForUndoRedo(container, workbook, chart, chartID);
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[CHARTS_2.0] Exception occurred while executing new action", e);      // NO I18N
        }
    }

    public static void insertChart(WorkbookContainer container, Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartsBCConstants.JSONConstants.CHART_JSON);
            Workbook workbook = sheet.getWorkbook();
            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                String chartID = chartJSON.getString(ChartsBCConstants.JSONConstants.CHART_ID);
                if (workbook.getChart(sheet.getAssociatedName(), chartID) == null) {
                    JSONObjectWrapper newChartJSON = new JSONObjectWrapper();

                    ChartUtils.copyObject(chartJSON, newChartJSON, FrameworkChartMetaConstants.ChartMeta.KEY);
                    ChartUtils.copyObject(chartJSON, newChartJSON, SheetChartMetaConstants.KEY);

                    Chart chart = ChartsBCManager.convert(sheet.getWorkbook(),
                            chartID, new JSONObjectWrapper().put(ChartsBCConstants.JSONConstants.NEW_CHART_OPTIONS, newChartJSON),
                            sheet.getName());

                    workbook.addChart(sheet.getAssociatedName(), chartID, chart);
                    try {
                        ChartsBCUtils.updateEssentialParamsForInsert(workbook, chart);
                        com.zoho.sheet.chart.ChartUtils.updateChart(chart, container.getDocOwner());
                    } catch (Exception e) {
                        LOGGER.log(Level.SEVERE, "Exception occurred while adding new chart.", e);              // NO I18N
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[CHARTS_2.0] Exception occurred while executing new action", e);      // NO I18N
        }
    }

    public static void removeChartFromDB(WorkbookContainer container, Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartsBCConstants.JSONConstants.CHART_JSON);
            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                String chartID = chartJSON.getString(ChartsBCConstants.JSONConstants.CHART_ID);

                try {
                    com.zoho.sheet.chart.ChartUtils.removeChart(sheet, chartID, container.getDocOwner(), container.getResourceId(), actionJSON);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[CHARTS_2.0] Exception occurred while executing new action", e);      // NO I18N
        }
    }

    public static void removeChartFromWB(Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartsBCConstants.JSONConstants.CHART_JSON);
            Workbook workbook = sheet.getWorkbook();

            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                String chartID = chartJSON.getString(ChartsBCConstants.JSONConstants.CHART_ID);

                workbook.removeChart(sheet.getAssociatedName(), chartID);
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[CHARTS_2.0] Exception occurred while executing new action", e);      // NO I18N
        }
    }

    public static void insertClonedChartDBAction(WorkbookContainer container, Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartsBCConstants.JSONConstants.CHART_JSON);
            Workbook workbook = sheet.getWorkbook();
            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                String chartID = chartJSON.getString(ChartsBCConstants.JSONConstants.CHART_ID);
                String orginalChartID = chartJSON.getString(ChartActionConstants.JSONConstants.ORIGINAL_CHART_ID);

                Chart originalChart = sheet.getWorkbook().getChart(sheet.getAssociatedName(), orginalChartID);
                Chart clonedChart = originalChart.clone(workbook);

                clonedChart.setChartId(chartID);
                ChartsBCUtils.updateEssentialParamsForInsert(workbook, clonedChart);
                ChartsBCUtils.updatePositionForClone(originalChart, clonedChart);
                workbook.addChart(sheet.getAssociatedName(), chartID, clonedChart);

                insertChartForUndoRedo(container, workbook, clonedChart, chartID);
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[CHARTS_2.0] Exception occurred while executing new action", e);      // NO I18N
        }
    }

    public static void insertClonedChart(WorkbookContainer container, Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartsBCConstants.JSONConstants.CHART_JSON);
            Workbook workbook = sheet.getWorkbook();
            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                String chartID = chartJSON.getString(ChartsBCConstants.JSONConstants.CHART_ID);
                String orginalChartID = chartJSON.getString(ChartActionConstants.JSONConstants.ORIGINAL_CHART_ID);

                if (workbook.getChart(sheet.getAssociatedName(), chartID) == null) {
                    Chart originalChart = sheet.getWorkbook().getChart(sheet.getAssociatedName(), orginalChartID);
                    Chart clonedChart = originalChart.clone(workbook);

                    clonedChart.setChartId(chartID);
                    ChartsBCUtils.updateEssentialParamsForInsert(workbook, clonedChart);
                    ChartsBCUtils.updatePositionForClone(originalChart, clonedChart);
                    workbook.addChart(sheet.getAssociatedName(), chartID, clonedChart);
                    try {
                        com.zoho.sheet.chart.ChartUtils.updateChart(clonedChart, container.getDocOwner());
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[CHARTS_2.0] Exception occurred while executing new action", e);      // NO I18N
        }
    }

    public static void handleSheetChartAction(Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            SheetChartActionHandler.handle(sheet.getWorkbook(), actionJSON);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[CHARTS_2.0] Exception occurred while executing new action", e);      // NO I18N
        }
    }

    public static void handleFrameworkChartAction(Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            FrameworkChartActionHandler.handle(sheet.getWorkbook(), actionJSON);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[CHARTS_2.0] Exception occurred while executing new action", e);      // NO I18N
        }
    }

    public static void handlePublishChartAction(WorkbookContainer container, Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = ChartUtils.optFromJSONObject(actionJSON, ChartActionConstants.JSONConstants.CHART_JSON);
            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                Chart chart = ChartsBCUtils.getChartFromChartJSON(sheet.getWorkbook(), chartJSON);
                if (chart != null && chartJSON.has(ChartActionConstants.JSONConstants.PUBLIC_CHART_NAME)) {
                    String pcn = chartJSON.getString(ChartActionConstants.JSONConstants.PUBLIC_CHART_NAME);
                    JSONObjectWrapper publishJSON = new JSONObjectWrapper().put("publicchartname", pcn).       // NO I18N
                    put("chartId", chart.getChartId());     // NO I18N

                    try {
                        com.zoho.sheet.chart.ChartUtils.publishChart(sheet, chart.getChartId(), container.getDocOwner(), publishJSON);
                    }catch (Exception e) {
                        LOGGER.log(Level.SEVERE, "Exception occurred while publishing chart.", e);                  // NO I18N
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Exception occurred while publishing chart.", e);                              // NO I18N
        }
    }

    public static void handleUnPublishChartAction(WorkbookContainer container, Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = ChartUtils.optFromJSONObject(actionJSON, ChartActionConstants.JSONConstants.CHART_JSON);
            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                Chart chart = ChartsBCUtils.getChartFromChartJSON(sheet.getWorkbook(), chartJSON);
                if (chart != null && chartJSON.has(ChartActionConstants.JSONConstants.PUBLIC_CHART_NAME)) {
                    JSONObjectWrapper publishJSON = new JSONObjectWrapper().put("chartId", chart.getChartId())        // NO I18N
                            .put("publishedType", "external");      // NO I18N

                    try {
                        com.zoho.sheet.chart.ChartUtils.unPublishChart(sheet, chart.getChartId(), container.getDocOwner(), publishJSON);
                    }catch (Exception e) {
                        LOGGER.log(Level.SEVERE, "Exception occurred while unpublishing chart.", e);                  // NO I18N
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Exception occurred while unpublishing chart.", e);                              // NO I18N
        }
    }

    public static void handleMoveToOtherSheetActionDBAction(WorkbookContainer container, Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = ChartUtils.optFromJSONObject(actionJSON, ChartActionConstants.JSONConstants.CHART_JSON);
            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                Chart chart = ChartsBCUtils.getChartFromChartJSON(sheet.getWorkbook(), chartJSON);
                if (chart != null && chartJSON.has(SheetChartActionConstants.JSONConstants.TARGET_SHEET_ID)) {
                    String newSheetName = chartJSON.getString(SheetChartActionConstants.JSONConstants.TARGET_SHEET_ID);

                    try {
                        com.zoho.sheet.chart.ChartUtils.getAndSetNewChartSheetInfoInDB(container, newSheetName, chart.getChartId());
                    }catch (Exception e) {
                        LOGGER.log(Level.SEVERE, "Exception occurred while moving chart to new sheet.", e);                  // NO I18N
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Exception occurred while moving chart to new sheet.", e);                              // NO I18N
        }
    }
    public static void handleMoveToOtherSheetAction(WorkbookContainer container, Sheet sheet, JSONObjectWrapper actionJSON) {
        try {
            JSONArrayWrapper chartJSONs = ChartUtils.optFromJSONObject(actionJSON, ChartActionConstants.JSONConstants.CHART_JSON);
            ChartUtils.forEach(chartJSONs, (obj) -> {
                JSONObjectWrapper chartJSON = new JSONObjectWrapper((JSONObjectWrapper) obj);
                Chart chart = ChartsBCUtils.getChartFromChartJSON(sheet.getWorkbook(), chartJSON);
                if (chart != null && chartJSON.has(SheetChartActionConstants.JSONConstants.TARGET_SHEET_ID)) {
                    String newSheetName = chartJSON.getString(SheetChartActionConstants.JSONConstants.TARGET_SHEET_ID);
                    JSONObjectWrapper moveJSON = new JSONObjectWrapper().put("chartId", chart.getChartId())        // NO I18N
                            .put("newSheetName", newSheetName)      // NO I18N
                            .put("zuid", actionJSON.getString("zuid"))      // NO I18N
                            .put("rsid", actionJSON.getString("rsid"));     // NO I18N

                    try {
                        com.zoho.sheet.chart.ChartUtils.getAndSetNewChartSheetInfo(container, moveJSON, false, false);
                    }catch (Exception e) {
                        LOGGER.log(Level.SEVERE, "Exception occurred while moving chart to new sheet.", e);                  // NO I18N
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Exception occurred while moving chart to new sheet.", e);                              // NO I18N
        }
    }

}
