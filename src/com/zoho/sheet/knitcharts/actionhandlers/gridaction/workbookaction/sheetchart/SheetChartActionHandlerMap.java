package com.zoho.sheet.knitcharts.actionhandlers.gridaction.workbookaction.sheetchart;

import com.zoho.sheet.knitcharts.actionhandlers.dataholder.SubActionDataHolder;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionConsumer;
import com.zoho.sheet.knitcharts.constants.SheetChartActionConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import static com.zoho.sheet.knitcharts.utils.ChartUtils.toChartsInteger;

import java.lang.ref.SoftReference;
import java.util.Map;

public final class SheetChartActionHandlerMap {

    public static FunctionConsumer<SubActionDataHolder> map(int subActionConstant) {
        switch (subActionConstant) {
                    case SheetChartActionConstants.SubActionConstants.IHC_STATUS_EDIT: return SheetChartActionHandler::updateIncludeHiddenCellStatus;
                    case SheetChartActionConstants.SubActionConstants.AUTO_EXPAND_STATUS_EDIT: return SheetChartActionHandler::updateAutoExpandStatus;
                    case SheetChartActionConstants.SubActionConstants.SERIES_IN_EDIT: return SheetChartActionHandler::updateSeriesIn;
                    case SheetChartActionConstants.SubActionConstants.FIRST_ROW_AS_LABEL_EDIT: return SheetChartActionHandler::updateFirstRowLabel;
                    case SheetChartActionConstants.SubActionConstants.FIRST_COL_AS_LABEL_EDIT: return SheetChartActionHandler::updateFirstColLabel;
                    case SheetChartActionConstants.SubActionConstants.CHART_MOVE: return SheetChartActionHandler::updateChartCoordinates;
                    case SheetChartActionConstants.SubActionConstants.CHART_RESIZE: return SheetChartActionHandler::updateChartDimensions;
                    case SheetChartActionConstants.SubActionConstants.DATA_SOURCES_EDIT: return SheetChartActionHandler::updateDataSources;
                    case SheetChartActionConstants.SubActionConstants.DATA_JOIN_TYPE_EDIT: return SheetChartActionHandler::updateDataJoinType;
                    case SheetChartActionConstants.SubActionConstants.AGGREGATION_RESET: return SheetChartActionHandler::resetAggregation;
                    case SheetChartActionConstants.SubActionConstants.AGGREGATION_OPTION_EDIT: return SheetChartActionHandler::updateAggregationOptions;
                    case SheetChartActionConstants.SubActionConstants.FILTER_OPTIONS_RESET: return SheetChartActionHandler::resetFilter;
                    case SheetChartActionConstants.SubActionConstants.FILTER_OPTIONS_EDIT: return SheetChartActionHandler::updateFilterOptions;
                    case SheetChartActionConstants.SubActionConstants.AUTO_FILL_TYPE_EDIT: return SheetChartActionHandler::updateAutoFillType;
                    case SheetChartActionConstants.SubActionConstants.COLOR_PALETTE_TYPE_EDIT: return SheetChartActionHandler::updateColorPaletteType;
                    case SheetChartActionConstants.SubActionConstants.COLOR_PALETTE_REVERSE_STATUS_EDIT: return SheetChartActionHandler::updateColorPaletteReverseStatus;
                    case SheetChartActionConstants.SubActionConstants.PIVOT_FILTER_PINNED_STATUS_EDIT: return SheetChartActionHandler::updatePivotFilterPinnedStatus;
                    case SheetChartActionConstants.SubActionConstants.CHART_VISIBILITY_STATUS_EDIT: return SheetChartActionHandler::updateChartVisibility;
                    case SheetChartActionConstants.SubActionConstants.CHART_RENAME: return SheetChartActionHandler::updateChartName;
                    case SheetChartActionConstants.SubActionConstants.IMAGE_SERIES_EDIT: return SheetChartActionHandler::updateImageSeries;
                    case SheetChartActionConstants.SubActionConstants.INFO_TEXT_STATUS_EDIT: return SheetChartActionHandler::updateInfoTextStatus;
                    case SheetChartActionConstants.SubActionConstants.REMOVE_BLANK_STATUS_EDIT: return SheetChartActionHandler::updateRemoveBlankStatus;
        }
        return null;
    }

}
