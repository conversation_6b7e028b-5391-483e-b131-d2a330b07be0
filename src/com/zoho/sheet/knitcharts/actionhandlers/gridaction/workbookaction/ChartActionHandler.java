package com.zoho.sheet.knitcharts.actionhandlers.gridaction.workbookaction;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.ActionDataHolder;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.SubActionDataHolder;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.pojo.ActiveCellPOJO;
import com.zoho.sheet.knitcharts.actionhandlers.gridaction.workbookaction.frameworkchart.FrameworkChartActionHandler;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.miscellaneous.exception.UnsupportedChartActionException;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionBiConsumer;
import com.zoho.sheet.knitcharts.constants.SheetChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.gridaction.workbookaction.sheetchart.SheetChartActionHandler;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.PublicChartType;
import com.zoho.sheet.knitcharts.utils.ChartCreator;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.navigator.actionhandlers.NavActionHandler;

/**
 * Charts related action handler
 * <AUTHOR>
 */
public class ChartActionHandler {

    private static FunctionBiConsumer<ActionDataHolder, WorkbookContainer> getActionHandler(int actionConstant) {
        switch (actionConstant) {
            case ActionConstants.INSERT_CHART:
            case ActionConstants.INSERT_TABLE_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART_VIA_CACHE:
                return ChartActionHandler::insertChartHandler;
            case ActionConstants.PASTE_CHART: return ChartActionHandler::pasteChartHandler;
            case ActionConstants.INSERT_PIVOT_CHART: return ChartActionHandler::insertPivotChartHandler;
            case ActionConstants.SHEET_CHART_EDIT: return ChartActionHandler::sheetChartEditHandler;
            case ActionConstants.CHART_MOVE_TO_OTHER_SHEET: return ChartActionHandler::moveChartToOtherSheet;
            case ActionConstants.FRAMEWORK_CHART_EDIT: return ChartActionHandler::frameworkChartEditHandler;
            case ActionConstants.DELETE_CHART: return ChartActionHandler::deleteChartHandler;
            case ActionConstants.CLONE_CHART: return ChartActionHandler::cloneChartHandler;
            case ActionConstants.PUBLISH_CHART: return ChartActionHandler::publishChartHandler;
            case ActionConstants.UNPUBLISH_CHART: return ChartActionHandler::unPublishChartHandler;
            case ActionConstants.PASTE_CHART_STYLES: return ChartActionHandler::pasteChartStyles;
            case ActionConstants.RESET_CHART_STYLES: return ChartActionHandler::resetChartStyles;
        }
        throw new UnsupportedChartActionException(String.format("%s: %d", ErrorMessages.INVALID_CHART_ACTION, actionConstant));                                     // NO I18N
    }

    public static void handle(int action, Sheet activeSheet, JSONObjectWrapper actionJSON, WorkbookContainer container) {
        JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartActionConstants.JSONConstants.CHART_JSON);
        ActiveCellPOJO activeCellPOJO = new ActiveCellPOJO();
        boolean isFromUndoRedo = actionJSON.has(ChartActionConstants.JSONConstants.FROM_UNDO) &&
                actionJSON.getBoolean(ChartActionConstants.JSONConstants.FROM_UNDO);

        activeCellPOJO.constructFromJSON(actionJSON.getJSONObject(JSONConstants.CURRENT_ACTIVE_CELL));
        ChartUtils.forEach(chartJSONs, objectChartJSON ->{
            JSONObjectWrapper chartJSON = (JSONObjectWrapper) objectChartJSON;
            ActionDataHolder bundle = new ActionDataHolder(activeSheet, action, chartJSON, activeCellPOJO, isFromUndoRedo);
            FunctionBiConsumer<ActionDataHolder, WorkbookContainer> handler = getActionHandler(bundle.getActionConstant());

            handler.consume(bundle, container);
        });
    }

    private static void insertChartHandler(ActionDataHolder bundle, WorkbookContainer workbookContainer) {
        com.zoho.sheet.knitcharts.utils.ChartCreator.createNewChart(bundle);
        NavActionHandler.setNavigatorAsModified(bundle.getActiveSheet().getWorkbook(), true);
    }

    private static void insertPivotChartHandler(ActionDataHolder bundle, WorkbookContainer workbookContainer) {
        com.zoho.sheet.knitcharts.utils.ChartCreator.createNewPivotChart(bundle);
        NavActionHandler.setNavigatorAsModified(bundle.getActiveSheet().getWorkbook(), true);
    }

    private static void cloneChartHandler(ActionDataHolder dataHolder, WorkbookContainer workbookContainer){
        com.zoho.sheet.knitcharts.utils.ChartCreator.cloneChart(dataHolder);
        NavActionHandler.setNavigatorAsModified(dataHolder.getActiveSheet().getWorkbook(), true);
    }

    private static void pasteChartHandler(ActionDataHolder dataHolder, WorkbookContainer workbookContainer) {
        ChartCreator.createPasteChart(dataHolder);
        NavActionHandler.setNavigatorAsModified(dataHolder.getActiveSheet().getWorkbook(), true);
    }

    private static void moveChartToOtherSheet(ActionDataHolder dataHolder, WorkbookContainer workbookContainer) {
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = ChartUtils.getChartContainer(dataHolder);
        String targetSheetID = chartJSON.getString(SheetChartActionConstants.JSONConstants.TARGET_SHEET_ID);

        chartContainer.moveChart(dataHolder.getChartID(), targetSheetID);
        ChartUtils.getChartFromActionDataHolder(dataHolder).setSaveRequired(true);
        NavActionHandler.setNavigatorAsModified(dataHolder.getActiveSheet().getWorkbook(), true);
    }

    private static void sheetChartEditHandler(ActionDataHolder bundle, WorkbookContainer workbookContainer) {
        SubActionDataHolder subActionDataHolder = bundle.toSubActionDataHolder();
        SheetChartActionHandler.handle(subActionDataHolder);
        ChartUtils.getChartFromActionDataHolder(bundle).setSaveRequired(true);
    }

    private static void frameworkChartEditHandler(ActionDataHolder bundle, WorkbookContainer workbookContainer) {
        SubActionDataHolder subActionDataHolder = bundle.toSubActionDataHolder();
        FrameworkChartActionHandler.handle(subActionDataHolder);
        ChartUtils.getChartFromActionDataHolder(bundle).setSaveRequired(true);
    }

    private static void deleteChartHandler(ActionDataHolder bundle, WorkbookContainer workbookContainer) {
        Workbook workbook = bundle.getActiveSheet().getWorkbook();
        ChartContainer chartContainer = workbook.getChartsContainer();

        chartContainer.removeChart(bundle.getActiveSheet().getAssociatedName(), bundle.getChartID());
        NavActionHandler.setNavigatorAsModified(bundle.getActiveSheet().getWorkbook(), true);
    }

    private static void publishChartHandler(ActionDataHolder dataHolder, WorkbookContainer workbookContainer) {
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        PublicChartType publicChartType = PublicChartType.retrieveByValue(chartJSON
                .optString(ChartActionConstants.JSONConstants.PUBLISH_CHART_TYPE, PublicChartType.EXTERNAL.getValue().toString()));
        String publicChartName = chartJSON.getString(ChartActionConstants.JSONConstants.PUBLIC_CHART_NAME);
        String publicSpaceChartID = chartJSON.getString(ChartActionConstants.JSONConstants.PUBLIC_SPACE_CHART_ID);

        SheetChartAPI.setPublicChartName(sheetMeta, publicChartName);
        SheetChartAPI.setPublicChartType(sheetMeta, publicChartType);
        SheetChartAPI.setPublicSpaceChartID(sheetMeta, publicSpaceChartID);
        ChartUtils.getChartFromActionDataHolder(dataHolder).setSaveRequired(true);
    }

    private static void unPublishChartHandler(ActionDataHolder dataHolder, WorkbookContainer workbookContainer) {
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        SheetChartAPI.setPublicChartName(sheetMeta, null);
        SheetChartAPI.setPublicChartType(sheetMeta, null);
        ChartUtils.getChartFromActionDataHolder(dataHolder).setSaveRequired(true);
    }

    private static void pasteChartStyles(ActionDataHolder dataHolder, WorkbookContainer workbookContainer) {
        com.zoho.sheet.knitcharts.chart.Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();

        if(chartJSON.has(ChartActionConstants.JSONConstants.STYLES_JSON)) {
            JSONObjectWrapper stylesJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.STYLES_JSON);

            /* Need to reset the styles before applying styles */
            chart.resetStyles();
            chart.applyStyles(stylesJSON);

            chart.setSaveRequired(true);
        }

    }

    private static void resetChartStyles(ActionDataHolder dataHolder, WorkbookContainer workbookContainer) {
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);

        chart.resetStyles();
        chart.setSaveRequired(true);
    }

}
