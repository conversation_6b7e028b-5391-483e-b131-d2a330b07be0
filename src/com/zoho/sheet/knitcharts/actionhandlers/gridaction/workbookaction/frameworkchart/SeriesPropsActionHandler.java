package com.zoho.sheet.knitcharts.actionhandlers.gridaction.workbookaction.frameworkchart;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.SubActionDataHolder;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.constants.FrameworkChartActionConstants;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.reference.ReferencePool;
import com.zoho.sheet.knitcharts.reference.referrer.KeyReferrer;
import com.zoho.sheet.knitcharts.reference.referrer.ReferrerType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.FrameworkChartAPIUtils;
import com.zoho.sheet.knitcharts.utils.SheetChartAPIUtils;

public class SeriesPropsActionHandler {

    private static void updateSeriesDatalabelsStatus(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsStatusWithIndex(chartMeta, null, seriesIndex);
        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDataLabelsStatus(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex -> updateSeriesDatalabelsStatus(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsStatus(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsPosition(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsPositionWithIndex(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDataLabelsPosition(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsPositions(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex -> updateSeriesDatalabelsPosition(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsPosition(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsPosition(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsFormat(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsFormatWithIndex(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDataLabelsFormat(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsFormat(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex -> updateSeriesDatalabelsFormat(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsFormat(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsFormat(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsBorderColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsBorderColor(chartMeta, null, seriesIndex);
        SheetChartAPI.updateSeriesDataLabelsBorderColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.updateSeriesDataLabelsBorderColorTone(sheetMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex -> {
            FrameworkChartAPI.updateDataPropertyDataLabelsBorderColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyDataLabelsBorderColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyDataLabelsBorderColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        });
    }

    public static void updateSharedSeriesDatalabelsBorderColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsBorderColor(chartMeta, sheetMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsBorderColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsBorderColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsBorderColorType(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDatalabelsBorderFillType(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDatalabelBorderFillType(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsBorderColorType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsBorderColorType(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsBorderColorType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsBorderColorType(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsBgColorType(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDatalabelsBgFillType(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDatalabelBgFillType(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsBgColorType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsBgColorType(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsBgColorType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsBgColorType(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsRotation(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDatalabelsRotation(chartMeta, null, seriesIndex);

        for(int dataPropIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyDatalabelsRotation(chartMeta, null, seriesIndex, dataPropIndex);
        }
    }

    public static void updateSharedSeriesDatalabelsRotation(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesDatalabelsRotation(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesDatalabelsRotation(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsRotation(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsBorderWidth(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsBorderWidth(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDataLabelsBorderWidth(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsBorderWidth(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsBorderWidth(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsBorderWidth(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsBorderWidth(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsShape(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsBgShape(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDataLabelsBgShape(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    private static void enableSharedSeriesDatalabelsBorderColor(ChartMeta chartMeta) {
        String color = FrameworkChartGetterAPI.getSharedSeriesDataLabelsBorderColor(chartMeta);
        if(color == null || color.equals(FrameworkChartActionConstants.Constants.TRANSPARENT)
                || color.equals(FrameworkChartActionConstants.Constants.NONE)) {
            FrameworkChartAPI.updateSharedSeriesDataLabelsBorderColor(chartMeta, FrameworkChartActionConstants.Constants.AUTO_COLOR);
        }
    }

    public static void updateSharedSeriesDatalabelsShape(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        enableSharedSeriesDatalabelsBorderColor(chartMeta);
        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsShape(chartMeta, seriesIndex));
    }

    private static void enableSeriesDatalabelsBorderColor(ChartMeta chartMeta, int seriesIndex) {
        String color = FrameworkChartGetterAPI.getSeriesDataLabelsBorderColor(chartMeta, seriesIndex);

        if(color == null || color.equals(FrameworkChartActionConstants.Constants.TRANSPARENT)
                || color.equals(FrameworkChartActionConstants.Constants.NONE)) {
            FrameworkChartAPI.updateSeriesDataLabelsBorderColor(chartMeta, FrameworkChartActionConstants.Constants.AUTO_COLOR, seriesIndex);
        }
    }

    public static void updateSeriesDatalabelsShape(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        enableSeriesDatalabelsBorderColor(chartMeta, seriesIndex);
        updateSeriesDatalabelsShape(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsX(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsXCoordinate(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDataLabelsXCoordinate(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsX(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsX(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsX(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsX(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsY(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsXCoordinate(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDataLabelsYCoordinate(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsY(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsY(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsY(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsY(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsBgColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsBgColor(chartMeta, null, seriesIndex);
        SheetChartAPI.updateSeriesDataLabelsBgColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.updateSeriesDataLabelsBgColorTone(sheetMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex -> {
            FrameworkChartAPI.updateDataPropertyDataLabelsBgColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyDataLabelsBgColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyDataLabelsBgColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        });
    }

    public static void updateSharedSeriesDatalabelsBgColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsBgColor(chartMeta, sheetMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsBgColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsBgColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsBgOpacity(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDataLabelsBgColorOpacity(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDataLabelsBgColorOpacity(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsBgOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsBgOpacity(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsBgOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsBgOpacity(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsFontColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDatalabelsFontColor(chartMeta, null, seriesIndex);
        SheetChartAPI.setDataLabelsColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.setDataLabelsColorTone(sheetMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex -> {
            FrameworkChartAPI.updateDataPropertyDataLabelsFontColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyDataLabelFontColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyDataLabelFontColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        });
    }

    public static void updateSharedSeriesDatalabelsFontColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsFontColor(chartMeta, sheetMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsFontColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        updateSeriesDatalabelsFontColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsFontSize(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDatalabelsFontSize(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDataLabelsFontSize(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsFontSize(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsFontSize(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsFontSize(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsFontSize(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsFontStyle(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDatalabelsFontStyle(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDatalabelsFontStyle(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsFontStyle(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsFontStyle(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsFontStyle(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsFontStyle(chartMeta, seriesIndex);
    }

    private static void updateSeriesDatalabelsFontWeight(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDatalabelsFontWeight(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyDatalabelsFontWeight(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesDatalabelsFontWeight(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesDatalabelsFontWeight(chartMeta, seriesIndex));
    }

    public static void updateSeriesDatalabelsFontWeight(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsFontWeight(chartMeta, seriesIndex);
    }

    private static void updateSeriesColorFillType(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesColorFillType(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyColorFillType(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesColorFillType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesColorFillType(chartMeta, seriesIndex));
    }

    public static void updateSeriesColorFillType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesColorFillType(chartMeta, seriesIndex);
    }

    private static void updateSeriesGradientType(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesGradientFunctionType(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyGradientFunctionType(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesGradientType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesGradientType(chartMeta, seriesIndex));
    }

    public static void updateSeriesGradientType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesGradientType(chartMeta, seriesIndex);
    }

    private static void updateSeriesColorOpacity(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesColorOpacity(chartMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateSeriesDataPropertyOpacity(chartMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSharedSeriesColorOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPIUtils.forEachSeries(chartMeta, seriesIndex ->
                updateSeriesColorOpacity(chartMeta, seriesIndex));
    }

    public static void updateSeriesColorOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesColorOpacity(chartMeta, seriesIndex);
    }

    private static void updateSeriesGradientStops(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesGradientStops(chartMeta, null, seriesIndex);
        SheetChartAPI.updateCustomPropsSeriesColorStops(sheetMeta, null, seriesIndex);

        FrameworkChartAPIUtils.forEachDataProperty(chartMeta, seriesIndex, dataPropsIndex ->
                FrameworkChartAPI.updateDataPropertyGradientStops(chartMeta, null, seriesIndex, dataPropsIndex));
        SheetChartAPIUtils.forEachCustomSeriesDataProps(sheetMeta, seriesIndex, dataPropsIndex ->
                SheetChartAPI.updateCustomPropsDataPropertyStops(sheetMeta, null, seriesIndex, dataPropsIndex));
    }

    public static void updateSeriesGradientStops(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesGradientStops(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesGradientDegree(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesGradientDegree(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyGradientDegree(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesGradientDegree(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesGradientDegree(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesGradientDegree(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesGradientDegree(chartMeta, seriesIndex);
    }

    private static void updateSeriesGradientCX(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesGradientCX(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyGradientCX(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSeriesGradientCX(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesGradientCX(chartMeta, seriesIndex);
    }

    private static void updateSeriesGradientCY(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesGradientCY(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyGradientCY(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSeriesGradientCY(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesGradientCY(chartMeta, seriesIndex);
    }

    private static void updateSeriesGradientRadius(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesGradientRadius(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyGradientRadius(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSeriesGradientRadius(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesGradientRadius(chartMeta, seriesIndex);
    }

    public static void updateSeriesGradientPresets(SubActionDataHolder dataHolder) {
        updateSeriesColorFillType(dataHolder);
        updateSeriesGradientType(dataHolder);
        updateSeriesGradientStops(dataHolder);
        updateSeriesGradientDegree(dataHolder);
        updateSeriesGradientCX(dataHolder);
        updateSeriesGradientCY(dataHolder);
        updateSeriesGradientRadius(dataHolder);
    }

    public static void updateSharedSeriesShadowPresets(SubActionDataHolder dataHolder) {
        updateSharedSeriesShadowDegree(dataHolder);
        updateSharedSeriesShadowDistance(dataHolder);
        updateSharedSeriesShadowSpread(dataHolder);
        updateSharedSeriesShadowColorOpacity(dataHolder);
    }

    public static void updateSeriesShadowPresets(SubActionDataHolder dataHolder) {
        updateSeriesShadowDegree(dataHolder);
        updateSeriesShadowDistance(dataHolder);
        updateSeriesShadowSpread(dataHolder);
        updateSeriesShadowColorOpacity(dataHolder);
    }

    private static void updateSeriesColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesColorWithIndex(chartMeta, null, seriesIndex);
        SheetChartAPI.setSeriesColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.setSeriesColorTone(sheetMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateSeriesDataPropertyColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.setDataPropertyColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.setDataPropertyColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSeriesColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesColor(chartMeta, sheetMeta, seriesIndex);
    }

    public static void updateSharedSeriesAreaOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesAreaOpacityWithIndex(chartMeta, null, seriesIndex);
        }
    }

    private static void updateSeriesBorderColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesBorderColorWithIndex(chartMeta, null, seriesIndex);
        SheetChartAPI.setSeriesBorderColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.setSeriesBorderColorTone(sheetMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateSeriesDataPropertyBorderColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.setDataPropertyBorderColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.setDataPropertyBorderColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesBorderColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesBorderColor(chartMeta, sheetMeta, seriesIndex);
        }
    }

    public static void updateSeriesBorderColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesBorderColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesBorderWidth(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesBorderWidth(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateSeriesDataPropertyBorderWidth(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesBorderWidth(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesBorderWidth(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesBorderWidth(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesBorderWidth(chartMeta, seriesIndex);
    }

    private static void updateSeriesBorderOpacity(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesBorderOpacity(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyBorderOpacity(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesBorderOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesBorderOpacity(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesBorderOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesBorderOpacity(chartMeta, seriesIndex);
    }

    private static void updateSeriesBorderFillType(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesBorderFillType(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyBorderFillType(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesBorderFillType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesBorderFillType(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesBorderFillType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesBorderFillType(chartMeta, seriesIndex);
    }

    private static void updateSeriesBorderRadius(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesBorderRadius(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateSeriesDataPropertyBorderRadius(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesBorderRadius(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesBorderRadius(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesBorderRadius(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesBorderRadius(chartMeta, seriesIndex);
    }

    private static void updateSeriesMarkerStatus(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesMarkerStatusWithIndex(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyMarkerStatus(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesMarkerStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesMarkerStatus(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesMarkerStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesMarkerStatus(chartMeta, seriesIndex);
    }

    private static void updateSeriesMarkerColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesMarkerColorWithIndex(chartMeta, null, seriesIndex);
        SheetChartAPI.setMarkerColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.setMarkerColorTone(sheetMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyMarkerColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyMarkerColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyMarkerColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesMarkerColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesMarkerColor(chartMeta, sheetMeta, seriesIndex);
        }
    }

    public static void updateSeriesMarkerColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesMarkerColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesMarkerBorderColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesMarkerBorderColorWithIndex(chartMeta, null, seriesIndex);
        SheetChartAPI.setMarkerBorderColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.setMarkerBorderColorTone(sheetMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyMarkerBorderColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyMarkerBorderColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyMarkerBorderColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesMarkerBorderColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesMarkerBorderColor(chartMeta, sheetMeta, seriesIndex);
        }
    }

    public static void updateSeriesMarkerBorderColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesMarkerBorderColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesMarkerShape(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesMarkerShapeWithIndex(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateSeriesDataPropertyMarkerShape(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesMarkerShape(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesMarkerShape(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesMarkerShape(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesMarkerShape(chartMeta, seriesIndex);
    }

    private static void updateSeriesMarkerSize(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesMarkerSizeWithIndex(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateSeriesDataPropertyMarkerSize(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesMarkerSize(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesMarkerSize(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesMarkerSize(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesMarkerSize(chartMeta, seriesIndex);
    }

    public static void updateSharedSeriesShowMeanMarkers(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateMeanMarkerStatus(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesShowOutlier(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateOutlierStatus(chartMeta, null, seriesIndex);
        }
    }

    private static void updateSeriesOutliersColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateOutliersColor(chartMeta, null, seriesIndex);
        SheetChartAPI.setSeriesOutlierColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.setSeriesOutlierColorTone(sheetMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyOutliersColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyOutliersColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyOutliersColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesOutliersColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesOutliersColor(chartMeta, sheetMeta, seriesIndex);
        }
    }

    public static void updateSeriesOutliersColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesOutliersColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesMeanColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateMeanColor(chartMeta, null, seriesIndex);
        SheetChartAPI.setSeriesMeanColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.setSeriesMeanColorTone(sheetMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyMeanColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyMeanColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyMeanColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesMeanColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesMeanColor(chartMeta, sheetMeta, seriesIndex);
        }
    }

    public static void updateSeriesMeanColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesMeanColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesMedianColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateMedianColor(chartMeta, null, seriesIndex);
        SheetChartAPI.setSeriesMedianColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.setSeriesMedianColorTone(sheetMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyMedianColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyMedianColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyMedianColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesMedianColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesMedianColor(chartMeta, sheetMeta, seriesIndex);
        }
    }

    public static void updateSeriesMedianColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesMedianColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesWhiskersColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateWhiskersColor(chartMeta, null, seriesIndex);
        SheetChartAPI.setSeriesWhiskerColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.setSeriesWhiskerColorTone(sheetMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyWhiskersColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyWhiskersColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyWhiskersColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesWhiskersColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesWhiskersColor(chartMeta, sheetMeta, seriesIndex);
        }
    }

    public static void updateSeriesWhiskersColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesWhiskersColor(chartMeta, sheetMeta, seriesIndex);
    }

    public static void updateSharedSeriesThresholdValue(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesThresholdValueWithIndex(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesThresholdColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesThresholdColorWithIndex(chartMeta, null, seriesIndex);
            SheetChartAPI.setNegativeColorAccent(sheetMeta, null, seriesIndex);
            SheetChartAPI.setNegativeColorTone(sheetMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesThresholdOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesThresholdOpacity(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesPointPadding(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesPointPadding(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesGroupPadding(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesGroupPadding(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateTrendLineStatusWithIndex(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateTrendLineTypeWithIndex(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineOrder(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateTrendLineOrderWithIndex(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineColorOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateTrendLineColorOpacity(chartMeta, null, seriesIndex);
        }
    }

    private static void updateSeriesShadowStatus(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesShadowStatus(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyShadowStatus(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesShadowStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesShadowStatus(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesShadowStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesShadowStatus(chartMeta, seriesIndex);
    }

    private static void updateSeriesShadowColor(ChartMeta chartMeta, SheetMeta sheetMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesShadowColor(chartMeta, null, seriesIndex);
        SheetChartAPI.updateSeriesShadowColorAccent(sheetMeta, null, seriesIndex);
        SheetChartAPI.updateSeriesShadowColorTone(sheetMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyShadowColor(chartMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyShadowColorAccent(sheetMeta, null, seriesIndex, dataPropsIndex);
            SheetChartAPI.updateDataPropertyShadowColorTone(sheetMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesShadowColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesShadowColor(chartMeta, sheetMeta, seriesIndex);
        }
    }

    public static void updateSeriesShadowColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesShadowColor(chartMeta, sheetMeta, seriesIndex);
    }

    private static void updateSeriesShadowColorOpacity(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesShadowColorOpacity(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyShadowOpacity(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesShadowColorOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesShadowColorOpacity(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesShadowColorOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesShadowColorOpacity(chartMeta, seriesIndex);
    }

    private static void updateSeriesShadowType(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesShadowType(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyShadowType(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesShadowType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesShadowType(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesShadowType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesShadowType(chartMeta, seriesIndex);
    }

    private static void updateSeriesShadowBlur(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesShadowBlur(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyShadowBlur(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesShadowBlur(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesShadowBlur(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesShadowBlur(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesShadowBlur(chartMeta, seriesIndex);
    }

    private static void updateSeriesShadowSpread(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesShadowSpread(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyShadowSpread(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesShadowSpread(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesShadowSpread(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesShadowSpread(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesShadowSpread(chartMeta, seriesIndex);
    }

    private static void updateSeriesShadowDistance(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesShadowDistance(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyShadowDistance(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesShadowDistance(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesShadowDistance(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesShadowDistance(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesShadowDistance(chartMeta, seriesIndex);
    }


    private static void updateSeriesShadowDegree(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesShadowDegree(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyShadowDegree(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesShadowDegree(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesShadowDegree(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesShadowDegree(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesShadowDegree(chartMeta, seriesIndex);
    }

    public static void updateSharedSeriesStartAngle(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSliceStartAngle(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesEndAngle(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSliceEndAngle(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesConnectNulls(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesConnectNulls(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesAreaLineType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesAreaLineType(chartMeta, null, seriesIndex);
        }
    }


    public static void updateSharedSeriesImageSeriesStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesImageSeriesStatus(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesImageSeriesPosition(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesImageSeriesPosition(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesImageSeriesClipShape(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesImageSeriesClipShape(chartMeta, null, seriesIndex);
        }
    }

    private static void updateSeriesDatalabelsCustomValue(Workbook workbook, Chart chart, int seriesIndex) {
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        ChartContainer chartContainer = workbook.getChartsContainer();
        int referenceID = chartContainer.getReferenceID(chart.getChartID());
        FrameworkChartAPI.updateSeriesDatalabelsCustomValue(chartMeta, null, seriesIndex);
        SheetChartAPI.updateSeriesDatalabelsCustomValueReference(sheetMeta, null, seriesIndex);
        ReferencePool referencePool = workbook.getChartsContainer().getReferencePool();
        Key seriesKey = Key.getKey(seriesIndex);

        referencePool.removeReference(new KeyReferrer(referenceID, ReferrerType.SERIES_DATALABELS_CUSTOM_VALUE, seriesKey));

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyDatalabelsCustomValue(chartMeta, null, seriesIndex, dataPropsIndex);
            referencePool.removeReference(new KeyReferrer(referenceID, ReferrerType.DP_DATALABELS_CUSTOM_VALUE, seriesKey.getChild(dataPropsIndex)));
        }
    }

    public static void updateSeriesDatalabelsCustomValue(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsCustomValue(workbook, chart, seriesIndex);
    }

    public static void updateSharedSeriesDatalabelsCustomValue(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesDatalabelsCustomValue(workbook, chart, seriesIndex);
        }
    }

    private static void updateSeriesDatalabelsSeparator(ChartMeta chartMeta, int seriesIndex) {
        FrameworkChartAPI.updateSeriesDatalabelsSeparator(chartMeta, null, seriesIndex);

        for(int dataPropsIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
            FrameworkChartAPI.updateDataPropertyDatalabelsSeparator(chartMeta, null, seriesIndex, dataPropsIndex);
        }
    }

    public static void updateSharedSeriesDatalabelsSeparator(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            updateSeriesDatalabelsSeparator(chartMeta, seriesIndex);
        }
    }

    public static void updateSeriesDatalabelsSeparator(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        updateSeriesDatalabelsSeparator(chartMeta, seriesIndex);
    }

    public static void updateSharedSeriesTrendlineFillType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        
        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesTrendlineFillType(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineDatalabelsStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsStatus(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineDatalabelsFontColour(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = ChartUtils.getSheetMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFontColour(chartMeta, null, seriesIndex);
            SheetChartAPI.updateSeriesTrendlineDataLabelsFontColorAccent(sheetMeta, null, seriesIndex);
            SheetChartAPI.updateSeriesTrendlineDataLabelsFontColorTone(sheetMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineDatalabelsFontSize(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFontSize(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineDatalabelsFontStyle(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFontStyle(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineDatalabelsFontWeight(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFontWeight(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineDatalabelsFormat(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFormat(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineDatalabelsCustomLabel(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsCustomLabel(chartMeta, null, seriesIndex);
        }
    }

    public static void updateSharedSeriesTrendlineDatalabelsSeparator(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsSeparator(chartMeta, null, seriesIndex);
        }
    }

}
