package com.zoho.sheet.knitcharts.actionhandlers.gridaction.workbookaction.frameworkchart;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.*;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.GradientFunctionType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.TickPosition;
import com.zoho.sheet.knitcharts.constants.FrameworkChartActionConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font.DefaultFontJSONConstants;
import com.zoho.sheet.knitcharts.presets.DelegatingPreset;
import com.zoho.sheet.knitcharts.presets.PresetParams;
import com.zoho.sheet.knitcharts.reference.ReferenceActionHandler;
import com.zoho.sheet.knitcharts.constants.SheetChartActionConstants;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.supplier.Purpose;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.BorderStyleType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.SubActionDataHolder;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font.Font;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.miscellaneous.exception.UnsupportedChartActionException;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionConsumer;
import com.zoho.sheet.knitcharts.actionhandlers.gridaction.workbookaction.sheetchart.CustomPropertiesActionHandler;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.*;


/**
 * Handles all the framework-related actions
 *
 * <AUTHOR>
 */
public class FrameworkChartActionHandler {
    /**
     * Method to handle the performed sub actions
     *
     * @param actionBundle Bundled up sub action data
     */
    public static void handle(SubActionDataHolder actionBundle) {
        FunctionConsumer<SubActionDataHolder> handler = FrameworkChartActionHandlerMap.map(actionBundle.getSubActionConstant());

        // checking if any action handler mapping available for the given sub action constant
        if (ChartUtils.isNull(handler)) {
            throw new UnsupportedChartActionException(String.format("%s: %d", ErrorMessages.INVALID_SUB_ACTION_CONSTANT, actionBundle.getSubActionConstant()));          // NO I18N
        }
        handler.consume(actionBundle);
    }

    /**
     * Method to get the color from theme options JSONWrapper
     * @param valueJSON Value JSONWrapper
     * @param key Key for color theme options JSONWrapper
     * @return Color String
     */
    private static String getColor(JSONObjectWrapper valueJSON, String key){
        JSONObjectWrapper colorJSON = ChartUtils.optFromJSONObject(valueJSON, key);

        if(colorJSON == null) { return null; }
        return ChartUtils.optFromJSONObject(colorJSON, FrameworkChartActionConstants.JSONConstants.VALUE);
    }

    private static int getSeriesIndex(JSONObjectWrapper valueJSON){
        return valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
    }

    private static Integer optSeriesIndex(JSONObjectWrapper valueJSON){
        return optSeriesIndex(valueJSON, null);
    }

    private static Integer optSeriesIndex(JSONObjectWrapper valueJSON, Integer defaultIndex){
        return ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX, defaultIndex);
    }

    /**
     * Method to update chart title text
     *
     * @param actionBundle bundled up sub action data
     */
    static void updateChartTitleText(SubActionDataHolder actionBundle) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        Boolean isEnabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        String titleText = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT);

        if(titleText != null) {
            ReferenceActionHandler.updateChartTitle(actionBundle, titleText);
        }
        ChartUtils.ifNotNullThen(isEnabled, () -> FrameworkChartAPI.updateChartTitleStatus(chartMeta, isEnabled));
    }

    /**
     * Method to update chart title text style
     *
     * @param actionBundle Bundled up sub action data
     */
    static void updateChartTitleStyle(SubActionDataHolder actionBundle) {
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);
        // getting all the style attributes
        String hAlign = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.H_ALIGN);
        Font font = new Font(new ColorThemeFontJSONConstants());
        String fontColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);

        font.getFromJSONObject(valueJSON);
        font.setFontColor(fontColor);
        // updating the title style attribute, if not null
        ChartUtils.ifNotNullThen(hAlign, () -> FrameworkChartAPI.updateChartTitleHAlign(chartMeta, hAlign));
        FrameworkChartAPI.updateChartTitleFont(chartMeta, font);
        // updating custom properties for chart title color
        CustomPropertiesActionHandler.updateTitleColorThemeOptions(actionBundle, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
    }

    static void updateChartSubtitleText(SubActionDataHolder actionBundle) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        Boolean isEnabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        String subtitleText = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT);

        if(subtitleText != null) {
            ReferenceActionHandler.updateChartSubtitle(actionBundle, subtitleText);
        }
        ChartUtils.ifNotNullThen(isEnabled, () -> FrameworkChartAPI.updateChartSubTitleStatus(chartMeta, isEnabled));
    }

    static void updateChartSubtitleStyle(SubActionDataHolder actionBundle) {
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);
        // getting all the style attributes
        String hAlign = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.H_ALIGN);
        Font font = new Font(new ColorThemeFontJSONConstants());
        String fontColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);

        font.getFromJSONObject(valueJSON);
        font.setFontColor(fontColor);
        // updating the title style attribute, if not null
        ChartUtils.ifNotNullThen(hAlign, () -> FrameworkChartAPI.updateChartSubTitleHAlign(chartMeta, hAlign));
        FrameworkChartAPI.updateChartSubtitleFont(chartMeta, font);
        // updating the custom properties
        CustomPropertiesActionHandler.updateSubtitleColorThemeOptions(actionBundle, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
    }

    static void updateChartXAxisTitleText(SubActionDataHolder actionBundle) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        Boolean isEnabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        String xAxisTitle = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT);

        if(xAxisTitle != null) {
            ReferenceActionHandler.updateChartXAxisTitle(actionBundle, xAxisTitle, xAxisIndex);
        }
        ChartUtils.ifNotNullThen(isEnabled, () -> FrameworkChartAPI.updateChartXAxisTitleStatusWithIndex(chartMeta, isEnabled, xAxisIndex));
    }

    static void updateChartXAxisTitleStyle(SubActionDataHolder actionBundle) {
        updateChartXAxisTitleFontStyles(actionBundle);
        updateChartXAxisTitleRotation(actionBundle);
    }

    private static void updateChartXAxisTitleRotation(SubActionDataHolder actionBundle) {
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.ROTATION)) { return; }

        String rotation = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.ROTATION);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);

        FrameworkChartAPI.updateXAxisTitleRotation(chartMeta, rotation, xAxisIndex);
    }

    private static void updateChartXAxisTitleFontStyles(SubActionDataHolder actionBundle) {
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);
        Font font = new Font(new ColorThemeFontJSONConstants());
        Integer xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX);

        font.getFromJSONObject(valueJSON);
        font.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));
        if(xAxisIndex == null){
            FrameworkChartAPI.updateChartXAxisTitleFontWithIndex(chartMeta, font, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
            CustomPropertiesActionHandler.updateXAxisTitleColorThemeOptions(actionBundle, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        } else {
            FrameworkChartAPI.updateChartXAxisTitleFontWithIndex(chartMeta, font, xAxisIndex);
            CustomPropertiesActionHandler.updateXAxisTitleColorThemeOptions(actionBundle, xAxisIndex);
        }
    }

    static void updateChartYAxisTitleText(SubActionDataHolder actionBundle) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        Boolean isEnabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        String yTitleText = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT);

        if(yTitleText != null) {
            ReferenceActionHandler.updateChartYAxisTitle(actionBundle, yTitleText, yAxisIndex);
        }
        ChartUtils.ifNotNullThen(isEnabled, () -> FrameworkChartAPI.updateChartYAxisTitleStatusWithIndex(chartMeta, isEnabled, yAxisIndex));
    }

    static void updateChartYAxisTitleStyle(SubActionDataHolder holder) {
        updateChartYAxisTitleFontStyle(holder);
        updateChartYAxisTitleRotation(holder);
    }

    private static void updateChartYAxisTitleRotation(SubActionDataHolder actionBundle) {
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.ROTATION)) { return; }

        String rotation = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.ROTATION);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);

        FrameworkChartAPI.updateYAxisTitleRotation(chartMeta, rotation, yAxisIndex);
    }

    private static void updateChartYAxisTitleFontStyle(SubActionDataHolder actionBundle) {
        JSONObjectWrapper valueJSON = actionBundle.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(actionBundle);
        Font font = new Font(new ColorThemeFontJSONConstants());
        Integer yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        // getting all font style attributed from value JSONWrapper
        font.getFromJSONObject(valueJSON);
        font.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));
        if(yAxisIndex == null){
            FrameworkChartAPI.updateChartYAxisTitleFontWithIndex(chartMeta, font, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
            CustomPropertiesActionHandler.updateYAxisTitleColorThemeOptions(actionBundle, FrameworkChartActionConstants.JSONConstants.FONT_COLOR, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        }else{
            FrameworkChartAPI.updateChartYAxisTitleFontWithIndex(chartMeta, font, yAxisIndex);
            CustomPropertiesActionHandler.updateYAxisTitleColorThemeOptions(actionBundle, FrameworkChartActionConstants.JSONConstants.FONT_COLOR, yAxisIndex);
        }
    }

    static void updateChartLegendPosition(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        String legendPos = holder.getValueJSON().getString(FrameworkChartActionConstants.JSONConstants.POSITION);

        FrameworkChartAPI.updateChartLegendPosition(chartMeta, LegendPositionType.retrieveByValue(legendPos));
        FrameworkChartAPI.updateLegendStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE);
    }

    static void updateChartLegendStyle(SubActionDataHolder holder) {
        updateLegendFontStyles(holder);
        updateLegendShapeStyle(holder);
    }

    private static void updateLegendFontStyles(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Font font = new Font(new ColorThemeFontJSONConstants());

        font.getFromJSONObject(valueJSON);
        font.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));

        FrameworkChartAPI.updateChartLegendFont(chartMeta, font);
        CustomPropertiesActionHandler.updateLegendFontColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
    }

    private static void updateLegendShapeStyle(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SHAPE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        LegendShapeType shapeType = LegendShapeType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SHAPE));

        FrameworkChartAPI.updateLegendShape(chartMeta, shapeType);
    }

    static void updateSeriesDataLabelsFormat(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        Object format = valueJSON.get(FrameworkChartActionConstants.JSONConstants.FORMAT);
        Set<DataLabelFormatType> formatTypes = null;

        if(format instanceof String) {
            formatTypes = Collections.singleton(DataLabelFormatType.retrieveByValue(format.toString()));

        } else if(format instanceof JSONArrayWrapper) {
            formatTypes = new LinkedHashSet<>();
            List<String> formats = ChartUtils.JSONArrayToList((JSONArrayWrapper) format);

            for(String formatValue : formats) {
                formatTypes.add(DataLabelFormatType.retrieveByValue(formatValue));
            }
        }

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsFormat(holder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsFormats(chartMeta, formatTypes);
        }else{
            SeriesPropsActionHandler.updateSeriesDatalabelsFormat(holder);
            FrameworkChartAPI.updateSeriesDataLabelsFormatWithIndex(chartMeta, formatTypes, seriesIndex);
        }
    }

    static void updateSeriesDataLabelsPosition(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        DataLabelPositionType position = DataLabelPositionType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.POSITION));

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsPositions(holder);
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsStatus(holder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsPosition(chartMeta, position);
            FrameworkChartAPI.updateSharedSeriesDataLabelsStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE);
        }else{
            SeriesPropsActionHandler.updateSeriesDatalabelsPosition(holder);
            SeriesPropsActionHandler.updateSeriesDatalabelsStatus(holder);
            FrameworkChartAPI.updateSeriesDataLabelsPositionWithIndex(chartMeta, position, seriesIndex);
            FrameworkChartAPI.updateSeriesDataLabelsStatusWithIndex(chartMeta, FrameworkChartActionConstants.Constants.ENABLE, seriesIndex);
        }
    }

    static void updateTrendlineDatalabelsStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        Boolean enabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesTrendlineDatalabelsStatus(holder);
            FrameworkChartAPI.updateSharedSeriesTrendlineDatalabelsStatus(chartMeta, enabled);
        } else {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsStatus(chartMeta, enabled, seriesIndex);
        }
    }

    static void updateTrendlineDatalabelsStyle(SubActionDataHolder holder) {
        updateTrendlineDatalabelsFontSize(holder);
        updateTrendlineDatalabelsFontStyle(holder);
        updateTrendlineDatalabelsFontWeight(holder);
        updateTrendlineDatalabelsFontColor(holder);
    }

    private static void updateTrendlineDatalabelsFontColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesTrendlineDatalabelsFontColour(holder);
            FrameworkChartAPI.updateSharedSeriesTrendlineDatalabelsFontColour(chartMeta, color);
            CustomPropertiesActionHandler.updateSharedSeriesTrendlineDatalabelsFontColor(holder);
        } else {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFontColour(chartMeta, color, seriesIndex);
            CustomPropertiesActionHandler.updateSeriesTrendlineDatalabelsFontColor(holder, seriesIndex);
        }

    }

    private static void updateTrendlineDatalabelsFontSize(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesTrendlineDatalabelsFontSize(holder);
            FrameworkChartAPI.updateSharedSeriesTrendlineDatalabelsFontSize(chartMeta, fontSize);
        } else {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFontSize(chartMeta, fontSize, seriesIndex);
        }
    }

    private static void updateTrendlineDatalabelsFontStyle(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesTrendlineDatalabelsFontStyle(holder);
            FrameworkChartAPI.updateSharedSeriesTrendlineDatalabelsFontStyle(chartMeta, fontStyle);
        } else {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFontStyle(chartMeta, fontStyle, seriesIndex);
        }
    }

    private static void updateTrendlineDatalabelsFontWeight(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesTrendlineDatalabelsFontWeight(holder);
            FrameworkChartAPI.updateSharedSeriesTrendlineDatalabelsFontWeight(chartMeta, fontWeight);
        } else {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFontWeight(chartMeta, fontWeight, seriesIndex);
        }
    }

    static void updateTrendlineDatalabelsFormat(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Object format = valueJSON.get(FrameworkChartActionConstants.JSONConstants.FORMAT);
        Set<DataLabelFormatType> formatTypes = null;

        if(format instanceof String) {
            formatTypes = Collections.singleton(DataLabelFormatType.retrieveByValue(format.toString()));
        } else if(format instanceof JSONArrayWrapper) {
            formatTypes = new LinkedHashSet<>();
            List<String> formats = ChartUtils.JSONArrayToList((JSONArrayWrapper) format);

            for(String formatValue : formats) {
                formatTypes.add(DataLabelFormatType.retrieveByValue(formatValue));
            }
        }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesTrendlineDatalabelsFormat(holder);
            FrameworkChartAPI.updateSharedSeriesTrendlineDatalabelsFormat(chartMeta, formatTypes);
        } else {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsFormat(chartMeta, formatTypes, seriesIndex);
        }
    }

    static void updateTrendlineDatalabelsSeparator(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = optSeriesIndex(valueJSON);
        DatalabelSeparatorType separatorType = DatalabelSeparatorType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SEPARATOR));
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesTrendlineDatalabelsSeparator(dataHolder);
            FrameworkChartAPI.updateSharedSeriesTrendlineDatalabelsSeparator(chartMeta, separatorType);
        } else {
            FrameworkChartAPI.updateSeriesTrendlineDatalabelsSeparator(chartMeta, separatorType, seriesIndex);
        }
    }

    static void updateTrendlineDatalabelsCustomLabel(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Integer seriesIndex = optSeriesIndex(valueJSON);
        String customLabel = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TEXT);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesTrendlineDatalabelsCustomLabel(holder);
            ReferenceActionHandler.updateSharedSeriesTrendlineDatalabelsCustomValue(holder, customLabel);
        } else {
            ReferenceActionHandler.updateSeriesTrendlineDatalabelsCustomValue(holder, customLabel, seriesIndex);
        }
    }

    static void updateSeriesDataLabelsStyles(SubActionDataHolder holder) {
        updateSeriesDataLabelsFontStyles(holder);
        updateSeriesDataLabelsBgColor(holder);
        updateSeriesDataLabelsBorderColor(holder);
        updateSeriesDataLabelsBorderWidth(holder);
        updateSeriesDataLabelsBgOpacity(holder);
        updateSeriesDatalabelsBgFillType(holder);
        updateSeriesDatalabelsBorderFillType(holder);
        updateSeriesDatalabelsRotation(holder);
    }

    private static void updateSeriesDatalabelsRotation(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.ROTATION)) { return; }

        Integer seriesIndex = optSeriesIndex(valueJSON);
        String rotation = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.ROTATION);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsRotation(holder);
            FrameworkChartAPI.updateSharedSeriesDatalabelsRotation(chartMeta, rotation);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsRotation(holder);
            FrameworkChartAPI.updateSeriesDatalabelsRotation(chartMeta, rotation, seriesIndex);
        }
    }

    private static void updateSeriesDatalabelsBgFillType(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BG_COLOR_TYPE)) { return; }

        Integer seriesIndex = optSeriesIndex(valueJSON);
        FillType fillType = FillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.BG_COLOR_TYPE));
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsBgColorType(holder);
            FrameworkChartAPI.updateSharedSeriesDatalabelsBgFillType(chartMeta, fillType);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsBgColorType(holder);
            FrameworkChartAPI.updateSeriesDatalabelsBgFillType(chartMeta, fillType, seriesIndex);
        }
    }

    private static void updateSeriesDatalabelsBorderFillType(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_COLOR_TYPE)) { return; }

        Integer seriesIndex = optSeriesIndex(valueJSON);
        FillType fillType = FillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.BORDER_COLOR_TYPE));
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsBorderColorType(holder);
            FrameworkChartAPI.updateSharedSeriesDatalabelsBorderFillType(chartMeta, fillType);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsBorderColorType(holder);
            FrameworkChartAPI.updateSeriesDatalabelsBorderFillType(chartMeta, fillType, seriesIndex);
        }
    }

    private static void updateSeriesDataLabelsFontStyles(SubActionDataHolder holder) {
        updateSeriesDatalabelsFontColor(holder);
        updateSeriesDatalabelsFontSize(holder);
        updateSeriesDatalabelsFontStyle(holder);
        updateSeriesDatalabelsFontWeight(holder);
    }

    private static void updateSeriesDatalabelsFontColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsFontColor(holder);
            FrameworkChartAPI.updateSharedSeriesDatalabelsFontColor(chartMeta, color);
            CustomPropertiesActionHandler.updateSharedSeriesDataLabelsFontColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsFontColor(holder);
            FrameworkChartAPI.updateSeriesDatalabelsFontColor(chartMeta, color, seriesIndex);
            CustomPropertiesActionHandler.updateDataLabelsFontColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.FONT_COLOR, seriesIndex);
        }
    }

    private static void updateSeriesDatalabelsFontSize(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsFontSize(holder);
            FrameworkChartAPI.updateSharedSeriesDatalabelsFontSize(chartMeta, fontSize);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsFontSize(holder);
            FrameworkChartAPI.updateSeriesDatalabelsFontSize(chartMeta, fontSize, seriesIndex);
        }
    }

    private static void updateSeriesDatalabelsFontStyle(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsFontStyle(holder);
            FrameworkChartAPI.updateSharedSeriesDatalabelsFontStyle(chartMeta, fontStyle);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsFontStyle(holder);
            FrameworkChartAPI.updateSeriesDatalabelsFontStyle(chartMeta, fontStyle, seriesIndex);
        }
    }

    private static void updateSeriesDatalabelsFontWeight(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsFontWeight(holder);
            FrameworkChartAPI.updateSharedSeriesDatalabelsFontWeight(chartMeta, fontWeight);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsFontWeight(holder);
            FrameworkChartAPI.updateSeriesDatalabelsFontWeight(chartMeta, fontWeight, seriesIndex);
        }
    }

    private static void updateSeriesDataLabelsBgColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BG_COLOR)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.BG_COLOR);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsBgColor(holder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsBgColor(chartMeta, color);
            CustomPropertiesActionHandler.updateSharedSeriesDataLabelsBgColor(holder);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsBgColor(holder);
            FrameworkChartAPI.updateSeriesDataLabelsBgColor(chartMeta, color, seriesIndex);
            CustomPropertiesActionHandler.updateSeriesDataLabelsBgColor(holder);
        }
    }

    private static void updateSeriesDataLabelsBorderColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_COLOR)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsBorderColor(holder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsBorderColor(chartMeta, color);
            CustomPropertiesActionHandler.updateSharedSeriesDataLabelsBorderColor(holder);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsBorderColor(holder);
            FrameworkChartAPI.updateSeriesDataLabelsBorderColor(chartMeta, color, seriesIndex);
            CustomPropertiesActionHandler.updateSeriesDataLabelsBorderColor(holder);
        }
    }

    private static void updateSeriesDataLabelsBorderWidth(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_WIDTH)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        Integer width = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.BORDER_WIDTH);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsBorderWidth(holder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsBorderWidth(chartMeta, width);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsBorderWidth(holder);
            FrameworkChartAPI.updateSeriesDataLabelsBorderWidth(chartMeta, width, seriesIndex);
        }
    }

    private static void updateSeriesDataLabelsBgOpacity(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        Double opacity = ChartUtils.optDoubleFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.OPACITY);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsBgOpacity(holder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsBgColorOpacity(chartMeta, opacity);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsBgOpacity(holder);
            FrameworkChartAPI.updateSeriesDataLabelsBgColorOpacity(chartMeta, opacity, seriesIndex);
        }
    }

    static void updateSeriesThresholdValue(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        Object thresholdObj = valueJSON.get(FrameworkChartActionConstants.JSONConstants.THRESHOLD_VALUE);

        if(seriesIndex != null){
            SeriesPropsActionHandler.updateSharedSeriesThresholdValue(holder);
            FrameworkChartAPI.updateSeriesThresholdValueWithIndex(chartMeta, ChartUtils.toInteger(thresholdObj), seriesIndex);
        }else{
            FrameworkChartAPI.updateSharedSeriesThreshold(chartMeta, ChartUtils.toInteger(thresholdObj));
        }
    }

    static void updateSeriesThresholdStyle(SubActionDataHolder holder) {
        updateSeriesThresholdColor(holder);
        updateSeriesThresholdOpacity(holder);
    }

    private static void updateSeriesThresholdColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.THRESHOLD_COLOR)){ return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String thresholdColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.THRESHOLD_COLOR);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesThresholdColor(dataHolder);
            FrameworkChartAPI.updateSharedSeriesThresholdColor(chartMeta, thresholdColor);
            CustomPropertiesActionHandler.updateSharedSeriesNegativeColorThemeOptions(dataHolder, FrameworkChartActionConstants.JSONConstants.THRESHOLD_COLOR);
        } else {
            FrameworkChartAPI.updateSeriesThresholdColorWithIndex(chartMeta, thresholdColor, seriesIndex);
            CustomPropertiesActionHandler.updateNegativeColorThemeOptions(dataHolder, FrameworkChartActionConstants.JSONConstants.THRESHOLD_COLOR, seriesIndex);
        }
    }

    private static void updateSeriesThresholdOpacity(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.THRESHOLD_OPACITY)){ return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        Double opacity = ChartUtils.optDoubleFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.THRESHOLD_OPACITY);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesThresholdOpacity(dataHolder);
            FrameworkChartAPI.updateSharedSeriesThresholdOpacity(chartMeta, opacity);
        } else {
            FrameworkChartAPI.updateSeriesThresholdOpacity(chartMeta, opacity, seriesIndex);
        }
    }


    static void disableSeriesThreshold(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        if(seriesIndex != null){
            SeriesPropsActionHandler.updateSharedSeriesThresholdValue(holder);
            FrameworkChartAPI.disableSeriesThresholdWithIndex(chartMeta, seriesIndex);
        }else{
            FrameworkChartAPI.disableSharedSeriesThreshold(chartMeta);
        }
    }

    static void updateSeriesTrendLineStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesTrendlineStatus(holder);
            FrameworkChartAPI.updateSharedSeriesTrendLineStatus(chartMeta, status);
        }else{
            FrameworkChartAPI.updateTrendLineStatusWithIndex(chartMeta, status, seriesIndex);
        }
    }

    /**
     * Method to handle update trendline type and order value
     *
     * @param holder Bundled up sub action data
     */
    static void updateSeriesTrendLine(SubActionDataHolder holder) {
        updateTrendlineType(holder);
        updateTrendlineOrder(holder);
    }

    private static void updateTrendlineType(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TYPE)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        TrendLineType type = TrendLineType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesTrendlineType(holder);
            SeriesPropsActionHandler.updateSharedSeriesTrendlineStatus(holder);

            FrameworkChartAPI.updateSharedSeriesTrendLineType(chartMeta, type);
            FrameworkChartAPI.updateSharedSeriesTrendLineStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE);
        } else {
            FrameworkChartAPI.updateTrendLineStatusWithIndex(chartMeta, FrameworkChartActionConstants.Constants.ENABLE, seriesIndex);
            FrameworkChartAPI.updateTrendLineTypeWithIndex(chartMeta, type, seriesIndex);
        }
    }

    private static void updateTrendlineOrder(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.ORDER)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        int order = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.ORDER);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesTrendlineOrder(holder);
            FrameworkChartAPI.updateSharedSeriesTrendLineOrder(chartMeta, order);
        } else {
            FrameworkChartAPI.updateTrendLineOrderWithIndex(chartMeta, order, seriesIndex);
        }
    }

    /**
     * Method to update trend line styling
     *
     * @param holder SubActionDataHolder
     */
    static void updateSeriesTrendLineStyle(SubActionDataHolder holder) {
        updateSeriesTrendlineColor(holder);
        updateSeriesTrendlineLineStyle(holder);
        updateSeriesTrendlineColorOpacity(holder);
    }

    private static void updateSeriesTrendlineColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        int seriesIndex = getSeriesIndex(valueJSON);
        String lineColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);

        FrameworkChartAPI.updateTrendLineColorWithIndex(chartMeta, lineColor, seriesIndex);
        CustomPropertiesActionHandler.updateTrendlineColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.LINE_COLOR, seriesIndex);
    }

    private static void updateSeriesTrendlineLineStyle(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_TYPE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        int seriesIndex = getSeriesIndex(valueJSON);
        LineStyleType lineStyleType = LineStyleType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_TYPE));

        FrameworkChartAPI.updateTrendLineStyleTypeWithIndex(chartMeta, lineStyleType, seriesIndex);
    }

    private static void updateSeriesTrendlineColorOpacity(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        double colorOpacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesTrendlineColorOpacity(holder);
            FrameworkChartAPI.updateSharedSeriesTrendLineColorOpacity(chartMeta, colorOpacity);
        } else {
            FrameworkChartAPI.updateTrendLineColorOpacity(chartMeta, colorOpacity, seriesIndex);
        }
    }

    /**
     * Method to update Series styling
     *
     * @param holder SubActionDataHolder
     */
    static void updateSeriesStyle(SubActionDataHolder holder) {
        updateSeriesColorOpacity(holder);
        updateSeriesColor(holder);
        updateSeriesLineType(holder);
        updateSeriesAreaOpacity(holder);
        updateSeriesBorderColor(holder);
        updateSeriesBorderRadius(holder);
        updateSeriesBorderWidth(holder);
        updateSeriesBorderOpacity(holder);
        updateSeriesBorderFillType(holder);
    }

    private static void updateSeriesBorderRadius(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_RADIUS)){ return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        int borderRadius = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BORDER_RADIUS);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesBorderRadius(holder);
            FrameworkChartAPI.updateSharedSeriesBorderRadius(chartMeta, borderRadius);
        } else {
            SeriesPropsActionHandler.updateSeriesBorderRadius(holder);
            FrameworkChartAPI.updateSeriesBorderRadius(chartMeta, borderRadius, seriesIndex);
        }
    }

    private static void updateSeriesBorderWidth(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_WIDTH)){ return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        int borderWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BORDER_WIDTH);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesBorderWidth(holder);
            FrameworkChartAPI.updateSharedSeriesBorderWidth(chartMeta, borderWidth);
        } else {
            SeriesPropsActionHandler.updateSeriesBorderWidth(holder);
            FrameworkChartAPI.updateSeriesBorderWidth(chartMeta, borderWidth, seriesIndex);
        }
    }

    private static void updateSeriesBorderOpacity(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_OPACITY)){ return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.BORDER_OPACITY);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesBorderOpacity(holder);
            FrameworkChartAPI.updateSharedSeriesBorderOpacity(chartMeta, opacity);
        } else {
            SeriesPropsActionHandler.updateSeriesBorderOpacity(holder);
            FrameworkChartAPI.updateSeriesBorderOpacity(chartMeta, opacity, seriesIndex);
        }
    }

    private static void updateSeriesBorderFillType(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_FILL_TYPE)){ return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        FillType fillType = FillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.BORDER_FILL_TYPE));

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesBorderFillType(holder);
            FrameworkChartAPI.updateSharedSeriesBorderFillType(chartMeta, fillType);
        } else {
            SeriesPropsActionHandler.updateSeriesBorderFillType(holder);
            FrameworkChartAPI.updateSeriesBorderFillType(chartMeta, fillType, seriesIndex);
        }
    }

    private static void updateSeriesBorderColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_COLOR)){ return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        String borderColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesBorderColor(holder);
            FrameworkChartAPI.updateSharedSeriesBorderColor(chartMeta, borderColor);
            CustomPropertiesActionHandler.updateSharedSeriesBorderColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR);
        } else {
            SeriesPropsActionHandler.updateSeriesBorderColor(holder);
            FrameworkChartAPI.updateSeriesBorderColorWithIndex(chartMeta, borderColor, seriesIndex);
            CustomPropertiesActionHandler.updateSeriesBorderColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR, seriesIndex);
        }

    }

    private static void updateSeriesAreaOpacity(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.AREA_OPACITY)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.AREA_OPACITY);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesAreaOpacity(holder);
            FrameworkChartAPI.updateSharedSeriesAreaOpacity(chartMeta, opacity);
        } else {
            FrameworkChartAPI.updateSeriesAreaOpacityWithIndex(chartMeta, opacity, seriesIndex);
        }
    }

    private static void updateSeriesLineType(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_TYPE)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        LineStyleType lineStyleType =  LineStyleType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_TYPE));

        FrameworkChartAPI.updateSeriesLineTypeWithIndex(chartMeta, lineStyleType, getSeriesIndex(valueJSON));
    }

    private static void updateSeriesColorOpacity(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesColorOpacity(dataHolder);
            FrameworkChartAPI.updateSharedSeriesColorOpacity(chartMeta, opacity);
        } else {
            SeriesPropsActionHandler.updateSeriesColorOpacity(dataHolder);
            FrameworkChartAPI.updateSeriesColorOpacity(chartMeta, opacity, seriesIndex);
        }
    }

    private static void updateSeriesColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SERIES_COLOR)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = getSeriesIndex(valueJSON);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_COLOR);

        SeriesPropsActionHandler.updateSeriesColor(dataHolder);
        FrameworkChartAPI.updateSeriesColorWithIndex(chartMeta, color, seriesIndex);
        CustomPropertiesActionHandler.updateSeriesColorThemeOptions(dataHolder);
    }

    /**
     * Method to handle marker status update
     *
     * @param holder Bundled up sub action data
     */
    static void updateMarkerStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesMarkerStatus(holder);
            FrameworkChartAPI.updateSharedSeriesMarkerStatus(chartMeta, status);
        }else{
            SeriesPropsActionHandler.updateSeriesMarkerStatus(holder);
            FrameworkChartAPI.updateSeriesMarkerStatusWithIndex(chartMeta, status, seriesIndex);
        }
    }

    /**
     * Method to handle marker style update
     *
     * @param holder Bundled up sub action data
     */
    static void updateMarkerStyle(SubActionDataHolder holder) {
        updateSeriesMarkerColor(holder);
        updateSeriesMarkerBorderColor(holder);
        updateSeriesMarkerShape(holder);
        updateSeriesMarkerSize(holder);
    }

    private static void updateSeriesMarkerSize(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_SIZE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        int markerSize = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.MARKER_SIZE);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesMarkerSize(dataHolder);
            FrameworkChartAPI.updateSharedSeriesMarkerSize(chartMeta, markerSize);
        } else {
            SeriesPropsActionHandler.updateSeriesMarkerSize(dataHolder);
            FrameworkChartAPI.updateSeriesMarkerSizeWithIndex(chartMeta, markerSize, seriesIndex);
        }
    }

    private static void updateSeriesMarkerShape(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_SHAPE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        Integer seriesIndex = optSeriesIndex(valueJSON);
        MarkerShapeType shapeType = MarkerShapeType.retrieveByValue(valueJSON.getString(
                FrameworkChartActionConstants.JSONConstants.MARKER_SHAPE));

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesMarkerShape(holder);
            FrameworkChartAPI.updateSharedSeriesMarkerShape(chartMeta, shapeType);
        } else {
            SeriesPropsActionHandler.updateSeriesMarkerShape(holder);
            FrameworkChartAPI.updateSeriesMarkerShapeWithIndex(chartMeta, shapeType, seriesIndex);
        }
    }

    private static void updateSeriesMarkerBorderColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_BORDER_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        String borderColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.MARKER_BORDER_COLOR);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesMarkerBorderColor(holder);
            FrameworkChartAPI.updateSharedSeriesMarkerBorderColor(chartMeta, borderColor);
            CustomPropertiesActionHandler.updateSharedSeriesMarkerBorderColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.MARKER_BORDER_COLOR);
        } else {
            SeriesPropsActionHandler.updateSeriesMarkerBorderColor(holder);
            FrameworkChartAPI.updateSeriesMarkerBorderColorWithIndex(chartMeta, borderColor, seriesIndex);
            CustomPropertiesActionHandler.updateMarkerBorderColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.MARKER_BORDER_COLOR, seriesIndex);
        }
    }

    private static void updateSeriesMarkerColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_COLOR)){ return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        String markerColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.MARKER_COLOR);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesMarkerColor(holder);
            FrameworkChartAPI.updateSharedSeriesMarkerColor(chartMeta, markerColor);
            CustomPropertiesActionHandler.updateSharedSeriesMarkerColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.MARKER_COLOR);
        } else {
            SeriesPropsActionHandler.updateSeriesMarkerColor(holder);
            FrameworkChartAPI.updateSeriesMarkerColorWithIndex(chartMeta, markerColor, seriesIndex);
            CustomPropertiesActionHandler.updateMarkerColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.MARKER_COLOR, seriesIndex);
        }
    }

    /**
     * Method to handle update in X-Axis inverted status
     *
     * @param holder Bundled up sub action data
     */
    static void updateXAxisInvertedStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean isInverted = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_INVERTED);
        Integer xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX);

        FrameworkChartAPI.updateXAxisInvertedStatusWithIndex(chartMeta, isInverted,
                ChartUtils.requireNonNullElse(xAxisIndex, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX));
    }

    /**
     * Method to handle update in X-Axis label status
     *
     * @param holder Bundled up sub action data
     */
    static void updateXAxisLabelStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean isEnabled = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        Integer xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX);

        FrameworkChartAPI.updateXAxisLabelStatusWithIndex(chartMeta, isEnabled,
                ChartUtils.requireNonNullElse(xAxisIndex, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX));
    }

    /**
     * Method to handle X-Axis label styling update
     *
     * @param holder Bundled up sub action data
     */
    static void updateXAxisLabelStyle(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        String slantAngle = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SLANT_ANGLE);
        String staggerLines = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STAGGER_LINES);
        String textOverflow = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT_OVERFLOW);
        Font sourceFont = new Font(new ColorThemeFontJSONConstants());
        int xAxisIndex = ChartUtils.requireNonNullElse(ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX), FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        sourceFont.getFromJSONObject(valueJSON);
        sourceFont.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));
        // if slant angle is not null, then proceed updating slant angle
        ChartUtils.ifNotNullThen(slantAngle, () -> FrameworkChartAPI.updateXAxisLabelSlantAngleWithIndex(chartMeta, slantAngle, xAxisIndex));
        // if stagger lines is not null, then proceed updating stagger lines
        ChartUtils.ifNotNullThen(staggerLines, () -> FrameworkChartAPI.updateXAxisLabelsStaggerLinesWithIndex(chartMeta, staggerLines, xAxisIndex));
        // if text overflow is not null, then proceed updating text overflow
        ChartUtils.ifNotNullThen(textOverflow, () -> FrameworkChartAPI.updateXAxisLabelsTextOverFlow(chartMeta, TextOverFlowType.retrieveByValue(textOverflow), xAxisIndex));
        // updating label font styling
        FrameworkChartAPI.updateXAxisLabelsFontStyleWithIndex(chartMeta, sourceFont, xAxisIndex);
        CustomPropertiesActionHandler.updateXAxisLabelsFontColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.FONT_COLOR, xAxisIndex);
    }

    /**
     * Method to update Y-Axis label status
     *
     * @param holder Bundled by sub action data
     */
    static void updateYAxisLabelStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean isEnabled = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        Integer yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);

        FrameworkChartAPI.updateYAxisLabelStatusWithIndex(chartMeta, isEnabled,
                ChartUtils.requireNonNullElse(yAxisIndex, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX));
    }

    /**
     * Method to update Y-Axis label font style
     *
     * @param holder Bundled by sub action data
     */
    static void updateYAxisLabelStyle(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Font sourceFont = new Font(new ColorThemeFontJSONConstants());
        Integer yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);

        sourceFont.getFromJSONObject(valueJSON);
        sourceFont.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));

        if(yAxisIndex == null){
            FrameworkChartAPI.updateYAxisLabelFontStyleWithIndex(chartMeta, sourceFont, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
            CustomPropertiesActionHandler.updateYAxisLabelsFontColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.FONT_COLOR, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        }else{
            FrameworkChartAPI.updateYAxisLabelFontStyleWithIndex(chartMeta, sourceFont, yAxisIndex);
            CustomPropertiesActionHandler.updateYAxisLabelsFontColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.FONT_COLOR, yAxisIndex);
        }
    }

    /**
     * Method to update Y-Axis scale type
     *
     * @param holder Bundled up sub action data
     */
    static void updateYAxisScaleType(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        YAxisScaleType type = YAxisScaleType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));
        Integer yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);

        FrameworkChartAPI.updateYAxisScaleTypeWithIndex(chartMeta, type,
                ChartUtils.requireNonNullElse(yAxisIndex, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX));
    }

    /**
     * Method to update Y-Axis logarithmic base value
     *
     * @param holder Bundled up sub action data
     */
    static void updateYAxisLogBaseValue(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Integer yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int baseValue = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BASE_VALUE);

        FrameworkChartAPI.updateYAxisLogBaseValueWithIndex(chartMeta, baseValue,
                ChartUtils.requireNonNullElse(yAxisIndex, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX));
    }

    /**
     * Method to handle plot line status update
     *
     * @param holder Bundled up sub action data
     */
    static void addNewPlotLine(SubActionDataHolder holder) {
        updatePlotline(holder);
    }

    /**
     * Method to handle plot line edit
     *
     * @param holder Bundled up sub action data
     */
    static void updatePlotline(SubActionDataHolder holder) {
        updatePlotLineValue(holder);
        updatePlotLineLabel(holder);
    }

    private static void updatePlotLineValue(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int plotLineIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);
        Object value = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.VALUE);

        if(value != null) {
            ReferenceActionHandler.updateChartPlotlineValue(dataHolder, value, yAxisIndex, plotLineIndex);
        }
    }

    private static void updatePlotLineLabel(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int plotLineIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);
        String label = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT);

        if(label != null) {
            ReferenceActionHandler.updateChartPlotlineLabel(dataHolder, label, yAxisIndex, plotLineIndex);
        }
    }

    /**
     * Method to handle plot line style update
     *
     * @param holder Bundled up sub action data
     */
    static void updatePlotlineStyle(SubActionDataHolder holder) {
        updatePlotlineLabelStyle(holder);
        updatePlotlineLineStyle(holder);
    }

    private static void updatePlotlineLabelStyle(SubActionDataHolder dataHolder) {
        updatePlotlineLabelFontColor(dataHolder);
        updatePlotlineLabelFontSize(dataHolder);
        updatePlotlineLabelFontStyle(dataHolder);
        updatePlotlineLabelFontWeight(dataHolder);
    }

    private static void updatePlotlineLabelFontColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int plotLineIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        String fontColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
        Font sourceFont = new Font(new DefaultFontJSONConstants());

        sourceFont.setFontColor(fontColor);
        FrameworkChartAPI.updatePlotLineFontStyle(chartMeta, sourceFont, yAxisIndex, plotLineIndex);
        CustomPropertiesActionHandler.updatePlotlineTitleColorThemeOptions(dataHolder);
    }

    private static void updatePlotlineLabelFontSize(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int plotLineIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);
        Font sourceFont = new Font(new DefaultFontJSONConstants());

        sourceFont.setFontSize(fontSize);
        FrameworkChartAPI.updatePlotLineFontStyle(chartMeta, sourceFont, yAxisIndex, plotLineIndex);
    }
    private static void updatePlotlineLabelFontStyle(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int plotLineIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);
        Font sourceFont = new Font(new DefaultFontJSONConstants());

        sourceFont.setFontStyle(fontStyle);
        FrameworkChartAPI.updatePlotLineFontStyle(chartMeta, sourceFont, yAxisIndex, plotLineIndex);
    }
    private static void updatePlotlineLabelFontWeight(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int plotLineIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);
        Font sourceFont = new Font(new DefaultFontJSONConstants());

        sourceFont.setFontWeight(fontWeight);
        FrameworkChartAPI.updatePlotLineFontStyle(chartMeta, sourceFont, yAxisIndex, plotLineIndex);
    }

    private static void updatePlotlineLineStyle(SubActionDataHolder dataHolder) {
        updatePlotlineLineStyleType(dataHolder);
        updatePlotlineLineWidth(dataHolder);
        updatePlotlineLineColor(dataHolder);
    }

    private static void updatePlotlineLineColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int plotLineIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        String lineColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);
        FrameworkChartAPI.updatePlotLineColor(chartMeta, lineColor, yAxisIndex, plotLineIndex);
        CustomPropertiesActionHandler.updatePlotlineColorThemeOptions(dataHolder, FrameworkChartActionConstants.JSONConstants.LINE_COLOR, yAxisIndex, plotLineIndex);
    }

    private static void updatePlotlineLineStyleType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_TYPE)) { return; }
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int plotLineIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        LineStyleType lineType = LineStyleType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_TYPE));

        FrameworkChartAPI.updatePlotLineLineType(chartMeta, lineType, yAxisIndex, plotLineIndex);
    }

    private static void updatePlotlineLineWidth(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH)) { return; }
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int plotLineIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updatePlotlineWidth(chartMeta, lineWidth, yAxisIndex, plotLineIndex);
    }

    /**
     * Method to handle Y-Axis Range edit action
     *
     * @param holder Bundled up sub action data
     */
    static void updateYAxisRange(SubActionDataHolder holder) {
        updateMaxValue(holder);
        updateMinValue(holder);
        updateInterval(holder);
    }

    private static void updateMaxValue(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MAX)) { return; }
        String reference = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.MAX);
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);

        ReferenceActionHandler.updateYAxisMaxValue(dataHolder, reference, yAxisIndex);
    }

    private static void updateMinValue(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MIN)) { return; }
        String reference = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.MIN);
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);

        ReferenceActionHandler.updateYAxisMinValue(dataHolder, reference, yAxisIndex);
    }

    private static void updateInterval(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.INTERVAL)) { return; }
        String reference = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.INTERVAL);
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);

        ReferenceActionHandler.updateYAxisIntervalValue(dataHolder, reference, yAxisIndex);
    }

    /**
     * Method to handle Y-Axis format update
     *
     * @param holder Bundled up sub action data
     */
    static void updateYAxisUnitFormat(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        String unitFormat = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.UNIT_FORMAT);
        Double customValue = ChartUtils.optDoubleFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.UNIT_FORMAT_CUSTOM_VALUE);

        if(unitFormat != null) {
            FrameworkChartAPI.updateYAxisUnitFormat(chartMeta, YAxisUnitFormatType.retrieveByValue(unitFormat), yAxisIndex);
        }
        if(customValue != null) {
            FrameworkChartAPI.updateYAxisUnitFormatCustomValue(chartMeta, customValue, yAxisIndex);
        }
    }

    /**
     * Method to handle prefix and suffix edit event
     *
     * @param holder Bundled up sub action data
     */
    static void updateYAxisNumberFormat(SubActionDataHolder holder) {
        updateYAxisPrefix(holder);
        updateYAxisSuffix(holder);
    }

    private static void updateYAxisPrefix(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.PREFIX)) { return; }

        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        String prefix = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.PREFIX);

        ReferenceActionHandler.updateYAxisPrefixValue(holder, prefix, yAxisIndex);
    }

    private static void updateYAxisSuffix(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SUFFIX)) { return; }

        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        String suffix = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SUFFIX);

        ReferenceActionHandler.updateYAxisSuffixValue(holder, suffix, yAxisIndex);
    }

    static void updateXAxisMajorGLStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();

        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateXAxisMajorGLStatus(chartMeta, status, xAxisIndex);
    }

    static void updateXAxisMajorGL(SubActionDataHolder holder) {
        updateXAxisMajorGLColor(holder);
        updateXAxisMajorGLLineType(holder);
        updateXAxisMajorGLOpacity(holder);
        updateXAxisMajorGLCount(holder);
        updateXAxisMajorGLWidth(holder);
    }

    private static void updateXAxisMajorGLOpacity(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateXAxisMajorGLOpacity(chartMeta, opacity, xAxisIndex);
    }

    private static void updateXAxisMajorGLCount(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.GRIDLINE_COUNT)) { return; }
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        GridLineCountType countType = GridLineCountType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.GRIDLINE_COUNT));

        FrameworkChartAPI.updateXAxisMajorGLCount(chartMeta, countType, xAxisIndex);
    }

    private static void updateXAxisMajorGLColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }
        String lineColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        FrameworkChartAPI.updateXAxisMajorGLColor(chartMeta, lineColor, xAxisIndex);
        CustomPropertiesActionHandler.updateXAxisMajorGLColorThemeOptions(holder);
    }

    private static void updateXAxisMajorGLWidth(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH)) { return; }
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updateXAxisMajorGLWidth(chartMeta, lineWidth, xAxisIndex);
    }

    private static void updateXAxisMajorGLLineType(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_TYPE)) { return; }
        LineStyleType type = LineStyleType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_TYPE));
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        FrameworkChartAPI.updateXAxisMajorGLType(chartMeta, type, xAxisIndex);
    }

    static void updateXAxisMinorGlStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateXAxisMinorGLStatus(chartMeta, status, xAxisIndex);
    }

    static void updateXAxisMinorGL(SubActionDataHolder holder) {
        updateXAxisMinorGLColor(holder);
        updateXAxisMinorGLLineType(holder);
        updateXAxisMinorGLOpacity(holder);
        updateXAxisMinorGLWidth(holder);
    }

    private static void updateXAxisMinorGLOpacity(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateXAxisMinorGLOpacity(chartMeta, opacity, xAxisIndex);
    }

    private static void updateXAxisMinorGLColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }
        String lineColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        FrameworkChartAPI.updateXAxisMinorGLColor(chartMeta, lineColor, xAxisIndex);
        CustomPropertiesActionHandler.updateXAxisMinorGLColorThemeOptions(holder);
    }

    private static void updateXAxisMinorGLWidth(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH)) { return; }
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updateXAxisMinorGLWidth(chartMeta, lineWidth, xAxisIndex);
    }

    private static void updateXAxisMinorGLLineType(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_TYPE)) { return; }
        LineStyleType type = LineStyleType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_TYPE));
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        FrameworkChartAPI.updateXAxisMinorGLType(chartMeta, type, xAxisIndex);
    }

    static void updateYAxisMajorGLStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateYAxisMajorGLStatus(chartMeta, status, yAxisIndex);
    }

    static void updateYAxisMajorGL(SubActionDataHolder holder) {
        updateYAxisMajorGLColor(holder);
        updateYAxisMajorGLLineType(holder);
        updateYAxisMajorGLOpacity(holder);
        updateYAxisMajorGLCount(holder);
        updateYAxisMajorGLWidth(holder);
    }

    private static void updateYAxisMajorGLOpacity(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateYAxisMajorGLOpacity(chartMeta, opacity, yAxisIndex);
    }

    private static void updateYAxisMajorGLCount(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.GRIDLINE_COUNT)) { return; }
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        GridLineCountType countType = GridLineCountType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.GRIDLINE_COUNT));

        FrameworkChartAPI.updateYAxisMajorGLCount(chartMeta, countType, yAxisIndex);
    }

    private static void updateYAxisMajorGLColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }
        String lineColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        FrameworkChartAPI.updateYAxisMajorGLColor(chartMeta, lineColor, yAxisIndex);
        CustomPropertiesActionHandler.updateYAxisMajorGLColorThemeOptions(holder);
    }

    private static void updateYAxisMajorGLWidth(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH)) { return; }
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updateYAxisMajorGLWidth(chartMeta, lineWidth, yAxisIndex);
    }

    private static void updateYAxisMajorGLLineType(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_TYPE)) { return; }
        LineStyleType type = LineStyleType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_TYPE));
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        FrameworkChartAPI.updateYAxisMajorGLType(chartMeta, type, yAxisIndex);
    }

    static void updateYAxisMinorGLStatus(SubActionDataHolder holder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateYAxisMinorGLStatus(chartMeta, status, yAxisIndex);
    }

    static void updateYAxisMinorGL(SubActionDataHolder holder) {
        updateYAxisMinorGLOpacity(holder);
        updateYAxisMinorGLColor(holder);
        updateYAxisMinorGLLineType(holder);
        updateYAxisMinorGLWidth(holder);
    }

    private static void updateYAxisMinorGLOpacity(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateYAxisMinorGLOpacity(chartMeta, opacity, yAxisIndex);
    }

    private static void updateYAxisMinorGLColor(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }
        String lineColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        FrameworkChartAPI.updateYAxisMinorGLColor(chartMeta, lineColor, yAxisIndex);
        CustomPropertiesActionHandler.updateYAxisMinorGLColorThemeOptions(holder);
    }

    private static void updateYAxisMinorGLWidth(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH)) { return; }
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updateYAxisMinorGLWidth(chartMeta, lineWidth, yAxisIndex);
    }

    private static void updateYAxisMinorGLLineType(SubActionDataHolder holder) {
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_TYPE)) { return; }
        LineStyleType type = LineStyleType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_TYPE));
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX,
                FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);

        FrameworkChartAPI.updateYAxisMinorGLType(chartMeta, type, yAxisIndex);
    }
    
    static void updateYAxisStackLabelsStatus(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateYAxisStackLabelsStatus(chartMeta, status, yAxisIndex);
    }

    static void updateYAxisStackLabels(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Font sourceFont = new Font(new ColorThemeFontJSONConstants());
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);

        sourceFont.getFromJSONObject(valueJSON);
        sourceFont.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));
        FrameworkChartAPI.updateYAxisStackLabelFontStyle(chartMeta, sourceFont, yAxisIndex);
        CustomPropertiesActionHandler.updateYAxisStackLabelsFontColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.FONT_COLOR, yAxisIndex);
    }

    static void updateChartFontFamily(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        String fontFamily = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_FAMILY);

        FrameworkChartAPI.updateChartFontFamily(chartMeta, fontFamily);
    }

    static void updateChartBGColor(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        String bgColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.BG_COLOR);

        Objects.requireNonNull(bgColor);
        FrameworkChartAPI.updateChartBGColor(chartMeta, bgColor);
        CustomPropertiesActionHandler.updateChartBackgroundColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.BG_COLOR);
    }

    static void updateChartAxisInvertedStatus(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean invertedStatus = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_AXIS_INVERTED);

        FrameworkChartAPI.updateChartAxisInvertedStatus(chartMeta, invertedStatus);
    }

    /**
     * Method to handle chart tool tip status update
     * @param holder Sub Action data holder
     */
    static void updateTooltipStatus(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateTooltipStatus(chartMeta, status);
    }

    static void updateChartGradientStatus(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateChartGradientStatus(chartMeta, status);
    }

    static void updateSliceStartAngle(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int startAngle = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.START_ANGLE);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesStartAngle(holder);
            FrameworkChartAPI.updateSharedSeriesStartAngle(chartMeta, startAngle);
        }else{
            FrameworkChartAPI.updateSliceStartAngle(chartMeta, startAngle, seriesIndex);
        }
    }

    static void updateSliceEndAngle(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int endAngle = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.END_ANGLE);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesEndAngle(holder);
            FrameworkChartAPI.updateSharedSeriesEndAngle(chartMeta, endAngle);
        }else{
            FrameworkChartAPI.updateSliceEndAngle(chartMeta, endAngle, seriesIndex);
        }
    }

    static void updateTargetColor(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        String targetColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.TARGET_COLOR);

        FrameworkChartAPI.updateSharedSeriesTargetColor(chartMeta, targetColor);
        CustomPropertiesActionHandler.updateSharedSeriesTargetColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.TARGET_COLOR);
    }

    static void updateUpColorStyles(SubActionDataHolder holder){
        updateUpColor(holder);
        updateUpColorOpacity(holder);
    }

    private static void updateUpColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.UP_COLOR)){ return; }
        String upColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.UP_COLOR);

        FrameworkChartAPI.updateUpColor(chartMeta, upColor, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        CustomPropertiesActionHandler.updateSeriesUpColorThemeOptions(dataHolder);
    }

    private static void updateUpColorOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.UP_OPACITY)){ return; }
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.UP_OPACITY);

        FrameworkChartAPI.updateUpOpacity(chartMeta, opacity, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    static void updateDownColorStyles(SubActionDataHolder holder){
        updateDownColor(holder);
        updateDownOpacity(holder);
    }

    private static void updateDownColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.DOWN_COLOR)){ return; }
        String downColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.DOWN_COLOR);

        FrameworkChartAPI.updateDownColor(chartMeta, downColor, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        CustomPropertiesActionHandler.updateSeriesDownColorThemeOptions(dataHolder);
    }

    private static void updateDownOpacity(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.DOWN_OPACITY)){ return; }
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.DOWN_OPACITY);

        FrameworkChartAPI.updateDownOpacity(chartMeta, opacity, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    static void updateSortOptions(SubActionDataHolder holder){
        updateSortOrder(holder);
        updateSortByKey(holder);
    }

    private static void updateSortOrder(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SORT_ORDER)){ return; }
        SortOrderType orderType = SortOrderType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SORT_ORDER));

        FrameworkChartAPI.updateSortOrder(chartMeta, orderType, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    private static void updateSortByKey(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SORT_KEY)){ return; }
        SortByKeyType keyType = SortByKeyType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SORT_KEY));

        FrameworkChartAPI.updateSortKey(chartMeta, keyType, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    static void updateBarCount(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        int max = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.MAX);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        FrameworkChartAPI.updateXAxisMaxWithIndex(chartMeta, max, xAxisIndex);
    }

    static void updateAnimationDuration(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        int duration = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DURATION);

        FrameworkChartAPI.updateAnimationDuration(chartMeta, duration);
    }

    static void updateCaptionStatus(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean isEnabled = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateCaptionStatus(chartMeta, isEnabled);
    }

    static void updateCaptionStyle(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        Font font = new Font(new ColorThemeFontJSONConstants());

        font.getFromJSONObject(valueJSON);
        font.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));

        FrameworkChartAPI.updateCaptionFontStyle(chartMeta, font);
        CustomPropertiesActionHandler.updateCaptionFontColorThemeOptions(holder, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
    }

    static void updateCumulationStatus(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        boolean isEnabled = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateSeriesCumulateStatus(chartMeta, isEnabled, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    static void updateDecimalPlace(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int decimals = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DECIMALS);

        FrameworkChartAPI.updateDecimalPlace(chartMeta, decimals, seriesIndex);
    }

    /**
     * Handles Animation status update action.
     * @param holder Bundled up sub action data
     */
    static void updateAnimationStatus(SubActionDataHolder holder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(holder);
        JSONObjectWrapper valueJSON = holder.getValueJSON();

        FrameworkChartAPI.updateAnimationStatus(chartMeta, valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED));
    }

    static void updateChartType(SubActionDataHolder holder){
        Chart chart = ChartUtils.getChartFromActionDataHolder(holder);
        String chartType = holder.getValueJSON().getString(FrameworkChartActionConstants.JSONConstants.TYPE);

        FrameworkChartAPI.updateChartType(chart.getApiMeta().getChartMeta(), ChartType.retrieveByValue(chartType));
        chart.onChartTypeChange();
    }

    static void updateLegendStatus(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        boolean legendStatus = dataHolder.getValueJSON().getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateLegendStatus(chartMeta, legendStatus);
    }

    static void updateDataLabelsStatus(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        boolean dataLabelsStatus = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsStatus(dataHolder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsStatus(chartMeta, dataLabelsStatus);
        }else{
            SeriesPropsActionHandler.updateSeriesDatalabelsStatus(dataHolder);
            FrameworkChartAPI.updateSeriesDataLabelsStatusWithIndex(chartMeta, dataLabelsStatus, seriesIndex);
        }
    }

    static void updateXAxisBinningInterval(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int binningInterval = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BINNING_INTERVAL);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        FrameworkChartAPI.updateXAxisBinningIntervalWithIndex(chartMeta, binningInterval, xAxisIndex);
    }

    static void updateXAxisLineColor(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        String lineColor = getColor(dataHolder.getValueJSON(), FrameworkChartActionConstants.JSONConstants.LINE_COLOR);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        FrameworkChartAPI.updateXAxisLineColorWithIndex(chartMeta, lineColor, xAxisIndex);
        CustomPropertiesActionHandler.updateXAxisBaselineColorThemeOptions(dataHolder, FrameworkChartActionConstants.JSONConstants.LINE_COLOR, xAxisIndex);
    }

    static void deletePlotLine(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        Integer plotlineIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.PLOTLINE_INDEX);

        if(plotlineIndex != null){
            ReferenceActionHandler.removeChartPlotline(dataHolder, yAxisIndex, plotlineIndex);
        }else{
            ReferenceActionHandler.removeChartAllPlotlines(dataHolder, yAxisIndex);
        }
    }

    static void updateSeriesDataProperty(SubActionDataHolder dataHolder){
        updateDataPropertyColor(dataHolder);
        updateDataPropertyBorderColor(dataHolder);
        updateDataPropertyMarkerStyles(dataHolder);
        updateDataPropertyOpacity(dataHolder);
        updateDataPropertyBorderWidth(dataHolder);
        updateDataPropertyBorderRadius(dataHolder);
        updateDataPropertyBorderOpacity(dataHolder);
        updateDataPropertyBorderFillType(dataHolder);
        updateSlicedProperty(dataHolder);
    }

    private static void updateSlicedProperty(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SLICED)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        boolean sliced = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.SLICED);

        FrameworkChartAPI.updateSlicedStatus(chartMeta, sliced, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyBorderWidth(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_WIDTH)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        int width = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BORDER_WIDTH);

        FrameworkChartAPI.updateSeriesDataPropertyBorderWidth(chartMeta, width, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyBorderRadius(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_RADIUS)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        int width = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BORDER_RADIUS);

        FrameworkChartAPI.updateSeriesDataPropertyBorderRadius(chartMeta, width, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyBorderOpacity(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_OPACITY)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        double opacity = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BORDER_OPACITY);

        FrameworkChartAPI.updateDataPropertyBorderOpacity(chartMeta, opacity, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyBorderFillType(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_FILL_TYPE)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        FillType fillType = FillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.BORDER_FILL_TYPE));

        FrameworkChartAPI.updateDataPropertyBorderFillType(chartMeta, fillType, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyColor(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.COLOR)){ return; }

        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.COLOR);

        FrameworkChartAPI.updateSeriesDataPropertyColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyColorThemeOptions(dataHolder, FrameworkChartActionConstants.JSONConstants.COLOR, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyBorderColor(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_COLOR)){ return; }

        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR);

        FrameworkChartAPI.updateSeriesDataPropertyBorderColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyBorderColorThemeOptions(dataHolder, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyMarkerStyles(SubActionDataHolder dataHolder){
        updateDataPropertyMarkerShape(dataHolder);
        updateDataPropertyMarkerSize(dataHolder);
        updateDataPropertyMarkerColor(dataHolder);
        updateDataPropertyMarkerBorderColor(dataHolder);
    }

    private static void updateDataPropertyMarkerShape(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_SHAPE)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        MarkerShapeType markerShapeType = MarkerShapeType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.MARKER_SHAPE));

        FrameworkChartAPI.updateSeriesDataPropertyMarkerShape(chartMeta, markerShapeType, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyMarkerSize(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_SIZE)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        int markerSize = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.MARKER_SIZE);

        FrameworkChartAPI.updateSeriesDataPropertyMarkerSize(chartMeta, markerSize, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyMarkerColor(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_COLOR)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.MARKER_COLOR);

        FrameworkChartAPI.updateDataPropertyMarkerColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyMarkerColor(dataHolder);
    }

    private static void updateDataPropertyMarkerBorderColor(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARKER_BORDER_COLOR)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.MARKER_BORDER_COLOR);

        FrameworkChartAPI.updateDataPropertyMarkerBorderColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyMarkerBorderColor(dataHolder);
    }

    private static void updateDataPropertyOpacity(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)){ return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        Double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateSeriesDataPropertyOpacity(chartMeta, opacity, seriesIndex, dataPropsIndex);
    }

    static void updateSortStatus(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        boolean isEnabled = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateSortStatus(chartMeta, isEnabled, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    static void updateSeriesChartType(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String chartType = valueJSON.optString(FrameworkChartActionConstants.JSONConstants.TYPE);

        FrameworkChartAPI.updateSeriesChartType(chartMeta, ChartType.retrieveByValue(chartType), seriesIndex);
    }

    static void updateTotalDataLabelStatus(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        FrameworkChartAPI.updateDonutTotalDatalabelsStatus(chartMeta, status);
    }

    static void updateTotalDataLabelFontStyle(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        Font font = new Font(new ColorThemeFontJSONConstants());
        font.getFromJSONObject(valueJSON);
        font.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));

        FrameworkChartAPI.updateTotalDatalabelsFont(chartMeta, font);
        CustomPropertiesActionHandler.updateDonutTotalDataLabelsFontColorThemeOptions(dataHolder, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
    }

    static void updateYAxisType(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        YAxisType yAxisType = YAxisType.retrieveByValue(valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS));

        FrameworkChartAPI.updateYAxisType(chartMeta, yAxisType, seriesIndex);
    }

    static void updateChartBackgroundColorOpacity(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateChartBackgroundOpacity(chartMeta, opacity);
    }

    static void updateGroupHeadersStatus(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateGroupHeaders(chartMeta, status);
    }

    static void updateShowOutliersStatus(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesShowOutlier(dataHolder);
            FrameworkChartAPI.updateSharedSeriesOutliersStatus(chartMeta, status);
        } else {
            FrameworkChartAPI.updateOutlierStatus(chartMeta, status, seriesIndex);
        }
    }

    static void updateShowMeanMarkersStatus(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        if(seriesIndex == null){
            SeriesPropsActionHandler.updateSharedSeriesShowMeanMarkers(dataHolder);
            FrameworkChartAPI.updateSharedSeriesMeanMarkerStatus(chartMeta, status);
        } else {
            FrameworkChartAPI.updateMeanMarkerStatus(chartMeta, status, seriesIndex);
        }
    }

    static void updateOutliersColor(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.OUTLIERS_COLOR);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesOutliersColor(dataHolder);
            FrameworkChartAPI.updateSharedSeriesOutliersColor(chartMeta, color);
            CustomPropertiesActionHandler.updateSharedSeriesOutliersColor(dataHolder);
        } else {
            SeriesPropsActionHandler.updateSeriesOutliersColor(dataHolder);
            FrameworkChartAPI.updateOutliersColor(chartMeta, color, seriesIndex);
            CustomPropertiesActionHandler.updateSeriesOutliersColor(dataHolder, seriesIndex);
        }
    }

    static void updateMeanColor(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.MEAN_COLOR);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesMeanColor(dataHolder);
            FrameworkChartAPI.updateSharedSeriesMeanColor(chartMeta, color);
            CustomPropertiesActionHandler.updateSharedSeriesMeanColor(dataHolder);
        } else {
            SeriesPropsActionHandler.updateSeriesMeanColor(dataHolder);
            FrameworkChartAPI.updateMeanColor(chartMeta, color, seriesIndex);
            CustomPropertiesActionHandler.updateSeriesMeanColor(dataHolder, seriesIndex);
        }
    }

    static void updateMedianColor(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.MEDIAN_COLOR);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesMedianColor(dataHolder);
            FrameworkChartAPI.updateSharedSeriesMedianColor(chartMeta, color);
            CustomPropertiesActionHandler.updateSharedSeriesMedianColor(dataHolder);
        } else {
            SeriesPropsActionHandler.updateSeriesMedianColor(dataHolder);
            FrameworkChartAPI.updateMedianColor(chartMeta, color, seriesIndex);
            CustomPropertiesActionHandler.updateSeriesMedianColor(dataHolder, seriesIndex);
        }
    }

    static void updateWhiskersColor(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.WHISKERS_COLOR);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesWhiskersColor(dataHolder);
            FrameworkChartAPI.updateSharedSeriesWhiskersColor(chartMeta, color);
            CustomPropertiesActionHandler.updateSharedSeriesWhiskersColor(dataHolder);
        } else {
            SeriesPropsActionHandler.updateSeriesWhiskersColor(dataHolder);
            FrameworkChartAPI.updateWhiskersColor(chartMeta, color, seriesIndex);
            CustomPropertiesActionHandler.updateSeriesWhiskersColor(dataHolder, seriesIndex);
        }

    }

    static void updatePointPadding(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        Double padding = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.PADDING);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesPointPadding(dataHolder);
            FrameworkChartAPI.updateSharedSeriesPointPadding(chartMeta, padding);
        } else {
            FrameworkChartAPI.updateSeriesPointPadding(chartMeta, padding, seriesIndex);
        }
    }

    static void updateGroupPadding(SubActionDataHolder dataHolder){
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        Double padding = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.PADDING);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesGroupPadding(dataHolder);
            FrameworkChartAPI.updateSharedSeriesGroupPadding(chartMeta, padding);
        } else {
            FrameworkChartAPI.updateSeriesGroupPadding(chartMeta, padding, seriesIndex);
        }
    }

    static void updateXAxisType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        XAxisType type = XAxisType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        FrameworkChartAPI.updateXAxisType(chartMeta, type, xAxisIndex);
    }

    static void updateSeriesConnectNullsStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        boolean isEnabled = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesConnectNulls(dataHolder);
            FrameworkChartAPI.updateSharedSeriesConnectNulls(chartMeta, isEnabled);
        } else {
            FrameworkChartAPI.updateSeriesConnectNulls(chartMeta, isEnabled, seriesIndex);
        }
    }

    static void updateXAxisBaselineWidth(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updateXAxisBaselineWidth(chartMeta, lineWidth, xAxisIndex);
    }

    static void updateDataPropertyDataLabelsStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        boolean isEnabled = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateDataPropertyDataLabelsStatus(chartMeta, isEnabled, seriesIndex, dataPropsIndex);
    }

    static void updateDataPropertyDataLabelsFormat(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);

        Object format = valueJSON.get(FrameworkChartActionConstants.JSONConstants.FORMAT);
        Set<DataLabelFormatType> formatTypes = null;

        if(format instanceof String) {
            formatTypes = Collections.singleton(DataLabelFormatType.retrieveByValue(format.toString()));

        } else if(format instanceof JSONArrayWrapper) {
            formatTypes = new LinkedHashSet<>();
            List<String> formats = ChartUtils.JSONArrayToList((JSONArrayWrapper) format);

            for(String formatValue : formats) {
                formatTypes.add(DataLabelFormatType.retrieveByValue(formatValue));
            }
        }

        FrameworkChartAPI.updateDataPropertyDataLabelsFormat(chartMeta, formatTypes, seriesIndex, dataPropsIndex);
    }

    static void updateDataPropertyDataLabelsPosition(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        DataLabelPositionType type = DataLabelPositionType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.POSITION));

        FrameworkChartAPI.updateDataPropertyDataLabelsPosition(chartMeta, type, seriesIndex, dataPropsIndex);
        FrameworkChartAPI.updateDataPropertyDataLabelsStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE, seriesIndex, dataPropsIndex);
    }

    static void updateDataPropertyDataLabelsStyle(SubActionDataHolder dataHolder) {
        updateDataPropertyDataLabelsFontStyle(dataHolder);
        updateDataPropertyDataLabelsBgColor(dataHolder);
        updateDataPropertyDataLabelsBorderColor(dataHolder);
        updateDataPropertyDataLabelsBorderWidth(dataHolder);
        updateDataPropertyDataLabelsBorderRadius(dataHolder);
        updateDataPropertyDataLabelsBgOpacity(dataHolder);
        updateDataPropertyDatalabelsBgFillType(dataHolder);
        updateDataPropertyDatalabelsBorderFillType(dataHolder);
        updateDataPropertyDatalabelsRotation(dataHolder);
    }

    private static void updateDataPropertyDatalabelsRotation(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.ROTATION)) { return; }

        String rotation = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.ROTATION);
        int seriesIndex = getSeriesIndex(valueJSON);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateDataPropertyDatalabelsRotation(chartMeta, rotation, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyDatalabelsBgFillType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BG_COLOR_TYPE)) { return; }

        FillType fillType = FillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.BG_COLOR_TYPE));
        int seriesIndex = getSeriesIndex(valueJSON);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateDataPropertyDatalabelBgFillType(chartMeta, fillType, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyDatalabelsBorderFillType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_COLOR_TYPE)) { return; }

        FillType fillType = FillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.BORDER_COLOR_TYPE));
        int seriesIndex = getSeriesIndex(valueJSON);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateDataPropertyDatalabelBorderFillType(chartMeta, fillType, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyDataLabelsFontStyle(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        Font sourceFont = new Font(new ColorThemeFontJSONConstants());

        sourceFont.getFromJSONObject(valueJSON);
        sourceFont.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));

        FrameworkChartAPI.updateDataPropertyDataLabelsFont(chartMeta, sourceFont, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyDataLabelFontColor(dataHolder);
    }

    private static void updateDataPropertyDataLabelsBgColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BG_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);

        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.BG_COLOR);

        FrameworkChartAPI.updateDataPropertyDataLabelsBgColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyDataLabelsBgColor(dataHolder);
    }

    private static void updateDataPropertyDataLabelsBorderColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);

        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR);

        FrameworkChartAPI.updateDataPropertyDataLabelsBorderColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyDataLabelsBorderColor(dataHolder);
    }

    private static void updateDataPropertyDataLabelsBorderWidth(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_WIDTH)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        Integer width = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.BORDER_WIDTH);

        FrameworkChartAPI.updateDataPropertyDataLabelsBorderWidth(chartMeta, width, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyDataLabelsBorderRadius(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BORDER_RADIUS)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        Integer width = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.BORDER_RADIUS);

        FrameworkChartAPI.updateDataPropertyDataLabelsBorderRadius(chartMeta, width, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyDataLabelsBgOpacity(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        Double opacity = ChartUtils.optDoubleFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateDataPropertyDataLabelsBgColorOpacity(chartMeta, opacity, seriesIndex, dataPropsIndex);
    }

    static void updateXAxisNumberFormat(SubActionDataHolder dataHolder) {
        updateXAxisNumberFormatPrefix(dataHolder);
        updateXAxisNumberFormatSuffix(dataHolder);
    }

    private static void updateXAxisNumberFormatSuffix(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SUFFIX)) { return; }

        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        String suffix = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SUFFIX);

        ReferenceActionHandler.updateXAxisSuffixValue(dataHolder, suffix, xAxisIndex);
    }

    private static void updateXAxisNumberFormatPrefix(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.PREFIX)) { return; }

        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        String prefix = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.PREFIX);

        ReferenceActionHandler.updateXAxisPrefixValue(dataHolder, prefix, xAxisIndex);
    }

    static void updateXAxisMajorTickLineStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateXAxisMajorTickStatus(chartMeta, status, xAxisIndex);
    }

    static void updateXAxisMajorTickLineStyle(SubActionDataHolder dataHolder) {
        updateXAxisMajorTickColor(dataHolder);
        updateXAxisMajorTickLength(dataHolder);
        updateXAxisMajorTickWidth(dataHolder);
        updateXAxisMajorTickPosition(dataHolder);
    }

    private static void updateXAxisMajorTickColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        String tickColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);
        FrameworkChartAPI.updateXAxisMajorTickColor(chartMeta, tickColor, xAxisIndex);
        CustomPropertiesActionHandler.updateXAxisMajorTickColor(dataHolder);
    }

    private static void updateXAxisMajorTickLength(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_LENGTH)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        int tickLength = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_LENGTH);

        FrameworkChartAPI.updateXAxisMajorTickLength(chartMeta, tickLength, xAxisIndex);
    }

    private static void updateXAxisMajorTickWidth(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        int tickWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updateXAxisMajorTickWidth(chartMeta, tickWidth, xAxisIndex);
    }

    private static void updateXAxisMajorTickPosition(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_POSITION)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        TickPosition tickPosition = TickPosition.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_POSITION));

        FrameworkChartAPI.updateXAxisMajorTickStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE, xAxisIndex);
        FrameworkChartAPI.updateXAxisMajorTickPosition(chartMeta, tickPosition, xAxisIndex);
    }

    static void updateXAxisMinorTickLineStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateXAxisMinorTickStatus(chartMeta, status, xAxisIndex);
    }

    static void updateXAxisMinorTickLineStyle(SubActionDataHolder dataHolder) {
        updateXAxisMinorTickColor(dataHolder);
        updateXAxisMinorTickLength(dataHolder);
        updateXAxisMinorTickWidth(dataHolder);
        updateXAxisMinorTickPosition(dataHolder);
    }

    private static void updateXAxisMinorTickColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String lineColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        FrameworkChartAPI.updateXAxisMinorTickColor(chartMeta, lineColor, xAxisIndex);
        CustomPropertiesActionHandler.updateXAxisMinorTickColor(dataHolder);
    }

    private static void updateXAxisMinorTickLength(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_LENGTH)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int lineLength = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_LENGTH);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        FrameworkChartAPI.updateXAxisMinorTickLength(chartMeta, lineLength, xAxisIndex);
    }

    private static void updateXAxisMinorTickWidth(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        FrameworkChartAPI.updateXAxisMinorTickWidth(chartMeta, lineWidth, xAxisIndex);
    }

    private static void updateXAxisMinorTickPosition(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_POSITION)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        TickPosition position = TickPosition.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_POSITION));
        int xAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.X_AXIS_INDEX, FrameworkChartActionConstants.Constants.X_AXIS_FIRST_INDEX);

        FrameworkChartAPI.updateXAxisMinorTickStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE, xAxisIndex);
        FrameworkChartAPI.updateXAxisMinorTickPosition(chartMeta, position, xAxisIndex);
    }

    static void updateYAxisMajorTickLineStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateYAxisMajorTickStatus(chartMeta, status, yAxisIndex);
    }

    static void updateYAxisMajorTickLineStyle(SubActionDataHolder dataHolder) {
        updateYAxisMajorTickColor(dataHolder);
        updateYAxisMajorTickLength(dataHolder);
        updateYAxisMajorTickWidth(dataHolder);
        updateYAxisMajorTickPosition(dataHolder);
    }

    private static void updateYAxisMajorTickColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        String tickColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);

        FrameworkChartAPI.updateYAxisMajorTickColor(chartMeta, tickColor, yAxisIndex);
        CustomPropertiesActionHandler.updateYAxisMajorTickColor(dataHolder);
    }

    private static void updateYAxisMajorTickLength(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_LENGTH)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        int lineLength = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_LENGTH);

        FrameworkChartAPI.updateYAxisMajorTickLength(chartMeta, lineLength, yAxisIndex);
    }

    private static void updateYAxisMajorTickWidth(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updateYAxisMajorTickWidth(chartMeta, lineWidth, yAxisIndex);
    }

    private static void updateYAxisMajorTickPosition(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_POSITION)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        TickPosition position = TickPosition.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_POSITION));

        FrameworkChartAPI.updateYAxisMajorTickStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE, yAxisIndex);
        FrameworkChartAPI.updateYAxisMajorTickPosition(chartMeta, position, yAxisIndex);
    }

    static void updateYAxisMinorTickLineStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateYAxisMinorTickStatus(chartMeta, status, yAxisIndex);
    }

    static void updateYAxisMinorTickLineStyle(SubActionDataHolder dataHolder) {
        updateYAxisMinorTickColor(dataHolder);
        updateYAxisMinorTickLength(dataHolder);
        updateYAxisMinorTickWidth(dataHolder);
        updateYAxisMinorTickPosition(dataHolder);
    }

    private static void updateYAxisMinorTickColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        String lineColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);

        FrameworkChartAPI.updateYAxisMinorTickColor(chartMeta, lineColor, yAxisIndex);
        CustomPropertiesActionHandler.updateYAxisMinorTickColor(dataHolder);
    }

    private static void updateYAxisMinorTickLength(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_LENGTH)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        int lineLength = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_LENGTH);

        FrameworkChartAPI.updateYAxisMinorTickLength(chartMeta, lineLength, yAxisIndex);
    }

    private static void updateYAxisMinorTickWidth(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updateYAxisMinorTickWidth(chartMeta, lineWidth, yAxisIndex);
    }

    private static void updateYAxisMinorTickPosition(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.LINE_POSITION)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        TickPosition position = TickPosition.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.LINE_POSITION));

        FrameworkChartAPI.updateYAxisMinorTickStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE, yAxisIndex);
        FrameworkChartAPI.updateYAxisMinorTickPosition(chartMeta, position, yAxisIndex);
    }

    static void updateSeriesInnerRadius(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = getSeriesIndex(valueJSON);
        String size = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SIZE);

        FrameworkChartAPI.updateSeriesInnerRadius(chartMeta, size, seriesIndex);
    }

    static void updateSeriesAreaLineType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        AreaLineType lineType = AreaLineType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.AREA_LINE_TYPE));

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesAreaLineType(dataHolder);
            FrameworkChartAPI.updateSharedSeriesAreaLineType(chartMeta, lineType);
        } else {
            FrameworkChartAPI.updateSeriesAreaLineType(chartMeta, lineType, seriesIndex);
        }

    }

    static void updateSeriesShowInLegend(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateSeriesVisibilityInLegend(chartMeta, status, seriesIndex);
    }

    static void updateYAxisAffixesType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int yAxisIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX, FrameworkChartActionConstants.Constants.Y_AXIS_FIRST_INDEX);
        YAxisAffixesType type = YAxisAffixesType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

        FrameworkChartAPI.updateYAxisAffixesType(chartMeta, type, yAxisIndex);
    }

    static void updateTotalDatalabelsType(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        TotalDatalabelsType type = TotalDatalabelsType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

        FrameworkChartAPI.updateTotalDatalabelsType(chartMeta, type);
    }

    static void updateTotalDatalabelsText(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        String reference = ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.TEXT);

        ReferenceActionHandler.updateTotalDatalabelsText(dataHolder, reference);
    }

    static void updateImageSeriesStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesImageSeriesStatus(dataHolder);
            FrameworkChartAPI.updateSharedSeriesImageSeriesStatus(chartMeta, status);
        } else {
            FrameworkChartAPI.updateSeriesImageSeriesStatus(chartMeta, status, seriesIndex);
        }
    }

    static void updateImageCaptionStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateSeriesImageCaptionStatus(chartMeta, status, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    static void updateImagePosition(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        ImagePositionType positionType = ImagePositionType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.POSITION));

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesImageSeriesPosition(dataHolder);
            FrameworkChartAPI.updateSharedSeriesImageSeriesPosition(chartMeta, positionType);
        } else {
            FrameworkChartAPI.updateSeriesImageSeriesPosition(chartMeta, positionType, seriesIndex);
        }
    }

    static void updateImageSeriesShape(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        ClipShapeType shapeType = ClipShapeType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SHAPE));

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesImageSeriesClipShape(dataHolder);
            SeriesPropsActionHandler.updateSharedSeriesImageSeriesStatus(dataHolder);
            FrameworkChartAPI.updateSharedSeriesImageSeriesClipShape(chartMeta, shapeType);
            FrameworkChartAPI.updateSharedSeriesImageSeriesStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE);
        } else {
            FrameworkChartAPI.updateSeriesImageSeriesClipShape(chartMeta, shapeType, seriesIndex);
            FrameworkChartAPI.updateSeriesImageSeriesStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE, seriesIndex);
        }
    }

    static void updateImageCaptionShape(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ClipShapeType shapeType = ClipShapeType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SHAPE));

        FrameworkChartAPI.updateSeriesImageCaptionClipShape(chartMeta, shapeType, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        FrameworkChartAPI.updateSeriesImageCaptionStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    static void updateSeriesShadowStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesShadowStatus(dataHolder);
            FrameworkChartAPI.updateSharedSeriesShadowStatus(chartMeta, status);
        } else {
            SeriesPropsActionHandler.updateSeriesShadowStatus(dataHolder);
            FrameworkChartAPI.updateSeriesShadowStatus(chartMeta, status, seriesIndex);
        }
    }

    static void updateSeriesShadowStyles(SubActionDataHolder dataHolder) {
        updateSeriesShadowColor(dataHolder);
        updateSeriesShadowColorOpacity(dataHolder);
        updateSeriesShadowType(dataHolder);
        updateSeriesShadowBlur(dataHolder);
        updateSeriesShadowSpread(dataHolder);
        updateSeriesShadowDistance(dataHolder);
        updateSeriesShadowDegree(dataHolder);
    }

    private static void updateSeriesShadowColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.COLOR)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.COLOR);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesShadowColor(dataHolder);
            FrameworkChartAPI.updateSharedSeriesShadowColor(chartMeta, color);
            CustomPropertiesActionHandler.updateSharedSeriesShadowColor(dataHolder);
        } else {
            SeriesPropsActionHandler.updateSeriesShadowColor(dataHolder);
            FrameworkChartAPI.updateSeriesShadowColor(chartMeta, color, seriesIndex);
            CustomPropertiesActionHandler.updateSeriesShadowColor(dataHolder);
        }

    }

    private static void updateSeriesShadowColorOpacity(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesShadowColorOpacity(dataHolder);
            FrameworkChartAPI.updateSharedSeriesShadowColorOpacity(chartMeta, opacity);
        } else {
            SeriesPropsActionHandler.updateSeriesShadowColorOpacity(dataHolder);
            FrameworkChartAPI.updateSeriesShadowColorOpacity(chartMeta, opacity, seriesIndex);
        }
    }

    private static void updateSeriesShadowType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TYPE)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        ShadowType type = ShadowType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesShadowType(dataHolder);
            FrameworkChartAPI.updateSharedSeriesShadowType(chartMeta, type);
        } else {
            SeriesPropsActionHandler.updateSeriesShadowType(dataHolder);
            FrameworkChartAPI.updateSeriesShadowType(chartMeta, type, seriesIndex);
        }

    }

    private static void updateSeriesShadowBlur(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BLUR)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer blur = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.BLUR);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesShadowBlur(dataHolder);
            FrameworkChartAPI.updateSharedSeriesShadowBlur(chartMeta, blur);
        } else {
            SeriesPropsActionHandler.updateSeriesShadowBlur(dataHolder);
            FrameworkChartAPI.updateSeriesShadowBlur(chartMeta, blur, seriesIndex);
        }

    }

    private static void updateSeriesShadowSpread(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SPREAD)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer spread = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SPREAD);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesShadowSpread(dataHolder);
            FrameworkChartAPI.updateSharedSeriesShadowSpread(chartMeta, spread);
        } else {
            SeriesPropsActionHandler.updateSeriesShadowSpread(dataHolder);
            FrameworkChartAPI.updateSeriesShadowSpread(chartMeta, spread, seriesIndex);
        }

    }


    private static void updateSeriesShadowDistance(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.DISTANCE)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int distance = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DISTANCE);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesShadowDistance(dataHolder);
            FrameworkChartAPI.updateSharedSeriesShadowDistance(chartMeta, distance);
        } else {
            SeriesPropsActionHandler.updateSeriesShadowDistance(dataHolder);
            FrameworkChartAPI.updateSeriesShadowDistance(chartMeta, distance, seriesIndex);
        }

    }

    private static void updateSeriesShadowDegree(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.DEGREE)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int degree = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DEGREE);
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesShadowDegree(dataHolder);
            FrameworkChartAPI.updateSharedSeriesShadowDegree(chartMeta, degree);
        } else {
            SeriesPropsActionHandler.updateSeriesShadowDegree(dataHolder);
            FrameworkChartAPI.updateSeriesShadowDegree(chartMeta, degree, seriesIndex);
        }

    }

    static void updateSliderLabelsStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateSeriesSliderLabelsStatus(chartMeta, status, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    static void updateSliderLabelsStyles(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Font sourceFont = new Font(new ColorThemeFontJSONConstants());

        sourceFont.getFromJSONObject(valueJSON);
        sourceFont.setFontColor(getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR));

        FrameworkChartAPI.updateSeriesSliderLabelsFont(chartMeta, sourceFont, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        CustomPropertiesActionHandler.updateSeriesSliderLabelsFontColor(dataHolder);
    }

    static void updateDataPropertyMarkerStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        Boolean status = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateDataPropertyMarkerStatus(chartMeta, status, seriesIndex, dataPropsIndex);
    }

    static void updateDataPropertyOutliersColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);

        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.OUTLIERS_COLOR);

        FrameworkChartAPI.updateDataPropertyOutliersColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyOutliersColor(dataHolder);
    }

    static void updateDataPropertyMeanColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.MEAN_COLOR);

        FrameworkChartAPI.updateDataPropertyMeanColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyMeanColor(dataHolder);
    }

    static void updateDataPropertyMedianColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.MEDIAN_COLOR);

        FrameworkChartAPI.updateDataPropertyMedianColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyMedianColor(dataHolder);
    }

    static void updateDataPropertyWhiskersColor(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.WHISKERS_COLOR);

        FrameworkChartAPI.updateDataPropertyWhiskersColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyWhiskersColor(dataHolder);
    }

    static void updateDataPropertyShadowStatus(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        Boolean status = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateDataPropertyShadowStatus(chartMeta, status, seriesIndex, dataPropsIndex);
    }

    static void updateDataPropertyShadowStyles(SubActionDataHolder dataHolder) {
        updateDataPropertyShadowColor(dataHolder);
        updateDataPropertyShadowOpacity(dataHolder);
        updateDataPropertyShadowDistance(dataHolder);
        updateDataPropertyShadowDegree(dataHolder);
        updateDataPropertyShadowSpread(dataHolder);
        updateDataPropertyShadowType(dataHolder);
        updateDataPropertyShadowBlur(dataHolder);
    }

    private static void updateDataPropertyShadowType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TYPE)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        ShadowType shadowType = ShadowType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

        FrameworkChartAPI.updateDataPropertyShadowType(chartMeta, shadowType, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyShadowBlur(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BLUR)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        int blur = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BLUR);

        FrameworkChartAPI.updateDataPropertyShadowBlur(chartMeta, blur, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyShadowDistance(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.DISTANCE)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        int distance = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DISTANCE);

        FrameworkChartAPI.updateDataPropertyShadowDistance(chartMeta, distance, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyShadowDegree(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.DEGREE)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        int degree = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DEGREE);

        FrameworkChartAPI.updateDataPropertyShadowDegree(chartMeta, degree, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyShadowSpread(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SPREAD)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        int spread = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SPREAD);

        FrameworkChartAPI.updateDataPropertyShadowSpread(chartMeta, spread, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyShadowColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.COLOR)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);

        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.COLOR);

        FrameworkChartAPI.updateDataPropertyShadowColor(chartMeta, color, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyShadowColor(dataHolder);
    }

    private static void updateDataPropertyShadowOpacity(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateDataPropertyShadowOpacity(chartMeta, opacity, seriesIndex, dataPropsIndex);
    }

    static void updateSeriesSliderTickCount(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int tickCount = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.COUNT);

        FrameworkChartAPI.updateSeriesSliderTicksCount(chartMeta, tickCount, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
    }

    static void updateSeriesDataLabelsBgShape(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        DataLabelsBgShapeType dataLabelsBgShapeType = DataLabelsBgShapeType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SHAPE));

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsShape(dataHolder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsBgShape(chartMeta, dataLabelsBgShapeType);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsShape(dataHolder);
            FrameworkChartAPI.updateSeriesDataLabelsBgShape(chartMeta, dataLabelsBgShapeType, seriesIndex);
        }
    }

    static void updateSeriesDataLabelsCoordinates(SubActionDataHolder dataHolder) {
        updateSeriesDatalabelsXCoordinates(dataHolder);
        updateSeriesDatalabelsYCoordinates(dataHolder);
    }

    private static void updateSeriesDatalabelsXCoordinates(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!(valueJSON.has(FrameworkChartActionConstants.JSONConstants.X))) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsX(dataHolder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsXCoordinate(chartMeta, valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.X));
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsX(dataHolder);
            FrameworkChartAPI.updateSeriesDataLabelsXCoordinate(chartMeta, valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.X), seriesIndex);
        }
    }

    private static void updateSeriesDatalabelsYCoordinates(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!(valueJSON.has(FrameworkChartActionConstants.JSONConstants.Y))) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsY(dataHolder);
            FrameworkChartAPI.updateSharedSeriesDataLabelsYCoordinate(chartMeta, valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y));
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsY(dataHolder);
            FrameworkChartAPI.updateSeriesDataLabelsYCoordinate(chartMeta, valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y), seriesIndex);
        }
    }


    static void updateDataPropertyDataLabelsBgShape(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        DataLabelsBgShapeType dataLabelsBgShapeType = DataLabelsBgShapeType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SHAPE));

        FrameworkChartAPI.updateDataPropertyDataLabelsBgShape(chartMeta, dataLabelsBgShapeType, seriesIndex, dataPropsIndex);
    }

    static void updateDataPropertyDataLabelsCoordinates(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);

        if(valueJSON.has(FrameworkChartActionConstants.JSONConstants.X)) {
            FrameworkChartAPI.updateDataPropertyDataLabelsXCoordinate(chartMeta, valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.X), seriesIndex, dataPropsIndex);
        }
        if(valueJSON.has(FrameworkChartActionConstants.JSONConstants.Y)) {
            FrameworkChartAPI.updateDataPropertyDataLabelsYCoordinate(chartMeta, valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y), seriesIndex, dataPropsIndex);
        }
    }

    static void updateSeriesRelationType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        SeriesRelationType seriesRelationType = SeriesRelationType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.RELATION));

        FrameworkChartAPI.updateSeriesRelation(chartMeta, seriesRelationType);
    }

    static void updateChartAreaFillType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        ColorFillType areaFillType = ColorFillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FILL));

        if(areaFillType == ColorFillType.GRADIENT) {
            GradientFunctionType functionType = GradientFunctionType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

            FrameworkChartAPI.updateChartAreaGradientFunctionType(chartMeta, functionType);
        }

        FrameworkChartAPI.updateChartAreaFillType(chartMeta, areaFillType);
    }

    static void updateChartAreaGradientDegree(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int degree = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DEGREE);

        FrameworkChartAPI.updateChartAreaGradientDegree(chartMeta, degree);
    }

    static void updateChartAreaGradientStops(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Object stops = valueJSON.get(FrameworkChartActionConstants.JSONConstants.STOPS);

        FrameworkChartAPI.updateChartAreaGradientStops(chartMeta, Objects.toString(stops));
        CustomPropertiesActionHandler.updateChartAreaGradientStops(dataHolder);
    }

    static void updateChartMargin(SubActionDataHolder dataHolder) {
        updateChartMarginLeft(dataHolder);
        updateChartMarginRight(dataHolder);
        updateChartMarginBottom(dataHolder);
        updateChartMarginTop(dataHolder);
        clearChartMarginLeft(dataHolder);
        clearChartMarginRight(dataHolder);
        clearChartMarginTop(dataHolder);
        clearChartMarginBottom(dataHolder);
    }

    private static void clearChartMarginLeft(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.CLEAR_LEFT_MARGIN)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateChartMarginLeft(chartMeta, null);
    }
    private static void clearChartMarginRight(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.CLEAR_RIGHT_MARGIN)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateChartMarginRight(chartMeta, null);
    }
    private static void clearChartMarginTop(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.CLEAR_TOP_MARGIN)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateChartMarginTop(chartMeta, null);
    }
    private static void clearChartMarginBottom(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.CLEAR_BOTTOM_MARGIN)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateChartMarginBottom(chartMeta, null);
    }

    private static void updateChartMarginLeft(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARGIN_LEFT)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer marginLeft = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.MARGIN_LEFT);

        FrameworkChartAPI.updateChartMarginLeft(chartMeta, marginLeft);
    }

    private static void updateChartMarginRight(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARGIN_RIGHT)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer marginRight = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.MARGIN_RIGHT);

        FrameworkChartAPI.updateChartMarginRight(chartMeta, marginRight);
    }

    private static void updateChartMarginTop(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARGIN_TOP)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer marginTop = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.MARGIN_TOP);

        FrameworkChartAPI.updateChartMarginTop(chartMeta, marginTop);
    }

    private static void updateChartMarginBottom(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MARGIN_BOTTOM)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer marginBottom = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.MARGIN_BOTTOM);

        FrameworkChartAPI.updateChartMarginBottom(chartMeta, marginBottom);
    }

    static void updateTotalLabelStatus(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateTotalLabelStatus(chartMeta, status);
    }

    static void updateTotalLabelFontStyles(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Font font = new Font(new ColorThemeFontJSONConstants());
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);

        font.getFromJSONObject(valueJSON);
        font.setFontColor(color);

        if(color != null) { CustomPropertiesActionHandler.updateTotalLabelFontColor(dataHolder);}
        FrameworkChartAPI.updateTotalLabelFont(chartMeta, font);
    }

    static void updateChartAreaShadowMode(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        boolean enabled = ChartUtils.optFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.IS_ENABLED, FrameworkChartActionConstants.Constants.ENABLE);

        if(enabled) {
            ShadowType shadowType = ShadowType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

            FrameworkChartAPI.updateChartAreaShadowType(chartMeta, shadowType);
            FrameworkChartAPI.updateChartAreaShadowStatus(chartMeta, FrameworkChartActionConstants.Constants.ENABLE);
        } else {
            FrameworkChartAPI.updateChartAreaShadowStatus(chartMeta, FrameworkChartActionConstants.Constants.DISABLE);
        }
    }

    static void updateChartAreaShadowStyles(SubActionDataHolder dataHolder) {
        updateChartAreaShadowColor(dataHolder);
        updateChartAreaShadowDegree(dataHolder);
        updateChartAreaShadowDistance(dataHolder);
        updateChartAreaShadowBlur(dataHolder);
        updateChartAreaShadowSpread(dataHolder);
        updateChartAreaShadowOpacity(dataHolder);
    }

    private static void updateChartAreaShadowColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.COLOR);

        FrameworkChartAPI.updateChartAreaShadowColor(chartMeta, color);
        CustomPropertiesActionHandler.updateChartAreaShadowColor(dataHolder);
    }

    private static void updateChartAreaShadowDegree(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.DEGREE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int degree = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DEGREE);

        FrameworkChartAPI.updateChartAreaShadowDegree(chartMeta, degree);
    }

    private static void updateChartAreaShadowDistance(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.DISTANCE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int distance = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DISTANCE);

        FrameworkChartAPI.updateChartAreaShadowDistance(chartMeta, distance);
    }

    private static void updateChartAreaShadowBlur(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.BLUR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int blur = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BLUR);

        FrameworkChartAPI.updateChartAreaShadowBlur(chartMeta, blur);
    }

    private static void updateChartAreaShadowSpread(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SPREAD)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int spread = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SPREAD);

        FrameworkChartAPI.updateChartAreaShadowSpread(chartMeta, spread);
    }

    private static void updateChartAreaShadowOpacity(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();

        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateChartAreaShadowOpacity(chartMeta, opacity);
    }

    static void updateSeriesGradientType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        ColorFillType colorFillType = ColorFillType.retrieveByValue(
                valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FILL)
        );

        if(colorFillType == ColorFillType.GRADIENT) {
            GradientFunctionType functionType = GradientFunctionType.retrieveByValue(
                    valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));
            if(seriesIndex == null) {
                SeriesPropsActionHandler.updateSharedSeriesGradientType(dataHolder);
                FrameworkChartAPI.updateSharedSeriesGradientFunctionType(chartMeta, functionType);
            } else {
                SeriesPropsActionHandler.updateSeriesGradientType(dataHolder);
                FrameworkChartAPI.updateSeriesGradientFunctionType(chartMeta, functionType, seriesIndex);
            }
        }

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesColorFillType(dataHolder);
            FrameworkChartAPI.updateSharedSeriesColorFillType(chartMeta, colorFillType);
        } else {
            SeriesPropsActionHandler.updateSeriesColorFillType(dataHolder);
            FrameworkChartAPI.updateSeriesColorFillType(chartMeta, colorFillType, seriesIndex);
        }

    }

    static void updateSeriesGradientDegree(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        int degree = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DEGREE);
        Integer seriesIndex = ChartUtils.optIntegerFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesGradientDegree(dataHolder);
            FrameworkChartAPI.updateSharedSeriesGradientDegree(chartMeta, degree);
        } else {
            SeriesPropsActionHandler.updateSeriesGradientDegree(dataHolder);
            FrameworkChartAPI.updateSeriesGradientDegree(chartMeta, degree, seriesIndex);
        }

    }

    static void updateSeriesGradientStops(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        String stops = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.STOPS);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);

        SeriesPropsActionHandler.updateSeriesGradientStops(dataHolder);
        FrameworkChartAPI.updateSeriesGradientStops(chartMeta, stops, seriesIndex);
        CustomPropertiesActionHandler.updateSeriesColorGradientStops(dataHolder);
    }

    static void updateDataPropertyGradientType(SubActionDataHolder dataHolder) {
        updateDataPropertyColorFillType(dataHolder);
        updateDataPropertyGradientFnType(dataHolder);
    }

    private static void updateDataPropertyColorFillType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FILL)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        ColorFillType colorFillType = ColorFillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FILL));

        FrameworkChartAPI.updateDataPropertyColorFillType(chartMeta, colorFillType, seriesIndex, dataPropsIndex);
    }

    private static void updateDataPropertyGradientFnType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TYPE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        GradientFunctionType functionType = GradientFunctionType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

        FrameworkChartAPI.updateDataPropertyGradientFunctionType(chartMeta, functionType, seriesIndex, dataPropsIndex);
    }

    static void updateDataPropertyGradientDegree(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        int degree = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DEGREE);

        FrameworkChartAPI.updateDataPropertyGradientDegree(chartMeta, degree, seriesIndex, dataPropsIndex);
    }

    static void updateDataPropertyGradientStops(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        int seriesIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.SERIES_INDEX);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        String stops = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.STOPS);

        FrameworkChartAPI.updateDataPropertyGradientStops(chartMeta, stops, seriesIndex, dataPropsIndex);
        CustomPropertiesActionHandler.updateDataPropertyColorGradientStops(dataHolder);
    }

    static void updateYAxisBaselineWidth(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        int lineWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.LINE_WIDTH);

        FrameworkChartAPI.updateYAxisBaseLineWidth(chartMeta, lineWidth, yAxisIndex);
    }

    static void updateYAxisBaselineColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        int yAxisIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.Y_AXIS_INDEX);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.LINE_COLOR);

        FrameworkChartAPI.updateYAxisBaseLineColor(chartMeta, color, yAxisIndex);
        CustomPropertiesActionHandler.updateYAxisBaseLineColor(dataHolder);
    }

    static void updateChartBorderWidth(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int borderWidth = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BORDER_WIDTH);

        FrameworkChartAPI.updateChartBorderWidth(chartMeta, borderWidth);
    }

    static void updateChartBorderStyle(SubActionDataHolder dataHolder) {
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        BorderStyleType borderStyleType = BorderStyleType.retrieveByValue(
                valueJSON.getString(FrameworkChartActionConstants.JSONConstants.BORDER_STYLE_TYPE)
        );

        FrameworkChartAPI.updateChartBorderStyle(chartMeta, borderStyleType);
    }

    static void updateChartBorderOpacity(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateChartBorderColorOpacity(chartMeta, opacity);
    }

    static void updateChartBorderColor(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String borderColor = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.BORDER_COLOR);

        FrameworkChartAPI.updateChartBorderColor(chartMeta, borderColor);
        CustomPropertiesActionHandler.updateChartBorderColorThemeOptions(dataHolder, SheetChartActionConstants.JSONConstants.BORDER_COLOR);
    }

    static void updateChartBorderRadius(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Integer radius = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.BORDER_RADIUS);

        FrameworkChartAPI.updateChartBorderRadius(chartMeta, radius);
    }

    static void updateNumberFormat(SubActionDataHolder dataHolder){
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        NumberFormatType type = NumberFormatType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

        FrameworkChartAPI.updateNumberFormatType(chartMeta, type);
    }

    static void applyChartAreaPreset(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        String presetName = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PRESET);
        PresetParams params = new PresetParams();

        params.setParam(PresetParams.Name.PRESET_NAME, presetName);
        params.setParam(PresetParams.Name.CUSTOM_STOPS, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS));
        params.setParam(PresetParams.Name.CUSTOM_STOPS_THEME, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS_THEME));
        DelegatingPreset preset = ObjectsSupplier.getInstance().getPresetStyles(Purpose.PresetStylesPurpose.CHART_AREA_BACKGROUND, params);
        preset.applyPreset(dataHolder.getActiveSheet().getWorkbook(), chart);
    }

    static void updateChartAreaNavigatorEnabledStatus(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        StatusType type = StatusType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.STATUS));

        FrameworkChartAPI.updateChartAreaNavigatorEnable(chartMeta, type);
    }

    static void updateChartAreaNavigatorScrollBarEnabledStatus(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        StatusType type = StatusType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.STATUS));

        FrameworkChartAPI.updateChartAreaNavigatorScrollBarEnabled(chartMeta, type);
    }

    static void updateChartAreaRangeSelectorEnabledStatus(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        StatusType type = StatusType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.STATUS));

        FrameworkChartAPI.updateChartAreaRangeSelectorStatus(chartMeta, type);
    }

    static void updateChartAreaShadowPreset(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        String presetName = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PRESET);
        PresetParams params = new PresetParams();

        params.setParam(PresetParams.Name.PRESET_NAME, presetName);
        DelegatingPreset preset = ObjectsSupplier.getInstance().getPresetStyles(Purpose.PresetStylesPurpose.CHART_AREA_SHADOW, params);
        preset.applyPreset(dataHolder.getActiveSheet().getWorkbook(), chart);
    }

    static void updateLegendTitle(SubActionDataHolder dataHolder) {
        updateLegendTitleStatus(dataHolder);
        updateLegendTitleText(dataHolder);
    }

    private static void updateLegendTitleStatus(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.IS_ENABLED)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        boolean enabled = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);

        FrameworkChartAPI.updateLegendTitleStatus(chartMeta, enabled);
    }

    private static void updateLegendTitleText(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TEXT)) { return; }

        String text = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TEXT);
        ReferenceActionHandler.updateLegendTitle(dataHolder, text);
    }

    static void updateLegendTitleFont(SubActionDataHolder dataHolder) {
        updateLegendTitleFontColor(dataHolder);
        updateLegendTitleFontSize(dataHolder);
        updateLegendTitleFontStyle(dataHolder);
        updateLegendTitleFontWeight(dataHolder);
    }

    private static void updateLegendTitleFontColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);

        FrameworkChartAPI.updateLegendTitleFontColor(chartMeta, color);
        CustomPropertiesActionHandler.updateLegendTitleFontColor(dataHolder);
    }

    private static void updateLegendTitleFontSize(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String size = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);

        FrameworkChartAPI.updateLegendTitleFontSize(chartMeta, size);
    }

    private static void updateLegendTitleFontStyle(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String style = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);

        FrameworkChartAPI.updateLegendTitleFontStyle(chartMeta, style);
    }

    private static void updateLegendTitleFontWeight(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String weight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);

        FrameworkChartAPI.updateLegendTitleFontWeight(chartMeta, weight);
    }

    static void updateChartBorderType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        FillType type = FillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateChartBorderFillType(chartMeta, type);
    }

    static void updateSeriesDatalabelsCustomValue(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = optSeriesIndex(valueJSON);
        String reference = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TEXT);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsCustomValue(dataHolder);
            ReferenceActionHandler.updateSharedSeriesDatalabelsCustomValue(dataHolder, reference);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsCustomValue(dataHolder);
            ReferenceActionHandler.updateSeriesDatalabelsCustomValue(dataHolder, reference, seriesIndex);
        }
    }

    static void updateDataPropertyDatalabelsCustomValue(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = getSeriesIndex(valueJSON);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        String reference = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TEXT);

        ReferenceActionHandler.updateDataPropertyDatalabelsCustomValue(dataHolder, reference, seriesIndex, dataPropsIndex);
    }

    static void updateSeriesDatalabelsSeparator(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = optSeriesIndex(valueJSON);

        DatalabelSeparatorType separatorType = DatalabelSeparatorType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SEPARATOR));
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesDatalabelsSeparator(dataHolder);
            FrameworkChartAPI.updateSharedSeriesDatalabelsSeparator(chartMeta, separatorType);
        } else {
            SeriesPropsActionHandler.updateSeriesDatalabelsSeparator(dataHolder);
            FrameworkChartAPI.updateSeriesDatalabelsSeparator(chartMeta, separatorType, seriesIndex);
        }
    }

    static void updateDataPropertyDatalabelsSeparator(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = getSeriesIndex(valueJSON);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);

        DatalabelSeparatorType separatorType = DatalabelSeparatorType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SEPARATOR));
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateDataPropertyDatalabelsSeparator(chartMeta, separatorType, seriesIndex, dataPropsIndex);
    }

    static void updateSeriesUpColorType(SubActionDataHolder dataHolder) {
        updateSeriesUpColorFillType(dataHolder);
        updateSeriesUpColorGradientType(dataHolder);
    }

    private static void updateSeriesUpColorFillType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FILL)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        ColorFillType colorFillType = ColorFillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FILL));

        FrameworkChartAPI.updateUpColorFillType(chartMeta, colorFillType, seriesIndex);
    }

    private static void updateSeriesUpColorGradientType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TYPE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        GradientFunctionType fnType = GradientFunctionType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

        FrameworkChartAPI.updateUpColorGradientFnType(chartMeta, fnType, seriesIndex);
    }

    static void updateSeriesDownColorType(SubActionDataHolder dataHolder) {
        updateSeriesDownColorFillType(dataHolder);
        updateSeriesDownColorGradientType(dataHolder);
    }

    private static void updateSeriesDownColorFillType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FILL)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        ColorFillType colorFillType = ColorFillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FILL));

        FrameworkChartAPI.updateDownColorFillType(chartMeta, colorFillType, seriesIndex);
    }

    private static void updateSeriesDownColorGradientType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TYPE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        GradientFunctionType fnType = GradientFunctionType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

        FrameworkChartAPI.updateDownColorGradientFnType(chartMeta, fnType, seriesIndex);
    }

    static void updateSeriesTotalColorType(SubActionDataHolder dataHolder) {
        updateSeriesTotalColorFillType(dataHolder);
        updateSeriesTotalColorGradientType(dataHolder);
    }

    private static void updateSeriesTotalColorFillType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FILL)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        ColorFillType colorFillType = ColorFillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FILL));

        FrameworkChartAPI.updateSeriesTotalColorFillType(chartMeta, colorFillType, seriesIndex);
    }

    private static void updateSeriesTotalColorGradientType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TYPE)) { return; }

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);
        GradientFunctionType fnType = GradientFunctionType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));

        FrameworkChartAPI.updateSeriesTotalColorGradientFnType(chartMeta, fnType, seriesIndex);
    }

    static void updateSeriesUpColorGradientDegree(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int degree = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DEGREE);
        FrameworkChartAPI.updateUpColorGradientDegree(chartMeta, degree, seriesIndex);
    }

    static void updateSeriesDownColorGradientDegree(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int degree = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DEGREE);
        FrameworkChartAPI.updateDownColorGradientDegree(chartMeta, degree, seriesIndex);
    }

    static void updateSeriesTotalColorGradientDegree(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        int degree = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DEGREE);
        FrameworkChartAPI.updateSeriesTotalColorGradientDegree(chartMeta, degree, seriesIndex);
    }

    static void updateSeriesUpColorGradientStops(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String stops = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.STOPS);

        FrameworkChartAPI.updateUpColorGradientStops(chartMeta, stops, seriesIndex);
        CustomPropertiesActionHandler.updateSeriesUpColorGradientStops(dataHolder);
    }

    static void updateSeriesDownColorGradientStops(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String stops = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.STOPS);

        FrameworkChartAPI.updateDownColorGradientStops(chartMeta, stops, seriesIndex);
        CustomPropertiesActionHandler.updateSeriesDownColorGradientStops(dataHolder);
    }

    static void updateSeriesTotalColorGradientStops(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String stops = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.STOPS);

        FrameworkChartAPI.updateSeriesTotalColorGradientStops(chartMeta, stops, seriesIndex);
        CustomPropertiesActionHandler.updateSeriesTotalColorGradientStops(dataHolder);
    }

    static void updateSeriesTotalColorProps(SubActionDataHolder dataHolder) {
        updateSeriesTotalColor(dataHolder);
        updateSeriesTotalColorOpacity(dataHolder);
    }

    private static void updateSeriesTotalColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.COLOR)) { return; }
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.COLOR);

        CustomPropertiesActionHandler.updateSeriesTotalColor(dataHolder);
        FrameworkChartAPI.updateSeriesTotalColor(chartMeta, color, seriesIndex);
    }

    private static void updateSeriesTotalColorOpacity(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.OPACITY)) { return; }
        int seriesIndex = optSeriesIndex(valueJSON, FrameworkChartActionConstants.Constants.SERIES_FIRST_INDEX);

        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);
        Double opacity = valueJSON.getDouble(FrameworkChartActionConstants.JSONConstants.OPACITY);

        FrameworkChartAPI.updateSeriesTotalColorOpacity(chartMeta, opacity, seriesIndex);
    }

    static void updateSeriesGradientPreset(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = getSeriesIndex(valueJSON);
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        String presetName = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PRESET);
        PresetParams params = new PresetParams()
                                .setParam(PresetParams.Name.SERIES_INDEX, seriesIndex)
                                .setParam(PresetParams.Name.PRESET_NAME, presetName);

        params.setParam(PresetParams.Name.CUSTOM_STOPS, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS));
        params.setParam(PresetParams.Name.CUSTOM_STOPS_THEME, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS_THEME));

        DelegatingPreset preset = ObjectsSupplier.getInstance().getPresetStyles(Purpose.PresetStylesPurpose.SERIES_COLOR, params);

        SeriesPropsActionHandler.updateSeriesGradientPresets(dataHolder);
        preset.applyPreset(workbook, chart);
    }

    static void updateDataPropertyGradientPreset(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = getSeriesIndex(valueJSON);
        int dataPropsIndex = valueJSON.getInt(FrameworkChartActionConstants.JSONConstants.DATA_PROPERTY_INDEX);
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        String presetName = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PRESET);

        PresetParams params = new PresetParams()
                .setParam(PresetParams.Name.DATA_PROPERTY_INDEX, dataPropsIndex)
                .setParam(PresetParams.Name.SERIES_INDEX, seriesIndex)
                .setParam(PresetParams.Name.PRESET_NAME, presetName);

        params.setParam(PresetParams.Name.CUSTOM_STOPS, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS));
        params.setParam(PresetParams.Name.CUSTOM_STOPS_THEME, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS_THEME));

        DelegatingPreset preset = ObjectsSupplier.getInstance().getPresetStyles(Purpose.PresetStylesPurpose.DATA_PROPERTY_COLOR, params);

        preset.applyPreset(workbook, chart);
    }


    static void updateTrendlineFillType(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = optSeriesIndex(valueJSON);
        FillType fillType = FillType.retrieveByValue(valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TYPE));
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesTrendlineFillType(dataHolder);
            FrameworkChartAPI.updateSharedSeriesTrendlineFillType(chartMeta, fillType);
        } else {
            FrameworkChartAPI.updateSeriesTrendlineFillType(chartMeta, fillType, seriesIndex);
        }
    }

    static void updateSeriesUpColorGradientPreset(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = getSeriesIndex(valueJSON);
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        String presetName = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PRESET);

        PresetParams params = new PresetParams()
                .setParam(PresetParams.Name.SERIES_INDEX, seriesIndex)
                .setParam(PresetParams.Name.PRESET_NAME, presetName);

        params.setParam(PresetParams.Name.CUSTOM_STOPS, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS));
        params.setParam(PresetParams.Name.CUSTOM_STOPS_THEME, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS_THEME));

        DelegatingPreset preset = ObjectsSupplier.getInstance().getPresetStyles(Purpose.PresetStylesPurpose.SERIES_UP_COLOR, params);

        preset.applyPreset(workbook, chart);
    }

    static void updateSeriesDownColorGradientPreset(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = getSeriesIndex(valueJSON);
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        String presetName = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PRESET);

        PresetParams params = new PresetParams()
                .setParam(PresetParams.Name.SERIES_INDEX, seriesIndex)
                .setParam(PresetParams.Name.PRESET_NAME, presetName);

        params.setParam(PresetParams.Name.CUSTOM_STOPS, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS));
        params.setParam(PresetParams.Name.CUSTOM_STOPS_THEME, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS_THEME));

        DelegatingPreset preset = ObjectsSupplier.getInstance().getPresetStyles(Purpose.PresetStylesPurpose.SERIES_DOWN_COLOR, params);

        preset.applyPreset(workbook, chart);
    }

    static void updateSeriesTotalColorGradientPreset(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        int seriesIndex = getSeriesIndex(valueJSON);
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        String presetName = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PRESET);

        PresetParams params = new PresetParams()
                .setParam(PresetParams.Name.SERIES_INDEX, seriesIndex)
                .setParam(PresetParams.Name.PRESET_NAME, presetName);

        params.setParam(PresetParams.Name.CUSTOM_STOPS, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS));
        params.setParam(PresetParams.Name.CUSTOM_STOPS_THEME, ChartUtils.optStringFromJSONObject(valueJSON, FrameworkChartActionConstants.JSONConstants.STOPS_THEME));

        DelegatingPreset preset = ObjectsSupplier.getInstance().getPresetStyles(Purpose.PresetStylesPurpose.SERIES_TOTAL_COLOR, params);

        preset.applyPreset(workbook, chart);
    }

    static void updateCredits(SubActionDataHolder dataHolder) {
        updateCreditsText(dataHolder);
        updateCreditsStatus(dataHolder);
    }

    private static void updateCreditsText(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TEXT)) { return; }
        String text = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TEXT);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateCreditsText(chartMeta, text);
    }

    private static void updateCreditsStatus(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.IS_ENABLED)) { return; }
        boolean status = valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateCreditsStatus(chartMeta, status);
    }

    static void updateCreditsStyle(SubActionDataHolder dataHolder) {
        updateCreditsFontSize(dataHolder);
        updateCreditsFontStyle(dataHolder);
        updateCreditsFontWeight(dataHolder);
        updateCreditsFontColor(dataHolder);
    }

    private static void updateCreditsFontSize(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_SIZE)) { return; }
        String fontSize = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_SIZE);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateCreditsFontSize(chartMeta, fontSize);
    }

    private static void updateCreditsFontWeight(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT)) { return; }
        String fontWeight = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_WEIGHT);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateCreditsFontWeight(chartMeta, fontWeight);
    }

    private static void updateCreditsFontStyle(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_STYLE)) { return; }
        String fontStyle = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.FONT_STYLE);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateCreditsFontStyle(chartMeta, fontStyle);
    }

    private static void updateCreditsFontColor(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.FONT_COLOR)) { return; }
        String color = getColor(valueJSON, FrameworkChartActionConstants.JSONConstants.FONT_COLOR);
        ChartMeta chartMeta = ChartUtils.getChartMetaFromActionDataHolder(dataHolder);

        FrameworkChartAPI.updateCreditsFontColor(chartMeta, color);
        CustomPropertiesActionHandler.updateCreditsFontColor(dataHolder);
    }

    static void updateSeriesShadowPresets(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        Integer seriesIndex = optSeriesIndex(valueJSON);

        if(seriesIndex == null) {
            SeriesPropsActionHandler.updateSharedSeriesShadowPresets(dataHolder);
            updateSharedSeriesShadowPresetStyles(dataHolder);
        } else {
            SeriesPropsActionHandler.updateSeriesShadowPresets(dataHolder);
            updateSeriesShadowPresetStyles(dataHolder);
        }
    }

    private static void updateSharedSeriesShadowPresetStyles(SubActionDataHolder dataHolder) {
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        String presetName = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PRESET);

        PresetParams params = new PresetParams()
                .setParam(PresetParams.Name.PRESET_NAME, presetName);

        DelegatingPreset preset = ObjectsSupplier.getInstance()
                .getPresetStyles(Purpose.PresetStylesPurpose.SHARED_SERIES_SHADOW, params);
        preset.applyPreset(dataHolder.getActiveSheet().getWorkbook(), chart);
    }

    private static void updateSeriesShadowPresetStyles(SubActionDataHolder dataHolder) {
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        String presetName = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PRESET);
        int seriesIndex = getSeriesIndex(valueJSON);

        PresetParams params = new PresetParams()
                .setParam(PresetParams.Name.SERIES_INDEX, seriesIndex)
                .setParam(PresetParams.Name.PRESET_NAME, presetName);

        DelegatingPreset preset = ObjectsSupplier.getInstance()
                .getPresetStyles(Purpose.PresetStylesPurpose.SERIES_SHADOW, params);
        preset.applyPreset(dataHolder.getActiveSheet().getWorkbook(), chart);
    }

}
