package com.zoho.sheet.knitcharts.actionhandlers.gridaction.modification;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.google.common.collect.Streams;
import com.zoho.sheet.knitcharts.api.ChartsAPIEndpoint;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.pojo.ModifiedChartPOJO;
import com.zoho.sheet.knitcharts.pojo.ModifiedReferencePOJO;
import com.zoho.sheet.knitcharts.reference.ReferenceUpdateListener;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Chart Modification listener for sheet level modification actions
 */
public class SheetLevelModificationListener {

    /**
     * Upon sheet delete, a chart either affected by data source or it resides inside the deleted sheet.
     * @param workbook Workbook instance
     * @param deletedSheetASN Deleted sheet Associated sheet name
     * @return list of modified and deleted chart ids
     */
    public static List<ModifiedChartPOJO> onSheetDelete(Workbook workbook, String deletedSheetASN) {
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = workbook.getChartsContainer();

        chartContainer.deleteSheet(deletedSheetASN);

        List<ModifiedReferencePOJO> modifiedReferencePOJOS = ReferenceUpdateListener.onSheetDelete(workbook, deletedSheetASN);
        List<ModifiedChartPOJO> modifiedChartPOJOS = ChartModificationListener.convertAndUpdateChart(workbook, modifiedReferencePOJOS, false);
        List<ModifiedChartPOJO> pivotModifiedChartPOJOS = PivotTableUpdateListener.onSheetDelete(workbook, deletedSheetASN);

        return Streams.concat(modifiedChartPOJOS.stream(), pivotModifiedChartPOJOS.stream()).collect(Collectors.toList());
    }

    public static JSONObjectWrapper onPreprocessSheetDuplicate(WorkbookContainer container, Workbook workbook, String newSheetName, Sheet sourceSheet) {
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = workbook.getChartsContainer();
        JSONObjectWrapper duplicateChartIDsMap = new JSONObjectWrapper();
        ChartsAPIEndpoint endpoint = ObjectsSupplier.getInstance().getChartsAPIEndpoint(workbook, container.getDocOwner(), Long.parseLong(container.getDocId()));

        chartContainer.forEachChart(sourceSheet.getAssociatedName(), chart ->
                duplicateChartIDsMap.put(chart.getChartID(), endpoint.copyChartToNewSheetDB(chart, newSheetName)));

        return duplicateChartIDsMap;
    }

    public static void onDBSheetDuplicate(WorkbookContainer container, Workbook workbook, Sheet sourceSheet, String newSheetName, JSONObjectWrapper ogToDupChartIDs) {
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = workbook.getChartsContainer();
        ChartsAPIEndpoint endpoint = ObjectsSupplier.getInstance().getChartsAPIEndpoint(workbook, container.getDocOwner(), Long.parseLong(container.getDocId()));

        chartContainer.forEachChart(sourceSheet.getAssociatedName(), chart ->  {
            endpoint.copyChartToNewSheetDB(chart, ogToDupChartIDs.getString(chart.getChartID()), newSheetName);
        }) ;
    }

    public static void onSheetDuplicate(WorkbookContainer container, Workbook workbook, String newSheetName, String sourceSheetName, JSONObjectWrapper duplicateChartIDsMap) {
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = workbook.getChartsContainer();
        ChartsAPIEndpoint endpoint = ObjectsSupplier.getInstance().getChartsAPIEndpoint(workbook, container.getDocOwner(), Long.parseLong(container.getDocId()));

        ChartUtils.forEachKey(duplicateChartIDsMap, originalChartID -> {
            Chart originalChart = chartContainer.getChart(originalChartID);
            String newChartID = duplicateChartIDsMap.getString(originalChartID);

            endpoint.copyChartToDuplicateSheet(originalChart, newChartID, sourceSheetName, newSheetName);
        });
    }

    public static JSONObjectWrapper onPreprocessSheetCopyToNewWorkbook(WorkbookContainer destContainer, Workbook destWorkbook, Workbook sourceWorkbook, Map<String, String> srcToNewSheetName) {
        ChartsAPIEndpoint destApiendPoint = com.zoho.sheet.knitcharts.supplier.ObjectsSupplier.getInstance().getChartsAPIEndpoint(destWorkbook, destContainer.getDocOwner(), Long.parseLong(destContainer.getDocId()));
        com.zoho.sheet.knitcharts.container.ChartContainer sourceChartContainer = sourceWorkbook.getChartsContainer();
        JSONObjectWrapper duplicateChartIDsMap = new JSONObjectWrapper();

        for(Map.Entry<String, String> entry : srcToNewSheetName.entrySet()) {
            Sheet sourceSheet = sourceWorkbook.getSheet(entry.getKey());

            sourceChartContainer.forEachChart(sourceSheet.getAssociatedName(), chart -> {
                String newChartID = destApiendPoint.copyChartToNewSheetDB(chart, entry.getValue());
                duplicateChartIDsMap.put(chart.getChartID(), newChartID);
            });
        }

        return duplicateChartIDsMap;
    }

    public static void onDBSheetCopyToNewWorkbook(WorkbookContainer destContainer, Workbook destWorkbook, Workbook sourceWorkbook,
                                                  JSONObjectWrapper oldToNewChartIDs, Map<String, String> srcToNewSheetName) {
        ChartsAPIEndpoint destApiendPoint = ObjectsSupplier.getInstance().getChartsAPIEndpoint(destWorkbook, destContainer.getDocOwner(), Long.parseLong(destContainer.getDocId()));
        ChartContainer sourceChartContainer = sourceWorkbook.getChartsContainer();

        for(Map.Entry<String, String> entry : srcToNewSheetName.entrySet()) {
            Sheet sourceSheet = sourceWorkbook.getSheet(entry.getKey());

            sourceChartContainer.forEachChart(sourceSheet.getAssociatedName(), chart -> {
                destApiendPoint.copyChartToNewSheetDB(chart, oldToNewChartIDs.getString(chart.getChartID()), entry.getValue());
            });
        }
    }

    public static void onSheetCopyToNewWorkbook(WorkbookContainer destContainer, Workbook destWorkbook,
                                                Workbook sourceWorkbook, Map<String, String> srcToNewSheetName,
                                                Map<String, String> sourceToTargetNamedReferences,
                                                JSONObjectWrapper duplicateChartIDsMap) {
        ChartsAPIEndpoint destApiendPoint = ObjectsSupplier.getInstance().getChartsAPIEndpoint(destWorkbook, destContainer.getDocOwner(), Long.parseLong(destContainer.getDocId()));
        ChartContainer sourceChartContainer = sourceWorkbook.getChartsContainer();

        for(Map.Entry<String, String> entry : srcToNewSheetName.entrySet()) {
            Sheet sourceSheet = sourceWorkbook.getSheet(entry.getKey());

            sourceChartContainer.forEachChart(sourceSheet.getAssociatedName(), chart ->
                    destApiendPoint.copyChartToNewSheetWorkbook(sourceWorkbook,
                            duplicateChartIDsMap.getString(chart.getChartID()), chart, srcToNewSheetName,
                            sourceToTargetNamedReferences));
        }
    }

}
