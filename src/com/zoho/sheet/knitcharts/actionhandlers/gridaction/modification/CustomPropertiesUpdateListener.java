package com.zoho.sheet.knitcharts.actionhandlers.gridaction.modification;

import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.pojo.CustPropsUpdatePOJO;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font.DefaultFontJSONConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font.Font;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionConsumer;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorTheme.ColorThemeOptions;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.xaxis.XAxes;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.YAxes;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.plotlines.PlotLine;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.SheetChartAPIUtils;

import java.util.*;

/**
 * Custom properties update listener
 * <AUTHOR>
 */
public final class CustomPropertiesUpdateListener {

    private static List<FunctionConsumer<CustPropsUpdatePOJO>> themeColorUpdater;

    private static List<FunctionConsumer<CustPropsUpdatePOJO>> getThemeColorUpdaterList(){
        if(themeColorUpdater == null){
            themeColorUpdater = Arrays.asList(CustomPropertiesUpdateListener::updateBackgroundColor,
                    CustomPropertiesUpdateListener::updateBorderColor, CustomPropertiesUpdateListener::updateTitleFontColor,
                    CustomPropertiesUpdateListener::updateSubtitleFontColor, CustomPropertiesUpdateListener::updateLegend,
                    CustomPropertiesUpdateListener::updateCaptionFontColor, CustomPropertiesUpdateListener::updateDonutDatalabelsFontColor,
                    CustomPropertiesUpdateListener::updateSeriesColors, CustomPropertiesUpdateListener::updateXAxisColors,
                    CustomPropertiesUpdateListener::updateYAxisColors,  CustomPropertiesUpdateListener::updateSharedSeriesColors,
                    CustomPropertiesUpdateListener::updateColorPaletteCustomColor, CustomPropertiesUpdateListener::updateSharedXAxisColors,
                    CustomPropertiesUpdateListener::updateTotalLabelFontColor, CustomPropertiesUpdateListener::updateAreaShadowColor,
                    CustomPropertiesUpdateListener::updateBackgroundGradientColor, CustomPropertiesUpdateListener::updateCreditsFontColor
            );
        }
        return themeColorUpdater;
    }

    private static Font getFontFromColor(String color){
        Font font = new Font(new DefaultFontJSONConstants());

        font.setFontColor(color);
        return font;
    }

    private static String getColorFromTheme(ZSTheme theme, ColorThemeOptions colorThemeOptions){
        String accent = colorThemeOptions.getAccent();
        Double tonePercent = colorThemeOptions.getTone();

        return ChartUtils.getColorFromTheme(theme, accent, tonePercent);
    }

    /**
     * Listener for theme update event.
     * @param workbook WorkBook Instance
     */
    public static List<CustPropsUpdatePOJO> onThemeChange(Workbook workbook){
        ZSTheme theme = workbook.getTheme();
        ChartContainer chartContainer = workbook.getChartsContainer();
        List<FunctionConsumer<CustPropsUpdatePOJO>> updaterList = getThemeColorUpdaterList();
        List<CustPropsUpdatePOJO> custPropsUpdatePOJOS = new ArrayList<>();

        chartContainer.forEachChart(chart -> {
            CustPropsUpdatePOJO pojo = new CustPropsUpdatePOJO(chart, theme);

            updaterList.forEach(updater -> updater.consume(pojo));
            custPropsUpdatePOJOS.add(pojo);
        });

        return custPropsUpdatePOJOS;
    }

    static void updateBackgroundGradientColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String stopsThemeString = SheetChartGetterAPI.getCustomPropsChartAreaGradientStops(sheetMeta);
        String stopsString = FrameworkChartGetterAPI.getChartAreaGradientStops(chartMeta);

        FrameworkChartAPI.updateChartAreaGradientStops(chartMeta, ChartUtils.updateStopsColorFromStopsThemes(theme, stopsString, stopsThemeString));
        bean.setUpdated(true);
    }

    static void updateCreditsFontColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getCreditsFontColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getCreditsFontColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateCreditsFontColor(chartMeta, color);
        bean.setUpdated(true);
    }

    /**
     * Updates chart border color when sheet theme change
     */
    static void updateBackgroundColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getBackgroundColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getBackgroundColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateChartBGColor(chartMeta, color);
        bean.setUpdated(true);
    }

    static void updateColorPaletteCustomColor(CustPropsUpdatePOJO bean) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();

        ColorThemeOptions colorPaletteCustomColor = SheetChartAPI.getColorPaletteCustomColorThemeOptions(sheetMeta);
        String color = getColorFromTheme(theme, colorPaletteCustomColor);

        if(color == null) { return; }
        SheetChartAPI.updateColorPaletteCustomColor(sheetMeta, color);
        bean.setUpdated(true);
    }

    static void updateBorderColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getChartBorderColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getChartBorderColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateChartBorderColor(chartMeta, color);
        bean.setUpdated(true);
    }

    static void updateAreaShadowColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getChartAreaShadowColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getChartAreaShadowColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateChartAreaShadowColor(chartMeta, color);
        bean.setUpdated(true);
    }

    static void updateTitleFontColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getTitleColorAccent( sheetMeta);
        Double tone = SheetChartAPI.getTitleColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateChartTitleFont(chartMeta, getFontFromColor(color));
        bean.setUpdated(true);
    }

    static void updateSubtitleFontColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getSubtitleAccent(sheetMeta);
        Double tone = SheetChartAPI.getSubtitleTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateChartSubtitleFont(chartMeta, getFontFromColor(color));
        bean.setUpdated(true);
    }

    static void updateLegend(CustPropsUpdatePOJO bean){
        updateLegendFontColor(bean);
        updateLegendTitleFontColor(bean);
    }

    private static void updateLegendFontColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getLegendFontColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getLegendFontColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateChartLegendFont(chartMeta, getFontFromColor(color));
    }

    private static void updateLegendTitleFontColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getLegendTitleFontColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getLegendTitleFontColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateLegendTitleFontColor(chartMeta, color);
    }

    static void updateCaptionFontColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getCaptionColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getCaptionColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateCaptionFontStyle(chartMeta, getFontFromColor(color));
        bean.setUpdated(true);
    }

    static void updateTotalLabelFontColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getTotalLabelAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getTotalLabelTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateTotalLabelFont(chartMeta, getFontFromColor(color));
        bean.setUpdated(true);
    }

    static void updateDonutDatalabelsFontColor(CustPropsUpdatePOJO bean) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getTotalDatalablesFontColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getTotalDatalabelsFontColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if (color == null) {
            return;
        }
        FrameworkChartAPI.updateTotalDatalabelsFont(chartMeta, getFontFromColor(color));
        bean.setUpdated(true);
    }

    static void updateSeriesColors(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        SheetMeta sheetMeta = chart.getSheetMeta();

        SheetChartAPIUtils.forEachCustomSeriesProps(sheetMeta, (seriesIndex) -> {
            updateDownColor(bean, seriesIndex);
            updateUpColor(bean, seriesIndex);
            updateTargetColor(bean, seriesIndex);
            updateNegativeColor(bean, seriesIndex);
            updateDatalabelsFontColor(bean, seriesIndex);
            updateTrendlineColors(bean, seriesIndex);
            updateDataPropertiesColors(bean, seriesIndex);
            updateMarkerColors(bean, seriesIndex);
            updateSeriesColor(bean, seriesIndex);
            updateSeriesBorderColor(bean, seriesIndex);
            updateSeriesMeanColor(bean, seriesIndex);
            updateSeriesMedianColor(bean, seriesIndex);
            updateSeriesOutliersColor(bean, seriesIndex);
            updateSeriesWhiskerColor(bean, seriesIndex);
            updateSeriesShadowColor(bean, seriesIndex);
            updateSeriesDatalabelsBgColor(bean, seriesIndex);
            updateSeriesDatalabelsBorderColor(bean, seriesIndex);
            updateSeriesSliderFontColor(bean, seriesIndex);
            updateSeriesColorGradient(bean, seriesIndex);
            updateSeriesUpColorGradient(bean, seriesIndex);
            updateSeriesDownColorGradient(bean, seriesIndex);
            updateSeriesTotalColorGradient(bean, seriesIndex);
        });
    }

    private static void updateSeriesColorGradient(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String stopsString = FrameworkChartGetterAPI.getSeriesGradientStops(chartMeta, seriesIndex);
        String stopsThemeString = SheetChartGetterAPI.getCustomPropsSeriesColorStops(sheetMeta, seriesIndex);

        FrameworkChartAPI.updateSeriesGradientStops(chartMeta, ChartUtils.updateStopsColorFromStopsThemes(theme, stopsString, stopsThemeString), seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesUpColorGradient(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String stopsString = FrameworkChartGetterAPI.getSeriesUpColorGradientStops(chartMeta, seriesIndex);
        String stopsThemeString = SheetChartGetterAPI.getCustomPropsUpColorGradientStops(sheetMeta, seriesIndex);

        FrameworkChartAPI.updateUpColorGradientStops(chartMeta, ChartUtils.updateStopsColorFromStopsThemes(theme, stopsString, stopsThemeString), seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesDownColorGradient(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String stopsString = FrameworkChartGetterAPI.getSeriesDownColorGradientStops(chartMeta, seriesIndex);
        String stopsThemeString = SheetChartGetterAPI.getCustomPropsDownColorGradientStops(sheetMeta, seriesIndex);

        FrameworkChartAPI.updateDownColorGradientStops(chartMeta, ChartUtils.updateStopsColorFromStopsThemes(theme, stopsString, stopsThemeString), seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesTotalColorGradient(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String stopsString = FrameworkChartGetterAPI.getSeriesTotalColorGradientStops(chartMeta, seriesIndex);
        String stopsThemeString = SheetChartGetterAPI.getCustomPropsTotalColorGradientStops(sheetMeta, seriesIndex);

        FrameworkChartAPI.updateSeriesTotalColorGradientStops(chartMeta, ChartUtils.updateStopsColorFromStopsThemes(theme, stopsString, stopsThemeString), seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesSliderFontColor(CustPropsUpdatePOJO bean, int seriesIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getSeriesSliderLabelsFontColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartGetterAPI.getSeriesSliderLabelsFontColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSeriesSliderLabelsFont(chartMeta, getFontFromColor(color), seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesDatalabelsBgColor(CustPropsUpdatePOJO bean, int seriesIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getSeriesDataLabelsBgColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartGetterAPI.getSeriesDataLabelsBgColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSeriesDataLabelsBgColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesDatalabelsBorderColor(CustPropsUpdatePOJO bean, int seriesIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getSeriesDataLabelsBorderColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartGetterAPI.getSeriesDataLabelsBorderColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSeriesDataLabelsBorderColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateDownColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getDownColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getDownColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateDownColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateUpColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getUpColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getUpColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateUpColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateTargetColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getTargetColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getTargetColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateTargetColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateNegativeColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getNegativeColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getNegativeColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSeriesThresholdColorWithIndex(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateDatalabelsFontColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getDataLabelsFontColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getDataLabelsFontColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSeriesDataLabelsFontWithIndex(chartMeta, getFontFromColor(color), seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateTrendlineColors(CustPropsUpdatePOJO bean, int seriesIndex){
        updateTrendlineDatalabelsFontColor(bean, seriesIndex);
        updateTrendlineLineColor(bean, seriesIndex);
    }

    private static void updateTrendlineDatalabelsFontColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getSeriesTrendlineDatalabelsFontColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartGetterAPI.getSeriesTrendlineDatalabelsFontColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSeriesTrendlineDatalabelsFontColour(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateTrendlineLineColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getTrendlineColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getTrendlineColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateTrendLineColorWithIndex(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertiesColors(CustPropsUpdatePOJO bean, int seriesIndex){
        SheetMeta sheetMeta = bean.getChart().getSheetMeta();

        SheetChartAPIUtils.forEachCustomSeriesDataProps(sheetMeta, seriesIndex, (dataPropIndex) -> {

            updateDataPropertColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyBorderColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyDataLabelFontColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertiesDataLabelsBgColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertiesDataLabelsBorderColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyMarkerColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyMarkerBorderColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyOutliersColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyMeanColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyMedianColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyWhiskersColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyShadowColor(bean, seriesIndex, dataPropIndex);
            updateDataPropertyColorGradient(bean, seriesIndex, dataPropIndex);
        });
    }

    private static void updateDataPropertColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getDataPropertyColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartAPI.getDataPropertyColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSeriesDataPropertyColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyBorderColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getDataPropertyBorderColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartAPI.getDataPropertyBorderColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSeriesDataPropertyBorderColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyDataLabelFontColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyDataLabelFontColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyDataLabelFontColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyDataLabelsFontColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyColorGradient(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String stopsString = FrameworkChartGetterAPI.getDataPropertyGradientStops(chartMeta, seriesIndex, dataPropsIndex);
        String stopsThemeString = SheetChartGetterAPI.getCustomPropsDataPropertyColorStops(sheetMeta, seriesIndex, dataPropsIndex);

        FrameworkChartAPI.updateDataPropertyGradientStops(chartMeta, ChartUtils.updateStopsColorFromStopsThemes(theme, stopsString, stopsThemeString), seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyMarkerColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyMarkerColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyMarkerColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyMarkerColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyMarkerBorderColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyMarkerBorderColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyMarkerBorderColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyMarkerBorderColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyOutliersColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyOutliersColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyOutliersColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyOutliersColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyMeanColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyMeanColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyMeanColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyMeanColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyMedianColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyMedianColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyMedianColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyMedianColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyWhiskersColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyWhiskersColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyWhiskersColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyWhiskersColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertyShadowColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropsIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyShadowColorAccent(sheetMeta, seriesIndex, dataPropsIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyShadowColorTone(sheetMeta, seriesIndex, dataPropsIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyShadowColor(chartMeta, color, seriesIndex, dataPropsIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertiesDataLabelsBgColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyDataLabelsBgColorAccent(sheetMeta, seriesIndex, dataPropIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyDataLabelsBgColorTone(sheetMeta, seriesIndex, dataPropIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyDataLabelsBgColor(chartMeta, color, seriesIndex, dataPropIndex);
        bean.setUpdated(true);
    }

    private static void updateDataPropertiesDataLabelsBorderColor(CustPropsUpdatePOJO bean, int seriesIndex, int dataPropIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getDataPropertyDataLabelsBorderColorAccent(sheetMeta, seriesIndex, dataPropIndex);
        Double tone = SheetChartGetterAPI.getDataPropertyDataLabelsBorderColorTone(sheetMeta, seriesIndex, dataPropIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateDataPropertyDataLabelsBorderColor(chartMeta, color, seriesIndex, dataPropIndex);
        bean.setUpdated(true);
    }

    private static void updateMarkerColors(CustPropsUpdatePOJO bean, int seriesIndex){
        updateSeriesMarkerFillColor(bean, seriesIndex);
        updateSeriesMarkerBorderColor(bean, seriesIndex);
    }

    private static void updateSeriesMarkerFillColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getSeriesMarkerColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartGetterAPI.getSeriesMarkerColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSeriesMarkerColorWithIndex(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesMarkerBorderColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getSeriesMarkerBorderColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartGetterAPI.getSeriesMarkerBorderColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSeriesMarkerBorderColorWithIndex(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();


        String accent = SheetChartAPI.getSeriesColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getSeriesColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSeriesColorWithIndex(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesBorderColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();


        String accent = SheetChartAPI.getSeriesBorderColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getSeriesBorderColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSeriesBorderColorWithIndex(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesMeanColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getSeriesMeanColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getSeriesMeanColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateMeanColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesMedianColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getSeriesMedianColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getSeriesMedianColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateMedianColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesOutliersColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getSeriesOutlierColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getSeriesOutlierColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateOutliersColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesWhiskerColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartAPI.getSeriesWhiskerColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartAPI.getSeriesWhiskerColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateWhiskersColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }

    private static void updateSeriesShadowColor(CustPropsUpdatePOJO bean, int seriesIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getSeriesShadowColorAccent(sheetMeta, seriesIndex);
        Double tone = SheetChartGetterAPI.getSeriesShadowColorTone(sheetMeta, seriesIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSeriesShadowColor(chartMeta, color, seriesIndex);
        bean.setUpdated(true);
    }


    static void updateXAxisColors(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        SheetMeta sheetMeta = chart.getSheetMeta();

        Map<ChartsInteger, XAxes> xAxesMap = SheetChartAPI.getXAxesMap(sheetMeta);

        for(ChartsInteger key: xAxesMap.keySet()){
            int index = key.getValue();

            updateXAxisTitleColor(bean, index);
            updateXAxisLabelColor(bean, index);
            updateXAxisMajorGLColor(bean, index);
            updateXAxisMinorGLColor(bean, index);
            updateXAxisBaseLineColor(bean, index);
            updateXAxisMajorTickColor(bean, index);
            updateXAxisMinorTickColor(bean, index);
        }
    }

    private static void updateXAxisMajorTickColor(CustPropsUpdatePOJO bean, int xAxisIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getXAxisMajorTickColorAccent(sheetMeta, xAxisIndex);
        Double tone = SheetChartGetterAPI.getXAxisMajorTickColorTone(sheetMeta, xAxisIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateXAxisMajorTickColor(chartMeta, color, xAxisIndex);
        bean.setUpdated(true);
    }

    public static void updateXAxisMinorTickColor(CustPropsUpdatePOJO bean, int xAxisIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getXAxisMinorTickColorAccent(sheetMeta, xAxisIndex);
        Double tone = SheetChartGetterAPI.getXAxisMinorTickColorTone(sheetMeta, xAxisIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateXAxisMinorTickColor(chartMeta, color, xAxisIndex);
        bean.setUpdated(true);
    }

    private static void updateXAxisTitleColor(CustPropsUpdatePOJO bean, int xIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getXAxisTitleFontColorAccent(sheetMeta, xIndex);
        Double tone = SheetChartGetterAPI.getXAxisTitleFontColorTone(sheetMeta, xIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateChartXAxisTitleFontWithIndex(chartMeta, getFontFromColor(color), xIndex);
        bean.setUpdated(true);
    }

    private static void updateXAxisLabelColor(CustPropsUpdatePOJO bean, int xIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getXAxisLabelsFontColorAccent(sheetMeta, xIndex);
        Double tone = SheetChartGetterAPI.getXAxisLabelsFontColorTone(sheetMeta, xIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }

        FrameworkChartAPI.updateXAxisLabelsFontStyleWithIndex(chartMeta, getFontFromColor(color), xIndex);
        bean.setUpdated(true);
    }

    private static void updateXAxisMajorGLColor(CustPropsUpdatePOJO bean, int xIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getXAxisMajorGridlineColorAccent(sheetMeta, xIndex);
        Double tone = SheetChartGetterAPI.getXAxisMajorGridlineColorTone(sheetMeta, xIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }

        FrameworkChartAPI.updateXAxisMajorGLColor(chartMeta, color, xIndex);
        bean.setUpdated(true);
    }

    private static void updateXAxisMinorGLColor(CustPropsUpdatePOJO bean, int xIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getXAxisMinorGridlineColorAccent(sheetMeta, xIndex);
        Double tone = SheetChartGetterAPI.getXAxisMinorGridlineColorTone(sheetMeta, xIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }

        FrameworkChartAPI.updateXAxisMinorGLColor(chartMeta, color, xIndex);
        bean.setUpdated(true);
    }

    private static void updateXAxisBaseLineColor(CustPropsUpdatePOJO bean, int xIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getXAxisBaseLineColorAccent(sheetMeta, xIndex);
        Double tone = SheetChartGetterAPI.getXAxisBaseLineColorTone(sheetMeta, xIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }

        FrameworkChartAPI.updateXAxisLineColorWithIndex(chartMeta, color, xIndex);
        bean.setUpdated(true);
    }

    static void updateSharedXAxisColors(CustPropsUpdatePOJO bean){
        updateSharedXAxisTitleColor(bean);
    }

    private static void updateSharedXAxisTitleColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getSharedXAxisTitleFontColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getSharedXAxisTitleFontColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSharedXAxisTitleFont(chartMeta, getFontFromColor(color));
        bean.setUpdated(true);
    }

    static void updateYAxisColors(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        SheetMeta sheetMeta = chart.getSheetMeta();

        Map<ChartsInteger, YAxes> yAxesMap = SheetChartAPI.getYAxesMap(sheetMeta);

        for(ChartsInteger key: yAxesMap.keySet()){
            int index = key.getValue();

            updateYAxisTitleFontColor(bean, index);
            updateYAxisLabelFontColor(bean, index);
            updateYAxisMajorGLColor(bean, index);
            updateYAxisMinorGLColor(bean, index);
            updatePlotlineColors(bean, key);
            updateYAxisStackLabelsFontColor(bean, index);
            updateYAxisMajorTickColor(bean, index);
            updateYAxisMinorTickColor(bean, index);
            updateYAxisBaseLineColor(bean, index);
        }
    }

    private static void updateYAxisBaseLineColor(CustPropsUpdatePOJO bean, int yAxisIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getYAxisBaseLineColorAccent(sheetMeta, yAxisIndex);
        Double tone = SheetChartGetterAPI.getYAxisBaseLineTone(sheetMeta, yAxisIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateYAxisBaseLineColor(chartMeta, color, yAxisIndex);
        bean.setUpdated(true);
    }

    private static void updateYAxisMajorTickColor(CustPropsUpdatePOJO bean, int yAxisIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        String accent = SheetChartGetterAPI.getYAxisMajorTickColorAccent(sheetMeta, yAxisIndex);
        Double tone = SheetChartGetterAPI.getYAxisMajorTickColorTone(sheetMeta, yAxisIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateYAxisMajorTickColor(chartMeta, color, yAxisIndex);
        bean.setUpdated(true);
    }

    private static void updateYAxisMinorTickColor(CustPropsUpdatePOJO bean, int yAxisIndex) {
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        String accent = SheetChartGetterAPI.getYAxisMinorTickColorAccent(sheetMeta, yAxisIndex);
        Double tone = SheetChartGetterAPI.getYAxisMinorTickColorTone(sheetMeta, yAxisIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateYAxisMinorTickColor(chartMeta, color, yAxisIndex);
        bean.setUpdated(true);
    }

    private static void updateYAxisTitleFontColor(CustPropsUpdatePOJO bean, int yIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getYAxisTitleFontColorAccent(sheetMeta, yIndex);
        Double tone = SheetChartGetterAPI.getYAxisTitleFontColorTone(sheetMeta, yIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateChartYAxisTitleFontWithIndex(chartMeta, getFontFromColor(color), yIndex);
        bean.setUpdated(true);
    }

    private static void updateYAxisLabelFontColor(CustPropsUpdatePOJO bean,  int yIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getYAxisLabelsFontColorAccent(sheetMeta, yIndex);
        Double tone = SheetChartGetterAPI.getYAxisLabelsFontColorTone(sheetMeta, yIndex);

        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateYAxisLabelFontStyleWithIndex(chartMeta, getFontFromColor(color), yIndex);
        bean.setUpdated(true);
    }

    private static void updateYAxisMajorGLColor(CustPropsUpdatePOJO bean, int yIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getYAxisMajorGridlineColorAccent(sheetMeta, yIndex);
        Double tone = SheetChartGetterAPI.getYAxisMajorGridlineColorTone(sheetMeta, yIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }

        FrameworkChartAPI.updateYAxisMajorGLColor(chartMeta, color, yIndex);
        bean.setUpdated(true);
    }

    private static void updateYAxisMinorGLColor(CustPropsUpdatePOJO bean, int yIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getYAxisMinorGridlineColorAccent(sheetMeta, yIndex);
        Double tone = SheetChartGetterAPI.getYAxisMinorGridlineColorTone(sheetMeta, yIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }

        FrameworkChartAPI.updateYAxisMinorGLColor(chartMeta, color, yIndex);
        bean.setUpdated(true);
    }

    private static void updatePlotlineColors(CustPropsUpdatePOJO bean, ChartsInteger yIndex){
        Chart chart = bean.getChart();
        SheetMeta sheetMeta = chart.getSheetMeta();
        Map<ChartsInteger, PlotLine> plotLineMap = SheetChartAPI.getPlotlineMap(sheetMeta, yIndex);
        int yAxisIndex = yIndex.getValue();

        for(ChartsInteger key: plotLineMap.keySet()){
            int index = key.getValue();

            updateYAxisPlotlineColor(bean, yAxisIndex, index);
            updateYAxisPlotlineLabelFontColor(bean, yAxisIndex, index);
        }
    }

    private static void updateYAxisPlotlineLabelFontColor(CustPropsUpdatePOJO bean, int yIndex, int plIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getYAxisPlotlineLabelFontColorAccent(sheetMeta, yIndex, plIndex);
        Double tone = SheetChartGetterAPI.getYAxisPlotlineLabelFontColorTone(sheetMeta, yIndex, plIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }

        FrameworkChartAPI.updatePlotLineFontStyle(chartMeta, getFontFromColor(color), yIndex, plIndex);
        bean.setUpdated(true);
    }

    private static void updateYAxisPlotlineColor(CustPropsUpdatePOJO bean, int yIndex, int plIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getYAxisPlotlineColorAccent(sheetMeta, yIndex, plIndex);
        Double tone = SheetChartGetterAPI.getYAxisPlotlineColorTone(sheetMeta, yIndex, plIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }

        FrameworkChartAPI.updatePlotLineColor(chartMeta, color, yIndex, plIndex);
        bean.setUpdated(true);
    }

    private static void updateYAxisStackLabelsFontColor(CustPropsUpdatePOJO bean, int yIndex){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        SheetMeta sheetMeta = chart.getSheetMeta();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        String accent = SheetChartGetterAPI.getYAxisStackLabelsFontColorAccent(sheetMeta, yIndex);
        Double tone = SheetChartGetterAPI.getYAxisStackLabelsFontColorTone(sheetMeta, yIndex);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }

        FrameworkChartAPI.updateYAxisStackLabelFontStyle(chartMeta, getFontFromColor(color), yIndex);
        bean.setUpdated(true);
    }

    static void updateSharedSeriesColors(CustPropsUpdatePOJO bean){
        updateSharedSeriesTargetColor(bean);
        updateSharedSeriesNegativeColor(bean);
        updateSharedSeriesDataLabelsColor(bean);
        updateSharedSeriesMarkerColors(bean);
        updateSharedSeriesBorderColor(bean);
        updateSharedSeriesOutliersColor(bean);
        updateSharedSeriesMeanColor(bean);
        updateSharedSeriesMedianColor(bean);
        updateSharedSeriesWhiskersColor(bean);
        updateSharedSeriesShadowColor(bean);
        updateSharedSeriesDataLabelsBgColor(bean);
        updateSharedSeriesDataLabelsBorderColor(bean);
        updateSharedSeriesTrendlineDatalabelsFontColor(bean);
    }

    private static void updateSharedSeriesTrendlineDatalabelsFontColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();
        String accent = SheetChartGetterAPI.getSharedSeriesTrendlineDatalabelsFontColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getSharedSeriesTrendlineDatalabelsFontColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSharedSeriesTrendlineDatalabelsFontColour(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesDataLabelsBgColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();
        String accent = SheetChartGetterAPI.getSharedSeriesDataLabelsBgColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getSharedSeriesDataLabelsBgColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSharedSeriesDataLabelsBgColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesDataLabelsBorderColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();
        String accent = SheetChartGetterAPI.getSharedSeriesDataLabelsBorderColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getSharedSeriesDataLabelsBorderColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSharedSeriesDataLabelsBorderColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesTargetColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartAPI.getSharedSeriesTargetColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getSharedSeriesTargetColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSharedSeriesTargetColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesNegativeColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartAPI.getSharedSeriesNegativeColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getSharedSeriesNegativeColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSharedSeriesThresholdColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesDataLabelsColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartAPI.getSharedSeriesDataLabelsFontColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getSharedSeriesDataLabelsFontColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSharedSeriesDataLabelsFont(chartMeta, getFontFromColor(color));

        bean.setUpdated(true);
    }

    private static void updateSharedSeriesBorderColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();


        String accent = SheetChartAPI.getSharedSeriesBorderColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getSharedSeriesBorderColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSharedSeriesBorderColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesMarkerColors(CustPropsUpdatePOJO bean){
        updateSharedSeriesMarkerFillColor(bean);
        updateSharedSeriesMarkerBorderColor(bean);
    }

    private static void updateSharedSeriesMarkerFillColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getSharedSeriesMarkerColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getSharedSeriesMarkerColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSharedSeriesMarkerColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesMarkerBorderColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();

        String accent = SheetChartGetterAPI.getSharedSeriesMarkerBorderColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getSharedSeriesMarkerBorderColorTone(sheetMeta);
        String color = ChartUtils.getColorFromTheme(theme, accent, tone);

        if(color == null) { return; }
        FrameworkChartAPI.updateSharedSeriesMarkerBorderColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesOutliersColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();
        String accent = SheetChartAPI.getSharedSeriesOutliersColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getSharedSeriesOutliersColorTone(sheetMeta);

        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSharedSeriesOutliersColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesMeanColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();
        String accent = SheetChartAPI.getSharedSeriesMeanAccent(sheetMeta);
        Double tone = SheetChartAPI.getSharedSeriesMeanTone(sheetMeta);

        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSharedSeriesMeanColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesMedianColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();
        String accent = SheetChartAPI.getSharedSeriesMedianColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getSharedSeriesMedianColorTone(sheetMeta);

        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSharedSeriesMedianColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesWhiskersColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();
        String accent = SheetChartAPI.getSharedSeriesWhiskerColorAccent(sheetMeta);
        Double tone = SheetChartAPI.getSharedSeriesWhiskerColorTone(sheetMeta);

        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSharedSeriesWhiskersColor(chartMeta, color);
        bean.setUpdated(true);
    }

    private static void updateSharedSeriesShadowColor(CustPropsUpdatePOJO bean){
        Chart chart = bean.getChart();
        ZSTheme theme = bean.getTheme();
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();
        SheetMeta sheetMeta = chart.getSheetMeta();
        String accent = SheetChartGetterAPI.getSharedSeriesShadowColorAccent(sheetMeta);
        Double tone = SheetChartGetterAPI.getSharedSeriesShadowColorTone(sheetMeta);

        String color = ChartUtils.getColorFromTheme(theme, accent, tone);
        if(color == null) { return; }

        FrameworkChartAPI.updateSharedSeriesShadowColor(chartMeta, color);
        bean.setUpdated(true);
    }

}
