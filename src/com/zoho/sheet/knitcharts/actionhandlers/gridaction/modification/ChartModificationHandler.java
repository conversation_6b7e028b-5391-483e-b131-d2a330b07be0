package com.zoho.sheet.knitcharts.actionhandlers.gridaction.modification;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.constants.ChartModificationConstants;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.pojo.ModifiedChartPOJO;

import java.util.List;

/**
 * Handler for Chart modification occurred due to some actions not related to charts.
 * for example: cell submit, content clear etc.
 * <AUTHOR>
 */
public final class ChartModificationHandler {

    public static void onChartModified(Workbook workbook, List<ModifiedChartPOJO> modifiedChartPOJOS) {
        ChartContainer chartContainer = workbook.getChartsContainer();

        for(ModifiedChartPOJO modifiedChartPOJO : modifiedChartPOJOS) {
            chartContainer.setChartAsModified(modifiedChartPOJO);
        }
    }

    public static void onChartDeleted(List<String> deletedChartIds, Workbook workbook){
        ChartContainer chartContainer = workbook.getChartsContainer();

        for (String deletedChartId : deletedChartIds) {
            Chart chart = chartContainer.getChart(deletedChartId);
            ModifiedChartPOJO modifiedChartPOJO = new ModifiedChartPOJO(chart.getChartID(), ChartModificationConstants.ModificationActions.CHART_DELETED, chart.getAssociatedSheetName());

            chartContainer.setChartAsModified(modifiedChartPOJO);
            chartContainer.removeChart(chart);
        }
    }

}
