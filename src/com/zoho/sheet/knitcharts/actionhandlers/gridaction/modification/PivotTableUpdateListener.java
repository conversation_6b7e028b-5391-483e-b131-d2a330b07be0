package com.zoho.sheet.knitcharts.actionhandlers.gridaction.modification;

import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.knitcharts.constants.ChartModificationConstants;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.pojo.ModifiedChartPOJO;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class PivotTableUpdateListener {

    public static List<ModifiedChartPOJO> onPivotTablesUpdated(Workbook workbook, JSONObjectWrapper actionJSON) {
        JSONObjectWrapper pivotInfo = actionJSON.getJSONObject(JSONConstants.PIVOT_INFO);

        JSONArrayWrapper affectedPivotIDs = pivotInfo.has(String.valueOf(CommandConstants.UPDATE)) ?
                ChartUtils.optFromJSONObject(pivotInfo, String.valueOf(CommandConstants.UPDATE)) :
                ChartUtils.optFromJSONObject(pivotInfo, String.valueOf(CommandConstants.MODIFY));

        return onPivotTablesUpdated(workbook, affectedPivotIDs);
    }

    public static List<ModifiedChartPOJO> onPivotTablesUpdated(Workbook workbook, JSONArrayWrapper affectedPivotIDs) {
        if(affectedPivotIDs != null){
            List<ModifiedChartPOJO> modifiedChartPOJOS = new ArrayList<>();
            ChartContainer chartContainer = workbook.getChartsContainer();

            ChartUtils.forEach(affectedPivotIDs, (oPivotID) -> {
                String pivotID = ChartUtils.typeCast(oPivotID);

                addModifiedChartBeans(chartContainer, pivotID, modifiedChartPOJOS);
            });
            return modifiedChartPOJOS;
        }
        return Collections.emptyList();
    }

    public static List<ModifiedChartPOJO> onAllPivotTableUpdated(Workbook workbook) {
        List<ModifiedChartPOJO> modifiedChartPOJOS = new ArrayList<>();
        ChartContainer chartContainer = workbook.getChartsContainer();

        chartContainer.forEachChart(chart -> {
            if(chart.isPivotChart()) {
                modifiedChartPOJOS.add(new ModifiedChartPOJO(chart.getChartID(),
                        ChartModificationConstants.ModificationActions.DATA_TABLE_MODIFIED, chart.getAssociatedSheetName()));
            }
        });

        return modifiedChartPOJOS;
    }

    public static List<ModifiedChartPOJO> onPivotTableUpdated(Workbook workbook, JSONObjectWrapper actionJSON) {
        String pivotID = actionJSON.getString(JSONConstants.ID);

        return onPivotTableUpdated(workbook, pivotID);
    }

    public static List<ModifiedChartPOJO> onPivotTableUpdated(Workbook workbook, String pivotID) {
        ChartContainer chartContainer = workbook.getChartsContainer();
        List<ModifiedChartPOJO> modifiedChartPOJOS = new ArrayList<>();

        addModifiedChartBeans(chartContainer, pivotID, modifiedChartPOJOS);
        return modifiedChartPOJOS;
    }

    private static void addModifiedChartBeans(ChartContainer chartContainer, String pivotID, List<ModifiedChartPOJO> modifiedChartPOJOS) {
        chartContainer.forEachChart(chart -> {
            if(chart.isPivotChart()) {
                String dataSource = SheetChartGetterAPI.getDataSources(chart.getSheetMeta()).get(0);
                if(Objects.equals(dataSource, pivotID)) {
                    modifiedChartPOJOS.add(new ModifiedChartPOJO(chart.getChartID(),
                            ChartModificationConstants.ModificationActions.DATA_TABLE_MODIFIED, chart.getAssociatedSheetName()));
                }
            }
        });
    }

    public static List<ModifiedChartPOJO> onPivotTableInsert(Workbook workbook, JSONObjectWrapper actionJSON) {
        String pivotID = actionJSON.getString(JSONConstants.ID);
        ChartContainer chartContainer = workbook.getChartsContainer();
        List<ModifiedChartPOJO> modifiedChartPOJOS = new ArrayList<>();

        addModifiedChartBeansForPivotInsert(chartContainer, pivotID, modifiedChartPOJOS);
        return modifiedChartPOJOS;
    }

    private static void addModifiedChartBeansForPivotInsert(ChartContainer chartContainer, String pivotID, List<ModifiedChartPOJO> modifiedChartPOJOS) {
        chartContainer.forEachChart(chart -> {
            if(chart.isPivotChart()) {
                String dataSource = SheetChartGetterAPI.getDataSources(chart.getSheetMeta()).get(0);
                if(Objects.equals(dataSource, pivotID)) {
                    modifiedChartPOJOS.add(new ModifiedChartPOJO(chart.getChartID(),
                            ChartModificationConstants.ModificationActions.CHART_INSERT, chart.getAssociatedSheetName()));
                }
            }
        });
    }

    public static List<ModifiedChartPOJO> onSheetDelete(Workbook workbook, String deletedSheetASN) {
        ChartContainer chartContainer = workbook.getChartsContainer();
        List<ModifiedChartPOJO> modifiedChartPOJOS = new ArrayList<>();

        chartContainer.forEachChart(chart -> {
            if(chart.isPivotChart()) {
                String dataSource = SheetChartGetterAPI.getDataSources(chart.getSheetMeta()).get(0);
                PivotTable pivotTable = workbook.getDataPivotTable(dataSource);
                String targetRangeASN = pivotTable.getTargetCellRange() == null ? "" : pivotTable.getTargetCellRange().getAssociatedSheetName();        // NO I18N

                if(Objects.equals(deletedSheetASN, targetRangeASN)) {
                    modifiedChartPOJOS.add(new ModifiedChartPOJO(chart.getChartID(),
                            ChartModificationConstants.ModificationActions.CHART_DELETED, chart.getAssociatedSheetName()));
                }
            }
        });

        return modifiedChartPOJOS;
    }

}
