package com.zoho.sheet.knitcharts.actionhandlers.gridaction.preprocessor;


import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.SubActionDataHolder;
import com.zoho.sheet.knitcharts.constants.FrameworkChartActionConstants;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionConsumer;
import com.zoho.sheet.knitcharts.utils.ChartUtils;

public class FrameworkChartActionPreprocessor {

    private static FunctionConsumer<SubActionDataHolder> getHandler(int subAction) {
        switch (subAction) {
            case FrameworkChartActionConstants.SubActionConstants.CREDITS_TEXT_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.TITLE_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.TOTAL_DATALABELS_TEXT_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.SUBTITLE_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.X_AXIS_TITLE_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.LEGEND_TITLE_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.Y_AXIS_TITLE_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.TRENDLINE_DATALABELS_CUSTOM_LABEL_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.SERIES_DATALABELS_CUSTOM_VALUE_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.DATA_PROPS_DATALABELS_CUSTOM_VALUE_EDIT: {
                return FrameworkChartActionPreprocessor::handleText;
            }
            case FrameworkChartActionConstants.SubActionConstants.Y_AXIS_ADD_PLOTLINE:
            case FrameworkChartActionConstants.SubActionConstants.Y_AXIS_PLOTLINE_EDIT: {
                return FrameworkChartActionPreprocessor::handlePlotlineEdit;
            }
            case FrameworkChartActionConstants.SubActionConstants.X_AXIS_NUMBER_FORMAT_EDIT:
            case FrameworkChartActionConstants.SubActionConstants.Y_AXIS_NUMBER_FORMAT_EDIT: {
                return FrameworkChartActionPreprocessor::handleNumberFormatEdit;
            }
            case FrameworkChartActionConstants.SubActionConstants.Y_AXIS_RANGE_EDIT: {
                return FrameworkChartActionPreprocessor::handleYAxisRangeEdit;
            }
        }
        return FrameworkChartActionPreprocessor::emptyHandler;
    }

    public static void preprocess(SubActionDataHolder dataHolder) {
        getHandler(dataHolder.getSubActionConstant()).consume(dataHolder);
    }

    private static void handleText(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.TEXT)) { return; }

        String text = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.TEXT);
        valueJSON.put(FrameworkChartActionConstants.JSONConstants.TEXT, ChartUtils.base64Decode(text));
    }

    private static void handlePlotlineEdit(SubActionDataHolder dataHolder) {
        handleText(dataHolder);
        handlePlotlineValueEdit(dataHolder);
    }

    private static void handlePlotlineValueEdit(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.VALUE)) { return; }

        Object value = valueJSON.get(FrameworkChartActionConstants.JSONConstants.VALUE);

        if(value instanceof String) {
            valueJSON.put(FrameworkChartActionConstants.JSONConstants.VALUE, ChartUtils.base64Decode((String) value));
        } else {
            valueJSON.put(FrameworkChartActionConstants.JSONConstants.VALUE, value);
        }
    }

    private static void handleNumberFormatEdit(SubActionDataHolder dataHolder) {
        handlePrefixEdit(dataHolder);
        handleSuffixEdit(dataHolder);
    }

    private static void handlePrefixEdit(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.PREFIX)) { return; }

        String prefix = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.PREFIX);
        valueJSON.put(FrameworkChartActionConstants.JSONConstants.PREFIX, ChartUtils.base64Decode(prefix));
    }

    private static void handleSuffixEdit(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.SUFFIX)) { return; }

        String suffix = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.SUFFIX);
        valueJSON.put(FrameworkChartActionConstants.JSONConstants.SUFFIX, ChartUtils.base64Decode(suffix));
    }

    private static void handleYAxisRangeEdit(SubActionDataHolder dataHolder) {
        handleYAxisMinEdit(dataHolder);
        handleYAxisMaxEdit(dataHolder);
        handleYAxisIntervalEdit(dataHolder);
    }

    private static void handleYAxisMinEdit(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MIN)) { return; }

        String min = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.MIN);
        valueJSON.put(FrameworkChartActionConstants.JSONConstants.MIN, ChartUtils.base64Decode(min));
    }

    private static void handleYAxisMaxEdit(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.MAX)) { return; }

        String max = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.MAX);
        valueJSON.put(FrameworkChartActionConstants.JSONConstants.MAX, ChartUtils.base64Decode(max));
    }

    private static void handleYAxisIntervalEdit(SubActionDataHolder dataHolder) {
        JSONObjectWrapper valueJSON = dataHolder.getValueJSON();
        if(!valueJSON.has(FrameworkChartActionConstants.JSONConstants.INTERVAL)) { return; }

        String interval = valueJSON.getString(FrameworkChartActionConstants.JSONConstants.INTERVAL);
        valueJSON.put(FrameworkChartActionConstants.JSONConstants.INTERVAL, ChartUtils.base64Decode(interval));
    }

    private static void emptyHandler(SubActionDataHolder dataHolder) {}

}
