package com.zoho.sheet.knitcharts.actionhandlers.gridaction.preprocessor;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Table;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.style.TableStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSHexColor;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSThemeColor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.TableUtil;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.ActionDataHolder;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.UserSpecificActionDataHolder;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionConsumer;
import com.zoho.sheet.knitcharts.pojo.ActiveCellPOJO;
import com.zoho.sheet.knitcharts.chartclip.ChartClip;
import com.zoho.sheet.knitcharts.chartclip.ChartClipManager;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.ColorPaletteType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.List;

public class ChartActionPreprocessor {

    private static FunctionConsumer<ActionDataHolder> getHandler(int actionConstant) {
        switch (actionConstant) {
            case ActionConstants.PASTE_CHART_STYLES: { return ChartActionPreprocessor::handlePasteStyle; }
            case ActionConstants.PASTE_CHART: { return ChartActionPreprocessor::handleChartPaste; }
            case ActionConstants.INSERT_TABLE_CHART: { return ChartActionPreprocessor::handleInsertTableChart; }
            case ActionConstants.FRAMEWORK_CHART_EDIT: { return ChartActionPreprocessor::handleFrameworkChartAction; }
            case ActionConstants.SHEET_CHART_EDIT: { return ChartActionPreprocessor::handleSheetChartAction; }
            case ActionConstants.INSERT_RECOMMENDED_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART_VIA_CACHE:
            case ActionConstants.INSERT_PIVOT_CHART:
            case ActionConstants.INSERT_CHART: { return ChartActionPreprocessor::handleChartInsert; }
        }

        return ChartActionPreprocessor::emptyHandler;
    }

    private static ActionDataHolder getDataHolder(int actionConstant, Sheet activeSheet, JSONObjectWrapper chartJSON, String ZUID, ActiveCellPOJO activeCellPOJO) {
        switch (actionConstant) {
            case ActionConstants.INSERT_RECOMMENDED_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART_VIA_CACHE:
            case ActionConstants.INSERT_PIVOT_CHART:
            case ActionConstants.INSERT_CHART:
            case ActionConstants.PASTE_CHART_STYLES:
            case ActionConstants.PASTE_CHART: {
                return new UserSpecificActionDataHolder(activeSheet, actionConstant, chartJSON, ZUID, activeCellPOJO, false);
            }
        }

        return new ActionDataHolder(activeSheet, actionConstant, chartJSON, activeCellPOJO, false);
    }

    public static void handle(int action, Sheet activeSheet, JSONObjectWrapper actionJSON, String ZUID, WorkbookContainer container) {
        JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartActionConstants.JSONConstants.CHART_JSON);
        ActiveCellPOJO activeCellPOJO = new ActiveCellPOJO();

        activeCellPOJO.constructFromJSON(actionJSON.getJSONObject(JSONConstants.CURRENT_ACTIVE_CELL));
        ChartUtils.forEach(chartJSONs, chartJSON ->{
            FunctionConsumer<ActionDataHolder> handler = getHandler(action);
            handler.consume(getDataHolder(action, activeSheet, ChartUtils.typeCast(chartJSON), ZUID, activeCellPOJO));
        });
    }

    public static void handlePasteStyle(ActionDataHolder dataHolder) {
        UserSpecificActionDataHolder userSpecificActionDataHolder = (UserSpecificActionDataHolder) dataHolder;
        JSONObjectWrapper chartJSON = userSpecificActionDataHolder.getChartJSON();
        String userZUID = userSpecificActionDataHolder.getZUID();
        String clipID = ChartClipManager.getInstance().getStylesClipID(userZUID);
        ChartClip chartClip = ChartClipManager.getInstance().getClip(clipID);

        if(chartClip == null) { return; }
        ChartUtils.copyObjectIfAvailable(chartClip.getClippedData(), chartJSON, ChartActionConstants.JSONConstants.STYLES_JSON);
    }

    public static void handleChartPaste(ActionDataHolder dataHolder) {
        UserSpecificActionDataHolder userSpecificActionDataHolder = (UserSpecificActionDataHolder) dataHolder;
        JSONObjectWrapper chartJSON = userSpecificActionDataHolder.getChartJSON();
        String clipID = ChartClipManager.getInstance().getChartClipID(userSpecificActionDataHolder.getZUID());
        ChartClip chartClip = ChartClipManager.getInstance().getClip(clipID);

        if(chartClip == null) { return; }
        JSONObjectWrapper chartClipData = chartClip.getClippedData();
        JSONObjectWrapper sheetMetaJSON = chartClipData.getJSONObject(ChartActionConstants.JSONConstants.SHEET_META);
        JSONObjectWrapper spatialParams = ChartUtils.computeIfAbsent(sheetMetaJSON, SheetChartMetaConstants.SpatialParameters.KEY, StatelessLambda::newJSONObject);

        spatialParams.put(SheetChartMetaConstants.SpatialParameters.START_ROW, dataHolder.getActiveCellBean().getRow());
        spatialParams.put(SheetChartMetaConstants.SpatialParameters.START_COL, dataHolder.getActiveCellBean().getColumn());
        spatialParams.put(SheetChartMetaConstants.SpatialParameters.START_COL_DIFF, 0);
        spatialParams.put(SheetChartMetaConstants.SpatialParameters.START_ROW_DIFF, 0);
        ChartUtils.copyObject(chartClipData, chartJSON, ChartActionConstants.JSONConstants.CHART_META);
        chartJSON.put(ChartActionConstants.JSONConstants.SHEET_META, sheetMetaJSON);
    }

    public static void handleInsertTableChart(ActionDataHolder dataHolder) {
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        SheetMeta sheetMeta = new SheetMeta();

        sheetMeta.fromJSON(ChartUtils.optFromJSONObject(chartJSON, ChartActionConstants.JSONConstants.SHEET_META));
        List<String> dataSources = SheetChartAPI.getDataSources(sheetMeta);

        if(!dataSources.isEmpty()) {
            String tableName = dataSources.get(0);
            Workbook workbook = dataHolder.getActiveSheet().getWorkbook();
            Table table = TableUtil.getTable(workbook, tableName);

            if(table == null) { return; }
            TableStyle style = workbook.getTableStyle(table.getTableStyleName());

            if(style == null) { return; }

            ZSColor color = ChartUtils.getPrimaryColortFromTableStyle(style);
            if(color == null) { return; }

            if(color instanceof ZSHexColor) {
                ZSHexColor zshexColor = (ZSHexColor) color;

                SheetChartAPI.updateColorPaletteCustomColor(sheetMeta, zshexColor.getHexColorToWrite());
                SheetChartAPI.updateColorPaletteType(sheetMeta, ColorPaletteType.CUSTOM);
            } else if(color instanceof ZSThemeColor) {
                ZSThemeColor zsThemeColor = (ZSThemeColor) color;

                SheetChartAPI.updateColorPaletteCustomColor(sheetMeta, ChartUtils.getColorFromTheme(workbook.getTheme(),
                        zsThemeColor.getColor().name(), zsThemeColor.getTint()));
                SheetChartAPI.setColorPaletteCustomColorAccent(sheetMeta, zsThemeColor.getColor().name());
                SheetChartAPI.setColorPaletteCustomColorTone(sheetMeta, zsThemeColor.getTint());
                SheetChartAPI.updateColorPaletteType(sheetMeta, ColorPaletteType.CUSTOM);
            }
        }

        chartJSON.put(ChartActionConstants.JSONConstants.SHEET_META, sheetMeta.toJSON());
    }

    private static void handleFrameworkChartAction(ActionDataHolder dataHolder) {
        FrameworkChartActionPreprocessor.preprocess(dataHolder.toSubActionDataHolder());
    }

    private static void handleSheetChartAction(ActionDataHolder dataHolder) {
        SheetChartActionPreprocessor.preprocess(dataHolder.toSubActionDataHolder());
    }

    private static void handleChartInsert(ActionDataHolder dataHolder) {
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        JSONObjectWrapper sheetMetaJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.SHEET_META);
        JSONObjectWrapper dataTableOptionsJSON = sheetMetaJSON.getJSONObject(ChartActionConstants.JSONConstants.DATA_TABLE_OPTIONS);
        List<String> dataSources = ChartUtils.JSONArrayToList(dataTableOptionsJSON.getJSONArray(ChartActionConstants.JSONConstants.DATA_SOURCES));

        dataTableOptionsJSON.put(ChartActionConstants.JSONConstants.DATA_SOURCES, ChartUtils.base64Decode(dataSources));
    }

    private static void emptyHandler(ActionDataHolder dataHolder) {}

}
