package com.zoho.sheet.knitcharts.actionhandlers.dataholder;

import com.adventnet.zoho.websheet.model.Sheet;
import com.zoho.sheet.knitcharts.pojo.ActiveCellPOJO;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class PublicModificationActionDataHolder extends ActionDataHolder{

    private final String resourceID;

    private final String documentOwner;

    public PublicModificationActionDataHolder(Sheet activeSheet, int actionConstant, JSONObjectWrapper chartJSON, String resourceID, String documentOwner, ActiveCellPOJO activeCellPOJO, boolean isFromUndoRedo) {
        super(activeSheet, actionConstant, chartJSON, activeCellPOJO, isFromUndoRedo);
        this.documentOwner = documentOwner;
        this.resourceID = resourceID;
    }

    public String getResourceID() {
        return resourceID;
    }

    public String getDocumentOwner() {
        return documentOwner;
    }

}
