package com.zoho.sheet.knitcharts.actionhandlers.external;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.PublicModificationActionDataHolder;
import com.zoho.sheet.knitcharts.pojo.ActiveCellPOJO;
import com.zoho.sheet.knitcharts.pojo.ModifiedChartPOJO;
import com.zoho.sheet.knitcharts.pojo.ModifiedPublicChartPOJO;
import com.zoho.sheet.knitcharts.constants.ChartModificationConstants;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsPublicDAO;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.PublicChartType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.PublicChartUtils;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.List;

/**
 * Published chart modified action handler
 * <AUTHOR>
 */
public class PublicChartModifyActionHandler {

    /**
     * Private constructor to prevent instantiation
     */
    public PublicChartModifyActionHandler(){}

    /**
     * On Chart Edit action occurred. It can be either SheetChartEdit or Framework
     * @param activeSheet
     * @param action
     * @param actionJSON
     */
    public static void onChartEdit(Sheet activeSheet, int action, JSONObjectWrapper actionJSON, String resourceID, String documentOwner) {
        if(action == ActionConstants.FRAMEWORK_CHART_EDIT || action == ActionConstants.SHEET_CHART_EDIT ||
                action == ActionConstants.PASTE_CHART_STYLES || action == ActionConstants.RESET_CHART_STYLES) {
            JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartActionConstants.JSONConstants.CHART_JSON);
            ActiveCellPOJO activeCellPOJO = new ActiveCellPOJO();
            boolean isFromUndoRedo = actionJSON.has(ChartActionConstants.JSONConstants.FROM_UNDO) &&
                    actionJSON.getBoolean(ChartActionConstants.JSONConstants.FROM_UNDO);

            activeCellPOJO.constructFromJSON(actionJSON.getJSONObject(JSONConstants.CURRENT_ACTIVE_CELL));
            ChartUtils.forEach(chartJSONs, chartJSON -> {
                PublicModificationActionDataHolder bundle = new PublicModificationActionDataHolder(activeSheet,
                        action, (JSONObjectWrapper) chartJSON, resourceID, documentOwner, activeCellPOJO, isFromUndoRedo);

                handleChartEditAction(bundle);
            });
        }
    }

    /**
     * Handler method for chart edit action
     * @param dataHolder
     */
    private static void handleChartEditAction(PublicModificationActionDataHolder dataHolder) {
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);
        SheetMeta sheetMeta = chart.getSheetMeta();

        if(SheetChartAPI.isChartPublic(sheetMeta)) {
            String publicSpaceChartID = SheetChartAPI.getPublicSpaceChartID(sheetMeta);
            String publicSpace = PublicChartUtils.getPublicSpace(SheetChartAPI.getPublicChartType(sheetMeta), dataHolder.getDocumentOwner());

            PublicChartUtils.handleChartModification(dataHolder.getActiveSheet().getWorkbook(), chart.getChartID(), publicSpaceChartID, publicSpace);
            updateInDAO(chart, dataHolder.getResourceID(), dataHolder.getDocumentOwner());
        }
    }

    public static void onChartModifyNew(Workbook workbook, List<ModifiedChartPOJO> modifiedCharts, String resourceID, String documentOwner) {
        ChartContainer chartContainer = workbook.getChartsContainer();

        for(ModifiedChartPOJO bean: modifiedCharts) {
            if(!shouldUpdateInPublicSpace(bean)) { return; }
            Chart chart = chartContainer.getChart(bean.getModifiedChartId());
            SheetMeta sheetMeta = chart.getSheetMeta();

            if(SheetChartAPI.isChartPublic(sheetMeta)) {
                String publicSpaceChartID = SheetChartAPI.getPublicSpaceChartID(sheetMeta);
                String publicSpace = PublicChartUtils.getPublicSpace(SheetChartAPI.getPublicChartType(sheetMeta), documentOwner);

                PublicChartUtils.handleChartModification(workbook, chart.getChartID(), publicSpaceChartID, publicSpace);
                updateInDAO(chart, resourceID, documentOwner);
            }
        }
    }

    private static boolean shouldUpdateInPublicSpace(ModifiedChartPOJO bean) {
        int modificationConstant = bean.getModificationConstants();

        return bean.getModificationConstants() == ChartModificationConstants.ModificationActions.DATA_TABLE_MODIFIED ||
        modificationConstant == ChartModificationConstants.ModificationActions.CHART_SUBTITLE_MODIFIED ||
        modificationConstant == ChartModificationConstants.ModificationActions.CHART_TITLE_MODIFIED ||
        modificationConstant == ChartModificationConstants.ModificationActions.CHART_X_AXIS_TITLE_MODIFIED ||
        modificationConstant == ChartModificationConstants.ModificationActions.CHART_Y_AXIS_TITLE_MODIFIED;
    }

    public static void onChartModify(Workbook workbook, String resourceID, String documentOwner) {
        ChartContainer chartContainer = workbook.getChartsContainer();

        chartContainer.forEachChart((chart) -> {
            SheetMeta sheetMeta = chart.getSheetMeta();

            if(SheetChartAPI.isChartPublic(sheetMeta)) {
                String publicSpaceChartID = SheetChartAPI.getPublicSpaceChartID(sheetMeta);
                String publicSpace = PublicChartUtils.getPublicSpace(SheetChartAPI.getPublicChartType(sheetMeta), documentOwner);

                PublicChartUtils.handleChartModification(workbook, chart.getChartID(), publicSpaceChartID, publicSpace);
                updateInDAO(chart, resourceID, documentOwner);
            }
        });
    }

    private static void updateInDAO(Chart chart, String resourceID, String docOwner) {
        SheetMeta sheetMeta = chart.getSheetMeta();
        PublicChartType publicChartType = SheetChartAPI.getPublicChartType(sheetMeta);
        String publicSpace = PublicChartUtils.getPublicSpace(publicChartType, docOwner);
        ChartsPublicDAO publicDAO = ChartsPublicDAO.getDefaultPublicDAO(publicSpace);
        ModifiedPublicChartPOJO bean = new ModifiedPublicChartPOJO();

        bean.setChartID(chart.getChartID());
        bean.setModifiedTime(System.currentTimeMillis());
        bean.setPublicChartName(SheetChartAPI.getPublicChartName(chart.getSheetMeta()));
        bean.setResourceID(resourceID);
        bean.setAssociatedSheetName(chart.getAssociatedSheetName());

        publicDAO.updateChartModified(bean);
    }


}
