package com.zoho.sheet.knitcharts.recommendation;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.RangeUtil;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.DataJoinType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.sheetzia.ZSZiaEssentials;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.ZSZiaUtilities;
import com.zoho.sheet.zia2.interfaces.ZiaTableDataHolder;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;

import java.util.*;
import java.util.stream.Collectors;

public class ChartDataSourceTransformer {

    private final ZiaTableDataHolder tableDataHolder;

    private final ZSZiaHolder holder;

    private final InsightSuggestion suggestion;

    private  final List<Integer> columnIdx = new ArrayList<>();

    private final List<DataRange> dataRanges = new ArrayList<>();

    private final ZSZiaEssentials essentials;

    private final boolean isRequiresWholeTable;

    public ChartDataSourceTransformer(ZSZiaHolder holder, InsightSuggestion suggestion, boolean isRequiresWholeTable) {
        this.tableDataHolder = holder.getTableDataHolder();
        this.suggestion = suggestion;
        this.holder = holder;
        this.essentials = holder.getEssentials();
        this.isRequiresWholeTable = isRequiresWholeTable;
        process();
    }

    private void process() {
        // first adding date columns into data sources
        for(Integer dateCol: suggestion.getDateColumns()) {
            prepareDataSourceByIndex(dateCol);
            columnIdx.add(dateCol);
        }
        // second adding cat columns into data sources
        for(Integer catCol: suggestion.getCategoricalColumns()) {
            prepareDataSourceByIndex(catCol);
            columnIdx.add(catCol);
        }
        // lastly adding value columns into data sources
        for(Integer valueCol: suggestion.getValueColumns()) {
            prepareDataSourceByIndex(valueCol);
            columnIdx.add(valueCol);
        }

        columnIdx.sort(Integer::compareTo);
    }

    private void prepareDataSourceByIndex(int idx) {
        dataRanges.add(getDataRangeByIndex(idx));
    }

    private DataRange getDataRangeByIndex(int idx) {
        List<ZiaRange> ranges = tableDataHolder.getColumnDataHolderByIndex(idx).getColumnRange();

        return ZSZiaUtilities.toZSRange(holder.getEssentials().getSheet(), ranges.get(0)).toDataRange();
    }

    private int dataRangeComparator(DataRange o1, DataRange o2) {
        SeriesInType seriesInType = ChartRecommendationUtils.convertZiaToChartSeriesInType(essentials.getSeriesIn());
        if(seriesInType == SeriesInType.COLUMNS) {
            return Integer.compare(o1.getStartColIndex(), o2.getStartColIndex());
        } else {
            return Integer.compare(o1.getStartRowIndex(), o2.getStartRowIndex());
        }
    }

    public List<String> getDataSources(Sheet sheet) {
        if(suggestion.isAggregatedChartSuggestion() && isRequiresWholeTable) {
            return holder.getZiaParams()
                    .getTableRanges().stream()
                    .map(ziaRange ->ZSZiaUtilities.toZSRange(sheet, ziaRange).getRangeStringForClient())
                    .collect(Collectors.toList());
        }
        return RangeUtil.mergeDataRanges(dataRanges).stream()
                .sorted(this::dataRangeComparator)
                .map(dataRange -> dataRange.toRange(sheet.getWorkbook()).getRangeStringForClient())
                .collect(Collectors.toList());
    }

    public int getRelativeIndex(int actualIdx) {
        if(suggestion.isAggregatedChartSuggestion() && isRequiresWholeTable) {
            Sheet sheet = holder.getEssentials().getSheet();
            Workbook workbook = sheet.getWorkbook();
            SeriesInType seriesInType = ChartRecommendationUtils.convertZiaToChartSeriesInType(essentials.getSeriesIn());
            DataJoinType dataJoinType = ChartRecommendationUtils.convertZiaToChartDataJoinType(essentials.getAppendingWay());

            return ChartUtils.getRelativeIndex(workbook,
                    getDataSources(sheet), getDataRangeByIndex(actualIdx).getRangeStringForClient(workbook), seriesInType, dataJoinType, sheet.getName());
        }
        return columnIdx.indexOf(actualIdx);
    }
}
