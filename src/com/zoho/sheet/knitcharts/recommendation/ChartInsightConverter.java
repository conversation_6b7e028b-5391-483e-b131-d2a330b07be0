package com.zoho.sheet.knitcharts.recommendation;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.utils.*;
import com.zoho.sheet.knitcharts.chart.ProtoChart;
import com.zoho.sheet.knitcharts.chartsdatatable.CacheDataTablePool;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTable;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTableCell;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.DataType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.YAxisScaleType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.GroupingType;
import com.zoho.sheet.sheetzia.ZSZiaEssentials;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.table.ZiaSplitTableDataHolderImpl;
import com.zoho.sheet.zia2.utils.ZiaConstants;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class ChartInsightConverter {

    public static JSONObjectWrapper convert(CacheDataTablePool dataTablePool, ZSZiaHolder holder, InsightSuggestion suggestion, boolean isWholeTableRequired) {
        ChartActivityMonitor.ChartActivity activity = ObjectsSupplier.getInstance().getChartActivityMonitor()
                .getChartActivity("ZIA_INSIGHT_CONVERSION", null, null, null, null);        // NO I18N
        try {
            activity.start();
            ZSZiaEssentials essentials = holder.getEssentials();
            Sheet sheet = essentials.getSheet();
            Workbook workbook = sheet.getWorkbook();
            ProtoChart protoChart = new ProtoChart();
            ChartDataSourceTransformer transformer = new ChartDataSourceTransformer(holder, suggestion, isWholeTableRequired);

            protoChart.chartMeta = new ChartMeta();
            protoChart.sheetMeta = new SheetMeta();

            convertTitle(protoChart, suggestion);
            convertChartType(protoChart, suggestion);
            convertLegendNLogScale(protoChart, suggestion);
            convertAggregationData(protoChart, suggestion, transformer);
            convertFilterData(protoChart, holder, suggestion, transformer);

            DataTable dataTable = convertDataTableOptions(dataTablePool, protoChart, holder, sheet, transformer);

            constructDataMeta(workbook, protoChart, dataTable);

            dataTable.invalidateDataMetaCache();
            dataTable.invalidateManipulatedDataCache();
            dataTable.invalidateSeriesDataMetaCache();

            JSONObjectWrapper chartJSON = ChartsDataGenerator.getChartMeta(protoChart);
            activity.completed();

            return chartJSON;
        }catch (Exception e) {
            activity.failed(e);
            return null;
        }
    }

    private static void convertTitle(ProtoChart protoChart, InsightSuggestion suggestion) {
        String title = suggestion.getTitle();

        if(title != null && !title.isEmpty()) {
            FrameworkChartAPI.updateChartTitleStatus(protoChart.chartMeta, true);
            FrameworkChartAPI.updateChartTitle(protoChart.chartMeta, title);
        }
    }

    private static void convertChartType(ProtoChart protoChart, InsightSuggestion suggestion) {
        String chartType = suggestion.getVisualizationType();

        FrameworkChartAPI.updateChartType(protoChart.chartMeta, ChartUtils.getChartForOldChartName(chartType));
    }

    private static void convertLegendNLogScale(ProtoChart protoChart, InsightSuggestion suggestion) {

        if(suggestion.isLogScaleReq()) {
            FrameworkChartAPI.updateYAxisScaleTypeWithIndex(protoChart.chartMeta, YAxisScaleType.LOGARITHMIC, 0);
        }

        if(suggestion.isLegendReq()) {
            FrameworkChartAPI.updateLegendStatus(protoChart.chartMeta, true);
        }
    }


    private static DataTable convertDataTableOptions(CacheDataTablePool dataTablePool, ProtoChart protoChart, ZSZiaHolder holder, Sheet sheet, ChartDataSourceTransformer transformer) {
        ZSZiaEssentials essentials = holder.getEssentials();
        ChartType chartType = FrameworkChartGetterAPI.getChartType(protoChart.chartMeta);
        List<String> dataSources = transformer.getDataSources(sheet);

        SheetChartAPI.updateDataSources(protoChart.sheetMeta, dataSources);
        SheetChartAPI.updateSeriesIn(protoChart.sheetMeta, ChartRecommendationUtils.convertZiaToChartSeriesInType(essentials.getSeriesIn()));

        SheetChartAPI.updateDataJoinType(protoChart.sheetMeta, DataTableUtils.determineDataJoinType(sheet.getWorkbook(), dataSources, sheet.getAssociatedName()));

        DataTable table = dataTablePool.getCacheDataTable(sheet.getWorkbook(), SheetChartGetterAPI.getDataJoinType(protoChart.sheetMeta),
                dataSources, sheet.getAssociatedName(), !SheetChartGetterAPI.getIncludeHiddenCellsStatus(protoChart.sheetMeta)).getDataTable();

        List<List<DataTableCell>> tableCells = table.getOriginalData(sheet.getWorkbook(), protoChart.sheetMeta, chartType);
        if(tableCells.isEmpty()) { return table; }

        if(tableCells.get(0).size() > 1) {
            DataType fcType = DataUtils.getColumnDataType(tableCells, 0);
            SheetChartAPI.updateFirstColAsLabelStatus(protoChart.sheetMeta, DataUtils.chartTypeCanHaveLabel(chartType, fcType));
        }

        if(tableCells.size() > 1) {
            DataType frType = DataUtils.getRowDataType(tableCells, 0);
            SheetChartAPI.updateFirstRowAsLabelStatus(protoChart.sheetMeta, DataUtils.chartTypeCanHaveLabel(chartType, frType));
        }

        return table;
    }



    private static void constructDataMeta(Workbook workbook, ProtoChart protoChart, DataTable table) {
        protoChart.dataMeta = table.constructDataMeta(workbook, protoChart.sheetMeta, FrameworkChartGetterAPI.getChartType(protoChart.chartMeta));
    }

    private static void convertAggregationData(ProtoChart protoChart, InsightSuggestion suggestion, ChartDataSourceTransformer transformer) {
        if(suggestion.isAggregatedChartSuggestion()) {

            List<Integer> valueColumns = suggestion.getValueColumns().stream()
                    .map(transformer::getRelativeIndex)
                    .collect(Collectors.toList());

            convertGroupingData(protoChart, suggestion, transformer);

            FrameworkChartAPI.updateLegendStatus(protoChart.chartMeta, true);
            FrameworkChartAPI.updateSortStatus(protoChart.chartMeta, valueColumns.size() == 1, 0);
            SheetChartAPI.setAggregationValueColumns(protoChart.sheetMeta, valueColumns);
            SheetChartAPI.setAggregationType(protoChart.sheetMeta, ChartZiaUtils.getAggregationType(suggestion.getAggregatedOperation().get(0)));
        }
    }

    private static void convertGroupingData(ProtoChart protoChart, InsightSuggestion suggestion, ChartDataSourceTransformer transformer) {
        if(suggestion.getChartType() == ZiaConstants.ZiaChartType.DATE_AGGREGATED) {
            convertDateGroupingData(protoChart, suggestion, transformer);
        }else {
            convertCatGroupingData(protoChart, suggestion, transformer);
        }
    }

    private static void convertDateGroupingData(ProtoChart protoChart, InsightSuggestion suggestion, ChartDataSourceTransformer transformer) {
        List<Integer> catColumns = suggestion.getCategoricalColumns();

        SheetChartAPI.setAxisColumn(protoChart.sheetMeta, transformer.getRelativeIndex(suggestion.getDateColumns().get(0)));
        SheetChartAPI.setAxisGroupingType(protoChart.sheetMeta, ChartZiaUtils.getGroupingType(suggestion.getDateGrouping()));

        if(!catColumns.isEmpty()) {
            SheetChartAPI.setLegendColumn(protoChart.sheetMeta, transformer.getRelativeIndex(catColumns.get(0)));
            SheetChartAPI.setLegendGroupingType(protoChart.sheetMeta, GroupingType.DEFAULT);
        }
    }

    private static void convertCatGroupingData(ProtoChart protoChart, InsightSuggestion suggestion, ChartDataSourceTransformer transformer) {
        List<Integer> catColumns = suggestion.getCategoricalColumns();

        if(!catColumns.isEmpty()) {
            SheetChartAPI.setAxisColumn(protoChart.sheetMeta, transformer.getRelativeIndex(catColumns.get(0)));
            SheetChartAPI.setAxisGroupingType(protoChart.sheetMeta, GroupingType.DEFAULT);

            if (catColumns.size() > 1) {
                SheetChartAPI.setLegendColumn(protoChart.sheetMeta, transformer.getRelativeIndex(catColumns.get(1)));
                SheetChartAPI.setLegendGroupingType(protoChart.sheetMeta, GroupingType.DEFAULT);
            }
        }
    }

    private static void convertFilterData(ProtoChart protoChart, ZSZiaHolder holder, InsightSuggestion suggestion, ChartDataSourceTransformer transformer) {
        if(Objects.nonNull(suggestion.getFilter())) {
            ZiaSplitTableDataHolderImpl tableDataHolder = ChartUtils.typeCast(holder.getTableDataHolder());
            InsightSuggestion.ZiaFilter filter = suggestion.getFilter();

            SheetChartAPI.setFilterType(protoChart.sheetMeta, ChartZiaUtils.getFilterType(filter.getType()));
            SheetChartAPI.setFilterCount(protoChart.sheetMeta, filter.getLimit());
            SheetChartAPI.setFilterColumn(protoChart.sheetMeta,
                    getFilterColumn(suggestion, filter, tableDataHolder, transformer));
        }
    }

    private static int getFilterColumn(InsightSuggestion suggestion,
                                       InsightSuggestion.ZiaFilter filter, ZiaSplitTableDataHolderImpl tableDataHolder, ChartDataSourceTransformer transformer) {
        String limitBy = filter.getLimitBy();

        if(limitBy.isEmpty() || limitBy.equals("NONE")) {                                                                   // NO I18N
            return transformer.getRelativeIndex(suggestion.getValueColumns().get(0));
        } else {
            int colIdx = tableDataHolder.getColumnDataHolderByHeader(limitBy).getColumnIndex();
            return transformer.getRelativeIndex(colIdx);
        }
    }


}
