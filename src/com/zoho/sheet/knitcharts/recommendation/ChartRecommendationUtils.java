package com.zoho.sheet.knitcharts.recommendation;

import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Sheet;
import com.zoho.sheet.knitcharts.utils.ChartActivityMonitor;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.chartcache.ChartCacheInterceptor;
import com.zoho.sheet.knitcharts.chartsdatatable.CacheDataTablePool;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.DataJoinType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.sheetzia.ZSZiaEssentials;
import com.zoho.sheet.sheetzia.ZSZiaHolder;
import com.zoho.sheet.sheetzia.ZSZiaUtilities;
import com.zoho.sheet.sheetzia.ZiaExecutorServiceImpl;
import com.zoho.sheet.sheetzia.interfaces.ZiaExecutorService;
import com.zoho.sheet.sheetzia.tasks.TableDetectionTask;
import com.zoho.sheet.zia2.utils.ZiaParams;
import com.zoho.sheet.zia2.utils.ZiaRange;
import com.zoho.sheet.zia2.utils.ziabeans.insight.InsightSuggestion;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.*;
import java.util.stream.Collectors;

public final class ChartRecommendationUtils {

    public static List<String> toRange(Sheet sheet, List<ZiaRange> ziaRangeList) {

        return ZSZiaUtilities.toZSRange(sheet, ziaRangeList).stream()
                .map(Range::getRangeStringForClient)
                .collect(Collectors.toList());
    }

    public static List<ZiaRange> expandActiveCell(Sheet sheet, ZiaRange activeCell) {
        ChartActivityMonitor.ChartActivity chartActivity = ChartUtils.getChartActivity("CHART_RECOMMENDATION_ANALYSE", null, null, null, null);     // NO I18N
        try {
            ZiaParams params = new ZiaParams();
            params.setTableRanges(new ArrayList<>());
            ZSZiaEssentials essentials = new ZSZiaEssentials(sheet, activeCell);
            ZSZiaHolder holder = new ZSZiaHolder(params);
            ZiaExecutorService service = new ZiaExecutorServiceImpl();

            params.setTableRanges(Collections.emptyList());
            essentials.setTableDetectionRequired(true);
            holder.setEssentials(essentials);

            service.execute(new TableDetectionTask(holder));

            return params.getTableRanges();
        } catch (Exception e) {
            chartActivity.failed(e);
            return Collections.emptyList();
        }
    }

    public static boolean isSingleCell(List<Range> ranges) {
        if(ranges.size() > 1) { return false; }

        return ChartUtils.isSingleCell(ranges.get(0));
    }

    public static List<JSONObjectWrapper> convertToProtoCharts(Sheet sheet, ZSZiaHolder holder, List<InsightSuggestion> suggestions) {
        CacheDataTablePool dataTablePool = new CacheDataTablePool();
        return suggestions.stream()
                .sorted((o1, o2) -> Integer.compare(o2.getWeightage(), o1.getWeightage()))
                .limit(6)
                .map(suggestion -> ChartInsightConverter.convert(dataTablePool, holder, suggestion, true))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static DataJoinType convertZiaToChartDataJoinType(ZSZiaEssentials.AppendingWay appendingWay) {
        if(appendingWay == ZSZiaEssentials.AppendingWay.VERTICAL) {
            return DataJoinType.VERTICAL;
        }
        return DataJoinType.HORIZONTAL;
    }

    public static SeriesInType convertZiaToChartSeriesInType(ZSZiaEssentials.SeriesIn seriesIn) {
        if(seriesIn == ZSZiaEssentials.SeriesIn.COLS) {
            return SeriesInType.COLUMNS;
        }
        return SeriesInType.ROWS;
    }

    public static JSONArrayWrapper getChartInsertJSONFromCache(String userZUID, String rID, String asn, JSONArrayWrapper chartJSONs) {
        JSONArrayWrapper metaJSONs = new JSONArrayWrapper();
        ChartUtils.forEach(chartJSONs, chartJSONObj -> {
            JSONObjectWrapper chartJSON = ChartUtils.typeCast(chartJSONObj);

            String chartID = chartJSON.getString(ChartActionConstants.JSONConstants.CHART_ID);
            long timeStamp = chartJSON.getLong(ChartActionConstants.JSONConstants.RECOMMENDATION_CACHE_TIME_STAMP);
            JSONObjectWrapper recommendedChartMeta = ChartCacheInterceptor.getCachedRecommendedChart(userZUID, rID, asn, chartID, timeStamp);


            SheetMeta sheetMeta = new SheetMeta();
            sheetMeta.fromJSON(recommendedChartMeta.getJSONObject(ChartActionConstants.JSONConstants.SHEET_META));

            SheetChartAPI.updateStartColumn(sheetMeta, ChartUtils.optDoubleFromJSONObject(chartJSON, ChartActionConstants.JSONConstants.START_COLUMN, 0D));
            SheetChartAPI.updateStartRow(sheetMeta, ChartUtils.optDoubleFromJSONObject(chartJSON, ChartActionConstants.JSONConstants.START_ROW, 0D));
            SheetChartAPI.updateStartColumnDiff(sheetMeta, ChartUtils.optDoubleFromJSONObject(chartJSON, ChartActionConstants.JSONConstants.START_COLUMN_DIFF, 0D));
            SheetChartAPI.updateStartRowDiff(sheetMeta, ChartUtils.optDoubleFromJSONObject(chartJSON, ChartActionConstants.JSONConstants.START_ROW_DIFF, 0D));

            recommendedChartMeta.put(ChartActionConstants.JSONConstants.SHEET_META, sheetMeta.toJSON());
            metaJSONs.put(recommendedChartMeta);
        });

        ChartUtils.forEach(chartJSONs, chartJSONObj -> {
            JSONObjectWrapper chartJSON = ChartUtils.typeCast(chartJSONObj);

            long timeStamp = chartJSON.getLong(ChartActionConstants.JSONConstants.RECOMMENDATION_CACHE_TIME_STAMP);
            ChartCacheInterceptor.clearRecommendationCache(userZUID, rID, asn, timeStamp);
        });

        return metaJSONs;

    }

}
