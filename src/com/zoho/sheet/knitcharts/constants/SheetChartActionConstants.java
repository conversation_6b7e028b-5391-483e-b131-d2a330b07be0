package com.zoho.sheet.knitcharts.constants;

public final class SheetChartActionConstants {

    public static final class SubActionConstants{
        /* Used, when include hidden cells status got updated */
        public static final int IHC_STATUS_EDIT                                 = 1072;
        /* Used, when auto-expand status got updated */
        public static final int AUTO_EXPAND_STATUS_EDIT                         = 1073;
        /* Used, when remove nested ranges option updated */
        @Deprecated public static final int REMOVE_NESTED_RANGE_STATUS_EDIT     = 1076;
        /* Used, when series in option updated */
        public static final int SERIES_IN_EDIT                                  = 1077;
        /* Used, when first row as label option updated */
        public static final int FIRST_ROW_AS_LABEL_EDIT                         = 1078;
        /* Used, when first column as label option updated */
        public static final int FIRST_COL_AS_LABEL_EDIT                         = 1079;
        /* Used, when Chart is moved */
        public static final int CHART_MOVE                                      = 1080;
        /* Used, when Chart is resized */
        public static final int CHART_RESIZE                                    = 1081;
        /* Used, when data source is modified */
        public static final int DATA_SOURCES_EDIT                               = 1082;
        /* Used, when chart is moved between sheets */
        @Deprecated public static final int MOVE_TO_OTHER_SHEET                 = 1084;
        /* Used, when data join type changed for multiple data ranges */
        public static final int DATA_JOIN_TYPE_EDIT                             = 1087;
        // Used, when auto fill type changed in Race chart
        public static final int AUTO_FILL_TYPE_EDIT                             = 1089;
        /* Used, when aggregation check box enabled or disabled */
        public static final int AGGREGATION_STATUS_EDIT                         = 1091;
        /* Used, when aggregation options are reset */
        public static final int AGGREGATION_RESET                               = 1092;
        /* Used, when any of the aggregation options were edited */
        public static final int AGGREGATION_OPTION_EDIT                         = 1093;
        /* Used, when filter status changed */
        public static final int FILTER_STATUS_EDIT                              = 1094;
        /* Used, when filter reset */
        public static final int FILTER_OPTIONS_RESET                            = 1095;
        /* Used, when filter options edit */
        public static final int FILTER_OPTIONS_EDIT                             = 1096;
        /* Used, when color palette type changed */
        public static final int COLOR_PALETTE_TYPE_EDIT                         = 1097;
        /* Used, when color palette reverse option enabled */
        public static final int COLOR_PALETTE_REVERSE_STATUS_EDIT               = 1098;
        /* Used, when pivot chart filter box pin status changed */
        public static final int PIVOT_FILTER_PINNED_STATUS_EDIT                 = 1108;
        /* Used, when chart visibility changed */
        public static final int CHART_VISIBILITY_STATUS_EDIT                    = 1130;
        /* Used, when chart renamed */
        public static final int CHART_RENAME                                    = 1134;
        /* Used, when image series selection changed */
        public static final int IMAGE_SERIES_EDIT                               = 1160;
        /* Used, when info text status changed */
        public static final int INFO_TEXT_STATUS_EDIT                           = 1161;
        /* Used, when remove blank status changed */
        public static final int REMOVE_BLANK_STATUS_EDIT                        = 1182;
    }

    public static final class JSONConstants{

        public static final String DATA_SOURCES = "dataSources";                        // No I18N

        public static final String IS_ENABLED = "isEnabled";                            // NO I18N

        public static final String SERIES_IN = "seriesIn";                              // NO I18N

        public static final String DATA_META = "dataMeta";                              // NO I18N

        public static final String SHEET_DATA_META = "sDataMeta";                       // NO I18N

        public static final String X = "x";                                             // NO I18N

        public static final String Y = "y";                                             // NO I18N

        public static final String HEIGHT = "height";                                   // NO I18N

        public static final String WIDTH = "width";                                     // NO I18N

        public static final String TARGET_SHEET_ID = "targetSheetID";                   // NO I18N

        public static final String BORDER_COLOR = "borderColor";                        // NO I18N

        public static final String DATA_JOIN_TYPE = "dataJoinType";                     // NO I18N

        public static final String COLOR = "color";                                     // NO I18N

        public static final String VALUE = "value";                                     // NO I18N

        public static final String ACCENT = "accent";                                   // NO I18N

        public static final String TONE = "tone";                                       // NO I18N

        public static final String SHEET_META = "sheetMeta";                            // NO I18N

        public static final String DATA_TABLE_OPTIONS = "dataTableOptions";             // NO I18N

        public static final String DATA_MANIPULATION_OPTIONS = "dataManipulationOptions";   // NO I18N

        public static final String AGGREGATION_OPTIONS = "aggregationOptions";          // NO I18N

        public static final String FILTER_OPTIONS = "filterOptions";                    // NO I18N

        public static final String AGGREGATION_OPERATION = "aggregationOperation";      // NO I18N

        public static final String VALUE_COLUMNS = "valueColumns";                      // NO I18N

        public static final String AXIS_COLUMN = "axisColumn";                          // NO I18N

        public static final String AXIS_GROUPING_TYPE = "axisGroupingType";             // NO I18N

        public static final String LEGEND_COLUMN = "legendColumn";                      // NO I18N

        public static final String LEGEND_GROUPING_TYPE = "legendGroupingType";         // NO I18N

        public static final String FILTER_TYPE = "filterType";                          // NO I18N

        public static final String FILTER_COLUMN = "filterColumn";                      // NO I18N

        public static final String FILTER_COUNT = "filterCount";                        // NO I18N

        public static final String AUTO_FILL = "autoFill";                              // NO I18N

        public static final String SORT_OPTIONS = "sortOptions";                        // NO I18N

        public static final String SORT_ENABLED = "sortEnabled";                        // NO I18N

        public static final String SORT_BY_KEY = "sortByKey";                           // NO I18N

        public static final String SORT_ORDER = "sortOrder";                            // NO I18N

        public static final String PALETTE_NAME = "paletteName";                        // NO I18N

        public static final String CUSTOM_COLOR = "customColor";                        // NO I18N

        public static final String START_COLUMN = "sc";                                 // NO I18N

        public static final String START_ROW = "sr";                                    // NO I18N

        public static final String START_COLUMN_DIFF = "scd";                           // NO I18N

        public static final String START_ROW_DIFF = "srd";                              // NO I18N

        public static final String IS_CELL_LIMIT_EXCEEDING = "isCellLimitExceeding";    // NO I18N

        public static final String VISIBLE = "visible";                                 // NO I18N

        public static final String NAME = "name";                                       // NO I18N

        public static final String OLD_NAME = "oldName";                                // NO I18N

        public static final String SERIES_INDEX = "seriesIndex";                        // NO I18N

        public static final String BORDER_WIDTH = "borderWidth";                        // NO I18N

        public static final String BORDER_STYLE_TYPE = "borderStyleType";               // NO I18N

        public static final String OPACITY = "opacity";                                 // NO I18N

    }


    public static final class Constants{

        public static final int DEFAULT_IMAGE_SERIES_INDEX = -1;

    }

}
