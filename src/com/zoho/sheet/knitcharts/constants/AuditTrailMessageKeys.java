package com.zoho.sheet.knitcharts.constants;

public final class AuditTrailMessageKeys {

    public static final String CHART_INSERT = "AT.NewChart";                                                                               // NO I18N

    public static final String CHART_DELETE = "AT.DeleteChart";                                                                            // NO I18N

    public static final String CHART_EDIT = "AT.EditChart";                                                                                // NO I18N

    public static final String CHART_PUBLISH = "AT.PublishChart";                                                                          // NO I18N

    public static final String CHART_UNPUBLISH = "AT.UnpublishChart";                                                                      // NO I18N

    public static final String PASTE_CHART_STYLES = "AT.PasteChartStyle";                                                                  // NO I18N

    public static final String RESET_CHART_STYLES = "AT.resetChartStyles";                                                                 // NO I18N

    public static final String CHART_PASTE = "AT.Chart.Pasted";                                                                            // NO I18N

    /*************** SHEET CHART EDIT ACTION **************/

    public static final String CHART_MOVE_TO_OTHER_SHEET = "AT.Chart.ChartMoveToOtherSheet";                                                          // NO I18N

    public static final String CHART_MOVE = "AT.MoveChart";                                                                                // NO I18N

    public static final String CHART_RESIZE = "AT.ResizeChart";                                                                            // NO I18N

    public static final String CHANGED_CHART_AUTO_EXPAND = "AT.changedChartAutoExpand";                                                    // NO I18N

    public static final String CHANGED_IHC_STATUS = "AT.Charts.changedIHCStatus";                                                          // NO I18N

    public static final String CHANGED_SERIES_IN = "AT.Charts.changedSeriesIn";                                                            // NO I18N

    public static final String CHANGED_FCL_STATUS = "AT.Charts.changedFCLStatus";                                                          // NO I18N

    public static final String CHANGED_FRL_STATUS = "AT.Charts.changedFRLStatus";                                                          // NO I18N

    public static final String CHANGED_DATA_RANGE = "AT.Charts.changedDataRange";                                                          // NO I18N

    /*************** FRAMEWORK CHART EDIT ACTION **************/

    public static final String CHANGED_DATALABELS_POSITION = "AT.changedDataLabelsPosition";                                               // NO I18N

    public static final String CHANGED_CHART_TYPE = "AT.chandedChartType";                                                                 // NO I18N

    public static final String CHANGED_CHART_ANIMATION = "AT.changedChartAnimation";                                                       // NO I18N

    public static final String CHANGED_CHART_TOOLTIP = "AT.changedChartTooltip";                                                           // NO I18N

    public static final String AXIS_INVERTED = "AT.inverted";                                                                              // NO I18N

    public static final String CHANGED_DATA_LABEL_TOTAL = "AT.changedDataLabelTotal";                                                      // NO I18N

    public static final String CHANGED_DATA_LABEL_TOTAL_STYLE = "AT.changedDataLabelTotalStyle";                                           // NO I18N

    public static final String CHANGED_CHART_BORDER_COLOR = "AT.changedChartBorderColor";                                                  // NO I18N

    public static final String CHANGED_CHART_BORDER_RADIUS = "AT.Chart.chartBorderRadiusChanged";                                          // NO I18N

    public static final String CHANGED_CHART_BACKGROUND_COLOR = "AT.changedChartBackgroundColor";                                          // NO I18N

    public static final String CHANGED_LEGEND_STYLE = "AT.changedLegendStyle";                                                             // NO I18N

    public static final String CHANGED_CHART_TITLE = "AT.changedChartTitle";                                                               // NO I18N

    public static final String CHANGED_CHART_TITLE_STYLE = "AT.changedChartTitleStyle";                                                    // NO I18N

    public static final String CHANGED_CHART_SUBTITLE = "AT.changedChartSubtitle";                                                         // NO I18N

    public static final String CHANGED_CHART_SUBTITLE_STYLE = "AT.changedChartSubtitleStyle";                                              // NO I18N

    public static final String CHANGED_CHART_X_TITLE = "AT.changedChartXAxisTitle";                                                        // NO I18N

    public static final String CHANGED_CHART_Y_TITLE = "AT.changedChartYAxisTitle";                                                        // NO I18N

    public static final String CHANGED_CHART_X_TITLE_STYLE = "AT.changedChartXAxisTitleStyle";                                             // NO I18N

    public static final String CHANGED_CHART_Y_TITLE_STYLE = "AT.changedChartYAxisTitleStyle";                                             // NO I18N

    public static final String CHANGED_THRESHOLD_VALUE = "AT.changedThresholdValue";                                                       // NO I18N

    public static final String CHANGED_THRESHOLD_COLOR = "AT.changedThresholdColor";                                                       // NO I18N

    public static final String CHANGED_THREHOLD_OPACITY = "AT.Chart.thresholdOpacityChanged";                                              // NO I18N

    public static final String CHANGED_MARKER_ENABLED = "AT.changedMarkerEnabled";                                                         // NO I18N

    public static final String CHANGED_MARKER_SYMBOL = "AT.changedMarkerSymbol";                                                           // NO I18N

    public static final String CHANGED_MARKER_SIZE = "AT.changedMarkerSize";                                                               // NO I18N

    public static final String CHANGED_MARKER_FILL_COLOR = "AT.Charts.changedMarkerFillColor";                                                    // NO I18N

    public static final String CHANGED_MARKER_BORDER_COLOR = "AT.Charts.changedMarkerBorderColor";                                                // NO I18N

    public static final String CHANGED_TRENDLINE_TYPE = "AT.changedChartTrendlineType";                                                    // NO I18N

    public static final String CHANGED_TRENDLINE_ORDER = "AT.changedChartTrendlineOrder";                                                  // NO I18N

    public static final String CHANGED_CHART_X_AXIS_LABEL_STYLE = "AT.changedChartXAxisLabelsStyle";                                       // NO I18N

    public static final String CHANGED_CHART_Y_AXIS_LABEL_STYLE = "AT.changedChartYAxisLabelsStyle";                                       // NO I18N

    public static final String CHANGED_CHART_LOGARITHMIC_SCALE = "AT.changedChartLogarithmicScale";                                        // NO I18N

    public static final String CHANGED_CHART_BASE_VALUE = "AT.changedChartBaseValue";                                                      // NO I18N

    public static final String CHANGED_CHART_PLOTLINE_TITLE_STYLE = "AT.changedChartPlotLineTitleStyle";                                   // NO I18N

    public static final String CHANGED_CHART_Y_AXIS_INTERVAL = "AT.changedChartYAxisInterval";                                             // NO I18N

    public static final String CHANGED_CHART_Y_AXIS_MIN = "AT.changedChartYAxisMin";                                                       // NO I18N

    public static final String CHANGED_CHART_Y_AXIS_MAX = "AT.changedChartYAxisMax";                                                       // NO I18N

    public static final String CHANGED_CHART_Y_AXIS_SCALE_FACTOR = "AT.changedChartYAxiesScaleFactor";                                     // NO I18N

    public static final String CHANGED_CHART_GRADIENT = "AT.changedChartGradient";                                                         // NO I18N

    public static final String CHANGED_START_ANGLE = "AT.changedStartAngle";                                                               // NO I18N

    public static final String CHANGED_END_ANGLE = "AT.changedEndAngle";                                                                   // NO I18N

    public static final String CHANGED_CHART_BAR_COUNT = "AT.changedChartBarCount";                                                        // NO I18N

    public static final String CHANGED_CHART_SUM_OPTION = "AT.changedChartSumOption";                                                      // NO I18N

    public static final String CHANGED_SERIES_COLOR = "AT.changedSeriesColor";                                                             // NO I18N

    public static final String CHANGED_SERIES_BORDER_COLOR = "AT.changedSeriesBorderColor";                                                // NO I18N

    public static final String CHANGED_CAPTION_STATUS = "AT.Charts.changedCaptionStatus";                                                  // NO I18N

    public static final String CHANGED_CAPTION_STYLE = "AT.Charts.changedCaptionStyle";                                                    // NO I18N

    public static final String CHANGED_TRENDLINE_STATUS = "AT.Charts.changedTrendlineStatus";                                              // NO I18N

    public static final String CHANGED_TRENDLINE_STYLE_TYPE = "AT.Charts.changedTrendlineStyleType";                                       // NO I18N

    public static final String CHANGED_TRENDLINE_COLOR = "AT.Charts.changedTrendlineColor";                                                // NO I18N

    public static final String CHANGED_TRENDLINE_OPACITY = "AT.Charts.changedTrendlineOpacity";                                            // NO I18N

    public static final String X_AXIS_INVERTER = "AT.Charts.xAxisInverted";                                                                // NO I18N

    public static final String CHANGED_LINE_STYLE = "AT.Charts.changedLineStyle";                                                          // NO I18N

    public static final String CHANGED_AREA_OPACITY = "AT.Charts.changedAreaOpacity";                                                      // NO I18N

    public static final String CHANGED_SERIES_TRANSPARENCY = "AT.Charts.changedSeriesTransparency";                                        // NO I18N

    public static final String CHANGED_SERIES_BORDER_OPACITY = "AT.Chart.seriesBorderOpacityChanged";                                      // NO I18N

    public static final String CHANGED_DATA_PROPERTY_BORDER_OPACITY = "AT.Chart.dataPropertyBorderOpacityChanged";                         // NO I18N

    public static final String CHANGED_SERIES_BORDER_FILL_TYPE = "AT.Chart.seriesBorderFillTypeChanged";                                   // NO I18N

    public static final String CHANGED_DATA_PROPERTY_BORDER_FILL_TYPE = "AT.Chart.dataPropertyBorderFillTypeChanged";                      // NO I18N

    public static final String CHANGED_AXIS_LABELS_STATUS = "AT.Charts.changedAxisLabelsStatus";                                           // NO I18N

    public static final String CHANGED_PLOTLINE_STYLE_TYPE = "AT.Charts.changedPlotlineStyleType";                                         // NO I18N

    public static final String CHANGED_PLOTLINE_COLOR = "AT.Charts.changedPlotlineColor";                                                  // NO I18N

    public static final String CHANGED_MAJOR_GRIDLINE_STATUS = "AT.Charts.changedMajorGridlineStatus";                                     // NO I18N

    public static final String CHANGED_MAJOR_GRIDLINE_STYLE_TYPE = "AT.Charts.changedMajorGridlineStyleType";                              // NO I18N

    public static final String CHANGED_MAJOR_GRIDLINE_COLOR = "AT.Charts.changedMajorGridlineColor";                                       // NO I18N

    public static final String CHANGED_MAJOR_GRIDLINE_COUNT = "AT.Charts.changedMajorGridlineCount";                                       // NO I18N

    public static final String CHANGED_MINOR_GRIDLINE_STATUS = "AT.Charts.changedMinorGridlineStatus";                                     // NO I18N

    public static final String CHANGED_MINOR_GRIDLINE_STYLE_TYPE = "AT.Charts.changedMinorGridlineStyleType";                              // NO I18N

    public static final String CHANGED_MINOR_GRIDLINE_COLOR = "AT.Charts.changedMinorGridlineColor";                                       // NO I18N

    public static final String CHANGED_TARGET_COLOR = "AT.Charts.changedTargetColor";                                                      // NO I18N

    public static final String CHANGED_UP_COLOR = "AT.Charts.changedUpColor";                                                              // NO I18N

    public static final String CHANGED_DOWN_COLOR = "AT.Charts.changedDownColor";                                                          // NO I18N

    public static final String CHANGED_SORT_OPTION = "AT.Charts.changedSortOption";                                                        // NO I18N

    public static final String CHANGED_SORT_STATUS = "AT.Charts.changedSortStatus";                                                        // NO I18N

    public static final String CHANGED_DECIMAL_PLACES = "AT.Charts.changedDecimalPlaces";                                                  // NO I18N

    public static final String CHANGED_BINNING_INTERVAL = "AT.Charts.changedBinningInterval";                                              // NO I18N

    public static final String CHANGED_X_BASELINE_COLOR = "AT.Charts.changedXBaselineColor";                                               // NO I18N

    public static final String CHANGED_DP_COLOR = "AT.Charts.changedDPColor";                                                              // NO I18N

    public static final String CHANGED_DP_BORDER_COLOR = "AT.Charts.changedDPBorderColor";                                                 // NO I18N

    public static final String CHANGED_DP_OPACITY = "AT.Charts.changedDPOpacity";                                                          // NO I18N

    public static final String CHANGED_DP_MARKER_SHAPE = "AT.Charts.changedDPMarkerShape";                                                 // NO I18N

    public static final String CHANGED_DP_MARKER_SIZE = "AT.Charts.changedDPMarkerSize";                                                   // NO I18N

    public static final String CHANGED_TITLE_STATUS = "AT.Charts.changedTitleStatus";                                                      // NO I18N

    public static final String CHANGED_TITLE_POSITION = "AT.Charts.changedTitlePosition";                                                  // NO I18N

    public static final String CHANGED_SUBTITLE_STATUS = "AT.Charts.changedSubtitleStatus";                                                // NO I18N

    public static final String CHANGED_SUBTITLE_POSITION = "AT.Charts.changedSubtitlePosition";                                            // NO I18N

    public static final String CHANGED_AXIS_TITLE_STATUS = "AT.Charts.changedAxisTitleStatus";                                             // NO I18N

    public static final String CHANGED_LEGEND_STATUS = "AT.Charts.changedLegendStatus";                                                    // NO I18N

    public static final String CHANGED_LEGEND_POSITION = "AT.Charts.changedLegendPosition";                                                // NO I18N

    public static final String CHANGED_DATALABELS_STATUS = "AT.Charts.changedDatalabelsStatus";                                            // NO I18N

    public static final String CHANGED_DATALABELS_FORMAT = "AT.Charts.changedDatalabelsFormat";                                            // NO I18N

    public static final String CREATED_PLOTLINE = "AT.Charts.createdPlotline";                                                             // NO I18N

    public static final String CHANGED_PLOTLINE_LABEL = "AT.Charts.changedPlotlineLabel";                                                  // NO I18N

    public static final String CHANGED_PLOTLINE_VALUE = "AT.Charts.changedPlotlineValue";                                                  // NO I18N

    public static final String REMOVED_A_PLOTLINE = "AT.Charts.removedAPlotline";                                                          // NO I18N

    public static final String REMOVED_PLOTLINES = "AT.Charts.removedPlotlines";                                                           // NO I18N

    public static final String DISABLED_THRESHOLD = "AT.Charts.disabledThreshold";                                                         // NO I18N

    public static final String CHANGED_PREFIX = "AT.Charts.changedPrefix";                                                                 // NO I18N

    public static final String CHANGED_SUFFIX = "AT.Charts.changedSuffix";                                                                 // NO I18N

    public static final String CHANGED_FONT_STYLE = "AT.Charts.changedFontStyle";                                                          // NO I18N

    public static final String CHANGED_X_LABELS_SLANT = "AT.Charts.changedXLabelsSlant";                                                   // NO I18N

    public static final String CHANGED_X_LABELS_STAGGER_LINES = "AT.Charts.changedXLabelsStaggerlines";                                    // NO I18N

    public static final String CLONE_CHART = "AT.CloneChart";                                                                              // NO I18N

    public static final String CHANGED_PLOT_SERIES_OPTION = "AT.Charts.changedPlotSeriesOption";                                           // NO I18N

    public static final String CHANGED_AUTO_FILL_OPTION = "AT.Charts.changedAutoFillOption";                                               // NO I18N

    public static final String CHANGED_CHART_BG_COLOR_OPACITY = "AT.Charts.changedChartBGOpacity";                                         // NO I18N

    public static final String CHANGED_GROUP_HEADER_STATUS = "AT.Charts.groupHeaderStatusEdit";                                            // NO I18N

    public static final String CHANGED_OUTLIERS_STATUS = "AT.Charts.outliersStatusEdit";                                                   // NO I18N

    public static final String CHANGED_SERIES_OUTLIERS_COLOR = "AT.Charts.outliersColorEdit";                                              // NO I18N

    public static final String CHANGED_SERIES_MEAN_COLOR = "AT.Charts.outliersMeanColorEdit";                                              // NO I18N

    public static final String CHANGED_SERIES_MEDIAN_COLOR = "AT.Charts.medianColorEdit";                                                  // NO I18N

    public static final String CHANGED_SERIES_WHISKERS_COLOR = "AT.Charts.whiskersColorEdit";                                              // NO I18N

    public static final String CHANGED_SERIES_POINT_PADDING = "AT.Charts.pointPaddingEdit";                                                // NO I18N

    public static final String CHANGED_SERIES_GROUPING_PADDING = "AT.Chart.groupPaddingEdit";                                              // NO I18N

    public static final String CHANGED_CONNECT_NULLS = "AT.Chart.connectNullsEdit";                                                        // NO I18N

    public static final String CHANGED_X_BASE_LINE_WIDTH = "AT.Chart.xBaseLineWidthEdit";                                                  // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_STATUS = "AT.Chart.dataPropDataLabelStatusEdit";                                 // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_FORMAT = "AT.Chart.dataPropsDataLabelFormatEdit";                                // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_POSITION = "AT.Chart.dataPropDataLabelPositionEdit";                             // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_FONT_COLOR = "AT.Chart.dataPropDataLabelFontColorEdit";                          // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_BG_COLOR = "AT.Chart.dataPropDataLabelsBgColorEdit";                             // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_BORDER_COLOR = "AT.Chart.dataPropDataLabelsBorderColorEdit";                     // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_BORDER_RADIUS = "AT.Chart.dataPropDataLabelsBorderRadiusEdit";                   // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_BORDER_WIDTH = "AT.Chart.dataPropDataLabelsBorderWidthEdit";                     // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_BG_OPACITY = "AT.Chart.DataPropDataLabelsBgOpacityEdit";                         // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_FONT_STYLE = "AT.Chart.dataPropDataLabelFontStyleEdit";                          // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_FONT_SIZE = "AT.Chart.dataPropDataLabelFontSizeEdit";                            // NO I18N

    public static final String CHANGED_DATAPROP_DATALABEL_FONT_WEIGHT = "AT.Chart.dataPropDataLabelFontWeightEdit";                        // NO I18N

    public static final String CHANGED_X_NUMBER_FORMAT_PREFIX = "AT.Chart.xNumberFormatPrefixEdit";                                        // NO I18N

    public static final String CHANGED_X_NUMBER_FORMAT_SUFFIX = "AT.Chart.xNumberFormatSuffixEdit";                                        // NO I18N

    public static final String AGGREGATION_ENABLED = "AT.Chart.aggregationEnabled";                                                        // NO I18N

    public static final String AGGREGATION_DISABLED = "AT.Chart.aggregationDisabled";                                                      // NO I18N

    public static final String AGGREGATION_RESET = "AT.Chart.aggregationReset";                                                            // NO I18N

    public static final String CHANGED_AGG_OPERATION = "AT.Chart.aggregationOperationEdit";                                                // NO I18N

    public static final String CHANGED_AGG_VALUE_COL = "AT.Chart.aggregationValueColEdit";                                                 // NO I18N

    public static final String CHANGED_AGG_AXIS_COL = "AT.Chart.aggregationAxisColEdit";                                                    // NO I18N

    public static final String CHANGED_AGG_AXIS_GRP = "AT.Chart.aggregationAxisGrpEdit";                                                   // NO I18N

    public static final String CHANGED_AGG_LEGEND_COL = "AT.Chart.aggregationLegendColEdit";                                               // NO I18N

    public static final String CHANGED_AGG_LEGEND_GRP = "AT.Chart.aggregationLegendGrpEdit";                                               // NO I18N

    public static final String FILTER_ENABLED = "AT.Chart.filterEnabled";                                                                  // NO I18N

    public static final String FILTER_DISABLED = "AT.Chart.filterDisabled";                                                                // NO I18N

    public static final String FILTER_RESET = "AT.Chart.filterReset";                                                                      // NO I18N

    public static final String CHANGED_FILTER_TYPE = "AT.Chart.filterTypeEdit";                                                            // NO I18N

    public static final String CHANGED_FILTER_COUNT = "AT.Chart.filterCountEdit";                                                          // NO I18N

    public static final String CHANGED_FILTER_COL = "AT.Chart.filterColEdit";                                                              // NO I18N

    public static final String PIVOT_FILTER_PINNED = "AT.Chart.pivotFilterPinned";                                                         // NO I18N

    public static final String PIVOT_FILTER_UNPINNED = "AT.Chart.pivotFilterUnPinned";                                                     // NO I18N

    public static final String COLOR_SCHEME_CHANGED = "AT.Chart.colorSchemeChanged";                                                       // NO I18N

    public static final String COLOR_SCHEME_REVERSED = "AT.Chart.colorSchemeReversed";                                                     // NO I18N

    public static final String COLOR_SCHEME_UNREVERSED = "AT.Chart.colorSchemeUnReversed";                                                 // NO I18N

    public static final String X_AXIS_MAJOR_TICK_ENABLED = "AT.Chart.xAxisMajorTickEnabled";                                               // NO I18N

    public static final String X_AXIS_MAJOR_TICK_DISABLED = "AT.Chart.xAxisMajorTickDisabled";                                             // NO I18N

    public static final String X_AXIS_MAJOR_TICK_LENGTH_CHANGED = "AT.Chart.xAxisMajorTickLenChanged";                                     // NO I18N

    public static final String X_AXIS_MAJOR_TICK_WIDTH_CHANGED = "AT.Chart.xAxisMajorTickWidthChanged";                                    // NO I18N

    public static final String X_AXIS_MAJOR_TICK_COLOR_CHANGED = "AT.Chart.xAxisMajorTickColorChanged";                                    // NO I18N

    public static final String X_AXIS_MINOR_TICK_ENABLED = "AT.Chart.xAxisMinorTickEnabled";                                               // NO I18N

    public static final String X_AXIS_MINOR_TICK_DISABLED = "AT.Chart.xAxisMinorTickDisabled";                                             // NO I18N

    public static final String X_AXIS_MINOR_TICK_LENGTH_CHANGED = "AT.Chart.xAxisMinorTickLenChanged";                                     // NO I18N

    public static final String X_AXIS_MINOR_TICK_WIDTH_CHANGED = "AT.Chart.xAxisMinorTickWidthChanged";                                    // NO I18N

    public static final String X_AXIS_MINOR_TICK_COLOR_CHANGED = "AT.Chart.xAxisMinorTickColorChanged";                                    // NO I18N

    public static final String Y_AXIS_MAJOR_TICK_ENABLED = "AT.Chart.yAxisMajorTickEnabled";                                               // NO I18N

    public static final String Y_AXIS_MAJOR_TICK_DISABLED = "AT.Chart.yAxisMajorTickDisabled";                                             // NO I18N

    public static final String Y_AXIS_MAJOR_TICK_LENGTH_CHANGED = "AT.Chart.yAxisMajorTickLenUpdated";                                     // NO I18N

    public static final String Y_AXIS_MAJOR_TICK_WIDTH_CHANGED = "AT.Chart.yAxisMajorTickWidthUpdated";                                    // NO I18N

    public static final String Y_AXIS_MAJOR_TICK_COLOR_CHANGED = "AT.Chart.yAxisMajorTickColorUpdated";                                    // NO I18N

    public static final String Y_AXIS_MINOR_TICK_ENABLED = "AT.Chart.yAxisMinorTickEnabled";                                               // NO I18N

    public static final String Y_AXIS_MINOR_TICK_DISABLED = "AT.Chart.yAxisMinorTickDisabled";                                             // NO I18N

    public static final String Y_AXIS_MINOR_TICK_LENGTH_CHANGED = "AT.Chart.yAxisMinorTickLenChanged";                                     // NO I18N

    public static final String Y_AXIS_MINOR_TICK_WIDTH_CHANGED = "AT.Chart.yAxisMinorTickWidthChange";                                     // NO I18N

    public static final String Y_AXIS_MINOR_TICK_COLOR_CHANGED = "AT.Chart.yAxisMinorTickColorChanged";                                    // NO I18N

    public static final String SERIES_AREA_LINE_TYPE_CHANGED = "AT.Chart.seriesAreaLineTypeChanged";                                       // NO I18N

    public static final String SERIES_INNER_RADIUS_CHANGED = "AT.Chart.seriesInnerRadius";                                                 // NO I18N

    public static final String SERIES_GRADIENT_DISABLED = "AT.Chart.seriesGradientDisabled";                                               // NO I18N

    public static final String SERIES_HIDE_IN_LEGEND = "AT.Chart.seriesNameHideInLegend";                                                  // NO I18N

    public static final String SERIES_SHOW_IN_LEGEND = "AT.Chart.seriesNameShowInLegend";                                                  // NO I18N

    public static final String CHART_VISIBLE = "AT.Chart.chartVisible";                                                                    // NO I18N

    public static final String CHART_HIDDEN = "AT.Chart.chartHidden";                                                                      // NO I18N

    public static final String Y_AXIS_AFFIXES_CHANGED = "AT.Chart.yAxisAffixesChanged";                                                    // NO I18N

    public static final String CENTER_LABEL_SET_TO_DEFAULT = "AT.Chart.centerLabelSetToDefault";                                           // NO I18N

    public static final String CENTER_LABEL_SET_TO_CUSTOM = "AT.Chart.centerLabelSetToCustom";                                             // NO I18N

    public static final String CENTER_LABEL_TEXT_UPDATED = "AT.Chart.centerLabelTextUpdated";                                              // NO I18N

    public static final String CHART_RENAMED = "AT.Chart.chartRenamed";                                                                    // NO I18N

    public static final String DELETED_MULTI_CHART = "AT.Chart.deletedMultiChart";                                                         // NO I18N

    public static final String MULTI_CHART_VISIBLE = "AT.Chart.multiChartVisible";                                                         // NO I18N

    public static final String MULTI_CHART_HIDE = "AT.Chart.multiChartHidden";                                                             // NO I18N

    public static final String ENABLE_IMAGE_SERIES = "AT.Chart.enableImageSeries";                                                         // NO I18N

    public static final String DISABLE_IMAGE_SERIES = "AT.Chart.disableImageSeries";                                                       // NO I18N

    public static final String ENABLE_IMAGE_CAPTION = "AT.Chart.enableImageCaption";                                                       // NO I18N

    public static final String DISABLE_IMAGE_CAPTION = "AT.Chart.disableImageCaption";                                                     // NO I18N

    public static final String IMAGE_POSITION_BAR = "AT.Chart.imagePositionBar";                                                           // NO I18N

    public static final String IMAGE_POSITION_AXIS = "AT.Chart.imagePositionAxis";                                                         // NO I18N

    public static final String IMAGE_SERIES_SHAPE_RECT = "AT.Chart.seriesImageShapeRect";                                                  // NO I18N

    public static final String IMAGE_SERIES_SHAPE_ROUND = "AT.Chart.seriesImageShapeRound";                                                // NO I18N

    public static final String IMAGE_CAPTION_SHAPE_RECT = "AT.Chart.captionImageShapeRect";                                                // NO I18N

    public static final String IMAGE_CAPTION_SHAPE_ROUND = "AT.Chart.captionImageShapeRound";                                              // NO I18N

    public static final String SERIES_SHADOW_DISABLED = "AT.Chart.shadowDisabledForSeries";                                                // NO I18N

    public static final String SERIES_SHADOW_ENABLED = "AT.Chart.shadowEnabledForSeries";                                                  // NO I18N

    public static final String SERIES_SHADOW_OPACITY_EDIT = "AT.Chart.seriesShadowOpacityEdit";                                            // NO I18N

    public static final String SERIES_SHADOW_COLOR_EDIT = "AT.Chart.seriesShadowColorEdit";                                                // NO I18N

    public static final String SERIES_DATA_LABELS_BG_COLOR_EDIT = "AT.Chart.seriesDataLabelsBgColorEdit";                                  // NO I18N

    public static final String SERIES_DATA_LABELS_BG_OPACITY_EDIT = "AT.Chart.seriesDataLabelsBgColorOpacityEdit";                         // NO I18N

    public static final String SERIES_DATA_LABELS_BORDER_COLOR_EDIT = "AT.Chart.seriesDataLabelsBorderColorEdit";                          // NO I18N

    public static final String SERIES_DATA_LABELS_BORDER_WIDTH_EDIT = "AT.Chart.seriesDataLabelsBorderWidthEdit";                          // NO I18N

    public static final String SERIES_DATA_LABELS_BORDER_RADIUS_EDIT = "AT.Chart.seriesDataLabelsBorderRadiusEdit";                        // NO I18N

    public static final String SERIES_DATA_LABELS_FONT_SIZE_EDIT = "AT.Chart.seriesDataLabelsFontSizeEdit";                                // NO I18N

    public static final String SERIES_DATA_LABELS_FONT_COLOR_EDIT = "AT.Chart.seriesDataLabelsFontColorEdit";                              // NO I18N

    public static final String SERIES_DATA_LABELS_FONT_STYLE_EDIT = "AT.Chart.seriesDataLabelsFontStyleEdit";                              // NO I18N

    public static final String SERIES_DATA_LABELS_FONT_WEIGHT_EDIT = "AT.Chart.seriesDataLabelsFontWeightEdit";                            // NO I18N

    public static final String SERIES_BORDER_RADIUS_EDIT = "AT.Chart.seriesBorderRadiusEdit";                                              // NO I18N

    public static final String SERIES_SLIDER_LABELS_ENABLED = "AT.Chart.seriesSliderLabelsEnabled";                                        // NO I18N

    public static final String SERIES_SLIDER_LABELS_DISABLED = "AT.Chart.seriesSliderLabelsDisabled";                                      // NO I18N

    public static final String SERIES_SLIDER_LABELS_FONT_SIZE_EDIT = "AT.Chart.seriesSliderLabelsFontSizeEdit";                            // NO I18N

    public static final String SERIES_SLIDER_LABELS_FONT_COLOR_EDIT = "AT.Chart.seriesSliderLabelsFontColorEdit";                          // NO I18N

    public static final String SERIES_SLIDER_LABELS_FONT_STYLE_EDIT = "AT.Chart.seriesSliderLabelsFontStyleEdit";                          // NO I18N

    public static final String SERIES_SLIDER_LABELS_FONT_WEIGHT_EDIT = "AT.Chart.seriesSliderLabelsFontWeightEdit";                        // NO I18N

    public static final String DATAPROPS_MARKER_STATUS_ENABLED = "AT.Chart.dataPropsMarkerStatusEnabled";                                  // NO I18N

    public static final String DATAPROPS_MARKER_STATUS_DISABLED = "AT.Chart.dataPropsMarkerStatusDisabled";                                // NO I18N

    public static final String DATAPROPS_MARKER_COLOR_EDIT = "AT.Chart.dataPropsMarkerColorEdit";                                          // NO I18N

    public static final String DATAPROPS_MARKER_BORDER_COLOR_EDIT = "AT.Chart.dataPropsMarkerBorderColorEdit";                             // NO I18N

    public static final String DATAPROPS_SHADOW_DISABLED = "AT.Chart.dataPropsShadowStatusDisabled";                                       // NO I18N

    public static final String DATAPROPS_SHADOW_ENABLED = "AT.Chart.dataPropsShadowStatusEnabled";                                         // NO I18N

    public static final String DATAPROPS_SHADOW_WIDTH_EDIT= "AT.Chart.dataPropsShadowWidthEdit";                                           // NO I18N

    public static final String DATAPROPS_SHADOW_OPACITY_EDIT = "AT.Chart.dataPropsShadowOpacityEdit";                                      // NO I18N

    public static final String DATAPROPS_SHADOW_COLOR_EDIT = "AT.Chart.dataPropsShadowColorEdit";                                          // NO I18N

    public static final String DATAPROPS_OUTLIERS_COLOR_EDIT = "AT.Chart.dataPropsOutliersColorEdit";                                      // NO I18N

    public static final String DATAPROPS_MEAN_COLOR_EDIT = "AT.Chart.dataPropsMeanColorEdit";                                              // NO I18N

    public static final String DATAPROPS_MEDIAN_COLOR_EDIT = "AT.Chart.dataPropsMedianColorEdit";                                          // NO I18N

    public static final String DATAPROPS_WHISKERS_COLOR_EDIT = "AT.Chart.dataPropsWhiskersColorEdit";                                      // NO I18N

    public static final String SERIES_SLIDER_TICK_COUNT_EDIT = "AT.Chart.seriesSliderTickCountEdit";                                       // NO I18N

    public static final String SERIES_BORDER_WIDTH_EDIT = "AT.Chart.seriesBorderWidthEdit";                                                // NO I18N

    public static final String DATAPROPERTY_BORDER_WIDTH_EDIT = "AT.Chart.dataPropsBorderWidthEdit";                                       // NO I18N

    public static final String DATAPROPERTY_BORDER_RADIUS_EDIT = "AT.Chart.dataPropsBorderRadiusEdit";                                     // NO I18N

    public static final String DATALABELS_BG_SHAPE_ROUNDED_RECT = "AT.Chart.datalabelsToRoundedRect";                                      // NO I18N

    public static final String DATALABELS_BG_SHAPE_RECT = "AT.Chart.datalabelsToRect";                                                     // NO I18N

    public static final String DATALABELS_BG_SHAPE_CIRCLE = "AT.Chart.datalabelsToCircle";                                                 // NO I18N

    public static final String DATALABELS_BG_SHAPE_ELLIPSE = "AT.Chart.datalabelsToEllipse";                                               // NO I18N

    public static final String SERIES_DATALABELS_BG_SHAPE_ROUNDED_RECT = "AT.Chart.seriesDatalabelsToRoundedRect";                         // NO I18N

    public static final String SERIES_DATALABELS_BG_SHAPE_RECT = "AT.Chart.seriesDatalabelsToRect";                                        // NO I18N

    public static final String SERIES_DATALABELS_BG_SHAPE_CIRCLE = "AT.Chart.seriesDatalabelsToCircle";                                    // NO I18N

    public static final String SERIES_DATALABELS_BG_SHAPE_ELLIPSE = "AT.Chart.seriesDatalabelsToEllipse";                                  // NO I18N

    public static final String DATALABELS_MOVED_VERTICALLY = "AT.Chart.datalabelsMovedVertically";                                         // NO I18N

    public static final String DATALABELS_MOVED_HORIZONTALLY = "AT.Chart.datalabelsMovedHorizontally";                                     // NO I18N

    public static final String DATALABELS_MOVED = "AT.Chart.datalabelsMoved";                                                              // NO I18N

    public static final String SERIES_DATALABELS_MOVED_VERTICALLY = "AT.Chart.seriesDatalabelsMovedVertically";                            // NO I18N

    public static final String SERIES_DATALABELS_MOVED_HORIZONTALLY = "AT.Chart.seriesDatalabelsMovedHorizontally";                        // NO I18N

    public static final String SERIES_DATALABELS_MOVED = "AT.Chart.seriesDatalabelsMoved";                                                 // NO I18N

    public static final String DP_DATALABELS_BG_SHAPE_ROUNDED_RECT = "AT.Chart.DPDatalabelsToRoundedRect";                                 // NO I18N

    public static final String DP_DATALABELS_BG_SHAPE_RECT = "AT.Chart.DPDatalabelsToRect";                                                // NO I18N

    public static final String DP_DATALABELS_BG_SHAPE_CIRCLE = "AT.Chart.DPDatalabelsToCircle";                                            // NO I18N

    public static final String DP_DATALABELS_BG_SHAPE_ELLIPSE = "AT.Chart.DPDatalabelsToEllipse";                                          // NO I18N

    public static final String DP_DATALABELS_MOVED_VERTICALLY = "AT.Chart.DPDatalabelsMovedVertically";                                    // NO I18N

    public static final String DP_DATALABELS_MOVED_HORIZONTALLY = "AT.Chart.DPDatalabelsMovedHorizontally";                                // NO I18N

    public static final String DP_DATALABELS_MOVED = "AT.Chart.DPDatalabelsMoved";                                                         // NO I18N

    public static final String SERIES_RELATION_SINGLE = "AT.Chart.seriesRelationSingle";                                                   // NO I18N

    public static final String SERIES_RELATION_PAIR = "AT.Chart.seriesRelationPair";                                                       // NO I18N

    public static final String CHART_AREA_FILL_TO_SOLID = "AT.Chart.areaFillToSolid";                                                      // NO I18N

    public static final String CHART_AREA_FILL_TO_LINEAR = "AT.Chart.areaFillToLinear";                                                    // NO I18N

    public static final String CHART_AREA_FILL_TO_RADIAL = "AT.Chart.areaFillToRadial";                                                    // NO I18N

    public static final String CHART_AREA_GRADIENT_DEGREE_EDIT = "AT.Chart.areaFillGradientDegreeChanged";                                 // NO I18N

    public static final String CHART_AREA_GRADIENT_STOPS_EDIT = "AT.Chart.areaFillGradientStopsChanged";                                   // NO I18N

    public static final String IMAGE_SERIES_EDIT = "AT.Chart.imageSeriesEdit";                                                             // NO I18N

    public static final String INFO_TEXT_ENABLED = "AT.Chart.infoTextEnabled";                                                             // NO I18N

    public static final String INFO_TEXT_DISABLED = "AT.Chart.infoTextDisabled";                                                           // NO I18N

    public static final String BORDER_WIDTH_CHANGED = "AT.Chart.chartBorderWidthChanged";                                                  // NO I18N

    public static final String MARGIN_LEFT_CHANGED = "AT.Chart.marginLeftChanged";                                                         // NO I18N

    public static final String MARGIN_RIGHT_CHANGED = "AT.Chart.marginRightChanged";                                                       // NO I18N

    public static final String MARGIN_TOP_CHANGED = "AT.Chart.marginTopChanged";                                                           // NO I18N

    public static final String MARGIN_BOTTOM_CHANGED = "AT.Chart.marginBottomChanged";                                                     // NO I18N

    public static final String TOTAL_LABEL_ENABLED = "AT.Chart.totalLabelEnabled";                                                         // NO I18N

    public static final String TOTAL_LABEL_DISABLED = "AT.Chart.totalLabelDisabled";                                                       // NO I18N

    public static final String TOTAL_LABEL_FONT_SIZE_CHANGED = "AT.Chart.totalLabelFontSizeChanged";                                       // NO I18N

    public static final String TOTAL_LABEL_FONT_COLOR_CHANGED = "AT.Chart.totalLabelFontColorChanged";                                     // NO I18N

    public static final String TOTAL_LABEL_FONT_STYLE_CHANGED = "AT.Chart.totalLabelFontStyleChanged";                                     // NO I18N

    public static final String TOTAL_LABEL_FONT_WEIGHT_CHANGED = "AT.Chart.totalLabelFontWeightChanged";                                   // NO I18N

    public static final String CHART_BORDER_STYLE_CHANGED = "AT.Chart.chartBorderStyleChanged";                                            // NO I18N

    public static final String CHART_BORDER_TRANSPARENCY_CHANGED = "AT.Chart.chartBorderOpacityChanged";                                   // NO I18N

    public static final String CHART_AREA_SHADOW_DISABLED = "AT.Chart.chartAreaShadowDisabled";                                            // NO I18N

    public static final String CHART_AREA_SHADOW_INSIDE = "AT.Chart.chartAreaShadowInside";                                                // NO I18N

    public static final String CHART_AREA_SHADOW_OUTSIDE = "AT.Chart.chartAreaShadowOutside";                                              // NO I18N

    public static final String CHART_AREA_SHADOW_COLOR_CHANGED = "AT.Chart.chartAreaShadowColorChanged";                                   // NO I18N

    public static final String CHART_AREA_SHADOW_ANGLE_CHANGED = "AT.Chart.chartAreaShadowAngleChanged";                                   // NO I18N

    public static final String CHART_AREA_SHADOW_DISTANCE_CHANGED = "AT.Chart.chartAreaShadowDistanceChanged";                             // NO I18N

    public static final String CHART_AREA_SHADOW_BLUR_CHANGED = "AT.Chart.chartAreaShadowBlurChanged";                                     // NO I18N

    public static final String CHART_AREA_SHADOW_SPREAD_CHANGED = "AT.Chart.chartAreaShadowSpreadChanged";                                 // NO I18N

    public static final String CHART_AREA_SHADOW_TRANSPARENCY_CHANGED = "AT.Chart.chartAreaShadowTransparencyChanged";                     // NO I18N

    public static final String SERIES_CHART_TYPE_CHANGED = "AT.Chart.seriesChartTypeChanged";                                              // NO I18N

    public static final String SERIES_GRADIENT_TYPE_LINEAR = "AT.Chart.seriesGradientTypeLinear";                                          // NO I18N

    public static final String SERIES_GRADIENT_TYPE_RADIAL = "AT.Chart.seriesGradientTypeRadial";                                          // NO I18N

    public static final String SERIES_GRADIENT_DEGREE_CHANGED = "AT.Chart.seriesGradientDegreeChanged";                                    // NO I18N

    public static final String SERIES_GRADIENT_STOPS_CHANGED = "AT.Chart.seriesGradientStopsChanged";                                      // NO I18N

    public static final String DATA_PROPS_GRADIENT_DISABLED = "AT.Chart.dataPropsGradientDisabled";                                        // NO I18N

    public static final String DATA_PROPS_GRADIENT_TYPE_LINEAR = "AT.Chart.dataPropsGradientTypeLinear";                                   // NO I18N

    public static final String DATA_PROPS_GRADIENT_TYPE_RADIAL = "AT.Chart.dataPropsGradientTypeRadial";                                   // NO I18N

    public static final String DATA_PROPS_GRADIENT_DEGREE_CHANGED = "AT.Chart.dataPropsGradientDegreeChanged";                             // NO I18N

    public static final String DATA_PROPS_GRADIENT_STOPS_CHANGED = "AT.Chart.dataPropsGradientStopsChanged";                               // NO I18N

    public static final String Y_AXIS_BASELINE_ENABLED = "AT.Chart.yAxisBaselineEnabled";                                                  // NO I18N

    public static final String Y_AXIS_BASELINE_DISABLED = "AT.Chart.yAxisBaselineDisabled";                                                // NO I18N

    public static final String Y_AXIS_BASELINE_COLOR_CHANGED = "AT.Chart.yAxisBaselineColorChanged";                                       // NO I18N

    public static final String NUMBER_FORMAT_TO_INDIAN = "AT.Chart.numberFormatToIndian";                                                  // NO I18N

    public static final String NUMBER_FORMAT_TO_GLOBAL = "AT.Chart.numberFormatToGlobal";                                                  // NO I18N

    public static final String DISABLED_MARGIN_LEFT = "AT.Chart.disabledMarginLeft";                                                       // NO I18N

    public static final String DISABLED_MARGIN_RIGHT = "AT.Chart.disabledMarginRight";                                                     // NO I18N

    public static final String DISABLED_MARGIN_TOP = "AT.Chart.disabledMarginTop";                                                         // NO I18N

    public static final String DISABLED_MARGIN_BOTTOM = "AT.Chart.disabledMarginBottom";                                                   // NO I18N

    public static final String SERIES_SHADOW_TYPE_CHANGED = "AT.Chart.seriesShadowTypeChanged";                                            // NO I18N

    public static final String SERIES_SHADOW_DEGREE_CHANGED = "AT.Chart.seriesShadowDegreeChanged";                                        // NO I18N

    public static final String SERIES_SHADOW_BLUR_CHANGED = "AT.Chart.seriesShadowBlurChanged";                                            // NO I18N

    public static final String SERIES_SHADOW_SPREAD_CHANGED = "AT.Chart.seriesShadowSpreadChanged";                                        // NO I18N

    public static final String SERIES_SHADOW_DISTANCE_CHANGED = "AT.Chart.seriesShadowDistanceChanged";                                    // NO I18N

    public static final String CHART_AREA_PRESET_CHANGED = "AT.Chart.chartAreaGradientPresetChanged";                                      // NO I18N

    public static final String PLOTLINE_LINE_WIDTH_CHANGED = "AT.Chart.PlotlineLineWidthChanged";                                          // NO I18N

    public static final String LEGEND_SHAPE_CHANGED = "AT.Chart.legendShapeChanged";                                                       // NO I18N

    public static final String BLANK_ADDED = "AT.Chart.blankAdded";                                                                        // NO I18N

    public static final String BLANK_REMOVED = "AT.Chart.blankRemoved";                                                                    // NO I18N

    public static final String X_AXIS_TEXTOVERFLOW_CHANGED = "AT.Chart.xAxisTextOverflowChanged";                                           // NO I18N

    public static final String X_AXIS_MAJOR_TICK_POSITION_CHANGED = "AT.Chart.xAxisMajorTickPositionChanged";                               // NO I18N

    public static final String X_AXIS_MINOR_TICK_POSITION_CHANGED = "AT.Chart.xAxisMinorTickPositionChanged";                               // NO I18N

    public static final String Y_AXIS_MAJOR_TICK_POSITION_CHANGED = "AT.Chart.yAxisMajorTickPositionChanged";                               // NO I18N

    public static final String Y_AXIS_MINOR_TICK_POSITION_CHANGED = "AT.Chart.yAxisMinorTickPositionChanged";                               // NO I18N

    public static final String AXIS_MAJOR_GL_OPACITY_CHANGED = "AT.Chart.axisMajorGLOpacityChanged";                                     // NO I18N

    public static final String AXIS_MINOR_GL_OPACITY_CHANGED = "AT.Chart.axisMinorGLOpacityChanged";                                     // NO I18N

    public static final String CHART_NAV_ENABLED = "AT.Chart.chartNavEnabled";                                                              // NO I18N

    public static final String CHART_NAV_DISABLED = "AT.Chart.chartNavDisabled";                                                            // NO I18N

    public static final String CHART_NAV_SCROLL_BAR_ENABLED = "AT.Chart.chartNavScrollBarEnabled";                                          // NO I18N

    public static final String CHART_NAV_SCROLL_BAR_DISABLED = "AT.Chart.chartNavScrollBarDisabled";                                        // NO I18N

    public static final String CHART_RANGE_SELECTOR_ENABLED = "AT.Chart.chartRangeSelectorEnabled";                                         // NO I18N

    public static final String CHART_RANGE_SELECTOR_DISABLED = "AT.Chart.chartRangeSelectorDisabled";                                         // NO I18N

    public static final String LEGEND_TITLE_ENABLED = "AT.Chart.legendTitleEnabled";                                                        // NO I18N

    public static final String LEGEND_TITLE_DISABLED = "AT.Chart.legendTitleDisabled";                                                      // NO I18N

    public static final String LEGEND_TITLE_CHANGED =  "AT.Chart.legendTitleTextChanged";                                                   // NO I18N

    public static final String LEGEND_TITLE_FONT_COLOR_CHANGED = "AT.Chart.legendTitleFontColorChanged";                                    // NO I18N

    public static final String LEGEND_TITLE_FONT_SIZE_CHANGED = "AT.Chart.legendTitleFontSizeChanged";                                      // NO I18N

    public static final String LEGEND_TITLE_FONT_STYLE_CHANGED = "AT.Chart.legendTitleFontStyleChanged";                                    // NO I18N

    public static final String LEGEND_TITLE_FONT_WEIGHT_CHANGED = "AT.Chart.legendTitleFontWeightChanged";                                  // NO I18N

    public static final String AXIS_MAJOR_GL_WIDTH_CHANGED = "AT.Chart.axisMajorGLWidthChanged";                                            // NO I18N

    public static final String AXIS_MINOR_GL_WIDTH_CHANGED = "AT.Chart.axisMinorGLWidthChanged";                                            // NO I18N

    public static final String CHART_AREA_SHADOW_PRESET_APPLIED = "AT.Chart.chartAreaShadowPresetApply";                                    // NO I18N

    public static final String CHART_AREA_BORDER_ENABLED = "AT.Chart.chartAreaBorderEnabled";                                               // NO I18N

    public static final String CHART_AREA_BORDER_DISABLED = "AT.Chart.chartAreaBorderDisabled";                                             // NO I18N

    public static final String SHARED_SERIES_DATALABELS_CUSTOM_VALUE_CHANGED = "AT.Chart.spSeriesDatalabelsCustomLabelChanged";             // NO I18N

    public static final String SERIES_DATALABELS_CUSTOM_VALUE_CHANGED = "AT.Chart.seriesDatalabelsCustomLabelsChanged";                     // NO I18N

    public static final String DATA_PROPS_DATALABELS_CUSTOM_VALUE_CHANGED = "AT.Chart.dataPropertyDatalabelsCustomLabelsChanged";           // NO I18N

    public static final String SERIES_DATALABLES_BG_FILL_TYPE_CHANGED = "AT.Chart.seriesDatalabelBgFillTypeChanged";                        // NO I18N

    public static final String SERIES_DATALABELS_BORDER_FILL_TYPE_CHANGED = "AT.Chart.seriesDatalabelBorderFillTypeChanged";                // NO I18N

    public static final String DATA_PROPS_DATALABEL_BG_FILL_TYPE_CHANGED = "AT.Chart.dataPropertyDatalabelBgFillTypeChanged";               // NO I18N

    public static final String DATA_PROPS_DATALABEL_BORDER_FILL_TYPE_CHANGED = "AT.Chart.dataPropertyDatalabelBorderFillTypeChanged";       // NO I18N

    public static final String SERIES_DATALABELS_SEPARATOR_CHANGED = "AT.Chart.seriesDatalabelSeparatorChanged";                            // NO I18N

    public static final String DATA_PROPS_DATALABELS_SEPARATOR_CHANGED = "AT.Chart.dataPropertyDatalabelSeparatorChanged";                  // NO I18N

    public static final String SERIES_UP_COLOR_FILL_CHANGED =  "AT.Chart.seriesUpColorFillTypeChanged";                                     // NO I18N

    public static final String SERIES_DOWN_COLOR_FILL_CHANGED =  "AT.Chart.seriesDownColorFillTypeChanged";                                 // NO I18N

    public static final String SERIES_TOTAL_COLOR_FILL_CHANGED =  "AT.Chart.seriesTotalColorFillTypeChanged";                               // NO I18N

    public static final String SERIES_UP_COLOR_GRADIENT_DEGREE_CHANGED =  "AT.Chart.seriesUpColorGradientDegreeChanged";                    // NO I18N

    public static final String SERIES_DOWN_COLOR_GRADIENT_DEGREE_CHANGED =  "AT.Chart.seriesDownColorGradientDegreeChanged";                // NO I18N

    public static final String SERIES_TOTAL_COLOR_GRADIENT_DEGREE_CHANGED =  "AT.Chart.seriesTotalColorGradientDegreeChanged";              // NO I18N

    public static final String SERIES_UP_COLOR_GRADIENT_CHANGED =  "AT.Chart.seriesUpColorGradientChanged";                                 // NO I18N

    public static final String SERIES_DOWN_COLOR_GRADIENT_CHANGED =  "AT.Chart.seriesDownColorGradientChanged";                             // NO I18N

    public static final String SERIES_TOTAL_COLOR_GRADIENT_CHANGED =  "AT.Chart.seriesTotalColorGradientChanged";                           // NO I18N

    public static final String SERIES_TOTAL_COLOR_CHANGED =  "AT.Chart.seriesTotalColorChanged";                                            // NO I18N

    public static final String SERIES_TOTAL_OPACITY_CHANGED =  "AT.Chart.seriesTotalColorOpacityChanged";                                   // NO I18N

    public static final String SERIES_COLOR_PRESET_APPLIED =  "AT.Chart.seriesColorPresetApplied";                                          // NO I18N

    public static final String DATA_PROPS_COLOR_PRESET_APPLIED =  "AT.Chart.dataPropsColorPresetApplied";                                   // NO I18N

    public static final String TRENDLINE_FILL_TYPE_CHANGED = "AT.Chart.trendlineFillTypeChanged";                                           // NO I18N

    public static final String SERIES_UP_COLOR_GRADIENT_PRESETAPPLIED = "AT.Chart.seriesUpColorGradientPresetApplied";                      // NO I18N

    public static final String SERIES_DOWN_COLOR_GRADIENT_PRESETAPPLIED = "AT.Chart.seriesDownColorGradientPresetApplied";                  // NO I18N

    public static final String SERIES_TOTAL_COLOR_GRADIENT_PRESETAPPLIED = "AT.Chart.seriesTotalColorGradientPresetApplied";                // NO I18N

    public static final String CREDITS_ENABLED = "AT.Chart.creditsEnabled";                                                                 // NO I18N

    public static final String CREDITS_DISABLED = "AT.Chart.creditsDisabled";                                                               // NO I18N

    public static final String CREDITS_TEXT_CHANGED = "AT.Chart.creditsTextChanged";                                                        // NO I18N

    public static final String CREDITS_FONT_SIZE_CHANGED = "AT.Chart.creditsFontSizeChanged";                                               // NO I18N

    public static final String CREDITS_FONT_WEIGHT_CHANGED = "AT.Chart.creditsFontWeightChanged";                                           // NO I18N

    public static final String CREDITS_FONT_STYLE_CHANGED = "AT.Chart.creditsFontStyleChanged";                                             // NO I18N

    public static final String CREDITS_FONT_COLOR_CHANGED = "AT.Chart.creditsFontColorChanged";                                             // NO I18N

    public static final String SERIES_SHADOW_PRESET_APPLIED = "AT.Chart.seriesShadowPresetApplied";                                         // NO I18N

    public static final String X_AXIS_TITLE_ROTATION_CHANGED = "AT.Chart.xAxisTitleRotationChanged";                                        // NO I18N

    public static final String Y_AXIS_TITLE_ROTATION_CHANGED = "AT.Chart.yAxisTitleRotationChanged";                                        // NO I18N

    public static final String SERIES_DATALABELS_ROTATION_CHANGED = "AT.Chart.seriesDatalabelsRotationChanged";                             // NO I18N

    public static final String DP_DATALABELS_ROTATION_CHANGED = "AT.Chart.DPDatalabelsRotationChanged";                                     // NO I18N

    public static final String SLICE_SELECTED = "AT.Chart.sliceSelected";                                                                   // NO I18N

    public static final String SLICE_DESELECTED = "AT.Chart.sliceDeselected";                                                               // NO I18N

    public static final String TRENDLINE_DATALABELS_DISABLED = "AT.Chart.TrendlineDatalabelsDisabled";                                      // NO I18N

    public static final String TRENDLINE_DATALABELS_ENABLED = "AT.Chart.TrendlineDatalabelsEnabled";                                        // NO I18N

    public static final String TRENDLINE_DATALABELS_FONT_COLOR_CHANGED = "AT.Chart.TrendlineDatalabelsFontColorChanged";                    // NO I18N

    public static final String TRENDLINE_DATALABELS_FONT_SIZE_CHANGED = "AT.Chart.TrendlineDatalabelsFontSizeChanged";                      // NO I18N

    public static final String TRENDLINE_DATALABELS_FONT_STYLE_CHANGED = "AT.Chart.TrendlineDatalabelsFontStyleChanged";                    // NO I18N

    public static final String TRENDLINE_DATALABELS_FONT_WEIGHT_CHANGED = "AT.Chart.TrendlineDatalabelsFontWeightChanged";                  // NO I18N

    public static final String TRENDLINE_DATALABELS_FORMAT_CHANGED = "AT.Chart.TrendlineDatalabelsFormatChanged";                           // NO I18N

    public static final String TRENDLINE_DATALABELS_CUSTOM_LABEL_CHANGED = "AT.Chart.TrendlineDatalabelsCustomLabelChanged";                // NO I18N

    public static final String TRENDLINE_DATALABELS_SEPARATOR_CHANGED = "AT.Chart.TrendlineDatalabelsSeparatorChanged";                     // NO I18N



}
