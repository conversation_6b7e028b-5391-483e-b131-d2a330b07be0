package com.zoho.sheet.knitcharts.chart.sheetchart.api;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.PivotChartSheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMetaDefaultSupplier;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties.SeriesProperty;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties.dataproperties.DataProperty;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.xaxis.XAxes;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.YAxes;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.plotlines.PlotLine;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.series.DataPropertyReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.series.SeriesPropertyReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.xaxis.XAxesReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.yaxis.PlotLineReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.yaxis.YAxesReferences;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.AutoFillType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.DataType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.BorderStyleType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.ColorPaletteType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.DataJoinType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.SheetChartAPIUtils;

import java.util.List;

public class SheetChartGetterAPI {

    public static String getXAxisTitleFontColorAccent(SheetMeta sheetMeta, int xIndex) {
        XAxes axes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xIndex);

        return axes.getTitle()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getXAxisTitleFontColorTone(SheetMeta sheetMeta, int xIndex) {
        XAxes axes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xIndex);

        return axes.getTitle()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getSharedXAxisTitleFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getXAxisProperty()
                .getTitle()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getSharedXAxisTitleFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getXAxisProperty()
                .getTitle()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getSharedSeriesThresholdColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getNegativeColor().getAccent();
    }

    public static Double getSharedSeriesThresholdColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getNegativeColor().getTone();
    }

    public static String getSeriesThresholdColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getNegativeColor()
                .getAccent();
    }

    public static Double getSeriesThresholdColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getNegativeColor()
                .getTone();
    }

    public static String getSeriesDataLabelsFontColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getDataLabels()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getSeriesDataLabelsFontColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getDataLabels()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getSharedSeriesDataLabelsFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getSharedSeriesDataLabelsFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getLegendFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getLegend()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getLegendFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getLegend()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getYAxisTitleFontColorAccent(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getTitle()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getYAxisTitleFontColorTone(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getTitle()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getSubtitleFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSubtitle()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getSubtitleFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSubtitle()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getTitleFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getTitle()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getTitleFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getTitle()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getSeriesTrendlineColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty  = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getTrendLine()
                .getColor()
                .getAccent();
    }

    public static Double getSeriesTrendlineColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty  = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getTrendLine()
                .getColor()
                .getTone();
    }

    public static String getSeriesColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getColor().getAccent();
    }

    public static Double getSeriesColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getColor().getTone();
    }

    public static String getSeriesBorderColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getBorderColor()
                .getAccent();
    }

    public static Double getSeriesBorderColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getBorderColor().getTone();
    }

    public static String getSharedSeriesBorderColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getBorderColor().getAccent();
    }

    public static Double getSharedSeriesBorderColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getBorderColor().getTone();
    }

    public static String getSeriesMarkerColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getMarkerColor().getAccent();
    }

    public static Double getSeriesMarkerColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getMarkerColor().getTone();
    }

    public static String getSharedSeriesMarkerColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getMarkerColor().getAccent();
    }

    public static Double getSharedSeriesMarkerColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getMarkerColor().getTone();
    }

    public static String getSeriesMarkerBorderColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getMarkerBorderColor().getAccent();
    }

    public static Double getSeriesMarkerBorderColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getMarkerBorderColor().getTone();
    }

    public static String getSharedSeriesMarkerBorderColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getMarkerBorderColor()
                .getAccent();
    }

    public static Double getSharedSeriesMarkerBorderColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getMarkerBorderColor().getTone();
    }

    public static String getXAxisLabelsFontColorAccent(SheetMeta sheetMeta, int xAxisIndex) {
        XAxes xAxes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex);

        return xAxes.getLabels()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getXAxisLabelsFontColorTone(SheetMeta sheetMeta, int xAxisIndex) {
        XAxes xAxes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex);

        return xAxes.getLabels()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getYAxisLabelsFontColorAccent(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes axes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return axes.getLabels()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getYAxisLabelsFontColorTone(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes axes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return axes.getLabels()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getYAxisPlotlineColorAccent(SheetMeta sheetMeta, int yAxisIndex, int plotlineIndex) {
        PlotLine plotLine = SheetChartAPIUtils.computePlotlineIfNotAvailable(sheetMeta, yAxisIndex, plotlineIndex);

        return plotLine.getLineColor().getAccent();
    }

    public static Double getYAxisPlotlineColorTone(SheetMeta sheetMeta, int yAxisIndex, int plotlineIndex) {
        PlotLine plotLine = SheetChartAPIUtils.computePlotlineIfNotAvailable(sheetMeta, yAxisIndex, plotlineIndex);

        return plotLine.getLineColor().getTone();
    }

    public static String getYAxisPlotlineLabelFontColorAccent(SheetMeta sheetMeta, int yAxisIndex, int plotlineIndex) {
        PlotLine plotLine = SheetChartAPIUtils.computePlotlineIfNotAvailable(sheetMeta, yAxisIndex, plotlineIndex);

        return plotLine.getLabel().getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getYAxisPlotlineLabelFontColorTone(SheetMeta sheetMeta, int yAxisIndex, int plotlineIndex) {
        PlotLine plotLine = SheetChartAPIUtils.computePlotlineIfNotAvailable(sheetMeta, yAxisIndex, plotlineIndex);

        return plotLine.getLabel().getFontColorThemeOptions()
                .getTone();
    }

    public static String getXAxisMajorGridlineColorAccent(SheetMeta sheetMeta, int xAxisIndex) {
        XAxes xAxes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex);

        return xAxes.getGridLines()
                .getMajorGLColor()
                .getAccent();
    }

    public static Double getXAxisMajorGridlineColorTone(SheetMeta sheetMeta, int xAxisIndex) {
        XAxes xAxes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex);

        return xAxes.getGridLines()
                .getMajorGLColor()
                .getTone();
    }

    public static String getXAxisMinorGridlineColorAccent(SheetMeta sheetMeta, int xAxisIndex) {
        XAxes xAxes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex);

        return xAxes.getGridLines()
                .getMinorGLColor()
                .getAccent();
    }

    public static Double getXAxisMinorGridlineColorTone(SheetMeta sheetMeta, int xAxisIndex) {
        XAxes xAxes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex);

        return xAxes.getGridLines()
                .getMinorGLColor()
                .getTone();
    }

    public static String getYAxisMajorGridlineColorAccent(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getGridLines()
                .getMajorGLColor()
                .getAccent();
    }

    public static Double getYAxisMajorGridlineColorTone(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getGridLines()
                .getMajorGLColor()
                .getTone();
    }

    public static String getYAxisMinorGridlineColorAccent(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getGridLines()
                .getMinorGLColor()
                .getAccent();
    }

    public static Double getYAxisMinorGridlineColorTone(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getGridLines()
                .getMinorGLColor()
                .getTone();
    }

    public static String getYAxisStackLabelsFontColorAccent(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getStackLabels()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getYAxisStackLabelsFontColorTone(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getStackLabels()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getChartBackgroundColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getChart().getBackgroundColor()
                .getAccent();
    }

    public static Double getChartBackgroundColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getChart().getBackgroundColor().getTone();
    }

    public static String getSeriesUpColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getUpColor().getAccent();
    }

    public static Double getSeriesUpColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getUpColor().getTone();
    }

    public static String getSharedSeriesTargetColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getTargetOptions()
                .getColor()
                .getAccent();
    }

    public static Double getSharedSeriesTargetColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getTargetOptions()
                .getColor()
                .getTone();
    }

    public static String getSeriesDownColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getDownColor().getAccent();
    }

    public static Double getSeriesDownColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getDownColor().getTone();
    }

    public static String getCaptionFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getCaption()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getCaptionFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getCaption()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getXAxisBaseLineColorAccent(SheetMeta sheetMeta, int xAxisIndex) {
        XAxes xAxes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex);

        return xAxes.getBaseLineColor().getAccent();
    }

    public static Double getXAxisBaseLineColorTone(SheetMeta sheetMeta, int xAxisIndex) {
        XAxes xAxes = SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex);

        return xAxes.getBaseLineColor().getTone();
    }

    public static String getDataPropertyColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropertyIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.computeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropertyIndex);

        return dataProperty.getColor()
                .getAccent();
    }

    public static Double getDataPropertyColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropertyIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.computeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropertyIndex);

        return dataProperty.getColor()
                .getTone();
    }

    public static String getDataPropertyBorderColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropertyIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.computeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropertyIndex);

        return dataProperty.getBorderColor().getAccent();
    }

    public static Double getDataPropertyBorderColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropertyIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.computeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropertyIndex);

        return dataProperty.getBorderColor().getTone();
    }

    public static String getTotalDataLabelsFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getTotalDataLabels().getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getTotalDataLabelsFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getTotalDataLabels().getFontColorThemeOptions()
                .getTone();
    }

    public static String getSeriesOutliersColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getOutliersColor().getAccent();
    }

    public static Double getSeriesOutliersColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getOutliersColor().getTone();
    }

    public static String getSharedSeriesOutliersColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties().getSeriesProperty()
                .getOutliersColor().getAccent();
    }

    public static Double getSharedSeriesOutliersColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties().getSeriesProperty()
                .getOutliersColor().getTone();
    }

    public static String getSeriesMeanColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getMeanColor().getAccent();
    }

    public static Double getSeriesMeanColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getMeanColor().getTone();
    }

    public static String getSharedSeriesMeanColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties().getSeriesProperty()
                .getMeanColor().getAccent();
    }

    public static Double getSharedSeriesMeanColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties().getSeriesProperty()
                .getMeanColor().getTone();
    }

    public static String getSeriesMedianColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getMedianColor().getAccent();
    }

    public static Double getSeriesMedianColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getMedianColor().getTone();
    }

    public static String getSharedSeriesMedianColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties().getSeriesProperty()
                .getMedianColor().getAccent();
    }

    public static Double getSharedSeriesMedianColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties().getSeriesProperty()
                .getMedianColor().getTone();
    }

    public static String getSeriesWhiskersColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getWhiskerColor().getAccent();
    }

    public static Double getSeriesWhiskersColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.getSeriesProperty(sheetMeta, seriesIndex);

        return seriesProperty.getWhiskerColor().getTone();
    }

    public static String getSharedSeriesWhiskersColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties().getSeriesProperty()
                .getWhiskerColor().getAccent();
    }

    public static Double getSharedSeriesWhiskersColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties().getSeriesProperty()
                .getWhiskerColor().getTone();
    }

    public static Boolean getIncludeHiddenCellsStatus(SheetMeta sheetMeta) {
        return sheetMeta.getDataTableOptions()
                .getIncludeHiddenCells();
    }

    public static boolean getAutoExpandCellsStatus(SheetMeta sheetMeta) {
        return sheetMeta.getDataTableOptions()
                .getAutoExpand();
    }

    public static SeriesInType getSeriesInType(SheetMeta sheetMeta) {
        return sheetMeta.getDataTableOptions().getSeriesIn();
    }


    public static Double getChartWidth(SheetMeta sheetMeta) {
        return sheetMeta.getSpatialParameters().getWidth();
    }

    public static Double getChartHeight(SheetMeta sheetMeta) {
        return sheetMeta.getSpatialParameters().getHeight();
    }

    public static List<String> getDataSources(SheetMeta sheetMeta) {
        return sheetMeta.getDataTableOptions().getDataSources();
    }

    @Deprecated
    public static String getChartBorderColor(SheetMeta sheetMeta) {
        return sheetMeta.getChart().getBorderColor();
    }

    public static String getChartBorderColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getChart().getBorderColor().getAccent();
    }

    public static Double getChartBorderColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getChart().getBorderColor().getTone();
    }

    public static DataJoinType getDataJoinType(SheetMeta sheetMeta) {
        return sheetMeta.getDataTableOptions().getDataJoinType();
    }

    public static AutoFillType getAutoFillType(SheetMeta sheetMeta) {
        return sheetMeta.getDataProps()
                .getSharedSeriesType().getAutoFillType();
    }

    public static DataType getSeriesDataType(SheetMeta sheetMeta, int seriesIndex) {
        return SheetChartAPIUtils.softComputeSeriesTypeIfNotAvailable(sheetMeta, seriesIndex)
                .getType();
    }

    public static ColorPaletteType getColorPaletteType(SheetMeta sheetMeta) {
        return sheetMeta.getColorPalette().getColorPaletteType();
    }

    public static String getColorPaletteCustomColor(SheetMeta sheetMeta) {
        return sheetMeta.getColorPalette()
                .getCustomColor();
    }

    public static String getColorPaletteCustomColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getColorPalette().getCustomColor()
                .getAccent();
    }

    public static Double getColorPaletteCustomColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getColorPalette().getCustomColor()
                .getTone();
    }

    public static Boolean getFirstRowLabel(SheetMeta sheetMeta) {
        return sheetMeta.getDataTableOptions().getFirstRowAsLabel();
    }

    public static Boolean getFirstColLabel(SheetMeta sheetMeta) {
        return sheetMeta.getDataTableOptions().getFirstColAsLabel();
    }

    public static Boolean getColorPaletteReversedStatus(SheetMeta sheetMeta) {
        return sheetMeta.getColorPalette().getReversed();
    }

    public static Boolean getPivotFilterPinnedStatus(SheetMeta sheetMeta) {
        if(sheetMeta instanceof PivotChartSheetMeta) {
            PivotChartSheetMeta pivotChartSheetMeta = (PivotChartSheetMeta) sheetMeta;
            return pivotChartSheetMeta.getIsFilterPinned();
        }

        return false;
    }

    public static String getPublicChartName(SheetMeta sheetMeta) {
        return sheetMeta.getPublicChart().getPublicChartName();
    }

    public static Double getStartColumn(SheetMeta sheetMeta) {
        return sheetMeta.getSpatialParameters().getStartColumn();
    }

    public static Double getStartRow(SheetMeta sheetMeta) {
        return sheetMeta.getSpatialParameters().getStartRow();
    }

    public static Double getStartColumnDiff(SheetMeta sheetMeta) {
        return sheetMeta.getSpatialParameters().getStartColumnDiff();
    }

    public static Double getStartRowDiff(SheetMeta sheetMeta) {
        return sheetMeta.getSpatialParameters().getStartRowDiff();
    }

    public static Boolean getCellLimitExceeded(SheetMeta sheetMeta) {
        return sheetMeta.getDataTableOptions().getIsCellCountExceeds();
    }

    public static String getChartTitleTextReference(SheetMeta sheetMeta) {
        return sheetMeta.getTextReferences()
                .getTitleTextReference();
    }

    public static String getChartSubtitleTextReference(SheetMeta sheetMeta) {
        return sheetMeta.getTextReferences()
                .getSubtitleTextReference();
    }

    public static String getLegendTitleTextReference(SheetMeta sheetMeta) {
        return sheetMeta.getTextReferences()
                .getLegendTitleReference();
    }

    public static String getChartXAxisTitleTextReference(SheetMeta sheetMeta, int xAxisIndex) {
        XAxesReferences references = SheetChartAPIUtils.computeXAxisReferences(sheetMeta, xAxisIndex);

        return references.getTitleTextReference();
    }

    public static String getChartYAxisTitleTextReference(SheetMeta sheetMeta, int yAxisIndex) {
        YAxesReferences yAxesReferences = SheetChartAPIUtils.computeYAxisReferences(sheetMeta, yAxisIndex);

        return yAxesReferences.getTitleTextReference();
    }

    public static String getPlotLinesLabelTextReference(SheetMeta sheetMeta, int yAxisIndex, int plotlineIndex) {
        PlotLineReferences references = SheetChartAPIUtils.computePlotLineReferences(sheetMeta, yAxisIndex, plotlineIndex);

        return references.getLabelReference();
    }

    public static String getPlotLinesValueReference(SheetMeta sheetMeta, int yAxisIndex, int plotlineIndex) {
        PlotLineReferences references = SheetChartAPIUtils.computePlotLineReferences(sheetMeta, yAxisIndex, plotlineIndex);

        return references.getValueReference();
    }

    public static String getTotalDataLabelsReference(SheetMeta sheetMeta) {
        return sheetMeta.getTextReferences()
                .getTotalDatalabelsReference();
    }

    public static String getDataPropertyDataLabelFontColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.computeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getDataLabels()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getDataPropertyDataLabelFontColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.computeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getDataLabels()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getChartTitleFromReference(Workbook workbook, SheetMeta sheetMeta) {
        String referenceString = SheetChartGetterAPI.getChartTitleTextReference(sheetMeta);

        return SheetChartAPIUtils.getTextFromReference(workbook, referenceString);
    }

    public static String getChartSubtitleFromReference(Workbook workbook, SheetMeta sheetMeta) {
        String referenceString = SheetChartGetterAPI.getChartSubtitleTextReference(sheetMeta);

        return SheetChartAPIUtils.getTextFromReference(workbook, referenceString);
    }

    public static String getLegendTitleFromReference(Workbook workbook, SheetMeta sheetMeta) {
        String referenceString = SheetChartGetterAPI.getLegendTitleTextReference(sheetMeta);

        return SheetChartAPIUtils.getTextFromReference(workbook, referenceString);
    }

    public static String getChartXAxisTitleFromReference(Workbook workbook, SheetMeta sheetMeta, int index) {
        String referenceString = SheetChartGetterAPI.getChartXAxisTitleTextReference(sheetMeta, index);

        return SheetChartAPIUtils.getTextFromReference(workbook, referenceString);
    }

    public static String getChartYAxisTitleFromReference(Workbook workbook, SheetMeta sheetMeta, int index) {
        String referenceString = SheetChartGetterAPI.getChartYAxisTitleTextReference(sheetMeta, index);

        return SheetChartAPIUtils.getTextFromReference(workbook, referenceString);
    }

    public static String getPlotlinesLabelFromReference(Workbook workbook, SheetMeta sheetMeta, int yAxisIndex, int plotlineIndex) {
        String referenceString = SheetChartGetterAPI.getPlotLinesLabelTextReference(sheetMeta, yAxisIndex, plotlineIndex);

        return SheetChartAPIUtils.getTextFromReference(workbook, referenceString);
    }

    public static Double getPlotlinesValueFromReference(Workbook workbook, SheetMeta sheetMeta, int yAxisIndex, int plotlineIndex) {
        String referenceString = SheetChartGetterAPI.getPlotLinesValueReference(sheetMeta, yAxisIndex, plotlineIndex);

        return SheetChartAPIUtils.getValueFromReference(workbook, referenceString);
    }

    public static String getTotalDataLabelTextFromReference(Workbook workbook, SheetMeta sheetMeta) {
        String referenceString = SheetChartGetterAPI.getTotalDataLabelsReference(sheetMeta);

        return SheetChartAPIUtils.getTextFromReference(workbook, referenceString);
    }

    public static String getXAxisMajorTickColorAccent(SheetMeta sheetMeta, int xAxisIndex) {
        return SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex)
                .getTickLines()
                .getMajorTickColor()
                .getAccent();
    }

    public static Double getXAxisMajorTickColorTone(SheetMeta sheetMeta, int xAxisIndex) {
        return SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex)
                .getTickLines()
                .getMajorTickColor()
                .getTone();
    }

    public static String getXAxisMinorTickColorAccent(SheetMeta sheetMeta, int xAxisIndex) {
        return SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex)
                .getTickLines()
                .getMinorTickColor()
                .getAccent();
    }

    public static Double getXAxisMinorTickColorTone(SheetMeta sheetMeta, int xAxisIndex) {
        return SheetChartAPIUtils.computeXAxisIfNotAvailable(sheetMeta, xAxisIndex)
                .getTickLines()
                .getMinorTickColor().getTone();
    }

    public static String getYAxisMajorTickColorAccent(SheetMeta sheetMeta, int yAxisIndex) {
        return SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex)
                .getTickLines()
                .getMajorTickColor()
                .getAccent();
    }

    public static Double getYAxisMajorTickColorTone(SheetMeta sheetMeta, int yAxisIndex) {
        return SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex)
                .getTickLines()
                .getMajorTickColor().getTone();
    }

    public static String getYAxisMinorTickColorAccent(SheetMeta sheetMeta, int yAxisIndex) {
        return SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex)
                .getTickLines()
                .getMinorTickColor().getAccent();
    }

    public static Double getYAxisMinorTickColorTone(SheetMeta sheetMeta, int yAxisIndex) {
        return SheetChartAPIUtils.computeYAxisIfNotAvailable(sheetMeta, yAxisIndex)
                .getTickLines()
                .getMinorTickColor().getTone();
    }

    public static Boolean getChartVisibility(SheetMeta sheetMeta) {
        return sheetMeta.getChart()
                .getVisibility();
    }

    public static String getChartName(SheetMeta sheetMeta) {
        return sheetMeta.getChart()
                .getName();
    }

    public static String getSeriesShadowColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getShadow()
                .getColorTheme()
                .getAccent();
    }

    public static Double getSeriesShadowColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getShadow()
                .getColorTheme().getTone();
    }

    public static String getSharedSeriesShadowColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getShadow()
                .getColorTheme()
                .getAccent();
    }

    public static Double getSharedSeriesShadowColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getSharedProperties()
                .getSeriesProperty()
                .getShadow()
                .getColorTheme()
                .getTone();
    }

    public static String getDataPropertyDataLabelsBgColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getDataLabels()
                .getBgColor()
                .getAccent();
    }

    public static Double getDataPropertyDataLabelsBgColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getDataLabels()
                .getBgColor().getTone();
    }

    public static String getDataPropertyDataLabelsBorderColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getDataLabels()
                .getBorderColor()
                .getAccent();
    }

    public static Double getDataPropertyDataLabelsBorderColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getDataLabels()
                .getBorderColor().getTone();
    }

    public static String getSeriesDataLabelsBgColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getDataLabels()
                .getBgColor()
                .getAccent();
    }

    public static Double getSeriesDataLabelsBgColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getDataLabels()
                .getBgColor().getTone();
    }

    public static String getSeriesDataLabelsBorderColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getDataLabels()
                .getBorderColor().getAccent();
    }

    public static Double getSeriesDataLabelsBorderColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getDataLabels()
                .getBorderColor().getTone();
    }

    public static String getSharedSeriesDataLabelsBgColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getSharedProperties()
                .getSeriesProperty().getDataLabels()
                .getBgColor().getAccent();
    }

    public static Double getSharedSeriesDataLabelsBgColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getSharedProperties()
                .getSeriesProperty().getDataLabels()
                .getBgColor().getTone();
    }

    public static String getSharedSeriesDataLabelsBorderColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getSharedProperties()
                .getSeriesProperty().getDataLabels()
                .getBorderColor().getAccent();
    }

    public static Double getSharedSeriesDataLabelsBorderColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getSharedProperties()
                .getSeriesProperty().getDataLabels()
                .getBorderColor().getTone();
    }

    public static String getSeriesSliderLabelsFontColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getSlider()
                .getLabelsFontColor()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getSeriesSliderLabelsFontColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getSlider()
                .getLabelsFontColor()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getDataPropertyShadowColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getShadow()
                .getColorTheme()
                .getAccent();
    }

    public static Double getDataPropertyShadowColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getShadow()
                .getColorTheme().getTone();
    }

    public static String getDataPropertyMarkerColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getMarkerColor()
                .getAccent();
    }

    public static Double getDataPropertyMarkerColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getMarkerColor().getTone();
    }

    public static String getDataPropertyMarkerBorderColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getMarkerBorderColor().getAccent();
    }

    public static Double getDataPropertyMarkerBorderColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getMarkerBorderColor().getTone();
    }

    public static String getDataPropertyOutliersColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getOutliersColor().getAccent();
    }

    public static Double getDataPropertyOutliersColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getOutliersColor().getTone();
    }

    public static String getDataPropertyMeanColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getMeanColor().getAccent();
    }

    public static Double getDataPropertyMeanColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getMeanColor().getTone();
    }

    public static String getDataPropertyMedianColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getMedianColor().getAccent();
    }

    public static Double getDataPropertyMedianColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getMedianColor().getTone();
    }

    public static String getDataPropertyWhiskersColorAccent(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getWhiskerColor().getAccent();
    }

    public static Double getDataPropertyWhiskersColorTone(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getWhiskerColor().getTone();
    }

    public static double getLeft(Sheet sheet, SheetMeta sheetMeta) {
        double startColumn = ChartUtils.requireNonNullElse(SheetChartGetterAPI.getStartColumn(sheetMeta), 0d);
        double left = ChartUtils.requireNonNullElse(SheetChartGetterAPI.getStartColumnDiff(sheetMeta), 0d);

        for(int colIdx = 0; colIdx < startColumn; colIdx++) {
            left += sheet.getColumnHeader(colIdx).getColumnWidth();
        }

        return left;
    }

    public static double getTop(Sheet sheet, SheetMeta sheetMeta) {
        double startRow = ChartUtils.requireNonNullElse(SheetChartGetterAPI.getStartRow(sheetMeta), 0d);
        double top = ChartUtils.requireNonNullElse(SheetChartGetterAPI.getStartRowDiff(sheetMeta), 0d);

        for(int rowIdx = 0; rowIdx < startRow; rowIdx++) {
            top += sheet.getRow(rowIdx).getRowHeight();
        }
        return top;
    }

    public static double getHeightOrDefault(SheetMeta sheetMeta, ChartType chartType){
        Double originalHT = getChartHeight(sheetMeta);
        return originalHT == null ? SheetMetaDefaultSupplier.getDefaultChartHeight(chartType) : originalHT;
    }

    public static double getWidthOrDefault(SheetMeta sheetMeta, ChartType chartType){
        Double originalWD = getChartWidth(sheetMeta);
        return originalWD == null ? SheetMetaDefaultSupplier.getDefaultChartWidth(chartType) : originalWD;
    }

    public static Boolean getInfoTextStatus(SheetMeta sheetMeta) {
        return ChartUtils.requireNonNullElse(sheetMeta.getDataProps()
                .getInfoTextStatus(), false);
    }

    @Deprecated
    public static Integer getChartBorderWidth(SheetMeta sheetMeta) {
        return sheetMeta.getChart().getBorderWidth();
    }

    public static String getTotalLabelAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getTotalLabel()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getTotalLabelTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getTotalLabel()
                .getFontColorThemeOptions()
                .getTone();
    }

    @Deprecated
    public static BorderStyleType getChartBorderStyle(SheetMeta sheetMeta) {
        return sheetMeta.getChart().getBorderStyle();
    }

    @Deprecated
    public static Double getChartBorderOpacity(SheetMeta sheetMeta) {
        return sheetMeta.getChart().getBorderOpacity();
    }

    public static String getChartAreaShadowColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getChart()
                .getShadow().getShadowColor().getAccent();
    }

    public static Double getChartAreaShadowColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getChart()
                .getShadow().getShadowColor().getTone();
    }

    public static String getCustomPropsChartAreaGradientStops(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps()
                .getChart().getBgGradient()
                .getStops();
    }

    public static String getCustomPropsSeriesColorStops(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getColorGradient()
                .getStops();
    }

    public static String getCustomPropsDataPropertyColorStops(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = SheetChartAPIUtils.softComputeDataPropertyIfNotAvailable(sheetMeta, seriesIndex, dataPropsIndex);

        return dataProperty.getColorGradient()
                .getStops();
    }

    public static String getYAxisBaseLineColorAccent(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.softComputeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getBaseLineColor()
                .getAccent();
    }

    public static Double getYAxisBaseLineTone(SheetMeta sheetMeta, int yAxisIndex) {
        YAxes yAxes = SheetChartAPIUtils.softComputeYAxisIfNotAvailable(sheetMeta, yAxisIndex);

        return yAxes.getBaseLineColor()
                .getTone();
    }

    public static Boolean getRemoveBlank(SheetMeta sheetMeta) {
        return sheetMeta.getDataTableOptions()
                .getDataManipulationOptions().getAggregationOptions()
                .getRemoveBlank();
    }

    public static String getXAxisPrefixTextReference(SheetMeta sheetMeta, int xAxisIndex) {
        return SheetChartAPIUtils.softComputeXAxisReferences(sheetMeta, xAxisIndex)
                .getPrefixTextReference();
    }

    public static String getXAxisSuffixTextReference(SheetMeta sheetMeta, int xAxisIndex) {
        return SheetChartAPIUtils.softComputeXAxisReferences(sheetMeta, xAxisIndex)
                .getSuffixTextReference();
    }

    public static String getYAxisPrefixTextReference(SheetMeta sheetMeta, int yAxisIndex) {
        return SheetChartAPIUtils.softComputeYAxisReferences(sheetMeta, yAxisIndex)
                .getPrefixTextReference();
    }

    public static String getYAxisSuffixTextReference(SheetMeta sheetMeta, int yAxisIndex) {
        return SheetChartAPIUtils.softComputeYAxisReferences(sheetMeta, yAxisIndex)
                .getSuffixTextReference();
    }

    public static String getXAxisPrefixFromTextReference(Workbook workbook, SheetMeta sheetMeta, int xAxisIndex) {
        String textReference = getXAxisPrefixTextReference(sheetMeta, xAxisIndex);

        return SheetChartAPIUtils.getTextFromReference(workbook, textReference);
    }

    public static String getXAxisSuffixFromTextReference(Workbook workbook, SheetMeta sheetMeta, int xAxisIndex) {
        String textReference = getXAxisSuffixTextReference(sheetMeta, xAxisIndex);

        return SheetChartAPIUtils.getTextFromReference(workbook, textReference);
    }

    public static String getYAxisPrefixFromTextReference(Workbook workbook, SheetMeta sheetMeta, int yAxisIndex) {
        String textReference = getYAxisPrefixTextReference(sheetMeta, yAxisIndex);

        return SheetChartAPIUtils.getTextFromReference(workbook, textReference);
    }

    public static String getYAxisSuffixFromTextReference(Workbook workbook, SheetMeta sheetMeta, int yAxisIndex) {
        String textReference = getYAxisSuffixTextReference(sheetMeta, yAxisIndex);

        return SheetChartAPIUtils.getTextFromReference(workbook, textReference);
    }

    public static String getLegendTitleFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getLegend()
                .getTitle().getFontColorThemeOptions().getAccent();
    }

    public static Double getLegendTitleFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getLegend()
                .getTitle().getFontColorThemeOptions().getTone();
    }

    public static String getYAxisMinReference(SheetMeta sheetMeta, int yAxisIndex) {
        YAxesReferences references = SheetChartAPIUtils.softComputeYAxisReferences(sheetMeta, yAxisIndex);

        return references.getMin();
    }

    public static Integer getYAxisMinFromReference(Workbook workbook, SheetMeta sheetMeta, int yAxisIndex) {
        String reference = getYAxisMinReference(sheetMeta, yAxisIndex);
        Double value = SheetChartAPIUtils.getValueFromReference(workbook, reference);

        return value == null ? null : value.intValue();
    }

    public static String getYAxisMaxReference(SheetMeta sheetMeta, int yAxisIndex) {
        YAxesReferences references = SheetChartAPIUtils.softComputeYAxisReferences(sheetMeta, yAxisIndex);

        return references.getMax();
    }

    public static Integer getYAxisMaxFromReference(Workbook workbook, SheetMeta sheetMeta, int yAxisIndex) {
        String reference = getYAxisMaxReference(sheetMeta, yAxisIndex);
        Double value = SheetChartAPIUtils.getValueFromReference(workbook, reference);

        return value == null ? null : value.intValue();
    }

    public static String getYAxisIntervalReference(SheetMeta sheetMeta, int yAxisIndex) {
        YAxesReferences references = SheetChartAPIUtils.softComputeYAxisReferences(sheetMeta, yAxisIndex);

        return references.getInterval();
    }

    public static Integer getYAxisIntervalFromReference(Workbook workbook, SheetMeta sheetMeta, int yAxisIndex) {
        String reference = getYAxisIntervalReference(sheetMeta, yAxisIndex);
        Double value = SheetChartAPIUtils.getValueFromReference(workbook, reference);

        return value == null ? null : value.intValue();
    }

    public static String getDataPropertyDatalabelCustomValueReference(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        DataPropertyReferences references = SheetChartAPIUtils.softComputeIfAbsentDataPropertyReferences(sheetMeta, seriesIndex, dataPropsIndex);

        return references.getDataLabels()
                .getCustomLabel();
    }

    public static String getDataPropertyDatalabelCustomValueFromReference(Workbook workbook, SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        String reference = getDataPropertyDatalabelCustomValueReference(sheetMeta, seriesIndex, dataPropsIndex);

        return SheetChartAPIUtils.getTextFromReference(workbook, reference);
    }

    public static String getSeriesDatalabelsCustomValueReference(SheetMeta sheetMeta, int seriesIndex) {
        SeriesPropertyReferences references = SheetChartAPIUtils.softComputeIfAbsentSeriesPropertyReferences(sheetMeta, seriesIndex);

        return references.getDataLabels()
                .getCustomLabel();
    }

    public static String getSeriesDatalabelsCustomValueFromReference(Workbook workbook, SheetMeta sheetMeta, int seriesIndex) {
        String reference = getSeriesDatalabelsCustomValueReference(sheetMeta, seriesIndex);

        return SheetChartAPIUtils.getTextFromReference(workbook, reference);
    }

    public static String getSharedSeriesDatalabelsCustomValueReference(SheetMeta sheetMeta) {
        return sheetMeta.getTextReferences()
                .getSharedPropertiesReferences()
                .getSeriesPropertyReferences()
                .getDataLabels()
                .getCustomLabel();
    }

    public static String getSharedSeriesDatalabelsCustomValueFromReference(Workbook workbook, SheetMeta sheetMeta) {
        String reference = getSharedSeriesDatalabelsCustomValueReference(sheetMeta);

        return SheetChartAPIUtils.getTextFromReference(workbook, reference);
    }

    public static String getCustomPropsUpColorGradientStops(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getUpColorGradient()
                .getStops();
    }

    public static String getCustomPropsDownColorGradientStops(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getDownColorGradient()
                .getStops();
    }

    public static String getCustomPropsTotalColorGradientStops(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getTotalColorGradient()
                .getStops();
    }

    public static String getSeriesTotalColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getTotalColor()
                .getAccent();
    }

    public static Double getSeriesTotalColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getTotalColor()
                .getTone();
    }

    public static String getCreditsFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getChart()
                .getCredits()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getCreditsFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getChart()
                .getCredits()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getSeriesTrendlineDatalabelsFontColorAccent(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getTrendLine()
                .getDataLabels()
                .getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getSeriesTrendlineDatalabelsFontColorTone(SheetMeta sheetMeta, int seriesIndex) {
        SeriesProperty seriesProperty = SheetChartAPIUtils.softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);

        return seriesProperty.getTrendLine()
                .getDataLabels()
                .getFontColorThemeOptions()
                .getTone();
    }

    public static String getSharedSeriesTrendlineDatalabelsFontColorAccent(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getSharedProperties()
                .getSeriesProperty().getTrendLine()
                .getDataLabels().getFontColorThemeOptions()
                .getAccent();
    }

    public static Double getSharedSeriesTrendlineDatalabelsFontColorTone(SheetMeta sheetMeta) {
        return sheetMeta.getCustomProps().getSharedProperties()
                .getSeriesProperty().getTrendLine()
                .getDataLabels().getFontColorThemeOptions()
                .getTone();
    }

    public static String getSeriesTrendlineDatalabelsCustomValueReference(SheetMeta sheetMeta, int seriesIndex) {
        SeriesPropertyReferences references = SheetChartAPIUtils.softComputeIfAbsentSeriesPropertyReferences(sheetMeta, seriesIndex);

        return references.getTrendline()
                .getDataLabels()
                .getCustomLabel();
    }

    public static String getSeriesTrendlineDatalabelsCustomValueFromReference(Workbook workbook, SheetMeta sheetMeta, int seriesIndex) {
        String reference = SheetChartGetterAPI.getSeriesTrendlineDatalabelsCustomValueReference(sheetMeta, seriesIndex);

        return SheetChartAPIUtils.getTextFromReference(workbook, reference);
    }

    public static String getSharedSeriesTrendlineDatalabelsCustomValueReference(SheetMeta sheetMeta) {
        return sheetMeta.getTextReferences().getSharedPropertiesReferences()
                .getSeriesPropertyReferences().getTrendline()
                .getDataLabels().getCustomLabel();
    }

    public static String getSharedSeriesTrendlineDatalabelsCustomValueFromReference(Workbook workbook, SheetMeta sheetMeta) {
        String reference = SheetChartGetterAPI.getSharedSeriesTrendlineDatalabelsCustomValueReference(sheetMeta);

        return SheetChartAPIUtils.getTextFromReference(workbook, reference);
    }

}
