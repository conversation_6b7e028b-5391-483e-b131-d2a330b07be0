package com.zoho.sheet.knitcharts.chart.sheetchart.meta.datatableoptions.datamanipulationoptions;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class DataManipulationOptions implements ChartOption {

    private AggregationOptions aggregationOptions;

    private FilterOptions filterOptions;

    public AggregationOptions getAggregationOptions() {
        if (aggregationOptions == null) {
            aggregationOptions = new AggregationOptions();
        }
        return aggregationOptions;
    }

    public FilterOptions getFilterOptions() {
        if (filterOptions == null) {
            filterOptions = new FilterOptions();
        }
        return filterOptions;
    }

    private void setAggregationOptions(JSONWrapper json){
        if(json != null) {
            getAggregationOptions().fromJSON(json);
        }
    }

    private void setFilterOptions(JSONWrapper json){
        if(json != null){
            getFilterOptions().fromJSON(json);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setAggregationOptions(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.AggregationOptions.KEY));
        setFilterOptions(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.FilterOptions.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.AggregationOptions.KEY, aggregationOptions);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.FilterOptions.KEY, filterOptions);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
