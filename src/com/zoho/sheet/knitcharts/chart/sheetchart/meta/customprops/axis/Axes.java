package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.axis;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorTheme.ColorThemeOptions;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.title.Title;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Custom propertied for Axis
 * <AUTHOR>
 */
public abstract class Axes implements ChartOption {
    /* Axis title */
    private Title title;
    /* Axis labels */
    private Labels labels;
    /* Axis gridlines */
    private GridLines gridLines;
    /* Axis tick lines */
    private TickLines tickLines;

    private ColorThemeOptions baselineColor;

    public Title getTitle() {
        if (title == null) {
            title = new Title();
        }
        return title;
    }

    public Labels getLabels() {
        if (labels == null) {
            labels = new Labels();
        }
        return labels;
    }

    public GridLines getGridLines() {
        if (gridLines == null) {
            gridLines = new GridLines();
        }
        return gridLines;
    }

    public ColorThemeOptions getBaseLineColor() {
        if (baselineColor == null) {
            baselineColor = new ColorThemeOptions();
        }
        return baselineColor;
    }

    private void setTitle(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getTitle().fromJSON(jsonObject);
        }
    }

    private void setLabels(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getLabels().fromJSON(jsonObject);
        }
    }

    private void setGridLines(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getGridLines().fromJSON(jsonObject);
        }
    }

    public TickLines getTickLines() {
        if (tickLines == null) {
            tickLines = new TickLines();
        }
        return tickLines;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        setLabels(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Labels.KEY));
        setTitle(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Title.KEY));
        setGridLines(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.KEY));
        ChartUtils.setGroupedChartOptions(this::getTickLines, json);
        ChartUtils.setChartOptions(this::getBaseLineColor, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_COLOR));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Labels.KEY, labels);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Title.KEY, title);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.KEY, gridLines);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, tickLines);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_COLOR, baselineColor);

        return isAnyKeyNotNull ? jsonObject : null;
    }
}
