package com.zoho.sheet.knitcharts.chart.sheetchart.meta.datameta;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.datameta.seriestypes.SeriesTypes;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Data Meta used for side panel population purpose
 * <AUTHOR>
 */
public class DataMeta implements ChartOption {

    private SeriesTypes seriesTypes;

    private void setSeriesTypes(JSONObjectWrapper jsonObject){
        if (jsonObject != null) {
            getSeriesTypes().fromJSON(jsonObject);
        }
    }
    public SeriesTypes getSeriesTypes() {
        if (seriesTypes == null) {
            seriesTypes = new SeriesTypes();
        }
        return seriesTypes;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        setSeriesTypes(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.DataMeta.SeriesTypes.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.DataMeta.SeriesTypes.KEY, seriesTypes);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
