package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorpalette.ColorPalette;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.totallabel.TotalLabel;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.caption.Caption;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.chart.Chart;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.legend.Legend;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties.SeriesProperties;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.sharedproperties.SharedProperties;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.subtitle.Subtitle;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.title.Title;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.totaldatalabels.TotalDataLabels;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.xaxis.XAxis;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.YAxis;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Custom Properties for Chart.<br>
 * Note: Here FrameworkChartMetaConstants are used to reduce redundant declaration of same keys.
 * <AUTHOR>
 */
public class CustomProps implements ChartOption {

    private Caption caption;

    private TotalDataLabels totalDataLabels;

    private Legend legend;

    private SeriesProperties seriesProperties;

    private SharedProperties sharedProperties;

    private Subtitle subtitle;

    private Title title;

    private XAxis xAxis;

    private YAxis yAxis;

    private Chart chart;

    private ColorPalette colorPalette;

    private TotalLabel totalLabel;

    public Caption getCaption() {
        if (caption == null) {
            caption = new Caption();
        }
        return caption;
    }

    public TotalLabel getTotalLabel() {
        if (totalLabel == null) {
            totalLabel = new TotalLabel();
        }
        return totalLabel;
    }

    public TotalDataLabels getTotalDataLabels() {
        if (totalDataLabels == null) {
            totalDataLabels = new TotalDataLabels();
        }
        return totalDataLabels;
    }

    public Legend getLegend() {
        if (legend == null) {
            legend = new Legend();
        }
        return legend;
    }

    public SeriesProperties getSeriesProperties() {
        if (seriesProperties == null) {
            seriesProperties = new SeriesProperties();
        }
        return seriesProperties;
    }

    public SharedProperties getSharedProperties() {
        if (sharedProperties == null) {
            sharedProperties = new SharedProperties();
        }
        return sharedProperties;
    }

    public Subtitle getSubtitle() {
        if (subtitle == null) {
            subtitle = new Subtitle();
        }
        return subtitle;
    }

    public Title getTitle() {
        if (title == null) {
            title = new Title();
        }
        return title;
    }

    public XAxis getxAxis() {
        if (xAxis == null) {
            xAxis = new XAxis();
        }
        return xAxis;
    }

    public YAxis getyAxis() {
        if (yAxis == null) {
            yAxis = new YAxis();
        }
        return yAxis;
    }

    public Chart getChart() {
        if (chart == null) {
            chart = new Chart();
        }
        return chart;
    }

    public ColorPalette getColorPalette() {
        if(colorPalette == null) {
            colorPalette = new ColorPalette();
        }

        return colorPalette;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        ChartUtils.setChartOptions(this::getCaption, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Caption.KEY));
        ChartUtils.setChartOptions(this::getTotalDataLabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalDataLabels.KEY));
        ChartUtils.setChartOptions(this::getLegend, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Legend.KEY));
        ChartUtils.setChartOptions(this::getSeriesProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.KEY));
        ChartUtils.setChartOptions(this::getSharedProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SharedProperties.KEY));
        ChartUtils.setChartOptions(this::getTitle, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Title.KEY));
        ChartUtils.setChartOptions(this::getSubtitle, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Subtitle.KEY));
        ChartUtils.setChartOptions(this::getxAxis, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.KEY));
        ChartUtils.setChartOptions(this::getyAxis, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.KEY));
        ChartUtils.setChartOptions(this::getChart, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.KEY));
        ChartUtils.setChartOptions(this::getColorPalette, ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.ColorPalette.KEY));
        ChartUtils.setChartOptions(this::getTotalLabel, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalLabel.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Caption.KEY, caption);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalDataLabels.KEY, totalDataLabels);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Legend.KEY, legend);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.KEY, seriesProperties);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SharedProperties.KEY, sharedProperties);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Title.KEY, title);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Subtitle.KEY, subtitle);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.KEY, xAxis);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.KEY, yAxis);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.KEY, chart);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.ColorPalette.KEY, colorPalette);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalLabel.KEY, totalLabel);

        return isAnyKeyNotNull ? jsonObject : null;
    }
}
