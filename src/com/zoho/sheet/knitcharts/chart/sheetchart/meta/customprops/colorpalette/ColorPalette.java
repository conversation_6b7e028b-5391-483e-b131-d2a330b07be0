package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorpalette;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorTheme.ColorThemeOptions;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Custom properties for color palette
 * <AUTHOR>
 */
public class ColorPalette implements ChartOption {

    private ColorThemeOptions customColor;

    public ColorThemeOptions getCustomColor() {
        if (customColor == null) {
            customColor = new ColorThemeOptions();
        }
        return customColor;
    }

    private void setCustomColor(JSONObjectWrapper jsonObject) {
        if(jsonObject != null) {
            getCustomColor().fromJSON(jsonObject);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setCustomColor(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.ColorPalette.CUSTOM_COLOR));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.ColorPalette.CUSTOM_COLOR, customColor);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
