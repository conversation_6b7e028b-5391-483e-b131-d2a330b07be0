package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.chart;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.gradient.Gradient;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.shadow.Shadow;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorTheme.ColorThemeOptions;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class Chart implements ChartOption {

    private ColorThemeOptions backgroundColor, borderColor;

    private Shadow shadow;

    private Gradient bgGradient;

    private Credits credits;

    public ColorThemeOptions getBackgroundColor() {
        if (backgroundColor == null) {
            backgroundColor = new ColorThemeOptions();
        }
        return backgroundColor;
    }

    public ColorThemeOptions getBorderColor() {
        if (borderColor == null) {
            borderColor = new ColorThemeOptions();
        }
        return borderColor;
    }

    public Shadow getShadow() {
        if (shadow == null) {
            shadow = new Shadow();
        }

        return shadow;
    }

    public Gradient getBgGradient() {
        if (bgGradient == null) {
            bgGradient = new Gradient();
        }
        return bgGradient;
    }

    public Credits getCredits() {
        if (credits == null) {
            credits = new Credits();
        }
        return credits;
    }

    private void setBackgroundColor(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getBackgroundColor().fromJSON(jsonObject);
        }
    }

    private void setBorderColor(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getBorderColor().fromJSON(jsonObject);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setBackgroundColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_COLOR));
        setBorderColor(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.Chart.BORDER_COLOR));
        ChartUtils.setChartOptions(this::getShadow, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BOX_SHADOW));
        ChartUtils.setChartOptions(this::getBgGradient, ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.CustomProps.Chart.BACKGROUND_COLOR_GRADIENT));
        ChartUtils.setChartOptions(this::getCredits, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Credits.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_COLOR, backgroundColor);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.Chart.BORDER_COLOR, borderColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BOX_SHADOW, shadow);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.CustomProps.Chart.BACKGROUND_COLOR_GRADIENT, bgGradient);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Credits.KEY, credits);

        return isAnyKeyNotNull ? jsonObject : null;
    }
}
