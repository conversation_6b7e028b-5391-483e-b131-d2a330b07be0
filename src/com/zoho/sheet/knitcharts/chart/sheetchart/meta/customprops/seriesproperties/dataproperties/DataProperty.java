package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties.dataproperties;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.gradient.Gradient;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties.DataLabels;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties.Shadow;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorTheme.ColorThemeOptions;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Custom properties for Data Properties
 * <AUTHOR>
 */
public class DataProperty implements ChartOption {

    private ColorThemeOptions color;

    private ColorThemeOptions borderColor;

    private DataLabels dataLabels;

    private Shadow shadow;

    private ColorThemeOptions markerColor;

    private ColorThemeOptions markerBorderColor;

    private ColorThemeOptions meanColor;

    private ColorThemeOptions medianColor;

    private ColorThemeOptions outliersColor;

    private ColorThemeOptions whiskerColor;

    private Gradient colorGradient;

    private final int index;

    public DataProperty(int index) {
        this.index = index;
    }

    public ColorThemeOptions getColor() {
        if (color == null) {
            color = new ColorThemeOptions();
        }
        return color;
    }

    public ColorThemeOptions getBorderColor() {
        if (borderColor == null) {
            borderColor = new ColorThemeOptions();
        }
        return borderColor;
    }

    public DataLabels getDataLabels() {
        if (dataLabels == null) {
            dataLabels = new DataLabels();
        }
        return dataLabels;
    }

    public int getIndex() {
        return index;
    }

    private void setColor(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getColor().fromJSON(jsonObject);
        }
    }

    private void setBorderColor(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getBorderColor().fromJSON(jsonObject);
        }
    }

    private void setDataLabels(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getDataLabels().fromJSON(jsonObject);
        }
    }

    public Shadow getShadow() {
        if (shadow == null) {
            shadow = new Shadow();
        }
        return shadow;
    }

    public ColorThemeOptions getMarkerColor() {
        if (markerColor == null) {
            markerColor = new ColorThemeOptions();
        }
        return markerColor;
    }

    public ColorThemeOptions getMarkerBorderColor() {
        if (markerBorderColor == null) {
            markerBorderColor = new ColorThemeOptions();
        }
        return markerBorderColor;
    }

    public ColorThemeOptions getMeanColor() {
        if (meanColor == null) {
            meanColor = new ColorThemeOptions();
        }
        return meanColor;
    }

    public ColorThemeOptions getMedianColor() {
        if (medianColor == null) {
            medianColor = new ColorThemeOptions();
        }
        return medianColor;
    }

    public ColorThemeOptions getOutliersColor() {
        if (outliersColor == null) {
            outliersColor = new ColorThemeOptions();
        }
        return outliersColor;
    }

    public ColorThemeOptions getWhiskerColor() {
        if (whiskerColor == null) {
            whiskerColor = new ColorThemeOptions();
        }
        return whiskerColor;
    }

    public Gradient getColorGradient() {
        if (colorGradient == null) {
            colorGradient = new Gradient();
        }
        return colorGradient;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null){ return; }
        setColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.COLOR));
        setBorderColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.BORDER_COLOR));
        setDataLabels(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY));
        ChartUtils.setChartOptions(this::getShadow, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Shadow.KEY));
        ChartUtils.setChartOptions(this::getMarkerColor, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.COLOR));
        ChartUtils.setChartOptions(this::getMarkerBorderColor, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.BORDER_COLOR));
        ChartUtils.setChartOptions(this::getOutliersColor, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.OUTLIERS_COLOR));
        ChartUtils.setChartOptions(this::getMeanColor, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.MEAN_COLOR));
        ChartUtils.setChartOptions(this::getMedianColor, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.MEDIAN_COLOR));
        ChartUtils.setChartOptions(this::getWhiskerColor, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.WHISKERS_COLOR));
        ChartUtils.setChartOptions(this::getColorGradient, ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.CustomProps.SeriesProperty.DataProperty.COLOR_GRADIENT));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.COLOR, color);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.BORDER_COLOR, borderColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY, dataLabels);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Shadow.KEY, shadow);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.COLOR, markerColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.BORDER_COLOR, markerBorderColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.OUTLIERS_COLOR, outliersColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.MEAN_COLOR, meanColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.MEDIAN_COLOR, medianColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.WHISKERS_COLOR, whiskerColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.CustomProps.SeriesProperty.DataProperty.COLOR_GRADIENT, colorGradient);

        return isAnyKeyNotNull ? jsonObject : null;
    }
}
