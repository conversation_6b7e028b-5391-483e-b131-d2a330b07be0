package com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.series;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.utils.ChartUtils;

public class SeriesPropertyReferences implements ChartOption {

    private DataLabels dataLabels;

    private DataPropertiesReferences dataProperties;

    private Trendline trendline;

    public DataPropertiesReferences getDataProperties() {
        if (dataProperties == null) {
            dataProperties = new DataPropertiesReferences();
        }
        return dataProperties;
    }

    public DataLabels getDataLabels() {
        if (dataLabels == null) {
            dataLabels = new DataLabels();
        }
        return dataLabels;
    }

    public Trendline getTrendline() {
        if (trendline == null) {
            trendline = new Trendline();
        }
        return trendline;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        ChartUtils.setChartOptions(this::getDataLabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY));
        ChartUtils.setChartOptions(this::getDataProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.KEY));
        ChartUtils.setChartOptions(this::getTrendline, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TrendLine.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY, dataLabels);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.KEY, dataProperties);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TrendLine.KEY, trendline);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
