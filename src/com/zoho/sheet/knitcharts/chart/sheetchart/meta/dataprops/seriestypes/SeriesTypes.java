package com.zoho.sheet.knitcharts.chart.sheetchart.meta.dataprops.seriestypes;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class SeriesTypes implements ChartOption {

    private Map<ChartsInteger, SeriesType> seriesTypeMap;

    public SeriesType getSeriesType(int index) {

        if(seriesTypeMap == null) { return null; }

        return seriesTypeMap.get(ChartUtils.toChartsInteger(index));
    }

    public Map<ChartsInteger, SeriesType> getSeriesTypeMap() {
        if (seriesTypeMap == null) {
            seriesTypeMap = new HashMap<>();
        }
        return seriesTypeMap;
    }

    public void setSeriesType(SeriesType seriesType) {
        Objects.requireNonNull(seriesType);
        getSeriesTypeMap().put(ChartUtils.toChartsInteger(seriesType.getIndex()), seriesType);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        ChartUtils.forEachKey(jsonObject, index -> {
            SeriesType seriesType = new SeriesType(Integer.parseInt(index));
            seriesType.fromJSON(ChartUtils.optFromJSONObject(jsonObject, index));

            setSeriesType(seriesType);
        });
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        return ChartUtils.putIfNotNull(jsonObject, seriesTypeMap) ? jsonObject : null;
    }
}
