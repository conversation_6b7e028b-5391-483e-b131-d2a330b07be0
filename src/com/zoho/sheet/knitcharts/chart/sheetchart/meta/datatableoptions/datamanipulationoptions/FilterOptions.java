package com.zoho.sheet.knitcharts.chart.sheetchart.meta.datatableoptions.datamanipulationoptions;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.FilterType;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Filter data manipulation options
 * <AUTHOR>
 */
public class FilterOptions implements ChartOption {

    private ChartsInteger seriesIndex;

    private FilterType filterType;

    private ChartsInteger filterCount;

    public Integer getFilterSeriesIndex(){
        return ChartUtils.getIntegerOrNull(seriesIndex);
    }

    public void setFilterSeriesIndex(Integer seriesIndex){
        this.seriesIndex = ChartUtils.getChartsIntegerOrNull(seriesIndex);
    }

    public FilterType getFilterType() {
        return ChartUtils.requireNonNullElse(filterType, FilterType.TOP);
    }

    public void setFilterType(FilterType filterType) {
        this.filterType = filterType;
    }

    private void setFilterTypeByValue(String value){
        if(value != null) {
            filterType = FilterType.retrieveByValue(value);
        }
    }

    public Integer getFilterCount(){
        return ChartUtils.requireNonNullElse(ChartUtils.getIntegerOrNull(filterCount), 5);
    }

    public void setFilterCount(Integer filterCount){
        this.filterCount = ChartUtils.getChartsIntegerOrNull(filterCount);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        /* Getting filter type from the JSONWrapper Object */
        setFilterTypeByValue(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.FilterOptions.TYPE));
        /* Getting filter count from the JSONWrapper Object */
        setFilterCount(ChartUtils.optIntegerFromJSONObject(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.FilterOptions.COUNT));
        /* Getting filter series index from the JSONWrapper Object */
        setFilterSeriesIndex(ChartUtils.optIntegerFromJSONObject(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.FilterOptions.SERIES_INDEX));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.FilterOptions.TYPE, getFilterType());
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.FilterOptions.COUNT, getFilterCount());
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.DataTableOptions.DataManipulationOptions.FilterOptions.SERIES_INDEX, seriesIndex);

        return isAnyKeyNotNull ? jsonObject : null;
    }
}
