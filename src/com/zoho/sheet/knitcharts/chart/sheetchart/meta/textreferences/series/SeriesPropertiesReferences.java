package com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.series;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.utils.ChartUtils;

import java.util.HashMap;
import java.util.Map;

public class SeriesPropertiesReferences implements ChartOption{

    private Map<ChartsInteger, SeriesPropertyReferences> properties;

    public Map<ChartsInteger, SeriesPropertyReferences> getProperties() {
        if (properties == null) {
            properties = new HashMap<>();
        }
        return properties;
    }

    public SeriesPropertyReferences getProperty(int index) {
        return getProperties().get(ChartUtils.toChartsInteger(index));
    }

    public void add(int index, SeriesPropertyReferences property) {
        getProperties().put(ChartUtils.toChartsInteger(index), property);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        Map<ChartsInteger, SeriesPropertyReferences> referencesMap = ChartUtils.optMapFromJSONObject(jsonObject, SeriesPropertyReferences::new);
        if(referencesMap.isEmpty()) { return; }
        getProperties().putAll(referencesMap);
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, properties);

        return isAnyKeyNotNull ? jsonObject : null;
    }
}
