package com.zoho.sheet.knitcharts.chart.sheetchart.meta;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTable;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class PivotChartSheetMeta extends SheetMeta {

    private ChartsBoolean isFilterPinned;

    public boolean getIsFilterPinned() {
        return ChartUtils.requireNonNullElse(ChartUtils.getBooleanOrNull(isFilterPinned), true);
    }

    public void setIsFilterPinned(Boolean isFilterPinned) {
        this.isFilterPinned = ChartUtils.getChartsBooleanOrNull(isFilterPinned);
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.toJSON()),
                StatelessLambda::newJSONObject);

        jsonObject.put(SheetChartMetaConstants.IS_PIVOT_CHART, true);
        ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.IS_FILTER_PINNED, isFilterPinned);
        return jsonObject;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        super.fromJSON(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setIsFilterPinned(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.IS_FILTER_PINNED));
    }

    @Override
    public JSONWrapper toJSON(Workbook workbook, Chart chart, DataTable dataTable) {
        return toJSON();
    }
}
