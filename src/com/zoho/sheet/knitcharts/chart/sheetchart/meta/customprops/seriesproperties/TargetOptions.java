package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorTheme.ColorThemeOptions;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Custom properties for Target Options
 */
public class TargetOptions implements ChartOption {

    private ColorThemeOptions color;

    public ColorThemeOptions getColor() {
        if (color == null) {
            color = new ColorThemeOptions();
        }
        return color;
    }

    private void setColor(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getColor().fromJSON(jsonObject);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null){ return; }
        setColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TargetOptions.COLOR));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        return ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TargetOptions.COLOR, color) ?
                jsonObject : null;
    }
}
