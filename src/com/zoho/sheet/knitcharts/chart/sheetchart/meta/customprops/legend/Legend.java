package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.legend;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorTheme.FontColorThemeOptions;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.title.Title;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.utils.ChartUtils;

public class Legend extends FontColorThemeOptions {

    private LegendTitle title;

    public Title getTitle() {
        if (title == null) {
            title = new LegendTitle();
        }
        return title;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        super.fromJSON(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        ChartUtils.setChartOptions(this::getTitle, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Legend.TITLE));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.toJSON()), StatelessLambda::newJSONObject);

        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Legend.TITLE, title);
        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
