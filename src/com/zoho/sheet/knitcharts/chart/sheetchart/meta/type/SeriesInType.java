package com.zoho.sheet.knitcharts.chart.sheetchart.meta.type;

import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.Objects;

/**
 * Series In property
 * <AUTHOR>
 */
public enum SeriesInType implements Type {
    ROWS("rows"),                               // NO I18N
    COLUMNS("columns")                          // NO I18N
    ;

    public static SeriesInType retrieveByValue(String value){
        if(Objects.equals(value, ROWS.value)){
            return ROWS;
        }else if(Objects.equals(value, COLUMNS.value)){
            return COLUMNS;
        }else{
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
    }

    private final String value;
    SeriesInType(String value){
        this.value = value;
    }
    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
