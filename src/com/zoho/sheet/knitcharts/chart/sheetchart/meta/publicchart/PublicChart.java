package com.zoho.sheet.knitcharts.chart.sheetchart.meta.publicchart;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.PublicChartType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Used to hold public chart options
 * <AUTHOR>
 */
public class PublicChart implements ChartOption {

    private PublicChartType publicChartType;

    private ChartsString publicChartName;

    private ChartsString publicSpaceChartID;

    public void setPublicChartType(PublicChartType publicChartType) {
        this.publicChartType = publicChartType;
    }

    private void setPublicChartTypeByValue(String value) {
        if(value != null) {
            setPublicChartType(PublicChartType.retrieveByValue(value));
        }
    }

    public PublicChartType getPublicChartType() {
        return this.publicChartType;
    }

    public void setPublicChartName(String publicChartName) {
        this.publicChartName = ChartUtils.getChartsStringOrNull(publicChartName);
    }

    public String getPublicChartName() {
        return ChartUtils.getStringOrNull(publicChartName);
    }

    public void setPublicSpaceChartID(String publicSpaceChartID) {
        this.publicSpaceChartID = ChartUtils.getChartsStringOrNull(publicSpaceChartID);
    }

    public String getPublicSpaceChartID() {
        return ChartUtils.getStringOrNull(this.publicSpaceChartID);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setPublicChartTypeByValue(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.PublicChart.TYPE));
        setPublicChartName(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.PublicChart.PUBLIC_CHART_NAME));
        setPublicSpaceChartID(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.PublicChart.PUBLIC_SPACE_CHART_ID));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.PublicChart.TYPE, publicChartType);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.PublicChart.PUBLIC_CHART_NAME, publicChartName);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.PublicChart.PUBLIC_SPACE_CHART_ID, publicSpaceChartID);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
