package com.zoho.sheet.knitcharts.chart.sheetchart.meta.type;

import com.zoho.sheet.Aggregation.AggEngineConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Aggregation operation type
 * <AUTHOR>
 */
public enum AggregationType implements Type {
    SUM("sum", AggEngineConstants.ValueOperation.SUM),                                              // NO I18N
    COUNT("count", AggEngineConstants.ValueOperation.COUNT),                                        // NO I18N
    COUNT_DISTINCT("countDistinct", AggEngineConstants.ValueOperation.COUNT_DISTINCT),              // NO I18N
    AVG("average", AggEngineConstants.ValueOperation.AVERAGE),                                      // NO I18N
    MIN("min", AggEngineConstants.ValueOperation.MIN),                                              // NO I18N
    MAX("max", AggEngineConstants.ValueOperation.MAX),                                              // NO I18N
    MEDIAN("median", AggEngineConstants.ValueOperation.MEDIAN),                                     // NO I18N
    ACTUAL("actual", AggEngineConstants.ValueOperation.ACTUAL);                                     // NO I18N

    private static Map<String, AggregationType> lookupMap;

    private static void initLookupMap(){
        HashMap<String, AggregationType> tempLookupMap = new HashMap<>();

        for(AggregationType aggregationType: values()){
            tempLookupMap.put(aggregationType.value, aggregationType);
        }
        lookupMap = Collections.unmodifiableMap(tempLookupMap);
    }

    public static AggregationType retrieveByValue(String value){
        if(lookupMap == null) { initLookupMap(); }

        if(Objects.equals(value, "NONE")) { return null; }

        AggregationType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;

    private final AggEngineConstants.ValueOperation engineConstant;

    AggregationType(String value, AggEngineConstants.ValueOperation engineConstant){
        this.value = value;
        this.engineConstant = engineConstant;
    }

    public AggEngineConstants.ValueOperation getEngineConstant() {
        return engineConstant;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
