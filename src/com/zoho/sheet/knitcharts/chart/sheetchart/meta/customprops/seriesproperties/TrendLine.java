package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorTheme.FontColorThemeOptions;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.colorTheme.ColorThemeOptions;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Customer properties for trend line
 * <AUTHOR>
 */
public class TrendLine implements ChartOption {

    private ColorThemeOptions colorThemeOptions;

    private DataLabels dataLabels;

    public ColorThemeOptions getColor() {
        if(colorThemeOptions == null){
            colorThemeOptions = new ColorThemeOptions();
        }
        return colorThemeOptions;
    }

    public DataLabels getDataLabels() {
        if(dataLabels == null){
            dataLabels = new DataLabels();
        }
        return dataLabels;
    }

    private void setColorThemeOptions(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getColor().fromJSON(jsonObject);
        }
    }


    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setColorThemeOptions(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TrendLine.COLOR));
        ChartUtils.setChartOptions(this::getDataLabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TrendLine.COLOR, colorThemeOptions);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY, dataLabels);

        return isAnyKeyNotNull ? jsonObject : null;
    }
}
