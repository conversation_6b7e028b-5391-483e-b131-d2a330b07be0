package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.axis.Axes;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.plotlines.PlotLines;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Custom properties for Y-Axis
 * <AUTHOR>
 */
public class YAxes extends Axes {

    /* index */
    private final int index;

    /* plot lines custom properties */
    private PlotLines plotLines;

    private StackLabels stackLabels;

    public YAxes(int index){
        this.index = index;
    }

    public PlotLines getPlotLines(){
        if (plotLines == null) {
            plotLines = new PlotLines();
        }
        return plotLines;
    }

    public StackLabels getStackLabels() {
        if (stackLabels == null) {
            stackLabels = new StackLabels();
        }
        return stackLabels;
    }

    public int getIndex() {
        return index;
    }

    private void setPlotLines(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getPlotLines().fromJSON(jsonObject);
        }
    }

    private void setStackLabels(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getStackLabels().fromJSON(jsonObject);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null){ return; }
        super.fromJSON(json);
        setPlotLines(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.KEY));
        setStackLabels(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.StackLabels.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.toJSON()),
                StatelessLambda::newJSONObject);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.KEY, plotLines);

        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.StackLabels.KEY, stackLabels);
        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
