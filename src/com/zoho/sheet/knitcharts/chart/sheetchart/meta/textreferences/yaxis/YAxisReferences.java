package com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.yaxis;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class YAxisReferences implements ChartOption {

    private Map<ChartsInteger, YAxesReferences> map;

    private Map<ChartsInteger, YAxesReferences> getMap() {
        if (map == null) {
            map = new HashMap<>();
        }
        return map;
    }

    public List<Integer> getYAxisIndex() {
        if(map == null) { return Collections.emptyList(); }

        return map.keySet().stream()
                .map(ChartsInteger::getValue)
                .collect(Collectors.toList());
    }

    public YAxesReferences getYAxesReferences(int index) {
        if(map == null) { return null; }

        return map.get(new ChartsInteger(index));
    }

    public void addYAxesReferences(YAxesReferences yAxesReferences) {
        getMap()
                .put(new ChartsInteger(yAxesReferences.getIndex()), yAxesReferences);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }
        Map<ChartsInteger, YAxesReferences> map = ChartUtils.optMapFromJSONObject(jsonObject, YAxesReferences::new);

        getMap().putAll(map);
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        return ChartUtils.putIfNotNull(jsonObject, map) ? jsonObject : null;
    }
}
