package com.zoho.sheet.knitcharts.chart.sheetchart.meta;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsDouble;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Spatial parameter of a Chart
 * <AUTHOR>
 */
public class SpatialParameters implements ChartOption {

    /* Spatial parameter such as x, y, height and width */
    private ChartsDouble x, y, height, width;
    /* Start row */
    private ChartsDouble sr;
    /* Start column */
    private ChartsDouble sc;
    /* start row difference */
    private ChartsDouble srd;
    /* start column difference */
    private ChartsDouble scd;

    public Double getX() {
        return ChartUtils.getDoubleOrNull(x);
    }

    public void setX(Double x) {
        this.x = ChartUtils.getChartsDoubleOrNull(x);
    }

    public Double getY() {
        return ChartUtils.getDoubleOrNull(y);
    }

    public void setY(Double y) {
        this.y = ChartUtils.getChartsDoubleOrNull(y);
    }

    public Double getHeight() {
        return ChartUtils.getDoubleOrNull(height);
    }

    public void setHeight(Double height) {
        this.height = ChartUtils.getChartsDoubleOrNull(height);
    }

    public Double getWidth() {
        return ChartUtils.getDoubleOrNull(width);
    }

    public void setWidth(Double width) {
        this.width = ChartUtils.getChartsDoubleOrNull(width);
    }

    public Double getStartRow() {
        return ChartUtils.getDoubleOrNull(sr);
    }

    public void setStartRow(Double sr) {
        this.sr = ChartUtils.getChartsDoubleOrNull(sr);
    }

    public Double getStartColumn() {
        return ChartUtils.getDoubleOrNull(sc);
    }

    public void setStartColumn(Double sc) {
        this.sc = ChartUtils.getChartsDoubleOrNull(sc);
    }

    public Double getStartRowDiff() {
        return ChartUtils.getDoubleOrNull(srd);
    }

    public void setStartRowDiff(Double srd) {
        this.srd = ChartUtils.getChartsDoubleOrNull(srd);
    }

    public Double getStartColumnDiff() {
        return ChartUtils.getDoubleOrNull(scd);
    }

    public void setStartColumnDiff(Double scd) {
        this.scd = ChartUtils.getChartsDoubleOrNull(scd);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null){
            return;
        }
        setX(ChartUtils.optDoubleFromJSONObject(jsonObject, SheetChartMetaConstants.SpatialParameters.X));
        setY(ChartUtils.optDoubleFromJSONObject(jsonObject, SheetChartMetaConstants.SpatialParameters.Y));
        setStartRow(ChartUtils.optDoubleFromJSONObject(jsonObject, SheetChartMetaConstants.SpatialParameters.START_ROW));
        setStartRowDiff(ChartUtils.optDoubleFromJSONObject(jsonObject, SheetChartMetaConstants.SpatialParameters.START_ROW_DIFF));
        setStartColumn(ChartUtils.optDoubleFromJSONObject(jsonObject, SheetChartMetaConstants.SpatialParameters.START_COL));
        setStartColumnDiff(ChartUtils.optDoubleFromJSONObject(jsonObject, SheetChartMetaConstants.SpatialParameters.START_COL_DIFF));
        setHeight(ChartUtils.optDoubleFromJSONObject(jsonObject, SheetChartMetaConstants.SpatialParameters.HEIGHT));
        setWidth(ChartUtils.optDoubleFromJSONObject(jsonObject, SheetChartMetaConstants.SpatialParameters.WIDTH));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.SpatialParameters.X, x);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.SpatialParameters.Y, y);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.SpatialParameters.START_ROW, sr);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.SpatialParameters.START_COL, sc);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.SpatialParameters.START_ROW_DIFF, srd);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.SpatialParameters.START_COL_DIFF, scd);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.SpatialParameters.HEIGHT, height);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.SpatialParameters.WIDTH, width);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
