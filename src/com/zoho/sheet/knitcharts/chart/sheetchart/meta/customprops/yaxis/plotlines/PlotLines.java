package com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.plotlines;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * PlotLines for Custom properties
 * <AUTHOR>
 */
public class PlotLines implements ChartOption {

    private Map<ChartsInteger, PlotLine> plotLineMap;

    public Map<ChartsInteger, PlotLine> getPlotLineMap() {
        if (plotLineMap == null) {
            plotLineMap = new HashMap<>();
        }
        return plotLineMap;
    }

    public void addPlotLine(PlotLine plotLine) {
        Objects.requireNonNull(plotLine);
        getPlotLineMap().put(ChartUtils.toChartsInteger(plotLine.getIndex()), plotLine);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (jsonObject == null) {
            return;
        }
        ChartUtils.forEachKey(jsonObject, key -> {
            PlotLine plotLine = new PlotLine(Integer.parseInt(key));
            plotLine.fromJSON(ChartUtils.optFromJSONObject(jsonObject, key));

            addPlotLine(plotLine);
        });
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        return ChartUtils.putIfNotNull(jsonObject, plotLineMap) ? jsonObject : null;
    }
}
