package com.zoho.sheet.knitcharts.chart.sheetchart.meta.colorpalette;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.ColorPaletteType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.Objects;

/**
 * ColorPalette options for side panel
 * <AUTHOR>
 */

public class ColorPalette implements ChartOption, ChartStyleOption {

    private ChartsString customColor;

    private ChartsBoolean reversed;

    private ColorPaletteType colorPaletteType;

    public void setCustomColor(String color) {
        this.customColor = ChartUtils.getChartsStringOrNull(color);
    }

    public String getCustomColor() {
        return ChartUtils.getStringOrNull(customColor);
    }

    public void setReversed(Boolean reversed) {
        this.reversed = ChartUtils.getChartsBooleanOrNull(reversed);
    }

    public Boolean getReversed() {
        return ChartUtils.getBooleanOrNull(reversed);
    }

    public ColorPaletteType getColorPaletteType() {
        return colorPaletteType;
    }

    public void setColorPaletteType(ColorPaletteType colorPaletteType) {
        this.colorPaletteType = colorPaletteType;
    }

    private void setColorPaletteTypeFromString(String value) {
        if(Objects.nonNull(value)) {
            this.colorPaletteType = ColorPaletteType.retrieveByValue(value);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }

        setCustomColor(ChartUtils.optColorStringFromJSONObject(jsonObject, SheetChartMetaConstants.ColorPalette.CUSTOM_COLOR));
        setReversed(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.ColorPalette.REVERSED));
        setColorPaletteTypeFromString(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.ColorPalette.PALETTE_NAME));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.ColorPalette.CUSTOM_COLOR, customColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.ColorPalette.REVERSED, reversed);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.ColorPalette.PALETTE_NAME, colorPaletteType);
        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.ColorPalette.CUSTOM_COLOR, customColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.ColorPalette.REVERSED, reversed);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.ColorPalette.PALETTE_NAME, colorPaletteType);
        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }

        setCustomColor(ChartUtils.optColorStringFromJSONObject(jsonObject, SheetChartMetaConstants.ColorPalette.CUSTOM_COLOR));
        setReversed(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.ColorPalette.REVERSED));
        setColorPaletteTypeFromString(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.ColorPalette.PALETTE_NAME));
    }

    @Override
    public void resetStyles() {
        reversed = null;
        customColor = null;
        colorPaletteType = null;
    }
}
