package com.zoho.sheet.knitcharts.chart.sheetchart.meta.datameta.seriestypes;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMetaDefaultSupplier;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.DataType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Series Type information
 * <AUTHOR>
 */
public class SeriesType implements ChartOption {

    private DataType type;

    private ChartsString dateFormat;

    private ChartsString name;

    private final int index;

    public SeriesType(int index) {
        this.index = index;
    }

    public void setType(DataType dataType){
        type = dataType;
    }

    private void setTypeByValue(String value){
        if(value != null){
            setType(DataType.retrieveByValue(value));
        }
    }

    public DataType getType(){
        return type;
    }

    public void setDateFormat(String dateFormat){
        this.dateFormat = ChartUtils.getChartsStringOrNull(dateFormat);
    }

    public String getDateFormat(){
        return ChartUtils.getStringOrNull(dateFormat);
    }

    public void setName(String name){
        this.name = ChartUtils.getChartsStringOrNull(name);
    }

    public String getName(){
        return ChartUtils.getStringOrNull(name);
    }

    public String getNameOrDefault(){
        String name = getName();

        if(name == null || name.isEmpty() || name.equals("null")){
            name = String.format("%s %d", SheetMetaDefaultSupplier.DEFAULT_SERIES_NAME_PREFIX, index + 1);                          // NO I18N
        }

        return name;
    }

    public int getIndex() {
        return index;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        setTypeByValue(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.DataMeta.SeriesTypes.TYPE));
        setDateFormat(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.DataMeta.SeriesTypes.DATE_FORMAT));
        setName(ChartUtils.optFromJSONObject(jsonObject, SheetChartMetaConstants.DataMeta.SeriesTypes.NAME));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.DataMeta.SeriesTypes.TYPE, type);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, SheetChartMetaConstants.DataMeta.SeriesTypes.DATE_FORMAT, dateFormat);
        jsonObject.put(SheetChartMetaConstants.DataMeta.SeriesTypes.NAME, getNameOrDefault());
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
