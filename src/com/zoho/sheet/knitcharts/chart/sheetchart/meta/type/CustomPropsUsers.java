package com.zoho.sheet.knitcharts.chart.sheetchart.meta.type;

/**
 * Custom properties users
 * <AUTHOR>
 */
public enum CustomPropsUsers {
    TITLE,
    SUB_TITLE,
    <PERSON>ATA_LABELS,
    <PERSON><PERSON>END,
    <PERSON>_<PERSON><PERSON><PERSON>,
    <PERSON>_<PERSON><PERSON><PERSON>,
    <PERSON>_<PERSON><PERSON><PERSON>,
    <PERSON>_<PERSON>ITLE,
    <PERSON><PERSON><PERSON><PERSON>,
    TOTAL_DATA_LABELS,
    X_MAJOR_GRIDLINES_COLOR,
    Y_MAJOR_GRIDLINES_COLOR,
    X_MINOR_GRIDLINES_COLOR,
    Y_MINOR_GRIDLINES_COLOR,
    CHART_BACKGROUND_COLOR,
    SERIES_COLOR,
    DATA_PROPERTIES_COLOR,
    UP_COLOR,
    DOW<PERSON>_COLOR,
    TARGET_COLOR,
    NEGATIVE_COLOR,
    PLOT_LINE_COLOR,
    PLOT_LINE_TITLE,
    TREND_LINE_COLOR,
    SERIES_BORDER_COLOR,
    DATA_PROPERTY_BORDER_COLOR,
    MARKE<PERSON>_COLOR,
    MARKER_BORDER_COLOR,
    X_BASE_LINE_COLOR,
    CHART_BORDER_COLOR,
    SHARED_SERIES_DATA_LABELS,
    SHARED_SERIES_NEGATIVE_COLOR,
    SHARED_SERIES_BORDER_COLOR,
    SHARED_SERIES_UP_COLOR,
    SHARED_SERIES_DOWN_COLOR,
    SHARED_SERIES_TARGET_COLOR,
    SHARED_SERIES_MARKER_COLOR,
    SHARED_SERIES_MARKER_BORDER_COLOR,
    Y_STACK_LABELS
}
