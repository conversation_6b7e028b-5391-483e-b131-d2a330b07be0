package com.zoho.sheet.knitcharts.chart.frameworkchart.api;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font.Font;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.gradient.Gradient;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.Marker;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.SeriesColorOptions;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.SeriesProperty;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.TrendLine;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.dataproperties.DataProperties;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.dataproperties.DataProperty;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.xaxis.XAxes;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.yaxis.YAxes;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.yaxis.plotlines.PlotLine;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.yaxis.plotlines.PlotLines;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.DataMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.coltypes.ColType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.headers.GroupedHeader;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.headers.Header;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.rowtypes.RowType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.*;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.GradientFunctionType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.TickPosition;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.BorderStyleType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.FrameworkChartAPIUtils;

import java.util.*;

/**
 * Bridge between chart data model and external controllers
 *
 * <AUTHOR>
 */
public final class FrameworkChartAPI {

    public static void setChartColors(ChartMeta chartMeta, List<String> colors){
        chartMeta.getChart().getColors().setColors(colors);
    }

    public static void updateDonutTotalDatalabelsStatus(ChartMeta chartMeta, Boolean status){
        chartMeta.getTotalDatalabels().setEnabled(status);
    }

    public static void updateTotalDatalabelsText(ChartMeta chartMeta, String text){
        chartMeta.getTotalDatalabels().setText(text);
    }

    public static void updateTotalDatalabelsType(ChartMeta chartMeta, TotalDatalabelsType type){
        chartMeta.getTotalDatalabels().setType(type);
    }

    public static void updateTotalDatalabelsFont(ChartMeta chartMeta, Font sourceFont){
        Font targetFont = chartMeta.getTotalDatalabels().getFont();
        FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
    }
    /**
     * Method to update Chart's title text
     *
     * @param chartMeta    Chart Instance
     * @param newTitle Updated title text
     */
    public static void updateChartTitle(ChartMeta chartMeta, String newTitle) {
        chartMeta.getTitle().setText(newTitle);
    }

    public static String getChartTitle(ChartMeta chartMeta) {
        return chartMeta.getTitle().getText();
    }

    public static void updateChartTitleStatus(ChartMeta chartMeta, Boolean status){
        chartMeta.getTitle()
                .setEnabled(status);
    }

    /**
     * Method to update Chart's title H-Align
     *
     * @param chartMeta  Chart Instance
     * @param hAlign Updated H-Align
     */
    public static void updateChartTitleHAlign(ChartMeta chartMeta, String hAlign) {
        chartMeta.getTitle().sethAlign(hAlign);
    }

    /**
     * Method to update Chart's title font styling
     *
     * @param chartMeta      Chart Instance
     * @param sourceFont Source Font instance for update
     */
    public static void updateChartTitleFont(ChartMeta chartMeta, Font sourceFont) {
        Font targetFont = chartMeta.getTitle().getFont();

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
    }

    /**
     * Method to update Chart's Sub title text
     *
     * @param chartMeta       Chart Instance
     * @param newSubtitle Update sub title text
     */
    public static void updateChartSubTitle(ChartMeta chartMeta, String newSubtitle) {
        chartMeta.getSubtitle().setText(newSubtitle);
    }

    public static void updateChartSubTitleStatus(ChartMeta chartMeta, Boolean status){
        chartMeta.getSubtitle()
                .setEnabled(status);
    }

    public static void updateChartSubTitleHAlign(ChartMeta chartMeta, String hAlign){
        chartMeta.getSubtitle().sethAlign(hAlign);
    }

    /**
     * Method to update Chart subtitle font styling
     *
     * @param chartMeta  Chart instance to be updated
     * @param source source for font styling
     */
    public static void updateChartSubtitleFont(ChartMeta chartMeta, Font source) {
        Font target = chartMeta.getSubtitle().getFont();

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(target, source);
    }

    /**
     * Method to update Chart X-Axis title text
     *
     * @param chartMeta         Chart Instance to be updated
     * @param newXAxisTitle updated X-Axis title
     * @param xAxisIndex x-axis index to be updated
     */
    public static void updateChartXAxisTitleWithIndex(ChartMeta chartMeta, String newXAxisTitle, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTitle().setText(newXAxisTitle);
    }

    public static void updateSharedXAxisTitle(ChartMeta chartMeta, String newXAxisTitle) {
        chartMeta.getSharedProperties()
                .getXAxisProperty()
                .getTitle()
                .setText(newXAxisTitle);
    }

    public static void updateChartXAxisTitleStatusWithIndex(ChartMeta chartMeta, Boolean status, int xAxisIndex){
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTitle()
                .setEnabled(status);
    }

    /**
     * Method to update Chart X-Axis title font styling
     *
     * @param chartMeta  Chart Instance to be updated
     * @param source Source for font styling
     */
    public static void updateChartXAxisTitleFontWithIndex(ChartMeta chartMeta, Font source, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(xAxes.getTitle().getFont(), source);
    }

    public static void updateSharedXAxisTitleFont(ChartMeta chartMeta, Font source) {
        FrameworkChartAPIUtils.updateFontStylesIfNotNull(
                chartMeta.getSharedProperties()
                        .getXAxisProperty()
                        .getTitle().getFont(), source);
    }

    public static void updateChartYAxisTitleWithIndex(ChartMeta chartMeta, String newYAxisTitle, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta.getyAxis().addYAxes(yAxes);
        }
        yAxes.getTitle().setText(newYAxisTitle);
    }

    public static void updateSharedYAxisTitle(ChartMeta chartMeta, String newYAxisTitle) {
        chartMeta.getSharedProperties()
                .getYAxisProperty()
                .getTitle()
                .setText(newYAxisTitle);
    }

    public static void updateChartYAxisTitleStatusWithIndex(ChartMeta chartMeta, Boolean status, int yAxisIndex){
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getTitle().setEnabled(status);
    }

    public static void updateChartYAxisTitleFontWithIndex(ChartMeta chartMeta, Font source, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta.getyAxis().addYAxes(yAxes);
        }
        Font target = yAxes.getTitle().getFont();
        FrameworkChartAPIUtils.updateFontStylesIfNotNull(target, source);
    }

    public static void updateChartLegendPosition(ChartMeta chartMeta, LegendPositionType legendPos) {
        chartMeta.getLegend().setPosition(legendPos);
    }

    public static void updateChartLegendFont(ChartMeta chartMeta, Font source) {
        Font target = chartMeta.getLegend().getFont();

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(target, source);
    }

    public static void updateSeriesDataLabelsStatusWithIndex(ChartMeta chartMeta, Boolean status, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels().setStatus(status);
    }

    public static void updateSeriesDataLabelsFormatWithIndex(ChartMeta chartMeta, Collection<DataLabelFormatType> format, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels().getFormat()
                .setFormats(format);
    }

    public static void updateSeriesDataLabelsPositionWithIndex(ChartMeta chartMeta, DataLabelPositionType position, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .setPosition(position);
    }

    public static void updateSeriesDataLabelsFontWithIndex(ChartMeta chartMeta, Font source, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        Font target = seriesProperty.getDataLabels().getFont();

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(target, source);
    }

    public static void updateSeriesDatalabelsFontColor(ChartMeta chartMeta, String color, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        Font target = seriesProperty.getDataLabels().getFont();

        target.setFontColor(color);
    }

    public static void updateSeriesDatalabelsFontStyle(ChartMeta chartMeta, String style, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        Font target = seriesProperty.getDataLabels().getFont();

        target.setFontStyle(style);
    }

    public static void updateSeriesDatalabelsFontWeight(ChartMeta chartMeta, String weight, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        Font target = seriesProperty.getDataLabels().getFont();

        target.setFontWeight(weight);
    }

    public static void updateSeriesDatalabelsFontSize(ChartMeta chartMeta, String size, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        Font target = seriesProperty.getDataLabels().getFont();

        target.setFontSize(size);
    }

    // ---------------------------------- Start of Shared properties API -------------------------------------\\

    public static void updateSharedSeriesOutliersStatus(ChartMeta chartMeta, Boolean status) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getBoxPlotSeriesProperties()
                .setShowOutliers(status);
    }

    public static void updateSharedSeriesMeanMarkerStatus(ChartMeta chartMeta, Boolean status) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getBoxPlotSeriesProperties()
                .setShowMeanMarkers(status);
    }

    public static void updateSharedSeriesMeanColor(ChartMeta chartMeta, String color) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getBoxPlotSeriesProperties()
                .setMeanColor(color);
    }

    public static void updateSharedSeriesMedianColor(ChartMeta chartMeta, String color) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getBoxPlotSeriesProperties()
                .setMedianColor(color);
    }

    public static void updateSharedSeriesOutliersColor(ChartMeta chartMeta, String color) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getBoxPlotSeriesProperties()
                .setOutliersColor(color);
    }

    public static void updateSharedSeriesWhiskersColor(ChartMeta chartMeta, String color) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getBoxPlotSeriesProperties()
                .setWhiskersColors(color);
    }

    public static void updateSharedSeriesDataLabelsStatus(ChartMeta chartMeta, Boolean status){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .setStatus(status);
    }

    public static void updateSharedSeriesDataLabelsPosition(ChartMeta chartMeta, DataLabelPositionType positionType){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .setPosition(positionType);
    }

    public static void addSharedSeriesDataLabelsFormat(ChartMeta chartMeta, DataLabelFormatType formatType){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .getFormat()
                .addFormat(formatType);
    }

    public static void addSharedSeriesDataLabelsFormats(ChartMeta chartMeta, List<DataLabelFormatType> formatTypes){
        if(formatTypes == null || formatTypes.isEmpty()){ return; }
        for(DataLabelFormatType formatType: formatTypes){
            addSharedSeriesDataLabelsFormat(chartMeta, formatType);
        }
    }

    public static void updateSharedSeriesDataLabelsFormats(ChartMeta chartMeta, Collection<DataLabelFormatType> formatTypes) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .getFormat()
                .setFormats(formatTypes);
    }

    public static void updateSharedSeriesDataLabelsFont(ChartMeta chartMeta, Font sourceFont){
        Font targetFont = chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels().getFont();
        FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
    }

    public static void updateSharedSeriesDatalabelsFontColor(ChartMeta chartMeta, String color){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels().getFont()
                .setFontColor(color);
    }

    public static void updateSharedSeriesDatalabelsFontSize(ChartMeta chartMeta, String size) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels().getFont()
                .setFontSize(size);
    }

    public static void updateSharedSeriesDatalabelsFontWeight(ChartMeta chartMeta, String weight) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels().getFont()
                .setFontWeight(weight);
    }

    public static void updateSharedSeriesDatalabelsFontStyle(ChartMeta chartMeta, String style) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels().getFont()
                .setFontStyle(style);
    }

    public static void updateSharedSeriesTrendLineStatus(ChartMeta chartMeta, Boolean status){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getTrendLine()
                .setEnabled(status);
    }

    public static void updateSharedSeriesTrendLineType(ChartMeta chartMeta, TrendLineType trendLineType){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getTrendLine()
                .setType(trendLineType);
    }

    public static void updateSharedSeriesTrendLineOrder(ChartMeta chartMeta, Integer order){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getTrendLine()
                .setOrder(order);
    }

    public static void updateSharedSeriesTrendLineColorOpacity(ChartMeta chartMeta, Double opacity){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getTrendLine()
                .setColorOpacity(opacity);
    }

    public static void updateSharedSeriesThreshold(ChartMeta chartMeta, Integer threshold){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .setThreshold(threshold);
    }

    public static void updateSharedSeriesThresholdColor(ChartMeta chartMeta, String negativeColor){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesColorOptions()
                .setNegativeColor(negativeColor);
    }

    public static void updateSharedSeriesThresholdOpacity(ChartMeta chartMeta, Double opacity){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesColorOptions()
                .setNegativeColorOpacity(opacity);
    }


    public static void disableSharedSeriesThreshold(ChartMeta chartMeta){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .setThreshold(null);
    }

    /**
     * Method to update cumulate status in the shared properties.<br>
     * Note: Cumulate option is applicable for race chart.
     * @param chartMeta ChartMeta to be updated
     * @param status Updated status
     */
    public static void updateSharedSeriesCumulateStatus(ChartMeta chartMeta, Boolean status){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .setEnableCumulation(status);
    }

    public static void updateSeriesCumulateStatus(ChartMeta chartMeta, Boolean status, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .setEnableCumulation(status);
    }

    /**
     * Method to update decimals in the shared properties.<br>
     * Note: Decimals is applicable for race chart
     * @param chartMeta ChartMeta to be updated
     * @param decimals Updated Decimals
     */
    public static void updateSharedSeriesDecimals(ChartMeta chartMeta, Integer decimals){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .setDecimals(decimals);
    }

    /**
     * Method to update target color in the shared properties.<br>
     * Note: target color is applicable for Bullet charts.<br>
     * In old charts target color is last series color.
     * @param chartMeta Chart Meta to be updated
     * @param color Updated target color
     */
    public static void updateSharedSeriesTargetColor(ChartMeta chartMeta, String color){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getTargetOptions()
                .setColor(color);
    }

    /**
     * Method to update start angle in the shared properties.<br>
     * Note: Start angle is applicable for pie chart and parliament chart types
     * @param chartMeta Chart Meta to be updated
     * @param startAngle Updated start angle
     */
    public static void updateSharedSeriesStartAngle(ChartMeta chartMeta, Integer startAngle){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .setStartAngle(startAngle);
    }

    /**
     * Method to update end angle in the Shared properties.<br>
     * Note: End angle is applicable for parliament chart types
     * @param chartMeta ChartMeta instance
     * @param endAngle Updated end angle
     */
    public static void updateSharedSeriesEndAngle(ChartMeta chartMeta, Integer endAngle){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .setEndAngle(endAngle);
    }

    public static void updateSharedSeriesColorOpacity(ChartMeta chartMeta, Double opacity){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesColorOptions()
                .setColorOpacity(opacity);
    }

    public static void updateSharedSeriesBorderColor(ChartMeta chartMeta, String borderColor){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getBorder()
                .getBorderOptions()
                .setColor(borderColor);
    }

    public static void updateSharedSeriesAreaOpacity(ChartMeta chartMeta, Double opacity){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesColorOptions()
                .setAreaOpacity(opacity);
    }

    public static void updateSharedSeriesMarkerStatus(ChartMeta chartMeta, Boolean status){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getMarker()
                .setStatus(status);
    }

    public static void updateSharedSeriesMarkerSize(ChartMeta chartMeta, Integer size){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getMarker()
                .setSize(size);
    }

    public static void updateSharedSeriesMarkerShape(ChartMeta chartMeta, MarkerShapeType markerShapeType){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getMarker()
                .setShape(markerShapeType);
    }


    public static void updateSharedSeriesMarkerColor(ChartMeta chartMeta, String markerColor){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getMarker()
                .setColor(markerColor);
    }

    public static void updateSharedSeriesMarkerBorderColor(ChartMeta chartMeta, String markerBorderColor){
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getMarker()
                .setBorderColor(markerBorderColor);
    }

    // ---------------------------------- End of Shared properties API -------------------------------------\\

    public static void updateSeriesThresholdValueWithIndex(ChartMeta chartMeta, Integer thresholdValue, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setThreshold(thresholdValue);
    }

    public static void updateSeriesThresholdColorWithIndex(ChartMeta chartMeta, String thresholdColor, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions().setNegativeColor(thresholdColor);
    }

    public static void updateSeriesThresholdOpacity(ChartMeta chartMeta, Double opacity, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .setNegativeColorOpacity(opacity);
    }

    public static void disableSeriesThresholdWithIndex(ChartMeta chartMeta, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setThreshold(null);
    }

    public static void updateTrendLineStatusWithIndex(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getTrendLine()
                .setEnabled(status);
    }

    public static void updateTrendLineTypeWithIndex(ChartMeta chartMeta, TrendLineType type, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getTrendLine()
                .setType(type);
    }

    public static void updateTrendLineOrderWithIndex(ChartMeta chartMeta, Integer order, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        updateTrendLineOrder(order, seriesProperty);
    }

    public static void updateTrendLineOrder(Integer order, SeriesProperty seriesProperty) {
        TrendLine trendLine = seriesProperty.getTrendLine();
        TrendLineType trendLineType = trendLine.getType();

        if(trendLineType == TrendLineType.POLYNOMIAL || trendLineType == TrendLineType.MOVING_AVERAGE){
            trendLine.setOrder(order);
        }
    }

    public static void updateTrendLineStyleTypeWithIndex(ChartMeta chartMeta, LineStyleType lineType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getTrendLine().setLineType(lineType);
    }

    public static void updateTrendLineColorWithIndex(ChartMeta chartMeta, String lineColor, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getTrendLine()
                .setColor(lineColor);
    }

    public static void updateTrendLineColorOpacity(ChartMeta chartMeta, Double colorOpacity, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getTrendLine()
                .setColorOpacity(colorOpacity);
    }

    /**
     * Method to update Series color for the given series
     *
     * @param chartMeta       Chart Instance
     * @param color       Series Color
     * @param seriesIndex Series Index
     */
    public static void updateSeriesColorWithIndex(ChartMeta chartMeta, String color, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions().setColor(color);
    }

    public static void updateSeriesLineTypeWithIndex(ChartMeta chartMeta, LineStyleType lineType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setLineType(lineType);
    }

    public static void updateSeriesAreaOpacityWithCount(ChartMeta chartMeta, Double opacity, int seriesCount) {
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = FrameworkChartAPIUtils.fillUpSeriesProperties(chartMeta, seriesCount);
        seriesPropertyMap.forEach((seriesIndex, seriesProperty) -> seriesProperty.getSeriesColorOptions().setAreaOpacity(opacity));
    }

    public static void updateSeriesAreaOpacityWithIndex(ChartMeta chartMeta, Double opacity, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions().setAreaOpacity(opacity);
    }

    public static void updateSeriesBorderColorWithCount(ChartMeta chartMeta, String borderColor, int seriesCount){
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = FrameworkChartAPIUtils.fillUpSeriesProperties(chartMeta, seriesCount);
        seriesPropertyMap.forEach((seriesIndex, seriesProperty) ->
                seriesProperty.getBorder()
                        .getBorderOptions().setColor(borderColor)
        );
    }

    public static void updateSeriesBorderColorWithIndex(ChartMeta chartMeta, String borderColor, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBorder().getBorderOptions().setColor(borderColor);
    }

    public static void updateSeriesColorOpacity(ChartMeta chartMeta, Double colorOpacity, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions().setColorOpacity(colorOpacity);
    }

    /**
     * Method to update marker status for all series
     *
     * @param chartMeta     Chart Instance
     * @param isEnabled Boolean to state whether marker enabled or disabled
     */
    public static void updateSeriesMarkerStatusWithCount(ChartMeta chartMeta, Boolean isEnabled, int seriesCount) {
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = FrameworkChartAPIUtils.fillUpSeriesProperties(chartMeta, seriesCount);

        seriesPropertyMap.forEach((seriesIndex, seriesProperty) -> {
            Marker marker = seriesProperty.getMarker();
            marker.setStatus(isEnabled);
        });
    }

    /**
     * Method to update marker status for given series
     *
     * @param chartMeta       Chart Instance
     * @param isEnabled   Boolean to state whether marker enabled or disabled
     * @param seriesIndex Series Index
     */
    public static void updateSeriesMarkerStatusWithIndex(ChartMeta chartMeta, Boolean isEnabled, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        Marker marker = seriesProperty.getMarker();
        marker.setStatus(isEnabled);
    }

    /**
     * Method to update marker shape for all series
     *
     * @param chartMeta       Chart Instance to update marker shape
     * @param markerShape Updated marker shape
     */
    public static void updateSeriesMarkerShapeWithCount(ChartMeta chartMeta, MarkerShapeType markerShape, int seriesCount) {
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = FrameworkChartAPIUtils.fillUpSeriesProperties(chartMeta, seriesCount);

        seriesPropertyMap.forEach((seriesIndex, seriesProperty) -> {
            Marker marker = seriesProperty.getMarker();
            marker.setShape(markerShape);
        });
    }

    /**
     * Method to update marker shape for the given series
     *
     * @param chartMeta       Chart Instance to update marker shape
     * @param markerShape Updated marker shape
     * @param seriesIndex Series to be updated
     */
    public static void updateSeriesMarkerShapeWithIndex(ChartMeta chartMeta, MarkerShapeType markerShape, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        Marker marker = seriesProperty.getMarker();
        marker.setShape(markerShape);
    }

    /**
     * Method to update marker size for all series
     *
     * @param chartMeta      Chart Instance
     * @param markerSize Updated marker size
     */
    public static void updateSeriesMarkerSizeWithCount(ChartMeta chartMeta, Integer markerSize, int seriesCount) {
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = FrameworkChartAPIUtils.fillUpSeriesProperties(chartMeta, seriesCount);

        seriesPropertyMap.forEach((seriesIndex, seriesProperty) -> {
            Marker marker = seriesProperty.getMarker();
            marker.setSize(markerSize);
        });
    }

    /**
     * Method to update marker size for the given series
     *
     * @param chartMeta       Chart instance
     * @param markerSize  updated marker size
     * @param seriesIndex Series Index to update
     */
    public static void updateSeriesMarkerSizeWithIndex(ChartMeta chartMeta, Integer markerSize, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        Marker marker = seriesProperty.getMarker();
        marker.setSize(markerSize);
    }

    /**
     * Method to update marker color for all the series
     *
     * @param chartMeta       Chart Instance to be updated
     * @param markerColor updated marker color
     */
    public static void updateSeriesMarkerColorWithCount(ChartMeta chartMeta, String markerColor, int seriesCount) {
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = FrameworkChartAPIUtils.fillUpSeriesProperties(chartMeta, seriesCount);

        seriesPropertyMap.forEach((seriesIndex, seriesProperty) -> {
            Marker marker = seriesProperty.getMarker();
            marker.setColor(markerColor);
        });
    }

    /**
     * Method to update marker color for the given series
     *
     * @param chartMeta       Chart instance to be updated
     * @param markerColor updated marker color
     * @param seriesIndex Series Index
     */
    public static void updateSeriesMarkerColorWithIndex(ChartMeta chartMeta, String markerColor, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        Marker marker = seriesProperty.getMarker();
        marker.setColor(markerColor);
    }

    /**
     * Method to update marker border color for all series
     *
     * @param chartMeta       Chart Instance to be updated
     * @param borderColor Updated border color
     */
    public static void updateSeriesMarkerBorderColorWithCount(ChartMeta chartMeta, String borderColor, int seriesCount) {
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = FrameworkChartAPIUtils.fillUpSeriesProperties(chartMeta, seriesCount);
        seriesPropertyMap.forEach((seriesIndex, seriesProperty) -> {
            Marker marker = seriesProperty.getMarker();
            marker.setBorderColor(borderColor);
        });
    }

    /**
     * Method to update marker border color for the given series
     *
     * @param chartMeta       Chart Instance to be updated
     * @param borderColor updated border color
     * @param seriesIndex Series Index
     */
    public static void updateSeriesMarkerBorderColorWithIndex(ChartMeta chartMeta, String borderColor, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        Marker marker = seriesProperty.getMarker();
        marker.setBorderColor(borderColor);
    }

    /**
     * Method to update Chart X-Axis Inverted Status for given x-axis index
     *
     * @param chartMeta      Chart Instance
     * @param isInverted updates Inverted status
     * @param xAxisIndex x-axis index to be updated
     */
    public static void updateXAxisInvertedStatusWithIndex(ChartMeta chartMeta, Boolean isInverted, int xAxisIndex) {
        XAxes xAxes = chartMeta.getxAxis().getXAxes(xAxisIndex);

        if(ChartUtils.isNull(xAxes)){
            xAxes = new XAxes(xAxisIndex);
            chartMeta.getxAxis().addXAxes(xAxes);
        }

        xAxes.setReversed(isInverted);
    }

    /**
     * Method to update Chart X-Axis label status for given x-axis index
     *
     * @param chartMeta     Chart Instance to be updated
     * @param isEnabled Updated label status
     */
    public static void updateXAxisLabelStatusWithIndex(ChartMeta chartMeta, Boolean isEnabled, int xAxisIndex) {
        XAxes xAxes = chartMeta.getxAxis().getXAxes(xAxisIndex);

        if(ChartUtils.isNull(xAxes)){
            xAxes = new XAxes(xAxisIndex);
            chartMeta.getxAxis().addXAxes(xAxes);
        }
        xAxes.getLabels().setEnabled(isEnabled);
    }

    /**
     * Method to update Chart X-Axis label slant angle
     *
     * @param chartMeta      Chart Instance to be updated
     * @param slantAngle Updated label slant angle
     */
    public static void updateXAxisLabelSlantAngleWithIndex(ChartMeta chartMeta, String slantAngle, int xAxisIndex) {
        XAxes xAxes = chartMeta.getxAxis().getXAxes(xAxisIndex);

        if(ChartUtils.isNull(xAxes)){
            xAxes = new XAxes(xAxisIndex);
            chartMeta.getxAxis().addXAxes(xAxes);
        }

        xAxes.getLabels().setRotation(slantAngle);
    }

    /**
     * Method to update Chart X-Axis label stagger lines
     *
     * @param chartMeta        Chart instance to be updated
     * @param staggerLines Updated Stagger lines
     */
    public static void updateXAxisLabelsStaggerLinesWithIndex(ChartMeta chartMeta, String staggerLines, int xAxisIndex) {
        XAxes xAxes = chartMeta.getxAxis().getXAxes(xAxisIndex);

        if(ChartUtils.isNull(xAxes)){
            xAxes = new XAxes(xAxisIndex);
            chartMeta.getxAxis().addXAxes(xAxes);
        }

        xAxes.getLabels().setStaggerLines(staggerLines);
    }

    public static void updateXAxisLabelsTextOverFlow(ChartMeta chartMeta, TextOverFlowType textOverFlow, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getLabels()
                .setTextOverFlow(textOverFlow);
    }

    /**
     * Method to update Chart X-Axis label font styling
     *
     * @param chartMeta      Chart Instance to be updated
     * @param sourceFont Source font styling
     */
    public static void updateXAxisLabelsFontStyleWithIndex(ChartMeta chartMeta, Font sourceFont, int xAxisIndex) {
        XAxes xAxes = chartMeta.getxAxis().getXAxes(xAxisIndex);

        if(ChartUtils.isNull(xAxes)){
            xAxes = new XAxes(xAxisIndex);
            chartMeta.getxAxis().addXAxes(xAxes);
        }
        FrameworkChartAPIUtils.updateFontStylesIfNotNull(xAxes.getLabels().getFont(), sourceFont);
    }

    /**
     * Method to update Y-Axis label status
     *
     * @param chartMeta      Chart Instance to be updated
     * @param isEnabled  Updated Y-Axis label status
     * @param yAxisIndex Y-Axis Index, when Multiple Y-Axis is not enabled, 0 is used as Index
     */
    public static void updateYAxisLabelStatusWithIndex(ChartMeta chartMeta, Boolean isEnabled, int yAxisIndex) {
        YAxes yAxes = chartMeta
                .getyAxis()
                .getYAxes(yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.getLabels()
                .setEnabled(isEnabled);
    }

    /**
     * Method to update Y-Axis label font styling
     *
     * @param chartMeta      Chart Instance to be updated
     * @param sourceFont Source font for updated font styling
     * @param yAxisIndex Y-Axis Index, when Multiple Y-Axis is not enabled, 0 is uses as Index
     */
    public static void updateYAxisLabelFontStyleWithIndex(ChartMeta chartMeta, Font sourceFont, int yAxisIndex) {
        YAxes yAxes = chartMeta
                .getyAxis()
                .getYAxes(yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        Font targetFont = yAxes.getLabels()
                .getFont();

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
    }

    /**
     * Method to update Y-Axis Scale type
     *
     * @param chartMeta      Chart Instance to be updated
     * @param type       Updated Scale type. either "linear" | "logarithmic"
     * @param yAxisIndex Y-Axis Index, when Multiple Y-Axis is not enabled, 0 is used as Index
     */
    public static void updateYAxisScaleTypeWithIndex(ChartMeta chartMeta, YAxisScaleType type, int yAxisIndex) {
        YAxes yAxes = chartMeta
                .getyAxis()
                .getYAxes(yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.setType(type);
    }

    /**
     * Updates Y-Axis Log scale base value
     *
     * @param chartMeta      Chart Instance to be updated
     * @param baseValue  Updates base value
     * @param yAxisIndex Y-Axis Index, When multiple Y-Axis is not enabled, 0 is used as Index
     */
    public static void updateYAxisLogBaseValueWithIndex(ChartMeta chartMeta, Integer baseValue, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.setBaseValue(baseValue);
    }

    /**
     * Updates plot line title text
     *
     * @param chartMeta         Chart to be updated
     * @param title         Updated title text
     * @param yAxisIndex    Y-Axis containing the updated plot line
     * @param plotLineIndex Index of the updated plot line
     * @throws IllegalArgumentException When Invalid Y-Axis Index provided or Invalid Plot line value
     */
    public static void updatePlotLineTitle(ChartMeta chartMeta, String title, int yAxisIndex, int plotLineIndex) {
        PlotLine plotLine = FrameworkChartAPIUtils.computePlotLineIfAbsent(chartMeta, yAxisIndex, plotLineIndex);

        plotLine.getLabel()
                .setText(title);
    }

    /**
     * updates plot line value
     *
     * @param chartMeta         Chart to updated
     * @param value         Updated title text
     * @param yAxisIndex    Y-Axis containing the updated plot line
     * @param plotLineIndex Index of the updated plot line
     * @throws IllegalArgumentException When Invalid Y-Axis Index provided or Invalid Plot line value
     */
    public static void updatePlotLineValue(ChartMeta chartMeta, Double value, int yAxisIndex, int plotLineIndex) {
        PlotLine plotLine = FrameworkChartAPIUtils.computePlotLineIfAbsent(chartMeta, yAxisIndex, plotLineIndex);

        plotLine.setValue(value);
    }

    /**
     * Updates the font styling for the given plot line
     *
     * @param chartMeta         Chart to be updated
     * @param sourceFont    Source for the updated font styling
     * @param yAxisIndex    Y-Axis Index containing plot line
     * @param plotLineIndex Index of the updated plot line
     * @throws IllegalArgumentException When Invalid Y-Axis Index provided or Invalid Plot line value
     */
    public static void updatePlotLineFontStyle(ChartMeta chartMeta, Font sourceFont, int yAxisIndex, int plotLineIndex) {
        PlotLine plotLine = FrameworkChartAPIUtils.computePlotLineIfAbsent(chartMeta, yAxisIndex, plotLineIndex);
        Font target = plotLine.getLabel().getFont();

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(target, sourceFont);
    }

    /**
     * Updates plot line style type
     *
     * @param chartMeta         Chart Instance to be updated
     * @param lineType      update line type
     * @param yAxisIndex    Y-Axis Index containing plot line
     * @param plotLineIndex Index of the updated plot line index
     * @throws IllegalArgumentException When Invalid Y-Axis Index provided or Invalid Plot line value
     */
    public static void updatePlotLineLineType(ChartMeta chartMeta, LineStyleType lineType, int yAxisIndex, int plotLineIndex) {
        PlotLine plotLine = FrameworkChartAPIUtils.computePlotLineIfAbsent(chartMeta, yAxisIndex, plotLineIndex);

        plotLine.setLineType(lineType);
    }

    /**
     * Updates plot line color value
     *
     * @param chartMeta         Chart Instance to be updated
     * @param updatedColor  Updated color value
     * @param yAxisIndex    Y-Axis Index containing plot line
     * @param plotLineIndex Index of the updated plot line index
     * @throws IllegalArgumentException When Invalid Y-Axis Index provided or Invalid Plot line value
     */
    public static void updatePlotLineColor(ChartMeta chartMeta, String updatedColor, int yAxisIndex, int plotLineIndex) {
        PlotLine plotLine = FrameworkChartAPIUtils.computePlotLineIfAbsent(chartMeta, yAxisIndex, plotLineIndex);

        plotLine.setLineColor(updatedColor);
    }

    public static void updatePlotlineWidth(ChartMeta chartMeta, Integer width, int yAxisIndex, int plotLineIndex) {
        PlotLine plotLine = FrameworkChartAPIUtils.computePlotLineIfAbsent(chartMeta, yAxisIndex, plotLineIndex);

        plotLine.setLineWidth(width);
    }

    /**
     * Method to update given Y-Axes min value
     *
     * @param chartMeta      Chart Instance to be updated
     * @param minValue   Updated Min value
     * @param yAxisIndex Y-Axis index to be updated
     */
    public static void updateYAxisRangeMinValue(ChartMeta chartMeta, Integer minValue, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.setMin(minValue);
    }

    /**
     * Method to update given Y-Axes max value
     *
     * @param chartMeta      Chart Instance to be updated
     * @param maxValue   Updated Max value
     * @param yAxisIndex Y-Axis index to be updated
     */
    public static void updateYAxisRangeMaxValue(ChartMeta chartMeta, Integer maxValue, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.setMax(maxValue);
    }

    /**
     * Method to update given Y-Axes interval
     *
     * @param chartMeta      Chart Instance to be updated
     * @param interval   updated interval value
     * @param yAxisIndex Y-Axis index to be updated
     */
    public static void updateYAxisRangeInterval(ChartMeta chartMeta, Integer interval, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.setTickInterval(interval);
    }

    /**
     * Method to update given Y-Axes format type
     *
     * @param chartMeta      Chart Instance to be updated
     * @param formatType Updated format Type
     * @param yAxisIndex Index of the updated Y-Axis
     * @throws IllegalArgumentException when invalid Y-Axis index passed
     */
    public static void updateYAxisUnitFormat(ChartMeta chartMeta, YAxisUnitFormatType formatType, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }

        yAxes.setUnitFormat(formatType);
    }

    public static void updateYAxisUnitFormatCustomValue(ChartMeta chartMeta, double customValue, int yAxisIndex){
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.setCustomValue(customValue);
    }

    /**
     * Method to update given Y-Axes prefix value
     *
     * @param chartMeta      Chart Instance to be updated
     * @param prefix     updated prefix value
     * @param yAxisIndex Index of the updated Y-Axis
     * @throws IllegalArgumentException When invalid Y-Axis index passed
     */
    public static void updateYAxisPrefix(ChartMeta chartMeta, String prefix, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }

        yAxes.setPrefix(prefix);
    }

    /**
     * Method to update given Y-Axes suffix value
     *
     * @param chartMeta      Chart Instance to be updated
     * @param suffix     updated suffix value
     * @param yAxisIndex Index of the updated Y-Axis
     * @throws IllegalArgumentException When invalid Y-Axis index passed
     */
    public static void updateYAxisSuffix(ChartMeta chartMeta, String suffix, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.setSuffix(suffix);
    }

    /**
     * Updates the X-Axis Major grid line status
     *
     * @param chartMeta     Chart Instance to updated
     * @param isEnabled Update status
     */
    public static void updateXAxisMajorGLStatus(ChartMeta chartMeta, Boolean isEnabled, int index) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, index);

        xAxes.getGridLines().setMajorGLEnabled(isEnabled);
    }

    /**
     * Updated the X-Axis Major grid line style type
     *
     * @param chartMeta         Chart Instance to be updated
     * @param lineStyleType updated line style type
     */
    public static void updateXAxisMajorGLType(ChartMeta chartMeta, LineStyleType lineStyleType, int index) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, index);

        xAxes.getGridLines().setMajorGLType(lineStyleType);
    }

    /**
     * Updates the X-Axis major grid line color
     *
     * @param chartMeta     Chart Instance to be updated
     * @param lineColor updated line color
     */
    public static void updateXAxisMajorGLColor(ChartMeta chartMeta, String lineColor, int index) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, index);

        xAxes.getGridLines().setMajorGLColor(lineColor);
    }

    /**
     * Updates the X-Axis major grid line count
     *
     * @param chartMeta       Chart Instance to be updated
     * @param glCountType Grid Line Count
     */
    public static void updateXAxisMajorGLCount(ChartMeta chartMeta, GridLineCountType glCountType, int index) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, index);

        xAxes.getGridLines().setMajorGLCount(glCountType);
    }

    public static void updateXAxisMinorGLStatus(ChartMeta chartMeta, Boolean status, int index) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, index);

        xAxes.getGridLines().setMinorGLEnabled(status);
    }

    public static void updateXAxisMinorGLType(ChartMeta chartMeta, LineStyleType lineStyleType, int index) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, index);

        xAxes.getGridLines().setMinorGLType(lineStyleType);
    }

    public static void updateXAxisMinorGLColor(ChartMeta chartMeta, String lineColor, int index) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, index);

        xAxes.getGridLines().setMinorGLColor(lineColor);
    }

    /**
     * Updates Y-Axis major grid line status
     *
     * @param chartMeta      Chart Instance to be updated
     * @param isEnabled  updated status
     * @param yAxisIndex Y-Axis
     */
    public static void updateYAxisMajorGLStatus(ChartMeta chartMeta, Boolean isEnabled, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta.getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.getGridLines()
                .setMajorGLEnabled(isEnabled);
    }

    /**
     * Updates Y-Axis Major grid lines count
     *
     * @param chartMeta      Chart Instance to be updated
     * @param type       GridLineCountType
     * @param yAxisIndex Y-Axis Index
     */
    public static void updateYAxisMajorGLCount(ChartMeta chartMeta, GridLineCountType type, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta.getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.getGridLines().setMajorGLCount(type);
    }

    /**
     * Updates Y-Axis Major Grid line type
     *
     * @param chartMeta      Chart Instance to be updated
     * @param type       LineStyleType
     * @param yAxisIndex Y-Axis Index
     */
    public static void updateYAxisMajorGLType(ChartMeta chartMeta, LineStyleType type, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta.getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.getGridLines().setMajorGLType(type);
    }

    public static void updateYAxisMajorGLColor(ChartMeta chartMeta, String color, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta.getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.getGridLines().setMajorGLColor(color);
    }

    public static void updateYAxisMinorGLStatus(ChartMeta chartMeta, Boolean status, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.getGridLines().setMinorGLEnabled(status);
    }

    public static void updateYAxisMinorGLType(ChartMeta chartMeta, LineStyleType type, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.getGridLines().setMinorGLType(type);
    }

    public static void updateYAxisMinorGLColor(ChartMeta chartMeta, String color, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.getGridLines().setMinorGLColor(color);
    }

    public static void updateYAxisStackLabelsStatus(ChartMeta chartMeta, Boolean status, int yAxisIndex){
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        yAxes.getStackLabels()
                .setEnabled(status);
    }

    public static void updateYAxisStackLabelsStatusWithCount(ChartMeta chartMeta, Boolean status, int count){
        FrameworkChartAPIUtils.fillUpYAxis(chartMeta, count).forEach((idx, yAxes) -> yAxes.getStackLabels()
                .setEnabled(status));
    }

    public static void updateYAxisStackLabelFontStyle(ChartMeta chartMeta, Font sourceFont, int yAxisIndex){
        YAxes yAxes = FrameworkChartAPIUtils.getYAxes(chartMeta, yAxisIndex);

        if (ChartUtils.isNull(yAxes)) {
            yAxes = new YAxes(yAxisIndex);

            chartMeta
                    .getyAxis()
                    .addYAxes(yAxes);
        }
        Font targetFont = yAxes.getStackLabels().getFont();

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
    }

    public static void updateYAxisStackLabelFontStyleWithCount(ChartMeta chartMeta, Font sourceFont, int count){
        FrameworkChartAPIUtils.fillUpYAxis(chartMeta, count).forEach((idx, yAxes) -> {
            Font targetFont = yAxes.getStackLabels().getFont();

            FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
        });
    }

    public static void updateChartFontFamily(ChartMeta chartMeta, String fontFamily){
        chartMeta
                .getChart()
                .setFontFamily(fontFamily);
    }

    public static void updateChartBGColor(ChartMeta chartMeta, String bgColor){
        chartMeta
                .getChart()
                .setBackgroundColor(bgColor);
    }

    public static void updateChartAxisInvertedStatus(ChartMeta chartMeta, Boolean status){
        chartMeta
                .getChart()
                .setAxisInverted(status);
    }

    /**
     * Updates tool tip on/off status
     * @param chartMeta chartMeta Instance to be updated
     * @param status Updated Status of the tooltip
     */
    public static void updateTooltipStatus(ChartMeta chartMeta, Boolean status){
        chartMeta
                .getTooltip()
                .setEnabled(status);
    }

    /**
     * Updates Chart gradient status
     * @param chartMeta chartMeta Instance to be updated
     * @param status Updated gradient status
     */
    public static void updateChartGradientStatus(ChartMeta chartMeta, Boolean status){
        chartMeta
                .getChart()
                .getGradient()
                .setIsEnabled(status);
    }

    /**
     * Updates Slice's start angle for given series
     * @param chartMeta chartMeta Instance to be updated
     * @param startAngle Slice's Start angle
     * @param seriesIndex Series Index
     */
    public static void updateSliceStartAngle(ChartMeta chartMeta, Integer startAngle, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setStartAngle(startAngle);
    }

    /**
     * Updates Slice's end angle for given series
     * @param chartMeta chartMeta Instance to be updated
     * @param endAngle Slice's end angle
     * @param seriesIndex Series Index
     */
    public static void updateSliceEndAngle(ChartMeta chartMeta, Integer endAngle, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setEndAngle(endAngle);
    }

    /**
     * Updates Target color. Applicable for Bullet charts
     * @param chartMeta chartMeta Instance to be updated
     * @param color updated target color
     * @param seriesIndex Series containing target
     */
    public static void updateTargetColor(ChartMeta chartMeta, String color, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getTargetOptions().setColor(color);
    }

    /**
     * Updates Up Color for the given series of a water fall chartMeta
     * @param chartMeta chartMeta Instance to be updated
     * @param upColor Updated up color
     * @param seriesIndex Series Index
     */
    public static void updateUpColor(ChartMeta chartMeta, String upColor, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions().setUpColor(upColor);
    }

    public static void updateUpOpacity(ChartMeta chartMeta, Double upOpacity, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions().setUpOpacity(upOpacity);
    }

    /**
     * Updates down color for the given series.<br>
     * Note: down color is applicable for water fall chartMeta
     * @param chartMeta chartMeta Instance to be updated
     * @param downColor Updated down color
     * @param seriesIndex Series Index
     */
    public static void updateDownColor(ChartMeta chartMeta, String downColor, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions().setDownColor(downColor);
    }

    public static void updateDownOpacity(ChartMeta chartMeta, Double downOpacity, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions().setDownOpacity(downOpacity);
    }

    /**
     * Updates sort order for the given series.<br>
     * Note: sort order is applicable for race chart
     * @param chartMeta chartMeta Instance to be updated
     * @param sortOrderType Updated sort order
     * @param seriesIndex Series index to be updated
     */
    public static void updateSortOrder(ChartMeta chartMeta, SortOrderType sortOrderType, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesSortOptions().setSortOrder(sortOrderType);
    }

    public static void updateSortKey(ChartMeta chartMeta, SortByKeyType sortByKeyType, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesSortOptions().setSortByKey(sortByKeyType);
    }

    public static void updateSortStatus(ChartMeta chartMeta, Boolean isEnabled, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesSortOptions().setSortEnabled(isEnabled);
    }

    /**
     * Method to update X-Axis Max value.<br>
     * Note: It is generally used to set bar count in race chartMeta
     * @param chartMeta chartMeta Instance to be updated
     * @param maxValue updated max value
     */
    public static void updateXAxisMaxWithIndex(ChartMeta chartMeta, Integer maxValue, int xAxisIndex){
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.setMax(maxValue);
    }

    /**
     * Updates animation duration of the chartMeta
     * @param chartMeta chartMeta Instance to be updated
     * @param duration Updated duration value
     */
    public static void updateAnimationDuration(ChartMeta chartMeta, Integer duration){
        chartMeta.getChart()
                .getAnimation()
                .setDuration(duration);
    }

    /**
     * Updates caption status of the chart.<br>
     * Note: Caption is applicable for race chartMeta as of now.
     * @param chartMeta chartMeta Instance to be updated
     * @param isEnabled Updated status of the caption
     */
    public static void updateCaptionStatus(ChartMeta chartMeta, Boolean isEnabled){
        chartMeta.getCaption()
                .setEnabled(isEnabled);
    }

    /**
     * Updates caption font styling.<br>
     * Note: Caption is applicable for race chart as of now
     * @param chartMeta chartMeta Instance to be updated
     * @param sourceFont updated styling containing source font
     */
    public static void updateCaptionFontStyle(ChartMeta chartMeta, Font sourceFont){
        Font targetFont = chartMeta
                .getCaption()
                .getFont();
        FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
    }

    /**
     * Updates decimal place for the given series.<br>
     * Note: decimal place is applicable for race chart, as of now
     * @param chartMeta chartMeta instance to be updated
     * @param decimals Updated decimal place
     * @param seriesIndex Series index to be updated
     */
    public static void updateDecimalPlace(ChartMeta chartMeta, int decimals, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setDecimals(decimals);
    }

    /**
     * Updates animation status of the Chart
     * @param chartMeta chartMeta instance to be updated
     * @param status Updated animation status
     */
    public static void updateAnimationStatus(ChartMeta chartMeta, Boolean status){
        chartMeta.getChart()
                .getAnimation()
                .setEnabled(status);
    }

    /**
     * Updates Chart Type of this chart
     * @param chartMeta chartMeta instance to be updated
     * @param chartType Updated Chart Type
     */
    public static void updateChartType(ChartMeta chartMeta, ChartType chartType){
        chartMeta.getChart().setChartType(chartType);
    }

    public static ChartType getChartType(ChartMeta chartMeta){
        return chartMeta.getChart().getChartType();
    }

    /**
     * Updates legend status
     * @param chartMeta chartMeta instance to be updated
     * @param status updated legend status
     */
    public static void updateLegendStatus(ChartMeta chartMeta, Boolean status){
        chartMeta.getLegend().setStatus(status);
    }

    /**
     * Updates legend status type
     * @param chartMeta chartMeta instance to be updated
     * @param status updated legend status, value can be "on" | "off" | "auto"
     */
    public static void updateLegendStatus(ChartMeta chartMeta, LegendStatusType status){
        chartMeta.getLegend().setStatus(status);
    }

    /**
     * Updates X-Axis Binning Interval
     * @param chartMeta chartMeta instance to be updated
     * @param binningInterval Updated binning
     */
    public static void updateXAxisBinningIntervalWithIndex(ChartMeta chartMeta, Integer binningInterval, int xAxisIndex){
        if(FrameworkChartAPIUtils.isValidBinningInterval(binningInterval)) {
            FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex).setBinningInterval(binningInterval);
        }
    }

    /**
     * Updates X-Axis line color
     * @param chartMeta chartMeta instance to be updated
     * @param lineColor Updated line color
     */
    public static void updateXAxisLineColorWithIndex(ChartMeta chartMeta, String lineColor, int xAxisIndex){
        FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex).setLineColor(lineColor);
    }

    /**
     * Method to remove a specific plot line from the given Y-Axis
     * @param chartMeta ChartMeta Instance
     * @param yAxisIndex Y-Axis index
     * @param plotLineIndex Plot line index
     */
    public static void deletePlotLine(ChartMeta chartMeta, int yAxisIndex, int plotLineIndex){
        PlotLines plotLines = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex).getPlotLines();

        plotLines.removePlotLine(plotLineIndex);
    }

    /**
     * Method to remove all the plot lines for the given Y-Axis
     * @param chartMeta ChartMeta Instance
     * @param yAxisIndex Y-Axis Index
     */
    public static void deletePlotLine(ChartMeta chartMeta, int yAxisIndex){
        PlotLines plotLines = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex).getPlotLines();

        plotLines.removeAllPlotLines();
    }

    public static void updateSeriesDataPropertyColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        DataProperties dataProperties = seriesProperty.getDataProperties();
        DataProperty dataProperty = dataProperties.getDataProperty(dataPropsIndex);

        if(dataProperty == null){
            dataProperty = new DataProperty(dataPropsIndex);
            dataProperty.setColor(color);
            dataProperties.addDataProperty(dataProperty);
        }else{
            dataProperty.setColor(color);
        }
    }

    public static void updateSeriesDataPropertyBorderColor(ChartMeta chartMeta, String borderColor, int seriesIndex, int dataPropsIndex){
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getBorder().getBorderOptions()
                .setColor(borderColor);
    }

    public static void updateSeriesDataPropertyBorderRadius(ChartMeta chartMeta, Integer borderRadius, int seriesIndex, int dataPropsIndex){
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getBorder().getBorderOptions()
                .setRadius(borderRadius);
    }

    public static void updateSeriesDataPropertyBorderWidth(ChartMeta chartMeta, Integer borderWidth, int seriesIndex, int dataPropsIndex){
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getBorder().getBorderOptions()
                .setWidth(borderWidth);
    }

    public static void updateSeriesDataPropertyMarkerShape(ChartMeta chartMeta, MarkerShapeType markerShapeType, int seriesIndex, int dataPropsIndex){
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getMarker().setShape(markerShapeType);
    }

    public static void updateSeriesDataPropertyMarkerSize(ChartMeta chartMeta, Integer markerSize, int seriesIndex, int dataPropsIndex){
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getMarker().setSize(markerSize);
    }

    public static void updateSeriesDataPropertyOpacity(ChartMeta chartMeta, Double opacity, int seriesIndex, int dataPropsIndex){
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.setOpacity(opacity);
    }

    public static void createRowHeader(DataMeta dataMeta, int headerIdx){
        dataMeta.getHeaderRows().getHeaders().addHeader(new Header(headerIdx));
    }

    public static void updateRowHeaderDataType(DataMeta dataMeta, DataType dataType, int headerIndex){
        Header header = dataMeta.getHeaderRows().getHeaders().getHeader(headerIndex);

        if (header == null){
            throw new IllegalArgumentException(ErrorMessages.INVALID_HEADER_INDEX);
        }
        header.setType(dataType);
    }

    public static void updateRowHeaderDateFormat(DataMeta dataMeta, String datePattern, int headerIndex){
        Header header = dataMeta.getHeaderRows().getHeaders().getHeader(headerIndex);

        if (header == null){
            throw new IllegalArgumentException(ErrorMessages.INVALID_HEADER_INDEX);
        }
        header.setFormat(datePattern);
    }

    public static void updateRowHeaderRequiredDataFormat(DataMeta dataMeta, String datePattern, int headerIndex){
        Header header = dataMeta.getHeaderRows().getHeaders().getHeader(headerIndex);

        if (header == null){
            throw new IllegalArgumentException(ErrorMessages.INVALID_HEADER_INDEX);
        }
        header.setRequiredFormat(datePattern);
    }

    public static void createColHeader(DataMeta dataMeta, int headerIdx){
        dataMeta.getHeaderColumns().getHeaders().addHeader(new Header(headerIdx));
    }

    public static void updateColHeaderDataType(DataMeta dataMeta, DataType dataType, int headerIndex){
        Header header = dataMeta.getHeaderColumns().getHeaders().getHeader(headerIndex);

        if (header == null){
            throw new IllegalArgumentException(ErrorMessages.INVALID_HEADER_INDEX);
        }
        header.setType(dataType);
    }

    public static void updateColHeaderDateFormat(DataMeta dataMeta, String datePattern, int headerIndex){
        Header header = dataMeta.getHeaderColumns().getHeaders().getHeader(headerIndex);

        if (header == null){
            throw new IllegalArgumentException(ErrorMessages.INVALID_HEADER_INDEX);
        }
        header.setFormat(datePattern);
    }

    public static void updateColHeaderRequiredDataFormat(DataMeta dataMeta, String datePattern, int headerIndex){
        Header header = dataMeta.getHeaderColumns().getHeaders().getHeader(headerIndex);

        if (header == null){
            throw new IllegalArgumentException(ErrorMessages.INVALID_HEADER_INDEX);
        }
        header.setRequiredFormat(datePattern);
    }

    public static void updateRowHeaderStatus(DataMeta dataMeta, Boolean status){
        dataMeta.getHeaderRows().setHasHeader(status);
    }

    public static void updateColHeaderStatus(DataMeta dataMeta, Boolean status){
        dataMeta.getHeaderColumns().setHasHeader(status);
    }

    public static void updateParseType(DataMeta dataMeta, ParseType parseType){
        dataMeta.setParseType(parseType);
    }

    public static void updateColTypesType(DataMeta dataMeta, DataType dataType, int colIndex){
        ColType colType = FrameworkChartAPIUtils.getOrCreateColType(dataMeta, colIndex);

        colType.setType(dataType);
    }

    public static void updateColTypesFormat(DataMeta dataMeta, String format, int colIndex){
        ColType colType = FrameworkChartAPIUtils.getOrCreateColType(dataMeta, colIndex);
        colType.setFormat(format);
    }

    public static void updateColTypesRequiredFormat(DataMeta dataMeta, String requiredFormat, int colIndex){
        ColType colType = FrameworkChartAPIUtils.getOrCreateColType(dataMeta, colIndex);
        colType.setRequiredFormat(requiredFormat);
    }

    public static void updateColTypesCurrency(DataMeta dataMeta, String currency, int colIndex){
        ColType colType = FrameworkChartAPIUtils.getOrCreateColType(dataMeta, colIndex);
        colType.setCurrency(currency);
    }

    public static void updateRowTypesType(DataMeta dataMeta, DataType dataType, int rowIndex){
        RowType rowType = FrameworkChartAPIUtils.getOrCreateRowType(dataMeta, rowIndex);
        rowType.setType(dataType);
    }

    public static void updateRowTypesFormat(DataMeta dataMeta, String format, int rowIndex){
        RowType rowType = FrameworkChartAPIUtils.getOrCreateRowType(dataMeta, rowIndex);
        rowType.setFormat(format);
    }

    public static void updateRowTypesRequiredFormat(DataMeta dataMeta, String requiredFormat, int rowIndex){
        RowType rowType = FrameworkChartAPIUtils.getOrCreateRowType(dataMeta, rowIndex);
        rowType.setRequiredFormat(requiredFormat);
    }

    public static void updateRowTypesCurrency(DataMeta dataMeta, String currency, int rowIndex){
        RowType rowType = FrameworkChartAPIUtils.getOrCreateRowType(dataMeta, rowIndex);
        rowType.setCurrency(currency);
    }

    public static void updateSeriesChartType(ChartMeta chartMeta, ChartType chartType, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setType(chartType);
    }

    public static void updateYAxisType(ChartMeta chartMeta, YAxisType yAxisType, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setYAxisType(yAxisType);
    }

    public static void updateDataMetaSharedPropertiesRowInfoAutoFill(DataMeta dataMeta, AutoFillType autoFillType){
        dataMeta.getSharedProperties()
                .getRowInfo()
                .setAutoFillType(autoFillType);
    }

    public static void updateDataMetaSharedPropertiesColInfoAutoFill(DataMeta dataMeta, AutoFillType autoFillType){
        dataMeta.getSharedProperties()
                .getColInfo()
                .setAutoFillType(autoFillType);
    }

    public static void updateChartBackgroundOpacity(ChartMeta chartMeta, Double opacity){
        chartMeta.getChart()
                .setBackgroundOpacity(opacity);
    }

    public static boolean getHasHeaderColumn(DataMeta dataMeta) {
        Boolean hasHeader = dataMeta.getHeaderColumns().getHasHeader();

        return ChartUtils.requireNonNullElse(hasHeader, false);
    }

    public static boolean getSortStatus(ChartMeta chartMeta) {
        SeriesProperty seriesProperty = chartMeta.getSeriesProperties().getSeriesPropertyMap()
                .get(ChartUtils.toChartsInteger(0));

        if(seriesProperty != null) {
            return ChartUtils.requireNonNullElse(seriesProperty.getSeriesSortOptions().getSortEnabled(), false);
        }
        return false;
    }


    public static SortByKeyType getSortByKeyType(ChartMeta chartMeta) {
        SeriesProperty seriesProperty = chartMeta.getSeriesProperties().getSeriesPropertyMap()
                .get(ChartUtils.toChartsInteger(0));

        if(seriesProperty != null) {
            return seriesProperty.getSeriesSortOptions().getSortByKey();
        }
        return null;
    }

    public static SortOrderType getSortOrderType(ChartMeta chartMeta) {
        SeriesProperty seriesProperty = chartMeta.getSeriesProperties().getSeriesPropertyMap()
                .get(ChartUtils.toChartsInteger(0));

        if(seriesProperty != null) {
            return seriesProperty.getSeriesSortOptions().getSortOrder();
        }
        return null;
    }

    public static void updateGroupedColumnHeadersIndices(DataMeta dataMeta, List<Integer> indices) {
        GroupedHeader groupedHeader = FrameworkChartAPIUtils.computeGroupedHeaderInAbsent(dataMeta.getHeaderColumns(), 0);

        groupedHeader.setIndices(indices);
    }

    public static void updateGroupedColumnHeadersEnabled(DataMeta dataMeta, Boolean enabled) {
        dataMeta.getHeaderColumns()
                .setHasGroupedHeader(enabled);
    }

    public static void updateGroupedRowHeadersIndices(DataMeta dataMeta, List<Integer> indices) {
        GroupedHeader groupedHeader = FrameworkChartAPIUtils.computeGroupedHeaderInAbsent(dataMeta.getHeaderRows(), 0);

        groupedHeader.setIndices(indices);
    }

    public static void updateGroupedRowHeadersEnabled(DataMeta dataMeta, Boolean enabled) {
        dataMeta.getHeaderRows()
                .setHasGroupedHeader(enabled);
    }

    /**
     * Method to update group header option in plot options
     * @param chartMeta Chart Meta instance
     * @param status Updated status
     */
    public static void updateGroupHeaders(ChartMeta chartMeta, Boolean status) {
        chartMeta.getPlotOptions()
                .getSeries()
                .setGroupHeaders(status);
    }

    public static void updateOutlierStatus(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBoxPlotSeriesProperties()
                .setShowOutliers(status);
    }

    public static void updateMeanMarkerStatus(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBoxPlotSeriesProperties()
                .setShowMeanMarkers(status);
    }

    public static void updateMeanColor(ChartMeta chartMeta, String color, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBoxPlotSeriesProperties()
                .setMeanColor(color);
    }

    public static void updateMedianColor(ChartMeta chartMeta, String color, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBoxPlotSeriesProperties()
                .setMedianColor(color);
    }

    public static void updateOutliersColor(ChartMeta chartMeta, String color, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBoxPlotSeriesProperties()
                .setOutliersColor(color);
    }

    public static void updateWhiskersColor(ChartMeta chartMeta, String color, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBoxPlotSeriesProperties()
                .setWhiskersColors(color);
    }

    public static void updateSeriesPointPadding(ChartMeta chartMeta, Double pointPadding, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getPaddingSeriesProperties()
                .setPointPadding(pointPadding);
    }

    public static void updateSeriesGroupPadding(ChartMeta chartMeta, Double groupPadding, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getPaddingSeriesProperties()
                .setGroupPadding(groupPadding);
    }

    public static void updateSharedSeriesPointPadding(ChartMeta chartMeta, Double pointPadding) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getPaddingSeriesProperties()
                .setPointPadding(pointPadding);
    }

    public static void updateSharedSeriesGroupPadding(ChartMeta chartMeta, Double groupPadding) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getPaddingSeriesProperties()
                .setGroupPadding(groupPadding);
    }

    public static void updateXAxisType(ChartMeta chartMeta, XAxisType xAxisType, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.setType(xAxisType);
    }

    public static void updateSeriesConnectNulls(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setConnectNulls(status);
    }

    public static void updateSharedSeriesConnectNulls(ChartMeta chartMeta, Boolean status) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .setConnectNulls(status);
    }

    public static void updateXAxisBaselineWidth(ChartMeta chartMeta, Integer lineWidth, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.setLineWidth(lineWidth);
    }

    public static void updateDataPropertyDataLabelsStatus(ChartMeta chartMeta, Boolean status, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .setStatus(status);
    }

    public static void updateDataPropertyDataLabelsFormat(ChartMeta chartMeta, Collection<DataLabelFormatType> type, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .getFormat()
                .setFormats(type);
    }

    public static void updateDataPropertyDataLabelsPosition(ChartMeta chartMeta, DataLabelPositionType type, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .setPosition(type);
    }

    public static void updateDataPropertyDataLabelsFont(ChartMeta chartMeta, Font sourceFont, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        Font targetFont = dataProperty.getDataLabels().getFont();
        FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
    }

    public static void updateDataPropertyDataLabelsFontColor(ChartMeta chartMeta, String fontColor, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .getFont()
                .setFontColor(fontColor);
    }

    public static void updateDataPropertyDataLabelsFontSize(ChartMeta chartMeta, String fontSize, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .getFont()
                .setFontSize(fontSize);
    }

    public static void updateDataPropertyDatalabelsFontStyle(ChartMeta chartMeta, String fontStyle, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .getFont()
                .setFontStyle(fontStyle);
    }

    public static void updateDataPropertyDatalabelsFontWeight(ChartMeta chartMeta, String fontWeight, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .getFont()
                .setFontWeight(fontWeight);
    }

    public static void updateXAxisNumberFormatPrefix(ChartMeta chartMeta, String prefix, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.setPrefix(prefix);
    }

    public static void updateXAxisNumberFormatSuffix(ChartMeta chartMeta, String suffix, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.setSuffix(suffix);
    }

    public static void updateXAxisMajorTickStatus(ChartMeta chartMeta, Boolean status, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTickLines()
                .setMajorTickEnabled(status);
    }

    public static void updateXAxisMajorTickLength(ChartMeta chartMeta, Integer length, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTickLines()
                .setMajorTickLength(length);
    }

    public static void updateXAxisMajorTickWidth(ChartMeta chartMeta, Integer width, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTickLines()
                .setMajorTickWidth(width);
    }

    public static void updateXAxisMajorTickColor(ChartMeta chartMeta, String color, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTickLines()
                .setMajorTickColor(color);
    }

    public static void updateXAxisMinorTickStatus(ChartMeta chartMeta, Boolean status, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTickLines()
                .setMinorTickEnabled(status);
    }

    public static void updateXAxisMinorTickLength(ChartMeta chartMeta, Integer length, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTickLines()
                .setMinorTickLength(length);
    }

    public static void updateXAxisMinorTickWidth(ChartMeta chartMeta, Integer width, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTickLines()
                .setMinorTickWidth(width);
    }

    public static void updateXAxisMinorTickColor(ChartMeta chartMeta, String color, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getTickLines()
                .setMinorTickColor(color);
    }

    public static void updateYAxisMajorTickStatus(ChartMeta chartMeta, Boolean status, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getTickLines()
                .setMajorTickEnabled(status);
    }

    public static void updateYAxisMajorTickLength(ChartMeta chartMeta, Integer length, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getTickLines()
                .setMajorTickLength(length);
    }

    public static void updateYAxisMajorTickWidth(ChartMeta chartMeta, Integer width, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getTickLines()
                .setMajorTickWidth(width);
    }

    public static void updateYAxisMajorTickColor(ChartMeta chartMeta, String color, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getTickLines()
                .setMajorTickColor(color);
    }

    public static void updateYAxisMinorTickStatus(ChartMeta chartMeta, Boolean status, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getTickLines()
                .setMinorTickEnabled(status);
    }

    public static void updateYAxisMinorTickLength(ChartMeta chartMeta, Integer length, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getTickLines()
                .setMinorTickLength(length);
    }

    public static void updateYAxisMinorTickWidth(ChartMeta chartMeta, Integer width, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getTickLines()
                .setMinorTickWidth(width);
    }

    public static void updateYAxisMinorTickColor(ChartMeta chartMeta, String color, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getTickLines()
                .setMinorTickColor(color);
    }

    public static void updateXAxisMajorGLWidth(ChartMeta chartMeta, Integer width, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getGridLines()
                .setMajorGLWidth(width);
    }

    public static void updateXAxisMajorGLOpacity(ChartMeta chartMeta, Double opacity, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getGridLines()
                .setMajorGLOpacity(opacity);
    }

    public static void updateXAxisMinorGLWidth(ChartMeta chartMeta, Integer width, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getGridLines()
                .setMinorGLWidth(width);
    }

    public static void updateXAxisMinorGLOpacity(ChartMeta chartMeta, Double opacity, int xAxisIndex) {
        XAxes xAxes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        xAxes.getGridLines()
                .setMinorGLOpacity(opacity);
    }

    public static void updateYAxisMajorGLWidth(ChartMeta chartMeta, Integer width, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getGridLines()
                .setMajorGLWidth(width);
    }

    public static void updateYAxisMajorGLOpacity(ChartMeta chartMeta, Double opacity, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getGridLines()
                .setMajorGLOpacity(opacity);
    }

    public static void updateYAxisMinorGLWidth(ChartMeta chartMeta, Integer width, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getGridLines()
                .setMinorGLWidth(width);
    }

    public static void updateYAxisMinorGLOpacity(ChartMeta chartMeta, Double opacity, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.getGridLines()
                .setMinorGLOpacity(opacity);
    }

    public static void updateSeriesAreaLineType(ChartMeta chartMeta, AreaLineType areaLineType, int seriesIndex){
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setAreaLineType(areaLineType);
    }

    public static void updateSharedSeriesAreaLineType(ChartMeta chartMeta, AreaLineType areaLineType) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .setAreaLineType(areaLineType);
    }

    public static void updateSeriesInnerRadius(ChartMeta chartMeta, String innerRadius, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setInnerSize(innerRadius);
    }

    public static void updateSeriesVisibilityInLegend(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions().setShowInLegend(status);
    }

    public static void updateYAxisAffixesType(ChartMeta chartMeta, YAxisAffixesType yAxisAffixesType, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.setAffixesType(yAxisAffixesType);
    }

    public static void updateSeriesImageSeriesStatus(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getImages()
                .getSeries()
                .setEnabled(status);
    }

    public static void updateSharedSeriesImageSeriesStatus(ChartMeta chartMeta, Boolean status) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getImages()
                .getSeries()
                .setEnabled(status);
    }

    public static void updateSeriesImageCaptionStatus(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getImages()
                .getCaption()
                .setEnabled(status);
    }

    public static void updateSeriesImageSeriesPosition(ChartMeta chartMeta, ImagePositionType positionType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getImages()
                .getSeries()
                .setPositionType(positionType);
    }

    public static void updateSharedSeriesImageSeriesPosition(ChartMeta chartMeta, ImagePositionType positionType) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getImages()
                .getSeries()
                .setPositionType(positionType);
    }

    public static void updateSeriesImageSeriesClipShape(ChartMeta chartMeta, ClipShapeType shapeType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getImages()
                .getSeries()
                .getClip()
                .setShapeType(shapeType);
    }

    public static void updateSharedSeriesImageSeriesClipShape(ChartMeta chartMeta, ClipShapeType shapeType) {

        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getImages()
                .getSeries()
                .getClip()
                .setShapeType(shapeType);
    }

    public static void updateSeriesImageCaptionClipShape(ChartMeta chartMeta, ClipShapeType shapeType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getImages()
                .getCaption()
                .getClip()
                .setShapeType(shapeType);
    }

    public static void updateSeriesBorderWidth(ChartMeta chartMeta, Integer borderWidth, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBorder().getBorderOptions()
                .setWidth(borderWidth);
    }

    public static void updateSharedSeriesBorderWidth(ChartMeta chartMeta, Integer borderWidth) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getBorder()
                .getBorderOptions()
                .setWidth(borderWidth);
    }

    public static void updateSeriesBorderRadius(ChartMeta chartMeta, Integer borderRadius, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBorder()
                .getBorderOptions()
                .setRadius(borderRadius);
    }

    public static void updateSharedSeriesBorderRadius(ChartMeta chartMeta, Integer borderRadius) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getBorder()
                .getBorderOptions()
                .setRadius(borderRadius);
    }

    public static void updateSeriesShadowStatus(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getShadow()
                .setEnabled(status);
    }

    public static void updateSharedSeriesShadowStatus(ChartMeta chartMeta, Boolean status) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .getShadow()
                .setEnabled(status);
    }

    public static void updateSeriesShadowColor(ChartMeta chartMeta, String color, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getShadow()
                .setColor(color);
    }

    public static void updateSharedSeriesShadowColor(ChartMeta chartMeta, String color) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .getShadow()
                .setColor(color);
    }

    public static void updateSeriesShadowColorOpacity(ChartMeta chartMeta, Double opacity, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getShadow()
                .setOpacity(opacity);
    }

    public static void updateSharedSeriesShadowColorOpacity(ChartMeta chartMeta, Double opacity) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .getShadow()
                .setOpacity(opacity);
    }

    public static void updateSeriesShadowType(ChartMeta chartMeta, ShadowType shadowType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getShadow()
                .setType(shadowType);
    }

    public static void updateSharedSeriesShadowType(ChartMeta chartMeta, ShadowType shadowType) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .getShadow()
                .setType(shadowType);
    }

    public static void updateSeriesShadowBlur(ChartMeta chartMeta, Integer blur, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getShadow()
                .setBlur(blur);
    }

    public static void updateSharedSeriesShadowBlur(ChartMeta chartMeta, Integer blur) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .getShadow()
                .setBlur(blur);
    }

    public static void updateSeriesShadowSpread(ChartMeta chartMeta, Integer spread, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getShadow()
                .setSpread(spread);
    }

    public static void updateSharedSeriesShadowSpread(ChartMeta chartMeta, Integer spread) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .getShadow()
                .setSpread(spread);
    }

    public static void updateSeriesShadowDistance(ChartMeta chartMeta, Integer distance, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getShadow()
                .setDistance(distance);
    }

    public static void updateSharedSeriesShadowDistance(ChartMeta chartMeta, Integer distance) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .getShadow()
                .setDistance(distance);
    }

    public static void updateSeriesShadowDegree(ChartMeta chartMeta, Integer degree, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getShadow()
                .setDegree(degree);
    }

    public static void updateSharedSeriesShadowDegree(ChartMeta chartMeta, Integer degree) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getSeriesOptions()
                .getShadow()
                .setDegree(degree);
    }

    public static void updateDataPropertyDataLabelsBgColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .setBgColor(color);
    }

    public static void updateDataPropertyDataLabelsBorderColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setColor(color);
    }

    public static void updateDataPropertyDataLabelsBgColorOpacity(ChartMeta chartMeta, Double opacity, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .setBgOpacity(opacity);
    }

    public static void updateDataPropertyDataLabelsBorderWidth(ChartMeta chartMeta, Integer width, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setWidth(width);
    }

    public static void updateDataPropertyDataLabelsBorderRadius(ChartMeta chartMeta, Integer radius, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setRadius(radius);
    }

    public static void updateSeriesDataLabelsBgColor(ChartMeta chartMeta, String color, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .setBgColor(color);
    }

    public static void updateSharedSeriesDataLabelsBgColor(ChartMeta chartMeta, String color) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getDataLabels().setBgColor(color);
    }

    public static void updateSeriesDataLabelsBgColorOpacity(ChartMeta chartMeta, Double opacity, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .setBgOpacity(opacity);
    }

    public static void updateSharedSeriesDataLabelsBgColorOpacity(ChartMeta chartMeta, Double opacity) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getDataLabels().setBgOpacity(opacity);
    }

    public static void updateSeriesDataLabelsBorderColor(ChartMeta chartMeta, String borderColor, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setColor(borderColor);
    }

    public static void updateSharedSeriesDataLabelsBorderColor(ChartMeta chartMeta, String borderColor) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setColor(borderColor);
    }

    public static void updateSeriesDataLabelsBorderWidth(ChartMeta chartMeta, Integer width, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setWidth(width);
    }

    public static void updateSharedSeriesDataLabelsBorderWidth(ChartMeta chartMeta, Integer width) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setWidth(width);
    }

    public static void updateDataPropertyMarkerStatus(ChartMeta chartMeta, Boolean status, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getMarker()
                .setStatus(status);
    }

    public static void updateDataPropertyMarkerColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getMarker()
                .setColor(color);
    }

    public static void updateDataPropertyMarkerBorderColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getMarker()
                .setBorderColor(color);
    }

    public static void updateDataPropertyShadowStatus(ChartMeta chartMeta, Boolean status, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getShadow()
                .setEnabled(status);
    }

    public static void updateDataPropertyShadowColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getShadow()
                .setColor(color);
    }

    public static void updateDataPropertyShadowOpacity(ChartMeta chartMeta, Double opacity, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getShadow()
                .setOpacity(opacity);
    }

    public static void updateDataPropertyShadowDegree(ChartMeta chartMeta, Integer degree, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getShadow()
                .setDegree(degree);
    }

    public static void updateDataPropertyShadowDistance(ChartMeta chartMeta, Integer distance, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getShadow()
                .setDistance(distance);
    }

    public static void updateDataPropertyShadowSpread(ChartMeta chartMeta, Integer spread, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getShadow()
                .setSpread(spread);
    }

    public static void updateDataPropertyOutliersColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getBoxPlotProperties()
                .setOutliersColor(color);
    }

    public static void updateDataPropertyMeanColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getBoxPlotProperties()
                .setMeanColor(color);
    }

    public static void updateDataPropertyMedianColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getBoxPlotProperties()
                .setMedianColor(color);
    }

    public static void updateDataPropertyWhiskersColor(ChartMeta chartMeta, String color, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getBoxPlotProperties()
                .setWhiskersColors(color);
    }

    public static void updateSeriesSliderLabelsStatus(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getSlider()
                .getLabels()
                .setEnabled(status);
    }

    public static void updateSeriesSliderLabelsFont(ChartMeta chartMeta, Font sourceFont, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        Font targetFont = seriesProperty.getSeriesOptions()
                .getSlider()
                .getLabels()
                .getFont();

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
    }

    public static void updateSeriesSliderTicksCount(ChartMeta chartMeta, Integer tickCount, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesOptions()
                .getSlider()
                .setTicks(tickCount);
    }

    public static void updateSeriesDataLabelsBgShape(ChartMeta chartMeta, DataLabelsBgShapeType shapeType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .setBgShapeType(shapeType);
    }

    public static void updateSeriesDataLabelsXCoordinate(ChartMeta chartMeta, Integer x, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .setXCoordinate(x);
    }

    public static void updateSeriesDataLabelsYCoordinate(ChartMeta chartMeta, Integer y, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .setYCoordinate(y);
    }

    public static void updateSharedSeriesDataLabelsBgShape(ChartMeta chartMeta, DataLabelsBgShapeType shapeType) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getDataLabels().setBgShapeType(shapeType);
    }

    public static void updateSharedSeriesDataLabelsXCoordinate(ChartMeta chartMeta, Integer x) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getDataLabels().setXCoordinate(x);
    }

    public static void updateSharedSeriesDataLabelsYCoordinate(ChartMeta chartMeta, Integer y) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getDataLabels().setYCoordinate(y);
    }

    public static void updateDataPropertyDataLabelsBgShape(ChartMeta chartMeta, DataLabelsBgShapeType shapeType,
                                                           int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .setBgShapeType(shapeType);
    }

    public static void updateDataPropertyDataLabelsXCoordinate(ChartMeta chartMeta, Integer x,
                                                               int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels().setXCoordinate(x);
    }

    public static void updateDataPropertyDataLabelsYCoordinate(ChartMeta chartMeta, Integer y,
                                                               int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .setYCoordinate(y);
    }

    public static void updateSeriesRelation(ChartMeta chartMeta, SeriesRelationType seriesRelationType) {
        chartMeta.getPlotOptions()
                .getSeries()
                .setRelationType(seriesRelationType);
    }

    public static void updateChartAreaFillType(ChartMeta chartMeta, ColorFillType colorFillType) {
        chartMeta.getChart()
                .setFillType(colorFillType);
    }

    public static void updateChartAreaGradientFunctionType(ChartMeta chartMeta, GradientFunctionType gradientFunctionType) {
        chartMeta.getChart()
                .getGradient()
                .setGradientFunctionType(gradientFunctionType);
    }

    public static void updateChartAreaGradientDegree(ChartMeta chartMeta, Integer degree) {
        chartMeta.getChart()
                .getGradient()
                .getLinearGradientOptions()
                .setDegree(degree);
    }

    public static void updateChartAreaGradientStops(ChartMeta chartMeta, String stops) {
        chartMeta.getChart()
                .getGradient()
                .setStops(stops);
    }

    public static void updateChartMarginLeft(ChartMeta chartMeta, Integer marginLeft) {
        chartMeta.getChart().getMargin()
                .setMarginLeft(marginLeft);
    }

    public static void updateTotalLabelStatus(ChartMeta chartMeta, Boolean status) {
        chartMeta.getTotalLabel()
                .setEnabled(status);
    }

    public static void updateTotalLabelFont(ChartMeta chartMeta, Font sourceFont) {
        Font targetFont = chartMeta.getTotalLabel()
                .getFont();

        FrameworkChartAPIUtils.updateFontStylesIfNotNull(targetFont, sourceFont);
    }

    public static void updateChartAreaShadowStatus(ChartMeta chartMeta, Boolean status) {
        chartMeta.getChart().getShadow()
                .setEnabled(status);
    }

    public static void updateChartAreaShadowType(ChartMeta chartMeta, ShadowType shadowType) {
        chartMeta.getChart().getShadow()
                .setType(shadowType);
    }

    public static void updateChartAreaShadowColor(ChartMeta chartMeta, String color) {
        chartMeta.getChart().getShadow()
                .setColor(color);
    }

    public static void updateChartAreaShadowDegree(ChartMeta chartMeta, Integer degree) {
        chartMeta.getChart().getShadow()
                .setDegree(degree);
    }

    public static void updateChartAreaShadowBlur(ChartMeta chartMeta, Integer blur) {
        chartMeta.getChart().getShadow()
                .setBlur(blur);
    }

    public static void updateChartAreaShadowDistance(ChartMeta chartMeta, Integer distance) {
        chartMeta.getChart().getShadow()
                .setDistance(distance);
    }

    public static void updateChartAreaShadowSpread(ChartMeta chartMeta, Integer spread) {
        chartMeta.getChart().getShadow()
                .setSpread(spread);
    }

    public static void updateChartAreaShadowOpacity(ChartMeta chartMeta, Double opacity) {
        chartMeta.getChart().getShadow()
                .setOpacity(opacity);
    }

    public static void updateSeriesGradientFunctionType(ChartMeta chartMeta, GradientFunctionType fnType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getGradient()
                .setGradientFunctionType(fnType);
    }

    public static void updateSharedSeriesGradientFunctionType(ChartMeta chartMeta, GradientFunctionType fnType) {
        chartMeta.getSharedProperties()
                .getSeriesProperty().getSeriesColorOptions()
                .getGradient().setGradientFunctionType(fnType);
    }

    public static void updateSeriesGradientDegree(ChartMeta chartMeta, Integer degree, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getGradient()
                .getLinearGradientOptions()
                .setDegree(degree);
    }

    public static void updateSeriesGradientCX(ChartMeta chartMeta, Double cx, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        seriesProperty.getSeriesColorOptions()
                .getGradient()
                .getRadialGradientOptions()
                .setCx(cx);
    }

    public static void updateSeriesGradientCY(ChartMeta chartMeta, Double cy, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        seriesProperty.getSeriesColorOptions()
                .getGradient()
                .getRadialGradientOptions()
                .setCy(cy);
    }

    public static void updateSeriesGradientRadius(ChartMeta chartMeta, Double radius, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        seriesProperty.getSeriesColorOptions()
                .getGradient()
                .getRadialGradientOptions()
                .setRadius(radius);
    }

    public static void updateSharedSeriesGradientDegree(ChartMeta chartMeta, Integer degree) {
        chartMeta.getSharedProperties()
                .getSeriesProperty().getSeriesColorOptions()
                .getGradient()
                .getLinearGradientOptions()
                .setDegree(degree);
    }

    public static void updateSeriesGradientStops(ChartMeta chartMeta, String stops, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getGradient()
                .setStops(stops);
    }

    public static void updateDataPropertyGradientFunctionType(ChartMeta chartMeta, GradientFunctionType fnType,
                                                              int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getGradient()
                .setGradientFunctionType(fnType);
    }

    public static void updateDataPropertyGradientDegree(ChartMeta chartMeta, Integer degree, int seriesIndex,
                                                                    int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getGradient()
                .getLinearGradientOptions()
                .setDegree(degree);
    }

    public static void updateDataPropertyGradientStops(ChartMeta chartMeta, String stops, int seriesIndex,
                                                       int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getGradient()
                .setStops(stops);
    }

    public static void updateSeriesColorFillType(ChartMeta chartMeta, ColorFillType colorFillType, int seriesIndex) {
        SeriesProperty seriesProperty  = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .setColorFillType(colorFillType);
    }

    public static void updateSharedSeriesColorFillType(ChartMeta chartMeta, ColorFillType colorFillType) {
        chartMeta.getSharedProperties()
                .getSeriesProperty().getSeriesColorOptions()
                .setColorFillType(colorFillType);
    }

    public static void updateDataPropertyColorFillType(ChartMeta chartMeta, ColorFillType colorFillType, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.setColorFillType(colorFillType);
    }

    public static void updateChartMarginRight(ChartMeta chartMeta, Integer marginRight) {
        chartMeta.getChart().getMargin()
                .setMarginRight(marginRight);
    }

    public static void updateChartMarginBottom(ChartMeta chartMeta, Integer marginBottom) {
        chartMeta.getChart().getMargin()
                .setMarginBottom(marginBottom);
    }

    public static void updateChartMarginTop(ChartMeta chartMeta, Integer marginTop) {
        chartMeta.getChart().getMargin()
                .setMarginTop(marginTop);
    }

    public static void updateYAxisBaseLineWidth(ChartMeta chartMeta, Integer lineWidth, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.setLineWidth(lineWidth);
    }

    public static void updateYAxisBaseLineColor(ChartMeta chartMeta, String lineColor, int yAxisIndex) {
        YAxes yAxes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        yAxes.setLineColor(lineColor);
    }

    public static void updateChartBorderColor(ChartMeta chartMeta, String color) {
        chartMeta.getChart().getBorder()
                .getBorderOptions()
                .setColor(color);
    }

    public static void updateChartBorderWidth(ChartMeta chartMeta, Integer width) {
        chartMeta.getChart().getBorder()
                .getBorderOptions()
                .setWidth(width);
    }

    public static void updateChartBorderRadius(ChartMeta chartMeta, Integer radius) {
        chartMeta.getChart().getBorder()
                .getBorderOptions()
                .setRadius(radius);
    }

    public static void updateChartBorderStyle(ChartMeta chartMeta, BorderStyleType styleType) {
        chartMeta.getChart().getBorder()
                .setBorderStyle(styleType);
    }

    public static void updateChartBorderColorOpacity(ChartMeta chartMeta, Double opacity) {
        chartMeta.getChart().getBorder()
                .setOpacity(opacity);
    }

    public static void updateNumberFormatType(ChartMeta chartMeta, NumberFormatType numberFormatType) {
        chartMeta.getChart()
                .setNumberFormat(numberFormatType);
    }

    public static void updateLegendShape(ChartMeta chartMeta, LegendShapeType shapeType) {
        chartMeta.getLegend()
                .setShape(shapeType);
    }

    public static void updateXAxisMajorTickPosition(ChartMeta chartMeta, TickPosition tickPosition, int xAxisIndex) {
        XAxes axes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        axes.getTickLines()
                .setMajorTickPosition(tickPosition);
    }

    public static void updateXAxisMinorTickPosition(ChartMeta chartMeta, TickPosition tickPosition, int xAxisIndex) {
        XAxes axes = FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xAxisIndex);

        axes.getTickLines()
                .setMinorTickPosition(tickPosition);
    }

    public static void updateYAxisMajorTickPosition(ChartMeta chartMeta, TickPosition tickPosition, int yAxisIndex) {
        YAxes axes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        axes.getTickLines()
                .setMajorTickPosition(tickPosition);
    }

    public static void updateYAxisMinorTickPosition(ChartMeta chartMeta, TickPosition tickPosition, int yAxisIndex) {
        YAxes axes = FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yAxisIndex);

        axes.getTickLines()
                .setMinorTickPosition(tickPosition);
    }

    public static void updateChartAreaNavigatorEnable(ChartMeta chartMeta,StatusType status) {
        chartMeta.getChart()
                .setNavigatorStatus(status);
    }

    public static void updateChartAreaNavigatorScrollBarEnabled(ChartMeta chartMeta, StatusType status) {
        chartMeta.getChart()
                .setNavigatorScrollBarStatus(status);
    }

    public static void updateChartAreaRangeSelectorStatus(ChartMeta chartMeta, StatusType status) {
        chartMeta.getChart()
                .setRangeSelectorStatus(status);
    }

    public static void updateDataPropertyShadowType(ChartMeta chartMeta, ShadowType shadowType, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getShadow()
                .setType(shadowType);
    }

    public static void updateDataPropertyShadowBlur(ChartMeta chartMeta, Integer blur, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getShadow()
                .setBlur(blur);
    }

    public static void updateLegendTitleStatus(ChartMeta chartMeta, Boolean status) {
        chartMeta.getLegend().getTitle()
                .setEnabled(status);
    }

    public static void updateLegendTitle(ChartMeta chartMeta, String title) {
        chartMeta.getLegend().getTitle()
                .setText(title);
    }

    public static void updateLegendTitleFontColor(ChartMeta chartMeta, String color) {
        chartMeta.getLegend().getTitle()
                .getFont().setFontColor(color);
    }

    public static void updateLegendTitleFontSize(ChartMeta chartMeta, String size) {
        chartMeta.getLegend().getTitle()
                .getFont().setFontSize(size);
    }

    public static void updateLegendTitleFontStyle(ChartMeta chartMeta, String style) {
        chartMeta.getLegend().getTitle()
                .getFont().setFontStyle(style);
    }

    public static void updateLegendTitleFontWeight(ChartMeta chartMeta, String weight) {
        chartMeta.getLegend().getTitle()
                .getFont().setFontWeight(weight);
    }

    public static void updateChartBorderFillType(ChartMeta chartMeta, FillType fillType) {
        chartMeta.getChart().getBorder()
                .getBorderOptions()
                .setFillType(fillType);
    }

    public static void updateSharedSeriesDatalabelsCustomValue(ChartMeta chartMeta, String customValue) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .setCustomLabel(customValue);
    }

    public static void updateSeriesDatalabelsCustomValue(ChartMeta chartMeta, String customValue, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .setCustomLabel(customValue);
    }

    public static void updateDataPropertyDatalabelsCustomValue(ChartMeta chartMeta, String customValue, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .setCustomLabel(customValue);
    }

    public static void updateSharedSeriesDatalabelsBgFillType(ChartMeta chartMeta, FillType fillType) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .setFillType(fillType);
    }

    public static void updateSeriesDatalabelsBgFillType(ChartMeta chartMeta, FillType fillType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .setFillType(fillType);
    }

    public static void updateDataPropertyDatalabelBgFillType(ChartMeta chartMeta, FillType fillType, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .setFillType(fillType);
    }

    public static void updateSharedSeriesDatalabelsBorderFillType(ChartMeta chartMeta, FillType fillType) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setFillType(fillType);
    }

    public static void updateSeriesDatalabelsBorderFillType(ChartMeta chartMeta, FillType fillType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setFillType(fillType);
    }

    public static void updateDataPropertyDatalabelBorderFillType(ChartMeta chartMeta, FillType fillType, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .getBorder()
                .getBorderOptions()
                .setFillType(fillType);
    }

    public static void updateSharedSeriesDatalabelsSeparator(ChartMeta chartMeta, DatalabelSeparatorType separatorType) {
        chartMeta.getSharedProperties()
                .getSeriesProperty()
                .getDataLabels()
                .setSeparator(separatorType);
    }

    public static void updateSeriesDatalabelsSeparator(ChartMeta chartMeta, DatalabelSeparatorType separatorType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getDataLabels()
                .setSeparator(separatorType);
    }

    public static void updateDataPropertyDatalabelsSeparator(ChartMeta chartMeta, DatalabelSeparatorType separatorType, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getDataLabels()
                .setSeparator(separatorType);
    }

    public static void updateDataPropertyGradientCX(ChartMeta chartMeta, Double cx, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getGradient()
                .getRadialGradientOptions()
                .setCx(cx);
    }

    public static void updateDataPropertyGradientCY(ChartMeta chartMeta, Double cy, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getGradient()
                .getRadialGradientOptions()
                .setCy(cy);
    }

    public static void updateDataPropertyGradientRadius(ChartMeta chartMeta, Double radius, int seriesIndex, int dataPropsIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropsIndex);

        dataProperty.getGradient()
                .getRadialGradientOptions()
                .setRadius(radius);
    }

    public static void updateUpColorFillType(ChartMeta chartMeta, ColorFillType fillType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .setUpFillType(fillType);
    }

    public static void updateDownColorFillType(ChartMeta chartMeta, ColorFillType fillType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .setDownFillType(fillType);
    }

    public static void updateUpColorGradientFnType(ChartMeta chartMeta, GradientFunctionType fnType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getUpGradient()
                .setGradientFunctionType(fnType);
    }

    public static void updateDownColorGradientFnType(ChartMeta chartMeta, GradientFunctionType fnType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getDownGradient()
                .setGradientFunctionType(fnType);
    }

    public static void updateUpColorGradientStops(ChartMeta chartMeta, String stops, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getUpGradient()
                .setStops(stops);
    }

    public static void updateDownColorGradientStops(ChartMeta chartMeta, String stops, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getDownGradient()
                .setStops(stops);
    }

    public static void updateUpColorGradientDegree(ChartMeta chartMeta, Integer degree, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getUpGradient()
                .getLinearGradientOptions()
                .setDegree(degree);
    }

    public static void updateDownColorGradientDegree(ChartMeta chartMeta, Integer degree, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getDownGradient()
                .getLinearGradientOptions()
                .setDegree(degree);
    }

    /**
     * Total Colour applicable to waterfall chart
     * @param chartMeta Chart Meta instance
     * @param totalColor Total colour
     * @param seriesIndex Series Index
     */
    public static void updateSeriesTotalColor(ChartMeta chartMeta, String totalColor, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .setTotalColor(totalColor);
    }

    public static void updateSeriesTotalColorFillType(ChartMeta chartMeta, ColorFillType fillType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .setTotalFillType(fillType);
    }

    public static void updateSeriesTotalColorOpacity(ChartMeta chartMeta, Double opacity, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .setTotalColorOpacity(opacity);
    }

    public static void updateSeriesTotalColorGradientFnType(ChartMeta chartMeta, GradientFunctionType fnType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getTotalGradient()
                .setGradientFunctionType(fnType);
    }

    public static void updateSeriesTotalColorGradientStops(ChartMeta chartMeta, String stops, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getTotalGradient()
                .setStops(stops);
    }

    public static void updateSeriesTotalColorGradientDegree(ChartMeta chartMeta, Integer degree, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getSeriesColorOptions()
                .getTotalGradient()
                .getLinearGradientOptions()
                .setDegree(degree);
    }

    public static void updateChartAreaGradientStyles(ChartMeta chartMeta, JSONObjectWrapper styles) {
        Gradient gradient = chartMeta.getChart().getGradient();

        gradient.resetStyles();
        gradient.applyStyles(styles);
    }

    public static void updateSeriesGradientStyles(ChartMeta chartMeta, JSONObjectWrapper styles, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        Gradient gradient = seriesProperty.getSeriesColorOptions().getGradient();

        gradient.resetStyles();
        gradient.applyStyles(styles);
    }

    public static void updateDataPropertyGradientStyles(ChartMeta chartMeta, JSONObjectWrapper styles, int seriesIndex, int dataPropertyIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropertyIndex);
        Gradient gradient = dataProperty.getGradient();

        gradient.resetStyles();
        gradient.applyStyles(styles);
    }

    public static void resetSeriesColours(ChartMeta chartMeta) {
        for(int seriesIndex: FrameworkChartAPIUtils.getSeriesIterable(chartMeta)) {
            SeriesColorOptions seriesColorOptions = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex).getSeriesColorOptions();

            seriesColorOptions.setColorFillType(null);
            seriesColorOptions.setColorOpacity(null);
            seriesColorOptions.getGradient().resetStyles();
            seriesColorOptions.setColor(null);

            seriesColorOptions.setUpColor(null);
            seriesColorOptions.setUpFillType(null);
            seriesColorOptions.setUpOpacity(null);
            seriesColorOptions.getUpGradient().resetStyles();

            seriesColorOptions.setDownColor(null);
            seriesColorOptions.setDownFillType(null);
            seriesColorOptions.setDownOpacity(null);
            seriesColorOptions.getDownGradient().resetStyles();

            for(int dataPropIndex: FrameworkChartAPIUtils.getDataPropertiesIterable(chartMeta, seriesIndex)) {
                DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropIndex);

                dataProperty.getGradient().resetStyles();
                dataProperty.setColor(null);
                dataProperty.setOpacity(null);
                dataProperty.setColorFillType(null);
            }
        }
    }

    public static void updateUpColorGradientStyles(ChartMeta chartMeta, JSONObjectWrapper styles, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        Gradient gradient = seriesProperty.getSeriesColorOptions().getUpGradient();

        gradient.resetStyles();
        gradient.applyStyles(styles);
    }

    public static void updateDownColorGradientStyles(ChartMeta chartMeta, JSONObjectWrapper styles, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        Gradient gradient = seriesProperty.getSeriesColorOptions().getDownGradient();

        gradient.resetStyles();
        gradient.applyStyles(styles);
    }

    public static void updateTotalColorGradientStyles(ChartMeta chartMeta, JSONObjectWrapper styles, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);
        Gradient gradient = seriesProperty.getSeriesColorOptions().getTotalGradient();

        gradient.resetStyles();
        gradient.applyStyles(styles);
    }

    public static void updateSharedSeriesTrendlineFillType(ChartMeta chartMeta, FillType fillType) {
        chartMeta.getSharedProperties()
                .getSeriesProperty().getTrendLine()
                .setFillType(fillType);
    }

    public static void updateSeriesTrendlineFillType(ChartMeta chartMeta, FillType fillType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getTrendLine()
                .setFillType(fillType);
    }

    public static void updateSharedSeriesBorderOpacity(ChartMeta chartMeta, Double opacity) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getBorder().setOpacity(opacity);
    }

    public static void updateSeriesBorderOpacity(ChartMeta chartMeta, Double opacity, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBorder().setOpacity(opacity);
    }

    public static void updateDataPropertyBorderOpacity(ChartMeta chartMeta, Double opacity, int seriesIndex, int dataPropertyIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropertyIndex);

        dataProperty.getBorder().setOpacity(opacity);
    }

    public static void updateSharedSeriesBorderFillType(ChartMeta chartMeta, FillType fillType) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getBorder().getBorderOptions()
                .setFillType(fillType);
    }

    public static void updateSeriesBorderFillType(ChartMeta chartMeta, FillType fillType, int seriesIndex) {
        SeriesProperty seriesProperty = FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex);

        seriesProperty.getBorder().getBorderOptions()
                .setFillType(fillType);
    }

    public static void updateDataPropertyBorderFillType(ChartMeta chartMeta, FillType fillType, int seriesIndex, int dataPropertyIndex) {
        DataProperty dataProperty = FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropertyIndex);

        dataProperty.getBorder().getBorderOptions()
                .setFillType(fillType);
    }

    public static void updateCreditsStatus(ChartMeta chartMeta, Boolean status) {
        chartMeta.getChart().getCredits().setEnabled(status);
    }

    public static void updateCreditsText(ChartMeta chartMeta, String text) {
        chartMeta.getChart().getCredits().setText(text);
    }

    public static void updateCreditsFontSize(ChartMeta chartMeta, String fontSize) {
        chartMeta.getChart().getCredits().getFont()
                .setFontSize(fontSize);
    }

    public static void updateCreditsFontColor(ChartMeta chartMeta, String fontColor) {
        chartMeta.getChart().getCredits().getFont()
                .setFontColor(fontColor);
    }

    public static void updateCreditsFontWeight(ChartMeta chartMeta, String fontWeight) {
        chartMeta.getChart().getCredits().getFont()
                .setFontWeight(fontWeight);
    }

    public static void updateCreditsFontStyle(ChartMeta chartMeta, String fontStyle) {
        chartMeta.getChart().getCredits().getFont()
                .setFontStyle(fontStyle);
    }

    public static void updateXAxisTitleRotation(ChartMeta chartMeta, String rotation, int xIndex) {
        FrameworkChartAPIUtils.computeXAxisIfAbsent(chartMeta, xIndex)
                .getTitle().setRotation(rotation);
    }

    public static void updateYAxisTitleRotation(ChartMeta chartMeta, String rotation, int yIndex) {
        FrameworkChartAPIUtils.computeYAxisIfAbsent(chartMeta, yIndex)
                .getTitle().setRotation(rotation);
    }

    public static void updateSharedSeriesDatalabelsRotation(ChartMeta chartMeta, String rotation) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getDataLabels().setRotation(rotation);
    }

    public static void updateSeriesDatalabelsRotation(ChartMeta chartMeta, String rotation, int seriesIndex) {
        FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex)
                .getDataLabels().setRotation(rotation);
    }

    public static void updateDataPropertyDatalabelsRotation(ChartMeta chartMeta, String rotation, int seriesIndex, int dataPropertyIndex) {
        FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropertyIndex)
                .getDataLabels().setRotation(rotation);
    }

    public static void updateSlicedStatus(ChartMeta chartMeta, Boolean status, int seriesIndex, int dataPropertyIndex) {
        FrameworkChartAPIUtils.computeDataPropertyIfAbsent(chartMeta, seriesIndex, dataPropertyIndex)
                .setSliced(status);
    }

    public static void updateSeriesTrendlineDatalabelsStatus(ChartMeta chartMeta, Boolean status, int seriesIndex) {
        FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex)
                .getTrendLine().getDatalabels()
                .setStatus(status);
    }

    public static void updateSharedSeriesTrendlineDatalabelsStatus(ChartMeta chartMeta, Boolean status) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getTrendLine().getDatalabels()
                .setStatus(status);
    }

    public static void updateSeriesTrendlineDatalabelsFontColour(ChartMeta chartMeta, String colour, int seriesIndex) {
        FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex)
                .getTrendLine().getDatalabels()
                .getFont().setFontColor(colour);
    }

    public static void updateSharedSeriesTrendlineDatalabelsFontColour(ChartMeta chartMeta, String colour) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getTrendLine().getDatalabels()
                .getFont().setFontColor(colour);
    }

    public static void updateSeriesTrendlineDatalabelsFontSize(ChartMeta chartMeta, String fontSize, int seriesIndex) {
        FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex)
                .getTrendLine().getDatalabels()
                .getFont().setFontSize(fontSize);
    }

    public static void updateSharedSeriesTrendlineDatalabelsFontSize(ChartMeta chartMeta, String fontSize) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getTrendLine().getDatalabels()
                .getFont().setFontSize(fontSize);
    }

    public static void updateSeriesTrendlineDatalabelsFontStyle(ChartMeta chartMeta, String fontStyle, int seriesIndex) {
        FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex)
                .getTrendLine().getDatalabels()
                .getFont().setFontStyle(fontStyle);
    }

    public static void updateSharedSeriesTrendlineDatalabelsFontStyle(ChartMeta chartMeta, String fontStyle) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getTrendLine().getDatalabels()
                .getFont().setFontStyle(fontStyle);
    }

    public static void updateSeriesTrendlineDatalabelsFontWeight(ChartMeta chartMeta, String fontWeight, int seriesIndex) {
        FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex)
                .getTrendLine().getDatalabels()
                .getFont().setFontWeight(fontWeight);
    }

    public static void updateSharedSeriesTrendlineDatalabelsFontWeight(ChartMeta chartMeta, String fontWeight) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getTrendLine().getDatalabels()
                .getFont().setFontWeight(fontWeight);
    }

    public static void updateSeriesTrendlineDatalabelsFormat(ChartMeta chartMeta, Collection<DataLabelFormatType> type, int seriesIndex) {
        FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex)
                .getTrendLine().getDatalabels()
                .getFormat().setFormats(type);
    }

    public static void updateSharedSeriesTrendlineDatalabelsFormat(ChartMeta chartMeta, Collection<DataLabelFormatType> type) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getTrendLine().getDatalabels()
                .getFormat().setFormats(type);
    }

    public static void updateSeriesTrendlineDatalabelsCustomLabel(ChartMeta chartMeta, String customLabel, int seriesIndex) {
        FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex)
                .getTrendLine().getDatalabels()
                .setCustomLabel(customLabel);
    }

    public static void updateSharedSeriesTrendlineDatalabelsCustomLabel(ChartMeta chartMeta, String customLabel) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getTrendLine().getDatalabels().setCustomLabel(customLabel);
    }

    public static void updateSharedSeriesTrendlineDatalabelsSeparator(ChartMeta chartMeta, DatalabelSeparatorType separatorType) {
        chartMeta.getSharedProperties().getSeriesProperty()
                .getTrendLine().getDatalabels().setSeparator(separatorType);
    }

    public static void updateSeriesTrendlineDatalabelsSeparator(ChartMeta chartMeta, DatalabelSeparatorType separatorType, int seriesIndex) {
        FrameworkChartAPIUtils.computeSeriesPropertyIfAbsent(chartMeta, seriesIndex)
                .getTrendLine().getDatalabels()
                .setSeparator(separatorType);
    }

}
