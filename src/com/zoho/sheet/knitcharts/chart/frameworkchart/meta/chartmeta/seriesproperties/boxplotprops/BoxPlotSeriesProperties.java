package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.boxplotprops;

import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Grouped Series Properties for Box plot
 *
 * <AUTHOR>
 */

public class BoxPlotSeriesProperties implements GroupedChartOption, GroupedChartStyleOption {

    private ChartsString outliersColor;

    private ChartsString meanColor;

    private ChartsString whiskersColors;

    private ChartsString medianColor;

    private ChartsBoolean showOutliers;

    private ChartsBoolean showMeanMarkers;

    public String getOutliersColor() {
        return ChartUtils.getStringOrNull(outliersColor);
    }

    public void setOutliersColor(String outliersColor) {
        this.outliersColor = ChartUtils.getChartsStringOrNull(outliersColor);
    }

    public String getMeanColor() {
        return ChartUtils.getStringOrNull(meanColor);
    }

    public void setMeanColor(String meanColor) {
        this.meanColor = ChartUtils.getChartsStringOrNull(meanColor);
    }

    public String getWhiskersColors() {
        return ChartUtils.getStringOrNull(whiskersColors);
    }

    public void setWhiskersColors(String whiskersColors) {
        this.whiskersColors = ChartUtils.getChartsStringOrNull(whiskersColors);
    }

    public String getMedianColor() {
        return ChartUtils.getStringOrNull(medianColor);
    }

    public void setMedianColor(String medianColor) {
        this.medianColor = ChartUtils.getChartsStringOrNull(medianColor);
    }

    public Boolean getShowOutliers() {
        return ChartUtils.getBooleanOrNull(showOutliers);
    }

    public void setShowOutliers(Boolean showOutliers) {
        this.showOutliers = ChartUtils.getChartsBooleanOrNull(showOutliers);
    }

    public Boolean getShowMeanMarkers() {
        return ChartUtils.getBooleanOrNull(showMeanMarkers);
    }

    public void setShowMeanMarkers(Boolean showMeanMarkers) {
        this.showMeanMarkers = ChartUtils.getChartsBooleanOrNull(showMeanMarkers);
    }

    @Override
    public void getFromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setShowOutliers(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.SHOW_OUTLIERS));
        setShowMeanMarkers(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.SHOW_MEAN_MARKERS));
        /* the rest of the properties were style so just calling apply styles method */
        applyStyles(json);
    }

    @Override
    public boolean putIntoJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.SHOW_OUTLIERS, showOutliers);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.SHOW_MEAN_MARKERS, showMeanMarkers);
        isAnyKeyNotNull |= extractStyles(json);
        return isAnyKeyNotNull;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setMeanColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.MEAN_COLOR));
        setMedianColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.MEDIAN_COLOR));
        setOutliersColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.OUTLIERS_COLOR));
        setWhiskersColors(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.WHISKERS_COLOR));
    }

    @Override
    public boolean extractStyles(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.MEAN_COLOR, meanColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.MEDIAN_COLOR, medianColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.OUTLIERS_COLOR, outliersColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.BoxPlotSeriesProperties.WHISKERS_COLOR, whiskersColors);
        return isAnyKeyNotNull;
    }

    @Override
    public void resetStyles() {
        meanColor = null;
        medianColor = null;
        outliersColor = null;
        whiskersColors = null;
    }
}
