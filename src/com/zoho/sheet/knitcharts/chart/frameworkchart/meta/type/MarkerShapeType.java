package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.HashMap;

/**
 * Type definition of MarkerShapeType
 * <AUTHOR>
 */
public enum MarkerShapeType implements Type {
    CIRCLE("circle"),                                       // NO I18N
    SQUARE("square"),                                       // NO I18N
    DIAMOND("diamond"),                                     // NO I18N
    TRIANGLE("triangle"),                                   // NO I18N
    TRIANGLE_DOWN("triangle-down");                         // NO I18N

    /* HashMap to store lookup value */
    private static HashMap<String, MarkerShapeType> lookupMap;

    /**
     * Initializes lookup map with values and type
     */
    private static void initLookupMap(){
        lookupMap = new HashMap<>();

        for(MarkerShapeType type: values()){
            lookupMap.put(type.value, type);
        }
    }

    /**
     * Retrieves MarkerShapeType with its value
     * @param value Value of MarkerShape to be fetched
     * @return MarkerShape
     */
    public static MarkerShapeType retrieveByValue(String value){
        if(ChartUtils.isNull(lookupMap)){
            initLookupMap();
        }
        MarkerShapeType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;

    /**
     * Constructs MarkerShapeType with given value
     * @param value Value of this type
     */
    MarkerShapeType(String value){
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
