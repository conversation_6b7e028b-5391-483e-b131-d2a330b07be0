package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.images;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class Images implements ChartOption, ChartStyleOption {

    private Caption caption;

    private Series series;

    public Caption getCaption() {
        if (caption == null) {
            caption = new Caption();
        }
        return caption;
    }

    public Series getSeries() {
        if (series == null) {
            series = new Series();
        }
        return series;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        ChartUtils.setChartOptions(this::getCaption, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Caption.KEY));
        ChartUtils.setChartOptions(this::getSeries, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Series.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Caption.KEY, caption);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Series.KEY, series);
        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Caption.KEY, caption);

        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Series.KEY, series);
        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        ChartUtils.applyChartStyles(this::getCaption, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Caption.KEY));
        ChartUtils.applyChartStyles(this::getSeries, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Series.KEY));
    }

    @Override
    public void resetStyles() {
        ChartUtils.resetStyles(caption, series);
    }
}
