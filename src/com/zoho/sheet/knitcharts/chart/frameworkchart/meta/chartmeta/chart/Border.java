package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.chart;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.borderoptions.BorderOptions;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.borderoptions.BorderOptionsKeySupplier;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsDouble;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.BorderStyleType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class Border implements ChartOption, ChartStyleOption {

    public static class ChartBorderOptionsKeySupplier implements BorderOptionsKeySupplier {

        @Override
        public String getRadiusKey() {
            return FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.RADIUS;
        }

        @Override
        public String getColorKey() {
            return FrameworkChartMetaConstants.ChartMeta.CommonKeys.COLOR;
        }

        @Override
        public String getWidthKey() {
            return FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.WIDTH;
        }

        @Override
        public String getFillTypeKey() {
            return FrameworkChartMetaConstants.ChartMeta.CommonKeys.TYPE;
        }
    }

    public static class OldBorderOptionsKeySupplier implements BorderOptionsKeySupplier {

        @Override
        public String getRadiusKey() {
            return FrameworkChartMetaConstants.ChartMeta.Chart.Border.RADIUS;
        }

        @Override
        public String getColorKey() {
            return FrameworkChartMetaConstants.ChartMeta.Chart.Border.COLOR;
        }

        @Override
        public String getWidthKey() {
            return FrameworkChartMetaConstants.ChartMeta.Chart.Border.WIDTH;
        }

        @Override
        public String getFillTypeKey() {
            return FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.BORDER_FILL_TYPE;
        }
    }

    private ChartsDouble opacity;

    private BorderStyleType borderStyle;

    private BorderOptions borderOptions;

    private void setBorderStyleString(String borderStyle) {
        if(borderStyle != null) {
            this.borderStyle = BorderStyleType.retrieveByValue(borderStyle);
        }
    }

    public BorderStyleType getBorderStyle() {
        return borderStyle;
    }

    public void setBorderStyle(BorderStyleType borderStyle) {
        this.borderStyle = borderStyle;
    }

    public Double getOpacity() {
        return ChartUtils.getDoubleOrNull(opacity);
    }

    public void setOpacity(Double opacity) {
        this.opacity = ChartUtils.getChartsDoubleOrNull(opacity);
    }

    public BorderOptions getBorderOptions() {
        if (borderOptions == null) {
            borderOptions = new BorderOptions(new ChartBorderOptionsKeySupplier());
        }
        return borderOptions;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        setBorderStyleString(ChartUtils.optStringFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.LINE_TYPE));
        setOpacity(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.OPACITY));
        ChartUtils.setGroupedChartOptions(this::getBorderOptions, jsonObject);
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, borderOptions);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.LINE_TYPE, borderStyle);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.OPACITY, opacity);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putStylesIfNotNull(jsonObject, borderOptions);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.LINE_TYPE, borderStyle);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.OPACITY, opacity);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public void resetStyles() {
        borderStyle = null;
        opacity = null;
        ChartUtils.resetStyles(borderOptions);
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        ChartUtils.applyGroupedChartStyles(this::getBorderOptions, json);
        fromJSON(json);
    }
}
