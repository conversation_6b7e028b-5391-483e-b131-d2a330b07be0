package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.HashMap;
import java.util.Map;

public enum DataLabelPositionType implements Type {
    NONE("none"),                               // NO I18N
    AUTO("auto"),                               // NO I18N
    TOP("top"),                                 // NO I18N
    BOTTOM("bottom"),                           // NO I18N
    LEFT("left"),                               // NO I18N
    RIGHT("right"),                             // NO I18N
    CENTER("center"),                           // NO I18N
    OUTSIDE_END("outsideEnd"),                  // NO I18N
    INSIDE_END("insideEnd"),                    // NO I18N
    INSIDE_MIDDLE("insideMiddle"),              // NO I18N
    INSIDE_BASE("insideBase"),                  // NO I18N
    OUTSIDE("outside"),                         // NO I18N
    INSIDE("inside");                           // NO I18N

    private static Map<String, DataLabelPositionType> lookupMap;

    private static void initLookupMap(){
        lookupMap = new HashMap<>();

        for(DataLabelPositionType type : values()){
            lookupMap.put(type.value, type);
        }
    }

    public static DataLabelPositionType retrieveByValue(String value){
        if(ChartUtils.isNull(lookupMap)){
            initLookupMap();
        }
        DataLabelPositionType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;
    DataLabelPositionType(String value){
        this.value = value;
    }
    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
