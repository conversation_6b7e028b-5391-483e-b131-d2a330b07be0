package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Data Meta Column auto fill type definition
 * <AUTHOR>
 */
public enum AutoFillType implements Type {
    ZERO("zero"),                                                   // NO I18N
    LAST_VALID("lastValid"),                                        // NO I18N
    INTERPOLATE("interpolate");                                     // NO I18N

    private static Map<String, AutoFillType> lookupMap;

    public static AutoFillType retrieveByValue(String value){
        if(lookupMap == null){
            lookupMap = Collections.unmodifiableMap(new HashMap<>() {{
                put(ZERO.value, ZERO);
                put(LAST_VALID.value, LAST_VALID);
                put(INTERPOLATE.value, INTERPOLATE);
            }});
        }
        AutoFillType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;
    AutoFillType(String value){
        this.value = value;
    }
    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
