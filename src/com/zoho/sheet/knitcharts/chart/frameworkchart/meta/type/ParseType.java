package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.Map;

public enum ParseType implements Type {
    VERTICAL("vertical"),                                       // NO I18N
    HORIZONTAL("horizontal");                                   // NO I18N

    private static Map<String, ParseType> lookupMap;

    public static ParseType retrieveByValue(String value){
        if(lookupMap == null){
            lookupMap = CollectionsUtils.mapOf(
                CollectionsUtils.mapEntry(VERTICAL.value, VERTICAL),
                CollectionsUtils.mapEntry(HORIZONTAL.value, HORIZONTAL)
            );
        }
        ParseType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;

    ParseType(String value){
        this.value = value;
    }
    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
