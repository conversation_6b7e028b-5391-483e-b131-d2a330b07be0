package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.dataproperties;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.borderoptions.BorderOptions;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.chart.Border;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.datalabels.DataPropertyDatalabels;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.datalabels.DataLabels;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.gradient.Gradient;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.Marker;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.boxplotprops.BoxPlotSeriesProperties;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.shadow.Shadow;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ColorFillType;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsDouble;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class DataProperty implements ChartOption, ChartStyleOption, ChartKeyOnTheFlyOption {

    private ChartsString color;

    private final int index;

    private ChartsDouble opacity;

    private DataPropertyDatalabels dataLabels;

    private Shadow shadow;

    private Marker marker;

    private BoxPlotSeriesProperties boxPlotSeriesProperties;

    private Border border;

    private Gradient gradient;

    private ColorFillType colorFillType;

    private ChartsBoolean sliced;

    public DataProperty(int index){
        this.index = index;
    }

    public int getIndex() {
        return index;
    }

    public String getColor() {
        return ChartUtils.getStringOrNull(color);
    }

    public void setColor(String color) {
        this.color = ChartUtils.getChartsStringOrNull(color);
    }

    public Double getOpacity(){
        return ChartUtils.getDoubleOrNull(opacity);
    }

    public void setOpacity(Double opacity){
        this.opacity = ChartUtils.getChartsDoubleOrNull(opacity);
    }

    public Boolean getSliced(){
        return ChartUtils.getBooleanOrNull(sliced);
    }

    public void setSliced(Boolean sliced){
        this.sliced = ChartUtils.getChartsBooleanOrNull(sliced);
    }

    public DataLabels getDataLabels() {
        if (dataLabels == null) {
            dataLabels = new DataPropertyDatalabels();
        }
        return dataLabels;
    }

    private void setDataLabelsFromJSON(JSONObjectWrapper object){
        if(object != null){
            getDataLabels().fromJSON(object);
        }
    }

    public Shadow getShadow() {
        if (shadow == null) {
            shadow = new Shadow();
        }
        return shadow;
    }

    public Marker getMarker() {
        if (marker == null) {
            marker = new Marker();
        }
        return marker;
    }

    public BoxPlotSeriesProperties getBoxPlotProperties() {
        if (boxPlotSeriesProperties == null) {
            boxPlotSeriesProperties = new BoxPlotSeriesProperties();
        }
        return boxPlotSeriesProperties;
    }

    public Border getBorder() {
        if(border == null){
            border = new Border();
        }
        return border;
    }

    private void migrateOldBorderOptions(JSONObjectWrapper options) {
        BorderOptions newBorder = getBorder().getBorderOptions();
        BorderOptions oldBorder = new BorderOptions(new Border.OldBorderOptionsKeySupplier());

        oldBorder.getFromJSON(options);

        if(newBorder.getColor() == null) { newBorder.setColor(oldBorder.getColor()); }
        if(newBorder.getWidth() == null) { newBorder.setWidth(oldBorder.getWidth()); }
        if(newBorder.getRadius() == null) { newBorder.setRadius(oldBorder.getRadius()); }
        if(newBorder.getFillType() == null) { newBorder.setFillType(oldBorder.getFillType()); }
    }

    public Gradient getGradient() {
        if (gradient == null) {
            gradient = new Gradient();
        }
        return gradient;
    }

    public ColorFillType getColorFillType() {
        return colorFillType;
    }

    public void setColorFillType(ColorFillType colorFillType) {
        this.colorFillType = colorFillType;
    }

    private void setColorFillTypeFromString(String colorFillType){
        if(colorFillType != null){
            this.colorFillType = ColorFillType.retrieveByValue(colorFillType);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting color from JSONObjectWrapper */
        setColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.COLOR));
        /* Getting opacity from JSONObjectWrapper */
        setOpacity(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.OPACITY));
        /* Getting data labels from JSONWrapper Object */
        setDataLabelsFromJSON(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY));
        /* getting marker data from JSONObjectWrapper */
        ChartUtils.setGroupedChartOptions(this::getMarker, jsonObject);
        /* getting box plot options */
        ChartUtils.setGroupedChartOptions(this::getBoxPlotProperties, jsonObject);
        /* getting border options */
        ChartUtils.setChartOptions(this::getBorder, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY));
        /* getting shadow options */
        ChartUtils.setChartOptions(this::getShadow, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Shadow.KEY));
        /* getting gradient options */
        ChartUtils.setChartOptions(this::getGradient, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.KEY));
        /* getting color fill type */
        setColorFillTypeFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.FILL));
        /* getting sliced option */
        setSliced(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.SLICED));
        /* migrating old border options */
        migrateOldBorderOptions(jsonObject);
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.COLOR, color);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.OPACITY, opacity);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY, dataLabels);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, marker);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, boxPlotSeriesProperties);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY, border);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Shadow.KEY, shadow);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.KEY, gradient);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.FILL, colorFillType);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.SLICED, sliced);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.COLOR, color);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.OPACITY, opacity);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY, dataLabels);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, marker);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, boxPlotSeriesProperties);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY, border);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Shadow.KEY, shadow);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.KEY, gradient);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.FILL, colorFillType);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.SLICED, sliced);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        

        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting color from JSONObjectWrapper */
        setColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.COLOR));
        /* Getting opacity from JSONObjectWrapper */
        setOpacity(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.OPACITY));
        /* Getting data labels styles from JSONObjectWrapper */
        ChartUtils.applyChartStyles(this::getDataLabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY));
        /* Getting marker styles from JSONObjectWrapper */
        ChartUtils.applyGroupedChartStyles(this::getMarker, jsonObject);
        /* applying style to box plot styles */
        ChartUtils.applyGroupedChartStyles(this::getBoxPlotProperties, jsonObject);
        /* applying style to border options */
        ChartUtils.applyChartStyles(this::getBorder, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY));
        /* applying style to shadow options */
        ChartUtils.applyChartStyles(this::getShadow, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Shadow.KEY));
        /* applying style to gradient options */
        ChartUtils.applyChartStyles(this::getGradient, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.KEY));
        /* getting color fill type */
        setColorFillTypeFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.FILL));
        /* getting sliced option */
        setSliced(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.SLICED));
    }

    @Override
    public void resetStyles() {
        color = null;
        opacity = null;
        colorFillType = null;
        sliced = null;
        ChartUtils.resetStyles(dataLabels, shadow, gradient, border);
        ChartUtils.resetStyles(marker, boxPlotSeriesProperties);
    }

    @Override
    public JSONWrapper toJSON(Workbook workbook, Chart chart, Key key) {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(toJSON()), StatelessLambda::newJSONObject);

        ChartUtils.putKeyOnTheFlyOptionIfNotNull(jsonObject,
                FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY, dataLabels, workbook, chart, key);
        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
