package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.plotoptions;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Plot options
 *
 * <AUTHOR>
 */

public class PlotOptions implements ChartOption {

    private Series series;

    public Series getSeries() {
        if (series == null) {
            series = new Series();
        }
        return series;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        ChartUtils.setChartOptions(this::getSeries, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.PlotOptions.Series.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        return ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.PlotOptions.Series.KEY, series) ?
                jsonObject : null;
    }
}
