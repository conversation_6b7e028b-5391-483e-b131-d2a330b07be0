package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.rowtypes;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.jsonstructure.DataMetaJSONValueStructure;

/**
 * DataMeta's RowType
 *
 * <AUTHOR>
 */
public class RowType extends DataMetaJSONValueStructure {

    /* Row index */
    private final int index;

    public RowType(int index){
        this.index = index;
    }

    public int getIndex() {
        return index;
    }
}
