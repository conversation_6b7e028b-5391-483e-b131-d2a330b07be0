package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

public enum DataLabelsBgShapeType implements Type {
    ROUNDED_RECT("roundedRect"),                    // NO I18N
    RECT("rect"),                                   // NO I18N
    CIRCLE("circle"),                               // NO I18N
    ELLIPSE("ellipse");                             // NO I18N


    public static DataLabelsBgShapeType retrieveByValue(String value) {
        for(DataLabelsBgShapeType type : DataLabelsBgShapeType.values()) {
            if(type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
    }

    private final String value;


    DataLabelsBgShapeType(String value) {
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
