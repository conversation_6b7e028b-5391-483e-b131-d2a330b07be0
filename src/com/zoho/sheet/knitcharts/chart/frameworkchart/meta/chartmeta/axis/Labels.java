package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartFontOption;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class Labels extends ChartFontOption {

    private Boolean isEnabled;

    public Boolean getEnabled() {
        return isEnabled;
    }

    public void setEnabled(Boolean enabled) {
        isEnabled = enabled;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        super.fromJSON(json);

        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting isEnabled from JSONObjectWrapper */
        setEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.toJSON()), StatelessLambda::newJSONObject);

        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED, isEnabled);

        return jsonObject.isEmpty() ? null : jsonObject;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.extractStyles()), StatelessLambda::newJSONObject);

        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED, isEnabled);

        return jsonObject.isEmpty() ? null : jsonObject;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        super.applyStyles(json);

        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting isEnabled from JSONObjectWrapper */
        setEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED));
    }

    @Override
    public void resetStyles() {
        super.resetStyles();
        isEnabled = null;
    }
}
