package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.Map;

public enum DataType implements Type {
    NUMBER("number"),                                               // NO I18N
    YEAR("number"),                                                 // NO I18N
    STRING("string"),                                               // NO I18N
    DATE("date"),                                                   // NO I18N
    URL("url"),                                                     // NO I18N
    IMAGE_URL("imageUrl"),                                          // NO I18N
    INFO_TEXT("infoText"),                                          // NO I18N
    EMPTY("string");                                                // NO I18N

    private static Map<String, DataType> lookupMap;

    public static DataType retrieveByValue(String value){
        if(lookupMap == null){
            lookupMap = CollectionsUtils.mapOf(
                    CollectionsUtils.mapEntry(NUMBER.value, NUMBER),
                    CollectionsUtils.mapEntry(STRING.value, STRING),
                    CollectionsUtils.mapEntry(DATE.value, DATE),
                    CollectionsUtils.mapEntry(EMPTY.value, EMPTY),
                    CollectionsUtils.mapEntry(URL.value, URL),
                    CollectionsUtils.mapEntry(IMAGE_URL.value, IMAGE_URL)
            );
        }

        DataType type = lookupMap.get(value);
        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;

    DataType(String value){
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
