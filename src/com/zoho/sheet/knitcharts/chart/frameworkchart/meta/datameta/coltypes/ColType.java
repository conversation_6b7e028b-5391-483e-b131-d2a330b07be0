package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.coltypes;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.jsonstructure.DataMetaJSONValueStructure;

/**
 * ColType definition object for datameta
 * <AUTHOR>
 */
public class ColType extends DataMetaJSONValueStructure {
    // column index
    private final int index;

    public ColType(int index){
        this.index = index;
    }

    public int getIndex() {
        return index;
    }
}
