package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.HashMap;
import java.util.Map;

public enum DataLabelFormatType implements Type {
    CUSTOM("C"),                                // NO I18N
    VALUE("V"),                                 // NO I18N
    LABEL("L"),                                 // NO I18N
    SERIES("S"),                                // NO I18N
    LABEL_VALUE("L_V"),                         // NO I18N
    SERIES_VALUE("S_V"),                        // NO I18N
    VALUE_PERCENTAGE("V_P"),                    // NO I18N
    LABEL_PERCENTAGE("L_P"),                    // NO I18N
    SERIES_PERCENTAGE("S_P"),                   // NO I18N
    PERCENTAGE("P");                            // NO I18N

    private static Map<String, DataLabelFormatType> lookupMap;

    private static void initLookupMap(){
        lookupMap = new HashMap<>();

        for(DataLabelFormatType type : values()){
            lookupMap.put(type.value, type);
        }
    }

    public static DataLabelFormatType retrieveByValue(String value){
        if(ChartUtils.isNull(lookupMap)){
            initLookupMap();
        }
        DataLabelFormatType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;
    DataLabelFormatType(String value){
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
