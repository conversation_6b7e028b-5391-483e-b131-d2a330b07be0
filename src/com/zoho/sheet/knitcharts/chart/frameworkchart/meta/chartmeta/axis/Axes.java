package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public abstract class Axes implements ChartOption, ChartStyleOption, ChartKeyOnTheFlyOption {

    private final int index;

    private ChartsString prefix, suffix;

    private ChartsInteger lineWidth;

    private ChartsString lineColor;

    public Axes(int index) {
        this.index = index;
    }

    public abstract GridLines getGridLines();

    public abstract Labels getLabels();

    public abstract TickLines getTickLines();

    public abstract Title getTitle();

    public String getPrefix(){
        return ChartUtils.getStringOrNull(prefix);
    }

    public void setPrefix(String prefix){
        this.prefix = ChartUtils.getChartsStringOrNull(prefix);
    }

    public String getSuffix(){
        return ChartUtils.getStringOrNull(suffix);
    }

    public void setSuffix(String suffix){
        this.suffix = ChartUtils.getChartsStringOrNull(suffix);
    }

    public int getIndex() {
        return index;
    }

    public Integer getLineWidth() {
        return ChartUtils.getIntegerOrNull(lineWidth);
    }

    public void setLineWidth(Integer lineWidth) {
        this.lineWidth = ChartUtils.getChartsIntegerOrNull(lineWidth);
    }

    public String getLineColor() {
        return ChartUtils.getStringOrNull(lineColor);
    }

    public void setLineColor(String lineColor) {
        this.lineColor = ChartUtils.getChartsStringOrNull(lineColor);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        ChartUtils.setChartOptions(this::getGridLines, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.KEY));
        ChartUtils.setChartOptions(this::getLabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Labels.KEY));
        ChartUtils.setChartOptions(this::getTitle, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Title.KEY));
        ChartUtils.setGroupedChartOptions(this::getTickLines, jsonObject);
        setPrefix(ChartUtils.optDecodedStringFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.PREFIX));
        setSuffix(ChartUtils.optDecodedStringFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.SUFFIX));
        setLineColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_COLOR));
        setLineWidth(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_WIDTH));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.KEY, getGridLines());

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Labels.KEY, getLabels());
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Title.KEY, getTitle());
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, getTickLines());
        isAnyKeyNotNull |= ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.PREFIX, prefix);
        isAnyKeyNotNull |= ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.SUFFIX, suffix);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_COLOR, lineColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_WIDTH, lineWidth);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.KEY, getGridLines());

        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Labels.KEY, getLabels());
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Title.KEY, getTitle());
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, getTickLines());
        isAnyKeyNotNull |= ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.PREFIX, prefix);
        isAnyKeyNotNull |= ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.SUFFIX, suffix);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_COLOR, lineColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_WIDTH, lineWidth);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        ChartUtils.applyChartStyles(this::getGridLines, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.KEY));
        ChartUtils.applyChartStyles(this::getLabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Labels.KEY));
        ChartUtils.applyChartStyles(this::getTitle, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Title.KEY));
        ChartUtils.applyGroupedChartStyles(this::getTickLines, jsonObject);
        setPrefix(ChartUtils.optDecodedStringFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.PREFIX));
        setSuffix(ChartUtils.optDecodedStringFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.SUFFIX));
        setLineColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_COLOR));
        setLineWidth(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.LINE_WIDTH));
    }

    @Override
    public void resetStyles() {
        ChartUtils.resetStyles(getGridLines(), getLabels(), getTitle());
        ChartUtils.resetStyles(getTickLines());
        lineColor = null;
        lineWidth = null;
        prefix = null;
        suffix = null;
    }

    @Override
    public JSONWrapper toJSON(Workbook workbook, Chart chart, Key key) {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(toJSON()), StatelessLambda::newJSONObject);

        ChartUtils.putKeyOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.Title.KEY, getTitle(), workbook, chart, key);
        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
