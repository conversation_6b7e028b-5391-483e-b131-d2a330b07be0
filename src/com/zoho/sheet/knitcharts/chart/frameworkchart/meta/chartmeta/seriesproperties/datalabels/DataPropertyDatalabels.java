package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.datalabels;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.utils.ChartUtils;

public class DataPropertyDatalabels extends DataLabels implements ChartKeyOnTheFlyOption {

    @Override
    public JSONWrapper toJSON(Workbook workbook, Chart chart, Key key) {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(toJSON()), StatelessLambda::newJSONObject);
        int dataPropsIndex = key.getValue();
        int seriesIndex = key.getParent().getValue();

        String customValue = SheetChartGetterAPI.getDataPropertyDatalabelCustomValueFromReference(workbook,
                chart.getSheetMeta(), seriesIndex, dataPropsIndex);
        ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.CUSTOM, customValue);
        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
