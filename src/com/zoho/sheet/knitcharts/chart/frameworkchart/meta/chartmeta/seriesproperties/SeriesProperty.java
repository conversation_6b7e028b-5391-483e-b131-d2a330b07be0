package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.borderoptions.BorderOptions;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.chart.Border;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.datalabels.DataLabels;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.datalabels.SeriesDatalabels;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.boxplotprops.BoxPlotSeriesProperties;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.images.Images;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.paddingprops.PaddingSeriesProperties;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.dataproperties.DataProperties;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class SeriesProperty implements ChartOption, ChartStyleOption, ChartKeyOnTheFlyOption {

    protected DataLabels dataLabels;

    protected TrendLine trendLine;

    protected SeriesColorOptions seriesColorOptions;

    protected SeriesSortOptions seriesSortOptions;

    protected SeriesOptions seriesOptions;

    protected Marker marker;

    protected Border border;

    protected DataProperties dataProperties;
    // applicable for both the bullet chart i.e. Bullet bar and Bullet col
    protected TargetOptions targetOptions;

    protected BoxPlotSeriesProperties boxPlotSeriesProperties;

    protected PaddingSeriesProperties paddingSeriesProperties;

    protected Images images;

    private final int index;

    public SeriesProperty(int index) {
        this.index = index;
    }

    public int getIndex() {
        return index;
    }

    public DataLabels getDataLabels() {
        if (ChartUtils.isNull(dataLabels)) {
            dataLabels = new SeriesDatalabels();
        }
        return dataLabels;
    }

    public TrendLine getTrendLine() {
        if (ChartUtils.isNull(trendLine)) {
            trendLine = new TrendLine();
        }
        return trendLine;
    }

    public Marker getMarker() {
        if (ChartUtils.isNull(marker)) {
            marker = new Marker();
        }
        return marker;
    }

    public DataProperties getDataProperties() {
        if (ChartUtils.isNull(dataProperties)) {
            dataProperties = new DataProperties();
        }

        return dataProperties;
    }

    public TargetOptions getTargetOptions() {
        if (ChartUtils.isNull(targetOptions)) {
            targetOptions = new TargetOptions();
        }

        return targetOptions;
    }

    public BoxPlotSeriesProperties getBoxPlotSeriesProperties() {
        if (ChartUtils.isNull(boxPlotSeriesProperties)) {
            boxPlotSeriesProperties = new BoxPlotSeriesProperties();
        }
        return boxPlotSeriesProperties;
    }

    public PaddingSeriesProperties getPaddingSeriesProperties() {
        if (ChartUtils.isNull(paddingSeriesProperties)) {
            paddingSeriesProperties = new PaddingSeriesProperties();
        }
        return paddingSeriesProperties;
    }

    public Images getImages() {
        if (images == null) {
            images = new Images();
        }
        return images;
    }

    public SeriesColorOptions getSeriesColorOptions() {
        if (seriesColorOptions == null) {
            seriesColorOptions = new SeriesColorOptions();
        }
        return seriesColorOptions;
    }

    public SeriesSortOptions getSeriesSortOptions() {
        if (seriesSortOptions == null) {
            seriesSortOptions = new SeriesSortOptions();
        }
        return seriesSortOptions;
    }

    public SeriesOptions getSeriesOptions() {
        if (seriesOptions == null) {
            seriesOptions = new SeriesOptions();
        }
        return seriesOptions;
    }

    public Border getBorder() {
        if (border == null) {
            border = new Border();
        }
        return border;
    }

    private void migrateOldBorderOptions(JSONObjectWrapper options) {
        BorderOptions newBorder = getBorder().getBorderOptions();
        BorderOptions oldBorder = new BorderOptions(new Border.OldBorderOptionsKeySupplier());

        oldBorder.getFromJSON(options);

        if(newBorder.getColor() == null) { newBorder.setColor(oldBorder.getColor()); }
        if(newBorder.getWidth() == null) { newBorder.setWidth(oldBorder.getWidth()); }
        if(newBorder.getRadius() == null) { newBorder.setRadius(oldBorder.getRadius()); }
        if(newBorder.getFillType() == null) { newBorder.setFillType(oldBorder.getFillType()); }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        ChartUtils.setChartOptions(this::getDataProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.KEY));
        /* Getting trend line data from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getTrendLine, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TrendLine.KEY));
        /* Getting Datalabels data from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getDataLabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY));
        /* Getting marker data from the JSONObjectWrapper */
        ChartUtils.setGroupedChartOptions(this::getMarker, json);
        /* Getting data property from the JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getDataProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.KEY));
        /* Getting target options from the JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getTargetOptions, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TargetOptions.KEY));
        /* Getting box plot series properties */
        ChartUtils.setGroupedChartOptions(this::getBoxPlotSeriesProperties, json);
        /* Getting padding series properties */
        ChartUtils.setGroupedChartOptions(this::getPaddingSeriesProperties, json);
        /* setting image options */
        ChartUtils.setChartOptions(this::getImages, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.KEY));
        /* setting series color options */
        ChartUtils.setGroupedChartOptions(this::getSeriesColorOptions, jsonObject);
        /* setting series sort options */
        ChartUtils.setGroupedChartOptions(this::getSeriesSortOptions, jsonObject);
        /* setting series options */
        ChartUtils.setGroupedChartOptions(this::getSeriesOptions, jsonObject);
        /* setting border options */
        ChartUtils.setChartOptions(this::getBorder, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY));
        migrateOldBorderOptions(jsonObject);
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY, dataLabels);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TrendLine.KEY, trendLine);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, marker);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.KEY, dataProperties);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TargetOptions.KEY, targetOptions);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, boxPlotSeriesProperties);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, paddingSeriesProperties);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.KEY, images);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, seriesColorOptions);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, seriesSortOptions);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, seriesOptions);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY, border);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY, dataLabels);

        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TrendLine.KEY, trendLine);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, marker);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TargetOptions.KEY, targetOptions);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.KEY, dataProperties);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, boxPlotSeriesProperties);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, paddingSeriesProperties);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.KEY, images);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, seriesColorOptions);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, seriesOptions);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY, border);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        

        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        /* applying styles to data labels */
        ChartUtils.applyChartStyles(this::getDataLabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY));
        /* applying styles to trendlines */
        ChartUtils.applyChartStyles(this::getTrendLine, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TrendLine.KEY));
        /* applying styles to target options */
        ChartUtils.applyChartStyles(this::getTargetOptions, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.TargetOptions.KEY));
        /* Getting data property from the JSONObjectWrapper */
        ChartUtils.applyChartStyles(this::getDataProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.KEY));
        /* Getting marker data from the JSONObjectWrapper */
        getMarker().applyStyles(jsonObject);
        /* applying box plot series styles */
        ChartUtils.applyGroupedChartStyles(this::getBoxPlotSeriesProperties, json);
        /* applying padding series properties */
        ChartUtils.applyGroupedChartStyles(this::getPaddingSeriesProperties, json);
        /* setting images styles */
        ChartUtils.applyChartStyles(this::getImages, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.KEY));
        /* setting series color styles */
        ChartUtils.applyGroupedChartStyles(this::getSeriesColorOptions, jsonObject);
        /* setting styles to series options */
        ChartUtils.applyGroupedChartStyles(this::getSeriesOptions, jsonObject);
        /* setting styles to border options */
        ChartUtils.applyChartStyles(this::getBorder, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY));
    }

    @Override
    public void resetStyles() {
        ChartUtils.resetStyles(dataLabels, trendLine, targetOptions, dataProperties, images, border);
        ChartUtils.resetStyles(boxPlotSeriesProperties, marker, paddingSeriesProperties, seriesColorOptions, seriesOptions);
    }

    @Override
    public void onChartTypeChange() {
        ChartUtils.onChartTypeChange(seriesOptions);
    }

    @Override
    public JSONWrapper toJSON(Workbook workbook, Chart chart, Key key) {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(toJSON()), StatelessLambda::newJSONObject);

        ChartUtils.putKeyOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.KEY, getDataLabels(),
                workbook, chart, key);
        ChartUtils.putKeyOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataProperties.KEY, dataProperties,
                workbook, chart, key);
        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
