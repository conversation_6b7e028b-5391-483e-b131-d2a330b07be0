package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.images;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ClipShapeType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Image clip
 * <AUTHOR>
 */
public class Clip implements ChartOption, ChartStyleOption {

    private ClipShapeType shapeType;

    private void setShapeTypeFromString(String value) {
        if(value != null && !value.isEmpty()) {
            this.shapeType = ClipShapeType.retrieveByValue(value);
        }
    }

    public ClipShapeType getShapeType() {
        return shapeType;
    }

    public void setShapeType(ClipShapeType shapeType) {
        this.shapeType = shapeType;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        setShapeTypeFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Clip.SHAPE));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Images.Clip.SHAPE, shapeType);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        return toJSON();
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        
        fromJSON(json);
    }

    @Override
    public void resetStyles() {
        shapeType = null;
    }
}
