package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.chart;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Animation property of the Chart
 * <AUTHOR>
 */
public class Animation implements ChartOption {
    private ChartsBoolean enabled;
    private ChartsInteger duration;

    public void setDuration(Integer duration) {
        this.duration = ChartUtils.getChartsIntegerOrNull(duration);
    }

    public Integer getDuration() {
        return ChartUtils.getIntegerOrNull(duration);
    }

    public Boolean getEnabled() {
        return ChartUtils.getBooleanOrNull(enabled);
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = ChartUtils.getChartsBooleanOrNull(enabled);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if (ChartUtils.isNull(jsonObject)) {
            return;
        }

        setDuration(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.Animation.DURATION));
        setEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.Animation.DURATION, duration);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED, enabled);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
