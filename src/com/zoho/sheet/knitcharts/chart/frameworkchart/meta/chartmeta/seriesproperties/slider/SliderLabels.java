package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.slider;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartFontOption;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Slider labels are only applicable to racing bar and racing line chart
 * <AUTHOR>
 */
public class SliderLabels extends ChartFontOption {

    private ChartsBoolean enabled;

    public Boolean getEnabled() {
        return ChartUtils.getBooleanOrNull(enabled);
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = ChartUtils.getChartsBooleanOrNull(enabled);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        super.fromJSON(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        setEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.toJSON()), StatelessLambda::newJSONObject);

        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED, enabled);
        return jsonObject.isEmpty() ? null : jsonObject;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.extractStyles()), StatelessLambda::newJSONObject);

        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED, enabled);
        return jsonObject.isEmpty() ? null : jsonObject;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        super.applyStyles(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        setEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ENABLED));
    }

    @Override
    public void resetStyles() {
        super.resetStyles();
        enabled = null;
    }
}
