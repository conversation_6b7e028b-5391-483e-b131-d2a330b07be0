package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis;

import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.TickPosition;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class TickLines implements GroupedChartOption, GroupedChartStyleOption {

    private ChartsBoolean majorTickEnabled, minorTickEnabled;

    private ChartsInteger majorTickLength, minorTickLength, majorTickWidth, minorTickWidth;

    private ChartsString majorTickColor, minorTickColor;

    private TickPosition majorTickPosition, minorTickPosition;

    public void setMajorTickEnabled(Boolean majorTickEnabled) {
        this.majorTickEnabled = ChartUtils.getChartsBooleanOrNull(majorTickEnabled);
    }

    public Boolean getMajorTickEnabled() {
        return ChartUtils.getBooleanOrNull(majorTickEnabled);
    }

    public void setMinorTickEnabled(Boolean minorTickEnabled) {
        this.minorTickEnabled = ChartUtils.getChartsBooleanOrNull(minorTickEnabled);
    }

    public Boolean getMinorTickEnabled() {
        return ChartUtils.getBooleanOrNull(minorTickEnabled);
    }

    public void setMajorTickLength(Integer majorTickLength) {
        this.majorTickLength = ChartUtils.getChartsIntegerOrNull(majorTickLength);
    }

    public Integer getMajorTickLength() {
        return ChartUtils.getIntegerOrNull(majorTickLength);
    }

    public void setMinorTickLength(Integer minorTickLength) {
        this.minorTickLength = ChartUtils.getChartsIntegerOrNull(minorTickLength);
    }

    public Integer getMinorTickLength() {
        return ChartUtils.getIntegerOrNull(minorTickLength);
    }

    public void setMajorTickWidth(Integer majorTickWidth) {
        this.majorTickWidth = ChartUtils.getChartsIntegerOrNull(majorTickWidth);
    }

    public Integer getMajorTickWidth() {
        return ChartUtils.getIntegerOrNull(majorTickWidth);
    }

    public void setMinorTickWidth(Integer minorTickWidth) {
        this.minorTickWidth = ChartUtils.getChartsIntegerOrNull(minorTickWidth);
    }

    public Integer getMinorTickWidth() {
        return ChartUtils.getIntegerOrNull(minorTickWidth);
    }

    public void setMajorTickColor(String majorTickColor) {
        this.majorTickColor = ChartUtils.getChartsStringOrNull(majorTickColor);
    }

    public String getMajorTickColor() {
        return ChartUtils.getStringOrNull(majorTickColor);
    }

    public void setMinorTickColor(String minorTickColor) {
        this.minorTickColor = ChartUtils.getChartsStringOrNull(minorTickColor);
    }

    public String getMinorTickColor() {
        return ChartUtils.getStringOrNull(minorTickColor);
    }

    public TickPosition getMajorTickPosition() {
        return majorTickPosition;
    }

    public void setMajorTickPosition(TickPosition majorTickPosition) {
        this.majorTickPosition = majorTickPosition;
    }

    public TickPosition getMinorTickPosition() {
        return minorTickPosition;
    }

    public void setMinorTickPosition(TickPosition minorTickPosition) {
        this.minorTickPosition = minorTickPosition;
    }

    private void setMajorTickPositionFromString(String majorTickPosition) {
        if(majorTickPosition == null) { return; }
        this.majorTickPosition = TickPosition.retrieveByValue(majorTickPosition);
    }

    private void setMinorTickPositionFromString(String minorTickPosition) {
        if(minorTickPosition == null) { return; }
        this.minorTickPosition = TickPosition.retrieveByValue(minorTickPosition);
    }

    @Override
    public void getFromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null){ return; }

        setMajorTickEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_ENABLED));
        setMinorTickEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_ENABLED));
        applyStyles(json);
    }

    @Override
    public boolean putIntoJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        boolean anyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_ENABLED, majorTickEnabled);

        anyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_ENABLED, minorTickEnabled);
        anyKeyNotNull |= extractStyles(json);

        return anyKeyNotNull;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        

        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null){ return; }

        setMajorTickLength(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_LENGTH));
        setMinorTickLength(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_LENGTH));
        setMajorTickWidth(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_WIDTH));
        setMinorTickWidth(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_WIDTH));
        setMajorTickColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_COLOR));
        setMinorTickColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_COLOR));
        setMajorTickPositionFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_POSITION));
        setMinorTickPositionFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_POSITION));
    }

    @Override
    public boolean extractStyles(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        boolean anyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_LENGTH, majorTickLength);

        anyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_LENGTH, minorTickLength);
        anyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_WIDTH, majorTickWidth);
        anyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_WIDTH, minorTickWidth);
        anyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_COLOR, majorTickColor);
        anyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_COLOR, minorTickColor);
        anyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MAJOR_TICK_POSITION, majorTickPosition);
        anyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.TickLines.MINOR_TICK_POSITION, minorTickPosition);

        return anyKeyNotNull;
    }

    @Override
    public void resetStyles() {
        majorTickLength = null;
        minorTickLength = null;
        majorTickWidth = null;
        minorTickWidth = null;
        majorTickColor = null;
        minorTickColor = null;
        majorTickPosition = null;
        minorTickPosition = null;
    }
}
