package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font.DefaultFontJSONConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font.Font;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public abstract class ChartFontOption implements ChartOption, ChartStyleOption {

    protected Font font;

    public Font getFont() {
        if (font == null) {
            font = new Font(new DefaultFontJSONConstants());
        }
        return font;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting font from JSONObjectWrapper */
        getFont().getFromJSONObject(jsonObject);
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = getFont().putIntoJSONObject(jsonObject);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = getFont().putIntoJSONObject(jsonObject);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting font from JSONObjectWrapper */
        getFont().getFromJSONObject(jsonObject);
    }

    @Override
    public void resetStyles() {
        font = null;
    }
}
