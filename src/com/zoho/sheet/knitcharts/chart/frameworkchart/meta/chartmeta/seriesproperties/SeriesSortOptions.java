package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties;

import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.SortByKeyType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.SortOrderType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class SeriesSortOptions implements GroupedChartOption {

    private SortByKeyType sortByKey;

    private ChartsBoolean sortEnabled;

    private SortOrderType sortOrder;

    public SortOrderType getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(SortOrderType sortOrder) {
        this.sortOrder = sortOrder;
    }

    public SortByKeyType getSortByKey() {
        return sortByKey;
    }

    public void setSortByKey(SortByKeyType sortByKey) {
        this.sortByKey = sortByKey;
    }

    public void setSortEnabled(Boolean isEnabled){
        sortEnabled = ChartUtils.getChartsBooleanOrNull(isEnabled);
    }

    public Boolean getSortEnabled(){
        return ChartUtils.getBooleanOrNull(sortEnabled);
    }

    @Override
    public void getFromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }
        /* Getting sort order type from the JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.SORT_ORDER),
                type -> setSortOrder(SortOrderType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting sort key data from the JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.SORT_KEY),
                type -> setSortByKey(SortByKeyType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting sort enabled from JSONObjectWrapper */
        setSortEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.SORT_ENABLED));
    }

    @Override
    public boolean putIntoJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.SORT_ORDER, sortOrder);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.SORT_KEY, sortByKey);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.SORT_ENABLED, sortEnabled);
        return isAnyKeyNotNull;
    }
}
