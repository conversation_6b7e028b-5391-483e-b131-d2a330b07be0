package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.Map;

public enum StatusType implements Type {
    ON("on"),               // NO I18N
    OFF("off"),             // NO I18N
    AUTO("auto");           // NO I18N

    private static Map<String, StatusType> lookupMap;

    private static void initLookupMap(){
        lookupMap = CollectionsUtils.mapOf(
                CollectionsUtils.mapEntry(ON.value, ON),
                CollectionsUtils.mapEntry(OFF.value, OFF),
                CollectionsUtils.mapEntry(AUTO.value, AUTO)
        );
    }

    public static StatusType retrieveByValue(String value){
        if(ChartUtils.isNull(lookupMap)){
            initLookupMap();
        }
        StatusType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;

    StatusType(String value){
        this.value = value;
    }


    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
