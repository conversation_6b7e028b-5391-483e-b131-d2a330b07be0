package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.Objects;

/**
 * Clip shape type is used to define the shape of the image to be displayed
 * <AUTHOR>
 */
public enum ClipShapeType implements Type {
    RECT("rect"),                           // NO I18N
    ROUNDED("rounded");                     // NO I18N

    private final String value;

    ClipShapeType(String value) {
        this.value = value;
    }

    public static ClipShapeType retrieveByValue(String value) {
        if(Objects.equals(value, RECT.value)) {
            return RECT;
        } else if(Objects.equals(value, ROUNDED.value)) {
            return ROUNDED;
        } else {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
