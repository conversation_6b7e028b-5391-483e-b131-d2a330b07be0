package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.xaxis;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.TextOverFlowType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.Labels;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class XAxisLabels extends Labels {
    private String staggerLines, rotation;

    private TextOverFlowType textOverFlow;

    public String getStaggerLines() {
        return staggerLines;
    }

    public void setStaggerLines(String staggerLines) {
        this.staggerLines = staggerLines;
    }

    public String getRotation() {
        return rotation;
    }

    public void setRotation(String rotation) {
        this.rotation = rotation;
    }

    private void setTextOverFlowString(String value) {
        if(value != null) {
            if(value.equals("none")) {          // NO I18N
                this.textOverFlow = TextOverFlowType.AUTO;                  // Temp code added to migrate none to auto
            } else {
                this.textOverFlow = TextOverFlowType.retrieveByValue(value);
            }
        }
    }

    public TextOverFlowType getTextOverFlow() {
        return textOverFlow;
    }

    public void setTextOverFlow(TextOverFlowType textOverFlow) {
        this.textOverFlow = textOverFlow;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        super.fromJSON(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }

        /* Getting StaggerLines from JSONObjectWrapper */
        setStaggerLines(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.Labels.STAGGER_LINES));
        /* Getting Rotation from JSONObjectWrapper */
        setRotation(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ROTATION));
        /* Getting text over flow from JSONObjectWrapper */
        setTextOverFlowString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.Labels.TEXT_OVERFLOW));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONWrapper json = super.toJSON();
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        jsonObject = ChartUtils.requireNonNullElseGet(jsonObject, JSONObjectWrapper::new);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.Labels.STAGGER_LINES, staggerLines);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ROTATION, rotation);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.Labels.TEXT_OVERFLOW, textOverFlow);

        return jsonObject;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONWrapper json = super.extractStyles();
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        jsonObject = ChartUtils.requireNonNullElseGet(jsonObject, JSONObjectWrapper::new);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.Labels.STAGGER_LINES, staggerLines);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ROTATION, rotation);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.Labels.TEXT_OVERFLOW, textOverFlow);

        return jsonObject;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        super.applyStyles(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }

        /* Getting StaggerLines from JSONObjectWrapper */
        setStaggerLines(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.Labels.STAGGER_LINES));
        /* Getting Rotation from JSONObjectWrapper */
        setRotation(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ROTATION));
        /* Getting text over flow from JSONObjectWrapper */
        setTextOverFlowString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.Labels.TEXT_OVERFLOW));
    }

    @Override
    public void resetStyles() {
        super.resetStyles();
        staggerLines = null;
        rotation = null;
        textOverFlow = null;
    }
}
