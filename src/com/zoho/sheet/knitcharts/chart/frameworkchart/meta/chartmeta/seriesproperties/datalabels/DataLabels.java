package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.datalabels;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartFontOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.borderoptions.BorderOptions;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.chart.Border;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.*;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsDouble;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public abstract class DataLabels extends ChartFontOption implements ChartKeyOnTheFlyOption {

    private StatusType status;

    private DataLabelPositionType position;

    private ChartsString bgColor, rotation;

    private DataLabelsFormat format;

    private ChartsDouble bgOpacity;

    private Border border;

    private DataLabelsBgShapeType bgShapeType;

    private ChartsInteger xCoordinate, yCoordinate;

    private ChartsString customLabel;

    private FillType fillType;

    private DatalabelSeparatorType separator;

    public StatusType getStatus() {
        return status;
    }

    public void setStatus(StatusType status) {
        this.status = status;
    }

    public void setStatus(Boolean status){
        if(status == null) {
            this.status = null;
        } else {
            this.status = status ? StatusType.ON : StatusType.OFF;
        }
    }

    public DataLabelPositionType getPosition() {
        return position;
    }

    public void setPosition(DataLabelPositionType position) {
        this.position = position;
    }

    public DataLabelsFormat getFormat() {
        if (format == null) {
            format = new DataLabelsFormat();
        }
        return format;
    }

    public String getBgColor() {
        return ChartUtils.getStringOrNull(bgColor);
    }

    public void setBgColor(String bgColor) {
        this.bgColor = ChartUtils.getChartsStringOrNull(bgColor);
    }

    public Double getBgOpacity() {
        return ChartUtils.getDoubleOrNull(bgOpacity);
    }

    public void setBgOpacity(Double bgOpacity) {
        this.bgOpacity = ChartUtils.getChartsDoubleOrNull(bgOpacity);
    }

    public Border getBorder() {
        if (border == null) {
            border = new Border();
        }
        return border;
    }

    private void migrateOldBorderOptions(JSONObjectWrapper options) {
        BorderOptions newBorder = getBorder().getBorderOptions();
        BorderOptions oldBorder = new BorderOptions(new Border.OldBorderOptionsKeySupplier());

        oldBorder.getFromJSON(options);

        if(newBorder.getColor() == null) { newBorder.setColor(oldBorder.getColor()); }
        if(newBorder.getWidth() == null) { newBorder.setWidth(oldBorder.getWidth()); }
        if(newBorder.getRadius() == null) { newBorder.setRadius(oldBorder.getRadius()); }
        if(newBorder.getFillType() == null) { newBorder.setFillType(oldBorder.getFillType()); }
    }

    public DataLabelsBgShapeType getBgShapeType() {
        return bgShapeType;
    }

    public void setBgShapeType(DataLabelsBgShapeType bgShapeType) {
        this.bgShapeType = bgShapeType;
    }

    private void setBgShapeTypeFromString(String bgShapeType){
        if(bgShapeType != null){
            this.bgShapeType = DataLabelsBgShapeType.retrieveByValue(bgShapeType);
        }
    }

    public Integer getXCoordinate() {
        return ChartUtils.getIntegerOrNull(xCoordinate);
    }

    public void setXCoordinate(Integer xCoordinate) {
        this.xCoordinate = ChartUtils.getChartsIntegerOrNull(xCoordinate);
    }

    public Integer getYCoordinate() {
        return ChartUtils.getIntegerOrNull(yCoordinate);
    }

    public void setYCoordinate(Integer yCoordinate) {
        this.yCoordinate = ChartUtils.getChartsIntegerOrNull(yCoordinate);
    }

    public String getCustomLabel() {
        return ChartUtils.getStringOrNull(customLabel);
    }

    public void setCustomLabel(String customLabel) {
        this.customLabel = ChartUtils.getChartsStringOrNull(customLabel);
    }

    public FillType getFillType() {
        return fillType;
    }

    public void setFillType(FillType fillType) {
        this.fillType = fillType;
    }

    public DatalabelSeparatorType getSeparator() {
        return separator;
    }

    public void setSeparator(DatalabelSeparatorType separator) {
        this.separator = separator;
    }

    public String getRotation() {
        return ChartUtils.getStringOrNull(rotation);
    }

    public void setRotation(String rotation) {
        this.rotation = ChartUtils.getChartsStringOrNull(rotation);
    }

    private void putProperties(JSONObjectWrapper jsonObject) {
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.STATUS, getStatus());
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.POSITION, position);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.BACKGROUND_COLOR, bgColor);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.BACKGROUND_OPACITY, bgOpacity);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.X_COORDINATE, xCoordinate);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.Y_COORDINATE, yCoordinate);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.SHAPE, bgShapeType);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.BACKGROUND_FILL_TYPE, fillType);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.SEPARATOR, separator);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ROTATION, rotation);
    }

    private void extractProperties(JSONObjectWrapper jsonObject) {
        setStatus(ChartUtils.retrieveTypeByValue(StatusType.class, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.STATUS)));
        setPosition(ChartUtils.retrieveTypeByValue(DataLabelPositionType.class, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.POSITION)));
        setBgColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.BACKGROUND_COLOR));
        setBgOpacity(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.BACKGROUND_OPACITY));
        setBgShapeTypeFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.SHAPE));
        setXCoordinate(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.X_COORDINATE));
        setYCoordinate(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.Y_COORDINATE));
        setFillType(ChartUtils.retrieveTypeByValue(FillType.class, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.BACKGROUND_FILL_TYPE)));
        setSeparator(ChartUtils.retrieveTypeByValue(DatalabelSeparatorType.class, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.SEPARATOR)));
        setRotation(ChartUtils.optStringFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.ROTATION));
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        super.fromJSON(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        ChartUtils.setGroupedChartOptions(this::getFormat, jsonObject);
        ChartUtils.setChartOptions(this::getBorder, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY));
        setCustomLabel(ChartUtils.optDecodedStringFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.CUSTOM));
        extractProperties(jsonObject);
        migrateOldBorderOptions(jsonObject);
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.toJSON()), StatelessLambda::newJSONObject);

        ChartUtils.putIfNotNull(jsonObject, format);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY, border);
        ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.DataLabels.CUSTOM, customLabel);
        putProperties(jsonObject);

        return jsonObject.isEmpty() ? null : jsonObject;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.extractStyles()),
                StatelessLambda::newJSONObject);

        ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY, border);
        ChartUtils.putStylesIfNotNull(jsonObject, format);
        putProperties(jsonObject);

        return jsonObject.isEmpty() ? null : jsonObject;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        super.applyStyles(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        extractProperties(jsonObject);
        ChartUtils.applyChartStyles(this::getBorder, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY));
        ChartUtils.applyGroupedChartStyles(this::getFormat, json);
    }

    @Override
    public void resetStyles() {
        super.resetStyles();
        bgColor = null;
        bgOpacity = null;
        bgShapeType = null;
        xCoordinate = null;
        yCoordinate = null;
        status = null;
        position = null;
        fillType = null;
        separator = null;
        rotation = null;
        ChartUtils.resetStyles(format);
        ChartUtils.resetStyles(border);
    }
}
