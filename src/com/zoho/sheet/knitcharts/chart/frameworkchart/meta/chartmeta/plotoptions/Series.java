package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.plotoptions;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.SeriesRelationType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class Series implements ChartOption {

    /* Group headers option applicable for box plot */
    private ChartsBoolean groupHeaders;

    /* series relation is applicable for scatter plot */
    private SeriesRelationType relationType;

    public Boolean getGroupHeaders() {
        return ChartUtils.getBooleanOrNull(groupHeaders);
    }

    public void setGroupHeaders(Boolean groupHeaders) {
        this.groupHeaders = ChartUtils.getChartsBooleanOrNull(groupHeaders);
    }

    public SeriesRelationType getRelationType() {
        return relationType;
    }

    public void setRelationType(SeriesRelationType relationType) {
        this.relationType = relationType;
    }

    private void setRelationTypeFromString(String type) {
        if(type != null) {
            this.relationType = SeriesRelationType.retrieveByValue(type);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setGroupHeaders(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.PlotOptions.Series.GROUP_HEADERS));
        setRelationTypeFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.PlotOptions.Series.RELATION));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.PlotOptions.Series.GROUP_HEADERS, groupHeaders);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.PlotOptions.Series.RELATION, relationType);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
