package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.Map;

public enum SortByKeyType implements Type {
    Y("y"),                     // NO I18N
    NAME("name");               // NO I18N

    private static Map<String, SortByKeyType> lookupMap;

    public static SortByKeyType retrieveByValue(String value){
        if(lookupMap == null){
            lookupMap = CollectionsUtils.mapOf(
               CollectionsUtils.mapEntry(Y.value, Y),
               CollectionsUtils.mapEntry(NAME.value, NAME)
            );
        }
        SortByKeyType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }


    private final String value;

    SortByKeyType(String value){
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
