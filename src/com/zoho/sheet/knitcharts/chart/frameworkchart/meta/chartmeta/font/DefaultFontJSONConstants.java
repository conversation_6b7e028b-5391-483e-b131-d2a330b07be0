package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.font;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;

public final class DefaultFontJSONConstants implements FontJSONConstants {

    @Override
    public String getFontColorConstant() {
        return FrameworkChartMetaConstants.ChartMeta.Font.COLOR;
    }

    @Override
    public String getFontStyleConstant() {
        return FrameworkChartMetaConstants.ChartMeta.Font.STYLE;
    }

    @Override
    public String getFontSizeConstant() {
        return FrameworkChartMetaConstants.ChartMeta.Font.SIZE;
    }

    @Override
    public String getFontWeightConstant() {
        return FrameworkChartMetaConstants.ChartMeta.Font.WEIGHT;
    }
}
