package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.coltypes;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import static com.zoho.sheet.knitcharts.utils.ChartUtils.toChartsInteger;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.HashMap;
import java.util.Map;

/**
 * ColTypes for DataMeta
 *
 * <AUTHOR>
 */
public class ColTypes implements ChartOption {

    private Map<ChartsInteger, ColType> colTypeMap;

    public Map<ChartsInteger, ColType> getColTypeMap() {
        if(colTypeMap == null){
            colTypeMap = new HashMap<>();
        }

        return colTypeMap;
    }

    public ColType getColType(int index){
        return getColTypeMap().get(toChartsInteger(index));
    }

    public void addColType(ColType colType){
        getColTypeMap().put(toChartsInteger(colType.getIndex()), colType);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null){
            return;
        }
        getColTypeMap().clear();
        ChartUtils.forEachKey(jsonObject, idx -> {
            ColType type = new ColType(Integer.parseInt(idx));
            type.fromJSON(jsonObject.getJSONObject(idx));
            addColType(type);
        });
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        getColTypeMap().forEach((idx, colType) -> ChartUtils.putIfNotNull(jsonObject, idx.toString(), colType.toJSON()));
        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
