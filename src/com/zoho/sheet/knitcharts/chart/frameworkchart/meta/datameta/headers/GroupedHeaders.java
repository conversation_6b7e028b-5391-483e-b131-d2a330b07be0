package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.headers;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.HashMap;
import java.util.Map;

public class GroupedHeaders implements ChartOption {
    private Map<ChartsInteger, GroupedHeader> groupedHeaderMap;

    public Map<ChartsInteger, GroupedHeader> getGroupedHeaderMap() {
        if (groupedHeaderMap == null) {
            groupedHeaderMap = new HashMap<>();
        }
        return groupedHeaderMap;
    }

    private void setGroupedHeaderMap(Map<ChartsInteger, GroupedHeader> map) {
        if(map == null || map.isEmpty()) { return; }

        getGroupedHeaderMap().putAll(map);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        setGroupedHeaderMap(ChartUtils.optMapFromJSONObject(jsonObject, GroupedHeader::new));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        return ChartUtils.putIfNotNull(jsonObject, groupedHeaderMap) ? jsonObject : null;
    }
}
