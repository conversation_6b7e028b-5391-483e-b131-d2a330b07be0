package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.dataproperties;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.HashMap;
import java.util.Map;

import static com.zoho.sheet.knitcharts.utils.ChartUtils.toChartsInteger;

public class DataProperties implements ChartOption, ChartStyleOption, ChartKeyOnTheFlyOption {
    private Map<ChartsInteger, DataProperty> dataProperties;

    public Map<ChartsInteger, DataProperty> getDataProperties() {
        if (ChartUtils.isNull(dataProperties)) {
            dataProperties = new HashMap<>();
        }

        return dataProperties;
    }

    public DataProperty getDataProperty(int index){
        return getDataProperties().get(toChartsInteger(index));
    }

    public void addDataProperty(DataProperty dataProperty) {
        getDataProperties().put(toChartsInteger(dataProperty.getIndex()), dataProperty);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        Map<ChartsInteger, DataProperty> dataProperties = getDataProperties();

        dataProperties.clear();
        ChartUtils.forEachKey(jsonObject, (key) -> {
            int index = Integer.parseInt(key);
            DataProperty dataProperty = new DataProperty(index);

            dataProperty.fromJSON(jsonObject.getJSONObject(key));
            dataProperties.put(toChartsInteger(index), dataProperty);
        });
    }

    @Override
    public JSONWrapper toJSON() {
        if (ChartUtils.isNull(dataProperties) || dataProperties.isEmpty()) {
            return null;
        }

        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        for(Map.Entry<ChartsInteger, DataProperty> dataPropertyEntry: dataProperties.entrySet()){
            DataProperty dataProperty = dataPropertyEntry.getValue();

            jsonObject.put(dataPropertyEntry.getKey().toString(), dataProperty.toJSON());
        }

        return jsonObject;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        return ChartUtils.putStylesIfNotNull(jsonObject, dataProperties) ? jsonObject : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        

        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }

        Map<ChartsInteger, DataProperty> dataProperties = getDataProperties();

        ChartUtils.forEachKey(jsonObject, (key) -> {
            int index = Integer.parseInt(key);
            DataProperty dataProperty = dataProperties.computeIfAbsent(toChartsInteger(index), k -> new DataProperty(index));

            dataProperty.applyStyles(jsonObject.getJSONObject(key));
        });
    }

    @Override
    public void resetStyles() {
        getDataProperties().forEach((key, dataProperty) -> dataProperty.resetStyles());
    }

    @Override
    public JSONWrapper toJSON(Workbook workbook, Chart chart, Key key) {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        return ChartUtils.putKeyOnTheFlyOptionIfNotNull(jsonObject, dataProperties, workbook, chart, key) ? jsonObject : null;
    }
}
