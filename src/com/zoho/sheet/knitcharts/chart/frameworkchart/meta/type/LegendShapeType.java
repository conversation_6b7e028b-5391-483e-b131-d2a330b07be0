package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.chart.Type;

import java.util.Objects;

public enum LegendShapeType implements Type {
    CIRCLE("circle"),                       // NO I18N
    ELLIPSE("ellipse"),                     // NO I18N
    RECTANGLE("rect"),                      // NO I18N
    ROUNDED_RECTANGLE("roundedRect"),       // NO I18N
    TRIANGLE("triangle");                   // NO I18N

    public static LegendShapeType retrieveByValue(String value) {
        for(LegendShapeType type : values()) {
            if(Objects.equals(type.value, value)) { return type; }
        }

        throw new IllegalArgumentException(String.format("The value %s is not a valid value for LegendShapeType.", value));            // NO I18N
    }

    private final String value;

    LegendShapeType(String value) {
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
