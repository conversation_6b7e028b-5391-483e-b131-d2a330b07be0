package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.Objects;

/**
 * Border Style type
 * <AUTHOR>
 */
public enum BorderStyleType implements Type {
    SOLID("solid"),                 // NO I18N
    DASHED("dashed"),               // NO I18N
    DOTTED("dotted"),               // NO I18N
    DOUBLED("double");              // NO I18N


    private final String value;

    public static BorderStyleType retrieveByValue(String value){
        for(BorderStyleType borderStyle : values()){
            if(Objects.equals(borderStyle.value, value)){
                return borderStyle;
            }
        }
        throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
    }

    BorderStyleType(String value) {
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
