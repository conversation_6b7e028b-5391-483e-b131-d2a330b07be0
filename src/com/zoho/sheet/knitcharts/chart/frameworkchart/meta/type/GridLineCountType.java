package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.HashMap;

public enum GridLineCountType implements Type {
    AUTO("auto"),                                   // NO I18N
    ONE("1"),                                       // NO I18N
    TWO("2"),                                       // NO I18N
    THREE("3"),                                       // NO I18N
    FOUR("4"),                                       // NO I18N
    FIVE("5"),                                       // NO I18N
    SIX("6"),                                       // NO I18N
    SEVEN("7"),                                       // NO I18N
    EIGHT("8"),                                       // NO I18N
    NINE("9"),                                       // NO I18N
    TEN("10");                                       // NO I18N

    private static HashMap<String, GridLineCountType> lookupMap;

    private static void initLookupMap() {
        lookupMap = new HashMap<>();

        for (GridLineCountType type : values()) {
            lookupMap.put(type.value, type);
        }
    }

    public static GridLineCountType retrieveByValue(Object valueObj) {
        String value = String.valueOf(valueObj);
        if (ChartUtils.isNull(lookupMap)) {
            initLookupMap();
        }
        GridLineCountType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;

    GridLineCountType(String value) {
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
