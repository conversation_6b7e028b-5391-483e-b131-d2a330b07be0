package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.gradient;

import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Grouped properties for linear gradient options
 * <AUTHOR>
 */
public class LinearGradientOptions implements GroupedChartOption, GroupedChartStyleOption {

    private ChartsInteger degree;

    public Integer getDegree() {
        return ChartUtils.getIntegerOrNull(degree);
    }

    public void setDegree(Integer degree) {
        this.degree = ChartUtils.getChartsIntegerOrNull(degree);
    }

    @Override
    public void getFromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setDegree(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.DEGREE));
    }

    @Override
    public boolean putIntoJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        return ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.DEGREE, degree);
    }

    @Override
    public boolean extractStyles(JSONWrapper json) {
        return putIntoJSON(json);
    }

    @Override
    public void resetStyles() {
        degree = null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        

        getFromJSON(json);
    }
}
