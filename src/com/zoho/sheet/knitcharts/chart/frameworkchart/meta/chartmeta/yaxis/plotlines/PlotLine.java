package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.yaxis.plotlines;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.LineStyleType;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class PlotLine implements ChartOption, ChartStyleOption, ChartKeyOnTheFlyOption {

    private Double value;

    private LineStyleType lineType;

    private String lineColor;

    private Label label;

    public final int index;

    public ChartsInteger lineWidth;

    public PlotLine(int index) {
        this.index = index;
    }

    public int getIndex() {
        return index;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public LineStyleType getLineType() {
        return lineType;
    }

    public void setLineType(LineStyleType lineType) {
        this.lineType = lineType;
    }

    public String getLineColor() {
        return lineColor;
    }

    public void setLineColor(String lineColor) {
        this.lineColor = lineColor;
    }

    public Integer getLineWidth() {
        return ChartUtils.getIntegerOrNull(lineWidth);
    }

    public void setLineWidth(Integer lineWidth) {
        this.lineWidth = ChartUtils.getChartsIntegerOrNull(lineWidth);
    }

    public Label getLabel() {
        if (ChartUtils.isNull(label)) {
            label = new Label();
        }
        return label;
    }

    private void applyStylesToLabel(JSONWrapper json) {
        if(json != null) {
            getLabel().applyStyles(json);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }

        /* Getting plotLineValue from JSONObjectWrapper */
        setValue(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.VALUE));
        /*Getting plotLine color from JSONObjectWrapper */
        setLineColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.COLOR));
        /* Getting plotLine type from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.TYPE),
                (type) -> setLineType(LineStyleType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting plotLine label from JSONObjectWrapper */
        getLabel().fromJSON(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.LABEL));
        /* getting line width */
        setLineWidth(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.WIDTH));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.VALUE, value);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.COLOR, lineColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.TYPE, lineType);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.LABEL, label);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.WIDTH, lineWidth);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.COLOR, lineColor);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.TYPE, lineType);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.LABEL, label);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.WIDTH, lineWidth);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        

        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }

        /*Getting plotLine color from JSONObjectWrapper */
        setLineColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.COLOR));
        /* Getting plotLine type from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.TYPE),
                (type) -> setLineType(LineStyleType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting plotLine label from JSONObjectWrapper */
        applyStylesToLabel(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.LABEL));
        /* getting line width */
        setLineWidth(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.WIDTH));
    }

    @Override
    public void resetStyles() {
        lineColor = null;
        lineType = null;
        lineWidth = null;
        ChartUtils.resetStyles(label);
    }

    @Override
    public JSONWrapper toJSON(Workbook workbook, Chart chart, Key key) {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(toJSON()), StatelessLambda::newJSONObject);
        int yAxisIndex = key.getParent().getValue();
        int plotLineIndex = key.getValue();

        Double value = SheetChartGetterAPI.getPlotlinesValueFromReference(workbook, chart.getSheetMeta(), yAxisIndex, plotLineIndex);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.VALUE, value);
        ChartUtils.putKeyOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.LABEL, label, workbook, chart, key);

        return jsonObject;
    }
}
