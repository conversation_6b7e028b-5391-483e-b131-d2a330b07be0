package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.Objects;

public enum SeriesGradientDirectionType implements Type {
    TOP_BOTTOM("topBottom"),                        // NO I18N
    BOTTOM_TOP("bottomTop");                        // NO I18N


    public static SeriesGradientDirectionType retrieveByValue(String value) {
        if(Objects.equals(TOP_BOTTOM.value, value)) {
            return TOP_BOTTOM;
        } else if(Objects.equals(BOTTOM_TOP.value, value)) {
            return BOTTOM_TOP;
        }
        throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
    }

    private final String value;

    SeriesGradientDirectionType(String value) {
        this.value = value;
    }
    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
