package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.Map;

public enum SortOrderType implements Type {
    ASCENDING("ascending"),                     // NO I18N
    DESCENDING("descending");                   // NO I18N

    /* HashMap to store lookup values */
    private static Map<String, SortOrderType> lookupMap;

    /**
     * method to initialize lookup map
     */
    private static void initLookupMap(){
        lookupMap = CollectionsUtils.mapOf(
            CollectionsUtils.mapEntry(ASCENDING.value, ASCENDING),
            CollectionsUtils.mapEntry(DESCENDING.value, DESCENDING)
        );
    }

    /**
     * Retrieves sort order type with its value
     * @param value Value of SortOrderType instance
     * @return SortOrderType instance associated with
     */
    public static SortOrderType retrieveByValue(String value){
        if(ChartUtils.isNull(lookupMap)){
            initLookupMap();
        }
        SortOrderType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;

    SortOrderType(String value){
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
