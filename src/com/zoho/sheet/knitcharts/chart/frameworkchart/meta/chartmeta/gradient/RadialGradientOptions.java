package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.gradient;

import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsDouble;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Grouped radial gradient options
 * <AUTHOR>
 */
public class RadialGradientOptions implements GroupedChartOption, GroupedChartStyleOption {

    /**
     * X-coordinate of the origin
     */
    private ChartsDouble cx;

    /**
     * Y-coordinate of the origin
     */
    private ChartsDouble cy;

    /**
     * Radius of the circle
     */
    private ChartsDouble radius;

    public Double getCx() {
        return ChartUtils.getDoubleOrNull(cx);
    }

    public void setCx(Double cx) {
        this.cx = ChartUtils.getChartsDoubleOrNull(cx);
    }

    public Double getCy() {
        return ChartUtils.getDoubleOrNull(cy);
    }

    public void setCy(Double cy) {
        this.cy = ChartUtils.getChartsDoubleOrNull(cy);
    }

    public Double getRadius() {
        return ChartUtils.getDoubleOrNull(radius);
    }

    public void setRadius(Double radius) {
        this.radius = ChartUtils.getChartsDoubleOrNull(radius);
    }

    @Override
    public void getFromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setCx(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.CX));
        setCy(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.CY));
        setRadius(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.RADIUS));
    }

    @Override
    public boolean putIntoJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.CX, cx);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.CY, cy);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.RADIUS, radius);

        return isAnyKeyNotNull;
    }

    @Override
    public boolean extractStyles(JSONWrapper json) {
        return putIntoJSON(json);           // since everything inside RadialGradientOptions are styles, just calling putIntoJSON
    }

    @Override
    public void resetStyles() {
        cx = null;
        cy = null;
        radius = null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        getFromJSON(json);
    }
}
