package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.headers;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.HashMap;
import java.util.Map;

/**
 * Chart's Data Meta Headers JSONWrapper Object Structure definition
 * <AUTHOR>
 */
public class Headers implements ChartOption {
    private Map<ChartsInteger, Header> headerMap;

    public Map<ChartsInteger, Header> getHeaderMap() {
        if(headerMap == null){
            headerMap = new HashMap<>();
        }

        return headerMap;
    }

    public Header getHeader(int index){
        return getHeaderMap().get(ChartUtils.toChartsInteger(index));
    }

    public void addHeader(Header header){
        getHeaderMap().put(ChartUtils.toChartsInteger(header.getIndex()), header);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null){
            return;
        }
        getHeaderMap().clear();
        ChartUtils.forEachKey(jsonObject, idx -> {
            Header header = new Header(Integer.parseInt(idx));

            header.fromJSON(jsonObject.getJSONObject(idx));
            addHeader(header);
        });
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        getHeaderMap().forEach((idx, header) -> ChartUtils.putIfNotNull(jsonObject, idx.toString(), header.toJSON()));
        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
