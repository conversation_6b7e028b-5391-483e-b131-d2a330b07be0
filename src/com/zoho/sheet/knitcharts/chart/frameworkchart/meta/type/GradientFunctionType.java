package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.Objects;

/**
 * Gradient function types
 * <AUTHOR>
 */
public enum GradientFunctionType implements Type {
    LINEAR("linear"),           // NO I18N
    RADIAL("radial");           // NO I18N

    public static GradientFunctionType retrieveByValue(String value) {
        if(Objects.equals(value, LINEAR.getValue())) {
            return LINEAR;
        } else if(Objects.equals(value, RADIAL.getValue())) {
            return RADIAL;
        } else {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
    }

    private final String value;

    private GradientFunctionType(String value) {
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
