package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.HashMap;
import java.util.Map;

public enum LegendPositionType implements Type {
    TOP("top"),                         // NO I18N
    BOTTOM("bottom"),                   // NO I18N
    LEFT("left"),                       // NO I18N
    RIGHT("right"),                     // NO I18N
    TOP_RIGHT("topRight");              // NO I18N

    private static Map<String, LegendPositionType> lookupMap;

    private static void initLookupMap(){
        lookupMap = new HashMap<>();
        for(LegendPositionType type : values()){
            lookupMap.put(type.value, type);
        }
    }

    public static LegendPositionType retrieveByValue(String value){
        if(ChartUtils.isNull(lookupMap)){
            initLookupMap();
        }
        LegendPositionType type = lookupMap.get(value);

        if (ChartUtils.isNull(type)) {
            throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
        }
        return type;
    }

    private final String value;
    LegendPositionType(String value){
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
