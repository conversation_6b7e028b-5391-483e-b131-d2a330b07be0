package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.yaxis;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.Axes;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.YAxisAffixesType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.yaxis.plotlines.PlotLines;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsDouble;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.YAxisScaleType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.YAxisUnitFormatType;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.Labels;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.GridLines;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.TickLines;

public class YAxes extends Axes {

    private YAxisScaleType type;

    private PlotLines plotLines;

    private Labels labels;

    private ChartsInteger min, max, tickInterval, baseValue;

    private YAxesTitle title;

    private StackLabels stackLabels;

    private GridLines gridLines;

    private YAxisUnitFormatType unitFormat;

    private ChartsDouble customValue;

    private TickLines tickLines;

    private YAxisAffixesType affixesType;

    public YAxes(int key) {
        super(key);
    }

    public YAxisScaleType getType() {
        return type;
    }

    public void setType(YAxisScaleType type) {
        this.type = type;
    }

    public Integer getMin() {
        return ChartUtils.getIntegerOrNull(min);
    }

    public void setMin(Integer min) {
        this.min = ChartUtils.getChartsIntegerOrNull(min);
    }

    public Integer getMax() {
        return ChartUtils.getIntegerOrNull(max);
    }

    public void setMax(Integer max) {
        this.max = ChartUtils.getChartsIntegerOrNull(max);
    }

    public Integer getTickInterval() {
        return ChartUtils.getIntegerOrNull(tickInterval);
    }

    public void setTickInterval(Integer tickInterval) {
        this.tickInterval = ChartUtils.getChartsIntegerOrNull(tickInterval);
    }

    public Integer getBaseValue() {
        return ChartUtils.getIntegerOrNull(baseValue);
    }

    public void setBaseValue(Integer baseValue) {
        this.baseValue = ChartUtils.getChartsIntegerOrNull(baseValue);
    }

    public YAxisUnitFormatType getUnitFormat() {
        return unitFormat;
    }

    public void setUnitFormat(YAxisUnitFormatType unitFormat) {
        this.unitFormat = unitFormat;
    }

    @Override
    public com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.TickLines getTickLines() {
        if (tickLines == null) {
            tickLines = new com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.TickLines();
        }
        return tickLines;
    }

    @Override
    public com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.Title getTitle() {
        if (ChartUtils.isNull(title)) {
            title = new YAxesTitle();
        }

        return title;
    }

    public StackLabels getStackLabels() {
        if (ChartUtils.isNull(stackLabels)) {
            stackLabels = new StackLabels();
        }

        return stackLabels;
    }

    @Override
    public com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.GridLines getGridLines() {
        if (ChartUtils.isNull(gridLines)) {
            gridLines = new com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.GridLines();
        }

        return gridLines;
    }

    public PlotLines getPlotLines() {
        if (ChartUtils.isNull(plotLines)) {
            plotLines = new PlotLines();
        }

        return plotLines;
    }

    @Override
    public com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.Labels getLabels() {
        if (ChartUtils.isNull(labels)) {
            labels = new com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis.Labels();
        }

        return labels;
    }

    public YAxisAffixesType getAffixesType() {
        return affixesType;
    }

    public void setAffixesType(YAxisAffixesType affixesType) {
        this.affixesType = affixesType;
    }

    private void setAffixesTypeString(String affixesType) {
        if(affixesType != null) {
            this.affixesType = YAxisAffixesType.retrieveByValue(affixesType);
        }
    }

    public Double getCustomValue() {
        return ChartUtils.getDoubleOrNull(customValue);
    }

    public void setCustomValue(Double customValue) {
        this.customValue = ChartUtils.getChartsDoubleOrNull(customValue);
    }

    private void applyStylesToStackLabels(JSONWrapper json) {
        if(json != null) {
            getStackLabels().applyStyles(json);
        }
    }

    private void applyStylesToPlotlines(JSONWrapper json) {
        if(json != null) {
            getPlotLines().applyStyles(json);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        super.fromJSON(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }

        /* Getting min from JSONObjectWrapper */
        setMin(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MIN));
        /* Getting max from JSONObjectWrapper */
        setMax(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MAX));
        /* Getting type from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.SCALE_TYPE),
                scaleType -> setType(YAxisScaleType.retrieveByValue((String) scaleType)));
        /* Getting tickInterval from JSONObjectWrapper */
        setTickInterval(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.TICKINTERVAL));
        /* Getting plotLines from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getPlotLines, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.KEY));
        /* Getting stackLabels from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getStackLabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.StackLabels.KEY));
        /* Getting baseValue from JSONObjectWrapper */
        setBaseValue(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.BASE_VALE));
        /* Getting format from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.UNIT_FORMAT),
                (type) -> setUnitFormat(YAxisUnitFormatType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting unit format custom value */
        setCustomValue(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.SCALE_CUSTOM_VALUE));
        /* Getting Y Affixes type */
        setAffixesTypeString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.Y_AFFIXES));

    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.toJSON()), StatelessLambda::newJSONObject);

        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.SCALE_TYPE, getType());
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MIN, min);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MAX, max);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.TICKINTERVAL, tickInterval);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.KEY, plotLines);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.StackLabels.KEY, stackLabels);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.BASE_VALE, baseValue);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.UNIT_FORMAT,  unitFormat);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.SCALE_CUSTOM_VALUE, customValue);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.Y_AFFIXES, affixesType);

        return jsonObject.isEmpty() ? null : jsonObject;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.extractStyles()), StatelessLambda::newJSONObject);

        ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.KEY, plotLines);
        ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.StackLabels.KEY, stackLabels);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.SCALE_TYPE, getType());
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MIN, min);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MAX, max);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.TICKINTERVAL, tickInterval);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.BASE_VALE, baseValue);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.UNIT_FORMAT,  unitFormat);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.SCALE_CUSTOM_VALUE, customValue);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.Y_AFFIXES, affixesType);

        return jsonObject.isEmpty() ? null : jsonObject;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        super.applyStyles(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting plotLines from JSONObjectWrapper */
        applyStylesToPlotlines(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.KEY));
        /* Getting stackLabels from JSONObjectWrapper */
        applyStylesToStackLabels(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.StackLabels.KEY));
        /* Getting min from JSONObjectWrapper */
        setMin(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MIN));
        /* Getting max from JSONObjectWrapper */
        setMax(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MAX));
        /* Getting type from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.SCALE_TYPE),
                scaleType -> setType(YAxisScaleType.retrieveByValue((String) scaleType)));
        /* Getting tickInterval from JSONObjectWrapper */
        setTickInterval(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.TICKINTERVAL));
        /* Getting baseValue from JSONObjectWrapper */
        setBaseValue(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.BASE_VALE));
        /* Getting format from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.UNIT_FORMAT),
                (type) -> setUnitFormat(YAxisUnitFormatType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting unit format custom value */
        setCustomValue(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.SCALE_CUSTOM_VALUE));
        /* Getting Y Affixes type */
        setAffixesTypeString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.Y_AFFIXES));
    }

    @Override
    public void resetStyles() {
        super.resetStyles();
        type = null;
        min = null;
        max = null;
        tickInterval = null;
        baseValue = null;
        unitFormat = null;
        customValue = null;
        affixesType = null;
        ChartUtils.resetStyles(plotLines, stackLabels);
    }

    @Override
    public JSONWrapper toJSON(Workbook workbook, Chart chart, Key key) {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.toJSON(workbook, chart, key)), StatelessLambda::newJSONObject);

        ChartUtils.putKeyOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.PlotLines.KEY, plotLines,
                workbook, chart, key);
        ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.PREFIX, FrameworkChartGetterAPI.getYAxisNumberFormatPrefix
                (workbook, chart.getSheetMeta(), chart.getApiMeta().getChartMeta(), key.getValue()));
        ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.SUFFIX, FrameworkChartGetterAPI.getYAxisNumberFormatSuffix
                (workbook, chart.getSheetMeta(), chart.getApiMeta().getChartMeta(), key.getValue()));
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MIN, FrameworkChartGetterAPI.getYAxisMinFromReference
                (workbook, chart.getSheetMeta(), chart.getApiMeta().getChartMeta(), key.getValue()));
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.MAX, FrameworkChartGetterAPI.getYAxisMaxValueFromReference
                (workbook, chart.getSheetMeta(), chart.getApiMeta().getChartMeta(), key.getValue()));
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.TICKINTERVAL, FrameworkChartGetterAPI.getYAxisIntervalFromReference
                (workbook, chart.getSheetMeta(), chart.getApiMeta().getChartMeta(), key.getValue()));

        return jsonObject.isEmpty() ? null : jsonObject;
    }
}


