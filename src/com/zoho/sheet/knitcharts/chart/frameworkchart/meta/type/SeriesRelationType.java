package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type;

import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.chart.Type;

import java.util.Objects;

public enum SeriesRelationType implements Type {
    SINGLE("single"),                   // NO I18N
    PAIR("pair");                       // NO I18N


    public static SeriesRelationType retrieveByValue(String value) {
        if(Objects.equals(value, SeriesRelationType.SINGLE.value)) {
            return SeriesRelationType.SINGLE;
        } else if(Objects.equals(value, SeriesRelationType.PAIR.value)) {
            return SeriesRelationType.PAIR;
        }
        throw new IllegalArgumentException(String.format("%s: %s", ErrorMessages.INVALID_TYPE_VALUE, value));           // NO I18N
    }

    private final String value;

    SeriesRelationType(String value) {
        this.value = value;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
