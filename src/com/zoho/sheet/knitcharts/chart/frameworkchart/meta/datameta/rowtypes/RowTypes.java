package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.rowtypes;

import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.HashMap;
import java.util.Map;

import static com.zoho.sheet.knitcharts.utils.ChartUtils.toChartsInteger;

public class RowTypes implements ChartOption {
    private Map<ChartsInteger, RowType> rowTypeMap;

    public Map<ChartsInteger, RowType> getRowTypeMap() {
        if(rowTypeMap == null){
            rowTypeMap = new HashMap<>();
        }

        return rowTypeMap;
    }

    public RowType getRowType(int index){
        return getRowTypeMap().get(toChartsInteger(index));
    }

    public void addRowType(RowType rowType){
        getRowTypeMap().put(toChartsInteger(rowType.getIndex()), rowType);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null){
            return;
        }
        getRowTypeMap().clear();
        ChartUtils.forEachKey(jsonObject, idx -> {
            RowType type = new RowType(Integer.parseInt(idx));
            type.fromJSON(jsonObject.getJSONObject(idx));
            addRowType(type);
        });
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        getRowTypeMap().forEach((idx, rowType) -> ChartUtils.putIfNotNull(jsonObject, idx.toString(), rowType.toJSON()));
        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
