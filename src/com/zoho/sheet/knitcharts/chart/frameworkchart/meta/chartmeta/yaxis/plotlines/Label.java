package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.yaxis.plotlines;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartFontOption;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class Label extends ChartFontOption implements ChartKeyOnTheFlyOption {

    private ChartsString text;

    public String getText() {
        return ChartUtils.getStringOrNull(text);
    }

    public void setText(String text) {
        this.text = ChartUtils.getChartsStringOrNull(text);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        super.fromJSON(json);
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting label text from JSONObjectWrapper */
        setText(ChartUtils.optDecodedStringFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.TEXT));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(super.toJSON()), StatelessLambda::newJSONObject);

        /* Setting data in JSONObjectWrapper */
        ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.TEXT, text);

        return jsonObject.isEmpty() ? null : jsonObject;
    }

    @Override
    public JSONWrapper toJSON(Workbook workbook, Chart chart, Key key) {
        int yAxisIndex = key.getParent().getValue();
        int plotLineIndex = key.getValue();
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(toJSON()), StatelessLambda::newJSONObject);
        String label = SheetChartGetterAPI.getPlotlinesLabelFromReference(workbook, chart.getSheetMeta(), yAxisIndex, plotLineIndex);


        ChartUtils.putEncodedIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.TEXT, label == null ? ChartUtils.getStringOrNull(text) : label);

        return jsonObject.isEmpty() ? null : jsonObject;
    }
}
