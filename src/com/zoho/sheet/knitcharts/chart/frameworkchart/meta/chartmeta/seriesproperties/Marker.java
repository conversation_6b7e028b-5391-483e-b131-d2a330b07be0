package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties;

import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption;
import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartStyleOption;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.MarkerShapeType;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class Marker implements GroupedChartStyleOption, GroupedChartOption {

    private ChartsBoolean status;

    private MarkerShapeType shape;

    private ChartsInteger size;

    private ChartsString color, borderColor;

    public Boolean getStatus() {
        return ChartUtils.getBooleanOrNull(status);
    }

    public void setStatus(Boolean status) {
        this.status = ChartUtils.getChartsBooleanOrNull(status);
    }

    public MarkerShapeType getShape() {
        return shape;
    }

    public void setShape(MarkerShapeType shape) {
        this.shape = shape;
    }

    public Integer getSize() {
        return ChartUtils.getIntegerOrNull(size);
    }

    public void setSize(Integer size) {
        this.size = ChartUtils.getChartsIntegerOrNull(size);
    }

    public String getColor() {
        return ChartUtils.getStringOrNull(color);
    }

    public void setColor(String color) {
        this.color = ChartUtils.getChartsStringOrNull(color);
    }

    public String getBorderColor() {
        return ChartUtils.getStringOrNull(borderColor);
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = ChartUtils.getChartsStringOrNull(borderColor);
    }

    public void resetStyles() {
        shape = null;
        size = null;
        color = null;
        borderColor = null;
    }

    @Override
    public void getFromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        /* Getting markerStatus from JSONObjectWrapper */
        setStatus(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.STATUS));
        /* the remaining properties are styles so just calling apply styles method */
        applyStyles(json);
    }

    @Override
    public boolean putIntoJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.STATUS, status);

        isAnyKeyNotNull |= extractStyles(json);
        return isAnyKeyNotNull;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        
        /* Getting markerShape from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.SHAPE),
                type -> setShape(MarkerShapeType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting markerSize from JSONObjectWrapper */
        setSize(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.SIZE));
        /* Getting markerColor from JSONObjectWrapper */
        setColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.COLOR));
        /* Getting markerBorderColor from JSONObjectWrapper */
        setBorderColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.BORDER_COLOR));
    }

    @Override
    public boolean extractStyles(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.SHAPE, shape);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.SIZE, size);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.COLOR, color);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.Marker.BORDER_COLOR, borderColor);
        return isAnyKeyNotNull;
    }
}
