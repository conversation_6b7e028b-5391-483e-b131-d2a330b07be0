package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOnTheFlyOption;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.plotoptions.PlotOptions;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.totalLabel.TotalLabel;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.caption.Caption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.totaldatalabels.TotalDatalabels;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.legend.Legend;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.seriesproperties.SeriesProperties;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.sharedproperties.SharedProperties;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.subtitle.Subtitle;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.title.Title;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.tooltip.Tooltip;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.xaxis.XAxis;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.yaxis.YAxis;
import com.zoho.sheet.knitcharts.miscellaneous.exception.InvalidChartMetaException;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class ChartMeta implements ChartOption, ChartStyleOption, ChartOnTheFlyOption {

    private Chart chart;

    private Title title;

    private Subtitle subtitle;

    private Caption caption;

    private Legend legend;

    private TotalDatalabels totalDatalabels;

    private SeriesProperties seriesProperties;

    private XAxis xAxis;

    private YAxis yAxis;

    private Tooltip tooltip;

    private Filter filter;

    private SharedProperties sharedProperties;

    private PlotOptions plotOptions;

    private TotalLabel totalLabel;

    public Chart getChart() {
        if (ChartUtils.isNull(chart)) {
            chart = new Chart();
        }
        return chart;
    }

    public Title getTitle() {
        if (ChartUtils.isNull(title)) {
            title = new Title();
        }
        return title;
    }

    public Subtitle getSubtitle() {
        if (ChartUtils.isNull(subtitle)) {
            subtitle = new Subtitle();
        }
        return subtitle;
    }

    public Caption getCaption() {
        if (ChartUtils.isNull(caption)) {
            caption = new Caption();
        }
        return caption;
    }

    public Legend getLegend() {
        if (ChartUtils.isNull(legend)) {
            legend = new Legend();
        }
        return legend;
    }

    public TotalDatalabels getTotalDatalabels() {
        if (ChartUtils.isNull(totalDatalabels)) {
            totalDatalabels = new TotalDatalabels();
        }
        return totalDatalabels;
    }

    public SeriesProperties getSeriesProperties() {
        if (ChartUtils.isNull(seriesProperties)) {
            seriesProperties = new SeriesProperties();
        }
        return seriesProperties;
    }

    public XAxis getxAxis() {
        if (ChartUtils.isNull(xAxis)) {
            xAxis = new XAxis();
        }
        return xAxis;
    }

    public YAxis getyAxis() {
        if (ChartUtils.isNull(yAxis)) {
            yAxis = new YAxis();
        }
        return yAxis;
    }

    public Tooltip getTooltip() {
        if (ChartUtils.isNull(tooltip)) {
            tooltip = new Tooltip();
        }
        return tooltip;
    }

    public Filter getFilter(){
        if(filter == null){
            filter = new Filter();
        }
        return filter;
    }

    public TotalLabel getTotalLabel() {
        if (totalLabel == null) {
            totalLabel = new TotalLabel();
        }
        return totalLabel;
    }

    public SharedProperties getSharedProperties(){
        if(sharedProperties == null){
            sharedProperties = new SharedProperties();
        }
        return sharedProperties;
    }

    public PlotOptions getPlotOptions() {
        if (plotOptions == null) {
            plotOptions = new PlotOptions();
        }
        return plotOptions;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            throw new InvalidChartMetaException("Invalid Chart meta \n" + json);            // NO I18N
        }

        /* Getting chart from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getChart, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.KEY));
        /* Getting title from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getTitle, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Title.KEY));
        /* Getting subtitle from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getSubtitle, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Subtitle.KEY));
        /* Getting caption from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getCaption, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Caption.KEY));
        /* Getting legend from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getLegend, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Legend.KEY));
        /* Getting donutTotalDatalabels from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getTotalDatalabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalDataLabels.KEY));
        /* Getting seriesProperties from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getSeriesProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.KEY));
        /* Getting xAxis from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getxAxis, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.KEY));
        /* Getting yAxis from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getyAxis, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.KEY));
        /* Getting tooltip from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getTooltip, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Tooltip.KEY));
        /* Getting filter data from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getFilter, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Filter.KEY));
        /* Getting shared properties from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getSharedProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SharedProperties.KEY));
        /* Getting plot options from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getPlotOptions, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.PlotOptions.KEY));
        /* Getting total label from JSONObjectWrapper */
        ChartUtils.setChartOptions(this::getTotalLabel, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalLabel.KEY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.KEY, chart);

        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Title.KEY, title);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Subtitle.KEY, subtitle);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Caption.KEY, caption);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Legend.KEY, legend);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalDataLabels.KEY, totalDatalabels);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.KEY, seriesProperties);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.KEY, xAxis);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.KEY, yAxis);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Tooltip.KEY, tooltip);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Filter.KEY, filter);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SharedProperties.KEY, sharedProperties);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.PlotOptions.KEY, plotOptions);
        ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalLabel.KEY, totalLabel);

        return jsonObject;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper styleJSON = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.Title.KEY, title);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.Subtitle.KEY, subtitle);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.Caption.KEY, caption);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.Legend.KEY, legend);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.Chart.KEY, chart);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.XAxis.KEY, xAxis);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.KEY, seriesProperties);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.YAxis.KEY, yAxis);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.SharedProperties.KEY, sharedProperties);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.TotalDataLabels.KEY, totalDatalabels);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.TotalLabel.KEY, totalLabel);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(styleJSON, FrameworkChartMetaConstants.ChartMeta.Credits.KEY, chart);

        return isAnyKeyNotNull ? styleJSON : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null) { return; }

        ChartUtils.applyChartStyles(this::getTitle, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Title.KEY));
        ChartUtils.applyChartStyles(this::getSubtitle, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Subtitle.KEY));
        ChartUtils.applyChartStyles(this::getChart, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.KEY));
        ChartUtils.applyChartStyles(this::getCaption, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Caption.KEY));
        ChartUtils.applyChartStyles(this::getxAxis, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.KEY));
        ChartUtils.applyChartStyles(this::getSeriesProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.KEY));
        ChartUtils.applyChartStyles(this::getyAxis, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.KEY));
        ChartUtils.applyChartStyles(this::getSharedProperties, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.SharedProperties.KEY));
        ChartUtils.applyChartStyles(this::getLegend, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Legend.KEY));
        ChartUtils.applyChartStyles(this::getTotalDatalabels, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalDataLabels.KEY));
        ChartUtils.applyChartStyles(this::getTotalLabel, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalLabel.KEY));
    }

    @Override
    public void resetStyles() {
        ChartUtils.resetStyles(title, subtitle, chart, caption, legend,
                totalDatalabels, seriesProperties, yAxis, sharedProperties, xAxis, totalLabel);
    }

    @Override
    public void onChartTypeChange() {
        ChartUtils.onChartTypeChange(seriesProperties, sharedProperties);
    }

    @Override
    public JSONWrapper toJSON(Workbook workbook, com.zoho.sheet.knitcharts.chart.Chart chart) {
        JSONObjectWrapper jsonObject = ChartUtils.requireNonNullElseGet(ChartUtils.JSONToJSONObject(toJSON()), StatelessLambda::newJSONObject);

        ChartUtils.putOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Title.KEY, title, workbook, chart);
        ChartUtils.putOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Subtitle.KEY, subtitle, workbook, chart);
        ChartUtils.putOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.XAxis.KEY, xAxis, workbook, chart);
        ChartUtils.putOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.YAxis.KEY, yAxis, workbook, chart);
        ChartUtils.putOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.TotalDataLabels.KEY, totalDatalabels, workbook, chart);
        ChartUtils.putOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SeriesProperties.KEY, seriesProperties, workbook, chart);
        ChartUtils.putOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.SharedProperties.KEY, sharedProperties, workbook, chart);
        ChartUtils.putOnTheFlyOptionIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Legend.KEY, legend, workbook, chart);

        return jsonObject;
    }
}
