package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.FilterType;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Chart Data Filter Options
 * <AUTHOR>
 */
public class Filter implements ChartOption {

    private ChartsInteger seriesIndex;

    private ChartsInteger count;

    private FilterType filterType;

    public void setSeriesIndex(Integer seriesIndex){
        this.seriesIndex = ChartUtils.getChartsIntegerOrNull(seriesIndex);
    }

    public Integer getSeriesIndex(){
        return ChartUtils.getIntegerOrNull(seriesIndex);
    }

    public void setCount(Integer count){
        this.count = ChartUtils.getChartsIntegerOrNull(count);
    }

    public Integer getCount(){
        return ChartUtils.getIntegerOrNull(count);
    }

    public FilterType getFilterType() {
        return filterType;
    }

    public void setFilterType(FilterType filterType) {
        this.filterType = filterType;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if(jsonObject == null){
            return;
        }
        setSeriesIndex(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Filter.SERIES_INDEX));
        setCount(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Filter.COUNT));
        String filterType = ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Filter.FILTER_TYPE);

        if(filterType != null){
            setFilterType(FilterType.retrieveByValue(filterType));
        }
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Filter.COUNT, count);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Filter.SERIES_INDEX, seriesIndex);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Filter.FILTER_TYPE, filterType);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
