package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.chart;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.borderoptions.BorderOptions;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.*;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.gradient.Gradient;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.shadow.Shadow;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ChartException;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsBoolean;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsDouble;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * The Chart class represents a chart with various options defined under the "chart" key.
 * The chart options are accessible and manipulable through methods provided by this class.
 *
 * <AUTHOR>
 */
public class Chart implements ChartOption, ChartStyleOption {

    private ChartsString backgroundColor, fontFamily;

    private ChartType chartType;

    private ChartsBoolean isAxisInverted;

    private StatusType navStatus, navScrollBarStatus, rangeSelectorStatus;

    private Colors colors;

    private Gradient gradient;

    private Animation animation;

    private ChartsDouble backgroundOpacity;

    private ColorFillType fillType;

    private Margin margin;

    private Shadow shadow;

    private Border border;

    private NumberFormatType numberFormat;

    private Credits credits;

    public String getBackgroundColor() {
        return ChartUtils.getStringOrNull(backgroundColor);
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = ChartUtils.getChartsStringOrNull(backgroundColor);
    }

    public ChartType getChartType() {
        return chartType;
    }

    public void setChartType(ChartType chartType) {
        if(chartType == null) {
            throw new ChartException("[CHARTS]: Chart type can't be null.");                                                        /// NO I18N
        }
        this.chartType = chartType;
    }

    public String getFontFamily() {
        return ChartUtils.getStringOrNull(fontFamily);
    }

    public void setFontFamily(String fontFamily) {
        this.fontFamily = ChartUtils.getChartsStringOrNull(fontFamily);
    }

    public Boolean getAxisInverted() {
        return ChartUtils.getBooleanOrNull(isAxisInverted);
    }

    public void setAxisInverted(Boolean axisInverted) {
        isAxisInverted = ChartUtils.getChartsBooleanOrNull(axisInverted);
    }

    public Colors getColors() {
        if (ChartUtils.isNull(colors)) {
            colors = new Colors();
        }
        return colors;
    }

    public Gradient getGradient() {
        if (ChartUtils.isNull(gradient)) {
            gradient = new Gradient();
        }

        return gradient;
    }

    public Animation getAnimation() {
        if (ChartUtils.isNull(animation)) {
            animation = new Animation();
        }

        return animation;
    }

    public Border getBorder() {
        if (ChartUtils.isNull(border)) {
            border = new Border();
        }
        return border;
    }

    public Credits getCredits() {
        if (credits == null) {
            credits = new Credits();
        }
        return credits;
    }

    private void migrateOldBorderOptions(JSONObjectWrapper options) {
        BorderOptions newBorder = getBorder().getBorderOptions();
        BorderOptions oldBorder = new BorderOptions(new Border.OldBorderOptionsKeySupplier());

        oldBorder.getFromJSON(options);

        if(newBorder.getColor() == null) { newBorder.setColor(oldBorder.getColor()); }
        if(newBorder.getWidth() == null) { newBorder.setWidth(oldBorder.getWidth()); }
        if(newBorder.getRadius() == null) { newBorder.setRadius(oldBorder.getRadius()); }
        if(newBorder.getFillType() == null) { newBorder.setFillType(oldBorder.getFillType()); }
    }

    public Double getBackgroundOpacity() {
        return ChartUtils.getDoubleOrNull(backgroundOpacity);
    }

    public void setBackgroundOpacity(Double backgroundOpacity) {
        this.backgroundOpacity = ChartUtils.getChartsDoubleOrNull(backgroundOpacity);
    }

    public ColorFillType getFillType() {
        return fillType;
    }

    public void setFillType(ColorFillType fillType) {
        this.fillType = fillType;
    }

    private void setFillTypeFromString(String fillType) {
        if(fillType != null) {
            this.fillType = ColorFillType.retrieveByValue(fillType);
        }
    }

    private void setColors(JSONWrapper json) {
        if(json != null) {
            getColors().fromJSON(json);
        }
    }

    private void setGradient(JSONWrapper json) {
        if(json != null) {
            getGradient().fromJSON(json);
        }
    }

    private void setAnimation(JSONWrapper json) {
        if(json != null) {
            getAnimation().fromJSON(json);
        }
    }

    public Margin getMargin() {
        if(margin == null) {
            margin = new Margin();
        }

        return margin;
    }

    public Shadow getShadow() {
        if(shadow == null) {
            shadow = new Shadow();
        }
        return shadow;
    }

    public NumberFormatType getNumberFormat() {
        return numberFormat;
    }

    public void setNumberFormat(NumberFormatType numberFormat) {
        this.numberFormat = numberFormat;
    }

    private void setNumberFormatFromString(String numberFormat) {
        if(numberFormat != null) {
            this.numberFormat = NumberFormatType.retrieveByValue(numberFormat);
        }
    }

    public StatusType getNavigatorStatus() {
        return navStatus;
    }

    public void setNavigatorStatus(StatusType navStatus) {
        this.navStatus = navStatus;
    }

    public StatusType getNavigatorScrollBarStatus() {
        return navScrollBarStatus;
    }

    public void setNavigatorScrollBarStatus(StatusType navScrollBarStatus) {
        this.navScrollBarStatus = navScrollBarStatus;
    }

    public StatusType getRangeSelectorStatus() {
        return rangeSelectorStatus;
    }

    public void setRangeSelectorStatus(StatusType rangeSelectorStatus) {
        this.rangeSelectorStatus = rangeSelectorStatus;
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting backgroundColour from JSONObjectWrapper */
        setBackgroundColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_COLOR));
        /* Getting chartType from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.TYPE),
                type -> setChartType(ChartType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting fontFamily from JSONObjectWrapper */
        setFontFamily(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.FONT_FAMILY));
        /* Getting isAxesInverted from JSONObjectWrapper */
        setAxisInverted(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.IS_AXES_INVERTED));
        /* Getting colors from JSONObjectWrapper */
        setColors(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.COLORS));
        /* Getting gradient from JSONObjectWrapper */
        setGradient(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.KEY));
        /* Getting Animation from JSONObjectWrapper */
        setAnimation(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.Animation.KEY));
        /* Getting background opacity from JSONObjectWrapper */
        setBackgroundOpacity(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_OPACITY));
        /* Getting area fill type */
        setFillTypeFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.FILL));
        /* getting margin data */
        ChartUtils.setGroupedChartOptions(this::getMargin, jsonObject);
        /* getting shadow data */
        ChartUtils.setChartOptions(this::getShadow, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BOX_SHADOW));
        /* getting chart border options */
        ChartUtils.setChartOptions(this::getBorder, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY));
        /* getting number format options */
        setNumberFormatFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NUMBER_FORMAT));
        /* getting navigator enabled */
        setNavigatorStatus(ChartUtils.retrieveTypeByValue(StatusType.class, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NAVIGATOR_STATUS)));
        /* getting navigator scroll bar enabled */
        setNavigatorScrollBarStatus(ChartUtils.retrieveTypeByValue(StatusType.class, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NAVIGATOR_SCROLL_BAR_STATUS)));
        /* getting range selector enabled */
        setRangeSelectorStatus(ChartUtils.retrieveTypeByValue(StatusType.class, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.RANGE_SELECTOR_STATUS)));
        /* getting credits options*/
        ChartUtils.setChartOptions(this::getCredits, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Credits.KEY));
        /* migrating old border options */
        migrateOldBorderOptions(jsonObject);
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_COLOR, backgroundColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.CommonKeys.TYPE, chartType);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.FONT_FAMILY, fontFamily);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.IS_AXES_INVERTED, isAxisInverted);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.COLORS, colors);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.KEY, gradient);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.Animation.KEY, animation);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_OPACITY, backgroundOpacity);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.FILL, fillType);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BOX_SHADOW, shadow);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, margin);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY, border);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NUMBER_FORMAT, numberFormat);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NAVIGATOR_STATUS, navStatus);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NAVIGATOR_SCROLL_BAR_STATUS, navScrollBarStatus);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.RANGE_SELECTOR_STATUS, rangeSelectorStatus);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Credits.KEY, credits);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_COLOR, backgroundColor);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.FONT_FAMILY, fontFamily);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.COLORS, colors);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.KEY, gradient);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_OPACITY, backgroundOpacity);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.FILL, fillType);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, margin);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BOX_SHADOW, shadow);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY, border);
        isAnyKeyNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Credits.KEY, credits);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        

        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);
        if (ChartUtils.isNull(jsonObject)) {
            return;
        }

        /* Getting backgroundColour from JSONObjectWrapper */
        setBackgroundColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_COLOR));
        /* Getting fontFamily from JSONObjectWrapper */
        setFontFamily(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.FONT_FAMILY));
        /* Getting colors from JSONObjectWrapper */
        setColors(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.COLORS));
        /* Getting gradient from JSONObjectWrapper */
        setGradient(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Gradient.KEY));
        /* Getting background opacity from JSONObjectWrapper */
        setBackgroundOpacity(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BACKGROUND_OPACITY));
        /* Getting area fill type */
        setFillTypeFromString(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.FILL));
        /* Getting margin styles */
        ChartUtils.applyGroupedChartStyles(this::getMargin, jsonObject);
        /* getting shadow data */
        ChartUtils.applyChartStyles(this::getShadow, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.BOX_SHADOW));
        /* getting border styles */
        fromJSON(jsonObject);
        ChartUtils.applyChartStyles(this::getBorder, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Chart.NewBorder.KEY));
        /* getting credits styles */
        ChartUtils.applyChartStyles(this::getCredits, ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Credits.KEY));
    }

    @Override
    public void resetStyles() {
        backgroundColor = null;
        backgroundOpacity = null;
        fontFamily = null;
        fillType = null;
        ChartUtils.resetStyles(colors, gradient, shadow, border, credits);
        ChartUtils.resetStyles(margin);
    }
}
