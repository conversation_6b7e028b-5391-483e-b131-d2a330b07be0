package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.sharedproperties;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.coltypes.ColType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.rowtypes.RowType;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * Data Meta Shared properties
 * <AUTHOR>
 */
public class SharedProperties implements ChartOption {

    private ColType colInfo;

    private RowType rowInfo;

    public ColType getColInfo() {
        if (colInfo == null) {
            colInfo = new ColType(0);
        }
        return colInfo;
    }

    public RowType getRowInfo() {
        if (rowInfo == null) {
            rowInfo = new RowType(0);
        }
        return rowInfo;
    }

    private void setColInfo(JSONObjectWrapper jsonObject){
        if(jsonObject != null) {
            getColInfo().fromJSON(jsonObject);
        }
    }

    private void setRowInfo(JSONObjectWrapper jsonObject){
        if(jsonObject != null){
            getRowInfo().fromJSON(jsonObject);
        }
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if(jsonObject == null) { return; }
        setRowInfo(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.DataMeta.SharedProperties.ROW_INFO));
        setColInfo(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.DataMeta.SharedProperties.COL_INFO));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.DataMeta.SharedProperties.ROW_INFO, rowInfo);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.DataMeta.SharedProperties.COL_INFO, colInfo);
        return isAnyKeyNotNull ? jsonObject : null;
    }
}
