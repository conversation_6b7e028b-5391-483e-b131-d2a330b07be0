package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.axis;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsDouble;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.GridLineCountType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.LineStyleType;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class GridLines implements ChartOption, ChartStyleOption {

    private Boolean majorGLEnabled, minorGLEnabled;

    private GridLineCountType majorGLCount;

    private String majorGLColor, minorGLColor;

    private LineStyleType majorGLType, minorGLType;

    private ChartsInteger majorGLWidth, minorGLWidth;

    private ChartsDouble majorGLOpacity, minorGLOpacity;

    public Boolean getMajorGLEnabled() {
        return majorGLEnabled;
    }

    public void setMajorGLEnabled(Boolean majorGLEnabled) {
        this.majorGLEnabled = majorGLEnabled;
    }

    public Boolean getMinorGLEnabled() {
        return minorGLEnabled;
    }

    public void setMinorGLEnabled(Boolean minorGLEnabled) {
        this.minorGLEnabled = minorGLEnabled;
    }

    public GridLineCountType getMajorGLCount() {
        return majorGLCount;
    }

    public void setMajorGLCount(GridLineCountType majorGLCount) {
        this.majorGLCount = majorGLCount;
    }

    public LineStyleType getMajorGLType() {
        return majorGLType;
    }

    public void setMajorGLType(LineStyleType majorGLType) {
        this.majorGLType = majorGLType;
    }

    public String getMajorGLColor() {
        return majorGLColor;
    }

    public void setMajorGLColor(String majorGLColor) {
        this.majorGLColor = majorGLColor;
    }

    public LineStyleType getMinorGLType() {
        return minorGLType;
    }

    public void setMinorGLType(LineStyleType minorGLType) {
        this.minorGLType = minorGLType;
    }

    public String getMinorGLColor() {
        return minorGLColor;
    }

    public void setMinorGLColor(String minorGLColor) {
        this.minorGLColor = minorGLColor;
    }

    public Integer getMajorGLWidth() {
        return ChartUtils.getIntegerOrNull(majorGLWidth);
    }

    public void setMajorGLWidth(Integer majorGLWidth) {
        this.majorGLWidth = ChartUtils.getChartsIntegerOrNull(majorGLWidth);
    }

    public Integer getMinorGLWidth() {
        return ChartUtils.getIntegerOrNull(minorGLWidth);
    }

    public void setMinorGLWidth(Integer minorGLWidth) {
        this.minorGLWidth = ChartUtils.getChartsIntegerOrNull(minorGLWidth);
    }

    public Double getMajorGLOpacity() {
        return ChartUtils.getDoubleOrNull(majorGLOpacity);
    }

    public void setMajorGLOpacity(Double majorGLOpacity) {
        this.majorGLOpacity = ChartUtils.getChartsDoubleOrNull(majorGLOpacity);
    }

    public Double getMinorGLOpacity() {
        return ChartUtils.getDoubleOrNull(minorGLOpacity);
    }

    public void setMinorGLOpacity(Double minorGLOpacity) {
        this.minorGLOpacity = ChartUtils.getChartsDoubleOrNull(minorGLOpacity);
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        if (ChartUtils.isNull(jsonObject)) {
            return;
        }
        /* Getting isMajorGLEnabled from JSONObjectWrapper */
        setMajorGLEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.ENABLED));
        /* Getting majorGLCount from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.COUNT),
                (type) -> setMajorGLCount(GridLineCountType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting majorGLType from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.TYPE),
                (type) -> setMajorGLType(LineStyleType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting majorGLColor from JSONObjectWrapper */
        setMajorGLColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.COLOR));
        /* Getting isMinorGLEnabled from JSONObjectWrapper */
        setMinorGLEnabled(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.ENABLED));
        /* Getting minorGLType from JSONObjectWrapper */
        ChartUtils.ifNotNullThen(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.TYPE),
                (type) -> setMinorGLType(LineStyleType.retrieveByValue(ChartUtils.typeCast(type))));
        /* Getting minorGLColor from JSONObjectWrapper */
        setMinorGLColor(ChartUtils.optFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.COLOR));
        /* Getting major grid line width */
        setMajorGLWidth(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.WIDTH));
        /* Getting minor grid line width */
        setMinorGLWidth(ChartUtils.optIntegerFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.WIDTH));
        /* Getting major grid line opacity */
        setMajorGLOpacity(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.OPACITY));
        /* Getting minor grid line opacity */
        setMinorGLOpacity(ChartUtils.optDoubleFromJSONObject(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.OPACITY));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        boolean isAnyKeyNotNull = ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.ENABLED, majorGLEnabled);

        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.COUNT, majorGLCount);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.TYPE, majorGLType);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.COLOR, majorGLColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.ENABLED, minorGLEnabled);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.TYPE, minorGLType);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.COLOR, minorGLColor);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.WIDTH, majorGLWidth);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.WIDTH, minorGLWidth);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MajorGridLines.OPACITY, majorGLOpacity);
        isAnyKeyNotNull |= ChartUtils.putIfNotNull(jsonObject, FrameworkChartMetaConstants.ChartMeta.Axis.GridLines.MinorGridLines.OPACITY, minorGLOpacity);

        return isAnyKeyNotNull ? jsonObject : null;
    }

    @Override
    public JSONWrapper extractStyles() {
        return toJSON();
    }

    @Override
    public void applyStyles(JSONWrapper json) {
       fromJSON(json);
    }

    @Override
    public void resetStyles() {
        majorGLEnabled = null;
        minorGLEnabled = null;
        majorGLType = null;
        minorGLType = null;
        majorGLColor = null;
        minorGLColor = null;
        minorGLWidth = null;
        majorGLWidth = null;
        majorGLOpacity = null;
        minorGLOpacity = null;
        majorGLCount = null;
    }
}
