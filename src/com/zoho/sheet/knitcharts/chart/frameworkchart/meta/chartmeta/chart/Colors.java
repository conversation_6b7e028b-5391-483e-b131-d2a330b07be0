package com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.chart;

import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartOption;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsString;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * The Colors class represents a collection of colors that can be used as a color theme for a chart.
 * The colors are stored as a list of color values represented as strings.
 *
 * <AUTHOR>
 */
public class Colors implements ChartOption, ChartStyleOption {
    private final List<ChartsString> colors;

    public Colors() {
        colors = new ArrayList<>();
    }

    public void addColor(String color) {
        colors.add(ChartUtils.getChartsStringOrNull(color));
    }

    public void setColors(List<String> colors){
        if(colors == null){return;}
        this.colors.clear();
        colors.forEach(this::addColor);
    }

    public List<String> getColors() {
        return colors.stream().map(ChartUtils::getStringOrNull).collect(Collectors.toList());
    }

    @Override
    public void fromJSON(JSONWrapper json) {
        JSONArrayWrapper jsonArray = ChartUtils.JSONTOJSONArray(json);

        if (ChartUtils.isNull(jsonArray)) {
            return;
        }
        colors.clear();
        ChartUtils.forEach(jsonArray, colour -> addColor(ChartUtils.typeCast(colour)));
    }

    @Override
    public JSONWrapper toJSON() {
        JSONArrayWrapper jsonArray = JSONArrayWrapper.fromCollection(getColors());

        return jsonArray.isEmpty() ? null : jsonArray;
    }

    @Override
    public JSONWrapper extractStyles() {
        JSONArrayWrapper jsonArray = JSONArrayWrapper.fromCollection(getColors());

        return jsonArray.isEmpty() ? null : jsonArray;
    }

    @Override
    public void applyStyles(JSONWrapper json) {
        

        JSONArrayWrapper jsonArray = ChartUtils.JSONTOJSONArray(json);

        if (ChartUtils.isNull(jsonArray)) {
            return;
        }
        colors.clear();
        ChartUtils.forEach(jsonArray, colour -> addColor(ChartUtils.typeCast(colour)));
    }

    @Override
    public void resetStyles() {
        colors.clear();
    }
}
