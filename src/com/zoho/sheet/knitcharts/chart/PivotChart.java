package com.zoho.sheet.knitcharts.chart;

import com.zoho.sheet.knitcharts.chart.sheetchart.meta.PivotChartSheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;

public class <PERSON>vot<PERSON>hart extends Chart {

    public PivotChart(String chartID, String associatedSheetName) {
        super(chartID, associatedSheetName);
    }

    @Override
    public boolean isPivotChart() {
        return true;
    }

    @Override
    public SheetMeta getSheetMeta() {
        return super.getSheetMeta();
    }

    @Override
    public void setSheetMeta(SheetMeta sheetMeta) {
        if(sheetMeta instanceof PivotChartSheetMeta) {
            super.setSheetMeta(sheetMeta);
        } else {
            throw new IllegalArgumentException("PivotChart does not support normal sheet meta.");           // NO I18N
        }
    }
}
