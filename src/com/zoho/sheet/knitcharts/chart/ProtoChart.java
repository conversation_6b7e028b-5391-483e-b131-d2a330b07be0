package com.zoho.sheet.knitcharts.chart;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.DataMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;

/**
 * Represents early stage of a chart.
 * Temporary Chart representation used before create actual Chart
 * <AUTHOR>
 */
public class ProtoChart {

    public String chartID;

    public ChartMeta chartMeta;

    public SheetMeta sheetMeta;

    public String sheetName;

    public String sheetID;

    public DataMeta dataMeta;

    public boolean isSaveRequired = false;
}
