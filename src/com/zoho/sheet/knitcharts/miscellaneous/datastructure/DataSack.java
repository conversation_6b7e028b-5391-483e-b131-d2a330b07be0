package com.zoho.sheet.knitcharts.miscellaneous.datastructure;

import com.zoho.sheet.knitcharts.utils.ChartUtils;

import java.util.EnumMap;
import java.util.Map;

public class DataSack {

    public enum Data {
        SERIES_COUNT,
        CHART_TYPE
    }

    private final Map<Data, Object> sack = new EnumMap<>(Data.class);

    public <T> T get(Data data) {
        return ChartUtils.typeCast(sack.get(data));
    }

    public void set(Data data, Object value) {
        sack.put(data, value);
    }

}
