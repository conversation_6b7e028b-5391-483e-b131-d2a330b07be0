package com.zoho.sheet.knitcharts.miscellaneous.datastructure;

import java.util.Map;

/**
 * Immutable map entry implementation of Map.Entry
 * @param <K> Key
 * @param <V> Value
 * @see java.util.Map.Entry
 */
public class ImmutableEntry<K, V> implements Map.Entry<K, V>{

    private final K key;

    private final V value;

    public ImmutableEntry(K key, V value) {
        this.key = key;
        this.value = value;
    }

    @Override
    public K getKey() {
        return key;
    }

    @Override
    public V getValue() {
        return value;
    }

    @Override
    public V setValue(V ignored) {
        return value;
    }
}
