package com.zoho.sheet.knitcharts.miscellaneous.datastructure;

/**
 * Tuple
 * @param <F> First Parameter
 * @param <S> Second Parameter
 * <AUTHOR>
 */
public class Tuple<F, S> {

    private F firstParam;

    private S secondParam;

    public Tuple(){}
    public Tuple(F firstParam, S secondParam){
        this.firstParam = firstParam;
        this.secondParam = secondParam;
    }

    public F getFirstParam() {
        return firstParam;
    }

    public S getSecondParam() {
        return secondParam;
    }

    public void setFirstParam(F firstParam) {
        this.firstParam = firstParam;
    }

    public void setSecondParam(S secondParam) {
        this.secondParam = secondParam;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof Tuple){
            Tuple<?, ?> otherTuple = (Tuple<?, ?>) obj;
            return this.firstParam.equals(otherTuple.firstParam) && this.secondParam.equals(otherTuple.secondParam);
        }else{
            return false;
        }
    }

    public static <F, S> Tuple<F, S> of(F firstParam, S secondParam){
        return new Tuple<>(firstParam, secondParam);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
