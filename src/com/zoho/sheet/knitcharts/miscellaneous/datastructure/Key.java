package com.zoho.sheet.knitcharts.miscellaneous.datastructure;

import com.zoho.sheet.knitcharts.utils.ChartUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * key
 * <AUTHOR>
 */
public class Key {

    private Key parent;

    private final int value;

    public Key(int value) {
        this.value = value;
    }

    private Key(Key parent, int value) {
        this.parent = parent;
        this.value = value;
    }

    public Key getChild(int value) {
        return new Key(this, value);
    }

    public int getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) { return true; }
        if (o == null || getClass() != o.getClass()) { return false; }
        Key key = (Key) o;
        return value == key.value && Objects.equals(parent, key.parent);
    }

    public Key getParent() {
        return parent;
    }

    @Override
    public int hashCode() {
        return Objects.hash(parent, value);
    }

    public static Key getKey(int value) {
        return new Key(value);
    }

    public static Key getKey(Key parent, int value) {
        return new Key(parent, value);
    }

    /**
     * Method to convert the string form key to object
     * @param stringFormKey example 1,0,1,3
     * @return Key instance
     */
    public static Key getKey(String stringFormKey) {
        String[] values = stringFormKey.split(",");         // NO I18N
        return _getKey(values, values.length - 1);
    }

    private static Key _getKey(String[] values, int index) {
        int value = Integer.parseInt(values[index]);
        if(index == 0) { return getKey(value); }
        return getKey(_getKey(values, index - 1), value);
    }

    /**
     * Method to get the string form of this key       example 0,2
     * @return String format of this key
     */
    @Override
    public String toString() {
        List<String> buffer = new ArrayList<>();
        _toString(buffer);

        return ChartUtils.join(buffer, ",");            // NO I18N
    }

    private void _toString(List<String> buffer) {
        if(parent != null) { parent._toString(buffer); }
        buffer.add(String.valueOf(value));
    }
}
