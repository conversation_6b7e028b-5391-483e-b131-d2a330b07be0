package com.zoho.sheet.knitcharts.miscellaneous.datatype;

import com.zoho.sheet.knitcharts.utils.ChartUtils;

/**
 * Percent representation of an integer based on the given total value. <br/>
 * Note: By default if the total value is not given it will be taken as 100 <br/>
 * Example1: integer 90 will be represented as 90% for 100 as total value.
 * Example2: integer 60 will be represented as 120% for 50 as total value
 */
public class Percent implements ChartsDataType{

    /* percent value*/
    private int value;

    /**
     * Creates a percent instance with 100 as total value
     * @param value percent value
     */
    public Percent(int value) {
        this.value = value;
    }

    /**
     * Creates a percent instance with the given total value and percent value
     * @param value Percent value
     * @param total total value
     */
    public Percent(int value, int total) {
        this.value = (value/total) * 100;
    }

    /**
     * getter method for percent value
     * @return percent value
     */
    public int getValue() {
        return value;
    }

    /**
     * Formats the percent value in Percentage representation
     * @return
     */
    @Override
    public String toString() {
        return String.format("%d%%", value);            // NO I18N
    }

    @Override
    protected Percent clone() throws CloneNotSupportedException {
        Percent percent = ChartUtils.typeCast(super.clone());

        percent.value = value;
        return percent;
    }

    @Override
    public Object getJavaType() {
        return value;
    }

    public static Percent fromString(String str) {
        if(str.length() > 1) {
            return new Percent(ChartUtils.requireNonNullElse(ChartUtils.toInteger(str.substring(0, str.length()-1)), 0));
        }
        return new Percent(0);
    }
}
