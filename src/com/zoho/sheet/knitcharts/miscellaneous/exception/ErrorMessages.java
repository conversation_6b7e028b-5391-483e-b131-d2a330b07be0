package com.zoho.sheet.knitcharts.miscellaneous.exception;

/**
 * Contains all the Exception messages
 *
 * <AUTHOR>
 */
public final class ErrorMessages {

    public static final String INVALID_Y_AXIS_INDEX = "[CHARTS]: Invalid Y-Axis Index";                                               // NO I18N

    public static final String INVALID_SUB_ACTION_CONSTANT = "[CHARTS]: Invalid Sub Action Constant";                                 // NO I18N

    public static final String INVALID_LOG_BASE_VALUE = "[CHARTS]: The Base value can't be less than 2";                              // NO I18N

    public static final String INVALID_TYPE_VALUE = "[CHARTS]: Invalid value passed for Type look up";                                // NO I18N

    public static final String INVALID_PLOTLINE_INDEX = "[CHARTS]: Invalid plot line index";                                          // NO I18N

    public static final String INVALID_CHART_ACTION = "[CHARTS]: Invalid Action Constant given";                                      // NO I18N

    public static final String UNKNOWN_CHART_TYPE = "[CHARTS]: Unknown chart type from the data base";                                // NO I18N

    public static final String CANT_SET_CUSTOM_VALUE = "[CHARTS]: Can't set Custom value for non custom Y-Axis unit format type";     // NO I18N

    public static final String BOTH_Y_AXIS_INDEX_AND_COUNT_EMPTY = "[CHARTS]: Both Y-Axis index and Y-Axis count can't be empty";     // NO I18N

    public static final String BOTH_SERIES_INDEX_AND_COUNT_EMPTY = "[CHARTS]: Both Series index and Series count can't be empty";     // NO I18N

    public static final String UNABLE_TO_FIND_GIVEN_CHART = "[CHARTS]: Unable to find the chart with given ID and given sheet";       // NO I18N

    public static final String INVALID_DATA_SOURCE = "[CHARTS]: Invalid data source given.";                                          // NO I18N

    public static final String INVALID_CHART_ID = "[CHARTS]: Invalid Chart ID given.";                                                // NO I18N

    public static final String DATA_SOURCE_REQUIRED = "[CHARTS]: Data source required for table construction.";                       // NO I18N

    public static final String INVALID_FILTER_OPTIONS = "[CHARTS]: All the filter options were null";                                 // NO I18N

    public static final String INVALID_HEADER_INDEX = "[CHARTS]: Invalid header index given.";                                        // NO I18N

    public static final String DATA_PROPERTY_NOT_SUPPORTED = "[CHARTS]: Data Properties not applicable to Shared Series property";    // NO I18N

    public static final String CHART_RECOMMENDATION_FAILED = "[CHARTS]: Chart Recommendation Caching failed.";                        // NO I18N

    public static final String UNABLE_TO_READ_CHART_FROM_DB = "[NEW_CHARTS][CHART_DB]: Unable to read chart from DB";                   // NO I18N

    public static final String UNABLE_TO_ADD_CHART_TO_CONTAINER = "[NEW_CHARTS][CHART_CONTAINER]: Unable to add chart to Chart Container";  // NO I18N

    public static final String UNABLE_GENERATE_CHART_RESPONSE = "[NEW_CHARTS][CHART_META]: Unable to generate chart meta."; // NO I18N

    public static final String INVALID_TEXT_REFERENCE = "[CHARTS]: Invalid Text Reference";                                             // NO I18N

    public static final String INVALID_REFERENCE = "[CHARTS]: Invalid reference";                                           // NO I18N

}
