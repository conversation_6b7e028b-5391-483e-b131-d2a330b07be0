package com.zoho.sheet.knitcharts.metaconstructor.sheetmeta;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chartsdatatable.*;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.DataType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.DataTableUtils;
import com.zoho.sheet.knitcharts.utils.DataUtils;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.List;

public class StandardSheetMetaConstructor implements SheetMetaConstructor{

    @Override
    public SheetMeta constructSheetMeta(Sheet activeSheet, JSONObjectWrapper chartJSON, DataTable dataTable, ChartType chartType) {
        SheetMeta sheetMeta = new SheetMeta();
        Workbook workbook = activeSheet.getWorkbook();
        List<Reference> references = dataTable.getReferences();

        sheetMeta.fromJSON(ChartUtils.optFromJSONObject(chartJSON, ChartActionConstants.JSONConstants.SHEET_META));
        updateDataJoinType(workbook, sheetMeta,references);
        updateSheetMetaOptions(workbook, sheetMeta, references, chartType);
        SheetChartAPI.updateDataSources(sheetMeta, dataTable.getDataSources());
        chartJSON.put(ChartActionConstants.JSONConstants.SHEET_META, sheetMeta.toJSON());

        return sheetMeta;
    }

    private static void updateDataJoinType(Workbook workbook, SheetMeta sheetMeta, List<Reference> dataSources) {
        if(dataSources.size() > 1) {
            SheetChartAPI.updateDataJoinType(sheetMeta, DataTableUtils.determineDataJoinType(workbook, dataSources));
        }
    }

    /**
     * Method used to determine necessary options from insert chart flow not from recommendation
     */
    private static void updateSheetMetaOptions(Workbook workbook, SheetMeta sheetMeta, List<Reference> references, ChartType chartType) {
        Matrix matrix = Matrix.getInstance(workbook, sheetMeta, chartType, references, true);

        List<List<DataTableCell>> originalData = matrix.getData();

        if(originalData.isEmpty()) { return; }

        SeriesInType seriesInType = DataUtils.getColSize(originalData) >= DataUtils.getRowSize(originalData) ?
                SeriesInType.COLUMNS : SeriesInType.ROWS;
        SheetChartAPI.updateSeriesIn(sheetMeta, seriesInType);

        if(originalData.get(0).size() > 1) {
            DataType fcType = DataUtils.getColumnDataType(originalData, 0);
            SheetChartAPI.updateFirstColAsLabelStatus(sheetMeta, DataUtils.chartTypeCanHaveLabel(chartType, fcType));
        }

        if(originalData.size() > 1) {
            DataType frType = DataUtils.getRowDataType(originalData, 0);
            SheetChartAPI.updateFirstRowAsLabelStatus(sheetMeta, DataUtils.chartTypeCanHaveLabel(chartType, frType));
        }
    }
}
