package com.zoho.sheet.knitcharts.metaconstructor.datameta;

import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.datameta.DataMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTableCell;
import com.zoho.sheet.knitcharts.utils.DataUtils;
import com.zoho.sheet.knitcharts.utils.FrameworkChartAPIUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class GroupedCatDataMetaConstructor extends StandardDataMetaConstructor {

    @Override
    protected void analyseDataAndUpdate(DataMeta dataMeta, SheetMeta sheetMeta, List<List<DataTableCell>> tableCells) {
        super.analyseDataAndUpdate(dataMeta, sheetMeta, tableCells);
        updateGroupedCategories(sheetMeta, dataMeta);
    }

    private static void updateGroupedCategories(SheetMeta sheetMeta, DataMeta dataMeta) {
        List<Integer> nonNumericalSeries = new ArrayList<>();
        AtomicBoolean interrupted = new AtomicBoolean(false);
        SeriesInType seriesInType = SheetChartAPI.getSeriesIn(sheetMeta);

        if((SheetChartAPI.getFirstColLabel(sheetMeta) && seriesInType == SeriesInType.COLUMNS)
                || (SheetChartAPI.getFirstRowLabel(sheetMeta) && seriesInType == SeriesInType.ROWS)) {
            nonNumericalSeries.add(0);
            FrameworkChartAPIUtils.forEachSeriesType(dataMeta, seriesInType, (index, type) -> {
                if (DataUtils.doesDataTypeSupportedForGroupedCategories(type) && !interrupted.get()) {
                    nonNumericalSeries.add(index);
                } else {
                    interrupted.set(true);
                }
            });

            if (nonNumericalSeries.size() > 1 && interrupted.get()) {
                if (seriesInType == SeriesInType.COLUMNS) {
                    FrameworkChartAPI.updateGroupedColumnHeadersEnabled(dataMeta, true);
                    FrameworkChartAPI.updateGroupedColumnHeadersIndices(dataMeta, nonNumericalSeries);
                } else {
                    FrameworkChartAPI.updateGroupedRowHeadersEnabled(dataMeta, true);
                    FrameworkChartAPI.updateGroupedRowHeadersIndices(dataMeta, nonNumericalSeries);
                }
            }
        }
    }
}
