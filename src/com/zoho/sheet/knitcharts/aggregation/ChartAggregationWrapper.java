package com.zoho.sheet.knitcharts.aggregation;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants;
import com.zoho.sheet.Aggregation.AggregationEngine;
import com.zoho.sheet.Aggregation.ChartUtility;
import com.zoho.sheet.Aggregation.model.FilterCriteria;
import com.zoho.sheet.Aggregation.model.GroupingColumn;
import com.zoho.sheet.Aggregation.model.ValueColumn;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.AggregationType;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTableCell;
import com.zoho.sheet.knitcharts.chartsdatatable.EmptyTableCell;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionProducer;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.DataUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class ChartAggregationWrapper {

    private final Workbook workbook;

    private final List<List<DataTableCell>> tableCells;

    private final SheetMeta sheetMeta;

    public ChartAggregationWrapper(Workbook workbook, SheetMeta sheetMeta, List<List<DataTableCell>> tableCells){
        this.workbook = workbook;
        this.sheetMeta = sheetMeta;
        this.tableCells = tableCells;
    }

    public List<List<DataTableCell>> execute(){
        if(validateOptions()){
            return applyAggregation();
        }
        return new ArrayList<>();
    }

    private List<List<DataTableCell>> applyAggregation(){
        AggregationEngine engine = prepareEngine();
        List<DataTableCell> sampleCells = getSampleCells();
        return transformData(engine.executeEngine(workbook).collectAsValue(), sampleCells);
    }

    private AggregationEngine prepareEngine(){
        AggregationEngine engine = new AggregationEngine();
        List<ZArrayI> tableRange = new ArrayList<>();
        FunctionProducer<Integer, ZArrayI> seriesGetter = getZArraySeriesGetter();

        if(SheetChartAPI.isAggregationPossible(sheetMeta)){
            tableRange.addAll(prepareEngineForAggregation(engine, seriesGetter));
        }

        if(SheetChartAPI.isFilterPossible(sheetMeta)){
            List<ZArrayI> filterCols = prepareEngineForFilter(engine, seriesGetter);
            /*
                IF FILTER ALONE IS ENABLED, THEN TABLERANGE WILL BE EMPTY SO WE HAVE TO ADD A COLUMN IN TABLE
                OTHERWISE THERE IS NO NEED TO ADD A COLUMN INTO THE TABLE RANGE
             */
            if(tableRange.isEmpty()) { tableRange.addAll(filterCols); }
        }

        engine.setTableRange(new StackedZArray(tableRange, StackedZArray.Type.H_STACK));
        return engine;
    }

    private List<ZArrayI> prepareEngineForAggregation(AggregationEngine engine, FunctionProducer<Integer, ZArrayI> seriesGetter){
        Integer axisColumn = SheetChartAPI.getAxisColumn(sheetMeta);
        Integer legendColumn = SheetChartAPI.getLegendColumn(sheetMeta);
        List<Integer> valueColumns = SheetChartAPI.getAggregationValueColumns(sheetMeta);
        List<ZArrayI> tableRange = new ArrayList<>();

        if(axisColumn != null){
            ZArrayI col = seriesGetter.produce(axisColumn);

            tableRange.add(col);
            engine.withAxisColumn(new GroupingColumn(col, SheetChartAPI.getAxisGroupingType(sheetMeta).getEngineConstant()));
        }

        if(legendColumn != null){
            ZArrayI col = seriesGetter.produce(legendColumn);

            tableRange.add(col);
            engine.withLegendCol(new GroupingColumn(col, SheetChartAPI.getLegendGroupingType(sheetMeta).getEngineConstant()));
        }

        if(SheetChartAPI.isFilterPossible(sheetMeta)){                          // If filter is possible the adding only that filter column to the data
            Integer filterColumn = SheetChartAPI.getFilterColumn(sheetMeta);
            ZArrayI col = seriesGetter.produce(filterColumn);

            tableRange.add(col);
            engine.withValueColumn(new ValueColumn(col));
            engine.withValueOperation(SheetChartAPI.getAggregationType(sheetMeta).getEngineConstant());
        } else {
            for (Integer valueColumn : valueColumns) {
                ZArrayI col = seriesGetter.produce(valueColumn);

                tableRange.add(col);
                engine.withValueColumn(new ValueColumn(col));
                engine.withValueOperation(SheetChartAPI.getAggregationType(sheetMeta).getEngineConstant());
            }
        }

        return tableRange;
    }

    private List<ZArrayI> prepareEngineForFilter(AggregationEngine engine, FunctionProducer<Integer, ZArrayI> seriesGetter){
        List<ZArrayI> table = new ArrayList<>();
        Integer filterColumn = SheetChartAPI.getFilterColumn(sheetMeta);
        ZArrayI col = seriesGetter.produce(filterColumn);
        engine.withFilter(
                new FilterCriteria(new ValueColumn(col), SheetChartAPI.getFilterType(sheetMeta).getEngineConstant(), SheetChartAPI.getFilterCount(sheetMeta))
        );

        if(SheetChartAPI.getFirstSeriesLabel(sheetMeta)) { table.add(seriesGetter.produce(0)); }
        table.add(col);

        return table;
    }

    private List<DataTableCell> getSampleCells(){
        List<DataTableCell> sampleCells = new ArrayList<>();
        FunctionProducer<Integer, DataTableCell> seriesGetter = SheetChartAPI.getSeriesIn(sheetMeta) == SeriesInType.COLUMNS ? this::getColumnSample : this::getRowSample;

        if(SheetChartAPI.isAggregationPossible(sheetMeta)){
            sampleCells.addAll(getSampleCellsForAggregation(seriesGetter));
        }

        if(sampleCells.isEmpty() && SheetChartAPI.isFilterPossible(sheetMeta)){
            sampleCells.addAll(getCellsForFilter(seriesGetter));
        }

        return sampleCells;
    }


    private List<DataTableCell> getHeaderCells(){
        List<DataTableCell> headerCells = new ArrayList<>();
        FunctionProducer<Integer, DataTableCell> seriesGetter;
        if(SheetChartAPI.getSeriesIn(sheetMeta) == SeriesInType.COLUMNS){
            if(!SheetChartAPI.getFirstRowLabel(sheetMeta)) { return headerCells; }
            seriesGetter = this::getColumnHeader;
        } else {
          if(!SheetChartAPI.getFirstColLabel(sheetMeta)) { return headerCells; }
          seriesGetter = this::getRowHeader;
        }

        if(SheetChartAPI.isAggregationPossible(sheetMeta)){
            headerCells.addAll(getHeaderCellsForAggregation(seriesGetter));
        }

        if(headerCells.isEmpty() && SheetChartAPI.isFilterPossible(sheetMeta)){
            headerCells.addAll(getCellsForFilter(seriesGetter));
        }

        return headerCells;
    }

    private List<DataTableCell> getHeaderCellsForAggregation(FunctionProducer<Integer, DataTableCell> producer) {
        List<DataTableCell> headerCells = new ArrayList<>();
        Integer axisColumn = SheetChartAPI.getAxisColumn(sheetMeta);
        Integer legendColumn = SheetChartAPI.getLegendColumn(sheetMeta);
        List<Integer> valueColumns = SheetChartAPI.getAggregationValueColumns(sheetMeta);

        if(axisColumn != null){
            headerCells.add(producer.produce(axisColumn));
        }

        if(legendColumn != null){
            headerCells.add(producer.produce(legendColumn));
        }

        if(SheetChartAPI.isFilterPossible(sheetMeta)) {                             // If filter possible only taking filter data
            Integer filterColumn = SheetChartAPI.getFilterColumn(sheetMeta);

            headerCells.add(producer.produce(filterColumn));
        } else {
            for (Integer valueColumn : valueColumns) {
                headerCells.add(producer.produce(valueColumn));
            }
        }

        return headerCells;
    }

    private List<DataTableCell> getSampleCellsForAggregation(FunctionProducer<Integer, DataTableCell> producer) {
        List<DataTableCell> headerCells = new ArrayList<>();
        Integer axisColumn = SheetChartAPI.getAxisColumn(sheetMeta);
        Integer legendColumn = SheetChartAPI.getLegendColumn(sheetMeta);
        List<Integer> valueColumns = SheetChartAPI.getAggregationValueColumns(sheetMeta);

        if(axisColumn != null){
            headerCells.add(producer.produce(axisColumn));
        }

        if(legendColumn != null){
            headerCells.add(producer.produce(legendColumn));
        }

        if(SheetChartAPI.isFilterPossible(sheetMeta)) {                             // If filter possible only taking filter data
            Integer filterColumn = SheetChartAPI.getFilterColumn(sheetMeta);

            headerCells.add(producer.produce(filterColumn));
        } else {
            AggregationType aggregationType = SheetChartAPI.getAggregationType(sheetMeta);
            if(aggregationType == AggregationType.COUNT || aggregationType == AggregationType.COUNT_DISTINCT){      // since count and count distinct always returns numeric values sample cell also given as numeric cell
                DataTableCell numericCell = DataUtils.getNumericSampleCell(workbook.getSpreadsheetSettings());
                for (Integer ignored : valueColumns) { headerCells.add(numericCell); }
            } else {
                for (Integer valueColumn : valueColumns) {
                    headerCells.add(producer.produce(valueColumn));
                }
            }
        }

        return headerCells;
    }

    private List<DataTableCell> getCellsForFilter(FunctionProducer<Integer, DataTableCell> producer) {
        List<DataTableCell> cells = new ArrayList<>();
        Integer filterColumn = SheetChartAPI.getFilterColumn(sheetMeta);

        if(SheetChartAPI.getFirstSeriesLabel(sheetMeta)) { cells.add(producer.produce(0)); }
        cells.add(producer.produce(filterColumn));
        return cells;
    }

    private List<List<DataTableCell>> transformData(List<List<Value>> aggResult, List<DataTableCell> sampleCells){
        Integer axisColumn = SheetChartAPI.getAxisColumn(sheetMeta);
        Integer legendColumn = SheetChartAPI.getLegendColumn(sheetMeta);
        List<List<DataTableCell>> transformedResult;

        if(SheetChartAPI.isAggregationPossible(sheetMeta)) {
            if (axisColumn != null && legendColumn != null) {
                transformedResult = applySparseTransformation(aggResult, sampleCells);
            } else if (legendColumn != null) {
                transformedResult = applyTransposeTransformation(aggResult, sampleCells, getHeaderCells());
            } else if (axisColumn != null) {
                transformedResult = applyDefaultTransformation(aggResult, sampleCells, getHeaderCells());
            } else {
                transformedResult = applyTransformationForNoGrouping(workbook, aggResult, sampleCells, getHeaderCells(), sheetMeta);
            }
        } else {
            transformedResult = applyTransformationForNoGrouping(workbook, aggResult, sampleCells, getHeaderCells(), sheetMeta);
        }
        return transformedResult;
    }

    private static List<List<DataTableCell>> applyTransformationForNoGrouping(Workbook workbook, List<List<Value>> aggResult,
                                                                              List<DataTableCell> sampleCells, List<DataTableCell> headerCells, SheetMeta sheetMeta) {
        SpreadsheetSettings spreadsheetSettings = workbook.getSpreadsheetSettings();
        List<List<DataTableCell>> result = aggResult.stream()
                .map(row -> IntStream.range(0, row.size())
                        .mapToObj(colIdx -> createNewDataTableCell(sampleCells.get(colIdx), row.get(colIdx)))
                        .collect(Collectors.toList())).collect(Collectors.toList());

        if(headerCells.isEmpty()) {
            int size = result.get(0).size();

            for(int idx = 1; idx <= size; idx++) {
                String header = String.format("%s %d", "Series", idx);                          // NO I18N
                DataTableCell headerCell = new DataTableCell(new ValueI(Cell.Type.STRING, getAggregationMessage(sheetMeta, header)),
                        Cell.Type.STRING, DataStyleConstants.EMPTY_PATTERN, spreadsheetSettings.getLocale(), spreadsheetSettings);
                headerCells.add(headerCell);
            }
        } else {
            List<DataTableCell> newHeaders = new ArrayList<>();
            for(DataTableCell headerCell: headerCells) {
                DataTableCell newHeaderCell = new DataTableCell(new ValueI(Cell.Type.STRING, getAggregationMessage(sheetMeta, headerCell.getValueObjectForHeader(workbook))),
                        Cell.Type.STRING, DataStyleConstants.EMPTY_PATTERN, spreadsheetSettings.getLocale(), spreadsheetSettings);
                newHeaders.add(newHeaderCell);
            }
            headerCells.clear();
            headerCells.addAll(newHeaders);
        }

        result.add(0, headerCells);
        return result;
    }

    private static String getAggregationMessage(SheetMeta sheetMeta, String header) {
        if(SheetChartAPI.isAggregationPossible(sheetMeta)) {
            return ChartUtils.getAggregationMessage(sheetMeta, header);
        }
        return header;
    }

    private static List<List<DataTableCell>> applyDefaultTransformation(List<List<Value>> aggResult,
                                                                        List<DataTableCell> sampleCells, List<DataTableCell> headerCells){
        List<List<DataTableCell>> result = aggResult.stream()
                .map(row -> IntStream.range(0, row.size())
                        .mapToObj(colIdx -> createNewDataTableCell(sampleCells.get(colIdx), row.get(colIdx)))
                        .collect(Collectors.toList())).collect(Collectors.toList());

        if(!headerCells.isEmpty()) { result.add(0, headerCells); }
        return result;
    }

    private static List<List<DataTableCell>> applyTransposeTransformation(List<List<Value>> aggResult,
                                                                          List<DataTableCell> sampleCells, List<DataTableCell> headers){
        List<List<Value>> tAggResult = ChartUtility.transposeData(aggResult);
        List<List<DataTableCell>> resultWithoutLabels = IntStream.range(0, tAggResult.size()).mapToObj(rowIdx -> {
            List<Value> row = tAggResult.get(rowIdx);
            return row.stream().map(value -> createNewDataTableCell(sampleCells.get(rowIdx), value))
                    .collect(Collectors.toList());
        }).collect(Collectors.toList());
        /* if there is no header cell, then we can skip adding headers */
        if(headers.isEmpty()) { return resultWithoutLabels; }

        return IntStream.range(0, resultWithoutLabels.size()).mapToObj(rowIdx -> {
            List<DataTableCell> row = resultWithoutLabels.get(rowIdx);
            List<DataTableCell> newRow = new ArrayList<>();
            newRow.add(headers.get(rowIdx));
            newRow.addAll(row);
            return newRow;
        }).collect(Collectors.toList());

    }

    private static List<List<DataTableCell>> applySparseTransformation(List<List<Value>> aggResult,
                                                                       List<DataTableCell> sampleCells){
        List<List<Value>> tAggResult = ChartUtility.changeToSparseMatrix(aggResult);

        return IntStream.range(0, tAggResult.size()).mapToObj(rowIdx -> {
            List<Value> row = tAggResult.get(rowIdx);
            return IntStream.range(0, row.size()).mapToObj(colIdx -> {
                if(rowIdx == 0 && colIdx == 0) { return new EmptyTableCell(); }
                else if(rowIdx == 0) { return createNewDataTableCell(sampleCells.get(1), row.get(colIdx)); }
                else if(colIdx == 0) { return createNewDataTableCell(sampleCells.get(0), row.get(colIdx)); }
                else { return createNewDataTableCell(sampleCells.get(2), row.get(colIdx)); }
            }).collect(Collectors.toList());
        }).collect(Collectors.toList());
    }

    private ZArrayI getColumnAsZArray(int colIndex){
        return DataUtils.getColAsZArray(tableCells, colIndex);
    }

    private ZArrayI getRowAsZArray(int rowIndex){
        return DataUtils.getRowAsZArray(tableCells, rowIndex);
    }

    private ZArrayI getRowAsZArrayWithoutHeader(int rowIndex){
        return DataUtils.getRowAsZArrayWithoutHeader(tableCells, rowIndex);
    }

    private ZArrayI getColAsZArrayWithoutHeader(int colIndex){
        return DataUtils.getColAsZArrayWithoutHeader(tableCells, colIndex);
    }

    private DataTableCell getColumnHeader(int colIndex){
        return tableCells.get(0).get(colIndex);
    }

    private DataTableCell getRowHeader(int rowIndex){
        return tableCells.get(rowIndex).get(0);
    }

    private DataTableCell getColumnSample(int colIndex){
        if(SheetChartAPI.getFirstRowLabel(sheetMeta)){
            List<DataTableCell> row = tableCells.get(1);
            return row.size() > colIndex ? row.get(colIndex) : new EmptyTableCell();
        }
        List<DataTableCell> row = tableCells.get(0);
        return row.size() > colIndex ? row.get(colIndex) : new EmptyTableCell();
    }

    private DataTableCell getRowSample(int rowIndex){
        List<DataTableCell> row = tableCells.get(rowIndex);
        if(SheetChartAPI.getFirstColLabel(sheetMeta)){
            return row.size() > 1 ? row.get(1) : new EmptyTableCell();
        }
        return row.isEmpty() ? new EmptyTableCell() : tableCells.get(rowIndex).get(0);
    }

    private FunctionProducer<Integer, ZArrayI> getZArraySeriesGetter(){
        SeriesInType seriesInType = SheetChartAPI.getSeriesIn(sheetMeta);

        if(seriesInType == SeriesInType.COLUMNS){
            if(SheetChartAPI.getFirstRowLabel(sheetMeta)) { return this::getColAsZArrayWithoutHeader; }
            return this::getColumnAsZArray;
        } else {
            if(SheetChartAPI.getFirstColLabel(sheetMeta)) { return this::getRowAsZArrayWithoutHeader; }
            return this::getRowAsZArray;
        }
    }

    private static DataTableCell createNewDataTableCell(DataTableCell sampleCells, Value value){
        if(value == null) { return new EmptyTableCell(); }
        else{
            return new DataTableCell(sampleCells, DataUtils.getRoundedValue(sampleCells, value));            // rounding the values to 2 digit floating point to match old behaviour & percentage values should not round off
        }
    }

    private boolean validateOptions(){
        boolean valid = true;
        filterOutInvalidValueColumns();

        /* aggregation value columns and filter value column check */
        if(SheetChartAPI.isAggregationPossible(sheetMeta) && SheetChartAPI.isFilterPossible(sheetMeta)) {
            valid = SheetChartAPI.getAggregationValueColumns(sheetMeta).contains(SheetChartAPI.getFilterColumn(sheetMeta));
            valid = valid && (Objects.nonNull(SheetChartAPI.getAxisColumn(sheetMeta)) && Objects.isNull(SheetChartAPI.getLegendColumn(sheetMeta)));
        }


        return valid;
    }

    private void filterOutInvalidValueColumns() {
        SeriesInType seriesInType = SheetChartAPI.getSeriesIn(sheetMeta);
        int seriesCount = seriesInType == SeriesInType.COLUMNS ? DataUtils.getRowSize(tableCells) : DataUtils.getColSize(tableCells);
        /* filtering out invalid aggregation columns */
        if(SheetChartAPI.isAggregationPossible(sheetMeta)) {
            List<Integer> valueColumns = SheetChartAPI.getAggregationValueColumns(sheetMeta);
            Integer axisColumn = SheetChartAPI.getAxisColumn(sheetMeta);
            Integer legendColumn = SheetChartAPI.getLegendColumn(sheetMeta);

            valueColumns = valueColumns.stream()
                    .filter(valueColumn -> valueColumn < seriesCount)
                    .collect(Collectors.toList());
            if(axisColumn != null && axisColumn >= seriesCount) { axisColumn = null; }
            if(legendColumn != null && legendColumn >= seriesCount) { legendColumn = null; }

            SheetChartAPI.setAxisColumn(sheetMeta, axisColumn);
            SheetChartAPI.setLegendColumn(sheetMeta, legendColumn);
            SheetChartAPI.setAggregationValueColumns(sheetMeta, valueColumns);
        }

        /* filtering out invalid filter column */
        if(SheetChartAPI.isFilterPossible(sheetMeta)) {
            Integer filterColumn = SheetChartAPI.getFilterColumn(sheetMeta);

            if(filterColumn != null && filterColumn >= seriesCount) { filterColumn = null; }

            SheetChartAPI.setFilterColumn(sheetMeta, filterColumn);
        }
    }
}
