package com.zoho.sheet.knitcharts.backwardconverter.decomissioned;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.utils.ChartsBCUtils;

/**
 * Charts Backward converter manager
 * <AUTHOR>
 */
public class ChartsBCManager {

    /**
     * Method to convert the given new chart data into old chart
     * @param workbook Workbook instance
     * @param chartID ID of the chart to be converted
     * @param chartMeta Chart Meta instance
     * @param sheetMeta Sheet Meta instance
     * @param asn Associated sheet name where the chart resides
     * @return Old chart instance
     */
    public Chart convert(Workbook workbook, String chartID, ChartMeta chartMeta, SheetMeta sheetMeta, String asn) {
        JSONObjectWrapper chartMetaInfo = new JSONObjectWrapper();
        Converter converter = ChartsBCUtils.getConverter(workbook);
        boolean isPivotChart = SheetChartAPI.isPivotChart(sheetMeta);
        String pivotID = ChartsBCUtils.getPivotID(workbook, sheetMeta);

        converter.constructSkeleton(chartMetaInfo);
        Chart chart = ChartsBCUtils.getChart(workbook, chartID, chartMeta, sheetMeta, asn, isPivotChart, pivotID, chartMetaInfo);

        chart.constructChartOptions(workbook.getSheetByAssociatedName(asn), new JSONObjectWrapper());
        chart.checkAndCreateChartPretiffyObject(workbook).reset(chart, workbook);
        converter.convert(chart, chartMeta, sheetMeta);

        return chart;
    }

}
