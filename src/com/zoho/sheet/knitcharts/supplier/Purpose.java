package com.zoho.sheet.knitcharts.supplier;

public class Purpose {

    public enum SheetMetaPurpose {
        STD_SHEET_META,
        PIVOT_SHEET_META,
        PASTE_CHART_SHEET_META,
    }

    public enum DataMetaPurpose {
        GROUPED_CAT_DATA_META,
        INFO_TEXT_DATA_META,
        SCATTER_DATA_META,
        STD_DATA_META,
        PIVOT_DATA_META,
        MANIPULATED_DATA_META
    }

    public enum ChartMetaPurpose {
        STD_CHART_META,
        PIVOT_CHART_META
    }

    public enum PresetStylesPurpose {
        CHART_AREA_BACKGROUND,
        CHART_AREA_SHADOW,
        SERIES_COLOR,
        DATA_PROPERTY_COLOR,
        SERIES_UP_COLOR,
        SERIES_DOWN_COLOR,
        SERIES_TOTAL_COLOR,
        SHARED_SERIES_SHADOW,
        SERIES_SHADOW
    }

}
