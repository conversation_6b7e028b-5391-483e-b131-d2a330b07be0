package com.zoho.sheet.knitcharts.supplier;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.metaconstructor.datameta.*;
import com.zoho.sheet.knitcharts.presets.PresetParams;
import com.zoho.sheet.knitcharts.presets.series.color.SeriesUpColorPreset;
import com.zoho.sheet.knitcharts.presets.series.shadow.SeriesShadowPreset;
import com.zoho.sheet.knitcharts.presets.series.shadow.SharedSeriesShadowPreset;
import com.zoho.sheet.knitcharts.utils.ChartActivityMonitor;
import com.zoho.sheet.knitcharts.api.ChartsAPIEndpoint;
import com.zoho.sheet.knitcharts.responsegenerator.gridaction.frameworkchart.FrameworkChartResponseGeneratorMap;
import com.zoho.sheet.knitcharts.metaconstructor.chartmeta.ChartMetaConstructor;
import com.zoho.sheet.knitcharts.metaconstructor.chartmeta.PivotChartMetaConstructor;
import com.zoho.sheet.knitcharts.metaconstructor.chartmeta.StandardChartMetaConstructor;
import com.zoho.sheet.knitcharts.metaconstructor.sheetmeta.PasteSheetMetaConstructor;
import com.zoho.sheet.knitcharts.metaconstructor.sheetmeta.PivotSheetMetaConstructor;
import com.zoho.sheet.knitcharts.metaconstructor.sheetmeta.SheetMetaConstructor;
import com.zoho.sheet.knitcharts.metaconstructor.sheetmeta.StandardSheetMetaConstructor;
import com.zoho.sheet.knitcharts.presets.DelegatingPreset;
import com.zoho.sheet.knitcharts.presets.EmptyPreset;
import com.zoho.sheet.knitcharts.presets.chartarea.background.ChartAreaBackgroundPreset;
import com.zoho.sheet.knitcharts.presets.chartarea.shadow.ChartAreaShadowPreset;
import com.zoho.sheet.knitcharts.responsegenerator.gridaction.sheetchart.SheetChartResponseGeneratorMap;

import java.lang.ref.SoftReference;

/**
 * Instance supplier for common classes.
 * <AUTHOR>
 */
public final class ObjectsSupplier {

    private static final ObjectsSupplier INSTANCE = new ObjectsSupplier();

    private SoftReference<SheetChartResponseGeneratorMap> sheetChartResponseGeneratorMap;

    private SoftReference<ChartActivityMonitor> chartActivityMonitor;

    private SoftReference<FrameworkChartResponseGeneratorMap> frameworkChartResponseGeneratorMap;

    private ObjectsSupplier() {}

    public synchronized SheetChartResponseGeneratorMap getSheetChartResponseGeneratorMap() {

        if(sheetChartResponseGeneratorMap == null || sheetChartResponseGeneratorMap.get() == null) {
            sheetChartResponseGeneratorMap = new SoftReference<>(new SheetChartResponseGeneratorMap());
        }

        return sheetChartResponseGeneratorMap.get();
    }

    public synchronized ChartActivityMonitor getChartActivityMonitor() {
        if(chartActivityMonitor == null || chartActivityMonitor.get() == null) {
            chartActivityMonitor = new SoftReference<>(new ChartActivityMonitor());
        }

        return chartActivityMonitor.get();
    }

    public synchronized ChartsAPIEndpoint getChartsAPIEndpoint(Workbook workbook, String documentOwner, long docID) {
        return new ChartsAPIEndpoint(workbook, documentOwner, docID);
    }

    public synchronized FrameworkChartResponseGeneratorMap getFrameworkChartResponseGeneratorMap() {
        if(frameworkChartResponseGeneratorMap == null || frameworkChartResponseGeneratorMap.get() == null) {
            frameworkChartResponseGeneratorMap = new SoftReference<>(new FrameworkChartResponseGeneratorMap());
        }

        return frameworkChartResponseGeneratorMap.get();
    }

    public ChartMetaConstructor getChartMetaConstructor(Purpose.ChartMetaPurpose purpose) {
        if(purpose == Purpose.ChartMetaPurpose.PIVOT_CHART_META) {
            return new PivotChartMetaConstructor();
        }
        return new StandardChartMetaConstructor();
    }

    public DataMetaConstructor getDataMetaConstructor(Purpose.DataMetaPurpose purpose) {
        switch (purpose) {
            case PIVOT_DATA_META: { return new PivotDataMetaConstructor(); }
            case MANIPULATED_DATA_META: { return new ManipulatedDataMetaConstructor(); }
            case SCATTER_DATA_META: { return new ScatterDataMetaConstructor(); }
            case INFO_TEXT_DATA_META: { return new InfoTextDataMetaConstructor(); }
            case GROUPED_CAT_DATA_META: { return new GroupedCatDataMetaConstructor(); }
            default: { return new StandardDataMetaConstructor(); }
        }
    }

    public SheetMetaConstructor getSheetMetaConstructor(Purpose.SheetMetaPurpose purpose) {
        if(purpose == Purpose.SheetMetaPurpose.PASTE_CHART_SHEET_META) {
            return new PasteSheetMetaConstructor();
        } else if(purpose == Purpose.SheetMetaPurpose.PIVOT_SHEET_META) {
            return new PivotSheetMetaConstructor();
        }
        return new StandardSheetMetaConstructor();
    }

    public DelegatingPreset getPresetStyles(Purpose.PresetStylesPurpose purpose, PresetParams params) {
        switch (purpose) {
            case CHART_AREA_BACKGROUND: return new DelegatingPreset(new ChartAreaBackgroundPreset(params));
            case CHART_AREA_SHADOW: return new DelegatingPreset(new ChartAreaShadowPreset(params));
            case SERIES_COLOR: return new DelegatingPreset(new com.zoho.sheet.knitcharts.presets.series.color.SeriesColorPreset(params));
            case DATA_PROPERTY_COLOR: return new DelegatingPreset(new com.zoho.sheet.knitcharts.presets.series.color.DataPropertyColorPreset(params));
            case SERIES_UP_COLOR: return new DelegatingPreset(new SeriesUpColorPreset(params));
            case SERIES_DOWN_COLOR: return new DelegatingPreset(new com.zoho.sheet.knitcharts.presets.series.color.SeriesDownColorPreset(params));
            case SERIES_TOTAL_COLOR: return new DelegatingPreset(new com.zoho.sheet.knitcharts.presets.series.color.SeriesTotalColorPreset(params));
            case SHARED_SERIES_SHADOW: return new DelegatingPreset(new SharedSeriesShadowPreset(params));
            case SERIES_SHADOW: return new DelegatingPreset(new SeriesShadowPreset(params));
            default: return new DelegatingPreset(new EmptyPreset());
        }
    }

    public synchronized static ObjectsSupplier getInstance() {
        return INSTANCE;
    }

}
