package com.zoho.sheet.knitcharts.utils;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.series.DataPropertiesReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.series.DataPropertyReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.series.SeriesPropertiesReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.series.SeriesPropertyReferences;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionConsumer;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.datameta.DataMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.datameta.seriestypes.SeriesType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.xaxis.XAxesReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.yaxis.PlotLineReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.yaxis.PlotLinesReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.yaxis.YAxesReferences;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.textreferences.yaxis.YAxisReferences;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties.SeriesProperties;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties.SeriesProperty;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.seriesproperties.dataproperties.DataProperty;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.xaxis.XAxes;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.YAxes;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.YAxis;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.customprops.yaxis.plotlines.PlotLine;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.reference.ReferencePool;
import com.zoho.sheet.knitcharts.reference.ReferenceType;

import java.util.*;


/**
 * Utilities related to Sheet Chart APIs
 */
public final class SheetChartAPIUtils {
    /**
     * Get or create Series property if there no series property associated with given index
     * @param sheetMeta Sheet meta instance
     * @param seriesIndex Series Index
     * @return Series property for the given index.
     */
    public static SeriesProperty computeSeriesPropertyIfNotAvailable(SheetMeta sheetMeta, int seriesIndex){
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = sheetMeta.getCustomProps()
                .getSeriesProperties().getSeriesPropertyMap();

        return seriesPropertyMap.computeIfAbsent(ChartUtils.toChartsInteger(seriesIndex),
                index -> new SeriesProperty(seriesIndex));
    }

    /**
     * Method to get series property for the given series index.
     * If there is no series property associated with the given index,
     * Dummy series property instance will be returned. <br/>
     * NOTE: use this method for read only purpose.
     * @param sheetMeta Sheet Meta instance.
     * @param seriesIndex Series Index.
     * @return Series Property instance.
     */
    public static SeriesProperty softComputeSeriesPropertyIfNotAvailable(SheetMeta sheetMeta, int seriesIndex){
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = sheetMeta.getCustomProps()
                .getSeriesProperties().getSeriesPropertyMap();

        SeriesProperty seriesProperty = seriesPropertyMap.get(ChartUtils.toChartsInteger(seriesIndex));

        if(seriesProperty == null){
            seriesProperty = new SeriesProperty(seriesIndex);
        }

        return seriesProperty;
    }

    public static DataProperty computeDataPropertyIfNotAvailable(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex){
        Map<ChartsInteger, DataProperty> dataPropertyMap = computeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex)
                .getDataProperties().getDataPropertyMap();

        return dataPropertyMap.computeIfAbsent(ChartUtils.toChartsInteger(dataPropsIndex),
                index -> new DataProperty(dataPropsIndex));
    }

    public static com.zoho.sheet.knitcharts.chart.sheetchart.meta.dataprops.seriestypes.SeriesType computeSeriesTypeIfNotAvailable(SheetMeta sheetMeta, int seriesIndex){
        com.zoho.sheet.knitcharts.chart.sheetchart.meta.dataprops.seriestypes.SeriesType seriesType = sheetMeta.getDataProps().getSeriesTypes()
                .getSeriesType(seriesIndex);
        if(seriesType == null){
            seriesType = new com.zoho.sheet.knitcharts.chart.sheetchart.meta.dataprops.seriestypes.SeriesType(seriesIndex);
            sheetMeta.getDataProps()
                    .getSeriesTypes()
                    .setSeriesType(seriesType);
        }

        return seriesType;
    }

    public static com.zoho.sheet.knitcharts.chart.sheetchart.meta.dataprops.seriestypes.SeriesType softComputeSeriesTypeIfNotAvailable(SheetMeta sheetMeta, int seriesIndex){
        com.zoho.sheet.knitcharts.chart.sheetchart.meta.dataprops.seriestypes.SeriesType seriesType = sheetMeta.getDataProps().getSeriesTypes()
                .getSeriesType(seriesIndex);
        if(seriesType == null){
            seriesType = new com.zoho.sheet.knitcharts.chart.sheetchart.meta.dataprops.seriestypes.SeriesType(seriesIndex);
        }

        return seriesType;
    }

    public static void forEachDataPropsSeriesTypes(SheetMeta sheetMeta, FunctionConsumer<Integer> consumer) {
        sheetMeta.getDataProps().getSeriesTypes().getSeriesTypeMap()
                .forEach((key, value) -> consumer.consume(key.getValue()));
    }

    public static Iterator<Integer> getDataPropsSeriesTypesIndexes(SheetMeta sheetMeta) {
        return sheetMeta.getDataProps().getSeriesTypes().getSeriesTypeMap()
                .keySet().stream().map(ChartsInteger::getValue).iterator();
    }

    public static DataProperty softComputeDataPropertyIfNotAvailable(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex){
        SeriesProperty seriesProperty = softComputeSeriesPropertyIfNotAvailable(sheetMeta, seriesIndex);
        DataProperty dataProperty = seriesProperty.getDataProperties().getDataProperty(dataPropsIndex);

        if(dataProperty == null){
            dataProperty = new DataProperty(dataPropsIndex);
        }

        return dataProperty;
    }

    public static SeriesProperty getSeriesProperty(SheetMeta sheetMeta, int seriesIndex) {
        return sheetMeta.getCustomProps()
                .getSeriesProperties()
                .getSeriesProperty(seriesIndex);
    }

    public static Map<ChartsInteger, SeriesProperty> fillUpSeriesProperty(SheetMeta sheetMeta, int seriesCount){
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap = sheetMeta.getCustomProps().getSeriesProperties().getSeriesPropertyMap();
        int idx = seriesPropertyMap.size();
        SeriesProperties seriesProperties = sheetMeta.getCustomProps().getSeriesProperties();

        for(;idx < seriesCount; idx++){
            seriesProperties.addSeriesProperty(new SeriesProperty(idx));
        }

        return seriesPropertyMap;
    }

    public static DataProperty getDataPropertyIfSeriesAvailable(SheetMeta sheetMeta, int seriesIndex, int dataPropsIndex) {
        SeriesProperty seriesProperty = getSeriesProperty(sheetMeta, seriesIndex);
        if(seriesProperty == null) { return null; }

        return seriesProperty.getDataProperties()
                .getDataProperty(dataPropsIndex);
    }

    public static XAxes getXAxes(SheetMeta sheetMeta, ChartsInteger xIndex){
        return sheetMeta.getCustomProps()
                .getxAxis()
                .getxAxesMap()
                .get(xIndex);
    }

    public static XAxes computeXAxisIfNotAvailable(SheetMeta sheetMeta, int xIndex){
        return sheetMeta.getCustomProps()
                .getxAxis()
                .getxAxesMap()
                .computeIfAbsent(ChartUtils.toChartsInteger(xIndex), index -> new XAxes(xIndex));
    }

    public static YAxes getYAxes(SheetMeta sheetMeta, ChartsInteger yIndex){
        return sheetMeta.getCustomProps()
                .getyAxis()
                .getyAxesMap()
                .get(yIndex);
    }

    public static Map<ChartsInteger, YAxes> fillUpYAxis(SheetMeta sheetMeta, int yAxisCount){
        Map<ChartsInteger, YAxes> yAxesMap = sheetMeta.getCustomProps().getyAxis().getyAxesMap();
        int idx = yAxesMap.size();
        YAxis yAxis = sheetMeta.getCustomProps().getyAxis();

        for(;idx < yAxisCount; idx++){
            yAxis.addYAxes(new YAxes(idx));
        }

        return yAxesMap;
    }

    public static YAxes computeYAxisIfNotAvailable(SheetMeta sheetMeta, int yIndex){
        return sheetMeta.getCustomProps()
                .getyAxis()
                .getyAxesMap()
                .computeIfAbsent(ChartUtils.toChartsInteger(yIndex), index -> new YAxes(yIndex));
    }


    public static YAxes softComputeYAxisIfNotAvailable(SheetMeta sheetMeta, int yIndex){
        YAxes yAxes = sheetMeta.getCustomProps().getyAxis().getyAxesMap().get(ChartUtils.toChartsInteger(yIndex));

        if(yAxes == null) { yAxes =  new YAxes(yIndex); }
        return yAxes;
    }

    public static PlotLine getPlotline(SheetMeta sheetMeta, ChartsInteger yIndex, ChartsInteger plIndex){
        return getYAxes(sheetMeta, yIndex)
                .getPlotLines()
                .getPlotLineMap()
                .get(plIndex);
    }

    public static PlotLine computePlotlineIfNotAvailable(SheetMeta sheetMeta, int yIndex, int plIndex){
        return computeYAxisIfNotAvailable(sheetMeta, yIndex)
                .getPlotLines()
                .getPlotLineMap()
                .computeIfAbsent(ChartUtils.toChartsInteger(plIndex), index -> new PlotLine(plIndex));
    }

    public static SeriesType computeDataMetaSeriesTypeIfNotAvailable(DataMeta dataMeta, int seriesIndex){
        SeriesType seriesType = dataMeta.getSeriesTypes().getSeriesType(seriesIndex);

        if(seriesType == null) {
            seriesType = new SeriesType(seriesIndex);
            dataMeta.getSeriesTypes().addSeriesType(seriesType);
        }

        return seriesType;
    }

    public static void forEachCustomSeriesProps(SheetMeta sheetMeta, FunctionConsumer<Integer> callBack) {
        Map<ChartsInteger, SeriesProperty> seriesPropertyMap =
                sheetMeta.getCustomProps().getSeriesProperties().getSeriesPropertyMap();
        List<ChartsInteger> seriesIndices = new ArrayList<>(seriesPropertyMap.keySet());

        for(ChartsInteger seriesIndex: seriesIndices) {
            callBack.consume(seriesIndex.getValue());
        }
    }

    public static void forEachCustomSeriesDataProps(SheetMeta sheetMeta, int seriesIndex, FunctionConsumer<Integer> callback) {
        SeriesProperty seriesProperty = getSeriesProperty(sheetMeta, seriesIndex);
        if(seriesProperty  == null) { return; }

        Map<ChartsInteger, DataProperty> dataPropertyMap = seriesProperty.getDataProperties().getDataPropertyMap();
        List<ChartsInteger> dataPropsIndices = new ArrayList<>(dataPropertyMap.keySet());

        for(ChartsInteger dataPropsIndex: dataPropsIndices) {
            callback.consume(dataPropsIndex.getValue());
        }
    }

    public static XAxesReferences computeXAxisReferences(SheetMeta sheetMeta, int xAxisIndex) {
        XAxesReferences references = sheetMeta.getTextReferences()
                .getxAxisReferences()
                .getXAxesReferences(xAxisIndex);
        if(references == null) {
            references = new XAxesReferences(xAxisIndex);

            sheetMeta.getTextReferences().getxAxisReferences()
                    .addXAxesReferences(references);
        }

        return references;
    }

    public static XAxesReferences softComputeXAxisReferences(SheetMeta sheetMeta, int xAxisIndex) {
        XAxesReferences references = sheetMeta.getTextReferences()
                .getxAxisReferences()
                .getXAxesReferences(xAxisIndex);
        if(references == null) {
            references = new XAxesReferences(xAxisIndex);
        }

        return references;
    }

    public static YAxesReferences computeYAxisReferences(SheetMeta sheetMeta, int yAxisIndex) {
        YAxisReferences yAxisReferences = sheetMeta.getTextReferences().getyAxisReferences();
        YAxesReferences references = yAxisReferences.getYAxesReferences(yAxisIndex);

        if(references == null) {
            references = new YAxesReferences(yAxisIndex);
            yAxisReferences.addYAxesReferences(references);
        }

        return references;
    }

    public static YAxesReferences softComputeYAxisReferences(SheetMeta sheetMeta, int yAxisIndex) {
        YAxisReferences yAxisReferences = sheetMeta.getTextReferences().getyAxisReferences();
        YAxesReferences references = yAxisReferences.getYAxesReferences(yAxisIndex);

        if(references == null) {
            references = new YAxesReferences(yAxisIndex);
        }

        return references;
    }

    public static PlotLineReferences computePlotLineReferences(SheetMeta sheetMeta, int yAxisIndex, int plIndex) {
        YAxesReferences yAxisReferences = computeYAxisReferences(sheetMeta, yAxisIndex);
        PlotLinesReferences plotLinesReferences = yAxisReferences.getPlotLinesReferences();
        PlotLineReferences references = plotLinesReferences.getPlotLineReferences(plIndex);

        if(references == null) {
            references = new PlotLineReferences(plIndex);
            plotLinesReferences.addPlotLineReferences(references);
        }

        return references;
    }

    public static void forEachXAxisReferences(SheetMeta sheetMeta, FunctionConsumer<Integer> forEachCallBack) {
        List<Integer> index = sheetMeta.getTextReferences().getxAxisReferences().getXAxisIndex();

        index.forEach(forEachCallBack::consume);
    }

    public static void forEachYAxisReferences(SheetMeta sheetMeta, FunctionConsumer<Integer> forEachCallBack) {
        List<Integer> index = sheetMeta.getTextReferences().getyAxisReferences().getYAxisIndex();

        index.forEach(forEachCallBack::consume);
    }

    public static Iterable<Integer> getSeriesReferencesIterable(SheetMeta sheetMeta) {
        Set<ChartsInteger> index = sheetMeta.getTextReferences()
                .getSeriesPropertiesReferences()
                .getProperties().keySet();

        return CollectionsUtils.toIterable(new CollectionsUtils.ChartsIntegerIterator(index));
    }

    public static Iterable<Integer> getDataPropertyReferencesIterable(SheetMeta sheetMeta, int seriesIndex) {
        Set<ChartsInteger> index = computeIfAbsentSeriesPropertyReferences(sheetMeta, seriesIndex)
                .getDataProperties()
                .getProperties().keySet();

        return CollectionsUtils.toIterable(new CollectionsUtils.ChartsIntegerIterator(index));
    }

    public static void forEachPlotLineReferences(SheetMeta sheetMeta, int yAxisIndex, FunctionConsumer<Integer> forEachCallBack) {
        int count = computeYAxisReferences(sheetMeta, yAxisIndex).getPlotLinesReferences().getPlotlinesCount();

        for(int i = 0; i < count; i++) {
            forEachCallBack.consume(i);
        }
    }

    public static String getTextFromReference(Workbook workbook, String textReference) {
        if(textReference == null) { return null; }

        ReferencePool referencePool = workbook.getChartsContainer().getReferencePool();
        Reference reference = referencePool.getReference(textReference);

        if(reference.getReferenceType() == ReferenceType.INVALID) { return null; }
        return reference.getTextReference().getText(workbook);
    }

    public static Double getValueFromReference(Workbook workbook, String valueReference) {
        if(valueReference == null) { return null; }

        ReferencePool referencePool = workbook.getChartsContainer().getReferencePool();
        Reference reference = referencePool.getReference(valueReference);

        if(reference.getReferenceType() == ReferenceType.INVALID) { return null; }

        return reference.getValueReference().getValue(workbook);
    }

    public static SeriesPropertyReferences computeIfAbsentSeriesPropertyReferences(SheetMeta sheetMeta, int seriesIndex) {
        SeriesPropertiesReferences seriesPropertiesReferences = sheetMeta.getTextReferences().getSeriesPropertiesReferences();
        SeriesPropertyReferences references = seriesPropertiesReferences.getProperty(seriesIndex);

        if(references == null) {
            references = new SeriesPropertyReferences();
            seriesPropertiesReferences.add(seriesIndex, references);
        }

        return references;
    }

    public static SeriesPropertyReferences softComputeIfAbsentSeriesPropertyReferences(SheetMeta sheetMeta, int seriesIndex) {
        SeriesPropertiesReferences seriesPropertiesReferences = sheetMeta.getTextReferences().getSeriesPropertiesReferences();
        SeriesPropertyReferences references = seriesPropertiesReferences.getProperty(seriesIndex);

        if(references == null) {
            references = new SeriesPropertyReferences();
        }

        return references;
    }

    public static DataPropertyReferences computeIfAbsentDataPropertyReferences(SheetMeta sheetMeta, int seriesIndex, int dataPropertyIndex) {
        DataPropertiesReferences dataPropertiesReferences = computeIfAbsentSeriesPropertyReferences(sheetMeta, seriesIndex)
                .getDataProperties();
        DataPropertyReferences references = dataPropertiesReferences.getProperty(dataPropertyIndex);

        if(references == null) {
            references = new DataPropertyReferences();
            dataPropertiesReferences.add(dataPropertyIndex, references);
        }

        return references;
    }

    public static DataPropertyReferences softComputeIfAbsentDataPropertyReferences(SheetMeta sheetMeta, int seriesIndex, int dataPropertyIndex) {
        DataPropertiesReferences dataPropertiesReferences = softComputeIfAbsentSeriesPropertyReferences(sheetMeta, seriesIndex)
                .getDataProperties();
        DataPropertyReferences references = dataPropertiesReferences.getProperty(dataPropertyIndex);

        if(references == null) {
            references = new DataPropertyReferences();
        }

        return references;
    }

}
