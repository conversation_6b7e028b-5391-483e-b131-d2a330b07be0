package com.zoho.sheet.knitcharts.utils;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

public class ConverterTest {
    private static boolean validateJSONs(JSONArrayWrapper benchmarkJSON, JSONArrayWrapper testJSON){
        int size = benchmarkJSON.length();
        if(testJSON.length() != size){ return false; }

        for(int i = 0; i < size; i++){
            if(!validateObjects(benchmarkJSON.get(i), testJSON.get(i))){ return false; }
        }
        return true;
    }

    private static boolean validateJSONs(JSONObjectWrapper benchmarkJSON, JSONObjectWrapper testJSON){
        Iterator<?> keysIterator = benchmarkJSON.keys();
        while(keysIterator.hasNext()){
            String key = keysIterator.next().toString();
            if(!testJSON.has(key)){
                //System.out.printf("Key: {%s} doesn't available in Test JSONWrapper%n", key);                                           // NO I18N
                continue;
            }

            if(!validateObjects(benchmarkJSON.get(key), testJSON.get(key))){
                String s = "System.out.printf(\"Key: {%s} contains different values benchmark value : {%s} test value : {%s}%n\", key, benchmarkJSON.get(key).toString(), testJSON.get(key).toString());";     // NO I18N
            }
        }
        return true;
    }

    private static boolean validateObjects(Object benchmarkObj, Object testObject){
        if(benchmarkObj instanceof JSONObjectWrapper && testObject instanceof  JSONObjectWrapper){
            return validateJSONs((JSONObjectWrapper) benchmarkObj, (JSONObjectWrapper) testObject);
        } else if(benchmarkObj instanceof JSONArrayWrapper && testObject instanceof JSONArrayWrapper){
            return validateJSONs((JSONArrayWrapper) benchmarkObj, (JSONArrayWrapper) testObject);
        }else{
            return Objects.equals(benchmarkObj, testObject);
        }
    }

    private static List<JSONObjectWrapper> readJSONs(BufferedReader reader) throws IOException {
        StringBuilder builder = new StringBuilder();
        String line;
        while((line = reader.readLine()) != null){
            builder.append(line);
        }
        return ChartUtils.JSONArrayToList(new JSONArrayWrapper(builder.toString()));
    }

    public static void main(String[] args){
        /*String benchmarkJSONFilePath = "/Users/<USER>/Desktop/benchmarkJSONs.json";                                     // NO I18N
        String testJSONFilePath = "/Users/<USER>/Desktop/testJSONs.json";                                               // NO I18N
        List<JSONObjectWrapper> benchmarkJSONObjects, testJSONObjects;

        try(BufferedReader benchmarkJSONReader = new BufferedReader(new FileReader(benchmarkJSONFilePath))){
            benchmarkJSONObjects = readJSONs(benchmarkJSONReader);
        }catch (IOException e){
            throw new RuntimeException(e.getMessage());
        }

        try(BufferedReader testJSONReader = new BufferedReader(new FileReader(testJSONFilePath))){
            testJSONObjects = readJSONs(testJSONReader).stream().map(ChartJSONConverter::convertOldChartJSONTest).collect(Collectors.toList());
        }catch (IOException e){
            throw new RuntimeException(e.getMessage());
        }

        if(benchmarkJSONObjects.size() != testJSONObjects.size()){
            throw new RuntimeException("Benchmark and Test JSONWrapper size doesn't match");                                       // NO I18N
        }

        for(int i = 0; i < benchmarkJSONObjects.size(); i++){
            validateJSONs(benchmarkJSONObjects.get(i), testJSONObjects.get(i));
            //System.out.printf("----------------- Test completed for JSONObjectWrapper with index %d ---------------%n", i);        // NO I18N
        }*/
    }
}
