package com.zoho.sheet.knitcharts.utils;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsDAO;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class ChartEngineUtils {

    /**
     * Method to clear the current version charts data from DB
     * @param workbook Workbook instance
     * @param documentOwner Document Owner
     * @param documentID Document ID
     * @param sheetsToBeRemoved list of sheets to removed
     */
    public static void clearCurrentChartsFromDB(Workbook workbook, String documentOwner, long documentID, String[] sheetsToBeRemoved) {
        ChartActivityMonitor.ChartActivity activity = ObjectsSupplier.getInstance().getChartActivityMonitor().getChartActivity(
                "CLEAR_CURRENT_VERSION_CHART_DB", null, documentOwner, null, null);                 // NO I18N
        activity.start();
       try {
           ChartsDAO dao = ChartsDAO.getDefaultDAO(documentOwner, documentID);
           for(String sheetName : sheetsToBeRemoved) {
               dao.unPublishChartsBySheetName(workbook, sheetName);
               dao.removeChartsBySheetName(sheetName);
           }
           activity.completed();
       } catch (Exception e) {
           activity.failed(e);
       }
    }

    /**
     * Method to revert the charts DB to the given version.
     * @param documentOwner Document owner
     * @param documentID Document ID
     * @param version Version to be reverted
     */
    public static void revertVersionDB(String documentOwner, long documentID, String version) {
        ChartActivityMonitor.ChartActivity activity = ObjectsSupplier.getInstance().getChartActivityMonitor().getChartActivity(
          "REVERT_VERSION_CHART_DB", null, documentOwner, null, null);                          // NO I18N
        activity.start();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(documentOwner, documentID);

        dao.revertVersion(version);
        activity.completed();
    }

}
