package com.zoho.sheet.knitcharts.utils;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.ext.standard.ZSPrintVisitor;
import com.adventnet.zoho.websheet.model.pivot.PivotChartUtil;
import com.adventnet.zoho.websheet.model.style.datastyle.CurrencyChar;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants;
import com.adventnet.zoho.websheet.model.style.datastyle.PatternComponent;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.*;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.parser.Node;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTableCell;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTableCellType;
import com.zoho.sheet.knitcharts.chartsdatatable.EmptyTableCell;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Tuple;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.reference.ReferencePool;
import com.zoho.sheet.knitcharts.reference.ReferenceType;
import com.zoho.sheet.knitcharts.reference.referenceimpl.RangeExpressionReference;
import com.zoho.sheet.knitcharts.reference.referenceimpl.RangeReference;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.DataType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Data related Utility function
 * <AUTHOR>
 */
public class DataUtils {

    public static Value getRoundedValue(DataTableCell dataTableCell, Value value) {
        Cell.Type type = value.getType();
        if(type == Cell.Type.FLOAT && !(dataTableCell.getType() == DataTableCellType.PERCENTAGE)) {
            Object obj = value.getValue();
            if(obj instanceof Number) {
                double floatValue = ((Number)obj).doubleValue();
                return new ValueI(type, Math.round(floatValue * 100.0) / 100.0);
            }
            return value;
        }
        return value;
    }

    public static boolean isNumber(Object object) {
        try {
            Double.parseDouble(Objects.toString(object));
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static Object truncateDecimals(Object object) {
        if(object instanceof Double) {
            double dbl = (Double) object;
            if((dbl - (int) dbl) == 0) { return (int) dbl; }
        } else if(object instanceof Float) {
            float flt = (Float) object;
            if((flt - (int) flt) == 0) { return (int) flt; }
        }
        return object;
    }

    /**
     * Method to get the pattern string from cell. If cell type is currency, currency symbol will be returned
     * @param cell Data table cell
     * @return Patter of the cell content
     */
    private static String getPattern(DataTableCell cell){
        if(cell.getType() == DataTableCellType.CURRENCY){
            return cell.getCurrencySymbol();
        }else if(cell.getType().isDateType()){
            Date date = cell.convertToDate();
            boolean hasMilliSeconds = date.getTime() % 1000 != 0;

            return ChartUtils.getDateFormat(cell.getType().getCellType(), cell.getPattern(), cell.getSpreadsheetSettings(), hasMilliSeconds);
        } else if (cell.getType() == DataTableCellType.PERCENTAGE) {
            return "%";                                                                     // NO I18N
        }else{
            return "";                                                                      // NO I18N
        }
    }

    public static int compareCellType(Map.Entry<DataTableCellType, Tuple<String, Integer>> t1,
                                      Map.Entry<DataTableCellType, Tuple<String, Integer>> t2) {
        DataTableCellType t1Key = t1.getKey();
        DataTableCellType t2Key = t2.getKey();

        if(t1Key.isDateType()) {
            return 1;
        } else if(t2Key.isDateType()) {
            return -1;
        } else if(t1Key.isNumberType()) {
            return 1;
        } else if(t2Key.isNumberType()) {
            return -1;
        }
        return Integer.compare(t1.getValue().getSecondParam(), t2.getValue().getSecondParam());
    }

    public static boolean doesChartSupportGroupedCategories(ChartType chartType) {
        return chartType == ChartType.COLUMN || chartType == ChartType.STACKED_COLUMN || chartType == ChartType.STACKED_PERCENT_COLUMN || chartType == ChartType.GROUPED_COLUMN ||
                chartType == ChartType.BAR || chartType == ChartType.STACKED_BAR || chartType == ChartType.STACKED_PERCENT_BAR || chartType == ChartType.GROUPED_BAR ||
                chartType == ChartType.LINE || chartType == ChartType.SPLINE || chartType == ChartType.STEP_LINE || chartType == ChartType.AREA || chartType == ChartType.AREA_SPLINE ||
                chartType == ChartType.STACKED_AREA || chartType == ChartType.STACKED_PERCENT_AREA;
    }

    public static boolean doesDataTypeSupportedForGroupedCategories(DataType dataType) {
        return !(dataType == DataType.NUMBER || dataType == DataType.EMPTY
                || dataType == DataType.URL || dataType == DataType.IMAGE_URL || dataType == DataType.YEAR);
    }

    public static boolean doesChartSupportInfoText(ChartType chartType) {
        return chartType == ChartType.RACE_BAR || chartType == ChartType.RACE_LINE;
    }

    public static boolean isChartInScatterFamily(ChartType chartType) {
        return chartType == ChartType.SCATTER || chartType == ChartType.SCATTER_LINE
                || chartType == ChartType.SCATTER_LINE_MARKERS || chartType == ChartType.BUBBLE;
    }

    public static Tuple<DataTableCellType, String> findMostDesiredType(Map<DataTableCellType, Tuple<String, Integer>> countMap) {
        List<Tuple<DataTableCellType, String>> sorted = countMap.entrySet().stream().sorted((o1, o2) -> compareCellType(o1, o2)).map(entry -> new Tuple<>(entry.getKey(), entry.getValue().getFirstParam()))
                .collect(Collectors.toList());

        return sorted.get(sorted.size() - 1);
    }

    public static Tuple<DataTableCellType, String> findMajorTypeAndPattern(List<DataTableCell> originalPopulation){
        int size = originalPopulation.size();
        Map<DataTableCellType, Tuple<String, Integer>> typesCountMap = new HashMap<>();
        double incrementFactor = size > 100 ? size / 100.0 : 1.0, index = 0;

        for(int idx = 0; idx < size; idx = (int) index){
            index += incrementFactor;
            DataTableCell cell = originalPopulation.get(idx);

            DataTableCellType type = cell.getType();

            Tuple<String, Integer> typeDetails = typesCountMap.computeIfAbsent(type, __ -> new Tuple<>("", 0));        // NO I18N
            typeDetails.setFirstParam(getPattern(cell));
            typeDetails.setSecondParam(typeDetails.getSecondParam() + 1);
        }

        if(typesCountMap.isEmpty()) {
            return new Tuple<>(DataTableCellType.STRING, "");
        }else{
            return findMostDesiredType(typesCountMap);
        }
    }

    public static boolean isCompletelyEmpty(List<List<DataTableCell>> matrix) {
        return !matrix.stream().anyMatch(rows -> rows.stream().anyMatch(cell -> !cell.isEmptyCell()));
    }

    public static List<DataTableCell> getRow(List<List<DataTableCell>> tableCells, int rowIndex){
        return tableCells.isEmpty() ? new ArrayList<>() : tableCells.get(rowIndex);
    }

    public static ZArrayI getRowAsZArray(List<List<DataTableCell>> tableCells, int rowIndex){
        List<DataTableCell> row = getRow(tableCells, rowIndex);
        List<Value> values = row.stream().map(DataTableCell::getValueForAggregation).collect(Collectors.toList());

        return new ZArray(values, values.size(), 1);
    }

    public static ZArrayI getRowAsZArrayWithoutHeader(List<List<DataTableCell>> tableCells, int rowIndex){
        List<DataTableCell> row = getRow(tableCells, rowIndex);
        List<Value> values = IntStream.range(1, row.size()).mapToObj(rowIdx -> row.get(rowIdx).getValueForAggregation())
                .collect(Collectors.toList());
        return new ZArray(values, values.size(), 1);
    }

    public static List<DataTableCell> getCol(List<List<DataTableCell>> tableCells, int colIndex){
        List<DataTableCell> column = new ArrayList<>();

        tableCells.forEach(row -> column.add(colIndex < row.size() ? row.get(colIndex) : new EmptyTableCell()));
        return column;
    }

    public static ZArrayI getColAsZArray(List<List<DataTableCell>> tableCells, int colIndex){
        List<Value> values = new ArrayList<>();
        tableCells.forEach(row -> values.add(colIndex < row.size() ? row.get(colIndex).getValueForAggregation() : Value.EMPTY_VALUE));

        return new ZArray(values, values.size(), 1);
    }

    public static ZArrayI getColAsZArrayWithoutHeader(List<List<DataTableCell>> tableCells, int colIndex){
        List<Value> values = IntStream.range(1, tableCells.size()).mapToObj(rowIdx -> {
            List<DataTableCell> row = tableCells.get(rowIdx);
            return colIndex < row.size() ?  row.get(colIndex).getValueForAggregation() : Value.EMPTY_VALUE;
        }).collect(Collectors.toList());

        return new ZArray(values, values.size(), 1);
    }

    public static int getRowSize(List<List<DataTableCell>> tableCells){
        int rowSize = 0;

        for(List<DataTableCell> row: tableCells){
            rowSize = Math.max(rowSize, row.size());
        }
        return rowSize;
    }

    public static int getColSize(List<List<DataTableCell>> tableCells){
        return tableCells.size();
    }

    public static DataType determineDataType(DataTableCellType type){
        if(type == DataTableCellType.YEAR) {
            return DataType.YEAR;
        } else if(type.isNumberType()){
            return DataType.NUMBER;
        }else if(type.isDateType()){
            return DataType.DATE;
        } else if(type == DataTableCellType.UNDEFINED || type == DataTableCellType.ERROR) {
            return DataType.EMPTY;
        } else if(type == DataTableCellType.URL) {
            return DataType.URL;
        } else if(type == DataTableCellType.BOOLEAN) {
            return DataType.NUMBER;
        }
        else{
            return DataType.STRING;
        }
    }

    public static DataType getColumnDataType(List<List<DataTableCell>> tableCell, int colIndex){
        List<DataTableCell> column = getCol(tableCell, colIndex);

        return determineDataType(findMajorTypeAndPattern(column).getFirstParam());
    }

    public static DataType getRowDataType(List<List<DataTableCell>> tableCell, int rowIndex){
        List<DataTableCell> row = getRow(tableCell, rowIndex);

        return determineDataType(findMajorTypeAndPattern(row).getFirstParam());
    }

    public static JSONObjectWrapper getPivotData(Workbook workbook, SheetMeta sheetMeta, ChartType chartType){
        Sheet sheet = workbook.getSheet(0);
        String pivotID = SheetChartGetterAPI.getDataSources(sheetMeta).get(0);

        return PivotChartUtil.getSeriesData(sheet, pivotID, ChartUtils.getOldChartNameForChartType(chartType),
                false, null, true);
    }

    public static JSONObjectWrapper getPivotCurrencySymbol(Workbook workbook, SheetMeta sheetMeta){
        Sheet sheet = workbook.getSheet(0);
        String pivotID = SheetChartGetterAPI.getDataSources(sheetMeta).get(0);

        return PivotChartUtil.getCurrencySymbol(sheet, pivotID);
    }

    public static List<ZArrayI> getAsZArrayI(List<List<DataTableCell>> tableCells, SheetMeta sheetMeta) {
        List<ZArrayI> zArrayIS = new ArrayList<>();
        SeriesInType seriesInType = SheetChartAPI.getSeriesIn(sheetMeta);
        if(seriesInType == SeriesInType.COLUMNS) {
            int colCount = getRowSize(tableCells);
            boolean frl = SheetChartAPI.getFirstRowLabel(sheetMeta);

            for(int idx = 0; idx < colCount; idx++) {
                if(frl) {
                    zArrayIS.add(getColAsZArrayWithoutHeader(tableCells, idx));
                } else {
                    zArrayIS.add(getColAsZArray(tableCells, idx));
                }
            }
        }else {
            int rowCount = getColSize(tableCells);
            boolean fcl = SheetChartAPI.getFirstColLabel(sheetMeta);
            for(int idx = 0; idx < rowCount; idx++) {
                if(fcl) {
                    zArrayIS.add(getRowAsZArrayWithoutHeader(tableCells, idx));
                } else {
                    zArrayIS.add(getRowAsZArray(tableCells, idx));
                }
            }
        }
        return zArrayIS;
    }

    public static boolean canCellsLimitExceed(ChartType chartType, List<List<DataTableCell>> dataTableCells) {
        if(chartType == ChartType.RACE_BAR || chartType == ChartType.RACE_LINE) { return false; }
        return (dataTableCells.size() * dataTableCells.get(0).size()) > EngineConstants.MAX_CHART_CELL_COUNT;
    }

    public static boolean canSeriesLimitExceed(SeriesInType seriesInType, List<List<DataTableCell>> dataTableCells, int maxSeriesCount) {
        return seriesInType == SeriesInType.COLUMNS ?
                (dataTableCells.get(0).size() > maxSeriesCount) :
                (dataTableCells.size() > maxSeriesCount);
    }

    public static int getMaxSeriesCount(ChartType chartType, boolean firstRecordLabel) {
        int maxSeriesCount;

        switch (chartType) {
            case DONUT:
            case SEMI_DONUT: {
                maxSeriesCount = EngineConstants.PIE_MAX_SERIES;
                break;
            }
            case OHLC: {
                maxSeriesCount = EngineConstants.OHLC_MAX_SERIES;
                break;
            }
            case FUNNEL:
            case PIE:
            case SEMI_PIE:
            case PIE_PARLIAMENT:
            case DONUT_PARLIAMENT:
            case WATERFALL:
            case WEIGHTED_FUNNEL: {
                maxSeriesCount = EngineConstants.SINGLE_SERIES;
                break;
            }
            default: {
                maxSeriesCount = EngineConstants.CHART_MAX_SERIES;
            }
        }

        maxSeriesCount = firstRecordLabel ? maxSeriesCount + 1 : maxSeriesCount;

        return maxSeriesCount;
    }

    public static List<List<DataTableCell>> filterOutExcessSeries(ChartType chartType, SeriesInType seriesInType, List<List<DataTableCell>> originalCells, int maxSeriesCount, boolean isDataManipulationPossible) {
        if(chartType == ChartType.RACE_BAR) {
            return filterOutExcessDataForRacingCharts(seriesInType, originalCells, maxSeriesCount);
        } else {
            return filterOutExcessDataForOtherCharts(seriesInType, originalCells, maxSeriesCount, isDataManipulationPossible);
        }
    }

    private static List<List<DataTableCell>> filterOutExcessDataForRacingCharts(SeriesInType seriesInType, List<List<DataTableCell>> originalCells, int maxSeriesCount) {
        int rowCount, columnCount;
        if(seriesInType == SeriesInType.COLUMNS) {
            columnCount = Math.min(originalCells.get(0).size(), maxSeriesCount);
            return filterOutExcessColumns(originalCells, columnCount);
        } else {
            rowCount = Math.min(originalCells.size(), maxSeriesCount);
            return filterOutExcessRows(originalCells, rowCount);
        }
    }

    private static List<List<DataTableCell>> filterOutExcessDataForOtherCharts(SeriesInType seriesInType, List<List<DataTableCell>> originalCells, int maxSeriesCount, boolean isDataManipulationPossible){
        int rowCount, columnCount;
        int maxCellCount = isDataManipulationPossible ? EngineConstants.MAX_CHART_CELL_COUNT_BEFORE_AGGREGATION : EngineConstants.MAX_CHART_CELL_COUNT;
        if(seriesInType == SeriesInType.COLUMNS) {
            columnCount = isDataManipulationPossible ? originalCells.get(0).size() : Math.min(originalCells.get(0).size(), maxSeriesCount);
            rowCount = Math.min(maxCellCount / columnCount, originalCells.size());
        } else {
            rowCount = isDataManipulationPossible ? originalCells.size() : Math.min(originalCells.size(), maxSeriesCount);
            columnCount = maxCellCount / rowCount;
        }

        return filterOutExcessCells(originalCells,rowCount,columnCount);
    }

    private static List<List<DataTableCell>> filterOutExcessCells(List<List<DataTableCell>> originalCells, int rowCount, int columnCount) {
        List<List<DataTableCell>> result = new ArrayList<>();

        for (int rowIdx = 0; rowIdx < rowCount; rowIdx++) {
            List<DataTableCell> row = originalCells.get(rowIdx);

            result.add(row.size() <= columnCount ? row : row.subList(0, columnCount));
        }

        return result;
    }

    private static List<List<DataTableCell>> filterOutExcessColumns(List<List<DataTableCell>> originalCells, int columnCount) {
        List<List<DataTableCell>> result = new ArrayList<>();

        for (List<DataTableCell> row: originalCells) {
            result.add(row.size() <= columnCount ? row : row.subList(0, columnCount));
        }

        return result;
    }

    private static List<List<DataTableCell>> filterOutExcessRows(List<List<DataTableCell>> originalCells, int rowCount) {
        List<List<DataTableCell>> result = new ArrayList<>();

        for (int rowIdx = 0; rowIdx < rowCount; rowIdx++) {
            List<DataTableCell> row = originalCells.get(rowIdx);

            result.add(row);
        }

        return result;
    }

    public static List<List<Object>> formatDataTableCells(Workbook workbook, List<List<DataTableCell>> tableCells, boolean fcl, boolean frl) {
        List<List<Object>> result = new ArrayList<>();
        int rowIdx = 0;

        if(frl) {
            result.add(mapToHeader(workbook, tableCells.get(rowIdx)));
            rowIdx++;
        }

        for(; rowIdx < tableCells.size(); rowIdx++) {
            result.add(mapToObject(workbook, tableCells.get(rowIdx), fcl));
        }
        return result;
    }

    public static List<Object> mapToObject(Workbook workbook, List<DataTableCell> row, boolean fcl) {
        List<Object> result = new ArrayList<>();
        int colIdx = 0;

        if(fcl) {
            result.add(row.get(colIdx).getValueForLabel(workbook));
            colIdx++;
        }

        for(; colIdx < row.size(); colIdx++) {
            result.add(row.get(colIdx).getValueObject(workbook));
        }

        return result;
    }

    public static List<Object> mapToHeader(Workbook workbook, List<DataTableCell> row) {
        return row.stream().map((cell) -> cell.getValueForLabel(workbook)).collect(Collectors.toList());
    }

    public static Object evaluateNode(Workbook workbook, Node node) {
        if(node == null) { return null; }

        try {
            Cell dummyCell = getDummyCell(workbook);

            ZSEvaluator evaluator = ChartUtils.typeCast(Workbook.getJepForOtherActions().getEvaluator());

            return evaluator.evaluate(node, dummyCell, false, true);
        }catch (EvaluationException e) {
            return null;
        }
    }

    public static String getCurrentSymbol(ZSPattern pattern, Locale locale) {
        try {
            Locale currencyLocale = null;

            if(pattern != null && pattern.getType() != Cell.Type.UNDEFINED) {

                for(PatternComponent component : pattern.getComponentList()) {
                    if(component instanceof CurrencyChar) {
                        currencyLocale = ((CurrencyChar) component).getCurrencyLocale();
                        break;
                    }
                }

                if(currencyLocale != null) {
                    String currencyCode = LocaleUtil.getCurrencyInstance(currencyLocale).getCurrencyCode();
                    return LocaleUtil.getCurrencySymbol(currencyCode);
                }
                else {
                    return LocaleUtil.getCurrencySymbol(locale);
                }
            }
        }catch(Exception e) {
            return "";      // NO I18N
        }

        return "";
    }

    public static List<DataRange> mergeDataRanges(Collection<DataRange> dataRanges) {
        Map<String, List<DataRange>> sheetWiseDataRanges = new HashMap<>();
        List<DataRange> mergedDataRanges = new ArrayList<>();

        for(DataRange dataRange : dataRanges) {
            sheetWiseDataRanges.computeIfAbsent(dataRange.getAssociatedSheetName(), k -> new ArrayList<>())
                    .add(dataRange);
        }

        for(Map.Entry<String, List<DataRange>> entry : sheetWiseDataRanges.entrySet()) {
            mergedDataRanges.addAll(mergeDataRangesForSingleSheet(entry.getValue()));
        }

        return mergedDataRanges;

    }

    /**
     * Method used to merge the collection data ranges for a single sheet.
     * Note: this method gives priority to horizontal merging
     * @param dataRanges collection of data ranges for a sheet
     * @return list of merged data ranges
     */
    public static List<DataRange> mergeDataRangesForSingleSheet(Collection<DataRange> dataRanges) {
        List<DataRange> hMerged = mergeDataRangesHorizontallyForSingleSheet(dataRanges);

        return mergeDataRangesVerticallyForSingleSheet(hMerged);
    }

    public static List<DataRange> mergeDataRangesHorizontallyForSingleSheet(Collection<DataRange> dataRanges) {
        List<DataRange> hMerged = new ArrayList<>();
        BitSet bitSet = new BitSet(dataRanges.size());

        List<DataRange> hSorted = dataRanges.stream().sorted(Comparator.comparingInt(DataRange::getStartColIndex))
                .collect(Collectors.toList());
        int hSortedSize = hSorted.size();
        for(int outer = 0; outer < hSortedSize; outer++) {
            DataRange outerDataRange = copyOf(hSorted.get(outer));

            for(int inner = outer + 1; inner < hSortedSize; inner++) {
                DataRange innerDataRange = hSorted.get(inner);

                if(!bitSet.get(inner)) {
                    if(outerDataRange.getStartRowIndex() == innerDataRange.getStartRowIndex() &&
                            outerDataRange.getEndRowIndex() == innerDataRange.getEndRowIndex() && (
                            innerDataRange.getStartColIndex() <= outerDataRange.getEndColIndex() ||
                                    innerDataRange.getStartColIndex() - outerDataRange.getEndColIndex() == 1)) {
                        bitSet.set(inner);
                        outerDataRange.setEndColIndex(innerDataRange.getEndColIndex());
                    }
                }
            }
            if(!bitSet.get(outer)) { hMerged.add(outerDataRange); }
        }

        return hMerged;
    }

    public static List<DataRange> mergeDataRangesVerticallyForSingleSheet(Collection<DataRange> dataRanges) {
        List<DataRange> vMerged = new ArrayList<>();
        BitSet bitSet = new BitSet(dataRanges.size());

        List<DataRange> vSorted = dataRanges.stream().sorted(Comparator.comparingInt(DataRange::getStartRowIndex))
                .collect(Collectors.toList());
        int vSortedSize = vSorted.size();
        for(int outer = 0; outer < vSortedSize; outer++) {
            DataRange outerDataRange = copyOf(vSorted.get(outer));

            for(int inner = outer + 1; inner < vSortedSize; inner++) {
                DataRange innerDataRange = vSorted.get(inner);

                if(!bitSet.get(inner)) {
                    if(outerDataRange.getStartColIndex() == innerDataRange.getStartColIndex() &&
                            outerDataRange.getEndColIndex() == innerDataRange.getEndColIndex() && (
                            innerDataRange.getStartRowIndex() <= outerDataRange.getEndRowIndex() ||
                                    innerDataRange.getStartRowIndex() - outerDataRange.getEndRowIndex() == 1)) {
                        bitSet.set(inner);
                        outerDataRange.setEndRowIndex(innerDataRange.getEndRowIndex());
                    }
                }
            }
            if(!bitSet.get(outer)) { vMerged.add(outerDataRange); }
        }

        return vMerged;
    }

    public static DataRange copyOf(DataRange dataRange) {
        return ChartUtils.getDataRange(dataRange.getAssociatedSheetName(), dataRange.getStartRowIndex(),
                dataRange.getStartColIndex(), dataRange.getEndRowIndex(), dataRange.getEndColIndex());
    }

    public static DataRange getDataSourceAsDataRange(Workbook workbook, String dataSource) {
        ChartActivityMonitor.ChartActivity chartActivity = ObjectsSupplier.getInstance()
                .getChartActivityMonitor().getChartActivity("DATASOURCE_TO_DATA_RANGE", null, null, null, null);            // NO I18N
        try {
            ReferencePool referencePool = workbook.getChartsContainer().getReferencePool();
            Reference reference = referencePool.getReference(dataSource);
            if (reference.getReferenceType() == ReferenceType.INVALID) {
                reference = ReferenceUtils.getReference(workbook, dataSource, true);
            }
            if (reference instanceof RangeReference) {
                RangeReference rangeReference = (RangeReference) reference;
                return rangeReference.getDataRange(workbook);
            }
            if (reference instanceof RangeExpressionReference) {
                RangeExpressionReference rangeExpressionReference = (RangeExpressionReference) reference;
                return rangeExpressionReference.getAsRangeReference().getDataRange(workbook);
            }
        } catch (Exception e) {
            chartActivity.failed(e);
        }
        return null;
    }

    public static List<DataRange> getDataSourcesAsDataRanges(Workbook workbook, List<String> dataSources) {
        List<DataRange> dataSourcesAsDataRanges = new ArrayList<>();
        for(String dataSource : dataSources) {
            DataRange dataRange = getDataSourceAsDataRange(workbook, dataSource);
            if(dataRange != null) { dataSourcesAsDataRanges.add(dataRange); }
        }

        return dataSourcesAsDataRanges;
    }

    public static Cell getDummyCell(Workbook workbook) {
        Cell dummyCell = new CellImpl();
        dummyCell.setRow(new Row(workbook.getSheet(0), 0));
        dummyCell.setColumn(new Column(workbook.getSheet(0), 0));

        return dummyCell;
    }

    public static boolean dataTypeCanBeLabel(DataType dataType) {
        return!(dataType == DataType.NUMBER || dataType == DataType.URL || dataType == DataType.IMAGE_URL);
    }

    public static boolean chartTypeCanHaveLabel(ChartType chartType, DataType dataType) {
        return chartType != ChartType.WORD_CLOUD && dataTypeCanBeLabel(dataType);
    }

    public static String addSheetName(Workbook workbook, String asn, String reference) {
        if(!reference.contains(".")) {          // NO I18N
            return String.format("%s.%s", CellUtil.getSheetNameEnclosed(workbook.getSheetByAssociatedName(asn).getName()), reference);      // NO I18N
        }
        return reference;
    }

    public static String getFormulaText(Workbook workbook, Node node) {
        ZSPrintVisitor printVisitor = new ZSPrintVisitor(ZSPrintVisitor.FORMULA_CLIENT.ZS_CLIENT);

        printVisitor.setConvertStructuredReferencesToRange(true);
        return FormulaUtil.getFormula(printVisitor, node, workbook,
                DataUtils.getDummyCell(workbook), 0, 0, false);
    }

    public static NamedExpression getNamedExpression(Workbook workbook, String reference) {
        Tuple<String, String> sheetAndName = getSheetAndName(workbook, reference);

        return workbook.getNamedExpression(sheetAndName.getSecondParam(), sheetAndName.getFirstParam());
    }

    public static Tuple<String, String> getSheetAndName(Workbook workbook, String reference) {
        if(reference.contains(".")) {                                   // NO I18N
            String[] split = reference.split("\\.");            // NO I18N
            if(split.length < 2) { return Tuple.of(null, reference); }
            String sheetName = split[0].startsWith("'") ? split[0].substring(1, split[0].length() - 1) : split[0];
            Sheet sheet = workbook.getSheet(sheetName);
            String asn = sheet == null ? null : sheet.getAssociatedName();
            return Tuple.of(asn, split[1]);
        }
        return Tuple.of(null, reference);
    }

    public static List<String> getValidDataRanges(Workbook workbook, String dataRanges, String sheetName) {
        if(dataRanges == null) { return Collections.emptyList(); }
        List<String> dataRangesList = CollectionsUtils.listOf(dataRanges.split(";"));
        String asn = workbook.getSheet(sheetName).getAssociatedName();
        return dataRangesList.stream()
                .map(dataRange -> {
                    if(workbook.getDataPivotTable(dataRange) != null) {
                        return dataRange;
                    } else {
                        Reference reference = ReferenceUtils.getReference(workbook, dataRange, true, asn);

                        if(reference.getReferenceType() != ReferenceType.INVALID) {
                            return reference.getReferenceName(workbook);
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static DataTableCell getNumericSampleCell(SpreadsheetSettings spreadsheetSettings) {
        return new DataTableCell(Value.ZERO_VALUE, Cell.Type.FLOAT, DataStyleConstants.EMPTY_PATTERN, spreadsheetSettings.getLocale(), spreadsheetSettings);
    }

}
