package com.zoho.sheet.knitcharts.utils;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.ImmutableEntry;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.ChartsInteger;

import java.util.*;
import java.util.function.Consumer;

public class CollectionsUtils {

    /**
     * Creates an unmodifiable list of given values
     * @param values Values
     * @return list of given values
     * @param <T> Type
     */
    @SafeVarargs
    public static <T> List<T> listOf(T ...values) {
        List<T> list = new ArrayList<>();
        Collections.addAll(list, values);

        return Collections.unmodifiableList(list);
    }

    /**
     * Method to convert the strings into list of strings
     * @param values Strings
     * @return list of strings
     */
    public static List<String> listOfStrings(String... values) {
        List<String> list = new ArrayList<>();
        Collections.addAll(list, values);

        return Collections.unmodifiableList(list);
    }

    /**
     * Creates an unmodifiable set of given values. This method uses HashSet.
     * @param values Values
     * @return set of given values
     * @param <T> Type
     */
    @SafeVarargs
    public static <T> Set<T> setOf(T ...values) {
        Set<T> set = new HashSet<>();
        Collections.addAll(set, values);

        return Collections.unmodifiableSet(set);
    }

    /**
     * Creates an immutable Map.Entry with the given key value pair
     * @param key Key
     * @param value Value
     * @return Map.Entry
     * @param <K>
     * @param <V>
     */
    public static <K, V> Map.Entry<K, V> mapEntry(K key, V value) {
        return new ImmutableEntry<>(key, value);
    }

    /**
     * Creates an unmodifiable map with the given Map.Entries
     * @param entries Map entries
     * @return Unmodifiable map
     * @param <K>
     * @param <V>
     */
    @SafeVarargs
    public static <K, V> Map<K, V> mapOf(Map.Entry<K, V> ...entries) {
        Map<K, V> map = new HashMap<>();
        for(Map.Entry<K, V> entry : entries) {
            map.put(entry.getKey(), entry.getValue());
        }

        return Collections.unmodifiableMap(map);
    }

    public static <T> Iterable<T> toIterable(Iterator<T> iterator) {
        return () -> iterator;
    }

    public static Iterable<JSONObjectWrapper> toJSONIterable(JSONArrayWrapper wrapper) {
        return new ChartsJSONArrayIterable<>(wrapper);
    }

    public static class ChartsJSONArrayIterable<T> implements Iterable<T> {

        private final JSONArrayWrapper wrapper;

        public ChartsJSONArrayIterable(JSONArrayWrapper wrapper) {
            this.wrapper = wrapper;
        }

        @Override
        public Iterator<T> iterator() {
            return new ChartsJSONArrayIterator<>(wrapper);
        }
    }

    public static class ChartsJSONArrayIterator<T> implements Iterator<T> {

        public final Iterator<?> iterator;

        public ChartsJSONArrayIterator(JSONArrayWrapper wrapper) {
            this.iterator = wrapper.iterator();
        }

        @Override
        public boolean hasNext() {
            return iterator.hasNext();
        }

        @Override
        public T next() {
            return ChartUtils.typeCast(iterator.next());
        }
    }

    public static class ChartsIntegerIterator implements Iterator<Integer> {

        private final Iterator<ChartsInteger> iterator;

        public ChartsIntegerIterator(Collection<ChartsInteger> list) {
            this.iterator = list.iterator();
        }

        @Override
        public boolean hasNext() {
            return iterator.hasNext();
        }

        @Override
        public Integer next() {
            ChartsInteger next = iterator.next();
            return next.getValue();
        }
    }

    /**
     * Method to convert the given collection into immutable list
     * @param collection Collection instance
     * @return Immutable list form of the given collection instance
     * @param <T> Value's type
     */
    public static <T> List<T> toList(Collection<T> collection) {
        if(collection == null) { return null; }
        return Collections.unmodifiableList(new ArrayList<>(collection));
    }

}
