package com.zoho.sheet.knitcharts.utils;

import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.connection.ZSChromeHeadlessConnection;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.constants.ChartConstants;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.pojo.DocumentSheetPOJO;
import com.zoho.sheet.knitcharts.pojo.PublicChartPOJO;
import com.zoho.sheet.knitcharts.chartcache.ChartCacheInterceptor;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsDAO;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTablePool;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ChartException;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.storefactoryinterceptor.ChartStoreFactoryInterceptor;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.PublicChartType;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ContainerSynchronizer;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.DynamicCDNUtils;
import com.zoho.sheet.util.FontUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;

/**
 * Chart Publish related Utility function
 */
public final class PublicChartUtils {

    /**
     * Method to check whether the given chart ID is published or not
     * @param chartID CHART ID
     * @param workbook Workbook instance
     * @return true if the given chart is published or false
     */
    public static boolean isChartPublic(String chartID, Workbook workbook) {
        ChartContainer chartContainer = workbook.getChartsContainer();
        Chart chart = chartContainer.getChart(chartID);

        return SheetChartAPI.isChartPublic(chart.getSheetMeta());
    }

    public static String getPublicSpace(PublicChartType type, String documentOwner) {
        if(type == PublicChartType.EXTERNAL) {
            return Constants.PUBLIC_SPACE;
        } else {
            return String.format("%s%s", Constants.CORPORATEDBPREFIX, DocumentUtils.getZOID(documentOwner));    // NO I18N
        }
    }

    public static String getPublicChartName(String chartID, Workbook workbook) {
        ChartContainer chartContainer = workbook.getChartsContainer();
        Chart chart = chartContainer.getChart(chartID);

        return SheetChartAPI.getPublicChartName(chart.getSheetMeta());
    }

    public static void doFollowUpPublishTasks(Workbook workbook, String chartID, String publicSpaceChartID, String publicSpace) {
        ExecutorService executorService = Executors.newFixedThreadPool(2);
        ChartContainer chartContainer = workbook.getChartsContainer();
        Chart chart = chartContainer.getChart(chartID);
        ChartType chartType = FrameworkChartAPI.getChartType(chart.getApiMeta().getChartMeta());

        int height = SheetChartAPI.getHeightOrDefault(chart.getSheetMeta(), chartType).intValue();
        int width = SheetChartAPI.getWidthOrDefault(chart.getSheetMeta(), chartType).intValue();

        JSONObjectWrapper chartMetaJSON = constructChartDetails(workbook, ChartsDataGenerator.getChartMeta(chart, workbook, new DataTablePool()));

        executorService.submit(() -> ChartCacheInterceptor.cachePublicChartDetails(chartMetaJSON, chartID));
        executorService.submit(() -> saveChartAsImage(chartMetaJSON, publicSpaceChartID, publicSpace, height, width, chartID));
    }

    public static void regeneratePublishChartCache(Workbook workbook, String chartID, String publicSpaceChartID, String publicSpace) {
        ChartContainer chartContainer = workbook.getChartsContainer();
        Chart chart = chartContainer.getChart(chartID);

        JSONObjectWrapper chartMetaJSON = ChartsDataGenerator.getChartMeta(chart, workbook, new DataTablePool());
        JSONObjectWrapper chartDetails = constructChartDetails(workbook, chartMetaJSON);

        ChartCacheInterceptor.cachePublicChartDetails(chartDetails, chartID);
    }

    private static JSONObjectWrapper constructChartDetails(Workbook workbook, JSONObjectWrapper chartMeta) {
        JSONObjectWrapper chartDetails = new JSONObjectWrapper();
        Objects.requireNonNull(chartMeta);

        chartDetails.put(ChartConstants.PublicChartConstants.COLORS, ChartUtils.getAccentColorsTheme(workbook));
        chartDetails.put(ChartConstants.PublicChartConstants.FONT, ChartUtils.getChartOrThemeBodyFontName(chartMeta, workbook));
        chartDetails.put(ChartConstants.PublicChartConstants.CHART_META, chartMeta);

        return chartDetails;
    }

    public static void regeneratePublishChartImage(Workbook workbook, String chartID, String publicSpaceChartID, String publicSpace) {
        ChartContainer chartContainer = workbook.getChartsContainer();
        Chart chart = chartContainer.getChart(chartID);
        ChartType chartType = FrameworkChartAPI.getChartType(chart.getApiMeta().getChartMeta());

        int height = SheetChartAPI.getHeightOrDefault(chart.getSheetMeta(), chartType).intValue();
        int width = SheetChartAPI.getWidthOrDefault(chart.getSheetMeta(), chartType).intValue();

        JSONObjectWrapper chartMetaJSON = constructChartDetails(workbook, ChartsDataGenerator.getChartMeta(chart, workbook, new DataTablePool()));
        Objects.requireNonNull(chartMetaJSON);

        saveChartAsImage(chartMetaJSON, publicSpaceChartID, publicSpace, height, width, chartID);
    }

    private static void saveChartAsImage(JSONObjectWrapper chartMetaJSON, String publicSpaceChartID, String publicSpace,
                                         int height, int width, String chartID) {
        byte[] imageBytes = convertChartToImageBytes(chartMetaJSON, height, width, null,
                ChartUtils.optFromJSONObject(chartMetaJSON, ChartConstants.PublicChartConstants.FONT, ""));         // NO I18N

        if(imageBytes != null && imageBytes.length > 0) {
            ChartStoreFactoryInterceptor.saveImageToStore(imageBytes, publicSpace, publicSpaceChartID);
        }
    }

    public static void doFollowUpUnPublishTasks(String publicSpace, String publicSpaceChartID, String chartID) {
        ChartCacheInterceptor.clearPublicChartDetails(chartID);
        ChartStoreFactoryInterceptor.deleteImageFromStore(publicSpace, publicSpaceChartID);
    }

    public static void regenerateRedisCache(HttpServletRequest request, PublicChartPOJO bean) {
        try {
            ChartsDAO chartsDAO = ChartsDAO.getDAOWithDocOwner(bean.getUserName());
            String serverURL = getServerURL(request);
            JSONObjectWrapper payload = new JSONObjectWrapper();

            DocumentSheetPOJO documentSheetPOJO = chartsDAO.getDocumetDetailsByChartID(bean.getChartID());
            payload.put(ChartActionConstants.JSONConstants.ACTION, ActionConstants.PUBLISH_CHART_REGENERATE);
            payload.put(ChartActionConstants.JSONConstants.PROXY_URL, ChartConstants.PublicChartConstants.INTERNAL_SERVER_CALL_URL);
            payload.put(ChartActionConstants.JSONConstants.RID, documentSheetPOJO.getResourceID());
            payload.put(ChartActionConstants.JSONConstants.SHEET_NAME, documentSheetPOJO.getSheetName());
            payload.put(ChartActionConstants.JSONConstants.CHART_ID, bean.getChartID());
            payload.put(ChartActionConstants.JSONConstants.PUBLIC_SPACE_CHART_ID, bean.getPublicSpaceChartID());
            payload.put(ChartActionConstants.JSONConstants.PUBLIC_SPACE, bean.getPublicSpace());
            payload.put(ChartActionConstants.JSONConstants.MODE, ChartActionConstants.Constants.INTERACTIVE);
            payload.put(ChartActionConstants.JSONConstants.ISC_SIGNATURE, SecurityUtil.sign());

            ContainerSynchronizer.postURLConnection(serverURL, documentSheetPOJO.getResourceID(),payload.getJsonObject());
        } catch (Exception e) {
            throw new ChartException(e.getMessage());
        }
    }

    public static String getServerURL(HttpServletRequest request) {
        if(request == null) {
            return String.format("%s/sheet/", EnginePropertyUtil.getSheetPropertyValue("ZohoSheetURL"));		// NO I18N
        } else {
            return ClientUtils.getServerURL(request, false, null, false, true);
        }
    }

    public static void regenerateImage(HttpServletRequest request, PublicChartPOJO bean) {
        try {
            ChartsDAO chartsDAO = ChartsDAO.getDAOWithDocOwner(bean.getUserName());
            String serverURL = getServerURL(request);
            JSONObjectWrapper payload = new JSONObjectWrapper();

            DocumentSheetPOJO documentSheetPOJO = chartsDAO.getDocumetDetailsByChartID(bean.getChartID());
            payload.put(ChartActionConstants.JSONConstants.ACTION, ActionConstants.PUBLISH_CHART_REGENERATE);
            payload.put(ChartActionConstants.JSONConstants.PROXY_URL, ChartConstants.PublicChartConstants.INTERNAL_SERVER_CALL_URL);
            payload.put(ChartActionConstants.JSONConstants.RID, documentSheetPOJO.getResourceID());
            payload.put(ChartActionConstants.JSONConstants.SHEET_NAME, documentSheetPOJO.getSheetName());
            payload.put(ChartActionConstants.JSONConstants.CHART_ID, bean.getChartID());
            payload.put(ChartActionConstants.JSONConstants.PUBLIC_SPACE_CHART_ID, bean.getPublicSpaceChartID());
            payload.put(ChartActionConstants.JSONConstants.PUBLIC_SPACE, bean.getPublicSpace());
            payload.put(ChartActionConstants.JSONConstants.MODE, ChartActionConstants.Constants.IMAGE);
            payload.put(ChartActionConstants.JSONConstants.ISC_SIGNATURE, SecurityUtil.sign());

            ContainerSynchronizer.postURLConnection(serverURL, documentSheetPOJO.getResourceID(), payload.getJsonObject());
        } catch (Exception e) {
            throw new ChartException(e.getMessage());
        }
    }

    public static void handleChartModification(Workbook workbook, String chartID, String publicSpaceChartID, String publicSpace) {
        ChartActivityMonitor.ChartActivity activity = ObjectsSupplier.getInstance().getChartActivityMonitor().getChartActivity(
                "Regenerate public chart cache", null, null, null, chartID);                // NO I18N
        try {
            ExecutorService executorService = Executors.newFixedThreadPool(1);

            executorService.submit(() -> regeneratePublishChartCache(workbook, chartID, publicSpaceChartID, publicSpace));
        } catch (Exception e) {
            activity.failed(e);
        }
    }

    public static JSONObjectWrapper getChartMetaForPublicUse(Workbook workbook, String chartID) {
        ChartContainer chartContainer = workbook.getChartsContainer();
        Chart chart = chartContainer.getChart(chartID);

        return getChartMetaForPublicUse(workbook, chart);
    }

    public static JSONObjectWrapper getChartMetaForPublicUse(Workbook workbook, Chart chart) {
        JSONObjectWrapper chartMetaJSON = ChartsDataGenerator.getChartMeta(chart, workbook, new DataTablePool());
        return constructChartDetails(workbook, chartMetaJSON);
    }

    public static byte[] getChartImageForPublicUse(Workbook workbook, String chartID) {
        ChartContainer chartContainer = workbook.getChartsContainer();
        Chart chart = chartContainer.getChart(chartID);
        ChartType chartType = FrameworkChartAPI.getChartType(chart.getApiMeta().getChartMeta());

        int height = SheetChartAPI.getHeightOrDefault(chart.getSheetMeta(), chartType).intValue();
        int width = SheetChartAPI.getWidthOrDefault(chart.getSheetMeta(), chartType).intValue();

        JSONObjectWrapper chartMetaJSON = getChartMetaForPublicUse(workbook, chartID);

        byte[] imageBytes = convertChartToImageBytes(chartMetaJSON, height, width, null,
                ChartUtils.optFromJSONObject(chartMetaJSON, ChartConstants.PublicChartConstants.FONT, ""));         // NO I18N)

        if(imageBytes != null && imageBytes.length > 0) {
            return imageBytes;
        }

        return null;
    }

    private static String appendHTTPS(String url) {
        if(url.startsWith("//")) {
            return String.format("https:%s", url);          // NO I18N
        } else {
            return String.format("https://%s", url);          // NO I18N
        }
    }

    /**
     * Chart to Image Conversion util used by new charts
     * @param chartMetaJSON ChartMeta in JSON
     * @param height Height
     * @param width Width
     * @param request Request
     * @return Chart as image bytes
     */
    public static byte[] convertChartToImageBytes(JSONObjectWrapper chartMetaJSON, int height, int width, HttpServletRequest request, String fontFamily) {
        ChartActivityMonitor.ChartActivity activity = ObjectsSupplier.getInstance().getChartActivityMonitor().getChartActivity(
                "[CONVERT_CHART_INTO_IMAGE]", null, null, null, null            // NO I18N
        );
        try {
//            String sheetURL = "hari-17992.csez.zohocorpin.com:8443";

            String fontServerUrl = EnginePropertyUtil.getSheetPropertyValue("WEBFONTS_STATIC_SERVER_URL");                          // NO I18N

            String securityPath = "https://" + DynamicCDNUtils.getDynamicCDN(request) + "/zohosecurity/v4/js/";                     // NO I18N
            String chartsNodeFile =  appendHTTPS(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.UNCOMPRESSED_JS, "Chart/node/charts.js"));                    // NO I18N
            String vendorsNodeFile = appendHTTPS(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.UNCOMPRESSED_JS, "Chart/node/vendors.js"));                   // NO I18N
            String knitChartsFile = appendHTTPS(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_JS, "knitchart_compressed.js"));                      // NO I18N
            String stylesFile = appendHTTPS(ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_CSS, "defer/charts.css"));        // NO I18N
            String zohoChartsDeps = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_JS, "appsheet/compressedJS/ZohoChartsDeps.js");              // NO I18N
            String highChartsDeps = ClientUtils.getPayloadLink(request, ClientUtils.ResourceType.APPSHEET_JS, "appsheet/compressedJS/HighChartsDeps.js");              // NO I18N
            String security_min = securityPath + "security.min.js";                                                                 // NO I18N

            ArrayList<String> fontList = new ArrayList<>();
            fontList.add(fontFamily);
            String webFontCSS = FontUtils.getDefaultFontFaceCSS(fontList);

            StringBuilder chartHTML = new StringBuilder();
            chartHTML.append("<html><head>");                                                                                       // NO I18N
            chartHTML.append("<style type='text/css'>");                                                                            // NO I18N
            chartHTML.append(webFontCSS);
            chartHTML.append("body{margin:0px; padding:0px}");                                                                      // NO I18N
            chartHTML.append("@font-face { font-family:'Lato'; font-weight:400; font-style:normal; src:url('https://").append(fontServerUrl).append("/latoregular/font_latin.woff') format('woff'); unicode-range: U+20-7E, U+A0-FF, U+131, U+152-153, U+2C6, U+2DA, U+2DC, U+2013-2014, U+2018-201A, U+201C-201E, U+2020-2022, U+2026, U+2030, U+2039-203A, U+2044, U+20AC, U+2212;}");                // NO I18N
            chartHTML.append("@font-face { font-family:'Lato'; font-weight:400; font-style:normal; src:url('https://").append(fontServerUrl).append("/latoregular/font_latin_ext.woff') format('woff'); unicode-range: U+104-107, U+118-119, U+141-144, U+15A-15B, U+160-161, U+178-17E, U+192;}");                                                                                                     // NO I18N
            chartHTML.append("@font-face { font-family:'Lato';font-weight:400;font-style:normal;src:url('https://").append(fontServerUrl).append("/latoregular/font_others.woff') format('woff');unicode-range: U+2C7, U+2C9, U+2D8-2D9, U+2DB, U+2DD, U+3C0, U+2122, U+2126, U+2202, U+2206, U+220F, U+2211, U+221A, U+221E, U+222B, U+2248, U+2260, U+2264-2265, U+25CA, U+2669, U+FB01-FB02;}");     // NO I18N
            chartHTML.append("@font-face {font-family:'Lato';font-weight:700;font-style:normal;src:url('https://").append(fontServerUrl).append("/latobold/font_latin.woff') format('woff');unicode-range: U+20-7E, U+A0-FF, U+131, U+152-153, U+2C6, U+2DA, U+2DC, U+2013-2014, U+2018-201A, U+201C-201E, U+2020-2022, U+2026, U+2030, U+2039-203A, U+2044, U+20AC, U+2212;}");                        // NO I18N
            chartHTML.append("@font-face {font-family:'Lato';font-weight:700;font-style:normal;src:url('https://").append(fontServerUrl).append("/latobold/font_latin_ext.woff') format('woff');unicode-range: U+104-107, U+118-119, U+141-144, U+15A-15B, U+160-161, U+178-17E, U+192;}");                                                                                                             // NO I18N
            chartHTML.append("@font-face {font-family:'Lato';font-weight:700;font-style:normal;src:url('https://").append(fontServerUrl).append("/latobold/font_others.woff') format('woff');unicode-range: U+2C7, U+2C9, U+2D8-2D9, U+2DB, U+2DD, U+3C0, U+2122, U+2126, U+2202, U+2206, U+220F, U+2211, U+221A, U+221E, U+222B, U+2248, U+2260, U+2264-2265, U+25CA, U+2669, U+FB01-FB02;}");         // NO I18N
            chartHTML.append("</style>");                                                                                           // NO I18N

            chartHTML.append("<link rel=\"stylesheet\" href='").append(stylesFile).append("'></link>");
            chartHTML.append("<script  type=\"text/javascript\" src='").append(security_min).append("'></script>");                 // NO I18N
            chartHTML.append("<script  type=\"text/javascript\" src='").append(chartsNodeFile).append("'></script>");                  // NO I18N
            chartHTML.append("<script  type=\"text/javascript\" src='").append(zohoChartsDeps).append("'></script>");                  // NO I18N
            chartHTML.append("<script  type=\"text/javascript\" src='").append(highChartsDeps).append("'></script>");                  // NO I18N
            chartHTML.append("<script  type=\"text/javascript\" src='").append(vendorsNodeFile).append("'></script>");                  // NO I18N
            chartHTML.append("<script  type=\"text/javascript\" src='").append(knitChartsFile).append("'></script>");                  // NO I18N
            chartHTML.append("</head><body ");                                                         // NO I18N
            chartHTML.append(String.format("style=\"height: %dpx; width: %dpx\">",height, width));                         // NO I18N
            chartHTML.append("<div id=\"chartContainer\" ");                                                        // NO I18N
            chartHTML.append("style=\"position:absolute;height: 100%; width: 100%\"></div>");                         // NO I18N
            chartHTML.append("<script type='text/javascript'>");                                                                    // NO I18N
            chartHTML.append("var chartJSON = ").append(chartMetaJSON).append(";");                                                 // NO I18N
            chartHTML.append("addEventListener(\"load\", () => { this.renderChart(chartJSON, \"chartContainer\", false); });");                                                 // NO I18N
            chartHTML.append("setTimeout(function() {window.close();}, 5000 * 60 );");                                              // NO I18N
            chartHTML.append("</script></body></html>");                                                               // NO I18N

            ZSChromeHeadlessConnection chromeConnection = new ZSChromeHeadlessConnection();

            return chromeConnection.convertHtmlToImage(chartHTML.toString(), width, height);

        } catch (Exception e) {
            activity.failed(e);
            return null;
        }
    }

}
