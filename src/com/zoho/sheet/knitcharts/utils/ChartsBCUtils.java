package com.zoho.sheet.knitcharts.utils;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.zoho.sheet.chart.*;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.*;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.PivotChartSheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.*;
import com.zoho.sheet.knitcharts.chartsdatatable.*;
import com.zoho.sheet.knitcharts.colorthemes.ColorThemeBuilder;
import com.zoho.sheet.knitcharts.colorthemes.ColorThemeBuilderUtils;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.constants.ChartsBCConstants;
import com.zoho.sheet.knitcharts.backwardconverter.decomissioned.ChartConverter;
import com.zoho.sheet.knitcharts.backwardconverter.decomissioned.Converter;
import com.zoho.sheet.knitcharts.constants.SheetChartActionConstants;
import com.zoho.sheet.util.ColorUtil;

import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ChartsBCUtils {

    private static final Logger LOGGER = Logger.getLogger(ChartsBCUtils.class.getName());

    public static boolean isNewChartOptions (JSONObjectWrapper chartOptions) {
        return chartOptions.has(ChartsBCConstants.JSONConstants.NEW_CHART_OPTIONS);
    }

    @Deprecated
    public static Converter getConverter(Workbook workbook) {
        return new ChartConverter(workbook);
    }

    public static String getColorFromChartJSON(JSONObjectWrapper chartJSON, String key) {
        if(chartJSON == null || !chartJSON.has(ChartActionConstants.JSONConstants.VALUE)) { return null; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        JSONObjectWrapper colorJSON = ChartUtils.optFromJSONObject(valueJSON, key, new JSONObjectWrapper());

        return ChartUtils.optFromJSONObject(colorJSON, SheetChartActionConstants.JSONConstants.VALUE);
    }

    public static JSONObjectWrapper getPropsJSONFromChartJSON(ZSTheme theme, JSONObjectWrapper chartJSON, String key, String defaultColor) {
        if(chartJSON == null || !chartJSON.has(ChartActionConstants.JSONConstants.VALUE)) { return null; }
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        JSONObjectWrapper colorJSON = ChartUtils.optFromJSONObject(valueJSON, key, new JSONObjectWrapper());
        String color = ChartUtils.optFromJSONObject(colorJSON, SheetChartActionConstants.JSONConstants.VALUE);
        String accent = ChartUtils.optFromJSONObject(colorJSON, SheetChartActionConstants.JSONConstants.ACCENT);
        Double tone = ChartUtils.optDoubleFromJSONObject(colorJSON, SheetChartActionConstants.JSONConstants.TONE);

        return getColorPropsTemplate(theme, color, accent, tone, defaultColor);
    }

    public static int getSeriesCount(Chart chart) {
        String dataJoinType = chart.getCombineRange();
        String seriesIn = chart.getSeries();
        List<ZArrayI> ranges = chart.getDataRange()
                .stream().map(range -> (ZArrayI) range).collect(Collectors.toList());

        StackedZArray stackedZArray = new StackedZArray(ranges, Objects.equals(dataJoinType, "VERTICAL")                // NO I18N
                ? StackedZArray.Type.V_STACK : StackedZArray.Type.H_STACK);

        if(Objects.equals(seriesIn, "ROWS")) {      // NO I18N
            return stackedZArray.getRowSize();
        } else {
            return stackedZArray.getColSize();
        }
    }

    public static Chart getChartFromChartJSON(Workbook workbook, JSONObjectWrapper chartJSON) {
        String chartID = chartJSON.optString(ChartActionConstants.JSONConstants.CHART_ID, "");      // NO I18N
        Map<String, Map<String, Chart>> chartMap = workbook.getChartMap();

        for(Map.Entry<String, Map<String, Chart>> entry : chartMap.entrySet()) {
            for(Map.Entry<String, Chart> chartEntry : entry.getValue().entrySet()) {
                if(chartEntry.getKey().equals(chartID)) {
                    return chartEntry.getValue();
                }
            }
        }
        return null;
    }

    public static String getReferenceStringWithoutEqualTo(String str) {
        return str.replaceFirst("=", "");
    }

    private static String getValidDataRange(Workbook workbook, List<String> dataRanges) {
        boolean isValidDataRanges = true;
        for(String dataRange: dataRanges) {
            try {
                new Range(workbook, dataRange, null, CellReference.ReferenceMode.A1, false);
            } catch (Exception e) {
                isValidDataRanges = false;
            }
        }
        return isValidDataRanges ? ChartUtils.join(dataRanges, ";") : "";          // NO I18N
    }

    public static String convertToOldChartType(String chartType) {
        try {
            ChartType nChartType = ChartType.retrieveByValue(chartType);
            return getOldChartNameForChartTypeOrNull(nChartType);
        } catch (Exception e) {
            return null;
        }
    }

    private static Map<ChartType, String> newToOldChartTypeMap;

    public static String getOldChartNameForChartTypeOrNull(ChartType chartType) {
        if(newToOldChartTypeMap == null) {
            newToOldChartTypeMap = Collections.unmodifiableMap(new HashMap<>() {{
                put(ChartType.COLUMN, "COL");                                            // NO I18N
                put(ChartType.STACKED_COLUMN, "STACKEDCOL");                             // NO I18N
                put(ChartType.STACKED_PERCENT_COLUMN, "STACKEDCOL_PERCENT");             // NO I18N
                put(ChartType.GROUPED_COLUMN, "GROUPEDCOL");                             // NO I18N
                put(ChartType.BAR, "BAR");                                               // NO I18N
                put(ChartType.STACKED_BAR, "STACKEDBAR");                                // NO I18N
                put(ChartType.STACKED_PERCENT_BAR, "STACKEDBAR_PERCENT");                // NO I18N
                put(ChartType.GROUPED_BAR, "GROUPEDBAR");                                // NO I18N
                put(ChartType.RACE_BAR, "ANIMATION_CHART");                              // NO I18N
                put(ChartType.AREA, "XYAREA");                                           // NO I18N
                put(ChartType.AREA_SPLINE, "XYAREA_AREA_SPLINE");                        // NO I18N
                put(ChartType.STACKED_AREA, "XYSTACKEDAREA");                            // NO I18N
                put(ChartType.STACKED_PERCENT_AREA, "XYSTACKEDAREA_PERCENT");            // NO I18N
                put(ChartType.AREA_TIME_SERIES, "TIMEAREA");                             // NO I18N
                put(ChartType.PIE, "PIE");                                               // NO I18N
                put(ChartType.SEMI_PIE, "SEMIPIE");                                      // NO I18N
                put(ChartType.DONUT, "DOUGHNUT");                                        // NO I18N
                put(ChartType.SEMI_DONUT, "SEMIDOUGHNUT");                               // NO I18N
                put(ChartType.LINE, "XYLINE");                                           // NO I18N
                put(ChartType.SPLINE, "SPLINE");                                         // NO I18N
                put(ChartType.STEP_LINE, "STEPCHART");                                   // NO I18N
                put(ChartType.LINE_TIME_SERIES, "TIMELINE");                             // NO I18N
                put(ChartType.SCATTER, "SCATTER");                                       // NO I18N
                put(ChartType.SCATTER_LINE, "XYLINE_SCATTER");                           // NO I18N
                put(ChartType.SCATTER_LINE_MARKERS, "XYLINE_SCATTER_SHAPES");            // NO I18N
                put(ChartType.BULLET_COLUMN, "BULLETCOL");                               // NO I18N
                put(ChartType.BULLET_BAR, "BULLETBAR");                                  // NO I18N
                put(ChartType.FUNNEL, "FUNNEL");                                         // NO I18N
                put(ChartType.WEIGHTED_FUNNEL, "WEIGHTED_FUNNEL");                       // NO I18N
                put(ChartType.BUBBLE, "BUBBLECHART");                                    // NO I18N
                put(ChartType.HISTOGRAM, "HISTOGRAM");                                   // NO I18N
                put(ChartType.WORD_CLOUD, "WORDCLOUD");                                  // NO I18N
                put(ChartType.COMBO, "COMBOCHART");                                      // NO I18N
                put(ChartType.DONUT_PARLIAMENT, "ITEM");                                 // NO I18N
                put(ChartType.PIE_PARLIAMENT, "PIE_ITEM");                               // NO I18N
                put(ChartType.CANDLE_STICK, "CANDLESTICK");                              // NO I18N
                put(ChartType.OHLC, "OHLCCHART");                                        // NO I18N
                put(ChartType.POLAR, "POLAR");                                           // NO I18N
                put(ChartType.SPIDER_WEB, "SPIDERWEB");                                  // NO I18N
                put(ChartType.PARETO, "PARETO");                                         // NO I18N
                put(ChartType.WATERFALL, "WATERFALL");                                   // NO I18N
                put(ChartType.BOXPLOT_COLUMN, "BOX_PLOT");                               // NO I18N
                put(ChartType.BOXPLOT_BAR, "BOX_PLOT_H");                                // NO I18N
            }});
        }
        return newToOldChartTypeMap.getOrDefault(chartType, null);
    }

    public static Chart getChart(Workbook workbook, String chartId, ChartMeta chartMeta,
                                 SheetMeta sheetMeta, String associatedSheetName, boolean isPivotChart,
                                 String pivotId, JSONObjectWrapper chartMetaInfo) {
        String chartType = ChartUtils.getOldChartNameForChartType(FrameworkChartGetterAPI.getChartType(chartMeta));
        String dataRange = isPivotChart ? null : getValidDataRange(workbook, SheetChartGetterAPI.getDataSources(sheetMeta));
        String seriesIn;

        if(FrameworkChartGetterAPI.getChartType(chartMeta) == ChartType.RACE_LINE
                || FrameworkChartGetterAPI.getChartType(chartMeta) == ChartType.RACE_BAR) {
            seriesIn = SheetChartGetterAPI.getSeriesInType(sheetMeta) == SeriesInType.ROWS ? "COLS" : "ROWS";         // NO I18N
        } else {
            seriesIn = SheetChartGetterAPI.getSeriesInType(sheetMeta) == SeriesInType.ROWS ? "ROWS" : "COLS";         // NO I18N
        }

        boolean firstRowAsLabel = SheetChartGetterAPI.getFirstRowLabel(sheetMeta);
        boolean firstColAsLabel = SheetChartGetterAPI.getFirstColLabel(sheetMeta);

        try {
            if (chartType.contains("BULLETCOL")) {
                return new HBulletChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("AREA_SPLINE")) {
                return new AreaSpline(workbook, chartId, chartType, "areaspline", associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId); //No I18N
            } else if (chartType.contains("BULLETBAR")) {
                return new VBulletChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("COL") || chartType.contains("BAR_Y") || chartType.contains("BAR3D_Y") || chartType.contains("STACKEDBAR_PERCENT_Y") || chartType.contains("STACKEDBAR3D_PERCENT_Y")) {
                return new ColumnChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("BAR") || chartType.contains("STACKEDBAR_PERCENT_X")) {
                return new BarChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("ANIMATION_CHART")) {
                return new AnimationChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("SCATTER")) {
                return new ScatterChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("SPLINE")) {
                return new SplineChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("XYLINE") || chartType.contains("STEPCHART") || chartType.contains("TIMELINE")) {
                return new LineChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("COMBOCHART")) {
                return new ComboChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("XYSTACKEDAREA") || chartType.contains("TIMEAREA") || chartType.contains("XYAREA")) {
                return new AreaChart(workbook, chartId, chartType, "area", associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId); //No I18N
            } else if (chartType.contains("ITEM")) {
                return new ItemChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("PIE") || chartType.contains("DOUGHNUT") || chartType.contains("SEMIDOUGHNUT")) {
                return new PieChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("BUBBLE")) {
                return new BubbleChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("CANDLESTICK")) {
                return new CandleStickChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("OHLCCHART")) {
                return new OHLCChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("SPIDERWEB")) {
                return new RadarChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.equals(ChartConstants.WATERFALL_CHART)) {
                return new WaterfallChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("POLAR")) {
                return new PolarChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("WEIGHTED_FUNNEL")) {
                return new WeightedFunnelChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("FUNNEL")) {
                return new FunnelChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("PARETO")) {
                return new ParetoChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("HISTOGRAM")) {
                return new HistogramChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains("WORDCLOUD")) {
                return new WordCloud(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            } else if (chartType.contains(ChartConstants.BOX_PLOT)) {
                return new BoxPlotChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
            }

            return new ColumnChart(workbook, chartId, chartType, associatedSheetName, dataRange, seriesIn, firstRowAsLabel, firstColAsLabel, chartMetaInfo, isPivotChart, pivotId);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }


    private static Map<ChartType, String> newToOldChartHighchartsTypeMap;

    public static String getOldHighchartsChartNameForChartType(ChartType chartType) {
        if(newToOldChartHighchartsTypeMap == null) {
            newToOldChartHighchartsTypeMap = Map.ofEntries(
                    Map.entry(ChartType.COLUMN, "column"),                                            // NO I18N
                    Map.entry(ChartType.STACKED_COLUMN, "column"),                             // NO I18N
                    Map.entry(ChartType.STACKED_PERCENT_COLUMN, "column"),             // NO I18N
                    Map.entry(ChartType.GROUPED_COLUMN, "column"),                             // NO I18N
                    Map.entry(ChartType.BAR, "bar"),                                               // NO I18N
                    Map.entry(ChartType.STACKED_BAR, "bar"),                                // NO I18N
                    Map.entry(ChartType.STACKED_PERCENT_BAR, "bar"),                // NO I18N
                    Map.entry(ChartType.GROUPED_BAR, "bar"),                                // NO I18N
                    Map.entry(ChartType.RACE_BAR, "bar"),                              // NO I18N
                    Map.entry(ChartType.AREA, "area"),                                           // NO I18N
                    Map.entry(ChartType.AREA_SPLINE, "area"),                        // NO I18N
                    Map.entry(ChartType.STACKED_AREA, "area"),                            // NO I18N
                    Map.entry(ChartType.STACKED_PERCENT_AREA, "area"),            // NO I18N
                    Map.entry(ChartType.AREA_TIME_SERIES, "area"),                             // NO I18N
                    Map.entry(ChartType.PIE, "pie"),                                               // NO I18N
                    Map.entry(ChartType.SEMI_PIE, "pie"),                                      // NO I18N
                    Map.entry(ChartType.DONUT, "pie"),                                        // NO I18N
                    Map.entry(ChartType.SEMI_DONUT, "pie"),                               // NO I18N
                    Map.entry(ChartType.LINE, "line"),                                           // NO I18N
                    Map.entry(ChartType.SPLINE, "line"),                                         // NO I18N
                    Map.entry(ChartType.STEP_LINE, "line"),                                   // NO I18N
                    Map.entry(ChartType.LINE_TIME_SERIES, "line"),                             // NO I18N
                    Map.entry(ChartType.SCATTER, "scatter"),                                       // NO I18N
                    Map.entry(ChartType.SCATTER_LINE, "scatter"),                           // NO I18N
                    Map.entry(ChartType.SCATTER_LINE_MARKERS, "scatter"),            // NO I18N
                    Map.entry(ChartType.BULLET_COLUMN, "column"),                               // NO I18N
                    Map.entry(ChartType.BULLET_BAR, "bar"),                                  // NO I18N
                    Map.entry(ChartType.FUNNEL, "funnel"),                                         // NO I18N
                    Map.entry(ChartType.WEIGHTED_FUNNEL, "columnrange"),                       // NO I18N
                    Map.entry(ChartType.BUBBLE, "bubble"),                                    // NO I18N
                    Map.entry(ChartType.HISTOGRAM, "histogram"),                                   // NO I18N
                    Map.entry(ChartType.WORD_CLOUD, "wordcloud"),                                  // NO I18N
                    Map.entry(ChartType.COMBO, "line"),                                      // NO I18N
                    Map.entry(ChartType.DONUT_PARLIAMENT, "item"),                                 // NO I18N
                    Map.entry(ChartType.PIE_PARLIAMENT, "item"),                               // NO I18N
                    Map.entry(ChartType.CANDLE_STICK, "candlestick"),                              // NO I18N
                    Map.entry(ChartType.OHLC, "ohlc"),                                        // NO I18N
                    Map.entry(ChartType.POLAR, "line"),                                           // NO I18N
                    Map.entry(ChartType.SPIDER_WEB, "line"),                                  // NO I18N
                    Map.entry(ChartType.PARETO, "pareto"),                                         // NO I18N
                    Map.entry(ChartType.WATERFALL, "waterfall"),                                   // NO I18N
                    Map.entry(ChartType.BOXPLOT_COLUMN, "boxplot"),                               // NO I18N
                    Map.entry(ChartType.BOXPLOT_BAR, "boxplot"));                                   // NO I18N
        }
        return newToOldChartHighchartsTypeMap.getOrDefault(chartType, "column");                                // NO I18N
    }

    @Deprecated
    public static void convertToOldChart(Workbook workbook, String chartID, JSONObjectWrapper chartOptions, String sheetName) {
        String asn = workbook.getSheet(sheetName).getAssociatedName();

        if(isNewChartOptions(chartOptions)) {
            JSONObjectWrapper newChartOptions = chartOptions.getJSONObject(ChartsBCConstants.JSONConstants.NEW_CHART_OPTIONS);
            JSONObjectWrapper chartMetaJSON = newChartOptions.getJSONObject(FrameworkChartMetaConstants.ChartMeta.KEY);
            JSONObjectWrapper sheetMetaJSON = newChartOptions.getJSONObject(ChartActionConstants.JSONConstants.SHEET_META);
            JSONObjectWrapper chartMetaInfo = new JSONObjectWrapper();
            String pivotID = getPivotID(workbook, sheetMetaJSON);
            boolean isPivotChart = pivotID != null;

            ChartMeta chartMeta = new ChartMeta();
            chartMeta.fromJSON(chartMetaJSON);

            SheetMeta sheetMeta = getSheetMeta(workbook, sheetMetaJSON);
            Converter converter = getConverter(workbook);
            converter.constructSkeleton(chartMetaInfo);

            Chart chart = getChart(workbook, chartID, chartMeta, sheetMeta, asn, isPivotChart, pivotID, chartMetaInfo);

            chart.constructChartOptions(workbook.getSheetByAssociatedName(asn), new JSONObjectWrapper());
            chart.checkAndCreateChartPretiffyObject(workbook).reset(chart, workbook);
            converter.convert(chart, chartMeta, sheetMeta);
            workbook.addChart(asn, chartID, chart);
            chart.setReGenRequired(false);
            chart.setModified(false);

        }
    }

    public static void updateEssentialParamsForInsert(Workbook workbook, Chart chart) {
        if(chart.getDataLabel() == null) {
            chart.setDataLabel("N");                // NO I18N
        }
    }

    public static String getPivotID(Workbook workbook, JSONObjectWrapper sheetMeta) {
        JSONArrayWrapper dataSources = sheetMeta.getJSONObject(SheetChartMetaConstants.DataTableOptions.KEY)
                .optJSONArray(SheetChartMetaConstants.DataTableOptions.DATA_SOURCES);
        if(dataSources != null) {
            if(dataSources.isEmpty()) {
                return null;
            }
            String pivotID = ChartUtils.base64Decode(dataSources.get(0).toString());
            if(pivotID != null && workbook.getDataPivotTable(pivotID) != null) {
                return pivotID;
            }
        }
        return null;
    }

    public static String getPivotID(Workbook workbook, SheetMeta sheetMeta) {
        List<String> dataSources = SheetChartGetterAPI.getDataSources(sheetMeta);
        if(dataSources == null || dataSources.isEmpty()) {
            return null;
        }
        String pivotID = dataSources.get(0);
        if(pivotID != null && workbook.getDataPivotTable(pivotID) != null) {
            return pivotID;
        }
        return null;
    }

    public static SheetMeta getSheetMeta(Workbook workbook, JSONObjectWrapper sheetMetaJSON) {
        String pivotID = getPivotID(workbook, sheetMetaJSON);
        SheetMeta sheetMeta = pivotID == null ? new SheetMeta(): new PivotChartSheetMeta();

        sheetMeta.fromJSON(sheetMetaJSON);
        return sheetMeta;
    }

    public static String parseNumber(Object value, char decimalSeparator) {
        if(!(value instanceof Number)){
            char[] stringValue = Objects.toString(value).toCharArray();
            StringBuilder builder = new StringBuilder();
            int idx = 0;

            if(stringValue[0] == '-') {
                builder.append('-');
                idx = 1;
            }

            for(;idx < stringValue.length; idx++) {
                boolean match = Pattern.compile("[0-9]").matcher(String.valueOf(stringValue[idx])).matches();
                if(match || stringValue[idx] == decimalSeparator) {
                    builder.append(stringValue[idx]);
                } else { break; }
            }

            return builder.toString();
        }

        return String.valueOf(value);
    }

    public static void updatePositionForClone(Chart orginalChart, Chart clonedChart) {
        JSONObjectWrapper positionJSON = orginalChart.getPosition();
        double sr = Double.parseDouble(positionJSON.getString("sr")) + 1;       // NO I18N
        double sc = Double.parseDouble(positionJSON.getString("sc")) + 1;       // NO I18N


        clonedChart.setPosition(String.valueOf(sr), String.valueOf(sc),
                positionJSON.getString("srd"), positionJSON.getString("scd"),                                          // NO I18N
                positionJSON.getString("left"), positionJSON.getString("top"));                                        // NO I18N
    }

    public static JSONObjectWrapper getColorStyleTemplate(String color, String defaultColor) {
        JSONObjectWrapper styleJSON = new JSONObjectWrapper();

        if(isDefaultColor(color)) {
            styleJSON.put("color", defaultColor);                           // NO I18N
            styleJSON.put("isDefaultColor", true);                          // NO I18N
        } else {
            styleJSON.put("color", color);                                  // NO I18N
            styleJSON.put("isDefaultColor", false);                         // NO I18N
        }

        return styleJSON;
    }

    public static String getBaseColorFromTheme(ZSTheme theme, String accent) {
        if(accent == null){ return null; }
        Map<ZSColorScheme.Colors, String> colorsMap = theme.getColorScheme().getColors();
        ZSColorScheme.Colors colors = ZSColorScheme.Colors.valueOf(accent);

        return colorsMap.get(colors);
    }

    public static JSONObjectWrapper getColorPropsTemplate(ZSTheme theme, String color, String accent, Double tone, String defaultColor) {
        JSONObjectWrapper propsJSON = new JSONObjectWrapper();

        if(isDefaultColor(color)) {
            propsJSON.put("color", defaultColor);                           // NO I18N
        } else {
            String themeColor = ChartUtils.getColorFromTheme(theme, accent, tone);
            if (themeColor == null) {
                propsJSON.put("color", color);                              // NO I18N
            } else {
                propsJSON.put("color", themeColor);                         // NO I18N
                propsJSON.put("theme", accent);                             // NO I18N
                propsJSON.put("baseColor", getBaseColorFromTheme(theme, accent));       // NO I18N
                if (tone != null && tone != 0) {
                    propsJSON.put("tonePercent", (int) (tone * 100));          // NO I18N
                    propsJSON.put("isShade", true);
                }
            }
        }

        return propsJSON;
    }

    public static String darkenBorderColor(String colorString) {
        if(colorString == null) { return null; }
        return ColorThemeBuilderUtils.darkenByPercentage(colorString, 25);
    }

    public static boolean isDefaultColor(String color) {
        return color == null || color.isEmpty() || Objects.equals("auto", color);                  // NO I18N
    }

    public static boolean isColorNull(String color) {
        return color == null || color.isEmpty();
    }

    public static boolean isAuto(String color) {
        return Objects.equals("auto", color);                  // NO I18N
    }

    public static String getValueColumnLabels(Workbook workbook, List<String> dataSources,
                                              List<Integer> valueColumns, SeriesInType seriesInType,
                                              DataJoinType dataJoinType) {
        List<String> labels = new ArrayList<>();
        for(Integer valueColumn: valueColumns) {
            labels.add(getValueColumnLabel(workbook, dataSources, valueColumn, seriesInType, dataJoinType));
        }

        return ChartUtils.join(labels, ";");            // NO I18N
    }

    public static String getValueColumnLabel(Workbook workbook, List<String> dataSources,
                                             int valueColumn, SeriesInType seriesInType,
                                             DataJoinType dataJoinType) {
        List<Range> ranges = dataSources.stream().map(dataSource -> tryCreatingRange(workbook, dataSource))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return getValueColumnLabelFromRange(workbook, ranges, valueColumn, seriesInType, dataJoinType);
    }

    public static String getValueColumnLabelFromRange(Workbook workbook, List<Range> dataSources,
                                                      int valueColumn, SeriesInType seriesInType, DataJoinType dataJoinType) {
        if(dataSources.isEmpty()) { return ""; }                    // NO I18N
        if(seriesInType == SeriesInType.COLUMNS) {
            if(dataJoinType == DataJoinType.HORIZONTAL) {
                int size = 0;
                for(Range range: dataSources) {
                    size += range.getColSize();
                    if(valueColumn < size) {
                        int rowIndex = range.getStartRowIndex();
                        int colIndex = valueColumn + range.getStartColIndex();
                        String cellRef = range.getSheet().getCell(rowIndex, colIndex).getCellRef();

                        return String.format("%s.%s", range.getSheet().getName(), cellRef);                         // NO I18N
                    }
                }
            } else {
                Range range = dataSources.get(0);
                if(valueColumn < range.getColSize()) {
                    int rowIndex = range.getStartRowIndex();
                    int colIndex = valueColumn + range.getStartColIndex();
                    String cellRef = range.getSheet().getCell(rowIndex, colIndex).getCellRef();

                    return String.format("%s.%s", range.getSheet().getName(), cellRef);                         // NO I18N
                }
            }
        } else {
            if(dataJoinType == DataJoinType.VERTICAL) {
                int size = 0;
                for(Range range: dataSources) {
                    size += range.getRowSize();
                    if(valueColumn < size) {
                        int rowIndex = range.getStartRowIndex() + valueColumn;
                        int colIndex = range.getStartColIndex();
                        String cellRef = range.getSheet().getCell(rowIndex, colIndex).getCellRef();

                        return String.format("%s.%s", range.getSheet().getName(), cellRef);                     // NO I18N
                    }
                }
            } else {
                Range range = dataSources.get(0);
                if(valueColumn < range.getRowSize()) {
                    int rowIndex = range.getStartRowIndex() + valueColumn;
                    int colIndex = range.getStartColIndex();
                    String cellRef = range.getSheet().getCell(rowIndex, colIndex).getCellRef();

                    return String.format("%s.%s", range.getSheet().getName(), cellRef);                     // NO I18N
                }
            }
        }
        return "";          // NO I18N
    }

    private static Range tryCreatingRange(Workbook workbook, String dataSource) {
        try {
            return new Range(workbook, dataSource, null, CellReference.ReferenceMode.A1, false);
        } catch (Exception e) {
            return null;
        }
    }

    public static String getOldAggregationType(AggregationType aggregationType) {
        if(aggregationType == null) { return "NONE"; }          // NO I18N
        switch (aggregationType) {
            case SUM:{
                return "SUM";                                           // NO I18N
            }
            case COUNT: {
                return "COUNT";                                         // NO I18N
            }
            case MIN: {
                return "MIN";                                           // NO I18N
            }
            case MAX: {
                return "MAX";                                           // NO I18N
            }
            case AVG: {
                return "AVERAGE";                                       // NO I18N
            }
            case COUNT_DISTINCT: {
                return "COUNT_DISTINCT";                                // NO I18N
            }
            case MEDIAN: {
                return "MEDIAN";                                        // NO I18N
            }
            case ACTUAL: {
                return "ACTUAL";                                        // NO I18N
            }
            default: {
                return "NONE";                                          // NO I18N
            }
        }
    }

    public static String getOldAggregationGroupingType(GroupingType groupingType) {
        if(groupingType == null) { return "NONE"; }
        switch (groupingType) {
            case DAY: { return "DATE-DAY"; }                // NO I18N
            case MONTH: { return "DATE-MONTH"; }            // NO I18N
            case YEAR: { return "DATE-YEAR"; }              // NO I18N
            case DAY_OF_WEEK: { return "DATE-DAY_OF_WEEK"; }     // NO I18N
            case QUARTER: { return "DATE-QUARTER"; }            // NO I18N
            case QUARTER_BY_YEAR: { return "DATE-QUARTER_BY_YEAR"; }        // NO I18N
            case MONTH_BY_YEAR: { return "DATE-MONTH_BY_YEAR"; }        // NO I18N
            default: { return "NONE"; }         // NO I18N
        }
    }

    public static String getOldFilterType(FilterType filterType) {
        if(filterType == null) { return "NONE"; }           // NO I18N
        switch (filterType) {
            case TOP: { return "TOP"; }                     // NO I18N
            case BOTTOM: { return "BOTTOM"; }               // NO I18N
        }
        return "NONE";                                      // NO I18N
    }

    public static String getColorWithOpacity(String colorString, Double opacity) {
        if(opacity == null || colorString == null) { return colorString; }

        if(colorString.startsWith("#")) {                   // NO I18N
            // if the given colorString is hex
            Color color = Color.decode(ColorUtil.getHexColor(extendShortHandHexValue(colorString)));
            return String.format("rgba(%d,%d,%d,%.2f)", color.getRed(), color.getGreen(), color.getBlue(), opacity);            // NO I18N

        } else if(colorString.startsWith("rgb")) {            // NO I18N
            // if the given colorString is rgb
            Color color = Color.decode(ColorUtil.getHexColor(colorString));
            return String.format("rgba(%d,%d,%d,%.2f)", color.getRed(), color.getGreen(), color.getBlue(), opacity);            // NO I18N
        } else if(colorString.startsWith("rgba")) {
            Color color = Color.decode(ColorUtil.getHexColor(colorString));
            return String.format("rgba(%d,%d,%d,%.2f)", color.getRed(), color.getGreen(), color.getBlue(), opacity);            // NO I18N
        }
        return colorString;
    }

    public static String extendShortHandHexValue(String hexColor) {
        if(hexColor == null || !hexColor.startsWith("#")) { return hexColor; }

        if(hexColor.length() == 4 || hexColor.length() == 5) {
            char[] colorComponents = hexColor.substring(1).toCharArray();
            return String.format("#%c%c%c%c%c%c", colorComponents[0], colorComponents[0], colorComponents[1],               // NO I18N
                    colorComponents[1], colorComponents[2], colorComponents[2]);
        }

        return hexColor;
    }

    public static int getOldLegendPosition(LegendPositionType legendPositionType) {
        if(legendPositionType == null) { return 0; }

        switch (legendPositionType) {
            case TOP: { return 2; }
            case BOTTOM: { return 1; }
            case RIGHT: { return 3; }
            case LEFT: { return 4; }
            case TOP_RIGHT: { return 5; }
            default: { return 0; }
        }
    }


    public static DataJoinType determineDataJoinType(Workbook workbook, List<String> dataSources) {
        List<DataRange> dataRanges = mapToDataRanges(workbook, dataSources);
        if(dataRanges.isEmpty() || dataRanges.size() == 1) { return DataJoinType.HORIZONTAL; }

        if(dataRanges.get(0).getStartColIndex() == dataRanges.get(1).getStartColIndex()) {
            return DataJoinType.HORIZONTAL;
        }

        return DataJoinType.VERTICAL;
    }

    public static SeriesInType determineSeriesInType(Workbook workbook, List<String> dataSources, DataJoinType dataJoinType) {
        List<DataRange> dataRanges = mapToDataRanges(workbook, dataSources);
        if(dataRanges.isEmpty() || dataRanges.size() == 1) {
            if(dataRanges.get(0).getColSize() > dataRanges.get(0).getRowSize()) { return SeriesInType.ROWS; }
        } else {
            int colSize = 0, rowSize = 0;
            if(dataJoinType == DataJoinType.HORIZONTAL) {
                rowSize = dataRanges.get(0).getRowSize();
                for(DataRange dataRange : dataRanges) { colSize += dataRange.getColSize(); }
            } else {
                colSize = dataRanges.get(0).getColSize();
                for(DataRange dataRange : dataRanges) { rowSize += dataRange.getRowSize(); }
            }
            if(colSize > rowSize) { return SeriesInType.ROWS; }
        }
        return SeriesInType.COLUMNS;
    }

    public static List<DataRange> mapToDataRanges(Workbook workbook, List<String> dataSources) {
        List<DataRange> dataRanges = new ArrayList<>();

        for(String dataSource : dataSources) {
            try {
                Range range = new Range(workbook, dataSource, null, CellReference.ReferenceMode.A1, false);
                dataRanges.add(range.toDataRange());
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Data source to Data range conversion failed", e);
            }
        }
        return dataRanges;
    }

    public static List<String> getDataSourcesFromChart(Chart chart) {
        return chart.getDataRange().stream().map(Range::getRangeStringForClient).collect(Collectors.toList());
    }

    public static SeriesInType getSeriesInTypeFromChart(Chart chart) {
        return Objects.equals(chart.getSeries(), "COLS") ? SeriesInType.COLUMNS : SeriesInType.ROWS;          // NO I18N
    }

    public static DataJoinType getDataJoinTypeFromChart(Chart chart) {
        return Objects.equals(chart.getCombineRange(), "HORIZONTAL") ? DataJoinType.HORIZONTAL : DataJoinType.VERTICAL;     // NO I18N
    }

    public static String getOldTrendlineTypeName(TrendLineType trendLineType) {
        if(trendLineType == null) { return "none"; }        // NO I18N
        if(trendLineType == TrendLineType.MOVING_AVERAGE) { return "average"; }     // NO I18N

        return ChartUtils.typeCast(trendLineType.getValue());
    }

    public static int getOldMarkerShape(MarkerShapeType markerShapeType){
        if(markerShapeType == null) { return 0; }

        switch(markerShapeType) {
            case SQUARE: { return 1; }
            case DIAMOND: { return 2; }
            case TRIANGLE: {return 3; }
            case TRIANGLE_DOWN: {return 4; }
            default: { return 0; }
        }
    }

    public static String getOldColorPaletteName(ColorPaletteType colorPaletteType, String themeName) {

        if(colorPaletteType == null) { return themeName; }
        switch (colorPaletteType) {
            case CUSTOM: { return "SHEET-OFFICE-CUSTOM-THEME";}     // NO I18N
            case PALETTE_2: { return themeName + "_VARIANT_1"; }          // NO I18N
            case PALETTE_3: { return themeName + "_VARIANT_2"; }          // NO I18N
            case PALETTE_4: { return themeName + "_VARIANT_3"; }          // NO I18N
            case MONOCHROMATIC_1: { return themeName + "_MONOCHROMATIC_1"; }      // NO I18N
            case MONOCHROMATIC_2: { return themeName + "_MONOCHROMATIC_2"; }      // NO I18N
            case MONOCHROMATIC_3: { return themeName + "_MONOCHROMATIC_3"; }      // NO I18N
            case MONOCHROMATIC_4: { return themeName + "_MONOCHROMATIC_4"; }      // NO I18N
            case MONOCHROMATIC_5: { return themeName + "_MONOCHROMATIC_5"; }      // NO I18N
            case MONOCHROMATIC_6: { return themeName + "_MONOCHROMATIC_6"; }      // NO I18N
        }
        return themeName;
    }

    public static String getOldThemeVariantName(ColorPaletteType colorPaletteType) {
        if(colorPaletteType == null) { return ""; }
        switch (colorPaletteType) {
            case CUSTOM: { return "";}     // NO I18N
            case PALETTE_2: { return "VARIANT_1"; }          // NO I18N
            case PALETTE_3: { return "VARIANT_2"; }          // NO I18N
            case PALETTE_4: { return "VARIANT_3"; }          // NO I18N
            case MONOCHROMATIC_1: { return "MONOCHROMATIC_1"; }      // NO I18N
            case MONOCHROMATIC_2: { return "MONOCHROMATIC_2"; }      // NO I18N
            case MONOCHROMATIC_3: { return "MONOCHROMATIC_3"; }      // NO I18N
            case MONOCHROMATIC_4: { return "MONOCHROMATIC_4"; }      // NO I18N
            case MONOCHROMATIC_5: { return "MONOCHROMATIC_5"; }      // NO I18N
            case MONOCHROMATIC_6: { return "MONOCHROMATIC_6"; }      // NO I18N
        }
        return "";
    }

    public static Object getBaseColorForColorPalette(Workbook workbook, ColorPaletteType colorPaletteType, boolean isReversed) {
        List<String> colors = ChartUtils.getAccentColorsTheme(workbook);

        if(colorPaletteType == null) { return colors; }
        switch (colorPaletteType) {
            case PALETTE_1:
            case PALETTE_2:
            case PALETTE_3:
            case PALETTE_4: {
                return ColorThemeBuilder.getDefaultThemeBuilder(workbook, colorPaletteType).getThemeColors(isReversed);
            }
            case MONOCHROMATIC_1: { return colors.get(0); }
            case MONOCHROMATIC_2: { return colors.get(1); }
            case MONOCHROMATIC_3: { return colors.get(2); }
            case MONOCHROMATIC_4: { return colors.get(3); }
            case MONOCHROMATIC_5: { return colors.get(4); }
            case MONOCHROMATIC_6: { return colors.get(5); }
        }

        return colors;
    }

    public static List<String> getSeriesColours(Workbook workbook, SheetMeta sheetMeta) {
        ColorPaletteType colorPaletteType = ChartUtils.requireNonNullElse(SheetChartGetterAPI.getColorPaletteType(sheetMeta), ColorPaletteType.PALETTE_1);
        boolean isReversed = ChartUtils.requireNonNullElse(SheetChartGetterAPI.getColorPaletteReversedStatus(sheetMeta), Boolean.FALSE);

        if(colorPaletteType == ColorPaletteType.CUSTOM) {
            String customColor = ChartUtils.requireNonNullElse(SheetChartGetterAPI.getColorPaletteCustomColor(sheetMeta), "#808080");       // NO I18N
            return ColorThemeBuilder.getCustomThemeBuilder(customColor).getSeriesColors(isReversed);
        } else {
            return ColorThemeBuilder.getDefaultThemeBuilder(workbook, colorPaletteType).getSeriesColors(isReversed);
        }
    }

    public static int getOldAutoHandleEmptyCells(AutoFillType autoFillType) {
        int oldValue = 2;
        if(autoFillType != null) {
            switch(autoFillType) {
                case INTERPOLATE:
                    oldValue = 1;
                    break;
                case ZERO:
                    oldValue = 0;
                    break;
            }
        }
        return oldValue;
    }

    public static int getDataPointCount(Workbook workbook, DataTable dataTable, SheetMeta sheetMeta, ChartType chartType, int seriesIndex) {
        List<List<DataTableCell>> cells = dataTable.getData(workbook, sheetMeta, chartType);
        SeriesInType seriesInType = SheetChartGetterAPI.getSeriesInType(sheetMeta);
        Boolean fcl = SheetChartGetterAPI.getFirstColLabel(sheetMeta);
        Boolean frl = SheetChartGetterAPI.getFirstRowLabel(sheetMeta);

        if(seriesInType == SeriesInType.COLUMNS) {
            return cells.size() - (Objects.equals(Boolean.TRUE, frl) ? 1 : 0);
        } else {
            int rowIdx = seriesIndex + (Objects.equals(Boolean.TRUE, frl) ? 1 : 0);
            if(cells.size() <= rowIdx) { return 0; }
            int rowSize = cells.get(rowIdx).size();

            return rowSize - (Objects.equals(Boolean.TRUE, fcl) ? 1 : 0);
        }
    }

}
