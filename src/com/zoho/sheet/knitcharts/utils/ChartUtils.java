package com.zoho.sheet.knitcharts.utils;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.ext.parser.ASTRangeNode;
import com.adventnet.zoho.websheet.model.pivot.PivotChartUtil;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.CellStyleProperties;
import com.adventnet.zoho.websheet.model.style.TableStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.style.fill.Fill;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSFontScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.*;
import com.singularsys.jep.parser.ASTVarNode;
import com.singularsys.jep.parser.Node;
import com.zoho.sheet.action.ActionInfo;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption;
import com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.*;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.PivotChartSheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.AggregationType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.ColorPaletteType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.DataJoinType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.GroupingType;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.SeriesInType;
import com.zoho.sheet.knitcharts.chartsdatastore.ChartsDSConstants;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTableCell;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.ActionDataHolder;
import com.zoho.sheet.knitcharts.constants.ChartConstants;
import com.zoho.sheet.knitcharts.container.ChartContainer;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.responsegenerator.dataholder.ActionResponseDataHolder;
import com.zoho.sheet.knitcharts.responsegenerator.dataholder.ModifiedResponseDataHolder;
import com.zoho.sheet.knitcharts.pojo.ModifiedChartPOJO;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsDAO;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTable;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTablePool;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.FrameworkChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Tuple;
import com.zoho.sheet.knitcharts.miscellaneous.datatype.*;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ChartException;
import com.zoho.sheet.knitcharts.miscellaneous.function.*;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.reference.ReferencePool;
import com.zoho.sheet.knitcharts.reference.ReferenceType;
import com.zoho.sheet.knitcharts.reference.referenceimpl.RangeReference;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetChartMetaConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.chart.Type;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.LocaleMsg;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormatSymbols;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Utility Class for Charts
 *
 * <AUTHOR>
 */
public final class ChartUtils {

    private static final String RGBA_REGEX = "^rgba\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3}),\\s*(0*(?:\\.\\d+)?|1(?:\\.0)?)\\)$";        // NO I18N

    private static final String HEX_COLOR_REGEX = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$";         // NO I18N

    private static final String RGB_REGEX = "^rgb\\((\\d{1,3}),\\s*(\\d{1,3}),\\s*(\\d{1,3})\\)$";        // NO I18N

    private static Pattern rgbPattern, rgbaPattern, hexColorPattern;

    private static final Logger LOGGER = Logger.getLogger(ChartUtils.class.getName());

    public static boolean isNotNull(Object object) {
        return object != null;
    }

    public static boolean isNull(Object object) {
        return object == null;
    }

    public static <T> T optFromJSONObject(JSONObjectWrapper jsonObject, String key, T defaultValue) {
        T resultant = null;
        Object object = jsonObject.opt(key);

        try {
            resultant = typeCast(object);
        } catch (ClassCastException e) {
            LOGGER.log(Level.SEVERE, "[getOrDefaultFromJSONObject] unable to perform type cast", e);
        }

        return isNull(resultant) ? defaultValue : resultant;
    }

    public static <T> T optFromJSONObject(JSONObjectWrapper jsonObject, String key) {
        return optFromJSONObject(jsonObject, key, null);
    }

    public static SheetMeta getSheetMeta(JSONObjectWrapper sheetMetaJSON) {
        if(sheetMetaJSON.has(ChartsDSConstants.JSONConstants.IS_PIVOT_CHART) &&
                sheetMetaJSON.getBoolean(ChartsDSConstants.JSONConstants.IS_PIVOT_CHART)) {
            return new PivotChartSheetMeta();
        }
        return new SheetMeta();
    }

    public static String optStringFromJSONObject(JSONObjectWrapper jsonObject, String key, String defaultValue) {
        Object object = jsonObject.opt(key);

        if(object == null) { return defaultValue; }
        return object.toString();
    }

    public static String optColorStringFromJSONObject(JSONObjectWrapper jsonObject, String key, String defaultValue) {
        Object object = jsonObject.opt(key);

        if(object instanceof String && ChartUtils.isValidColor((String) object)) {
            return object.toString();
        }
        return defaultValue;
    }

    public static String optColorStringFromJSONObject(JSONObjectWrapper jsonObject, String key) {
        return optColorStringFromJSONObject(jsonObject, key, null);
    }

    public static String optStringFromJSONObject(JSONObjectWrapper jsonObject, String key) {
        return optStringFromJSONObject(jsonObject, key, null);
    }

    public static String optDecodedStringFromJSONObject(JSONObjectWrapper jsonObject, String key) {
        return optDecodedStringFromJSONObject(jsonObject, key, null);
    }

    public static String optDecodedStringFromJSONObject(JSONObjectWrapper jsonObject, String key, String defaultValue) {
        String str = optStringFromJSONObject(jsonObject, key, defaultValue);
        return base64Decode(str);
    }

    public static Double optDoubleFromJSONObject(JSONObjectWrapper jsonObject, String key, Double defaultValue) {
        Double resultant = toDouble(jsonObject.opt(key));

        return isNull(resultant) ? defaultValue : resultant;
    }

    public static Double optDoubleFromJSONObject(JSONObjectWrapper jsonObject, String key) {
        return optDoubleFromJSONObject(jsonObject, key, null);
    }

    public static Integer optIntegerFromJSONObject(JSONObjectWrapper jsonObject, String key, Integer defaultValue) {
        Integer resultant = toInteger(jsonObject.opt(key));

        return isNull(resultant) ? defaultValue : resultant;
    }

    public static Integer optIntegerFromJSONObject(JSONObjectWrapper jsonObject, String key) {
        return optIntegerFromJSONObject(jsonObject, key, null);
    }

    public static ChartsInteger optChartsIntegerFromJSONObject(JSONObjectWrapper jsonObject, String key, ChartsInteger defaultValue){
        ChartsInteger resultant = null;
        Object object = jsonObject.opt(key);

        if(object instanceof Number){
            Number number = typeCast(object);
            resultant = new ChartsInteger(number.intValue());
        }

        return isNull(resultant) ? defaultValue : resultant;
    }

    public static ChartsInteger optChartsIntegerFromJSONObject(JSONObjectWrapper jsonObject, String key){
        return optChartsIntegerFromJSONObject(jsonObject, key, null);
    }

    public static ChartsDouble optChartsDoubleFromJSONObject(JSONObjectWrapper jsonObject, String key, ChartsDouble defaultValue){
        Double db = optDoubleFromJSONObject(jsonObject, key);
        if(db == null){
            return defaultValue;
        }else{
            return new ChartsDouble(db);
        }
    }

    public static ChartsDouble optChartsDoubleFromJSONObject(JSONObjectWrapper jsonObject, String key){
        return optChartsDoubleFromJSONObject(jsonObject, key, null);
    }

    public static <T extends Type> T retrieveTypeByValue(Class<T> clazz, String value) {
        if(value == null) { return null; }
        try {
            Method method = clazz.getMethod("retrieveByValue", String.class);           // NO I18N
            method.setAccessible(true);

            @SuppressWarnings("unchecked") T type = (T) method.invoke(null, value);  // NO I18N
            return type;
        } catch (InvocationTargetException | IllegalAccessException | NoSuchMethodException e) {
            return null;
        }
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.ChartOption> Map<ChartsInteger, T> optMapFromJSONObject(JSONObjectWrapper mapObject, FunctionSupplier<T> chartOptionSupplier) {
        Map<ChartsInteger, T> map = new HashMap<>();

        if(mapObject != null) {
            ChartUtils.forEachKey(mapObject, index -> {
                ChartsInteger cIndex = ChartUtils.toChartsInteger(Integer.parseInt(index));
                JSONWrapper mapEntryObject = ChartUtils.optFromJSONObject(mapObject, index);

                T option = chartOptionSupplier.supply();
                option.fromJSON(mapEntryObject);
                map.put(cIndex, option);
            });
        }
        return map;
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.ChartOption> Map<ChartsInteger, T> optMapFromJSONObject(JSONObjectWrapper mapObject,
                                                                                                                                 FunctionProducer<Integer, T> producer){
        Map<ChartsInteger, T> map = new HashMap<>();

        if(mapObject != null) {
            ChartUtils.forEachKey(mapObject, index -> {
                ChartsInteger cIndex = ChartUtils.toChartsInteger(Integer.parseInt(index));
                JSONWrapper mapEntryObject = ChartUtils.optFromJSONObject(mapObject, index);

                T option = producer.produce(cIndex.getValue());
                option.fromJSON(mapEntryObject);
                map.put(cIndex, option);
            });
        }
        return map;
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.ChartOption> List<T> optListFromJSONObject(JSONObjectWrapper listObject,
                                                                                                                    FunctionProducer<Integer, T> producer) {
        List<T> list = new ArrayList<>();

        if(listObject != null) {
            ChartUtils.forEachKey(listObject, index -> {
                int cIndex = Integer.parseInt(index);
                JSONWrapper mapEntryObject = ChartUtils.optFromJSONObject(listObject, index);

                T option = producer.produce(cIndex);
                option.fromJSON(mapEntryObject);
                while(list.size() < cIndex) { list.add(list.size(), null); }
                list.add(option);
            });
        }
        return list;
    }

    public static boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, Object value) {
        if(value == null){
            return false;
        } else if(value instanceof Type){
            return putIfNotNull(jsonObject, key, (Type) value);
        } else if(value instanceof Key){
            return putIfNotNull(jsonObject, key, (Key) value);
        } else if(value instanceof com.zoho.sheet.knitcharts.chart.chartoption.ChartOption){
            return putIfNotNull(jsonObject, key, (com.zoho.sheet.knitcharts.chart.chartoption.ChartOption) value);
        }
        else{
            jsonObject.put(key, value);
            return true;
        }
    }

    public static boolean putIfNotEmpty(JSONObjectWrapper jsonObject, String key, JSONObjectWrapper value) {
        if(value == null || value.isEmpty()){ return false; }

        jsonObject.put(key, value);
        return true;
    }

    public static boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, Key value) {
        if(value == null){ return false; }

        jsonObject.put(key, value.toString());
        return true;
    }

    public static <T extends Type> boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, T type) {
        if(type == null){
            return false;
        }else{
            jsonObject.put(key, type.getValue());
            return true;
        }
    }

    public static <T extends Type> void put(JSONObjectWrapper jsonObject, String key, T type) {
        put(jsonObject, key, type, com.zoho.sheet.knitcharts.constants.ChartConstants.Defaults.DEFAULT_VALUE);
    }

    public static <T extends Type> void put(JSONObjectWrapper jsonObject, String key, Collection<T> type) {
        put(jsonObject, key, type, com.zoho.sheet.knitcharts.constants.ChartConstants.Defaults.DEFAULT_VALUE);
    }

    public static <T extends Type> void put(JSONObjectWrapper jsonObject, String key, Collection<T> type, String defaultValue) {
        if(type == null || type.isEmpty()){
            jsonObject.put(key, defaultValue);
        } else {
            JSONArrayWrapper jsonArray = new JSONArrayWrapper();
            for(T t : type){
                put(jsonArray, t);
            }
            jsonObject.put(key, jsonArray);
        }
    }

    public static <T extends Type> void put(JSONObjectWrapper jsonObject, String key, T type, String defaultValue) {
        if(type != null){
            jsonObject.put(key, type.getValue());
        } else {
            jsonObject.put(key, defaultValue);
        }
    }

    public static <T extends Type> void put(JSONArrayWrapper jsonArray, T type) {
        put(jsonArray, type, com.zoho.sheet.knitcharts.constants.ChartConstants.Defaults.DEFAULT_VALUE);
    }

    public static <T extends Type> void put(JSONArrayWrapper jsonArray, T type, String defaultValue) {
        if(type == null){
            jsonArray.put(defaultValue);
        } else {
            jsonArray.put(type.getValue());
        }
    }



    public static void put(JSONObjectWrapper jsonObject, String key, Object value) {
        put(jsonObject, key, value, com.zoho.sheet.knitcharts.constants.ChartConstants.Defaults.DEFAULT_VALUE);
    }

    public static void put(JSONObjectWrapper jsonObject, String key, Object value, Object defaultValue) {
        jsonObject.put(key, value == null ? defaultValue : value);
    }

    public static void putEncoded(JSONObjectWrapper jsonObject, String key, String value, Object defaultValue) {
        jsonObject.put(key, value == null ? defaultValue : base64Encode(value));
    }

    public static void putEncoded(JSONObjectWrapper jsonObject, String key, String value) {
        putEncoded(jsonObject, key, value, com.zoho.sheet.knitcharts.constants.ChartConstants.Defaults.DEFAULT_VALUE);
    }

    public static boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, ChartsInteger chartsInteger) {
        if(chartsInteger == null){
            return false;
        }else{
            jsonObject.put(key, chartsInteger.getValue());
            return true;
        }
    }

    public static boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, ChartsBoolean chartsBoolean) {
        if(chartsBoolean == null){
            return false;
        }else{
            jsonObject.put(key, chartsBoolean.getValue());
            return true;
        }
    }

    public static boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, ChartsDouble chartsDouble) {
        if(chartsDouble == null){
            return false;
        }else{
            jsonObject.put(key, chartsDouble.getValue());
            return true;
        }
    }

    private static DataLabelPositionType[] newToOldDataLabelsPositionMap;


    public static int getOldDataLabelsPositionIndex(DataLabelPositionType newPosition){

        int index = -1;

        if(newToOldDataLabelsPositionMap == null){
            newToOldDataLabelsPositionMap = new DataLabelPositionType[] {DataLabelPositionType.AUTO, DataLabelPositionType.INSIDE_END,
                    DataLabelPositionType.INSIDE_MIDDLE, DataLabelPositionType.INSIDE_BASE, DataLabelPositionType.OUTSIDE_END,
                    DataLabelPositionType.AUTO, DataLabelPositionType.TOP, DataLabelPositionType.CENTER, DataLabelPositionType.BOTTOM,
                    DataLabelPositionType.LEFT, DataLabelPositionType.RIGHT, DataLabelPositionType.AUTO, DataLabelPositionType.INSIDE,
                    DataLabelPositionType.OUTSIDE};

        }

        if(newPosition != null) {
            for (int i = 0; i < newToOldDataLabelsPositionMap.length; i++) {
                if (newToOldDataLabelsPositionMap[i] == newPosition) {
                    index =  i;
                }
            }
        }

        return index;
    }


    public static boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, ChartsString chartsString){
        if(chartsString == null){
            return false;
        }else{
            jsonObject.put(key, chartsString.getValue());
            return true;
        }
    }

    public static boolean putStringIfNotNull(JSONObjectWrapper jsonObject, String key, String value) {
        if(value == null){
            return false;
        }
        jsonObject.putString(key, value);
        return true;
    }

    public static boolean putStringIfNotNull(JSONObjectWrapper jsonObject, String key, ChartsString value) {
        if(value == null){
            return false;
        }
        jsonObject.putString(key, value.getValue());
        return true;
    }

    public static boolean putEncodedIfNotNull(JSONObjectWrapper jsonObject, String key, ChartsString chartsString){
        if(chartsString == null){
            return false;
        }
        return putEncodedIfNotNull(jsonObject, key, chartsString.getValue());
    }

    public static boolean putEncodedIfNotNull(JSONObjectWrapper jsonObject, String key, String value) {
        if(value == null){
            return false;
        }
        jsonObject.put(key, base64Encode(value));
        return true;
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.ChartOption> boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, T chartOption){
        if(chartOption == null){ return false; }
        else{
            JSONWrapper json = chartOption.toJSON();
            if(json == null) { return false; }
            jsonObject.put(key, json);
            return true;
        }
    }

    public static boolean putOnTheFlyOptionIfNotNull(JSONObjectWrapper jsonObject, String key,
                                                     com.zoho.sheet.knitcharts.chart.chartoption.ChartOnTheFlyOption chartOnTheFlyOption, Workbook workbook, com.zoho.sheet.knitcharts.chart.Chart chart){
        if(chartOnTheFlyOption == null){ return false; }
        else{
            JSONWrapper json = chartOnTheFlyOption.toJSON(workbook, chart);
            if(json == null) { return false; }
            jsonObject.put(key, json);
            return true;
        }
    }

    public static boolean putKeyOnTheFlyOptionIfNotNull(JSONObjectWrapper jsonObject, String key,
                                                        com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption chartKeyOnTheFlyOption, Workbook workbook, Chart chart, Key optionKey){
        if(chartKeyOnTheFlyOption == null){ return false; }
        else{
            JSONWrapper json = chartKeyOnTheFlyOption.toJSON(workbook, chart, optionKey);
            if(json == null) { return false; }
            jsonObject.put(key, json);
            return true;
        }
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption> boolean putKeyOnTheFlyOptionIfNotNull(JSONObjectWrapper jsonObject, List<T> list,
                                                                                                                                       Workbook workbook, Chart chart, Key key){
        if(list == null || list.isEmpty()){ return false; }
        boolean isAnyKeyNotNull = false;

        for(int idx = 0; idx < list.size(); idx++){
            com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption chartKeyOnTheFlyOption = list.get(idx);

            isAnyKeyNotNull |= putKeyOnTheFlyOptionIfNotNull(jsonObject, String.valueOf(idx), chartKeyOnTheFlyOption,
                    workbook, chart, key.getChild(idx));
        }

        return isAnyKeyNotNull;
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption> boolean putKeyOnTheFlyOptionIfNotNull(JSONObjectWrapper jsonObject, Map<ChartsInteger, T> map,
                                                                                                                                       Workbook workbook, Chart chart, Key key){
        if (map == null || map.isEmpty()){ return false; }
        boolean isAnyKeyNotNull = false;

        for(Map.Entry<ChartsInteger, T> entry : map.entrySet()){
            ChartsInteger index = entry.getKey();

            isAnyKeyNotNull |= putKeyOnTheFlyOptionIfNotNull(jsonObject, String.valueOf(index), entry.getValue(),
                    workbook, chart, key.getChild(index.getValue()));
        }

        return isAnyKeyNotNull;
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.ChartKeyOnTheFlyOption> boolean putOnTheFlyOptionIfNotNull(JSONObjectWrapper jsonObject, Map<ChartsInteger, T> map,
                                                                                                                                    Workbook workbook, Chart chart){
        if (map == null || map.isEmpty()){ return false; }
        boolean isAnyKeyNotNull = false;

        for(Map.Entry<ChartsInteger, T> entry : map.entrySet()){
            ChartsInteger index = entry.getKey();

            isAnyKeyNotNull |= putKeyOnTheFlyOptionIfNotNull(jsonObject, String.valueOf(index), entry.getValue(),
                    workbook, chart, Key.getKey(index.getValue()));
        }

        return isAnyKeyNotNull;
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption> boolean putIfNotNull(JSONObjectWrapper jsonObject, T groupedChartOption){
        if(groupedChartOption == null){ return false; }
        else {
            return groupedChartOption.putIntoJSON(jsonObject);
        }
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption> boolean putStylesIfNotNull(JSONObjectWrapper jsonObject, String key, T chartStyleOption) {
        if(chartStyleOption == null) { return false; }
        else  {
            JSONWrapper json = chartStyleOption.extractStyles();
            if(json == null) { return false; }
            jsonObject.put(key, json);
            return true;
        }
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartStyleOption> boolean putStylesIfNotNull(JSONObjectWrapper jsonObject, T groupedChartStyleOption) {
        if(groupedChartStyleOption == null) { return false; }
        else {
            return groupedChartStyleOption.extractStyles(jsonObject);
        }
    }

    public static <K, V extends com.zoho.sheet.knitcharts.chart.chartoption.ChartOption> boolean putIfNotNull(JSONObjectWrapper jsonObject, Map<K, V> map){
        if(map == null || map.isEmpty()) { return false; }

        for(Map.Entry<K, V> entry : map.entrySet()){
            jsonObject.put(entry.getKey().toString(), entry.getValue().toJSON());
        }
        return !jsonObject.isEmpty();
    }

    public static <K, V extends com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption> boolean putStylesIfNotNull(JSONObjectWrapper jsonObject, Map<K, V> map){
        if(map == null || map.isEmpty()) { return false; }

        boolean anyStylesNotNull = false;

        for(Map.Entry<K, V> entry : map.entrySet()){
            anyStylesNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, entry.getKey().toString(), entry.getValue());
        }
        return anyStylesNotNull;
    }

    public static <T extends com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption> boolean putStylesIfNotNull(JSONObjectWrapper jsonObject, List<T> chartStyleOptions){
        if(chartStyleOptions == null || chartStyleOptions.isEmpty()) { return false; }
        boolean anyStylesNotNull = false;

        for(int idx = 0; idx < chartStyleOptions.size(); idx++){
            anyStylesNotNull |= ChartUtils.putStylesIfNotNull(jsonObject, String.valueOf(idx), chartStyleOptions.get(idx));
        }

        return anyStylesNotNull;
    }

    public static <E extends com.zoho.sheet.knitcharts.chart.chartoption.ChartOption> boolean putIfNotNull(JSONArrayWrapper jsonArray, List<E> list){
        if(list == null || list.isEmpty()) { return false; }

        for(E element: list){
            jsonArray.put(element.toJSON());
        }
        return true;
    }

    public static <E extends com.zoho.sheet.knitcharts.chart.chartoption.ChartOption> boolean putIfNotNull(JSONObjectWrapper jsonObject, List<E> list) {
        if(list == null || list.isEmpty()) { return false; }

        boolean anyOptionNotNull = false;
        for(int idx = 0; idx < list.size(); idx++){
            anyOptionNotNull |= ChartUtils.putIfNotNull(jsonObject, String.valueOf(idx), list.get(idx));
        }

        return anyOptionNotNull;
    }

    public static <T> boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, Collection<T> collection) {
        if(collection == null || collection.isEmpty()) { return false; }
        jsonObject.put(key, collection);

        return true;
    }

    public static <T extends Type> boolean putTypesIfNotNull(JSONObjectWrapper jsonObject, String key, Collection<T> collection) {
        if(collection == null || collection.isEmpty()) { return false; }

        jsonObject.put(key, collection.stream().map(Type::getValue).collect(Collectors.toList()));
        return true;
    }

    public static <T extends ChartsDataType> boolean putIfNotNull(JSONObjectWrapper jsonObject, String key, List<T> collection) {
        if(collection == null || collection.isEmpty()) { return false; }

        jsonObject.put(key, collection.stream().map(ChartsDataType::getJavaType)
                .collect(Collectors.toList()));
        return true;
    }

    public static JSONObjectWrapper JSONToJSONObject(JSONWrapper json) {
        if (ChartUtils.isNull(json) || json.isArray()) {
            return null;
        }
        return typeCast(json);
    }

    public static JSONArrayWrapper JSONTOJSONArray(JSONWrapper json) {
        if (ChartUtils.isNull(json) || !json.isArray()) {
            return null;
        }
        return typeCast(json);
    }

    public static void forEachKey(JSONObjectWrapper jsonObject, FunctionConsumer<String> forEachCallback) {
        Iterator<?> keysIterator = jsonObject.keys();

        while (keysIterator.hasNext()) {
            String key = typeCast(keysIterator.next());
            forEachCallback.consume(key);
        }
    }

    public static void forEach(JSONArrayWrapper jsonArray, FunctionConsumer<Object> forEachCallBack){
        Iterator<?> iterator = jsonArray.iterator();

        while(iterator.hasNext()){
            forEachCallBack.consume(jsonArray.getAsJSONWrapper(iterator.next()));
        }
    }

    /**
     * Method to assist type cast an Object instance to the instance of a desired class.
     * Note: This method doesn't contain any check or doesn't provide safety, may throw ClassCastException
     *
     * @param object Object instance to be type cast
     * @return type cast T's instance
     */
    @SuppressWarnings("unchecked")                                  // NO I18N
    public static <T> T typeCast(Object object) {
        return (T) object;
    }

    /**
     * Method to assist getting Chart object from ActionDataHolder
     *
     * @param bundle ActionDataHolder instance
     * @return Chart instance from workbook
     */
    public static ChartMeta getChartMetaFromActionDataHolder(ActionDataHolder bundle) {
        Sheet sheet = bundle.getActiveSheet();
        Workbook workbook = sheet.getWorkbook();
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = workbook.getChartsContainer();

        return chartContainer.getChart(bundle.getChartID()).getApiMeta().getChartMeta();
    }

    public static SheetMeta getSheetMetaFromActionDataHolder(ActionDataHolder dataHolder){
        Sheet sheet = dataHolder.getActiveSheet();
        Workbook workbook = sheet.getWorkbook();
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = workbook.getChartsContainer();

        return chartContainer.getChart(dataHolder.getChartID()).getSheetMeta();
    }

    public static SheetMeta getSheetMetaFromActionResponseDataHolder(ActionResponseDataHolder dataHolder){
        Workbook workbook = dataHolder.getWorkbook();
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = workbook.getChartsContainer();
        String chartID = dataHolder.getChartsBean().getChartID();

        return chartContainer.getChart(chartID).getSheetMeta();
    }

    public static ChartMeta getChartMetaFromActionResponseDataHolder(ActionResponseDataHolder dataHolder){
        Workbook workbook = dataHolder.getWorkbook();
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = workbook.getChartsContainer();
        String chartID = dataHolder.getChartsBean().getChartID();

        return chartContainer.getChart(chartID).getApiMeta().getChartMeta();
    }

    public static com.zoho.sheet.knitcharts.container.ChartContainer getChartContainer(ActionDataHolder dataHolder){
        return dataHolder.getActiveSheet().getWorkbook().getChartsContainer();
    }

    public static com.zoho.sheet.knitcharts.chart.Chart getChartFromActionResponseDataHolder(ActionResponseDataHolder dataHolder){
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = dataHolder.getWorkbook().getChartsContainer();
        String chartID = dataHolder.getChartsBean().getChartID();

        return chartContainer.getChart(chartID);
    }

    public static com.zoho.sheet.knitcharts.chart.Chart getChartFromModifiedResponseDataHolder(ModifiedResponseDataHolder dataHolder) {
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = dataHolder.getWorkbook().getChartsContainer();

        return chartContainer.getChart(dataHolder.getChartsModifiedBean().getChartID());
    }

    public static DataTable getDataTableFromActionResponseDataHolder(ActionResponseDataHolder dataHolder){
        DataTablePool dataTablePool = dataHolder.getChartsBean().getDataTablePool();
        Workbook workbook = dataHolder.getWorkbook();
        String chartID = dataHolder.getChartsBean().getChartID();

        return dataTablePool.getDataTable(workbook, chartID);
    }

    public static DataTable getDataTableFromModifiedResponseDataHolder(ModifiedResponseDataHolder dataHolder){
        DataTablePool dataTablePool = dataHolder.getChartsModifiedBean().getDataTablePool();
        Workbook workbook = dataHolder.getWorkbook();

        return dataTablePool.getDataTable(workbook, dataHolder.getChartsModifiedBean().getChartID());
    }

    public static Chart getChartFromActionDataHolder(ActionDataHolder dataHolder){
        Sheet sheet = dataHolder.getActiveSheet();
        Workbook workbook = sheet.getWorkbook();
        com.zoho.sheet.knitcharts.container.ChartContainer chartContainer = workbook.getChartsContainer();

        return chartContainer.getChart(dataHolder.getChartID());
    }

    public static boolean ifNotNullThen(Object object, Block block) {
        if (ChartUtils.isNotNull(object)) {
            block.execute();
            return true;
        }
        return false;
    }

    public static <T> boolean ifNotNullThen(Object object, BlockConsumer<T> block) {
        if (ChartUtils.isNotNull(object)) {
            block.execute(typeCast(object));
            return true;
        }
        return false;
    }

    public static ChartsInteger toChartsInteger(int integerValue) {
        return new ChartsInteger(integerValue);
    }

    public static List<ChartsInteger> toChartsInteger(List<Integer> integerList) {

        if(integerList == null || integerList.isEmpty()) { return Collections.emptyList(); }

        return integerList.stream().map(ChartUtils::toChartsInteger)
                .collect(Collectors.toList());
    }

    public static List<Integer> toInteger(List<ChartsInteger> chartsIntegerList) {

        if(chartsIntegerList == null || chartsIntegerList.isEmpty()) { return Collections.emptyList(); }

        return chartsIntegerList.stream().map(ChartsInteger::getValue)
                .collect(Collectors.toList());
    }

    public static ChartsBoolean toChartsBoolean(boolean booleanValue) {
        return new ChartsBoolean(booleanValue);
    }

    public static ChartsDouble toChartsDouble(double doubleValue) {
        return new ChartsDouble(doubleValue);
    }

    public static ChartsString toChartsString(String value){
        return new ChartsString(value);
    }

    public static boolean needToRecordForUndo(int actionConstant) {
        switch (actionConstant) {
            case ActionConstants.PUBLISH_CHART:
            case ActionConstants.CHART_MOVE_TO_OTHER_SHEET:
            case ActionConstants.UNPUBLISH_CHART: {
                return false;
            }
            default: {
                return true;
            }
        }
    }

    public static ActionInfo getActionInfoForAction(int actionConstant){
        return new ActionInfo(
                actionConstant, ActionInfo.SHEET_JSON,
                needToRecordForUndo(actionConstant), false, false,
                false, false,false,
                false, new ActionInfo.ActionSelectionProperties(
                        ActionInfo.RangeSelectionType.NONE, true, true
                )
            );
    }

    public static ActionInfo getActionInfoForUserAction(int actionConstant) {
        return new ActionInfo(actionConstant, ActionInfo.SHEET_JSON, false, false, false, false, false, false, false, new ActionInfo.ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true,true));
    }

    /**
     * Transfers object from source JSONWrapper to target JSONWrapper
     * @param sourceJSON Source of the object
     * @param targetJSON target of the object
     * @param sourceKey key of the object in source JSONWrapper
     * @param targetKey key for the object in target JSONWrapper
     */
    public static void copyObject(JSONObjectWrapper sourceJSON, JSONObjectWrapper targetJSON, String sourceKey, String targetKey){
        targetJSON.put(targetKey, sourceJSON.get(sourceKey));
    }

    /**
     * Copy object from source JSONWrapper to target JSONWrapper in the same key
     * @param sourceJSON Source JSONWrapper of the object
     * @param targetJSON Target JSONWrapper
     * @param key Key of the Object
     */
    public static void copyObject(JSONObjectWrapper sourceJSON, JSONObjectWrapper targetJSON, String key){
        copyObject(sourceJSON, targetJSON, key, key);
    }

    public static void copyObjectIfAvailable(JSONObjectWrapper sourceJSON, JSONObjectWrapper targetJSON, String sourceKey, String targetKey){
        if(sourceJSON.has(sourceKey)) {
            targetJSON.put(targetKey, sourceJSON.get(sourceKey));
        }
    }

    public static void copyObjectIfAvailable(JSONObjectWrapper sourceJSON, JSONObjectWrapper targetJSON, String key){
        copyObjectIfAvailable(sourceJSON, targetJSON, key, key);
    }

    public static <T> List<T> JSONArrayToList(JSONArrayWrapper jsonArray){
        List<T> list = new ArrayList<>();
        Iterator<?> jsonIterator = jsonArray.iterator();

        while(jsonIterator.hasNext()){
            list.add(typeCast(jsonArray.getAsJSONWrapper(jsonIterator.next())));
        }

        return list;
    }

    public static <T> Set<T> JSONArrayToSet(JSONArrayWrapper jsonArray){
        Set<T> set = new HashSet<>();
        Iterator<?> jsonIterator = jsonArray.iterator();

        while(jsonIterator.hasNext()){
            set.add(typeCast(jsonArray.getAsJSONWrapper(jsonIterator.next())));
        }

        return set;
    }

    public static <T> T computeIfAbsent(JSONObjectWrapper jsonObject, String key, FunctionProducer<String, T> callback){
        if(!jsonObject.has(key)){
            T obj = callback.produce(key);
            jsonObject.put(key, obj);
            return obj;
        }
        return typeCast(jsonObject.opt(key));
    }

    public static void putAll(JSONArrayWrapper target, JSONArrayWrapper source) {
        forEach(source, target::put);
    }

    public static Double getDoubleOrNull(ChartsDouble chartsDouble){
        return chartsDouble == null ? null : chartsDouble.getValue();
    }

    public static ChartsDouble getChartsDoubleOrNull(Double dbl){
        return dbl == null ? null : toChartsDouble(dbl);
    }

    public static Boolean getBooleanOrNull(ChartsBoolean chartsBoolean){
        return chartsBoolean == null ? null : chartsBoolean.getValue();
    }

    public static ChartsBoolean getChartsBooleanOrNull(Boolean bool){
        return bool == null ? null : toChartsBoolean(bool);
    }

    public static Integer getIntegerOrNull(ChartsInteger chartsInteger){
        return chartsInteger == null ? null : chartsInteger.getValue();
    }

    public static ChartsInteger getChartsIntegerOrNull(Integer integer){
        return integer == null ? null : toChartsInteger(integer);
    }

    public static ChartsString getChartsStringOrNull(String string){
        return string == null ? null : toChartsString(string);
    }

    public static String getStringOrNull(ChartsString chartsString){
        return chartsString == null ? null : chartsString.getValue();
    }

    public static int getMax(int ...numbers){
        int maxIndex = getMaxIndex(numbers);
        return numbers[maxIndex];
    }

    public static int getMaxIndex(int ...numbers){
        int maxIndex = 0;
        for(int idx = 1; idx < numbers.length; idx++){
            maxIndex = numbers[maxIndex] > numbers[idx] ? maxIndex : idx;
        }
        return maxIndex;
    }


    /**
     * Joins the given list into a string with delimiter between the values
     * @param list List of values to be joined
     * @param delimiter Delimiter
     * @return Joint string
     * @param <T> Type param of the value
     */
    public static <T> String join(List<T> list, String delimiter){
        if(list == null || list.isEmpty()) { return ""; }

        if(delimiter == null) { delimiter = ""; }

        int size = list.size() - 1;
        StringBuilder builder = new java.lang.StringBuilder();
        for(int idx = 0; idx < size; idx++){
            builder.append(list.get(idx).toString()).append(delimiter);
        }
        builder.append(list.get(size).toString());
        return builder.toString();
    }

    public static Workbook getWorkbook() {
        try {
            return CurrentRealm.getContainerWithoutTrace().getWorkbook(CurrentRealm.getWorkBookIdentity());
        }catch (Exception e){
            throw new ChartException(e.getMessage());
        }
    }

    public static String getSheetNameByChartID(WorkbookContainer container, String chartID){
        ChartsDAO dao = ChartsDAO.getDAOWithDocOwner(container.getDocOwner());

        return dao.getDocumetDetailsByChartID(chartID).getSheetName();
    }

    public static<R, P> P produceIfNotNull(FunctionProducer<R, P> producer, R resource){
        if(resource == null) { return null;}
        return producer.produce(resource);
    }

    public static <P> P produceIfNotNullAndEmpty(FunctionProducer<String, P> producer, String resource){
        if(resource == null || resource.isEmpty()) { return null; }
        return producer.produce(resource);
    }

    public static String getCommandConstant(int action){
        switch (action){
            case ActionConstants.CHART_NEW:
            case ActionConstants.CLONE_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART_VIA_CACHE:
            case ActionConstants.PASTE_CHART:
            case ActionConstants.INSERT_PIVOT_CHART:
            case ActionConstants.INSERT_TABLE_CHART:
            case ActionConstants.INSERT_CHART:{
                return String.valueOf(CommandConstants.INSERT);
            }
            case ActionConstants.PASTE_CHART_STYLES:
            case ActionConstants.RESET_CHART_STYLES:{
                return String.valueOf(CommandConstants.STYLE);
            }
            case ActionConstants.FRAMEWORK_CHART_EDIT:
            case ActionConstants.CHART_MOVE_TO_OTHER_SHEET:
            case ActionConstants.SHEET_CHART_EDIT: {
                return String.valueOf(CommandConstants.UPDATE);
            }
            case ActionConstants.PUBLISH_CHART: {
                return String.valueOf(CommandConstants.PUBLISH);
            }
            case ActionConstants.UNPUBLISH_CHART: {
                return String.valueOf(CommandConstants.UNPUBLISH);
            }
            case ActionConstants.DELETE_CHART:{
                return String.valueOf(CommandConstants.DELETE);
            }
        }
        return String.valueOf(CommandConstants.META);
    }

    /* Mapping from old chart type name to new chart type object */
    private static Map<String, ChartType> oldToNewChartTypeMap;

    /**
     * Retrieves new chart type for the given old chart type name
     * @param oldChartType Old chart type name
     * @return New chart type object
     */
    public static ChartType getChartForOldChartName(String oldChartType){
        if(Objects.isNull(oldChartType)) { return ChartType.COLUMN; }

        if(oldToNewChartTypeMap == null){
            oldToNewChartTypeMap = Collections.unmodifiableMap(new HashMap<>() {{
                put("COL", ChartType.COLUMN);                                            // NO I18N
                put("STACKEDCOL", ChartType.STACKED_COLUMN);                             // NO I18N
                put("STACKEDCOL_PERCENT", ChartType.STACKED_PERCENT_COLUMN);             // NO I18N
                put("GROUPEDCOL", ChartType.GROUPED_COLUMN);                             // NO I18N
                put("BAR", ChartType.BAR);                                               // NO I18N
                put("STACKEDBAR", ChartType.STACKED_BAR);                                // NO I18N
                put("STACKEDBAR_PERCENT", ChartType.STACKED_PERCENT_BAR);                // NO I18N
                put("GROUPEDBAR", ChartType.GROUPED_BAR);                                // NO I18N
                put("ANIMATION_CHART", ChartType.RACE_BAR);                              // NO I18N
                put("XYAREA", ChartType.AREA);                                           // NO I18N
                put("XYAREA_AREA_SPLINE", ChartType.AREA_SPLINE);                        // NO I18N
                put("XYSTACKEDAREA", ChartType.STACKED_AREA);                            // NO I18N
                put("XYSTACKEDAREA_PERCENT", ChartType.STACKED_PERCENT_AREA);            // NO I18N
                put("TIMEAREA", ChartType.AREA_TIME_SERIES);                             // NO I18N
                put("PIE", ChartType.PIE);                                               // NO I18N
                put("SEMIPIE", ChartType.SEMI_PIE);                                      // NO I18N
                put("DOUGHNUT", ChartType.DONUT);                                        // NO I18N
                put("SEMIDOUGHNUT", ChartType.SEMI_DONUT);                               // NO I18N
                put("XYLINE", ChartType.LINE);                                           // NO I18N
                put("SPLINE", ChartType.SPLINE);                                         // NO I18N
                put("STEPCHART", ChartType.STEP_LINE);                                   // NO I18N
                put("TIMELINE", ChartType.LINE_TIME_SERIES);                             // NO I18N
                put("SCATTER", ChartType.SCATTER);                                       // NO I18N
                put("XYLINE_SCATTER", ChartType.SCATTER_LINE);                           // NO I18N
                put("XYLINE_SCATTER_SHAPES", ChartType.SCATTER_LINE_MARKERS);            // NO I18N
                put("BULLETCOL", ChartType.BULLET_COLUMN);                               // NO I18N
                put("BULLETBAR", ChartType.BULLET_BAR);                                  // NO I18N
                put("FUNNEL", ChartType.FUNNEL);                                         // NO I18N
                put("WEIGHTED_FUNNEL", ChartType.WEIGHTED_FUNNEL);                       // NO I18N
                put("BUBBLECHART", ChartType.BUBBLE);                                    // NO I18N
                put("HISTOGRAM", ChartType.HISTOGRAM);                                   // NO I18N
                put("WORDCLOUD", ChartType.WORD_CLOUD);                                  // NO I18N
                put("COMBOCHART", ChartType.COMBO);                                      // NO I18N
                put("ITEM", ChartType.DONUT_PARLIAMENT);                                 // NO I18N
                put("PIE_ITEM", ChartType.PIE_PARLIAMENT);                               // NO I18N
                put("CANDLESTICK", ChartType.CANDLE_STICK);                              // NO I18N
                put("OHLCCHART", ChartType.OHLC);                                        // NO I18N
                put("POLAR", ChartType.POLAR);                                           // NO I18N
                put("SPIDERWEB", ChartType.SPIDER_WEB);                                  // NO I18N
                put("PARETO", ChartType.PARETO);                                         // NO I18N
                put("WATERFALL", ChartType.WATERFALL);                                   // NO I18N
                put("BOX_PLOT", ChartType.BOXPLOT_COLUMN);                               // NO I18N
                put("BOX_PLOT_H", ChartType.BOXPLOT_BAR);                                // NO I18N
                put("COL3D", ChartType.COLUMN);                                          // NO I18N
                put("BAR3D", ChartType.BAR);                                             // NO I18N
                put("PIE3D", ChartType.PIE);                                             // NO I18N
                put("FUNNEL3D", ChartType.FUNNEL);                                       // NO I18N
                put("DOUGHNUT3D", ChartType.DONUT);                                      // NO I18N
                put("STACKEDCOL3D", ChartType.STACKED_COLUMN);                           // NO I18N
                put("STACKEDBAR3D", ChartType.STACKED_BAR);                              // NO I18N
                put("STACKEDCOL3D_PERCENT", ChartType.STACKED_PERCENT_COLUMN);           // NO I18N
                put("STACKEDBAR3D_PERCENT", ChartType.STACKED_PERCENT_BAR);              // NO I18N
                put("XYLINE_SHAPES", ChartType.LINE);                                    // NO I18N
            }});
        }
        return oldToNewChartTypeMap.getOrDefault(oldChartType, ChartType.COLUMN);
    }

    private static Map<ChartType, String> newToOldChartTypeMap;

    public static String getOldChartNameForChartType(ChartType chartType) {
        if(newToOldChartTypeMap == null) {
            newToOldChartTypeMap = Collections.unmodifiableMap(new HashMap<>() {{
                put(ChartType.COLUMN, "COL");                                            // NO I18N
                put(ChartType.STACKED_COLUMN, "STACKEDCOL");                             // NO I18N
                put(ChartType.STACKED_PERCENT_COLUMN, "STACKEDCOL_PERCENT");             // NO I18N
                put(ChartType.GROUPED_COLUMN, "GROUPEDCOL");                             // NO I18N
                put(ChartType.BAR, "BAR");                                               // NO I18N
                put(ChartType.STACKED_BAR, "STACKEDBAR");                                // NO I18N
                put(ChartType.STACKED_PERCENT_BAR, "STACKEDBAR_PERCENT");                // NO I18N
                put(ChartType.GROUPED_BAR, "GROUPEDBAR");                                // NO I18N
                put(ChartType.RACE_BAR, "ANIMATION_CHART");                              // NO I18N
                put(ChartType.AREA, "XYAREA");                                           // NO I18N
                put(ChartType.AREA_SPLINE, "XYAREA_AREA_SPLINE");                        // NO I18N
                put(ChartType.STACKED_AREA, "XYSTACKEDAREA");                            // NO I18N
                put(ChartType.STACKED_PERCENT_AREA, "XYSTACKEDAREA_PERCENT");            // NO I18N
                put(ChartType.AREA_TIME_SERIES, "TIMEAREA");                             // NO I18N
                put(ChartType.PIE, "PIE");                                               // NO I18N
                put(ChartType.SEMI_PIE, "SEMIPIE");                                      // NO I18N
                put(ChartType.DONUT, "DOUGHNUT");                                        // NO I18N
                put(ChartType.SEMI_DONUT, "SEMIDOUGHNUT");                               // NO I18N
                put(ChartType.LINE, "XYLINE");                                           // NO I18N
                put(ChartType.SPLINE, "SPLINE");                                         // NO I18N
                put(ChartType.STEP_LINE, "STEPCHART");                                   // NO I18N
                put(ChartType.LINE_TIME_SERIES, "TIMELINE");                             // NO I18N
                put(ChartType.SCATTER, "SCATTER");                                       // NO I18N
                put(ChartType.SCATTER_LINE, "XYLINE_SCATTER");                           // NO I18N
                put(ChartType.SCATTER_LINE_MARKERS, "XYLINE_SCATTER_SHAPES");            // NO I18N
                put(ChartType.BULLET_COLUMN, "BULLETCOL");                               // NO I18N
                put(ChartType.BULLET_BAR, "BULLETBAR");                                  // NO I18N
                put(ChartType.FUNNEL, "FUNNEL");                                         // NO I18N
                put(ChartType.WEIGHTED_FUNNEL, "WEIGHTED_FUNNEL");                       // NO I18N
                put(ChartType.BUBBLE, "BUBBLECHART");                                    // NO I18N
                put(ChartType.HISTOGRAM, "HISTOGRAM");                                   // NO I18N
                put(ChartType.WORD_CLOUD, "WORDCLOUD");                                  // NO I18N
                put(ChartType.COMBO, "COMBOCHART");                                      // NO I18N
                put(ChartType.DONUT_PARLIAMENT, "ITEM");                                 // NO I18N
                put(ChartType.PIE_PARLIAMENT, "PIE_ITEM");                               // NO I18N
                put(ChartType.CANDLE_STICK, "CANDLESTICK");                              // NO I18N
                put(ChartType.OHLC, "OHLCCHART");                                        // NO I18N
                put(ChartType.POLAR, "POLAR");                                           // NO I18N
                put(ChartType.SPIDER_WEB, "SPIDERWEB");                                  // NO I18N
                put(ChartType.PARETO, "PARETO");                                         // NO I18N
                put(ChartType.WATERFALL, "WATERFALL");                                   // NO I18N
                put(ChartType.BOXPLOT_COLUMN, "BOX_PLOT");                               // NO I18N
                put(ChartType.BOXPLOT_BAR, "BOX_PLOT_H");                                // NO I18N
            }});
        }
        return newToOldChartTypeMap.getOrDefault(chartType, "COL");                                // NO I18N
    }

    /* Mapping from old chart marker shape index to new chart marker shape*/
    private static MarkerShapeType[] oldToNewMarkerShapeTypeMap;

    /**
     * Retrieves new chart marker shape type for the given old chart marker shape index
     *
     * @param oldMarkerShapeIndex Old chart marker shape index
     * @return New chart marker shape type
     */
    public static MarkerShapeType getNewMarkerShapeType(int oldMarkerShapeIndex){
        if(oldToNewMarkerShapeTypeMap == null){
            oldToNewMarkerShapeTypeMap = new MarkerShapeType[]{MarkerShapeType.CIRCLE, MarkerShapeType.SQUARE, MarkerShapeType.DIAMOND,
                    MarkerShapeType.TRIANGLE, MarkerShapeType.TRIANGLE_DOWN};
        }

        return oldToNewMarkerShapeTypeMap[oldMarkerShapeIndex];
    }

    /* mapping from old chart data label position index to new chart data label position */
    private static DataLabelPositionType[] oldToNewDataLabelsPositionMap;

    /**
     * Retrieves new chart data labels position for the given old chart data labels position
     * @param oldPositionIndex Old chart position
     * @return New chart position type
     */
    public static DataLabelPositionType getNewDataLabelsPosition(int oldPositionIndex){
        if(oldToNewDataLabelsPositionMap == null){
            oldToNewDataLabelsPositionMap = new DataLabelPositionType[] {DataLabelPositionType.AUTO, DataLabelPositionType.INSIDE_END,
                    DataLabelPositionType.INSIDE_MIDDLE, DataLabelPositionType.INSIDE_BASE, DataLabelPositionType.OUTSIDE_END,
                    DataLabelPositionType.AUTO, DataLabelPositionType.TOP, DataLabelPositionType.CENTER, DataLabelPositionType.BOTTOM,
                    DataLabelPositionType.LEFT, DataLabelPositionType.RIGHT, DataLabelPositionType.AUTO, DataLabelPositionType.INSIDE,
                    DataLabelPositionType.OUTSIDE};

        }

        return oldToNewDataLabelsPositionMap[oldPositionIndex];
    }
    /* mapping from old chart trend line type to new chart trend line type */
    private static Map<String, TrendLineType> oldToNewTrendLineMap;

    /**
     * Retrieves new chart trend line type for the given old chart trend line type
     * @param oldTrendLine old chart trend line type
     * @return new chart trend line type
     */
    public static TrendLineType getNewTrendLineType(String oldTrendLine){
        if(oldToNewTrendLineMap == null){
            oldToNewTrendLineMap = Collections.unmodifiableMap(new HashMap<>() {{
                put("average", TrendLineType.MOVING_AVERAGE);               // NO I18N
                put("linear", TrendLineType.LINEAR);                        // NO I18N
                put("power", TrendLineType.POWER);                          // NO I18N
                put("exponential", TrendLineType.EXPONENTIAL);              // NO I18N
                put("logarithmic", TrendLineType.LOGARITHMIC);              // NO I18N
                put("polynomial", TrendLineType.POLYNOMIAL);                // NO I18N
                put("Polynomial", TrendLineType.POLYNOMIAL);                // NO I18N
            }});
        }
        return oldToNewTrendLineMap.get(oldTrendLine);
    }

    public static String decodeURI(String encodedString){
        if(encodedString == null) { return null; }
        try{
            return URLDecoder.decode(encodedString, String.valueOf(StandardCharsets.UTF_8));
        }catch (IllegalArgumentException | UnsupportedEncodingException e){
            return encodedString;
        }
    }

    public static String doubleDecode(String doubleEncodedString){
        return decodeURI(decodeURI(doubleEncodedString));
    }


    public static SeriesInType getSeriesInTypeForOldSeriesIn(String seriesIn){
        if(seriesIn == null){
            return null;
        }
        return Objects.equals(seriesIn, "COLS") ? SeriesInType.COLUMNS : SeriesInType.ROWS;
    }

    public static Boolean toBoolean(Object object){
        if(object instanceof String){
            return Boolean.parseBoolean((String) object);
        }else if(object instanceof Boolean){
            return (Boolean) object;
        }else{
            return null;
        }
    }

    public static Double toDouble(Object object){
        if(object instanceof String){
            return safeParseToDouble((String) object);
        }else if(object instanceof Number){
            return ((Number) object).doubleValue();
        }else{
            return null;
        }
    }

    public static Integer toInteger(Object object){
        if(object instanceof String){
            return safeParseToInteger((String) object);
        }else if(object instanceof Number){
            return ((Number) object).intValue();
        } else{
            return null;
        }
    }

    public static Double safeParseToDouble(String string) {
        try {
            return Double.valueOf(string);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public static Integer safeParseToInteger(String string) {
        try {
            return Integer.valueOf(string);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * Method to extract Data format from ZSPattern
     * @param pattern ZSPattern instance
     * @return Date format String
     */
    public static String getDateFormat(Cell.Type type, ZSPattern pattern, SpreadsheetSettings settings, boolean hasMilliSeconds){
        if(pattern == null) {
           return getDateFormatFromSpreadSheetSettings(settings, type, hasMilliSeconds);
        }
        String patternString = pattern.toPatternString();
        /* example conditional patterns
            1. [>=0]dd/mm/yyyy;@
            2. [>=0]MMMM d, y;[<=1000]dd/mm/yyyy;@
         */
        if(pattern.isConditionalPattern()){
            String[] patternComponents = patternString.split(";");                                                      // NO I18N
            String firstPattern = patternComponents[0];

            String[] conditionSplit = firstPattern.split("]");                                                          // NO I18N
            patternString = conditionSplit[conditionSplit.length - 1];
        }

        if(patternString == null || patternString.isEmpty()){
            return getDateFormatFromSpreadSheetSettings(settings, type, hasMilliSeconds);
        }

        return patternString;
    }

    public static Cell.Type getCellTypeByPriority(Cell.Type patternType, Cell.Type valueType) {
        if(patternType == null || patternType == Cell.Type.UNDEFINED) {
            return valueType;
        }
        return patternType;
    }

    public static String getDateFormatFromSpreadSheetSettings(SpreadsheetSettings settings, Cell.Type type, boolean hasMilliSeconds){
        return settings.getDateTimeFormat(type, hasMilliSeconds)
                .toPattern(new DecimalFormatSymbols(settings.getLocale()), false);
    }

    /**
     * Retrieves opacity from the given color string.
     * Note: opacity can be only retrieved from RGBA string, otherwise it will return 1 as opacity.
     * @throws NullPointerException When color String is null
     * @param color Color String value
     * @return Opacity value.
     */
    public static double getColorOpacity(String color){
        Objects.requireNonNull(color);
        Pattern pattern = Pattern.compile(RGBA_REGEX);
        Matcher matcher = pattern.matcher(color);

        if(matcher.matches()){
            return Double.parseDouble(matcher.group(4));
        }else{
            return 1d;
        }
    }

    public static String getColorFromTheme(ZSTheme theme, String accent, Double tonePercent){
        if(accent == null){ return null; }
        Map<ZSColorScheme.Colors, String> colorsMap = theme.getColorScheme().getColors();
        ZSColorScheme.Colors colors = ZSColorScheme.Colors.valueOf(accent);

        String baseColor = colorsMap.get(colors);
        return tonePercent == null ? baseColor : "#".concat(ZSColor.getTintAppliedHex(baseColor.substring(1), tonePercent));        // NO I18N
    }

    public static List<String> getAccentColorsTheme(Workbook workbook) {
        ZSTheme theme = workbook.getTheme();
        Map<ZSColorScheme.Colors, String> colorsMap = theme.getColorScheme().getColors();
        return new ArrayList<>() {{
            add(colorsMap.get(ZSColorScheme.Colors.ACCENT1));
            add(colorsMap.get(ZSColorScheme.Colors.ACCENT2));
            add(colorsMap.get(ZSColorScheme.Colors.ACCENT3));
            add(colorsMap.get(ZSColorScheme.Colors.ACCENT4));
            add(colorsMap.get(ZSColorScheme.Colors.ACCENT5));
            add(colorsMap.get(ZSColorScheme.Colors.ACCENT6));
        }};
    }

    public static String getThemeBodyFontName(Workbook workbook) {
        ZSTheme theme = workbook.getTheme();

        return theme.getFontScheme().getFonts().get(ZSFontScheme.Fonts.BODY);
    }

    public static String getChartOrThemeBodyFontName(JSONObjectWrapper chartMetaJSON, Workbook workbook) {
        return chartMetaJSON.getJSONObject(ChartActionConstants.JSONConstants.API_META)
                .getJSONObject(ChartActionConstants.JSONConstants.CHART_META)
                .getJSONObject(FrameworkChartMetaConstants.ChartMeta.Chart.KEY)
                .optString(FrameworkChartMetaConstants.ChartMeta.Chart.FONT_FAMILY, getThemeBodyFontName(workbook));
    }

    public static int getSeriesCountForPivotChart(Workbook workbook, SheetMeta sheetMeta, String chartType) {
        List<String> dataSources = SheetChartAPI.getDataSources(sheetMeta);

        if(dataSources.isEmpty()) { return 0; }
        String pivotID = dataSources.get(0);
        JSONObjectWrapper data = PivotChartUtil.getSeriesData(workbook.getSheet(0), pivotID, chartType, false, null, true);
        return data.getJSONArray(com.zoho.sheet.knitcharts.constants.ChartConstants.PivotChart.SERIES).length();
    }

    public static List<Range> toRanges(Workbook workbook, List<String> dataSources){
        return dataSources.stream()
                .map(dataSource -> {
                    try {
                        return new Range(workbook, dataSource, null, CellReference.ReferenceMode.A1, false);
                    } catch (SheetEngineException e) {
                        throw new ChartException(e.getMessage());
                    }
                }).collect(Collectors.toList());
    }

    /**
     * Should be used from only old chart conversion logic
     * @param workbook Work book instance
     * @param sheetMeta Sheet meta instance
     * @return series count
     */
    public static int getSeriesCount(Workbook workbook, SheetMeta sheetMeta, String oldChartType){
        if(SheetChartAPI.isPivotChart(sheetMeta)) {
            return getSeriesCountForPivotChart(workbook, sheetMeta, oldChartType);
        }
        List<String> dataSources = SheetChartAPI.getDataSources(sheetMeta);
        SeriesInType seriesInType = SheetChartAPI.getSeriesIn(sheetMeta);
        boolean fcl = SheetChartGetterAPI.getFirstColLabel(sheetMeta);
        boolean frl = SheetChartGetterAPI.getFirstRowLabel(sheetMeta);
        int seriesCount;
        DataTable dataTable = DataTableUtils.createTempReferenceDataTable(workbook, dataSources);
        List<List<DataTableCell>> data = dataTable.getData(workbook, sheetMeta, ChartUtils.getChartForOldChartName(oldChartType));

        if(data.isEmpty()) { return 0; }

        if(seriesInType == SeriesInType.COLUMNS){
            seriesCount = data.get(0).size();
            seriesCount -= fcl ? 1 : 0;
        }else{
            seriesCount = data.size();
            seriesCount -= frl ? 1 : 0;
        }
        return seriesCount;
    }

    public static boolean isTargetColorPossible(ChartType chartType){
        return chartType == ChartType.BULLET_BAR || chartType == ChartType.BULLET_COLUMN;
    }

    public static AggregationType getNewAggregationTypeForOldType(String oldAggType) {
        if(oldAggType == null) { return null; }

        switch (oldAggType) {
            case "COUNT": { return AggregationType.COUNT; }                                                                     // NO I18N
            case "COUNT_DISTINCT": { return AggregationType.COUNT_DISTINCT; }                                                   // NO I18N
            case "AVERAGE": { return AggregationType.AVG; }                                                                     // NO I18N
            case "MIN": { return AggregationType.MIN; }                                                                         // NO I18N
            case "MAX": { return AggregationType.MAX; }                                                                         // NO I18N
            case "MEDIAN": { return AggregationType.MEDIAN; }                                                                   // NO I18N
            case "ACTUAL": { return AggregationType.ACTUAL; }                                                                   // NO I18N
            case "NONE" : { return null; }
            default: { return AggregationType.SUM; }
        }
    }

    public static GroupingType getNewGroupingTypeForOldType(String oldGroupingType) {
        if(oldGroupingType == null || oldGroupingType.equals("NONE")) { return null; }                                          // NO I18N

        switch (oldGroupingType) {
            case "DATE-QUARTER": { return GroupingType.QUARTER; }                                                               // NO I18N
            case "DATE-YEAR": { return GroupingType.YEAR; }                                                                     // NO I18N
            case "DATE-MONTH": { return GroupingType.MONTH; }                                                                   // NO I18N
            case "DATE-DAY": { return GroupingType.DAY; }                                                                       // NO I18N
            case "DATE-DAY_OF_WEEK": { return GroupingType.DAY_OF_WEEK; }                                                       // NO I18N
            case "DATE-QUARTER_BY_YEAR": { return GroupingType.QUARTER_BY_YEAR; }                                               // NO I18N
            case "DATE-MONTH_BY_YEAR": { return GroupingType.MONTH_BY_YEAR; }                                                   // NO I18N
            default: { return GroupingType.DEFAULT; }
        }
    }

    public static FilterType getNewFilterTypeForOldType(String oldFilterType) {
        if(oldFilterType == null || oldFilterType.equals("NONE")) { return null; }                                              // NO I18N

        if(oldFilterType.equals("TOP")) { return FilterType.TOP; }                                                              // NO I18N
        else { return FilterType.BOTTOM; }
    }

    public static Tuple<GroupingType, GroupingType> getGroupingTypesFromOldName(String oldGroupings) {
        if(oldGroupings == null || oldGroupings.isEmpty()) { return new Tuple<>(); }

        String[] dateGroupings = oldGroupings.split(",");                                                      //  NO I18N

        GroupingType axisGrouping = dateGroupings.length > 1 ? ChartUtils.getNewGroupingTypeForOldType(dateGroupings[0]) : GroupingType.DEFAULT;
        GroupingType legendGrouping = dateGroupings.length > 2 ? ChartUtils.getNewGroupingTypeForOldType(dateGroupings[1]) : GroupingType.DEFAULT;

        return new Tuple<>(axisGrouping, legendGrouping);
    }

    public static LegendPositionType getNewLegendPositionFromOld(Integer legendPosition) {
        if(legendPosition == null) { return null; }
        switch (legendPosition) {
            case 1: return LegendPositionType.BOTTOM;
            case 2: return LegendPositionType.TOP;
            case 3: return LegendPositionType.RIGHT;
            case 4: return LegendPositionType.LEFT;
        }

        return null;
    }

    public static int getRelativeIndex(Workbook workbook, List<String> dataSources, String colAddress, SeriesInType seriesInType, DataJoinType dataJoinType, String sheetName) {
        if(colAddress == null || colAddress.equals("NONE") || dataSources.isEmpty()) { return -1; }
        try {
            String asn = workbook.getSheet(sheetName).getAssociatedName();
            Reference reference = ReferenceUtils.getReference(workbook, colAddress, true, asn);
            if(reference.getReferenceType() != ReferenceType.RANGE_STRING) { return -1; }
            DataRange colRange = ((RangeReference) reference).getDataRange(workbook);

            if (dataJoinType == DataJoinType.HORIZONTAL) {
               if(seriesInType == SeriesInType.COLUMNS) {
                   return getRelativeIndexForHStackCols(workbook, dataSources, colRange, sheetName);
               }else {
                   return getRelativeIndexForHStackRows(workbook, dataSources, colRange, sheetName);
               }
            } else{
                if(seriesInType == SeriesInType.COLUMNS) {
                    return getRelativeIndexForVStackCols(workbook, dataSources, colRange, sheetName);
                }else {
                    return getRelativeIndexForVStackRows(workbook, dataSources, colRange, sheetName);
                }
            }
        }catch (SheetEngineException e){
            return -1;
        }
    }

    private static int getRelativeIndexForHStackRows(Workbook workbook, List<String> dataSources, DataRange colRange, String sheetName) throws SheetEngineException {
        String asn = workbook.getSheet(sheetName).getAssociatedName();
        Reference reference = ReferenceUtils.getReference(workbook, dataSources.get(0), true, asn);
        if(reference.getReferenceType() != ReferenceType.RANGE_STRING) { return -1; }
        DataRange sourceRange = ((RangeReference) reference).getDataRange(workbook);

        return colRange.getStartRowIndex() - sourceRange.getStartRowIndex();
    }

    private static int getRelativeIndexForHStackCols(Workbook workbook, List<String> dataSources, DataRange colRange, String sheetName) throws SheetEngineException {
        int indexAcc = 0;
        String asn = workbook.getSheet(sheetName).getAssociatedName();
        for(String dataSource: dataSources) {
            Reference reference = ReferenceUtils.getReference(workbook, dataSource, true, asn);
            if(reference.getReferenceType() != ReferenceType.RANGE_STRING) { continue; }
            DataRange sourceRange = ((RangeReference) reference).getDataRange(workbook);

            if((DataTableUtils.isRowIn(sourceRange, colRange) || DataTableUtils.isRowIn(colRange, sourceRange)) &&
                    (DataTableUtils.isColumnIn(sourceRange, colRange) || DataTableUtils.isColumnIn(colRange, sourceRange))) {
                return indexAcc + (colRange.getStartColIndex() - sourceRange.getStartColIndex());
            } else {
                indexAcc += sourceRange.getColSize();
            }
        }

        return -1;
    }

    private static int getRelativeIndexForVStackRows(Workbook workbook, List<String> dataSources, DataRange colRange, String sheetName) throws SheetEngineException {
        int indexAcc = 0;
        String asn = workbook.getSheet(sheetName).getAssociatedName();
        for(String dataSource: dataSources) {
            Reference reference = ReferenceUtils.getReference(workbook, dataSource, true, asn);
            if(reference.getReferenceType() != ReferenceType.RANGE_STRING) { continue; }
            DataRange sourceRange = ((RangeReference) reference).getDataRange(workbook);

            if((DataTableUtils.isRowIn(sourceRange, colRange) || DataTableUtils.isRowIn(colRange, sourceRange)) &&
                    (DataTableUtils.isColumnIn(sourceRange, colRange) || DataTableUtils.isColumnIn(colRange, sourceRange))) {
                return indexAcc + (colRange.getStartRowIndex() - sourceRange.getStartRowIndex());
            } else {
                indexAcc += sourceRange.getRowSize();
            }
        }

        return -1;
    }

    private static int getRelativeIndexForVStackCols(Workbook workbook, List<String> dataSources, DataRange colRange, String sheetName) throws SheetEngineException {
        String asn = workbook.getSheet(sheetName).getAssociatedName();
        Reference reference = ReferenceUtils.getReference(workbook, dataSources.get(0), true, asn);
        if(reference.getReferenceType() != ReferenceType.RANGE_STRING) { return -1; }
        DataRange sourceRange = ((RangeReference) reference).getDataRange(workbook);

        return colRange.getStartColIndex() - sourceRange.getStartColIndex();
    }

    public static GroupingType getOrDefaultGrouping(Integer groupingColumn, GroupingType groupingType) {
        if(groupingType != null) { return groupingType; }

        if(groupingColumn != null) { return GroupingType.DEFAULT; }

        return null;
    }

    /**
     * Method to put an object into the json array if only it is not null
     * @param jsonArray JSONWrapper Array
     * @param object Object to be inserted
     * @return true if the object got insert or false
     */

    public static boolean putIfNotNull(JSONArrayWrapper jsonArray, Object object) {
        if(Objects.nonNull(object)) {
            jsonArray.put(object);
            return true;
        }

        return false;
    }

    public static ColorPaletteType getColorPaletteTypeFromOldName(String oldPaletteName) {
        if(Objects.isNull(oldPaletteName)) { return null; }

        String paletteName = null;
        int lastUnderScoreIdx = oldPaletteName.lastIndexOf("_");                                                        // NO I18N


        if(lastUnderScoreIdx == -1) {
            if(oldPaletteName.equals("SHEET-OFFICE-CUSTOM-THEME")) {                                                            // NO I18N
                return ColorPaletteType.CUSTOM;
            } else {
                return ColorPaletteType.PALETTE_1;
            }
        } else {
            int paletteNumber = Integer.parseInt(oldPaletteName.substring(lastUnderScoreIdx + 1));

            if(oldPaletteName.contains("VARIANT")) {                                                                        // NO I18N
                paletteName = String.format("%s_%d", "palette", (paletteNumber + 1));                                       // NO I18N
            } else if (oldPaletteName.contains("MONOCHROMATIC")) {                                                          // NO I18N
                paletteName = String.format("%s_%d", "monochromatic", paletteNumber);                                       // NO I18N
            }
        }

        return ColorPaletteType.retrieveByValue(paletteName == null ? "custom" : paletteName);                              // NO I18N
    }

    public static Tuple<String, Double> splitColorAndOpacity(String rgbaString) {
        if(Objects.isNull(rgbaString)) { return new Tuple<>(null, 1d); }
        String rgbaPattern = "^rgba?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(0|1|0?\\.\\d+)\\s*\\)$";           // NO I18N
        Pattern pattern = Pattern.compile(rgbaPattern);
        Matcher matcher = pattern.matcher(rgbaString);

        if (matcher.matches()) {
            String r = matcher.group(1);
            String g = matcher.group(2);
            String b = matcher.group(3);
            String a = matcher.group(4);

            String rgb = String.format("rgb(%s, %s, %s)", r, g, b);             // NO I18N
            return new Tuple<>(rgb, Double.parseDouble(a));
        } else {
            return new Tuple<>(rgbaString, 1d);
        }
    }

    public static String getAggregationMessage(SheetMeta sheetMeta, String seriesName) {
        AggregationType aggregationType = SheetChartAPI.getAggregationType(sheetMeta);
        String message;

        switch (aggregationType) {
            case SUM: {
                message = LocaleMsg.getMsg(com.zoho.sheet.knitcharts.constants.ChartConstants.LocaleMessageKeys.SUM);
                break;
            }
            case COUNT: {
                message = LocaleMsg.getMsg(com.zoho.sheet.knitcharts.constants.ChartConstants.LocaleMessageKeys.COUNT);
                break;
            }
            case COUNT_DISTINCT: {
                message = LocaleMsg.getMsg(com.zoho.sheet.knitcharts.constants.ChartConstants.LocaleMessageKeys.COUNT_DISTINCT);
                break;
            }
            case AVG: {
                message = LocaleMsg.getMsg(com.zoho.sheet.knitcharts.constants.ChartConstants.LocaleMessageKeys.AVERAGE);
                break;
            }
            case MIN: {
                message = LocaleMsg.getMsg(com.zoho.sheet.knitcharts.constants.ChartConstants.LocaleMessageKeys.MIN);
                break;
            }
            case MAX: {
                message = LocaleMsg.getMsg(com.zoho.sheet.knitcharts.constants.ChartConstants.LocaleMessageKeys.MAX);
                break;
            }
            case MEDIAN: {
                message = LocaleMsg.getMsg(com.zoho.sheet.knitcharts.constants.ChartConstants.LocaleMessageKeys.MEDIAN);
                break;
            }
            default: {
                message = "{0}";
            }
        }

        return message.replace("{0}", seriesName);
    }

    public static ChartActivityMonitor.ChartActivity getChartActivity(String activityName, String resourceID,
                                                                      String actionOwner, String activityData, String chartID) {
        return ObjectsSupplier.getInstance().getChartActivityMonitor()
                .getChartActivity(activityName, resourceID, actionOwner, activityData, chartID);
    }

    public static void resetStyles(ChartStyleOption...styleOptions) {

        for(ChartStyleOption styleOption: styleOptions) {
            if(styleOption != null) { styleOption.resetStyles(); }
        }
    }


    public static void onChartTypeChange(com.zoho.sheet.knitcharts.chart.chartoption.ChartOption...chartOptions) {
        for(com.zoho.sheet.knitcharts.chart.chartoption.ChartOption chartOption: chartOptions) {
            if(chartOption != null) { chartOption.onChartTypeChange(); }
        }
    }

    public static void onChartTypeChange(com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption...groupedChartOptions) {
        for(com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartOption groupedChartOption: groupedChartOptions) {
            if(groupedChartOption != null) { groupedChartOption.onChartTypeChange(); }
        }
    }

    public static void resetStyles(com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartStyleOption... styleOptions) {
        for(com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartStyleOption styleOption: styleOptions) {
            if(styleOption != null) {
                styleOption.resetStyles();
            }
        }
    }

    public static void setChartOptions(FunctionSupplier<com.zoho.sheet.knitcharts.chart.chartoption.ChartOption> supplier, JSONWrapper json) {
        if(json != null) {
            supplier.supply().fromJSON(json);
        }
    }

    public static void setGroupedChartOptions(FunctionSupplier<GroupedChartOption> supplier, JSONWrapper json) {
        if(json != null) {
            supplier.supply().getFromJSON(json);
        }
    }

    public static void applyChartStyles(FunctionSupplier<com.zoho.sheet.knitcharts.chart.chartoption.ChartStyleOption> supplier, JSONWrapper json) {
        if(json != null) {
            supplier.supply().applyStyles(json);
        }
    }

    public static void applyGroupedChartStyles(FunctionSupplier<com.zoho.sheet.knitcharts.chart.chartoption.GroupedChartStyleOption> supplier, JSONWrapper json) {
        if(json != null) {
            supplier.supply().applyStyles(json);
        }
    }

    public static String getSheetIDForSheetName(Workbook workbook, String sheetName) {
        return workbook.getSheet(sheetName).getAssociatedName();
    }

    /**
     * Method to get reference sheet associated names for the charts in the given source sheet.
     * @param workbook WorkBook instance
     * @param sourceSheetASN Source Sheet name
     * @return list of reference sheet associated names.
     */
    public static List<String> getReferenceSheetNames(Workbook workbook, String sourceSheetASN) {
        ChartContainer chartContainer = workbook.getChartsContainer();
        List<String> referenceSheets = new ArrayList<>();

        chartContainer.forEachChart(sourceSheetASN, chart -> {
            ReferencePool referencePool = chartContainer.getReferencePool();

            if(chart.isPivotChart()) {
                String pivotChartID = SheetChartGetterAPI.getDataSources(chart.getSheetMeta()).get(0);
                PivotTable pivotTable = workbook.getDataPivotTable(pivotChartID);
                String targetASN = pivotTable.getTargetCellRange().getAssociatedSheetName();
                String sourceASN = pivotTable.getSourceCellRange().getAssociatedSheetName();

                if(!sourceSheetASN.equals(targetASN)) { referenceSheets.add(targetASN); }
                if(!sourceSheetASN.equals(sourceASN)) { referenceSheets.add(sourceASN); }

            }

            for(Reference reference: referencePool.getReferences(chartContainer.getReferenceID(chart.getChartID()))) {
                List<DataRange> dataRanges = ReferenceUtils.getAssociatedDataRanges(workbook, reference);
                referenceSheets.addAll(dataRanges.stream().map(DataRange::getAssociatedSheetName).collect(Collectors.toList()));
            }

        });

        return referenceSheets;
    }


    public static <T> Map<String, T> convertJSONObjectToMap(JSONObjectWrapper jsonObject) {
        Map<String, T> map = new HashMap<>();

        ChartUtils.forEachKey(jsonObject, key -> map.put(key, ChartUtils.typeCast(jsonObject.get(key))));

        return map;
    }

    public static List<ModifiedChartPOJO> clubModifiedCharts(List<ModifiedChartPOJO> modifiedChartPOJOS) {
        List<ModifiedChartPOJO> clubModifiedCharts = new ArrayList<>();

        Map<String, Map<ModifiedChartPOJO.ModificationType, Map<Key, List<ModifiedChartPOJO>>>> result =
                modifiedChartPOJOS.stream().collect(Collectors.groupingBy(ModifiedChartPOJO::getModifiedChartId,
                        Collectors.groupingBy(ModifiedChartPOJO::getModificationType, Collectors.groupingBy(ModifiedChartPOJO::getKey))));

        result.forEach((chartID, modificationTypeMap) -> modificationTypeMap.forEach((modificationType, keyBasedMap) -> {
            keyBasedMap.forEach((key, bean) -> {
                if(!bean.isEmpty()) {
                    bean.sort(Comparator.comparing(ModifiedChartPOJO::getModificationConstants));
                    clubModifiedCharts.add(bean.get(0));
                }
            });
        }));

        return clubModifiedCharts;
    }

    public static boolean isSingleCell(Range range) {
        return range.getEndColIndex() == range.getStartColIndex() && range.getEndRowIndex() == range.getStartRowIndex();
    }

    public static boolean isTextReference(String referenceString) {
        if(referenceString == null) { return false; }

        return referenceString.startsWith("=");
    }

    public static <T> T requireNonNullElse(T obj, T def) {
        return obj == null ? def : obj;
    }

    public static <T> T requireNonNullElseGet(T obj, FunctionSupplier<T> supplier) {
        return obj == null ? supplier.supply() : obj;
    }

    public static boolean isSingleSeriesChart(ChartType chartType) {
        switch (chartType) {
            case FUNNEL:
            case PIE:
            case SEMI_PIE:
            case DONUT:
            case SEMI_DONUT:
            case PIE_PARLIAMENT:
            case DONUT_PARLIAMENT:
            case WATERFALL:
            case WEIGHTED_FUNNEL:
                return true;
        }
        return false;
    }

    private static void _getDataRangeFromNode(Workbook workbook, Node node, List<DataRange> dataRanges) {
        if(node instanceof ASTRangeNode) {
            ASTRangeNode rangeNode = typeCast(node);
            ASTVarNode startCellNode = typeCast(rangeNode.jjtGetChild(0)), endCellNode = typeCast(rangeNode.jjtGetChild(1));
            DataRange range = ChartUtils.getDataRange(rangeNode.getASN(), startCellNode.getRowValue(), startCellNode.getColValue(),
                    endCellNode.getRowValue(), endCellNode.getColValue());

            dataRanges.add(range);
        } else if(node instanceof ASTVarNode) {
            ASTVarNode cellNode = typeCast(node);
            if(cellNode.getASN() != null && cellNode.getRowValue() >= 0 && cellNode.getColValue() >= 0) {
                DataRange range = ChartUtils.getDataRange(cellNode.getASN(), cellNode.getRowValue(),
                        cellNode.getColValue(), cellNode.getRowValue(), cellNode.getColValue());
                dataRanges.add(range);
            } else {
                NamedExpression expression = workbook.getNamedExpression(cellNode.getName());
                if(expression != null) { _getDataRangeFromNode(workbook, expression.getNode(), dataRanges);  }
            }
        } else {
            for(int idx = 0; idx < node.jjtGetNumChildren(); idx++) {
                _getDataRangeFromNode(workbook, node.jjtGetChild(idx), dataRanges);
            }
            Object result = DataUtils.evaluateNode(workbook, node);
            if(result instanceof Range) {dataRanges.add(((Range) result).toDataRange());}
        }
    }

    public static List<DataRange> getDataRangesFromNode(Workbook workbook, Node node) {
        ChartActivityMonitor.ChartActivity chartActivity = ChartUtils.getChartActivity("GET_DATA_FROM_NODE", null, null, null, null);     // NO I18N
        try {
            List<DataRange> dataRanges = new ArrayList<>();
            _getDataRangeFromNode(workbook, node, dataRanges);

            return RangeUtil.mergeDataRanges(dataRanges);
        } catch (Exception e) {
            chartActivity.failed(e);
            return Collections.emptyList();
        }
    }

    public static ZSColor getPrimaryColortFromTableStyle(TableStyle tableStyle) {
        for(TableStyle.TableStylePropertyKey key : TableStyle.TableStylePropertyKey.values()) {
            ZSColor color = getColorFromTableStyleProperty(tableStyle, key);
            if(color != null) { return color; }
        }
        return null;
    }

    public static ZSColor getColorFromTableStyleProperty(TableStyle tableStyle, TableStyle.TableStylePropertyKey propertyKey) {
        CellStyle style = tableStyle.get(propertyKey).getCellStyle();

        return getPrimaryColorFromCellStyle(style);
    }

    public static ZSColor getPrimaryColorFromCellStyle(CellStyle cellStyle) {
        CellStyleProperties cellStyleProperties = cellStyle.getCellStyleProperties();
        for(CellStyleProperties.Property property: CellStyleProperties.Property.values()) {
            ZSColor color = getColorFromCellStyleProperty(cellStyleProperties, property);
            if(color != null) { return color; }
        }

        return null;
    }

    public static ZSColor getColorFromCellStyleProperty(CellStyleProperties cellStyle, CellStyleProperties.Property property) {
        Object propertyObj = cellStyle.getProperty(property);
        if(propertyObj instanceof ZSColor) {
            return ChartUtils.typeCast(propertyObj);
        } else if(propertyObj instanceof CellStyleProperties.BorderBean) {
            CellStyleProperties.BorderBean borderBean = (CellStyleProperties.BorderBean) propertyObj;
            return getColorFromBorderBean(borderBean);
        } else if(propertyObj instanceof Fill) {
            Fill fill = (Fill) propertyObj;
            return fill.getMajorColor();
        }
        return null;
    }

    public static ZSColor getColorFromBorderBean(CellStyleProperties.BorderBean borderBean) {
        if(borderBean != null) {
            if(borderBean.getBorderTop() != null) {
                return borderBean.getBorderBottom().getColor();
            } else if(borderBean.getBorderLeft() != null) {
                return borderBean.getBorderLeft().getColor();
            } else if(borderBean.getBorderRight() != null) {
                return borderBean.getBorderRight().getColor();
            } else if(borderBean.getBorderBottom() != null) {
                return borderBean.getBorderBottom().getColor();
            } else if(borderBean.getDiagonalBLTR() != null) {
                return borderBean.getDiagonalBLTR().getColor();
            } else if(borderBean.getDiagonalTLBR() != null) {
                return borderBean.getDiagonalTLBR().getColor();
            } else if(borderBean.getBorderInnerHorizontal() != null) {
                return borderBean.getBorderInnerHorizontal().getColor();
            } else if(borderBean.getBorderInnerVertical() != null) {
                return borderBean.getBorderInnerVertical().getColor();
            }
        }
        return null;
    }

    public static Double getStartRow(Sheet sheet, double top) {
        int rowIdx = 0;
        double currentTop = top;
        while(currentTop > 0) {
            currentTop -= sheet.getRow(rowIdx).getRowHeight();
            rowIdx++;
        }
        if(currentTop < 0) {
            rowIdx -= 1;
        }

        return (double) rowIdx;
    }

    public static Double getStartRowDiff(Sheet sheet, double top) {
        int rowIdx = 0;
        double rowDiff = 0;
        double currentTop = top;
        while(currentTop > 0) {
            currentTop -= sheet.getRow(rowIdx).getRowHeight();
            rowIdx++;
        }
        if(currentTop < 0) {
            rowIdx -= 1;
            rowDiff = currentTop + sheet.getRow(rowIdx).getRowHeight();
        }

        return rowDiff;
    }

    public static Double getStartColumn(Sheet sheet, double left) {
        int colIdx = 0;
        double currentLeft = left;
        while(currentLeft > 0) {
            currentLeft -= sheet.getColumnHeader(colIdx).getColumnWidth();
            colIdx++;
        }
        if(currentLeft < 0) {
            colIdx -= 1;
        }

        return (double) colIdx;
    }

    public static Double getStartColumnDiff(Sheet sheet, double left) {
        int colIdx = 0;
        double colDiff = 0;
        double currentLeft = left;
        while(currentLeft > 0) {
            currentLeft -= sheet.getColumnHeader(colIdx).getColumnWidth();
            colIdx++;
        }
        if(currentLeft < 0) {
            colIdx -= 1;
            colDiff = currentLeft + sheet.getColumnHeader(colIdx).getColumnWidth();
        }

        return colDiff;
    }

    public static int clamp(int min, int max, int value) {
        return Math.max(Math.min(value, max), min);
    }

    public static DataRange getDataRange(String asn, int startRow, int startCol, int endRow, int endCol) {
        int clampedSR = clamp(0, Utility.MAXNUMOFROWS - 1, startRow);
        int clampedER = clamp(0, Utility.MAXNUMOFROWS - 1, endRow);
        int clampedSC = clamp(0, Utility.MAXNUMOFCOLS - 1, startCol);
        int clampedEC = clamp(0, Utility.MAXNUMOFCOLS - 1, endCol);

        return new DataRange(asn, clampedSR, clampedSC, clampedER, clampedEC);
    }

    public static boolean isNonEmptyString(String str) {
        return str != null && !str.isEmpty();
    }

    public static String updateStopsColorFromStopsThemes(ZSTheme theme, String stopsString, String stopsThemeString) {
        if(stopsString == null || stopsThemeString == null) { return null; }
        JSONArrayWrapper stops = new JSONArrayWrapper(stopsString);
        JSONArrayWrapper stopsTheme = new JSONArrayWrapper(stopsThemeString);

        for(int idx = 0; idx < stopsTheme.length(); idx++){
            JSONObjectWrapper stopTheme = stopsTheme.getJSONObject(idx);
            JSONObjectWrapper stop = stops.optJSONObject(idx);

            if(stop == null) { continue; }
            String accent = ChartUtils.optStringFromJSONObject(stopTheme, SheetChartMetaConstants.CustomProps.ColorThemeOptions.ACCENT);
            Double tone = ChartUtils.optDoubleFromJSONObject(stopTheme, SheetChartMetaConstants.CustomProps.ColorThemeOptions.TONE);
            String color =  ChartUtils.getColorFromTheme(theme, accent, tone);

            if(color == null) { continue; }
            stop.put(FrameworkChartMetaConstants.ChartMeta.CommonKeys.COLOR, color);
        }

        return Objects.toString(stops);
    }

    public static boolean isValidColor(String color) {
        if(color == null || color.isEmpty()) { return false; }
        return getHexColorPattern().matcher(color).matches() ||
                getRGBAPattern().matcher(color).matches() ||
                getRGBPattern().matcher(color).matches();
    }

    public static Pattern getRGBAPattern() {
        if(rgbaPattern == null) {
            rgbaPattern = Pattern.compile(RGBA_REGEX);
        }
        return rgbaPattern;
    }

    public static Pattern getHexColorPattern() {
        if(hexColorPattern == null) {
            hexColorPattern = Pattern.compile(HEX_COLOR_REGEX);
        }
        return hexColorPattern;
    }

    public static Pattern getRGBPattern() {
        if(rgbPattern == null) {
            rgbPattern = Pattern.compile(RGB_REGEX);
        }
        return rgbPattern;
    }

    /**
     * Method to apply custom base 64 encoding.
     * This method will apply base 64 encoding to the given string and append a custom prefix
     * @param str String to be encoded.
     * @return Custom base 64 encoded string
     */
    public static String base64Encode(String str) {
        return String.format("%s%s", com.zoho.sheet.knitcharts.constants.ChartConstants.BASE64_PREFIX, Base64.getUrlEncoder().encodeToString(str.getBytes()));     // NO I18N
    }

    public static List<String> base64Encode(List<String> strings) {
        return strings.stream().map(ChartUtils::base64Encode).collect(Collectors.toList());
    }

    /**
     * Method to apply custom base 64 decoding.
     * This method will apply base 64 decoding to the given string after removing the custom prefix
     * @param str String to be decoded.
     * @return Custom base 64 decoded String
     */
    public static String base64Decode(String str) {
        try {
            if (str == null) {
                return null;
            } else if (str.startsWith(com.zoho.sheet.knitcharts.constants.ChartConstants.BASE64_PREFIX)) {
                String prefixRemoved = str.substring(ChartConstants.BASE64_PREFIX.length());
                return new String(Base64.getUrlDecoder().decode(prefixRemoved));
            }
        }catch (Exception e) {
            return str;
        }
        return str;
    }

    public static List<String> base64Decode(List<String> strings) {
        return strings.stream().map(ChartUtils::base64Decode).collect(Collectors.toList());
    }
}
