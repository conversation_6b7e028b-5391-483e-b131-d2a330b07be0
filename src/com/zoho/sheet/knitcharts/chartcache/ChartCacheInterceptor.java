package com.zoho.sheet.knitcharts.chartcache;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.zoho.cache.cg.RedisConnectionPoolHandler;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ChartCacheException;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import redis.clients.jedis.Jedis;

import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ChartCacheInterceptor {

    private static final Logger LOGGER = Logger.getLogger(ChartCacheInterceptor.class.getName());

    private static final long RECOMMENDATION_CACHE_EXPIRE_TIME = 600L;      // 10 minutes in seconds

    private static final String CHART_ID = "chartID";                                                                       // NO I18N

    private static final String THIRD_GEN_CHARTS_ENABLED_KEY = "thirdGenChart";                                             // NO I18N

    private static final String ZUID_FIELD = "zuids";                                                                          // NO I18N

    private static final String ORG_FIELD = "org";                                                                          // NO I18N

    public static Long cacheRecommendations(String userZUID, String rID, String asn, List<JSONObjectWrapper> recommendedCharts) {
        try {
            long timeStamp = System.currentTimeMillis();
            String key = getKeyForRecommendationCaching(userZUID, rID, asn, timeStamp);
            hSet(key, recommendedCharts);
            return timeStamp;
        } catch (ChartCacheException e) {
            LOGGER.log(Level.INFO, "[CHART_RECOMMENDATION]: Recommendation caching failed.", e);                            // NO I18N
            return null;
        }
    }

    public static void cache(String key, String data, int expireTime) {
        try {
            RedisHelper.set(key, data, expireTime);
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "[CHART_STYLES]: Chart Styles caching failed.", e);                            // NO I18N
        }
    }

    public static String getCache(String key) {
        try {
            return RedisHelper.get(key, -1);
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "[CHART_STYLES]: Chart Styles caching fetch failed.", e);                            // NO I18N
            return null;
        }
    }

    public static void cacheChartStyles(JSONObjectWrapper stylesJSON, String stylesID) {
        try {
            String key = getKeyForStylesCaching(stylesID);
            RedisHelper.set(key, stylesJSON.toString(), RedisHelper.COPY_CHART_STYLE_EXPIRE_TIME);
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "[CHART_STYLES]: Chart Styles caching failed.", e);                            // NO I18N
        }
    }

    public static JSONObjectWrapper getCacheChartStyles(String stylesID) {
        try {
            String key = getKeyForStylesCaching(stylesID);
            return new JSONObjectWrapper(RedisHelper.get(key, -1));
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "[CHART_STYLES]: Chart Styles caching fetch failed.", e);                            // NO I18N
            return null;
        }
    }

    public static JSONObjectWrapper getCachedRecommendedChart(String userZUID, String rID, String asn, String chartID, long timeStamp) {
        String key = getKeyForRecommendationCaching(userZUID, rID, asn, timeStamp);
        return hGet(key, chartID);
    }

    public static void clearRecommendationCache(String userZUID, String rID, String asn, long timeStamp) {
        try{
            RedisHelper.del(getKeyForRecommendationCaching(userZUID, rID, asn, timeStamp));
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "[CHART_RECOMMENDATION]: Unable to clear cache.", e);                            // NO I18N
        }
    }

    private static String getKeyForRecommendationCaching(String userZUID, String rID, String asn, long timeStamp) {

        return String.format("%s:%s:%s:%s", userZUID, rID, asn, timeStamp);                                              // NO I18N
    }

    private static String getKeyForStylesCaching(String stylesID) {
        return String.format("%s:%s", RedisHelper.COPY_CHART_STYLE, stylesID);          // NO I18N
    }

    private static void hSet(String key, List<JSONObjectWrapper> recommendedChartLists) {
        Jedis jedis = null;
        try {
            jedis = RedisConnectionPoolHandler.getRedisConnection(RedisHelper.REDIS_POOL_NAME);

            for(JSONObjectWrapper recommendedChart: recommendedChartLists) {
                String field = recommendedChart.getString(CHART_ID);

                jedis.hset(key, field, prepareRecommendationMetaForCaching(recommendedChart).toString());
            }
            jedis.expire(key, RECOMMENDATION_CACHE_EXPIRE_TIME);
        } catch (Exception e) {
            throw new ChartCacheException(e.getMessage());
        } finally {
            if(jedis != null) { RedisConnectionPoolHandler.returnRedis(jedis); }
        }
    }

    private static JSONObjectWrapper hGet(String key, String field) {
        try {
            return new JSONObjectWrapper(RedisHelper.hget(key, field));
        }catch (Exception e) {
            throw new ChartCacheException(e.getMessage());
        }
    }



    private static JSONObjectWrapper prepareRecommendationMetaForCaching(JSONObjectWrapper recommendedChart) {
        JSONObjectWrapper apiMeta = recommendedChart.getJSONObject(ChartActionConstants.JSONConstants.API_META);
        JSONObjectWrapper chartMeta = apiMeta.getJSONObject(ChartActionConstants.JSONConstants.CHART_META);
        JSONObjectWrapper sheetMeta = recommendedChart.getJSONObject(ChartActionConstants.JSONConstants.SHEET_META);
        JSONObjectWrapper metaForCaching = new JSONObjectWrapper();


        metaForCaching.put(ChartActionConstants.JSONConstants.CHART_META, chartMeta)
                .put(ChartActionConstants.JSONConstants.SHEET_META, sheetMeta);

        return metaForCaching;
    }

    public static void cachePublicChartDetails(JSONObjectWrapper chartMetaJSON, String chartID) {
        try {
            RedisHelper.set(getKeyForPublicCaching(chartID),
                    chartMetaJSON.toString(), RedisHelper.PUBLISH_CHART_EXPIRE_TIME);
        } catch (Exception e) {
            throw new ChartCacheException(e.getMessage());
        }
    }

    public static void cachePublicChartImage(byte[] chartImage, String chartID) {
        String encoded = Base64.getEncoder().encodeToString(chartImage);

        try {
            RedisHelper.set(getKeyForPublicChartImageCache(chartID),
                    encoded, RedisHelper.PUBLISH_CHART_EXPIRE_TIME);
        } catch (Exception e) {
            throw new ChartCacheException(e.getMessage());
        }
    }

    public static void deletePublicChartDetails(String chartID) {
        try {
            RedisHelper.del(getKeyForPublicChartImageCache(chartID));
        } catch (Exception e) {
            throw new ChartCacheException(e.getMessage());
        }
    }

    public static byte[] getPublicChartImage(String chartID) {
        try {
            String encoeded = RedisHelper.get(getKeyForPublicChartImageCache(chartID), -1);
            return Base64.getDecoder().decode(encoeded);
        } catch (Exception e) {
            return null;
        }
    }

    public static String getPublicChartDetails(String chartID) {
        try {
            return RedisHelper.get(getKeyForPublicCaching(chartID), -1);
        } catch (Exception e) {
            return null;
        }
    }

    public static void clearPublicChartDetails(String chartID) {
        try {
            RedisHelper.del(getKeyForPublicCaching(chartID));
        } catch (Exception e) {
            throw new ChartCacheException(e.getMessage());
        }
    }

    private static String getKeyForPublicCaching(String chartID) {
        return String.format("%s%s", RedisHelper.PUBLISH_CHART, chartID);           // NO I18N
    }

    public static String getKeyForPublicChartImageCache(String chartID) {
        return String.format("%s:image:%s", RedisHelper.PUBLISH_CHART, chartID);        // NO I18N
    }

    public static List<String> getThirdGenChartsEnabledZUIDs() {
        return getThirdGenChartsEnabled(ZUID_FIELD);
    }

    public static List<String> getThirdGenChartsEnabledOrgs() {
        return getThirdGenChartsEnabled(ORG_FIELD);
    }

    private static List<String> getThirdGenChartsEnabled(String field) {
        try {
            return ChartUtils.JSONArrayToList(new JSONArrayWrapper(RedisHelper.hget(THIRD_GEN_CHARTS_ENABLED_KEY, field)));
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public static void setThirdGenChartsEnabledZUIDs(JSONArrayWrapper enabledIDs) {
        setThirdGenChartsEnabled(ZUID_FIELD, enabledIDs);
    }

    public static void setThirdGenChartsEnabledOrgs(JSONArrayWrapper enabledIDs) {
        setThirdGenChartsEnabled(ORG_FIELD, enabledIDs);
    }

    private static void setThirdGenChartsEnabled(String field, JSONArrayWrapper enabledIDs) {
        try {
            RedisHelper.hset(THIRD_GEN_CHARTS_ENABLED_KEY, field, enabledIDs.toString());
        } catch (Exception e) {
            throw new ChartCacheException(e.getMessage());
        }
    }

}
