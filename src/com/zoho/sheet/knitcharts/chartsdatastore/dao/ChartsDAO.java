package com.zoho.sheet.knitcharts.chartsdatastore.dao;

import com.adventnet.persistence.DataAccessException;
import com.adventnet.persistence.Persistence;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.ProtoChart;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ChartException;
import com.zoho.sheet.knitcharts.pojo.DocumentSheetPOJO;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.List;

/**
 * Chart's Data Access Object
 * <AUTHOR>
 */
public interface ChartsDAO {

    /**
     * Method to get all the charts associated with this Document ID
     * @param workbook WorkBook instance
     * @return List of an early form of charts
     */
    List<ProtoChart> getCharts(Workbook workbook) throws DataAccessException;

    /**
     * Method to get all the charts associated with this Document ID's provided version
     * @param version Version
     * @param workbook Workbook instance
     * @return List of an early form of charts
     */
    List<ProtoChart> getCharts(String version, Workbook workbook) throws DataAccessException;

    /**
     * Creates new chart in the data space from the given proto chart
     * @param protoChart earliest form of a chart object.
     */
    void createNewChart(ProtoChart protoChart);

    /**
     * Insert a chart in the data space from the given proto chart
     * @param protoChart earliest form of a chart.
     */
    void insertChart(ProtoChart protoChart);

    /**
     * Updates the Chart option in the data space from the given proto chart
     * @param protoChart earliest form of a chart.
     */
    void updateChartOptions(ProtoChart protoChart);

    /**
     * Updates the chart options in the data space from the given chart object
     * @param chart Chart instance
     */
    void updateChartOptions(Workbook workbook, Chart chart);

    /**
     * Method to update list of charts meta into DAO
     * @param chartList List of charts to be updated
     */
    void updateChartsOptions(Workbook workbook, List<Chart> chartList);

    /**
     * Method to update Sheet ID
     */
    void updateSheetID(String chartID, String newSheetName);

    /**
     * Method to update publish details and returns public space chartID
     * @param chartID CHART ID
     * @param publicChartName public chart name
     * @param publicSpace public space name
     * @param timeStamp time when chart made public
     */
    void publishChart(String chartID, String publicChartName, String publicSpace, long timeStamp);

    /**
     * Method un publish chart
     * @param chartID Chart ID for un publish
     */
    void unPublishChart(String chartID);

    /**
     * Deletes the ChartData
     * @param chartID ID of the chart to be deleted
     */
    void deleteChart(String chartID);

    /**
     * Method to get document details by given Chart ID
     * @param chartID CHART ID
     * @return Document Sheet POJO instance
     */
    DocumentSheetPOJO getDocumetDetailsByChartID(String chartID);

    /**
     * Method to check whether the given chart present in the DB
     * @param chartID ID of the chart
     * @return true if the chart is in DB, else false
     */
    boolean isChartPresent(String chartID);

    /**
     * Method to revert the current version charts table
     * @param version version to be reverted
     */
    void revertVersion(String version);

    /**
     * Method to get the corresponding chart ID for external chart ID
     * @param externalChartID External Chart ID (i.e. external chart ID from writer)
     * @return Sheet's Chart ID
     */
    String getChartIDByExternalChartID(String externalChartID);

    /**
     * Method to update the external chart ID
     * @param chartID Chart ID
     * @param newExternalChartID External Chart ID
     */
    void updateChartExternalID(String chartID, String newExternalChartID);

    /**
     * Method to get current version top charts as JSONWrapper Object
     * @return JSONWrapper Object of Charts
     */
    JSONObjectWrapper getCharts();

    /**
     * Method to get charts from the given version
     * @param version Version number
     * @return list of charts from given version
     */
    JSONObjectWrapper getCharts(String version);

    /**
     * Method to get the charts with censoring or excluding. Must not be used for exporting
     * Note: This method should be used only from AdminAction
     * @return list of charts from given version
     */
    JSONObjectWrapper getUncensoredCharts();

    /**
     * Method to get the charts for the given version with censoring or excluding. Must not be used for exporting
     * Note: This method should be used only from AdminAction
     * @param version ZFSNGVersion No.
     * @return list of charts from given version
     */
    JSONObjectWrapper getUncensoredCharts(String version);

    /**
     * Method to get the chart of given ID with censoring or excluding. Must not be used for exporting
     * Note: This method should be used only from AdminAction
     * @return list of charts from given version
     */
    JSONObjectWrapper getUncensoredChart(String chartID);

    /**
     * Method to get the chart of given ID for the given version with censoring or excluding. Must not be used for exporting
     * Note: This method should be used only from AdminAction
     * @param version ZFSNGVersion No.
     * @return list of charts from given version
     */
    JSONObjectWrapper getUncensoredChart(String chartID, String version);

    /**
     * Method to update charts with chart json
     * Note: This method should be used only from AdminAction
     * @param charts JSONObject of charts in ZSheet Format
     */
    void updateCharts(JSONObjectWrapper charts);

    /**
     * Method to update charts with chart json
     * Note: This method should be used only from AdminAction
     * @param chart JSONObject of charts in ChartVersion Format
     */
    void updateChart(JSONObjectWrapper chart);

    /**
     * Method to update charts in ChartVersion table with chart json
     * Note: This method should be used only from AdminAction
     * @param charts JSONObject of charts in ZSheet Format
     * @param version ZFSNGversion no.
     */
    void updateCharts(JSONObjectWrapper charts, String version);

    /**
     * Method to update chart in ChartVersion table with chart json
     * Note: This method should be used only from AdminAction
     * @param chart JSONObject of charts in ChartVersion Format
     * @param version ZFSNGversion no.
     */
    void updateChart(JSONObjectWrapper chart, String version);

    /**
     * Method to get all the sheet names of this document
     * @return list of sheet name
     */
    List<String> getAllSheetNameForDocument();

    /**
     * Method to remove the charts in given sheet name from public view
     * @param sheetName Sheet name
     */
    void unPublishChartsBySheetName(Workbook workbook, String sheetName);
    /**
     * Method to remove chart for the given sheet name
     * @param sheetName Sheet Name
     */
    void removeChartsBySheetName(String sheetName);

    /**
     * Method to remove the charts in given sheet ID
     * @param sheetID Sheet ID
     */
    void removeChartsBySheetID(Long sheetID);

    /**
     * Method to create charts from JSONObject. <br/>
     * This method is used from ZSheet import
     * @param charts Charts
     */
    void createCharts(JSONObjectWrapper charts);


    /**
     * Class method to get a default DAO
     * @param docOwner Document owner name
     * @param docID Document ID
     * @return Default DAO
     */
    static ChartsDAO getDefaultDAO(String docOwner, long docID){
        return new ChartsDBDAO(docOwner, docID);
    }

    /**
     * Static Method to create a DAO with Document owner alone
     * @param docOwner Document Owner
     * @return Data Access Object
     */
    static ChartsDAO getDAOWithDocOwner(String docOwner) {
        return new ChartsDBDAO(docOwner);
    }

    static Persistence checkForNull(Persistence persistence){
        if(persistence == null){
            throw new ChartException("Persistence object is null");             // NO I18N
        }
        return persistence;
    }

}
