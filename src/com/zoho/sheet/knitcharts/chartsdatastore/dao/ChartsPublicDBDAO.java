package com.zoho.sheet.knitcharts.chartsdatastore.dao;

import com.adventnet.ds.query.*;
import com.adventnet.persistence.DataAccessException;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.store.StoreUtil;
import com.zoho.sheet.knitcharts.pojo.ModifiedPublicChartPOJO;
import com.zoho.sheet.knitcharts.pojo.PublicChartPOJO;
import com.zoho.sheet.knitcharts.chartsdatastore.ChartsDSConstants;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ChartException;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.util.SheetPersistenceUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

import static com.zoho.sheet.knitcharts.chartsdatastore.ChartsDSConstants.Attributes.*;
import static com.zoho.sheet.knitcharts.chartsdatastore.ChartsDSConstants.TableNames.*;

public class ChartsPublicDBDAO implements ChartsPublicDAO {

    private final String publicSpace;

    public ChartsPublicDBDAO(String publicSpace) {
        this.publicSpace = publicSpace;
    }

    @Override
    public long publishChart(String chartID, String publicChartName, String userName, Long ZUID) {
        try {
            Persistence persistence = SheetPersistenceUtils.getPersistence(publicSpace);
            DataObject dataObject = ChartsDAO.checkForNull(persistence).constructDataObject();
            long uniqueKeyID = StoreUtil.getUniqueKeyId(this.publicSpace, Constants.DEFAULT_ZUID);
            Row row = new Row(CHARTS);
            long lChartID = Long.parseLong(chartID);

            row.set(PUBLIC_CHART_ID, lChartID);
            row.set(CHART_TYPE, ChartsDSConstants.Constants.PUBLIC_CHART_TYPE);
            row.set(CHART_X, -1);
            row.set(CHART_Y, -1);
            row.set(CHART_HT, -1);
            row.set(CHART_WD, -1);
            row.set(PUBLIC_CHART_NAME, publicChartName);
            row.set(USER_NAME, userName);
            row.set(USER_ZUID, ZUID);
            row.set(UNIQUE_KEY_ID, uniqueKeyID);

            dataObject.addRow(row);
            persistence.add(dataObject);

            return ChartUtils.typeCast(dataObject.getFirstValue(CHARTS, CHART_ID));
        } catch (Exception e) {
            throw new ChartException(e.getMessage());
        }
    }

    public void publishChart(String chartID, String publicSpaceChartID, String publicChartName, String userName, Long ZUID) {
        try {
            Persistence persistence = SheetPersistenceUtils.getPersistence(publicSpace);
            DataObject dataObject = ChartsDAO.checkForNull(persistence).constructDataObject();
            Row row = new Row(CHARTS);
            long lChartID = Long.parseLong(chartID);
            long lPublicChartID = Long.parseLong(publicSpaceChartID);
            long uniqueKeyID = StoreUtil.getUniqueKeyId(this.publicSpace, Constants.DEFAULT_ZUID);

            row.set(PUBLIC_CHART_ID, lChartID);
            row.set(CHART_TYPE, ChartsDSConstants.Constants.PUBLIC_CHART_TYPE);
            row.set(CHART_X, -1);
            row.set(CHART_Y, -1);
            row.set(CHART_HT, -1);
            row.set(CHART_WD, -1);
            row.set(PUBLIC_CHART_NAME, publicChartName);
            row.set(USER_NAME, userName);
            row.set(USER_ZUID, ZUID);
            row.set(CHART_ID, lPublicChartID);
            row.set(UNIQUE_KEY_ID, uniqueKeyID);

            dataObject.addRow(row);
            persistence.add(dataObject);

        } catch (Exception e) {
            throw new ChartException(e.getMessage());
        }
    }

    @Override
    public void unPublishChart(String chartID) {
        long lChartID = Long.parseLong(chartID);

        try {
            Persistence publicSpacePersistence = SheetPersistenceUtils.getPersistence(publicSpace);
            ChartsDAO.checkForNull(publicSpacePersistence).delete(new Criteria(new Column(CHARTS, PUBLIC_CHART_ID), lChartID, QueryConstants.EQUAL));
        } catch (DataAccessException e) {
            throw new ChartException(e.getMessage());
        }

    }

    @Override
    public PublicChartPOJO readPublicChart(String publicChartName) {
        DataObject dataObject = getPublishDataObject(publicChartName);

        if(dataObject.isEmpty()) { return null; }

        PublicChartPOJO bean = new PublicChartPOJO();

        try {
            Long chartID = ChartUtils.typeCast(dataObject.getFirstValue(CHARTS, PUBLIC_CHART_ID));
            Long publicChartID = ChartUtils.typeCast(dataObject.getFirstValue(CHARTS, CHART_ID));

            bean.setChartID(chartID.toString());
            bean.setPublicSpaceChartID(publicChartID.toString());
            bean.setUserName(ChartUtils.typeCast(dataObject.getFirstValue(CHARTS, USER_NAME)));
            bean.setPublicSpace(publicSpace);
            bean.setPublicChartName(publicChartName);
            bean.setUserZUID(dataObject.getFirstValue(CHARTS, USER_ZUID).toString());
            return bean;
        } catch (DataAccessException e) {
            throw new ChartException(e.getMessage());
        }
    }

    @Override
    public PublicChartPOJO readPublicChartWithChartID(String chartID) {
        DataObject dataObject = getPublishDataObjectWithChartID(chartID);

        if(dataObject.isEmpty()) { return null; }

        PublicChartPOJO bean = new PublicChartPOJO();

        try {
            Long publicChartID = ChartUtils.typeCast(dataObject.getFirstValue(CHARTS, CHART_ID));

            bean.setChartID(chartID);
            bean.setPublicSpaceChartID(publicChartID.toString());
            bean.setUserName(ChartUtils.typeCast(dataObject.getFirstValue(CHARTS, USER_NAME)));
            bean.setPublicSpace(publicSpace);
            bean.setPublicChartName(dataObject.getFirstValue(CHARTS, PUBLIC_CHART_NAME).toString());
            bean.setUserZUID(dataObject.getFirstValue(CHARTS, USER_ZUID).toString());
            return bean;
        } catch (DataAccessException e) {
            throw new ChartException(e.getMessage());
        }
    }

    @Override
    public void updateChartModified(ModifiedPublicChartPOJO bean) {
        if(Objects.isNull(getChartModified(bean.getChartID()))) {
            try {
                long lChartID = Long.parseLong(bean.getChartID());
                Persistence persistence = SheetPersistenceUtils.getPersistence(publicSpace);
                DataObject dataObject = ChartsDAO.checkForNull(persistence).constructDataObject();

                Row row = new Row(MODIFIED_PUBLIC_CHARTS);
                row.set(PUBLIC_CHART_NAME, bean.getPublicChartName());
                row.set(RESOURCE_ID, bean.getResourceID());
                row.set(CHART_ID, lChartID);
                row.set(LAST_MODIFIED_TIME, bean.getModifiedTime());
                row.set(ASSOCIATED_SHEET_NAME, bean.getAssociatedSheetName());

                dataObject.addRow(row);
                persistence.add(dataObject);
            } catch (DataAccessException e) {
                throw new ChartException(e.getMessage());
            }
        }
    }

    @Override
    public ModifiedPublicChartPOJO getChartModified(String chartID) {
        try{
            long lChartID = Long.parseLong(chartID);
            ModifiedPublicChartPOJO bean = new ModifiedPublicChartPOJO();
            Persistence persistence = SheetPersistenceUtils.getPersistence(publicSpace);
            SelectQuery selectQuery = new SelectQueryImpl(new Table(MODIFIED_PUBLIC_CHARTS));

            selectQuery.addSelectColumn(new Column(MODIFIED_PUBLIC_CHARTS, ChartsDSConstants.Constants.ASTERISK));
            selectQuery.setCriteria(new Criteria(new Column(MODIFIED_PUBLIC_CHARTS, CHART_ID), lChartID, QueryConstants.EQUAL));
            DataObject dataObject = ChartsDAO.checkForNull(persistence).get(selectQuery);

            if(dataObject.isEmpty()) { return null; }

            bean.setPublicChartName(ChartUtils.typeCast(dataObject.getFirstValue(MODIFIED_PUBLIC_CHARTS, PUBLIC_CHART_NAME)));
            bean.setResourceID(ChartUtils.typeCast(dataObject.getFirstValue(MODIFIED_PUBLIC_CHARTS, RESOURCE_ID)));
            bean.setChartID(chartID);
            bean.setModifiedTime(ChartUtils.typeCast(dataObject.getFirstValue(MODIFIED_PUBLIC_CHARTS,LAST_MODIFIED_TIME)));
            bean.setAssociatedSheetName(ChartUtils.typeCast(dataObject.getFirstValue(MODIFIED_PUBLIC_CHARTS, ASSOCIATED_SHEET_NAME)));

            return bean;
        } catch (DataAccessException e) {
            return null;
        }
    }

    @Override
    public List<ModifiedPublicChartPOJO> getModifiedCharts() {
        try{
            List<ModifiedPublicChartPOJO> beans = new ArrayList<>();
            Persistence persistence = SheetPersistenceUtils.getPersistence(publicSpace);
            SelectQuery selectQuery = new SelectQueryImpl(new Table(MODIFIED_PUBLIC_CHARTS));

            selectQuery.addSelectColumn(new Column(MODIFIED_PUBLIC_CHARTS, ChartsDSConstants.Constants.ASTERISK));
            DataObject dataObject = ChartsDAO.checkForNull(persistence).get(selectQuery);

            if(dataObject.isEmpty()) { return null; }
            Iterator<?> iterator = dataObject.getRows(MODIFIED_PUBLIC_CHARTS);

            while(iterator.hasNext()) {
                Row row = ChartUtils.typeCast(iterator.next());
                ModifiedPublicChartPOJO bean = new ModifiedPublicChartPOJO();

                bean.setPublicChartName(row.getString(PUBLIC_CHART_NAME));
                bean.setResourceID(row.getString(RESOURCE_ID));
                bean.setChartID(row.getString(CHART_ID));
                bean.setModifiedTime(row.getLong(LAST_MODIFIED_TIME));
                bean.setAssociatedSheetName(row.getString(ASSOCIATED_SHEET_NAME));

                beans.add(bean);
            }
            return beans;
        } catch (DataAccessException e) {
            return null;
        }
    }

    @Override
    public void deleteChartModified(String chartID) {
        try {
            Persistence persistence = SheetPersistenceUtils.getPersistence(publicSpace);
            DeleteQuery deleteQuery = new DeleteQueryImpl(MODIFIED_PUBLIC_CHARTS);

            deleteQuery.setCriteria(new Criteria(new Column(MODIFIED_PUBLIC_CHARTS, CHART_ID), Long.parseLong(chartID), QueryConstants.EQUAL));
            ChartsDAO.checkForNull(persistence).delete(deleteQuery);
        } catch (DataAccessException e) {
            throw new ChartException(e.getMessage());
        }
    }

    @Override
    public void deleteModifiedCharts() {
        try {
            Persistence persistence = SheetPersistenceUtils.getPersistence(publicSpace);
            DeleteQuery deleteQuery = new DeleteQueryImpl(MODIFIED_PUBLIC_CHARTS);

            deleteQuery.setCriteria(new Criteria(new Column(MODIFIED_PUBLIC_CHARTS, CHART_ID), -1L, QueryConstants.NOT_EQUAL));
            ChartsDAO.checkForNull(persistence).delete(deleteQuery);
        } catch (DataAccessException e) {
            throw new ChartException(e.getMessage());
        }
    }

    private DataObject getPublishDataObject(String publicChartName) {
        try {
            Persistence persistence = SheetPersistenceUtils.getPersistence(publicSpace);
            SelectQuery selectQuery = new SelectQueryImpl(new Table(CHARTS));
            Criteria criteria = new Criteria(new Column(CHARTS, PUBLIC_CHART_NAME), publicChartName, QueryConstants.EQUAL);

            selectQuery.addSelectColumn(new Column(CHARTS, ChartsDSConstants.Constants.ASTERISK));
            selectQuery.setCriteria(criteria);
            return ChartsDAO.checkForNull(persistence).get(selectQuery);
        } catch (DataAccessException e) {
            throw new ChartException(e.getMessage());
        }
    }

    private DataObject getPublishDataObjectWithChartID(String chartID) {
        try {
            Persistence persistence = SheetPersistenceUtils.getPersistence(publicSpace);
            SelectQuery selectQuery = new SelectQueryImpl(new Table(CHARTS));
            Criteria criteria = new Criteria(new Column(CHARTS, PUBLIC_CHART_ID), Long.parseLong(chartID), QueryConstants.EQUAL);

            selectQuery.addSelectColumn(new Column(CHARTS, ChartsDSConstants.Constants.ASTERISK));
            selectQuery.setCriteria(criteria);
            return ChartsDAO.checkForNull(persistence).get(selectQuery);
        } catch (DataAccessException e) {
            throw new ChartException(e.getMessage());
        }
    }
}
