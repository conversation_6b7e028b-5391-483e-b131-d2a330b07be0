package com.zoho.sheet.knitcharts.chartsdatastore;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.pojo.ActiveCellPOJO;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsPublicDAO;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTable;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.constants.SheetChartActionConstants;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.supplier.ObjectsSupplier;
import com.zoho.sheet.knitcharts.supplier.Purpose;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.PublicChartType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.ProtoChart;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.actionhandlers.dataholder.ActionDataHolder;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsDAO;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.miscellaneous.exception.UnsupportedChartActionException;
import com.zoho.sheet.knitcharts.miscellaneous.function.FunctionBiConsumer;
import com.zoho.sheet.knitcharts.utils.DataTableUtils;
import com.zoho.sheet.knitcharts.utils.PublicChartUtils;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.security.SecureRandom;
import java.util.*;

/**
 * Charts Data Store related Action handler
 * <AUTHOR>
 */
public class ChartsDataStoreActionHandler {

    private static FunctionBiConsumer<ActionDataHolder, WorkbookContainer> getHandler(int action){
        switch (action) {
            case ChartsDSConstants.DSActionConstants.CREATE_CHART: return ChartsDataStoreActionHandler::createChart;
            case ChartsDSConstants.DSActionConstants.INSERT_CHART: return ChartsDataStoreActionHandler::insertChart;
            case ChartsDSConstants.DSActionConstants.DELETE_CHART: return ChartsDataStoreActionHandler::deleteChart;
            case ChartsDSConstants.DSActionConstants.CREATE_CLONE_CHART: return ChartsDataStoreActionHandler::createCloneChart;
            case ChartsDSConstants.DSActionConstants.INSERT_CLONE_CHART: return ChartsDataStoreActionHandler::insertCloneChart;
            case ChartsDSConstants.DSActionConstants.PUBLISH_CHART: return ChartsDataStoreActionHandler::publishChart;
            case ChartsDSConstants.DSActionConstants.UNPUBLISH_CHART: return ChartsDataStoreActionHandler::unpublishChart;
            case ChartsDSConstants.DSActionConstants.INSERT_PIVOT_CHART: return ChartsDataStoreActionHandler::insertPivotChart;
            case ChartsDSConstants.DSActionConstants.CREATE_PIVOT_CHART: return ChartsDataStoreActionHandler::createPivotChart;
            case ChartsDSConstants.DSActionConstants.REGENERATE_PUBLIC_CHART: return ChartsDataStoreActionHandler::regeneratePublicChart;
            case ChartsDSConstants.DSActionConstants.CREATE_PASTE_CHART: return ChartsDataStoreActionHandler::createPasteChart;
            case ChartsDSConstants.DSActionConstants.INSERT_PASTE_CHART: return ChartsDataStoreActionHandler::insertPasteChart;
            case ChartsDSConstants.DSActionConstants.MOVE_CHART_TO_OTHER_SHEET: return ChartsDataStoreActionHandler::handleMoveChartToOtherSheet;
        }

        throw new UnsupportedChartActionException(String.format("%s: %d", ErrorMessages.INVALID_CHART_ACTION, action));                                     // NO I18N
    }

    /**
     * Handle all the charts data store related Action
     * @param action Action Constant
     * @param activeSheet Active Sheet object
     * @param actionJSON Action JSONWrapper
     */
    public static void handle(int action, Sheet activeSheet, JSONObjectWrapper actionJSON, WorkbookContainer container){
        JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartActionConstants.JSONConstants.CHART_JSON);
        ActiveCellPOJO activeCellPOJO = new ActiveCellPOJO();

        activeCellPOJO.constructFromJSON(actionJSON.getJSONObject(JSONConstants.CURRENT_ACTIVE_CELL));
        ChartUtils.forEach(chartJSONs, chartJSON -> {
            FunctionBiConsumer<ActionDataHolder, WorkbookContainer> handler = getHandler(action);
            ActionDataHolder dataHolder = new ActionDataHolder(activeSheet, action, (JSONObjectWrapper) chartJSON, activeCellPOJO, false);

            handler.consume(dataHolder, container);
        });
    }

    private static void createChart(ActionDataHolder dataHolder, WorkbookContainer container){
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        ProtoChart protoChart = createProtoChart(dataHolder);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();

        dao.createNewChart(protoChart);
        chartJSON.put(ChartActionConstants.JSONConstants.IS_CHART_CREATED, true);
        chartJSON.put(ChartActionConstants.JSONConstants.CHART_ID, protoChart.chartID);
    }

    private static void createPivotChart(ActionDataHolder dataHolder, WorkbookContainer container){
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        ProtoChart protoChart = createPivotProtoChart(dataHolder);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();

        dao.createNewChart(protoChart);
        chartJSON.put(ChartActionConstants.JSONConstants.IS_CHART_CREATED, true);
        chartJSON.put(ChartActionConstants.JSONConstants.CHART_ID, protoChart.chartID);
    }
    
    private static void createCloneChart(ActionDataHolder dataHolder, WorkbookContainer container){
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        Chart originalChart = dataHolder.getActiveSheet().getWorkbook().getChartsContainer()
                .getChart(chartJSON.getString(ChartActionConstants.JSONConstants.ORIGINAL_CHART_ID));
        ProtoChart protoChart = createProtoChart(dataHolder, originalChart);

        SheetChartAPI.updatePositionForClone(protoChart.sheetMeta);
        SheetChartAPI.clearPublicDetails(protoChart.sheetMeta);
        dao.createNewChart(protoChart);
        chartJSON.put(ChartActionConstants.JSONConstants.IS_CHART_CREATED, true);
        chartJSON.put(ChartActionConstants.JSONConstants.CHART_ID, protoChart.chartID);
    }

    private static void createPasteChart(ActionDataHolder dataHolder, WorkbookContainer container) {
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        ProtoChart protoChart = createPasteProtoChart(dataHolder);

        dao.createNewChart(protoChart);
        chartJSON.put(ChartActionConstants.JSONConstants.IS_CHART_CREATED, true);
        chartJSON.put(ChartActionConstants.JSONConstants.CHART_ID, protoChart.chartID);
    }

    private static void insertChart(ActionDataHolder dataHolder, WorkbookContainer workbookContainer){
        long docID = Long.parseLong(workbookContainer.getDocId());
        String docOwnerID = workbookContainer.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        ProtoChart protoChart = createProtoChart(dataHolder);

        if(chartJSON.has(ChartActionConstants.JSONConstants.IS_CHART_CREATED)){
            dao.updateChartOptions(protoChart);
            chartJSON.remove(ChartActionConstants.JSONConstants.IS_CHART_CREATED);
        }else{
            dao.insertChart(protoChart);
        }
    }

    private static void insertPivotChart(ActionDataHolder dataHolder, WorkbookContainer workbookContainer){
        long docID = Long.parseLong(workbookContainer.getDocId());
        String docOwnerID = workbookContainer.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        ProtoChart protoChart = createPivotProtoChart(dataHolder);

        if(chartJSON.has(ChartActionConstants.JSONConstants.IS_CHART_CREATED)){
            dao.updateChartOptions(protoChart);
            chartJSON.remove(ChartActionConstants.JSONConstants.IS_CHART_CREATED);
        }else{
            dao.insertChart(protoChart);
        }
    }

    private static void insertCloneChart(ActionDataHolder dataHolder, WorkbookContainer workbookContainer){
        long docID = Long.parseLong(workbookContainer.getDocId());
        String docOwnerID = workbookContainer.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        Chart originalChart = dataHolder.getActiveSheet().getWorkbook().getChartsContainer()
                .getChart(chartJSON.getString(ChartActionConstants.JSONConstants.ORIGINAL_CHART_ID));
        ProtoChart protoChart = createProtoChart(dataHolder, originalChart);

        SheetChartAPI.updatePositionForClone(protoChart.sheetMeta);
        SheetChartAPI.clearPublicDetails(protoChart.sheetMeta);
        if(chartJSON.has(ChartActionConstants.JSONConstants.IS_CHART_CREATED)){
            dao.updateChartOptions(protoChart);
            chartJSON.remove(ChartActionConstants.JSONConstants.IS_CHART_CREATED);
        }else{
            dao.insertChart(protoChart);
        }
    }

    private static void insertPasteChart(ActionDataHolder dataHolder, WorkbookContainer container) {
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        ProtoChart protoChart = createPasteProtoChart(dataHolder);

        if(chartJSON.has(ChartActionConstants.JSONConstants.IS_CHART_CREATED)){
            dao.updateChartOptions(protoChart);
            chartJSON.remove(ChartActionConstants.JSONConstants.IS_CHART_CREATED);
        }else{
            dao.insertChart(protoChart);
        }
    }

    private static void handleMoveChartToOtherSheet(ActionDataHolder dataHolder, WorkbookContainer container) {
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);

        String targetSheetID = dataHolder.getChartJSON().getString(SheetChartActionConstants.JSONConstants.TARGET_SHEET_ID);
        String sheetName = dataHolder.getActiveSheet().getWorkbook().getSheetByAssociatedName(targetSheetID).getName();

        dao.updateSheetID(dataHolder.getChartID(), sheetName);
    }


    private static void deleteChart(ActionDataHolder dataHolder, WorkbookContainer container){
        if(PublicChartUtils.isChartPublic(dataHolder.getChartID(), dataHolder.getActiveSheet().getWorkbook())) {
            unpublishChart(dataHolder, container);
        }

        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        dao.deleteChart(dataHolder.getChartID());
    }

    private static void publishChart(ActionDataHolder dataHolder, WorkbookContainer container) {
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();

        String publicChartName = UUID.randomUUID().toString() + System.currentTimeMillis() + ((int) (new SecureRandom().nextDouble() * 1000));
        publicChartName = publicChartName.replace("-", "");     // NO I18N
        PublicChartType publicChartType = PublicChartType.retrieveByValue(chartJSON
                .optString(ChartActionConstants.JSONConstants.PUBLISH_CHART_TYPE, PublicChartType.EXTERNAL.getValue().toString()));

        String publicSpace = PublicChartUtils.getPublicSpace(publicChartType, docOwnerID);
        ChartsPublicDAO publicDAO = ChartsPublicDAO.getDefaultPublicDAO(publicSpace);

        dao.publishChart(dataHolder.getChartID(), publicChartName, publicSpace, new Date().getTime());
        Long publicSpaceChartID = publicDAO.publishChart(dataHolder.getChartID(), publicChartName, docOwnerID, DocumentUtils.getZUID(docOwnerID));
        chartJSON.put(ChartActionConstants.JSONConstants.PUBLIC_SPACE_CHART_ID, publicSpaceChartID);
        chartJSON.put(ChartActionConstants.JSONConstants.PUBLIC_CHART_NAME, publicChartName);
        PublicChartUtils.doFollowUpPublishTasks(dataHolder.getActiveSheet().getWorkbook(), dataHolder.getChartID(), publicSpaceChartID.toString(), publicSpace);
    }

    private static void regeneratePublicChart(ActionDataHolder dataHolder, WorkbookContainer container){
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);

        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();

        String publicChartName = chartJSON.getString(ChartActionConstants.JSONConstants.PUBLIC_CHART_NAME);
        String publicSpaceChartID = chartJSON.getString(ChartActionConstants.JSONConstants.PUBLIC_SPACE_CHART_ID);
        PublicChartType publicChartType = PublicChartType.retrieveByValue(chartJSON
                .optString(ChartActionConstants.JSONConstants.PUBLISH_CHART_TYPE, PublicChartType.EXTERNAL.getValue().toString()));
        String publicSpace = PublicChartUtils.getPublicSpace(publicChartType, docOwnerID);

        ChartsPublicDAO publicDAO = ChartsPublicDAO.getDefaultPublicDAO(publicSpace);

        dao.publishChart(dataHolder.getChartID(), publicChartName, publicSpace, new Date().getTime());
        publicDAO.publishChart(dataHolder.getChartID(), publicSpaceChartID, publicChartName, docOwnerID, DocumentUtils.getZUID(docOwnerID));
        PublicChartUtils.doFollowUpPublishTasks(dataHolder.getActiveSheet().getWorkbook(), dataHolder.getChartID(), publicSpaceChartID, publicSpace);
    }

    private static void unpublishChart(ActionDataHolder dataHolder, WorkbookContainer container) {
        long docID = Long.parseLong(container.getDocId());
        String docOwnerID = container.getDocOwner();
        ChartsDAO dao = ChartsDAO.getDefaultDAO(docOwnerID, docID);
        PublicChartType publicChartType = SheetChartAPI.getPublicChartType(ChartUtils.getSheetMetaFromActionDataHolder(dataHolder));
        Chart chart = ChartUtils.getChartFromActionDataHolder(dataHolder);

        String publicSpace = PublicChartUtils.getPublicSpace(publicChartType, docOwnerID);
        ChartsPublicDAO publicDAO = ChartsPublicDAO.getDefaultPublicDAO(publicSpace);


        dao.unPublishChart(dataHolder.getChartID());
        publicDAO.unPublishChart(dataHolder.getChartID());
        PublicChartUtils.doFollowUpUnPublishTasks(publicSpace, SheetChartAPI.getPublicSpaceChartID(chart.getSheetMeta()), dataHolder.getChartID());
    }

    private static ProtoChart createProtoChart(ActionDataHolder dataHolder){
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        List<String> dataSources = ChartUtils.base64Decode(ChartUtils.JSONArrayToList(chartJSON.getJSONObject(ChartActionConstants.JSONConstants.SHEET_META)
                .getJSONObject(ChartActionConstants.JSONConstants.DATA_TABLE_OPTIONS)
                .getJSONArray(ChartActionConstants.JSONConstants.DATA_SOURCES)));
        ProtoChart protoChart = new ProtoChart();
        Workbook workbook = dataHolder.getActiveSheet().getWorkbook();

        protoChart.sheetName = dataHolder.getActiveSheet().getName();
        protoChart.sheetID = dataHolder.getActiveSheet().getAssociatedName();
        protoChart.chartMeta = ObjectsSupplier.getInstance().getChartMetaConstructor(Purpose.ChartMetaPurpose.STD_CHART_META)
                .constructChartMeta(dataHolder.getActiveSheet(), chartJSON, dataSources);
        DataTable dataTable = DataTableUtils.createTempReferenceDataTable(workbook, dataSources, dataHolder.getActiveCellBean().getAssociatedSheetName());
        protoChart.sheetMeta = ObjectsSupplier.getInstance().getSheetMetaConstructor(Purpose.SheetMetaPurpose.STD_SHEET_META)
                .constructSheetMeta(dataHolder.getActiveSheet(), chartJSON, dataTable, FrameworkChartAPI.getChartType(protoChart.chartMeta));

        if(dataHolder.hasChartID()){
            protoChart.chartID = dataHolder.getChartID();
        }
        return protoChart;
    }

    private static ProtoChart createPasteProtoChart(ActionDataHolder dataHolder) {
        ProtoChart protoChart = new ProtoChart();
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        List<String> dataSources = ChartUtils.base64Decode(ChartUtils.JSONArrayToList(chartJSON.getJSONObject(ChartActionConstants.JSONConstants.SHEET_META)
                .getJSONObject(ChartActionConstants.JSONConstants.DATA_TABLE_OPTIONS)
                .getJSONArray(ChartActionConstants.JSONConstants.DATA_SOURCES)));

        protoChart.sheetName = dataHolder.getActiveSheet().getName();
        protoChart.sheetID = dataHolder.getActiveSheet().getAssociatedName();
        protoChart.chartMeta = ObjectsSupplier.getInstance().getChartMetaConstructor(Purpose.ChartMetaPurpose.STD_CHART_META)
                .constructChartMeta(dataHolder.getActiveSheet(), chartJSON, dataSources);
        protoChart.sheetMeta = ObjectsSupplier.getInstance().getSheetMetaConstructor(Purpose.SheetMetaPurpose.PASTE_CHART_SHEET_META)
                .constructSheetMeta(dataHolder.getActiveSheet(), chartJSON, null, FrameworkChartAPI.getChartType(protoChart.chartMeta));

        if(dataHolder.hasChartID()){
            protoChart.chartID = dataHolder.getChartID();
        }
        return protoChart;
    }

    private static ProtoChart createPivotProtoChart(ActionDataHolder dataHolder){
        ProtoChart protoChart = new ProtoChart();
        JSONObjectWrapper chartJSON = dataHolder.getChartJSON();
        List<String> dataSources = ChartUtils.base64Decode(ChartUtils.JSONArrayToList(chartJSON.getJSONObject(ChartActionConstants.JSONConstants.SHEET_META)
                .getJSONObject(ChartActionConstants.JSONConstants.DATA_TABLE_OPTIONS)
                .getJSONArray(ChartActionConstants.JSONConstants.DATA_SOURCES)));
        protoChart.sheetName = dataHolder.getActiveSheet().getName();
        protoChart.sheetID = dataHolder.getActiveSheet().getAssociatedName();
        protoChart.chartMeta = ObjectsSupplier.getInstance().getChartMetaConstructor(Purpose.ChartMetaPurpose.PIVOT_CHART_META)
                .constructChartMeta(dataHolder.getActiveSheet(), chartJSON, dataSources);
        protoChart.sheetMeta = ObjectsSupplier.getInstance().getSheetMetaConstructor(Purpose.SheetMetaPurpose.PIVOT_SHEET_META)
                .constructSheetMeta(dataHolder.getActiveSheet(), chartJSON, null, FrameworkChartAPI.getChartType(protoChart.chartMeta));

        if(dataHolder.hasChartID()){
            protoChart.chartID = dataHolder.getChartID();
        }
        return protoChart;
    }

    /**
     * Method to create a new proto chart.<br>
     * This method is used while chart clone.
     * @param chart Original Chart Instance
     * @param dataHolder Action Data holder
     * @return ProtoChart Instance
     */
    public static ProtoChart createProtoChart(ActionDataHolder dataHolder, Chart chart){
        ProtoChart protoChart = new ProtoChart();
        Chart clonedChart = chart.createClone(dataHolder.getChartID());
        
        protoChart.sheetName = dataHolder.getActiveSheet().getName();
        protoChart.sheetID = clonedChart.getAssociatedSheetName();
        protoChart.chartMeta = clonedChart.getApiMeta().getChartMeta();
        protoChart.sheetMeta = clonedChart.getSheetMeta();
        protoChart.chartID = dataHolder.getChartID();
        
        return protoChart;
    }

}
