package com.zoho.sheet.knitcharts.dataconverter.firstgen;

import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.*;
import com.zoho.sheet.knitcharts.chartsdatastore.ChartsDSConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.*;

/**
 * Converts old chart options to new framework meta
 * <AUTHOR>
 */
public final class FrameworkMetaConverter {

    /**
     * Converts necessary chart options.<br>
     */
    static class ChartDataConverter {

        static void convertLimitedOptions(ChartMeta chartMeta, String chartType) {
            FrameworkChartAPI.updateChartType(chartMeta, ChartUtils.getChartForOldChartName(chartType));
        }
    }

    static class LegendDataConverter {

        static void convertLimitedOptions(ChartMeta chartMeta, JSONObjectWrapper oldEntry){
            boolean legendStatus = oldEntry.getBoolean(ChartsDSConstants.Attributes.CHART_LEGEND);
            Integer legendPosition = oldEntry.optInt(ChartsDSConstants.Attributes.CHART_LEGEND_POSITION);

            FrameworkChartAPI.updateLegendStatus(chartMeta, legendStatus);
            FrameworkChartAPI.updateChartLegendPosition(chartMeta, ChartUtils.getNewLegendPositionFromOld(legendPosition));
        }

    }

    static class SeriesPropsDataConverter {

        static void convertLimitedOptions(ChartMeta chartMeta, String chartType){
            if(Objects.equals(chartType, "XYLINE_SHAPES")) {            // NO I18N
                FrameworkChartAPI.updateSharedSeriesMarkerStatus(chartMeta, Boolean.TRUE);
            }

            FrameworkChartAPI.updateSharedSeriesDataLabelsStatus(chartMeta, Boolean.TRUE);
            FrameworkChartAPI.addSharedSeriesDataLabelsFormat(chartMeta, DataLabelFormatType.VALUE);
            convertRelationParing(chartMeta);
        }

        /**
         * Setting series relation to Single so that it matches Old Chart Behaviour
         * @param chartMeta ChartMeta instance
         */
        private static void convertRelationParing(ChartMeta chartMeta){
            ChartType chartType = FrameworkChartAPI.getChartType(chartMeta);

            if(chartType == ChartType.SCATTER || chartType == ChartType.SCATTER_LINE || chartType == ChartType.SCATTER_LINE_MARKERS) {
                FrameworkChartAPI.updateSeriesRelation(chartMeta, SeriesRelationType.SINGLE);
            }
        }

    }

    static class TitleDataConverter {

        static void convertLimitedOptions(ChartMeta chartMeta, JSONObjectWrapper entry){
            String title = entry.optString(ChartsDSConstants.Attributes.CHART_TITLE);

            FrameworkChartAPI.updateChartTitle(chartMeta, title);
            FrameworkChartAPI.updateChartTitleStatus(chartMeta, ChartUtils.isNonEmptyString(title));
        }
    }

    static class XAxisDataConverter {

        public static void convertLimitedOptions(ChartMeta chartMeta, JSONObjectWrapper entry){
            String title = ChartUtils.doubleDecode(entry.optString(ChartsDSConstants.Attributes.CHART_XAXIS_TITLE));

            FrameworkChartAPI.updateChartXAxisTitleWithIndex(chartMeta, title, 0);
            FrameworkChartAPI.updateChartXAxisTitleStatusWithIndex(chartMeta, ChartUtils.isNonEmptyString(title), 0);
        }
    }

    static class YAxisDataConverter {

        static void convertLimitedOptions(ChartMeta chartMeta, JSONObjectWrapper entry) {
            String title = ChartUtils.doubleDecode(entry.optString(ChartsDSConstants.Attributes.CHART_YAXIS_TITLE));

            FrameworkChartAPI.updateChartYAxisTitleWithIndex(chartMeta, title, 0);
            FrameworkChartAPI.updateChartYAxisTitleStatusWithIndex(chartMeta, ChartUtils.isNonEmptyString(title), 0);
        }
    }

    public static ChartMeta convertLimitedOptions(String chartType, JSONObjectWrapper entry) {
        ChartMeta chartMeta = new ChartMeta();

        ChartDataConverter.convertLimitedOptions(chartMeta, chartType);
        TitleDataConverter.convertLimitedOptions(chartMeta, entry);
        SeriesPropsDataConverter.convertLimitedOptions(chartMeta, chartType);
        LegendDataConverter.convertLimitedOptions(chartMeta, entry);
        XAxisDataConverter.convertLimitedOptions(chartMeta, entry);
        YAxisDataConverter.convertLimitedOptions(chartMeta, entry);


        return chartMeta;
    }
}
