package com.zoho.sheet.knitcharts.dataconverter;

import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.ProtoChart;

public interface DataConverter {

    void convert(JSONObjectWrapper entry, ProtoChart protoChart, Workbook workbook);

    default void pipedConvert(ProtoChart protoChart, Workbook workbook) {}

}
