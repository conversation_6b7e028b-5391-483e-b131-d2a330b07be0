package com.zoho.sheet.knitcharts.dataconverter.thirdgen;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.ProtoChart;
import com.zoho.sheet.knitcharts.chartsdatastore.ChartsDSConstants;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsDBDAO;
import com.zoho.sheet.knitcharts.dataconverter.DataConverter;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.PivotChartSheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.utils.ChartUtils;

import java.util.Objects;

public class ThirdGenDataConverter implements DataConverter {

    @Override
    public void convert(JSONObjectWrapper entry, ProtoChart protoChart, Workbook workbook) {
        JSONObjectWrapper chartOptions = ChartsDBDAO.getChartOptions(entry);
        JSONObjectWrapper newChartOptions = chartOptions.getJSONObject(ChartsDSConstants.JSONConstants.NEW_CHART_OPTIONS);

        ChartMeta chartMeta = new ChartMeta();
        JSONObjectWrapper sheetMetaJSON = newChartOptions.getJSONObject(ChartsDSConstants.JSONConstants.SHEET_META);
        SheetMeta sheetMeta = ChartUtils.getSheetMeta(sheetMetaJSON);

        chartMeta.fromJSON(newChartOptions.getJSONObject(ChartsDSConstants.JSONConstants.CHART_META));
        sheetMeta.fromJSON(sheetMetaJSON);

        protoChart.chartMeta = chartMeta;
        protoChart.sheetMeta = sheetMeta;
    }
}
