package com.zoho.sheet.knitcharts.dataconverter.secondgen;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoho.sheet.knitcharts.chart.ProtoChart;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chartsdatastore.dao.ChartsDBDAO;
import com.zoho.sheet.knitcharts.dataconverter.DataConverter;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.DataSack;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ChartException;

import java.io.IOException;

import static com.zoho.sheet.knitcharts.chartsdatastore.ChartsDSConstants.Attributes.*;

public class SecondGenDataConverter implements DataConverter {

    private static OldChartJSON convertToOldJSONObject(String oldChartJSON){
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(old<PERSON><PERSON><PERSON><PERSON><PERSON>, OldChartJSON.class);
        }catch (IOException e){
            throw new ChartException(e.getMessage());
        }
    }

    @Override
    public void convert(JSONObjectWrapper entry, ProtoChart protoChart, Workbook workbook) {
        JSONObjectWrapper oldChartJSONObject = ChartsDBDAO.getChartOptions(entry);
        String chartType = entry.getString(CHART_TYPE);

        protoChart.chartID = entry.get(CHART_ID).toString();

        OldChartJSON oldChartJSON = convertToOldJSONObject(oldChartJSONObject.toString());
        DataSack dataSack = new DataSack();
        SheetMeta sheetMeta = SheetMetaConverter.convert(workbook, oldChartJSON, entry, chartType,
                protoChart.sheetName, dataSack);
        int seriesCount = dataSack.get(DataSack.Data.SERIES_COUNT);

        /* Constructing proto chart object */
        protoChart.chartMeta = FrameworkMetaConverter.convert(oldChartJSON, oldChartJSONObject, chartType, seriesCount);
        protoChart.sheetMeta = sheetMeta;

        protoChart.isSaveRequired = true;
    }
}
