package com.zoho.sheet.knitcharts.presets;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;

/**
 * Preset Delegate
 * <AUTHOR>
 */
public class DelegatingPreset implements Preset {

    private final Preset preset;

    public DelegatingPreset(Preset preset) {
        this.preset = preset;
    }

    @Override
    public void applyPreset(Workbook workbook, Chart chart) {
        this.preset.applyPreset(workbook, chart);
    }
}
