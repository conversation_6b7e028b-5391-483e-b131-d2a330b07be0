package com.zoho.sheet.knitcharts.presets.chartarea.background;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ColorFillType;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.presets.PresetParams;
import com.zoho.sheet.knitcharts.presets.templates.gradient.AbstractPresetGradient1;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;
import static com.zoho.sheet.knitcharts.constants.ChartConstants.Presets;

import java.util.List;

public class ChartAreaBackgroundPreset extends AbstractPresetGradient1 {

    public ChartAreaBackgroundPreset(PresetParams params) {
        super(params);
    }

    @Override
    protected List<String> getStylesParams() {
        String accent = getAccentFromPresetName();
        return CollectionsUtils.listOf(Presets.ZERO, accent, Presets.ZERO, accent, Presets.NINETY, Presets.SIXTY);
    }

    @Override
    protected void clearPreviousProps(ChartMeta chartMeta, SheetMeta sheetMeta) {
        FrameworkChartAPI.updateChartAreaFillType(chartMeta, null);

        if(isCustom()) { return; }
        SheetChartAPI.updateCustomPropsChartAreaGradientStops(sheetMeta, null);
    }

    @Override
    protected void updateThemeColors(ZSTheme theme, ChartMeta chartMeta, SheetMeta sheetMeta) {
        String stopsThemeString = SheetChartGetterAPI.getCustomPropsChartAreaGradientStops(sheetMeta);
        String stopsString = FrameworkChartGetterAPI.getChartAreaGradientStops(chartMeta);

        FrameworkChartAPI.updateChartAreaGradientStops(chartMeta, ChartUtils.updateStopsColorFromStopsThemes(theme, stopsString, stopsThemeString));
    }

    @Override
    protected void applyStyles(ChartMeta chartMeta, SheetMeta sheetMeta, String themes, JSONObjectWrapper styles, ColorFillType fillType) {
        FrameworkChartAPI.updateChartAreaGradientStyles(chartMeta, styles);
        SheetChartAPI.updateCustomPropsChartAreaGradientStops(sheetMeta, themes);
        FrameworkChartAPI.updateChartAreaFillType(chartMeta, fillType);
    }

    @Override
    protected void applyCustomStyles(ChartMeta chartMeta, SheetMeta sheetMeta, JSONObjectWrapper styles, ColorFillType fillType) {
        FrameworkChartAPI.updateChartAreaGradientStyles(chartMeta, styles);
        FrameworkChartAPI.updateChartAreaFillType(chartMeta, fillType);
        FrameworkChartAPI.updateChartAreaGradientStops(chartMeta, getCustomStops());
        SheetChartAPI.updateCustomPropsChartAreaGradientStops(sheetMeta, getCustomStopsTheme());
    }
}
