package com.zoho.sheet.knitcharts.presets.templates.shadow;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.miscellaneous.exception.InvalidPresetNameException;
import com.zoho.sheet.knitcharts.presets.Preset;
import com.zoho.sheet.knitcharts.presets.PresetParams;

public abstract class AbstractPresetShadow1 implements Preset {

    private final String presetName;

    public AbstractPresetShadow1(PresetParams params) {
        this.presetName = params.getParam(PresetParams.Name.PRESET_NAME);
    }

    protected String getStyles() {
        String preset = PresetShadow1Supplier.getStyles(presetName);
        if(preset == null) {
            throw new InvalidPresetNameException(String.format("Invalid preset name: %s", presetName));             // NO I18N
        }
        return preset;
    }

    @Override
    public void applyPreset(Workbook workbook, Chart chart) {
        JSONObjectWrapper styles = new JSONObjectWrapper(getStyles());
        ChartMeta chartMeta = chart.getApiMeta().getChartMeta();

        clearPreviousProps(chartMeta);
        applyStyles(chartMeta, styles);
    }

    protected abstract void clearPreviousProps(ChartMeta chartMeta);

    protected abstract void applyStyles(ChartMeta chartMeta, JSONObjectWrapper styles);

    public String getPresetName() {
        return presetName;
    }

}
