package com.zoho.sheet.knitcharts.presets.templates.shadow;

import com.zoho.sheet.knitcharts.miscellaneous.datastructure.ImmutableEntry;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.lang.ref.SoftReference;
import java.util.Map;
import java.util.Objects;

/**
 * This preset shadow supplier is used in series shadow
 * <AUTHOR>
 */
public class PresetShadow2Supplier {

    public static SoftReference<Map<String, String>> presets;

    private static void initialize() {
        presets = new SoftReference<>(CollectionsUtils.mapOf(
                new ImmutableEntry<>("PRESET1", "{\"distance\":5,\"spread\":8,\"degree\":145,\"type\":\"outside\",\"opacity\":0.22}"),          // NO I18N
                new ImmutableEntry<>("PRESET2", "{\"distance\":5,\"spread\":8,\"degree\":180,\"type\":\"outside\",\"opacity\":0.22}"),          // NO I18N
                new ImmutableEntry<>("PRESET3", "{\"distance\":5,\"spread\":8,\"degree\":210,\"type\":\"outside\",\"opacity\":0.22}"),          // NO I18N
                new ImmutableEntry<>("PRESET4", "{\"distance\":5,\"spread\":8,\"degree\":90,\"type\":\"outside\",\"opacity\":0.22}"),          // NO I18N
                new ImmutableEntry<>("PRESET5", "{\"distance\":0,\"spread\":8,\"degree\":0,\"type\":\"outside\",\"opacity\":0.22}"),          // NO I18N
                new ImmutableEntry<>("PRESET6", "{\"distance\":5,\"spread\":8,\"degree\":270,\"type\":\"outside\",\"opacity\":0.22}"),          // NO I18N
                new ImmutableEntry<>("PRESET7", "{\"distance\":5,\"spread\":8,\"degree\":45,\"type\":\"outside\",\"opacity\":0.22}"),          // NO I18N
                new ImmutableEntry<>("PRESET8", "{\"distance\":5,\"spread\":8,\"degree\":0,\"type\":\"outside\",\"opacity\":0.22}"),          // NO I18N
                new ImmutableEntry<>("PRESET9", "{\"distance\":5,\"spread\":8,\"degree\":320,\"type\":\"outside\",\"opacity\":0.22}")          // NO I18N
        ));

    }

    public static String getStyles(String styleName) {
        if(presets == null || presets.get() == null) { initialize(); }

        return Objects.requireNonNull(presets.get()).get(styleName);
    }

}
