package com.zoho.sheet.knitcharts.presets.series.color;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ColorFillType;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.constants.ChartConstants;
import com.zoho.sheet.knitcharts.presets.PresetParams;
import com.zoho.sheet.knitcharts.presets.templates.gradient.AbstractPresetGradient1;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.List;

public class DataPropertyColorPreset extends AbstractPresetGradient1 {

    private final int seriesIndex, dataPropertyIndex;

    public DataPropertyColorPreset(PresetParams params) {
        super(params);
        this.seriesIndex = params.getParam(PresetParams.Name.SERIES_INDEX);
        this.dataPropertyIndex = params.getParam(PresetParams.Name.DATA_PROPERTY_INDEX);
    }

    @Override
    protected List<String> getStylesParams() {
        String accent = getAccentFromPresetName();

        return CollectionsUtils.listOf(ChartConstants.Presets.TWENTY_FIVE_PERCENT_LIGHT, accent, ChartConstants.Presets.TWENTY_FIVE_PERCENT_LIGHT, accent, ChartConstants.Presets.THIRTY_FOUR, ChartConstants.Presets.NEGATIVE_FIVE);
    }

    @Override
    protected void clearPreviousProps(ChartMeta chartMeta, SheetMeta sheetMeta) {
        FrameworkChartAPI.updateDataPropertyColorFillType(chartMeta, null, seriesIndex, dataPropertyIndex);

        if(isCustom()) { return; }
        SheetChartAPI.updateCustomPropsDataPropertyStops(sheetMeta, null, seriesIndex, dataPropertyIndex);
    }

    @Override
    protected void updateThemeColors(ZSTheme theme, ChartMeta chartMeta, SheetMeta sheetMeta) {
        String stopsThemeString = SheetChartGetterAPI.getCustomPropsDataPropertyColorStops(sheetMeta, seriesIndex, dataPropertyIndex);
        String stopsString = FrameworkChartGetterAPI.getDataPropertyGradientStops(chartMeta, seriesIndex, dataPropertyIndex);

        FrameworkChartAPI.updateDataPropertyGradientStops(chartMeta, ChartUtils.updateStopsColorFromStopsThemes(theme, stopsString, stopsThemeString), seriesIndex, dataPropertyIndex);
    }

    @Override
    protected void applyStyles(ChartMeta chartMeta, SheetMeta sheetMeta, String themes, JSONObjectWrapper styles, ColorFillType fillType) {
        FrameworkChartAPI.updateDataPropertyGradientStyles(chartMeta, styles, seriesIndex, dataPropertyIndex);
        FrameworkChartAPI.updateDataPropertyColorFillType(chartMeta, fillType, seriesIndex, dataPropertyIndex);
        SheetChartAPI.updateCustomPropsDataPropertyStops(sheetMeta, themes, seriesIndex, dataPropertyIndex);
    }

    @Override
    protected void applyCustomStyles(ChartMeta chartMeta, SheetMeta sheetMeta, JSONObjectWrapper styles, ColorFillType fillType) {
        FrameworkChartAPI.updateDataPropertyGradientStyles(chartMeta, styles, seriesIndex, dataPropertyIndex);
        FrameworkChartAPI.updateDataPropertyColorFillType(chartMeta, fillType, seriesIndex, dataPropertyIndex);
        FrameworkChartAPI.updateDataPropertyGradientStops(chartMeta, getCustomStops(), seriesIndex, dataPropertyIndex);
        SheetChartAPI.updateCustomPropsDataPropertyStops(sheetMeta, getCustomStopsTheme(), seriesIndex, dataPropertyIndex);
    }
}
