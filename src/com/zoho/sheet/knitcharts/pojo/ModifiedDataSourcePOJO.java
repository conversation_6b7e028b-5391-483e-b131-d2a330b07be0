package com.zoho.sheet.knitcharts.pojo;

import com.zoho.sheet.knitcharts.utils.ChartUtils;

public class ModifiedDataSourcePOJO implements POJO {

    private boolean expanded = false, affected = false;

    private final String dataSource;

    private String newDataSource;

    public ModifiedDataSourcePOJO(String dataSource) {
        this.dataSource = dataSource;
    }

    public boolean isExpanded() {
        return expanded;
    }

    public void setExpanded(boolean expanded) {
        this.expanded = expanded;
    }

    public boolean isAffected() {
        return affected;
    }

    public void setAffected(boolean affected) {
        this.affected = affected;
    }

    public String getNewDataSource() {
        return ChartUtils.requireNonNullElse(newDataSource, dataSource);
    }

    public void setNewDataSource(String newDataSource) {
        this.newDataSource = newDataSource;
    }

    public String getDataSource() {
        return dataSource;
    }
}
