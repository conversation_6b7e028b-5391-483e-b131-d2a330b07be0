package com.zoho.sheet.knitcharts.pojo;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.reference.referrer.Referrer;

public class ModifiedReferencePOJO implements POJO {

    private boolean isExpanded, isAffected, isRenamed, isModified, isRemoved, isExpandedHorizontally;

    private final Referrer referrer;

    private final String referenceName;

    private String newReference;

    private final Reference reference;

    public ModifiedReferencePOJO(Reference reference, Referrer referrer, String referenceName) {
        this.referrer = referrer;
        this.referenceName = referenceName;
        this.reference = reference;
    }

    public boolean isExpanded() {
        return isExpanded;
    }

    public void setExpanded(boolean expanded) {
        isExpanded = expanded;
    }

    public boolean isAffected() {
        return isAffected;
    }

    public void setAffected(boolean affected) {
        isAffected = affected;
    }

    public boolean isRenamed() {
        return isRenamed;
    }

    public void setRenamed(boolean renamed) {
        isRenamed = renamed;
    }

    public String getReferenceName() {
        return referenceName;
    }

    public Referrer getReferrer() {
        return referrer;
    }

    public String getNewReference(Workbook workbook) {
        return newReference == null ? reference.getReferenceName(workbook) : newReference;
    }

    public void setNewReference(String newReference) {
        this.newReference = newReference;
    }

    public boolean isModified() {
        return isModified;
    }

    public void setModified(boolean modified) {
        isModified = modified;
    }

    public boolean isRemoved() {
        return isRemoved;
    }

    public void setRemoved(boolean removed) {
        isRemoved = removed;
    }

    public boolean isExpandedHorizontally() {
        return isExpandedHorizontally;
    }

    public void setExpandedHorizontally(boolean expandedHorizontally) {
        isExpandedHorizontally = expandedHorizontally;
    }
}
