package com.zoho.sheet.knitcharts.pojo;

import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.constants.ChartModificationConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public class Modified<PERSON>hartPOJO implements POJO {

    public enum ModificationType {
        DATA,
        INSERT,
        DELETE,
        MOVE,
        TITLE_MODIFIED,
        SUBTITLE_MODIFIED,
        X_AXIS_TITLE_MODIFIED,
        Y_AXIS_TITLE_MODIFIED,
        PLOT_LINE_VALUE_MODIFIED,
        PLOT_LINE_LABEL_MODIFIED,
        TOTAL_DATALABELS_TEXT_MODIFIED,
        THEME_MODIFIED,
        X_AXIS_PREFIX,
        X_AXIS_SUFFIX,
        Y_AXIS_PREFIX,
        Y_AXIS_SUFFIX,
        LEGEND_TITLE_MODIFIED,
        Y_AXIS_MIN,
        Y_AXIS_MAX,
        Y_AXIS_INTERVAL,
        SP_DATALABELS_CUSTOM_VALUE,
        SERIES_DATALABELS_CUSTOM_VALUE,
        DATA_PROPS_DATALABELS_CUSTOM_VALUE,
        SP_TRENDLINE_DATALABELS_CUSTOM_VALUE,
        TRENDLINE_DATALABELS_CUSTOM_VALUE
    }

    private final String modifiedChartId;

    private final int modificationConstants;

    private final String associatedSheetName;

    private Key key;                                                        // key is used to identify which element modified among the generic types for example which Y-Axis

    public ModifiedChartPOJO(String modifiedChartId, int modificationConstants, String associatedSheetName) {
        this.modifiedChartId = modifiedChartId;
        this.modificationConstants = modificationConstants;
        this.associatedSheetName = associatedSheetName;
    }

    public ModifiedChartPOJO(String modifiedChartId, int modificationConstants, String associatedSheetName, Key key) {
        this.modifiedChartId = modifiedChartId;
        this.modificationConstants = modificationConstants;
        this.associatedSheetName = associatedSheetName;
        this.key = key;
    }

    public String getModifiedChartId() {
        return modifiedChartId;
    }

    public int getModificationConstants() {
        return modificationConstants;
    }

    public String getAssociatedSheetName() {
        return associatedSheetName;
    }

    public Key getKey() {
        return key == null ? Key.getKey(0) : key;
    }

    public ModificationType getModificationType() {
        switch (modificationConstants) {
            case ChartModificationConstants.ModificationActions.CHART_INSERT: { return ModificationType.INSERT; }
            case ChartModificationConstants.ModificationActions.CHART_DELETED: { return ModificationType.DELETE; }
            case ChartModificationConstants.ModificationActions.DATA_TABLE_MODIFIED:
            case ChartModificationConstants.ModificationActions.CHART_DATA_SOURCE_MODIFIED: { return ModificationType.DATA; }
            case ChartModificationConstants.ModificationActions.CHART_REPOSITIONED: { return ModificationType.MOVE; }
            case ChartModificationConstants.ModificationActions.CHART_TITLE_MODIFIED: { return ModificationType.TITLE_MODIFIED; }
            case ChartModificationConstants.ModificationActions.CHART_SUBTITLE_MODIFIED: { return ModificationType.SUBTITLE_MODIFIED; }
            case ChartModificationConstants.ModificationActions.CHART_X_AXIS_TITLE_MODIFIED: { return ModificationType.X_AXIS_TITLE_MODIFIED; }
            case ChartModificationConstants.ModificationActions.CHART_Y_AXIS_TITLE_MODIFIED: { return ModificationType.Y_AXIS_TITLE_MODIFIED; }
            case ChartModificationConstants.ModificationActions.PLOT_LINE_LABEL_MODIFIED: { return ModificationType.PLOT_LINE_LABEL_MODIFIED; }
            case ChartModificationConstants.ModificationActions.PLOT_LINE_VALUE_MODIFIED: { return ModificationType.PLOT_LINE_VALUE_MODIFIED; }
            case ChartModificationConstants.ModificationActions.THEME_MODIFIED: { return ModificationType.THEME_MODIFIED; }
            case ChartModificationConstants.ModificationActions.TOTAL_DATALABELS_TEXT_MODIFIED: { return ModificationType.TOTAL_DATALABELS_TEXT_MODIFIED; }
            case ChartModificationConstants.ModificationActions.X_AXIS_PREFIX: { return ModificationType.X_AXIS_PREFIX; }
            case ChartModificationConstants.ModificationActions.X_AXIS_SUFFIX: { return ModificationType.X_AXIS_SUFFIX; }
            case ChartModificationConstants.ModificationActions.Y_AXIS_PREFIX: { return ModificationType.Y_AXIS_PREFIX; }
            case ChartModificationConstants.ModificationActions.Y_AXIS_SUFFIX: { return ModificationType.Y_AXIS_SUFFIX; }
            case ChartModificationConstants.ModificationActions.LEGEND_TITLE_MODIFIED: { return ModificationType.LEGEND_TITLE_MODIFIED; }
            case ChartModificationConstants.ModificationActions.Y_AXIS_MIN_MODIFIED: { return ModificationType.Y_AXIS_MIN; }
            case ChartModificationConstants.ModificationActions.Y_AXIS_MAX_MODIFIED: { return ModificationType.Y_AXIS_MAX; }
            case ChartModificationConstants.ModificationActions.Y_AXIS_INTERVAL_MODIFIED: {return ModificationType.Y_AXIS_INTERVAL; }
            case ChartModificationConstants.ModificationActions.SP_DATALABELS_CUSTOM_VALUE_MODIFIED: {return ModificationType.SP_DATALABELS_CUSTOM_VALUE; }
            case ChartModificationConstants.ModificationActions.SERIES_DATALABELS_CUSTOM_VALUE_MODIFIED: {return ModificationType.SERIES_DATALABELS_CUSTOM_VALUE; }
            case ChartModificationConstants.ModificationActions.DP_DATALABELS_CUSTOM_VALUE_MODIFIED: { return ModificationType.DATA_PROPS_DATALABELS_CUSTOM_VALUE; }
            case ChartModificationConstants.ModificationActions.TRENDLINE_DATALABELS_CUSTOM_VALUE_MODIFIED: { return ModificationType.TRENDLINE_DATALABELS_CUSTOM_VALUE; }
            case ChartModificationConstants.ModificationActions.SP_TRENDLINE_DATALABELS_CUSTOM_VALUE_MODIFIED: { return ModificationType.SP_TRENDLINE_DATALABELS_CUSTOM_VALUE; }
        }

        return ModificationType.DATA;
    }

    public static ModifiedChartPOJO fromJSONObject(JSONObjectWrapper object) {
        String chartID = object.getString(ChartActionConstants.JSONConstants.CHART_ID);
        String asn = object.getString(ChartActionConstants.JSONConstants.ASSOCIATED_SHEET_NAME);
        int action = object.getInt(ChartActionConstants.JSONConstants.MODIFIED_CHART_ACTION);
        String key = ChartUtils.optFromJSONObject(object, ChartActionConstants.JSONConstants.KEY);

        if(key != null) {
            return new ModifiedChartPOJO(chartID, action, asn, Key.getKey(key));
        }
        return new ModifiedChartPOJO(chartID, action, asn);
    }

    public static JSONObjectWrapper toJSONObject(ModifiedChartPOJO modifiedChartPOJO) {
        JSONObjectWrapper object = new JSONObjectWrapper();

        object.put(ChartActionConstants.JSONConstants.CHART_ID, modifiedChartPOJO.getModifiedChartId());
        object.put(ChartActionConstants.JSONConstants.MODIFIED_CHART_ACTION, modifiedChartPOJO.getModificationConstants());
        object.put(ChartActionConstants.JSONConstants.ASSOCIATED_SHEET_NAME, modifiedChartPOJO.getAssociatedSheetName());
        ChartUtils.putIfNotNull(object, ChartActionConstants.JSONConstants.KEY, modifiedChartPOJO.getKey());

        return object;
    }
}
