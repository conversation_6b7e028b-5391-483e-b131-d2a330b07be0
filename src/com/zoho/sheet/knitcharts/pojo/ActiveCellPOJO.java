package com.zoho.sheet.knitcharts.pojo;

import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.Objects;

public class ActiveCellPOJO implements POJO {

    private String associatedSheetName;

    private int row;

    private int column;

    public String getAssociatedSheetName() {
        return associatedSheetName;
    }

    public void setAssociatedSheetName(String associatedSheetName) {
        this.associatedSheetName = associatedSheetName;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public int getColumn() {
        return column;
    }

    public void setColumn(int column) {
        this.column = column;
    }

    public void constructFromJSON(JSONWrapper json) {
        JSONObjectWrapper jsonObject = ChartUtils.JSONToJSONObject(json);

        Objects.requireNonNull(jsonObject);
        associatedSheetName = jsonObject.getString(JSONConstants.CURRENT_ACTIVE_SHEET);
        row = jsonObject.getInt(JSONConstants.CURRENT_ACTIVE_ROW);
        column = jsonObject.getInt(JSONConstants.CURRENT_ACTIVE_COLUMN);
    }

    public JSONWrapper convertToJSON() {
        JSONObjectWrapper jsonObject = new JSONObjectWrapper();

        jsonObject.put(JSONConstants.CURRENT_ACTIVE_COLUMN, column);
        jsonObject.put(JSONConstants.CURRENT_ACTIVE_ROW, row);
        jsonObject.put(JSONConstants.CURRENT_ACTIVE_SHEET, associatedSheetName);

        return jsonObject;
    }
}
