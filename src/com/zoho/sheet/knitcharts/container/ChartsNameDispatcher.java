package com.zoho.sheet.knitcharts.container;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ChartsNameDispatcher implements Cloneable {

    private List<String> chartNames = new ArrayList<>();

    public String getChartName(String chartName) {
        if(chartName == null) {
            String nextChartName = getNextChartName();

            chartNames.add(nextChartName);
            return nextChartName;
        } else {
            chartNames.add(chartName);
            return chartName;
        }
    }

    public void removeChartName(String chartName){
        chartNames.remove(chartName);
    }

    private String getNextChartName(){
        int count = 1;
        Set<String> namesSet = new HashSet<>(chartNames);
        while(true) {
            String nextChartName = String.format("Chart %d", count);                                        // NO I18N
            if(!namesSet.contains(nextChartName)) { return nextChartName; }
            count++;
        }
    }

    @Override
    public ChartsNameDispatcher clone() {

        try {
            ChartsNameDispatcher clone = (ChartsNameDispatcher) super.clone();
            clone.chartNames = new ArrayList<>(chartNames);
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException(e);
        }

    }
}
