package com.zoho.sheet.knitcharts.macros.converter;

import com.adventnet.zoho.websheet.model.Sheet;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.Iterator;

/**
 * Chart Insert action to macros converter
 * <AUTHOR>
 */
public abstract class ChartInsertConverter implements ActionToMacrosConverter {

    private JSONObjectWrapper actionJSON;

    private StringBuffer codeBuffer;

    private Sheet sheet;

    @Override
    public void convert(Sheet sheet, JSONObjectWrapper actionJSON, StringBuffer codeBuffer) {
        this.actionJSON = actionJSON;
        this.codeBuffer = codeBuffer;
        this.sheet = sheet;
        initiateConversion();
    }

    private void initiateConversion() {
        JSONArrayWrapper chartJSONs = ChartUtils.optFromJSONObject(actionJSON, ChartActionConstants.JSONConstants.CHART_JSON);
        if(chartJSONs == null || chartJSONs.isEmpty()) { return; }

        Iterator<?> iterator = chartJSONs.iterator();
        while(iterator.hasNext()) {
            JSONObjectWrapper chartJSON = (JSONObjectWrapper) iterator.next();
            forEachChartJSON(chartJSON);
        }
    }

    protected abstract void forEachChartJSON(JSONObjectWrapper chartJSON);

    protected JSONObjectWrapper getActionJSON() {
        return actionJSON;
    }

    protected StringBuffer getCodeBuffer() {
        return codeBuffer;
    }

    protected Sheet getSheet() {
        return sheet;
    }
}
