package com.zoho.sheet.knitcharts.macros.vba.converter;

import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.chart.frameworkchart.api.FrameworkChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.chartmeta.ChartMeta;
import com.zoho.sheet.knitcharts.macros.converter.ChartInsertConverter;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.Objects;

import static com.zoho.sheet.knitcharts.constants.ChartConstants.VBAMacroConstants;

public class VBAChartInsertConverter extends ChartInsertConverter {

    @Override
    protected void forEachChartJSON(JSONObjectWrapper chartJSON) {
        ChartMeta chartMeta = new ChartMeta();
        JSONObjectWrapper sheetMetaJSON = ChartUtils.optFromJSONObject(chartJSON, ChartActionConstants.JSONConstants.SHEET_META);
        SheetMeta sheetMeta = ChartUtils.getSheetMeta(sheetMetaJSON);

        chartMeta.fromJSON(ChartUtils.optFromJSONObject(chartJSON, ChartActionConstants.JSONConstants.CHART_META));
        sheetMeta.fromJSON(sheetMetaJSON);
        convert(chartMeta, sheetMeta);
    }

    private void convert(ChartMeta chartMeta, SheetMeta sheetMeta) {
        StringBuffer codeBuffer = getCodeBuffer();
        ChartType chartType = FrameworkChartGetterAPI.getChartType(chartMeta);
        /* ***************** Converting starting line ******************** */
        codeBuffer.append(VBAMacroConstants.STARTLINE);
        codeBuffer.append("With ActiveSheet.ChartObjects.AddChart(ChartType:=\"");                  // NO I18N
        codeBuffer.append(chartType.getValue());
        codeBuffer.append("\", DataRange:=\"");                                                 // NO I18N
        codeBuffer.append(ChartUtils.join(SheetChartGetterAPI.getDataSources(sheetMeta), ";"));     // NO I18N
        codeBuffer.append("\", SC:=");                                                   // NO I18N
        codeBuffer.append(SheetChartGetterAPI.getStartColumn(sheetMeta));
        codeBuffer.append(", SCD:=");                                                // NO I18N
        codeBuffer.append(SheetChartGetterAPI.getStartColumnDiff(sheetMeta));
        codeBuffer.append(", SR:=");                                                   // NO I18N
        codeBuffer.append(SheetChartGetterAPI.getStartRow(sheetMeta));
        codeBuffer.append(", SRD:=");                                                // NO I18N
        codeBuffer.append(SheetChartGetterAPI.getStartRowDiff(sheetMeta));
        codeBuffer.append(", Width:=");                                                   // NO I18N
        codeBuffer.append(SheetChartAPI.getWidthOrDefault(sheetMeta, chartType));
        codeBuffer.append(", Height:=");                                                // NO I18N
        codeBuffer.append(SheetChartAPI.getHeightOrDefault(sheetMeta, chartType));
        codeBuffer.append(")");                                                         // NO I18N
        /* ***************** Setting chart title ******************** */
        if(hasTitle(chartMeta)) {
            codeBuffer.append(VBAMacroConstants.STARTLINE);
            codeBuffer.append(VBAMacroConstants.SUBLINE);
            codeBuffer.append(".Chart.HasTitle = True");                                    // NO I18N
            codeBuffer.append(VBAMacroConstants.STARTLINE);
            codeBuffer.append(VBAMacroConstants.SUBLINE);
            codeBuffer.append(".Chart.ChartTitle.Text = \"");                                 // NO I18N
            codeBuffer.append(FrameworkChartGetterAPI.getChartTitle(chartMeta));
            codeBuffer.append("\"");                                                        // NO I18N
        }
        /* ***************** converting last line ******************** */
        codeBuffer.append(VBAMacroConstants.STARTLINE);
        codeBuffer.append("End With");                                                      // NO I18N
    }

    private boolean hasTitle(ChartMeta chartMeta) {
        Boolean status = FrameworkChartGetterAPI.getChartTitleEnabled(chartMeta);

        if(status == null) {
            status = Objects.nonNull(FrameworkChartGetterAPI.getChartTitle(chartMeta));
        }
        return status;
    }

    private boolean hasLegend(ChartMeta chartMeta) {
        return FrameworkChartGetterAPI.getLegendStatus(chartMeta);
    }

}
