package com.zoho.sheet.knitcharts.macros.vba.converter;

import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.zoho.sheet.knitcharts.macros.converter.ActionToMacrosConverter;


public class VBAConverterSupplier {

    public static ActionToMacrosConverter supply(int actionConstant) {
        switch (actionConstant) {
            case ActionConstants.INSERT_RECOMMENDED_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART_VIA_CACHE:
            case ActionConstants.INSERT_CHART: { return new VBAChartInsertConverter(); }
        }
        return null;
    }

}
