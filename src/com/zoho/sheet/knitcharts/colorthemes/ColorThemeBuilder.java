package com.zoho.sheet.knitcharts.colorthemes;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.type.ColorPaletteType;

import java.util.List;

/**
 * Chart's Color theme colors builder
 */
public interface ColorThemeBuilder {

    /**
     * Method to get the colors for the given theme
     * @return list of 6 colour with respect to the given theme
     */
    List<String> getThemeColors(boolean isReversed);

    /**
     * Method to get all 18 series colours, then this 18 colours reused for rest of the series
     * @param isReversed param to mention the colours should be reverse order
     * @return list of all 18 series colours
     */
    List<String> getSeriesColors(boolean isReversed);

    /**
     * Method to get the custom color theme builder
     * @param baseColor Base Color
     * @return Custom Color Theme builder
     */
    static ColorThemeBuilder getCustomThemeBuilder(String baseColor) {
        return new CustomThemeBuilder(baseColor);
    }

    /**
     * Method to get default color theme builder
     * @param workbook Workbook instance
     * @param colorPaletteType Color Palette type
     * @return color theme builder
     */
    static ColorThemeBuilder getDefaultThemeBuilder(Workbook workbook,
                                                    ColorPaletteType colorPaletteType) {
        return new PaletteThemeBuilder(workbook, colorPaletteType);
    }

}
