package com.zoho.sheet.knitcharts.chartauditrail.sheetchart;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.knitcharts.chartauditrail.ChartAuditTrailMessage;
import com.zoho.sheet.knitcharts.constants.AuditTrailMessageKeys;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.constants.FrameworkChartActionConstants;
import com.zoho.sheet.knitcharts.constants.SheetChartActionConstants;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.Collections;
import java.util.List;

import static com.zoho.sheet.knitcharts.constants.SheetChartActionConstants.SubActionConstants.*;

public class SheetChartAuditTrailHelper {

    public static ChartAuditTrailMessage getMessage(JSONObjectWrapper chartJSON, String sheetName, int count) {
        int subAction = chartJSON.getInt(ChartActionConstants.JSONConstants.SUB_ACTION);
        List<String> links = Collections.singletonList(sheetName);

        switch (subAction) {
            case CHART_MOVE: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_MOVE, links); }
            case CHART_RESIZE: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_RESIZE, links); }
            case AUTO_EXPAND_STATUS_EDIT: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_CHART_AUTO_EXPAND, links); }
            case IHC_STATUS_EDIT: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_IHC_STATUS, links); }
            case SERIES_IN_EDIT: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_SERIES_IN, links); }
            case FIRST_COL_AS_LABEL_EDIT: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_FCL_STATUS, links); }
            case FIRST_ROW_AS_LABEL_EDIT: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_FRL_STATUS, links); }
            case DATA_SOURCES_EDIT: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_DATA_RANGE, links); }
            case AUTO_FILL_TYPE_EDIT: {return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_AUTO_FILL_OPTION, links); }
            case AGGREGATION_STATUS_EDIT: {return getAggregationStatusEditMessage(chartJSON, links); }
            case AGGREGATION_RESET: {return new ChartAuditTrailMessage(AuditTrailMessageKeys.AGGREGATION_RESET, links); }
            case AGGREGATION_OPTION_EDIT: {return getAggregationEditMessage(chartJSON, links); }
            case FILTER_STATUS_EDIT: {return getFilterStatusEditMessage(chartJSON, links); }
            case FILTER_OPTIONS_EDIT: { return getFilterOptionsEditMessage(chartJSON, links); }
            case FILTER_OPTIONS_RESET: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.FILTER_RESET, links); }
            case PIVOT_FILTER_PINNED_STATUS_EDIT: {return getPivotFilterPinnedStatus(chartJSON, links); }
            case COLOR_PALETTE_TYPE_EDIT: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.COLOR_SCHEME_CHANGED, links); }
            case COLOR_PALETTE_REVERSE_STATUS_EDIT: {return getColorPalatteReveresedMessage(chartJSON, links); }
            case CHART_VISIBILITY_STATUS_EDIT: { return getMessageForChartVisibility(chartJSON, links, count); }
            case CHART_RENAME: { return getMessageForChartNameEdit(chartJSON, links); }
            case IMAGE_SERIES_EDIT: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.IMAGE_SERIES_EDIT, links); }
            case INFO_TEXT_STATUS_EDIT: { return getMessageForInfoTextStatusEdit(chartJSON, links); }
            case REMOVE_BLANK_STATUS_EDIT: { return getRemoveBlankEditMessage(chartJSON, links); }

            default: { return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_EDIT, links); }
        }
    }

    private static ChartAuditTrailMessage getMessageForInfoTextStatusEdit(JSONObjectWrapper chartJSON, List<String> links) {
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        boolean status = valueJSON.getBoolean(SheetChartActionConstants.JSONConstants.IS_ENABLED);

        if(status) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.INFO_TEXT_ENABLED, links);
        } else {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.INFO_TEXT_DISABLED, links);
        }
    }

    private static ChartAuditTrailMessage getMessageForChartNameEdit(JSONObjectWrapper chartJSON, List<String> links) {
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        String oldName = valueJSON.getString(SheetChartActionConstants.JSONConstants.OLD_NAME);
        String newName = valueJSON.getString(SheetChartActionConstants.JSONConstants.NAME);

        return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_RENAMED, links, CollectionsUtils.listOf(oldName, newName));
    }


    private static ChartAuditTrailMessage getAggregationStatusEditMessage(JSONObjectWrapper chartJSON, List<String> links) {
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        if(valueJSON.has(SheetChartActionConstants.JSONConstants.IS_ENABLED) &&
                valueJSON.getBoolean(SheetChartActionConstants.JSONConstants.IS_ENABLED)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.AGGREGATION_ENABLED, links);
        }
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.AGGREGATION_DISABLED, links);
    }

    private static ChartAuditTrailMessage getAggregationEditMessage(JSONObjectWrapper chartJSON, List<String> links) {
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        if(valueJSON.has(SheetChartActionConstants.JSONConstants.AGGREGATION_OPERATION)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_AGG_OPERATION, links);
        } else if(valueJSON.has(SheetChartActionConstants.JSONConstants.VALUE_COLUMNS)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_AGG_VALUE_COL, links);
        } else if(valueJSON.has(SheetChartActionConstants.JSONConstants.AXIS_COLUMN)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_AGG_AXIS_COL, links);
        } else if(valueJSON.has(SheetChartActionConstants.JSONConstants.AXIS_GROUPING_TYPE)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_AGG_AXIS_GRP, links);
        } else if(valueJSON.has(SheetChartActionConstants.JSONConstants.LEGEND_COLUMN)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_AGG_LEGEND_COL, links);
        }

        return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_AGG_LEGEND_GRP, links);
    }

    private static ChartAuditTrailMessage getFilterStatusEditMessage(JSONObjectWrapper chartJSON, List<String> links) {
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        if(valueJSON.has(SheetChartActionConstants.JSONConstants.IS_ENABLED)
                && valueJSON.getBoolean(SheetChartActionConstants.JSONConstants.IS_ENABLED)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.FILTER_ENABLED, links);
        }
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.FILTER_DISABLED, links);
    }

    private static ChartAuditTrailMessage getFilterOptionsEditMessage(JSONObjectWrapper chartJSON, List<String> links) {
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        if(valueJSON.has(SheetChartActionConstants.JSONConstants.FILTER_TYPE)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_FILTER_TYPE, links);
        } else if(valueJSON.has(SheetChartActionConstants.JSONConstants.FILTER_COUNT)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_FILTER_COUNT, links);
        }
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHANGED_FILTER_COL, links);
    }

    private static ChartAuditTrailMessage getPivotFilterPinnedStatus(JSONObjectWrapper chartJSON, List<String> links) {
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        if(valueJSON.has(SheetChartActionConstants.JSONConstants.IS_ENABLED) &&
                valueJSON.getBoolean(SheetChartActionConstants.JSONConstants.IS_ENABLED)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.PIVOT_FILTER_PINNED, links);
        }
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.PIVOT_FILTER_UNPINNED, links);
    }

    private static ChartAuditTrailMessage getColorPalatteReveresedMessage(JSONObjectWrapper chartJSON, List<String> links) {
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        if(valueJSON.has(SheetChartActionConstants.JSONConstants.IS_ENABLED)
                && valueJSON.getBoolean(SheetChartActionConstants.JSONConstants.IS_ENABLED)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.COLOR_SCHEME_REVERSED, links);
        }
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.COLOR_SCHEME_UNREVERSED, links);
    }

    private static ChartAuditTrailMessage getRemoveBlankEditMessage(JSONObjectWrapper chartJSON, List<String> links){
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(FrameworkChartActionConstants.JSONConstants.VALUE);
        if(valueJSON.has(FrameworkChartActionConstants.JSONConstants.IS_ENABLED) &&
                valueJSON.getBoolean(FrameworkChartActionConstants.JSONConstants.IS_ENABLED)) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.BLANK_REMOVED, links);
        }
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.BLANK_ADDED, links);
    }

    private static ChartAuditTrailMessage getMessageForChartVisibility(JSONObjectWrapper chartJSON, List<String> links, int count) {
        JSONObjectWrapper valueJSON = chartJSON.getJSONObject(ChartActionConstants.JSONConstants.VALUE);
        if(valueJSON.has(SheetChartActionConstants.JSONConstants.VISIBLE) &&
                valueJSON.getBoolean(SheetChartActionConstants.JSONConstants.VISIBLE)) {
            if(count == 1) {
                return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_VISIBLE, links);
            } else {
                return new ChartAuditTrailMessage(AuditTrailMessageKeys.MULTI_CHART_VISIBLE, links, CollectionsUtils.listOf(String.valueOf(count)));
            }
        }
        if(count == 1) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_HIDDEN, links);
        } else {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.MULTI_CHART_HIDE, links, CollectionsUtils.listOf(String.valueOf(count)));
        }
    }

}
