package com.zoho.sheet.knitcharts.chartauditrail;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.zoho.sheet.knitcharts.chartauditrail.frameworkchart.FrameworkChartAuditTrailHelper;
import com.zoho.sheet.knitcharts.chartauditrail.sheetchart.SheetChartAuditTrailHelper;
import com.zoho.sheet.knitcharts.constants.AuditTrailMessageKeys;
import com.zoho.sheet.knitcharts.miscellaneous.exception.ErrorMessages;
import com.zoho.sheet.knitcharts.miscellaneous.exception.UnsupportedChartActionException;
import com.zoho.sheet.knitcharts.constants.SheetChartActionConstants;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.Collections;
import java.util.List;

/**
 * Audit trail message dispatcher for chart actions
 *
 * <AUTHOR>
 */
public final class ChartAuditTrailHelper {

    /**
     * Generates an audit trail message for single Chart related actions
     *
     * @param action    Action constant
     * @param chartJSON Chart JSON
     * @return Chart Audit trail message
     */
    public static ChartAuditTrailMessage getMessage(Workbook workbook, int action, JSONObjectWrapper chartJSON, String sheetName, int count) {
        switch (action) {
            case ActionConstants.INSERT_CHART:
            case ActionConstants.INSERT_PIVOT_CHART:
            case ActionConstants.INSERT_TABLE_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART_VIA_CACHE:
            case ActionConstants.INSERT_RECOMMENDED_CHART: { return getMessageForInsertChart(sheetName); }
            case ActionConstants.CLONE_CHART: { return getMessageForCloneChart(sheetName); }
            case ActionConstants.DELETE_CHART: { return getMessageForDeleteChart(sheetName, count); }
            case ActionConstants.FRAMEWORK_CHART_EDIT: { return FrameworkChartAuditTrailHelper.getMessage(chartJSON, sheetName); }
            case ActionConstants.SHEET_CHART_EDIT: { return SheetChartAuditTrailHelper.getMessage(chartJSON, sheetName, count); }
            case ActionConstants.CHART_MOVE_TO_OTHER_SHEET: { return getMessageForChartMoveToOtherSheet(workbook, chartJSON, sheetName); }
            case ActionConstants.PUBLISH_CHART: { return getMessageForPublishChart(sheetName); }
            case ActionConstants.UNPUBLISH_CHART: { return getMessageForUnPublishChart(sheetName); }
            case ActionConstants.PASTE_CHART_STYLES: { return getMessageForPasteChartStyles(sheetName); }
            case ActionConstants.RESET_CHART_STYLES: { return getMessageForResetChartStyles(sheetName); }
            case ActionConstants.PASTE_CHART: { return getMessageForPasteChart(sheetName); }
            default: { throw new UnsupportedChartActionException(ErrorMessages.INVALID_CHART_ACTION); }
        }
    }

    private static ChartAuditTrailMessage getMessageForPasteChartStyles(String sheetName) {
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.PASTE_CHART_STYLES, Collections.singletonList(sheetName));
    }

    private static ChartAuditTrailMessage getMessageForPasteChart(String sheetName) {
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_PASTE,  Collections.singletonList(sheetName));
    }

    private static ChartAuditTrailMessage getMessageForResetChartStyles(String sheetName) {
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.RESET_CHART_STYLES, Collections.singletonList(sheetName));
    }

    private static ChartAuditTrailMessage getMessageForPublishChart(String sheetName) {
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_PUBLISH, Collections.singletonList(sheetName));
    }

    private static ChartAuditTrailMessage getMessageForUnPublishChart(String sheetName) {
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_UNPUBLISH, Collections.singletonList(sheetName));
    }

    private static ChartAuditTrailMessage getMessageForInsertChart(String sheetName) {
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_INSERT, Collections.singletonList(sheetName));
    }

    private static ChartAuditTrailMessage getMessageForCloneChart(String sheetName){
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.CLONE_CHART, Collections.singletonList(sheetName));
    }

    private static ChartAuditTrailMessage getMessageForDeleteChart(String sheetName, int count) {
        if(count == 1) {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_DELETE, Collections.singletonList(sheetName));
        } else {
            return new ChartAuditTrailMessage(AuditTrailMessageKeys.DELETED_MULTI_CHART, Collections.singletonList(sheetName), CollectionsUtils.listOf(String.valueOf(count)));
        }
    }

    private static ChartAuditTrailMessage getMessageForChartMoveToOtherSheet(Workbook workbook, JSONObjectWrapper chartJSON, String sheetName) {
        String destSheet = workbook.getSheetByAssociatedName(chartJSON.getString(SheetChartActionConstants.JSONConstants.TARGET_SHEET_ID)).getName();

        List<String> links = CollectionsUtils.listOf(sheetName, destSheet);
        return new ChartAuditTrailMessage(AuditTrailMessageKeys.CHART_MOVE_TO_OTHER_SHEET, links, Collections.singletonList(destSheet));
    }

}
