package com.zoho.sheet.knitcharts.chartauditrail;

import java.util.List;

/**
 * Audit trail message object for chart actions.<br>
 * This class can be further extended with many fields for future usage
 * <AUTHOR>
 */
public final class ChartAuditTrailMessage {
    /* Key for the audit trail message */
    private final String messageKey;
    /* link strings */
    private List<String> links;
    /* no link strings */
    private List<String> noLinks;

    public ChartAuditTrailMessage(String messageKey){
        this.messageKey = messageKey;
    }

    public ChartAuditTrailMessage(String messageKey, List<String> links){
        this.messageKey = messageKey;
        this.links = links;
    }

    public ChartAuditTrailMessage(String messageKey, List<String> links, List<String> noLinks){
        this.messageKey = messageKey;
        this.links = links;
        this.noLinks = noLinks;
    }

    public String getMessageKey() {
        return messageKey;
    }

    public List<String> getLinks() {
        return links;
    }

    public List<String> getNoLinks() {
        return noLinks;
    }
}
