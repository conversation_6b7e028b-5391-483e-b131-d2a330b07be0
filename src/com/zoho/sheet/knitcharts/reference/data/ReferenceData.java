package com.zoho.sheet.knitcharts.reference.data;

import com.adventnet.zoho.websheet.model.SpreadsheetSettings;

public class ReferenceData {

    private final ReferenceDataType referenceDataType;

    private final Object data;

    private final int rowIndex;

    private final int columnIndex;

    private final SpreadsheetSettings settings;

    public ReferenceData(SpreadsheetSettings settings, ReferenceDataType referenceDataType, Object data, int rowIndex, int columnIndex) {
        this.settings = settings;
        this.referenceDataType = referenceDataType;
        this.data = data;
        this.rowIndex = rowIndex;
        this.columnIndex = columnIndex;
    }

    public ReferenceDataType getReferenceDataType() {
        return referenceDataType;
    }

    public Object getData() {
        return data;
    }

    public int getRowIndex() {
        return rowIndex;
    }

    public int getColumnIndex() {
        return columnIndex;
    }

    public SpreadsheetSettings getSettings() {
        return settings;
    }
}
