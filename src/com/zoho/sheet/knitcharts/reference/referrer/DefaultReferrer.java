package com.zoho.sheet.knitcharts.reference.referrer;

public class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends Referrer {

    public DefaultReferrer(int referenceID, ReferrerType type) {
        super(referenceID, type);
    }

    @Override
    public int hashCode() {
        if(hash == null) {
            String codeString = String.format("%d%d", referenceID, type.ordinal());             // NO I18N
            hash = codeString.hashCode();
        }
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof DefaultReferrer) {
            DefaultReferrer other = (DefaultReferrer)obj;
            return referenceID == other.referenceID && type == other.type;
        }
        return false;
    }
}
