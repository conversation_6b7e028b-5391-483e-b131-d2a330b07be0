package com.zoho.sheet.knitcharts.reference.referrer;

import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;

import java.util.Objects;

/**
 * Reference's referrer with key
 * <AUTHOR>
 */
public class KeyReferrer extends Referrer{

    private final Key key;

    public KeyReferrer(int referenceID, ReferrerType type, Key key) {
        super(referenceID, type);
        this.key = key;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) { return true; }
        if (o == null || getClass() != o.getClass()) { return false; }
        KeyReferrer that = (KeyReferrer) o;
        return Objects.equals(referenceID, that.referenceID) && Objects.equals(type, that.type)
                && Objects.equals(key, that.key);
    }

    @Override
    public int hashCode() {
        return Objects.hash(referenceID, key, type);
    }

    public Key getKey() {
        return key;
    }
}
