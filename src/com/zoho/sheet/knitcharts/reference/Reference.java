package com.zoho.sheet.knitcharts.reference;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.reference.iterater.ReferenceDataIterator;

public interface Reference {

    String getReferenceName(Workbook workbook);

    ReferenceType getReferenceType();

    ReferenceDataIterator getReferenceDataIterator(Workbook workbook, boolean skipHiddenCells);

    int getColumnCount(Workbook workbook, boolean skipHiddenCells);

    int getRowCount(Workbook workbook, boolean skipHiddenCells);

    int getStartColumnIndex(Workbook workbook);

    int getStartRowIndex(Workbook workbook);

    String getAssociatedSheetName(Workbook workbook);

    int getEndColumnIndex(Workbook workbook);

    int getEndRowIndex(Workbook workbook);

    default TextReference getTextReference() {
        return new TextReference(this);
    }

    default ValueReference getValueReference() {
        return new ValueReference(this);
    }

}
