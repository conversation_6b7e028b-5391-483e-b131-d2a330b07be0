package com.zoho.sheet.knitcharts.reference;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.miscellaneous.datastructure.Key;
import com.zoho.sheet.knitcharts.reference.referrer.*;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartGetterAPI;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.PivotChartSheetMeta;
import com.zoho.sheet.knitcharts.chart.sheetchart.meta.SheetMeta;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.ReferenceUtils;
import com.zoho.sheet.knitcharts.utils.SheetChartAPIUtils;

import java.util.ArrayList;
import java.util.List;

public class ReferencePoolFiller {

    public static void fillWithSheetMeta(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        fillDataSource(workbook, pool, sheetMeta, referenceID);
        fillTitle(workbook, pool, sheetMeta, referenceID);
        fillSubtitle(workbook, pool, sheetMeta, referenceID);
        fillXAxis(workbook, pool, sheetMeta, referenceID);
        fillYAxis(workbook, pool, sheetMeta, referenceID);
        fillTotalDatalabelsReference(workbook, pool, sheetMeta, referenceID);
        fillLegendTitleReference(workbook, pool, sheetMeta, referenceID);
        fillSeriesProperties(workbook, pool, sheetMeta, referenceID);
        fillSharedSeriesReferences(workbook, pool, sheetMeta, referenceID);
    }

    private static void fillDataSource(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        if(sheetMeta instanceof PivotChartSheetMeta) { return; }            // Need not to add any reference to reference pool for pivot chart

        List<String> dataSources = SheetChartGetterAPI.getDataSources(sheetMeta);
        List<String> validDataSources = new ArrayList<>();

        for(int idx = 0; idx < dataSources.size(); idx++) {
            String dataSource = dataSources.get(idx);
            Reference reference = pool.addReference(workbook, dataSource, new KeyReferrer(referenceID, ReferrerType.CHART_DATA_SOURCE, Key.getKey(idx)), true);

            if(reference.getReferenceType() != ReferenceType.INVALID) {
                validDataSources.add(reference.getReferenceName(workbook));
            }
        }

        SheetChartAPI.updateDataSources(sheetMeta, validDataSources);
    }

    private static void fillTitle(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        String textReference = SheetChartGetterAPI.getChartTitleTextReference(sheetMeta);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer =  new DefaultReferrer(referenceID, ReferrerType.TITLE);
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    public static void fillSubtitle(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        String textReference = SheetChartGetterAPI.getChartSubtitleTextReference(sheetMeta);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer =  new DefaultReferrer(referenceID, ReferrerType.SUBTITLE);
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    public static void fillXAxis(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        SheetChartAPIUtils.forEachXAxisReferences(sheetMeta, (xAxisIndex) -> {

            fillXAxisTitle(workbook, pool, sheetMeta, referenceID, xAxisIndex);
            fillXAxisPrefix(workbook, pool, sheetMeta, referenceID, xAxisIndex);
            fillXAxisSuffix(workbook, pool, sheetMeta, referenceID, xAxisIndex);
        });
    }

    private static void fillXAxisTitle(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int xAxisIndex) {
        String textReference = SheetChartGetterAPI.getChartXAxisTitleTextReference(sheetMeta, xAxisIndex);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.X_AXIS_TITLE, Key.getKey(xAxisIndex));
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillXAxisPrefix(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int xAxisIndex) {
        String textReference = SheetChartGetterAPI.getXAxisPrefixTextReference(sheetMeta, xAxisIndex);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.X_AXIS_PREFIX, Key.getKey(xAxisIndex));
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillXAxisSuffix(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int xAxisIndex) {
        String textReference = SheetChartGetterAPI.getXAxisSuffixTextReference(sheetMeta, xAxisIndex);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.X_AXIS_SUFFIX, Key.getKey(xAxisIndex));
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    public static void fillYAxis(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        SheetChartAPIUtils.forEachYAxisReferences(sheetMeta, (yAxisIndex) -> {
            fillYAxisTitle(workbook, pool, sheetMeta, referenceID, yAxisIndex);
            fillYAxisPrefix(workbook, pool, sheetMeta, referenceID, yAxisIndex);
            fillYAxisSuffix(workbook, pool, sheetMeta, referenceID, yAxisIndex);
            fillYAxisPlotlineReferences(workbook, pool, sheetMeta, referenceID, yAxisIndex);
            fillYAxisMin(workbook, pool, sheetMeta, referenceID, yAxisIndex);
            fillYAxisMax(workbook, pool, sheetMeta, referenceID, yAxisIndex);
            fillYAxisInterval(workbook, pool, sheetMeta, referenceID, yAxisIndex);
        });
    }

    private static void fillYAxisMin(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int yAxisIndex) {
        String valueReference = SheetChartGetterAPI.getYAxisMinReference(sheetMeta, yAxisIndex);

        if(ChartUtils.isTextReference(valueReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.Y_AXIS_MIN, Key.getKey(yAxisIndex));
            Reference reference = ReferenceUtils.getReference(workbook, valueReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillYAxisMax(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int yAxisIndex) {
        String valueReference = SheetChartGetterAPI.getYAxisMaxReference(sheetMeta, yAxisIndex);

        if(ChartUtils.isTextReference(valueReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.Y_AXIS_MAX, Key.getKey(yAxisIndex));
            Reference reference = ReferenceUtils.getReference(workbook, valueReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillYAxisInterval(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int yAxisIndex) {
        String valueReference = SheetChartGetterAPI.getYAxisIntervalReference(sheetMeta, yAxisIndex);

        if(ChartUtils.isTextReference(valueReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.Y_AXIS_INTERVAL, Key.getKey(yAxisIndex));
            Reference reference = ReferenceUtils.getReference(workbook, valueReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillYAxisTitle(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int yAxisIndex) {
        String textReference = SheetChartGetterAPI.getChartYAxisTitleTextReference(sheetMeta, yAxisIndex);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.Y_AXIS_TITLE, Key.getKey(yAxisIndex));
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillYAxisPrefix(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int yAxisIndex) {
        String textReference = SheetChartGetterAPI.getYAxisPrefixTextReference(sheetMeta, yAxisIndex);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.Y_AXIS_PREFIX, Key.getKey(yAxisIndex));
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillYAxisSuffix(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int yAxisIndex) {
        String textReference = SheetChartGetterAPI.getYAxisSuffixTextReference(sheetMeta, yAxisIndex);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.Y_AXIS_SUFFIX, Key.getKey(yAxisIndex));
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    public static void fillYAxisPlotlineReferences(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta,
                                                   int referenceID, int yAxisIndex) {
        SheetChartAPIUtils.forEachPlotLineReferences(sheetMeta, yAxisIndex, (plotLineIndex) -> {
            String labelReference = SheetChartGetterAPI.getPlotLinesLabelTextReference(sheetMeta, yAxisIndex, plotLineIndex);
            String valueReference = SheetChartGetterAPI.getPlotLinesValueReference(sheetMeta, yAxisIndex, plotLineIndex);

            if(ChartUtils.isTextReference(labelReference)) {
                Referrer referrer = new KeyReferrer(referenceID, ReferrerType.PLOT_LINE_LABEL,
                        Key.getKey(Key.getKey(yAxisIndex), plotLineIndex));
                Reference reference = ReferenceUtils.getReference(workbook, labelReference, false);

                pool.addReference(workbook, reference, referrer);
            }
            if(ChartUtils.isTextReference(valueReference)) {
                Referrer referrer = new KeyReferrer(referenceID, ReferrerType.PLOT_LINE_VALUE,
                        Key.getKey(Key.getKey(yAxisIndex), plotLineIndex));
                Reference reference = ReferenceUtils.getReference(workbook, valueReference, false);

                pool.addReference(workbook, reference, referrer);
            }
        });
    }

    public static void fillTotalDatalabelsReference(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        String textReference = SheetChartGetterAPI.getTotalDataLabelsReference(sheetMeta);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer =  new DefaultReferrer(referenceID, ReferrerType.TOTAL_DATALABELS);
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    public static void fillLegendTitleReference(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        String textReference = SheetChartGetterAPI.getLegendTitleTextReference(sheetMeta);
        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer =  new DefaultReferrer(referenceID, ReferrerType.LEGEND_TITLE);
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillSeriesProperties(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        for(int seriesIndex: SheetChartAPIUtils.getSeriesReferencesIterable(sheetMeta)) {
            fillSeriesDatalabelsReferences(workbook, pool, sheetMeta, referenceID, seriesIndex);
            fillDataPropertiesReferences(workbook, pool, sheetMeta, referenceID, seriesIndex);
        }
    }

    private static void fillSeriesDatalabelsReferences(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int seriesIndex) {
        fillSeriesDatalabelsCustomValueReferences(workbook, pool, sheetMeta, referenceID, seriesIndex);
    }

    private static void fillSeriesDatalabelsCustomValueReferences(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int seriesIndex) {
        String textReference = SheetChartGetterAPI.getSeriesDatalabelsCustomValueReference(sheetMeta, seriesIndex);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.SERIES_DATALABELS_CUSTOM_VALUE, Key.getKey(seriesIndex));
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillDataPropertiesReferences(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int seriesIndex) {
        for(int dataPropsIndex: SheetChartAPIUtils.getDataPropertyReferencesIterable(sheetMeta, seriesIndex)) {
            fillDataPropertyDatalabelReferences(workbook, pool, sheetMeta, referenceID, seriesIndex, dataPropsIndex);
        }
    }

    private static void fillDataPropertyDatalabelReferences(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int seriesIndex, int dataPropsIndex) {
        fillDataPropertyDatalabelCustomValueRefereces(workbook, pool, sheetMeta, referenceID, seriesIndex, dataPropsIndex);
    }

    private static void fillDataPropertyDatalabelCustomValueRefereces(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID, int seriesIndex, int dataPropsIndex) {
        String textReference = SheetChartGetterAPI.getDataPropertyDatalabelCustomValueReference(sheetMeta, seriesIndex, dataPropsIndex);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer = new KeyReferrer(referenceID, ReferrerType.DP_DATALABELS_CUSTOM_VALUE, Key.getKey(seriesIndex).getChild(dataPropsIndex));
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

    private static void fillSharedSeriesReferences(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        fillSharedSeriesDatalabelsReferences(workbook, pool, sheetMeta, referenceID);
    }

    private static void fillSharedSeriesDatalabelsReferences(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        fillSharedSeriesDatalabelsCustomValueReferences(workbook, pool, sheetMeta, referenceID);
    }

    private static void fillSharedSeriesDatalabelsCustomValueReferences(Workbook workbook, ReferencePool pool, SheetMeta sheetMeta, int referenceID) {
        String textReference = SheetChartGetterAPI.getSharedSeriesDatalabelsCustomValueReference(sheetMeta);

        if(ChartUtils.isTextReference(textReference)) {
            Referrer referrer = new DefaultReferrer(referenceID, ReferrerType.SP_DATALABELS_CUSTOM_VALUE);
            Reference reference = ReferenceUtils.getReference(workbook, textReference, false);

            pool.addReference(workbook, reference, referrer);
        }
    }

}
