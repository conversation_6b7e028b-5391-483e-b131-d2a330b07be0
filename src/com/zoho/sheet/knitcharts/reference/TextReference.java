package com.zoho.sheet.knitcharts.reference;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.zoho.sheet.knitcharts.constants.ChartConstants;
import com.zoho.sheet.knitcharts.reference.data.ReferenceData;
import com.zoho.sheet.knitcharts.reference.data.ReferenceDataType;
import com.zoho.sheet.knitcharts.reference.iterater.ReferenceDataIterator;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.DataUtils;

import java.util.Objects;

public class TextReference {

    private final Reference reference;

    public TextReference(Reference reference) {
        this.reference = reference;
    }

    public String getText(Workbook workbook, int limit) {
        ReferenceDataIterator iterator = reference.getReferenceDataIterator(workbook, false);
        StringBuilder buffer = new StringBuilder();
        if(iterator.hasNext() && buffer.length() < limit) {
            buffer.append(getText(workbook, iterator.next()));
        }

        return buffer.toString();
    }

    public String getText(Workbook workbook) {
        return getText(workbook, ChartConstants.MAX_TEXT_REFERENCE_LEN);
    }


    private String getText(Workbook workbook, ReferenceData data) {
        if(data.getReferenceDataType() == ReferenceDataType.CELL) {
           return getTextFromCell(workbook, (CellImpl) data.getData());
        } else if(data.getReferenceDataType() == ReferenceDataType.VALUE ||
                data.getReferenceDataType() == ReferenceDataType.ARRAY_VALUE) {
            return getTextFromValue(workbook, (Value) data.getData());
        }

        return "";      // NO I18N
    }

    private String getTextFromCell(Workbook workbook, CellImpl cell) {
        if(cell == null) { return ""; }     // NO I18N
        return ChartUtils.requireNonNullElse(cell.getContent(), "");        // NO I18N
    }

    private String getTextFromValue(Workbook workbook, Value value) {
        if(value instanceof ValueWithPattern) {
            ValueWithPattern valueWithPattern = (ValueWithPattern) value;
            ZSPattern pattern = valueWithPattern.getPattern();
            ValueI valueI = valueWithPattern.getValueObject();

            if(pattern != null && valueI != null) {
                ContentObject contentObject = pattern.formatValue(workbook, valueI);
                if(contentObject != null) { return ChartUtils.requireNonNullElse(contentObject.getContent(), ""); }     // NO I18N
            }
            if(valueI != null) {
                Object valueObj = valueI.getValue();
                if(valueObj != null) { return Objects.toString(DataUtils.truncateDecimals(valueObj)); }
            }
        } else if(value != null) {
            Object valueObj = value.getValue();
            if(valueObj != null) { return Objects.toString(DataUtils.truncateDecimals(valueObj)); }
        }
        return "";          // NO I18N
    }
}
