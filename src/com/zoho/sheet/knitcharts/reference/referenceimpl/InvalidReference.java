package com.zoho.sheet.knitcharts.reference.referenceimpl;

import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.reference.ReferenceType;
import com.zoho.sheet.knitcharts.reference.iterater.EmptyReferenceDataIterator;
import com.zoho.sheet.knitcharts.reference.iterater.ReferenceDataIterator;

public class InvalidReference implements Reference {


    @Override
    public String getReferenceName(Workbook workbook) {
        return "";
    }

    @Override
    public ReferenceType getReferenceType() {
        return ReferenceType.INVALID;
    }

    @Override
    public ReferenceDataIterator getReferenceDataIterator(Workbook workbook, boolean skipHiddenCells) {
        return new EmptyReferenceDataIterator();
    }

    @Override
    public int getColumnCount(Workbook workbook, boolean skipHiddenCells) {
        return 0;
    }

    @Override
    public int getRowCount(Workbook workbook, boolean skipHiddenCells) {
        return 0;
    }

    @Override
    public String getAssociatedSheetName(Workbook workbook) {
        return "0#";
    }

    @Override
    public int getEndColumnIndex(Workbook workbook) {
        return 0;
    }

    @Override
    public int getEndRowIndex(Workbook workbook) {
        return 0;
    }

    @Override
    public int getStartColumnIndex(Workbook workbook) {
        return 0;
    }

    @Override
    public int getStartRowIndex(Workbook workbook) {
        return 0;
    }
}
