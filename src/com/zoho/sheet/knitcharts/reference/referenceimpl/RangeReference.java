package com.zoho.sheet.knitcharts.reference.referenceimpl;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.ColumnUtil;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.RowUtil;
import com.adventnet.zoho.websheet.model.util.TableUtil;
import com.zoho.sheet.knitcharts.miscellaneous.exception.InvalidReferenceException;
import com.zoho.sheet.knitcharts.reference.Reference;
import com.zoho.sheet.knitcharts.reference.ReferenceType;
import com.zoho.sheet.knitcharts.reference.iterater.RangeReferenceDataIterator;
import com.zoho.sheet.knitcharts.reference.iterater.ReferenceDataIterator;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.DataUtils;
import com.zoho.sheet.knitcharts.utils.ReferenceUtils;

import java.util.Objects;

public class RangeReference implements Reference {

    private final String referenceName;

    private final ReferenceType referenceType;

    private String associatedSheetName;

    private Integer rowCount, columnCount, startRow, startColumn, endRow, endColumn, hRowCount, hColumnCount;

    private DataRange dataRange;


    public RangeReference(String referenceName, ReferenceType referenceType) {
        this.referenceName = referenceName;
        this.referenceType = referenceType;
    }

    @Override
    public String getReferenceName(Workbook workbook) {
        return referenceName;
    }

    @Override
    public ReferenceType getReferenceType() {
        return referenceType;
    }

    @Override
    public ReferenceDataIterator getReferenceDataIterator(Workbook workbook, boolean skipHiddenCells) {
        Range range = getRange(workbook);
        return new RangeReferenceDataIterator(range, skipHiddenCells, workbook.getSpreadsheetSettings());
    }

    @Override
    public int getColumnCount(Workbook workbook, boolean skipHiddenCells) {
        if(skipHiddenCells) {
            if(hColumnCount == null) {
                int startCol = getStartColumnIndex(workbook), endCol = getEndColumnIndex(workbook);
                int count = getColumnCount(workbook, false);
                Sheet sheet = workbook.getSheetByAssociatedName(getAssociatedSheetName(workbook));

                for (int i = startCol; i <= endCol; i++) {count = ColumnUtil.isHiddenColumn(sheet, i) ? count - 1 : count;}
                hColumnCount = count;
            }
            return hColumnCount;
        }

        if(columnCount == null) { cacheDetails(workbook); }
        return columnCount;
    }

    @Override
    public int getRowCount(Workbook workbook, boolean skipHiddenCells) {
        if(skipHiddenCells) {
            if(hRowCount == null) {
                int startRow = getStartRowIndex(workbook), endRow = getEndRowIndex(workbook);
                int count = getRowCount(workbook, false);
                Sheet sheet = workbook.getSheetByAssociatedName(getAssociatedSheetName(workbook));

                for (int i = startRow; i <= endRow; i++) {count = RowUtil.isHiddenRow(sheet, i) ? count - 1 : count;}
                hRowCount = count;
            }
            return hRowCount;
        }
        if(rowCount == null) { cacheDetails(workbook); }
        return rowCount;
    }

    @Override
    public String getAssociatedSheetName(Workbook workbook) {
        if(associatedSheetName == null) { cacheDetails(workbook); }
        return associatedSheetName;
    }

    @Override
    public int getStartRowIndex(Workbook workbook) {
        if(startRow == null) { cacheDetails(workbook); }
        return startRow;
    }

    @Override
    public int getStartColumnIndex(Workbook workbook) {
        if(startColumn == null) { cacheDetails(workbook); }
        return startColumn;
    }

    @Override
    public int getEndRowIndex(Workbook workbook) {
        if (endRow == null) {cacheDetails(workbook); }
        return endRow;
    }

    @Override
    public int getEndColumnIndex(Workbook workbook) {
        if(endColumn == null) { cacheDetails(workbook);}
        return endColumn;
    }

    private void cacheDetails(Workbook workbook) {
        Range range = getRange(workbook);
        rowCount = range.getRowSize();
        columnCount = range.getColSize();
        associatedSheetName = range.getSheet().getAssociatedName();
        startRow = range.getStartRowIndex();
        startColumn = range.getStartColIndex();
        endRow = range.getEndRowIndex();
        endColumn = range.getEndColIndex();
        dataRange = range.toDataRange();
    }

    public void inValidateCache() {
        rowCount = null;
        columnCount = null;
        associatedSheetName = null;
        startRow = null;
        startColumn = null;
        endRow = null;
        endColumn = null;
        dataRange = null;
    }

    public DataRange getDataRange(Workbook workbook) {
        if(dataRange == null) { cacheDetails(workbook);}
        return dataRange;
    }


    public Range getRange(Workbook workbook) {
        try {
            switch (referenceType) {
                case RANGE_STRING: {
                    return new Range(workbook, referenceName, null, CellReference.ReferenceMode.A1, false);
                }
                case TABLE_NAME: {
                    Table table =  TableUtil.getTable(workbook, referenceName);
                    Objects.requireNonNull(table);

                    int sr = table.getStartRowIndex(), sc = table.getStartColIndex(), er = table.getEndRowIndex(), ec = table.getEndColIndex();
                    er = table.isFooterRowShown() ? er - 1 : er;

                    return ChartUtils.getDataRange(table.getSheet().getAssociatedName(), sr, sc, er, ec).toRange(workbook);
                }
                case NAMED_EXPRESSION: {
                    String prefixRemovedReference = ReferenceUtils.checkAndRemoveExpressionPrefix(referenceName);
                    NamedExpression expression = workbook.getNamedExpression(prefixRemovedReference);
                    if(expression.isRange()) { workbook.getNamedRanges().get(prefixRemovedReference); }
                    Object result = DataUtils.evaluateNode(workbook, expression.getNode());

                    if(result instanceof Range) { return (Range) result; }
                    break;
                }
                case EXPRESSION: {
                    String prefixRemovedReference = ReferenceUtils.checkAndRemoveExpressionPrefix(referenceName);
                    ExpressionImpl expression = new ExpressionImpl(workbook, prefixRemovedReference, 0, 0, true, CellReference.ReferenceMode.A1);
                    Object result = DataUtils.evaluateNode(workbook, expression.getNode());

                    if(result instanceof Range) { return (Range) result; }
                    break;
                }
            }
        } catch (Exception ignored) {

        }
        throw new InvalidReferenceException(referenceName);
    }


}
