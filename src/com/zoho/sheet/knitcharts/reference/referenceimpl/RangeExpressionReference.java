package com.zoho.sheet.knitcharts.reference.referenceimpl;

import com.adventnet.zoho.websheet.model.Expression;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Workbook;
import com.zoho.sheet.knitcharts.reference.ReferenceType;
import com.zoho.sheet.knitcharts.reference.iterater.EmptyReferenceDataIterator;
import com.zoho.sheet.knitcharts.reference.iterater.RangeReferenceDataIterator;
import com.zoho.sheet.knitcharts.reference.iterater.ReferenceDataIterator;

public class RangeExpressionReference extends ExpressionReference {

    private final RangeReference rangeReference;

    public RangeExpressionReference(String referenceName, Expression expression, ReferenceType referenceType) {
        super(referenceName, expression, referenceType);
        this.rangeReference = new RangeReference(referenceName, referenceType);
    }

    @Override
    public ReferenceDataIterator getReferenceDataIterator(Workbook workbook, boolean skipHiddenCells) {
        Object result = ExpressionReference.evaluate(workbook, getExpression());

        if(result instanceof Range) {
            return new RangeReferenceDataIterator((Range) result, skipHiddenCells, workbook.getSpreadsheetSettings());
        }
        return new EmptyReferenceDataIterator();
    }

    @Override
    public int getColumnCount(Workbook workbook, boolean skipHiddenCells) {
        return rangeReference.getColumnCount(workbook, skipHiddenCells);
    }

    @Override
    public int getRowCount(Workbook workbook, boolean skipHiddenCells) {
        return rangeReference.getRowCount(workbook, skipHiddenCells);
    }

    @Override
    public int getStartColumnIndex(Workbook workbook) {
        return rangeReference.getStartColumnIndex(workbook);
    }

    @Override
    public int getStartRowIndex(Workbook workbook) {
        return rangeReference.getStartRowIndex(workbook);
    }

    @Override
    public String getAssociatedSheetName(Workbook workbook) {
        return rangeReference.getAssociatedSheetName(workbook);
    }

    @Override
    public int getEndColumnIndex(Workbook workbook) {
        return rangeReference.getEndColumnIndex(workbook);
    }

    @Override
    public int getEndRowIndex(Workbook workbook) {
        return rangeReference.getEndRowIndex(workbook);
    }

    public RangeReference getAsRangeReference() {
        return rangeReference;
    }
}
