package com.zoho.sheet.knitcharts.reference;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.zoho.sheet.knitcharts.reference.data.ReferenceData;
import com.zoho.sheet.knitcharts.reference.iterater.ReferenceDataIterator;

public class ValueReference {

    private final Reference reference;

    public ValueReference(Reference reference) {
        this.reference = reference;
    }

    public Double getValue(Workbook workbook) {
        ReferenceDataIterator dataIterator = reference.getReferenceDataIterator(workbook, false);
        if(dataIterator.hasNext()){ return getValue(dataIterator.next()); }
        return 0d;
    }

    private Double getValue(ReferenceData data) {
        switch (data.getReferenceDataType()) {
            case CELL: {
                Cell cell = (Cell) data.getData();
                if(cell != null) { return getValue(cell.getValue()); }
            }
            case ARRAY_VALUE:
            case VALUE: {
                Value value = (Value) data.getData();
                return getValue(value);
            }
        }
        return 0d;
    }

    private Double getValue(Value value) {
        if(value != null) {
            try {
                return FunctionUtil.objectToNumber(value.getValue()).doubleValue();
            } catch (Exception ignored) {
                return 0d;
            }
        }
        return 0d;
    }

}
