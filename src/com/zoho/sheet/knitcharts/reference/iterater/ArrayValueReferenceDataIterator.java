package com.zoho.sheet.knitcharts.reference.iterater;

import com.adventnet.zoho.websheet.model.*;
import com.zoho.sheet.knitcharts.reference.data.ReferenceData;
import com.zoho.sheet.knitcharts.reference.data.ReferenceDataType;

public class ArrayValueReferenceDataIterator implements ReferenceDataIterator {

    private final ZArrayI arrayI;

    private final int totalSize;

    private int currentRow = 0, currentColumn = 0, retrievedCount = 0;

    private final SpreadsheetSettings settings;

    public ArrayValueReferenceDataIterator(ZArrayI arrayI, SpreadsheetSettings settings) {
        this.arrayI = arrayI;
        this.totalSize = arrayI.getSize();
        this.settings = settings;
    }

    @Override
    public boolean hasNext() {
        return retrievedCount < totalSize;
    }

    private void updateIndexes() {
        currentColumn++;
        retrievedCount++;

        if(currentColumn >= arrayI.getColSize()) {
            currentColumn = 0;
            currentRow++;
        }

    }

    @Override
    public ReferenceData next() {
        if(hasNext()) {
            Value value = getValidValue(arrayI.getValueWithPattern(currentRow, currentColumn));
            ReferenceData data = new ReferenceData(settings, ReferenceDataType.ARRAY_VALUE, value, currentRow, currentColumn);

            updateIndexes();
            return data;
        }

        return null;
    }

    private Value getValidValue(Value value) {
        if(value instanceof ValueI || value instanceof ValueWithPattern) { return value; }

        return Value.ZERO_VALUE;
    }
}
