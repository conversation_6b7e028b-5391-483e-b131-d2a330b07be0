/* $Id$ */
package com.zoho.sheet.sku.handler;

import com.zoho.ear.encryptagent.EncryptAgent;
import com.zoho.officeplatform.lib.license.api.LicenseUtils;
import com.zoho.officeplatform.lib.license.constants.LicenseAgentConstants;
import com.zoho.officeplatform.lib.license.constants.StoreConstants;
import com.zoho.officeplatform.lib.license.interfaces.LicenseHandler;
import com.zoho.officeplatform.lib.license.model.License;
import com.zoho.officeplatform.lib.db.Util;
import com.zoho.officeplatform.lib.license.model.Plan;

import com.zoho.officeplatform.lib.sku.interfaces.CreditPointsApiFactory;
import com.zoho.sheet.sku.ZSLicenseConstants;
import org.json.JSONObject;

import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.util.ZSCreditPointsUtil;

public class LicenseHandlerImpl extends LicenseHandler {

    private static final Logger LOGGER = Logger.getLogger(LicenseHandlerImpl.class.getName());

    @Override
    public JSONObject addUserToService(JSONObject jsonObject) {
        return null;
    }

    @Override
    public boolean onPlanChange(License license, boolean isSkuPolicyChangesNeeded) {
        boolean status = false;
        try {
            LOGGER.log(Level.INFO, "LicenseHandlerImpl :: onPlanChange called for zid - {0} : service name - {1} : isSkuPolicyChangesNeeded - {2}", new Object[] { license.getZid(), license.getServiceName(), isSkuPolicyChangesNeeded } ); //No I18N
            if (Util.isDataSpaceExist(license.getZid())) {
                status = CreditPointsApiFactory.getInstance().getSkuHandler().handlerPlanChange(license.toJSON(), isSkuPolicyChangesNeeded);
            }

        } catch (Exception e) {
            LOGGER.log(Level.INFO, "Exception :  ", e ); //No I18N
        }
        return status;
    }

    @Override
    public Plan getDefaultFreePlan(String zId, String serviceName) {
        Plan freePlan = new Plan(zId, serviceName);
        Boolean isWdFreeUser = true;

        try {
            isWdFreeUser = ZSCreditPointsUtil.isWDFreeUser(zId);
//             License license = LicenseUtils.getLicense(zId);
//             if(license != null) {
//                 freePlan.setSubscriptionStartTime(license.getSubscriptionStartTime());
//                 freePlan.setSubscriptionEndTime(license.getSubscriptionEndTime());
// //            } else if (!isWdFreeUser) {
// //                JSONObject wdPlanDetails = ZSCreditPointsUtil.getWDPlanDetails();
// //                if(wdPlanDetails.has("subscription_start_time")) freePlan.setSubscriptionStartTime(wdPlanDetails.getLong("subscription_start_time"));
// //                if(wdPlanDetails.has("subscription_end_time")) freePlan.setSubscriptionEndTime(wdPlanDetails.getLong("subscription_end_time"));
//             }
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "Exception occured while trying to get WD license status :: ", e);
        }

        if (isWdFreeUser) {
            JSONObject freePlanJson = LicenseAgentConstants.getFreePlan();
            freePlan.setPlanId(freePlanJson.optLong(StoreConstants.CLIENT_VARIABLE.PLAN_ID, -1));
            freePlan.setPlanName(freePlanJson.optString(StoreConstants.CLIENT_VARIABLE.PLAN_NAME, StoreConstants.CLIENT_VARIABLE.EMPTY_STRING));
        } else {
            freePlan.setPlanId(700100);
            freePlan.setPlanName("Exclusive Free Plan");      //No I18N
        }
        return freePlan;
    }

    @Override
    public License updateSubscriptionToFreePlan(License license) {
        Plan freePlan = getDefaultFreePlan(license.getZid(), license.getServiceName());
        license.setAddonMap(freePlan.isFreePlan() ? this.getDefaultFreeAddons(license.getZid(), license.getServiceName(), license) : this.getPaidFreeAddonsList(license.getZid(), license.getServiceName(), license));
        license.setPlanId(freePlan.getPlanId());
        license.setPlanName(freePlan.getPlanName());
        return license;
    }

    @Override
    public String getEncryptedCustomerId(String customerId) {
        try {
            if ( !"".equals(customerId)) { //No I18N
                customerId = EncryptAgent.getInstance().encryptWithCommonKey(LicenseAgentConstants.STORE_HANDLER_EAR_ENCRYPTION_KEY, LicenseAgentConstants.STORE_IAM_SERVICE_NAME, customerId, true);
            }
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Exception in encrypting zid for store handler. zid " + customerId, e); //No I18N
        }
        return customerId;
    }
}
