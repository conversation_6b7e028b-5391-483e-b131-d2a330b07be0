/* $Id$ */
package com.zoho.sheet.parse.impl;

import com.sun.star.sheet.XSpreadsheet;
import com.sun.star.sheet.XSpreadsheetDocument;
import com.sun.star.lang.IndexOutOfBoundsException;
import com.zoho.sheet.parse.CellValueProvider;
import com.adventnet.mfw.bean.BeanUtil;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * Author: tthiyagarajan
 * Date: Jun 27, 2007
 *
 * In this implementation, XSpreadsheetDocument object will be created only if it's needed.
 */
public class CellValueProviderImpl1 implements CellValueProvider
{

	public static Logger logger = Logger.getLogger(CellValueProviderImpl1.class.getName());
    private Long docId = null;
    private String docOwnerName = null;
    private String sheetName = null;
    private XSpreadsheet xSheet = null;
    boolean isInit = false;

    public CellValueProviderImpl1(Long docId, String docOwnerName, String sheetName)
    {
        this.docId = docId;
        this.docOwnerName = docOwnerName;
        this.sheetName = sheetName;
    }

    public String getText(int colIndex, int rowIndex)
    {
        if (!isInit)
        {
            xSheet = getXSheet();
            isInit = true;
        }
        if (xSheet == null)
        {
            return null;
        }
        try
        {
            return getCellString(xSheet, colIndex, rowIndex);
        }
        catch (Exception e)
        {
			logger.log(Level.WARNING,null,e);
            return null;
        }
    }

    private XSpreadsheet getXSheet()
    {
//        try
//        {
//            OpenDocBean ob = (OpenDocBean) BeanUtil.lookup("OpenDocBean", docOwnerName);
//            XSpreadsheetDocument xDocument = ob.open(null, docId.toString(), String.valueOf(
//                    SasUtil.getInstance().getAccountId(docOwnerName))
//            );
//			XSpreadsheet xsheet=Utility.XSpreadsheet(xDocument.getSheets().getByName(sheetName));
//			DocumentUtils.updateStockData(xsheet,docId,sheetName,docOwnerName);
//            return xsheet;
//        }
//        catch (Exception e)
//        {
//			logger.log(Level.WARNING,null,e);
//            return null;
//        }
		return null;
    }

    static String getCellString(XSpreadsheet xSheet, int colIndex, int rowIndex)
    {
//        try
//        {
//            return Utility.XTextRange(xSheet.getCellByPosition(colIndex, rowIndex)).getString();
//        }
//        catch (IndexOutOfBoundsException e)
//        {
//			logger.log(Level.WARNING,null,e);
//            return null;
//        }
		return null;
    }
	

}
