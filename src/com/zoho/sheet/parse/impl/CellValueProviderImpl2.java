/**
 * $Id$
 * Author: t<PERSON><PERSON><PERSON><PERSON>
 * Date: Jun 27, 2007
 */

package com.zoho.sheet.parse.impl;

import com.sun.star.sheet.XSpreadsheet;
import com.zoho.sheet.parse.CellValueProvider;

import java.util.logging.Logger;
import java.util.logging.Level;

public class CellValueProviderImpl2 implements CellValueProvider
{

	public static Logger logger = Logger.getLogger(CellValueProviderImpl2.class.getName());
    private XSpreadsheet xSheet = null;

    public CellValueProviderImpl2(XSpreadsheet xSheet)
    {
        this.xSheet = xSheet;
    }

    public String getText(int colIndex, int rowIndex)
    {
        if (xSheet == null)
        {
            return null;
        }
        try
        {
            return CellValueProviderImpl1.getCellString(xSheet, colIndex, rowIndex);
        }
        catch (Exception e)
        {
			logger.log(Level.WARNING,null,e);
            return null;
        }
    }
}
