/*$Id$*/
package com.zoho.sheet.parse.html;

import java.util.StringTokenizer;
import java.util.NoSuchElementException;
import java.util.logging.Logger;
import java.util.logging.Level;


/**
 * To store details of a Cell Border for Html Rendering
 */
public class HtmlBorder
{
	public static Logger logger = Logger.getLogger(HtmlBorder.class.getName());
    final String top;
    final String bottom;
    final String left;
    final String right;

    static class Default
    {
        static final String LEFT = "0px solid #FFF";//No I18N
        static final String TOP = "0px solid #FFF";//No I18N
        static final String RIGHT = "1px solid #E4E3C9";//No I18N
        static final String BOTTOM = "1px solid #E4E3C9";//No I18N
    }

    public static HtmlBorder getInstance(String border, String left, String top, String right, String bottom)
    {
        if(border == null)
        {
        	//Assumption Of If left is null, all other is null is breaked in case of .ods
        	//if(left == null)
            if(left == null && top == null && right == null && bottom == null)
            {
                return null;
            }
            return new HtmlBorder(left,top,right,bottom);
        }
        else
        {
            return new HtmlBorder(border);
        }
    }

    //Have TO Use This Method Only For Sxc, and we should use engine utils for OO
    private String convert(String value,String fallbackValue)
    {
        if(value == null || value.contains("none"))
        {
            return null;
        }
        String inInches;
        float thickness;
        try
        {
            inInches = new StringTokenizer(value, " ").nextToken();
            //thickness = Float.parseFloat(inInches.replaceFirst("inch", ""));
            thickness = HtmlUtil.getInchValue(inInches);
        }
        catch(NoSuchElementException noexp)
        {
			logger.log(Level.WARNING,null,noexp);
            return fallbackValue;
        }
        catch(NumberFormatException nExp)
        {
			logger.log(Level.WARNING,null,nExp);
            return fallbackValue;
        }


        // converting inches to pixel with the assumption 93 PPI
        if (thickness < 0.021F)
        {
            //return "1px solid #000";
            return value.replaceFirst(inInches, "1px");//No I18N
        }
        else if (thickness < 0.032F)
        {
            return value.replaceFirst(inInches, "2px");//No I18N
        } else if(thickness < 0.038F) {
        	return value.replaceFirst(inInches, "2.5px");//NO I18N
        } else if (thickness < 0.043F)
        {

            //return "3px solid #000";
            return value.replaceFirst(inInches, "3px");//No I18N
        }
        //return "4px solid #000";
        return value.replaceFirst(inInches, "4px");//No I18N
    }

    private HtmlBorder(String left, String top, String right, String bottom)
    {
        this.left = convert(left,Default.LEFT);
        this.top = convert(top,Default.TOP);
        this.right = convert(right,Default.RIGHT);
        this.bottom = convert(bottom,Default.BOTTOM);
    }

    private HtmlBorder(String allBorder)
    {
        this.left = this.top = this.right = this.bottom = convert(allBorder,"1px solid #000000");//No I18N
    }

}
