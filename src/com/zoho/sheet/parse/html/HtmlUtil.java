/** $Id$ */
package com.zoho.sheet.parse.html;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.SpreadsheetSettings;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EngineUtils;
import com.zoho.sheet.parse.sxc.Struct;

import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.Date;
import java.util.Locale;
import java.util.HashMap;

import org.apache.commons.collections4.map.MultiKeyMap;

public class HtmlUtil
{
	public static void escSpecialChars(Struct.Cell cell)
	{
		if(cell.formula != null)
		{
			cell.formula = EngineUtils.forHTMLTag(cell.formula);
		}
		if(cell.text != null)
		{
			cell.text = EngineUtils.forHTMLTag(cell.text);
		}
		if("string".equals(cell.valueType.intern()) && cell.value != null)
		{
			cell.value = EngineUtils.forHTMLTag(cell.value);
		}
	}

	private static SimpleDateFormat timeFormat = new SimpleDateFormat("'PT'HH'H'mm'M'ss'S'");
	private static SimpleDateFormat tTimeFormat = new SimpleDateFormat("hh:mm:ss a");
	private static SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
	private static SimpleDateFormat tDateTimeFormat = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
	private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
	private static SimpleDateFormat tDateFormat = new SimpleDateFormat("MM/dd/yyyy");
	private static DecimalFormat decimalFormat = new DecimalFormat("0.00%");

	public static String getFormulaText(String formula, String text, String dataType, String value) throws ParseException
	{
		if (formula != null) // formula cell
		{
			return formula;
		}
		return getFormulaLocal(text,dataType,value);
	}

	public static String getFormulaLocal(String text, String dataType, String value) throws ParseException
	{
		if (value == null)
		{
			if ("string".equals(dataType))
			{
				try {
					int nfe = Integer.parseInt(text);
					return "'" + text;
				} catch(NumberFormatException e) {
					return text;
				}
			}
			return null;
		}
		// now value is not null
		if ("string".equals(dataType))
		{
			return value;
		}
		else if ("float".equals(dataType) || "currency".equals(dataType))
		{
			return value;
		}
		else if ("percentage".equals(dataType))
		{
			return decimalFormat.format(Double.parseDouble(value));
		}
		else if ("boolean".equals(dataType))
		{
			return value.toUpperCase();
		}
		else if ("time".equals(dataType))
		{
			return handleTimeType(value);
		}
		else if ("date".equals(dataType))
		{
			return handleDateType(value);
		}
		return null;
	}

	/// For Ods Parser
	public static String getFormulaText(String formula, String text, Cell.Type type, Object value) throws ParseException
	{
		if (formula != null) // formula cell
		{
			return formula;
		}
		return getFormulaLocal(text,type,value);
	}

	/// For Ods Parser
	public static String getFormulaLocal(String text, Cell.Type type, Object value) throws ParseException
	{
		if (value == null)
		{
			if (type == Cell.Type.STRING)
			{
				try {
					int nfe = Integer.parseInt(text);
					return "'" + text;
				} catch(NumberFormatException e) {
					return text;
				}
			}
			return null;
		}

		String str = null;
		switch(type) {
			case FLOAT:
			case SCIENTIFIC:
			case FRACTION:
			case CURRENCY:
			case PERCENTAGE:
				{
					NumberFormat f;
					Number temp = (Number)value;
				    if(temp.doubleValue() > 99999999999999999d || temp.doubleValue() < -99999999999999999d)
				    {
				        f = new DecimalFormat("0.0#############E00");//No I18N
				    }
				    else
				    {
				    	Locale locale = Locale.getDefault();
				        if(type.equals(Type.PERCENTAGE))
				        {
				            /*if(this.locale != null)
				            {
				                return locale;
				            }
				            else
				            {*/
				                //return this.getRow().getLocale();
				            	//return Locale.getDefault();
				            //}
				            f = NumberFormat.getPercentInstance(locale);
				        }
				        else
				        {
				            f = NumberFormat.getNumberInstance(locale);
				        }

				        f.setGroupingUsed(false);
				        f.setMinimumFractionDigits(0);
				        f.setMaximumFractionDigits(10);
				    }
				    str = f.format(temp);
				}
				break;
			case DATE:
				{
				    SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
				    str = sdf.format(value);
				}
				break;
			case DATETIME:
				{
					SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy hh:mm a");
					str = sdf.format(value);
				}
				break;
			case TIME:
				{
				    SimpleDateFormat sdf;
				    Number temp = DateUtil.convertDateToNumber((Date)value);
				    if(temp.doubleValue() < 1)
				    {
				        sdf = new SimpleDateFormat("hh:mm:ss a");
				    }
				    else
				    {
				        sdf = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss a");
				    }
				    str = sdf.format(value);
				}
				break;
			case STRING:
				{
				    str = (String)value;
				    /*Pattern patt = this.getPattern();
				    if(patt != null && patt.getType().equals(Type.STRING))
				    {
				        return str;
				    }*/

				    Locale locale = EngineConstants.DEFAULT_LOCALE;
				    // If the string is  a number date or time add prefix a ' and show in formula bar.
				    Value temp = Value.getInstance(str, SpreadsheetSettings.defaultSpreadsheetSettings);
				    if(temp.getType() != Type.STRING)
				    {
				    	str = "'"+str;
				    }
				}
			case ERROR:
				str = (String)value;
				//str = ((Throwable)value).getMessage();
				break;
			case UNDEFINED:
				break;
			case BOOLEAN:
				str = (String.valueOf(value)).toUpperCase();
				break;
			default:
				str = value.toString();
		}
		return (String)str;
	}

	/** private methods -> START */
	private static String handleDateType(String value) throws ParseException
	{
		if (value.indexOf('T') != -1) // DateTime type
		{
			Date tempDate = dateTimeFormat.parse(value);
			return tDateTimeFormat.format(tempDate);
		}
		else // Date type
		{
			Date tempDate = dateFormat.parse(value);
			return tDateFormat.format(tempDate);
		}
	}

	private static String handleTimeType(String value) throws ParseException
	{
		try
		{
			Date tempDate = timeFormat.parse(value);
			return tTimeFormat.format(tempDate);
		}catch(ParseException pe)
		{
			return null;
		}
	}

	/* used to change the other unit values to inch*/
	public static float getInchValue(String value)
	{
		return (float)EngineUtils.getInchValue(value);
	}
	/** private methods -> END */
	public static void setValue(Object object,int row,String value) {
		if(object instanceof HashMap){
			((HashMap)object).put(row,value);
		} else {
			(((String[])object)[row]) = value;
		}
	}

	public static void setValue(Object object,int row,int col,String value) {
		if(object != null) {
			if(object instanceof MultiKeyMap){
				((MultiKeyMap)object).put(row,col,value);
			} else {
				(((String[][])object)[row][col]) = value;
			}
		}
	}

	public static Long getValue(Object object,int row) {
		if(object instanceof HashMap){
			return (Long)(((HashMap)object).get(row));
		} else {
			return Long.valueOf((String)(((String[])object)[row]));
		}
	}

	public static String getValue(Object object,int row,int col) {
		String value = "";//No internationalization
		if(object != null) {
			if(object instanceof MultiKeyMap){
				value = (String)(((MultiKeyMap)object).get(row,col));
			} else {
				value = (String)(((String[][])object)[row][col]);
			}
		}
		return value;
	}
}
