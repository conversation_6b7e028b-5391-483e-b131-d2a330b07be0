/* $Id$ */
package com.zoho.sheet.parse.sxc;

import com.zoho.sheet.parse.XmlName;

/** some constants representing the nodes/attributes in content.xml of .sxc file */
public interface Names
{
    /** Namespaces */
    String OFFICE = "http://openoffice.org/2000/office";//No I18N
    String STYLE = "http://openoffice.org/2000/style";//No I18N
    String FO = "http://www.w3.org/1999/XSL/Format";//No I18N
    String TABLE = "http://openoffice.org/2000/table";//No I18N
    String TEXT = "http://openoffice.org/2000/text";//No I18N
    String XLINK = "http://www.w3.org/1999/xlink";//No I18N

    /** prefixes */
    String pOffice = "office";//No I18N
    String pStyle = "style";//No I18N
    String pFO = "fo";//No I18N
    String pTable = "table";//No I18N
    String pText = "text";//No I18N
    String pXlink = "xlink";//No I18N


    /** Node Names */
    XmlName nOfficeDoc = new XmlName(OFFICE, pOffice, "document-content");//No I18N

    XmlName nFontDecls = new XmlName(OFFICE, pOffice, "font-decls");//No I18N
    XmlName nFontDecl = new XmlName(STYLE, pStyle, "font-decl");//No I18N

    XmlName nAutomaticStyles = new XmlName(OFFICE, pOffice, "automatic-styles");//No I18N
    XmlName nStyle = new XmlName(STYLE, pStyle, "style");//No I18N
    XmlName nStyleProps = new XmlName(STYLE, pStyle, "properties");//No I18N

    XmlName nBody = new XmlName(OFFICE, pOffice, "body");//No I18N
    XmlName nTable = new XmlName(TABLE, pTable, "table");//No I18N

    XmlName nTableHeaderColumns = new XmlName(TABLE, pTable, "table-header-columns");//No I18N
    XmlName nTableColumns = new XmlName(TABLE, pTable, "table-columns");//No I18N
    XmlName nTableColumnGroup = new XmlName(TABLE, pTable, "table-column-group");//No I18N
    XmlName nTableColumn = new XmlName(TABLE, pTable, "table-column");//No I18N

    XmlName nTableHeaderRows = new XmlName(TABLE, pTable, "table-header-rows");//No I18N
    XmlName nTableRows = new XmlName(TABLE, pTable, "table-rows");//No I18N
    XmlName nTableRowGroup = new XmlName(TABLE, pTable, "table-row-group");//No I18N
    XmlName nTableRow = new XmlName(TABLE, pTable, "table-row");//No I18N

    XmlName nCoveredTableCell = new XmlName(TABLE, pTable, "covered-table-cell");//No I18N
    XmlName nTableCell = new XmlName(TABLE, pTable, "table-cell");//No I18N
    XmlName nAnnotation = new XmlName(OFFICE, pOffice, "annotation");//No I18N

    XmlName nTextP = new XmlName(TEXT, pText, "p");//No I18N
    XmlName nTextA = new XmlName(TEXT, pText, "a");//No I18N

    XmlName NNAMEDEXPRESSIONS = new XmlName(TABLE, pTable, "named-expressions");//No I18N
    XmlName NNAMEDRANGE = new XmlName(TABLE, pTable, "named-range");//No I18N
    XmlName NNAMEDEXPR = new XmlName(TABLE, pTable, "named-expression");//No I18N

    /** Attribute Names */
    XmlName aStyleName = new XmlName(STYLE, pStyle, "name");//No I18N
    XmlName aStyleFamily = new XmlName(STYLE, pStyle, "family");//No I18N

    XmlName aFontFamily = new XmlName(FO, pFO, "font-family");//No I18N
    XmlName aFontBGColor = new XmlName(FO, pFO, "background-color");//No I18N
    XmlName aFontColor = new XmlName(FO, pFO, "color");//No I18N
    XmlName aFontName = new XmlName(STYLE, pStyle, "font-name");//No I18N
    XmlName aFontSize = new XmlName(FO, pFO, "font-size");//No I18N
    XmlName aFontStyle = new XmlName(FO, pFO, "font-style");//No I18N
    XmlName aFontWeight = new XmlName(FO, pFO, "font-weight");//No I18N
    XmlName aHAlign = new XmlName(FO, pFO, "text-align");//No I18N
    XmlName aVAlign = new XmlName(FO, pFO, "vertical-align");//No I18N
    XmlName aTextUnderline = new XmlName(STYLE, pStyle, "text-underline");//No I18N
    XmlName aWrap = new XmlName(FO, pFO, "wrap-option");//No I18N
    XmlName aBorder = new XmlName(FO,pFO, "border");//No I18N
    XmlName aBorderTop = new XmlName(FO,pFO,"border-top");//No I18N
    XmlName aBorderBottom = new XmlName(FO,pFO,"border-bottom");//No I18N
    XmlName aBorderLeft = new XmlName(FO,pFO,"border-left");//No I18N
    XmlName aBorderRight = new XmlName(FO,pFO,"border-right");//No I18N
    XmlName AOPTROWHEIGHT = new XmlName(STYLE,pStyle, "use-optimal-row-height");//No I18N
    XmlName aRowHeight = new XmlName(STYLE, pStyle, "row-height");//No I18N
    XmlName aColumnWidth = new XmlName(STYLE, pStyle, "column-width");//No I18N


    XmlName A_TABLE_DISPLAY = new XmlName(TABLE, pTable, "display"); //No I18N

    XmlName aTableName = new XmlName(TABLE, pTable, "name");//No I18N

    XmlName aTableStyleName = new XmlName(TABLE, pTable, "style-name");//No I18N
    XmlName aTableValueType = new XmlName(TABLE, pTable, "value-type");//No I18N


    XmlName aTableFormula = new XmlName(TABLE, pTable, "formula");//No I18N
    XmlName aTableValue = new XmlName(TABLE, pTable, "value");//No I18N
    XmlName aTableBoolVal = new XmlName(TABLE, pTable, "boolean-value");//No I18N
    XmlName aTableStringVal = new XmlName(TABLE, pTable, "string-value");//No I18N
    XmlName aTableDateVal = new XmlName(TABLE, pTable, "date-value");//No I18N
    XmlName aTableTimeVal = new XmlName(TABLE, pTable, "time-value");//No I18N

    XmlName aNoColsRepeated = new XmlName(TABLE, pTable, "number-columns-repeated");//No I18N
    XmlName aNoColsSpanned = new XmlName(TABLE, pTable, "number-columns-spanned");//No I18N
    XmlName aNoRowsRepeated = new XmlName(TABLE, pTable, "number-rows-repeated");//No I18N
    XmlName aNoRowsSpanned = new XmlName(TABLE, pTable, "number-rows-spanned");//No I18N

    XmlName aDefCellStyleName = new XmlName(TABLE, pTable, "default-cell-style-name");//No I18N

    XmlName aHref = new XmlName(XLINK,pXlink,"href");//No I18N

    XmlName aTableCurrency = new XmlName(TABLE, pTable, "currency");//No I18N

	XmlName ANAME = new XmlName(TABLE, pTable, "name");//No I18N
	XmlName ACELLRANGEADDRESS = new XmlName(TABLE, pTable, "cell-range-address");//No I18N
	XmlName AEXPR = new XmlName(TABLE, pTable, "expression");//No I18N

}
