/* $Id$ */
package com.zoho.sheet.parse.sxc;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.filter.Filter;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.shapes.Equation;
import com.adventnet.zoho.websheet.model.shapes.Handle;
import com.adventnet.zoho.websheet.model.shapes.ShapeAnchor;
import com.adventnet.zoho.websheet.model.shapes.ShapeGroup;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSFontScheme;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.zsparser.ZSEventListener;

import java.util.BitSet;
import java.util.List;

public class ZSToSxcAdapter implements ZSEventListener {
    private SxcEventListener listener = null;
    protected Struct.Sheet sxcSheet = Struct.factory.getSheet();
    private int sheetIndex = 0;

    public ZSToSxcAdapter(SxcEventListener eventListener) {
        this.listener = eventListener;
    }

    @Override
    public void updateStylesVersionId(int stylesVID){

    }

    @Override
    public void updateFontFace(FontFace fontFace) {

    }

    @Override
    public void updateColumnStyle(ColumnStyle columnStyle) {

    }

    @Override
    public void updateRowStyle(RowStyle rowStyle) {

    }

    @Override
    public void updateCellStyle(CellStyle cellStyle) {

    }

    @Override
    public void startSheet(Sheet sheet) {
        sxcSheet.sheetName = sheet.getName();
        sxcSheet.index = ++sheetIndex;

        listener.startSheet(sxcSheet);
    }

    @Override
    public void endSheet(Sheet sheet) {
        listener.endSheet(sxcSheet);
        sxcSheet.reset();
    }

    public void updateNonDynamicArraySheet(Sheet sheet){
    }

    @Override
    public void updateColumnHeader(ColumnHeader columnHeader, ColumnVisibility columnvisibility) {

    }

    @Override
    public void startRow(Row row) {
        sxcSheet.row.styleName = row.getStyleName();
        sxcSheet.row.cellStyleName = row.getDefaultCellStyleName();
        sxcSheet.row.rep = row.getRowsRepeated();
        listener.startRow(sxcSheet);
    }

    @Override
    public void endRow(Row row) {
        listener.endRow(sxcSheet);
        sxcSheet.row.reset();
    }

    @Override
    public void updateDummyCellRepeated(int colRepeated) {
        sxcSheet.row.vCell.rep = colRepeated;
        listener.updateDummyCell(sxcSheet);
        sxcSheet.row.vCell.reset();
    }

    @Override
    public void updateCell(Cell cell, String picklistAndItemIndex, String picklistAndItemID, String picklistSourceID, boolean isArrayCell, List<Integer> picklistItemIDs) {
        sxcSheet.row.cell.styleName = cell.getStyleName();
        sxcSheet.row.cell.rep = cell.getColsRepeated();
        //int[] spans = cell.getRow().getSheet().getMergeCellSpans(cell);
        sxcSheet.row.cell.colSpan = 1;//spans[1];
        sxcSheet.row.cell.rowSpan = 1;//spans[0];
        Value value = cell.getValue();
        Cell.Type type = null;
        if (value != null) {
            type = value.getType();
        }

        if(type == null) {
            sxcSheet.row.cell.valueType =  "STANDARD";//No I18N
        }
        else if(type == Cell.Type.DATETIME)
        {
            sxcSheet.row.cell.valueType = "date";//No I18N
        }
        else if(type == Cell.Type.FRACTION || type == Cell.Type.SCIENTIFIC)
        {
            sxcSheet.row.cell.valueType = "float";//No I18N
        }
        else
        {
            sxcSheet.row.cell.valueType = type.toString().toLowerCase();
        }

        if("percentage".equals(sxcSheet.row.cell.valueType))
        {
            sxcSheet.row.cell.numberFormat = "PERCENT";//No I18N
        }
        else if("date".equals(sxcSheet.row.cell.valueType))
        {
            sxcSheet.row.cell.numberFormat = "DATE";//No I18N
        }
        else if("time".equals(sxcSheet.row.cell.valueType))
        {
            sxcSheet.row.cell.numberFormat = "TIME";//No I18N
        }
        else if("boolean".equals(sxcSheet.row.cell.valueType))
        {
            sxcSheet.row.cell.numberFormat = "LOGICAL";//No I18N
        }
        else if("currency".equals(sxcSheet.row.cell.valueType))
        {
            sxcSheet.row.cell.numberFormat = "CURRENCY";//No I18N
        }
        else
        {
            sxcSheet.row.cell.numberFormat = "STANDARD";//No I18N
        }

        sxcSheet.row.cell.formula = cell.isFormula()?cell.getFormula():null;
        sxcSheet.row.cell.value = value != null ? value.getValue()!=null? value.getValue().toString():null:null;
        sxcSheet.row.cell.text = Utility.masknull(((CellImpl)cell).getContentFromWriter(), sxcSheet.row.cell.value);
        sxcSheet.row.cell.comment = (cell.getAnnotation() != null)?cell.getAnnotation().getContent():null;
        if(cell.getLink() != null)
        {
            sxcSheet.row.cell.addLink(cell.getLink(), sxcSheet.row.cell.text);
        }
        listener.updateCell(sxcSheet);
        sxcSheet.row.cell.reset();
    }

    @Override
    public void updateMergeCells(Cell cell, int rowSpan, int colSpan) {

    }

    @Override
    public void updateCellLink(Cell cell, String link) {

    }

    @Override
    public void constructWorkbook(String workbookName) {

    }

    @Override
    public void endWorkbook() {

    }

    @Override
    public void updateSheetStyle(SheetStyle sheetStyle) {

    }

    @Override
    public void updateForms(Forms forms) {

    }

    @Override
    public void updateGraphicStyle(GraphicStyle graphicStyle) {

    }

    @Override
    public void updateParagraphStyle(ParagraphStyle paragraphStyle) {

    }

    @Override
    public void updateTextStyle(TextStyle textStyle) {

    }

    @Override
    public ZSPattern updatePattern(String patternString, boolean isAccountingPattern, boolean isAutoOrder) {
        return null;
    }

    @Override
    public void updateDefaultDatePattern(String patternStr)
    {

    }

    @Override
    public void updateMapStyle(String condition, String baseCellAddress, String applyStyleName, Object style) {

    }

    @Override
    public void updateNamedExpression(String name, String scopeASN, String comment, String expression, String baseCellAddress, boolean isNamedRange) {

    }

    @Override
    public void updatePivotTable(PivotTable pivotTable) {

    }

    @Override
    public void startTableStylesNode() {

    }

    @Override
    public void endTableStylesNode() {

    }

    @Override
    public void startTableStyleNode(String tableStyleName, boolean isCustom) {

    }

    @Override
    public void endTableStyleNode() {

    }

    @Override
    public void endDefaultTableStyleNode() {

    }

    @Override
    public void updateCellStyleForTableStyles(CellStyle cellStyle) {

    }

    @Override
    public void updateTableStyleProperty(String propertyKey, String stripeSizeStr, String cellStyleName) {

    }

    @Override
    public void updateProtectedRange(String cellRangeAddress, String authUsersStr, String unAuthUsersStr, String authGroupsStr, String authOrgsStr, String isPubAuthorized, boolean isAllowInsert, boolean isAllowFormats, String authExternalShareLinksStr, String unAuthExternalShareLinksStr) {

    }

    @Override
    public void updateDefaultFilterView(String rangeAddress, Filter filters)
    {

    }

    @Override
    public void updateNamedFilterView(String name, String targetRangeAddress, Filter filter)
    {

    }

    @Override
    public void updateEmptyFilterView(int id, String zuid, String sheetAsn) {

    }

    @Override
    public void updateFilterView(int id, int tableID, String rangeAddress, Filter filter, BitSet filteredRowsSet, boolean displayFilterButtons, List<String> zuids) {

    }

    @Override
    public void updateContentValidation(String condition, String baseCellAddress, String validationName, boolean isAllowEmptyCell, String displayList, DVHelpMessage helpMessage, DVErrorMessage errorMessage) {

    }

    @Override
    public void updatePicklist(String picklistID, String startsWith, String startsWithID, String rangeStrings, String sourceRangeString, String isRangePicklist, boolean showAsBubble, String autoStyleID, boolean allowMultiSelect, Picklist.DropdownStyle dropdownIcon, Picklist.StackDirection stackDirection) {

    }

    @Override
    public void updateRangePicklist(String picklistID, String rangeStrings, String sourceRangeStrings, String sort, String autoStyleID, boolean showAsBubble, boolean allowMultiSelect, Picklist.DropdownStyle dropdownIcon, Picklist.StackDirection stackDirection) {}

    @Override
    public void endPicklists() {

    }

    @Override
    public Picklist getPicklist(int picklistID) {
        return null;
    }

    @Override
    public void updateListItem(String itemID, String displayString, String valueString, String color, String colorTheme, String colorTint, String bgColor, String bgTheme, String bgTint, String valueID) {

    }

    @Override
    public void createPicklistAutoColorsList() {

    }

    @Override
    public void addAutoColor(String textColor, String textColorTheme, String textColorTint, String bgColor, String bgTheme, String bgTint) {

    }

    @Override
    public void endPicklistAutoColorNode(int id) {

    }

    @Override
    public void updateFormRange(String cellRangeAddress) {

    }

    @Override
    public void updateCheckboxRange(String cellRangeAddress) {

    }

    @Override
    public void updateSparkline(String source, String destination, String orientation, String minValue, String maxValue) {

    }

    @Override
    public void updateSparklinesGroup() {

    }

    @Override
    public void updateSparklineProperties(String type, String sparklineColor, String sparklineColorTint, String sparklineColorTheme, boolean isMarkerRequired, String markerColor, String markerColorTint, String markerColorTheme, String negativeColor, String negativeTint, String negativeTheme, String highPointColor, String highPointTint, String highPointTheme, String lowPointColor, String lowPointTint, String lowPointTheme, String firstPointColor, String firstPointTint, String firstPointTheme, String lastPointColor, String lastPointTint, String lastPointTheme, String minType, String maxType, boolean isXAxisRequired, String hiddenCells, String emptyCells, boolean isReversed) {

    }

    @Override
    public void startConditionalFormats() {

    }

    @Override
    public void startConditionalFormat(String targetRangeAddress, String priority) throws SheetEngineException {

    }

    @Override
    public void updateCondition(String applyStyleName, String condition, String baseCellAddress, int applyColIndex) {

    }

    @Override
    public void updateColorScale(boolean isAutoColor, boolean isHideText) {

    }

    @Override
    public void updateColorScaleEntry(ConditionalFormatEntry.Entry_Type type, String value, ZSColor color, String baseCellAddress) {

    }

    @Override
    public void updateIconSet(String iconSetName, boolean iconDefaultSize, boolean iconReverseOrder, boolean isAutoColor, boolean isHideText) {

    }

    @Override
    public void updateIconSetEntry(ConditionalFormatEntry.Entry_Type cstype, String aValue, String aIconName, Integer aIconID, String iconCriteria, String baseCellAddress) {

    }

    @Override
    public void updateDataBar(DataBar.Direction dbDirection, DataBar.Type fillType, ZSColor fillPColor, ZSColor fillNColor, DataBar.Type borderType, ZSColor borderPColor, ZSColor borderNColor, DataBar.Axis axisPosition, ZSColor axisColor, boolean isAutoColor, boolean isHideText) {

    }

    @Override
    public void updateDataBarEntry(ConditionalFormatEntry.Entry_Type cstype, String aValue, String baseCellAddress) {

    }

    @Override
    public void updateCommentRange(String name, String changeFormula) {

    }

    @Override
    public void updateNotificationRange(String name, JSONObjectWrapper notifyJson) {

    }

    @Override
    public void putCellExpressionNameEntry(String formulaName, Cell cell) {

    }

    @Override
    public void putExpressionNameExpressionEntry(String formulaName, String expressionString) {

    }

    @Override
    public List<Cell> createAndSetExpressionForReferredCells() {
        return null;
    }

    @Override
    public void updateFrameList(Frame frame) {

    }

    @Override
    public void publishDefaultColumnWidth(String defaultColumnWidthStr) {

    }

    @Override
    public void setRowVisibility(Row row, RowVisibility visibility) {

    }

    @Override
    public void updatePlaceholder(String name, String baseCellAddress, String rangeAddress, String desc) {

    }

    @Override
    public void updateThemeProperties(ZSColorScheme colorScheme, ZSFontScheme fontScheme) {

    }

    @Override
    public void addSheetImage(int id, int imageID, double height, double width, int rowIndex, int columnIndex, double rowDiff, double columnDiff)
    {

    }

    @Override
    public void updateField(Field field) {
        
    }
    
    public void updateEnhancedGeometry(String viewBox, String textArea, String type, String modifiers, String enhancedPath, String gluePoints, String pathStretchPointX, String pathStretchPointY, String mirrorHorizontal, String mirrorVertical) {

    }

    @Override
    public Field getField(int id) {
        return null;
    }
    
    public void updateEquation(Equation equation) {

    }

    @Override
    public void updateHandle(Handle handle) {

    }

    @Override
    public void updateTextInCustomShape(RichStringProperties linkObj) {

    }

    @Override
    public void updateCustomShapeParagraph(String paragraphStyle) {

    }

    @Override
    public void updatePolyLine(String id, String zIndex, String styleName, String textStyleName, String width, String height, String viewBox, String transform, String points) {

    }

    @Override
    public void updatePath(String id, String zIndex, String styleName, String textStyleName, String width, String height, String viewBox, String transform, String d) {

    }

    @Override
    public void updateLine(String id, String zIndex, String styleName, String textStyleName, String x1, String x2, String y1, String y2) {

    }

    @Override
    public void updateEllipse(String id, String zIndex, String styleName, String textStyleName, String height, String width, String svgX, String svgY, String startAngle, String endAngle, String kind) {

    }

    @Override
    public void updateCustomShape(String id, String endCellAddress, String endX, String endY, String zIndex, String name, String styleName, String textStyleName, String width, String height, String svgX, String svgY) {

    }

    @Override
    public void updateAnchor(ShapeAnchor anchor) {

    }

    @Override
    public void updateGroup(ShapeGroup group) {

    }

    @Override
    public void addShapeGroupToSheet() {

    }

    @Override
    public void updateDataConnection(String name, String baseCellAddress, String rangeAddress) {

    }

    @Override
    public void updateWorkbookSettings(WorkbookSettings workbookSettings) {

    }
}
