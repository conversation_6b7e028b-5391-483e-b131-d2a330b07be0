/* $Id$ */
package com.zoho.sheet.parse.tsv;

import com.adventnet.zoho.websheet.model.parser.ODSRangeParser;
import java.io.*;
import org.xmlpull.v1.XmlPullParserException;
import java.util.logging.Logger;
//DFS 
import com.adventnet.zoho.websheet.store.Store;
import com.zoho.sheet.parse.RangeAddress;
import com.zoho.sheet.parse.sxc.OdsToSxcAdapter;
import com.zoho.sheet.parse.sxc.SxcRangeParser;

public class TsvRangeTransformer extends TsvSheetTransformer
{
	public static Logger logger = Logger.getLogger(TsvRangeTransformer.class.getName());
    ////    testing code -> START   ////
    public static class Testing
    {
        // usage : args[0] - filepath ;  args[1] - rangeAddr
        public static void main(String[] args) throws Exception
        {
            TsvRangeTransformer tx = new TsvRangeTransformer("2005", RangeAddress.forLabel(args[1]));
            long t1 = System.currentTimeMillis();
            StringBuffer result = tx.transfrom(getFileStream(args[0]));
            logger.info("Time taken(ms) : " + (System.currentTimeMillis() - t1));
            // writing the response to a file
            PrintWriter myout = new PrintWriter(new File("response.tsv"));// output file
            myout.print(result.toString());
            myout.flush();
            myout.close();
        }

        private static InputStream getFileStream(String path) throws FileNotFoundException
        {
            return new BufferedInputStream(
                    new FileInputStream(path)
            );
        }
    }
    ////    testing code -> END    ////

    private RangeAddress rangeAddr = null;

    public TsvRangeTransformer(String sheetName, RangeAddress rangeAddr)
    {
        super(sheetName);
        this.rangeAddr = rangeAddr;
    }

    public TsvRangeTransformer(int sheetIndex, RangeAddress rangeAddr)
    {
        super(sheetIndex);
        this.rangeAddr = rangeAddr;
    }

    public StringBuffer transfrom(InputStream iStream) throws IOException, XmlPullParserException
    {
        SxcRangeParser parser = getRangeParser();
        parser.parse(iStream);
        return generateOutput();
    }

    public StringBuffer transfromODS(InputStream iStream) throws Exception
    {
		if(sheetName == null)
		{
			ODSRangeParser parser = new ODSRangeParser(new OdsToSxcAdapter(this),sheetIndex,rangeAddr.startRow,rangeAddr.startCol,rangeAddr.endRow,rangeAddr.endCol,rangeAddr.noOfRows,rangeAddr.noOfCols,true); //*** true or false based on need
			parser.parse(iStream, null);
		}
		else
		{
			ODSRangeParser parser = new ODSRangeParser(new OdsToSxcAdapter(this),sheetName,rangeAddr.startRow,rangeAddr.startCol,rangeAddr.endRow,rangeAddr.endCol,rangeAddr.noOfRows,rangeAddr.noOfCols,true); //*** true or false based on need
			parser.parse(iStream, null);
		}
		return generateOutput();
    }


   /*
    *OLD FileStore 
    *  public StringBuffer transfromFragmentedFile(FileStore store,Long bookId) throws Exception
    {
		if(sheetName == null)
		{
			ODSRangeParser parser = new ODSRangeParser(new OdsToSxcAdapter(this),sheetIndex,rangeAddr.startRow,rangeAddr.startCol,rangeAddr.endRow,rangeAddr.endCol,rangeAddr.noOfRows,rangeAddr.noOfCols,true); //*** true or false based on need
			parser.parse(store, EngineConstants.LOCATION + bookId, null, null, true);
		}
		else
		{
			ODSRangeParser parser = new ODSRangeParser(new OdsToSxcAdapter(this),sheetName,rangeAddr.startRow,rangeAddr.startCol,rangeAddr.endRow,rangeAddr.endCol,rangeAddr.noOfRows,rangeAddr.noOfCols,true); //*** true or false based on need
			parser.parse(store, EngineConstants.LOCATION + bookId, sheetName, null, true);
		}
		return generateOutput();
    }*/
    
    public StringBuffer transfromFragmentedFile(Store store, String docOwner, long bookId) throws Exception
    {
		if(sheetName == null)
		{
			ODSRangeParser parser = new ODSRangeParser(new OdsToSxcAdapter(this),sheetIndex,rangeAddr.startRow,rangeAddr.startCol,rangeAddr.endRow,rangeAddr.endCol,rangeAddr.noOfRows,rangeAddr.noOfCols,true); //*** true or false based on need
			parser.parse(store, docOwner, bookId, null, null, true);
		}
		else
		{
			ODSRangeParser parser = new ODSRangeParser(new OdsToSxcAdapter(this),sheetName,rangeAddr.startRow,rangeAddr.startCol,rangeAddr.endRow,rangeAddr.endCol,rangeAddr.noOfRows,rangeAddr.noOfCols,true); //*** true or false based on need
			parser.parse(store, docOwner, bookId, sheetName, null, true);
		}
		return generateOutput();
    }


    private SxcRangeParser getRangeParser() throws XmlPullParserException
    {
        if (sheetName == null)
        {
            return new SxcRangeParser(sheetIndex, this, rangeAddr, true);
        }
        return new SxcRangeParser(sheetName, this, rangeAddr, true);
    }
}
