/* $Id$ */
package com.zoho.sheet.parse.csv;

import com.adventnet.zoho.websheet.model.parser.ODSSheetParser;
import java.util.List;
import java.util.ArrayList;
import java.io.*;
import java.util.logging.Logger;
import java.util.logging.Level;


import org.xmlpull.v1.XmlPullParserException;
//DFS 
import com.adventnet.zoho.websheet.store.Store;
import com.zoho.sheet.parse.sxc.OdsToSxcAdapter;
import com.zoho.sheet.parse.sxc.Struct;
import com.zoho.sheet.parse.sxc.SxcEventListener;
import com.zoho.sheet.parse.sxc.SxcSheetParser;
/**
 * Generates CSV output for a particular sheet.
 * The sheet to be transformed can be specified by either it's 'name' or 'index'
 *
 * @see #transfrom
 */
public class CsvSheetTransformer implements SxcEventListener
{
	public static Logger logger = Logger.getLogger(CsvSheetTransformer.class.getName());
    ////    testing code -> START    ////
    public static class Testing
    {
        // usage : args[0] - filepath ; args[1] - 'index' | 'name' ; args[2] - <index/name>
        public static void main(String[] args) throws Exception
        {
            CsvSheetTransformer tx;
            if ("index".equals(args[1]))
            {
                tx = getIndexTx(Integer.parseInt(args[2]));
            }
            else
            {
                tx = getNameTx(args[2]);
            }
            long t1 = System.currentTimeMillis();
            StringBuffer result = tx.transfrom(getFileStream(args[0]));
            logger.info("Time taken(ms) : " + (System.currentTimeMillis() - t1));
            // writing the response to a file
            PrintWriter myout = new PrintWriter(new File("output.csv"));// output file
            myout.print(result.toString());
            myout.flush();
            myout.close();
        }

        private static InputStream getFileStream(String path) throws FileNotFoundException
        {
            return new BufferedInputStream(
                    new FileInputStream(path)
            );
        }

        private static CsvSheetTransformer getNameTx(String sheetName)
        {
            return new CsvSheetTransformer(sheetName);
        }

        private static CsvSheetTransformer getIndexTx(int sheetIndex)
        {
            return new CsvSheetTransformer(sheetIndex);
        }
    }
    ////    testing code -> END    ////

    ////    Inner class RowContent -> START    ////
    private class RowContent
    {
        private StringBuffer buff = new StringBuffer();
        private int size;

        /** cell.text is *not* expected to be null or empty */
        private void append(Struct.Cell cell)
        {
            while ((colIndex - size) > 0)
            {
                buff.append(',');
                size++;
            }
            String data = null;
            try
            {
            	 
            	//This block is get data from formula 
            	/*
            	 * If Formula not exist, then we are going for cell content
            	 * 
            	 * On Disadvantage Using This:
            	 * 	Below commented code itself should work, since it came under workbook to sxc adapter, this makes an issue.  
            	 * 	Will get fixed by avoiding sxc adapter.
            	 * 
            	 */
            	
            	//data = HtmlUtil.getFormulaLocal(cell.text, cell.valueType, cell.value);
            	
            	if(cell.formula != null && !cell.formula.startsWith("=")) {            		
            		data = cell.formula;
            	} else {
            		data = cell.text;
            	}

                // Inserting any cell content in quotes as even number or Date values can have comma in it and that comma should not be considered as  delimiter.
                //if ("string".equals(cell.valueType))
                //{
                    data = '"' + data.replaceAll("\"","\"\"") + '"';
                //}
            }
            catch (Exception e)
            {
				logger.log(Level.WARNING,null,e);
                data = "";
            }
            for (int i = 0; i < cell.rep; i++)
            {
                buff.append(data).append(',');
            }
            size = colIndex += cell.rep;
        }
    }
    ////    Inner class RowContent -> END    ////

    // the sheet to transformed is decided based on either sheetName or sheetIndex
    protected String sheetName;
    protected int sheetIndex;

    public CsvSheetTransformer(String sheetName)
    {
        this.sheetName = sheetName;
    }

    public CsvSheetTransformer(int sheetIndex)
    {
        this.sheetIndex = sheetIndex;
    }

    public StringBuffer transfrom(InputStream iStream) throws IOException, XmlPullParserException
    {
        SxcSheetParser parser = getSheetParser();
        parser.parse(iStream);
        return generateOutput();
    }

    public StringBuffer transfromODS(InputStream iStream) throws Exception
    {
		if(sheetName == null)
		{
			ODSSheetParser parser = new ODSSheetParser(new OdsToSxcAdapter(this),sheetIndex, true); //*** true or false based on need
			parser.parse(iStream, null);
		}
		else
		{
			ODSSheetParser parser = new ODSSheetParser(new OdsToSxcAdapter(this),sheetName, true); //*** true or false based on need
			parser.parse(iStream, null);
		}
		return generateOutput();
    }

   /* public StringBuffer transfromFragmentedFile(FileStore store,Long bookId) throws Exception
    {
		if(sheetName == null)
		{
			ODSSheetParser parser = new ODSSheetParser(new OdsToSxcAdapter(this),sheetIndex, true); //*** true or false based on need
			//parser.parse(iStream, null);
			parser.parse(store, EngineConstants.LOCATION + bookId, null, null, true);
		}
		else
		{
			ODSSheetParser parser = new ODSSheetParser(new OdsToSxcAdapter(this),sheetName, true); //*** true or false based on need
			//parser.parse(iStream, null);
			parser.parse(store, EngineConstants.LOCATION + bookId, sheetName, null, true);
		}
		return generateOutput();
    }*/
    public StringBuffer transfromFragmentedFile(Store store, String docOwner, long bookId) throws Exception
    {
		if(sheetName == null)
		{
			ODSSheetParser parser = new ODSSheetParser(new OdsToSxcAdapter(this),sheetIndex, true); //*** true or false based on need
			//parser.parse(iStream, null);
			parser.parse(store, docOwner,  bookId, null, null, true);
		}
		else
		{
			ODSSheetParser parser = new ODSSheetParser(new OdsToSxcAdapter(this),sheetName, true); //*** true or false based on need
			//parser.parse(iStream, null);
			parser.parse(store, docOwner,  bookId, sheetName, null, true);
		}
		return generateOutput();
    }

    protected StringBuffer generateOutput()
    {
        StringBuffer csvbuff = new StringBuffer();
        StringBuffer emptyRow = getEmptyRow();
        for (int i = 0; i < rows.size(); i++)
        {
            RowContent rowContent = (RowContent) rows.get(i);
            if (rowContent == null)
            {
                csvbuff.append(emptyRow);
            }
            else
            {
                if (rowContent.size == maxCol)
                {
                    rowContent.buff.deleteCharAt(rowContent.buff.length() - 1);
                }
                else if (rowContent.size < maxCol - 1)
                {
                    // fill with commas
                    for (; rowContent.size < (maxCol - 1); rowContent.size++)
                    {
                        rowContent.buff.append(',');
                    }
                    rowContent.size = maxCol;
                }
                csvbuff.append(rowContent.buff);
            }

            if (i < (rows.size() - 1))
            {
                csvbuff.append('\n');
            }
        }
        return csvbuff;
    }

    private StringBuffer getEmptyRow()
    {
        StringBuffer buff = new StringBuffer();
        for (int i = 0; i < (maxCol - 1); i++)
        {
            buff.append(',');
        }
        return buff;
    }

    private SxcSheetParser getSheetParser() throws XmlPullParserException
    {
        if (sheetName == null)
        {
            return new SxcSheetParser(sheetIndex, this, true);
        }
        return new SxcSheetParser(sheetName, this, true);
    }

    private int rowIndex;
    private int colIndex;
    protected int maxCol;
    private List rows = new ArrayList();
    private RowContent rowContent = null;

    ////    SXC Event Listener Interface -> START   ////
    public void updateFontStyle(Struct.Font font)
    {
        throw new UnsupportedOperationException("Method updateFontStyle() is not expected to be invoked..");
    }

    public void updateColumnStyle(Struct.Style style)
    {
        throw new UnsupportedOperationException("Method updateColumnStyle() is not expected to be invoked..");
    }

    public void updateRowStyle(Struct.Style style)
    {
        throw new UnsupportedOperationException("Method updateRowStyle() is not expected to be invoked..");
    }

    public void updateCellStyle(Struct.Style style)
    {
        throw new UnsupportedOperationException("Method updateCellStyle() is not expected to be invoked..");
    }

    public void startSheet(Struct.Sheet sheet)
    {
        rowIndex = 0; // reset buff index
        maxCol = 0;
    }

    public void endSheet(Struct.Sheet sheet)
    {
        // do nothing

    }

    public void updateColumnHeader(Struct.Sheet sheet)
    {
        throw new UnsupportedOperationException("Method updateColumnHeader() is not expected to be invoked..");
    }

    public void startRow(Struct.Sheet sheet)
    {
        // reset column index and column cursor
        colIndex = 0;
        rowContent = new RowContent();
    }

    public void endRow(Struct.Sheet sheet)
    {
        if (rowContent.size == 0) // if empty row
        {
            rowIndex += sheet.row.rep;
        }
        else
        {
			while(rows.size() < rowIndex)
            {
                rows.add(null);
            }
            for (int i = 0; i < sheet.row.rep; i++, rowIndex++)
            {
                rows.add(rowContent);
            }
            //maxRow = rowIndex;
            if (rowContent.size > maxCol)
            {
                maxCol = rowContent.size;
            }
        }
    }

    public void updateDummyCell(Struct.Sheet sheet)
    {
        colIndex += sheet.row.vCell.rep;
    }

    public void updateCell(Struct.Sheet sheet)
    {
        Struct.Cell thisCell = sheet.row.cell;
        if (thisCell.text == null) // if empty cell
        {
            colIndex += thisCell.rep;
        }
        else
        {
            rowContent.append(thisCell);
        }
    }
    ////    SXC Event Listener Interface -> END   ////
}
