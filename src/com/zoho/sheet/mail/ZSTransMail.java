/*  $Id$ */
package com.zoho.sheet.mail;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;


import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import org.apache.tika.Tika;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.transmail.Email;
import com.transmail.EmailAddressInfo;
import com.transmail.FileInfo;
import com.transmail.Files;
import com.transmail.Properties;
import com.transmail.Response;
import com.transmail.TransmailCallable;
import com.transmail.Email.EmailBuilder;
import com.transmail.NameConstants.Authorization;
import com.zoho.sas.container.AppResources;
import com.zoho.sheet.util.RemoteUtils;
import java.util.logging.Level;

public class ZSTransMail {
	static Logger logger = Logger.getLogger(ZSTransMail.class.getName());
	Email email = null;
	EmailBuilder emailBuilder = null;
	String toMailAddress = null; // The email address to which mail being sent
	String fromMailAddress = null; // the email address from which mail being sent
	String subject = null; // subject of the mail
	String message = null; // mail body message which you want to convey
	String mailAgentKey = null; // EnginePropertyUtil.getSheetPropertyValue("newTransMailAgentKey"); // mail agent key for the project which you are using , get it from transmail account with respective of project  //No I18N 
	String bouncMailAddress = null;  // EnginePropertyUtil.getSheetPropertyValue("newTransMailBoundMailAddress"); //  bouncMailAddress can be found in transmail account for each project //No I18N
	String authoken = EnginePropertyUtil.getSheetPropertyValue("zst_authtoken");  // the authtoken which you have generated for TRANSMAIL API //No I18N
	List < File > attachmentFile = new ArrayList < File > ();
	List < String > attachmentFilePaths = new ArrayList < String > ();
	List < String > attachmentFileKeys = new ArrayList < String > ();
	List < EmailAddressInfo > toEmails = new ArrayList < EmailAddressInfo > ();
	List < EmailAddressInfo > toCCEmails = new ArrayList < EmailAddressInfo > ();
	List < EmailAddressInfo > toBCCEmails = new ArrayList < EmailAddressInfo > ();

	User currentUser = IAMUtil.getCurrentUser();
	String fromUserName = null;

	String mailMessageType         = "TEXT"; //No I18N
	String mailProjectType           = null;  //we have 3 project as of now as "APPLICATION", "USER" and "BUG". So it can be any of these. // No I18N 
	String defaultProject              = "APPLICATION"; //No I18N 
	JSONObjectWrapper userMailObject = new JSONObjectWrapper();
	static String transMailConfigFilePath = AppResources.getProperty("server.home") + "/conf/transmail-config.properties"; //No I18N
	
	//	 initializing TransMail Configuration 
	static {
		Properties.setConfigFile(transMailConfigFilePath);
	}

	//	 Constructor to initialize mail in old way
	 ZSTransMail(JSONObjectWrapper userMailObject, String projectApiType) {
		this.userMailObject = userMailObject;
		setProjectType(projectApiType);
		initMailObjectInfo();
	}

	//	 New TransMail 
	 ZSTransMail(JSONObjectWrapper userMailObject) {
		this.userMailObject = userMailObject;
		initMailObjectInfo();
	}

	//	 sending mail
	private void setupMail() {
		EmailAddressInfo frmMailId = new EmailAddressInfo(this.fromMailAddress);
		if(this.fromUserName != null ) {
		   frmMailId.setName(this.fromUserName);
		}
		
		this.emailBuilder
		.setTo(this.toEmails)
		.addMimeHeader("X-Transmail", "transmail") //No I18N
		.setBounceAddress(this.bouncMailAddress)
		.setFrom(frmMailId);
		
		if (this.toCCEmails.size() > 0) {
			this.emailBuilder.setCc(this.toCCEmails);
		}
		if (this.toBCCEmails.size() > 0) {
			this.emailBuilder.setBcc(this.toBCCEmails);
		}
		if (this.mailMessageType.equals("HTML")) {
			this.emailBuilder.setHtmlBody("<html><body>" + message + "</body></html>"); //No I18N
		} else {
			this.emailBuilder.setTextBody(message);
		}
		this.emailBuilder.setSubject(subject).setTag("notification"); //No I18N
//		.setTrackClicks(true).setTrackOpens(true);
		if(currentUser != null){
			this.emailBuilder.setInternalUserId(currentUser.getZuid());
			this.emailBuilder.setInternalOrgId(currentUser.getZoid());
		}else{
			try{
				if(this.userMailObject.has(JSONConstants.INTERNAL_USER_ID)){
					this.emailBuilder.setInternalUserId(this.userMailObject.getString(JSONConstants.INTERNAL_USER_ID));
				}
				if(this.userMailObject.has(JSONConstants.INTERNAL_ORG_ID)){
					this.emailBuilder.setInternalOrgId(this.userMailObject.getString(JSONConstants.INTERNAL_ORG_ID));
				}
			}catch (Exception e){
				logger.log(Level.INFO,"Exception Occurs while getting user or org id :: {0}",new Object[]{e} );
			}
		}
		this.email = this.emailBuilder.build();
	}
	private JSONObjectWrapper getPasrsedError(String errorStr) {
		JSONObjectWrapper  errJObj = new JSONObjectWrapper();
		try {
			JSONObjectWrapper _errJObj = new JSONObjectWrapper(errorStr);
			if(_errJObj.has("error")) {
				JSONObjectWrapper errorDetailsJObj = _errJObj.getJSONObject("error");
				if(errorDetailsJObj!=null && !errorDetailsJObj.isEmpty())	{
					if(errorDetailsJObj.has("code")) {
						errJObj.put("ERROR_CODE", errorDetailsJObj.getString("code"));
					}
					if(errorDetailsJObj.has("request_id")) {
						errJObj.put("REQUEST_ID", errorDetailsJObj.getString("request_id"));
					}
					if(errorDetailsJObj.has("message")) {
						errJObj.put("MESSAGE", errorDetailsJObj.getString("message"));
					}
					if(errorDetailsJObj.has("details")) {
						JSONArrayWrapper detailErrorJArry = errorDetailsJObj.getJSONArray("details");
						JSONObjectWrapper detailErrorJObj = detailErrorJArry.getJSONObject(0);
						if(detailErrorJObj!=null && !detailErrorJObj.isEmpty()) {
							if(detailErrorJObj.has("message")) {
						      errJObj.put("DETAIL_MESSAGE!", detailErrorJObj.getString("message"));
							}
							if(detailErrorJObj.has("code")) {
								errJObj.put("DETAIL_CODE", errorDetailsJObj.getString("code"));
							}
						}
					}
				}
			}
			logger.info("[TRANSMAIL SENDING ERROR ] :: "+errJObj);
		}
		catch(Exception e) {
			logger.info("[TRANSMAIL  ERROR  PARSING EXCPETION] :: "+e);
		}
		return errJObj;
	}
	
	//	
	public JSONObjectWrapper send() {
		JSONObjectWrapper respObj = new JSONObjectWrapper();
		try {
			Response res = this.email.sendSync();
			int mailSendRespCode = res.getHttpStatus();
			logger.log(Level.INFO, "TransMail send: mailSendRespCode: {0}", mailSendRespCode);
			if ( mailSendRespCode >= 200 && mailSendRespCode < 300 ) {
				if (res.getEntity().contains("additional_info")) {
					respObj.put("RESP_MSG",  "mail sent success with additional info>>"+ res.getEntity());
				}
				else {
					respObj.put("RESP_MSG",  "mail sent success, response>> " + res.getEntity());
				}
			}
			else if (mailSendRespCode >= 400 & mailSendRespCode < 500) {
				respObj.put("RESP_MSG",  "mail sending failure>> " + getPasrsedError(res.getEntity()));
			}
			else if (mailSendRespCode >= 500) {
				respObj.put("RESP_MSG",  "mail sending failure>> " + getPasrsedError(res.getEntity()));
			}
			else {
				respObj.put("RESP_MSG",  "case not found, transmail response>> " + res);
			}
			respObj.put("RESP_CODE",  mailSendRespCode);
			logger.log(Level.INFO, "TransMail send: respObj: {0}", respObj);
			
			TransmailCallable transmailCallable = new TransmailCallable() {
				@Override
				public void onSuccess(Response response) {
					logger.info(response.toString());
				}

				@Override
				public void onFailure(Throwable throwable) {
					logger.info(throwable.getMessage());
				}
			};
			return respObj;

		} catch(Exception e) {
			respObj.put("RESP_MSG",  " Exception occured while sending mail ::" + e);
			respObj.put("RESP_CODE",  -1);
			logger.info(" Exception occured while sending mail ::" + e);
			return respObj;
		}
	}
	//	 initializing all info for setting up mail object 
	private void initMailObjectInfo() {
		setMailAddress();
		setMailSubjectMessage();
		setProjectType();
		setBounceEmailAddress();
		setMailAgentkey();
                
		setFileAttachementKeys();
		initMailBuilder();
                
                setAttachmentKey();
		setupMail();
	}

    private void setAttachmentKey() {
        if (this.userMailObject.has("ATTACHMENT_ID") && this.userMailObject.getString("ATTACHMENT_ID").trim().length() > 0) {
            JSONArrayWrapper attachmentKeyArray = this.userMailObject.getJSONArray("ATTACHMENT_ID");

            if (attachmentKeyArray != null) {
                for (int index = 0; index < attachmentKeyArray.length(); index++) {
                    this.emailBuilder.addAttachmentFileKey(attachmentKeyArray.getString(index));
                }
            }
        }
    }

	//	 Setting  to/cc/bcc Mail Address from user input
	public void setMailAddress() {
		if(     this.userMailObject.has("FROM_USER_NAME") && this.userMailObject.getString("FROM_USER_NAME").trim().length() > 0) {
			this.fromUserName = this.userMailObject.getString("FROM_USER_NAME");
		}
		
		if (this.userMailObject.has("TO") && this.userMailObject.getJSONArray("TO").length() > 0) {
			JSONArrayWrapper toMailAdd = this.userMailObject.getJSONArray("TO");
			for (int index = 0; index < toMailAdd.length(); index++) {
				this.toEmails.add(new EmailAddressInfo(toMailAdd.getString(index)));
			}
		}
		if (this.userMailObject.has("CC_MAIL") && this.userMailObject.getJSONArray("CC_MAIL").length() > 0) {
			JSONArrayWrapper ccMailAdd = this.userMailObject.getJSONArray("CC_MAIL");
			for (int index = 0; index < ccMailAdd.length(); index++) {
				this.toCCEmails.add(new EmailAddressInfo(ccMailAdd.getString(index)));
			}
		}
		if (this.userMailObject.has("BCC_MAIL") && this.userMailObject.getJSONArray("BCC_MAIL").length() > 0) {
			JSONArrayWrapper bccMailAdd = this.userMailObject.getJSONArray("BCC_MAIL");
			for (int index = 0; index < bccMailAdd.length(); index++) {
				this.toBCCEmails.add(new EmailAddressInfo(bccMailAdd.getString(index)));
			}
		}
		if (this.userMailObject.has("FROM") && this.userMailObject.getString("FROM").length() > 0) {
			this.fromMailAddress = this.userMailObject.getString("FROM");
		}
	}

	//	 setting mail subject and message to being sent over mail 
	private void setMailSubjectMessage() {
		if (this.userMailObject.has("MESSAGE_TYPE") && (this.userMailObject.getString("MESSAGE_TYPE")).trim().length() > 0) {
			if ((this.userMailObject.getString("MESSAGE_TYPE")).trim().equals("HTML")) {
				this.mailMessageType = "HTML"; //No I18N
			}
		}
		if (this.userMailObject.has("MESSAGE") && (this.userMailObject.getString("MESSAGE")).trim().length() > 0) {
			this.message = this.userMailObject.getString("MESSAGE");
		}
		if (this.userMailObject.has("SUBJECT") && (this.userMailObject.getString("SUBJECT")).trim().length() > 0) {
			this.subject = this.userMailObject.getString("SUBJECT");
		}
	}

	//	initializing  mail builder with mail agent key and authtoken
	private void initMailBuilder() {
		this.emailBuilder = new Email.EmailBuilder(this.mailAgentKey, Authorization.AUTH_TOKEN, this.authoken); 
	}



	//	  Api to upload the file and get Attachment Key 			 
	private String uploadFile(String filePath) {
		// multipart attachment 
		try {
			Files cli = new Files(mailAgentKey, Authorization.AUTH_TOKEN, authoken); 
			File file = new File(filePath); 
			FileInfo fileInfo = new FileInfo(file,file.getName(),(new Tika()).detect(file));
			cli.setContent(fileInfo);
			Response respo = cli.uploadSync();
			JSONObjectWrapper respionseObject = new JSONObjectWrapper(respo.getEntity().toString());
			if (respionseObject.has("file_key")) {
				return respionseObject.getString("file_key");
			}
			else {
				logger.info(" There is problem while uploading file to the tramsmail, filePath:  " + filePath);
				return null;
			}
		}
		catch(Exception e) {
			logger.info(" Exception while uploading file  " + e);
			return null;
		}
	}

	//	  If user providing the attachment key  then below api will be used
	private void setFileAttachementKeys() {
		JSONArrayWrapper _uploadFileIdArray = (JSONArrayWrapper) getJsonValue(this.userMailObject, "ATTCHMENT_ID", "JSON_ARRAY"); //NO I18N
		if (_uploadFileIdArray != null) {
			for (int index = 0; index <= _uploadFileIdArray.length(); index++) {
				String tempAttachmentKey = RemoteUtils.maskNull(_uploadFileIdArray.getString(index).trim());
				if (tempAttachmentKey != null) {
					this.attachmentFileKeys.add(tempAttachmentKey);
				}
			}
		}
	}

	//	 this will used for supporting old api
	private void setProjectType(String apiType) {
		apiType = RemoteUtils.maskNull(apiType);
		if (apiType != null) {
			this.mailProjectType = apiType.split("_")[0];
		}
	}

	//	  setting bounceaddress for sending mail
	private void setProjectType() {
		String projType = null;
		if (this.userMailObject.has("PROJECT_TYPE")) {
			projType = RemoteUtils.maskNull(this.userMailObject.getString("PROJECT_TYPE").trim());
		}
		this.mailProjectType = projType != null ? projType: this.defaultProject;
	}

	//	  setting bounceaddress for sending mail
	private void setBounceEmailAddress() {
		this.bouncMailAddress = EnginePropertyUtil.getSheetPropertyValue(this.mailProjectType + "_BOUNCE_ADDRESS"); //No I18N
	}

	//	  setting Mail Agent Key 
	private void setMailAgentkey() {
		this.mailAgentKey = EnginePropertyUtil.getSheetPropertyValue(this.mailProjectType + "_MAIL_AGENT_KEY"); //No I18N
	}

	//	  utility method to get value form JSONObject 
	private static Object getJsonValue(JSONObjectWrapper json, String key, String retrunType) {
		try {
			if (json != null && json.has(key) && !json.getString(key).isEmpty()) {
				switch (retrunType) {
				case "JSON_ARRAY":
					return json.getJSONArray(key);
				case "JSON_OBJECT":
					return json.getJSONObject(key);
				case "INTEGER":
					return json.getInt(key);
				default:
					return json.getString(key);
				}

			}
		}
		catch(Exception e) {
			logger.info(" json ::" + json + " key ::" + key + "  retrunType::" + retrunType + " Exception ::" + e);
		}
		return null;
	}
}
