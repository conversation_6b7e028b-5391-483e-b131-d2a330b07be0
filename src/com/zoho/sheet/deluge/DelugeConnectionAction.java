package com.zoho.sheet.deluge;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.zfsng.client.ZohoFS;

import com.zoho.zfsng.client.ZohoFS;



import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.zoho.zfsng.client.ZohoFS;


public class DelugeConnectionAction extends HttpServlet{
	public static Logger logger = Logger.getLogger(DelugeConnectionAction.class.getName());
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		try {
			String zuid = req.getParameter("zuId");
        	String sharedBy = req.getParameter("sharedBy");
			JSONObjectWrapper response = new JSONObjectWrapper();
        	User user = IAMUtil.getCurrentUser();

        	logger.info("Deluge  connection details print here:::"+zuid+"::::"+sharedBy);

        	if(user != null){
	        	String _zuid = user.getZuid();

	        	if(zuid.equalsIgnoreCase(_zuid)){
					boolean isValidUser = verifyUser(zuid,sharedBy);
					if(isValidUser)
					{
						DelugeUtils.checkAndCreateSpace(sharedBy);
					}
					response.put("status",isValidUser);

	        	}else{
	        		response.put("status",false);
	        	}
        	}else{
        		response.put("status",false);
        	}
        		 PrintWriter out = resp.getWriter();
	             out.println(response); 
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.info(" Problem while Authentication user for connection" + e);
		}
	}


	private boolean verifyUser(String zuid,String sharedBy){
		try{

			if (sharedBy.equalsIgnoreCase(zuid)) {
				return true;			//user space
			}
			String orgDetails  = ZohoFS.getUserOrgInfos(zuid,1);
			if(orgDetails != null){
				JSONArrayWrapper _orgDetails = new JSONArrayWrapper(orgDetails);
				for (int i=0;i<_orgDetails.length();i++){
					JSONObjectWrapper org = _orgDetails.getJSONObject(i);
					if(org.has("resource_id")){
						String rid = org.getString("resource_id");
						String zid  =  ZohoFS.getOwnerZID(rid);
						logger.info("deluge connection :"+sharedBy+"::"+zid);
						if (sharedBy.equalsIgnoreCase(zid)){
							return true;


						}
					}
				}


			}
		}catch (Exception e){
			logger.log(Level.WARNING,"Problem While verifying user for deluge connection",e);

		}
		return false;
	}

	
}
