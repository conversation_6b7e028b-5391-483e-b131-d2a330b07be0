package com.zoho.sheet.deluge;

import com.adventnet.ds.query.*;
import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.ear.common.util.EARException;
import com.zoho.ear.encryptagent.EncryptAgent;
import com.zoho.sheet.util.SheetPersistenceUtils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class OAuthUtils {
    private static String authTokenTableName = "OAuthToken";// No I18N
    //private static String serverName = "https://" + EnginePropertyUtil.getSheetPropertyValue("DELUGE_SERVER"); // No I18N
    private static String scopeKey;
    private static String[] scopeValue;
    private static String serviceName = "ZohoSheet";// NO I18N
    private static String secretLabel = "zohosheet_pwd_secret";// No I18N


    public static final Logger LOGGER = Logger.getLogger(OAuthUtils.class.getName());
    private static void setScopes(HashMap<String, String[]> scope){
        String _scope="";
        for (String eachScope : scope.keySet()) {
            _scope = eachScope;
        }
        scopeKey= _scope;
        scopeValue=scope.get(scopeKey);
    }
    public static String getOAuthToken(Workbook workbook,Long zuid ,HashMap<String,String[]> scope,String dbUserName,String clientId){
        try{
            
            setScopes(scope);
            String oauthToken = null;
            HashMap userDetails = workbook.getUserDetails();
            if (userDetails != null && userDetails.containsKey(scopeKey+"_OAUTHTOKEN")) {
                oauthToken = (String) userDetails.get(scopeKey+"_OAUTHTOKEN");
                long timeupdated = (long) userDetails.get(scopeKey+"_CREATED_TIME");
                long currentTime = System.currentTimeMillis();
                if(currentTime - timeupdated < (59 * 60000)){
                    LOGGER.info("[OAUTHUTILS] AUTH TOKEN not expired for user ::::::::::"+ zuid+"::::"+currentTime+"::::"+timeupdated+"::::"+scopeKey);
                    if (oauthToken != null) {

                        return decryptToken(workbook,oauthToken);
                    }

                }else{
                    LOGGER.info("[OAUTHUTILS] AUTH TOKEN  EXPIRED for user ::::::::::"+ zuid+"::::"+timeupdated+"::::"+scopeKey);
                    return OAuthUtils.generateOAuthToken(workbook,zuid,scope,dbUserName,clientId);

                }
                //return decryptToken(workbook,oauthToken);
            }else {
                //String zuid = (String) userDetails.get("DOCS_SPACE_ID");
                //String sheetSpaceName = (String) userDetails.get("SHEET_SPACE_NAME");
                Persistence persistence = SheetPersistenceUtils.getPersistence(dbUserName);
                SelectQueryImpl sql = new SelectQueryImpl(new Table(authTokenTableName));
                LOGGER.info("[OAUTHUTILS] AUTH TOKEN try to get from db  for user ::::::::::"+ zuid+"::::"+scopeKey);//"dre"// No I18N
                sql.addSelectColumn(new Column(null, "*"));
                Criteria crt = new Criteria(new Column(authTokenTableName, "ZUID"), Long.valueOf(zuid), QueryConstants.EQUAL); // NO
                crt = crt.and(new Criteria(new Column(authTokenTableName, "scope"), scopeKey, QueryConstants.EQUAL));
                sql.setCriteria(crt);
                DataObject dobj = persistence.get(sql);
                if (!dobj.isEmpty()) {
                    Iterator iterator = dobj.getRows(authTokenTableName);
                    while (iterator.hasNext()) {
                        Row row = (Row) iterator.next();
                        Object _auth = row.get("OAUTHTOKEN");
                        if(_auth != null){
                            oauthToken = _auth.toString();

                            long timeupdated = (long) row.get("CREATED_TIME");
                            long currentTime = System.currentTimeMillis();
                            if(currentTime - timeupdated < (59 * 60000)){
                                LOGGER.info("[OAUTHUTILS] AUTH TOKEN expired in db  for user ::::::::::"+ zuid+"::::::OAUTHTOKEN EXPIRED"+"::::"+scopeKey);

                                userDetails.put(scopeKey+"_OAUTHTOKEN", oauthToken);
                                userDetails.put(scopeKey+"_CREATED_TIME", timeupdated);
                                workbook.setUserDetails(userDetails);
                                return decryptToken(workbook,oauthToken);
                            }else{
                                return OAuthUtils.generateOAuthToken(workbook,zuid,scope,dbUserName,clientId);

                            }
                        }else{
                            return OAuthUtils.generateOAuthToken(workbook,zuid,scope,dbUserName,clientId);
                        }
                    }
                }else{
//
                    return OAuthUtils.generateOAuthToken(workbook,zuid,scope,dbUserName,clientId);
                }
            }
        }catch(Exception e){
            LOGGER.log(Level.INFO, "Problem in Checking AuthToken", e);
        }

        return null;

    }
    public static String generateOAuthToken(Workbook workbook, Long zuid, HashMap<String,String[]> scope,String dbUserName,String clientId ){
        //String userName = (String) workbook.getUserDetails().get("SHEET_SPACE_NAME");// No I18N
        try{
            setScopes(scope);
           // String clientId = EnginePropertyUtil.getSheetPropertyValue("SHEET_DELUGE_CLIENT_ID") ;// No I18N
            String refreshToken = getRefreshToken(workbook,zuid,scope,dbUserName,clientId);
            if(refreshToken != null){
                String accessToken = IAMProxy.getInstance().getOAuthAPI().generateInternalOAuthAccessToken(clientId, zuid,refreshToken, serviceName, null, null);
                String encryptedAccessToken = encryptToken(accessToken);
                LOGGER.info("[OAUTHUTILS] refreshtoken is not null  ACCESSTOKEN for user ::::::::::"+ zuid+"::::"+scopeKey);
                setAccessTokenInDB(dbUserName,encryptedAccessToken, zuid,scopeKey);
                HashMap userDetails = workbook.getUserDetails();
                userDetails.put(scopeKey+"_OAUTHTOKEN", encryptedAccessToken);
                userDetails.put(scopeKey+"_CREATED_TIME",System.currentTimeMillis() );
                workbook.setUserDetails(userDetails);
                return accessToken;
            }else {
                LOGGER.info("[OAUTHUTILS] Refresh token empty ");
                return null;
            }
        }catch(Exception e){
            LOGGER.log(Level.WARNING,"[OAUTHUTILS] AUTH TOKEN generation:::",e);
//			setAccessTokenInDB(userName,"", zuid);
//			HashMap userDetails = workbook.getUserDetails();
//			userDetails.remove("OAUTHTOKEN");
//			userDetails.remove("CREATED_TIME");
//			workbook.setUserDetails(userDetails);
        }
        return null;
    }


    public static String generateRefreshToken(Workbook workbook,String userName,Long zuid,HashMap<String,String[]> scope,String clientId){
        try{
            setScopes(scope);
            LOGGER.info("[OAUTHUTILS] REFRESHTOKEN generation for user ::::::::::"+ zuid+"::::"+scopeKey);
            User user = IAMUtil.getCurrentUser();
            if (user != null && user.getZUID() == zuid && IAMUtil.getCurrentTokenType() == IAMUtil.TokenType.TICKET) {
               // String clientId =EnginePropertyUtil.getSheetPropertyValue("SHEET_DELUGE_CLIENT_ID") ;// No I18N
                String refreshToken ="";

                refreshToken = IAMProxy.getInstance().getOAuthAPI().generateUserOAuthRefreshTokenbyTicket(clientId, zuid, scopeValue, serviceName, null ,null);

                //String[] scopes ={"DRE.dreapi.ALL"} ;// No I18N
                //	IAMProxy.getInstance().getOAuthAPI().generateInternalOAuthAccessToken(arg0, arg1, arg2, arg3, arg4, arg5)
                String encryptedRefreshToken = encryptToken(refreshToken);
                setRefreshTokenInDB(userName,encryptedRefreshToken, zuid,scopeKey);
                HashMap userDetails = workbook.getUserDetails();
                userDetails.put(scopeKey+"_REFRESHTOKEN", encryptedRefreshToken);
                workbook.setUserDetails(userDetails);
                return refreshToken;
            }
            LOGGER.log(Level.INFO, "User Presence Not there for Refresh Token generation ",zuid);
        }catch(Exception e){ // To Do  be Handled
            LOGGER.log(Level.INFO, "Problem in Checking AuthToken", e);
        }
        return null;

    }

    public static void setRefreshTokenInDB(String userName,String token,Long zuid,String scope){
        try {
            LOGGER.info("[OAUTHUTILS] AUTH TOKEN REFRESH TOKEN SET IN DB   for user ::::::::::"+ zuid);
            Persistence persistence = SheetPersistenceUtils.getPersistence(userName);
            DataObject dobj = persistence.constructDataObject();
            Row row = new Row(authTokenTableName);
            row.set("ZUID", Long.valueOf(zuid));
            row.set("SCOPE", scope);// No I18N
            row.set("REFRESHTOKEN", token);// No I18N
            row.set("CREATED_TIME",0);
            dobj.addRow(row);
            persistence.add(dobj);
//			userDetails.put(scopeKey+"_authtoken", encryptAuth);
//			workbook.setUserDetails(userDetails);

        }catch(Exception e){
            LOGGER.log(Level.INFO, "Problem in Checking AuthToken", e);
        }

    }
    public static String encryptToken(String authToken) {
        try {

            //return com.adventnet.iam.CryptoUtil.encrypt(secretLabel, authToken);
            String refreshToken = EnginePropertyUtil.getSheetPropertyValue("EAR_REFRESH_TOKEN");	//No I18N
            String encryptVal= EncryptAgent.getInstance().encrypt(secretLabel, authToken, refreshToken, false);
            return encryptVal;

        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Problem in Encrypting  AuthToken", e);

        }
        return null;
    }
    public static String decryptToken(Workbook workbook,String token) {
        try {
            String earRefreshToken = EnginePropertyUtil.getSheetPropertyValue("EAR_REFRESH_TOKEN");	//No I18N
            String encryptVal=EncryptAgent.getInstance().decrypt(secretLabel, token, earRefreshToken, false);
            return encryptVal;
        } catch(java.lang.IllegalArgumentException exp) {
            LOGGER.log(Level.INFO, "Problem in IAM ERROR Decrypting AuthToken", exp.getMessage());


        }catch(EARException exp) {
            //if(exp.getErrorCode().equals("E101")){
            LOGGER.log(Level.INFO, "Problem in EAR  Decrypting AuthToken", exp.getMessage());
//            try{
//
//
//                String decryptToken = com.adventnet.iam.CryptoUtil.decrypt(secretLabel, token);
//                String encryptToken = encryptToken(decryptToken);
//
//                //updateAuthTokeninDB(workbook,encryptToken);
//                return decryptToken;
//            }catch(Exception ex){
//
//                LOGGER.log(Level.INFO, "Problem in IAM ERROR Decrypting AuthToken", ex.getMessage());
////					if(isCreateAllowed) {
////						return recreateAuthToken(workbook);
////					}
//            }
            //}

        } catch (Exception e) {

            LOGGER.log(Level.INFO, "Problem in Decrypting AuthToken", e);
        }
        return null;
    }
    public static void setAccessTokenInDB(String userName,String token,Long zuid,String scope ){
        try {

            LOGGER.info("[OAUTHUTILS] AUTH TOKEN ACCESS TOKEN SET IN DB   for user ::::::::::"+ zuid);

            Persistence persistence = SheetPersistenceUtils.getPersistence(userName);
            DataObject dobj = persistence.constructDataObject();
            dobj = persistence.constructDataObject();
            Criteria crt = new Criteria(new Column(authTokenTableName, "ZUID"), zuid,QueryConstants.EQUAL);// No I18N
            //To Do add scope criteria
            crt = crt.and(new Criteria(new Column(authTokenTableName, "scope"), scope, QueryConstants.EQUAL));// No I18N
            UpdateQueryImpl uq = new UpdateQueryImpl(authTokenTableName);
            uq.setUpdateColumn("OAUTHTOKEN", token);//No I18N
            uq.setUpdateColumn("CREATED_TIME",System.currentTimeMillis());
            uq.setCriteria(crt);
            persistence.update(uq);

        }catch(Exception e){
            LOGGER.log(Level.INFO, "Problem in Checking AuthToken", e);
        }

    }
    public static String getRefreshToken(Workbook workbook,Long zuid,HashMap<String,String[]> scope, String dbUserName,String clientId ){
        try{
            setScopes(scope);
            String refreshToken = null;

            HashMap userDetails = workbook.getUserDetails();
            if (userDetails != null && userDetails.containsKey(scopeKey+"_REFRESHTOKEN")) {
                refreshToken = (String) userDetails.get(scopeKey+"_REFRESHTOKEN");
                return decryptToken(workbook,refreshToken);
            }else {
                //String dbUserName = (String) userDetails.get("SHEET_SPACE_NAME");

                Persistence persistence = SheetPersistenceUtils.getPersistence(dbUserName);
                SelectQueryImpl sql = new SelectQueryImpl(new Table(authTokenTableName));
                LOGGER.info("[OAUTHUTILS] REFRESH TOKEN  retrieval from DB  for user ::::::::::"+ zuid);
                sql.addSelectColumn(new Column(authTokenTableName, "*"));
                Criteria crt = new Criteria(new Column(authTokenTableName, "ZUID"), Long.valueOf(zuid), QueryConstants.EQUAL); // NO I18N

                crt = crt.and(new Criteria(new Column(authTokenTableName, "scope"), scopeKey, QueryConstants.EQUAL));

                //crt = crt.and(new Criteria(new Column(authTokenTableName, "scope"), scopes, QueryConstants.EQUAL));
                sql.setCriteria(crt);
                DataObject dobj = persistence.get(sql);
                if (!dobj.isEmpty()) {
                    Iterator iterator = dobj.getRows(authTokenTableName);
                    while (iterator.hasNext()) {
                        Row row = (Row) iterator.next();


                        refreshToken = (String) row.get("REFRESHTOKEN");
                        if (refreshToken != null) {
                            userDetails.put(scopeKey+"_REFRESHTOKEN", refreshToken);
                            workbook.setUserDetails(userDetails);
                            return decryptToken(workbook,refreshToken);

                        }
                        else{
                            LOGGER.info("[OAUTHUTILS] refreshtoken not in  db so generating");
                            return 	generateRefreshToken(workbook,dbUserName,zuid, scope,clientId);
                        }
                    }


                }else{
                    LOGGER.info("[OAUTHUTILS] AUTH TOKEN REFRESH TOKEN  retrieval from DB DOBJ Empty");
                    return generateRefreshToken(workbook,dbUserName,zuid,scope,clientId);

                }
            }
        }catch(Exception e){
            LOGGER.log(Level.INFO, "Problem in Checking AuthToken", e);
        }

        return null;

    }
    public static String getAuthTokenFromDB(String userName,long zuid){
        try{

            Persistence persistence = SheetPersistenceUtils.getPersistence(userName);
            SelectQueryImpl sql = new SelectQueryImpl(new Table(authTokenTableName));
            LOGGER.info("[OAUTHUTILS] AUTH TOKEN generation  for user ::::::::::"+ zuid);
            sql.addSelectColumn(new Column(null, "*"));
            Criteria crt = new Criteria(new Column(authTokenTableName, "ZUID"), Long.valueOf(zuid), QueryConstants.EQUAL); // NO
            // I18N
            crt = crt.and(new Criteria(new Column(authTokenTableName, "scope"), "dre", QueryConstants.EQUAL));
            sql.setCriteria(crt);
            DataObject dobj = persistence.get(sql);
            if (!dobj.isEmpty()) {
                Iterator iterator = dobj.getRows(authTokenTableName);
                while (iterator.hasNext()) {
                    Row row = (Row) iterator.next();

                    String authToken = (String) row.get("AUTHTOKEN");
                    LOGGER.info("[OAUTHUTILS] AUTH TOKEN generation  for user ::::::::::"+ zuid);
                    if (authToken != null) {

                        return decryptAuthToken(authToken);
                    }
                }
            }
        }catch(Exception e){
            LOGGER.log(Level.WARNING,"[OAUTHUTILS] Auth token get from db error",e);
            return null;
        }
        return null;
    }
    public static String decryptAuthToken(String authToken) {
        try {
            String refreshToken = EnginePropertyUtil.getSheetPropertyValue("EAR_REFRESH_TOKEN");	//No I18N
            String encryptVal=EncryptAgent.getInstance().decrypt(secretLabel, authToken, refreshToken, false);
            return encryptVal;
        } catch(java.lang.IllegalArgumentException exp) {
            LOGGER.log(Level.INFO, "Problem in IAM ERROR Decrypting AuthToken", exp.getMessage());

//					if(isCreateAllowed) {
//						return generateOAuthToken(workbook,Long.parseLong(sharedBy));
//					}


        }catch(EARException exp) {
            //if(exp.getErrorCode().equals("E101")){
            LOGGER.log(Level.INFO, "Problem in EAR  Decrypting AuthToken", exp.getMessage());
//            try{
//
//
//                String decryptToken = com.adventnet.iam.CryptoUtil.decrypt(secretLabel, authToken);
//                //String encryptToken = encryptAuthToken(decryptToken);
//                //updateAuthTokeninDB(workbook,encryptToken);
//                return decryptToken;
//            }catch(Exception ex    ){
//
////					if(isCreateAllowed) {
////						return generateOAuthToken(workbook,Long.parseLong(sharedBy));
////					}
//            }
//            //}

        } catch (Exception e) {

            LOGGER.log(Level.INFO, "Problem in Decrypting AuthToken", e);
        }
        return null;
    }

}
