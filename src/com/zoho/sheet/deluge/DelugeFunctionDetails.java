/* $Id$ */
package com.zoho.sheet.deluge;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;



public class DelugeFunctionDetails {
	
	private String functionId;
	private JSONArrayWrapper paramDetails=null;
	private String returnType=null;
	private String script=null;
	private String description=null;
	private String zuid=null;
	private String functionScope=null;
	
	
//	public DelugeFunctionDetails(String functionId,JSONArray paramDetails,String returnType) {
//		this.functionId =functionId;
//		this.paramDetails =paramDetails;
//		this.returnType = returnType;
//		this.script = null;
//		this.description = null;
//	}
//	public DelugeFunctionDetails(String functionId,JSONArray paramDetails,String returnType,String script) {
//		this.functionId =functionId;
//		this.paramDetails =paramDetails;
//		this.returnType = returnType;
//		this.script = script;
//		this.description = null;
//	}
//	public DelugeFunctionDetails(String functionId,JSONArray paramDetails,String returnType,String script,String description) {
//		this.functionId =functionId;
//		this.paramDetails =paramDetails;
//		this.returnType = returnType;
//		this.script = script;
//		this.description = description;
//	}
//	public DelugeFunctionDetails(String functionId,JSONArray paramDetails,String returnType,String script,String description,String zuid) {
//		this.functionId =functionId;
//		this.paramDetails =paramDetails;
//		this.returnType = returnType;
//		this.script = script;
//		this.description = description;
//		this.zuid = zuid;
//	}
	public DelugeFunctionDetails(String functionId,JSONArrayWrapper paramDetails,String returnType,String script,String description,String zuid,String functionScope) {
		this.functionId =functionId;
		this.paramDetails =paramDetails;
		this.returnType = returnType;
		this.script = script;
		this.description = description;
		this.zuid = zuid;
		this.functionScope = functionScope;
	}
	
	
	public void setFunctionName(String functionId) {
		this.functionId = functionId;
	}
	public void setZUID(String zuid) {
		this.zuid = zuid;
	}
	
	public void setParamDetails(JSONArrayWrapper paramDetails) {
		this.paramDetails = paramDetails;
	}
	
	public void setreturnType(String returnType) {
		this.returnType = returnType;
	}
	public void setScope(String scope) {
		this.functionScope = scope;
	}
	
	public String getScope() {
		return this.functionScope;
	}
	
	public String getFunctionId() {
		return this.functionId;
	}
	
	public JSONArrayWrapper getParamDetails() {
		return this.paramDetails;
	}
	
	public String getReturnType() {
		return this.returnType;
	}
	public void setScript(String script) {
		this.script = script;
	}
	
	public String getScript() {
		return this.script;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	
	public String getDescription() {
		return this.description;
	}
	public String getZUID() {
		return this.zuid;
	}
}
