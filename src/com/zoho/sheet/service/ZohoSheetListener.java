//$Id$
package com.zoho.sheet.service;


import com.adventnet.collaboration.Collaboration;
import com.adventnet.ds.query.*;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.mfw.bean.BeanUtil;
import com.adventnet.mfw.message.MessageListener;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.sas.util.SASPersistenceUtil;
import com.adventnet.zoho.websheet.model.EngineFactory;
import com.adventnet.zoho.websheet.model.beans.JobSchedulerBean;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.store.StoreDataUtil;
import com.zoho.cache.cg.RedisConnectionPoolHandler;
import com.zoho.components.server.view.ViewBuilder;
import com.zoho.conf.Configuration;
import com.zoho.ear.dbencryptagent.DBEncryptAgent;
import com.zoho.ear.encryptagent.EncryptAgent;
import com.zoho.pns.api.PnsApi;
import com.zoho.sas.container.AppResources;
import com.zoho.sheet.conversion.ImportExportUtil;
import com.zoho.sheet.util.*;
//import com.zoho.sheet.conversion.ConversionServer;
import com.zoho.sheet.tools.HtmlListener;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.translation.ZTranslationSubscriptions;

import java.io.File;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Takes care of service specific handling upon user registration, modification
 * and deletion.
 */
public class ZohoSheetListener implements MessageListener {

        static Logger logger = Logger.getLogger(ZohoSheetListener.class.getName());

        public void onMessage(Object msg) {
                try {
                        logger.info("Server startup message :::" + msg);
                        if (msg != null) {
                                String message = msg.toString();
                                if (message.contains("start.type=cold")) {
                                        logger.info("COLD START!!!!");
                                        handleColdStart();
                                }
                                populateProps();
                                initializeSchedulers();

                        }
                } catch (Exception e) {
                        logger.log(Level.WARNING, "Exception Occured while starting ZohoSheetService :::", e);
//			throw new Exception("Exception Occured while starting ZohoSheetService");

                }
        }


        private static void createDataSpace(String[] dataSpaceName) throws Exception {
                try {
                        Collaboration c = (Collaboration) BeanUtil.lookup("CollaborationBean");
                        for (String dbName : dataSpaceName) {
                                if (!c.dataSpaceExists(dbName)) {
                                        c.reserveDataSpace(dbName);
                                } else {
                                        logger.log(Level.INFO, " Already populated", dataSpaceName );
                                }
                        }
                } catch (Exception e) {
                        logger.info("Exception occured while populating data Space dataSpaceName  :::" + dataSpaceName + " ::Exception ::" + e);
                        throw new Exception("Exception occured while populating data Space dataSpaceName  :::" + dataSpaceName);
                }
        }

        private static void createRemoteDataSpace() throws Exception {
        	try{
        		String[] REMOTE_USERS = Constants.REMOTE_USERS;
        		for (int i = 0; i < REMOTE_USERS.length; i++) {
        			logger.info("Checking DataSpaces:" + REMOTE_USERS[i]);
              		DocumentUtils.checkDataSpace(REMOTE_USERS[i], true);
           		}
           	}
           	catch(Exception e){
           		logger.info("Exception :" + e);
            }
    }

        private static void populateProps() {
                logger.info("-- Loading rebranding file and messages  --");
                try {
                        StoreDataUtil.parseStoreXml("/conf/websheet/stores.xml"); //NO I18N
                        
                        logger.info("-- Creating Redis Connection --");
                        setRedisConfiguration();
                        if (!"false".equalsIgnoreCase(Constants.rebrand_Properties.getProperty("allow_redis_validation"))) { //NO I18N
                                RedisConnectionPoolHandler.initialize(RedisHelper.REDIS_POOL_NAME, EngineConstants.REDIS_MAX_ACTIVE_CONNECTION, 2000);
                        } else {
                                logger.info("-- Redis Connection Validation Skipped --");
                        }
                        
                        logger.info("-- Poputaling error messages  --");
                        ClientUtils.loadErrorMessages();
                        
                        logger.info("-- Poputaling Rebranding messages  --");
                        ClientUtils.loadRebrandMessagesPath();
                        
                        logger.info("-- Poputaling Supported Locale   --");
                        ClientUtils.loadSupportedLocales();
                        
                        logger.info("-- Poputaling Rebranding Properties   --");
                        ClientUtils.loadRebrandProperties();
                        
//                        LOGGER.info("-- Poputaling Combinations.json   --");
//                        ClientUtils.loadCombinationsFile();
                        
//                        LOGGER.info("-- Poputaling Features List   --");
//                        FeatureController.readFeatureConfigFiles();
                        
                        logger.info("-- Poputaling Html Version   --");
                        ClientUtils.loadHTMLVersionPath();
                        
                        logger.info("-- Triggering Word Forms Loading --");
                        ClientUtils.loadWordFormsPath();
                        
                        ViewFactoryImpl viewFactory = new ViewFactoryImpl();
                        viewFactory.setClassesPath(Configuration.getString("app.home") + File.separator + "classes" + File.separator);//NO I18N
                        ViewBuilder.init(viewFactory);
                        
                        logger.info("-- Loading Conversion servers settings --");
//                        try{
//                        	if(!Constants.IS_DISASTER_RECOVERY_SERVER) {
//                        		ConversionServer.verifyConversionServers();
//                        	}
//						} catch (Exception cse) {
//			                        	logger.log(Level.WARNING, "Problem while updating Conversion Server details.", cse);
//			            }
                        
                        if("true".equalsIgnoreCase(EnginePropertyUtil.getSheetPropertyValue("ENABLE_JSON_WATCHER"))) {
                                logger.info("-- Poputaling scripts jsp files  --");
                                //JsonListener.generateIncludes();
                                logger.info("-- Creating HTML Locale Files --");
                                HtmlListener.generate();
                        }
                        
                        logger.info("-- Setting EncryptAgent AuthToken --");
                        String refreshToken = EnginePropertyUtil.getSheetPropertyValue("EAR_REFRESH_TOKEN");	//No I18N
                        try {
                        	String SECURITY_CONTEXT=AppResources.getProperty("server.dir");
                        	String[] SECURITY_FILES = new String[]{
                        			SECURITY_CONTEXT + "/security-properties.xml", SECURITY_CONTEXT + "/conf/security-privatekey.xml"	//No I18N
                        	};
                        	SecurityUtil.initSecurityConfiguration(SECURITY_FILES);
                        	EncryptAgent.setAuthToken(refreshToken);
                        } catch (Exception e) {
                			logger.log(Level.WARNING, "Problem while Setting EncryptAgent AuthToken.", e);
                		}
                        
                        try {
                        	DBEncryptAgent.setEARRefreshToken(refreshToken);
                        } catch(Exception e) {
                        	logger.log(Level.SEVERE, "Exception while setting EAR RefreshToken" , e);
                        }
				        try
					{
						com.zlabs.codeminer.stringtransform.Configuration.populateStringTransformProperties();
					}
					catch(Exception e)
					{
						logger.log(Level.SEVERE, "[PATTERNFILL][Exception] Exception while loading patternFill properties." , e);
					}
                        
                } catch (Exception e) {
                        logger.log(Level.WARNING, "-- Exception occured while populating properties file ::: --", e);
                }
                
               // Migrated to PNS 
                /*try {
                	LOGGER.log(Level.INFO, "-- [ACS] Started");
//                	AcsApi.initialize();
                	LOGGER.log(Level.INFO, "-- [ACS] End");
                } catch (Exception e) {
					// TODO: handle exception
                	
                	LOGGER.log(Level.WARNING, "-- [ACS]Exception occured while initializing acs properties file ::: --", e);
				}
				*/
              //Initializing PNSAPI for Push Notifications
                try {
                	logger.log(Level.INFO, "-- [PNS] Started");
                	PnsApi.initialize();
                	logger.log(Level.INFO, "-- [PNS] End");
                } catch (Exception e) {
					// TODO: handle exception
                	
                	logger.log(Level.WARNING, "-- [PNS]Exception occured while initializing pns properties file ::: --", e);
				}
        }

        private static void initializeSchedulers(){
        	EngineFactory.startSaveAndWebhookScheduler();
            ZTranslationSubscriptions.initializeSubscription();
        }

        public static void setRedisConfiguration() {
            try {
                String redisIP = RemoteUtils.maskNull(EnginePropertyUtil.getSheetPropertyValue("SHEET_REDIS_IP")).trim();// != null && !EnginePropertyUtil.getSheetPropertyValue("SHEET_REDIS_IP").isEmpty()) ? EnginePropertyUtil.getSheetPropertyValue("SHEET_REDIS_IP") : null; // No I18N
                String redisUpdateNeed = RemoteUtils.maskNull(EnginePropertyUtil.getSheetPropertyValue("REDIS_CONFIGUARATION_NEEDED")).trim();// != null && !EnginePropertyUtil.getSheetPropertyValue("SHEET_REDIS_IP").isEmpty()) ? EnginePropertyUtil.getSheetPropertyValue("SHEET_REDIS_IP") : null; // No I18N
                if (redisIP != null && redisUpdateNeed.equalsIgnoreCase("true")) {
                        SelectQueryImpl websql = new SelectQueryImpl(new Table("GridConfiguration"));
                        Criteria updateCri = new Criteria(new Column("GridConfiguration", "PROPNAME"), "redis-server-ip", QueryConstants.EQUAL); // No I18N
                        websql.addSelectColumn(new Column(null, "*"));
                        websql.setCriteria(updateCri);
                        Persistence pers = SASPersistenceUtil.getPersistence();
                        DataObject webDO = pers.get(websql);
                        if (webDO != null) {
                                logger.info("PROPVAL ::" + webDO.getRow("GridConfiguration").get("PROPVAL"));
                                String oldRedisIP = (String) webDO.getRow("GridConfiguration").get("PROPVAL");
                                if (oldRedisIP.equals(redisIP)) {
                                        return;
                                }
                                UpdateQuery uq = new UpdateQueryImpl("GridConfiguration"); // No I18N
                                uq.setUpdateColumn("PROPVAL", redisIP); // No I18N
                                uq.setCriteria(updateCri);
                                pers.update(uq);
                                logger.info("-- GridConfiguration updated   --");
                        } else {
                                logger.info("-- GridConfiguration NOT Found  --");
                        }
                }
            } catch (Exception e) {
                logger.info("-- Exception occured for GridConfiguration  --" + e);
            }
        }

        private static void handleColdStart() throws Exception {
                logger.info(":::::::handle Cold Start:::::::");
                
                // String[] dataSpaceName = {"iamdb", "Public", "searchdb", "EmailShared", "I18N", "GroupShared", "remoteapi"};	//No I18N
                //String[] dataSpaceName = {"Public"};    //No I18N

                String[] dataSpaceName = (EnginePropertyUtil.getSheetPropertyValue("SHEET_DATASPACES")).split(","); //NO I18N
                logger.info("::::::: Populating public space :::::::");
                createDataSpace(dataSpaceName);
                
                logger.info("::::::: Populating Remote spaces :::::::");
                createRemoteDataSpace();

                logger.info("::::::: Updating SheetSystemTask[schedular] :::::::");
                try {
                        String[] schedulers = {"DeleteRemoteDoc", "com.adventnet.zoho.websheet.task.DeleteRemoteDoc", "1hr-webData", "Scheduled for every one hour", "DeleteAuthRemoteDoc", "com.adventnet.zoho.websheet.task.DeleteRemoteDoc", "12hr-webData", "Scheduled for every twelve hour", "FetchStockData", "com.adventnet.zoho.websheet.task.FetchStockData", "1hr-webData", "Scheduled for every hour", "SaveWorkBooks", "com.adventnet.zoho.websheet.task.SaveWorkbooks", "1hr-webData", "Scheduled for every 1 hour", "PublicGraphsSchedular", "com.adventnet.zoho.websheet.task.PublicGraphsSchedular", "2min-webData", "Scheduled for every two minute","ConversionRetry","com.adventnet.zoho.websheet.task.DeleteRemoteDoc","1hr-webData","Scheduled for every hour", "PublicChartUpdater", "com.adventnet.zoho.websheet.task.PublicChartUpdater", "2min-webData", "Scheduled for every 2 minutes to regenerate modified charts."}; //No I18N
                        Persistence pers = SheetPersistenceUtils.getPersistence("Public");//No I18N
                        DataObject dObj = null;
                        for (int i = 0; i < schedulers.length; i += 4) {
                                dObj = pers.constructDataObject();
                                Row row = new Row("SheetSystemTask");
                                row.set("TASK_NAME", schedulers[i]);//No I18N 
                                row.set("CLASS_NAME", schedulers[i + 1]);//No I18N 
                                row.set("DESCRIPTION", schedulers[i + 3]);//No I18N 
                                dObj.addRow(row);
                                dObj = pers.add(dObj);
                                String TASK_NAME = schedulers[i];
                                String CLASS_NAME = schedulers[i + 1];
                                String SCHEDULE_NAME = schedulers[i + 2];
                                long TASK_ID = (Long) dObj.getValue("SheetSystemTask", "TASK_ID", dObj.getRow("SheetSystemTask"));//No I18N 
                                logger.info("\nTASK_NAME: " + TASK_NAME + "\nSCHEDULE_NAME: " + SCHEDULE_NAME + "\nCLASS_NAME" + CLASS_NAME + "\nTASK_ID: " + TASK_ID);
                                JobSchedulerBean JSbean = (JobSchedulerBean) BeanUtil.lookup("JobSchedulerBean", "Public");//No I18N
                                JSbean.addorUpdateJob(TASK_ID, SCHEDULE_NAME, "Public", true, CLASS_NAME);	//No I18N 
                        }
                } catch (Exception e) {
                        logger.info("Exception while populating default schedule for stock data..." + e);
                }

                //for default apikey
                try {
                        Persistence pers = (Persistence) BeanUtil.lookup("Persistence", "Public");
                        DataObject apiDO = pers.constructDataObject();
                        Row apiProfileRow = new Row("APIKeyProfile");
                        apiProfileRow.set("USER_NAME", "rameshs");				       //No I18N
                        apiProfileRow.set("EMAIL_ID", "<EMAIL>");	       //No I18N
                        apiProfileRow.set("COMPANY", "adventnet");				       //No I18N
                        apiProfileRow.set("USAGE", 0);				                   //No I18N
                        apiProfileRow.set("ENTRY_TIME", System.currentTimeMillis());				//No I18N
                        apiDO.addRow(apiProfileRow);
                        Row apiKeyRow = new Row("APIKey");
                        apiKeyRow.set("API_KEY_ID", apiDO.getRow("APIKeyProfile").get("API_KEY_ID"));				//No I18N
                        apiKeyRow.set("API_KEY", "4224158ffca02072c1db6f41f7c3adac");				//No I18N
                        apiKeyRow.set("ACTIVE", true);				//No I18N
                        apiDO.addRow(apiKeyRow);
                        pers.update(apiDO);
                } catch (Exception e) {
                        logger.info("Exception while populating default api key..." + e);
                }
//                try{
//                	String groupName = EnginePropertyUtil.getSheetPropertyValue("CONVERSION_SERVER_GROUP_NAME"); //No I18N
//        			Persistence persistence = SheetPersistenceUtils.getPersistence("Public"); //No I18N
//        			int count=Integer.parseInt(EnginePropertyUtil.getSheetPropertyValue("ooNum")); //No I18N
//                	for(int i=0;i<count;i++)
//                	{
//                		String ip=EnginePropertyUtil.getSheetPropertyValue("openoffice.server."+i); //No I18N
//                		String port=EnginePropertyUtil.getSheetPropertyValue("openoffice.port."+i); //No I18N
//                		DataObject dobj = persistence.constructDataObject();
//    					Row row = new Row("ConversionServers"); //No I18N
//    					row.set("IP", ip); //No I18N
//    					row.set("PORT", port); //No I18N
//    					row.set("GROUPNAME", groupName); //No I18N
//    					row.set("LAST_MODIFIED_TIME", System.currentTimeMillis()); //No I18N
//    					dobj.addRow(row);
//    					persistence.add(dobj);
//    						
//                	}
//                }catch(Exception e)
//                {
//                	logger.log(Level.WARNING,"problem in instatiating",e);
//                }

                logger.info("::::: cold start finished :::::");
        }

        public static String getOperation(int type) {
                switch (type) {
                        case 1:
                                return "ADD";//No I18N
                        case 2:
                                return "UPDATE";//No I18N
                        case 3:
                                return "DELETE";//No I18N
                }
                return "ALL";//No I18N
        }



}
