/* $Id$ */
package com.zoho.sheet.converter;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.InterruptedIOException;
import java.io.OutputStreamWriter;
import java.io.PipedInputStream;
import java.io.PipedOutputStream;
import java.io.PrintWriter;
import java.io.Reader;
import java.io.Writer;
import java.util.StringTokenizer;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.xmlpull.v1.XmlPullParserException;

import com.zoho.sheet.parse.sxc.Struct;
import com.zoho.sheet.parse.sxc.SxcEventListener;
import com.zoho.sheet.parse.sxc.SxcWorkbookParser;

/**
 * Author: tthi<PERSON><PERSON><PERSON>
 * Date: Jul 27, 2007
 *
 * To extract details from .sxc file for Lucene indexing.
 * Right now, sheet names, cell text values and cell comments are extracted..
 *
 */
//public class LuceneBookTransformer implements SxcEventListener
//{
//	public static Logger logger = Logger.getLogger(LuceneBookTransformer.class.getName());
//    public static void main(String[] args) throws IOException
//    {
//        Reader reader = LuceneBookTransformer.transform(new FileInputStream(args[0]), Long.valueOf("123"));//No I18N
//        PrintWriter myout = new PrintWriter(new FileOutputStream("lucene.txt"));//No I18N
//        char[] buff = new char[100];
//        int size = -1;
//        while ((size = reader.read(buff)) != -1)
//        {
//            myout.write(buff, 0, size);
//        }
//        System.in.read();
//        reader.close();
//        myout.close();
//    }
//
//    /**
//     * Just overrided close() method - so that we will get control when Lucene closes the PipedReader stream
//     */
//    private static class MyPipedInStream extends PipedInputStream
//    {
//        Thread readerThread;
//
//        public void setReaderThread(Thread readerThread)
//        {
//            this.readerThread = readerThread;
//        }
//
//        public void close() throws IOException
//        {
//            super.close();
//            if (readerThread.isAlive())
//            {
//                readerThread.interrupt();
//            }
//        }
//    }
//
//    private Writer pipedWriter;
//    private SxcWorkbookParser parser;
//    private int wordCount;
//    private Long bookId;
//    private static final char DELIMITER = ' ';
//    private static final int MAX_LENGTH =
//            (Integer.parseInt(System.getProperty("org.apache.lucene.maxFieldLength", "30000")) * 5) / 6;
//
//    /**
//     * @param writer The parsed details will be written to this writer
//     */
//    private LuceneBookTransformer(Long bookId, Writer writer)
//    {
//        this.bookId = bookId;
//        this.pipedWriter = writer;
//    }
//
//    /**
//     * This method will start a seperate thread and dump the output to a 'PipedOutputStream'. Using the 'Reader' object
//     * returned by this method, one can read the piped output
//     * <p> Make sure to close the returned 'Reader' once reading is done !!
//     * @param sxcIn InputStream of the .sxc file which needs to be parsed for Lucene indexing
//     * @return The Reader for reading the piped output details
//     * @throws IOException
//     */
//    public static Reader transform(final InputStream sxcIn, final Long bookId) throws IOException
//    {
//        MyPipedInStream pipeIn = new MyPipedInStream();
//        final OutputStreamWriter pipedWriter = new OutputStreamWriter(new PipedOutputStream(pipeIn));
//
//        // start parsing the .sxc in a seperate thread
//        // -------------------------------------------
//        Thread readerThread = new Thread("Lucene-" + bookId)//No I18N
//        {
//            public void run()
//            {
//                // LOG.info(getName() + " - START");
//                LuceneBookTransformer trans = new LuceneBookTransformer(bookId, pipedWriter);
//                try
//                {
//                    trans.parser = new SxcWorkbookParser(trans, true);
//                    trans.parser.parse(sxcIn);
//                }
//                catch (XmlPullParserException e)
//                {
//                    logger.log(Level.SEVERE, "XPP Error in workbook " + trans.bookId, e);
//                }
//                catch (IOException e)
//                {
//                    logger.log(Level.SEVERE, "IO Error(.sxc stream) in workbook " + trans.bookId, e);
//                }
//                finally
//                {
//                    try
//                    {
//                        trans.pipedWriter.close();
//                    }
//                    catch (IOException e)
//                    {
//                        logger.log(Level.WARNING, "Stream closing error in workbook " + trans.bookId, e);
//                    }
//                }
//                // LOG.info(getName() + " - END");
//            }
//        };
//        pipeIn.setReaderThread(readerThread);
//        readerThread.start();
//        return new InputStreamReader(pipeIn);
//    }
//
//    // SxcEventListener interface implementation -> START
//
//    public void startSheet(Struct.Sheet sheet)
//    {
//        if (!writeToPipe(sheet.sheetName))
//        {
//            parser.stop();
//            return;
//        }
//    }
//
//    public void endSheet(Struct.Sheet sheet)
//    {
//        // do nothing
//    }
//
//    public void updateCell(Struct.Sheet sheet)
//    {
//        if (!writeToPipe(sheet.row.cell.text))
//        {
//            parser.stop();
//            return;
//        }
//        if (!writeToPipe(sheet.row.cell.comment))
//        {
//            parser.stop();
//            return;
//        }
//    }
//
//    /**
//     * Method for writing a content to PipedWriter. If MAX_LENGTH is reached, content won't be written to the 'pipe'
//     * @param content The content to be written to the 'pipe'
//     * @return true, if the content is written to the 'pipe', false otherwise
//     */
//    private boolean writeToPipe(String content)
//    {
//        if (content == null)
//        {
//            return true;
//        }
//        wordCount += new StringTokenizer(content, " \n\t\r\f,.:;@&-_()[]{}/\\=?").countTokens();//No I18N
//        if (wordCount > MAX_LENGTH)
//        {
//            logger.info("MAX_LENGTH(" + MAX_LENGTH + ")reached for workbook " + bookId);
//            return false;
//        }
//        try
//        {
//            pipedWriter.write(content + DELIMITER);
//        }
//        catch (InterruptedIOException ioE)
//        {
//            logger.info("INTERRUPT - " + bookId + " ; WordCount - " + wordCount);
//            return false;
//        }
//        catch (IOException e)
//        {
//            logger.log(Level.WARNING, "Write(content) error in workbook " + bookId, e);
//            return false;
//        }
//        return true;
//    }
//
//    public void startRow(Struct.Sheet sheet)
//    {
//        // do nothing
//    }
//
//    public void endRow(Struct.Sheet sheet)
//    {
//        // do nothing
//    }
//
//    public void updateDummyCell(Struct.Sheet sheet)
//    {
//        // do nothing
//    }
//
//    public void updateFontStyle(Struct.Font font)
//    {
//        throw new UnsupportedOperationException("Method updateFontStyle() is not expected to be invoked..");//No I18N
//    }
//
//    public void updateColumnStyle(Struct.Style style)
//    {
//        throw new UnsupportedOperationException("Method updateFontStyle() is not expected to be invoked..");//No I18N
//    }
//
//    public void updateRowStyle(Struct.Style style)
//    {
//        throw new UnsupportedOperationException("Method updateFontStyle() is not expected to be invoked..");//No I18N
//    }
//
//    public void updateCellStyle(Struct.Style style)
//    {
//        throw new UnsupportedOperationException("Method updateFontStyle() is not expected to be invoked..");//No I18N
//    }
//
//    public void updateColumnHeader(Struct.Sheet sheet)
//    {
//        throw new UnsupportedOperationException("Method updateFontStyle() is not expected to be invoked..");//No I18N
//    }
//
//    // SxcEventListener interface implementation <- END
//}