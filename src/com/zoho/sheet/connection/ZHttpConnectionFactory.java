/*  $Id$ */
package com.zoho.sheet.connection;

import java.net.Proxy;
import java.net.URL;
import java.util.logging.Logger;

import javax.net.ssl.HttpsURLConnection;

public class ZHttpConnectionFactory {
	public static Logger logger = Logger.getLogger(ZHttpConnectionFactory.class.getName());
	String urlString = null;

	public ZHttpConnectionFactory(String url) {
		this.urlString = ConnectionUtils.maskNull(url);
	}

	public ZHttpConnection getHttpUrlConnection(Proxy proxy) throws Exception {
		URL url = new URL(this.urlString);
		if (url.openConnection() instanceof HttpsURLConnection) {
			return new ZHttpsUrlConnection(this.urlString,proxy);
		} else {
			return new ZHttpUrlConnection(this.urlString,proxy);
		}
	}
}
