/*  $Id$ */
package com.zoho.sheet.connection;

import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.accounts.AccountsConstants;
import com.zoho.sas.container.AppResources;
import com.zoho.security.agent.MultipartFile;
import com.zoho.security.api.wrapper.URLWrapper;
import com.zoho.sheet.util.RemoteUtils;
import org.apache.tika.Tika;
import org.apache.tika.mime.MimeTypes;

import javax.net.ssl.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLConnection;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ZSheetUrlWrapperConnection implements ZSConnection {
	public static Logger log = Logger.getLogger(ZSheetUrlWrapperConnection.class.getName());
	private String crlf = "\r\n";// No I18N
	private String charset = "UTF-8";// No I18N
	private int connect_timeout = 120000;
	private int read_timeout = 120000; // Can be reset by setter method
	private String boundary = ""+System.currentTimeMillis();
	private String strUrl = null;
	private String response = null;
	private int responseCode = -1;
	private long responseLength = -1;
	private String pPort = ConnectionUtils.maskNull(AppResources.getProperty("writer.proxy.port"));
	private String pHost = ConnectionUtils.maskNull(AppResources.getProperty("writer.proxy.host"));
	private String pUserName = ConnectionUtils.maskNull(AppResources.getProperty("writer.proxy.username"));
	private String pPassword = ConnectionUtils.maskNull(AppResources.getProperty("writer.proxy.password"));
	private boolean isAccetpJson = false;
//	private Object paramName[] = null;
//	private Object paramValue[] = null;
	private String credentails = null;
	private String contentLength = null;
	private Proxy proxy = null;
	private String remoteIp = null;
	private String remoteIpWithSignature = null;
	private String zUserAgent = null;
	private String content_type = "application/x-www-form-urlencoded"; // Can be reset by setter method //No I18N
	private String multiPart_content = "multipart/form-data"; // Can be reset by setter method //No I18N
	private List<File> uploadFileList = new ArrayList<File>();
	private boolean isMultiPartFormPost = false;
	private HttpsURLConnection httpsUrlConnection = null;
	private HttpURLConnection httpUrlConnection = null;
//	private DataOutputStream dataOutputStream = null;
	private static SSLSocketFactory factory = null;
	private String writeToOutputStream = null;
	private byte[] writeByteToOutputStream = null;
	private HashMap responseMap = new HashMap();
	private boolean boundyCloseNeed = false;
	private byte[] byteStream = null;
    private OutputStream outputStream = null;
    private FileInputStream fin = null;
    private String sslType = "TLS";// No I18N
    private String contentEncodingType = null;
    private InputStream  connectionInputStream = null;
//    private boolean useURLWrapper = false;
    private String[] headerKeys = null;
    private String[] headerValues = null;
    private URLWrapper urlWrapper=null;
    Map<String, String> params =new HashMap<String, String>();
    List<MultipartFile> files = new ArrayList<MultipartFile>();
    private String oAuthHeader = null;
    private byte[] fileBytes = null;
	private String fileName = null;
	private String contentParamName = "content";//No I18N
	private String requestMethodType = null;
	private boolean readOutput = true;
	private long fileSizeLimit = -1;
	private  boolean fileSizeExceeded = false;

	public ZSheetUrlWrapperConnection(String url) throws IOException {
		this.strUrl = url;
		String tempUrl = ConnectionUtils.maskNull(EnginePropertyUtil.getSheetPropertyValue("testUrl")); // No I18N
		if (tempUrl != null) {
			this.strUrl = tempUrl;
		}
	}
	public ZSheetUrlWrapperConnection(String url, String requestMethodType, boolean readOutput) throws IOException {
		this.strUrl = url;
		this.requestMethodType = requestMethodType;
		this.readOutput = readOutput;
	}

	@Override
	public void setRemoteIp(String ip) {
		this.remoteIp = ip;
	}
	public void setRemoteIpWithSignature(String ip) {
		this.remoteIpWithSignature = ip;
	}
	public void setZUserAgent(String zUserAgent) { this.zUserAgent = zUserAgent; }
	@Override
	// A setter to adding extra parameter for the request
	public void setParameter(final Object[] paramNameArray, final Object paramValueArray[]) {
		 if( paramNameArray != null && paramNameArray.length >0 ){
			 for(int i= 0; i < paramNameArray.length;i++){
				 String _paramName  = RemoteUtils.maskNull(paramNameArray[i].toString());
				 String _paramValue = RemoteUtils.maskNull(paramValueArray[i].toString());
				 if(_paramName != null && _paramValue !=null) {

				  this.params.put(_paramName, _paramValue);
				 }
			 }
		 }
	}


	@Override
	// setter to add file as part
	public void setFilePart(File file) {
		if (file != null && file.isFile()) {
			uploadFileList.add(file);
			log.log(Level.INFO, "File Part Added ...");
			this.isMultiPartFormPost = true;
		}
	}

	@Override
	// Setting Connection Time out explicitly
	public void setConnectionTimeout(int connectionTimeOut) {
		if (connectionTimeOut > 0) {
			this.connect_timeout = connectionTimeOut;
		}
	}

	@Override
	// Setting Connection Read Time out explicitly
	public void setReadTimeout(int readTimeOut) {
		if (readTimeOut > 0) {
			this.read_timeout = readTimeOut;
		}
	}

	@Override
	// setting Content-type property for the request
	public void setContentType(String contentType) {
		contentType = ConnectionUtils.maskNull(contentType);
		if (contentType != null) {
			this.content_type = contentType;
		}
	}

	@Override
	// setter to set Content Length for the request
	public void setContentLength(String contentLength) {
		contentLength = ConnectionUtils.maskNull(contentLength);
		if (contentLength != null) {
			this.contentLength = contentLength;
		}
	}

	@Override
	// Setting proxy Hostname
	public void setProxy(String hostName, String portNumber, String userName, String pass) {
		hostName = ConnectionUtils.maskNull(hostName);
		if (hostName != null) {
			this.pHost = hostName;
		}
		portNumber = ConnectionUtils.maskNull(portNumber);
		if (portNumber != null) {
			this.pPort = portNumber;
		}
		userName = ConnectionUtils.maskNull(userName);
		if (userName != null) {
			this.pUserName = userName;
		}
		pass = ConnectionUtils.maskNull(pass);
		if (pass != null) {
			this.pPassword = pass;
		}
	}

	@Override
	// setting authentication
	public void setAuthentication() {
		try {
			if (this.pUserName != null && this.pPassword != null) {
				this.credentails = Base64.getEncoder().encodeToString((this.pUserName + ":" + this.pPassword).getBytes("UTF-8"));
			}
		} catch (Exception e) {
			log.info("Exception occure while setting authentication for httpUrlConnection :"+ e);// No I18N
			ConnectionUtils.printLog(e);
		}
	}
    @Override
    public void setJsonAccept(){
    	this.isAccetpJson = true;
    }
	
	public void addParameter() {
	}
	
	
	@Override
	public void setWrittingBytes(String writeToOutPutStream) {
		writeToOutPutStream = ConnectionUtils.maskNull(writeToOutPutStream);
		if (writeToOutPutStream != null) {
			this.writeToOutputStream = writeToOutPutStream;
		}
	}
	
	@Override
	public void setWrittingByteArray(byte[] byteArrayStream) {
		if (byteArrayStream != null && byteArrayStream.length >0) {
			this.writeByteToOutputStream = byteArrayStream;
		}
	}
	

	@Override
	public boolean initConnection() {
		this.addProxy();
		this.setAuthentication();
		return true;
	}

	@Override
	// Getting response
	public void process() {
		this.initConnection();
		FileInputStream inputStream = null;
		try {
			
			this.urlWrapper = new URLWrapper(this.strUrl,true);
			if (this.credentails != null) {
				this.urlWrapper.setRequestProperty("Authorization","Basic " + credentails);// No I18N
			}else if(this.oAuthHeader!=null){
				this.urlWrapper.setRequestProperty("Authorization",this.oAuthHeader);//No I18N
			}
			this.urlWrapper.setConnectTimeout(connect_timeout);
			this.urlWrapper.setReadTimeout(read_timeout);
			this.urlWrapper.setRequestProperty("Accept-charset",charset);// No I18N
			if(this.isAccetpJson){
				this.urlWrapper.setRequestProperty("Accept", "application/json");// No I18N
            }
			this.urlWrapper.setUseCaches(false);
			this.urlWrapper.setDoInput(true);
			this.urlWrapper.setDoOutput(true);
			if(this.requestMethodType != null) {
			    this.urlWrapper.setRequestMethod(this.requestMethodType);
			}
			else {
				this.urlWrapper.setRequestMethod("POST");// No I18N	
			}
			this.urlWrapper.setRequestProperty("User-Agent", "ZohoSheet");// No I18N

			// Content-length will set only iff its set by setter
			if (this.contentLength != null) {
				this.urlWrapper.setRequestProperty("Content-length", this.contentLength);// No I18N
			}
			if(this.remoteIp != null){
				this.urlWrapper.setRequestProperty("REMOTE_USER_IP",this.remoteIp);// No I18N
			}
			if(this.remoteIpWithSignature !=null){
				this.urlWrapper.setRequestProperty("Z-SIGNED_REMOTE_USER_IP",this.remoteIpWithSignature); //No I18N
			}
			if(this.zUserAgent != null) {
				this.urlWrapper.setRequestProperty(AccountsConstants.Z_USERAGENT, this.zUserAgent);
			}
			if(this.headerKeys != null && this.headerValues !=null) {
				for( int headerIndex =0;  headerIndex<  this.headerKeys.length; headerIndex++) {
					if(this.headerValues[headerIndex] !=null) {
						this.urlWrapper.setRequestProperty(this.headerKeys[headerIndex],this.headerValues[headerIndex] );
					}
				}
			}
			if( this.params != null  && this.params.size() > 0 ){
//				String _paramStr = getParameterAsString(params);
				this.urlWrapper.setDoOutput(true);
				this.urlWrapper.setMultipartParams(this.params);
				
				
			}
			
			if (! this.isMultiPartFormPost) {
				// this.urlWrapper.setRequestProperty("Content-Type",this.multiPart_content + "; boundary=" + boundary);// No I18N
			// } else {
				this.urlWrapper.setRequestProperty("Content-type",this.content_type);// No I18N
			}
			
			
			if (this.uploadFileList != null && !this.uploadFileList.isEmpty()) {
				Iterator<File> fileItr = this.uploadFileList.iterator();
				while (fileItr.hasNext()) {
					File _file = fileItr.next();
					MultipartFile mFile = new MultipartFile(this.contentParamName,_file, URLConnection.guessContentTypeFromName(_file.getName())); 
					this.files.add(mFile);
				}
				if(this.files.size() >0)
				{
					this.urlWrapper.setMultipartFiles(files);
					
				}
			}
			
			if (this.fileBytes != null && this.fileBytes.length>0) {
				File _tempFile = new File(this.fileName);
		         try (FileOutputStream fos = new FileOutputStream(_tempFile)) {
		        	    fos.write(this.fileBytes);
		        	    MultipartFile _mFile = new MultipartFile("datafile",_tempFile, URLConnection.guessContentTypeFromName(this.fileName )); //NO I18N
						this.files.add(_mFile);
		        	} catch (IOException ioe) {
		        	    // ioe.printStackTrace();
		        	    log.log(Level.INFO, "Exception ...",ioe);
		        	}
		    }
			
			if (this.writeToOutputStream != null) {
				this.urlWrapper.setPostData(this.writeToOutputStream);
			}
			if(this.writeByteToOutputStream != null){
				 
				 this.urlWrapper.setPostData(this.writeByteToOutputStream);
			}
			//if (this.urlWrapper.openURLConnection() instanceof HttpsURLConnection) {
			if (this.strUrl.contains("https://")) {
				if (this.proxy != null) {
					this.httpsUrlConnection = (HttpsURLConnection) this.urlWrapper.openURLConnection(this.proxy);
				} else {
					this.httpsUrlConnection = (HttpsURLConnection) this.urlWrapper.openURLConnection();
				}
				
			} else {
				
				if (this.proxy != null) {
					this.httpUrlConnection = (HttpURLConnection) this.urlWrapper.openURLConnection(this.proxy);
				} else {
					this.httpUrlConnection = (HttpURLConnection) this.urlWrapper.openURLConnection();
				}
			}
			if(this.readOutput){
				if (this.httpUrlConnection != null) {
					this.responseCode = this.httpUrlConnection.getResponseCode();
					this.response = this.httpUrlConnection.getResponseMessage();
					if(this.responseCode == 200 || this.responseCode == 201 || this.responseCode == 206){
						setByteInputStream(this.httpUrlConnection.getInputStream());
						this.connectionInputStream = this.httpUrlConnection.getInputStream();
					}
					else{
						setByteInputStream(this.httpUrlConnection.getErrorStream());
					}
					this.contentEncodingType = this.httpUrlConnection.getContentEncoding();
				} else {
					this.response = this.httpsUrlConnection.getResponseMessage();
					this.responseCode = this.httpsUrlConnection.getResponseCode();
					if(this.responseCode ==200 || this.responseCode ==201|| this.responseCode == 206){
						setByteInputStream(this.httpsUrlConnection.getInputStream());
						this.connectionInputStream = this.httpsUrlConnection.getInputStream();
					}
					else{
						setByteInputStream(this.httpsUrlConnection.getErrorStream());
					}
					this.contentEncodingType = this.httpsUrlConnection.getContentEncoding();
				}
				if(this.response != null) {
					this.responseLength  =  this.response.length();
				}
				if( this.byteStream != null ){
					this.responseMap.put("RESP_MESSAGE", new String(this.byteStream, "UTF-8"));
				}
				this.responseMap.put("REQ_URL",   this.strUrl);
				this.responseMap.put("RESP_CODE", this.responseCode);
				this.responseMap.put("RESP_MSG",  this.response);
				this.responseMap.put("RESP_SIZE", this.responseLength);
				this.responseMap.put("RESP_CONTENT_TYPE",this.content_type);
			}

		} catch (Exception e) {
			log.log(Level.INFO,"[ZSException] Exception occured while getting response::::::{0}",e);
			ConnectionUtils.printLog(e);
		}

		finally { 
                        try{
							if(this.readOutput){
								if (this.httpUrlConnection != null) {
									this.httpUrlConnection.disconnect();
								}
								if (this.httpsUrlConnection != null) {
									this.httpsUrlConnection.disconnect();
								}
							}

			                if (inputStream != null) {
                                    inputStream.close();
                            }
                            
                            if(this.outputStream != null){
                              this.outputStream.close();
                            }
                            if(this.fin != null){
                                this.fin.close();
                            }
			} catch (Exception e) {
				log.log(Level.INFO,"[ZSException]  Finaly block Exception occured while getting response::::::");
				ConnectionUtils.printLog(e);
			}

		}
	}
	public Object getURLConnectionObj(){
		if(this.httpUrlConnection !=null){
			return this.httpUrlConnection;
		}else if(this.httpsUrlConnection !=null){
			return this.httpsUrlConnection;
		}
		return null;
	}


	static synchronized SSLSocketFactory prepFactory( String sslType) throws NoSuchAlgorithmException, KeyStoreException,KeyManagementException {
		if (factory == null) {
			SSLContext ctx = SSLContext.getInstance(sslType);
			ctx.init(null, new TrustManager[] { new AlwaysTrustManager() },null);
			factory = ctx.getSocketFactory();
		}
		return factory;
	}
	
	 private static String getParameterAsString(Map<String, String> params){
         String res = "";
         for(Entry<String,String> param : params.entrySet()){
                 res += param.getKey() + "=" + param.getValue() + "&";
         }
         res = res.length() > 0 ? res.substring(0,res.length()-1) : res;
         return res;
 }

	@Override
	public void setSSLFactoryType(String sslType) {
		String tempSSL = ConnectionUtils.maskNull(sslType);
		if( tempSSL  !=  null) {
			this.sslType = tempSSL;
		}
	}

	@Override
	public void setRawData(String rawData) {

	}


	private static class AlwaysTrustManager implements X509TrustManager {
		public void checkClientTrusted(X509Certificate[] arg0, String arg1)throws CertificateException {
		}

		public void checkServerTrusted(X509Certificate[] arg0, String arg1)throws CertificateException {
		}

		public X509Certificate[] getAcceptedIssuers() {
			return null;
		}
	}

	@Override
	// Getter started from here
	public long getContentLength() {
		long fSize = -1;
			try {
				if (this.urlWrapper  != null) {
					fSize = this.urlWrapper.openURLConnection().getContentLength();
				}
			} catch (Exception e) {
				log.info(" Exception occurred while getting content Length from urlConnection :"+ e);// No I18N
				ConnectionUtils.printLog(e);
			}
		return fSize;
	}

	@Override
	public HashMap getHeaderMap() {
		HashMap resMap = new HashMap();
		HashMap headerMap = new HashMap();
		if (this.responseCode != 0) {
			try {
				Map responseMap = null;
				if (this.httpUrlConnection != null) {
					responseMap = this.httpUrlConnection.getHeaderFields();
				} else if (this.httpsUrlConnection != null) {
					responseMap = this.httpsUrlConnection.getHeaderFields();
				}
				if (responseMap != null) {
					for (Iterator iterator = responseMap.keySet().iterator(); iterator.hasNext();) {
						String key = (String) iterator.next();
						List values = (List) responseMap.get(key);
						String value = null;
						for (int i = 0; i < values.size(); i++) {
							Object o = values.get(i);
							value = value == null ? (String) o : value+ (String) o;
						}
						resMap.put(key, value);
					}
				}
				HashMap fileDetailsMap = ConnectionUtils.getFileDetails(resMap);
				headerMap.putAll(fileDetailsMap);
				log.info("HASHMAP: " + headerMap);// No I18N
			} catch (Exception e) {
				log.log(Level.WARNING, null, e);
				ConnectionUtils.printLog(e);
			}
		}
		return headerMap;
	}

	@Override
	public int getResponseCode() {
		return this.responseCode;
	}

	@Override
	public String getResponseMessage() {
		return this.response;
	}

	@Override
	public long getResponseLength() {
		return this.responseLength;
	}

	@Override
	public HashMap getResponseMap() {
		return this.responseMap;
	}
	
	
	public void setByteInputStream(InputStream ins){
		try{
				ByteArrayOutputStream bos = null;
                try {
					if(this.fileSizeLimit != -1) {
						bos = new ByteArrayOutputStream();
						byte[] buf = new byte[1024];
						int len;
						while ((len = ins.read(buf)) > 0) {
							bos.write(buf, 0, len);
							if(bos.size() > fileSizeLimit) {
								this.fileSizeExceeded = true;
								break;
							}
						}
						this.byteStream =  bos.toByteArray();
					} else {
						bos = new ByteArrayOutputStream();
						byte[] buf = new byte[1024];
						int len;
						while ((len = ins.read(buf)) > 0) {
							bos.write(buf, 0, len);
						}
						this.byteStream = bos.toByteArray();
					}
                } catch (Exception e) {
                        log.log(Level.WARNING, "Exception while readStream ", e);
                } finally {
                        if (bos != null) {
                                try {
                                        bos.close();
                                } catch (Exception bose) {
                                }
                        }
                }
		}
		catch(Exception e){
			log.info("Exception occured while getting InputStream from HttpUrlConnection "+e);// No i18N
		}
	}
	@Override
	public byte[]  getByteInputStream(){
		return this.byteStream;
	}
    
	// Getting content encoding from connection

	 @Override
	 public String getContentEncoding() {
	  return this.contentEncodingType;
	 }

	

	// Getting inputStream from connection
	 @Override
	 public InputStream getInputStream() {
	      return this.connectionInputStream;
	 }
		
	// proxy setter
	private void addProxy() {
		this.pHost = ConnectionUtils.maskNull(EnginePropertyUtil.getSheetPropertyValue("writer.proxy.host")); // No I18N
		if (this.pHost != null && this.pPort != null && this.pPort.isEmpty()) {
			this.proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(this.pHost, (new Integer(this.pPort)).intValue()));
		}
	}
	
	public static String getCType(String filePath){
		String type  = null;
		try{
			final Tika mimeTika = new Tika(new MimeTypes());
			final File file = new File(filePath);
		    type = mimeTika.detect(file);
		}
		catch(Exception e){
			log.info("Exception occured ::"+e); // NO i18n
		}
		
		return type;
		
	}

	@Override
	public void setRequestHeader(String[] keys, String[] vals) {
		this.headerKeys = keys;
		this.headerValues = vals;
		
	}
	@Override
	 public HashMap getFileDetailsFromResponse() {
	     HashMap resMap = new HashMap();
	     HashMap fileMap = new HashMap();
	     if (this.response != null) {
	         try {
	             Map _responseMap = new HashMap();
	             if (httpsUrlConnection != null) {
	                 _responseMap = this.httpUrlConnection.getHeaderFields();
	             }
	             if (httpsUrlConnection != null) {
	                 _responseMap = this.httpsUrlConnection.getHeaderFields();
	             }
	             if (!_responseMap.isEmpty() && _responseMap.size() > 0) {
	                 for (Iterator iterator = _responseMap.keySet().iterator(); iterator.hasNext();) {
	                     String key = (String) iterator.next();
	                     List values = (List) _responseMap.get(key);
	                     String value = null;
	                     for (int i = 0; i < values.size(); i++) {
	                         Object o = values.get(i);
	                         value = value == null ? (String) o : value + (String) o;
	                     }
	                     resMap.put(key, value);
	                 }
	             }
	             HashMap fileDetailsMap = ConnectionUtils.getFileDetails(resMap);
	             fileMap.putAll(fileDetailsMap);
	         } catch (Exception e) {
	             log.log(Level.WARNING, null, e);
	         }
	     }
	     return fileMap;
	 }

	@Override
	public void setFileBytes(byte[] contentbytes, String fileName) {
		// TODO Auto-generated method stub
		if(contentbytes!=null && fileName !=null){
			this.fileBytes=contentbytes;
			this.fileName=fileName;
            this.isMultiPartFormPost = true;
		}else{
			log.info(" Content bytes are empty...  bytes cannot be null...");//No I18N
		}
		
	}

	@Override
	public void setOAuthHeader(String oAuthHeader) {
		// TODO Auto-generated method stub
		try{
			if(oAuthHeader!=null){
				this.oAuthHeader = oAuthHeader;
			}
		}catch (Exception e) {
			log.info("Exception occured while setting oauth token for httpUrlConnection :"+ e);// No I18N
			ConnectionUtils.printLog(e);
		}
		
	}

	@Override
	public String getContentParam() {
		// TODO Auto-generated method stub
		return this.contentParamName;
	}

	@Override
	public void setContentParam(String contentParam) {
		// TODO Auto-generated method stub
		this.contentParamName = contentParam;
		
	}

	@Override
	public void setFileSizeLimit(long fileSizeLimit) {
		this.fileSizeLimit = fileSizeLimit;
	}

	@Override
	public boolean isFileSizeExceeded() {
		return this.fileSizeExceeded;
	}

}
