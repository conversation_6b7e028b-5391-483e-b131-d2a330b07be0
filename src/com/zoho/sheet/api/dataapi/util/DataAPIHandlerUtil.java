/* $Id$ */

package com.zoho.sheet.api.dataapi.util;

import com.adventnet.ds.query.*;
import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.User;
import com.adventnet.persistence.DataObject;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.persistence.Persistence;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Protection;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Range;
import com.zoho.officeplatform.lib.license.api.LicenseUtils;
import com.zoho.officeplatform.lib.license.model.Addon;
import com.zoho.officeplatform.lib.license.model.License;
import com.zoho.officeplatform.lib.sku.constants.SkuAgentConstants;
import com.zoho.officeplatform.lib.sku.interfaces.CreditPointsApiFactory;
import com.zoho.officeplatform.lib.sku.interfaces.bean.CreditPointsQueryBean;
import com.zoho.officeplatform.lib.sku.interfaces.bean.CreditPointsReportQueryBean;
import com.zoho.officeplatform.lib.sku.model.CreditPoints;
import com.zoho.officeplatform.lib.sku.model.SkuPolicy;
import com.zoho.officeplatform.lib.sku.model.db.CreditPointsUsageReportModel;
import com.zoho.officeplatform.lib.sku.services.SkuUtil;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
import com.zoho.sheet.exception.APIErrorHandler;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.zfsng.client.ProductInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ProductType;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class DataAPIHandlerUtil
{
    public static final Logger LOGGER = Logger.getLogger(DataAPIHandlerUtil.class.getName());
    public static String getResourceIDFromURI(String uri)
    {
        StringTokenizer st=new StringTokenizer(uri,"//");

        if(uri.startsWith("/sheet"))
        { //In case the url is docs.zoho.com/sheet/api... instead of sheet.zoho.com/api/..
            st.nextToken();
        }

        st.nextToken();     // returns api
        st.nextToken();     // returns version
        return st.nextToken();
    }

    public static ProductInfo getProductInfo(String zuid) throws Exception {
        ProductInfo pdtInfo = null;
        List<ProductInfo> userProducts = ZohoFS.getUserProducts(zuid);
        if(!(userProducts == null || userProducts.isEmpty()))
        {
            for(ProductInfo tempProductInfo: userProducts)
            {
                if(tempProductInfo.getProductType() == ProductType.TEAMDRIVE.getType())
                {
                    pdtInfo = tempProductInfo;
                    break;
                }
            }
        }
        return pdtInfo;
    }

    private static JSONObject getCreditPointAsJSON(CreditPoints creditPoints, List<CreditPointsUsageReportModel> creditsUsageReportObj)
    {
        JSONObject creditInfo = new JSONObject();
        creditInfo.put("zid", creditPoints.getZid());
        creditInfo.put("used_points", creditPoints.getUsedPoints());
        creditInfo.put("earned_points", creditPoints.getEarnedPoints());
        creditInfo.put("reserved_points", creditPoints.getReservedPoints());
        creditInfo.put("available_points", creditPoints.getAvailablePoints());

        if(creditsUsageReportObj!=null && !creditsUsageReportObj.isEmpty()) {
            JSONObject reportObj = new JSONObject();
            Iterator<CreditPointsUsageReportModel> it = creditsUsageReportObj.iterator();
            while(it.hasNext()) {
                CreditPointsUsageReportModel creditPointsUsage = it.next();
                reportObj.put(SkuAgentConstants.getSkuObjectBySkuId(creditPointsUsage.getJobType()).getSkuName(), creditPointsUsage.getUsedPoints());
            }
            creditInfo.put("usage_report", reportObj);
        }
        return creditInfo;
    }
    public static JSONArray getSKUPoliciesAsJSONArray(List<SkuPolicy> policies)
    {
        JSONArray policyArr = new JSONArray();
        for(SkuPolicy policy : policies)
        {
            JSONObject policyObj = new JSONObject();
            policyObj.put("zid", policy.getZid());
            policyObj.put("sku_id", policy.getSKUId());
            policyObj.put("sku_type", policy.getSkuType());
            policyObj.put("usage_limit", policy.getUsageLimit());
            policyObj.put("available_limit", policy.getAvailableLimit());
            policyObj.put("interval_type", policy.getInterval().getType());
            policyObj.put("interval_name", policy.getInterval().getName());
            policyArr.put(policyObj);
        }
        return policyArr;
    }

    private static JSONObject getLicenseAsJSON(License license)
    {
        JSONObject licenseObj = new JSONObject();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        Date subscriptionStartDate = new Date(license.getSubscriptionStartTime());
        Date subscriptionEndDate = new Date(license.getSubscriptionEndTime());
        Date nextPaymentDate = new Date(license.getNextPaymentTime());
        licenseObj.put("addons", license.getAddonAsArray());
        JSONObject paidAddonJson = new JSONObject();
        if(!license.getPaidAddons().isEmpty()) {
            Map<Long, Addon > paidAddonsMap = license.getPaidAddons();
            for(Map.Entry<Long, Addon> entry : paidAddonsMap.entrySet()) {
                paidAddonJson.put(String.valueOf(entry.getKey()), new JSONObject(String.valueOf(entry.getValue().toJSON())));
            }
        }
        licenseObj.put("paid_addons", paidAddonJson);

        licenseObj.put("subscription_start_time", sdf.format(subscriptionStartDate));
        licenseObj.put("subscription_start_time_in_ms", license.getSubscriptionStartTime());
        licenseObj.put("subscription_end_time", sdf.format(subscriptionEndDate));
        licenseObj.put("subscription_end_time_in_ms", license.getSubscriptionEndTime());
        licenseObj.put("next_payment_time", sdf.format(nextPaymentDate));
        licenseObj.put("next_payment_time_in_ms", license.getNextPaymentTime());
        licenseObj.put("plan_name", license.getPlanName());
        licenseObj.put("plan_id", license.getPlanId());
        licenseObj.put("payment_interval", license.getPaymentInterval().toString());
//        licenseObj.put("subscription_start_time", subscriptionStartDate.toString());
//        licenseObj.put("subscription_end_time", subscriptionEndDate.toString());
//        licenseObj.put("next_payment_time", nextPaymentDate.toString());
        return licenseObj;
    }
    public static JSONObject getCreditDetails(String zsoid) throws Exception {
        return getCreditDetails(zsoid, 0, 0);
    }

    public static JSONObject getCreditDetails(String zsoid, long subscriptionStartTime, long subscriptionEndTime) throws Exception
    {
        CreditPointsQueryBean creditPointsQueryBean = CreditPointsApiFactory.getInstance().getCreditPointsQueryBean(zsoid);
        CreditPoints creditPoints = creditPointsQueryBean.getCreditPointsByZid(zsoid, zsoid);
        List<CreditPointsUsageReportModel> creditsUsageReportObj = null;
        if(subscriptionStartTime!=0 && subscriptionEndTime!=0) {
            CreditPointsReportQueryBean creditPointsReportQueryBean = CreditPointsApiFactory.getInstance().getCreditPointsReportQueryBean(zsoid);
            creditsUsageReportObj = creditPointsReportQueryBean.v2GetUsageSummaryByZidGroupByJobType(zsoid, zsoid, subscriptionStartTime, subscriptionEndTime);
        }

        if(creditPoints == null) {
            LOGGER.log(Level.SEVERE, "CREDIT POINTS :: credit points is null");
            throw new Exception();
        }
        return getCreditPointAsJSON(creditPoints, creditsUsageReportObj);
    }

    public static JSONObject getLicenseDetails(String zsoid) throws Exception
    {
        String serviceName = LicenseUtils.getServiceName(zsoid);
        LOGGER.log(Level.INFO, "serviceName : {0}", serviceName);
        License license = LicenseUtils.getLicense(zsoid, serviceName);
        if (license == null)
        {
            LOGGER.log(Level.SEVERE, "CREDIT POINTS :: license is null");
            throw new Exception();
        }

        //need to remove this log
        LOGGER.log(Level.INFO, "license for zsoid : {0} :: {1}", new Object[]{zsoid, license.toString()});

        return getLicenseAsJSON(license);
    }

    /*public static JSONObject getCreditAndLicenseJsonFromRID(String rid) throws Exception
    {
        String zosid = ZohoFS.getOwnerZID(rid);
        JSONObject response = new JSONObject();
        response.put("license_details", getLicenseDetails(zosid));
        response.put("credit_details", getCreditDetails(zosid));
        return response;
    }*/

    public static JSONObjectWrapper getUserLicenseAndCreditDetails(String zsoid) {
        JSONObjectWrapper response = new JSONObjectWrapper();
        try {
            response.put("license_details", DataAPIHandlerUtil.getLicenseDetails(zsoid));
            Long subscriptionStartTime = response.getJSONObject("license_details").getLong("subscription_start_time_in_ms"); // No I18N
            Long subscriptionEndTime = response.getJSONObject("license_details").getLong("subscription_end_time_in_ms"); // No I18N

            response.put("credit_details", DataAPIHandlerUtil.getCreditDetails(zsoid, subscriptionStartTime, subscriptionEndTime));
            List<SkuPolicy> policies = SkuUtil.getPolicy(zsoid, 1000);
            policies.addAll(SkuUtil.getPolicy(zsoid, 1001));
            response.put("policies", DataAPIHandlerUtil.getSKUPoliciesAsJSONArray(policies));
        } catch (Exception e) {
            LOGGER.log(Level.INFO, "error while getting license details for zsoid : {0}", zsoid);
            LOGGER.log(Level.INFO, "error in getUserLicenseAndCreditDetails", e);
        }
        return response;
    }

    private static Long convertDateStringtoMilliseconds(String dateStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        Date date = sdf.parse(dateStr);
        long milliseconds = date.getTime();
        return milliseconds;
    }

    public static JSONObjectWrapper getVersionDetails(String rid, Map<String,String> userdetails, String timeZoneID)throws Exception{
        String docOwner = DocumentUtils.getDocOwnerFromRid(rid);
        String docID = DocumentUtils.getDocumentId(rid, docOwner);
        String docSpaceId = ZohoFS.getSpaceId(rid);

        String versionDetails = ZohoFS.getVersions(docSpaceId, rid);
        if(versionDetails == null)
        {
            JSONObjectWrapper verObj = new JSONObjectWrapper();
            DataAPIResponse.setActionStatus(verObj.put("versions",new JSONArrayWrapper()), DataAPIConstants.SUCCESS, "workbook.version.list"); // No I18N
            return verObj;
        }
        Map<String,String> versionMap = new HashMap<>();
        JSONArray versionDetailArray = new JSONArray(versionDetails);
        for(int i=0; i<versionDetailArray.length(); i++){
            JSONObject versionObj = versionDetailArray.getJSONObject(i);
            versionMap.put(versionObj.getString("version_id"),versionObj.getString("version_number"));
        }
        Set<String> versionList = versionMap.keySet();
        SelectQueryImpl sql = new SelectQueryImpl(new Table("DocumentVersion")); // No I18N
        sql.addSelectColumn(new Column("DocumentVersion", "*")); // No I18N

        Criteria criteria1 = new Criteria(new Column("DocumentVersion", "DOCUMENT_ID"), new Long(docID), QueryConstants.EQUAL);
        Criteria criteria2 = new Criteria(new Column("DocumentVersion","VERSION_MODE"), "MANUAL", QueryConstants.EQUAL);
        Criteria criteria3 = new Criteria(new Column("DocumentVersion", "RESOURCE_VERSION_ID"), versionList.toArray(), QueryConstants.IN);
        Criteria cri = (criteria1.and(criteria2)).and(criteria3);

        SortColumn sortColumn = new SortColumn("DocumentVersion", "VERSION", false); // No I18N
        sql.addSortColumn(sortColumn);
        sql.setCriteria(cri);
        Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
        DataObject dObj = persistence.get(sql);
        return listVersions(dObj,versionMap,userdetails,timeZoneID);
    }

    private static JSONObjectWrapper listVersions(DataObject dObj, Map<String,String> versionMap,  Map<String,String> userDetails, String timeZoneID){
        JSONArrayWrapper respObject = new JSONArrayWrapper();
        JSONObjectWrapper verObj = new JSONObjectWrapper();
        String createdBy;
        try {
                Iterator itr = dObj.getRows("DocumentVersion");
                while (itr.hasNext()) {
                    JSONObject result = new JSONObject();
                    com.adventnet.persistence.Row row = (com.adventnet.persistence.Row) itr.next();
                    String zfsngVersionId = (String) row.get("RESOURCE_VERSION_ID");
                    String versionDescription = (String) row.get("VERSION_LOG");
                    if(versionDescription.indexOf("~") != -1)
                    {
                        String str[] = versionDescription.split("~");
                        versionDescription = str[0]+" "+str[1];
                    }
                    result.put("version_description", versionDescription);
                    String authorZuid = String.valueOf(row.get("AUTHOR_ZUID"));
                    if(userDetails != null) {
                        if (userDetails.containsKey(authorZuid)) {
                            result.put("created_by", userDetails.get(authorZuid));
                        } else {
                            createdBy = DocumentUtils.getZFullName(authorZuid);
                            result.put("created_by", createdBy);
                            userDetails.put(authorZuid, createdBy);
                        }
                    }
                    else {
                        result.put("created_by", authorZuid);
                    }
                    if(timeZoneID == null){
                        timeZoneID = TimeZone.getDefault().getID();
                    }
                    result.put("created_time", DataAPIResponse.getDate(String.valueOf(row.get("VERSION_TIME")), timeZoneID));
                    result.put("version_number",versionMap.get(zfsngVersionId));
                    respObject.put(result);
                }
                verObj.put("versions",respObject);
                DataAPIResponse.setActionStatus(verObj, DataAPIConstants.SUCCESS, "workbook.version.list"); // No I18N
            return verObj;
        }catch(Exception e){
            LOGGER.log(Level.INFO,"Exception in dataApiresp: {0}",e);
        }
        return null;
    }

    public static void getLockDetails(Sheet sheet, JSONObject jObj, HashMap<String,Object> sheetSharedDetails, Protection protection, String zuid, String ownerZuid, String rid) throws Exception {
        Set<String> userSet = new HashSet<>();
        Set<String> extLinkSet = new HashSet<>();
        Set<String> allUsersOfSheetSet = new HashSet<>();
        Set<String> allExtSrdLinks = new HashSet<>();
        JSONArray userArray = jObj.optJSONArray("user_emails"); //No I18N
        JSONArray extShareLnkArray = jObj.optJSONArray("external_share_links"); //No I18N
        int action = jObj.getInt(JSONConstants.ACTION);

        DataAPIHandlerUtil.userAndSharedSheetValidation(jObj, userArray, extShareLnkArray, sheetSharedDetails, userSet, allUsersOfSheetSet, extLinkSet, allExtSrdLinks, zuid, rid);
        allUsersOfSheetSet.add(ownerZuid);
        if(jObj.has(DataAPIConstants.ERROR_CODE)){
            return;
        }

        if(action==ActionConstants.LOCK_RANGE || action==ActionConstants.UNLOCK_RANGE){
            DataAPIHandlerUtil.setLockForRange(sheet, protection, jObj, action, userSet, allUsersOfSheetSet, extLinkSet, allExtSrdLinks);
        }
        else{
            DataAPIHandlerUtil.setLockForSheet(sheet, protection, jObj, action, userSet, allUsersOfSheetSet, extLinkSet, allExtSrdLinks);
        }
    }

    public static void setLockForSheet(Sheet sheet, Protection protection, JSONObject jObj, int action, Set<String> userSet, Set<String> allUsersOfSheetSet, Set<String> extLinkSet, Set<String> allExtSrdLinks){
        Set<String> authUsers = new HashSet<>();
        Set<String> unAuthUsers = new HashSet<>();
        Set<String> authEsl = new HashSet<>();
        Set<String> unAuthEsl = new HashSet<>();
//        Protection protection = sheet.getProtection();
        if(protection != null) {  //lock already exists
            authUsers = convertToSet(protection.getAuthorizedUsers());
            unAuthUsers = convertToSet(protection.getUnAuthorizedUsers());
            authEsl = convertToSet(protection.getAuthorizedExternalShareLinks());
            unAuthEsl = convertToSet(protection.getUnAuthorizedExternalShareLinks());
            switch (action) {
                case ActionConstants.LOCK_SHEET:
                    if(!userSet.isEmpty()) {
                        unAuthUsers = addAllForSet(unAuthUsers, userSet);
                        authUsers = removeAllForSet(authUsers, userSet);
                    }if(!extLinkSet.isEmpty()) {
                        unAuthEsl = addAllForSet(unAuthEsl, extLinkSet);
                        authEsl = removeAllForSet(authEsl, extLinkSet);
                    }
                    convertSetToString(jObj, authUsers, unAuthUsers, "au", "uau");    //No I18N
                    convertSetToString(jObj, authEsl, unAuthEsl, JSONConstants.AUTHORIZED_EXTERNAL_SHARE_LINKS, JSONConstants.UNAUTHORIZED_EXTERNAL_SHARE_LINKS);
                    break;
                case ActionConstants.UNLOCK_SHEET:
                    JSONArray prArray = new JSONArray();
                    JSONObject prJson = new JSONObject();
                    prJson.put(JSONConstants.PROTECTED_RANGES, sheet.getName());
                    prJson.put(JSONConstants.IS_SHEET, true);
                    if(!extLinkSet.isEmpty()) {
                        authEsl = addAllForSet(authEsl, extLinkSet);
                        unAuthEsl = removeAllForSet(unAuthEsl, extLinkSet);
                    }
                    if (!userSet.isEmpty()) {
                        authUsers = addAllForSet(authUsers, userSet);
                        unAuthUsers = removeAllForSet(unAuthUsers, userSet);
                    }
                    prJson.put("isAllUnlocked", false);
                    convertSetToString(prJson, authUsers, unAuthUsers, "au", "uau");    //No I18N
                    convertSetToString(prJson, authEsl, unAuthEsl, JSONConstants.AUTHORIZED_EXTERNAL_SHARE_LINKS, JSONConstants.UNAUTHORIZED_EXTERNAL_SHARE_LINKS);
                    prArray.put(prJson);
                    jObj.put("prObj", prArray);
                    break;
            }
        }
        else{ //new lock has to be set. Lock does NOT exist already
            switch (action) {
                case ActionConstants.LOCK_SHEET:
                    if(!userSet.isEmpty() && extLinkSet.isEmpty()){
                        unAuthUsers = addAllForSet(unAuthUsers, userSet);
                        authUsers = removeAllForSet(allUsersOfSheetSet, userSet);
                        authEsl = allExtSrdLinks;
                    }
                    else if(userSet.isEmpty() && !extLinkSet.isEmpty()){
                        unAuthEsl = addAllForSet(unAuthEsl, extLinkSet);
                        authEsl = removeAllForSet(allExtSrdLinks, extLinkSet);
                        authUsers = allUsersOfSheetSet;
                    }
                    else{
                        unAuthEsl = addAllForSet(unAuthEsl, extLinkSet);
                        authEsl = removeAllForSet(allExtSrdLinks, extLinkSet);
                        unAuthUsers = addAllForSet(unAuthUsers, userSet);
                        authUsers = removeAllForSet(allUsersOfSheetSet, userSet);
                    }
                    convertSetToString(jObj, authUsers, unAuthUsers, "au", "uau");    //No I18N
                    convertSetToString(jObj, authEsl, unAuthEsl, JSONConstants.AUTHORIZED_EXTERNAL_SHARE_LINKS, JSONConstants.UNAUTHORIZED_EXTERNAL_SHARE_LINKS);
                    break;
                case ActionConstants.UNLOCK_SHEET:
                    jObj.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.NO_SUCH_LOCKED_CELLS_ERR);
                    jObj.put(DataAPIConstants.ARGUMENT, "worksheet");
                    break;
            }
        }
    }

    public static void setLockForRange(Sheet sheet, Protection protection, JSONObject jObj, int action, Set userSet, Set allUsersOfSheetSet, Set<String> extLinkSet, Set<String> allExtSrdLinks){
        int sC = jObj.getInt(JSONConstants.START_COLUMN);
        int eC = jObj.getInt(JSONConstants.END_COLUMN);
        int sR = jObj.getInt(JSONConstants.START_ROW);
        int eR = jObj.getInt(JSONConstants.END_ROW);
        Set<String> authUsers = new HashSet<>();
        Set<String> unAuthUsers = new HashSet<>();
        Set<String> authEsl = new HashSet<>();
        Set<String> unAuthEsl = new HashSet<>();

//        Protection protection = RangeUtil.getProtection(sheet, sR, sC, eR, eC);
        if(protection != null){
            authUsers = convertToSet(protection.getAuthorizedUsers());
            unAuthUsers =  convertToSet(protection.getUnAuthorizedUsers());
            authEsl =  convertToSet(protection.getAuthorizedExternalShareLinks());
            unAuthEsl =  convertToSet(protection.getUnAuthorizedExternalShareLinks());

            switch (action) {
                case ActionConstants.LOCK_RANGE:
                    if(!extLinkSet.isEmpty()){
                        authEsl = removeAllForSet(authEsl,extLinkSet);
                        unAuthEsl = addAllForSet(unAuthEsl,extLinkSet);
                    }
                    if(!userSet.isEmpty()){
                        authUsers = removeAllForSet(authUsers,userSet);
                        unAuthUsers = addAllForSet(unAuthUsers,userSet);
                    }
                    convertSetToString(jObj,authUsers,unAuthUsers,"au","uau");  //No I18N
                    convertSetToString(jObj,authEsl,unAuthEsl,JSONConstants.AUTHORIZED_EXTERNAL_SHARE_LINKS,JSONConstants.UNAUTHORIZED_EXTERNAL_SHARE_LINKS);
                    break;
                case ActionConstants.UNLOCK_RANGE:
                    JSONArray prArray = new JSONArray();
                    JSONObject prJson = new JSONObject();
                    prJson.put(JSONConstants.IS_SHEET, false);
                    Range range = new Range(sheet, sR, sC, eR, eC);
                    String protRange = sheet.getName()+"."+range.getSimpleRangeAddressInA1();
                    prJson.put(JSONConstants.PROTECTED_RANGES, protRange);
                    if(!extLinkSet.isEmpty()){
                        authEsl = addAllForSet(authEsl,extLinkSet);
                        unAuthEsl = removeAllForSet(unAuthEsl,extLinkSet);
                    }
                    if(!userSet.isEmpty()) {
                        authUsers = addAllForSet(authUsers,userSet);
                        unAuthUsers = removeAllForSet(unAuthUsers,userSet);
                    }
                    prJson.put("isAllUnlocked", false);
                    convertSetToString(prJson,authUsers,unAuthUsers,"au","uau");    //No I18N
                    convertSetToString(prJson,authEsl,unAuthEsl,JSONConstants.AUTHORIZED_EXTERNAL_SHARE_LINKS,JSONConstants.UNAUTHORIZED_EXTERNAL_SHARE_LINKS);
                    prArray.put(prJson);
                    jObj.put("prObj",prArray);
                    break;
            }
        }
        else {  //new lock being set
            switch (action) {
                case ActionConstants.LOCK_RANGE:
                    if(userSet.isEmpty() && !extLinkSet.isEmpty()){
                        unAuthEsl = extLinkSet;
                        authEsl = removeAllForSet(allExtSrdLinks, unAuthEsl);
                        authUsers = allUsersOfSheetSet;
                    }
                    else if(!userSet.isEmpty() && extLinkSet.isEmpty()){
                        unAuthUsers = userSet;
                        authUsers = removeAllForSet(allUsersOfSheetSet, unAuthUsers);
                        authEsl = allExtSrdLinks;
                    }
                    else{
                        unAuthEsl = addAllForSet(unAuthEsl, extLinkSet);
                        authEsl = removeAllForSet(allExtSrdLinks, extLinkSet);
                        unAuthUsers = addAllForSet(unAuthUsers, userSet);
                        authUsers = removeAllForSet(allUsersOfSheetSet, userSet);
                    }
                    convertSetToString(jObj, authUsers, unAuthUsers, "au", "uau");  //No I18N
                    convertSetToString(jObj, authEsl, unAuthEsl, JSONConstants.AUTHORIZED_EXTERNAL_SHARE_LINKS, JSONConstants.UNAUTHORIZED_EXTERNAL_SHARE_LINKS);
                    break;
                case ActionConstants.UNLOCK_RANGE:
                    jObj.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.NO_SUCH_LOCKED_CELLS_ERR);
                    jObj.put(DataAPIConstants.ARGUMENT, "range of the worksheet");
                    break;
            }
        }
    }

    public static <T> T doSafeTypeCast(Object obj) {
        try {
            @SuppressWarnings("unchecked")  //No I18N
            T tObj = (T) obj;
            return tObj;
        } catch (ClassCastException e) {
            return null;
        }
    }

    public static <T> Set<T> convertToSet(Set users) {
        if(users == null)
        {
            return null;
        }
        Iterator<?> iterator =  users.iterator();
        Set<T> set = new HashSet<>();

        while(iterator.hasNext()) {
            T str = doSafeTypeCast(iterator.next());
            if(str != null) { set.add(str); }
        }
        return set;
    }

    public static void convertSetToString(JSONObject jsonObject, Set<String> authorised, Set<String> unAuthorised, String key1, String key2){
        String auth, unAuth;
        if(authorised!=null && !authorised.isEmpty()) {
            auth = String.join(",", authorised);
            jsonObject.put(key1, auth);
        }
        if(unAuthorised!=null && !unAuthorised.isEmpty()){
            unAuth = String.join(",",unAuthorised);
            jsonObject.put(key2, unAuth);
        }
    }

    public static Set<String> addAllForSet(Set<String> originalSet, Set<String> extraElementsSet){
        if(originalSet==null){
            originalSet=extraElementsSet;
        }
        else{
            originalSet.addAll(extraElementsSet);
        }
        return originalSet;
    }

    public static Set<String> removeAllForSet(Set<String> originalSet, Set<String> extraElementsSet){
        if(originalSet==null){
            return new HashSet<>();
        }
        else{
            originalSet.removeAll(extraElementsSet);
        }
        return originalSet;
    }

    public static void userAndSharedSheetValidation(JSONObject jObj, JSONArray userArray, JSONArray extShareLnkArray, HashMap<String,Object> sheetSharedDetails, Set<String> userSet, Set<String> allUsersOfSheetSet, Set<String> extLinkSet, Set<String> allExtSrdLinks, String zuid, String rid) throws Exception {
        boolean canShare, canRead;
        String capability = ZohoFS.getCapabalitiy(zuid, rid);
        if (capability != null) {
            JSONObject previlages = new JSONObject(capability);
            canShare = (boolean) previlages.get("canShare");
            if (!canShare) {
                jObj.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.AUTH_FAIL_OPERATION_ERR);
                return;
            }
        } else {
            jObj.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.AUTH_FAIL_ACCESS_ERR);
            return;
        }

        if(userArray != null) {
            for (int i = 0; i < userArray.length(); i++) { //checking if they are valid users
                User user = IAMProxy.getInstance().getUserAPI().getUser(userArray.getString(i));
                String userZuid = user != null ? user.getZuid() : null;
                if (userZuid == null) {
                    jObj.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.NO_SUCH_USER_ERR);
                    return;
                } else {
                    String capability2 = ZohoFS.getCapabalitiy(userZuid, rid);
                    if (capability2 != null) {
                        JSONObject previlages = new JSONObject(capability2);
                        canRead = (boolean) previlages.get("canRead");
                        if (!canRead) {
                            jObj.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WORKBOOK_NOT_SHARED_ERR);
                            jObj.put(DataAPIConstants.ARGUMENT, userArray.getString(i));
                            return;
                        }
                    } else {
                        jObj.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.AUTH_FAIL_ACCESS_ERR);
                        return;
                    }
                    userSet.add(userZuid);      //userSet is all the users mentioned in user_emails
                }
            }
        }

        boolean flag = false;
        if(extShareLnkArray != null){
            for (int i = 0; i < extShareLnkArray.length(); i++) {
                JSONArrayWrapper jsonArrayWrapper =  sheetSharedDetails.containsKey(JSONConstants.EXTERNAL_SHARE_LINKS) ? (JSONArrayWrapper) sheetSharedDetails.get(JSONConstants.EXTERNAL_SHARE_LINKS) : null;
                if(jsonArrayWrapper==null){
                    jObj.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.NO_SUCH_EXTERNAL_SHARE_LINK);
                    return;
                }
                Iterator<JSONObjectWrapper> itr = jsonArrayWrapper.iterator();
                while (itr.hasNext()) {
                    JSONObjectWrapper jsonObjectWrapper = itr.next();
                    if (extShareLnkArray.getString(i).equals(jsonObjectWrapper.getString("email"))) {
                        extLinkSet.add(jsonObjectWrapper.getString("zuid"));
                        flag = true;
                    }
                }
                if (!flag) {
                    jObj.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.NO_SUCH_EXTERNAL_SHARE_LINK);
                    return;
                }
            }
        }

        List<String> sharePermission = new ArrayList<>();
        sharePermission.add(JSONConstants.READ_WRITE_USERS);
        sharePermission.add(JSONConstants.CO_OWNER_USERS);
        sharePermission.add(JSONConstants.READ_COMMENT_USERS);
        sharePermission.add(JSONConstants.READ_ONLY_USERS);

        for(int i = 0; i < sharePermission.size(); i++){
            JSONArrayWrapper jsonArrayWrapper = (sheetSharedDetails.containsKey(sharePermission.get(i))) ? (JSONArrayWrapper) sheetSharedDetails.get(sharePermission.get(i)) : null;
            if (jsonArrayWrapper != null) {
                Iterator<JSONObjectWrapper> itr = jsonArrayWrapper.iterator();
                while (itr.hasNext()) {
                    JSONObjectWrapper sharedUsers = itr.next();
                    allUsersOfSheetSet.add(sharedUsers.getString("zuid"));
                }
            }
        }

        JSONArrayWrapper extLink = sheetSharedDetails.containsKey(JSONConstants.EXTERNAL_SHARE_LINKS) ? (JSONArrayWrapper) sheetSharedDetails.get(JSONConstants.EXTERNAL_SHARE_LINKS) : null;
        if(extLink!=null) {
            Iterator<JSONObjectWrapper> iterator = extLink.iterator();
            while (iterator.hasNext()) {
                JSONObjectWrapper jsonObjectWrapper = iterator.next();
                allExtSrdLinks.add(jsonObjectWrapper.getString("zuid"));
            }
        }
    }
}