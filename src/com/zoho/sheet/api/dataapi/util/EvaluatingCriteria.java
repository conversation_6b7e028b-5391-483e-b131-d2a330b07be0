/* $Id$ */

package com.zoho.sheet.api.dataapi.util;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Row;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.CellImpl;
import com.zoho.sheet.api.dataapi.DataAPIResponseUtil;
import com.zoho.sheet.exception.APIErrorHandler;

import java.util.Stack;
import java.util.Locale;

public class EvaluatingCriteria {
    private int index = 0;
    private SpreadsheetSettings spreadsheetSettings = SpreadsheetSettings.getInstance(LocaleUtil.getLocaleForSpreadSheet(IAMUtil.getCurrentUser()));
    //private Locale locale = LocaleUtil.getLocaleForSpreadSheet(IAMUtil.getCurrentUser());
    private Locale locale = null;
    private Row row;
    private Row headerRow;
    private String criteria;
    int startCol;
    int endCol;
    boolean isCaseSensitive;
    private boolean popped1,popped2;
    private Stack<Boolean> booleans = new Stack<>();
    private Stack<Boolean> booleans1 = new Stack<>();
    private Stack<APIFilters.LogicalOperator> logicalOperators = new Stack<>();
    private Stack<APIFilters.LogicalOperator> logicalOperators1 = new Stack<>();

    EvaluatingCriteria(String criteria, Locale locale, Row headerRow, int startCol, int endCol, boolean isCaseSensitive){
        this.locale = locale;
        this.criteria = criteria;
        this.headerRow = headerRow;
        this.startCol = startCol;
        this.endCol = endCol;
        this.isCaseSensitive = isCaseSensitive;
    }

    public boolean evaluateCriteria(Row row) throws Exception {
        this.row = row;
        index = 0;
        logicalOperators.clear();
        logicalOperators1.clear();
        booleans.clear();
        booleans1.clear();
        return evaluate();
    }

    private boolean evaluate() throws Exception {
        boolean result=false;
        while (index < criteria.length()) {
            char currentChar = criteria.charAt(index);
            if (Character.isWhitespace(currentChar)) {
                index++; // Skip whitespaces
            } else if (currentChar == '(') { //start of an expression
                index++;
                logicalOperators.push(APIFilters.LogicalOperator.LEFT_PARENTHESIS);
                newExpressionEvaluation(null);
            }
            else if (currentChar == ')') { //end of an expression
                index++; // Consume the closing parenthesis
                while(!logicalOperators.isEmpty() && logicalOperators.peek() != APIFilters.LogicalOperator.LEFT_PARENTHESIS){
                    boolean res;
                    res = conditionalOperator(booleans.pop(), booleans.pop(), logicalOperators.pop()); //this whole part can just be converted to conditionalOperator(booleans.pop(), booleans.pop(), logicalOperators.pop())
                    booleans.push(res);
                }
                if(!logicalOperators.isEmpty() && logicalOperators.peek()==APIFilters.LogicalOperator.LEFT_PARENTHESIS){
                    logicalOperators.pop();
                }
                if((index==criteria.length() || index==criteria.length()+1) && !booleans.isEmpty()) {
                    result = endOfCriteria();
                    return result;
                }
            }
            else {
                updateConditionsFromStack();
                if((index==criteria.length() || index==criteria.length()+1) && !booleans.isEmpty()) {
                    result = endOfCriteria();
                    return result;
                }
            }
        }
        return result;
    }

    private void newExpressionEvaluation(String field) throws Exception {
        if (field == null){
            field = getNextToken();
        }
        String comparisonOperator = getNextToken();
        String val = getNextToken();
        Cell cell,headerCell;
        Value valueObj;
        int headerRowIndex = headerRow.getRowIndex();
        String value = "";
        boolean flag = false;
        for (int i = startCol; i <= endCol; i++) {
            headerCell = headerRow.getSheet().getReadOnlyCellFromShell(headerRowIndex,i).getCell();
            if (headerCell.getContent().equals(field)){
                cell = row.getSheet().getReadOnlyCellFromShell(row.getRowIndex(),i).getCell();
                if(!(cell == null || cell.isEmpty() || cell.getContent() == null)) {
                    valueObj = cell.getValue();
                    switch (valueObj.getType()) {
                        case FLOAT:
                        case CURRENCY:
                            value = valueObj.getValue().toString();
                            break;
                        case DATE:
                            if ((APIFilter.getOperatorFromString(comparisonOperator, null).equals(APIFilter.Operator.GREATER_THAN)) || (APIFilter.getOperatorFromString(comparisonOperator, null).equals((APIFilter.Operator.LESS_THAN)))) {
                                value = Long.toString(DataAPIResponseUtil.getDateValue(valueObj));
                                break;
                            }
                        default:
                            value = ((CellImpl) cell).getLocalizedValueString();
                            break;
                    }
                }
                flag=true;
                break;
            }
        }
        if (!flag) {
            throw new Exception(String.valueOf(APIErrorHandler.INVALID_CRITERIA));
        }
        //APIFilter apiFilter = new APIFilter(field,APIFilter.getOperatorFromString(comparisonOperator, null),val,spreadsheetSettings);
        //apiFilter.setCaseSensitive(isCaseSensitive);
        //boolean condition = apiFilter.isFilterConditionMatch(value);
        //booleans.push(condition);
    }

    private void updateConditionsFromStack() throws Exception {
        boolean result;
        String logicalop = getNextToken();
        if(APIFilters.getLogicalOperatorFromString(logicalop) != null){
            while(!logicalOperators.isEmpty() && checkIfStackPrecedence(APIFilters.getLogicalOperatorFromString(logicalop),logicalOperators.peek())){
                result = conditionalOperator(booleans.pop(),booleans.pop(),logicalOperators.pop());
                booleans.push(result);
            }
            logicalOperators.push(APIFilters.getLogicalOperatorFromString(logicalop));
        } else{
            newExpressionEvaluation(logicalop);
        }
    }

    private boolean conditionalOperator(boolean popped1, boolean popped2, APIFilters.LogicalOperator logicalOperator){
        if(logicalOperator.equals(APIFilters.LogicalOperator.OR)){
            return (popped1 || popped2);
        }else{
            return (popped1 && popped2);
        }
    }

    private String getNextToken() {
        StringBuilder token = new StringBuilder();
        while (index < criteria.length() && Character.isWhitespace(criteria.charAt(index))) { //skip whitespaces
            index++;
        }

        while(index < criteria.length() && criteria.charAt(index) == '('){
            index++;
            logicalOperators.push(APIFilters.LogicalOperator.LEFT_PARENTHESIS);
            return getNextToken();
        }

        token.setLength(0);

        boolean insideQuotes = false;
        if(criteria.charAt(index)=='"') {
            while(index < criteria.length()){
                char c = criteria.charAt(index);
                if (c == '\"') {
                    insideQuotes = !insideQuotes;
                    if (!insideQuotes) {
                        index++;
                        return token.toString();
                    }
                }
                else if (insideQuotes) {
                    token.append(c);
                }
                index++;
            }
        }

        while (index < criteria.length() && !Character.isWhitespace(criteria.charAt(index)) && criteria.charAt(index) != ')' && criteria.charAt(index) != '(' && criteria.charAt(index) != '"') {
            token.append(criteria.charAt(index));
            if(criteria.charAt(index) == '>' || criteria.charAt(index) == '<' || criteria.charAt(index) == '='){
                index++;
                return token.toString();
            }
            index++;
        }
        return token.toString();
    }

    private boolean checkIfStackPrecedence(APIFilters.LogicalOperator newLogicalOp, APIFilters.LogicalOperator stackLogicalOp){
        if(newLogicalOp.equals(stackLogicalOp) || stackLogicalOp.equals(APIFilters.LogicalOperator.AND)){
            return true;
        }
        else{
            return false;
        }
    }

    private boolean endOfCriteria() throws Exception{
        boolean result = false, flag = true;
        if(!logicalOperators.isEmpty() && logicalOperators.peek()==APIFilters.LogicalOperator.AND){ //if last logicaloperator is AND, then it cannot be given precedenc in updateconditionsfromstack itself.
            result = conditionalOperator(booleans.pop(),booleans.pop(),logicalOperators.pop());
            booleans.push(result);
        }
        while (!booleans.isEmpty()) { //hereee
            if (flag) {
                booleans1.push(booleans.pop());
                flag = false;
            }
            if(!logicalOperators.isEmpty()) {
                Boolean bool = booleans.pop();
                APIFilters.LogicalOperator logic = logicalOperators.pop();
                logicalOperators1.push(logic);
                booleans1.push(bool);
            }
            else if(!booleans.isEmpty() && logicalOperators.isEmpty()) {
                throw new Exception(String.valueOf(APIErrorHandler.INVALID_CRITERIA));
            }
        }
        flag = true;
        while (!booleans1.isEmpty()) { //here
            if (flag) {
                popped1 = booleans1.pop();
                result = popped1;
                flag = false;
            } else {
                APIFilters.LogicalOperator logicalOperator = logicalOperators1.pop();
                popped2 = booleans1.pop();
                result = conditionalOperator(popped1, popped2, logicalOperator);
                popped1 = result;
            }
        }
        return result;
    }
}


/*OLD CODE WITH APIFILTERS*/

//public class EvaluatingCriteria{
//    private int index;
//    private Stack<APIFilters.LogicalOperator> logicalOperators = new Stack<>();
//    private Stack<APIFilter> apiFilterStack = new Stack<>();
//
//    private Stack<APIFilters.LogicalOperator> logicalOperators1 = new Stack<>();
//    private Stack<APIFilter> apiFilterStack1 = new Stack<>();
//    private ArrayList<Integer> expressionCount = new ArrayList<>();
//
//    private Locale locale = LocaleUtil.getLocaleForSpreadSheet(IAMUtil.getCurrentUser());
//
//    public EvaluatingCriteria(){
//        index = 0;
//        logicalOperators.clear();
//    }
//
//    public APIFilters evaluate(String criteria, boolean flag) throws Exception {
//        APIFilters apiFilters = new APIFilters();
//        while (index < criteria.length()) {
//            char currentChar = criteria.charAt(index);
//            if (Character.isWhitespace(currentChar)) {
//                index++; // Skip whitespaces
//            }
//            else if (currentChar == '(') {   //start of an expression
//                expressionCount.add(0);
//                index++; // Consume the opening parenthesis
//                newExpressionEvaluation(criteria,null);
//            }
//            else if (currentChar == ')') { //end of an expression
//                index++; // Consume the closing parenthesis
//                addApiFiltersFromStack(flag, apiFilters);
//                flag = false;
//                if (index == criteria.length()){
////                if ((index == criteria.length()) || (index == (criteria.length()-1))){
//                    endOfCriteria(apiFilters);
//                }
//            }
//            else {
//                updateConditionsFromStack(criteria,logicalOperators);
//                if(index<criteria.length()){
//                    while (Character.isWhitespace(criteria.charAt(index))) { //for whitespaces after criteria //should another check for critlength be added here?
//                        index++;
//                        if(index==criteria.length()){
//                            break;
//                        }
//                    }
//                }
//                if(index == criteria.length()){
//                    addApiFiltersFromStack(flag, apiFilters);
//                    flag=false;
//                    endOfCriteria(apiFilters);
//                }
//            }
//        }
//        return apiFilters;
//    }
//
//    private void newExpressionEvaluation(String criteria, String field) throws Exception {
//        if (field == null){
//            field = getNextToken(criteria);
//        }
//        String comparisonOperator = getNextToken(criteria);
//        String value = getNextToken(criteria);
//        if(expressionCount.size()>0) {
//            expressionCount.set(expressionCount.size() - 1, expressionCount.get(expressionCount.size() - 1) + 1);
//        }
//        else{
//            expressionCount.add(1);
//        }
//        APIFilter apiFilter = new APIFilter(field,APIFilter.getOperatorFromString(comparisonOperator),value,locale); //variable,operator,constant
//        apiFilterStack.push(apiFilter);
//    }
//
//    private void updateConditionsFromStack(String criteria, Stack logicalOperators) throws Exception {
//        String logicalop = getNextToken(criteria);
//        if(APIFilters.getLogicalOperatorFromString(logicalop) != null){
//            logicalOperators.push(APIFilters.getLogicalOperatorFromString(logicalop));
//        } else{
//            newExpressionEvaluation(criteria,logicalop);
//        }
//    }
//
//    private String getNextToken(String criteria) {
//        StringBuilder token = new StringBuilder();
//        while (index < criteria.length() && Character.isWhitespace(criteria.charAt(index))) { //skip whitespaces
//            index++;
//        }
//
//        while(index < criteria.length() && criteria.charAt(index) == '('){
//            index++;
//            expressionCount.add(0);
//            return getNextToken(criteria);
//        }
//
//        token.setLength(0);
//
//        boolean insideQuotes = false;
//        if(criteria.charAt(index)=='"') {
//            while(index < criteria.length()){
//                char c = criteria.charAt(index);
//                if (c == '\"') {
//                    insideQuotes = !insideQuotes;
//                    if (!insideQuotes) {
//                        index++;
//                        return token.toString();
//                    }
//                }
//                else if (insideQuotes) {
//                    token.append(c);
//                }
//                index++;
//            }
//        }
//
//        while (index < criteria.length() && !Character.isWhitespace(criteria.charAt(index)) && criteria.charAt(index) != ')' && criteria.charAt(index) != '(' && criteria.charAt(index) != '"') {
//            token.append(criteria.charAt(index));
//            if(criteria.charAt(index) == '>' || criteria.charAt(index) == '<' || criteria.charAt(index) == '='){
//                index++;
//                return token.toString();
//            }
//            index++;
//        }
//        return token.toString();
//    }
//
//    private void addApiFiltersFromStack(boolean flag,APIFilters apiFilters) throws Exception {
//        APIFilter aFilter;
//        int i=0;
//        int j = expressionCount.get(expressionCount.size()-1);
////        while(!logicalOperators.isEmpty()) {
//            for (i = 0; i < j; i++) {    //j helps u find the number of conditions between parenthesis. With this, u r popping from apifilterstack and then pushing into apifilterstack1. Then u r immediately adding into APIfilters
//                if (flag) {
//                    APIFilter apiFilter = apiFilterStack.pop();
//                    apiFilterStack1.push(apiFilter);
//                    flag = false;
//                } else {
//                    APIFilters.LogicalOperator logicalOperator = logicalOperators.pop();
//                    APIFilter apiFilter = apiFilterStack.pop();
//                    logicalOperators1.push(logicalOperator);
//                    apiFilterStack1.push(apiFilter);
//                }
//            }
//            i = 0;
//            while (i < j) { //this is the first condition/batch of conditions that are going into apifilters. So apifilterstack will be empty. Can't say the same about next condition.
//                aFilter = apiFilterStack1.pop();
//                if (apiFilters.getFiltersAsJSONArray().isEmpty()) {
//                    apiFilters.addFilter(aFilter, null);
//                } else {
//                    APIFilters.LogicalOperator logicalOperator = logicalOperators1.pop();
//                    apiFilters.addFilter(aFilter, logicalOperator);
//                }
//                i++;
//            }
//            expressionCount.remove(expressionCount.size()-1);
//            index++;
////        }
//    }
//
//    private void endOfCriteria(APIFilters apiFilters) throws Exception {
//        APIFilter aFilter;
//        while(!logicalOperators.isEmpty()) {
//            logicalOperators1.push(logicalOperators.pop());
//            APIFilter apiFilter = apiFilterStack.pop();
//            apiFilterStack1.push(apiFilter);
//        }
//        while(!logicalOperators1.isEmpty()) {
//            APIFilters.LogicalOperator logicalOperator = logicalOperators1.pop();
//            aFilter = apiFilterStack1.pop();
//            apiFilters.addFilter(aFilter, logicalOperator);
//        }
//    }
//}
