/* $Id$ */
package com.zoho.sheet.api.dataapi.handler;

import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.api.dataapi.APIActionObject;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
import com.zoho.sheet.exception.APIErrorHandler;
import com.zoho.sheet.util.ContainerSynchronizer;

import org.json.JSONArray;
import org.json.JSONObject;

public class ShareLinkAPIHandler implements APIHandler
{
	public static final Logger LOGGER = Logger.getLogger(ShareLinkAPIHandler.class.getName());
    
    private static final APIHandler INSTANCE = new ShareLinkAPIHandler();
    
    public static APIHandler getInstance()
    {
        return INSTANCE;
    }

    @Override
	public void handleMethod(HttpServletRequest request, HttpServletResponse response) 
	{
		if(!UploadAPIHandler.isAuthRequest(request))
        {
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_ERR, null, null));
            return;
        }

		String method = request.getParameter("method");
        JSONObject actionJson = APIActionObject.getActionObject(request, method);
        if(actionJson.has(DataAPIConstants.ERROR_CODE))
        {
            LOGGER.log(Level.WARNING, "Error while getting the action object : {0}", actionJson.toString());
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(actionJson.getInt(DataAPIConstants.ERROR_CODE), actionJson.has(DataAPIConstants.ARGUMENT) ? actionJson.getString(DataAPIConstants.ARGUMENT) : null, actionJson.has(DataAPIConstants.EXPECTED) ? actionJson.getString(DataAPIConstants.EXPECTED) : null));
            return;
        }
        
        User user = IAMUtil.getCurrentUser();
        String zuid = user.getZuid();
        
        String rid = actionJson.getString("rid");
        
        if(!ShareWorkbookAPIHandler.isSharePermitted(zuid, rid))
        {
            LOGGER.log(Level.WARNING, "User do not have permission to create sharelink : zuid : {0} :: rid : {1}", new Object[]{zuid, rid});
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_OPERATION_ERR, null, null));
            return;
        }

        final String WORKDRIVE_SERVER_URL = EnginePropertyUtil.getSheetPropertyValue("DOMAIN_WORKDRIVE_API");   //No I18N
        String sUrl = "https://"+WORKDRIVE_SERVER_URL+"/api/v1/links";   //No I18N
        //String sUrl = "https://workdrive.csez.zohocorpin.com/api/v1/links";	//Used in localmachine
        
        String ticket = "Zoho-ticket "+IAMUtil.getCurrentTicket();  //No I18N
        //String ticket = "Zoho-ticket 15137971-6615b9c7-791862fa10152a48c3fc6771d7e5dd47";	//Used in localmachine
        
        String linkName = actionJson.getString("link_name");
        
        String accessLevel = actionJson.getString("access_level");
        String roleId = accessLevel.contains("edit") ? "5" : "6";   //No I18N
        
        String inputFields = actionJson.getString("request_user_data");
        Boolean requestUserData = !inputFields.isEmpty();

        JSONArray inputFieldsJson = new JSONArray();
        if(inputFields.contains("NAME")||inputFields.contains("name"))
        {
        	JSONObject obj = new JSONObject();
        	obj.put("field_name", "Name");
        	obj.put("field_type", "TEXT");
        	obj.put("is_name_field", "true");
        	inputFieldsJson.put(obj);
        }
        if(inputFields.contains("PHONE")||inputFields.contains("phone"))
        {
        	JSONObject obj = new JSONObject();
        	obj.put("field_name", "phone");
        	obj.put("field_type", "PHONE");
        	inputFieldsJson.put(obj);
        }
        if(inputFields.contains("EMAIL")||inputFields.contains("email"))
        {
        	JSONObject obj = new JSONObject();
        	obj.put("field_name", "Email");
        	obj.put("field_type", "EMAIL_ID");
        	inputFieldsJson.put(obj);
        }
        
        String password = actionJson.getString("password");
        String expirationDate = actionJson.getString("expiration_date");
        Boolean allowDownload = actionJson.getBoolean("allow_download");
        
        JSONObject attributes = new JSONObject();
        attributes.put("resource_id", rid);
        attributes.put("link_name", linkName);
        if(requestUserData)
        {
            attributes.put("input_fields", inputFieldsJson);
        }
        attributes.put("request_user_data", requestUserData);
        attributes.put("allow_download", allowDownload);
        attributes.put("password_text", password);
        attributes.put("expiration_date", expirationDate);
        attributes.put("role_id", roleId);
        
        JSONObject data = new JSONObject();
        data.put("attributes", attributes);
        data.put("type", "links");
        
        JSONObject requestParameters =  new JSONObject();
        requestParameters.put("data", data);
        LOGGER.log(Level.INFO, "The request parameters are {0}", requestParameters.toString());
        
        try
        {
			JSONObject jsonResponse = new JSONObject();
			
			JSONObject responseObj = ContainerSynchronizer.sendRequest(sUrl, requestParameters.toString(), ticket, null, false, null);
			LOGGER.log(Level.INFO, "The response Object from Container Synchronizer is {0}", responseObj.toString());
			
			if(responseObj.getInt("rs") >= 200 && responseObj.getInt("rs") <= 201)
			{
				JSONObject obj = new JSONObject(responseObj.getJSONObject("rb"));
				JSONObject dataResponse = new JSONObject(obj.getJSONObject("data"));
				JSONObject attributesResponse = new JSONObject(dataResponse.getJSONObject("attributes"));
				jsonResponse.put("link", attributesResponse.getString("link"));
				jsonResponse.put("resource_id", attributesResponse.getString("resource_id"));
				DataAPIResponse.setActionStatus(jsonResponse, DataAPIConstants.SUCCESS, method);
			}
			DataAPIResponse.writeToOutputStream(response, jsonResponse.has("link") ? HttpServletResponse.SC_OK :HttpServletResponse.SC_INTERNAL_SERVER_ERROR, null, jsonResponse.has("link") ? jsonResponse : APIErrorHandler.getErrorObject(APIErrorHandler.INTERNAL_SERVER_ERR, null, null, method));
		}
        catch (Exception e)
        {
        	LOGGER.log(Level.INFO, "exception occured in ShareLinkAPIHandler", e);
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, null, APIErrorHandler.getErrorObject(APIErrorHandler.INTERNAL_SERVER_ERR, null, null));
        }
	}
}
