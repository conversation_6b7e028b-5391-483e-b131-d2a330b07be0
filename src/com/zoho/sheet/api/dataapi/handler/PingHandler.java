/* $Id$ */
package com.zoho.sheet.api.dataapi.handler;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.Webhook.Event;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.api.dataapi.APIActionObject;
import com.zoho.sheet.api.dataapi.DataAPIBase;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
import com.zoho.sheet.exception.APIErrorHandler;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceStatus;
import com.zoho.zfsng.constants.ResourceType;
import com.zoho.zfsng.constants.ServiceType;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class PingHandler implements APIHandler
{
    public static final Logger LOGGER = Logger.getLogger(PingHandler.class.getName());
    
    private static final APIHandler INSTANCE = new PingHandler();
    
    public static APIHandler getInstance()
    {
        return INSTANCE;
    }
    
    @Override
    public void handleMethod(HttpServletRequest request, HttpServletResponse response)
    {
        JSONObject responseJson;
        String event = request.getParameter("event");
        if(event == null)   //Plain ping to test authentication
        {
            String email = IAMUtil.getCurrentUser().getPrimaryEmail();
            responseJson = new JSONObject();
            responseJson.put("email", email);
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_OK, null, responseJson);
        }
        else    //test url to get webhook payload structure
        {
            switch(event)
            {
                case "new_workbook":
                    try
                    {
                        String zuid = String.valueOf(IAMUtil.getCurrentUser().getZUID());
                        String baseParentId = ZohoFS.getDefaultLibraryID(zuid);
                        List<Integer> resourceType = new ArrayList<>();
                        resourceType.add(ResourceType.WORKBOOK_NATIVE);
                        int resourceCount = ZohoFS.getResourceCount(zuid, "0", resourceType, ServiceType.SHEET, ResourceStatus.ACTIVE, -1, baseParentId);
                        resourceCount = Math.min(9, resourceCount);
                        if(resourceCount < 1)   //No resource available
                        {
                            JSONObject errObj = APIErrorHandler.getErrorObject(APIErrorHandler.NO_RESOURCE_FOUND, null, null);
                            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_NOT_FOUND, null, errObj);
                            return;
                        }
                        int startFrom = (int) (Math.random()*resourceCount);    //get a random resource from last 10 created resources
                        String JSONString = ZohoFS.getResourceJsonsWithParent(zuid, "0", resourceType, ServiceType.SHEET, ResourceStatus.ACTIVE, -1, baseParentId, "CREATED_TIME", false, startFrom, 1);
                        JSONArray workbooks = new JSONArray(JSONString); //The array will have only 1 element as count is 1
                        JSONObject workbookObj = workbooks.getJSONObject(0);
                        responseJson = DataAPIResponse.getNewBookJson(null, workbookObj.getString("resource_id"), workbookObj.getString("name"));
                        DataAPIResponse.addWebhookDetails(responseJson, 12345, "service_name", Event.NEW_WORKBOOK.toString().toLowerCase());    //No I18N
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_OK, null, responseJson);
                    }
                    catch(Exception e)
                    {
                        JSONObject errObj = APIErrorHandler.getErrorObject(e);
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, null, errObj);
                    }
//                    responseJson = DataAPIResponse.getNewBookJson(null, "abc...", "test_for_payload");              //No I18N
//                    DataAPIResponse.addWebhookDetails(responseJson, 123456, "service_name", "new_workbook");        //No I18N
//                    DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_OK, null, responseJson);
                    break;
                case "new_worksheet":
                case "new_row":
                case "update_worksheet":
                case "update_columns":
                    String method = "ping"; //No I18N
                    JSONObject actionObject = APIActionObject.getActionObject(request, method);
                    if(actionObject.has(DataAPIConstants.ERROR_CODE))
                    {
                        LOGGER.log(Level.WARNING, "Error while getting the action object : {0}", actionObject.toString());
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(actionObject.getInt(DataAPIConstants.ERROR_CODE), actionObject.has(DataAPIConstants.ARGUMENT) ? actionObject.getString(DataAPIConstants.ARGUMENT) : null, actionObject.has(DataAPIConstants.EXPECTED) ? actionObject.getString(DataAPIConstants.EXPECTED) : null));
                    }
                    else
                    {
                        String accessToken = accessToken = request.getHeader("Authorization");
                        String rid = (String) actionObject.remove(JSONConstants.RID);
                        StringBuilder parameters = new StringBuilder();
                        parameters.append("&actionObject=").append(Utility.getEncodedString(actionObject.toString()));
                        parameters.append("&proxyURL=").append("dataapiaction_read");
                        parameters.append("&rid=").append(rid);
                        DataAPIBase.postURLConnection(request, response, rid, parameters.toString(), accessToken, method, 30000);
                    }
                    break;
                default:
                    JSONObject errObj = APIErrorHandler.getErrorObject(APIErrorHandler.INVALID_DATA, "event", null);    //No I18N
                    DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, errObj);
                    break;
            }
        }
    }
}
