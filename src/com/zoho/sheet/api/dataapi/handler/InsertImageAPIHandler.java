/* $Id$ */
package com.zoho.sheet.api.dataapi.handler;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.iam.security.UploadedFileItem;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.action.handhelds.ImageUploadAction;
import com.zoho.sheet.api.dataapi.APIActionObject;
import com.zoho.sheet.api.dataapi.DataAPIBase;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
import com.zoho.sheet.exception.APIErrorHandler;
import com.zoho.sheet.util.ImageUtils;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class InsertImageAPIHandler implements APIHandler
{
    public static final Logger LOGGER = Logger.getLogger(InsertImageAPIHandler.class.getName());

    private static final APIHandler INSTANCE = new InsertImageAPIHandler();

    public static APIHandler getInstance()
    {
        return INSTANCE;
    }

    private class FileDetails
    {
        String fileName;
        String fileExtn;
        int width;
        int height;
        byte[] imageByteArray;  //to be used for upload image case
        JSONObject imageProperties; // to be used for library image case

        FileDetails(String fileName, String fileExtn, int width, int height, byte[] imageByteArray, JSONObject imageProperties)
        {
            this.fileName = fileName;
            this.fileExtn = fileExtn;
            this.width = width;
            this.height = height;
            this.imageByteArray = imageByteArray;
            this.imageProperties = imageProperties;
        }
    }

    public void handleMethod(HttpServletRequest request, HttpServletResponse response)
    {
        if(!UploadAPIHandler.isAuthRequest(request))
        {
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_ERR, null, null));
            return;
        }

        String method = request.getParameter("method");

        JSONObject actionJson = APIActionObject.getActionObject(request, method);
        if(actionJson.has(DataAPIConstants.ERROR_CODE))
        {
            LOGGER.log(Level.WARNING, "Error while getting the action object : {0}", actionJson.toString());
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(actionJson.getInt(DataAPIConstants.ERROR_CODE), actionJson.has(DataAPIConstants.ARGUMENT) ? actionJson.getString(DataAPIConstants.ARGUMENT) : null, actionJson.has(DataAPIConstants.EXPECTED) ? actionJson.getString(DataAPIConstants.EXPECTED) : null));
            return;
        }

        String rid = request.getParameter("resource_id");
        if(rid == null || rid.trim().isEmpty())
        {
            LOGGER.log(Level.WARNING, "resource id is missming");
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.MISSING_PARAM_ERR, "resource_id", null));    //No I18N
            return;
        }
        JSONArray imageJSONArray = actionJson.getJSONArray("image_json");
        if(imageJSONArray == null || imageJSONArray.isEmpty())
        {
            LOGGER.log(Level.WARNING, "imageJSONArray is missming");
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.MISSING_PARAM_ERR, "image_json", null));     //No I18N
            return;
        }

        Map<String, FileDetails> imageMap = new HashMap<>();
        Map<String, FileDetails> libraryImageMap = null;

        if (request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST) != null)
        {
            LOGGER.log(Level.INFO, "multi part request available");
            //imageMap = new HashMap<>();
            try
            {
                ArrayList<UploadedFileItem> filesList = (ArrayList<UploadedFileItem>) request.getAttribute(SecurityUtil.MULTIPART_FORM_REQUEST);
                for (UploadedFileItem uploadedFileItem : filesList)
                {
                    File imageFile = uploadedFileItem.getUploadedFile();

                    byte[] imageByteArray = ImageUploadAction.readFile(imageFile);
                    if (imageByteArray.length <= 0)
                    {
                        LOGGER.log(Level.WARNING, "Image byte array is empty");
                        throw new Exception("Image byte array is empty");
                    }

                    String imageFileName = uploadedFileItem.getFileName();
                    String imageExtension = imageFileName.substring(imageFileName.lastIndexOf(".") + 1);

                    if (imageFileName.indexOf("\\") != -1) {
                        imageFileName = imageFileName.substring(imageFileName.lastIndexOf("\\") + 1, imageFileName.length());
                    } else if (imageFileName.indexOf(File.separatorChar) != -1) {
                        imageFileName = imageFileName.substring(imageFileName.lastIndexOf(File.separatorChar) + 1, imageFileName.length());
                    }

                    /*BufferedImage bImage = ImageIO.read(imageFile);
                    if(bImage != null)
                    {
                        FileDetails fileDetails = new FileDetails(imageFileName, imageExtension, bImage.getWidth(), bImage.getHeight(), imageByteArray, null);
                        imageMap.put(imageFileName, fileDetails);
                    }
                    else
                    {
                        LOGGER.log(Level.WARNING, "buffered image is null :: image name : {0}", imageFileName);
                        throw new Exception("buffered image is null");
                    }*/
                    ByteArrayInputStream bais = null;
                    try {
                        ByteArrayInputStream imageInputStream = new ByteArrayInputStream(imageByteArray);
                        BufferedImage bImage = ImageIO.read(imageInputStream);
                        if (bImage != null) {
                            FileDetails fileDetails = new FileDetails(imageFileName, imageExtension, bImage.getWidth(), bImage.getHeight(), imageByteArray, null);
                            imageMap.put(imageFileName, fileDetails);
                        } else {
                            LOGGER.log(Level.WARNING, "buffered image is null :: image name : {0}", imageFileName);
                            throw new Exception("buffered image is null");
                        }
                    } finally {
                        if (bais != null) {
                            try {
                                bais.close();
                            } catch (Exception e) {
                                LOGGER.log(Level.WARNING, "exception occurred while closing input stream");
                                throw e;
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                LOGGER.log(Level.WARNING, "Error while reading image files", e);
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.INVALID_DATA, "imagefile", null));   //No I18N
                return;
            }
        }

        Set<String> imageNames = imageMap.keySet();
        String ownerZUID;
        String docOwner;
        String docID;
        //first check if any all files which are required to be uploaded are available
        try
        {
            ResourceInfo resourceInfo = ZohoFS.getResourceInfo(rid);
            if(resourceInfo == null)
            {
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.INVALID_RESOURCE_ID, null, null));
                return;
            }
            ownerZUID = resourceInfo.getOwner();
            docOwner = DocumentUtils.getDocOwnerFromRid(rid);
            docID = DocumentUtils.getDocumentId(rid, docOwner);

            if(docID == null)
            {
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.INVALID_RESOURCE_ID, null, null));
                return;
            }

            for (int i = 0; i < imageJSONArray.length(); i++)
            {
                JSONObject imageObj = imageJSONArray.getJSONObject(i);

                if(!(imageObj.has("worksheet_name") || imageObj.has("worksheet_id")))
                {
                    DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.MISSING_PARAM_ERR, "worksheet_name or worksheet_id", null)); //No I18N
                    return;
                }
                if(!isValidRowColumnIndexes(response, imageObj))
                {
                    return;
                }
                if(imageObj.optBoolean("insert_in_cell", true)) {
                    int imgMode = APIActionObject.getImageMode(imageObj.optString("image_fit_option", "fit"));  //No I18N
                    if(imgMode == -1) {
                        LOGGER.log(Level.INFO, "invalid image mode found in image json");
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.WRONG_VALUE_ERR, "image_fit_option", "fit, stretch, or cover"));    //No I18N
                        return;
                    }
                    imageObj.put(JSONConstants.IMAGE_DISPLAY_MODE, imgMode);
                }

                if (imageObj.has("image_id"))   //images in library
                {
                    String imageID = imageObj.getString("image_id");
                    ResourceInfo imageResourceInfo = ZohoFS.getResourceInfo(imageID);
                    if (imageResourceInfo == null)
                    {
                        LOGGER.log(Level.INFO, "image resource info is null");
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.INVALID_DATA, "image_id", null));    //No I18N
                        return;
                    }

                    String imageFileName = ImageUploadAction.escapeSingleQuote(imageResourceInfo.getName());
                    String imageExtension = imageResourceInfo.getExtension();
                    Map imageDimension = ImageUploadAction.getImageDimension(imageID, imageExtension);
                    JSONObject imageProperties = ImageUtils.generateImageProperties(ownerZUID, rid, docID, docOwner, null, imageID, imageFileName, false, true, true).getJsonObject();

                    if(imageProperties == null)
                    {
                        LOGGER.log(Level.INFO, "image properties is null");
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.INVALID_DATA, "image_id", null));    //No I18N
                        return;
                    }
                    imageProperties.remove(JSONConstants.IMAGE_WIDTH);
                    imageProperties.remove(JSONConstants.IMAGE_HEIGHT);
                    FileDetails fileDetails = new FileDetails(imageFileName, imageExtension, (Integer)imageDimension.get("width"), (Integer)imageDimension.get("height"), null, imageProperties);
                    if(libraryImageMap == null)
                    {
                        libraryImageMap = new HashMap<>();
                    }
                    libraryImageMap.put(imageID, fileDetails);
                }
                else    //image not in library, so needs to be uploaded
                {
                    String imageFileName = imageObj.optString("image_file_name", null); //No I18N
                    if (imageFileName == null || imageFileName.trim().isEmpty())
                    {
                        LOGGER.log(Level.INFO, "image file name not available");
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.MISSING_PARAM_ERR, "image_id or image_file_name", null));    //No I18N
                        return;
                    }
                    if (!imageNames.contains(imageFileName))
                    {
                        LOGGER.log(Level.INFO, "image file missing");
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.MISSING_FILE_ERR, imageFileName, null));
                        return;
                    }
                }
            }
        }
        catch (Exception e)
        {
            LOGGER.log(Level.WARNING, "Error while verifying images", e);
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, null, APIErrorHandler.getErrorObject(APIErrorHandler.INTERNAL_SERVER_ERR, null, null));
            return;
        }

        //Everything is fine, now iterate the array and upload if required and create new json array
        JSONArray newImageArray = new JSONArray();
        try
        {
            String zuid = IAMUtil.getCurrentUser().getZuid();

            for (int i = 0; i < imageJSONArray.length(); i++)
            {
                JSONObject imageObj = imageJSONArray.getJSONObject(i);
                String imageID = null;
                FileDetails fileDetails;
                if(imageObj.has("image_id")) // inserting image from library
                {
                    imageID = imageObj.getString("image_id");
                    fileDetails = libraryImageMap.get(imageID);
                }
                else // need to upload image
                {
                    boolean addToLibrary = imageObj.optBoolean("add_to_library", false);    //No I18N
                    String imageName = imageObj.getString("image_file_name");
                    fileDetails = imageMap.get(imageName);
                    if(addToLibrary)
                    {
                        //add image to library
                        ByteArrayInputStream bais = null;
                        try
                        {
                            bais = new ByteArrayInputStream(fileDetails.imageByteArray);
                            ImageInputStream imageStream = ImageIO.createImageInputStream(bais);
                            imageID = ImageUploadAction.addImageResource(zuid, fileDetails.fileName, fileDetails.fileExtn, bais, null);
                            JSONObject imageProperties = ImageUtils.generateImageProperties(ownerZUID, rid, docID, docOwner, null, imageID, fileDetails.fileName, false, true, true).getJsonObject();
                            if (imageProperties == null)
                            {
                                LOGGER.log(Level.INFO, "image properties is null while uploading to library : image name :: {0}", imageName);
                                continue;
                            }
                            imageProperties.remove(JSONConstants.IMAGE_HEIGHT);
                            imageProperties.remove(JSONConstants.IMAGE_WIDTH);
                            fileDetails.imageProperties = imageProperties;
                        }
                        finally
                        {
                            if (bais != null) {
                                try {
                                    bais.close();
                                } catch (Exception e) {
                                    LOGGER.log(Level.WARNING, "exception occurred while closing input stream");
                                    throw e;
                                }
                            }
                        }
                    }
                    else
                    {
                        //add image to dfs store
                        String uniqueKey = ImageUtils.writeImageToSheetImagesDFSStore(docID, docOwner, ownerZUID, null, fileDetails.imageByteArray, fileDetails.fileExtn);
                        String uploadedFileUrl = "&u_n=" + uniqueKey; //NO I18N
                        JSONObject imageProperties = new JSONObject();
                        imageProperties.put(JSONConstants.IMAGE_URL, uploadedFileUrl);
                        fileDetails.imageProperties = imageProperties;
                    }
                }

                JSONObject newObj = new JSONObject();
                newObj.put(JSONConstants.IMAGE_ROW, imageObj.getInt("row") - 1);
                newObj.put(JSONConstants.IMAGE_COLUMN, imageObj.getInt("column") - 1);
                newObj.put("sRowDiff", 0);
                newObj.put("sColDiff", 0);
                newObj.put(JSONConstants.IMAGE_NAME, fileDetails.fileName);
                newObj.put("isRemoteMode", false);
                newObj.put("insertFromLib", imageObj.has("image_id"));
                newObj.put("image_properties", fileDetails.imageProperties);
                if(imageID != null)
                {
                    newObj.put("resId", imageID);
                }
                if(imageObj.has("worksheet_name"))
                {
                    newObj.put(JSONConstants.SHEET_NAME, imageObj.getString("worksheet_name"));
                }
                if(imageObj.has("worksheet_id"))
                {
                    newObj.put(JSONConstants.ASSOCIATED_SHEET_NAME, Utility.getDecodedString(imageObj.getString("worksheet_id")));
                }
                newObj.put(JSONConstants.IMAGE_ACTUAL_HEIGHT, fileDetails.height);
                newObj.put(JSONConstants.IMAGE_ACTUAL_WIDTH, fileDetails.width);
                newObj.put(JSONConstants.IMAGE_HEIGHT, fileDetails.height);
                newObj.put(JSONConstants.IMAGE_WIDTH, fileDetails.width);
                boolean isCellImage = imageObj.optBoolean("insert_in_cell", true);  //No I18N
                newObj.put(JSONConstants.IS_CELL_IMAGE, isCellImage);
                if(isCellImage) {
                    newObj.put(JSONConstants.IMAGE_DISPLAY_MODE, imageObj.getInt(JSONConstants.IMAGE_DISPLAY_MODE));
                }
                newImageArray.put(newObj);
            }
        }
        catch(Exception e)
        {
            LOGGER.log(Level.INFO, "error occurred during actual process", e);
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.INVALID_DATA, "image_json", null));  //No I18N
            return;
        }

        if(newImageArray.isEmpty())
        {
            LOGGER.log(Level.WARNING, "no images to insert :: image json array is empty");
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.INVALID_DATA, "image_json", null));  //No I18N
            return;
        }

        //next send internal request
        StringBuilder parameters = new StringBuilder();
        String accessToken = request.getHeader("Authorization");
        actionJson.remove("image_json");
        actionJson.put("imageJsonArray", newImageArray);
        parameters.append("&actionObject=").append(Utility.getEncodedString(actionJson.toString()));
        parameters.append("&proxyURL=").append("dataapiaction_update");
        parameters.append("&rid=").append(rid);
        DataAPIBase.postURLConnection(request, response, rid, parameters.toString(), accessToken, method, 30000);
    }

    private static boolean isValidRowColumnIndexes(HttpServletResponse response, JSONObject imageObj)
    {
        if(!(imageObj.has("row")))
        {
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.MISSING_PARAM_ERR, "row", null));    //No I18N
            return false;
        }
        else
        {
            int intValue = -1;
            try
            {
                intValue = imageObj.getInt("row");
            }
            catch(Exception e)
            {
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.WRONG_TYPE_ERR, "row", "Integer"));  //No I18N
                return false;
            }
            if(intValue <=0 || intValue > Utility.MAXNUMOFROWS)
            {
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.WRONG_VALUE_ERR, "row", "1 to "+Utility.MAXNUMOFROWS));  //No I18N
                return false;
            }
        }
        if(!(imageObj.has("column")))
        {
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.MISSING_PARAM_ERR, "column", null));     //No I18N
            return false;
        }
        {
            int intValue = -1;
            try
            {
                intValue = imageObj.getInt("column");
            }
            catch(Exception e)
            {
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.WRONG_TYPE_ERR, "column", "Integer"));   //No I18N
                return false;
            }
            if(intValue <=0 || intValue > Utility.MAXNUMOFCOLS)
            {
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.WRONG_VALUE_ERR, "column", "1 to "+Utility.MAXNUMOFCOLS));   //No I18N
                return false;
            }
        }
        return true;
    }
}
