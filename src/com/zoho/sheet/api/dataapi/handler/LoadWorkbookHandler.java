/* $Id$ */

package com.zoho.sheet.api.dataapi.handler;

import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.sheet.api.dataapi.APIActionObject;
import com.zoho.sheet.api.dataapi.DataAPIBase;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class LoadWorkbookHandler implements APIHandler
{
    public static final Logger LOGGER = Logger.getLogger(LoadWorkbookHandler.class.getName());
    
    private static final APIHandler INSTANCE = new LoadWorkbookHandler();
    
    public static APIHandler getInstance()
    {
        return INSTANCE;
    }
    
    @Override
    public void handleMethod(HttpServletRequest request, HttpServletResponse response)
    {
        String method = "workbook.load";        //No I18N
        JSONObject actionObject = APIActionObject.getActionObject(request, method);
        String accessToken = accessToken = request.getHeader("Authorization");
        String rid = (String)request.getAttribute("rid");
        StringBuilder parameters = new StringBuilder();
        parameters.append("&actionObject=").append(Utility.getEncodedString(actionObject.toString()));
        parameters.append("&proxyURL=").append("dataapiaction_read");
        parameters.append("&rid=").append(rid);
        DataAPIBase.postURLConnection(request, response, rid, parameters.toString(), accessToken, method, 30000);
    }
}
