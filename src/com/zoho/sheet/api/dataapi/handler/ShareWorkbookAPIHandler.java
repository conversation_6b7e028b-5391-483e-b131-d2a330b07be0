/* $Id$ */
package com.zoho.sheet.api.dataapi.handler;

import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.zoho.sheet.api.dataapi.APIActionObject;
import com.zoho.sheet.api.dataapi.DataAPIBase;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
import com.zoho.sheet.exception.APIErrorHandler;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ShareConstants;

import org.json.JSONArray;
import org.json.JSONObject;

public class ShareWorkbookAPIHandler implements APIHandler
{
    public static final Logger LOGGER = Logger.getLogger(ShareWorkbookAPIHandler.class.getName());
    
    private static final APIHandler INSTANCE = new ShareWorkbookAPIHandler();
    
    public static APIHandler getInstance()
    {
        return INSTANCE;
    }

    @Override
    public void handleMethod(HttpServletRequest request, HttpServletResponse response) 
    {
        if(!UploadAPIHandler.isAuthRequest(request))
        {
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_ERR, null, null));
            return;
        }

        String method = request.getParameter("method");
        JSONObject actionJson = APIActionObject.getActionObject(request, method);
        if(actionJson.has(DataAPIConstants.ERROR_CODE))
        {
            LOGGER.log(Level.WARNING, "Error while getting the action object : {0}", actionJson.toString());
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(actionJson.getInt(DataAPIConstants.ERROR_CODE), actionJson.has(DataAPIConstants.ARGUMENT) ? actionJson.getString(DataAPIConstants.ARGUMENT) : null, actionJson.has(DataAPIConstants.EXPECTED) ? actionJson.getString(DataAPIConstants.EXPECTED) : null));
            return;
        }

        User user = IAMUtil.getCurrentUser();
        String zuid = user.getZuid();
        
        String rid = actionJson.getString("rid");
        
        /*if(!isSharePermitted(zuid, rid))
        {
            LOGGER.log(Level.WARNING, "User do not have permission to share : zuid : {0} :: rid : {1}", new Object[]{zuid, rid});
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_OPERATION_ERR, null, null));
            return;
        }*/

        LOGGER.log(Level.INFO, "share workbook api handler :: rid : {0} :: zuid : {1}", new Object[]{rid, zuid});
        try {
            String capability = ZohoFS.getCapabalitiy(zuid, rid);
            //LOGGER.log(Level.INFO, "capability : {0}", capability);
            if (capability != null) {
                JSONObject privilege = new JSONObject(capability);
                boolean canShare = privilege.optBoolean("canShare"); // No I18N
                if(!canShare)
                {
                    LOGGER.log(Level.INFO,"use do not have privilege to share");
                    DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_OPERATION_ERR, null, null));
                    return;
                }
            } else {
                LOGGER.log(Level.INFO,"Capability is null");
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_OPERATION_ERR, null, null));
                return;
            }
        }catch (Exception e){
            LOGGER.log(Level.INFO,"Exception in trashrestoredelete capability : {0}",e.getMessage());
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_OPERATION_ERR, null, null));
            return;
        }

        LOGGER.log(Level.INFO, "user can share the document");
        JSONArray shareJson = actionJson.getJSONArray("share_json");

        JSONArray failureList = new JSONArray();
        boolean isPartialSuccess = false;
        try 
        {        	
            for (int i = 0; i < shareJson.length(); i++) 
            {
                JSONObject failureObj = null;
                String shareRole = null;
                String shareToUserZUID = null;
                User shareToUser;
                
                JSONObject shareObj = shareJson.getJSONObject(i);
                //LOGGER.log(Level.INFO, shareObj.toString());
                
                if(shareObj.has("user_email"))
                {
                    shareToUser = IAMProxy.getInstance().getUserAPI().getUser(shareObj.getString("user_email"));
                    shareToUserZUID = shareToUser != null ? shareToUser.getZuid() : null;

                }
                if(shareToUserZUID == null)
                {
                    Integer errorCode = shareObj.has("user_email") ? APIErrorHandler.NO_SUCH_USER_ERR : APIErrorHandler.MISSING_PARAM_ERR;//No I18N
                    failureObj = new JSONObject(shareObj);
                    addToFailureList(failureList, failureObj, "user_email", errorCode); //No I18N
                    continue;
                }
                
                
                if(shareObj.has("access_level"))
                {
                    String accessLevel = shareObj.getString("access_level");

                    switch(accessLevel)
                    {
                        case "view":
                            shareRole = "VIEWER";//No I18N
                            break;
                        case "edit":
                            shareRole = "COLLABORATOR";//No I18N
                            break;
                        case "comment":
                        case "view_and_comment":
                            shareRole = "COMMENTOR";//No I18N
                            break;
                        case "share":
                            shareRole = "COOWNER";//No I18N
                            break;
                        case "remove_share":
                        	shareRole = "REMOVE";//No I18N
                        	break;
                        default:
                            break;
                    }
                }
                if(shareRole == null)
                {
                    Integer errorCode = shareObj.has("access_level") ? APIErrorHandler.INVALID_DATA : APIErrorHandler.MISSING_PARAM_ERR;//No I18N
                    failureObj = new JSONObject(shareObj);
                    addToFailureList(failureList, failureObj, "access_level", errorCode);   //No I18N
                    continue;
                }
                
                
                LOGGER.log(Level.INFO, "zuid of the current user is:{0}...resource_id:{1}...zuid of the shared user is:{2}...with shareRole as {3}", new Object[]{zuid, rid, shareToUserZUID, shareRole});
                
                try 
                {
                    boolean shareStatus = false;
                    if("REMOVE".equals(shareRole))
                    {
                        shareStatus = ZohoFS.deletePermission(zuid, rid, "-1", shareToUserZUID, ShareConstants.SHAREDTYPE_PERSONAL);
                    }
                    else
                    {
                        //ZohoFS.shareResource(zuid, rid, "-1", shareToUserZUID , ShareConstants.SHAREDTYPE_PERSONAL, shareRole, false);
                        //String resourcePermission = ZohoFS.getResourcePermission(zuid, rid, "-1", shareToUserZUID);
                        //LOGGER.log(Level.INFO, "current resource permission : {0}", resourcePermission);

                        //if(resourcePermission == null || resourcePermission.isEmpty())
                        //{
                        LOGGER.log(Level.INFO, "sharing resource");
                        shareStatus = ZohoFS.shareResource(zuid, rid, "-1", shareToUserZUID, ShareConstants.SHAREDTYPE_PERSONAL, shareRole, false, true, null);
                        //}
                        //else
                        //{
                            //shareStatus = ZohoFS.updatePermission(zuid, rid, "-1", shareToUserZUID, ShareConstants.SHAREDTYPE_PERSONAL, shareRole, false, null);
                            //shareStatus = ZohoFS.shareResource(zuid, rid, "-1", shareToUserZUID , ShareConstants.SHAREDTYPE_PERSONAL, shareRole, false);
                        //}
                    }
                    if(!shareStatus)
                    {
                        failureObj = new JSONObject(shareObj);
                        addToFailureList(failureList, failureObj, null, APIErrorHandler.INTERNAL_SERVER_ERR);
                        continue;
                    }
                    isPartialSuccess = true;
                }
                catch (Exception excp)
                {
                    if(excp.getMessage() != null && excp.getMessage().contains("ZFSError:F313:Co-owner cannot change permission to another co-owner."))
                    {
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_OPERATION_ERR, null, null));
                        return;
                    }
                    else if(excp.getMessage().contains("ZFSError:R955:Sharing resource outside org with share permission is restricted.")){
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.SHARE_PERMISSION_DENIED_ERR, null, null));
                        return;
                    }
                    LOGGER.log(Level.WARNING , "Exception occured while sharing" , excp);
                    failureObj = new JSONObject(shareObj);
                    addToFailureList(failureList, failureObj, null, APIErrorHandler.INTERNAL_SERVER_ERR);
                }
            }

            DataAPIResponse.writeToOutputStream(response, isPartialSuccess ? HttpServletResponse.SC_OK : HttpServletResponse.SC_BAD_REQUEST, null, DataAPIResponse.getSharedWorkbookResponse(method, failureList, isPartialSuccess));
        }
        catch(Exception e)
        {
            LOGGER.log(Level.WARNING , "Exception occured while sharing in outer try" , e);
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, null, APIErrorHandler.getErrorObject(APIErrorHandler.INTERNAL_SERVER_ERR, null, null));
        }
    }
    
    private void addToFailureList(JSONArray failureList, JSONObject failureObj, String arg, int errorCode)
    {
        failureObj.put("error_message", APIErrorHandler.getErrorMessage(errorCode, arg , null));
        failureObj.put("error_code", errorCode);
        failureList.put(failureObj);
    }
    
    public static boolean isSharePermitted(String ZUID, String rid)
    {
        return DataAPIBase.isActionPermitted(ZUID, rid, ShareConstants.PERMISSION_SHARE);
    }
}
