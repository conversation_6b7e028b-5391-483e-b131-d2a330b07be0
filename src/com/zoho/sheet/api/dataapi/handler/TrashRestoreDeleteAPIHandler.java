/* $Id$ */
package com.zoho.sheet.api.dataapi.handler;

import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.wms.api.WmsApi;
import com.adventnet.wms.common.WmsService;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.api.dataapi.APIActionObject;
import com.zoho.sheet.api.dataapi.DataAPIBase;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
import com.zoho.sheet.exception.APIErrorHandler;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceStatus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.json.JSONArray;
import org.json.JSONObject;

public class TrashRestoreDeleteAPIHandler implements APIHandler {
    public static final Logger LOGGER = Logger.getLogger(TrashRestoreDeleteAPIHandler.class.getName());
    private static final APIHandler INSTANCE = new TrashRestoreDeleteAPIHandler();

    public static APIHandler getInstance()
    {
        return INSTANCE;
    }

    public void handleMethod(HttpServletRequest request, HttpServletResponse response){
        if(!DataAPIBase.isAuthRequest(request))
        {
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_ERR, null, null));
            return;
        }

        User user = IAMUtil.getCurrentUser();
        String zuid = user.getZuid();
        String method = request.getParameter("method");
        JSONObject actionJson = APIActionObject.getActionObject(request, method);
        if(actionJson.has(DataAPIConstants.ERROR_CODE))
        {
            LOGGER.log(Level.INFO, "action object has error code : {0}", actionJson.toString());
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(actionJson.getInt(DataAPIConstants.ERROR_CODE), actionJson.has(DataAPIConstants.ARGUMENT) ? actionJson.getString(DataAPIConstants.ARGUMENT) : null, actionJson.has(DataAPIConstants.EXPECTED) ? actionJson.getString(DataAPIConstants.EXPECTED) : null, method));
            return;
        }
        int action = actionJson.getInt(JSONConstants.ACTION);
        JSONArray ridArray = actionJson.getJSONArray("resource_ids");
        if(ridArray.isEmpty()){
            DataAPIResponse.writeToOutputStream(response,HttpServletResponse.SC_BAD_REQUEST,null,APIErrorHandler.getErrorObject(APIErrorHandler.MISSING_PARAM_ERR,"resource_ids",null));    // No I18N
            return;
        }
        JSONArray failureArray = new JSONArray();
        for(int i=0; i<ridArray.length(); i++) {
            try {
                String rid = ridArray.getString(i);
                if(rid == null || rid.trim().isEmpty()) {
                    LOGGER.log(Level.INFO,"Rid is null or empty");
                    throw new Exception(String.valueOf(APIErrorHandler.MISSING_PARAM_ERR));
                }
                LOGGER.log(Level.INFO,"rid in trashrestoredeleteapihandler: {0} :: and action : {1} :: by zuid : {2}",new Object[]{rid, action, zuid});

                boolean canTrash = false;
                boolean canRestore = false;
                boolean canDelete = false;
                boolean canRead = false;

                try {
                    String capability = ZohoFS.getCapabalitiy(zuid, rid);
                    if (capability != null) {
                        JSONObject previlages = new JSONObject(capability);
                        canTrash = ((boolean) previlages.get("canTrash"));
                        canRestore = ((boolean) previlages.get("canRestore"));
                        canDelete = ((boolean) previlages.get("canDelete"));
                        canRead = ((boolean) previlages.get("canRead"));
                    } else {
                        LOGGER.log(Level.INFO,"Capability is null");
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_OPERATION_ERR, null, null));
                        return;
                    }
                }catch (Exception e){
                    LOGGER.log(Level.INFO,"Exception in trashrestoredelete capability : {0}",e.getMessage());
                    DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.NO_SUCH_WORKBOOK_ERR, rid, null));
                    return;
                }

                LOGGER.log(Level.INFO,"canTrash : {0} :: canRestore : {1} :: canDelete : {2} in TrashRestoreAPIHandler",new Object[]{canTrash,canRestore,canDelete});
                if (!canTrash && !canDelete && !canRestore) {
                    try {
                        ResourceInfo resourceInfo = ZohoFS.getResourceInfo(rid);
                        int resourceStatus = resourceInfo.getStatus();
                        if (resourceStatus == ResourceStatus.ACTIVE) {
                            if (canRead) {
                                LOGGER.log(Level.INFO, "can read is true and the document is active");
                                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_OPERATION_ERR, null, null));
                            } else {
                                LOGGER.log(Level.INFO, "can read is false and the document is active");
                                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_UNAUTHORIZED, null, APIErrorHandler.getErrorObject(APIErrorHandler.AUTH_FAIL_ACCESS_ERR, null, null));
                            }
                            return;
                        } else { //resource not active, may be trashed
                            LOGGER.log(Level.INFO, "Resource status is not active");
                            throw new Exception(String.valueOf(APIErrorHandler.NO_SUCH_WORKBOOK_ERR));
                        }
                    } catch (Exception e) {
                        LOGGER.log(Level.INFO, "Exception e in trashrestoredelete resourcestatus : {0}", e.getMessage());
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.NO_SUCH_WORKBOOK_ERR, rid, null));
                        return;
                    }
                } else if (action == ActionConstants.DOCUMENT_RESTORE || action == ActionConstants.DOCUMENT_DELETE) {
                    if ((!canRestore || !canDelete) && canTrash) {
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.WORKBOOK_RESTORE_DELETE_ERR, null, null));
                        return;
                    }
                } else if (action == ActionConstants.DOCUMENT_TRASH) {
                    if (!canTrash && (canDelete || canRestore)) {
                        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.NO_SUCH_WORKBOOK_ERR, rid, null));
                        return;
                    }
                }
            } catch (Exception e){
                LOGGER.log(Level.INFO, "Exception in resource_ids param : {0}", e.getMessage());
                DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(APIErrorHandler.MISSING_PARAM_ERR, "resource_ids", null));    // No I18N
                return;
            }
        }

        JSONObject apiResponse = new JSONObject();
        for(int i=0; i<ridArray.length(); i++){
            String rid = ridArray.getString(i);
            try {
                switch (action) {
                    case ActionConstants.DOCUMENT_TRASH:
                        List<String> trashList = new ArrayList<>();
                        trashList.add(rid);
                        if (ZohoFS.trashResource(ZohoFS.getOwnerZID(rid), trashList, zuid)) {
                            Hashtable<String, String> msgobj = new Hashtable<>();
                            msgobj.put("resourceId", rid);
                            msgobj.put("action", "RESOURCE_TRASHED");// No I18N
                            WmsApi.sendCrossProductMessage(zuid, msgobj, WmsService.SHEET);
                            LOGGER.log(Level.INFO, "Document trashed successfully");
                        } else {
                            LOGGER.log(Level.INFO, "unable to trash");
                            throw new Exception(String.valueOf(APIErrorHandler.INTERNAL_SERVER_ERR));
                        }
                        break;
                    case ActionConstants.DOCUMENT_RESTORE:
                        if (ZohoFS.restoreResource(ZohoFS.getOwnerZID(rid), rid, zuid)) {
                            Hashtable<String, String> msgobj = new Hashtable<>();
                            msgobj.put("resourceId", rid);
                            msgobj.put("action", "RESOURCE_RESTORED");// No I18N
                            WmsApi.sendCrossProductMessage(zuid, msgobj, WmsService.SHEET);
                            LOGGER.log(Level.INFO, "Document restored successfully");
                        } else {
                            LOGGER.log(Level.INFO, "unable to restore");
                            throw new Exception(String.valueOf(APIErrorHandler.INTERNAL_SERVER_ERR));
                        }
                        break;
                    case ActionConstants.DOCUMENT_DELETE:
                        String ownerZid = ZohoFS.getOwnerZID(rid);
                        if (ZohoFS.deleteResource(ownerZid, rid, zuid)) {
                            Hashtable<String, String> msgobj = new Hashtable<>();
                            msgobj.put("resourceId", rid);
                            msgobj.put("action", "RESOURCE_DELETED");// No I18N
                            WmsApi.sendCrossProductMessage(zuid, msgobj, WmsService.SHEET);
                            LOGGER.log(Level.INFO, "Document deleted successfully");
                        } else {
                            LOGGER.log(Level.INFO, "unable to delete");
                            throw new Exception(String.valueOf(APIErrorHandler.INTERNAL_SERVER_ERR));
                        }
                        break;
                }
            } catch (Exception e){
                JSONObject errorObject = APIErrorHandler.getErrorObject(e);
                apiResponse.put(rid, errorObject);
                failureArray.put(apiResponse);
                LOGGER.log(Level.INFO,"Exception e in trash/restore/delete : {0}", e.getMessage());
            }
        }

        if(failureArray.length() > 0) {
            LOGGER.log(Level.INFO, "failure array: {0}",failureArray.toString());
            DataAPIResponse.writeToOutputStream(response, (failureArray.length() == ridArray.length()) ? HttpServletResponse.SC_INTERNAL_SERVER_ERROR : HttpServletResponse.SC_OK , null, (failureArray.length() == ridArray.length()) ? APIErrorHandler.getErrorObject(APIErrorHandler.INTERNAL_SERVER_ERR,null,null) : DataAPIResponse.getSharedWorkbookResponse(method, failureArray, true));
        }
        else{
            JSONObject jsonObject = new JSONObject();
            DataAPIResponse.setActionStatus(jsonObject, DataAPIConstants.SUCCESS, method);
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_OK, null, jsonObject);
        }
    }
}
