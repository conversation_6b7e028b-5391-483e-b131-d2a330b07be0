/* $Id$ */
package com.zoho.sheet.api.dataapi.handler;

import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.api.dataapi.APIActionObject;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
import com.zoho.sheet.exception.APIErrorHandler;

import org.json.JSONObject;

public class UtilityAPIHandler implements APIHandler
{
	public static final Logger LOGGER = Logger.getLogger(UtilityAPIHandler.class.getName());
    
    private static final APIHandler INSTANCE = new UtilityAPIHandler();
    
    public static APIHandler getInstance()
    {
        return INSTANCE;
    }
	@Override
	public void handleMethod(HttpServletRequest request, HttpServletResponse response) 
	{
		String method = request.getParameter("method");
        JSONObject actionJson = APIActionObject.getActionObject(request, method);
        if(actionJson.has(DataAPIConstants.ERROR_CODE))
        {
            LOGGER.log(Level.WARNING, "Error while getting the action object : {0}", actionJson.toString());
            DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_BAD_REQUEST, null, APIErrorHandler.getErrorObject(actionJson.getInt(DataAPIConstants.ERROR_CODE), actionJson.has(DataAPIConstants.ARGUMENT) ? actionJson.getString(DataAPIConstants.ARGUMENT) : null, actionJson.has(DataAPIConstants.EXPECTED) ? actionJson.getString(DataAPIConstants.EXPECTED) : null));
            return;
        }
        JSONObject jsonResponse = new JSONObject();
        
        switch(method)
        {
        	case "range.address.get":
        		String range = CellUtil.getCellReference(actionJson.getInt(JSONConstants.START_ROW), actionJson.getInt(JSONConstants.START_COLUMN), false, false);
        		range += ":";
        		range += CellUtil.getCellReference(actionJson.getInt(JSONConstants.END_ROW), actionJson.getInt(JSONConstants.END_COLUMN), false, false);
        		jsonResponse.put("range_string", range);
        		DataAPIResponse.setActionStatus(jsonResponse, DataAPIConstants.SUCCESS, method);
        		break;
        		
        	case "range.index.get":
        		String cellAddr[] = actionJson.getString(JSONConstants.VALUE).split(":");
                jsonResponse.put("start_row", CellUtil.getRow(cellAddr[0])+1);
                jsonResponse.put("start_column", CellUtil.getColumn(cellAddr[0])+1);
                jsonResponse.put("end_row", CellUtil.getRow(cellAddr[1])+1);
                jsonResponse.put("end_column", CellUtil.getColumn(cellAddr[1])+1);
        		DataAPIResponse.setActionStatus(jsonResponse, DataAPIConstants.SUCCESS, method);
        		break;
        }
        DataAPIResponse.writeToOutputStream(response, HttpServletResponse.SC_OK, null, jsonResponse);
	}

}
