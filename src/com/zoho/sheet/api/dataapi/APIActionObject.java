/* $Id$ */

package com.zoho.sheet.api.dataapi;

import com.adventnet.iam.IAMUtil;
import com.adventnet.zoho.websheet.model.ErrorCode;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.action.ActionInfo;
import com.zoho.sheet.action.ActionInfo.ActionSelectionProperties;
import com.zoho.sheet.action.ActionObject;
import com.zoho.sheet.authorization.FeatureSuppressInfo;
import com.zoho.sheet.exception.APIErrorHandler;
import com.zoho.sheet.util.textimport.ContentParser;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceType;
import org.json.JSONArray;
import org.json.JSONObject;
import com.zoho.zfsng.constants.ZFSNGConstants;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class APIActionObject
{
    public static final Logger LOGGER = Logger.getLogger(APIActionObject.class.getName());
    
    private final static HashMap<String, ActionInfo> API_ACTION_INFO_MAP = new HashMap<>();
    
    static
    {
        API_ACTION_INFO_MAP.put("worksheet.list", new ActionInfo(ActionConstants.LIST_SHEET_NAMES, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        //API_ACTION_INFO_MAP.put("list_namedranges", new ActionInfo(ActionConstants.LIST_NAMEDRANGES, "list_namedranges", ActionInfo.DOC_JSON, false, false, false, false, false, false, false));
        //API_ACTION_INFO_MAP.put("read_namedrange_details", new ActionInfo(ActionConstants.READ_NAMEDRANGE_DETAILS, "read_namedrange_details", ActionInfo.DOC_JSON, false, false, false, false, false, false, false));
        API_ACTION_INFO_MAP.put("namedrange.list", new ActionInfo(ActionConstants.LIST_NAMEDRANGES, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("namedrange.content.get", new ActionInfo(ActionConstants.READ_NAMEDRANGE_DETAILS, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.content.get", new ActionInfo(ActionConstants.READ_SHEET_DETAILS, ActionInfo.SHEET_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("cell.content.get", new ActionInfo(ActionConstants.READ_CELL, ActionInfo.CELL_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.usedarea", new ActionInfo(ActionConstants.USED_INDEX, ActionInfo.SHEET_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        //API_ACTION_INFO_MAP.put("read_cells", new ActionInfo(ActionConstants.READ_CELLS, "read_cells", ActionInfo.DOC_JSON, false, false, false, false, false, false, false));
        API_ACTION_INFO_MAP.put("worksheet.csvdata.append", new ActionInfo(ActionConstants.INSERT_CSV_DATA_AT_END, ActionInfo.SHEET_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.jsondata.append", new ActionInfo(ActionConstants.INSERT_JSON_DATA_AT_END, ActionInfo.SHEET_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.records.add", new ActionInfo(ActionConstants.INSERT_JSON_DATA_AT_END, ActionInfo.SHEET_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.csvdata.set", new ActionInfo(ActionConstants.UPDATE_CSV_DATA, ActionInfo.CELL_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.jsondata.set", new ActionInfo(ActionConstants.UPDATE_JSON_DATA, ActionInfo.SHEET_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        //submit action set as range json in action object so repeating it.        
        API_ACTION_INFO_MAP.put("cell.content.set", new ActionInfo(ActionConstants.SUBMIT, ActionInfo.CELL_JSON, false, true, false, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), false, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("cells.content.set", new ActionInfo(ActionConstants.BATCH_UPDATE_BY_CELL_REF, ActionInfo.DOC_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        //API_ACTION_INFO_MAP.put("add_row_in_namedrange", new ActionInfo(ActionConstants.ADD_ROW_IN_NAMEDRANGE, null, ActionInfo.DOC_JSON, false, true, true, true, true, true, false));
        API_ACTION_INFO_MAP.put("row.content.set", new ActionInfo(ActionConstants.UPDATE_ROW_DATA, ActionInfo.SHEET_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("webhook.subscribe", new ActionInfo(ActionConstants.WEBHOOK_SUBSCRIBE, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("webhook.unsubscribe", new ActionInfo(ActionConstants.WEBHOOK_UNSUBSCRIBE, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("webhook.range.update", new ActionInfo(ActionConstants.WEBHOOK_UPDATE_RANGE, ActionInfo.RANGE_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("webhook.worksheetsheet.update", new ActionInfo(ActionConstants.WEBHOOK_UPDATE_SHEET, ActionInfo.SHEET_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        //API_ACTION_INFO_MAP.put("workbook.create", new ActionInfo(ActionConstants.NEW_WORKBOOK, ActionInfo.USER_JSON, false, false, false, false, false, false, false,new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("find", new ActionInfo(ActionConstants.SEARCH, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("recalculate", new ActionInfo(ActionConstants.RECALCULATE, ActionInfo.DOC_JSON, false, true, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), true, true, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("replace", new ActionInfo(ActionConstants.SEARCH_REPLACE, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("cell.note.set", new ActionInfo(ActionConstants.CELL_COMMENT, ActionInfo.CELL_JSON, true, false, false, false, false, true, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.upload", new ActionInfo(ActionConstants.IMPORT_CREATE_NEW, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.download", new ActionInfo(ActionConstants.EXPORT_DOCUMENT, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("namedrange.create", new ActionInfo(ActionConstants.NAMEDRANGE_ADD, ActionInfo.SHEET_JSON, false, false, false, false, false, true, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), true, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("namedrange.update", new ActionInfo(ActionConstants.NAMEDRANGE_MODIFY, ActionInfo.SHEET_JSON, false, false, false, false, false, true, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("namedrange.delete", new ActionInfo(ActionConstants.NAMEDRANGE_DELETE, ActionInfo.DOC_JSON, false, false, false, false, false, true, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.create", new ActionInfo(ActionConstants.NEW_WORKBOOK, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.share", new ActionInfo(ActionConstants.SHARE_WORKBOOK, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.externalsharelink", new ActionInfo(ActionConstants.EXTERNAL_SHARE_LINK, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.copy", new ActionInfo(ActionConstants.COPY_DOCUMENT, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.convert", new ActionInfo(ActionConstants.CHANGE_DOC_TYPE, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.createfromtemplate", new ActionInfo(ActionConstants.COPY_DOCUMENT, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.list", new ActionInfo(ActionConstants.LIST_WORKBOOKS, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("template.list", new ActionInfo(ActionConstants.LIST_WORKBOOKS, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("mergetemplate.list", new ActionInfo(ActionConstants.LIST_WORKBOOKS, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("ping", new ActionInfo(ActionConstants.API_PING, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.load", new ActionInfo(ActionConstants.LOAD_BOOK, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        
        API_ACTION_INFO_MAP.put("worksheet.records.fetch", new ActionInfo(ActionConstants.FETCH_RECORDS, ActionInfo.SHEET_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.headers.fetch", new ActionInfo(ActionConstants.LIST_HEADERS, ActionInfo.SHEET_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.records.update", new ActionInfo(ActionConstants.UPDATE_RECORDS, ActionInfo.SHEET_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.records.delete", new ActionInfo(ActionConstants.DELETE_RECORDS, ActionInfo.SHEET_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("records.columns.insert", new ActionInfo(ActionConstants.INSERT_COL, ActionInfo.SHEET_JSON, false, true, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("chart.get", new ActionInfo(ActionConstants.GET_CHARTS, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("serverclip.get", new ActionInfo(ActionConstants.SERVERCLIP_GET, ActionInfo.USER_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("ranges.format.set", new ActionInfo(ActionConstants.FORMAT, ActionInfo.DOC_JSON, false, false, true, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));

        //API_ACTION_INFO_MAP.put("images.upload", new ActionInfo(ActionConstants.IMAGE_UPLOAD, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.images.insert", new ActionInfo(ActionConstants.IMAGE_NEW, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), true, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("range.images.fit", new ActionInfo(ActionConstants.IMAGE_DISPLAY_MODE_CHANGE, ActionInfo.SHEET_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), false, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE, false, false, true, true, true, true)));

        API_ACTION_INFO_MAP.put("field.list", new ActionInfo(ActionConstants.LIST_FIELDS, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.mergefield.list", new ActionInfo(ActionConstants.LIST_FIELDS, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("field.create", new ActionInfo(ActionConstants.FIELD_ADD, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("field.update", new ActionInfo(ActionConstants.FIELD_EDIT, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("field.delete", new ActionInfo(ActionConstants.FIELD_DELETE, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("fields.create.workbooks", new ActionInfo(ActionConstants.FIELDS_DATA_NEW_WORKBOOK, ActionInfo.DOC_JSON, false, true, false, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("merge.save", new ActionInfo(ActionConstants.FIELDS_DATA_NEW_WORKBOOK, ActionInfo.DOC_JSON, false, true, false, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("fields.create.worksheets", new ActionInfo(ActionConstants.FIELDS_DATA_NEW_WORKSHEET, ActionInfo.SHEET_JSON, false, true, false, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("fields.create.pdfs", new ActionInfo(ActionConstants.FIELDS_DATA_PDF, ActionInfo.DOC_JSON, false, true, false, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("fields.mail.attachment", new ActionInfo(ActionConstants.FIELDS_DATA_MAIL_ATTACHMENT, ActionInfo.DOC_JSON, false, true, false, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("merge.email.attachment", new ActionInfo(ActionConstants.FIELDS_DATA_MAIL_ATTACHMENT, ActionInfo.DOC_JSON, false, true, false, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        
        API_ACTION_INFO_MAP.put("worksheet.copy", new ActionInfo(ActionConstants.SHEET_COPY, ActionInfo.SHEET_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, false,false)));
        API_ACTION_INFO_MAP.put("worksheet.copy.otherdoc", new ActionInfo(ActionConstants.SERVERCLIP_PASTE_SHEET, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, false,false)));
        //API_ACTION_INFO_MAP.put("worksheet.copy.createnew", new ActionInfo(ActionConstants.SHEET_COPY_CREATE_NEW_SPREADSHEET, ActionInfo.SHEET_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, false,false)));
        API_ACTION_INFO_MAP.put("range.address.get", new ActionInfo(ActionConstants.RANGE_ADDRESS, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, false,false)));
        API_ACTION_INFO_MAP.put("range.index.get", new ActionInfo(ActionConstants.RANGE_INDEX, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, false,false)));
        
        API_ACTION_INFO_MAP.put("worksheet.rows.delete", new ActionInfo(ActionConstants.DELETE_ROW, ActionInfo.SHEET_JSON, true, true, false, false, true, true, true, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_MULTIPLE_SELECTION, false,false)));
        API_ACTION_INFO_MAP.put("worksheet.rows.height", new ActionInfo(ActionConstants.HEIGHT, ActionInfo.SHEET_JSON, false, false, false, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_MULTIPLE_SELECTION, false,false), true, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE, false, false, true, true, true, false)));
        API_ACTION_INFO_MAP.put("worksheet.columns.width", new ActionInfo(ActionConstants.WIDTH, ActionInfo.SHEET_JSON, false, false, false, true, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_MULTIPLE_SELECTION, false,false), true, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE, false, false, true, true, true, false)));
        API_ACTION_INFO_MAP.put("workbook.version.create", new ActionInfo(ActionConstants.CREATE_VERSION, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true)));
        API_ACTION_INFO_MAP.put("workbook.version.list", new ActionInfo(ActionConstants.GET_VERSION_DETAILS, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), true, true, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("workbook.version.revert", new ActionInfo(ActionConstants.REVERT_VERSION, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), false, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE_SAVE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("worksheet.filter.clear", new ActionInfo(ActionConstants.APPLY_FILTER, ActionInfo.SHEET_JSON, false, true, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_SINGLE_SELECTION, false, true), true, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE, false, false, false, false, false, false)));
        API_ACTION_INFO_MAP.put("workbook.trash", new ActionInfo(ActionConstants.DOCUMENT_TRASH, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), false, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE_SAVE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("workbook.restore", new ActionInfo(ActionConstants.DOCUMENT_RESTORE, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), false, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE_SAVE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("workbook.delete", new ActionInfo(ActionConstants.DOCUMENT_DELETE, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), false, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE_SAVE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("lock", new ActionInfo(ActionConstants.LOCK_SHEET, ActionInfo.SHEET_JSON, false, false, false, false, false, true, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true,true), true, true, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE_SAVE_SHARE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("unlock", new ActionInfo(ActionConstants.UNLOCK_SHEET, ActionInfo.SHEET_JSON, false, false, false, false, false, true, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true,true), true, true, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE_SAVE_SHARE, false, false, true, true, true, true)));
        API_ACTION_INFO_MAP.put("workbook.publish", new ActionInfo(ActionConstants.MAKE_PUBLIC, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), false, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE_SAVE, false, false, false, false, false, false)));
        API_ACTION_INFO_MAP.put("workbook.publish.remove", new ActionInfo(ActionConstants.MAKE_PUBLIC, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true, true), false, false, new FeatureSuppressInfo(UserProfile.PermissionType.READ_WRITE_SAVE, false, false, false, false, false, false)));
        API_ACTION_INFO_MAP.put("table.list", new ActionInfo(ActionConstants.LIST_TABLES, ActionInfo.DOC_JSON, false, false, false, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE, true,true)));
        API_ACTION_INFO_MAP.put("table.create", new ActionInfo(ActionConstants.TABLE_CREATE, ActionInfo.RANGE_JSON, false, true, true, false,false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_SINGLE_SELECTION,false,false)));
        API_ACTION_INFO_MAP.put("table.remove", new ActionInfo(ActionConstants.TABLE_REMOVE, ActionInfo.DOC_JSON, false, false, true, false,false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_SINGLE_SELECTION,false,false)));
        API_ACTION_INFO_MAP.put("table.records.fetch", new ActionInfo(ActionConstants.GET_DATA_FROM_TABLE, ActionInfo.DOC_JSON, false, false, false, false,false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE,true,true)));
        API_ACTION_INFO_MAP.put("table.records.add", new ActionInfo(ActionConstants.TABLE_INSERT_ROW, ActionInfo.DOC_JSON, false, true, true, false,false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_MULTIPLE_SELECTION,true,false)));
        API_ACTION_INFO_MAP.put("table.records.update", new ActionInfo(ActionConstants.UPDATE_TABLE_RECORDS, ActionInfo.DOC_JSON, false, true, true, true,false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE,true,true)));
        API_ACTION_INFO_MAP.put("table.records.delete", new ActionInfo(ActionConstants.TABLE_DELETE_ROW, ActionInfo.DOC_JSON, false, true, true, false,false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_MULTIPLE_SELECTION,true,false)));
        API_ACTION_INFO_MAP.put("table.columns.insert", new ActionInfo(ActionConstants.TABLE_INSERT_COL, ActionInfo.DOC_JSON, false, true, true, false, false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_MULTIPLE_SELECTION, true, false)));
        API_ACTION_INFO_MAP.put("table.columns.delete", new ActionInfo(ActionConstants.TABLE_DELETE_COL, ActionInfo.DOC_JSON, false, true, true, false,false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_MULTIPLE_SELECTION,true,false)));
        API_ACTION_INFO_MAP.put("table.header.rename", new ActionInfo(ActionConstants.TABLE_HEADER_RENAME, ActionInfo.DOC_JSON, false, true, true, true,false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.NONE,false,false)));
//        API_ACTION_INFO_MAP.put("table.records.update", new ActionInfo(ActionConstants.GET_DATA_FROM_TABLE, ActionInfo.TABLE_JSON, true, true, true, false,false, false, false, new ActionSelectionProperties(ActionInfo.RangeSelectionType.SUPPORT_SINGLE_SELECTION,false,false)));
    }
    
    private enum ParamType
    {
        STRING,
        ROW_INDEX,
        COLUMN_INDEX,
        ROW_COLUMN_HEIGHT_WIDTH,
        JSON_ARRAY,
        JSON_OBJECT,
        RANGE,
        BOOLEAN,
        INTEGER,
        CONTENT_PARSER,
        SUBMODE
    }
    
    public static JSONObject getActionObject(HttpServletRequest request, String method)
    {
        ActionInfo actionInfo = API_ACTION_INFO_MAP.get(method);
        int action = -1;
        JSONObject actionJson = null;
        if(actionInfo == null)
        {
            action = getActionConstant(method);
            if(action == -1)
            {
                actionJson = new JSONObject();
                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.METHOD_NOT_SUPPORTED);
                return actionJson;
            }
            actionInfo = ActionObject.getActionInfo(action);
        }
        
        actionJson = actionInfo.toJSON().getJsonObject();
        action = actionJson.getInt(JSONConstants.ACTION);

        try
        {
            switch(actionJson.getInt(JSONConstants.JSON_OBJECT_TYPE))
            {
                case ActionInfo.DOC_JSON:
                    switch(action)
                    {
                        case ActionConstants.GET_CHARTS:
                            getParam(request, actionJson, "id", "linked_id", ParamType.STRING);   //No I18N
                            getOptionalParam(request, actionJson, "mode", "mode", ParamType.STRING, "IMAGE"); //No I18N
                            getOptionalParam(request, actionJson, "height", "hgt", ParamType.STRING, null); //No I18N
                            getOptionalParam(request, actionJson, "width", "wdth", ParamType.STRING, null); //No I18N
                            break;
                        case ActionConstants.SHEET_RENAME:
                            getParam(request, actionJson, "old_name", JSONConstants.OLD_SHEET_NAME, ParamType.STRING);    //No I18N
                            getParam(request, actionJson, "new_name", JSONConstants.SHEET_NAME, ParamType.STRING);    //No I18N
                            checkWorksheetNameLength(actionJson, JSONConstants.SHEET_NAME);
                            break;
                        case ActionConstants.BATCH_UPDATE_BY_CELL_REF:
                            getParam(request, actionJson, "data", JSONConstants.VALUE, ParamType.JSON_ARRAY);   //No I18N
                            break;
                        case ActionConstants.SHEET_INSERT:
                            getOptionalParam(request, actionJson, "worksheet_name", JSONConstants.NEW_SHEET_NAME, ParamType.STRING, null);  //No I18N
                            checkWorksheetNameLength(actionJson, JSONConstants.NEW_SHEET_NAME);
                            actionJson.put(JSONConstants.INSERT_AT_END, true);
                            actionJson.put(JSONConstants.IS_PIVOT_SHEET, false);
                            break;
                        case ActionConstants.NAMEDRANGE_DELETE:
                            getParam(request, actionJson, "name_of_range", JSONConstants.NAME_OF_RANGE, ParamType.STRING); //No I18N
                            break;
                        case ActionConstants.SEARCH_REPLACE:
                            //getParam(request, actionJson, "replace_with", JSONConstants.REPLACE_STRING, ParamType.STRING);  //No I18N
                            getOptionalParam(request, actionJson, "replace_with", JSONConstants.REPLACE_STRING, ParamType.STRING, null);  //No I18N
                            if(!actionJson.has(JSONConstants.REPLACE_STRING)) {
                                String value = request.getParameter("replace_with");
                                if(value != null && value.equals("")) {
                                    actionJson.put(JSONConstants.REPLACE_STRING, "");
                                } else {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                                    actionJson.put(DataAPIConstants.ARGUMENT, "replace_with");
                                    throw new Exception();
                                }
                            }
                            //no break
                        case ActionConstants.SEARCH:
                            getParam(request, actionJson, "search", JSONConstants.VALUE, ParamType.STRING); //No I18N
                            getParam(request, actionJson, "scope", JSONConstants.SCOPE, ParamType.STRING);  //No I18N
                            String scope = actionJson.getString(JSONConstants.SCOPE);
                            if(!scope.equalsIgnoreCase("workbook"))
                            {
                                //getParam(request, actionJson, "worksheet_name", JSONConstants.SHEET_NAME, ParamType.STRING);    //No I18N
                                getSheetParam(request, actionJson);
                                if(scope.equalsIgnoreCase("row"))
                                {
                                    getParam(request, actionJson, "row", JSONConstants.START_ROW, ParamType.ROW_INDEX); //No I18N
                                }
                                else if(scope.equalsIgnoreCase("column"))
                                {
                                    getParam(request, actionJson, "column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);    //No I18N
                                }
                            }
                            getOptionalParam(request, actionJson, "is_case_sensitive", JSONConstants.IS_CASE_SENSITIVE, ParamType.BOOLEAN, false);  //No I18N
                            getOptionalParam(request, actionJson, "is_exact_match", JSONConstants.IS_EXACT_MATCH, ParamType.BOOLEAN, false);    //No I18N
                            break;
                        case ActionConstants.WEBHOOK_SUBSCRIBE:
                            getParam(request, actionJson, "event", "event", ParamType.STRING);  //No I18N
                            
                            String event = actionJson.getString("event").toLowerCase();
                            if(!event.equals("new_workbook"))
                            {
                                getParam(request, actionJson, "resource_id", JSONConstants.RID, ParamType.STRING);  //No I18N
                            
                                if(event.equals("add_comment") || event.equals("delete_comment") || event.equals("edit_comment") || event.equals("resolve_comment") ||  event.equals("reply_comment"))
                                {
                                    getSheetParam(request, actionJson, true);
                                }
                                else if(!event.equals("new_worksheet"))
                                {
                                    //getParam(request, actionJson, "worksheet_name", JSONConstants.SHEET_NAME, ParamType.STRING);    //No I18N
                                    getSheetParam(request, actionJson);
                                    if(event.equals("new_row") || event.equals("update_worksheet"))
                                    {
                                        getOptionalParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX, null); //No I18N
                                    }
                                    else if(event.equals("update_range"))
                                    {
                                        getParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX);   //No I18N
                                        getParam(request, actionJson, "start_column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                                        getParam(request, actionJson, "end_row", JSONConstants.END_ROW, ParamType.ROW_INDEX);   //No I18N
                                        getParam(request, actionJson, "end_column", JSONConstants.END_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                                    }
                                    else if(event.equals("update_columns"))
                                    {
                                        getOptionalParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX, null); //No I18N
                                        getParam(request, actionJson, "start_column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                                        getParam(request, actionJson, "end_column", JSONConstants.END_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                                    }
                                }
                            }
                            
                            getParam(request, actionJson, "target_url", "url", ParamType.STRING);   //No I18N
                            getParam(request, actionJson, "service_name", "service_name", ParamType.STRING);    //No I18N
                            break;
                        case ActionConstants.WEBHOOK_UNSUBSCRIBE:
                            getOptionalParam(request, actionJson, JSONConstants.ID, JSONConstants.ID, ParamType.STRING, null);
                            getOptionalParam(request, actionJson, "resource_id", JSONConstants.RID, ParamType.STRING, null);    //No I18N
                            if(actionJson.has(JSONConstants.ID))
                            {
                                try
                                {
                                    Long.parseLong(actionJson.getString(JSONConstants.ID));
                                }
                                catch(NumberFormatException nfe)
                                {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_TYPE_ERR);
                                    actionJson.put(DataAPIConstants.ARGUMENT, JSONConstants.ID);
                                    actionJson.put(DataAPIConstants.EXPECTED, "Long");
                                    throw new Exception();
                                }
                            }
                            else        //webhook id is not available, so try to get url
                            {
                                getOptionalParam(request, actionJson, "target_url", "url", ParamType.STRING, null);                 //No I18N
                                if(!actionJson.has("url"))
                                {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                                    actionJson.put(DataAPIConstants.ARGUMENT, "id or target_url");
                                    throw new Exception();
                                }
                            }
                            break;
                        case ActionConstants.IMPORT_CREATE_NEW:
                            getOptionalParam(request, actionJson, "file_url", "file_url", ParamType.STRING, null);                      //No I18N
                            getOptionalParam(request, actionJson, "workbook_name", "workbook_name", ParamType.STRING, null);            //No I18N
                            getOptionalParam(request, actionJson, "delimiter", "delimiter", ParamType.STRING, null);                    //No I18N
                            getOptionalParam(request, actionJson, "date_format", "date_format", ParamType.STRING, null);                //No I18N
                            getOptionalParam(request, actionJson, "service_name", "service_name", ParamType.STRING, null);              //No I18N
                            getOptionalParam(request, actionJson, "import_option", "import_option", ParamType.STRING, "import_as_new_workbook");    //No I18N
                            getOptionalParam(request, actionJson, "resource_id", JSONConstants.RID, ParamType.STRING, "import_as_new_workbook");    //No I18N
                            break;
                        case ActionConstants.IMAGE_NEW:
                            getParam(request, actionJson, "image_json", "image_json", ParamType.JSON_ARRAY);    //No I18N
                            break;
                        case ActionConstants.EXPORT_DOCUMENT:
                            getParam(request, actionJson, "format", "format", ParamType.STRING);                                        //No I18N
                            getOptionalParam(request, actionJson, "worksheet_name", JSONConstants.SHEET_NAME, ParamType.STRING, null);  //No I18N
                            getOptionalParam(request, actionJson, "page_settings", "page_settings", ParamType.JSON_OBJECT, null);  //No I18N
                            if(actionJson.has("page_settings")) {
                                JSONObject pageSettings = actionJson.getJSONObject("page_settings");
                                String printType = pageSettings.optString("printType", pageSettings.optString("print_type", null));  //No I18N
                                if("RANGE".equals(printType)) {
                                    getParam(request, actionJson, "range", "range", ParamType.STRING);  //No I18N
                                }
                            }
                            getOptionalParam(request, actionJson, "range", "range", ParamType.STRING, null);                            //No I18N
                            getOptionalParam(request, actionJson, "version", JSONConstants.VERSION_NO, ParamType.INTEGER, null); 		//No I18N
                            getOptionalParam(request, actionJson, "password", "password", ParamType.STRING, null);  //No I18N
                            break;
                        case ActionConstants.READ_NAMEDRANGE_DETAILS:
                            getParam(request, actionJson, "name_of_range", JSONConstants.NAME_OF_RANGE, ParamType.STRING); //No I18N
                            break;
                        case ActionConstants.READ_CELLS:
                            getParam(request, actionJson, "cells", JSONConstants.VALUE, ParamType.JSON_ARRAY);  //No I18N
                            break;
                        case ActionConstants.NEW_WORKBOOK:
                            getParam(request, actionJson, "workbook_name", "workbook_name", ParamType.STRING);  //No I18N
                            getOptionalParam(request, actionJson, "parentid", "parentid", ParamType.STRING, null);  //No I18N
                            if(!actionJson.has("parentid")) {
                                getOptionalParam(request, actionJson, "parent_id", "parentid", ParamType.STRING, null);  //No I18N
                            }
                            getOptionalParam(request, actionJson, "resource_type", "resource_type", ParamType.INTEGER, ResourceType.WORKBOOK_NATIVE);  //No I18N
                            break;
                        case ActionConstants.SHARE_WORKBOOK:
                            getParam(request, actionJson, "resource_id", JSONConstants.RID, ParamType.STRING);  //No I18N
                            getParam(request, actionJson, "share_json", "share_json", ParamType.JSON_ARRAY);  //No I18N
                            break;
                        case ActionConstants.EXTERNAL_SHARE_LINK:
                        	getParam(request, actionJson, "resource_id", JSONConstants.RID, ParamType.STRING); //No I18N
			                getParam(request, actionJson, "link_name", "link_name", ParamType.STRING); //No I18N
			                getParam(request, actionJson, "access_level", "access_level", ParamType.STRING); //No I18N
			                getOptionalParam(request, actionJson, "allow_download", "allow_download", ParamType.BOOLEAN, true); //No I18N
			                getOptionalParam(request, actionJson, "expiration_date", "expiration_date", ParamType.STRING, ""); //No I18N
			                getOptionalParam(request, actionJson, "password", "password", ParamType.STRING, ""); //No I18N
			                getOptionalParam(request, actionJson, "request_user_data", "request_user_data", ParamType.STRING, ""); //No I18N
                        	break;
                        case ActionConstants.COPY_DOCUMENT:
                            getParam(request, actionJson, "resource_id", JSONConstants.RID, ParamType.STRING);  //No I18N
                            getOptionalParam(request, actionJson, "workbook_name", "workbook_name", ParamType.STRING, null);    //No I18N
                            getOptionalParam(request, actionJson, "parent_id", "parentid", ParamType.STRING, null);  //No I18N
                            getOptionalParam(request, actionJson, "copy_lock_settings", "persist_protection", ParamType.BOOLEAN, true);  //No I18N
                            break;
                        case ActionConstants.LIST_WORKBOOKS:
                            getOptionalParam(request, actionJson, "start_index", "start_index", ParamType.INTEGER, null);       //No I18N
                            getOptionalParam(request, actionJson, "sort_option", "sort_option", ParamType.STRING, null);        //No I18N
                            if(actionJson.has("start_index"))
                            {
                                int startIndex = actionJson.getInt("start_index");
                                if(startIndex < 1)
                                {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_VALUE_ERR);
                                    actionJson.put(DataAPIConstants.ARGUMENT, "start_index");
                                    actionJson.put(DataAPIConstants.EXPECTED, "0 to 1000000");
                                    throw new Exception();
                                }
                            }
                            getOptionalParam(request, actionJson, "count", "count", ParamType.INTEGER, null);  //No I18N
                            if(actionJson.has("count"))
                            {
                                int count = actionJson.getInt("count");
                                if(count < 1 || count > 1000)
                                {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_VALUE_ERR);
                                    actionJson.put(DataAPIConstants.ARGUMENT, "count");
                                    actionJson.put(DataAPIConstants.EXPECTED, "1 to 1000");
                                    throw new Exception();
                                }
                            }
                            break;
                        case ActionConstants.API_PING:  //to be called only if webhook event is new_row or updated_worksheet
                            getParam(request, actionJson, "event", "event", ParamType.STRING);  //No I18N
                            event = actionJson.getString("event").toLowerCase();
                            getParam(request, actionJson, "resource_id", JSONConstants.RID, ParamType.STRING);  //No I18N
                            if(event.equals("new_row") || event.equals("update_worksheet"))
                            {
                                getSheetParam(request, actionJson);
                            }
                            if(event.equals("update_columns"))
                            {
                                getSheetParam(request, actionJson);
                                getParam(request, actionJson, "start_column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                                actionJson.put(JSONConstants.END_COLUMN, actionJson.getInt(JSONConstants.START_COLUMN));
                            }
                            break;
                        case ActionConstants.FORMAT:
                            getParam(request, actionJson, "format_json", "format_json", ParamType.JSON_ARRAY);  //No I18N
                            break;
                        case ActionConstants.CHANGE_DOC_TYPE:
                            break;
                        case ActionConstants.FIELD_ADD:
                            getParam(request, actionJson, "field_names", JSONConstants.FIELD_NAMES_JSON, ParamType.JSON_ARRAY); //No I18N
                            //getOptionalParam(request, actionJson, "cells", "cells", ParamType.JSON_ARRAY, null);  //No I18N
                            break;
                        case ActionConstants.FIELD_EDIT:
                        	getParam(request, actionJson, "new_field_name", JSONConstants.NEW_FIELD_NAME, ParamType.STRING);  //No I18N
                            getOptionalParam(request, actionJson, "cells", "cells", ParamType.JSON_ARRAY, null);  //No I18N
                            //no break;
                        case ActionConstants.FIELD_DELETE:
                            getOptionalParam(request, actionJson, "field_name", JSONConstants.FIELD_NAME, ParamType.STRING, null); //No I18N
                            getOptionalParam(request, actionJson, "field_id", JSONConstants.ID, ParamType.INTEGER, null);   //No I18N
                            if(!(actionJson.has(JSONConstants.FIELD_NAME) || actionJson.has(JSONConstants.ID)))
                            {
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                                actionJson.put(DataAPIConstants.ARGUMENT, "field_name or field_id");
                            }
                            break;
                        case ActionConstants.FIELDS_DATA_NEW_WORKBOOK:
                        case ActionConstants.FIELDS_DATA_MAIL_ATTACHMENT:
                            if("fields.create.workbooks".equals(method) || "fields.mail.attachment".equals(method)) {
                                getParam(request, actionJson, "merge_configuration", JSONConstants.MERGE_CONFIGURATION, ParamType.JSON_OBJECT); //No I18N
//                                getParam(request, actionJson, "fields", "fields", ParamType.JSON_ARRAY);                                   //No I18N
                            } else {
                                getParam(request, actionJson, "merge_data", "merge_data", ParamType.JSON_ARRAY);    //No I18N
                                if("merge.save".equals(method)) {

                                    getParam(request, actionJson, "output_settings", "output_settings", ParamType.JSON_OBJECT); //No I18N
                                } else if("merge.email.attachment".equals(method)) {
                                    getParam(request, actionJson, "email_settings", "email_settings", ParamType.JSON_OBJECT);   //No I18N
                                }
                            }
                            break;
                        //case ActionConstants.FIELDS_DATA_PDF:
                            //getParam(request, actionJson, "fields_data", JSONConstants.VALUE, ParamType.JSON_ARRAY);            //No I18N
                            //getOptionalParam(request, actionJson, "pdf_file_name", "pdf_file_name", ParamType.STRING, null);    //No I18N
                            //getOptionalParam(request, actionJson, "folder_id", "folder_id", ParamType.STRING, null);            //No I18N
                            //break;
                        //case ActionConstants.FIELDS_DATA_MAIL_ATTACHMENT:
                            //getParam(request, actionJson, "fields_data", JSONConstants.VALUE, ParamType.JSON_ARRAY);            //No I18N
                            //getParam(request, actionJson, "file_format", "file_format", ParamType.STRING);                      //No I18N
                            //getParam(request, actionJson, "recipients", "recipients", ParamType.STRING);                        //No I18N
                            //getOptionalParam(request, actionJson, "subject", "subject", ParamType.STRING, null);                //No I18N
                            //getOptionalParam(request, actionJson, "message", JSONConstants.MESSAGE, ParamType.STRING, null);    //No I18N
                            //getOptionalParam(request, actionJson, "send_me_a_copy", "send_me_a_copy", ParamType.BOOLEAN, false);//No I18N
                            //break;
//                        case ActionConstants.ADD_ROW_IN_NAMEDRANGE:
//                            getParam(request, actionJson, "range_name", JSONConstants.NAME_OF_RANGE, ParamType.STRING); //No I18N
//                            getParam(request, actionJson, "data", null, ParamType.CONTENT_PARSER);  //No I18N
//                            actionJson.remove(JSONConstants.END_ROW);
//                            actionJson.remove(JSONConstants.END_COLUMN);
//                            break;
                        case ActionConstants.RANGE_ADDRESS:
                        	getParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX);   //No I18N
                            getParam(request, actionJson, "start_column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                            getParam(request, actionJson, "end_row", JSONConstants.END_ROW, ParamType.ROW_INDEX);   //No I18N
                            getParam(request, actionJson, "end_column", JSONConstants.END_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                            break;
                        case ActionConstants.RANGE_INDEX:
                        	getParam(request, actionJson, "range_address", JSONConstants.VALUE, ParamType.RANGE);   //No I18N
                            break;
                        case ActionConstants.SERVERCLIP_PASTE_SHEET:
                            getParam(request, actionJson, "source_resource_id", JSONConstants.SOURCE_RID, ParamType.STRING);    //No I18N
                            String sourceWorksheetName = request.getParameter("source_worksheet_name");
                            String paramName = "source_worksheet_name";    //No I18N
                            String key = JSONConstants.SOURCE_SHEET_NAME;
                            if(sourceWorksheetName == null || sourceWorksheetName.equals(""))
                            {
                                String sasn = request.getParameter("source_worksheet_id");
                                if(sasn == null || sasn.equals(""))
                                {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                                    actionJson.put(DataAPIConstants.ARGUMENT, "source_worksheet_name or source_worksheet_id");
                                    throw new Exception();
                                }
                                paramName = "source_worksheet_id";         //No I18N
                                key = JSONConstants.ASSOCIATED_SOURCE_SHEET_NAME;
                            }
                            getParam(request, actionJson, paramName, key, ParamType.STRING);
                            String sourceResourceId = null;

                            try {
                                sourceResourceId = actionJson.getString(JSONConstants.SOURCE_RID);
                                String spaceId = ZohoFS.getSpaceId(sourceResourceId);
                                String docOwner = DocumentUtils.getZUserName(spaceId);
                                Map map = DocumentUtils.getDocumentDetails(sourceResourceId, docOwner);
                                actionJson.put(JSONConstants.SOURCE_DOCID, String.valueOf(map.get("DOCUMENT_ID")));
                                actionJson.put(JSONConstants.SOURCE_DOC_OWNER, String.valueOf(map.get("AUTHOR_NAME")));
                            } catch (Exception e) {
                                LOGGER.log(Level.INFO, "error while getting source document details in server clip paste sheet via api action :: sourceResourceId : {0}", sourceResourceId);
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_RESOURCE_ID);
                                throw new Exception();
                            }
                            //check the read access for source resource here
                            String userZuid = String.valueOf(IAMUtil.getCurrentUser().getZUID());
                            boolean canRead = false;
                            org.json.JSONObject previlages = null;
                            if(request.getAttribute(ZFSNGConstants.CAPABILITIES) != null) {
                                previlages = new org.json.JSONObject(((String) request.getAttribute(ZFSNGConstants.CAPABILITIES)));
                                //LOGGER.log(Level.INFO, "previlages : {0}", previlages.toString());
                                canRead = previlages.getBoolean("canRead");
                            } else {
                                LOGGER.log(Level.INFO, "source document capability is null in apiactionobject for serverclippaste case");
                            }
                            if(!canRead) {
                                LOGGER.log(Level.INFO, "User do not have previlages to read :: srid : {0} :: zuid : {1}", new Object[]{sourceResourceId, userZuid});
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.AUTH_FAIL_ACCESS_ERR);
                                throw new Exception();
                            }
                            boolean isOnlyOrgPublished = false;
                            boolean isOnlyPublished = false;
                            if (request.getAttribute(ZFSNGConstants.IS_ONLY_ORGPUBLISHED) != null) {
                                isOnlyOrgPublished = (Boolean) request.getAttribute(ZFSNGConstants.IS_ORGPUBLISHED);
                                LOGGER.log(Level.INFO, "isOnlyOrgPublished : {0}", isOnlyOrgPublished);
                            }
                            if (request.getAttribute(ZFSNGConstants.IS_ONLY_PUBLISHED) != null) {
                                isOnlyPublished = (Boolean)request.getAttribute(ZFSNGConstants.IS_PUBLISHED);
                                LOGGER.log(Level.INFO, "IS_PUBLISHED not null : {0}", isOnlyPublished);
                            }

                            if(isOnlyOrgPublished || isOnlyPublished) {
                                //user only with published access - cannot copy in this case
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.AUTH_FAIL_OPERATION_ERR);
                                throw new Exception();
                            }
                            break;
//                        case ActionConstants.RECALCULATE:
//                            actionJson.put("premium_api", true);
//                            break;
                        case ActionConstants.CREATE_VERSION:
                            getParam(request,actionJson,"version_description","version_description",ParamType.STRING); //No I18N
                            break;
                        case ActionConstants.REVERT_VERSION:
                            getParam(request,actionJson,"version_number",JSONConstants.REVERT_VERSION_NO,ParamType.STRING);  //No I18N
                            break;
                        case ActionConstants.DOCUMENT_TRASH:
                        case ActionConstants.DOCUMENT_RESTORE:
                        case ActionConstants.DOCUMENT_DELETE:
                            getParam(request, actionJson, "resource_ids", "resource_ids", ParamType.JSON_ARRAY);  //No I18N
                            break;
                        case ActionConstants.MAKE_PUBLIC:
                            getOptionalParam(request, actionJson, "publish_options", "publish_options", ParamType.JSON_OBJECT, null);  //No I18N
                            getParam(request, actionJson, "resource_id", JSONConstants.RID, ParamType.STRING);  //No I18N
                            getParam(request, actionJson, "publish_type", "publish_type", ParamType.STRING);    //No I18N
                            String publish_type = actionJson.getString("publish_type");
                            if(!publish_type.equalsIgnoreCase("workbook"))
                            {
                                getSheetParam(request, actionJson);
                                if(publish_type.equalsIgnoreCase("range"))
                                {
                                    getParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX);   //No I18N
                                    getParam(request, actionJson, "start_column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                                    getParam(request, actionJson, "end_row", JSONConstants.END_ROW, ParamType.ROW_INDEX);   //No I18N
                                    getParam(request, actionJson, "end_column", JSONConstants.END_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                                }
                            }
                            break;
                        case ActionConstants.TABLE_REMOVE:
                            getTableParam(request, actionJson);
                            getOptionalParam(request, actionJson, "clear_format", JSONConstants.CLEAR_FORMATTING, ParamType.BOOLEAN, false);    //No I18N
                            break;
                        case ActionConstants.GET_DATA_FROM_TABLE:
                            getTableParam(request, actionJson);
                            getOptionalParam(request, actionJson, "column_names", JSONConstants.VALUE, ParamType.CONTENT_PARSER, null);     //No I18N
                            getOptionalParam(request, actionJson, "criteria_json", JSONConstants.CRITERIA_JSON, ParamType.JSON_ARRAY, null);    //No I18N
                            if(actionJson.has(JSONConstants.CRITERIA_JSON))
                            {
                                int len = actionJson.getJSONArray(JSONConstants.CRITERIA_JSON).length();
                                if(len > 1 && len <=5) {
                                    getParam(request, actionJson, "criteria_pattern", JSONConstants.CRITERIA_PATTERN, ParamType.STRING);    //No I18N
                                } else if(len > 5) {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.CRITERIA_LIMIT_EXCEEDED);
                                    throw new Exception();
                                } else if(len == 0) {
                                    actionJson.remove(JSONConstants.CRITERIA_JSON);
                                }
                            }
                            getOptionalParam(request, actionJson, "match_case", JSONConstants.IS_EXACT_MATCH, ParamType.BOOLEAN, false);            //No I18N
                            getOptionalParam(request, actionJson, "render_option", "render_option", ParamType.STRING, "unformatted");               //No I18N
                            getOptionalParam(request, actionJson, "is_case_sensitive", JSONConstants.IS_CASE_SENSITIVE, ParamType.BOOLEAN, true);   //No I18N
                            actionJson.remove(JSONConstants.RANGELIST);
                            break;
                        case ActionConstants.UPDATE_TABLE_RECORDS:
                            getTableParam(request, actionJson);
                            getParam(request, actionJson, "data", JSONConstants.VALUE, ParamType.JSON_OBJECT);  //No I18N
                            getOptionalParam(request, actionJson, "criteria_json", JSONConstants.CRITERIA_JSON, ParamType.JSON_ARRAY, null);    //No I18N
                            if(actionJson.has(JSONConstants.CRITERIA_JSON))
                            {
                                int len = actionJson.getJSONArray(JSONConstants.CRITERIA_JSON).length();
                                if(len > 1 && len <=5) {
                                    getParam(request, actionJson, "criteria_pattern", JSONConstants.CRITERIA_PATTERN, ParamType.STRING);    //No I18N
                                } else if(len > 5) {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.CRITERIA_LIMIT_EXCEEDED);
                                    throw new Exception();
                                } else if(len == 0) {
                                    actionJson.remove(JSONConstants.CRITERIA_JSON);
                                }
                            }
                            getOptionalParam(request, actionJson, "first_match_only", JSONConstants.IS_FIRST_MATCH, com.zoho.sheet.api.dataapi.APIActionObject.ParamType.BOOLEAN, false);  //No I18N
                            getOptionalParam(request, actionJson, "is_case_sensitive", JSONConstants.IS_CASE_SENSITIVE, com.zoho.sheet.api.dataapi.APIActionObject.ParamType.BOOLEAN, true);   //No I18N
                            break;
                        case ActionConstants.TABLE_DELETE_ROW:
                            getTableParam(request, actionJson);
                            getParam(request, actionJson, "criteria_json", JSONConstants.CRITERIA_JSON, ParamType.JSON_ARRAY);    //No I18N
                            if(actionJson.has(JSONConstants.CRITERIA_JSON))
                            {
                                int len = actionJson.getJSONArray(JSONConstants.CRITERIA_JSON).length();
                                if(len > 1 && len <=5) {
                                    getParam(request, actionJson, "criteria_pattern", JSONConstants.CRITERIA_PATTERN, ParamType.STRING);    //No I18N
                                } else if(len > 5) {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.CRITERIA_LIMIT_EXCEEDED);
                                    throw new Exception();
                                } else if(len == 0) {
                                    actionJson.remove(JSONConstants.CRITERIA_JSON);
                                }
                            }
                            getOptionalParam(request, actionJson, "first_match_only", JSONConstants.IS_FIRST_MATCH, ParamType.BOOLEAN, false);  //No I18N
                            getOptionalParam(request, actionJson, "is_case_sensitive", JSONConstants.IS_CASE_SENSITIVE, ParamType.BOOLEAN, true);   //No I18N
                            break;
                        case ActionConstants.TABLE_INSERT_COL:
                            getTableParam(request, actionJson);
                            getParam(request, actionJson, "column_names", JSONConstants.TABLE_HEADER_NAMES, ParamType.JSON_ARRAY);  //No I18N
                            getOptionalParam(request, actionJson, "insert_column_after", JSONConstants.INSERT_COLUMN_AFTER, ParamType.STRING, null);  //No I18N
                            if(actionJson.getJSONArray(JSONConstants.TABLE_HEADER_NAMES).length() <= 0)
                            {
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                                actionJson.put(DataAPIConstants.ARGUMENT, "column_names");
                                throw new Exception();
                            }
                            break;
                        case ActionConstants.TABLE_DELETE_COL:
                            getTableParam(request, actionJson);
                            getParam(request, actionJson, "column_names", JSONConstants.TABLE_HEADER_NAMES, ParamType.JSON_ARRAY);    //No I18N
                            break;
                        case ActionConstants.TABLE_INSERT_ROW:
                            getTableParam(request, actionJson);
                            getParam(request, actionJson, "json_data", JSONConstants.VALUE, ParamType.JSON_ARRAY);  //No I18N
                            getOptionalParam(request, actionJson, "insert_at_top", "insert_at_top", ParamType.BOOLEAN, false);  //No I18N
                            actionJson.put(JSONConstants.COUNT, actionJson.getJSONArray(JSONConstants.VALUE).length());
                            break;
                        case ActionConstants.TABLE_HEADER_RENAME:
                            getTableParam(request, actionJson);
                            getParam(request, actionJson, "data", JSONConstants.VALUE, ParamType.JSON_ARRAY);  //No I18N
                            break;
                    }
                    break;
                case ActionInfo.SHEET_JSON:
                    //getParam(request, actionJson, "worksheet_name", JSONConstants.SHEET_NAME, ParamType.STRING);    //No I18N
                    getSheetParam(request, actionJson);
                    switch(action)
                    {
                        case ActionConstants.UPDATE_ROW_DATA:
                            getParam(request, actionJson, "column_array", JSONConstants.COLUMN_FIELDS, ParamType.JSON_ARRAY);    //No I18N
                            getParam(request, actionJson, "data_array", JSONConstants.VALUE_ARRAY, ParamType.JSON_ARRAY);   //No I18N
                            if(actionJson.getJSONArray(JSONConstants.COLUMN_FIELDS).length() != actionJson.getJSONArray(JSONConstants.VALUE_ARRAY).length())
                            {
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_COLUMN_DATA_ARRAY);
                                throw new Exception();
                            }
                            //No break
                        case ActionConstants.WEBHOOK_UPDATE_SHEET:
                            getParam(request, actionJson, "row", JSONConstants.START_ROW, ParamType.ROW_INDEX); //No I18N
                            actionJson.put(JSONConstants.END_ROW, actionJson.getInt(JSONConstants.START_ROW));
                            break;
                        case ActionConstants.NAMEDRANGE_ADD:
                        case ActionConstants.NAMEDRANGE_MODIFY:
                            actionJson.put(JSONConstants.START_ROW, 0);
                            actionJson.put(JSONConstants.START_COLUMN, 0);
//                            actionJson.put(JSONConstants.END_ROW, 0);
//                            actionJson.put(JSONConstants.END_COLUMN, 0);
                            getParam(request, actionJson, "name_of_range", JSONConstants.NAME_OF_RANGE, ParamType.STRING); //No I18N
                            getParam(request, actionJson, "range", JSONConstants.VALUE, ParamType.RANGE);   //No I18N
                            break;
                        case ActionConstants.INSERT_CSV_DATA_AT_END:
                            actionJson.put(JSONConstants.START_COLUMN, 0);  //Always start from first column
                            getParam(request, actionJson, "data", null, ParamType.CONTENT_PARSER);  //No I18N
                            actionJson.put(JSONConstants.COUNT, actionJson.getInt(JSONConstants.END_ROW) + 1);
                            actionJson.remove(JSONConstants.END_ROW);
                            break;
                        case ActionConstants.INSERT_JSON_DATA_AT_END:
                            getParam(request, actionJson, "json_data", JSONConstants.VALUE, ParamType.JSON_ARRAY);  //No I18N
                            actionJson.put(JSONConstants.COUNT, actionJson.getJSONArray(JSONConstants.VALUE).length());
                            getOptionalParam(request, actionJson, "header_row", JSONConstants.HEADER_ROW, ParamType.ROW_INDEX, 0);  //No I18N
                            getOptionalParam(request, actionJson, "decode_data", "decode_data", ParamType.BOOLEAN, false);  //No I18N
                            if(isPremiumAPI(request)) {
                                actionJson.put("premium_api", true);
                                actionJson.put("decode_data", true);
                            }
                            break;
                        case ActionConstants.UPDATE_JSON_DATA:
                            getParam(request, actionJson, "json_data", JSONConstants.VALUE, ParamType.JSON_ARRAY);                  //No I18N
                            getOptionalParam(request, actionJson, "header_row", JSONConstants.HEADER_ROW, ParamType.ROW_INDEX, 0);  //No I18N
                            break;
                        case ActionConstants.READ_SHEET_DETAILS:
                            String range = request.getParameter(JSONConstants.RANGE);
                            if(!(range == null || range.equals("")))
                            {
//                                try
//                                {
//                                    String[] cellAddress = range.split(":");
//                                    actionJson.put(JSONConstants.START_ROW, CellUtil.getRow(cellAddress[0]));
//                                    actionJson.put(JSONConstants.START_COLUMN, CellUtil.getColumn(cellAddress[0]));
//                                    actionJson.put(JSONConstants.END_ROW, CellUtil.getRow(cellAddress[1]));
//                                    actionJson.put(JSONConstants.END_COLUMN, CellUtil.getColumn(cellAddress[1]));
//                                }
//                                catch(Exception e)
//                                {
//                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_VALUE_ERR);
//                                    actionJson.put(DataAPIConstants.ARGUMENT, "range");
//                                    actionJson.put(DataAPIConstants.EXPECTED, "Range string like A1:D4");
//                                    throw new Exception();
//                                }
                                getParam(request, actionJson, "range", JSONConstants.RANGE, ParamType.STRING); //No I18N
                            }
                            else
                            {
                                getOptionalParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX, null); //No I18N
                                getOptionalParam(request, actionJson, "start_column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX, null); //No I18N
                                getOptionalParam(request, actionJson, "end_row", JSONConstants.END_ROW, ParamType.ROW_INDEX, null); //No I18N
                                getOptionalParam(request, actionJson, "end_column", JSONConstants.END_COLUMN, ParamType.COLUMN_INDEX, null); //No I18N
                            }
                            getOptionalParam(request, actionJson, "visible_rows_only", JSONConstants.VISIBLE_ROWS_ONLY, ParamType.BOOLEAN, false);       //No I18N
                            getOptionalParam(request, actionJson, "visible_columns_only", JSONConstants.VISIBLE_COLUMNS_ONLY, ParamType.BOOLEAN, false);       //No I18N
                            getOptionalParam(request, actionJson, "response_type", "response_type", ParamType.STRING, "default");       //No I18N
                            if(actionJson.getString("response_type").equals("array"))
                            {
                                getParam(request, actionJson, "major_dimension", "dimension", ParamType.STRING);                        //No I18N
                            }
                            break;
                        case ActionConstants.IMAGE_DISPLAY_MODE_CHANGE:
                            getParam(request, actionJson, "image_fit_option", JSONConstants.MODE, ParamType.STRING);    //No I18N
                            int imageMode = getImageMode(actionJson, "image_fit_option");   //No I18N
                            actionJson.put(JSONConstants.IMAGE_DISPLAY_MODE, imageMode);
                            actionJson.remove(JSONConstants.MODE);

                            getOptionalParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX, null); //No I18N
                            getOptionalParam(request, actionJson, "start_column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX, null); //No I18N
                            getOptionalParam(request, actionJson, "end_row", JSONConstants.END_ROW, ParamType.ROW_INDEX, null); //No I18N
                            getOptionalParam(request, actionJson, "end_column", JSONConstants.END_COLUMN, ParamType.COLUMN_INDEX, null); //No I18N
                            break;
                        case ActionConstants.FETCH_RECORDS:
                        case ActionConstants.LIST_HEADERS:
                            getOptionalParam(request, actionJson, "header_row", JSONConstants.HEADER_ROW, ParamType.ROW_INDEX, 0);  //No I18N
                            getOptionalParam(request, actionJson, "start_index", "start_index", ParamType.ROW_INDEX, null);//not to be used, user should use records start index. start index refers start row index only.  //No I18N
                            getOptionalParam(request, actionJson, "records_start_index", "records_start_index", ParamType.INTEGER, null);  //No I18N
//                            getOptionalParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX, actionJson.getInt(JSONConstants.HEADER_ROW) + 1); //No I18N
//                            if((actionJson.getInt(JSONConstants.START_ROW) - actionJson.getInt(JSONConstants.HEADER_ROW)) < 0)
//                            {
//                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_VALUE_ERR);
//                                actionJson.put(DataAPIConstants.ARGUMENT, "start_row");
//                                actionJson.put(DataAPIConstants.EXPECTED, "greater than header_row");
//                                throw new Exception();
//                            }
                            getOptionalParam(request, actionJson, "count", JSONConstants.COUNT, ParamType.INTEGER, null); //No I18N
                            
                            int count = actionJson.optInt(JSONConstants.COUNT, 1);
                            if(count < 1)
                            {
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_VALUE_ERR);
                                actionJson.put(DataAPIConstants.ARGUMENT, "count");
                                actionJson.put(DataAPIConstants.EXPECTED, "greater than zero");
                                throw new Exception();
                            }

                            if(isPremiumAPI(request)) { //premium API
                                if(count > 200) {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_VALUE_ERR);
                                    actionJson.put(DataAPIConstants.ARGUMENT, "count");
                                    actionJson.put(DataAPIConstants.EXPECTED, "1 to 200");
                                    throw new Exception();
                                } else if(!actionJson.has(JSONConstants.COUNT)) {
                                    actionJson.put(JSONConstants.COUNT, 200);   //max value or default value for premium API
                                }
                                getOptionalParam(request, actionJson, "criteria_json", JSONConstants.CRITERIA_JSON, ParamType.JSON_ARRAY, null);    //No I18N
                                if(actionJson.has(JSONConstants.CRITERIA_JSON))
                                {
                                    int len = actionJson.getJSONArray(JSONConstants.CRITERIA_JSON).length();
                                    if(len > 1 && len <=5) {
                                        getParam(request, actionJson, "criteria_pattern", JSONConstants.CRITERIA_PATTERN, ParamType.STRING);    //No I18N
                                    } else if(len > 5) {
                                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.CRITERIA_LIMIT_EXCEEDED);
                                        throw new Exception();
                                    } else if(len == 0) {
                                        actionJson.remove(JSONConstants.CRITERIA_JSON);
                                    }
                                }
                                actionJson.put("premium_api", true);
                            }
                            
                            //special handling for deluge
                            String userAgent = request.getHeader("User-Agent");
                            if("Deluge".equals(userAgent))
                            {
                                int c = actionJson.optInt(JSONConstants.COUNT, 1001);
                                if(c > 1000)
                                {
                                    actionJson.put(JSONConstants.COUNT, 1000);  //Max value of default value for deluge
                                }
                            }
                            
                            getOptionalParam(request, actionJson, "column_names", JSONConstants.VALUE, ParamType.CONTENT_PARSER, null);                            //No I18N
                            getOptionalParam(request, actionJson, "criteria", JSONConstants.CRITERIA, ParamType.STRING, null);                      //No I18N
                            getOptionalParam(request, actionJson, "match_case", JSONConstants.IS_EXACT_MATCH, ParamType.BOOLEAN, false);            //No I18N
                            getOptionalParam(request, actionJson, "render_option", "render_option", ParamType.STRING, "unformatted");               //No I18N
                            getOptionalParam(request, actionJson, "is_case_sensitive", JSONConstants.IS_CASE_SENSITIVE, ParamType.BOOLEAN, true);   //No I18N
                            actionJson.remove(JSONConstants.RANGELIST);
                            break;
                        case ActionConstants.UPDATE_RECORDS:
                            getParam(request, actionJson, "data", JSONConstants.VALUE, ParamType.JSON_OBJECT);  //No I18N
                            getOptionalParam(request, actionJson, "header_row", JSONConstants.HEADER_ROW, ParamType.ROW_INDEX, 0);  //No I18N
                            if(isPremiumAPI(request)) {
                                //getOptionalParam(request, actionJson, "criteria_json", JSONConstants.CRITERIA, ParamType.STRING, null);      //No I18N
                                getOptionalParam(request, actionJson, "criteria_json", JSONConstants.CRITERIA_JSON, ParamType.JSON_ARRAY, null);    //No I18N
                                if(actionJson.has(JSONConstants.CRITERIA_JSON))
                                {
                                    int len = actionJson.getJSONArray(JSONConstants.CRITERIA_JSON).length();
                                    if(len > 1 && len <=5) {
                                        getParam(request, actionJson, "criteria_pattern", JSONConstants.CRITERIA_PATTERN, ParamType.STRING);    //No I18N
                                    } else if(len > 5) {
                                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.CRITERIA_LIMIT_EXCEEDED);
                                        throw new Exception();
                                    } else if(len == 0) {
                                        actionJson.remove(JSONConstants.CRITERIA_JSON);
                                    }
                                }
                                getOptionalParam(request, actionJson, "row_index", JSONConstants.START_ROW, ParamType.ROW_INDEX, null);      //No I18N
                                if(!(actionJson.has(JSONConstants.CRITERIA_JSON) || actionJson.has(JSONConstants.START_ROW)))
                                {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                                    actionJson.put(DataAPIConstants.ARGUMENT, "criteria or row_index");
                                    throw new Exception();
                                }
                                actionJson.put("premium_api", true);
                            }
                            else {
                                getOptionalParam(request, actionJson, "criteria", JSONConstants.CRITERIA, ParamType.STRING, null);      //No I18N
                            }
                            getOptionalParam(request, actionJson, "first_match_only", JSONConstants.IS_FIRST_MATCH, ParamType.BOOLEAN, false);  //No I18N
                            getOptionalParam(request, actionJson, "is_case_sensitive", JSONConstants.IS_CASE_SENSITIVE, ParamType.BOOLEAN, true);   //No I18N
                            break;
                        case ActionConstants.DELETE_RECORDS:
                            getOptionalParam(request, actionJson, "header_row", JSONConstants.HEADER_ROW, ParamType.ROW_INDEX, 0);  //No I18N
                            getOptionalParam(request, actionJson, "criteria", JSONConstants.CRITERIA, ParamType.STRING, null);      //No I18N
                            getOptionalParam(request, actionJson, "row_array", JSONConstants.ROW_FIELDS, ParamType.JSON_ARRAY, null);    //No I18N
                            if(!(actionJson.has(JSONConstants.CRITERIA) || actionJson.has(JSONConstants.ROW_FIELDS)))
                            {
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                                actionJson.put(DataAPIConstants.ARGUMENT, "criteria or row_array");
                                throw new Exception();
                            }
                            getOptionalParam(request, actionJson, "first_match_only", JSONConstants.IS_FIRST_MATCH, ParamType.BOOLEAN, false);  //No I18N
                            getOptionalParam(request, actionJson, "is_case_sensitive", JSONConstants.IS_CASE_SENSITIVE, ParamType.BOOLEAN, true);   //No I18N
                            getOptionalParam(request, actionJson, "delete_rows", JSONConstants.DELETE_ROWS, ParamType.BOOLEAN, false);   //No I18N
                            break;
                        case ActionConstants.HEIGHT:
                            getOptionalParam(request, actionJson, "auto_fit", JSONConstants.IS_OPTIMAL, ParamType.BOOLEAN, false);  //No I18N
                            boolean isOptimal = actionJson.getBoolean(JSONConstants.IS_OPTIMAL);
                            if (isOptimal) {
                                actionJson.put(JSONConstants.VALUE, -1);
                            } else {
                                getParam(request, actionJson, "row_height", JSONConstants.VALUE, ParamType.ROW_COLUMN_HEIGHT_WIDTH);    //No I18N
                                String size = String.valueOf(actionJson.getInt(JSONConstants.VALUE));
                                String rowHeight = EngineUtils1.convertPixelsToInches(size, EngineConstants.ROWDPI);
                                actionJson.put(JSONConstants.VALUE, rowHeight);
                            }
                            //do not break
                        case ActionConstants.DELETE_ROW:
                            getParam(request, actionJson, "row_index_array", JSONConstants.ROW_INDEX_ARRAY, ParamType.JSON_ARRAY);  //No I18N
                            break;
                        case ActionConstants.WIDTH:
                            getOptionalParam(request, actionJson, "auto_fit", JSONConstants.IS_OPTIMAL, ParamType.BOOLEAN, false);  //No I18N
                            isOptimal = actionJson.getBoolean(JSONConstants.IS_OPTIMAL);
                            if (isOptimal) {
                                actionJson.put(JSONConstants.VALUE, -1);
                            } else {
                                getParam(request, actionJson, "column_width", JSONConstants.VALUE, ParamType.ROW_COLUMN_HEIGHT_WIDTH);  //No I18N
                                String size = String.valueOf(actionJson.getInt(JSONConstants.VALUE));
                                String rowHeight = EngineUtils1.convertPixelsToInches(size, EngineConstants.ROWDPI);
                                actionJson.put(JSONConstants.VALUE, rowHeight);
                            }
                            getParam(request, actionJson, "column_index_array", "col_index_array", ParamType.JSON_ARRAY);  //No I18N
                            break;
                        case ActionConstants.INSERT_COL:
                            getParam(request, actionJson, "column_names", JSONConstants.COLUMN_NAME, ParamType.JSON_ARRAY);  //No I18N
                            if(actionJson.getJSONArray(JSONConstants.COLUMN_NAME).length() <= 0)
                            {
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                                actionJson.put(DataAPIConstants.ARGUMENT, "column_names");
                                throw new Exception();
                            }
                            getOptionalParam(request, actionJson, "header_row", JSONConstants.HEADER_ROW, ParamType.ROW_INDEX, 0);  //No I18N
                            getOptionalParam(request, actionJson, "insert_column_after", JSONConstants.INSERT_COLUMN_AFTER, ParamType.STRING, null);	//No I18N
                            break;
                        case ActionConstants.FIELDS_DATA_NEW_WORKSHEET:
                            getParam(request, actionJson, "fields_data", JSONConstants.VALUE, ParamType.JSON_ARRAY);    //No I18N
                            getOptionalParam(request, actionJson, "new_worksheet_name", JSONConstants.NEW_SHEET_NAME, ParamType.STRING, null);    //No I18N
                            break;
                        case ActionConstants.SHEET_COPY:
                            getOptionalParam(request, actionJson, "new_worksheet_name", JSONConstants.NEW_SHEET_NAME, ParamType.STRING, null);  //No I18N
                            getOptionalParam(request, actionJson, "insert_after", JSONConstants.INSERT_AFTER, ParamType.STRING, null);          //No I18N
                            checkWorksheetNameLength(actionJson, JSONConstants.NEW_SHEET_NAME);
                            break;
                        case ActionConstants.APPLY_FILTER:
                            actionJson.put(JSONConstants.FILTER_TYPE,ActionConstants.APPLY_FILTER_ALL);
                            actionJson.put(JSONConstants.FILTER_INDEX,-1);
                            break;
                        case ActionConstants.LOCK_SHEET:
                        case ActionConstants.UNLOCK_SHEET:
                            getParam(request, actionJson, "scope", JSONConstants.SCOPE, ParamType.STRING);  //No I18N
                            String scope = actionJson.getString(JSONConstants.SCOPE);
                            if(!scope.equalsIgnoreCase("worksheet"))
                            {
                                getParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX);   //No I18N
                                getParam(request, actionJson, "start_column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                                getParam(request, actionJson, "end_row", JSONConstants.END_ROW, ParamType.ROW_INDEX);   //No I18N
                                getParam(request, actionJson, "end_column", JSONConstants.END_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                                checkRangeData(actionJson);
                            }
                            getOptionalParam(request, actionJson, "user_emails", "user_emails", ParamType.JSON_ARRAY, null);    //No I18N
                            getOptionalParam(request, actionJson, "external_share_links", "external_share_links", ParamType.JSON_ARRAY, null);    //No I18N
                            if(!(actionJson.has("user_emails") || actionJson.has("external_share_links")))
                            {
                                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                                actionJson.put(DataAPIConstants.ARGUMENT, "user_emails or external_share_links");
                            }
                            break;
                    }
                    break;
                case ActionInfo.CELL_JSON:
                    //getParam(request, actionJson, "worksheet_name", JSONConstants.SHEET_NAME, ParamType.STRING);    //No I18N
                    getSheetParam(request, actionJson);
                    getParam(request, actionJson, "row", JSONConstants.START_ROW, ParamType.ROW_INDEX); //No I18N
                    getParam(request, actionJson, "column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);    //No I18N
                    actionJson.put(JSONConstants.END_ROW, actionJson.getInt(JSONConstants.START_ROW));
                    actionJson.put(JSONConstants.END_COLUMN, actionJson.getInt(JSONConstants.START_COLUMN));
                    switch(action)
                    {
                        case ActionConstants.SUBMIT:
                            getParam(request, actionJson, "content", JSONConstants.VALUE, ParamType.STRING);    //No I18N
                            String content = actionJson.getString(JSONConstants.VALUE);
                            actionJson.put(JSONConstants.VALUE, Utility.getEncodedString(content));
                            actionJson.put(JSONConstants.PARSE_PICKLIST_VALUE, true);
                            break;
                        case ActionConstants.UPDATE_CSV_DATA:
                            getParam(request, actionJson, "data", null, ParamType.CONTENT_PARSER);  //No I18N
                            getOptionalParam(request, actionJson, "ignore_empty", JSONConstants.IGNORE_EMPTY, ParamType.BOOLEAN, false);   //No I18N
                            break;
                        case ActionConstants.CELL_COMMENT:
                            actionJson.put("m", "add");     //m represents mode which is either add or remove
                            getParam(request, actionJson, "note", JSONConstants.VALUE, ParamType.STRING);    //No I18N
                            break;
                    }
                    break;
                case ActionInfo.RANGE_JSON:
                    //getParam(request, actionJson, "worksheet_name", JSONConstants.SHEET_NAME, ParamType.STRING);    //No I18N
                    getSheetParam(request, actionJson);
                    getParam(request, actionJson, "start_row", JSONConstants.START_ROW, ParamType.ROW_INDEX);   //No I18N
                    getParam(request, actionJson, "start_column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                    getParam(request, actionJson, "end_row", JSONConstants.END_ROW, ParamType.ROW_INDEX);   //No I18N
                    getParam(request, actionJson, "end_column", JSONConstants.END_COLUMN, ParamType.COLUMN_INDEX);  //No I18N
                    checkRangeData(actionJson);
                    switch(action)
                    {
                        case ActionConstants.HYPERLINK:
                            getParam(request, actionJson, "sub_mode", "m", ParamType.SUBMODE);   //No I18N
                            
                            if(!actionJson.getString("m").equals("remove"))
                            {
                                getParam(request, actionJson, "url", "l", ParamType.STRING);    //No I18N
                                getParam(request, actionJson, "content", "la", ParamType.STRING);   //No I18N
                            }
                            break;
                        case ActionConstants.READ_RANGE_DETAILS:
                            getOptionalParam(request, actionJson, "with_format", "with_format", ParamType.BOOLEAN, false);  //No I18N
                            getOptionalParam(request, actionJson, "with_merge_info", "with_merge_info", ParamType.BOOLEAN, false);  //No I18N
                            break;
                        case ActionConstants.TABLE_CREATE:
                            getOptionalParam(request, actionJson, "contains_header", JSONConstants.CONTAINS_HEADER, ParamType.BOOLEAN, true);    //No I18N
                            getOptionalParam(request, actionJson, "table_style", JSONConstants.TABLE_STYLE, ParamType.JSON_OBJECT, null);    //No I18N
                            boolean containsHeader = actionJson.getBoolean(JSONConstants.CONTAINS_HEADER);
                            if(!containsHeader) { //false
                                getOptionalParam(request, actionJson, "header_names", JSONConstants.TABLE_HEADER_NAMES, ParamType.JSON_ARRAY, null);    //No I18N
                                if(actionJson.has(JSONConstants.TABLE_HEADER_NAMES) && actionJson.getJSONArray(JSONConstants.TABLE_HEADER_NAMES) != null){
                                    int tableSize = actionJson.getInt(JSONConstants.END_COLUMN)-actionJson.getInt(JSONConstants.START_COLUMN)+1;
                                    int colSize = actionJson.getJSONArray(JSONConstants.TABLE_HEADER_NAMES).length();
                                    if(tableSize!=colSize){
                                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_HEADER_ROW);
                                        throw new Exception();
                                    }
                                }
                            }
                            break;
                    }
                    break;
                case ActionInfo.ROW_JSON:
                    //getParam(request, actionJson, "worksheet_name", JSONConstants.SHEET_NAME, ParamType.STRING);    //No I18N
                    getSheetParam(request, actionJson);
                    getParam(request, actionJson, "row", JSONConstants.START_ROW, ParamType.ROW_INDEX); //No I18N
                    switch(action)
                    {
                        case ActionConstants.INSERT_ROW:
                            getOptionalParam(request, actionJson, "json_data", JSONConstants.VALUE, ParamType.JSON_ARRAY, null);    //No I18N
                            getOptionalParam(request, actionJson, "header_row", JSONConstants.HEADER_ROW, ParamType.ROW_INDEX, 0);    //No I18N
                            getOptionalParam(request, actionJson, "decode_data", "decode_data", ParamType.BOOLEAN, false);  //No I18N
                            int count = 1;  //by default 1 row to be inserted
                            //if(actionJson.has(JSONConstants.VALUE) && (actionJson.getInt(JSONConstants.START_ROW) <= actionJson.getInt(JSONConstants.HEADER_ROW)))
                            if(actionJson.has(JSONConstants.VALUE))
                        	{
                                if((actionJson.getInt(JSONConstants.START_ROW) <= actionJson.getInt(JSONConstants.HEADER_ROW)))
                                {
                                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_HEADER_ROW);
                                    throw new Exception();
                                }
                                count = actionJson.getJSONArray(JSONConstants.VALUE).length();
                        	}
                            actionJson.put(JSONConstants.START_COLUMN, 0);
                            actionJson.put(JSONConstants.END_COLUMN, Utility.MAXNUMOFCOLS-1);
                            actionJson.put(JSONConstants.END_ROW, actionJson.getInt(JSONConstants.START_ROW) + count - 1);
                            break;
                        case ActionConstants.DELETE_ROW:
                            actionJson.put(JSONConstants.START_COLUMN, 0);
                            actionJson.put(JSONConstants.END_COLUMN, Utility.MAXNUMOFCOLS-1);
                            actionJson.put(JSONConstants.END_ROW, actionJson.getInt(JSONConstants.START_ROW));
                            break;
                    }
                    break;
                case ActionInfo.COL_JSON:
                    //getParam(request, actionJson, "worksheet_name", JSONConstants.SHEET_NAME, ParamType.STRING);    //No I18N
                    getSheetParam(request, actionJson);
                    getParam(request, actionJson, "column", JSONConstants.START_COLUMN, ParamType.COLUMN_INDEX);    //No I18N
                    switch(action)
                    {
                        case ActionConstants.INSERT_COL:
                        case ActionConstants.DELETE_COL:
                            actionJson.put(JSONConstants.START_ROW, 0);
                            actionJson.put(JSONConstants.END_ROW, Utility.MAXNUMOFCOLS-1);
                            actionJson.put(JSONConstants.END_COLUMN, actionJson.getInt(JSONConstants.START_COLUMN));
                            break;
                    }
                    break;
                case ActionInfo.USER_JSON:
                    switch(action)
                    {
                        case ActionConstants.SERVERCLIP_GET:
                            getParam(request, actionJson, "type", "type", ParamType.STRING);    //No I18N
                            break;
                    }
                    break;
            }
            
            actionJson.put(JSONConstants.IS_REPEAT_ACTION, false);
            actionJson.put(JSONConstants.RECORD_UNDO, false);
            actionJson.put(DataAPIConstants.METHOD, method);
            actionJson.put(JSONConstants.API_ACTION, true);
            actionJson.put(JSONConstants.RSID, -1);
            //actionJson.putOpt("api_version", version);  //No I18N
        }
        catch(Exception e)
        {
            //LOGGER.log(Level.INFO, "Exception in action object", e);
            if(!actionJson.has(DataAPIConstants.ERROR_CODE))
            {
                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_PARAM_ERR);
            }
        }
        return actionJson;
    }
    
    private static void getParam(HttpServletRequest request, JSONObject actionJson, String paramName, String key, ParamType paramType) throws Exception
    {
        String value = request.getParameter(paramName);
        if(value == null || value.equals(""))
        {
            actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
            actionJson.put(DataAPIConstants.ARGUMENT, paramName);
            throw new Exception();
        }
        else
        {
            switch(paramType)
            {
                case STRING:
                    actionJson.put(key, value);
                    break;
                case INTEGER:
                    int intValue = -1;
                    try
                    {
                        intValue = Integer.parseInt(value);
                    }
                    catch(NumberFormatException e)
                    {
                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_TYPE_ERR);
                        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
                        actionJson.put(DataAPIConstants.EXPECTED, "Integer");
                        throw e;
                    }
                    actionJson.put(key, intValue);
                    break;
                case ROW_INDEX:
                case COLUMN_INDEX:
                    //int intValue = -1;
                    try
                    {
                        intValue = Integer.parseInt(value);
                    }
                    catch(NumberFormatException e)
                    {
                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_TYPE_ERR);
                        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
                        actionJson.put(DataAPIConstants.EXPECTED, "Integer");
                        throw e;
                    }
                    int maxValue = (paramType == ParamType.ROW_INDEX) ? Utility.MAXNUMOFROWS : Utility.MAXNUMOFCOLS;
                    if(intValue <=0 || intValue > maxValue)
                    {
                        
                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_VALUE_ERR);
                        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
                        actionJson.put(DataAPIConstants.EXPECTED, "1 to "+maxValue);
                        throw new Exception();
                    }
                    actionJson.put(key, intValue - 1);
                    break;
                case ROW_COLUMN_HEIGHT_WIDTH:
                    try
                    {
                        intValue = Integer.parseInt(value);
                    }
                    catch(NumberFormatException e)
                    {
                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_TYPE_ERR);
                        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
                        actionJson.put(DataAPIConstants.EXPECTED, "Integer");
                        throw e;
                    }
                    maxValue = 2000; //2000 is max value for row column height width
                    if(intValue <=0 || intValue > maxValue)
                    {

                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_VALUE_ERR);
                        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
                        actionJson.put(DataAPIConstants.EXPECTED, "0 to "+maxValue);
                        throw new Exception();
                    }
                    actionJson.put(key, intValue);
                    break;
                case BOOLEAN:
                    actionJson.put(key, Boolean.valueOf(value));
                    break;
                case JSON_ARRAY:
                    try
                    {
                        actionJson.put(key, new JSONArray(value));
                    }
                    catch(Exception e)
                    {
                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_TYPE_ERR);
                        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
                        actionJson.put(DataAPIConstants.EXPECTED, "JSON Array");
                        throw e;
                    }
                    break;
                case JSON_OBJECT:
                    try
                    {
                        actionJson.put(key, new JSONObject(value));
                    }
                    catch(Exception e)
                    {
                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_TYPE_ERR);
                        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
                        actionJson.put(DataAPIConstants.EXPECTED, "JSON Object");
                        throw e;
                    }
                    break;
                case RANGE:
                    String[] cellAddrs = value.split(":");
                    if(cellAddrs.length != 2)
                    {
                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_RANGE_REFERENCE);
                        throw new Exception();
                    }
                    StringBuilder sb = new StringBuilder();
                    for(int i=0; i<2; i++)
                    {
                        String cellAddr = cellAddrs[i];
                        int row = CellUtil.getRow(cellAddr);
                        if(row == -1)
                        {
                            actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_RANGE_REFERENCE);
                            throw new Exception();
                        }
                        int column = CellUtil.getColumn(cellAddr);
                        if(column == -1)
                        {
                            actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_RANGE_REFERENCE);
                            throw new Exception();
                        }
                        sb.append(CellUtil.getCellReference(row, column, true, true)).append(":");
                    }
                    String reConstructRangeAddr = sb.substring(0, sb.length() -1);
                    actionJson.put(key, reConstructRangeAddr);
                    break;
                case CONTENT_PARSER:
                    //value = value.replaceAll("\"\"", "\"");
                    ContentParser parser = new ContentParser(',');
                    try
                    {
                        ActionObject.getDataUsingContentParser(parser, new JSONObjectWrapper(actionJson), value, actionJson.has(JSONConstants.START_ROW) ? actionJson.getInt(JSONConstants.START_ROW) : 0, actionJson.has(JSONConstants.START_COLUMN) ? actionJson.getInt(JSONConstants.START_COLUMN) : 0);
                    }
                    catch(ProhibitedActionException pae)
                    {
                        if(pae.getMessage().equals(ErrorCode.ERROR_ROW_LIMIT_EXCEED))
                        {
                            actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.ROW_LIMIT_EXCEEDED);
                        }
                        else
                        {
                            actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.COLUMN_LIMIT_EXCEEDED);
                        }
                        throw pae;
                    }
                    catch(Exception e)
                    {
                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_DATA);
                        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
                        throw e;
                    }
                    break;
                case SUBMODE:
                    if(!(value.equals("add") || value.equals("remove")))
                    {
                        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.WRONG_VALUE_ERR);
                        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
                        actionJson.put(DataAPIConstants.EXPECTED, "add or remove");
                        throw new Exception();
                    }
                    actionJson.put(key, value);
                    break;
            }
        }
    }
    
    private static void getOptionalParam(HttpServletRequest request, JSONObject actionJson, String paramName, String key, ParamType paramType, Object defaultValue) throws Exception
    {
        try
        {
            getParam(request, actionJson, paramName, key, paramType);
        }
        catch(Exception e)
        {
            if(actionJson.has(DataAPIConstants.ERROR_CODE) && (actionJson.getInt(DataAPIConstants.ERROR_CODE) == APIErrorHandler.MISSING_PARAM_ERR))
            {
                if(defaultValue != null)
                {
                    actionJson.put(key, defaultValue);
                }
                actionJson.remove(DataAPIConstants.ERROR_CODE);
                actionJson.remove(DataAPIConstants.ARGUMENT);
                actionJson.remove(DataAPIConstants.EXPECTED);
            }
            else
            {
                throw e;
            }
        }
    }
    
    private static void getSheetParam(HttpServletRequest request, JSONObject actionJson) throws Exception
    {
        getSheetParam(request, actionJson, false);
    }
    
    private static void getSheetParam(HttpServletRequest request, JSONObject actionJson, boolean isOptional) throws Exception
    {
        String worksheetName = request.getParameter("worksheet_name");
        String paramName = "worksheet_name";    //No I18N
        String key = JSONConstants.SHEET_NAME;
        if(worksheetName == null || worksheetName.equals(""))
        {
            String asn = request.getParameter("worksheet_id");
            if(asn == null || asn.equals(""))
            {
                if(isOptional)
                {
                    return;
                }
                else
                {
                    actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                    actionJson.put(DataAPIConstants.ARGUMENT, "worksheet_name or worksheet_id");
                    throw new Exception();
                }
            }
            paramName = "worksheet_id";         //No I18N
            key = JSONConstants.ASSOCIATED_SHEET_NAME;
        }
        getParam(request, actionJson, paramName, key, ParamType.STRING);
    }

    private static void getTableParam(HttpServletRequest request, JSONObject actionJson) throws Exception
    {
        String tableName = request.getParameter("table_name");
        String paramName = "table_name";    //No I18N
        if(tableName == null || tableName.equals(""))
        {
            String tableId = request.getParameter("table_id");
            if(tableId == null || tableId.equals(""))
            {
                actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.MISSING_PARAM_ERR);
                actionJson.put(DataAPIConstants.ARGUMENT, "table_name or table_id");
                throw new Exception();
            }
            paramName = "table_id";         //No I18N
        }
        getParam(request, actionJson, paramName, paramName, ParamType.STRING);
    }

    private static void checkRowData(JSONObject actionJson) throws Exception
    {
        if((actionJson.getInt(JSONConstants.END_ROW) - actionJson.getInt(JSONConstants.START_ROW)) < 0)
        {
            actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_ROWS);
            throw new Exception();
        }
    }
    
    private static void checkColumnData(JSONObject actionJson) throws Exception
    {
        if((actionJson.getInt(JSONConstants.END_COLUMN) - actionJson.getInt(JSONConstants.START_COLUMN)) < 0)
        {
            actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_COLUMNS);
            throw new Exception();
        }
    }
    
    private static void checkRangeData(JSONObject actionJson) throws Exception
    {
        checkRowData(actionJson);
        checkColumnData(actionJson);
    }
    
    public static int getActionConstant(String method)
    {
        switch(method)
        {
        	case "worksheet.jsondata.insert":
        	case "row.insert":
                return ActionConstants.INSERT_ROW;
            case "row.delete":
                return ActionConstants.DELETE_ROW;
            case "column.insert":
                return ActionConstants.INSERT_COL;
            case "column.delete":
                return ActionConstants.DELETE_COL;
            case "range.content.get":
                return ActionConstants.READ_RANGE_DETAILS;
            case "range.content.clear":
                return ActionConstants.CLEARCONTENTS;
            case "range.clear":
                return ActionConstants.CLEARALL;
            case "worksheet.insert":
                return ActionConstants.SHEET_INSERT;
            case "worksheet.delete":
                return ActionConstants.SHEET_REMOVE;
            case "worksheet.rename":
                return ActionConstants.SHEET_RENAME;
            case "chart.get":
                return ActionConstants.GET_CHARTS;
        }
        return -1;
    }

    private static void checkWorksheetNameLength(JSONObject jsonObject, String key) throws Exception
    {
        if(jsonObject.has(key) && jsonObject.getString(key).length() > 31)
        {
            jsonObject.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.SHEET_NAME_LENGTH_EXCEEDED);
            throw new Exception();
        }
    }

    private static int getImageMode(JSONObject actionJson, String paramName) throws Exception{
        //String mode = actionJson.getString(JSONConstants.MODE);
        int mode = getImageMode(actionJson.getString(JSONConstants.MODE));
        if(mode != -1)
        {
            return mode;
        }
        actionJson.put(DataAPIConstants.ERROR_CODE, APIErrorHandler.INVALID_DATA);
        actionJson.put(DataAPIConstants.ARGUMENT, paramName);
        throw new Exception();
    }

    private static boolean isPremiumAPI(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return (uri.contains("/fetchrecords") || uri.contains("/addrecords") || uri.contains("/updaterecords"));  //No I18N
    }

    public static int getImageMode(String mode) {
        if(mode != null) {
            switch (mode) {
                case "fit":
                    return 1;
                case "stretch":
                    return 2;
                case "cover":
                    return 3;
            }
        }
        return -1;
    }
    
    public static ActionInfo getActionInfo(String method) {
        return API_ACTION_INFO_MAP.get(method);
    }
}
