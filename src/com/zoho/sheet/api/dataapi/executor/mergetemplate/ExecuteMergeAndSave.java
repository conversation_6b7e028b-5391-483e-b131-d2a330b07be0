/* $Id$ */

package com.zoho.sheet.api.dataapi.executor.mergetemplate;

import com.adventnet.zoho.websheet.model.field.MergeConfiguration;
import com.adventnet.zoho.websheet.model.field.MergeConfiguration.MergeType;
import com.adventnet.zoho.websheet.model.field.util.MergeDataUtils;
import com.adventnet.zoho.websheet.model.field.util.MergeJobUtils;
import com.adventnet.zoho.websheet.model.field.util.MergeTemplateUtils;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.officeplatform.lib.sku.client.SkuJob;
import com.zoho.officeplatform.lib.sku.constants.SkuAgentConstants;
import com.zoho.sheet.api.dataapi.DataAPIResponse;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;


import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ExecuteMergeAndSave implements Runnable{

    public static final Logger LOGGER = Logger.getLogger(ExecuteMergeAndSave.class.getName());

    WorkbookContainer container;
    SkuJob skuJob;
    MergeType mergeType;
    Map<String, String> fieldNameIDMap;
    JSONArray fieldsDataArray;
    String docOwner;
    String userName;
    String userZuid;
    String folderID;
    String newWorkbookName;
    String versionNo;
    long jobID;
    int startIndex = 0;

    public ExecuteMergeAndSave(WorkbookContainer container, SkuJob skuJob, MergeType mergeType, Map<String, String> fieldNameIDMap, JSONArray fieldsDataArray, String docOwner, String userName, String userZuid, String folderID, String newWorkbookName, String versionNo, long jobID, int startIndex)
    {
        this.container = container;
        this.skuJob = skuJob;
        this.mergeType = mergeType;
        this.fieldNameIDMap = fieldNameIDMap;
        this.fieldsDataArray = fieldsDataArray;
        this.docOwner = docOwner;
        this.userName = userName;
        this.userZuid = userZuid;
        this.folderID = folderID;
        this.newWorkbookName = newWorkbookName;
        this.versionNo = versionNo;
        this.jobID = jobID;
        this.startIndex = startIndex;
    }

    public void run() {
        runMerge(container, skuJob, mergeType, fieldNameIDMap, fieldsDataArray, docOwner, userName, userZuid, folderID, newWorkbookName, versionNo, jobID, startIndex);
    }

    private static void runMerge(WorkbookContainer container, SkuJob skuJob, MergeType mergeType, Map<String, String> fieldNameIDMap, JSONArray fieldsDataArray, String docOwner, String userName, String userZuid, String folderID, String newWorkbookName, String versionNo, long jobID, int startIndex) {

        String rid = container.getResourceId();
        LOGGER.log(Level.INFO, "start of run merge : rid : {0}", rid);
        long t1 = System.currentTimeMillis();
        int mergeJobStatus = 0;
        JSONObject redisObj = new JSONObject();
        redisObj.put("merge_job_id", jobID);
        redisObj.put("sku_job_id", skuJob.getJobId());
        boolean isScheduledJob = !mergeType.equals(MergeType.ONLY_ONCE);
        String redisFieldStr = isScheduledJob ? rid +"_"+ jobID : rid;

        try {
            int recordsCount = fieldsDataArray.length();
            ArrayList<String> workbookNameArray = new ArrayList<>();

            boolean isJobCompleted = false;

            for (int i = startIndex; i < recordsCount; i++) {
                LOGGER.log(Level.INFO, "processing record : {0}", i);
                mergeJobStatus = MergeJobUtils.getMergeJobStatus(docOwner, jobID);
                if (mergeJobStatus == DataAPIConstants.STATUS_ABORT) {
                    LOGGER.log(Level.INFO, "Merge job aborted by user");
                    JSONArray abortedJobs = new JSONArray();
                    //create aborted json in loop
                    for (int j = i; j < recordsCount; j++) {
                        JSONObject abortedJson = new JSONObject();
                        abortedJson.put("record_index", j + 1);
                        abortedJson.put("status", DataAPIConstants.STATUS_ABORT);
                        abortedJson.put("merge_action", MergeConfiguration.MergeAction.CREATE_NEW_FILE.toString().toLowerCase());
                        abortedJobs.put(abortedJson);
                    }
                    MergeJobUtils.addCreateFileMergeJobsToDB(docOwner, jobID, abortedJobs);
                    break;
                }

                String newRID = "";
                long t2 = System.currentTimeMillis();
                try {
                    String newDocName = MergeTemplateUtils.generateNewName(fieldsDataArray, fieldNameIDMap, workbookNameArray, newWorkbookName, "", i);
                    //Creating new workbook
                    newRID = DocumentUtils.copyDocument(container.getDocId(), container.getDocOwner(), container.getDocOwnerZUID(), newDocName, userName, userZuid, userName, userZuid, false, folderID, rid, false, false, null, null, UserProfile.AccessType.AUTH, true, versionNo, false, null, false, false, new JSONObjectWrapper(fieldsDataArray.getJSONObject(i)));
                    LOGGER.log(Level.INFO, "new container :: rid : {0}", new Object[]{newRID});
                    if (newRID == null) {
                        throw new Exception("new rid is null");
                    }
                    JSONObject newBookJson = DataAPIResponse.getNewBookJson(MergeDataUtils.RESOURCE_URL + newRID, newRID, newDocName);
                    newBookJson.put("record_index", i + 1);
                    newBookJson.put("status", DataAPIConstants.STATUS_COMPLETED);
                    MergeJobUtils.addCreateFileMergeJobToDB(docOwner, jobID, newBookJson);
                    skuJob.commitUsage(SkuAgentConstants.getSkuIdByName("save_as_new"), 1);         //No I18N
                    MergeTemplateUtils.updateMergeStatusInRedis(redisObj, redisFieldStr, i+1);
                    if(i==(recordsCount-1)) {     //last record
                        isJobCompleted = true;
                    }
                } catch (Exception e) {
                    LOGGER.log(Level.INFO, "error while creating new document in ", e);
                    JSONObject failureJson = new JSONObject();
                    failureJson.put("record_index", i + 1);
                    failureJson.put("status", DataAPIConstants.STATUS_ERROR_CREATE_DOC);
                    MergeJobUtils.addCreateFileMergeJobToDB(docOwner, jobID, failureJson);
                    MergeTemplateUtils.updateMergeStatusInRedis(redisObj, redisFieldStr, i+1);
                    continue;
                }
                LOGGER.log(Level.INFO, "time taken to create new workbook: {0}", System.currentTimeMillis() - t2);
            }
            if (mergeJobStatus != DataAPIConstants.STATUS_ABORT || isJobCompleted) {  //do not update if already aborted else update if the last record has been merged
                MergeJobUtils.updateMergeJobStatus(docOwner, jobID, DataAPIConstants.STATUS_COMPLETED, false);
            }
        }
        catch (Exception e) {
            LOGGER.log(Level.INFO, "error while running merge in ExecuteMergeAndSave", e);
            try {
                MergeJobUtils.updateMergeJobStatus(docOwner, jobID, DataAPIConstants.STATUS_ERROR_INTERNAL);
            } catch (Exception ex) {
                LOGGER.log(Level.INFO, "error in catch", ex);
            }
        }
        finally {
            MergeJobUtils.completeJob(skuJob, redisFieldStr);
            if(isScheduledJob) {
                MergeDataUtils.deleteScheduledMergeDataFile(container, jobID);
            }
        }

        LOGGER.log(Level.INFO, "total time taken to execute fields data action: {0}", System.currentTimeMillis() - t1);
    }
}
