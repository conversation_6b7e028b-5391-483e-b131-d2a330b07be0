/* $Id$ */

package com.zoho.sheet.api.dataapi;

import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.util.ContainerSynchronizer;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.net.HttpURLConnection;
import java.net.URL;
import org.json.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.InputStreamBody;

/**
 *
 * <AUTHOR>
 */
public class APIRequestResponse
{
    private static final Logger LOGGER = Logger.getLogger(APIRequestResponse.class.getName());
    
    private enum Status
    {
        TEST_OK,
        TEST_FAILED,
        NOT_TESTED
    }

    private int responseStatus;
    private final String method;
    private final String apiName;
    private final String apiUrl;
    private Status status;
    private JSONObject responseObject;
    private final Map<String, String> parameters;
    private boolean isMultiPart = false;
    private String multipartFileURL = null;
    private String multipartParam = null;
    private String multipartFileName = null;

    private List<String> exceptionErrorCodes = new ArrayList<>();
    
    public APIRequestResponse(String method, String apiName, String apiUrl, Map<String, String> parameters)
    {
        this.responseStatus = -1;
        this.method = method;
        this.apiName = apiName;
        this.apiUrl = apiUrl;
        this.status = Status.NOT_TESTED;
        this.responseObject = null;
        this.parameters = parameters;
    }

    public APIRequestResponse(String method, String apiName, String apiUrl, Map<String, String> parameters, boolean isMultiPart, String multipartFileURL, String multipartParam, String multipartFileName)
    {
        this(method,apiName,apiUrl,parameters);
        this.isMultiPart = isMultiPart;
        this.multipartFileURL = multipartFileURL;
        this.multipartParam = multipartParam;
        this.multipartFileName = multipartFileName;
    }
    
    private void setResponseStatus(int responseStatus)
    {
        this.responseStatus = responseStatus;
    }
    
    protected int getResponseStatus()
    {
        return this.responseStatus;
    }

    protected void addToExceptionErrorCodes(String errorCode) {
        exceptionErrorCodes.add(errorCode);
    }

    protected boolean isExceptedCode(String errorCode) {
        return exceptionErrorCodes.contains(errorCode);
    }
    
    private void setStatus(Status status)
    {
        this.status = status;
    }
    
    protected String getStatus()
    {
        return this.status.toString();
    }
    
    private void setResponseObject(JSONObject responseObject)
    {
        this.responseObject = responseObject;
    }
    
    protected JSONObject getResponseObject()
    {
        return this.responseObject;
    }
    
    protected String getMethod()
    {
        return this.method;
    }
    
    protected String getAPIName()
    {
        return this.apiName;
    }

    protected String getParameters()
    {
        StringBuilder parametersBuilder = new StringBuilder();
        parametersBuilder.append("method").append("=").append(this.method);
        
        parameters.forEach((key, value) -> 
        {
            parametersBuilder.append("&").append(key).append("=").append(value);
        });
        return parametersBuilder.toString();
    }
    
    protected void execute(String accessToken)
    {
        if(isMultiPart){
            try {
                byte[] fileContent;
                URL imgUrl = new URL(multipartFileURL);
                HttpURLConnection connection = (HttpURLConnection) imgUrl.openConnection();
                connection.setReadTimeout(30000);
                connection.setConnectTimeout(30000);
                connection.connect();
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                IOUtils.copy(connection.getInputStream(), baos);
                fileContent = baos.toByteArray();
                ContentBody cd = new InputStreamBody(new ByteArrayInputStream(fileContent), multipartFileName);
                MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
                multipartEntityBuilder.addPart(multipartParam, cd);
                multipartEntityBuilder.addTextBody("method",this.method);   //No I18N
                parameters.forEach((key, value) ->
                {
                    multipartEntityBuilder.addTextBody(key,value);
                });
                URL url = new URL(this.apiUrl);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                HttpEntity entity = multipartEntityBuilder.build();
                conn.setConnectTimeout(30000);
                conn.setReadTimeout(30000);
                conn.setRequestMethod("POST");  //No I18N
                conn.setDoInput(true);
                conn.setDoOutput(true);
                conn.setRequestProperty("Connection", "Keep-Alive");    //No I18N
                conn.setRequestProperty("Authorization", accessToken);  //No I18N
                conn.addRequestProperty("Content-length", entity.getContentLength() + "");  //No I18N
                conn.addRequestProperty(entity.getContentType().getName(), entity.getContentType().getValue());

                OutputStream os = conn.getOutputStream();
                entity.writeTo(conn.getOutputStream());
                os.close();
                conn.connect();

                JSONObject responseObj = new JSONObject();
                InputStream responseStream = null;

                int responseStatus = conn.getResponseCode();
                if(responseStatus == JSONConstants.RESEND_RESPONSE)
                {
                    String tempCookie = conn.getHeaderField("Set-Cookie");      //No I18N
                    responseObj.put("cookie", tempCookie);
                }
                responseObj.put("rs", responseStatus);
                //Get Response
                responseStream = (responseStatus < 400) ? conn.getInputStream() : conn.getErrorStream();
                //TODO : we may need to check for all outputs
                String responseBody = IOUtils.toString(responseStream, "UTF-8");    //No I18N
                responseObj.put("rb", responseBody);
                //setResponseObject(responseObj.getJSONObject(DataAPIConstants.RESPONSE_BODY));
                setResponseObject(new JSONObject(responseObj.get(DataAPIConstants.RESPONSE_BODY).toString()));
                setResponseStatus(responseStatus);
                setStatus(responseStatus < 300 ? Status.TEST_OK : Status.TEST_FAILED);
            }
            catch (IOException ioe) {
                LOGGER.log(Level.WARNING, "IOE exception in APIRequestResponse ", ioe);
                JSONObject respObj = new JSONObject();
                respObj.put(DataAPIConstants.ERROR_MESSAGE, ioe.getMessage());
                this.responseObject = respObj;
                setStatus(Status.TEST_FAILED);
            } catch (Exception e){
                LOGGER.log(Level.WARNING, "Exception in APIRequestResponse ", e);
                JSONObject respObj = new JSONObject();
                respObj.put(DataAPIConstants.ERROR_MESSAGE, e.getMessage());
                this.responseObject = respObj;
                setStatus(Status.TEST_FAILED);
            }
        }
        else {
            String parametersAsString = this.getParameters();
            LOGGER.log(Level.INFO, "executing automation :: api url : {0} :: parameters : {1}", new Object[]{this.apiUrl, parametersAsString});
            try {
                JSONObject apiResponseObj = ContainerSynchronizer.sendRequest(this.apiUrl, parametersAsString, accessToken, null, false, null);
                LOGGER.log(Level.INFO, "responseObject : {0}", apiResponseObj.toString());
                int responseStatus = apiResponseObj.getInt(DataAPIConstants.RESPONSE_STATUS);
                if (this.apiUrl.contains("download") && responseStatus < 300) {
                    setResponseObject(new JSONObject("{\"method\":\"workbook.download\",\"status\":\"success\"}"));    //No I18N
                } else {
                    //setResponseObject((apiResponseObj.getJSONObject(DataAPIConstants.RESPONSE_BODY));
                    setResponseObject(new JSONObject(apiResponseObj.get(DataAPIConstants.RESPONSE_BODY).toString()));
                }
                setResponseStatus(responseStatus);
                setStatus(responseStatus < 300 ? Status.TEST_OK : Status.TEST_FAILED);
            } catch (IOException ioe) {
                LOGGER.log(Level.WARNING, "IOE exception in ContainerSynchronizer ", ioe);
                JSONObject respObj = new JSONObject();
                respObj.put(DataAPIConstants.ERROR_MESSAGE, ioe.getMessage());
                this.responseObject = respObj;
                setStatus(Status.TEST_FAILED);
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "Exception in ContainerSynchronizer ", e);
                JSONObject respObj = new JSONObject();
                respObj.put(DataAPIConstants.ERROR_MESSAGE, e.getMessage());
                this.responseObject = respObj;
                setStatus(Status.TEST_FAILED);
            }
        }
    }
}