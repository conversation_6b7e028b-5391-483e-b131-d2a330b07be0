/* $Id$ */
package com.zoho.sheet.api.handler.post;

import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.api.APIErrMsg;
import com.zoho.sheet.api.APIError;
import com.zoho.sheet.api.APIResponse;
import com.zoho.sheet.api.MethodHandler;
import com.zoho.zfsng.client.ZohoFS;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
/***
 *
 * <AUTHOR>
 *
 * PURPOSE : Used to get the my folder Id of the workdrive
 *
 * **** REQUEST URL *******
 *
 *  http://sheet.zoho.com/sheet/api/private/json/getWDMyFolderId  - authenticated
 *          ----no params needed -----
 *
 * ****  RESPONSE FORMAT ******
 *
 * JSONObject -
 * {"response":
 *          {"uri":"/sheet/api/private/json/getWDMyFolderId",
 *           "result"  : { "MY_FOLDER_ID": " myFolderId "
 *                      }
 *           }
 * }
 * ***** ERROR HANDLING ******
 *
 *  status -    500 -  if any exception in server side (server cant get the permission of the specifiedd document rid)
 */
public class GetWorkdriveInfoHandler implements MethodHandler {
    private static final Logger LOGGER = Logger.getLogger(GetWorkdriveInfoHandler.class.getName());
    private static MethodHandler instance = new GetWorkdriveInfoHandler();

    public static MethodHandler getInstance() {
        return instance;
    }
    @Override
    public void handleMethod(Map struct, HttpServletRequest request, HttpServletResponse response) {

        String respFormat 		= (String)struct.get("resp-format");
        try {
            String folderId = ZohoFS.getMyFolderId(DocumentUtils.getZUID().toString());
            APIResponse.getInst(respFormat).GetWorkdriveInfoHandler(request, response, folderId);

        } catch (Exception e) {

            LOGGER.log(Level.WARNING, "[WD]Error while getting Myfolder ID", e);
            APIError.getInst(respFormat).GenErr(request,response, APIErrMsg.GenErr());
        }
    }
}
