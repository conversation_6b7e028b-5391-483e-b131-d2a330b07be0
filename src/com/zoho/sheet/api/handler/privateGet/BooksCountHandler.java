/* $Id$ */
package com.zoho.sheet.api.handler.privateGet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import com.adventnet.ds.query.QueryConstructionException;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.api.APIErrMsg;
import com.zoho.sheet.api.APIError;
import com.zoho.sheet.api.APIResponse;
import com.zoho.sheet.api.MethodHandler;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceStatus;
import com.zoho.zfsng.constants.ResourceType;
import com.zoho.zfsng.constants.ServiceType;
import com.zoho.zfsng.constants.ShareConstants;
import com.zoho.zfsng.constants.ZFSNGConstants;

/**
 * Author: asasikumar
 * Date: July 31, 2012
 */
public class BooksCountHandler implements MethodHandler
{
    public static final BooksCountHandler INST = new BooksCountHandler();
    public void handleMethod(Map struct, HttpServletRequest request, HttpServletResponse response)
    {
        String respFormat = (String)struct.get("resp-format");
        String tag = (String) struct.get("tag");
        String userName = request.getUserPrincipal().getName();
       // Long accId = cred.getAccountId();
       // List temp = null; // having List, but expecting only a single value..
        String tempValue = request.getParameter("type");
		int type=-1;
		int count = 0;
		if (tempValue != null)
		{
			try
			{
				if((Integer.parseInt(tempValue)) < 0)
				{
					APIError.getInst(respFormat).WrongTypeErr(request, response, APIErrMsg.WrongType("type","Integer")); //No I18N
					return;
				}
				type = Integer.parseInt(tempValue);
			}
			catch (NumberFormatException e)
			{
				APIError.getInst(respFormat).WrongTypeErr(request, response, APIErrMsg.WrongType("type","Integer"));   //No I18N
				return;
			}
		}
		try
        {
			int sharedType =-1; //ShareConstants.SharedType_ALL;
			if(type==0)
			{
				sharedType = ShareConstants.SHAREDTYPE_PERSONAL;
			}
			else if(type ==1)
			{
				sharedType = ShareConstants.SHAREDTYPE_ORG;
			}
			else if(type == 2)
			{
				sharedType = ShareConstants.SHAREDTYPE_PUBLIC;
			}
			
			Long ownerZuid = DocumentUtils.getZUID(userName);
			String baseParentId = ZohoFS.getDefaultLibraryID(""+ownerZuid);
			String myFolder = ZohoFS.getMyFolderId(""+ownerZuid);
			
			String templibraryId = ZohoFS.getLibraryID(""+ownerZuid, ResourceType.TEMPLATE_LIBRARY, ZFSNGConstants.DEFAULT_TEMPLATE_LIBRARY_NAME);
			
			List<Integer> resourceType = new ArrayList<Integer>();
        	resourceType.add(ResourceType.WORKBOOK_NATIVE);
        	
        	count = ZohoFS.getResourceCount(""+ownerZuid, "0", resourceType, ServiceType.SHEET, ResourceStatus.ACTIVE, templibraryId);		//No I18N
        	count = ZohoFS.getResourceCount(""+ownerZuid, "0", resourceType, ServiceType.SHEET, ResourceStatus.ACTIVE, myFolder);		//No I18N
        	if(sharedType == -1)//ShareConstants.ALL)
        	{
        	count = ZohoFS.getResourceCount(""+ownerZuid, "0", resourceType, ServiceType.SHEET, ResourceStatus.ACTIVE, baseParentId);		//No I18N
        	}
        	else
        	{
			 count = ZohoFS.getSharedResourcesCount(""+ownerZuid, ResourceType.ALL_LIBRARY, sharedType, resourceType);
        	}
			//ZohoFS.getMySharedResourceJson(String owner, String resourceId, String version, int sharedType) 
			//ZohoFS.getOwnOrMySharedResourceJsons(zid, parentId, ResourceType.WORKBOOK_NATIVE, ServiceType.SHEET, sharedType, ResourceStatus.ACTIVE, sortBy, isAsc, startFrom, limit)
			/*OpenDocBean openDocBean = (OpenDocBean) APIUtil.lookupBean("OpenDocBean");//No I18N
            temp = openDocBean.fetchAllValues(getSelectQuery(accId, tag, type));*/
        }
        catch (SQLException e)
        {
            APIError.getInst(respFormat).DBErr(request, response, APIErrMsg.GenErr());
            return;
        }
        catch (QueryConstructionException e)
        {
            APIError.getInst(respFormat).GenErr(request, response, APIErrMsg.GenErr());
            return;
        }
		catch (Exception e) {
			// TODO: handle exception
			 APIError.getInst(respFormat).GenErr(request, response, APIErrMsg.GenErr());
	            return;
		}
        APIResponse.getInst(respFormat).Count(request, response, count);
        //return;
    }

    /*private static SelectQuery getSelectQuery(Long accId, String tagName)
    {
        SelectQuery sQuery = new SelectQueryImpl(new Table("Documents"));
        sQuery.addSelectColumn(new Column("Documents", "DOCUMENT_ID").count());
        sQuery.addJoin(
                new Join("Documents", "AccountDocuments",
                        new Criteria(new Column("Documents", "DOCUMENT_ID"), new Column("AccountDocuments", "DOCUMENT_ID"),
                                QueryConstants.EQUAL
                        ).
                and(new Criteria(new Column("AccountDocuments", "ACCOUNT_ID"), accId, QueryConstants.EQUAL)).
                and(new Criteria(new Column("Documents", "DEL_TIME"), new Long(0), QueryConstants.EQUAL)).
                and(new Criteria(new Column("Documents", "DOCUMENT_TYPE"), 100, QueryConstants.NOT_EQUAL)),
                        Join.INNER_JOIN
                )
        );
        if (tagName != null)
        {
            sQuery.addJoin(new Join("Documents", "Tags_X_Util", new String[]{"DOCUMENT_ID"}, new String[]{"DOCUMENT_ID"},
                    Join.INNER_JOIN)
            );
            sQuery.addJoin(new Join("Tags_X_Util", "Tags", new String[]{"TAG_ID"}, new String[]{"TAG_ID"}, Join.INNER_JOIN));
            sQuery.setCriteria(new Criteria(new Column("Tags", "TAG_NAME"), tagName, QueryConstants.LIKE));
        }
        return sQuery;
    }

    private static SelectQuery getSelectQuery(Long accId, String tagName,int type)
	{
		SelectQuery sQuery = getSelectQuery(accId,tagName);
		if(type != -1)
		{
			sQuery.setCriteria(new Criteria(new Column("Documents", "DOCUMENT_STATUS"), type, QueryConstants.EQUAL));
		}
		return sQuery;
	}
*/

}
