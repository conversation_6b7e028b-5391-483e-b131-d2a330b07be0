/* $Id$ */
package com.zoho.sheet.api.handler.privateGet;

import com.adventnet.iam.IAMException;
import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.OrgPolicy;
import com.adventnet.iam.User;
import com.adventnet.iam.UserAPI;
import com.zoho.sheet.api.APIErrMsg;
import com.zoho.sheet.api.APIError;
import com.zoho.sheet.api.APIUtil;
import com.zoho.sheet.api.MethodHandler;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.io.IOException;
import java.net.URLEncoder;

import java.util.logging.Logger;
import java.util.logging.Level;


/**
 * To handle downloads related API calls
 */
public class DownloadHandler implements MethodHandler
{
	public 		static 		Logger 				logger 		= Logger.getLogger(DownloadHandler.class.getName());
    private 	static	 	final MethodHandler INST 		= new DownloadHandler();
    private 	static 		APIUtil.Info 		bookInfo 	= null;
    
    public static MethodHandler getInst()
    {
        return INST;
    }

    public void handleMethod(Map struct, HttpServletRequest request, HttpServletResponse response)
    {
    	com.adventnet.iam.User 	userObj 	= 	com.adventnet.iam.IAMUtil.getCurrentUser();
    	String 					respFormat 	= 	(String)struct.get("resp-format");
        Long 					zoid 		= 	userObj.getZOID();
        String docowner = null;
        
//    	if (zoid != -1 && !IAMUtil.isCurrentUserAllowed(OrgPolicy.EXPORT_DOCUMENT)) 
//    	{
//    		APIError.getInst(respFormat).GenErr(request, response, APIErrMsg.PermissionDenied());
//    		return;
//    	}
    	
		long 					zuid 		= userObj.getZUID();
        String 					id 		= (String) struct.get("book-id");
        Long 	bookId = -1l;
        String 	resourceId ;
        try{
        	bookId = Long.parseLong(id);
        	resourceId 	= DocumentUtils.getResourceId(String.valueOf(id), userObj.getLoginName());//DocumentUtils.getResourceId(""+bookId);
        }catch(NumberFormatException e){
        	try{
        		resourceId = id;
        		String ownerZuid = ZohoFS.getOwnerZID(resourceId);
        		docowner = DocumentUtils.getZUserName(ownerZuid);
        		bookId = Long.parseLong(DocumentUtils.getDocumentId(resourceId, docowner));
        	}catch(Exception e1){
	        	logger.log(Level.WARNING,null,e1);
				APIError.getInst(respFormat).NoSuchBookErr(request, response, APIErrMsg.NoSuchBook(bookId));
	            return;
        	}
        }catch(Exception e2){
        	logger.log(Level.WARNING,null,e2);
			APIError.getInst(respFormat).NoSuchBookErr(request, response, APIErrMsg.NoSuchBook(bookId));
            return;
    	}
        String 					userName 	= request.getUserPrincipal().getName();
        String 					version 	= request.getParameter("version");
		ResourceInfo 			resInfo 	= null;
		//TODO: Have to handle.
        if(resourceId != null){
	        try {
				resInfo = ZohoFS.getResourceInfo(resourceId);
			} catch (Exception e) {
				logger.log(Level.WARNING,null,e);
				APIError.getInst(respFormat).NoSuchBookErr(request, response, APIErrMsg.NoSuchBook(bookId));
	            return;	
			}
        }else{
			APIError.getInst(respFormat).NoSuchBookErr(request, response, APIErrMsg.NoSuchBook(bookId));
            return;	    	
        }
	       
		try{
	     	if(!(""+zuid).equals(resInfo.getOwner())){
	       		boolean isShared = ZohoFS.isResourceSharedToUser(""+zuid, resourceId);
	       		if(!isShared){
	       			if(userObj.isOrgAdmin()){
		        		try{
		        			String ownerZuid = ZohoFS.getOwnerZID(resourceId);
		        			userName = DocumentUtils.getZUserName(ownerZuid);
		        			//userName = docowner;//DocumentUtils.getDocOwner(bookId.toString());
		        			if(userName != null){
	        	    			IAMProxy proxy = IAMProxy.getInstance();
	        					UserAPI uapi = proxy.getUserAPI();
	        					User authUserObj = uapi.getUser(userName);
	        					Long authOrgId = authUserObj.getZOID();
	        					
	        					if(!zoid.equals(authOrgId)){
	        						 APIError.getInst(respFormat).AuthFailureErr(request, response, "Admin Authorization failed");//No I18N
	        						 return;
	        					}
	        	    		}else{
	        	    			APIError.getInst(respFormat).NoSuchBookErr(request, response, APIErrMsg.NoSuchBook(bookId));
	        		            return;      		
	        		    	}
		        				
	        	    	}catch(IAMException e){
	        				 APIError.getInst(respFormat).AuthFailureErr(request, response, "Admin Authorization failed");//No I18N
	        				 return;	    			
	        	    	}catch(Exception e){
	        	    		logger.log(Level.WARNING,null,e);
	        	    		APIError.getInst(respFormat).NoSuchBookErr(request, response, APIErrMsg.NoSuchBook(bookId));
	        	    		return;	    			
	        	    	}
	       			}else{
	       				APIError.getInst(respFormat).NoSuchBookErr(request, response, APIErrMsg.NoSuchBook(bookId));
        	    		return;	
	       			}
	     		}
	       	}
	    } catch (Exception e1) {
	    	logger.log(Level.WARNING,null,e1);
			APIError.getInst(respFormat).NoSuchBookErr(request, response, APIErrMsg.NoSuchBook(bookId));
            return;	
		}
		
        
        try
        {
        	ResourceInfo resinfo = ZohoFS.getResourceInfo(resourceId);
        	String redirectURL = DocumentUtils.getDocControllerPath(AccessType.AUTH, resourceId, "export", true, request)+"?fname=" + URLEncoder.encode(resinfo.getName(), "UTF-8") + "&ftype=." + respFormat; //NO I18N
        	
        	// Version level export 
        	if(version != null){
        		redirectURL = redirectURL + "&version="+version; //NO I18N
        	}
        	
        	// Sheet Name based export support for CSV | TSV download
        	String sheetName = (String) struct.get("sheet-name");
        	if(sheetName != null && !"".equals(sheetName)){
        		redirectURL += "&currentSheet="+sheetName; //NO I18N
        	} else {
	        	
        		// Sheet Index based export support for CSV | TSV download
        		Integer sheetIdx = (Integer) struct.get("sheet-index");
	        	if(sheetIdx != null){
	        		redirectURL += "&currentSheet="+sheetIdx+"&isSheetIndexBased=true"; //NO I18N
	        	}
        	}
        	
        	// Range level export support for CSV | TSV download
        	String range = (String) struct.get("range");
        	if(range != null && !"".equals(range)){
        		redirectURL += "&range="+range; //NO I18N
        	}
        	//logger.info("redirectURL >>>> "+ redirectURL);
        	request.getRequestDispatcher(redirectURL).forward(request, response);	//No I18N
        }
        catch(IOException e)
        {
			logger.log(Level.WARNING,null,e);
            response.resetBuffer();
            APIError.getInst(respFormat).IOErr(request, response, APIErrMsg.GenErr());
            return;
        }
        catch (Exception e)
        {
			logger.log(Level.WARNING,null,e);
            response.resetBuffer();
            APIError.getInst(respFormat).GenErr(request, response, APIErrMsg.GenErr());
            return;
        }
    }

/*    public static void generateCsvResp(String bookName, Map input, InputStream bookStream, HttpServletRequest request,
                                               HttpServletResponse response ) throws Exception
    {
		String respFormat = (String)input.get("resp-format");
		Long bookId = (Long) input.get("book-id");
		StringBuffer content = null;
		String range = (String)input.get("range");
		RangeAddress rangeAddr = null;
		String fileFormat = (String) request.getAttribute("FORMAT");
		Store store = null;
		if(range != null)
		{
			try
			{
				rangeAddr = RangeAddress.forLabel(range);
			}
			catch (IllegalArgumentException e)
			{
				APIError.getInst(respFormat).WrongValErr(request, response, APIErrMsg.WrongRange(range));
				return;
			}
		}

		boolean isODS = false;
		try{
			isODS = APIUtil.isODSFile(input,request,(String)bookInfo.getValue("AUTHOR_NAME"));
		}catch(Exception e)
		{
			e.printStackTrace();
		}

		String sheetName = (String) input.get("sheet-name");
		if(sheetName != null)
		{
			bookName += '_' + sheetName;
			try
			{
				if(EngineConstants.ENGINE_ODS_FILE_FORMAT.equals(fileFormat))
				{
					if(rangeAddr != null)
					{
						content = new CsvRangeTransformer(sheetName, rangeAddr).transfromODS(bookStream);
					}
					else
					{
						content = new CsvSheetTransformer(sheetName).transfromODS(bookStream);
					}
				}else if(EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT.equals(fileFormat))
				{
					store = (Store) request.getAttribute("STORE");
					String authorName = (String) request.getAttribute("AUTHOR_NAME");
					if(rangeAddr != null)
					{
						content = new CsvRangeTransformer(sheetName, rangeAddr).transfromFragmentedFile(store, authorName, bookId);
					}
					else
					{
						content = new CsvSheetTransformer(sheetName).transfromFragmentedFile(store, authorName, bookId);
					}
				}
				else
				{
					if(rangeAddr != null)
					{
						content = new CsvRangeTransformer(sheetName, rangeAddr).transfrom(bookStream);
					}
					else
					{
						content = new CsvSheetTransformer(sheetName).transfrom(bookStream);
					}
				}
			}
			catch (IllegalArgumentException e)
			{
				APIError.getInst(respFormat).WrongValErr(request, response, APIErrMsg.WrongSheetName(sheetName));
				return;
			}
		}
		else
		{
			Integer temp = (Integer)input.get("sheet-index");
			int sheetIndex = 1;
			if(temp != null)
			{
				sheetIndex = temp.intValue();
			}
			bookName += '_' + ("" + sheetIndex);
			try
			{
				if(EngineConstants.ENGINE_ODS_FILE_FORMAT.equals(fileFormat))
				{
					if(rangeAddr != null)
					{
						content = new CsvRangeTransformer(sheetIndex, rangeAddr).transfromODS(bookStream);
					}
					else
					{
						content = new CsvSheetTransformer(sheetIndex).transfromODS(bookStream);
					}
				}
				else if(EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT.equals(fileFormat))
				{
					store = (Store) request.getAttribute("STORE");
					String authorName = (String) request.getAttribute("AUTHOR_NAME");
					if(rangeAddr != null)
					{
						content = new CsvRangeTransformer(sheetIndex, rangeAddr).transfromFragmentedFile(store, authorName, bookId);
					}
					else
					{
						content = new CsvSheetTransformer(sheetIndex).transfromFragmentedFile(store, authorName, bookId);
					}
				}
				else
				{
					if (rangeAddr != null)
					{
						content = new CsvRangeTransformer(sheetIndex, rangeAddr).transfrom(bookStream);
					}
					else
					{
						content = new CsvSheetTransformer(sheetIndex).transfrom(bookStream);
					}
				}

			}
			catch (IllegalArgumentException e)
			{
				APIError.getInst(respFormat).WrongValErr(request, response, APIErrMsg.WrongSheetIndex(sheetIndex));
				return;
			}
		}
		//IE with https (secured) connection export issue fix block starts
		response.reset();
		//IE with https (secured) connection export issue fix block ends
		response.setContentType("text/csv;charset=utf-8");
		response.setHeader("Content-Disposition", "attachment;filename=\"" + bookName + '.' + respFormat + "\"");	//No I18N
		response.getWriter().print(content);
    }

        public static void generateTsvResp(String bookName, Map input, InputStream bookStream, HttpServletRequest request,
                                               HttpServletResponse response ) throws Exception
    {
        String respFormat = (String)input.get("resp-format");
        Long bookId = (Long) input.get("book-id");
        StringBuffer content = null;
        String range = (String)input.get("range");
        String fileFormat = (String) request.getAttribute("FORMAT");
        RangeAddress rangeAddr = null;
        Store store = null;
        if(range != null)
        {
            try
            {
                rangeAddr = RangeAddress.forLabel(range);
            }
            catch (IllegalArgumentException e)
            {
                APIError.getInst(respFormat).WrongValErr(request, response, APIErrMsg.WrongRange(range));
                return;
            }
        }

		boolean isODS = false;
		try{
			isODS = APIUtil.isODSFile(input,request,(String)bookInfo.getValue("AUTHOR_NAME"));
		}catch(Exception e)
		{
			e.printStackTrace();
		}

        String sheetName = (String) input.get("sheet-name");
        if(sheetName != null)
        {
            bookName += '_' + sheetName;
            try
            {
				if(EngineConstants.ENGINE_ODS_FILE_FORMAT.equals(fileFormat))
				{
					if(rangeAddr != null)
					{
						content = new TsvRangeTransformer(sheetName, rangeAddr).transfromODS(bookStream);
					}
					else
					{
						content = new TsvSheetTransformer(sheetName).transfromODS(bookStream);
					}
				}
				else if(EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT.equals(fileFormat))
				{
					store = (Store) request.getAttribute("STORE");
					String authorName = (String) request.getAttribute("AUTHOR_NAME");
					if(rangeAddr != null)
					{
						content = new TsvRangeTransformer(sheetName, rangeAddr).transfromFragmentedFile(store, authorName, bookId);
					}
					else
					{
						content = new TsvSheetTransformer(sheetName).transfromFragmentedFile(store, authorName, bookId);
					}
				}
				else
				{
					if(rangeAddr != null)
					{
						content = new TsvRangeTransformer(sheetName, rangeAddr).transfrom(bookStream);
					}
					else
					{
						content = new TsvSheetTransformer(sheetName).transfrom(bookStream);
					}
				}
            }
            catch (IllegalArgumentException e)
            {
                APIError.getInst(respFormat).WrongValErr(request, response, APIErrMsg.WrongSheetName(sheetName));
                return;
            }
        }
        else
        {
            Integer temp = (Integer)input.get("sheet-index");
            int sheetIndex = 1;
            if(temp != null)
            {
                sheetIndex = temp.intValue();
            }
            bookName += '_' + ("" + sheetIndex);
            try
			{
				if(EngineConstants.ENGINE_ODS_FILE_FORMAT.equals(fileFormat))
				{
					if(rangeAddr != null)
					{
						content = new TsvRangeTransformer(sheetIndex, rangeAddr).transfromODS(bookStream);
					}
					else
					{
						content = new TsvSheetTransformer(sheetIndex).transfromODS(bookStream);
					}
				}
				else if(EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT.equals(fileFormat))
				{
					store = (Store) request.getAttribute("STORE");
					String authorName = (String) request.getAttribute("AUTHOR_NAME");

					if(rangeAddr != null)
					{
						content = new TsvRangeTransformer(sheetIndex, rangeAddr).transfromFragmentedFile(store, authorName, bookId);
					}
					else
					{
						content = new TsvSheetTransformer(sheetIndex).transfromFragmentedFile(store, authorName, bookId);
					}
				}
				else
				{
					if (rangeAddr != null)
					{
						content = new TsvRangeTransformer(sheetIndex, rangeAddr).transfrom(bookStream);
					}
					else
					{
						content = new TsvSheetTransformer(sheetIndex).transfrom(bookStream);
					}
				}

            }
            catch (IllegalArgumentException e)
            {
                APIError.getInst(respFormat).WrongValErr(request, response, APIErrMsg.WrongSheetIndex(sheetIndex));
                return;
            }
        }
        //IE with https (secured) connection export issue fix block starts
		response.reset();
		//IE with https (secured) connection export issue fix block ends
        response.setContentType("text/tsv;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=\"" + bookName + '.' + respFormat + "\"");					//No I18N
        response.getWriter().print(content);
    }*/
}
