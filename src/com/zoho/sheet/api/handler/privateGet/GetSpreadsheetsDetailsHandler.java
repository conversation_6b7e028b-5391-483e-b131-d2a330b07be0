/* $Id$ */
package com.zoho.sheet.api.handler.privateGet;

/**
 * <AUTHOR>
 */
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zoho.sheet.authorization.util.AuthorizationUtil;
import com.zoho.sheet.util.ClientUtils;
import org.apache.commons.collections4.map.CaseInsensitiveMap;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.iam.UserAPI;
import com.adventnet.zoho.websheet.model.UserProfile.PermissionType;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.sheet.api.APIErrMsg;
import com.zoho.sheet.api.APIError;
import com.zoho.sheet.api.APIResponse;
import com.zoho.sheet.api.APIUtil;
import com.zoho.sheet.api.MethodHandler;
import com.zoho.sheet.listingpage.SheetListingAction;
import com.zoho.zfsng.client.ProductInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ProductType;
import com.zoho.zfsng.constants.ResourceStatus;
import com.zoho.zfsng.constants.ResourceType;
import com.zoho.zfsng.constants.ServiceType;
import com.zoho.zfsng.constants.ShareConstants;
import com.zoho.zfsng.constants.ZFSNGConstants;
/**
 * Purpose : -- Provide the list spreadsheets
 * Note: -- This API is used by Handheld devices
 */
public class GetSpreadsheetsDetailsHandler implements MethodHandler
{
	private static GetSpreadsheetsDetailsHandler instance = new GetSpreadsheetsDetailsHandler();
	public static Logger logger = Logger.getLogger(GetSpreadsheetsDetailsHandler.class.getName());
	public static MethodHandler getInstance()
	{
		return instance;
	}

	public static GetSpreadsheetsDetailsHandler.Params getParamsInstance()
	{
		return instance.new Params();
	}

	public static Object getOrderByValue(String input){
		Map orderByEnum = new CaseInsensitiveMap(3);
		orderByEnum.put("lastModifiedTime", "STATUS_CHANGE_TIME");
		orderByEnum.put("createdTime", "CREATED_TIME");
		orderByEnum.put("name", "NAME");
		orderByEnum.put("lastopenedtime", "LAST_OPENED_TIME");
		orderByEnum.put("lastOpenedTime", "LAST_OPENED_TIME");
		orderByEnum.put("sharedTime", "SHARED_TIME");

		return orderByEnum.get(input);
	}

	public static Object getSortOrderByValue(String input){
		Map sortOrderEnum = new CaseInsensitiveMap(4);
		sortOrderEnum.put("asc", Boolean.TRUE);
		sortOrderEnum.put("ascending", Boolean.TRUE);
		sortOrderEnum.put("desc", Boolean.FALSE);
		sortOrderEnum.put("descending", Boolean.FALSE);
		return sortOrderEnum.get(input);
	}

	public static Object getScopeByValue(String input){
		Map scopeEnum = new CaseInsensitiveMap(4);
		scopeEnum.put("all", -1);
		scopeEnum.put("owned", 0);
		scopeEnum.put("shared", 1);
		return scopeEnum.get(input);
	}


	class Params
	{

		int type = -1;
		int startFrom = 0; // default value
		int limit = -1;		// default value
		String orderBy = "CREATED_TIME";		//No I18N
		boolean isAscending = false;
		int scope	=	-1;
		int libraryType = 0;

		boolean init(String respFormat, HttpServletRequest request, HttpServletResponse response)
		{

			String tempValue = request.getParameter("start-from");
			if (tempValue != null)
			{
				try
				{
					startFrom = Integer.parseInt(tempValue);
				}
				catch (NumberFormatException e)
				{
					APIError.getInst(respFormat).WrongTypeErr(request, response, APIErrMsg.WrongType("start-from","Integer"));			//No I18N
					return false;
				}
			}
			tempValue = request.getParameter("limit");
			if (tempValue != null)
			{
				try
				{
					limit = Integer.parseInt(tempValue);
				}
				catch (NumberFormatException e)
				{
					APIError.getInst(respFormat).WrongTypeErr(request, response, APIErrMsg.WrongType("limit","Integer"));				//No I18N
					return false;
				}
			}

			tempValue = request.getParameter("order-by") != null ? request.getParameter("order-by") : (String) request.getAttribute("orderBy");
			if (tempValue != null)
			{
				orderBy = (String) getOrderByValue(tempValue);
				if(orderBy == null){
					APIError.getInst(respFormat).WrongValErr(request, response,
							APIErrMsg.WrongValue("order-by"," createdTime | lastModifiedTime | name "));							//No I18N
					return false;
				}
			}
			tempValue = request.getParameter("sort-order") != null ? request.getParameter("sort-order") : (String) request.getAttribute("sortOrder");
			if (tempValue != null)
			{
				if(getSortOrderByValue(tempValue) ==  null){
					APIError.getInst(respFormat).WrongValErr(request, response,
							APIErrMsg.WrongValue("sort-order"," asc | desc "));														//No I18N
					return false;
				}
				isAscending = (Boolean) getSortOrderByValue(tempValue);
			}
			tempValue = request.getParameter("type");
			if (tempValue != null)
			{
				try
				{
					if((Integer.parseInt(tempValue)) < 0)
					{
						APIError.getInst(respFormat).WrongTypeErr(request, response, APIErrMsg.WrongType("type","Integer"));					//No I18N
						return false;
					}
					type = Integer.parseInt(tempValue);
				}
				catch (NumberFormatException e)
				{
					APIError.getInst(respFormat).WrongTypeErr(request, response, APIErrMsg.WrongType("type","Integer"));			//No I18N
					return false;
				}
			}
			tempValue = request.getParameter("scope");
			if(tempValue != null)
			{
				if(getScopeByValue(tempValue) == null){
					APIError.getInst(respFormat).WrongValErr(request, response, APIErrMsg.WrongValue("scope", " all | owned | shared ")); //No I18N
					return false;
				}
				scope = (Integer) getScopeByValue(tempValue);
			}

			tempValue = request.getParameter("libraryType");
			if(tempValue != null) {
				try
				{
					if((Integer.parseInt(tempValue)) < 0)
					{
						APIError.getInst(respFormat).WrongTypeErr(request, response, APIErrMsg.WrongType("type","Integer"));					//No I18N
						return false;
					}
					libraryType = Integer.parseInt(tempValue);
				}
				catch (NumberFormatException e)
				{
					APIError.getInst(respFormat).WrongTypeErr(request, response, APIErrMsg.WrongType("type","Integer"));			//No I18N
					return false;
				}
			}
			return true;
		}
	}

	public void handleMethod(Map struct, HttpServletRequest request, HttpServletResponse response)
	{
		String respFormat = (String)struct.get("resp-format");
		try {
			// Long accId = new Long(-1);

			/* --  Admin access implementation to get the org member details -- */
			String userName = request.getParameter("emailid");
			//logger.info(" userName: "+userName);
			if(userName != null && !userName.equals("")){
				Long adminOrgId;
				User adminUserObj = IAMUtil.getCurrentUser();
				if(adminUserObj != null){
					if(adminUserObj.isOrgAdmin()){
						adminOrgId = adminUserObj.getZOID();
					}else{
						APIError.getInst(respFormat).AuthFailureErr(request, response, "Admin Authorization failed");//No I18N
						return;
					}
				}else{
					APIError.getInst(respFormat).AuthFailureErr(request, response, "Admin Authorization failed");//No I18N
					return;
				}
				// user/emailid available under the same org validation
				try{
					if(userName.indexOf('@') > 0 ){
						userName = DocumentUtils.getZuserEmailId(userName);
					}
					IAMProxy proxy = IAMProxy.getInstance();
					UserAPI uapi = proxy.getUserAPI();
					User userObj = uapi.getUser(userName);
					Long userOrgId = userObj.getZOID();
					if(!adminOrgId.equals(userOrgId)){
						APIError.getInst(respFormat).AuthFailureErr(request, response, "Admin Authorization failed");//No I18N
						return;
					}
					//accId = DocumentUtils.getAccountId`(userName); // User Account Id
				} catch(Exception e){
					APIError.getInst(respFormat).AuthFailureErr(request, response, "Admin Authorization failed");//No I18N
					return;
				}
			} else {
				//Credential cred = AuthUtil.getUserCredential();
				//accId = cred.getAccountId();
				//userName = request.getUserPrincipal().getName();//cred.getLoginName();
				userName =IAMUtil.getCurrentUser().getLoginName();

			}

			Params params = new Params();
			if (!params.init(respFormat, request, response))
			{
				return;
			}
			JSONArray jsonArray;
			List<Map<String,String>> list = null;
//			String isMergeFieldEnabledStr = RemoteUtils.maskNull(EnginePropertyUtil.getSheetPropertyValue("isMergeFieldEnabled"));// NO I18N
//			boolean isMergeFieldEnabled = isMergeFieldEnabledStr == null ? false : "true".equals(isMergeFieldEnabledStr);
			Boolean isMergeFieldEnabled = ClientUtils.isFeatureEnabled("isMergeFieldEnabled", "all");   //No I18N
			long zuid = DocumentUtils.getZUID(userName);
			List<Integer> resourceType = new ArrayList<>();
			resourceType.add(ResourceType.WORKBOOK_NATIVE);
			if(isMergeFieldEnabled) {
				resourceType.add(ResourceType.ZS_MERGE_TEMPLATE_WORKBOOK);
			}

			String		resourceStatusStr		=		request.getParameter("spreadsheetstatus");
			int			resourceStatus			=		"trashed".equals(resourceStatusStr) ? ResourceStatus.TRASHED : ResourceStatus.ACTIVE;	//No I18N
			int 		scope					=		"trashed".equals(resourceStatusStr) ? 0 : params.scope;//No I18N

			String baseParentId;
//			List<String>	sharedByMeSheetList			=	null;
			int totalSpreasheets = 0;
			switch(params.libraryType) {
				case 0:	//Spreadsheets
					String orgId =  (String)request.getAttribute("orgId");
					ProductInfo pdtInfo;
					if(orgId != null) {
						pdtInfo=SheetListingAction.getProductInfo(orgId);
						baseParentId = ZohoFS.getDefaultLibraryID(""+zuid, pdtInfo);
					}else {
						baseParentId = ZohoFS.getDefaultLibraryID(""+zuid);
					}
					if(params.limit == -1)
					{
						params.limit = ZohoFS.getResourceCount(""+zuid, "0", resourceType, 0, resourceStatus, -1, baseParentId);
					}

					//Handling shared by me spreadsheets
//					String	sharedByMeResources	=	ZohoFS.getMySharedResources(""+zuid, resourceType, baseParentId, ShareConstants.SHAREDTYPE_PERSONAL, params.orderBy, params.isAscending, params.startFrom, params.limit);

//					JSONArray		sharedByMeSpreadsheetArr;

//					if(sharedByMeResources != null) {
//						sharedByMeSpreadsheetArr	=	new	 JSONArray(sharedByMeResources);
//						sharedByMeSheetList			=	getSharedByMeSpreadsheetList(sharedByMeSpreadsheetArr);
//					}
					String responseString =	ZohoFS.getResourceJsonsWithParentAndMySharedInfo(""+zuid, "0", resourceType, 0, resourceStatus, scope, baseParentId, params.orderBy, params.isAscending, params.startFrom, params.limit);
//                String responseString =	ZohoFS.getResourceJsonsWithParentAndMySharedInfo(""+zuid, "0",resourceType,0,resourceStatus,scope,baseParentId,)
					jsonArray = responseString != null ? new JSONArray(responseString) : new	JSONArray();

					list = getBooksInfo(jsonArray,params.orderBy);
					totalSpreasheets =  ZohoFS.getResourceCount(""+zuid, "0", resourceType, 0, resourceStatus, scope, baseParentId);
					break;

				case 1:	//User Templates
					orgId =  (String)request.getAttribute("orgId");
					if(orgId != null) {
						pdtInfo=SheetListingAction.getProductInfo(orgId);
						baseParentId = ZohoFS.getLibraryID(""+zuid, ResourceType.TEMPLATE_LIBRARY, ZFSNGConstants.DEFAULT_TEMPLATE_LIBRARY_NAME, pdtInfo);
					}else {
						baseParentId = ZohoFS.getLibraryID(""+zuid, ResourceType.TEMPLATE_LIBRARY, ZFSNGConstants.DEFAULT_TEMPLATE_LIBRARY_NAME);
					}
					if(params.limit == -1)
					{
						params.limit = ZohoFS.getResourceCount(""+zuid, "0", resourceType, 0, resourceStatus, -1, baseParentId);
					}
					responseString =	ZohoFS.getResourceJsonsWithParent(""+zuid, "0" , resourceType, ServiceType.SHEET, ResourceStatus.ACTIVE, -1, baseParentId, params.orderBy, params.isAscending, params.startFrom, params.limit); // no i18n

					jsonArray = responseString != null ? new JSONArray(responseString) : new	JSONArray();

					list = getBooksInfo(jsonArray,params.orderBy);
					totalSpreasheets =  ZohoFS.getResourceCount(""+zuid, "0" , resourceType, 0, resourceStatus, scope, baseParentId);
					break;

				case 2:	//System Templates

					baseParentId = ZohoFS.getLibraryID("template", ResourceType.TEMPLATE_LIBRARY, ZFSNGConstants.DEFAULT_TEMPLATE_LIBRARY_NAME);	//No i18N
					if(params.limit == -1)
					{
						params.limit = ZohoFS.getResourceCount("template", "0", resourceType, 0, resourceStatus, -1, baseParentId);
					}
					responseString 	=	ZohoFS.getResourceJsonsWithParent("template", "0", resourceType, ServiceType.SHEET, ResourceStatus.ACTIVE, -1, baseParentId, params.orderBy, params.isAscending, params.startFrom, params.limit); // no i18n

					jsonArray = responseString != null ? new JSONArray(responseString) : new	JSONArray();

					list = getBooksInfo(jsonArray,params.orderBy);
					totalSpreasheets =  ZohoFS.getResourceCount("template", "0", resourceType, 0, resourceStatus, scope, baseParentId);	//No i18N
					break;
			}

			APIResponse.getInst(respFormat).GetSpreadsheetsDetails(request, response, list, totalSpreasheets);

		} catch(Exception e)	{

			logger.log(Level.INFO, "[SPREADSHEETDETAILS][API] Error while spreadsheet listing", e);

			APIError.getInst(respFormat).GenErr(request, response, APIErrMsg.GenErr());
		}

	}

	private		List<String> getSharedByMeSpreadsheetList(JSONArray  sharedByMeSpreadsheet){

		List<String> 	spreadsheetList		= new ArrayList<>();

		if(sharedByMeSpreadsheet != null) {

			for(int i=0; i < sharedByMeSpreadsheet.length(); i++)	{
				try{
					JSONObject	spreadsheetJson		=	sharedByMeSpreadsheet.getJSONObject(i);
					if(spreadsheetJson.has("resource_id")) {

						spreadsheetList.add(spreadsheetJson.getString("resource_id"));
					}
				}catch(JSONException je) {
					logger.log(Level.INFO, "[SPREADSHEETDETAILS][API] Error while spreadsheet listing", je);
				}
			}
		}

		return	spreadsheetList;

	}
	/**
	 *
	 * @return List of arrayList{
	 * 0- Resource ID
	 * 1- Document Name
	 * 2- Scope
	 * 3- resource Type
	 * 4- resource permission
	 * 5- createdTime
	 * 6- Last modified time
	 * 7 - favourite
	 * 8- Document Onwer
	 * 9 - Last modified by
	 * 10- Locked by
	 * 11 - Shared By Me
	 * 12 - displayTime
	 * 13 - last opened time
	 * 14 - Is public link share?
	 * }
	 */
	private  List<Map<String,String>> getBooksInfo(JSONArray respArray, String orderBy)
	{

		List<Map<String,String>> bookInfo = new ArrayList<>();

		if(respArray != null && respArray.length() ==0)
		{
			return bookInfo;
		}

		try{
			Map<String,String> userDetails = new HashMap<>();
			Long tempValue;
			String stempValue;
			String documentOwner;
			String lastModifiedBy = null;
			Map<String, String> tempMap;
//		ArrayList<String> tempArrayList =  null;
			String modifiedBy;
			String modifiedbyZuid = null;
			String	resource_type;
			boolean isPublicLinkShare;
			String publishedType;
			String productName = null;
			if(respArray != null)
			{
				JSONObject childJsonObject;
				for (int i = 0; i < respArray.length(); i++) {
					childJsonObject = respArray.getJSONObject(i);
//				     tempArrayList =  new ArrayList<String>();
					tempMap = new HashMap<>();

					String			resourceID			=	childJsonObject.getString("resource_id");
					//Getting document Owner name
					tempValue = new Long(String.valueOf(childJsonObject.get("creator")));
					stempValue = String.valueOf(tempValue);
					if(userDetails.containsKey(stempValue)) {
						documentOwner = userDetails.get(stempValue);
					}
					else	{
						documentOwner =  DocumentUtils.getZFullName(Long.toString(tempValue));
						userDetails.put(stempValue, documentOwner);
					}
					try {
						modifiedBy = String.valueOf(childJsonObject.get("modified_by"));
						modifiedbyZuid = modifiedBy;
						if(modifiedBy.contains(",")){
							int lastCommaIndex = modifiedBy.lastIndexOf(",");
							modifiedBy = modifiedBy.substring(lastCommaIndex + 1);
						}

						boolean	isPublicUser	=	false;
						// Below code is taken from AuditTrailHelper -- to Handle the public user case
						if(modifiedBy.startsWith("$")){
							//ZUID will be some annon user id (wms annon id) in case of public editing. The nickname for that id will be maintained by zfs - Venkat
							isPublicUser	=	true;
							try {
//          	            	String guestUserDetails = ZohoFS.getGuestUserDetails(resourceID, String.valueOf(childJsonObject.get("creator")));
								String ownerID = ZohoFS.getOwnerZID(resourceID);
								String guestName = ZohoFS.getGuestName(ownerID, modifiedBy);
								if(guestName != null){
//          		            	JSONArray guestInfoJson = new JSONArray(guestUserDetails);
									lastModifiedBy = guestName;

								}else {
									lastModifiedBy = Constants.NON_LOGEDIN_COLLAB_USER_NAME;

								}
							}catch(Exception e){
								logger.log(Level.WARNING, "[SPREADSHEETDETAILS][API]Error while getting guestUserDetails : {0} | annonid : {1} | Message : {2}",new Object[]{resourceID, tempValue, e.getMessage()});
								lastModifiedBy = Constants.NON_LOGEDIN_COLLAB_USER_NAME;

							}
						}
						if(!isPublicUser) {
							tempValue = new Long(modifiedBy);
							stempValue = String.valueOf(tempValue);
							if(userDetails.containsKey(stempValue))	{
								lastModifiedBy = userDetails.get(stempValue);
							}
							else {
								lastModifiedBy =  DocumentUtils.getZFullName(Long.toString(tempValue));
								userDetails.put(stempValue, lastModifiedBy);
							}
						}
					} catch(Exception e) {
						logger.log(Level.INFO, "[SPREADSHEETDETAILS][API]Error occured while geting spreadsheet details getSpreadsheets api,problem in modified_by name ",e);
					}


					int	tempResource_type	=	Integer.parseInt(String.valueOf(childJsonObject.get("resource_type")));

					resource_type		=	APIUtil.getSpreadSheetType(tempResource_type);

					int	tempMaxPermission			=	Integer.parseInt(String.valueOf(childJsonObject.get("max_permission")));
//						String userName =IAMUtil.getCurrentUser().getLoginName();
//						String capabilities = ZohoFS.getCapabalitiy(DocumentUtils.getZUID(userName).toString(), resourceID);
					PermissionType	permission_type		=	AuthorizationUtil.getPermissionType(tempMaxPermission);

					int			permissionType		=	APIUtil.getMaxPermission(permission_type);

//					int			sharedByMe			=	sharedByMeSpreadsheets != null && sharedByMeSpreadsheets.contains(resourceID) ? 1 : 0 ;
					String			displayTime			=	orderBy.equals("STATUS_CHANGE_TIME") ? (String.valueOf(childJsonObject.get("status_change_time"))) :
							(orderBy.equals("CREATED_TIME") ? (String.valueOf(childJsonObject.get("created_time"))) :
									(orderBy.equals("LAST_OPENED_TIME") ? (String.valueOf(childJsonObject.get("last_opened_time"))): (String.valueOf(childJsonObject.get("status_change_time")))));

					isPublicLinkShare 					= 	false;
					publishedType = null;

					// Happens parsing exception for some users, so catching here.
					try {
						String 		shareInfo 			= 	childJsonObject.optString("shared_info");	//No I18N
						if(shareInfo !=null && !shareInfo.isEmpty()) {
							JSONArray shareInfoArr = new JSONArray(shareInfo);
							for(int index = 0; index < shareInfoArr.length(); index++) {
								JSONObject shareInfoObj = shareInfoArr.getJSONObject(index);
								String sharedTo = shareInfoObj.optString("SHARED_TO");	//No I18N
								String sharedType = shareInfoObj.optString("SHARED_TYPE");	//No I18N
								if("public".equals(sharedTo)) {
									isPublicLinkShare = true;
								}
								int type = Integer.parseInt(sharedType);

								if(type == ShareConstants.SHAREDTYPE_PUBLIC) {
									publishedType = "0";
								} else if(type == ShareConstants.SHAREDTYPE_ORG_PUBLISH) {
									publishedType = "1";
								}
							}
						}
					}catch (Exception ex) {
						// TODO: handle exception
						logger.log(Level.INFO, "[SPREADSHEETDETAILS][API]Error occured while geting spreadsheet details getSpreadsheets api",ex);

					}
					try {
						ProductInfo productType=ZohoFS.getProductInfo(resourceID);
						productName=ProductType.getProductNameForProductType(productType.getProductType());
						productName=productName.equals(ProductType.TEAMDRIVE.getName())?"WORKDRIVE":productName;//No I18N
					}catch(Exception e) {
						logger.log(Level.INFO, "[SPREADSHEETDETAILS][API]Error occured while geting product name ",e);
					}

				     String extraProperties = childJsonObject.getString("extra_prop");
				     JSONObject extraPropObj = new JSONObject();
				     if(childJsonObject.getString("extra_prop") != null) {
				    	 extraPropObj = new JSONObject(extraProperties);
				     }
					tempMap.put("finalStatus", String.valueOf(SheetListingAction.getFinalStatusFromAdditionalInfo(extraPropObj)));
					tempMap.put("resourceID",resourceID);
					tempMap.put("modifiedbyZuid",modifiedbyZuid);
					tempMap.put("name",childJsonObject.getString("name"));
					tempMap.put("scope",String.valueOf(childJsonObject.get("scope")));
					tempMap.put("resource_type",resource_type);
					tempMap.put("permissionType",Integer.toString(permissionType));
					tempMap.put("created_time",String.valueOf(childJsonObject.get("created_time")));
					tempMap.put("status_change_time",String.valueOf(childJsonObject.get("status_change_time")));
					tempMap.put("favorite",String.valueOf(childJsonObject.get("favorite")));
					tempMap.put("documentOwner",documentOwner);
					tempMap.put("lastModifiedBy",lastModifiedBy);
					tempMap.put("locked_by",childJsonObject.has("locked_by") ? String.valueOf(childJsonObject.get("locked_by")) : "");
//					tempMap.put("sharedByMe",Integer.toString(sharedByMe));
					tempMap.put("displayTime",displayTime);
					tempMap.put("last_opened_time",String.valueOf(childJsonObject.get("last_opened_time")));
					tempMap.put("isPublicLinkShare",String.valueOf(isPublicLinkShare));
					tempMap.put("publishedType", publishedType);
					tempMap.put("productName", productName);
					tempMap.put("ownerZuid", String.valueOf(childJsonObject.get("creator")));
					bookInfo.add(tempMap);


				}
			}
		}
		catch (Exception e) {

			logger.log(Level.INFO, "[SPREADSHEETDETAILS][API]Error occured while geting spreadsheet details getSpreadsheets api",e);
		}
		return bookInfo;
	}
}
