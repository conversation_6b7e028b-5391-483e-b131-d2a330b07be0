/* $Id$ */
package com.zoho.sheet.api.handler.privateGet;

import com.zoho.sheet.api.*;
import com.adventnet.ds.query.*;
import java.util.*;
import java.sql.SQLException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * To process API request asking for list of workbooks
 */
public class GetBooksHandler1 implements MethodHandler
{
	public static Logger logger = Logger.getLogger(GetBooksHandler1.class.getName());
    public static final GetBooksHandler1 INST = new GetBooksHandler1();

    public void handleMethod(Map struct, HttpServletRequest request, HttpServletResponse response)
    {
        String respFormat = (String)struct.get("resp-format");

        GetBooksHandler.Params params = GetBooksHandler.getParamsInstance();
        if (!params.init(respFormat, request, response))
        {
            return;
        }
        SelectQuery sQuery = null;//getSelectQuery(accId, params);
        List dataSet = null;
        /*try
        {
			OpenDocBean openDocBean = (OpenDocBean) APIUtil.lookupBean("OpenDocBean");//No I18N
        	dataSet = openDocBean.fetchAllRows(sQuery);
        }
        catch (SQLException e)
        {
            APIError.getInst(respFormat).DBErr(request, response, APIErrMsg.GenErr());
            return;
        }
        catch (QueryConstructionException e)
        {
            APIError.getInst(respFormat).GenErr(request, response, APIErrMsg.GenErr());
            return;
        }
        APIResponse.getInst(respFormat).GetWorkbooks1(request, response, processDataSet(dataSet));*/
        //return;
    }

    private SelectQuery getSelectQuery(Long accId, GetBooksHandler.Params params)
    {
        /*SelectQuery sQuery = com.adventnet.zoho.websheet.api.handler.userView.GetBooksHandler.getSelectQuery(
                accId, params.range, params.sortCol);
        sQuery.addSelectColumn(new Column("Tags", "TAG_NAME"));
        sQuery.addSelectColumn(new Column("SharedDocuments", "SHARED_TO"));
        sQuery.addSelectColumn(new Column("SharedDocuments", "ALLOW_TO_WRITE"));
        sQuery.addJoin(new Join("Documents", "SharedDocuments", new String[]{"DOCUMENT_ID"}, new String[]{"DOCUMENT_ID"},
                Join.LEFT_JOIN));
        sQuery.addJoin(new Join("Documents", "Tags_X_Util", new String[]{"DOCUMENT_ID"}, new String[]{"DOCUMENT_ID"},
                Join.LEFT_JOIN));
        sQuery.addJoin(new Join("Tags_X_Util", "Tags", new String[]{"TAG_ID"}, new String[]{"TAG_ID"}, Join.LEFT_JOIN));
        return sQuery;*/
    	return null;
    }

    private Collection processDataSet(List dataSet)
    {
        Map tempMap = new HashMap();
        for (int i = 0; i < dataSet.size(); i++)
        {
            Object[] row = (Object[]) dataSet.get(i);
            if(tempMap.containsKey(row[0]))
            {
                PrivateDoc thisDoc = (PrivateDoc)tempMap.get(row[0]);
                thisDoc.addTag((String)row[TAG_NAME]);
                thisDoc.addSharedToUser((String)row[SHARED_TO], (Boolean)row[ALLOW_TO_WRITE]);
            }
            else
            {
                PrivateDoc tempDoc = new PrivateDoc((Long)row[DOCUMENT_ID], (String)row[DOCUMENT_NAME], (Long)row[CREATED_DATE],
                        (Long)row[LAST_MODIFIED_TIME], (String)row[LAST_AUTHOR_NAME], (String)row[ACCOUNT_NAME]);
                tempDoc.addTag((String)row[TAG_NAME]);
                tempDoc.addSharedToUser((String)row[SHARED_TO], (Boolean)row[ALLOW_TO_WRITE]);
                tempMap.put(row[DOCUMENT_ID], tempDoc);
            }
        }
        return tempMap.values();
    }

    private static final int DOCUMENT_ID = 0;
    private static final int DOCUMENT_NAME = 1;
    private static final int CREATED_DATE = 2;
    private static final int LAST_MODIFIED_TIME = 3;
    private static final int LAST_AUTHOR_NAME = 4;
    private static final int ACCOUNT_NAME = 5;
    private static final int TAG_NAME = 6;
    private static final int SHARED_TO = 7;
    private static final int ALLOW_TO_WRITE = 8;


    public static class PrivateDoc
    {
        public final Long docId;
        public final String docName;
        public final Long createdTime;
        public final Long lastModifedTime;
        public final String lastModifiedBy;
        public final String lockedBy;
        public final Long updateLock;

        private Set tags;
        private List sharedToUsers;
        private List users; // temporary hack - todo: use DO implementation instead of manually processing the dataset

        public PrivateDoc(Long docId, String docName, Long createdTime, Long lastModifedTime, String lastModifiedBy,
                          String lockedBy)
        {
            this.docId = docId;
            this.docName = docName;
            this.createdTime = createdTime;
            this.lastModifedTime = lastModifedTime;
            this.lastModifiedBy = lastModifiedBy;
            this.lockedBy = lockedBy;
            this.updateLock = lastModifedTime;
        }

        void addTag(String tag)
        {
            if(tag == null)
            {
                return;
            }
            if(tags == null)
            {
                tags = new HashSet();
            }
            tags.add(tag);
        }

        void addSharedToUser(String userName, Boolean isWrite)
        {
            if(userName == null || isWrite == null)
            {
                return;
            }
            if(sharedToUsers == null)
            {
                sharedToUsers = new ArrayList();
                users = new ArrayList();
            }
            if(! users.contains(userName))
            {
                sharedToUsers.add(SharedToUser.getUserInst(userName, isWrite.booleanValue()));
                users.add(userName);
            }
        }

        public Set getTags()
        {
            return tags != null ? tags : Collections.EMPTY_SET;
        }

        public List getSharedToUsers()
        {
            return sharedToUsers != null ? sharedToUsers : Collections.EMPTY_LIST;
        }

    }

    public static class SharedToUser
    {
        private String loginName;
        private String emailId;
        private String permission;

        public String getLoginName()
        {
            return loginName;
        }

        public String getEmailId()
        {
            return emailId;
        }

        public String getPermission()
        {
            return permission;
        }

        static SharedToUser getUserInst(String userName, boolean isWrite)
        {
            SharedToUser retObj = new SharedToUser();
            retObj.loginName = userName;
            try
            {
                retObj.emailId = null;//SasUtil.getInstance().getEMailForUser(userName);
            }
            catch (Exception e)
            {
				logger.log(Level.WARNING,null,e);
            }
            retObj.permission = isWrite ? "readWrite" : "readOnly";//NO I18N
            return retObj;
        }
    }

}
