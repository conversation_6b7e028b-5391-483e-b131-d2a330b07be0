/* $Id$ */
package com.zoho.sheet.api.remote;

 import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
 import com.adventnet.zoho.websheet.model.WorkbookContainer;
 import com.adventnet.zoho.websheet.model.redis.RedisHelper;
 import com.adventnet.zoho.websheet.model.util.ActionConstants;
 import com.adventnet.zoho.websheet.model.util.MessagePropagator;
 import com.zoho.sheet.util.*;
 import com.zoho.sheet.util.RemoteUtils.Keys;
 import com.adventnet.zoho.websheet.model.redis.RedisHelper;


 import javax.servlet.http.HttpServletResponse;
 import java.util.Map;
 import java.util.logging.Level;
 import java.util.logging.Logger;
 import java.util.logging.Level;


 public class SaveRemoteBookAction extends StrutsRequestHandler {

     public static Logger logger = Logger.getLogger(SaveRemoteBookAction.class.getName());

     public String execute() throws Exception {
         // -- Initialization -- //
         // Setting user name based on AuthRemoteAPI
         //String ownerName =  IAMUtil.getCurrentUser() != null ? IAMUtil.getCurrentUser().getLoginName(): Constants.REMOTE_USER;
         //boolean isAuthRemoteAPI = RemoteUtils.isRemoteDocument(ownerName);
         // Parsing details
         String doc = RemoteUtils.maskNull((String) request.getParameter("doc"));
         Map<Keys, Object> coreDetails = RemoteUtils.getDecryptedContent(doc);
         Long remoteBookId = (Long) coreDetails.get(Keys.REMOTE_BOOK_ID);
         Long bookStateId = (Long) coreDetails.get(Keys.REMOTE_BOOK_STATE_ID);
         int mode = (Integer) coreDetails.get(Keys.MODE);

         String _pushFormat = RemoteUtils.maskNull((String) request.getParameter("format"));
         WorkbookContainer container = CurrentRealm.getContainer();
         String userName = container.getDocOwner();
         String proxyUrl = request.getParameter("proxyURL");

         if("remoteautosave".equalsIgnoreCase(proxyUrl)){

             container.save(null,null,false);
             response.getWriter().println("success"); //NO I18N
             return null;

         }
         Integer responseStatus = null;
         String responseMsg = null;
         String loggerMsg = null;
         String reload = "false";

         // -- Validation -- //
         if (mode == 0 || mode == 3) {
             // Check the read write permission for the document.
             responseStatus = HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
             responseMsg = LocaleMsg.getMsg("RemoteEditor.SaveMessage.PermissionDenied");
             loggerMsg = "Permission denied [Dont have read/Write permission] - " + remoteBookId + " ( " + doc + " )"; //NO I18N
         }


         //  -- Remote File Save -- //


         String docId = null;
         String userId = userId = CurrentRealm.getUserProfile().getZUserId();

         JSONObjectWrapper responseObject = RemoteUtils.saveRemoteBook(container, remoteBookId, bookStateId, userId, _pushFormat, false);
         responseStatus = (Integer) responseObject.get("responseStatus");
         responseMsg = responseObject.has("responseMsg") ? (String) responseObject.get("responseMsg") : "";
         try {
             RedisHelper.deleteUndoRedoList(request.getParameter("rid"), request.getParameter("utid"));
         }catch (Exception e){
             logger.log(Level.SEVERE,"Undo redo delete req failed",e);
         }
         loggerMsg = responseObject.has("loggerMsg") ? (String) responseObject.get("loggerMsg") : "";


         // -- Sending response  -- //

         //API call settings
         Boolean isAPICall = "true".equalsIgnoreCase((String) request.getParameter("apicall"));

         JSONObjectWrapper json = new JSONObjectWrapper();
         response.setContentType("application/json");

         if (isAPICall) {

             //{"statusValue":<partnermessage|our message>,"statusCode": 200 or other http status code}
             json.put("statusCode", responseStatus);
             json.put("statusValue", responseMsg);


             logger.info("EXTERNAL REMOTE SAVE - user: " + userId + " , collabId: " + container.getCollabId() + " , msg:" + json);
             MessagePropagator.sendExternalRemoteSaveResponse(container, json, userId);

             response.getWriter().println(json); //NO OUTPUTENCODING

         } else {
             json.put("msg", ClientUtils.replaceJsonSpclChars(responseMsg));
             logger.info("my response:::" + responseObject);
             if (responseObject.has("delete")) {
                 json.put("delete", responseObject.getString("delete"));
             }
             if (responseStatus == HttpServletResponse.SC_OK || responseStatus == HttpServletResponse.SC_PARTIAL_CONTENT) {

                 if (responseObject.has("reload")) {
                     json.put("reload", responseObject.getString("reload"));

                 }
                 if (responseObject.has("rowerror")) {
                     json.put("rowerror", responseObject.getString("rowerror"));
                 }
                 if (responseObject.has("wait")) {
                     json.put("wait", responseObject.getString("wait"));
                 }

                 if (responseObject.has("status")) {
                     json.put("status", responseObject.getString("status"));

                 } else if (responseStatus == 206) {
                     json.put("status", "partial");
                 } else {
                     json.put("status", "success");

                 }
                 if (responseObject.has("row")) {
                     json.put("row", responseObject.getInt("row"));
                     json.put("col", responseObject.getInt("col"));
                 }
                 json.put("statusCode", responseStatus);
             } else {
                 json.put("reload","true");
                 json.put("status", "failure");
             }
             logger.info("my save response json:::" + json);
             if(json.has("reload")){
                 logger.info("my save response json messge:::" + json);
                 RemoteUtils.sendCollabMsg(remoteBookId,bookStateId,"",userName,ActionConstants.SAVESTATUSUPDATE,json.toString());
             }


             response.getWriter().println(json); //NO OUTPUTENCODING
         }


         return null;
     }

 }
        
