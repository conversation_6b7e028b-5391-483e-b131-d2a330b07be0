/* $Id$ */
package com.zoho.sheet.api.remote;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.sheet.util.StrutsRequestHandler;

import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.ear.common.util.EARException;
import com.zoho.ear.encryptagent.EncryptAgent;
import com.zoho.sheet.util.RemoteUtils;
import com.adventnet.persistence.*;
import com.adventnet.ds.query.*;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;

import java.util.logging.Logger;
import java.util.logging.Level;

/** <AUTHOR> 
 * 
 * 	@Notes
 * 	Used to get the Remote Books Collaborators Count
 */
public class RemoteBookStatusAction extends StrutsRequestHandler
{
	public static Logger logger = Logger.getLogger(RemoteBookStatusAction.class.getName());
	
	public String execute() throws Exception {
		
		//@attribute: apikeyid - set by ZAPI team
				if(request.getAttribute("apikeyid") == null) {
					logger.info(" Invalid API key. ");
					request.setAttribute("errorCode", 2890); //NO I18N
					RemoteUtils.throwAPIErorr(request, response);
					return null;
				}
				
		/* -- Input values -- */
		//@param: doc - either Integer or Encrypted String(cryptoUtil)
		String _userDocId = RemoteUtils.maskNull((String)request.getParameter("doc"));
		
		/* -- API key validation --*/
		
		Long apiKeyId = Long.valueOf((String) request.getAttribute("apikeyid"));

		/* -- Doc Id validation -- */
		String userDocId = null;
		try {
			userDocId = _userDocId;
		} catch (Exception e){
			try{
				//userDocId = Long.valueOf(com.adventnet.iam.CryptoUtil.decrypt("zohosheet_secret", _userDocId));//No I18N
				try{
					String refreshToken = EnginePropertyUtil.getSheetPropertyValue("EAR_REFRESH_TOKEN");	//No I18N
					userDocId = EncryptAgent.getInstance().decrypt("zohosheet_secret", _userDocId, refreshToken, false);	//No I18N
				}catch (EARException ear) {
					logger.log(Level.INFO, "RemoteBookStatusAction decryptAuthToken Ear Decryption Failed, Resolve this ", ear.getMessage());
					//userDocId = com.adventnet.iam.CryptoUtil.decrypt("zohosheet_secret", _userDocId);//No I18N
				}
			} catch(Exception e1){
				logger.info("Value specified for 'doc' parameter is incorrect.");
				request.setAttribute("errorCode", 2850); //NO I18N
				request.setAttribute("secondaryErrorMessage", "doc"); //NO I18N
				RemoteUtils.throwAPIErorr(request, response);
				return null;
			}
		}
		logger.info("_userDocId: " + _userDocId  +", userDocId : " + userDocId);
		
		/* -- Getting collaborators count -- */
		int collabCnt = getCollaboratorsCount(userDocId, apiKeyId);
		
		/*-- Response formation --*/
		JSONObjectWrapper resObj = new JSONObjectWrapper();
		resObj.put("docid", _userDocId); //NO I18N
		resObj.put("isPresent", ((collabCnt == -1) ? false : true)); //NO I18N
		resObj.put("collaboratorsCount", ((collabCnt == -1) ? 0 : collabCnt)); //NO I18N

		JSONObjectWrapper result = new JSONObjectWrapper();
		result.put("result", resObj); //NO OUTPUTENCODING //NO I18N
		logger.info("result : " + result);
		
		response.setContentType("text/plain;charset=utf-8");
		try {
			response.getWriter().println(result); //NO OUTPUTENCODING
		} catch (Exception e) {
			logger.log(Level.WARNING,null,e);
		}
		return null;
	
	}
	
	private static Integer getCollaboratorsCount(String userDocId, Long apiKeyId){
		// Response: For error case: -1 , success case: valid count 
		try{
		
			SelectQueryImpl sql = new SelectQueryImpl(new Table("RemoteBooks")); //No I18N
			sql.addSelectColumn(new Column("RemoteBooks_State", "*")); //NO I18N
			sql.addJoin(new Join("RemoteBooks", "RemoteBooks_State", new String[] {"REMOTE_BOOK_ID"}, new String[] {"REMOTE_BOOK_ID"}, Join.INNER_JOIN)); //NO I18N
			Criteria cri = new Criteria(new Column("RemoteBooks", "USER_DOC_ID"), userDocId, QueryConstants.EQUAL); //NO I18N
			cri = cri.and(new Column("RemoteBooks","API_KEY_ID"),apiKeyId,QueryConstants.EQUAL); //NO I18N
			sql.setCriteria(cri);
			
			Persistence pers = SheetPersistenceUtils.getPersistence(Constants.REMOTE_USER);
			DataObject dataObj = pers.get(sql);
			if(!dataObj.isEmpty()) {
				return dataObj.size("RemoteBooks_State"); //NO I18N
			}
			
		} catch (Exception e) {
			logger.log(Level.WARNING,null,e);
		}
		
		return -1;
	}
}
