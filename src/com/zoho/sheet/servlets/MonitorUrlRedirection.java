	/*  $Id$ */
package com.zoho.sheet.servlets;
import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zoho.sheet.exception.ZohoSheetException;
	/**
	 * its a moniroting servelet , used for monitoring our service
	 */
	public class MonitorUrlRedirection  extends HttpServlet {
		public static Logger logger = Logger.getLogger(MonitorUrlRedirection.class.getName());

		protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ZohoSheetException, ServletException, IOException {
			try {
				process(req, resp, true);
			}
			catch(ZohoSheetException e) {
				throw e;
			}
			catch (Exception e) {
				logger.log(Level.WARNING,null,e);
			}
		}
		protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ZohoSheetException, ServletException, IOException {
			try {
				process(req, resp, false);
			}
			catch(ZohoSheetException e) {
				throw e;
			}
			catch (Exception e) {
				logger.log(Level.WARNING,null,e);
			}
		}
		private void process(HttpServletRequest request, HttpServletResponse response, boolean isGet) throws Exception {
	            try {
	            	String newURL	=	"/sheet/pages/monitor/monitor.html"; // NO I18N
	            	request.getRequestDispatcher(newURL).forward(request,response);
	            } catch (Exception ex) {
	            	logger.log(Level.SEVERE, null, ex);
	            }
		}
	}

