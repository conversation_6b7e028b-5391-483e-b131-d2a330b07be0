/* $Id$ */
package com.zoho.sheet.servlets;

import java.io.IOException;
import java.io.OutputStream;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zoho.sheet.conversion.FileConverter;

public class OfficeSuiteConverter extends HttpServlet  {
	public static Logger logger = Logger.getLogger(OfficeSuiteConverter.class.getName());

	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		try {
			byte[] content =null;
    		FileConverter converter = new FileConverter();
    		content =  converter.convertDocument(request);
    		if(content != null){
	    		String mimeType = converter.getMimeType();
	 			response.reset();    //IE with https (secured) connection export issue fix block starts
	 			response.setContentType(mimeType);
	 			response.setCharacterEncoding("UTF-8"); // No I18N
	
	 			String fileName = converter.getFileName()+"."+converter.getFormat();//ClientUtils.encodeFileName(((isExportName && activeSheetName != null) ? docName + '_' + activeSheetName : docName) + "." + format,userAgent);
	 			response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\""); // No I18N
				
	 			response.setCharacterEncoding("UTF-8");	// No I18N
	    		 OutputStream out = response.getOutputStream();
	 			 out.write(content); //NO OUTPUTENCODING    
	 			 out.flush();
    		}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.log(Level.WARNING, "[CONVERT API] Problem while CONVERTING a remote document",  e);
			
		}
	
}
		
}
