package com.zoho.sheet.servlets;

import com.adventnet.wms.api.WmsApi;
import com.adventnet.wms.common.WmsService;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.zoho.sheet.util.DFSFileReaderUtil;
import org.apache.commons.codec.binary.Base64;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Hashtable;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * This servlet contains basic handling of upload server's upload, httpcallback and download
 */
public class UploadFileServer extends HttpServlet {
    private static final Logger LOGGER = Logger.getLogger(UploadFileServer.class.getName());

    @Override
    public void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        handleResponse(req, resp);
    }

    @Override
    public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        handleResponse(request, response);
    }
    private void handleResponse(HttpServletRequest request, HttpServletResponse response) {

        try {
            /** The action param can be u,d,n
             u->upload
             d->download
             n->notify **/

            String action = request.getParameter("type");
            LOGGER.info("UploadFileServerc::SHEET_IMPORT_UPLOAD_SUCCESS::action::"+action);

            if(action.equals("u")) {

                LOGGER.info("UploadFileServer:: Auth Call:: UPLOAD");
                StringBuilder sb = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        sb.append(line);
                    }
                }

                JSONObjectWrapper authParamsObj = JSONObjectWrapper.fromString(sb.toString());
                JSONObjectWrapper authJSONParams;
                String authJson = null;

                if(authParamsObj.has("auth-params")) {
                    byte[] decodedBytes = Base64.decodeBase64(authParamsObj.get("auth-params").toString());
                    authJson = new String(decodedBytes);
                    String timestamp = String.valueOf(System.currentTimeMillis());
                    String zuid = null, uploadId = null;
                    LOGGER.info("UploadFileServer :: UPLOAD::authJson::"+authJson);
                    authJSONParams = JSONObjectWrapper.fromString(authJson);
                    uploadId = authJSONParams.getString("upload-id");
                    JSONObjectWrapper xMsg = JSONObjectWrapper.fromString(authJSONParams.getString("x-msg"));
                    String fileName = xMsg.getString("file-name");
                    zuid = authJSONParams.getString("zuid");
//                    timestamp = uploadId.substring("zsheet_upload_".length(), uploadId.length());
                    String dfsPath = zuid + "/sheet/" + timestamp + "_" + UUID.randomUUID().toString(); //No I18N
                    String filePath = java.util.Base64.getEncoder().encodeToString(dfsPath.getBytes());

                    //Adding mandatory headers to the response, the mandatory headers are upload-params,author-zuid,dfs-path,upload-id
                    response.addHeader("upload-params", authJson); //No I18N
                    response.addHeader("author-zuid", zuid); //No I18N
                    response.addHeader("upload-id", uploadId); //No I18N
                    response.addHeader("dfs-path", dfsPath); //No I18N
                    response.addHeader("x-dfs-service", "dfs"); //No I18N
                    response.addHeader("x-msg", java.util.Base64.getEncoder().encodeToString(("Zohosheet Upload Files Success -->"+fileName).getBytes())); //No I18N
                    response.addHeader("x-info", "true"); //No I18N
                }
            } else if(action.equals("n")) {

                String notifyParamsStr = request.getHeader("notify-params");
                LOGGER.info("notifyParam::"+request.getHeader("notify-params"));
//                LOGGER.info("UPLOAD-PARAMS ::"+request.getHeader("upload-params"));
//                LOGGER.info("ERROR ::"+request.getHeader("ERROR"));
                byte[] decodedBytes = Base64.decodeBase64(notifyParamsStr);
                String authJson = new String(decodedBytes);
                JSONObjectWrapper authJSONParams = JSONObjectWrapper.fromString(authJson);
                JSONObjectWrapper xMsg = JSONObjectWrapper.fromString(authJSONParams.getString("x-msg"));
                String fileName = xMsg.getString("file-name");

                // Adding upload params to REDIS

                JSONObjectWrapper uploadAuthParams = new JSONObjectWrapper();
                String uploadId = authJSONParams.getString("upload-id");
                LOGGER.info("UploadFileServer::uploadIDok::"+uploadId+"AUTHPARAMS::"+authJSONParams);
                String zuid = authJSONParams.getString("zuid");
                uploadAuthParams.set("author-zuid", zuid);
                uploadAuthParams.set("block-id", authJSONParams.getString("block-id"));
                uploadAuthParams.set("upload-id", uploadId);
                uploadAuthParams.set("dfs-path", authJSONParams.getString("dfs-path"));
                uploadAuthParams.set("file-size", authJSONParams.getString("file-size"));
                uploadAuthParams.set("dfs-service", authJSONParams.getString("dfs-service"));
                uploadAuthParams.put("file-name", fileName);
                String tabId = "";
                String[] uploadIdParams = uploadId.split("_");

                if(uploadIdParams.length == 3) {
                    JSONObjectWrapper message = new JSONObjectWrapper();
                    message.put("zuid", zuid);
                    message.put("uploadId", uploadId);
                    message.put("a", "FILE_UPLOAD_SUCCESS_CALLBACK");
                    tabId = uploadId.split("_")[1];
                    message.put("tabId", tabId);
                    uploadAuthParams.set("tab-id", tabId);
                    WmsApi.sendCustomMessage(""+zuid, message);
                } else {
                    Hashtable<String, String> msgobj = new Hashtable<String, String>();
                    msgobj.put("zuid", zuid);
                    msgobj.put("uploadId", uploadId);
                    msgobj.put("action", "FILE_UPLOAD_SUCCESS_CALLBACK");// No I18N
                    WmsApi.sendCrossProductMessage(""+zuid, msgobj, WmsService.SHEET);
                }
//                RedisHelper.del(zuid);
                RedisHelper.set(RedisHelper.UPLOAD_SERVER_ID + uploadId, uploadAuthParams.toString(), 300);
//                WmsApi.sendCustomMessage(zuid, uploadAuthParams.toString());
            }
//            else if(action.equals("d")) {//Download authorization request from upload server we will receive blockId and dfs file name,filesize ,zuId
//
//                LOGGER.info("UploadFileServer :: DOWNLOAD ");
//                StringBuilder sb = new StringBuilder();
//                try (BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()))) {
//                    String line;
//                    while ((line = reader.readLine()) != null) {
//                        sb.append(line);
//                    }
//                }
//
//                JSONObject authParamsObj = JSONObject.fromString(sb.toString());
//                JSONObject authJSONParams;
//                String authJson = null;
//                String zuid = null, blockId = null;
//
//                if(authParamsObj.has("download-params")) {
//                    byte[] decodedBytes = Base64.decodeBase64(authParamsObj.get("download-params").toString());
//                    authJson = new String(decodedBytes);
//
//                    LOGGER.info("UploadFileServer :: DOWNLOAD::authJson::" + authJson);
//                    authJSONParams = JSONObject.fromString(authJson);
//                    zuid = authJSONParams.getString("zuid");
//                    String eventId = authJSONParams.getString("event-id");
//                    String fileUploadDetailsObj = RedisHelper.get(zuid, 300);
//                    LOGGER.info("UploadFileServer :: REDIS::GET::fileUploadDetailsObj::"+fileUploadDetailsObj);
//
//                    if(fileUploadDetailsObj != null) {
//
//                        JSONObject fileUploadDetailsJson = JSONObject.fromString(fileUploadDetailsObj);
//                        LOGGER.info("UploadFileServer :: fileUploadDetailsJson::"+fileUploadDetailsJson);
//                        String dfsPath = fileUploadDetailsJson.getString("dfs-path");
//                        String fileSize = fileUploadDetailsJson.getString("file-size");
//                        blockId = fileUploadDetailsJson.getString("block-id");
//                        String dfsService = fileUploadDetailsJson.getString("dfs-service");
//                        String tabId = fileUploadDetailsJson.getString("tab-id");
//                        String fileName = fileUploadDetailsJson.getString("file-name");
//
////                    String dfsPath = "15256126/sheet/1723468850598_b39b8bed-9b54-43d7-8a3d-4429a2cfca0c";
////                    String fileSize = "261";
////                    String dfsService = "streamline"; //No I18N
////                    blockId = "NN902:3622411757933969574";
//
//                        //These are the mandatory headers that must be added during download authorization request
//                        response.addHeader("author-zuid",zuid); //No I18N
//                        response.addHeader("block-id",blockId); //No I18N
//                        response.addHeader("dfs-path",dfsPath); //No I18N
//                        response.addHeader("file-size",fileSize); //No I18N
//                        response.addHeader("x-dfs-service", dfsService); //No I18N
//                        response.addHeader("download-params",request.getHeader("download-params")); //No I18N
//
//                        /*JSONObject message = new JSONObject();
//                        message.put("zuid", zuid);
//                        message.put("tabId", tabId);
//                        message.put("a", "FILE_DOWNLOAD_AUTHCALL_SUCCESS_CALLBACK");
//                        message.put("blockId", blockId);
//                        WmsApi.sendCustomMessage(zuid, message);*/
//
//                        JSONObject downloadAuthParams = new JSONObject();
//                        downloadAuthParams.put("block-id", blockId);
//                        downloadAuthParams.put("dfsService", dfsService);
//                        downloadAuthParams.put("dfsPath", dfsPath);
//                        downloadAuthParams.put("file-name", fileName);
////                      WmsApi.sendCustomMessage(zuid, uploadAuthParams.toString());
//                        RedisHelper.set(zuid, downloadAuthParams.toString(), 60);
//                        LOGGER.info("UploadFileServer :: REDIS::SET::downloadAuthParams::"+downloadAuthParams);
////                        String od = "/Users/<USER>/PONDSSUCCESS1.csv"; //No I18N
////                        DFSFileReaderUtil.readFile("READ", zuid, dfsService, dfsPath, blockId, od); //No I18N
//                    }
//                }
//            }
            response.setStatus(200);
            response.getWriter().println("Hello Zohosheet"); //No I18N
        }
        catch(Exception ex) {
            LOGGER.log(Level.WARNING, "Exception On UploadFileServer", ex); //No I18N
        }
    }
}