/*  $Id$ */
package com.zoho.sheet.servlets;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.StringTokenizer;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.sheet.exception.ZohoSheetException;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.zfsng.constants.ZFSNGConstants;

import java.util.logging.Logger;
import java.util.logging.Level;

public class PublicServlet extends HttpServlet {
	public static Logger logger = Logger.getLogger(PublicServlet.class.getName());

	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ZohoSheetException, ServletException, IOException {
		try {
			process(req, resp, true);
		}
		catch(ZohoSheetException e) {
			throw e;
		}
		catch (Exception e) {
			logger.log(Level.WARNING,null,e);
		}
	}
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ZohoSheetException, ServletException, IOException {
		try {
			process(req, resp, false);
		}
		catch(ZohoSheetException e) {
			throw e;
		}
		catch (Exception e) {
			logger.log(Level.WARNING,null,e);
		}
	}
	private void process(HttpServletRequest req, HttpServletResponse resp, boolean isGet) throws Exception {
		String publicServer = EnginePropertyUtil.getSheetPropertyValue("PublicServer");    //No I18N
		String path = ClientUtils.getServerURL(req, false, null, false, false);
		String reqUri = req.getRequestURI();
		// contextpath setting
		String ctxpath = req.getContextPath();
		String isCorp = req.getParameter("isCorp");

		/*String redirected = (String)req.getParameter("redirected");
		if(ctxpath != "" && reqUri.indexOf(ctxpath) != -1) {
			reqUri = reqUri.substring(ctxpath.length(),reqUri.length());
		}*/
		if (!"true".equals(isCorp) && !reqUri.startsWith("/org") && !reqUri.startsWith("/corporate") && publicServer != null && path.indexOf(publicServer) == -1) {  //No I18N
			String uri = req.getRequestURI();
			String qString = req.getQueryString();
			path = publicServer;
			if(uri != null) {
				path = path + req.getRequestURI();
			}
			if(qString != null) {
				path = path + "?"+req.getQueryString();
			} else {
				path = path.replace("/public.do", "/public"); //No I18N
			}
			resp.sendRedirect(req.getScheme() + "://" + path); //No I18N
			return;
		}/* else if (reqUri.startsWith("/corporate") && publicServer != null && path.indexOf(publicServer) != -1) {
			String appServer = SheetPropertyUtil.getPropertyValue("AppServer");
			String uri = req.getRequestURI();
			String qString = req.getQueryString();
			String schema = req.getScheme();
			path = schema + appServer;
			if(uri != null) {
				path = path + req.getRequestURI();
			}
			if(qString != null) {
				path = path + "?"+req.getQueryString();
			} else {
				path = path.replace("/public.do", "/public");
			}
			System.out.println("corp redirecting url..."+path);
			resp.sendRedirect(path);
			return;
		}*/

		String forwardURL = "";
		int cnt = 0;
		String docURL = "";
		StringTokenizer uriTokens = new StringTokenizer(reqUri, "/"); //No I18N

		if (reqUri.startsWith("/public")) {  //No I18N
                    logger.info(" inside public ");
			if (publicServer != null && path.indexOf(publicServer) != -1) {
				forwardURL = "/public.do?"; //No I18N
			}
			forwardURL = "/public.do?"; //No I18N
			while(uriTokens.hasMoreTokens()) {
				if (cnt == 1) {
					forwardURL += "name="+ URLEncoder.encode(uriTokens.nextToken(), "UTF-8"); //No I18N
				} else if (cnt > 1) {
					docURL += URLEncoder.encode(uriTokens.nextToken(), "UTF-8") + "/"; //No I18N
				} else {
					String temp = uriTokens.nextToken();
				}
				cnt++;
			}
			docURL = (docURL.length() > 0) ? docURL.substring(0, docURL.length() - 1) : docURL;
			forwardURL +=  "&iscorp=false&docurl="+ docURL; //No I18N
		} else if (reqUri.startsWith("/templates")) { //No I18N
                    logger.info(" inside templates ");
			forwardURL = "/public.do?"; //No I18N
			while(uriTokens.hasMoreTokens()) {
				if (cnt == 1) {
					forwardURL += "name="+ URLEncoder.encode(uriTokens.nextToken(), "UTF-8"); //No I18N
				} else if (cnt > 1) {
					docURL += URLEncoder.encode(uriTokens.nextToken(), "UTF-8") + "/"; //No I18N
				} else {
					String temp = uriTokens.nextToken();
				}
				cnt++;
			}
			docURL = (docURL.length() > 0) ? docURL.substring(0, docURL.length() - 1) : docURL;
			forwardURL +=  "&iscorp=false&template=true&docurl="+ docURL;	 //No I18N
		} else if (reqUri.startsWith("/corporate")) { //No I18N
                        logger.info(" inside corporate ");
			//String appServer = SheetPropertyUtil.getPropertyValue("AppServer");
			forwardURL = "/corporate.do?"; //No I18N
			boolean isRedirect = false;
			while(uriTokens.hasMoreTokens()) {
				if (cnt == 1) {
					forwardURL += "orgid="+ URLEncoder.encode(uriTokens.nextToken(), "UTF-8"); //No I18N
				} else if (cnt == 2) {
					forwardURL += "&name="+ URLEncoder.encode(uriTokens.nextToken(), "UTF-8"); //No I18N
				} else if (cnt > 2) {
					String _docURL = uriTokens.nextToken();
					String redirectURL = DocumentUtils.getRedirectURL(req, _docURL);

					if(redirectURL != null) {
						//redirecting org public url to open.do
						resp.sendRedirect(redirectURL);
						//isRedirect = true;
						//forwardURL = redirectURL;
					} else {
						docURL += URLEncoder.encode(_docURL, "UTF-8") + "/";// No I18N
					}
				} else {
					String temp = uriTokens.nextToken();
				}
				cnt++;
			}
			/*if(!isRedirect) {
				docURL = (docURL.length() > 0) ? docURL.substring(0, docURL.length() - 1) : docURL;
				forwardURL += "&iscorp=true&docurl="+ docURL;
				if("true".equals(redirected)) {	
					forwardURL += "&redirected="+redirected;
				} else {
					forwardURL += "&redirected="+reqUri;
				}	
			}*/
                        docURL = (docURL.length() > 0) ? docURL.substring(0, docURL.length() - 1) : docURL;
			forwardURL += "&iscorp=true&docurl="+ docURL; //No I18N
                        
		} else if (reqUri.startsWith("/publish")) { //No I18N
                    logger.info(" inside publish ");
			forwardURL = "/publish.do?"; //No I18N
			while(uriTokens.hasMoreTokens()) {
				if (cnt == 1) {
					forwardURL += "name="+ URLEncoder.encode(uriTokens.nextToken(), "UTF-8"); //No I18N
				} else if (cnt > 1) {
					docURL += URLEncoder.encode(uriTokens.nextToken(), "UTF-8") + "/"; //No I18N
				} else {
					String temp = uriTokens.nextToken();
				}
				cnt++;
			}
			docURL = (docURL.length() > 0) ? docURL.substring(0, docURL.length() - 1) : docURL;
			forwardURL += "&iscorp=false&docurl="+ docURL; //No I18N
		}
		String 	rid				 =	(String) req.getAttribute(ZFSNGConstants.RID);
		String isOrgPublished	 =  (String)req.getAttribute(ZFSNGConstants.IS_ORGPUBLISHED);
		AccessType accesstype = AccessType.PUBLIC_EXTERNAL;
		if(isOrgPublished != null && "true".equals(isOrgPublished)){
			accesstype = AccessType.PUBLIC_ORG;
		}
		String requri = req.getRequestURI();
		String urlSplit[] = requri.split("/");
		if("e".equals(urlSplit[3]) || "o".equals(urlSplit[3])){
			req.getRequestDispatcher(requri).forward(req, resp);//No I18N
		}else{
			req.getRequestDispatcher(DocumentUtils.getDocControllerPath(accesstype, rid, null, true, req)).forward(req, resp);
		}
		//return;
		//req.getRequestDispatcher(forwardURL).forward(req, resp);
	}
}
