/* $Id$ */
package com.zoho.sheet.servlets;

import com.adventnet.ds.query.*;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.ear.encryptagent.EncryptAgent;
import com.zoho.sheet.conversion.Exporter;
import com.zoho.sheet.conversion.ImportExportUtil;
import com.zoho.sheet.util.*;
import com.zoho.sheet.util.RemoteUtils.Keys;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class OfficeSuiteURLMapper extends HttpServlet  {
	public static Logger logger = Logger.getLogger(OfficeSuiteURLMapper.class.getName());

	protected void doDelete(HttpServletRequest req, HttpServletResponse resp) {

		try {
			if(req.getRequestURI().contains("session")){
				logger.info("Delete MESSAGE TRIGGERED:::::deleteSessionAPI");
				deleteSessionAPI(req,resp);
			}else{
				logger.info("Delete MESSAGE TRIGGERED:::::deleteAPI");
				deleteAPI(req,resp);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.log(Level.WARNING, "[DELETE API] Problem while deleting a remote document",  e);

		}

	}
	protected void doPost(HttpServletRequest req, HttpServletResponse resp){
		try{
			if(req.getRequestURI().contains("save")){
				String[] uri = req.getRequestURI().split("/");
				String doc = uri[uri.length -2];
				JSONObjectWrapper response = RemoteUtils.externalSave(req, doc, false);
				JSONObjectWrapper responseObj = new JSONObjectWrapper();
				responseObj.put("doc_save", "true");
				resp.getWriter().print(responseObj); // NO OUTPUTENCODING
			}else if(req.getRequestURI().contains("download")) {//No I18N
				downloadAPI(req,resp);
			}





		}catch(Exception e){
			logger.log(Level.WARNING, "[SAVE API] Problem while saving  a remote document",  e);

		}
	}

	protected void doGet(HttpServletRequest req, HttpServletResponse resp){
		try{
			if(req.getRequestURI().contains("sessions")){
				String[] uri = req.getRequestURI().split("/");
				String _apikeyid = (String) req.getAttribute("apikeyid");

				String doc = uri[uri.length -1];
				JSONObjectWrapper response = RemoteUtils.getSessionInfo(doc,_apikeyid);
				resp.setContentType("application/json;charset=UTF-8");
				resp.getWriter().print(response); // NO OUTPUTENCODING
			}
		}catch(Exception e){
			logger.log(Level.WARNING, "[SAVE API] Problem while saving  a remote document",  e);

		}
	}



	private  void deleteSessionAPI(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String[] uri = request.getRequestURI().split("/");
		String sessionId = uri[uri.length -1];
		Map<Keys, Number> decryptContent = null;
		String remoteUser;
		Long remoteBookState;
		try{
			decryptContent = decryptContent(sessionId);

			//remoteUser = decryptContent.get(1);
		}catch(Exception e){
			logger.log(Level.INFO, "[DELETE API] Decrytpion failed trying with original value");
		}
		String apikeyid = (String) request.getAttribute("apikeyid");//No I18N;
		if(decryptContent != null){
			int spaceCounter = (int) decryptContent.get(Keys.SPACECOUNTER);
			remoteUser = Constants.REMOTE_USERS[spaceCounter];
			remoteBookState = (Long) decryptContent.get(Keys.REMOTE_BOOK_STATE_ID);
			Persistence  pers = SheetPersistenceUtils.getPersistence(remoteUser);
			Row delDocRow = null;
			Criteria remoteBookStateCri = new Criteria(new Column("RemoteBooks_State","REMOTE_BOOK_STATE_ID"),remoteBookState,QueryConstants.EQUAL);
			DataObject dObj = pers.get("RemoteBooks_State", remoteBookStateCri); // NO I18N
			delDocRow = dObj.getRow("RemoteBooks_State");
			Long remoteBookId = (Long) delDocRow.get("REMOTE_BOOK_ID");

			try{
				RemoteUtils.sendCollabMsg( remoteBookId, remoteBookState, apikeyid, remoteUser, ActionConstants.DELETESESSIONAPI,"Session has been deleted");//No I18N
			}catch(Exception e){
				logger.log(Level.INFO,"Problem while sending message : " ,e); //NO I18N

			}
			pers.delete(delDocRow);
			JSONObjectWrapper responseObj = new JSONObjectWrapper();
			responseObj.put("session_delete", "true");
			response.setContentType("application/json");
			response.getWriter().print(responseObj); // NO OUTPUTENCODING


		}

	}
	private void downloadAPI(HttpServletRequest req, HttpServletResponse resp){
		try{
			WorkbookContainer container = null;

			String url = req.getRequestURL().toString();
			String[] uri = req.getRequestURI().split("/");
			String doc = uri[uri.length - 2];
			logger.info("my doc url :::" + doc);
			Map<Keys, Object> decryptContent = null;
			try {
				decryptContent = RemoteUtils.getDecryptedContent(doc);
			} catch (Exception e) {
				logger.log(Level.INFO, "[DELETE API] Decrytpion failed trying with original value");
			}
			Long remoteBookId = (Long) decryptContent.get(Keys.REMOTE_BOOK_ID);
			Long bookStateId = (Long) decryptContent.get(Keys.REMOTE_BOOK_STATE_ID);
			String userName = Constants.REMOTE_USERS[(int) decryptContent.get(Keys.SPACECOUNTER)];
			Long documentId = RemoteUtils.getRemoteBookDocumentId(remoteBookId,userName);
			container = new WorkbookContainer(documentId,userName);

			StringBuffer parameters = new StringBuffer();
			parameters.append("&proxyURL=").append("remoteautosave");
			parameters.append("&doc=").append(doc);
			parameters.append("&iscsignature=").append(SecurityUtil.sign());



			Row row = RemoteUtils.fetchRemoteBookStateRow(remoteBookId, bookStateId, userName);
			if (row != null) {
				String serverUrl = ClientUtils.getServerURL(req, false, null, false, true);
				int mode = (int) row.get("MODE");
				String requrl;
				if (mode == 2) {
					requrl = "https://" + serverUrl + "officeapi/v1/" + remoteBookId;//No I18N

				} else {
					requrl = "https://" + serverUrl + "officeapi/v1/" + doc;//No I18N
				}
				byte[] response = ContainerSynchronizer.postURLConnection(requrl, remoteBookId.toString(), parameters.toString(), null, AccessType.REMOTE, false, 30000);

			}

			String format = req.getParameter("format");
			String docName = container.getDocName();
			if (docName == null || "".equals(docName)) {
				try {
					docName = LocaleMsg.getMsg("Untitled");
				} catch (Exception le) {
					docName = "Untitled_Spreadsheet";// No I18N
				}
			}
			Exporter exporter = new Exporter(container, format);
			exporter.allowSave(true);
			byte[] content = null;
			try {

				boolean isEnabled = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("enableHeadlessPDF"));  // No I18N
				if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_ZIP)) {
					content = exporter.getDocumentZipBytes(req);
				} else if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_PDF) && isEnabled) {
					content = exporter.getDocumentPDFBytes(req);
				} else if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_CSV) || format.equalsIgnoreCase(EngineConstants.FILEEXTN_TSV)){
					content = exporter.getDocumentCsvTsvBytes();
				} else if (format.equalsIgnoreCase(EngineConstants.FILEEXTN_XLSX)){
					content = exporter.getXLSXBytes();
				} else if(format.endsWith("nzsheet")){//No I18N
					content = ImportExportUtil.getZohoSheet(container, null, null);
				} else {
					File tempFile = null;
					try {
						tempFile = EngineUtils1.createTempODSFile(container, null, false, null, (format == null || format.endsWith("ods")));//No I18N
						exporter.setTempFile(tempFile);
					} catch (Exception e) {
						logger.log(Level.INFO, "[CONVERSION:EXPORT] ExportDocumentAction tempFile creation error:" + remoteBookId, e);
					}
					content = exporter.getDocumentBytes();
					if (tempFile != null) {
						tempFile.delete();
					}
				}
			} catch (Exception e) {

				logger.log(Level.INFO, "[CONVERSION:EXPORT][Exception] ExportDocumentAction couldn't get the content from Exporter:" + remoteBookId, e);
				resp.setContentType("text/plain");
				resp.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

			}
			String mimeType = null;
			if (format.equals("xlsx")) {
				mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";// No I18N
			} else if (format.equals("xls")) {
				mimeType = "application/vnd.ms-excel";// No I18N
			} else if (format.equals("csv")) {
				mimeType = "text/csv";// No I18N
			} else if (format.equals("tsv")) {
				mimeType = "text/tsv";// No I18N
			} else if (format.equals("ods")) {
				mimeType = "application/vnd.oasis.opendocument.spreadsheet";// No I18N
			} else if (format.equals("pdf")) {
				mimeType = "application/pdf";// No I18N
			} else if (format.equals("html")) {
				mimeType = "text/html";// No I18N
			}

			resp.setContentType(mimeType);
			OutputStream out = resp.getOutputStream();
			out.write(content); //NO OUTPUTENCODING
			out.flush();
		} catch (Exception e) {
			logger.log(Level.INFO, "[CONVERSION:EXPORT] ExportDocumentAction tempFile creation error:" , e);// TODO: handle exception
		}



	}
	private byte[] downloadAPI1(HttpServletRequest req, HttpServletResponse resp){
		try{
			String url = req.getRequestURL().toString();
			String format = req.getParameter("format");
			logger.info("my test url :::" + url);
			String[] uri = req.getRequestURI().split("/");
			String doc = uri[uri.length - 2];
			logger.info("my doc url :::" + doc);
			Map<Keys, Object> decryptContent = null;
			try {
				decryptContent = RemoteUtils.getDecryptedContent(doc);
			} catch (Exception e) {
				logger.log(Level.INFO, "[DELETE API] Decrytpion failed trying with original value");
			}
			Long remoteBookId = (Long) decryptContent.get(Keys.REMOTE_BOOK_ID);
			Long bookStateId = (Long) decryptContent.get(Keys.REMOTE_BOOK_STATE_ID);
			String userName = Constants.REMOTE_USERS[(int) decryptContent.get(Keys.SPACECOUNTER)];
//				String parameters = "?doc="+doc+"&proxyURL=export&ftype=.xlsx&action=270";
			StringBuffer parameters = new StringBuffer();
			parameters.append("&proxyURL=").append("internalexport");
			parameters.append("&ftype=").append("."+format);
			parameters.append("&action=").append("270");
			parameters.append("&doc=").append(doc);
			parameters.append("&responseType=").append("base64String");
			parameters.append("&iscsignature=").append(SecurityUtil.sign());



			Row row = RemoteUtils.fetchRemoteBookStateRow(remoteBookId, bookStateId, userName);
			if (row != null) {
				String serverUrl = ClientUtils.getServerURL(req, false, null, false, true);
				int mode = (int) row.get("MODE");
				String requrl;
				if(mode ==2){
					requrl ="https://"+serverUrl + "officeapi/v1/"+remoteBookId;//No I18N

				}else{
					requrl = "https://" +serverUrl+"officeapi/v1/"+doc;//No I18N
				}
				byte[] response = ContainerSynchronizer.postURLConnection(requrl, remoteBookId.toString(), parameters.toString(), null, AccessType.REMOTE, false, 30000);
//						File f = new File("/Users/<USER>/Official/Build/test.xls");
//
//						FileOutputStream os = new FileOutputStream(f);
//						os.write(response);
//						os.close();

				String mimeType = null;
				if (format.equals("xlsx")) {
					mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";// No I18N
				} else if (format.equals("xls")) {
					mimeType = "application/vnd.ms-excel";// No I18N
				} else if (format.equals("csv")) {
					mimeType = "text/csv";// No I18N
				} else if (format.equals("tsv")) {
					mimeType = "text/tsv";// No I18N
				} else if (format.equals("ods")) {
					mimeType = "application/vnd.oasis.opendocument.spreadsheet";// No I18N
				} else if (format.equals("pdf")) {
					mimeType = "application/pdf";// No I18N
				} else if (format.equals("html")) {
					mimeType = "text/html";// No I18N
				}
				resp.setContentType(mimeType);

				String fileName = "test" + "." + format;// No I18N
				resp.setCharacterEncoding("UTF-8");	// No I18N

				resp.getWriter().print(Base64.getEncoder().encodeToString(response)); //NO OUTPUTENCODING
//						} else {
//							OutputStream out = response.getOutputStream();
//							out.write(content); //NO OUTPUTENCODING
//							out.flush();
//						}
			}
		} catch (Exception e) {
			logger.log(Level.INFO, "[DELETE API] Decrytpion failed trying with original value");
		}
		return null;
	}
	private  void deleteAPI(HttpServletRequest request, HttpServletResponse response) throws Exception {


		Long remoteBookId;

		String[] uri = request.getRequestURI().split("/");
		String userDocId = uri[uri.length -1];
		Map<Keys, Object> decryptContent = null;
		try{
			decryptContent = RemoteUtils.getDecryptedContent(userDocId);
		}catch(Exception e){
			logger.log(Level.INFO, "[DELETE API] Decrytpion failed trying with original value");
		}

		String apikeyid = (String) request.getAttribute("apikeyid");//No I18N;
		Criteria userDocIdCri;
		Criteria remoteDocIdCri;
		Criteria resCri = null;
		Criteria apiKeyCri = new Criteria(new Column("RemoteBooks","API_KEY_ID"),apikeyid,QueryConstants.EQUAL);
		if(decryptContent != null){

			remoteBookId = (Long) decryptContent.get(RemoteUtils.Keys.REMOTE_BOOK_ID);
			remoteDocIdCri =new Criteria(new Column("RemoteBooks","REMOTE_BOOK_ID"),remoteBookId,QueryConstants.EQUAL);
			resCri = apiKeyCri.and(remoteDocIdCri);
			// resCri = remoteDocIdCri;
		}else{
			userDocIdCri = new Criteria(new Column("RemoteBooks","USER_DOC_ID"),userDocId,QueryConstants.EQUAL);
			resCri = apiKeyCri.and(userDocIdCri);
			//resCri =userDocIdCri;
		}

		Persistence pers ;
		DataObject dObj = null;
		for(String remoteUser:Constants.REMOTE_USERS){
			try{
				pers = SheetPersistenceUtils.getPersistence(remoteUser);
				SelectQueryImpl sql = new SelectQueryImpl(new Table("RemoteBooks"));
				sql.addSelectColumn(new Column(null, "*"));
				sql.setCriteria(resCri);
				dObj = pers.get(sql);

				//dObj = pers.get("RemoteBooks",resCri);

				//    Iteratdobj.getRows(this.conversionServersTableName)
				if(!dObj.isEmpty()) {
					Iterator itr = dObj.getRows("RemoteBooks");
					Row row = (Row) itr.next();
					Long docId = (Long) row.get("DOCUMENT_ID");
					remoteBookId = (Long) row.get("REMOTE_BOOK_ID");
					RemoteUtils.sendCollabMsg( remoteBookId, null, apikeyid, remoteUser, ActionConstants.DELETEAPI,"Spreadsheet has been deleted");//No I18N
					RemoteUtils.removeRemoteDoc(docId,remoteBookId,remoteUser);
					JSONObjectWrapper responseObj = new JSONObjectWrapper();
					responseObj.put("doc_delete", "true");
					response.setContentType("application/json");
					response.getWriter().print(responseObj); // NO OUTPUTENCODING

					break;

				}else {
					continue;
				}
			}catch(Exception e){
				continue;

			}
		}



	}


	public static Map<Keys, Number> decryptContent(String remoteBookTxt) throws Exception
	{
		try {
			String decrypttxt = null;


			try{
				String refreshToken = EnginePropertyUtil.getSheetPropertyValue("EAR_REFRESH_TOKEN");	//No I18N
				decrypttxt=EncryptAgent.getInstance().decrypt("zohosheet_secret", remoteBookTxt.trim(), refreshToken, false); //No I18N
			}catch (Exception ear) {

				logger.log(Level.INFO, "OfficeSuiteURLMapper Ear Decryption Failed, Resolve this ", ear.getMessage());
				//return null;
				//decrypttxt  =   com.adventnet.iam.CryptoUtil.decrypt("zohosheet_secret", remoteBookTxt.trim()); //No I18N
			}



			String[] values         = decrypttxt.split("_");
			Map<Keys, Number> remoteIds = new HashMap<Keys, Number>();
			remoteIds.put(Keys.SPACECOUNTER, 5);
			for (int i = 0, len =   values.length; i < len; i++){
				switch(i){

					case 0 :
						if(!values[1].equals("")){
							remoteIds.put(Keys.REMOTE_BOOK_STATE_ID, Long.valueOf(values[0]));
						}
						break;

					case 1 :
						remoteIds.put(Keys.SPACECOUNTER, Integer.parseInt(values[1]));
						break;
				}
			}
			return remoteIds;
		}
		catch (Exception e) {
			logger.log(Level.WARNING,"Problem while decrypting the text: " + remoteBookTxt,e); //NO I18N
			return null;
		}
	}

}
