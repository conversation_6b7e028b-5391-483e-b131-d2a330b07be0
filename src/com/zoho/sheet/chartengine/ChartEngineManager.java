package com.zoho.sheet.chartengine;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.zoho.sheet.knitcharts.chartcache.ChartCacheInterceptor;
import com.zoho.sheet.knitcharts.utils.CollectionsUtils;

import java.util.*;

/**
 * Chart Engine Manager
 * <AUTHOR>
 */
public class ChartEngineManager {

    private static final Set<String> CSEZ_ACCOUTS;
    private static final Set<String> LOCAL_ZOHO_ACCOUTS;

    static {
        CSEZ_ACCOUTS = CollectionsUtils.setOf(
                "32417852",             // <PERSON><PERSON>
                "16181652"              // Harihara Sudhan
        );
        LOCAL_ZOHO_ACCOUTS = CollectionsUtils.setOf(
                "105244273",            // Sanjana Priya
                "105018761",            // Santhosh J
                "76226596"             // Harihara Sudhan
        );

    }

    public static ChartEngineVersion getChartEngineVersion(String docCreatorZUID) {

        if(isThirdGenChartsEnabledForZUID(docCreatorZUID) || isThirdGenChartsEnabledForZUIDOrg(docCreatorZUID)
                || isDevModeThirdGenChartsEnabledForZUID(docCreatorZUID)) {
            return ChartEngineVersion.THIRD_VERSION;
        }
        return ChartEngineVersion.SECOND_VERSION;
    }

    private static boolean isThirdGenChartsEnabledForZUID(String docCreatorZUID) {
        try {
            List<String> enabledZUIDs = ChartCacheInterceptor.getThirdGenChartsEnabledOrgs();

            return enabledZUIDs.contains(docCreatorZUID);
        } catch (Exception e) {
            return false;
        }
    }

    private static boolean isDevModeThirdGenChartsEnabledForZUID(String docCreatorZUID) {
        if(EngineConstants.NEW_CHARTS_DEV_ENABLED) {
            return LOCAL_ZOHO_ACCOUTS.contains(docCreatorZUID) ||
                    CSEZ_ACCOUTS.contains(docCreatorZUID);
        }
        return false;
    }

    private static boolean isThirdGenChartsEnabledForZUIDOrg(String docCreatorZUID) {
        try {
            String orgId = Objects.toString(DocumentUtils.getZOIDFromZuid(docCreatorZUID));
            List<String> enabledOrgs = ChartCacheInterceptor.getThirdGenChartsEnabledOrgs();

            return enabledOrgs.contains(orgId);
        } catch (Exception e) {
            return false;
        }
    }

    public static ChartEngine getChartEngine(ChartEngineVersion version) {

        if(version == ChartEngineVersion.THIRD_VERSION) {
            return new ThirdGenChartEngine();
        }

        return new SecondGenChartEngine();
    }

    public static ChartEngine getChartEngine(WorkbookContainer container) {

        return getChartEngine(getChartEngineVersion(container));
    }

    public static ChartEngine getChartEngine(Workbook workbook) {
        return getChartEngine(workbook.getChartEngineVersion());
    }

    public static void setChartEngineVersion(WorkbookContainer container, Workbook workbook) {
        workbook.setChartEngineVersion(getChartEngineVersion(container));
    }

    public static boolean getIsThirdGenChartEnabled(WorkbookContainer container) {
        try {
            return getChartEngineVersion(container) == ChartEngineVersion.THIRD_VERSION;
        } catch (Exception e) {
            return false;
        }
    }

    public static ChartEngineVersion getChartEngineVersion(WorkbookContainer container) {
        try {
            String id = container.getCreatorZUID();

            if (id != null && !id.isEmpty()) {
                return ChartEngineManager.getChartEngineVersion(id);
            }
        } catch (Exception ignored) {}

        return ChartEngineVersion.SECOND_VERSION;
    }

}
