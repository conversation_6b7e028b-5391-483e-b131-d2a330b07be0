//$Id$
package com.zoho.sheet.filter;

//Java Imports
import com.adventnet.iam.DummyEntityResolver;
import com.adventnet.iam.security.SecurityUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;

import java.io.File;
import java.util.Map;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;


import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.FilterConfig;
import javax.xml.parsers.DocumentBuilderFactory;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

//import com.adventnet.iam.security.DummyEntityResolver;
import javax.xml.parsers.DocumentBuilder;

//Sheet Imports
//import com.adventnet.zoho.websheet.util.SheetFeaturesStatsTimerTask;
/**
 *
 * <AUTHOR>
 */
public class SheetStatsRuleSetParser
{

    private static final Logger LOGGER = Logger.getLogger(SheetStatsRuleSetParser.class.getName());
    /* All the URL's configured in the file will be here */
    private static Map<String, SheetStatsURLRule> reqURLRuleMap = new HashMap<String, SheetStatsURLRule>();
    private static Map<String, SheetStatsURLRule> resURLRuleMap = new HashMap<String, SheetStatsURLRule>();
    private static Map<Pattern, SheetStatsURLRule> reqURLPatternRuleMap = new HashMap<Pattern, SheetStatsURLRule>();
    private static Map<Pattern, SheetStatsURLRule> resURLPatternRuleMap = new HashMap<Pattern, SheetStatsURLRule>();
    public static Map<String, SheetStatsTrack> keyRuleMap = new HashMap<String, SheetStatsTrack>();

    SheetStatsRuleSetParser(FilterConfig filterConfig) throws Exception
    {
        String file = filterConfig.getInitParameter("config-file"); //No I18N
        String filePath = filterConfig.getServletContext().getRealPath("WEB-INF") + File.separator + file; //No I18N
        SheetStatsRuleSetParser.initFeaturesStatsRules(filePath);
        LOGGER.log(Level.INFO, "Total size for keyRuleMap : " + keyRuleMap.size() + " and keyRuleMap: " + keyRuleMap);

    }

    /**
     * parses the configuration file and inits all the parameter rules
     * @param fileName
     */
    protected static void initFeaturesStatsRules(String fileName) throws Exception
    {
        Document document = parseXmlFile(fileName);
        Element root = document.getDocumentElement();
        initURLRuleSet(root);
    }

    /**
     * parses every tag's names url and inits the urlrule's
     * @param root
     */
    private static void initURLRuleSet(Element root)
    {
        NodeList nodelist = root.getElementsByTagName("url");
        for (int i = 0; i < nodelist.getLength(); i++)
        {
            Node node = nodelist.item(i);

            if (node.getNodeType() == node.ELEMENT_NODE)
            {
                Element element = (Element) nodelist.item(i);
                // parses the element and inits the urlrule and adds it in a rule set
                addURLRule(element);
            }
        }
    }

    /**
     *
     * @param fileName
     * @return
     */
    private static Document parseXmlFile(String fileName) throws Exception
    {
        Document document = null;
//        DocumentBuilderFactory documnetBuilder = null;
//        documnetBuilder = DocumentBuilderFactory.newInstance();
//	DocumentBuilder docBuilder = documnetBuilder.newDocumentBuilder(); 
	DocumentBuilder docBuilder = SecurityUtil.getDocumentBuilder();
	docBuilder.setEntityResolver(new DummyEntityResolver());
	document = docBuilder.parse(new File(fileName)); 
	return document;
    }

    private static void addURLRule(Element element)
    {

        SheetStatsTrack sTrack = null;
        String url = element.getAttribute("path");
        String type = element.getAttribute("type");
        String pathReg = element.getAttribute("path-regex");
        boolean pathRegex = false;


        SheetStatsURLRule uRule = new SheetStatsURLRule(url, type);

//----------------------------INVOKE TRACKS

        NodeList invokeNodes = element.getElementsByTagName("invokeTrack");
        Set<SheetStatsTrack> invokeTracksSet = null;
        if (invokeNodes.getLength() > 0)
        {
            Node invokeNode = invokeNodes.item(0);
            if (invokeNode.getNodeType() == invokeNode.ELEMENT_NODE)
            {
                invokeTracksSet = new HashSet<SheetStatsTrack>();
                String[] dbKeys = invokeNode.getTextContent().split("\\,");
                for (int i = 0; i < dbKeys.length; i++)
                {
                    sTrack = getSSTrack(dbKeys[i]);
                    invokeTracksSet.add(sTrack);
                }
                uRule.setInvokeTracksSet(invokeTracksSet);
            }
        }

//-----------------------------CONDITIONAL TRACKS

        NodeList conditionalNodes = element.getElementsByTagName("conditionalTrack");
        Table<Integer, String, Object> conditionalTracksTable = HashBasedTable.create();
        int rowNo_1 = 1;
        for (int j = 0; j < conditionalNodes.getLength(); j++)
        {
            Node conditionalNode = conditionalNodes.item(j);
            if (conditionalNode.getNodeType() == conditionalNode.ELEMENT_NODE)
            {

                String dbKey = conditionalNode.getTextContent();
                sTrack = getSSTrack(dbKey);
                conditionalTracksTable.put(rowNo_1, "sheetStatsTrack", sTrack);
                int keyNo = 1;
                for (int k = 0; k < conditionalNode.getAttributes().getLength(); k++)
                {
                    Node attribute = conditionalNode.getAttributes().item(k);

                    conditionalTracksTable.put(rowNo_1,"key" + keyNo, attribute.getNodeName());
                    conditionalTracksTable.put(rowNo_1,"value" + keyNo, attribute.getNodeValue());
                    keyNo++;
                }
            }
            rowNo_1++;

        }
        if (!conditionalTracksTable.isEmpty())
        {
            uRule.setConditionalTracksTable(conditionalTracksTable);
        }

//-------------------------------CONDITIONAL GROUPING TRACK

        Set<Table<Integer, String, Object>> conditionalGroupingTracksSet = new HashSet<Table<Integer, String, Object>>();
        NodeList conditionalGroupingNodes = element.getElementsByTagName("conditionalGroupingTrack");

        for (int i = 0; i < conditionalGroupingNodes.getLength(); i++)
        {
            int rowNo_2 = 1;
            Table<Integer, String, Object> conditionalGroupingTable = HashBasedTable.create();
            Node groupingConditionNode = conditionalGroupingNodes.item(i);
            if (groupingConditionNode.getNodeType() == groupingConditionNode.ELEMENT_NODE)
            {
                Node conditionNode = groupingConditionNode.getAttributes().item(0);
                String conditionKey = conditionNode.getNodeName();
                String conditionValue = conditionNode.getNodeValue();
                conditionalGroupingTable.put(rowNo_2, "conditionKey",conditionKey);
                conditionalGroupingTable.put(rowNo_2, "conditionValue", conditionValue);

                NodeList groupingNodeList = ((Element) groupingConditionNode).getElementsByTagName("groupingKey");
                for (int j = 0; j < groupingNodeList.getLength(); j++)
                {
                    Node groupingNode = groupingNodeList.item(j);
                    if (groupingNode.getNodeType() == groupingNode.ELEMENT_NODE)
                    {
                        String groupingKey = groupingNode.getAttributes().getNamedItem("key").getNodeValue();   //No I18N
                        conditionalGroupingTable.put(rowNo_2, "groupingKey", groupingKey);

                        NodeList groupingRangeNodeList = ((Element) groupingNode).getElementsByTagName("groupingRange");
                        for (int k = 0; k < groupingRangeNodeList.getLength(); k++)
                        {
                            Node groupingRangeNode = groupingRangeNodeList.item(k);
                            if (groupingRangeNode.getNodeType() == groupingNode.ELEMENT_NODE)
                            {
                                String groupingValue = groupingRangeNode.getAttributes().getNamedItem("value").getNodeValue();  //No I18N
                                conditionalGroupingTable.put(rowNo_2, "groupingValue", groupingValue);

                                String dbKey = groupingRangeNode.getTextContent();
                                sTrack = getSSTrack(dbKey);
                                conditionalGroupingTable.put(rowNo_2, "sheetStatsTrack", sTrack);

                                rowNo_2++;
                            }
                        }
                    }
                }
            }
            conditionalGroupingTracksSet.add(conditionalGroupingTable);
        }
        if(!conditionalGroupingTracksSet.isEmpty())
        {
            uRule.setConditionalGroupingTracksSet(conditionalGroupingTracksSet);
        }

        if (url == null || "".equals(url))
        {
            throw new RuntimeException("Attribute PATH not specifed in the URL element : " + element); //No I18N
        }
        if (type == null || "".equals(type))
        {
            throw new RuntimeException("Attribute TRACK not specifed in the URL element : " + element); //No I18N
        }
        if (pathReg != null || "true".equals(pathReg))
        {
            pathRegex = Boolean.valueOf(pathReg);
        }

        // checks whether the rule for this url is already added or not
        // SheetFeaturesURLRule urlRule = getURLRule(url);


        /*stopped to throw RuntimeException for already exist path check,
        because  filter is called for each context path, it cause problem  for context path(/sheet),
        since filter is already done for default context path.
         */

        if ("request".equals(type) && !reqURLRuleMap.containsKey(url))  //No I18N
        {
            LOGGER.info("adding URL request type>> " + url);		//No I18N

            if (pathRegex)
            {
                reqURLPatternRuleMap.put(Pattern.compile(url), uRule);
            } else
            {
                reqURLRuleMap.put(url, uRule);
            }


        } else if ("response".equals(type) && !resURLRuleMap.containsKey(url))  //No I18N
        {
            LOGGER.info("adding URL  response type   >> " + url);			//No I18N

            if (pathRegex)
            {
                resURLPatternRuleMap.put(Pattern.compile(url), uRule);
            } else
            {
                resURLRuleMap.put(url, uRule);
            }

        }
        
    }


    private static SheetStatsTrack getSSTrack(String key)
    {
        SheetStatsTrack sTrack = keyRuleMap.get(key.trim());
        if (sTrack != null)
        {
            return sTrack;
        } else
        {
            sTrack = new SheetStatsTrack(key);
            keyRuleMap.put(key, sTrack);
        }
        return sTrack;
    }

    protected SheetStatsURLRule getURLRule(String url, boolean isRequest)
    {
        SheetStatsURLRule sfurlrule = null;
        if (isRequest)
        {
            sfurlrule = reqURLRuleMap.get(url);
        } else
        {
            sfurlrule = resURLRuleMap.get(url);
        }
        if (sfurlrule != null)
        {
            return sfurlrule;
        }
        if (isRequest)
        {
            for (Map.Entry<Pattern, SheetStatsURLRule> entrySet : reqURLPatternRuleMap.entrySet())
            {
                Pattern urlPattern = entrySet.getKey();
                if (urlPattern.pattern().equals(url) || urlPattern.matcher(url).matches())
                {
                    return entrySet.getValue();
                }
            }
        } else
        {
            for (Map.Entry<Pattern, SheetStatsURLRule> entrySet : resURLPatternRuleMap.entrySet())
            {
                Pattern urlPattern = entrySet.getKey();
                if (urlPattern.pattern().equals(url) || urlPattern.matcher(url).matches())
                {
                    return entrySet.getValue();
                }
            }
        }
        return null;
    }
}
