/**
 * $Id$
 **/

/**
 * Used for context based redirection
 */

package com.zoho.sheet.filter;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.Join;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQuery;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.Table;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.Encrypter;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.zfsng.client.ZohoFS;
import java.io.IOException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class ContextURLRedirection implements Filter 
{
    private static final Logger LOGGER = Logger.getLogger(ContextURLRedirection.class.getName()); 
    public String serverName    = null;
    public String externalServer = null;
    public String hostName = null;
    public ServletContext context = null;
    private FilterConfig config = null;
    private boolean isRedirectionRequired = true;
    private boolean isURLRedirectionRequired = true;
    private String fromDomain = null;
    private String toDomain = null;
    public void init(FilterConfig fc) throws ServletException
    {       
        this.config = fc;
        this.fromDomain = fc.getInitParameter("fromDomain");  //NO I18N
        this.toDomain = EnginePropertyUtil.getSheetPropertyValue("ZohoDocsURL")+"/sheet"; //NO I18N
        this.isRedirectionRequired= Boolean.valueOf(fc.getInitParameter("isRedirectionRequired")); //NO I18N
    }   
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain fc) throws ServletException, IOException
    {
        if(isRedirectionRequired)
        {
                        HttpServletRequest req = (HttpServletRequest) request;
            HttpServletResponse res = (HttpServletResponse) response;
            String docId = null;
            String resourceId = null;
            String newurl = request.getScheme() +"://"+ EnginePropertyUtil.getSheetPropertyValue("ZohoDocsURL");
            String url = (new URL((req.getRequestURL()).toString())).toString();
            if(req.getRequestURI().contains("signup"))
            {
                newurl = Constants.rebrand_Properties.getProperty("SignInUpUrl");
            
                String queryString = req.getQueryString();
                if( queryString != null && !queryString.isEmpty() ){                
                    newurl = newurl + "?" + queryString;
                }
                ClientUtils.redirectResponse(res, newurl);
                return;
            }
            if(req.getRequestURI().contains("/keyboard-shortcuts")){ //No I18N
                            String shortcuturl = Constants.rebrand_Properties.getProperty("keyboardShortCutkeyforSheet");
                            if(req.getRequestURI().contains("/keyboard-shortcuts-ja")){
                                 shortcuturl = Constants.rebrand_Properties.getProperty("keyboardShortCutkeyforSheetforjapaneese");
                            }
                            ClientUtils.redirectResponse(res, shortcuturl);
                            return;
                        }
                        if(req.getRequestURI().contains("/sheet/ropen")){
                            String rid = req.getParameter("rid");
	                        String URL = request.getScheme() +"://"+toDomain+"/open/"+rid; 
	                        res.sendRedirect(URL);
	                        return;
                        }
//                        if(req.getRequestURI().contains("/sheet/open/")){
//                            if(IAMUtil.getCurrentUser() != null){
//                                if(ClientUtils.isNewClientUser()){
//                                    String URI = req.getRequestURI().replace("/sheet/open", "/hopen");
//                                    String URL = request.getScheme() +"://"+toDomain+URI;
//                                    res.sendRedirect(URL);
//                                    return;
//                                }
//                                else {
//                                    fc.doFilter(request, response);
//                                    return;
//                                }
//                            } else {
//                               fc.doFilter(request, response);
//                               return;
//                            }
//                        }
            if(req.getRequestURI().contains("/publi") || req.getRequestURI().contains("/corporate/") || req.getRequestURI().contains("/org/") ){    //No I18N
                        String fid = req.getParameter("fid");
                        String mode =  req.getParameter("mode");
                        if(fid == null) {
                            fid = req.getParameter("doc_id");
                        }
                        if(fid != null) {
                            try{
                                Long tempfid = Long.parseLong(fid);
                                SelectQuery sql =new SelectQueryImpl(new Table("PublicDocuments"));
                                sql.addSelectColumn(new Column(null,"*"));
                                sql.addJoin(new Join("PublicDocuments","OldDocIdNewIdMapping",new String[]{"DOCUMENT_ID"},new String[]{"DOC_ID"},Join.INNER_JOIN));
                                sql.setCriteria(new Criteria(new Column("OldDocIdNewIdMapping","OLD_DOC_ID"),tempfid,QueryConstants.EQUAL));
                                DataObject dobj = (SheetPersistenceUtils.getPersistence("Public")).get(sql);   //No I18N
                                Row publicdocinfo = dobj.getRow("PublicDocuments");
                                if(publicdocinfo != null) {
                                    String documenturl = (String)publicdocinfo.get("DOCUMENT_NAME_URL");
                                    String authorname = (String)publicdocinfo.get("AUTHOR_NAME");
                                    String resourceId1 = ZohoFS.getResourceIdOfPublicNiceUrl("2/"+authorname+"/"+documenturl);    //No I18N
                                    if(resourceId1 == null){
                                         Long zoid   = DocumentUtils.getZOID(authorname);
                                        if(zoid == -1){
                                            req.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(req, res);   //No I18N
                                            return;
                                        }
                                        else{
                                        resourceId1 = ZohoFS.getResourceIdOfCorporateNiceUrl(zoid.toString(), "2/"+authorname+"/"+documenturl);
                                        }
                                    }
                                    if(resourceId1 == null){
                                        req.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(req, res);   //No I18N
                                        return; 
                                    }
                                    String newPublicURL = null;
                                    if(mode == null){
                                        newPublicURL = request.getScheme() +"://"+toDomain+"/published.do?rid="+resourceId1;  //No I18N
                                    }
                                    else {
                                         newPublicURL = request.getScheme() +"://"+toDomain+"/published.do?rid="+resourceId1+"&mode="+mode;   //No I18N
                                    }
                                    res.sendRedirect(newPublicURL);
                                    return;
                                }
                            }catch (Exception e) {
                                LOGGER.info("Problem in fetching old Public URL.. "+e.getMessage());    //No I18N
                            }
                        }
                            else {
                                    String URI = null;
                                    String resourceId1 = null;
                                    if(req.getRequestURI().contains("/public/")){
                                        URI = req.getRequestURI().substring(req.getRequestURI().indexOf("public"));
                                        String urlconst[] = URI.split("/");
                                    
                                        if(urlconst.length == 3 && (urlconst[0].equals("public"))){
                                            try {
                                                 resourceId1 = ZohoFS.getResourceIdOfPublicNiceUrl("2/" + urlconst[1] + "/" + URLDecoder.decode(urlconst[2])); //No I18N
                                            } catch (Exception ex) {
                                                Logger.getLogger(ContextURLRedirection.class.getName()).log(Level.SEVERE, null, ex);
                                            }
                                        }
                                    }
                                    else if(req.getRequestURI().contains("/corporate/") || req.getRequestURI().contains("/org/"))
                                    {
                                        if(req.getRequestURI().contains("/corporate/")){
                                            URI = req.getRequestURI().substring(req.getRequestURI().indexOf("corporate"));
                                        }
                                        else{
                                            URI = req.getRequestURI().substring(req.getRequestURI().indexOf("org"));
                                        }
                                        String urlconst1[] = URI.split("/");
                                        String userName =urlconst1[2];
                                        Long zoid   = DocumentUtils.getZOID(userName);
                                        if(zoid == -1){
                                            req.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(req, res);   //No I18N
                                            return; 
                                        }
                                        else{
                                            try {
                                                if(urlconst1.length == 4){
                                                    resourceId1 = ZohoFS.getResourceIdOfCorporateNiceUrl(zoid.toString(), "2/"+urlconst1[2]+"/"+URLDecoder.decode(urlconst1[3]));
                                                    if(req.getUserPrincipal() == null){
                                                    String newPublicURL = null;
                                                    if(resourceId1 != null){
                                                    if(mode == null){
                                                        newPublicURL = request.getScheme() +"://"+toDomain+"/published.do?rid="+resourceId1;   //No I18N
                                                    }
                                                    else {
                                                        newPublicURL = request.getScheme() +"://"+toDomain+"/published.do?rid="+resourceId1+"&mode="+mode;   //No I18N
                                                    }
                                                    String loginPageRedirectionURL = Constants.rebrand_Properties.getProperty("loginPageRedirectionURL");
                                                    newPublicURL = loginPageRedirectionURL+"#serviceurl="+newPublicURL;   //No I18N
                                                    ClientUtils.redirectResponse(res, newPublicURL);
                                                    return; 
                                                   
                                                    }
        
                                                }
        
                                                }
                                                } catch (Exception ex) {
                                                    Logger.getLogger(ContextURLRedirection.class.getName()).log(Level.SEVERE, null, ex);
                                                }
        
                                        }
                                    }
                                    else if(req.getRequestURI().contains("public.do")){
                                        Encrypter enc = new Encrypter("DES","123456789012345678901234567890");             //No I18N
                                        String niceURL = "2/"+enc.decrypt(request.getParameter("name"))+"/"+request.getParameter("docurl");  //No I18N
                                        try {
                                                 resourceId1 = ZohoFS.getResourceIdOfPublicNiceUrl(niceURL); //No I18N
                                                 if(resourceId1 == null){
                                                     Long zuid   = DocumentUtils.getZOID(request.getParameter("name"));
                                                    if(zuid == -1){
                                                        req.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(req, res);   //No I18N
                                                        return; 
                                                    }
                                                    else{
                                                    resourceId1 = ZohoFS.getResourceIdOfCorporateNiceUrl(zuid.toString(), niceURL);    //No I18N
                                                    }
                                                }
        
                                            } catch (Exception ex) {
                                                Logger.getLogger(ContextURLRedirection.class.getName()).log(Level.SEVERE, null, ex);
                                            }
        
        
                                    }
                                    else if(req.getRequestURI().contains("publish.do")){
                                         Encrypter enc = new Encrypter("DES","123456789012345678901234567890");             //No I18N
                                         String authorName = enc.decrypt(URLDecoder.decode(request.getParameter("name")));
                                         String docNameURL = URLDecoder.decode(request.getParameter("docurl"));
                                         String niceURL = "2/"+authorName+"/"+docNameURL;
                                         try {
                                                 resourceId1 = ZohoFS.getResourceIdOfPublicNiceUrl(niceURL); //No I18N
                                                 if(resourceId1 == null){
                                                     Long zuid   = DocumentUtils.getZOID(authorName);
                                                    if(zuid == -1){
                                                        req.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(req, res);   //No I18N
                                                        return;
                                                    }
                                                    else{
                                                    resourceId1 = ZohoFS.getResourceIdOfCorporateNiceUrl(zuid.toString(), niceURL);    //No I18N
                                                    }
                                                }
        
                                            } catch (Exception ex) {
                                                Logger.getLogger(ContextURLRedirection.class.getName()).log(Level.SEVERE, null, ex);
                                            }
        
                                     }
                                    else if(req.getRequestURI().contains("/publish/")){
                                        mode = "embed";    //No I18N
                                        URI = req.getRequestURI().substring(req.getRequestURI().indexOf("publish"));
                                        String urlconst[] = URI.split("/");
                                        if(urlconst.length == 3 && (urlconst[0].equals("publish"))){
                                            try {
                                                 resourceId1 = ZohoFS.getResourceIdOfPublicNiceUrl("2/" + urlconst[1] + "/" + URLDecoder.decode(urlconst[2])); //No I18N
                                                 if(resourceId1 == null){
                                                     Long zoid   = DocumentUtils.getZOID(request.getParameter("name"));
                                                     if(zoid == -1){
                                                        req.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(req, res);   //No I18N
                                                        return;
                                                    }
                                                    else{
                                                    resourceId1 = ZohoFS.getResourceIdOfCorporateNiceUrl(zoid.toString(), "2/" + urlconst[1] + "/" + URLDecoder.decode(urlconst[2]));    //No I18N
                                                    }
                                
                                                 }
                                                 if(resourceId1 == null){
                                                    req.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(req, res);   //No I18N
                                                    return; 
                                                }
                                                    String newPublicURL = request.getScheme() +"://"+toDomain+"/published/"+resourceId1+"?mode="+mode;   //No I18N
                                                    ClientUtils.redirectResponse(res, newPublicURL);
                                                    return;
                                            } catch (Exception ex) {
                                                Logger.getLogger(ContextURLRedirection.class.getName()).log(Level.SEVERE, null, ex);
                                            }
                                        }
        
                                    }
                                    else{
                                        req.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(req, res);   //No I18N
                                        return; 
                                    }
                                    String newPublicURL = null;
                                    if(resourceId1 != null){
                                        if(mode == null){
                                            newPublicURL = request.getScheme() +"://"+toDomain+"/published/"+resourceId1;   //No I18N
                                        }
                                        else {
                                            newPublicURL = request.getScheme() +"://"+toDomain+"/published/"+resourceId1+"?mode="+mode;   //No I18N
                                        }
                                        res.sendRedirect(newPublicURL);
                                        return;
                                    }
                                else{
                                    req.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(req, res);   //No I18N
                                    return; 
                                }
    
                            }
                        }
                }
        fc.doFilter(request,response);
    }

    public void destroy() {

    }
}



