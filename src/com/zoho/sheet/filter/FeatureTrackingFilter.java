 /* $Id$ */
 package com.zoho.sheet.filter;

 import com.adventnet.iam.IAMUtil;
 import com.adventnet.iam.User;
 import com.adventnet.iam.security.IAMSecurityException;
 import java.io.IOException;
 import java.util.Arrays;
 import java.util.Enumeration;
 import java.util.HashSet;
 import java.util.List;
 import java.util.Set;
 import java.util.logging.Level;
 import java.util.logging.Logger;
 import javax.servlet.*;
 import javax.servlet.http.*;
//import com.zoho.conf.Configuration;
 import com.adventnet.zoho.websheet.model.util.ActionConstants;
 import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
 import com.zoho.instrument.ma.FeatureTracker;
 import com.zoho.instrument.ma.TrackerConf;
 import com.zoho.instrument.ma.TrackerData;
 import com.adventnet.iam.security.SecurityRequestWrapper;
 import com.adventnet.sas.ds.SASThreadLocal;
 import com.zoho.instrument.ma.TrackerDataStore;
 import org.json.JSONArray;

 public class FeatureTrackingFilter implements Filter {

     private static Logger logger = Logger.getLogger(FeatureTrackingFilter.class.getName());
     //    private boolean instrumentEnabled = Configuration.getBoolean("com.zoho.instrument");
     public static Level level = Level.FINE;

     public void init(FilterConfig conf)throws ServletException
     {
     }


     public void doFilter(ServletRequest requests, ServletResponse responses, FilterChain chain) throws IOException,ServletException {
         HttpServletRequest request = (HttpServletRequest) requests;
         long zuid = -1;
         long zoid = -1;
         List TRACKING_ACTION_LIST = (new JSONArray(EnginePropertyUtil.getSheetPropertyValue("TRACKING_ACTION_CONSTANT")).toList()); // No I18N
         User currUser = IAMUtil.getCurrentUser();
         if(currUser != null){
             zuid = currUser.getZUID();
             zoid = currUser.getZOID();
         }
         String urlString;
         try
         {
             SecurityRequestWrapper securityWrapper = SecurityRequestWrapper.getInstance(request);
             urlString = securityWrapper.getURLActionRule().getPath();
         }
         catch(IAMSecurityException e)
         {
             urlString = request.getRequestURL().toString();
             urlString = urlString.substring(urlString.lastIndexOf("/"));
//                   logger.log(Level.WARNING, "IAMSecurityException  ::{0}", new Object[]{e});

         }

//        logger.log(Level.WARNING, "urlString  ::{0}", new Object[]{urlString});
         try
         {
             TrackerConf conf = FeatureTracker.getTrackerConf(urlString);

//                   logger.log(Level.WARNING, "(conf==null)  ::{0}", new Object[]{(conf==null)});
             boolean b = FeatureTracker.isTrackingEnabled();
             if(conf==null || !b)
             {

//                       logger.log(Level.WARNING, "Feature tracking is not enabled : enableProperty   ::{0} ::conf {1}", new Object[]{b,conf});
                 chain.doFilter(requests,responses);
                 return;
             }

//                   logger.log(Level.WARNING, "Tracker conf is  ::{0}", new Object[]{conf.toString()});
             FeatureTracker.start(conf);
             TrackerData trackerData = FeatureTracker.getTrackerData();
             trackerData.setPaymentPlan("FREE");// NO I18N
             trackerData.setSasID(zuid); //sasID_Of_Request
             trackerData.setOrgID(zuid); //orgID_Of_Request
             trackerData.setZuid(zuid); //zuID_Of_Request
             if( ! "GET".equals(request.getMethod()))
             {
                 String _paramName = null;
                 if (request.getParameter("action") != null) {
                     _paramName = "action"; // NO I18N
                 } else if (request.getParameter("a") != null) {
                     _paramName = "a"; // NO I18N
                 }
                 if (_paramName != null && request.getParameter(_paramName) != null) {
                     if(isValidInteger(request.getParameter(_paramName))){
                         int _actionConstant = Integer.parseInt(request.getParameter(_paramName));
                         if(TRACKING_ACTION_LIST.contains(_actionConstant) ){
                             logger.log(Level.WARNING, "[MICS_FEATURE_TRACKED]actionConstant tracked ID::{0}", new Object[]{_actionConstant});
                             String _actionName = ActionConstants.getActionName(_actionConstant);
                             TrackerData _newData = new TrackerData(_actionName, _actionName);
                             _newData.setPaymentPlan("FREE");// NO I18N
                             _newData.setSasID(zuid); //sasID_Of_Request
                             _newData.setOrgID(zuid); //orgID_Of_Request
                             _newData.setZuid(zuid); //zuID_Of_Request
                             _newData.addParam("action", request.getParameter(_paramName));//No I18N
                             if(request.getParameter("ftype") != null){
                                 _newData.addParam("ftype", request.getParameter("ftype"));//No I18N
                             }
                             _newData.hit();
                             TrackerDataStore.addTrackerData(_newData);
                         }
                     }
                 }
             }else {
                 String _uri = request.getRequestURI();
                 if(_uri.contains("/sheet/open/")){
                     TrackerData _newData = new TrackerData("FILE_OPEN_REQUEST",request.getRequestURI());//No I18N
                     _newData.setPaymentPlan("FREE");// NO I18N
                     _newData.setSasID(zuid); //sasID_Of_Request
                     _newData.setOrgID(zuid); //orgID_Of_Request
                     _newData.setZuid(zuid); //zuID_Of_Request
                     _newData.hit();
                     TrackerDataStore.addTrackerData(_newData);
                 }else{
                     logger.log(Level.WARNING, "[POST TRACKING]_uri ::{0}", new Object[]{_uri});
                 }
             }
             FeatureTracker.includeDefaults(conf,request);
             chain.doFilter(requests,responses);
         }
         catch(Exception excp)
         {
             chain.doFilter(requests,responses);
             logger.log(Level.WARNING, "Exception occurred while Feature Tracking . Exception ::{0}", new Object[]{excp});
         }
         finally
         {
             try{
                 FeatureTracker.complete();
//                 logger.log(level,"Feature Tracking complete");
             }
             catch(Exception e)
             {
                 logger.log(Level.SEVERE,"Exception while completing feature tracking"+e);
             }
         }
     }
     private static boolean isValidInteger(String paramName) {
         if (paramName == null || paramName.isBlank()) {
             return false;
         }
         try {
             Integer.parseInt(paramName.trim());
             return true;
         } catch (NumberFormatException e) {
             return false;
         }
     }
     public void destroy()
     {
     }
 }
