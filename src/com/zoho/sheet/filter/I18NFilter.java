//$Id$
package com.zoho.sheet.filter;

import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import com.zoho.sheet.authorization.DocIdentityBroadCaster;

import java.util.Locale;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class I18NFilter implements Filter {

        public static final Logger LOGGER = Logger.getLogger(I18NFilter.class.getName());

        private String encoding;
        private String contentType;

        @Override
        public void init(FilterConfig filterConfig) throws ServletException {
                this.encoding = filterConfig.getInitParameter("encoding");		//No I18N
                this.contentType = filterConfig.getInitParameter("contentType");	//No I18N
        }

        @Override
        public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse,
                FilterChain chain) throws IOException, ServletException {
                if (this.encoding != null) {
                        servletRequest.setCharacterEncoding(this.encoding);
                }
                if (this.contentType != null) {
                        servletResponse.setContentType(this.contentType);
                        servletResponse.setCharacterEncoding(this.encoding);
                }

                HttpServletRequest request = (HttpServletRequest) servletRequest;
                String uri = request.getRequestURI();
                String localeStr = request.getHeader("User-Locale");
                //Locale for hopen.do, w.r.t parameter(send from native) instead of header
                if(localeStr == null || "".equals(localeStr)){
                	localeStr	=	request.getParameter("userLocale");
                }
                String proxyURL = DocumentUtils.getProxyURL(request);
                Locale locale = null;
                
                if(!"getstatus".equals(proxyURL)) {
                        
                        String value = "^.*\\.(js|css|gif|png)$";	//No I18N
                        Pattern pattern = Pattern.compile(value);
                        Matcher matcher = pattern.matcher(uri);
                        if(!matcher.matches()) {
                        
                                if(localeStr != null) {
                                        int index = localeStr.indexOf("_");
                                        if(index > 0) {
                                                locale = LocaleUtil.getLocale(localeStr.substring(0, index), localeStr.substring(index + 1, localeStr.length()));
                                        } else {
                                                locale = new Locale(localeStr);
                                        }
                                } else {
                                        locale = LocaleUtil.getDefaultLocale(request);
                                }
                        }
                }
                
//                System.out.println("[i18NFilter] Final UserLocale : -----> "+locale.toString());

                DocIdentityBroadCaster broadCaster = DocIdentityBroadCaster.getInstance();
                try {
                        broadCaster.setLocale(locale);
                        chain.doFilter(servletRequest, servletResponse);
                } finally {
                        broadCaster.resetLocale();
                }
        }

        @Override
        public void destroy() {
        }
}
