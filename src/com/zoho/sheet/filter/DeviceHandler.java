/**
 * $Id$
 * */
/**
 * Used for Device based  redirection
 */
package com.zoho.sheet.filter;

import java.io.IOException;
import java.util.logging.Logger;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.zoho.websheet.model.response.DeviceType;
import com.adventnet.zoho.websheet.model.response.ResponseObject;
import com.adventnet.zoho.websheet.model.response.v2.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.logs.common.util.logging.OnDemandSearchLogger;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;
import java.util.logging.Level;

public class DeviceHandler implements Filter {

    private static final Logger LOGGER = Logger.getLogger(DeviceHandler.class.getName());

    private String toDomain = null;

    public void init(FilterConfig fc) throws ServletException {
        this.toDomain = fc.getInitParameter("toDomain"); // NO I18N
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain fc) throws ServletException, IOException {
        String deviceDetails = ((HttpServletRequest) request).getHeader("User-Agent");
        String MobileDocsURL = (String) EnginePropertyUtil.getSheetPropertyValue("MobileDocsURL");// No I18N
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;
        String url = req.getRequestURI();
        String method = req.getMethod();
        String customDomain = null;

        /*
        *  Benefits of making new url for handheld devie, there will not be any redirection.
         */
        // for Response Versioning
        String versionNumber = request.getParameter(JSONConstants.RESPONSE_VERSION_NUMBER);
        ResponseObject.ResponseVersion responseVersion;
        if(versionNumber == null) {
            DeviceType deviceType = deviceDetails != null ? DeviceType.getDeviceType(deviceDetails) : null;
            responseVersion = deviceType != null ? (deviceType.equals(DeviceType.WEB) && !method.equals("GET") ? EngineConstants.DEFAULT_WEB_CLIENT_RESPONSE_VERSION
                    : deviceType.getResponseVersion()) : EngineConstants.DEFAULT_RESPONSE_VERSION;
            OnDemandSearchLogger.log(LOGGER, Level.INFO,"[DEVICE HANDLER][RESPONSE VERSION]Response Version Number is not passed.");
            OnDemandSearchLogger.log(LOGGER, Level.INFO, "[DEVICE HANDLER][RESPONSE VERSION]Response Version Number {0} is used which is decided based on user-agent{1}", new Object[]{responseVersion.getVersion(), deviceDetails});
        }
        else {
            responseVersion = ResponseObject.ResponseVersion.getResponseVersion(Double.parseDouble(versionNumber));
        }
        request.setAttribute(JSONConstants.RESPONSE_VERSION_NUMBER, responseVersion);
        String redirectURL = null;
        if ((deviceDetails != null) && ((deviceDetails.contains("Mobile") && !(deviceDetails.contains("ZSheet/AndroidApp")) && !(deviceDetails.contains("ZSheet/iOSApp"))) || url.contains("iphone.do")) 
        		&& "true".equalsIgnoreCase(Constants.rebrand_Properties.getProperty("AllowMobileRedirection"))) {
            //if (true) {
            /**
             * CASE 1: /ropen.do?rid=<rid>
             * CASE 2: /open.do?docid=<docid>
             * CASE 3: /iphone.do?docid=<rid>
             * CASE 4: /iphone.do?docid=<docid>
             *
             */
            String queryString = req.getQueryString();
            
            if ("GET".equals(method) && (url.contains("ropen.do") || url.contains("open.do") || url.contains("/open/") || url.contains("iphone.do") || url.contains("published.do") || url.contains("/published/") || url.contains("/publishedsheet/") || url.contains("/publishedrange/") || url.contains("officeapi/v1") || url.contains("/ar/"))) {
                
                String mode = request.getParameter("mode");
                String type = request.getParameter("type");
                String proxyURL = request.getParameter("proxyURL");
                String frameOrigin = request.getParameter("frameorigin");

                if(queryString != null){
                    
                    String docID = request.getParameter("docid");
                    String view = request.getParameter("zview");
                    
                    if("android".equalsIgnoreCase(mode) || (proxyURL != null && !("preview").equalsIgnoreCase(proxyURL))) {
                        
                        fc.doFilter(request, response);
                        
                    } else  {
                    
                        if (docID != null) {

                            String rID = docID;
                            try {
                                //rID = DocumentUtils.getResourceId(docID);
                                /**
                                 * CASE 2: /open.do?docid=<docid>
                                 */
                                /**
                                 * CASE 4: /iphone.do?docid=<docid>
                                 */

                            } catch (Exception e) {
                                /**
                                 * CASE 4: /iphone.do?docid=<rid>
                                 */
                                rID = docID;
                            }
                            redirectURL = "/sheet/riphone.do?rid=" + rID;       // No I18N

                        } else if(url.contains("/open/") && (view == null || ("preview").equalsIgnoreCase(view))){
                            // (!("html").equalsIgnoreCase(view) && url.contains("/open/")) -> change to this condition to prevent other views in mobile browser.
                            // ---- Prevent Extra Redirection for custom domain ----
                            String domain = request.getServerName();
                            String rID = (String) request.getAttribute(ZFSNGConstants.RID);
                            boolean isCustomDomain = ZohoFS.isCustomDomain(rID, domain);
                            if(isCustomDomain) {
                                customDomain = domain;
                            }
                            // LOGGER.log(Level.INFO,">>> DEVICEHANDLER :: Forced Redirection to HTML View - deviceDetails :: Mobile Browser - isCustomDomain :: {0} - domain :: {1}",new Object[]{isCustomDomain, domain});// No I18N
                           // --------------------------------------------------------
                            if(frameOrigin == null){
                                redirectURL = "/sheet/open/" + rID + "?zview=html";        // No I18N
                            }
                            else{
                                redirectURL = "/sheet/open/" + rID + "?zview=html&frameorigin=" + frameOrigin;        // No I18N
                            }
                        }
                        else if (mode == null && url.contains("/published/")) {

                            redirectURL = "/sheet/published/" + request.getAttribute(ZFSNGConstants.RID) + "?mode=html";      // No I18N

                        }
                        else if ( url.contains("/publishedsheet/") && (mode == null) && "grid".equalsIgnoreCase(type)) {

                            redirectURL = "/sheet/publishedsheet/" + request.getAttribute(JSONConstants.RANGE_ID);      // No I18N

                        }
                        else if (url.contains("/publishedrange/") && (mode == null) && "grid".equalsIgnoreCase(type)) {

                            redirectURL = "/sheet/publishedrange/" + request.getAttribute(JSONConstants.RANGE_ID);      // No I18N

                        }
                    }
                
                } else {

                    if (url.contains("/open/")) {

                        redirectURL = "/sheet/open/" + request.getAttribute(ZFSNGConstants.RID) + "?zview=html";        // No I18N

                    } else if (url.contains("/published/")) {

                        redirectURL = "/sheet/published/" + request.getAttribute(ZFSNGConstants.RID) + "?mode=html";      // No I18N

                    }
                    else if (url.contains("officeapi/v1") || url.contains("/ar/")) { // No I18N
                        String servletPath = req.getServletPath();
                        int len = servletPath.length();
                        int startIdx = url.indexOf(servletPath) + len + 1;
                        int endIdx = url.indexOf("/", startIdx); // No I18N
                        if (endIdx == -1) {
                            endIdx = url.length();
                        }
                        String id = url.substring(startIdx, endIdx);
                        if(url.contains("officeapi/v1")){ // No I18N
                            redirectURL = "/sheet/officeapi/v1/" + id + "?zview=html"; // No I18N
                        }
                        else if(url.contains("/ar/")){ // No I18N
                            redirectURL = "/sheet/ar/" + id + "?zview=html"; // No I18N
                        }
//                        redirectURL = url.replace("editor", "remoteiphone");

                    } else {
                        if(!(url.contains("/publishedsheet/") || url.contains("/publishedrange/"))){
                            redirectURL = request.getScheme() + "://" + MobileDocsURL;
                            res.sendRedirect(redirectURL);
                            return;
                        }
                    }
                }
                
                LOGGER.log(Level.INFO, "DEVICE HANDLER >> From : {0} TO : {1}", new Object[]{req.getServletPath(), redirectURL});
                
                if (redirectURL != null) {
                    String serverUrl = null;
                    if(customDomain != null){
                        serverUrl = ClientUtils.getServerURL(req, true, redirectURL, false, false, customDomain);
                    }else {
                        serverUrl = ClientUtils.getServerURL(req, true, redirectURL, false, false);
                    }
                    ClientUtils.redirectResponse(res, serverUrl);
                    return;
                }
            }
        }
        fc.doFilter(request, response);
    }

    public void destroy() {

    }

}
