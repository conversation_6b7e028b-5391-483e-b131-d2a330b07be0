/* $Id$ */
package com.zoho.sheet.filter;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;

/* <AUTHOR> */
public class APIKeyFilter implements Filter {
	/* --  Used to use the dummy APIKey for APIKEYID population -- */
	public static Logger logger = Logger.getLogger(APIKeyFilter.class.getName());
	
	private FilterConfig filterConfig = null;
	public void init(FilterConfig filterConfig) throws ServletException
	{
		this.filterConfig = filterConfig;
	}

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws java.io.IOException, ServletException
	{
		//Default APIKey for remote API
		String _apiKeyId = EnginePropertyUtil.getSheetPropertyValue("ZS_REMOTE_APIKEYID"); //NO I18N
		String apiKeyId = (_apiKeyId != null) ? _apiKeyId : "877";	
		
		HttpServletRequest httpReq = (HttpServletRequest) request;	
		HttpServletResponse httpRes = (HttpServletResponse) response;		
		try {
			httpReq.setAttribute("apikeyid", apiKeyId);//NO I18N
		} catch(Exception ex){
			logger.log(Level.WARNING, "===>> Exception while setting APIkey.",ex);
		}
		if(filterChain == null || apiKeyId == null) {
			logger.log(Level.INFO, "===>> Problem while setting APIkey.");
		}
		filterChain.doFilter(httpReq , httpRes);
	}
	
	public void destroy() {

	}
}
