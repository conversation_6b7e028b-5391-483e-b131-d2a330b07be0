/*  $Id$ */
package com.zoho.sheet.filter;

// Java Imports
// Mickey Imports

//Sheet Imports
//public class SheetStatsTimerTask extends TimerTask
//{
//
//    private SheetStatsTrack sTrack = null;
//    private static Logger logger = Logger.getLogger(SheetStatsTimerTask.class.getName());
//
//    public void run()
//    {
//        /*for (Map.Entry<String, SheetStatsTrack> entrySet : SheetStatsRuleSetParser.keyRuleMap.entrySet()) {
//        String key = entrySet.getKey();
//
//        //System.out.println(key+" : "+entrySet.getValue());
//        sTrack = entrySet.getValue();
//
//
//        //sTrack.resetCount();
//
//        }*/
//        try
//        {
//            Persistence per = (Persistence) BeanUtil.lookup("Persistence", "public"); //NO I18N
//            SelectQuery sQuery_sheetstat = new SelectQueryImpl(new Table("SpreadsheetStats"));
//            sQuery_sheetstat.addSelectColumn(new Column(null, "*"));
//            DataObject dObject_sheetstat = per.get(sQuery_sheetstat);
//            if (dObject_sheetstat.isEmpty())
//            {
//                dObject_sheetstat = new WritableDataObject();
//            }
////				      System.out.println(" Is empty: "+dObject_sheetstat.isEmpty()+" dObject_sheetstat: "+dObject_sheetstat );
//            for (Map.Entry<String, SheetStatsTrack> entrySet : SheetStatsRuleSetParser.keyRuleMap.entrySet())
//            {
//                String key = null;
//                long curStatValue = 0;
//                key = (String) entrySet.getKey();
//                sTrack = entrySet.getValue();
//                curStatValue = sTrack.getCount();
////                logger.log(Level.INFO, key + " : " + curStatValue);
//
//
//                key = key.toUpperCase();
//
//                Row rRow_sheetstat = null;
//                long sheetstatvalue = 0;
//                Criteria c = new Criteria(new Column("SpreadsheetStats", "KEY"), key, QueryConstants.EQUAL);
//                rRow_sheetstat = dObject_sheetstat.getRow("SpreadsheetStats", c);//No I18N
//                /**This condition required for fresh start. Commended on: 08th Nov 2010*/
//                if (rRow_sheetstat != null)
//                {
//                    sheetstatvalue = (Long) rRow_sheetstat.get("VALUE");//No I18N
//                    sheetstatvalue = sheetstatvalue + curStatValue;
//                    rRow_sheetstat.set("VALUE", sheetstatvalue);//No I18N
//                    dObject_sheetstat.updateRow(rRow_sheetstat);
//                } else
//                {
//                    rRow_sheetstat = new Row("SpreadsheetStats");//No I18N
//                    sheetstatvalue = sheetstatvalue + curStatValue;
//                    rRow_sheetstat.set("KEY", key);//No I18N
//                    rRow_sheetstat.set("VALUE", sheetstatvalue);//No I18N
//                    dObject_sheetstat.addRow(rRow_sheetstat);
//                }
//                sTrack.resetCount();
//            }
//            // logger.log(Level.INFO,"Updated Is empty:  "+dObject_sheetstat.isEmpty()+" Updated dObject_sheetstat: "+dObject_sheetstat );
//            if (dObject_sheetstat.isEmpty())
//            {
//                per.add(dObject_sheetstat);
//            } else
//            {
//                per.update(dObject_sheetstat);
//            }
//
//
//        } catch (Exception e)
//        {
//            logger.log(Level.WARNING, "Error occurred while storing Data in  SpreadsheetStats");
//        }
//
//    }
//}
