/* $Id: UserCreationFilter.java,v 48021:171a727774bf 2012/09/10 16:21:21 venkatasubramanian $ */
package com.zoho.sheet.filter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.mfw.bean.BeanUtil;
import com.adventnet.persistence.DataAccess;
import com.adventnet.persistence.DataAccessException;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.PersistenceInitializer;
import com.adventnet.persistence.ReadOnlyPersistence;
import com.adventnet.persistence.Row;

import com.zoho.sheet.util.SheetPersistenceUtils;

public class UserCreationFilter implements Filter {

	private static final Logger LOGGER = Logger.getLogger(UserCreationFilter.class.getName());

	public static final List<String> ACCOUNT_TABLES = Arrays.asList(new String []{"AaaLogin", "AaaAccount", "AaaService"});//No I18N
    public static final List<String> USER_TABLES = Arrays.asList(new String []{"AaaUser", "AaaUserStatus", "AaaLogin", "AaaAccount", "AaaService", "AaaUserContactInfo", "AaaContactInfo","AaaAccountStatus"});//No I18N
    public static final List<String> ROLE_TABLES = Arrays.asList(new String []{"AaaAuthorizedRole", "AaaRole"});//No I18N

    public static final Column SERVICE_NAME = new Column("AaaService", "NAME");
    public static final Column LOGIN_NAME   = new Column("AaaLogin", "NAME");
    public static final Column EMAILID   = new Column("AaaContactInfo", "EMAILID");
    public static final Column USER_LOGINID = new Column("AaaUser", "LOGIN_ID");
    public static final Column USER_EMAILID   = new Column("AaaUser", "EMAIL_ID");
    public static final Column ACCOUNT_ID   = new Column("AaaAccount", "ACCOUNT_ID");
    
    public static String serviceName;
    public static boolean isorg = false;

	private String userParamNames;
	
	@Override
	public void destroy() {
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain fc) throws IOException, ServletException {
		User usr = IAMUtil.getCurrentUser();
		if (usr != null) {
			addUser(usr.getLoginName(), usr.getPrimaryEmail(), usr.getLoginName());
			fc.doFilter(request, response);
			return;
		}
		if (userParamNames == null || userParamNames.trim().isEmpty()) {
			return;
		}
		String paramNames[] = userParamNames.split(","); // No I18N
		for (String paramName : paramNames) {
			paramName = paramName.trim();
			String user = request.getParameter(paramName);
			if (user != null) {
				try {
					try {
						long zuid = Long.parseLong(user);
						usr = IAMProxy.getInstance().getUserAPI().getUserFromZUID(user);
						if(usr == null) {
							usr = IAMProxy.getInstance().getUserAPI().getUser(user);
						}
					} catch (Exception e) {
						usr = IAMProxy.getInstance().getUserAPI().getUser(user);
					}
					if (usr == null) {
						throw new ServletException("Invalid user : "+user);
					}
					addUser(usr.getLoginName(), usr.getPrimaryEmail(), usr.getLoginName());
					fc.doFilter(request, response);
					//return;
				}
				catch (Exception e) {
					LOGGER.log(Level.FINE, "", e);
				}
			}
		}
	}

	@Override
	public void init(FilterConfig fc) throws ServletException {
		userParamNames = fc.getInitParameter("user.param.name"); // No I18N
		serviceName = fc.getInitParameter("service.name"); // No I18N
		isorg = "true".equalsIgnoreCase(fc.getInitParameter("isorg")); // No I18N
		
		if (serviceName == null) {
			serviceName = getCurrentService();
		}
	}
	
	private static String getCurrentService() {
		String service = null;
		try {
			service = IAMProxy.getInstance().getCurrentServiceName();
		} catch (Exception e) {
			LOGGER.log(Level.FINE, "", e);
		}
		return service;
	}
	
	public static Object getObject(String tablename, String getColName, String gvnColName, Object value) throws DataAccessException {
		Criteria criteria = new Criteria(new Column(tablename, gvnColName), value, QueryConstants.EQUAL, false);
		DataObject dobj = null;
		try {
			dobj = getPersistence().get(tablename, criteria);
		}
		catch(Exception re) {
			throw new DataAccessException("Exception occured while fetching dataobject ", re); //No I18N
		}
		if(dobj.containsTable(tablename)){
			return dobj.getFirstValue(tablename, getColName);
		}
		return null;
	}
	
	public static Object getBeanInstance(String beanName) {
		Object instance = null;
		try {
			instance = BeanUtil.lookup(beanName);
		}
		catch(Exception e) {
			LOGGER.log(Level.SEVERE, "Exception caught while looking up Bean : {0}  : {1}", new Object [] {beanName, e});
		}
		return instance;
	}

	public static Persistence getPersistence() {
		return (Persistence) getBeanInstance("Persistence"); //No I18N
	}

	public static ReadOnlyPersistence getCachedPersistence() {
		return (ReadOnlyPersistence) getBeanInstance("PureCachedPersistence"); //No I18N
	}

	public static Persistence getSASPersistence(String loginName) throws Exception {
		Persistence sasPersistence = SheetPersistenceUtils.getPersistence( loginName);
		return sasPersistence;
	}

	public static DataObject getSASUserDO(String loginName) {
		try {
			Persistence sasPersistence = getSASPersistence(loginName);
			DataObject userDO = null;
			Criteria criteria = new Criteria(LOGIN_NAME, loginName, QueryConstants.EQUAL, false);
			userDO = sasPersistence.get(USER_TABLES, criteria);
			LOGGER.log(Level.FINE, "The user DO is {0}", userDO);
			return userDO;
		} catch(Exception e) {
			LOGGER.log(Level.FINE,"", e);
		}
		return null;
	}

	public static DataObject getUserDO(String loginName, String emailId, boolean fromCache) {
		try{
			DataObject userDO = null;
			Criteria criteria = new Criteria(LOGIN_NAME, loginName, QueryConstants.EQUAL, false);
			if(emailId != null){
				criteria = criteria.or(new Criteria(EMAILID, emailId, QueryConstants.EQUAL, false));
			}
			if(fromCache){
				userDO = getCachedPersistence().get(USER_TABLES, criteria);
			}else{
				userDO = getPersistence().get(USER_TABLES, criteria);
			}
			LOGGER.log(Level.FINE, "The user DO is {0}", userDO);
			return userDO;
		} catch(Exception e) {
			LOGGER.log(Level.FINE,"", e);
		}
		return null;
	}

	public boolean addUser (String userName, String emailId, String firstName) {
		try {
			String loginName = userName;

			//Dummy values for password & salt for compatibility
			// with other zoho services
			String pass  = userName;
			String salt = userName;
			if(loginName  == null) { loginName  = emailId;}
			if(firstName == null) { firstName = loginName ;}
			
			DataObject userDO = null;
			if(PersistenceInitializer.onSAS()) {
				userDO = getSASUserDO(userName);
			} else {
				userDO = getUserDO(loginName, emailId, false);
			}
			if(userDO != null && !userDO.isEmpty()) {
				LOGGER.log(Level.FINE, " User already exist's. loginName : {0}", userName);
				return true;
			}
			LOGGER.log(Level.INFO, "Started account creation for loginName : {0}", loginName);
			
			long now = System.currentTimeMillis();
			DataObject dobj = DataAccess.constructDataObject();
			
			Row user = new Row("AaaUser"); //No I18N
			Row ucontact = new Row("AaaUserContactInfo"); //No I18N
			Row contact = new Row("AaaContactInfo"); //No I18N
			Row login = new Row("AaaLogin"); //No I18N
			Row account = new Row("AaaAccount"); //No I18N
			Row password = new Row("AaaPassword"); //No I18N
			Row apass = new Row("AaaAccPassword"); //No I18N
			Row status = new  Row("AaaUserStatus"); //No I18N
			Row role = new Row("AaaAuthorizedRole"); //No I18N
			Row astatus = new Row("AaaAccountStatus"); //No I18N
			Row profile = new Row("AaaAccOwnerProfile"); //No I18N

			user.set("FIRST_NAME", firstName); //No I18N
			user.set("CREATEDTIME", now); //No I18N
			dobj.addRow(user);

			status.set("USER_ID", user.get("USER_ID")); //No I18N
			status.set("STATUS", "ACTIVE"); //No I18N
			status.set("UPDATEDTIME", now); //No I18N
			dobj.addRow(status);

			login.set("NAME", loginName); //No I18N
			login.set("USER_ID", user.get("USER_ID")); //No I18N
			dobj.addRow(login);

			account.set("SERVICE_ID", getObject("AaaService", "SERVICE_ID", "NAME", serviceName));  //No I18N
			account.set("LOGIN_ID", login.get("LOGIN_ID")); //No I18N
			account.set("CREATEDTIME", now); //No I18N
			account.set("ACCOUNTPROFILE_ID", getObject("AaaAccAdminProfile", "ACCOUNTPROFILE_ID", "NAME", "Profile 1")); //No I18N
			dobj.addRow(account);

			profile.set("ACCOUNT_ID", account.get("ACCOUNT_ID")); //No I18N
			dobj.addRow(profile);

			astatus.set("ACCOUNT_ID", account.get("ACCOUNT_ID"));  //No I18N
			astatus.set("STATUS", "NEW"); //No I18N
			astatus.set("EXPIREAT", Long.valueOf(-1L));  //No I18N
			astatus.set("UPDATEDTIME", now); //No I18N
			dobj.addRow(astatus);

			password.set("PASSWORD", pass); //No I18N
			password.set("SALT", salt); //No I18N
			password.set("PASSWDRULE_ID", getCachedPersistence().get("AaaPasswordRule", (Criteria)null).getFirstValue("AaaPasswordRule", "PASSWDRULE_ID")); //No I18N
			password.set("PASSWDPROFILE_ID", getObject("AaaPasswordProfile", "PASSWDPROFILE_ID", "NAME", "Profile 1")); //No I18N
			password.set("CREATEDTIME", now); //No I18N
			dobj.addRow(password);

			Row pstatus= new Row("AaaPasswordStatus"); //No I18N
			pstatus.set("PASSWORD_ID", password.get("PASSWORD_ID")); //No I18N
			pstatus.set("STATUS", "NEW"); //No I18N
			pstatus.set("EXPIREAT", Long.valueOf(-1L));//No I18N
			pstatus.set("UPDATEDTIME", now); //No I18N
			dobj.addRow(pstatus);

			apass.set("ACCOUNT_ID", account.get("ACCOUNT_ID")); //No I18N
			apass.set("PASSWORD_ID", password.get("PASSWORD_ID")); //No I18N
			dobj.addRow(apass);

			role.set("ACCOUNT_ID", account.get("ACCOUNT_ID")); //No I18N
			role.set("ROLE_ID", getObject("AaaRole", "ROLE_ID", "NAME", "AccessAllTables")); //No I18N
			dobj.addRow(role);

			contact.set("EMAILID" , emailId); //No I18N
			dobj.addRow(contact);

			ucontact.set("CONTACTINFO_ID" ,contact.get("CONTACTINFO_ID")); //No I18N
			ucontact.set("USER_ID" ,  user.get("USER_ID")); //No I18N
			dobj.addRow(ucontact);
			if(isorg) {
				Row orgname = new Row("AaaOrganization"); //No I18N
				int index = loginName.indexOf('.');
				String orgNameVal = "";
				if(index < 0) {
					orgNameVal = loginName + "|"+ now + "|ORG"; //No I18N
				}else{
					orgNameVal = loginName.substring(index+1);
					orgNameVal = orgNameVal + "$o"; //No I18N
				}

				orgname.set("NAME",orgNameVal); //No I18N
				orgname.set("CREATEDTIME",now); //No I18N
				dobj.addRow(orgname);

				Row orguser = new Row("AaaOrgUser"); //No I18N
				orguser.set("ORG_ID",orgname.get("ORG_ID")); //No I18N
				orguser.set("USER_ID",user.get("USER_ID")); //No I18N
				dobj.addRow(orguser);

				Row orgcontact = new Row("AaaOrgContactInfo"); //No I18N
				orgcontact.set("ORG_ID",orgname.get("ORG_ID")); //No I18N
				orgcontact.set("CONTACTINFO_ID",ucontact.get("CONTACTINFO_ID")); //No I18N
				dobj.addRow(orgcontact);
			}
			
			dobj = getPersistence().add(dobj);
			LOGGER.log(Level.FINE, "The user successfully added is {0}", dobj);
			return true;
		}
		catch (Exception e) {
			LOGGER.log(Level.WARNING, "", e);
			return false;
		}
	}
}