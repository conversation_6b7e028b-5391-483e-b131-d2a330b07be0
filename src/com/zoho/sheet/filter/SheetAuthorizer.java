/* $Id$ */
package com.zoho.sheet.filter;

import com.adventnet.iam.IAMProxy;
import com.adventnet.zoho.websheet.model.ErrorCode;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.exception.ExceptionHandler;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceStatus;
import com.zoho.zfsng.constants.ZFSNGConstants;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *
 */
public class SheetAuthorizer implements Filter {
	private static final Logger LOGGER = Logger.getLogger(SheetAuthorizer.class.getName());

	@Override
	public void init(FilterConfig filterConfig) throws ServletException
	{

	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest req 	= (HttpServletRequest) request;
		HttpServletResponse res = (HttpServletResponse) response;
		
		String id;
		String servletPath = ((HttpServletRequest) request).getServletPath();
		
		if(request.getParameter(JSONConstants.RANGE_ID) != null) {
			id = request.getParameter(JSONConstants.RANGE_ID);
		} else {
			String URIPath = ((HttpServletRequest) request).getRequestURI();

	        int len = servletPath.length();

	        int startIdx = URIPath.indexOf(servletPath) + len + 1;
	        int endIdx = URIPath.indexOf("/", startIdx);
	        if (endIdx == -1) {
	            endIdx = URIPath.length();
	        }
	        id = URIPath.substring(startIdx, endIdx);
		}
	        
//		String id = req.getParameter(JSONConstants.RANGE_ID);
		JSONObjectWrapper rangeMeta;
         
		try {
			String rangeDetails = RedisHelper.get(RedisHelper.PUBLISHED_RANGES + id, -1);
			String zoid = String.valueOf(DocumentUtils.getZOID());
			if(rangeDetails != null) {
				rangeMeta = new JSONObjectWrapper(rangeDetails);
				
				if(((HttpServletRequest) request).getUserPrincipal() == null && !"external".equalsIgnoreCase(rangeMeta.optString("pubType"))) {
					String serviceName = EnginePropertyUtil.getSheetPropertyValue("serviceName");   // NO I18N
	                String iamServerUrl = IAMProxy.getInstance().getIAMServerURL();
	                
	                String redirectionUrl = iamServerUrl +"/signin?servicename="+serviceName+"&serviceurl=/sheet/"+(servletPath.contains("publishedrange") ? "publishedrange/" : "publishedsheet/")+id+"?type=grid";   // NO I18N
	                
	                ((HttpServletResponse) response).setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
	                ((HttpServletResponse) response).setHeader("Location", redirectionUrl);//No internationalization
	                ((HttpServletResponse) response).setHeader("Connection", "close");//No internationalization
	                return;
				}
				
				if("org".equalsIgnoreCase(rangeMeta.optString("pubType")) && !rangeMeta.optString("orgId").equalsIgnoreCase(zoid)) {
					request.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(request, response);//No I18N
	                return;
				}
			} else {
				rangeMeta = new JSONObjectWrapper();

//				String docId = String.valueOf(PublishRangeUtils.getDocumentId(id));
				String[] docDetails = DocumentUtils.getDocumentIdByPublishRangeID(id);

				if (((HttpServletRequest) request).getUserPrincipal() == null && !"external".equalsIgnoreCase(docDetails[2])) {
					String serviceName = EnginePropertyUtil.getSheetPropertyValue("serviceName");   // NO I18N
					String iamServerUrl = IAMProxy.getInstance().getIAMServerURL();

					String redirectionUrl = iamServerUrl + "/signin?servicename=" + serviceName + "&serviceurl=/sheet/" + (servletPath.contains("publishedrange") ? "publishedrange/" : "publishedsheet/") + id + "?type=grid";   // NO I18N

					((HttpServletResponse) response).setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
					((HttpServletResponse) response).setHeader("Location", redirectionUrl);//No internationalization
					((HttpServletResponse) response).setHeader("Connection", "close");//No internationalization
					return;
				}

				if (docDetails[0] == "null" && Boolean.parseBoolean(request.getParameter("isHandheldDevice"))) {
					((HttpServletResponse) response).setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
					ExceptionHandler.handle((HttpServletRequest) request, (HttpServletResponse) response, ErrorCode.ERRORMSG_PUBLISH_LINK_REMOVED);
				} else {

					String rid = DocumentUtils.getResourceId(docDetails[0], docDetails[1]);
					ResourceInfo rinfo = ZohoFS.getResourceInfo(rid);

					int resourseStatus = rinfo.getStatus();
					if (resourseStatus == ResourceStatus.TRASHED || resourseStatus == ResourceStatus.DELETED) {
						LOGGER.log(Level.INFO, "accessing published range after it is trashed/deleted");
						request.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(request, response);//No I18N
						return;
					}
					rangeMeta.put("spaceId", rinfo.getSpaceId());
					rangeMeta.put("owner", rinfo.getOwner());
					rangeMeta.put("rid", rid);
					rangeMeta.put("pubType", docDetails[2]);
					rangeMeta.put("orgId", docDetails[3]);

					if ("org".equalsIgnoreCase(rangeMeta.getString("pubType")) && !rangeMeta.getString("orgId").equalsIgnoreCase(zoid)) {
						request.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(request, response);//No I18N
						return;
					}

					RedisHelper.set(RedisHelper.PUBLISHED_RANGES + id, rangeMeta.toString(), RedisHelper.PUBLISHED_RANGES_EXPIRE_TIME);
				}
			}

			if(((HttpServletRequest) request).getUserPrincipal() == null && "org".equalsIgnoreCase(rangeMeta.getString("pubType"))) {
				LOGGER.info("Accessing org published range url without login.. "+id);
				String serviceName = EnginePropertyUtil.getSheetPropertyValue("serviceName");   // NO I18N
				String iamServerUrl = IAMProxy.getInstance().getIAMServerURL();

				String redirectionUrl = iamServerUrl +"/signin?servicename="+serviceName+"&serviceurl=/sheet/"+(servletPath.contains("publishedrange") ? "publishedrange/" : "publishedsheet/")+id;   // NO I18N

				((HttpServletResponse) response).setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
				((HttpServletResponse) response).setHeader("Location", redirectionUrl);//No internationalization
				((HttpServletResponse) response).setHeader("Connection", "close");//No internationalization
				return;
			}

			request.setAttribute(ZFSNGConstants.RID, rangeMeta.get("rid"));
			request.setAttribute(ZFSNGConstants.ROWNER, rangeMeta.get("owner"));
			request.setAttribute(ZFSNGConstants.SPACE_ID, rangeMeta.get("spaceId"));
			request.setAttribute(JSONConstants.RANGE_ID, id);
			chain.doFilter(request, response);
			
		} catch (Exception e) {
			LOGGER.info("Exception in SheetAuthorizer :: "+e);
			request.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(request, response);//No I18N
		}
	}

	@Override
	public void destroy()
	{

	}
}
