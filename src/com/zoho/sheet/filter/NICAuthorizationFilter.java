//$ID$
package com.zoho.sheet.filter;

import com.zoho.sheet.authorization.util.NICAuthorizationUtil;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * author - vengatesh.ss - 10141
 */
public class NICAuthorizationFilter implements Filter {
    private static final Logger LOGGER = Logger.getLogger(NICAuthorizationFilter.class.getName());

    // --------------------------------------- Filter Functions ----------------------------------------------------------------------
    public void init(FilterConfig fc) throws ServletException {}

    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain fc) throws ServletException, IOException {
        if(isAuthorized((HttpServletRequest) request, (HttpServletResponse) response)){
            fc.doFilter(request, response);
        }
    }

    public void destroy() {}
    // ---------------------------------------- Authorization Logic -----------------------------------------------------------------

    private boolean isAuthorized(HttpServletRequest request, HttpServletResponse response){
        if(NICAuthorizationUtil.isNICGrid()){
            if(NICAuthorizationUtil.isNICRestrictedAction(request, response)){
                LOGGER.log(Level.WARNING, "NIC GRID: Restricted Action Access");//NO I18N
                return false;
            }
            // TODO: Need to add other NIC Related checks here.
        }
        return true;
    }
}
