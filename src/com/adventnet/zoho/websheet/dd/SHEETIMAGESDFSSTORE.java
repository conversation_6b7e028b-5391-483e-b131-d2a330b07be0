package com.adventnet.zoho.websheet.dd;

/** <p> Description of the table <code>SheetImagesDFSStore</code>.
 *  Column Name and Table Name of  database table  <code>SheetImagesDFSStore</code> is mapped
 * as constants in this util.</p> 
  Block ID information w.r.t to Images. For example ,
				Images will be stored as be ${unique_key}.png ${unique_key}_thumb.png. <br>
   * 
  * Primary Key for this definition is  <br>
  <ul>
  * <li> {@link #IMAGES_DFSSTORE_ID}
  * </ul>
 */
 
public final class SHEETIMAGESDFSSTORE
{
    private SHEETIMAGESDFSSTORE()
    {
    }
   
    /** Constant denoting the Table Name of this definition.
     */
    public static final String TABLE = "SheetImagesDFSStore" ;
    /**
                            * This column is an Primary Key for this Table definition. <br>
                            * Data Type of this field is <code>BIGINT</code>. <br>
                                          * This field is nullable. <br>
                            *    This field is an unique column.<br>
                  */
    public static final String IMAGES_DFSSTORE_ID= "IMAGES_DFSSTORE_ID" ;

    /*
    * The index position of the column IMAGES_DFSSTORE_ID in the table.
    */
    public static final int IMAGES_DFSSTORE_ID_IDX = 1 ;

    /**
                            * Data Type of this field is <code>CHAR</code>. <br>
                     * Maximum length of this field value is <code>255</code>. <br>
                                   * This field is nullable. <br>
                            *    This field is an unique column.<br>
                  */
    public static final String BLOCK_ID= "BLOCK_ID" ;

    /*
    * The index position of the column BLOCK_ID in the table.
    */
    public static final int BLOCK_ID_IDX = 2 ;

    /**
                            * Data Type of this field is <code>BIGINT</code>. <br>
                                          * This field is nullable. <br>
                            *    This field is an unique column.<br>
                  */
    public static final String UNIQUE_KEY_ID= "UNIQUE_KEY_ID" ;

    /*
    * The index position of the column UNIQUE_KEY_ID in the table.
    */
    public static final int UNIQUE_KEY_ID_IDX = 3 ;

    /**
                            * Data Type of this field is <code>CHAR</code>. <br>
                     * Maximum length of this field value is <code>255</code>. <br>
                                   * This field is nullable. <br>
                            *    This field is an unique column.<br>
                  */
    public static final String UNIQUE_KEY= "UNIQUE_KEY" ;

    /*
    * The index position of the column UNIQUE_KEY in the table.
    */
    public static final int UNIQUE_KEY_IDX = 4 ;

    /**
              * <p> The time when the image is added.</p>
                            * Data Type of this field is <code>BIGINT</code>. <br>
                                          * This field is nullable. <br>
                            *    This field is an unique column.<br>
                  */
    public static final String CREATED_TIME= "CREATED_TIME" ;

    /*
    * The index position of the column CREATED_TIME in the table.
    */
    public static final int CREATED_TIME_IDX = 5 ;

    /**
                            * Data Type of this field is <code>BIGINT</code>. <br>
                                          * This field is nullable. <br>
                            *    This field is an unique column.<br>
                  */
    public static final String DOCUMENT_ID= "DOCUMENT_ID" ;

    /*
    * The index position of the column DOCUMENT_ID in the table.
    */
    public static final int DOCUMENT_ID_IDX = 6 ;

    /**
              * <p> Img resource id.</p>
                            * Data Type of this field is <code>CHAR</code>. <br>
                     * Maximum length of this field value is <code>255</code>. <br>
                                   * This field is nullable. <br>
                                */
    public static final String IMG_RID= "IMG_RID" ;

    /*
    * The index position of the column IMG_RID in the table.
    */
    public static final int IMG_RID_IDX = 7 ;

    /**
              * <p> Uniquely indentifies a file.</p>
                            * Data Type of this field is <code>CHAR</code>. <br>
                     * Maximum length of this field value is <code>50</code>. <br>
                                   * This field is not nullable. <br>
                            *    This field is an unique column.<br>
                  */
    public static final String FILE_TYPE= "FILE_TYPE" ;

    /*
    * The index position of the column FILE_TYPE in the table.
    */
    public static final int FILE_TYPE_IDX = 8 ;

    /**
              * <p> file extension.</p>
                            * Data Type of this field is <code>CHAR</code>. <br>
                     * Maximum length of this field value is <code>10</code>. <br>
                                   * This field is not nullable. <br>
                            *    This field is an unique column.<br>
                  */
    public static final String FILE_EXTN= "FILE_EXTN" ;

    /*
    * The index position of the column FILE_EXTN in the table.
    */
    public static final int FILE_EXTN_IDX = 9 ;

    /**
                            * Data Type of this field is <code>BOOLEAN</code>. <br>
                            * Default Value is <code>false</code>. <br>
                     * This field is not nullable. If value for field is not set default value "<code>false</code>" , 
       * will be taken.<br>
                         */
    public static final String IS_ENCRYPTED= "IS_ENCRYPTED" ;

    /*
    * The index position of the column IS_ENCRYPTED in the table.
    */
    public static final int IS_ENCRYPTED_IDX = 10 ;

}
