/* $Id$ */
package com.adventnet.zoho.websheet.task;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.iam.IAMUtil;
import com.adventnet.mfw.bean.BeanUtil;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.beans.JobSchedulerBean;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.zoho.scheduler.RepetitiveJob;
import com.zoho.scheduler.RunnableJob;
import com.zoho.sheet.util.ChangeNotifier;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.zfsng.client.ZohoFS;

import java.util.logging.Level;
import java.util.logging.Logger;

public class SendDocChangeNotification implements RunnableJob
{
	public static Logger logger = Logger.getLogger(SendDocChangeNotification.class.getName());
	private static final boolean ISDEV_MODE = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("isDevelopmentMode"));       // NO i18N
	public static int schUpdDayCount = Integer.parseInt(EnginePropertyUtil.getSheetPropertyValue("SchUpdateDayCount"));
	public void run(long  jobID) {
		if(Constants.IS_DISASTER_RECOVERY_SERVER) {
			return;
		}
		logger.info("SENDING MAIL FOR DAILY NOTIFICATION: JOBID"+jobID);
		Row 	    fetchRow 	= null;
		try {
			Persistence persistence = SheetPersistenceUtils.getPersistence();
			//TODO - dfs method
			DataObject  fetchDO     = getJobDetails(jobID);

			JobSchedulerBean JSbean = (JobSchedulerBean) BeanUtil.lookup("JobSchedulerBean");
			RepetitiveJob repetitiveJob = JSbean.fetchRepetitiveJob(jobID);
			if(fetchDO == null) {
				logger.info("No data present in DB fetchRepetitiveJob :: "+JSbean.fetchRepetitiveJob(jobID));
				JSbean.deleteSchedule(jobID, null);
				return;
			}

			fetchRow 	= fetchDO.getRow("NotificationSettings"); //No I18N
			long document_ID = (Long)fetchRow.get("DOCUMENT_ID");
			String zuid = fetchRow.get("SUBSCRIBER_MEMBERS").toString();
			String subsciber = fetchRow.get("SUBSCRIBER").toString();
			String subsciberID = fetchRow.get("SUBSCRIBER_ID").toString();
			
			// Sending mail code
			String rid = DocumentUtils.getResourceId(document_ID+"", null);
			String docsSpaceId = ZohoFS.getOwnerZID(rid);
			String documentId = DocumentUtils.getDocumentId(rid, docsSpaceId);
			WorkbookContainer.ContainerEntity entity = WorkbookContainer.getEntity();
			String sheetSpaceId = ZohoFS.getSpaceId(rid);
			String docName = DocumentUtils.getDocumentName(rid);
			entity.docOwner = sheetSpaceId;
			entity.resourceId = rid;
			entity.docsSpaceId = docsSpaceId;
			entity.documentId = documentId;
			entity.documentName		=	docName;
			WorkbookContainer wbContainer = new WorkbookContainer(entity);

			String lang = null;
			//ClientUtils.getServerURL("", true, null, false, true)

			Workbook workbook = wbContainer.getWorkbook(null);
			if (IAMUtil.getCurrentUser() != null) {
				lang = IAMUtil.getCurrentUser().getLanguage().toString();
			} else {
				lang = workbook.getLocale().getLanguage();
			}

			String finalLang = lang;
			try {
				ChangeNotifier docChangeNotification = new ChangeNotifier(document_ID, rid, sheetSpaceId, docsSpaceId, 4, finalLang,  docName);// DOC_CHANGES_DAILY
				int versionCnt = ZohoFS.getVersionCountByTime(sheetSpaceId, rid, getPreviousDay(), System.currentTimeMillis());
				String versions = ZohoFS.getVersionsByTime(sheetSpaceId, rid, getPreviousDay(), System.currentTimeMillis(), 0, 100,  true);
				String fromVersion = "";
				String topVersion = ZohoFS.getTopVersion(docsSpaceId, rid);
				JSONObjectWrapper notifyJson = null;
				boolean triggerNotifyMail = false;
				if(versions != null) {
					if(!subsciberID.equalsIgnoreCase("1")) { // All Doc Daily Notify Subscribed
						notifyJson = DocumentUtils.getNotificationRangeById(workbook, jobID+"");
						triggerNotifyMail = notifyJson.isEmpty() ? false : true;
					} else {
						triggerNotifyMail = true;
					}
					if(triggerNotifyMail) {
						logger.info("SendDocChangeNotification::rid::"+rid);
						docChangeNotification.notifyDailyChanges(wbContainer, versions, notifyJson, zuid);
					}
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				logger.log(Level.INFO, "Exception : Sending doc Change Notification : workbook Save : {e}", e);
			}
		} catch(Exception e) {
            logger.log(Level.WARNING,"Problem in executing task....{0}",new Object[]{e});
        }
	}

	private long getPreviousDay(){
		return System.currentTimeMillis()-24*60*60*1000;
	}
	private DataObject getJobDetails(Long jobID) {
		// TODO Auto-generated method stub
		String tableName = "NotificationSettings";  //NO I18N
		Persistence per = SheetPersistenceUtils.getPersistence();
		Criteria subscriberCri = new Criteria(new Column(tableName, "NOTIFICATION_ID"), jobID, QueryConstants.EQUAL); 
		DataObject dobj = null;
		try {
			dobj = per.get(tableName, subscriberCri);
			if(!dobj.isEmpty()) {
				return dobj;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.log(Level.INFO, "Exception on executing scheduler :: jobID ::"+jobID , e);
		}
		return null;
	}
	
	public static void sendInternalRequest(){
		logger.info("Problem in executing task");
	}
}

