//$Id$

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.task;

import java.util.logging.Logger;


import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
//import com.zoho.scheduler.RunnableJob;
//import com.zoho.sheet.conversion.ConversionServer;

//public class RestartLibreOfficeServer implements RunnableJob {
//
//public static Logger logger = Logger.getLogger(RestartLibreOfficeServer.class.getName());
//
//    @Override
//    public void run(long jobID) {
//    	try{
//    		restartLibreOfficeServer();
//    	}catch(Exception e){
//
//    	}
//    }
//
//    public static void restartLibreOfficeServer() {
//    	try{
//    		//SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
//    		logger.info("[LIBO:SCHEDULER] STARTED:");
//    		//String groupName = EnginePropertyUtil.getSheetPropertyValue("CONVERSION_SERVER_GROUP_NAME"); //NO I18N
//    		//String redisKey = "sheet:cs:";//NO I18N
//    		JSONArrayWrapper liboServers = ConversionServer.getInactiveServersFromDB();
//    		logger.info("[LIBO:SCHEDULER] ALL SERVERS:"+liboServers);
//    		for(int i =0 ;i<liboServers.length();i++) {
//    			//String value = liboServers;
//    			String[] serverDetails =  liboServers.getString(i).split(":");
//    			logger.info("[LIBO:SCHEDULER] RESTARTING:"+serverDetails[0]+":"+serverDetails[1]);
//    			restartServer(serverDetails[0],serverDetails[1]);
//    		}
//    	}catch(Exception e){
//    		logger.info("[LIBO:SCHEDULER] RESTARTING EXCEPTION:"+e);
//    	}
//    }
//
//    private static void restartServer(String ip, String ports1) throws Exception{
//		String[] ports = ports1.split(":");
//
//		for(String port: ports) {
//			try{
//				ConversionServer cs = new ConversionServer();
//				boolean restart = cs.schedulerRestartLiboServer(ip, port);
//			}
//			catch(Exception e){
//				logger.info("[LIBO:SCHEDULER][CONVERSION:SERVER] RESTARTING SERVER EXCEPTION:"+e);
//			}
//			//Thread.sleep(10000);
//		}
//	}
//
//}
