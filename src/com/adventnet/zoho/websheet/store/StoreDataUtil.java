/* $Id$ */
package com.adventnet.zoho.websheet.store;


import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.logging.Logger;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathFactory;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.zoho.sas.container.AppResources;

public class StoreDataUtil {
	private static Logger logger = Logger.getLogger(StoreDataUtil.class.getName());
	private static Map<String,StoreType> storeTypes = new HashMap<String,StoreType>();
	private static Map<String,StoreObject> stores = new HashMap<String,StoreObject>();
	private static List<String> readList = new ArrayList<String>();
	private static List<String> writeList = new ArrayList<String>();
	private static List<String> deleteList = new ArrayList<String>();
	//private static Map<Long,String> skipUsers = new HashMap<Long,String>();

	public static XPathExpression storeTypesXPath = null;
	public static XPathExpression storesXPath = null;
	public static XPathExpression readStoreXPath = null;
	public static XPathExpression writeStoreXPath = null;
	public static XPathExpression deleteStoreXPath = null;
	public static XPathExpression skipUsersXPath = null;

	static {
		try {
			/* XPath Expression Initialization */
			XPathFactory factory = XPathFactory.newInstance();
			XPath xPath = factory.newXPath();

			storeTypesXPath = xPath.compile("/root/storetypes/*");
			storesXPath = xPath.compile("/root/stores/store");
			readStoreXPath = xPath.compile("/root/storeorder/read/store");
			writeStoreXPath = xPath.compile("/root/storeorder/write/store");
			deleteStoreXPath = xPath.compile("/root/storeorder/delete/store");
			skipUsersXPath = xPath.compile("/root/users");
		}catch(Exception e) {
			e.printStackTrace();
		}
	}
	public static Element loadXML(String filePath) throws Exception {
		File file = new File(filePath);
		DocumentBuilderFactory docBuilderFactory = DocumentBuilderFactory.newInstance();
		docBuilderFactory.setValidating(false);
		DocumentBuilder docBuilder= docBuilderFactory.newDocumentBuilder();
		Document doc = docBuilder.parse(file.getPath());
		Element root = doc.getDocumentElement();
		return root;
	}
	public static void parseStoreXml(String filePath) throws Exception {
		// String storesPath = AppResources.getProperty("server.home")  + "/conf/websheet/stores.xml"; // No i18N
		String storesPath = AppResources.getProperty("server.home")  + filePath; // No i18N
		Element root = loadXML(storesPath);

		populateStoreTypes( (NodeList) storeTypesXPath.evaluate(root, XPathConstants.NODESET) );

		populateStores( (NodeList) storesXPath.evaluate(root, XPathConstants.NODESET) );

		populateStoreOrder( (NodeList) readStoreXPath.evaluate(root, XPathConstants.NODESET) , readList);

		populateStoreOrder( (NodeList) writeStoreXPath.evaluate(root, XPathConstants.NODESET) , writeList);

		populateStoreOrder( (NodeList) deleteStoreXPath.evaluate(root, XPathConstants.NODESET) , deleteList);

		//populateSkipUsers( (NodeList) skipUsersXPath.evaluate(root, XPathConstants.NODESET) );
	}

	private static void populateStoreTypes(NodeList list) throws Exception {
		int len = list.getLength();
		for(int i=0;i<len;i++) {
			Element storeTypeEle = (Element) list.item(i);
			populateStoreType(storeTypeEle);
		}
	}

	private static void populateStoreType(Element ele) throws Exception {
		String name = ele.getNodeName().toLowerCase();
		String tableName = ele.getAttribute("tablename");
		String columnName = ele.getAttribute("columnname");
		String storeTable = ele.getAttribute("storetable");

		StoreType stype = new StoreType();
		stype.setName(name);
		stype.setTableName(tableName);
		stype.setColumnName(columnName);
		stype.setStoreTable(storeTable);
		storeTypes.put(name, stype);
	}

	public static class StoreType {
		private String name;
		private String tableName;
		private String columnName;
		private String storeTable;

		public void setTableName(String tableName) {
			this.tableName = tableName;
		}

		public String getTableName() {
			return tableName;
		}

		public void setName(String name) {
			this.name = name;
		}

		public void setColumnName(String columnName) {
			this.columnName = columnName;
		}

		public String getColumnName() {
			return columnName;
		}

		public void setStoreTable(String storeTable) {
			this.storeTable = storeTable;
		}

		public String getStoreTable() {
			return storeTable;
		}
	}

	private static void populateStores(NodeList list) throws Exception {
		int len = list.getLength();
		for(int i=0;i<len;i++) {
			Element store = (Element) list.item(i);
			populateStore(store);
		}
	}

	private static void populateStore(Element ele) throws Exception {
		StoreObject sobj = new StoreObject();
		sobj.setName(ele.getAttribute("name"));
		sobj.setClassName(ele.getAttribute("classname"));
		sobj.setCache(ele.getAttribute("cache"));
		sobj.setSkip(ele.getAttribute("skip"));
		sobj.setStoreTypes(populateSTypes(ele.getChildNodes()));
		sobj.setClass(loadClass(sobj.getClassName()));
		stores.put(sobj.getName(), sobj);
	}

	private static Class loadClass(String className) throws Exception {
		Class clazz = Class.forName(className);
		return clazz;
	}

	public static class StoreObject {
		private String name;

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		private String className;

		public String getClassName() {
			return className;
		}

		public void setClassName(String className) {
			this.className = className;
		}

		private boolean cache;
		public boolean isCache() {
			return cache;
		}

		public void setCache(String cache) {
			if(cache == null || cache.length() == 0) {
				this.cache = false;
			}
			Boolean b = Boolean.valueOf(cache);
			this.cache = b.booleanValue();
		}

		private String skip ;

		public String getSkip() {
			return skip;
		}

		public void setSkip(String skip) {
			this.skip = skip;
		}

		private Map<String,Class> storeTypesMap = new HashMap<String,Class>();

		public void setStoreTypes(Map<String,Class> storeTypesMap) {
			this.storeTypesMap = storeTypesMap;
		}

		public Class getStoreClassName(String storeType) {
			return this.storeTypesMap.get(storeType);
		}

		public Map<String,Class> getStoreTypes() {
			return this.storeTypesMap;
		}

		private Class clazz = null;
		public Class getClazz() {
			return clazz;
		}

		public void setClass(Class clazz) {
			this.clazz = clazz;
		}
	}

	private static Map<String,Class> populateSTypes(NodeList list) throws Exception {
		Map<String,Class> storeTypesMap = new HashMap<String,Class>();
		int len = list.getLength();
		for(int i=0;i<len;i++) {
			Node node = list.item(i);
			short type = node.getNodeType();
			if(type == Node.ELEMENT_NODE) {
				String className = ((Element) node).getAttribute("classname");
				storeTypesMap.put(node.getNodeName(), loadClass(className));
			}
		}
		return storeTypesMap;
	}

	private static void populateStoreOrder(NodeList list, List<String> orderList) {
		int len = list.getLength();
		for(int i=0;i<len;i++) {
			Node node = (Node) list.item(i);
			short type = node.getNodeType();
			if(type	==	Node.ELEMENT_NODE)	{
				orderList.add(node.getFirstChild().getNodeValue());
			}
		}
	}

	public static StoreType getStoreType(String storeType) throws Exception {
		return storeTypes.get(storeType);
	}

	public static List<String> getReadList() {
		return readList;
	}

	public static List<String> getWriteList() {
		return writeList;
	}

	public static List<String> getDeleteList() {
		return deleteList;
	}

	public static Map<String,Store> getAvailableStores(String storeType) throws Exception {
		Map<String,Store> map = new HashMap<String,Store>();
		Set<String> keys = stores.keySet();
		for(String key : keys) {
			StoreObject sObj = stores.get(key);
			if(skipStoreType(storeType, sObj.getSkip())) {
				continue;
			}
			Map<String,Class> storeTypesMap = sObj.getStoreTypes();
			Set<String> stKeys = storeTypesMap.keySet();
			Class clazz = null;
			if(stKeys.contains(storeType)) {
				clazz = storeTypesMap.get(storeType);
			}
			else {
				clazz = sObj.getClazz();
			}
			Store store = (Store) clazz.newInstance();
			store.setCache(sObj.isCache());
			map.put(key, store);
		}
		return map;
	}

	public static boolean skipStoreType(String storeType, String toSkipList) throws Exception {
		StringTokenizer stTok = new StringTokenizer(toSkipList, ",");
		while(stTok.hasMoreTokens()) {
			String token = stTok.nextToken().trim();
			if(token.equals(storeType)) {
				return true;
			}
		}
		return false;
	}

	public static void main(String[] args) throws Exception {
		parseStoreXml("/conf/websheet/stores.xml"); //NO I18N
	}
}
