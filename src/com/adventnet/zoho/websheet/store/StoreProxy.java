/* $Id$ */
package com.adventnet.zoho.websheet.store;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.*;
import java.util.logging.*;

public class StoreProxy implements InvocationHandler {
	private static Logger logger = Logger.getLogger(StoreProxy.class.getName());
	private Map<String,Store> stores = null;
	private StoreProxy(Map<String,Store> stores) {
		this.stores = stores;
	}
	private Long zUID = null;
	private String docOwner = null;
	private String dbSpaceName = null;
	private Long docId = null;
	private Long[] resourceIds = null;
	private int storeType = 0;
	public static Store newStoreProxy(Map<String,Store> stores) throws Exception {
		StoreProxy proxy = new StoreProxy(stores);
		return (Store) Proxy.newProxyInstance(Store.class.getClassLoader(), new Class[] { Store.class } , proxy);
	}
	public Object invoke(Object proxy, Method method, Object[] args) throws Exception {
		//logger.log(Level.INFO,"invoke method is called: "+method.getName());
		String name = method.getName();
		if(name.startsWith("init")) {
			proxyInit(method, args);
		}
		List<String> list = null;
		if(name.startsWith("read")) {
			list = StoreDataUtil.getReadList();
			Iterator<String> iter = list.iterator();
			return proxyRead(method, args, iter.next(), iter);
		}

		if(name.startsWith("write")) {
			list = StoreDataUtil.getWriteList();
			//logger.log(Level.INFO,"getWriteList: "+list);//~commentted by Mani 
			/*//proxyWriteAndDelete(method, args, list);
				return proxyWrite(method, args, list);
			//return null;
			*/
		}else if(name.startsWith("delete") || name.startsWith("trash")) { //No I18N
			list = StoreDataUtil.getDeleteList();
			//proxyWriteAndDelete(method, args, list);
			//return null;
		}/*else if(name.startsWith("isTrashed")) {
			list = StoreDataUtil.getDeleteList();
		}*/else {
			list = StoreDataUtil.getWriteList();
		}

		return proxyOtherMethods(method, args, list);
	}
	private void proxyInit(Method method, Object[] args) throws Exception {
		if(args[0]	instanceof	Long){
			this.zUID = (Long) args[0];
		}	else if(args[0]	instanceof	String)	{
			this.docOwner = (String) args[0];
		}
		this.dbSpaceName =(String)args[1];
		this.docId = (Long) args[2];
		this.resourceIds = (Long[]) args[3];
		this.storeType = (Integer) args[4];
	}
	// store init will be called only when that particular store is invoked.
	private void initStore(Store store) throws Exception {
		if(this.zUID 	!=	null)	{
			store.init(zUID, dbSpaceName,docId, resourceIds, storeType);
		}	else {
			store.init(docOwner,dbSpaceName, docId, resourceIds, storeType);			
		}
	}
	/**
	 * This method is called recursively because the returned result should be stored in cache stores
	 *
	 * @param method
	 * @param args
	 * @param storeName
	 * @param iter
	 * @return
	 * @throws Exception
	 */
	private Object proxyRead(Method method, Object[] args, String storeName, Iterator<String> iter) throws Exception {
//		logger.info("storeName:: "+storeName);
		Store store = stores.get(storeName);
		if(store == null && iter.hasNext()) {
			return proxyRead(method, args, iter.next() , iter);
		}
//		logger.info("store: "+store);
		initStore(store); 
		/*logger.info("method name: "+method.getName()+" args: "+args.length);
		for(int i=0; i<args.length;i++){
			logger.info("ttt___> "+args[i]);
		}*/
		Object result = method.invoke(store, args);
//		byte[] b = null;
		if(result == null && iter.hasNext()) {
			result = proxyRead(method, args, iter.next(), iter);
			/*if(result == null) {
				return null;
			}*/
			/*currently not used ~Mani
			      if(result instanceof InputStream){b = getByteArray((InputStream) result);}
			if(store.isCache()) {
				    if(result instanceof InputStream) {
					ByteArrayInputStream bis = new ByteArrayInputStream(b);
					storeInCache(store, args, bis);
				}else if(result instanceof String) {
					 storeInCache(store, args, (String) result);
				}
			}*/
		}
		/*if(b != null) {
			return new ByteArrayInputStream(b);
		}*/
		return result;
	}
	
	// methods other than read/write/delete are not forwarded to cache stores. the first file store which receives this method will be invoked.
	private Object proxyOtherMethods(Method method, Object[] args, List<String> list) throws Exception {
//		logger.log(Level.INFO,"list: "+list);
		for(String s : list) {
			Store store = stores.get(s);
			if(store == null) {
				continue;
			}
			
			if(!store.isCache()) {
//				logger.log(Level.INFO,"proxyOtherMethods called :"+method.getName());
				initStore(store);
				return method.invoke(store, args);
			}
		}
		return null;
	}
}
	/*Currently not used ~Mani
	 private void proxyWriteAndDelete(Method method, Object[] args, List<String> list) throws Exception {
		Exception exp = null;
		for(String s : list) {
			Store store = stores.get(s);
			if(store == null) {
				continue;
			}

			initStore(store);
			String name = method.getName();
			try {
				// if during a write/delete operation, any exception occurs, the data will be removed from the remaining cache stores
				// if there are any other stores (which are not cache stores), we can skip writing the data.
				if(exp == null) {
					method.invoke(store, args);
				}else if(name.startsWith("write") && exp != null && store.isCache()) {
					deleteFromStore(store, args);
				}

			}catch(Exception e) {
				if(exp == null) {
					exp = e;
				}
				// if the exception occurs during writing the data in a cache, invalidate/delete the cache
				if(name.startsWith("write") && store.isCache()) {
					try {
						deleteFromStore(store, args);
					}catch(Exception e1) {
						//logger.log(Level.SEVERE, "Exception while deleting from the cache store", new String[] { GeneralUtils.getExceptionAsString(e1) } );
					}
				}
			}
		}

		if(exp != null) {
			throw exp;
		}
	}
	 
	 private void deleteFromStore(Store store, Object[] args) throws Exception {
		if(args.length == 4) {
			store.delete((Long) args[0], (String) args[1], (String) args[2]);
		}
		else if(args.length == 3) {
			store.delete((Long) args[0], (String) args[1]);
		}
	} 
	 
	  private void storeInCache(Store store, Object[] args, InputStream result) throws Exception {
		if(args.length == 4) {
			//store.write((Long) args[0], (String) args[1], (String) args[2], result);
		}
		else if(args.length == 3) {
			//store.write((Long) args[0], null, (String) args[1], result);
		}
	}


	private void storeInCache(Store store, Object[] args, String result) throws Exception {
		if(args.length == 4) {
			//store.writeString((Long) args[0], (String) args[1], (String) args[2], result);
		}
		else if(args.length == 3) {
			//store.writeString((Long) args[0], null, (String) args[1], result);
		}
	}

	private byte[] getByteArray(InputStream is) throws Exception {
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		StoreUtil.write(is, bos);
		return bos.toByteArray();
	}*/

/*	public static void main(String[] args) throws Exception {
		Store store = newStoreProxy(new HashMap<String,Store>()) ;
		logger.log(Level.INFO,"store :"+ store);
		logger.log(Level.INFO,"unique key "+ store.getUniqueKey(12121L));
	} */
