/* $Id$ */

package com.adventnet.zoho.websheet.store;

//Java Imports

import com.adventnet.db.persistence.metadata.ColumnDefinition;
import com.adventnet.db.persistence.metadata.util.MetaDataUtil;
import com.adventnet.ds.query.*;
import com.adventnet.iam.User;
import com.adventnet.persistence.*;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.ZSStore.FileName;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import com.zoho.sheet.util.SheetPersistenceUtils;

import java.util.*;
import java.util.logging.Logger;

public class StoreUtil {
	private static Logger logger = Logger.getLogger(StoreUtil.class.getName());
	public static final int DOCUMENTS_STORE = 1;
	public static final int SHEETS_STORE = 2;
	public static final int VERSION_STORE = 3;
	public static final int CHARTS_STORE = 4;
	public static final int IMAGES_STORE = 5;
	public static final int UNDOREDO_STORE = 6;
	public static final int WEBDATA_STORE = 7;
	public static final int USERDATA_STORE = 8;
	public static final int WEBFORMDATA_STORE = 9;
	public static final int SHEETIMAGES_STORE = 10;
	public static final int FRAGMENTSHEET_VERSIONSTORE = 11;

	public static Map<Integer, String> storeTypes = new HashMap<Integer,String>();
	static {
		storeTypes.put(new Integer(StoreUtil.DOCUMENTS_STORE), "documentstore"); // No i18N
		storeTypes.put(new Integer(StoreUtil.SHEETS_STORE), "sheetstore"); // No i18N
		storeTypes.put(new Integer(StoreUtil.VERSION_STORE), "versionstore"); // No i18N
		storeTypes.put(new Integer(StoreUtil.CHARTS_STORE), "chartstore"); // No i18N
		storeTypes.put(new Integer(StoreUtil.IMAGES_STORE), "imagestore"); // No i18N
		storeTypes.put(new Integer(StoreUtil.SHEETIMAGES_STORE), "sheetimagestore"); // No i18N
		storeTypes.put(new Integer(StoreUtil.UNDOREDO_STORE), "undoredostore"); // No i18N
		storeTypes.put(new Integer(StoreUtil.WEBDATA_STORE), "webdatastore"); // No i18N
		storeTypes.put(new Integer(StoreUtil.USERDATA_STORE), "userdatastore"); // No i18N
		storeTypes.put(new Integer(StoreUtil.WEBFORMDATA_STORE), "webformdatastore"); // No i18N
		storeTypes.put(StoreUtil.FRAGMENTSHEET_VERSIONSTORE, "fragmentsheetversionstore"); // No i18N
	}

	/**
	 * @param docId
	 * @param resourceId
	 * @param tableName
	 * @param columnName
	 * @param storeTable
	 * @return
	 * @throws Exception
	 */
	public static DataObject getStoreDataObject(Long docId, String dbSpaceName, Long resourceId, String tableName, String columnName, String storeTable) throws Exception {
		return getStoreDataObject(docId, dbSpaceName, resourceId == null ? null : new Long[] { resourceId} , tableName, columnName, storeTable);
	}
	/**
	 * Gets DataObject  
	 * Used by: AbstractStore
	 * 
	 * @param docId
	 * @param resourceIds
	 * @param tableName
	 * @param columnName
	 * @param storeTable
	 * @return
	 * @throws Exception
	 */
	public static DataObject getStoreDataObject(Long docId, String dbSpaceName, Long[] resourceIds, String tableName, String columnName, String storeTable) throws Exception {
		SelectQuery query = getStoreQuery(docId, resourceIds, tableName, columnName, storeTable);
		//logger.info("query: "+query);
		Persistence persistence = null;
		persistence = SheetPersistenceUtils.getPersistence(dbSpaceName);
		
//		if(docId == null) {
//			persistence = SheetPersistenceUtils.getPersistence(resourceIds[0]);
//		}
//		else {
//			persistence = SheetPersistenceUtils.getPersistence(docId);
//		}
		return persistence.get(query);
	}
	/**
	 * Construct and return the select Query.
	 * Used by here itself. 
	 * 
	 * @param docId
	 * @param resourceIds
	 * @param tableName
	 * @param columnName
	 * @param storeTable
	 * @return
	 * @throws Exception
	 */
	private static SelectQuery getStoreQuery(Long docId, Long[] resourceIds, String tableName, String columnName, String storeTable) throws Exception {
		List tableNames = null;
		boolean[] isLeftJoins = null;

		if(tableName.equalsIgnoreCase(storeTable)) {
			tableNames = Arrays.asList(new String[] { tableName,"UniqueKeys" } ); //No I18N
			isLeftJoins = new boolean[] { false, true } ;
		}else{
			tableNames = Arrays.asList(new String[] { tableName, storeTable, "UniqueKeys" } ); //No I18N
			isLeftJoins = new boolean[] { false, true, true} ;
		}
		Criteria cri = null;
		if(docId != null) {
			cri = getStoreDocCritria(docId, tableName);
		}
		if(resourceIds != null && !tableName.equals("Documents")) {
			if(cri != null) {
				cri = cri.and(new Criteria(Column.getColumn(tableName, columnName), resourceIds, QueryConstants.IN));
			}else{
				cri = new Criteria(Column.getColumn(tableName, columnName), resourceIds, QueryConstants.IN);
			}
		}
		return QueryConstructor.get(tableNames, isLeftJoins, cri);
	}
	/**
	 * Construct the criteria
	 * 
	 * @param docId
	 * @param tableName
	 * @return
	 * @throws Exception
	 */
	private static Criteria getStoreDocCritria(Long docId, String tableName) throws Exception {
		ColumnDefinition col = MetaDataUtil.getTableDefinitionByName(tableName).getColumnDefinitionByName("DOCUMENT_ID"); //NO I18N
		if(col != null) {
			Criteria cri = new Criteria(Column.getColumn(tableName, "DOCUMENT_ID"), docId, QueryConstants.EQUAL);
			return cri;
		}
		return null;
	}
	
	
	public static Criteria getStoreCriteria(Object[] resourceId, String storeTable, String columnName) throws Exception {
		Criteria cri = new Criteria(Column.getColumn(storeTable, columnName), resourceId, QueryConstants.IN);
		return cri;
	}
	
	/**
	 * Construct and returns the criteria for storeTable. 
	 * Used by: DFSStore in deleteFromDB
	 * 
	 * @param resourceId
	 * @param fileType
	 * @param fileExtn
	 * @param storeTable
	 * @param columnName
	 * @return
	 * @throws Exception
	 */
	public static Criteria getStoreCriteria(Long resourceId, String fileType, String fileExtn, String storeTable, String columnName) throws Exception {
		Criteria cri = new Criteria(Column.getColumn(storeTable, columnName), resourceId, QueryConstants.EQUAL);
		ColumnDefinition col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("FILE_TYPE"); //NO I18N
		if(col != null && fileType != null) {
			cri = cri.and(new Criteria(Column.getColumn(storeTable, "FILE_TYPE"), fileType, QueryConstants.EQUAL));
		}
		col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("FILE_EXTN"); //NO I18N
		if(col != null && fileExtn != null) {
			cri = cri.and(new Criteria(Column.getColumn(storeTable, "FILE_EXTN"), fileExtn, QueryConstants.EQUAL));
		}
		return cri;
	}
	/**
	 * Generate [initial generation] and returns the UniqueKey. 
	 * 
	 * @param user
	 * @return
	 * @throws Exception
	 */
	public static Long getUniqueKeyId(User user) throws Exception {
		String authorName = user.getLoginName();
		Row rw = generateUniqueKey(authorName, null);
		return (Long) rw.get("UNIQUE_KEY_ID");
	}
	/**
	 * Generate [initial generation] and returns the UniqueKey. 
	 * 
	 * @param authorName
	 * @return
	 * @throws Exception
	 */
//	public static Long getUniqueKeyId(String authorName) throws Exception {
//		Row rw = generateUniqueKey(authorName);
//		return (Long) rw.get("UNIQUE_KEY_ID");
//	}
	
	public static Long getUniqueKeyId(String authorName, String authorZuid) throws Exception {
		Row rw = generateUniqueKey(authorName, authorZuid);
		return (Long) rw.get("UNIQUE_KEY_ID");
	}
	public static Long getUniqueKeyId(String authorName, Long authorZuid) throws Exception {
		if(authorZuid == null){
			authorZuid = Constants.DEFAULT_ZUID;
		}
		Row rw = generateUniqueKey(authorName, Long.toString(authorZuid));
		return (Long) rw.get("UNIQUE_KEY_ID");
	}
//	public static Row generateUniqueKey(String authorName) throws Exception {
//        Row rw = generateUniqueKey(authorName, null);
//		return rw;
//	}
	
	/**
	 * Generate [initial generation] and returns the UniqueKey.	
	 * 
	 * @param authorName
	 * @return
	 * @throws Exception
	 */
        public static Row generateUniqueKey(String authorName, String authorZuid) throws Exception {
		//String authorName = user.getLoginName();
		Persistence persistence = SheetPersistenceUtils.getPersistence(authorName);
		Row rw = null;
		Long defaultZuid = Constants.DEFAULT_ZUID;//data-dictionary default ZUID value
		while(true) {
//			if((authorZuid == null ||  authorZuid.equals(defaultZuid.toString())) && (!authorName.equalsIgnoreCase(Constants.PUBLIC_USER))){
//				try{
//					authorZuid = Long.toString(DocumentUtils.getZUID(authorName));
//				}catch(Exception e){
//					authorZuid = Long.toString(defaultZuid);
//					logger.log(Level.INFO,"ZUID Changes:Could not create UniqueKey for account :"+authorName);
//				}
//			}
			if(authorZuid == null){
				authorZuid = Long.toString(defaultZuid);
			}
			/*}else if(authorZuid.equalsIgnoreCase(Constants.PUBLIC_USER) || authorZuid.equalsIgnoreCase(Constants.PUBLIC_SPACE)){
				authorZuid = defaultZuid.toString();
			}*/
			String uniqueKey = UUID.randomUUID().toString();
			DataObject data = persistence.constructDataObject();
			rw = new Row("UniqueKeys"); //No I18N
			rw.set("UNIQUE_KEY", uniqueKey); //No I18N
			rw.set("AUTHOR_NAME", authorName); //No I18N
			 rw.set("AUTHOR_ZUID", authorZuid); //No I18N
			try {
				data.addRow(rw);
				persistence.add(data);
			}
			catch(DataAccessException e) {
				logger.info("Duplicate Key might be generated ! " + uniqueKey + " So retrying !" );
				//e.printStackTrace();
				continue;
			}
			break;
		}
		return rw;
	}
	/**
	 * Gets the UniqueKey from the database. 
	 * 
	 * @param resourceId
	 * @param tableName
	 * @param columnName
	 * @return
	 * @throws Exception
	 */
	public static String getUniqueKey(Long resourceId, String dbSpaceName, String tableName, String columnName) throws Exception {
		Row rw = getUniqueKeyData(resourceId, dbSpaceName, tableName, columnName);
		if(rw == null) {
			return null;
		}
		return (String) rw.get("UNIQUE_KEY");
	}
	/**
	 * Fetch the row from table {UniqueKeys}.
	 * 
	 * @param resourceId
	 * @param tableName
	 * @param columnName
	 * @return
	 * @throws Exception
	 */
	private static Row getUniqueKeyData(Long resourceId, String dbSpaceName, String tableName, String columnName) throws Exception {
		List<String> tableNames = Arrays.asList( new String[] { tableName, "UniqueKeys" } ); //No I18N
		SelectQuery query = QueryConstructor.get(tableNames, new Criteria(Column.getColumn(tableName, columnName), resourceId, QueryConstants.EQUAL));
		return getRow(query, dbSpaceName, "UniqueKeys"); //No I18N
	}
	/**
	 * Fetch the row
	 * 
	 * @param query
	 * @param tableName
	 * @param dbSpace
	 * @return
	 * @throws Exception
	 */
	private static Row getRow(SelectQuery query, String dbSpaceName, String tableName) throws Exception {
		Persistence persistence = SheetPersistenceUtils.getPersistence(dbSpaceName);
		DataObject data = persistence.get(query);
		if(data.isEmpty()) {
			return null;
		}
		return data.getFirstRow(tableName);
	}
	/**
	 * Add or Update the blockid in database.
	 * This method is used by DFSStore
	 * 
	 * @param blockId
	 * @param existingBlockId
	 * @param resourceId
	 * @param fileType
	 * @param fileExtn
	 * @param tableName
	 * @param storeTable
	 * @param columnName
	 * @param delTime
	 * @throws Exception
	 */
	public static void addOrUpdateBlockId(String blockId, String dbSpaceName, String existingBlockId, Long resourceId, Object authorZuid, String fileType, String fileExtn, Long fileSize, String tableName, String storeTable, String columnName ,Long delTime) throws Exception {
		Persistence persistence = SheetPersistenceUtils.getPersistence(dbSpaceName);
		boolean isEncryptionEnabled = isEncryption(authorZuid);
		if (existingBlockId != null && existingBlockId.equalsIgnoreCase(blockId) && delTime == -1) {
			//logger.log(Level.INFO, "Data is written in the same block Id. Not necessary to update the DB. Existing Block Id {0}", new String[] { existingBlockId } );
			UpdateQuery query =  new UpdateQueryImpl(storeTable);
			Criteria cri = getStoreCriteria(resourceId, fileType, fileExtn, storeTable, columnName);
			query.setCriteria(cri);
			ColumnDefinition col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("LAST_MODIFIED_TIME");//NO I18N
			if (col != null) {
				query.setUpdateColumn("LAST_MODIFIED_TIME", System.currentTimeMillis());//NO I18N
				query.setUpdateColumn("IS_ENCRYPTED", isEncryptionEnabled);//NO I18N
			}
			col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("FILE_SIZE"); //NO I18N
			if(col != null && fileSize != -1L) {
				query.setUpdateColumn("FILE_SIZE", fileSize); //No I18N
			}
			if(!query.getUpdateColumns().isEmpty())
			{
				persistence.update(query);
			}
			return;
		}
		if(existingBlockId == null && !tableName.equalsIgnoreCase(storeTable)) {
			DataObject data = persistence.constructDataObject();
			Row rw = new Row(storeTable);
			rw.set("BLOCK_ID", blockId); //No I18N
			rw.set(columnName, resourceId);
			ColumnDefinition col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("FILE_TYPE"); //NO I18N
			if(col != null) {
				if(fileType == null) {
					fileType = "";
					//logger.log(Level.INFO, "fillType is null , therefore replaced by empty string");
					//logger.log(Level.INFO,"fileType" + fileType);
				}
				rw.set("FILE_TYPE", fileType); //No I18N
			}
			col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("FILE_EXTN"); //NO I18N
			if(col != null && fileExtn != null) {
				rw.set("FILE_EXTN", fileExtn); //No I18N
			}
			col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("FILE_SIZE"); //NO I18N
			if(col != null && fileSize != -1L) {
				rw.set("FILE_SIZE", fileSize); //No I18N
			}
			col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("LAST_MODIFIED_TIME"); //NO I18N
			if(col != null) {
				long currentTime = System.currentTimeMillis();
				rw.set("LAST_MODIFIED_TIME", currentTime);  // No i18N
			}
			rw.set("IS_ENCRYPTED", isEncryptionEnabled); // No i18N
			data.addRow(rw);
			persistence.add(data);
		}else{

			
			UpdateQuery query = new UpdateQueryImpl(storeTable);
			Criteria cri = getStoreCriteria(resourceId, fileType, fileExtn, storeTable, columnName);
			query.setCriteria(cri);

			if (existingBlockId != null){
				
				if(blockId==null || "".equals(blockId.trim())) {
					logger.info("blockId error | returned as empty resourceId : "+resourceId+" Existing blockid :"+existingBlockId);
				}else {
					query.setUpdateColumn("BLOCK_ID", blockId); //No I18N
				}
				
			}else
			{
				query.setUpdateColumn("BLOCK_ID", blockId); //No I18N
			}
			
			ColumnDefinition col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("DEL_TIME"); //NO I18N
			if(col != null && delTime != null) {
				query.setUpdateColumn("DEL_TIME", new Long(-1)); //No I18N
			}
			col = MetaDataUtil.getTableDefinitionByName(storeTable).getColumnDefinitionByName("FILE_SIZE"); //NO I18N
			if(col != null && fileSize != -1L) {
				query.setUpdateColumn("FILE_SIZE", fileSize); //No I18N
			}
			query.setUpdateColumn("IS_ENCRYPTED", isEncryptionEnabled);//NO I18N
			persistence.update(query);
		}
	}
	/**
	 * Construct the file name. 
	 * Called by : DFSStore
	 * 
	 * @param uniqueKey
	 * @param fileType
	 * @param fileExtn
	 * @return
	 */
	public static String constructPath(String uniqueKey, String fileType, String fileExtn) {
		StringBuffer stBuf = new StringBuffer();
		/* "sheet_" is appended to distinguish the file between various services, if we move to ZFS.
		 **/
		stBuf.append("SHEET_"); // No I18N
		stBuf.append(uniqueKey);
		if (fileType != null && !fileType.equals("")) {
			stBuf.append("_"); // No I18N
			stBuf.append(fileType);
		}
		stBuf.append("."); // No I18N
		stBuf.append(fileExtn);
		return stBuf.toString();
	}
	public static void setDRWrite(int storeType, String fileType, SheetFileInfo info) {
		FileName name = null;
		switch(storeType){
		case 2:
			if(fileType==null){
				return;
			}else if (fileType.equals(FileName.ACTIONSTEMP.toString().toLowerCase())) {
				name = FileName.ACTIONSTEMP;
			} 
			break;
		case 4://undo Redo
			name = FileName.UNDOREDO;
			break;
		case 6:
			name = FileName.CHART;
			break;
		default:
				return;
		}
		if(name!=null){
			setDRWrite(name, info);
		}
	}

	public static void setDRWrite(FileName name, SheetFileInfo info) {
		if(info==null){
			return;
		}
		switch(name){
		case ACTIONSTEMP:
		case UNDOREDO:
		//case CACHE:
		//case CHART:
			info.setdrWrite(false);
			break;
			default:
				
		}
		
	}
	
	public static boolean isEncryption(Object authorZuid) throws Exception{
		try{
			boolean isEncryptionEnabled = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("EAR_ENCRYPTION"));	//NO I18N
			String encryptionZuid = EnginePropertyUtil.getSheetPropertyValue("EAR_ENABLE_ZUID").toString();	//NO I18N
			String[] aa	 = encryptionZuid.split(",");
			if(isEncryptionEnabled || Arrays.asList(aa).contains(authorZuid.toString())){
				return true;
			}else{
				return false;
			}
		}catch(Exception e){
			return false;
		}
	}
	
	
}


/*Currently following methods are not used in Zoho Sheet, it might be used in future ~Mani

public static SelectQuery getStoreQuery(Long docId, Long resourceId, String tableName, String columnName, String storeTable) throws Exception {
		return getStoreQuery(docId, new Long[] { resourceId } , tableName, columnName, storeTable);
	}
public static OutputStream write(InputStream is , OutputStream os) throws Exception {
		BufferedInputStream bis = null;
		try {
			int BUF_LEN = 8192;
			byte [] bufr = new byte [BUF_LEN];
			int c=0;
			bis = new BufferedInputStream(is);
			while((c=bis.read(bufr,0,BUF_LEN)) != -1) {
				os.write(bufr,0,c);
			}
		}
		catch(Exception e) {
			e.printStackTrace();
			throw e;
		}
		return os;
		finally {
			try {
				if(is != null) {
					is.close();
				}
			}
			catch(Exception e) {
				e.printStackTrace();
				throw e;
			}

			try {
				if(bis != null) {
					bis.close();
				}
			}
			catch(Exception e) {
				e.printStackTrace();
				throw e;
			}

			try {
				if(os != null) {
					os.close();
				}
			}catch(Exception e) {
				e.printStackTrace();
				throw e;
			}
		}
	}
***
public static String getImageExtn(String imageName) {
		int index = imageName.lastIndexOf(".");
		String imgExtn = null;
		if(index > -1) {
			imgExtn = imageName.substring(index+1,imageName.length());
		}
		return imgExtn;
	}
****
public static Row generateUniqueKey(String authorName) throws Exception {

		//String authorName = user.getLoginName();

		Persistence persistence = SheetPersistenceUtils.getPersistence(authorName);
		Row rw = null;
		while(true) {
			String uniqueKey = UUID.randomUUID().toString();

			DataObject data = persistence.constructDataObject();

			rw = new Row("UniqueKeys");
			rw.set("UNIQUE_KEY", uniqueKey);
			rw.set("AUTHOR_NAME", authorName);

			try {
				data.addRow(rw);

				persistence.add(data);
			}
			catch(DataAccessException e) {
				logger.info("Duplicate Key might be generated ! " + uniqueKey + " So retrying !" );
				e.printStackTrace();
				continue;
			}
			break;
		}
		return rw;
	}
***
public static void addUniqueKeyId(Long uniqueKeyId, Long resourceId, String tableName, String columnName) throws Exception {
Criteria cri = new Criteria(Column.getColumn(tableName, columnName), resourceId, QueryConstants.EQUAL);
HashMap<String,Object> map = new HashMap();
map.put("UNIQUE_KEY_ID", uniqueKeyId); // No I18N
QueryUtil.update(tableName, cri, map, resourceId);
}

public static String getBlockId(Long resourceId, String fileType, String fileExtn, String storeTable, String columnName) throws Exception {
Criteria cri = getStoreCriteria(resourceId, fileType, fileExtn, storeTable, columnName);

return (String) QueryUtil.getValue(storeTable, cri, "BLOCK_ID", resourceId);
}
****
public static String getContent(InputStream is) throws Exception {

		if(is == null) {
			return null;
		}

		BufferedReader in = null;
		StringBuffer stBuf = new StringBuffer();
		try {
			in = new BufferedReader(new InputStreamReader(is, "UTF-8")); // No I18N

			String token = "";
			while((token=in.readLine()) != null)
			{
				stBuf.append(token);
				stBuf.append("\n");
			}

		}catch(Exception e) {
			e.printStackTrace();
			throw e;
		}
		finally {
			if (in != null) {
				try { in.close(); } catch(Exception e) { e.printStackTrace(); }
			}
			if(is != null) {
				try { is.close(); } catch(Exception e) { e.printStackTrace(); }
			}
		}

		return stBuf.toString();
	}
	****
	public static Long getUniqueKeyId(String authorName) throws Exception {
		Row rw = generateUniqueKey(authorName);
		return (Long) rw.get("UNIQUE_KEY_ID");
	}
*/
