/* $Id$ */
package com.adventnet.zoho.websheet.store;

import java.util.*;
import java.util.logging.*;
import com.adventnet.ds.query.*;
import com.adventnet.persistence.*;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
public abstract class AbstractStore implements Store {
	private static Logger logger = Logger.getLogger(AbstractStore.class.getName());
	public static HashMap<Integer,String> defaultFileExtns = new HashMap<Integer,String>();
	static {
		//defaultFileExtns.put(new Integer(StoreUtil.VERSION_STORE), "ods"); // No I18N
	}
	protected String docOwner;
	protected String dbSpaceName;
	protected Long zUID;
	protected Long docId;
	/* tableName - this is the table that refers to the UniqueKeys table */
	protected String tableName;
	/* columnName - the primary key column of the tableName. */
	protected String columnName;
	protected int storeType;
	protected String storeTable;
	protected DataObject data = null;
	protected boolean initialized = false;
	public AbstractStore() {
	}
	public void init(Object zUID, String dbSpaceName, Long docId, Long[] resourceIds, int storeType) throws Exception {
		if(initialized) {
			return;
		}
		
		if(zUID	instanceof	Long)	{
			this.zUID	=	(Long)zUID;	
			if(this.zUID < 0) {throw (new Exception("Invalid ZUID"));}	//No I18N
		}	else if(zUID	instanceof	String)	{
//			Remote Imports
//			logger.info("AbstractStore EAR Exceptions, This ZUID is a String, Follow the trace and change this to Long.");
//			StackTraceElement[] elements = Thread.currentThread().getStackTrace();
//			DocumentUtils.printStackTrace(elements);
			this.docOwner	=	(String)zUID;
			this.zUID	=	-1L;
		}	else {
			throw (new Exception("Invalid ZUID"));	//No I18N
		}
		this.dbSpaceName = dbSpaceName;
		this.docId = docId;
		this.storeType = storeType;
		String sT = StoreUtil.storeTypes.get(new Integer(this.storeType));
		StoreDataUtil.StoreType sObj = StoreDataUtil.getStoreType(sT);
		this.tableName = sObj.getTableName();
		this.columnName = sObj.getColumnName();
		this.storeTable = sObj.getStoreTable();
		/*logger.info("zUID: "+zUID);
		logger.info("docId: "+docId);
		logger.info("tableName: "+tableName);
		logger.info("columnName: "+columnName);
		logger.info("storeTable: "+storeTable);
		logger.info("resourceIds: "+resourceIds[0]);
		logger.info("storeType: "+storeType);*/
		
		if(resourceIds != null && resourceIds.length >0 ) {
			this.data = getResourceInfo(resourceIds);
		}else {
			this.data = getResourcesInfo();
		}
//		logger.info("this.data: "+this.data);
		this.initialized = true;
	}

	protected DataObject getResourceInfo(Long[] resourceIds) throws Exception {
		return StoreUtil.getStoreDataObject(docId, dbSpaceName, resourceIds, tableName, columnName, storeTable);
	}
	protected DataObject getResourceInfo(Long resourceId) throws Exception {
		return StoreUtil.getStoreDataObject(docId, dbSpaceName, resourceId, tableName, columnName, storeTable);
	}
	protected DataObject getResourcesInfo() throws Exception {
		return StoreUtil.getStoreDataObject(docId, dbSpaceName, (Long) null, tableName, columnName, storeTable);
	}
	public String getUniqueKey(Long resourceId) throws Exception {
		Criteria cri = new Criteria(Column.getColumn(tableName, columnName), resourceId, QueryConstants.EQUAL);
		Row rw = data.getRow(tableName, cri);
		Long uniqueKeyId = (Long) rw.get("UniqueKeys");
		cri = new Criteria(Column.getColumn("UniqueKeys", "UNIQUE_KEY"), uniqueKeyId, QueryConstants.EQUAL);
		Row ukRow = data.getRow("UniqueKeys", cri); //No I18N
		return (String) ukRow.get("UNIQUE_KEY");
	}
	protected boolean cache = false;
	public void setCache(boolean cache) {
		this.cache = cache;
	}
	public boolean isCache() {
		return cache;
	}
}
