/* $Id$ */
package com.adventnet.zoho.websheet.store;
//JAVA Imports
import java.util.Map;
import java.util.logging.Logger;

import com.singularsys.jep.functions.Str;

/**
 * <AUTHOR>
 * 
 */
public class StoreFactory {
	private static Logger logger = Logger.getLogger(StoreFactory.class.getName());
	public static StoreFactory factory = null;
	public static StoreFactory getInstance() {
		if(factory == null) {
			factory = new StoreFactory();
		}
		return factory;
	}
//	public Store getStore(Object zUID, Long docId, Long resourceId, int storeType) throws Exception {
//		return getStore(zUID, docId, new Long[] {  resourceId } , storeType);
//	}

	public Store getStore(Object zUID, String docOwner, Long docId, Long resourceId, int storeType) throws Exception {
		return getStore(zUID, docOwner, docId, new Long[] {  resourceId } , storeType);
	}

	public Store getStore(Object zUID, String docOwner, Long docId, Long[] resourceIds, int storeType) throws Exception {
		Map<String,Store> stores = StoreDataUtil.getAvailableStores(StoreUtil.storeTypes.get(new Integer(storeType)));
		Store store = null;
		store = StoreProxy.newStoreProxy(stores);
//		store.init(zUID, docOwner, docId, resourceIds, storeType);
		store.init(zUID, docOwner, docId, resourceIds, storeType);
		return store;
	}
	
//	public Store getStore(Object zUID, Long docId, Long[] resourceIds, int storeType) throws Exception {
//		Map<String,Store> stores = StoreDataUtil.getAvailableStores(StoreUtil.storeTypes.get(new Integer(storeType)));
//		Store store = null;
//		store = StoreProxy.newStoreProxy(stores);
////		store.init(zUID, docOwner, docId, resourceIds, storeType);
//		store.init(zUID, zUID, docId, resourceIds, storeType);
//		return store;
//	}
	/**Gets store object for DOCUMENT [ods] related operation #1
	 * 
	 * @param zUID
	 * @param docId
	 * @return
	 * @throws Exception
	 */
	public Store getDocumentStore(Object zUID, String docOwner, Long docId) throws Exception {
		return getStore(zUID, docOwner, docId, docId, StoreUtil.DOCUMENTS_STORE);
	}
	/**Gets store object for DOCUMENT [ods] related operation #1
	 * 
	 * @param docOwner
	 * @param docId
	 * @return
	 * @throws Exception
	 */
	public Store getDocumentStore(String docOwner, Long docId) throws Exception {
		return getStore(docOwner,  docOwner, docId, new Long[] {  docId } , StoreUtil.DOCUMENTS_STORE);
	}
	/**Gets store object for SHEET [fragment files] related operation  #2
	 * 
	 * @param zUID
	 * @param docId
	 * @param sheetId
	 * @return
	 * @throws Exception
	 * 
	 */
	public Store getSheetStore(Object zUID, String docOwner, Long docId, Long sheetId) throws Exception {
		return getStore(zUID, docOwner, docId, sheetId, StoreUtil.SHEETS_STORE);
	}
	
	/**Gets store object for SHEET [fragment files] related operation  #2
	 * 
	 * @param zUID
	 * @param docId
	 * @param sheetId[]
	 * @return
	 * @throws Exception
	 * 
	 */
	public Store getSheetStore(Object zUID, String docOwner, Long docId, Long[] sheetIds) throws Exception {
		return getStore(zUID, docOwner, docId, sheetIds, StoreUtil.SHEETS_STORE);
	}
	/**Gets store object for VERSION related operation  #3
	 * 
	 * @param zUID
	 * @param docId
	 * @param versionId
	 * @return
	 * @throws Exception
	 */
	public Store getVersionStore(Object zUID, String docOwner, Long docId, Long versionId) throws Exception {
		return getStore(zUID, docOwner,docId, versionId, StoreUtil.VERSION_STORE);
	}
	/**Gets store object for VERSION related operation  #3
	 * 
	 * @param zUID
	 * @param docId
	 * @return
	 * @throws Exception
	 */
	public Store getVersionStore(Object zUID, String docOwner, Long versionId) throws Exception {
		return getStore(zUID, docOwner, null, versionId,  StoreUtil.VERSION_STORE);
	}
	/**Gets store object for CHART related operation #4
	 * 
	 * @param zUID
	 * @param chartId
	 * @return
	 * @throws Exception
	 */
	public Store getChartStore(Object zUID, String dbSpaceName, Long chartId) throws Exception {
		return getStore(zUID, dbSpaceName, null, chartId, StoreUtil.CHARTS_STORE);
	}
	/**Gets store object for CHART related operation #4
	 * @param zUID
	 * @param chartIds
	 * @return
	 * @throws Exception
	 */
	public Store getChartStore(Object zUID,  String dbSpaceName, Long[] chartIds) throws Exception {
		return getStore(zUID, dbSpaceName, null, chartIds, StoreUtil.CHARTS_STORE);
	}
	/**
	 * Gets store object for IMAGE related operation #5
	 * 
	 * @param zUID
	 * @param imageId
	 * @return
	 * @throws Exception
	 */
	public Store getImageStore(Object zUID, String docOwner, Long imageId) throws Exception {
		return getImageStore(zUID, docOwner, null, imageId);
	}
	/**IMAGE Stores #5**/
	public Store getImageStore(Object zUID, String docOwner, Long docId, Long imageId) throws Exception {
		return getStore(zUID, docOwner, docId, imageId, StoreUtil.IMAGES_STORE);
	}
	public Store getSheetImageStore(Object zUID, String docOwner, Long imageId) throws Exception {
		return getSheetImageStore(zUID, docOwner, null, imageId);
	}
	/**IMAGE Stores #5**/
	public Store getSheetImageStore(Object zUID, String docOwner, Long docId, Long imageId) throws Exception {
		return getStore(zUID, docOwner,docId, imageId, StoreUtil.SHEETIMAGES_STORE);
	}
	/**IMAGE Stores #5**/
	public Store getImageStore(Object zUID, String docOwner, Long[] imageIds) throws Exception {
		return getImageStore(zUID, docOwner, null, imageIds);
	}
	/**IMAGE Stores #5**/
	public Store getImageStore(Object zUID, String docOwner, Long docId, Long[] imageIds) throws Exception {
		return getStore(zUID, docOwner, docId, imageIds, StoreUtil.IMAGES_STORE);
	}
	/**Gets store object for UNDO/REDO related operation #6
	 * 
	 * @param zUID
	 * @param docId
	 * @param undoRedoId
	 * @return
	 * @throws Exception
	 */
	public Store getUndoRedoStore(Object zUID, String docOwner, Long docId, Long undoRedoId) throws Exception {
		return getStore(zUID, docOwner, docId, undoRedoId, StoreUtil.UNDOREDO_STORE);
	}
	/**Gets store object for UNDO/REDO related operation #6
	 * 
	 * @param zUID
	 * @param docId
	 * @param undoRedoIds[]
	 * @return
	 * @throws Exception
	 * 
	 */
	public Store getUndoRedoStore(Object zUID, String docOwner, Long docId, Long[] undoRedoIds) throws Exception {
		return getStore(zUID, docOwner, docId, undoRedoIds, StoreUtil.UNDOREDO_STORE);
	}
	/**Gets store object for WEBDATA related operation #7
	 * 
	 * @param zUID
	 * @param docId
	 * @param webdataId
	 * @return
	 * @throws Exception
	 */
	public Store getWebDataStore(Object zUID, String docOwner, Long docId, Long webdataId) throws Exception {
		return getStore(zUID, docOwner, docId, webdataId, StoreUtil.WEBDATA_STORE);
	}
	/**Gets store object for USERDATA related operation #8
	 * 
	 * @param zUID
	 * @param userdataId
	 * @return
	 * @throws Exception
	 */
	public Store getUserStore(Object zUID, String docOwner, Long userdataId) throws Exception {
		return getStore(zUID, docOwner, null, userdataId, StoreUtil.USERDATA_STORE);
	}
	/**Gets store object for WEBDATA related operation #9
	 * 
	 * @param zUID
	 * @param docId
	 * @param webFormdataId
	 * @return
	 * @throws Exception
	 */
	public Store getWebFormDataStore(Object zUID, String docOwner,  Long docId, Long webFormdataId) throws Exception {
		return getStore(zUID, docOwner, docId, webFormdataId, StoreUtil.WEBFORMDATA_STORE);
	}
}
