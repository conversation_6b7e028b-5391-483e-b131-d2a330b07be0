//$Id$
package com.adventnet.zoho.websheet.store.fs;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;



import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQuery;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.Table;
import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.User;
import com.adventnet.iam.UserAPI;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.zoho.websheet.store.AbstractStore;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.StoreUtil;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;

import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.zoho.sheet.util.SheetPersistenceUtils;
import java.util.List;
//import com.adventnet.zoho.websheet.beans.OpenDocBean;
/**
*
* <AUTHOR>
*/
public class SheetFileStore extends AbstractStore implements Store {
	private static Logger logger = Logger.getLogger(SheetFileStore.class.getName());

	public InputStream read(Long resourceId, String fileExtn) throws Exception {
		return read(resourceId, null, fileExtn);
	}

	public InputStream read(Long resourceId, String fileType, String fileExtn) throws Exception {
		logger.log(Level.INFO, "Read FileStore:{0} {1} {2}", new String[] { "" + docId, fileType, fileExtn });
	/*	
		FilePathHandler pathHandler = new FilePathHandlerFactory().getFilePathHandler(storeType);
		String filePath = pathHandler.getFilePath(resourceId, fileType, fileExtn);
		logger.info("File path:: "+filePath);
		try{
			long accountId = getAccountId(getUserName(zUID));

			StoreBean bean = null;
			if(docId != null)
			{
				bean = (StoreBean)BeanUtil.lookup("StoreBean", docId);
			}
			else
			{
				bean = (StoreBean) BeanUtil.lookup("StoreBean",  resourceId);
			}
			InputStream stream = bean.getReadStream(String.valueOf(accountId), filePath);
			if ("xml".equals(fileExtn)) {
				return getInputStream(stream, fileType + "." + fileExtn);
			} else {
				return stream;
			}	
		}catch(Exception e){
			logger.info("Error occured while reading from filestore");
			e.printStackTrace();
			return null;
		}
		*/
		return null;
	}

	public HashMap write(Long resourceId, SheetFileInfo info) throws Exception {
		return null;
	}

	public void finishWrite(HashMap writeInfo) throws Exception {
	}

	public void delete(Long resourceId, String fileExtn) throws Exception {
		delete(resourceId, null, fileExtn);
	}

	public void delete(Long resourceId, String fileType, String fileExtn) throws Exception {
	}

	public void delete(Long[] resourceId, String fileType, String fileExtn) throws Exception {
	}

	public boolean exists(Long resourceId, String fileExtn) throws Exception {
		return exists(resourceId, null, fileExtn);
	}

	public boolean exists(Long resourceId, String fileType, String fileExtn) throws Exception {
		//Yet to implement ~Mani
		return false;
	}

	public SheetFileInfo createFileInfo(Long resourceId, String fileType, String fileExtn, Long fileSize) throws Exception {
		return null;
	}

	public SheetFileInfo createFileInfo(Long resourceId, String fileType, String fileExtn) throws Exception {
		return null;
	}

	public SheetFileInfo getFileInfo(Long resourceId, String fileType, String fileExtn) throws Exception {
		return null;
	}
        
        @Override
        public List<SheetFileInfo> getAvailableFileInfos() {
            return null;
        }

	private InputStream getInputStream(InputStream stream, String fileName) throws IOException {
		if (fileName == null) {
			fileName = "content.xml";//No I18N
		}
		ZipInputStream zin = new ZipInputStream(stream);
		ZipEntry zEntry;
		while ((zEntry = zin.getNextEntry()) != null) {
			if (fileName.equals(zEntry.getName())) {
				return zin;
			}
		}
		return null;
	}

	

	private class FilePathHandlerFactory {
		public FilePathHandler getFilePathHandler(int storeType) throws Exception {
			
			switch (storeType) {
			case StoreUtil.DOCUMENTS_STORE: //1
				 logger.log(Level.INFO,"DOCUMENTS_STORE: "+"storeType {0}... "+storeType);
				return new DocumentsFilePathHandler();
			case StoreUtil.SHEETS_STORE: //2
				logger.log(Level.INFO,"SHEETS_STORE: "+"storeType {0}... "+storeType);
				return new SheetFilePathHandler();
			case StoreUtil.VERSION_STORE: //3
				logger.log(Level.INFO,"VERSION_STORE: "+"storeType {0}... "+storeType);
				return new DocumentVersionFilePathHandler();
			case StoreUtil.CHARTS_STORE: //4
				logger.log(Level.INFO,"CHARTS_STORE: "+"storeType {0}... "+storeType);
				return new ChartFilePathHandler();
			case StoreUtil.IMAGES_STORE: //5
				logger.log(Level.INFO,"IMAGES_STORE: "+"storeType {0}... "+storeType);
				return new ImageFilePathHandler();
			
			default:
				logger.log(Level.INFO,"UNKNOWN_STORE: need to define~Mani "+"storeType {0}... "+storeType);
				return null;
			}
		}
	}

	private interface FilePathHandler {
		public String getFilePath(Long resourceId, String fileType, String fileExtn) throws Exception;
		// public String[] getFilePaths(Long resourceId) throws Exception;
	}

	private class DocumentsFilePathHandler implements FilePathHandler {
		public String getFilePath(Long resourceId, String fileType, String fileExtn) throws Exception {
			if(!fileExtn.startsWith(".")) {
				fileExtn = "." + fileExtn;
			}
			String documentPath = EngineConstants.LOCATION + docId + fileExtn;
			return documentPath;
		}
	}

	private class SheetFilePathHandler implements FilePathHandler {
		public String getFilePath(Long resourceId, String fileType, String fileExtn) throws Exception {
			if ("xml".equals(fileExtn)) {
				fileExtn = "zip";//No I18N
			}
			return getSheetPath(fileType, fileExtn);
		}
	}
	private class DocumentVersionFilePathHandler implements FilePathHandler {
		public String getFilePath(Long resourceId, String fileType, String fileExtn) throws Exception {
			double versionNo = getVersionNo(zUID, resourceId);
			String fileName = docId + "_" + versionNo + "." + fileExtn;
			//---FilePath /7000000000098/version/7000000000098_1.5.ods
			String docVersionPath = EngineConstants.LOCATION + docId + EngineConstants.LOCATION + "version" +EngineConstants.LOCATION + fileName; //No I18N
			return docVersionPath;
		}
	}
	
	private class ImageFilePathHandler implements FilePathHandler {
		public String getFilePath(Long resourceId, String fileType, String fileExtn) throws Exception {
			String imgPath = null;
			String imgValue = resourceId + "."+ fileExtn;
			if(fileType.equals(EngineConstants.IMAGE)) {
				imgPath = EngineConstants.LOCATION + "images" + EngineConstants.LOCATION + imgValue; // No I18N	
			} else if(fileType.equals(EngineConstants.THUMBNAIL)){
				imgPath = EngineConstants.LOCATION + "images" + EngineConstants.LOCATION + "thumbnails" +  EngineConstants.LOCATION + imgValue; // No I18N	
			}
			
			return imgPath;
		}
	}
	private class ChartFilePathHandler implements FilePathHandler {
		public String getFilePath(Long resourceId, String fileType, String fileExtn) throws Exception {
			String chartImgValue = resourceId + "."+ fileExtn;
			String chartImgPath = EngineConstants.LOCATION + "Charts" + EngineConstants.LOCATION + chartImgValue; // No I18N
			return chartImgPath;
		}
	}
	
	  public static String getUserName(long zuid) {
          try {
                  IAMProxy proxy = IAMProxy.getInstance();
                  UserAPI userAPI = proxy.getUserAPI();
                  User user = new User();
                  user = userAPI.getUserFromZUID(String.valueOf(zuid));
                  return user.getLoginName();
          } catch (Exception e) {
			   logger.log(Level.WARNING,null,e);

          }
          return "";
	  }
	  /**
	 * @param fileType
	 * @param fileExtn
	 * @return
	 * @throws Exception
	 */
	private String getSheetPath(String fileType, String fileExtn) throws Exception {
			String fileName = fileType + "." + fileExtn;// hardcoded by Mani .. Need to test completed
			String filePath = null;
			if(fileType.equals(EngineConstants.FILENAME_CACHE)){
				filePath = EngineConstants.LOCATION + docId + EngineConstants.LOCATION + fileType + EngineConstants.LOCATION + fileName;
			} else{
				filePath = EngineConstants.LOCATION + docId + EngineConstants.LOCATION + fileName; // No I18N
			}
			return filePath;
		}
	  /**
	 * @param zuid
	 * @return
	 * Used for getting version number
	 */
	public static double getVersionNo(long zUID, long versionId) {
		String docOwner = getUserName(zUID);
		double version_no = -1;
		try {
			Persistence persistence = SheetPersistenceUtils.getPersistence( docOwner);
			SelectQuery sql = null;
			sql = new SelectQueryImpl(new Table("DocumentVersion"));
			sql.addSelectColumn(new Column(null, "*"));
			Criteria cri = new Criteria(new Column("DocumentVersion", "VERSION_ID"), versionId, QueryConstants.EQUAL);
			sql.setCriteria(cri);
			DataObject dataObject = persistence.get(sql);
//			logger.info("dataObject: " + dataObject);
			if (!dataObject.isEmpty()) {
				version_no = (Double)dataObject.getFirstValue("DocumentVersion", "VERSION"); //No I18N
			}
		} catch (Exception e) {
			logger.log(Level.WARNING, null, e);
		}
		return version_no;
	}
	  

}
