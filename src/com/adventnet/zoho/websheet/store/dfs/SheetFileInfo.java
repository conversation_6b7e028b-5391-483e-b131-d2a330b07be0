/* $Id$ */
package com.adventnet.zoho.websheet.store.dfs;

public class SheetFileInfo extends com.adventnet.dfs.FileInfo {
	private String fileType;
	private String fileExtn;
	private String uniqueKey;
	private Long resourceId;
	private Long delTime = -1L;
	private Long lastModifiedTime = -1L;
	private boolean drWrite = true;
	private boolean isEncrypted = false;
	private Long fileSize = -1L;
	

	public boolean isEncrypted() {
		return isEncrypted;
	}

	public void setEncrypted(boolean isEncrypted) {
		this.isEncrypted = isEncrypted;
	}

	/**
	 * Set uniqueKey.
	 *
	 * @param uniqueKey the value to set.
	 */
	public void setUniqueKey(String uniqueKey)
	{
		this.uniqueKey = uniqueKey;
	}

	/**
	 * Get uniqueKey.
	 *
	 * @return uniqueKey as String.
	 */
	public String getUniqueKey()
	{
		return uniqueKey;
	}

	/**
	 * Set fileExtn.
	 *
	 * @param fileExtn the value to set.
	 */
	public void setFileExtn(String fileExtn)
	{
		this.fileExtn = fileExtn;
	}

	/**
	 * Get fileExtn.
	 *
	 * @return fileExtn as String.
	 */
	public String getFileExtn()
	{
		return fileExtn;
	}

	/**
	 * Set fileType.
	 *
	 * @param fileType the value to set.
	 */
	public void setFileType(String fileType)
	{
		this.fileType = fileType;
	}

	public void setResourceId(Long id)
	{
		this.resourceId = id;
	}

	/**
	 * Get fileType.
	 *
	 * @return fileType as String.
	 */
	public String getFileType()
	{
		return fileType;
	}


	public Long getResourceId()
	{
		return resourceId;
	}
	/**
	 * Set delTime.
	 *
	 * @param delTime the value to set.
	 */
	public void setDelTime(Long delTime)
	{
		this.delTime = delTime;
	}

	/**
	 * Get delTime.
	 *
	 * @return delTime as Long.
	 */
	public Long getDelTime()
	{
		return delTime;
	}
	/**
	 * Set lastModifiedTime.
	 *
	 * @param lastModifiedTime the value to set.
	 */
	public void setLastModifiedTime(Long lastModifiedTime)
	{
		this.lastModifiedTime = lastModifiedTime;
	}

	/**
	 * Get delTime.
	 *
	 * @return lastModifiedTime as Long.
	 */
	public Long getLastModifiedTime()
	{
		return lastModifiedTime;
	}

	/**
	 * Get drWrite.
	 *
	 * @return drWrite as Boolean.
	 */
	public boolean getdrWrite()
	{
		return drWrite;
	}
	/**
	 * Set drWrite.
	 *
	 * @param drWrite the value to set.
	 */
	public void setdrWrite(boolean drWrite)
	{
		this.drWrite = drWrite;
	}

	public Long getFileSize()
	{
		return fileSize;
	}

	public void setFileSize(Long fileSize)
	{
		this.fileSize = fileSize;
	}
}
