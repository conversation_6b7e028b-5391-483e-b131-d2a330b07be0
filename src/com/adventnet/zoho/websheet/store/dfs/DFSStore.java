/* $Id$ */
package com.adventnet.zoho.websheet.store.dfs;
//Java Imports

import com.adventnet.db.persistence.metadata.ColumnDefinition;
import com.adventnet.db.persistence.metadata.util.MetaDataUtil;
import com.adventnet.dfs.*;
import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.store.AbstractStore;
import com.adventnet.zoho.websheet.store.StoreUtil;
import com.google.common.collect.ImmutableList;
import com.zoho.ear.fileencryptagent.FileEncryptAgent;
import com.zoho.logs.common.util.logging.OnDemandSearchLogger;
import com.zoho.sheet.util.SheetPersistenceUtils;

import java.io.InputStream;
import java.io.OutputStream;
import java.text.MessageFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
public class DFSStore extends AbstractStore {
    private static Logger logger = Logger.getLogger(DFSStore.class.getName());
    private static String refreshToken = EnginePropertyUtil.getSheetPropertyValue("EAR_REFRESH_TOKEN");	//No I18N
    //private static boolean isEncryptionEnabled = Boolean.parseBoolean(EnginePropertyUtil.getSheetPropertyValue("EAR_ENCRYPTION"));	//NO I18N

    private static String isCNSetup = EnginePropertyUtil.getSheetPropertyValue("IS_CN_SETUP");	//No I18N
    //private static long docOwnerZOID = IAMUtil.getCurrentUser().getZUID();
    protected HashMap<Long, List<SheetFileInfo>> map = null;
    public DFSStore() {
    }
    /*public void init(Long zUID, Long docId, Long resourceId, int storeType) throws Exception {
        this.init(zUID, docId, new Long[] { resourceId  } , storeType);
    }*/
    public void init(Object zUID, String docOwner, Long docId, Long[] resourceIds, int storeType) throws Exception {
        super.init(zUID, docOwner, docId, resourceIds, storeType);
        this.map = convertToFileInfo(this.data);
    }
    /*public void init(Long zUID, Long docId, Long[] resourceIds, int storeType) throws Exception {
        super.init(zUID, docId, resourceIds, storeType);
        this.map = convertToFileInfo(this.data);
    }*/
    public String getUniqueKey(Long resourceId) throws Exception {
        List<SheetFileInfo> list = map.get(resourceId);
        if(list == null || list.size() == 0) {
            return null;
        }
        Iterator<SheetFileInfo> iter = list.iterator();
        SheetFileInfo info = iter.next();
        return info.getUniqueKey();
    }
    /**READ Methods implementations*/
    public InputStream read(Long resourceId, String fileExtn) throws Exception {
        return read(resourceId, null,fileExtn);
    }
    public InputStream read(Long resourceId, String fileType, String fileExtn) throws Exception {
        if(fileExtn!=null && fileExtn.startsWith(".")){
            fileExtn = fileExtn.substring(1);
        }
        SheetFileInfo info = getFileInfo(resourceId, fileType, fileExtn);
        if(info == null) {
            logger.log(Level.SEVERE,"Content null in DFS resourceId {0} fileType {1} in fileExtn {2} ", new String[] { ""+resourceId, fileType, fileExtn });
            return null;
        }else {
            return read(resourceId, info);
        }
    }
    private InputStream read(Long resourceId, SheetFileInfo info) throws Exception {//Finally this method only will be called. ~Mani
//        logger.log(Level.SEVERE,"Read {0} {1} {2} {3} {4}",new String[]{info.getFileType(), info.getFileExtn(),resourceId.toString(), info.getBlockId(),info.getPath()});
        /* If del time is greater than -1, then the file is trashed. so return null */
        long startTime = System.currentTimeMillis();
        long dfsClientTime = 0, dfsReadTime = 0, dfsDecryptedTime = 0;
        boolean isEncrypted = info.isEncrypted();
    	if(info.getDelTime()  > -1L) {
            //logger.log(Level.INFO,"read method .........** exit......");
            return null;
        }
        /* Read the file from DFS */
        DFSClient dfsClient = null;
        if(zUID>0){
            dfsClient = DFSClientPool.getDFSClient(""+zUID, "zfs"); //No I18N
            //isEncrypted = ZSStore.isEncrypted(""+zUID, storeTable, resourceId);
        }else if(docOwner!=null){
            dfsClient = DFSClientPool.getDFSClient(docOwner, "zfs"); //No I18N
            //isEncrypted = ZSStore.isEncrypted(docOwner, storeTable, resourceId);
        }
        dfsClientTime = System.currentTimeMillis() - startTime;
        InputStream is = null;
        try {
		String blockId = info.getBlockId();
		if("true".equals(isCNSetup) && blockId.startsWith("NN11:")) {
			blockId = "NN1:"+ blockId.split(":")[1]; //No I18N
		}
            startTime = System.currentTimeMillis();
            is = dfsClient.read(info.getPath(), blockId, true);
            dfsReadTime = System.currentTimeMillis() - startTime;
            startTime = System.currentTimeMillis();
		if(isEncrypted){
        		if(zUID>0){
        			is = FileEncryptAgent.getInstance().getDecryptInputStream(zUID, is, false, refreshToken);
        		}else if(docOwner!=null){
        			is = FileEncryptAgent.getInstance().getDecryptInputStream(docOwner, is, false, refreshToken);
        		}
        	}

            dfsDecryptedTime = System.currentTimeMillis() - startTime;
        }catch (DFSException dfse) {
        	logger.log(Level.INFO, "[DFS][Exception] DFSStore DFS Exceptions ZUID:{0} docOwner {1} Exception : {2}", new Object[]{zUID, docOwner, dfse});
        }catch(DFSIOException dfsio) {
        	logger.log(Level.INFO, "[DFS][Exception] DFSStore DFSIO Exceptions ZUID:{0} docOwner {1} Exception : {2}", new Object[]{zUID, docOwner, dfsio});
        }
        catch(Exception e) {
        	logger.log(Level.INFO, "[DFS][Exception] DFSStore EAR Exceptions ZUID:{0} docOwner {1} Exception : {2} This ZUID may be a String, Follow the trace and change this to Long.", new Object[]{zUID, docOwner, e});
			//StackTraceElement[] elements = Thread.currentThread().getStackTrace();
			DocumentUtils.printingStackTrace();
            //logger.log(Level.SEVERE,"DFS read exception occured and read from filestore , resourceId {0}  "+resourceId+" info: "+info);
            //MailUtil.reportError(docId.toString(), "Error in DFS READ", e); //No I18N
        }finally {
            if(dfsClient != null) {
                DFSClientPool.returnDFSClient(dfsClient);
            }
        }
        OnDemandSearchLogger.log(logger, Level.INFO,"[DFS][TimeStats][READ] DFS Client {0} ms {1} \n\nRead {2} ms {3} \n\nDecrypted {4} ms {5} \n\n", // No I18N
            new Object[]{dfsClientTime, getTimeStamp(dfsClientTime),
            dfsReadTime, getTimeStamp(dfsReadTime),
            dfsDecryptedTime, getTimeStamp(dfsDecryptedTime)
        });
        return is;
    }


    /**Write Methods implementations*/
    public HashMap<String, Object> write(Long resourceId, SheetFileInfo info) throws Exception {

        long startTime = System.currentTimeMillis();
        long dfsClientTime = 0, dfsOverwriteTime = 0, dfsWriteTime = 0, dfsEncryptedTime = 0;
        boolean isEncryptionEnabled = StoreUtil.isEncryption(zUID.toString());
    	//long docOwnerZOID = IAMUtil.getCurrentUser().getZUID();

        short replicationCount = 3;
        DFSClient dfsClient = null;
        String existingBlockId = null;
        String path = null;
        try {
            String fileType = info.getFileType();
            String fileExtn = info.getFileExtn();
        //    Long delTime = info.getDelTime();
        //    ByteArrayOutputStream bos = new ByteArrayOutputStream();
        //    StoreUtil.write(is, bos);
        //    byte[] b = bos.toByteArray();

            path = StoreUtil.constructPath(info.getUniqueKey(), fileType, fileExtn);
            //logger.log(Level.INFO,"Content to be written in {0} for {1} in table {2} ", new String[] { path, ""+resourceId, tableName });

            /* Write the file to DFS */
            //ByteArrayInputStream bis = new ByteArrayInputStream(b);
            logger.info("zuid ..."+zUID);
//            logger.info("docOwner ..."+docOwner);
            if(zUID>0){
                dfsClient = DFSClientPool.getDFSClient(""+zUID, "zfs"); //No I18N
            }else if(docOwner!=null){
                dfsClient = DFSClientPool.getDFSClient(docOwner, "zfs"); //No I18N

            }
            dfsClientTime = System.currentTimeMillis() - startTime;
            OutputStream os = null;
            existingBlockId = info.getBlockId();
            //logger.info("existingBlockId: "+existingBlockId);
            if(existingBlockId != null) {
                try {
                    startTime = System.currentTimeMillis();
                    os = dfsClient.overwrite(path, existingBlockId, replicationCount, true, info.getdrWrite());
                    dfsOverwriteTime = System.currentTimeMillis() - startTime;
//                    os = dfsClient.writeFileForBlock(path, existingBlockId, true, true, info.getdrWrite());
                }catch(DFSException dfse) {
                    logger.log(Level.WARNING, "[DFS][Exception] [DFSException] 111 Error Code {0}  Error Message {1} path: {2} existingBlockId: {3}", new Object[]{ dfse.getErrorCode(), dfse.getMessage(), path, existingBlockId });
                    if(dfse.getMessage().contains("AlreadyBeingCreatedException") || dfse.getErrorCode()==DFSConstants.WRITE_OPERATION_FAILED) {
                        logger.log(Level.WARNING, "[DFS][Exception] [DFSException] 11 abandonFileWrite path: {0} existingBlockId: {1} newBlockId {2} ", new Object[]{path, existingBlockId, dfsClient.getBlockId() });
                        dfsClient.abandonFileWrite(path, existingBlockId);
                    }
                    //logger.log(Level.INFO,"DFSException Writing to the existing block id {0} failed. Trying to write in a different block id ", new String[] { existingBlockId } );
                    startTime = System.currentTimeMillis();

                    os = dfsClient.write(path ,true, true, info.getdrWrite());

                    dfsWriteTime = System.currentTimeMillis() - startTime;
                }catch(Exception re) {
                    logger.log(Level.WARNING, "[DFS][Exception] [DFSException]  Exception Writing to the existing block id {0} failed. Error Message {1} path: {2}. Trying to write in a different block id", new Object[]{existingBlockId, re.getMessage(), path});
                    startTime = System.currentTimeMillis();

                    os = dfsClient.write(path ,true, true, info.getdrWrite());

                    dfsWriteTime = System.currentTimeMillis() - startTime;
                }
            }
            else {
                startTime = System.currentTimeMillis();
                os = dfsClient.write(path, true, true, info.getdrWrite());
                dfsWriteTime = System.currentTimeMillis() - startTime;
            }
            //EAR Encryption Changes TODO: If odd exceptions, check if ZUID and DOCOWNER are same with different data type in read and write methods. 
            OutputStream eos = null;
            startTime = System.currentTimeMillis();
            if(isEncryptionEnabled){
            	if(zUID>0){
            		eos = FileEncryptAgent.getInstance().getEncryptOutputStream(zUID, os,false,refreshToken);
                }else if(docOwner!=null){
                	eos = FileEncryptAgent.getInstance().getEncryptOutputStream(docOwner, os,false,refreshToken);
                }
            }
            dfsEncryptedTime = System.currentTimeMillis() - startTime;
            //StoreUtil.write(bis, os);
            HashMap<String, Object> writeInfo = new HashMap<String, Object>();
            if(isEncryptionEnabled){
            	writeInfo.put("OS",eos);//writing encrpted os
            }else{
            	writeInfo.put("OS",os);//writing encrpted os
            }
            writeInfo.put("DFSClient",dfsClient);
            writeInfo.put("SheetFileInfo",info);
            writeInfo.put("resourceId",resourceId);
            writeInfo.put("path",path);
            writeInfo.put("existingBlockId",existingBlockId);
            /////////
            writeInfo.put("tableName", tableName);
            writeInfo.put("storeTable", storeTable);
            writeInfo.put("columnName", columnName);
            writeInfo.put("authorZuid", zUID);
            writeInfo.put("dbSpaceName", dbSpaceName);

            OnDemandSearchLogger.log(logger, Level.INFO,"[DFS][TimeStats][WRITE] DFS Client {0} ms {1} \n\nOverwrite {2} ms {3} \n\nWrite {4} ms {5} \n\nEncrypted {6} ms {7}",   // No I18N
                new Object[]{dfsClientTime, getTimeStamp(dfsClientTime),
                dfsOverwriteTime, getTimeStamp(dfsOverwriteTime),
                dfsWriteTime, getTimeStamp(dfsWriteTime),
                dfsEncryptedTime, getTimeStamp(dfsEncryptedTime)
            });


            return writeInfo;
        }
        catch(DFSException dfse) {
            logger.log(Level.WARNING, "[DFS][Exception][DFSException] 222 Writing Error Code {0}  Error Message {1} info: {2} path: {3} existingBlockId: {4} newBlockId {5}", new Object[]{ dfse.getErrorCode(), dfse.getMessage(), info, path, existingBlockId, dfsClient.getBlockId() });
            if(dfse.getMessage().contains("AlreadyBeingCreatedException")|| dfse.getErrorCode()==DFSConstants.WRITE_OPERATION_FAILED) {
                logger.log(Level.WARNING, "[DFS][Exception][DFSException] 22 abandonFileWrite path: {0} existingBlockId: {1} newBlockId {2} ", new Object[] { path, existingBlockId, dfsClient.getBlockId() });
                dfsClient.abandonFileWrite(path, existingBlockId);
            }
            if(dfsClient != null) {
                DFSClientPool.returnDFSClient(dfsClient);
            }
            //dfse.printStackTrace();
            //MailUtil.reportError(resourceId.toString(), "Error in DFS WRITE", dfse); //No I18N
            throw dfse;
        }
        catch(Exception e) {
            logger.log(Level.WARNING, "[DFS][Exception] Exception while writing..  Error Message {0} info: {1} path: {2} existingBlockId: {3}", new Object[] { e.getMessage(), info, path, existingBlockId });
            if(dfsClient != null) {
                DFSClientPool.returnDFSClient(dfsClient);
            }
            //e.printStackTrace();
            //MailUtil.reportError(resourceId.toString(), "Error in DFS WRITE", e); //No I18N
            throw e;
        }
    }
    public void finishWrite(HashMap<String, Object> writeInfo) throws Exception
    {
    	finishWrite1(writeInfo);
    }
    public static void finishWrite1(Map<String, Object> writeInfo) throws Exception
    {
    	OutputStream os = (OutputStream)writeInfo.get("OS");
        DFSClient dfsClient = (DFSClient)writeInfo.get("DFSClient");
        SheetFileInfo info = (SheetFileInfo)writeInfo.get("SheetFileInfo");
        Long resourceId = (Long)writeInfo.get("resourceId");
        Boolean fileWritten = true;
        if(writeInfo.get("fileWritten")!=null){
            fileWritten = (Boolean)writeInfo.get("fileWritten");
        }

        ///////////////////
        String tableName = (String)writeInfo.get("tableName");
        String storeTable = (String)writeInfo.get("storeTable");
        String columnName = (String)writeInfo.get("columnName");
        Object authorZuid = (Long)writeInfo.get("authorZuid");
        String dbSpaceName = (String)writeInfo.get("dbSpaceName");
        String fileType = info.getFileType();
        String fileExtn = info.getFileExtn();
        String existingBlockId = info.getBlockId();
        Long fileSize = info.getFileSize();

        try {
            if(os != null) {
                os.close();
            }
            if(fileWritten) {
                Long delTime = info.getDelTime();
                String blockId = dfsClient.getBlockId();
                StoreUtil.addOrUpdateBlockId(blockId, dbSpaceName, existingBlockId, resourceId, authorZuid, fileType, fileExtn, fileSize, tableName, storeTable, columnName, delTime);
            }
        } catch(Exception exception) {
            String path = (String) writeInfo.get("path");
            logger.log(Level.WARNING, MessageFormat.format("DFSException while write {0} {1} {2} {3} {4}", info.getFileType(), info.getFileExtn(), resourceId, info.getBlockId(), path), exception);

            if(path != null) {
                try {
                    dfsClient.abandonFileWrite(path, existingBlockId);
                } catch(Exception e) {
                    logger.log(Level.WARNING, MessageFormat.format("DFSException while abandonFileWrite {0} {1} {2} {3} {4}", info.getFileType(), info.getFileExtn(), resourceId, info.getBlockId(), path), e);
                    path = StoreUtil.constructPath(info.getUniqueKey(), fileType, fileExtn);
                    dfsClient.abandonFileWrite(path, existingBlockId);
                }
            } else {
                path = StoreUtil.constructPath(info.getUniqueKey(), fileType, fileExtn);
                dfsClient.abandonFileWrite(path, existingBlockId);
            }
        } finally {
            if(dfsClient != null) {
                DFSClientPool.returnDFSClient(dfsClient);
            }
        }
    }
    /**Delete Methods implementations*/
    public void delete(Long[] resourceIds, String fileType, String fileExtn) throws Exception {
        //logger.log(Level.INFO,"bulk delete: "+resourceId.length);
    	try {
    	SheetFileInfo[] infoArray = new SheetFileInfo[resourceIds.length];
        List<String> blockIdList = new ArrayList<String>();
        List<SheetFileInfo> sheetInfoList = new ArrayList<SheetFileInfo>();
        for(int i=0; i<resourceIds.length;i++){
        	SheetFileInfo info = getFileInfo(resourceIds[i], fileType, fileExtn);
        	if(info == null) {
        		continue;
        	}
        	sheetInfoList.add(info);
        	if(!blockIdList.contains(info.getBlockId())) {
        		blockIdList.add(info.getBlockId());
        	}
        }
        Iterator<String> listIterator = blockIdList.iterator();
        while(listIterator.hasNext()) {
        	String blockId = listIterator.next();
        	List<Long> fileIdList = new ArrayList<Long>();
        	List<String> filePathList = new ArrayList<String>();
        	Iterator<SheetFileInfo> fileInfoIterator = sheetInfoList.iterator();
        	 while(fileInfoIterator.hasNext()) {
        		SheetFileInfo sfi = fileInfoIterator.next();
            	if(blockId.equals(sfi.getBlockId())) {
            		filePathList.add(sfi.getPath());
            		fileIdList.add(sfi.getResourceId());
            	}
            }
        	 logger.log(Level.SEVERE,"Delete {0} >>>> {1} >>>> {2}",new String[]{fileIdList.toString(), filePathList.toString(), blockId});
        	deleteFromStore(filePathList, blockId);
        	deleteFromDB(fileIdList.toArray());
        }
    	}catch(Exception ex) {
    		logger.log(Level.WARNING, "DFSException:: ", ex);
    	}
    }
    public void delete(Long resourceId, String fileExtn) throws Exception {
        delete(resourceId, null, fileExtn);
    }
    public void delete(Long resourceId, String fileType, String fileExtn) throws Exception {
        SheetFileInfo info = getFileInfo(resourceId, fileType, fileExtn);
        if(info != null) {
            //StoreUtil.setDRWrite(this.storeType, fileType, info);
            delete(resourceId, info);
        }
    }
    private void delete(Long resourceId, SheetFileInfo info) throws Exception {
        logger.log(Level.SEVERE,"Delete {0} {1} {2} {3} {4}",new String[]{info.getFileType(), info.getFileExtn(),resourceId.toString(), info.getBlockId(),info.getPath()});
        deleteFromStore(resourceId, info, true);
        deleteFromDB(resourceId, info);
    }

    private void deleteFromDB(Object[] resourceIds) throws Exception {
        /* Delete from DB */
        Criteria cri = null;
        cri = StoreUtil.getStoreCriteria(resourceIds,storeTable, columnName);
        Persistence persistence = SheetPersistenceUtils.getPersistence(dbSpaceName);
        persistence.delete(cri);
    }

    private void deleteFromDB(Long resourceId, SheetFileInfo info) throws Exception {
        /* Delete from DB */
        Criteria cri = null;
        if(info != null) {
            cri = StoreUtil.getStoreCriteria(resourceId, info.getFileType(), info.getFileExtn(), storeTable, columnName);
        }else{
            cri = StoreUtil.getStoreCriteria(resourceId, null, null, storeTable, columnName);
        }
        Persistence persistence = SheetPersistenceUtils.getPersistence(dbSpaceName);
        persistence.delete(cri);
    }

    private void deleteFromStore(List<String> filePathList, String blockId) throws Exception {
        DFSClient dfsClient = null;
        try {
                if(zUID>0){
                    dfsClient = DFSClientPool.getDFSClient(""+zUID, "zfs"); //No I18N
                }else if(docOwner!=null){
                    dfsClient = DFSClientPool.getDFSClient(docOwner, "zfs"); //No I18N
                }
                dfsClient.delete(filePathList, blockId);

        }catch(DFSException dfse) {
            //dfse.printStackTrace();
            logger.log(Level.SEVERE,"DFSException Delete {0} {1} ",new String[]{filePathList.toString(), blockId});
            throw dfse;
        }catch(Exception e) {
            //e.printStackTrace();
        	logger.log(Level.SEVERE,"Exception Delete {0} {1} ",new String[]{filePathList.toString(), blockId});
            throw e;
        }
        finally {
            if(dfsClient != null) {
                DFSClientPool.returnDFSClient(dfsClient);
            }
        }
    }

    private void deleteFromStore(Long resourceId, SheetFileInfo info, boolean deleteIndividualFiles) throws Exception {
        DFSClient dfsClient = null;
        try {
            if(info.getBlockId() != null) {
                if(zUID>0){
                    dfsClient = DFSClientPool.getDFSClient(""+zUID, "zfs"); //No I18N
                }else if(docOwner!=null){
                    dfsClient = DFSClientPool.getDFSClient(docOwner, "zfs"); //No I18N
                }
                dfsClient.delete(info.getPath(), info.getBlockId());
                //dfsClient.delete(info.getPath(), info.getBlockId(), info.getdrWrite());
            }
        }catch(DFSException dfse) {
            //dfse.printStackTrace();
            logger.log(Level.SEVERE,"DFSException Delete {0} {1} {2} {3} {4}",new String[]{info.getFileType(), info.getFileExtn(),resourceId.toString(), info.getBlockId(),info.getPath()});
            throw dfse;
        }catch(Exception e) {
            //e.printStackTrace();
            logger.log(Level.SEVERE,"Exception Delete {0} {1} {2} {3} {4}",new String[]{info.getFileType(), info.getFileExtn(),resourceId.toString(), info.getBlockId(),info.getPath()});
            throw e;
        }
        finally {
            if(dfsClient != null) {
                DFSClientPool.returnDFSClient(dfsClient);
            }
        }
    }

    public SheetFileInfo createFileInfo(Long resourceId, String fileType, String fileExtn, Long fileSize) throws Exception {
        String uniqueKey = StoreUtil.getUniqueKey(resourceId, dbSpaceName, tableName, columnName);
        if(uniqueKey == null) {
            throw new Exception("Unique key not generated");
        }
        SheetFileInfo info = new SheetFileInfo();
        info.setFileType(fileType);
        info.setFileExtn(fileExtn);
        info.setUniqueKey(uniqueKey);
        info.setResourceId(resourceId);
        info.setFileSize(fileSize);
        String path = StoreUtil.constructPath(uniqueKey, fileType, fileExtn);
        info.setPath(path);
        logger.log(Level.INFO,"createFileInfo::: SheetFileInfo: {0} {1}", new Object[]{info, info.getPath()});
        return info;
    }

    public SheetFileInfo createFileInfo(Long resourceId, String fileType, String fileExtn) throws Exception
    {
        return createFileInfo(resourceId, fileType, fileExtn, -1L);
    }

    public SheetFileInfo getFileInfo(Long resourceId, String fileType, String fileExtn) throws Exception {
        //logger.log(Level.INFO,"resourceId: "+resourceId+" fileType: "+fileType+" fileExtn:"+fileExtn);
        //Integer isT = new Integer(storeType);
//        if(fileExtn == null) {
//            fileExtn = defaultFileExtns.get(isT);
//        }
        if(map == null) {
            logger.log(Level.INFO,"*** MAP IS NULL ***"+"resourceId: "+resourceId+" fileType: "+fileType+" fileExtn:"+fileExtn);
            return null;
        }
        List<SheetFileInfo> list = map.get(resourceId);
        if(list == null) {
            logger.log(Level.INFO,"*** LIST IS NULL ***"+"resourceId: "+resourceId+" fileType: "+fileType+" fileExtn:"+fileExtn);
            return null;
        }
        //logger.info("List Size: "+list.size());
        Iterator<SheetFileInfo> iter = list.iterator();
        while(iter.hasNext()) {
            SheetFileInfo info = iter.next();
            //logger.log(Level.INFO,"resourceId: "+info.getResourceId()+" fileType: "+info.getFileType()+" fileExtn:"+info.getFileExtn());
            if (info.getBlockId() != null
                    && ((fileExtn == null || fileExtn.equalsIgnoreCase(info.getFileExtn())) && (fileType == null || fileType.equalsIgnoreCase(info
                            .getFileType())))) {
            //if(info.getBlockId() != null && ((fileType == null || fileType.equalsIgnoreCase(info.getFileType())) && (fileType == null || fileType.equalsIgnoreCase(info.getFileType())))) { // writen by mani need to be considered EXTENSION
                logger.log(Level.INFO,"SheetFileInfo :: resourceId: {0} fileType: {1} fileExtn: {2} info: {3}", new Object[]{resourceId, fileType, fileExtn, info});
                return info;
            }
        }
        return null;
    }

    @Override
    public ImmutableList<SheetFileInfo> getAvailableFileInfos() {
        return ImmutableList.copyOf(map.values().stream().flatMap(sheetFileInfos -> sheetFileInfos.stream()).collect(Collectors.toList()));
    }

    public void trash(Long resourceId, String fileType, String fileExtn) throws Exception {
        /*Have to be implemented ~Mani
         * SheetFileInfo info = getFileInfo(resourceId, fileType, fileExtn);
         update DEL_TIME 
        if(info!=null) {
            Map<String,Object> values = new HashMap<String,Object>();
            values.put("DEL_TIME", System.currentTimeMillis());
            Criteria cri = null;//StoreUtil.getStoreCriteria(resourceId, info.getFileType(), info.getFileExtn(), storeTable, columnName); 
            //QueryUtil.update(storeTable, cri, values, resourceId);
        }*/
    }
    public boolean isTrashed(Long resourceId, String fileType, String fileExtn) throws Exception {
        /*Have to be implemented ~Mani
         * //check for DEL_TIME
        SheetFileInfo info = getFileInfo(resourceId, fileType, fileExtn);
        if(info != null){
            if(info.getDelTime() > -1) {
                return true;
            }
        }*/
        return false;
    }
    public boolean exists(Long resourceId, String fileExtn) throws Exception {
        return exists(resourceId, null, fileExtn);
    }
    public boolean exists(Long resourceId, String fileType, String fileExtn) throws Exception {
        boolean isExist = false;
        DFSClient dfsClient = null;
        try{
            SheetFileInfo info = getFileInfo(resourceId, fileType, fileExtn);
            /* Read the file from DFS */
            if(info!=null){
                if(zUID>0){
                    dfsClient = DFSClientPool.getDFSClient(""+zUID, "zfs"); //No I18N
                }else if(docOwner!=null){
                    dfsClient = DFSClientPool.getDFSClient(docOwner, "zfs"); //No I18N
                }
                isExist = dfsClient.exists(info.getPath(), info.getBlockId());
            }
        }catch(Exception e) {
            e.printStackTrace();
            throw e;
        }
        finally {
            if(dfsClient != null) {
                DFSClientPool.returnDFSClient(dfsClient);
            }
        }
        return isExist;
    }
    /**DFS Specific Methods*/
    protected HashMap<Long, List<SheetFileInfo>> convertToFileInfo(DataObject data) throws Exception {

        HashMap<Long, List<SheetFileInfo>> fileInfos = new HashMap<Long, List<SheetFileInfo>>();
        Iterator<Row> iter = data.getRows(tableName);
        while(iter.hasNext()) {
            Row rw = (Row) iter.next();
            Long resId = (Long) rw.get(columnName);
            Long uniqueKeyId = (Long) rw.get("UNIQUE_KEY_ID"); // No I18N
            Iterator iter1 = null;
            if(!storeTable.equalsIgnoreCase(tableName)) {
                Criteria cri = new Criteria(new Column(storeTable, columnName), resId, QueryConstants.EQUAL);
                iter1 = data.getRows(storeTable, cri);
            }
            Criteria cri1 = new Criteria(new Column("UniqueKeys", "UNIQUE_KEY_ID"), uniqueKeyId, QueryConstants.EQUAL);
            Row uniqueKeyRow = data.getRow("UniqueKeys", cri1); //No I18N
            if(uniqueKeyRow != null) {
                String uKey = (String) uniqueKeyRow.get("UNIQUE_KEY");
                List<SheetFileInfo> list = convertToFileInfo(rw, iter1, uKey);
                fileInfos.put(resId, list);
            }
        }
        return fileInfos;
    }
    private List<SheetFileInfo> convertToFileInfo(Row rw, Iterator iter, String uKey) throws Exception {
        Vector<SheetFileInfo> list = new Vector();
        if(iter == null) {
            list.add(convertToFileInfo(rw, uKey));
            return list;
        }
        while(iter.hasNext()) {
            Row iterRow = (Row) iter.next();
            SheetFileInfo info = convertToFileInfo(iterRow, uKey);
            list.add(info);
        }
        return list;
    }
    private SheetFileInfo convertToFileInfo(Row rw, String uKey) throws Exception {
        SheetFileInfo info = new SheetFileInfo();
        String tableName = rw.getTableName();
        ColumnDefinition _col = MetaDataUtil.getTableDefinitionByName(tableName).getColumnDefinitionByName("FILE_TYPE"); //NO I18N
        if(_col != null) {
            String fileType = (String) rw.get("FILE_TYPE");
            info.setFileType(fileType); // No I18N
        }
        _col = MetaDataUtil.getTableDefinitionByName(tableName).getColumnDefinitionByName("FILE_EXTN"); //NO I18N
        if(_col != null) {
            String fileExtn = (String) rw.get("FILE_EXTN");
            info.setFileExtn(fileExtn); // No I18N
        }
        _col = MetaDataUtil.getTableDefinitionByName(tableName).getColumnDefinitionByName("DEL_TIME"); //NO I18N
        if(_col != null) {
            Long delTime = (Long) rw.get("DEL_TIME");
            info.setDelTime(delTime); // No I18N
        }
        _col = MetaDataUtil.getTableDefinitionByName(tableName).getColumnDefinitionByName("LAST_MODIFIED_TIME"); //NO I18N
        if(_col != null) {
            Long lastModifiedTime = (Long) rw.get("LAST_MODIFIED_TIME");
            info.setLastModifiedTime(lastModifiedTime); // No I18N
        }
        _col = MetaDataUtil.getTableDefinitionByName(tableName).getColumnDefinitionByName(this.columnName); //NO I18N
        if(_col != null) {
            Long resourceId = (Long) rw.get(this.columnName);
            info.setResourceId(resourceId); // No I18N
        }
        _col = MetaDataUtil.getTableDefinitionByName(tableName).getColumnDefinitionByName("IS_ENCRYPTED");	//NO I18N
        if(_col != null) {
            boolean isEncrypted = (boolean) rw.get("IS_ENCRYPTED");
            //info.setResourceId(resourceId); // No I18N
            info.setEncrypted(isEncrypted);
        }
        info.setBlockId((String) rw.get("BLOCK_ID")); // No I18N
        info.setUniqueKey(uKey);
        info.setPath(StoreUtil.constructPath(uKey, info.getFileType(), info.getFileExtn()));
        return info;
    }

    private String getTimeStamp(long time)
    {
        return time > 1000 ? ( ( time > 3000 ? ( time > 5000 ? "GT_5": "GT_3") : "GT_1" ) ) : "LT_1"; // No I18N
    }
}
