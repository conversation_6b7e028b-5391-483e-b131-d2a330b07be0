/* $Id$ */

package com.adventnet.zoho.websheet.store;

import java.io.*;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import java.util.HashMap;
import java.util.List;

public interface Store {
	
	/**
	 * 
	 * Set the required parameters.
	 * @param zUID
	 * @param docOwner
	 * @param docId
	 * @param resourceIds
	 * @param storeType
	 * @throws Exception
	 */
	public void init(Object zUID, String spaceName, Long docId, Long[] resourceIds, int storeType) throws Exception;
	/**
	 * Reads the content of a resource with extn (fileExtn) from the store. If the resource is a document, then the resource id refers to the document id.
	 * For example, {document_id}.ods where ods is the fileExtn, will fetch the ods contents of the document.
	 *
	 * @param resourceId unique indentifier of the resource
	 * @param fileExtn file extension
	 * @return
	 * @throws Exception
	 */
	public InputStream read(Long resourceId, String fileExtn) throws Exception;
	/**
	 * Reads the content of a resource with file type and file extension from the store. If the resource is a document,
	 * then the resource id refers to the document id.
	 *
	 * @param resourceId
	 * @param fileType
	 * @param fileExtn
	 * @return
	 * @throws Exception
	 */
	public InputStream read(Long resourceId, String fileType, String fileExtn) throws Exception ;
	/**
	 * Returns HashMap, which mainly contains the OutputStream for writing the content of resource.
	 * 
	 * @param resourceId - ID of the resource to be write
	 * @param info - Fileinfo of resource
	 * @return
	 * @throws Exception
	 */
	public HashMap<String, Object> write(Long resourceId, SheetFileInfo info) throws Exception ;
	/**
	 * For closing the stream and returning the store object
	 * 
	 * @param writeInfo
	 * @throws Exception
	 */
	public void finishWrite(HashMap<String,Object> writeInfo) throws Exception;
	/**
	 * Deletes a resource with file extension.
	 * If the resource is a document, then the resource id refers to the document id.
	 * 
	 * @param resourceId
	 * @param fileExtn
	 * @throws Exception
	 */
	public void delete(Long resourceId, String fileExtn) throws Exception ;
	/**
	 * Deletes a resource with file type and file extension.
	 * If the resource is a document, then the resource id refers to the document id.
	 *
	 * @param resourceId
	 * @param fileType
	 * @param fileExtn
	 * @throws Exception
	 */
	public void delete(Long resourceId, String fileType, String fileExtn) throws Exception ;
	/** 
	 * Delete bulk resources with file type and file extension.
	 *
	 * @param resourceId
	 * @param fileType
	 * @param fileExtn
	 * @throws Exception
	 */
	public void delete(Long[] resourceId, String fileType, String fileExtn) throws Exception ;
	/**
	 * Checks whether resource is persist or not.  
	 * 
	 * @param resourceId 
	 * @param fileExtn
	 * @return
	 * @throws Exception
	 */
	public boolean exists(Long resourceId, String fileExtn) throws Exception;
	/**
	 * Checks whether resource is persist or not.
	 * 
	 * @param resourceId
	 * @param fileType
	 * @param fileExtn
	 * @return
	 * @throws Exception
	 */
	public boolean exists(Long resourceId, String fileType, String fileExtn) throws Exception;
	/**
	 * Returns the unique key for the specified resourceId.
	 *
	 * @param resourceId
	 * @return
	 * @throws Exception
	 */
	public String getUniqueKey(Long resourceId) throws Exception;
	/**
	 * Create the information of the resource which to be stored.
	 *
	 * @param resourceId
	 * @param fileType
	 * @param fileExtn
	 * @param fileSize
	 * @return
	 * @throws Exception
	 */
	public SheetFileInfo createFileInfo(Long resourceId, String fileType, String fileExtn, Long fileSize) throws Exception;

	/**
	 *
	 * @param resourceId
	 * @param fileType
	 * @param fileExtn
	 * @return
	 * @throws Exception
	 */
	public SheetFileInfo createFileInfo(Long resourceId, String fileType, String fileExtn) throws Exception;

	/**
	 * Get the information of the resource.
	 * 
	 * @param resourceId
	 * @param fileType
	 * @param fileExtn
	 * @return
	 * @throws Exception
	 */
	public SheetFileInfo getFileInfo(Long resourceId, String fileType, String fileExtn) throws Exception;
        
        public List<SheetFileInfo> getAvailableFileInfos();
        
   /* Cache related methods */
	public void setCache(boolean cache);
	public boolean isCache();
   /*
    * Following method signatures are currently commented but these are used by Show 
    * It will be used when it is needed.
    *
    * Commented ~Mani
    *
	public void write(Long resourceId, String fileExtn, Message message) throws Exception;
	public void write(Long resourceId, String fileType, String fileExtn, Message message) throws Exception;
	public void deleteResource(Long resourceId,boolean deleteFromDB) throws Exception;
	public void deleteResource(Long resourceId) throws Exception;
	public void deleteResources() throws Exception;
	public void trash(Long resourceId, String fileType, String fileExtn) throws Exception;
	public boolean isTrashed(Long resourceId, String fileType, String fileExtn) throws Exception;
	*/
}
