// $Id$
package com.adventnet.zoho.websheet.test;

import com.adventnet.zoho.websheet.model.ext.standard.ZSFunctionTable;
import com.adventnet.zoho.websheet.model.util.FormulaUtil;
import com.singularsys.jep.Jep;
import com.singularsys.jep.ParseException;
import com.singularsys.jep.parser.Node;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.FormulaUtil;
import com.singularsys.jep.Jep;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FormulaParserPrinterTest {

    public static void main(String[] args) throws ParseException {
//        StructureReferenceTest();
        addImplicitIntersectionOperatorTest();
    }

    private static void StructureReferenceTest() {
        Jep jep = new Jep();
        List<String> expressions = new ArrayList<>();
//        expressions.add("Table1[1]");
//        expressions.add("R[1]");
//        expressions.add("Table1.A1:A100");
//        expressions.add("DeptSales.dep[#ALL],[Sales Amount]]");
//        expressions.add("DeptSales.dep[[#ALL],[Sales Amount]]");
//        expressions.add("DeptSales[[#Headers],[% Commission]]");
//        expressions.add("DeptSales[[#Totals],[Region]]");
//        expressions.add("DeptSales[[#All],[Sales Amount]:[% Commission]]");
//        expressions.add("DeptSales[[#Data],[% Commission]:[Commission Amount]]");
//        expressions.add("DeptSales[@[% Commission]:[Commission Amount]]");
//        expressions.add("DeptSales[[#Headers],[Region]:[Commission Amount]]");
//        expressions.add("DeptSales[[#Headers],[#Data],[% Commission]]");
//        expressions.add("DeptSales[[#This Row], [Commission Amount]]");
//        expressions.add("SUM([Sales])");
//        expressions.add("COUNT(Table1[[#Data],[#Totals],[Sales]])");
//        expressions.add("DeptSales[@Commission Amount]");
//        expressions.add("DeptSales[@]");
//        expressions.add("[Sales Amount]*[% Commission]");
//        expressions.add("DeptSales[Sales Amount]*DeptSales[% Commission]");
//        expressions.add("A1[[Sales Person]:[Region]]");
//        expressions.add("DeptSales[Sales Amount],DeptSales[Commission Amount]");
//        expressions.add("DeptSales[[Sales Person]:[Sales Amount]] DeptSales[[Region]:[% Commission]]");
//        expressions.add("SUM(LEN(\"SDFFSDFSDFSD[[#ALL],[COLD]]\"))");
////        for(String expression: expressions) {
//            try {
//                Node node = jep.parse(expression);
//                System.out.println(expression);
//                System.out.println("--to string--" + node.toString());
//            } catch (Exception e) {
//                System.err.println(expression);
////                e.printStackTrace();
//            }
//            }
    }

    private static void addImplicitIntersectionOperatorTest() throws ParseException {
        Jep jep = new Jep();
        jep.setComponent(ZSFunctionTable.getInstance());

        Node node;
        Node resultantNode;
        node = jep.parse("COUNTIF(H36:H41;H37:H38)");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>(Arrays.asList("namedexp")));//No I18N
        //COUNTIF(H36:H41;@H37:H38) -------------------------------------------------------covered
        node = jep.parse("namedexp");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>(Arrays.asList("namedexp")));//No I18N
        //@namedexp -------------------------------------------------------covered
        node = jep.parse("namedexp+sum(namedexp)+namedexp1");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>(Arrays.asList("namedexp")));//No I18N
        //@namedexp+sum(namedexp)+namedexp1 -------------------------------------------------------covered

        node = jep.parse("A1");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //@namedexp -------------------------------------------------------covered

        node = jep.parse("A1:A10+B1:B10");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //@A1:A10+@B1:B10 -------------------------------------------------------covered

        node = jep.parse("A1:A10");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //@A1:A10 -------------------------------------------------------covered


        node = jep.parse("table1[col1;col2]");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //@table1[col1;col2]  -------------------------------------------------------covered

        node = jep.parse("A1:A2+A1:A2+A1:A2");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //@A1:A2+@A1:A2+@A1:A2 -------------------------------------------------------covered

        node = jep.parse("{1;2;3}");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //{1;2;3} -------------------------------------------------------covered

        node = jep.parse("@A1:A10");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //@A1:A10 -------------------------------------------------------covered

        node = jep.parse("A1:A2+SUM(A1:A2)");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //@A1:A2+SUM(A1:A2) -------------------------------------------------------covered

        node = jep.parse("{1;2;3}+{1;2;3}");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //@{1;2;3}+@{1;2;3} -------------------------------------------------------covered

        node = jep.parse("A1:A2+SUM(A1:A2+A1:A2)");//No I18N
        resultantNode = FormulaUtil.addImplicitIntersectionOperator(node, new HashSet<>());
        //@A1:A2+SUM(A1:A2+A1:A2) -------------------------------------------------------covered
    }

    private static void parseToRangeTest() throws Exception {
        Logger logger = Logger.getLogger("parseToRangeTest");//No I18N

        Workbook workbook = new Workbook();
        Sheet sheet;

        sheet = new Sheet();
        sheet.setName("Sheet1");//No I18N
        sheet.setWorkbook(workbook);
        workbook.addSheet(sheet, 0);
        sheet.getCell("Z100");//No I18N

        sheet = new Sheet();
        sheet.setName("Sheet2");//No I18N
        sheet.setWorkbook(workbook);
        workbook.addSheet(sheet, 0);
        sheet.getCell("Z100");//No I18N

        sheet = new Sheet();
        sheet.setName("Sheet,1");//No I18N
        sheet.setWorkbook(workbook);
        workbook.addSheet(sheet, 0);
        sheet.getCell("Z100");//No I18N

        List<Range> r;

        r = FormulaUtil.parseToRange("Sheet1.A1:A10,Sheet1.B1:B10", ",", workbook);//No I18N

        r = FormulaUtil.parseToRange("Sheet1.A1:A10,Sheet1.B1", ",", workbook);//No I18N

        r = FormulaUtil.parseToRange("Sheet2.B1:B10", ",", workbook);//No I18N

        r = FormulaUtil.parseToRange("'Sheet,1'.A1:A10,Sheet2.B1:B10", ",", workbook);//No I18N

        try {
            r = FormulaUtil.parseToRange("Sheet,1.A1:A10", ",", workbook);//No I18N
        } catch (Exception e) {
            logger.log(Level.OFF, "fail " , e);
        }

        r = FormulaUtil.parseToRange("Sheet1.A1:A10$$$Sheet1.B1:B10", "$$$", workbook);//No I18N

        try {
            r = FormulaUtil.parseToRange("Sheet,1.A1:A10$$$Sheet1.B1:B10", "$$$", workbook);//No I18N
        } catch (Exception e) {
            logger.log(Level.OFF, "fail " , e);
        }

        r = FormulaUtil.parseToRange("'Sheet,1'.A1:A10$$$Sheet1.B1:B10", "$$$", workbook);//No I18N

        logger.log(Level.OFF,"done");
    }
}
