// $Id$
/*
 * Cell.java
 *
 * Created on May 11, 2007, 12:30 PM
 */

package com.adventnet.zoho.websheet.model;


import com.adventnet.zoho.websheet.model.ext.functions.ImportRange;
import com.adventnet.zoho.websheet.model.ext.standard.ZSPrintVisitor;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.singularsys.jep.EvaluationException;

import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public interface Cell extends Cloneable  
{
     /** The recognized types of values */
    public enum Type
    {
        /* ***************************************
         * 
         * IMPORTANT :  Do not change the order as this order is used for SORTING
         *
         */
        
        /** Denotes a numeric value, with or without decimals */
        FLOAT,

        // Though a scientific number is also of type FLOAT, we need to identify it as a scientific number in order to set the appropriate Format.
        SCIENTIFIC,

        // Though a Fraction is also of type FLOAT, we need to identify it as a Fraction in order to set the appropriate Format.
        FRACTION,

        /** Denotes a percentage value, with or without decimals */
        PERCENTAGE,

        /** Denotes a currency value, with or without decimals */
        CURRENCY,

        /** Denotes a date */
        DATE,

        /** Denotes a time  */
        TIME,

        /** Denotes a DateTime value  */
        DATETIME,

        /** Denotes a text value, or a type of value derived from text */
        STRING,

        /** Denotes a boolean value, i.e. true or false */
        BOOLEAN,        
        
        /** Denotes an error, e.g. a type mismatch */
        ERROR,
        
        /** Denotes a value of undefined type */
        UNDEFINED;
        
    	
        public boolean isDateType() {
            return this == DATE 
                    || this == TIME 
                    || this == DATETIME;
        }
        
        public boolean isNumberType()
        {
            return this == FLOAT 
                    || this == PERCENTAGE 
                    || this == CURRENCY 
                    || this == FRACTION 
                    || this == SCIENTIFIC;
        }
    }


    public enum Error {
        CIRCULARREF("#CIRCULARREF!"),// NO I18N
        //#CIRCULARREF!

        DIV("#DIV/0!"),// NO I18N
        //#DIV/0! (2) - Attempt to divide by zero, including division by an empty cell.

        NA("#N/A!"),// NO I18N
        //#N/A (7) - NA. Lookup functions which failed and NA() return this value.

        NAME("#NAME!"),// NO I18N
        //#NAME? (5) - Unrecognized/deleted name.

        NULL("#NULL!"),// NO I18N
        //#NULL! (1) - Intersection of ranges produced zero cells.

        NUM("#NUM!"),// NO I18N
        //#NUM! (6) - Failed to meet domain constraints (e.g., input was too large or too small)

        REF("#REF!"),// NO I18N
        //#REF! (4) - Reference to invalid cell.

        VALUE("#VALUE!"),// NO I18N
        //#VALUE! (3) - Parameter is wrong type.

        TIMEOUT("#TIMEOUT"),//NO I18N

        FN_NOT_SUPPORTED("#FN_NOT_SUPPORTED!"),// NO I18N

        EVAL("#EVAL!"),// NO I18N

        NOT_LINKED("#NOT_LINKED!"),// NO I18N

        UNKNOWN_ERROR("#UNKNOWN_ERROR!"), // NO I18N

        ACCESS_DENIED("#ACCESS_DENIED!"),// NO I18N

        SPILL("#SPILL!"),// NO I18N

        LAZY_EVAL("#LAZY_EVAL!"),// NO I18N

        LOADING("#LOADING!");//NO I18N

        private final String errorString;
        private final Value valueObject;
        private final EvaluationException throwableObject;

        Error(String inErrorString) 
        {
            errorString = inErrorString;
            valueObject = new ValueI(Type.ERROR, this);
            throwableObject = new EvaluationException(this.errorString);
            throwableObject.setStackTrace(new StackTraceElement[]{});
        }

        public String getErrorString() 
        {
            return this.errorString;
        }

        public Value getValueObject() 
        {
            return valueObject;
        }

        public EvaluationException getThrowableObject() 
        {
            return throwableObject;
        }

        @Override
        public String toString() 
        {
            return this.getErrorString();
        }

        public static Error getError(String errorString)
        {
            if(errorString == null)
            {
                return UNKNOWN_ERROR;
            }
            switch(errorString)
            {
                case "#CIRCULARREF!":// NO I18N
                    return CIRCULARREF;

                case "#DIV/0!":// NO I18N
                    return DIV;

                case "#N/A":// NO I18N
                case "#N/A!":// NO I18N
                    return NA;

                case "#NAME!":// NO I18N
                    return NAME;

                case "#NULL!":// NO I18N
                    return NULL;

                case "#NUM!":// NO I18N
                    return NUM;

                case "#REF!":// NO I18N
                    return REF;

                case "#VALUE!":// NO I18N
                    return VALUE;

                case "#TIMEOUT"://NO I18N
                    return TIMEOUT;

                case "#FN_NOT_SUPPORTED!":// NO I18N
                    return FN_NOT_SUPPORTED;

                case "#EVAL!":// NO I18N
                    return EVAL;

                case "#NOT_LINKED!":
                    return NOT_LINKED;

                case "#ACCESS_DENIED!":
                    return ACCESS_DENIED;

                case "#SPILL!":
                    return SPILL;

                case "#LAZY_EVAL!":
                    return LAZY_EVAL;

                case "#LOADING!":// NO I18N
                    return LOADING;

                case "#UNKNOWN_ERROR!":// NO I18N
                default:
                        return UNKNOWN_ERROR;
            }
        }
    }


    public Row getRow();

    public void setRow(Row row);

    public int getColumnIndex();

    public void setColumn(Column column);

    public Column getColumn();

    public int getRowIndex();

    public int getColsRepeated();

    public void setColsRepeated(int colsRepeated);

    public String getCellRef();

    public String getContent();

    public int getPatternRepeatIndex();

    public String getPatternRepeatChar();

    public String getContentColor();
    
    public Type getContentType();
    
    public Boolean isContentValid();
    
    public void setContentValid(Boolean contentValid);

    public Value getValue();

    public Object getValueObject();

    public void setValue(Value value);

    public String getFormula();

    public String getLocalizedFormula();

    public void setFormula(String formula, boolean isUpdateDependencies);

    public void setExpression(Expression inExpression, boolean isUpdateDependencies, boolean isUpdateCellEditHistory);

    public void setExpression(Expression inExpression, boolean isUpdateDependencies);

    public void setExpressionFromParser(Expression inExpression, boolean isUpdateDependencies, boolean isUpdateCellEditHistory);

    public String getStyleName();

    public void setStyleName(String styleName);

    public void setContentValidationName(String contentValidationName);

    public String getContentValidationName();

    public com.adventnet.zoho.websheet.model.Cell.Type getType();

    public boolean isFormula();

    public void setLink(String linkUrl);
    
    public String getLink();
    
    public List<RichStringProperties> getLinks();

    public Collection<Cell> getDependents();

    public Expression getExpression();

    void evaluate();

    public String[] getAttributes();
    
    public String[] getValues(ZSPrintVisitor zsPrintVisitor, int repeated);

    public void getZSValues(int repeated, String expressionName, String[] valuesShell);

    public Cell clone();

    public void setAnnotation(Annotation annotation);

    public Annotation getAnnotation();

    public int getArrayColSpan();

    public int getArrayRowSpan();

    public void setArraySpan(int arrayRowSize, int arrayColSize);

    public void setPattern(ZSPattern pattern);

    public CellStyle getCellStyle();

    public ContentValidation getContentValidation();

    public String getCurrencyCode();

    public boolean isEmpty();

    public void clearContent(boolean isSetUsedIndex, boolean isUpdateCellEditHistory, boolean resetPicklistValue);

    public void clearStyle(boolean isSetUsedIndex);

    public void clearAll(boolean isSetUsedIndex, boolean isUpdateCellEditHistory);

    public void clearAll(boolean isSetUsedIndex);

    public void clearAllFromShift(boolean isSetUsedIndex);

    public void clearAnnotation();

    public void clearRichText(boolean isSetUsedIndex);

    public CellImpl.Dimention getCellHeight();

    public CellImpl.Dimention getCellWidth();

    public boolean hasFieldNode();

    public int getOptimalWidth();

    public int getOptimalHeight();

    public List<DrawControl> getDrawControlList();

    public void addDrawControl(DrawControl drawControl);

    public Locale getFunctionLocale();

    public SpreadsheetSettings getSpreadsheetSettings();

    public Locale getAccountsLocale();

    public Set getPrecedents();
    
    public long getValueTimestamp();
    
    public Cell absoluteClone(Row row);
    
    public void addAttachment(int index, Attachment attachment);
    
    public List<Attachment> getAttachments();
    
    public void removeAllAttachments();
    
    public void addAllAttachment (Collection <Attachment> attachments);
    
    public void removeAttachment(int index);

    public void addAttachment(Attachment attachment);

    public boolean isImage();

    public int getIgnoreError();

    public void setIgnoreError(int errorType);

    public Collection<CellPicklist> getCellPicklists();

    public CellPicklist getCellPicklist(int picklistID);

    public boolean addCellPicklist(CellPicklist cellPicklist);

    public void setCellPicklists(Map<Integer,CellPicklist> cellPicklists);

    public boolean isEdited();

    public void setEdited(boolean edited);

    boolean isZSString();

    ImportRange.SYNC_STATE getImportrangeSyncState();
}
