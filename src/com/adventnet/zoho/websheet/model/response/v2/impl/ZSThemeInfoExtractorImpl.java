package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.ThemesBean;
import com.adventnet.zoho.websheet.model.response.extractor.ZSThemeInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

public class ZSThemeInfoExtractorImpl implements ZSThemeInfoExtractor {

    ThemesBean themesBean;
    public ZSThemeInfoExtractorImpl(JSONObjectWrapper actionJSON)
    {
        int action = (actionJSON != null && actionJSON.has(JSONConstants.ACTION)) ? actionJSON.getInt(JSONConstants.ACTION) : -1;

        switch (action) {
            case -1:
                CommandConstants.OperationType opType = CommandConstants.OperationType.GENERATE_LIST;
                themesBean = new ThemesBean(null, opType);
                break;
            case ActionConstants.APPLY_THEME:
                themesBean = new ThemesBean(null, CommandConstants.OperationType.THEME);
                break;
        }
    }

    @Override
    public ThemesBean getThemesBean() {
        return themesBean;
    }
}
