package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.TimelineBean;
import com.adventnet.zoho.websheet.model.response.extractor.TimelineInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;

public class TimelineInfoExtractorImpl implements TimelineInfoExtractor {

    List<TimelineBean> timelineBeanList = new ArrayList<>();
    public TimelineInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        String sheetName = null;
        CommandConstants.OperationType operationType = null;
        TimelineBean timelineBean;
        List<String> timelines = new ArrayList<>();
        List<String> pivotIDList = new ArrayList<>();
        String timelineTobeInserted = null;
        boolean sendFullResponse = false;
        TimelineBean.TimelineResponseType timelineResponseType = TimelineBean.TimelineResponseType.INIT;

        switch (action) {
            case ActionConstants.APPLY_THEME:
            case -1:
                sheetName = actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                operationType = CommandConstants.OperationType.GENERATE_LIST;
                break;
            case ActionConstants.TIMELINE_NEW:
                sheetName = actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                operationType = CommandConstants.OperationType.INSERT;
                JSONArrayWrapper timelineNames = actionJson.getJSONArray(JSONConstants.TIMELINE_LIST);
                for(int i = timelineNames.length()-1; i >= 0 ; i--) {
                    timelines.add(timelineNames.getString(i));
                }
                break;
            case ActionConstants.APPLY_SLICER_FILTER:
            case ActionConstants.UPDATE_CONNECTED_PIVOTS:

                if(actionJson.has(JSONConstants.TIMELINE_ID)) {
                    if(actionJson.has(JSONConstants.CONNECTED_PIVOTS)){
                        JSONArrayWrapper pivotNames = actionJson.getJSONArray(JSONConstants.CONNECTED_PIVOTS);
                        for(int j = 0; j < pivotNames.length(); j++){
                            pivotIDList.add(pivotNames.getString(j));
                        }
                    }
                    timelines.add(actionJson.getString(JSONConstants.TIMELINE_ID));
                    operationType = CommandConstants.OperationType.MODIFY;
                    sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString(JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                    sendFullResponse = true;
                    timelineResponseType = TimelineBean.TimelineResponseType.BUTTON_STATES;
                }
                break;
            case ActionConstants.APPLY_TIMELINE_FILTER:
            case ActionConstants.UPDATE_TIMELINE_CONNECTED_PIVOTS:
            case ActionConstants.APPLYFILTER_PIVOT:
                sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString( JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                operationType = CommandConstants.OperationType.MODIFY;
                if(actionJson.has(JSONConstants.TIMELINE_ID)) {
                    String timelineName = actionJson.getString(JSONConstants.TIMELINE_ID);
                    timelines.add(timelineName);
                }
                else if(actionJson.has(JSONConstants.ID) ) {
                    pivotIDList.add(actionJson.getString(JSONConstants.ID));
                }
                timelineResponseType = TimelineBean.TimelineResponseType.BUTTON_STATES;
                sendFullResponse = true;
                break;
            case ActionConstants.CHANGE_TIMELINE_TITLE:
            case ActionConstants.CHANGE_TIMELINE_LEVEL:
            case ActionConstants.CHANGE_TIMELINE_LAYOUT:
            case ActionConstants.CHANGE_TIMELINE_THEME:
                //modify response without full response.
                sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString( JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                operationType = CommandConstants.OperationType.MODIFY;
                String timelineName = actionJson.getString(JSONConstants.TIMELINE_ID);
                timelines.add(timelineName);
                if(action == ActionConstants.CHANGE_TIMELINE_THEME){
                    timelineResponseType = TimelineBean.TimelineResponseType.STYLE;
                }else {
                    timelineResponseType = TimelineBean.TimelineResponseType.SETTINGS;
                }
                break;

            case ActionConstants.TIMELINE_RESIZE:
            case ActionConstants.TIMELINE_MOVE:
                if(actionJson.has(JSONConstants.TIMELINE_ID)){
                    sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString( JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                    operationType = CommandConstants.OperationType.MOVE;
                    timelines.add(actionJson.getString(JSONConstants.TIMELINE_ID));
                }
                timelineResponseType = TimelineBean.TimelineResponseType.DIMENSION;
                break;
            case ActionConstants.MOVE_TIMELINE_TO_NEW_SHEET:
                JSONArrayWrapper temp = new JSONArrayWrapper();
                timelineTobeInserted = actionJson.getString(JSONConstants.TIMELINE_ID);
                temp.put(timelineTobeInserted);
                sheetName = actionJson.getString(JSONConstants.DESTINATION_SHEET);
                operationType = CommandConstants.OperationType.ADD;
                actionJson.put(JSONConstants.TIMELINE_LIST,temp);
            case ActionConstants.UPDATE_TIMELINE_COLUMN:
                sheetName = sheetName == null ? actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME) : sheetName;
                timelineTobeInserted  = timelineTobeInserted == null ? actionJson.getString(JSONConstants.NEW_TIMELINE_ID) : timelineTobeInserted;
                operationType = operationType == null ? CommandConstants.OperationType.INSERT : operationType;
                timelines.add(timelineTobeInserted);
                timelineBean =  new TimelineBean(timelines, operationType,sheetName,false,timelineResponseType);
                timelines = new ArrayList<>();
                timelineBeanList.add(timelineBean);
            case ActionConstants.TIMELINE_DELETE:
                sheetName = actionJson.has(JSONConstants.ASSOCIATED_SHEET_NAME) ? actionJson.getString( JSONConstants.ASSOCIATED_SHEET_NAME) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                operationType = CommandConstants.OperationType.DELETE;
                JSONArrayWrapper timelineArr =  actionJson.has(JSONConstants.TIMELINE_LIST) ? actionJson.getJSONArray(JSONConstants.TIMELINE_LIST) : new JSONArrayWrapper();
                for(int i = 0; i < timelineArr.length(); i++) {
                    timelines.add(timelineArr.getString(i));
                }

                if(actionJson.has(JSONConstants.CONNECTED_PIVOTS)){
                    JSONArrayWrapper pivotNames = actionJson.getJSONArray(JSONConstants.CONNECTED_PIVOTS);
                    for(int j = 0; j < pivotNames.length(); j++){
                        pivotIDList.add(pivotNames.getString(j));
                    }
                }
                sendFullResponse = true;
                break;

            default:
                sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString( JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                JSONObjectWrapper sheetToTimelineMap =  actionJson.has(JSONConstants.TIMELINE_DELETE_MAP) ? actionJson.getJSONObject(JSONConstants.TIMELINE_DELETE_MAP) : new JSONObjectWrapper();
                Iterator<String> iterator = sheetToTimelineMap.keys();
                HashSet<String> deletedSlicers = new HashSet<>();
                if(!actionJson.has("fromundo")) {
                    while (iterator.hasNext()) {
                        String sheet = iterator.next();
                        JSONArrayWrapper slicerArray = sheetToTimelineMap.getJSONArray(sheet);
                        for (int j = 0; j < slicerArray.length(); j++) {
                            timelines.add(slicerArray.getString(j));
                        }
                        timelineBean = new TimelineBean(timelines, CommandConstants.OperationType.DELETE, sheet, true,timelineResponseType);
                        timelineBeanList.add(timelineBean);
                        deletedSlicers.addAll(timelines);
                        timelines = new ArrayList<>();
                    }
                }
                if(actionJson.has(JSONConstants.TIMELINE_UPDATE_MAP)){
                    JSONObjectWrapper slicerUpdateMap = actionJson.getJSONObject(JSONConstants.TIMELINE_UPDATE_MAP);
                    Iterator<String> sheetIterator = slicerUpdateMap.keys();
                    while(sheetIterator.hasNext()){
                        String sheet = sheetIterator.next();
                        JSONArrayWrapper slicerArray = slicerUpdateMap.getJSONArray(sheet);
                        for(int j = 0; j < slicerArray.length(); j++){
                            if(deletedSlicers.contains(slicerArray.getString(j))){
                                continue;
                            }
                            timelines.add(slicerArray.getString(j));
                        }
                        timelineBean = new TimelineBean(timelines, CommandConstants.OperationType.MODIFY, sheet, true,timelineResponseType);
                        timelineBeanList.add(timelineBean);
                    }
                }
                if(actionJson.has(JSONConstants.CONNECTED_PIVOTS)){
                    JSONArrayWrapper pivotNames = actionJson.getJSONArray(JSONConstants.CONNECTED_PIVOTS);
                    for(int j = 0; j < pivotNames.length(); j++){
                        pivotIDList.add(pivotNames.getString(j));
                    }
                    if(!timelineBeanList.isEmpty()) {
                        timelineBeanList.get(0).setPivotNames(pivotIDList);
                    }
                    else{
                        operationType = CommandConstants.OperationType.MODIFY;
                        sendFullResponse = true;
                    }
                }
                break;
        }
        if(operationType != null){
            timelineBean = new TimelineBean(timelines,operationType,sheetName,sendFullResponse,timelineResponseType);
            if(!pivotIDList.isEmpty()){
                timelineBean.setPivotNames(pivotIDList);
            }
            timelineBeanList.add(timelineBean);
        }
    }
    @Override
    public List<TimelineBean> getTimelineBeanList() {
        return timelineBeanList;
    }

}
