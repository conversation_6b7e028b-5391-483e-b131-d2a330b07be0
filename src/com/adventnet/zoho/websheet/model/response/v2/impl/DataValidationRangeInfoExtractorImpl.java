package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.DataValidationRangeInfoExtractor;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.List;

public class DataValidationRangeInfoExtractorImpl implements DataValidationRangeInfoExtractor {

    List<RangeWrapper> rangeList = new ArrayList<>();

    public DataValidationRangeInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        boolean isUndoAction = actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO);
        RangeWrapper rangeWrapper;
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        if (action == ActionConstants.DATA_VALIDATION_CLEAR)
        {
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), isUndoAction? CommandConstants.OperationType.ADD : CommandConstants.OperationType.REMOVE);
                    rangeList.add(rangeWrapper);
                }
            }
        }
        else if (action == -1) {
            rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), -1, -1, -1, -1, CommandConstants.OperationType.GENERATE_LIST);
            rangeList.add(rangeWrapper);
        }
        else {
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), isUndoAction ? CommandConstants.OperationType.REMOVE : CommandConstants.OperationType.ADD);
                    rangeList.add(rangeWrapper);
                }
            }
        }
    }

    @Override
    public List<RangeWrapper> getRangeList() {
        return rangeList;
    }
}
