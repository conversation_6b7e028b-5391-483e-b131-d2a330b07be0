package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.InsertDeleteCellsInfoExtractor;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.List;

public class InsertDeleteCellsInfoExtractorImpl implements InsertDeleteCellsInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public InsertDeleteCellsInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        RangeWrapper rangeWrapper;
        CommandConstants.OperationType opType = action == ActionConstants.INSERT_CELL_LEFT ? CommandConstants.OperationType.INSERT_CELL_LEFT
                : (action == ActionConstants.INSERT_CELL_TOP ? CommandConstants.OperationType.INSERT_CELL_TOP
                : (action == ActionConstants.DELETE_CELL_BOTTOM ? CommandConstants.OperationType.DELETE_CELL_BOTTOM
                : (action == ActionConstants.DELETE_CELL_RIGHT ? CommandConstants.OperationType.DELETE_CELL_RIGHT : null)));
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        if(dataRange != null){
            for (DataRange range : dataRange) {
                rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(),range.getStartColIndex(),range.getEndRowIndex(),range.getEndColIndex(), opType);
                rangeWrapperList.add(rangeWrapper);
            }
        }else{
            rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), -1, -1, -1, -1, CommandConstants.OperationType.GENERATE_LIST);
            rangeWrapperList.add(rangeWrapper);
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
