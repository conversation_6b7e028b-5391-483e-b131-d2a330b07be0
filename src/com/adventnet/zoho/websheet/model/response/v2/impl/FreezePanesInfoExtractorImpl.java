package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.ext.LinearIntegralRange;
import com.adventnet.zoho.websheet.model.response.extractor.FreezePanesInfoExtractor;
import com.adventnet.zoho.websheet.model.response.helper.FreezeWrapper;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;

public class FreezePanesInfoExtractorImpl implements FreezePanesInfoExtractor {

    List<FreezeWrapper> freezeWrapperList = new ArrayList<>();
    public FreezePanesInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        CommandConstants.OperationType operationType;
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        FreezeWrapper freezeWrapper;
        LinearIntegralRange freezeRowRange = null;
        LinearIntegralRange freezeColRange = null;
        switch(action) {
            case ActionConstants.FREEZE_PANES:
                operationType = CommandConstants.OperationType.MODIFY;
                break;
            case ActionConstants.UNFREEZE_PANES:
                operationType = CommandConstants.OperationType.MODIFY;
                break;
            case ActionConstants.INSERT_ROW:
            case ActionConstants.INSERT_COL:
            case ActionConstants.INSERT_COPY_ROW:
            case ActionConstants.INSERT_COPY_COLUMN:
            case ActionConstants.DELETE_ROW:
            case ActionConstants.DELETE_COL:
                operationType = CommandConstants.OperationType.UPDATE;
                freezeWrapper = new FreezeWrapper(actionJson.getString(JSONConstants.SHEET_NAME), null, null, operationType);
                freezeWrapperList.add(freezeWrapper);
                break;
            case ActionConstants.INSERT_CUT_ROW:
            case ActionConstants.INSERT_CUT_COLUMN:
                operationType = CommandConstants.OperationType.UPDATE;
                freezeWrapperList.add(new FreezeWrapper(actionJson.getString(JSONConstants.SRC_SHEET), null, null, operationType));
                freezeWrapperList.add(new FreezeWrapper(actionJson.getString(JSONConstants.DES_SHEET), null, null, operationType));
                break;
            default:
                operationType = CommandConstants.OperationType.GENERATE_LIST;
        }
        if (actionJson.has(JSONConstants.FREEZEPANE)) {
            JSONObjectWrapper freezeAction = actionJson.getJSONObject(JSONConstants.FREEZEPANE);
            if(freezeAction.has(JSONConstants.FREEZE_ROW_RANGE))
            {
                JSONObjectWrapper freezeRowJson = freezeAction.getJSONObject(JSONConstants.FREEZE_ROW_RANGE);
                freezeRowRange = new LinearIntegralRange(freezeRowJson.getInt(JSONConstants.START_ROW), freezeRowJson.getInt(JSONConstants.END_ROW) + 1);
            }
            if(freezeAction.has(JSONConstants.FREEZE_COL_RANGE))
            {
                JSONObjectWrapper freezeColJson = freezeAction.getJSONObject(JSONConstants.FREEZE_COL_RANGE);
                freezeColRange = new LinearIntegralRange(freezeColJson.getInt(JSONConstants.START_COLUMN), freezeColJson.getInt(JSONConstants.END_COLUMN) + 1);
            }
            freezeWrapper = new FreezeWrapper(freezeAction.getString(JSONConstants.ASSOCIATED_SHEET_NAME), freezeRowRange, freezeColRange, operationType);
            freezeWrapperList.add(freezeWrapper);
        } else if (actionJson.has(JSONConstants.UNFREEZEPANE)) {
            JSONObjectWrapper unfreezePaneAction = actionJson.getJSONObject(JSONConstants.UNFREEZEPANE);
            if(unfreezePaneAction.optBoolean(JSONConstants.UNFREEZE_ROWS))
            {
                freezeWrapperList.add(new FreezeWrapper(unfreezePaneAction.getString(JSONConstants.ASSOCIATED_SHEET_NAME), new LinearIntegralRange(0, -1), null, operationType));
            }
            if(unfreezePaneAction.optBoolean(JSONConstants.UNFREEZE_COLS))
            {
                freezeWrapperList.add(new FreezeWrapper(unfreezePaneAction.getString(JSONConstants.ASSOCIATED_SHEET_NAME), null, new LinearIntegralRange(0, -1), operationType));
            }

        }
    }

    @Override
    public List<FreezeWrapper> getFreezeWrapperList() {
        return freezeWrapperList;
    }
}
