package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.ErrorCode;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.ErrorBean;
import com.adventnet.zoho.websheet.model.response.extractor.ErrorInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

public class ErrorInfoExtractorImpl implements ErrorInfoExtractor {

    ErrorBean errorBean;
    public ErrorInfoExtractorImpl(JSONObjectWrapper actionJson, JSONObjectWrapper errorJson)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        boolean isUndoAction = actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO);
        CommandConstants.OperationType operationType	= 	null;
        JSONObjectWrapper errorObj = null;
        switch (action) {

            case ActionConstants.IMPORT_CLOUD_DATA:
            case ActionConstants.UPDATE_CLOUD_DATA:
                if(actionJson.has("errorCode") || "Not.Authorized.To.Access".equals(actionJson.optString(JSONConstants.ROLL_BACK_ERROR_CODE))) {
                    errorObj = new JSONObjectWrapper();
                    String actionType = actionJson.getString(JSONConstants.SUB_ACTION);
                    if (actionType.equals("add")) {
                        operationType = CommandConstants.OperationType.ADD;
                    } else if (actionType.equals("edit")) {
                        operationType = CommandConstants.OperationType.MODIFY;
                    } else if (actionType.equals("refresh") || actionType.equals("scheduleRefresh")) {
                        operationType = CommandConstants.OperationType.UPDATE;
                    } else if (actionType.equals("delete")) {
                        operationType = CommandConstants.OperationType.REMOVE;
                    }
                    else if("reActivate".equals(actionType))
                    {
                        operationType = CommandConstants.OperationType.TURN_ON_LINK;
                        errorObj.put("isDocLimitExceed", actionJson.optBoolean("isDocLimitExceed"));
                    }
                    errorObj.put(JSONConstants.ERROR_MESSAGE, actionJson.getString(JSONConstants.ROLL_BACK_ERROR_CODE));
                    errorObj.put(JSONConstants.WEBDATA_ID, actionJson.getString(JSONConstants.WEBDATA_ID));
                    errorObj.put(JSONConstants.SUB_ACTION, operationType.toString());
                    errorObj.put("newSize", actionJson.optJSONObject("newSize"));
                    if(actionJson.has("errorCode")) {
                        errorObj.put("errorCode", actionJson.getInt("errorCode"));
                    }
                    errorBean = new ErrorBean(CommandConstants.DATA_CONNECTION, errorObj);
                }
                else
                {
                    // default error handling
                    errorBean = new ErrorBean(CommandConstants.ALL, errorJson);
                }
                break;
            case ActionConstants.CREATE_PIVOT:
            case ActionConstants.EDIT_PIVOT:
                errorObj = new JSONObjectWrapper();
                if(ActionConstants.CREATE_PIVOT == action){
                    operationType = CommandConstants.OperationType.ADD;
                }else if(ActionConstants.EDIT_PIVOT == action){
                    operationType = CommandConstants.OperationType.MODIFY;
                }
                // errorBean = new ErrorBean("Pivot Error Key", "Pivot Error Msg"); //NO I18N
                if(ErrorCode.ERROR_DOCUMENT_SAVE_SIZEEXCEED.equals(actionJson.getString(JSONConstants.ROLL_BACK_ERROR_CODE))
                        || ErrorCode.ERROR_RANGE_LOCKED.equals(actionJson.getString(JSONConstants.ROLL_BACK_ERROR_CODE))
                        || ErrorCode.ERROR_PIVOT_RANGE_LOCKED.equals(actionJson.getString(JSONConstants.ROLL_BACK_ERROR_CODE))
                        || ErrorCode.ERROR_PIVOT_TIMEOUT.equals(actionJson.getString(JSONConstants.ROLL_BACK_ERROR_CODE))
                        || ErrorCode.ERROR_ROW_LIMIT_EXCEED.equals(actionJson.getString(JSONConstants.ROLL_BACK_ERROR_CODE))
                        || ErrorCode.ERROR_COLUMN_LIMIT_EXCEED.equals(actionJson.getString(JSONConstants.ROLL_BACK_ERROR_CODE)))
                {
                    errorBean = new ErrorBean(CommandConstants.ALL, errorJson);
                }
                else {
                    errorObj.put(JSONConstants.SUB_ACTION, operationType.toString());
                    errorObj.put(JSONConstants.ERROR_MESSAGE, actionJson.getString(JSONConstants.ROLL_BACK_ERROR_CODE));
                    errorBean = new ErrorBean(CommandConstants.PIVOT, errorObj);
                }
                break;
            default:
                errorBean = new ErrorBean(CommandConstants.ALL, errorJson);
                break;

        }
    }

    @Override
    public ErrorBean getErrorBean() {
        return errorBean;
    }
}
