package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.response.viewport.Constraints;
import com.adventnet.zoho.websheet.model.response.beans.KnitChartsBean;
import com.adventnet.zoho.websheet.model.response.extractor.KnitChartsInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTablePool;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;

import java.util.ArrayList;
import java.util.List;


/**
 * Charts Information Extractor. Every chart edit actions information are extracted from here.
 * <AUTHOR>
 */
public class KnitChartsInfoExtractorImpl implements KnitChartsInfoExtractor {

    private final List<KnitChartsBean> knitChartsBeans;

    public KnitChartsInfoExtractorImpl(JSONObjectWrapper actionJSON, Constraints constraints) {
        knitChartsBeans = new ArrayList<>();
        generateChartBeans(actionJSON, constraints);
    }

    private boolean isChartAction(int action) {
        switch (action) {
            case ActionConstants.INSERT_CHART:
            case ActionConstants.INSERT_TABLE_CHART:
            case ActionConstants.INSERT_PIVOT_CHART:
            case ActionConstants.FRAMEWORK_CHART_EDIT:
            case ActionConstants.SHEET_CHART_EDIT:
            case ActionConstants.CHART_MOVE_TO_OTHER_SHEET:
            case ActionConstants.DELETE_CHART:
            case ActionConstants.CLONE_CHART:
            case ActionConstants.PUBLISH_CHART:
            case ActionConstants.UNPUBLISH_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART_VIA_CACHE:
            case ActionConstants.PASTE_CHART_STYLES:
            case ActionConstants.RESET_CHART_STYLES:
            case ActionConstants.PASTE_CHART: {
               return true;
            }
        }

        return false;
    }

    private void generateChartBeans(JSONObjectWrapper actionJSON, Constraints constraints) {
        if(actionJSON.has(JSONConstants.ACTION)) {
            int action = actionJSON.getInt(JSONConstants.ACTION);
            if(isChartAction(action)) {
                DataTablePool dataTablePool = new DataTablePool();
                JSONArrayWrapper chartJSONs = actionJSON.getJSONArray(ChartActionConstants.JSONConstants.CHART_JSON);
                String associatedSheetID = actionJSON.getString(JSONConstants.ASSOCIATED_SHEET_NAME);

                if(constraints.isSheetInViewPort(associatedSheetID)) {
                    ChartUtils.forEach(chartJSONs, objectChartJSON -> {
                        JSONObjectWrapper chartJSON = (JSONObjectWrapper) objectChartJSON;
                        KnitChartsBean bean = new KnitChartsBean(action, associatedSheetID, dataTablePool)
                                .setChartJSON(chartJSON)
                                .setClientUpdatedNeed(getIsClientUpdateNeeded(actionJSON, chartJSON));
                        knitChartsBeans.add(bean);
                    });
                }
            }
        }
    }

    private boolean getIsClientUpdateNeeded(JSONObjectWrapper actionJSON, JSONObjectWrapper chartJSON) {
        return actionJSON.has(ChartActionConstants.JSONConstants.UNDO)
                || actionJSON.has(ChartActionConstants.JSONConstants.REDO)
                || chartJSON.has(ChartActionConstants.JSONConstants.IS_CLIENT_UPDATE_NEEDED);
    }

    @Override
    public List<KnitChartsBean> getChartsInfo() {
        return knitChartsBeans;
    }
}
