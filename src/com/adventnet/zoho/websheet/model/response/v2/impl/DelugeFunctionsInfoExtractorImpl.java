package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.DelugeFunctionsBean;
import com.adventnet.zoho.websheet.model.response.extractor.DelugeFunctionsInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;

public class DelugeFunctionsInfoExtractorImpl implements DelugeFunctionsInfoExtractor {

    List<DelugeFunctionsBean> delugeFunctionsBeanList = new ArrayList<>();
    public DelugeFunctionsInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        Integer action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        String functionName = null;
        CommandConstants.OperationType operationType = null;
        DelugeFunctionsBean delugeFunctionsBean = null;
        if(action == -1 ||  action== ActionConstants.DRE_GETALL){
            // Fetching buttons on load
            operationType = CommandConstants.OperationType.GENERATE_LIST;
            delugeFunctionsBean = new DelugeFunctionsBean(operationType);
            delugeFunctionsBeanList.add(delugeFunctionsBean);
        } else {
            switch(action){
                case ActionConstants.DRE_CREATE:
                    operationType = CommandConstants.OperationType.INSERT;
                    break;
                case ActionConstants.DRE_UPDATE:
                    operationType = CommandConstants.OperationType.EDIT;
                    break;
                case ActionConstants.DRE_DELETE:
                    operationType = CommandConstants.OperationType.DELETE;
                    break;
            }
            functionName = actionJson.has(JSONConstants.DRE_FUNCTION_NAME) ? actionJson.getString(JSONConstants.DRE_FUNCTION_NAME) : null;
            delugeFunctionsBean = new DelugeFunctionsBean(functionName, operationType);
            delugeFunctionsBeanList.add(delugeFunctionsBean);
        }
    }

    @Override
    public List<DelugeFunctionsBean> getDelugeFunctionsBeanList() {
        return delugeFunctionsBeanList;
    }
}
