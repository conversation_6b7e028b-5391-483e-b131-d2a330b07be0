package com.adventnet.zoho.websheet.model.response.v2;

import com.adventnet.iam.IAMUtil;
import com.adventnet.persistence.DataAccessException;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.standard.ZSJSONizerVisitor;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.filter.UserFilterViews;
import com.adventnet.zoho.websheet.model.filter.util.FilterUtil;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.pivot.PivotTemplateContainer;
import com.adventnet.zoho.websheet.model.response.ResponseObject;
import com.adventnet.zoho.websheet.model.response.*;
import com.adventnet.zoho.websheet.model.response.UserInfo;
import com.adventnet.zoho.websheet.model.response.beans.*;
import com.adventnet.zoho.websheet.model.response.meta.CellMeta;
import com.adventnet.zoho.websheet.model.response.meta.UserMeta;
import com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta;
import com.adventnet.zoho.websheet.model.response.meta.util.MetaBitUtil;
import com.adventnet.zoho.websheet.model.response.extractor.*;
import com.adventnet.zoho.websheet.model.response.helper.*;
import com.adventnet.zoho.websheet.model.response.holder.ConstraintResponseHolder;
import com.adventnet.zoho.websheet.model.response.holder.DocumentResponseHolder;
import com.adventnet.zoho.websheet.model.response.holder.UserSpecificResponseHolder;
import com.adventnet.zoho.websheet.model.response.meta.DocumentMeta;
import com.adventnet.zoho.websheet.model.response.v2.impl.*;
import com.adventnet.zoho.websheet.model.response.v2.impl.CellResponseJsonImpl;
import com.adventnet.zoho.websheet.model.response.v2.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.response.viewport.Area;
import com.adventnet.zoho.websheet.model.response.viewport.Constraints;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSThemesLibrary;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.fcomponents.util.FCUtil;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.action.externaldata.constants.DCLiveStatusCode;
import com.zoho.sheet.action.externaldata.metastorage.DBActionManager;
import com.zoho.sheet.authorization.util.AuthorizationUtil;
import com.zoho.sheet.chart.ChartUtil;
import com.zoho.sheet.chart.ChartUtils;
import com.zoho.sheet.chartengine.ChartEngineManager;
import com.zoho.sheet.deluge.DelugeUtils;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.responsegenerator.gridaction.ChartResponseGenerator;
import com.zoho.sheet.knitcharts.pojo.ModifiedChartPOJO;
import com.zoho.sheet.knitcharts.chartclip.ChartClip;
import com.zoho.sheet.knitcharts.responsegenerator.gridaction.modification.ChartModificationResponseGenerator;
import com.zoho.sheet.knitcharts.miscellaneous.StatelessLambda;
import com.zoho.sheet.util.*;
import com.zoho.zfsng.client.ZohoFS;

import java.text.DecimalFormatSymbols;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType.*;
import static com.zoho.sheet.action.DynamicRequestAction.getCurrencyFormats;

public class V2ResponseAnalyzer extends VersionBasedResponseAnalyzer {

    private static final Logger LOGGER = Logger.getLogger(V2ResponseAnalyzer.class.getName());

    /* Variables used to update CellResponse */
    HashSet<String> faultySheets;
    boolean isActiveSheetFaulty; // Boolean used to indicate current sheet is faulty. Used in CellResponseJsonImpl to send cell response for current sheet
    List<Cell> srcCells;
    HashSet<Cell> dependentCells;
    ArrayList<RangeWrapper> rangeList;
    /* Variables used to update CellResponse */

    /* Variable used to create Constraint Response */
    JSONObjectWrapper faultySheetObj = null;	//it is not part of actual constraint response, later it will get move to documentJson while creating response
    /* Variable used to create Constraint Response */

    public V2ResponseAnalyzer(ResponseObject responseObject, Constraints constraints, UserInfo userInfo) {
        super(responseObject, constraints, userInfo);
    }

    @Override
    public ResponseHelper updateResponseHelper()
    {
        ResponseHelper responseHelper = null;
        ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
        JSONObjectWrapper actionJson = responseObject.getActionJson();
        if(actionJson.optBoolean("isPostDoc"))
        {
            responseHelper = updateResponseHelperForPostDocLoadAction();
        }
        else if(actionJson.optBoolean("isFetchAction"))
        {
            responseHelper = updateResponseHelperForFetch(actionJson);
        }
        else if(actionJson.has(JSONConstants.ACTION))
        {
            responseHelper  = updateResponseHelperForGrid(actionJson, responseObject.getSrcCells(), responseObject.getDependentCells(), responseObject.getMacroResponse());
        }
        return responseHelper;
    }

    @Override
    public ResponseHelper updateResponseHelperForErrorResponse()
    {
        ResponseHelper responseHelper = new ResponseHelper();
        responseHelper.includeErrorResponse = true;
        responseHelper.includeActionIdentifier = true;
        return responseHelper;
    }

    @Override
    public ResponseHelper updateResponseHelperSyncStylesResponse()
    {
        ResponseHelper responseHelper = new ResponseHelper();
        responseHelper.includeSyncStyles = true;
        responseHelper.includeActionIdentifier = true;
        return responseHelper;
    }

    private void setDocumentResponseHolder(ResponseHelper responseHelper)
    {
        JSONObjectWrapper actionJson = getActionJson();
        updateForceReloadTile(responseHelper);
        updateActionIdentifier(actionJson, responseHelper);
        updateVersionInfo(actionJson, responseHelper);
        updateRevertVersion(responseHelper);
        updateSheetOperationsInfo(actionJson, responseHelper);
        updateActiveInfo(actionJson, responseHelper);
        updateDocumentSettings(actionJson, responseHelper);
        updateUsageRestriction(responseHelper);
        updateDataConnectionInfo(actionJson, responseHelper);
        updateNewlyCreatedStyles(responseHelper);
        updateNamedRange(actionJson, responseHelper);
        updateFormSheets(actionJson, responseHelper);
        updatePivotInfo(actionJson, responseHelper);
        updateNewlyCreatedWorkbookImages(actionJson, responseHelper);
        updateImportedSheetInfo(actionJson, responseHelper);
        updateDiscussions(actionJson, responseHelper);
        updateDeluge(actionJson, responseHelper);
        updatePicklistDocInfo(actionJson, responseHelper);
        updateZSTheme(actionJson, responseHelper);
        updateTableInfo(actionJson, responseHelper);
        updateWorkbookLinkInfo(actionJson);
        updateCellStyles(responseHelper);
        updateTextStyles(responseHelper);
        updateFieldInfo(actionJson, responseHelper);
        updateRowHeaderDefinitions(responseHelper);
        updateColumnHeaderDefinitions(responseHelper);
        updateDocumentMeta(actionJson, responseHelper);
        updateUserMeta(responseHelper);
        updateOleObjInfo(responseHelper);
        updateWorkbookImagesInfo(actionJson, responseHelper);
        updateFontFamilyList(actionJson, responseHelper);
        updateOnLoadDetails(responseHelper);
    }

    private void setConstraintResponseHolder(ResponseHelper responseHelper)
    {
        JSONObjectWrapper actionJson = getActionJson();
        updateDataValidationRanges(actionJson, responseHelper);
        updateConditionalFormatRanges(actionJson, responseHelper);
        updateFaultySheets(actionJson, responseHelper);
        updateActiveInfoForConstraintResponse(responseHelper);
        aggregateDependentCells(actionJson, responseHelper);
        accumulateAffectedRanges(actionJson, responseHelper);
        updateColumnHeaders(actionJson, responseHelper);
        updateRowHeaders(actionJson, responseHelper);
        updateColumnLevelFormat(actionJson, responseHelper);
        updateFreezePanes(actionJson, responseHelper);
        updateHideGrid(actionJson, responseHelper);
        updateMergeCellsRange(actionJson, responseHelper);
        updateArrayFormula(actionJson, responseHelper);
        updateCellResponse(actionJson, responseHelper);
        updateCheckBoxRange(actionJson, responseHelper);
        updateFormRanges(actionJson, responseHelper);
        updateDataConnectionInfoForConstraintResponse(actionJson, responseHelper);
        updateInsertDeleteCellsInfo(actionJson, responseHelper);
        updateAppliedConditionalCellStyles(actionJson, responseHelper);
        updateHiddenRowsInfo(actionJson, responseHelper);
        updateHiddenColumnsInfo(actionJson, responseHelper);
        updateMaxUsedCells(actionJson, responseHelper);
        updateImageInfo(actionJson, responseHelper);
        updateChartInfo(actionJson, responseHelper);
        updateButtons(actionJson, responseHelper);
        updateSparklineInfo(actionJson, responseHelper);
        updateSheetZoom(actionJson, responseHelper);
        updateSheetView(actionJson, responseHelper);
        updateDataCleaningInfo(actionJson, responseHelper);
        updatePicklistRangeInfo(actionJson, responseHelper);
        updateSheetRtl(actionJson, responseHelper);
        updateGrouping(actionJson, responseHelper);
        updatePublishedViewInfo(actionJson, responseHelper);
        updateRootMergeCellInfo(actionJson, responseHelper);
        updateSlicerInfo(actionJson, responseHelper);
        updateKnitChartsInfo(actionJson, responseHelper);
        updateKnitChartModifiedInfo(responseHelper);
        updateKnitChartsSheetInfo(actionJson, responseHelper);
        updateTimelineInfo(actionJson, responseHelper);
        updateNavigatorRefreshStatus(responseHelper);
    }

    private void setUserSpecificResponseHolder(ResponseHelper responseHelper)
    {
        JSONObjectWrapper actionJson = getActionJson();
        updateActionIdentifierForUserSpecific(actionJson, responseHelper);
        updateDataValidationMessage(actionJson, responseHelper);
        updateProtectedRange(actionJson, responseHelper);
        updateProtectedSheets(actionJson, responseHelper);
        updateReplaceAllCount(actionJson, responseHelper);
        updateReplaceCount(actionJson, responseHelper);
        updateFilteredRowCount(actionJson, responseHelper);
        updateRangeHighlights(actionJson, responseHelper);
        updateUserNotification(actionJson, responseHelper);
        updateServerClipObject(responseHelper);
//        updateChartServerClipObject(responseHelper);
        updateFilterInfo(actionJson, responseHelper);
        updateKnitChartsClipInfo(responseHelper);
    }

    @Override
    public void setResponseHolder(ResponseHelper responseHelper)
    {
        // For Action Response
        // call the update functions.
        setDocumentResponseHolder(responseHelper);
        if(getConstraints() != null) {
            setConstraintResponseHolder(responseHelper);
        }
        setUserSpecificResponseHolder(responseHelper);
    }

    @Override
    public void setResponseHolderForErrorResponse(ResponseHelper responseHelper)
    {
        // For ErrorMsg Response
        JSONObjectWrapper actionJson = getActionJson();
        updateActionIdentifier(actionJson, responseHelper);
        updateActionIdentifierForUserSpecific(actionJson, responseHelper);
        updateErrorInfo(actionJson, responseHelper);
    }
    @Override
    public void setResponseHolderForSyncStyleResponse(ResponseHelper responseHelper)
    {
        // For SyncStyle Response
        JSONObjectWrapper actionJson = getActionJson();
        updateActionIdentifier(actionJson, responseHelper);
        updateSyncStyles(responseHelper);
    }

    @Override
    public void setResponseHolderForDiscussion(ResponseHelper responseHelper)
    {
        // For Discussion Response
        JSONObjectWrapper actionJson = getActionJson();
        updateActionIdentifier(actionJson, responseHelper);
        updateActionIdentifierForUserSpecific(actionJson, responseHelper);
        updateDiscussions(actionJson, responseHelper);
    }


    private void updateForceReloadTile(ResponseHelper responseHelper)
    {
        if(responseHelper.includeForceReloadTile)
        {
            getDocumentResponseHolder().setReloadTile(true);
        }
    }
    private void updateVersionInfo(JSONObjectWrapper actionJson, ResponseHelper responseHelper)
    {
        if (responseHelper.includeVersionInfo)
        {
            VersionInfoExtractor info = new VersionInfoExtractorImpl(actionJson);
            getDocumentResponseHolder().setVersionInfoBean(info.getVersionInfoBean());
        }
    }

    private void updateRevertVersion(ResponseHelper responseHelper)
    {
        if(responseHelper.isRevertVersion)
        {
            getDocumentResponseHolder().setRevertVesion(true);
        }
    }

    private	void updateDataValidationRanges(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeDataValidationRange)
        {
            DataValidationRangeInfoExtractor info = new DataValidationRangeInfoExtractorImpl(actionJson);
            List<RangeWrapper> validations = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeList()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    validations.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addDataValidations(validations);
        }
    }

    private void updateConditionalFormatRanges(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeConditionalFormatRange)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            ConditionalFormatRangeInfoExtractor info = new ConditionalFormatRangeInfoExtractorImpl(actionJson, responseObject.getCfRangeMap());
            List<RangeWrapper> cFRanges = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getCFRanges()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    cFRanges.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addConditionalFormats(cFRanges);
        }
    }

    private	void updateFaultySheets(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        /*
         * Initial Thought
         * Sheet faulty command is part of constraint exceutor and document executor
         * Reason : --
         * 			It is part of Constraint executor to avoid cell response for the sheet which is faulty
         * 			it is part of the document executor to generate the response for clients
         * 		Limitations: --		Even a particular sheet is not cached in client, then also client will get the particular sheet as faulty
         *
         *
         * Modified Version :
         * 			Now FaultySheet Command is part of constraint command, but in response structure it is part of document as it belongs to sheet
         * 			Reason to Make FaultySheet Command as constraint command : -- Sometime, active sheet also becomes part of faulty sheet, To avoid this.
         * 			We will remove the active sheet as faulty sheet & then update the response to DocumentData.
         */
        if(helper.includeFaultySheets)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            FaultySheetsInfoExtractor info = new FaultySheetsInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            HashSet<String> faultySheets = info.getSheetList();
            if (info.isMakeAllSheetsFaulty()) {
                faultySheets = new HashSet<>(Arrays.asList(getWorkbook().getAssociatedSheetNames()));
            }
            else {
                //To avoid the concurrent modification, creating new set for operations
                faultySheets = faultySheets != null ? new HashSet<>(faultySheets) : new HashSet<>();
            }
            // removing active sheet from FaultysheetList.,
            String activeSheetName = getConstraints().getContemporaryViewPort().getActiveViewPortSheetName();
            if (faultySheets.contains(activeSheetName)) {
                faultySheets.remove(activeSheetName);
                this.isActiveSheetFaulty = true;
            }
            //Keeping this to use while creating cell response
            this.faultySheets = faultySheets;
            getConstraintResponseHolder().setFaultySheets(faultySheets);
        }
    }

    private void updateActionIdentifier(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeActionIdentifier)
        {
            ActionIdentifierInfoExtractor info = new ActionIdentifierInfoExtractorImpl(actionJson);
            com.adventnet.zoho.websheet.model.response.beans.ActionIdentifierBean bean = info.getActionIdentifierBean();
            getDocumentResponseHolder().setActiveIdentifierBean(bean);
        }
    }
    private void updateActionIdentifierForUserSpecific(JSONObjectWrapper actionJson, ResponseHelper responseHelper)
    {
        if(responseHelper.includeActionIdentifier && actionJson.has(JSONConstants.ACTION))
        {
            //adding this for the purpose to get RSID & UTID in userSpecific response
            getUserSpecificResponseHolder().setActionIdentifierBean(getDocumentResponseHolder().getActiveIdetifierBean());
        }
    }

    private void updateSheetOperationsInfo(JSONObjectWrapper actionJson, ResponseHelper helper) // updateSheetMeta() in DataImpl
    {
        if(helper.includeSheetInfo)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            SheetOperationInfoExtractor info = new SheetOperationInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            getDocumentResponseHolder().setSheetWrapper(info.getSheetList());
        }
    }

    private void updateDocumentSettings(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeDocumentSettingsInfo)
        {
            DocumentSettingsInfoExtractor info = new DocumentSettingsInfoExtractorImpl(actionJson);
            getDocumentResponseHolder().setDocumentSettingsWrapper(info.getDocumentSettingsWrapper());
        }
    }

    private void updateUsageRestriction(ResponseHelper helper)
    {
        if(helper.includeUsageRestrictionInfo)
        {
            getDocumentResponse().put(JSONConstants.CHARACTERS_IN_SPREADSHEET, getWorkbook().reviseStringLength(0));
        }
    }

    private	void updateNewlyCreatedStyles(ResponseHelper helper)
    {
        ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
        HashMap<String,Set<String>> newlyCreatedStylesKeyInfo = responseObject.getNewlyCreatedStylesKeyInfo();
        if(helper.includeNewlyCreatedStyles && newlyCreatedStylesKeyInfo != null)
        {
            getDocumentResponseHolder().setNewlyCreatedStyles(newlyCreatedStylesKeyInfo);
        }
    }

    /*
     * Aggregating all the dependent cells which can be sent to client with data as well to Highlight the cells...,
     */
    private void aggregateDependentCells(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeDependentCells)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            AggregateDependentCellInfoExtractor info  = new AggregateDependentCellInfoExtractorImpl(actionJson);
            this.srcCells = responseObject.getSrcCells();
            HashSet<Cell> dependentCells = responseObject.getDependentCells();
            //To avoid the concurrent modification, creating new set for operations
            dependentCells = dependentCells == null ? new HashSet<>() : new HashSet<>(dependentCells);
            MacroResponse macroResponse = responseObject.getMacroResponse();
            if(macroResponse != null)
            {
                Sheet activeSheet = getWorkbook().getSheetByAssociatedName(getConstraints().getContemporaryViewPort().getActiveViewPortSheetName());
                if(macroResponse.IsCalculate() && activeSheet != null)
                {
                    dependentCells.addAll(activeSheet.getFormulaCells());
                }
                else
                {
                    dependentCells.addAll(macroResponse.getDependentCellList());
                }
            }
            this.dependentCells = dependentCells;
        }
    }

    /*
     * Extracting ranges and keep in a bean named as RangeWrapper ...
     */
    private void accumulateAffectedRanges(JSONObjectWrapper actionJson, ResponseHelper responseHelper)
    {
        if(actionJson.has(JSONConstants.ACTION) && responseHelper.includeCellResponse) // used only for GridAction
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            RangeAccumulatorInfoExtractor info = new RangeAccumulatorInfoExtractorImpl(actionJson, responseObject.getDestRanges(), responseObject.getMacroResponse());
            this.rangeList = info.getRangeWrapperList();
        }
    }

    private void updateColumnHeaders(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if (helper.includeColumnHeaders)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            ColumnHeaderInfoExtractor info = new ColumnHeaderInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            List<RangeWrapper> colHeaderRangeList = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeWrapperList()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    colHeaderRangeList.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addColumnHeaders(colHeaderRangeList);
        }
    }

    private void updateRowHeaders(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeRowHeaders)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            RowHeadersInfoExtractor info = new RowHeadersInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            List<RangeWrapper> rowHeaders = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeWrapperList()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    rowHeaders.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addRowHeaders(rowHeaders);
        }
    }

    private void updateColumnLevelFormat(JSONObjectWrapper	actionJson, ResponseHelper responseHelper)
    {
        if(responseHelper.includeColumnLevelFormats)
        {
            ColumnLevelFormatInfoExtractor info =  new ColumnLevelFormatInfoExtractorImpl(actionJson);
            List<RangeWrapper> columnHeaderList = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeWrapperList()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    columnHeaderList.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addColumnLevelFormats(columnHeaderList);
        }
    }

    private void updateFreezePanes(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeFreezePanesInfo)
        {
            FreezePanesInfoExtractor info = new FreezePanesInfoExtractorImpl(actionJson);
            List<FreezeWrapper> freezePanes = new ArrayList<>();
            for (FreezeWrapper freezePane : info.getFreezeWrapperList()) {
                if (getConstraints().isSheetInViewPort(freezePane.getAssociatedSheetName())) {
                    freezePanes.add(freezePane);
                }
            }
            getConstraintResponseHolder().addFreezePanes(freezePanes);
        }
    }

    private void updateHideGrid(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.isHideGrid)
        {
            HideGridInfoExtractor info = new HideGridInfoExtractorImpl(actionJson);
            List<HideGridWrapper> hideGrids = new ArrayList<>();
            for (HideGridWrapper hideGrid : info.getHideGridWrapperList()) {
                if (getConstraints().isSheetInViewPort(hideGrid.getAssociatedSheetName())) {
                    hideGrids.add(hideGrid);
                }
            }
            getConstraintResponseHolder().addHideGrid(hideGrids);
        }
    }

    private void updateMergeCellsRange(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeMergeCells)
        {
            MergeCellsInfoExtractor info = new MergeCellsInfoExtractorImpl(actionJson);
            List<RangeWrapper> mergeList = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeWrapperList()) {
                //Handle merge action Type , merge or unmerge
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    mergeList.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addMergeCells(mergeList);
        }
    }

    private void updateArrayFormula(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeArrayFormulasRange)
        {
            ArrayFormulaInfoExtractor info = new ArrayFormulaInfoExtractorImpl(actionJson);
            List<RangeWrapper> arrayFormulas = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeWrapperList()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    arrayFormulas.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addArrayFormulas(arrayFormulas);
        }
    }

    private	void updateDataValidationMessage(JSONObjectWrapper	actionJson, ResponseHelper helper)
    {
        if(helper.includeDataValidationMsg)
        {
            DataValidationMessageInfoExtractor info = new DataValidationMessageInfoExtractorImpl(actionJson);
            com.adventnet.zoho.websheet.model.response.beans.DataValidationMessageInfo dataValidationMessageInfo = info.getDataValidationMessageInfo();
            if(dataValidationMessageInfo != null)
            {
                getUserSpecificResponseHolder().setDataValidationMessage(dataValidationMessageInfo);
            }
        }
    }

    private	void updateCellResponse(JSONObjectWrapper	actionJson,	ResponseHelper	helper)
    {
        //Need to boolean for scroll and load
        if(helper.includeCellResponse)
        {
            ConstraintResponseHolder responseHolder = getConstraintResponseHolder();
            FetchDataInfoExtractor info;
            CellResponse cellResponse;
            if(helper.isFetchDataAction) //Fetch(Scroll) data
            {
                info = new FetchDataInfoExtractorImpl(actionJson);
                cellResponse = new CellResponseJsonImpl(getWorkbook(), info.getSheetName(), info.getRanges(), getConstraints());
            }
            else if(actionJson.has(JSONConstants.ACTION)) // GridAction
            {
                cellResponse = getCellData();
                HashMap<String, List<Long>> formualsCellMap = getDependentCellsList();
                responseHolder.addDependentCellsMap(formualsCellMap);
            }
            else //Document load -- Not using as of now --- reason :-- getting sheet data as fetch data
            {
                info = new FetchDataInfoExtractorImpl(actionJson);
                JSONArrayWrapper rangeAry = info.getRanges();
                cellResponse = new CellResponseJsonImpl(getWorkbook(), info.getSheetName(), rangeAry.getInt(0), rangeAry.getInt(1), rangeAry.getInt(2), rangeAry.getInt(3), getConstraints());
            }
            responseHolder.addCellResponse(cellResponse);
            responseHolder.setCellMetaHeader(getConstraints().getCellMetaBit());
        }
    }

    private CellResponse getCellData()
    {
        HashSet<Cell> cellsForResponse = trimCellsList();
        CellResponse cellResponse = new CellResponseJsonImpl(getWorkbook(), this.rangeList, cellsForResponse, getConstraints(), this.faultySheets, this.isActiveSheetFaulty);
        return cellResponse;
    }

    private boolean isCellLiesInRange(RangeWrapper rangeWrapper, Cell cell)
    {
        int cellRowIndex = cell.getRowIndex();
        int cellColIndex = cell.getColumnIndex();
        if ((rangeWrapper.getSheetName().equals(cell.getRow().getSheet().getName()))
                && (rangeWrapper.getstartRow() >= cellRowIndex && rangeWrapper.getEndRow() <= cellRowIndex)
                && (rangeWrapper.getstartCol() >= cellColIndex && rangeWrapper.getEndCol() <= cellColIndex)) {
            return true;
        }
        return false;
    }

    private HashSet<Cell> mergeCellsList(HashSet<Cell> dependentCells, List<Cell> srcCells)
    {
        if (dependentCells == null) {
            return srcCells != null ? new HashSet<>(srcCells) : new HashSet<>();	// Need to check this line -- Compatability
        }

        if (srcCells != null) {
            dependentCells.addAll(srcCells);
        }
        return dependentCells;
    }

    private HashSet<Cell> trimCellsList()
    {
        boolean excludeCell;
        HashSet<Cell> tempDependentCells = null;
        HashSet<Cell>	dependentCells = this.dependentCells;
        if (dependentCells != null)
        {
            //To avoid the concurrent modificatoin, creating new set for operations
            tempDependentCells = new HashSet<>(dependentCells);
            for (Cell cell : tempDependentCells) {
                for (RangeWrapper rangeWrapper : this.rangeList) {
                    excludeCell = isCellLiesInRange(rangeWrapper, cell);
                    if (excludeCell) {
                        tempDependentCells.remove(cell);
                        // excludedCell.add(cell);
                    }
                }
            }
        }
        List<Cell> tempSrcCells = null;
        List<Cell> srcCells = this.srcCells;
        if (srcCells != null)
        {
            //To avoid the concurrent modificatoin, creating new list for operations
            tempSrcCells = new ArrayList<>(srcCells);
            for (Cell cell : tempSrcCells) {
                for (RangeWrapper rangeWrapper : this.rangeList) {
                    excludeCell = isCellLiesInRange(rangeWrapper, cell);
                    if (excludeCell) {
                        tempSrcCells.remove(cell);
                    }
                }
            }
        }
        HashSet<Cell> cellsForResponse = mergeCellsList(tempDependentCells, tempSrcCells);
        return cellsForResponse;
    }

    private HashMap<String, List<Long>> getDependentCellsList()
    {
        // Send indexes of cells to highlight in client
        HashMap<String, List<Long>> formulaCellsMap = new HashMap<String, List<Long>>();
        List<Long> formulasCellsList;
        String sheetName;
        HashSet<Cell> dependentCells = this.dependentCells;
        if (dependentCells != null && !dependentCells.isEmpty())
        {
            boolean isActiveCell;
            for (Cell cell : dependentCells) {
                isActiveCell = getConstraints().isCellInActiveViewPort(cell);
                //Need to add the check for faulty sheet, if it is faulty sheet, no need to update
                if (isActiveCell) {
                    sheetName = cell.getRow().getSheet().getAssociatedName();
                    formulasCellsList = formulaCellsMap.containsKey(sheetName) ? formulaCellsMap.get(sheetName) : new ArrayList<Long>();

                    formulasCellsList.add(new Long((Utility.MAXNUMOFCOLS) * cell.getRowIndex() + cell.getColumnIndex()));

                    formulaCellsMap.put(sheetName, formulasCellsList);

                }
            }
        }
        return formulaCellsMap;
    }

    private void updateProtectedRange(JSONObjectWrapper actionJson, ResponseHelper	helper)
    {
        if(helper.includeProtectedRange)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            ProtectedRangeInfoExtractor info = new ProtectedRangeInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            getUserSpecificResponseHolder().setPrtoectedRangeBeans(info.getProtectionBeans());
        }
    }

    private void updateProtectedSheets(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeProtectedSheet)
        {
            ProtectedSheetInfoExtractor info = new ProtectedSheetInfoExtractorImpl(actionJson);
            getUserSpecificResponseHolder().setPrtoectedSheetBeans(info.getProtectionBeans());
        }
    }

    private void updateCheckBoxRange(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeCheckBoxRange)
        {
            CheckBoxInfoExtractor info = new CheckBoxInfoExtractorImpl(actionJson);
            List<RangeWrapper> checkBoxList = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeWrapperList()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    checkBoxList.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addCheckBox(checkBoxList);
        }
    }

    private void updateNamedRange(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeNamedRangeInfo)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            NamedRangeInfoExtractor info = new NamedRangeInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            getDocumentResponseHolder().setNamedRangeWrapper(info.getNamedRangeWrapper());
        }
    }

    private void updateFormRanges(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeFormRange)
        {
            FormRangeInfoExtractor info = new FormRangeInfoExtractorImpl(actionJson);
            RangeWrapper rangeWrapper = info.getRangeWrapper();
            if(getConstraints().isSheetInViewPort(rangeWrapper.getSheetName()))
            {
                getConstraintResponseHolder().addFormRange(rangeWrapper);
            }
        }
    }

    private void updateFormSheets(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeFormSheets)
        {
            FormSheetsInfoExtractor info = new FormSheetsInfoExtractorImpl(actionJson);
            getDocumentResponseHolder().setFormBean(info.getFormBean());
        }
    }

    private void updatePivotInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includePivotInfo)
        {
            PivotInfoExtractor info = new PivotInfoExtractorImpl(actionJson);
            getDocumentResponseHolder().setPivotBean(info.getPivotBeanList());
        }
    }

    private void updateDataConnectionInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeDataConnectionInfo && !actionJson.has(JSONConstants.ACTION)) // for Fetch
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            DataConnectionInfoExtractor info = new DataConnectionInfoExtractorImpl(actionJson, responseObject.getDataConnectionBean());
            getDocumentResponseHolder().setDataConnectionBean(info.getDataConnectionBean());
        }
    }

    private void updateDataConnectionInfoForConstraintResponse(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeDataConnectionInfo && actionJson.has(JSONConstants.ACTION)) // for GridAction
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            DataConnectionInfoExtractor info = new DataConnectionInfoExtractorImpl(actionJson, responseObject.getDataConnectionBean());
            getConstraintResponseHolder().setDataConnectionBean(info.getDataConnectionBean());
        }
    }

    private	void updateActiveInfo(JSONObjectWrapper actionJson, ResponseHelper	helper)
    {
        if(helper.includeActiveInfo)
        {
            ActiveInfoExtractor info = new ActiveInfoExtractorImpl(actionJson);
            com.adventnet.zoho.websheet.model.response.beans.ActiveInfoBean activeInfoBean = info.getActiveInfoBean();
            getDocumentResponseHolder().setActiveInfoBean(activeInfoBean);
        }
    }

    private void updateActiveInfoForConstraintResponse(ResponseHelper helper)
    {
        if(helper.includeActiveInfo && helper.includeSheetActiveInfo)
        {
            getConstraintResponseHolder().setActiveInfoBean(getDocumentResponseHolder().getActiveInfoBean());
        }
    }

    private void updateInsertDeleteCellsInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeInsertDeleteCells)
        {
            InsertDeleteCellsInfoExtractor info = new InsertDeleteCellsInfoExtractorImpl(actionJson);
            List<RangeWrapper> deleteCellRanges = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeWrapperList()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    deleteCellRanges.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().setInsertDeleteCellRange(deleteCellRanges);
        }
    }

    private	void updateAppliedConditionalCellStyles(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeAppliedConditionalCellStyles)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            AppliedConditionalCellStylesToRangeInfoExtractor info = new AppliedConditionalCellStylesToRangeInfoExtractorImpl(actionJson, responseObject.getCfRangeMap());
            Map<String, List<RangeWrapper>> cfRangeWrapperMap = info.getRangeWrapperMap();
            Constraints constraints = getConstraints();
            ConstraintResponseHolder responseHolder = getConstraintResponseHolder();
            Map<String,List<RangeWrapper>> cfMap = new HashMap<>();
            Map<String,List<RangeWrapper>> cfFaultyMap = new HashMap<>();
            if (cfRangeWrapperMap != null && !cfRangeWrapperMap.isEmpty()) {
                for(String sheetName : cfRangeWrapperMap.keySet()) {
                    List<RangeWrapper> list = cfRangeWrapperMap.get(sheetName);
                    if(list.isEmpty() || !constraints.isSheetInViewPort(sheetName)) {
                        /* Skipping sheet if it not is viewport */
                        continue;
                    }
                    RangeWrapper tempRange = list.get(0);
                    if (tempRange.getOperationType() == CommandConstants.OperationType.APPLIED_CONDITIONAL_FORMATS) {
                        //assumption :- it is for fetch data
                        if (constraints.isSheetInViewPort(tempRange.getSheetName())) {
                            cfMap.put(sheetName,list);
                            responseHolder.setCFRangeList(cfMap);
                            break;  // Fetch is for only one sheet.
                        }
                    } else {
                        //assumption :- it is for Grid actions
                        List<RangeWrapper> cfRangeWrapperList = new ArrayList<>();
                        List<RangeWrapper> faultyRangeList = new ArrayList<>();
                        List<Area> activeAreas = null;
                        if (constraints.isSheetInActiveViewPort(sheetName)) {
                            ArrayList<Area> areas  = constraints.getContemporaryViewPort().getActiveViewPortAreas(sheetName);
                            if(areas != null) {
                                //To avoid the concurrent modification, creating new list for operations
                                activeAreas = new ArrayList<Area>(areas);
                                for (Area area : activeAreas) {
                                    for (RangeWrapper wrapper : list) {
                                        DataRange styleDataRange = new DataRange(sheetName, wrapper.getstartRow(), wrapper.getstartCol(), wrapper.getEndRow(), wrapper.getEndCol());
                                        DataRange areaDataRange = new DataRange(sheetName, area.getStartRow(), area.getStartCol(), area.getEndRow(), area.getEndCol());
                                        DataRange intersection = RangeUtil.intersection(styleDataRange, areaDataRange);
                                        if (intersection != null) {
                                            ResponseUtils.mergeAndAddRangeWrapperToList(new RangeWrapper(sheetName, intersection.getStartRowIndex(), intersection.getStartColIndex(), intersection.getEndRowIndex(), intersection.getEndColIndex(), wrapper.getOperationType()), cfRangeWrapperList);
                                        }
                                    }
                                }
                            }
                        }
                        for(RangeWrapper wrapper : list){
                            for(RangeWrapper rangeNotInVP : constraints.getContemporaryViewPort().getRangeNonIntersectingWithDomViewPort(wrapper.getSheetName(), wrapper.getstartRow(), wrapper.getstartCol(), wrapper.getEndRow(), wrapper.getEndCol())){
                                ResponseUtils.mergeAndAddRangeWrapperToList(new RangeWrapper(rangeNotInVP.getSheetName(), rangeNotInVP.getstartRow(), rangeNotInVP.getstartCol(), rangeNotInVP.getEndRow(), rangeNotInVP.getEndCol(), wrapper.getOperationType()), faultyRangeList);
                            }
                        }
                        if(!cfRangeWrapperList.isEmpty()) {
                            cfMap.put(sheetName, cfRangeWrapperList);
                        }
                        if(!faultyRangeList.isEmpty()) {
                            cfFaultyMap.put(sheetName, faultyRangeList);
                        }
                    }
                }
            }
            responseHolder.setCFRangeList(cfMap);
            responseHolder.setCFFaultyRangeList(cfFaultyMap);
        }
    }

    private void updateHiddenRowsInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeHiddenRows)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            RowHiddenInfoExtractor info = new RowHiddenInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            List<RangeWrapper> hiddenRows = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeWrapperList()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    hiddenRows.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addHiddenRows(hiddenRows);
        }
    }

    private void updateHiddenColumnsInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeHiddenColumns)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            ColumnHiddenInfoExtractor info = new ColumnHiddenInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            List<RangeWrapper> hiddenCols = new ArrayList<>();
            for (RangeWrapper rangeWrapper : info.getRangeWrapperList()) {
                if (getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    hiddenCols.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().addHiddenColumns(hiddenCols);
        }
    }

    private void updateMaxUsedCells(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeMaxUsedCell)
        {
            MaxUsedCellInfoExtractor info = new MaxUsedCellInfoExtractorImpl(actionJson);
            RangeWrapper rangeWrapper = info.getRangeWrapper();
            if(rangeWrapper != null && getConstraints().isSheetInActiveViewPort(rangeWrapper.getSheetName()))
            {
                getConstraintResponseHolder().setMaxUsedCells(rangeWrapper);
            }
        }
    }

    private void updateImageInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeImage)
        {
            SheetImageInfoExtractor info = new SheetImageInfoExtractorImpl(actionJson);
            List<com.adventnet.zoho.websheet.model.response.beans.SheetImageBean> imageBeans = info.getImageBeanList();
            if(imageBeans != null && !imageBeans.isEmpty())
            {
                List<com.adventnet.zoho.websheet.model.response.beans.SheetImageBean> imageBeanList = new ArrayList<>();
                for(com.adventnet.zoho.websheet.model.response.beans.SheetImageBean imageBean : imageBeans) {
                    if(getConstraints().isSheetInViewPort(imageBean.getSheetAsn())) {
                        imageBeanList.add(imageBean);
                    }
                }
                getConstraintResponseHolder().setImageBean(imageBeanList);
            }
        }
    }

    private void updateNewlyCreatedWorkbookImages(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeNewlyCreatedWorkbookImages)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            NewlyCreatedWorkbookImagesInfoExtractor info = new NewlyCreatedWorkbookImagesInfoExtractorImpl(actionJson, responseObject.getWorkbookImageIds());
            DocumentResponseHolder responseHolder = getDocumentResponseHolder();
            responseHolder.setImageMetaBeanList(info.getImageMetaBeanList());
            responseHolder.setPublishRangeId(info.getPublishRangeId());
        }
    }

    private void updateChartInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeChartInfo)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            ChartInfoExtractor info = new ChartInfoExtractorImpl(actionJson, responseObject.getAffectedChartDetails());
            List<com.adventnet.zoho.websheet.model.response.beans.ChartBean> chartBeans = info.getChartBeanList();
            if(chartBeans != null && !chartBeans.isEmpty()) {
                List<com.adventnet.zoho.websheet.model.response.beans.ChartBean> chartBeanList = new ArrayList<>();
                for (com.adventnet.zoho.websheet.model.response.beans.ChartBean chartBean : chartBeans) {
                    if (getConstraints().isSheetInViewPort(chartBean.getSheetName())) {
                        chartBeanList.add(chartBean);
                    }
                }
                getConstraintResponseHolder().setChartBean(chartBeanList);
            }
        }
    }

    private void updateKnitChartsInfo(JSONObjectWrapper actionJson, ResponseHelper helper) {
        if(helper.includeKnitChartsInfo) {
            KnitChartsInfoExtractor infoExtractor = new KnitChartsInfoExtractorImpl(actionJson, getConstraints());
            List<KnitChartsBean> knitChartsBeans = infoExtractor.getChartsInfo();

            if(knitChartsBeans != null && !knitChartsBeans.isEmpty()) {
                getConstraintResponseHolder().setKnitChartsBeans(Collections.unmodifiableList(knitChartsBeans));
            }
        }
    }

    private void updateKnitChartsSheetInfo(JSONObjectWrapper actionJson, ResponseHelper helper) {
        if(helper.includeKnitChartsSheetInfo) {
            KnitChartsSheetInfoExtractor infoExtractor = new KnitChartsSheetInfoExtractorImpl(actionJson);
            List<KnitChartsSheetBean> knitChartsSheetBeans = infoExtractor.getChartsSheetInfo();

            if(knitChartsSheetBeans != null && !knitChartsSheetBeans.isEmpty()) {
                getConstraintResponseHolder().setKnitChartsSheetBeans(Collections.unmodifiableList(knitChartsSheetBeans));
            }
        }
    }

    private void updateKnitChartModifiedInfo(ResponseHelper helper) {
        ResponseObject responseObject = getResponseObject();
        if(helper.includeKnitChartModifiedInfo && responseObject instanceof ActionResponseObject) {
            ActionResponseObject actionResponseObject = (ActionResponseObject) responseObject;
            KnitChartsModifiedInfoExtractor infoExtractor = new KnitChartsModifiedInfoExtractorImpl(actionResponseObject.getModifiedChartBeans(), getConstraints());
            List<KnitChartsModifiedBean> knitChartsModifiedBeans = infoExtractor.getModifiedChartsInfo();
            if(knitChartsModifiedBeans != null && !knitChartsModifiedBeans.isEmpty()) {
                getConstraintResponseHolder().setKnitChartsModifiedBeans(Collections.unmodifiableList(knitChartsModifiedBeans));
            }
        }
    }

    private void updateNavigatorRefreshStatus(ResponseHelper helper) {
        ResponseObject responseObject = getResponseObject();
        if(responseObject instanceof ActionResponseObject) {
            ActionResponseObject actionResponseObject = (ActionResponseObject) responseObject;

            getDocumentResponseHolder().setNeedToRefreshNavigator(actionResponseObject.isNeedToRefreshNavigator() || helper.includeNavMetaVersion);
        }
    }

    private void updateKnitChartsClipInfo(ResponseHelper helper) {
        if(helper.includeKnitChartClipInfo) {
            KnitChartsClipInfoExtractor infoExtractor = new KnitChartsClipInfoExtractorImpl(getUserInfo());
            KnitChartsClipBean knitChartsClipBean = infoExtractor.getChartsClipInfo();

            if(knitChartsClipBean != null) {
                getUserSpecificResponseHolder().setKnitChartsClipBean(knitChartsClipBean);
            }
        }
    }

    private void updateImportedSheetInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeImportResponse)
        {
            ImportInfoExtractor info = new ImportInfoExtractorImpl(actionJson);
            getDocumentResponseHolder().setImportBeans(info.getImportBeanList());
        }
    }

    private void updateReplaceAllCount(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeReplaceAllCount)
        {
            com.adventnet.zoho.websheet.model.response.beans.FindReplaceResponseBean findReplaceResponseBean = new com.adventnet.zoho.websheet.model.response.beans.FindReplaceResponseBean(actionJson.getInt(JSONConstants.NO_OF_MATCH), actionJson.getInt(JSONConstants.MATCH_AT));
            if(actionJson.has(JSONConstants.NO_OF_REPLACE)) {
                findReplaceResponseBean.setNoOfReplaces(actionJson.getInt(JSONConstants.NO_OF_REPLACE));
            }
            getUserSpecificResponseHolder().setFindReplaceResponseBean(findReplaceResponseBean);
        }
    }

    private void updateReplaceCount(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeReplaceCount)
        {
            com.adventnet.zoho.websheet.model.response.beans.FindReplaceResponseBean findReplaceResponseBean = new com.adventnet.zoho.websheet.model.response.beans.FindReplaceResponseBean(1, 0);
            findReplaceResponseBean.setNoOfReplaces(actionJson.getInt(JSONConstants.NO_OF_REPLACE));
            findReplaceResponseBean.setIncludeReplaceCountOnly(true);
            getUserSpecificResponseHolder().setFindReplaceResponseBean(findReplaceResponseBean);
        }
    }

    private void  updateFilteredRowCount(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeFilterRowCount)
        {
            getUserSpecificResponseHolder().setFilteredRowCount(actionJson.getInt(JSONConstants.FILTERED_ROW_COUNT));
        }
    }

    private void updateRangeHighlights(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeRangeHighlight)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            RangeHighlighterInfoExtractor info = new RangeHighlighterInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            getUserSpecificResponseHolder().setRangeHighlight(info.getRangeWrapperList());
        }
    }

    private void updateUserNotification(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeUserNotification)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            UserNotificationInfoExtractor info = new UserNotificationInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            List<com.adventnet.zoho.websheet.model.response.beans.NotificationBean> notificationBeans = info.getNotificationBeanList();
            if(!notificationBeans.isEmpty())
            {
                getUserSpecificResponseHolder().setUserNotification(notificationBeans);
            }
        }
    }

    private void updateButtons(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeButtons)
        {
            ButtonInfoExtractor info = new ButtonInfoExtractorImpl(actionJson);
            List<com.adventnet.zoho.websheet.model.response.beans.ButtonBean> buttonBeans = info.getButtonBeanList();
            if(!buttonBeans.isEmpty()){
                List<com.adventnet.zoho.websheet.model.response.beans.ButtonBean> buttonBeanList = new ArrayList<>();
                for (com.adventnet.zoho.websheet.model.response.beans.ButtonBean buttonBean : buttonBeans) {
                    if (getConstraints().isSheetInActiveViewPort(buttonBean.getSheetName())) {
                        buttonBeanList.add(buttonBean);
                    }
                }
                getConstraintResponseHolder().setButtonBean(buttonBeanList);
            }
        }
    }
    private void updateSparklineInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeSparkline || (helper.includeCellResponse && actionJson.has(JSONConstants.ACTION)))
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            SparklineInfoExtractor info = new SparklineInfoExtractorImpl(actionJson, responseObject.getModifiedSparklineGroups(), responseObject.getDestRanges(), responseObject.getModifiedSparklineCells());
            List<com.adventnet.zoho.websheet.model.response.beans.SparklineBean> beanList = info.getSparklineBeanList();
            Constraints constraints = getConstraints();
            Workbook workbook = getWorkbook();
            List<com.adventnet.zoho.websheet.model.response.beans.SparklineBean> newBeanList = new ArrayList<>();
            for(com.adventnet.zoho.websheet.model.response.beans.SparklineBean bean : beanList) {
                // creating new modifiedSparklinesGroups as arraylist to avoid concurrent exception
                List<SparklinesGroup> modifiedSparklinesGroups = bean.getModifiedSparklinesGroupList() != null ? new ArrayList<>(bean.getModifiedSparklinesGroupList()) : new ArrayList<SparklinesGroup>();
                Collection<String> faultySheetList = new HashSet<>();
                CommandConstants.OperationType operationType = bean.getOperationType();
                String associatedSheetName = bean.getAssociatedSheetName();

                List<DataRange> sourceDataRanges = bean.getSourceRange();
                List<DataRange> modifiedCellList = new ArrayList<>();
                List<RangeWrapper> destinationRangeList = new ArrayList<>();

                if (!modifiedSparklinesGroups.isEmpty()) {
                    Iterator<SparklinesGroup> iter = modifiedSparklinesGroups.iterator();
                    while (iter.hasNext()) {
                        SparklinesGroup sparklinesGroup = iter.next();
                        List<SparklinesGroup.Sparkline> sparklineList = sparklinesGroup.getSparklinesList();
                        if (!sparklineList.isEmpty()) {
                            DataRange destinationRange = sparklineList.get(0).getDestinationRange();
                            if(constraints != null && !constraints.isSheetInActiveViewPort(destinationRange.getAssociatedSheetName()) && bean.getOperationType() != CommandConstants.OperationType.PUBLISH) {
                                faultySheetList.add(sparklinesGroup.getSparklinesList().get(0).getDestinationRange().getAssociatedSheetName());
                            }
                            if(constraints != null && !constraints.isSheetInViewPort(destinationRange.getAssociatedSheetName())) {
                                iter.remove();
                            }
                        }
                    }
                }
                /* In case of delete row/col. If sparkline is above the deleted row or col, it won't be redrawn in client. */
                if (operationType == CommandConstants.OperationType.DELETE) {
                    for (SparklinesGroup sparklinesGroup : modifiedSparklinesGroups) {
                        for (SparklinesGroup.Sparkline sparkline : sparklinesGroup.getSparklinesList()) {
                            DataRange range = sparkline.getDestinationRange();
                            if (constraints != null && constraints.isRangeInDomViewPort(new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex()))) {
                                RangeIterator iter = new RangeIterator(workbook.getSheetByAssociatedName(range.getAssociatedSheetName()), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, true, false, true, false, true, true);
                                while (iter.hasNext()) {
                                    ReadOnlyCell rCell = iter.next();
                                    modifiedCellList.add(new DataRange(range.getAssociatedSheetName(), rCell.getRowIndex(), rCell.getColIndex(), rCell.getRowIndex(), rCell.getColIndex()));
                                }
                            }
                        }
                    }
                    modifiedSparklinesGroups = null;
                    operationType = CommandConstants.OperationType.INSERT;
                }
                /* On theme change, sparkline meta has to be generated for all the cached sheets. */
                if(operationType == CommandConstants.OperationType.THEME) {
                    for(Sheet sheet : workbook.getSheetList()) {
                        if(constraints != null && constraints.isSheetInViewPort(sheet.getAssociatedName())) {
                            faultySheetList.add(sheet.getAssociatedName());
                        }
                    }
                }
                if(bean.getModifiedCells() != null) {
                    for (DataRange dr : bean.getModifiedCells()) {
                        if (constraints != null && constraints.isRangeInDomViewPort(new RangeWrapper(dr.getAssociatedSheetName(), dr.getStartRowIndex(), dr.getStartColIndex(), dr.getEndRowIndex(), dr.getEndColIndex()))) {
                            modifiedCellList.add(dr);
                        }
                    }
                }
                newBeanList.add(new com.adventnet.zoho.websheet.model.response.beans.SparklineBean(modifiedSparklinesGroups, faultySheetList, sourceDataRanges, destinationRangeList, associatedSheetName, modifiedCellList, operationType, bean.isValueChanged(), bean.isFromUndoRedo(), bean.isSparklineAction(), bean.getSparklineID(), bean.getTableIDs()));
            }
            getConstraintResponseHolder().setSparklineBeanList(newBeanList);
        }
    }

    private void updateDiscussions(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeDiscussions)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            JSONObjectWrapper disIdObj = responseObject.getDisIdObj();
            if(actionJson.optBoolean("isFetchAction")) // for Fetch
            {
                UserProfile.AccessType accessType = CurrentRealm.getAccessIdentity();
                JSONObjectWrapper previlages = actionJson.has(JSONConstants.CAPABILITIES) ? actionJson.getJSONObject(JSONConstants.CAPABILITIES) : null;
                if(previlages != null)
                {
                    boolean canCreateComment = previlages.getBoolean("canCreateComment"); //NO I18N
                    if ((accessType == UserProfile.AccessType.AUTH && IAMUtil.getCurrentUser() != null) || canCreateComment)
                    {
                        WorkbookContainer container = getContainer();
                        String zuid = IAMUtil.getCurrentUser() != null ? IAMUtil.getCurrentUser().getZuid() : container.getCollabId();
                        UserProfile userProfile = CurrentRealm.getUserProfile();
                        actionJson.put("zuid", zuid);
                        boolean isCoOwner = false;
                        if (userProfile != null) {
                            isCoOwner = (userProfile.getPermissionType() == UserProfile.PermissionType.READ_WRITE_SAVE_SHARE);
                        }
                        actionJson.put("hasCoOwnerPerm", isCoOwner);
                        try
                        {
                            disIdObj = new Discussions(container, actionJson).get();
                        }
                        catch (Exception e)
                        {
                            LOGGER.log(Level.WARNING, "Exception while getting discussion Object - ResponseAnalyzer", e);
                        }
                    }
                }
            }
            if(disIdObj != null)
            {
                DiscussionInfoExtractor info = new DiscussionInfoExtractorImpl(actionJson, disIdObj);
                getDocumentResponseHolder().setDiscussionBean(info.getDiscussionBean());
            }
        }
    }

    private void updateDeluge(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeDeluge)
        {
            DelugeFunctionsInfoExtractor info = new DelugeFunctionsInfoExtractorImpl(actionJson);
            getDocumentResponseHolder().setDelugeFunctionsList(info.getDelugeFunctionsBeanList());
        }
    }

    private void updateSheetZoom(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeZoomMeta)
        {
            SheetZoomInfoExtractor info = new SheetZoomInfoExtractorImpl(actionJson);
            getConstraintResponseHolder().setSheetZoomList(info.getSheetWrapperList());
        }
    }

    private void updateSheetView(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeSheetView)
        {
            SheetViewInfoExtractor info = new SheetViewInfoExtractorImpl(actionJson);
            getConstraintResponseHolder().setSheetViewList(info.getSheetWrapperList());
        }
    }

    private void updateDataCleaningInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(DataCleaningInfoExtractorImpl.isValidToExecute(actionJson, helper))
        {
            DataCleaningInfoExtractor info = new DataCleaningInfoExtractorImpl(actionJson);
            com.adventnet.zoho.websheet.model.response.beans.DataCleaningBean bean = info.getDataCleaningBean();
            List<DataRange> modifiedRanges = new ArrayList();
            if(!(bean.getOperationType() == CommandConstants.OperationType.MODIFY || bean.isUndoAction())) {
                if(bean.getOperationType() == CommandConstants.OperationType.REPLACE) {
                    if(this.srcCells != null) {
                        for(Cell cell : this.srcCells) {
                            modifiedRanges.add(new DataRange(cell.getRow().getSheet().getAssociatedName(),cell.getRowIndex(),cell.getColumnIndex(),cell.getRowIndex(),cell.getColumnIndex()));
                        }
                    }
                }
                bean = new com.adventnet.zoho.websheet.model.response.beans.DataCleaningBean(bean.getOperationType(),bean.getAssociatedSheetName(),bean.getUpdatedCount(),bean.getDuplicateCount(),bean.isUndoAction(),bean.getActionType(), modifiedRanges);
            }
            getConstraintResponseHolder().setDataCleaningBean(bean);
        }
    }

    private void updatePicklistDocInfo(JSONObjectWrapper actionJson, ResponseHelper responseHelper)
    {
        boolean isUndoAction = actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO);
        ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
        Set<Picklist> modifiedPicklists = responseObject.getModifiedPicklists();
        if(isUndoAction || responseHelper.includePicklistDocInfo || (modifiedPicklists != null && !modifiedPicklists.isEmpty()))
        {
            PicklistInfoExtractor info = new PicklistInfoExtractorImpl(actionJson, modifiedPicklists, responseObject.getMacroResponse());
            getDocumentResponseHolder().setPicklistBean(info.getPicklistBean());
        }
    }

    private void updatePicklistRangeInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includePicklistRangeinfo)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            PicklistRangeInfoExtractor info = new PicklistRangeInfoExtractorImpl(actionJson, responseObject.getDestRanges());
            List<com.adventnet.zoho.websheet.model.response.beans.PicklistBean> beanList = info.getPicklistBeanList();
            List<com.adventnet.zoho.websheet.model.response.beans.PicklistBean> newBeanList = new ArrayList<>();
            for(com.adventnet.zoho.websheet.model.response.beans.PicklistBean bean : beanList) {
                if (bean != null) {
                    Map<String, List<RangeWrapper>> newMap = new HashMap<>();
                    for (Map.Entry<String, List<RangeWrapper>> entry : bean.getRanges().entrySet()) {
                        if (getConstraints().isSheetInViewPort(entry.getKey())) {
                            newMap.put(entry.getKey(), entry.getValue());
                        }
                    }
                    bean = new com.adventnet.zoho.websheet.model.response.beans.PicklistBean(bean.getOperationType(), newMap, bean.getPicklistID(), bean.getAssociatedSheetName(), bean.isFromUndo());
                    newBeanList.add(bean);
                }
            }
            getConstraintResponseHolder().setPicklistBean(newBeanList);
        }
    }

    private void updateSheetRtl(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeSheetRtl)
        {
            SheetRtlInfoExtractor info = new SheetRtlInfoExtractorImpl(actionJson);
            getConstraintResponseHolder().setSheetRtlList(info.getSheetWrapperList());
        }
    }

    private void updateZSTheme(JSONObjectWrapper actionJSON, ResponseHelper helper)
    {
        if(helper.includeZSThemeInfo)
        {
            ZSThemeInfoExtractor info = new ZSThemeInfoExtractorImpl(actionJSON);
            com.adventnet.zoho.websheet.model.response.beans.ThemesBean themesBean = info.getThemesBean();
            if(themesBean != null) {
                getDocumentResponseHolder().setZSTheme(themesBean);
            }
        }
    }

    private void updateTableInfo(JSONObjectWrapper actionJson, ResponseHelper responseHelper)
    {
        ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
        com.adventnet.zoho.websheet.model.response.beans.TableBean tableBean = responseObject.getTableBean();
        if(tableBean != null || actionJson.optBoolean(JSONConstants.FROM_UNDO) || actionJson.optBoolean(JSONConstants.FROM_REDO) || responseHelper.includeTableInfo)
        {
            TableInfoExtractor info = new TableInfoExtractorImpl(actionJson, tableBean, responseObject.getMacroResponse());
            getDocumentResponseHolder().setTableBeanList(info.getTableBeanList());
        }
    }

    private void updateWorkbookLinkInfo(JSONObjectWrapper actionJson)
    {
        if (actionJson.has(String.valueOf(CommandConstants.WORKBOOK_LINK)))
        {
            getDocumentResponseHolder().setWorkbookLinkResponse(actionJson.getJSONObject(String.valueOf(CommandConstants.WORKBOOK_LINK)));
        }
    }

    private void updateCellStyles(ResponseHelper helper)
    {
        if(helper.includeCellStyles)
        {
            getDocumentResponseHolder().setCellStylesDefinitions(true);
        }
    }

    private void updateTextStyles(ResponseHelper helper)
    {
        if(helper.includeTextStyles)
        {
            getDocumentResponseHolder().setTextStyleDefinitions(true);
        }
    }

    private void updateFieldInfo(JSONObjectWrapper actionJSON, ResponseHelper helper)
    {
        if(helper.includeFieldInfo)
        {
            FieldInfoExtractor info = new FieldInfoExtractorImpl(actionJSON);
            com.adventnet.zoho.websheet.model.response.beans.FieldsBean fieldsBean = info.getFieldsBean();
            if(fieldsBean != null)
            {
                getDocumentResponseHolder().setFieldsBean(fieldsBean);
            }
        }
    }

    private void updateGrouping(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeGroupingInfo)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            GroupingInfoExtractor info = new GroupingInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            List<RangeWrapper> rangeWrappers = info.getRangeWrapperList();
            List<RangeWrapper> rangeWrapperList = new ArrayList<>();
            for(RangeWrapper rangeWrapper : rangeWrappers)
            {
                if(getConstraints().isSheetInViewPort(rangeWrapper.getSheetName())) {
                    rangeWrapperList.add(rangeWrapper);
                }
            }
            getConstraintResponseHolder().setGroupingRangeWrapper(rangeWrapperList);
        }
    }

    private	void updateRowHeaderDefinitions(ResponseHelper helper)
    {
        if(helper.includeRowHeadersDefinitions)
        {
            getDocumentResponseHolder().setRowHeaderDefinitions(true);
        }
    }

    private	void updateColumnHeaderDefinitions(ResponseHelper helper)
    {
        if(helper.includeColumnHeadersDefinitions)
        {
            getDocumentResponseHolder().setColHeaderDefinitions(true);
        }
    }

    private	void updateDocumentMeta(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeDocumentMeta)
        {
            DocumentMetaInfoExtractor info = new DocumentMetaInfoExtractorImpl(actionJson, helper);
            getDocumentResponseHolder().setDocumentMeta(info.getDocumentMetaBean());
        }
    }

    private	void updateUserMeta(ResponseHelper helper)
    {
        if(helper.includeUserMeta)
        {
            getDocumentResponseHolder().setUserMeta(true);
        }
    }

    private void updateOleObjInfo(ResponseHelper helper)
    {
        if(helper.isContainsOleObj)
        {
            boolean isContainsOleObj = ResponseUtils.checkForImageChartsButtons(getWorkbook());
            getDocumentResponseHolder().setOleObj(isContainsOleObj);
        }
    }

    private void updateWorkbookImagesInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeWorkbookImagesInfo)
        {
            DocumentResponseHolder responseHolder = getDocumentResponseHolder();
            responseHolder.setGenerateWorkbookImages(true);
            responseHolder.setPublishRangeId(actionJson.optString(JSONConstants.RANGE_ID, null));
        }
    }

    private void updateServerClipObject(ResponseHelper helper)
    {
        if(helper.includeServerClipObject)
        {
            String zuid = getUserInfo().getZuid();
            JSONObjectWrapper serverClipObject = null;
            try{
                serverClipObject = ActionUtil.getServerClipObject(zuid);
            }
            catch(Exception e){
                LOGGER.log(Level.WARNING, "[ServerClip] exception in serverClip Response", e);
            }
            getUserSpecificResponseHolder().setServerClipObj(serverClipObject);
        }
    }

    private void updateChartServerClipObject(ResponseHelper helper)
    {
        if(helper.includeChartServerClipObject)
        {
            String zuid = getUserInfo().getZuid();
            JSONObjectWrapper chartClipObject = null;
            try{
                chartClipObject = ChartUtils.getChartStyleClip(zuid, "");
            }
            catch(Exception e){
                LOGGER.log(Level.WARNING, "[ChartServerClip] exception in chart serverClip", e);
            }
            getUserSpecificResponseHolder().setChartStyleClipObj(chartClipObject);
        }
    }

    private void updateFilterInfo(JSONObjectWrapper actionJson, ResponseHelper responseHelper)
    {
        if(responseHelper.includeFilterDetails)
        {
            ActionResponseObject responseObject = (ActionResponseObject) getResponseObject();
            FilterInfoExtractor info = new FilterInfoExtractorImpl(actionJson, responseObject.getMacroResponse());
            Map<String, List<FilterBean>> filterBeanMap = info.getFilterBeanMap();
            UserSpecificResponseHolder userSpecificResponseHolder = getUserSpecificResponseHolder();
            for(String key : filterBeanMap.keySet())
            {
                userSpecificResponseHolder.addFilterBean(key, filterBeanMap.get(key));
            }
        }
    }

    private void updatePublishedViewInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.isPublishedView)
        {
            PublishedViewInfoExtractor info = new PublishedViewInfoExtractorImpl(actionJson);
            getConstraintResponseHolder().addPublishedViewWrapper(info.getPublishedViewWrapperList());
        }
    }

    private void updateRootMergeCellInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if (helper.isIncludeRootMergeCellInfo && actionJson.has(JSONConstants.SHEET_NAME) && actionJson.has("ranges"))
        {
            Workbook workbook = getWorkbook();
            String sheetName = actionJson.getString(JSONConstants.SHEET_NAME);
            JSONArrayWrapper rangeAry = actionJson.getJSONArray("ranges");
            Sheet sheet = workbook.getSheet(sheetName);
            List<CellResponse> rootMergeCellsData = new ArrayList<>();
            Map<String, JSONArrayWrapper> rootMergeCellsCfData = new HashMap<>();
            JSONArrayWrapper cfData = new JSONArrayWrapper();
            for (int i = 0; i < rangeAry.length(); i++) {
                JSONObjectWrapper rangeJsonObject = rangeAry.getJSONObject(i);
                JSONArrayWrapper rangeBoundaryAry = rangeJsonObject.getJSONArray("boundry");     // No I18N
                DataRange dataRange = new DataRange(sheet.getAssociatedName(), rangeBoundaryAry.getInt(0), rangeBoundaryAry.getInt(1), rangeBoundaryAry.getInt(2), rangeBoundaryAry.getInt(3));
                for (DataRange mergedRange : sheet.getMergeCellDetails().values()) {
                    Cell cell = sheet.getCell(mergedRange.getStartRowIndex(), mergedRange.getStartColIndex());
                    if (RangeUtil.intersection(mergedRange, dataRange) != null && (!sheet.isRowVisible(cell.getRowIndex()) || !sheet.isColumnVisible(cell.getColumnIndex()))) {
                        CellResponse cellResponse = new CellResponseJsonImpl(workbook, sheet.getAssociatedName(), cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex(), getConstraints());
                        rootMergeCellsData.add(cellResponse);
                        JSONArrayWrapper rootMergeCellCfResp = ConditionalStyleResponse.getAppliedConditionalCellStylesToRanges(sheet, new HashSet<>(Collections.singletonList(new DataRange(sheet.getAssociatedName(), cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex()))));
                        for (int k = 0; k < rootMergeCellCfResp.length(); k++) {
                            JSONArrayWrapper resp = rootMergeCellCfResp.getJSONArray(k);
                            if (!resp.isEmpty()) {
                                cfData.put(resp);
                            }
                        }
                    }
                }
            }
            if (!cfData.isEmpty()) {
                rootMergeCellsCfData.put(sheet.getAssociatedName(), cfData);
            }
            getConstraintResponseHolder().setRootMergeCellsData(rootMergeCellsData, rootMergeCellsCfData);
        }
    }

    private void updateFontFamilyList(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeFontFamilyList || helper.includeUserFontList)
        {
            UserFontListInfoExtractor info = new UserFontListInfoExtractorImpl(actionJson, helper.includeFontFamilyList, helper.includeUserFontList);
            com.adventnet.zoho.websheet.model.response.beans.UserFontBean userFontBean = info.getUserfontbean();
            if(userFontBean != null)
            {
                getDocumentResponseHolder().setFontFamilyList(userFontBean);
            }
        }
    }

    private void updateSyncStyles(ResponseHelper helper)
    {
        if(helper.includeSyncStyles)
        {
            SyncStyleResponseObject responseObject = (SyncStyleResponseObject) getResponseObject();
            getDocumentResponseHolder().setSyncStyleNames(responseObject.getTemporaryChangedStyleNames());
        }
    }

    private void updateErrorInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeErrorResponse)
        {
            ErrorResponseObject responseObject = (ErrorResponseObject) getResponseObject();
            ErrorInfoExtractor info = new ErrorInfoExtractorImpl(actionJson, responseObject.getErrorMsgObj());
            getUserSpecificResponseHolder().setErrorBean(info.getErrorBean());
        }
    }
    private void updateOnLoadDetails(ResponseHelper helper)
    {
        if(helper.includeOnLoadDetails)
        {
            DocumentResponseHolder holder = getDocumentResponseHolder();
            holder.setOnLoadRequest(true);
        }
    }

    private void updateSlicerInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeSlicerInfo)
        {
            SlicerInfoExtractor info = new SlicerInfoExtractorImpl(actionJson);
            getConstraintResponseHolder().setSlicerBean(info.getSlicerBeanList());
        }
    }
    private void updateTimelineInfo(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        if(helper.includeTimelineInfo)
        {
            TimelineInfoExtractor info = new TimelineInfoExtractorImpl(actionJson);
            getConstraintResponseHolder().setTimelineBean(info.getTimelineBeanList());
        }
    }

    private ResponseHelper updateResponseHelperForPostDocLoadAction()
    {
        ResponseHelper responseHelper = new ResponseHelper();
        responseHelper.includeServerClipObject = true;
        responseHelper.includeChartServerClipObject = true;
        responseHelper.includeUsageRestrictionInfo = true;
        responseHelper.includeKnitChartClipInfo = true;
        return responseHelper;
    }

    private ResponseHelper updateResponseHelperForFetch(JSONObjectWrapper dataJson)
    {
        ResponseHelper helper = new ResponseHelper();
        JSONObjectWrapper metaJson = dataJson.getJSONObject("meta");
        int cellOffsetBit = metaJson.optInt(Integer.toString(CommandConstants.CELL_META), 0);
        long sheetMetaData = metaJson.optLong(Integer.toString(CommandConstants.SHEET_META), 0);
        long documentMetaData = metaJson.optLong(Integer.toString(CommandConstants.DOCUMENT_META), 0);
        int userMetaData = metaJson.optInt(Integer.toString(CommandConstants.USER_META), 0);

        // parsing DocumentMeta
        if(documentMetaData > 0) {
            helper.includeSheetInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.WORKSHEETS);
            helper.includeRowHeadersDefinitions = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.ROWHEADER_DEFINITION);
            helper.includeColumnHeadersDefinitions = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.COLHEADER_DEFINITION);
            helper.includeCellStyles = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.CELLSTYLE_DEFINITION);
            helper.includeTextStyles = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.TEXTSTYLE_DEFINITION);
            helper.includeActiveInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.ACTIVE_INFO);
            helper.includeActionIdentifier = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.ACTION_IDENTIFIER);
            helper.includeDocumentMeta = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.META);
            helper.includeFormSheets = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.FORM_SHEETS);
            helper.includeProtectedSheet = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.PROTECTED_SHEETS);
            helper.includePivotInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.PIVOT);
            helper.includeNamedRangeInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.NAMED_RANGE);
            helper.includeDeluge = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.DELUGE_FUNCTIONS);
            helper.includeDiscussions = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.DISCUSSION);
            helper.includeDocumentSettingsInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.DOCUMENT_SETTINGS);
            helper.includeDataConnectionInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.DATA_CONNECTION);
            helper.includePicklistDocInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.PICKLIST);
            helper.includeTableInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.TABLE);
            helper.includeWorkbookImagesInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.IMAGES);
            helper.includeZSThemeInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.ZSTHEME);
            helper.includeFieldInfo = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.FIELD);
            helper.includeUserFontList = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.USER_FONTLIST);
            helper.includeFontFamilyList = checkPermission(documentMetaData, com.adventnet.zoho.websheet.model.response.meta.DocumentMeta.FONT_FAMILYLIST);
            helper.includeOnLoadDetails = checkPermission(documentMetaData, DocumentMeta.ONLOAD_DETAILS);
        }

        // parsing SheetMeta
        helper.includeCellResponse = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.CELLDATA);
        helper.includeSheetActiveInfo = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.ACTIVE_INFO);
        helper.includeRowHeaders = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.ROWHEADER);
        helper.includeColumnHeaders = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.COLHEADER);
        helper.includeColumnLevelFormats = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.COLLEVEL_STYLENAMES);
        helper.includeMergeCells = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.MERGE);
        helper.includeArrayFormulasRange = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.ARRAYFORMULA);
        helper.includeDataValidationRange = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.DATAVALIDATION);
        helper.includeConditionalFormatRange = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.CONDITIONALFORMATS);
        helper.includeFilterDetails = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.FILTERINFO);
        helper.includeFreezePanesInfo = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.FREEZEINFO);
        helper.includeHiddenRows = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.HIDDENROWS);
        helper.includeHiddenColumns = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.HIDDENCOLS);
        helper.includeProtectedRange = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.PROTECTEDRANGE);
        helper.includeFormRange = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.FORMRANGE);
        helper.includeMaxUsedCell = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.MAXUSEDCELL);
        helper.isHideGrid = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.HIDE_GRID);
        helper.includeCheckBoxRange = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.CHECK_BOX);
        helper.includeImage = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.IMAGE);
        helper.includeChartInfo = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.CHARTS);
        helper.includeButtons = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.BUTTONS);
        helper.includeSparkline = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.SPARKLINE);
        helper.includeZoomMeta = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.ZOOM);
        helper.includeSheetView = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.SHEET_VIEW);
        helper.includeSheetRtl = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.SHEET_DIRECTION);
        helper.includePicklistRangeinfo = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.PICKLIST);
        helper.isIncludeRootMergeCellInfo = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.ROOTMERGECELLDATA);
        helper.includeGroupingInfo = checkPermission(sheetMetaData, com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta.GROUPING);
        helper.includeSlicerInfo = helper.includeTimelineInfo = checkPermission(sheetMetaData, WorkSheetMeta.SLICER);

        helper.includeKnitChartsSheetInfo = checkPermission(sheetMetaData, WorkSheetMeta.CHARTS);

        //parsing UserMeta
        helper.includeUserMeta = checkPermission(userMetaData, UserMeta.USER_META_INFO);

        if(cellOffsetBit > 0 && helper.includeCellResponse)
        {
            // For Internal Use
            helper.isFetchDataAction = true;
            helper.includeAppliedConditionalCellStyles = MetaBitUtil.checkPermission(CellMeta.CellMetaType.COND_STYLE_NAME, cellOffsetBit);
        }
        if(dataJson.optString(JSONConstants.VERSION) != null)
        {
            helper.includeVersionInfo = true;
        }
        if(dataJson.has(JSONConstants.FORMS_INFO))
        {
            //Sending formRange if active sheet has associated form
            JSONObjectWrapper formsInfo = dataJson.getJSONObject(JSONConstants.FORMS_INFO);
            if(formsInfo.has(getWorkbook().getSheetByAssociatedName(dataJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME)).getName()))
            {
                helper.includeFormRange = true;
            }
        }
        helper.isPublishedView = dataJson.optBoolean("isPublishedView"); // No I18N
        helper.includeActiveInfo = true;
        helper.includeNavMetaVersion = true;
        return helper;
    }

    private ResponseHelper updateResponseHelperForGrid(JSONObjectWrapper actionJson, List<Cell> srcCells ,HashSet<Cell> dependentCells, MacroResponse macroResponse)
    {
        ResponseHelper responseHelper = new ResponseHelper();
                  /*
                    setting includeChartInfo is true becaues we are constructing chart response based on affected chart id. so no need to go and toggle property.
                    we will enable this property in future if we not coming with affected chart list. ~Pawan
                */
//                if(actionJson.has(JSONConstants.RELOAD_TILES)){
//                    responseHelper.includeForceReloadTile = true;
//                    return;
//                }
        if(getResponseObject() instanceof ActionResponseObject) {
            List<ModifiedChartPOJO> modifiedChartPOJOS = ((ActionResponseObject) getResponseObject()).getModifiedChartBeans();
            responseHelper.includeKnitChartModifiedInfo = modifiedChartPOJOS != null && !modifiedChartPOJOS.isEmpty();
        }
        responseHelper.includeChartInfo  = true;
        responseHelper.includeActionIdentifier = true;
        int action = actionJson != null && actionJson.has(JSONConstants.ACTION)    ? actionJson.getInt(JSONConstants.ACTION): -1;
        boolean isCellImage = actionJson != null && actionJson.has(JSONConstants.IS_CELL_IMAGE) && actionJson.getBoolean(JSONConstants.IS_CELL_IMAGE);
        if(action == ActionConstants.REVERT_VERSION) {
            responseHelper.isRevertVersion	=	true;
            return responseHelper;
        }

        boolean isStyleChanged = actionJson.has(JSONConstants.IS_STYLE_CHANGED) && actionJson.getBoolean(JSONConstants.IS_STYLE_CHANGED);
        boolean isValueChanged = actionJson.has(JSONConstants.IS_VALUE_CHANGED) && actionJson.getBoolean(JSONConstants.IS_VALUE_CHANGED);
        /*
         * marking cellResponse as true if cellValue is false in jsonObject, then check for format, as it is part of cellData
         */
        responseHelper.includeNewlyCreatedStyles = true;
        responseHelper.includeNewlyCreatedWorkbookImages = true;
        responseHelper.includeDependentCells = action != ActionConstants.LOCK_MULTIPLE;
        responseHelper.includeCellResponse 	= 	isValueChanged || isStyleChanged;
//		responseHelper.includeTextStyles 	= 	isStyleChanged;
//	    responseHelper.includeCellStyles 	=	isStyleChanged;
        responseHelper.includeMaxUsedCell	=	true;
        responseHelper.includeFaultySheets = true;
        responseHelper.includeNamedRangeInfo   			= actionJson.has("is_nrc") && actionJson.getBoolean("is_nrc");//No I18N
        responseHelper.includeRowHeaders  				= actionJson.has("is_calc_rh") && actionJson.getBoolean("is_calc_rh");//No I18N
        responseHelper.includeRepeatActionInfo 		 	= actionJson.has("is_ra") && actionJson.getBoolean("is_ra");//No I18N
        responseHelper.isClientAction 					= actionJson.has("is_ca") && actionJson.getBoolean("is_ca");   //No I18N
        responseHelper.includeConditionalFormatRange 	=	actionJson.has(JSONConstants.CSTYLEJSON);
        responseHelper.includeFilterDetails = true;
        responseHelper.includeFilterRowCount = actionJson.has(JSONConstants.FILTERED_ROW_COUNT) && !actionJson.optBoolean(JSONConstants.FROM_UNDO) && !actionJson.optBoolean(JSONConstants.FROM_REDO);

        if(actionJson.has("isCSE") && actionJson.getBoolean("isCSE")) {							//No I18N
            responseHelper.includeArrayFormulasRange = true;
        }

        if(actionJson.has(JSONConstants.ADDITIONAL_INFO) && actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).has(JSONConstants.PIVOT_INFO))
        {
            responseHelper.includePivotInfo 	= true;
            responseHelper.includeSlicerInfo = true;
            responseHelper.includeTimelineInfo = true;
        }

        if(actionJson.has(JSONConstants.EXPANDED_TABLES) && !actionJson.getJSONArray(JSONConstants.EXPANDED_TABLES).isEmpty())
        {
            responseHelper.includeNamedRangeInfo = true;
            responseHelper.includeProtectedRange = true;
            responseHelper.includeMergeCells = true;
            responseHelper.includeCheckBoxRange = true;
            responseHelper.includeDataValidationRange = true;
            responseHelper.includePicklistRangeinfo = true;
            responseHelper.includeSparkline = true;
        }

        switch (action) {

            case ActionConstants.SUBMIT :

            case ActionConstants.REPLACE :
                responseHelper.includeReplaceCount   = actionJson.has(JSONConstants.NO_OF_REPLACE);

            case ActionConstants.REPLACE_ALL :
                responseHelper.includeReplaceAllCount   = actionJson.has(JSONConstants.NO_OF_MATCH);
                responseHelper.includeUserNotification = true;
            case ActionConstants.COPY_PASTE_CONTENT :
                responseHelper.includeCheckBoxRange = true;
            case ActionConstants.DATA_FROM_PICTURE:
//	    		responseHelper.includeCellStyles = true;
//                responseHelper.includeTextStyles = true;
                responseHelper.includeRowHeaders = true;
                responseHelper.includeDataValidationMsg	=true;
                break;

            case ActionConstants.SYSTEMCLIP_PASTE:
                responseHelper.includeCheckBoxRange = true;
                responseHelper.includePicklistRangeinfo = true;
            case ActionConstants.CREATE_WORKBOOK_WITH_CLIP_CONTENT:

                responseHelper.includeMergeCells = true;
//	    		responseHelper.includeRangeHighlight	= true;
                responseHelper.includeUserNotification = true;
                responseHelper.includeRowHeaders = true;
                break;
            case ActionConstants.SERVERCLIP_PASTE_RANGE:
                responseHelper.includeCheckBoxRange = true;
                break;
            /*
             * Now cell data contains cellComment and hyperlink, so making cellResponse true for that.
             */
            case ActionConstants.HYPERLINK :
            case ActionConstants.FORMAT_DATE :
            case ActionConstants.FORMAT_CURRENCY :
            case ActionConstants.FORMAT_ACCOUNTING:
            case ActionConstants.FORMAT_COMMA :
            case ActionConstants.FORMAT_PERCENT :
            case ActionConstants.FORMAT_TEXT :
            case ActionConstants.FORMAT_INCREMENT_DECIMAL :
            case ActionConstants.FORMAT_DECREMENT_DECIMAL :
            case ActionConstants.FORMATCELLS_GRID :
            case ActionConstants.NUMBER_FORMAT_APPLY:
            case ActionConstants.HALIGN:
            case ActionConstants.VALIGN:
            case ActionConstants.ALIGN:
            case ActionConstants.BORDERS:
            case ActionConstants.FONTFAMILY:
            case ActionConstants.FONTSIZE:
            case ActionConstants.WRAP:
            case ActionConstants.TEXT_ROTATE_BY_ANGLE:
            case ActionConstants.CELL_COMMENT :

                responseHelper.includeCellResponse = true;

                break;
            /**
             * adding indentation to the cell.
             * Needed, when the cell is wrapped. - To increase row height.
             */
            case ActionConstants.SET_INDENT:
            case ActionConstants.INCREASE_INDENT:
            case ActionConstants.DECREASE_INDENT:
                responseHelper.includeRowHeaders = true;
                break;
            /**
             *
             * for Conditional formatting
             */
            case ActionConstants.CONDITIONAL_FORMAT_APPLY :
            case ActionConstants.COLOR_SCALES_APPLY :
            case ActionConstants.ICON_SET_APPLY :
            case ActionConstants.DATA_BAR_APPLY :
            case ActionConstants.CONDITIONAL_FORMAT_DELETE :
            case ActionConstants.CONDITIONAL_FORMAT_READ_RANGE :
            case ActionConstants.CONDITIONAL_FORMAT_READ_SHEET :
            case ActionConstants.CONDITIONAL_FORMAT_READ_WORKBOOK :
            case ActionConstants.CONDITIONAL_FORMAT_READ_RANGE_FOR_MOBILE:
            case ActionConstants.CONDITIONAL_FORMAT_READ_SHEET_FOR_MOBILE:
            case ActionConstants.CONDITIONAL_FORMAT_READ_WORKBOOK_FOR_MOBILE:
//	    	case ActionConstants.CONDITIONAL_FORMAT_READ_EDITOBJ :

                responseHelper.includeConditionalFormatRange = true;
                responseHelper.includeAppliedConditionalCellStyles = true;
            case ActionConstants.CONDITIONAL_FORMAT_EDIT :
            case ActionConstants.CONDITIONAL_STYLE_REORDER :

//	    		responseHelper.includeCellResponse = true;

                break;

            /**
             *
             * Based on the actions enabling the commands
             */

            case ActionConstants.DELETE_ROW :
                responseHelper.includeCellResponse = false;
                responseHelper.includeAppliedConditionalCellStyles = true;
                responseHelper.includeConditionalFormatRange = true;
            case ActionConstants.INSERT_ROW :
                responseHelper.includePivotInfo 	= true;
                responseHelper.includeImage = true;
                responseHelper.includeSlicerInfo = true;
                responseHelper.includeTimelineInfo = true;
            case ActionConstants.INSERT_COPY_ROW :
                responseHelper.includeFreezePanesInfo = true;
                responseHelper.includeCheckBoxRange = true;
                responseHelper.includeMergeCells = true;
                responseHelper.includeRowHeaders = true;
                responseHelper.includeDataValidationRange = true;
                responseHelper.includeSparkline = true;
                responseHelper.includeRangeHighlight	= true;
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includeNamedRangeInfo = true;
                responseHelper.includeGroupingInfo = true;
                responseHelper.includeDataConnectionInfo = true;
                break;
            case ActionConstants.INSERT_CUT_ROW :
                responseHelper.includeHiddenRows=true;
                responseHelper.includeFreezePanesInfo = true;
                responseHelper.includeCheckBoxRange = true;
                responseHelper.includeMergeCells = true;
                responseHelper.includeRowHeaders = true;
                responseHelper.includeDataValidationRange = true;
                responseHelper.includeHiddenRows = true;
                responseHelper.includeSparkline = true;
                responseHelper.includeRangeHighlight	= true;
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includeNamedRangeInfo = true;
                responseHelper.includeGroupingInfo = true;
                responseHelper.includeDiscussions = true;
                responseHelper.includeDataConnectionInfo = true;
                break;
            case ActionConstants.DELETE_COL :
                responseHelper.includeCellResponse = false;
                responseHelper.includeAppliedConditionalCellStyles = true;
                responseHelper.includeConditionalFormatRange = true;
            case ActionConstants.INSERT_COL :
                responseHelper.includePivotInfo 	= true;
                responseHelper.includeImage = true;
                responseHelper.includeSlicerInfo = true;
                responseHelper.includeTimelineInfo = true;
            case ActionConstants.INSERT_COPY_COLUMN :
                responseHelper.includeFreezePanesInfo = true;
                responseHelper.includeCheckBoxRange = true;
                responseHelper.includeMergeCells = true;
                responseHelper.includeColumnHeaders = true;
                responseHelper.includeColumnLevelFormats = true;
                responseHelper.includeDataValidationRange = true;
                responseHelper.includeSparkline = true;
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includeRangeHighlight	= true;
                responseHelper.includeNamedRangeInfo = true;
                responseHelper.includeGroupingInfo = true;
                responseHelper.includeDataConnectionInfo = true;
                break;
            case ActionConstants.INSERT_CUT_COLUMN :
                responseHelper.includeHiddenColumns=true;
                responseHelper.includeFreezePanesInfo = true;
                responseHelper.includeCheckBoxRange = true;
                responseHelper.includeMergeCells = true;
                responseHelper.includeColumnHeaders = true;
                responseHelper.includeHiddenColumns = true;
                responseHelper.includeColumnLevelFormats = true;
                responseHelper.includeDataValidationRange = true;
                responseHelper.includeSparkline = true;
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includeRangeHighlight	= true;
                responseHelper.includeNamedRangeInfo = true;
                responseHelper.includeGroupingInfo = true;
                responseHelper.includeDiscussions = true;
                responseHelper.includeDataConnectionInfo = true;
                break;
            case ActionConstants.WIDTH:
                responseHelper.includeColumnHeaders     =       true;
                responseHelper.includeRowHeaders     =       true;
                responseHelper.includeCellResponse		=		false;
                break;
            case ActionConstants.HEIGHT :
                responseHelper.includeCellResponse		=		false;
                break;
            case ActionConstants.NAMEDRANGE_ADD :
            case ActionConstants.NAMEDRANGE_MODIFY :
            case ActionConstants.NAMEDRANGE_DELETE :

                responseHelper.includeNamedRangeInfo = true;

                break;

            case ActionConstants.SOLVER:
                responseHelper.includeCellResponse = true;
                break;
            case ActionConstants.IMPORT_REPLACE_SPREADSHEET:
                responseHelper.includeImportResponse= true;
                break;
            case ActionConstants.IMPORT_REPLACE_CURRENTSHEET:
            case ActionConstants.IMPORT_APPEND_ROWS_TO_CURRENTSHEET:
            case ActionConstants.IMPORT_REPLACE_DATA_STARTING_AT_CELL:
                responseHelper.includeCellResponse = true;
                responseHelper.includeImportResponse= true;
                responseHelper.includeImage = true;
                break;
            case ActionConstants.Z_TRANSLATION_INSERT_NEW:
            case ActionConstants.IMPORT_INSERT_AS_NEW_SHEETS:
            case ActionConstants.Z_TRANSLATION_EXISTING_SHEET:
            case ActionConstants.REPLACE_SOURCE_TABLE:
            case ActionConstants.DP_NEW_SHEET:
            case ActionConstants.DP_EXISTING_SHEET:
                responseHelper.includeSheetInfo = true;
                break;
            case ActionConstants.COPY_PASTE :
            case ActionConstants.PASTE_AS_LINK:
                responseHelper.includeUserNotification = true;
                responseHelper.includeColumnHeaders = true;
                responseHelper.includeCheckBoxRange = true;
            case ActionConstants.CUT_PASTE :
                responseHelper.includeHiddenColumns = true;
                responseHelper.includeHiddenRows = true;
                responseHelper.includeColumnHeaders = true;
                responseHelper.includeNamedRangeInfo = true;
                responseHelper.includePicklistDocInfo = true;
                responseHelper.includeGroupingInfo = true;
                responseHelper.includeDiscussions = true;
                responseHelper.includeDataConnectionInfo = true;
            case ActionConstants.CLEARALL :
                responseHelper.includeSparkline = true;
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includePicklistDocInfo = true;
            case ActionConstants.FILLSERIES :
            case ActionConstants.PATTERN_FILL :
                responseHelper.includeRangeHighlight = actionJson.has("mode");//No I18N
                responseHelper.includeDataValidationRange = true;
                responseHelper.includeUserNotification = true;
                responseHelper.includeCheckBoxRange = true;
            case ActionConstants.CLEARCONTENTS :
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includeCheckBoxRange = true;
                responseHelper.includeArrayFormulasRange = true;

            case ActionConstants.MERGE_RANGE :
            case ActionConstants.MERGE_ACROSS :
            case ActionConstants.MERGE_DOWN :
            case ActionConstants.MERGE_AND_CENTER :
            case ActionConstants.MERGE_SPLIT :
            case ActionConstants.PASTESPECIAL_FORMATS:
                responseHelper.includeCheckBoxRange = true;
                responseHelper.includeMergeCells = true;
                responseHelper.includeUserNotification=true;
                responseHelper.includePicklistRangeinfo = true;
                break;

            case ActionConstants.FREEZE_PANES :
            case ActionConstants.UNFREEZE_PANES :
                responseHelper.includeFreezePanesInfo = true;
                break;
            case ActionConstants.POST_DOC_LOAD:
                responseHelper.includeUserNotification = true;
                break;
            case ActionConstants.SHEET_INSERT :
            case ActionConstants.INSERT_SHEET_WITH_CLIP_CONTENT:
                responseHelper.includeUserNotification = true;
                responseHelper.includeSheetInfo = true;
                break;
            case ActionConstants.SHEET_MOVE :
                responseHelper.includeSheetInfo = true;
                break;
            case ActionConstants.SERVERCLIP_PASTE_SHEET:
                responseHelper.includeImage = true;
                if(!actionJson.optBoolean(JSONConstants.IS_SAME_DOCUMENT, false))
                {
                    responseHelper.includeFieldInfo = true;
                }
                responseHelper.includeSheetInfo = true;
                responseHelper.includeProtectedSheet = true;
                responseHelper.includePivotInfo = true;
                responseHelper.includePicklistDocInfo = true;
                responseHelper.includeTableInfo = true;
                responseHelper.includeDataConnectionInfo = true;
                break;
            case ActionConstants.SHEET_DUPLICATE:
                responseHelper.includeSheetInfo = true;
                responseHelper.includeProtectedSheet = true;
                responseHelper.includePivotInfo = true;
                responseHelper.includePicklistDocInfo = true;
                responseHelper.includeTableInfo = true;
                responseHelper.includeDataConnectionInfo = true;
                responseHelper.includeChartInfo = false;
                break;

            case ActionConstants.SHEET_REMOVE :
                responseHelper.includeFormSheets	=	true;
                responseHelper.includeMaxUsedCell = false;
                responseHelper.includePivotInfo 	= true;
                responseHelper.includeTableInfo 	= true;
                responseHelper.includeSlicerInfo = true;
            case ActionConstants.SHEET_TABCOLOR :
            case ActionConstants.SHEET_VISIBLE :
            case ActionConstants.SHEET_RENAME :
                responseHelper.includeCellResponse      =       false;
                responseHelper.includeAppliedConditionalCellStyles      =       false;
                responseHelper.includeSheetInfo 		= 		true;
                responseHelper.includeDataConnectionInfo 	= true;
                break;
            case ActionConstants.EDIT_PIVOT :
                responseHelper.includeSlicerInfo = true;
                responseHelper.includeTimelineInfo = true;
            case ActionConstants.CREATE_PIVOT :
            case ActionConstants.COPY_PIVOT :
//	    		responseHelper.includeSheetInfo = actionJson.optBoolean(JSONConstants.IS_NEW_SHEET);
//	    		responseHelper.includePivotInfo 	= true;
//	    		responseHelper.includeColumnHeaders = true;
//	    		//responseHelper.includeUserNotification = true;
//	    		break;
            case ActionConstants.MOVE_PIVOT :
                responseHelper.includeSheetInfo = actionJson.optBoolean(JSONConstants.IS_NEW_SHEET);
                responseHelper.includePivotInfo 	= true;
                responseHelper.includeColumnHeaders = true;
                responseHelper.includeDataConnectionInfo = true;
                break;
            case ActionConstants.SHEET_COPY :
                responseHelper.includeSheetInfo = actionJson.optBoolean(JSONConstants.IS_NEW_SHEET);
                responseHelper.includePivotInfo 	= true;
                responseHelper.includeColumnHeaders = true;
                responseHelper.includeDataConnectionInfo = true;
                responseHelper.includeChartInfo = false;
                break;
            case ActionConstants.DELETE_PIVOT:
            case ActionConstants.APPLYFILTER_PIVOT:
            case ActionConstants.CHANGE_PIVOT_SOURCE:
                responseHelper.includeSlicerInfo = true;
                responseHelper.includeTimelineInfo = true;
            case ActionConstants.REFRESH_PIVOT:
            case ActionConstants.SORT_PIVOT:
            case ActionConstants.CHANGE_PIVOT_SUBTOTALOPTION:
            case ActionConstants.CHANGE_PIVOT_GRANDTOTALROWOPTION:
            case ActionConstants.CHANGE_PIVOT_GRANDTOTALCOLOPTION:
            case ActionConstants.PIVOT_REPEAT_LABEL:
            case ActionConstants.GROUPING_PIVOT_FIELDS:
            case ActionConstants.PIVOT_AUTO_REFRESH:
            case ActionConstants.PIVOT_AUTO_EXPAND:
            case ActionConstants.CHANGE_PIVOT_THEME:
            case ActionConstants.CHANGE_PIVOT_NAME:
            case ActionConstants.PIVOT_HIDE_ERRORS:
                responseHelper.includePivotInfo 	= true;
                break;
            case ActionConstants.UPDATE_CONNECTED_PIVOTS:
            case ActionConstants.APPLY_SLICER_FILTER:
                responseHelper.includeTimelineInfo = true;
            case ActionConstants.UPDATE_SLICER_COLUMN:
            case ActionConstants.SLICER_DELETE:
            case ActionConstants.SLICER_NEW:
            case ActionConstants.SLICER_MOVE:
            case ActionConstants.SLICER_RESIZE:
            case ActionConstants.SORT_SLICER:
            case ActionConstants.UPDATE_SLICER_STYLE:
            case ActionConstants.CHANGE_SLICER_THEME:
//			case ActionConstants.HIDE_SLICER_HEADER:
//			case ActionConstants.MAKE_SLICER_TRANSPARENT:
            case ActionConstants.CHANGE_SLICER_TITLE:
            case ActionConstants.CHANGE_SLICER_LAYOUT:
            case ActionConstants.UPDATE_MULTI_SELECT:
            case ActionConstants.MOVE_SLICER_TO_NEW_SHEET:
                if(actionJson.has(JSONConstants.ID)){
                    responseHelper.includePivotInfo = true;
                }
                responseHelper.includeSlicerInfo = true;
                break;
            case ActionConstants.APPLY_TIMELINE_FILTER:
            case ActionConstants.UPDATE_TIMELINE_CONNECTED_PIVOTS:
            case ActionConstants.UPDATE_TIMELINE_COLUMN:
                responseHelper.includeSlicerInfo = true;
            case ActionConstants.TIMELINE_NEW:
            case ActionConstants.TIMELINE_DELETE:
            case ActionConstants.CHANGE_TIMELINE_TITLE:
            case ActionConstants.MOVE_TIMELINE_TO_NEW_SHEET:
            case ActionConstants.CHANGE_TIMELINE_LAYOUT:
            case ActionConstants.CHANGE_TIMELINE_THEME:
                responseHelper.includeTimelineInfo = true;
                if(actionJson.has(JSONConstants.ID)){
                    responseHelper.includePivotInfo = true;
                }
                break;
            case ActionConstants.TIMELINE_MOVE:
            case ActionConstants.TIMELINE_RESIZE:
            case ActionConstants.CHANGE_TIMELINE_LEVEL:
                responseHelper.includeTimelineInfo = true;
                break;
            case ActionConstants.REFRESH_ALL_PIVOT :
                responseHelper.includeCellResponse		=	true;
                break;
            case ActionConstants.PIVOT_SHOW_DETAILS:
                responseHelper.includeSheetInfo = true;
                break;
            case ActionConstants.REMOVE_FILTER:
            case ActionConstants.APPLY_FILTER:
                responseHelper.includeRowHeaders = false;
//			case ActionConstants.FILTER_BY_VALUE:
			case ActionConstants.FILTER_BY_CELL_COLOR:
			case ActionConstants.FILTER_BY_FONT_COLOR:
				responseHelper.includeCellResponse  = true;
                break;
            case ActionConstants.UPDATE_CLOUD_DATA:
                if(!("refresh".equals(actionJson.optString(JSONConstants.SUB_ACTION)) || "scheduleRefresh".equals(actionJson.optString(JSONConstants.SUB_ACTION))))
                {
                    responseHelper.includeDataConnectionInfo = true;
                    break;
                }
            case ActionConstants.IMPORT_CLOUD_DATA:
                responseHelper.includeTableInfo = true;
            case ActionConstants.INSERT_CELL_LEFT :
            case ActionConstants.INSERT_CELL_TOP :
            case ActionConstants.DELETE_CELL_BOTTOM :
            case ActionConstants.DELETE_CELL_RIGHT :
                responseHelper.includeInsertDeleteCells	=	true;
                responseHelper.includeCellResponse		=	true;
                responseHelper.includeCheckBoxRange		=	true;
                responseHelper.includeMergeCells		=	true;
                responseHelper.includeProtectedRange	=	true;
                responseHelper.includeArrayFormulasRange=	true;
                responseHelper.includeDataValidationRange = true;
                responseHelper.includeSparkline = true;
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includePivotInfo = true;
                responseHelper.includeNamedRangeInfo = true;
                responseHelper.includeDataConnectionInfo = true;
                responseHelper.includeRowHeaders = true;
                break;
            case ActionConstants.LOCK_RANGE:
            case ActionConstants.UNLOCK_RANGE:
                responseHelper.includeProtectedRange	=	true;
                break;
            case ActionConstants.LOCK_SHEET:
            case ActionConstants.UNLOCK_SHEET:
                responseHelper.includeProtectedSheet	=	true;
                break;
            case ActionConstants.LOCK_MULTIPLE:
                if(actionJson.has("isSheet") && actionJson.getBoolean("isSheet")){
                    responseHelper.includeProtectedSheet	=	true;
                }
                else {
                    responseHelper.includeProtectedRange	=	true;
                }
                break;
            case	ActionConstants.DOCUMENT_SETTING	:
                responseHelper.includeCellResponse = actionJson.optBoolean(JSONConstants.CELL_RESPONSE_NEEDED, true);
                responseHelper.includeCellStyles = true;
                responseHelper.includeDocumentSettingsInfo = true;
                responseHelper.includeSheetRtl = true;
                break;
            case	ActionConstants.FORM_CREATE :
            case	ActionConstants.CREATE_SHEET_FOR_FORM :
                responseHelper.includeSheetInfo		 = true;
            case	ActionConstants.FORM_DELETE :
                responseHelper.includeFormSheets	=	true;
                responseHelper.includeFormRange		=	true;
                break;
            case	ActionConstants.FORM_FIELD_ADDED :
            case	ActionConstants.FORM_FIELD_DELETED :
                responseHelper.includeColumnHeaders	=	true;
                responseHelper.includeFormRange		=	true;
                break;
            //case	ActionConstants.FORM_FIELD_UPDATED :
            case	ActionConstants.FORM_LIVE_SUBMIT :
                responseHelper.includeRowHeaders	=	true;
                responseHelper.includeFormRange		=	true;

                break;
            case    ActionConstants.GRID_COLOR:
            case	ActionConstants.HIDE_GRID :
            case 	ActionConstants.SHOW_GRID:
                responseHelper.isHideGrid		=	true;
                //No needed in client
                responseHelper.includeCellResponse	=	false;
                break;
            case 	ActionConstants.CHECKBOX_ADD:
            case 	ActionConstants.CHECKBOX_CLEAR:
            case 	ActionConstants.CHECKBOX_EDIT:
                responseHelper.includeUserNotification = true;
                responseHelper.includeCheckBoxRange 	= true;
                break;
            case	ActionConstants.HIDE_ROWS :
            case ActionConstants.COLLAPSE_ROWGROUP:
            case ActionConstants.COLLAPSEALL_ROWGROUPS:
                responseHelper.includeHiddenRows =true;
                responseHelper.includeSparkline =true;
                break;

            case	ActionConstants.UNHIDE_ROWS :
            case ActionConstants.EXPAND_ROWGROUP:
            case ActionConstants.EXPANDALL_ROWGROUPS:
                responseHelper.includeHiddenRows =true;
                responseHelper.includeSparkline =true;
                break;
            case	ActionConstants.HIDE_COLUMNS :
            case ActionConstants.COLLAPSE_COLGROUP:
            case ActionConstants.COLLAPSEALL_COLGROUPS:
                responseHelper.includeHiddenColumns = true;
                responseHelper.includeSparkline =true;
                break;

            case	ActionConstants.UNHIDE_COLUMNS :
            case ActionConstants.EXPAND_COLGROUP:
            case ActionConstants.EXPANDALL_COLGROUPS:
                responseHelper.includeHiddenColumns =true;
                responseHelper.includeSparkline =true;
                break;

            /**
             * For Image Actions
             */

            case 	ActionConstants.IMAGE_NEW:
                if(isCellImage)
                {

                    responseHelper.includeCheckBoxRange = true;
                }
            case 	ActionConstants.IMAGE_CLONE:
            case 	ActionConstants.IMAGE_REPLACE:
            case	ActionConstants.IMAGE_DELETE:
            case 	ActionConstants.IMAGE_REMOVE:
            case 	ActionConstants.IMAGE_MOVE:
            case 	ActionConstants.IMAGE_RESIZE:
            case 	ActionConstants.IMAGE_CUT_PASTE:
            case 	ActionConstants.IMAGE_COPY_PASTE:
                if(isCellImage)
                {
                    responseHelper.includeCellResponse = true;
                }
                else
                {
                    responseHelper.includeImage = true;
                }
                break;
            case ActionConstants.IMAGE_IN_CELLS:
                responseHelper.includeCheckBoxRange = true;
            case ActionConstants.IMAGE_OVER_CELLS:
                responseHelper.includeImage = true;
            case ActionConstants.IMAGE_DISPLAY_MODE_CHANGE:
                responseHelper.includeCellResponse = true;
                break;

            /**
             * For Buttons
             */

            case ActionConstants.CTRL_COMP_ADD:
            case ActionConstants.CTRL_COMP_ASSIGNMACRO:
            case ActionConstants.CTRL_COMP_CLONE:
            case ActionConstants.CTRL_COMP_DELETE:
            case ActionConstants.CTRL_COMP_MOVE:
            case ActionConstants.CTRL_COMP_RESIZE:
            case ActionConstants.CTRL_COMP_SETCAPTION:
                responseHelper.includeButtons = true;
                break;

            /**
             * For Data Validation Actions
             */
            case ActionConstants.DC_APPLY_FORMATS:
            case ActionConstants.GET_DATA_DUPLICATES:
                responseHelper.includeCellResponse = true;
                responseHelper.includeDataCleaningInfo=true;
                responseHelper.includeSheetInfo = actionJson.getBoolean(JSONConstants.IS_NEW_SHEET);
                break;
            case 	ActionConstants.DATA_VALIDATION_APPLY:
            case 	ActionConstants.DATA_VALIDATION_CLEAR:
                responseHelper.includeDataValidationRange	=	true;
                responseHelper.includeAppliedConditionalCellStyles = true;
                responseHelper.includeCellResponse = true;
                break;
            case    ActionConstants.FORM_PUBLISH:
            case    ActionConstants.FORM_UNPUBLISH:
                responseHelper.includeMaxUsedCell = false;
                responseHelper.includeFormSheets  = true;
                responseHelper.includeChartInfo = false;
                break;
            /**
             * For Chart Actions
             */
            case    ActionConstants.CHART_PUBLISH:
            case    ActionConstants.CHART_UNPUBLISH:
            case 	ActionConstants.CHART_NEW:
            case 	ActionConstants.CHART_RESIZE:
            case 	ActionConstants.CHART_MOVE:
            case 	ActionConstants.CHART_DELETE:
            case 	ActionConstants.CHART_EDIT:
            case 	ActionConstants.CHART_PASTE_STYLE:
            case 	ActionConstants.CHART_QUICK_STYLE:
            case 	ActionConstants.CHART_QUICK_EDIT:
            case 	ActionConstants.CHART_MOVE_TO_NEW_SHEET:
//	    				responseHelper.includeChartInfo 		=	true;
                responseHelper.includeCellResponse 		= 	false;
                break;
            case    ActionConstants.SPARKLINE_CREATE:
            case    ActionConstants.SPARKLINE_REMOVE:
            case    ActionConstants.SPARKLINE_GROUP_REMOVE:
            case    ActionConstants.SPARKLINE_GROUP:
            case    ActionConstants.SPARKLINE_PROPERTIES:
            case	ActionConstants.EDIT_SPARKLINE_GROUP_DATA:
            case ActionConstants.EDIT_SPARKLINE_DATA:
            case 	ActionConstants.UNDO:
                responseHelper.includeSparkline = true;
                responseHelper.includeDataCleaningInfo = true;
                break;
            case    ActionConstants.DISCUSSION_ADD:
            case    ActionConstants.DISCUSSION_ADD_REPLY:
            case    ActionConstants.DISCUSSION_DELETE:
            case    ActionConstants.DISCUSSION_DELETE_ALL:
            case    ActionConstants.DISCUSSION_DELETE_REPLY:
            case    ActionConstants.DISCUSSION_LIKE:
            case    ActionConstants.DISCUSSION_LIKE_REPLY:
            case    ActionConstants.DISCUSSION_REOPEN:
            case    ActionConstants.DISCUSSION_RESOLVE:
            case    ActionConstants.DISCUSSION_UNLIKE:
            case    ActionConstants.DISCUSSION_UNLIKE_REPLY:
            case    ActionConstants.DISCUSSION_UPDATE_RANGE_CONTENT:
            case    ActionConstants.DISCUSSION_UPDATE_REPLY:
                responseHelper.includeDiscussions = true;
                break;

            //Zoom

            case ActionConstants.SHEET_ZOOM:
                responseHelper.includeZoomMeta = true;
                break;
            case ActionConstants.DATA_CLEANSING_REPLACE_ALL:
                responseHelper.includeDataCleaningInfo=true;
                break;
            //Grid Spacing
            case ActionConstants.SHEET_VIEW:
                responseHelper.includeSheetView = true;
                break;
            //RTL
            case ActionConstants.SAVE_RTL_SHEET:
                responseHelper.includeSheetRtl = true;


                //Macro
            case 	ActionConstants.MACRO_RUN:
            case 	ActionConstants.MACRO_SAVE_RUN:
                responseHelper.includeRangeHighlight	= true;
                responseHelper.includeUserNotification = true;
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includePicklistDocInfo = true;
                responseHelper.includeConditionalFormatRange = true;
                responseHelper.includeAppliedConditionalCellStyles = true;
                break;


            case ActionConstants.PICKLIST_RANGE_CREATE:
            case ActionConstants.PICKLIST_CREATE:
            case ActionConstants.PICKLIST_EDIT:
            case ActionConstants.PICKLIST_RANGE_EDIT:
                responseHelper.includeCheckBoxRange = true;
            case ActionConstants.PICKLIST_REMOVE:
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includeRowHeaders = true;
            case ActionConstants.PICKLIST_EMPTY_REMOVE:
                responseHelper.includePicklistDocInfo = true;
                break;

            case ActionConstants.PICKLIST_CREATE_SP:
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includePicklistDocInfo = true;
                break;

            case ActionConstants.SORT:
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includeMergeCells = true;
                responseHelper.includeProtectedRange = true;
                break;

            case ActionConstants.APPLY_THEME:
                responseHelper.includeZSThemeInfo = true;
                responseHelper.includeCellStyles = true;
                responseHelper.includeTextStyles = true;
                responseHelper.includeSheetInfo = true;
                responseHelper.includeSparkline = true;
                responseHelper.includeConditionalFormatRange = true;
                responseHelper.includeAppliedConditionalCellStyles = true;
//				responseHelper.includePicklistRangeinfo = true;
                responseHelper.includePicklistDocInfo = true;
                responseHelper.includeChartInfo = true;
                responseHelper.includeTableInfo = true;
                responseHelper.isHideGrid		= true;
                responseHelper.includeDocumentSettings = true;
                responseHelper.includeSlicerInfo = true;
                responseHelper.includeTimelineInfo = true;
                break;
            case ActionConstants.FIELD_ADD:
            case ActionConstants.FIELD_EDIT:
            case ActionConstants.FIELD_DELETE:
            case ActionConstants.FIELDS_UPDATE:
                responseHelper.includeFieldInfo =true;
                break;
            case ActionConstants.FIELDS_DATA_NEW_WORKSHEET:
                responseHelper.includeSheetInfo = true;
                break;
            case ActionConstants.WEB_DATA:
            case ActionConstants.UPDATE_WEB_DATA:

                responseHelper.includeDataConnectionInfo = true;
                break;
            case ActionConstants.UNGROUP_ROW:
                responseHelper.includeGroupingInfo = true;
                responseHelper.includeHiddenRows = true;
                break;

            case ActionConstants.UNGROUP_COL:
                responseHelper.includeGroupingInfo = true;
                responseHelper.includeHiddenColumns = true;
                break;

            case ActionConstants.CLEAROUTLINE:
                responseHelper.includeHiddenColumns = true;
                responseHelper.includeHiddenRows = true;
                responseHelper.includeGroupingInfo = true;
                break;

            case ActionConstants.GROUP_ROW:
            case ActionConstants.GROUP_COL:
            case ActionConstants.MOVESUMMARYCOL:
            case ActionConstants.MOVESUMMARYROW:
                responseHelper.includeGroupingInfo = true;
                break;

            case ActionConstants.TABLE_CREATE:
                responseHelper.includeMergeCells = true;
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includeCheckBoxRange = true;
                if(!actionJson.getBoolean(JSONConstants.CONTAINS_HEADER)) {
                    responseHelper.includeNamedRangeInfo = true;
                    responseHelper.includeProtectedRange = true;
                    responseHelper.includeDataValidationRange = true;
                    responseHelper.includeSparkline = true;
                    responseHelper.includePivotInfo = true;
                    responseHelper.includeRowHeaders = true;
                }
                responseHelper.includeTableInfo = true;
                responseHelper.includeUserNotification = true;
                break;

            case ActionConstants.TABLE_TOGGLE_HEADER_FOOTER:
                responseHelper.includeTableInfo = true;
                responseHelper.includePivotInfo = true;
                if(actionJson.has(JSONConstants.TABLE_INSERT_CELLS)) {
                    responseHelper.includeNamedRangeInfo = true;
                    responseHelper.includeProtectedRange = true;
                    responseHelper.includeMergeCells = true;
                    responseHelper.includeCheckBoxRange = true;
                    responseHelper.includeDataValidationRange = true;
                    responseHelper.includePicklistRangeinfo = true;
                    responseHelper.includeSparkline = true;
                    responseHelper.includeRowHeaders = true;
                }
                break;

            case ActionConstants.TABLE_INSERT_ROW:
            case ActionConstants.TABLE_DELETE_ROW:
                responseHelper.includeRowHeaders = true;
            case ActionConstants.TABLE_INSERT_COL:
            case ActionConstants.TABLE_DELETE_COL:
                responseHelper.includeNamedRangeInfo = true;
                responseHelper.includeProtectedRange = true;
                responseHelper.includeMergeCells = true;
                responseHelper.includeCheckBoxRange = true;
                responseHelper.includeDataValidationRange = true; // TODO : Is this needed ?
                responseHelper.includePicklistRangeinfo = true;
                responseHelper.includeSparkline = true;
                responseHelper.includePivotInfo = true;
                responseHelper.includeTableInfo = true;
                break;
            case ActionConstants.TABLE_REMOVE:
                responseHelper.includePivotInfo = true;
                responseHelper.includeTableInfo = true;
                break;
            case ActionConstants.CLEARSTYLES:
            case ActionConstants.TABLE_STYLE_CHANGE:
            case ActionConstants.TABLE_REMOVE_CUSTOM_STYLE:
            case ActionConstants.TABLE_CLEAR_STYLE:
            case ActionConstants.TABLE_PROPERTIES_CHANGE:
                responseHelper.includeTableInfo = true;
                break;
            case ActionConstants.DRE_CREATE:
            case ActionConstants.DRE_DELETE:
                responseHelper.includeDeluge = true;
                responseHelper.includeChartInfo = false;
                break;
            case ActionConstants.INSERT_CHART:
            case ActionConstants.INSERT_TABLE_CHART:
            case ActionConstants.INSERT_PIVOT_CHART:
            case ActionConstants.PASTE_CHART:
            case ActionConstants.PASTE_CHART_STYLES:
            case ActionConstants.RESET_CHART_STYLES:
            case ActionConstants.PUBLISH_CHART:
            case ActionConstants.UNPUBLISH_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART:
            case ActionConstants.INSERT_RECOMMENDED_CHART_VIA_CACHE:
            case ActionConstants.SHEET_CHART_EDIT:
            case ActionConstants.CHART_MOVE_TO_OTHER_SHEET:
            case ActionConstants.FRAMEWORK_CHART_EDIT:
            case ActionConstants.CLONE_CHART:
            case ActionConstants.DELETE_CHART:{
                responseHelper.includeKnitChartsInfo = true;
                break;
            }
            default:
                break;

        }


        /*

         * For Column level actions

         */
        boolean isColSelected = false;
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        if(dataRange != null){
            for (DataRange range : dataRange) {
                if(range.getStartRowIndex() == 0 && range.getEndRowIndex() == Utility.MAXNUMOFROWS-1){
                    isColSelected = true;
                    break;
                }
            }
        }


        if(isColSelected) {
            switch(action) {

                case ActionConstants.BOLD:
                case ActionConstants.ITALIC:
                case ActionConstants.UNDERLINE:
                case ActionConstants.STRIKETHROUGH:
                case ActionConstants.FONTFAMILY:
                case ActionConstants.FONTSIZE:
                case ActionConstants.VALIGN:
                case ActionConstants.HALIGN:
                case ActionConstants.COLOR:
                case ActionConstants.TEXTCOLOR:
                case ActionConstants.WRAP:
                case ActionConstants.TEXT_ROTATE_BY_ANGLE:
                case ActionConstants.BORDERS:
                case ActionConstants.ALIGN:
                case ActionConstants.SET_INDENT:
                case ActionConstants.NUMBER_FORMAT_APPLY:
                    responseHelper.includeColumnLevelFormats = true;
                    responseHelper.includeCellResponse = true;
                    break;
                case ActionConstants.PASTESPECIAL_FORMATS:
                    responseHelper.includeColumnHeaders = true;
                    break;

            }

            if((action == ActionConstants.COPY_PASTE || action == ActionConstants.CUT_PASTE) &&
                    (actionJson.has("t") && actionJson.getInt("t") == 1))	{					//No I18N

                responseHelper.includeColumnLevelFormats	=	true;
            }

        }

        //Special cases handled :--

        if(actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO)){

            // If a row is deleted and while doing we should come with data as well
            if(action == ActionConstants.INSERT_ROW || action ==  ActionConstants.INSERT_COL){

                responseHelper.includeCellResponse	=	true;
                responseHelper.includeImage = true;
            }
        }

        /** temporarily added as it can be made true in actionObject **/
        if((action == ActionConstants.DELETE_PIVOT || action == ActionConstants.CHANGE_PIVOT_THEME) || ((actionJson.has(JSONConstants.ADDITIONAL_INFO) && actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).has(JSONConstants.PIVOT_AFFECTED_RANGE)) && (action == ActionConstants.DELETE_ROW || action == ActionConstants.DELETE_COL))){
            responseHelper.includeCellResponse	=	true;
        }


        if(!responseHelper.includeCellResponse) {
            responseHelper.includeCellResponse = ((srcCells != null && !srcCells.isEmpty()) || (dependentCells != null && !dependentCells.isEmpty())) ;
        }

        if((actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO)) || (actionJson.has(JSONConstants.FROM_REDO) && actionJson.getBoolean(JSONConstants.FROM_REDO))){
            if(action == ActionConstants.CHART_NEW || action == ActionConstants.CHART_MOVE || action == ActionConstants.CHART_RESIZE || action == ActionConstants.CHART_EDIT || action == ActionConstants.CHART_DELETE) {
                responseHelper.includeCellResponse = false;
            }
            if(action == ActionConstants.CREATE_PIVOT){
                responseHelper.includeCellResponse	=	true;
            }
        }

        responseHelper.includeAppliedConditionalCellStyles	= responseHelper.includeAppliedConditionalCellStyles || responseHelper.includeCellResponse;
        responseHelper.includeZiaInfo = true;

        updateResponseHelperForAPIActions(actionJson, responseHelper);
        updateResponseHelperForMacro(macroResponse, responseHelper);
        return responseHelper;
    }


    private void updateResponseHelperForMacro(MacroResponse macroResponse, ResponseHelper responseHelper)
    {
        if(macroResponse != null){
            if(!macroResponse.getRangeList().isEmpty() || !macroResponse.getDependentCellList().isEmpty() || !macroResponse.getCommentsList().isEmpty()){
                responseHelper.includeCellResponse = true;
            }
            if(!macroResponse.getSheetActionsList().isEmpty() || (macroResponse.getVisibleSheet() != null && !macroResponse.getVisibleSheet().isEmpty())){
                responseHelper.includeSheetInfo = true;
            }
            if(!macroResponse.getColWidthList().isEmpty()){
                responseHelper.includeColumnHeaders = true;
                responseHelper.includeGroupingInfo = true;
            }
            if(!macroResponse.getAffectedRowHeaders().isEmpty()){
                responseHelper.includeRowHeaders = true;
                responseHelper.includeGroupingInfo = true;
            }
            if(!macroResponse.getReloadTileSheetsSet().isEmpty()) {
                responseHelper.includeGroupingInfo = true;
            }
            if(!macroResponse.getNamedExpressionList().isEmpty()){
                responseHelper.includeNamedRangeInfo = true;
            }
            if(!macroResponse.getHiddenRows().isEmpty()) {
                responseHelper.includeHiddenRows = true;
            }
            if(!macroResponse.getHiddenColumns().isEmpty()) {
                responseHelper.includeHiddenColumns = true;
            }
            if(!macroResponse.getProtectedRange().isEmpty()) {
                responseHelper.includeProtectedRange = true;
            }
            if(!macroResponse.getDeleteRowsArray().isEmpty()) {
                responseHelper.includeRowHeaders = true;
            }
            if(!macroResponse.getDeleteColumnsArray().isEmpty()) {
                responseHelper.includeColumnHeaders = true;
            }
        }
    }

    private void updateResponseHelperForAPIActions(JSONObjectWrapper actionJson, ResponseHelper responseHelper)
    {
        int action = actionJson != null && actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        switch (action)
        {
            case ActionConstants.FORMAT :
                JSONArrayWrapper formatArray = actionJson.getJSONArray("format_json");
                for (int i = 0; i < formatArray.length(); i++)
                {
                    JSONObjectWrapper formatObj = formatArray.getJSONObject(i);
                    if (formatObj.has("merge_cell"))
                    {
                        responseHelper.includeMergeCells = true;
                        break;
                    }
                }
                break;
            default:
                break;
        }
    }

    private boolean checkPermission(long metaData, long offsetBit) {
        //Checking particular bit is ON or NOT
        return (offsetBit & metaData) != 0;
    }

    @Override
    public JSONObjectWrapper generateDocumentResponse()
    {
        updateAllImageDetails();
        updateNewlyCreatedWorkbookImages();
        updateForcedReloadTile();
        updateVersionInfo();
        updateRevertVersion();
        updateActionIdentifier();
        updateRowHeaderDefinition();
        updateColHeaderDefinition();
        updateDocumentMeta();
        updateUserMeta();
        updateCellStyleDefinition();
        updateTextStyleDefinition();
        updateSheetOperations();
        updateSyncStyleNames();
        updateActiveInfo();
        updateNewlyCreatedStyles();
        updateFaultySheets();
        updateFormSheets();
        updatePivotInfo();
        updateDataConnectionInfo();
        updateOleObjectsInfo();
        updateNamedRange();
        updateDocumentSettingsInfo();
        updateImportInfo();
        updateDiscussionInfo();
        updateDelugeFunctionsInfo();
        updatePicklistInfo();
        updateZSTheme();
        updateTableInfo();
        updateWorkbookLinkInfo();
        updateField();
        updateFontFamilyList();
        updateOnLoadDetails();

        JSONObjectWrapper responseData = new JSONObjectWrapper();
        responseData.put(Integer.toString(CommandConstants.DOCUMENT_META), getDocumentResponse());
        responseData.put(Integer.toString(CommandConstants.IS_NEW_CLIENT_RESPONSE), true);
        return getDocumentResponse();
    }

    @Override
    public JSONObjectWrapper generateConstraintResponse()
    {
        updateRowHeadersNew();
        updateColHeadersNew();
        updateFreezePanes();
        updateDataValidationRanges();
        updateConditionalFormatRanges();
        updateMergeCells();
        updateArrayFormulaRanges();
        updateCellsResponse();
        updateColumnLevelFormats();
        updateHiddenRows();
        updateHiddenColumns();
        updateConstraintResponseActiveInfo();
        updateInsertDeleteCells();
        updateFormRanges();
        updateAppliedConditionalCellStyleToRanges();
        updateMaxUsedCells();
        updateConstraintResponseFaultySheets();
        updateHideGrid();
        updateCheckBoxRanges();
        updateImageInfo();
        updateChartInfo();
        updateSlicerInfo();
        updateTimelineInfo();
        updateButtonInfo();
        updateSparklineInfo();
        updateZiaInfo();
        updateSheetZoom();
        updateSheetView();
        updateDataCleaningInfo();
        updatePicklistRangeInfo();
        updateSheetRtl();
        updatePublishedView();
        updateRootMergeCellsDataInfo();
        updateDataConnectionInfoForConstraintResponse();
        updateGroupingInfo();
        updateKnitChartsInfo();
        updateKnitChartsSheetInfo();
        updateKnitChartsModifiedInfo();
        updateNavigatorRefreshStatusInResponse();

        JSONObjectWrapper responseJson = new JSONObjectWrapper();
        responseJson.put(Integer.toString(CommandConstants.SHEET_META), getConstraintResponse());
        responseJson.put(JSONConstants.SHEET_FAULTY_LIST, this.faultySheetObj);
        return responseJson;
    }

    @Override
    public JSONObjectWrapper generateUserSpecificResponse()
    {
        updateProtectedRanges();
        updateProtectedSheets();
        updateDataValidationErrorStatus();
        updateServerClipObject();
        updateReplaceAllCount();
        updateRangeHighlight();
        updateChartClipObject();
        updateUserNotification();
        updateErrorNotification();
        updateNewFilterDetails();
        updateFilteredRowsAndCount();
        updateReceiverUTID();
        updateKnitChartsClipInfo();
        updateSheetFilterTableInfo();
        return getUserSpecificResponse();
    }


    /** --------------------------------------DocumentJsonResponseCreation --------------------------------------------- */

    private void updateWorkbookLinkInfo() {
        JSONObjectWrapper response = getDocumentResponseHolder().getWorkbookLinkResponse();
        if (response != null) {
            getDocumentResponse().put(Integer.toString(CommandConstants.WORKBOOK_LINK), response);
        }
    }

    private void updateAllImageDetails()
    {
        DocumentResponseHolder documentResponseHolder = getDocumentResponseHolder();
        if(documentResponseHolder.isGenerateWorkbookImages())
        {
            WorkbookContainer container = getContainer();
            Boolean isRemoteMode = (container.getResourceId() == null);
            String docID = container.getDocId();
            if(!isRemoteMode){
                docID = container.getResourceId();
            }
            JSONObjectWrapper imageJson = new JSONObjectWrapper();
            String action = Integer.toString(CommandConstants.META);
            JSONArrayWrapper imageJsonArray = imageJson.has(action) ? imageJson.getJSONArray(action) : new JSONArrayWrapper();
            try
            {
                // TODO PASS IMAGEBOOK IN RESPONSE OBJECT
                String versionNo;
                try
                {
                    versionNo = CurrentRealm.getWorkBookIdentity();
                }
                catch(Exception e)
                {
                    versionNo = null;
                }
                ImageBook imageBook = container.getImageBook(versionNo);
                if (versionNo != null)
                {
                    container.addToVersionBooksMap(versionNo);
                }
                if (imageBook != null)
                {
                    imageJsonArray = ImageUtils.getImagesResponse(container, imageBook, isRemoteMode, docID, documentResponseHolder.getPublishRangeId());
                }
            }
            catch (Exception ex)
            {
                LOGGER.log(Level.WARNING, "[IMAGES][Exception] Exception while generating list of workbook image response", ex);
            }

            imageJson.put(action, imageJsonArray);
            getDocumentResponse().put(Integer.toString(CommandConstants.WORKBOOK_IMAGES), imageJson);
        }
    }

    private void updateNewlyCreatedWorkbookImages()
    {
        DocumentResponseHolder documentResponseHolder = getDocumentResponseHolder();
        WorkbookContainer container = getContainer();
        if(documentResponseHolder.getImageMetaBeanList() != null && !documentResponseHolder.getImageMetaBeanList().isEmpty())
        {
            Boolean isRemoteMode = (container.getResourceId() == null);
            String docID = container.getDocId();
            if(!isRemoteMode){
                docID = container.getResourceId();
            }
            String action = Integer.toString(CommandConstants.INSERT);
            JSONObjectWrapper imageJson = new JSONObjectWrapper();
            JSONArrayWrapper imageJsonArray = new JSONArrayWrapper();
            try
            {
                imageJsonArray = imageJson.has(action) ? imageJson.getJSONArray(action) : new JSONArrayWrapper();
                ImageBook imageBook = container.getImageBook(null);
                for(com.adventnet.zoho.websheet.model.response.beans.ImageMetaBean imageMetaBean : documentResponseHolder.getImageMetaBeanList())
                {
                    JSONObjectWrapper imageResponseObject = ImageUtils.getImageResponse(imageMetaBean.getImageId(), imageMetaBean.getDummyId(), imageBook.getImage(imageMetaBean.getImageId()), isRemoteMode, docID, documentResponseHolder.getPublishRangeId());

                    imageJsonArray.put(imageResponseObject);
                }
            }
            catch (Exception ex)
            {
                LOGGER.log(Level.WARNING, "[IMAGES] Exception while generating add workbook image response {0}", ex);
            }

            imageJson.put(action, imageJsonArray);
            getDocumentResponse().put(Integer.toString(CommandConstants.WORKBOOK_IMAGES), imageJson);
        }
    }

    private void updateForcedReloadTile(){
        if(getDocumentResponseHolder().isForcedReloadTile()){
            getDocumentResponse().put(Integer.toString(CommandConstants.FORCED_REFRESH_TILE), 1);
        }
    }

    private void updateRevertVersion() {

        if (getDocumentResponseHolder().isRevertVesion()) {
            getDocumentResponse().put(Integer.toString(CommandConstants.IS_REVERT_VERSION), 1);
        }
    }

    private void updateVersionInfo() {
        VersionInfoBean versionInfoBean = getDocumentResponseHolder().getVersionInfoBean();
        if (versionInfoBean != null) {
            JSONObjectWrapper versionMeta = new JSONObjectWrapper();
            versionMeta.put(Integer.toString(CommandConstants.VERSION_NUMBER), versionInfoBean.getVersionNumber());
            getDocumentResponse().put(Integer.toString(CommandConstants.VERSION), versionMeta);
        }
    }

    private void updateRowHeaderDefinition() {
        if (getDocumentResponseHolder().getRowHeaderDefinitions()) {
            JSONObjectWrapper rowHeaderDef = ResponseUtils.getRowStyles(getWorkbook());

            JSONObjectWrapper metaRowHeaderInfo = new JSONObjectWrapper();
            metaRowHeaderInfo.put(Integer.toString(CommandConstants.META), rowHeaderDef);
            getDocumentResponse().put(Integer.toString(CommandConstants.ROW_HEADERS_DEFINITION), metaRowHeaderInfo);
        }
    }

    private void updateColHeaderDefinition() {
        if (getDocumentResponseHolder().getColHeaderDefinitions()) {
            JSONObjectWrapper colHeaderDef = ResponseUtils.getColStyles(getWorkbook());

            JSONObjectWrapper metaColHeaderInfo = new JSONObjectWrapper();
            metaColHeaderInfo.put(Integer.toString(CommandConstants.META), colHeaderDef);

            getDocumentResponse().put(Integer.toString(CommandConstants.COLUMN_HEADERS_DEFINITION), metaColHeaderInfo);

        }

    }

    private void updateDocumentMeta() {

        com.adventnet.zoho.websheet.model.response.beans.DocumentMetaBean documentMetaBean = getDocumentResponseHolder().getDocumentMeta();
        JSONObjectWrapper documentResponse = getDocumentResponse();
        if (documentMetaBean != null)  {
            JSONObjectWrapper documentMeta = new JSONObjectWrapper();
            if(documentMetaBean.getDocInfo()) {
                ResponseUtils.getDocumentMeta(documentMeta, documentMetaBean.getPrivileges(), documentMetaBean.getRangeId(), documentMetaBean.isPublishedView());
            }

            if(documentMetaBean.getLocaleInfo()) {
                ResponseUtils.getLocaleMeta(documentMeta);
            }

            if(documentMetaBean.getShareInfo()) {
                ResponseUtils.getShareMeta(documentMeta, documentMetaBean.getPrivileges(), documentMetaBean.getRangeId());
                if(documentMeta.has(Integer.toString(CommandConstants.SHARE_META))) {
                    PublishRangeUtils.getPublishedRangeDetails(getContainer(), documentMeta.getJSONObject(Integer.toString(CommandConstants.SHARE_META)));
                }
            }

            if(documentMetaBean.getLockedInfo()) {
                ResponseUtils.getLockedInfo(documentMeta);
            }
            if(documentMetaBean.isOnLoad())
            {
                documentResponse.put(Integer.toString(CommandConstants.MAX_COLS), Utility.MAXNUMOFCOLS);
                documentResponse.put(Integer.toString(CommandConstants.MAX_ROWS), Utility.MAXNUMOFROWS);
            }
            else
            {
                documentMeta.put(Integer.toString(CommandConstants.MAX_COLS), Utility.MAXNUMOFCOLS);
                documentMeta.put(Integer.toString(CommandConstants.MAX_ROWS), Utility.MAXNUMOFROWS);

                if(!getContainer().isRemoteMode()) {
                    JSONObjectWrapper activeWorkFlows = WorkDriveInfoUtils.getWorkflowInfo(getContainer().getResourceId());
                    JSONArrayWrapper manualWorkFlowsList = WorkDriveInfoUtils.fetchAllManualWorkflows(getContainer().getResourceId());
                    JSONArrayWrapper workFlowsListActivities = WorkDriveInfoUtils.getWorkflowActivities(getContainer().getResourceId());
                    documentMeta.put(Integer.toString(CommandConstants.WORKFOLW_META), activeWorkFlows.toString());
                    documentMeta.put(Integer.toString(CommandConstants.WORKFOLW_LIST), manualWorkFlowsList.toString());
                    documentMeta.put(Integer.toString(CommandConstants.WORKFOLW_ACTIVITY), workFlowsListActivities.toString());
                }
            }

//            documentMeta = ResponseUtils.getDocumentMeta();

//                        JSONObjectWrapper              metaDocumentInfo                 =       new     JSONObjectWrapper();
//                                                metaDocumentInfo.put(Integer.toString(CommandConstants.META), documentMeta);

            documentResponse.put(Integer.toString(CommandConstants.META), documentMeta);
        }

    }

    private void updateUserMeta() {
        if (getDocumentResponseHolder().getUserMeta()) {

            String rmUser = CurrentRealm.getUserProfile().getZUserId();
            JSONObjectWrapper userMeta = ResponseUtils.getUsersMetaInfo(rmUser);
            getDocumentResponse().put(Integer.toString(CommandConstants.USERS_METAINFO), userMeta);

            String zuid = String.valueOf(DocumentUtils.getZUID());
            JSONObjectWrapper webUserMeta = ResponseUtils.getUsersMetaInfo(zuid);
            getDocumentResponse().put(Integer.toString(CommandConstants.WEB_USERMETA_INFO), webUserMeta);
        }
    }

    private JSONObjectWrapper getWorkBookFontList(){
        JSONObjectWrapper fontMap = new JSONObjectWrapper();
        try{
            Map<String, FontFace> fontFaceMap = getWorkbook().getFontFaceMap();
            for(Map.Entry<String, FontFace> entry : fontFaceMap.entrySet()){
                FontFace font = entry.getValue();
                String fontFamily = font.getFontFamily();
                if(fontFamily != null){
                    FontFaceInfo fontInfo = new FontFaceInfo(fontFamily, font.getStyleName());
                    fontMap.put(fontInfo.getFontFamily(), fontInfo.toJSON());
                }
            }
        }catch (Exception e){
            LOGGER.log(Level.WARNING, "[FONT] Exception while generating font list response {0}", e);//NO i18N
        }
        return fontMap;
    }

    private void updateFontFamilyList(){
        com.adventnet.zoho.websheet.model.response.beans.UserFontBean bean = getDocumentResponseHolder().getFontFamilyList();
        if(bean!= null){
            if(CommandConstants.OperationType.GETFONT_LIST == bean.getOperationType()){
                getDocumentResponse().put(Integer.toString(CommandConstants.FONTFAMILYLIST), getWorkBookFontList());
            }
            if(CommandConstants.OperationType.GENERATE_LIST == bean.getOperationType()){
                String zuid = bean.getzuid();
                ArrayList<String> UserFonts = new ArrayList<String>();
                JSONArrayWrapper userfontjson;
                userfontjson = new JSONArrayWrapper(FCUtil.getUserFontsASJSON(zuid, true));
                for (int i = 0; i < userfontjson.length(); ++i) {

                    JSONObjectWrapper userfont = userfontjson.getJSONObject(i);
                    String fontname = userfont.getString("family");
                    UserFonts.add(fontname);
                }
                getDocumentResponse().put(Integer.toString(CommandConstants.USER_FONTLIST),UserFonts);
/*
            UserProfile userProfile = CurrentRealm.getUserProfile();
            String userName = null;
            Long _zuid = null;
            if(userProfile != null && StringUtils.isNumeric(userProfile.getZUserId())) {
                _zuid = Long.parseLong(userProfile.getZUserId());
                userName = userProfile.getUserName();

                JSONObjectWrapper respObject = EngineUtils.getInstance().readUserCustomFonts( _zuid, userName);
                JSONArrayWrapper jsonArray;
                try{
                    jsonArray = respObject.getJSONArray("USERLEVELCUSTOMFONTS");//NO I18N
                }catch (Exception e){
                    jsonArray = new JSONArrayWrapper();
                }
                List<String> jsonList = JSONArrayWrapper.toList(jsonArray);
                jsonArray = JSONArrayWrapper.fromCollection(jsonList);
                documentResponseJson.put(Integer.toString(CommandConstants.USER_CUSTOM_FONTLIST),jsonArray);
            }

 */

            }
        }
    }

    private void updateCellStyleDefinition() {
        if (getDocumentResponseHolder().getCellStylesDefinitions()) {
            Workbook workbook = getWorkbook();

            JSONObjectWrapper cellStyleDef = ResponseUtils.getCellStylesWithDef(workbook);
            JSONObjectWrapper metaCellStyleInfo = new JSONObjectWrapper();
            metaCellStyleInfo.put(Integer.toString(CommandConstants.META), cellStyleDef);


            if (cellStyleDef != null && !cellStyleDef.isEmpty()) {
                getDocumentResponse().put(Integer.toString(CommandConstants.CELLSTYLES_DEFINITION), metaCellStyleInfo);
            }

        }
    }

    private void updateTextStyleDefinition()
    {
        if (getDocumentResponseHolder().getTextStylesDefinitions())
        {

            JSONObjectWrapper textStyleWithDef = ResponseUtils.getTextStyleWithDef(getWorkbook());

            JSONObjectWrapper metaCellStyleInfo = new JSONObjectWrapper();
            metaCellStyleInfo.put(Integer.toString(CommandConstants.META), textStyleWithDef);

            if (textStyleWithDef != null && !textStyleWithDef.isEmpty())
            {
                getDocumentResponse().put(Integer.toString(CommandConstants.TEXT_STYLES_DEFINTION), metaCellStyleInfo);
            }

        }
    }

    private void updateSheetOperations()
    {

        List<SheetWrapper> sheetWrappers = getDocumentResponseHolder().getSheetWrapper();

        if (sheetWrappers != null && !sheetWrappers.isEmpty())
        {
            Workbook workbook = getWorkbook();
            String action = Integer.toString(CommandConstants.META);
            String insertAction = null;
            JSONArrayWrapper data = new JSONArrayWrapper();
            JSONArrayWrapper InsertData = new JSONArrayWrapper();
            JSONObjectWrapper sheetResponseObj = new JSONObjectWrapper();
            for (SheetWrapper sheetWrapper : sheetWrappers)
            {
                JSONArrayWrapper sheetDetails = new JSONArrayWrapper();
                if(CommandConstants.OperationType.GENERATE_LIST == sheetWrapper.getOperationType())
                {
                    data = sheetWrapper.getSheetName() != null ? ResponseUtils.getSheetInfo(workbook, sheetWrapper.getSheetName(), true) : ResponseUtils.getSheetNameList(workbook, true);
                }
                if (CommandConstants.OperationType.INSERT == sheetWrapper.getOperationType())
                {
                    Sheet sheetObj = workbook.getSheet(sheetWrapper.getSheetName());
                    sheetDetails.put(0, sheetObj.getAssociatedName());
                    sheetDetails.put(1, sheetObj.getName());
                    sheetDetails.put(2, sheetObj.getSheetIndex());
                    sheetDetails.put(3, sheetObj.getSheetStyle().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook));
                    sheetDetails.put(4, sheetObj.isHidden());



                    data.put(sheetDetails);
                    action = Integer.toString(CommandConstants.INSERT);

                }
                else if (CommandConstants.OperationType.PASTE == sheetWrapper.getOperationType())
                {
                    JSONArrayWrapper pastedSheets = sheetWrapper.getPastedSheetNames();
                    for (int i = 0; i < pastedSheets.length(); i++)
                    {
                        String shnm = pastedSheets.getString(i);
                        Sheet actSheet = workbook.getSheet(shnm);
                        String asn = actSheet.getAssociatedName();
                        sheetDetails.put(0, asn); //associate sheetName
                        sheetDetails.put(1, shnm); // sheetname
                        sheetDetails.put(2, actSheet.getSheetIndex());  //position
                        sheetDetails.put(3, actSheet.getSheetStyle().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook));  // tab color
                        sheetDetails.put(4, actSheet.isHidden());
                    }
                    data.put(sheetDetails);
                    action = Integer.toString(CommandConstants.INSERT);
                }
                else if (CommandConstants.OperationType.DELETE == sheetWrapper.getOperationType())
                {
                    sheetDetails.put(0, sheetWrapper.getAssociatedSheetName());
                    sheetDetails.put(1, sheetWrapper.getSheetName());
                    data.put(sheetDetails);
                    action = Integer.toString(CommandConstants.DELETE);

                }
                else if (CommandConstants.OperationType.RENAME == sheetWrapper.getOperationType())
                {
                    sheetDetails.put(0, sheetWrapper.getAssociatedSheetName());
                    sheetDetails.put(1, sheetWrapper.getSheetName());
                    data.put(sheetDetails);
                    action = Integer.toString(CommandConstants.RENAME);
                }
                else if (CommandConstants.OperationType.MOVE == sheetWrapper.getOperationType())
                {
                    Sheet sheetObj = workbook.getSheetByAssociatedName(sheetWrapper.getAssociatedSheetName());
                    sheetDetails.put(0, sheetObj.getAssociatedName());
                    sheetDetails.put(1, sheetObj.getName());
                    sheetDetails.put(2, sheetObj.getSheetIndex());
                    data.put(sheetDetails);
                    action = Integer.toString(CommandConstants.MOVE);
                }
                else if (CommandConstants.OperationType.SHEET_TABCOLOR == sheetWrapper.getOperationType())
                {
                    Sheet sheetObj = workbook.getSheetByAssociatedName(sheetWrapper.getAssociatedSheetName());
                    sheetDetails.put(0, sheetObj.getAssociatedName());
                    sheetDetails.put(1, sheetObj.getName());
                    sheetDetails.put(2, sheetObj.getSheetIndex());
                    sheetDetails.put(3, sheetObj.getSheetStyle().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook));
                    data.put(sheetDetails);
                    action = Integer.toString(CommandConstants.TABCOLOR);
                }
                else if (CommandConstants.OperationType.HIDE == sheetWrapper.getOperationType())
                {
                    Sheet sht = workbook.getSheetByAssociatedName(sheetWrapper.getAssociatedSheetName());
                    sheetDetails.put(0, sht.getAssociatedName());
                    sheetDetails.put(1, sht.getName());
                    sheetDetails.put(4, sht.isHidden());
                    data.put(sheetDetails);
                    action = Integer.toString(CommandConstants.HIDDEN_SHEETS);
                }
                else if (CommandConstants.OperationType.UNHIDE == sheetWrapper.getOperationType())
                {
                    Sheet sht = workbook.getSheetByAssociatedName(sheetWrapper.getAssociatedSheetName());
                    sheetDetails.put(0, sht.getAssociatedName());
                    sheetDetails.put(1, sht.getName());
                    sheetDetails.put(2, sht.getSheetIndex());
                    sheetDetails.put(3, sht.getSheetStyleReadOnly().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook));
                    sheetDetails.put(4, sht.isHidden());
                    data.put(sheetDetails);
                    action = Integer.toString(CommandConstants.UNHIDDEN_SHEETS);
                }
                else if (CommandConstants.OperationType.IMPORT_SHEET_INSERT == sheetWrapper.getOperationType())
                {
                    JSONArrayWrapper pastedSheets = sheetWrapper.getPastedSheetNames();
                    JSONArrayWrapper newAry = new JSONArrayWrapper();
                    for (int i = 0; i < pastedSheets.length(); i++)
                    {
                        JSONArrayWrapper respAry = new JSONArrayWrapper();
                        String shnm = pastedSheets.getString(i);
                        Sheet actSheet = workbook.getSheet(shnm);
                        String asn = actSheet.getAssociatedName();
                        respAry.put(0, asn); //associate sheetName
                        respAry.put(1, shnm); // sheetname
                        respAry.put(2, actSheet.getSheetIndex());  //position
                        respAry.put(3, actSheet.getSheetStyle().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook));  // tab color
                        respAry.put(4, actSheet.isHidden());
                        newAry.put(respAry);
                    }
                    action = Integer.toString(CommandConstants.IMPORT_AS_NEWSHEET);
                    data.put(newAry);
                }
                else if(CommandConstants.OperationType.THEME == sheetWrapper.getOperationType())
                {
                    Sheet[] sheets = workbook.getSheets();
                    for(Sheet sht : sheets)
                    {
                        JSONArrayWrapper respAry = new JSONArrayWrapper();
                        if(!sht.isHidden())
                        {
                            respAry.put(0, sht.getAssociatedName()); //associate sheetName
                            respAry.put(1, sht.getName()); // sheetname
                            respAry.put(2, sht.getSheetIndex());  //position
                            respAry.put(3, sht.getSheetStyleReadOnly().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook));
                            respAry.put(4, sht.isHidden());
                            data.put(respAry);
                        }
                    }
                    action = Integer.toString(CommandConstants.TABCOLOR);
                }
                else if(CommandConstants.OperationType.FIELDS_SHEET_INSERT == sheetWrapper.getOperationType())
                {
                    Sheet sht = workbook.getSheet(sheetWrapper.getSheetName());
                    int sheetIndex = workbook.getSheetIndex(sheetWrapper.getSheetName());

                    sheetDetails.put(0, sht.getAssociatedName());
                    sheetDetails.put(1, sheetWrapper.getSheetName());
                    sheetDetails.put(2, sheetIndex);
                    sheetDetails.put(3, sht.getSheetStyle().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook));
                    sheetDetails.put(4, sht.isHidden());
                    data.put(sheetDetails);
                    action = Integer.toString(CommandConstants.FIELDS_SHEET_INSERT);
                }

                if(CommandConstants.OperationType.INSERT == sheetWrapper.getOperationType() || CommandConstants.OperationType.IMPORT_SHEET_INSERT == sheetWrapper.getOperationType() || CommandConstants.OperationType.PASTE == sheetWrapper.getOperationType())
                {
                    JSONArrayWrapper pastedSheets = sheetWrapper.getPastedSheetNames();
                    JSONArrayWrapper newAry = new JSONArrayWrapper();
                    if(pastedSheets!=null && !pastedSheets.isEmpty())
                    {
                        for (int i = 0; i < pastedSheets.length(); i++)
                        {
                            JSONArrayWrapper respAry = new JSONArrayWrapper();
                            String shnm = pastedSheets.getString(i);
                            Sheet actSheet = workbook.getSheet(shnm);
                            String asn = actSheet.getAssociatedName();
                            respAry.put(0, asn); //associate sheetName
                            respAry.put(1, shnm); // sheetname
                            respAry.put(2, actSheet.getSheetIndex());  //position
                            respAry.put(3, actSheet.getSheetStyle().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook));  // tab color
                            respAry.put(4, actSheet.isHidden());
                            newAry.put(respAry);
                        }
                    }
                    else
                    {
                        JSONArrayWrapper respAry = new JSONArrayWrapper();
                        Sheet sht = workbook.getSheet(sheetWrapper.getSheetName());
                        respAry.put(0, sht.getAssociatedName());
                        respAry.put(1, sheetWrapper.getSheetName());
                        respAry.put(2, sht.getSheetIndex());
                        respAry.put(3, sht.getSheetStyle().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook));  // tab color
                        respAry.put(4, sht.isHidden());  // tab color
                        newAry.put(respAry);
                    }
                    insertAction = Integer.toString(CommandConstants.INSERT_SHEETS);
                    InsertData.put(newAry);
                }
            }
            sheetResponseObj.put(action, data);
            if(insertAction != null)
            {
                sheetResponseObj.put(insertAction, InsertData);
            }
            getDocumentResponse().put(Integer.toString(CommandConstants.WORKSHEETS), sheetResponseObj);
        }

    }

    private void updateSyncStyleNames() {

        Map<String, Map<String, String>> syncStyleNames = getDocumentResponseHolder().getSyncStyleNames();

        if (syncStyleNames != null && !syncStyleNames.isEmpty()) {
            JSONObjectWrapper documentResponseJson = getDocumentResponse();
            JSONObjectWrapper tempStyleName = new JSONObjectWrapper();

            if (syncStyleNames.containsKey("cellStyle")) {

                tempStyleName.put(Integer.toString(CommandConstants.SYNC), syncStyleNames.get("cellStyle"));
                documentResponseJson.put(Integer.toString(CommandConstants.CELLSTYLES_DEFINITION), tempStyleName);
            }

            if (syncStyleNames.containsKey("textStyle")) {

                tempStyleName = new JSONObjectWrapper();
                tempStyleName.put(Integer.toString(CommandConstants.SYNC), syncStyleNames.get("textStyle"));
                documentResponseJson.put(Integer.toString(CommandConstants.TEXT_STYLES_DEFINTION), tempStyleName);
            }

            if (syncStyleNames.containsKey("rowStyle")) {

                tempStyleName = new JSONObjectWrapper();
                tempStyleName.put(Integer.toString(CommandConstants.SYNC), syncStyleNames.get("rowStyle"));
                documentResponseJson.put(Integer.toString(CommandConstants.ROW_HEADERS_DEFINITION), tempStyleName);
            }

            if (syncStyleNames.containsKey("colStyle")) {

                tempStyleName = new JSONObjectWrapper();
                tempStyleName.put(Integer.toString(CommandConstants.SYNC), syncStyleNames.get("colStyle"));
                documentResponseJson.put(Integer.toString(CommandConstants.COLUMN_HEADERS_DEFINITION), tempStyleName);
            }

        }

    }

    private void updateActiveInfo() {
        com.adventnet.zoho.websheet.model.response.beans.ActiveInfoBean activeInfoBean = getDocumentResponseHolder().getActiveInfoBean();
        if (activeInfoBean != null) {
            JSONObjectWrapper activeInfo = new JSONObjectWrapper();

            activeInfo.put(Integer.toString(CommandConstants.ACTIVESHEET_NAME), activeInfoBean.getActiveSheetName());
            activeInfo.put(Integer.toString(CommandConstants.COMMENT_INFO),activeInfoBean.getCommentInfo());
            activeInfo.put(Integer.toString(CommandConstants.DATA_CONNECTION),activeInfoBean.getActiveDataConnectionInfo());
//                        activeInfo.put(Integer.toString(CommandConstants.ACTIVE_CELL),          activeInfoBean.getActiveCell());
//
//                        activeInfo.put(Integer.toString(CommandConstants.ACTIVE_RANGES),        activeInfoBean.getActiveRanges());
//                        activeInfo.put(Integer.toString(CommandConstants.PERSISTED_POSITIONS),  activeInfoBean.getPersistedPositions());

            getDocumentResponse().put(Integer.toString(CommandConstants.ACTIVE_INFO), activeInfo);
        }

    }

    private void updateActionIdentifier()
    {
        com.adventnet.zoho.websheet.model.response.beans.ActionIdentifierBean actionIdentifierBean = getDocumentResponseHolder().getActiveIdetifierBean();
        if(actionIdentifierBean != null)
        {
            JSONObjectWrapper actionIdentifierJson = new JSONObjectWrapper();
            if(actionIdentifierBean.getActionConstant() != -1)
            {
                actionIdentifierJson.put(Integer.toString(CommandConstants.ACTION_CONSTANT), actionIdentifierBean.getActionConstant());
            }
            if(actionIdentifierBean.getAid() != -1)
            {
                actionIdentifierJson.put(Integer.toString(CommandConstants.EXECUTED_ACTION_ID), actionIdentifierBean.getAid());
            }
            if(actionIdentifierBean.getLastExecutedActionId() != -1)
            {
                actionIdentifierJson.put(Integer.toString(CommandConstants.LAST_EXECUTED_ACTION_ID), actionIdentifierBean.getLastExecutedActionId());
            }
            if(actionIdentifierBean.getLastSavedActionId() != -1)
            {
                actionIdentifierJson.put(Integer.toString(CommandConstants.LAST_SAVED_ID), actionIdentifierBean.getLastSavedActionId());
            }

            if(actionIdentifierBean.getdocumentServedState() != -1)
            {
                actionIdentifierJson.put(Integer.toString(CommandConstants.DOCUMENT_SERVED_STATE), actionIdentifierBean.getdocumentServedState());
            }
            if(!actionIdentifierBean.isServerAction())
            {
                // Used only for queued actions
                actionIdentifierJson.put(Integer.toString(CommandConstants.RSID), actionIdentifierBean.getRsid());
                //actionIdentifierJson.put(Integer.toString(CommandConstants.DIRECT_UPDATE),   actionIdentifierBean.isDirectUpdate());
                actionIdentifierJson.put(Integer.toString(CommandConstants.IS_SERVER_ACTION), false);

            }
            else
            {
                actionIdentifierJson.put(Integer.toString(CommandConstants.IS_SERVER_ACTION), true);
            }

            actionIdentifierJson.put(Integer.toString(CommandConstants.UNIQUE_TAB_ID), actionIdentifierBean.getUtid());
            getDocumentResponse().put(Integer.toString(CommandConstants.ACTION_IDENTIFIER), actionIdentifierJson);
        }

    }

    private void updateNewlyCreatedStyles() {

        HashMap<String, Set<String>> newlyCreatedstylesKeyInfo = getDocumentResponseHolder().getNewlyCreatedstylesKeyInfo();

        if (newlyCreatedstylesKeyInfo != null) {
            JSONArrayWrapper stylesKey;
            JSONObjectWrapper stylesObj;
            Set<String> stylesKeys = newlyCreatedstylesKeyInfo.get("cellStylesKey");
            JSONObjectWrapper tempStyleName = new JSONObjectWrapper();
            Workbook workbook = getWorkbook();
            JSONObjectWrapper documentResponseJson = getDocumentResponse();
            if (stylesKeys != null) {
                stylesKey = JSONArrayWrapper.fromCollection(stylesKeys);
                stylesObj = ResponseUtils.getCellStylesDefinitionObj(workbook, stylesKey);
                if (!stylesObj.isEmpty()) {

                    tempStyleName.put(Integer.toString(CommandConstants.APPEND), stylesObj);
                    documentResponseJson.put(Integer.toString(CommandConstants.CELLSTYLES_DEFINITION), tempStyleName);
                }
            }

            stylesKeys = newlyCreatedstylesKeyInfo.get("textStylesKey");
            tempStyleName = new JSONObjectWrapper();
            if (stylesKeys != null) {
                stylesKey = JSONArrayWrapper.fromCollection(stylesKeys);
                stylesObj = ResponseUtils.getTextStylesDefinitionObj(workbook, stylesKey);

                if (!stylesObj.isEmpty()) {

                    tempStyleName.put(Integer.toString(CommandConstants.APPEND), stylesObj);
                    documentResponseJson.put(Integer.toString(CommandConstants.TEXT_STYLES_DEFINTION), tempStyleName);
                }
            }

            stylesKeys = newlyCreatedstylesKeyInfo.get("columnStylesKey");
            if (stylesKeys != null) {
                stylesKey = JSONArrayWrapper.fromCollection(stylesKeys);
                stylesObj = ResponseUtils.getColumnStylesDefinitionObj(workbook, stylesKey);

                if (!stylesObj.isEmpty()) {
                    tempStyleName = new JSONObjectWrapper();
                    tempStyleName.put(Integer.toString(CommandConstants.APPEND), stylesObj);
                    documentResponseJson.put(Integer.toString(CommandConstants.COLUMN_HEADERS_DEFINITION), tempStyleName);
                }
            }

            stylesKeys = newlyCreatedstylesKeyInfo.get("rowStylesKey");
            if (stylesKeys != null) {
                stylesKey = JSONArrayWrapper.fromCollection(stylesKeys);
                stylesObj = ResponseUtils.getRowStylesDefinitionObj(workbook, stylesKey);

                if (!stylesObj.isEmpty()) {
                    tempStyleName = new JSONObjectWrapper();
                    tempStyleName.put(Integer.toString(CommandConstants.APPEND), stylesObj);
                    documentResponseJson.put(Integer.toString(CommandConstants.ROW_HEADERS_DEFINITION), tempStyleName);
                }
            }

        }
    }

    private void updateFaultySheets() {

        HashSet<String> faultySheets = getDocumentResponseHolder().getFaultySheets();

        if (faultySheets != null && !faultySheets.isEmpty()) {

            JSONObjectWrapper faultySheetsObj = new JSONObjectWrapper();
            faultySheetsObj.put(Integer.toString(CommandConstants.WORKSHEETS), faultySheets.toArray());

            getDocumentResponse().put(Integer.toString(CommandConstants.FAULTY), faultySheetsObj);
        }

    }

    private void updateFormSheets() {

        com.adventnet.zoho.websheet.model.response.beans.FormBean formBean = getDocumentResponseHolder().getFormBean();

        if (formBean != null) {
            JSONObjectWrapper documentResponseJson = getDocumentResponse();
            CommandConstants.OperationType operationType = formBean.getOperationType();

            if (operationType == CommandConstants.OperationType.GENERATE_LIST) {

                Map<String, JSONObjectWrapper> formSheetMap = formBean.getsheetFormMap();

                Set<String> keySet = formSheetMap.keySet();
                JSONArrayWrapper formDataHolder = new JSONArrayWrapper();
                for (Iterator iterator = keySet.iterator(); iterator.hasNext();) {

                    String sheetName = (String) iterator.next();

                    Sheet sheet = getWorkbook().getSheet(sheetName);

                    if (sheet != null) {

                        String associatedSheetName = sheet.getAssociatedName();
                        JSONObjectWrapper formObject = formSheetMap.get(sheetName);
                        String formRid = formObject.getString(JSONConstants.FORM_RID);
                        //Unwanted
                        JSONObjectWrapper formObj = new JSONObjectWrapper();
                        formObj.put(Integer.toString(CommandConstants.FORM_RID), formRid);
                        formObj.put(Integer.toString(CommandConstants.FORM_PUBLISH_TYPE), formObject.getInt(JSONConstants.FORM_PUBLISH_TYPE));
                        JSONArrayWrapper formAry = new JSONArrayWrapper();

                        formAry.put(associatedSheetName);
                        formAry.put(formObj);

                        formDataHolder.put(formAry);

                    }
                }

                JSONObjectWrapper formBundle = new JSONObjectWrapper();
                formBundle.put(Integer.toString(CommandConstants.META), formDataHolder);

                documentResponseJson.put(Integer.toString(CommandConstants.FORM_SHEETS), formBundle);

            } else if (operationType == ADD) {

                JSONObjectWrapper formObj = new JSONObjectWrapper();
                formObj.put(Integer.toString(CommandConstants.FORM_RID), formBean.getFormRid());
                formObj.put(Integer.toString(CommandConstants.FORM_PUBLISH_TYPE), formBean.getFormPubishType());
                JSONArrayWrapper formAry = new JSONArrayWrapper();
                formAry.put(formBean.getFormSheet());
                formAry.put(formObj);

                JSONObjectWrapper formBundle = new JSONObjectWrapper();

                formBundle.put(Integer.toString(CommandConstants.ADD), formAry);

                documentResponseJson.put(Integer.toString(CommandConstants.FORM_SHEETS), formBundle);

            } else if (operationType == CommandConstants.OperationType.DELETE) {

                String formSheetId = formBean.getFormSheet();
                if (formBean.isFormDeleted() && (formSheetId != null)) {

                    JSONObjectWrapper formBundle = new JSONObjectWrapper();
                    formBundle.put(Integer.toString(CommandConstants.DELETE), formSheetId);

                    documentResponseJson.put(Integer.toString(CommandConstants.FORM_SHEETS), formBundle);
                }
            } else if (operationType == CommandConstants.OperationType.MODIFY) {
                JSONObjectWrapper formObj = new JSONObjectWrapper();
                formObj.put(Integer.toString(CommandConstants.FORM_RID), formBean.getFormRid());
                formObj.put(Integer.toString(CommandConstants.FORM_PUBLISH_TYPE), formBean.getFormPubishType());
                JSONArrayWrapper formAry = new JSONArrayWrapper();
                formAry.put(formBean.getFormSheet());
                formAry.put(formObj);
                JSONObjectWrapper formBundle = new JSONObjectWrapper();
                formBundle.put(Integer.toString(CommandConstants.MODIFY), formAry);
                documentResponseJson.put(Integer.toString(CommandConstants.FORM_SHEETS), formBundle);
            }
        }
    }

    private void updatePivotInfo() {
        Long time = System.currentTimeMillis();
        List<com.adventnet.zoho.websheet.model.response.beans.PivotBean> pivotBeanList = getDocumentResponseHolder().getPivotBean();
        PivotTable pivotTable;
        Map<String, Long> timeTracker = new HashMap<>();
        if (pivotBeanList != null) {
            Workbook workbook = getWorkbook();
            JSONObjectWrapper pivotJson = null;

            for(int i=0; i < pivotBeanList.size() ; i++)
            {
                CommandConstants.OperationType operationType = pivotBeanList.get(i).getOperationType();

                if (operationType != null && operationType == CommandConstants.OperationType.GENERATE_LIST) {

                    List<PivotTable> pivotTables = workbook.getPivotTables();
                    JSONObjectWrapper pivotJsonObj = new JSONObjectWrapper();

                    if (pivotTables != null) {
                        for (PivotTable pivotTable2 : pivotTables) {

                            JSONObjectWrapper pivotData = ResponseUtils.getPivotRanges(workbook, pivotTable2, timeTracker);
                            if(pivotData!=null && !pivotData.isEmpty()) {
                                pivotJsonObj.put(pivotTable2.getName(), pivotData);
                            }

                        }

                        JSONObjectWrapper pivotInfo = new JSONObjectWrapper();

                        pivotInfo.put(Integer.toString(CommandConstants.META), pivotJsonObj);

                        getDocumentResponse().put(Integer.toString(CommandConstants.PIVOT), pivotInfo);
                    }
                } else {

                    //String pivotId = pivotBean.getPivotId();

                    JSONArrayWrapper pivotIdArray = pivotBeanList.get(i).getPivotIdArray();
                    JSONObjectWrapper pivotInfo = new JSONObjectWrapper();
                    for(int j=0; j< pivotIdArray.length(); j++) {
                        String pivotId =  pivotIdArray.getString(j);
                        if (operationType == CommandConstants.OperationType.DELETE) {
                            pivotInfo.put(pivotId, 1);
                        }else {
                            pivotTable = workbook.getDataPivotTable(pivotId);
                            JSONObjectWrapper pivotData = ResponseUtils.getPivotRanges(workbook, pivotTable, timeTracker);
                            if(operationType == CommandConstants.OperationType.FAULTY)
                            {
                                pivotData.put(Integer.toString(CommandConstants.ERRORMSG),"Pivot Faulty Error");
                            }
                            pivotInfo.put(pivotId, pivotData);
                        }
                    }

                    if(pivotJson == null)
                    {
                        pivotJson = new JSONObjectWrapper();
                    }

                    if (operationType == ADD) {
                        pivotJson.put(Integer.toString(CommandConstants.ADD), pivotInfo);
                    } else if (operationType == CommandConstants.OperationType.MODIFY) {
                        pivotJson.put(Integer.toString(CommandConstants.MODIFY), pivotInfo);
                    }else if (operationType == CommandConstants.OperationType.UPDATE) {
                        pivotJson.put(Integer.toString(CommandConstants.UPDATE), pivotInfo);
                    } else if (operationType == CommandConstants.OperationType.DELETE) {
                        pivotJson.put(Integer.toString(CommandConstants.DELETE), pivotInfo);
                    } else if (operationType == CommandConstants.OperationType.FAULTY) {
                        pivotJson.put(Integer.toString(CommandConstants.FAULTY), pivotInfo);
                    }
                }
            }
            if(pivotJson != null)
            {
                getDocumentResponse().put(Integer.toString(CommandConstants.PIVOT), pivotJson);
            }
        }
        if (System.currentTimeMillis() - time > 3000) {
            LOGGER.log(Level.WARNING, "[Pivot_Response Slowness] Time taken to update pivot info: {0} ms, Tracker: {1}",
                    new Object[]{System.currentTimeMillis() - time, timeTracker});
        }
    }

    private void updateDataConnectionInfo(){
        com.adventnet.zoho.websheet.model.response.beans.DataConnectionBean dcBean = getDocumentResponseHolder().getDataConnectionBean();
        if(dcBean == null)
        {
            return;
        }
        WorkbookContainer container = getContainer();
        if(container.isGdriveDoc() || container.getResourceId() == null || container.isRemoteMode())
        {
            return;
        }
        JSONObjectWrapper dcInfo = new JSONObjectWrapper();
        JSONObjectWrapper webdataInfoObj = dcBean.getWebDataObj();
        Workbook workbook = getWorkbook();
        if(dcBean.getOperationType() == GENERATE_LIST)
        {
            try {
                webdataInfoObj = ExternalDataUtils.getWebDataList(workbook, container.getResourceId());
                if(!webdataInfoObj.isEmpty() && CurrentRealm.getUserProfile() != null){
                    dcInfo.put("isPrivilegedUser", new JSONObjectWrapper(ZohoFS.getCapabalitiy(CurrentRealm.getUserProfile().getZUserId(), container.getResourceId())).optBoolean("canShare"));
                }
            } catch (Exception e) {
                LOGGER.log(Level.INFO, "[Data-Connection] exception in WebDataCollection Response", e);
            }
        }
        else if(dcBean.getOperationType() == DUPLICATES)
        {
            try {
                webdataInfoObj = ExternalDataUtils.getWebDataInfo(container.getResourceId(), ActionJsonUtil.constructStringList(webdataInfoObj.names()));
            } catch (Exception e) {
                LOGGER.log(Level.INFO, "Exception Occurred While generating WebData Response", e);
            }
        }

        Iterator<String> webDataIdItr = webdataInfoObj.keys();
        JSONArrayWrapper webDataList = new JSONArrayWrapper();
        while (webDataIdItr.hasNext())
        {
            String webDataId = webDataIdItr.next();
            JSONObjectWrapper webDataObj = webdataInfoObj.getJSONObject(webDataId);
            DataRange rangeObj = workbook.getDataConnection().getDataConnectionRange(webDataId);
            if(rangeObj != null){
                webDataObj.put("sheetId", rangeObj.getAssociatedSheetName());
                webDataObj.put("sheetName", workbook.getSheetByAssociatedName(rangeObj.getAssociatedSheetName()).getName());
                webDataObj.put("startRow", rangeObj.getStartRowIndex());
                webDataObj.put("startCol", rangeObj.getStartColIndex());
                webDataObj.put("endRow", rangeObj.getEndRowIndex());
                webDataObj.put("endCol", rangeObj.getEndColIndex());
                webDataList.put(webDataObj);
            }
            else if(webDataObj.getInt("liveStatusCode") == DCLiveStatusCode.ACTIVE)
            {
                try {
                    LOGGER.log(Level.INFO, "[Data Connection] range null for WebDataID : {0}", new Object[]{webDataId});
                    DBActionManager.updateLiveStatusCode(container.getDocOwner(), webDataId, DCLiveStatusCode.RANGE_NOT_FOUND);
                } catch (DataAccessException e) {
                    LOGGER.log(Level.INFO, "[Data Connection] Error while changing StatusCode for WebDataID : {0}", new Object[]{webDataId});
                }
            }
        }
        dcInfo.put(Integer.toString(CommandConstants.META), webDataList);
        getDocumentResponse().put(Integer.toString(CommandConstants.DATA_CONNECTION), dcInfo);
    }

    private void updateOleObjectsInfo() {
        if (getDocumentResponseHolder().isContainsOleObj()) {
            getDocumentResponse().put(Integer.toString(CommandConstants.IS_CONTAINS_OLEOBJ), 1);
        }
    }

    private void updateImportInfo() {
        List<com.adventnet.zoho.websheet.model.response.beans.ImportBean> importBeans = getDocumentResponseHolder().getImportBean();
        if (importBeans != null && !importBeans.isEmpty()) {
            for (Iterator iterator = importBeans.iterator(); iterator.hasNext();) {
                com.adventnet.zoho.websheet.model.response.beans.ImportBean importBean = (com.adventnet.zoho.websheet.model.response.beans.ImportBean) iterator.next();
                if (importBean != null) {

                    JSONObjectWrapper importJSON = new JSONObjectWrapper();
                    CommandConstants.OperationType action = importBean.getOperationType();

                    if (action == CommandConstants.OperationType.REPLACE_SPREADSHEET) {
                        JSONObjectWrapper responseObj = new JSONObjectWrapper();
                        String actionOwnerName = DocumentUtils.getZUserName(importBean.getZUID());
                        responseObj.put(JSONConstants.LOGIN_NAME, actionOwnerName);

                        importJSON.put(Integer.toString(CommandConstants.REPLACE_SPREADSHEET), responseObj);
                    }
                    if (action == CommandConstants.OperationType.MODIFY) {
                        JSONObjectWrapper responseObj = new JSONObjectWrapper();
                        String actionOwnerName = DocumentUtils.getZUserName(importBean.getZUID());
                        responseObj.put(JSONConstants.LOGIN_NAME, actionOwnerName);
                        importJSON.put(Integer.toString(CommandConstants.REPLACE_CURRENTSHEET), responseObj);
                    }
                    getDocumentResponse().put(Integer.toString(CommandConstants.IMPORT), importJSON);
                }
            }
        }
    }

    private void updateDocumentSettingsInfo()
    {
        DocumentSettingsWrapper docSettingsWrapper = getDocumentResponseHolder().getDocumentSettingsWrapper();
        if(docSettingsWrapper != null)
        {
            String zuid = docSettingsWrapper.getZuid();
            Workbook workbook = getWorkbook();
            WorkbookAdditionalInfo workbookAdditionalInfo = getContainer().getWorkbookAdditionalInfo();
            JSONObjectWrapper docSettingsJSON = workbookAdditionalInfo.getJSON(workbook);
            SpreadsheetSettings spreadsheetSettings = workbookAdditionalInfo.getSpreadsheetSettings();
            //FORMATCELLS_REGIONAL
            Locale locale = spreadsheetSettings.getLocale();
            Locale currencyLocale = spreadsheetSettings.getCurrencyLocale();
            JSONObjectWrapper jsonObject = ClientUtils.regionalFormats(locale);
            docSettingsJSON.put(Integer.toString(CommandConstants.FORMATCELLS_REGIONAL), jsonObject);
            docSettingsJSON.put(Integer.toString(CommandConstants.REGIONAL_PATTERNS), FormatCellsUtils.getRegionalFormatsForCountry(locale.getCountry()));

            //CURRENCY.
            DecimalFormatSymbols decimalFormatSymbol = new DecimalFormatSymbols(currencyLocale);
            JSONObjectWrapper currencyFormat = getCurrencyFormats(decimalFormatSymbol, currencyLocale);
            docSettingsJSON.put(Integer.toString(CommandConstants.CURRENCY), currencyFormat);
            //DATE & TIME MENU.
            JSONObjectWrapper dateTimeMenu = FormatCellsUtils.getDateFormats(locale, workbookAdditionalInfo.getTimeZone());
            docSettingsJSON.put(Integer.toString(CommandConstants.DATE), dateTimeMenu);
            //FORMATCELLS_CUSTOM && //USERLEVELCUSTOMFONTS
            if(zuid != null && !zuid.contains("$"))
            {
                JSONObjectWrapper readCustomFormat;
                String userName = DocumentUtils.getZUserName(zuid);
                readCustomFormat = EngineUtils.readUserProperty((zuid.contains("$")) ? -1 : Long.parseLong(zuid), userName);
                docSettingsJSON.put(Integer.toString(CommandConstants.FORMATCELLS_CUSTOM), readCustomFormat);
            }
            //LOCALE INFO
            docSettingsJSON.put(Integer.toString(CommandConstants.WORKBOOKLOCALE), workbook.getAccountsLocale().toString());

            getDocumentResponse().put(Integer.toString(CommandConstants.SPREADSHEET_SETTINGS_NEW), docSettingsJSON);
        }
    }

    private void updateZSTheme(){
        com.adventnet.zoho.websheet.model.response.beans.ThemesBean themesBean= getDocumentResponseHolder().getZSTheme();
        Workbook workbook = getWorkbook();
        if (themesBean != null)
        {
            JSONObjectWrapper themeResp = new JSONObjectWrapper();
            CommandConstants.OperationType operationType = themesBean.getOperationType();
            if (operationType == CommandConstants.OperationType.GENERATE_LIST)
            {
                ZSTheme theme = workbook.getTheme();
                String themeName = ZSThemesLibrary.getThemeName(theme);
                JSONObjectWrapper themeJSON = theme.getAsJSON();
                /* Theme name found in server for client */
                themeJSON.put(Integer.toString(CommandConstants.THEME), themeName);
                themeResp.put(Integer.toString(CommandConstants.META), themeJSON);
            }
            else if (operationType == CommandConstants.OperationType.THEME)
            {
                ZSTheme theme = workbook.getTheme();
                JSONObjectWrapper themeJSON = theme.getAsJSON();
                String themeName = ZSThemesLibrary.getThemeName(theme);
                /* Theme name found in server for client */
                themeJSON.put(Integer.toString(CommandConstants.THEME), themeName);
                themeResp.put(Integer.toString(CommandConstants.MODIFY), themeJSON);
            }
            if(!themeResp.isEmpty()){
                getDocumentResponse().put(Integer.toString(CommandConstants.THEME), themeResp);
            }
        }
    }

    private void updateField(){
        com.adventnet.zoho.websheet.model.response.beans.FieldsBean fieldsBean= getDocumentResponseHolder().getFieldsBean();
        if (fieldsBean != null)
        {
            Workbook workbook = getWorkbook();
            JSONObjectWrapper fieldResp = new JSONObjectWrapper();
//            JSONObjectWrapper fieldJSON = new JSONObjectWrapper();
            CommandConstants.OperationType operationType = fieldsBean.getOperationType();
            List<Field> fields = workbook.getFields();

            switch(operationType)
            {
                case GENERATE_LIST:
                    //fieldJSON.put(Integer.toString(CommandConstants.FIELDS), fields);
                    fieldResp.put(Integer.toString(CommandConstants.META), fields);
                    break;
                case UPDATE:
                    fieldResp.put(Integer.toString(CommandConstants.UPDATE), fields);
                    break;
                case ADD:
                    JSONArrayWrapper fieldNameJson = fieldsBean.getfieldJSON().getJSONArray(JSONConstants.FIELD_NAMES_JSON);
                    Iterator<Object> it = fieldNameJson.iterator();
                    List<Field> field = new ArrayList<>();
                    while(it.hasNext())
                    {
                        String fieldName = (String) it.next();
                        field.add(workbook.getField(fieldName));
                    }
//                    fieldJSON.put(Integer.toString(CommandConstants.FIELDS), field);
                    fieldResp.put(Integer.toString(CommandConstants.ADD), field);
                    break;
                case EDIT:
                    JSONObjectWrapper fieldObj = fieldsBean.getfieldJSON().getJSONObject(JSONConstants.FIELDS_JSON);
//	            	fieldJSON.put(Integer.toString(CommandConstants.FIELDS), fieldObj);
                    fieldResp.put(Integer.toString(CommandConstants.EDIT), fieldObj);
                    break;
                case DELETE:
                    fieldObj = fieldsBean.getfieldJSON().getJSONObject(JSONConstants.FIELDS_JSON);
//	            	fieldJSON.put(Integer.toString(CommandConstants.FIELDS), fieldObj);
                    fieldResp.put(Integer.toString(CommandConstants.DELETE), fieldObj);
                    break;
                default:
                    break;
            }
            if(!fieldResp.isEmpty()){
                getDocumentResponse().put(Integer.toString(CommandConstants.FIELDS), fieldResp);
            }
        }
    }

    private void updateNamedRange() {
        NamedRangeWrapper namedRangeWrapper = getDocumentResponseHolder().getNamedRangeWrapper();

        JSONArrayWrapper modifiedRangeAry = new JSONArrayWrapper();
        JSONArrayWrapper addRangeAry = new JSONArrayWrapper();
        JSONArrayWrapper removedRangeAry = new JSONArrayWrapper();

        if (namedRangeWrapper != null) {
            Workbook workbook = getWorkbook();
            CommandConstants.OperationType operationType = namedRangeWrapper.getOperationType();
            JSONObjectWrapper namedRangeObj = new JSONObjectWrapper();
            NamedExpression namedExp = workbook.getNamedExpression(namedRangeWrapper.getRangeName(), namedRangeWrapper.getScopeASN());

            if (operationType == CommandConstants.OperationType.GENERATE_LIST) {
                addRangeAry = ResponseUtils.getNameRangeDetails(workbook);

            } else {
                if (null != operationType) {
                    switch (operationType) {
                        case ADD: {
                            JSONObjectWrapper exprJson = new JSONObjectWrapper(new ZSJSONizerVisitor(workbook.getFunctionLocale(), workbook).toJson(namedExp).toString());
                            JSONArrayWrapper namedExpressionArray = ResponseUtils.getNamedExpArray(ActionConstants.NAMEDRANGE_ADD, namedExp.getName(), null, exprJson, workbook.getNamedExpressions().indexOf(namedExp), null, namedExp.getScopeASN(), namedExp.getComment());
                            addRangeAry.put(namedExpressionArray);
                            break;
                        }
                        case MODIFY: {
                            JSONObjectWrapper exprJson = new JSONObjectWrapper(new ZSJSONizerVisitor(workbook.getFunctionLocale(), workbook).toJson(namedExp).toString());
                            JSONArrayWrapper namedExpressionArray = ResponseUtils.getNamedExpArray(ActionConstants.NAMEDRANGE_MODIFY, namedExp.getName(), namedRangeWrapper.getOldName(), exprJson, workbook.getNamedExpressions().indexOf(namedExp), null, namedExp.getScopeASN(), namedExp.getComment());
                            modifiedRangeAry.put(namedExpressionArray);
                            break;
                        }
                        case DELETE: {
                            JSONArrayWrapper namedExpressionArray = ResponseUtils.getNamedExpArray(ActionConstants.NAMEDRANGE_DELETE, namedRangeWrapper.getRangeName(), null, null, -1, workbook.getNamedExpressions().size(), namedRangeWrapper.getScopeASN(), null);
                            removedRangeAry.put(namedExpressionArray);
                            break;
                        }
                        case SHIFT:{
                            List<NamedExpression> namedExpressionList = workbook.getNamedExpressions();
                            for(NamedExpression namedExpression : namedExpressionList){
                                JSONObjectWrapper exprJson = new JSONObjectWrapper(new ZSJSONizerVisitor(workbook.getFunctionLocale(), workbook).toJson(namedExpression).toString());
                                JSONArrayWrapper namedExpressionArray = ResponseUtils.getNamedExpArray(ActionConstants.NAMEDRANGE_MODIFY, namedExpression.getName(), null, exprJson, workbook.getNamedExpressions().indexOf(namedExpression), null, namedExpression.getScopeASN(), namedExpression.getComment());
                                modifiedRangeAry.put(namedExpressionArray);
                            }
                            break;
                        }
                    }
//
//                List<RangeWrapper> rangeList = namedRangeWrapper.getRangeWrapper();
//
//                if (rangeList != null && !rangeList.isEmpty()) {
//
//                    List<NamedExpression> namedExpressionList = workbook.getNamedExpressions();
//                    for (NamedExpression namedExpressionItem : namedExpressionList) {
//
//                        if (rangeList.size() > 0) {
//
//                            for (RangeWrapper rangeWrapperItem : rangeList) {
//
//                                if (operationType == OperationType.REMOVE) {
//                                    JSONArrayWrapper namedExpressionArray = ResponseObject.getNamedExpArray(ActionConstants.NAMEDRANGE_DELETE, namedExpressionItem.getName(), null, workbook.getNamedExpressions().indexOf(namedExpressionItem), null);
//
////                                    String[] namedVals = namedExpressionItem.getValues(workbook);
////                                    String formulaVal = namedVals[2];
////                                    String delSheetName = workbook.getActiveSheetName();
//
////                                    if(formulaVal.contains(delSheetName) || formulaVal.contains("#REF!")) { //NO I18N
//                                        removedRangeAry.put(namedExpressionArray);
////                                    }
//                                } else if (operationType == OperationType.MODIFY) {
//
//                                    Range tempRange = NamedExpression.getRange(namedExpressionItem, workbook.getSheet(workbook.getActiveSheetName()), 0, 0);
//                                    DataRange dRange1 = new DataRange(workbook.getActiveSheetName(), tempRange.getStartRowIndex(), tempRange.getStartColIndex(), tempRange.getEndRowIndex(), tempRange.getEndColIndex());
//                                    DataRange dRange2 = new DataRange(rangeList.get(0).getSheetName(), rangeList.get(0).getstartRow(), rangeList.get(0).getstartCol(), rangeList.get(0).getEndRow(), rangeList.get(0).getEndCol());
//                                    DataRange DataRangeIntersection = RangeUtil.intersection(dRange1, dRange2);
//                                    if (DataRangeIntersection != null && DataRangeIntersection.equals(dRange1)) {
//                                        JSONObjectWrapper exprJson = new JSONObjectWrapper(new ZSJSONizerVisitor(workbook.getFunctionLocale(), workbook).toJson(namedExp).toString());
//                                        JSONArrayWrapper namedExpressionArray = ResponseObject.getNamedExpArray(ActionConstants.NAMEDRANGE_MODIFY, namedExpressionItem.getName(), exprJson, workbook.getNamedExpressions().indexOf(namedExpressionItem), null);
//                                        modifiedRangeAry.put(namedExpressionArray);
//
//                                    }
//
//                                }
//
//                            }
//                        }
//                    }
//
                }
            }
            if (operationType == CommandConstants.OperationType.GENERATE_LIST) {
                namedRangeObj.put(Integer.toString(CommandConstants.META), addRangeAry);
            } else if (!modifiedRangeAry.isEmpty()) {
                namedRangeObj.put(Integer.toString(CommandConstants.MODIFY), modifiedRangeAry);
            } else if (!addRangeAry.isEmpty()) {
                namedRangeObj.put(Integer.toString(CommandConstants.ADD), addRangeAry);
            } else if (!removedRangeAry.isEmpty()) {
                namedRangeObj.put(Integer.toString(CommandConstants.DELETE), removedRangeAry);
            }
            if(!namedRangeObj.isEmpty()){
                getDocumentResponse().put(Integer.toString(CommandConstants.NAMED_RANGE), namedRangeObj);
            }
        }
    }

    private void updateDiscussionInfo() {
        com.adventnet.zoho.websheet.model.response.beans.DiscussionBean disBean = getDocumentResponseHolder().getDiscussionBean();
        if (disBean != null) {
            CommandConstants.OperationType opType = disBean.getOperationType();
            JSONObjectWrapper disObj = new JSONObjectWrapper();
            if(opType == CommandConstants.OperationType.GENERATE_LIST && disBean.getDiscussion().has(Integer.toString(CommandConstants.DISCUSSION))) {
                disObj.put(Integer.toString(CommandConstants.META), disBean.getDiscussion().get(Integer.toString(CommandConstants.DISCUSSION)));
            } else if(opType == ADD) {
                disObj.put(Integer.toString(CommandConstants.ADD), disBean.getDiscussion());
            } else if(opType == CommandConstants.OperationType.MODIFY) {
                disObj.put(Integer.toString(CommandConstants.MODIFY), disBean.getDiscussion());
            } else if(opType == CommandConstants.OperationType.DELETE) {
                disObj.put(Integer.toString(CommandConstants.DELETE), disBean.getDiscussion());
            } else if(opType == CommandConstants.OperationType.DELETE_REPLY) {
                disObj.put(Integer.toString(CommandConstants.DELETE_REPLY), disBean.getDiscussion());
            } else if(opType == CommandConstants.OperationType.LIKE) {
                disObj.put(Integer.toString(CommandConstants.DISCUSSION_LIKE), disBean.getDiscussion());
            } else if(opType == CommandConstants.OperationType.UNLIKE) {
                disObj.put(Integer.toString(CommandConstants.DISCUSSION_UNLIKE), disBean.getDiscussion());
            } else if(opType == CommandConstants.OperationType.LIKE_REPLY) {
                disObj.put(Integer.toString(CommandConstants.DISCUSSION_LIKE_REPLY), disBean.getDiscussion());
            } else if(opType == CommandConstants.OperationType.UNLIKE_REPLY) {
                disObj.put(Integer.toString(CommandConstants.DISCUSSION_UNLIKE_REPLY), disBean.getDiscussion());
            } else if(opType == CommandConstants.OperationType.PASTE && disBean.getDiscussion().has("rangeUpdatelist")) {
                JSONArrayWrapper rangeUpdatelist = new JSONArrayWrapper();
                rangeUpdatelist = disBean.getDiscussion().getJSONArray("rangeUpdatelist"); //NO I18N
                JSONArrayWrapper sheetList = disBean.getSheet();
                String asn 	= sheetList.length() > 0 ? sheetList.getString(0) : null;
                Sheet sheet = getWorkbook().getSheetByAssociatedName(asn);
                JSONArrayWrapper discussionList = new JSONArrayWrapper();
                Iterator<Object> it = rangeUpdatelist.iterator();
                while(it.hasNext()){
                    String uid = (String) it.next();
                    DataRange CommentRange =  sheet.getDiscussionRangeMap().get(uid);
                    String commentId = uid.replace(EngineConstants.DISCUSSIONRANGE_NAME +"_", "");
                    JSONObjectWrapper discussionObj = new JSONObjectWrapper();
                    discussionObj.put(Integer.toString(CommandConstants.ID), commentId);
                    JSONArrayWrapper rngList = new JSONArrayWrapper();
                    JSONArrayWrapper rangeArray = new JSONArrayWrapper();
                    rngList.put(asn);
                    rngList.put(CommentRange.getStartRowIndex());
                    rngList.put(CommentRange.getStartColIndex());
                    rngList.put(CommentRange.getEndRowIndex());
                    rngList.put(CommentRange.getEndColIndex());
                    rangeArray.put(rngList);
                    discussionObj.put(Integer.toString(CommandConstants.RANGES), rangeArray);
                    discussionList.put(discussionObj);
                }
                disObj.put(Integer.toString(CommandConstants.CUT_PASTE), discussionList);
            }
            getDocumentResponse().put(Integer.toString(CommandConstants.DISCUSSION), disObj);
        }
    }
    private void updateDelugeFunctionsInfo() {
        List<com.adventnet.zoho.websheet.model.response.beans.DelugeFunctionsBean> delugeFunctionsBeans = getDocumentResponseHolder().getDelugeFunctionsList();

        if(delugeFunctionsBeans != null && !delugeFunctionsBeans.isEmpty()){
            for (Iterator iterator = delugeFunctionsBeans.iterator(); iterator.hasNext(); ) {
                com.adventnet.zoho.websheet.model.response.beans.DelugeFunctionsBean delugeFunctionsBean = (com.adventnet.zoho.websheet.model.response.beans.DelugeFunctionsBean) iterator.next();
                if(delugeFunctionsBean != null){
                    Workbook workbook = getWorkbook();
                    JSONObjectWrapper delugeResponse = new JSONObjectWrapper();
                    CommandConstants.OperationType operationType = delugeFunctionsBean.getOperationType();
                    if(operationType == CommandConstants.OperationType.GENERATE_LIST){

                        JSONArrayWrapper delugeMeta = DelugeUtils.getAllFunctionDetailsForResponse(workbook);

                        if(!delugeMeta.isEmpty()){

                            delugeResponse.put(Integer.toString(CommandConstants.META), delugeMeta);
                        }

                    } else if (operationType == CommandConstants.OperationType.INSERT ) {

                        String functionName = delugeFunctionsBean.getName();
                        JSONObjectWrapper functionMeta = DelugeUtils.getFunctionDetailsForResponse(workbook, functionName);
                        if(!functionMeta.isEmpty()){
                            delugeResponse.put(Integer.toString(CommandConstants.INSERT), functionMeta);
                        }

                    } else if (operationType == CommandConstants.OperationType.DELETE) {

                        String functionName = delugeFunctionsBean.getName();
                        if(functionName != null){
                            JSONArrayWrapper functionMeta = new JSONArrayWrapper();
                            functionMeta.put(functionName);
                            delugeResponse.put(Integer.toString(CommandConstants.DELETE), functionMeta);
                        }

                    } else if (operationType == CommandConstants.OperationType.EDIT){

                        String functionName = delugeFunctionsBean.getName();
                        JSONObjectWrapper functionMeta =  DelugeUtils.getFunctionDetailsForResponse(workbook, functionName);
                        if(!functionMeta.isEmpty()){
                            delugeResponse.put(Integer.toString(CommandConstants.MODIFY), functionMeta);
                        }

                    }
                    if(!delugeResponse.isEmpty()){
                        getDocumentResponse().put(Integer.toString(CommandConstants.DELUGE_FUNCTIONS), delugeResponse);
                    }


                }
            }
        }
    }

    private void updatePicklistInfo() {
        com.adventnet.zoho.websheet.model.response.beans.PicklistBean bean = getDocumentResponseHolder().getPicklistBean();
        if(bean == null) {
            return;
        }
        CommandConstants.OperationType operationType = bean.getOperationType();
        JSONObjectWrapper picklistResponse = new JSONObjectWrapper();
        JSONObjectWrapper editResponse = null;
        Workbook workbook = getWorkbook();
        JSONObjectWrapper documentResponseJson = getDocumentResponse();

        switch(operationType)  {
            case GENERATE_LIST:
                JSONArrayWrapper picklistArray = new JSONArrayWrapper();

                for(Picklist picklist : workbook.getPicklistMap().values()) {
                    picklistArray.put(ResponseUtils.getPicklistJson(picklist, workbook));
                }

                if(!picklistArray.isEmpty()) {
                    picklistResponse.put(Integer.toString(CommandConstants.META), picklistArray);
                }
                break;
            case ADD: {
                int picklistID = bean.getPicklistID();
                Picklist picklist = workbook.getPicklist(picklistID);
                if(picklist == null) {
                    return;
                }
                picklistResponse.put(Integer.toString(CommandConstants.ADD),ResponseUtils.getPicklistJson(picklist, workbook));
                break;
            }
            case REMOVE: {
                int picklistID = bean.getPicklistID();
                JSONArrayWrapper removeArray = new JSONArrayWrapper();
                removeArray.put(picklistID);
                picklistResponse.put(Integer.toString(CommandConstants.REMOVE),removeArray);
                break;
            }
            case REORDER: {
                int picklistID = bean.getPicklistID();
                JSONArrayWrapper orderArray = bean.getOrderArray();
                JSONObjectWrapper json = new JSONObjectWrapper();
                json.put(Integer.toString(CommandConstants.PICKLIST_ID),picklistID);
                json.put(Integer.toString(CommandConstants.PL_LIST),orderArray);

                picklistResponse.put(Integer.toString(CommandConstants.REORDER),json);
                break;
            }
            case EDIT: {
                int picklistID = bean.getPicklistID();
                Picklist picklist = workbook.getPicklist(picklistID);
                editResponse = ResponseUtils.getPicklistJson(picklist, workbook);
            }
            case UPDATE:
                Set<Picklist> modifiedPicklists = bean.getModifiedPicklists();
                picklistResponse = documentResponseJson.has(Integer.toString(CommandConstants.PICKLIST)) ? documentResponseJson.getJSONObject(Integer.toString(CommandConstants.PICKLIST)) : new JSONObjectWrapper();
                JSONArrayWrapper updateArray = picklistResponse.has(Integer.toString(CommandConstants.UPDATE)) ? picklistResponse.getJSONArray(Integer.toString(CommandConstants.UPDATE)) : new JSONArrayWrapper();

                if(modifiedPicklists != null) {
                    for(Picklist picklist : modifiedPicklists) {
                        updateArray.put(ResponseUtils.getPicklistJson(picklist, workbook));
                    }
                }

                if(editResponse != null) {
                    picklistResponse.put(Integer.toString(CommandConstants.EDIT), editResponse);
                }
                if(!updateArray.isEmpty()) {
                    picklistResponse.put(Integer.toString(CommandConstants.UPDATE), updateArray);
                }
                break;

            case MACRO: {
                Map<String, List<RangeWrapper>> modifiedRanges = bean.getRanges();
                if(modifiedRanges != null && !modifiedRanges.isEmpty()) {
                    modifiedPicklists = new HashSet<>();

                    for(Map.Entry<Integer,Picklist> picklistEntry : workbook.getPicklistMap().entrySet()) {
                        Picklist picklist = picklistEntry.getValue();
                        List<DataRange> sourceRanges = picklist.getSourceRanges();

                        if(sourceRanges != null) {
                            outer: for(Map.Entry<String, List<RangeWrapper>> entry: modifiedRanges.entrySet()) {
                                List<RangeWrapper> ranges = entry.getValue();

                                for(RangeWrapper rangeWrapper: ranges) {
                                    DataRange dataRange = new DataRange(rangeWrapper.getSheetName(), rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());

                                    if(RangeUtil.hasIntersection(dataRange, sourceRanges)) {
                                        modifiedPicklists.add(picklist);
                                        break outer;
                                    }
                                }
                            }
                        }
                    }

                    if(!modifiedPicklists.isEmpty()) {
                        picklistResponse = documentResponseJson.has(Integer.toString(CommandConstants.PICKLIST)) ? documentResponseJson.getJSONObject(Integer.toString(CommandConstants.PICKLIST)) : new JSONObjectWrapper();
                        updateArray = picklistResponse.has(Integer.toString(CommandConstants.UPDATE)) ? picklistResponse.getJSONArray(Integer.toString(CommandConstants.UPDATE)) : new JSONArrayWrapper();

                        for(Picklist picklist : modifiedPicklists) {
                            updateArray.put(ResponseUtils.getPicklistJson(picklist, workbook));
                        }

                        if(!updateArray.isEmpty()) {
                            picklistResponse.put(Integer.toString(CommandConstants.UPDATE), updateArray);
                        }
                    }
                }
                break;
            }
        }

        if(!picklistResponse.isEmpty()) {
            documentResponseJson.put(Integer.toString(CommandConstants.PICKLIST),picklistResponse);
        }
    }


    private void updateTableInfo() {
        List<com.adventnet.zoho.websheet.model.response.beans.TableBean> beanList = getDocumentResponseHolder().getTableBeanList();
        if(beanList == null) {
            return;
        }

        Set<Integer> modifiedTables = new HashSet<>();
        Set<Integer> headersChanged = new HashSet<>();
        Set<String> tableStyleNames = new HashSet<>();
        TableStyle defaultStyleToAdd = null;
        Workbook workbook = getWorkbook();
        JSONObjectWrapper tableResponse = new JSONObjectWrapper();
        for(com.adventnet.zoho.websheet.model.response.beans.TableBean bean : beanList) {
            headersChanged.addAll(bean.getHeaderChanged());
            modifiedTables.addAll(bean.getExpandedTables());
            Map<String, Set<Integer>> sheetFilterTableIdMap = new HashMap<>();
            for(Integer id : bean.getExpandedTables())
            {
                Table table = workbook.getTableById(id);
                if(table != null && table.isFilterTable()) {
                    sheetFilterTableIdMap.computeIfAbsent(table.getSheet().getAssociatedName(), k -> new HashSet<>()).add(table.getID());
                }
            }
            if(!sheetFilterTableIdMap.isEmpty())
            {
                bean.setSheetFilterTableIdMap(sheetFilterTableIdMap);
            }

            CommandConstants.OperationType operation = bean.getOperation();
            if(operation == null) {
                continue;
            }



            switch(operation) {
                case GENERATE_LIST:
                    JSONArrayWrapper tableListJson = new JSONArrayWrapper();
                    sheetFilterTableIdMap = new HashMap<>();
                    for(Sheet sheet : workbook.getSheetList()) {
                        Map<String, Table> tablesMap = sheet.getTables();
                        for (Map.Entry<String, Table> entry : tablesMap.entrySet()) {
                            Table table = entry.getValue();
                            try {
                                if(!table.isFilterTable()) {
                                    tableListJson.put(ResponseUtils.getTableJson(workbook, table));
                                }
                                else {
                                    sheetFilterTableIdMap.computeIfAbsent(sheet.getAssociatedName(), k -> new HashSet<>()).add(table.getID());
                                }
                            }
                            catch (Exception e) {
                                LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Error while getting table json, error: ", e);
                            }
                        }
                    }
                    if(!sheetFilterTableIdMap.isEmpty()) {
                        bean.setSheetFilterTableIdMap(sheetFilterTableIdMap);
                    }

                    for(TableStyle tableStyle: workbook.getTableStyles().values()) {
                        tableStyleNames.add(tableStyle.getName());
                    }

                    tableResponse.put(Integer.toString(CommandConstants.META),tableListJson);
                    TableStyle defaultStyle = workbook.getDefaultTableStyle();
                    if(defaultStyle != null) {
                        if(!workbook.containsTableStyle(defaultStyle.getName())) {
                            defaultStyleToAdd = defaultStyle;
                        }
                        tableResponse.put(Integer.toString(CommandConstants.DEFAULT), defaultStyle.getName());
                    }
                    break;


                case MODIFY:
                case ADD:
                case HIDE:
                    int comConst = operation == ADD ? CommandConstants.ADD: CommandConstants.MODIFY;
                    JSONArrayWrapper array = tableResponse.has(Integer.toString(comConst)) ? tableResponse.getJSONArray(Integer.toString(comConst)) : new JSONArrayWrapper();
                    sheetFilterTableIdMap = new HashMap<>();
                    for(com.google.common.collect.Table.Cell<Integer,String,List<RangeUtil.SheetRange>> entry: bean.getModifiedRangesMap().cellSet()) {
                        int tableID = entry.getRowKey();
                        String asn = entry.getColumnKey();
                        Sheet sheet = workbook.getSheetByAssociatedName(asn);
                        List<Integer> tableIds = new ArrayList<>();
                        if(tableID >= 0) {
                            tableIds.add(tableID);
                        }
                        else {
                            for(Table tbl: sheet.getTables().values()) {
                                DataRange tableRange = tbl.getAsDataRange();
                                for (RangeUtil.SheetRange range : entry.getValue()) {
                                    DataRange intersection = RangeUtil.intersection(tableRange, range.toDataRange(asn));
                                    if(intersection != null)
                                    {
                                        if(tableRange.equals(intersection))
                                        {
                                            tableIds.add(tbl.getID());
                                        }
                                        else if(tbl.isFilterTable() && intersection.getStartRowIndex() <= tbl.getStartRowIndex() && intersection.getEndRowIndex() >= tbl.getStartRowIndex()
                                                && intersection.getStartColIndex() == tbl.getStartColIndex() && intersection.getEndColIndex() == tbl.getEndColIndex())
                                        {
                                            tableIds.add(tbl.getID());
                                        }
                                        break;
                                    }
                                }
                            }
                        }


                        for(int id: tableIds) {
                            Table table = sheet.getTableById(id);

                            if(table != null) {
                                try {
                                    if(!table.isFilterTable()) {
                                        array.put(ResponseUtils.getTableJson(workbook, table));
                                    }
                                    else {
                                        sheetFilterTableIdMap.computeIfAbsent(asn, k -> new HashSet<>()).add(table.getID());
                                    }
                                } catch (Exception e) {
                                    LOGGER.log(Level.WARNING, "Error while getting table json for action {0}", operation.name());
                                }
                                tableStyleNames.add(table.getTableStyleName());
                            }
                        }
                    }

                    for(Integer id: bean.getModifiedTables()) {
                        Table table = workbook.getTableById(id);
                        if(table != null) {
                            try {
                                if(!table.isFilterTable()) {
                                    array.put(ResponseUtils.getTableJson(workbook, table));
                                }
                                else {
                                    sheetFilterTableIdMap.computeIfAbsent(table.getSheet().getAssociatedName(), k -> new HashSet<>()).add(table.getID());
                                }
                            } catch (Exception e) {
                                LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Error while getting table json for action {0}", operation.name());
                            }
                            tableStyleNames.add(table.getTableStyleName());
                        }
                    }

                    if(!sheetFilterTableIdMap.isEmpty())
                    {
                        bean.setSheetFilterTableIdMap(sheetFilterTableIdMap);
                    }

                    if(!array.isEmpty()) {
                        tableResponse.put(Integer.toString(comConst),array);
                    }
                    break;

                case REMOVE:
                    array = tableResponse.has(Integer.toString(CommandConstants.REMOVE)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.REMOVE)) : new JSONArrayWrapper();
                    for(com.google.common.collect.Table.Cell<Integer,String,List<RangeUtil.SheetRange>> entry: bean.getModifiedRangesMap().cellSet()) {
                        JSONObjectWrapper json = new JSONObjectWrapper();
                        json.put(Integer.toString(CommandConstants.ID), entry.getRowKey());

                        array.put(json);
                    }

                    if(!array.isEmpty()) {
                        tableResponse.put(Integer.toString(CommandConstants.REMOVE), array);
                    }
                    break;

                case IMPORT_SHEET_INSERT:
                case ADD_SHEET:
                    List<String> sheetsAsn = bean.getSheetsForMeta();
                    if(sheetsAsn != null) {
                        sheetFilterTableIdMap = new HashMap<>();
                        array = tableResponse.has(Integer.toString(CommandConstants.ADD)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                        for(String sheetName: sheetsAsn) {
                            Sheet sheet =  workbook.getSheet(sheetName);
                            if(sheet == null) {
                                continue;
                            }
                            for(Table table: sheet.getTables().values()) {
                                try {
                                    if(!table.isFilterTable()) {
                                        array.put(ResponseUtils.getTableJson(workbook, table));
                                    }
                                    else {
                                        sheetFilterTableIdMap.computeIfAbsent(sheet.getAssociatedName(), k -> new HashSet<>()).add(table.getID());
                                    }
                                }
                                catch(Exception e){
                                    LOGGER.log(Level.WARNING, "Error while getting table json for action {0}", operation.name());
                                }
                                tableStyleNames.add(table.getTableStyleName());
                            }
                        }

                        if(!sheetFilterTableIdMap.isEmpty())
                        {
                            bean.setSheetFilterTableIdMap(sheetFilterTableIdMap);
                        }

                        if(!array.isEmpty()) {
                            tableResponse.put(Integer.toString(CommandConstants.ADD),array);
                        }
                    }
                    break;

                case PASTE_WITH_STYLE:
                case SHIFT:
                    array = tableResponse.has(Integer.toString(CommandConstants.MODIFY)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.MODIFY)) : new JSONArrayWrapper();
                    JSONArrayWrapper removeArray = tableResponse.has(Integer.toString(CommandConstants.DELETE)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.DELETE)) : new JSONArrayWrapper();
                    sheetFilterTableIdMap = new HashMap<>();
                    for(com.google.common.collect.Table.Cell<Integer,String,List<RangeUtil.SheetRange>> entry: bean.getModifiedRangesMap().cellSet()) {
                        Set<Integer> idSet = new HashSet<>();
                        String asn = entry.getColumnKey();
                        for(RangeUtil.SheetRange range : bean.getModifiedRanges(0,asn)) {
                            Collection<Table> tableList = workbook.getSheetByAssociatedName(asn).getTables().values();
                            for(Table table: tableList) {
                                DataRange tableRange = table.getAsDataRange();
                                if(RangeUtil.intersection(tableRange, new DataRange(asn, range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex())) != null) {
                                    idSet.add(table.getID());
                                    if(operation == PASTE_WITH_STYLE) {
                                        tableStyleNames.add(table.getTableStyleName());
                                    }
                                }
                            }
                            removeArray.put(new JSONObjectWrapper().put(Integer.toString(CommandConstants.ASSOCIATED_SHEET_NAME), asn).put(Integer.toString(CommandConstants.START_ROW), range.getStartRowIndex())
                                    .put(Integer.toString(CommandConstants.START_COL),range.getStartColIndex()).put(Integer.toString(CommandConstants.END_ROW), range.getEndRowIndex()).put(Integer.toString(CommandConstants.END_COL),range.getEndColIndex()));
                        }
                        for(Integer id: idSet) {
                            Sheet sheet =  workbook.getSheetByAssociatedName(asn);
                            try {
                                Table table = sheet.getTableById(id);
                                if(!table.isFilterTable()) {
                                    array.put(ResponseUtils.getTableJson(workbook, table));
                                }
                                else {
                                    sheetFilterTableIdMap.computeIfAbsent(asn, k -> new HashSet<>()).add(table.getID());
                                }
                            }
                            catch (Exception e) {
                                LOGGER.log(Level.WARNING, "Error while getting table json for action {0}", operation.name());
                            }
                        }
                    }

                    if(!sheetFilterTableIdMap.isEmpty())
                    {
                        bean.setSheetFilterTableIdMap(sheetFilterTableIdMap);
                    }
                    if(!array.isEmpty()) {
                        tableResponse.put(Integer.toString(CommandConstants.MODIFY),array);
                    }
                    if(!removeArray.isEmpty()) {
                        tableResponse.put(Integer.toString(CommandConstants.DELETE), removeArray);
                    }
                    break;

                case PASTE:
                    boolean needsMeta = false;
                    outer: for(com.google.common.collect.Table.Cell<Integer,String,List<RangeUtil.SheetRange>> entry: bean.getModifiedRangesMap().cellSet()) {
                        String asn = entry.getColumnKey();
                        Sheet sheet = workbook.getSheetByAssociatedName(asn);
                        if(sheet == null) {
                            continue;
                        }
                        for(RangeUtil.SheetRange range : bean.getModifiedRanges(0,asn)) {
                            Collection<Table> tableList = sheet.getTables().values();
                            for(Table table: tableList) {
                                DataRange tableRange = table.getTableDataRange();
                                if(RangeUtil.intersection(tableRange, new DataRange(asn, range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex())) != null) {
                                    needsMeta = true;
                                    break outer;
                                }
                            }
                        }
                    }

                    if(needsMeta) {
                        tableListJson = new JSONArrayWrapper();
                        sheetFilterTableIdMap = new HashMap<>();
                        for(Sheet sheet : workbook.getSheetList()) {
                            Map<String, Table> tablesMap = sheet.getTables();
                            for (Map.Entry<String, Table> entry : tablesMap.entrySet()) {
                                Table table = entry.getValue();
                                try {
                                    if(!table.isFilterTable()) {
                                        tableListJson.put(ResponseUtils.getTableJson(workbook, table));
                                    }
                                    else {
                                        sheetFilterTableIdMap.computeIfAbsent(sheet.getAssociatedName(), k -> new HashSet<>()).add(table.getID());
                                    }
                                }
                                catch (Exception e) {
                                    LOGGER.log(Level.WARNING, "Error while getting table json for action {0}", operation.name());
                                }
                            }
                        }
                        for(TableStyle tableStyle: workbook.getTableStyles().values()) {
                            tableStyleNames.add(tableStyle.getName());
                        }
                        if(!sheetFilterTableIdMap.isEmpty())
                        {
                            bean.setSheetFilterTableIdMap(sheetFilterTableIdMap);
                        }

                        tableResponse.put(Integer.toString(CommandConstants.META),tableListJson);
                    }
                    break;

                case DELETE:
                    removeArray = tableResponse.has(Integer.toString(CommandConstants.DELETE)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.DELETE)) : new JSONArrayWrapper();
                    for(com.google.common.collect.Table.Cell<Integer,String,List<RangeUtil.SheetRange>> entry: bean.getModifiedRangesMap().cellSet()) {
                        List<RangeUtil.SheetRange> ranges = entry.getValue();
                        for(RangeUtil.SheetRange range: ranges) {
                            removeArray.put(new JSONObjectWrapper().put(Integer.toString(CommandConstants.ASSOCIATED_SHEET_NAME), entry.getColumnKey()).put(Integer.toString(CommandConstants.START_ROW), range.getStartRowIndex())
                                    .put(Integer.toString(CommandConstants.START_COL),range.getStartColIndex()).put(Integer.toString(CommandConstants.END_ROW), range.getEndRowIndex()).put(Integer.toString(CommandConstants.END_COL),range.getEndColIndex()));
                        }
                    }

                    if(!removeArray.isEmpty()) {
                        tableResponse.put(Integer.toString(CommandConstants.DELETE), removeArray);
                    }
                    break;

                case INSERT:
                    sheetFilterTableIdMap = new HashMap<>();
                    for(com.google.common.collect.Table.Cell<Integer,String,List<RangeUtil.SheetRange>> entry: bean.getModifiedRangesMap().cellSet()) {
                        String asn = entry.getColumnKey();
                        Sheet sheet = workbook.getSheetByAssociatedName(asn);

                        for(Table table: sheet.getTables().values()) {
                            DataRange tableRange = table.getAsDataRange();
                            for (RangeUtil.SheetRange range : entry.getValue()) {
                                if(RangeUtil.intersection(RangeUtil.SheetRange.fromDataRange(tableRange), range) != null) {
                                    if(!table.isFilterTable()) {
                                        modifiedTables.add(table.getID());
                                    }
                                    else {
                                        sheetFilterTableIdMap.computeIfAbsent(asn, k -> new HashSet<>()).add(table.getID());
                                    }
                                    break;
                                }
                            }
                        }
                    }
                    if(!sheetFilterTableIdMap.isEmpty())
                    {
                        bean.setSheetFilterTableIdMap(sheetFilterTableIdMap);
                    }
                    break;

                case REMOVE_STYLE:
                    removeArray = tableResponse.has(Integer.toString(CommandConstants.REMOVE_STYLE)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.REMOVE_STYLE)) : new JSONArrayWrapper();
                    for(String name: bean.getRemovedStyles()) {
                        removeArray.put(name);
                    }

                    if(!removeArray.isEmpty()) {
                        tableResponse.put(Integer.toString(CommandConstants.REMOVE_STYLE), removeArray);
                    }
                    break;

                case CHANGE_DEFAULT:
                    defaultStyle = workbook.getDefaultTableStyle();
                    if(defaultStyle != null) {
                        tableResponse.put(Integer.toString(CommandConstants.DEFAULT), defaultStyle.getName());
                        if(workbook.containsTableStyle(defaultStyle.getName())) {
                            tableStyleNames.add(defaultStyle.getName());
                        }
                    }
                    break;

                case REPLACE:
                    for(com.google.common.collect.Table.Cell<Integer,String,List<RangeUtil.SheetRange>> entry: bean.getModifiedRangesMap().cellSet()) {
                        String asn = entry.getColumnKey();
                        Sheet sheet = workbook.getSheetByAssociatedName(asn);

                        outer: for(Table table: sheet.getTables().values()) {
                            if(table.isHeaderRowShown()) {
                                DataRange tableRange = table.getAsDataRange();
                                for (RangeUtil.SheetRange range : entry.getValue()) {
                                    if(range.getStartRowIndex() <= tableRange.getStartRowIndex() && range.getEndRowIndex() >= tableRange.getStartRowIndex()
                                            && range.getStartColIndex() <= tableRange.getEndColIndex() && range.getEndColIndex() >= tableRange.getStartColIndex()) {
                                        headersChanged.add(table.getID());
                                        break outer;
                                    }
                                }
                            }
                        }
                    }
                    break;

                case REPLACE_SPREADSHEET:
                    for(Sheet sheet : workbook.getSheetList()) {
                        for(Table table: sheet.getTables().values()) {
                            headersChanged.add(table.getID());
                        }
                    }

                    break;

                case HEADER_CHANGE:
                    for(com.google.common.collect.Table.Cell<Integer,String,List<RangeUtil.SheetRange>> entry: bean.getModifiedRangesMap().cellSet()) {
                        String asn = entry.getColumnKey();
                        Sheet sheet = workbook.getSheetByAssociatedName(asn);

                        for(Table table: sheet.getTables().values()) {
                            if(table.isFilterTable()) {
                                continue;
                            }
                            DataRange tableRange = table.getAsDataRange();
                            for (RangeUtil.SheetRange range : entry.getValue()) {
                                RangeUtil.SheetRange intersection = RangeUtil.intersection(RangeUtil.SheetRange.fromDataRange(tableRange), range);
                                if(intersection != null) {
                                    headersChanged.add(table.getID());
                                    break;
                                }
                            }
                        }
                    }
                    break;
            }


        }

        JSONArrayWrapper headerChangeResponse = tableResponse.has(Integer.toString(CommandConstants.COLUMN_HEADERS)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.COLUMN_HEADERS)) : new JSONArrayWrapper();
        for (int tableID : headersChanged) {
            for (Sheet sheet : workbook.getSheetList()) {
                Table table = sheet.getTableById(tableID);
                try {
                    if (table != null && !table.isFilterTable()) {
                        JSONObjectWrapper tableJson = new JSONObjectWrapper();
                        tableJson.put(Integer.toString(CommandConstants.ID), tableID);
                        JSONArrayWrapper colJson = new JSONArrayWrapper();
                        for (TableColumn column : table.getTableColumns()) {
                            colJson.put(Utility.getEncodedString(column.getColumnHeader()).replace("+", "%20"));
                        }
                        tableJson.put(Integer.toString(CommandConstants.COLUMN_HEADERS), colJson);
                        headerChangeResponse.put(tableJson);
                    }
                }
                catch (Exception e) {}
            }
        }
        if (!headerChangeResponse.isEmpty() && !tableResponse.has(Integer.toString(CommandConstants.META))) {
            tableResponse.put(Integer.toString(CommandConstants.COLUMN_HEADERS), headerChangeResponse);
        }

        JSONArrayWrapper expansionResponse = tableResponse.has(Integer.toString(CommandConstants.TABLE_EXPANSION)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.TABLE_EXPANSION)) : new JSONArrayWrapper();
        for (int tableID : modifiedTables) {
            JSONObjectWrapper jobj = new JSONObjectWrapper();
            Table table = workbook.getTableById(tableID);
            // When one table is pasted on other, the initial table won't exist in sheet, but the id will be added in preActionNotifyEdit, so we need to add nullcheck here.
            if(table == null || table.isFilterTable()) {
                continue;
            }
            jobj.put(Integer.toString(CommandConstants.ID), tableID);
            jobj.put(Integer.toString(CommandConstants.START_ROW), table.getStartRowIndex()).put(Integer.toString(CommandConstants.START_COL), table.getStartColIndex());
            jobj.put(Integer.toString(CommandConstants.END_ROW), table.getEndRowIndex()).put(Integer.toString(CommandConstants.END_COL), table.getEndColIndex());
            expansionResponse.put(jobj);
        }
        if (!expansionResponse.isEmpty() && !tableResponse.has(Integer.toString(CommandConstants.META))) {
            tableResponse.put(Integer.toString(CommandConstants.TABLE_EXPANSION), expansionResponse);
        }

        if(!tableStyleNames.isEmpty() || defaultStyleToAdd != null) {
            JSONArrayWrapper styles = new JSONArrayWrapper();
            for(String styleName: tableStyleNames) {
                TableStyle tableStyle = workbook.getTableStyle(styleName);
                JSONObjectWrapper style = ResponseUtils.getTableStyleJson(workbook, tableStyle);
                styles.put(style);
            }

            if(defaultStyleToAdd != null) {
                JSONObjectWrapper style = ResponseUtils.getTableStyleJson(workbook, defaultStyleToAdd);
                styles.put(style);
            }

            tableResponse.put(Integer.toString(CommandConstants.CELL_STYLES), styles);
        }

        if(!tableResponse.isEmpty()) {
            getDocumentResponse().put(Integer.toString(CommandConstants.TABLE), tableResponse);
        }
    }

    private void updateOnLoadDetails()
    {
        if(getDocumentResponseHolder().isOnLoadRequest())
        {
            JSONObjectWrapper documentResponse = getDocumentResponse();
            WorkbookContainer container = getContainer();
            documentResponse.put(Integer.toString(CommandConstants.PIVOT_STYLE_DEF), PivotTemplateContainer.getStyleDefJSON());
            documentResponse.put(Integer.toString(CommandConstants.SPREADSHEET_SETTINGS_NEW), container.getWorkbookAdditionalInfo().getJSON(getWorkbook()));
            JSONObjectWrapper documentMeta = documentResponse.getJSONObject(Integer.toString(CommandConstants.META));
            documentMeta.put(Integer.toString(CommandConstants.UNIQUE_TAB_ID), ClientUtils.generateUniqueTabID());
            documentMeta.put(Integer.toString(CommandConstants.IS_TEAM_RESOURCE), container.isRemoteMode() ? false : ClientUtils.isTeamResource(container.getResourceKey()));
            documentMeta.put(Integer.toString(CommandConstants.DOCUMENT_STATUS), (!container.isRemoteMode() && container.getResourceKey()!=null)? ClientUtils.getDocumentStatus(container.getResourceKey()) : -1);
            documentMeta.put(Integer.toString(CommandConstants.ALLOW_COPY), container.isRemoteMode() ? true : AuthorizationUtil.canCopy(String.valueOf(DocumentUtils.getZUID()), container.getResourceKey()));
            documentMeta.put(Integer.toString(CommandConstants.ALLOW_RENAME), container.isRemoteMode() ? true : AuthorizationUtil.canRename(String.valueOf(DocumentUtils.getZUID()), container.getResourceKey()));
            documentResponse.put(Integer.toString(CommandConstants.META), documentMeta);
            try
            {
                List<String> customColors = container.getCustomColors();
                documentResponse.put(Integer.toString(CommandConstants.CUSTOM_COLORS), new JSONArrayWrapper(customColors));
            }
            catch(Exception e)
            {
                LOGGER.log(Level.SEVERE,"[CUSTOM_COLORS][Exception] Exception while retrieving custom colors", e);
            }
        }
    }


    /** --------------------------------------ConstraintJsonResponseCreation --------------------------------------------- */

    private void updateCellsResponse() {

        CellResponse cellResponse = getConstraintResponseHolder().getCellsReponse();
        int cellMetaheader = getConstraintResponseHolder().getCellMetaHeader();

        if (cellResponse != null) {

            HashMap<String, Object> cellResponseData = (HashMap<String, Object>) cellResponse.getCellResponse();
            HashMap<String, HashSet<Long>> faultyCells = (HashMap<String, HashSet<Long>>) cellResponse.getFaultyCells();
            HashMap<String, JSONArrayWrapper> faultyRanges = (HashMap<String, JSONArrayWrapper>) cellResponse.getFaultyRanges();

            if (cellResponseData != null && !cellResponseData.isEmpty()) {
                updateCellResposneData(cellResponseData);
                updateCellMetaHeader(cellMetaheader);
            }

            if (faultyCells != null && !faultyCells.isEmpty()) {
                updateFaultyCells(faultyCells);
            }

            if (faultyRanges != null && !faultyRanges.isEmpty()) {
                updateFaultyRanges(faultyRanges);
            }
        }
    }

    private void updateDataValidationRanges() {

        List<RangeWrapper> dataValidationRanges = getConstraintResponseHolder().getDataValidationRanges();
        Map<String, Object> validationCache = new HashMap<>();
        if (dataValidationRanges != null && !dataValidationRanges.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = dataValidationRanges.iterator(); iterator.hasNext();) {
                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();
                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();
                JSONObjectWrapper dataValidationResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.DATAVALIDATION)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.DATAVALIDATION))
                        : new JSONObjectWrapper();

                if (rangeWrapper.getOperationType() == CommandConstants.OperationType.GENERATE_LIST) {

                    JSONArrayWrapper dataValidationJson = ResponseUtils.getDataValidationRangeList(sheet, sheet.getContentValidationRanges(), validationCache);
                    dataValidationResp.put(Integer.toString(CommandConstants.META), dataValidationJson);

                }
                else if(rangeWrapper.getOperationType() == ADD)
                {
                    List<DataRange> ranges = new ArrayList<>();
                    ranges.add(new DataRange(rangeWrapper.getSheetName(), rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol()));
                    JSONArrayWrapper dataValidationJson = ResponseUtils.getDataValidationRangeList(sheet, sheet.getContentValidationRanges(), validationCache);
                    dataValidationResp.put(Integer.toString(CommandConstants.META), dataValidationJson);
                }
                else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.REMOVE) {

                    JSONArrayWrapper dvArray = new JSONArrayWrapper();

                    dvArray.put(rangeWrapper.getstartRow());
                    dvArray.put(rangeWrapper.getstartCol());
                    dvArray.put(rangeWrapper.getEndRow());
                    dvArray.put(rangeWrapper.getEndCol());

                    dataValidationResp.put(Integer.toString(CommandConstants.REMOVE), dvArray);
                }

                if (!dataValidationResp.isEmpty()) {
                    sheetObjResponse.put(Integer.toString(CommandConstants.DATAVALIDATION), dataValidationResp);
                }

                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);

            }
        }
    }

    private void updateConditionalFormatRanges() {

        List<RangeWrapper> conditionalFormatRanges = getConstraintResponseHolder().getCondtionalFormatRanges();

        if (conditionalFormatRanges != null && !conditionalFormatRanges.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = conditionalFormatRanges.iterator(); iterator.hasNext();) {

                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();

                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());

                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();

                JSONObjectWrapper conditionalFormatResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.CONDITIONALFORMAT)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CONDITIONALFORMAT))
                        : new JSONObjectWrapper();

                if (rangeWrapper.getOperationType() == CommandConstants.OperationType.GENERATE_LIST) {

                    JSONArrayWrapper conditionalFormatJson = ResponseUtils.getConditionalFormatRangeList(sheet);
//                    if (!conditionalFormatJson.isEmpty()) {
                    conditionalFormatResp.put(Integer.toString(CommandConstants.META), conditionalFormatJson);
//                    }

                }

//                if (!conditionalFormatResp.isEmpty()) {
                sheetObjResponse.put(Integer.toString(CommandConstants.CONDITIONALFORMAT), conditionalFormatResp);
//                }

                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);
            }
        }
    }

    private void updateArrayFormulaRanges() {

        List<RangeWrapper> arrayFormulasRanges = getConstraintResponseHolder().getArrayFormulasRanges();

        if (arrayFormulasRanges != null && !arrayFormulasRanges.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = arrayFormulasRanges.iterator(); iterator.hasNext();) {

                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();

                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());

                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();

                JSONObjectWrapper arrayFormulaResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.ARRAY_FORMULAS)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.ARRAY_FORMULAS))
                        : new JSONObjectWrapper();

                if (rangeWrapper.getOperationType() == CommandConstants.OperationType.GENERATE_LIST) {
                    JSONArrayWrapper arrayFormualsJson = ResponseUtils.getArrayFormulasRangeList(sheet);
                    arrayFormulaResp.put(Integer.toString(CommandConstants.META), arrayFormualsJson);
                }

                if (!arrayFormulaResp.isEmpty()) {
                    sheetObjResponse.put(Integer.toString(CommandConstants.ARRAY_FORMULAS), arrayFormulaResp);
                }
                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);
            }

        }
    }

    private void updateMergeCells() {

        List<RangeWrapper> mergeCellRanges = getConstraintResponseHolder().getMergeCells();

        if (mergeCellRanges != null && !mergeCellRanges.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator<RangeWrapper> iterator = mergeCellRanges.iterator(); iterator.hasNext();) {

                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();

                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());

                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();

                JSONObjectWrapper mergeCellsResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.MERGE_CELLS)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.MERGE_CELLS))
                        : new JSONObjectWrapper();

                JSONObjectWrapper mergeAcrossResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.MERGE_ACROSS)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.MERGE_ACROSS))
                        : new JSONObjectWrapper();

                if (rangeWrapper.getOperationType() == CommandConstants.OperationType.GENERATE_LIST) {

                    Map<String, JSONArrayWrapper> mergeMap = ResponseUtils.getMergeCellsList(sheet, rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());
                    JSONArrayWrapper mergeCellsAry = !mergeMap.isEmpty() && mergeMap.containsKey("mergeCellAry") ? mergeMap.get("mergeCellAry") : new JSONArrayWrapper(); //No I18N
                    JSONArrayWrapper mergeAcrossAry = !mergeMap.isEmpty() && mergeMap.containsKey("mergeAcrossAry") ? mergeMap.get("mergeAcrossAry") : new JSONArrayWrapper(); //No I18N
                    if (!mergeCellsAry.isEmpty()) {
                        mergeCellsResp.put(Integer.toString(CommandConstants.META), mergeCellsAry);
                    }
                    if (!mergeAcrossAry.isEmpty()) {
                        mergeAcrossResp.put(Integer.toString(CommandConstants.META), mergeAcrossAry);
                    }

                } else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.MERGE) {

                    Map<String, JSONArrayWrapper> mergeMap = ResponseUtils.getMergeCellsList(sheet, rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());

                    JSONArrayWrapper mergeCellsAry = !mergeMap.isEmpty() && mergeMap.containsKey("mergeCellAry") ? mergeMap.get("mergeCellAry") : new JSONArrayWrapper(); //No I18N
                    JSONArrayWrapper mergeCellsMeta = !mergeCellsResp.isEmpty() && mergeCellsResp.has(Integer.toString(CommandConstants.ADD)) ? mergeCellsResp.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                    JSONArrayWrapper tempMergeCellsResp = mergeJsonArrays(mergeCellsAry, mergeCellsMeta);

                    JSONArrayWrapper mergeAcrossAry = !mergeMap.isEmpty() && mergeMap.containsKey("mergeAcrossAry") ? mergeMap.get("mergeAcrossAry") : new JSONArrayWrapper(); //No I18N
                    JSONArrayWrapper mergeAcrossMeta = !mergeAcrossResp.isEmpty() && mergeAcrossResp.has(Integer.toString(CommandConstants.ADD)) ? mergeAcrossResp.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                    JSONArrayWrapper tempMergeAcrossResp = mergeJsonArrays(mergeAcrossAry, mergeAcrossMeta);

                    if (!mergeCellsAry.isEmpty()) {
                        mergeCellsResp.put(Integer.toString(CommandConstants.ADD), tempMergeCellsResp);
                    }
                    if (!mergeAcrossAry.isEmpty()) {
                        mergeAcrossResp.put(Integer.toString(CommandConstants.ADD), tempMergeAcrossResp);
                    }


                } else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.MERGE_SPLIT) {

                    JSONArrayWrapper mergeCellAry = new JSONArrayWrapper();
                    JSONArrayWrapper tempJsonAry = new JSONArrayWrapper();

                    tempJsonAry.put(0, rangeWrapper.getstartRow());
                    tempJsonAry.put(1, rangeWrapper.getstartCol());
                    tempJsonAry.put(2, rangeWrapper.getEndRow());
                    tempJsonAry.put(3, rangeWrapper.getEndCol());

                    mergeCellAry.put(tempJsonAry);

                    JSONArrayWrapper mergeCellsMeta = !mergeCellsResp.isEmpty() && mergeCellsResp.has(Integer.toString(CommandConstants.REMOVE)) ? mergeCellsResp.getJSONArray(Integer.toString(CommandConstants.REMOVE)) : new JSONArrayWrapper();
                    JSONArrayWrapper tempMergeCellsResp = mergeJsonArrays(mergeCellAry, mergeCellsMeta);

                    mergeCellsResp.put(Integer.toString(CommandConstants.REMOVE), tempMergeCellsResp);

                }

                if (!mergeCellsResp.isEmpty()) {
                    sheetObjResponse.put(Integer.toString(CommandConstants.MERGE_CELLS), mergeCellsResp);
                }
                if (!mergeAcrossResp.isEmpty()) {
                    sheetObjResponse.put(Integer.toString(CommandConstants.MERGE_ACROSS), mergeAcrossResp);
                }

                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);
            }

        }
    }

    private void updateRowHeadersNew() {

        List<RangeWrapper> rowHeaders = getConstraintResponseHolder().getRowHeaderRanges();
        if (rowHeaders != null && !rowHeaders.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = rowHeaders.iterator(); iterator.hasNext();) {

                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();
                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());

                /**
                 * <AUTHOR> N J ( ZT-0049 )
                 */
                JSONArrayWrapper rowHeadDetails = ResponseUtils.getRowsNew(sheet, rangeWrapper.getstartRow(), rangeWrapper.getEndRow(), null);
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();
                JSONObjectWrapper rowHeadersResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.ROW_HEADERS)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.ROW_HEADERS))
                        : new JSONObjectWrapper();

                if (rangeWrapper.getOperationType() == ADD) {

                    JSONArrayWrapper addRowHead = !rowHeadersResp.isEmpty() && rowHeadersResp.has(Integer.toString(CommandConstants.META)) ? rowHeadersResp.getJSONArray(Integer.toString(CommandConstants.META))
                            : new JSONArrayWrapper();
                    JSONArrayWrapper tempRowHead = mergeJsonArrays(rowHeadDetails, addRowHead);
                    rowHeadersResp.put(Integer.toString(CommandConstants.META), tempRowHead);

                } else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.MODIFY) {

                    JSONArrayWrapper modifyRowHead = !rowHeadersResp.isEmpty() && rowHeadersResp.has(Integer.toString(CommandConstants.RESIZE)) ? rowHeadersResp.getJSONArray(Integer.toString(CommandConstants.RESIZE))
                            : new JSONArrayWrapper();
                    JSONArrayWrapper tempRowHead = mergeJsonArrays(rowHeadDetails, modifyRowHead);
                    rowHeadersResp.put(Integer.toString(CommandConstants.RESIZE), tempRowHead);

                } else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.INSERT) {

                    JSONArrayWrapper modifyRowHead = !rowHeadersResp.isEmpty() && rowHeadersResp.has(Integer.toString(CommandConstants.INSERT)) ? rowHeadersResp.getJSONArray(Integer.toString(CommandConstants.INSERT))
                            : new JSONArrayWrapper();
                    JSONArrayWrapper tempRowHead = mergeJsonArrays(rowHeadDetails, modifyRowHead);
                    rowHeadersResp.put(Integer.toString(CommandConstants.INSERT), tempRowHead);

                } else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.DELETE) {

                    JSONArrayWrapper modifyRowHead = !rowHeadersResp.isEmpty() && rowHeadersResp.has(Integer.toString(CommandConstants.DELETE)) ? rowHeadersResp.getJSONArray(Integer.toString(CommandConstants.DELETE))
                            : new JSONArrayWrapper();
                    JSONArrayWrapper deleteRowHeader = new JSONArrayWrapper();
                    JSONArrayWrapper tempDeleteRowHeader = new JSONArrayWrapper();
                    tempDeleteRowHeader.put(rangeWrapper.getstartRow());
                    tempDeleteRowHeader.put(rangeWrapper.getEndRow() - rangeWrapper.getstartRow() + 1); // This is count to delete number of rows
                    deleteRowHeader.put(tempDeleteRowHeader);
                    JSONArrayWrapper tempRowHead = mergeJsonArrays(deleteRowHeader, modifyRowHead);
                    rowHeadersResp.put(Integer.toString(CommandConstants.DELETE), tempRowHead);

                }
                sheetObjResponse.put(Integer.toString(CommandConstants.ROW_HEADERS), rowHeadersResp);
                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);

            }

        }

    }

    private void updateHiddenRows() {
        List<RangeWrapper> hiddenRows = getConstraintResponseHolder().getHiddenRows();
        if (hiddenRows != null && !hiddenRows.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = hiddenRows.iterator(); iterator.hasNext();) {
                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();
                CommandConstants.OperationType operationType = rangeWrapper.getOperationType();
                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();
                JSONObjectWrapper hiddenRowResponse = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.HIDDEN_ROWS)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.HIDDEN_ROWS)) : new JSONObjectWrapper();
                if (operationType == CommandConstants.OperationType.GENERATE_LIST) {
                    JSONArrayWrapper HideRowMeta = !hiddenRowResponse.isEmpty() && hiddenRowResponse.has(Integer.toString(CommandConstants.META)) ? hiddenRowResponse.getJSONArray(Integer.toString(CommandConstants.META)) : new JSONArrayWrapper();
                    JSONArrayWrapper hiddenRowsAry = ResponseUtils.getHiddenRows(sheet, rangeWrapper.getstartRow(), rangeWrapper.getEndRow());
                    JSONArrayWrapper tempHiddenRowsResp = mergeJsonArrays(hiddenRowsAry, HideRowMeta);
                    if (!hiddenRowsAry.isEmpty()) {
                        hiddenRowResponse.put(Integer.toString(CommandConstants.META), tempHiddenRowsResp);
                    }
                }else if(operationType == ADD){
                    JSONArrayWrapper HideRowMeta = !hiddenRowResponse.isEmpty() && hiddenRowResponse.has(Integer.toString(CommandConstants.ADD)) ? hiddenRowResponse.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                    JSONArrayWrapper hiddenRowsAry = ResponseUtils.getHiddenRowsWithingRange(sheet, rangeWrapper.getstartRow(), rangeWrapper.getEndRow());
                    JSONArrayWrapper tempHiddenRowsResp = mergeJsonArrays(hiddenRowsAry, HideRowMeta);

                    if (!tempHiddenRowsResp.isEmpty()) {
                        hiddenRowResponse.put(Integer.toString(CommandConstants.ADD), tempHiddenRowsResp);
                    }
                }
                else if (operationType == CommandConstants.OperationType.REMOVE) {
                    JSONArrayWrapper removeHideRowMeta = !hiddenRowResponse.isEmpty() && hiddenRowResponse.has(Integer.toString(CommandConstants.REMOVE)) ? hiddenRowResponse.getJSONArray(Integer.toString(CommandConstants.REMOVE)) : new JSONArrayWrapper();
                    JSONArrayWrapper RowArrayDetails = ResponseUtils.getVisibleRows(sheet, rangeWrapper.getstartRow(), rangeWrapper.getEndRow());
                    JSONArrayWrapper	tempHiddenRowsResp		=		mergeJsonArrays(RowArrayDetails, removeHideRowMeta);
                    if (!tempHiddenRowsResp.isEmpty()) {
                        hiddenRowResponse.put(Integer.toString(CommandConstants.REMOVE), tempHiddenRowsResp);
                    }
                }
                if (!hiddenRowResponse.isEmpty()) {
                    sheetObjResponse.put(Integer.toString(CommandConstants.HIDDEN_ROWS), hiddenRowResponse);
                }
                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);

            }
        }

    }

    private void updateColHeadersNew() {

        List<RangeWrapper> colHeaders = getConstraintResponseHolder().getColumnHeaderRanges();
        if (colHeaders != null && !colHeaders.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = colHeaders.iterator(); iterator.hasNext();) {

                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();
                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());
                JSONArrayWrapper colHeadDetails = ResponseUtils.getColHeadDetailsNew(sheet, rangeWrapper.getstartCol(), rangeWrapper.getEndCol());

                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();
                JSONObjectWrapper colHeadersResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.COLUMN_HEADERS)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.COLUMN_HEADERS))
                        : new JSONObjectWrapper();

                //There is a chance of duplication of columns
                if (rangeWrapper.getOperationType() == ADD) {

                    JSONArrayWrapper addColHead = !colHeadersResp.isEmpty() && colHeadersResp.has(Integer.toString(CommandConstants.META)) ? colHeadersResp.getJSONArray(Integer.toString(CommandConstants.META))
                            : new JSONArrayWrapper();
                    JSONArrayWrapper tempColHead = mergeJsonArrays(colHeadDetails, addColHead);
                    colHeadersResp.put(Integer.toString(CommandConstants.META), tempColHead);

                } else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.MODIFY) {

                    JSONArrayWrapper modifyColHead = !colHeadersResp.isEmpty() && colHeadersResp.has(Integer.toString(CommandConstants.RESIZE)) ? colHeadersResp.getJSONArray(Integer.toString(CommandConstants.RESIZE))
                            : new JSONArrayWrapper();
                    modifyColHead = mergeJsonArrays(colHeadDetails, modifyColHead);
                    colHeadersResp.put(Integer.toString(CommandConstants.RESIZE), modifyColHead);

                } else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.INSERT) {

                    JSONArrayWrapper modifyColHead = !colHeadersResp.isEmpty() && colHeadersResp.has(Integer.toString(CommandConstants.INSERT)) ? colHeadersResp.getJSONArray(Integer.toString(CommandConstants.INSERT))
                            : new JSONArrayWrapper();
                    JSONArrayWrapper tempColHead = mergeJsonArrays(colHeadDetails, modifyColHead);
                    colHeadersResp.put(Integer.toString(CommandConstants.INSERT), tempColHead);
                } else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.DELETE) {

                    JSONArrayWrapper modifyRowHead = !colHeadersResp.isEmpty() && colHeadersResp.has(Integer.toString(CommandConstants.DELETE)) ? colHeadersResp.getJSONArray(Integer.toString(CommandConstants.DELETE))
                            : new JSONArrayWrapper();
                    JSONArrayWrapper deleteColHeader = new JSONArrayWrapper();
                    JSONArrayWrapper tempDeleteColHeader = new JSONArrayWrapper();
                    tempDeleteColHeader.put(rangeWrapper.getstartCol());
                    tempDeleteColHeader.put(rangeWrapper.getEndCol() - rangeWrapper.getstartCol() + 1); // This is count to delete number of Cols
                    deleteColHeader.put(tempDeleteColHeader);
                    JSONArrayWrapper tempRowHead = mergeJsonArrays(deleteColHeader, modifyRowHead);
                    colHeadersResp.put(Integer.toString(CommandConstants.DELETE), tempRowHead);

                }

                sheetObjResponse.put(Integer.toString(CommandConstants.COLUMN_HEADERS), colHeadersResp);
                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);

            }
        }
    }

    private void updateHiddenColumns() {
        List<RangeWrapper> hiddenColumns = getConstraintResponseHolder().getHiddenColumns();
        if (hiddenColumns != null && !hiddenColumns.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = hiddenColumns.iterator(); iterator.hasNext();) {
                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();
                CommandConstants.OperationType operationType = rangeWrapper.getOperationType();
                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();
                JSONObjectWrapper hiddenColResponse = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.HIDDEN_COLS)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.HIDDEN_COLS)) : new JSONObjectWrapper();
                if (operationType == CommandConstants.OperationType.GENERATE_LIST) {
                    JSONArrayWrapper HideColMeta = !hiddenColResponse.isEmpty() && hiddenColResponse.has(Integer.toString(CommandConstants.META)) ? hiddenColResponse.getJSONArray(Integer.toString(CommandConstants.META)) : new JSONArrayWrapper();
                    JSONArrayWrapper hiddenColumnsAry = ResponseUtils.getHiddenColumns(sheet, rangeWrapper.getstartCol(), rangeWrapper.getEndCol());
                    JSONArrayWrapper tempHiddenColumnsResp = mergeJsonArrays(hiddenColumnsAry, HideColMeta);
                    if (!hiddenColumnsAry.isEmpty()) {
                        hiddenColResponse.put(Integer.toString(CommandConstants.META), tempHiddenColumnsResp);
                    }
                }else if(operationType == ADD){
                    JSONArrayWrapper addHideColMeta = !hiddenColResponse.isEmpty() && hiddenColResponse.has(Integer.toString(CommandConstants.ADD)) ? hiddenColResponse.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                    JSONArrayWrapper hiddenColumnsAry = ResponseUtils.getHiddenColumnsWithinRange(sheet, rangeWrapper.getstartCol(), rangeWrapper.getEndCol());
                    JSONArrayWrapper tempHiddenColumnsResp = mergeJsonArrays(hiddenColumnsAry, addHideColMeta);
                    if (!tempHiddenColumnsResp.isEmpty()) {
                        hiddenColResponse.put(Integer.toString(CommandConstants.ADD), tempHiddenColumnsResp);
                    }
                }
                else if (operationType == CommandConstants.OperationType.REMOVE) {
                    JSONArrayWrapper removeHideColMeta = !hiddenColResponse.isEmpty() && hiddenColResponse.has(Integer.toString(CommandConstants.REMOVE)) ? hiddenColResponse.getJSONArray(Integer.toString(CommandConstants.REMOVE)) : new JSONArrayWrapper();
                    JSONArrayWrapper colArrayDetails = ResponseUtils.getVisibleCols(sheet, rangeWrapper.getstartCol(), rangeWrapper.getEndCol());
                    JSONArrayWrapper	tempHiddenColumnsResp		=		mergeJsonArrays(colArrayDetails, removeHideColMeta);
                    if (!tempHiddenColumnsResp.isEmpty()) {
                        hiddenColResponse.put(Integer.toString(CommandConstants.REMOVE), tempHiddenColumnsResp);
                    }
                }
                if (!hiddenColResponse.isEmpty()) {
                    sheetObjResponse.put(Integer.toString(CommandConstants.HIDDEN_COLS), hiddenColResponse);
                }
                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);

            }

        }
    }

    private void updateFreezePanes() {

        List<FreezeWrapper> freezePanes = getConstraintResponseHolder().getFreezePanes();
        if(freezePanes != null) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (FreezeWrapper freezeWrapper :freezePanes) {
                CommandConstants.OperationType opType = freezeWrapper.getOperationType();
                int frzRowStart = 0, frzRowEnd = 0, frzColStart = 0, frzColEnd = 0;

                int operationType = opType == CommandConstants.OperationType.GENERATE_LIST ? CommandConstants.META : CommandConstants.MODIFY;
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(freezeWrapper.getAssociatedSheetName()) ? constraintResponseJson.getJSONObject(freezeWrapper.getAssociatedSheetName()) : new JSONObjectWrapper();
                JSONObjectWrapper freezePanesResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.FREEZE_PANES)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.FREEZE_PANES)): new JSONObjectWrapper();
                JSONArrayWrapper freezeAry = new JSONArrayWrapper();

                if(opType == CommandConstants.OperationType.UPDATE) {
                    Workbook workbook = getWorkbook();
                    String frzUpdateSheetName = workbook.getSheetByAssociatedName(freezeWrapper.getAssociatedSheetName()).getName();
                    WorkbookSettings wbSettings = workbook.getWorkbookSettings();
                    frzRowStart = wbSettings.getFreezePaneRowStart(frzUpdateSheetName);
                    frzColStart = wbSettings.getFreezePaneColumnStart(frzUpdateSheetName);
                    frzRowEnd = wbSettings.getFreezePaneRowEnd(frzUpdateSheetName) + 1;
                    frzColEnd = wbSettings.getFreezePaneColumnEnd(frzUpdateSheetName) + 1;
                } else {
                    frzRowStart = freezeWrapper.getFreezedRowPosStart();
                    frzColStart = freezeWrapper.getFreezedColPosStart();
                    frzRowEnd = freezeWrapper.getFreezedRowPosEnd();
                    frzColEnd = freezeWrapper.getFreezedColPosEnd();
                }

                //response will be frzEndRow, frzEndCol, frzStartRow, frzStartCol to account for mobile devices.
                freezeAry.put(0, frzRowEnd);
                freezeAry.put(1, frzColEnd);
                freezeAry.put(2, frzRowStart);
                freezeAry.put(3, frzColStart);
                freezePanesResp.put(Integer.toString(operationType), freezeAry);
                if (!freezePanesResp.isEmpty()) {
                    sheetObjResponse.put(Integer.toString(CommandConstants.FREEZE_PANES), freezePanesResp);
                }
                constraintResponseJson.put(freezeWrapper.getAssociatedSheetName(), sheetObjResponse);
            }
        }

    }

    private void updateHideGrid() {
        ConstraintResponseHolder responseHolder = getConstraintResponseHolder();
        List<HideGridWrapper> hideGrids = responseHolder.getHideGrid();
        if (hideGrids != null && !hideGrids.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = hideGrids.iterator(); iterator.hasNext();) {
                HideGridWrapper hideGrid = (HideGridWrapper) iterator.next();
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(hideGrid.getAssociatedSheetName()) ? constraintResponseJson.getJSONObject(hideGrid.getAssociatedSheetName()) : new JSONObjectWrapper();
                JSONObjectWrapper hideGridResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.HIDE_GRID)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.HIDE_GRID)): new JSONObjectWrapper();
                JSONArrayWrapper hideGridArray = new JSONArrayWrapper();
                Sheet sheet = getWorkbook().getSheetByAssociatedName(hideGrid.getAssociatedSheetName());
                boolean isShowGrid = ActionUtil.isShowGridLines(sheet);
                ZSColor gridColor = ActionUtil.getGridColor(sheet);

                try {
                    String rangeId = hideGrid.getRangeId();
                    if ( responseHolder.getPublishedViewWrapper() != null && !responseHolder.getPublishedViewWrapper().isEmpty() ) {
                        JSONObjectWrapper json = DocumentUtils.getPublicResourceMeta(getContainer(), rangeId);
                        isShowGrid = !(json.getBoolean(Integer.toString(CommandConstants.HIDE_GRID)));
                    }
                }
                catch (Exception e) {
                    LOGGER.info("Exception occured when fetching publicResourceMeta : " + e);
                }
                hideGridArray.put(0, hideGrid.getAssociatedSheetName());
                hideGridArray.put(1, isShowGrid);

                if(gridColor != null) {
                    String gridColorCode = ZSColor.getHexColor(gridColor, sheet.getWorkbook().getTheme());
                    hideGridArray.put(2, gridColorCode);
                }

                CommandConstants.OperationType operationType = hideGrid.getOperationType();
                String hideString = "";
                if(operationType == CommandConstants.OperationType.HIDE_SHOW_GRID)
                {
                    hideString = Integer.toString(CommandConstants.META);
                }
                else
                {
                    hideString = Integer.toString(CommandConstants.MODIFY);
                    if(gridColor != null) {
                        ActionUtil.setGridColor(sheet, gridColor, sheet.getWorkbook().getTheme());
                    }
                }

                hideGridResp.put(hideString, hideGridArray);
                if(!hideGridResp.isEmpty()){
                    sheetObjResponse.put(Integer.toString(CommandConstants.HIDE_GRID), hideGridResp);
                }
                constraintResponseJson.put(hideGrid.getAssociatedSheetName(), sheetObjResponse);
            }
        }
    }

    private void updateCheckBoxRanges() {

        List<RangeWrapper> checkBoxRanges = getConstraintResponseHolder().getCheckBoxRanges();
        if (checkBoxRanges != null && !checkBoxRanges.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = checkBoxRanges.iterator(); iterator.hasNext();) {

                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();
                CommandConstants.OperationType operationType = rangeWrapper.getOperationType();
                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();
                JSONObjectWrapper checkBoxResponse = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.CHECK_BOX)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CHECK_BOX)) : new JSONObjectWrapper();

                if (operationType == CommandConstants.OperationType.GENERATE_LIST) {
                    List<DataRange> checkBox = RangeUtil.getCheckboxRanges(sheet, rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());
                    JSONArrayWrapper listRange = new JSONArrayWrapper();
                    for (DataRange range : checkBox) {
                        JSONArrayWrapper rangeArray = new JSONArrayWrapper();
                        rangeArray.put(range.getStartRowIndex());
                        rangeArray.put(range.getStartColIndex());
                        rangeArray.put(range.getEndRowIndex());
                        rangeArray.put(range.getEndColIndex());
                        listRange.put(rangeArray);
                    }

                    if (!listRange.isEmpty()) {
                        checkBoxResponse.put(Integer.toString(CommandConstants.META), listRange);
                    }
                } else if (operationType == CommandConstants.OperationType.MODIFY) {
                    JSONArrayWrapper modifyRange = !checkBoxResponse.isEmpty() && checkBoxResponse.has(Integer.toString(CommandConstants.MODIFY)) ? checkBoxResponse.getJSONArray(Integer.toString(CommandConstants.MODIFY)) : new JSONArrayWrapper();
                    List<DataRange> checkBox = RangeUtil.getCheckboxRanges(sheet, rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());
                    for (DataRange range : checkBox) {
                        JSONArrayWrapper rangeArray = new JSONArrayWrapper();
                        rangeArray.put(range.getStartRowIndex());
                        rangeArray.put(range.getStartColIndex());
                        rangeArray.put(range.getEndRowIndex());
                        rangeArray.put(range.getEndColIndex());
                        modifyRange.put(rangeArray);
                    }
                    if (!modifyRange.isEmpty()) {
                        checkBoxResponse.put(Integer.toString(CommandConstants.MODIFY), modifyRange);
                    }
                } else if (operationType == ADD) {
                    JSONArrayWrapper addRange = !checkBoxResponse.isEmpty() && checkBoxResponse.has(Integer.toString(CommandConstants.ADD)) ? checkBoxResponse.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                    List<DataRange> checkBox = RangeUtil.getCheckboxRanges(sheet, rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());
                    for (DataRange range : checkBox) {
                        JSONArrayWrapper rangeArray = new JSONArrayWrapper();
                        rangeArray.put(range.getStartRowIndex());
                        rangeArray.put(range.getStartColIndex());
                        rangeArray.put(range.getEndRowIndex());
                        rangeArray.put(range.getEndColIndex());
                        addRange.put(rangeArray);
                    }
                    if (!addRange.isEmpty()) {
                        checkBoxResponse.put(Integer.toString(CommandConstants.ADD), addRange);
                    }
                } else if (operationType == CommandConstants.OperationType.REMOVE) {
                    JSONArrayWrapper removeRange = !checkBoxResponse.isEmpty() && checkBoxResponse.has(Integer.toString(CommandConstants.REMOVE)) ? checkBoxResponse.getJSONArray(Integer.toString(CommandConstants.REMOVE)) : new JSONArrayWrapper();
                    JSONArrayWrapper rangeArray = new JSONArrayWrapper();
                    rangeArray.put(rangeWrapper.getstartRow());
                    rangeArray.put(rangeWrapper.getstartCol());
                    rangeArray.put(rangeWrapper.getEndRow());
                    rangeArray.put(rangeWrapper.getEndCol());
                    removeRange.put(rangeArray);
                    checkBoxResponse.put(Integer.toString(CommandConstants.REMOVE), removeRange);
                }
                if (!checkBoxResponse.isEmpty()) {
                    sheetObjResponse.put(Integer.toString(CommandConstants.CHECK_BOX), checkBoxResponse);
                }
                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);
            }
        }

    }

    private void updateColumnLevelFormats() {

        List<RangeWrapper> colLevelFormats = getConstraintResponseHolder().getColumnLevelFormats();

        if (colLevelFormats != null && !colLevelFormats.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = colLevelFormats.iterator(); iterator.hasNext();) {

                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();

                Sheet sheet = getWorkbook().getSheetByAssociatedName(rangeWrapper.getSheetName());

                JSONArrayWrapper colLvelCellStyles = ResponseUtils.getColumnLevelCellStyles(sheet, rangeWrapper.getstartCol(), rangeWrapper.getEndCol());

                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(rangeWrapper.getSheetName()) ? constraintResponseJson.getJSONObject(rangeWrapper.getSheetName()) : new JSONObjectWrapper();

                JSONObjectWrapper colLevelFrmtResp = !sheetObjResponse.isEmpty() && sheetObjResponse.has(Integer.toString(CommandConstants.COLUMN_LEVEL_CELLSTYLES)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.COLUMN_LEVEL_CELLSTYLES))
                        : new JSONObjectWrapper();

                if (rangeWrapper.getOperationType() == ADD) {

                    JSONArrayWrapper colLevelResp = !colLevelFrmtResp.isEmpty() && colLevelFrmtResp.has(Integer.toString(CommandConstants.META)) ? colLevelFrmtResp.getJSONArray(Integer.toString(CommandConstants.META))
                            : new JSONArrayWrapper();

                    JSONArrayWrapper colLevelFrmt = mergeJsonArrays(colLvelCellStyles, colLevelResp);

                    colLevelFrmtResp.put(Integer.toString(CommandConstants.META), colLevelFrmt);

                } else if (rangeWrapper.getOperationType() == CommandConstants.OperationType.MODIFY) {

                    JSONArrayWrapper colLevelResp = !colLevelFrmtResp.isEmpty() && colLevelFrmtResp.has(Integer.toString(CommandConstants.MODIFY)) ? colLevelFrmtResp.getJSONArray(Integer.toString(CommandConstants.MODIFY))
                            : new JSONArrayWrapper();

                    JSONArrayWrapper colLevelFrmt = mergeJsonArrays(colLvelCellStyles, colLevelResp);

                    colLevelFrmtResp.put(Integer.toString(CommandConstants.MODIFY), colLevelFrmt);

                }

                sheetObjResponse.put(Integer.toString(CommandConstants.COLUMN_LEVEL_CELLSTYLES), colLevelFrmtResp);
                constraintResponseJson.put(rangeWrapper.getSheetName(), sheetObjResponse);
            }
        }
    }

    private void updateCellResposneData(HashMap<String, Object> cellResponseData) {

        HashMap<String, JSONArrayWrapper> rowKeyMap = (HashMap<String, JSONArrayWrapper>) cellResponseData.get("ROWDATA");

        HashMap<String, JSONObjectWrapper> colKeyMap = (HashMap<String, JSONObjectWrapper>) cellResponseData.get("COLDATA");

        Set<String> keySet = rowKeyMap.keySet();
        JSONObjectWrapper constraintResponseJson = getConstraintResponse();
        for (Iterator iterator = keySet.iterator(); iterator.hasNext();) {

            String sheetName = (String) iterator.next();

            JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetName) ? constraintResponseJson.getJSONObject(sheetName) : new JSONObjectWrapper();
            JSONObjectWrapper cellconstraintResponseJson = sheetObjResponse.has(Integer.toString(CommandConstants.CELL_META)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CELL_META)) : new JSONObjectWrapper();

            cellconstraintResponseJson.put(Integer.toString(CommandConstants.ROW_DATA), rowKeyMap.get(sheetName));
            cellconstraintResponseJson.put(Integer.toString(CommandConstants.COLUMN_DATA), colKeyMap.get(sheetName));

            //Temporarily, we are putting here.
            cellconstraintResponseJson.put(Integer.toString(CommandConstants.DATA_HEADER), getConstraintResponseHolder().getCellMetaHeader());

            JSONObjectWrapper metaCellconstraintResponseJson = new JSONObjectWrapper();
            metaCellconstraintResponseJson.put(Integer.toString(CommandConstants.META), cellconstraintResponseJson);

            sheetObjResponse.put(Integer.toString(CommandConstants.CELL_META), metaCellconstraintResponseJson);

            constraintResponseJson.put(sheetName, sheetObjResponse);

        }

    }

    private void updateCellMetaHeader(int cellMetaheader) {

    }

    private void updateFaultyCells(HashMap<String, HashSet<Long>> faultyCells) {

        Set<String> keySet = faultyCells.keySet();
        JSONObjectWrapper constraintResponseJson = getConstraintResponse();
        for (Iterator iterator = keySet.iterator(); iterator.hasNext();) {

            String sheetName = (String) iterator.next();
            JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetName) ? constraintResponseJson.getJSONObject(sheetName) : new JSONObjectWrapper();
            JSONObjectWrapper cellconstraintResponseJson = sheetObjResponse.has(Integer.toString(CommandConstants.CELL_META)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CELL_META)) : new JSONObjectWrapper();
            HashSet<Long> cellsList = faultyCells.get(sheetName);
            JSONObjectWrapper faultyData = cellconstraintResponseJson.has(Integer.toString(CommandConstants.FAULTY)) ? cellconstraintResponseJson.getJSONObject(Integer.toString(CommandConstants.FAULTY)) : new JSONObjectWrapper();

            faultyData.put(Integer.toString(CommandConstants.CELLS), JSONArrayWrapper.fromCollection(cellsList));
            cellconstraintResponseJson.put(Integer.toString(CommandConstants.FAULTY), faultyData);

            sheetObjResponse.put(Integer.toString(CommandConstants.CELL_META), cellconstraintResponseJson);

            constraintResponseJson.put(sheetName, sheetObjResponse);
        }

    }

    private void updateFaultyRanges(HashMap<String, JSONArrayWrapper> faultyRanges) {

        Set<String> keySet = faultyRanges.keySet();
        JSONObjectWrapper constraintResponseJson = getConstraintResponse();
        for (Iterator iterator = keySet.iterator(); iterator.hasNext();) {

            String sheetName = (String) iterator.next();
            JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetName) ? constraintResponseJson.getJSONObject(sheetName) : new JSONObjectWrapper();
            JSONObjectWrapper cellconstraintResponseJson = sheetObjResponse.has(Integer.toString(CommandConstants.CELL_META)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CELL_META)) : new JSONObjectWrapper();
            JSONObjectWrapper faultyData = cellconstraintResponseJson.has(Integer.toString(CommandConstants.FAULTY)) ? cellconstraintResponseJson.getJSONObject(Integer.toString(CommandConstants.FAULTY)) : new JSONObjectWrapper();

            faultyData.put(Integer.toString(CommandConstants.RANGES), faultyRanges.get(sheetName));
            cellconstraintResponseJson.put(Integer.toString(CommandConstants.FAULTY), faultyData);
            sheetObjResponse.put(Integer.toString(CommandConstants.CELL_META), cellconstraintResponseJson);

            constraintResponseJson.put(sheetName, sheetObjResponse);
        }
    }


    private void updateConstraintResponseActiveInfo() {
        com.adventnet.zoho.websheet.model.response.beans.ActiveInfoBean activeInfoBean = getConstraintResponseHolder().getActiveInfoBean();
        if (activeInfoBean != null) {
            JSONObjectWrapper activeInfo = new JSONObjectWrapper();
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            String sheetName = activeInfoBean.getActiveSheetName();

            activeInfo.put(Integer.toString(CommandConstants.APPLIED_FILTER_NAME), activeInfoBean.getAppliedFilterName());
            activeInfo.put(Integer.toString(CommandConstants.ACTIVE_CELL), activeInfoBean.getActiveCell());
            activeInfo.put(Integer.toString(CommandConstants.ACTIVE_RANGES), activeInfoBean.getActiveRanges());
//            activeInfo.put(Integer.toString(CommandConstants.COMMENT_INFO),activeInfoBean.getCommentInfo());
            activeInfo.put(Integer.toString(CommandConstants.PERSISTED_POSITIONS), activeInfoBean.getPersistedPositions());

            JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetName) ? constraintResponseJson.getJSONObject(sheetName) : new JSONObjectWrapper();

            sheetObjResponse.put(Integer.toString(CommandConstants.ACTIVE_INFO), activeInfo);

            constraintResponseJson.put(sheetName, sheetObjResponse);
        }

    }

    private void updateInsertDeleteCells() {

        List<RangeWrapper> insertDeleteCells = getConstraintResponseHolder().getinsertDeleteCellRangeWrapper();
        if (insertDeleteCells != null && !insertDeleteCells.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = insertDeleteCells.iterator(); iterator.hasNext();) {
                RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();
                String sheetName = rangeWrapper.getSheetName();
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetName) ? constraintResponseJson.getJSONObject(sheetName) : new JSONObjectWrapper();
                JSONObjectWrapper cellconstraintResponseJson = sheetObjResponse.has(Integer.toString(CommandConstants.CELL_META)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CELL_META)) : new JSONObjectWrapper();
                CommandConstants.OperationType opType = rangeWrapper.getOperationType();
                int commandType = opType == CommandConstants.OperationType.INSERT_CELL_LEFT ? CommandConstants.INSERT_CELL_LEFT
                        : (opType == CommandConstants.OperationType.INSERT_CELL_TOP ? CommandConstants.INSERT_CELL_TOP
                        : (opType == CommandConstants.OperationType.DELETE_CELL_BOTTOM ? CommandConstants.DELETE_CELL_BOTTOM
                        : (opType == CommandConstants.OperationType.DELETE_CELL_RIGHT ? CommandConstants.DELETE_CELL_RIGHT : -1)));
                if (commandType != -1) {

                    JSONArrayWrapper range = new JSONArrayWrapper();
                    range.put(0, rangeWrapper.getstartRow());
                    range.put(1, rangeWrapper.getstartCol());
                    range.put(2, rangeWrapper.getEndRow());
                    range.put(3, rangeWrapper.getEndCol());

                    cellconstraintResponseJson.put(Integer.toString(commandType), range);
                    sheetObjResponse.put(Integer.toString(CommandConstants.CELL_META), cellconstraintResponseJson);

                    constraintResponseJson.put(sheetName, sheetObjResponse);

                }
            }
        }

    }

    private void updateFormRanges() {

        RangeWrapper formRanges = getConstraintResponseHolder().getFormRanges();

        if (formRanges != null) {

            String sheetName = formRanges.getSheetName();
            Sheet sheet = getWorkbook().getSheetByAssociatedName(sheetName);

            FormRange formRange = sheet.getFormRange();

            if (formRange != null) {
                JSONObjectWrapper constraintResponseJson = getConstraintResponse();
                JSONArrayWrapper rangeAry = new JSONArrayWrapper();
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetName) ? constraintResponseJson.getJSONObject(sheetName) : new JSONObjectWrapper();

                rangeAry.put(0, formRange.getStartRowIndex());
                rangeAry.put(1, formRange.getStartColIndex());
                rangeAry.put(2, formRange.getEndRowIndex());
                rangeAry.put(3, formRange.getEndColIndex());

                JSONObjectWrapper formObj = new JSONObjectWrapper();
                formObj.put(Integer.toString(CommandConstants.META), rangeAry);

                sheetObjResponse.put(Integer.toString(CommandConstants.FORM_RANGE), formObj);

                constraintResponseJson.put(sheetName, sheetObjResponse);
            }

        }
    }

    private void updateAppliedConditionalCellStyleToRanges() {

        long		startTime	=	System.currentTimeMillis();
        JSONObjectWrapper constraintResponseJson = getConstraintResponse();
        ConstraintResponseHolder constraintResponseHolder = getConstraintResponseHolder();
        Workbook workbook = getWorkbook();
        Map<String,List<RangeWrapper>> cfRangeMap = constraintResponseHolder.getCFRangeList();
        Map<String,List<RangeWrapper>> cfFaultyRangeMap = constraintResponseHolder.getCFFaultyRangeList();
        if (cfRangeMap != null && !cfRangeMap.isEmpty()) {
            for(String sheetName : cfRangeMap.keySet()) {
                List<RangeWrapper> rangeList = cfRangeMap.get(sheetName);
                if(!rangeList.isEmpty()) {
                    Sheet sheet = workbook.getSheetByAssociatedName(sheetName);
                    JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetName) ? constraintResponseJson.getJSONObject(sheetName) : new JSONObjectWrapper();
                    JSONObjectWrapper cellconstraintResponseJson = sheetObjResponse.has(Integer.toString(CommandConstants.CELL_META)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CELL_META)) : new JSONObjectWrapper();

                    Set<DataRange> dataRanges = new HashSet<>();
                    for(RangeWrapper wrapper : rangeList) {
                        DataRange dr = new DataRange(sheetName,wrapper.getstartRow(),wrapper.getstartCol(),wrapper.getEndRow(),wrapper.getEndCol());
                        dataRanges.add(dr);
                    }

                    JSONObjectWrapper conditionalFrmtJson = new JSONObjectWrapper();
                    try {
                        JSONArrayWrapper conditionalFormatAry = ConditionalStyleResponse.getAppliedConditionalCellStylesToRanges(sheet, dataRanges);

                        if (!conditionalFormatAry.isEmpty()) {
                            conditionalFrmtJson.put(Integer.toString(CommandConstants.META), conditionalFormatAry);
                        }
                    } catch(ConditionalStyleResponse.ConditionalStyleResponseFailedException e) {
                        conditionalFrmtJson.put(Integer.toString(CommandConstants.ERROR), JSONConstants.CF_RESPONSE_TIMEOUT);
                    }

                    if(!conditionalFrmtJson.isEmpty()) {
                        cellconstraintResponseJson.put(Integer.toString(CommandConstants.APPLIED_COND_STYLE), conditionalFrmtJson);
                        sheetObjResponse.put(Integer.toString(CommandConstants.CELL_META), cellconstraintResponseJson);

                        constraintResponseJson.put(sheetName, sheetObjResponse);
                    }
                }
            }
        }
        if(cfFaultyRangeMap != null && !cfFaultyRangeMap.isEmpty()) {
            for(String sheetName : cfFaultyRangeMap.keySet()) {
                List<RangeWrapper> cfFaultyRangeList = cfFaultyRangeMap.get(sheetName);
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetName) ? constraintResponseJson.getJSONObject(sheetName) : new JSONObjectWrapper();
                JSONObjectWrapper cellconstraintResponseJson = sheetObjResponse.has(Integer.toString(CommandConstants.CELL_META)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CELL_META)) : new JSONObjectWrapper();
                JSONObjectWrapper CFJson = cellconstraintResponseJson.has(Integer.toString(CommandConstants.APPLIED_COND_STYLE)) ? cellconstraintResponseJson.getJSONObject(Integer.toString(CommandConstants.APPLIED_COND_STYLE)) : new JSONObjectWrapper();
                Sheet sheet = workbook.getSheetByAssociatedName(sheetName);

                JSONArrayWrapper CFFaultyArray = ResponseUtils.getCFFaultyRanges(cfFaultyRangeList,sheet);

                if (!CFFaultyArray.isEmpty()) {
                    JSONObjectWrapper faultyJson = new JSONObjectWrapper();
                    faultyJson.put(Integer.toString(CommandConstants.RANGES), CFFaultyArray);
                    CFJson.put(Integer.toString(CommandConstants.FAULTY), faultyJson);
                    cellconstraintResponseJson.put(Integer.toString(CommandConstants.APPLIED_COND_STYLE), CFJson);
                    sheetObjResponse.put(Integer.toString(CommandConstants.CELL_META), cellconstraintResponseJson);
                    constraintResponseJson.put(sheetName, sheetObjResponse);
                }
            }
        }

//        LOGGER.log(Level.INFO, " [NEW CLIENT] Totaltime taken to execute CF response  {0} " ,(System.currentTimeMillis() - startTime));
    }

    private void updateMaxUsedCells() {

        RangeWrapper maxUsedCellsRange = getConstraintResponseHolder().getMaxUsedCells();
        if (maxUsedCellsRange != null) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            String sheetName = maxUsedCellsRange.getSheetName();
            JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetName) ? constraintResponseJson.getJSONObject(sheetName) : new JSONObjectWrapper();

            Sheet sheet = getWorkbook().getSheetByAssociatedName(sheetName);

            JSONArrayWrapper rangeAry = new JSONArrayWrapper();
            JSONArrayWrapper formatAry = new JSONArrayWrapper();

            rangeAry.put(sheet.getUsedRowIndex());
            rangeAry.put(sheet.getUsedColumnIndex());

            // Max used cell based on format
            formatAry.put(sheet.getRowNum()-1);
            formatAry.put(sheet.getColNum()-1);

            JSONObjectWrapper maxUsedCellObj = new JSONObjectWrapper();
            JSONObjectWrapper maxUsedFormatCellObj = new JSONObjectWrapper();

            maxUsedCellObj.put(Integer.toString(CommandConstants.META), rangeAry);
            maxUsedFormatCellObj.put(Integer.toString(CommandConstants.META), formatAry);

            sheetObjResponse.put(Integer.toString(CommandConstants.MAX_USED_CELL), maxUsedCellObj);
            sheetObjResponse.put(Integer.toString(CommandConstants.MAX_USED_CELLFORMAT), maxUsedFormatCellObj);

            constraintResponseJson.put(sheetName, sheetObjResponse);
        }

    }

    private void updateConstraintResponseFaultySheets() {

        HashSet<String> faultySheets = getConstraintResponseHolder().getFaultySheets();

        if (faultySheets != null && !faultySheets.isEmpty()) {
            JSONObjectWrapper faultySheetObj = new JSONObjectWrapper();
            faultySheetObj.put(Integer.toString(CommandConstants.WORKSHEETS), faultySheets.toArray());
            this.faultySheetObj = faultySheetObj;
        }

    }

    private JSONArrayWrapper mergeJsonArrays(JSONArrayWrapper firstAry, JSONArrayWrapper secondAry) {

        if (firstAry.isEmpty()) {

            return secondAry;

        }

        if (secondAry.isEmpty()) {
            return firstAry;
        }

        for (int i = 0; i < firstAry.length(); i++) {
            secondAry.put(firstAry.get(i));
        }

        return secondAry;
    }

    private void updateImageInfo() {
        List<com.adventnet.zoho.websheet.model.response.beans.SheetImageBean> imageBeanList = getConstraintResponseHolder().getImageBeans();
        WorkbookContainer container = getContainer();
        Boolean isRemoteMode = (container.getResourceId() == null);
        String DocID = container.getDocId();
        if(!isRemoteMode){
            DocID = container.getResourceId();
        }
        String imageConstant = Integer.toString(CommandConstants.IMAGE);
        // TODO PASS IMAGEBOOK IN RESPONSE OBJECT
        try
        {
            String versionNo = null;
            try
            {
                versionNo = CurrentRealm.getWorkBookIdentity();
            }
            catch(Exception e1)
            {
                versionNo = null;
            }
            ImageBook imageBook = container.getImageBook(versionNo);
            if (versionNo != null)
            {
                container.addToVersionBooksMap(versionNo);
            }
            if (imageBeanList != null && !imageBeanList.isEmpty())
            {
                JSONObjectWrapper constraintResponseJson = getConstraintResponse();
                Workbook workbook = getWorkbook();
                for (Iterator iterator = imageBeanList.iterator(); iterator.hasNext();)
                {
                    com.adventnet.zoho.websheet.model.response.beans.SheetImageBean imageBean = (com.adventnet.zoho.websheet.model.response.beans.SheetImageBean) iterator.next();
                    String assSheetName = imageBean.getSheetAsn();
                    Sheet sheet = workbook.getSheetByAssociatedName(assSheetName);
                    JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(assSheetName) ? constraintResponseJson.getJSONObject(assSheetName) : new JSONObjectWrapper();
                    JSONObjectWrapper imageJsonResponse = !sheetObjResponse.isEmpty() && sheetObjResponse.has(imageConstant) ? sheetObjResponse.getJSONObject(imageConstant) : new JSONObjectWrapper();
                    CommandConstants.OperationType operationType = imageBean.getOperationType();
                    String action;
                    JSONArrayWrapper imageJsonArray;
                    if (operationType == CommandConstants.OperationType.GENERATE_LIST)
                    {
                        action = Integer.toString(CommandConstants.META);
                        imageJsonArray = imageJsonResponse.has(action) ? imageJsonResponse.getJSONArray(action) : new JSONArrayWrapper();
                        try
                        {
                            if (imageBook != null)
                            {
                                imageJsonArray = ImageUtils.getSheetImagesResponse(imageBook, sheet, isRemoteMode, DocID);
                            }
                        }
                        catch (Exception ex)
                        {
                            LOGGER.log(Level.SEVERE, "[IMAGES][Exception] Exception while sending image response.", ex);
                        }
                    }
                    else
                    {
                        action = (operationType == CommandConstants.OperationType.INSERT || operationType == ADD) ? Integer.toString(CommandConstants.INSERT) : (operationType == CommandConstants.OperationType.MOVE) ? Integer.toString(CommandConstants.MOVE) : (operationType == CommandConstants.OperationType.REMOVE || operationType == CommandConstants.OperationType.DELETE) ? Integer.toString(CommandConstants.DELETE) : (operationType == CommandConstants.OperationType.MODIFY) ? Integer.toString(CommandConstants.RESIZE) : (operationType == CommandConstants.OperationType.EDIT) ? Integer.toString(CommandConstants.EDIT) : null;
                        imageJsonArray = imageJsonResponse.has(action) ? imageJsonResponse.getJSONArray(action) : new JSONArrayWrapper();
                        try
                        {
                            JSONObjectWrapper imageResponseObject = ImageUtils.getImageResponse(container, sheet, imageBean, action);
                            imageJsonArray.put(imageResponseObject);
                        }
                        catch (Exception ex)
                        {
                            LOGGER.log(Level.SEVERE, "[IMAGES] Exception while generating response for image", ex);
                        }
                    }

                    if (!imageJsonArray.isEmpty())
                    {
                        imageJsonResponse.put(action, imageJsonArray);
                        sheetObjResponse.put(imageConstant, imageJsonResponse);
                    }
                    constraintResponseJson.put(assSheetName, sheetObjResponse);
                }
            }
        }
        catch(Exception e)
        {
            LOGGER.log(Level.SEVERE, "[IMAGES][Exception] Exception while sending image response.", e);
        }
    }

    private void updateChartInfo() {

        List<com.adventnet.zoho.websheet.model.response.beans.ChartBean> chartBeanList			=	getConstraintResponseHolder().getChartBean();
        if(chartBeanList != null && !chartBeanList.isEmpty()) {
            WorkbookContainer container = getContainer();
            Workbook workbook = getWorkbook();
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for(Iterator iterator = chartBeanList.iterator(); iterator.hasNext();) {
                com.adventnet.zoho.websheet.model.response.beans.ChartBean chartBean = (ChartBean) iterator.next();
                if(chartBean != null) {
                    String assSheetName = chartBean.getSheetName();
                    String chartId = chartBean.getChartId();
                    int action = chartBean.getAction();
                    CommandConstants.OperationType operationType = chartBean.getOperationType();
                    JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(assSheetName) ? constraintResponseJson.getJSONObject(assSheetName) : new JSONObjectWrapper();
                    String sheetName = workbook.getSheetByAssociatedName(assSheetName).getName();
                    int subAction = chartBean.getSubAction();
                    String value = chartBean.getValue();
                    JSONObjectWrapper chart = sheetObjResponse.has(Integer.toString(CommandConstants.CHART)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CHART)) : new JSONObjectWrapper();
                    String targetSheetName = chartBean.getTargetSheetName();
                    boolean isYAxis = chartBean.isYAxis();
                    if (operationType == CommandConstants.OperationType.GENERATE_LIST) {
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.readChartDetails(container, workbook, sheetName);
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for GENERATE_LIST  {0}", e);
                        }
                        if (!chartJson.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.META), chartJson);
                        }
                    } else if (operationType == CommandConstants.OperationType.THEME) {
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.readChartDetails(container, workbook, sheetName);
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for THEME {0}", e);
                        }
                        if (!chartJson.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.THEME), chartJson);
                        }
                    } else if (operationType == CommandConstants.OperationType.INSERT) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.INSERT)) ? chart.getJSONArray(Integer.toString(CommandConstants.INSERT)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for INSERT {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.INSERT), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.MOVE) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.MOVE)) ? chart.getJSONArray(Integer.toString(CommandConstants.MOVE)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for MOVE {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.MOVE), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.RESIZE) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.RESIZE)) ? chart.getJSONArray(Integer.toString(CommandConstants.RESIZE)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for RESIZE {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.RESIZE), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.MODIFY) {
                        //JSONArrayWrapper  chartArray 	= 	!chart.isEmpty() && !chart.getJSONArray(Integer.toString(CommandConstants.MODIFY)).isEmpty() ? chart.getJSONArray(Integer.toString(CommandConstants.MODIFY)) : new JSONArrayWrapper();
                        JSONArrayWrapper chartArray = !chart.isEmpty() && chart.has(Integer.toString(CommandConstants.MODIFY)) && !chart.getJSONArray(Integer.toString(CommandConstants.MODIFY)).isEmpty() ? chart.getJSONArray(Integer.toString(CommandConstants.MODIFY)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, subAction, value, isYAxis, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for MODIFY {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.MODIFY), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.CHART_MODIFY) {
                        JSONArrayWrapper chartArray = !chart.isEmpty() && chart.has(Integer.toString(CommandConstants.CHART_MODIFY)) && !chart.getJSONArray(Integer.toString(CommandConstants.CHART_MODIFY)).isEmpty() ? chart.getJSONArray(Integer.toString(CommandConstants.CHART_MODIFY)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, subAction, value, isYAxis, true);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for CHART_MODIFY {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.CHART_MODIFY), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.DELETE) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.DELETE)) ? chart.getJSONArray(Integer.toString(CommandConstants.DELETE)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson.put("asn", assSheetName);
                            chartJson.put("id", chartId);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for DELETE {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.DELETE), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.EDIT) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.EDIT)) ? chart.getJSONArray(Integer.toString(CommandConstants.EDIT)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, subAction, value, isYAxis, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for EDIT {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.EDIT), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.UPDATE) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.UPDATE)) ? chart.getJSONArray(Integer.toString(CommandConstants.UPDATE)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, subAction, value, isYAxis, false, true);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for UPDATE {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.UPDATE), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.PUBLISH) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.PUBLISH)) ? chart.getJSONArray(Integer.toString(CommandConstants.PUBLISH)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for PUBLISH {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.PUBLISH), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.UNPUBLISH) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.UNPUBLISH)) ? chart.getJSONArray(Integer.toString(CommandConstants.UNPUBLISH)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for UNPUBLISH {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.UNPUBLISH), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.CHART_PUBLISH_CHANGE) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.CHART_PUBLISH_CHANGE)) ? chart.getJSONArray(Integer.toString(CommandConstants.CHART_PUBLISH_CHANGE)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for CHART_PUBLISH_CHANGE {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.CHART_PUBLISH_CHANGE), chartArray);
                        }
                    } else if (operationType == CommandConstants.OperationType.RENAME) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.RENAME)) ? chart.getJSONArray(Integer.toString(CommandConstants.RENAME)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, " ############ Error while generating chart response for RENAME {0}", e);
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.RENAME), chartArray);
                        }
                    } else if (operationType == ADD) {
                        JSONArrayWrapper chartArray = chart.has(Integer.toString(CommandConstants.ADD)) ? chart.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                        JSONObjectWrapper chartJson = new JSONObjectWrapper();
                        try {
                            chartJson = ChartUtil.getChartResponse(container, workbook, assSheetName, chartId, action, false);
                            if (!chartJson.isEmpty()) {
                                chartArray.put(chartJson);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, "[CHART] >>>>>>>>>>>>>> Exception while generation chart response {0} {1}", new Object[]{ADD, e});
                        }
                        if (!chartArray.isEmpty()) {
                            chart.put(Integer.toString(CommandConstants.ADD), chartArray);
                        }
                    }

                    if (!chart.isEmpty()) {
                        sheetObjResponse.put(Integer.toString(CommandConstants.CHART), chart);
                        constraintResponseJson.put(assSheetName, sheetObjResponse);
                    }
                }
            }
        }
    }

    public void updateKnitChartsInfo() {
        List<KnitChartsBean> chartBeans = getConstraintResponseHolder().getKnitChartsBeans();
        ChartEngineManager.getChartEngine(getWorkbook())
                .updateKnitChartsInfo(getWorkbook(), getConstraintResponse(), chartBeans, ResponseObject.ResponseVersion.V2);
    }

    public void updateNavigatorRefreshStatusInResponse() {
        boolean needToRefresh = getDocumentResponseHolder().isNeedToRefreshNavigator();

        if(!needToRefresh) { return; }
        getDocumentResponse().put(String.valueOf(CommandConstants.NAVIGATOR_META_VERSION), getWorkbook().getNavigator().getNavigatorMetaVersion());
    }

    public void updateKnitChartsSheetInfo() {
        List<KnitChartsSheetBean> knitChartsSheetBeans = getConstraintResponseHolder().getKnitChartsSheetBeans();

        ChartEngineManager.getChartEngine(getWorkbook())
                .updateKnitChartsSheetInfo(getWorkbook(), getConstraintResponse(), knitChartsSheetBeans, ResponseObject.ResponseVersion.V2);
    }

    public void updateKnitChartsModifiedInfo() {
        List<KnitChartsModifiedBean> modifiedChartList = getConstraintResponseHolder().getKnitChartsModifiedBeans();

        ChartEngineManager.getChartEngine(getWorkbook())
                .updateKnitChartsModifiedInfo(getWorkbook(), getConstraintResponse(), modifiedChartList, ResponseObject.ResponseVersion.V2);
    }

    public void updateKnitChartsClipInfo() {
        KnitChartsClipBean clipBean = getUserSpecificResponseHolder().getKnitChartsClipBean();
        JSONObjectWrapper serverClip = getUserSpecificResponseHolder().getChartStyleClipObj();
        ChartEngineManager.getChartEngine(getWorkbook())
                .updateKnitChartsClipInfo(getUserSpecificResponse(), serverClip, clipBean, ResponseObject.ResponseVersion.V2);
    }

    public void updateButtonInfo(){

        List<com.adventnet.zoho.websheet.model.response.beans.ButtonBean> buttonBeans = getConstraintResponseHolder().getButtonBeans();
        if(buttonBeans != null && !buttonBeans.isEmpty()){
            WorkbookContainer container = getContainer();
            Workbook workbook = getWorkbook();
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (Iterator iterator = buttonBeans.iterator(); iterator.hasNext(); ) {

                com.adventnet.zoho.websheet.model.response.beans.ButtonBean buttonBean = (com.adventnet.zoho.websheet.model.response.beans.ButtonBean) iterator.next();
                if(buttonBean != null){

                    String sheetID = buttonBean.getSheetName();
                    JSONObjectWrapper response = constraintResponseJson.has(sheetID) ? constraintResponseJson.getJSONObject(sheetID) : new JSONObjectWrapper();

                    Sheet sheet =  workbook.getSheetByAssociatedName(sheetID);
                    JSONObjectWrapper buttonResponse = new JSONObjectWrapper();
                    CommandConstants.OperationType operationType = buttonBean.getOperationType();

                    if(operationType == CommandConstants.OperationType.GENERATE_LIST){

                        JSONArrayWrapper buttonMeta = ButtonUtils.getAllButtonDetailsInSheet(container, workbook, sheet);
                        if(!buttonMeta.isEmpty()){
                            buttonResponse.put(Integer.toString(CommandConstants.META), buttonMeta);
                        }

                    } else if (operationType == CommandConstants.OperationType.INSERT || operationType == CommandConstants.OperationType.CLONE) {

                        String buttonID = buttonBean.getID();
                        JSONArrayWrapper buttonMeta = ButtonUtils.getButtonDetailsAsArray(container, sheet, buttonID);
                        if(!buttonMeta.isEmpty()){
                            buttonResponse.put(Integer.toString(CommandConstants.INSERT), buttonMeta);
                        }

                    } else if (operationType == CommandConstants.OperationType.DELETE) {

                        String buttonID = buttonBean.getID();
                        if(buttonID != null){
                            JSONArrayWrapper buttonMeta = new JSONArrayWrapper();
                            buttonMeta.put(buttonID);
                            buttonResponse.put(Integer.toString(CommandConstants.DELETE), buttonMeta);
                        }

                    } else if (operationType == CommandConstants.OperationType.EDIT || operationType == CommandConstants.OperationType.ASSIGNMACRO){

                        String buttonID = buttonBean.getID();
                        JSONArrayWrapper buttonMeta = ButtonUtils.getButtonDetailsAsArray(container, sheet, buttonID);
                        if(!buttonMeta.isEmpty()){
                            buttonResponse.put(Integer.toString(CommandConstants.MODIFY), buttonMeta);
                        }

                    } else if(operationType == CommandConstants.OperationType.MOVE){

                        String buttonID = buttonBean.getID();
                        JSONArrayWrapper buttonMeta = ButtonUtils.getButtonDetailsAsArray(container, sheet, buttonID);
                        if(!buttonMeta.isEmpty()){
                            buttonResponse.put(Integer.toString(CommandConstants.MOVE), buttonMeta);
                        }

                    } else if (operationType == CommandConstants.OperationType.RESIZE) {

                        String buttonID = buttonBean.getID();
                        JSONArrayWrapper buttonMeta = ButtonUtils.getButtonDetailsAsArray(container, sheet, buttonID);
                        if(!buttonMeta.isEmpty()){
                            buttonResponse.put(Integer.toString(CommandConstants.RESIZE), buttonMeta);
                        }
                    }

                    if(!buttonResponse.isEmpty()){
                        response.put(Integer.toString(CommandConstants.BUTTON), buttonResponse);
                    }
                    constraintResponseJson.put(sheetID, response);
                }
            }
        }
    }

    private void updateSparklineInfo(){
        List<com.adventnet.zoho.websheet.model.response.beans.SparklineBean> sparklineBeanList= getConstraintResponseHolder().getSparklineBeanList();
        if(sparklineBeanList == null) {
            return;
        }
        Workbook workbook = getWorkbook();
        JSONObjectWrapper constraintResponseJson = getConstraintResponse();
        for(com.adventnet.zoho.websheet.model.response.beans.SparklineBean sparklineBean : sparklineBeanList) {
            if (sparklineBean == null) {
                continue;
            }
            Collection<String> faultySheetList = sparklineBean.getFaultySheetList();
            if (!faultySheetList.isEmpty()) {
                SparklineResponse.putMetaForFaulty(workbook, faultySheetList, constraintResponseJson);
            }

            boolean isValueChanged = sparklineBean.isValueChanged();
            Sheet actionSheet = workbook.getSheetByAssociatedName(sparklineBean.getAssociatedSheetName());
            if (actionSheet == null) {
                continue;
            }
            JSONObjectWrapper sparklineResponse = constraintResponseJson.has(actionSheet.getAssociatedName()) ? constraintResponseJson.getJSONObject(actionSheet.getAssociatedName()) : new JSONObjectWrapper();
            JSONObjectWrapper rangeJson = sparklineResponse.has(Integer.toString(CommandConstants.SPARKLINE)) ? sparklineResponse.getJSONObject(Integer.toString(CommandConstants.SPARKLINE)) : new JSONObjectWrapper();
            if (sparklineBean.isFromUndoRedo()) {
                SparklineResponse.putMetaForUndo(workbook, sparklineBean, rangeJson);
            } else if (sparklineBean.getOperationType() == CommandConstants.OperationType.GENERATE_LIST) {
                SparklineResponse.putMetaForReload(workbook, constraintResponseJson, actionSheet);
            } else {
                SparklineResponse.getSparklineResponse(workbook, sparklineBean, constraintResponseJson, rangeJson);
            }

            if (!rangeJson.isEmpty()) {
                sparklineResponse.put(Integer.toString(CommandConstants.SPARKLINE), rangeJson);
            }

            if (!sparklineResponse.isEmpty()) {
                constraintResponseJson.put(actionSheet.getAssociatedName(), sparklineResponse);
            }

            List<DataRange> modifiedCellsList = sparklineBean.getModifiedCells();
            if (modifiedCellsList != null && !modifiedCellsList.isEmpty() && sparklineBean.getOperationType() != ADD && isValueChanged) {
                Map<String, JSONArrayWrapper> responseMap = new HashMap<>();
                for (DataRange cellDataRange : modifiedCellsList) {
                    JSONArrayWrapper jArray = new JSONArrayWrapper();
                    JSONArrayWrapper location = new JSONArrayWrapper();
                    location.put(cellDataRange.getStartRowIndex());
                    location.put(cellDataRange.getStartColIndex());
                    location.put(cellDataRange.getAssociatedSheetName());
                    jArray.put(location);
                    JSONArrayWrapper dataArray = responseMap.get(cellDataRange.getAssociatedSheetName());
                    if(dataArray == null) {
                        dataArray = new JSONArrayWrapper();
                        responseMap.put(cellDataRange.getAssociatedSheetName(), dataArray);
                    }
                    dataArray.put(jArray);
                }
                for(Map.Entry<String, JSONArrayWrapper> entry: responseMap.entrySet()) {
                    JSONObjectWrapper sheetResponse = constraintResponseJson.has(entry.getKey()) ? constraintResponseJson.getJSONObject(entry.getKey()) : new JSONObjectWrapper();
                    JSONObjectWrapper featureResponse = sheetResponse.has(Integer.toString(CommandConstants.SPARKLINE)) ? sheetResponse.getJSONObject(Integer.toString(CommandConstants.SPARKLINE)) : new JSONObjectWrapper();
                    JSONArrayWrapper dataArray = entry.getValue();
                    if(featureResponse.has(Integer.toString(CommandConstants.DATA))) {
                        JSONArrayWrapper array = featureResponse.getJSONArray(Integer.toString(CommandConstants.DATA));
                        dataArray = SparklineResponse.mergeJSONArray(array,dataArray);
                    }
                    featureResponse.put(Integer.toString(CommandConstants.DATA), dataArray);
                    sheetResponse.put(Integer.toString(CommandConstants.SPARKLINE), featureResponse);
                    constraintResponseJson.put(entry.getKey(), sheetResponse);
                }

            }

            if((sparklineBean.getOperationType() != ADD && isValueChanged) || sparklineBean.getOperationType() == HIDE ) {
                if(sparklineBean.getModifiedSparklinesGroupList() != null) {
                    for(SparklinesGroup sparklinesGroup: sparklineBean.getModifiedSparklinesGroupList()) {
                        JSONArrayWrapper extremesArray = new JSONArrayWrapper();
                        SparklinesGroup.SparklineProperties properties = sparklinesGroup.getSparklineProperties();
                        if (properties.getVerticalAxesMaximum() == SparklinesGroup.VerticalAxesValue.SAME || properties.getVerticalAxesMinimum() == SparklinesGroup.VerticalAxesValue.SAME) {
                            JSONArrayWrapper jArray = new JSONArrayWrapper();
                            SparklinesGroup.Sparkline sparkline = sparklinesGroup.getSparklinesList().get(0);
                            if(sparkline != null) {
                                DataRange destRange = sparkline.getDestinationRange();
                                String sparklineSheet = destRange.getAssociatedSheetName();
                                JSONObjectWrapper rangeObj = new JSONObjectWrapper();
                                rangeObj.put(JSONConstants.START_ROW, destRange.getStartRowIndex());
                                rangeObj.put(JSONConstants.START_COLUMN, destRange.getStartColIndex());
                                rangeObj.put(JSONConstants.END_ROW, destRange.getEndRowIndex());
                                rangeObj.put(JSONConstants.END_COLUMN, destRange.getEndColIndex());

                                jArray.put(rangeObj);
                                jArray.put(sparklinesGroup.getVerticalAxisMinimum());
                                jArray.put(sparklinesGroup.getVerticalAxisMaximum());
                                jArray.put(sparklinesGroup.getSparklinesGroupID());
                                extremesArray.put(jArray);

                                JSONObjectWrapper sheetResponse = constraintResponseJson.has(sparklineSheet) ? constraintResponseJson.getJSONObject(sparklineSheet) : new JSONObjectWrapper();
                                JSONObjectWrapper featureResponse = sheetResponse.has(Integer.toString(CommandConstants.SPARKLINE)) ? sheetResponse.getJSONObject(Integer.toString(CommandConstants.SPARKLINE)) : new JSONObjectWrapper();

                                if(featureResponse.has(Integer.toString(CommandConstants.EXTREMES))) {
                                    JSONArrayWrapper array = featureResponse.getJSONArray(Integer.toString(CommandConstants.EXTREMES));
                                    extremesArray = SparklineResponse.mergeJSONArray(array,extremesArray);
                                }
                                featureResponse.put(Integer.toString(CommandConstants.EXTREMES), extremesArray);
                                sheetResponse.put(Integer.toString(CommandConstants.SPARKLINE), featureResponse);
                                constraintResponseJson.put(sparklineSheet, sheetResponse);
                            }
                        }
                    }
                }
            }

        }
    }

    private void updateZiaInfo() {
        Map<String,List<String>> ziaInfoList = getConstraintResponseHolder().getZiaInfoList();
        if(ziaInfoList != null && !ziaInfoList.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for (String sheetId: ziaInfoList.keySet()) {
                List<String> sheetHolderList = ziaInfoList.get(sheetId);
                if (sheetHolderList != null && !sheetHolderList.isEmpty()) {
                    JSONObjectWrapper responseObj = constraintResponseJson.has(sheetId) ? constraintResponseJson.getJSONObject(sheetId) : new JSONObjectWrapper();
                    JSONObjectWrapper ziaJson = responseObj.has(Integer.toString(CommandConstants.ZIA)) ? responseObj.getJSONObject(Integer.toString(CommandConstants.ZIA)) : new JSONObjectWrapper();
                    JSONArrayWrapper response = ziaJson.has(Integer.toString(CommandConstants.ZIA_AFFECTED_HOLDERS)) ? ziaJson.getJSONArray(Integer.toString(CommandConstants.ZIA_AFFECTED_HOLDERS)) : new JSONArrayWrapper();
                    for (String holderId : sheetHolderList) {
                        response.put(holderId);
                    }
                    ziaJson.put(Integer.toString(CommandConstants.ZIA_AFFECTED_HOLDERS), response);
                    responseObj.put(Integer.toString(CommandConstants.ZIA), ziaJson);
                    constraintResponseJson.put(sheetId, responseObj);
                }
            }
        }
    }

    private void updateSheetView() {
        List<SheetWrapper> sheetViewList = getConstraintResponseHolder().getSheetViewList();
        if(sheetViewList != null && !sheetViewList.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for(SheetWrapper sheetView : sheetViewList) {
                String asn = sheetView.getAssociatedSheetName();
                Sheet sheet = getWorkbook().getSheetByAssociatedName(asn);
                JSONObjectWrapper sheetViewResponseObject = constraintResponseJson.has(asn) ? constraintResponseJson.getJSONObject(asn) : new JSONObjectWrapper();
                String view = ActionUtil.extractView(sheet).toString();
                sheetViewResponseObject.put(Integer.toString(CommandConstants.SHEET_VIEW), view);
                constraintResponseJson.put(asn, sheetViewResponseObject);
            }
        }
    }

    private void updateSheetRtl() {
        List<SheetWrapper> sheetRtlList = getConstraintResponseHolder().getSheetRtlList();
        if(sheetRtlList != null && !sheetRtlList.isEmpty()) {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for(SheetWrapper sheetRtl : sheetRtlList) {
                String asn = sheetRtl.getAssociatedSheetName();
                Sheet sheet = getWorkbook().getSheetByAssociatedName(asn);
                JSONObjectWrapper sheetRtlResponseObject = constraintResponseJson.has(asn) ? constraintResponseJson.getJSONObject(asn) : new JSONObjectWrapper();
                String tStyle = StyleActionUtil.extractTableStyle(sheet);
                sheetRtlResponseObject.put(Integer.toString(CommandConstants.SHEET_DIRECTION), tStyle);
                constraintResponseJson.put(asn, sheetRtlResponseObject);
            }
        }
    }

    private void updatePublishedView() {
        List<PublishedViewWrapper> publishedViewWrapper = getConstraintResponseHolder().getPublishedViewWrapper();
        if(publishedViewWrapper != null && !publishedViewWrapper.isEmpty()) {
            WorkbookContainer container = getContainer();
            Workbook workbook = getWorkbook();
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for(PublishedViewWrapper publishedView : publishedViewWrapper) {
                try {
                    JSONObjectWrapper json = DocumentUtils.getPublicResourceMeta(container, publishedView.getRangeId());
                    JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(publishedView.getAssociatedSheetName()) ? constraintResponseJson.getJSONObject(publishedView.getAssociatedSheetName()) : new JSONObjectWrapper();
                    sheetObjResponse.put(Integer.toString(CommandConstants.HIDE_HEADERS), json.getBoolean(Integer.toString(CommandConstants.HIDE_HEADERS)));
                    sheetObjResponse.put(Integer.toString(CommandConstants.HIDE_FORMULABAR), json.getBoolean(Integer.toString(CommandConstants.HIDE_FORMULABAR)));
                    constraintResponseJson.put(publishedView.getAssociatedSheetName(), sheetObjResponse);
                }
                catch (Exception e) {
                    LOGGER.info("Exception when getting public meta");
                }
            }
        }
    }

    private void updateSheetZoom() {
        List<SheetWrapper> sheetZoomList = getConstraintResponseHolder().getSheetZoomList();
        if(sheetZoomList != null && !sheetZoomList.isEmpty()) {
            Workbook workbook = getWorkbook();
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            for(SheetWrapper sheetZoom : sheetZoomList) {
                String asn = sheetZoom.getAssociatedSheetName();
                Sheet sheet = workbook.getSheetByAssociatedName(asn);
                JSONObjectWrapper sheetZoomResponseObject = constraintResponseJson.has(asn) ? constraintResponseJson.getJSONObject(asn) : new JSONObjectWrapper();
                WorkbookSettings wbSettings = workbook.getWorkbookSettings();
                int zoomVal = wbSettings.getZoomValue(sheet.getName());
                sheetZoomResponseObject.put(Integer.toString(CommandConstants.ZOOM), zoomVal);
                constraintResponseJson.put(asn, sheetZoomResponseObject);
            }
        }
    }

    private void updateDataCleaningInfo() {
        DataCleaningBean bean = getConstraintResponseHolder().getDataCleaningBean();
        JSONObjectWrapper sheetResponse;
        JSONObjectWrapper dcJson = new JSONObjectWrapper();
        JSONArrayWrapper colArray = new JSONArrayWrapper();

        if(bean == null) {
            return;
        }

        CommandConstants.OperationType operation = bean.getOperationType();
        List<DataRange> modifiedRanges = bean.getModifiedRanges();
        String associatedSheetName = bean.getAssociatedSheetName();
        if(associatedSheetName == null) {
            return;
        }
        JSONObjectWrapper constraintResponseJson = getConstraintResponse();
        sheetResponse = constraintResponseJson.has(associatedSheetName) ? constraintResponseJson.getJSONObject(associatedSheetName) : new JSONObjectWrapper();

        if(!bean.isUndoAction() && operation != CommandConstants.OperationType.MODIFY) {
            if(operation == CommandConstants.OperationType.REPLACE) {
                JSONArrayWrapper replaceArray = DataCleaningUtil.getDCReplaceResponse(modifiedRanges);
                if(!replaceArray.isEmpty()) {
                    dcJson.put(Integer.toString(CommandConstants.DC_HIGHLIGHT), replaceArray);
                    dcJson.put(Integer.toString(CommandConstants.REPLACE_ALL_COUNT), bean.getUpdatedCount());
                }
            }
            else if(operation == CommandConstants.OperationType.DUPLICATES) {
                if (!bean.isUndoAction() && !"highlight".equals(bean.getActionType())) {
                    dcJson.put(Integer.toString(CommandConstants.DATA_DUPLICATES), bean.getDuplicateCount());
                }
            }
        } else {
            if(modifiedRanges != null) {
                for (DataRange range : modifiedRanges) {
                    JSONArrayWrapper array = new JSONArrayWrapper();
                    array.put(range.getStartRowIndex());
                    array.put(range.getStartColIndex());
                    array.put(range.getEndRowIndex());
                    array.put(range.getEndColIndex());

                    colArray.put(array);
                }
            }
            if (!colArray.isEmpty() && sheetResponse != null) {
                dcJson.put(Integer.toString(CommandConstants.DC_AFFECTED_COLS), colArray);
            }
        }

        if(!dcJson.isEmpty()) {
            sheetResponse.put(Integer.toString(CommandConstants.DATACLEANSING),dcJson);
            constraintResponseJson.put(associatedSheetName, sheetResponse);
        }
    }

    private void updatePicklistRangeInfo() {
        List<com.adventnet.zoho.websheet.model.response.beans.PicklistBean> beanList = getConstraintResponseHolder().getPicklistBeanList();
        if(beanList == null) {
            return;
        }
        JSONObjectWrapper constraintResponseJson = getConstraintResponse();
        for(com.adventnet.zoho.websheet.model.response.beans.PicklistBean bean : beanList) {
            if (bean == null) {
                continue;
            }

            JSONObjectWrapper sheetResponse = constraintResponseJson.has(bean.getAssociatedSheetName()) ? constraintResponseJson.getJSONObject(bean.getAssociatedSheetName()) : new JSONObjectWrapper();
            JSONObjectWrapper picklistResponse = new JSONObjectWrapper();

            CommandConstants.OperationType operationType = bean.getOperationType();

            switch (operationType) {
                case ADD:
                case EDIT: {
                    for (Map.Entry<String, List<RangeWrapper>> entry : bean.getRanges().entrySet()) {
                        String sheetAssn = entry.getKey();
                        List<RangeWrapper> ranges = entry.getValue();
                        if (ranges != null) {
                            JSONArrayWrapper rangeListJson = new JSONArrayWrapper();
                            JSONObjectWrapper sResponse = constraintResponseJson.has(sheetAssn) ? constraintResponseJson.getJSONObject(sheetAssn) : new JSONObjectWrapper();
                            JSONObjectWrapper pResponse = sResponse.has(Integer.toString(CommandConstants.PICKLIST)) ? sResponse.getJSONObject(Integer.toString(CommandConstants.PICKLIST)) : new JSONObjectWrapper();
                            for (RangeWrapper range : ranges) {
                                JSONArrayWrapper rangeJson = new JSONArrayWrapper();
                                rangeJson.put(bean.getPicklistID());
                                rangeJson.put(range.getstartRow());
                                rangeJson.put(range.getstartCol());
                                rangeJson.put(range.getEndRow());
                                rangeJson.put(range.getEndCol());
                                rangeListJson.put(rangeJson);
                            }
                            if (!rangeListJson.isEmpty()) {
                                JSONObjectWrapper addJson = new JSONObjectWrapper();
                                addJson.put("id", bean.getPicklistID());
                                addJson.put("ranges", rangeListJson);
                                pResponse.put(Integer.toString(operationType == ADD ? CommandConstants.ADD : CommandConstants.EDIT), addJson);
                                sResponse.put(Integer.toString(CommandConstants.PICKLIST), pResponse);
                                constraintResponseJson.put(sheetAssn, sResponse);
                            }
                        }
                    }

                    break;
                }

                case REMOVE: {
                    for (Map.Entry<String, List<RangeWrapper>> entry : bean.getRanges().entrySet()) {
                        String asn = entry.getKey();
                        JSONObjectWrapper sResponse = constraintResponseJson.has(asn) ? constraintResponseJson.getJSONObject(asn) : new JSONObjectWrapper();
                        JSONObjectWrapper pResponse = new JSONObjectWrapper();
                        pResponse.put(Integer.toString(CommandConstants.REMOVE), bean.getPicklistID());
                        sResponse.put(Integer.toString(CommandConstants.PICKLIST), pResponse);
                        constraintResponseJson.put(asn, sResponse);
                    }

                    break;
                }

                case GENERATE_LIST: {
                    ResponseUtils.setpicklistMeta(getWorkbook(), constraintResponseJson, bean.getAssociatedSheetName());
                    break;
                }

                case DELETE: {
                    for (Map.Entry<String, List<RangeWrapper>> entry : bean.getRanges().entrySet()) {
                        String asn = entry.getKey();
                        JSONObjectWrapper sResponse = constraintResponseJson.has(asn) ? constraintResponseJson.getJSONObject(asn) : new JSONObjectWrapper();
                        JSONObjectWrapper pResponse = sResponse.has(Integer.toString(CommandConstants.PICKLIST)) ? sResponse.getJSONObject(Integer.toString(CommandConstants.PICKLIST)) : new JSONObjectWrapper();
                        JSONArrayWrapper rangeListJson = pResponse.has(Integer.toString(CommandConstants.DELETE)) ? pResponse.getJSONArray(Integer.toString(CommandConstants.DELETE)) : new JSONArrayWrapper();

                        for (RangeWrapper range : entry.getValue()) {
                            JSONArrayWrapper rangeJson = new JSONArrayWrapper();
                            rangeJson.put(range.getstartRow());
                            rangeJson.put(range.getstartCol());
                            rangeJson.put(range.getEndRow());
                            rangeJson.put(range.getEndCol());
                            rangeListJson.put(rangeJson);
                        }

                        pResponse.put(Integer.toString(CommandConstants.DELETE), rangeListJson);
                        sResponse.put(Integer.toString(CommandConstants.PICKLIST), pResponse);
                        constraintResponseJson.put(asn, sResponse);
                    }
                    break;
                }

                case TABLE_EXPANSION:
                    Workbook workbook = getWorkbook();
                    for (Map.Entry<String, List<RangeWrapper>> entry : bean.getRanges().entrySet()) {
                        String asn = entry.getKey();

                        Sheet sheet = workbook.getSheetByAssociatedName(asn);
                        picklistLoop: for(Map.Entry<Integer, List<DataRange>> picklistEntry: sheet.getPicklistRangeMap().entrySet()) {
                            outer: for(DataRange picklistRange: picklistEntry.getValue()) {
                                for(RangeWrapper wrapper: entry.getValue()) {
                                    RangeUtil.SheetRange intersection = RangeUtil.intersection(new RangeUtil.SheetRange(wrapper.getstartRow(), wrapper.getstartCol(), wrapper.getEndRow(), wrapper.getEndCol()), RangeUtil.SheetRange.fromDataRange(picklistRange));
                                    if(intersection != null) {
                                        ResponseUtils.setpicklistMeta(workbook, constraintResponseJson, asn);
                                        break picklistLoop;
                                    }
                                }
                            }
                        }
                    }
                    break;
                default:
            }

            if (!sheetResponse.isEmpty()) {
                constraintResponseJson.put(bean.getAssociatedSheetName(), sheetResponse);
            }
        }

    }

    private void updateRootMergeCellsDataInfo()
    {
        ConstraintResponseHolder constraintResponseHolder = getConstraintResponseHolder();
        List<com.adventnet.zoho.websheet.model.response.beans.CellResponse> rootMergeCellsData = constraintResponseHolder.getRootMergeCellsData();
        Map<String, JSONArrayWrapper> rootMergeCellsCfData = constraintResponseHolder.getRootMergeCellsCfData();
        String sheetName = null;
        JSONObjectWrapper sheetObjResponse = null;
        JSONArrayWrapper cellResponseRowData = null;
        JSONObjectWrapper cellResponseColData = null;
        JSONObjectWrapper constraintResponseJson = getConstraintResponse();
        if(rootMergeCellsData != null && !rootMergeCellsData.isEmpty())
        {
            for(int i = 0 ; i < rootMergeCellsData.size() ; i++)
            {
                Map<String, Object> cellResponseData = (Map<String, Object>) rootMergeCellsData.get(i).getCellResponse();
                if(cellResponseData != null && !cellResponseData.isEmpty())
                {
                    Map<String, JSONArrayWrapper> rowKeyMap = (Map<String, JSONArrayWrapper>) cellResponseData.get("ROWDATA");
                    Map<String, JSONObjectWrapper> colKeyMap = (Map<String, JSONObjectWrapper>) cellResponseData.get("COLDATA");
                    Set<String> keySet = rowKeyMap.keySet();
                    String currSheetName = keySet.iterator().next();
                    JSONObjectWrapper cellconstraintResponseJson = null;
                    JSONObjectWrapper cellResponseMetaJson = null;
                    if(!currSheetName.equals(sheetName))
                    {
                        sheetName = currSheetName;
                        if(constraintResponseJson.has(sheetName))
                        {
                            sheetObjResponse = constraintResponseJson.getJSONObject(sheetName);
                        }
                        else
                        {
                            sheetObjResponse = new JSONObjectWrapper();
                            constraintResponseJson.put(sheetName, sheetObjResponse);
                        }
                        if(sheetObjResponse.has(Integer.toString(CommandConstants.CELL_META)))
                        {
                            cellconstraintResponseJson = sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CELL_META));
                        }
                        else
                        {
                            cellconstraintResponseJson = new JSONObjectWrapper();
                            sheetObjResponse.put(Integer.toString(CommandConstants.CELL_META), cellconstraintResponseJson);
                        }


                        if(cellconstraintResponseJson.has(Integer.toString(CommandConstants.META)))
                        {
                            cellResponseMetaJson = cellconstraintResponseJson.getJSONObject(Integer.toString(CommandConstants.META));
                        }
                        else
                        {
                            cellResponseMetaJson = new JSONObjectWrapper();
                            cellconstraintResponseJson.put(Integer.toString(CommandConstants.META), cellResponseMetaJson);
                        }

                        if(cellResponseMetaJson.has(Integer.toString(CommandConstants.ROW_DATA)))
                        {
                            cellResponseRowData = cellResponseMetaJson.getJSONArray(Integer.toString(CommandConstants.ROW_DATA));
                        }
                        else
                        {
                            cellResponseRowData = new JSONArrayWrapper();
                            cellResponseMetaJson.put(Integer.toString(CommandConstants.ROW_DATA), cellResponseData);
                        }

                        if(cellResponseMetaJson.has(Integer.toString(CommandConstants.COLUMN_DATA)))
                        {
                            cellResponseColData = cellResponseMetaJson.getJSONObject(Integer.toString(CommandConstants.COLUMN_DATA));
                        }
                        else
                        {
                            cellResponseColData = new JSONObjectWrapper();
                            cellResponseMetaJson.put(Integer.toString(CommandConstants.COLUMN_DATA),cellResponseColData);
                        }
                    }


                    JSONArrayWrapper rowDataArray = rowKeyMap.get(currSheetName);
                    Iterator<Object> rowDataIterator = rowDataArray.iterator();
                    while(rowDataIterator.hasNext())
                    {
                        JSONArrayWrapper rowData = (JSONArrayWrapper) rowDataIterator.next();
                        int rowIndex = rowData.getInt(0);
                        Optional<JSONArrayWrapper> existingRowData = Optional.empty();
                        for(int k = 0 ; k < cellResponseRowData.length() ; k++)
                        {
                            JSONArrayWrapper array = cellResponseRowData.getJSONArray(k);
                            if(array.getInt(0) == rowIndex)
                            {
                                existingRowData = Optional.of(array);
                                break;
                            }
                        }
                        if(existingRowData.isPresent())
                        {
                            String colDataKey = existingRowData.get().getString(2);
                            JSONArrayWrapper existingCellData = cellResponseColData.getJSONArray(colDataKey);
                            JSONArrayWrapper rootMergeCellData = colKeyMap.get(currSheetName).getJSONArray(rowData.getString(2));
                            for(int k = 0 ; k < rootMergeCellData.length() ; k++)
                            {
                                existingCellData.put(rootMergeCellData.getJSONArray(k));
                            }
                        }
                        else
                        {
                            String oldColKey = rowData.getString(2);
                            String newColKey = String.valueOf(cellResponseRowData.length());
                            rowData.put(2, newColKey);
                            cellResponseRowData.put(rowData);

                            JSONArrayWrapper rootMergeCellData = colKeyMap.get(currSheetName).getJSONArray(oldColKey);
                            cellResponseColData.put(newColKey, rootMergeCellData);
                        }
                    }
                }
            }
        }

        if(rootMergeCellsCfData != null && !rootMergeCellsCfData.isEmpty())
        {
            sheetName = rootMergeCellsCfData.keySet().iterator().next();
            JSONObjectWrapper cellconstraintResponseJson = null;
            JSONObjectWrapper appliedCfData = null;
            JSONArrayWrapper appliedCfMetaData = null;
            if(constraintResponseJson.has(sheetName))
            {
                sheetObjResponse =  constraintResponseJson.getJSONObject(sheetName);
            }
            else
            {
                sheetObjResponse = new JSONObjectWrapper();
                constraintResponseJson.put(sheetName, sheetObjResponse);
            }
            if(sheetObjResponse.has(Integer.toString(CommandConstants.CELL_META)))
            {
                cellconstraintResponseJson = sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.CELL_META));
            }
            else
            {
                cellconstraintResponseJson = new JSONObjectWrapper();
                sheetObjResponse.put(Integer.toString(CommandConstants.CELL_META), cellconstraintResponseJson);
            }
            if(cellconstraintResponseJson.has(Integer.toString(CommandConstants.APPLIED_COND_STYLE)))
            {
                appliedCfData = cellconstraintResponseJson.getJSONObject(Integer.toString(CommandConstants.APPLIED_COND_STYLE));
            }
            else
            {
                appliedCfData = new JSONObjectWrapper();
                cellconstraintResponseJson.put(Integer.toString(CommandConstants.APPLIED_COND_STYLE), appliedCfData);
            }
            if(appliedCfData.has(Integer.toString(CommandConstants.META)))
            {
                appliedCfMetaData = appliedCfData.getJSONArray(Integer.toString(CommandConstants.META));
            }
            else
            {
                appliedCfMetaData = new JSONArrayWrapper();
                appliedCfData.put(Integer.toString(CommandConstants.META), appliedCfMetaData);
            }

            JSONArrayWrapper cfDataArray = rootMergeCellsCfData.get(sheetName);
            for(int i = 0 ; i < rootMergeCellsCfData.size() ; i++) {
                JSONArrayWrapper cfData  = cfDataArray.getJSONArray(i);
                appliedCfMetaData.put(cfData);
            }
        }
    }
    private void updateDataConnectionInfoForConstraintResponse() {

        com.adventnet.zoho.websheet.model.response.beans.DataConnectionBean dataConnectionBean = getConstraintResponseHolder().getDataConnectionBean();
        if (dataConnectionBean != null) {
            JSONObjectWrapper webdataInfoObj = dataConnectionBean.getWebDataObj();
            if(webdataInfoObj == null)
            {
                return;
            }
            WorkbookContainer container = getContainer();
            Workbook workbook = getWorkbook();
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            if(dataConnectionBean.getOperationType() == DUPLICATES)
            {
                try {
                    webdataInfoObj = ExternalDataUtils.getWebDataInfo(container.getResourceId(), ActionJsonUtil.constructStringList(webdataInfoObj.names()));
                } catch (Exception e) {
                    LOGGER.log(Level.INFO, "Exception Occurred While generating WebData Response", e);
                }
            }

            Iterator<String> keyItr = webdataInfoObj.keys();
            while (keyItr.hasNext()){
                String webdataID = keyItr.next();
                JSONObjectWrapper webbDataInfo = webdataInfoObj.getJSONObject(webdataID);

                String sheetAsn;
                DataRange rangeObj = workbook.getDataConnection().getDataConnectionRange(webdataID);
                if(rangeObj != null) {
                    sheetAsn = rangeObj.getAssociatedSheetName();
                    webbDataInfo.put("sheetId", sheetAsn);
                    webbDataInfo.put("sheetName", workbook.getSheetByAssociatedName(sheetAsn).getName());
                    webbDataInfo.put("startRow", rangeObj.getStartRowIndex());
                    webbDataInfo.put("startCol", rangeObj.getStartColIndex());
                    webbDataInfo.put("endRow", rangeObj.getEndRowIndex());
                    webbDataInfo.put("endCol", rangeObj.getEndColIndex());
                }
                else
                {
                    sheetAsn = webbDataInfo.optString("sheetId", null); //NO I18N
                }

                //webbDataInfo.put(Integer.toString(CommandConstants.ID), webdataID);
                webbDataInfo.put("webDataId", webdataID);
                if(!webbDataInfo.has(Integer.toString(CommandConstants.ACTION_TYPE))) {
                    webbDataInfo.put(Integer.toString(CommandConstants.ACTION_TYPE), dataConnectionBean.getOperationType().toString());
                }
                if(sheetAsn == null)
                {
                    if(webbDataInfo.getInt("liveStatusCode") == DCLiveStatusCode.ACTIVE) {
                        try {
                            LOGGER.log(Level.INFO, "[Data Connection] range null for WebDataID : {0}", new Object[]{webdataID});
                            DBActionManager.updateLiveStatusCode(container.getDocOwner(), webdataID, DCLiveStatusCode.RANGE_NOT_FOUND);
                        } catch (DataAccessException e) {
                            LOGGER.log(Level.INFO, "[Data Connection] Error while changing StatusCode for WebDataID : {0}", new Object[]{webdataID});
                        }
                    }
                    continue;
                }
                JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(sheetAsn) ? constraintResponseJson.getJSONObject(sheetAsn) : new JSONObjectWrapper();
                JSONObjectWrapper dcInfo;
                JSONArrayWrapper webdataInfoArray;
                if(!sheetObjResponse.has(Integer.toString(CommandConstants.DATA_CONNECTION)))
                {
                    dcInfo = new JSONObjectWrapper();
                    dcInfo.put(Integer.toString(CommandConstants.ACTION_TYPE), dataConnectionBean.getOperationType().toString());
                    dcInfo.put(JSONConstants.WEBDATA_ID, dataConnectionBean.getWebDataId());
                    webdataInfoArray = new JSONArrayWrapper();
                }
                else
                {
                    dcInfo = sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.DATA_CONNECTION));
                    webdataInfoArray = dcInfo.getJSONArray(Integer.toString(CommandConstants.META));
                }
                webdataInfoArray.put(webbDataInfo);
                dcInfo.put(Integer.toString(CommandConstants.META), webdataInfoArray);
                sheetObjResponse.put(Integer.toString(CommandConstants.DATA_CONNECTION), dcInfo);
                constraintResponseJson.put(sheetAsn, sheetObjResponse);
            }
        }
    }

    private void updateGroupingInfo()
    {
        List<RangeWrapper> wrapperList = getConstraintResponseHolder().getGroupingRangeWrapper();
        if(wrapperList == null) {
            return;
        }
        Workbook workbook = getWorkbook();
        JSONObjectWrapper constraintResponseJson = getConstraintResponse();
        for(RangeWrapper groupingRangeWrapper: wrapperList) {
            if (groupingRangeWrapper != null) {
                String asn = groupingRangeWrapper.getSheetName();
                Sheet sheet = workbook.getSheetByAssociatedName(asn);
                JSONObjectWrapper sheetResponse = constraintResponseJson.has(asn) ? constraintResponseJson.getJSONObject(asn) : new JSONObjectWrapper();
                if (groupingRangeWrapper.getstartRow() != -1) {
                    JSONObjectWrapper rowGroupsJSON = new JSONObjectWrapper();
                    rowGroupsJSON.put(Integer.toString(CommandConstants.ISSUMMARYBELOW), sheet.isSummaryBelow());
                    if (groupingRangeWrapper.getOperationType() == CommandConstants.OperationType.ADD) {
                        List<List<Group>> rowGroups = sheet.getRowGroups();
                        JSONArrayWrapper allGroupsArray = new JSONArrayWrapper();
                        for (List<Group> groups : rowGroups) {
                            if (!groups.isEmpty()) {
                                JSONArrayWrapper levelArray = new JSONArrayWrapper();
                                for (Group group : groups) {
                                    JSONArrayWrapper array = new JSONArrayWrapper();
                                    array.put(group.getId());
                                    array.put(group.getStartIndex());
                                    array.put(group.getEndIndex());
                                    levelArray.put(array);
                                }
                                allGroupsArray.put(levelArray);
                            }
                        }
                        rowGroupsJSON.put(Integer.toString(CommandConstants.GROUPS), allGroupsArray);
                    }
                    sheetResponse.put(Integer.toString(CommandConstants.ROWGROUPS), rowGroupsJSON);
                }


                if (groupingRangeWrapper.getstartCol() != -1) {
                    JSONObjectWrapper colGroupsJSON = new JSONObjectWrapper();
                    colGroupsJSON.put(Integer.toString(CommandConstants.ISSUMMARYRIGHT), sheet.isSummaryRight());
                    if (groupingRangeWrapper.getOperationType() == CommandConstants.OperationType.ADD) {
                        List<List<Group>> colGroups = sheet.getColGroups();
                        JSONArrayWrapper allGroupsArray = new JSONArrayWrapper();
                        for (List<Group> groups : colGroups) {
                            if (!groups.isEmpty()) {
                                JSONArrayWrapper levelArray = new JSONArrayWrapper();
                                for (Group group : groups) {
                                    JSONArrayWrapper array = new JSONArrayWrapper();
                                    array.put(group.getId());
                                    array.put(group.getStartIndex());
                                    array.put(group.getEndIndex());
                                    levelArray.put(array);
                                }
                                allGroupsArray.put(levelArray);
                            }
                        }
                        colGroupsJSON.put(Integer.toString(CommandConstants.GROUPS), allGroupsArray);
                    }
                    sheetResponse.put(Integer.toString(CommandConstants.COLGROUPS), colGroupsJSON);
                }
                constraintResponseJson.put(asn, sheetResponse);
            }
        }
    }

    private void updateSlicerInfo()
    {
        List<com.adventnet.zoho.websheet.model.response.beans.SlicerBean> slicerBeanList	= getConstraintResponseHolder().getSlicerBeans();
        if(slicerBeanList != null && !slicerBeanList.isEmpty())
        {
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            Workbook workbook = getWorkbook();
            HashSet<String> visitedSlicers = new HashSet<>();
            for(Iterator iterator = slicerBeanList.iterator(); iterator.hasNext();)
            {
                com.adventnet.zoho.websheet.model.response.beans.SlicerBean slicerBean = (com.adventnet.zoho.websheet.model.response.beans.SlicerBean) iterator.next();
                String assSheetName;
                if(slicerBean != null)
                {
                    boolean sendFullResponse = slicerBean.isSendFullResponse();
                    SlicerBean.SlicerResponseType responseType = slicerBean.getResponseType();
                    Set<String> connectedTables = new HashSet<>();
                    assSheetName = slicerBean.getSheetName();
                    String slicerId = slicerBean.getSlicerIDList().isEmpty() ? null : slicerBean.getSlicerIDList().get(0);
                    Slicer slicer = slicerId != null ? workbook.getSlicer(slicerId) : null;
                    JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(assSheetName) ? constraintResponseJson.getJSONObject(assSheetName) : new JSONObjectWrapper();
                    JSONObjectWrapper		slicerResponse =	sheetObjResponse.has(Integer.toString(CommandConstants.SLICER)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.SLICER)) : new JSONObjectWrapper();
                    CommandConstants.OperationType operationType = slicerBean.getOperationType();

                    if(operationType == CommandConstants.OperationType.GENERATE_LIST)
                    {
                        JSONArrayWrapper slicerJSONArray = slicerResponse.has(Integer.toString(CommandConstants.META)) ? slicerResponse.getJSONArray(Integer.toString(CommandConstants.META)) : new JSONArrayWrapper();
                        List<Slicer> slicerList = workbook.getSlicers();
                        for (Slicer curSlicer : slicerList)
                        {
                            if(curSlicer.getPosition().getAssociatedSheetName().equals(assSheetName))
                            {

                                JSONObjectWrapper slicerJSON = new JSONObjectWrapper();
                                ResponseUtils.buildSlicerResponse(workbook, curSlicer, slicerJSON, responseType);
                                if(!slicerJSON.isEmpty()) {
                                    slicerJSONArray.put(slicerJSON);
                                }
                            }
                        }
                        if(!slicerJSONArray.isEmpty())
                        {
                            slicerResponse.put(Integer.toString(CommandConstants.META), slicerJSONArray);
                        }
                    }
                    if(operationType.equals(MODIFY))
                    {
                        if(slicerBean.getPivotNames() != null)
                        {
                            connectedTables.addAll(slicerBean.getPivotNames());
                        }
                        if(slicerId != null)
                        {
                            JSONArrayWrapper slicerJSONArray = slicerResponse.has(Integer.toString(CommandConstants.MODIFY)) ? slicerResponse.getJSONArray(Integer.toString(CommandConstants.MODIFY)) : new JSONArrayWrapper();
                            JSONObjectWrapper slicerJSON = new JSONObjectWrapper();
                            ResponseUtils.buildSlicerResponse(workbook, slicer, slicerJSON, responseType);
                            slicerJSONArray.put(slicerJSON);
                            slicerResponse.put(Integer.toString(CommandConstants.MODIFY), slicerJSONArray);
                            connectedTables.addAll(workbook.getSlicer(slicerId).getConnection().getTablesNames());
                        }
                    }
                    if(operationType.equals(INSERT))
                    {
                        for( int i = 0 ; i < slicerBean.getSlicerIDList().size() ; i++ )
                        {
                            slicerId = slicerBean.getSlicerIDList().get(i); // SidePanel should be opened for last inserted slicer
                            slicer = workbook.getSlicer(slicerId);
                            JSONArrayWrapper slicerJSONArray = slicerResponse.has(Integer.toString(CommandConstants.INSERT)) ? slicerResponse.getJSONArray(Integer.toString(CommandConstants.INSERT)) : new JSONArrayWrapper();

                            JSONObjectWrapper slicerJSON = new JSONObjectWrapper();
                            ResponseUtils.buildSlicerResponse(workbook, slicer, slicerJSON, responseType);
                            slicerJSONArray.put(slicerJSON);
                            slicerResponse.put(Integer.toString(CommandConstants.INSERT), slicerJSONArray);
                            visitedSlicers.add(slicerId);
                            if(sendFullResponse) {
                                connectedTables.addAll(workbook.getSlicer(slicerId).getConnection().getTablesNames());
                            }
                        }

                    }
                    if(operationType.equals(ADD))
                    {
                        if(slicerId != null)
                        {
                            JSONArrayWrapper slicerJSONArray = slicerResponse.has(Integer.toString(CommandConstants.ADD)) ? slicerResponse.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                            JSONObjectWrapper slicerJSON = new JSONObjectWrapper();
                            ResponseUtils.buildSlicerResponse(workbook, slicer, slicerJSON, responseType);
                            slicerJSONArray.put(slicerJSON);
                            slicerResponse.put(Integer.toString(CommandConstants.ADD), slicerJSONArray);
                        }
                    }
                    if(operationType.equals(DELETE))
                    {
                        JSONArrayWrapper slicerJSONArray = slicerResponse.has(Integer.toString(CommandConstants.DELETE)) ? slicerResponse.getJSONArray(Integer.toString(CommandConstants.DELETE)) : new JSONArrayWrapper();
                        List<String> slicerIdList = slicerBean.getSlicerIDList();
                        for(String curSlicerId :slicerIdList)
                        {
                            slicerJSONArray.put(curSlicerId);
                        }
                        slicerResponse.put(Integer.toString(CommandConstants.DELETE), slicerJSONArray);
                        if(slicerBean.getPivotNames() != null) {
                            connectedTables.addAll(slicerBean.getPivotNames());
                        }
                    }
                    if(operationType.equals(MOVE))
                    {
                        JSONArrayWrapper slicerJSONArray = slicerResponse.has(Integer.toString(CommandConstants.MOVE)) ? slicerResponse.getJSONArray(Integer.toString(CommandConstants.MOVE)) : new JSONArrayWrapper();
                        JSONObjectWrapper slicerJSON = new JSONObjectWrapper();
                        ResponseUtils.buildSlicerResponse(workbook, slicer, slicerJSON, responseType);
//                        if(workbook.isPivotSlicer(slicer))
//                        {
//                            SlicerUtil.getPivotSlicerDetails(workbook, slicer, slicerJSON);
//                        }
                        slicerJSONArray.put(slicerJSON);
                        if(!slicerJSONArray.isEmpty())
                        {
                            slicerResponse.put(Integer.toString(CommandConstants.MOVE), slicerJSONArray);
                        }
                    }
                    if(!slicerResponse.isEmpty())
                    {
                        sheetObjResponse.put(Integer.toString(CommandConstants.SLICER), slicerResponse);
                        constraintResponseJson.put(assSheetName, sheetObjResponse);
                    }
                    if(slicerId != null) {
                        visitedSlicers.add(slicerId);
                    }
                    if(sendFullResponse)
                    {
                        ResponseUtils.sendFullSlicerResponse(workbook, constraintResponseJson, sheetObjResponse, slicerResponse, connectedTables, visitedSlicers, assSheetName, responseType);
                    }

                }
            }
        }
    }

    private void updateTimelineInfo(){
        List<com.adventnet.zoho.websheet.model.response.beans.TimelineBean> timelineBeans = getConstraintResponseHolder().getTimelineBeans();
        if(timelineBeans != null && !timelineBeans.isEmpty()){
            JSONObjectWrapper constraintResponseJson = getConstraintResponse();
            Workbook workbook = getWorkbook();
            HashSet<String> visitedTimelines = new HashSet<>();
            for(Iterator iterator = timelineBeans.iterator(); iterator.hasNext();){
                com.adventnet.zoho.websheet.model.response.beans.TimelineBean timelineBean = (com.adventnet.zoho.websheet.model.response.beans.TimelineBean) iterator.next();
                String assSheetName;
                if(timelineBean != null){
                    boolean sendFullResponse = timelineBean.isSendFullResponse();
                    TimelineBean.TimelineResponseType timelineResponseType = timelineBean.getResponseType();
                    Set<String> connectedTables = new HashSet<>();
                    assSheetName = timelineBean.getSheetName();
                    String timelineID = timelineBean.getTimelines().isEmpty() ? null : timelineBean.getTimelines().get(0);
                    TimeLineSlicer timeline = timelineID != null ? workbook.getTimeLineSlicer(timelineID) : null;
                    JSONObjectWrapper sheetObjResponse = constraintResponseJson.has(assSheetName) ? constraintResponseJson.getJSONObject(assSheetName) : new JSONObjectWrapper();
                    JSONObjectWrapper		timelineResponse =	sheetObjResponse.has(Integer.toString(CommandConstants.PIVOT_TIMELINE)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.PIVOT_TIMELINE)) : new JSONObjectWrapper();
                    CommandConstants.OperationType operationType = timelineBean.getOperationType();

                    if(operationType == CommandConstants.OperationType.GENERATE_LIST){
                        JSONArrayWrapper timelineJSONArray = timelineResponse.has(Integer.toString(CommandConstants.META)) ? timelineResponse.getJSONArray(Integer.toString(CommandConstants.META)) : new JSONArrayWrapper();
                        List<TimeLineSlicer> timelines = workbook.getTimelines();
                        for (TimeLineSlicer curTimeline : timelines) {
                            if(curTimeline.getPosition().getAssociatedSheetName().equals(assSheetName)) {

                                JSONObjectWrapper slicerJSON = new JSONObjectWrapper();
                                ResponseUtils.buildTimelineResponse(workbook,curTimeline,slicerJSON,timelineResponseType);
                                if(!slicerJSON.isEmpty()) {
                                    timelineJSONArray.put(slicerJSON);
                                }
                            }
                        }
                        if(!timelineJSONArray.isEmpty()) {
                            timelineResponse.put(Integer.toString(CommandConstants.META), timelineJSONArray);
                        }
                    }
                    if(operationType.equals(MODIFY)){

                        if(timelineBean.getPivotNames() != null){
                            connectedTables.addAll(timelineBean.getPivotNames());
                        }
                        if(timelineID != null) {
                            JSONArrayWrapper timelineJSONArray = timelineResponse.has(Integer.toString(CommandConstants.MODIFY)) ? timelineResponse.getJSONArray(Integer.toString(CommandConstants.MODIFY)) : new JSONArrayWrapper();
                            JSONObjectWrapper timelineJSON = new JSONObjectWrapper();
                            ResponseUtils.buildTimelineResponse(workbook,timeline,timelineJSON,timelineResponseType);

                            timelineJSONArray.put(timelineJSON);
                            timelineResponse.put(Integer.toString(CommandConstants.MODIFY), timelineJSONArray);
                            connectedTables.addAll(workbook.getTimeLineSlicer(timelineID).getConnection().getTablesNames());
                        }
                    }
                    if(operationType.equals(INSERT)){
                        for( int i = 0 ; i < timelineBean.getTimelines().size() ; i++ ) {
                            JSONArrayWrapper timelineJSONArray = timelineResponse.has(Integer.toString(CommandConstants.INSERT)) ? timelineResponse.getJSONArray(Integer.toString(CommandConstants.INSERT)) : new JSONArrayWrapper();
                            JSONObjectWrapper timelineJSON = new JSONObjectWrapper();
                            timelineID = timelineBean.getTimelines().get(i);
                            timeline = workbook.getTimeLineSlicer(timelineID);
                            ResponseUtils.buildTimelineResponse(workbook, timeline, timelineJSON, timelineResponseType);
                            timelineJSONArray.put(timelineJSON);
                            timelineResponse.put(Integer.toString(CommandConstants.INSERT), timelineJSONArray);
                            if (sendFullResponse) {
                                connectedTables.add(workbook.getTimeLineSlicer(timelineID).getConnection().getTablesNames().get(0));
                            }
                            visitedTimelines.add(timelineID);
                        }
                    }
                    if(operationType.equals(ADD)){

                        if(timelineID != null) {
                            JSONArrayWrapper timelineJSONArray = timelineResponse.has(Integer.toString(CommandConstants.ADD)) ? timelineResponse.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                            JSONObjectWrapper timelineJSON = new JSONObjectWrapper();
                            ResponseUtils.buildTimelineResponse(workbook,timeline,timelineJSON,timelineResponseType);
                            timelineJSONArray.put(timelineJSON);
                            timelineResponse.put(Integer.toString(CommandConstants.ADD), timelineJSONArray);
                        }
                    }
                    if(operationType.equals(DELETE)){
                        if(timelineID != null) {
                            JSONArrayWrapper timelineJSONArray = timelineResponse.has(Integer.toString(CommandConstants.DELETE)) ? timelineResponse.getJSONArray(Integer.toString(CommandConstants.DELETE)) : new JSONArrayWrapper();
                            List<String> timelines = timelineBean.getTimelines();
                            for(String curTimelineId :timelines){
                                timelineJSONArray.put(curTimelineId);
                            }
                            timelineResponse.put(Integer.toString(CommandConstants.DELETE), timelineJSONArray);
                            if(timelineBean.getPivotNames() != null) {
                                connectedTables.addAll(timelineBean.getPivotNames()); //since we cannot take from the timeline object as it is deleted.. taking it from the bean.
                            }
                        }
                    }
                    if(operationType.equals(MOVE)){
                        JSONArrayWrapper timelineJSONArray = timelineResponse.has(Integer.toString(CommandConstants.MOVE)) ? timelineResponse.getJSONArray(Integer.toString(CommandConstants.MOVE)) : new JSONArrayWrapper();
                        JSONObjectWrapper timelineJSON = new JSONObjectWrapper();
                        ResponseUtils.buildTimelineResponse(workbook,timeline,timelineJSON,timelineResponseType);
                        timelineJSONArray.put(timelineJSON);
                        if(!timelineJSONArray.isEmpty()) {
                            timelineResponse.put(Integer.toString(CommandConstants.MOVE), timelineJSONArray);
                        }
                    }
                    if(!timelineResponse.isEmpty()) {
                        sheetObjResponse.put(Integer.toString(CommandConstants.PIVOT_TIMELINE), timelineResponse);
                        constraintResponseJson.put(assSheetName, sheetObjResponse);
                    }
                    if(!visitedTimelines.contains(timelineID)){
                        visitedTimelines.add(timelineID);
                    }
                    if(sendFullResponse) {
                        ResponseUtils.sendFullTimelineResponse(workbook,constraintResponseJson,sheetObjResponse,timelineResponse,connectedTables,timelineID,assSheetName, timelineResponseType,visitedTimelines);
                    }

                }
            }

        }

    }

    /** --------------------------------------UserSpecificJsonResponseCreation --------------------------------------------- */


    private void updateProtectedSheets() {

        List<com.adventnet.zoho.websheet.model.response.beans.ProtectionBean> sheetProtectionInfoList = getUserSpecificResponseHolder().getPrtoectedSheetBeans();
        if (sheetProtectionInfoList != null) {

            for (com.adventnet.zoho.websheet.model.response.beans.ProtectionBean protectedSheetInfo : sheetProtectionInfoList) {

                updateProtectedSheetInJson(protectedSheetInfo);
            }
        }
    }

    private void updateProtectedRanges() {

        List<com.adventnet.zoho.websheet.model.response.beans.ProtectionBean> protectionBeans = getUserSpecificResponseHolder().getPrtoectedRangeBeans();
        if (protectionBeans != null) {

            for (com.adventnet.zoho.websheet.model.response.beans.ProtectionBean protectionBean : protectionBeans) {

                updateProtectedRangeInJson(protectionBean);

            }
        }
    }

    private void updateProtectedRangeInJson(com.adventnet.zoho.websheet.model.response.beans.ProtectionBean protectionBean) {

        if (protectionBean != null) {
            Workbook workbook = getWorkbook();
            JSONObjectWrapper userSpecificResponseJson = getUserSpecificResponse();
            CommandConstants.OperationType opType = protectionBean.getOperationType();
            long externalSharedLinkId = protectionBean.getExSharedLinkId();
            String sheetName = protectionBean.getSheetName();
            Sheet sheet = workbook.getSheetByAssociatedName(sheetName);
            if (sheet == null) {
                sheet = workbook.getSheet(sheetName);
            }
            UserInfo userInfo = getUserInfo();
            String zuid = userInfo.getZuid();
            boolean isExternalShareLinkId = false;
            if(externalSharedLinkId != 0l) {
                isExternalShareLinkId = true;
            }
            if (opType == CommandConstants.OperationType.GENERATE_LIST) {

                JSONArrayWrapper protectedRangeAry = ResponseUtils.getProtectedRanges(sheet, userInfo, externalSharedLinkId);

                if (!protectedRangeAry.isEmpty()) {
                    JSONObjectWrapper protectedRange = new JSONObjectWrapper();

                    protectedRange.put(Integer.toString(CommandConstants.META), protectedRangeAry);

                    JSONObjectWrapper protectedRangeJson = new JSONObjectWrapper();
                    protectedRangeJson.put(Integer.toString(CommandConstants.PROTECTED_RANGES), protectedRange);

                    JSONObjectWrapper sheetMeta = new JSONObjectWrapper();
                    sheetMeta.put(sheetName, protectedRangeJson);

                    userSpecificResponseJson.put(Integer.toString(CommandConstants.SHEET_META), sheetMeta);
                }
            } else if (opType == CommandConstants.OperationType.LOCK_RANGE || opType == CommandConstants.OperationType.MODIFY_LOCK_RANGE) {

                Protection protection = RangeUtil.getProtection(sheet, protectionBean.getStartRow(), protectionBean.getStartCol(), protectionBean.getEndRow(), protectionBean.getEndCol());
                boolean isAuthorised = isExternalShareLinkId ? protection.isAuthorizedExternalShareLink(externalSharedLinkId) : protection.isAuthorized(zuid, userInfo.getUserGroups(), false);
                boolean isSheetLocked = sheet.isLocked(zuid, externalSharedLinkId);

                if (!isSheetLocked && (userInfo.getPermissionType() == UserProfile.PermissionType.READ_WRITE_SAVE || userInfo.getPermissionType() == UserProfile.PermissionType.READ_WRITE_SAVE_SHARE || userInfo.getPermissionType() == UserProfile.PermissionType.READ_WRITE_SAVE_SHARE_DELETE)) {

                    JSONObjectWrapper sheetMetaResponse = userSpecificResponseJson.has(Integer.toString(CommandConstants.SHEET_META)) ? userSpecificResponseJson.getJSONObject(Integer.toString(CommandConstants.SHEET_META)) : new JSONObjectWrapper();

                    JSONObjectWrapper sheetObjResponse = sheetMetaResponse.has(protectionBean.getSheetName()) ? sheetMetaResponse.getJSONObject(protectionBean.getSheetName()) : new JSONObjectWrapper();

                    JSONObjectWrapper protectedRanges = sheetObjResponse.has(Integer.toString(CommandConstants.PROTECTED_RANGES)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.PROTECTED_RANGES)) : new JSONObjectWrapper();
//                	protectedRanges.getJ
                    JSONArrayWrapper modifiedRangeAry = protectedRanges.has(Integer.toString(CommandConstants.MODIFY)) ? protectedRanges.getJSONArray(Integer.toString(CommandConstants.MODIFY)) : new JSONArrayWrapper();
                    JSONArrayWrapper lockedRangeAry = new JSONArrayWrapper();
                    lockedRangeAry.put(0, protectionBean.getStartRow());
                    lockedRangeAry.put(1, protectionBean.getStartCol());
                    lockedRangeAry.put(2, protectionBean.getEndRow());
                    lockedRangeAry.put(3, protectionBean.getEndCol());


                    lockedRangeAry.put(4, !isAuthorised ? 1 : 0);
                    lockedRangeAry.put(5, protection.isAllowInsert() ? 1 : 0);
                    lockedRangeAry.put(6, protection.isAllowFormats() ? 1 : 0);

                    modifiedRangeAry.put(lockedRangeAry);

                    protectedRanges.put(Integer.toString(CommandConstants.MODIFY), modifiedRangeAry);

                    sheetObjResponse.put(Integer.toString(CommandConstants.PROTECTED_RANGES), protectedRanges);

                    sheetMetaResponse.put(sheetName, sheetObjResponse);

                    userSpecificResponseJson.put(Integer.toString(CommandConstants.SHEET_META), sheetMetaResponse);
                }

            } else if (opType == CommandConstants.OperationType.UNLOCK_RANGE || opType == CommandConstants.OperationType.REMOVE_RANGE_PROTECTION) {

                boolean isSheetLocked = sheet.isLocked(zuid, externalSharedLinkId);

                if (!isSheetLocked) {

                    JSONObjectWrapper sheetMetaResponse = userSpecificResponseJson.has(Integer.toString(CommandConstants.SHEET_META)) ? userSpecificResponseJson.getJSONObject(Integer.toString(CommandConstants.SHEET_META)) : new JSONObjectWrapper();

                    JSONObjectWrapper sheetObjResponse = sheetMetaResponse.has(protectionBean.getSheetName()) ? sheetMetaResponse.getJSONObject(protectionBean.getSheetName()) : new JSONObjectWrapper();

                    JSONObjectWrapper protectedRanges = sheetObjResponse.has(Integer.toString(CommandConstants.PROTECTED_RANGES)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.PROTECTED_RANGES)) : new JSONObjectWrapper();

                    JSONArrayWrapper removedRangeAry = protectedRanges.has(Integer.toString(CommandConstants.REMOVE)) ? protectedRanges.getJSONArray(Integer.toString(CommandConstants.REMOVE)) : new JSONArrayWrapper();

                    JSONArrayWrapper unLockedRangeAry = new JSONArrayWrapper();
                    unLockedRangeAry.put(0, protectionBean.getStartRow());
                    unLockedRangeAry.put(1, protectionBean.getStartCol());
                    unLockedRangeAry.put(2, protectionBean.getEndRow());
                    unLockedRangeAry.put(3, protectionBean.getEndCol());

                    removedRangeAry.put(unLockedRangeAry);

                    protectedRanges.put(Integer.toString(CommandConstants.REMOVE), removedRangeAry);

                    sheetObjResponse.put(Integer.toString(CommandConstants.PROTECTED_RANGES), protectedRanges);

                    sheetMetaResponse.put(sheetName, sheetObjResponse);

                    userSpecificResponseJson.put(Integer.toString(CommandConstants.SHEET_META), sheetMetaResponse);

                }
            }
        }

    }

    private void updateProtectedSheetInJson(com.adventnet.zoho.websheet.model.response.beans.ProtectionBean protectionBean) {

        if (protectionBean != null) {
            Workbook workbook = getWorkbook();
            JSONObjectWrapper userSpecificResponseJson = getUserSpecificResponse();
            CommandConstants.OperationType opType = protectionBean.getOperationType();
            long externalLinkId = protectionBean.getExSharedLinkId();
            UserInfo userInfo = getUserInfo();
            String id = userInfo.getZuid(); // zuid or externalShared link id
            boolean isExternalShareLinkId = false;
            if(externalLinkId != 0l) {
                isExternalShareLinkId = true;
                id = externalLinkId+"";
            }

            if (opType == CommandConstants.OperationType.GENERATE_LIST) {

                JSONArrayWrapper protectedSheetAry = ResponseUtils.getAllProtectedSheets(workbook, userInfo, externalLinkId);

                if (!protectedSheetAry.isEmpty()) {
                    JSONObjectWrapper protectedJson = new JSONObjectWrapper();
                    protectedJson.put(Integer.toString(CommandConstants.META), protectedSheetAry);
                    userSpecificResponseJson.put(Integer.toString(CommandConstants.PROTECTED_SHEETS), protectedJson);
                }
            } else if (opType == CommandConstants.OperationType.LOCK_SHEET || opType == CommandConstants.OperationType.MODIFY_LOCK_SHEET || opType == CommandConstants.OperationType.CLONE) {

                String sheetName = protectionBean.getSheetName();
                String asn = protectionBean.getAssociatedSheetName();
                Sheet sheet = (asn != null) ? workbook.getSheetByAssociatedName(asn) : workbook.getSheet(sheetName);

                asn = (asn != null) ? asn : sheet.getAssociatedName();

                boolean isSheetLocked = sheet.isLocked(id, externalLinkId);

                if (sheet.isZProtected() && (userInfo.getPermissionType() == UserProfile.PermissionType.READ_WRITE_SAVE || userInfo.getPermissionType() == UserProfile.PermissionType.READ_WRITE_SAVE_SHARE || userInfo.getPermissionType() == UserProfile.PermissionType.READ_WRITE_SAVE_SHARE_DELETE || isSheetLocked)) {

                    JSONObjectWrapper protectedSheets = userSpecificResponseJson.has(Integer.toString(CommandConstants.PROTECTED_SHEETS)) ? userSpecificResponseJson.getJSONObject(Integer.toString(CommandConstants.PROTECTED_SHEETS)) : new JSONObjectWrapper();
                    JSONArrayWrapper protectedSheetsAry = protectedSheets.has(Integer.toString(CommandConstants.MODIFY)) ? protectedSheets.getJSONArray(Integer.toString(CommandConstants.MODIFY)) : new JSONArrayWrapper();

                    JSONArrayWrapper sheetAry = new JSONArrayWrapper();
                    sheetAry.put(0, asn);
                    sheetAry.put(1, isSheetLocked ? 1 : 0);

                    protectedSheetsAry.put(sheetAry);

                    protectedSheets.put(Integer.toString(CommandConstants.MODIFY), protectedSheetsAry);
                    userSpecificResponseJson.put(Integer.toString(CommandConstants.PROTECTED_SHEETS), protectedSheets);

                    //Updating all the locked/protected ranges for unlocked sheet to particular user
                    if (!isSheetLocked) {
                        com.adventnet.zoho.websheet.model.response.beans.ProtectionBean unlockedSheetBean = new com.adventnet.zoho.websheet.model.response.beans.ProtectionBean(asn, -1, -1, -1, -1, CommandConstants.OperationType.GENERATE_LIST, externalLinkId);
                        updateProtectedRangeInJson(unlockedSheetBean);
                    }
                }

            } else if (opType == CommandConstants.OperationType.UNLOCK_SHEET || opType == CommandConstants.OperationType.REMOVE_SHEET_PROTECTION) {

                String asn = protectionBean.getAssociatedSheetName();

                String sheetName = protectionBean.getSheetName();
                Sheet sheet = (asn != null) ? workbook.getSheetByAssociatedName(asn) : workbook.getSheet(sheetName);
                asn = (asn != null) ? asn : sheet.getAssociatedName();

                JSONObjectWrapper protectedSheets = userSpecificResponseJson.has(Integer.toString(CommandConstants.PROTECTED_SHEETS)) ? userSpecificResponseJson.getJSONObject(Integer.toString(CommandConstants.PROTECTED_SHEETS)) : new JSONObjectWrapper();
                JSONArrayWrapper protectedSheetsAry = userSpecificResponseJson.has(Integer.toString(CommandConstants.REMOVE)) ? userSpecificResponseJson.getJSONArray(Integer.toString(CommandConstants.REMOVE)) : new JSONArrayWrapper();

                JSONArrayWrapper sheetAry = new JSONArrayWrapper();
                sheetAry.put(0, asn);

                protectedSheetsAry.put(sheetAry);

                protectedSheets.put(Integer.toString(CommandConstants.REMOVE), protectedSheetsAry);

                userSpecificResponseJson.put(Integer.toString(CommandConstants.PROTECTED_SHEETS), protectedSheets);

                //Updating all the locked/protected ranges for unprotected sheet to particular user
                com.adventnet.zoho.websheet.model.response.beans.ProtectionBean lockedSheetBean = new com.adventnet.zoho.websheet.model.response.beans.ProtectionBean(asn, -1, -1, -1, -1, CommandConstants.OperationType.GENERATE_LIST, externalLinkId);
                updateProtectedRangeInJson(lockedSheetBean);

            }
        }
    }

    public void updateDataValidationErrorStatus() {

        DataValidationMessageInfo dataValidationMessageInfo = getUserSpecificResponseHolder().getdataValidationMessageInfo();

        if (dataValidationMessageInfo != null) {

            boolean isDatapermitted = dataValidationMessageInfo.isPermittedData();

            JSONObjectWrapper dataValidation = new JSONObjectWrapper();
            dataValidation.put(Integer.toString(CommandConstants.DATAVALIDATION), isDatapermitted ? 1 : 0);

            getUserSpecificResponse().put(Integer.toString(CommandConstants.ERROR), dataValidation);

        }

    }

    private void updateServerClipObject() {
        JSONObjectWrapper serverClipObject = getUserSpecificResponseHolder().getServerClipObj();
        if (serverClipObject != null) {
            getUserSpecificResponse().put(Integer.toString(CommandConstants.SERVERCLIP), serverClipObject);
        }
    }

    private void updateChartClipObject() {
        JSONObjectWrapper serverChartClipObject = getUserSpecificResponseHolder().getChartStyleClipObj();

        ChartEngineManager.getChartEngine(getWorkbook())
                .updateChartClipObject(getUserSpecificResponse(), serverChartClipObject);
    }

    private void updateReplaceAllCount() {

        com.adventnet.zoho.websheet.model.response.beans.ActionIdentifierBean actionIdentifierBean = getUserSpecificResponseHolder().getActionIdentifierBean();
        //if (actionIdentifierBean != null && actionIdentifierBean.getRsid() != null && actionIdentifierBean.getRsid().equals(userInfo.getClientId())) {
        if (actionIdentifierBean != null && actionIdentifierBean.getUtid() != null && actionIdentifierBean.getUtid().equals(getUserInfo().getUtid())) {
            com.adventnet.zoho.websheet.model.response.beans.FindReplaceResponseBean findReplaceResponseBean = getUserSpecificResponseHolder().getFindReplaceResponseBean();
            JSONObjectWrapper userSpecificResponseJson = getUserSpecificResponse();
            if(findReplaceResponseBean != null) {
                if(findReplaceResponseBean.isIncludeReplaceCountOnly()){
                    if(findReplaceResponseBean.getNoOfReplaces() != -1) {
                        userSpecificResponseJson.put(Integer.toString(CommandConstants.REPLACE_COUNT), findReplaceResponseBean.getNoOfReplaces());
                    }
                }
                else{
                    userSpecificResponseJson.put(Integer.toString(CommandConstants.FIND_ALL_COUNT), findReplaceResponseBean.getNoOfMatches());
                    userSpecificResponseJson.put(Integer.toString(CommandConstants.MATCH_AT), findReplaceResponseBean.getMatchAt());
                    if(findReplaceResponseBean.getNoOfReplaces() != -1) {
                        userSpecificResponseJson.put(Integer.toString(CommandConstants.REPLACE_ALL_COUNT), findReplaceResponseBean.getNoOfReplaces());
                    }
                }
            }
        }
    }

    private void updateRangeHighlight() {

        com.adventnet.zoho.websheet.model.response.beans.ActionIdentifierBean actionIdentifierBean = getUserSpecificResponseHolder().getActionIdentifierBean();

        //if (actionIdentifierBean != null && actionIdentifierBean.getRsid() != null && actionIdentifierBean.getRsid().equals(userInfo.getClientId())) {
        if(actionIdentifierBean != null && actionIdentifierBean.getUtid() != null && actionIdentifierBean.getUtid().equals(getUserInfo().getUtid())) {
            List<RangeWrapper> rangehighlightRanges = getUserSpecificResponseHolder().getRangeHighlight();
            JSONObjectWrapper userSpecificResponseJson = getUserSpecificResponse();
            if (rangehighlightRanges != null && !rangehighlightRanges.isEmpty()) {

                for (Iterator iterator = rangehighlightRanges.iterator(); iterator.hasNext();) {

                    RangeWrapper rangeWrapper = (RangeWrapper) iterator.next();
                    JSONObjectWrapper sheetMetaResponse = userSpecificResponseJson.has(Integer.toString(CommandConstants.SHEET_META)) ? userSpecificResponseJson.getJSONObject(Integer.toString(CommandConstants.SHEET_META)) : new JSONObjectWrapper();
                    String sheetID = rangeWrapper.getSheetName();
                    JSONObjectWrapper sheetResponse = sheetMetaResponse.has(sheetID) ? sheetMetaResponse.getJSONObject(sheetID) : new JSONObjectWrapper();

                    JSONArrayWrapper ranges = sheetResponse.has(Integer.toString(CommandConstants.RANGE_HIGHLIGHT)) ? sheetResponse.getJSONArray(Integer.toString(CommandConstants.RANGE_HIGHLIGHT)) : new JSONArrayWrapper();
                    JSONArrayWrapper range = new JSONArrayWrapper();

                    range.put(rangeWrapper.getstartRow());
                    range.put(rangeWrapper.getstartCol());
                    range.put(rangeWrapper.getEndRow());
                    range.put(rangeWrapper.getEndCol());
                    range.put(rangeWrapper.isScrollIntoView());

                    ranges.put(range);

                    sheetResponse.put(Integer.toString(CommandConstants.RANGE_HIGHLIGHT), ranges);
                    sheetMetaResponse.put(sheetID, sheetResponse);
                    userSpecificResponseJson.put(Integer.toString(CommandConstants.SHEET_META), sheetMetaResponse);
                }
            }

//                    RangeWrapper    rangeWrapper = userSpecificResponseHolder.getRangeHighlight();
//
//                    if(rangeWrapper != null){
//
//                        JSONObjectWrapper sheetMetaResponse = this.userSpecificResponseJson.has(Integer.toString(CommandConstants.SHEET_META)) ? this.userSpecificResponseJson.getJSONObject(Integer.toString(CommandConstants.SHEET_META)) : new JSONObjectWrapper();
//                        String sheetID = rangeWrapper.getSheetName();
//                        JSONObjectWrapper sheetResponse = sheetMetaResponse.has(sheetID) ? sheetMetaResponse.getJSONObject(sheetID) : new JSONObjectWrapper();
//
//                        JSONArrayWrapper ranges = sheetResponse.has(Integer.toString(CommandConstants.RANGE_HIGHLIGHT)) ? sheetResponse.getJSONArray(Integer.toString(CommandConstants.RANGE_HIGHLIGHT)) : new JSONArrayWrapper();
//                        JSONArrayWrapper range = new JSONArrayWrapper();
//
//                        range.put(rangeWrapper.getstartRow());
//                        range.put(rangeWrapper.getstartCol());
//                        range.put(rangeWrapper.getEndRow());
//                        range.put(rangeWrapper.getEndCol());
//
//                        ranges.put(range);
//
//                        sheetResponse.put(Integer.toString(CommandConstants.RANGE_HIGHLIGHT), ranges);
//                        sheetMetaResponse.put(sheetID, sheetResponse);
//                        this.userSpecificResponseJson.put(Integer.toString(CommandConstants.SHEET_META), sheetMetaResponse);
//
//                    }
        }
    }

    private void updateUserNotification() {

        com.adventnet.zoho.websheet.model.response.beans.ActionIdentifierBean actionIdentifierBean = getUserSpecificResponseHolder().getActionIdentifierBean();

        //if (actionIdentifierBean != null && actionIdentifierBean.getRsid() != null && actionIdentifierBean.getRsid().equals(userInfo.getClientId())) {
        if(actionIdentifierBean != null && actionIdentifierBean.getUtid() != null && actionIdentifierBean.getUtid().equals(getUserInfo().getUtid())) {
            List<com.adventnet.zoho.websheet.model.response.beans.NotificationBean> notificationBeans = getUserSpecificResponseHolder().getUserNotification();
            if (notificationBeans != null) {
                JSONArrayWrapper userNotificationArray = new JSONArrayWrapper();
                for (com.adventnet.zoho.websheet.model.response.beans.NotificationBean notificationBean : notificationBeans) {
                    JSONArrayWrapper userNotification = new JSONArrayWrapper();
                    userNotification.put(notificationBean.getType());
                    userNotification.put(notificationBean.getContent());
                    userNotificationArray.put(userNotification);
                }

                getUserSpecificResponse().put(Integer.toString(CommandConstants.USER_NOTIFICATION), userNotificationArray);
            }
        }
    }
    private void updateErrorNotification() {
        com.adventnet.zoho.websheet.model.response.beans.ActionIdentifierBean actionIdentifierBean = getUserSpecificResponseHolder().getActionIdentifierBean();
        if(actionIdentifierBean != null && actionIdentifierBean.getUtid() != null && actionIdentifierBean.getUtid().equals(getUserInfo().getUtid())) {
            com.adventnet.zoho.websheet.model.response.beans.ErrorBean errorBean = getUserSpecificResponseHolder().getErrorBean();
            JSONObjectWrapper userSpecificResponseJson = getUserSpecificResponse();
            if (errorBean != null) {
                int commandConst = errorBean.getCommandConstants();
                JSONArrayWrapper errorNotificationArray = new JSONArrayWrapper();
                errorNotificationArray.put(errorBean.getErrorObj());
                //errorNotificationArray.put(errorBean.getErrorMsg());
    			/*for (NotificationBean notificationBean : notificationBeans) {
    				JSONArrayWrapper userNotification = new JSONArrayWrapper();
    				userNotification.put(notificationBean.getType());
    				userNotification.put(notificationBean.getContent());
    				userNotificationArray.put(userNotification);
    			}*/
                JSONObjectWrapper errorObj = new JSONObjectWrapper();

                if(commandConst == CommandConstants.ALL){
                    userSpecificResponseJson.put(Integer.toString(CommandConstants.ERRORMSG), errorBean.getErrorObj());
                }else{
                    errorObj.put(Integer.toString(commandConst), errorNotificationArray);
                    userSpecificResponseJson.put(Integer.toString(CommandConstants.ERROR), errorObj);
                }
            }
        }
    }

    private void updateFilteredRowCount() {
        UserSpecificResponseHolder userSpecificResponseHolder = getUserSpecificResponseHolder();
        com.adventnet.zoho.websheet.model.response.beans.ActionIdentifierBean actionIdentifierBean = userSpecificResponseHolder.getActionIdentifierBean();
        if(actionIdentifierBean != null && actionIdentifierBean.getUtid() != null && actionIdentifierBean.getUtid().equals(getUserInfo().getUtid())) {
            if(userSpecificResponseHolder.getFilterdRowCount() != -1){
                getUserSpecificResponse().put(Integer.toString(CommandConstants.FILTERED_ROW_COUNT), userSpecificResponseHolder.getFilterdRowCount());
            }
        }
    }

    private void updateNewFilterDetails()
    {
        UserInfo userInfo = getUserInfo();
        String zuid = userInfo.getZuid();
        Map<String, List<FilterBean>> filterBeanMap = getUserSpecificResponseHolder().getFilterBeans();
        JSONObjectWrapper userSpecificResponseJson = getUserSpecificResponse();
        JSONArrayWrapper filterAry = new JSONArrayWrapper();
        for(Map.Entry<String, List<FilterBean>> entrySet : filterBeanMap.entrySet())
        {
            String asn = entrySet.getKey();
            Sheet sheet = getWorkbook().getSheetByAssociatedName(asn);
            List<FilterBean> filterBeans = entrySet.getValue();
            if(filterBeans == null) // DATA RESPONSE
            {
                for(FilterView filterView : sheet.getAllFilterViews(zuid))
                {
                    JSONObjectWrapper filterJSON = getFilterJSON(filterView);
                    filterAry.put(filterJSON);
                }
            }
            else
            {
                for(FilterBean filterBean : filterBeans)
                {
                    // USER SPECIFIC RESPONSE GENERATION
                    if(!filterBean.getZuid().equals(userInfo.getZuid()))
                    {
                        continue;
                    }
                    FilterView view = sheet.getFilterView(filterBean.getFilterViewID());
                    JSONObjectWrapper filterJson = new JSONObjectWrapper();
                    filterJson.put(Integer.toString(CommandConstants.ASSOCIATED_SHEET_NAME), asn);
                    filterJson.put(Integer.toString(CommandConstants.FILTER_VIEW_ID), filterBean.getFilterViewID());
                    if(view != null && !view.isEmptyFilterView())
                    {
                        Table table = view.getTable();
                        filterJson.put(Integer.toString(CommandConstants.ID), table.getID());
                        if(filterBean.isFilterCreated() || filterBean.isFilterRangeUpdated())
                        {
                            JSONArrayWrapper filterAppliedCol = FilterUtil.getFilterCriteriaAppliedColumnIndexes(view);
                            if(filterAppliedCol != null)
                            {
                                filterJson.put(Integer.toString(CommandConstants.FILTER_CRITERIA_APPLIED_COLS), filterAppliedCol);
                            }
                        }
                        if(filterBean.isCriteriaTouched() && !filterBean.isFilterRemoved()) // client need FilterRange to update FilteredRows in clientSide
                        {
                            int startRow = table.getStartRowIndex();
                            int startCol = table.getStartColIndex();
                            int endRow = table.getEndRowIndex();
                            int endCol = table.getEndColIndex();

                            JSONArrayWrapper filterRangeAry = new JSONArrayWrapper();
                            filterRangeAry.put(0, startRow);
                            filterRangeAry.put(1, startCol);
                            filterRangeAry.put(2, endRow);
                            filterRangeAry.put(3, endCol);

                            filterJson.put(Integer.toString(CommandConstants.FILTER_RANGE), filterRangeAry);
                        }
                        if (filterBean.isFilterAttribute())
                        {
                            filterJson.put(Integer.toString(CommandConstants.DISPLAY_FILTER_BUTTONS), view.isDisplayFilterButtons());
                        }
                        boolean toggleFilter = filterBean.isFilterRemoved();
                        filterJson.put(Integer.toString(CommandConstants.TOGGLE_FILTER), toggleFilter);
                    }
                    else
                    {
                        boolean toggleFilter = filterBean.isFilterRemoved();
                        filterJson.put(Integer.toString(CommandConstants.TOGGLE_FILTER), toggleFilter);
                    }
                    filterAry.put(filterJson);
                }
            }

            JSONObjectWrapper sheetMetaResponse = userSpecificResponseJson.has(Integer.toString(CommandConstants.SHEET_META)) ? userSpecificResponseJson.getJSONObject(Integer.toString(CommandConstants.SHEET_META)) : new JSONObjectWrapper();
            JSONObjectWrapper sheetObjResponse = sheetMetaResponse.has(asn) ? sheetMetaResponse.getJSONObject(asn) : new JSONObjectWrapper();
            if(!filterAry.isEmpty())
            {
                sheetObjResponse.put(Integer.toString(CommandConstants.NEW_FILTER_DETAILS), filterAry);
            }
            sheetMetaResponse.put(asn, sheetObjResponse);
            userSpecificResponseJson.put(Integer.toString(CommandConstants.SHEET_META), sheetMetaResponse);
        }
    }

    private void updateFilteredRowsAndCount()
    {
        UserSpecificResponseHolder userSpecificResponseHolder = getUserSpecificResponseHolder();
        Map<String, List<FilterBean>> fiterBeanMap = userSpecificResponseHolder.getFilterBeans();
        JSONObjectWrapper userSpecificResponseJson = getUserSpecificResponse();
        UserInfo userInfo = getUserInfo();
        for(Map.Entry<String, List<FilterBean>> entrySet : fiterBeanMap.entrySet())
        {
            String asn = entrySet.getKey();
            Sheet sheet = getWorkbook().getSheetByAssociatedName(asn);
            List<FilterBean> filterBeans = entrySet.getValue();
            JSONArrayWrapper filteredRowArray = new JSONArrayWrapper();
            JSONObjectWrapper sheetMetaResponse = userSpecificResponseJson.has(Integer.toString(CommandConstants.SHEET_META)) ? userSpecificResponseJson.getJSONObject(Integer.toString(CommandConstants.SHEET_META)) : new JSONObjectWrapper();
            JSONObjectWrapper sheetObjResponse = sheetMetaResponse.has(asn) ? sheetMetaResponse.getJSONObject(asn) : new JSONObjectWrapper();
            if(filterBeans == null)
            {
                List<DataRange> filterRanges = new ArrayList<>();
                UserFilterViews userFilterViews = sheet.getUserFilterViews();
                Collection<FilterView> filterViews = userFilterViews != null ? userFilterViews.getFilterViews().values() : new ArrayList<>();
                for(FilterView view : filterViews)
                {
                    if(view != null && !view.isEmptyFilterView() && view.getFilter() != null)
                    {
                        DataRange filterRange = view.getDataRange();
                        DataRange rowRange = new DataRange(filterRange.getAssociatedSheetName(), filterRange.getStartRowIndex(), 0, filterRange.getEndRowIndex(), 0);
                        RangeUtil.mergeAndAddDataRangeToList(rowRange, filterRanges);
                    }
                }
                for(DataRange filterRange : filterRanges)
                {
                    ResponseUtils.getFilteredRows(sheet, filterRange.getStartRowIndex(), filterRange.getEndRowIndex(), filteredRowArray);
                }
                sheetObjResponse.put(Integer.toString(CommandConstants.FILTERED_ROWS), filteredRowArray);
            }
            else
            {
                List<DataRange> filterRanges = new ArrayList<>();
                for(FilterBean filterBean : filterBeans)
                {
                    if(!filterBean.getZuid().equals(userInfo.getZuid()) || filterBean.isFilterRemoved())
                    {
                        continue;
                    }
                    FilterView view = sheet.getFilterView(filterBean.getFilterViewID());
                    if(view != null && !view.isEmptyFilterView())
                    {
                        if (filterBean.isCriteriaTouched())
                        {
                            DataRange filterRange = view.getDataRange();
                            DataRange rowRange = new DataRange(filterRange.getAssociatedSheetName(), filterRange.getStartRowIndex(), 0, filterRange.getEndRowIndex(), 0);
                            RangeUtil.mergeAndAddDataRangeToList(rowRange, filterRanges);
                        }
                    }
                }
                for(DataRange filterRange : filterRanges)
                {
                    ResponseUtils.getFilteredRows(sheet, filterRange.getStartRowIndex(), filterRange.getEndRowIndex(), filteredRowArray);
                }
                if(!filterRanges.isEmpty())
                {
                    sheetObjResponse.put(Integer.toString(CommandConstants.FILTERED_ROWS), filteredRowArray);
                }
            }
            int filteredRowCount = userSpecificResponseHolder.getFilterdRowCount();
            if(filteredRowCount != -1)
            {
                sheetObjResponse.put(Integer.toString(CommandConstants.FILTERED_ROW_COUNT), filteredRowCount);
            }
            sheetMetaResponse.put(asn, sheetObjResponse);
            userSpecificResponseJson.put(Integer.toString(CommandConstants.SHEET_META), sheetMetaResponse);
        }
    }

    private void updateSheetFilterTableInfo() {
        List<com.adventnet.zoho.websheet.model.response.beans.TableBean> beanList = getDocumentResponseHolder().getTableBeanList();
        if(beanList == null) {
            return;
        }
        Workbook workbook = getWorkbook();
        JSONObjectWrapper tableResponse = new JSONObjectWrapper();
        JSONArrayWrapper tableExpansionArray = new JSONArrayWrapper();
        for(com.adventnet.zoho.websheet.model.response.beans.TableBean bean : beanList)
        {

            CommandConstants.OperationType operation = bean.getOperation();
            if(operation == null) {
                continue;
            }



            switch(operation)
            {
                case GENERATE_LIST:
                    JSONArrayWrapper tableListJson = new JSONArrayWrapper();
                    if(bean.getSheetFilterTableIdMap() != null)
                    {
                        for (Map.Entry<String, Set<Integer>> entry : bean.getSheetFilterTableIdMap().entrySet()) {
                            String asn = entry.getKey();
                            Set<Integer> tableIds = entry.getValue();
                            Sheet sheet = workbook.getSheetByAssociatedName(asn);
                            FilterView filterView = sheet.getFilterView();
                            try {
                                if (filterView != null && tableIds.contains(filterView.getTable().getID())) {
                                    tableListJson.put(ResponseUtils.getTableJson(workbook, filterView.getTable()));
                                }
                            } catch (Exception e) {
                                LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Error while getting table json, error: ", e);
                            }
                        }
                        tableResponse.put(Integer.toString(CommandConstants.META), tableListJson);
                    }

                    break;


                case MODIFY:
                case ADD:
                case HIDE:
                    int comConst = operation == ADD ? CommandConstants.ADD: CommandConstants.MODIFY;
                    JSONArrayWrapper array = tableResponse.has(Integer.toString(comConst)) ? tableResponse.getJSONArray(Integer.toString(comConst)) : new JSONArrayWrapper();
                    if(bean.getSheetFilterTableIdMap() != null)
                    {
                        for (Map.Entry<String, Set<Integer>> entry : bean.getSheetFilterTableIdMap().entrySet())
                        {
                            String asn = entry.getKey();
                            Set<Integer> tableIds = entry.getValue();
                            Sheet sheet = workbook.getSheetByAssociatedName(asn);
                            FilterView filterView = sheet.getFilterView();
                            try {
                                if (filterView != null && tableIds.contains(filterView.getTable().getID())) {
                                    array.put(ResponseUtils.getTableJson(workbook, filterView.getTable()));
                                }
                            } catch (Exception e) {
                                LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Error while getting table json, error: ", e);
                            }
                        }
                        if(!array.isEmpty()) {
                            tableResponse.put(Integer.toString(comConst),array);
                        }
                    }

                    break;

                case IMPORT_SHEET_INSERT:
                case ADD_SHEET:
                    if(bean.getSheetFilterTableIdMap() != null)
                    {
                        array = tableResponse.has(Integer.toString(CommandConstants.ADD)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                        for (Map.Entry<String, Set<Integer>> entry : bean.getSheetFilterTableIdMap().entrySet())
                        {
                            String asn = entry.getKey();
                            Set<Integer> tableIds = entry.getValue();
                            Sheet sheet = workbook.getSheetByAssociatedName(asn);
                            FilterView filterView = sheet.getFilterView();
                            try {
                                if (filterView != null && tableIds.contains(filterView.getTable().getID())) {
                                    array.put(ResponseUtils.getTableJson(workbook, filterView.getTable()));
                                }
                            } catch (Exception e) {
                                LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Error while getting table json, error: ", e);
                            }
                        }

                        if(!array.isEmpty()) {
                            tableResponse.put(Integer.toString(CommandConstants.ADD),array);
                        }
                    }
                    break;

                case PASTE_WITH_STYLE:
                case SHIFT:
                    array = tableResponse.has(Integer.toString(CommandConstants.MODIFY)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.MODIFY)) : new JSONArrayWrapper();

                    if(bean.getSheetFilterTableIdMap() != null)
                    {
                        array = tableResponse.has(Integer.toString(CommandConstants.ADD)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.ADD)) : new JSONArrayWrapper();
                        for (Map.Entry<String, Set<Integer>> entry : bean.getSheetFilterTableIdMap().entrySet())
                        {
                            String asn = entry.getKey();
                            Set<Integer> tableIds = entry.getValue();
                            Sheet sheet = workbook.getSheetByAssociatedName(asn);
                            FilterView filterView = sheet.getFilterView();
                            try {
                                if (filterView != null && tableIds.contains(filterView.getTable().getID())) {
                                    array.put(ResponseUtils.getTableJson(workbook, filterView.getTable()));
                                }
                            } catch (Exception e) {
                                LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Error while getting table json, error: ", e);
                            }
                        }
                        if(!array.isEmpty()) {
                            tableResponse.put(Integer.toString(CommandConstants.MODIFY),array);
                        }
                    }
                    break;

                case PASTE:
                    if(bean.getSheetFilterTableIdMap() != null)
                    {
                        tableListJson = new JSONArrayWrapper();
                        for (Map.Entry<String, Set<Integer>> entry : bean.getSheetFilterTableIdMap().entrySet())
                        {
                            String asn = entry.getKey();
                            Set<Integer> tableIds = entry.getValue();
                            Sheet sheet = workbook.getSheetByAssociatedName(asn);
                            FilterView filterView = sheet.getFilterView();
                            try {
                                if (filterView != null && tableIds.contains(filterView.getTable().getID())) {
                                    tableListJson.put(ResponseUtils.getTableJson(workbook, filterView.getTable()));
                                }
                            } catch (Exception e) {
                                LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Error while getting table json, error: ", e);
                            }
                        }

                        tableResponse.put(Integer.toString(CommandConstants.META),tableListJson);
                    }
                    break;

                case INSERT:
                    if(bean.getSheetFilterTableIdMap() != null)
                    {
                        for (Map.Entry<String, Set<Integer>> entry : bean.getSheetFilterTableIdMap().entrySet())
                        {
                            String asn = entry.getKey();
                            Set<Integer> tableIds = entry.getValue();
                            Sheet sheet = workbook.getSheetByAssociatedName(asn);
                            FilterView filterView = sheet.getFilterView();
                            try {
                                if (filterView != null && tableIds.contains(filterView.getTable().getID()))
                                {
                                    Table table = filterView.getTable();
                                    tableExpansionArray = tableResponse.has(Integer.toString(CommandConstants.TABLE_EXPANSION)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.TABLE_EXPANSION)) : new JSONArrayWrapper();
                                    JSONObjectWrapper jobj = new JSONObjectWrapper();
                                    jobj.put(Integer.toString(CommandConstants.ID), table.getID());
                                    jobj.put(Integer.toString(CommandConstants.START_ROW), table.getStartRowIndex()).put(Integer.toString(CommandConstants.START_COL), table.getStartColIndex());
                                    jobj.put(Integer.toString(CommandConstants.END_ROW), table.getEndRowIndex()).put(Integer.toString(CommandConstants.END_COL), table.getEndColIndex());
                                    tableExpansionArray.put(jobj);
                                }
                            } catch (Exception e) {
                                LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Error while getting table json, error: ", e);
                            }
                        }
                    }
            }
            if(!bean.getExpandedTables().isEmpty())
            {
                if(bean.getSheetFilterTableIdMap() != null)
                {
                    for (Map.Entry<String, Set<Integer>> entry : bean.getSheetFilterTableIdMap().entrySet())
                    {
                        String asn = entry.getKey();
                        Set<Integer> tableIds = entry.getValue();
                        Sheet sheet = workbook.getSheetByAssociatedName(asn);
                        FilterView filterView = sheet.getFilterView();
                        try {
                            if (filterView != null && tableIds.contains(filterView.getTable().getID()))
                            {
                                Table table = filterView.getTable();
                                tableExpansionArray = tableResponse.has(Integer.toString(CommandConstants.TABLE_EXPANSION)) ? tableResponse.getJSONArray(Integer.toString(CommandConstants.TABLE_EXPANSION)) : new JSONArrayWrapper();
                                JSONObjectWrapper jobj = new JSONObjectWrapper();
                                jobj.put(Integer.toString(CommandConstants.ID), table.getID());
                                jobj.put(Integer.toString(CommandConstants.START_ROW), table.getStartRowIndex()).put(Integer.toString(CommandConstants.START_COL), table.getStartColIndex());
                                jobj.put(Integer.toString(CommandConstants.END_ROW), table.getEndRowIndex()).put(Integer.toString(CommandConstants.END_COL), table.getEndColIndex());
                                tableExpansionArray.put(jobj);
                            }
                        } catch (Exception e) {
                            LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Error while getting table json, error: ", e);
                        }
                    }
                }
            }
        }

        if (!tableExpansionArray.isEmpty() && !tableResponse.has(Integer.toString(CommandConstants.META))) {
            tableResponse.put(Integer.toString(CommandConstants.TABLE_EXPANSION), tableExpansionArray);
        }

        if(!tableResponse.isEmpty()) {
            getUserSpecificResponse().put(Integer.toString(CommandConstants.TABLE), tableResponse);
        }
    }


    /*
     * Adding this Multi window support for iOS13, so adding by default(It's mandatory for client )
     * Use case :--
     * 1. Same spreadsheet can be opened in multiple window
     * 2. we are adding this to differentiate the response in client
     * */
    private void updateReceiverUTID(){

        com.adventnet.zoho.websheet.model.response.beans.ActionIdentifierBean actionIdentifierBean =getUserSpecificResponseHolder().getActionIdentifierBean();
        if(actionIdentifierBean != null){
            UserInfo userInfo = getUserInfo();
            if (userInfo.getUtid() != null) {
                getUserSpecificResponse().put(Integer.toString(CommandConstants.RESPONSE_RECEIVER_UTID),userInfo.getUtid());
            }
        }
    }

    private JSONObjectWrapper getFilterJSON( FilterView filterView)
    {
        JSONObjectWrapper filterJson = new JSONObjectWrapper();
        Table table = filterView.getTable();
        filterJson.put(Integer.toString(CommandConstants.FILTER_VIEW_ID), filterView.getId());
        if(table != null)   // EMPTY FILTER VIEW
        {
            int startRow = table.getStartRowIndex();
            int startCol = table.getStartColIndex();
            int endRow = table.getEndRowIndex();
            int endCol = table.getEndColIndex();

            JSONArrayWrapper filterRangeAry = new JSONArrayWrapper();
            filterRangeAry.put(startRow);
            filterRangeAry.put(startCol);
            filterRangeAry.put(endRow);
            filterRangeAry.put(endCol);

            filterJson.put(Integer.toString(CommandConstants.FILTER_RANGE), filterRangeAry);
            filterJson.put(Integer.toString(CommandConstants.ID), table.getID());
        }
        JSONArrayWrapper filterAppliedCol = FilterUtil.getFilterCriteriaAppliedColumnIndexes(filterView);
        if(!filterAppliedCol.isEmpty())
        {
            filterJson.put(Integer.toString(CommandConstants.FILTER_CRITERIA_APPLIED_COLS), filterAppliedCol);
        }
        filterJson.put(Integer.toString(CommandConstants.DISPLAY_FILTER_BUTTONS), filterView.isDisplayFilterButtons());

        return filterJson;
    }


}
