package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.SheetRtlInfoExtractor;
import com.adventnet.zoho.websheet.model.response.helper.SheetWrapper;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;

public class SheetRtlInfoExtractorImpl implements SheetRtlInfoExtractor {

    List<SheetWrapper> sheetWrapperList = new ArrayList<>();
    public SheetRtlInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        SheetWrapper sheetWrapper;
        int action = (actionJson != null && actionJson.has(JSONConstants.ACTION)) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        List<String> asnSheetList = ActionJsonUtil.getListOfAssociateSheetNames(actionJson, false);
        switch(action) {
            case -1:
            case ActionConstants.DOCUMENT_SETTING:
                CommandConstants.OperationType opType = CommandConstants.OperationType.GENERATE_LIST;
                String as = actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                sheetWrapper = new SheetWrapper(as, opType);
                sheetWrapperList.add(sheetWrapper);
                break;
            case ActionConstants.SAVE_RTL_SHEET:
                for(String asn : asnSheetList) {
                    sheetWrapper = new SheetWrapper(asn, CommandConstants.OperationType.GENERATE_LIST);
                    sheetWrapperList.add(sheetWrapper);
                }
                break;
        }
    }

    @Override
    public List<SheetWrapper> getSheetWrapperList() {
        return sheetWrapperList;
    }
}
