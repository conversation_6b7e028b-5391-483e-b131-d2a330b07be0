package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.TableBean;
import com.adventnet.zoho.websheet.model.response.extractor.TableInfoExtractor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.*;

public class TableInfoExtractorImpl implements TableInfoExtractor {

    List<TableBean> tableBeanList = new ArrayList<>();
    public TableInfoExtractorImpl(JSONObjectWrapper actionJson, TableBean tableBean, MacroResponse macroResponse)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        String associatedSheetName = (actionJson.has(JSONConstants.ASSOCIATED_SHEET_NAME)) ? actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME) :  ActionJsonUtil.getAssociateSheetName(actionJson);
        boolean	isUndoAction =	(actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO));
        boolean isRedoAction = (actionJson.has(JSONConstants.FROM_REDO) && actionJson.getBoolean(JSONConstants.FROM_REDO));
        CommandConstants.OperationType operation = null;
        int tableId = actionJson.optInt(JSONConstants.TABLE_ID, -1);

        if(tableBean != null) {
            tableBean = tableBean.cloneBean();
        }
        else {
            /* For fetch data case. */
            tableBean = new TableBean();
        }

        switch(action) {
            case -1:
            case ActionConstants.APPLY_THEME:
                operation = CommandConstants.OperationType.GENERATE_LIST;
                break;
            case ActionConstants.FILTER_BY_VALUE:
            case ActionConstants.FILTER_BY_FONT_COLOR:
            case ActionConstants.FILTER_BY_CELL_COLOR:
            case ActionConstants.CREATE_FILTER:
            case ActionConstants.MAP_FILTER_VIEW:
            case ActionConstants.SET_DISPLAY_FILTER_BUTTONS:
                // SEND TABLE INFO ONLY FOR SHEET FILTER
                if(tableBean.isSheetFilterView())
                {
                    operation = CommandConstants.OperationType.ADD;
                    for(com.google.common.collect.Table.Cell<Integer,String,List<RangeUtil.SheetRange>> entry: tableBean.getModifiedRangesMap().cellSet())
                    {
                        if(tableId != entry.getRowKey())
                        {
                            TableBean removeBean = new TableBean(CommandConstants.OperationType.REMOVE, associatedSheetName);
                            removeBean.addModifiedRange(entry.getRowKey(), associatedSheetName, entry.getValue().get(0));
                            tableBeanList.add(removeBean);
                        }
                    }
                }
                else
                {
                    if(!tableBean.getHeaderChanged().isEmpty())
                    {
                        operation = CommandConstants.OperationType.MODIFY;
                    }
                }
                if((isUndoAction || isRedoAction) && actionJson.has(JSONConstants.TABLE) && !actionJson.getJSONArray(JSONConstants.TABLE).isEmpty()) // while doing FilterByValue like actions, if that action created or deleted a table means, we will have that tableInfo in actionJson. So If the action affected the table means, we need to send TableInfo.
                {
                    operation = CommandConstants.OperationType.GENERATE_LIST ;
                }
                break;
            case ActionConstants.REMOVE_FILTER:
                // SEND TABLE INFO ONLY FOR SHEET FILTER
                if(tableBean.isSheetFilterView())
                {
                    operation = CommandConstants.OperationType.REMOVE;
                }
                if(isUndoAction || isRedoAction)
                {
                    operation = isUndoAction ? CommandConstants.OperationType.ADD : CommandConstants.OperationType.REMOVE;
                }
                break;
            case ActionConstants.CLEARALL:
            case ActionConstants.CLEARCONTENTS:
            case ActionConstants.TABLE_REMOVE:
                operation = isUndoAction ? CommandConstants.OperationType.ADD :CommandConstants.OperationType.REMOVE;
                break;
            case ActionConstants.TABLE_CREATE:
            case ActionConstants.PIVOT_SHOW_DETAILS:
                if(isUndoAction) {
                    operation = CommandConstants.OperationType.REMOVE;
                }
                else {
                    operation = CommandConstants.OperationType.ADD;
                    if(actionJson.optBoolean(JSONConstants.TABLE_INSERT_CELLS, false)) {
                        TableBean shiftBean = new TableBean();
                        List<DataRange> dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                        shiftBean.setOperation(CommandConstants.OperationType.MODIFY);
                        shiftBean.setAssociatedSheetName(associatedSheetName);
                        for(DataRange dataRange: dataRanges) {
                            shiftBean.addModifiedRange(-1, dataRange.getAssociatedSheetName(), new RangeUtil.SheetRange(dataRange.getEndRowIndex()+1 , dataRange.getStartColIndex(), Utility.MAXNUMOFROWS-1, dataRange.getEndColIndex()));
                        }
                        tableBeanList.add(shiftBean);
                    }
                }
                if(actionJson.has(JSONConstants.IS_SHEET_FILTER_TABLE_CONVERT))
                {
                    operation = CommandConstants.OperationType.GENERATE_LIST;

                }
                break;

            case ActionConstants.TABLE_REMOVE_CUSTOM_STYLE:
                TableBean removeStyleBean = new TableBean();
                removeStyleBean.setOperation(CommandConstants.OperationType.REMOVE_STYLE);
                removeStyleBean.setAssociatedSheetName(associatedSheetName);
                removeStyleBean.addRemovedStyle(actionJson.getString(JSONConstants.STYLE_NAME));
                tableBeanList.add(removeStyleBean);

                removeStyleBean = new TableBean();
                removeStyleBean.setOperation(CommandConstants.OperationType.CHANGE_DEFAULT);
                removeStyleBean.setAssociatedSheetName(associatedSheetName);
                tableBeanList.add(removeStyleBean);

                operation = CommandConstants.OperationType.MODIFY;
                break;
            case ActionConstants.TABLE_STYLE_CHANGE:
            case ActionConstants.TABLE_CLEAR_STYLE:
            case ActionConstants.TABLE_PROPERTIES_CHANGE:
                operation = CommandConstants.OperationType.MODIFY;
                break;
            case ActionConstants.TABLE_TOGGLE_HEADER_FOOTER:
                operation = CommandConstants.OperationType.HIDE;
                break;

            case ActionConstants.SHEET_DUPLICATE:
                operation = CommandConstants.OperationType.ADD_SHEET;
                tableBean.addSheetsForMeta(actionJson.getString(JSONConstants.NEW_SHEET_NAME));
                break;
            case ActionConstants.IMPORT_INSERT_AS_NEW_SHEETS:
                operation = CommandConstants.OperationType.IMPORT_SHEET_INSERT;
                JSONArrayWrapper sheetNames = actionJson.has(JSONConstants.PASTED_SHEETNAMES) ? actionJson.getJSONArray(JSONConstants.PASTED_SHEETNAMES) : new JSONArrayWrapper();
                for(int i=0 ;i<sheetNames.length(); i++) {
                    tableBean.addSheetsForMeta(sheetNames.getString(i));
                }
                break;
            case ActionConstants.SERVERCLIP_PASTE_SHEET:
                operation = CommandConstants.OperationType.ADD_SHEET;
                JSONArrayWrapper jArray = actionJson.getJSONArray(JSONConstants.PASTED_SHEETNAMES);
                for(int i=0; i< jArray.length(); i++) {
                    tableBean.addSheetsForMeta(jArray.getString(i));
                }
                break;


            case ActionConstants.INSERT_CUT_ROW:
            case ActionConstants.INSERT_CUT_COLUMN:
                List<DataRange> dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
                for(DataRange range: dataRanges) {
                    int endRow = range.getEndRowIndex();
                    int endCol = range.getEndColIndex();
                    if(action == ActionConstants.INSERT_CUT_ROW) {
                        endRow = Utility.MAXNUMOFROWS-1;
                    }
                    else {
                        endCol = Utility.MAXNUMOFCOLS-1;
                    }
                    tableBean.addModifiedRange(0, range.getAssociatedSheetName(), new RangeUtil.SheetRange(range.getStartRowIndex(), range.getStartColIndex(), endRow, endCol));
                    if(action == ActionConstants.INSERT_CUT_COLUMN) {
                        TableBean headerChangeBean = new TableBean();
                        headerChangeBean.setOperation(CommandConstants.OperationType.HEADER_CHANGE);
                        headerChangeBean.setAssociatedSheetName(associatedSheetName);
                        headerChangeBean.addModifiedRange(-1, range.getAssociatedSheetName(), new RangeUtil.SheetRange(0, Math.max(range.getStartColIndex()-1,0), Utility.MAXNUMOFROWS-1, range.getEndColIndex()));
                        tableBeanList.add(headerChangeBean);
                    }
                }
            case ActionConstants.INSERT_COPY_ROW:
            case ActionConstants.INSERT_COPY_COLUMN:
            case ActionConstants.INSERT_ROW:
            case ActionConstants.INSERT_COL:
                dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                for(DataRange range: dataRanges) {
                    int startRow = range.getStartRowIndex();
                    int startCol = range.getStartColIndex();
                    int endRow = range.getEndRowIndex();
                    int endCol = range.getEndColIndex();

                    if(action == ActionConstants.INSERT_ROW || action == ActionConstants.INSERT_CUT_ROW || action == ActionConstants.INSERT_COPY_ROW) {
                        startCol = 0;
                        endCol = Utility.MAXNUMOFCOLS-1;
                    }
                    else {
                        startRow = 0;
                        endRow = Utility.MAXNUMOFROWS-1;
                    }

                    TableBean addBean = new TableBean();
                    addBean.setOperation(CommandConstants.OperationType.ADD);
                    addBean.setAssociatedSheetName(associatedSheetName);
                    addBean.addModifiedRange(-1, range.getAssociatedSheetName(), new RangeUtil.SheetRange(startRow, startCol, endRow, endCol));
                    tableBeanList.add(addBean);

                    if(action == ActionConstants.INSERT_COPY_COLUMN || action == ActionConstants.INSERT_COL || action == ActionConstants.INSERT_CUT_COLUMN) {
                        TableBean headerChangeBean = new TableBean();
                        headerChangeBean.setOperation(CommandConstants.OperationType.HEADER_CHANGE);
                        headerChangeBean.setAssociatedSheetName(associatedSheetName);
                        headerChangeBean.addModifiedRange(-1, range.getAssociatedSheetName(), new RangeUtil.SheetRange(startRow, startCol, endRow, endCol));
                        tableBeanList.add(headerChangeBean);
                    }

                    if(action == ActionConstants.INSERT_ROW || action == ActionConstants.INSERT_CUT_ROW || action == ActionConstants.INSERT_COPY_ROW) {
                        endRow = Utility.MAXNUMOFROWS-1;
                    }
                    else {
                        endCol = Utility.MAXNUMOFCOLS-1;
                    }
                    tableBean.addModifiedRange(0, range.getAssociatedSheetName(), new RangeUtil.SheetRange(startRow, startCol, endRow, endCol));
                }
                operation = CommandConstants.OperationType.INSERT;

                if(actionJson.has(JSONConstants.REMOVED_FILTER_TABLES)) {
                    JSONArrayWrapper removedTables = actionJson.getJSONArray(JSONConstants.REMOVED_FILTER_TABLES);
                    TableBean removeBean = new TableBean();
                    removeBean.setOperation(CommandConstants.OperationType.REMOVE);
                    removeBean.setAssociatedSheetName(associatedSheetName);

                    for(int i=0; i<removedTables.length(); i++) {
                        int removedTable = removedTables.getInt(i);
                        removeBean.addModifiedRange(removedTable, associatedSheetName, new RangeUtil.SheetRange(-1, -1, -1, -1));
                    }
                    tableBeanList.add(removeBean);
                }
                break;

            case ActionConstants.DELETE_ROW:
            case ActionConstants.DELETE_COL:
                dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                for(DataRange range: dataRanges) {
                    int startRow = range.getStartRowIndex();
                    int startCol = range.getStartColIndex();
                    int endRow = range.getEndRowIndex();
                    int endCol = range.getEndColIndex();

                    if(action == ActionConstants.DELETE_ROW) {
                        startCol = 0;
                        endCol = Utility.MAXNUMOFCOLS-1;
                    }
                    else {
                        startRow = 0;
                        endRow = Utility.MAXNUMOFROWS-1;
                    }

                    TableBean deleteBean = new TableBean();
                    deleteBean.setOperation(CommandConstants.OperationType.DELETE);
                    deleteBean.setAssociatedSheetName(associatedSheetName);
                    deleteBean.addModifiedRange(-1, range.getAssociatedSheetName(), new RangeUtil.SheetRange(startRow, startCol, endRow, endCol));
                    tableBeanList.add(deleteBean);

                    if(action == ActionConstants.DELETE_COL) {
                        TableBean headerChangeBean = new TableBean();
                        headerChangeBean.setOperation(CommandConstants.OperationType.HEADER_CHANGE);
                        headerChangeBean.setAssociatedSheetName(associatedSheetName);
                        headerChangeBean.addModifiedRange(-1, range.getAssociatedSheetName(), new RangeUtil.SheetRange(startRow, Math.max(startCol-1,0), endRow, endCol));
                        tableBeanList.add(headerChangeBean);
                    }

                    if(action == ActionConstants.DELETE_ROW) {
                        endRow = Utility.MAXNUMOFROWS-1;
                    }
                    else {
                        endCol = Utility.MAXNUMOFCOLS-1;
                    }
                    tableBean.addModifiedRange(0, range.getAssociatedSheetName(), new RangeUtil.SheetRange(Math.max(startRow-1,0), Math.max(startCol-1,0), endRow, endCol));
                }
                operation = CommandConstants.OperationType.INSERT;
                break;

            case ActionConstants.SYSTEMCLIP_PASTE:
            case ActionConstants.TEXTTOCOLUMNS_APPLY:
                List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                if(dataRange != null) {
                    /*This is needed, since the table might expand using insertCells, so we need to update the position of the tables below this table. */
                    TableBean insertBean = new TableBean();
                    insertBean.setOperation(CommandConstants.OperationType.INSERT);
                    insertBean.setAssociatedSheetName(associatedSheetName);
                    for(DataRange drange: dataRange) {
                        insertBean.addModifiedRange(0,drange.getAssociatedSheetName(), new RangeUtil.SheetRange(drange.getStartRowIndex(), drange.getStartColIndex(), Utility.MAXNUMOFROWS-1, drange.getEndColIndex()));
                    }
                    tableBeanList.add(insertBean);
                }
            case ActionConstants.COPY_PASTE:
                if(isUndoAction) {
                    operation = CommandConstants.OperationType.PASTE;
                    dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
                }
                else {
                    operation = CommandConstants.OperationType.PASTE_WITH_STYLE;
                    dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                }
                if(dataRange != null) {
                    for(DataRange drange: dataRange) {
                        tableBean.addModifiedRange(0,drange.getAssociatedSheetName(), new RangeUtil.SheetRange(drange.getStartRowIndex(), drange.getStartColIndex(), drange.getEndRowIndex(), drange.getEndColIndex()));
                    }
                }
                break;
            case ActionConstants.CUT_PASTE:
                operation = CommandConstants.OperationType.SHIFT;
                List<DataRange> deleteRanges = null;
                if(isUndoAction) {
                    dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
                    deleteRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                }
                else {
                    dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                    deleteRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
                }
                if(dataRange != null) {
                    for(DataRange drange: dataRange) {
                        tableBean.addModifiedRange(0,drange.getAssociatedSheetName(), new RangeUtil.SheetRange(drange.getStartRowIndex(), drange.getStartColIndex(), drange.getEndRowIndex(), drange.getEndColIndex()));
                    }
                }
                if(deleteRanges != null) {
                    TableBean deleteBean = new TableBean();
                    deleteBean.setOperation(CommandConstants.OperationType.DELETE);
                    deleteBean.setAssociatedSheetName(associatedSheetName);
                    for(DataRange drange: deleteRanges) {
                        deleteBean.addModifiedRange(0,drange.getAssociatedSheetName(), new RangeUtil.SheetRange(drange.getStartRowIndex(), drange.getStartColIndex(), drange.getEndRowIndex(), drange.getEndColIndex()));
                    }
                    tableBeanList.add(deleteBean);
                }
                break;
            case ActionConstants.UPDATE_CLOUD_DATA:
            case ActionConstants.IMPORT_CLOUD_DATA:
//                int rowDiff = actionJson.optInt("rowDiff"); // No I18N
//                int colDiff = actionJson.optInt("colDiff"); // No I18N
//
//                if(rowDiff != 0 || colDiff != 0) {
//                    dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
//                    if (dataRange != null) {
//                        for (DataRange drange : dataRange) {
//                            int eC = drange.getEndColIndex();
//                            int eR = drange.getEndRowIndex();
//                            int sR = drange.getStartRowIndex();
//                            int sC = drange.getStartColIndex();
//                            if (colDiff != 0) {
//                                eC = Utility.MAXNUMOFCOLS;
//                            }
//                            if(rowDiff != 0){
//                                eR = Utility.MAXNUMOFROWS;
//                            }
//                            tableBean.addModifiedRange(0, drange.getAssociatedSheetName(), new RangeUtil.SheetRange(sR, sC, eR, eC));
//                        }
//                    }
//                    operation = CommandConstants.OperationType.SHIFT;
//                }
                operation = CommandConstants.OperationType.GENERATE_LIST;
                break;
            case ActionConstants.INSERT_CELL_LEFT:
            case ActionConstants.INSERT_CELL_TOP:
            case ActionConstants.DELETE_CELL_BOTTOM:
            case ActionConstants.DELETE_CELL_RIGHT:
            case ActionConstants.TABLE_INSERT_ROW:
            case ActionConstants.TABLE_INSERT_COL:
            case ActionConstants.TABLE_DELETE_ROW:
            case ActionConstants.TABLE_DELETE_COL:
                dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                if(dataRange != null) {
                    for (DataRange drange : dataRange) {
                        int eC = drange.getEndColIndex();
                        int eR = drange.getEndRowIndex();
                        int sR = drange.getStartRowIndex();
                        int sC = drange.getStartColIndex();
                        if (action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.TABLE_INSERT_COL || action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.TABLE_DELETE_COL) {
                            eC = Utility.MAXNUMOFCOLS;
                        } else {
                            eR = Utility.MAXNUMOFROWS;
                        }
                        tableBean.addModifiedRange(0,drange.getAssociatedSheetName(), new RangeUtil.SheetRange(sR, sC, eR, eC));
                    }
                }
                operation = CommandConstants.OperationType.SHIFT;
                break;

            case ActionConstants.IMPORT_REPLACE_CURRENTSHEET:
                operation = CommandConstants.OperationType.DELETE;
                tableBean.addModifiedRange(0, associatedSheetName, new RangeUtil.SheetRange(0,0,Utility.MAXNUMOFROWS-1, Utility.MAXNUMOFCOLS-1));
                TableBean addBean = new TableBean();
                addBean.addSheetsForMeta(associatedSheetName);
                addBean.setOperation(CommandConstants.OperationType.IMPORT_SHEET_INSERT);
                addBean.setAssociatedSheetName(associatedSheetName);
                tableBeanList.add(addBean);
                break;
            case ActionConstants.SHEET_REMOVE:
                operation = CommandConstants.OperationType.DELETE;
                tableBean.addModifiedRange(0, associatedSheetName, new RangeUtil.SheetRange(0,0,Utility.MAXNUMOFROWS-1, Utility.MAXNUMOFCOLS-1));
                break;
            case ActionConstants.TABLE_SET_AS_DEFAULT:
                operation = CommandConstants.OperationType.CHANGE_DEFAULT;
                break;
            case ActionConstants.CLEARSTYLES:
                dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                if(dataRanges != null) {
                    for(DataRange range: dataRanges) {
                        tableBean.addModifiedRange(-1, range.getAssociatedSheetName(), new RangeUtil.SheetRange(range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex()));
                    }
                }
                operation = CommandConstants.OperationType.MODIFY;
                break;
            case ActionConstants.REPLACE:
                dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                if(dataRanges != null) {
                    for(DataRange range: dataRanges) {
                        tableBean.addModifiedRange(-1, range.getAssociatedSheetName(), new RangeUtil.SheetRange(range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex()));
                    }
                }
                operation = CommandConstants.OperationType.REPLACE;
                break;
            case ActionConstants.REPLACE_ALL:
                dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                /* This is just needed for ReplaceAll from Macro and DataAPI*/
                if(dataRanges != null && !dataRanges.isEmpty() && (tableBean.getHeaderChanged().isEmpty() && !actionJson.has(JSONConstants.TABLE_HEADERS))) {
                    DataRange searchRange = dataRanges.get(0);
                    int searchIn = actionJson.optInt(JSONConstants.TRAVERSE_MODE, -1);
                    switch (searchIn) {
                        case ActionConstants.FIND_IN_WORKBOOK:
                            operation = CommandConstants.OperationType.REPLACE_SPREADSHEET;
                            break;
                        case ActionConstants.FIND_IN_SHEET:
                            tableBean.addModifiedRange(-1, searchRange.getAssociatedSheetName(), new RangeUtil.SheetRange(0,0,Utility.MAXNUMOFROWS-1, Utility.MAXNUMOFCOLS-1));
                            operation = CommandConstants.OperationType.REPLACE;
                            break;
                        case ActionConstants.FIND_IN_RANGE:
                            tableBean.addModifiedRange(-1, searchRange.getAssociatedSheetName(), new RangeUtil.SheetRange(searchRange.getStartRowIndex(), searchRange.getStartColIndex(), searchRange.getEndRowIndex(), searchRange.getEndColIndex()));
                            operation = CommandConstants.OperationType.REPLACE;
                            break;
                        case ActionConstants.FIND_IN_ROW:
                            tableBean.addModifiedRange(-1, searchRange.getAssociatedSheetName(), new RangeUtil.SheetRange(searchRange.getStartRowIndex(), 0, searchRange.getEndRowIndex(), Utility.MAXNUMOFCOLS-1));
                            operation = CommandConstants.OperationType.REPLACE;
                            break;
                        case ActionConstants.FIND_IN_COL:
                            tableBean.addModifiedRange(-1, searchRange.getAssociatedSheetName(), new RangeUtil.SheetRange(0, searchRange.getStartColIndex(), Utility.MAXNUMOFROWS-1, searchRange.getEndColIndex()));
                            operation = CommandConstants.OperationType.REPLACE;
                            break;

                    }
                }
                break;
            case ActionConstants.MACRO_RUN:
            case ActionConstants.MACRO_SAVE_RUN:
                if(macroResponse != null && (!macroResponse.isEmpty() || !macroResponse.getNewTables().isEmpty() || !macroResponse.getRemovedTables().isEmpty())) {
                    handleMacroResponse(macroResponse, tableBean);
                }
                operation = CommandConstants.OperationType.INSERT;
                break;
            //TODO : Check and remove the default case. Mention each action explicitly in the switch.
            default:
                operation = CommandConstants.OperationType.EXPAND;
                break;
        }
        if(isUndoAction || isRedoAction) {
            if(actionJson.has(JSONConstants.TABLE) && !actionJson.getJSONArray(JSONConstants.TABLE).isEmpty()) {
                JSONArrayWrapper modifiedJson = actionJson.getJSONArray("table"); //No I18N
                for(int i=0; i<modifiedJson.length(); i++) {
                    JSONArrayWrapper array = modifiedJson.getJSONArray(i);
                    tableBean.addModifiedRange(array.getInt(0),array.getString(1), new RangeUtil.SheetRange(array.getInt(2),array.getInt(3),array.getInt(4),array.getInt(5)));
                }
            }
            if(actionJson.has(JSONConstants.EXPANDED_TABLES)) {
                JSONArrayWrapper expandedArray = actionJson.getJSONArray(JSONConstants.EXPANDED_TABLES);
                tableBean.resetExpandedTables();
                for(int i=0; i<expandedArray.length(); i++) {
                    JSONArrayWrapper arr = expandedArray.getJSONArray(i);
                    tableBean.setExpansion(arr.getInt(0));
                }
            }
            if(actionJson.has(JSONConstants.TABLE_HEADERS)) {
                JSONArrayWrapper headersChanged = actionJson.getJSONArray(JSONConstants.TABLE_HEADERS);
                for(int i=0; i< headersChanged.length(); i++) {
                    JSONArrayWrapper arr = headersChanged.getJSONArray(i);
                    tableBean.addHeaderChanged(arr.getInt(0), true);
                }
            }
        }

        else if(!tableBean.getTablesExpandByInsertCells().isEmpty())
        {
            operation = CommandConstants.OperationType.GENERATE_LIST;
        }

        if(actionJson.has(JSONConstants.REMOVED_FILTER_TABLES) && isUndoAction) {
            tableBean.resetBean();
            operation = CommandConstants.OperationType.GENERATE_LIST;
        }

        if(tableBean.isResendMeta() || actionJson.has(JSONConstants.TABLE_RESEND)) {
            tableBean.resetBean();
            if(isUndoAction) {
                operation = CommandConstants.OperationType.GENERATE_LIST;
            }
            else {
                List<DataRange> dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                for(DataRange range: dataRanges) {
                    tableBean.addModifiedRange(0,range.getAssociatedSheetName(), new RangeUtil.SheetRange(range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex()));
                }
                operation = CommandConstants.OperationType.SHIFT;
            }
        }

        tableBean.setOperation(operation);
        tableBean.setAssociatedSheetName(associatedSheetName);

        tableBeanList.add(tableBean);
    }

    private void handleMacroResponse(MacroResponse macroResponse, TableBean tableBean) {
        Map<String, List<RangeUtil.SheetRange>> modifiedRanges = new HashMap<>();
        ArrayList<JSONObjectWrapper> macroRangeList = macroResponse.getRangeList();
        int size = macroRangeList.size();
        for (int i = 0; i < size; i++) {
            JSONObjectWrapper rangeObj = macroRangeList.get(i);
            JSONArrayWrapper sheetListJson = rangeObj.optJSONArray(JSONConstants.SHEETLIST);
            JSONArrayWrapper rangeListJson = rangeObj.optJSONArray(JSONConstants.RANGELIST);
            boolean isValueChanged = rangeObj.optBoolean(JSONConstants.IS_VALUE_CHANGED, false);
            if(!isValueChanged) {
                continue;
            }

            if(sheetListJson != null && rangeListJson != null && !sheetListJson.isEmpty() && !rangeListJson.isEmpty()) {
                JSONArrayWrapper sheetArray = sheetListJson.optJSONArray(0);
                JSONArrayWrapper rangeArray = rangeListJson.optJSONArray(0);
                if(sheetArray != null && rangeArray != null && !sheetArray.isEmpty() && !rangeArray.isEmpty()) {
                    JSONObjectWrapper rangeObject = rangeArray.getJSONObject(0);
                    String asn = sheetArray.optString(0);
                    int startRow = rangeObject.optInt(JSONConstants.START_ROW);
                    int startCol = rangeObject.optInt(JSONConstants.START_COLUMN);
                    int endRow = rangeObject.optInt(JSONConstants.END_ROW);
                    int endCol = rangeObject.optInt(JSONConstants.END_COLUMN);

                    List<RangeUtil.SheetRange> list = modifiedRanges.computeIfAbsent(asn, s-> new ArrayList<>());
                    list.add(new RangeUtil.SheetRange(startRow, startCol, endRow, endCol));
                }
            }
        }

        if(!macroResponse.getNewTables().isEmpty()) {
            TableBean filterBean = new TableBean();
            filterBean.setOperation(CommandConstants.OperationType.ADD);

            for(Integer id: macroResponse.getNewTables()) {
                filterBean.addModifiedTable(id);
            }
            tableBeanList.add(filterBean);
        }

        if(!macroResponse.getRemovedTables().isEmpty())
        {
            TableBean filterBean = new TableBean();
            filterBean.setOperation(CommandConstants.OperationType.REMOVE);
            for(Integer id: macroResponse.getRemovedTables()) {
                filterBean.addModifiedRange(id, "0#", new RangeUtil.SheetRange(-1, -1, -1, -1));
            }
            tableBeanList.add(filterBean);
        }

        /* Here we are sending the whole sheet table meta, because there seems to be no way to know the exact modified ranges
         * from the macroResponse for certain actions like insert row, insert col*/
        Set reloadTileSheets = macroResponse.getReloadTileSheetsSet();
        for(Map.Entry<String, List<RangeUtil.SheetRange>> entry: modifiedRanges.entrySet()) {
            if(reloadTileSheets.contains(entry.getKey())) {
                continue;
            }

            for(RangeUtil.SheetRange range: entry.getValue()) {
                tableBean.addModifiedRange(-1, entry.getKey(), range);
            }

        }

        for(Object asn: reloadTileSheets) {
            tableBean.addModifiedRange(-1, (String) asn, new RangeUtil.SheetRange(0,0, Utility.MAXNUMOFROWS-1, Utility.MAXNUMOFCOLS-1));
        }

    }

    @Override
    public List<TableBean> getTableBeanList() {
        return tableBeanList;
    }
}
