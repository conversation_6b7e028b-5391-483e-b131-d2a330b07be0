package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.DataCleaningBean;
import com.adventnet.zoho.websheet.model.response.extractor.DataCleaningInfoExtractor;
import com.adventnet.zoho.websheet.model.response.helper.ResponseHelper;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.datarefineries.datacleaning.DataCleaningMessageConstants;

import java.util.List;

public class DataCleaningInfoExtractorImpl implements DataCleaningInfoExtractor {

    DataCleaningBean dataCleaningBean;
    public DataCleaningInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int action = actionJson.optInt(JSONConstants.ACTION,-1);
        int updatedCount = actionJson.optInt(JSONConstants.NO_OF_MATCH,0);
        int duplicateCount = actionJson.optInt(JSONConstants.NO_OF_DUPLICATES,0);
        boolean isUndoAction = actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO);
        List<DataRange> dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        String associatedSheetName = actionJson.optString(JSONConstants.ASSOCIATED_SHEET_NAME,ActionJsonUtil.getAssociateSheetName(actionJson));
        String actionType = actionJson.optString(DataCleaningMessageConstants.OPERATION_TYPE,"");
        CommandConstants.OperationType operation = null;
        switch(action) {
            case ActionConstants.DATA_CLEANSING_REPLACE_ALL:
                operation = CommandConstants.OperationType.REPLACE;
                break;
            case ActionConstants.FILL_EMPTY:
                operation = CommandConstants.OperationType.ADD;
                break;
            case ActionConstants.DC_APPLY_FORMATS:
            case ActionConstants.GET_DATA_DUPLICATES:
                operation = CommandConstants.OperationType.DUPLICATES;
                break;

            default:
                operation = CommandConstants.OperationType.MODIFY;
                break;
        }
        dataCleaningBean = new DataCleaningBean(operation,associatedSheetName,updatedCount, duplicateCount, isUndoAction, actionType, dataRanges);
    }


    public static boolean isValidToExecute(JSONObjectWrapper actionJSON, ResponseHelper responseHelper) {
        boolean isValid = actionJSON.has(JSONConstants.IS_VALUE_CHANGED) ? actionJSON.getBoolean(JSONConstants.IS_VALUE_CHANGED) || responseHelper.includeDataCleaningInfo : responseHelper.includeDataCleaningInfo;

        boolean isFromUndo = actionJSON.has(JSONConstants.FROM_UNDO);
        int actionConst = actionJSON.optInt(JSONConstants.ACTION, -1);

        if (isFromUndo && actionConst != -1) {
            switch (actionConst) {
                case ActionConstants.DATA_BAR_APPLY:
                case ActionConstants.COLOR_SCALES_APPLY:
                case ActionConstants.CONDITIONAL_FORMAT_APPLY:
                case ActionConstants.ICON_SET_APPLY:
                case ActionConstants.CONDITIONAL_FORMAT_EDIT:
                case ActionConstants.CONDITIONAL_FORMAT_DELETE:
                case ActionConstants.CHECKBOX_ADD:
                case ActionConstants.CHECKBOX_EDIT:
                case ActionConstants.CHECKBOX_CLEAR:
                case ActionConstants.PICKLIST_CREATE:
                case ActionConstants.PICKLIST_EDIT:
                case ActionConstants.PICKLIST_REMOVE:
                case ActionConstants.PICKLIST_MANAGE:
                case ActionConstants.PICKLIST_EMPTY_REMOVE:
                case ActionConstants.PICKLIST_INTERSECTION:
                case ActionConstants.PICKLIST_RANGE_CREATE:
                case ActionConstants.PICKLIST_RANGE_EDIT:
                    isValid = true;
                    break;
            }
        }

        return isValid;
    }

    @Override
    public DataCleaningBean getDataCleaningBean() {
        return dataCleaningBean;
    }
}
