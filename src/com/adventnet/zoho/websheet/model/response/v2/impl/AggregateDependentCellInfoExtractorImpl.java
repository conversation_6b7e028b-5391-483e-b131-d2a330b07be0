package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.AggregateDependentCellInfoExtractor;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;

public class AggregateDependentCellInfoExtractorImpl implements AggregateDependentCellInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();

    public AggregateDependentCellInfoExtractorImpl(JSONObjectWrapper actionJson) {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        String actionSheetName = ActionJsonUtil.getAssociateSheetName(actionJson);

        //Need to handle this case.
//	    	if (action == ActionConstants.RECALCULATE)
//	    	{
//	    		// Move srcCells to dependent cells. So that the yellow fade will ve shown in client.
//	    		if(srcCells != null){
//	    			dependentCells.addAll(srcCells);
//	    			srcCells = null;
//	    		}
//	    	}
//	    	else if(srcCells != null && !srcCells.isEmpty())
//	    	{
//	    		dependentCells.addAll(CellImpl.reevaluateDependents(srcCells, false));
//	    	}

//	    	if (action == ActionConstants.RECALCULATE) {
//	    		isRecalculateAction             =     true;
//	    	}

        if (actionSheetName != null) {
            if ((action == ActionConstants.INSERT_COL || action == ActionConstants.INSERT_ROW) && actionJson.has(JSONConstants.FROM_UNDO)) {
                List<DataRange> dataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                if (dataRanges != null) {
                    for (DataRange range : dataRanges) {
                        RangeWrapper rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),
                                range.getStartRowIndex(),
                                range.getStartColIndex(),
                                range.getEndRowIndex(),
                                range.getEndColIndex());

                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }

            if (action == ActionConstants.CUT_PASTE) {
                List<DataRange> sourceDataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                if (sourceDataRanges != null) {
                    for (DataRange range : sourceDataRanges) {
                        RangeWrapper rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),
                                range.getStartRowIndex(),
                                range.getStartColIndex(),
                                range.getEndRowIndex(),
                                range.getEndColIndex());

                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
