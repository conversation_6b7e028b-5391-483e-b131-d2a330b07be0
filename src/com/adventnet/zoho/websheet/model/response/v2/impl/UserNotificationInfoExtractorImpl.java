package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.NotificationBean;
import com.adventnet.zoho.websheet.model.response.extractor.UserNotificationInfoExtractor;
import com.adventnet.zoho.websheet.model.response.v2.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.MacroResponse;

import java.util.ArrayList;
import java.util.List;

public class UserNotificationInfoExtractorImpl implements UserNotificationInfoExtractor {

    List<NotificationBean> notificationBeanList = new ArrayList<>();
    public UserNotificationInfoExtractorImpl(JSONObjectWrapper actionJson, MacroResponse macroResponse)
    {
        boolean isUndoAction = actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO);
        if(isUndoAction) {
            return ;
        }
        int action = actionJson.getInt(JSONConstants.ACTION);
        boolean isRecastNeeded=true;

        if(macroResponse != null) {


            JSONArrayWrapper messageJsonArray = macroResponse.getMessageJsonArray();
            JSONArrayWrapper inputBoxJsonArray = macroResponse.getInputboxJsonArray();
            JSONArrayWrapper responseArray = new JSONArrayWrapper();
            JSONArrayWrapper hyperlinkjsonArray=macroResponse.getHyperlinkList();

            if(messageJsonArray != null && !messageJsonArray.isEmpty()) {
                JSONArrayWrapper parsedMessageJsonArray = ResponseUtils.parseMacroMessageJsonArray(messageJsonArray);
                JSONArrayWrapper macroArray = new JSONArrayWrapper();
                macroArray.put(CommandConstants.MACRO_INFO);
                macroArray.put(parsedMessageJsonArray);
                responseArray.put(macroArray);
            }

            if(inputBoxJsonArray!=null && !inputBoxJsonArray.isEmpty()){
                JSONArrayWrapper parsedInputBoxJsonArray = new JSONArrayWrapper( ResponseUtils.parseMacroInputBoxJsonArray(inputBoxJsonArray));
                JSONArrayWrapper macroArray = new JSONArrayWrapper();
                macroArray.put(CommandConstants.MACRO_INPUTBOX);
                macroArray.put(parsedInputBoxJsonArray);
                responseArray.put(macroArray);
            }

            if(hyperlinkjsonArray!=null && !hyperlinkjsonArray.isEmpty()){
                JSONArrayWrapper macroArray = new JSONArrayWrapper();
                macroArray.put(CommandConstants.MACRO_FOLLOWHYPERLINK);
                macroArray.put(hyperlinkjsonArray);
                 responseArray.put(macroArray);
            }

            JSONObjectWrapper errorJson =  macroResponse.getErrorJson();

            if(errorJson != null) {
                JSONArrayWrapper parsedErrorJsonArray = ResponseUtils.parseMacroErrorJsonArray(errorJson);
                JSONArrayWrapper macroArray = new JSONArrayWrapper();
                macroArray.put(CommandConstants.MACRO_ERROR);
                macroArray.put(parsedErrorJsonArray);
                responseArray.put(macroArray);
            }

            if(!responseArray.isEmpty()){
                NotificationBean notificationBean = new NotificationBean(CommandConstants.MACRO, responseArray);
                notificationBeanList.add(notificationBean);
            }
        }

        switch(action) {

//			case ActionConstants.MACRO_RUN:
//			case ActionConstants.MACRO_SAVE_RUN:
//			case ActionConstants.SUBMIT:
//			case ActionConstants.CHECKBOX_EDIT:
//			case ActionConstants.SHEET_INSERT:
//			case ActionConstants.INSERT_SHEET_WITH_CLIP_CONTENT:
//			case ActionConstants.POST_DOC_LOAD:
//				break;

            case ActionConstants.SYSTEMCLIP_PASTE :
                JSONArrayWrapper msgJsonArray  = new JSONArrayWrapper();
                JSONArrayWrapper respArray     = new JSONArrayWrapper();
                if(actionJson.has("isFormatApply")) {
                    isRecastNeeded = actionJson.getBoolean("isFormatApply"); //NO I18N
                }
                msgJsonArray.put(actionJson.getInt(JSONConstants.ACTION_ID));
                msgJsonArray.put(actionJson.getJSONArray("rangeList"));
                msgJsonArray.put(isRecastNeeded);
                msgJsonArray.put("SYSTEM_CLIP");
                msgJsonArray.put(actionJson.getJSONArray("sheetList"));
                msgJsonArray.put(actionJson.getBoolean("isT2C"));
                respArray.put(msgJsonArray);
                NotificationBean systemClipPasteNotificationBean = new NotificationBean(CommandConstants.SYSTEMCLIP_PASTE, respArray);
                notificationBeanList.add(systemClipPasteNotificationBean);
                break;
            case ActionConstants.COPY_PASTE :
                if(actionJson.has("isFormatApply")) {
                    isRecastNeeded = actionJson.getBoolean("isFormatApply"); //NO I18N
                }
                msgJsonArray  = new JSONArrayWrapper();
                respArray     = new JSONArrayWrapper();
                msgJsonArray.put(actionJson.getInt(JSONConstants.ACTION_ID));
                msgJsonArray.put(actionJson.getJSONArray("rangeList"));
                msgJsonArray.put(isRecastNeeded);
                msgJsonArray.put(actionJson.getString("pstype")); //NO I18N
                msgJsonArray.put(actionJson.getJSONArray("sheetList"));
                respArray.put(msgJsonArray);
                NotificationBean serverClipPaseteNotificationBean = new NotificationBean(CommandConstants.SERVERCLIP_PASTE_RANGE, respArray);
                notificationBeanList.add(serverClipPaseteNotificationBean);
                break;
            case ActionConstants.CUT_PASTE :
                msgJsonArray  = new JSONArrayWrapper();
                respArray     = new JSONArrayWrapper();
                msgJsonArray.put(actionJson.getInt(JSONConstants.ACTION_ID));
                msgJsonArray.put(actionJson.getJSONArray("rangeList"));
                msgJsonArray.put(false);
                msgJsonArray.put("CUTPASTE"); //NO I18N
                msgJsonArray.put(actionJson.getJSONArray("sheetList"));
                respArray.put(msgJsonArray);
                NotificationBean cutPasteNotificationBean = new NotificationBean(CommandConstants.CUT_PASTE, respArray);
                notificationBeanList.add(cutPasteNotificationBean);
                break;
            case ActionConstants.RECAST :
                NotificationBean recastNotificationBean = new NotificationBean(CommandConstants.RECAST, actionJson.getInt(JSONConstants.ACTION_ID));
                notificationBeanList.add(recastNotificationBean);
                break;
            case ActionConstants.FILLSERIES :
                msgJsonArray  = new JSONArrayWrapper();
                respArray     = new JSONArrayWrapper();
                msgJsonArray.put(actionJson.getInt(JSONConstants.ACTION_ID));
                msgJsonArray.put(actionJson.getJSONArray("rangeList"));
                msgJsonArray.put(isRecastNeeded);
                msgJsonArray.put(actionJson.getString("fst")); //NO I18N
                msgJsonArray.put(actionJson.getJSONArray("sheetList"));
                respArray.put(msgJsonArray);
                NotificationBean fillSeriesNotificationBean = new NotificationBean(CommandConstants.FILLSERIES, respArray);
                notificationBeanList.add(fillSeriesNotificationBean);
                break;
            case ActionConstants.PATTERN_FILL:
                msgJsonArray = new JSONArrayWrapper();
                respArray = new JSONArrayWrapper();
                JSONArrayWrapper patternFillDetails = actionJson.getJSONArray(JSONConstants.PATTERNFILL_DETAILS);
                msgJsonArray.put(actionJson.getInt(JSONConstants.ACTION_ID));
                msgJsonArray.put(patternFillDetails.getJSONArray(0));
                msgJsonArray.put(patternFillDetails.getInt(1));
                msgJsonArray.put(patternFillDetails.getJSONArray(2));
                msgJsonArray.put(patternFillDetails.getInt(3));
                msgJsonArray.put(patternFillDetails.getJSONArray(4));
                msgJsonArray.put(patternFillDetails.getJSONArray(5));
                msgJsonArray.put(patternFillDetails.getBoolean(6));
                msgJsonArray.put(patternFillDetails.getJSONArray(7));
                msgJsonArray.put(actionJson.getJSONArray(JSONConstants.SHEETLIST));
                respArray.put(msgJsonArray);
                NotificationBean patternFillBean = new NotificationBean(CommandConstants.PATTERN_FILL, respArray);
                notificationBeanList.add(patternFillBean);
                break;
            case ActionConstants.PASTESPECIAL_FORMATS :
                msgJsonArray  = new JSONArrayWrapper();
                respArray     = new JSONArrayWrapper();
                msgJsonArray.put(actionJson.getInt(JSONConstants.ACTION_ID));
                msgJsonArray.put(actionJson.getJSONArray("rangeList"));
                msgJsonArray.put(actionJson.getJSONArray("sheetList"));
                msgJsonArray.put(false);
                msgJsonArray.put("PASTESPECIAL"); //NO I18N
                msgJsonArray.put(actionJson.getJSONArray("sheetList"));
                respArray.put(msgJsonArray);
                NotificationBean pasteSpeacialNotificationBean = new NotificationBean(CommandConstants.PASTESPECIAL, respArray);
                notificationBeanList.add(pasteSpeacialNotificationBean);
                break;
            case ActionConstants.TABLE_CREATE:
                JSONArrayWrapper table = actionJson.getJSONArray(JSONConstants.TABLE);
                if(table.length() > 0) {
                    msgJsonArray  = new JSONArrayWrapper();
                    respArray     = new JSONArrayWrapper();
                    msgJsonArray.put(actionJson.getInt(JSONConstants.ACTION_ID));
                    JSONArrayWrapper rangeArray = table.getJSONArray(0);
                    msgJsonArray.put(new JSONArrayWrapper().put(rangeArray.get(1)).put(rangeArray.get(2)).put(rangeArray.get(3)).put(rangeArray.get(4)).put(rangeArray.get(5)));
                    respArray.put(msgJsonArray);
                    NotificationBean bean = new NotificationBean(CommandConstants.TABLE, respArray);
                    notificationBeanList.add(bean);
                }

                break;
			/*case ActionConstants.CREATE_PIVOT :
				if(actionJson.has("errorJson")){
					 msgJsonArray  = new JSONArrayWrapper();
					 respArray     = new JSONArrayWrapper();
					 msgJsonArray.put(actionJson.getInt(JSONConstants.ACTION));
					 msgJsonArray.put(actionJson.get("errorJson"));
					 respArray.put(msgJsonArray);
					 NotificationBean pivotErrorNotificationBean = new NotificationBean(CommandConstants.PIVOT, 1);
	                notificationBeans.add(pivotErrorNotificationBean);
				}
				break;*/

            case ActionConstants.REPLACE_ALL:
                break;
        }
    }

    @Override
    public List<NotificationBean> getNotificationBeanList() {
        return notificationBeanList;
    }
}
