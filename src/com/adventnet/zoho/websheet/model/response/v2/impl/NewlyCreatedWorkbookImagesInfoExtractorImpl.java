package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.ImageMetaBean;
import com.adventnet.zoho.websheet.model.response.extractor.NewlyCreatedWorkbookImagesInfoExtractor;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class NewlyCreatedWorkbookImagesInfoExtractorImpl implements NewlyCreatedWorkbookImagesInfoExtractor {

    String publishRangeId;
    List<ImageMetaBean> imageMetaBeanList = new ArrayList<>();
    public NewlyCreatedWorkbookImagesInfoExtractorImpl(JSONObjectWrapper actionJson, Set<Integer> workbookImageIds)
    {
        publishRangeId = actionJson.has(JSONConstants.RANGE_ID) ? actionJson.getString(JSONConstants.RANGE_ID) : null;
        JSONArrayWrapper imageJSONArray = actionJson.has(JSONConstants.IMAGE_JSON_ARRAY) ? actionJson.getJSONArray(JSONConstants.IMAGE_JSON_ARRAY) : new JSONArrayWrapper();
        if(workbookImageIds != null && !workbookImageIds.isEmpty())
        {
            for(Integer imageId : workbookImageIds)
            {
                long dummyId = -1;
                for (int i = 0; i < imageJSONArray.length(); i++)
                {
                    JSONObjectWrapper imageObject = imageJSONArray.getJSONObject(i);
                    if(imageObject.optInt(JSONConstants.IMAGE_ID, -1) == imageId)
                    {
                        dummyId = imageObject.has(JSONConstants.DUMMY_ID) ? imageObject.getLong(JSONConstants.DUMMY_ID) : -1;
                        break;
                    }
                }
                imageMetaBeanList.add(new ImageMetaBean(imageId, dummyId));
            }
        }
    }

    @Override
    public List<ImageMetaBean> getImageMetaBeanList() {
        return imageMetaBeanList;
    }

    @Override
    public String getPublishRangeId() {
        return publishRangeId;
    }
}
