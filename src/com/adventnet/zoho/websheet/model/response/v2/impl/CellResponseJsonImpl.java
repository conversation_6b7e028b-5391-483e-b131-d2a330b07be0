package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.response.beans.CellResponse;
import com.adventnet.zoho.websheet.model.response.meta.util.MetaBitUtil;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.helper.TimeCapsule;
import com.adventnet.zoho.websheet.model.response.v2.listener.ResponseListener;
import com.adventnet.zoho.websheet.model.response.v2.listener.ResponseListenerImpl;
import com.adventnet.zoho.websheet.model.response.viewport.*;
import com.adventnet.zoho.websheet.model.util.Constants;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.RangeUtil;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.zoho.logs.common.util.logging.OnDemandSearchLogger;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CellResponseJsonImpl implements CellResponse
{

    private static final Logger LOGGER = Logger.getLogger(CellResponseJsonImpl.class.getName());

    private Workbook workbook;
    ResponseListener listener;
    Constraints constraints;

    /*
     * Constructor used to create response for gridactions  (response for the action through grid actions).
     *
     */
    public CellResponseJsonImpl(Workbook workbook, List<RangeWrapper> rangeList, HashSet<Cell> cellList, Constraints constraints, HashSet<String> faultySheets, Boolean isActiveSheetFaulty) {

//    		long		startTime	=	System.currentTimeMillis();

        this.workbook = workbook;
        this.constraints = constraints;

        this.listener = new ResponseListenerImpl(workbook, constraints);


        // TODO : Revisit RangeAccumulator and check for redundancy in range
        // fix : removed redundant range
        List<RangeWrapper> modifiedList = removeRedundantRange(rangeList);



        processCellResponse(modifiedList, cellList, constraints, faultySheets, isActiveSheetFaulty);

//        long		totalTime	=	System.currentTimeMillis() - startTime;
//        LOGGER.log(Level.INFO, "[RESPONSE] Total time to generate the cellResponse with Faulties for Grid actions {0} " , totalTime);

    }

    private List<RangeWrapper> removeRedundantRange(List<RangeWrapper> rangeList){
        List<RangeWrapper> modifiedList = new ArrayList(new HashSet(rangeList));

        return modifiedList;
    }

    /*
     * Constructor used to create response for fetch data
     */
    public CellResponseJsonImpl(Workbook workbook, String sheetName, JSONArrayWrapper ranges, Constraints constraints) {

        this.workbook = workbook;
        this.constraints = constraints;
        this.listener = new ResponseListenerImpl(workbook, constraints);

        generateFetchDataResponse(sheetName, ranges);

    }

    /*
     * Constructor used to create response for a range
     *
     */
    public CellResponseJsonImpl(Workbook workbook, String sheetName, int startRow, int startCol, int endRow, int endCol, Constraints constraints) {
        this.workbook = workbook;
        this.constraints = constraints;
        this.listener = new ResponseListenerImpl(workbook, constraints);

        processCellResponseForDummyConstraints(sheetName, startRow, startCol, endRow, endCol);
    }

    // gateway to get the cell response.-- available for all
    public Object getCellResponse() {
        return this.listener.getProcessedCellResponse();
    }

    @Override
    public Object getFaultyRanges() {
        // TODO Auto-generated method stub

        return this.listener.getFaultyRangeList();
    }

    @Override
    public Object getFaultyCells() {

        return this.listener.getFaultyCellList();

    }

    //    		"FAULTY_CELLS"   :    {
//
//                "ASSOCIATED_SHEET_NAME" :  {
//
//                "IS_WHOLE_SHEET_FAULTY"  :   TRUE/FALSE,
//                "FAULTY_CELLS_LIST"            :    [[256*R+C],[,], ,,,]
//                " FAULTY_RANGE_LIST "           :    [[sR,sC,eR,eC],[,], ,,,]
//                },
//
//                "ASSOCIATED_SHEET_NAME" :  {
//
//                "IS_WHOLE_SHEET_FAULTY"  :   TRUE/FALSE,
//
//                "FAULTY_Range_LIST"           :    [[sR,sC,eR,eC],[,], ,,,]
//
//                },
    private void processCellResponseForDummyConstraints(String sheetName, int startRow, int startCol, int endRow, int endCol) {
        Sheet sheet = workbook.getSheetByAssociatedName(sheetName);
        processRangeResponse(sheet, startRow, startCol, endRow, endCol);
    }

    private void processCellResponse(List<RangeWrapper> rangeList, HashSet<Cell> cellList, Constraints constraints, HashSet<String> faultySheets, boolean isActiveSheetFaulty) {

        Sheet sheet;

        long startTime	=	System.currentTimeMillis();

        String activeSheetName = constraints.getContemporaryViewPort().getActiveViewPortSheetName();

        sheet = workbook.getSheetByAssociatedName(activeSheetName);
        /*
         * Temporarily handled the response if active sheet as faulty sheet
         */
        if (isActiveSheetFaulty) {

            /*
             * Getting first whole dom and data area, and creating areaItearator to iterate and send the response
             * to client.
             */
            List<Area> activeAreas = constraints.getContemporaryViewPort().getActiveViewPortAreas(activeSheetName);

            IterateArea iterateArea = new IterateArea(activeSheetName);

            for (Iterator iterator = activeAreas.iterator(); iterator.hasNext();) {

                Area area = (Area) iterator.next();
                iterateArea.addActiveAreas(area);

            }

            for(RangeWrapper notInActiveArea : constraints.getContemporaryViewPort().getRangeNonIntersectingWithDomViewPort(activeSheetName, 0, 0, Utility.MAXNUMOFROWS - 1, Utility.MAXNUMOFCOLS - 1)){
                listener.updateFaultyRangeList(notInActiveArea);
            }

            sheet = workbook.getSheetByAssociatedName(activeSheetName);

            while (iterateArea.hasNext()) {
                try {

                    CellInfo cellInfo = iterateArea.next();
                    listener.updateCellInfo(sheet, cellInfo);

                } catch (Exception e) {
                    // TODO: handle exception
                    LOGGER.log(Level.INFO, "[Response][Exception] Exception while generating cell Response.", e);
                }
            }
        }

        else if (rangeList != null && !rangeList.isEmpty()) {
            for (RangeWrapper rangeWrapper : rangeList) {

                String sheetName = rangeWrapper.getSheetName();

                /*	1. :-- if it is part of faulty sheet, should not create response
                 * 	2. :- null check
                 *  3. :- if sheet is in viewport(either dom/data)
                 */
                if (!faultySheets.contains(sheetName) && constraints != null && constraints.isSheetInViewPort(sheetName)) {

                    // This flow is only for grid action	-
                    /**
                     * **************************	Grid Actions
                     * ****************************
                     */
                    if (!constraints.isDummyConstraints()) {

                        // Considering the constraints.
                        boolean getAreaIterator = true;
                        // It checks in dataViewPort (Which contains dom too).
                        if (!constraints.isRangeInDomViewPort(rangeWrapper)) {

                            //if whole Range is not part of dom viewPort
                            //Updating range list as faulty rangeList
                            // if it intersects means full range is not faulty
                            getAreaIterator = constraints.isRangeIntersectWithDOMViewPort(rangeWrapper);
                            if (!getAreaIterator) {
                                //if we are not going for iterator, update the range as faulty
                                listener.updateFaultyRangeList(rangeWrapper);
                            }

                        }
                        //else{

                        if (getAreaIterator) {

                            //Here, Range may be part of both data and dom.


                            // Get the part of the range that inrtersects with DOM viewport

                            List<RangeWrapper> actionPerformedInDom = constraints.getRangeIntersectingWithDOMViewPort(rangeWrapper);

                            int startRow , startCol, endRow, endCol;

                            // split the DOM viewport from cached viewport

                            for(RangeWrapper domRanges : actionPerformedInDom) {
                                // determining startRow, startCol, endRow, endCol
                                if(rangeWrapper.getstartRow()< domRanges.getstartRow()) {
                                    startCol = rangeWrapper.getstartCol();
                                    endCol = rangeWrapper.getEndCol();
                                    startRow = rangeWrapper.getstartRow();
                                    endRow = domRanges.getstartRow() - 1;
                                    listener.updateFaultyRangeList(new RangeWrapper(domRanges.getSheetName(),startRow,startCol,endRow,endCol));


                                }
                                if(rangeWrapper.getEndRow() > domRanges.getEndRow()) {
                                    startCol = rangeWrapper.getstartCol();
                                    endCol = rangeWrapper.getEndCol();
                                    startRow = domRanges.getEndRow() + 1;
                                    endRow = rangeWrapper.getEndRow();
                                    listener.updateFaultyRangeList(new RangeWrapper(domRanges.getSheetName(),startRow,startCol,endRow,endCol));
                                }
                                if(rangeWrapper.getstartCol() < domRanges.getstartCol()) {
                                    startRow = domRanges.getstartRow();
                                    endRow = domRanges.getEndRow();
                                    startCol = rangeWrapper.getstartCol();
                                    endCol = domRanges.getstartCol() -1 ;
                                    listener.updateFaultyRangeList(new RangeWrapper(domRanges.getSheetName(),startRow,startCol,endRow,endCol));

                                }
                                if(rangeWrapper.getEndCol() > domRanges.getEndCol()) {
                                    startRow = domRanges.getstartRow();
                                    endRow = domRanges.getEndRow();
                                    startCol = domRanges.getEndCol() + 1;
                                    endCol = rangeWrapper.getEndCol() ;
                                    listener.updateFaultyRangeList(new RangeWrapper(domRanges.getSheetName(),startRow,startCol,endRow,endCol));
                                }


                            }
                            // if range is in DOM viewport and not in cached viewport send cell response
                            long startCellRespTime	=	System.currentTimeMillis();
                            for(RangeWrapper activeRange : actionPerformedInDom){
                                RangeIterator rangeIterator = new RangeIterator(workbook.getSheetByAssociatedName(activeRange.getSheetName()), activeRange.getstartRow(), activeRange.getstartCol(), activeRange.getEndRow(), activeRange.getEndCol(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, false, true);
                                while (rangeIterator.hasNext()){
                                    ReadOnlyCell roCell = rangeIterator.next();
                                    try {
                                        CellInfo cellInfo = new CellInfo(roCell.getRowIndex(), roCell.getColIndex(), ViewPortStatus.ACTIVE);
                                        listener.updateCellInfo(sheet, cellInfo);

                                    } catch (Exception e) {
//                                          // TODO: handle exception
                                        LOGGER.log(Level.INFO, "[Response][Exception] Exception while generating cell Response.", e);
                                    }
                                }
                            }
//                            LOGGER.log(Level.INFO, "[RESPONSE] total time to create cellDATAResponse >> {0} " , (System.currentTimeMillis() - startCellRespTime));

                        }
                        //}

                    } else {

                        // This flow is only for Fetch data action
                        sheet = workbook.getSheetByAssociatedName(sheetName);
                        processRangeResponse(sheet, rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());

                    }
                }
            }

        }

//        if (srcCells != null && !srcCells.isEmpty()) {
//            for (Cell tempSrcCell : srcCells) {
//                //if it is part of faulty sheet, it should not generate the response.
//                if (!faultySheets.contains(tempSrcCell.getRow().getSheet().getAssociatedName())) {
//
//                    listener.updateCell(tempSrcCell);
//                }
//            }
//
//        }

        if (cellList != null && !cellList.isEmpty()) {

            for (Cell tempCell : cellList) {
                //if it is part of faulty sheet, it should not generate the response.
                if (!faultySheets.contains(tempCell.getRow().getSheet().getAssociatedName())) {

                    listener.updateCell(tempCell);
                }
            }
        }
        OnDemandSearchLogger.log(LOGGER, Level.INFO, "[RESPONSE] Total time taken to generate cell response is {0} ms" ,(System.currentTimeMillis() - startTime));
    }

    private void processRangeResponse(Sheet sheet, int startRow, int startCol, int endRow, int endCol) {
        CellInfo cellInfo;
        for (int row = startRow; row <= endRow;) {
            for (int col = startCol; col <= endCol;) {
                cellInfo = new CellInfo(row, col, ViewPortStatus.ACTIVE);
                listener.updateCellInfo(sheet, cellInfo);

                Cell cell = sheet.getCell(row, col);
                int colRepeat = cell.getColsRepeated();

                if ((colRepeat + col) > Utility.MAXNUMOFCOLS) {

                    colRepeat = Utility.MAXNUMOFCOLS - col;

                }

                col = col + colRepeat;
            }

            int rowRepeat = sheet.getReadOnlyRowFromShell(row).getRowsRepeated();

            if (rowRepeat + row > Utility.MAXNUMOFROWS) {
                rowRepeat = Utility.MAXNUMOFROWS - row;
            }

            row = rowRepeat + row;
        }
    }

    private void generateFetchDataResponse(String sheetName, JSONArrayWrapper ranges) {

        JSONObjectWrapper rangeJsonObject;
        JSONArrayWrapper rangeBoundaryAry;
        JSONArrayWrapper missedArray;
        TimeCapsule timeCapsule = new TimeCapsule();

        //Sheet		sheet			=		workbook.getSheetByAssociatedName(sheetName);
        Sheet sheet = workbook.getSheet(sheetName);
        for (int i = 0; i < ranges.length(); i++) {
            // Printing the ranges if responseGeneration takes more than 5 seconds.
            if(timeCapsule.getLapsedTime() > 2000)
            {
                JSONArrayWrapper rangeBoundaries = new JSONArrayWrapper();
                for(int k =0; k < ranges.length(); k++){
                    rangeBoundaries.put(ranges.getJSONObject(k).getJSONArray("boundry"));
                }
                LOGGER.log(Level.INFO, "CRITICAL : SHOULD BE FIXED : generateFetchDataResponse taking too long to complete ::: index>>> {0} : for rangeBoundaries >>> {1} : averageRangeTime >>> {2}", new Object[]{i, rangeBoundaries, timeCapsule.printAverageTime()});
                return;
            }

            rangeJsonObject = ranges.getJSONObject(i);
            rangeBoundaryAry = rangeJsonObject.getJSONArray("boundry");	 // No I18N
            missedArray = rangeJsonObject.has("missed") ? rangeJsonObject.getJSONArray("missed") : null;		  // No I18N
            if(missedArray!=null){
                generateCellResponse(sheet, rangeBoundaryAry, missedArray, timeCapsule);
            }
        }
    }

    private void generateCellResponse(Sheet sheet, JSONArrayWrapper rangeBoundaryAry, JSONArrayWrapper missedArray, TimeCapsule timeCapsule)
    {
        int startRow = rangeBoundaryAry.getInt(0);
        int startCol = rangeBoundaryAry.getInt(1);
        int endRow = rangeBoundaryAry.getInt(2);
        int endCol = rangeBoundaryAry.getInt(3);

        // Get the merged ranges that intersect either with first row or first column.
        Map<Cell, DataRange> intersectingMergeRanges =  null;

        int row = startRow;
        List<DataRange> faultyDataRanges = new ArrayList<>();
        int hiddenSCol = (int) (Math.floor(startCol / Constants.BIT_TYPE) * Constants.BIT_TYPE);

        for(int i1 = 0; i1 < missedArray.length() && row <= endRow; i1++)
        {
            List<DataRange> singleRowFaultyRanges = new ArrayList<>();
            JSONArrayWrapper subArray = missedArray.getJSONArray(i1);
            int col = hiddenSCol;

            for(int  j = 0; j < subArray.length(); j++)
            {
                int x = subArray.getInt(j);
                for(int i = 0; i < Constants.BIT_TYPE && col <= endCol; i++)
                {
                    if(timeCapsule.getLapsedTime() > 2000)
                    {
                        return;
                    }

                    if(col >= startCol)
                    {
                        boolean isFaulty = MetaBitUtil.getBit(x, Constants.BIT_TYPE, i);
                        if(isFaulty)
                        {
                            timeCapsule.startEvent(TimeCapsule.Event.DATARANGE_ISMEMBER);
                            boolean isGenerated = RangeUtil.isMember(singleRowFaultyRanges, sheet.getAssociatedName(), row, col) ||
                                    RangeUtil.isMember(faultyDataRanges, sheet.getAssociatedName(), row, col);
                            timeCapsule.endCurrentEvent();
                            if(!isGenerated) {
                                // Initialize the intersecting ranges for current range...
                                if(intersectingMergeRanges == null)
                                {
                                    timeCapsule.startEvent(TimeCapsule.Event.INTERSECTING_MERGE_CELLS);
                                    // Get the merged ranges that intersect either with first row or first column.
                                    intersectingMergeRanges =  getIntersectingMergeRanges(sheet, startRow, startCol, endRow, endCol);
                                    timeCapsule.endCurrentEvent();
                                }
                                //////////////////////////////////

//                                Cell mergeParentCell = null;
//                                if(row == startRow | col == startCol)
//                                {
//                                    timeCapsule.startEvent(TimeCapsule.Event.GET_MERGE_PARENT);
//                                    mergeParentCell = ResponseUtils.getMergeParentCellIfCovered(sheet, row, col, intersectingMergeRanges);
//                                    timeCapsule.endCurrentEvent();
//                                }

                                DataRange dataRange = null;
                                timeCapsule.startEvent(TimeCapsule.Event.CELL_RESPONSE);

                                // Getting the cell response.
                                CellInfo cellInfo = new CellInfo(row, col, ViewPortStatus.ACTIVE);
                                dataRange = this.listener.updateCellInfo(sheet, cellInfo, timeCapsule);
                                // Getting merge parent cell response.
                                timeCapsule.endCurrentEvent();

                                if(dataRange != null && dataRange.getSize() > 1) {
                                    if (dataRange.getRowSize() == 1) {
                                        singleRowFaultyRanges.add(dataRange);
                                    } else {
                                        faultyDataRanges.add(dataRange);
                                    }
                                }
                            }
                        }

                        // Printing the ranges if responseGeneration takes more than 5 seconds.
                        if(timeCapsule.getLapsedTime() > 2000)
                        {
                            return;
                        }
                    }
                    col++;
                }
            }
            row++;
        }
    }

    private Map<Cell, DataRange> getIntersectingMergeRanges(Sheet sheet, int startRow, int startCol, int endRow, int endCol)
    {
        // First Row Range
        DataRange rowRange = new DataRange(sheet.getAssociatedName(), startRow, startCol, startRow, endCol);
        // First Col Range
        DataRange colRange = new DataRange(sheet.getAssociatedName(), startRow, startCol, endRow, startCol);

        Map<Cell, DataRange> intersectingRanges = new HashMap<>();
        for (DataRange mergedRange : sheet.getMergeCellDetails().values())
        {
            Cell mergeParentCell = sheet.getCell(mergedRange.getStartRowIndex(), mergedRange.getStartColIndex());
            if(RangeUtil.intersection(mergedRange, rowRange) != null || RangeUtil.intersection(mergedRange, colRange) != null)
            {
                intersectingRanges.put(mergeParentCell, mergedRange);
            }
        }

        return intersectingRanges;
    }


//    private void generateCellResponse(Sheet sheet, JSONArray rangeBoundaryAry, JSONArray missedArray, JSONArray cfMissedArray) {
//        int startRow = rangeBoundaryAry.getInt(0);
//        int startCol = rangeBoundaryAry.getInt(1);
//        int endRow = rangeBoundaryAry.getInt(2);
//        int endCol = rangeBoundaryAry.getInt(3);
//
////    	         JSONArray       hiddenRow       	=   ResponseUtils.getInnerArray(hiddenArray , 0);
////    	         JSONArray       hiddenCol       	=   ResponseUtils.getInnerArray(hiddenArray , 1);
//        int hiddenSRow = (int) (Math.floor(startRow / 8) * 8);
//        int hiddenSCol = (int) (Math.floor(startCol / 8) * 8);
//
//        boolean isFaulty;
//        boolean isCfFaulty = false;
//        JSONArray innerArray;
//        JSONArray cfInnerArray = new JSONArray();
//        boolean isCfMissedAryNull = cfMissedArray == null ? true : false;
//
//        ReadOnlyCell rCell;
//        int rowRepeat;
//        int colRepeat;
//
//        ReadOnlyRow rRow;
//        CellInfo cellInfo;
//
//        int j = 0;   //    TO HANDLE THE MISSED LOOP
//        for (int row = startRow; row <= endRow;) {       // cell response
//            rRow = sheet.getReadOnlyRow(row);
//            rowRepeat = rRow.getRowsRepeated();
//            if (rowRepeat + row > Utility.MAXNUMOFROWS) {
//                rowRepeat = Utility.MAXNUMOFROWS - row;
//            }// it consider hide row separatley.
//            innerArray = ResponseUtils.getInnerArray(missedArray, j);
//            for (int col = startCol; col <= endCol;) {
//                isFaulty = ResponseUtils.isFaulty(col, hiddenSCol, innerArray);
////                // if cell is faulty, add it to colAry
//                if (isFaulty) {
//                    cellInfo = new CellInfo(row, col, ViewPortStatus.ACTIVE);
//                    this.listener.updateCellInfo(sheet, cellInfo);
//
//                rCell = sheet.getReadOnlyCell(row, col);
//                colRepeat = rCell.getColsRepeated();
//                if ((colRepeat + col) > Utility.MAXNUMOFCOLS) {
//                    colRepeat = Utility.MAXNUMOFCOLS - col;
//                }
//                col = col + colRepeat;
//                }else{
//                    col++;
//            }
//            }
//            row = row + rowRepeat;
//            j = j + rowRepeat;
//        }
//        j= 0 ;

//        for (int row = startRow; row <= endRow;) {
//
//            rRow = sheet.getReadOnlyRow(row);
//            rowRepeat = rRow.getRowsRepeated();				// it consider hide row separatley.
//
//            if (rowRepeat + row > Utility.MAXNUMOFROWS) {
//                rowRepeat = Utility.MAXNUMOFROWS - row;
//            }
//            innerArray = ResponseUtils.getInnerArray(missedArray, j);
//            if (!isCfMissedAryNull) {
//                cfInnerArray = ResponseUtils.getInnerArray(cfMissedArray, j);
//            }
//            for (int col = startCol; col <= endCol;) {
//                isFaulty = ResponseUtils.isFaulty(col, hiddenSCol, innerArray);
//
//                // if cell is faulty, add it to colAry
//                if (isFaulty) {
//                    cellInfo = new CellInfo(row, col, ViewPortStatus.ACTIVE);
//                    this.listener.updateCellInfo(sheet, cellInfo);
//                }
//
//                rCell = sheet.getReadOnlyCell(row, col);
//                colRepeat = rCell.getColsRepeated();
//
//                if ((colRepeat + col) > Utility.MAXNUMOFCOLS) {
//
//                    colRepeat = Utility.MAXNUMOFCOLS - col;
//
//                }
//
//                col = col + colRepeat;// ResponseUtils.getNextNonHiddenCol(col, hiddenSCol, hiddenCol ,endCol);
//
//            }
//            for (int col = startCol; col <= endCol;) {
//                if (!isCfMissedAryNull) {
//                    isCfFaulty = (col == startCol) ? ResponseUtils.isFaulty(col, hiddenSCol, cfInnerArray) : true;
//                }
//                if (isCfFaulty) {
//                    cellInfo = new CellInfo(row, col, ViewPortStatus.ACTIVE);
//                    this.listener.updateConditionalStyleInfo(sheet, cellInfo);
//                }
//
//                col = ResponseUtils.getNextNonHiddenCol(col, hiddenSCol, cfInnerArray, endCol);
//            }
//    	            row					=  		row  + rowRepeat;
//            // shifting to the corresponding missed arrays
//    	           j 					= 		j    + rowRepeat;
////    	            row 				= 		ResponseUtils.getNextNonHiddenRow(row, hiddenSRow, hiddenRow, endRow);
//        }
//    }

}
