package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.SlicerBean;
import com.adventnet.zoho.websheet.model.response.extractor.SlicerInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;

public class SlicerInfoExtractorImpl implements SlicerInfoExtractor {

    List<SlicerBean> slicerBeanList = new ArrayList<>();

    public SlicerInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        String sheetName = null;
        CommandConstants.OperationType operationType = null;
        SlicerBean slicerBean;
        List<String> slicerIDList = new ArrayList<>();
        List<String> pivotIDList = new ArrayList<>();
        String slicerToBeInserted = null;
        boolean sendFullResponse = false;
        boolean isFromUndo = actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO);
        SlicerBean.SlicerResponseType slicerResponseType = SlicerBean.SlicerResponseType.INIT;
        switch (action)
        {
            case ActionConstants.APPLY_THEME:
            case -1:
                sheetName = actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                operationType = CommandConstants.OperationType.GENERATE_LIST;
                break;
            case ActionConstants.SLICER_NEW:
                sheetName = actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                operationType = CommandConstants.OperationType.INSERT;
                JSONArrayWrapper slicerNames =  actionJson.getJSONArray(JSONConstants.SLICER_ID_LIST);
                for(int i = 0; i < slicerNames.length(); i++) {
                    slicerIDList.add(slicerNames.getString(i));
                }
                if(isFromUndo) { 
                    sendFullResponse = true;
                }
                break;
            case ActionConstants.APPLYFILTER_PIVOT:
            case ActionConstants.APPLY_SLICER_FILTER:
            case ActionConstants.UPDATE_CONNECTED_PIVOTS:
                sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString( JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                operationType = CommandConstants.OperationType.MODIFY;
                if(actionJson.has(JSONConstants.SLICER_ID) ) {
                    slicerIDList.add(actionJson.getString(JSONConstants.SLICER_ID));
                }
                if(actionJson.has(JSONConstants.ID) ) {
                    pivotIDList.add(actionJson.getString(JSONConstants.ID));
                }
                slicerResponseType = SlicerBean.SlicerResponseType.BUTTON_STATES;
                sendFullResponse = true;
                break;
            case ActionConstants.APPLY_TIMELINE_FILTER:
            case ActionConstants.UPDATE_TIMELINE_CONNECTED_PIVOTS:
            case ActionConstants.UPDATE_TIMELINE_COLUMN:
                sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString( JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                operationType = CommandConstants.OperationType.MODIFY;
                if(actionJson.has(JSONConstants.CONNECTED_PIVOTS)){
                    JSONArrayWrapper pivotNames = actionJson.getJSONArray(JSONConstants.CONNECTED_PIVOTS);
                    for(int j = 0; j < pivotNames.length(); j++){
                        pivotIDList.add(pivotNames.getString(j));
                    }
                }
                slicerResponseType = SlicerBean.SlicerResponseType.BUTTON_STATES;
                sendFullResponse = true;
                break;
            case ActionConstants.UPDATE_MULTI_SELECT:
            case ActionConstants.CHANGE_SLICER_THEME:
            case ActionConstants.SORT_SLICER:
//            case ActionConstants.HIDE_SLICER_HEADER:
            case ActionConstants.UPDATE_SLICER_STYLE:
//            case ActionConstants.MAKE_SLICER_TRANSPARENT:
            case ActionConstants.CHANGE_SLICER_TITLE:
            case ActionConstants.CHANGE_SLICER_LAYOUT:
                sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString( JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                operationType = CommandConstants.OperationType.MODIFY;
                if(actionJson.has(JSONConstants.SLICER_ID) ) {
                    slicerIDList.add(actionJson.getString(JSONConstants.SLICER_ID));
                }
                if(action == ActionConstants.CHANGE_SLICER_THEME){
                    slicerResponseType = SlicerBean.SlicerResponseType.STYLE;
                }else if(action == ActionConstants.SORT_SLICER){
                    slicerResponseType = SlicerBean.SlicerResponseType.BUTTON_ELEMENTS_AND_STATES;
                }else{
                    slicerResponseType = SlicerBean.SlicerResponseType.SETTINGS;
                }
                break;
            case ActionConstants.SLICER_RESIZE:
            case ActionConstants.SLICER_MOVE:
                if(actionJson.has(JSONConstants.SLICER_ID)){
                    sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString( JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                    operationType = CommandConstants.OperationType.MOVE;
                    slicerIDList.add(actionJson.getString(JSONConstants.SLICER_ID));
                }
                slicerResponseType = SlicerBean.SlicerResponseType.DIMENSION;
                break;
            case ActionConstants.MOVE_SLICER_TO_NEW_SHEET:
                JSONArrayWrapper temp = new JSONArrayWrapper();
                temp.put(actionJson.getString(JSONConstants.SLICER_ID));
                sheetName = actionJson.getString(JSONConstants.DESTINATION_SHEET);
                slicerToBeInserted = actionJson.getString(JSONConstants.SLICER_ID);
                operationType = CommandConstants.OperationType.ADD;
                actionJson.put(JSONConstants.SLICER_ID_LIST,temp);
            case ActionConstants.UPDATE_SLICER_COLUMN:
                sheetName = sheetName == null ? actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME) : sheetName;
                slicerToBeInserted  = slicerToBeInserted == null ? actionJson.getString(JSONConstants.NEW_SLICER_ID) : slicerToBeInserted;
                operationType = operationType == null ? CommandConstants.OperationType.INSERT : operationType;
                slicerIDList.add(slicerToBeInserted);
                slicerBean =  new SlicerBean(slicerIDList, operationType,sheetName,false,slicerResponseType);
                slicerIDList = new ArrayList<>();
                slicerBeanList.add(slicerBean);
            case ActionConstants.SLICER_DELETE:
                sheetName = actionJson.has(JSONConstants.ASSOCIATED_SHEET_NAME) ? actionJson.getString( JSONConstants.ASSOCIATED_SHEET_NAME) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                operationType = CommandConstants.OperationType.DELETE;
                slicerNames =  actionJson.has(JSONConstants.SLICER_ID_LIST) ? actionJson.getJSONArray(JSONConstants.SLICER_ID_LIST) : new JSONArrayWrapper();
                for(int i = 0; i < slicerNames.length(); i++) {
                    slicerIDList.add(slicerNames.getString(i));
                }
                if(actionJson.has(JSONConstants.CONNECTED_PIVOTS)){
                    JSONArrayWrapper pivotNames = actionJson.getJSONArray(JSONConstants.CONNECTED_PIVOTS);
                    for(int j = 0; j < pivotNames.length(); j++){
                        pivotIDList.add(pivotNames.getString(j));
                    }
                }
                else if(actionJson.has(JSONConstants.ID)){
                    pivotIDList.add(actionJson.getString(JSONConstants.ID));
                }
                sendFullResponse = true;
                slicerResponseType = SlicerBean.SlicerResponseType.BUTTON_STATES;
                break;

            default:
                //When the pivot Source is edited, we need to send Slicer Response too.
                JSONObjectWrapper sheetToSlicerMap =  actionJson.has(JSONConstants.SLICER_DELETE_MAP) ? actionJson.getJSONObject(JSONConstants.SLICER_DELETE_MAP) : new JSONObjectWrapper();
                Iterator<String> iterator = sheetToSlicerMap.keys();
                HashSet<String> deletedSlicers = new HashSet<>();
                if(!actionJson.has("fromundo")) {
                    while (iterator.hasNext()) {
                        String sheet = iterator.next();
                        JSONArrayWrapper slicerArray = sheetToSlicerMap.getJSONArray(sheet);
                        for (int j = 0; j < slicerArray.length(); j++) {
                            slicerIDList.add(slicerArray.getString(j));
                        }
                        slicerBean = new SlicerBean(slicerIDList, CommandConstants.OperationType.DELETE, sheet, true,slicerResponseType);
                        slicerBeanList.add(slicerBean);
                        deletedSlicers.addAll(slicerIDList);
                        slicerIDList = new ArrayList<>();
                    }
                }

                if(actionJson.has(JSONConstants.SLICER_UPDATE_MAP)){
                    JSONObjectWrapper slicerUpdateMap = actionJson.getJSONObject(JSONConstants.SLICER_UPDATE_MAP);
                    Iterator<String> sheetIterator = slicerUpdateMap.keys();
                    while(sheetIterator.hasNext()){
                        String sheet = sheetIterator.next();
                        JSONArrayWrapper slicerArray = slicerUpdateMap.getJSONArray(sheet);
                        for(int j = 0; j < slicerArray.length(); j++){
                            if(deletedSlicers.contains(slicerArray.getString(j))){
                                continue;
                            }
                            slicerIDList.add(slicerArray.getString(j));
                        }
                        slicerBean = new SlicerBean(slicerIDList, CommandConstants.OperationType.MODIFY, sheet, false,slicerResponseType);
                        slicerBeanList.add(slicerBean);
                    }
                }
                if(actionJson.has(JSONConstants.CONNECTED_PIVOTS)){
                    JSONArrayWrapper pivotNames = actionJson.getJSONArray(JSONConstants.CONNECTED_PIVOTS);
                    for(int j = 0; j < pivotNames.length(); j++){
                        pivotIDList.add(pivotNames.getString(j));
                    }
                    if(!slicerBeanList.isEmpty()) {
                        slicerBeanList.get(0).setPivotNames(pivotIDList);
                    }
                    else{
                        operationType = CommandConstants.OperationType.MODIFY;
                        sendFullResponse = true;
                    }
                }
                break;
        }
        if(operationType != null)
        {
            slicerBean = new SlicerBean(slicerIDList, operationType, sheetName, sendFullResponse,slicerResponseType);
            if (!pivotIDList.isEmpty()) {
                slicerBean.setPivotNames(pivotIDList);
            }
            slicerBeanList.add(slicerBean);
        }
    }
    @Override
    public List<SlicerBean> getSlicerBeanList() {
        return slicerBeanList;
    }

}
