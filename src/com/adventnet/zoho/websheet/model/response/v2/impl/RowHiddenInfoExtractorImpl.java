package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.RowHiddenInfoExtractor;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.List;

public class RowHiddenInfoExtractorImpl implements RowHiddenInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public RowHiddenInfoExtractorImpl(JSONObjectWrapper actionJson, MacroResponse macroResponse)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        RangeWrapper rangeWrapper;
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        List<DataRange> sourceDataRanges = ActionJsonUtil.getListOfUnfilteredDataRangesFromJsonObject(actionJson, true);

        CommandConstants.OperationType operationType;
        Boolean isUndo = actionJson.has(JSONConstants.FROM_UNDO);
        switch (action)
        {
            case ActionConstants.INSERT_CUT_ROW:
                boolean isSameSheet = actionJson.has(JSONConstants.IS_SAME_SHEET) ? actionJson.getBoolean(JSONConstants.IS_SAME_SHEET) : false;
                CommandConstants.OperationType oprType = !actionJson.has(JSONConstants.FROM_UNDO) ? CommandConstants.OperationType.ADD : CommandConstants.OperationType.REMOVE;
                if(dataRange != null)
                {
                    DataRange sourceDataRange = sourceDataRanges.get(0);
                    for(DataRange range : dataRange)
                    {
                        if(isSameSheet)
                        {
                            int srcStartRow = sourceDataRange.getStartRowIndex();
                            int srcEndRow = sourceDataRange.getEndRowIndex();
                            int totalSize = srcEndRow - srcStartRow + 1;
                            int startRow = srcStartRow <= range.getStartRowIndex() ? range.getStartRowIndex() - totalSize : range.getStartRowIndex();
                            int endRow = srcEndRow <= range.getEndRowIndex() ?  range.getEndRowIndex() - totalSize : range.getEndRowIndex();
                            if(isUndo)
                            {
                                startRow = srcStartRow <= range.getStartRowIndex() ? sourceDataRange.getStartRowIndex() : sourceDataRange.getStartRowIndex() - totalSize;
                                endRow = srcEndRow <= range.getEndRowIndex() ?  sourceDataRange.getEndRowIndex() : sourceDataRange.getEndRowIndex() - totalSize;
                            }
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), startRow, range.getStartColIndex(), endRow, range.getEndColIndex(), CommandConstants.OperationType.ADD);

                        }
                        else
                        {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), oprType);
                        }
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            case ActionConstants.HIDE_ROWS:
            case ActionConstants.COLLAPSE_ROWGROUP:
            case ActionConstants.COLLAPSEALL_ROWGROUPS:
                if (isUndo) {
                    operationType = CommandConstants.OperationType.REMOVE;
                }else{
                    operationType = CommandConstants.OperationType.ADD;
                }
                if (dataRange != null) {
                    dataRange = RangeUtil.sortRowAscendingOrder(dataRange);
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1,operationType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            case ActionConstants.UNHIDE_ROWS:
            case ActionConstants.EXPAND_ROWGROUP:
            case ActionConstants.UNGROUP_ROW:
            case ActionConstants.EXPANDALL_ROWGROUPS:
                if (isUndo) {
                    operationType = CommandConstants.OperationType.ADD;
                }else{
                    operationType = CommandConstants.OperationType.REMOVE;
                }
                if (dataRange != null) {
                    dataRange = RangeUtil.sortRowAscendingOrder(dataRange);
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, operationType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            case ActionConstants.MACRO_RUN:
            case ActionConstants.MACRO_SAVE_RUN:
            case ActionConstants.SUBMIT: //This Submit check is for the fire Worksheet_Change Event.
            case ActionConstants.COPY_PASTE_CONTENT:
            case ActionConstants.COPY_PASTE:
            case ActionConstants.CUT_PASTE:
            case ActionConstants.CHECKBOX_EDIT:
                if(macroResponse != null) {
                    JSONArrayWrapper rowArray = macroResponse.getHiddenRows();
                    for(int i = 0; i < rowArray.length(); i++) {
                        dataRange = new ArrayList<>();
                        JSONObjectWrapper childJson = rowArray.getJSONObject(i);

                        if(childJson.getBoolean("isHidden")) {
                            operationType = CommandConstants.OperationType.ADD;
                        } else {
                            operationType = CommandConstants.OperationType.REMOVE;
                        }

                        JSONArrayWrapper jsonArray = childJson.getJSONArray(JSONConstants.RANGELIST);
                        for(int j = 0; j < jsonArray.length(); j++) {
                            JSONObjectWrapper json = jsonArray.getJSONObject(j);
                            DataRange range = new DataRange(json.getString(JSONConstants.ASSOCIATED_SHEET_NAME), json.getInt(JSONConstants.START_ROW), json.getInt(JSONConstants.START_COLUMN), json.getInt(JSONConstants.END_ROW), json.getInt(JSONConstants.END_COLUMN));
                            dataRange.add(range);
                        }

                        dataRange = RangeUtil.sortRowAscendingOrder(dataRange);
                        for (DataRange range : dataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, operationType);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
                if(action == ActionConstants.CUT_PASTE || action == ActionConstants.COPY_PASTE) {
                    if(action == ActionConstants.CUT_PASTE) {
                        List<DataRange> sourceRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
                        if (sourceRanges != null) {
                            RangeUtil.sortRowAscendingOrder(sourceRanges);
                            for (DataRange sourceRange : sourceRanges) {
                                if (sourceRange.isEntireRow()) {
                                    rangeWrapperList.add(new RangeWrapper(sourceRange.getAssociatedSheetName(), sourceRange.getStartRowIndex(), -1, sourceRange.getEndRowIndex(), -1, isUndo ? CommandConstants.OperationType.ADD: CommandConstants.OperationType.REMOVE));
                                }
                            }
                        }
                    }
                    List<DataRange> destRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                    if(destRanges != null) {
                        RangeUtil.sortRowAscendingOrder(destRanges);
                        for(DataRange destRange: destRanges) {
                            if(destRange.isEntireRow()) {
                                rangeWrapperList.add(new RangeWrapper(destRange.getAssociatedSheetName(), destRange.getStartRowIndex(), -1, destRange.getEndRowIndex(), -1, isUndo ? CommandConstants.OperationType.REMOVE: CommandConstants.OperationType.ADD));
                            }
                        }
                    }
                }
                break;

            case ActionConstants.CLEAROUTLINE:
                rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), 0, -1, Utility.MAXNUMOFROWS-1, -1, isUndo? CommandConstants.OperationType.ADD: CommandConstants.OperationType.REMOVE);
                rangeWrapperList.add(rangeWrapper);
                break;

            case -1:
                rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), actionJson.getInt(JSONConstants.START_ROW), -1, actionJson.getInt(JSONConstants.END_ROW), -1, CommandConstants.OperationType.GENERATE_LIST);
                rangeWrapperList.add(rangeWrapper);
                break;
            default:
                break;
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
