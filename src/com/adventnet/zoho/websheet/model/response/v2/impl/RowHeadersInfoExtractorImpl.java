package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.ext.LinearIntegralRange;
import com.adventnet.zoho.websheet.model.response.extractor.RowHeadersInfoExtractor;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.util.FilterBean;

import java.util.*;
import java.util.stream.Collectors;

public class RowHeadersInfoExtractorImpl implements RowHeadersInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public RowHeadersInfoExtractorImpl(JSONObjectWrapper actionJson, MacroResponse macroResponse)
    {
        int action = actionJson != null && actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        RangeWrapper rangeWrapper;
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        List<DataRange> sourceDataRange = ActionJsonUtil.getListOfUnfilteredDataRangesFromJsonObject(actionJson, true);
        List<DataRange> affectedRowHeadersInfo = ActionJsonUtil.getListOfDataRanges(actionJson.optJSONArray(JSONConstants.SHEETLIST), actionJson.optJSONArray(JSONConstants.AFFECTED_ROW_HEADERS));
        boolean isSameSheet = actionJson.has(JSONConstants.IS_SAME_SHEET) ? actionJson.getBoolean(JSONConstants.IS_SAME_SHEET) : false;
        boolean fromUndo = actionJson.has(JSONConstants.FROM_UNDO) ? true : false;
        if (actionJson != null && (action == ActionConstants.COPY_PASTE || action == ActionConstants.PASTESPECIAL_FORMATS
                || action == ActionConstants.CUT_PASTE)) {

            int type = actionJson.has("t") ? actionJson.getInt("t") : -1;					// No I18N

            boolean addRowHeader = true;

            if (action == ActionConstants.COPY_PASTE || action == ActionConstants.CUT_PASTE) {
                //Stopping row header data for column level copy/cut paste -- Beleive it should not affect row height
                if (isSameSheet && type == 1) {
                    addRowHeader = false;
                }
            }
            if (addRowHeader) {

                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, CommandConstants.OperationType.MODIFY);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }

                if (action == ActionConstants.CUT_PASTE) {
                    if (dataRange != null) {
                        for (DataRange range : sourceDataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, CommandConstants.OperationType.MODIFY);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
            }

        }

//        if (filterBean != null) {
//            JSONObject rowsDetails = filterBean.getRowsDetails();
//            if (rowsDetails != null) {
//                String asn = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString(JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
//                rangeWrapper = new RangeWrapper(asn, rowsDetails.getInt(JSONConstants.START_ROW), -1, rowsDetails.getInt(JSONConstants.END_ROW), -1, CommandConstants.OperationType.MODIFY);
//                rangeWrapperList.add(rangeWrapper);
//
//            }
//        }
        switch (action) {

            case ActionConstants.SUBMIT:
            case ActionConstants.COPY_PASTE_CONTENT:
            case ActionConstants.DATA_FROM_PICTURE:
            case ActionConstants.REPLACE:
            case ActionConstants.HEIGHT:
            case ActionConstants.FONTFAMILY:
            case ActionConstants.FONTSIZE:
            case ActionConstants.WRAP:
            case ActionConstants.TEXT_ROTATE_BY_ANGLE:
            case ActionConstants.FILLSERIES:
            case ActionConstants.CLEARALL:
            case ActionConstants.CLEARCONTENTS:
            case ActionConstants.CLEARSTYLES:
            case ActionConstants.SYSTEMCLIP_PASTE:
            case ActionConstants.SORT:
            case ActionConstants.SET_INDENT:
            case ActionConstants.INCREASE_INDENT:
            case ActionConstants.DECREASE_INDENT:
            case ActionConstants.PICKLIST_CREATE:       // We need to send for Picklist actions because the Picklist Values might add Bubble Node
            case ActionConstants.PICKLIST_RANGE_CREATE: // in cells which will change the optimal row height
            case ActionConstants.PICKLIST_EDIT:
            case ActionConstants.PICKLIST_RANGE_EDIT:
            case ActionConstants.PICKLIST_REMOVE:
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, CommandConstants.OperationType.MODIFY);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            case ActionConstants.WIDTH:
                if (affectedRowHeadersInfo != null && !affectedRowHeadersInfo.isEmpty())
                {
                    for (DataRange range : affectedRowHeadersInfo)
                    {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, CommandConstants.OperationType.MODIFY);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            /* added these three actions for wrap				*/
            case ActionConstants.INSERT_COPY_ROW:
            case ActionConstants.FORM_LIVE_SUBMIT:
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, CommandConstants.OperationType.INSERT);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            case ActionConstants.INSERT_ROW:
                Map<String, Map<Integer, Integer>> asnToRowIndexToCount;
                JSONArrayWrapper asnToRowIndexToCountJSON = actionJson.optJSONArray(JSONConstants.INDICES);
                if(asnToRowIndexToCountJSON != null) {
                    asnToRowIndexToCount = ActionJsonUtil.dejsoniseASNToIndexToCount(asnToRowIndexToCountJSON);
                }
                else
                {
                    Map<String, List<LinearIntegralRange>> asnToRowRangesForInsert = dataRange.stream().collect(Collectors.groupingBy(DataRange::getAssociatedSheetName, Collectors.mapping(tempDataRange -> new LinearIntegralRange(tempDataRange.getStartRowIndex(), tempDataRange.getEndRowIndex()), Collectors.toList())));
                    asnToRowIndexToCount = asnToRowRangesForInsert.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry ->
                            ActionUtil.getIndexToCountForInsert(entry.getValue())
                    ));
                }
                if(fromUndo)
                {
                    for(Map.Entry<String, Map<Integer, Integer>> asnToIndexToCountEntry: asnToRowIndexToCount.entrySet()) {
                        String asn = asnToIndexToCountEntry.getKey();
                        Map<Integer, Integer> indexToCount = asnToIndexToCountEntry.getValue();
                        List<Map.Entry<Integer, Integer>> indexToCountSorted = indexToCount
                                .entrySet()
                                .stream()
                                .sorted(Comparator.comparingInt(lir -> lir.getKey()))
                                .collect(Collectors.toList());

                        for(Map.Entry<Integer, Integer> indexToCountEntry: indexToCountSorted) {
                            rangeWrapper = new RangeWrapper(asn, indexToCountEntry.getKey(), -1, indexToCountEntry.getKey()+indexToCountEntry.getValue()-1, -1, CommandConstants.OperationType.INSERT);
                            rangeWrapperList.add(rangeWrapper);

                        }
                    }
                }else{

                    for(Map.Entry<String, Map<Integer, Integer>> asnToIndexToCountEntry: asnToRowIndexToCount.entrySet()) {
                        String asn = asnToIndexToCountEntry.getKey();
                        Map<Integer, Integer> indexToCount = asnToIndexToCountEntry.getValue();
                        List<Map.Entry<Integer, Integer>> indexToCountSorted = indexToCount
                                .entrySet()
                                .stream()
                                .sorted(Comparator.comparingInt(lir -> -lir.getKey()))
                                .collect(Collectors.toList());

                        for(Map.Entry<Integer, Integer> indexToCountEntry: indexToCountSorted) {
                            rangeWrapper = new RangeWrapper(asn, indexToCountEntry.getKey(), -1, indexToCountEntry.getKey()+indexToCountEntry.getValue()-1, -1, CommandConstants.OperationType.INSERT);
                            rangeWrapperList.add(rangeWrapper);

                        }
                    }
                }
//                if (dataRange != null) {
//                    dataRange = RangeUtil.sortRowAscendingOrder(dataRange); //1 to 4
//                    if(fromUndo){
//                       for (DataRange drange : dataRange) {
//                             rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(),  drange.getStartRowIndex(), -1, drange.getEndRowIndex(), -1, OperationType.INSERT);
//                            rowHeaderList.add(rangeWrapper);
//                       }
//
//                    }else{
//                        int prevRow = 0;
//                        int prevRowCount = 0;
//                        for (DataRange drange : dataRange) {
//                            int count = (drange.getEndRowIndex() - drange.getStartRowIndex()) + 1;
//                            int start = drange.getStartRowIndex();
//                            int end = drange.getEndRowIndex();
//                            if (prevRow < start) {
//                                start += prevRowCount;
//                                end += prevRowCount;
//                            }
//                            rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), start, -1, end, -1, OperationType.INSERT);
//                            rowHeaderList.add(rangeWrapper);
//                            prevRow = drange.getStartRowIndex();
//                            prevRowCount += count;
//                        }
//                    }
//                }
                break;

            case ActionConstants.INSERT_CELL_TOP:
            case ActionConstants.DELETE_CELL_BOTTOM:
            case ActionConstants.TABLE_INSERT_ROW:
            case ActionConstants.TABLE_DELETE_ROW:
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, Utility.MAXNUMOFROWS - 1, -1, CommandConstants.OperationType.MODIFY);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;

            case ActionConstants.INSERT_CUT_ROW:

                CommandConstants.OperationType oprType = !actionJson.has(JSONConstants.FROM_UNDO) ? CommandConstants.OperationType.INSERT : CommandConstants.OperationType.DELETE;
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        DataRange srcrange = sourceDataRange.get(dataRange.indexOf(range));
                        if(srcrange.getStartRowIndex() < range.getStartRowIndex()) // drag and drop after the source range
                        {
                            int count = range.getEndRowIndex() - range.getStartRowIndex() + 1;
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, oprType);
                            rangeWrapperList.add(rangeWrapper);
                            if(!actionJson.has(JSONConstants.FROM_UNDO))
                            {
                                rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex() - count, -1, range.getEndRowIndex() - count, -1, CommandConstants.OperationType.MODIFY);
                                rangeWrapperList.add(rangeWrapper);
                            }
                        }
                        else if(srcrange.getStartRowIndex() > range.getStartRowIndex()) // drag and drop before the source range
                        {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, oprType);
                            rangeWrapperList.add(rangeWrapper);
                        }

                    }
                }
                oprType = isSameSheet ? (actionJson.has(JSONConstants.FROM_UNDO) ? CommandConstants.OperationType.INSERT : CommandConstants.OperationType.DELETE) : CommandConstants.OperationType.MODIFY;
                if (sourceDataRange != null) {
                    for (DataRange range : sourceDataRange)
                    {
                        int count = 0;
                        DataRange destRange = dataRange.get(sourceDataRange.indexOf(range));
                        if(actionJson.has(JSONConstants.FROM_UNDO) && range.getStartRowIndex() > destRange.getStartRowIndex())
                        {
                            count = range.getEndRowIndex() - range.getStartRowIndex() + 1;
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex() - count, -1, range.getEndRowIndex() - count, -1, CommandConstants.OperationType.MODIFY);
                            rangeWrapperList.add(rangeWrapper);
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, oprType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;

            case ActionConstants.DELETE_ROW:
            case ActionConstants.DELETE_RECORDS:
                Map<String, Map<Integer, Integer>> asnToRowIndexToCountForDelete;
                JSONArrayWrapper asnToRowIndexToCountForDeleteJSON = actionJson.optJSONArray(JSONConstants.INDICES);
                if(asnToRowIndexToCountForDeleteJSON != null)
                {
                    asnToRowIndexToCountForDelete = ActionJsonUtil.dejsoniseASNToIndexToCount(asnToRowIndexToCountForDeleteJSON);
                }
                else
                {
                    Map<String, List<LinearIntegralRange>> asnToRowRangesForDelete = dataRange != null ? dataRange.stream().collect(Collectors.groupingBy(DataRange::getAssociatedSheetName, Collectors.mapping(tempDataRange -> new LinearIntegralRange(tempDataRange.getStartRowIndex(), tempDataRange.getEndRowIndex()), Collectors.toList()))) : new HashMap<>();
                    asnToRowIndexToCountForDelete = asnToRowRangesForDelete.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry ->
                            ActionUtil.getIndexToCountForInsert(entry.getValue())
                    ));
                }
                if(fromUndo)
                {
                    for(Map.Entry<String, Map<Integer, Integer>> asnToIndexToCountEntry: asnToRowIndexToCountForDelete.entrySet()) {
                        String asn = asnToIndexToCountEntry.getKey();

                        Map<Integer, Integer> indexToCount = asnToIndexToCountEntry.getValue();
                        List<Map.Entry<Integer, Integer>> indexToCountSorted = indexToCount
                                .entrySet()
                                .stream()
                                .sorted(Comparator.comparingInt(lir -> lir.getKey()))
                                .collect(Collectors.toList());

                        for(Map.Entry<Integer, Integer> indexToCountEntry: indexToCountSorted) {
                            rangeWrapper = new RangeWrapper(asn, indexToCountEntry.getKey(), -1, indexToCountEntry.getKey()+indexToCountEntry.getValue()-1, -1, CommandConstants.OperationType.DELETE);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }else{
                    for(Map.Entry<String, Map<Integer, Integer>> asnToIndexToCountEntry: asnToRowIndexToCountForDelete.entrySet()) {
                        String asn = asnToIndexToCountEntry.getKey();

                        Map<Integer, Integer> indexToCount = asnToIndexToCountEntry.getValue();
                        List<Map.Entry<Integer, Integer>> indexToCountSorted = indexToCount
                                .entrySet()
                                .stream()
                                .sorted(Comparator.comparingInt(lir -> -lir.getKey()))
                                .collect(Collectors.toList());

                        for(Map.Entry<Integer, Integer> indexToCountEntry: indexToCountSorted) {
                            rangeWrapper = new RangeWrapper(asn, indexToCountEntry.getKey(), -1, indexToCountEntry.getKey()+indexToCountEntry.getValue()-1, -1, CommandConstants.OperationType.DELETE);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
//                if (dataRange != null) {
//                    if(fromUndo){
//                        dataRange = RangeUtil.sortRowAscendingOrder(dataRange);
//                        int prevRow = 0;
//                        int prevRowCount = 0;
//                        List<DataRange> newDataRanges = new ArrayList<>();
//                        for (DataRange drange : dataRange) {
//                            int count = (drange.getEndRowIndex() - drange.getStartRowIndex()) + 1;
//                            int start = drange.getStartRowIndex();
//                            int end = drange.getEndRowIndex();
//                            if (prevRow < start) {
//                                start += prevRowCount;
//                                end += prevRowCount;
//                            }
//                            DataRange newRange = new DataRange(drange.getAssociatedSheetName(), start, drange.getStartColIndex(), end, drange.getEndColIndex());
//                            newDataRanges.add(newRange);
//                            prevRow = drange.getStartRowIndex();
//                            prevRowCount += count;
//                        }
//
//                        newDataRanges = RangeUtil.sortRowDescendingOrder(newDataRanges); //4 to 1
//                        for(DataRange range: newDataRanges){
//                             rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, OperationType.DELETE);
//                            rowHeaderList.add(rangeWrapper);
//                        }
//                    }else{
//                        dataRange = RangeUtil.sortRowDescendingOrder(dataRange);
//                        for (DataRange drange : dataRange) {
//                                rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), drange.getStartRowIndex(), -1, drange.getEndRowIndex(), -1, OperationType.DELETE);
//                                rowHeaderList.add(rangeWrapper);
//                        }
//                    }
//                }
                break;

            case ActionConstants.DOCUMENT_SETTING:
            case ActionConstants.APPLY_THEME:
                String activeSheetASN = ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                rangeWrapper = new RangeWrapper(activeSheetASN, 0, -1, Utility.MAXNUMOFROWS - 1, -1, CommandConstants.OperationType.MODIFY);
                rangeWrapperList.add(rangeWrapper);
                break;
            case ActionConstants.TABLE_CREATE:
            case ActionConstants.TABLE_TOGGLE_HEADER_FOOTER:
                if(actionJson.has(JSONConstants.TABLE_INSERT_CELLS)) {
                    // If Table_insert_cells is present, then it means either cutpaste or insert cell is performed.
                    if (dataRange != null) {
                        for (DataRange range : dataRange) {
                            int endRow = actionJson.getBoolean(JSONConstants.TABLE_INSERT_CELLS) ? Utility.MAXNUMOFROWS - 1 : range.getEndRowIndex();
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), -1, endRow, -1, CommandConstants.OperationType.MODIFY);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
                break;
            case -1:
                // This block will execute if it is coming through responseAnalyzerDataImpl  -- For document load
                if (actionJson != null) {
                    rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), actionJson.getInt(JSONConstants.START_ROW), -1, actionJson.getInt(JSONConstants.END_ROW), -1, CommandConstants.OperationType.ADD);
                    rangeWrapperList.add(rangeWrapper);
                }
                break;
        }

        if (macroResponse != null && !macroResponse.isEmpty()) {
            Map<String, List<DataRange>> rowMap = macroResponse.getAffectedRowHeaders();
            if(rowMap != null && !rowMap.isEmpty())
            {
                for (Map.Entry<String, List<DataRange>> entry : rowMap.entrySet())
                {
                    String asn = entry.getKey();
                    List<DataRange> rowList = entry.getValue();
                    for (DataRange range : rowList)
                    {
                        rangeWrapper = new RangeWrapper(asn, range.getStartRowIndex(), -1, range.getEndRowIndex(), -1, CommandConstants.OperationType.MODIFY);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }
            if(!macroResponse.getDeleteRowsArray().isEmpty()) {
                JSONArrayWrapper deleteRowArray = macroResponse.getDeleteRowsArray();
                for(int i = 0; i < deleteRowArray.length(); i++) {
                    JSONObjectWrapper childJson = deleteRowArray.getJSONObject(i);
                    rangeWrapper = new RangeWrapper(childJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), childJson.getInt(JSONConstants.START_ROW), -1, childJson.getInt(JSONConstants.END_ROW), -1, CommandConstants.OperationType.DELETE);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
