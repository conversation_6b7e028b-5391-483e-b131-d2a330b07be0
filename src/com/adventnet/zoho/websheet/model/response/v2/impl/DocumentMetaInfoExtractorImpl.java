package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.DocumentMetaBean;
import com.adventnet.zoho.websheet.model.response.extractor.DocumentMetaInfoExtractor;
import com.adventnet.zoho.websheet.model.response.helper.ResponseHelper;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

public class DocumentMetaInfoExtractorImpl implements DocumentMetaInfoExtractor {

    DocumentMetaBean documentMetaBean;
    public DocumentMetaInfoExtractorImpl(JSONObjectWrapper actionJson, ResponseHelper helper)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        JSONObjectWrapper privileges = actionJson.has(JSONConstants.CAPABILITIES) ? actionJson.getJSONObject(JSONConstants.CAPABILITIES) : null;
        if (action == -1) // Fetching Meta on Load.
        {
            String rangeId = actionJson.has(JSONConstants.RANGE_ID) ? actionJson.getString(JSONConstants.RANGE_ID) : (actionJson.has(JSONConstants.RANGE_META) ? actionJson.getJSONObject(JSONConstants.RANGE_META).getString(JSONConstants.RANGE_ID): null);
            documentMetaBean = helper.includeOnLoadDetails ? new DocumentMetaBean(true, true, true, privileges) :new DocumentMetaBean(true, privileges);
            documentMetaBean.setRangeId(rangeId);
            documentMetaBean.setIsPublishedView(helper.isPublishedView);
        }
    }

    @Override
    public DocumentMetaBean getDocumentMetaBean() {
        return documentMetaBean;
    }
}
