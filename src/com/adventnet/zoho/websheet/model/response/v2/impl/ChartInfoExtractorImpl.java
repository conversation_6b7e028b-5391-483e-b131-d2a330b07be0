package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.ChartBean;
import com.adventnet.zoho.websheet.model.response.extractor.ChartInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.chart.ChartConstants;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ChartInfoExtractorImpl implements ChartInfoExtractor {

    List<ChartBean> chartBeanList = new ArrayList<>();
    public ChartInfoExtractorImpl(JSONObjectWrapper actionJson, JSONObjectWrapper affectedChartList)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        CommandConstants.OperationType operationType = null;
        String sheetName = null;
        String targetSheetName = null;
        ChartBean chartBean = null;
        String chartId = null;
        int subAction = actionJson.has("subAction") ? actionJson.getInt("subAction") : -1;
        String value = null;
        boolean isYAxis = false;
        boolean isUndoAction = actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO);
        switch (action) {
            case -1:
                sheetName = actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                operationType = CommandConstants.OperationType.GENERATE_LIST;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;
            case ActionConstants.APPLY_THEME:
                sheetName = actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                operationType = CommandConstants.OperationType.THEME;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;

            case ActionConstants.CHART_NEW:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                operationType = CommandConstants.OperationType.INSERT;
                chartId = actionJson.getString("chartId");
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;

            case ActionConstants.CHART_RESIZE:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                chartId = actionJson.getString("chartId");
                operationType = CommandConstants.OperationType.RESIZE;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;
            case ActionConstants.CHART_MOVE:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                chartId = actionJson.getString("chartId");
                operationType = CommandConstants.OperationType.MOVE;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;

            case ActionConstants.CHART_DELETE:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                chartId = actionJson.getString("chartId");
                operationType = CommandConstants.OperationType.DELETE;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;

            case ActionConstants.CHART_EDIT:
            case ActionConstants.CHART_QUICK_STYLE:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                chartId = actionJson.getString("chartId");	//No I18N
                List<CommandConstants.OperationType> opTypesList = chooseOperationTypeForSubAction(subAction);
                value = actionJson.has("value") ? actionJson.getString("value") : null;	//No I18N
                isYAxis = actionJson.has("actionList") && actionJson.getJSONArray("actionList").getJSONObject(0).has("isYAxis")?actionJson.getJSONArray("actionList").getJSONObject(0).getBoolean("isYAxis"):false;	//No I18N
                for(CommandConstants.OperationType opType : opTypesList) {
                    chartBean = new ChartBean(sheetName, chartId, action, opType, subAction, value, isYAxis);
                    chartBeanList.add(chartBean);
                }
                break;
            case ActionConstants.CHART_PASTE_STYLE:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                chartId = actionJson.getString("chartId");
                operationType = CommandConstants.OperationType.CHART_MODIFY;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);

                operationType = CommandConstants.OperationType.MODIFY;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;

            case ActionConstants.CHART_QUICK_EDIT:
                sheetName = actionJson.getString(JSONConstants.CURRENT_ACTIVE_SHEET);
                chartId = actionJson.getString("chartId");	//No I18N
                opTypesList = chooseOperationTypeForSubAction(subAction);
                value = actionJson.getString("value") != null ? actionJson.getString("value") : value;	//No I18N
                isYAxis = actionJson.has("isYAxis")?actionJson.getBoolean("isYAxis"):false;	//No I18N
                for(CommandConstants.OperationType opType : opTypesList) {
                    chartBean = new ChartBean(sheetName, chartId, action, opType, subAction, value, isYAxis);
                    chartBeanList.add(chartBean);
                }
                if(operationType == CommandConstants.OperationType.UPDATE) {
                    operationType = CommandConstants.OperationType.EDIT;
                    chartBean = new ChartBean(sheetName, chartId, action, operationType, subAction, value, isYAxis);
                    chartBeanList.add(chartBean);
                }
                break;

            case ActionConstants.INSERT_ROW:
            case ActionConstants.DELETE_ROW:
            case ActionConstants.INSERT_COL:
            case ActionConstants.DELETE_COL:
            case ActionConstants.INSERT_CUT_ROW:
            case ActionConstants.INSERT_CUT_COLUMN:
            case ActionConstants.INSERT_COPY_ROW:
            case ActionConstants.INSERT_COPY_COLUMN:
            case ActionConstants.APPLY_FILTER:
            case ActionConstants.REMOVE_FILTER:
            case ActionConstants.SUBMIT:
            default:
                if(affectedChartList != null )
                {
                    for (Iterator affectedChartKeys = affectedChartList.keys(); affectedChartKeys.hasNext();)
                    {
                        sheetName = (String) affectedChartKeys.next();
                        JSONArrayWrapper affectedChartForSheet = affectedChartList.getJSONArray(sheetName);
                        for (int i = 0; i < affectedChartForSheet.length(); i++)
                        {
                            chartId = affectedChartForSheet.getString(i);
                            chartBean = new ChartBean(sheetName, chartId, action, CommandConstants.OperationType.UPDATE);
                            chartBeanList.add(chartBean);
                        }
                    }
                }
                break;

            case ActionConstants.CHART_MOVE_TO_NEW_SHEET:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                chartId = actionJson.getString("chartId");
                operationType = isUndoAction ? CommandConstants.OperationType.DELETE : CommandConstants.OperationType.ADD;
                targetSheetName = actionJson.getString("newSheetName");
                action = isUndoAction ? ActionConstants.CHART_DELETE : ActionConstants.CHART_NEW;
                chartBean = new ChartBean(targetSheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                operationType = !isUndoAction ? CommandConstants.OperationType.DELETE : CommandConstants.OperationType.ADD;
                action = !isUndoAction ? ActionConstants.CHART_DELETE : ActionConstants.CHART_NEW;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;
            case ActionConstants.CHART_PUBLISH:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                chartId = actionJson.getString("chartId");
                operationType = CommandConstants.OperationType.PUBLISH;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;
            case ActionConstants.CHART_UNPUBLISH:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                chartId = actionJson.getString("chartId");
                operationType = CommandConstants.OperationType.UNPUBLISH;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;
            case ActionConstants.CHART_PUBLISH_CHANGE:
                sheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
                chartId = actionJson.getString("chartId");
                operationType = CommandConstants.OperationType.CHART_PUBLISH_CHANGE;
                chartBean = new ChartBean(sheetName, chartId, action, operationType);
                chartBeanList.add(chartBean);
                break;
            case ActionConstants.SHEET_RENAME:
                operationType = CommandConstants.OperationType.RENAME;
                if(affectedChartList != null)
                {
                    for (Iterator affectedChartKeys = affectedChartList.keys(); affectedChartKeys.hasNext();)
                    {
                        sheetName = (String) affectedChartKeys.next();
                        JSONArrayWrapper affectedChartForSheet = affectedChartList.getJSONArray(sheetName);
                        for (int i = 0; i < affectedChartForSheet.length(); i++)
                        {
                            chartId = affectedChartForSheet.getString(i);
                            chartBean = new ChartBean(sheetName, chartId, action, operationType);
                            chartBeanList.add(chartBean);
                        }
                    }
                }
                break;
            case ActionConstants.DELETE_PIVOT:
                JSONArrayWrapper chartList = actionJson.optJSONArray("chartIdArr"); //No I18N
                JSONArrayWrapper sheetChartIdList = actionJson.optJSONArray("sheetChartId"); //No I18N
                if(chartList != null) {
                    operationType = CommandConstants.OperationType.DELETE;
                    for (int i = 0; i < chartList.length(); i++) {
                        chartId = chartList.getString(i);
                        sheetName = sheetChartIdList.getString(i);
                        chartBean = new ChartBean(sheetName, chartId, action, operationType);
                        chartBeanList.add(chartBean);
                    }
                }
                break;
        }
    }


    public boolean isSubActionApplicableForChartRegeneration(int subAction) {
        return subAction == -1 || subAction == ChartConstants.COLOR_PALLATE || subAction==ChartConstants.CHART_TYPE || subAction == ChartConstants.TRENDLINE_TYPE || subAction == ChartConstants.TRENDLINE_ORDER || subAction == ChartConstants.TRENDLINE_PERIOD
                || subAction == ChartConstants.UPDATE_BINNING_INTERVAL || subAction == ChartConstants.AGGREGATION || subAction == ChartConstants.FILTER || subAction == ChartConstants.MULTIPLE_Y_AXES;
    }

    private List<CommandConstants.OperationType> getMobileAndWebConstantsForEachSubAction(int subAction) {
        List<CommandConstants.OperationType> opTypesList = new ArrayList<CommandConstants.OperationType>();
        if(subAction == -1 || subAction == ChartConstants.COLOR_PALLATE || subAction == ChartConstants.MULTIPLE_Y_AXES){
            opTypesList.add(CommandConstants.OperationType.CHART_MODIFY);	// for mobile.
            opTypesList.add(CommandConstants.OperationType.MODIFY);	// for web.
        }
        else if(subAction == ChartConstants.TRENDLINE_ORDER || subAction == ChartConstants.TRENDLINE_TYPE || subAction == ChartConstants.TRENDLINE_PERIOD){
            opTypesList.add(CommandConstants.OperationType.EDIT);	// for mobile and web.
        }
        else if(subAction == ChartConstants.AGGREGATION || subAction == ChartConstants.FILTER || subAction == ChartConstants.UPDATE_BINNING_INTERVAL) {
            opTypesList.add(CommandConstants.OperationType.CHART_MODIFY);	// for mobile.
            opTypesList.add(CommandConstants.OperationType.EDIT);	// for web.
        }
        else if(subAction == ChartConstants.CHART_TYPE) {
            opTypesList.add(CommandConstants.OperationType.EDIT);	// web and mobile.
        }
        else{
            opTypesList.add(CommandConstants.OperationType.UPDATE); 	// web and mobile.
        }
        return opTypesList;
    }

    public List<CommandConstants.OperationType> chooseOperationTypeForSubAction(int subAction) {
        return getMobileAndWebConstantsForEachSubAction(subAction);
    }

    @Override
    public List<ChartBean> getChartBeanList() {
        return chartBeanList;
    }
}
