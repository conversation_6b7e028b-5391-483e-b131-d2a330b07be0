package com.adventnet.zoho.websheet.model.response.v2.impl;

import com.adventnet.zoho.websheet.model.response.beans.KnitChartsClipBean;
import com.adventnet.zoho.websheet.model.response.UserInfo;
import com.adventnet.zoho.websheet.model.response.extractor.KnitChartsClipInfoExtractor;
import com.zoho.sheet.knitcharts.chartclip.ChartClip;
import com.zoho.sheet.knitcharts.chartclip.ChartClipManager;

public class KnitChartsClipInfoExtractorImpl implements KnitChartsClipInfoExtractor {

    private final KnitChartsClipBean clipBean;

    public KnitChartsClipInfoExtractorImpl(UserInfo userInfo) {
        String zuid = userInfo.getZuid();
        String clipID = ChartClipManager.getInstance().getStylesClipID(zuid);
        ChartClip chartClip = ChartClipManager.getInstance().getClip(clipID);

        clipBean = new KnitChartsClipBean(chartClip);
    }
    @Override
    public KnitChartsClipBean getChartsClipInfo() {
        return clipBean;
    }
}
