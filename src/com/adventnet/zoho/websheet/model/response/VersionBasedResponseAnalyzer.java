package com.adventnet.zoho.websheet.model.response;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.response.helper.ResponseHelper;
import com.adventnet.zoho.websheet.model.response.holder.ConstraintResponseHolder;
import com.adventnet.zoho.websheet.model.response.holder.DocumentResponseHolder;
import com.adventnet.zoho.websheet.model.response.holder.UserSpecificResponseHolder;
import com.adventnet.zoho.websheet.model.response.ResponseObject.ResponseType;
import com.adventnet.zoho.websheet.model.response.v1.V1ResponseAnalyzer;
import com.adventnet.zoho.websheet.model.response.v2.V2ResponseAnalyzer;
import com.adventnet.zoho.websheet.model.response.v2.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.response.viewport.Constraints;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.logs.common.util.logging.OnDemandSearchLogger;
import com.zoho.sheet.authorization.AppUtil;
import java.util.logging.Level;
import java.util.logging.Logger;

public abstract class VersionBasedResponseAnalyzer {

    private static final Logger LOGGER = Logger.getLogger(VersionBasedResponseAnalyzer.class.getName());

    ResponseObject responseObject;
    DocumentResponseHolder documentResponseHolder = new DocumentResponseHolder();
    ConstraintResponseHolder constraintResponseHolder = new ConstraintResponseHolder();
    UserSpecificResponseHolder userSpecificResponseHolder = new UserSpecificResponseHolder();
    JSONObjectWrapper documentResponse = new JSONObjectWrapper();
    JSONObjectWrapper constraintResponse = new JSONObjectWrapper();
    JSONObjectWrapper userSpecificResponse = new JSONObjectWrapper();
    Constraints constraints;
    UserInfo userInfo;


    public VersionBasedResponseAnalyzer(ResponseObject responseObject, Constraints constraints, UserInfo userInfo)
    {
        this.responseObject = responseObject;
        this.constraints = constraints;
        this.userInfo = userInfo;
        ResponseType responseType = responseObject.getResponseType();
        ResponseHelper responseHelper;
        if(responseType == ResponseType.ERROR)
        {
            responseHelper = this.updateResponseHelperForErrorResponse();
            this.setResponseHolderForErrorResponse(responseHelper);
        }
        else if(responseType == ResponseType.DISCUSSION)
        {
            responseHelper = this.updateResponseHelper();
            this.setResponseHolderForDiscussion(responseHelper);
        }
        else if(responseType == ResponseType.SYNCSTYLE)
        {
            responseHelper = this.updateResponseHelperSyncStylesResponse();
            this.setResponseHolderForSyncStyleResponse(responseHelper);
        }
        else if(responseType == ResponseType.ACTION)
        {
            responseHelper = this.updateResponseHelper();
            this.setResponseHolder(responseHelper);
        }
    }

    private static VersionBasedResponseAnalyzer getInstance(ResponseObject responseObject, Constraints constraints, UserInfo userInfo)
    {
        ResponseObject.ResponseVersion responseVersion = userInfo.getResponseVersion();
        if(responseVersion == ResponseObject.ResponseVersion.V1)
        {
            return new V1ResponseAnalyzer(responseObject, constraints, userInfo);
        }
        else if(responseVersion == ResponseObject.ResponseVersion.V2)
        {
            return new V2ResponseAnalyzer(responseObject, constraints, userInfo);
        }
        return null;
    }

    public static Object getResponse(ResponseObject responseObject, Constraints constraints, UserInfo userInfo) throws Exception
    {
        String existingUserZUID = AppUtil.getUserZuid();
        try
        {
            OnDemandSearchLogger.log(LOGGER, Level.INFO, "[RESPONSE] Version {0} is used for generating response for user {1} with tab id {2}", new Object[]{userInfo.getResponseVersion().getVersion(), userInfo.getZuid(), userInfo.getUtid()});
            AppUtil.setUserZUID(userInfo.getZuid());
            VersionBasedResponseAnalyzer analyzer = getInstance(responseObject, constraints, userInfo);
            if(analyzer != null)
            {
                JSONObjectWrapper documentResponse = analyzer.generateDocumentResponse();
                JSONObjectWrapper constraintResponse = null;
                if(constraints != null)
                {
                    constraintResponse = analyzer.generateConstraintResponse();
                }
                JSONObjectWrapper userSpecificResponse = analyzer.generateUserSpecificResponse();
                return getProcessedResponse(documentResponse, constraintResponse, userSpecificResponse, responseObject.getContainer().getResourceKey());
            }
            return null;
        }
        catch(StackOverflowError | Exception e)
        {
            LOGGER.log(Level.SEVERE, "[RESPONSE][Exception] Exception while generating response.", e);
            throw e;
        }
        finally
        {
            AppUtil.setUserZUID(existingUserZUID);
        }

    }

    private static Object getProcessedResponse(JSONObjectWrapper documentResponse, JSONObjectWrapper constrainedResponse, JSONObjectWrapper userSpecificResponse, String responseKey) throws Exception
    {
        if(constrainedResponse != null && constrainedResponse.has(JSONConstants.SHEET_FAULTY_LIST))
        {
            JSONObjectWrapper sheetFaultyObj = constrainedResponse.getJSONObject(JSONConstants.SHEET_FAULTY_LIST);
            documentResponse.put(Integer.toString(CommandConstants.FAULTY), sheetFaultyObj);
            //Removing from here, as it is not part of constraints
            constrainedResponse.remove(JSONConstants.SHEET_FAULTY_LIST);
        }
        JSONObjectWrapper documentData = ResponseUtils.mergeResponseData(documentResponse, constrainedResponse, userSpecificResponse, responseKey);
        return documentData;
    }

    public WorkbookContainer getContainer()
    {
        return responseObject.getContainer();
    }
    public Workbook getWorkbook()
    {
        return responseObject.getWorkbook();
    }
    public ResponseObject getResponseObject()
    {
        return responseObject;
    }
    public DocumentResponseHolder getDocumentResponseHolder()
    {
        return documentResponseHolder;
    }
    public ConstraintResponseHolder getConstraintResponseHolder()
    {
        return constraintResponseHolder;
    }
    public UserSpecificResponseHolder getUserSpecificResponseHolder()
    {
        return userSpecificResponseHolder;
    }
    public JSONObjectWrapper getDocumentResponse()
    {
        return documentResponse;
    }
    public JSONObjectWrapper getConstraintResponse()
    {
        return constraintResponse;
    }
    public JSONObjectWrapper getUserSpecificResponse()
    {
        return userSpecificResponse;
    }
    public Constraints getConstraints()
    {
        return constraints;
    }
    public UserInfo getUserInfo() {
        return userInfo;
    }
    public JSONObjectWrapper getActionJson()
    {
        return responseObject.getActionJson();
    }

    public abstract JSONObjectWrapper generateDocumentResponse();
    public abstract JSONObjectWrapper generateConstraintResponse();
    public abstract JSONObjectWrapper generateUserSpecificResponse();

    public abstract ResponseHelper updateResponseHelper();
    public abstract ResponseHelper updateResponseHelperForErrorResponse();
    public abstract ResponseHelper updateResponseHelperSyncStylesResponse();
    public abstract void setResponseHolder(ResponseHelper responseHelper);
    public abstract void setResponseHolderForErrorResponse(ResponseHelper responseHelper);
    public abstract void setResponseHolderForSyncStyleResponse(ResponseHelper responseHelper);
    public abstract void setResponseHolderForDiscussion(ResponseHelper responseHelper);
}
