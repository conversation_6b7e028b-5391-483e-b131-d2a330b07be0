/* $Id$ */
package com.adventnet.zoho.websheet.model.response.beans;

import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.DataRange;

import java.util.List;

public class DataCleaningBean {

    private CommandConstants.OperationType operationType;
    private String associatedSheetName;
    private int updatedCount;
    private int duplicateCount;
    private boolean isUndoAction = false;
    private String actionType;
    private List<DataRange> modifiedRanges;

    public DataCleaningBean(CommandConstants.OperationType operationType,String associatedSheetName,int updatedCount, int duplicateCount, boolean isUndoAction, String actionType, List<DataRange> modifiedRanges) {
        this.operationType = operationType;
        this.associatedSheetName = associatedSheetName;
        this.updatedCount = updatedCount;
        this.duplicateCount = duplicateCount;
        this.isUndoAction = isUndoAction;
        this.actionType = actionType;
        this.modifiedRanges = modifiedRanges;
    }

    public String getAssociatedSheetName() {
        return associatedSheetName;
    }

    public CommandConstants.OperationType getOperationType() {
        return operationType;
    }

    public List<DataRange> getModifiedRanges() {
        return modifiedRanges;
    }

    public int getUpdatedCount() {
        return updatedCount;
    }
    public int getDuplicateCount() {
        return duplicateCount;
    }
    public boolean isUndoAction() {
        return isUndoAction;
    }
    public String getActionType(){
        return actionType;
    }
}
