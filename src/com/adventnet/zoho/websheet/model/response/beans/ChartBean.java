/* $Id$ */

package com.adventnet.zoho.websheet.model.response.beans;


import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;

public class ChartBean {

	String 					sheetName;
	String 					chartId;
	int 					action;
	OperationType			        operationType;
	int 					subAction = -1;
	String 					value;
	String 					targetSheetName;
	boolean 				isYAxis;
	
	public ChartBean(String sheetName, String chartId, int action, OperationType operationType) {
		this.sheetName 			= sheetName;
		this.chartId 			= chartId;
		this.action 			= action;
		this.operationType 		= operationType; 
	}

	public ChartBean(String sheetName, String chartId, int action, OperationType operationType, int subAction, String value, boolean isYAxis) {
		this.sheetName 			= sheetName;
		this.chartId 			= chartId;
		this.action 			= action;
		this.operationType 		= operationType;
		this.subAction 			= subAction;
		this.value 				= value;
		this.isYAxis 			= isYAxis;
	}
	
	public ChartBean(String sheetName, String chartId, int action, OperationType operationType, String targetSheetName) {
		this.sheetName 			= sheetName;
		this.chartId 			= chartId;
		this.action 			= action;
		this.operationType 		= operationType;
		this.targetSheetName 	= targetSheetName;
	}
	
	public String getSheetName() {
		return this.sheetName;
	}
	
	public String getChartId() {
		return this.chartId;
	}
	
	public int getAction() {
		return this.action;
	}
	
	public boolean isYAxis(){
		return this.isYAxis;
	}
	public OperationType getOperationType() {
		return this.operationType;
	}
	
	public int getSubAction() {
		return this.subAction;
	}
	
	public String getValue() {
		return this.value;
	}
	
	public String getTargetSheetName() {
		return this.targetSheetName;
	}
}
