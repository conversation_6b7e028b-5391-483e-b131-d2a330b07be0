/* $Id$ */
package com.adventnet.zoho.websheet.model.response.beans;

public class FindReplaceResponseBean {
    private int noOfMatches;
    private int matchAt;
    private int noOfReplaces = -1;
    private boolean includeReplaceCountOnly=false;
    public FindReplaceResponseBean(int noOfMatches, int matchAt) {
        this.noOfMatches = noOfMatches;
        this.matchAt = matchAt;
    }

    public int getNoOfMatches() {
        return noOfMatches;
    }

    public int getMatchAt() {
        return matchAt;
    }

    public int getNoOfReplaces() {
        return noOfReplaces;
    }

    public void setIncludeReplaceCountOnly(boolean includeReplaceCountOnly) {
        this.includeReplaceCountOnly = includeReplaceCountOnly;
    }

    public boolean isIncludeReplaceCountOnly(){
        return includeReplaceCountOnly;
    }



    public void setNoOfReplaces(int noOfReplaces) {
        this.noOfReplaces = noOfReplaces;
    }
}
