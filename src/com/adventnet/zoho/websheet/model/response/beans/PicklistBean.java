/* $Id$ */
package com.adventnet.zoho.websheet.model.response.beans;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.Picklist;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.util.CommandConstants;


import java.util.*;

public class PicklistBean {
    CommandConstants.OperationType operationType;
    int picklistID;
    JSONArrayWrapper orderArray;
    Map<String,List<RangeWrapper>> ranges;
    List<RangeWrapper> redrawRanges;
    Set<String> redrawSheets;
    String associatedSheetName;
    boolean fromUndo;
    Set<Picklist> modifiedPicklists;

    public PicklistBean(CommandConstants.OperationType operationType, int id, JSONArrayWrapper orderArray, Set<Picklist> modifiedPicklists, Map<String, List<RangeWrapper>> modifiedRanges) {
        this.operationType = operationType;
        this.picklistID = id;
        this.orderArray = orderArray;
        this.modifiedPicklists = modifiedPicklists;
        this.ranges = modifiedRanges;
    }

    public PicklistBean(CommandConstants.OperationType operationType, Map<String,List<RangeWrapper>> ranges, int picklistID, String associatedSheetName, boolean fromUndo) {
        this.operationType = operationType;
        this.ranges = ranges;
        this.picklistID = picklistID;
        this.associatedSheetName = associatedSheetName;
        this.fromUndo = fromUndo;
    }

    public int getPicklistID() {
        return picklistID;
    }

    public List<RangeWrapper> getRedrawRanges() {
        return this.redrawRanges;
    }

    public Set<String> getRedrawSheets() {
        return redrawSheets;
    }

    public CommandConstants.OperationType getOperationType() {
        return operationType;
    }

    public JSONArrayWrapper getOrderArray() {
        return orderArray;
    }

    public Map<String,List<RangeWrapper>> getRanges() {
        return ranges;
    }

    public String getAssociatedSheetName() {
        return associatedSheetName;
    }

    public boolean isFromUndo() { return this.fromUndo; }

    public Set<Picklist> getModifiedPicklists() {
        return this.modifiedPicklists;
    }
}
