/* $Id$ */

package com.adventnet.zoho.websheet.model.response.beans;


import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 *
 * <AUTHOR>
 */
public class ActiveInfoBean {

    
    private     String              activeSheetName;
    private JSONArrayWrapper activeCell;
    private     JSONArrayWrapper           persistedPositions;
    private     JSONArrayWrapper           activeRanges;
    private String appliedFilterName;
    private JSONObjectWrapper activeDataConnectionInfo;
    private JSONObjectWrapper commentInfo;

    public      ActiveInfoBean(String     activeSheetName, JSONArrayWrapper      activeCell, JSONArrayWrapper       persistedPositions, JSONArrayWrapper       activeRanges, String appliedFilterName, JSONObjectWrapper activeDataConnectionInfo, JSONObjectWrapper commentInfo)
    {
                this.activeSheetName        =       activeSheetName;
                this.activeCell             =       activeCell;
                this.persistedPositions     =       persistedPositions;
                this.activeRanges           =       activeRanges;
                this.appliedFilterName = appliedFilterName;
                this.activeDataConnectionInfo = activeDataConnectionInfo;
                this.commentInfo = commentInfo;
    }

    
    public      void        setActiveSheetName(String       activeSheetName) 
    {
                this.activeSheetName    =       activeSheetName;
    }

    public      void        setActiveCell(JSONArrayWrapper         activeCell)
    {
                this.activeCell         =       activeCell;
    }


    public      void        setPersistedPositions(JSONArrayWrapper         persistedPositions)
    {
                this.persistedPositions     =       persistedPositions;
    }
    
    public      void        setActiveRanges(JSONArrayWrapper       activeRanges)
    {
                this.activeRanges       =       activeRanges;
    }
    
    public      String      getActiveSheetName() 
    {
                return      activeSheetName;
    }
    
    public      JSONArrayWrapper   getActiveCell()
    {
                return      activeCell;
    }
    
    public      JSONArrayWrapper   getPersistedPositions()
    {
                return      persistedPositions;
    }

    public      JSONArrayWrapper   getActiveRanges()
    {
                return      activeRanges;
    }


    public String getAppliedFilterName()
    {
        return this.appliedFilterName;
    }

    public JSONObjectWrapper getActiveDataConnectionInfo() {
        return activeDataConnectionInfo;
    }

    public JSONObjectWrapper getCommentInfo(){
            return this.commentInfo;
        }
    
    
}
