/*$Id$*/

package com.adventnet.zoho.websheet.model.response.beans;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;

/**
 *
 * <AUTHOR>
 */
public class ImportBean {

    String ZUID;
    JSONArrayWrapper pastedSheetNames;
    OperationType operationType;
    String associateSheetName;

    public ImportBean(OperationType opreationType, String ZUID) {
        this.ZUID = ZUID;
        this.operationType = opreationType;
    }

    public ImportBean(OperationType opreationType, JSONArrayWrapper sheetNames, String associateSheetName) {
        this.pastedSheetNames = sheetNames;
        this.operationType = opreationType;
        this.associateSheetName = associateSheetName;
    }

    public JSONArrayWrapper getPastedSheetNames() {
        return this.pastedSheetNames;
    }

    public String getAssociateSheetName() {
        return this.associateSheetName;
    }

    public OperationType getOperationType() {
        return this.operationType;
    }

    public String getZUID() {
        return this.ZUID;
    }

}
