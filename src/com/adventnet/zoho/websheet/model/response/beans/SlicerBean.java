package com.adventnet.zoho.websheet.model.response.beans;

import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;
import java.util.List;

public class SlicerBean {
    public enum SlicerResponseType {
        INIT,BUTTON_ELEMENTS_AND_STATES, BUTTON_STATES, STYLE, DIMENSION, SETTINGS
    }
    private List<String> slicerIDList;
    private OperationType operationType;
    private String sheetName;
    private List<String> pivotNames;
    private boolean sendFullResponse;
    private SlicerResponseType responseType;


    public SlicerBean(List<String> slicerIDList, OperationType operationType, String sheetName, boolean sendFullResponse, SlicerResponseType responseType){
        this.slicerIDList = slicerIDList;
        this.operationType = operationType;
        this.sheetName = sheetName;
        this.sendFullResponse = sendFullResponse;
        this.responseType = responseType;
    }
    public SlicerBean(List<String> slicerIDList, OperationType operationType, String sheetName, boolean sendFullResponse){
        this.slicerIDList = slicerIDList;
        this.operationType = operationType;
        this.sheetName = sheetName;
        this.sendFullResponse = sendFullResponse;
    }
    public void setPivotNames(List<String> pivotNames){
        this.pivotNames = pivotNames;
    }
    public List<String > getPivotNames(){return this.pivotNames;}
    public List<String> getSlicerIDList() {
        return slicerIDList;
    }
    public OperationType getOperationType() {
        return operationType;
    }
    public String getSheetName(){return this.sheetName;}
    public boolean isSendFullResponse() {
        return sendFullResponse;
    }
    public SlicerResponseType getResponseType() {
        return responseType;
    }

}
