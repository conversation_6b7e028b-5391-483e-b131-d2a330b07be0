/* $Id$ */

package com.adventnet.zoho.websheet.model.response.beans;

import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;

/**
 *
 * <AUTHOR> N J (ZT-0049)
 *
 */
public class SheetImageBean
{

	String sheetAsn;
	OperationType operationType;
	int sheetImageID;
	// TODO TO BE REMOVED
	int imageID = -1;
	long dummyId;

	public SheetImageBean(OperationType operationType)
	{
		this.operationType 		=	 operationType;
	}

//	public ImageBean(String sheetAsn, OperationType operationType)
//	{
//		this.sheetAsn = sheetAsn;
//		this.operationType = operationType;
//	}
//	
	public SheetImageBean(String sheetAsn, OperationType operationType)
	{
		this.sheetAsn = sheetAsn;
		this.operationType = operationType;
	}

	public SheetImageBean(String sheetAsn, OperationType operationType, int sheetImageID)
	{
		this.sheetAsn = sheetAsn;
		this.operationType = operationType;
		this.sheetImageID = sheetImageID;
	}

	public SheetImageBean(String sheetAsn, OperationType operationType, int sheetImageID, int imageID)
	{
		this.sheetAsn = sheetAsn;
		this.operationType = operationType;
		this.sheetImageID = sheetImageID;
		this.imageID = imageID;
	}

	public SheetImageBean(String sheetAsn, OperationType operationType, int sheetImageID, long dummyId)
	{
		this.sheetAsn = sheetAsn;
		this.operationType = operationType;
		this.sheetImageID = sheetImageID;
		this.dummyId = dummyId;
	}

	public SheetImageBean(OperationType operationType, int sheetImageID)
	{
		this.operationType = operationType;
		this.sheetImageID = sheetImageID;
	}

	public String getSheetAsn()
	{
		return this.sheetAsn;
	}

	public OperationType getOperationType()
	{
		return this.operationType;
	}

	public int getSheetImageID()
	{
		return this.sheetImageID;
	}

	/**
	 * @deprecated
	 * @return
	 */
	public int getImageID()
	{
		return this.imageID;
	}

	public long getDummyId()
	{
		return this.dummyId;
	}
}
