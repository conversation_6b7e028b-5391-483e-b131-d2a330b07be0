/* $Id$ */
package com.adventnet.zoho.websheet.model.response.beans;


import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * <AUTHOR>
 *
 */
public class ErrorBean {
	
	private		String			key;
	private		String	msg;
	private		int	commandConstants;
	private JSONObjectWrapper errorObj;

	
		public	ErrorBean(int commandConstants, JSONObjectWrapper errorObj)	{
			this.commandConstants = commandConstants;
			this.errorObj = errorObj;
		}
		public	ErrorBean(String errorKey, String errorMsg)	{
			this.key		=	errorKey;
			this.msg		=	errorMsg;
		}
		
		public	String getErrorKey() {
			
			return	 this.key;
		}
		
		public	int	getCommandConstants()	{
			
			return	this.commandConstants;
		}

		public	JSONObjectWrapper	getErrorObj()	{

			return	this.errorObj;
		}

}



