/* $Id$ */
package com.adventnet.zoho.websheet.model.response.beans;


import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * <AUTHOR>
 *
 */

public class DocumentMetaBean {
	private boolean docInfo 			= false;
	private boolean shareInfo 		= false;
	private boolean localeInfo 		= false;
	private boolean lockedInfo 		= false;
	private JSONObjectWrapper privileges 		= null;
	private String rangeId = null;
	private boolean isPublishedView = false;
	private boolean isOnLoad = false;
	private JSONObjectWrapper publishInfo = null;
	
	public DocumentMetaBean(boolean isNeeded, JSONObjectWrapper previlages) {
		
		this.docInfo 		= isNeeded;
		this.shareInfo 		= isNeeded;
		this.localeInfo 		= isNeeded;
		this.lockedInfo 		= isNeeded;
		this.privileges 		= previlages;
	}

	public DocumentMetaBean(boolean isDocInfoNeeded, boolean isLocaleInfoNeeded, boolean isOnLoad, JSONObjectWrapper privileges)
	{
		this.docInfo = isDocInfoNeeded;
		this.localeInfo = isLocaleInfoNeeded;
		this.isOnLoad = isOnLoad;
		this.privileges = privileges;
	}
	
	public void setDocInfo(boolean isNeeded) {
		this.docInfo 		= isNeeded;
	}
	
	public void setShareInfo(boolean isNeeded) {
		this.shareInfo 		= isNeeded;
	}
		
	public void setLocaleInfo(boolean isNeeded) {
		this.localeInfo 		= isNeeded;
	}
	
	public void setLockedInfo(boolean isNeeded) {
		this.lockedInfo 		= isNeeded;
	}
	
	public void setPrivileges(JSONObjectWrapper privileges) {
		this.privileges 		= privileges;
	}
	
	public void setRangeId(String rangeId) {
		this.rangeId = rangeId;
	}
	
	public void setIsPublishedView(boolean isPubView) {
		this.isPublishedView = isPubView;
	}

	public void setPublishInfo(JSONObjectWrapper publishInfo) {
		this.publishInfo = publishInfo;
	}
	
	public boolean getDocInfo() {
		return this.docInfo;
	}
	
	public boolean getShareInfo() {
		return this.shareInfo;
	}
		
	public boolean getLocaleInfo() {
		return this.localeInfo;
	}
	
	public boolean getLockedInfo() {
		return this.lockedInfo;
	}
	
	public JSONObjectWrapper getPrivileges() {
		return this.privileges;
	}
	
	public String getRangeId() {
		return this.rangeId;
	}
	
	public boolean isPublishedView() {
		return this.isPublishedView;
	}

	public JSONObjectWrapper getPublishInfo() {
		return this.publishInfo;
	}

	public boolean isOnLoad()
	{
		return this.isOnLoad;
	}
}
