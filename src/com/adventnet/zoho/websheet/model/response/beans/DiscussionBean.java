/* $Id$ */
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.response.beans;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;

/**
 *
 * <AUTHOR>
 */
public class DiscussionBean {
    private JSONObjectWrapper discussion;
    private OperationType opType;
    private JSONArrayWrapper sheet;
    private JSONArrayWrapper range;
    
    public DiscussionBean(OperationType opType, JSONObjectWrapper disIdObj) {
        this.discussion = disIdObj;
        this.opType = opType;
    }
    public DiscussionBean(OperationType opType, JSONObjectWrapper disIdObj, JSONArrayWrapper sheet, JSONArrayWrapper range) {
        this.discussion = disIdObj;
        this.opType = opType;
        this.sheet = sheet;
        this.range = range;
    }
    
    public JSONObjectWrapper getDiscussion() {
        return this.discussion;
    }
    
    public OperationType getOperationType() {
        return this.opType;
    }
    
    public JSONArrayWrapper getSheet() {
        return this.sheet;
    }
    
    public JSONArrayWrapper getRange() {
        return this.range;
    }
}
