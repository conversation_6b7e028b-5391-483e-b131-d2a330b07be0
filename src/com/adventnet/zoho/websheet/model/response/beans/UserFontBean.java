/* $Id$ */

package com.adventnet.zoho.websheet.model.response.beans;

import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;

/**
 *
 * <AUTHOR>
 */
public class UserFontBean {
    String zuid;
    OperationType	operationType;
    
    public UserFontBean(){
        //empty constructor
    }
    public UserFontBean(String zuid){
        this.zuid   =   zuid;
    }
     public UserFontBean(OperationType operationType){
        this.operationType  =   operationType;
    }
    
    public UserFontBean(String zuid,OperationType operationType){
        this.zuid   =   zuid;
        this.operationType  =   operationType;
    }
   
    public OperationType getOperationType(){
		return this.operationType;
	}
    
    public String getzuid(){
        return this.zuid;
    }
}
