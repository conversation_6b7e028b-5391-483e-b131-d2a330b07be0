/* $Id$ */
package com.adventnet.zoho.websheet.model.response.beans;



import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;


/**
 * <AUTHOR>
 *
 */
public class PivotBean {
	
	private		String			pivotId;
	private JSONArrayWrapper pivotIdArray;
	

	private		OperationType	operationType;
	
	
		public	PivotBean(OperationType operationType, String pivotId)	{
			this.operationType	=	operationType;
			this.pivotId		=	pivotId;
			pivotIdArray  = new JSONArrayWrapper();
			pivotIdArray.put(pivotId);
		}
		public	PivotBean(OperationType operationType, JSONArrayWrapper pivotIdArray)	{
			this.operationType	=	operationType;
			this.pivotIdArray		=	pivotIdArray;
		}
		
		/*public	String getPivotId() {
			
			return	 this.pivotId;
		}*/
		
		public	OperationType	getOperationType()	{
			
			return	operationType;
		}
		
		public JSONArrayWrapper getPivotIdArray() {
			return pivotIdArray;
		}
		public void setPivotIdArray(JSONArrayWrapper pivotIdArray) {
            if(this.pivotIdArray == null) {
                this.pivotIdArray = new JSONArrayWrapper();
            }
            for (int i = 0; i < pivotIdArray.length(); i++) {
                this.pivotIdArray.put(pivotIdArray.get(i));
            }
		}

}
