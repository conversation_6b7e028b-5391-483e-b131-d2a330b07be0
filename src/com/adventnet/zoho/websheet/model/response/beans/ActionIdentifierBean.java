/* $Id$ */

package com.adventnet.zoho.websheet.model.response.beans;

/**
 * <AUTHOR>
 */
public class ActionIdentifierBean
{

	private final int aid;        //For grid action
	private final int a;
	private final String rsid;
	private final String utid;
	//  private         boolean     directUpdate;		// need to remove this ..
	private final boolean isServerAction;
	private final int lastExecutedActionId;    // For fetch data
	private final int lastSavedId;        //For saving

	// Used while loading : (if doucment loaded through parser it will lastSavedid otherwise it will be lastexecutedId
	private int documentServedState;

	public ActionIdentifierBean(int aid, int a, int lastExecuttedActionId, int lastSavedId, int documentServedState, String utid, String rsid, boolean isServerAction)
	{
		this.aid = aid;
		this.a = a;
		this.lastExecutedActionId = lastExecuttedActionId;
		this.lastSavedId = lastSavedId;
		this.rsid = rsid;
		//  this.directUpdate       	=       directUpdate;
		this.isServerAction = isServerAction;
		this.documentServedState = documentServedState;
		this.utid = utid;
	}

	public int getAid()
	{
		return aid;
	}

	public String getRsid()
	{
		return rsid;
	}

	public String getUtid()
	{
		return this.utid;
	}

	public boolean isServerAction()
	{
		return isServerAction;
	}


	public int getLastExecutedActionId()
	{
		return lastExecutedActionId;
	}


	public int getLastSavedActionId()
	{
		return lastSavedId;
	}


	public int getdocumentServedState()
	{
		return documentServedState;
	}


	public void setdocumentServedState(int documentServedState)
	{
		this.documentServedState = documentServedState;
	}

	public int getActionConstant()
	{
		return a;
	}
}
