/* $Id$ */
package	com.adventnet.zoho.websheet.model.response;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.parser.ODSWorkbookParser;
import com.adventnet.zoho.websheet.model.parser.ODSWorkbookTransformer;
import com.adventnet.zoho.websheet.model.response.beans.CellResponse;
import com.adventnet.zoho.websheet.model.response.v2.impl.CellResponseJsonImpl;
import com.adventnet.zoho.websheet.model.response.helper.ResponseHelper;
import com.adventnet.zoho.websheet.model.response.v2.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.response.viewport.DummyConstraintsImpl;
import com.adventnet.zoho.websheet.model.util.*;

/*
 * PURPOSE : --- To Test Response data.
 */


public class TestResponseData
{

  // static
  // {
  // System.setProperty("catalina.home",
  // "/Volumes/Official/Builds/handhelds/June_15/AdventNet/Sas"); //No I18N
  // AppResources.setProperty("server.home","/Volumes/Official/Builds/zfsng/Nov_24_15/AdventNet/Sas/tomcat/webapps/ROOT/WEB-INF");
  // //No I18N
  // System.setProperty("server.mode", "zoho"); //No I18N
  // }

  public static void main(String[] args)
    throws Exception
  {
    Workbook book = getBook("/Users/<USER>/Downloads/Testing Ipad use case.ods");				//No I18N
    	long time = System.currentTimeMillis();
    
   
    
    	Sheet	sheet	=	book.getSheet("Sheet1");		//No I18N
    		
    	JSONArrayWrapper jsonAry	=	ResponseUtils.getRowHeadDetails(sheet, 0, (Utility.MAXNUMOFROWS - 1));
    	//	System.out.println(jsonAry);
    			time	=	System.currentTimeMillis();
    		CellResponse	cellResp	=	new CellResponseJsonImpl(book, sheet.getAssociatedName(), 1 ,1, 1, 1, new DummyConstraintsImpl(true, (Utility.MAXNUMOFCOLS - 1)));
    		
    		
    		
    		
//    ResponseAnalyzer                analyzer                    =   getResponseAnalyzer();
//    CommandExecutor                 executor                    =   analyzer.getCommandExecutor();
//
//    ResponseGenerator               responseGenerator           =   getGridResponseGenerator();
//    executor.execute(responseGenerator);
//
//    JSONObject                      gridResponse                =   (JSONObject) responseGenerator.generateResponse();
//
//
//    //View port for loop
//    Constraints                     constraints                  =  new	DummyConstraintsImpl(255);
//
//    CommandExecutor                 executor1                    =  analyzer.getConstraintCommandExecutor();
//    ResponseGenerator               responseGenerator1           =  new ConstraintResponseGeneratorImpl(book,constraints);
//    executor1.execute(responseGenerator1);
//
//    JSONObject                      constraintResp                   =   (JSONObject) responseGenerator1.generateResponse();

    // System.out.println("constraints response "+constraintResp);
    // loop for responseGenerator.getResponse(Constaraints);

    // System.out.println(gridResponse);

    // RangeWrapper rangeWrapper = new RangeWrapper("0#", 2, 3, 2, 4);
    //
    // List rangeWrapperList = new ArrayList();
    //
    // rangeWrapperList.add(rangeWrapper);
    //
    // rangeWrapper = new RangeWrapper("0#", 4, 5, 4, 6);
    // rangeWrapperList.add(rangeWrapper);
    //
    // rangeWrapper = new RangeWrapper("0#", 6, 5, 6, 6);
    // rangeWrapperList.add(rangeWrapper);
    //
    // rangeWrapper = new RangeWrapper("0#", 8, 5, 8, 6);
    // rangeWrapperList.add(rangeWrapper);

    // CellResponse resp = CellResponseJsonImpl.gridCellResponse(book,
    // rangeWrapperList, null, new ConstraintsImpl(new ContemporaryViewPort(null,
    // null, null), true));

    // System.out.println(resp.getCellResponse());
  }

  public static Workbook getBook(String filePath) throws Exception {
    Workbook workBook = new Workbook();
    workBook.setFunctionLocale(LocaleUtil.getLocale("en", "US")); // No I18N
    workBook.setLocale(LocaleUtil.getLocale("en", "US")); // No I18N
    ODSWorkbookTransformer transformer = new ODSWorkbookTransformer();
    transformer.constructWorkbook(workBook, null);
    ODSWorkbookParser parser = new ODSWorkbookParser(transformer, false);
    parser.parse(filePath);
    transformer.endWorkbook();

    return workBook;
  }

  private static VersionBasedResponseAnalyzer getResponseAnalyzer() {

    JSONObjectWrapper actionJson = new JSONObjectWrapper();

    actionJson.put(JSONConstants.SHEET_NAME, "Sheet1"); // No I18N
    actionJson.put(JSONConstants.ASSOCIATED_SHEET_NAME, "0#"); // No I18N
    actionJson.put(JSONConstants.START_ROW, 2);
    actionJson.put(JSONConstants.START_COLUMN, 2);
    actionJson.put(JSONConstants.END_ROW, 3);
    actionJson.put(JSONConstants.END_COLUMN, 4);
    actionJson.put(JSONConstants.ACTION, ActionConstants.INSERT_ROW);

    MacroResponse mResponse = null;

    ResponseHelper helper = new ResponseHelper();
    helper.includeRowHeaders = true;
    helper.includeColumnHeaders = true;

      return null;
//      return  new ResponseAnalyzerActionImpl(actionJson, bean, ranges,cells,mResponse,null,null, helper, null,null,null,null,null,null,null, null, null,null);
}
//
//  private	static	ResponseGenerator	getGridResponseGenerator(){
//
//	  return	new		DocumentResponseGeneratorImpl(book);
//
//  }
}
