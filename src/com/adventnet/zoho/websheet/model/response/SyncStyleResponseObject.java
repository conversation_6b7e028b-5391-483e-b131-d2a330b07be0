package com.adventnet.zoho.websheet.model.response;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;

import java.util.Map;

public class SyncStyleResponseObject extends ResponseObject{

    Map<String, Map<String, String>> temporaryChangedStyleNames;
    public SyncStyleResponseObject(WorkbookContainer container, Workbook workbook, JSONObjectWrapper actionJson, Map<String, Map<String, String>> temporaryChangedStyleNames)
    {
        super(container, workbook, actionJson, ResponseType.SYNCSTYLE);
        this.temporaryChangedStyleNames = temporaryChangedStyleNames;
    }

    public Map<String, Map<String, String>> getTemporaryChangedStyleNames()
    {
        return temporaryChangedStyleNames;
    }
}
