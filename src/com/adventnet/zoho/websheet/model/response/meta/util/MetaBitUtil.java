package com.adventnet.zoho.websheet.model.response.meta.util;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.meta.CellMeta;
import com.adventnet.zoho.websheet.model.response.meta.DocumentMeta;
import com.adventnet.zoho.websheet.model.response.meta.UserMeta;
import com.adventnet.zoho.websheet.model.response.meta.WorkSheetMeta;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> N J (ZT-0049)
 */
public class MetaBitUtil
{
	public static boolean getBit(int inp, int type, int base) {
		return (inp & (1 << type - base - 1)) == 0 ? true : false;
	}

	public static boolean checkPermission(CellMeta.CellMetaType cellMetaType, int cellMetaOffsetBit) {

		int cellMeta;

		switch (cellMetaType) {

			case ACTUAL_VALUE:
				cellMeta = CellMeta.ACTUAL_VALUE;
				break;
			case DISPLAY_VALUE:
				cellMeta = CellMeta.DISPLAY_VALUE;
				break;
			case FORMULA:
				cellMeta = CellMeta.FORMULA;
				break;
			case STYLE_NAME:
				cellMeta = CellMeta.STYLE_NAME;
				break;
			case COND_STYLE_NAME:
				cellMeta = CellMeta.COND_STYLE_NAME;
				break;
			case ANNOTATION:
				cellMeta = CellMeta.ANNOTATION;
				break;
			case HLINK:
				cellMeta = CellMeta.HLINK;
				break;
			case TYPE:
				cellMeta = CellMeta.TYPE;
				break;
			case DISCUSSION:
				cellMeta = CellMeta.DISCUSSION;
				break;
			case PATTERN_COLOR:
				cellMeta = CellMeta.PATTERN_COLOR;
				break;
			case PATTERN:
				cellMeta = CellMeta.PATTERN;
				break;
			case IS_CONTENT_VALID:
				cellMeta = CellMeta.IS_CONTENT_VALID;
				break;
			case PICKLIST:
				cellMeta = CellMeta.PICKLIST;
				break;
			case REPEAT_INDEX:
				cellMeta = CellMeta.REPEAT_INDEX;
				break;
			case REPEAT_CHAR:
				cellMeta = CellMeta.REPEAT_CHAR;
				break;
			case CELL_IMAGE:
				cellMeta = CellMeta.CELL_IMAGE;
				break;
			case TYPE_MISMATCH:
				cellMeta = CellMeta.TYPE_MISMATCH;
				break;
			case PICKLIST_ID:
				cellMeta = CellMeta.PICKLIST_ID;
				break;
			case AUTO_ARRAY:
				cellMeta = CellMeta.AUTO_ARRAY;
				break;
			case RICH_STRING:
				cellMeta = CellMeta.RICH_STRING;
				break;

			case INVISIBLE_CHARS:
				cellMeta = CellMeta.INVISIBLE_CHARS;
				break;

			case MULTI_VALUE:
				cellMeta = CellMeta.MULTI_VALUE;
				break;
			case OUT_OF_SYNC:
				cellMeta = CellMeta.OUT_OF_SYNC;
				break;

			default:
				cellMeta = 0;
				break;
		}

		boolean permission = checkPermission(cellMeta, cellMetaOffsetBit);
		return permission;
	}

	private static boolean checkPermission(int cellMeta, int cellMetaOffsetBit) {
		return (cellMetaOffsetBit & cellMeta) != 0;
	}

	public static boolean isFaulty(int idx, int hdnSCol, JSONArrayWrapper hdnCol) {
		int q = Math.round((idx - hdnSCol) / 8);
		int m = (idx % 8);

		if (m == 8) {
			q++;
			m = 0;
		}
		boolean bit = getBit(hdnCol.getInt(q), 8, m);
		return bit;
	}

	public static JSONObjectWrapper getMetaForOnLoadResponse(JSONObjectWrapper dataJson)
	{
		JSONObjectWrapper meta = new JSONObjectWrapper();
		List<Long> documentMeta = new ArrayList<>();
		documentMeta.add(DocumentMeta.ROWHEADER_DEFINITION);
		documentMeta.add(DocumentMeta.COLHEADER_DEFINITION);
		documentMeta.add(DocumentMeta.WORKSHEETS);
		documentMeta.add(DocumentMeta.CELLSTYLE_DEFINITION);
		documentMeta.add(DocumentMeta.TEXTSTYLE_DEFINITION);
		documentMeta.add(DocumentMeta.ACTIVE_INFO);
		documentMeta.add(DocumentMeta.ACTION_IDENTIFIER);
		documentMeta.add(DocumentMeta.ZSTHEME);
		documentMeta.add(DocumentMeta.FONT_FAMILYLIST);
		documentMeta.add(DocumentMeta.PIVOT);
		documentMeta.add(DocumentMeta.META);
		documentMeta.add(DocumentMeta.ONLOAD_DETAILS);
		documentMeta.add(DocumentMeta.TABLE);

		List<Long> sheetMeta = new ArrayList<>();
		sheetMeta.add(WorkSheetMeta.COLHEADER);
		sheetMeta.add(WorkSheetMeta.ROWHEADER);
		sheetMeta.add(WorkSheetMeta.HIDDENCOLS);
		sheetMeta.add(WorkSheetMeta.HIDDENROWS);
		sheetMeta.add(WorkSheetMeta.FILTERINFO);
		sheetMeta.add(WorkSheetMeta.ACTIVE_INFO);
		sheetMeta.add(WorkSheetMeta.FREEZEINFO);
		sheetMeta.add(WorkSheetMeta.SPARKLINE);
		sheetMeta.add(WorkSheetMeta.CHECK_BOX);
		sheetMeta.add(WorkSheetMeta.ZOOM);
		sheetMeta.add(WorkSheetMeta.SHEET_VIEW);
		sheetMeta.add(WorkSheetMeta.SHEET_DIRECTION);
		sheetMeta.add(WorkSheetMeta.GROUPING);
		sheetMeta.add(WorkSheetMeta.MAXUSEDCELL);
		if(dataJson.optJSONObject(JSONConstants.RANGE_META) == null && !dataJson.optBoolean("isPublishedView"))
		{
			sheetMeta.add(WorkSheetMeta.HIDE_GRID);
		}

		List<Long> userMeta = new ArrayList<>();
		userMeta.add(UserMeta.USER_META_INFO);

		meta.put(String.valueOf(CommandConstants.DOCUMENT_META), calculateMetaKey(documentMeta));
		meta.put(String.valueOf(CommandConstants.SHEET_META), calculateMetaKey(sheetMeta));
		meta.put(String.valueOf(CommandConstants.USER_META), calculateMetaKey(userMeta));
		return meta;
	}

	private static long calculateMetaKey(List<Long> keys)
	{
		long metaKey = 0;
		for(long key : keys)
		{
			metaKey = metaKey | key;
		}
		return metaKey;
	}
}
