/* $Id$ */
package com.adventnet.zoho.websheet.model.response.meta;

/**
 * <AUTHOR>
 *
 */

public interface CellMeta {
	
	public int ACTUAL_VALUE 	= 1;

	public int DISPLAY_VALUE 	= 1 << 1;

	public int FORMULA 			= 1 << 2;

	public int STYLE_NAME 		= 1 << 3;

	public int COND_STYLE_NAME 	= 1 << 4;

	public int ANNOTATION 		= 1 << 5;

	public int HLINK 			= 1 << 6;

	public int TYPE 			= 1 << 7;
	
	public int DISCUSSION 		= 1 << 8;
	
	public int PATTERN_COLOR 	= 1 << 9;
	
	public int PATTERN 			= 1 << 10;
        
	public int IS_CONTENT_VALID 			= 1 << 11;

	public int PICKLIST 		= 1 << 12;

	public int REPEAT_INDEX   	= 1 << 13;

	public int REPEAT_CHAR 		= 1 << 14;
        
        public int CELL_IMAGE 		= 1 << 15;

	public int TYPE_MISMATCH    = 1 << 16;

	public int AUTO_ARRAY 		= 1 << 17;

	public int PICKLIST_ID   	= 1 << 18;


	public int RICH_STRING 		= 1 << 19;

	public int INVISIBLE_CHARS = 1 << 20;

	public int MULTI_VALUE = 1 << 21;

	public int OUT_OF_SYNC = 1 << 22;


	
	public	 enum	CellMetaType{
    	
		ACTUAL_VALUE,

		DISPLAY_VALUE,

		FORMULA,

		STYLE_NAME,

		COND_STYLE_NAME,

		ANNOTATION,

		HLINK,

		TYPE,
		
		DISCUSSION,
		
		PATTERN_COLOR,
		
		PATTERN,
                
		IS_CONTENT_VALID,

		PICKLIST,

		REPEAT_INDEX,

		REPEAT_CHAR,

		TYPE_MISMATCH,

		CELL_IMAGE,

		PICKLIST_ID,

		AUTO_ARRAY,
		RICH_STRING,

		INVISIBLE_CHARS,
		MULTI_VALUE,
		OUT_OF_SYNC
    	
    }
}

