/* $Id$ */

package com.adventnet.zoho.websheet.model.response;

import java.util.List;

import com.adventnet.zoho.websheet.model.UserProfile.PermissionType;

/**
 * <AUTHOR>
 */
/*
 * PURPOSE : used for lock range/sheet
 */
public final class UserInfo
{
	private final List userGroups;
	private final String zuid;
	private final PermissionType permissionType;
	private final String utid;
	private final String rsid;
	private final ResponseObject.ResponseVersion responseVersion;

	public UserInfo(String zuid, List userGroups, PermissionType permissionType, String utid, String rsid, ResponseObject.ResponseVersion responseVersion)
	{
		this.zuid = zuid;
		this.permissionType = permissionType;
		this.utid = utid;
		this.rsid = rsid;
		this.userGroups = userGroups;
		this.responseVersion = responseVersion;
	}

	public UserInfo(String zuid, List userGroups, PermissionType permissionType, String utid, ResponseObject.ResponseVersion responseVersion)
	{
		this(zuid, userGroups, permissionType, utid, null, responseVersion);
	}

	public UserInfo(String zuid, PermissionType permissionType, String utid, String rsid)
	{
		this(zuid, null, permissionType, utid, rsid, ResponseObject.ResponseVersion.V1);
	}
	public UserInfo(ResponseObject.ResponseVersion responseVersion)
	{
		this(null, null, null, null, responseVersion);
	}

	public String getZuid()
	{
		return zuid;
	}

	public List getUserGroups()
	{
		return userGroups;
	}

	public PermissionType getPermissionType()
	{
		return permissionType;
	}

	public String getUtid()
	{
		return this.utid;
	}

	public String getRsid()
	{
		return this.rsid;
	}

	public ResponseObject.ResponseVersion getResponseVersion()
	{
		return this.responseVersion;
	}
}
