/* $Id$ */
package com.adventnet.zoho.websheet.model.response.helper;

import java.util.List;

import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;

public class NamedRangeWrapper {
	
  private List<RangeWrapper> rangeWrapper;
    
    private	OperationType	operationType; 
    
    private int action;
    
    private String rangeName;

    private String oldName;

    private String scopeASN;


    public NamedRangeWrapper(List<RangeWrapper> rangeWrapper,OperationType	operationType) {

        this.rangeWrapper       =       rangeWrapper;
        this.operationType       =       operationType;
    }

    public NamedRangeWrapper(List<RangeWrapper> rangeWrapper,OperationType	operationType,String rangeName, String scopeASN) {
        this.rangeWrapper       =       rangeWrapper;
        this.operationType      =       operationType;
        this.rangeName			=       rangeName;
        this.scopeASN           =       scopeASN;
    }

    public NamedRangeWrapper(List<RangeWrapper> rangeWrapper,OperationType	operationType,String rangeName, String oldName, String scopeASN) {
        this.rangeWrapper       =       rangeWrapper;
        this.operationType      =       operationType;
        this.rangeName			=       rangeName;
        this.oldName            =       oldName;
        this.scopeASN           =       scopeASN;
    }

    public List<RangeWrapper> getRangeWrapper(){
    	return this.rangeWrapper;
    }

    public	OperationType	getOperationType()
    {
    		return	this.operationType;
    }

    public String getRangeName(){
    	return this.rangeName;
    }

    public String getScopeASN(){
        return this.scopeASN;
    }

    public String getOldName() {
        return this.oldName;
    }
    
    public String	toString()
    {
    		return	new	StringBuffer().append(rangeWrapper).append(" , ").
    									append(operationType).append(" , ").toString();
    }
    
    

}
