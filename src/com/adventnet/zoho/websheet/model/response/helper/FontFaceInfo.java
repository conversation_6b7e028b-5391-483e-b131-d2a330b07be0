//$ID$
package com.adventnet.zoho.websheet.model.response.helper;

import java.nio.charset.StandardCharsets;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

/**
 * <AUTHOR>
 */
public class FontFaceInfo {
    private static final Logger LOGGER = Logger.getLogger(FontFaceInfo.class.getName());

    private final String fontFamily;
    private final String styleName;

    public FontFaceInfo(String fontFamily, String styleName){
        this.fontFamily = encodeToUTF8(fontFamily);
        this.styleName = encodeToUTF8(styleName);
    }

    public JSONObjectWrapper toJSON(){
        JSONObjectWrapper fontInfo = new JSONObjectWrapper();
        fontInfo.put("fontFamily", fontFamily);//NO I18N
        fontInfo.put("styleName", styleName);//NO i18N
        return fontInfo;
    }

    public String getFontFamily() {
        return fontFamily;
    }

    public String getStyleName() {
        return styleName;
    }

    @Override
    public String toString() {
        return this.toJSON().toString();
    }

    public static String encodeToUTF8(String str){
        try {
            return new String(str.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            LOGGER.severe("Error while encoding to UTF-8: "+e.getMessage());//NO I18N
        }
        return str;
    }
    
}
