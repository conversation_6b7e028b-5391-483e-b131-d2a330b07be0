/* $Id$ */

package com.adventnet.zoho.websheet.model.response.helper;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

import com.adventnet.zoho.websheet.model.util.CommandConstants;

/**
 * <AUTHOR>
 */
public class DocumentSettingsWrapper
{
	private final String zuid;
	private final CommandConstants.OperationType operationType;

	/**
	 * @param zuid
	 * @param operationType
	 */
	public DocumentSettingsWrapper(String zuid, CommandConstants.OperationType operationType)
	{
		this.zuid = zuid;
		this.operationType = operationType;
	}

	public String getZuid()
	{
		return zuid;
	}

	public CommandConstants.OperationType getOperationType()
	{
		return operationType;
	}
}
