/* $Id$ */

package com.adventnet.zoho.websheet.model.response.helper;

import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;

public class HideGridWrapper {
	
	private 	String 				associatedSheetName;
	private 	OperationType 		operationType;
	private String rangeId;
	
	public HideGridWrapper(String associatedSheetName, OperationType operationType, String rangeId){
		
		this.associatedSheetName 	=	associatedSheetName;
		this.operationType			=	operationType;
		this.rangeId 				= 	rangeId;
		
	}

	public String getAssociatedSheetName(){
		return this.associatedSheetName;
	}
	
	public OperationType getOperationType(){
		return this.operationType;
	}
	
	public String getRangeId() {
		return this.rangeId;
	}
}
