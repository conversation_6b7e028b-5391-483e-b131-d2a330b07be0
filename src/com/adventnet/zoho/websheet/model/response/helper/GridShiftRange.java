/* $Id$ */
package com.adventnet.zoho.websheet.model.response.helper;

import com.adventnet.zoho.websheet.model.util.CommandConstants.OperationType;

/**
 * <AUTHOR>
 *
 */
//Name need to change --- Important
public class GridShiftRange {
			
			private		String				associatedSheetName;
			private		int					startIndex;
			private		int					count;
			private		OperationType		type;
	
			public	GridShiftRange(String	associatedSheetName,	int 	startIndex,	int	count,	OperationType 	type)
			{
						this.associatedSheetName		=		associatedSheetName;
						this.startIndex					=		startIndex;
						this.count						=		count;
						this.type						=		type;
			}
			
			public	String	getSheetName()
			{
				return	associatedSheetName;
			}
			
			public	OperationType	getOperationType(){
				return	type;
			}
			
			public	int	getStartIndex(){
				return	startIndex;
			}
			
			public	int	getCount(){
				return	count;
			}
}
