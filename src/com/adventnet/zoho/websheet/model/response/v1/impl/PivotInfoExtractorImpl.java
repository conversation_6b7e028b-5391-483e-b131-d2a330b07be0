package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.PivotBean;
import com.adventnet.zoho.websheet.model.response.extractor.PivotInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import java.util.ArrayList;
import java.util.List;

public class PivotInfoExtractorImpl implements PivotInfoExtractor {

    List<PivotBean> pivotBeanList = new ArrayList<>();
    public PivotInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int		action		=		actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        CommandConstants.OperationType operationType	= 	null;
        String			pivotId			=	null;
        Boolean isUndo = actionJson.has(JSONConstants.FROM_UNDO);
        switch (action) {

            case ActionConstants.CREATE_PIVOT:
            case ActionConstants.COPY_PIVOT:
                operationType = CommandConstants.OperationType.ADD;
                pivotId			=	actionJson.getString(JSONConstants.ID);
                break;
            case ActionConstants.EDIT_PIVOT:
            case ActionConstants.APPLYFILTER_PIVOT:
            case ActionConstants.SORT_PIVOT:
            case ActionConstants.REFRESH_PIVOT:
            case ActionConstants.MOVE_PIVOT:
            case ActionConstants.CHANGE_PIVOT_SOURCE:
            case ActionConstants.CHANGE_PIVOT_SUBTOTALOPTION:
            case ActionConstants.PIVOT_REPEAT_LABEL:
            case ActionConstants.CHANGE_PIVOT_GRANDTOTALROWOPTION:
            case ActionConstants.CHANGE_PIVOT_GRANDTOTALCOLOPTION:
            case ActionConstants.GROUPING_PIVOT_FIELDS:
            case ActionConstants.SLICER_NEW:
            case ActionConstants.APPLY_SLICER_FILTER:
            case ActionConstants.UPDATE_CONNECTED_PIVOTS:
            case ActionConstants.SLICER_DELETE:
            case ActionConstants.UPDATE_SLICER_COLUMN:
            case ActionConstants.PIVOT_HIDE_ERRORS:
                if(actionJson.has(JSONConstants.ID)) {
                operationType	=	CommandConstants.OperationType.MODIFY;
                pivotId			=	actionJson.getString(JSONConstants.ID);
                }
                break;
            case ActionConstants.PIVOT_AUTO_REFRESH:
            case ActionConstants.PIVOT_AUTO_EXPAND:
            case ActionConstants.CHANGE_PIVOT_THEME:
            case ActionConstants.CHANGE_PIVOT_NAME:
                operationType	=	CommandConstants.OperationType.UPDATE;
                pivotId			=	actionJson.getString(JSONConstants.ID);
                break;

            case ActionConstants.DELETE_PIVOT:

                operationType = CommandConstants.OperationType.DELETE;
                pivotId			=	actionJson.getString(JSONConstants.ID);
                break;


            //DocumentLoad /Fetch data
            case	-1 :
                operationType	=	CommandConstants.OperationType.GENERATE_LIST;

//		case ActionConstants.PIVOT_REFRESH:
//
//		case ActionConstants.PIVOT_UPDATEPROPERTIES:


                break;
            case ActionConstants.SHEET_DUPLICATE:
            case ActionConstants.SHEET_COPY:
            case ActionConstants.SERVERCLIP_PASTE_SHEET:
                if(actionJson.has(JSONConstants.ADDITIONAL_INFO) && actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).has(JSONConstants.PIVOT_INFO)) {
                    JSONObjectWrapper pivotInfo = actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).getJSONObject(JSONConstants.PIVOT_INFO);
                    if(pivotInfo.has(Integer.toString(CommandConstants.ADD))) {
                        JSONArrayWrapper pivotIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.ADD));
                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.ADD, pivotIdArray));

                    }

                }
                break;
            case ActionConstants.IMPORT_CLOUD_DATA:
            case ActionConstants.UPDATE_CLOUD_DATA:
            case ActionConstants.INSERT_CELL_TOP:
            case ActionConstants.INSERT_CELL_LEFT:
            case ActionConstants.DELETE_CELL_RIGHT:
            case ActionConstants.DELETE_CELL_BOTTOM:
            case ActionConstants.TABLE_CREATE:
            case ActionConstants.TABLE_INSERT_ROW:
            case ActionConstants.TABLE_INSERT_COL:
            case ActionConstants.TABLE_DELETE_ROW:
            case ActionConstants.TABLE_DELETE_COL:
                if(actionJson.has(JSONConstants.ADDITIONAL_INFO) && actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).has(JSONConstants.PIVOT_INFO)) {
                    JSONObjectWrapper pivotInfo = actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).getJSONObject(JSONConstants.PIVOT_INFO);
                    if(pivotInfo.has(Integer.toString(CommandConstants.DELETE))) {
                        JSONArrayWrapper pivotDelIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.DELETE));
                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.DELETE, pivotDelIdArray));
                    }
                    if(pivotInfo.has(Integer.toString(CommandConstants.UPDATE))) {
                        JSONArrayWrapper pivotModIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.UPDATE));

                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.MODIFY, pivotModIdArray));
                    }
                    if(pivotInfo.has(Integer.toString(CommandConstants.MODIFY))) {
                        JSONArrayWrapper pivotModIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.MODIFY));
                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.MODIFY, pivotModIdArray));
                    }
//                    actionJson.remove(JSONConstants.PIVOT_INFO);
                }
                break;
            case ActionConstants.INSERT_ROW:
            case ActionConstants.DELETE_ROW:
            case ActionConstants.INSERT_COL:
            case ActionConstants.DELETE_COL:
            case ActionConstants.INSERT_CUT_ROW:
            case ActionConstants.INSERT_CUT_COLUMN:
            case ActionConstants.SHEET_REMOVE:
                //case ActionConstants.DELETE_CELL_RIGHT:
                //case ActionConstants.DELETE_CELL_BOTTOM:
                if(actionJson.has(JSONConstants.ADDITIONAL_INFO) && actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).has(JSONConstants.PIVOT_INFO)) {
                    JSONObjectWrapper pivotInfo = actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).getJSONObject(JSONConstants.PIVOT_INFO);
                    if(pivotInfo.has(Integer.toString(CommandConstants.DELETE))) {
                        JSONArrayWrapper pivotDelIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.DELETE));
                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.DELETE, pivotDelIdArray));
                    }
                    if(pivotInfo.has(Integer.toString(CommandConstants.MODIFY))) {
                        JSONArrayWrapper pivotModIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.MODIFY));
                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.MODIFY, pivotModIdArray));
                    }
                    if(pivotInfo.has(Integer.toString(CommandConstants.UPDATE))) {
                        JSONArrayWrapper pivotModIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.UPDATE));
                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.UPDATE, pivotModIdArray));
                    }
//                    actionJson.remove(JSONConstants.PIVOT_INFO);
                }
                break;
            default:
                if(actionJson.has(JSONConstants.ADDITIONAL_INFO) && actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).has(JSONConstants.PIVOT_INFO)) {
                    JSONObjectWrapper pivotInfo = actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).getJSONObject(JSONConstants.PIVOT_INFO);
                    if(pivotInfo.has(Integer.toString(CommandConstants.DELETE))) {
                        JSONArrayWrapper pivotDelIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.DELETE));
                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.DELETE, pivotDelIdArray));
                    }
                    if(pivotInfo.has(Integer.toString(CommandConstants.MODIFY))) {
                        JSONArrayWrapper pivotModIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.MODIFY));
                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.MODIFY, pivotModIdArray));
                        actionJson.put(JSONConstants.CONNECTED_PIVOTS, pivotModIdArray); // for slicer response.
                    }
                    if(pivotInfo.has(Integer.toString(CommandConstants.FAULTY))) {
                        JSONArrayWrapper pivotModIdArray = pivotInfo.getJSONArray(Integer.toString(CommandConstants.FAULTY));
                        pivotBeanList.add(new PivotBean(CommandConstants.OperationType.FAULTY, pivotModIdArray));
                    }
//                    actionJson.remove(JSONConstants.PIVOT_INFO);
                }
                break;
        }
        if(operationType != null) {
            PivotBean pivotBean = new PivotBean(operationType, pivotId);
            // This code is added because, when we apply slicer filter. all the pivots connected to it is updated
            if(actionJson.has(JSONConstants.CONNECTED_PIVOTS) && actionJson.has(JSONConstants.SLICER_ID) && action != ActionConstants.DELETE_PIVOT){
                JSONArrayWrapper pivotNames = (JSONArrayWrapper) actionJson.get(JSONConstants.CONNECTED_PIVOTS);
                pivotBean.setPivotIdArray(pivotNames);
            }
            pivotBeanList.add(pivotBean);
        }
    }

    @Override
    public List<PivotBean> getPivotBeanList() {
        return pivotBeanList;
    }
}
