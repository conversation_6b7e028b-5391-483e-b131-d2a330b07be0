package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.ActiveInfoBean;
import com.adventnet.zoho.websheet.model.response.extractor.ActiveInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

public class ActiveInfoExtractorImpl implements ActiveInfoExtractor {

    ActiveInfoBean activeInfoBean;
    public ActiveInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        String tempSheetName = (actionJson.has(JSONConstants.ASSOCIATED_SHEET_NAME)) ? actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME) :  ActionJsonUtil.getAssociateSheetName(actionJson);
        JSONArrayWrapper tempActiveCell				=	null;
        JSONArrayWrapper  			tempPersistedPositions		=	null;
        JSONArrayWrapper  			tempActiveRanges			=	null;

        if(actionJson.has(JSONConstants.ACTIVE_CELL))	{
            tempActiveCell				=		actionJson.getJSONArray(JSONConstants.ACTIVE_CELL);
        }
        else{
            //Default
            tempActiveCell				=		new JSONArrayWrapper();
            tempActiveCell.put(0);
            tempActiveCell.put(0);
        }
        if(actionJson.has(JSONConstants.ACTIVE_RANGES))	{
            tempActiveRanges			=		actionJson.getJSONArray(JSONConstants.ACTIVE_RANGES);
        }
        if(actionJson.has(JSONConstants.PERSISTED_POSITIONS))	{
            tempPersistedPositions		=		actionJson.getJSONArray(JSONConstants.PERSISTED_POSITIONS);
        }

        /** Nice URL Mapping with named filters in URL
         * Example: /sheet/open/rid/sheets/sheetName/filters/filterName
         *
         * <AUTHOR> N J (ZT-0049)
         */
        String appliedFilterName = null;
        if(actionJson.has(JSONConstants.APPLIED_FILTER_NAME))
        {
            appliedFilterName = actionJson.getString(JSONConstants.APPLIED_FILTER_NAME);
        }

        JSONObjectWrapper commentInfo = null;
        if(actionJson.has(JSONConstants.COMMENT_INFO)){
            commentInfo = actionJson.getJSONObject(JSONConstants.COMMENT_INFO);
        }

        JSONObjectWrapper activeDataConnectionInfo = null;
        if(actionJson.has(JSONConstants.SERVICE_CONSTANT))
        {
            activeDataConnectionInfo = new JSONObjectWrapper();
            activeDataConnectionInfo.put(Integer.toString(CommandConstants.FILTERED_SERVICE_CONSTANT), actionJson.getInt(JSONConstants.SERVICE_CONSTANT));
        }

        activeInfoBean = new ActiveInfoBean(tempSheetName, tempActiveCell, tempPersistedPositions, tempActiveRanges, appliedFilterName, activeDataConnectionInfo, commentInfo);
    }

    @Override
    public ActiveInfoBean getActiveInfoBean() {
        return activeInfoBean;
    }
}
