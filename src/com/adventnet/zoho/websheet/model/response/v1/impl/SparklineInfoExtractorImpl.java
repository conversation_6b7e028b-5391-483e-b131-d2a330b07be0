package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.SparklinesGroup;
import com.adventnet.zoho.websheet.model.response.beans.SparklineBean;
import com.adventnet.zoho.websheet.model.response.extractor.SparklineInfoExtractor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class SparklineInfoExtractorImpl implements SparklineInfoExtractor {

    List<SparklineBean> sparklineBeanList = new ArrayList<>();
    public SparklineInfoExtractorImpl(JSONObjectWrapper actionJson, List<SparklinesGroup> modifiedSparklinesGroups, List<Range> ranges, List<DataRange> modifiedSparklineCells)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        boolean	isUndoRedoAction =	(actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO)) || (actionJson.has(JSONConstants.FROM_REDO) && actionJson.getBoolean(JSONConstants.FROM_REDO));
        boolean isValueChanged = actionJson.has(JSONConstants.IS_VALUE_CHANGED) && actionJson.getBoolean(JSONConstants.IS_VALUE_CHANGED);
        int sparklineID = -1;
        String sheetName = (actionJson.has(JSONConstants.ASSOCIATED_SHEET_NAME)) ? actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME) :  ActionJsonUtil.getAssociateSheetName(actionJson);
        CommandConstants.OperationType operation = null;
        List<DataRange> sourceDataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
        if(isUndoRedoAction && isSparklineAction(action)) {
            operation = CommandConstants.OperationType.UNDO;
            sourceDataRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson,false);
            if(action == ActionConstants.EDIT_SPARKLINE_GROUP_DATA) {
                sparklineID = actionJson.optInt(JSONConstants.SPARKLINE_ID,-1);
            }
        }
        else if(action == ActionConstants.EDIT_SPARKLINE_DATA && ranges != null) {
            sourceDataRanges = ranges.stream().map(Range::toDataRange).collect(Collectors.toList());
            operation = CommandConstants.OperationType.EDIT;
        }
        else {
            switch (action) {
                case -1:
                    operation = CommandConstants.OperationType.GENERATE_LIST;
                    break;
                case ActionConstants.APPLY_THEME:
                    operation = CommandConstants.OperationType.THEME;
                    break;
                case ActionConstants.CHANGE_SPARKLINE_ORIENTATION:
                case ActionConstants.SPARKLINE_CREATE:
                    operation = CommandConstants.OperationType.ADD;
                    break;
                case ActionConstants.SPARKLINE_REMOVE:
                case ActionConstants.CLEARALL:
                    operation = CommandConstants.OperationType.REMOVE;
                    break;
                case ActionConstants.SPARKLINE_GROUP_REMOVE:
                    operation = CommandConstants.OperationType.REMOVE_GROUP;
                    break;
                case ActionConstants.SPARKLINE_GROUP:
                    operation = CommandConstants.OperationType.GROUP;
                    break;
                case ActionConstants.SPARKLINE_PROPERTIES:
                    operation = CommandConstants.OperationType.MODIFY;
                    break;
                case ActionConstants.EDIT_SPARKLINE_DATA:
                    operation = CommandConstants.OperationType.EDIT;
                    break;
                case ActionConstants.EDIT_SPARKLINE_GROUP_DATA:
                    operation = CommandConstants.OperationType.EDIT_GROUP;
                    break;
                case ActionConstants.IMPORT_CLOUD_DATA:
                case ActionConstants.UPDATE_CLOUD_DATA:
                case ActionConstants.INSERT_ROW:
                case ActionConstants.MERGE_ACROSS:
                case ActionConstants.MERGE_DOWN:
                case ActionConstants.MERGE_RANGE:
                case ActionConstants.MERGE_AND_CENTER:
                case ActionConstants.INSERT_COL:
                case ActionConstants.INSERT_CELL_TOP:
                case ActionConstants.INSERT_CELL_LEFT:
                case ActionConstants.DELETE_CELL_BOTTOM:
                case ActionConstants.DELETE_CELL_RIGHT:
                case ActionConstants.TABLE_INSERT_ROW:
                case ActionConstants.TABLE_INSERT_COL:
                case ActionConstants.TABLE_DELETE_ROW:
                case ActionConstants.TABLE_DELETE_COL:
                    operation = CommandConstants.OperationType.INSERT;
                    break;
                case ActionConstants.DELETE_COL:
                case ActionConstants.DELETE_ROW:
                    operation = CommandConstants.OperationType.DELETE;
                    break;
                case ActionConstants.COPY_PASTE:
                case ActionConstants.FILLSERIES:
                case ActionConstants.CUT_PASTE:
                case ActionConstants.INSERT_CUT_ROW:
                case ActionConstants.INSERT_CUT_COLUMN:
                case ActionConstants.INSERT_COPY_ROW:
                case ActionConstants.INSERT_COPY_COLUMN:
                case ActionConstants.TABLE_CREATE:  // Table Creation may shift the cells similar to cut-paste or insert cells.
                case ActionConstants.TABLE_TOGGLE_HEADER_FOOTER:
                    operation = CommandConstants.OperationType.PASTE;
                    break;
                case ActionConstants.HIDE_ROWS:
                case ActionConstants.HIDE_COLUMNS:
                case ActionConstants.UNHIDE_ROWS:
                case ActionConstants.UNHIDE_COLUMNS:
                    operation = CommandConstants.OperationType.HIDE;
                    break;
                default:
                    operation = CommandConstants.OperationType.PUBLISH;
            }
        }

        List<Integer> tableIDs = null;
        if(actionJson.has(JSONConstants.EXPANDED_TABLES)) {
            operation = CommandConstants.OperationType.TABLE_EXPANSION;
            tableIDs = new ArrayList<>();
            JSONArrayWrapper arr = actionJson.getJSONArray(JSONConstants.EXPANDED_TABLES);
            for(int i=0; i<arr.length(); i++) {
                tableIDs.add(arr.getJSONArray(i).getInt(0));
            }
        }

        SparklineBean bean = new SparklineBean(modifiedSparklinesGroups,sourceDataRanges,sheetName,operation,isValueChanged,isUndoRedoAction,isSparklineAction(action),action,sparklineID, modifiedSparklineCells, tableIDs);
        sparklineBeanList.add(bean);
    }


    private boolean isSparklineAction(int action)
    {
        return action == ActionConstants.SPARKLINE_CREATE || action == ActionConstants.SPARKLINE_GROUP_REMOVE ||
                action == ActionConstants.SPARKLINE_GROUP || action == ActionConstants.SPARKLINE_REMOVE ||
                action == ActionConstants.SPARKLINE_PROPERTIES ||
                action == ActionConstants.CHANGE_SPARKLINE_ORIENTATION || action == ActionConstants.EDIT_SPARKLINE_DATA ||
                action == ActionConstants.EDIT_SPARKLINE_GROUP_DATA || action == ActionConstants.COPY_PASTE ||
                action == ActionConstants.FILLSERIES || action == ActionConstants.CUT_PASTE ||
                action == ActionConstants.INSERT_ROW || action == ActionConstants.INSERT_COL ||
                action == ActionConstants.INSERT_CUT_ROW || action == ActionConstants.INSERT_CUT_COLUMN ||
                action == ActionConstants.INSERT_COPY_ROW || action == ActionConstants.INSERT_COPY_COLUMN ||
                action == ActionConstants.INSERT_CELL_TOP || action == ActionConstants.INSERT_CELL_LEFT ||
                action == ActionConstants.DELETE_CELL_BOTTOM || action == ActionConstants.DELETE_CELL_RIGHT ||
                action == ActionConstants.IMPORT_CLOUD_DATA || action == ActionConstants.UPDATE_CLOUD_DATA ||
                action == ActionConstants.DELETE_ROW || action == ActionConstants.DELETE_COL ||
                action == ActionConstants.CLEARALL || action == ActionConstants.TABLE_RESIZE ||
                action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.TABLE_INSERT_COL ||
                action == ActionConstants.TABLE_DELETE_ROW || action == ActionConstants.TABLE_DELETE_COL;
    }

    @Override
    public List<SparklineBean> getSparklineBeanList() {
        return sparklineBeanList;
    }
}
