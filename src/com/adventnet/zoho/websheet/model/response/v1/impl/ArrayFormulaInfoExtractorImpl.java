package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.ArrayFormulaInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;

public class ArrayFormulaInfoExtractorImpl implements ArrayFormulaInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public ArrayFormulaInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        RangeWrapper rangeWrapper;
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        if(dataRange != null){
            for (DataRange range : dataRange) {
                rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1,-1,-1,-1, CommandConstants.OperationType.GENERATE_LIST);
                rangeWrapperList.add(rangeWrapper);
            }
        }
        else{
            rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), -1, -1, -1, -1, CommandConstants.OperationType.GENERATE_LIST);
            rangeWrapperList.add(rangeWrapper);
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
