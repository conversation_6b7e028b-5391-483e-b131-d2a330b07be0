package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.MaxUsedCellInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

public class MaxUsedCellInfoExtractorImpl implements MaxUsedCellInfoExtractor {

    RangeWrapper rangeWrapper;
    public MaxUsedCellInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        String sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString(JSONConstants.CURRENT_ACTIVE_SHEET) :actionJson.has(JSONConstants.SHEETLIST) ?  ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST)) : actionJson.has(JSONConstants.ASSOCIATED_SHEET_NAME)? actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME) : null;
        if (sheetName != null)
        {
            rangeWrapper = new RangeWrapper(sheetName, -1, -1, -1, -1);
        }
    }

    @Override
    public RangeWrapper getRangeWrapper() {
        return rangeWrapper;
    }
}
