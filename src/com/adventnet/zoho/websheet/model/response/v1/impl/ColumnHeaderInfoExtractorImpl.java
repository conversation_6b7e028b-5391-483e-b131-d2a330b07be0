package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.ext.LinearIntegralRange;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.ColumnHeaderInfoExtractor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ColumnHeaderInfoExtractorImpl implements ColumnHeaderInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public ColumnHeaderInfoExtractorImpl(JSONObjectWrapper actionJson, MacroResponse macroResponse)
    {
        int action = actionJson != null && actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        int type = actionJson != null && actionJson.has("t") ? actionJson.getInt("t") : -1;					// No I18N
        boolean fromUndo = actionJson.has(JSONConstants.FROM_UNDO);
        RangeWrapper rangeWrapper;
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        List<DataRange> sourceDataRange = ActionJsonUtil.getListOfUnfilteredDataRangesFromJsonObject(actionJson, true);
        if (type == 1) {

            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), CommandConstants.OperationType.MODIFY);
                    rangeWrapperList.add(rangeWrapper);
                }
            }


            if (action == ActionConstants.CUT_PASTE) {
                if (dataRange != null) {
                    for (DataRange range : sourceDataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), CommandConstants.OperationType.MODIFY);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }

            }


        }
        switch (action) {

            case ActionConstants.WIDTH:
            case ActionConstants.CREATE_PIVOT:
            case ActionConstants.EDIT_PIVOT:
            case ActionConstants.COPY_PIVOT:
            case ActionConstants.MOVE_PIVOT:
            case ActionConstants.COPY_PASTE:
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), CommandConstants.OperationType.MODIFY);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;

            case ActionConstants.FORM_FIELD_ADDED:
            case ActionConstants.INSERT_COPY_COLUMN:
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), CommandConstants.OperationType.INSERT);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            case ActionConstants.INSERT_COL:
                Map<String, Map<Integer, Integer>> asnToColIndexToCount;
                JSONArrayWrapper asnToColIndexToCountJSON = actionJson.optJSONArray(JSONConstants.INDICES);
                if(asnToColIndexToCountJSON != null) {
                    asnToColIndexToCount = ActionJsonUtil.dejsoniseASNToIndexToCount(asnToColIndexToCountJSON);
                }
                else
                {
                    Map<String, List<LinearIntegralRange>> asnToColRangesForInsert = dataRange.stream().collect(Collectors.groupingBy(DataRange::getAssociatedSheetName, Collectors.mapping(tempDataRange -> new LinearIntegralRange(tempDataRange.getStartColIndex(), tempDataRange.getEndColIndex()), Collectors.toList())));
                    asnToColIndexToCount = asnToColRangesForInsert.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry ->
                            ActionUtil.getIndexToCountForInsert(entry.getValue())
                    ));
                }
                if(fromUndo)
                {
                    for(Map.Entry<String, Map<Integer, Integer>> asnToIndexToCountEntry: asnToColIndexToCount.entrySet()) {
                        String asn = asnToIndexToCountEntry.getKey();

                        Map<Integer, Integer> indexToCount = asnToIndexToCountEntry.getValue();
                        List<Map.Entry<Integer, Integer>> indexToCountSorted = indexToCount
                                .entrySet()
                                .stream()
                                .sorted(Comparator.comparingInt(lir -> lir.getKey()))
                                .collect(Collectors.toList());

                        for(Map.Entry<Integer, Integer> indexToCountEntry: indexToCountSorted) {
                            rangeWrapper = new RangeWrapper(asn, -1, indexToCountEntry.getKey(), -1, indexToCountEntry.getKey() + indexToCountEntry.getValue() - 1, CommandConstants.OperationType.INSERT);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }else
                {
                    for(Map.Entry<String, Map<Integer, Integer>> asnToIndexToCountEntry: asnToColIndexToCount.entrySet()) {
                        String asn = asnToIndexToCountEntry.getKey();

                        Map<Integer, Integer> indexToCount = asnToIndexToCountEntry.getValue();
                        List<Map.Entry<Integer, Integer>> indexToCountSorted = indexToCount
                                .entrySet()
                                .stream()
                                .sorted(Comparator.comparingInt(lir -> -lir.getKey()))
                                .collect(Collectors.toList());

                        for(Map.Entry<Integer, Integer> indexToCountEntry: indexToCountSorted) {
                            rangeWrapper = new RangeWrapper(asn, -1, indexToCountEntry.getKey(), -1, indexToCountEntry.getKey() + indexToCountEntry.getValue() - 1, CommandConstants.OperationType.INSERT);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }

//                if (dataRange != null) {
//                    dataRange = RangeUtil.sortColAscendingOrder(dataRange); //from 1 to 4
//                    if(fromUndo){
//                        for (DataRange drange : dataRange) {
//                               rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), -1, drange.getStartColIndex(), -1, drange.getEndColIndex(), OperationType.INSERT);
//                              columnHeaderlist.add(rangeWrapper);
//                        }
//
//                    }else{
//                        int prevCol = 0;
//                        int prevColCount = 0;
//                        for (DataRange drange : dataRange) {
//                            int count = (drange.getEndColIndex() - drange.getStartColIndex()) + 1;
//                            int start = drange.getStartColIndex();
//                            int end = drange.getEndColIndex();
//                            if (prevCol < start) {
//                                start += prevColCount;
//                                end += prevColCount;
//                            }
//                            rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), -1, start, -1, end, OperationType.INSERT);
//                            columnHeaderlist.add(rangeWrapper);
//                            prevCol = drange.getStartColIndex();
//                            prevColCount += count;
//                        }
//                    }
//                }
                break;
            case ActionConstants.DELETE_COL:
                Map<String, Map<Integer, Integer>> asnToColIndexToCountForDelete;
                JSONArrayWrapper asnToColIndexToCountForDeleteJSON = actionJson.optJSONArray(JSONConstants.INDICES);
                if(asnToColIndexToCountForDeleteJSON != null)
                {
                    asnToColIndexToCountForDelete = ActionJsonUtil.dejsoniseASNToIndexToCount(asnToColIndexToCountForDeleteJSON);
                }
                else
                {
                    Map<String, List<LinearIntegralRange>> asnToColRangesForDelete = dataRange.stream().collect(Collectors.groupingBy(DataRange::getAssociatedSheetName, Collectors.mapping(tempDataRange -> new LinearIntegralRange(tempDataRange.getStartColIndex(), tempDataRange.getEndColIndex()), Collectors.toList())));
                    asnToColIndexToCountForDelete = asnToColRangesForDelete.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry ->
                            ActionUtil.getIndexToCountForDelete(entry.getValue())
                    ));
                }

                if(fromUndo)
                {
                    for(Map.Entry<String, Map<Integer, Integer>> asnToIndexToCountEntry: asnToColIndexToCountForDelete.entrySet()) {
                        String asn = asnToIndexToCountEntry.getKey();

                        Map<Integer, Integer> indexToCount = asnToIndexToCountEntry.getValue();
                        List<Map.Entry<Integer, Integer>> indexToCountSorted = indexToCount
                                .entrySet()
                                .stream()
                                .sorted(Comparator.comparingInt(lir -> lir.getKey()))
                                .collect(Collectors.toList());

                        for(Map.Entry<Integer, Integer> indexToCountEntry: indexToCountSorted) {
                            rangeWrapper = new RangeWrapper(asn,-1, indexToCountEntry.getKey(), -1, indexToCountEntry.getKey() + indexToCountEntry.getValue()-1, CommandConstants.OperationType.DELETE);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }

                }else
                {
                    for(Map.Entry<String, Map<Integer, Integer>> asnToIndexToCountEntry: asnToColIndexToCountForDelete.entrySet()) {
                        String asn = asnToIndexToCountEntry.getKey();

                        Map<Integer, Integer> indexToCount = asnToIndexToCountEntry.getValue();
                        List<Map.Entry<Integer, Integer>> indexToCountSorted = indexToCount
                                .entrySet()
                                .stream()
                                .sorted(Comparator.comparingInt(lir -> -lir.getKey()))
                                .collect(Collectors.toList());

                        for(Map.Entry<Integer, Integer> indexToCountEntry: indexToCountSorted) {
                            rangeWrapper = new RangeWrapper(asn,-1, indexToCountEntry.getKey(), -1, indexToCountEntry.getKey() + indexToCountEntry.getValue()-1, CommandConstants.OperationType.DELETE);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
//                if (dataRange != null) {
//                        dataRange = RangeUtil.sortColAscendingOrder(dataRange); //from 1 to 4
//                        if(fromUndo){
//                            int prevDelCol = 0;
//                            int prevDelColCount = 0;
//                            List<DataRange> newDataRanges = new ArrayList<>();
//                            for (DataRange drange : dataRange) {
//                                int count = (drange.getEndColIndex() - drange.getStartColIndex()) + 1;
//                                int start = drange.getStartColIndex();
//                                int end = drange.getEndColIndex();
//                                if (prevDelCol < start) {
//                                    start += prevDelColCount;
//                                    end += prevDelColCount;
//                                }
//                                DataRange newRange = new DataRange(drange.getAssociatedSheetName(), drange.getStartRowIndex(),start, drange.getEndRowIndex(),end);
//                                newDataRanges.add(newRange);
//                                prevDelCol = drange.getStartColIndex();
//                                prevDelColCount += count;
//                            }
//                            newDataRanges = RangeUtil.sortColDescendingOrder(newDataRanges); // from 4 to 1
//                            for(DataRange range: newDataRanges){
//                                 rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), OperationType.DELETE);
//                                columnHeaderlist.add(rangeWrapper);
//                            }
//                        }else{
//                            dataRange =  RangeUtil.sortColDescendingOrder(dataRange);
//                            for (DataRange drange : dataRange) {
//                                    rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(),-1, drange.getStartColIndex(), -1, drange.getEndColIndex(), OperationType.DELETE);
//                                    columnHeaderlist.add(rangeWrapper);
//                            }
//                        }
//                }
                break;

            case ActionConstants.INSERT_CUT_COLUMN:
                boolean isSameSheet = actionJson.has(JSONConstants.IS_SAME_SHEET) ? actionJson.getBoolean(JSONConstants.IS_SAME_SHEET) : false;

//            						int       startCol 		= 	isSameSheet ? ((actionJson.getInt(JSONConstants.START_COLUMN) > actionJson.getInt(JSONConstants.SOURCE_START_COLUMN)) ?
//                                                                              actionJson.getInt(JSONConstants.SOURCE_START_COLUMN) :actionJson.getInt(JSONConstants.START_COLUMN)):
//                                                                              actionJson.getInt(JSONConstants.START_COLUMN);
                CommandConstants.OperationType oprType = !actionJson.has(JSONConstants.FROM_UNDO) ? CommandConstants.OperationType.INSERT : CommandConstants.OperationType.DELETE;
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        DataRange srcrange = sourceDataRange.get(dataRange.indexOf(range));
                        if(srcrange.getStartColIndex() < range.getStartColIndex()) // drag and drop after the source range
                        {
                            int count = range.getEndColIndex() - range.getStartColIndex() + 1;
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), oprType);
                            rangeWrapperList.add(rangeWrapper);
                            if(!actionJson.has(JSONConstants.FROM_UNDO))
                            {
                                rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex() - count, -1, range.getEndColIndex() - count, CommandConstants.OperationType.MODIFY);
                                rangeWrapperList.add(rangeWrapper);
                            }
                        }
                        else if(srcrange.getStartColIndex() > range.getStartColIndex()) // drag and drop before the source range
                        {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), oprType);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }

                oprType = isSameSheet ? (actionJson.has(JSONConstants.FROM_UNDO) ? CommandConstants.OperationType.INSERT : CommandConstants.OperationType.DELETE) : CommandConstants.OperationType.MODIFY;
                if (sourceDataRange != null) {
                    for (DataRange range : sourceDataRange) {
                        int count = 0 ;
                        DataRange destRange = dataRange.get(sourceDataRange.indexOf(range));
                        if(actionJson.has(JSONConstants.FROM_UNDO) && range.getStartColIndex() > destRange.getStartColIndex())
                        {
                            count = range.getEndColIndex() - range.getStartColIndex() + 1;
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex() - count, -1, range.getEndColIndex() - count, CommandConstants.OperationType.MODIFY);
                            rangeWrapperList.add(rangeWrapper);
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), oprType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;

            case ActionConstants.FORM_FIELD_DELETED:

                boolean isFormColumnDeleted = actionJson.getBoolean(JSONConstants.FORM_COL_DELETED);

                if (isFormColumnDeleted) {
                    if (dataRange != null) {
                        for (DataRange range : dataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), CommandConstants.OperationType.DELETE);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }

                break;

            case -1:
                // This block will execute if it is coming through responseAnalyzerDataImpl  -- For document load or macroResponse handling (in future wil remove this check)

                if (actionJson != null) {

                    rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), -1, actionJson.getInt(JSONConstants.START_COLUMN), -1, actionJson.getInt(JSONConstants.END_COLUMN), CommandConstants.OperationType.ADD);
                    rangeWrapperList.add(rangeWrapper);


                }

                break;
        }

        if (macroResponse != null && !macroResponse.isEmpty()) {
            ArrayList<JSONObjectWrapper> colWidthList = macroResponse.getColWidthList();
            if (colWidthList != null && !colWidthList.isEmpty()) {

                int size = colWidthList.size();
                //List<RangeWrapper> macroList = new ArrayList<RangeWrapper>();
                for (int i = 0; i < size; i++) {

                    JSONObjectWrapper colWidthJson = colWidthList.get(i);

                    if (colWidthJson.has(JSONConstants.ACTION)) {

                        JSONArrayWrapper widthArr = colWidthJson.getJSONArray(String.valueOf(ActionConstants.WIDTH));
                        int widthSize = widthArr.length();
                        for (int j = 0; j < widthSize; j++) {

                            JSONObjectWrapper job = (JSONObjectWrapper) widthArr.get(j);
                            rangeWrapper = new RangeWrapper(colWidthJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), -1, job.getInt(JSONConstants.START_COLUMN),
                                    -1, job.getInt(JSONConstants.END_COLUMN), CommandConstants.OperationType.MODIFY);
                            rangeWrapperList.add(rangeWrapper);  // check actionJSON from macros and change this to multirange support

                        }
                    } else {
                        rangeWrapper = new RangeWrapper(colWidthJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), -1, colWidthJson.getInt(JSONConstants.START_COLUMN),
                                -1, colWidthJson.getInt(JSONConstants.END_COLUMN), CommandConstants.OperationType.MODIFY);
                        rangeWrapperList.add(rangeWrapper);  // check actionJSON from macros and change this to multirange support
                    }

                }
            }
            if(!macroResponse.getDeleteColumnsArray().isEmpty()) {
                JSONArrayWrapper deleteColumnArray = macroResponse.getDeleteColumnsArray();
                for(int i = 0; i < deleteColumnArray.length(); i++) {
                    JSONObjectWrapper childJson = deleteColumnArray.getJSONObject(i);
                    rangeWrapper = new RangeWrapper(childJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), -1, childJson.getInt(JSONConstants.START_COLUMN), -1, childJson.getInt(JSONConstants.END_COLUMN), CommandConstants.OperationType.DELETE);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
