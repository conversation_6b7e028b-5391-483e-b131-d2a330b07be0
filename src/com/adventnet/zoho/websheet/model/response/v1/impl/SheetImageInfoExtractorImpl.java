package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.SheetImageBean;
import com.adventnet.zoho.websheet.model.response.extractor.SheetImageInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import java.util.ArrayList;
import java.util.List;

public class SheetImageInfoExtractorImpl implements SheetImageInfoExtractor {

    List<SheetImageBean> imageBeanList = new ArrayList<>();
    public SheetImageInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int sheetImageID = -1;
        long dummyId = -1;
        Boolean isUndo = actionJson.has(JSONConstants.FROM_UNDO);
        String sheetAsn = ActionJsonUtil.getAssociateSheetName(actionJson);
        List<String> srcSheetList = ActionJsonUtil.getListOfAssociateSheetNames(actionJson, true);
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        JSONArrayWrapper imageJSONArray = actionJson.has("imageJsonArray") ? actionJson.getJSONArray("imageJsonArray") : null; //NO I18N
        CommandConstants.OperationType operationType = null;
        switch (action)
        {
            case -1:
            case ActionConstants.SERVERCLIP_PASTE_SHEET:
            case ActionConstants.IMPORT_REPLACE_CURRENTSHEET:
            case ActionConstants.IMPORT_APPEND_ROWS_TO_CURRENTSHEET:
            case ActionConstants.IMPORT_REPLACE_DATA_STARTING_AT_CELL:
            case ActionConstants.IMPORT_INSERT_AS_NEW_SHEETS:
                sheetAsn = actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                SheetImageBean imageBean = new SheetImageBean(sheetAsn, CommandConstants.OperationType.GENERATE_LIST);
                imageBeanList.add(imageBean);
                break;
            case ActionConstants.INSERT_COL:
            case ActionConstants.DELETE_COL:
            case ActionConstants.INSERT_ROW:
            case ActionConstants.DELETE_ROW:
//          case ActionConstants.INSERT_CUT_ROW:
//          case ActionConstants.INSERT_CUT_COLUMN:
//          case ActionConstants.INSERT_COPY_ROW:
//          case ActionConstants.INSERT_COPY_COLUMN:
                imageBean = new SheetImageBean(sheetAsn, CommandConstants.OperationType.GENERATE_LIST);
                imageBeanList.add(imageBean);
                break;
            case ActionConstants.IMAGE_NEW:
                if(actionJson.has(JSONConstants.IS_CELL_IMAGE) && actionJson.getBoolean(JSONConstants.IS_CELL_IMAGE))
                {
                    break;
                }
            case ActionConstants.IMAGE_CLONE:
            case ActionConstants.IMAGE_OVER_CELLS:
            case ActionConstants.IMAGE_IN_CELLS:
                if (isUndo) {
                    operationType = CommandConstants.OperationType.REMOVE;
                    operationType = (action == ActionConstants.IMAGE_IN_CELLS) ? CommandConstants.OperationType.INSERT : operationType;
                    operationType = (action == ActionConstants.IMAGE_OVER_CELLS) ? CommandConstants.OperationType.REMOVE : operationType;
                } else {
                    operationType = (action == ActionConstants.IMAGE_NEW) ? CommandConstants.OperationType.INSERT : CommandConstants.OperationType.ADD;
                    operationType = (action == ActionConstants.IMAGE_IN_CELLS) ? CommandConstants.OperationType.REMOVE : operationType;
                    operationType = (action == ActionConstants.IMAGE_OVER_CELLS) ? CommandConstants.OperationType.INSERT : operationType;
                }
                if(action == ActionConstants.IMAGE_IN_CELLS){
                    sheetAsn = srcSheetList.get(0);
                }
                if (imageJSONArray != null)
                {
                    for (int i = 0; i < imageJSONArray.length(); i++)
                    {
                        JSONObjectWrapper imgObj = imageJSONArray.getJSONObject(i);
                        dummyId = imgObj.has(JSONConstants.DUMMY_ID) ? imgObj.getLong(JSONConstants.DUMMY_ID) : -1;
                        sheetImageID = imgObj.has(JSONConstants.SHEET_IMAGE_ID) ? imgObj.getInt(JSONConstants.SHEET_IMAGE_ID) : -1;

                        // TODO IMAGES - TO BE REMOVED ONCE SHEET IMAGE ID HAS BEEN MIGRATED
                        imageBean = new SheetImageBean(sheetAsn, operationType, sheetImageID, dummyId);
                        imageBeanList.add(imageBean);
                    }
                }
                break;
            case ActionConstants.IMAGE_MOVE:
            case ActionConstants.IMAGE_RESIZE:
            case ActionConstants.IMAGE_DELETE:
                operationType = (action == ActionConstants.IMAGE_MOVE) ? CommandConstants.OperationType.MOVE : (action == ActionConstants.IMAGE_RESIZE ? CommandConstants.OperationType.MODIFY : CommandConstants.OperationType.DELETE);
                for (int i = 0; i < imageJSONArray.length(); i++)
                {
                    JSONObjectWrapper imgObj = imageJSONArray.getJSONObject(i);
                    if (imgObj != null)
                    {
                        sheetImageID = imgObj.has(JSONConstants.SHEET_IMAGE_ID) ? imgObj.getInt(JSONConstants.SHEET_IMAGE_ID) : null;
                    }
                    imageBean = new SheetImageBean(sheetAsn, operationType, sheetImageID);
                    imageBeanList.add(imageBean);
                }
                break;
            case ActionConstants.IMAGE_REMOVE:
                if (isUndo) {
                    operationType = CommandConstants.OperationType.ADD;
                } else {
                    operationType = CommandConstants.OperationType.REMOVE;
                }
                for (int i = 0; i < imageJSONArray.length(); i++)
                {
                    JSONObjectWrapper imgObj = imageJSONArray.getJSONObject(i);
                    if (imgObj != null)
                    {
                        sheetImageID = imgObj.has(JSONConstants.SHEET_IMAGE_ID) ? imgObj.getInt(JSONConstants.SHEET_IMAGE_ID) : null;
                    }
                    // TODO IMAGES - TO BE REMOVED ONCE SHEET IMAGE ID HAS BEEN MIGRATED
                    imageBean = new SheetImageBean(sheetAsn, operationType, sheetImageID);
                    imageBeanList.add(imageBean);
                }
                break;
            case ActionConstants.IMAGE_CUT_PASTE:
                String oldSheetName 	= actionJson.has(JSONConstants.SOURCE_SHEET_NAME) ? actionJson.getString(JSONConstants.SOURCE_SHEET_NAME) : null;
                String newSheetName 	= actionJson.has(JSONConstants.SHEET_NAME) ? actionJson.getString(JSONConstants.SHEET_NAME) : null;
                String assOldSheetName 	= actionJson.has(JSONConstants.ASSOCIATED_SOURCE_SHEET_NAME) ? actionJson.getString(JSONConstants.ASSOCIATED_SOURCE_SHEET_NAME) : null;
                if(isUndo)
                {
                    for(int i = 0; i < imageJSONArray.length(); i++)
                    {
                        JSONObjectWrapper imgObj = imageJSONArray.getJSONObject(i);
                        int sourceSheetImageID = -1;
                        if(imgObj != null)
                        {
                            sourceSheetImageID = imgObj.has(JSONConstants.SHEET_IMAGE_ID) ? imgObj.getInt(JSONConstants.SHEET_IMAGE_ID) : null;
                            sheetImageID = imgObj.has(JSONConstants.SHEET_IMAGE_SOURCE_ID) ? imgObj.getInt(JSONConstants.SHEET_IMAGE_SOURCE_ID) : null;
                        }
                        // TODO IMAGES - TO BE REMOVED ONCE SHEET IMAGE ID HAS BEEN MIGRATED
                        imageBean = new SheetImageBean(sheetAsn, CommandConstants.OperationType.REMOVE, sourceSheetImageID);
                        imageBeanList.add(imageBean);
                        imageBean = new SheetImageBean(assOldSheetName, CommandConstants.OperationType.INSERT, sheetImageID);
                        imageBeanList.add(imageBean);
                    }
                }
                else
                {
                    for(int i = 0; i < imageJSONArray.length(); i++)
                    {
                        JSONObjectWrapper imgObj = imageJSONArray.getJSONObject(i);
                        int sourceSheetImageID = -1;
                        if(imgObj != null)
                        {
                            sourceSheetImageID = imgObj.has(JSONConstants.SHEET_IMAGE_SOURCE_ID) ? imgObj.getInt(JSONConstants.SHEET_IMAGE_SOURCE_ID) : null;
                            sheetImageID = imgObj.has(JSONConstants.SHEET_IMAGE_ID) ? imgObj.getInt(JSONConstants.SHEET_IMAGE_ID) : null;
                        }
                        dummyId = imgObj.has(JSONConstants.DUMMY_ID) ? imgObj.getLong(JSONConstants.DUMMY_ID) : -1;

                        // TODO IMAGES - TO BE REMOVED ONCE SHEET IMAGE ID HAS BEEN MIGRATED
                        imageBean = new SheetImageBean(assOldSheetName, CommandConstants.OperationType.REMOVE, sourceSheetImageID);
                        imageBeanList.add(imageBean);
                        imageBean = new SheetImageBean(sheetAsn, CommandConstants.OperationType.INSERT, sheetImageID, dummyId);
                        imageBeanList.add(imageBean);
                    }
                }
                break;
            case ActionConstants.IMAGE_REPLACE:
                for(int i = 0; i < imageJSONArray.length(); i++)
                {
                    JSONObjectWrapper imgObj = imageJSONArray.getJSONObject(i);
                    if(imgObj != null)
                    {
                        sheetImageID = imgObj.has(JSONConstants.SHEET_IMAGE_ID) ? imgObj.getInt(JSONConstants.SHEET_IMAGE_ID) : null;
                    }
                    // TODO IMAGES - TO BE REMOVED ONCE SHEET IMAGE ID HAS BEEN MIGRATED
                    if(imgObj.has(JSONConstants.IMAGE_SOURCE_ID))
                    {
                        int sourceImageID = imgObj.getInt(JSONConstants.IMAGE_SOURCE_ID);
                        imageBean = new SheetImageBean(sheetAsn, CommandConstants.OperationType.DELETE, sheetImageID, sourceImageID);
                        imageBeanList.add(imageBean);
                        imageBean = new SheetImageBean(sheetAsn, CommandConstants.OperationType.INSERT, sheetImageID);
                        imageBeanList.add(imageBean);
                    }
                    // TODO IMAGES - TO BE REMOVED ONCE SHEET IMAGE ID HAS BEEN MIGRATED
                    else
                    {
                        imageBean = new SheetImageBean(sheetAsn, CommandConstants.OperationType.EDIT, sheetImageID);
                        imageBeanList.add(imageBean);
                    }
                }
                break;
            case ActionConstants.IMAGE_COPY_PASTE:
                for(int i = 0; i < imageJSONArray.length(); i++)
                {
                    JSONObjectWrapper imgObj = imageJSONArray.getJSONObject(i);
                    if(imgObj != null)
                    {
                        sheetImageID = imgObj.has(JSONConstants.SHEET_IMAGE_ID) ? imgObj.getInt(JSONConstants.SHEET_IMAGE_ID) : null;
                    }
                    if(isUndo)
                    {
                        // TODO IMAGES - TO BE REMOVED ONCE SHEET IMAGE ID HAS BEEN MIGRATED
                        imageBean = new SheetImageBean(sheetAsn, CommandConstants.OperationType.REMOVE, sheetImageID);
//						imageBean = new SheetImageBean(sheetAsn, OperationType.REMOVE, sheetImageID);
                        imageBeanList.add(imageBean);
                    }
                    else
                    {
                        dummyId = imgObj.has(JSONConstants.DUMMY_ID) ? imgObj.getLong(JSONConstants.DUMMY_ID) : -1;
                        imageBean = new SheetImageBean(sheetAsn, CommandConstants.OperationType.ADD, sheetImageID, dummyId);
                        imageBeanList.add(imageBean);
                    }
                }
                break;
            default:
                imageBeanList = null;
                break;
        }
    }

    @Override
    public List<SheetImageBean> getImageBeanList() {
        return imageBeanList;
    }
}
