package com.adventnet.zoho.websheet.model.response.v1.listener;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.helper.TimeCapsule;
import com.adventnet.zoho.websheet.model.response.meta.CellMeta;
import com.adventnet.zoho.websheet.model.response.v1.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.response.viewport.*;
import com.adventnet.zoho.websheet.model.response.viewport.IterateArea;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ResponseListenerImpl implements ResponseListener
{

    private static final Logger LOGGER = Logger.getLogger(ResponseListenerImpl.class.getName());

    private Workbook workbook;
    private Constraints constraints;		// it can be used while creating response for cellData like showFormulas

    private HashMap<String, HashSet<Long>> faultyCellsListMap = new HashMap<>();		// here Sheet name is key and list of cell indexes

    private HashMap<String, JSONObjectWrapper> rowKeyMap = new HashMap<>();

    private HashMap<String, JSONObjectWrapper> colKeyMap = new HashMap<>();

    private HashMap<String, JSONArrayWrapper> faultyRangeListMap = new HashMap<>();

    private HashMap<String, List<Range>> csRangeListMap = new HashMap<>();
    private HashMap<String, JSONArrayWrapper> csResponseMap = new HashMap<>();
    private HashMap<String, JSONArrayWrapper> csFaultyRangesMap = new HashMap<>();
    private HashMap<String, HashSet<Long>> csFaultyCellsMap = new HashMap<>();

    private final Map<ZSPattern, JSONObjectWrapper> patternProps = new HashMap<>();

    public ResponseListenerImpl(Workbook workbook, Constraints constraints)
    {
        this.workbook = workbook;
        this.constraints = constraints;

    }

    @Override
    public Object getFaultyCellList()
    {

        return faultyCellsListMap;
    }

    @Override
    public Object getProcessedCellResponse()
    {

        //Note : Converting Row Data Response of JSONObjectWrapper To JSONArrayWrapper.
        Map<String, JSONArrayWrapper> rowKeyMap = new HashMap<>();
        for(Map.Entry<String, JSONObjectWrapper> entry : this.rowKeyMap.entrySet()){
            //NOTE: Here TreeMap is Used to Sort the RowDataResponse in RowKey Order.
            Map<Integer, JSONArrayWrapper> rowKeyToRowData = new TreeMap<>();
            JSONObjectWrapper rowDataJObj = entry.getValue();
            Iterator<String> rowItr = rowDataJObj.keys();
            while (rowItr.hasNext()){
                JSONArrayWrapper rowJson = rowDataJObj.getJSONArray(rowItr.next());
                //NOTE:Here we are removing CellNum from RowDataResponse.
                JSONArrayWrapper newRowJSon = new JSONArrayWrapper();
                int rowKey = rowJson.getInt(2);
                for(int i = 0; i < 3; i++) {
                    newRowJSon.put(rowJson.get(i));
                }
                rowKeyToRowData.put(rowKey, newRowJSon);
            }

            JSONArrayWrapper rowDataJAry = new JSONArrayWrapper();
            for(JSONArrayWrapper rowJson : rowKeyToRowData.values()){
                rowDataJAry.put(rowJson);
            }
            rowKeyMap.put(entry.getKey(), rowDataJAry);
        }

        Map<String, Object> cellData = new HashMap<>();

        cellData.put("ROWDATA", rowKeyMap);
        cellData.put("COLDATA", colKeyMap);

        return cellData;

    }

    @Override
    public Object getFaultyRangeList()
    {

        return faultyRangeListMap;
    }

    @Override
    public void updateCellResposne(Sheet sheet, CellInfo cellInfo)
    {
        // TODO Auto-generated method stub

        updateResposne(sheet, cellInfo);

    }

    @Override
    public void updateFaultyCellList(String sheetName, CellInfo cellInfo)
    {
        // TODO Auto-generated method stub

        updateFaultyLists(sheetName, cellInfo);

    }

    @Override
    public DataRange updateCellInfo(Sheet sheet, CellInfo cellInfo)
    {
        return updateCellInfo(sheet, cellInfo, new TimeCapsule());
    }

    @Override
    public DataRange updateCellInfo(Sheet sheet, CellInfo cellInfo, TimeCapsule timeCapsule)
    {
        // TODO Auto-generated method stub
        ViewPortStatus cvPort = cellInfo.getCVP();

        if (ViewPortStatus.ACTIVE == cvPort)
        {
            return updateResposne(sheet, cellInfo, timeCapsule);

        }
        return null;
    }

    @Override
    public void updateCell(Cell cell)
    {
        // TODO Auto-generated method stub

        ViewPortStatus cvport = constraints.getCellStatusInViewPort(cell);

        CellInfo cellInfo = new CellInfo(cell.getRowIndex(), cell.getColumnIndex(), cvport);

        if (ViewPortStatus.ACTIVE == cvport)
        {
            updateResposne(cell.getRow().getSheet(), cellInfo);

        } else if (ViewPortStatus.CACHE == cvport)
        {
            updateFaultyLists(cell.getRow().getSheet().getAssociatedName(), cellInfo);
        }
    }

    private DataRange updateResposne(Sheet sheet, CellInfo cellInfo){
        return updateResposne(sheet, cellInfo, new TimeCapsule());
    }

    private DataRange updateResposne(Sheet sheet, CellInfo cellInfo, TimeCapsule timeCapsule) {
        //this.sheet		=	sheet;			//Need to think of this line -- always resetting
        JSONArrayWrapper colAry = new JSONArrayWrapper();

        int rowIndex = cellInfo.getRowID();
        int colIndex = cellInfo.getColID();

        String sheetName = sheet.getAssociatedName();

        JSONObjectWrapper rowKeyInfoAry = rowKeyMap.containsKey(sheetName) ? rowKeyMap.get(sheetName) : new JSONObjectWrapper();
        JSONObjectWrapper keyColAryMap = colKeyMap.containsKey(sheetName) ? colKeyMap.get(sheetName) : new JSONObjectWrapper();

        int rowKey;
        int rowRepeat;
        ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(rowIndex);
        int rowsRepeated = rRow.getRowsRepeated();
        Row row = rRow.getRow();
        int readRowIndex = row == null ? rowIndex : row.getRowIndex();
        timeCapsule.startEvent(TimeCapsule.Event.GET_ROWJSON);
        JSONArrayWrapper rowHolderAry = rowKeyInfoAry.has(Integer.toString(readRowIndex)) ? rowKeyInfoAry.getJSONArray(Integer.toString(readRowIndex)) : null;
        timeCapsule.endCurrentEvent();
        if (rowHolderAry == null) {
            rowHolderAry = new JSONArrayWrapper();
            rowKey = rowKeyInfoAry.length();

            timeCapsule.startEvent(TimeCapsule.Event.GET_READONLYROW);
            timeCapsule.endCurrentEvent();//
            rowRepeat = rowsRepeated;

            if (rowRepeat + rowIndex > Utility.MAXNUMOFROWS) {
                rowRepeat = Utility.MAXNUMOFROWS - rowIndex;
            }
            if(rowRepeat == 1)
            {
                int sheetRowNum =  sheet.getRowNum();
                if(rowIndex >= sheetRowNum)
                {
                    rowRepeat = Utility.MAXNUMOFROWS - rowIndex;
                }
            }

            rowHolderAry.put(0, rowIndex);
            rowHolderAry.put(1, rowRepeat);
            rowHolderAry.put(2, Integer.toString(rowKey));
            rowHolderAry.put(3, row == null ? -1 : row.getCellNum());
            rowHolderAry.put(4, readRowIndex);

            timeCapsule.startEvent(TimeCapsule.Event.ADD_ROWJSON);
            rowKeyInfoAry.put(Integer.toString(readRowIndex), rowHolderAry);
            timeCapsule.endCurrentEvent();

        }else {
            rowKey = rowHolderAry.getInt(2);
            rowRepeat = rowHolderAry.getInt(1);
            readRowIndex = rowHolderAry.getInt(4);
        }

        boolean isCellFound = false;
        if (keyColAryMap.has(Integer.toString(rowKey))) // Check for row
        {
            timeCapsule.startEvent(TimeCapsule.Event.IS_CELLFOUND);
            isCellFound = isCellFound(keyColAryMap, rowKey, colIndex);
            timeCapsule.endCurrentEvent();
            timeCapsule.startEvent(TimeCapsule.Event.GET_COLJSON);
            colAry = keyColAryMap.getJSONArray(Integer.toString(rowKey));
            timeCapsule.endCurrentEvent();
        }

        DataRange dataRange = null;
        if (!isCellFound) {
            timeCapsule.startEvent(TimeCapsule.Event.GET_READONLYCELL);
            ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(readRowIndex, colIndex);
            timeCapsule.endCurrentEvent();
            int colRepeat = rCell.getColsRepeated();

            if ((colRepeat + colIndex) > Utility.MAXNUMOFCOLS) {

                colRepeat = Utility.MAXNUMOFCOLS - colIndex;

            }

            if(colRepeat == 1)
            {
                int rowCellNum =  rowHolderAry.getInt(3);
                if(colIndex >= rowCellNum)
                {
                    colRepeat = Utility.MAXNUMOFCOLS - colIndex;
                }
            }

            timeCapsule.startEvent(TimeCapsule.Event.GET_CELL_DATA);
            JSONArrayWrapper cellDataAry = getCellData(rCell.getCell(), timeCapsule);
            timeCapsule.endCurrentEvent();

            JSONArrayWrapper columnHolderAry = new JSONArrayWrapper();
            columnHolderAry.put(0, colIndex);
            columnHolderAry.put(1, colRepeat);
            columnHolderAry.put(2, cellDataAry);
            colAry.put(columnHolderAry);

            if(RangeIterator.isCellEmpty(rCell.getCell())){
                if(colIndex < sheet.getUsedColumnIndex()) {
                    RangeIterator rangeIterator = new RangeIterator(sheet, cellInfo.getRowID(), colIndex + 1, cellInfo.getRowID(), sheet.getUsedColumnIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, true, false, false, true);
                    boolean isEmptyCell = true;
                    while (rangeIterator.hasNext() && isEmptyCell) {
                        rCell = rangeIterator.next();
                        isEmptyCell = RangeIterator.isCellEmpty(rCell.getCell());
                        if (!isEmptyCell && rCell.getCell().getType() == Cell.Type.STRING && !isCellFound(keyColAryMap, rowKey, rCell.getColIndex())) {

                            timeCapsule.startEvent(TimeCapsule.Event.GET_CELL_DATA);
                            cellDataAry = getCellData(rCell.getCell(), timeCapsule);
                            timeCapsule.endCurrentEvent();

                            columnHolderAry = new JSONArrayWrapper();
                            columnHolderAry.put(0, rCell.getColIndex());
                            columnHolderAry.put(1, rCell.getColsRepeated());
                            columnHolderAry.put(2, cellDataAry);
                            colAry.put(columnHolderAry);

                        }
                    }
                }

                if(colIndex > 0) {
                    RangeIterator rangeIterator = new RangeIterator(sheet, cellInfo.getRowID(), 0, cellInfo.getRowID(), colIndex - 1, RangeIterator.IterationStartPositionEnum.TOP_RIGHT, false, false, true, false, false, true);
                    boolean isEmptyCell = true;
                    while (rangeIterator.hasNext() && isEmptyCell) {
                        rCell = rangeIterator.next();
                        isEmptyCell = RangeIterator.isCellEmpty(rCell.getCell());
                        if (!isEmptyCell && rCell.getCell().getType() == Cell.Type.STRING && !isCellFound(keyColAryMap, rowKey, rCell.getColIndex())) {

                            timeCapsule.startEvent(TimeCapsule.Event.GET_CELL_DATA);
                            cellDataAry = getCellData(rCell.getCell(), timeCapsule);
                            timeCapsule.endCurrentEvent();

                            columnHolderAry = new JSONArrayWrapper();
                            columnHolderAry.put(0, rCell.getColIndex());
                            columnHolderAry.put(1, rCell.getColsRepeated());
                            columnHolderAry.put(2, cellDataAry);
                            colAry.put(columnHolderAry);
                        }
                    }
                }
            }
            keyColAryMap.put(Integer.toString(rowKey), colAry);

            dataRange = new DataRange(sheetName, rowIndex, colIndex, rowIndex + rowRepeat -1, colIndex + colRepeat - 1);
        }

        //Finally Updating the map
        rowKeyMap.put(sheetName, rowKeyInfoAry);
        colKeyMap.put(sheetName, keyColAryMap);
        return dataRange;
    }

    private void updateFaultyLists(String sheetName, CellInfo cellInfo)
    {
        HashSet<Long> faultySet = null;

        if (!faultyCellsListMap.isEmpty() && faultyCellsListMap.containsKey(sheetName))
        {
            faultySet = faultyCellsListMap.get(sheetName);
        }

        if (faultySet == null)
        {
            faultySet = new HashSet<>();
        }

        long cellId = (Utility.MAXNUMOFCOLS) * cellInfo.getRowID() + cellInfo.getColID();
        faultySet.add(cellId);

        faultyCellsListMap.put(sheetName, faultySet);

    }

//    private JSONArrayWrapper getRowHolderArray(int row, JSONObjectWrapper rowKeyInfoAry)
//    {
//        int rowAryLen = rowKeyInfoAry.length();
//        for (int j = 0; j < rowAryLen; j++)
//        {
//            JSONArrayWrapper rowArray = rowKeyInfoAry.getJSONArray(j);
//            int startIndex = rowArray.getInt(0);
//            int repeated = rowArray.getInt(1);
//            if (row >= startIndex && row <= (startIndex + repeated - 1))
//            {
//                return rowArray;
//            }
//        }
//        return null;
//    }
//
//    private int findNextKey(JSONArrayWrapper rowKeyInfoAry, JSONObjectWrapper keyColAryMap)
//    {
//        int key = 0;
//        if (!rowKeyInfoAry.isEmpty() && !keyColAryMap.isEmpty())
//        {
//            Iterator<String> itr = keyColAryMap.keys();
//            while (itr.hasNext())
//            {
//                String str = (String) itr.next();
//                int tempKey = Integer.parseInt(str);
//                //key		=	tempKey > key	?	tempKey
//                if (tempKey > key)
//                {
//                    key = tempKey;
//                }
//
//            }
//        }
//
//        return ++key;
//    }

    private boolean isCellFound(JSONObjectWrapper keyColAryMap, int key, int col)
    {
        JSONArrayWrapper tempAry;
        JSONArrayWrapper colArray;

        if (!keyColAryMap.has(Integer.toString(key)))		// Check for row
        {
            return false;
        }

        colArray = keyColAryMap.getJSONArray(Integer.toString(key));
        int colAryLen = colArray.length();
        for (int j = 0; j < colAryLen; j++)
        {
            tempAry = colArray.getJSONArray(j);
            if (col >= tempAry.getInt(0) && col <= (tempAry.getInt(0) + tempAry.getInt(1) - 1))
            {
                return true;
            }

        }

        return false;
    }

    private JSONArrayWrapper getCellData(Cell cell, TimeCapsule timeCapsule)
    {
        JSONArrayWrapper cellDataAry = new JSONArrayWrapper();

        if (cell != null)
        {
            Cell.Type cellType = cell.getContentType();
            timeCapsule.startEvent(TimeCapsule.Event.GET_ACTUAL_VALUE);
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.ACTUAL_VALUE))
            {
                String actualValue = null;
                Value val = cell.getValue();
                //if (val != null && (val.getType().isNumberType() || val.getType().isDateType()))
                {
                    //Taking only english,because we need always actual value for client as status bar cal
                    actualValue = val.getValueString(SpreadsheetSettings.defaultSpreadsheetSettings,cellType);
                }
                cellDataAry.put("'"+Utility.masknull(actualValue, "")+"'"); // 0
            }
            timeCapsule.endCurrentEvent();

            timeCapsule.startEvent(TimeCapsule.Event.GET_CELL_CONTENT);
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.DISPLAY_VALUE))
            {
                cellDataAry.put("'"+Utility.masknull(cell.getContent(), "")+"'"); //1
            }
            timeCapsule.endCurrentEvent();


            timeCapsule.startEvent(TimeCapsule.Event.GET_CELL_FORMULA);
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.FORMULA)){
                String formula = null;
                if (cell.isFormula() || ((CellImpl) cell).isArrayCell()) // Array Child cells will not be a formulacell. But we need to add formula for those cells too
                {
                    if(constraints.showFormulas()){
                        formula = cell.getLocalizedFormula();
                    }
                    else
                    {
                        formula = "hide";    //NO I18N
                    }
                }
                else if(cell.getType() != Cell.Type.BOOLEAN)
                {
                    formula = cell.getLocalizedFormula();
                }

                cellDataAry.put("'"+Utility.masknull(formula, "")+"'"); //2
            }
            timeCapsule.endCurrentEvent();

            timeCapsule.startEvent(TimeCapsule.Event.GET_STYLE_NAME);
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.STYLE_NAME))
            {
                cellDataAry.put(cell.getStyleName()); //3
            }
            timeCapsule.endCurrentEvent();

            //Need to removed this -- it is dummy because we are using AppliedConditionalFormat
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.COND_STYLE_NAME))
            {

                //JSONArrayWrapper	styleAry	=	MapStyle.getAppliedConditionalCellStylesToCell(sheet, cell);
                //List<String> 		conditionalCellStyleNqmes	=		cellImpl.getConditionalCellStyleNames();
                cellDataAry.put(new JSONArrayWrapper()); //4
            }

            //Cell comments
//    		    String comments	 = 	EngineUtils1.getComments(cell);

            timeCapsule.startEvent(TimeCapsule.Event.GET_ANNOTATION);
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.ANNOTATION))
            {
                JSONArrayWrapper comments = EngineUtils1.getCommentsArr(cell);
                cellDataAry.put(comments); //5
            }
            timeCapsule.endCurrentEvent();

            timeCapsule.startEvent(TimeCapsule.Event.GET_LINKS);
            boolean isCellHyperLink = false;
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.HLINK))
            {
                //Cell hyperlink
                List list = ResponseUtils.getCellLinksInfo(cell);
                isCellHyperLink = (Boolean) list.get(1);
                if (isCellHyperLink)
                {
                    cellDataAry.put((String) list.get(0));
                } else
                {
                    cellDataAry.put(""); //6
                }
            }
            timeCapsule.endCurrentEvent();

            timeCapsule.startEvent(TimeCapsule.Event.GET_PATTERN);
            ZSPattern cellPattern = ((CellImpl)cell).getPattern(2);
            timeCapsule.endCurrentEvent();

            timeCapsule.startEvent(TimeCapsule.Event.GET_PATTERNTYPE);
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.TYPE))
            {

                ZSPattern.RegionalType regionalType = ZSPattern.RegionalType.NONE;
                if(cellType == Cell.Type.FLOAT && cellPattern != null && cellPattern.getType() != Cell.Type.UNDEFINED)
                {
                    regionalType = cellPattern.getRegionalType();
                }
                String cellTypeString = regionalType != ZSPattern.RegionalType.NONE ? regionalType.toString() : cellType.toString();
                cellDataAry.put(cellTypeString); //7
            }
            timeCapsule.endCurrentEvent();

            timeCapsule.startEvent(TimeCapsule.Event.GET_PATTERN_COLOR);
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.PATTERN_COLOR))
            {
                cellDataAry.put(cell.getContentColor()); //8
            }
            timeCapsule.endCurrentEvent();

            timeCapsule.startEvent(TimeCapsule.Event.GET_CELL_FORMATINFO);
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.PATTERN)) {
                JSONObjectWrapper patternObject = new JSONObjectWrapper();
                if(cellPattern != null) {
                    synchronized (cellPattern)
                    {
//                        patternObject = null;
                        patternObject = patternProps.get(cellPattern);
                        if (patternObject == null) {
                            patternObject = new JSONObjectWrapper();
                            patternProps.put(cellPattern, patternObject);
                            try {
                                long s = System.currentTimeMillis();
                                HashMap<String, Object> patternMap = CellUtil.getCellFormatInfoNew(workbook, cellPattern, timeCapsule);

                                if (patternMap != null && !patternMap.isEmpty()) {
                                    for (String key : patternMap.keySet()) {
                                        patternObject.put(key, patternMap.get(key));
                                    }
                                }
                                if (System.currentTimeMillis() - s > 100) {
                                    LOGGER.log(Level.INFO, "Pattern taking time >>> {0}", patternObject);
                                }
                            } catch (Exception e) {
                                LOGGER.log(Level.INFO, "Exception while getting format info : ", e);
                            }
                        }
                    }
                }
                cellDataAry.put(patternObject);
            }

            timeCapsule.endCurrentEvent();

            timeCapsule.startEvent(TimeCapsule.Event.GET_CONTENTVALID);
            if (constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.IS_CONTENT_VALID)) {
                cellDataAry.put((cell.isContentValid()) ? 1 : 0);
            } else {
                cellDataAry.put(1); //10
            }
            timeCapsule.endCurrentEvent();
            if (isCellHyperLink)
            {
                cellDataAry.put(1); //11
            } else
            {
                cellDataAry.put(0);
            }
            timeCapsule.startEvent(TimeCapsule.Event.GET_PICKLIST);
            if(constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.PICKLIST)) {
                if(cell.getValue() instanceof PicklistValue) {
                    PicklistValue picklistValue = (PicklistValue) cell.getValue();
                    PicklistItem picklistItem = picklistValue.getPicklistItems().get(0); // Only sending one Item even if it is multi-select
                    PicklistStyle style = picklistItem.getStyle();
                    JSONArrayWrapper picklistStyle = new JSONArrayWrapper();

                    if(style != null) {
                        picklistStyle.put(ZSColor.getHexColor(style.getTextZSColor(), workbook.getTheme()));
                        picklistStyle.put(ZSColor.getHexColor(style.getBgZSColor(), workbook.getTheme()));
                    }

                    cellDataAry.put(picklistStyle); //12
                }
                else {
                    cellDataAry.put(new JSONArrayWrapper());
                }
            }
// 	    if(((CellImpl) cell).getImageID() != null)
//            {
//                cellDataAry.put(((CellImpl) cell).getImageID()); //15
//            }
            timeCapsule.endCurrentEvent();

            if(constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.REPEAT_INDEX)) {
                cellDataAry.put(cell.getPatternRepeatIndex()); //13
            }

            if(constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.REPEAT_CHAR)) {
                String repeatChar = cell.getPatternRepeatChar();
                if(repeatChar == null) {
                    repeatChar = "";
                }
                cellDataAry.put(repeatChar); //14
            }
            if(constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.CELL_IMAGE))
            {
                if(cell.getValue() instanceof ImageValue)
                {
                    ImageValue imageValue = (ImageValue) cell.getValue();
                    JSONArrayWrapper imageArray = new JSONArrayWrapper();
                    imageArray.put(imageValue.getMode().getId());
                    imageArray.put(imageValue.getImageID());
                    imageArray.put(imageValue.getImageHeight());
                    imageArray.put(imageValue.getImageWidth());

                    cellDataAry.put(imageArray);
                }else{
                    cellDataAry.put(new JSONArrayWrapper());
                }
            }

            if(constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.TYPE_MISMATCH)) {
                Object value = cell.getValue().getRawValue();
                int isMismatch = 0;

                if(!cell.isFormula() && ((CellImpl) cell).getAutoArraySuccessParent() == null && cell.getIgnoreError() != 1 && value instanceof ZSString) {
                    Cell.Type convertedType = ((ZSString) value).getConvertedValue(workbook.getSpreadsheetSettings()).getType();

                    switch(convertedType) {
                        case FLOAT:
                            isMismatch = 1;
                            break;
                        case DATE:
                        case DATETIME:
                            isMismatch = 2;
                    }

                }

                cellDataAry.put(isMismatch); //16
            }

            if(constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.AUTO_ARRAY)) {
                Cell arrayCell = cell;
                if(((CellImpl) cell).getAutoArraySuccessParent() != null) {
                    arrayCell = ((CellImpl) cell).getAutoArraySuccessParent();
                }

                int arrayRowSpan = ((CellImpl)arrayCell).getAutoArrayRowSpan();
                int arrayColSpan = ((CellImpl)arrayCell).getAutoArrayColSpan();
                boolean ignoreError = arrayCell.getValue().getType() == Cell.Type.ERROR && arrayCell.getIgnoreError() == 2;

                if((arrayRowSpan > 1 || arrayColSpan > 1)) {
                    int endRow = arrayCell.getRowIndex() + arrayRowSpan - 1;
                    int endCol = arrayCell.getColumnIndex() + arrayColSpan - 1;
                    cellDataAry.put(new JSONArrayWrapper().put(arrayCell.getRowIndex()).put(arrayCell.getColumnIndex()).put(endRow).put(endCol).put(ignoreError)); //17
                }
                else {
                    cellDataAry.put(new JSONArrayWrapper());
                }
            }

            if(constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.PICKLIST_ID)) {
                if(cell.getValue() instanceof PicklistValue) {
                    PicklistValue picklistValue = (PicklistValue) cell.getValue();
                    Picklist picklist = picklistValue.getPicklist();
                    List<PicklistItem> items = picklistValue.getPicklistItems();
                    JSONArrayWrapper arr = new JSONArrayWrapper();
                    for(PicklistItem item: items) {
                        arr.put(item.getId());
                    }

                    cellDataAry.put(new JSONArrayWrapper().put(picklist.getId()).put(arr));
                }
                else {
                    cellDataAry.put(new JSONArrayWrapper());
                }
            }

            if(constraints.isPermissible(com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType.RICH_STRING))
            {
                cellDataAry.put(ResponseUtils.getCellRichStringContents(cell));
            }

            if(constraints.isPermissible(CellMeta.CellMetaType.INVISIBLE_CHARS)) {
                Map<Integer, Character> invisibleChars = ((CellImpl)cell).getInvisibleCharacters();
                if(invisibleChars != null) {
                    JSONObjectWrapper obj = new JSONObjectWrapper();
                    for(Map.Entry<Integer, Character> entry: invisibleChars.entrySet()) {
                        obj.put(String.valueOf(entry.getKey()), entry.getValue());
                    }
                    cellDataAry.put(obj);
                }
                else {
                    cellDataAry.put(new JSONObjectWrapper());
                }
            }

            cellDataAry.put(cell.getImportrangeSyncState().getAsInt());//22
        }

        return cellDataAry;
    }

    public static void main(String args[]) throws Exception
    {
        ResponseListener listener = new ResponseListenerImpl(null, null);
        Constraints constraints = new ConstraintsImpl(null, false, (Utility.MAXNUMOFCOLS - 1));

        if (constraints.isSheetInActiveViewPort("0#"))
        {
            IterateArea areaIterator = constraints.getAreaIterator("0#", 1, 1, 1, 1);

            while (areaIterator.hasNext())
            {
                CellInfo cellInfo = areaIterator.next();

                listener.updateCellInfo(null, cellInfo);
            }
        }
    }

    @Override
    public void updateFaultyRangeList(RangeWrapper rangeWrapper)
    {

        JSONArrayWrapper faultyRangeAry = null;
        String sheetName = rangeWrapper.getSheetName();

        if (!faultyRangeListMap.isEmpty() && faultyRangeListMap.containsKey(sheetName))
        {
            faultyRangeAry = faultyRangeListMap.get(sheetName);
        }

        if (faultyRangeAry == null)
        {
            faultyRangeAry = new JSONArrayWrapper();
        }

        JSONArrayWrapper tempFaultyRange = new JSONArrayWrapper();
        tempFaultyRange.put(0, rangeWrapper.getstartRow());
        tempFaultyRange.put(1, rangeWrapper.getstartCol());
        tempFaultyRange.put(2, rangeWrapper.getEndRow());
        tempFaultyRange.put(3, rangeWrapper.getEndCol());

        faultyRangeAry.put(tempFaultyRange);

        faultyRangeListMap.put(sheetName, faultyRangeAry);

    }
}
