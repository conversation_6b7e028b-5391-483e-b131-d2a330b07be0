package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.RangeHighlighterInfoExtractor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.List;

public class RangeHighlighterInfoExtractorImpl implements RangeHighlighterInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public RangeHighlighterInfoExtractorImpl(JSONObjectWrapper actionJson, MacroResponse macroResponse)
    {
        int action = actionJson.getInt(JSONConstants.ACTION);
        RangeWrapper rangeWrapper;
        switch(action){

            case ActionConstants.FILLSERIES:
                if(actionJson.has("mode") && actionJson.getString("mode").equals("auto")){
                    List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                    if (dataRange != null) {
                        for (DataRange range : dataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
                break;

            case ActionConstants.SYSTEMCLIP_PASTE :
                List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            case ActionConstants.MACRO_RUN:
            case ActionConstants.MACRO_SAVE_RUN:

                if(macroResponse != null) {
                    JSONObjectWrapper selectionJSON = macroResponse.getSelectionJson();
                    if(!selectionJSON.isEmpty()) {
                        dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(selectionJSON, false);
                        for (DataRange range : dataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), true);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
                break;

            case ActionConstants.INSERT_CUT_COLUMN:
                dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                List<DataRange> srcRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);

                // handled single range as of now since we are restricting multirange in client itself.
                if (dataRange != null) {
                    for(int i = 0; i< dataRange.size(); i++) {
                        DataRange range = dataRange.get(i);
                        DataRange srcRng = srcRange.get(i);
                        if(srcRng.getStartColIndex() < range.getStartColIndex()) {
                            range.setStartColIndex(range.getStartColIndex() - (srcRng.getEndColIndex() - srcRng.getStartColIndex() + 1));
                            range.setEndColIndex(range.getEndColIndex() - (srcRng.getEndColIndex() - srcRng.getStartColIndex() + 1));
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;

            case ActionConstants.INSERT_CUT_ROW:
                dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                srcRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);

                // handled single range as of now since we are restricting multirange in client itself.
                if (dataRange != null) {
                    for(int i = 0; i< dataRange.size(); i++) {
                        DataRange range = dataRange.get(i);
                        DataRange srcRng = srcRange.get(i);
                        if(srcRng.getStartRowIndex() < range.getStartRowIndex()) {
                            range.setStartRowIndex(range.getStartRowIndex() - (srcRng.getEndRowIndex() - srcRng.getStartRowIndex() + 1));
                            range.setEndRowIndex(range.getEndRowIndex() - (srcRng.getEndRowIndex() - srcRng.getStartRowIndex() + 1));
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;

            case ActionConstants.INSERT_COPY_COLUMN:
            case ActionConstants.INSERT_COPY_ROW:
                dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
