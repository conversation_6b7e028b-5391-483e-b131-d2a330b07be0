package com.adventnet.zoho.websheet.model.response.v1.listener;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.helper.TimeCapsule;
import com.adventnet.zoho.websheet.model.response.viewport.CellInfo;
import com.adventnet.zoho.websheet.model.util.DataRange;

/**
 * <AUTHOR> <PERSON> (ZT-0049)
 */
public interface ResponseListener
{
	public	void	updateCellResposne(Sheet sheet, CellInfo cellInfo);
	public 	void	updateFaultyCellList(String sheetName, CellInfo cellInfo);
	public DataRange updateCellInfo(Sheet sheet, CellInfo cellInfo);
	public DataRange updateCellInfo(Sheet sheet, CellInfo cellInfo, TimeCapsule timeCapsule);
	public	void	updateCell(Cell cell);
	public 	void	updateFaultyRangeList(RangeWrapper rangeWrapper);
	public	Object	getProcessedCellResponse();
	public	Object	getFaultyCellList();
	public	Object	getFaultyRangeList();

}