package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.ColumnHiddenInfoExtractor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.List;

public class ColumnHiddenInfoExtractorImpl implements ColumnHiddenInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public ColumnHiddenInfoExtractorImpl(JSONObjectWrapper actionJson, MacroResponse macroResponse)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        RangeWrapper rangeWrapper;
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        List<DataRange> sourceDataRanges = ActionJsonUtil.getListOfUnfilteredDataRangesFromJsonObject(actionJson, true);
        CommandConstants.OperationType operationType;
        Boolean isUndo = actionJson.has(JSONConstants.FROM_UNDO);
        switch (action) {
            case ActionConstants.INSERT_CUT_COLUMN:
                boolean isSameSheet = actionJson.has(JSONConstants.IS_SAME_SHEET) ? actionJson.getBoolean(JSONConstants.IS_SAME_SHEET) : false;
                CommandConstants.OperationType oprType = !actionJson.has(JSONConstants.FROM_UNDO) ? CommandConstants.OperationType.ADD : CommandConstants.OperationType.REMOVE;
                if(dataRange != null)
                {
                    DataRange sourceDataRange = sourceDataRanges.get(0);
                    for(DataRange range : dataRange)
                    {
                        if(isSameSheet)
                        {
                            int srcStartCol = sourceDataRange.getStartColIndex();
                            int srcEndCol = sourceDataRange.getEndColIndex();
                            int totalSize = srcEndCol - srcStartCol + 1;
                            int startCol = srcStartCol <= range.getStartColIndex() ? range.getStartColIndex() - totalSize : range.getStartColIndex();
                            int endCol = srcEndCol <= range.getEndColIndex() ?  range.getEndColIndex() - totalSize : range.getEndColIndex();
                            if(isUndo)
                            {
                                startCol = srcStartCol <= range.getStartColIndex() ? sourceDataRange.getStartColIndex() : sourceDataRange.getStartColIndex() - totalSize;
                                endCol = srcEndCol <= range.getEndColIndex() ?  sourceDataRange.getEndColIndex() : sourceDataRange.getEndColIndex() - totalSize;
                            }
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), startCol, range.getEndRowIndex(), endCol, CommandConstants.OperationType.ADD);
                        }
                        else
                        {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), oprType);
                        }
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            case ActionConstants.HIDE_COLUMNS:
            case ActionConstants.COLLAPSE_COLGROUP:
            case ActionConstants.COLLAPSEALL_COLGROUPS:
                if (isUndo) {
                    operationType = CommandConstants.OperationType.REMOVE;
                }else{
                    operationType = CommandConstants.OperationType.ADD;
                }
                if (dataRange != null) {
                    dataRange = RangeUtil.sortColAscendingOrder(dataRange);
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(),operationType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;
            case ActionConstants.UNHIDE_COLUMNS:
            case ActionConstants.EXPAND_COLGROUP:
            case ActionConstants.UNGROUP_COL:
            case ActionConstants.EXPANDALL_COLGROUPS:
                if (isUndo) {
                    operationType = CommandConstants.OperationType.ADD;
                }else{
                    operationType = CommandConstants.OperationType.REMOVE;
                }
                if (dataRange != null) {
                    dataRange = RangeUtil.sortColAscendingOrder(dataRange);
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), operationType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                break;

            case ActionConstants.MACRO_RUN:
            case ActionConstants.MACRO_SAVE_RUN:
            case ActionConstants.SUBMIT: //This Submit check is for the fire Worksheet_Change Event.
            case ActionConstants.COPY_PASTE_CONTENT:
            case ActionConstants.COPY_PASTE:
            case ActionConstants.CUT_PASTE:
            case ActionConstants.CHECKBOX_EDIT:
                if(macroResponse != null) {
                    JSONArrayWrapper colArray = macroResponse.getHiddenColumns();
                    for(int i = 0; i < colArray.length(); i++) {
                        dataRange = new ArrayList<>();
                        JSONObjectWrapper childJson = colArray.getJSONObject(i);

                        if(childJson.getBoolean("isHidden")) {
                            operationType = CommandConstants.OperationType.ADD;
                        } else {
                            operationType = CommandConstants.OperationType.REMOVE;
                        }

                        JSONArrayWrapper jsonArray = childJson.getJSONArray(JSONConstants.RANGELIST);
                        for(int j = 0; j < jsonArray.length(); j++) {
                            JSONObjectWrapper json = jsonArray.getJSONObject(j);
                            DataRange range = new DataRange(json.getString(JSONConstants.ASSOCIATED_SHEET_NAME), json.getInt(JSONConstants.START_ROW), json.getInt(JSONConstants.START_COLUMN), json.getInt(JSONConstants.END_ROW), json.getInt(JSONConstants.END_COLUMN));
                            dataRange.add(range);
                        }

                        dataRange = RangeUtil.sortRowAscendingOrder(dataRange);
                        for (DataRange range : dataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), -1, range.getStartColIndex(), -1, range.getEndColIndex(), operationType);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
                if(action == ActionConstants.CUT_PASTE || action == ActionConstants.COPY_PASTE) {
                    if(action == ActionConstants.CUT_PASTE) {
                        List<DataRange> sourceRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
                        if (sourceRanges != null) {
                            RangeUtil.sortColAscendingOrder(sourceRanges);
                            for (DataRange sourceRange : sourceRanges) {
                                if (sourceRange.isEntireColumn()) {
                                    rangeWrapperList.add(new RangeWrapper(sourceRange.getAssociatedSheetName(), -1, sourceRange.getStartColIndex(), -1, sourceRange.getEndColIndex(), isUndo? CommandConstants.OperationType.ADD: CommandConstants.OperationType.REMOVE));
                                }
                            }
                        }
                    }
                    List<DataRange> destRanges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                    if(destRanges != null) {
                        RangeUtil.sortColAscendingOrder(destRanges);
                        for(DataRange destRange: destRanges) {
                            if(destRange.isEntireColumn()) {
                                rangeWrapperList.add(new RangeWrapper(destRange.getAssociatedSheetName(), -1, destRange.getStartColIndex(), -1, destRange.getEndColIndex(),isUndo? CommandConstants.OperationType.REMOVE: CommandConstants.OperationType.ADD));
                            }
                        }
                    }

                }
                break;
            case ActionConstants.CLEAROUTLINE:
                rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), -1, 0, -1, Utility.MAXNUMOFCOLS-1, isUndo ? CommandConstants.OperationType.ADD: CommandConstants.OperationType.REMOVE);
                rangeWrapperList.add(rangeWrapper);
                break;
            case -1:
                rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), -1, actionJson.getInt(JSONConstants.START_COLUMN), -1, actionJson.getInt(JSONConstants.END_COLUMN), CommandConstants.OperationType.GENERATE_LIST);
                rangeWrapperList.add(rangeWrapper);
                break;
            default:
                break;
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
