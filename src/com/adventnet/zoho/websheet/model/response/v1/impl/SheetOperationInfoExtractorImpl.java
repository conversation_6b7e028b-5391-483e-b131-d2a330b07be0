package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.SheetWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.SheetOperationInfoExtractor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.List;

public class SheetOperationInfoExtractorImpl implements SheetOperationInfoExtractor {

    List<SheetWrapper> sheetList = new ArrayList<>();
    public SheetOperationInfoExtractorImpl(JSONObjectWrapper actionJson, MacroResponse macroResponse)
    {
        SheetWrapper sheetWrapper = null;
        int action = actionJson != null && actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        List<String> asnSheetList = ActionJsonUtil.getListOfAssociateSheetNames(actionJson, false);
        switch (action) {
            case ActionConstants.SHEET_COPY:
            case ActionConstants.SHEET_INSERT:
            case ActionConstants.INSERT_SHEET_WITH_CLIP_CONTENT:
            case ActionConstants.SHEET_DUPLICATE:
                sheetWrapper = new SheetWrapper(actionJson.getString(JSONConstants.NEW_SHEET_NAME), (String) null, CommandConstants.OperationType.INSERT);
                sheetList.add(sheetWrapper);
                break;
            case ActionConstants.FIELDS_DATA_NEW_WORKSHEET:
                JSONArrayWrapper sheetNameJsonArray = actionJson.optJSONArray(JSONConstants.SHEET_NAMES_LIST);
                JSONArrayWrapper asnJsonArray = actionJson.optJSONArray(JSONConstants.SHEETLIST).getJSONArray(0);
                for(int i=0;i<sheetNameJsonArray.length();i++) {
                    sheetWrapper = new SheetWrapper(sheetNameJsonArray.getString(i), asnJsonArray.getString(i), CommandConstants.OperationType.FIELDS_SHEET_INSERT);
                    sheetList.add(sheetWrapper);
                }
                break;
            case ActionConstants.SHEET_MOVE:
                for(String asn : asnSheetList){
                    sheetWrapper = new SheetWrapper(asn, CommandConstants.OperationType.MOVE);
                    sheetList.add(sheetWrapper);
                }
                break;
            case ActionConstants.FORM_CREATE:
                JSONObjectWrapper formResponseObj = actionJson.getJSONObject(JSONConstants.FORM_RESPONSE);
                if (formResponseObj.getBoolean(JSONConstants.IS_SUCCESS)) {
                    for(String asn : asnSheetList){
                        sheetWrapper = new SheetWrapper(actionJson.getString(JSONConstants.NEW_SHEET_NAME), asn, CommandConstants.OperationType.INSERT);
                        sheetList.add(sheetWrapper);
                    }
                }
                break;
            case ActionConstants.SHEET_TABCOLOR:
                for(String asn : asnSheetList){
                    sheetWrapper = new SheetWrapper(asn, CommandConstants.OperationType.SHEET_TABCOLOR);
                    sheetList.add(sheetWrapper);
                }
                break;
            case ActionConstants.SHEET_RENAME:
                for(String asn : asnSheetList){
                    sheetWrapper = new SheetWrapper(actionJson.getString(JSONConstants.SHEET_NAME), asn, CommandConstants.OperationType.RENAME);
                    sheetList.add(sheetWrapper);
                }
                break;
            case ActionConstants.SHEET_VISIBLE:
                for(String asn : asnSheetList){
                    boolean isHidden = actionJson.getBoolean(JSONConstants.IS_HIDDEN);
                    CommandConstants.OperationType optType = (isHidden) ? CommandConstants.OperationType.HIDE : CommandConstants.OperationType.UNHIDE;
                    sheetWrapper = new SheetWrapper(asn, optType);
                    sheetList.add(sheetWrapper);
                }
                break;
            case ActionConstants.SHEET_REMOVE:
                for(String asn : asnSheetList){
                    sheetWrapper = new SheetWrapper(asn, CommandConstants.OperationType.DELETE);
                    sheetList.add(sheetWrapper);
                }
                break;
            case ActionConstants.MOVE_PIVOT:
            case ActionConstants.CREATE_PIVOT :
            case ActionConstants.DC_APPLY_FORMATS:
            case ActionConstants.GET_DATA_DUPLICATES:
            case ActionConstants.EDIT_PIVOT :
            case ActionConstants.COPY_PIVOT :
            case ActionConstants.PIVOT_SHOW_DETAILS:
                if (actionJson.getBoolean(JSONConstants.IS_NEW_SHEET)) {
                    // for(String asn : asnSheetList){
                    sheetWrapper = new SheetWrapper(actionJson.getString(JSONConstants.NEW_SHEET_NAME), (String) null, CommandConstants.OperationType.INSERT);
                    sheetList.add(sheetWrapper);
                    // }
                }
                break;
            case ActionConstants.SERVERCLIP_PASTE_SHEET:
//                for(String asn : asnSheetList){
//                    sheetWrapper = new SheetWrapper(actionJson.getJSONArray(JSONConstants.PASTED_SHEETNAMES), asn, OperationType.PASTE);
//                    sheetList.add(sheetWrapper);
//                }
//                break;
            case ActionConstants.IMPORT_INSERT_AS_NEW_SHEETS:
                CommandConstants.OperationType type = (action == ActionConstants.SERVERCLIP_PASTE_SHEET) ? CommandConstants.OperationType.PASTE : CommandConstants.OperationType.IMPORT_SHEET_INSERT;
                JSONArrayWrapper pastedSheetNames = actionJson.getJSONArray(JSONConstants.PASTED_SHEETNAMES);
                for(String asn : asnSheetList){
                    sheetWrapper = new SheetWrapper(asn, pastedSheetNames, type);
                    sheetList.add(sheetWrapper);
                }
                break;
           /* case ActionConstants.CREATE_PIVOT:

            	if(actionJson.getBoolean(JSONConstants.IS_NEW_SHEET)){

            		sheetWrapper = new SheetWrapper(actionJson.getString(JSONConstants.NEW_SHEET_NAME), null, false, OperationType.INSERT);
                    sheetList.add(sheetWrapper);
            	}*/
            case ActionConstants.APPLY_THEME:
                for(String asn : asnSheetList){
                    sheetWrapper = new SheetWrapper(asn, CommandConstants.OperationType.THEME);
                    sheetList.add(sheetWrapper);
                }
                break;
            case -1:
                //To generate the sheet list while loading docs
                //We can add the check actionJson != null if required
                if(actionJson.has(JSONConstants.RANGE_META)) {
                    JSONObjectWrapper rangeJson = actionJson.getJSONObject(JSONConstants.RANGE_META);
                    String sheetName = rangeJson.getString(JSONConstants.SHEET_NAME);
                    sheetWrapper = new SheetWrapper(sheetName);
                } else {
                    sheetWrapper = new SheetWrapper(CommandConstants.OperationType.GENERATE_LIST);
                }
                sheetList.add(sheetWrapper);
                break;
        }
        if (macroResponse != null && !macroResponse.isEmpty()) {
            ArrayList<JSONObjectWrapper> sheetActionsList = macroResponse.getSheetActionsList();
            int size = sheetActionsList.size();
            List<SheetWrapper> macroList = new ArrayList<SheetWrapper>();
            for (int i = 0; i < size; i++) {
                JSONObjectWrapper sheetAction = sheetActionsList.get(i);
                action = sheetAction.getInt(JSONConstants.ACTION);

                switch (action) {
                    case ActionConstants.SHEET_INSERT:
                    case ActionConstants.INSERT_SHEET_WITH_CLIP_CONTENT:
                    case ActionConstants.SHEET_COPY:
                    case ActionConstants.SHEET_DUPLICATE:
                        sheetWrapper = new SheetWrapper(sheetAction.getString(JSONConstants.SHEET_NAME), sheetAction.getString("cn"), CommandConstants.OperationType.INSERT);
                        break;
                    case ActionConstants.SHEET_RENAME:
                        sheetWrapper = new SheetWrapper(sheetAction.getString(JSONConstants.SHEET_NAME), sheetAction.getString("asn"), CommandConstants.OperationType.RENAME);
                        break;
                    case ActionConstants.SHEET_MOVE:
                        sheetWrapper = new SheetWrapper(sheetAction.getString("asn"), CommandConstants.OperationType.MOVE);
                        break;
                    case ActionConstants.SHEET_REMOVE:
                        sheetWrapper = new SheetWrapper(sheetAction.getString("asn"), CommandConstants.OperationType.DELETE);
                        break;
                }
                if (sheetWrapper != null) {
                    sheetList.add(sheetWrapper);
                }
            }
            JSONArrayWrapper colArray = macroResponse.getVisibleSheet();
            for(int i = 0; i < colArray.length(); i++) {
                JSONObjectWrapper childJson = colArray.getJSONObject(i);
                boolean isHidden = childJson.getBoolean(JSONConstants.IS_HIDDEN);
                CommandConstants.OperationType optType = (isHidden) ? CommandConstants.OperationType.HIDE : CommandConstants.OperationType.UNHIDE;
                sheetWrapper = new SheetWrapper(childJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), optType);
                sheetList.add(sheetWrapper);
            }

        }
    }

    @Override
    public List<SheetWrapper> getSheetList() {
        return sheetList;
    }
}
