package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.DiscussionBean;
import com.adventnet.zoho.websheet.model.response.extractor.DiscussionInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.Iterator;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DiscussionInfoExtractorImpl implements DiscussionInfoExtractor {

    public static final Logger LOGGER = Logger.getLogger(DiscussionInfoExtractorImpl.class.getName());

    DiscussionBean discussionBean;

    public DiscussionInfoExtractorImpl(JSONObjectWrapper actionJson, JSONObjectWrapper disIdObj)
    {
        try
        {

//    		LOGGER.info("actionJson::"+actionJson.getJSONObject("sheetList"));
            int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
            CommandConstants.OperationType opType = null;
            JSONObjectWrapper deleteDiscussionObj = new JSONObjectWrapper();
            JSONObjectWrapper deleteReplyobj = new JSONObjectWrapper();
            switch (action) {
                case -1:
                    opType = CommandConstants.OperationType.GENERATE_LIST;
                    disIdObj = modifyMeta(disIdObj);
                    discussionBean = new DiscussionBean(opType, disIdObj);
                    break;
                case ActionConstants.DISCUSSION_ADD:
                case ActionConstants.DISCUSSION_ADD_REPLY:
                case ActionConstants.DISCUSSION_RESOLVE:
                case ActionConstants.DISCUSSION_REOPEN:
                    opType = CommandConstants.OperationType.ADD;
                    disIdObj =modify(disIdObj,action);
                    discussionBean = new DiscussionBean(opType, disIdObj);
                    break;
                case ActionConstants.DISCUSSION_DELETE:
                    opType = CommandConstants.OperationType.DELETE;
                    deleteDiscussionObj.put(Integer.toString(CommandConstants.ID), disIdObj.get("uid"));
                    discussionBean = new DiscussionBean(opType, deleteDiscussionObj);
                    break;
                case ActionConstants.DISCUSSION_DELETE_ALL:
                    opType = CommandConstants.OperationType.DELETE;
                    deleteDiscussionObj.put(Integer.toString(CommandConstants.ALL), true);
                    boolean sheetBased = actionJson.has("isSheetBased") ? actionJson.getBoolean("isSheetBased") : false;
                    if(sheetBased) {
                        JSONArrayWrapper sheetList = actionJson.has(JSONConstants.SHEETLIST) ? actionJson.getJSONArray(JSONConstants.SHEETLIST) : null;
                        deleteDiscussionObj.put(Integer.toString(CommandConstants.SHEET_LIST), sheetList);
                    }
                    discussionBean = new DiscussionBean(opType, deleteDiscussionObj);
                    break;
                case ActionConstants.DISCUSSION_DELETE_REPLY:
                    opType = CommandConstants.OperationType.DELETE_REPLY;
                    deleteDiscussionObj.put(Integer.toString(CommandConstants.ID), disIdObj.get("uid"));
                    deleteReplyobj.put(Integer.toString(CommandConstants.ID), disIdObj.get("cid"));
                    deleteDiscussionObj.put(Integer.toString(CommandConstants.REPLY), deleteReplyobj);
                    discussionBean = new DiscussionBean(opType, deleteDiscussionObj);
                    break;
                case ActionConstants.DISCUSSION_UPDATE_REPLY:
                case ActionConstants.DISCUSSION_UPDATE_RANGE_CONTENT:
                    opType = CommandConstants.OperationType.MODIFY;
                    disIdObj =modify(disIdObj,action);
                    discussionBean = new DiscussionBean(opType, disIdObj);
                    break;
                case ActionConstants.DISCUSSION_LIKE:
                case ActionConstants.DISCUSSION_UNLIKE:
                    opType = CommandConstants.OperationType.LIKE;
                    disIdObj =modify(disIdObj,action);
                    discussionBean = new DiscussionBean(opType, disIdObj);
                    break;
                case ActionConstants.DISCUSSION_LIKE_REPLY:
                case ActionConstants.DISCUSSION_UNLIKE_REPLY:
                    opType = CommandConstants.OperationType.LIKE_REPLY;
                    disIdObj =modify(disIdObj,action);
                    discussionBean = new DiscussionBean(opType, disIdObj);
                    break;
                case ActionConstants.CUT_PASTE:
                case ActionConstants.INSERT_CUT_COLUMN:
                case ActionConstants.INSERT_CUT_ROW:
                    opType = CommandConstants.OperationType.PASTE;
                    JSONArrayWrapper sheetArray = actionJson.has(JSONConstants.SHEETLIST) ? actionJson.getJSONArray(JSONConstants.SHEETLIST) : null;
                    JSONArrayWrapper rangeArray = actionJson.has(JSONConstants.RANGELIST) ? actionJson.getJSONArray(JSONConstants.RANGELIST) : null;
                    discussionBean = new DiscussionBean(opType, disIdObj, sheetArray.getJSONArray(0), rangeArray.getJSONArray(0));
                    break;
            }

        }catch(Exception e) {
            LOGGER.log(Level.WARNING, "Exception while construction discussion object", e);
        }
    }
    @Override
    public DiscussionBean getDiscussionBean() {
        return discussionBean;
    }

    public JSONObjectWrapper modify (JSONObjectWrapper disIdObj,int action)
    {
        JSONObjectWrapper discussionObj =new JSONObjectWrapper();
        JSONObjectWrapper replyobj =new JSONObjectWrapper();
        if(disIdObj.has("uid"))
        {
            discussionObj.put(Integer.toString(CommandConstants.ID), disIdObj.get("uid"));
        }
        if (disIdObj.has("cid"))
        {
            replyobj.put(Integer.toString(CommandConstants.ID), disIdObj.get("cid"));
        }
        if (disIdObj.has("type"))
        {
            replyobj.put(Integer.toString(CommandConstants.ACTION_TYPE), disIdObj.get("type"));
        }
        if (disIdObj.has("range"))
        {
            JSONObjectWrapper srcRange =disIdObj.getJSONObject("range");
            JSONArrayWrapper rangeList =new JSONArrayWrapper();
            JSONArrayWrapper range =new JSONArrayWrapper();
            rangeList.put(disIdObj.get("asn"));
            rangeList.put(srcRange.get("sr"));
            rangeList.put(srcRange.get("sc"));
            rangeList.put(srcRange.get("er"));
            rangeList.put(srcRange.get("ec"));
            range.put(rangeList);
            discussionObj.put(Integer.toString(CommandConstants.RANGES), range);
        }
        if (disIdObj.has("DU"))
        {
            if(action == ActionConstants.DISCUSSION_ADD || action == ActionConstants.DISCUSSION_UPDATE_RANGE_CONTENT || action == ActionConstants.DISCUSSION_LIKE || action == ActionConstants.DISCUSSION_UNLIKE)
            {
                discussionObj.put(Integer.toString(CommandConstants.MSG_UNREAD), disIdObj.get("DU"));
            }
            else{
                replyobj.put(Integer.toString(CommandConstants.MSG_UNREAD), disIdObj.get("DU"));
            }
        }
        if (disIdObj.has("t"))
        {
            if(action == ActionConstants.DISCUSSION_ADD )
            {
                discussionObj.put(Integer.toString(CommandConstants.TIME), disIdObj.get("t"));
            }
            if(action == ActionConstants.DISCUSSION_ADD_REPLY || action == ActionConstants.DISCUSSION_RESOLVE || action == ActionConstants.DISCUSSION_REOPEN )
            {
                replyobj.put(Integer.toString(CommandConstants.TIME), disIdObj.get("t"));
            }
        }
        if (disIdObj.has("likes"))
        {
            Object element = null;
            JSONObjectWrapper srcLikeObj = new JSONObjectWrapper();
            JSONObjectWrapper destLikeObj = new JSONObjectWrapper();
            Boolean likeState = (action == ActionConstants.DISCUSSION_LIKE || action == ActionConstants.DISCUSSION_LIKE_REPLY);
            if(likeState)
            {
                srcLikeObj=disIdObj.getJSONObject("likes");
                String username =disIdObj.has("oid") ? srcLikeObj.getString(disIdObj.getString("oid")):""; // No I18N
                destLikeObj.put(Integer.toString(CommandConstants.USER_ID), disIdObj.getString("oid"));
                destLikeObj.put(Integer.toString(CommandConstants.USER_NAME), username);

                destLikeObj.put(Integer.toString(CommandConstants.STATE), true);
                if(action == ActionConstants.DISCUSSION_LIKE )
                {
                    discussionObj.put(Integer.toString(CommandConstants.LIKES), destLikeObj);
                }
                else if( action == ActionConstants.DISCUSSION_LIKE_REPLY)
                {
                    replyobj.put(Integer.toString(CommandConstants.LIKES), destLikeObj);
                }
            }

            else
            {
                destLikeObj.put(Integer.toString(CommandConstants.USER_NAME), disIdObj.get("on"));
                if(disIdObj.has("oid")) {
                    destLikeObj.put(Integer.toString(CommandConstants.USER_ID), disIdObj.get("oid"));
                }
                destLikeObj.put(Integer.toString(CommandConstants.STATE), false);
                if(action == ActionConstants.DISCUSSION_UNLIKE ) {
                    discussionObj.put(Integer.toString(CommandConstants.LIKES), destLikeObj);
                }else if( action == ActionConstants.DISCUSSION_UNLIKE_REPLY){
                    replyobj.put(Integer.toString(CommandConstants.LIKES), destLikeObj);
                }
            }

        }
        if (disIdObj.has("c"))
        {
            if(action == ActionConstants.DISCUSSION_ADD || action == ActionConstants.DISCUSSION_UPDATE_RANGE_CONTENT )
            {
                discussionObj.put(Integer.toString(CommandConstants.MSG), disIdObj.get("c"));
            }
            if(action == ActionConstants.DISCUSSION_ADD_REPLY || action == ActionConstants.DISCUSSION_UPDATE_REPLY)
            {
                replyobj.put(Integer.toString(CommandConstants.MSG), disIdObj.get("c"));
            }
        }
        if (disIdObj.has("oid"))
        {
            if(action == ActionConstants.DISCUSSION_ADD)
            {
                discussionObj.put(Integer.toString(CommandConstants.OWNER_ID), disIdObj.get("oid"));
            }
            if(action == ActionConstants.DISCUSSION_ADD_REPLY||action == ActionConstants.DISCUSSION_REOPEN||action == ActionConstants.DISCUSSION_RESOLVE){
                replyobj.put(Integer.toString(CommandConstants.OWNER_ID), disIdObj.get("oid"));
            }
        }
        if (disIdObj.has("on"))
        {
            if(action == ActionConstants.DISCUSSION_ADD || action == ActionConstants.DISCUSSION_UPDATE_RANGE_CONTENT )
            {
                discussionObj.put(Integer.toString(CommandConstants.OWNER), disIdObj.get("on"));
            }
            if(action == ActionConstants.DISCUSSION_ADD_REPLY||action == ActionConstants.DISCUSSION_REOPEN||action == ActionConstants.DISCUSSION_RESOLVE)
            {
                replyobj.put(Integer.toString(CommandConstants.OWNER), disIdObj.get("on"));
            }
        }
        if(disIdObj.has("r_cid")) {
            replyobj.put(Integer.toString(CommandConstants.R_ID), disIdObj.get("r_cid"));
        }
        if(!replyobj.isEmpty()) {
            discussionObj.put(Integer.toString(CommandConstants.REPLY), replyobj);
        }
        return discussionObj;
    }

    public JSONObjectWrapper modifyMeta (JSONObjectWrapper disIdObj)
    {
        JSONObjectWrapper discussionResponseObj = new JSONObjectWrapper();
        JSONArrayWrapper disuccionList =new JSONArrayWrapper();

        try {

            if(disIdObj.has("dns"))
            {
                JSONArrayWrapper discussionArray=disIdObj.getJSONObject("dns").getJSONArray("dnA");	// No I18N
                for(int i=0,k=0;i<discussionArray.length();i++)
                {
                    JSONObjectWrapper destDiscussionObj=new JSONObjectWrapper();
                    JSONObjectWrapper discussionObj = new JSONObjectWrapper();
                    try {
                        discussionObj = discussionArray.getJSONObject(i);
                    } catch(Exception e) {
                        LOGGER.log(Level.INFO, "Exception on modify discussion Meta ::", e);
                        continue;
                    }
                    if(discussionObj != null) {
                        if(discussionObj.has("cmtA"))
                        {
                            JSONArrayWrapper replyAry =new JSONArrayWrapper();
                            JSONArrayWrapper replyList=discussionObj.getJSONArray("cmtA");	// No I18N
                            for(int j=0;j<replyList.length();j++)
                            {
                                JSONObjectWrapper destReplyObj =new JSONObjectWrapper();
                                JSONObjectWrapper srcReplyObj = replyList.getJSONObject(j);
                                destReplyObj.put(Integer.toString(CommandConstants.ID), srcReplyObj.get("cid"));
                                destReplyObj.put(Integer.toString(CommandConstants.ACTION_TYPE), srcReplyObj.get("type"));
                                JSONObjectWrapper details= srcReplyObj.getJSONObject("details");
                                if(details.has("ur")) {

                                    destReplyObj.put(Integer.toString(CommandConstants.MSG_UNREAD), details.get("ur"));
                                }
                                if(details.has("oid")) {

                                    destReplyObj.put(Integer.toString(CommandConstants.OWNER_ID), details.get("oid"));
                                }
                                if(details.has("likes") ) {
                                    JSONArrayWrapper destLikeAry = new JSONArrayWrapper();
                                    JSONObjectWrapper srcLikeObj=details.getJSONObject("likes");
                                    if(!srcLikeObj.isEmpty()) {
                                        Iterator iterator = srcLikeObj.keys();
                                        while(iterator.hasNext()) {
                                            JSONObjectWrapper likobj =new JSONObjectWrapper();
                                            Object element = iterator.next();
                                            String username =srcLikeObj.getString(element.toString());
                                            likobj.put(Integer.toString(CommandConstants.USER_ID), element);
                                            likobj.put(Integer.toString(CommandConstants.USER_NAME), username);
                                            destLikeAry.put(likobj);
                                        }
                                        destReplyObj.put(Integer.toString(CommandConstants.LIKES), destLikeAry);
                                    }
                                }
                                if(details.has("t")) {

                                    destReplyObj.put(Integer.toString(CommandConstants.TIME), details.get("t"));
                                }
                                if(details.has("on")) {

                                    destReplyObj.put(Integer.toString(CommandConstants.OWNER), details.get("on"));
                                }
                                if(details.has("c") && srcReplyObj.getInt("type")==0) {

                                    destReplyObj.put(Integer.toString(CommandConstants.MSG), details.get("c"));
                                }
                                replyAry.put(destReplyObj);
                            }

                            destDiscussionObj.put(Integer.toString(CommandConstants.REPLY), replyAry);
                        }
                        JSONObjectWrapper discussionInfo=discussionObj.getJSONObject("details");
                        if(discussionInfo.has("ur")) {
                            destDiscussionObj.put(Integer.toString(CommandConstants.MSG_UNREAD), discussionInfo.get("ur"));
                        }
                        if(discussionInfo.has("s")) {
                            destDiscussionObj.put(Integer.toString(CommandConstants.STATE), discussionInfo.get("s"));
                        }
                        if(discussionInfo.has("c")) {

                            destDiscussionObj.put(Integer.toString(CommandConstants.MSG), discussionInfo.get("c"));
                        }
                        if(discussionInfo.has("oid")) {

                            destDiscussionObj.put(Integer.toString(CommandConstants.OWNER_ID), discussionInfo.get("oid"));
                        }
                        if(discussionInfo.has("on")) {

                            destDiscussionObj.put(Integer.toString(CommandConstants.OWNER), discussionInfo.get("on"));
                        }
                        if(discussionInfo.has("t")) {

                            destDiscussionObj.put(Integer.toString(CommandConstants.TIME), discussionInfo.get("t"));
                        }
                        if(discussionInfo.has("likes")) {
                            JSONObjectWrapper srcLikeObj=discussionInfo.getJSONObject("likes");
                            JSONArrayWrapper destLikeAry =new JSONArrayWrapper();
                            if(!srcLikeObj.isEmpty()) {
                                Iterator iterator = srcLikeObj.keys();
                                while(iterator.hasNext()) {
                                    JSONObjectWrapper likeObj =new JSONObjectWrapper();
                                    Object element = iterator.next();
                                    String username =srcLikeObj.getString(element.toString());
                                    likeObj.put(Integer.toString(CommandConstants.USER_ID), element);
                                    likeObj.put(Integer.toString(CommandConstants.USER_NAME), username);
                                    destLikeAry.put(likeObj);
                                }
                                destDiscussionObj.put(Integer.toString(CommandConstants.LIKES), destLikeAry);
                            }
                        }
                        if(discussionInfo.has("range")) {
                            JSONArrayWrapper rangeList =new JSONArrayWrapper();
                            JSONArrayWrapper range =new JSONArrayWrapper();
                            JSONObjectWrapper rangeobj=discussionInfo.getJSONObject("range");
                            rangeList.put(discussionInfo.get("asn"));
                            rangeList.put(rangeobj.get("sr"));
                            rangeList.put(rangeobj.get("sc"));
                            rangeList.put(rangeobj.get("er"));
                            rangeList.put(rangeobj.get("ec"));
                            range.put(rangeList);
                            destDiscussionObj.put(Integer.toString(CommandConstants.RANGES), range);
                        }
                        destDiscussionObj.put(Integer.toString(CommandConstants.ID), discussionObj.get("uid"));
                        disuccionList.put(k++,destDiscussionObj);
                    }
                }
                discussionResponseObj.put(Integer.toString(CommandConstants.DISCUSSION), disuccionList);
                return discussionResponseObj;
            }
        } catch(Exception e) {
            LOGGER.log(Level.INFO, "Exception on modify discussion Meta ::", e);
        }
        return disIdObj;

    }
}
