package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.PublishedViewWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.PublishedViewInfoExtractor;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;

public class PublishedViewInfoExtractorImpl implements PublishedViewInfoExtractor {

    List<PublishedViewWrapper> publishedViewWrapperList = new ArrayList<>();
    public PublishedViewInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        CommandConstants.OperationType operationType;
        PublishedViewWrapper publishedViewWrapper;
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        if (action == -1)
        {
            String rangeId = null;
            if(actionJson.has(JSONConstants.RANGE_META)) {
                rangeId = actionJson.getJSONObject(JSONConstants.RANGE_META).getString(JSONConstants.RANGE_ID);
            } else if(actionJson.has(JSONConstants.RANGE_ID)) {
                rangeId = actionJson.getString(JSONConstants.RANGE_ID);
            }
            operationType = CommandConstants.OperationType.HIDE_SHOW_GRID;
            publishedViewWrapper = new PublishedViewWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), operationType, rangeId);
            publishedViewWrapperList.add(publishedViewWrapper);
        }
    }

    @Override
    public List<PublishedViewWrapper> getPublishedViewWrapperList() {
        return publishedViewWrapperList;
    }
}
