package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.FaultySheetsInfoExtractor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.HashSet;

public class FaultySheetsInfoExtractorImpl implements FaultySheetsInfoExtractor {

    HashSet<String> sheetList = new HashSet<>();
    boolean makeAllSheetsFaulty;
    public FaultySheetsInfoExtractorImpl(JSONObjectWrapper actionJson, MacroResponse macroResponse)
    {
        if(macroResponse != null && !macroResponse.isEmpty()) {
            sheetList = macroResponse.getReloadTileSheetsSet();
        }
        int	action	= actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        if(action == ActionConstants.DOCUMENT_SETTING || action == ActionConstants.APPLY_THEME || action == ActionConstants.REFRESH_ALL_PIVOT) {
            makeAllSheetsFaulty = true;
        }
        if(!makeAllSheetsFaulty) {
            //For whole sheet actions
            String  associatedSheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
            JSONObjectWrapper actionRange = ActionJsonUtil.getFirstRangeFromJsonArray(actionJson.optJSONArray(JSONConstants.RANGELIST));
            int sR	= -1;
            int sC	= -1;
            int eR	= -1;
            int eC	= -1;
            if(actionRange != null) {
                sR	= actionRange.getInt(JSONConstants.START_ROW);
                sC	= actionRange.getInt(JSONConstants.START_COLUMN);
                eR	= actionRange.getInt(JSONConstants.END_ROW);
                eC	= actionRange.getInt(JSONConstants.END_COLUMN);
            }
            //For whole sheet level operation, making whole sheet faulty and respectively there will be no cellresponse
            if(sR == 0 && sC == 0 &&  eR == Utility.MAXNUMOFROWS -1 && eC  ==  Utility.MAXNUMOFCOLS -1)	{
                sheetList.add(associatedSheetName);
            }

        }
        if(action == ActionConstants.IMPORT_REPLACE_CURRENTSHEET || action == ActionConstants.IMPORT_APPEND_ROWS_TO_CURRENTSHEET  || action == ActionConstants.IMPORT_REPLACE_DATA_STARTING_AT_CELL ){
            String associatedSheetName = ActionJsonUtil.getAssociateSheetName(actionJson);
            sheetList.add(associatedSheetName);
        }
    }

    @Override
    public HashSet<String> getSheetList() {
        return sheetList;
    }

    @Override
    public boolean isMakeAllSheetsFaulty() {
        return makeAllSheetsFaulty;
    }
}
