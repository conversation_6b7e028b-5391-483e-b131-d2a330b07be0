package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.CheckBoxInfoExtractor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.List;

public class CheckBoxInfoExtractorImpl implements CheckBoxInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public CheckBoxInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        CommandConstants.OperationType operationType;
        RangeWrapper rangeWrapper;
        boolean isUndoAction = actionJson.has(JSONConstants.FROM_UNDO) && actionJson.getBoolean(JSONConstants.FROM_UNDO);
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        List<DataRange> sourceDataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
        if (action == -1) {
            operationType = CommandConstants.OperationType.GENERATE_LIST;
            rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), actionJson.getInt(JSONConstants.START_ROW),
                    actionJson.getInt(JSONConstants.START_COLUMN), actionJson.getInt(JSONConstants.END_ROW), actionJson.getInt(JSONConstants.END_COLUMN), operationType);
            rangeWrapperList.add(rangeWrapper);


        } else if (action == ActionConstants.CHECKBOX_ADD) {

            operationType = (isUndoAction) ? CommandConstants.OperationType.REMOVE : CommandConstants.OperationType.ADD;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }

        } else if (action == ActionConstants.CHECKBOX_EDIT) {
            operationType = CommandConstants.OperationType.MODIFY;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }

        } else if (action == ActionConstants.CHECKBOX_CLEAR) {

            operationType = (isUndoAction) ? CommandConstants.OperationType.ADD : CommandConstants.OperationType.REMOVE;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }

        } else if (action == ActionConstants.CLEARALL || action == ActionConstants.CLEARCONTENTS) {
            operationType = (isUndoAction) ? CommandConstants.OperationType.ADD : CommandConstants.OperationType.REMOVE;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }

        } else if (action == ActionConstants.CUT_PASTE) {
            if (action == ActionConstants.CUT_PASTE) {
                operationType = isUndoAction ? CommandConstants.OperationType.ADD : CommandConstants.OperationType.REMOVE;
                if (sourceDataRange != null) {
                    for (DataRange range : sourceDataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }
            operationType = isUndoAction ? CommandConstants.OperationType.REMOVE : CommandConstants.OperationType.ADD;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }

        } else if(action == ActionConstants.FILLSERIES || action == ActionConstants.COPY_PASTE || action == ActionConstants.SERVERCLIP_PASTE_RANGE || action == ActionConstants.SYSTEMCLIP_PASTE || action == ActionConstants.COPY_PASTE_CONTENT){
            operationType =  CommandConstants.OperationType.REMOVE;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }

            operationType =  CommandConstants.OperationType.ADD;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }

        }
        else if (action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.INSERT_CELL_TOP || action == ActionConstants.TABLE_INSERT_COL || action == ActionConstants.TABLE_DELETE_ROW || action == ActionConstants.TABLE_DELETE_COL || action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.DELETE_CELL_RIGHT
                || action == ActionConstants.DELETE_CELL_BOTTOM || action == ActionConstants.TABLE_CREATE ||  (action == ActionConstants.TABLE_TOGGLE_HEADER_FOOTER && actionJson.optBoolean(JSONConstants.TABLE_INSERT_CELLS, false))) {

            operationType = CommandConstants.OperationType.REMOVE;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    int eR = range.getEndRowIndex();
                    int eC = range.getEndColIndex();

                    if (action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.TABLE_INSERT_COL || action == ActionConstants.TABLE_DELETE_COL) {
                        eC = Utility.MAXNUMOFCOLS;
                    } else {
                        eR = Utility.MAXNUMOFROWS;
                    }
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), eR, eC, operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
            operationType = CommandConstants.OperationType.ADD;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    int eR = range.getEndRowIndex();
                    int eC = range.getEndColIndex();

                    if (action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.TABLE_INSERT_COL || action == ActionConstants.TABLE_DELETE_COL) {
                        eC = Utility.MAXNUMOFCOLS;
                    } else {
                        eR = Utility.MAXNUMOFROWS;
                    }
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), eR, eC, operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }

        } else if (action == ActionConstants.INSERT_ROW) {
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), 0, range.getEndRowIndex(), Utility.MAXNUMOFCOLS-1, CommandConstants.OperationType.ADD);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        } else if (action == ActionConstants.INSERT_COL) {
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), 0, range.getStartColIndex(), Utility.MAXNUMOFROWS-1, range.getEndColIndex(), CommandConstants.OperationType.ADD);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        } else if (action == ActionConstants.INSERT_COPY_ROW || action == ActionConstants.INSERT_COPY_COLUMN) {
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), CommandConstants.OperationType.ADD);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        } else if (action == ActionConstants.INSERT_CUT_ROW || action == ActionConstants.INSERT_CUT_COLUMN) {
            boolean isSameSheet = actionJson.has(JSONConstants.IS_SAME_SHEET) ? actionJson.getBoolean(JSONConstants.IS_SAME_SHEET) : false;

            int sR = dataRange.get(0).getStartRowIndex();
            int eR = dataRange.get(0).getEndRowIndex();
            int sC = dataRange.get(0).getStartColIndex();
            int eC = dataRange.get(0).getEndColIndex();
            String sheetName = dataRange.get(0).getAssociatedSheetName();

            int ssR = sourceDataRange.get(0).getStartRowIndex();
            int seR = sourceDataRange.get(0).getEndRowIndex();
            int ssC = sourceDataRange.get(0).getStartColIndex();
            int seC = sourceDataRange.get(0).getEndColIndex();
            String sourceSheetName = sourceDataRange.get(0).getAssociatedSheetName();

            if (!isUndoAction) {
                if (isSameSheet) {
                    if (action == ActionConstants.INSERT_CUT_ROW && (sR > ssR)) {
                        int count = seR - ssR + 1;
                        sR = sR - count;
                        eR = eR - count;
                    }
                    if (action == ActionConstants.INSERT_CUT_COLUMN && (sC > ssC)) {
                        int count = seC - ssC + 1;
                        sC = sC - count;
                        eC = eC - count;
                    }
                } else {
                    rangeWrapper = new RangeWrapper(sourceSheetName, ssR, ssC, seR, seC, CommandConstants.OperationType.REMOVE);
                    rangeWrapperList.add(rangeWrapper);
                }
                rangeWrapper = new RangeWrapper(sheetName, sR, sC, eR, eC, CommandConstants.OperationType.ADD);
                rangeWrapperList.add(rangeWrapper);

            } else {
                if (isSameSheet) {
                    if (action == ActionConstants.INSERT_CUT_ROW && (ssR > sR)) {
                        int count = eR - sR + 1;
                        ssR = ssR - count;
                        seR = seR - count;
                    }
                    if (action == ActionConstants.INSERT_CUT_COLUMN && (ssC > sC)) {
                        int count = eC - sC + 1;
                        ssC = ssC - count;
                        seC = seC - count;
                    }
                    rangeWrapper = new RangeWrapper(sourceSheetName, ssR, ssC, seR, seC, CommandConstants.OperationType.ADD);
                    rangeWrapperList.add(rangeWrapper);
                }
            }

        } else if (action == ActionConstants.MERGE_RANGE || action == ActionConstants.MERGE_AND_CENTER || action == ActionConstants.MERGE_ACROSS || action == ActionConstants.MERGE_DOWN || action == ActionConstants.MERGE_SPLIT) {
            operationType = CommandConstants.OperationType.REMOVE;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
            operationType = CommandConstants.OperationType.ADD;
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        }
        else if(action == ActionConstants.PICKLIST_CREATE || action == ActionConstants.IMAGE_NEW) {
            operationType = CommandConstants.OperationType.REMOVE;
            if(dataRange != null) {
                for(DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),range.getStartRowIndex(),range.getStartColIndex(),range.getEndRowIndex(),range.getEndColIndex(),operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        }
        else if(action == ActionConstants.IMPORT_CLOUD_DATA || action == ActionConstants.UPDATE_CLOUD_DATA)
        {
            int rowDiff = actionJson.optInt("rowDiff"); // No I18N
            int colDiff = actionJson.optInt("colDiff"); // No I18N

            if(rowDiff != 0 || colDiff != 0) {
                operationType = CommandConstants.OperationType.REMOVE;
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        int eR = range.getEndRowIndex();
                        int eC = range.getEndColIndex();

                        if (colDiff != 0) {
                            eC = Utility.MAXNUMOFCOLS;
                        }
                        if(rowDiff != 0){
                            eR = Utility.MAXNUMOFROWS;
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), eR, eC, operationType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                operationType = CommandConstants.OperationType.ADD;
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        int eR = range.getEndRowIndex();
                        int eC = range.getEndColIndex();

                        if (colDiff != 0) {
                            eC = Utility.MAXNUMOFCOLS;
                        }
                        if(rowDiff != 0){
                            eR = Utility.MAXNUMOFROWS;
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), eR, eC, operationType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }
        }

//        if(actionJson.has(JSONConstants.TABLE) && !actionJson.getJSONArray(JSONConstants.TABLE).isEmpty())
//        {
//            operationType = OperationType.GENERATE_LIST;
//            String associatedSheetName = (actionJson.has(JSONConstants.ASSOCIATED_SHEET_NAME)) ? actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME) :  ActionJsonUtil.getAssociateSheetName(actionJson);
//            rangeWrapper = new RangeWrapper(associatedSheetName, 0,
//                    0, Utility.MAXNUMOFROWS - 1, Utility.MAXNUMOFCOLS - 1, operationType);
//            checkBoxRangeList.add(rangeWrapper);
//        }

        if(actionJson.has(JSONConstants.EXPANDED_RANGES)) {
            JSONArrayWrapper jarr = actionJson.getJSONArray(JSONConstants.EXPANDED_RANGES);
            for(int i=0; i<jarr.length(); i++) {
                JSONObjectWrapper jobj = jarr.getJSONObject(i);
                operationType = jobj.getBoolean(JSONConstants.IS_EXPANSION) ? CommandConstants.OperationType.ADD : CommandConstants.OperationType.REMOVE;
                if(operationType == CommandConstants.OperationType.ADD && isUndoAction) {
                    operationType = CommandConstants.OperationType.REMOVE;
                }
                else if(operationType == CommandConstants.OperationType.REMOVE && isUndoAction) {
                    operationType = CommandConstants.OperationType.ADD;
                }
                rangeWrapper = new RangeWrapper(jobj.getString(JSONConstants.ASSOCIATED_SHEET_NAME), jobj.getInt(JSONConstants.START_ROW), jobj.getInt(JSONConstants.START_COLUMN), jobj.getInt(JSONConstants.END_ROW), jobj.getInt(JSONConstants.END_COLUMN), operationType);
                rangeWrapperList.add(rangeWrapper);
            }
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
