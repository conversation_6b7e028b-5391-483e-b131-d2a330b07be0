package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.response.viewport.Constraints;
import com.adventnet.zoho.websheet.model.response.beans.KnitChartsModifiedBean;
import com.adventnet.zoho.websheet.model.response.extractor.KnitChartsModifiedInfoExtractor;
import com.zoho.sheet.knitcharts.pojo.ModifiedChartPOJO;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTablePool;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Charts Modification information extractor.
 * <AUTHOR>
 */

public class KnitChartsModifiedInfoExtractorImpl implements KnitChartsModifiedInfoExtractor {

    private final List<KnitChartsModifiedBean> chartsBeans;

    public KnitChartsModifiedInfoExtractorImpl(List<ModifiedChartPOJO> modifiedChartPOJOS, Constraints constraints) {
        chartsBeans = new ArrayList<>();
        if(modifiedChartPOJOS != null) { generateChartsBean(modifiedChartPOJOS, constraints); }
    }

    private void generateChartsBean(List<ModifiedChartPOJO> modifiedChartPOJOS, Constraints constraints) {
        DataTablePool dataTablePool = new DataTablePool();

        chartsBeans.addAll(modifiedChartPOJOS.stream()
                .map(modifiedChartDetail -> new KnitChartsModifiedBean(modifiedChartDetail.getModifiedChartId(),
                        modifiedChartDetail.getModificationConstants(), dataTablePool,
                        modifiedChartDetail.getAssociatedSheetName(), modifiedChartDetail.getKey()))
                        .filter(modifiedBean -> constraints.isSheetInViewPort(modifiedBean.getAssociatedSheetName()))
                .collect(Collectors.toList()));
    }

    @Override
    public List<KnitChartsModifiedBean> getModifiedChartsInfo() {
        return chartsBeans;
    }
}
