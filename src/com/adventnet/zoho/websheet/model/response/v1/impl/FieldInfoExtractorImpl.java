package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.FieldsBean;
import com.adventnet.zoho.websheet.model.response.extractor.FieldInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

public class FieldInfoExtractorImpl implements FieldInfoExtractor {

    FieldsBean fieldsBean;
    public FieldInfoExtractorImpl(JSONObjectWrapper actionJSON)
    {
        int action = (actionJSON != null && actionJSON.has(JSONConstants.ACTION)) ? actionJSON.getInt(JSONConstants.ACTION) : -1;

        switch (action) {
            // Pasting a sheet to new workbook requires all fields to be sent
            case ActionConstants.SERVERCLIP_PASTE_SHEET:
            case -1:
                fieldsBean = new FieldsBean(null, CommandConstants.OperationType.GENERATE_LIST);
                break;
            case ActionConstants.FIELD_ADD:
                fieldsBean = new FieldsBean(actionJSON, CommandConstants.OperationType.ADD);
                break;
            case ActionConstants.FIELD_EDIT:
                fieldsBean = new FieldsBean(actionJSON, CommandConstants.OperationType.EDIT);
                break;
            case ActionConstants.FIELD_DELETE:
                fieldsBean = new FieldsBean(actionJSON, CommandConstants.OperationType.DELETE);
                break;
            case ActionConstants.FIELDS_UPDATE:
                fieldsBean = new FieldsBean(actionJSON, CommandConstants.OperationType.UPDATE);
                break;
        }
    }

    @Override
    public FieldsBean getFieldsBean() {
        return fieldsBean;
    }
}
