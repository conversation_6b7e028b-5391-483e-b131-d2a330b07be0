package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.SheetWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.SheetZoomInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;

public class SheetZoomInfoExtractorImpl implements SheetZoomInfoExtractor {

    List<SheetWrapper> sheetWrapperList = new ArrayList<>();
    public SheetZoomInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        SheetWrapper sheetWrapper = null;
        int action = (actionJson != null && actionJson.has(JSONConstants.ACTION)) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        List<String> asnSheetList = ActionJsonUtil.getListOfAssociateSheetNames(actionJson, false);

        switch (action) {
            case -1:
                CommandConstants.OperationType opType = CommandConstants.OperationType.GENERATE_LIST;
                String as = actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                sheetWrapper = new SheetWrapper(as, opType);
                sheetWrapperList.add(sheetWrapper);
                break;
            case ActionConstants.SHEET_ZOOM:
                for (String asn : asnSheetList) {
                    sheetWrapper = new SheetWrapper(asn, CommandConstants.OperationType.SHEET_ZOOM);
                    sheetWrapperList.add(sheetWrapper);
                }
                break;
        }
    }

    @Override
    public List<SheetWrapper> getSheetWrapperList() {
        return sheetWrapperList;
    }
}
