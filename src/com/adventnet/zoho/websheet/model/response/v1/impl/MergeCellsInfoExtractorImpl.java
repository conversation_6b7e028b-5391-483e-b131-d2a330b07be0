package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.MergeCellsInfoExtractor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.List;

public class MergeCellsInfoExtractorImpl implements MergeCellsInfoExtractor {

    List<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public MergeCellsInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        CommandConstants.OperationType operationType;
        RangeWrapper rangeWrapper;
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        List<DataRange> sourceDataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
        //Action will be -1 for document load and switch sheet
//      boolean                 isUndoAction        =   actionJson.has(String.valueOf(ActionConstants.UNDO)) && actionJson.getBoolean(String.valueOf(ActionConstants.UNDO));
        boolean isUndoAction = actionJson.has("fromundo") && actionJson.getBoolean("fromundo");//No I18N
        if (action == -1) {

            operationType = CommandConstants.OperationType.GENERATE_LIST;
            rangeWrapper = new RangeWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME),
                    actionJson.getInt(JSONConstants.START_ROW),
                    actionJson.getInt(JSONConstants.START_COLUMN),
                    actionJson.getInt(JSONConstants.END_ROW),
                    actionJson.getInt(JSONConstants.END_COLUMN),
                    operationType);
            rangeWrapperList.add(rangeWrapper);

        } else if (action == ActionConstants.MERGE_SPLIT || action == ActionConstants.MERGE_ACROSS || action == ActionConstants.MERGE_AND_CENTER
                || action == ActionConstants.MERGE_DOWN || action == ActionConstants.MERGE_RANGE) {

            operationType = (isUndoAction ? (action == ActionConstants.MERGE_SPLIT ? CommandConstants.OperationType.MERGE : CommandConstants.OperationType.MERGE_SPLIT)
                    : (action != ActionConstants.MERGE_SPLIT ? CommandConstants.OperationType.MERGE : CommandConstants.OperationType.MERGE_SPLIT));
            if (dataRange != null) {
                for (DataRange range : dataRange) {
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),
                            range.getStartRowIndex(),
                            range.getStartColIndex(),
                            range.getEndRowIndex(),
                            range.getEndColIndex(),
                            operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }



            if (isUndoAction) {
                operationType = operationType == CommandConstants.OperationType.MERGE ? CommandConstants.OperationType.MERGE_SPLIT : CommandConstants.OperationType.MERGE;
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),
                                range.getStartRowIndex(),
                                range.getStartColIndex(),
                                range.getEndRowIndex(),
                                range.getEndColIndex(),
                                operationType);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }
        } else if (action == ActionConstants.FILLSERIES || action == ActionConstants.COPY_PASTE || action == ActionConstants.SYSTEMCLIP_PASTE || action == ActionConstants.CUT_PASTE || action == ActionConstants.CLEARALL
                || action == ActionConstants.PASTESPECIAL_FORMATS || action == ActionConstants.DELETE_CELL_BOTTOM || action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.INSERT_CELL_TOP || action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.TABLE_INSERT_COL || action == ActionConstants.TABLE_DELETE_ROW || action == ActionConstants.TABLE_DELETE_COL
                || action == ActionConstants.INSERT_ROW || action == ActionConstants.INSERT_COL || action == ActionConstants.INSERT_CUT_ROW || action == ActionConstants.INSERT_CUT_COLUMN || action == ActionConstants.INSERT_COPY_COLUMN || action == ActionConstants.INSERT_COPY_ROW || action == ActionConstants.IMPORT_CLOUD_DATA || action == ActionConstants.UPDATE_CLOUD_DATA || action == ActionConstants.SORT) {

            /*
             * For all these action, Fist we are sending 2 response for a single range, as we are not sure about merge info
             *  1. Split the range then
             *  2. Merge the range
             */
            if (!isUndoAction) {

                if (action != ActionConstants.INSERT_ROW && action != ActionConstants.INSERT_COL && action != ActionConstants.INSERT_CUT_ROW && action != ActionConstants.INSERT_CUT_COLUMN && action != ActionConstants.INSERT_COPY_COLUMN && action != ActionConstants.INSERT_COPY_ROW) {

                    if (dataRange != null) {
                        for (DataRange range : dataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),
                                    range.getStartRowIndex(),
                                    range.getStartColIndex(),
                                    range.getEndRowIndex(),
                                    range.getEndColIndex(),
                                    CommandConstants.OperationType.MERGE_SPLIT);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
                //In copy paste, For data range range, split and merge to be send in response
                //In cut paste, For source range, only split is enough to send, no need to send merge
                if (action == ActionConstants.CUT_PASTE) {
                    if (sourceDataRange != null) {
                        for (DataRange range : sourceDataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),
                                    range.getStartRowIndex(),
                                    range.getStartColIndex(),
                                    range.getEndRowIndex(),
                                    range.getEndColIndex(),
                                    CommandConstants.OperationType.MERGE_SPLIT);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }

                }

                if (action == ActionConstants.INSERT_CUT_ROW || action == ActionConstants.INSERT_CUT_COLUMN || action == ActionConstants.INSERT_COPY_COLUMN || action == ActionConstants.INSERT_COPY_ROW) {

                    if (dataRange != null) {
                        String sheetName = null;
                        int sR = (dataRange != null) ? dataRange.get(0).getStartRowIndex() : 0;
                        int sC = (dataRange != null) ? dataRange.get(0).getStartColIndex() : 0;
                        int eR = (dataRange != null) ? dataRange.get(0).getEndRowIndex() : 0;
                        int eC = (dataRange != null) ? dataRange.get(0).getEndColIndex() : 0;
                        sheetName = (dataRange != null) ? dataRange.get(0).getAssociatedSheetName() : sheetName;

                        int ssR = (sourceDataRange != null) ? sourceDataRange.get(0).getStartRowIndex() : 0;
                        int ssC = (sourceDataRange != null) ? sourceDataRange.get(0).getStartColIndex() : 0;
                        int seR = (sourceDataRange != null) ? sourceDataRange.get(0).getEndRowIndex() : Utility.MAXNUMOFROWS -1;
                        int seC = (sourceDataRange != null) ? sourceDataRange.get(0).getEndColIndex() : Utility.MAXNUMOFCOLS -1;

                        if (action == ActionConstants.INSERT_CUT_ROW ) {
                            if (sR > ssR) {
                                int count = seR - ssR + 1;
                                sR = sR - count;
                                eR = eR - count;
                            }
                        }
                        if (action == ActionConstants.INSERT_CUT_COLUMN ) {

                            if (sC > ssC) {
                                int count = seC - ssC + 1;
                                sC = sC - count;
                                eC = eC - count;
                            }
                        }

                        rangeWrapper = new RangeWrapper(sheetName,sR,sC,eR,eC, CommandConstants.OperationType.MERGE);
                        rangeWrapperList.add(rangeWrapper);
                    }
                } else if (action != ActionConstants.CLEARALL) { //For clearall action, Merge info is not required, so stopped
                    //add new
                    if (dataRange != null) {
                        for (DataRange range : dataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),
                                    range.getStartRowIndex(),
                                    range.getStartColIndex(),
                                    range.getEndRowIndex(),
                                    range.getEndColIndex(),
                                    CommandConstants.OperationType.MERGE);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }

            } else {
                // For Undo

                //Merge Split is not required for clear all in case undo
                //In copy paste, For data range range, split and merge to be send in response - undo
                if (action != ActionConstants.CLEARALL && action!=ActionConstants.INSERT_COL && action != ActionConstants.INSERT_ROW) {
                    if (dataRange != null) {
                        for (DataRange range : dataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),
                                    range.getStartRowIndex(),
                                    range.getStartColIndex(),
                                    range.getEndRowIndex(),
                                    range.getEndColIndex(),
                                    CommandConstants.OperationType.MERGE_SPLIT);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }
                //Getting Merge info for source
                if (action == ActionConstants.CUT_PASTE || action == ActionConstants.INSERT_CUT_ROW || action == ActionConstants.INSERT_CUT_COLUMN) { //insertvol/insertrow
                    if (dataRange != null) {
                        for (DataRange range : sourceDataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(),
                                    range.getStartRowIndex(),
                                    range.getStartColIndex(),
                                    range.getEndRowIndex(),
                                    range.getEndColIndex(),
                                    CommandConstants.OperationType.MERGE);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }

                }

                // For rest of the action, Merge info is not required for Undo
                if (action == ActionConstants.FILLSERIES || action == ActionConstants.CLEARALL || action == ActionConstants.INSERT_ROW || action ==ActionConstants.INSERT_COL || action == ActionConstants.COPY_PASTE || action == ActionConstants.SYSTEMCLIP_PASTE || action == ActionConstants.SORT) {
                    if (dataRange != null) {
                        for (DataRange range : dataRange) {
                            rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), CommandConstants.OperationType.MERGE);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }

                }

            }
            if (action == ActionConstants.DELETE_CELL_BOTTOM || action == ActionConstants.INSERT_CELL_TOP || action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.TABLE_INSERT_COL
                    || action == ActionConstants.TABLE_DELETE_ROW || action == ActionConstants.TABLE_DELETE_COL) {

                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        int eR = range.getEndRowIndex();
                        int eC = range.getEndColIndex();
                        if (action == ActionConstants.DELETE_CELL_BOTTOM || action == ActionConstants.INSERT_CELL_TOP || action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.TABLE_DELETE_ROW) {
                            eR = Utility.MAXNUMOFROWS;
                        } else {
                            eC = Utility.MAXNUMOFCOLS;
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), eR, eC, CommandConstants.OperationType.MERGE_SPLIT);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        int eR = range.getEndRowIndex();
                        int eC = range.getEndColIndex();
                        if (action == ActionConstants.DELETE_CELL_BOTTOM || action == ActionConstants.INSERT_CELL_TOP || action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.TABLE_DELETE_ROW) {
                            eR = Utility.MAXNUMOFROWS;
                        } else {
                            eC = Utility.MAXNUMOFCOLS;
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), eR, eC, CommandConstants.OperationType.MERGE);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }

            }

        }
        else if(action == ActionConstants.FORMAT)
        {
            JSONArrayWrapper formatArray = actionJson.getJSONArray("format_json");
            for(int i = 0; i < formatArray.length(); i++)
            {
                JSONObjectWrapper formatObj = formatArray.getJSONObject(i);
                if(formatObj.has("merge_cell"))
                {
                    String value = formatObj.getString("merge_cell");
                    operationType = value.equals("merge_split") ? CommandConstants.OperationType.MERGE_SPLIT : CommandConstants.OperationType.MERGE;
                    DataRange range = dataRange.get(i);
                    rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        }
        if(action == ActionConstants.IMPORT_CLOUD_DATA || action == ActionConstants.UPDATE_CLOUD_DATA)
        {
            int rowDiff = actionJson.optInt("rowDiff"); // No I18N
            int colDiff = actionJson.optInt("colDiff"); // No I18N

            if(rowDiff != 0 || colDiff != 0) {
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        int eR = range.getEndRowIndex();
                        int eC = range.getEndColIndex();
                        if (colDiff != 0) {
                            eC = Utility.MAXNUMOFCOLS;
                        }
                        if(rowDiff != 0){
                            eR = Utility.MAXNUMOFROWS;
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), eR, eC, CommandConstants.OperationType.MERGE_SPLIT);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
                if (dataRange != null) {
                    for (DataRange range : dataRange) {
                        int eR = range.getEndRowIndex();
                        int eC = range.getEndColIndex();
                        if (colDiff != 0) {
                            eC = Utility.MAXNUMOFCOLS;
                        }
                        if(rowDiff != 0){
                            eR = Utility.MAXNUMOFROWS;
                        }
                        rangeWrapper = new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), eR, eC, CommandConstants.OperationType.MERGE);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }
        }
        if(action == ActionConstants.TABLE_CREATE || action == ActionConstants.TABLE_TOGGLE_HEADER_FOOTER) {
            DataRange range = dataRange.get(0);
            range = new DataRange(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), Utility.MAXNUMOFROWS-1, range.getEndColIndex());
            rangeWrapperList.add(new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), CommandConstants.OperationType.MERGE_SPLIT));
            rangeWrapperList.add(new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), CommandConstants.OperationType.MERGE));
        }

        //For Table Expansion - merge cells may get shifted
        if(actionJson.has("table") && !actionJson.getJSONArray("table").isEmpty())
        {
            JSONArrayWrapper modifiedJson = actionJson.getJSONArray("table"); //No I18N
            for(int i=0; i<modifiedJson.length(); i++) {
                JSONArrayWrapper array = modifiedJson.getJSONArray(i);
                if(array.length() > 6)
                {
                    rangeWrapper = new RangeWrapper(array.getString(1), array.getInt(6), array.getInt(7), array.getInt(8), array.getInt(9), CommandConstants.OperationType.MERGE);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        }
    }

    @Override
    public List<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
