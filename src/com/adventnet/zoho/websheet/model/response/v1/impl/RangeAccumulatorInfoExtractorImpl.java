package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.RangeAccumulatorInfoExtractor;
import com.adventnet.zoho.websheet.model.response.v1.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class RangeAccumulatorInfoExtractorImpl implements RangeAccumulatorInfoExtractor {

    ArrayList<RangeWrapper> rangeWrapperList = new ArrayList<>();
    public RangeAccumulatorInfoExtractorImpl(JSONObjectWrapper actionJson, List<Range> ranges, MacroResponse macroResponse)
    {
        int action = actionJson != null && actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        List<DataRange> dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
        List<DataRange> sourceDataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, true);
        RangeWrapper rangeWrapper;
        /*
         * (macroResponse    ==  null    ||      macroResponse.isEmpty()) --> removing this check from below for replace action as it is not working
         */
        String sheetName = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString(JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
        boolean isSameSheet = actionJson.has(JSONConstants.IS_SAME_SHEET) ? actionJson.getBoolean(JSONConstants.IS_SAME_SHEET) : false;
        if (action != ActionConstants.RECALCULATE && action != ActionConstants.DOCUMENT_SETTING) {
            if (sheetName != null && !(isSameSheet && actionJson.has(JSONConstants.FROM_UNDO) && (action == ActionConstants.INSERT_CUT_COLUMN || action == ActionConstants.INSERT_CUT_ROW))) {
                if (action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.INSERT_CELL_TOP || action == ActionConstants.TABLE_INSERT_ROW
                        || action == ActionConstants.TABLE_INSERT_COL || action == ActionConstants.DELETE_CELL_BOTTOM || action == ActionConstants.TABLE_DELETE_ROW || action == ActionConstants.TABLE_DELETE_COL) {
                    if (dataRange != null) {
                        for (DataRange drange : dataRange) {
                            int eC = drange.getEndColIndex();
                            int eR = drange.getEndRowIndex();
                            int sR = drange.getStartRowIndex();
                            int sC = drange.getStartColIndex();
                            if (action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.TABLE_INSERT_COL || action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.TABLE_DELETE_COL) {
                                eC = Utility.MAXNUMOFCOLS;
                            } else if (action == ActionConstants.INSERT_CELL_TOP || action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.DELETE_CELL_BOTTOM || action == ActionConstants.TABLE_DELETE_ROW) {
                                eR = Utility.MAXNUMOFROWS;
                            }
                            if(action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.TABLE_DELETE_ROW) {
                                sR = Math.max(0, sR-1);
                            }
                            else if(action == ActionConstants.TABLE_INSERT_COL || action == ActionConstants.TABLE_DELETE_COL) {
                                sC = Math.max(0, sC-1);
                            }
                            rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), sR, sC, eR, eC);
                            rangeWrapperList.add(rangeWrapper);
                        }
                    }
                }

                int sR = (dataRange != null) ? dataRange.get(0).getStartRowIndex() : 0;
                int sC = (dataRange != null) ? dataRange.get(0).getStartColIndex() : 0;
                int eR = (dataRange != null) ? dataRange.get(0).getEndRowIndex() : 0;
                int eC = (dataRange != null) ? dataRange.get(0).getEndColIndex() : 0;
                sheetName = (dataRange != null) ? dataRange.get(0).getAssociatedSheetName() : sheetName;

                int ssR = (sourceDataRange != null) ? sourceDataRange.get(0).getStartRowIndex() : 0;
                int ssC = (sourceDataRange != null) ? sourceDataRange.get(0).getStartColIndex() : 0;
                int seR = (sourceDataRange != null) ? sourceDataRange.get(0).getEndRowIndex() : Utility.MAXNUMOFROWS;
                int seC = (sourceDataRange != null) ? sourceDataRange.get(0).getEndColIndex() : Utility.MAXNUMOFCOLS;
                boolean isMultiRangeAllowed = true;
                if (action == ActionConstants.INSERT_CUT_ROW && isSameSheet) {

                    if (sR > ssR) {
                        int count = seR - ssR + 1;
                        sR = sR - count;
                        eR = eR - count;
                    }
                    isMultiRangeAllowed = false;
                }
                if (action == ActionConstants.INSERT_CUT_COLUMN && isSameSheet) {

                    if (sC > ssC) {
                        int count = seC - ssC + 1;
                        sC = sC - count;
                        eC = eC - count;
                    }
                    isMultiRangeAllowed = false;
                }

                if (action != ActionConstants.DELETE_ROW && action != ActionConstants.DELETE_COL && action != ActionConstants.FILLSERIES) {
                    //	 if (action != ActionConstants.DELETE_ROW && action != ActionConstants.DELETE_COL && !(actionJson.has(JSONConstants.FROM_UNDO) && action == ActionConstants.EDIT_PIVOT)) {
                    if (isMultiRangeAllowed) {
                        if (dataRange != null) {
                            if (action == ActionConstants.INSERT_ROW) {
                                int prevRow = 0;
                                int prevRowCount = 0;
                                dataRange = RangeUtil.sortRowAscendingOrder(dataRange);
                                for (DataRange drange : dataRange) {
                                    int count = (drange.getEndRowIndex() - drange.getStartRowIndex()) + 1;
                                    int start = drange.getStartRowIndex();
                                    int end = drange.getEndRowIndex();
                                    if(!actionJson.has(JSONConstants.FROM_UNDO)){
                                        if (prevRow < start) {
                                            start += prevRowCount;
                                            end += prevRowCount;
                                        }
                                    }
                                    rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), start, 0, end, Utility.MAXNUMOFCOLS -1);
                                    rangeWrapperList.add(rangeWrapper);
                                    prevRow = drange.getStartRowIndex();
                                    prevRowCount += count;
                                }
                            } else if (action == ActionConstants.INSERT_COL) {
                                int prevCol = 0;
                                int prevColCount = 0;
                                dataRange = RangeUtil.sortColAscendingOrder(dataRange);
                                for (DataRange drange : dataRange) {
                                    int count = (drange.getEndColIndex() - drange.getStartColIndex()) + 1;
                                    int start = drange.getStartColIndex();
                                    int end = drange.getEndColIndex();
                                    if(!actionJson.has(JSONConstants.FROM_UNDO)){
                                        if (prevCol < start) {
                                            start += prevColCount;
                                            end += prevColCount;
                                        }
                                    }
                                    rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), 0, start,Utility.MAXNUMOFROWS -1 , end);
                                    rangeWrapperList.add(rangeWrapper);
                                    prevCol = drange.getStartColIndex();
                                    prevColCount += count;
                                }
                            } else {
                                for (DataRange drange : dataRange) {
                                    rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), drange.getStartRowIndex(), drange.getStartColIndex(), drange.getEndRowIndex(), drange.getEndColIndex());
                                    rangeWrapperList.add(rangeWrapper);
                                }
                            }
                        }
                    } else {
                        rangeWrapper = new RangeWrapper(sheetName, sR, sC, eR, eC);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }

            }

        }
        if(action == ActionConstants.GET_DATA_DUPLICATES || action == ActionConstants.DC_APPLY_FORMATS){
            String operationType = actionJson.getString("operationType");
            if((operationType.contains(JSONConstants.DD_COPY) || operationType.contains(JSONConstants.DD_MOVE)) && sourceDataRange != null){
                DataRange range1 = sourceDataRange.get(0);
                if(range1 != null){
                    rangeWrapper = new RangeWrapper(range1.getAssociatedSheetName(), range1.getStartRowIndex(), range1.getStartColIndex(), range1.getEndRowIndex(), range1.getEndColIndex());
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        }
        if (action == ActionConstants.CUT_PASTE || action == ActionConstants.INSERT_CUT_COLUMN
                || action == ActionConstants.INSERT_CUT_ROW || action == ActionConstants.MOVE_PIVOT) {
            if (sheetName != null && !(isSameSheet && !actionJson.has(JSONConstants.FROM_UNDO) && (action == ActionConstants.INSERT_CUT_COLUMN || action == ActionConstants.INSERT_CUT_ROW))) {
                int ssR = (sourceDataRange != null) ? sourceDataRange.get(0).getStartRowIndex() : 0;
                int ssC = (sourceDataRange != null) ? sourceDataRange.get(0).getStartColIndex() : 0;
                int seR = (sourceDataRange != null) ? sourceDataRange.get(0).getEndRowIndex() : Utility.MAXNUMOFROWS;
                int seC = (sourceDataRange != null) ? sourceDataRange.get(0).getEndColIndex() : Utility.MAXNUMOFCOLS;
                String sourceSheetName = (sourceDataRange != null) ? sourceDataRange.get(0).getAssociatedSheetName() : sheetName;

                int sR = (dataRange != null) ? dataRange.get(0).getStartRowIndex() : 0;
                int sC = (dataRange != null) ? dataRange.get(0).getStartColIndex() : 0;
                int eR = (dataRange != null) ? dataRange.get(0).getEndRowIndex() : 0;
                int eC = (dataRange != null) ? dataRange.get(0).getEndColIndex() : 0;

                if (action == ActionConstants.INSERT_CUT_ROW && isSameSheet) {
                    if (ssR > sR) {
                        int count = eR - sR + 1;
                        ssR = ssR - count;
                        seR = seR - count;
                    }
                }
                if (action == ActionConstants.INSERT_CUT_COLUMN && isSameSheet) {
                    if (ssC > sC) {
                        int count = eC - sC + 1;
                        ssC = ssC - count;
                        seC = seC - count;
                    }
                }
                if (action == ActionConstants.MOVE_PIVOT) {
                    ssR = ssR == 0  ?  ssR : ssR -1;
                    ssC			=	ssC == 0  ?  ssC : ssC -1;
                    seR			=	seR != 0 && seR != Utility.MAXNUMOFROWS ?  seR +1 : seR;
                    seC			=	seC != 0 && seR != Utility.MAXNUMOFCOLS ?  seC +1 : seC;


                }
                if (action != ActionConstants.INSERT_COPY_ROW && action != ActionConstants.INSERT_COPY_COLUMN) {
                    rangeWrapperList.add(new RangeWrapper(sourceSheetName, ssR, ssC, seR, seC));
                }
            }
        }
        if (dataRange!=null && sheetName != null && !(isSameSheet && !actionJson.has(JSONConstants.FROM_UNDO))){
            DataRange newTarget = null;
            switch (action) {
                case		ActionConstants.EDIT_PIVOT:
                case		ActionConstants.CHANGE_PIVOT_THEME:
                case		ActionConstants.CHANGE_PIVOT_SOURCE:
                case ActionConstants.CHANGE_PIVOT_SUBTOTALOPTION:
                case ActionConstants.CHANGE_PIVOT_GRANDTOTALROWOPTION:
                case ActionConstants.CHANGE_PIVOT_GRANDTOTALCOLOPTION:
                case ActionConstants.REFRESH_PIVOT:
                case ActionConstants.DELETE_PIVOT:
                case ActionConstants.APPLYFILTER_PIVOT:
                case ActionConstants.SORT_PIVOT:
                case ActionConstants.GROUPING_PIVOT_FIELDS:
                case ActionConstants.APPLY_SLICER_FILTER:
                case ActionConstants.UPDATE_CONNECTED_PIVOTS:
                    rangeWrapperList = new ArrayList<RangeWrapper>();
                    //int action = actionJson.getInt(JSONConstants.ACTION);
                    //	 if(action != ActionConstants.DELETE_PIVOT){
                    JSONObjectWrapper oldTarget = null;
                    if(actionJson.has("oldTarget")) {
                        oldTarget = actionJson.getJSONObject("oldTarget");
                    }else {
                        oldTarget = ActionJsonUtil.getFirstRangeFromJsonArray(actionJson.optJSONArray(JSONConstants.RANGELIST));
                    }
                    //To send cell Response to multiple pivots
                    if(actionJson.has("slid") && actionJson.has("oldTargets")){
                        for(int i = 0; i < dataRange.size(); i++){
                            JSONObjectWrapper oldTarget1 = (JSONObjectWrapper) ((JSONArrayWrapper)actionJson.get("oldTargets")).get(i);
                            updatePivotAffRanges(oldTarget1,dataRange.get(i));
                        }
                    }
                    else {
                    newTarget = dataRange.get(0);

                    updatePivotAffRanges(oldTarget,newTarget);
                    }

                    break;
            }

        }
        if(ranges != null && action != ActionConstants.DELETE_COL && action != ActionConstants.DELETE_ROW) {
            for(Range range : ranges) {
                if(range != null) {
//                    if(!range.isEmptyRange() || action == ActionConstants.SHEET_REMOVE || action == ActionConstants.IMPORT_CLOUD_DATA || action == ActionConstants.TABLE_RESIZE || action == ActionConstants.FILLSERIES) {
                        rangeWrapper = new RangeWrapper(range.getSheet().getAssociatedName(), range.getStartRowIndex(),
                                range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), CommandConstants.OperationType.MODIFY);
                        rangeWrapperList.add(rangeWrapper);
//                    }

                }
            }

            if(actionJson.has(JSONConstants.TABLE) && !actionJson.getJSONArray(JSONConstants.TABLE).isEmpty())
            {
                JSONArrayWrapper modifiedJson = actionJson.getJSONArray(JSONConstants.TABLE);
                for(int i=0; i<modifiedJson.length(); i++) {
                    JSONArrayWrapper array = modifiedJson.getJSONArray(i);
                    if(array.length() > 6)
                    {
                        //This range added to table by shifting cells. * NEED TO ADD CELL RESPONSE FOR SHIFTED RANGE *
                        rangeWrapper = new RangeWrapper(array.getString(1), array.getInt(6),array.getInt(7),array.getInt(8),array.getInt(9));
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }
            if(actionJson.has(JSONConstants.TABLE_AFFECTED_RANGES) && (actionJson.optBoolean(JSONConstants.FROM_UNDO) || actionJson.optBoolean(JSONConstants.FROM_REDO))) {
                JSONArrayWrapper modifiedJson = actionJson.getJSONArray(JSONConstants.TABLE_AFFECTED_RANGES);
                if(!modifiedJson.isEmpty()) {
                    for(int i=0; i<modifiedJson.length(); i++) {
                        JSONArrayWrapper array = modifiedJson.getJSONArray(i);
                        rangeWrapperList.add(new RangeWrapper(array.getString(0), array.getInt(1), array.getInt(2), array.getInt(3), array.getInt(4)));
                    }
                }
            }

            if((action == ActionConstants.TABLE_STYLE_CHANGE || action == ActionConstants.TABLE_REMOVE_CUSTOM_STYLE) && actionJson.has(JSONConstants.TABLE)) {
                JSONArrayWrapper modifiedJson = actionJson.getJSONArray(JSONConstants.TABLE);
                if(!modifiedJson.isEmpty()) {
                    for(int i=0; i<modifiedJson.length(); i++) {
                        JSONArrayWrapper array = modifiedJson.getJSONArray(i);
                        rangeWrapperList.add(new RangeWrapper(array.getString(1), array.getInt(2), array.getInt(3), array.getInt(4), array.getInt(5)));
                    }
                }
            }

            if(action == ActionConstants.TABLE_RESIZE && actionJson.has(JSONConstants.TABLE) && (actionJson.has(JSONConstants.FROM_UNDO) || actionJson.has(JSONConstants.FROM_REDO))) {
                JSONArrayWrapper modifiedJson = actionJson.getJSONArray(JSONConstants.TABLE);
                for(int i=0; i<modifiedJson.length(); i++) {
                    JSONArrayWrapper array = modifiedJson.getJSONArray(i);
                    rangeWrapperList.add(new RangeWrapper(array.getString(1), array.getInt(2), array.getInt(3), array.getInt(4), array.getInt(5)));
                }
            }
            if (action == ActionConstants.EDIT_PIVOT && !ranges.isEmpty()) {
                if (dataRange != null) {
                    for (DataRange drange : dataRange) {
                        int sR = drange.getStartRowIndex();
                        int sC = drange.getStartColIndex();
                        int eR = drange.getEndRowIndex();
                        int eC = drange.getEndColIndex();

                        eR = eR > ranges.get(0).getEndRowIndex() ? eR : ranges.get(0).getEndRowIndex();
                        eC = eC > ranges.get(0).getEndColIndex() ? eC : ranges.get(0).getEndColIndex();
                        rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), sR, sC, eR, eC);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }
        }
        if(action == ActionConstants.PICKLIST_EDIT || action == ActionConstants.PICKLIST_RANGE_EDIT && actionJson.has(JSONConstants.PICKLIST)) {
            JSONArrayWrapper picklistOldRanges = actionJson.getJSONArray(JSONConstants.PICKLIST);
            for(int i=0; i< picklistOldRanges.length(); i++) {
                JSONArrayWrapper rangeArr = picklistOldRanges.getJSONArray(i);
                rangeWrapperList.add(new RangeWrapper(rangeArr.getString(0), rangeArr.getInt(1), rangeArr.getInt(2), rangeArr.getInt(3), rangeArr.getInt(4)));
            }
        }
        //Handling Macro response separately and handling cell comments too.
        if (macroResponse != null && !macroResponse.isEmpty()) {
            ArrayList<JSONObjectWrapper> rangeList = macroResponse.getRangeList();
            int size = rangeList.size();
            for (int i = 0; i < size; i++) {
                JSONObjectWrapper rangeObj = rangeList.get(i);
                JSONArrayWrapper sheetListJson = rangeObj.optJSONArray(JSONConstants.SHEETLIST);
                JSONArrayWrapper rangeListJson = rangeObj.optJSONArray(JSONConstants.RANGELIST);

                if(sheetListJson != null && rangeListJson != null && !sheetListJson.isEmpty() && !rangeListJson.isEmpty()) {
                    JSONArrayWrapper sheetArray = sheetListJson.optJSONArray(0);
                    JSONArrayWrapper rangeArray = rangeListJson.optJSONArray(0);
                    if(sheetArray != null && rangeArray != null && !sheetArray.isEmpty() && !rangeArray.isEmpty()) {
                        JSONObjectWrapper rangeObject = rangeArray.getJSONObject(0);
                        String asn = sheetArray.optString(0);
                        int startRow = rangeObject.optInt(JSONConstants.START_ROW);
                        int startCol = rangeObject.optInt(JSONConstants.START_COLUMN);
                        int endRow = rangeObject.optInt(JSONConstants.END_ROW);
                        int endCol = rangeObject.optInt(JSONConstants.END_COLUMN);
                        // Avoid this current sheet check to be able to add the response of cut, copy and paste to the sheets they are not visible. If sheet is not in view port then the response will go to faulty ranges
                        //if(sheetName.equals(asn)) {
                        rangeWrapperList.add(new RangeWrapper(asn, startRow, startCol, endRow, endCol));
                        //}
                    }
                }
            }

            // cell comment is part of cell Response. So only put it here. (need optimization)
            ArrayList<JSONObjectWrapper> commentList = macroResponse.getCommentsList();
            size = commentList.size();

            for (int i = 0; i < size; i++) {
                JSONObjectWrapper jobj = commentList.get(i);
                action = jobj.getInt(JSONConstants.ACTION);

                if (action == ActionConstants.CLEAR_ANNOTATION) {
                    rangeWrapper = new RangeWrapper(jobj.getString(JSONConstants.ASSOCIATED_SHEET_NAME), jobj.getInt(JSONConstants.START_ROW),
                            jobj.getInt(JSONConstants.START_COLUMN), jobj.getInt(JSONConstants.END_ROW), jobj.getInt(JSONConstants.END_COLUMN), CommandConstants.OperationType.MODIFY);
                    rangeWrapperList.add(rangeWrapper);
                } else {
                    rangeWrapper = new RangeWrapper(jobj.getString(JSONConstants.ASSOCIATED_SHEET_NAME), jobj.getInt(JSONConstants.START_ROW),
                            jobj.getInt(JSONConstants.START_COLUMN), jobj.getInt(JSONConstants.START_ROW), jobj.getInt(JSONConstants.START_COLUMN), CommandConstants.OperationType.MODIFY);
                    rangeWrapperList.add(rangeWrapper);
                }
            }
        }

        if (action == ActionConstants.COMPUTE_SUM && actionJson.has(JSONConstants.FROM_UNDO)) {
            JSONArrayWrapper formulasAndCells = actionJson.getJSONArray(JSONConstants.FORMULA_AND_CELLS);
            JSONObjectWrapper jObj = null;
            if (dataRange != null) {
                for (DataRange drange : dataRange) {
                    for (int i = 0; i < formulasAndCells.length(); i++) {
                        jObj = formulasAndCells.getJSONObject(i);
                        int row = jObj.getInt("r"); //No I18N
                        int col = jObj.getInt("c"); //No I18N
                        rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), row, col, row, col);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }

        }

        if(actionJson.has(JSONConstants.ADDITIONAL_INFO) && actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).has(JSONConstants.PIVOT_AFFECTED_RANGE))
        {
            JSONArrayWrapper jsonArr = actionJson.getJSONObject(JSONConstants.ADDITIONAL_INFO).getJSONArray(JSONConstants.PIVOT_AFFECTED_RANGE);
            JSONObjectWrapper jObj = null;
            DataRange newTarget = null;
            for (int i = 0; i < jsonArr.length(); i++) {
                jObj = jsonArr.getJSONObject(i);
                dataRange = null;
                JSONObjectWrapper newTargetJson=jObj.getJSONObject("newTarget");
                newTarget=new DataRange(newTargetJson.getString("asn"),newTargetJson.getInt(JSONConstants.START_ROW),newTargetJson.getInt(JSONConstants.START_COLUMN),newTargetJson.getInt(JSONConstants.END_ROW), newTargetJson.getInt(JSONConstants.END_COLUMN));
                if(jObj.has("oldTarget"))
                {
                    updatePivotAffRanges(jObj.getJSONObject("oldTarget"),newTarget);
                }else
                {
                    RangeWrapper newRangeWrapper = new RangeWrapper(newTarget.getAssociatedSheetName(), newTarget.getStartRowIndex(),newTarget.getStartColIndex(),newTarget.getEndRowIndex(), newTarget.getEndColIndex());
                    rangeWrapperList.add(newRangeWrapper);
                }

            }
//            actionJson.remove(JSONConstants.PIVOT_AFFECTED_RANGE);
        }

        if((action == ActionConstants.TABLE_CREATE || action == ActionConstants.TABLE_TOGGLE_HEADER_FOOTER) && (actionJson.has(JSONConstants.FROM_UNDO) || actionJson.has(JSONConstants.FROM_REDO))) {
            DataRange range = dataRange.get(0);
            if(actionJson.optBoolean(JSONConstants.TABLE_INSERT_CELLS, false)) {
                rangeWrapperList.add(new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(),Utility.MAXNUMOFROWS-1, range.getEndColIndex()));
            }
            else {
                rangeWrapperList.add(new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(),range.getEndRowIndex()+1, range.getEndColIndex()));
            }

        }

        if(actionJson.has(JSONConstants.EXPANDED_RANGES) && (actionJson.has(JSONConstants.FROM_UNDO) || actionJson.has(JSONConstants.FROM_REDO))) {
            JSONArrayWrapper rangesArray = actionJson.getJSONArray(JSONConstants.EXPANDED_RANGES);
            for(int i=0; i<rangesArray.length(); i++) {
                JSONObjectWrapper range =  rangesArray.getJSONObject(i);
                rangeWrapperList.add(new RangeWrapper(range.getString(JSONConstants.ASSOCIATED_SHEET_NAME), range.getInt(JSONConstants.START_ROW), range.getInt(JSONConstants.START_COLUMN), range.getInt(JSONConstants.END_ROW), range.getInt(JSONConstants.END_COLUMN)));
            }
        }

        if(actionJson.has(JSONConstants.TABLE_HEADERS))  {
            JSONArrayWrapper arry = actionJson.getJSONArray(JSONConstants.TABLE_HEADERS);
            for(int i=0; i< arry.length(); i++) {
                JSONArrayWrapper range = arry.getJSONArray(i);
                rangeWrapperList.add(new RangeWrapper(range.getString(1), range.getInt(2), range.getInt(3), range.getInt(4), range.getInt(5)));
            }
        }

        if(action == ActionConstants.IMPORT_CLOUD_DATA || action == ActionConstants.UPDATE_CLOUD_DATA)
        {
            int rowDiff = actionJson.optInt("rowDiff"); // No I18N
            int colDiff = actionJson.optInt("colDiff"); // No I18N

            if(rowDiff != 0 || colDiff != 0) {
                dataRange = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
                if (dataRange != null) {
                    for (DataRange drange : dataRange) {
                        int eC = drange.getEndColIndex();
                        int eR = drange.getEndRowIndex();
                        int sR = drange.getStartRowIndex();
                        int sC = drange.getStartColIndex();
                        if (colDiff != 0) {
                            eC = Utility.MAXNUMOFCOLS;
                        }
                        if(rowDiff != 0){
                            eR = Utility.MAXNUMOFROWS;
                        }
                        rangeWrapper = new RangeWrapper(drange.getAssociatedSheetName(), sR, sC, eR, eC);
                        rangeWrapperList.add(rangeWrapper);
                    }
                }
            }
        }


        updateConditionalFormatRanges(actionJson, dataRange, action);
        updateDVRanges(actionJson, dataRange, action);
        updateBorderRanges(actionJson, dataRange, action);
    }

    private void updatePivotAffRanges(JSONObjectWrapper oldTarget , DataRange newTarget)
    {
        RangeWrapper  rangeWrapper = null;
        // DataRange testDataRange = new DataRange(sheet.getAssociatedName(), startRow, startCol, endRow, endCol);
        int newStartRowIndex = Math.max(oldTarget.getInt(JSONConstants.START_ROW), newTarget.getStartRowIndex());
        int newStartColIndex = Math.max(oldTarget.getInt(JSONConstants.START_COLUMN), newTarget.getStartColIndex());
        int newEndRowIndex = Math.min(oldTarget.getInt(JSONConstants.END_ROW), newTarget.getEndRowIndex());
        int newEndColIndex = Math.min(oldTarget.getInt(JSONConstants.END_COLUMN), newTarget.getEndColIndex());

        if(newEndRowIndex >= newStartRowIndex && newEndColIndex >= newStartColIndex){
            //There is some intersession
            if((oldTarget.getInt(JSONConstants.END_ROW) >= newTarget.getEndRowIndex() &&  oldTarget.getInt(JSONConstants.END_COLUMN) >= newTarget.getEndColIndex())){
                newTarget = new DataRange(newTarget.getAssociatedSheetName(), oldTarget.getInt(JSONConstants.START_ROW),oldTarget.getInt(JSONConstants.START_COLUMN),oldTarget.getInt(JSONConstants.END_ROW), oldTarget.getInt(JSONConstants.END_COLUMN));
            } else if(oldTarget.getInt(JSONConstants.END_ROW) > newTarget.getEndRowIndex()+1) {
                int sC = oldTarget.getInt(JSONConstants.START_COLUMN);
                sC			=	sC == 0  ?  sC : sC -1;
                int eR = oldTarget.getInt(JSONConstants.END_ROW);
                eR			=	eR != 0 && eR != Utility.MAXNUMOFROWS ?  eR+1  : eR;
                int eC = oldTarget.getInt(JSONConstants.END_COLUMN);
                eC			=	eC != 0 && eR != Utility.MAXNUMOFCOLS ?  eC+1  : eC;
                //toBeEmptyRange = new Range(newTarget.getSheet(),newTarget.getEndRowIndex()+2,sC,eR, eC);
                rangeWrapper = new RangeWrapper(newTarget.getAssociatedSheetName(), newTarget.getEndRowIndex()+2,sC,eR, eC);
                rangeWrapperList.add(rangeWrapper);
            } else if(oldTarget.getInt(JSONConstants.END_COLUMN) > newTarget.getEndColIndex()+1) {
                int sR = oldTarget.getInt(JSONConstants.START_ROW);
                sR = sR == 0  ?  sR : sR -1;
                int eR = oldTarget.getInt(JSONConstants.END_ROW);
                eR			=	eR != 0 && eR != Utility.MAXNUMOFROWS ?  eR +1 : eR;
                int eC = oldTarget.getInt(JSONConstants.END_COLUMN);
                eC			=	eC != 0 && eR != Utility.MAXNUMOFCOLS ?  eC +1 : eC;
                // toBeEmptyRange = new Range(newTarget.getSheet(),sR,newTarget.getEndColIndex()+2,eR, eC);
                rangeWrapper = new RangeWrapper(newTarget.getAssociatedSheetName(), sR,newTarget.getEndColIndex()+2,eR, eC);
                rangeWrapperList.add(rangeWrapper);
            }
        }else {
            //No intersession, Need to be add both range.
            //toBeEmptyRange = new Range(newTarget.getSheet(),oldTarget.getInt(JSONConstants.START_ROW),oldTarget.getInt(JSONConstants.START_COLUMN),oldTarget.getInt(JSONConstants.END_ROW), oldTarget.getInt(JSONConstants.END_COLUMN));
            rangeWrapper = new RangeWrapper(newTarget.getAssociatedSheetName(), oldTarget.getInt(JSONConstants.START_ROW),oldTarget.getInt(JSONConstants.START_COLUMN),oldTarget.getInt(JSONConstants.END_ROW), oldTarget.getInt(JSONConstants.END_COLUMN));
            rangeWrapperList.add(rangeWrapper);
        }


        int sR = newTarget.getStartRowIndex();
        int sC = newTarget.getStartColIndex();
        int eR = newTarget.getEndRowIndex();
        int eC = newTarget.getEndColIndex();
        eR			=	eR != 0 && eR != Utility.MAXNUMOFROWS ?  eR +1 : eR;
        eC			=	eC != 0 && eR != Utility.MAXNUMOFCOLS ?  eC +1 : eC;
        sR			=	sR == 0  ?  sR : sR -1;
        sC			=	sC == 0  ?  sC : sC -1;

        rangeWrapper = new RangeWrapper(newTarget.getAssociatedSheetName(), sR,sC,eR, eC);

        rangeWrapperList.add(rangeWrapper);

    }

    private void updateBorderRanges(JSONObjectWrapper actionJson, List<DataRange> dataRange, int action) {
        if(dataRange == null || action != ActionConstants.BORDERS){
            return;
        }
        JSONObjectWrapper borderJSON = actionJson.getJSONObject(JSONConstants.BORDER);
        Iterator<String> keys = borderJSON.keys();
        while(keys.hasNext()) {
            String key = keys.next();
            action = Integer.parseInt(key);
            {
                for (DataRange sRange : dataRange) {
                    int sr = sRange.getStartRowIndex();
                    int sc = sRange.getStartColIndex();
                    int er = sRange.getEndRowIndex();
                    int ec = sRange.getEndColIndex();
                    switch (action) {
                        case ActionConstants.BORDER_ALL:
                        case ActionConstants.BORDER_NONE:
                            sr = sr == 0 ? sr : sr - 1;
                            sc = sc == 0 ? sc : sc - 1;
                            er = er == Utility.MAXNUMOFROWS - 1 ? er : er + 1;
                            ec = ec == Utility.MAXNUMOFCOLS - 1 ? ec : ec + 1;
                            RangeWrapper rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), sr, sc, sr, ec);
                            rangeWrapperList.add(rangeWrapper);

                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), er, sc, er, ec);
                            rangeWrapperList.add(rangeWrapper);

                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), sr, sc, er, sc);
                            rangeWrapperList.add(rangeWrapper);

                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), sr, ec, er, ec);
                            rangeWrapperList.add(rangeWrapper);
                            break;
                        case ActionConstants.BORDER_LEFT:
                            sc = sc == 0 ? sc : sc - 1;
                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), sr, sc, er, sc);
                            rangeWrapperList.add(rangeWrapper);
                            break;
                        case ActionConstants.BORDER_RIGHT:
                            ec = ec == Utility.MAXNUMOFCOLS - 1 ? ec : ec + 1;
                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), sr, ec, er, ec);
                            rangeWrapperList.add(rangeWrapper);
                            break;
                        case ActionConstants.BORDER_TOP:
                            sr = sr == 0 ? sr : sr - 1;
                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), sr, sc, sr, ec);
                            rangeWrapperList.add(rangeWrapper);
                            break;
                        case ActionConstants.BORDER_BOTTOM:
                            er = er == Utility.MAXNUMOFROWS - 1 ? er : er + 1;
                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), er, sc, er, ec);
                            rangeWrapperList.add(rangeWrapper);
                            break;
                        case ActionConstants.BORDER_VERTICAL:
                            sc = sc == 0 ? sc : sc - 1;
                            ec = ec == Utility.MAXNUMOFCOLS - 1 ? ec : ec + 1;
                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), sr, sc, er, sc);
                            rangeWrapperList.add(rangeWrapper);
                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), sr, ec, er, ec);
                            rangeWrapperList.add(rangeWrapper);
                            break;
                        case ActionConstants.BORDER_HORIZONTAL:
                            sr = sr == 0 ? sr : sr - 1;
                            er = er == Utility.MAXNUMOFROWS - 1 ? er : er + 1;
                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), sr, sc, sr, ec);
                            rangeWrapperList.add(rangeWrapper);
                            rangeWrapper = new RangeWrapper(sRange.getAssociatedSheetName(), er, sc, er, ec);
                            rangeWrapperList.add(rangeWrapper);
                            break;
                    }
                }
            }
        }
    }

    private void updateConditionalFormatRanges(JSONObjectWrapper actionJson, List<DataRange> dataRange, int action) {

        if (action == ActionConstants.DATA_BAR_APPLY
                || action == ActionConstants.ICON_SET_APPLY || action == ActionConstants.COLOR_SCALES_APPLY
                || action == ActionConstants.CONDITIONAL_FORMAT_APPLY || action == ActionConstants.CONDITIONAL_FORMAT_EDIT
                || action == ActionConstants.CONDITIONAL_STYLE_REORDER || action == ActionConstants.CONDITIONAL_FORMAT_DELETE) {
            // Only edit and delete case will hold clearranges json.
            if (actionJson.has(JSONConstants.CSTYLEJSON)) {
                JSONObjectWrapper cStyleJSON = actionJson.getJSONObject(JSONConstants.CSTYLEJSON);
                String refSheetID = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? actionJson.getString(JSONConstants.CURRENT_ACTIVE_SHEET) : ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                if (cStyleJSON.has(JSONConstants.CLEARRANGES)) {
                    List<RangeWrapper> clearCFRanges = ResponseUtils.convertConditionalStrtoRangeWrappers(refSheetID, cStyleJSON.getString(JSONConstants.CLEARRANGES));
                    rangeWrapperList.addAll(clearCFRanges);
                }

                if (dataRange != null) {
                    for (DataRange sRange : dataRange) {
                        String range = CellUtil.getCellReference(sRange.getStartColIndex(), sRange.getStartRowIndex()) + ":" + CellUtil.getCellReference(sRange.getEndColIndex(), sRange.getEndRowIndex());
                        List<RangeWrapper> dataCFRanges = ResponseUtils.convertConditionalStrtoRangeWrappers(sRange.getAssociatedSheetName(), range);
                        rangeWrapperList.addAll(dataCFRanges);
                    }
                }

            }

        }

    }

    private void updateDVRanges(JSONObjectWrapper actionJson, List<DataRange> dataRange, int action) {
        if (action == ActionConstants.DATA_VALIDATION_APPLY || action == ActionConstants.DATA_VALIDATION_CLEAR) {
            for (DataRange sRange : dataRange) {
                String range = CellUtil.getCellReference(sRange.getStartColIndex(), sRange.getStartRowIndex()) + ":" + CellUtil.getCellReference(sRange.getEndColIndex(), sRange.getEndRowIndex());
                List<RangeWrapper> dvRange = ResponseUtils.convertConditionalStrtoRangeWrappers(sRange.getAssociatedSheetName(), range);
                rangeWrapperList.addAll(dvRange);
            }

        }
    }

    @Override
    public ArrayList<RangeWrapper> getRangeWrapperList() {
        return rangeWrapperList;
    }
}
