package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.FetchDataInfoExtractor;
import com.adventnet.zoho.websheet.model.util.JSONConstants;


public class FetchDataInfoExtractorImpl implements FetchDataInfoExtractor {

    String sheetName;
    JSONArrayWrapper ranges;
    boolean	includeMergeRootCellData;
    public FetchDataInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        this.sheetName = actionJson.getString(JSONConstants.SHEET_NAME);
        this.ranges = actionJson.getJSONArray("ranges");
    }


    @Override
    public String getSheetName() {
        return sheetName;
    }

    @Override
    public JSONArrayWrapper getRanges() {
        return ranges;
    }
}
