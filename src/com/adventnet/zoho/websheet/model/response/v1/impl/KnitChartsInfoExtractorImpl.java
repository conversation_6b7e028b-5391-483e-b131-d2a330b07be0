package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.response.beans.KnitChartsBean;
import com.adventnet.zoho.websheet.model.response.extractor.KnitChartsInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.knitcharts.actionhandlers.oldchartactionhandler.OldChartActionConstants;
import com.zoho.sheet.knitcharts.chartsdatatable.DataTablePool;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.util.ArrayList;
import java.util.List;


/**
 * Charts Information Extractor. Every chart edit actions information are extracted from here.
 * <AUTHOR>
 */
public class KnitChartsInfoExtractorImpl implements KnitChartsInfoExtractor {

    private final List<KnitChartsBean> knitChartsBeans;

    public KnitChartsInfoExtractorImpl(JSONObjectWrapper actionJSON) {
        knitChartsBeans = new ArrayList<>();
        generateChartBeans(actionJSON);
    }

    private boolean isChartAction(int action) {
        switch (action) {
            case ActionConstants.CHART_NEW:
            case ActionConstants.CHART_RESIZE:
            case ActionConstants.CHART_MOVE:
            case ActionConstants.CHART_DELETE:
            case ActionConstants.CHART_EDIT:
            case ActionConstants.CHART_QUICK_STYLE:
            case ActionConstants.CHART_PASTE_STYLE:
            case ActionConstants.CHART_QUICK_EDIT:
            case ActionConstants.CHART_MOVE_TO_NEW_SHEET:
            case ActionConstants.CHART_PUBLISH:
            case ActionConstants.CHART_UNPUBLISH: {
               return true;
            }
        }

        return false;
    }

    private void generateChartBeans(JSONObjectWrapper actionJSON) {
        if(actionJSON.has(JSONConstants.ACTION)) {
            int action = actionJSON.getInt(JSONConstants.ACTION);
            if(isChartAction(action)) {
                DataTablePool dataTablePool = new DataTablePool();
                String associatedSheetID = actionJSON.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
                String chartID = actionJSON.getString(OldChartActionConstants.JSONConstant.CHART_ID);

                KnitChartsBean bean = new KnitChartsBean(action, associatedSheetID, dataTablePool)
                        .setChartID(chartID);
                knitChartsBeans.add(bean);
            }
        }
    }

    @Override
    public List<KnitChartsBean> getChartsInfo() {
        return knitChartsBeans;
    }
}
