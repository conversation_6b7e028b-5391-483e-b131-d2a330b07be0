package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.DataConnectionBean;
import com.adventnet.zoho.websheet.model.response.extractor.DataConnectionInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.action.externaldata.constants.DCLiveStatusCode;

import java.util.Iterator;

public class DataConnectionInfoExtractorImpl implements DataConnectionInfoExtractor {

    DataConnectionBean dataConnectionBean;
    public DataConnectionInfoExtractorImpl(JSONObjectWrapper actionJson, DataConnectionBean dataConnectionBean)
    {
        int		action		=		actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        CommandConstants.OperationType operationType	= 	null;
        JSONObjectWrapper webdataInfoObj = new JSONObjectWrapper();
        if(dataConnectionBean == null)
        {
            dataConnectionBean = new DataConnectionBean();
        }
        //String webDataID = null;
        //String			pivotId			=	null;
        switch (action) {

            case -1:
                operationType = CommandConstants.OperationType.GENERATE_LIST;
                break;
            //case ActionConstants.WEB_DATA:
            case ActionConstants.IMPORT_CLOUD_DATA:
                //case ActionConstants.UPDATE_WEB_DATA:
            case ActionConstants.UPDATE_CLOUD_DATA:
                //editdelete
                String webDataId = actionJson.optString(JSONConstants.WEBDATA_ID,"-1");
                String actionType = actionJson.getString(JSONConstants.SUB_ACTION);
                if(actionType.equals("add")){
                    operationType = CommandConstants.OperationType.ADD;
                }else if(actionType.equals("edit")){
                    operationType = CommandConstants.OperationType.MODIFY;
                }else if(actionType.equals("scheduleRefresh")){
                    operationType = CommandConstants.OperationType.FAULTY;
                }else if(actionType.equals("refresh")){
                    operationType = CommandConstants.OperationType.UPDATE;
                }else if(actionType.equals("delete")){
                    operationType = CommandConstants.OperationType.REMOVE;
                }else if(actionType.equals("enable") || actionType.equals("disable") || actionType.equals("ignore") || actionType.equals("reActivate")){
                    operationType = actionType.equals("reActivate") ? CommandConstants.OperationType.TURN_ON_LINK : CommandConstants.OperationType.EDIT;
                    if(actionType.equals("ignore"))
                    {
                        dataConnectionBean.addOrUpdateWebDataInfo(webDataId, "errorCode", actionJson.optString("errorCode", "0")); //NO I18N
                    }
                    else if(actionType.equals("reActivate"))
                    {
                        dataConnectionBean.addOrUpdateWebDataInfo(webDataId, "liveStatusCode", actionJson.optString("liveStatusCode", String.valueOf(DCLiveStatusCode.ACTIVE))); //NO I18N
                    }
                    else {
                        dataConnectionBean.addOrUpdateWebDataInfo(webDataId, "isDisabled", String.valueOf(actionType.equals("disable"))); //NO I18N
                    }
                }
                dataConnectionBean.setWebDataId(webDataId);
                //constructWebDataObj(webdataInfoObj, actionJson);
                //dataConnectionBean.setWebdataInfoObj(webdataInfoObj);
                break;
            case ActionConstants.SHEET_DUPLICATE:
            case ActionConstants.SHEET_COPY:
            case ActionConstants.SERVERCLIP_PASTE_SHEET:
                operationType = CommandConstants.OperationType.DUPLICATES;
                if(actionJson.has(JSONConstants.WEBDATA_IDS)) {
                    JSONObjectWrapper wedataIdMap = actionJson.getJSONObject(JSONConstants.WEBDATA_IDS);
                    Iterator<String> keys = wedataIdMap.keys();
                    while (keys.hasNext()) {
                        dataConnectionBean.addOrAffectedWebDataId(keys.next());
                    }
                }
                break;
            case ActionConstants.INSERT_ROW:
            case ActionConstants.INSERT_COL:
            case ActionConstants.INSERT_CUT_ROW:
            case ActionConstants.INSERT_CUT_COLUMN:
            case ActionConstants.INSERT_CELL_LEFT:
            case ActionConstants.INSERT_CELL_TOP:
            case ActionConstants.DELETE_ROW:
            case ActionConstants.DELETE_COL:
            case ActionConstants.DELETE_CELL_BOTTOM:
            case ActionConstants.DELETE_CELL_RIGHT:
            case ActionConstants.CUT_PASTE:
                operationType = CommandConstants.OperationType.SHIFT;
                break;
            case ActionConstants.SHEET_REMOVE:
                operationType = CommandConstants.OperationType.REMOVE;
                break;
            case ActionConstants.SHEET_RENAME:
                operationType = CommandConstants.OperationType.RENAME;
                break;
        }
        if(operationType != null) {
            dataConnectionBean.setOperationType(operationType);
            this.dataConnectionBean = dataConnectionBean;
        }
    }


    private void constructWebDataObj(JSONObjectWrapper webdataInfoObj, JSONObjectWrapper actionJson){

        JSONObjectWrapper webdataInfo = new JSONObjectWrapper();
        String webDataID = actionJson.getString(JSONConstants.WEBDATA_ID);




		/*webdataInfo.put("URL", showUrl);
		webdataInfo.put("fullURL", url);
		webdataInfo.put("interval",actionJson.getString(JSONConstants.));
		webdataInfo.put("schTime",actionJson.getString(JSONConstants.SCHEDULER_INTERVAL));
		webdataInfo.put("schName",actionJson.getString(JSONConstants.));

		webdataInfo.put("webDataOwnerZUID",actionJson.getString(JSONConstants.));
		webdataInfo.put("webDataOwner",actionJson.getString(JSONConstants.));*/
        //webdataInfo.put("failedCount",);
        //webdataInfo.put("htmlTblList",actionJson.getString(JSONConstants.));
        if(actionJson.has(JSONConstants.URLTYPE)) {webdataInfo.put("URLType",actionJson.getString(JSONConstants.URLTYPE));}
        if(actionJson.has(JSONConstants.DB_NAME)) {webdataInfo.put("DBName",actionJson.getString(JSONConstants.DB_NAME));}
        if(actionJson.has(JSONConstants.MODULE_NAME)) {webdataInfo.put("moduleName",actionJson.getString(JSONConstants.MODULE_NAME));}
        if(actionJson.has(JSONConstants.FIELD_LIST)) {webdataInfo.put("fieldList",actionJson.getString(JSONConstants.FIELD_LIST));}

        //webdataInfo.put("errorCode",);
        webdataInfoObj.put(webDataID,webdataInfo);

    }

    @Override
    public DataConnectionBean getDataConnectionBean() {
        return dataConnectionBean;
    }
}
