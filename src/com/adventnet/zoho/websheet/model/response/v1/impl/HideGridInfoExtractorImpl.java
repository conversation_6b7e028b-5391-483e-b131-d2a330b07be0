package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.HideGridWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.HideGridInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;

public class HideGridInfoExtractorImpl implements HideGridInfoExtractor {


    List<HideGridWrapper> hideGridWrapperList = new ArrayList<>();
    public HideGridInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        CommandConstants.OperationType operationType;
        HideGridWrapper hideGridWrapper;
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        List<String> asnSheetList = ActionJsonUtil.getListOfAssociateSheetNames(actionJson, false);
        String rangeId = null;

        if(actionJson.has(JSONConstants.RANGE_META)) {
            rangeId = actionJson.getJSONObject(JSONConstants.RANGE_META).getString(JSONConstants.RANGE_ID);
        } else if(actionJson.has(JSONConstants.RANGE_ID)) {
            rangeId = actionJson.getString(JSONConstants.RANGE_ID);
        }

        if (action == -1) {
            operationType = CommandConstants.OperationType.HIDE_SHOW_GRID;

            hideGridWrapper = new HideGridWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), operationType, rangeId);
            hideGridWrapperList.add(hideGridWrapper);
        }
        else if (action == ActionConstants.HIDE_GRID || action == ActionConstants.SHOW_GRID || action == ActionConstants.GRID_COLOR) {
            operationType = CommandConstants.OperationType.MODIFY;
            if (asnSheetList != null) {
                for (String asn : asnSheetList) {
                    hideGridWrapper = new HideGridWrapper(asn, operationType, rangeId);
                    hideGridWrapperList.add(hideGridWrapper);
                }
            }
        }
        else if(action == ActionConstants.APPLY_THEME) {
            operationType = CommandConstants.OperationType.GENERATE_LIST;

            hideGridWrapper = new HideGridWrapper(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), operationType, rangeId);
            hideGridWrapperList.add(hideGridWrapper);
        }
    }

    @Override
    public List<HideGridWrapper> getHideGridWrapperList() {
        return hideGridWrapperList;
    }
}
