package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.ProtectionBean;
import com.adventnet.zoho.websheet.model.response.extractor.ProtectedSheetInfoExtractor;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionJsonUtil;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class ProtectedSheetInfoExtractorImpl implements ProtectedSheetInfoExtractor {

    private static final Logger LOGGER = Logger.getLogger(ProtectedSheetInfoExtractorImpl.class.getName());
    List<ProtectionBean> protectionBeans = new ArrayList<>();
    public ProtectedSheetInfoExtractorImpl(JSONObjectWrapper actionJson)
    {
        int action = actionJson.has(JSONConstants.ACTION) ? actionJson.getInt(JSONConstants.ACTION) : -1;
        ProtectionBean protectedSheetInfo;
        List<String> asnSheetList = ActionJsonUtil.getListOfAssociateSheetNames(actionJson, false);
        Long exSharedLinkId = actionJson.optLong(JSONConstants.EXTERNAL_SHARE_LINK_ID);
        switch (action) {
            case ActionConstants.LOCK_SHEET:
                for(String asn : asnSheetList){
                    protectedSheetInfo = new ProtectionBean(null,asn, CommandConstants.OperationType.LOCK_SHEET, exSharedLinkId);
                    protectionBeans.add(protectedSheetInfo);
                }

                break;
            case ActionConstants.UNLOCK_SHEET:
                for(String asn : asnSheetList){
                    protectedSheetInfo = new ProtectionBean(null,asn, CommandConstants.OperationType.UNLOCK_SHEET, exSharedLinkId);
                    protectionBeans.add(protectedSheetInfo);
                }

                break;
            case ActionConstants.SHEET_DUPLICATE:
                String newSheetName = actionJson.get(JSONConstants.NEW_SHEET_NAME).toString();
                protectedSheetInfo = new ProtectionBean(newSheetName, null, CommandConstants.OperationType.CLONE, exSharedLinkId);
                protectionBeans.add(protectedSheetInfo);
                break;
            case ActionConstants.SERVERCLIP_PASTE_SHEET:
                JSONArrayWrapper pastedSheetNames = actionJson.getJSONArray(JSONConstants.PASTED_SHEETNAMES);
                for (int i = 0; i < pastedSheetNames.length(); i++) {
                    JSONArrayWrapper respAry = new JSONArrayWrapper();
                    String shnm = pastedSheetNames.getString(i);
                    protectedSheetInfo = new ProtectionBean(shnm, null, CommandConstants.OperationType.CLONE, exSharedLinkId);
                    protectionBeans.add(protectedSheetInfo);
                }
                break;
            case ActionConstants.LOCK_MULTIPLE:

                JSONArrayWrapper prArray = actionJson.getJSONArray("prObj"); //NO I18N
                boolean isSheet = actionJson.getBoolean("isSheet"); //NO I18N
                CommandConstants.OperationType opType;

                if (isSheet) {
//                      for(String asn : asnSheetList){ //should get proper asn form prObj
                    for (int i = 0; i < prArray.length(); i++) {

                        JSONObjectWrapper prJson = prArray.getJSONObject(i);
                        String sheetName = prJson.getString(JSONConstants.PROTECTED_RANGES);

                        opType = prJson.has("isAllUnlocked") && prJson.getBoolean("isAllUnlocked")  //NO I18N
                                ?
                                CommandConstants.OperationType.REMOVE_SHEET_PROTECTION : CommandConstants.OperationType.MODIFY_LOCK_SHEET;

                        protectedSheetInfo = new ProtectionBean(sheetName, null, opType, exSharedLinkId);

                        protectionBeans.add(protectedSheetInfo);
//                        }
                    }

                }

                break;
            case -1:

                protectedSheetInfo = new ProtectionBean(null, null, CommandConstants.OperationType.GENERATE_LIST, exSharedLinkId);

                protectionBeans.add(protectedSheetInfo);
                break;
        }
    }

    @Override
    public List<ProtectionBean> getProtectionBeans() {
        return protectionBeans;
    }
}
