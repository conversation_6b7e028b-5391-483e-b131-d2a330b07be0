package com.adventnet.zoho.websheet.model.response.v1.impl;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.extractor.AppliedConditionalCellStylesToRangeInfoExtractor;
import com.adventnet.zoho.websheet.model.response.v1.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.JSONConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppliedConditionalCellStylesToRangeInfoExtractorImpl implements AppliedConditionalCellStylesToRangeInfoExtractor {

    Map<String, List<RangeWrapper>> rangeWrapperMap = new HashMap<>();
    public AppliedConditionalCellStylesToRangeInfoExtractorImpl(JSONObjectWrapper actionJson, Map<String, List<DataRange>> cfRangeMap) {
        //For Fetch Data
        if (actionJson.has("ranges")) {
            //For Fetch data only

            JSONArrayWrapper ranges = actionJson.getJSONArray("ranges");    // No I18N

            for (int i = 0; i < ranges.length(); i++) {

                JSONObjectWrapper rangeJson = ranges.getJSONObject(i);
                JSONArrayWrapper rangeBoundaryAry = rangeJson.getJSONArray("boundry");     // No I18N
                String sheetName = rangeJson.getString("sheetId");
                List<RangeWrapper> rangeWrapperList = rangeWrapperMap.get(sheetName);
                if (rangeWrapperList == null) {
                    rangeWrapperList = new ArrayList<>();
                    rangeWrapperMap.put(sheetName, rangeWrapperList);
                }
                int startRow = rangeBoundaryAry.getInt(0);
                int startCol = rangeBoundaryAry.getInt(1);
                int endRow = rangeBoundaryAry.getInt(2);
                int endCol = rangeBoundaryAry.getInt(3);

                int hiddenSRow = (int) (Math.floor(startRow / 8) * 8);
                int hiddenSCol = (int) (Math.floor(startCol / 8) * 8);

                int j = 0;
                boolean isCfFaulty = false;

                JSONArrayWrapper cfMissedArray = rangeJson.has("cfMissed") ? rangeJson.getJSONArray("cfMissed") : null; // No I18N
                JSONArrayWrapper cfInnerArray = new JSONArrayWrapper();

                boolean isCfMissedAryNull = cfMissedArray == null;
                if (!isCfMissedAryNull) {
                    for (int row = startRow; row <= endRow; ) {  // CF response
                        cfInnerArray = ResponseUtils.getInnerArray(cfMissedArray, j);
                        if (cfInnerArray.length() > 0) {
                            for (int col = startCol; col <= endCol; ) {
                                isCfFaulty = (col == startCol) ? ResponseUtils.isFaulty(col, hiddenSCol, cfInnerArray) : true;
                                if (isCfFaulty) {
                                    RangeWrapper wrapper = new RangeWrapper(sheetName, row, col, row, col, CommandConstants.OperationType.APPLIED_CONDITIONAL_FORMATS);
                                    ResponseUtils.mergeAndAddRangeWrapperToList(wrapper, rangeWrapperList);

                                }
                                col = ResponseUtils.getNextNonHiddenCol(col, hiddenSCol, cfInnerArray, endCol);   //getting next faulty col
                            }
                        }//row = ResponseUtils.getNextNonHiddenRow(row, hiddenSRow, cfInnerArray, endRow);  // //getting next faulty row
                        row++;
                        j = (row - startRow);
                    }
                }
            }

        }
        else {
            if (cfRangeMap != null) {
                // During Grid Actions
                int action = actionJson.getInt(JSONConstants.ACTION);
                CommandConstants.OperationType operationType;
                if (action == ActionConstants.CONDITIONAL_FORMAT_DELETE) {
                    operationType = CommandConstants.OperationType.DELETE;
                } else {
                    operationType = CommandConstants.OperationType.MODIFY;
                }
                for (String sheetName : cfRangeMap.keySet()) {
                    List<DataRange> rangeList = cfRangeMap.get(sheetName);
                    List<RangeWrapper> wrapperList = new ArrayList<>();
                    rangeWrapperMap.put(sheetName, wrapperList);
                    for (DataRange range : rangeList) {
                        wrapperList.add(new RangeWrapper(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), operationType));
                    }
                }
            }
        }
    }

    @Override
    public Map<String, List<RangeWrapper>> getRangeWrapperMap() {
        return rangeWrapperMap;
    }
}
