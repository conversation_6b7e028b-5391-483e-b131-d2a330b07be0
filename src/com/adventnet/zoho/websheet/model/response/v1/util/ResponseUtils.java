/* $Id$ */
package com.adventnet.zoho.websheet.model.response.v1.util;

import com.adventnet.iam.IAMUtil;
import com.adventnet.persistence.DataObject;
import com.adventnet.zoho.websheet.dd.SHEETIMAGESDFSSTORE;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ErrorCode.MsgType;
import com.adventnet.zoho.websheet.model.UserProfile.PermissionType;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.ext.parser.ASTRangeNode;
import com.adventnet.zoho.websheet.model.ext.parser.ASTStructuredReferenceNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSJSONizerVisitor;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.iterator.ColumnIterator;
import com.adventnet.zoho.websheet.model.iterator.RowIterator;
import com.adventnet.zoho.websheet.model.pivot.*;
import com.adventnet.zoho.websheet.model.pivot.PivotField.Orientation;
import com.adventnet.zoho.websheet.model.pivot.PivotSortInfo.Order;
import com.adventnet.zoho.websheet.model.pivot.PivotSortInfo.SortMode;
import com.adventnet.zoho.websheet.model.pivot.PivotTable.GrandTotal;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.response.UserInfo;
import com.adventnet.zoho.websheet.model.response.beans.SlicerBean;
import com.adventnet.zoho.websheet.model.response.meta.CellMeta;
import com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.helper.TimeCapsule;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.style.fill.Fill;
import com.adventnet.zoho.websheet.model.style.fill.GradientFill;
import com.adventnet.zoho.websheet.model.style.fill.PatternFill;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSFontScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.model.zs.ZSModelConstants;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.parser.ASTFunNode;
import com.singularsys.jep.parser.ASTVarNode;
import com.singularsys.jep.parser.Node;
import com.zoho.sheet.authorization.util.AuthorizationUtil;
import com.zoho.sheet.chart.ChartUtils;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.ImageUtils;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ZFSNGConstants;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.DecimalFormatSymbols;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import  org.json.JSONObject;

import static com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFCOLS;
import static com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFROWS;
import static com.adventnet.zoho.websheet.model.zs.ZSModelConstants.TRANSPARENT;
import static com.zoho.sheet.util.ConditionFormatUtils.CF_INVALIDRANGE;

public class ResponseUtils {

	private static final Logger LOGGER = Logger.getLogger(ResponseUtils.class.getName());

	public static JSONArrayWrapper getNameRangeDetails(Workbook workbook) {

		List<NamedExpression> namedExpressionList = workbook.getNamedExpressions();

		JSONArrayWrapper namedAry = new JSONArrayWrapper();
		if (namedExpressionList.size() > 0) {
			int pos = 0;

			for (NamedExpression nExp : namedExpressionList) {
				try {
					JSONArrayWrapper tempArray = new JSONArrayWrapper();

					String name = nExp.getName();

					tempArray.put(name);
					tempArray.put(pos++);
					tempArray.put(new JSONObjectWrapper(new ZSJSONizerVisitor(workbook.getFunctionLocale(), workbook).toJson(nExp).toString()));
					tempArray.put(nExp.getScopeASN());
					tempArray.put(nExp.getComment());
//                    tempArray.put(FormulaUtil.getFormula(nExp.getNode(), workbook));

					// If expression is a Range, send range details.
//                    tempArray.put(nExp.isRange());
					namedAry.put(tempArray);
				} catch (Exception e) {
					return null;
				}
			}
		}
		return namedAry;
	}

	public static JSONObjectWrapper getCellStylesDefinitionObj(Workbook workbook, JSONArrayWrapper cellStyleNamesAry, boolean isNewResponse) {
		//JSONArrayWrapper		cellStyleDefAry		=		new		JSONArrayWrapper();
		JSONObjectWrapper cellStyleJSON = new JSONObjectWrapper();
		if (cellStyleNamesAry != null & !cellStyleNamesAry.isEmpty()) {
			//JSONObjectWrapper	cellStyleJSON;
			String styleName;
			JSONArrayWrapper definitionAry;
			CellStyle cellStyle;
			for (int i = 0; i < cellStyleNamesAry.length(); i++) {
				//cellStyleJSON		=	new		JSONObjectWrapper();
				styleName = cellStyleNamesAry.getString(i);
				cellStyle = workbook.getCellStyle(styleName);
				if (cellStyle != null) {
					if(isNewResponse) {
						definitionAry = getCellStyleDefinition(workbook, cellStyle);
					}
					else {
						definitionAry = getCellStyleDefinition_deep(workbook, cellStyle);
					}
					cellStyleJSON.put(styleName, definitionAry);
					//cellStyleDefAry.put(cellStyleJSON);
				}
			}
		}

		//return 	cellStyleDefAry;
		return cellStyleJSON;
	}

	public static JSONObjectWrapper getTextStylesDefinitionObj(Workbook workbook, JSONArrayWrapper textStyleNamesAry) {
		//JSONArrayWrapper		cellStyleDefAry		=		new		JSONArrayWrapper();
		JSONObjectWrapper textStyleJSON = new JSONObjectWrapper();
		if (textStyleNamesAry != null & !textStyleNamesAry.isEmpty()) {
			//JSONObjectWrapper	cellStyleJSON;
			String styleName;
			JSONArrayWrapper definitionAry;
			TextStyle textStyle;
			for (int i = 0; i < textStyleNamesAry.length(); i++) {
				//cellStyleJSON		=	new		JSONObjectWrapper();
				styleName = textStyleNamesAry.getString(i);
				textStyle = workbook.getTextStyle(styleName);
				if (textStyle != null) {
					definitionAry = getTextStyleDefinition(workbook, textStyle);
					textStyleJSON.put(styleName, definitionAry);
				}
			}
		}
		return textStyleJSON;
	}

	public static JSONObjectWrapper getRowStylesDefinitionObj(Workbook workbook, JSONArrayWrapper rowStyleNamesAry) {
		//JSONArrayWrapper		rowStyleDefAry		=		new		JSONArrayWrapper();
		JSONObjectWrapper rowStyleJSON = new JSONObjectWrapper();
		if (rowStyleNamesAry != null & !rowStyleNamesAry.isEmpty()) {
			//JSONObjectWrapper	rowStyleJSON;
			String styleName;
			RowStyle rowStyle;
			for (int i = 0; i < rowStyleNamesAry.length(); i++) {
				//rowStyleJSON		=	new		JSONObjectWrapper();
				styleName = rowStyleNamesAry.getString(i);
				rowStyle = workbook.getRowStyle(styleName);
				if (rowStyle != null) {
					if(rowStyle.getPropertyAsString_deep(RowStyle.Property.ROWHEIGHT,workbook) != null) {
						rowStyleJSON.put(styleName, EngineUtils1.convertToPixels(rowStyle.getPropertyAsString_deep(RowStyle.Property.ROWHEIGHT, workbook), EngineConstants.ROWDPI));
					} else {
						rowStyleJSON.put(styleName, workbook.getDefaultRowHeight());
					}
					//	rowStyleDefAry.put(rowStyleJSON);
				}
			}
		}

		//return 	rowStyleDefAry;
		return rowStyleJSON;
	}

	public static JSONObjectWrapper getColumnStylesDefinitionObj(Workbook workbook, JSONArrayWrapper columnStyleNamesAry) {
		JSONArrayWrapper columnStyleDefAry = new JSONArrayWrapper();
		JSONObjectWrapper columnStyleJSON = new JSONObjectWrapper();
		if (columnStyleNamesAry != null & !columnStyleNamesAry.isEmpty()) {
			//JSONObjectWrapper	columnStyleJSON;
			String styleName;
			ColumnStyle columnStyle;
			for (int i = 0; i < columnStyleNamesAry.length(); i++) {
				//columnStyleJSON		=	new		JSONObjectWrapper();
				styleName = columnStyleNamesAry.getString(i);
				columnStyle = workbook.getColumnStyle(styleName);
				if (columnStyle != null) {
					if(columnStyle.getPropertyAsString_deep(ColumnStyle.Property.COLUMNWIDTH, workbook) != null) {
						columnStyleJSON.put(styleName, EngineUtils1.convertToPixels(columnStyle.getPropertyAsString_deep(ColumnStyle.Property.COLUMNWIDTH, workbook), EngineConstants.COLDPI));
					} else {
						columnStyleJSON.put(styleName, workbook.getDefaultColumnWidth());
					}
					columnStyleDefAry.put(columnStyleJSON);
				}
			}
		}

		//return 	columnStyleDefAry;
		return columnStyleJSON;
	}

	public static JSONObjectWrapper getTextStyleWithDef(Workbook workbook) {
		Map<String, TextStyle> textStyleMap = workbook.getTextStyleMap();
		JSONObjectWrapper textStyleObject = new JSONObjectWrapper();

		for (Map.Entry<String, TextStyle> entry : textStyleMap.entrySet()) {
			TextStyle textStyle = entry.getValue();
			String styleName = textStyle.getStyleName();
			JSONArrayWrapper textStyleDef = getTextStyleDefinition(workbook, textStyle);
			textStyleObject.put(styleName, textStyleDef);
		}
		return textStyleObject;
	}

	public static JSONObjectWrapper getCellStylesWithDef(Workbook workbook, boolean isNewResponse) {
		Map<String, CellStyle> cellStyleMap = workbook.getCellStyleMap();
		JSONObjectWrapper cellStyleObj = new JSONObjectWrapper();
		JSONArrayWrapper cellStyleDef = new JSONArrayWrapper();

		for (Map.Entry<String, CellStyle> entry : cellStyleMap.entrySet()) {
			CellStyle cellStyle = entry.getValue();
			String styleName = cellStyle.getStyleName();
			if(isNewResponse) {
				cellStyleDef = getCellStyleDefinition(workbook, cellStyle);
			}
			else {
				cellStyleDef = getCellStyleDefinition_deep(workbook, cellStyle);
			}

			//cellStyleObj		=	new JSONObjectWrapper();
			cellStyleObj.put(styleName, cellStyleDef);
		}
		return cellStyleObj;
	}

	/**
	 * Returns an JSONArrayWrapper containing text style definitions in following order
	 * 1. Text Color, Empty if transparent.
	 * 2. Font Size.
	 * 3. Font Name.
	 * 4. Font Weight (Normal/Bold)
	 * 5. Font Style (Italic)
	 * 6. Text Line
	 * 7. Text underlined.
	 * @param workbook
	 * @param textStyle
	 * @return
	 */
	public static JSONArrayWrapper getTextStyleDefinition(Workbook workbook, TextStyle textStyle)
	{
		JSONArrayWrapper textStyleDefArray = new JSONArrayWrapper();
		String textColor = null;
		ZSColor textColorObj = (ZSColor) textStyle.getProperty_Deep(TextStyle.Property.COLOR, workbook);
		if(textColorObj != null)
		{
			textColor = ZSColor.getHexColor(textColorObj, workbook.getTheme())
				+ (textColorObj.getThemeColorToWrite() == null ? "" : " "+textColorObj.getThemeColorToWrite())
				+ (textColorObj.getColorTintToWrite() == null ? "" : " "+textColorObj.getColorTintToWrite());
		}
		textColor = (TRANSPARENT).equals(textColor) ? "" : textColor;   //No I18N
		textStyleDefArray.put(0, textColor);
		textStyleDefArray.put(1, textStyle.getPropertyAsString_deep(TextStyle.Property.FONTSIZE, workbook));

		String fontName = textStyle.getPropertyAsString_deep(TextStyle.Property.FONTNAME, workbook);
		FontFace tempFontFace = workbook.getFontFace(fontName);
		if (tempFontFace != null) {
			fontName = tempFontFace.getFontFamily();
		}
		textStyleDefArray.put(2, fontName);

		textStyleDefArray.put(3, textStyle.getPropertyAsString_deep(TextStyle.Property.FONTWEIGHT, workbook));
		textStyleDefArray.put(4, textStyle.getPropertyAsString_deep(TextStyle.Property.FONTSTYLE, workbook));
		textStyleDefArray.put(5, textStyle.getPropertyAsString_deep(TextStyle.Property.TEXTLINETHROUGHSTYLE, workbook));
		textStyleDefArray.put(6, textStyle.getPropertyAsString_deep(TextStyle.Property.TEXTUNDERLINESTYLE, workbook));

		return textStyleDefArray;
	}

	private static JSONArrayWrapper getBorderStyleDefintion(Workbook workbook, CellStyle cellStyle) {
		JSONArrayWrapper borders = new JSONArrayWrapper();        // another way to handle using string buffer
		borders.put(0, cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, workbook));
		borders.put(1, cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERRIGHT, workbook));
		borders.put(2, cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERTOP, workbook));
		borders.put(3, cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERBOTTOM, workbook));
		return borders;
	}

	private static JSONArrayWrapper getCellStyleDefinition_deep(Workbook workbook, CellStyle cellStyle) {
		JSONArrayWrapper cellStyleDefAry = new JSONArrayWrapper();
		if (cellStyle != null) {

			FontFace tempFontFace;
			String fontName;
			String border;

			// FontFace, FontFamily, Bold, Italic, Underline, Strikethrough, BG Color, Font Color.
			//	Border, Align[Horizontal, Vertical], Cell Comment, HyperLink.
			// String backColor = "transparent".equals(cellStyle.getBackgroundColor()) ? "" : cellStyle.getBackgroundColor() ;   //No I18N
			String backColor = cellStyle.getPropertyAsString_deep(CellStyle.Property.BACKGROUNDCOLOR, workbook);
			backColor = backColor != null && !TRANSPARENT.equals(backColor) ? backColor : "";	//No I18N
			cellStyleDefAry.put(0, backColor);

			String textColor = cellStyle.getPropertyAsString_deep(TextStyle.Property.COLOR, workbook);
			textColor = TRANSPARENT.equals(textColor) ? "" : textColor;   //No I18N
			cellStyleDefAry.put(1, textColor);

			cellStyleDefAry.put(2, cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTSIZE, workbook));

			fontName = cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTNAME, workbook);
			tempFontFace = workbook.getFontFace(fontName);
			if (tempFontFace != null) {
				fontName = tempFontFace.getFontFamily();
			}
			cellStyleDefAry.put(3, fontName);

			cellStyleDefAry.put(4, cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTWEIGHT, workbook));
			cellStyleDefAry.put(5, cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTSTYLE, workbook));

			String textLineThroughStyle = cellStyle.getPropertyAsString_deep(TextStyle.Property.TEXTLINETHROUGHSTYLE, workbook);
			if(textLineThroughStyle != null)
			{
				cellStyleDefAry.put(6, textLineThroughStyle);
			}
			String textUnderLineStyle = cellStyle.getPropertyAsString_deep(TextStyle.Property.TEXTUNDERLINESTYLE, workbook);
			if(textUnderLineStyle != null)
			{
				cellStyleDefAry.put(7, textUnderLineStyle);
			}

			String displayType = cellStyle.getPropertyAsString_deep(CellStyle.Property.DISPLAYTYPE, workbook);
			cellStyleDefAry.put(8, "1".equals(displayType) ? "wrap" : displayType); //No I18N

			//For imported spreadsheet Check
			String hAlignTemp = null;
			if(cellStyle.getPropertyAsString_deep(CellStyle.Property.TEXTALIGNSOURCE, workbook) == null || "fix".equals(cellStyle.getPropertyAsString_deep(CellStyle.Property.TEXTALIGNSOURCE, workbook)))
			{
				hAlignTemp = "right".equals(cellStyle.getPropertyAsString_deep(ParagraphStyle.Property.TEXTALIGN, workbook)) ? "end" : ("left".equals(cellStyle.getPropertyAsString_deep(ParagraphStyle.Property.TEXTALIGN, workbook)) ? "start" : cellStyle.getPropertyAsString_deep(ParagraphStyle.Property.TEXTALIGN, workbook));	//No I18N
			}
			if(hAlignTemp != null)
			{
				cellStyleDefAry.put(9, hAlignTemp);
			}
			String vAlign = cellStyle.getPropertyAsString_deep(CellStyle.Property.VERTICALALIGN, workbook);
			if(vAlign != null)
			{
				cellStyleDefAry.put(10, cellStyle.getPropertyAsString_deep(CellStyle.Property.VERTICALALIGN, workbook));
			}
			JSONArrayWrapper borderAry = new JSONArrayWrapper();		// another way to handle using string buffer
			borderAry.put(0, cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, workbook));
			borderAry.put(1, cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERRIGHT, workbook));
			borderAry.put(2, cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERTOP, workbook));
			borderAry.put(3, cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERBOTTOM, workbook));
			cellStyleDefAry.put(11, borderAry);
			String rotationAngle = cellStyle.getPropertyAsString_deep(CellStyle.Property.ROTATIONANGLE, workbook);
			if(rotationAngle != null)
			{
				int angle = Integer.parseInt(rotationAngle);
				if(angle >= 270)
				{
					angle = angle - 360;
				}
				rotationAngle = String.valueOf(Math.min(angle, 90));
			}
			cellStyleDefAry.put(12, rotationAngle);
			cellStyleDefAry.put(13, cellStyle.getPropertyAsString_deep(ParagraphStyle.Property.ZSINDENT, workbook));

			//For webclient to differentiate themeFont and normalFont
			//Mobile client will use the fontFamily at the 3rd index as usual
			fontName = cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTNAME, workbook);
			if(!ZSFontScheme.BODY_FONTNAME.equals(fontName))
			{
				tempFontFace = workbook.getFontFace(fontName);
				if (tempFontFace != null) {
					fontName = tempFontFace.getFontFamily();
				}
			}
			cellStyleDefAry.put(14, fontName);
		}
		return cellStyleDefAry;
	}


	private static JSONArrayWrapper getCellStyleDefinition(Workbook workbook, CellStyle cellStyle) {
		JSONArrayWrapper cellStyleDefAry = new JSONArrayWrapper();
		if (cellStyle != null) {

			ZSTheme theme = workbook.getTheme();
			FontFace tempFontFace;
			String fontName;
			String border;

			// FontFace, FontFamily, Bold, Italic, Underline, Strikethrough, BG Color, Font Color.
			//	Border, Align[Horizontal, Vertical], Cell Comment, HyperLink.
			// String backColor = "transparent".equals(cellStyle.getBackgroundColor()) ? "" : cellStyle.getBackgroundColor() ;   //No I18N
			String backColor = cellStyle.getPropertyAsString(CellStyle.Property.BACKGROUNDCOLOR, theme);
			backColor = backColor != null && !"transparent".equals(backColor) ? backColor : "";	//No I18N
			cellStyleDefAry.put(0, backColor);

			String textColor = cellStyle.getPropertyAsString(TextStyle.Property.COLOR, theme);
			textColor = "transparent".equals(textColor) ? "" : textColor;   //No I18N
			cellStyleDefAry.put(1, textColor);

			cellStyleDefAry.put(2, cellStyle.getPropertyAsString(TextStyle.Property.FONTSIZE, theme));

			fontName = cellStyle.getPropertyAsString(TextStyle.Property.FONTNAME, theme);
			tempFontFace = workbook.getFontFace(fontName);
			if (tempFontFace != null) {
				fontName = tempFontFace.getFontFamily();
			}
			cellStyleDefAry.put(3, fontName);

			cellStyleDefAry.put(4, cellStyle.getPropertyAsString(TextStyle.Property.FONTWEIGHT, theme));
			cellStyleDefAry.put(5, cellStyle.getPropertyAsString(TextStyle.Property.FONTSTYLE, theme));

			String textLineThroughStyle = cellStyle.getPropertyAsString(TextStyle.Property.TEXTLINETHROUGHSTYLE, theme);
			if(textLineThroughStyle != null)
			{
				cellStyleDefAry.put(6, textLineThroughStyle);
			}
			String textUnderLineStyle = cellStyle.getPropertyAsString(TextStyle.Property.TEXTUNDERLINESTYLE, theme);
			if(textUnderLineStyle != null)
			{
				cellStyleDefAry.put(7, textUnderLineStyle);
			}
			String displayType = cellStyle.getPropertyAsString(CellStyle.Property.DISPLAYTYPE, theme);
			cellStyleDefAry.put(8, "1".equals(displayType) ? "wrap" : displayType); //No I18N


			//For imported spreadsheet Check
			String hAlign = cellStyle.getPropertyAsString(ParagraphStyle.Property.TEXTALIGN, theme);
			String hAlignTemp = "right".equals(hAlign) ? "end" : ("left".equals(hAlign) ? "start" : hAlign);	//No I18N
			if(hAlignTemp != null)
			{
				cellStyleDefAry.put(9, hAlignTemp);
			}
			String vAlign = cellStyle.getPropertyAsString(CellStyle.Property.VERTICALALIGN, theme);
			if(vAlign != null)
			{
				cellStyleDefAry.put(10, cellStyle.getPropertyAsString(CellStyle.Property.VERTICALALIGN, theme));
			}

			JSONArrayWrapper borderAry = new JSONArrayWrapper();		// another way to handle using string buffer
			borderAry.put(0, cellStyle.getPropertyAsString(CellStyle.Property.BORDERLEFT, theme));
			borderAry.put(1, cellStyle.getPropertyAsString(CellStyle.Property.BORDERRIGHT, theme));
			borderAry.put(2, cellStyle.getPropertyAsString(CellStyle.Property.BORDERTOP, theme));
			borderAry.put(3, cellStyle.getPropertyAsString(CellStyle.Property.BORDERBOTTOM, theme));
			cellStyleDefAry.put(11, borderAry);
			String rotationAngle = cellStyle.getPropertyAsString(CellStyle.Property.ROTATIONANGLE, theme);
			if(rotationAngle != null)
			{
				int angle = Integer.parseInt(rotationAngle);
				if(angle >= 270)
				{
					angle = angle - 360;
				}
				rotationAngle = String.valueOf(Math.min(angle, 90));
			}
			cellStyleDefAry.put(12, rotationAngle);
			cellStyleDefAry.put(13, cellStyle.getPropertyAsString(ParagraphStyle.Property.ZSINDENT, theme));

			//For webclient to differentiate themeFont and normalFont
			//Mobile client will use the fontFamily at the 3rd index as usual
			fontName = cellStyle.getPropertyAsString(TextStyle.Property.FONTNAME, theme);
			if(!ZSFontScheme.BODY_FONTNAME.equals(fontName))
			{
				tempFontFace = workbook.getFontFace(fontName);
				if (tempFontFace != null) {
					fontName = tempFontFace.getFontFamily();
				}
			}
			cellStyleDefAry.put(14, fontName);
			ZSPattern cellPattern = cellStyle.getPattern();
			JSONObject numberFormatObj = getNumberFormatDefinition(workbook, cellPattern);
			cellStyleDefAry.put(15, numberFormatObj);

		}
		return cellStyleDefAry;
	}

	private static JSONObject getNumberFormatDefinition(Workbook workbook, ZSPattern cellPattern) {
		JSONObject patternObject = new JSONObject();
		if (cellPattern != null) {
			try {
				long s = System.currentTimeMillis();
				TimeCapsule timeCapsule = new TimeCapsule();
				timeCapsule.startEvent(TimeCapsule.Event.GET_CELL_FORMATINFO);
				HashMap<String, Object> patternMap = CellUtil.getCellFormatInfoNew(workbook, cellPattern, timeCapsule);
				timeCapsule.endCurrentEvent();

				if (patternMap != null && !patternMap.isEmpty()) {
					for (String key : patternMap.keySet()) {
						patternObject.put(key, patternMap.get(key));
					}
				}
				if (System.currentTimeMillis() - s > 100) {
					LOGGER.log(Level.INFO, "Pattern taking time for cellStyleDefinition >>> {0}", patternObject);
				}
			} catch (Exception e) {
				LOGGER.log(Level.INFO, "Exception while getting format info for cellStyleDefinition : {0}", e);
			}
		}
		return patternObject;
	}

	public static JSONArrayWrapper getSlicerStyleDefinition(Workbook workbook, CellStyle cellStyle) {
		JSONArrayWrapper cellStyleDefAry = new JSONArrayWrapper();
		FontFace tempFontFace;
		String fontName;

		Fill fill = (Fill) cellStyle.getProperty(CellStyle.Property.FILL);
		if(fill instanceof PatternFill) {
			// sending only one color in response as Pattern fill is not yet supported.
			ZSColor color = fill.getMajorColor();
			String hexColor = color.getHexColorToWrite();
			String tint = color.getColorTintToWrite();
			String accent = color.getThemeColorToWrite();
			JSONObjectWrapper colorJson = new JSONObjectWrapper();
			if(hexColor != null) {
				colorJson.put(Integer.toString(CommandConstants.HEX_COLOR), hexColor);
			}
			else {
				colorJson.put(Integer.toString(CommandConstants.TINT), tint);
				colorJson.put(Integer.toString(CommandConstants.THEME_COLOR), accent);
			}
			cellStyleDefAry.put(0, colorJson);
		}
		else {
			cellStyleDefAry.put(0, new JSONObjectWrapper());
		}

		Object textColor = cellStyle.getProperty(TextStyle.Property.COLOR);
		if(textColor != null ) {
			String hexColor = ((ZSColor)textColor).getHexColorToWrite();
			String tint = ((ZSColor)textColor).getColorTintToWrite();
			String accent = ((ZSColor)textColor).getThemeColorToWrite();
			JSONObjectWrapper colorJson = new JSONObjectWrapper();
			if(hexColor != null) {
				colorJson.put(Integer.toString(CommandConstants.HEX_COLOR), hexColor);
			}
			else {
				colorJson.put(Integer.toString(CommandConstants.TINT), tint);
				colorJson.put(Integer.toString(CommandConstants.THEME_COLOR), accent);
			}
			cellStyleDefAry.put(1, colorJson);
		}
		else {
			cellStyleDefAry.put(1, new JSONObjectWrapper());
		}

		cellStyleDefAry.put(2, cellStyle.getPropertyAsString(TextStyle.Property.FONTSIZE, workbook.getTheme()));

		fontName = cellStyle.getPropertyAsString(TextStyle.Property.FONTNAME, workbook.getTheme());
		tempFontFace = workbook.getFontFace(fontName);
		if (tempFontFace != null) {
			fontName = tempFontFace.getFontFamily();
		}
		cellStyleDefAry.put(3, fontName);


		JSONArrayWrapper borderAry = new JSONArrayWrapper();
		CellStyle.Property[] borders = new CellStyle.Property[] {CellStyle.Property.BORDERLEFT, CellStyle.Property.BORDERRIGHT, CellStyle.Property.BORDERTOP, CellStyle.Property.BORDERBOTTOM};
		for(int i=0; i< borders.length; i++) {
			JSONObjectWrapper borderObj = new JSONObjectWrapper();
			BorderProperties props = (BorderProperties) CellStyle.getProperty(cellStyle.getCellStyleProperties(),borders[i]);
			if(props != null) {
				ZSColor color = props.getColor();
				JSONObjectWrapper colorObj = new JSONObjectWrapper();
				if(color != null) {
					if(color.getThemeColorToWrite() != null) {
//                        colorObj.put(Integer.toString(CommandConstants.HEX_COLOR), getHexColor(color, theme));
						colorObj.put(Integer.toString(CommandConstants.THEME_COLOR), color.getThemeColorToWrite());
						colorObj.put(Integer.toString(CommandConstants.TINT), color.getColorTintToWrite());
					}
					else {
						colorObj.put(Integer.toString(CommandConstants.HEX_COLOR), color.getHexColorToWrite());
					}
				}
				borderObj.put(Integer.toString(CommandConstants.COLOR), colorObj);
				borderObj.put(Integer.toString(CommandConstants.STYLE), String.format("%.4f",props.getSize()) + "px " + props.getType());
			}
			borderAry.put(i, borderObj);
		}


		cellStyleDefAry.put(4, borderAry);
		JSONObjectWrapper fillJson = new JSONObjectWrapper();
		if(fill instanceof GradientFill){
			GradientFill gradient = (GradientFill) fill;
			List<Double> positions = new ArrayList<>(gradient.getGradientColors().keySet());
			Collections.sort(positions);
			// We support only 2 color gradient in Slicer now.
			ZSColor startColor = gradient.getGradientColors().get(positions.get(0));
			ZSColor stopColor = gradient.getGradientColors().get(positions.get(positions.size()-1));
			String hexColorStart = startColor.getHexColorToWrite();
			String hexColorStop = stopColor.getHexColorToWrite();
			String accentStart = startColor.getThemeColorToWrite();
			String tintStart = startColor.getColorTintToWrite();
			String accentStop = stopColor.getThemeColorToWrite();
			String tintStop = stopColor.getColorTintToWrite();

			JSONObjectWrapper colorJsonStart = new JSONObjectWrapper();
			JSONObjectWrapper colorJsonStop = new JSONObjectWrapper();

			if(hexColorStart != null) {
				colorJsonStart.put(Integer.toString(CommandConstants.HEX_COLOR), hexColorStart);
			}
			if(hexColorStop != null) {
				colorJsonStop.put(Integer.toString(CommandConstants.HEX_COLOR), hexColorStop);
			}
			if(accentStart != null ){
				colorJsonStart.put(Integer.toString(CommandConstants.TINT), tintStart);
				colorJsonStart.put(Integer.toString(CommandConstants.THEME_COLOR), accentStart);
			}
			if(accentStop != null){
				colorJsonStop.put(Integer.toString(CommandConstants.TINT), tintStop);
				colorJsonStop.put(Integer.toString(CommandConstants.THEME_COLOR), accentStop);
			}
			fillJson.put("bgStart",colorJsonStart);
			fillJson.put("bgStop",colorJsonStop);
		}
		cellStyleDefAry.put(5,fillJson);
		return cellStyleDefAry;
	}
	public static JSONArrayWrapper getTableStyleDefinition(Workbook workbook, CellStyle cellStyle, int stripeSize) {
		JSONArrayWrapper cellStyleDefAry = new JSONArrayWrapper();


		FontFace tempFontFace;
		String fontName;
		String border;
		ZSTheme theme = workbook.getTheme();


		// String backColor = "transparent".equals(cellStyle.getBackgroundColor()) ? "" : cellStyle.getBackgroundColor() ;   //No I18N
		Object backColor = cellStyle.getProperty(CellStyle.Property.BACKGROUNDCOLOR);
		if(backColor != null && !"transparent".equals(cellStyle.getPropertyAsString(CellStyle.Property.BACKGROUNDCOLOR, workbook.getTheme()))) {
			String hexColor = ((ZSColor)backColor).getHexColorToWrite();
			String tint = ((ZSColor)backColor).getColorTintToWrite();
			String accent = ((ZSColor)backColor).getThemeColorToWrite();
			JSONObjectWrapper colorJson = new JSONObjectWrapper();
			if(hexColor != null) {
				colorJson.put(Integer.toString(CommandConstants.HEX_COLOR), hexColor);
			}
			else {
//                colorJson.put(Integer.toString(CommandConstants.HEX_COLOR), getHexColor((ZSColor)backColor, theme));
				colorJson.put(Integer.toString(CommandConstants.TINT), tint);
				colorJson.put(Integer.toString(CommandConstants.THEME_COLOR), accent);
			}
			cellStyleDefAry.put(0, colorJson);
		}
		else {
			cellStyleDefAry.put(0, new JSONObjectWrapper());
		}

		Object textColor = cellStyle.getProperty(TextStyle.Property.COLOR);
		if(textColor != null && !"transparent".equals(cellStyle.getPropertyAsString(TextStyle.Property.COLOR, workbook.getTheme()))) {
			String hexColor = ((ZSColor)textColor).getHexColorToWrite();
			String tint = ((ZSColor)textColor).getColorTintToWrite();
			String accent = ((ZSColor)textColor).getThemeColorToWrite();
			JSONObjectWrapper colorJson = new JSONObjectWrapper();
			if(hexColor != null) {
				colorJson.put(Integer.toString(CommandConstants.HEX_COLOR), hexColor);
			}
			else {
//                colorJson.put(Integer.toString(CommandConstants.HEX_COLOR), getHexColor((ZSColor)textColor, theme));
				colorJson.put(Integer.toString(CommandConstants.TINT), tint);
				colorJson.put(Integer.toString(CommandConstants.THEME_COLOR), accent);
			}
			cellStyleDefAry.put(1, colorJson);
		}
		else {
			cellStyleDefAry.put(1, new JSONObjectWrapper());
		}

		cellStyleDefAry.put(2, cellStyle.getPropertyAsString(TextStyle.Property.FONTSIZE, workbook.getTheme()));

		fontName = cellStyle.getPropertyAsString(TextStyle.Property.FONTNAME, workbook.getTheme());
		tempFontFace = workbook.getFontFace(fontName);
		if (tempFontFace != null) {
			fontName = tempFontFace.getFontFamily();
		}
		cellStyleDefAry.put(3, fontName);

		cellStyleDefAry.put(4, cellStyle.getPropertyAsString(TextStyle.Property.FONTWEIGHT, workbook.getTheme()));
		cellStyleDefAry.put(5, cellStyle.getPropertyAsString(TextStyle.Property.FONTSTYLE, workbook.getTheme()));

		if (cellStyle.getPropertyAsString(TextStyle.Property.TEXTLINETHROUGHSTYLE, workbook.getTheme()) != null) {
			cellStyleDefAry.put(6, cellStyle.getPropertyAsString(TextStyle.Property.TEXTLINETHROUGHSTYLE, workbook.getTheme()));
		}

		if (cellStyle.getPropertyAsString(TextStyle.Property.TEXTUNDERLINESTYLE, workbook.getTheme()) != null) {
			cellStyleDefAry.put(7, cellStyle.getPropertyAsString(TextStyle.Property.TEXTUNDERLINESTYLE, workbook.getTheme()));
		}



//        border = cellStyleProperties.getBorder();
		JSONArrayWrapper borderAry = new JSONArrayWrapper();		// another way to handle using string buffer
//        if (border != null) {
//            borderAry.put(border);
//        } else {
//            borderAry.put(0, cellStyle.getPropertyAsString(CellStyle.Property.BORDERLEFT, workbook.getTheme()));
//            borderAry.put(1, cellStyle.getPropertyAsString(CellStyle.Property.BORDERRIGHT, workbook.getTheme()));
//            borderAry.put(2, cellStyle.getPropertyAsString(CellStyle.Property.BORDERTOP, workbook.getTheme()));
//            borderAry.put(3, cellStyle.getPropertyAsString(CellStyle.Property.BORDERBOTTOM, workbook.getTheme()));
//            borderAry.put(4, cellStyle.getPropertyAsString(CellStyle.Property.BORDERINNERVERTICAL, workbook.getTheme()));
//            borderAry.put(5, cellStyle.getPropertyAsString(CellStyle.Property.BORDERINNERHORIZONTAL, workbook.getTheme()));
//        }
		CellStyle.Property[] borders = new CellStyle.Property[] {CellStyle.Property.BORDERLEFT, CellStyle.Property.BORDERRIGHT, CellStyle.Property.BORDERTOP, CellStyle.Property.BORDERBOTTOM, CellStyle.Property.BORDERINNERVERTICAL, CellStyle.Property.BORDERINNERHORIZONTAL};
		for(int i=0; i< borders.length; i++) {
			JSONObjectWrapper borderObj = new JSONObjectWrapper();
			BorderProperties props = (BorderProperties) CellStyle.getProperty(cellStyle.getCellStyleProperties(),borders[i]);
			if(props != null) {
				ZSColor color = props.getColor();
				JSONObjectWrapper colorObj = new JSONObjectWrapper();
				if(color != null) {
					if(color.getThemeColorToWrite() != null) {
//                        colorObj.put(Integer.toString(CommandConstants.HEX_COLOR), getHexColor(color, theme));
						colorObj.put(Integer.toString(CommandConstants.THEME_COLOR), color.getThemeColorToWrite());
						colorObj.put(Integer.toString(CommandConstants.TINT), color.getColorTintToWrite());
					}
					else {
						colorObj.put(Integer.toString(CommandConstants.HEX_COLOR), color.getHexColorToWrite());
					}
				}
				borderObj.put(Integer.toString(CommandConstants.COLOR), colorObj);
				borderObj.put(Integer.toString(CommandConstants.STYLE), String.format("%.4f",props.getSize()) + "in " + props.getType());
			}
			borderAry.put(i, borderObj);

		}
		cellStyleDefAry.put(8, borderAry);
		cellStyleDefAry.put(9, stripeSize);



		return cellStyleDefAry;
	}

	public static JSONArrayWrapper getInnerArray(JSONArrayWrapper arr, int idx) {
		JSONArrayWrapper innArr = new JSONArrayWrapper(arr.get(idx).toString());
		return innArr;
	}

	public static int getNextNonHiddenRow(int idx, int hdnSRow, JSONArrayWrapper hdnRow, int er) {

		if (idx > er) {
			return idx;
		}
		idx++;
		int q = Math.round((idx - hdnSRow) / 8);
		int m = (idx % 8);
		if (m == 8) {
			q++;
			m = 0;
		}
		if (hdnRow.length() == q) {
			return er + 1;
		}

		while (!getBit(hdnRow.getInt(q), 8, m)) {
			m++;
			if (m == 8) {
				q++;
				m = 0;
			}
			if (hdnRow.length() == q) {  // q exceeds ary length
				return er + 1;
			}
		}
		int result = hdnSRow + (q * 8) + m;
		return result;
	}

	public static int getNextNonHiddenCol(int idx, int hdnSCol, JSONArrayWrapper hdnCol, int ec) {

		if (idx > ec) {
			return idx;
		}
		idx++;
		int q = Math.round((idx - hdnSCol) / 8);
		int m = (idx % 8);
		if (m == 8) {
			q++;
			m = 0;
		}
		if (hdnCol.length() == q) {
			return ec + 1;
		}

		while (!getBit(hdnCol.getInt(q), 8, m)) {
			m++;
			if (m == 8) {
				q++;
				m = 0;
			}
			if (hdnCol.length() == q) {  // q exceeds ary length
				return ec + 1;
			}
		}
		return hdnSCol + (q * 8) + m;
	}

	public static boolean isFaulty(int idx, int hdnSCol, JSONArrayWrapper hdnCol) {
		int q = Math.round((idx - hdnSCol) / 8);
		int m = (idx % 8);

		if (m == 8) {
			q++;
			m = 0;
		}
		boolean bit = getBit(hdnCol.getInt(q), 8, m);
		return bit;
	}

	public static boolean getBit(int inp, int type, int base) {
		return (inp & (1 << type - base - 1)) == 0 ? true : false;
	}

	public static JSONObjectWrapper getFreezeResponse(String sheetName, int row, int column) {
		JSONObjectWrapper freezeResponse = new JSONObjectWrapper();
		freezeResponse.put(JSONConstants.ASSOCIATED_SHEET_NAME, sheetName);
		freezeResponse.put(JSONConstants.START_ROW, row);
		freezeResponse.put(JSONConstants.START_COLUMN, column);

		return freezeResponse;

	}

	public static JSONObjectWrapper getUsersMetaInfo(String zuid) {
		//long  zuid    =    DocumentUtils.getZUID();
		JSONObjectWrapper userMeta = new JSONObjectWrapper();
		userMeta.put(Integer.toString(CommandConstants.ZUID), zuid);

		return userMeta;

	}

	public static void getDocumentMeta(JSONObjectWrapper documentMeta, JSONObjectWrapper previlege, String rangeId, boolean isPublishedView) {
		WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();
		UserProfile uProfile = CurrentRealm.getUserProfile();
		PermissionType pType = uProfile.getPermissionType();
		String docOwnerZUID = container.getDocOwnerZUID();
		String userZuid = uProfile.getZUserId();

		if(isPublishedView) {
			try {
				JSONObjectWrapper json = DocumentUtils.getPublicResourceMeta(container, rangeId);
				documentMeta.put(Integer.toString(CommandConstants.ISEDITABLE), json.getBoolean(Integer.toString(CommandConstants.ALLOW_INTERACTIONS)) ? 1 : 0);
			} catch (Exception e) {
				LOGGER.log(Level.WARNING, "Exception occured while getting published meta :: ", e);
			}
		} else {
			documentMeta.put(Integer.toString(CommandConstants.ISEDITABLE), AuthorizationUtil.getIsDocumentEditable(pType) ? 1 : 0);
		}
		documentMeta.put(Integer.toString(CommandConstants.ISSHARABLE), AuthorizationUtil.getIsDocumentSharable(pType, previlege) ? 1 : 0);
		documentMeta.put(Integer.toString(CommandConstants.ISCOMMENTABLE), AuthorizationUtil.getIsDocumentCommentable(pType) ? 1 : 0);
		documentMeta.put(Integer.toString(CommandConstants.IS_ORG_PUBLISHABLE), AuthorizationUtil.getIsDocumentOrgPublishable(previlege) ? 1 : 0);
		documentMeta.put(Integer.toString(CommandConstants.IS_EXTERNAL_PUBLISHABLE), AuthorizationUtil.getIsDocumentExternalPublishable(previlege) ? 1 : 0);

		boolean isTemplate = false;
		try {
			isTemplate = container.isTemplate();
		} catch (Exception e1) {
			LOGGER.log(Level.WARNING, "Exception occured while getting isTemplate :: ", e1);
		}
		documentMeta.put(Integer.toString(CommandConstants.IS_TEMPLATE), isTemplate ? 1 : 0);

		try {
			documentMeta.put(Integer.toString(CommandConstants.DOCUMENT_NAME), URLEncoder.encode(container.getDocName(), "UTF-8").replace("+", "%20"));
			if(!userZuid.contains("$")){
				documentMeta.put(Integer.toString(CommandConstants.IS_FAVORITE), DocumentUtils.isFavoriteSpreadSheet(container.getResourceId(), userZuid));
			}
		} catch (Exception ex) {
			LOGGER.log(Level.WARNING, "Exception occured while getting shareMeta", ex);
		}

		boolean isDocOwner = ClientUtils.isDocumentOwner(docOwnerZUID, userZuid);
		documentMeta.put(Integer.toString(CommandConstants.ISDOCOWNER), isDocOwner ? 1 : 0);

		boolean isScratchDoc = container.isRemoteMode();
		documentMeta.put(Integer.toString(CommandConstants.ISSCRATCH), isScratchDoc ? 1 : 0);

		updateLastModifiedTime(container, documentMeta, -1);
	}

	public static void getShareMeta(JSONObjectWrapper documentMeta, JSONObjectWrapper previlege, String rangeId) {
		WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();
		String docsSpaceID = container.getDocsSpaceId();
		String creatorZuid = container.getCreatorZUID();

		UserProfile.AccessType accessIdentity = CurrentRealm.getAccessIdentity();

		UserProfile uProfile = CurrentRealm.getUserProfile();
		String userZuid = uProfile.getZUserId();
		boolean canWrite = (previlege != null) && previlege.getBoolean("canEdit"); // No i18N
		boolean canComment = (previlege != null) && previlege.getBoolean("canCreateComment"); // No i18N
		boolean isAnnon = DocumentUtils.isAnonUser(userZuid);
		boolean isNonZohoUser = false;
		HttpServletRequest request = IAMUtil.getCurrentRequest();
		if (request.getAttribute(ZFSNGConstants.IS_NON_ZOHO_USER) != null) {
			isNonZohoUser = (boolean) request.getAttribute(ZFSNGConstants.IS_NON_ZOHO_USER);
		}
		try {
			JSONObjectWrapper sharePubMeta = new JSONObjectWrapper();

			JSONObjectWrapper pubMeta = DocumentUtils.getPublicResourceMeta(container, rangeId);
			if(pubMeta != null){

				sharePubMeta.put(Integer.toString(CommandConstants.PUBLISH), pubMeta);

				if(accessIdentity == UserProfile.AccessType.PUBLIC_EXTERNAL || accessIdentity == UserProfile.AccessType.PUBLIC_ORG ||
					accessIdentity == UserProfile.AccessType.RANGE_PUBLIC_EXTERNAL || accessIdentity == UserProfile.AccessType.RANGE_PUBLIC_ORG ||
					accessIdentity == UserProfile.AccessType.SHEET_PUBLIC_EXTERNAL || accessIdentity == UserProfile.AccessType.SHEET_PUBLIC_ORG) {

					boolean showFormula = (Boolean) pubMeta.get(Integer.toString(CommandConstants.SHOW_FORMULAS));
					boolean allowExport = (Boolean) pubMeta.get(Integer.toString(CommandConstants.ALLOW_EXPORT));
					boolean allowInteraction = (Boolean) pubMeta.get(Integer.toString(CommandConstants.ALLOW_INTERACTIONS));
					boolean hideGridlines = (Boolean) pubMeta.get(Integer.toString(CommandConstants.HIDE_GRID));
					boolean hideHeaders = (Boolean) pubMeta.get(Integer.toString(CommandConstants.HIDE_HEADERS));
					boolean hideFormulabar = (Boolean) pubMeta.get(Integer.toString(CommandConstants.HIDE_FORMULABAR));

					documentMeta.put(Integer.toString(CommandConstants.SHOW_FORMULAS), showFormula);
					documentMeta.put(Integer.toString(CommandConstants.ALLOW_EXPORT), allowExport);
					documentMeta.put(Integer.toString(CommandConstants.ALLOW_INTERACTIONS), allowInteraction);
					documentMeta.put(Integer.toString(CommandConstants.HIDE_GRID), hideGridlines);
					documentMeta.put(Integer.toString(CommandConstants.HIDE_HEADERS), hideHeaders);
					documentMeta.put(Integer.toString(CommandConstants.HIDE_FORMULABAR), hideFormulabar);

					if(pubMeta.has(Integer.toString(CommandConstants.PUBLISH_DATE))) {
						documentMeta.put(Integer.toString(CommandConstants.PUBLISH_DATE), pubMeta.get(Integer.toString(CommandConstants.PUBLISH_DATE)));
					}
					container.setShowFormulas(showFormula);
				}
			}

			if (!isAnnon || (isNonZohoUser && canWrite)) {
				JSONObjectWrapper shareMeta;
				if (canWrite || canComment) {
					// if public meta is not null then it is public view so dont have to add share meta
					shareMeta = DocumentUtils.getShareResourceMeta(container.getResourceId(), docsSpaceID, userZuid);
				} else {
					shareMeta = new JSONObjectWrapper();
					shareMeta.put("isShared", true);
				}
				sharePubMeta.put(Integer.toString(CommandConstants.SHARE_INFO), shareMeta);
			}

			documentMeta.put(Integer.toString(CommandConstants.SHARE_META), sharePubMeta);

		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Exception occured while getting shareMeta", e);
		}
	}

	public static void getLockedInfo(JSONObjectWrapper documentMeta) {
		WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();
		String resourceId = container.getResourceId();
		String ownerZuid = container.getDocsSpaceId();

		try {
			HashMap<String, Object> lockedInfo = ZohoFS.getLockedResourceInfo(ownerZuid, resourceId);
			if (lockedInfo != null) {
				String lockedBy = (String) lockedInfo.get("LOCKED_BY");
				documentMeta.put(Integer.toString(CommandConstants.LOCKED_BY), DocumentUtils.getZFullName(lockedBy));
				documentMeta.put(Integer.toString(CommandConstants.LOCKED_BY_ZUID), lockedBy);
			}
		} catch (Exception e) {
			LOGGER.info("Exception while getting resource info : " + e);
			// TODO Auto-generated catch block
		}
	}

	public static void getLocaleMeta(JSONObjectWrapper documentMeta) {
		WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();
		try {
			WorkbookAdditionalInfo additionalInfo = container.getWorkbookAdditionalInfo();
			Locale locale = additionalInfo.getSpreadsheetSettings().getLocale();

			documentMeta.put(Integer.toString(CommandConstants.LANGUAGE), locale.getLanguage());
			documentMeta.put(Integer.toString(CommandConstants.COUNTRY), locale.getCountry());

			char thousandSeparator = new DecimalFormatSymbols(locale).getGroupingSeparator();
			char decimalSeparator = new DecimalFormatSymbols(locale).getDecimalSeparator();
			documentMeta.put(Integer.toString(CommandConstants.THOUSAND_SEPARATOR), String.valueOf(thousandSeparator));
			documentMeta.put(Integer.toString(CommandConstants.DECIMAL_SEPARATOR), String.valueOf(decimalSeparator));
			documentMeta.put(Integer.toString(CommandConstants.DATE_SEPARATOR), Character.toString((char) StyleActionUtil.getDateSeparator(locale)));

			String dateFormatType = LocaleUtil.getDateFormatType(locale).toString();
			documentMeta.put(Integer.toString(CommandConstants.LOCALE_DATEFORMAT_TYPE), dateFormatType);

		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Exception occured while getting localeMeta", e);
		}

	}

	public static JSONObjectWrapper getDocumentMeta(JSONObjectWrapper previlege, String rangeId, boolean isPublishedView) {

		JSONObjectWrapper documentMeta = new JSONObjectWrapper();

		UserProfile uProfile = CurrentRealm.getUserProfile();
		UserProfile.AccessType accessIdentity = CurrentRealm.getAccessIdentity();

		PermissionType pType = uProfile.getPermissionType();

		WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();


//        String docOwnerZUID = container.getCreatorZUID();
		String docOwnerZUID = container.getDocOwnerZUID();
		String docsSpaceID = container.getDocsSpaceId();

		String userZuid = uProfile.getZUserId();

		boolean isDocOwner = ClientUtils.isDocumentOwner(docOwnerZUID, userZuid);

		boolean isDocEditable = (PermissionType.READ == pType || PermissionType.READ_COMMENT == pType) ? false : true;
		if(isPublishedView) {
			try {
				JSONObjectWrapper pubMeta = DocumentUtils.getPublicResourceMeta(container, rangeId);
				isDocEditable = pubMeta.getBoolean(Integer.toString(CommandConstants.ALLOW_INTERACTIONS));
			} catch (Exception e) {
				LOGGER.info("Exception while fetching published meta :: " + e);
			}
		}

		boolean isDocShareable = PermissionType.READ_WRITE_SAVE_SHARE == pType || PermissionType.READ_WRITE_SAVE_SHARE_DELETE == pType || (previlege != null && previlege.getBoolean("canShare")); //No I18N
		boolean isCommentable = (PermissionType.READ == pType) ? false : true;
		boolean isScratchDoc = container.isRemoteMode();
		boolean isDocOrgPublishable = previlege != null && previlege.getBoolean("canOrgPublish");
		boolean isDocExternalPublishable = previlege != null && previlege.getBoolean("canPublish");

		boolean isTemplate = false;
		try {
			isTemplate = container.isTemplate();
		} catch (Exception e1) {
			LOGGER.log(Level.WARNING, "Exception occured while getting isTemplate :: ", e1);
		}
		try {
			documentMeta.put(Integer.toString(CommandConstants.DOCUMENT_NAME), URLEncoder.encode(container.getDocName(), "UTF-8").replace("+", "%20"));
		} catch (UnsupportedEncodingException e) {
			LOGGER.log(Level.WARNING, "Exception occured while getting shareMeta", e);
		}

		documentMeta.put(Integer.toString(CommandConstants.ISEDITABLE), isDocEditable ? 1 : 0);
		documentMeta.put(Integer.toString(CommandConstants.ISSHARABLE), isDocShareable ? 1 : 0);
		documentMeta.put(Integer.toString(CommandConstants.IS_ORG_PUBLISHABLE), isDocOrgPublishable ? 1 : 0);
		documentMeta.put(Integer.toString(CommandConstants.IS_EXTERNAL_PUBLISHABLE), isDocExternalPublishable ? 1 : 0);
		documentMeta.put(Integer.toString(CommandConstants.IS_TEMPLATE), isTemplate ? 1 : 0);
		documentMeta.put(Integer.toString(CommandConstants.ISDOCOWNER), isDocOwner ? 1 : 0);
		documentMeta.put(Integer.toString(CommandConstants.ISCOMMENTABLE), isCommentable ? 1 : 0);

		if (isScratchDoc) {
			documentMeta.put(Integer.toString(CommandConstants.ISSCRATCH), 1);
		}

		try {

			if(!DocumentUtils.isAnonUser(userZuid)){
				documentMeta.put(Integer.toString(CommandConstants.IS_FAVORITE), DocumentUtils.isFavoriteSpreadSheet(container.getResourceId(), userZuid));
			}

			WorkbookAdditionalInfo additionalInfo = container.getWorkbookAdditionalInfo();
			Locale locale = additionalInfo.getSpreadsheetSettings().getLocale();

			// TODO - SHIRU REMOVE BELOW CODES IF LATEST RESPONSE IS HANDLED

			documentMeta.put(Integer.toString(CommandConstants.LANGUAGE), locale.getLanguage());
			documentMeta.put(Integer.toString(CommandConstants.COUNTRY), locale.getCountry());
			documentMeta.put(Integer.toString(CommandConstants.THOUSAND_SEPARATOR), String.valueOf(additionalInfo.getSpreadsheetSettings().getThousandSeparator()));
			documentMeta.put(Integer.toString(CommandConstants.DECIMAL_SEPARATOR), String.valueOf(additionalInfo.getSpreadsheetSettings().getDecimalSeparator()));
			documentMeta.put(Integer.toString(CommandConstants.DATE_SEPARATOR), Character.toString((char) StyleActionUtil.getDateSeparator(locale)));

			DateUtil.DateFormatType dateFormatType = additionalInfo.getSpreadsheetSettings().getDateInputFormat();
			documentMeta.put(Integer.toString(CommandConstants.DATE_FORMAT), dateFormatType.toString());
			// MOBILE APP - SHIRU
			documentMeta.put(Integer.toString(CommandConstants.LOCALE_DATEFORMAT_TYPE), dateFormatType.toString());

		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Exception occured while getting documentMeta", e);
		}

		// documentMeta.put(Integer.toString(CommandConstants.DOCUMENT_NAME),CurrentRealm.getContainer().getDocName());
		//  documentMeta.put(Integer.toString(CommandConstants.DOCUMENT_ID),CurrentRealm.getContainer().getDocId());
		// documentMeta.put(Integer.toString(CommandConstants.RESOURCE_ID),CurrentRealm.getContainer().getResourceId());
		return documentMeta;
	}

	public static void updateLastModifiedTime(WorkbookContainer container, JSONObjectWrapper documentMeta, long lastSavedActionId)
	{
		String docsSpaceID = container.getDocsSpaceId();

		if (!container.isNewDoc() && docsSpaceID != null && !docsSpaceID.equals("-1")) {

			String lastActionJsonStr = null;
			try
			{
				if(lastSavedActionId == -1){
					lastSavedActionId = container.getLastSavedActionId();
				}

				lastActionJsonStr = RedisHelper.hget(RedisHelper.ACTIONS_LIST + container.getResourceKey(), String.valueOf(lastSavedActionId));
			} catch (Exception e) {
				LOGGER.log(Level.WARNING, "RESOURCE_ID: {0} Exception occured while getting actionList {1}", new Object[]{container.getResourceKey(), e});
			}

			long lastSavedTime = (lastActionJsonStr != null) ? Long.valueOf(new JSONObjectWrapper(lastActionJsonStr).getString(JSONConstants.TIME_STAMP)) : DocumentUtils.getLastSavedTime(docsSpaceID, container.getResourceKey());
			documentMeta.put(Integer.toString(CommandConstants.LAST_SAVED_TIME), lastSavedTime);

		}

	}
	public static JSONArrayWrapper getSheetNameList(Workbook workbook, boolean isTransparentTabColorStringAccepted) {
		Sheet sheets[] = workbook.getSheets();
		JSONArrayWrapper sheetNames = new JSONArrayWrapper();
		JSONArrayWrapper tempSheetNames = new JSONArrayWrapper();

		for (int i = 0; i < sheets.length; i++) {
//            if (sheets[i].isHidden()) {
//                continue;
//            }
			tempSheetNames.put(0, sheets[i].getAssociatedName());
			tempSheetNames.put(1, sheets[i].getName());
			//Sheet Positions
			tempSheetNames.put(i);
			//We can send tableStylename too, but avoiding as of now tabcolor is done, later we can go with tabcolor
			String tabColor = sheets[i].getSheetStyleReadOnly().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook);
			if(!isTransparentTabColorStringAccepted && tabColor != null && tabColor.equals(ZSModelConstants.TRANSPARENT))
			{
				tabColor = "";
			}
			tempSheetNames.put(tabColor);

			tempSheetNames.put(sheets[i].isHidden());

			sheetNames.put(tempSheetNames);
			tempSheetNames = new JSONArrayWrapper();
		}

		return sheetNames;
	}

	public static JSONArrayWrapper getSheetInfo(Workbook workbook, String sheetName, boolean isTransparentTabColorStringAccepted) {
		Sheet sheets[] = workbook.getSheets();
		JSONArrayWrapper sheetNames = new JSONArrayWrapper();
		JSONArrayWrapper tempSheetNames = new JSONArrayWrapper();

		for (int i = 0; i < sheets.length; i++) {
//            if (sheets[i].isHidden()) {
//                continue;
//            }
			if(sheets[i].getName().equals(sheetName)) {
				tempSheetNames.put(0, sheets[i].getAssociatedName());
				tempSheetNames.put(1, sheets[i].getName());
				//Sheet Positions
				tempSheetNames.put(0);
				//We can send tableStylename too, but avoiding as of now tabcolor is done, later we can go with tabcolor

				String tabColor = sheets[i].getSheetStyleReadOnly().getPropertyAsString_deep(SheetStyle.Property.TABCOLOR, workbook);
				if(!isTransparentTabColorStringAccepted && tabColor != null && tabColor.equals(ZSModelConstants.TRANSPARENT))
				{
					tabColor = "";
				}
				tempSheetNames.put(tabColor);
				tempSheetNames.put(sheets[i].isHidden());

				sheetNames.put(tempSheetNames);
				break;
			}
		}

		return sheetNames;
	}


	public static JSONObjectWrapper getSheetMeta(Workbook workbook) {

		JSONObjectWrapper sheetJson = new JSONObjectWrapper();

		JSONArrayWrapper sheetNames = getSheetNameList(workbook, false);
		sheetJson.put(Integer.toString(CommandConstants.META), sheetNames);

		Sheet activeSheeet = null;
		if (workbook.getActiveSheetName() != null) {
			activeSheeet = workbook.getSheet(workbook.getActiveSheetName());
		} else {
			activeSheeet = workbook.getSheet(0);
		}
		sheetJson.put(Integer.toString(CommandConstants.ACTIVESHEET_NAME), activeSheeet.getName());
		return sheetJson;
	}

	public static Map getMergeCellsList(Sheet sheet, int startRow, int startCol, int endRow, int endCol) {
		Map<String, JSONArrayWrapper> mergeMap = new HashMap<>();
		JSONArrayWrapper mergeCellsAry = new JSONArrayWrapper();
		JSONArrayWrapper mergeAcrossAry = new JSONArrayWrapper();

		try {
			int colRepeat;
			Cell cell;

			//ReadOnlyRow readOnlyRow;
			// Row              rowObj;

			JSONArrayWrapper tempCellsAry = new JSONArrayWrapper();
			int rowRepeat;
			int usedRow = sheet.getRowNum() - 1;
			int usedCol = sheet.getColNum() - 1;

			endRow = Math.min(endRow, usedRow);
			endRow = Math.min(endRow, Utility.MAXNUMOFROWS - 1);

			endCol = Math.min(endCol, usedCol);
			endCol = Math.min(endCol, Utility.MAXNUMOFCOLS - 1);        //For import spreadSheets which is having more than supported used.

			int[][] mergeAcrossTemp = new int[endCol - startCol + 1][4];
			BitSet mergeAcrossTempPresence = new BitSet(endCol - startCol + 1);

			int rowSpan, colSpan;
			for(DataRange spanRange : sheet.getMergeCellDetails().values())
			{
				int rowIndex = spanRange.getStartRowIndex();
				int colIndex = spanRange.getStartColIndex();
				if(rowIndex >= startRow && rowIndex <= endRow && colIndex >= startCol && colIndex <= endCol) {
					rowSpan = spanRange.getRowSize();
					colSpan = spanRange.getColSize();

					if (rowSpan > 1)
					{
						tempCellsAry.put(0, rowIndex);
						tempCellsAry.put(1, colIndex);
						tempCellsAry.put(2, rowIndex + rowSpan - 1);
						tempCellsAry.put(3, colIndex + colSpan - 1);

						mergeCellsAry.put(tempCellsAry);

					}
					else if (colSpan > 1)
					{
						if (!mergeAcrossTempPresence.get(colIndex - startCol)) {
							// No previous records in current column
							mergeAcrossTempPresence.set(colIndex - startCol);
							mergeAcrossTemp[colIndex - startCol][0] = rowIndex;
							mergeAcrossTemp[colIndex - startCol][1] = colIndex;
							mergeAcrossTemp[colIndex - startCol][2] = rowIndex + rowSpan - 1;
							mergeAcrossTemp[colIndex - startCol][3] = colIndex + colSpan - 1;
						} else {
							if (mergeAcrossTemp[colIndex - startCol][2] + 1 == rowIndex && mergeAcrossTemp[colIndex - startCol][1] == colIndex && mergeAcrossTemp[colIndex - startCol][3] == colIndex + colSpan - 1) {
								mergeAcrossTemp[colIndex - startCol][2]++;
							} else {
								tempCellsAry.put(0, mergeAcrossTemp[colIndex - startCol][0]);
								tempCellsAry.put(1, mergeAcrossTemp[colIndex - startCol][1]);
								tempCellsAry.put(2, mergeAcrossTemp[colIndex - startCol][2]);
								tempCellsAry.put(3, mergeAcrossTemp[colIndex - startCol][3]);
								if (mergeAcrossTemp[colIndex - startCol][2] == mergeAcrossTemp[colIndex - startCol][0]) {
									mergeCellsAry.put(tempCellsAry);
								} else {
									mergeAcrossAry.put(tempCellsAry);
								}

								mergeAcrossTemp[colIndex - startCol][0] = rowIndex;
								mergeAcrossTemp[colIndex - startCol][1] = colIndex;
								mergeAcrossTemp[colIndex - startCol][2] = rowIndex + rowSpan - 1;
								mergeAcrossTemp[colIndex - startCol][3] = colIndex + colSpan - 1;

							}
						}
					}
					tempCellsAry = new JSONArrayWrapper();

				}
			}
			int bitIndex = mergeAcrossTempPresence.nextSetBit(0);
			while (bitIndex >= 0 && (bitIndex + startCol) <= endCol) {
				tempCellsAry = new JSONArrayWrapper();
				tempCellsAry.put(0, mergeAcrossTemp[bitIndex][0]);
				tempCellsAry.put(1, mergeAcrossTemp[bitIndex][1]);
				tempCellsAry.put(2, mergeAcrossTemp[bitIndex][2]);
				tempCellsAry.put(3, mergeAcrossTemp[bitIndex][3]);

				if (mergeAcrossTemp[bitIndex][2] == mergeAcrossTemp[bitIndex][0]) {
					mergeCellsAry.put(tempCellsAry);
				} else {
					mergeAcrossAry.put(tempCellsAry);
				}

				bitIndex = mergeAcrossTempPresence.nextSetBit(bitIndex + 1);
			}
		} catch (Exception e) {
			// TODO: handle exception
			LOGGER.log(Level.WARNING, "Exception occured while getting mergeCellsList", e);
		}

		mergeMap.put("mergeCellAry", mergeCellsAry);
		mergeMap.put("mergeAcrossAry", mergeAcrossAry);
		return mergeMap;
	}

	private static JSONArrayWrapper constructJSONArray(int rowIndex, int rowsRepeated)
	{
		JSONArrayWrapper array = new JSONArrayWrapper();
		array.put(0, rowIndex);
		array.put(1, rowIndex + rowsRepeated > MAXNUMOFROWS ? rowsRepeated + (MAXNUMOFROWS - (rowsRepeated + rowIndex)) : rowsRepeated);
		return array;
	}

	private static JSONArrayWrapper constructColJSONArray(int colIndex, int colsRepeated)
	{
		JSONArrayWrapper array = new JSONArrayWrapper();
		array.put(0, colIndex);
		array.put(1, colIndex + colsRepeated > MAXNUMOFCOLS ? colsRepeated + (MAXNUMOFCOLS - (colsRepeated + colIndex)) : colsRepeated);
		return array;
	}

	private static JSONArrayWrapper constructJSONArray(String ...args)
	{
		JSONArrayWrapper array = new JSONArrayWrapper();
		for(String s : args)
		{
			array.put(s);
		}
		return array;
	}

	private static JSONArrayWrapper constructJSONArray(int index, int repeated, String style, boolean isForRows)
	{
		JSONArrayWrapper array = isForRows ? constructJSONArray(index, repeated) : constructColJSONArray(index, repeated);
		array.put(2, style);
		return array;
	}

	public static JSONArrayWrapper getRows(Sheet sheet, int startRowIndex, int endRowIndex, RowVisibility visibility)
	{
		Iterator<ReadOnlyRow> rows = RowIterator.getInstance(sheet, startRowIndex, endRowIndex, visibility);
		JSONArrayWrapper rowsArray = new JSONArrayWrapper();

		int lastRowIndex = -1;
		int lastRowsRepeated = -1;
		String lastRowStyle = null;
		int lastMaxHeightColIndex = -1;
		while(rows.hasNext())
		{
			ReadOnlyRow row = rows.next();
			int currRowIndex = row.getRowIndex();
			int currRowsRepeated = (currRowIndex + row.getRowsRepeated() - 1) > endRowIndex ? (endRowIndex - currRowIndex + 1) : row.getRowsRepeated();
			String currRowStyle = row.getRow() != null ? row.getRow().getStyleName() : EngineConstants.DEFAULT_ROWSTYLENAME;
			int currLastMaxHeightColIndex = row.getRow() != null ? row.getRow().getMaxHeightColIndex() : -1;
			if(lastRowStyle != null && lastRowIndex + lastRowsRepeated == currRowIndex && lastRowStyle.equals(currRowStyle) && lastMaxHeightColIndex == currLastMaxHeightColIndex)
			{
				lastRowsRepeated += currRowsRepeated;
			}
			else
			{
				if(lastRowStyle != null)
				{
					JSONArrayWrapper rowArray = constructJSONArray(lastRowIndex, lastRowsRepeated, lastRowStyle, true);
					rowsArray.put(rowArray);
				}

				lastRowIndex = currRowIndex;
				lastRowsRepeated = currRowsRepeated;
				lastRowStyle = currRowStyle;
				lastMaxHeightColIndex = currLastMaxHeightColIndex;
			}
		}

		if(lastRowStyle != null)
		{
			JSONArrayWrapper rowArray = constructJSONArray(lastRowIndex, lastRowsRepeated, lastRowStyle, true);
			rowsArray.put(rowArray);
		}

		return rowsArray;
	}

	public static JSONArrayWrapper getRowsNew(Sheet sheet, int startRowIndex, int endRowIndex, RowVisibility visibility)
	{
		Iterator<ReadOnlyRow> rows = RowIterator.getInstance(sheet, startRowIndex, endRowIndex, visibility);
		JSONArrayWrapper rowsArray = new JSONArrayWrapper();

		int lastRowIndex = -1;
		int lastRowsRepeated = -1;
		String lastRowStyle = null;
		int lastMaxHeightColIndex = -1;
		while(rows.hasNext())
		{
			ReadOnlyRow row = rows.next();
			int currRowIndex = row.getRowIndex();
			int currRowsRepeated = row.getRowsRepeated();
			String currRowStyle = row.getRow() != null ? row.getRow().getStyleName() : EngineConstants.DEFAULT_ROWSTYLENAME;
			int currLastMaxHeightColIndex = row.getRow() != null ? row.getRow().getMaxHeightColIndex() : -1;
			if(lastRowStyle != null && lastRowIndex + lastRowsRepeated == currRowIndex && lastRowStyle.equals(currRowStyle) && lastMaxHeightColIndex == currLastMaxHeightColIndex)
			{
				lastRowsRepeated += currRowsRepeated;
			}
			else
			{
				if(lastRowStyle != null)
				{
					JSONArrayWrapper rowArray = constructJSONArray(lastRowIndex, lastRowsRepeated, lastRowStyle, true);
					rowArray.put(lastMaxHeightColIndex);
					rowsArray.put(rowArray);
				}

				lastRowIndex = currRowIndex;
				lastRowsRepeated = currRowsRepeated;
				lastRowStyle = currRowStyle;
				lastMaxHeightColIndex = currLastMaxHeightColIndex;
			}
		}

		if(lastRowStyle != null)
		{
			JSONArrayWrapper rowArray = constructJSONArray(lastRowIndex, lastRowsRepeated, lastRowStyle, true);
			rowArray.put(lastMaxHeightColIndex);
			rowsArray.put(rowArray);
		}

		return rowsArray;
	}
	/**
	 *
	 * @param sheet
	 * @param startRowIndex
	 * @param endRowIndex
	 * @return
	 */
	public static JSONArrayWrapper getVisibleRows(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		return getRows(sheet, startRowIndex, endRowIndex, RowVisibility.VISIBLE);
	}

	/**
	 *
	 * @param sheet
	 * @param startRowIndex
	 * @param endRowIndex
	 * @return
	 */
	public static JSONArrayWrapper getRows(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		return getRows(sheet, startRowIndex, endRowIndex, null);
	}

	/**
	 *
	 * @param startRow
	 * @param endRow
	 * @return
	 */
	public static JSONArrayWrapper getHiddenRows(Sheet sheet, int startRow, int endRow)
	{
		JSONArrayWrapper hiddenRowsArray = new JSONArrayWrapper();
		int usedRow = sheet.getRowNum();
		usedRow = Math.max(usedRow, endRow);
		usedRow = Math.min(usedRow, Utility.MAXNUMOFROWS - 1);
		Iterator<ReadOnlyRow> hiddenRows = sheet.getHiddenRows(startRow, usedRow);
		while(hiddenRows.hasNext())
		{
			ReadOnlyRow row = hiddenRows.next();
			hiddenRowsArray.put(constructJSONArray(row.getRowIndex(), row.getRowsRepeated()));
		}
		return hiddenRowsArray;
	}

	public static JSONArrayWrapper getUnHiddenRows(Sheet sheet, int startRow, int endRow)
	{
		int rowRepeat = 1;
		int tempRowIndex = startRow;
		Row row;
		String styleName = "";
		String currStyleName = "";
		JSONArrayWrapper unhiddenRowAry = new JSONArrayWrapper();

		for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
			rowRepeat = 1;

			if (!RowUtil.isFilteredRow(sheet, rowIndex)) {
				row = sheet.getReadOnlyRowFromShell(rowIndex).getRow();
				if (row != null) {
					styleName = row.getStyleName();
				} else {
					break;
				}
				JSONArrayWrapper unhiddenRows = new JSONArrayWrapper();
				for (tempRowIndex = rowIndex + 1; (tempRowIndex <= endRow) && !RowUtil.isFilteredRow(sheet, tempRowIndex);) {
					row = sheet.getReadOnlyRowFromShell(tempRowIndex).getRow();

					if (row != null) {
						currStyleName = row.getStyleName();
					} else {
						break;
					}
					if (!currStyleName.equals(styleName)) {
						break;
					}
					rowRepeat++;
					tempRowIndex++;
				}

				unhiddenRows.put(rowIndex);
				unhiddenRows.put(rowRepeat);
				unhiddenRows.put(styleName);
				unhiddenRowAry.put(unhiddenRows);
				rowIndex = tempRowIndex - 1;
			}
		}
		return unhiddenRowAry;
	}

	public static JSONObjectWrapper getHeadersDataStructure(String sheetName, JSONArrayWrapper headerAry, boolean addHeaders) {
		JSONObjectWrapper headerJson = new JSONObjectWrapper();

		int actionCommand = addHeaders ? CommandConstants.ADD : CommandConstants.MODIFY;

		JSONObjectWrapper metaDefinitionObj = new JSONObjectWrapper();
		metaDefinitionObj.put(Integer.toString(CommandConstants.META_DEFINITION), headerAry);

		headerJson.put(Integer.toString(CommandConstants.ASSOCIATED_SHEET_NAME), sheetName);

		headerJson.put(Integer.toString(actionCommand), metaDefinitionObj);

		return headerJson;
	}

	public static JSONObjectWrapper getHiddenDataStructure(String sheetName, JSONArrayWrapper hiddenAry) {
		JSONObjectWrapper hiddenJson = new JSONObjectWrapper();

		hiddenJson.put(Integer.toString(CommandConstants.ASSOCIATED_SHEET_NAME), sheetName);
		hiddenJson.put(Integer.toString(CommandConstants.META_DEFINITION), hiddenAry);

		return hiddenJson;
	}
	//not getting hidecols withing this range, it returns untill the usedcol
	public static JSONArrayWrapper getHiddenColumns(Sheet sheet, int startCol, int endCol) {
		// based on active sheet name
		JSONArrayWrapper hiddenColumns = new JSONArrayWrapper();

		int colRepeat;
		JSONArrayWrapper tempHiddenColAry = new JSONArrayWrapper();
		int usedCol = sheet.getColNum();
		usedCol = Math.max(usedCol, endCol);
		usedCol = Math.min(usedCol, Utility.MAXNUMOFCOLS - 1);  // used col shows more than 256 in this case

		ReadOnlyColumnHeader readOnlyColumnHeader;
		ColumnHeader columnHeader;

		for (int i = startCol; i <= usedCol;)
		{
			readOnlyColumnHeader = sheet.getReadOnlyColumnHeader(i);
			columnHeader = readOnlyColumnHeader.getColumnHeader();

			colRepeat = readOnlyColumnHeader.getColsRepeated();

			if (colRepeat + i > usedCol)
			{
				colRepeat = Utility.MAXNUMOFCOLS - i;
			}

			if (ColumnUtil.isHiddenColumn(columnHeader))
			{
				int colIndex = columnHeader.getColumn().getColumnIndex();
				//Send col repeat for this
				tempHiddenColAry.put(0, colIndex);
				tempHiddenColAry.put(1, colRepeat);
				hiddenColumns.put(tempHiddenColAry);
				tempHiddenColAry = new JSONArrayWrapper();
			}

			i += colRepeat;
		}

		// hiddenColJson.put(Integer.toString(CommandConstants.ASSOCIATED_SHEET_NAME), sheet.getAssociatedName());
		// hiddenColJson.put(Integer.toString(CommandConstants.HIDDEN_COLUMNS_ARY), hiddenColumns);
		return hiddenColumns;
	}

	public static JSONArrayWrapper getHiddenColumnsWithinRange(Sheet sheet, int startCol, int endCol) {
		// based on active sheet name
		JSONArrayWrapper hiddenColumns = new JSONArrayWrapper();

		int colRepeat;
		JSONArrayWrapper tempHiddenColAry = new JSONArrayWrapper();

		ReadOnlyColumnHeader readOnlyColumnHeader;
		ColumnHeader columnHeader;

		for (int i = startCol; i <= endCol;) {
			readOnlyColumnHeader = sheet.getReadOnlyColumnHeader(i);
			columnHeader = readOnlyColumnHeader.getColumnHeader();

			colRepeat = readOnlyColumnHeader.getColsRepeated();
			if (colRepeat + i > endCol) {
				colRepeat = (endCol - i) + 1;
			}
			if (ColumnUtil.isHiddenColumn(columnHeader))
			{
				int colIndex = columnHeader.getColumn().getColumnIndex();
				//Send col repeat for this
				tempHiddenColAry.put(0, colIndex);
				tempHiddenColAry.put(1, colRepeat);
				hiddenColumns.put(tempHiddenColAry);
				tempHiddenColAry = new JSONArrayWrapper();
			}

			i += colRepeat;
		}

		return hiddenColumns;
	}
	public static JSONArrayWrapper getHiddenRowsWithingRange(Sheet sheet, int startRow, int endRow)
	{
		JSONArrayWrapper hiddenRowsArray = new JSONArrayWrapper();
		Iterator<ReadOnlyRow> hiddenRows = RowUtil.getHiddenRows(sheet, startRow, endRow);
		while(hiddenRows.hasNext())
		{
			ReadOnlyRow row = hiddenRows.next();
			int rowIndex = row.getRowIndex();
			int rowsRepeated = row.getRowsRepeated() + rowIndex > endRow ? endRow - rowIndex + 1 : row.getRowsRepeated();
			hiddenRowsArray.put(constructJSONArray(rowIndex, rowsRepeated));
		}
		return hiddenRowsArray;
		// based on active sheet name


//        int rowRepeat;
//        JSONArrayWrapper tempHiddenArray = new JSONArrayWrapper();
//
//        ReadOnlyRow readOnlyRow;
//        Row row;
//
//        for (int i = startRow; i <= endRow;) {
//            readOnlyRow = sheet.getReadOnlyRow(i);
//            row = readOnlyRow.getRow();
//
//            rowRepeat = readOnlyRow.getRowsRepeated();
//
//            if (rowRepeat + i > endRow) {
//                 rowRepeat = (endRow -i)+1 ;
//            }
//
//            if(RowUtil.isHiddenRow(row))
//            {
//                //Send row repeat for this
//                tempHiddenArray.put(0, readOnlyRow.getRowIndex());
//                tempHiddenArray.put(1, rowRepeat);
//                hiddenRows.put(tempHiddenArray);
//                tempHiddenArray = new JSONArrayWrapper();
//
//            }
//
//            i += rowRepeat;
//        }
//        return hiddenRows;
	}
	public static JSONObjectWrapper getRowStyles(Workbook wb) {

		JSONObjectWrapper rowsData = new JSONObjectWrapper();
		Map<String, RowStyle> rowStyleMap = wb.getRowStyleMap();
		for (String key : rowStyleMap.keySet()) {
			RowStyle className = rowStyleMap.get(key);
			if(className.getPropertyAsString_deep(RowStyle.Property.ROWHEIGHT, wb) != null) {
				rowsData.put(className.getStyleName(), EngineUtils1.convertToPixels(className.getPropertyAsString_deep(RowStyle.Property.ROWHEIGHT, wb), EngineConstants.ROWDPI));
			} else {
				rowsData.put(className.getStyleName(), wb.getDefaultRowHeight());
			}
		}

		// We are getting this row style name which is not available, so hardcoded.
		rowsData.put(EngineConstants.DEFAULT_ROWSTYLENAME, wb.getDefaultRowHeight());
		return rowsData;

	}

	public static JSONObjectWrapper getColStyles(Workbook workbook) {

		JSONObjectWrapper colsData = new JSONObjectWrapper();
		Map<String, ColumnStyle> columnStyleMap = workbook.getColumnStyleMap();

		for (String key : columnStyleMap.keySet()) {
			ColumnStyle className = columnStyleMap.get(key);

			if(className.getPropertyAsString_deep(ColumnStyle.Property.COLUMNWIDTH, workbook) != null) {
				colsData.put(className.getStyleName(), EngineUtils1.convertToPixels(className.getPropertyAsString_deep(ColumnStyle.Property.COLUMNWIDTH, workbook), EngineConstants.COLDPI));
			} else {
				colsData.put(className.getStyleName(), workbook.getDefaultColumnWidth());
			}
		}

		// We are getting this column style name which is not available, so hardcoded.
		colsData.put(EngineConstants.DEFAULT_COLSTYLENAME, workbook.getDefaultColumnWidth());
		return colsData;
	}

	/**
	 *
	 * @param sheet
	 * @param startColIndex
	 * @param endColIndex
	 * @return
	 */
	public static JSONArrayWrapper getVisibleCols(Sheet sheet, int startColIndex, int endColIndex)
	{
		return getCols(sheet, startColIndex, endColIndex, ColumnVisibility.VISIBLE);
	}

	public static JSONArrayWrapper getCols(Sheet sheet, int startRowIndex, int endRowIndex, ColumnVisibility visibility)
	{
		Iterator<ReadOnlyColumnHeader> cols = ColumnIterator.getInstance(sheet, startRowIndex, endRowIndex, visibility);
		JSONArrayWrapper colsArray = new JSONArrayWrapper();

		int lastColIndex = -1;
		int lastColsRepeated = -1;
		String lastColStyle = null;
		while(cols.hasNext())
		{
			ReadOnlyColumnHeader col = cols.next();
			int currColIndex = col.getColIndex();
			int currColsRepeated = col.getColsRepeated();
			String currColStyle = col.getColumnHeader() != null ? col.getColumnHeader().getStyleName() : EngineConstants.DEFAULT_COLSTYLENAME;

			if(lastColStyle != null && lastColIndex + lastColsRepeated == currColIndex && lastColStyle.equals(currColStyle))
			{
				lastColsRepeated += currColsRepeated;
			}
			else
			{
				if(lastColStyle != null)
				{
					colsArray.put(constructJSONArray(lastColIndex, lastColsRepeated, lastColStyle, false));
				}

				lastColIndex = currColIndex;
				lastColsRepeated = currColsRepeated;
				lastColStyle = currColStyle;
			}
		}

		if(lastColStyle != null)
		{
			colsArray.put(constructJSONArray(lastColIndex, lastColsRepeated, lastColStyle, false));
		}

		return colsArray;
	}

	public static JSONArrayWrapper getColHeadDetailsNew(Sheet sheet, int startCol, int endCol)
	{

		JSONArrayWrapper colsDetails = new JSONArrayWrapper();
		JSONArrayWrapper columnsAry = new JSONArrayWrapper();
		endCol++;       // increasing endCol by 1 for actions(for repeat), anyhow we have checked boundary condition in below step
		//  int usedCol = sheet.getColNum();
		// usedCol = Math.min(endCol, usedCol);
		endCol = Math.min(Utility.MAXNUMOFCOLS, endCol); // used col shows more than 256 in this case
		int repeat = 1;
		ReadOnlyColumnHeader readOnlyColumn;
		ColumnHeader colHeader;
		int i;

//		ColumnIterator columnIterator = ColumnIterator.getInstance(sheet, startCol, endCol);
//		while(columnIterator.hasNext())
//		{
//			ReadOnlyColumnHeader readOnlyColumnHeader = columnIterator.next();
//			if(readOnlyColumnHeader.getColumnHeader() != null)
//			{
//				ColumnHeader columnHeader = readOnlyColumnHeader.getColumnHeader();
//
//				int columnIndex = readOnlyColumnHeader.getColIndex();
//				int colsRepeated = readOnlyColumnHeader.getColsRepeated();
//
//				String styleName = columnHeader.getStyleName();
//
//				JSONArrayWrapper columnDetails = new JSONArrayWrapper();
//
//				columnDetails.put(columnIndex);
//				columnDetails.put(colsRepeated);
//				columnDetails.put(styleName);
//				columnDetails.put(columnHeader.getMaxWidthRowIndex());
//
//				columnsAry.put(columnDetails);
//			}
//		}

		for(i = startCol; i < endCol;)
		{
			readOnlyColumn = sheet.getReadOnlyColumnHeader(i);
			repeat = readOnlyColumn.getColsRepeated();
			colHeader = readOnlyColumn.getColumnHeader();
			if(colHeader != null)
			{
				String styleName = colHeader.getStyleName();
				//This check is added to make sure, col repeat should not be grater than endCol
				if(repeat != 1 && repeat + i > endCol)
				{
					repeat = endCol - i;
				}
				colsDetails.put(i);
				colsDetails.put(repeat);

				//Added this check to avoid while getting columnlevel styles while doing actions
				colsDetails.put(styleName);
				colsDetails.put(colHeader.getMaxWidthRowIndex());
				columnsAry.put(colsDetails);
				i += repeat;

			}
			else
			{
				// Assuming after this no column header is created
				break;
				//done to avoid infinite loop
			}
			colsDetails = new JSONArrayWrapper();

		}
		//adding default column styles for rest of columns which are not used as dummy
		if(i < endCol)
		{
			colsDetails = new JSONArrayWrapper();
			colsDetails.put(i);
			colsDetails.put(endCol - i);
			colsDetails.put(EngineConstants.DEFAULT_COLSTYLENAME);
			colsDetails.put(-1);
			columnsAry.put(colsDetails);

		}
		return columnsAry;

	}

	public static JSONArrayWrapper getColHeadDetails(Sheet sheet, int startCol, int endCol)
	{

		JSONArrayWrapper colsDetails = new JSONArrayWrapper();
		JSONArrayWrapper columnsAry = new JSONArrayWrapper();
		endCol++;       // increasing endCol by 1 for actions(for repeat), anyhow we have checked boundary condition in below step
		//  int usedCol = sheet.getColNum();
		// usedCol = Math.min(endCol, usedCol);
		endCol = Math.min(Utility.MAXNUMOFCOLS, endCol); // used col shows more than 256 in this case
		int repeat = 1;
		ReadOnlyColumnHeader readOnlyColumn;
		ColumnHeader colHeader;
		int i;

//		ColumnIterator columnIterator = ColumnIterator.getInstance(sheet, startCol, endCol);
//		while(columnIterator.hasNext())
//		{
//			ReadOnlyColumnHeader readOnlyColumnHeader = columnIterator.next();
//			if(readOnlyColumnHeader.getColumnHeader() != null)
//			{
//				ColumnHeader columnHeader = readOnlyColumnHeader.getColumnHeader();
//
//				int columnIndex = readOnlyColumnHeader.getColIndex();
//				int colsRepeated = readOnlyColumnHeader.getColsRepeated();
//
//				String styleName = columnHeader.getStyleName();
//
//				JSONArrayWrapper columnDetails = new JSONArrayWrapper();
//
//				columnDetails.put(columnIndex);
//				columnDetails.put(colsRepeated);
//				columnDetails.put(styleName);
//				columnDetails.put(columnHeader.getMaxWidthRowIndex());
//
//				columnsAry.put(columnDetails);
//			}
//		}

		for(i = startCol; i < endCol;)
		{
			readOnlyColumn = sheet.getReadOnlyColumnHeader(i);
			repeat = readOnlyColumn.getColsRepeated();
			colHeader = readOnlyColumn.getColumnHeader();
			if(colHeader != null)
			{
				String styleName = colHeader.getStyleName();
				//This check is added to make sure, col repeat should not be grater than endCol
				if(repeat != 1 && repeat + i > endCol)
				{
					repeat = endCol - i;
				}
				colsDetails.put(i);
				colsDetails.put(repeat);

				//Added this check to avoid while getting columnlevel styles while doing actions
				colsDetails.put(styleName);
				columnsAry.put(colsDetails);
				i += repeat;

			}
			else
			{
				// Assuming after this no column header is created
				break;
				//done to avoid infinite loop
			}
			colsDetails = new JSONArrayWrapper();

		}
		//adding default column styles for rest of columns which are not used as dummy
		if(i < endCol)
		{
			colsDetails = new JSONArrayWrapper();
			colsDetails.put(i);
			colsDetails.put(endCol - i);
			colsDetails.put(EngineConstants.DEFAULT_COLSTYLENAME);
			columnsAry.put(colsDetails);

		}
		return columnsAry;

	}

	public static JSONArrayWrapper getColumnLevelCellStyles(Sheet sheet, int startCol, int endCol) {

		JSONArrayWrapper colsDetails = new JSONArrayWrapper();
		JSONArrayWrapper columnsAry = new JSONArrayWrapper();
		//  int                 usedCol         =   sheet.getColNum();
		//  usedCol         =   Math.min(endCol, usedCol);
		endCol = Math.min(Utility.MAXNUMOFCOLS - 1, endCol);        // used col shows more than 256 in this case
		int repeat = 1;

		ReadOnlyColumnHeader readOnlyColumn;
		ColumnHeader colHeader;
		int i;
		for (i = startCol; i <= endCol;) {

			readOnlyColumn = sheet.getReadOnlyColumnHeader(i);
			repeat = readOnlyColumn.getColsRepeated();
			colHeader = readOnlyColumn.getColumnHeader();

//                              if(repeat+i > endCol) {
//                                  repeat          =       endCol - i;
//                                  }
			if (colHeader != null) {
				// colHeader.getCellStyle().getStyleName();
				//  String          styleName                   =       colHeader.getStyleName();
				String colLevelCellStyleName = colHeader.getDefaultCellStyleName();

				colsDetails.put(i);
				colsDetails.put(repeat);

				//Added this check to avoid while getting columnlevel styles while doing actions
				//          colsDetails.put(styleName);
				colsDetails.put(colLevelCellStyleName);

				columnsAry.put(colsDetails);
				i += repeat;
			} else {

				// Assuming after this no column header is created
				break;
				//done to avoid infinite loop
			}
			colsDetails = new JSONArrayWrapper();
		}

		//adding default column styles for rest of columns which are not used as dummy
		if (i < endCol) {
			colsDetails = new JSONArrayWrapper();
			colsDetails.put(i);
			colsDetails.put(endCol - i + 1);
			colsDetails.put(EngineConstants.DEFAULT_CELLSTYLENAME);
			columnsAry.put(colsDetails);
		}

		return columnsAry;
	}

	public static JSONArrayWrapper getRowHeadDetails(Sheet sheet, int startRow, int endRow) {

//        JSONArrayWrapper rowsDetails = new JSONArrayWrapper();
//        JSONArrayWrapper rowsAry = new JSONArrayWrapper();

		String styleName = "";
		endRow++;       // increasing endRow by 1 for actions(for repeat), anyhow we have checked boundary condition in below step
		//  int     usedRow = sheet.getRowNum();
		// usedRow = Math.min(endRow, usedRow);
		endRow = Math.min(Utility.MAXNUMOFROWS, endRow); // For imported documents, having more than supported
		int rowRepeat = 1;
		Row row;

		int rowInd = startRow;
		int count = 1;
		String prevStyleName = "";
		JSONArrayWrapper newRowsDetails = new JSONArrayWrapper();
		JSONArrayWrapper newRowsArray = new JSONArrayWrapper();

		int l;
		for (l = startRow; l < endRow;) {

			ReadOnlyRow readOnlyRow = sheet.getReadOnlyRowFromShell(l);
			row = readOnlyRow.getRow();
			rowRepeat = readOnlyRow.getRowsRepeated();
			if (row != null) {
				styleName = row.getStyleName();
				//This check is added to make sure, row repeat should not be grater than endRow
				if (rowRepeat != 1 && rowRepeat + l > endRow) {
					rowRepeat = endRow - l;
				}

				if(l == startRow){
					prevStyleName = styleName;
					count = rowRepeat;
				} else{
					if(prevStyleName.equals(styleName)){
						count += rowRepeat;
					} else {
						newRowsDetails.put(rowInd);
						newRowsDetails.put(count);
						newRowsDetails.put(prevStyleName);
						newRowsArray.put(newRowsDetails);
						rowInd = l;
						count = rowRepeat;
						prevStyleName = styleName;
						newRowsDetails = new JSONArrayWrapper();
					}
				}

//                rowsDetails.put(l);
//                rowsDetails.put(rowRepeat);
//                rowsDetails.put(styleName);
//                rowsAry.put(rowsDetails);
				l += rowRepeat;
			} else {

				// Assuming after this no row  is created
				//Done to avoid infinite loop
				break;
			}
//            rowsDetails = new JSONArrayWrapper();

		}

		newRowsDetails.put(rowInd);
		newRowsDetails.put(count);
		newRowsDetails.put(prevStyleName);
		newRowsArray.put(newRowsDetails);

		//adding default row styles for rest of rows which are not used as dummy(i.e. for rows which are null.)
		if (l < endRow) {

//            rowsDetails = new JSONArrayWrapper();
//            rowsDetails.put(l);
//            rowsDetails.put(endRow - l);
//            rowsDetails.put(EngineConstants.DEFAULT_ROWSTYLENAME);
//            rowsAry.put(rowsDetails);

			newRowsDetails = new JSONArrayWrapper();
			newRowsDetails.put(l);
			newRowsDetails.put(endRow - l);
			newRowsDetails.put(EngineConstants.DEFAULT_ROWSTYLENAME);
			newRowsArray.put(newRowsDetails);
		}
//        return rowsAry;
		return newRowsArray;

	}
//adding default row styles for rest of rows which are not used as dummy(i.e. for rows which are null.)

//    public static JSONArrayWrapper getFilteredRows(Sheet sheet, FilterRange filterRange) {
//        Range range = filterRange.getRange();
//        int startRow = range.getStartRowIndex();
//        int endRow = range.getEndRowIndex();
//        String visibility;
//        Row row;
//        int rowRepeat = 1;
//        JSONArrayWrapper filteredRowAry = new JSONArrayWrapper();
//        boolean continueRowRepeat = false;
//
//        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
//
//            row = sheet.getReadOnlyRow(rowIndex).getRow();
//
//            if (row != null) {
//                int tempRowIndex = rowIndex;
//                visibility = row.getVisibility();
//
//                if (EngineConstants.VISIBILITY_FILTER.equals(visibility)) {
//
//                    JSONArrayWrapper filteredRows = new JSONArrayWrapper();
//                    if ((tempRowIndex + 1) == rowIndex) {
//                        rowRepeat = rowRepeat + 1;
//                        continueRowRepeat = true;
//                    } else {
//                        continueRowRepeat = false;
//                    }
//
//                    //revisit
//                    if (!continueRowRepeat) {
//                        filteredRows.put(0, tempRowIndex);
//                        filteredRows.put(1, rowRepeat);
//
//                        filteredRowAry.put(filteredRows);
//                    }
//
//                }
//            }
//
//        }
//
//        return filteredRowAry;
//    }

	public static void getFilteredRows(Sheet sheet, int startRow, int endRow, JSONArrayWrapper filteredRowAry)
	{
		JSONArrayWrapper rowAry = getRows(sheet, startRow, endRow, RowVisibility.FILTER);
		for(int i=0; i<rowAry.length(); i++)
		{
			filteredRowAry.put(rowAry.get(i));
		}
//        Iterator<ReadOnlyRow> filteredRows = sheet.getRows(startRow, endRow, RowVisibility.FILTER);
//        while(filteredRows.hasNext())
//        {
//            ReadOnlyRow row = filteredRows.next();
//            if(row.getRow() != null)
//            {
//                filteredRowAry.put(constructJSONArray(row.getRowIndex(), row.getRowsRepeated(), row.getRow().getStyleName(), true));
//            }
//            else
//            {
//                if(row.getRowIndex() >= sheet.getRowNum())
//                {
//                    filteredRowAry.put(constructJSONArray(row.getRowIndex(), endRow - (row.getRowIndex() - 1), EngineConstants.DEFAULT_ROWSTYLENAME, true));
//                    break;
//                }
//                filteredRowAry.put(constructJSONArray(row.getRowIndex(), row.getRowsRepeated(), EngineConstants.DEFAULT_ROWSTYLENAME, true));
//            }
//        }
	}

	/**
	 * @param sheet
	 * <AUTHOR> N J ( ZT-0049 )
	 * @return
	 */
	public static JSONArrayWrapper getFilteredRows(Sheet sheet) {
		if (!sheet.isFilterViewExist()) {
			return new JSONArrayWrapper();
		}
		FilterView filterView = sheet.getFilterView();
		return getRows(sheet, filterView.getStartRowIndex(), filterView.getEndRowIndex(), RowVisibility.FILTER);
//        Iterator<ReadOnlyRow> filteredRows = sheet.getAllFilteredRows();
//    	int endRowIndex = filterView.getEndRowIndex();
//    	while(filteredRows.hasNext())
//    	{
//            ReadOnlyRow row = filteredRows.next();
//            if(row.getRow() != null)
//            {
//                filteredRowAry.put(constructJSONArray(row.getRowIndex(), row.getRowsRepeated(), row.getRow().getStyleName(), true));
//            }
//            else
//            {
//                if(row.getRowIndex() >= sheet.getRowNum())
//                {
//                    filteredRowAry.put(constructJSONArray(row.getRowIndex(), endRowIndex - (row.getRowIndex() - 1), EngineConstants.DEFAULT_ROWSTYLENAME, true));
//                    break;
//                }
//                filteredRowAry.put(constructJSONArray(row.getRowIndex(), row.getRowsRepeated(), EngineConstants.DEFAULT_ROWSTYLENAME, true));
//            }
//    	}
//    	return filteredRowAry;
	}

	/**
	 * Generates column headers with newly updated column limit.
	 * @param sheetJSONMetaData
	 * @return
	 */
	public static JSONArrayWrapper getColHeadersFromCache(JSONObjectWrapper sheetJSONMetaData)
	{
		if(sheetJSONMetaData.has(EngineConstants.COLUMN_HEADERS))
		{
			JSONArrayWrapper ColHeaders = sheetJSONMetaData.getJSONArray(EngineConstants.COLUMN_HEADERS);
			JSONArrayWrapper lastElement = ColHeaders.getJSONArray(ColHeaders.length() - 1);
			int lastCol = lastElement.getInt(0) + lastElement.getInt(1);
			int maxCol = Utility.MAXNUMOFCOLS;
			if(lastCol < maxCol)
			{
				JSONArrayWrapper colsDetails = new JSONArrayWrapper();
				colsDetails.put(lastCol);
				colsDetails.put(maxCol - lastCol);
				colsDetails.put(EngineConstants.DEFAULT_COLSTYLENAME);
				ColHeaders.put(colsDetails);
			}
			return ColHeaders;
		}
		return null;
	}

	/**
	 * Generates row headers with newly updated row limit.
	 * @param sheetJSONMetaData
	 * @return
	 */
	public static JSONArrayWrapper getRowHeadersFromCache(JSONObjectWrapper sheetJSONMetaData)
	{
		if(sheetJSONMetaData.has(EngineConstants.ROW_HEADERS))
		{
			JSONArrayWrapper rowHeaders = sheetJSONMetaData.getJSONArray(EngineConstants.ROW_HEADERS);
			JSONArrayWrapper lastElement = rowHeaders.getJSONArray(rowHeaders.length() - 1);
			int lastRow = lastElement.getInt(0) + lastElement.getInt(1);
			int maxRow = Utility.MAXNUMOFROWS;
			if(lastRow < maxRow)
			{
				JSONArrayWrapper rowDetails = new JSONArrayWrapper();
				rowDetails.put(lastRow);
				rowDetails.put(maxRow - lastRow);
				rowDetails.put(EngineConstants.DEFAULT_ROWSTYLENAME);
				rowHeaders.put(rowDetails);
			}
			return rowHeaders;
		}
		return null;
	}

	public static JSONArrayWrapper getDataValidationRangeList(Sheet sheet, List<DataRange> dataRanges) {

		JSONArrayWrapper dataValidationRangeAry = new JSONArrayWrapper();


		for (DataRange range: dataRanges) {

			Cell topLeftCell = sheet.getCell(range.getStartRowIndex(), range.getStartColIndex());
			ContentValidation contentValidation =  topLeftCell.getContentValidation();
			if(contentValidation == null) {
				LOGGER.log(Level.WARNING, "ContentValidation is null in :: getDataValidationRangeList :: {0} ", new Object[]{range.toString()});
				continue;
			}
			DVErrorMessage errorMsg = contentValidation.getErrorMessage();
			DVHelpMessage helpMsg = contentValidation.getHelpMessage();
			String helpMsgContent = "";
			if (helpMsg != null && helpMsg.isDisplay()) {
				helpMsgContent = helpMsg.getContent();
			}
			String messageType = "";
			String message = "";
			if (errorMsg != null && errorMsg.isDisplay()) {
				messageType = MsgType.ERROR == errorMsg.getMessageType() || MsgType.STOP == errorMsg.getMessageType() ? JSONConstants.INTERRUPT : JSONConstants.WARN;
				message = errorMsg.getContent();
			}
			int rowIndex = range.getStartRowIndex();
			int colIndex = range.getStartColIndex();

			JSONArrayWrapper rangeAry = new JSONArrayWrapper();
			rangeAry.put(range.getStartRowIndex());
			rangeAry.put(range.getStartColIndex());
			rangeAry.put(range.getEndRowIndex());
			rangeAry.put(range.getEndColIndex());
			rangeAry.put(messageType);
			rangeAry.put(message);
			rangeAry.put(helpMsgContent);
			rangeAry.put(contentValidation.getConditionType().name());
			if (contentValidation.getConditionType() == ConditionalFormatOperator.ConditionType.LIST && contentValidation.getDisplayList() != ContentValidation.LIST_DISPLAY_TYPE.NO) {
				boolean isRange = contentValidation.getValueList().get(0) instanceof Expression;
				rangeAry.put(isRange);
				if (isRange) {
					String listStr = contentValidation.getValue(sheet.getWorkbook(), rowIndex, colIndex, ";", true);
					Expression rangeExp = sheet.getWorkbook().getNamedExpression(listStr);
					if (rangeExp == null) {
						rangeExp = new ExpressionImpl(sheet.getWorkbook(), listStr, 0, 0, true, CellReference.ReferenceMode.A1);
					}
					Range cvRangeRef = null;
					try {
						Node o = rangeExp.getNode();
						if (o instanceof ASTRangeNode) {
							cvRangeRef = CellUtil.getRange((ASTRangeNode) o, sheet, rowIndex, colIndex);
						} else if (o instanceof ASTVarNode) {
							try {
								CellReference cellRef = CellUtil.getCellRefFromVarNode((ASTVarNode) o, sheet, rowIndex, colIndex);
								cvRangeRef = new Range(cellRef.getCell().getRow().getSheet(), cellRef, cellRef);
							}
							catch (Exception e) { // If only table name is given, then the node will be ASTVarNode instead of StructRefNode
								for (Sheet tableSheet : sheet.getWorkbook().getSheetList()) {
									Table table = tableSheet.getTable(((ASTVarNode) o).getName());
									if (table != null) {
										cvRangeRef = table.getTableDataRange().toRange(sheet.getWorkbook());
									}
								}
							}

						} else if (o instanceof ASTFunNode) {
							rangeAry.put(listStr);
						}
						else if(o instanceof ASTStructuredReferenceNode) {
							cvRangeRef = ((ASTStructuredReferenceNode)o).getRange(topLeftCell);
						}
					} catch (EvaluationException e) {
					}

					if (cvRangeRef != null) {
						listStr = (listStr.startsWith("=")) ? listStr.substring(1, listStr.length()): listStr ;
//                                JSONObjectWrapper rangeObj = new JSONObjectWrapper();
//                                rangeObj.put("sR", cvRangeRef.getStartRowIndex());
//                                rangeObj.put("sC", cvRangeRef.getStartColIndex());
//                                rangeObj.put("eR", cvRangeRef.getEndRowIndex());
//                                rangeObj.put("eC", cvRangeRef.getEndColIndex());
//                                rangeObj.put("asn", cvRangeRef.getSheet().getAssociatedName());
						rangeAry.put(listStr);
					}
					/////////////////////
				} else {
					String listStr = contentValidation.getValue(sheet.getWorkbook());
					listStr = listStr.substring(1, listStr.length() - 1);
					String[] listItems = listStr.split("\";\"");
					List<String> encodedStrings = new ArrayList<>();
					for(String str : listItems) {
						try {
							encodedStrings.add(URLEncoder.encode(str, "UTF-8").replace("+", "%20")); //NO I18N
						} catch (UnsupportedEncodingException ex) {
							LOGGER.log(Level.INFO, " error in getDataValidationRangeList method ", ex);
						}
					}
					rangeAry.put(new JSONArrayWrapper(encodedStrings));
				}

				rangeAry.put(contentValidation.getDisplayList() == ContentValidation.LIST_DISPLAY_TYPE.SORT_ASCENDING);
			}
			dataValidationRangeAry.put(rangeAry);


		}

		return dataValidationRangeAry;
	}

	public static JSONArrayWrapper getConditionalFormatRangeList(Sheet sheet) {

		JSONArrayWrapper condtionalFormatRangeAry = new JSONArrayWrapper();
		List<Integer> csUIDList = sheet.getConditionalStyleUIDList();
		Map<Integer, ConditionalStyleObject> csMap = sheet.getConditionalStyleMap();
		for (Integer uid : csUIDList) {
			ConditionalStyleObject cso = csMap.get(uid);
			for (DataRange range : cso.getSpecialRange().getRanges()) {
				JSONArrayWrapper rangeAry = new JSONArrayWrapper();
				rangeAry.put(range.getStartRowIndex());
				rangeAry.put(range.getStartColIndex());
				rangeAry.put(range.getEndRowIndex());
				rangeAry.put(range.getEndColIndex());
//                        rangeAry.put(uid);
				condtionalFormatRangeAry.put(rangeAry);
			}
		}

		return condtionalFormatRangeAry;
	}

	public static JSONArrayWrapper getArrayFormulasRangeList(Sheet sheet) {

		Collection<Cell> arrayFormulaCells = sheet.getArrayFormulaCells();
		JSONArrayWrapper arrJson = new JSONArrayWrapper();
		if (arrayFormulaCells != null && !arrayFormulaCells.isEmpty()) {

			for (Cell cell : arrayFormulaCells) {
				int sr = cell.getRowIndex();
				int sc = cell.getColumnIndex();
				int er = sr + cell.getArrayRowSpan() - 1;
				int ec = sc + cell.getArrayColSpan() - 1;
				JSONArrayWrapper detail = new JSONArrayWrapper();
				detail.put(sr);
				detail.put(sc);
				detail.put(er);
				detail.put(ec);
				arrJson.put(detail); // adds array containing [sr, sc, er, ec]
			}
		}

		return arrJson;
	}

	public static boolean checkPermission(CellMetaType cellMetaType, int cellMetaOffsetBit) {

		int cellMeta;

		switch (cellMetaType) {

			case ACTUAL_VALUE:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.ACTUAL_VALUE;
				break;
			case DISPLAY_VALUE:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.DISPLAY_VALUE;
				break;
			case FORMULA:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.FORMULA;
				break;
			case STYLE_NAME:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.STYLE_NAME;
				break;
			case COND_STYLE_NAME:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.COND_STYLE_NAME;
				break;
			case ANNOTATION:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.ANNOTATION;
				break;
			case HLINK:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.HLINK;
				break;
			case TYPE:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.TYPE;
				break;
			case DISCUSSION:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.DISCUSSION;
				break;
			case PATTERN_COLOR:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.PATTERN_COLOR;
				break;
			case PATTERN:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.PATTERN;
				break;
			case IS_CONTENT_VALID:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.IS_CONTENT_VALID;
				break;
			case PICKLIST:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.PICKLIST;
				break;
			case REPEAT_INDEX:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.REPEAT_INDEX;
				break;
			case REPEAT_CHAR:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.REPEAT_CHAR;
				break;
			case CELL_IMAGE:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.CELL_IMAGE;
				break;
			case TYPE_MISMATCH:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.TYPE_MISMATCH;
				break;
			case PICKLIST_ID:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.PICKLIST_ID;
				break;
			case AUTO_ARRAY:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.AUTO_ARRAY;
				break;
			case RICH_STRING:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.RICH_STRING;
				break;

			case INVISIBLE_CHARS:
				cellMeta = com.adventnet.zoho.websheet.model.response.meta.CellMeta.INVISIBLE_CHARS;
				break;

			case MULTI_VALUE:
				cellMeta = CellMeta.MULTI_VALUE;
				break;

            case OUT_OF_SYNC:
                cellMeta = CellMeta.OUT_OF_SYNC;
                break;
			default:
				cellMeta = 0;
				break;
		}

		boolean permission = checkPermission(cellMeta, cellMetaOffsetBit);
		return permission;
	}

	private static boolean checkPermission(int cellMeta, int cellMetaOffsetBit) {
		return (cellMetaOffsetBit & cellMeta) != 0;
	}

	public static JSONArrayWrapper getProtectedRanges(Sheet sheet, com.adventnet.zoho.websheet.model.response.UserInfo userInfo, long externalSharedLinkId) {
		String zuid = userInfo.getZuid();
		PermissionType permissionType = userInfo.getPermissionType();
		List userGroups = userInfo.getUserGroups();


		JSONArrayWrapper lockedRanges = new JSONArrayWrapper();

		Map<Protection, List<DataRange>> protectionRangeMap = sheet.getProtectionRangeMap();
		boolean isExternalShareLinkId = false;

		if(externalSharedLinkId != 0l) {
			isExternalShareLinkId = true;
		}

		for (Protection protection : protectionRangeMap.keySet()) {
			List<DataRange> ranges = protectionRangeMap.get(protection);
			for (DataRange range : ranges) {
				boolean isAuthorised = isExternalShareLinkId ? protection.isAuthorizedExternalShareLink(externalSharedLinkId) : protection.isAuthorized(zuid, userGroups, false);
				if (permissionType == PermissionType.READ_WRITE_SAVE_SHARE || permissionType == PermissionType.READ_WRITE_SAVE_SHARE_DELETE || !isAuthorised) {

					JSONArrayWrapper lockedRangeAry = new JSONArrayWrapper();
					lockedRangeAry.put(0, range.getStartRowIndex());
					lockedRangeAry.put(1, range.getStartColIndex());
					lockedRangeAry.put(2, range.getEndRowIndex());
					lockedRangeAry.put(3, range.getEndColIndex());

					lockedRangeAry.put(4, !isAuthorised ? 1 : 0);
					lockedRangeAry.put(5, protection.isAllowInsert() ? 1 : 0);
					lockedRangeAry.put(6, protection.isAllowFormats() ? 1 : 0);

					lockedRanges.put(lockedRangeAry);
				}
			}
		}
		return lockedRanges;
	}

	public static JSONArrayWrapper getAllProtectedSheets(Workbook workbook, UserInfo userInfo, long externalLinkId) {

		JSONArrayWrapper jsonArray = new JSONArrayWrapper();
		String id = userInfo.getZuid();
		PermissionType permissionType = userInfo.getPermissionType();
		List userGroups = userInfo.getUserGroups();

		boolean isExternalShareLinkId = false;

		if(externalLinkId != 0l) {
			isExternalShareLinkId = true;
		}
		Sheet[] sheets = workbook.getSheets();
		boolean isLocked;
		int length = sheets.length;
		JSONArrayWrapper sheetAry;
		for (int i = 0; i < length; i++) {
			isLocked = isExternalShareLinkId ? sheets[i].isLocked(externalLinkId) : sheets[i].isLocked(id, userGroups);
			if (sheets[i].isZProtected() && (permissionType == PermissionType.READ_WRITE_SAVE_SHARE || permissionType == PermissionType.READ_WRITE_SAVE_SHARE_DELETE || isLocked)) {

				sheetAry = new JSONArrayWrapper();
				sheetAry.put(sheets[i].getAssociatedName());
				sheetAry.put(isLocked ? 1 : 0);
				jsonArray.put(sheetAry);
			}
		}
		return jsonArray;
	}

	public static JSONObjectWrapper getPivotRanges(Workbook workbook, PivotTable pivotTable) {

		JSONObjectWrapper pivotObj = new JSONObjectWrapper();
		DataRange targetRange = null;
		DataRange sourceRange = null;

		if (pivotTable != null) {
			try {
				boolean error = false;
				boolean isImport = false;
				//JSONObjectWrapper pivotObj = new JSONObjectWrapper();
				targetRange = pivotTable.getTargetCellRange();
				sourceRange = pivotTable.getSourceCellRange();


                 /*pivotObj.put(JSONConstants.ASSOCIATED_SHEET_NAME, destinationRange.getSheet().getAssociatedName());
                   pivotObj.put(JSONConstants.START_ROW, destinationRange.getStartRowIndex());
                   pivotObj.put(JSONConstants.START_COLUMN, destinationRange.getStartColIndex());
                   pivotObj.put(JSONConstants.END_ROW, destinationRange.getEndRowIndex());
                   pivotObj.put(JSONConstants.END_COLUMN, destinationRange.getEndColIndex());*/
				//updating Target range
				JSONArrayWrapper rangeAry = new JSONArrayWrapper();
				rangeAry.put(targetRange.getAssociatedSheetName());
				rangeAry.put(targetRange.getStartRowIndex());
				rangeAry.put(targetRange.getStartColIndex());
				rangeAry.put(targetRange.getEndRowIndex());
				rangeAry.put(targetRange.getEndColIndex());

				pivotObj.put(Integer.toString(CommandConstants.TARGET), rangeAry);

                 /* pivotObj.put(JSONConstants.ASSOCIATED_SOURCE_SHEET_NAME, sourceRange.getSheet().getAssociatedName());
                   pivotObj.put(JSONConstants.SOURCE_START_ROW, sourceRange.getStartRowIndex());
                   pivotObj.put(JSONConstants.SOURCE_END_ROW, sourceRange.getEndRowIndex());
                   pivotObj.put(JSONConstants.SOURCE_START_COLUMN, sourceRange.getStartColIndex());
                   pivotObj.put(JSONConstants.SOURCE_END_COLUMN, sourceRange.getEndColIndex());*/

				//Updating Source range
				JSONArrayWrapper allFields = new JSONArrayWrapper();
				if (sourceRange != null) {
					rangeAry = new JSONArrayWrapper();
					rangeAry.put(sourceRange.getAssociatedSheetName());
					rangeAry.put(sourceRange.getStartRowIndex());
					rangeAry.put(sourceRange.getStartColIndex());
					rangeAry.put(sourceRange.getEndRowIndex());
					rangeAry.put(sourceRange.getEndColIndex());
					pivotObj.put(Integer.toString(CommandConstants.SOURCE), rangeAry);
				}
				pivotObj.put(Integer.toString(CommandConstants.NAME), pivotTable.getSourceName());
				allFields = ActionUtil.getPivotFields(workbook, pivotTable);

				List<PivotField> pivotFields = pivotTable.getPivotFields();
				JSONArrayWrapper page = new JSONArrayWrapper();
				JSONArrayWrapper row = new JSONArrayWrapper();
				JSONArrayWrapper column = new JSONArrayWrapper();
				JSONArrayWrapper data = new JSONArrayWrapper();
				JSONArrayWrapper filteredFields = new JSONArrayWrapper();
				JSONArrayWrapper sortedFields = new JSONArrayWrapper();
				String sourceFieldName = "";
				//String function = "";
				JSONArrayWrapper function = new JSONArrayWrapper();
				boolean isNumFieldExist = false;
				JSONArrayWrapper rowGroupMap = new JSONArrayWrapper();
				JSONArrayWrapper colGroupMap = new JSONArrayWrapper();
				JSONArrayWrapper pageGroupMap = new JSONArrayWrapper();
				JSONArrayWrapper dataGroupMap = new JSONArrayWrapper();
				HashSet<Integer> hiddenFilterIndices = new HashSet<>();
				for(PivotField pivotField : pivotFields){
					if(pivotField.getColRangeType() == PivotUtil.FieldType.Td && pivotField.getOrientation().equals(Orientation.HIDDEN) && pivotField.getPivotLevel().getIsSpecialFilterApplied()){
						hiddenFilterIndices.add(pivotField.getColIndex());
					}
				}
				for (PivotField pivotField : pivotFields) {

					int headerIndex = 0;
					sourceFieldName = pivotField.getSourceFieldName();
					//LOGGER.info("pivotField "+sourceFieldName+" : "+pivotField.getIsDataLayoutField());
					// if(sourceFieldName.equals("") || Boolean.valueOf(pivotField.getIsDataLayoutField()) )
					if (pivotField.getOrientation().equals(Orientation.SLICER) || sourceFieldName.equals("")) {
						continue;
					}
					int colIndex = -2;
					// if(pivotField.isDummy())
					if (Boolean.valueOf(pivotField.getIsDataLayoutField())) {
						colIndex = -1;
					} else {
						JSONObjectWrapper field = null;
						for (int i = 0; i < allFields.length(); i++) {
							field = allFields.getJSONObject(i);
							/*
							 * Decoding string check is modified bcz,
							 * While decoding field content,if field header contains "+" char it got removed
							 * so colIndex was not updated properly in pivot response for these fields
							 */
//                           if (Utility.getDecodedString(field.getString("content")).equals(sourceFieldName)) {
                             if (PivotUtil.checkIfFieldNameInFieldObj(field, sourceFieldName)) {
								colIndex = field.getInt("colIndex");   //NO I18N
								break;
							}
						}

						if (colIndex == -2) {
							error = true;
							continue;
						}

						if (field!=null && field.getString("type") =="Td"  && pivotField.getUsedHierarchy()>-1) {
							field.put(JSONConstants.GROUP, pivotField.getUsedHierarchy());
						}
						if (field!=null && field.getString("type") =="Tn" && pivotField.getOrientation() != Orientation.DATA && pivotField.getOrientation() != Orientation.PAGE) {
							isNumFieldExist = true;
							if(pivotField.getPivotGroups() != null) {
								PivotGroups  pivotGroups = pivotField.getPivotGroups();
								String groupStart = pivotGroups.getStart();
								String groupEnd = pivotGroups.getEnd();
								double groupStep = pivotGroups.getStep();
								field.put(JSONConstants.PIVOT_GROUP_MIN, groupStart);
								field.put(JSONConstants.PIVOT_GROUP_MAX, groupEnd);
								field.put(JSONConstants.PIVOT_GROUP_RANGE_SIZE, groupStep);
							}
						}
					}

					//LOGGER.log(Level.INFO, "{0} field: {1}", new Object[]{colIndex, field});
					Orientation orientation = pivotField.getOrientation();
					// LOGGER.info("orientation: "+orientation);
					if (orientation == Orientation.PAGE) {
						headerIndex = page.length();
						page.put(colIndex);
						pageGroupMap.put(pivotField.getUsedHierarchy());
					} else if (orientation == Orientation.ROW) {
						headerIndex = row.length();
						row.put(colIndex);
						rowGroupMap.put(pivotField.getUsedHierarchy());
					} else if (orientation == Orientation.COLUMN) {
						headerIndex = column.length();
						column.put(colIndex);
						colGroupMap.put(pivotField.getUsedHierarchy());
					} else if (orientation == Orientation.DATA) {
						//function = pivotField.getFunction().toString().toLowerCase();
						String pivotFieldRef = "none"; //NO I18N
						if (pivotField.getPivotFieldReference() != null) {
							pivotFieldRef = pivotField.getPivotFieldReference().getType().toString().toLowerCase();
							pivotFieldRef = pivotFieldRef.replace("_", "-");
						}
						JSONObjectWrapper funObj = new JSONObjectWrapper();
						funObj.set(pivotField.getFunction().toString().toLowerCase(), pivotFieldRef);
						function.put(funObj);
						//function.put(pivotField.getFunction().toString().toLowerCase());
						data.put(colIndex);
						dataGroupMap.put(pivotField.getUsedHierarchy());
					}
					if (pivotField.getIsDataLayoutField() == null) {
						String sortMode = pivotField.getPivotLevel().getPivotSortInfo().getSortMode().toString();
						if (!sortMode.equalsIgnoreCase(SortMode.MANUAL.toString()) && orientation != Orientation.DATA && orientation != Orientation.PAGE) {

							int dataFieldIndex = pivotField.getPivotLevel().getPivotSortInfo().getDataFieldIndex();
							boolean ascendingOrder = (pivotField.getPivotLevel().getPivotSortInfo().getOrder().equals(Order.ASCENDING)) ? true : false;
							JSONObjectWrapper sortJson = new JSONObjectWrapper();

							//LOGGER.info(sortMode+" Datafield111: "+pivotField.getPivotLevel().getPivotSortInfo().getDatafield()+" dataFieldIndex: "+dataFieldIndex);
							if (sortMode.equalsIgnoreCase(SortMode.DATA.toString()) && dataFieldIndex == -1) {
								//LOGGER.info(" Datafield: "+pivotField.getPivotLevel().getPivotSortInfo().getDatafield());
								isImport = true;
								sortJson.put("isFieldIndexNeed", true);//NO I18N
								//sortJson.put("sortMode", sortMode);
								String dataField = pivotField.getPivotLevel().getPivotSortInfo().getDatafield();
								sortJson.put("Datafield", dataField == null ? "" : dataField);//NO I18N
							} else {
								sortJson.put("isFieldIndexNeed", false);//NO I18N
							}

							sortJson.put(JSONConstants.HEADER_INDEX, headerIndex);
							sortJson.put(JSONConstants.ASCENDING_SORT, ascendingOrder);
							sortJson.put(JSONConstants.PIVOT_FIELD_TYPE, orientation.toString().toLowerCase());
							sortJson.put(JSONConstants.PIVOT_DATAFIELDINDEX, dataFieldIndex);
							sortedFields.put(sortJson);
						}
						if (pivotField.getPivotLevel().getIsFilterApplied() || hiddenFilterIndices.contains(pivotField.getColIndex())) {
							JSONObjectWrapper filterJson = new JSONObjectWrapper();
							filterJson.put(JSONConstants.HEADER_INDEX, headerIndex);
							filterJson.put(JSONConstants.PIVOT_FIELD_TYPE, orientation.toString().toLowerCase());
							filteredFields.put(filterJson);
						} else if (pivotField.getPivotLevel().getPivotDisplayInfo().isEnabled()) {
							JSONObjectWrapper filterJson = new JSONObjectWrapper();
							filterJson.put(JSONConstants.HEADER_INDEX, headerIndex);
							filterJson.put(JSONConstants.PIVOT_FIELD_TYPE, orientation.toString().toLowerCase());
							filteredFields.put(filterJson);
						}
					}

				}
				JSONObjectWrapper numericRangeInfo = null;
				PivotComponent pivotComponent = pivotTable.getPivotComponent();
				if (pivotComponent != null) {
					if (pivotComponent.getPivotTargetGrid() != null) {
						numericRangeInfo = pivotComponent.getPivotTargetGrid().getNumericRangeInfo();
					}

				} else if (isNumFieldExist) {
					pivotComponent = PivotUtil.getPivotCompObj(workbook.getSheetByAssociatedName(targetRange.getAssociatedSheetName()), pivotTable.getName());
					pivotComponent.projectPivotModel();
					numericRangeInfo = pivotComponent.getPivotTargetGrid().getNumericRangeInfo();
				}

				if (numericRangeInfo != null) {
					pivotObj.put(JSONConstants.PIVOT_NUMERIC_RANGE_DETAILS, numericRangeInfo);
				}
				//pivotComponent.

				if (isImport) {
					//LOGGER.info("isImport : "+isImport);
					for (int i = 0; i < sortedFields.length(); i++) {
						JSONObjectWrapper sortJson = sortedFields.getJSONObject(i);
						boolean isFieldIndexNeed = sortJson.getBoolean("isFieldIndexNeed");//NO I18N

						//LOGGER.info("isFieldIndexNeed : "+isFieldIndexNeed);
						if (isFieldIndexNeed) {
							int imDataFieldIndex = 0;
							String dataField = sortJson.getString("Datafield");
							if (!dataField.equals("")) {
								//	LOGGER.info("dataField is not null: "+dataField);
								JSONObjectWrapper field = new JSONObjectWrapper();
								int dIndex = 0;
								for (int j = 0; j < allFields.length(); j++) {
									field = allFields.getJSONObject(j);
									if (Utility.getDecodedString(field.getString("content")).equals(dataField)) {
										dIndex = field.getInt("colIndex");   //NO I18N
										break;
									}
								}
								//LOGGER.info("dataIndex position: "+dIndex);
								for (int k = 0; k < data.length(); k++) {
									if (dIndex == data.getInt(k)) {
										//	LOGGER.info("PIVOT_DATAFIELDINDEX position: "+k);
										imDataFieldIndex = k;
										sortJson.put(JSONConstants.PIVOT_DATAFIELDINDEX, imDataFieldIndex);

									}
								}
							} else {
								//LOGGER.info("dataField is null: "+dataField);
								sortJson.put(JSONConstants.PIVOT_DATAFIELDINDEX, imDataFieldIndex);
							}
							sortJson.remove("isFieldIndexNeed");//NO I18N
							sortJson.remove("Datafield");//NO I18N
							int headerIndex = sortJson.getInt(JSONConstants.HEADER_INDEX);
							int colIndex = -1;
							if (sortJson.get(JSONConstants.PIVOT_FIELD_TYPE).equals("row")) {
								colIndex = row.getInt(headerIndex);
							} else if (sortJson.get(JSONConstants.PIVOT_FIELD_TYPE).equals("column")) {
								colIndex = column.getInt(headerIndex);
							}
							JSONObjectWrapper field = new JSONObjectWrapper();
							String fieldString = null;
							for (int j = 0; j < allFields.length(); j++) {
								field = allFields.getJSONObject(j);
								if (colIndex != -1 && field.getInt("colIndex") == colIndex) {
									fieldString = field.getString("content");//NO I18N
									break;
								}
							}
							if (fieldString != null) {
								for (PivotField pivotField : pivotFields) {
									sourceFieldName = pivotField.getSourceFieldName();
									if (Utility.getDecodedString(fieldString).equals(sourceFieldName)) {
										String sortMode = pivotField.getPivotLevel().getPivotSortInfo().getSortMode().toString();
										int dataFieldIndex = pivotField.getPivotLevel().getPivotSortInfo().getDataFieldIndex();

										//      	LOGGER.info(sortMode+" Datafield111: "+pivotField.getPivotLevel().getPivotSortInfo().getDatafield()+" dataFieldIndex: "+dataFieldIndex);
										if (sortMode.equalsIgnoreCase(SortMode.DATA.toString()) && dataFieldIndex == -1 && pivotField.getPivotLevel().getPivotSortInfo().getDatafield().equals(dataField)) {
											pivotField.getPivotLevel().getPivotSortInfo().setDataFieldIndex(imDataFieldIndex);
										}
										break;
									}
								}
							}

						}
					}
				}
				if (error) {
					//LOGGER.log(Level.WARNING, "pivot field does not exists in the source range :: field name : {0}", sourceFieldName);
//                       throw new Exception();
					pivotObj.put(JSONConstants.IS_SOURCE_CHANGED, true);
				}

				if(pivotComponent != null && pivotComponent.getPivotTargetGrid() != null) {
					JSONObjectWrapper pts = pivotComponent.getPivotTargetGrid().getPivotTemplateSetting();
					pts.put("pageFilterStartRow", targetRange.getStartRowIndex());
					pivotObj.put(JSONConstants.PIVOT_TEMPLATE_SETTING, pts);
				}
				JSONObjectWrapper groupMap = new JSONObjectWrapper();
				groupMap.put(JSONConstants.ROW_FIELDS,rowGroupMap);
				groupMap.put(JSONConstants.COLUMN_FIELDS,colGroupMap);
				groupMap.put(JSONConstants.PAGE_FIELDS,pageGroupMap);
				groupMap.put(JSONConstants.DATA_FIELDS,dataGroupMap);
				pivotObj.put(JSONConstants.GROUP_MAP, groupMap);
				pivotObj.put(JSONConstants.FUNCTION, function);
				pivotObj.put(JSONConstants.PAGE_FIELDS, page);
				pivotObj.put(JSONConstants.ROW_FIELDS, row);
				pivotObj.put(JSONConstants.COLUMN_FIELDS, column);
				pivotObj.put(JSONConstants.DATA_FIELDS, data);
				pivotObj.put(JSONConstants.ALL_FIELDS, allFields);
				pivotObj.put(JSONConstants.ID, pivotTable.getName());
				pivotObj.put(JSONConstants.PIVOT_NAME, pivotTable.getDisplayName());

				//List<String> buttons = pivotTable.getButtons();
				// LOGGER.info("buttons: "+buttons);
				JSONArrayWrapper buttonRanges = new JSONArrayWrapper();
				for(DataRange buttonRange : pivotTable.getButtonRanges()) {
					JSONArrayWrapper buttonRangeAry = new JSONArrayWrapper();
					buttonRangeAry.put(buttonRange.getAssociatedSheetName());
					buttonRangeAry.put(buttonRange.getStartRowIndex());
					buttonRangeAry.put(buttonRange.getStartColIndex());
					buttonRangeAry.put(buttonRange.getEndRowIndex());
					buttonRangeAry.put(buttonRange.getEndColIndex());
					buttonRanges.put(buttonRangeAry);

				}
				pivotObj.put(Integer.toString(CommandConstants.ICONSET), buttonRanges);
				pivotObj.put(Integer.toString(CommandConstants.PIVOT_ICONSET), pivotTable.getPivotIconSet().toArray());
				pivotObj.put(JSONConstants.PIVOT_BUTTONS, pivotTable.getButtonsAsString());
				pivotObj.put(JSONConstants.PIVOTTEMPLATE_ID, pivotTable.getStyleName());
				pivotObj.put(JSONConstants.PIVOT_FILTER_DETAILS, filteredFields);
				pivotObj.put(JSONConstants.PIVOT_SORT_DETAILS, sortedFields);
				pivotObj.put(JSONConstants.SUBTOTALOPTION, pivotTable.isSubTotalON());
				pivotObj.put(JSONConstants.IS_REPEAT_LABEL,pivotTable.isRepeatLabelEnabled());
				if (pivotTable.isOldVersion()) {
					pivotObj.put(JSONConstants.SUBTOTALOPTION, true);
				}
				GrandTotal gTotal = pivotTable.getGrandTotal();
				if (gTotal == null) {
					pivotObj.put(JSONConstants.ROWGRANDTOTALOPTION, true);
					pivotObj.put(JSONConstants.COLGRANDTOTALOPTION, true);
				} else {
					switch (gTotal) {
						case ROW:
							pivotObj.put(JSONConstants.ROWGRANDTOTALOPTION, true);
							pivotObj.put(JSONConstants.COLGRANDTOTALOPTION, false);
							break;
						case COLUMN:
							pivotObj.put(JSONConstants.ROWGRANDTOTALOPTION, false);
							pivotObj.put(JSONConstants.COLGRANDTOTALOPTION, true);
							break;
						case NONE:
							pivotObj.put(JSONConstants.ROWGRANDTOTALOPTION, false);
							pivotObj.put(JSONConstants.COLGRANDTOTALOPTION, false);
							break;
						default:
						case BOTH:
							pivotObj.put(JSONConstants.ROWGRANDTOTALOPTION, true);
							pivotObj.put(JSONConstants.COLGRANDTOTALOPTION, true);
							break;
					}
				}
				pivotObj.put(JSONConstants.AUTOREFRESH, pivotTable.isAutoRefresh());
				pivotObj.put(JSONConstants.HIDE_ERRORS, pivotTable.isHideErrors());
				pivotObj.put(JSONConstants.AUTOEXPAND, pivotTable.isAutoExpand());
				pivotObj.put(JSONConstants.PIVOT_FAULTY, pivotTable.getPivotFaultyType());

			}catch(Exception e) {
				LOGGER.info("targetRange "+targetRange+" sourceRange: "+sourceRange );
				LOGGER.log(Level.WARNING, "[NEWCLIENT] Exception occurec while checking Pivot Info", e);
				return null;
			}

		}

		return pivotObj;
	}

	public static boolean checkForImageChartsButtons(Workbook book) {

		boolean isContainsChart = false;
		boolean isContainsImage = false;
		boolean isContainsButtons = false;
		try {
			isContainsChart = ChartUtils.isChartAvailableInWorkbook(book);
			WorkbookContainer container = CurrentRealm.getContainerWithoutTrace();

			if (!isContainsChart) {
				DataObject imagesDO = ImageUtils.getImagesDO(Long.parseLong(container.getDocId()), container.getDocOwner());
				Iterator imgIter = imagesDO.getRows(SHEETIMAGESDFSSTORE.TABLE);
				isContainsImage = imgIter.hasNext();
			}

			if (!isContainsChart && !isContainsImage) {
				Sheet[] sheets = book.getSheets();
				for (int i = 0; i < sheets.length; i++) {
					Sheet sheet = sheets[i];
					if (!isContainsButtons) {
						//any sheet contains form control(i.e. buttons)
						Forms forms = sheet.getForms();
						List<Form> formList = null;
						Form formObj, formElement = null;
						DrawControl drawcontrol = null;
						if (forms != null) {
							formList = forms.getFormList();
						}
						if (formList != null && !isContainsButtons) {
							for (int j = 0; j < formList.size(); j++) {
								formObj = (Form) formList.get(j);
								for (int k = 0; k < formObj.getFormElementList().size(); k++) {
									formElement = formObj.getFormElementList().get(k);
									if (formElement.isRenderable()) {
										drawcontrol = sheet.getDrawControl(formElement.getId());
										if (drawcontrol != null) {
											isContainsButtons = true;
											break;
										}
									}
								}
							}

						}
					}
				}
			}
		} catch (Exception ex) {
			LOGGER.log(Level.INFO, "[NEWCLIENT] Exception occurec while checking image buttons charts ", ex);
		}

		return isContainsChart || isContainsImage || isContainsButtons;
	}
//if (imageBeanList != null && !imageBeanList.isEmpty()) {
//                for (int i = 0; i < imageBeanList.size(); i++) {
//                    ImageBean imageBean = (ImageBean)imageBeanList.get(i);
//                }
//            }

//    public static JSONArrayWrapper getImageBook(Workbook workbook, List<ImageBean> imageBeanList) {
//    public static JSONArrayWrapper getImageBook(Workbook workbook,ImageBean imageBean) {
//
//        JSONArrayWrapper imageJsonArray = new JSONArrayWrapper();
//        WorkbookContainer wbc = CurrentRealm.getContainer();
//        try {
//            String docId = wbc.getDocId();
//            String docOwner = wbc.getDocOwner();
//            Persistence pers = SheetPersistenceUtils.getPersistence(docOwner);
//            SelectQuery sql = new SelectQueryImpl(new Table("SheetImages"));
//            sql.addSelectColumn(new Column(null, "*"));
////            if (imageBeanList != null && !imageBeanList.isEmpty()) {
////                for (int i = 0; i < imageBeanList.size(); i++) {
////                    ImageBean imageBean = (ImageBean) imageBeanList.get(i);
//                    String sheetName = workbook.getSheetByAssociatedName(imageBean.getSheetName()).getName();
//                    String doc_sheet_id = GraphUtils.getDocumentSheetID(Long.parseLong(docId), sheetName, docOwner);
//
//                    Criteria cri = new Criteria(new Column("SheetImages", "DOCUMENTSHEET_ID"), doc_sheet_id, QueryConstants.EQUAL);
//                    sql.setCriteria(cri);
//                    DataObject dataObject = pers.get(sql);
//                    if (dataObject != null & !dataObject.isEmpty()) {
//                        Iterator rowsSheetImage = dataObject.getRows("SheetImages");
//                        while (rowsSheetImage.hasNext()) {
//                            JSONObjectWrapper tempObject = new JSONObjectWrapper();
//                            com.adventnet.persistence.Row rowSheetImage = (com.adventnet.persistence.Row) rowsSheetImage.next();
//                            Long seqNum = (Long) rowSheetImage.get("SEQUENCE_NUMBER");
//                            Long imgId = (Long) rowSheetImage.get("IMAGE_ID");
//                            getSheetImageUtil(tempObject, rowSheetImage);
//
//                            Iterator rowsImage = getImageDoWithId(pers, sql, imgId);
//                            com.adventnet.persistence.Row rowImage = (com.adventnet.persistence.Row) rowsImage.next();
//                            getImageUtil(tempObject, rowImage);
//
//                            String extn = (String) rowImage.get("IMAGE_EXTENSION");
//                            String fileName = imgId.toString() + extn;
//
//                            boolean isRemoteView = false;
//                            String encryptedUN = ClientUtils.getEncryptedValue(wbc.getDocOwner(), false);
//                            encryptedUN = java.net.URLEncoder.encode(encryptedUN, "UTF-8"); // No I18N
//                            UserProfile uProfile = CurrentRealm.getUserProfile();
//                            String remoteEncDocId = null;
//
//                            String uploadedFileUrl = DocumentUtils.getDocumentContextPath(CurrentRealm.getAccessIdentity(), null) + CurrentRealm.getContainerIdentity() + "?proxyURL=imagedisplay&i_n=" + fileName + "&u_n=" + encryptedUN + "&s_n=" + seqNum + "&isR=" + isRemoteView + "&optype=" + 1 + "&RMNAME=" + uProfile.getZUserId() + "&doc=" + remoteEncDocId; // No I18N
//
//                            tempObject.put("DOCUMENT", docId);
//                            tempObject.put("SHEET", sheetName);
//                            tempObject.put("IMG_URL", uploadedFileUrl);
//                            tempObject.put("IMGID", String.valueOf(imgId));
//                            tempObject.put("SEQ_NUM", String.valueOf(seqNum));
//                            tempObject.put("IMGEXTN", extn);
//                            imageJsonArray.put(tempObject);
//                        }
//                    }
	////                }
	////            }
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return imageJsonArray;
//    }
//
//    public static JSONObjectWrapper getImage(Workbook workbook, ImageBean imageBean) {
//
//        JSONObjectWrapper tempObject = new JSONObjectWrapper();
//        try {
//            String sheetName = workbook.getSheetByAssociatedName(imageBean.getSheetName()).getName();
//            String docOwner = imageBean.getDocOwner();
//            String seqNum = imageBean.getSeqNum();
//            Persistence pers = SheetPersistenceUtils.getPersistence(docOwner); //not able to avoid this in loop
//            SelectQuery sql = new SelectQueryImpl(new Table("SheetImages"));
//            sql.addSelectColumn(new Column(null, "*"));
//            Criteria cri = new Criteria(new Column("SheetImages", "SEQUENCE_NUMBER"), seqNum, QueryConstants.EQUAL);
//            sql.setCriteria(cri);
//            DataObject dataObject = pers.get(sql);
//
//            if (dataObject != null && !dataObject.isEmpty()) {
//
//                Iterator rowsSheetImage = dataObject.getRows("SheetImages");
//                com.adventnet.persistence.Row rowSheetImage = (com.adventnet.persistence.Row) rowsSheetImage.next();
//
//                Long imgId = (Long) rowSheetImage.get("IMAGE_ID");
//                getSheetImageUtil(tempObject, rowSheetImage);
//
//                Iterator rowsImage = getImageDoWithId(pers, sql, imgId);
//                com.adventnet.persistence.Row rowImage = (com.adventnet.persistence.Row) rowsImage.next();
//                getImageUtil(tempObject, rowImage);
//
//                        String extn = (String) rowImage.get("IMAGE_EXTENSION");
//
//                        tempObject.put("SHEET", sheetName);
//                        tempObject.put("IMGID", String.valueOf(imgId));
//                        tempObject.put("SEQ_NUM", String.valueOf(seqNum));
//                        tempObject.put("IMGEXTN", extn);
//                        String imageUrl = URLDecoder.decode(imageBean.getImgUrl());
//                        tempObject.put("IMG_URL", imageUrl);
//			String dummyId		= imageBean.getDummyId();
//               		 if(dummyId != null && !dummyId.isEmpty()){
//            			tempObject.put("DUMMY_ID", dummyId);
//            		}
//                    }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return tempObject;
//    }
//
//    public static void getSheetImageUtil(JSONObjectWrapper imageObject, com.adventnet.persistence.Row rowSheetImage) {
//
//        Long left = (Long) rowSheetImage.get("IMAGE_X_POSITION");
//        Long top = (Long) rowSheetImage.get("IMAGE_Y_POSITION");
//        Long width = (Long) rowSheetImage.get("IMAGE_WIDTH");
//        Long height = (Long) rowSheetImage.get("IMAGE_HEIGHT");
//
//        imageObject.put("LEFT", left);
//        imageObject.put("TOP", top);
//        imageObject.put("WIDTH", width);
//        imageObject.put("HEIGHT", height);
//
//    }
//
//    public static void getImageUtil(JSONObjectWrapper imageObject, com.adventnet.persistence.Row rowImage) {
//
//        String imgName = (String) rowImage.get("IMAGE_NAME");
//        String imgUrl = (String) rowImage.get("IMAGE_URL");
//        int origH = ((Long) rowImage.get("ACTUAL_HEIGHT")).intValue();
//        int origW = ((Long) rowImage.get("ACTUAL_WIDTH")).intValue();
//
//        imageObject.put("IMGNAME", ClientUtils.replaceSpclChars(imgName));
//        imageObject.put("IMG_EXT_URL", imgUrl);
//        imageObject.put("ACT_WIDTH", origW);
//        imageObject.put("ACT_HEIGHT", origH);
//    }
//
//    public static Iterator getImageDoWithId(Persistence pers, SelectQuery sql, Long imgId) {
//        Iterator rowsImage = null;
//        try {
//            SelectQuery sqlImage = new SelectQueryImpl(new Table("Images"));
//            sqlImage.addSelectColumn(new Column(null, "*"));
//            Criteria criteria = new Criteria(new Column("Images", "IMAGE_ID"), imgId, QueryConstants.EQUAL);
//            sqlImage.setCriteria(criteria);
//            DataObject dataObjectImage = pers.get(sqlImage);
//            rowsImage = dataObjectImage.getRows("Images");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return rowsImage;
//    }
	public static List<RangeWrapper> convertConditionalStrtoRangeWrappers(String associatedSheetname, String dataRange) {
		List<RangeWrapper> rangeWrappers = new ArrayList<>();
		try {

			String datarange = Utility.convertExcelStyleFormulaToOO(dataRange, SpreadsheetSettings.defaultSpreadsheetSettings);
			String[] rangeStr = datarange.split(";");

			String tempRangeStr;
			for (String ranStr : rangeStr) {
				// NOTE: Parsing range alone from string
				String[] str = ranStr.trim().split("\\.");
				if (str.length < 2) {
					// SheetName passed in Range "Sheet1.A2:F3" case
					tempRangeStr = ranStr;
				} else {
					// SheetName not passed in Range "A2:F3" case
					tempRangeStr = str[1];
				}

				RangeUtil.SheetRange sheetRange = RangeUtil.getSheetRange(tempRangeStr);
				RangeWrapper rangeWrapper = new RangeWrapper(associatedSheetname, sheetRange.getStartRowIndex(), sheetRange.getStartColIndex(), sheetRange.getEndRowIndex(), sheetRange.getEndColIndex());
				rangeWrappers.add(rangeWrapper);
			}
		} catch (SheetEngineException e) {
			throw new IllegalArgumentException(CF_INVALIDRANGE);
		}
		return rangeWrappers;
	}

	public static JSONArrayWrapper parseMacroMessageJsonArray(JSONArrayWrapper messageJsonArray) {
		JSONArrayWrapper parsedArray = new JSONArrayWrapper();
		for (int i = 0; i < messageJsonArray.length(); i++) {
			JSONObjectWrapper msgJson = messageJsonArray.getJSONObject(i);
			JSONArrayWrapper msgArray = new JSONArrayWrapper();
			msgArray.put(msgJson.getString("MN"));
			msgArray.put(msgJson.getString("M"));
			msgArray.put(msgJson.getString("L"));
			msgArray.put(msgJson.getString("Title"));
			if(msgJson.has("i")){
				msgArray.put(msgJson.getString("i"));
			}

			if(msgJson.has("Cur_sel")){
				msgArray.put(msgJson.getString("Cur_sel"));
			}

			if(msgJson.has("Mname")){
				msgArray.put(msgJson.getString("Mname"));
			}

			if(msgJson.has("pastIterationData")){
				msgArray.put(msgJson.getString("pastIterationData"));
			}
			parsedArray.put(msgArray);
		}
		return parsedArray;
	}

	public static JSONArrayWrapper parseMacroInputBoxJsonArray(JSONArrayWrapper messageJsonArray) {
		JSONArrayWrapper parsedArray = new JSONArrayWrapper();
		JSONObjectWrapper msgJson = messageJsonArray.getJSONObject(0);
		JSONArrayWrapper msgArray = new JSONArrayWrapper();
		msgArray.put(msgJson.getString("title"));
		msgArray.put(msgJson.getString("message"));
		msgArray.put(msgJson.getString("value"));
		msgArray.put(msgJson.getString("currentIterationData"));
		if(msgJson.has("CurrentSelection")){
			msgArray.put(msgJson.getString("Cur_sel"));
		}
		if(msgJson.has("Mname")){
			msgArray.put(msgJson.getString("Mname"));
		}
		if(msgJson.has("pastIterationData")){
			msgArray.put(msgJson.getString("pastIterationData"));
		}
		parsedArray.put(msgArray);
		return parsedArray;
	}

	public static JSONArrayWrapper parseMacroErrorJsonArray(JSONObjectWrapper errorJsonObject) {
		JSONArrayWrapper parsedArray = new JSONArrayWrapper();
		parsedArray.put(errorJsonObject.getString("mn"));
		parsedArray.put(errorJsonObject.getString("m"));
		parsedArray.put(errorJsonObject.getString("l"));
		parsedArray.put(errorJsonObject.getString("c"));
		return parsedArray;
	}

	public static JSONArrayWrapper getCFFaultyRanges(List<RangeWrapper> faultyRangeList, Sheet sheet) {
		JSONArrayWrapper faultyArray = new JSONArrayWrapper();
		List<DataRange> intersectingRanges = getIntersectingCFRanges(faultyRangeList,sheet);
		for (DataRange range : intersectingRanges) {
			JSONArrayWrapper array = new JSONArrayWrapper();
			array.put(range.getStartRowIndex());
			array.put(range.getStartColIndex());
			array.put(range.getEndRowIndex());
			array.put(range.getEndColIndex());
			faultyArray.put(array);
		}
		return faultyArray;
	}

	public static List<DataRange> getIntersectingCFRanges(List<RangeWrapper> rangeWrapperList,Sheet sheet) {
		List<DataRange> intersectedList = new ArrayList<>();
		List<Integer> csUIDList = sheet.getConditionalStyleUIDList();
		Map<Integer, ConditionalStyleObject> conditionalStyleMap = sheet.getConditionalStyleMap();

		outer:
		for (RangeWrapper rangeWrapper : rangeWrapperList) {
			if (rangeWrapper.getOperationType() == CommandConstants.OperationType.DELETE) {
				intersectedList.add(new DataRange(rangeWrapper.getSheetName(), rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol()));
				continue;
			}
			for (int i = csUIDList.size() - 1; i >= 0; i--) {
				int csUID = csUIDList.get(i);
				ConditionalStyleObject cso = conditionalStyleMap.get(csUID);
				SpecialRange specialRange = cso.getSpecialRange();
				for(DataRange range : specialRange.getRanges()) {
					DataRange intercept = RangeUtil.intersection(new DataRange(rangeWrapper.getSheetName(),rangeWrapper.getstartRow(),rangeWrapper.getstartCol(),rangeWrapper.getEndRow(),rangeWrapper.getEndCol()), range);
					if(intercept != null) {
						intersectedList.add(intercept);
					}
				}
			}
		}
		return intersectedList;
	}

	public static List<RangeWrapper> mergeRangeWrappers(Collection<RangeWrapper> ranges) {
		List<RangeWrapper> mergedRanges = new ArrayList<>();
		for (RangeWrapper range : ranges) {
			mergeAndAddRangeWrapperToList(range, mergedRanges);
		}
		return mergedRanges;
	}

	public static boolean mergeAndAddRangeWrapperToList(RangeWrapper inRange, List<RangeWrapper> rangeList) {
		String inSheet = inRange.getSheetName();
		int inStartRow = inRange.getstartRow();
		int inStartCol = inRange.getstartCol();
		int inEndRow = inRange.getEndRow();
		int inEndCol = inRange.getEndCol();

		RangeWrapper tempRange;

		int size = rangeList.size();
		for (int i = 0; i < size; i++) {
			RangeWrapper loopRange = rangeList.get(i);
			String loopSheet = loopRange.getSheetName();

			int loopStartRow = loopRange.getstartRow();
			int loopStartCol = loopRange.getstartCol();
			int loopEndRow = loopRange.getEndRow();
			int loopEndCol = loopRange.getEndCol();

			if (isMember(loopRange, inSheet, inStartRow, inStartCol) && isMember(loopRange, inSheet, inEndRow, inEndCol)) {
				return true;
			} else if (isMember(inRange, loopSheet, loopStartRow, loopStartCol) && isMember(inRange, loopSheet, loopEndRow, loopEndCol)) {
				rangeList.remove(i);
				i -= 1;
				size = rangeList.size();
			} else {

				if (inStartCol == loopStartCol && inEndCol == loopEndCol) {
					if (inEndRow >= loopStartRow - 1 && inEndRow <= loopEndRow) {
						rangeList.remove(i);
						tempRange = new RangeWrapper(inRange.getSheetName(), inStartRow, inStartCol, loopEndRow, inEndCol, inRange.getOperationType());
						inRange = tempRange;
						mergeAndAddRangeWrapperToList(inRange, rangeList);
						return true;
					} else if (inStartRow <= loopEndRow + 1 && inStartRow >= loopStartRow) {
						rangeList.remove(i);
						tempRange = new RangeWrapper(inRange.getSheetName(), loopStartRow, inStartCol, inEndRow, inEndCol, inRange.getOperationType());
						inRange = tempRange;
						mergeAndAddRangeWrapperToList(inRange, rangeList);
						return true;
					}
				} else if (inStartRow == loopStartRow && inEndRow == loopEndRow) {
					if (inEndCol >= loopStartCol - 1 && inEndCol <= loopEndCol) {
						rangeList.remove(i);
						tempRange = new RangeWrapper(inRange.getSheetName(), inStartRow, inStartCol, inEndRow, loopEndCol, inRange.getOperationType());
						inRange = tempRange;
						mergeAndAddRangeWrapperToList(inRange, rangeList);
						return true;
					} else if (inStartCol <= loopEndCol + 1 && inStartCol >= loopStartCol) {
						rangeList.remove(i);
						tempRange = new RangeWrapper(inRange.getSheetName(), inStartRow, loopStartCol, inEndRow, inEndCol, inRange.getOperationType());
						inRange = tempRange;
						mergeAndAddRangeWrapperToList(inRange, rangeList);
						return true;
					}
				}
			}
		}

		rangeList.add(inRange);
		return false;
	}

	private static boolean isMember(RangeWrapper range, String sheetName, int cellRow, int cellCol) {
		return !(!range.getSheetName().equals(sheetName)
			|| cellRow < range.getstartRow() || cellRow > range.getEndRow()
			|| cellCol < range.getstartCol() || cellCol > range.getEndCol());
	}

	public static Cell getMergeParentCellIfCovered(Sheet sheet, int rowIndex, int colIndex, Map<Cell, DataRange> mergeRanges)
	{
		Cell cell = sheet.getCellReadOnly(rowIndex, colIndex);
		if(cell != null && mergeRanges.get(cell) != null)
		{
			return null;
		}
		Iterator <Map.Entry<Cell, DataRange>> mergeCellDetailsItr = mergeRanges.entrySet().iterator();       //used iterator to fix concurrent modification exception
		while(mergeCellDetailsItr.hasNext())
		{
			Map.Entry<Cell, DataRange> mergeCellDetailEntry = mergeCellDetailsItr.next();
			Cell mergeParentCell = mergeCellDetailEntry.getKey();
			DataRange mergedRange = mergeCellDetailEntry.getValue();
			if (mergedRange.isMember(sheet.getAssociatedName(), rowIndex, colIndex)) {
				return mergeParentCell;
			}
		}
		return null;
	}

	public static JSONObjectWrapper getPicklistJson(Picklist picklist, Workbook workbook) {
		JSONObjectWrapper picklistJson = new JSONObjectWrapper();
		picklistJson.put(Integer.toString(CommandConstants.PICKLIST_ID), picklist.getId());

		JSONArrayWrapper itemJson;
		if(picklist.isRangePicklist()) {
			itemJson = getRangeItemJson(workbook, picklist);
		}
		else {
			itemJson = getItemJson(picklist, workbook.getTheme());
		}

		picklistJson.put(Integer.toString(CommandConstants.PL_LIST),itemJson);

		JSONArrayWrapper deletedIDArray = new JSONArrayWrapper();
		for(Integer in : picklist.getDeletedIds()) {
			deletedIDArray.put(in);
		}
		picklistJson.put(Integer.toString(CommandConstants.PICKLIST_DELETE), deletedIDArray);
		picklistJson.put(Integer.toString(CommandConstants.DEFAULT), picklist.getDefaultItem().getId());
		picklistJson.put(Integer.toString(CommandConstants.IS_RANGE_PICKLIST), picklist.isRangePicklist());
		picklistJson.put(Integer.toString(CommandConstants.PICKLIST_BUBBLE), picklist.isShowAsBubble());
		picklistJson.put(Integer.toString(CommandConstants.ALLOW_MULTI_SELECT), picklist.isAllowMultiSelect());
		picklistJson.put(Integer.toString(CommandConstants.SHOW_DROPDOWN_ICON), picklist.showDropdownIcon().getForJson());
		picklistJson.put(Integer.toString(CommandConstants.STACK_DIRECTION), picklist.getStackDirection().getForJson());
		if(!picklist.getAutoStyleList().isEmpty()) {
			picklistJson.put(Integer.toString(CommandConstants.AUTO_COLORS), picklist.getAutoStylesJson(workbook.getTheme()));
		}


		if(picklist.isRangePicklist()) {
			JSONArrayWrapper sourceArray = new JSONArrayWrapper();

			for(DataRange range: picklist.getSourceRanges()) {
				JSONArrayWrapper array = new JSONArrayWrapper();
				array.put(range.getAssociatedSheetName());
				array.put(range.getStartRowIndex());
				array.put(range.getStartColIndex());
				array.put(range.getEndRowIndex());
				array.put(range.getEndColIndex());
				sourceArray.put(array);
			}
			picklistJson.put(Integer.toString(CommandConstants.SOURCE), sourceArray);
			picklistJson.put(Integer.toString(CommandConstants.SORT), picklist.isSort());
		}


		return picklistJson;
	}

	private static JSONArrayWrapper getItemJson(Picklist picklist, ZSTheme theme) {
		Map<PicklistStyle, JSONObjectWrapper> styleMap = picklist.getPicklistStyleMap(theme);
		JSONArrayWrapper itemJson = new JSONArrayWrapper();
		for(PicklistItem item : picklist.getItemList()) {
			try {
				JSONObjectWrapper jObj = new JSONObjectWrapper();
				jObj.put(Integer.toString(CommandConstants.PL_ITEM_ID), item.getId());
				if (item.getDisplayValue().isPresent()) {
					jObj.put(Integer.toString(CommandConstants.PL_DISPLAY), URLEncoder.encode(item.getDisplayValue().get(), "UTF-8").replace("+", "%20"));
				}
//                jObj.put(Integer.toString(CommandConstants.PL_VALUE), item.getActualValue().getValueString(EngineConstants.DEFAULT_LOCALE));
				PicklistStyle itemStyle = item.getStyle();
				if (itemStyle != null) {
					JSONObjectWrapper styleJson = styleMap.get(itemStyle);
					jObj.put(Integer.toString(CommandConstants.PL_STYLE), styleJson);
				}
				jObj.put(Integer.toString(CommandConstants.DELIMITER), item.containsDelimiter());
				itemJson.put(jObj);
			}
			catch(Exception e){}
		}

		return itemJson;
	}

	private static JSONArrayWrapper getRangeItemJson(Workbook workbook, Picklist picklist) {
		List<DataRange> sourceRanges = picklist.getSourceRanges();
		Map<PicklistStyle, JSONObjectWrapper> styleMap = picklist.getPicklistStyleMap(workbook.getTheme());

		List<PicklistItem> values = new LinkedList<>();
		Set<Integer> set = new HashSet();
		JSONArrayWrapper itemJson = new JSONArrayWrapper();
		for(DataRange range: sourceRanges) {
			RangeIterator iterator = new RangeIterator(workbook.getSheetByAssociatedName(range.getAssociatedSheetName()), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, true, false, true, false, true);
			while(iterator.hasNext()) {
				ReadOnlyCell rCell = iterator.next();
				Cell cell = rCell.getCell();
				if (cell != null) {
					if (cell.getCellPicklists() != null) {
						for (CellPicklist cellPicklist : cell.getCellPicklists()) {
							if (cellPicklist.getPicklist().getId() == picklist.getId()) {
								Integer itemID = cellPicklist.getItemID();
								PicklistItem item = itemID != null ? picklist.getItem(itemID) : null;
								if (itemID != null && item != null) {
									if (item.getDisplayValue().isPresent()) {
										sortPicklistItems(item, values, set, picklist.isSort());
									}
								}
							}
						}

					}
				}
			}
		}

		for(PicklistItem item: values) {
			try {
				JSONObjectWrapper jObj = new JSONObjectWrapper();
				jObj.put(Integer.toString(CommandConstants.PL_ITEM_ID), item.getId());
				jObj.put(Integer.toString(CommandConstants.PL_DISPLAY), URLEncoder.encode(item.getDisplayValue().get(), "UTF-8").replace("+", "%20"));
				PicklistStyle itemStyle = item.getStyle();
				if (itemStyle != null) {
					JSONObjectWrapper styleJson = styleMap.get(itemStyle);
					jObj.put(Integer.toString(CommandConstants.PL_STYLE), styleJson);
				}
				itemJson.put(jObj);
			}
			catch (Exception e){}
		}

		return itemJson;
	}

	private static void sortPicklistItems(PicklistItem item, List<PicklistItem> values, Set<Integer> set, boolean sort) {
		if(!sort) {
			if(set.contains(item.getId())) {
				return;
			}
			set.add(item.getId());
			values.add(item);
			return;
		}

		Value value1 = item.getActualValue();
		for(int i=0; i<values.size(); i++) {
			PicklistItem iter = values.get(i);
			Value value2 = iter.getActualValue();

			if(value1.compareTo(value2) < 0) {
				if(set.contains(item.getId())) {
					return;
				}
				set.add(item.getId());
				values.add(i, item);
				return;
			}
		}

		if(set.contains(item.getId())) {
			return;
		}
		set.add(item.getId());
		values.add(item);

	}

	public static JSONObjectWrapper getTableJsonOld(Workbook workbook, Table table) throws Exception {
		JSONObjectWrapper tableJson = new JSONObjectWrapper();

		JSONArrayWrapper rangeJson = new JSONArrayWrapper();
		rangeJson.put(table.getSheet().getAssociatedName());
		rangeJson.put(table.getStartRowIndex());
		rangeJson.put(table.getStartColIndex());
		rangeJson.put(table.getEndRowIndex());
		rangeJson.put(table.getEndColIndex());
		tableJson.put(Integer.toString(CommandConstants.RANGES), rangeJson);

		tableJson.put(Integer.toString(CommandConstants.ID), table.getID());
		tableJson.put(Integer.toString(CommandConstants.NAME), table.getName());
		tableJson.put(Integer.toString(CommandConstants.STYLE), table.getTableStyleName());

		JSONArrayWrapper properties = new JSONArrayWrapper();
		properties.put(table.isHeaderRowShown() ? 1 : 0);
		properties.put(table.isFooterRowShown() ? 1 : 0);
		properties.put(table.isShowFirstColumn() ? 1 : 0);
		properties.put(table.isShowLastColumn() ? 1 : 0);
		properties.put(table.isShowRowStripes() ? 1 : 0);
		properties.put(table.isShowColumnStripes() ? 1 : 0);

		tableJson.put(Integer.toString(CommandConstants.PROPERTIES), properties);

		JSONArrayWrapper colJson = new JSONArrayWrapper();
		for (TableColumn column : table.getTableColumns()) {
			colJson.put(Utility.getEncodedString(column.getColumnHeader()).replace("+", "%20"));
		}
		tableJson.put(Integer.toString(CommandConstants.COLUMN_HEADERS), colJson);
		tableJson.put(Integer.toString(CommandConstants.RESIZE), table.isAllowResize());

		return tableJson;
	}

	public static JSONObjectWrapper getTableJson(Workbook workbook, Table table) {
		JSONObjectWrapper tableJson = new JSONObjectWrapper();

		JSONArrayWrapper rangeJson = new JSONArrayWrapper();
		rangeJson.put(table.getSheet().getAssociatedName());
		rangeJson.put(table.getStartRowIndex());
		rangeJson.put(table.getStartColIndex());
		rangeJson.put(table.getEndRowIndex());
		rangeJson.put(table.getEndColIndex());
		tableJson.put(Integer.toString(CommandConstants.RANGES), rangeJson);

		tableJson.put(Integer.toString(CommandConstants.ID), table.getID());
		tableJson.put(Integer.toString(CommandConstants.NAME), table.getName());
		tableJson.put(Integer.toString(CommandConstants.STYLE), table.getTableStyleName());

		JSONArrayWrapper properties = new JSONArrayWrapper();
		properties.put(table.isHeaderRowShown() ? 1 : 0);
		properties.put(table.isFooterRowShown() ? 1 : 0);
		properties.put(table.isShowFirstColumn() ? 1 : 0);
		properties.put(table.isShowLastColumn() ? 1 : 0);
		properties.put(table.isShowRowStripes() ? 1 : 0);
		properties.put(table.isShowColumnStripes() ? 1 : 0);

		tableJson.put(Integer.toString(CommandConstants.PROPERTIES), properties);

		JSONArrayWrapper colJson = new JSONArrayWrapper();
		for (TableColumn column : table.getTableColumns()) {
			colJson.put(Utility.getEncodedString(column.getColumnHeader()).replace("+", "%20"));
		}
		tableJson.put(Integer.toString(CommandConstants.COLUMN_HEADERS), colJson);
		tableJson.put(Integer.toString(CommandConstants.RESIZE), table.isAllowResize());

		return tableJson;
	}

	public static JSONObjectWrapper getTableStyleJson(Workbook workbook, TableStyle style) {
		JSONObjectWrapper styleJson = new JSONObjectWrapper();
		for (Map.Entry<TableStyle.TableStylePropertyKey, TableStyle.TableStylePropertyValue> styleEntry : style.getProperties().entrySet()) {
			TableStyle.TableStylePropertyKey key = styleEntry.getKey();
			TableStyle.TableStylePropertyValue value = styleEntry.getValue();
			JSONArrayWrapper styleArray = ResponseUtils.getTableStyleDefinition(workbook, value.getCellStyle(), value.getStripeSize());
			styleJson.put(key.getJsonString(), styleArray);
		}
		styleJson.put(Integer.toString(CommandConstants.NAME), style.getName());
		styleJson.put(Integer.toString(CommandConstants.ISCUSTOM), style.getIsCustomStyle());
		return styleJson;
	}

	public static void setpicklistMeta(Workbook workbook, JSONObjectWrapper response, String asn) {
		JSONObjectWrapper sheetResponse = response.has(asn) ? response.getJSONObject(asn) : new JSONObjectWrapper();
		JSONObjectWrapper picklistResponse = sheetResponse.has(Integer.toString(CommandConstants.PICKLIST)) ? sheetResponse.getJSONObject(Integer.toString(CommandConstants.PICKLIST)) : new JSONObjectWrapper();
		if(picklistResponse.has(Integer.toString(CommandConstants.META))) {
			return;
		}

		Sheet sheet = workbook.getSheetByAssociatedName(asn);
		if(sheet == null) {
			return;
		}
		Map<Integer, List<DataRange>> rangeMap = sheet.getPicklistRangeMap();
		JSONArrayWrapper rangeListJson = new JSONArrayWrapper();
		for (Map.Entry<Integer, List<DataRange>> entry : rangeMap.entrySet()) {
			int picklistID = entry.getKey();
			List<DataRange> rangeList = entry.getValue();
			for (DataRange range : rangeList) {
				JSONArrayWrapper rangeJson = new JSONArrayWrapper();
				rangeJson.put(picklistID);
				rangeJson.put(range.getStartRowIndex());
				rangeJson.put(range.getStartColIndex());
				rangeJson.put(range.getEndRowIndex());
				rangeJson.put(range.getEndColIndex());
				rangeListJson.put(rangeJson);
			}
		}

		picklistResponse.put(Integer.toString(CommandConstants.META), rangeListJson);
		sheetResponse.put(Integer.toString(CommandConstants.PICKLIST),picklistResponse);
		response.put(asn, sheetResponse);
	}

	/************   METHODS ARE MOVED FROM RESPONSEOBJECT **************/

	//
	public static JSONArrayWrapper getNamedExpArray(int action, String name,String oldName, JSONObjectWrapper value,int pos,Integer count, String scopeASN, String comment){
		JSONArrayWrapper jArray =new JSONArrayWrapper();
//    	jObj.put(JSONConstants.ACTION, action);
//    	if(name != null)
//        {
//            name = "'" + name + "'";
//        }
//        jObj.put("n", name);
//        jObj.put("pos", pos);
		jArray.put(name);
		if(pos !=-1){
			jArray.put(pos);
		}

		switch (action){

			case  ActionConstants.NAMEDRANGE_ADD:
			case ActionConstants.NAMEDRANGE_MODIFY:
				jArray.put(value);
				jArray.put(scopeASN == null ? "" : scopeASN);
				jArray.put(comment == null ? "" : comment);
				break;
			case ActionConstants.NAMEDRANGE_DELETE:
				jArray.put(scopeASN == null ? "" : scopeASN);
				jArray.put(count);
				break;
		}
		if (action == ActionConstants.NAMEDRANGE_MODIFY) {
			jArray.put(oldName);
		}
		return jArray;
	}

	public static List getCellLinksInfo(Cell cell)
	{
		String cellLink = cell.getLink();
		List<RichStringProperties> valueLinks = new ArrayList<>();
		if(cell.getValue().getRawValue() instanceof ZSString){
			ZSString richString = (ZSString)cell.getValue().getRawValue();
			for(RichStringProperties stringProperty : richString.getProperties())
			{
				if(stringProperty.getUrl() != null)
				{
					valueLinks.add(stringProperty);
				}
			}

			// This part can be removed once client handles content links.
			if(cellLink != null && valueLinks != null && valueLinks.size() == 1 && valueLinks.get(0).getLabel().equals(richString.getBaseStringValue()))
			{
				// Should consider Cell level link in this case.
				valueLinks = Collections.EMPTY_LIST;
			}
			//////////////////
		}

		List list = new ArrayList();
		if(valueLinks != null && !valueLinks.isEmpty()){
			list.add(null);
			list.add(true);
		}else{
			list.add(cellLink);
			list.add(cell.getLink() != null);
		}
		return list;
	}

	/**
	 * Returns an JSON array containing rich string property i.e start index, end index, text style name
	 * @param cell
	 * @return
	 */
	public static JSONArrayWrapper getCellRichStringContents(Cell cell)
	{
		JSONArrayWrapper richStringContent = new JSONArrayWrapper();
		try
		{
			if(cell != null && cell.isZSString())
			{
				ZSString zsString = ((ZSString)cell.getValue().getRawValue());
				zsString.getProperties().stream().filter(property -> (property.getStyleName() != null || property.getField() != null) || property.getUrl() != null).forEach(richStringProperty ->
				{
					JSONArrayWrapper richString = new JSONArrayWrapper();

					richString.put(richStringProperty.getStartIndex());
					richString.put(richStringProperty.getEndIndex());
					richString.put(richStringProperty.getStyleName() == null ? "" : richStringProperty.getStyleName());
					richString.put(richStringProperty.getField() == null ? "" : richStringProperty.getField().getId());
					richString.put(richStringProperty.getUrl() == null ? "" : richStringProperty.getUrl());

					richStringContent.put(richString);
				});
			}
		}
		catch(Exception e)
		{
			LOGGER.log(Level.SEVERE,"TO BE FIXED : Exception while retrieving cell rich strings", e);
		}
		return richStringContent;

	}
	public static void getSlicerDetails(Workbook workbook,Slicer slicer, JSONObjectWrapper slicerJSON) {


		JSONObjectWrapper slicerStyles = new JSONObjectWrapper();
		for(Slicer.SlicerStyle style: Slicer.SlicerStyle.values()){
			if(slicer.getCellStyle(style) != null){
				JSONArrayWrapper styleArray = getSlicerStyleDefinition(workbook,slicer.getCellStyle(style));
				slicerStyles.put(style.toString().toLowerCase(),styleArray);
			}
		}
		JSONObjectWrapper boxShadow = new JSONObjectWrapper();
		Shadow slicerShadow = slicer.getShadow();
		if(slicerShadow != null){
			ZSColor shadowColor = slicerShadow.getColor();
			int x = (int)Math.round( slicerShadow.getDistance() * Math.cos(slicerShadow.getAngle() * (Math.PI / 180)));
			int y = (int)Math.round( slicerShadow.getDistance() * Math.sin(slicerShadow.getAngle() * (Math.PI / 180)));
			String hexColor = shadowColor.getHexColorToWrite();
			String tint = shadowColor.getColorTintToWrite();
			String accent = shadowColor.getThemeColorToWrite();
			JSONObjectWrapper colorJson = new JSONObjectWrapper();
			if(hexColor != null) {
				colorJson.put(Integer.toString(CommandConstants.HEX_COLOR), hexColor);
			}
			else {
				colorJson.put(Integer.toString(CommandConstants.TINT), tint);
				colorJson.put(Integer.toString(CommandConstants.THEME_COLOR), accent);
			}
			boxShadow.put("x",x);
			boxShadow.put("y",y);
			boxShadow.put("blur",slicerShadow.getBlur());
			boxShadow.put("opacity",slicerShadow.getTransparency());
			boxShadow.put("inner",slicerShadow.isInner());
			boxShadow.put("color",colorJson);
			boxShadow.put("spread",slicerShadow.getSpread());
		}
		String slicerID= slicer.getSlicerID();
		String slicerName = slicer.getName();
		Position position = slicer.getPosition();
		slicerJSON.put(JSONConstants.SLICER_ID, slicerID);
		slicerJSON.put(JSONConstants.SLICER_NAME,slicerName);
		slicerJSON.put(JSONConstants.SLICER_SOURCE_COL,slicer.getConnection().getFieldName());
		slicerJSON.put(JSONConstants.START_ROW,position.getRowIndex());
		slicerJSON.put(JSONConstants.START_COLUMN,position.getColIndex());
		slicerJSON.put(JSONConstants.START_ROW_DIFF,position.getRowDiff());
		slicerJSON.put(JSONConstants.START_COL_DIFF,position.getColDiff());
		slicerJSON.put(JSONConstants.SLICER_HEIGHT, slicer.getHeight());
		slicerJSON.put(JSONConstants.SLICER_WIDTH,  slicer.getWidth());
		slicerJSON.put(JSONConstants.SLICER_BUTTON_HEIGHT, slicer.getButtonHeight());
		slicerJSON.put(JSONConstants.SLICER_COL_LAYOUT,slicer.getColumnLayout());
//        slicerJSON.put(JSONConstants.SLICER_TRANSPARENCY,slicer.getSlicerTransparency());
		slicerJSON.put(JSONConstants.SLICER_MULTI_SELECT,slicer.isMultiSelect());
		slicerJSON.put(JSONConstants.SLICER_SORT_ORDER,slicer.getSortOrder());
//        slicerJSON.put(JSONConstants.SLICER_HEADER_VISIBLE,slicer.isHeaderVisible());
		slicerJSON.put(JSONConstants.CONNECTED_PIVOTS,slicer.getConnection().getTablesNames());
		slicerJSON.put(JSONConstants.SLICER_STYLE, slicerStyles);
		slicerJSON.put(JSONConstants.SLICER_THEME_ID,SlicerUtil.getSlicerTheme(slicer));
		slicerJSON.put(JSONConstants.SLICER_LAYOUT,slicer.getSlicerLayoutIndex());
		slicerJSON.put(JSONConstants.SCROLL_INDEX,slicer.getScrollStartItemIndex());
		slicerJSON.put("boxShadow",getShadowFromSlicer(slicer));

	}
	public static JSONObjectWrapper getShadowFromSlicer(SlicerBody slicer){
		JSONObjectWrapper boxShadow = new JSONObjectWrapper();
		Shadow timelineShadow = slicer.getShadow();
		if(timelineShadow != null){
			ZSColor shadowColor = timelineShadow.getColor();
			int x = (int)Math.round( timelineShadow.getDistance() * Math.cos(timelineShadow.getAngle() * (Math.PI / 180)));
			int y = (int)Math.round( timelineShadow.getDistance() * Math.sin(timelineShadow.getAngle() * (Math.PI / 180)));
			String hexColor = shadowColor.getHexColorToWrite();
			String tint = shadowColor.getColorTintToWrite();
			String accent = shadowColor.getThemeColorToWrite();
			JSONObjectWrapper colorJson = new JSONObjectWrapper();
			if(hexColor != null) {
				colorJson.put(Integer.toString(CommandConstants.HEX_COLOR), hexColor);
			}
			else {
				colorJson.put(Integer.toString(CommandConstants.TINT), tint);
				colorJson.put(Integer.toString(CommandConstants.THEME_COLOR), accent);
			}
			boxShadow.put("x",x);
			boxShadow.put("y",y);
			boxShadow.put("blur",timelineShadow.getBlur());
			boxShadow.put("opacity",timelineShadow.getTransparency());
			boxShadow.put("inner",timelineShadow.isInner());
			boxShadow.put("color",colorJson);
			boxShadow.put("spread",timelineShadow.getSpread());
		}
		return boxShadow;
	}
	public static void sendFullSlicerResponse(Workbook workbook,JSONObjectWrapper responseJson,JSONObjectWrapper sheetObjResponse,JSONObjectWrapper slicerResponse,Set<String> connectedTables,String slicerId,String assSheetName){
		JSONArrayWrapper slicerJSONArray = slicerResponse.has(Integer.toString(CommandConstants.META)) ? slicerResponse.getJSONArray(Integer.toString(CommandConstants.META)) : new JSONArrayWrapper();
		List visitedSlicersList = new ArrayList();
		visitedSlicersList.add(slicerId);
		for (String pivot : connectedTables) {
			List<String> slicers = SlicerUtil.getSlicersOfPivot(workbook, pivot);
			for (String slicer1 : slicers) {
				Slicer curSlicer = workbook.getSlicer(slicer1);
				if(!visitedSlicersList.contains(curSlicer.getSlicerID())) {
					visitedSlicersList.add(curSlicer.getSlicerID());
					if (!curSlicer.getPosition().getAssociatedSheetName().equals(assSheetName)) {
						if (!slicerJSONArray.isEmpty()) {
							slicerResponse.put(Integer.toString(CommandConstants.META), slicerJSONArray);
							sheetObjResponse.put(Integer.toString(CommandConstants.SLICER), slicerResponse);
							responseJson.put(assSheetName, sheetObjResponse);
						}
						assSheetName = curSlicer.getPosition().getAssociatedSheetName();
						sheetObjResponse = responseJson.has(assSheetName) ? responseJson.getJSONObject(assSheetName) : new JSONObjectWrapper();
						slicerResponse = sheetObjResponse.has(Integer.toString(CommandConstants.SLICER)) ? sheetObjResponse.getJSONObject(Integer.toString(CommandConstants.SLICER)) : new JSONObjectWrapper();
						slicerJSONArray = slicerResponse.has(Integer.toString(CommandConstants.META)) ? slicerResponse.getJSONArray(Integer.toString(CommandConstants.META)) : new JSONArrayWrapper();
					}

					JSONObjectWrapper slicerJSON = new JSONObjectWrapper();
					getSlicerDetails(workbook,curSlicer,slicerJSON);
					if(workbook.isPivotSlicer(curSlicer)) {
						SlicerUtil.getPivotSlicerDetails(workbook, curSlicer, slicerJSON);
					}
					slicerJSONArray.put(slicerJSON);
				}
			}
		}
		if(!slicerJSONArray.isEmpty()) {
			slicerResponse.put(Integer.toString(CommandConstants.META), slicerJSONArray);
			sheetObjResponse.put(Integer.toString(CommandConstants.SLICER), slicerResponse);
			responseJson.put(assSheetName, sheetObjResponse);
		}
	}

}
