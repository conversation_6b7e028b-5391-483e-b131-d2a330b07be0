/* $Id$ */
package com.adventnet.zoho.websheet.model.response.viewport;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.response.meta.CellMeta.CellMetaType;
import com.adventnet.zoho.websheet.model.response.meta.util.MetaBitUtil;
import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ConstraintsImpl implements Constraints{

    private	ContemporaryViewPort    contemporaryViewPort;
    private	boolean     			showFormulas;
    private	int						cellMetaOffsetBit;
    
    public ConstraintsImpl(ContemporaryViewPort contemporaryViewPort, boolean showFormulas,int	cellMetaOffsetBit)
    {
        
        this.contemporaryViewPort       =   contemporaryViewPort;
        this.showFormulas   			=   showFormulas;
        this.cellMetaOffsetBit			=	cellMetaOffsetBit;
    }
    
    @Override
    public boolean isValidCell(String sheetName, int rowIndex, int colIndex) {
    	
    	
    	return false;
    }

    @Override
    public boolean showFormulas() {
        return  this.showFormulas;
    }


    @Override
    public boolean isCellInActiveViewPort(Cell cell) {
    	ViewPortStatus		cvport	=	ViewPortStatus.CACHE;
    	try{
    		cvport	=	contemporaryViewPort.getCellStatus(cell.getRow().getSheet().getAssociatedName(), cell.getRowIndex(), cell.getColumnIndex());
    	}catch (Exception e) {
			// TODO: handle exception
		}
        return ViewPortStatus.ACTIVE  == cvport;
    }


	@Override
	public boolean isUnMix() {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
		{
			return true;
		}
		if (o == null || getClass() != o.getClass())
		{
			return false;
		}
		ConstraintsImpl that = (ConstraintsImpl) o;
		return showFormulas == that.showFormulas && cellMetaOffsetBit == that.cellMetaOffsetBit && Objects.equals(contemporaryViewPort, that.contemporaryViewPort);
	}

	@Override
	public int hashCode() {
		return Objects.hash(contemporaryViewPort, showFormulas, cellMetaOffsetBit);
	}

	//	public	String	toString()
//    {
//    	return null;
//    }

	@Override
	public boolean isSheetInActiveViewPort(String sheetName) {
		// TODO Auto-generated method stub
		return contemporaryViewPort.getActiveViewPortSheetName() != null && contemporaryViewPort.getActiveViewPortSheetName().equals(sheetName);
	}

	@Override
	public IterateArea getAreaIterator(String sheetName, int startRow,
			int startCol, int endRow, int endCol) {
		// TODO Auto-generated method stub
		
		return	contemporaryViewPort.getAreaIterator(sheetName, startRow, startCol, endRow, endCol);
		//return null;
	}

	@Override
	public boolean isSheetInViewPort(String asn) {
		// TODO Auto-generated method stub
		return contemporaryViewPort.isSheetInViewPort(asn);
	}

	@Override
	public ViewPortStatus getCellStatusInViewPort(Cell cell) {
		// TODO Auto-generated method stub
		ViewPortStatus		cvPort	=	ViewPortStatus.CACHE;
		String	sheetName	=	cell.getRow().getSheet().getAssociatedName();
		try{
				cvPort		=	contemporaryViewPort.getCellStatus(sheetName, cell.getRowIndex(), cell.getColumnIndex());
		}
		catch (Exception e) {
			// TODO: handle exception
		}
				
//		if(contemporaryViewPort.getActiveViewPortSheetName().equals(sheetName))
//		{
//				
//					
//			
//		}
		
		return cvPort;
	}

	@Override
	public boolean isDummyConstraints() {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public ContemporaryViewPort getContemporaryViewPort() {
		// TODO Auto-generated method stub
		return this.contemporaryViewPort;
	}

	@Override
	public void updateContemporaryViewPort(ContemporaryViewPort  contemporaryViewPort) {
		// TODO Auto-generated method stub
		  this.contemporaryViewPort	=	contemporaryViewPort;
	}

	@Override
	public void setCellMetaBit(int offsetBit) {
		// TODO Auto-generated method stub
		this.cellMetaOffsetBit	=	offsetBit;
	}

	@Override
	public boolean isPermissible(CellMetaType cellMetaType) {
		// TODO Auto-generated method stub
		//Moved implementation to ResponseUtils because of code duplications
		return	MetaBitUtil.checkPermission(cellMetaType, this.cellMetaOffsetBit);
		
		
	}

	@Override
	public int getCellMetaBit() {
		// TODO Auto-generated method stub
		return this.cellMetaOffsetBit;
	}
	@Override
	public boolean isRangeInDomViewPort(RangeWrapper rangeWrapper) {
		return this.contemporaryViewPort != null && this.contemporaryViewPort.isAreaBelongsToDomViewPort(rangeWrapper.getSheetName(), rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());
	}

	@Override
	public boolean isRangeIntersectWithDOMViewPort(RangeWrapper rangeWrapper) {
		return this.contemporaryViewPort != null && this.contemporaryViewPort.isRangeIntersectWithDomViewPort(rangeWrapper.getSheetName(), rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());
	}
	// Find the range that intersects with Active/DOM Viewport
	@Override
	public List<RangeWrapper> getRangeIntersectingWithDOMViewPort(RangeWrapper rangeWrapper) {
		List<RangeWrapper> intersectingRange = new ArrayList();
		
		if(isRangeIntersectWithDOMViewPort(rangeWrapper)) {
			intersectingRange = this.contemporaryViewPort.getRangeIntersectingWithDomViewPort(rangeWrapper.getSheetName(), rangeWrapper.getstartRow(), rangeWrapper.getstartCol(), rangeWrapper.getEndRow(), rangeWrapper.getEndCol());
		}
		return intersectingRange;
	}
	
	
}
