/* $Id$ */
package com.adventnet.zoho.websheet.model.response.holder;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.*;
import com.adventnet.zoho.websheet.model.response.helper.DocumentSettingsWrapper;
import com.adventnet.zoho.websheet.model.response.helper.NamedRangeWrapper;
import com.adventnet.zoho.websheet.model.response.helper.SheetWrapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public class DocumentResponseHolder {

    private List <SheetWrapper> sheetWrapper;
    private HashMap<String, Set<String>> newlyCreatedstylesKeyInfo;
    private HashMap<String, String> documentSettingsInfo;		// We can go for custom class too.
    private boolean generateRowHeaderDefinitions;
    private boolean generateColHeaderDefinitions;
    private boolean generateDocumentMeta;
    private boolean generateUserMeta;
    private boolean generateCellStylesDefinitions;
    private boolean generateTextStyleDefinitions;
    private ActiveInfoBean activeInfoBean;
    private ActionIdentifierBean actionIdentifierBean;
    private Map<String, Map<String, String>> syncStyleNames;
    private HashSet<String> faultySheets;
    private FormBean formBean;
    private List<PivotBean> pivotBean;
    private DataConnectionBean dataConnectionBean;
    private DiscussionBean disBean;
    private boolean isRevertVesion;
    private boolean isContainsOleObj;
    private NamedRangeWrapper namedRangeWrapper;
    private DocumentSettingsWrapper docSettingsWrapper;
    private List<ImportBean> importBean;
    private VersionInfoBean versionInfoBean;
	private DocumentMetaBean documentMetaBean;
	private List<DelugeFunctionsBean> delugeFunctionList;
	private PicklistBean picklistBean;
        private UserFontBean userfontlistBean;
    private boolean isReloadTile = false;
    private String zsTheme ;
   private List<TableBean> tableBeanList;
    private List<ImageMetaBean> imageMetaBeanList;
    private JSONObjectWrapper workbookLinkResponse;
    private String publishRangeId = null;
    private boolean generateWorkbookImages;
    private ThemesBean themesBean;
    private FieldsBean fieldsBean;
    private boolean isOnLoadRequest = false;
    private boolean isNeedToRefreshNavigator = false;


    //active info
    //action info
    public void setVersionInfoBean(VersionInfoBean versionInfoBean){
        this.versionInfoBean = versionInfoBean;
    }
    
    public VersionInfoBean getVersionInfoBean(){
        return this.versionInfoBean;
    }
    
    public void setRevertVesion(boolean isRevertVesion) {

        this.isRevertVesion = isRevertVesion;

    }
    
    public boolean isForcedReloadTile(){
        return this.isReloadTile;
    }
    
    public void setReloadTile(boolean isReloadTile){
        this.isReloadTile = isReloadTile;
    }
    
    public void setOleObj(boolean isContainsOleObj) {

        this.isContainsOleObj = isContainsOleObj;

    }

    public void setSheetWrapper(List<SheetWrapper> sheetWrapper) {        
        this.sheetWrapper = sheetWrapper;
    }

    public void setNewlyCreatedStyles(HashMap<String, Set<String>> newlyCreatedstylesKeyInfo) {
        this.newlyCreatedstylesKeyInfo = newlyCreatedstylesKeyInfo;
    }

    public void setRowHeaderDefinitions(boolean generateRowHeaderDefinitions) {

        this.generateRowHeaderDefinitions = generateRowHeaderDefinitions;
    }

    public void setColHeaderDefinitions(boolean generateColHeaderDefinitions) {
        this.generateColHeaderDefinitions = generateColHeaderDefinitions;
    }

    public void setDocumentMeta(DocumentMetaBean documentMetaBean) {
    		this.documentMetaBean = documentMetaBean;
    }

    public void setUserMeta(boolean generateUserMeta) {
        this.generateUserMeta = generateUserMeta;
    }
    
    public void setFontFamilyList(UserFontBean bean){
        this.userfontlistBean = bean;
    }

    public void setCellStylesDefinitions(boolean generateCellStylesDefinitions) {
        this.generateCellStylesDefinitions = generateCellStylesDefinitions;
    }

    public void setTextStyleDefinitions(boolean generateTextStyleDefinitions)
    {
        this.generateTextStyleDefinitions = generateTextStyleDefinitions;
    }

    public void setSyncStyleNames(Map<String, Map<String, String>> syncStyleNames) {
        this.syncStyleNames = syncStyleNames;
    }

    public void setActiveInfoBean(ActiveInfoBean activeInfoBean) {
        this.activeInfoBean = activeInfoBean;
    }

    public void setActiveIdentifierBean(ActionIdentifierBean actionIdentifierBean) {
        this.actionIdentifierBean = actionIdentifierBean;
    }

    public void setFaultySheets(HashSet<String> faultySheets) {
        this.faultySheets = faultySheets;
    }

    public void setFormBean(FormBean formBean) {
        this.formBean = formBean;
    }

    public void setPivotBean(List<PivotBean> pivotBeanList) {
    	    if(this.pivotBean == null)
    	    {
    	     	this.pivotBean = new ArrayList<PivotBean>();
    	    }
   	     this.pivotBean.addAll(pivotBeanList);
    }

    public void setDataConnectionBean(DataConnectionBean dcBean) {
        this.dataConnectionBean = dcBean;
    }

    public List<SheetWrapper> getSheetWrapper() {
        return sheetWrapper;
    }

    public HashMap<String, Set<String>> getNewlyCreatedstylesKeyInfo() {
        return newlyCreatedstylesKeyInfo;
    }

    public HashMap<String, String> getDocumentSettingsInfo() {
        return documentSettingsInfo;
    }

    public boolean getRowHeaderDefinitions() {
        return generateRowHeaderDefinitions;
    }

    public boolean getColHeaderDefinitions() {
        return this.generateColHeaderDefinitions;
    }

    public DocumentMetaBean getDocumentMeta() {
        return this.documentMetaBean;
    }

    public boolean getUserMeta() {
        return generateUserMeta;
    }

    public UserFontBean getFontFamilyList(){
        return this.userfontlistBean;
    }
    public boolean getCellStylesDefinitions() {
        return generateCellStylesDefinitions;
    }

    public boolean getTextStylesDefinitions() {
        return generateTextStyleDefinitions;
    }

    public Map<String, Map<String, String>> getSyncStyleNames() {
        return syncStyleNames;
    }

    public ActiveInfoBean getActiveInfoBean() {
        return activeInfoBean;
    }

    public ActionIdentifierBean getActiveIdetifierBean() {
        return actionIdentifierBean;
    }

    public HashSet<String> getFaultySheets() {
        return faultySheets;
    }

    public FormBean getFormBean() {
        return this.formBean;
    }

    public List<PivotBean> getPivotBean() {
        return this.pivotBean;
    }

    public DataConnectionBean getDataConnectionBean() {
        return this.dataConnectionBean;
    }

    public boolean isRevertVesion() {
        return isRevertVesion;
    }

    public boolean isContainsOleObj() {
        return isContainsOleObj;
    }

    public void setNamedRangeWrapper(NamedRangeWrapper namedRangeWrapper) {
        this.namedRangeWrapper = namedRangeWrapper;
    }

    public NamedRangeWrapper getNamedRangeWrapper() {
        return namedRangeWrapper;
    }

    public void setDocumentSettingsWrapper(DocumentSettingsWrapper docSettingsWrapper) {
        this.docSettingsWrapper = docSettingsWrapper;
    }

    public DocumentSettingsWrapper getDocumentSettingsWrapper() {
        return docSettingsWrapper;
    }

    public List<ImportBean> getImportBean() {
        return this.importBean;
    }

    public void setImportBeans(List<ImportBean> importWrapperList) {

        this.importBean = importWrapperList;
    }
    
    public void setDiscussionBean(DiscussionBean disBean) {
        this.disBean = disBean;
    }
    
    public DiscussionBean getDiscussionBean() {
        return this.disBean;
    }
    public List<DelugeFunctionsBean> getDelugeFunctionsList(){
    		return this.delugeFunctionList;
    }
    public void setDelugeFunctionsList(List<DelugeFunctionsBean> delugeFunctionsList) {
    		this.delugeFunctionList = delugeFunctionsList;
    }

    public void setPicklistBean(PicklistBean bean) {
        this.picklistBean = bean;
    }

    public PicklistBean getPicklistBean() {
        return this.picklistBean;
    }

    public ThemesBean getZSTheme() {
        return this.themesBean;
    }

    public void setZSTheme(ThemesBean themesBean) {
        this.themesBean = themesBean;
    }

    public FieldsBean getFieldsBean() {
    	return this.fieldsBean;
    }
    
    public void setFieldsBean(FieldsBean fieldsBean) {
    	this.fieldsBean = fieldsBean;
    }
    
    public void setTableBeanList(List<TableBean> beanList) {
        this.tableBeanList = beanList;
    }

    public List<TableBean> getTableBeanList() {
        return this.tableBeanList;
    }

    public void setPublishRangeId(String publishRangeId) {
    	this.publishRangeId = publishRangeId;
    }
    
    public String getPublishRangeId() {
    	return this.publishRangeId;
    }


    public void setImageMetaBeanList(List<ImageMetaBean> imageMetaBeanList)
    {
        this.imageMetaBeanList = imageMetaBeanList;
    }

    public List<ImageMetaBean> getImageMetaBeanList()
    {
        return this.imageMetaBeanList;
    }

    public JSONObjectWrapper getWorkbookLinkResponse() {
        return this.workbookLinkResponse;
    }

    public void setWorkbookLinkResponse(JSONObjectWrapper workbookLinkResponse) {
        this.workbookLinkResponse = workbookLinkResponse;
    }

    public boolean isGenerateWorkbookImages() {
        return generateWorkbookImages;
    }

    public void setGenerateWorkbookImages(boolean generateWorkbookImages) {
        this.generateWorkbookImages = generateWorkbookImages;
    }

    public void setOnLoadRequest(boolean onLoadRequest)
    {
        this.isOnLoadRequest = onLoadRequest;
    }

    public boolean isOnLoadRequest()
    {
        return isOnLoadRequest;
    }

    public boolean isNeedToRefreshNavigator() {
        return isNeedToRefreshNavigator;
    }

    public void setNeedToRefreshNavigator(boolean needToRefreshNavigator) {
        isNeedToRefreshNavigator = needToRefreshNavigator;
    }
}
