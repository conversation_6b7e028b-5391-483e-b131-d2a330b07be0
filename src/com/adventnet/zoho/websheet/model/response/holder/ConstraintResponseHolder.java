/* $Id$ */
package com.adventnet.zoho.websheet.model.response.holder;

import com.adventnet.zoho.websheet.model.ImageBook;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.response.beans.*;
import com.adventnet.zoho.websheet.model.response.helper.*;
import com.zoho.sheet.util.FilterBean;

import java.util.*;

/**
 * <AUTHOR>
 */
public class ConstraintResponseHolder {

	private List<RangeWrapper> columnHeaderRanges;
	private List<RangeWrapper> rowHeaderRanges;
	private Map<String, List<FilterBean>> filterBeanMap = new HashMap<>();
	private List<FreezeWrapper> freezePanes;

	private List<RangeWrapper> dataValidationRanges;
	private List<RangeWrapper> conditionalFormatRanges;
	private List<RangeWrapper> arrayFormulasRanges;
	private List<RangeWrapper> cellProtectedRanges;

	private List<RangeWrapper> hiddenRows;
	private List<RangeWrapper> hiddenColumns;
	private List<RangeWrapper> mergeCells;
	private List<RangeWrapper> columnLevelFormats;
	private CellResponse cellsResponse;

	private List<RangeWrapper> selectedRanges;

	private HashMap<String, List<Long>> dependentCellsMap;

	private int cellMetaHeader;
	private ActiveInfoBean activeInfoBean;

	private List<RangeWrapper> insertDeleteCellRangeWrapper;

	private RangeWrapper formRanges;

	private List<RangeWrapper> pivotRagnes;

	private List<RangeWrapper> appliedConditionalCellStyleRanges;

	private RangeWrapper maxUsedCells;
	private HashSet<String> faultySheets;

	private List<HideGridWrapper> hideGrid;

	private List<RangeWrapper> checkBox;

	private List<ChartBean> chartBean;

	private List<SheetImageBean> imageBeans;
	private 	List<SlicerBean>				slicerBeans;
	private 	List<TimelineBean>				timelineBeans;
	private 	Map<String,List<RangeWrapper>> cfRangeList;
	private 	Map<String,List<RangeWrapper>> cfFaultyRangeList;
    private         List<ButtonBean>                        buttonBeans;

	private List<SparklineBean> sparklineBeanList;
	private Map<String, List<String>> ziaInfoList;

	private List<SheetWrapper> sheetZoomList;
	private List<SheetWrapper> sheetViewList;
	private List<SheetWrapper> sheetRtlList;
	private DataCleaningBean dataCleaningBean;

	private List<PicklistBean> picklistBean;

	private List<TableBean> tableBean;
	private List<ImageBook> imageBean;

	private List<PublishedViewWrapper> publishedViewWrapper;
	private List<CellResponse> rootMergeCellsData;
	private Map<String, JSONArrayWrapper> rootMergeCellsCfData;
	private DataConnectionBean dataConnectionBean;

	private List<RangeWrapper> groupingRangeWrapper;

	private List<KnitChartsSheetBean> knitChartsSheetBeans;

	private List<KnitChartsBean> knitChartsBeans;

	private List<KnitChartsModifiedBean> knitChartsModifiedBeans;

	private List<KnitChartsClipBean> knitChartsClipBeans;

	// yet to handle :--- datavalidationMessage ...

	public void addDependentCellsMap(HashMap<String, List<Long>> dependentCellsMap) {

		this.dependentCellsMap = dependentCellsMap;
	}

	public void addCellResponse(CellResponse cellResponse) {

		this.cellsResponse = cellResponse;
	}

	public void addFreezePanes(List<FreezeWrapper> freezePanes) {

		this.freezePanes = freezePanes;

	}

	public void addHideGrid(List<HideGridWrapper> hideGridWrapper) {
		this.hideGrid = hideGridWrapper;
	}

	public void addFilterBean(String asn, List<FilterBean> filterBeans) {
		this.filterBeanMap.put(asn, filterBeans);
	}

	public void addColumnHeaders(List<RangeWrapper> columnHeaderList) {

		if (this.columnHeaderRanges == null) {
			this.columnHeaderRanges = new ArrayList<RangeWrapper>();
		}

		this.columnHeaderRanges = columnHeaderList;
	}

	public void addRowHeaders(List<RangeWrapper> rowHeaderList) {

		if (this.rowHeaderRanges == null) {
			this.rowHeaderRanges = new ArrayList<RangeWrapper>();
		}

		this.rowHeaderRanges = rowHeaderList;
	}

	public void addArrayFormulas(List<RangeWrapper> arrayFormuals) {

		if (this.arrayFormulasRanges == null) {
			this.arrayFormulasRanges = new ArrayList<RangeWrapper>();
		}

		this.arrayFormulasRanges = arrayFormuals;
	}

	public void addConditionalFormats(List<RangeWrapper> conditionalFormats) {

		if (this.conditionalFormatRanges == null) {
			this.conditionalFormatRanges = new ArrayList<RangeWrapper>();
		}

		this.conditionalFormatRanges = conditionalFormats;
	}

	public void addDataValidations(List<RangeWrapper> dataValidations) {

		if (this.dataValidationRanges == null) {
			this.dataValidationRanges = new ArrayList<RangeWrapper>();
		}

		this.dataValidationRanges = dataValidations;
	}

	public void addMergeCells(List<RangeWrapper> mergeCells) {

		if (this.mergeCells == null) {
			this.mergeCells = new ArrayList<RangeWrapper>();
		}

		this.mergeCells = mergeCells;
	}

	public void addCheckBox(List<RangeWrapper> checkBoxList) {

		if (this.checkBox == null) {
			this.checkBox = new ArrayList<RangeWrapper>();
		}

		this.checkBox = checkBoxList;
	}

	public void addColumnLevelFormats(List<RangeWrapper> rangeWrapper) {
		if (this.columnLevelFormats == null) {
			this.columnLevelFormats = new ArrayList<RangeWrapper>();
		}
		this.columnLevelFormats = rangeWrapper;
	}

	public void addHiddenRows(List<RangeWrapper> hiddenRowList) {

		if (this.hiddenRows == null) {
			this.hiddenRows = new ArrayList<RangeWrapper>();
		}

		this.hiddenRows = hiddenRowList;
	}

	public void addHiddenColumns(List<RangeWrapper> hiddenColumnList) {

		if (this.hiddenColumns == null) {
			this.hiddenColumns = new ArrayList<RangeWrapper>();
		}

		this.hiddenColumns = hiddenColumnList;
	}

	public void setCellMetaHeader(int cellMetaHeader) {

		this.cellMetaHeader = cellMetaHeader;
	}

	public void setActiveInfoBean(ActiveInfoBean activeInfoBean) {
		this.activeInfoBean = activeInfoBean;
	}

	public void setInsertDeleteCellRange(List<RangeWrapper> insertDeleteCellRangeWrapper) {

		if (this.insertDeleteCellRangeWrapper == null) {
			this.insertDeleteCellRangeWrapper = new ArrayList<RangeWrapper>();
		}

		this.insertDeleteCellRangeWrapper = insertDeleteCellRangeWrapper;
	}

	public void addFormRange(RangeWrapper formRanges) {

		this.formRanges = formRanges;
	}

	public void setPivotRanges(List<RangeWrapper> pivotRanges) {
		this.pivotRagnes = pivotRanges;
	}

	public void setMaxUsedCells(RangeWrapper maxUsedCells) {

		this.maxUsedCells = maxUsedCells;
	}

	public void setFaultySheets(HashSet<String> faultySheets) {
		this.faultySheets = faultySheets;
	}

	public void setImageBean(List<SheetImageBean> imageBeanList) {

		this.imageBeans = imageBeanList;
	}

	public void setButtonBean(List<ButtonBean> buttonBeans) {
		this.buttonBeans = buttonBeans;
	}

	public void setChartBean(List<ChartBean> chartBean) {
		this.chartBean = chartBean;
	}

	public void setSlicerBean(List<SlicerBean> slicerBeans){
		this.slicerBeans = slicerBeans;
	}
	public void setTimelineBean(List<TimelineBean> timelineBeans){
		this.timelineBeans = timelineBeans;
	}
	public List<SlicerBean> getSlicerBeans(){
		return this.slicerBeans;
	}
	public List<TimelineBean> getTimelineBeans(){
		return this.timelineBeans;
	}
	public List<RangeWrapper> getColumnHeaderRanges() {
		return columnHeaderRanges;
	}

	public List<RangeWrapper> getRowHeaderRanges() {
		return rowHeaderRanges;
	}

	public Map<String, List<FilterBean>> getFilterBean() {
		return filterBeanMap;
	}

	public List<FreezeWrapper> getFreezePanes() {
		return freezePanes;
	}

	public List<RangeWrapper> getDataValidationRanges() {
		return dataValidationRanges;
	}

	public List<RangeWrapper> getCondtionalFormatRanges() {
		return conditionalFormatRanges;
	}

	public List<RangeWrapper> getArrayFormulasRanges() {
		return arrayFormulasRanges;
	}

	public List<RangeWrapper> getHiddenRows() {
		return hiddenRows;
	}

	public List<RangeWrapper> getHiddenColumns() {
		return hiddenColumns;
	}

	public List<RangeWrapper> getMergeCells() {
		return mergeCells;
	}

	public List<RangeWrapper> getColumnLevelFormats() {
		return columnLevelFormats;
	}

	public List<RangeWrapper> getSelectedRanges() {
		return selectedRanges;
	}

	public HashMap<String, List<Long>> getDependentCellsMap() {
		return dependentCellsMap;
	}

	public CellResponse getCellsReponse() {
		return cellsResponse;
	}

	public int getCellMetaHeader() {

		return cellMetaHeader;
	}

	public ActiveInfoBean getActiveInfoBean() {
		return activeInfoBean;
	}

	public List<RangeWrapper> getinsertDeleteCellRangeWrapper() {
		return insertDeleteCellRangeWrapper;
	}

	public RangeWrapper getFormRanges() {

		return this.formRanges;
	}

	public List<RangeWrapper> getPivotRanges() {
		return this.pivotRagnes;
	}

	public List<RangeWrapper> getAppliedConditionalCellStyleRanges() {
		return appliedConditionalCellStyleRanges;
	}

	public RangeWrapper getMaxUsedCells() {

		return this.maxUsedCells;
	}

	public HashSet<String> getFaultySheets() {
		return this.faultySheets;
	}

	public List<HideGridWrapper> getHideGrid() {
		return this.hideGrid;
	}

	public List<RangeWrapper> getCheckBoxRanges() {
		return this.checkBox;
	}

	public List<SheetImageBean> getImageBeans() {
		return this.imageBeans;
	}

	public List<ButtonBean> getButtonBeans() {
		return this.buttonBeans;
	}

	public List<ChartBean> getChartBean() {
		return this.chartBean;
	}

	public void setCFRangeList(Map<String, List<RangeWrapper>> rangeList) {
		this.cfRangeList = rangeList;
	}

	public Map<String, List<RangeWrapper>> getCFRangeList() {
		return this.cfRangeList;
	}

	public void setCFFaultyRangeList(Map<String, List<RangeWrapper>> faultyRangeList) {
		this.cfFaultyRangeList = faultyRangeList;
	}

	public Map<String, List<RangeWrapper>> getCFFaultyRangeList() {
		return this.cfFaultyRangeList;
	}

	public List<SparklineBean> getSparklineBeanList() {
		return sparklineBeanList;
	}

	public void setSparklineBeanList(List<SparklineBean> sparklineBeanList) {
		this.sparklineBeanList = sparklineBeanList;
	}

	public void setZiaInfoList(Map<String, List<String>> ziaInfoList) {
		this.ziaInfoList = ziaInfoList;
	}

	public Map<String, List<String>> getZiaInfoList() {
		return this.ziaInfoList;
	}

	public void setSheetViewList(List<SheetWrapper> sheetViewList) {
		this.sheetViewList = sheetViewList;
	}

	public List<SheetWrapper> getSheetViewList() {
		return this.sheetViewList;
	}

	public void setSheetRtlList(List<SheetWrapper> sheetRtlList) {
		this.sheetRtlList = sheetRtlList;
	}

	public List<SheetWrapper> getSheetRtlList() {
		return this.sheetRtlList;
	}

	public void setSheetZoomList(List<SheetWrapper> sheetZoomList) {
		this.sheetZoomList = sheetZoomList;
	}

	public List<SheetWrapper> getSheetZoomList() {
		return this.sheetZoomList;
	}

	public void setDataCleaningBean(DataCleaningBean bean) {
		this.dataCleaningBean = bean;
	}

	public DataCleaningBean getDataCleaningBean() {
		return this.dataCleaningBean;
	}

	public List<PicklistBean> getPicklistBeanList() {
		return picklistBean;
	}

	public void setPicklistBean(List<PicklistBean> picklistBean) {
		this.picklistBean = picklistBean;
	}

	public List<TableBean> getTableBean() {
		return tableBean;
	}

	public void setTableBean(List<TableBean> tableBean) {
		this.tableBean = tableBean;
	}

	public List<SheetImageBean> getImageBean() {
		return this.imageBeans;
	}

	public void setImageBeans(List<SheetImageBean> imageBeans) {
		this.imageBeans = imageBeans;
	}

	public void addPublishedViewWrapper(List<PublishedViewWrapper> publishViewWrapper) {
		this.publishedViewWrapper = publishViewWrapper;
	}

	public List<PublishedViewWrapper> getPublishedViewWrapper() {
		return this.publishedViewWrapper;
	}

	public void setRootMergeCellsData(List<CellResponse> rootMergeCellsData, Map<String, JSONArrayWrapper> rootMergeCellCfData)
	{
		this.rootMergeCellsData = rootMergeCellsData;
		this.rootMergeCellsCfData = rootMergeCellCfData;
	}

	public List<CellResponse> getRootMergeCellsData() {
		return this.rootMergeCellsData;
	}

	public Map<String, JSONArrayWrapper> getRootMergeCellsCfData()
	{
		return this.rootMergeCellsCfData;
	}

	public void setDataConnectionBean(DataConnectionBean dataConnectionBean) {

		this.dataConnectionBean = dataConnectionBean;
	}

	public DataConnectionBean getDataConnectionBean() {
		return this.dataConnectionBean;
	}

	public void setGroupingRangeWrapper(RangeWrapper rangeWrapper) {
		if (this.groupingRangeWrapper == null) {
			this.groupingRangeWrapper = new ArrayList<>();
		}

		this.groupingRangeWrapper.add(rangeWrapper);
	}

	public void setGroupingRangeWrapper(List<RangeWrapper> rangeWrapper) {
		if(this.groupingRangeWrapper == null) {
			this.groupingRangeWrapper = new ArrayList<>();
		}

		this.groupingRangeWrapper.addAll(rangeWrapper);
	}

	public List<RangeWrapper> getGroupingRangeWrapper() { return this.groupingRangeWrapper; }

	public List<KnitChartsSheetBean> getKnitChartsSheetBeans() {
		return knitChartsSheetBeans;
	}

	public void setKnitChartsSheetBeans(List<KnitChartsSheetBean> knitChartsSheetBeans) {
		this.knitChartsSheetBeans = knitChartsSheetBeans;
	}

	public List<KnitChartsModifiedBean> getKnitChartsModifiedBeans() {
		return knitChartsModifiedBeans;
	}

	public void setKnitChartsModifiedBeans(List<KnitChartsModifiedBean> modifiedChartBeans) {
		this.knitChartsModifiedBeans = modifiedChartBeans;
	}

	public List<KnitChartsBean> getKnitChartsBeans() {
		return knitChartsBeans;
	}

	public void setKnitChartsBeans(List<KnitChartsBean> knitChartsBeans) {
		this.knitChartsBeans = knitChartsBeans;
	}

	public List<KnitChartsClipBean> getKnitChartsClipBeans() {
		return knitChartsClipBeans;
	}

	public void setKnitChartsClipBeans(List<KnitChartsClipBean> knitChartsClipBeans) {
		this.knitChartsClipBeans = knitChartsClipBeans;
	}
}
