/* $Id$ */

package com.adventnet.zoho.websheet.model.response.holder;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.response.beans.ActionIdentifierBean;
import com.adventnet.zoho.websheet.model.response.beans.DataValidationMessageInfo;
import com.adventnet.zoho.websheet.model.response.beans.ErrorBean;
import com.adventnet.zoho.websheet.model.response.beans.FindReplaceResponseBean;
import com.adventnet.zoho.websheet.model.response.beans.KnitChartsClipBean;
import com.adventnet.zoho.websheet.model.response.beans.NotificationBean;
import com.adventnet.zoho.websheet.model.response.beans.ProtectionBean;

import java.util.ArrayList;

import com.adventnet.zoho.websheet.model.util.DataRange;

import com.adventnet.zoho.websheet.model.response.helper.RangeWrapper;
import com.adventnet.zoho.websheet.model.response.helper.SheetWrapper;
import com.zoho.sheet.util.FilterBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class UserSpecificResponseHolder {
	
	
		private	List <RangeWrapper>	rangeWrapperList;
		private	List<SheetWrapper>	sheetWrapperList;
		private	List<ProtectionBean>	protectedSheetResponseBeans;
		private	List<ProtectionBean>	protectedRangeResponseBeans;
		private JSONObjectWrapper serverClipObject;
                private JSONObjectWrapper                              clipChartStyleObject;

				private KnitChartsClipBean knitChartsClipBean;

		private DataValidationMessageInfo dataValidationMessageInfo;
		private FindReplaceResponseBean findReplaceResponseBean;
		private ActionIdentifierBean actionIdentifierBean;
                private List <RangeWrapper> rangeHighlights;
                private List<NotificationBean> notificationBeans;
                private ErrorBean errorBean;
                private int filteredRowCount = -1;
                private Map<String, List<FilterBean>> filterBeans = new HashMap<>();
                private List<DataRange> rangeList;
                private RangeWrapper fetchRangeWrapper;
                
//		public List <RangeWrapper> getPrtoectedRangeWrapperList() {
//			return rangeWrapperList;
//		}
	public List<ProtectionBean> getPrtoectedRangeBeans()
	{
		return protectedRangeResponseBeans;
	}

	//		public List<SheetWrapper> getPrtoectedSheetWrapperList() {
//			return sheetWrapperList;
//		}
	public List<ProtectionBean> getPrtoectedSheetBeans()
	{
		return protectedSheetResponseBeans;
	}

	//		public void setPrtoectedRangeWrapper(List <RangeWrapper> protectRanges) {
//			if(this.rangeWrapperList == null){
//				
//				this.rangeWrapperList	=	new		ArrayList<RangeWrapper>();
//			}
//			
//			this.rangeWrapperList  = protectRanges;
//		}
	public void setPrtoectedRangeBeans(List<ProtectionBean> protectRanges)
	{
		if(this.protectedRangeResponseBeans == null)
		{

			this.protectedRangeResponseBeans = new ArrayList<ProtectionBean>();
		}

		this.protectedRangeResponseBeans = protectRanges;
	}


	public void setDataValidationMessage(DataValidationMessageInfo dataValidationMessageInfo)
	{
		this.dataValidationMessageInfo = dataValidationMessageInfo;
	}

	/*public void setPrtoectedSheetWrapper(List <SheetWrapper> sheetWrappers) {
		if(this.sheetWrapperList == null)	{

			this.sheetWrapperList	=	new		ArrayList<SheetWrapper>();
		}


		this.sheetWrapperList = sheetWrappers;
	}*/
	public void setPrtoectedSheetBeans(List<ProtectionBean> protectedSheetBeans)
	{
		if(this.protectedSheetResponseBeans == null)
		{

			this.protectedSheetResponseBeans = new ArrayList<ProtectionBean>();
		}

		this.protectedSheetResponseBeans = protectedSheetBeans;
	}

	public DataValidationMessageInfo getdataValidationMessageInfo()
	{
		return dataValidationMessageInfo;
	}

	public void setServerClipObj(JSONObjectWrapper serverClipObj)
	{
		this.serverClipObject = serverClipObj;
	}

	public JSONObjectWrapper getServerClipObj()
	{
		return this.serverClipObject;
	}

	public void setChartStyleClipObj(JSONObjectWrapper chartClipStyle)
	{
		this.clipChartStyleObject = chartClipStyle;
	}

	public JSONObjectWrapper getChartStyleClipObj()
	{
		return this.clipChartStyleObject;
	}

	public FindReplaceResponseBean getFindReplaceResponseBean()
	{
		return findReplaceResponseBean;
	}

	public void setFindReplaceResponseBean(FindReplaceResponseBean findReplaceResponseBean)
	{
		this.findReplaceResponseBean = findReplaceResponseBean;
	}

	public void setActionIdentifierBean(ActionIdentifierBean actionIdentifierBean)
	{

		this.actionIdentifierBean = actionIdentifierBean;
	}

	public ActionIdentifierBean getActionIdentifierBean()
	{

		return this.actionIdentifierBean;
	}

	public void setRangeHighlight(List<RangeWrapper> rangeWrappers)
	{
		this.rangeHighlights = rangeWrappers;
	}

	public List<RangeWrapper> getRangeHighlight()
	{
		return this.rangeHighlights;
	}

	public void setUserNotification(List<NotificationBean> notificationBeans)
	{
		this.notificationBeans = notificationBeans;
	}

	public List<NotificationBean> getUserNotification()
	{
		return this.notificationBeans;
	}

	public void setErrorBean(ErrorBean errorBean)
	{
		this.errorBean = errorBean;
	}

	public ErrorBean getErrorBean()
	{
		return this.errorBean;
	}

	public void setFilteredRowCount(int filteredRowCount)
	{
		this.filteredRowCount = filteredRowCount;
	}

	public int getFilterdRowCount()
	{
		return this.filteredRowCount;
	}

	public void setRangeList(List<DataRange> rangeList)
	{
		this.rangeList = rangeList;
	}

	public List<DataRange> getRangeList()
	{
		return this.rangeList;
	}

	public KnitChartsClipBean getKnitChartsClipBean() {
		return knitChartsClipBean;
	}

	public void setKnitChartsClipBean(KnitChartsClipBean knitChartsClipBean) {
		this.knitChartsClipBean = knitChartsClipBean;
	}

	public Map<String, List<FilterBean>> getFilterBeans()
	{
		return filterBeans;
	}

	public void addFilterBean(String asn, List<FilterBean> filterBeans)
	{
		this.filterBeans.put(asn, filterBeans);
	}

	public void setFilterBeans(Map<String, List<FilterBean>> filterBeans)
	{
		this.filterBeans = filterBeans;
	}

	public void setFetchRangeWrapper(RangeWrapper fetchRangeWrapper)
	{
		this.fetchRangeWrapper = fetchRangeWrapper;
	}

	public RangeWrapper getFetchRangeWrapper()
	{
		return this.fetchRangeWrapper;
	}
}

