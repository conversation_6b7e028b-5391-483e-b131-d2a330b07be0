// $Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.Cell.Type;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Stack;

/**
 *
 * <AUTHOR>
 */
public class ZArray implements ZArrayI
{
    private List array = null;
    private final int rowSize;
    private final int colSize;
    

    public ZArray(List array, int rowSize, int colSize)
    {
        this.array = array;
        this.rowSize = rowSize;
        this.colSize = colSize;
    }

    public int getRowSize()
    {
        return rowSize;
    }

    public int getColSize()
    {
        return colSize;
    }

    public int getSize()
    {
        return this.getColSize() * this.getRowSize();
    }

    public List getArray()
    {
        return array;
    }


    public boolean isBound(int rowIndex, int colIndex)
    {
        return rowIndex >= 0 && colIndex >= 0 && rowIndex < this.getRowSize() && colIndex < this.getColSize();
    }

    public Value getValue(int rowIndex, int colIndex)
    {
        if(!isBound(rowIndex, colIndex))
        {
            return Value.getInstance(Type.ERROR, Cell.Error.NA);
        }

        int index = (rowIndex * colSize) + colIndex;
        if(index >= array.size())
        {
            return Value.EMPTY_VALUE;
        }
        Object value = array.get(index);
        if(value instanceof Value)
        {
            return (Value)value;
        }        
        return CellImpl.getAsValueObject(value);
    }

    @Override
    public Value getValueWithPattern(int rowIndex, int colIndex)
    {
        return getValue(rowIndex, colIndex);
    }
    
    public void addArrayValues(Stack stack) // Used in Chitest function
    {
        for(int i = 0; i < this.getRowSize(); i++)
        {
            for(int j = 0; j < this.getColSize(); j++)
            {
                Object value = getValue(i,j);
                stack.push(value);
            }
        }
    }
    
    public ZArray getSubArray(int startRow, int startCol, int endRow, int endCol)
    {
        List newList = new ArrayList();
        int newRowSize = endRow - startRow + 1;
        int newColSize = endCol - startCol + 1;
        for(int i = 0; i <= endRow; i++)
        {
            if(i < startRow)
            {
                continue;
            }
            for(int j = 0; j <= endCol; j++)
            {
                if(j < startCol)
                {
                    continue;
                }
                else
                {
                    newList.add(array.get(i*colSize + j));
                }
            }
        }
        return new ZArray(newList, newRowSize, newColSize);
    }
    

    private Integer hashCode = null;
    @Override
    public int hashCode() {
        if(hashCode == null) {
            this.hashCode = Objects.hash(array, rowSize, colSize);
        }
        return hashCode;
    }

    @Override
    public boolean equals(Object obj) {
        
        if(obj instanceof ZArray)
        {
            ZArray thatArray = (ZArray) obj;
            if(this.getRowSize() == thatArray.getRowSize())
            {
                return true;
            }
            if(this.getColSize() == thatArray.getColSize())
            {
                return true;
            }
            if(this.getArray().equals(thatArray.getArray())) {
                return true;
            }
        }
        
            return false;
    }

    public String toString()
    {
        return "rowSize  : "+rowSize+"   colSize : "+colSize+"   array : "+array;//No I18N
    }

    public void updateValues(ZArrayI subZArray, final int sr, final int sc) {
        if (!this.isBound(sr, sc)) {
            return;//precautionary
        }
        if (!this.isBound(sr + subZArray.getRowSize() - 1, sc + subZArray.getColSize() - 1)) {
            return;//precautionary
        }
        for (int i = 0; i < subZArray.getRowSize(); i++) {
            for (int j = 0; j < subZArray.getColSize(); j++) {
                int indexInArray = this.getColSize() * (sr + i) + (sc+ j);
                this.array.set(indexInArray, subZArray.getValue(i, j));
            }
        }
    }

    @Override
    public boolean isMadeofRange() {
        return false;
    }
}
