//* $Id$ */
package com.adventnet.zoho.websheet.model.util;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.google.gson.JsonObject;

import java.util.*;

public class Constants {

	public static String version = "M9_7";
	public static final String X_MDM_TOKEN = EnginePropertyUtil.getSheetPropertyValue("X_MDM_TOKEN_KEY") ;
	public static final long LISTENER_WAITING_TIME = Long.parseLong(EnginePropertyUtil.getSheetPropertyValue("LISTENER_WAITING_TIME")); //No I18N
	public static final String VERSION = EngineConstants.version;
	public static final String CURRENTTIME = "CURRENTTIME";

	public static final String DEFAULT_FILE_FORMAT = ".sxc";
	public static final String XLS_FILE_FORMAT = ".xls";
	public static final String COMPRESSED_FILE_FORMAT = ".gz";
	public static final String LOCATION = "/";//PersistenceInitializer.getConfigurationValue("WebsheetFilesLocation");
	public static final String FORMULA_ERROR = "#Invalid Formula!"; // #VALUE!

	public static final String ALLOW_TO_WRITE = "ALLOW_TO_WRITE";
	public static final String ALLOW_TO_COMMENT = "ALLOW_TO_COMMENT"; //NO I18N

	public static final String DOCUMENT_ID = "DOCUMENT_ID";
	public static final String RESOURCE_ID = "RESOURCE_ID";//No internationalization
	public static final String DOCUMENT_NAME = "DOCUMENT_NAME";
	public static final String ISDOCUMENT_OWNER = "ISDOCUMENT_OWNER";
	public static final String IS_CO_OWNER = "IS_CO_OWNER"; //NO I18N

	public static final String DOC_AUTHOR_ID = "DOC_AUTHOR_ID";
	public static final String DOC_AUTHOR_NAME = "DOC_AUTHOR_NAME";

	public static final String DOCS_SPACE_ID = "DOCS_SPACE_ID";//NO I18N

	public static final String CHARTSDO = "CHARTSDO";

	public static final String SHEETVALUES = "SHEETVALUES";
	public static final String SHEETFORMULAS = "SHEETFORMULAS";
	public static final String SHEETSTYLES = "SHEETSTYLES";
	public static final String SHEETANNOTS = "SHEETANNOTS";
	//public static final String SHEETDISCUSSIONS = "SHEETDISCUSSIONS";
	public static final String CELLLEFTBORDERS = "CELLLEFTBORDERS";
	public static final String CELLRIGHTBORDERS = "CELLRIGHTBORDERS";
	public static final String CELLTOPBORDERS = "CELLTOPBORDERS";
	public static final String CELLBOTTOMBORDERS = "CELLBOTTOMBORDERS";
	public static final String COLUMNWIDTH = "COLUMNWIDTH";
	public static final String ROWHEIGHT = "ROWHEIGHT";
	public static final String COLSPAN = "COLSPAN";
	public static final String ROWSPAN = "ROWSPAN";
	public static final String CELL_HYPERLINKS = "CELL_HYPERLINKS";

	public static final String NO_OF_COLUMNS = "NO_OF_COLUMNS";
	public static final String NO_OF_ROWS = "NO_OF_ROWS";
	public static final String NO_OF_CELLS = "NO_OF_CELLS";	//No I18N

	public static final String DEFAULTCOLUMNWIDTH = "DEFAULTCOLUMNWIDTH";
	public static final String DEFAULTROWHEIGHT = "DEFAULTROWHEIGHT";
        public static final String DEFAULTFONTNAME = "DEFAULTFONTNAME"; //No I18N
        public static final String DEFAULTFONTSIZE = "DEFAULTFONTSIZE"; //No I18N
	public static final String TABLEWIDTH = "TABLEWIDTH";
	public static final String TABLEHEIGHT = "TABLEHEIGHT";
	public static final String SHEETNAMES = "SHEETNAMES";
	public static final String ACTIVESHEETNAME = "ACTIVESHEETNAME";
	public static final String ACTIVESHEETINDEX = "ACTIVESHEETINDEX";

	//For our own calculation engine
	//	public static final String ENGINEHASHTABLE ="ENGINEHASHTABLE";
	//	public static final String ERRORSTRING = "<errorcell>#FN_NOT_SUPPORTED!</errorcell>";
	//	public static final String ENGINE_ODS_FILE_FORMAT = ".ods";
	//	public static final String IS_ENGINE_SUPPORTED = "IS_ENGINE_SUPPORTED";
	public static final String BRIDGENO = "BRIDGENO"; // No I18N
	public static final String LIBOHOST = "LIBOHOST"; // No I18N
	public static final String IMAGE_EXTN_JPG = "jpg"; //NO I18N
	public static final String IMAGE_EXTENSION = ".jpg"; // No I18N
	public static final String IMAGE_EXTN_PNG = "png"; //NO I18N
	public static final boolean UPDATEIMAGEINOO = false;
	// Already defined in EngineConstants
	//public static boolean switchengine = Boolean.parseBoolean(EnginePropertyUtil.getPropertyValue("switchengine"));
	//public static boolean switchOO = Boolean.parseBoolean(EnginePropertyUtil.getPropertyValue("switchoo"));

	public static Properties rebrand_Properties = new Properties();
	public static JSONObjectWrapper rebrand_Urls = new JSONObjectWrapper();
        public static JsonObject staticMap = EnginePropertyUtil.getStaticMap();
        public static JsonObject clientStaticMap = EnginePropertyUtil.getclientStaticMap();
	public static JsonObject integrityHashViewBasedMap = EnginePropertyUtil.integrityHashViewBasedMap();
	public static JsonObject integrityHashMap = EnginePropertyUtil.getIntegrityHashMap();
	public static JsonObject previewStaticMap = EnginePropertyUtil.getPreviewStaticMap();
	public static JsonObject publicTemplatesMap = EnginePropertyUtil.getPublicTemplatesMap();
	public static List<String> localeInfo = new ArrayList<String>(20);
	public static String rebrandMessagesPath = "";
	public static HashMap error_Map = new HashMap();

	public static Map<String, Set<String>> viewExludes=new HashMap<String, Set<String>>();

	public static List<String> authRead=new ArrayList();
        public static List<String> authReadComment=new ArrayList();
	public static List<String> authReadWrite=new ArrayList();
	public static List<String> authCoowner=new ArrayList();
	public static List<String> authOwner=new ArrayList();

	public static List<String> unauthRead=new ArrayList();
        public static List<String> unauthReadComment=new ArrayList();
	public static List<String> unauthReadWrite=new ArrayList();
	public static List<String> unauthCoowner=new ArrayList();
	public static List<String> unauthOwner=new ArrayList();

	public static String htmlVersionPath = "";

	public static final String PWPROTECTED = "PWPROTECTED";												// No I18N
	public static final String ALLOWED_FILE_SIZE = EnginePropertyUtil.getSheetPropertyValue("CONVERSION_IMPORT_FILE_SIZE_IN_MB");
	public static final String ALLOWED_FILE_SIZE_NEW = EnginePropertyUtil.getSheetPropertyValue("CONVERSION_IMPORT_FILE_SIZE_IN_MB_NEW");
	public static final String ERRORIMPORT = "ERRORIMPORT";												// No I18N
	public static final String HASHTABLE = "HASHTABLE";													// No I18N
	public static final String CURRENTSHEETNAME = "ACTIVESHEETNAME";									// No I18N
	public static String docNameDisallowedCharsRegex = "['\"<>#?/%&*\\\\]";
	public static final String CSV_FILE_FORMAT = ".csv";												// No I18N
	public static final String TSV_FILE_FORMAT = ".tsv";												// No I18N
	public static final String FILE_STORE_CONF = "Spreadsheets";										// No I18N
	public static final String XLSX_FILE_FORMAT = ".xlsx";												// No I18N
	public static final String FILE_FORMAT = "FILE_FORMAT";												// No I18N
	public static final String ODS_FILE_FORMAT = ".ods";												// No I18N

	// For VIEW
	public static final String NEW_DOC_VIEW = "NEW_VIEW";												// No I18N
	public static final String OPEN_DOC_VIEW = "OPEN_VIEW";												// No I18N
	public static final String SHARE_VIEW = "SHARE_VIEW";											// No I18N
	public static final String TRASH_VIEW = "TRASH_VIEW";											// No I18N
	public static final String GUEST_VIEW = "GUEST_VIEW";											// No I18N
	public static final String PUBLIC_VIEW = "PUBLIC_VIEW";											// No I18N
	public static final String THROWAWAY_VIEW = "THROWAWAY_VIEW";										// No I18N
	public static final String REMOTE_VIEW = "REMOTE_VIEW";											// No I18N
	public static final String VERSION_VIEW = "VERSION_VIEW";											// No I18N
	public static final String PUBLISH_RANGE_VIEW = "PUBLISH_RANGE_VIEW";								// No I18N
	public static final String PRINT_PREVIEW = "PRINT_PREVIEW";										// No I18N
	/**
	 * In this view, on clicking 'save' button, documents will be pushed to
	 * a remote server for storage
	 */
	public static final String REMOTE_SAVE_VIEW = "REMOTE_SAVE_VIEW";										// No I18N
	public static final String INTERNAL_VIEW = "INTERNAL_VIEW";	//Used for docs internal API				// No I18N

	public static final String AUTHORIZED = "AUTHORIZED";												// No I18N
	public static final String NOT_AUTHORIZED = "NOT_AUTHORIZED";										// No I18N
	public static final String SHARED_READ_ONLY = "SHARED_READ_ONLY";									// No I18N
	public static final String SHARED_READ_WRITE = "SHARED_READ_WRITE";									// No I18N
	public static final String SHEET_SHARED_READ_ONLY = "SHEET_SHARED_READ_ONLY";						// No I18N
	public static final String SHEET_SHARED_READ_WRITE = "SHEET_SHARED_READ_WRITE";						// No I18N
	public static final String SHEET_BASED_SHARE = "SHEET_BASED_SHARE";									// No I18N

	public static final boolean DO_INDEX = true;
	public static final boolean SEARCH_GRID_ENABLED = (EnginePropertyUtil.getSheetPropertyValue("EnableSearchGrid") != null && EnginePropertyUtil.getSheetPropertyValue("EnableSearchGrid").equals("true")); // No I18N

	public static final String PUBLICGRAPH_LOCATION = EnginePropertyUtil.getSheetPropertyValue("WebsheetFilesLocation") + "publicgraphs/";   // No I18N

	public static final boolean ISFACEBOOK = (System.getProperty("server.mode") != null && System.getProperty("server.mode").equals("facebook"));
	public static final boolean IS_DISASTER_RECOVERY_SERVER = (System.getProperty("app.readonly.mode") != null) ? Boolean.valueOf(System.getProperty("app.readonly.mode")) : false;

	public static final String DEFAULT_BRAND_NAME = "zoho"; // No I18N
	public static final String BRANDNAME = (System.getProperty("server.mode") != null) ? System.getProperty("server.mode") : DEFAULT_BRAND_NAME; // No I18N
	public static final boolean ISREBRANDED = (DEFAULT_BRAND_NAME.equals(BRANDNAME)) ? false : true;					// No I18N
	public static final String COPY_DOCUMENT = "duplicate";	// No I18N
	//public static final String SAVE_TO_MYACCOUNT = "saveToMyAccount";									// No I18N
	public static final String ENABLE_SAVE_TO_ZOHO_SHEET = "enableSaveToZohoSheet";						// No I18N
	public static final String AUTHENTICATE_SAVE_TO_MYACCOUNT = "authenticatesavetomyaccount";			// No I18N
	public static final String CREATE_CHARTS = "createcharts";									// No I18N
	public static final String VERIFY_ACCOUNT = "verifyAccount";										// No I18N
	public static final String SAVEAS_NEW_DOCUMENT = "saveAs";											// No I18N
	public static final String CALLED_FROM_URL = "CALLED_FROM_URL";										// No I18N
	//For Document Comments
	public static final String COMMENTS_COUNT = "COMMENTS_COUNT";										// No I18N
	public static final String VIEW = "VIEW";															// No I18N
	public static final String LOCKED_USER = "LOCKED_USER";												// No I18N
	public static final String USEDROW = "USEDROW";														// No I18N
	public static final String USEDCOL = "USEDCOL";														// No I18N
	public static final String ROW_DETAILS = "ROWDETAILS";												// No I18N
	public static final String COLUMN_DETAILS = "COLUMNDETAILS";										// No I18N

	//For V-Align
	public static final String VALIGN_BOTTOM = "bottom";												// No I18N
	public static final String VALIGN_MIDDLE = "middle";												// No I18N
	public static final String VALIGN_TOP = "top";														// No I18N

	public static final String NOWRAP = "nW";															// No I18N
	public static final String WRAP = "W";																// No I18N

	public static final boolean ISBAIHUI = (System.getProperty("server.mode") != null && System.getProperty("server.mode").equals("baihui")) ? true : false;		// No I18N
	public static final boolean ISFUJIXEROX = (System.getProperty("server.mode") != null && System.getProperty("server.mode").equals("fujixerox")) ? true : false;	// No I18N

	public static final String HGT_CHANGED_ROWS = "HGT_CHANGED_ROWS";									// No I18N
	public static final String LITERALCOLSPAN = "LITERALCOLSPAN";										// No I18N
	public static final String LITERALROWSPAN = "LITERALROWSPAN";										// No I18N
	public static final String CONDITIONAL_FORMAT = "CONDITIONAL_FORMAT";								// No I18N

	//below constants used in jsp files
	public static final String MASHUP = "MASHUP";   // No I18N
	public static final String CSV_MASHUP   = "CSV_MASHUP"; // No I18N
	public static final boolean ISNTT = (System.getProperty("server.mode") != null && System.getProperty("server.mode").equals("ntt")) ? true : false;   // No I18N
	public static final String DEFAULTTHEME = "grey";   // No I18N
	public static final String INTEGRATION_AUTO_COMPLETE = "INTEGRATION_AUTO_COMPLETE";   // No I18N
	public static final String INTEGRATION_DATE_PICKER = "INTEGRATION_DATE_PICKER";   // No I18N
	public static final boolean ISMKI = (System.getProperty("server.mode") != null && System.getProperty("server.mode").equals("mki")) ? true : false;   // No I18N
	public static final boolean ENABLERTC = !ISMKI;
	public static final String API_KEY_ID = "API_KEY_ID";   // No I18N
	public static final String JS_VERSION = EngineConstants.jsVersion;   // No I18N
	public static final boolean ENABLECACHE = true;
	public static final boolean DELETEHTML = true;
	public static final boolean DO_INDEX_SEARCH = true;
	public static final String ISPUBLISHED = "isPublished";   // No I18N
	public static final String OOLOADED = "OOLOADED";   // No I18N
	public static final String DOCUMENT_CATEGORY = "DOCUMENT_CATEGORY";   // No I18N
	public static final String LOCK = "LOCK";   // No I18N
	public static final String CORPORATEDBPREFIX = "";
	public static final String HTMLSTRING = "HTMLSTRING";   // No I18N
	public static final int READ_ONLY_PERMISSION_LEVEL = 0;
	public static final int READ_WRITE_PERMISSION_LEVEL = 1;
	public static final int CO_OWNER_PERMISSION_LEVEL = 2;
	public static final int READ_COMMENT_PERMISSION_LEVEL = 3;

	//For Zoho Desktop
	public static final String EDITORACTION = "EDITORACTION";   // No I18N
	public static final String EDITORTHEME = "EDITORTHEME";   // No I18N
	public static final String EDITORTAGS = "EDITORTAGS";   // No I18N

	//For RTC
	public static final String COLLAB_ID = "COLLAB_ID";   // No I18N
	public static final String LISTOFCOLLABIDS = "LISTOFCOLLABIDS";   // No I18N

	//Themes
	public static final String PUBLISHEDTHEME = "publishedTheme";   // No I18N
	public static final String[] SUPPORTEDTHEMES = {"green", "grey", "blue"};   // No I18N

	//For Range Publishing
	public static final String PUB_RANGE = "PUB_RANGE";						// No I18N
	public static final String PUB_ENC_URL = "PUB_ENC_URL";// No I18N
	public static final String PUB_RANGE_SCOL = "PUB_RANGE_SCOL";// No I18N
	public static final String PUB_RANGE_ECOL = "PUB_RANGE_ECOL";// No I18N
	public static final String PUB_RANGE_SROW = "PUB_RANGE_SROW";// No I18N
	public static final String PUB_RANGE_EROW = "PUB_RANGE_EROW";// No I18N
	public static final String PUB_DATE = "PUB_DATE";// No I18N
	public static final String PUB_SHEETS = "PUB_SHEETS";// No I18N
	public static final String ADDITIONAL_INFO = "ADDITIONAL_INFO";// No I18N
	public static final String TOTAL_ROW_HEIGHT = "trh";// No I18N
	public static final String TOTAL_COLUMN_WIDTH = "tcw";// No I18N

	//For Print Preview
	public static final String PRINT_KEY = "PRINT_KEY";// No I18N
	public static final String PREVIEW_DEFAULT_CLASS_NAME = Constants.ISBAIHUI ? "PCcD" : "PcD";// No I18N
	public static final boolean IS_PREVIEW = false;	// No I18N
	public static final int DEFAULT_ROWCOUNT_INLOAD = 40;// No I18N

	public static final int COLHEAD_HEIGHT = 21;
	public static final String DEFAULT_CLASS_NAME = "cD";// No I18N

	public static final String SCROLLTYPE = "SCROLLTYPE";// No I18N
	public static final String SCROLLSTATUS = "SCROLLSTATUS";// No I18N
	public static final String ROWSARY = "ROWSARY";// No I18N
        public static final String COLSARY = "COLSARY";// No I18N

	public static final String TOTAL_ENDROW = "TOTAL_ENDROW";// No I18N
	public static final String ROWAPPENDFLAG = "ROWAPPENDFLAG";// No I18N
	public static final String CELL_RANGE_DETAIL = "HtmlRange";// No I18N
	public static final String SSWIDTH = "100%";// No I18N
	public static final String SSHEIGHT = "100%";// No I18N
	public static final String SSSERVER = "zohosheet";// No I18N
	public static final String STARTROW_TOP = "STARTROWTOP";// No I18N
	public static final int ROWS_PER_PRINT = 50;// No I18N
	public static final String STARTROW = "STARTROW";// No I18N
	public static final String ENDROW = "ENDROW";// No I18N
	public static final String STARTCOL = "STARTCOL";// No I18N
	public static final String ENDCOL = "ENDCOL";// No I18N
	public static final String SHOWGRID = "SHOWGRID";// No I18N
	public static final String SHOWFORMULAS = "SHOWFORMULAS";		// No I18N
	public static final int ROWS_PER_TILE = 8;						// No I18N
	public static final String LOCKED = "LOCKED";// No I18N
	public static final String SHEETNAME = "SHEETNAME";		// No I18N
	public static final String IS_BROWSER_IE = "IS_BROWSER_IE";			// No I18N
	public static final String TILE_SEPERATOR = "<!---tileSep--->";		// No I18N
	public static final String SYSTEM_VERSION_NAME = "System";			// No I18N

	/**
	 * Added temporarily, to avoid compilation error, will be removed once
	 * we move to DFS
	 */
	public static final String AUTHREMOTELOCATION = "AuthRemoteLocation";	//No I18N
	// APIKeyId for unauthenticated views like excelviewer,scrach,etc.,
	public static final String ZS_REMOTE_APIKEYID = EnginePropertyUtil.getSheetPropertyValue("ZS_REMOTE_APIKEYID"); //No I18N

	public static final int MAX_RESPONSE_SIZE = 61440 * 10;  // 60KB*10

	public static final String DOC_CONTEXT_BASE_PATH = "h";	// No I18N
	public static final String PUBLIC_DOC_CONTEXT_BASE_PATH = "published";	// No I18N
	public static final String RANGE_PUBLISHED_CONTEXT_BASE_PATH = "publishedranges";	// No I18N
	public static final String EXEC_ACTION_ID_KEY = "EXEC_ACTION_ID_KEY";	//No I18N
	public static final String TRANSIENT = "transient";	//No I18N

	public static final String SHEET_CONTEXT_BASE_PATH = "sheet"; //NO I18N
	//DOCUMENT TYPE

	public static final int SHEET_DOCUMENT = 1;
	public static final int REMOTE_DOCUMENT = 200;
	public static final int NEW_DOCUMENT = 201;
	public static final int AUTHENTICATED_REMOTE_DOCUMENT = 102;
	public static final int GDRIVE_DOCUMENT = 103;

	// Custom Format Variables
	public static final String AUTOMATIC = "AUTOMATIC";		// No I18N
	public static final String NUMBER = "NUMBER";			// No I18N
	public static final String CURRENCY = "CURRENCY";			// No I18N
	public static final String DATETIME = "DATETIME";		// No I18N
	public static final String DATE = "DATE";			// No I18N
	public static final String TIME = "TIME";			// No I18N
	public static final String PERCENTAGE = "PERCENTAGE";		// No I18N
	public static final String FRACTION = "FRACTION";		// No I18N
	public static final String SCIENTIFIC = "SCIENTIFIC";		// No I18N
	public static final String TEXT = "TEXT";			// No I18N
	public static final String OTHERS = "OTHERS";		// No I18N
	public static final String SPECIAL = "SPECIAL";		// No I18N
	public static final String CUSTOM = "CUSTOM";		// No I18N
	public static final String REGIONAL = "REGIONAL";		// No I18N
	public static final String ACCOUNTING = "ACCOUNTING";	// No I18N

	//For Integration
	public static final String IS_INTEGRATION = "IS_INTEGRATION";	// No I18N
	public static final String IS_TOOLBAR_HIDE = "IS_TOOLBAR_HIDE";	// No I18N

	public static final String GDRIVE_VIEW = "GDRIVE_VIEW"; // No I18N	
	// For Password protection
	public static final String PROMPT_PASSWORD = "promptpassword"; // No I18N
	public static final String PASSWORD = "password"; // No I18N

	//for Remote API Imports
	public static final String REMOTE_USER   = "remoteapi"; // No I18N
	public static final String[] REMOTE_USERS = {"sheetremoteapi0","sheetremoteapi1","sheetremoteapi2","sheetremoteapi3","sheetremoteapi4","sheetremoteapi5","sheetremoteapi6","sheetremoteapi7","sheetremoteapi8","sheetremoteapi9","sheetremoteapi10","sheetremoteapi11","sheetremoteapi12","sheetremoteapi13","sheetremoteapi14","remoteapi"};	// No I18N
	public static final String[] REMOTE_USERS_FOR_DOC_ID = {"sheetremoteapi0","remoteapi"};	// No I18N
	public static final String PUBLIC_SPACE = "public";	// No I18N

	public static final String VIEWER = "VIEWER";// No I18N
	public static final String COLLABORATOR = "COLLABORATOR";// No I18N

	public static final String ORDERED_USERS_LIST = "ORDERED_USERS_LIST"; //No I18N
	public static final String[] USERPRESENCE_COLORS = {"ae3800", "f8a657", "4b10ac", "e93862", "005eff", "620f6d", "9b8c2b", "056493", "668ed0", "0d6856", "646464", "3dc34b", "a47d7c", "9258f1", "c52119", "d91997", "7b9191", "d87d34", "547724", "1dadba", "ce990a", "3fd3b6", "e43e29", "1692cf", "347c85", "866817", "d27960", "5a64cc", "504367", "852630", "93d21c", "9f5e4d", "730451", "dcb907", "0dcb7d", "f76605", "a05ea8", "166dbf", "576d77", "3c0479", "479b8c", "9d6d88", "65765a", "f61111", "417663", "c0504d", "f5cf0e", "9bbb59", "4a452a", "ff8500"};		//No I18N
        
    public static final String DOCUMENT_VIEW = "DOC_VIEW"; //No I18N

	public static final String HOUR_FORMAT = "HOUR_FORMAT";//No I18N 
	public static final String AM_FORMAT = "AM_FORMAT";//No I18N 
	public static final String PM_FORMAT = "PM_FORMAT";//No I18N 

	// for publish print view array of total rows, array of endrows, array of end cols
	public static final String TOTALENDROWARRAY = "TOTALENDROWARRAY";       //No I18N 
	public static final String ENDCOLARRAY = "ENDCOLARRAY"; //No I18N 
	public static final String ENDROWARRAY = "ENDROWARRAY"; //No I18N 
	public static final int TEXT_TO_COLUMNS_MAX_ROW_COUNT = 26; //No I18N
	public static final String NON_LOGEDIN_COLLAB_USER_NAME = "Guest"; //No I18N
	public static final String SCHEDULER_USER_NAME = "Scheduler"; //No I18N
	public static int js_split_count = EnginePropertyUtil.getJsSplitCount(); //No I18N
	public static final String BUILD_LABEL = EnginePropertyUtil.getVersionLabel();

	public static final String FORM_RID = "FORM_RID";//No I18N
	public static final int SYSTEM_GENERATED_FORMFIELDS = 2;//TIME_STAMP, ADDED_EMAILID

	public static JSONObjectWrapper combinations_file_info = null;
	public static final long THUMBNAIL_HOLD_ON_TIME = 6000000;        // Hold on time for thumbnail creation - 1 hour
	public static final long CLIENT_THUMBNAIL_HOLD_ON_TIME = 900000;        // Hold on time for client thumbnail creation - 15 minutes
	public static final int COLUMN_SELECTION_OFFSET = 1000;
	public static final int ROW_SELECTION_OFFSET = 100;

	public static final String TRANMAIL_ATTACH_ENCODE_METHODE = "ENCODE";//No I18N
	public static final String TRANMAIL_ATTACH_UPLOAD_METHODE = "UPLOAD";//No I18N

	public static final String CSP_HEADER = EnginePropertyUtil.readContentSecurityPolicyHeader();
        
	public static final String GOOGLE_DOC_FOLDER_ID = "GDOC_FOLDER_ID";//No I18N

        public static final int MAX_UNDOREDO_COUNT = 30;
        public static final Long DEFAULT_ZUID= new Long(-2L);
 		static final String SOURCE_FORMAT	=	"SOURCE_FORMAT";//No I18N
        public static final String DESTINATION_FORMAT	=	"DESTINATION_FORMAT";//No I18N
 public static final String JSON_COL_NAME="name";//No I18N
//        public static final String JSON_COL_TYPE="type";
        public static final String JSON_COL_PATTERN="pattern";//No I18N
        public static final String JSON_COL_LOCK="readonly";//No I18N
		public static final String JSON_COL_HIDE="ishide";//No I18N
        public static final String JSON_COL_HEADERS="headers";//No I18N
        public static final String JSON_CHECKBOX="ischeckbox";//No I18N
        public static final String JSON_COL_IS_DATE="isdate";//No I18N
        public static final String JSON_COL_IS_MANDATORY="ismandatory";//No I18	N
		public static final String JSON_PICKLIST_DEFAULT="predefinedDefault";//No I18	N
		public static final String JSON_COL_MAX_LENGTH="maxLength";//No I18	N
        public static final String JSON_COL_IS_UNIQUE="isunique";//No I18	N
        public static final String JSON_COL_IS_REGEX="regex";//No I18	N
	public static final String JSON_DV_HINT_SHOW="showHint";//No I18	N
        

        public static final String JSON_COL_MANDATORYNOTE="ismandatory_note";//No I18	N

        public static final String JSON_COL_PICKLIST="picklist";//No I18N
        public static final String JSON_NOTES_DENOTATION="notes";//No I18N
        public static final String JSON_VIEW_SETTINGS="view_settings";//No I18N
	public static final String JSON_IS_MULTIPICKLSIT="ismultipicklist";//No I18N
        
        
        public static final String JSON_ERROR_DENOTATION="errors"; //No I18N
        public static final String JSON_VALUES="values";//No I18N
        public static final String JSON_SUB_HEADERS="subheaders";//No I18N
        public static final String JSON_ROW_INDEX_LOCK = "rowindex_lock";
	public static final String JSON_ROW_INDEX_ERROR = "rowindex_error";
	public static final String JSON_UI_SETTINGS = "ui_settings";
        public static final String JSON_ROW_INDEX_HIDE = "rowindex_hide";
        public static final String JSON_COL_INDEX_HIDE = "colindex_hide";
//        public static final String JSON_COL_TYPE_GENERAL="general";
//        public static final String JSON_COL_TYPE_TEXT="text";
//        public static final String JSON_COL_TYPE_DMY="dmy";
//        public static final String JSON_COL_TYPE_MDY="mdy";
//        public static final String JSON_COL_TYPE_YMD="ymd";
        public static final String JSON_COL_IS_TEXT="istext";//No I18N
        public static final String JSON_COL_IS_PK= "isprimarykey";//No I18N
        public static final String JSON_HEADER_NOTE = "notes";//No I18N
        public static final String JSON_HEADER_DV = "predefinedvalues";//No I18N
		public static final String JSON_HEADER_INCLUDE = "includeinresponse";//No I18N
        public static final String JSON_DISPLAY_NAME = "displayname";//No I18N
	public static final String JSON_COL_WIDTH = "col_width";//No I18N
	//Various path and property which is used across the project centralised 
	//	public static final String SERVER_DIR_PATH 		= 	AppResources.getProperty("server.dir");//No I18N
	//	public static final String SERVER_HOME_PATH 	= 	AppResources.getProperty("server.home");//No I18N
	//	public static final String XML_XML_PARSER_FACTORY_PROPERTY_NAME = AppResources.getProperty(XmlPullParserFactory.PROPERTY_NAME);
	//	public static final String LINE_SEPARATOR 			= 	AppResources.getProperty( "line.separator" );
	//	public static final String WRITER_PROXY_PORT		= 	AppResources.getProperty("writer.proxy.port");
	//    public static final String WRITER_PROXY_HOST		=	AppResources.getProperty("writer.proxy.host");
	//    public static final String WRITER_PROXY_USERNAME 	=	AppResources.getProperty("writer.proxy.username");
	//    public static final String WRITER_PROXY_PASSWORD 	=	AppResources.getProperty("writer.proxy.password");
	//    public static final String APP_CONTEXT_PATH 		=   AppResources.getProperty("app.contextPath");
	//    public static final String I18N_LOCALIZATION        = 	AppResources.getProperty("i18n.localization");
	//    public static final String JAVA_IO_TMPDIR			=	AppResources.getProperty("java.io.tmpdir");
	//    public static final String OS_HOME					=	System.getProperty("os.name");
	//    public static final String USER_HOME				=	System.getProperty("user.home");
	//    public static final String EXPLORER_SERVICE_NAME	=	System.getProperty("explorer.service.name");
	//    public static final String EXPLORER_SERVER			=	System.getProperty("explorer.server");
        
      //Constants for NLP  
     public static final String NLP_OPERATIONS = "operations"; //NO I18N
	public static final String OPERATION_TYPE = "type";	//NO I18N
	public static final String OPERATION_ON = "on";		//NO I18N
        public static final String WORD_FORMS_BIN ="wordforms.bin"; //NO I18N
        public static final String LOAD_WORD_FORMS = "loadWordForms"; //NO I18N
	
	
	
	
	//Constants for formula
	public static final String F_SUM = "SUM";		//NO I18N
	public static final String F_MAX = "MAX";		//NO I18N
	public static final String F_MIN = "MIN";		//NO I18N
	public static final String F_DISTINCT = "UNIQUE";	//NO I18N
	public static final String F_AVG = "AVERAGE";		//NO I18N
	public static final String F_PERCENT = "PERCENTAGE";	//NO I18N
	public static final String F_CORREL = "CORREL";		//NO I18N
	public static final String F_COUNT = "COUNT";		//NO I18N
	
	public static final String IS_TEAM_RESOURCE = "IS_TEAM_RESOURCE";		//NO I18N
	public static final String CAPABILITIES = "CAPABILITIES";	//NO I18N
	public static final String DEFAULT_READ_USER = "defaultReadUser";	//NO I18N
	public static final String IS_ZILLUM_PLAN = "isZillumPlan";	//NO I18N
	public static final String IS_WD_PERSONAL_EDITION = "isWDPersonalEdition";	//NO I18N
        
        public static final int BIT_TYPE = 8;

	/** SPREADSHEET SETTINGS **/

	public static final String DOCUMENT_LOCALE = "DOC_LOCALE";//No I18N
	public static final String DICTIONARY_LOCALE = "DICT_LOCALE";//No I18N
	public static final String COMPLEXITY_LEVEL = "COMPLEXITY_LEVEL"; // No I18N

	public static final String FONT_NAME = String.valueOf(CommandConstants.FONT_NAME);
	public static final String FONT_SIZE = String.valueOf(CommandConstants.FONT_SIZE);

	public static final String DATE_FORMAT = String.valueOf(CommandConstants.DATE_FORMAT);
	public static final String DATE_DISPLAY_FORMAT = String.valueOf(CommandConstants.DATE_DISPLAY_FORMAT);
	public static final String NUMBER_GROUPING_TYPE = String.valueOf(CommandConstants.NUMBER_GROUPING_TYPE);

	public static final String DECIMAL_SEP = String.valueOf(CommandConstants.DECIMAL_SEPARATOR);
	public static final String THOUSAND_SEP = String.valueOf(CommandConstants.THOUSAND_SEPARATOR);
	public static final String CURRENCY_LOCALE = String.valueOf(CommandConstants.CURRENCY_LOCALE);
	public static final String SHEET_DIR = "SHEET_DIR"; //NO I18N
	public static final String DOCUMENT_TIMEZONE = "DOC_TIMEZONE";//No I18N


	/** ITERATIVE CALCULATION **/

	public static final String ITERATIVE_CALCULATION = "IS_ITR_CALC";   // No I18N
	public static final String MAX_NUMBER_ITERATIONS = "MAX_NO_ITR";    // No I18N
	public static final String THRESHOLD = "THRESHOLD"; // No I18N
	public static final String PATTERN_SETTING = "PATTERN_SETTING"; // No I18N

	/** ITERATIVE CALCULATION **/


	public static final String CUSTOM_COLORS = Integer.toString(CommandConstants.CUSTOM_COLORS);

	
    
        public static JsonObject getViewBasedStaticMap(String view){
//            JsonObject obj1 = clientStaticMap.getAsJsonObject("UNCLASSIFIED");//NO I18N
//            JsonObject obj2 = clientStaticMap.getAsJsonObject(view);
//            obj2.entrySet().forEach((entry) -> {
//                obj1.add(entry.getKey(),entry.getValue());
//            });
            return clientStaticMap.getAsJsonObject(view); 
        }
		public static JsonObject getViewBasedIntegrityHashMap(String view){
			return integrityHashViewBasedMap.getAsJsonObject(view);
		}
//	MEDIAN
//	MODE // 
//	PERCENTILE
//	STDEV
}
