//$Id$
package com.adventnet.zoho.websheet.model.util;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Table;
import com.adventnet.ds.query.*;
import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.ext.IntegralMap;
import com.adventnet.zoho.websheet.model.ext.IntegralSet;
import com.adventnet.zoho.websheet.model.ext.LinearIntegralRange;
import com.adventnet.zoho.websheet.model.parser.ODSWorkbookParser;
import com.adventnet.zoho.websheet.model.parser.ODSWorkbookTransformer;
import com.adventnet.zoho.websheet.model.response.v2.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.writer.MacroWriter;
import com.adventnet.zoho.websheet.model.writer.SettingsWriter;
import com.adventnet.zoho.websheet.model.writer.WorkbookToXML;
import com.adventnet.zoho.websheet.model.zs.ZSZipInputStream;
import com.adventnet.zoho.websheet.model.zsparser.OrderFileReader;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.StoreFactory;
import com.adventnet.zoho.websheet.store.StoreUtil;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import com.zoho.conf.Configuration;
import com.zoho.sas.container.AppResources;
import com.zoho.sheet.parse.html.HtmlBorder;
import com.zoho.sheet.parse.html.HtmlStyle;
import com.zoho.sheet.util.DataValidationUtils;
import com.zoho.sheet.util.SheetJspUtil;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.zfsng.client.ZohoFS;
import org.apache.commons.collections4.map.AbstractReferenceMap.ReferenceStrength;
import org.apache.commons.collections4.map.ReferenceMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.net.URLEncoder;
import java.text.CharacterIterator;
import java.text.SimpleDateFormat;
import java.text.StringCharacterIterator;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeoutException;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */


public class EngineUtils
{
	public static final Logger LOGGER = Logger.getLogger(EngineUtils.class.getName());
	//int cellsToSkip = 0;


	private EngineUtils()
	{
	}

	private static EngineUtils engineUtil = null;

	public static EngineUtils getInstance()
	{
		if(engineUtil == null)
		{
			engineUtil = new EngineUtils();
		}
		return engineUtil;
	}

	public void storeWorkBook(String currenttime, Workbook workBook, HttpServletRequest request)
	{
		//Hashtable ht = (Hashtable) request.getSession().getAttribute(EngineConstants.ENGINEHASHTABLE);
		HttpSession session = request.getSession();
		synchronized (session)
		{
			ReferenceMap ht = (ReferenceMap) session.getAttribute(EngineConstants.ENGINEHASHTABLE);
			if(ht == null)
			{
				ht = new ReferenceMap(ReferenceStrength.HARD, ReferenceStrength.HARD);
			}

			// check if the current time is available, if true generate the new current time.
			boolean containsKey = ht.containsKey(currenttime);
			while(containsKey)
			{
				currenttime = String.valueOf(System.currentTimeMillis());
				containsKey = ht.containsKey(currenttime);
				if(!containsKey)
				{
					break;
				}
			}

			ht.put(currenttime, workBook);
			request.setAttribute(EngineConstants.CURRENTTIME, currenttime);
			session.setAttribute(EngineConstants.ENGINEHASHTABLE, ht);
		}
	}

	public Workbook getWorkBook(String currenttime, HttpServletRequest request)
	{
		return getWorkBook(currenttime, request, true); //
	}


	public Workbook getWorkBook(String currenttime, HttpServletRequest request, boolean checkReadyToRender)
	{
		Workbook workBook = null;
		//Hashtable ht = (Hashtable) request.getSession().getAttribute(EngineConstants.ENGINEHASHTABLE);
		ReferenceMap ht = (ReferenceMap) request.getSession().getAttribute(EngineConstants.ENGINEHASHTABLE);
		if(currenttime != null && ht != null)
		{
			if(ht.containsKey(currenttime))
			{
				workBook = (Workbook) ht.get(currenttime);
			}
		}

		/////////
		if(workBook != null && checkReadyToRender)
		{
			synchronized (workBook)
			{
				if(!workBook.isReadyToRender())
				{
					try
					{
						workBook.wait(7000); // 7 sec
					}
					catch(InterruptedException ie)
					{
						LOGGER.log(Level.WARNING, "Error: ", ie);
					}
				}
			}
		}
		//////////////
		return workBook;
	}

	//Used only for Print Preview in Public View
	public Workbook getWorkBook(HttpServletRequest request, String filePath) throws Exception
	{
		Workbook workBook = new Workbook();
		String currenttime = String.valueOf(System.currentTimeMillis());
		try
		{
			ODSWorkbookTransformer transformer = new ODSWorkbookTransformer();
			transformer.constructWorkbook(workBook, null);
			ODSWorkbookParser parser = new ODSWorkbookParser(transformer, false);
			parser.parse(filePath);
			transformer.endWorkbook();

			//request.setAttribute(Constants.CURRENTTIME, currenttime);
			storeWorkBook(currenttime, workBook, request);
		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "Engine: Exception while parsing ODS File : " + filePath, e);
			// Send an email
			//MailUtil.reportError(filePath, "Exception on parsing the document (print preview)", e); //No I18N
			throw (new Exception("Document.Parse.Error", e));
		}
		return workBook;
	}


	// For WEBAPI Usage - Not in use as of now
	//////////////////////////////////////////////////////////////////////////////////
	public Workbook getWorkBook(String documentId, String docOwnerId,
	                            String documentName, String version) throws Exception
	{
		Workbook workBook = null;


		InputStream stream2 = null;
		InputStream stream1 = null;
		InputStream stream3 = null;
		String filePath = EngineConstants.LOCATION + documentId + EngineConstants.ENGINE_ODS_FILE_FORMAT;
		if(version != null)
		{
			filePath = EngineConstants.LOCATION + documentId + "/" + "version" + "/" + documentId + "_" + version + EngineConstants.ENGINE_ODS_FILE_FORMAT;
		}

		long time = new Date().getTime();
		try
		{

			//FileStore store = FileStore.getStore(String.valueOf(docOwnerId));
			//stream1 = store.read(filePath);
			filePath = AppResources.getProperty("server.home") + EngineConstants.LOCATION + "bin/filestore/" + docOwnerId + "/" + documentId + EngineConstants.ENGINE_ODS_FILE_FORMAT;//NO I18N
			stream1 = new FileInputStream(filePath);
			LOGGER.log(Level.INFO, "Engine: Parsing workbook started for : {0}", documentId);
			ODSWorkbookTransformer transformer = new ODSWorkbookTransformer();
			ODSWorkbookParser parser = new ODSWorkbookParser(transformer);
			parser.setWorkbookName(documentName);
			String fileToParse = "content.xml";
			parser.parse(stream1, fileToParse);
			fileToParse = "styles.xml";
			//stream2 = store.read(filePath);
			stream2 = new FileInputStream(filePath);
			parser.parse(stream2, fileToParse);
			workBook = transformer.transform();

			// check if it contains any macros
			//stream3 = store.read(filePath);
			stream3 = new FileInputStream(filePath);
            try(ZSZipInputStream zin = new ZSZipInputStream(stream3)) {
                ZipEntry zEntry;
                while((zEntry = zin.getNextEntry()) != null) {
                    // To check if the ODS file has macros
                    if(zEntry.getName().contains("script-lb.xml")) {
                        //workBook.setContainsMacro(true);
                        break;
                    }
                }
            }
            ////////////


		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "Engine: Exception while parsing ODS File : " + documentId, e);
			throw e;
		}
		finally
		{
			try
			{
				if(stream1 != null)
				{
					stream1.close();
				}
				if(stream2 != null)
				{
					stream2.close();
				}
				if(stream3 != null)
				{
					stream3.close();
				}
			}
			catch(Exception e1)
			{
				LOGGER.log(Level.WARNING, null, e1);
			}
		}


		LOGGER.log(Level.INFO, "Engine: Parsing workbook end =====> {0}ms : doc Id : {1}", new Object[]{new Date().getTime() - time, documentId});
		for(Sheet sheet : workBook.getSheetList())
		{
			LOGGER.log(Level.INFO, "Engine: SheetDetails : sheetName: {0} : usedRowIndex: {1} : usedColumnIndex: {2} : rowNum: {3} : colNum: {4}", new Object[]{sheet.getName(), sheet.getUsedRowIndex(), sheet.getUsedColumnIndex(), sheet.getRowNum(), sheet.getColNum()});
		}
		return workBook;
	}
	///////////////////////////////////////////////////////////////////////////////////////////

	public String[] getSheetNamesAsArray(Workbook workBook)
	{
		List<String> sheetNamesArr = new ArrayList<>();
		if(workBook != null)
		{
			Sheet sheets[] = workBook.getSheets();
			if(sheets != null)
			{
				for(Sheet sheet : sheets)
				{
					if(sheet.isHidden())
					{
						continue;
					}
					sheetNamesArr.add(sheet.getName());
				}
			}
		}
		return sheetNamesArr.toArray(new String[]{});
	}

	/////////////Added For DIV BASED GRID By Kirubha
	// For WEBAPI Usage
	public HashMap getSheetDetails(Workbook workBook, String sheetname, boolean isNewSheet) throws Exception
	{
		//JSONObjectWrapper rowObj = new JSONObjectWrapper();
		//JSONObjectWrapper cellObj = null;

		int docrows = EngineConstants.DEFAULTROWS;
		int doccols = EngineConstants.DEFAULTCOLS;
		HashMap hashMap = new HashMap();
		//if("new".equals(neworopen))
		if(isNewSheet)
		{
			JSONArrayWrapper rowArr = new JSONArrayWrapper();
			//JSONObjectWrapper rowJSON = null;

			//JSONArrayWrapper colArr = new JSONArrayWrapper();
			//JSONObjectWrapper colJSON = null;
			//JSONObjectWrapper rowJSON = null;
			JSONArrayWrapper rowHeightAry = new JSONArrayWrapper();
			JSONArrayWrapper rowTopAry = new JSONArrayWrapper();


			JSONArrayWrapper colArr = new JSONArrayWrapper();

			JSONArrayWrapper colWidAry = new JSONArrayWrapper();
			JSONArrayWrapper colLeftAry = new JSONArrayWrapper();


			SheetVariables sheetVar = new SheetVariables();
			sheetVar.rowheight = workBook.getDefaultRowHeight();
			sheetVar.colwidth = workBook.getDefaultColumnWidth();
			sheetVar.defaultCellStyle = EngineUtils.toHtmlStyle(workBook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME), workBook);

			String[] rowHeaderLabels = new String[docrows + 1];
			String cellstyle = "cD"; //No I18N
			if(EngineConstants.ISMKI)
			{
				cellstyle = "JcD"; //No I18N
				sheetVar.rowheight = 20;
			}
			else if(EngineConstants.ISBAIHUI)
			{
				cellstyle = "CcD"; //No I18N
				sheetVar.rowheight = 20;
			}
			sheetVar.cellstyle = cellstyle;
			sheetVar.sheetname = sheetname;

			sheetVar.rows = docrows;
			sheetVar.cols = doccols;

			//Commeneted For DIV BASED Grid By Kirubha
			//Range Should Be Only Upto 40 And Not 41
			long colLeft = 0;
			long colWidth = workBook.getDefaultColumnWidth();

			for(int cnt = 0; cnt < doccols; cnt++)
			{
				sheetVar.colWidth[cnt] = String.valueOf(colWidth);
				colWidAry.put(colWidth);
				colLeftAry.put(colLeft);

				colLeft += colWidth;
				sheetVar.tablewidth = colLeft;
			}

			for(int cnt = doccols; cnt < Utility.MAXNUMOFCOLS; cnt++)
			{
				colWidAry.put(colWidth);
				colLeftAry.put(colLeft);
				colLeft += colWidth;
			}

			long rowTop = 0;
			long rowHeight = workBook.getDefaultRowHeight();

			for(int cnt = 0; cnt < docrows; cnt++)
			{
				sheetVar.rowHeight[cnt] = String.valueOf(rowHeight);

				//for row details
				//rowHeightAry.put(rowHeight);
				//rowTopAry.put(rowTop);

				rowTop += rowHeight;
				sheetVar.tableheight = rowTop;
			}

	    /*for(int cnt = docrows; cnt < Utility.MAXNUMOFROWS; cnt++) {
	    	//rowHeightAry.put(rowHeight);
	    	//rowTopAry.put(rowTop);
	    	rowTop += rowHeight;
	    }*/

			rowArr.put(getRowHeightJSON(0, Utility.MAXNUMOFROWS - 1, workBook.getDefaultRowHeight()));
			//rowArr.put(rowTopAry);

			colArr.put(colWidAry);
			colArr.put(colLeftAry);


			sheetVar.rowInfo = rowArr.toString();
			sheetVar.columnInfo = colArr.toString();
			hashMap.put("SHEETVARIABLES", sheetVar);
			hashMap.put("rowHeaderLabels", rowHeaderLabels);
		}
		else
		{
			Sheet sheet = workBook.getSheet(sheetname);

			int usedrows = sheet.getUsedRowIndex();
			int usedcols = sheet.getUsedColumnIndex();

			docrows = usedrows;
			doccols = usedcols;

			if(docrows + 15 >= EngineConstants.DEFAULTROWS)
			{
				docrows = docrows + 15;
				if(docrows > Utility.MAXNUMOFROWS)
				{
					docrows = Utility.MAXNUMOFROWS;
				}
			}
			else
			{
				docrows = EngineConstants.DEFAULTROWS;
			}

			if(doccols + 5 >= EngineConstants.DEFAULTCOLS)
			{
				doccols = doccols + 5;
				if(doccols > Utility.MAXNUMOFCOLS)
				{
					doccols = Utility.MAXNUMOFCOLS;
				}
			}
			else
			{
				doccols = EngineConstants.DEFAULTCOLS;
			}


			// hashMap = readSheetDetails(workBook, sheet, sheetname, 0, 0, docrows, doccols, false);
			hashMap = readSheetDetails1(workBook, sheet, sheetname, 0, 0, docrows, doccols, false, usedrows, usedcols);
		}

		return hashMap;
	}

	public static JSONObjectWrapper getRowHeightJSON(int startRow, int endRow, Object value)
	{
		JSONObjectWrapper rowObject = new JSONObjectWrapper();

		rowObject.put(JSONConstants.START_ROW, startRow);
		rowObject.put(JSONConstants.END_ROW, endRow);
		rowObject.put(JSONConstants.VALUE, value);

		return rowObject;
	}

	public static JSONArrayWrapper readRowHeights(Sheet sheet, int startRow, int endRow)
	{

		JSONArrayWrapper rowArr = new JSONArrayWrapper();
		int row = startRow;

		List<Integer> rowHeightRepeatedList = new ArrayList<Integer>();
		List<Integer> rowHeightsList = new ArrayList<Integer>();
		List<Boolean> rowVisibilityList = new ArrayList<Boolean>();

		while(row <= endRow)
		{

			Row oRow = sheet.getRowReadOnly(row);
			boolean isVisible = RowUtil.isVisibleRow(oRow);
			int height = RowUtil.getRowHeight(sheet.getWorkbook(), oRow);
			int rowsRepeated = oRow != null ? oRow.getRowsRepeated() : 1;
			rowHeightRepeatedList.add(rowsRepeated);
			rowHeightsList.add(height);
			rowVisibilityList.add(isVisible);

			row = row + rowsRepeated;
		}

		//This block of code To send Row Height Repeated
		////////////////////////////////////////////////////////////////////////////////////////////////

		JSONObjectWrapper rowObj = null;

		int defRowHeight = sheet.getWorkbook().getDefaultRowHeight();
		int prevRowHeight = defRowHeight;
		int rowHeightRepeated = 0;

		if(!rowHeightRepeatedList.isEmpty())
		{

			prevRowHeight = (Integer) rowHeightsList.get(0);
			rowHeightRepeated = (Integer) rowHeightRepeatedList.get(0);

		}

		for(int listIter = 1, len = rowHeightRepeatedList.size(); listIter < len; listIter++)
		{

			int rowHeight = ((Boolean) rowVisibilityList.get(listIter)) ? (Integer) rowHeightsList.get(listIter) : 0;
			int rep1 = (Integer) rowHeightRepeatedList.get(listIter);

			if(prevRowHeight != rowHeight)
			{
				rowArr.put(getRowHeightJSON(startRow, startRow + rowHeightRepeated - 1, Long.valueOf(prevRowHeight)));
				startRow += rowHeightRepeated;
				rowHeightRepeated = rep1;
				prevRowHeight = rowHeight;

			}
			else
			{

				rowHeightRepeated += rep1;
			}

		}
		rowArr.put(getRowHeightJSON(startRow, endRow, Long.valueOf(prevRowHeight)));
		return rowArr;
	}

	/////////////Added For DIV BASED GRID By Kirubha
	// ------------------------------------------------------------------------------------------------------------
	public HashMap readSheetDetails1(Workbook workBook, Sheet sheet, String sheetname, int startRow, int startCol,
	                                 int endRow, int endCol, boolean formHtmlRange, int usedRow, int usedCol) throws Exception
	{

		HashMap hashMap = new HashMap();

		SheetVariables sheetVar = new SheetVariables();

		sheetVar.rowheight = workBook.getDefaultRowHeight();
		sheetVar.colwidth = workBook.getDefaultColumnWidth();
		sheetVar.defaultCellStyle = EngineUtils.toHtmlStyle(workBook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME), workBook);

		//In New DIV BASED GRID, Used_Row Is Not Identified By Iterating, Instead It's Finded Out By Our Values.

		sheetVar.usedRow = usedRow;
		sheetVar.usedCol = usedCol;

		sheetVar.sheetname = sheetname;

		int maxRows = EngineUtils.getFloorRow(com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFROWS);
		//Validating maximum rows
		if(sheetVar.usedRow > maxRows)
		{
			sheetVar.usedRow = maxRows - 1;
		}

		//Have To Change These Values Based On Resolution On Client Side.
		//By Default 18 for Col && By Default 40 For Row
		sheetVar.rows = (endRow - startRow) + 1;
		sheetVar.cols = (endCol - startCol) + 1;

		if(!formHtmlRange)
		{
			validateMaxDIM(sheetVar, endRow, endCol);
		}

		//rowHeaderLabels = new String[(int)sheetVar.rows+1];
		sheetVar.rowHeight = new String[(int) sheetVar.rows];
		sheetVar.colWidth = new String[(int) sheetVar.cols];


		JSONArrayWrapper rowArr = new JSONArrayWrapper();
		//JSONObjectWrapper rowJSON = null;
		//JSONArrayWrapper rowHeightAry = new JSONArrayWrapper();
		//JSONArrayWrapper rowTopAry = new JSONArrayWrapper();

		int row = startRow;
		//int maxRows = EngineUtils.getFloorRow(com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFROWS );
		int rowTop = 0;

		List<Integer> rowHeightRepeatedList = new ArrayList<Integer>();
		List<Integer> rowHeightsList = new ArrayList<Integer>();

//                Iterator<ReadOnlyRow> rows = sheet.getAllVisibleRows();
//                while(rows.hasNext())
//				{
//					ReadOnlyRow oRow = rows.next();
//					int rowIndex = oRow.getRowIndex();
//					int height = RowUtil.getRowHeight(sheet.getWorkbook(), oRow.getRow());
//					int rowsRepeated = oRow.getRowsRepeated();
//					for (int rep = 0; rep < rowsRepeated; rep++)
//					{
//						rowTop += height;
//						if (rowIndex + rep < sheetVar.rows)
//						{
//							sheetVar.rowHeight[rowIndex + rep] = String.valueOf(height);
//							sheetVar.tableheight = rowTop;
//						}
//					}
//					rowArr.put(getRowHeightJSON(rowIndex, rowIndex + rowsRepeated - 1, height));
//				}
//
//			rows = sheet.getAllHiddenRows();
//			while(rows.hasNext())
//			{
//				ReadOnlyRow oRow = rows.next();
//				int rowIndex = oRow.getRowIndex();
//				int height = 0;
//				int rowsRepeated = oRow.getRowsRepeated();
//				for (int rep = 0; rep < rowsRepeated; rep++)
//				{
//					rowTop += height;
//					if (rowIndex + rep < sheetVar.rows)
//					{
//						sheetVar.rowHeight[rowIndex + rep] = String.valueOf(height);
//						sheetVar.tableheight = rowTop;
//					}
//				}
//				rowArr.put(getRowHeightJSON(rowIndex, rowIndex + rowsRepeated - 1, height));
//			}
//
//			rows = sheet.getAllFilteredRows();
//			while(rows.hasNext())
//			{
//				ReadOnlyRow oRow = rows.next();
//				int rowIndex = oRow.getRowIndex();
//				int height = 0;
//				int rowsRepeated = oRow.getRowsRepeated();
//				for (int rep = 0; rep < rowsRepeated; rep++)
//				{
//					rowTop += height;
//					if (rowIndex + rep < sheetVar.rows)
//					{
//						sheetVar.rowHeight[rowIndex + rep] = String.valueOf(height);
//						sheetVar.tableheight = rowTop;
//					}
//				}
//				rowArr.put(getRowHeightJSON(rowIndex, rowIndex + rowsRepeated - 1, height));
//			}

		while(row < maxRows)
		{
			Row oRow = sheet.getRowReadOnly(row);
			int height = RowUtil.getRowHeight(sheet.getWorkbook(), oRow);
			int rowsRepeated = oRow != null ? oRow.getRowsRepeated() : 1;
			rowHeightRepeatedList.add(rowsRepeated);
			rowHeightsList.add(height);
			for(int rep = 0; rep < rowsRepeated; rep++)
			{
				//rowHeightAry.put(height);
				//rowTopAry.put(rowTop);
				rowTop += height;
				if(row < sheetVar.rows)
				{
					sheetVar.rowHeight[row] = String.valueOf(height);
					sheetVar.tableheight = rowTop;
				}
				row++;
			}
		}
		//This block of code To send Row Height Repeated
		////////////////////////////////////////////////////////////////////////////////////////////////
		JSONObjectWrapper rowObj = null;
		int prevRowHeight = workBook.getDefaultRowHeight();
		int rowHeightRepeated = 0;


		if(!rowHeightRepeatedList.isEmpty())
		{
			prevRowHeight = (Integer) rowHeightsList.get(0);
			rowHeightRepeated = (Integer) rowHeightRepeatedList.get(0);
		}


		// Always start from 0 as this method called first time load of docs
		startRow = 0;
		for(int listIter = 1; listIter < rowHeightRepeatedList.size(); listIter++)
		{
			int rowHeight = (Integer) rowHeightsList.get(listIter);
			int rep1 = (Integer) rowHeightRepeatedList.get(listIter);
			//Should Remove This Part And Construct From ActionUtils.
			if(prevRowHeight != rowHeight)
			{
				rowArr.put(getRowHeightJSON(startRow, startRow + rowHeightRepeated - 1, Long.valueOf(prevRowHeight)));
				startRow += rowHeightRepeated;
				rowHeightRepeated = rep1;
				prevRowHeight = rowHeight;

			}
			else
			{
				rowHeightRepeated += rep1;
			}
		}

		endRow = startRow + rowHeightRepeated - 1;
		rowArr.put(getRowHeightJSON(startRow, endRow, Long.valueOf(prevRowHeight)));
		if(endRow < com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFROWS - 1)
		{
			rowArr.put(getRowHeightJSON(endRow + 1, Utility.MAXNUMOFROWS - 1, workBook.getDefaultRowHeight()));
			rowArr.put(rowObj);
		}

		//logger.info("rowArr print in Engine Utils --------------->"+rowArr);

		///////////////////////////////////////////////////////////////////////////////////////////////

		JSONArrayWrapper colArr = new JSONArrayWrapper();

		JSONArrayWrapper colWidAry = new JSONArrayWrapper();
		JSONArrayWrapper colLeftAry = new JSONArrayWrapper();


		//Code to retrieve Column Width
		int maxCol = com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFCOLS;
		int col = startCol;
		int colLeft = 0;
		while(col < maxCol)
		{
			long width = workBook.getDefaultColumnWidth();
			int colsRepeated = 1;
			ColumnHeader columnHeader = sheet.getColumnHeaderReadOnly(col);
			if(columnHeader != null)
			{
				colsRepeated = columnHeader.getColsRepeated();
				width = columnHeader.getColumnWidth();
			}

			for(int rep = 0; rep < colsRepeated; rep++)
			{
				colWidAry.put(width);
				colLeftAry.put(colLeft);
				colLeft += width;
				if(col < sheetVar.cols)
				{
					sheetVar.colWidth[col] = String.valueOf(width);
					sheetVar.tablewidth = sheetVar.tablewidth + width;
				}
				col++;
			}
		}
		//rowArr.put(rowHeightAry);
		//rowArr.put(rowTopAry);

		colArr.put(colWidAry);
		colArr.put(colLeftAry);


		sheetVar.rowInfo = rowArr.toString();
		sheetVar.columnInfo = colArr.toString();

		sheetVar.freezepane = DocumentUtils.getFreezePaneInfo(workBook, sheetname);

		//sheetVar.colMergeDetails = colMerge.toString();
		hashMap.put("SHEETVARIABLES", sheetVar);
		//hashMap.put("rowHeaderLabels", rowHeaderLabels);
		return hashMap;

	}

	// ---------------------------------------------------------------------------------------------------------
	/*
	 * Used To Convert Sheet Names Arr to String
	 */
	public static String convertArrayToJSONString(String[] sheets)
	{
		if(sheets != null)
		{
			JSONArrayWrapper jsonArr = new JSONArrayWrapper();
			for(String sheet : sheets)
			{
				jsonArr.put(sheet);
			}
			return jsonArr.toString();
		}
		return null;
	}

	public HashMap readSheetDetails(WorkbookContainer container, String version, Sheet sheet, int[] rowsAry, int startCol, int endCol, boolean formHtmlRange, boolean showFormulas) throws Exception
	{
		Instant currentInstant = Instant.now();

		if(rowsAry.length > 200)
		{
			LOGGER.log(Level.WARNING, "rowsToLoad size exceeded 200 : {0} in sheet : {1}", new Object[]{rowsAry.length, container.getResourceKey()});
		}

		Workbook workbook = container.getWorkbook(version);

		String sheetName = sheet.getAssociatedName();
		HashMap hashMap = new HashMap();

		//Added to solve the border issue while scroll(reading a range), if the topmost cell in range has border.
		int startRow = rowsAry[0];

		//JSONObjectWrapper rowObj = new JSONObjectWrapper();
		//JSONObjectWrapper cellObj = null;


		SheetVariables sheetVar = new SheetVariables();

		sheetVar.rowheight = workbook.getDefaultRowHeight();
		sheetVar.colwidth = workbook.getDefaultColumnWidth();
		sheetVar.defaultCellStyle = EngineUtils.toHtmlStyle(workbook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME), workbook);

		sheetVar.rowsAry = rowsAry;
		//Filter Implementation Added By Sriarun
		String[] rowHeaderLabels;
		String cellstyle = "cD"; //No I18N
		if(EngineConstants.ISMKI)
		{
			cellstyle = "JcD"; //No I18N
			sheetVar.rowheight = 20;
		}
		else if(EngineConstants.ISBAIHUI)
		{
			cellstyle = "CcD"; //No I18N
			sheetVar.rowheight = 20;
		}

		sheetVar.cellstyle = cellstyle;
		sheetVar.sheetname = sheetName;
		sheetVar.readTopMostBorder = startRow != 0;
		sheetVar.readLeftMostBorder = startCol != 0;

		sheetVar.rows = rowsAry.length; // (endRow - startRow ) + 1;
		sheetVar.cols = Math.min((endCol - startCol) + 1, Utility.MAXNUMOFCOLS);

		rowHeaderLabels = new String[(int) sheetVar.rows];
		sheetVar.sheetvalues = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.sheetformulas = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.sheetstyles = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.sheetannots = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.cellLeftBorders = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.cellRightBorders = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.cellTopBorders = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.cellBottomBorders = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.colspan = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.rowspan = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.hyper = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.literalColspan = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.literalRowspan = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		sheetVar.colWidth = new String[(int) sheetVar.cols];
		sheetVar.rowHeight = new String[(int) sheetVar.rows];
		sheetVar.dValidation = new String[(int) sheetVar.rows][(int) sheetVar.cols];//DataValidation
		sheetVar.dataObject = new JSONObjectWrapper();
		String format = null;

		//Cell cell = null;

		//Assumed That variable "row" is to get from Workbook

		long rowTop = 0;

		//Assumed That variable "rowIndex" is to Update Two Dimensional Arrays


		//boolean setalldefault = false;
		//List<Link> links = new ArrayList<Link>();
		//Map <Integer, List<Integer>> coveredCellMap = new HashMap <Integer, List<Integer>>();

		// int cellsToSkip = 0;
		JSONArrayWrapper styleAry = new JSONArrayWrapper();
		JSONArrayWrapper borderAry = new JSONArrayWrapper();
		JSONArrayWrapper cellAry = new JSONArrayWrapper();
		int rowIterate = 0;
		boolean isMergeInfo = false;

		while(rowIterate < sheetVar.rows)
		{

			int rowIndex = rowsAry[rowIterate];


			//For maintaining colMergDetails
			sheetVar.rowWiseJSON = new JSONObjectWrapper();

			/////////
			long height = sheetVar.rowheight;
			int tempRow = rowIndex;


			ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(rowIndex);
			int rowsRepeated = rRow.getRowsRepeated();
			Row row = rRow.getRow();
			//if (oRow != null) {
			if(!((rowIterate + rowsRepeated) < sheetVar.rows))
			{
				rowsRepeated = (sheetVar.rows - rowIterate);
			}

			sheetVar.cellsToSkip = 0; // used for merge cells.
			//List<Integer> coveredCellList = new ArrayList<Integer>();


			int colIndex = startCol;
			int colIterate = 0;
			//         int colSkipped =0;
			// boolean isCoveredCell=false;
			//     List<DataRange> coveredList = new ArrayList<DataRange>();
			while(colIterate < sheetVar.cols)
			{

				Cell cell = null;
				int colsRepeated = 1;

				if(row != null)
				{
					ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(row.getRowIndex(), colIndex);
					cell = rCell.getCell();
					colsRepeated = rCell.getColsRepeated();
				}

				if(!((colIterate + colsRepeated) < sheetVar.cols))
				{
					colsRepeated = (sheetVar.cols - colIterate);
				}
				if(sheetVar.cellsToSkip > 0)
				{

					sheetVar.cellsToSkip -= colsRepeated;
					if(sheetVar.cellsToSkip < 0)
					{
						sheetVar.cellsToSkip = 0;
					}

				}

				// upto this.
				// Create a ReadOnlyCell with correct rowIndex and colIndex and corrected rowsRepeated and colsrepeated and send that to readCellDetails.

				// readCellDetails(sheet, new ReadOnlyCell(cell, rowIndex, colIndex, rowsRepeated, colsRepeated), sheetVar, rowIterate, colIterate, formHtmlRange, showFormulas);


				JSONObjectWrapper cellDetailsNew = new JSONObjectWrapper();
				for(int r = 0; r < rowsRepeated; r++)
				{
//                                    	   JSONObjectWrapper rowObject = new JSONObjectWrapper();
					sheetVar.dataObject.put(String.valueOf(rowIndex + r), sheetVar.dataObject.has(String.valueOf(rowIndex + r)) ? sheetVar.dataObject.getJSONObject(String.valueOf(rowIndex + r)) : new JSONObjectWrapper());
					JSONObjectWrapper rowObject = sheetVar.dataObject.getJSONObject(String.valueOf(rowIndex + r));
					for(int c = 0; c < colsRepeated; c++)
					{
						if(Instant.now().isAfter(currentInstant.plusSeconds(EngineConstants.HTML_GENERATION_TIMEOUT_SECONDS)))
						{
							LOGGER.log(Level.SEVERE, "[HTML_GENERATION] HTML Generation has taken more than {0} seconds", new Object[]{EngineConstants.HTML_GENERATION_TIMEOUT_SECONDS});
							throw new TimeoutException("[HTML_GENERATION] HTML Generation has taken more than "+EngineConstants.HTML_GENERATION_TIMEOUT_SECONDS+" seconds");  // No I18N
						}
						cellDetailsNew = sheet.getMergeCellSpanRange(cell) == null ? readCellDetails(container, new ReadOnlyCell(sheet, cell, rowIndex + r, colIndex + c, rowsRepeated, colsRepeated), sheetVar, rowIterate + r, colIterate + c, formHtmlRange, showFormulas, isMergeInfo) : cellDetailsNew;
						if(cellDetailsNew.length() > 0)
						{
							rowObject.put(String.valueOf(colIndex + c), cellDetailsNew);
						}

					}

				}


				//   JSONObjectWrapper cellDetails = readCellDetailsNew(sheet, new ReadOnlyCell(cell, rowIndex, colIndex, rowsRepeated, colsRepeated), sheetVar, rowIterate, colIterate, formHtmlRange, showFormulas);

				colIndex += colsRepeated;
				colIterate += colsRepeated;
//                                        if(colSkipped > 0){
//                                        	colIterate += colSkipped;
//                                        }

				/************* Code to retrieve Column Width *************/

			} // close of cols while


			rowIterate += rowsRepeated;
			rowIndex += rowsRepeated;

			//} // close of for rowsRepeated

			/************* Code to retrieve Row Height *************/
			RowStyle rowStyle = null;
			if(row != null && tempRow <= rowsAry[rowsAry.length - 1])
			{
				rowStyle = row.getRowStyleReadOnly();
			}
			if(rowStyle != null)
			{
				String rH = rowStyle.getPropertyAsString_deep(RowStyle.Property.ROWHEIGHT, workbook);

				if(rH != null)
				{
					height = (long) EngineUtils1.convertToPixels(rH, EngineConstants.ROWDPI);
				}
				else
				{
					height = workbook.getDefaultRowHeight();
				}
			}

			for(int k = 1; k <= rowsRepeated; k++)
			{
				int tempRowIndex = rowIterate - k;
				/************* Code to set Row Height *************/
				sheetVar.rowHeight[tempRowIndex] = String.valueOf(height);
				rowHeaderLabels[tempRowIndex] = "" + (tempRowIndex + 1);
				//To Add Row Top (If it is tiled row)
		    /*if(isTiledRow(tempRowIndex))
                            {
                            cellObj = new JSONObjectWrapper();
                            cellObj.put("top",rowTop);
                            rowObj.putOpt(tempRowIndex+"",cellObj);
                            }*/
				sheetVar.tableheight = sheetVar.tableheight + height;
				rowTop = sheetVar.tableheight;
				/************* Code to retrieve set Height *************/
			}


		} // close of rows while

		//Manipulating Width(Because Column Repeadted Differs From Cells Repeated).
		int colHeaderIndex = startCol;
		int colHeaderIter = 0;

		while(colHeaderIter < sheetVar.cols)
		{
			int colsRepeated = 1;
			long width = sheetVar.colwidth;

			ColumnHeader columnHeader = sheet.getColumnHeaderReadOnly(colHeaderIndex);
			if(columnHeader == null)
			{
				ColumnHeader prevColumnHeader = null;
				// check here if its part of cols repeated
				for(int i = (colHeaderIndex - 1); i >= 0; i--)
				{
					prevColumnHeader = sheet.getColumnHeaderReadOnly(i);
					if(prevColumnHeader != null)
					{
						break;
					}
				}

				if(prevColumnHeader != null && prevColumnHeader.getColsRepeated() > (colHeaderIndex - prevColumnHeader.getColumn().getColumnIndex()))
				{
					columnHeader = prevColumnHeader;
					colsRepeated = prevColumnHeader.getColsRepeated() - (colHeaderIndex - prevColumnHeader.getColumn().getColumnIndex());
				}
			}
			else
			{
				colsRepeated = columnHeader.getColsRepeated();
			}

			if(!((colHeaderIter + colsRepeated) < sheetVar.cols))
			{
				colsRepeated = (sheetVar.cols - colHeaderIter);
			}

			if(columnHeader != null)
			{
				ColumnStyle columnStyle = columnHeader.getColumnStyleReadOnly();
				if(columnStyle != null)
				{
					String cW = columnStyle.getPropertyAsString_deep(ColumnStyle.Property.COLUMNWIDTH, workbook);
					if(cW != null)
					{
						width = (long) EngineUtils1.convertToPixels(cW, EngineConstants.COLDPI);
					}
				}
			}

			for(int rep = 0; rep < colsRepeated; rep++)
			{

				if(Instant.now().isAfter(currentInstant.plusSeconds(EngineConstants.HTML_GENERATION_TIMEOUT_SECONDS)))
				{
					LOGGER.log(Level.SEVERE, "[HTML_GENERATION] HTML Generation has taken more than {0} seconds", new Object[]{EngineConstants.HTML_GENERATION_TIMEOUT_SECONDS});
					throw new TimeoutException("[HTML_GENERATION] HTML Generation has taken more than "+EngineConstants.HTML_GENERATION_TIMEOUT_SECONDS+" seconds");  // No I18N
				}
				sheetVar.colWidth[colHeaderIter] = String.valueOf(width);
				sheetVar.tablewidth = sheetVar.tablewidth + width;
				colHeaderIter++;
				colHeaderIndex++;

			}

		}
		List<DataRange> intersectingMergeRange = sheet.getCoveredArea(rowsAry[0], startCol, rowsAry[rowsAry.length - 1], endCol);
		SheetVariables mergeDetails = new SheetVariables();
		mergeDetails.rowheight = workbook.getDefaultRowHeight();
		mergeDetails.colwidth = workbook.getDefaultColumnWidth();
		mergeDetails.defaultCellStyle = EngineUtils.toHtmlStyle(workbook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME), workbook);
		mergeDetails.cellstyle = cellstyle;
		mergeDetails.sheetvalues = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		mergeDetails.sheetformulas = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		mergeDetails.sheetstyles = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		mergeDetails.sheetannots = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		mergeDetails.hyper = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		mergeDetails.literalColspan = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		mergeDetails.literalRowspan = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		mergeDetails.dValidation = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		mergeDetails.colspan = new String[(int) sheetVar.rows][(int) sheetVar.cols];
		mergeDetails.rowspan = new String[(int) sheetVar.rows][(int) sheetVar.cols];

		JSONObjectWrapper mergeInfoNew = new JSONObjectWrapper();
		isMergeInfo = true;

		for(DataRange intersectingMergeRange1 : intersectingMergeRange)
		{
//            	boolean sendMergeResponse =false;
//           if(intersectingMergeRange.get(k).getStartRowIndex() > rowsAry[0] && intersectingMergeRange.get(k).getStartRowIndex()<rowsAry[rowsAry.length-1])	{
//
//           if(intersectingMergeRange.get(k).getEndRowIndex()-intersectingMergeRange.get(k).getStartRowIndex() <=sheetVar.rows-1 ) {
//        	   sendMergeResponse=true;
//           }
//           }else if(intersectingMergeRange.get(k).getStartRowIndex()<rowsAry[0] && ){
//
//           }
//
//           }
			ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(intersectingMergeRange1.getStartRowIndex());
			int colsRepeated = 1;
			int rowsRepeated = rRow.getRowsRepeated();
			Row row = rRow.getRow();
			Cell cell = null;
			if(row != null)
			{
				ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(row.getRowIndex(), intersectingMergeRange1.getStartColIndex());
				cell = rCell.getCell();
				colsRepeated = rCell.getColsRepeated();
			}
			//mergeInfo= readCellDetailsNew(sheet, new ReadOnlyCell(cell, intersectingMergeRange.get(k).getStartRowIndex(), intersectingMergeRange.get(k).getStartColIndex(), rowsRepeated, colsRepeated), sheetVar, intersectingMergeRange.get(k).getStartRowIndex(), intersectingMergeRange.get(k).getStartColIndex(), formHtmlRange, showFormulas,isMergeInfo);
			mergeInfoNew = readCellDetails(container, new ReadOnlyCell(sheet, cell, intersectingMergeRange1.getStartRowIndex(), intersectingMergeRange1.getStartColIndex(), rowsRepeated, colsRepeated), sheetVar, intersectingMergeRange1.getStartRowIndex(), intersectingMergeRange1.getStartColIndex(), formHtmlRange, showFormulas, isMergeInfo);
			mergeInfoNew.put(JSONConstants.START_ROW, intersectingMergeRange1.getStartRowIndex());
			mergeInfoNew.put(JSONConstants.START_COLUMN, intersectingMergeRange1.getStartColIndex());
			mergeInfoNew.put(JSONConstants.END_ROW, intersectingMergeRange1.getEndRowIndex());
			mergeInfoNew.put(JSONConstants.END_COLUMN, intersectingMergeRange1.getEndColIndex());
			sheetVar.mergeAry.put(intersectingMergeRange1.getStartRowIndex() + ":" + intersectingMergeRange1.getStartColIndex(), mergeInfoNew);
		}

		JSONObjectWrapper styleObjct = new JSONObjectWrapper();
		styleObjct.put(ActionConstants.RANGE_STYLE + "", styleAry);
		styleObjct.put("s", sheetName);

		JSONObjectWrapper borderObjct = new JSONObjectWrapper();
		borderObjct.put(ActionConstants.BORDERS + "", borderAry);
		borderObjct.put("s", sheetName);

		JSONObjectWrapper cellObjct = new JSONObjectWrapper();
		cellObjct.put(ActionConstants.CELL + "", cellAry);
		cellObjct.put("s", sheetName);

		//ColorScales
		int sR_C = rowsAry[0];
		int eR_C = rowsAry[sheetVar.rows - 1];

		Collection<DataRange> rangesToLoad = new ArrayList<>();
		BitSet rowsBitSet = new BitSet();
		Arrays.stream(rowsAry).boxed().forEach(index -> rowsBitSet.set(index));

		int startInt = rowsBitSet.nextSetBit(0);
		while(startInt !=-1){
			int endInt = rowsBitSet.nextClearBit(startInt);
			rangesToLoad.add(new DataRange(sheet.getAssociatedName(), startInt, startCol, endInt - 1, endCol));
			startInt = rowsBitSet.nextSetBit(endInt);
		}

		try
		{
			JSONArrayWrapper cfAry = ConditionalStyleResponse.getResponseJson(sheet, rangesToLoad, false);
			sheetVar.conditionalFormatInfo = cfAry.toString();
		}
		catch(ConditionalStyleResponse.ConditionalStyleResponseFailedException e)
		{
			sheetVar.conditionalFormatInfo = JSONConstants.CF_RESPONSE_TIMEOUT;
		}

		List<DataRange> checkboxRange = RangeUtil.getCheckboxRanges(sheet, sR_C, startCol, eR_C, endCol);
		if(!checkboxRange.isEmpty())
		{
			sheetVar.checkboxInfo = checkboxRange.toString();
		}
		JSONArrayWrapper ary = new JSONArrayWrapper();

		ary.put(styleObjct);
		ary.put(borderObjct);
		ary.put(cellObjct);

		//sheetVar.colorScalesInfo = colorScalesAry.toString();
		sheetVar.coveredCellInfo = ary.toString();

		//For Maintaining Col Merge Details.
		sheetVar.colMergeDetails = (sheetVar.colMergeJSON).toString();
		// Set writing mode
		sheetVar.writingMode = sheet.getSheetStyle().getPropertyAsString_deep(SheetStyle.Property.WRITINGMODE, workbook);

		sheetVar.freezepane = DocumentUtils.getFreezePaneInfo(workbook, sheet.getName());

//            try {
//            	sheetVar.serverClip = String.valueOf(ActionUtil.getServerClipObject());
//            } catch (Exception e) {
//            	LOGGER.log(Level.WARNING, e.getMessage());
//            }
		//sheetVar.rowTops = rowObj.toString();
		hashMap.put("SHEETVARIABLES", sheetVar);
		// hashMap.put("BORDERDETAILS", borderJson);
		hashMap.put("rowHeaderLabels", rowHeaderLabels);

		setLastRowBottomBorderDetails(sheet, rowsAry[rowsAry.length - 1], sheetVar);
		return hashMap;
	}

	public static Collection<DataRange> toDataRanges(String asn, IntegralSet rowIndices, int startColIndex, int endColIndex)
	{
		List<LinearIntegralRange> rowRanges = rowIndices.toLinearIntegralRangeList();
		return rowRanges.stream()
				.map(rowRange -> new DataRange(asn, rowRange.getStartInt(), startColIndex, rowRange.getEndInt(), endColIndex))
				.collect(Collectors.toList());
	}

//	public static JSONArrayWrapper borderArrayTrasform(Sheet sheetObj, SheetVariables sheetVar, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, JSONArrayWrapper borderJson) {
//		String prev_bottomBorder = null;
//		String prev_rightBorder = null;
//		int rangeStartRow = startRowIndex;
//		int rangeStartCol = startColIndex;
//
//		for(int rowIndex = startRowIndex, rowIterate = 0; rowIndex <= endRowIndex; rowIndex++, rowIterate++)
//		{
//
//			for(int colIndex = startColIndex, colIterate = 0; colIndex <= endColIndex; colIndex++, colIterate++)
//			{
//				String bottomBorder = sheetVar.cellBottomBorders[rowIterate][colIterate];
//				String rightBorder = sheetVar.cellRightBorders[rowIterate][colIterate];
//				if(rowIndex == startRowIndex && colIndex == startColIndex)
//				{
//					prev_rightBorder = rightBorder;
//					prev_bottomBorder = bottomBorder;
//				}
//				else
//				{
//
//					if(prev_rightBorder == null && prev_bottomBorder == null)
//					{
//						rangeStartRow = rowIndex;
//						rangeStartCol = colIndex;
//						prev_rightBorder = rightBorder;
//						prev_bottomBorder = bottomBorder;
//					}
//					else
//					{
//						boolean isChanged = prev_rightBorder != null ? !prev_rightBorder.equals(rightBorder) : rightBorder != null;
//						if(!isChanged)
//						{
//							isChanged = prev_bottomBorder != null ? !prev_bottomBorder.equals(bottomBorder) : bottomBorder != null;
//						}
//
//						if(isChanged)
//						{
//							int rangeEndRow = rangeStartRow;
//							int rangeEndCol = rangeStartCol;
//							if(rowIndex > rangeStartRow)
//							{
//								rangeEndCol = endColIndex;
//								if(rangeStartCol > startColIndex)
//								{ //Write here with rangestartrow, rangestartcol,rangeendrow & rangeendcol.
//									ResponseObject.updateBorderJson(rangeStartRow, rangeStartCol, rangeEndRow, rangeEndCol, prev_rightBorder, prev_bottomBorder, borderJson);
//
//									rangeStartRow += 1;
//									rangeStartCol = startColIndex;
//								}
//
//								if(rangeStartRow < rowIndex)
//								{
//									rangeEndRow = rowIndex - 1;
//									// Write here with rangestartrow, rangestartcol,rangeendrow & rangeendcol.
//									ResponseObject.updateBorderJson(rangeStartRow, rangeStartCol, rangeEndRow, rangeEndCol, prev_rightBorder, prev_bottomBorder, borderJson);
//									rangeStartRow = rowIndex;
//								}
//							}
//
//							if(colIndex > startColIndex)
//							{
//								rangeEndCol = colIndex - 1;
//								if(rangeStartCol > rangeEndCol)
//								{
//									rangeStartCol = rangeEndCol;
//								}
//								int rowsRepeated = 1;
//								ReadOnlyCell rCell = sheetObj.getReadOnlyCell(rowIndex, colIndex);
//
//								rowsRepeated = rCell.getRowsRepeated();
//								ReadOnlyCell nextRCell = sheetObj.getReadOnlyCell(rowIndex + rowsRepeated, colIndex);
//								int nextCellRIndex = nextRCell.getRowIndex();
//								ResponseObject.updateBorderJson(rangeStartRow, rangeStartCol, nextCellRIndex - 1, rangeEndCol, prev_rightBorder, prev_bottomBorder, borderJson);
//								// Write here with
//								// rangestartrow, rangestartcol,
//								// rangeendrow & rangeendcol.
//							}
//							prev_rightBorder = rightBorder;
//							prev_bottomBorder = bottomBorder;
//							rangeStartRow = rowIndex;
//							rangeStartCol = colIndex;
//						}
//
//					}
//
//
//				}
//
//
//			}
//
//		}
//
//		int rangeEndRow = rangeStartRow;
//		int rangeEndCol = endColIndex;
//		if(rangeStartCol > startColIndex)
//		{
//			// Write here with rangestartrow, rangestartcol, rangeendrow &
//			// rangeendcol.
//			ResponseObject.updateBorderJson(rangeStartRow, rangeStartCol, rangeEndRow, rangeEndCol, prev_rightBorder, prev_bottomBorder, borderJson);
//			rangeStartRow += 1;
//		}
//
//		if(rangeStartRow <= endRowIndex)
//		{
//			rangeEndRow = endRowIndex;
//			ResponseObject.updateBorderJson(rangeStartRow, rangeStartCol, rangeEndRow, rangeEndCol, prev_rightBorder, prev_bottomBorder, borderJson);
//			// Write here with rangestartrow, rangestartcol, rangeendrow &
//			// rangeendcol.
//		}
//		if(borderJson.isEmpty())
//		{
//			borderJson.put(ResponseObject.getBorderJson(startRowIndex, startColIndex, endRowIndex, endColIndex, null, null));
//		}
//		return borderJson;
//
//	}


	private void setLastRowBottomBorderDetails(Sheet sheet, int rowIndex, SheetVariables sheetVar)
	{

		Row rowVal = sheet.getReadOnlyRowFromShell(RangeUtil.getNthVisibleRowIndex(sheet, rowIndex, 2)).getRow();
		String defaultBorder = null;
		if(rowVal != null)
		{
			for(Cell nextRowCell : rowVal.getCells())
			{

				if(nextRowCell != null)
				{

					int colIndex = nextRowCell.getColumnIndex();
					//int colsRepeated=nextRowCell.getColSpan();
					int colsRepeated = sheet.getMergeCellSpans(nextRowCell)[1];

					if(colsRepeated == 1)
					{
						colsRepeated = nextRowCell.getColsRepeated();
					}
					if(colIndex >= sheetVar.cols)
					{
						break;
					}
					CellStyle appliedCellStyleNext = ((CellImpl) nextRowCell).getCellStyleReadOnly();
					String nextCellTopBorderInfo = null;
					if(appliedCellStyleNext != null)
					{
						String topBorderNextRowCell = appliedCellStyleNext.getPropertyAsString_deep(CellStyle.Property.BORDERTOP, sheet.getWorkbook());

						if(topBorderNextRowCell != null && !"none".equals(topBorderNextRowCell))
						{
							nextCellTopBorderInfo = topBorderNextRowCell;
						}

						if(nextCellTopBorderInfo != null && !"none".equals(nextCellTopBorderInfo))
						{
							for(int colIter = 0; colIter < colsRepeated; colIter++)
							{
								int currCol = colIndex + colIter;
								if(currCol >= sheetVar.cols)
								{
									break;
								}
//			               				if(sheetVar.cellBottomBorders[sheetVar.rows-1][currCol]==null|| sheetVar.cellBottomBorders[sheetVar.rows-1][currCol].equals(defaultBorder)){
//					               				sheetVar.cellBottomBorders[sheetVar.rows-1][currCol]=nextCellTopBorderInfo;
//					               		}

								JSONObjectWrapper newCellObject = new JSONObjectWrapper();
								if(!sheetVar.dataObject.has(String.valueOf(rowIndex)))
								{
									JSONObjectWrapper newRowObject = new JSONObjectWrapper();
									sheetVar.dataObject.put(String.valueOf(rowIndex), newRowObject);
									//  sheetVar.dataObject.getJSONObject(String.valueOf(0)).put(String.valueOf(colIndex + c), newCellObject);
								}
								JSONObjectWrapper styleObject = new JSONObjectWrapper();
								styleObject.put("8", nextCellTopBorderInfo);

								if(!sheetVar.dataObject.getJSONObject(String.valueOf(rowIndex)).has(String.valueOf(currCol)))
								{
									newCellObject.put(CellConstants.CELLSTYLE, styleObject);

									sheetVar.dataObject.getJSONObject(String.valueOf(rowIndex)).put(String.valueOf(currCol), newCellObject);
								}
								else
								{
									JSONObjectWrapper requiredCellObject = sheetVar.dataObject.getJSONObject(String.valueOf(rowIndex)).getJSONObject(String.valueOf(currCol));

									if(requiredCellObject.has(CellConstants.CELLSTYLE))
									{

										if(requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).has("8"))
										{
											String borderValuePresent = requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).getString("8");
											if(!borderValuePresent.equals(defaultBorder))
											{
												requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).put("8", nextCellTopBorderInfo);
											}
										}
										else
										{
											requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).put("8", nextCellTopBorderInfo);
										}

									}
									else
									{

										requiredCellObject.put(CellConstants.CELLSTYLE, styleObject);

									}
								}

							}
						}
					}

				}
			}

		}
	}

	public static void setLastRowBottomBorderDetailsJson(Sheet sheet, JSONObjectWrapper dataObject, int rowIndex, int endColIndex)
	{
		Row rowVal = sheet.getReadOnlyRowFromShell(RangeUtil.getNthVisibleRowIndex(sheet, rowIndex, 2)).getRow();
		if(rowVal != null)
		{
			for(Cell nextRowCell : rowVal.getCells())
			{

				if(nextRowCell != null)
				{

					int colIndex = nextRowCell.getColumnIndex();
					//int colsRepeated=nextRowCell.getColSpan();
					int colsRepeated = sheet.getMergeCellSpans(nextRowCell)[1];

					if(colsRepeated == 1)
					{
						colsRepeated = nextRowCell.getColsRepeated();
					}
					if(colIndex > endColIndex)
					{
						break;
					}
					CellStyle appliedCellStyleNext = ((CellImpl) nextRowCell).getCellStyleReadOnly();
					String nextCellTopBorderInfo = null;
					if(appliedCellStyleNext != null)
					{
						String topBorderNextRowCell = appliedCellStyleNext.getPropertyAsString_deep(CellStyle.Property.BORDERTOP, sheet.getWorkbook());

						if(topBorderNextRowCell != null && !"none".equals(topBorderNextRowCell))
						{
							nextCellTopBorderInfo = topBorderNextRowCell;
						}
						if(nextCellTopBorderInfo != null && !"none".equals(nextCellTopBorderInfo))
						{
							for(int colIter = 0; colIter < colsRepeated; colIter++)
							{
								int currCol = colIndex + colIter;
								if(currCol > endColIndex)
								{
									break;
								}
//			               				if(sheetVar.cellBottomBorders[sheetVar.rows-1][currCol]==null|| sheetVar.cellBottomBorders[sheetVar.rows-1][currCol].equals(defaultBorder)){
//					               				sheetVar.cellBottomBorders[sheetVar.rows-1][currCol]=nextCellTopBorderInfo;
//					               		}

								JSONObjectWrapper newCellObject = new JSONObjectWrapper();
								if(!dataObject.has(String.valueOf(rowIndex)))
								{
									JSONObjectWrapper newRowObject = new JSONObjectWrapper();
									dataObject.put(String.valueOf(rowIndex), newRowObject);
									//  sheetVar.dataObject.getJSONObject(String.valueOf(0)).put(String.valueOf(colIndex + c), newCellObject);
								}
//                              		  	JSONObjectWrapper styleObject=new JSONObjectWrapper();
//                           			 	styleObject.put("8",nextCellTopBorderInfo);

								if(!dataObject.getJSONObject(String.valueOf(rowIndex)).has(String.valueOf(currCol)))
								{
									newCellObject.put(CellConstants.BORDERBOTTOM, nextCellTopBorderInfo);

									dataObject.getJSONObject(String.valueOf(rowIndex)).put(String.valueOf(currCol), newCellObject);
								}
								else
								{
									JSONObjectWrapper requiredCellObject = dataObject.getJSONObject(String.valueOf(rowIndex)).getJSONObject(String.valueOf(currCol));

									if(!requiredCellObject.has(CellConstants.BORDERBOTTOM) || (requiredCellObject.has(CellConstants.BORDERBOTTOM) && requiredCellObject.getString(CellConstants.BORDERBOTTOM).contains("transparent")))
									{
										requiredCellObject.put(CellConstants.BORDERBOTTOM, nextCellTopBorderInfo);
									}
								}

							}
						}
					}

				}
			}

		}

	}


	public static void setLastColRightBorderDetails(Sheet sheet, SheetVariables sheetVar, int colIndex, int startRowIndex, int endRowIndex, int endColIndex)
	{

		for(int rowIndex = startRowIndex, rowIter = 0; rowIndex <= endRowIndex; rowIndex++, rowIter++)
		{

			ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(rowIndex, endColIndex);
			int rowsRepeated = rCell.getRowsRepeated();
			Cell nextColCell = rCell.getCell();
			if(nextColCell != null)
			{


				int rowSpan = 1;
				rowSpan = sheet.getMergeCellSpans(nextColCell)[0];
				if(rowsRepeated == 1)
				{
					rowsRepeated = rowSpan;
				}
				CellStyle appliedCellStyleNext = ((CellImpl) nextColCell).getCellStyleReadOnly();
				String nextCellLeftBorderInfo = null;
				if(appliedCellStyleNext != null)
				{
					String leftBorderNextRowCell = appliedCellStyleNext.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, sheet.getWorkbook());

					if(leftBorderNextRowCell != null && !"none".equals(leftBorderNextRowCell))
					{
						nextCellLeftBorderInfo = leftBorderNextRowCell;
					}
					for(int r = 0; r < rowsRepeated; r++)
					{
						if(nextCellLeftBorderInfo != null && !"none".equals(nextCellLeftBorderInfo))
						{
							if((rowIndex + r) <= endRowIndex)
							{
								if(sheetVar.cellRightBorders[rowIter + r][colIndex] != "none" || !sheetVar.cellRightBorders[rowIter + r][colIndex].contains("transparent"))
								{
									sheetVar.cellRightBorders[rowIter + r][colIndex] = nextCellLeftBorderInfo;
								}

							}

						}
					}


				}

			}
			rowIter += rowsRepeated - 1;
			rowIndex += rowsRepeated - 1;
		}
	}


	public static void setLastColRightBorderDetailsJson(Sheet sheet, JSONObjectWrapper dataObject, int colIndex, int startRowIndex, int endRowIndex)
	{

		for(int rowIndex = startRowIndex, rowIter = 0; rowIndex <= endRowIndex; rowIndex++, rowIter++)
		{

			ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(rowIndex, colIndex + 1);
			int rowsRepeated = rCell.getRowsRepeated();
			Cell nextColCell = rCell.getCell();
			if(nextColCell != null)
			{


				CellStyle appliedCellStyleNext = ((CellImpl) nextColCell).getCellStyleReadOnly();
				int rowSpan = sheet.getMergeCellSpans(nextColCell)[0];
				if(rowsRepeated == 1)
				{
					rowsRepeated = rowSpan;
				}
				String nextCellLeftBorderInfo = null;
				if(appliedCellStyleNext != null)
				{
					String leftBorderNextRowCell = appliedCellStyleNext.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, sheet.getWorkbook());

					if(leftBorderNextRowCell != null && !"none".equals(leftBorderNextRowCell))
					{
						nextCellLeftBorderInfo = leftBorderNextRowCell;
					}
					//sheetVar.cellRightBorders[rowIter+r][colIndex]=nextCellLeftBorderInfo;
					JSONObjectWrapper nextRowObject = new JSONObjectWrapper();
					for(int r = 0; r < rowsRepeated; r++)
					{
						if(nextCellLeftBorderInfo != null && !"none".equals(nextCellLeftBorderInfo))
						{
							if((rowIndex + r) <= endRowIndex)
							{

								if(dataObject.has(String.valueOf(rowIndex + r)))
								{
									nextRowObject = dataObject.getJSONObject(String.valueOf(rowIndex + r));
								}
								JSONObjectWrapper nextCellObject = new JSONObjectWrapper();
								if(nextRowObject.has(String.valueOf(colIndex)))
								{
									nextCellObject = nextRowObject.getJSONObject(String.valueOf(colIndex));
								}
								if(!nextCellObject.has(CellConstants.BORDERRIGHT) || (nextCellObject.has(CellConstants.BORDERRIGHT) && (nextCellObject.getString(CellConstants.BORDERRIGHT).contains("transparent") || nextCellObject.getString(CellConstants.BORDERRIGHT).equals("none"))))
								{
									nextCellObject.put(CellConstants.BORDERRIGHT, nextCellLeftBorderInfo);
									nextRowObject.put(String.valueOf(colIndex), nextCellObject);
								}

							}

						}
						if(nextRowObject.length() > 0)
						{
							dataObject.put(String.valueOf(rowIndex + r), nextRowObject);
						}

					}


				}

			}
			rowIter += rowsRepeated - 1;
			rowIndex += rowsRepeated - 1;
		}
	}

	private JSONObjectWrapper readCellDetails(WorkbookContainer workbookContainer, ReadOnlyCell rCell, SheetVariables sheetVar, int rowIterate, int colIterate, boolean formHtmlRange, boolean showFormulas, boolean isMergeInfo)
	{
		JSONObjectWrapper cellObject = new JSONObjectWrapper();

		Sheet sheet = rCell.getSheet();
		int rowIndex = rCell.getRowIndex();
		int colIndex = rCell.getColIndex();

		if(!isMergeInfo && sheetVar.dataObject.has(String.valueOf(rowIndex)))
		{
			if(sheetVar.dataObject.getJSONObject(String.valueOf(rowIndex)).has(String.valueOf(colIndex)))
			{
				cellObject = sheetVar.dataObject.getJSONObject(String.valueOf(rowIndex)).getJSONObject(String.valueOf(colIndex));
			}
		}

		String cellValue = null;
		Type valueType = null;

		int colSpan = 1;
		int rowSpan = 1;
		int literalRowSpan = 1;
		int literalColSpan = 1;

		String styleName = null;
		String patternColor = null;
		Type patternType = null;

		if(rCell.getCell() != null)
		{
			Cell cell = rCell.getCell();

			styleName = rCell.getCell().getStyleName();
			patternColor = rCell.getCell().getContentColor();
			patternType = rCell.getCell().getContentType();

			if(cell.getValue() instanceof PicklistValue)
			{
				Picklist picklist = ((PicklistValue) cell.getValue()).getPicklist();
				if(!picklist.isShowAsBubble()) {
					PicklistItem picklistItem = ((PicklistValue) cell.getValue()).getPicklistItems().get(0);
					PicklistStyle picklistStyle = picklistItem.getStyle();
					if(picklistStyle != null)
					{
						cellObject.put(CellConstants.PICKLIST, picklistStyle.getJSON(sheet.getWorkbook().getTheme()));
					}
				}
			}
			else if(cell.getValue() instanceof ImageValue)
			{
				ImageValue imageValue = (ImageValue) cell.getValue();
				JSONArrayWrapper imageValueArray = new JSONArrayWrapper();

				imageValueArray.put(imageValue.getMode().getId());
				imageValueArray.put(imageValue.getImageID());
				imageValueArray.put(imageValue.getImageHeight());
				imageValueArray.put(imageValue.getImageWidth());
				cellObject.put(CellConstants.IMAGE, imageValueArray);
			}

			// Merge
			colSpan = sheet.getMergeCellSpans(cell)[1];
			rowSpan = sheet.getMergeCellSpans(cell)[0];
			literalRowSpan = rowSpan;
			literalColSpan = colSpan;
			if(!isMergeInfo)
			{
				rowSpan = (rowIterate + rowSpan) > sheetVar.rows ? (sheetVar.rows - rowIterate) : rowSpan;
				colSpan = (colIterate + colSpan) > sheetVar.cols ? (sheetVar.cols - colIterate) : colSpan;
			}
			if(!(rowSpan == 1 && colSpan == 1))
			{
				cellObject.put(CellConstants.ROWSPAN, rowSpan);
				cellObject.put(CellConstants.COLSPAN, colSpan);
			}
			/////////////////Merge End

			// Cell Type/Formula/Content
			valueType = cell.getType();
			cellValue = cell.getContent();
			String cellFormula = (showFormulas || !cell.isFormula()) ? cell.getLocalizedFormula() : "hide"; //No I18N

			if(cellFormula != null && !cellFormula.equals(cellValue) && !"".equals(cellFormula))
			{
				cellObject.put(CellConstants.CELLFORMULA, Utility.getEncodedString(cellFormula).replace("+", "%20"));//NO I18N
			}

			if(cellValue != null)
			{
				String urlEncodedCellValue = Utility.getEncodedString(cellValue).replace("+", "%20");
				cellObject.put(CellConstants.CELLVALUE, urlEncodedCellValue);
				String xmlEnCodedCellVal = EngineUtils1.forHTMLTag(cellValue);
				if(!cellValue.equals(xmlEnCodedCellVal))
				{
					String urlEncodedXMLEncodedCellValue = Utility.getEncodedString(xmlEnCodedCellVal).replace("+", "%20");
					cellObject.put(CellConstants.ENCODEDCELLVALUE, urlEncodedXMLEncodedCellValue);
				}
			}
			//[[0,3,{}],[]]
			JSONArrayWrapper textStringProp = ResponseUtils.getCellRichStringContents(cell);
			if(!textStringProp.isEmpty())
			{
				JSONArrayWrapper cellTextStyleList = new JSONArrayWrapper();
				String fId, fieldName;
				int fieldId;
				for(int i = 0; i < textStringProp.length(); i++)
				{
					fId = "";// No I18N
					fieldName = "";// No I18N
					JSONArrayWrapper cellTextStyle = textStringProp.getJSONArray(i);
					String cellstyle = cellTextStyle.getString(2);
					JSONObjectWrapper textStyleObj = new JSONObjectWrapper();
					if(!cellstyle.equalsIgnoreCase(""))
					{
						TextStyle textStyle = sheet.getWorkbook().getTextStyle(cellTextStyle.getString(2));
						JSONArrayWrapper TextStyleAry = ResponseUtils.getTextStyleDefinition(sheet.getWorkbook(), textStyle);

						textStyleObj.put(CellConstants.TEXTCOLOR, TextStyleAry.get(0));
						textStyleObj.put(CellConstants.FONTSIZE, TextStyleAry.get(1));
						textStyleObj.put(CellConstants.FONTFAMILY, TextStyleAry.get(2));
						textStyleObj.put(CellConstants.FONTWEIGHT, TextStyleAry.get(3));
						textStyleObj.put(CellConstants.FONTSTYLE, TextStyleAry.get(4));
						textStyleObj.put(CellConstants.STRIKETHROUGH, TextStyleAry.get(5));
						textStyleObj.put(CellConstants.TEXTDECORATION, TextStyleAry.get(6));
					}

					JSONArrayWrapper newTextStyleProps = new JSONArrayWrapper();
					newTextStyleProps.put(0, cellTextStyle.get(0));
					newTextStyleProps.put(1, cellTextStyle.get(1));
					newTextStyleProps.put(2, textStyleObj);


					fId = cellTextStyle.getString(3);
					if(!(fId.equalsIgnoreCase("")))
					{
						fieldId = Integer.parseInt(fId);
						Field field = sheet.getWorkbook().getField(fieldId);
						fieldName = field.getName();
					}
					newTextStyleProps.put(3, fieldName);
					newTextStyleProps.put(4, cellTextStyle.get(4));
					cellTextStyleList.put(newTextStyleProps);
				}
				cellObject.put(CellConstants.TEXTSTYLE, cellTextStyleList);
			}
		}

		if(isMergeInfo)
		{
			cellObject.put(CellConstants.LITERALROWSPAN, literalRowSpan);
			cellObject.put(CellConstants.LITERALCOLSPAN, literalColSpan);
		}

		/**
		 * **************** HyperLinks ******************
		 */
		if(rCell.getCell() != null)
		{
			JSONObjectWrapper hyperJSON = null;
			Cell cell = rCell.getCell();
			String value = getLink(cell);
			if(value != null)
			{
				if(value.contains("<a"))
				{
					hyperJSON = new JSONObjectWrapper();

					String tempHyper;

					int noOfLinks = value.split("<a idx='link").length;//NO I18N

					//if ( noOfLinks <= 2 && value.endsWith("</a>")){//NO I18N
					if(noOfLinks <= 2 && value.startsWith("<a idx='link") && value.endsWith("</a>"))//NO I18N
					{
						tempHyper = "full";//NO I18N
					}
					else
					{
						tempHyper = "multi";//NO I18N
					}

					//Have To Optimize In Server Side.
					hyperJSON.put("0", value.substring(cellValue.indexOf("href='") + 6, value.indexOf(" title='")));//NO I18N

					hyperJSON.put("1", tempHyper);//NO I18N

					hyperJSON.put("2", Utility.getEncodedString(value).replace("+", "%20"));//NO I18N
				}
			}

			if(hyperJSON != null)
			{
				cellObject.put(CellConstants.CELLHYPER, hyperJSON);//NO I18N
			}

			/**
			 * **************** Cell Comments ******************
			 */
			Annotation annotation = cell.getAnnotation();

			if(annotation != null)
			{
				JSONObjectWrapper annotObj = new JSONObjectWrapper();
				String content = annotation.getContent();

				if(content != null)
				{
					if(annotation.getX() != null && annotation.getY() != null)
					{
						int xPos = EngineUtils1.convertToPixels(annotation.getX(), 96);
						int yPos = EngineUtils1.convertToPixels(annotation.getY(), 96);

						annotObj.put("x", xPos);
						annotObj.put("y", yPos);
					}
					annotObj.put("v", (Utility.getEncodedString(content.trim())).replace("+", "%20"));
				}

				cellObject.put(CellConstants.CELLANNOT, annotObj.toString());
			}

			/**
			 * **************** Data Validation ******************
			 */
			ContentValidation cv = ((CellImpl) cell).getContentValidationReadOnly();

			// TODO : Use the above ContentValidationObject to get the details instead of getting the object again in the method getDataValidationForCell.
			if(cv != null)
			{
				JSONObjectWrapper dv = new JSONObjectWrapper();
				if(workbookContainer.getHighlightInvalidCell())
				{
					Boolean contentValid = ((CellImpl) cell).isContentValid();
					if(contentValid != null && contentValid == false)
					{
						dv.set("contentValid", contentValid); //No I18N
					}
				}
				String dvC = DataValidationUtils.getDataValidationForCell(cv, dv);
				if(dvC != null)
				{
					cellObject.put(CellConstants.DATAVALIDATION, Utility.getEncodedString(dvC).replace("+", "%20"));
				}
			}
		}

		if(!isMergeInfo && !"".equals(cellValue))
		{
			/// ?????
			if(sheetVar.usedRow <= rowIterate)
			{
				sheetVar.usedRow = rowIterate + 1;
			}
			sheetVar.usedCol = colIterate + 1;
		}

		if(styleName == null)
		{
			ReadOnlyColumnHeader rCH = sheet.getReadOnlyColumnHeader(colIndex);
			ColumnHeader ch = rCH.getColumnHeader();
			if(ch != null)
			{
				styleName = ch.getDefaultCellStyleName();
			}
		}

		CellStyle cStyle = null;
		if(styleName != null && !styleName.equals("Default"))
		{
			cStyle = sheet.getWorkbook().getCellStyle(styleName);
		}

        /*
            0 : BGColor
            1 : TextColor
            2 : FontWeight
            3 : FontStyle
            4 : TextDecoration
            5 : FontFamily
            6 : FontSize
            7 : HAlign
            8 : Wrap
            9 : ContentType
            10: VAlign
            11: strikeThrough
        */
		String[] default_format_split = SheetJspUtil.getDefaultFormats(sheet.getWorkbook());
		String[] format_split = handleCellStyles(sheet.getWorkbook(), cStyle, default_format_split, patternColor, patternType, valueType);

		StringBuilder className1 = new StringBuilder();
		JSONObjectWrapper style1 = new JSONObjectWrapper();
		if(!format_split[0].equals(default_format_split[0]))
		{
			//Making White For Transparent(Since Merge Cell Issue)
			if(!format_split[0].equalsIgnoreCase("transparent"))//NO I18N
			{
				style1.put(CellConstants.BACKGROUNDCOLOR, format_split[0]);
			}
		}

		if(!format_split[1].equals(default_format_split[1]))
		{
			style1.put(CellConstants.TEXTCOLOR, format_split[1]);
		}

		if(!format_split[2].equals(default_format_split[2]))
		{
			style1.put(CellConstants.FONTWEIGHT, format_split[2]);
			className1.append(" fwb");//NO I18N
		}

		if(!format_split[3].equals(default_format_split[3]))
		{
			style1.put(CellConstants.FONTSTYLE, format_split[3]);
			className1.append(" fsi");//NO I18N
		}

		if(!format_split[4].equals(default_format_split[4]))
		{
			style1.put(CellConstants.TEXTDECORATION, format_split[4]);
			className1.append(" tdu");//NO I18N
		}

		if(!format_split[11].equals(default_format_split[11]))
		{
			if((className1.indexOf(" tdu") != -1))
			{
				className1.append(" tdsu");//NO I18N
			}
			else
			{
				style1.put(CellConstants.STRIKETHROUGH, format_split[4]);
				className1.append(" tdst");//NO I18N
			}
		}

		if(format_split[5] != null)
		{

			String fontFamily = SheetJspUtil.getFontFamily(format_split[5]);

			if(fontFamily != null)
			{
				className1.append(fontFamily);
			}

			style1.put(CellConstants.FONTFAMILY, format_split[5]);

		}

		if(format_split[6] != null)
		{
			String fontSize = SheetJspUtil.getFontSize(format_split[6]);

			if(fontSize != null)
			{
				className1.append(fontSize);
			}

			style1.put(CellConstants.FONTSIZE, format_split[6]);
		}

		String valueFormat = null;
		if(!format_split[9].equals(default_format_split[9]))
		{
			valueFormat = format_split[9];
		}

		if(format_split[7] != null)
		{
			switch(format_split[7])
			{
				case "center"://NO I18N
					className1.append(" fac");//NO I18N
					break;
				case "right"://NO I18N
					className1.append(" far");//NO I18N
					if(format_split[13] != null)
					{
						style1.put(CellConstants.PADDINGRIGHT, format_split[13]);
					}
					break;
				case "left"://NO I18N
					className1.append(" fal");//NO I18N
					if(format_split[13] != null)
					{
						style1.put(CellConstants.PADDINGLEFT, format_split[13]);
					}
					break;
				default:
					style1.put("5", format_split[7]);//NO I18N
					if(format_split[13] != null)
					{
						style1.put(CellConstants.PADDINGLEFT, format_split[13]);
					}
					break;
			}
		}
		else if(valueFormat != null && !"STANDARD".equals(valueFormat) && !"STRING".equals(valueFormat))
		{
			className1.append(" tar");//NO I18N
		}

		if(format_split.length >= 13 && format_split[12] != null && !("none").equals(format_split[12]))
		{
			style1.put("11", format_split[12]);//NO I18N
		}

		String wrap = format_split[8];
		if(("shrinkToFit").equals(wrap))
		{
			int contentwidth = 0;
			if(rCell.getCell() != null)
			{
				Cell cell = rCell.getCell();
				contentwidth = ((CellImpl) cell).calculateCellContentWidth(((CellImpl) cell).getContentWithFieldNode());
				if(format_split[13] != null)
				{
					if(!format_split[13].equals(default_format_split[13]))
					{
						contentwidth = contentwidth + (Integer.parseInt(format_split[13]) * EngineConstants.ZSINDENT);
					}
				}
			}
			cellObject.put(CellConstants.CONTENTWIDTH, contentwidth);
		}
//        if (format_split[8].equals("wrap"))
//        {
//            wrap = "wrap" ;
//        }

		if(!format_split[10].equals(default_format_split[10]))
		{
			if(format_split[10].equals("bottom"))//NO I18N
			{
				String verticalAlign = " vab";//NO I18N
				className1.append(verticalAlign);
			}
			else if(format_split[10].equals("middle"))//NO I18N
			{
				String verticalAlign = " vam";//NO I18N
				className1.append(verticalAlign);
			}
		}
		if(format_split[13] != null)
		{
			if(!format_split[13].equals(default_format_split[13]))
			{
				style1.put(CellConstants.ZSINDENT, format_split[13]);
			}
		}

		if(format_split[14] != null)
		{
			if(!format_split[14].equals(default_format_split[14]))
			{
				if(rCell.getCell() != null && (wrap.equals("nW")))
				{
					long paddingtop = 0;
					int contentwidth = 0;
					Cell cell = rCell.getCell();
					contentwidth = ((CellImpl) cell).calculateCellContentWidth(((CellImpl) cell).getContentWithFieldNode());
					int deg = Integer.parseInt(format_split[14]);
//                       if(!(wrap.equals("nW"))){
//                           contentwidth = ((CellImpl) cell).getColumn().getSheet().getWorkbook().getDefaultColumnWidth();
//                       }
					paddingtop = Math.round(Math.abs(contentwidth * Math.cos((90 - deg) * (3.14 / 180))));
					style1.put(CellConstants.PADDINGTOP, paddingtop);
				}
				style1.put(CellConstants.ROTATION, format_split[14]);
			}
		}

		JSONObjectWrapper rangeAndReadborderDetails = new JSONObjectWrapper();

		rangeAndReadborderDetails.put("startRow", sheetVar.rowsAry[0]);
		rangeAndReadborderDetails.put("endRow", sheetVar.rowsAry[sheetVar.rows - 1]);
		rangeAndReadborderDetails.put("readTopMostBorder", sheetVar.readTopMostBorder);
		rangeAndReadborderDetails.put("readLeftMostBorder", sheetVar.readLeftMostBorder);

		//handleBorderNew3(cStyle, sheetVar, rowSpan, colSpan, rowIterate, colIterate, formHtmlRange, sheet.getWorkbook().getCellStyle("Default"),true,rangeAndReadborderDetails);//No I18N
		JSONObjectWrapper borderJsonVal = handleBorderNew2(cStyle, sheetVar.dataObject, rowSpan, colSpan, rowIndex, colIndex, formHtmlRange, sheet.getWorkbook().getCellStyle("Default"), cellObject, rangeAndReadborderDetails, true, sheet.getWorkbook());//No I18N

		String borderBottom = borderJsonVal.has("CELLBOTTOMBORDER") ? borderJsonVal.getString("CELLBOTTOMBORDER") : null;//No I18N
		String borderRight = borderJsonVal.has("CELLRIGHTBORDER") ? borderJsonVal.getString("CELLRIGHTBORDER") : null;//No I18N

		ZSTheme theme = sheet.getWorkbook().getTheme();
		if(isMergeInfo && (borderRight == null || borderRight.contains("transparent")) && colSpan > 1)
		{
			ReadOnlyCell lastColRCell = sheet.getReadOnlyCellFromShell(rowIndex, colIndex + literalColSpan - 1);

			if(lastColRCell.getCell() != null)
			{
				Cell lastColCell = lastColRCell.getCell();
				CellStyle lastColCStyle = ((CellImpl) lastColCell).getCellStyleReadOnly();

				if(lastColCStyle != null && lastColCStyle.getProperty(CellStyle.Property.BORDERRIGHT) != null && !BorderProperties.BORDER_NONE.equals(lastColCStyle.getProperty(CellStyle.Property.BORDERRIGHT)))
				{
					borderRight = getBorderValue1((BorderProperties) lastColCStyle.getProperty_Deep(CellStyle.Property.BORDERRIGHT, sheet.getWorkbook()), theme);
				}
			}

			if(borderRight != null)
			{
				ReadOnlyCell rCellAfterSpan = sheet.getReadOnlyCellFromShell(rowIndex, colIndex + literalColSpan);

				if(rCellAfterSpan.getCell() != null)
				{
					Cell CellAfterSpan = rCellAfterSpan.getCell();
					int nextCellRowSpan = sheet.getMergeCellSpans(CellAfterSpan)[0];

					if(nextCellRowSpan >= literalRowSpan)
					{
						CellStyle nextCellCStyle = ((CellImpl) CellAfterSpan).getCellStyleReadOnly();
						if(nextCellCStyle != null && nextCellCStyle.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, sheet.getWorkbook()) != null)
						{
							borderRight = getBorderValue1((BorderProperties) nextCellCStyle.getProperty_Deep(CellStyle.Property.BORDERLEFT, sheet.getWorkbook()), theme);
						}
					}
				}
			}
		}

		if(isMergeInfo && (borderBottom == null || borderBottom.contains("transparent")) && rowSpan > 1)
		{
			ReadOnlyCell lastRowRCell = sheet.getReadOnlyCellFromShell(rowIndex + literalRowSpan - 1, colIndex);

			if(lastRowRCell.getCell() != null)
			{
				Cell lastRowCell = lastRowRCell.getCell();
				CellStyle lastRowCStyle = ((CellImpl) lastRowCell).getCellStyleReadOnly();

				if(lastRowCStyle != null && lastRowCStyle.getPropertyAsString_deep(CellStyle.Property.BORDERBOTTOM, sheet.getWorkbook()) != null && !BorderProperties.BORDER_NONE.equals(lastRowCStyle.getProperty(CellStyle.Property.BORDERBOTTOM)))
				{
					borderBottom = getBorderValue1((BorderProperties) lastRowCStyle.getProperty_Deep(CellStyle.Property.BORDERBOTTOM, sheet.getWorkbook()), theme);
				}
			}

			if(borderBottom != null)
			{
				ReadOnlyCell rCellAfterSpan = sheet.getReadOnlyCellFromShell(rowIndex + literalRowSpan, colIndex);

				if(rCellAfterSpan.getCell() != null)
				{
					Cell CellAfterSpan = rCellAfterSpan.getCell();

					int nextCellColSpan = sheet.getMergeCellSpans(CellAfterSpan)[1];

					if(nextCellColSpan >= literalColSpan)
					{
						CellStyle nextCellCStyle = ((CellImpl) CellAfterSpan).getCellStyleReadOnly();

						if(nextCellCStyle != null && nextCellCStyle.getPropertyAsString_deep(CellStyle.Property.BORDERTOP, sheet.getWorkbook()) != null)
						{
							borderBottom = getBorderValue1((BorderProperties) nextCellCStyle.getProperty_Deep(CellStyle.Property.BORDERTOP, sheet.getWorkbook()), theme);
						}
					}
				}
			}
		}

		if(borderBottom != null)
		{
			style1.put(CellConstants.BORDERBOTTOM, borderBottom);
		}

		if(borderRight != null)
		{
			style1.put(CellConstants.BORDERRIGHT, borderRight);
		}

		if(style1.length() > 0)
		{
			cellObject.put(CellConstants.CELLSTYLE, style1);
		}

		if(className1.length() > 0)
		{
			cellObject.put(CellConstants.CLASSNAME, className1.toString());
		}

		if(!(wrap.equals("nW")))  //No I18N
		{
			cellObject.put(CellConstants.WRAP, wrap);
		}

		if(valueFormat != null)
		{
			cellObject.put(CellConstants.VALUEFORMAT, SheetJspUtil.getFormatEQUIV(valueFormat));
		}

		return cellObject;
	}

	public static JSONObjectWrapper handleBorderNew2(CellStyle cellStyle, JSONObjectWrapper dataObject, int rowSpan, int colSpan, int rowIndex, int colIndex, boolean formHtmlRange, CellStyle defaultCellStyle, JSONObjectWrapper cellObject, JSONObjectWrapper rangeAndReadborderDetails, boolean isFromScroll, Workbook workbook)
	{

		JSONObjectWrapper borderObject = new JSONObjectWrapper();
		JSONObjectWrapper cellStyleVal = new JSONObjectWrapper();
		if(cellObject.has(CellConstants.CELLSTYLE))
		{
			cellStyleVal = cellObject.getJSONObject(CellConstants.CELLSTYLE);
		}

		if(cellStyle == null && defaultCellStyle == null)
		{
			return null;
		}

		String defaultBorder = null;
		String borderValue = "";

		BorderProperties topBorder = null;
		BorderProperties bottomBorder = null;
		BorderProperties leftBorder = null;
		BorderProperties rightBorder = null;

		if(cellStyle != null)
		{
			topBorder = (BorderProperties) cellStyle.getProperty_Deep(CellStyle.Property.BORDERTOP, workbook);
			bottomBorder = (BorderProperties) cellStyle.getProperty_Deep(CellStyle.Property.BORDERBOTTOM, workbook);
			leftBorder = (BorderProperties) cellStyle.getProperty_Deep(CellStyle.Property.BORDERLEFT, workbook);
			rightBorder = (BorderProperties) cellStyle.getProperty_Deep(CellStyle.Property.BORDERRIGHT, workbook);
		}

		if(defaultCellStyle != null)
		{
			topBorder = topBorder == null ? (BorderProperties) defaultCellStyle.getProperty(CellStyle.Property.BORDERTOP) : topBorder;
			bottomBorder = bottomBorder == null ? (BorderProperties) defaultCellStyle.getProperty(CellStyle.Property.BORDERBOTTOM) : bottomBorder;
			leftBorder = leftBorder == null ? (BorderProperties) defaultCellStyle.getProperty(CellStyle.Property.BORDERLEFT) : leftBorder;
			rightBorder = rightBorder == null ? (BorderProperties) defaultCellStyle.getProperty(CellStyle.Property.BORDERRIGHT) : rightBorder;
		}

		if(cellStyle != null && cellStyle.getPropertyAsString_deep(CellStyle.Property.BACKGROUNDCOLOR, workbook) != null && !cellStyle.getPropertyAsString_deep(CellStyle.Property.BACKGROUNDCOLOR, workbook).equalsIgnoreCase("transparent"))
		{
//            if (topBorder == null || topBorder.equals(BorderProperties.BORDER_NONE))
//            {
//                topBorder = BorderProperties.BORDER_TRANSPARENT;
//            }
//
//            if (bottomBorder == null || bottomBorder.equals(BorderProperties.BORDER_NONE))
//            {
//                bottomBorder = BorderProperties.BORDER_TRANSPARENT;
//            }
//
//            if (leftBorder == null || leftBorder.equals(BorderProperties.BORDER_NONE))
//            {
//                leftBorder = BorderProperties.BORDER_TRANSPARENT;
//            }
//
//            if (rightBorder == null || rightBorder.equals(BorderProperties.BORDER_NONE))
//            {
//                rightBorder = BorderProperties.BORDER_TRANSPARENT;
//            }
		}

		if(topBorder != null)
		{
			borderObject.put("CELLTOPBORDER", defaultBorder);

			if(!BorderProperties.BORDER_NONE.equals(topBorder))
			{
				borderValue = getBorderValue1(topBorder, workbook.getTheme());
				if((rowIndex - 1) >= 0)
				{
					boolean isTransparent = EngineConstants.TRANSPARENTBORDER.equals(topBorder);
					for(int c = 0; c < colSpan; c++)
					{
						//Issue fixed in border WHile Reading Value
						String borderval = null;
						if(((rowIndex - 1) >= rangeAndReadborderDetails.getInt("startRow")) && dataObject.has(String.valueOf(rowIndex - 1)) && dataObject.getJSONObject(String.valueOf(rowIndex - 1)).has(String.valueOf(colIndex + c)))
						{
							JSONObjectWrapper nextCellObject = dataObject.getJSONObject(String.valueOf(rowIndex - 1)).getJSONObject(String.valueOf(colIndex + c));
							if(isFromScroll && nextCellObject.length() > 0 && nextCellObject.has(CellConstants.CELLSTYLE))
							{

								if(nextCellObject.getJSONObject(CellConstants.CELLSTYLE).has(CellConstants.BORDERBOTTOM))
								{
									borderval = nextCellObject.getJSONObject(CellConstants.CELLSTYLE).getString(CellConstants.BORDERBOTTOM);
								}

							}
							else if(!isFromScroll)
							{
								if(nextCellObject.has(CellConstants.BORDERBOTTOM))
								{
									borderval = nextCellObject.getString(CellConstants.BORDERBOTTOM);
								}
							}

						}
						if((rowIndex - 1) >= rangeAndReadborderDetails.getInt("startRow"))
						{

							if(isTransparent ? borderval == null : true)
							{

								JSONObjectWrapper newCellObject = new JSONObjectWrapper();
								if(!dataObject.has(String.valueOf(rowIndex - 1)))
								{
									JSONObjectWrapper newRowObject = new JSONObjectWrapper();
									dataObject.put(String.valueOf(rowIndex - 1), newRowObject);
								}
								JSONObjectWrapper styleObject = new JSONObjectWrapper();
								styleObject.put(CellConstants.BORDERBOTTOM, borderValue);

								if(!dataObject.getJSONObject(String.valueOf(rowIndex - 1)).has(String.valueOf(colIndex + c)))
								{
									if(isFromScroll)
									{
										newCellObject.put(CellConstants.CELLSTYLE, styleObject);
										dataObject.getJSONObject(String.valueOf(rowIndex - 1)).put(String.valueOf(colIndex + c), newCellObject);
									}
									else
									{
										dataObject.getJSONObject(String.valueOf(rowIndex - 1)).put(String.valueOf(colIndex + c), styleObject);
									}
//

								}
								else
								{
									JSONObjectWrapper requiredCellObject = dataObject.getJSONObject(String.valueOf(rowIndex - 1)).getJSONObject(String.valueOf(colIndex + c));
//

									if(isFromScroll)
									{
										if(requiredCellObject.has(CellConstants.CELLSTYLE))
										{

											requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).put(CellConstants.BORDERBOTTOM, borderValue);

										}
										else
										{
											requiredCellObject.put(CellConstants.CELLSTYLE, styleObject);

										}
									}
									else
									{
										requiredCellObject.put(CellConstants.BORDERBOTTOM, borderValue);
									}
								}

							}
						}

					}
				}
				else
				{
					if((formHtmlRange || (rangeAndReadborderDetails.has("readTopMostBorder") && rangeAndReadborderDetails.getBoolean("readTopMostBorder"))))
					{
						for(int c = 0; c < colSpan; c++)
						{

							JSONObjectWrapper newCellObject = new JSONObjectWrapper();
							if(!dataObject.has(String.valueOf(0)))
							{
								JSONObjectWrapper newRowObject = new JSONObjectWrapper();
								dataObject.put(String.valueOf(0), newRowObject);
							}
							JSONObjectWrapper styleObject = new JSONObjectWrapper();
							styleObject.put(CellConstants.BORDERTOP, borderValue);
							borderObject.put("CELLTOPBORDER", borderValue);

							if(!dataObject.getJSONObject(String.valueOf(0)).has(String.valueOf(colIndex + c)))
							{

								newCellObject.put(CellConstants.CELLSTYLE, styleObject);
								dataObject.getJSONObject(String.valueOf(0)).put(String.valueOf(colIndex + c), newCellObject);

							}
							else
							{
								JSONObjectWrapper requiredCellObject = dataObject.getJSONObject(String.valueOf(0)).getJSONObject(String.valueOf(colIndex + c));

								if(requiredCellObject.has(CellConstants.CELLSTYLE))
								{
									requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).put(CellConstants.BORDERTOP, borderValue);

								}
								else
								{

									requiredCellObject.put(CellConstants.CELLSTYLE, styleObject);

								}
							}

						}

					}
				}
			}
		}

		if(bottomBorder != null && !BorderProperties.BORDER_NONE.equals(bottomBorder))
		{
			borderValue = getBorderValue1(bottomBorder, workbook.getTheme());
			if(rowSpan > 1)
			{
				borderObject.put("CELLBOTTOMBORDER", borderValue);
				if(rowIndex - 1 + rowSpan <= rangeAndReadborderDetails.getInt("endRow"))
				{    //Added By Kirubha, For Issue While Reading Range.

					JSONObjectWrapper newCellObject = new JSONObjectWrapper();
					if(!dataObject.has(String.valueOf(rowIndex + rowSpan - 1)))
					{
						JSONObjectWrapper newRowObject = new JSONObjectWrapper();
						dataObject.put(String.valueOf(rowIndex + rowSpan - 1), newRowObject);
						//  sheetVar.dataObject.getJSONObject(String.valueOf(0)).put(String.valueOf(colIndex + c), newCellObject);
					}
					JSONObjectWrapper styleObject = new JSONObjectWrapper();
					styleObject.put(CellConstants.BORDERBOTTOM, borderValue);

					if(!dataObject.getJSONObject(String.valueOf(rowIndex + rowSpan - 1)).has(String.valueOf(colIndex)))
					{
						if(isFromScroll)
						{
							newCellObject.put(CellConstants.CELLSTYLE, styleObject);
							dataObject.getJSONObject(String.valueOf(rowIndex + rowSpan - 1)).put(String.valueOf(colIndex), newCellObject);
						}
						else
						{
							dataObject.getJSONObject(String.valueOf(rowIndex + rowSpan - 1)).put(String.valueOf(colIndex), styleObject);
						}

					}
					else
					{
						JSONObjectWrapper requiredCellObject = dataObject.getJSONObject(String.valueOf(rowIndex + rowSpan - 1)).getJSONObject(String.valueOf(colIndex));
						if(isFromScroll)
						{
							if(requiredCellObject.has(CellConstants.CELLSTYLE))
							{
								requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).put(CellConstants.BORDERBOTTOM, borderValue);

							}
							else
							{

								requiredCellObject.put(CellConstants.CELLSTYLE, styleObject);

							}
						}
						else
						{
							requiredCellObject.put(CellConstants.BORDERBOTTOM, borderValue);
						}
					}

				}
			}
			else
			{
				if(isFromScroll && cellStyleVal.has(CellConstants.BORDERBOTTOM))
				{
					borderObject.put("CELLBOTTOMBORDER", cellStyleVal.getString(CellConstants.BORDERBOTTOM));
				}
				else if(cellObject.has(CellConstants.BORDERBOTTOM))
				{
					borderObject.put("CELLBOTTOMBORDER", cellObject.getString(CellConstants.BORDERBOTTOM));
				}
				else
				{

					borderObject.put("CELLBOTTOMBORDER", borderValue);
				}
			}
		}
		if(leftBorder != null)
		{
			borderObject.put("CELLLEFTBORDER", defaultBorder);
			if(!BorderProperties.BORDER_NONE.equals(leftBorder))
			{
				borderValue = getBorderValue1(leftBorder, workbook.getTheme());
				if((colIndex - 1) >= 0)
				{

					boolean isTransparent = EngineConstants.TRANSPARENTBORDER.equals(leftBorder);
					for(int r = 0; r < rowSpan; r++)
					{
						if(rowIndex + r <= rangeAndReadborderDetails.getInt("endRow"))
						{ //Added By Kirubha, For Issue While Reading Range.
							String borderval = null;

							if(dataObject.has(String.valueOf(rowIndex + r)) && dataObject.getJSONObject(String.valueOf(rowIndex + r)).has(String.valueOf(colIndex - 1)))
							{

								JSONObjectWrapper requiredCellObject = dataObject.getJSONObject(String.valueOf(rowIndex + r)).getJSONObject(String.valueOf(colIndex - 1));
								if(isFromScroll)
								{
									if(requiredCellObject.has(CellConstants.CELLSTYLE) && requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).has(CellConstants.BORDERRIGHT))
									{
										borderval = requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).getString(CellConstants.BORDERRIGHT);
									}
								}
								else
								{
									if(requiredCellObject.has(CellConstants.BORDERRIGHT))
									{
										borderval = requiredCellObject.getString(CellConstants.BORDERRIGHT);
									}
								}

							}

							if(isTransparent ? borderval == null : true)
							{

								JSONObjectWrapper newCellObject = new JSONObjectWrapper();
								if(!dataObject.has(String.valueOf(rowIndex + r)))
								{
									JSONObjectWrapper newRowObject = new JSONObjectWrapper();
									dataObject.put(String.valueOf(rowIndex + r), newRowObject);
								}

								JSONObjectWrapper styleObject = new JSONObjectWrapper();
								styleObject.put(CellConstants.BORDERRIGHT, borderValue);

								if(!dataObject.getJSONObject(String.valueOf(rowIndex + r)).has(String.valueOf(colIndex - 1)))
								{
									if(isFromScroll)
									{
										newCellObject.put(CellConstants.CELLSTYLE, styleObject);

										dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(colIndex - 1), newCellObject);
									}
									else
									{
										dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(colIndex - 1), styleObject);

									}

								}
								else
								{
									JSONObjectWrapper requiredCellObject = dataObject.getJSONObject(String.valueOf(rowIndex + r)).getJSONObject(String.valueOf(colIndex - 1));

									if(isFromScroll)
									{
										if(requiredCellObject.has(CellConstants.CELLSTYLE))
										{
											requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).put(CellConstants.BORDERRIGHT, borderValue);
											dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(colIndex - 1), requiredCellObject);
										}
										else
										{

											requiredCellObject.put(CellConstants.CELLSTYLE, styleObject);
											dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(colIndex - 1), requiredCellObject);
										}
									}
									else
									{
										requiredCellObject.put(CellConstants.BORDERRIGHT, borderValue);
										dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(colIndex - 1), requiredCellObject);
									}

								}

							}
						}
					}
				}
				else
				{
					if((formHtmlRange || (rangeAndReadborderDetails.has("readLeftMostBorder") && rangeAndReadborderDetails.getBoolean("readLeftMostBorder"))))
					{
						for(int r = 0; r < rowSpan; r++)
						{
							if(rowIndex + r <= rangeAndReadborderDetails.getInt("endRow"))
							{ //Added By Kirubha, For Issue While Reading Range.

								JSONObjectWrapper newCellObject = new JSONObjectWrapper();
								if(!dataObject.has(String.valueOf(rowIndex + r)))
								{
									JSONObjectWrapper newRowObject = new JSONObjectWrapper();
									dataObject.put(String.valueOf(rowIndex + r), newRowObject);
								}
								JSONObjectWrapper styleObject = new JSONObjectWrapper();
								styleObject.put(CellConstants.BORDERLEFT, borderValue);
								borderObject.put("CELLLEFTBORDER", borderValue);

								if(!dataObject.getJSONObject(String.valueOf(rowIndex + r)).has(String.valueOf(0)))
								{
									newCellObject.put(CellConstants.CELLSTYLE, styleObject);
									dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(0), newCellObject);
								}
								else
								{
									JSONObjectWrapper requiredCellObject = dataObject.getJSONObject(String.valueOf(rowIndex + r)).getJSONObject(String.valueOf(0));

									if(requiredCellObject.has(CellConstants.CELLSTYLE))
									{
										requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).put(CellConstants.BORDERLEFT, borderValue);

									}
									else
									{

										requiredCellObject.put(CellConstants.CELLSTYLE, styleObject);

									}
								}

							}
						}
					}
				}
			}
		}
		if(rightBorder != null && !BorderProperties.BORDER_NONE.equals(rightBorder))
		{
			borderValue = getBorderValue1(rightBorder, workbook.getTheme());
			for(int r = 0; r < rowSpan; r++)
			{
				if(rowIndex + r <= rangeAndReadborderDetails.getInt("endRow"))
				{ //Will Be useFul while reading only range, instead of reading whole file.

					if(r == 0)
					{

						JSONObjectWrapper newCellObject = new JSONObjectWrapper();
						if(!dataObject.has(String.valueOf(rowIndex + r)))
						{
							JSONObjectWrapper newRowObject = new JSONObjectWrapper();
							dataObject.put(String.valueOf(rowIndex + r), newRowObject);
						}

						JSONObjectWrapper styleObject = new JSONObjectWrapper();
						styleObject.put(CellConstants.BORDERRIGHT, borderValue);
						borderObject.put("CELLRIGHTBORDER", borderValue);

						if(!dataObject.getJSONObject(String.valueOf(rowIndex + r)).has(String.valueOf(colIndex - 1 + colSpan)))
						{
							if(isFromScroll)
							{
								newCellObject.put(CellConstants.CELLSTYLE, styleObject);
								dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(colIndex - 1 + colSpan), newCellObject);
							}
							else
							{
								dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(colIndex - 1 + colSpan), styleObject);
							}

						}
						else
						{
							JSONObjectWrapper requiredCellObject = dataObject.getJSONObject(String.valueOf(rowIndex + r)).getJSONObject(String.valueOf(colIndex - 1 + colSpan));

							if(isFromScroll)
							{
								if(requiredCellObject.has(CellConstants.CELLSTYLE))
								{
									requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).put(CellConstants.BORDERRIGHT, borderValue);

								}
								else
								{

									requiredCellObject.put(CellConstants.CELLSTYLE, styleObject);

								}
							}
							else
							{
								requiredCellObject.put(CellConstants.BORDERRIGHT, borderValue);
							}

						}

					}
					else
					{
						for(int c = 0; c < colSpan; c++)
						{

							JSONObjectWrapper newCellObject = new JSONObjectWrapper();
							if(!dataObject.has(String.valueOf(rowIndex + r)))
							{
								JSONObjectWrapper newRowObject = new JSONObjectWrapper();
								dataObject.put(String.valueOf(rowIndex + r), newRowObject);
								//  sheetVar.dataObject.getJSONObject(String.valueOf(0)).put(String.valueOf(colIndex + c), newCellObject);
							}
							JSONObjectWrapper styleObject = new JSONObjectWrapper();
							styleObject.put(CellConstants.BORDERRIGHT, borderValue);

							if(!dataObject.getJSONObject(String.valueOf(rowIndex + r)).has(String.valueOf(colIndex + c)))
							{
								if(isFromScroll)
								{
									newCellObject.put(CellConstants.CELLSTYLE, styleObject);
									dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(colIndex + c), newCellObject);
								}
								else
								{
									dataObject.getJSONObject(String.valueOf(rowIndex + r)).put(String.valueOf(colIndex + c), styleObject);
								}

							}
							else
							{
								JSONObjectWrapper requiredCellObject = dataObject.getJSONObject(String.valueOf(rowIndex + r)).getJSONObject(String.valueOf(colIndex + c));

								if(isFromScroll)
								{
									if(requiredCellObject.has(CellConstants.CELLSTYLE))
									{
										requiredCellObject.getJSONObject(CellConstants.CELLSTYLE).put(CellConstants.BORDERRIGHT, borderValue);

									}
									else
									{

										requiredCellObject.put(CellConstants.CELLSTYLE, styleObject);

									}
								}
								else
								{
									requiredCellObject.put(CellConstants.BORDERRIGHT, borderValue);
								}

							}

						}
					}
				}
			}
		}
		//}
		return borderObject;
	}

	public static void handleBorderNew3(CellStyle cellStyle, SheetVariables sheetVar, int rowSpan, int colSpan, int rowIndex, int colIndex, boolean formHtmlRange, CellStyle defaultCellStyle, boolean isForScroll, JSONObjectWrapper rangeAndReadborderDetails, Workbook workbook)
	{
		if(cellStyle == null && defaultCellStyle == null)
		{
			return;
		}

		String defaultBorder = null;
		String borderValue = "";

		BorderProperties topBorder = null;
		BorderProperties bottomBorder = null;
		BorderProperties leftBorder = null;
		BorderProperties rightBorder = null;

		if(cellStyle != null)
		{
			topBorder = (BorderProperties) cellStyle.getProperty_Deep(CellStyle.Property.BORDERTOP, workbook);
			bottomBorder = (BorderProperties) cellStyle.getProperty_Deep(CellStyle.Property.BORDERBOTTOM, workbook);
			leftBorder = (BorderProperties) cellStyle.getProperty_Deep(CellStyle.Property.BORDERLEFT, workbook);
			rightBorder = (BorderProperties) cellStyle.getProperty_Deep(CellStyle.Property.BORDERRIGHT, workbook);
		}

		if(defaultCellStyle != null)
		{
			topBorder = topBorder == null ? (BorderProperties) defaultCellStyle.getProperty(CellStyle.Property.BORDERTOP) : topBorder;
			bottomBorder = bottomBorder == null ? (BorderProperties) defaultCellStyle.getProperty(CellStyle.Property.BORDERBOTTOM) : bottomBorder;
			leftBorder = leftBorder == null ? (BorderProperties) defaultCellStyle.getProperty(CellStyle.Property.BORDERLEFT) : leftBorder;
			rightBorder = rightBorder == null ? (BorderProperties) defaultCellStyle.getProperty(CellStyle.Property.BORDERRIGHT) : rightBorder;
		}

		if(cellStyle != null && cellStyle.getPropertyAsString_deep(CellStyle.Property.BACKGROUNDCOLOR, workbook) != null && !cellStyle.getPropertyAsString_deep(CellStyle.Property.BACKGROUNDCOLOR, workbook).equalsIgnoreCase("transparent"))
		{

			if(topBorder == null || topBorder.equals(BorderProperties.BORDER_NONE))
			{
				topBorder = BorderProperties.BORDER_TRANSPARENT;
			}

			if(bottomBorder == null || bottomBorder.equals(BorderProperties.BORDER_NONE))
			{
				bottomBorder = BorderProperties.BORDER_TRANSPARENT;
			}

			if(leftBorder == null || leftBorder.equals(BorderProperties.BORDER_NONE))
			{
				leftBorder = BorderProperties.BORDER_TRANSPARENT;
			}

			if(rightBorder == null || rightBorder.equals(BorderProperties.BORDER_NONE))
			{
				rightBorder = BorderProperties.BORDER_TRANSPARENT;
			}
		}


		if(topBorder != null)
		{

			sheetVar.cellTopBorders[rowIndex][colIndex] = defaultBorder;
			if(!BorderProperties.BORDER_NONE.equals(topBorder))
			{
				borderValue = getBorderValue1(topBorder, workbook.getTheme());
				if((rowIndex - 1) >= 0)
				{
					boolean isTransparent = EngineConstants.TRANSPARENTBORDER.equals(topBorder);
					for(int c = 0; c < colSpan; c++)
					{
						//Issue fixed in border WHile Reading Value
						String borderval = null;
						if((colIndex + c) < sheetVar.cols)
						{
							if((rowIndex - 1) >= rangeAndReadborderDetails.getInt("startRow"))
							{
								borderval = sheetVar.cellBottomBorders[rowIndex - 1][colIndex + c];
								//if(borderval == null)

								//{
								if(isTransparent ? borderval == null : true)
								{
									sheetVar.cellBottomBorders[rowIndex - 1][colIndex + c] = borderValue;
								}
							}

						}
						//}
					}
				}
				else
				{
					if((formHtmlRange || sheetVar.readTopMostBorder) && isForScroll)
					{
						for(int c = 0; c < colSpan; c++)
						{
							sheetVar.cellTopBorders[0][colIndex + c] = borderValue;

						}

					}
				}
			}
		}
		if(bottomBorder != null && !BorderProperties.BORDER_NONE.equals(bottomBorder))
		{
			borderValue = getBorderValue1(bottomBorder, workbook.getTheme());
			if(rowSpan > 1)
			{
				sheetVar.cellBottomBorders[rowIndex][colIndex] = borderValue;
				//logger.info((rowIndex+rowSpan-1) +"::"+ sheetVar.rows);
				if(rowIndex - 1 + rowSpan < rangeAndReadborderDetails.getInt("endRow"))
				{    //Added By Kirubha, For Issue While Reading Range.
					sheetVar.cellBottomBorders[rowIndex + rowSpan - 1][colIndex] = borderValue;
				}
			}
			else
			{
				sheetVar.cellBottomBorders[rowIndex][colIndex] = borderValue;
			}
		}
		if(leftBorder != null)
		{
			sheetVar.cellLeftBorders[rowIndex][colIndex] = defaultBorder;
			if(!BorderProperties.BORDER_NONE.equals(leftBorder))
			{
				borderValue = getBorderValue1(leftBorder, workbook.getTheme());
				if((colIndex - 1) >= 0)
				{
					boolean isTransparent = EngineConstants.TRANSPARENTBORDER.equals(leftBorder);
					for(int r = 0; r < rowSpan; r++)
					{
						if(rowIndex + r < rangeAndReadborderDetails.getInt("endRow"))
						{ //Added By Kirubha, For Issue While Reading Range.
							String borderval = sheetVar.cellRightBorders[rowIndex + r][colIndex - 1];
							if(isTransparent ? borderval == null : true)
							{
								sheetVar.cellRightBorders[rowIndex + r][colIndex - 1] = borderValue;
							}
						}
					}
				}
				else
				{
					if((formHtmlRange || sheetVar.readLeftMostBorder) && isForScroll)
					{
						for(int r = 0; r < rowSpan; r++)
						{
							if(rowIndex + r < sheetVar.rows)
							{ //Added By Kirubha, For Issue While Reading Range.
								sheetVar.cellLeftBorders[rowIndex + r][0] = borderValue;
							}
						}
					}
				}
			}
		}
		if(rightBorder != null && !BorderProperties.BORDER_NONE.equals(rightBorder))
		{
			borderValue = getBorderValue1(rightBorder, workbook.getTheme());

                    /*if(colSpan > 1)
                     {
                     sheetVar.cellRightBorders[rowIndex][colIndex+colSpan-1] = borderValue;
                     }
                     else
                     {
                     sheetVar.cellRightBorders[rowIndex][colIndex] = borderValue;
                     }*/
			for(int r = 0; r < rowSpan; r++)
			{
				if(rowIndex + r < rangeAndReadborderDetails.getInt("endRow"))
				{ //Will Be useFul while reading only range, instead of reading whole file.
//                                    if ((colIndex - 1) == 0 && formHtmlRange) {
//                                            //sheetVar.cellLeftBorders[rowIndex+r][0] = borderValue;
//                                    }

					if(r == 0)
					{
						sheetVar.cellRightBorders[rowIndex + r][colIndex - 1 + colSpan] = borderValue;
					}
					else
					{
						for(int c = 0; c < colSpan; c++)
						{
							sheetVar.cellRightBorders[rowIndex + r][colIndex + c] = borderValue;
						}
					}
				}
			}
		}
		//}

	}

	private String[] handleCellStyles(Workbook workBook, CellStyle cellStyle, String[] formatSplit, String patternColor, Type patternType, Type valueType)
	{
        /*
            0 : BGColor
            1 : TextColor
            2 : FontWeight
            3 : FontStyle
            4 : TextDecoration
            5 : FontFamily
            6 : FontSize
            7 : HAlign
            8 : Wrap
            9 : ContentType
            10: VAlign
            11: strikeThrough
            12: patternColor
            13: zsIndent
            14: rotationAngle
        */

		String[] formatResult = new String[15];
		String[] formatCurr;
		if(cellStyle == null)
		{
			cellStyle = workBook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME);
		}
		formatCurr = SheetJspUtil.getFormats(cellStyle, workBook, patternColor, patternType, valueType);
		for(int i = 0; i < formatCurr.length; i++)
		{
			String valCurr = formatCurr[i];
			switch(i)
			{
				case 0:
					valCurr = "transparent".equals(valCurr) ? null : valCurr; //No I18N
					break;
				case 1:
					valCurr = "transparent".equals(valCurr) ? null : valCurr; //No I18N
					break;
				case 10:
					valCurr = "automatic".equals(valCurr) ? null : valCurr; //No I18N
			}
			formatResult[i] = Utility.masknull(valCurr, formatSplit[i]);
		}

		return formatResult;
	}

	public static String getLink(Cell cell)
	{

		List links = cell.getLinks();
		if(links == null || links.isEmpty())
		{ // content has no links..
			return null;
		}

		String origCellText = cell.getContent();
		if(origCellText == null)
		{
			return null;
		}

		int contentLen = origCellText.length();
		int fromIndex = 0;
		int toIndex = 0;
		StringBuilder result = new StringBuilder();
		for(Object link1 : links)
		{
			RichStringProperties link = (RichStringProperties) link1;
			String label = link.getLabel();
//            String url = link.getUrl();

			Sheet sheet = cell.getRow().getSheet();
			String url = EngineUtils1.validateLink(link.getUrl(), cell.getRow().getSheet().getWorkbook());

			toIndex = origCellText.indexOf(label, fromIndex);
			if(toIndex < contentLen && toIndex >= fromIndex)
			{
				result.append(forHTMLTag(origCellText.substring(fromIndex, toIndex)));
				result.append(formHrefTag(label, url, sheet.getWorkbook()));
				fromIndex = toIndex + label.length();
			}
		}
		if(fromIndex < contentLen)
		{ // append the remaining part
			result.append(forHTMLTag(origCellText.substring(fromIndex)));
		}
		if(result.length() == 0)
		{
			return null;
		}
		else
		{
			return result.toString();
		}
	}


	private static StringBuffer formHrefTag(String label, String url, Workbook workbook)
	{

		//String label = link.getLabel();

		//String url = EngineUtils1.validateLink(link.getUrl(),null);//TOBE VALIDATED
		StringBuffer temp = new StringBuffer();
		if(url != null)
		{

			temp.append("<a").
					append(" idx='link' rel='noreferrer'"); // No I18N
			// Internal link handlink
			if(url.indexOf("#") == 0)
			{
				url = url.substring(1, url.length());
				String asn = url.split("\\.")[0];
				Sheet sheet = workbook.getSheetByAssociatedName(asn);
				if(sheet != null)
				{
					String shName = sheet.getName();
					url = url.replace(asn, shName);
				}
				String title = url;
				if(url.contains("'"))
				{
					url = url.replaceAll("'", "");
				}
				temp.append(" onclick=\"Goto.gotoCellInit(\'").append(IAMEncoder.encodeJavaScript(url)).append("\');return false;\"").          // No I18N
						append(" title='#").append(IAMEncoder.encodeHTMLAttribute(title)).append('\'');// No I18N
			}
			else
			{
				temp.append(" href='").append(IAMEncoder.encodeHTMLAttribute(url)).append('\'').
						append(" title='").append(IAMEncoder.encodeHTMLAttribute(url)).append('\'');
			}
			temp.append(EngineConstants.linkStyle).
					append(EngineConstants.linkTarget).
					append(" >");
			temp.append(IAMEncoder.encodeHTML(label));
			temp.append("</a>");
		}
		return temp;

	}

	public static String getBorderValue1(BorderProperties border, ZSTheme theme)
	{
		String borderValue = null;
		if(border != null && !border.equals(BorderProperties.BORDER_NONE))
		{
			String borderWidth = convertBorderWidth(border.getSize());
			if(borderWidth == null)
			{
				return borderValue;
			}
			else
			{
				borderWidth = borderWidth + "px";//No I18N
				String borderStyle = border.getType();

				if(borderStyle.equals("double") && (borderWidth.equals("2px") || borderWidth.equals("2.5px")))
				{
					borderWidth = "5px";//No I18N
				}
				else if(borderStyle.equals("double-thin"))
				{
					borderStyle = "double";                //No I18N
				}

				String borderColor = "transparent"; //No I18N
				if(border.getColor() != null)
				{
					borderColor = ZSColor.getHexColor(border.getColor(), theme);
				}

				borderValue = borderWidth + " " + borderStyle + " " + borderColor;
			}
		}
		return borderValue;
	}

	public static String getBorderValue(String border)
	{
		//String borderWidth = "0px";
		//String borderStyle = "solid";
		//String borderColor = "#FFF";
		String borderValue = null;//borderWidth + " " + borderStyle + " " + borderColor;
		if(border != null && !"none".equals(border))
		{

			String splitBorder[] = border.split(" ");

			String borderWidth = convertBorderWidth(getInchValue(splitBorder[0]));
			if(borderWidth == null)
			{
				return border;
			}
			else
			{
				borderWidth = borderWidth + "px";//No I18N
				String borderStyle = splitBorder[1];

				if(borderStyle.equals("double") && borderWidth.equals("2px"))
				{
					borderWidth = "2.5px";
				}
				else if(borderStyle.equals("double-thin"))
				{
					borderStyle = "double";                //No I18N
				}

				String borderColor = "#000000"; //No I18N
				if(splitBorder.length == 3)
				{
					borderColor = splitBorder[2];
				}
				borderValue = borderWidth + " " + borderStyle + " " + borderColor;
			}
		}
		return borderValue;
	}

	public static String convertBorderWidth(double inches)
	{
		String actual = null;
		try
		{
			//    	    String tempValue = value.substring(0, value.length() - 2);
			//	    double inches = Double.parseDouble(tempValue);
			//	    if(value.contains("cm"))
			//	    {
			//			inches = inches * 0.39;
			//	    }
//			double dpiValue = inches * 96;
//			actual = String.valueOf(dpiValue);
//
//			if(dpiValue > 3 && dpiValue < 3.5){
//				actual = "3.5";
//			}
			if(inches < 0.0250)
			{
				actual = "1";
			}
			if(inches >= 0.0250 && inches < 0.0340)
			{
				actual = "2";
			}
			if(inches >= 0.0340 && inches < 0.0405)
			{
				actual = "2.5";
			}
			if(inches >= 0.0405 && inches < 0.0470)
			{
				actual = "3";
			}
			if(inches >= 0.0470 && inches < 0.0545)
			{
				actual = "3.5";
			}
			if(inches >= 0.0545 && inches < 0.0612)
			{
				actual = "4";
			}
			if(inches >= 0.0612 && inches < 0.0685)
			{
				actual = "4.5";
			}
			if(inches >= 0.0685 && inches < 0.0755)
			{
				actual = "5";
			}
			if(inches >= 0.0755 && inches < 0.0825)
			{
				actual = "5.5";
			}
			if(inches >= 0.0825 && inches < 0.0895)
			{
				actual = "6";
			}
			if(inches >= 0.0895 && inches < 0.0965)
			{
				actual = "6.5";
			}
			if(inches >= 0.0965 && inches < 0.1030)
			{
				actual = "7";
			}
			if(inches >= 0.1030 && inches < 0.1100)
			{
				actual = "7.5";
			}
			if(inches >= 0.1100 && inches < 0.1170)
			{
				actual = "8";
			}
			if(inches >= 0.1170 && inches < 0.1238)
			{
				actual = "8.5";
			}
			if(inches >= 0.1238 && inches < 0.1310)
			{
				actual = "9";
			}
			if(inches >= 0.1310 && inches < 0.1390)
			{
				actual = "9.5";
			}
			if(inches >= 0.1390)
			{
				actual = "10";
			}
		}
		catch(Exception e)
		{
			actual = null;
		}
		return actual;
	}


	public static String getColorValueAsHex(String strColor)
	{
		String hexValue;
		try
		{
			String[] split = strColor.split(",");
			String h1 = Dhexadecimal(Integer.parseInt(split[0].trim()));
			String h2 = Dhexadecimal(Integer.parseInt(split[1].trim()));
			String h3 = Dhexadecimal(Integer.parseInt(split[2].trim()));
			hexValue = "#" + h1 + h2 + h3;
		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, null, e);
			hexValue = "";
		}
		return hexValue;
	}

	public static String Dhexadecimal(int w) throws Exception
	{
		int rem;
		String output = "";
		String digit;
		String backwards = "";
		do
		{
			rem = w % 16;
			digit = DtoHex(rem);
			w = w / 16;
			output += digit;
		}
		while(w / 16 != 0);
		rem = w % 16;
		digit = DtoHex(rem);
		output = output + digit;
		for(int i = output.length() - 1; i >= 0; i--)
		{
			backwards += output.charAt(i);
		}
		return backwards;
	}

	public static String DtoHex(int rem) throws Exception
	{
		String str1 = String.valueOf(rem);
		switch(str1)
		{
			case "10":
				str1 = "A";
				break;
			case "11":
				str1 = "B";
				break;
			case "12":
				str1 = "C";
				break;
			case "13":
				str1 = "D";
				break;
			case "14":
				str1 = "E";
				break;
			case "15":
				str1 = "F";
				break;
		}
		return str1;
	}

	public static String getComments(Cell cell)
	{
		String comment = null;
		Annotation annotation = cell.getAnnotation();
		if(annotation != null)
		{
			String text = annotation.getContent();
			if(text != null && !text.equals(""))
			{
				comment = text.trim();
			}
		}
		return comment;
	}

	public boolean createODSFile(Workbook workBook, long ownerZUID, String docOwner, long documentId, String documentName, HttpServletRequest request, String activesheet, boolean isRemoteFile) throws Exception
	{
		return createODSFile(workBook, ownerZUID, docOwner, documentId, documentName, request, activesheet, isRemoteFile, null);
	}

	public boolean createODSFile(Workbook workBook, long ownerZUID, String docOwner, long documentId, String documentName, HttpServletRequest request, String activesheet, boolean isRemoteFile,
	                             String rowHeight) throws Exception
	{
		/*logger.info(" -------createODSFile--------");
		logger.info("documentId: " + documentId);
		logger.info("documentName: " + documentName);
		logger.info("docOwner: " + docOwner);
		logger.info("activesheet: " + activesheet);
		logger.info("isRemoteFile " + isRemoteFile);
		logger.info("rowHeight: " + rowHeight);*/
		if(isRemoteFile)
		{
			return createODSFileForCollaboration(workBook, ownerZUID, docOwner, documentId, request, activesheet, isRemoteFile, rowHeight);
		}

		if(workBook.isPartialLoaded())
		{
			LOGGER.log(Level.INFO, "Engine: This document is partial loaded and can not be saved. Document Id : {0}", documentId);
			return false;
		}

		long s1 = System.currentTimeMillis();
		long hashcode = workBook.hashCode() + System.currentTimeMillis();
		try
		{// This Block Of Code is Added To Write Details In Conf File.
			String[] sheetNames = null;
			int usedRow = 0;
			int usedCol = 0;
			if(activesheet != null)
			{
				sheetNames = getSheetNamesAsArray(workBook);
				Sheet activeSheet = workBook.getSheet(activesheet);
				// because index start from 0.
				usedRow = activeSheet.getUsedRowIndex();
				usedCol = activeSheet.getUsedColumnIndex();
			}
			// Upto This

			boolean isFirstTimeWrite = false;
			// only for first time save if workBook object is created from ODS
			// file
			// as the associated sheet name will be regenrated and it will have
			// different sheet code
			// Also, there will be ods and fragments file co exist and hence
			// make sense to write the
			// entire file again in fragments
			if(workBook.isAllFragmentsModified())
			{
				isFirstTimeWrite = true;
			}
			else
			{
				long sheetId = getFragmentID(docOwner, documentId, EngineConstants.FILENAME_FONTFACES, ownerZUID, false);
				if(sheetId < 0)
				{
					isFirstTimeWrite = true;
				}
			}
			// write the order json file
			// so writing the workbook object first time
			List<JSONObjectWrapper> sheetFileList = new ArrayList<>();
			List sheetFileNameList = new ArrayList();

			int totalCellCount = 0;
			for(Sheet sheet : workBook.getSheetList())
			{
				int usedRowIndex = sheet.getUsedRowIndex() + 1;
				int usedColIndex = sheet.getUsedColumnIndex() + 1;
				if(usedRowIndex > Utility.MAXNUMOFROWS || usedColIndex > Utility.MAXNUMOFCOLS)
				{
					// ////////////////////////////////////
					LOGGER.log(Level.INFO, "Engine: Save. Rows or Columns size exceed. " + "Doc Id : "
							+ "{0} : Sheet : {1} : usedRow : {2} : usedCol : {3}", new Object[]{documentId, sheet.getName(), usedRowIndex, usedColIndex});//No I18N
					throw new Exception("Error.SizeExceed.Save"); // message key
				}

				totalCellCount += usedRowIndex * usedColIndex;

				JSONObjectWrapper jObj = new JSONObjectWrapper();
				jObj.put("fn", sheet.getAssociatedName()); // No I18N\
				jObj.put("name", sheet.getName()); // No I18N\
				sheetFileList.add(jObj);
				// ///////////////////////////////
				sheetFileNameList.add(sheet.getAssociatedName());
			}

			if(totalCellCount > Utility.CELLCOUNTLIMIT) // 1 million
			{
				// ////////////////////////////////////
				LOGGER.log(Level.INFO, "Engine: Save. Exceed One million cells : cell count :{0} : docId : {1}", new Object[]{totalCellCount, documentId});
				throw new Exception("Error.SizeExceed.Save"); // message key
			}
			// //////////////////

			WorkbookToXML workBookToXMLObj = new WorkbookToXML(workBook, true);
			List<Sheet> modifiedSheetList = new ArrayList<>();
//			long ownerZUID = getZUID(docOwner);
			boolean recalcRequired = false;

			boolean docContainsArrayFormula = false;

			// write all the sheets first
			int counter = 0;
			Store sheetStore = null;
			SheetFileInfo sheetfileInfo = null;
			for(Sheet sheet : workBook.getSheetList())
			{
				// save the entire workbook if sheetFileList.isEmpty() is true
				// as this is writing the workbook object first time
				// TODO : first time to take care to save full file
				if(isFirstTimeWrite || sheet.isModified() || sheet.getName().equals(activesheet))
				{
					String sheetAssociatedName = sheet.getAssociatedName(); // No I18N
					writeFragmentedFile(workBookToXMLObj, sheet, ownerZUID, docOwner, sheetAssociatedName, EngineConstants.FILEEXTN_ZIP, documentId);
					// TODO :
					modifiedSheetList.add(sheet);
				}
				if(!recalcRequired && sheet.hasDynamicFormulaCells())
				{
					recalcRequired = true;
				}
				if(!docContainsArrayFormula && sheet.hasDynamicFormulaCells())
				{
					docContainsArrayFormula = true;
				}
				counter++;
			}

			// write the namedranges.xml
			if(workBook.isNamedExpressionChanged() || isFirstTimeWrite)
			{
				writeFragmentedFile(workBookToXMLObj, null, ownerZUID, docOwner, EngineConstants.FILENAME_NAMEDRANGES, EngineConstants.FILEEXTN_ZIP, documentId);
			}
			////write the contentvalidationxml.
			if(workBook.isContentvalidationChanged() || workBookToXMLObj.isContentValidationChanged() || isFirstTimeWrite)
			{
				writeFragmentedFile(workBookToXMLObj, null, ownerZUID, docOwner, EngineConstants.FILENAME_CONTENTVALIDATIONS, EngineConstants.FILEEXTN_ZIP, documentId);
			}
			// write filters.xml
			// TODO : Once we support change of filters... the check here need to change
			//if (isFirstTimeWrite)
			{
				writeFragmentedFile(workBookToXMLObj, null, ownerZUID, docOwner, EngineConstants.FILENAME_FILTERS, EngineConstants.FILEEXTN_ZIP, documentId);
			}
			// write astyles.xml
			if(workBook.isStylesChanged() || workBookToXMLObj.isStylesChanged() || isFirstTimeWrite)
			{
				writeFragmentedFile(workBookToXMLObj, null, ownerZUID, docOwner, EngineConstants.FILENAME_ASTYLES, EngineConstants.FILEEXTN_ZIP, documentId);
			}
			// fontfaces.xml
			if(workBook.isFontFaceChanged() || isFirstTimeWrite)
			{
				writeFragmentedFile(workBookToXMLObj, null, ownerZUID, docOwner, EngineConstants.FILENAME_FONTFACES, EngineConstants.FILEEXTN_ZIP, documentId);
			}
			// write styles.xml
			// code to check if styles.zip exists
			long sheetId = getFragmentID(docOwner, documentId, EngineConstants.FILENAME_STYLES, ownerZUID, false);
			// Should write all conditionalStyles wvwn if isStylesChanged is false.
			//if(workBook.isDefaultStyle() || (workBookToXMLObj.isStylesChanged() && !workBookToXMLObj.getConditinalCellStyleNameSet().isEmpty()))
			if((!workBookToXMLObj.getConditionalCellStyleNameSet().isEmpty()))
			{
				//if ((workBook.isDefaultStyle() && sheetId < 0) || (workBookToXMLObj.isStylesChanged() && !workBookToXMLObj.getConditinalCellStyleNameSet().isEmpty())) {
				// If we have styles.zip written already should use that instead
				// of the one in template folder.
				// Otherwise the conditional formats already written in
				// styles.zip will be lost.
				BufferedReader br = null;
				sheetId = getFragmentID(docOwner, documentId, EngineConstants.FILENAME_STYLES, ownerZUID, true);
				sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, sheetId);
				sheetfileInfo = sheetStore.getFileInfo(sheetId, EngineConstants.FILENAME_STYLES, EngineConstants.FILEEXTN_ZIP);
				InputStream stream1 = null;
				ZipInputStream zin = null;
				try
				{
					if(sheetfileInfo == null)
					{
						sheetfileInfo = sheetStore.createFileInfo(sheetId, EngineConstants.FILENAME_STYLES, EngineConstants.FILEEXTN_ZIP);
					}
					else
					{
						stream1 = sheetStore.read(sheetId, EngineConstants.FILENAME_STYLES, EngineConstants.FILEEXTN_ZIP);// No I18N
						zin = new ZipInputStream(stream1);
						zin.getNextEntry();
						br = new BufferedReader(new InputStreamReader(zin));
					}
					HashMap<String, Object> writeInfo = sheetStore.write(sheetId, sheetfileInfo);
					OutputStream os = (OutputStream) writeInfo.get("OS");
					ZipOutputStream tempZOS = new ZipOutputStream(os);
					writeStylesXML(tempZOS, workBookToXMLObj, workBook, br);
					tempZOS.close();
					sheetStore.finishWrite(writeInfo);
				}
				finally
				{
					if(zin != null)
					{
						zin.close();
					}
					if(stream1 != null)
					{
						stream1.close();
					}
				}

			}
			String dirPath = null;
			//FileStore fstore = null;
			// Code to write Macros
			if(workBook.containsMacro() && (workBook.isMacrosChanged() || isFirstTimeWrite))
			{
				sheetId = getFragmentID(docOwner, documentId, EngineConstants.FILENAME_MACROS, ownerZUID, true);
				sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, sheetId);
				sheetfileInfo = sheetStore.getFileInfo(sheetId, EngineConstants.FILENAME_MACROS, EngineConstants.FILEEXTN_ZIP);
				if(sheetfileInfo == null)
				{
					sheetfileInfo = sheetStore.createFileInfo(sheetId, EngineConstants.FILENAME_MACROS, EngineConstants.FILEEXTN_ZIP);
				}
				HashMap<String, Object> writeInfo = sheetStore.write(sheetId, sheetfileInfo);
				OutputStream os = (OutputStream) writeInfo.get("OS");// I18N
				ZipOutputStream zos = new ZipOutputStream(os);
				MacroWriter.writeMacros(zos, workBook);
				zos.close();
				sheetStore.finishWrite(writeInfo);
				//} else if (!workBook.containsMacro() && workBook.isMacrosChanged() && fstore.exists(dirPath + "/" + "macros.zip")) { // No I18N
			}
			else if(!workBook.containsMacro() && workBook.isMacrosChanged())
			{
				// assuming macros has been deleted, so delete the file
				sheetId = getFragmentID(docOwner, documentId, EngineConstants.FILENAME_MACROS, ownerZUID, false);
				sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, sheetId);
				sheetfileInfo = sheetStore.getFileInfo(sheetId, EngineConstants.FILENAME_MACROS, EngineConstants.FILEEXTN_ZIP);
				if(sheetfileInfo != null)
				{
					sheetStore.delete(sheetId, EngineConstants.FILEEXTN_ZIP);
				}
			}

			// Code to write settings.xml
			if(isFirstTimeWrite || workBook.getWorkbookSettings().isChanged())
			{
				sheetId = getFragmentID(docOwner, documentId, EngineConstants.FILENAME_SETTINGS, ownerZUID, true);
				sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, sheetId);
				sheetfileInfo = sheetStore.getFileInfo(sheetId, EngineConstants.FILENAME_SETTINGS, EngineConstants.FILEEXTN_ZIP);
				if(sheetfileInfo == null)
				{
					sheetfileInfo = sheetStore.createFileInfo(sheetId, EngineConstants.FILENAME_SETTINGS, EngineConstants.FILEEXTN_ZIP);
				}
				HashMap<String, Object> writeInfo = sheetStore.write(sheetId, sheetfileInfo);
				OutputStream os = (OutputStream) writeInfo.get("OS");// I18N
				ZipOutputStream zos = new ZipOutputStream(os);
				SettingsWriter.writeSettings(zos, workBook.getWorkbookSettings());
				zos.close();
				sheetStore.finishWrite(writeInfo);
			}

			// prolog.xml
			// no need to write, available in template folder

			// ////////////////////////////////////////////////////
			// write to original file and set the variables accordingly
			// synchronized this part on workBook object
			synchronized (workBook)
			{
				// do the clean up here
				// Files, those are not in sheetFileNameList, are deleted here.
				DataObject dataObj = getFragmentDataObj(docOwner, new Long(documentId));
				Iterator itr = dataObj.getRows("FragmentSheets");
				while(itr.hasNext())
				{
					com.adventnet.persistence.Row row = (com.adventnet.persistence.Row) itr.next();
					String fName = (String) row.get("SHEET_NAME");
					if(fName.endsWith("#"))
					{
						String fName1 = fName.substring(fName.lastIndexOf("/") + 1, fName.indexOf("#") + 1);
						if(!sheetFileNameList.contains(fName1))
						{
							Long delSheetId = (Long) row.get("FRAGMENTSHEET_ID");

							Store store = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, new Long(documentId), delSheetId);
							store.delete(delSheetId, EngineConstants.FILEEXTN_ZIP);
							// deleteList(fName);
							// store.delete(fName);
							workBook.setIsSheetOrderChanged(true);
						}
					}
				}

				// ///////////////////////////////
				if(isFirstTimeWrite || workBook.isSheetOrderChanged())
				{
					sheetId = getFragmentID(docOwner, documentId, EngineConstants.FILENAME_ORDER, ownerZUID, true);
					try
					{
						sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, sheetId);
						sheetfileInfo = sheetStore.getFileInfo(sheetId, EngineConstants.FILENAME_ORDER, EngineConstants.FILEEXTN_JSON);
						if(sheetfileInfo == null)
						{
							sheetfileInfo = sheetStore.createFileInfo(sheetId, EngineConstants.FILENAME_ORDER, EngineConstants.FILEEXTN_JSON);
						}
						writeSheetOrderFile(sheetFileList, sheetStore, sheetfileInfo, sheetId); // No
						// I18N
					}
					catch(Exception e)
					{
						LOGGER.log(Level.WARNING, "Error Occured while writing order.json: {0}", documentId);
					}

				}
				// ///////////////////////////////
				// ///////////////////////////////////////////////////////////
				// if(activesheet != null) // && activesheet != "null")
				// {
				// Save It After Saving The Document(Write Only If App server Is
				// Not Killed).
				// EngineUtils.writeSheetMetaInfo(documentId , activesheet, new
				// Long(docOwnerId), sheetNames, usedRow, usedCol, rowHeight,
				// recalcRequired, docContainsArrayFormula);
				writeSheetMetaInfo(ownerZUID, docOwner, documentId, activesheet, sheetNames, usedRow, usedCol, rowHeight, recalcRequired, docContainsArrayFormula);
				// }
				// reset sheets
				for(Sheet sheet : modifiedSheetList)
				{
					sheet.setIsModified(false);
				}
				workBook.setIsNamedExpressionChanged(false);
				workBook.setIsFontFaceChanged(false);
				workBook.setIsSheetOrderChanged(false);
				workBook.setIsStylesChanged(false);
				workBook.setIsContentValidationChanged(false);
				// /////
				workBook.setIsMacrosChanged(false);
				workBook.getWorkbookSettings().setIsChanged(false);
				// /////////////////
				// setting it to false as we have written this as fragments
				workBook.setIsAllFragmentsModified(false);
			}

			// //////////////////////
			// if(isRemoteFile)
			// {
			// String destFileName = dirPath +
			// EngineConstants.ENGINE_ODS_FILE_FORMAT;
			// writeFragmentsToODSFile(store, dirPath, destFileName, documentId,
			// true);
			// }
			// else
			// {
			// delete the ods file if it exists

			Store store = StoreFactory.getInstance().getDocumentStore(ownerZUID, docOwner, documentId);
			if(store.exists(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ZSHEET))
			{
				store.delete(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ZSHEET);
			}
			else if(store.exists(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ODS))
			{
				store.delete(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ODS);// No I18N
			}

			// also delete the sheetid.json (equivalent of order.json)
//			logger.info("need to delete sheetid.json, if that file is exist in store while while writting fragementfile");
			/*
			 * if(store.exists(dirPath+"/"+"sheetid.json")) {
			 * store.delete(dirPath+"/"+"sheetid.json"); //No I18N }
			 */
			// }

		}
		catch(Exception ex)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while writing the file.........." + documentId, ex);
			LOGGER.log(Level.WARNING, "Engine: Exception message..........{0}", ex.getMessage());
			// send email
			if(ex.getMessage() != null && ex.getMessage().contains("Error.SizeExceed.Save"))
			{
				throw ex;
			}
			else
			{
				MailUtil.reportErrorToAdmins(String.valueOf(documentId), "Error while saving the file", ex); // No I18N
				throw new Exception("Problem while saving document", ex);
			}
		}
		long s2 = System.currentTimeMillis();
		LOGGER.log(Level.INFO, "Engine: Total Time Taken to Write ===>{0}ms.....{1}", new Object[]{s2 - s1, documentId});
		return true;
	}

	/**
	 * ~DFS Implementation
	 *
	 * @param workBook
	 * @param documentId
	 * @param docOwner
	 * @param request
	 * @param activesheet
	 * @param isRemoteFile
	 * @param rowHeight
	 * @return
	 * @throws Exception
	 *             Also used for Remote mode save.
	 */

	//NEW CONCEPT

	/**
	 * ~DFS Implementation
	 *
	 * @param workBook
	 * @param ownerZUID
	 * @param documentId
	 * @param docOwner
	 * @param request
	 * @param activesheet
	 * @param isRemoteFile
	 * @param rowHeight
	 * @return
	 * @throws Exception Also used for Remote mode save.
	 */
	public boolean createODSFileForCollaboration(Workbook workBook, long ownerZUID, String docOwner, long documentId, HttpServletRequest request, String activesheet,
	                                             boolean isRemoteFile, String rowHeight) throws Exception
	{
		long s1 = System.currentTimeMillis();
		/*logger.info("@@@@@@@ newCreateODSFileForCollaboration");
		logger.info("documentId: " + documentId);
		logger.info("docOwner: " + docOwner);
		logger.info("activesheet: " + activesheet);
		logger.info("isRemoteFile: " + isRemoteFile);
		logger.info("documentId: " + rowHeight);*/

		if(workBook.isPartialLoaded())
		{
			LOGGER.log(Level.INFO, "Engine: This document is partial loaded and can not be saved. Document Id : {0}", documentId);
			return false;
		}
		long hashCode = workBook.hashCode(); // hopefully createodsunique
		String tempfragDirPath = Configuration.getString("app.home") + File.separator + "bin" + File.separator + "tempfgmt" + File.separator + getToday() + File.separator + ownerZUID + File.separator + documentId + File.separator + hashCode + File.separator;//NO I18N
		File dir = new File(tempfragDirPath);
		if(!dir.exists())
		{
			dir.mkdirs();
		}

		boolean isReportError = true;
		DataObject dataObj = null;
		try
		{
			// This Block Of Code is Added To Write Details In Conf File.
			String[] sheetNames = null;
			int usedRow = 0;
			int usedCol = 0;
			if(activesheet != null)
			{
				// sheetNames = getSheetNames(workBook);
				sheetNames = getSheetNamesAsArray(workBook);
				Sheet activeSheet = workBook.getSheet(activesheet);
				// because index start from 0.
				usedRow = activeSheet.getUsedRowIndex();
				usedCol = activeSheet.getUsedColumnIndex();
			}
			// Upto This
			List<JSONObjectWrapper> sheetFileList = new ArrayList<>();
			List sheetFileNameList = new ArrayList();

			int totalCellCount = 0;
			for(Sheet sheet : workBook.getSheetList())
			{
				int usedRowIndex = sheet.getUsedRowIndex() + 1;
				int usedColIndex = sheet.getUsedColumnIndex() + 1;
				if(usedRowIndex > Utility.MAXNUMOFROWS || usedColIndex > Utility.MAXNUMOFCOLS)
				{
					// ////////////////////////////////////
					LOGGER.log(Level.INFO, "Engine: Save. Rows or Columns size exceed. " + "Doc Id : "
							+ "{0} : Sheet : {1} : usedRow : {2} : usedCol : {3}", new Object[]{documentId, sheet.getName(), usedRowIndex, usedColIndex});//No I18N
					throw new Exception("Error.SizeExceed.Save"); // message key
				}

				totalCellCount += usedRowIndex * usedColIndex;

				JSONObjectWrapper jObj = new JSONObjectWrapper();
				jObj.put("fn", sheet.getAssociatedName()); // No I18N\
				jObj.put("name", sheet.getName()); // No I18N\
				sheetFileList.add(jObj);
				// ///////////////////////////////
				sheetFileNameList.add(sheet.getAssociatedName());
			}

			if(totalCellCount > Utility.CELLCOUNTLIMIT) // 1 million
			{
				// ////////////////////////////////////
				LOGGER.log(Level.INFO, "Engine: Save. Exceed One million cells : cell count :{0} : docId : {1}", new Object[]{totalCellCount, documentId});
				throw new Exception("Error.SizeExceed.Save"); // message key
			}
			//////////////////////

			String timeStamp = hashCode + "-" + String.valueOf(System.currentTimeMillis()); // No I18N
			String cacheDirPath = tempfragDirPath + File.separator + timeStamp;
			File cacheDir = new File(cacheDirPath); // to be deleted this dir
			if(!cacheDir.exists())
			{
				cacheDir.mkdirs();
			}
			// //////////////////
			WorkbookToXML workBookToXMLObj = new WorkbookToXML(workBook, true);
			String tempFileName = null;
			String fileName = null;
			boolean recalcRequired = false;
			boolean docContainsArrayFormula = false;
			// write all the sheets first
			int counter = 0;
			for(Sheet sheet : workBook.getSheetList())
			{
				// save the entire workbook if sheetFileList.isEmpty() is true
				// as this is writing the workbook object first time
				// TODO : first time to take care to save full file
				//if(isFirstTimeWrite || sheet.isModified() || sheet.getName().equals(activesheet))
				{
					String sheetFileName = sheet.getAssociatedName(); //No I18N
					tempFileName = cacheDirPath + File.separator + hashCode + "-" + sheetFileName + ".zip"; //No I18N
					//orgFileName = dirPath+ "/" +sheetFileName+".zip"; //No I18N
					fileName = sheetFileName + ".xml"; //No I18N
					File tempFile = new File(tempFileName);
					String canonicalPath = tempFile.getCanonicalPath();
					if(canonicalPath.startsWith(cacheDirPath))
					{
						writeFragmentedFile(workBookToXMLObj, sheet, tempFile, fileName, documentId);
					}
					// TODO :
					// modifiedSheetList.add(sheet);
					// fileNameHT.put(fileName, tempFileName);
				}
				if(!recalcRequired && !sheet.getRecalculateFormulaCells().isEmpty())
				{
					recalcRequired = true;
				}
				if(!docContainsArrayFormula && !sheet.getArrayFormulaCells().isEmpty())
				{
					docContainsArrayFormula = true;
				}

				counter++;
			}
			ZipOutputStream odszos = null;
			ZSZipInputStream zis = null;
			InputStream is = null;
			boolean isErrorWhileWriting = false;
			File templateDir = new File(EngineConstants.ENGINETEMPLATEDIR);
			//
			Store store = null;
			HashMap<String, Object> writeInfo = null;
			try
			{
				String fileType = EngineConstants.FILENAME_DOCUMENT;
				if(isRemoteFile)
				{
					fileType = EngineConstants.FILENAME_REMOTEDOC;
				}

				store = StoreFactory.getInstance().getDocumentStore(ownerZUID, docOwner, documentId);

				if(store.exists(documentId, fileType, EngineConstants.FILEEXTN_ZSHEET))
				{
					store.delete(documentId, fileType, EngineConstants.FILEEXTN_ZSHEET);
				}
				SheetFileInfo sheetFileInfo = store.getFileInfo(documentId, fileType, EngineConstants.FILEEXTN_ODS);
				if(sheetFileInfo == null)
				{
					sheetFileInfo = store.createFileInfo(documentId, fileType, EngineConstants.FILEEXTN_ODS);
				}
				dataObj = getFragmentDataObj(docOwner, documentId);
				writeInfo = store.write(documentId, sheetFileInfo);

				OutputStream os = (OutputStream) writeInfo.get("OS");// NO I18N

				int c = 0;
				int bufferLength = 10240;
				byte[] tmpBytes = new byte[bufferLength];

//				    os = store.write(destFileName);
//				    zos = new ZipOutputStream(os);
				odszos = new ZipOutputStream(os);
				odszos.putNextEntry(new ZipEntry("content.xml")); //No I18N

				// now read prolog.xml from template
				is = new FileInputStream(EngineConstants.PROLOGFILEPATH);
				while((c = is.read(tmpBytes, 0, bufferLength)) != -1)
				{
					odszos.write(tmpBytes, 0, c);
				}
				is.close();
				///////////
				//FONTFACES
				workBookToXMLObj.writeFontFaceDeclsXML(odszos);
				//ASTYLES----
				workBookToXMLObj.writeAutomaticStyleXML(odszos);
				//////////// Sheets
				odszos.write("<office:body>".getBytes()); //No I18N
				odszos.write("<office:spreadsheet>".getBytes()); //No I18N
				workBookToXMLObj.writeContentValidationsXml(odszos);
				if(sheetFileList == null)
				{
					long orderId = EngineUtils.getFragmentId(dataObj, EngineConstants.FILENAME_ORDER);
					store = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, orderId);
					sheetFileList = EngineUtils.getInstance().readSheetOrderFile(store, orderId, EngineConstants.FILENAME_ORDER).getOrderJSON();
				}

				for(JSONObjectWrapper jsonObj : sheetFileList)
				{
					String sheetFileName = jsonObj.getString("fn");
					fileName = cacheDirPath + "/" + hashCode + "-" + sheetFileName + ".zip"; //No I18N
					File tempFile = new File(fileName);
					String canonicalPath = tempFile.getCanonicalPath();
					if(canonicalPath.startsWith(cacheDirPath))
					{
						is = new FileInputStream(tempFile);
						zis = new ZSZipInputStream(is);
						zis.getNextEntry();
						while ((c = zis.read(tmpBytes, 0, bufferLength)) != -1) {
							odszos.write(tmpBytes, 0, c);
						}
						zis.close();
						is.close();
					}
				}

				//NAMEDRANGES
				workBookToXMLObj.writeNamedExpressionsXML(odszos);
				//FILTERS
				workBookToXMLObj.writeFilterRangesXML(odszos);
				odszos.write("</office:spreadsheet>".getBytes()); //No I18N
				odszos.write("</office:body>".getBytes()); //No I18N
				odszos.write("</office:document-content>".getBytes()); //No I18N


				// write styles.xml // Should write all conditionalStyles wvwn if isStylesChanged is false.
				boolean isStylesXMLWriiten = false;
				if((!workBookToXMLObj.getConditionalCellStyleNameSet().isEmpty()))
				{
					writeStylesXML(odszos, workBookToXMLObj, workBook, null);
					isStylesXMLWriiten = true;
				}
				// for writing macros
				if(workBook.containsMacro())
				{
					MacroWriter.writeMacros(odszos, workBook);
				}
				// for writing settings
//					boolean isSettingsWritten = false;

				// write the settings.xml
				SettingsWriter.writeSettings(odszos, workBook.getWorkbookSettings());
				boolean isSettingsWritten = true;

				//adds files from template folder to the output.ods zip file
				for(File file : templateDir.listFiles())
				{
					if(file.isFile())
					{
						fileName = file.getName();
						if(fileName.equals("styles.xml"))
						{
							if(isStylesXMLWriiten)
							{
								continue;
							}
							//////////////
							if(EngineConstants.ISMKI || EngineConstants.ISNTT)
							{
								file = new File(EngineConstants.ENGINEDIR + File.separator + "ja" + File.separator + "styles.xml"); //No I18N
							}
							else if(EngineConstants.ISBAIHUI)
							{
								file = new File(EngineConstants.ENGINEDIR + File.separator + "zh" + File.separator + "styles.xml"); //No I18N
							}
							else if(EngineConstants.ISFUJIXEROX)
							{
								file = new File(EngineConstants.ENGINEDIR + File.separator + "fuji" + File.separator + "styles.xml"); //No I18N
							}
						}
						else if(isSettingsWritten && fileName.equals("settings.xml"))
						{
							continue;
						}

						WorkbookToXML.writeFile("", odszos, file); // closeEntry() done in this fn
					}
					else if(file.isDirectory())
					{
						String path = file.getName();
						WorkbookToXML.exploreFolder(path, odszos, file);
					}
				}

				odszos.close();


			}
			catch(Exception ex)
			{
				LOGGER.log(Level.WARNING, "Engine: Error while writing the fragments to ODS file.........." + documentId, ex);
				//send email
				MailUtil.reportErrorToAdmins(String.valueOf(documentId), "Error while writing fragments to ODS", ex); //No I18N
				isErrorWhileWriting = true;
				throw new Exception("Problem while saving document");
			}
			finally
			{
				try
				{
					if(is != null)
					{
						is.close();
					}
					if(zis != null)
					{
						zis.close();
					}
					if(odszos != null)
					{
						odszos.close();
					}
					store.finishWrite(writeInfo);
//					if(os != null)
//					{
//					    os.close();
//					}
				}
				catch(Exception e)
				{
					LOGGER.log(Level.WARNING, "Engine: Error while closing streams (writeFragmentsToODSFile).........." + documentId, e);//No I18N
				}

				if(isErrorWhileWriting)
				{

					LOGGER.info("Engine: Deleting corrupted ods (writing fragments to ods) file....... ");//No I18N
//					store.delete(destFileName);
				}
				boolean cache = cacheDir.delete();
				LOGGER.log(Level.INFO, "cache file deleted: {0}", cache);

			}

			if(activesheet != null)// && activesheet != "null")
			{
				// Save It After Saving The Document(Write Only If App server Is
				// Not Killed).
				EngineUtils.writeSheetMetaInfo(ownerZUID, docOwner, documentId, activesheet, sheetNames, usedRow, usedCol, rowHeight, recalcRequired,
						docContainsArrayFormula);
			}


		}
		catch(Exception ex)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while writing the Collaboration file.........." + documentId, ex);
			// send email
			if(ex.getMessage() != null && ex.getMessage().contains("Error.SizeExceed.Save"))
			{
				throw ex;
			}
			else
			{
				if(isReportError)
				{
					MailUtil.reportErrorToAdmins(String.valueOf(documentId), "Error while saving the file of Collaboration/Remote:" + isRemoteFile, ex); // No I18N
				}
				throw new Exception("Problem while saving document", ex);
			}
		}
		finally
		{
			// mark the boolean as next time we need to write the fragements fully again
			if(!isRemoteFile)
			{
				Store store = StoreFactory.getInstance().getDocumentStore(ownerZUID, docOwner, documentId);
				if(store.exists(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ODS) || store.exists(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ZSHEET))
				{
					workBook.setIsAllFragmentsModified(true);
					//--Delete all the fragment files, if exist.
					if(!dataObj.isEmpty())
					{
						Long[] sheetList = getAllFragmentIDS(dataObj);
						store = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, sheetList);
						store.delete(sheetList, null, EngineConstants.FILEEXTN_ZIP);
					}
				}
			}
			if(dir.exists())
			{
				//logger.info("tobeDelDir: "+(tempfragDirPath + File.separator + hashCode));
				deleteDir(dir);
			}
		}
		long s2 = System.currentTimeMillis();
		LOGGER.log(Level.INFO, "Engine: Total Time Taken to Write in Collaboration/Remote ===>{0}ms.....{1}", new Object[]{s2 - s1, documentId});
		return true;
	}

	public static boolean deleteDir(File dir)
	{
		if(dir.isDirectory())
		{
			String[] children = dir.list();
			for(int i = 0; i < children.length; i++)
			{
				boolean success = deleteDir(new File(dir, children[i]));
				if(!success)
				{
					return false;
				}
			}
		}
		return dir.delete();
	}

	private void writeStylesXML(ZipOutputStream zos, WorkbookToXML workBookToXMLObj, Workbook workBook, BufferedReader br) throws Exception
	{
		CellStyle defCellStyle = workBook.getCellStyle("Default");
		String defCellStyleTag = defCellStyle == null ? "" : workBookToXMLObj.getCellStyleTag(defCellStyle, false);

		if(br == null)
		{
			File styleFile = null;
			if(EngineConstants.ISMKI || EngineConstants.ISNTT)
			{
				styleFile = new File(EngineConstants.ENGINEDIR + File.separator + "ja" + File.separator + "styles.xml");
			}
			else if(EngineConstants.ISBAIHUI)
			{
				styleFile = new File(EngineConstants.ENGINEDIR + File.separator + "zh" + File.separator + "styles.xml");
			}
			else if(EngineConstants.ISFUJIXEROX)
			{
				styleFile = new File(EngineConstants.ENGINEDIR + File.separator + "fuji" + File.separator + "styles.xml");
			}
			else
			{
				styleFile = new File(EngineConstants.ENGINEDIR + File.separator + "template" + File.separator + "styles.xml");
			}
			br = new BufferedReader(new InputStreamReader(new FileInputStream(styleFile)));
		}

//	ZipOutputStream zos = null;
		try
		{
			//BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(styleFile)));
			StringBuilder stringToWrite = new StringBuilder();
			String find1 = "<style:style style:name=\"Default\" style:family=\"table-cell\"/>";
			String find2 = "<office:font-face-decls>";
			String find3 = "</office:font-face-decls>";
			String str = null;
			int sIndex, eIndex = 0;
			while((str = br.readLine()) != null)
			{
				/////////
				if((sIndex = str.indexOf(find1)) != -1)
				{
					String tempStr = str.substring(0, sIndex);
					tempStr += defCellStyleTag;

					tempStr += str.substring(sIndex + find1.length());
					str = tempStr;
				}

				// At the end of offive:styles, add all new conditional cellStyles.
				if((sIndex = str.indexOf("</office:styles>")) != -1)
				{
					String conditionalCellStyleTag = "";
					for(String styleName : workBookToXMLObj.getConditionalCellStyleNameSet())
					{
						CellStyle cStyle = workBook.getCellStyle(styleName);
						if(cStyle != null && !str.contains(cStyle.getStyleName()))
						{
							conditionalCellStyleTag += workBookToXMLObj.getCellStyleTag(cStyle, false);
						}
					}

					if(conditionalCellStyleTag.length() > 0)
					{
						String tempStr = str.substring(0, sIndex);
						tempStr += conditionalCellStyleTag; // Conditional styles should be added tp styles.xml otherwise export removes those styles from content.xml.
						tempStr += str.substring(sIndex);
						str = tempStr;
					}
				}

				/////////
				if((sIndex = str.indexOf(find2)) != -1
						&& (eIndex = str.indexOf(find3)) != -1)
				{
					// fetch the font-face xml and append it here
					String newFontFaceXML = WorkbookToXML.getFontFaceXML(workBook);
					String tempStr = str.substring(0, sIndex + find2.length());
					tempStr += newFontFaceXML;
					tempStr += str.substring(eIndex);
					str = tempStr;
				}
				// append the string
				stringToWrite.append(str);
			}

			// expecting the content here will be in limit less tahn 10 kb
			// hence writing directly rather in chunk
//	    if(store != null)
//	    {
//		zos = new ZipOutputStream(store.write(filePath));
//	    }else
//	    {
//		zos = new ZipOutputStream(new FileOutputStream(filePath));
//	    }
			// TODO: need to optimize this code
			zos.putNextEntry(new ZipEntry("styles.xml"));
			byte[] b = stringToWrite.toString().getBytes();
			zos.write(b, 0, b.length);

//	    zos.close();
		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while writing to styles xml file.", e);
			throw new Exception("Problem while saving document");
		}
//	finally
//	{
//	    try
//	    {
//		if(zos != null)
//		{
//		    zos.close();
//		}
//
//	    }
//	    catch(Exception e)
//	    {
//		logger.info("Engine: Error while closing streams of styles xml file.");
//		e.printStackTrace();
//	    }
//
//	}

	}

	/**
	 * DFS Implementation
	 *
	 * @param workBookToXMLObj
	 * @param ownerZUID
	 * @param docOwner
	 * @param fileName
	 * @param fileExtn
	 * @param documentId
	 * @throws Exception Added in DFS Implementation
	 */
	private void writeFragmentedFile(WorkbookToXML workBookToXMLObj, Sheet sheet, long ownerZUID, String docOwner, String fileName, String fileExtn, long documentId) throws Exception
	{
		// TODO :
		try
		{
			long sheetId = getFragmentID(docOwner, documentId, fileName, ownerZUID, true);
			Store sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, sheetId);
			SheetFileInfo sheetfileInfo = sheetStore.getFileInfo(sheetId, fileName, fileExtn);
			if(sheetfileInfo == null)
			{
				sheetfileInfo = sheetStore.createFileInfo(sheetId, fileName, fileExtn);
			}
			writeFragmentedFile(workBookToXMLObj, sheet, sheetStore, fileName, sheetId, sheetfileInfo);
		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "Error Occured while writing: {0} documentId: {1}", new Object[]{fileName, documentId});
			throw new Exception("Problem while saving document");
		}
	}

	private void writeFragmentedFile(WorkbookToXML workBookToXMLObj, Sheet sheet, Store store, String fileName, long resourceid, SheetFileInfo sheetfileInfo) throws Exception
	{
		//logger.info("@@@@@ writeFragmentedFile fileName::"+fileName);
		HashMap<String, Object> writeInfo = store.write(resourceid, sheetfileInfo);
		OutputStream os = (OutputStream) writeInfo.get("OS");
		ZipOutputStream zos = new ZipOutputStream(os);
		zos.putNextEntry(new ZipEntry(fileName + "." + EngineConstants.FILEEXTN_XML));

		try
		{
			if(sheet != null)
			{
				workBookToXMLObj.writeSheetXML(sheet, zos);
			}
			else if(fileName.equals(EngineConstants.FILENAME_NAMEDRANGES))
			{
				workBookToXMLObj.writeNamedExpressionsXML(zos);
			}
			else if(fileName.equals(EngineConstants.FILENAME_CONTENTVALIDATIONS))
			{
				workBookToXMLObj.writeContentValidationsXml(zos);
			}
			else if(fileName.equals(EngineConstants.FILENAME_FILTERS))
			{
				workBookToXMLObj.writeFilterRangesXML(zos);
			}
			else if(fileName.equals(EngineConstants.FILENAME_ASTYLES))
			{
				workBookToXMLObj.writeAutomaticStyleXML(zos);
			}
			else if(fileName.equals(EngineConstants.FILENAME_FONTFACES))
			{
				workBookToXMLObj.writeFontFaceDeclsXML(zos);
			}

		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while writing to fragmented file..........{0}", e);
			throw new Exception("Problem while saving document");
		}
		finally
		{
			if(zos != null)
			{
				zos.close();
			}
			store.finishWrite(writeInfo);
		}
	}

	private void writeFragmentedFile(WorkbookToXML workBookToXMLObj, Sheet sheet, File tempFile, String fileName, long documentId) throws Exception
	{
		ZipOutputStream zos = null;
		try
		{
			zos = new ZipOutputStream(new FileOutputStream(tempFile));
			zos.putNextEntry(new ZipEntry(fileName));
			if(sheet != null)
			{
				workBookToXMLObj.writeSheetXML(sheet, zos);
			}
			else if(fileName.equals("namedranges.xml"))
			{
				workBookToXMLObj.writeNamedExpressionsXML(zos);
			}
			else if(fileName.equals("contentvalidations.xml"))
			{
				workBookToXMLObj.writeContentValidationsXml(zos);
			}
			else if(fileName.equals("filters.xml"))
			{
				workBookToXMLObj.writeFilterRangesXML(zos);
			}
			else if(fileName.equals("astyles.xml"))
			{
				workBookToXMLObj.writeAutomaticStyleXML(zos);
			}
			else if(fileName.equals("fontfaces.xml"))
			{
				workBookToXMLObj.writeFontFaceDeclsXML(zos);
			}
			zos.close();
		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while writing to fragmented file.........." + documentId, e);
			throw new Exception("Problem while saving document");
		}
		finally
		{
			try
			{
				if(zos != null)
				{
					zos.close();
				}
			}
			catch(Exception e)
			{
				LOGGER.log(Level.WARNING, "Engine: Error while closing streams (writeFragmentedFile)..........{0}", documentId);
			}
		}
	}

	/**
	 * DFS Implementation
	 *
	 * @param ownerZUID
	 * @param docOwner
	 * @param documentId
	 * @param fsFileFormat
	 * @throws Exception Added in DFS Migration
	 */
	public void migrateToDFS(long ownerZUID, String docOwner, long documentId, String fsFileFormat) throws Exception
	{
		if(EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT.equals(fsFileFormat))
		{
			writeFragmentsToODSFile(ownerZUID, docOwner, documentId, false, null);
		}
		else if(EngineConstants.ENGINE_ODS_FILE_FORMAT.equalsIgnoreCase(fsFileFormat) || EngineConstants.ENGINE_ZSHEET_FILE_FORMAT.equalsIgnoreCase(fsFileFormat))
		{
			Store store = StoreFactory.getInstance().getDocumentStore(ownerZUID, docOwner, documentId);
			InputStream is = null;
			SheetFileInfo sheetFileInfo = store.getFileInfo(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.ENGINE_ZSHEET_FILE_FORMAT.equalsIgnoreCase(fsFileFormat) ? EngineConstants.FILEEXTN_ZSHEET : EngineConstants.FILEEXTN_ODS);
			if(sheetFileInfo == null)
			{
				is = store.read(documentId, EngineConstants.ENGINE_ZSHEET_FILE_FORMAT.equalsIgnoreCase(fsFileFormat) ? EngineConstants.FILEEXTN_ZSHEET : EngineConstants.FILEEXTN_ODS);
				updateDocumentsTable(docOwner, documentId, ownerZUID); // This update is not need after migration ~mani
				sheetFileInfo = store.createFileInfo(documentId, EngineConstants.FILENAME_DOCUMENT, EngineConstants.ENGINE_ZSHEET_FILE_FORMAT.equalsIgnoreCase(fsFileFormat) ? EngineConstants.FILEEXTN_ZSHEET : EngineConstants.FILEEXTN_ODS);
				HashMap<String, Object> writeInfo = store.write(documentId, sheetFileInfo);
				try
				{
					OutputStream os = (OutputStream) writeInfo.get("OS");// NO I18N
					int c = 0;
					int bufferLength = 10240;
					byte[] tmpBytes = new byte[bufferLength];

					while((c = is.read(tmpBytes, 0, bufferLength)) != -1)
					{
						os.write(tmpBytes, 0, c);
					}
				}
				catch(Exception e)
				{
					LOGGER.log(Level.INFO, "Error occured{0} fsFileFormat: {1} docOwner: {2} Exception::{3}", new Object[]{documentId, fsFileFormat, docOwner, e});
//					e.printStackTrace();
				}
				finally
				{
					is.close();
					store.finishWrite(writeInfo);

				}
			}
		}
	}

	public void writeFragmentsToODSFile(long ownerZUID, String docOwner, long documentId, boolean isClearFragmentsFile) throws Exception
	{
		writeFragmentsToODSFile(ownerZUID, docOwner, documentId, isClearFragmentsFile, null);
	}

	public void writeFragmentsToODSFile(long ownerZUID, String docOwner, long documentId, boolean isClearFragmentsFile, List<JSONObjectWrapper> sheetFileList) throws Exception
	{
		writeFragmentsToODSFile(ownerZUID, docOwner, documentId, isClearFragmentsFile, null, false);
	}

	public void writeFragmentsToODSFile(long ownerZUID, String docOwner, long documentId, boolean isClearFragmentsFile, List<JSONObjectWrapper> sheetFileList, boolean isRemote) throws Exception
	{
		String fileType = EngineConstants.FILENAME_DOCUMENT;
		if(isRemote)
		{
			fileType = EngineConstants.FILENAME_REMOTEDOC;
		}
		Store store = StoreFactory.getInstance().getDocumentStore(ownerZUID, docOwner, documentId);
		SheetFileInfo sheetFileInfo = store.getFileInfo(documentId, fileType, EngineConstants.FILEEXTN_ODS);
		if(sheetFileInfo == null)
		{
			updateDocumentsTable(docOwner, documentId, ownerZUID); // This update is not need after migration ~mani
			sheetFileInfo = store.createFileInfo(documentId, fileType, EngineConstants.FILEEXTN_ODS);
		}
		writeFragmentsToODSFile(ownerZUID, docOwner, store, sheetFileInfo, documentId, isClearFragmentsFile, sheetFileList);
	}

	public void writeFragmentsToODSFile(long ownerZUID, String docOwner,
	                                    Store store, SheetFileInfo sheetFileInfo, long documentId,
	                                    boolean isClearFragmentsFile, List<JSONObjectWrapper> sheetFileList)
			throws Exception
	{
		/*
		 * logger.info("@@@@@ writeFragmentsToODSFile");
		 * logger.info("docOwner: "+docOwner);
		 * logger.info("isClearFragmentsFile: "+isClearFragmentsFile);
		 * logger.info("documentId: "+documentId);
		 * logger.info("sheetfileInfo: "+sheetFileInfo);
		 */

		// long ownerZUID = getZUID(docOwner);
		long resourceId = sheetFileInfo.getResourceId();
		long sheetId = -1;

		HashMap<String, Object> writeInfo = store.write(resourceId,
				sheetFileInfo);

		OutputStream os = (OutputStream) writeInfo.get("OS");// NO I18N

		long s = System.currentTimeMillis();
		File templateDir = new File(EngineConstants.ENGINETEMPLATEDIR);
		// long currenttime = System.currentTimeMillis();
		// String orgFileName = dirPath + EngineConstants.ENGINE_ODS_FILE_FORMAT;

		// String tempFileName = dirPath + "-" + hashcode + "-" + currenttime +
		// EngineConstants.ENGINE_ODS_FILE_FORMAT + "~";

		// OutputStream os = null;
		ZipOutputStream zos = null;
		InputStream is = null;
		ZSZipInputStream zis = null;
		boolean isErrorWhileWriting = false;
		try
		{
			Store sheetStore = null;
			DataObject dataObj = getFragmentDataObj(docOwner, documentId);
			// logger.log(Level.INFO,"dataObj: "+dataObj);
			int c = 0;
			int bufferLength = 10240;
			byte[] tmpBytes = new byte[bufferLength];

			zos = new ZipOutputStream(os);
			zos.putNextEntry(new ZipEntry("content.xml")); // No I18N

			// now read prolog.xml from template
			is = new FileInputStream(EngineConstants.PROLOGFILEPATH);
			while((c = is.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			is.close();
			// fontfaces
			sheetId = getFragmentId(dataObj, EngineConstants.FILENAME_FONTFACES);
			sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner,
					documentId, sheetId);
			is = sheetStore.read(sheetId, EngineConstants.FILENAME_FONTFACES,
					EngineConstants.FILEEXTN_ZIP);
			zis = new ZSZipInputStream(is);
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			is.close();
			if(isClearFragmentsFile)
			{
				sheetStore.delete(sheetId, EngineConstants.FILENAME_FONTFACES,
						EngineConstants.FILEEXTN_ZIP);
			}
			// astyles
			sheetId = getFragmentId(dataObj, EngineConstants.FILENAME_ASTYLES);
			sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner,
					documentId, sheetId);
			is = sheetStore.read(sheetId, EngineConstants.FILENAME_ASTYLES,
					EngineConstants.FILEEXTN_ZIP); // No I18N
			zis = new ZSZipInputStream(is);
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			is.close();
			if(isClearFragmentsFile)
			{
				sheetStore.delete(sheetId, EngineConstants.FILENAME_ASTYLES,
						EngineConstants.FILEEXTN_ZIP);
			}

			zos.write("<office:body>".getBytes()); // No I18N
			zos.write("<office:spreadsheet>".getBytes()); // No I18N

			// contentvalidation.xml
			sheetId = getFragmentId(dataObj, EngineConstants.FILENAME_CONTENTVALIDATIONS);
			sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, sheetId);
			is = sheetStore.read(sheetId, EngineConstants.FILENAME_CONTENTVALIDATIONS, EngineConstants.FILEEXTN_ZIP); // No I18N
			zis = new ZSZipInputStream(is);
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			is.close();
			if(isClearFragmentsFile)
			{
				sheetStore.delete(sheetId, EngineConstants.FILENAME_CONTENTVALIDATIONS, EngineConstants.FILEEXTN_ZIP);
			}

			if(sheetFileList == null)
			{
				// logger.log(Level.INFO,"--sheetFileList is null--");
				// sheetFileList = readSheetOrderFile(store, "order.json"); //No
				// I18N
				sheetId = EngineUtils.getFragmentId(dataObj,
						EngineConstants.FILENAME_ORDER);
				sheetStore = StoreFactory.getInstance().getSheetStore(
						ownerZUID, docOwner, documentId, sheetId);
				sheetFileList = EngineUtils.getInstance().readSheetOrderFile(
						sheetStore, sheetId, EngineConstants.FILENAME_ORDER).getOrderJSON();
				// I18N
			}
			// ////////// Sheets
			for(JSONObjectWrapper jsonObj : sheetFileList)
			{
				String sheetFileName = jsonObj.getString("fn");
				// here the file.getName() returns the absolute path of the file
				sheetId = getFragmentId(dataObj, sheetFileName);
				sheetStore = StoreFactory.getInstance().getSheetStore(
						ownerZUID, docOwner, documentId, sheetId);
				is = sheetStore.read(sheetId, sheetFileName,
						EngineConstants.FILEEXTN_ZIP); // No I18N
				zis = new ZSZipInputStream(is);
				zis.getNextEntry();
				while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
				{
					zos.write(tmpBytes, 0, c);
				}
				zis.close();
				is.close();
				if(isClearFragmentsFile)
				{
					sheetStore.delete(sheetId, sheetFileName,
							EngineConstants.FILEEXTN_ZIP);
				}

			}

			// namedranges.xml
			sheetId = getFragmentId(dataObj,
					EngineConstants.FILENAME_NAMEDRANGES);
			sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner,
					documentId, sheetId);
			is = sheetStore.read(sheetId, EngineConstants.FILENAME_NAMEDRANGES,
					EngineConstants.FILEEXTN_ZIP); // No I18N
			zis = new ZSZipInputStream(is);
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			is.close();
			if(isClearFragmentsFile)
			{
				sheetStore.delete(sheetId,
						EngineConstants.FILENAME_NAMEDRANGES,
						EngineConstants.FILEEXTN_ZIP);
			}
			// filters.xml
			sheetId = getFragmentId(dataObj, EngineConstants.FILENAME_FILTERS);
			sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner,
					documentId, sheetId);
			is = sheetStore.read(sheetId, EngineConstants.FILENAME_FILTERS,
					EngineConstants.FILEEXTN_ZIP);
			zis = new ZSZipInputStream(is);
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			is.close();
			if(isClearFragmentsFile)
			{
				sheetStore.delete(sheetId, EngineConstants.FILENAME_FILTERS,
						EngineConstants.FILEEXTN_ZIP);
			}
			zos.write("</office:spreadsheet>".getBytes()); // No I18N
			zos.write("</office:body>".getBytes()); // No I18N
			zos.write("</office:document-content>".getBytes()); // No I18N

			// for writing styles.xml
			boolean isStylesXMLWriiten = false;
			sheetId = getFragmentId(dataObj, EngineConstants.FILENAME_STYLES);
			// if (sheetId > 0) {//non migrate files also has to come ~Mani
			sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner,
					documentId, sheetId);
			is = sheetStore.read(sheetId, EngineConstants.FILENAME_STYLES,
					EngineConstants.FILEEXTN_ZIP);
			if(is != null)
			{
				zis = new ZSZipInputStream(is);
				zis.getNextEntry();
				zos.putNextEntry(new ZipEntry("styles.xml"));  // No I18N
				while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
				{
					zos.write(tmpBytes, 0, c);
				}
				isStylesXMLWriiten = true;
				zis.close();
				is.close();
				if(isClearFragmentsFile)
				{
					sheetStore.delete(sheetId, EngineConstants.FILENAME_STYLES,
							EngineConstants.FILEEXTN_ZIP);
				}
			}

			// }

			// for writing macros
			sheetId = getFragmentId(dataObj, EngineConstants.FILENAME_MACROS);
			// if (sheetId > 0) {//non migrate files also has to come ~Mani
			sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner,
					documentId, sheetId);
			is = sheetStore.read(sheetId, EngineConstants.FILENAME_MACROS,
					EngineConstants.FILEEXTN_ZIP);
			if(is != null)
			{
				zis = new ZSZipInputStream(is); // it will contian multiple files
				ZipEntry zipEntry = null;
				while((zipEntry = zis.getNextEntry()) != null)
				{
					zos.putNextEntry(zipEntry);
					while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
					{
						zos.write(tmpBytes, 0, c);
					}
				}
				zis.close();
				is.close();
				if(isClearFragmentsFile)
				{
					sheetStore.delete(sheetId, EngineConstants.FILENAME_MACROS,
							EngineConstants.FILEEXTN_ZIP);
				}
			}
			// }

			// for writing settings
			boolean isSettingsWritten = false;
			sheetId = getFragmentId(dataObj, EngineConstants.FILENAME_SETTINGS);
			// if (sheetId > 0) { //non migrate files also has to come ~Mani
			sheetStore = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner,
					documentId, sheetId);
			is = sheetStore.read(sheetId, EngineConstants.FILENAME_SETTINGS,
					EngineConstants.FILEEXTN_ZIP);
			if(is != null)
			{
				// is = store.read(fileName); //No I18N
				zis = new ZSZipInputStream(is);
				ZipEntry zipEntry = null;
				while((zipEntry = zis.getNextEntry()) != null)
				{
					zos.putNextEntry(zipEntry);
					while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
					{
						zos.write(tmpBytes, 0, c);
					}
				}
				zis.close();
				is.close();
				isSettingsWritten = true;
				if(isClearFragmentsFile)
				{
					sheetStore.delete(sheetId,
							EngineConstants.FILENAME_SETTINGS,
							EngineConstants.FILEEXTN_ZIP);
				}
			}

			// }

			String fileName = "";
			// adds files from template folder to the output.ods zip file
			for(File file : templateDir.listFiles())
			{
				if(file.isFile())
				{
					fileName = file.getName();
					if(fileName.equals("styles.xml"))
					{
						if(isStylesXMLWriiten)
						{
							continue;
						}
						// ////////////
						if(EngineConstants.ISMKI || EngineConstants.ISNTT)
						{
							file = new File(EngineConstants.ENGINEDIR + File.separator + "ja" + File.separator + "styles.xml"); // No I18N
						}
						else if(EngineConstants.ISBAIHUI)
						{
							file = new File(EngineConstants.ENGINEDIR + File.separator + "zh" + File.separator + "styles.xml"); // No I18N
						}
						else if(EngineConstants.ISFUJIXEROX)
						{
							file = new File(EngineConstants.ENGINEDIR + File.separator + "fuji" + File.separator + "styles.xml"); // No I18N
						}
					}
					else if(isSettingsWritten
							&& fileName.equals("settings.xml"))
					{
						continue;
					}

					WorkbookToXML.writeFile("", zos, file); // closeEntry() done
					// in this fn
				}
				else if(file.isDirectory())
				{
					String path = file.getName();
					WorkbookToXML.exploreFolder(path, zos, file);
				}
			}

			// os.close();
			// temp ods file created

			// write this to original file
			// writeToOriginalFile(store, tempFileName, orgFileName);

			// /////// clear the fragmented file
			/*
			 * if (isClearFragmentsFile) { clearFragmentsFile(store, dirPath); }
			 */

		}
		catch(Exception ex)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while writing the fragments to ODS file.........." + documentId, ex);
			// send email
			MailUtil.reportErrorToAdmins(String.valueOf(documentId), "Error while writing fragments to ODS", ex); // No I18N
			isErrorWhileWriting = true;
			throw new Exception("Problem while saving document");
		}
		finally
		{
			try
			{
				if(is != null)
				{
					is.close();
				}
				if(zos != null)
				{
					zos.close();
				}
				store.finishWrite(writeInfo);
			}
			catch(Exception e)
			{
				LOGGER.log(Level.WARNING, "Engine: Error while closing streams (writeFragmentsToODSFile).........." + documentId, e);//No I18N
			}

			if(isErrorWhileWriting)
			{
				LOGGER.info("Engine: Deleting corrupted ods (writing fragments to ods) file....... Need to handle ~Mani");//No I18N
				// store.delete(destFileName);
			}
		}
		LOGGER.log(Level.INFO, "Time taken to write Fragments to ODS alone :: {0} for file :: ", (System.currentTimeMillis() - s));//No I18N

	}

	public void clearFragmentsFile(long ownerZUID, String docOwner, long documentId) throws Exception
	{
//		long ownerZUID = getZUID(docOwner);
		DataObject dataObj = getFragmentDataObj(docOwner, documentId);
		if(!dataObj.isEmpty())
		{
			Long[] sheetList = getAllFragmentIDS(dataObj);
			Store store = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, sheetList);
			clearFragmentsFile(store, sheetList);
		}
	}

	public void clearFragmentsFile(Store store, Long[] resourceId) throws Exception
	{
		store.delete(resourceId, null, EngineConstants.FILEEXTN_ZIP);
	}

	/**
	 * DFS IMPLEMENTATION
	 *
	 * @param list
	 * @param store
	 * @param sheetfileInfo
	 * @param resourceId
	 * @throws java.lang.Exception
	 */
	public void writeSheetOrderFile(List<JSONObjectWrapper> list, Store store, SheetFileInfo sheetfileInfo, long resourceId) throws Exception
	{
		// order.json incase of fragments
		// sheetid.json incase of ods
		BufferedWriter bw = null;
		try
		{
			HashMap writeInfo = store.write(resourceId, sheetfileInfo);
			OutputStream os = (OutputStream) writeInfo.get("OS");
			bw = new BufferedWriter(new OutputStreamWriter(os));
			for(JSONObjectWrapper jObj : list)
			{
				bw.write(jObj.toString());
				bw.newLine();
			}
			bw.flush();
			store.finishWrite(writeInfo);
		}
		catch(Exception ex)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while writing the sheet order file.", ex);
			throw new Exception("Problem while saving document", ex);
		}
	}

	/**
	 * DFS IMPLEMENTATION
	 *
	 * @param store
	 * @param sheetId
	 * @param fileName
	 * @return
	 * @throws Exception
	 */
	/*
	 * public List<JSONObjectWrapper> readSheetOrderFile(FileStore store, String
	 * dirPath, String fileName) throws Exception {
	 */
	public OrderFileReader readSheetOrderFile(Store store, long sheetId, String fileName) throws Exception
	{
		try(InputStream in = store.read(sheetId, fileName, EngineConstants.FILEEXTN_JSON))
		{
			OrderFileReader orderFileReader = new OrderFileReader(in);
			return orderFileReader;
		}
		catch(Exception ex)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while reading the sheet order file : {0}", ex);
			throw new Exception("Problem while parsing document", ex);
		}
	}

	///////////////////////////////////////////////////////////////////////////////////

	public File createTempODSFile(Workbook workBook) throws Exception
	{
		File cacheFile = null;
		ZipOutputStream zos = null;
		ZipInputStream zis = null;
		InputStream is = null;

		int c = 0;
		int bufferLength = 10240;
		byte[] tmpBytes = new byte[bufferLength];
		try
		{
			LOGGER.log(Level.INFO, "EngineUtils createTempODSFile");//No I18N
			//TODO : Comment this method, if not in use.
			String dirPath = EngineConstants.TEMPEXPORTDIR;
			//String dirPath = AppResources.getProperty("server.home") + File.separator + "webapps" + File.separator + "websheet" + File.separator + "exportdocs";//No internationalization
			// For Local Testing
//	    String serverHome = "c:\\temp1";
//	    String dirPath = serverHome;

			File dir = new File(dirPath);
			if(!dir.exists())
			{
				dir.mkdirs();
			}
			String timeStamp = workBook.hashCode() + "-" + String.valueOf(System.currentTimeMillis());    //No I18N
			String cacheDirPath = dirPath + File.separator + timeStamp;

			File cacheDir = new File(cacheDirPath); // to be deleted this dir
			if(!cacheDir.exists())
			{
				cacheDir.mkdirs();
			}

			String cacheFilePath = cacheDirPath + EngineConstants.ENGINE_ODS_FILE_FORMAT;
			// For Local Testing
			//String cacheFilePath = cacheDir + File.separator + "nilam" + EngineConstants.ENGINE_ODS_FILE_FORMAT;
			cacheFile = new File(cacheFilePath);

			WorkbookToXML workBookToXMLObj = new WorkbookToXML(workBook, true);
			String zipFileName = cacheDirPath + "/" + "sheets.zip"; //No I18N
			String fileName = "sheets.xml"; //No I18N

			zos = new ZipOutputStream(new FileOutputStream(zipFileName));
			zos.putNextEntry(new ZipEntry(fileName));
			// write all the sheets first
			for(Sheet sheet : workBook.getSheetList())
			{
				workBookToXMLObj.writeSheetXML(sheet, zos);

			}
			//zos.closeEntry();
			zos.close();

			zipFileName = cacheDirPath + "/" + "namedranges.zip"; //No I18N
			fileName = "namedranges.xml"; //No I18N
			zos = new ZipOutputStream(new FileOutputStream(zipFileName));
			zos.putNextEntry(new ZipEntry(fileName));
			workBookToXMLObj.writeNamedExpressionsXML(zos);
			//zos.closeEntry();
			zos.close();

			zipFileName = cacheDirPath + "/" + "contentvalidations.zip"; //No I18N
			fileName = "contentvalidations.xml"; //No I18N
			zos = new ZipOutputStream(new FileOutputStream(zipFileName));
			zos.putNextEntry(new ZipEntry(fileName));
			workBookToXMLObj.writeContentValidationsXml(zos);
			//zos.closeEntry();
			zos.close();

			zipFileName = cacheDirPath + "/" + "filters.zip"; //No I18N
			fileName = "filters.xml"; //No I18N
			zos = new ZipOutputStream(new FileOutputStream(zipFileName));
			zos.putNextEntry(new ZipEntry(fileName));
			workBookToXMLObj.writeFilterRangesXML(zos);
			//zos.closeEntry();
			zos.close();

			zipFileName = cacheDirPath + "/" + "astyles.zip"; //No I18N
			fileName = "astyles.xml"; //No I18N
			zos = new ZipOutputStream(new FileOutputStream(zipFileName));
			zos.putNextEntry(new ZipEntry(fileName));
			workBookToXMLObj.writeAutomaticStyleXML(zos);
			//zos.closeEntry();
			zos.close();

			zipFileName = cacheDirPath + "/" + "fontfaces.zip"; //No I18N
			fileName = "fontfaces.xml"; //No I18N
			zos = new ZipOutputStream(new FileOutputStream(zipFileName));
			zos.putNextEntry(new ZipEntry(fileName));
			workBookToXMLObj.writeFontFaceDeclsXML(zos);
			//zos.closeEntry();
			zos.close();

			// not needed now as prolog.xml is amde as template
//	    zipFileName = dirPath+ "/" +"prolog.zip"; //No I18N
//	    fileName = "prolog.xml"; //No I18N
//	    zos = new ZipOutputStream(new FileOutputStream(zipFileName));
//	    zos.putNextEntry(new ZipEntry(fileName));
//	    workBookToXMLObj.writePrologXML(zos);
//	    zos.closeEntry(); zos.close();

			/////////////////////////
			zos = new ZipOutputStream(new FileOutputStream(cacheFile));
			zos.putNextEntry(new ZipEntry("content.xml")); //No I18N

// write all files here
//	    zis = new ZipInputStream(new FileInputStream(dirPath+ "/" +"prolog.zip")); //No I18N
//	    zis.getNextEntry();
//	    while((c = zis.read(tmpBytes,0,bufferLength)) != -1 )
//	    {
//		zos.write(tmpBytes, 0, c);
//	    }
//	    zis.close();
//	    (new File(dirPath+ "/" +"prolog.zip")).delete(); //No I18N

			// write the prolog.xml from template
			is = new FileInputStream(EngineConstants.PROLOGFILEPATH);
//	    is = new FileInputStream("C:\\websheet\\AdventNet\\Sas\\engine\\prolog.xml");
			while((c = is.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			is.close();

			zis = new ZipInputStream(new FileInputStream(cacheDirPath + "/" + "fontfaces.zip")); //No I18N
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			(new File(cacheDirPath + "/" + "fontfaces.zip")).delete(); //No I18N

			zis = new ZipInputStream(new FileInputStream(cacheDirPath + "/" + "astyles.zip")); //No I18N
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			(new File(cacheDirPath + "/" + "astyles.zip")).delete(); //No I18N

			//////////// Sheets
			zos.write("<office:body>".getBytes()); //No I18N
			zos.write("<office:spreadsheet>".getBytes()); //No I18N

			zis = new ZipInputStream(new FileInputStream(cacheDirPath + "/" + "contentvalidations.zip"));
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			(new File(cacheDirPath + "/" + "contentvalidations.zip")).delete(); //No I18N

			zis = new ZipInputStream(new FileInputStream(cacheDirPath + "/" + "sheets.zip")); //No I18N
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			(new File(cacheDirPath + "/" + "sheets.zip")).delete(); //No I18N

			zis = new ZipInputStream(new FileInputStream(cacheDirPath + "/" + "namedranges.zip")); //No I18N
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			(new File(cacheDirPath + "/" + "namedranges.zip")).delete(); //No I18N

			zis = new ZipInputStream(new FileInputStream(cacheDirPath + "/" + "filters.zip")); //No I18N
			zis.getNextEntry();
			while((c = zis.read(tmpBytes, 0, bufferLength)) != -1)
			{
				zos.write(tmpBytes, 0, c);
			}
			zis.close();
			(new File(cacheDirPath + "/" + "filters.zip")).delete(); //No I18N

			zos.write("</office:spreadsheet>".getBytes()); //No I18N
			zos.write("</office:body>".getBytes()); //No I18N
			zos.write("</office:document-content>".getBytes()); //No I18N
			/////

			boolean isStylesXMLWritten = false;
			if(!workBookToXMLObj.getConditionalCellStyleNameSet().isEmpty())
			{
				// code to check if styles.zip exists
				//zipFileName = cacheDirPath+ "/" +"styles.zip"; //No I18N
				//File styleFile = store.exists(dirPath+ "/" +"styles.zip") ? new File(dirPath+ "/" +"styles.zip"): null;
				writeStylesXML(zos, workBookToXMLObj, workBook, null);
				///
				isStylesXMLWritten = true;
			}

			////////
			// write macros
			// write the Macros
			boolean isManifestWrittten = false;
			if(workBook.containsMacro())
			{
				MacroWriter.writeMacros(zos, workBook);
				EngineUtils1.writeManifestXML(zos, workBook);
				isManifestWrittten = true;
			}

			// write the settings.xml
			SettingsWriter.writeSettings(zos, workBook.getWorkbookSettings());
			/////////////////////////////////

			//zos.flush(); zos.closeEntry();

			File tempFile = new File(EngineConstants.ENGINETEMPLATEDIR);
			// For Local Testing
//	    File tempFile = new File("C:\\websheet\\AdventNet\\Sas\\engine\\template");

			//adds files from template folder to the output.ods zip file
			for(File file1 : tempFile.listFiles())
			{
				if(file1.isFile())
				{
					String fileName1 = file1.getName();
					if(fileName1.equals("styles.xml")) //No I18N
					{
						if(isStylesXMLWritten)
						{
							continue;
						}
						/////////
						if(EngineConstants.ISMKI || EngineConstants.ISNTT)
						{
							file1 = new File(EngineConstants.ENGINEDIR + File.separator + "ja" + File.separator + "styles.xml");
						}
						else if(EngineConstants.ISBAIHUI)
						{
							file1 = new File(EngineConstants.ENGINEDIR + File.separator + "zh" + File.separator + "styles.xml");
						}
						else if(EngineConstants.ISFUJIXEROX)
						{
							file1 = new File(EngineConstants.ENGINEDIR + File.separator + "fuji" + File.separator + "styles.xml");
						}
					}
					else if(fileName1.equals("settings.xml"))
					{
						continue;
					}
					WorkbookToXML.writeFile("", zos, file1); // close entry done here

				}
				else if(file1.isDirectory())
				{
					String path = file1.getName();
					// There is only one file manifest.xml in the META-INF folder, hence we are doing the direct skip for the folder
					// in the engine/template dir
					if(path.equals("META-INF"))
					{
						if(isManifestWrittten)
						{
							continue;                    //skip manifest.xml if it has already been written for macros PS:LibreOffice launch
						}
					}
					WorkbookToXML.exploreFolder(path, zos, file1);
				}
			}

			zos.close();

			// Delete the folder of timestamp which is cacheDir
			cacheDir.delete();
		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "Error while writing to temp ODS file..........", e);
		}
		finally
		{
			try
			{
				if(zis != null)
				{
					zis.close();
				}
				if(is != null)
				{
					is.close();
				}
				if(zos != null)
				{
					zos.close();
				}
			}
			catch(Exception e2)
			{
				LOGGER.log(Level.WARNING, "Engine: Error while closing stream (createTempODSFile)......................", e2);
			}
		}
		return cacheFile;
	}

	public static String getSheetNames(Workbook workBook)
	{
		if(workBook != null)
		{
			JSONArrayWrapper jsonArr = new JSONArrayWrapper();
			Sheet sheets[] = workBook.getSheets();
			if(sheets != null)
			{
				for(Sheet sheet : sheets)
				{
					jsonArr.put(sheet.getName());
				}
			}
			return jsonArr.toString();
		}
		return null;
	}

	/**
	 * ~DFS Implementation
	 *
	 * @param ownerZUID
	 * @param activesheet
	 * @param documentId
	 * @param docOwner
	 * @param sheetNamesArr
	 * @param usedRow
	 * @param usedCol
	 * @param rowHeight
	 * @param recalcRequired
	 * @param docContainsArrayFormula
	 * @throws Exception Calling from: createODSFile
	 */
	public static void writeSheetMetaInfo(long ownerZUID, String docOwner, long documentId, String activesheet, String[] sheetNamesArr, int usedRow, int usedCol, String rowHeight, boolean recalcRequired,
	                                      boolean docContainsArrayFormula) throws Exception
	// public static void writeSheetMetaInfo(String docid, String activesheet,
	// Long accountid, String[] sheetNamesArr, int usedRow, int usedCol, String
	// rowHeight, boolean recalcRequired, boolean docContainsArrayFormula)
	// throws Exception
	{
		String sheetNames = convertArrayToJSONString(sheetNamesArr);
		Properties props = new Properties();

		long resourceId = getFragmentID(docOwner, documentId, EngineConstants.FILENAME_CACHE, ownerZUID, true);
		Store store = null;
		SheetFileInfo sheetFileInfo = null;
//		long ownerZUID = getZUID(docOwner);
		try
		{
			store = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, resourceId);
			sheetFileInfo = store.getFileInfo(resourceId, EngineConstants.FILENAME_CACHE, EngineConstants.FILEEXTN_PROPS);

			if(sheetFileInfo == null)
			{
				sheetFileInfo = store.createFileInfo(resourceId, EngineConstants.FILENAME_CACHE, EngineConstants.FILEEXTN_PROPS);
			}
			else
			{
				InputStream fis = store.read(resourceId, null, EngineConstants.FILEEXTN_PROPS);
				props.load(fis);
				fis.close();
			}
		}
		catch(Exception e)
		{
			LOGGER.info("Error Occured");//No I18N
		}

		if(activesheet != null)
		{
			props.setProperty(activesheet + ".Version", EngineConstants.version);
		}

		if(sheetNames != null)
		{
			props.setProperty("SHEET_NAMES", sheetNames);
		}
		if(activesheet != null)
		{
			props.setProperty("ACTIVESHEET", activesheet);
			JSONObjectWrapper sheetMetaObject = new JSONObjectWrapper();
			sheetMetaObject.put(EngineConstants.USEDROWKEY, String.valueOf(usedRow));
			sheetMetaObject.put(EngineConstants.USEDCOLKEY, String.valueOf(usedCol));

			// No Need To Put An Entry, if it is null
			if(rowHeight != null && !"".equals(rowHeight))
			{
				sheetMetaObject.put(EngineConstants.ROWHEIGHTKEY, rowHeight);
			}

			props.setProperty(activesheet, sheetMetaObject.toString());
		}

		props.setProperty(EngineConstants.IS_RECAL_REQUIRED, String.valueOf(recalcRequired));

		props.setProperty(EngineConstants.IS_DOC_CONTAINS_ARRAYFORMULAS, String.valueOf(docContainsArrayFormula));

		// props.setProperty("USED_ROW", String.valueOf(usedRow));
		// props.setProperty("USED_COL", String.valueOf(usedCol));
		HashMap writeInfo = store.write(resourceId, sheetFileInfo);
		OutputStream out = (OutputStream) writeInfo.get("OS");
		// OutputStream out = fstore.write(filePath, false);
		props.store(out, EngineConstants.FILENAME_CACHE); // No
		// internationalization
		store.finishWrite(writeInfo);
	}

	public static void removeSheetMetaInfo(long ownerZUID, String docOwner, long documentId) throws Exception
	{
		DataObject dataObj = getFragmentDataObj(docOwner, documentId);
		long fragmentId = getFragmentId(dataObj, EngineConstants.FILENAME_CACHE);
		Store store = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, fragmentId);
		store.delete(fragmentId, EngineConstants.FILEEXTN_PROPS);
	}

	public static InputStream readSheetMetaInfo(long ownerZUID, String docOwner, long documentId) throws Exception
	{
		long fragmentId = getFragmentId(docOwner, documentId, EngineConstants.FILENAME_CACHE);
		Store store = StoreFactory.getInstance().getSheetStore(ownerZUID, docOwner, documentId, fragmentId);

		InputStream is = store.read(fragmentId, EngineConstants.FILEEXTN_PROPS);
		return is;
	}

	public static void savehtml(String docid, String activesheet, Long accountid, String htmlStr, boolean setactivesheet, boolean saveHTML) throws Exception
	{


		String filePath = "";
		if(saveHTML)
		{
			if(htmlStr == null)
			{
				return;
			}
			htmlStr = changeClassName(htmlStr);
			/*
	    cache sheet data using sheet name issue exist in BAIHUI setup. If it is fixed then use this commented code.
			if(EngineConstants.ISMKI || EngineConstants.ISBAIHUI || EngineConstants.ISNTT) {
				filePath = "/"+docid+"/"+"cache"+"/"+ activesheet +".html"+EngineConstants.COMPRESSED_FILE_FORMAT;
			}else{
				filePath = "/"+docid+"/"+"cache"+"/"+URLEncoder.encode(activesheet,"UTF-8")+".html"+EngineConstants.COMPRESSED_FILE_FORMAT;
			}*/
			filePath = "/" + docid + "/" + "cache" + "/" + URLEncoder.encode(activesheet, "UTF-8") + ".html" + EngineConstants.COMPRESSED_FILE_FORMAT;
			byte[] htmlStrInBytes = htmlStr.getBytes();
			//storeContent(accountid, filePath, htmlStrInBytes); SAS18_IMPL FS Comment ~Mani
		}
	}
	/******* Methods copied from DocumentUtils **********/

	/******* Methods copied from UserUtils
	 * @param aTagFragment
	 * @return  **********/
	public static String forHTMLTag(String aTagFragment)
	{
		final StringBuffer result = new StringBuffer();

		final StringCharacterIterator iterator = new StringCharacterIterator(aTagFragment);
		char character = iterator.current();
		while(character != CharacterIterator.DONE)
		{
			if(character == '<')
			{
				result.append("&lt;");                                                            //No I18N
			}
			else if(character == '>')
			{
				result.append("&gt;");                                                            //No I18N
			}
			else if(character == '\"')
			{
				result.append("&quot;");                                                        //No I18N
			}
			else if(character == '\'')
			{
				result.append("&#039;");
			}
			else if(character == '\\')
			{
				result.append("&#092;");
			}
			else if(character == '&')
			{
				result.append("&amp;");                                                            //No I18N
			}
			else
			{
				//the char is not a special one
				//add it to the result as is
				result.append(character);
			}
			character = iterator.next();
		}
		return result.toString();
	}

	public static String changeClassName(String html) throws Exception
	{
		String chartregex = "charts=\"[^\"]+\"";
		//regex for graph image check in firefox mozilla
		String imageregex = "<img style.*id=\"ExcelGraph[^\"]+\"[^<]+";
		//regex for graph image check in ie
		String imageregexie = "<IMG class.*id.*ExcelGraph[^\"]+\"[^<]+";

		html = html.replaceAll("colHeadFocus", "cH");
		html = html.replaceAll("rowHeadFocus", "rH");
		html = html.replaceAll(imageregex, "");
		html = html.replaceAll(imageregexie, "");
		html = html.replaceAll(chartregex, "");
		return html;
	}

	public static ArrayList getStockDataList(BufferedReader br)
	{
		ArrayList list = new ArrayList();
		CSVReader reader = new CSVReader(br, ',', '\"', true, true);
		while(true)
		{
			try
			{
				Vector lineData = reader.getAllFieldsInLine();
				for(Object lineData1 : lineData)
				{
					list.add(lineData1.toString());
				}
			}
			catch(Exception eof)
			{
				break;
			}
		}
		return list;
	}

	/**
	 * To Fetch Stock Details Copied from DocumentUtils.java
	 *
	 * @param row
	 * @return
	 */

	public static boolean isTiledRow(int row)
	{
		return ((row % EngineConstants.ROWS_PER_TILE) == 0);
	}

	/*
	 */
	public static int getCeilRow(int row)
	{
		if(!isTiledRow(row))
		{
			//row = (row-(row%EngineConstants.ROWS_PER_TILE)+EngineConstants.ROWS_PER_TILE);
			row = getFloorRow(row) + EngineConstants.ROWS_PER_TILE;
		}
		return row;
	}

	/*
	 *
	 */
	public static int getFloorRow(int row)
	{
		if(!isTiledRow(row))
		{
			row = (row - (row % EngineConstants.ROWS_PER_TILE));
		}
		return row;
	}

	/*
		Validating Max Dimension In Grid.
	 */
	private void validateMaxDIM(SheetVariables sheetVar, int endRow, int endCol)
	{
		if(endRow + 15 >= EngineConstants.DEFAULTROWS)
		{
			//For To Make rounded It As Tiled Row
			//sheetVar.rows = endRow + 15;
			sheetVar.rows = getCeilRow(endRow) + EngineConstants.ROWS_PER_TILE;
			int maxRows = getFloorRow(Utility.MAXNUMOFROWS);

			if(sheetVar.rows > maxRows)
			{
				sheetVar.rows = maxRows;
			}
		}
		else
		{
			sheetVar.rows = EngineConstants.DEFAULTROWS;
		}

		//if(endCol + 5 >= EngineConstants.DEFAULTCOLS)
		//{
		//sheetVar.cols = endCol + 5;
		sheetVar.cols = endCol;
		if(sheetVar.cols > Utility.MAXNUMOFCOLS)
		{
			sheetVar.cols = Utility.MAXNUMOFCOLS;
		}
	/*}
	else
	{
	    sheetVar.cols = EngineConstants.DEFAULTCOLS;
	}*/
	}

	public OrderFileReader readSheetOrderFileLocal(String dirPath, String fileName) throws Exception
	{
		// order.json incase of fragments
		// sheetid.json incase of ods
		fileName = dirPath + "/" + fileName; //No I18N
		File file = new File(fileName);
		try
		{
			if(file.exists())
			{
				try(FileInputStream fileInputStream = new FileInputStream(file))
				{
					OrderFileReader orderFileReader = new OrderFileReader(fileInputStream);
					return orderFileReader;
				}
			}
			else
			{
				throw new Exception("Problem while parsing document");
			}
		}
		catch(Exception ex)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while reading the sheet order file : " + dirPath, ex);
			throw new Exception("Problem while parsing document");
		}

	}
	/////////////////////////////////////Local Methods////////////////////////////////////////


	public static JSONObjectWrapper readUserProperty(long ownerZUID, String docOwner)
	{
		BufferedReader br = null;
		Store store = null;
		JSONObjectWrapper jObj = null;
		InputStream is = null;
		try
		{
			long userdataId = getUserDataId(ownerZUID, docOwner, false,EngineConstants.FILENAME_USERINFO);
			if(userdataId > 0)
			{
				store = StoreFactory.getInstance().getUserStore(ownerZUID, docOwner, userdataId);
				SheetFileInfo info = store.getFileInfo(userdataId, EngineConstants.FILENAME_USERINFO, EngineConstants.FILEEXTN_PROPS);
				if(info != null)
				{
					is = store.read(userdataId, EngineConstants.FILEEXTN_PROPS);
					if(is != null)
					{
						br = new BufferedReader(new InputStreamReader(is));
						String str = null;
						while((str = br.readLine()) != null)
						{
							jObj = new JSONObjectWrapper(str);
						}
						// New boolean, to identify whether string got encoded.(Done one time)
						// Since previous string's will not be encoded. Once ISENCODED is true, later added string's will be encoded.
						if(!jObj.has("ISENCODED")) // No I18N
						{
							jObj.put("ISENCODED", true); // No I18N
							List<String> encodedJsonList = new ArrayList();
							List jsonList = JSONArrayWrapper.toList(jObj.has("USERLEVELCUSTOMFORMATS") ? jObj.getJSONArray("USERLEVELCUSTOMFORMATS") : new JSONArrayWrapper()); // No I18N
							for(Object customFrmtJson : jsonList)
							{
								if(customFrmtJson instanceof String)
								{
									encodedJsonList.add(Utility.getEncodedString((String) customFrmtJson).replace("+", "%20"));
								}
							}
							jObj.put("USERLEVELCUSTOMFORMATS", encodedJsonList); // No I18N
						}
					}
				}
			}
			if(jObj == null)
			{
				jObj = new JSONObjectWrapper();
				jObj.put("ISENCODED", true); // No I18N
				jObj.put("USERLEVELCUSTOMFORMATS", new ArrayList()); // No I18N
			}

			if(!jObj.has("USERLEVELCUSTOMFORMATS")){// No I18N
				jObj.put("ISENCODED", true); // No I18N
				jObj.put("USERLEVELCUSTOMFORMATS", new ArrayList()); // No I18N
			}

			if(!jObj.has("USERLEVELCUSTOMFONTS")){// No I18N
				jObj.put("ISENCODED", true); // No I18N
				jObj.put("USERLEVELCUSTOMFONTS", new ArrayList()); // No I18N
			}

		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, null, e);
		}
		finally
		{
			if(is != null)
			{
				try
				{
					is.close();
				}
				catch(IOException e)
				{
					LOGGER.log(Level.WARNING, null, e);
				}
			}
		}
		return jObj;
	}

	public static void saveUserProperty(long ownerZUID, String docOwner, List<JSONObjectWrapper> list)
	{
		BufferedWriter bw = null;
		Store store = null;
		HashMap writeInfo = null;
		try
		{
//			long ownerZUID = getZUID(docOwner);
			long userdataId = getUserDataId(ownerZUID, docOwner, true,EngineConstants.FILENAME_USERINFO);
			store = StoreFactory.getInstance().getUserStore(ownerZUID, docOwner, userdataId);
			SheetFileInfo info = store.getFileInfo(userdataId, EngineConstants.FILENAME_USERINFO, EngineConstants.FILEEXTN_PROPS);
			if(info == null)
			{
				info = store.createFileInfo(userdataId, EngineConstants.FILENAME_USERINFO, EngineConstants.FILEEXTN_PROPS);
			}
			writeInfo = store.write(userdataId, info);
			OutputStream os = (OutputStream) writeInfo.get("OS");
			bw = new BufferedWriter(new OutputStreamWriter(os));
			for(JSONObjectWrapper jObj : list)
			{
				bw.write(jObj.toString());
			}
		}
		catch(Exception ex)
		{
			LOGGER.log(Level.WARNING, "Engine: Error while reading the sheet order file.", ex);
		}
		finally
		{
			if(bw != null)
			{
				try
				{
					bw.flush();
					bw.close();
					store.finishWrite(writeInfo);
				}
				catch(Exception ex1)
				{
					LOGGER.log(Level.WARNING, null, ex1);
				}
			}
		}
	}

	public static JSONObjectWrapper getRowData(Sheet sheet, int row)
	{

		JSONObjectWrapper rowData = new JSONObjectWrapper();

		Row rRow = sheet.getReadOnlyRowFromShell(row).getRow();

		if(!RowUtil.isVisibleRow(rRow))
		{
			List<Cell> cells = rRow.getCells();
			Cell cell = null;

			for(int i = 0; i < cells.size(); )
			{ // iterating cells in a row

				cell = cells.get(i);

				int colsRepeatCount = 1;//cell.getColsRepeated();
				int colSpan = 1;//cell.getColSpan();
				String formula = null;//cell.getFormula();

				if(cell != null)
				{

					colsRepeatCount = cell.getColsRepeated();
					//colSpan = cell.getColSpan();
					colSpan = sheet.getMergeCellSpans(cell)[1];
					formula = cell.getFormula();
				}

				if(formula != null && !"".equals(formula))
				{

					for(int j = 0; j < colsRepeatCount; j++)
					{

						rowData.put(String.valueOf(i + j), formula);

					}

					i = i + Math.max(colSpan, colsRepeatCount);

				}
				else
				{
					i = i + Math.max(colSpan, colsRepeatCount);
				}
			}

		}

		return rowData;

	}

	/**
	 * ~Mani Following Methods Introduced in DFS Implementation
	 * */
	// ----------------------------------------------DATABASE RELATED----------------------------------------------------------//

	/**
	 * @param docOwner
	 * @param documentId
	 * @return
	 * @throws Exception
	 */
	public static DataObject getFragmentDataObj(String docOwner, long documentId) throws Exception
	{
		Persistence persistence = null;
		if(docOwner != null)
		{
			persistence = SheetPersistenceUtils.getPersistence(docOwner);
		}
		else
		{
			LOGGER.info("[DB_LOOK_CHANGES] DOC OWNER IS NULL");
			return null;
		}
		SelectQuery sql = null;
		DataObject dataObject = null;
		sql = new SelectQueryImpl(new Table("FragmentSheets"));
		sql.addSelectColumn(new Column(null, "*"));
		Criteria cri = new Criteria(new Column("FragmentSheets", "DOCUMENT_ID"), documentId, QueryConstants.EQUAL);
		sql.setCriteria(cri);
		dataObject = persistence.get("FragmentSheets", cri);
		return dataObject;
	}

	/**
	 * @param dataObj
	 * @param sheetName
	 * @return
	 * @throws Exception This method is used for getting sheet id from data object.
	 */
	public static long getFragmentId(DataObject dataObj, String sheetName) throws Exception
	{
		Criteria cri = new Criteria(new Column("FragmentSheets", "SHEET_NAME"), sheetName, QueryConstants.EQUAL);
		com.adventnet.persistence.Row row = dataObj.getRow("FragmentSheets", cri); // No I18N
		if(row == null)
		{
			return -1;
		}
		return (Long) row.get("FRAGMENTSHEET_ID");
	}

	/**
	 * @param docOwner
	 * @param documentId
	 * @param sheetName
	 * @return
	 * @throws Exception
	 */
	public static long getFragmentId(String docOwner, long documentId, String sheetName) throws Exception
	{
		DataObject dataObj = EngineUtils.getFragmentDataObj(docOwner, documentId);
		return EngineUtils.getFragmentId(dataObj, sheetName);
	}

	/**
	 * @param dataObject
	 * @return
	 * @throws Exception
	 */
	public static Long[] getAllFragmentIDS(DataObject dataObject) throws Exception
	{
		long sheetId = -1;
		ArrayList<Long> sheetList = new ArrayList<>();
		if(!dataObject.isEmpty() && dataObject.containsTable("FragmentSheets"))
		{
			Iterator itr = dataObject.getRows("FragmentSheets");
			while(itr.hasNext())
			{
				com.adventnet.persistence.Row row = (com.adventnet.persistence.Row) itr.next();
				sheetId = (Long) row.get("FRAGMENTSHEET_ID");
				sheetList.add(sheetId);
			}
		}
		if(!sheetList.isEmpty())
		{
			Long[] sheetIds = new Long[sheetList.size()];
			sheetList.toArray(sheetIds);
			return sheetIds;
		}
		else
		{
			return null;
		}
	}

	/**
	 * @param documentId
	 * @param docOwner
	 * @return
	 * @throws Exception
	 */
	public static Long[] getAllFragmentIDS(String docOwner, long documentId) throws Exception
	{
		DataObject dataObj = EngineUtils.getFragmentDataObj(docOwner, documentId);
		return getAllFragmentIDS(dataObj);

	}

	/**
	 * @param docOwner
	 * @param docId
	 * @param docSheetName
	 * @param authorZuid
	 * @param insertIfNotFound
	 * @return
	 * @throws Exception
	 */
	public static long getFragmentID(String docOwner, long docId, String docSheetName, Long authorZuid, boolean insertIfNotFound) throws Exception
	{
//    		logger.info("*****docId .." + docId + " {0} ..docSheetName " + docSheetName + ".. docOwner " + docOwner + " ... insertIfNotFound " + insertIfNotFound);
		if(docSheetName == null)
		{
			return -1;
		}
		Persistence persistence = null;
		if(docOwner != null)
		{
			persistence = SheetPersistenceUtils.getPersistence(docOwner);
		}
		else
		{
			persistence = SheetPersistenceUtils.getPersistence();
		}
		//String autherZuid = Long.toString(DocumentUtils.getZUID(docOwner));
		SelectQuery sql = null;
		long docSheetID = -1;
		DataObject dataObject = null;
		// sql = new SelectQueryImpl(new Table("DocumentSheets"));
		//sql = new SelectQueryImpl(new Table("Sheets"));
		sql = new SelectQueryImpl(new Table("FragmentSheets"));
		sql.addSelectColumn(new Column(null, "*"));
		Criteria cri = new Criteria(new Column("FragmentSheets", "SHEET_NAME"), docSheetName, QueryConstants.EQUAL);
		sql.setCriteria(cri.and(new Criteria(new Column("FragmentSheets", "DOCUMENT_ID"), docId, QueryConstants.EQUAL)));

		dataObject = persistence.get(sql);
		if(!dataObject.isEmpty() && dataObject.containsTable("FragmentSheets"))
		{
			// docSheetID =
			// (Long)dataObject.getFirstValue("Sheets","FRAGMENTSHEET_ID");
			docSheetID = (Long) dataObject.getFirstValue("FragmentSheets", "FRAGMENTSHEET_ID"); // No I18N
		}
		else if(insertIfNotFound)
		{
			dataObject = persistence.constructDataObject();
			com.adventnet.persistence.Row row = new com.adventnet.persistence.Row("FragmentSheets"); // No I18N
			row.set("DOCUMENT_ID", docId);// No I18N
			row.set("SHEET_NAME", docSheetName);  // No I18N
			//ZUID_Persistence_Changes
			long ukId = StoreUtil.getUniqueKeyId(docOwner, authorZuid);
			row.set("UNIQUE_KEY_ID", ukId);  // No I18N
			dataObject.addRow(row);
			persistence.add(dataObject);
			docSheetID = (Long) dataObject.getFirstRow("FragmentSheets").get("FRAGMENTSHEET_ID");// No I18N
		}
//    		logger.log(Level.INFO, "docSheetID... {0}...", docSheetID);
		return docSheetID;
	}

	/**
	 * @param documentId
	 * @param docOwner
	 * @param versionNo
	 * @return
	 * @throws Exception
	 */
	public static long getVersionId(String docOwner, long documentId, String versionNo) throws Exception
	{
		SelectQueryImpl sql = new SelectQueryImpl(new Table("DocumentVersion"));
		sql.addSelectColumn(new Column("DocumentVersion", "*"));
		Criteria cri = new Criteria(new Column("DocumentVersion", "DOCUMENT_ID"), documentId, QueryConstants.EQUAL);
		if(versionNo != null)
		{
			cri = cri.and(new Criteria(new Column("DocumentVersion", "VERSION"), new Double(versionNo), QueryConstants.EQUAL));
		}
		SortColumn sortColumn = new SortColumn("DocumentVersion", "VERSION", false); // No I18N
		sql.addSortColumn(sortColumn);
		sql.setCriteria(cri);
		Persistence persistence = null;
		if(docOwner != null)
		{
			persistence = SheetPersistenceUtils.getPersistence(docOwner);
		}
		else
		{
			persistence = SheetPersistenceUtils.getPersistence();
		}
		DataObject data = persistence.get(sql);
		Iterator iter = data.getRows("DocumentVersion");
		if(!iter.hasNext())
		{
			return -1;
		}
		com.adventnet.persistence.Row rw = (com.adventnet.persistence.Row) iter.next();
		long versionId = -1;
		if((Long) rw.get("VERSION_ID") != null)
		{
			versionId = (Long) rw.get("VERSION_ID");
		}

    		/*Double d = null;
    		if (versionNo == null) {
    			d = (Double) rw.get("VERSION");
    		} else {
    			d = Double.valueOf((Long) rw.get("VERSION_ID"));
    		}*/

		return versionId;
	}

	/**
	 * @param docOwner
	 * @param documentId Following code will add unique key in Documents table if it is
	 *                   not present. This code will not be used after DFS migration
	 *                   ~Mani
	 * @param authorZuid
	 */
	public static void updateDocumentsTable(String docOwner, long documentId, long authorZuid)
	{
		Persistence persistence = null;
		String tableName = "Documents"; //No I18N
		String columnName = "DOCUMENT_ID"; //No I18N
		try
		{
			if(docOwner != null)
			{
				persistence = SheetPersistenceUtils.getPersistence(docOwner);
			}
			else
			{
				persistence = SheetPersistenceUtils.getPersistence();
			}
			//String authorZuid = DocumentUtils.getPersistanceZuid(docOwner);
			SelectQuery sql = null;
			sql = new SelectQueryImpl(new Table(tableName));
			sql.addSelectColumn(new Column(null, "*"));
			Criteria cri = new Criteria(new Column(tableName, columnName), documentId, QueryConstants.EQUAL);
			sql.setCriteria(cri);
			DataObject dataObject = persistence.get(sql);
			if(!dataObject.isEmpty())
			{
				Long uniqueId = (Long) dataObject.getFirstValue(tableName, "UNIQUE_KEY_ID"); //No I18N
				if(uniqueId == null)
				{
					//ZUID_Persistence_Changes
					uniqueId = StoreUtil.getUniqueKeyId(docOwner, authorZuid);
					UpdateQuery uq = new UpdateQueryImpl(tableName);
					uq.setUpdateColumn("UNIQUE_KEY_ID", uniqueId); //No I18N
					cri = new Criteria(new Column(tableName, columnName), documentId, QueryConstants.EQUAL);
					uq.setCriteria(cri);
					persistence.update(uq);
				}
			}
		}
		catch(Exception e)
		{
			//e.printStackTrace();
			LOGGER.log(Level.WARNING, "Error occured while update Document table ", e);
		}
	}

//        public static List<String> getExecutedActionsBelow(String resKey, int lastActionIdInclusive) throws Exception {
//            List<String> xListStrs = RedisHelper.lrange(RedisHelper.EXECUTED_ACTIONS_LIST + resKey, 0, -1);
//            List<String> xListTrimmed = xListStrs.stream()
////                    .map(actionStr -> new JSONObjectWrapper(actionStr))
//                    .filter(actionJsonStr -> {
//                        JSONObjectWrapper actionJson = new JSONObjectWrapper(actionJsonStr);
//                        int actionId = actionJson.getInt(JSONConstants.ACTION_ID);
//                        return actionId <= lastActionIdInclusive;
//                    })
//                    .collect(Collectors.toList());
//            return xListTrimmed;
//        }

	public static long getUserDataId(long ownerZUID, String authorName, boolean insertIfNotFound,String fileName)
	{
		long userDataId = -1;
		try
		{
			Persistence pers = SheetPersistenceUtils.getPersistence(authorName);
			Criteria cri = new Criteria(new Column("UserData", "FILE_NAME"), fileName, QueryConstants.EQUAL);
			cri = cri.and(new Criteria(new Column("UserData", "OWNER_ZUID"), ownerZUID, QueryConstants.EQUAL));
			DataObject dobj = pers.get("UserData", cri);
			if(dobj.isEmpty() && insertIfNotFound)
			{

				dobj = pers.constructDataObject();
				com.adventnet.persistence.Row row = new com.adventnet.persistence.Row("UserData"); //NO I18N
				row.set("OWNER_ZUID", ownerZUID);//NO I18N
				row.set("FILE_NAME", fileName);//NO I18N
				long uniqueId = StoreUtil.getUniqueKeyId(authorName, ownerZUID);
				row.set("UNIQUE_KEY_ID", uniqueId);//NO I18N
				dobj.addRow(row);
				dobj = pers.add(dobj);
			}
			if(dobj.containsTable("UserData"))
			{
				userDataId = (Long) dobj.getFirstValue("UserData", "USERDATA_ID");//NO I18N
			}


		}
		catch(Exception e)
		{
			LOGGER.log(Level.INFO, "error ", e);//No I18N
			//e.printStackTrace();
		}
		return userDataId;
	}

	private static String getToday()
	{
		return (new SimpleDateFormat("yyyyMMdd").format(Calendar.getInstance().getTime()));//No I18N
	}

	public static double getInchValue(String value)
	{
		double val;
		if(value.contains("inch"))
		{
			value = value.replace("inch", "");            //No I18N
			val = Double.parseDouble(value);
		}
		else if(value.contains("cm"))
		{
			value = value.replace("cm", "");//No I18N
			val = Double.parseDouble(value) / 2.54f;  // 1 cm = 2.54 inch
		}
		else if(value.contains("in"))
		{
			value = value.replaceFirst("in", "");//No I18N
			val = Double.parseDouble(value);
		}
		else if(value.contains("pt"))
		{   //Libreoffice Imports border thickness calculates as "pt" //No I18N
			value = value.replaceFirst("pt", "");//No I18N
			val = Double.parseDouble(value) / 50.8f;  // Checked with content.xml the boder size difference is (20 * 2.54f)
		}
		else
		{
			val = Double.parseDouble(value);
		}

		return Value.roundTo15DP(val);
	}

	public static List<JSONObjectWrapper> toActionJsonsList(List actionStringsList)
	{
		List<JSONObjectWrapper> actionJsonsList = new ArrayList<>();
		for(Object actionStrObj : actionStringsList)
		{
			String actionStr = String.valueOf(actionStrObj);
			JSONObjectWrapper actionJson = new JSONObjectWrapper(actionStr);
			actionJsonsList.add(actionJson);
		}

		return actionJsonsList;
	}

	public static IntegralMap<JSONObjectWrapper> toSortedMap(List<JSONObjectWrapper> actionsList)
	{
		//Can use our own IntegralMap. but avoiding since it is in beta
		IntegralMap<JSONObjectWrapper> actionsMap = new IntegralMap<>();
		for(JSONObjectWrapper action : actionsList)
		{
			int actionID = action.getInt(JSONConstants.ACTION_ID);
			actionsMap.put(actionID, action, 1);
		}
		return actionsMap;
	}

	public static HtmlStyle toHtmlStyle(CellStyle cellStyle, Workbook workbook)
	{
		return new HtmlStyle(cellStyle.getStyleName(), cellStyle.getPropertyAsString_deep(CellStyle.Property.BACKGROUNDCOLOR, workbook), cellStyle.getPropertyAsString_deep(TextStyle.Property.COLOR, workbook), cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTNAME, workbook), cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTSIZE, workbook), cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTSTYLE, workbook), cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTWEIGHT, workbook), cellStyle.getPropertyAsString_deep(ParagraphStyle.Property.TEXTALIGN, workbook), cellStyle.getPropertyAsString_deep(CellStyle.Property.VERTICALALIGN, workbook), cellStyle.getPropertyAsString_deep(TextStyle.Property.TEXTUNDERLINESTYLE, workbook), cellStyle.getPropertyAsString_deep(CellStyle.Property.DISPLAYTYPE, workbook), cellStyle.getPropertyAsString_deep(CellStyle.Property.ROTATIONANGLE, workbook), HtmlBorder.getInstance(
				null,
				cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, workbook), cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERTOP, workbook),
				cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERRIGHT, workbook), cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERBOTTOM, workbook)
		), null, new ArrayList(), cellStyle.getPropertyAsString_deep(TextStyle.Property.TEXTLINETHROUGHSTYLE, workbook));
	}

	public static WorkbookContainer creatNewWorkbookContainerFromRSID(String rID)
	{
		try
		{
			String ownerZuid = ZohoFS.getSpaceId(rID);
			String ownerName = DocumentUtils.getZUserName(ownerZuid);
			return new WorkbookContainer(rID, ownerName);
		}
		catch(Exception e)
		{
			WorkbookContainer.logger.log(Level.WARNING, "can't create WorkbookContainer from resource-id >> {0}", rID);//No I18N
		}
		return null;
	}


	public static JSONObjectWrapper readUserCustomFonts(long ownerZUID, String docOwner) {
		BufferedReader br = null;
		Store store = null;
		JSONObjectWrapper jObj = null;
		InputStream is = null;
		try
		{
			long userdataId = getUserDataId(ownerZUID, docOwner, false,EngineConstants.FILENAME_USERINFO);
			if(userdataId > 0)
			{
				store = StoreFactory.getInstance().getUserStore(ownerZUID, docOwner, userdataId);
				SheetFileInfo info = store.getFileInfo(userdataId, EngineConstants.FILENAME_USERINFO, EngineConstants.FILEEXTN_PROPS);
				if(info != null)
				{
					is = store.read(userdataId, EngineConstants.FILEEXTN_PROPS);
					if(is != null)
					{
						br = new BufferedReader(new InputStreamReader(is));
						String str = null;
						while((str = br.readLine()) != null)
						{
							jObj = new JSONObjectWrapper(str);
						}
					}
				}
			}
			if(jObj == null)
			{
				jObj = new JSONObjectWrapper();
				jObj.put("USERLEVELCUSTOMFONTS", new ArrayList()); // No I18N
			} else if (!jObj.has("USERLEVELCUSTOMFONTS")) { // No I18N
				jObj.put("USERLEVELCUSTOMFONTS", new ArrayList()); // No I18N
			}


		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, null, e);
		}
		finally
		{
			if(is != null)
			{
				try
				{
					is.close();
				}
				catch(IOException e)
				{
					LOGGER.log(Level.WARNING, null, e);
				}
			}
		}
		return jObj;
	}
}
