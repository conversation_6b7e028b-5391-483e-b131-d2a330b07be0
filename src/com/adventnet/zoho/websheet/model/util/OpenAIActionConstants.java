/* $Id$ */
/*
 * OpenAIActionConstants.java
 */
package com.adventnet.zoho.websheet.model.util;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

import java.lang.reflect.Field;

import java.util.HashMap;
import java.util.logging.Logger;
import java.util.logging.Level;
/**
 *
 * <AUTHOR>
 */
public class OpenAIActionConstants
{
    private static final Logger LOGGER = Logger.getLogger(OpenAIActionConstants.class.getName());

    public static final int FETCH_USER_DETAILS = 1;
    public static final int TOGGLE_FEATURE = 2;
    public static final int ADD_TOKEN = 3;
    public static final int API_KEY_REMOVE = 4;
    public static final int ADMIN_DETAILS = 5;
    public static final int CONTINUE_INTEG = 6;

    public static final int GPT_GENERAL_QUERY = 7;
    public static final int GPT_GENERATE_FORMULA = 8;
    public static final int GPT_EXPLAIN_FORMULA = 9;
    public static final int GPT_GENERATE_VBA = 10;
    public static final int GPT_EXPLAIN_VBA = 11;
    public static final int GPT_TABLE = 12;
    public static final int GPT_EXTRACT_FILE = 13;

    private static String openAIActionConstantsJsonStr = null;
    static {
        JSONObjectWrapper openAIActionConstantsJson = new JSONObjectWrapper();
        Field[] fields = OpenAIActionConstants.class.getFields();
        //Field f = null;
        Object fieldValueObject = null;
        for (int i = 0; i < fields.length; i++) {
            try {
                fieldValueObject = fields[i].get(OpenAIActionConstants.class);
                if (fieldValueObject instanceof Integer) {
                    openAIActionConstantsJson.put(fields[i].getName(), (Integer) fieldValueObject);
                }
            } catch (IllegalArgumentException ex) {
                LOGGER.log(Level.WARNING, null, ex);
            } catch (IllegalAccessException ex) {
                LOGGER.log(Level.WARNING, null, ex);
            }
        }
        openAIActionConstantsJsonStr = openAIActionConstantsJson.toString();
    }

    public static String getOpenAIActionConstantsJson() {
        return openAIActionConstantsJsonStr;
    }

}
