// $Id$
/*
 * CellUtil.java
 *
 * Created on April 12, 2007, 3:20 PM
 */
package com.adventnet.zoho.websheet.model.util;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.ext.parser.ASTArrayNode;
import com.adventnet.zoho.websheet.model.ext.parser.ASTRangeNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSPrintVisitor;
import com.adventnet.zoho.websheet.model.response.helper.TimeCapsule;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Variable;
import com.singularsys.jep.parser.ASTConstant;
import com.singularsys.jep.parser.ASTVarNode;
import com.singularsys.jep.parser.JccParserTreeConstants;
import com.singularsys.jep.parser.Node;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ClientUtils.FormatProps;

import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 */
public class CellUtil {

	public static final Logger LOGGER = Logger.getLogger(CellUtil.class.getName());
	// private static final char sheetInd = '!';
	private static final char SHEETIND = '.';

	/**
	 * Creates a new instance of CellUtil
	 */
	public CellUtil() {
	}

	// Pick and Claculate the column index from location like AB23
	public static int getColumn(String s) {
		return getColumn(s, false);
	}

	public static int getColumn(String s, boolean isFromParser) {
		int colnum = 0;
		try {
			int numindex = getNumberIndex(s);
			String s2 = s.toUpperCase();

			int startPos = s.lastIndexOf(SHEETIND) + 1;
			if (s.charAt(startPos) == '$') {
				startPos++;
			}
			int endPos = numindex;
			if (s.charAt(numindex - 1) == '$') {
				endPos--;
			}

			if (startPos >= endPos) {
				return -1;
			}

			for (int i = startPos; i < endPos; i++) {

				if (i != startPos) {
					colnum = (colnum + 1) * 26;
				}
                                char c = s2.charAt(i);
                                if(!(c >= 'A' && c <= 'Z')) {
                                    return -1;
                                }
				colnum += (int) c - (int) 'A';
			}

		} catch (StringIndexOutOfBoundsException ex) {
			// ex.printStackTrace();
			return -1;
		}

		// if (isFromParser && colnum == Utility.LIBREOFFICEMAXNUMOFCOLS - 1) {
		// return Utility.MAXNUMOFCOLS - 1;
		// }

		if (colnum > Utility.MAXNUMOFCOLS - 1) {
			if (isFromParser && colnum < Utility.LIBREOFFICEMAXNUMOFCOLS) { //Excel and LibreOffice support 16384 columns
				return Utility.MAXNUMOFCOLS - 1;
			}
			return -1;
		}
		return colnum;

	}

	// Get the row number from Cell location like AC224
	public static int getRow(String s) {
		return getRow(s, false);
	}

	public static int getRow(String s, boolean isFromParser) {
		try {
			int row = (Integer.parseInt(s.substring(getNumberIndex(s))) - 1);

			// if (isFromParser && row == Utility.LIBREOFFICEMAXNUMOFROWS - 1) {
			// return Utility.MAXNUMOFROWS - 1;
			// }

			if (row > Utility.MAXNUMOFROWS - 1) {
				if (isFromParser) {
					return Utility.MAXNUMOFROWS - 1;
				}
				return -1;
			}
			return row;
		} catch (NumberFormatException e) {
			// e.printStackTrace();
			// return 0xffff;
			return -1;
		}

	}

	public static Ref getRefFromR1C1ReferenceString(String referenceString) throws EvaluationException {
		String sheetName = null;
		RowRef rowRef = null;
		ColRef colRef = null;

		if (referenceString.contains(".")) {
			if (referenceString.contains("'")) {
				int indexOf = referenceString.lastIndexOf("'");
				sheetName = referenceString.substring(referenceString.indexOf("'") + 1, indexOf).replaceAll("''", "'");
			} else {
				sheetName = referenceString.split("\\.")[0].replace("$", "");
			}
		}

		String referenceStringSheetNameTrimmed = referenceString.substring(referenceString.lastIndexOf(".") + 1);
		referenceStringSheetNameTrimmed = referenceStringSheetNameTrimmed.toUpperCase();

		int indexR = referenceStringSheetNameTrimmed.indexOf('R');
		int indexC = referenceStringSheetNameTrimmed.indexOf('C');

		if (sheetName == null && indexR == -1 && indexC == -1) {
			throw Cell.Error.NAME.getThrowableObject();
		}

		// eg. "C5R2" , "" and "xyzR1C4"
		if ((indexC != -1 && indexC < indexR) || (indexR > 0)) {
			throw Cell.Error.NAME.getThrowableObject();
		}

		try {
			if (indexR != -1) {
				String rowRefStr = referenceStringSheetNameTrimmed.substring(indexR + 1,
						indexC == -1 ? referenceStringSheetNameTrimmed.length() : indexC);
				LinearRef tempRowRef = getLinearRefFromRefIndexString(rowRefStr);
				rowRef = new RowRef(tempRowRef.getIndex(), tempRowRef.isRelative());
			}

			if (indexC != -1) {
				String colRefStr = referenceStringSheetNameTrimmed.substring(indexC + 1);
				LinearRef tempColRef = getLinearRefFromRefIndexString(colRefStr);
				colRef = new ColRef(tempColRef.getIndex(), tempColRef.isRelative());
			}

			return new Ref(sheetName, rowRef, colRef);
		} catch (NumberFormatException e) {
			throw Cell.Error.NAME.getThrowableObject();
		}
	}

	private static LinearRef getLinearRefFromRefIndexString(String refStr) throws NumberFormatException {
		boolean isRelative;
		if ((refStr.startsWith("[")) && (refStr.endsWith("]"))) {
			isRelative = true;
			refStr = refStr.substring(1, refStr.length() - 1);
		} else {
			isRelative = "".equals(refStr);
		}

		int index;
		if ("".equals(refStr)) {
			index = 0;
		} else {
			index = Integer.parseInt(refStr);
		}
		index -= isRelative ? 0 : 1;

		return new LinearRef(index, isRelative);
	}

	// Get sheetname from range string like Sheet1.A1:A10, $Sheet1.$A1:$A10
	public static String getSheetName(String range) {
		String sheetName = "";
		try {
			range = range.replaceAll("\\$", "");
			String[] rangeSplit = range.split("\\.");
			if (rangeSplit.length > 1) {
				sheetName = rangeSplit[0];
			}
		} catch (Exception e) {
			sheetName = "";
		}
		return sheetName;
	}

	/**
	 * 
	 * @param rangeString
	 *            : apostrophe inside sheet-name should be in pair, sheet name
	 *            with special-characters should be enclosed
	 * 
	 * @return 'not-enclosed sheet-name' and 'rangeString-ref/cell-ref' e.g:
	 *         $'234@#'.A1:$'234@#'.A2 --> {234@#,A1.A2}
	 * @throws com.adventnet.zoho.websheet.model.exception.SheetEngineException
	 */
	public static String[] getSheetNameAndRef(String rangeString) throws SheetEngineException {

		if (rangeString == null) {
			throw new SheetEngineException("rangeString cannot be null"); // No  I18N
																			
		}

		String sheetName = null;
		String rangeRef = null;

		try {

			String[] references = rangeString.split(":");
			for (String reference : references) {
				rangeRef = rangeRef == null ? "" : (rangeRef += ":");

				int dotIndex = reference.lastIndexOf(".");
				if (dotIndex != -1) {
					String sheetNameProspect = reference.substring(0, dotIndex).trim();
					if (sheetNameProspect.startsWith("$")) {
						sheetNameProspect = sheetNameProspect.replaceFirst("[$]", "").trim();
					}

					int len = sheetNameProspect.length();
					if (sheetNameProspect.length() > 1 && sheetNameProspect.startsWith("'")
							&& sheetNameProspect.endsWith("'")) {
						sheetNameProspect = sheetNameProspect.substring(1, sheetNameProspect.length() - 1);

						if (!sheetNameProspect.matches("(([']{2})*[^']*)*")) {
							throw new SheetEngineException("Cannot create range from rangeString >>> " + rangeString); // No I18N
																														
						}
					} else if (!sheetNameProspect.matches("[A-Za-z0-9_ ]*")) {
						throw new SheetEngineException("SheetName should be enclosed with apostrophe " + rangeString); // No I18N
																														 
					}

					sheetNameProspect = sheetNameProspect.replace("''", "'");

					if (!sheetNameProspect.isEmpty()) {
						if (sheetName == null) {
							sheetName = sheetNameProspect;
						} else if (!sheetName.equals(sheetNameProspect)) {

							throw new SheetEngineException("Expecting Same Sheet-Name in Reference " + rangeString); // No I18N
																																				}
					}

					rangeRef += reference.substring(dotIndex + 1);
				} else {
					rangeRef += reference;
				}
			}

			rangeRef = rangeRef.replaceAll("[$ ]", "");

		} catch (SheetEngineException e) {
			throw e;
		} catch (Exception e) {
			throw new SheetEngineException("Cannot create range from rangeString >>> " + rangeString, e); // No I18N
																											
		}

		return new String[] { sheetName, rangeRef };
	}

	// Construct and get the Cell refernece/location in string format
	// from the given row and column
	public static String getCellReference(int column, int row) {

		StringBuilder buf = new StringBuilder();
		buf.append(getColumnReference(column));
		buf.append(String.valueOf(row + 1));
		// logger.log(Level.INFO, buf+" :: Col : "+column+" : Row : "+row);
		return buf.toString();
	}

	public static String getCellReference(Sheet sheet, int row, int column, boolean isSheetAbsolute,
			boolean isRowAbsolute, boolean isColumnAbsolute) {
		return getCellReference(sheet.getName(), row, column, isSheetAbsolute, isRowAbsolute, isColumnAbsolute);
	}

	public static String getCellReference(String sheetName, int row, int column, boolean isSheetAbsolute,
			boolean isRowAbsolute, boolean isColumnAbsolute) {
		StringBuilder cellRef = new StringBuilder();

		if (isSheetAbsolute) {
			cellRef.append("$");

			sheetName = sheetName.replace("'", "''");// for XlsxParser sheet
														// name can be A'B.A1
														// but it's reference
														// should be 'A''B'.A1
			sheetName = getSheetNameEnclosed(sheetName);

			cellRef.append(sheetName);
			cellRef.append(".");
		}

		return cellRef.append(CellUtil.getCellReference(row, column, isRowAbsolute, isColumnAbsolute)).toString();
	}

	// Get A1 cell reference with respect to row and column absolute reference
	public static String getCellReference(int row, int column, boolean isRowAsolute, boolean isColumnAbsolute) {
		StringBuilder r1c1Ref = new StringBuilder();
		if (column != -1) {
			if (isColumnAbsolute) {
				r1c1Ref.append("$");
			}
			r1c1Ref.append(CellUtil.getColumnReference(column));
		}
		if (row != -1) {
			if (isRowAsolute) {
				r1c1Ref.append("$");
			}
			r1c1Ref.append(row + 1);
		}
		return r1c1Ref.toString();
	}

	// Get R1C1 CellReference with respective to relative row and column
	public static String getR1C1CellReference(int row, int column, boolean isRowAbsolute, boolean isColumnAbsolute,
			int relativeRowOffset, int relativeColumnOffset) {
		StringBuilder r1c1Ref = new StringBuilder();
		if (row != -1) {
			r1c1Ref.append("R");
			if (isRowAbsolute) {
				r1c1Ref.append(row + 1);
			} else {
				int relativeRowIndex = row - relativeRowOffset;
				if (relativeRowIndex != 0) {
					r1c1Ref.append("[");
					r1c1Ref.append(relativeRowIndex);
					r1c1Ref.append("]");
				}
			}
		}
		if (column != -1) {
			r1c1Ref.append("C");
			if (isColumnAbsolute) {
				r1c1Ref.append(column + 1);
			} else {
				int relativeColIndex = column - relativeColumnOffset;
				if (relativeColIndex != 0) {
					r1c1Ref.append("[");
					r1c1Ref.append(relativeColIndex);
					r1c1Ref.append("]");
				}
			}
		}
		return r1c1Ref.toString();
	}

	public static String getColumnReference(int columnIndex) {
		columnIndex++;
		String letter = "";
		while (columnIndex > 0) {
			int temp = (columnIndex - 1) % 26;
			letter = ((char)(65+temp)) + letter;
			columnIndex = (columnIndex - temp - 1) / 26;
		}
		return letter;
	}

	/*
	 * While removing or inserting columns or removing or insering rows to chnge
	 * the references in formula this method will be used.
	 *
	 *
	 */
	public static String getColumnReferenceForFormula(int column) {
		// If column number is < 1 return REF error.
		if (column < 0) {
			return CellUtil.getErrorString(Error.REF);
		}

		return getColumnReference(column);
	}

	/*
	 * This is to use while remove row and insert row. If rowIndex is less tha 0
	 * should get Ref error.
	 *
	 * while changin the reference in formula, while removing or insering rows
	 * getting the columnreference will take care of returning REF error if
	 * columnIndex < 0 that handling was not there for row. This method is to
	 * serve that purpose.
	 *
	 */
	public static String getRowReferenceForFormula(int rowIndex) {
		if (rowIndex < 0) {
			return CellUtil.getErrorString(Error.REF);
		} else {
			return String.valueOf(rowIndex + 1);
		}
	}

	/**
	 * Sees if the column component is relative or not
	 *
	 * @param s
	 *            the cell
	 * @return TRUE if the column is relative, FALSE otherwise
	 */
	public static boolean isColumnRelative(String s) {
		// return (s.charAt(0) != '$');

		int startPos = s.lastIndexOf(SHEETIND) + 1;
		return (s.charAt(startPos) != '$');
	}

	/**
	 * Sees if the row component is relative or not
	 *
	 * @param s
	 *            the cell
	 * @return TRUE if the row is relative, FALSE otherwise
	 */
	public static boolean isRowRelative(String s) {
		return (s.charAt(getNumberIndex(s) - 1) != '$');
	}

	// Finds the position where the first number occurs in the string
	public static int getNumberIndex(String s) {
		// Find the position of the first number
		boolean numberFound = false;
		int pos = s.lastIndexOf(SHEETIND) + 1;
		// char c = '\0';

		while (!numberFound && pos < s.length()) {
			char c = s.charAt(pos);

			if (c >= '0' && c <= '9') {
				numberFound = true;
			} else {
				pos++;
			}
		}

        return pos;
    }
    
 // Finds the position where the last number occurs in the string
    public static int getNumberIndexFromLast(String s) {
        // Find the position of the first number
        boolean numberFound = false;
        //int pos = s.length()-1;
        int pos = s.lastIndexOf("_") + 1;
        //char c = '\0';

        while (!numberFound && pos > 0) {
            char c = s.charAt(pos);

            if (c >= '0' && c <= '9') {
                numberFound = true;
            } else {
                pos++;
            }
        }

        return pos;
    }

    public static boolean isRowRangeBound(int inRowIndex) {
        return !(inRowIndex > Utility.MAXNUMOFROWS - 1 || inRowIndex < 0);
    }

	public static boolean isColumnRangeBound(int inColIndex) {
		return !(inColIndex > Utility.MAXNUMOFCOLS - 1 || inColIndex < 0);
	}

	public static boolean isCellRangeBound(String cellRef) {
		int row = getRow(cellRef);
		if (row > Utility.MAXNUMOFROWS - 1 || row < 0) {
			return false;
		}

		int col = getColumn(cellRef);
		return !(col > Utility.MAXNUMOFCOLS - 1 || col < 0);
	}

	public static String getErrorString(Cell.Error error) 
        {
            if(error == null)
            {
                error = Error.UNKNOWN_ERROR;
            }

            return error.getErrorString();
	}

	// This method will return the undelying object of this VarNode in Sheet
	// and the return type can be namedrange ie. Range or Cell object
	// This method is changed to return a Cell if the variable Node refers a
	// cell.
	// Otherwise returns null.
	// public static Cell[] getCells(ASTVarNode node, Sheet sheet) throws
	// EvaluationException
	public static Cell getCellFromVarNode(ASTVarNode node, Sheet sheet, int currRow, int currCol)
			throws EvaluationException {
		return getCellRefFromVarNode(node, sheet, currRow, currCol).getCell();
	}

	public static CellReference getCellRefFromVarNode(ASTVarNode node, Sheet sheet, int currRow, int currCol)
			throws EvaluationException {
		Variable var = node.getVar();
		if (!var.isConstant()) {
			String varName = var.getName();
			// named ranges will come here so need to handle here
			NamedExpression namedExpression = sheet.getWorkbook().getNamedExpression(varName);
			if (namedExpression == null) {
				return node.getVarCellRef(sheet, currRow, currCol);
			}
		}
		LOGGER.log(Level.INFO, "Cell reference is beyond sheet size");
		throw Cell.Error.REF.getThrowableObject();
	}

	public static boolean isCellReferenceNode(Node node) {
		if (node instanceof ASTVarNode) {
			try {
				((ASTVarNode) node).getVarRowIndex(0);
				((ASTVarNode) node).getVarColIndex(0);
				return true;
			} catch (EvaluationException e) {
			}
		}
		return false;
	}

	public static boolean isRangeReferenceNode(Node node) {
		if (node instanceof ASTRangeNode) {
			ASTRangeNode astRangeNode = (ASTRangeNode) node;
			return astRangeNode.jjtGetNumChildren() == 2 && isCellReferenceNode(astRangeNode.jjtGetChild(0))
					&& isCellReferenceNode(astRangeNode.jjtGetChild(1));
		}
		return false;
	}

	public static ZArray getArray(ASTArrayNode node) throws EvaluationException {
		int rowSize = node.getRowDimension();
		int colSize = node.getColDimension();
		List array = node.getArray();
		ZArray zArray = new ZArray(array, rowSize, colSize);
		return zArray;
	}

	public static Range getRange(ASTRangeNode node, Sheet currSheet, int currRow, int currCol)
			throws EvaluationException {
		List<Integer> rowIndexes = new ArrayList<>();
		List<Integer> colIndexes = new ArrayList<>();
		Sheet rangeSheet = getRangeIndices(node, currSheet, currRow, currCol, rowIndexes, colIndexes);

		Collections.sort(rowIndexes);
		Collections.sort(colIndexes);
		Range range = new Range(rangeSheet == null ? currSheet : rangeSheet, rowIndexes.get(0), colIndexes.get(0),
				rowIndexes.get(rowIndexes.size() - 1), colIndexes.get(colIndexes.size() - 1));

		Range existingRange = range.getSheet().getRanges().get(range.getSimpleRangeAddress());
		range = (existingRange == null) ? range : existingRange;
		return range;
	}

	private static Sheet getRangeIndices(ASTRangeNode node, Sheet currSheet, int currRow, int currCol,
			List<Integer> rowIndexes, List<Integer> colIndexes) throws EvaluationException {
		int numOfChildren = node.jjtGetNumChildren();
		if (numOfChildren != 2) {
			// logger.log(Level.INFO, "Range corrupted");
			//throw Error.NAME.getThrowableObject();
                        throw Error.NAME.getThrowableObject();
		}

		Sheet rangeSheet = null;
		for (int c = 0; c < numOfChildren; c++) {
			Sheet varSheet = null;
			Node child = node.jjtGetChild(c);
			if (child instanceof ASTVarNode) {
				ASTVarNode varChild = (ASTVarNode) child;
				if (!varChild.isSheetRelative()) {
					varSheet = varChild.getVarSheet(currSheet);
				}
				rowIndexes.add(varChild.getVarRowIndex(currRow));
				colIndexes.add(varChild.getVarColIndex(currCol));
			} else if (child instanceof ASTRangeNode) {
				varSheet = CellUtil.getRangeIndices((ASTRangeNode) child, currSheet, currRow, currCol, rowIndexes,
						colIndexes);
			} else {
				throw Cell.Error.VALUE.getThrowableObject();
			}

			if (varSheet != null) {
				if (rangeSheet == null) {
					rangeSheet = varSheet;
				} else if (rangeSheet != varSheet) {
					LOGGER.log(Level.INFO, "Exception as start and end node of range has different sheet names.");
					throw Cell.Error.NAME.getThrowableObject();
				}
			}
		}

		return rangeSheet;
	}

	public static String getRangeString(Range range,  int currRow, int currCol, ZSPrintVisitor.FORMULA_CLIENT formula_client,
										boolean isR1C1Ref) {
		List<RowRef> rowRefs = new ArrayList<>();
		List<ColRef> colRefs = new ArrayList<>();
		CellReference topLeft = range.getTopLeft();
		CellReference bottomRight = range.getBottomRight();
		rowRefs.add(new RowRef(topLeft.getCell().getRowIndex(), topLeft.isRowRelative()));
		rowRefs.add(new RowRef(bottomRight.getCell().getRowIndex(), bottomRight.isRowRelative()));
		colRefs.add(new ColRef(topLeft.getCell().getColumnIndex(), topLeft.isColumnRelative()));
		colRefs.add(new ColRef(bottomRight.getCell().getColumnIndex(), bottomRight.isColumnRelative()));

		return getRangeString(range.getSheet().getName(), rowRefs, colRefs, formula_client, isR1C1Ref, currRow, currCol);
	}
	public static String getRangeString(ASTRangeNode node, Sheet currSheet, int currRow, int currCol, ZSPrintVisitor.FORMULA_CLIENT formula_client,
										boolean isR1C1Ref) {
		if (!node.isRangeValid()) {
			if (formula_client == ZSPrintVisitor.FORMULA_CLIENT.XLSX_WRITER) {
				return "#REF!";//No I18N
			}
			return getInvalidRangeString(node);
		}

		List<RowRef> rowRefs = new ArrayList<>();
		List<ColRef> colRefs = new ArrayList<>();
		String sheetName = getRangeNodeProperties(node, currSheet, currRow, currCol, rowRefs, colRefs);
		try {
			return getRangeString(sheetName, rowRefs, colRefs, formula_client, isR1C1Ref, currRow, currCol);
		} catch(IndexOutOfBoundsException e) {
			//some action corrupting the range node (of condition expression in Conditional Format), for that invalid range node rowRefs is empty causing IndexOutOfBoundsException
			//example =322701:914575:922482:89255
			//to properly fix the issue, need to identify why the expression got corrupted.
			LOGGER.log(Level.OFF, getInvalidRangeString(node), e);
			return "#REF!";//No I18N
		}
	}

	public static String getRangeString(ASTRangeNode node, Sheet currSheet, int currRow, int currCol, boolean isForXML,
			boolean isR1C1Ref) {
		return getRangeString(node, currSheet, currRow, currCol, isForXML ? ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER : ZSPrintVisitor.FORMULA_CLIENT.ZS_CLIENT, isR1C1Ref);
	}

	public static String getInvalidRangeString(Node node) {
		StringBuilder rangeBuilder = new StringBuilder();

		if (node instanceof ASTVarNode) {
			rangeBuilder.append(((ASTVarNode) node).getName());
		} else if (node instanceof ASTConstant) {
			String value = ((ASTConstant) node).getValue().toString();
			rangeBuilder.append(value.substring(0, value.indexOf("."))); // To
																			// trim
																			// 1.0.
		} else if (node instanceof ASTRangeNode) {
			for (int i = 0; i < node.jjtGetNumChildren(); i++) {
				if (i > 0) {
					rangeBuilder.append(":");
				}
				Node child = node.jjtGetChild(i);
				rangeBuilder.append(getInvalidRangeString(child));
			}
		}

		return rangeBuilder.toString();
	}

	private static String getRangeNodeProperties(ASTRangeNode node, Sheet currSheet, int currRow, int currCol,
			List<RowRef> rowRefs, List<ColRef> colRefs) {
		String sheetName = null;
		for (int c = 0; c < node.jjtGetNumChildren(); c++) {
			Node child = node.jjtGetChild(c);

			if (child instanceof ASTVarNode) {
				ASTVarNode varNode = (ASTVarNode) child;
				try {
					if (sheetName == null && !varNode.isSheetRelative()) {
						sheetName = varNode.getVarSheetName(currSheet);
					}
				} catch (EvaluationException e) {
					if (CellUtil.getErrorString(Error.REF).equals(e.getMessage())) {
						sheetName = CellUtil.getErrorString(Error.REF);
					}
				}

				int rowIndex = -1;
				int colIndex = -1;
				boolean isRowRelative = varNode.isRowRelative();
				boolean isColRelative = varNode.isColRelative();
				try {
					rowIndex = varNode.getVarRowIndexIgnoringBound(currRow);
				} catch (EvaluationException e) {
				}
				try {
					colIndex = varNode.getVarColIndexIgnoringBound(currCol);
				} catch (EvaluationException e) {
				}
				rowRefs.add(new RowRef(rowIndex, isRowRelative));
				colRefs.add(new ColRef(colIndex, isColRelative));
			} else if (child instanceof ASTRangeNode) {
				String sheetName_temp = getRangeNodeProperties((ASTRangeNode) child, currSheet, currRow, currCol, rowRefs, colRefs);
				if(sheetName == null) {
					sheetName = sheetName_temp;
				}
			}
		}

		return sheetName;
	}

	private static String getRangeString(String sheetName, List<RowRef> rowRefs, List<ColRef> colRefs, ZSPrintVisitor.FORMULA_CLIENT formula_client,
			boolean isR1C1Ref, int curRowIndex, int curColIndex) {
		Collections.sort(colRefs, new ColRefComparator());
		Collections.sort(rowRefs, new RowRefComparator());

		boolean isWholeCol = false;
		boolean isWholeRow = false;
		int tempStartRowI = rowRefs.get(0).getRowI();
		int tempEndRowI = rowRefs.get(rowRefs.size() - 1).getRowI();
		isWholeCol = tempStartRowI == 0 && tempEndRowI == Utility.MAXNUMOFROWS - 1;
		if (!isWholeCol) {
			int tempStartColI = colRefs.get(0).getColI();
			int tempEndColI = colRefs.get(colRefs.size() - 1).getColI();
			isWholeRow = tempStartColI == 0 && tempEndColI == Utility.MAXNUMOFCOLS - 1;
		}

		boolean resetRowI = (formula_client == ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER) && isWholeRow;
		boolean resetColI = (formula_client == ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER) && isWholeCol;
		boolean isRowLevel = (formula_client != ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER) && isWholeRow;
		boolean isColLevel = (formula_client != ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER) && isWholeCol;

		StringBuilder rangeBuilder = new StringBuilder();
		if (sheetName != null) {
			if ((formula_client == ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER)) {
				rangeBuilder.append("$");
			}
			if(formula_client == ZSPrintVisitor.FORMULA_CLIENT.XLSX_WRITER) {
				rangeBuilder.append("'").append(sheetName).append("'!");
			} else {
				rangeBuilder.append(getSheetNameEnclosed(sheetName)).append(".");
			}
		}

		for (int i = 0; i < rowRefs.size(); i += 2) {
			if (i > 0) {
				rangeBuilder.append(":");
			}

			int rowI1 = rowRefs.get(i).getRowI();
			rowI1 = (resetRowI && rowI1 == Utility.MAXNUMOFROWS - 1) ? Utility.LIBREOFFICEMAXNUMOFROWS - 1 : rowI1;
			int colI1 = colRefs.get(i).getColI();
			colI1 = (resetColI && colI1 == Utility.MAXNUMOFCOLS - 1) ? Utility.LIBREOFFICEMAXNUMOFCOLS - 1 : colI1;
			boolean isRowR1 = rowRefs.get(i).isRowR();
			boolean isColR1 = colRefs.get(i).isColR();

			if (i + 1 < rowRefs.size()) {
				int rowI2 = rowRefs.get(i + 1).getRowI();
				rowI2 = (resetRowI && rowI2 == Utility.MAXNUMOFROWS - 1) ? Utility.LIBREOFFICEMAXNUMOFROWS - 1 : rowI2;
				int colI2 = colRefs.get(i + 1).getColI();
				colI2 = (resetColI && colI2 == Utility.MAXNUMOFCOLS - 1) ? Utility.LIBREOFFICEMAXNUMOFCOLS - 1 : colI2;
				boolean isRowR2 = rowRefs.get(i + 1).isRowR();
				boolean isColR2 = colRefs.get(i + 1).isColR();

				if (isRowR1 != isRowR2 || isColR1 != isColR2) {
					isRowLevel = isColLevel = false;
				}
				String refBeforeColon = CellUtil.getCellReferenceForFormula(rowI1, colI1, curRowIndex, curColIndex,
						isRowLevel, isColLevel, isRowR1, isColR1, isR1C1Ref, (formula_client == ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER));

				boolean transformedIsRowLevel = isRowLevel;
				boolean transformedIsColLevel = isColLevel;
				if((formula_client == ZSPrintVisitor.FORMULA_CLIENT.ZS_CLIENT) && !isColLevel && !isRowLevel) {
					//for cases: =B4:B$65536 (becomes B4:B), =B4:$IV4 (becomes B4:4)
					transformedIsColLevel = !isRowR2 && Utility.MAXNUMOFROWS - 1 == rowI2;
					transformedIsRowLevel = !isColR2 && Utility.MAXNUMOFCOLS - 1 == colI2;

					if(transformedIsColLevel == true && transformedIsRowLevel == true) {
						//for cases: =B4:$IV$65536
						transformedIsRowLevel = false;
					}
				}

				String refAfterColon = CellUtil.getCellReferenceForFormula(rowI2, colI2, curRowIndex, curColIndex,
						transformedIsRowLevel, transformedIsColLevel, isRowR2, isColR2, isR1C1Ref, (formula_client == ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER));
				rangeBuilder.append(refBeforeColon);

				if (isR1C1Ref ? ((isRowLevel || isColLevel) ? !refBeforeColon.equals(refAfterColon) : true) : true) {
					rangeBuilder.append(":");
					rangeBuilder.append(refAfterColon);
				}
			} else {
				rangeBuilder.append(CellUtil.getCellReferenceForFormula(rowI1, colI1, curRowIndex, curColIndex,
						isRowLevel, isColLevel, isRowR1, isColR1, isR1C1Ref, (formula_client == ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER)));
			}
		}

		return rangeBuilder.toString();
	}

	private static String getCellReferenceForFormula(int rowI, int colI, int currRowIndex, int currColIndex,
													 boolean isRowLevel, boolean isColLevel, boolean isRowR, boolean isColR, boolean isR1C1Ref,
			boolean isFoxXml) {
		if (isR1C1Ref) {
			int rOffSet = rowI;
			int cOffSet = colI;

			if (isRowR) {
				rOffSet -= currRowIndex;
			}
			if (isColR) {
				cOffSet -= currColIndex;
			}

			return getR1C1CellReferenceForFormula(rOffSet, cOffSet, isRowLevel, isColLevel, isRowR, isColR, isFoxXml);
		}
		return getA1CellReferenceForFormula(rowI, colI, isRowLevel, isColLevel, isRowR, isColR);
	}

	public static String getCellReferenceForFormula(Node node, int currentRowIndex, int currentColIndex, Sheet sheet,
													boolean isR1C1, ZSPrintVisitor.FORMULA_CLIENT formula_client) {

		StringBuilder cellRefBuilder = new StringBuilder();
		ASTVarNode varNode = (ASTVarNode) node;

		if (!varNode.isVariableValid()) {
			if (formula_client == ZSPrintVisitor.FORMULA_CLIENT.XLSX_WRITER) {
				return "#REF!";//No I18N
			}
			return getInvalidRangeString(node);
		}
		int rI, cI;

		boolean isRowR = varNode.isRowRelative();
		boolean isColR = varNode.isColRelative();

		if (!varNode.isSheetRelative()) {
			try {
				if (formula_client == ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER) {
					cellRefBuilder.append("$");
				}
				if(formula_client == ZSPrintVisitor.FORMULA_CLIENT.XLSX_WRITER) {
					cellRefBuilder.append("'").append((varNode.getVarSheetName(sheet))).append("'");
				} else {
					cellRefBuilder.append(getSheetNameEnclosed(varNode.getVarSheetName(sheet)));
				}
			} catch (EvaluationException ex) {
				cellRefBuilder.append(CellUtil.getErrorString(Cell.Error.REF));
			}
			if(formula_client == ZSPrintVisitor.FORMULA_CLIENT.XLSX_WRITER) {
				cellRefBuilder.append("!");
			} else {
				cellRefBuilder.append(".");
			}
		}

		if (isR1C1) {
			rI = varNode.getRowValue();
			cI = varNode.getColValue();
			cellRefBuilder.append(getR1C1CellReferenceForFormula(rI, cI, false, false, isRowR, isColR, (formula_client == ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER)));
		} else {

			rI = cI = -1;

			try {
				rI = varNode.getVarRowIndex(currentRowIndex);
			} catch (EvaluationException ex) {
			}
			try {
				cI = varNode.getVarColIndex(currentColIndex);
			} catch (EvaluationException ex) {
			}
			cellRefBuilder.append(getA1CellReferenceForFormula(rI, cI, false, false, isRowR, isColR));
		}

		return cellRefBuilder.toString();
	}

	public static String getCellReferenceForFormula(Node node, int currentRowIndex, int currentColIndex, Sheet sheet,
			boolean isR1C1, boolean isForXml) {
		return getCellReferenceForFormula(node, currentRowIndex, currentColIndex, sheet, isR1C1, isForXml ? ZSPrintVisitor.FORMULA_CLIENT.ZS_WRITER : ZSPrintVisitor.FORMULA_CLIENT.ZS_CLIENT);
	}

	private static String getR1C1RefString(int offset, boolean isR, boolean isForXML) {
		StringBuilder offsetBuilder = new StringBuilder();
		if (isR) {
			if (offset != 0 || isForXML) {
				offsetBuilder.append("[");
				offsetBuilder.append(offset);
				offsetBuilder.append("]");
			}
		} else {
			offsetBuilder.append(offset + 1);
		}
		return offsetBuilder.toString();
	}

	private static String getA1CellReferenceForFormula(int rowI, int colI, boolean isRowLevel, boolean isColLevel,
													   boolean isRowR, boolean isColR) {
		StringBuilder cellBuilder = new StringBuilder();

		if (!isRowLevel) {
			if (CellUtil.isColumnRangeBound(colI)) {
				if (!isColR) {
					cellBuilder.append("$");
				}
				cellBuilder.append(getColumnReferenceForFormula(colI));
			} else {
				cellBuilder.append(CellUtil.getErrorString(Error.REF));
			}

		}
		if (!isColLevel) {
			if (CellUtil.isRowRangeBound(rowI)) {
				if (!isRowR) {
					cellBuilder.append("$");
				}
				cellBuilder.append(getRowReferenceForFormula(rowI));
			} else {
				cellBuilder.append(CellUtil.getErrorString(Error.REF));
			}

		}

		return cellBuilder.toString();
	}

	private static String getR1C1CellReferenceForFormula(int rowI, int colI, boolean isRowLevel, boolean isColLevel,
			boolean isRowR, boolean isColR, boolean isForXml) {
		StringBuilder cellBuilder = new StringBuilder();
		// if (rowI == -1 && colI == -1) {
		// cellBuilder.append(CellUtil.getErrorString(Error.REF));
		// } else {
		if (!isColLevel) {
			cellBuilder.append("R"); // No I18N
			cellBuilder.append(getR1C1RefString(rowI, isRowR, isForXml));
		}
		if (!isRowLevel) {
			cellBuilder.append("C"); // No I18N
			cellBuilder.append(getR1C1RefString(colI, isColR, isForXml));
		}
		// }
		return cellBuilder.toString();
	}

	public static List<ASTVarNode> getSortedRangeNodes(ASTRangeNode node, Sheet currSheet, int currRow, int currCol) {
		if (!node.isRangeValid()) // Just need to traverse in same order.
		{
			return new ArrayList<>();
		}

		List<RowRef> rowRefs = new ArrayList<>();
		List<ColRef> colRefs = new ArrayList<>();
		String refferedSheetName = getRangeNodeProperties(node, currSheet, currRow, currCol, rowRefs, colRefs);
		boolean isCircularTranspose = node.isCircularTranspose();

		Collections.sort(colRefs, new ColRefComparator());
		Collections.sort(rowRefs, new RowRefComparator());

		List<ASTVarNode> varNodes = new ArrayList<>();
		String varASN = null;
		if (refferedSheetName != null) {
			Sheet varSheet = currSheet.getWorkbook().getSheet(refferedSheetName);
			if (varSheet != null) {
				varASN = varSheet.getAssociatedName();
			} else {
				varASN = CellUtil.getErrorString(Error.REF);
			}
		}

		for (int i = 0; i < rowRefs.size(); i++) {
			int rowI = rowRefs.get(i).getRowI();
			int colI = colRefs.get(i).getColI();
			boolean isRowR = rowRefs.get(i).isRowR();
			boolean isColR = colRefs.get(i).isColR();

			int varRow = isRowR ? rowI - currRow : rowI;
			int varCol = isColR ? colI - currCol : colI;

			ASTVarNode varNode = new ASTVarNode(JccParserTreeConstants.JJTVARNODE);
			varNode.set(varASN, varRow, varCol, isRowR, isColR, isCircularTranspose);
			varNodes.add(varNode);
		}

		return varNodes;
	}

	public static String getRangeReferenceString(String sheetName, int startRowIndex, int startColIndex,
			int endRowIndex, int endColIndex) {
		return getRangeReferenceString(sheetName, startRowIndex, startColIndex, endRowIndex, endColIndex, false);
	}

	/**
	 * 
	 * <p>
	 * if <i>isForXML</i> is <br/>
	 * true: (0,2 : 65535,4) => C1:E65536<br/>
	 * false: (0,2 : 65535,4) => C:E
	 * </p>
     * @param sheetName
     * @param isForXML
     * @param startRowIndex
     * @param endColIndex
     * @param startColIndex
     * @param endRowIndex
     * @return 
	 */
	public static String getRangeReferenceString(String sheetName, int startRowIndex, int startColIndex,
			int endRowIndex, int endColIndex, boolean isForXML) {
		return getRangeReferenceString(sheetName, startRowIndex, startColIndex, endRowIndex, endColIndex, isForXML,
				false, false, false, false);
	}

	/**
	 * <p>
	 * if an <i>isRelative</i> parameter is <i>false</i>, a <b>"$"</b> will be
	 * prefixed for the corresponding reference<br/>
	 * ex. (5,2 : 12,4) ==(isStartColRelative = false, all others true)==>
	 * $C5:E12
	 * </p>
	 * 
	 * <p>
	 * if <i>isForXML</i> is <br/>
	 * true: (0,2 : 65535,4) => C1:E65536<br/>
	 * false: (0,2 : 65535,4) => C:E
	 * </p>
     * @param sheetName
     * @param startRowIndex
     * @param isEndColRelative
     * @param isEndRowRelative
     * @param startColIndex
     * @param isStartColRelative
     * @param endRowIndex
     * @param isStartRowRelative
     * @param endColIndex
     * @param isForXML
     * @return 
	 */
	public static String getRangeReferenceString(String sheetName, int startRowIndex, int startColIndex,
			int endRowIndex, int endColIndex, boolean isForXML, boolean isStartRowRelative, boolean isStartColRelative,
			boolean isEndRowRelative, boolean isEndColRelative) {
		StringBuilder rangeString = new StringBuilder();
		rangeString.append(CellUtil.getSheetNameEnclosed(sheetName)).append(".")
				.append(getRangeReferenceString(startRowIndex, startColIndex, endRowIndex, endColIndex, isForXML,
						isStartRowRelative, isStartColRelative, isEndRowRelative, isEndColRelative)); // No
																										// I18N
		return rangeString.toString();
	}

	public static String getRangeReferenceString(int startRowIndex, int startColIndex, int endRowIndex, int endColIndex,
			boolean isForXML, boolean isStartRowRelative, boolean isStartColRelative, boolean isEndRowRelative,
			boolean isEndColRelative) {
		StringBuilder rangeString = new StringBuilder();

		if (!isForXML && startRowIndex == 0 && endRowIndex == Utility.MAXNUMOFROWS - 1) {
			rangeString.append(getColReferenceString(startColIndex, isStartColRelative)).append(":")
					.append(getColReferenceString(endColIndex, isEndColRelative)); // No
																					// I18N
		} else if (!isForXML && startColIndex == 0 && endColIndex == Utility.MAXNUMOFCOLS - 1) {
			rangeString.append(getRowReferenceString(startRowIndex, isStartRowRelative)).append(":")
					.append(getRowReferenceString(endRowIndex, isEndRowRelative)); // No
																					// I18N
		} else {
			rangeString
					.append(getCellReferenceString(startRowIndex, startColIndex, isStartRowRelative,
							isStartColRelative))
					.append(":")
					.append(getCellReferenceString(endRowIndex, endColIndex, isEndRowRelative, isEndColRelative)); // No
																													// I18N
		}

		return rangeString.toString();
	}

	public static String getCellReferenceString(int rowIndex, int colIndex, boolean isRowRelative,
			boolean isColRelative) {
		StringBuilder ref = new StringBuilder();
		ref.append(getColReferenceString(colIndex, isColRelative));
		ref.append(getRowReferenceString(rowIndex, isRowRelative));
		return ref.toString();
	}

	public static String getRowReferenceString(int index, boolean isRelative) {
		if (!isRowRangeBound(index)) {
			throw new IllegalArgumentException("Row Index Out of Bound: " + index); // No I18N
																				
		}

		StringBuilder ref = new StringBuilder();
		if (!isRelative) {
			ref.append("$"); // No I18N
		}
		ref.append(index + 1);

		return ref.toString();
	}

	public static String getColReferenceString(int index, boolean isRelative) {
		if (!isColumnRangeBound(index)) {
			throw new IllegalArgumentException("Col Index Out of Bound: " + index); // No I18N
																					
		}

		StringBuilder ref = new StringBuilder();
		if (!isRelative) {
			ref.append("$"); // No I18N
		}
		ref.append(CellUtil.getColumnReference(index));

		return ref.toString();
	}

	static final Pattern UNENCLOSED_SHEETNAME_REGEX_PATTERN = Pattern.compile("^([a-zA-Z_]+[0-9]*)+$");

	public static String getSheetNameEnclosed(String sheetName) {
		if (!UNENCLOSED_SHEETNAME_REGEX_PATTERN.matcher(sheetName).matches()) {
			sheetName = "'" + sheetName + "'";
		}
		return sheetName;
	}

	public static class IndexComparator implements Comparator<CellReference> {

		public IndexComparator() {
		}

		@Override
		public int compare(CellReference cell1, CellReference cell2) {
			int i1 = cell1.getCell().getRowIndex();
			int i2 = cell2.getCell().getRowIndex();

			if (i1 == i2) {
				i1 = cell1.getCell().getColumnIndex();
				i2 = cell2.getCell().getColumnIndex();
			}

			return i1 > i2 ? 1 : i1 < i2 ? -1 : 0;
		}
	}

	public static class RowIndexComparator implements Comparator<CellReference> {

		public RowIndexComparator() {
		}

		@Override
		public int compare(CellReference cell1, CellReference cell2) {
			int i1 = cell1.getCell().getRowIndex();
			int i2 = cell2.getCell().getRowIndex();

			return i1 > i2 ? 1 : i1 < i2 ? -1 : 0;
		}
	}

	public static class ColIndexComparator implements Comparator<CellReference> {

		public ColIndexComparator() {
		}

		@Override
		public int compare(CellReference cell1, CellReference cell2) {
			int i1 = cell1.getCell().getColumnIndex();
			int i2 = cell2.getCell().getColumnIndex();

			return i1 > i2 ? 1 : i1 < i2 ? -1 : 0;
		}
	}

	public static class CellIndexComparator implements Comparator<Cell> {

		public CellIndexComparator() {
		}

		@Override
		public int compare(Cell cell1, Cell cell2) {
			int i1 = cell1.getRowIndex();
			int i2 = cell2.getRowIndex();

			if (i1 == i2) {
				i1 = cell1.getColumnIndex();
				i2 = cell2.getColumnIndex();
			}

			return i1 > i2 ? 1 : i1 < i2 ? -1 : 0;
		}
	}


	public static class DataRangeStartIndexComparator implements Comparator<DataRange> {

		public DataRangeStartIndexComparator() {
		}

		@Override
		public int compare(DataRange range1, DataRange range2) {
			int i1 = range1.getStartRowIndex();
			int i2 = range2.getStartRowIndex();

			if (i1 == i2) {
				i1 = range1.getStartColIndex();
				i2 = range2.getStartColIndex();
			}

			return Integer.compare(i1, i2);
		}
	}

	public static class ColMajorRangeStartIndexComparator implements Comparator<Range> {

		public ColMajorRangeStartIndexComparator() {
		}

		@Override
		public int compare(Range range1, Range range2) {
			int i1 = range1.getStartColIndex();
			int i2 = range2.getStartColIndex();

			if (i1 == i2) {
				i1 = range1.getStartRowIndex();
				i2 = range2.getStartRowIndex();
			}

			return i1 > i2 ? 1 : i1 < i2 ? -1 : 0;
		}
	}

	public static class ColMajorDataRangeStartIndexComparator implements Comparator<DataRange> {

		public ColMajorDataRangeStartIndexComparator() {
		}

		@Override
		public int compare(DataRange range1, DataRange range2) {
			int i1 = range1.getStartColIndex();
			int i2 = range2.getStartColIndex();

			if (i1 == i2) {
				i1 = range1.getStartRowIndex();
				i2 = range2.getStartRowIndex();
			}

			return i1 > i2 ? 1 : i1 < i2 ? -1 : 0;
		}
	}

	// Dont write these cellStyle as they are default and will be present in
	// styles.xml of an empty ODF file itself.
	private static List defaultCellStyleList = null;

	public static List getDefaultCellStyles() {
		if (defaultCellStyleList == null) {
			defaultCellStyleList = new ArrayList();
			// defaultCellStyleList.add("Default");
			defaultCellStyleList.add("Result");
			defaultCellStyleList.add("Result2");
			defaultCellStyleList.add("Heading");
			defaultCellStyleList.add("Heading1");
		}
		return defaultCellStyleList;
	}

	// This will return the default currency styles which normally gets
	// generated
	// in styles.xml file. Normally all the Currency styles gets written in
	// styles.xml
	// file. But we will write any other currency styles other than deafult one
	// in
	// content.xml file. Hence we need this list, so that we don't write these
	// styles
	// in content.xml as it will be available in default styles.xml template
	private static List defaultNumberStyleList = null;

	public static List getDefaultNumberStyles() {
		if (defaultNumberStyleList == null) {
			defaultNumberStyleList = new ArrayList();
			defaultNumberStyleList.add("N0");
			defaultNumberStyleList.add("N104");
			defaultNumberStyleList.add("N104P0");
		}
		return defaultNumberStyleList;
	}

	/*
	 * public static Cell getAltEqualCell(int rowIndex,int colIndex, boolean
	 * isColumn, Sheet sheet) {
	 * 
	 * boolean isNumberEncountered = false; boolean isTrueSet = false; Cell
	 * tempCell = null;
	 * 
	 * int index = (isColumn ? rowIndex : colIndex) - 1;
	 * 
	 * for( ;index >= 0; index-- ) { Cell prevCell = tempCell;
	 * 
	 * if(isColumn) { //Iterating In Row Wise //tempCell =
	 * sheet.getCellReadOnly(index, colIndex); tempCell =
	 * sheet.getCellInUsedIndex(index, colIndex);; } else { //tempCell =
	 * sheet.getCellReadOnly(rowIndex, index); tempCell =
	 * sheet.getCellInUsedIndex(rowIndex, index); }
	 * 
	 * if(tempCell != null && tempCell.getValue().getValue() instanceof Number)
	 * { isNumberEncountered = true; isTrueSet = true; } else {
	 * isNumberEncountered = false; }
	 * 
	 * if(isTrueSet && !isNumberEncountered) { //Should Return Previous Cell If
	 * Meets Requirement. return prevCell; } }
	 * 
	 * //Note: (isNumberEncountered is true)This Case Will Be Reached Only If It
	 * Traverse Upto Zero return (isNumberEncountered) ? tempCell : null; }
	 */
	/**
	 * Method Used When altEquals In Client.
	 *
	 * @param sheet
	 * @param rowIndex
	 * @param colIndex
	 * @param isColumn
	 * @return
	 */
	public static Range getAltEqualRange(Sheet sheet, int rowIndex, int colIndex, boolean isColumn) {
		boolean isNumberFormat = false;
		ReadOnlyCell prevRCell = null;

		int index = (isColumn ? rowIndex : colIndex) - 1;
		for (; index >= 0; index--) {
			ReadOnlyCell rCell;
			if (isColumn) { // Iterating In Row Wise
				rCell = sheet.getReadOnlyCellFromShell(index, colIndex);
			} else {
				rCell = sheet.getReadOnlyCellFromShell(rowIndex, index);
			}

			Cell cell = rCell.getCell();
			if (cell != null) {
				Type cellType = cell.getType(); // getting cell type.

				if (cellType == Type.FLOAT || cellType == Type.PERCENTAGE || cellType == Type.CURRENCY
						|| cellType == Type.FRACTION) {
					isNumberFormat = true;
				} else if (isNumberFormat) {
					break;
				}
			} else if (isNumberFormat) {
				break;
			}

			prevRCell = rCell;
		}

		if (isNumberFormat) {
			return new Range(sheet, prevRCell.getRowIndex(), prevRCell.getColIndex(),
					isColumn ? rowIndex - 1 : rowIndex, isColumn ? colIndex : colIndex - 1);
		}

		return null;
	}

	public static CellStyle getCellStyle(Sheet sheet, int rowIndex, int colIndex) {
		for (int iRow = rowIndex; iRow >= 0; iRow--) {
			Row tempRow = sheet.getRowReadOnly(iRow);

			if (tempRow != null) {
				if (rowIndex < (tempRow.getRowIndex() + tempRow.getRowsRepeated())) {
					for (int iCol = colIndex; iCol >= 0; iCol--) {
						Cell cell = sheet.getCellReadOnly(iRow, iCol);

						if (cell != null) {
							if (colIndex < (cell.getColumnIndex() + cell.getColsRepeated())) {
								return cell.getCellStyle();
							}

							break;
						}
					}
				}

				break;
			}
		}

		return null;
	}

	public static DataRange toDataRange(Cell cell) {

		return new DataRange(cell.getRow().getSheet().getAssociatedName(), cell.getRowIndex(), cell.getColumnIndex(),
				cell.getRowIndex(), cell.getColumnIndex());

	}

	public static boolean isCheckboxPresent(Sheet sheet, int rowIndex, int colIndex) {
		for (DataRange range : sheet.getCheckboxRangeList()) {
			if (range.isCellInRange(sheet.getAssociatedName(), rowIndex, colIndex)) {
				return true;
			}
		}
		return false;
	}

    public static boolean isPicklistPresent(Sheet sheet, int rowIndex, int colIndex) {
        for(Map.Entry<Integer,List<DataRange>> entry : sheet.getPicklistRangeMap().entrySet()) {
            List<DataRange> rangeList = entry.getValue();
            for(DataRange range : rangeList) {
                if (range.isCellInRange(sheet.getAssociatedName(), rowIndex, colIndex)) {
                    return true;
                }
            }
        }
    return false;
    }

    public static List<Picklist> getPicklistsForSourceCell(Sheet sheet, int rowIndex, int colIndex) {
		Workbook workbook = sheet.getWorkbook();
		List<Picklist> result = new ArrayList();
		for(Picklist picklist: workbook.getPicklistMap().values()) {
			List<DataRange> sourceRanges = picklist.getSourceRanges();
			if(sourceRanges != null) {
				for(DataRange range: sourceRanges) {
					if(range.getAssociatedSheetName().equals(sheet.getAssociatedName()) && range.isCellInRange(sheet.getAssociatedName(), rowIndex, colIndex)) {
						result.add(picklist);
						break;
					}
				}
			}
		}

		return result;
	}

	public static HashMap<String,Object> getCellFormatInfo(Workbook workbook, Sheet sheet, int row, int col) {
		try {
			return ClientUtils.getCellFormatInfo(workbook,sheet.getAssociatedName(),row,col);
		}
		catch (Exception e) {
			return null;
		}
	}

	public static HashMap<String, Object> getCellFormatInfoNew(Workbook workbook, ZSPattern cellPattern, TimeCapsule timeCapsule) {
		/* Used to store Cell Pattern info in client ,
		 If no pattern leave pattern object empty, else add type and required 	 info for each type
		 Pattern values may contain [] , {} etc ,hence URL encoding and decoding again in c. 
		 Need to fix .Issue in netsfjson library*/
		HashMap<String, Object> formatInfo = new HashMap<>();
		try {
                        timeCapsule.startEvent(TimeCapsule.Event.EXTRACT_CELLFORMAT_INFO);
			HashMap<FormatProps, Object> cellPatternMap = ClientUtils.extractCellFormatInFo(workbook, cellPattern, timeCapsule);
			timeCapsule.endCurrentEvent();
                        String formatType = (String) cellPatternMap.get(FormatProps.FORMATTYPE);

			if ("".equals(formatType) || "UNDEFINED".equals(formatType)) { // NO I18N
				return formatInfo;
			}

			else {
				formatInfo.put(Integer.toString(CommandConstants.FORMATTYPE), formatType);
				formatInfo.put(Integer.toString(CommandConstants.ISCUSTOM), cellPatternMap.get(FormatProps.ISCUSTOM));
				String formatString = (String) cellPatternMap.get(FormatProps.FORMATSTRING);
				formatString = URLEncoder.encode(formatString, "UTF-8").replace("+", "%20");// NO I18N

				formatInfo.put(Integer.toString(CommandConstants.FORMATSTRING), formatString);

				if(cellPatternMap.containsKey(FormatProps.VALUETYPE)) {
					formatInfo.put(Integer.toString(CommandConstants.VALUETYPE),
							cellPatternMap.get(FormatProps.VALUETYPE));
				}

				switch (formatType) {

				case "FLOAT": // NO I18N
					formatInfo.put(Integer.toString(CommandConstants.NEGATIVEFRMT),
							cellPatternMap.get(FormatProps.NEGATIVEFORMAT));
					formatInfo.put(Integer.toString(CommandConstants.DECIMALS),
							cellPatternMap.get(FormatProps.DECIMALS));
					formatInfo.put(Integer.toString(CommandConstants.LEADINGZEROES),
							cellPatternMap.get(FormatProps.LEADINGZEROES));
					formatInfo.put(Integer.toString(CommandConstants.ISGROUPINGUSED),
							cellPatternMap.get(FormatProps.ISGROUPINGUSED));
                    if(cellPatternMap.containsKey(FormatProps.PREFIX)) {
                        formatInfo.put(Integer.toString(CommandConstants.PREFIX),cellPatternMap.get(FormatProps.PREFIX));
                    }
                    if(cellPatternMap.containsKey(FormatProps.SUFFIX)) {
                        formatInfo.put(Integer.toString(CommandConstants.SUFFIX),cellPatternMap.get(FormatProps.SUFFIX));
                    }
                    break;
				case "SCIENTIFIC": // NO I18N
					formatInfo.put(Integer.toString(CommandConstants.DECIMALS),
							cellPatternMap.get(FormatProps.DECIMALS));
					break;
				case "FRACTION": // NO I18N
					formatInfo.put(Integer.toString(CommandConstants.FRACTION_DIGITS),
							cellPatternMap.get(FormatProps.FRACTIONDIGITS));
					break;
				case "PERCENTAGE": // NO I18N
					formatInfo.put(Integer.toString(CommandConstants.DECIMALS),
							cellPatternMap.get(FormatProps.DECIMALS));
					break;
				case "CURRENCY": // NO I18N
					formatInfo.put(Integer.toString(CommandConstants.NEGATIVEFRMT),
							cellPatternMap.get(FormatProps.NEGATIVEFORMAT));
					formatInfo.put(Integer.toString(CommandConstants.SYMBOL), cellPatternMap.get(FormatProps.SYMBOL));
					formatInfo.put(Integer.toString(CommandConstants.DECIMALS),
							cellPatternMap.get(FormatProps.DECIMALS));
					formatInfo.put(Integer.toString(CommandConstants.LEADINGZEROES),
							cellPatternMap.get(FormatProps.LEADINGZEROES));
					formatInfo.put(Integer.toString(CommandConstants.ISGROUPINGUSED),
							cellPatternMap.get(FormatProps.ISGROUPINGUSED));
					break;
					case "ACCOUNTING":// NO I18N
						formatInfo.put(Integer.toString(CommandConstants.SYMBOL), cellPatternMap.get(FormatProps.SYMBOL));
						formatInfo.put(Integer.toString(CommandConstants.DECIMALS),
								cellPatternMap.get(FormatProps.DECIMALS));
						formatInfo.put(Integer.toString(CommandConstants.LEADINGZEROES),
								cellPatternMap.get(FormatProps.LEADINGZEROES));
						formatInfo.put(Integer.toString(CommandConstants.ISGROUPINGUSED),
								cellPatternMap.get(FormatProps.ISGROUPINGUSED));
						break;
				// Format String is the same as the options shown in UI but for
				// Date-Time alone both exists together and to split we get
				// seperately
				case "DATE": // NO I18N
					formatInfo.put(Integer.toString(CommandConstants.DATEFMRT), formatString);
					break;
				case "TIME": // NO I18N
					formatInfo.put(Integer.toString(CommandConstants.TIMEFMRT), formatString);

					break;
				case "DATETIME": // NO I18N
					String dateFormat = (String) cellPatternMap.get(FormatProps.DATEFORMAT);
					dateFormat = URLEncoder.encode(dateFormat, "UTF-8");// NO I18N
					String timeFormat = (String) cellPatternMap.get(FormatProps.TIMEFORMAT);
					timeFormat =URLEncoder.encode(timeFormat, "UTF-8");// NO I18N
					formatInfo.put(Integer.toString(CommandConstants.DATEFMRT), dateFormat);
					formatInfo.put(Integer.toString(CommandConstants.TIMEFMRT), timeFormat);
					break;

				case "DURATION": // NO I18N
					formatInfo.put(Integer.toString(CommandConstants.TIMEFMRT), formatString);
					break;
				case "REGIONAL": // NO I18N
					formatInfo.put(Integer.toString(CommandConstants.REGIONALTYPE),
							cellPatternMap.get(FormatProps.REGIONALTYPE));
					formatInfo.put(Integer.toString(CommandConstants.COUNTRY), cellPatternMap.get(FormatProps.COUNTRY));
					formatInfo.put(Integer.toString(CommandConstants.LANGUAGE),
							cellPatternMap.get(FormatProps.LANGUAGE));
					break;

				default:
					break;
				}
			}

		} catch (Exception e) {
			LOGGER.log(Level.INFO, "Exception while getting cell pattern returning empty pattern", e);
		}
		return formatInfo;

	}

	public static List<CellStyle> getNonLoopingAncestors(CellStyle cellStyle, Workbook workbook) {
		return CellUtil.getNonLoopingAncestors(cellStyle, cellStyleName -> workbook.getCellStyle(cellStyleName), new HashSet());
	}

	public static List<CellStyle> getNonLoopingAncestors(CellStyle cellStyle, Function<String, CellStyle> cellStyleNameToCellStyle, Collection<String> visitedAncestorStyleNames) {
		LinkedList<CellStyle> nonLoopingAncestors = new LinkedList<>();

		while (!visitedAncestorStyleNames.contains(cellStyle.getStyleName())) {
			nonLoopingAncestors.addFirst(cellStyle);
			visitedAncestorStyleNames.add(cellStyle.getStyleName());

			String parentStyleName = cellStyle.getParenStyleName();
			if (parentStyleName == null) {
				break;
			}
			CellStyle parentCellStyle = cellStyleNameToCellStyle.apply(parentStyleName);
			if (parentCellStyle == null) {
				break;
			}
			cellStyle = parentCellStyle;
		}

		if (!visitedAncestorStyleNames.contains(EngineConstants.DEFAULT_CELLSTYLENAME)) {
			CellStyle defaultCellStyle = cellStyleNameToCellStyle.apply(EngineConstants.DEFAULT_CELLSTYLENAME);
			if (defaultCellStyle != null) {
				nonLoopingAncestors.addFirst(defaultCellStyle);
				visitedAncestorStyleNames.add(EngineConstants.DEFAULT_CELLSTYLENAME);
			}
		}

		return nonLoopingAncestors;
	}

    public static Collection<String> reduceDisplayNamelessNodes(Map<String, CellStyle> namedCellStyleMap){
        Set<String> displayNamedCellStyleNames = new HashSet<>();
        Set<String> visitedCellStyleNamesCached = new HashSet();
        for(Map.Entry<String, CellStyle> styleEntry : namedCellStyleMap.entrySet()){
            String displayName = styleEntry.getValue().getDisplayName();
            if(displayName != null){
                List<CellStyle> relativePath = getNonLoopingAncestors(styleEntry.getValue(), cellStylename -> namedCellStyleMap.get(cellStylename), visitedCellStyleNamesCached);
                for(CellStyle cellStyle: relativePath){
                    displayNamedCellStyleNames.add(cellStyle.getStyleName());
                }
            }
        }
        Set<String> namedCellStyleNames = new HashSet(namedCellStyleMap.keySet());
        namedCellStyleNames.removeAll(displayNamedCellStyleNames);
        return  namedCellStyleNames;
    }

    public static List<String> getTopologicalSortedCellStyleList(Map<String, CellStyle> cellStyleMap){
        ArrayList<String> hierarchialCellStyleList = new ArrayList<>();
        Set<String> visitedCellStyleNamesCached = new HashSet();
        for(Map.Entry<String, CellStyle> styleEntry : cellStyleMap.entrySet()){
            List<CellStyle> relativePath = getNonLoopingAncestors(styleEntry.getValue(), cellStyleName -> cellStyleMap.get(cellStyleName), visitedCellStyleNamesCached);
            for(CellStyle cellStyle: relativePath){
                hierarchialCellStyleList.add(cellStyle.getStyleName());
            }
        }
        return hierarchialCellStyleList;
    }


	// The below methods commented as these are moved to Range
	/*
	 * public static List<Cell> copyRangeToCell(Sheet sheet, int startRow, int
	 * startCol, int endRow, int endCol, int destRow, int destCol) { Range range
	 * = new Range(sheet, startRow, startCol, endRow, endCol); Cell srcCells[][]
	 * = range.getDimension();
	 * 
	 * 
	 * // Copy to the destination cell here List<Cell> destCells = new
	 * ArrayList<Cell>(); for(int i=0; i<srcCells.length; i++) { for(int j=0;
	 * j<srcCells[i].length; j++) { Cell srcCell = srcCells[i][j];
	 * 
	 * Cell destCell = sheet.getCell(destRow+i, destCol+j);
	 * destCell.copyFrom(srcCell); // Evaluate Cell to calculate and set the
	 * value destCell.evaluate(); destCells.add(destCell); } }
	 * 
	 * return destCells; }
	 * 
	 * public static List<Cell> copyRangeToRange(Sheet sheet, int srcStartRow,
	 * int srcStartCol, int srcEndRow, int srcEndCol, int destStartRow, int
	 * destStartCol, int destEndRow, int destEndCol) { int totalDestRows = (new
	 * Integer(destEndRow).intValue()) - destStartRow +1; int totalDestCols =
	 * (new Integer(destEndCol).intValue()) - destStartCol +1; int totalSrcRows
	 * = srcEndRow - srcStartRow + 1; int totalSrcCols = srcEndCol - srcStartCol
	 * +1; int acrossRows = totalDestRows/totalSrcRows; int acrossCols =
	 * totalDestCols/totalSrcCols;
	 * 
	 * int remainderRows = totalDestRows%totalSrcRows; int remainderCols =
	 * totalDestCols%totalSrcCols;
	 * 
	 * if(acrossRows == 0) { acrossRows = 1; } if(acrossCols == 0) { acrossCols
	 * = 1; }
	 * 
	 * Range range = new Range(sheet, srcStartRow, srcStartCol, srcEndRow,
	 * srcEndCol); Cell srcCells[][] = range.getDimension();
	 * 
	 * // Copy to the destination cell here List<Cell> destCells = new
	 * ArrayList<Cell>(); for(int i=0; i<acrossRows; i++) { int itrRows =
	 * srcCells.length; for(int m=0; m<itrRows; m++) { for(int j=0;
	 * j<acrossCols; j++ ) { int itrCols = srcCells[m].length; for(int n=0;
	 * n<itrCols; n++) { Cell srcCell = srcCells[m][n];
	 * 
	 * Cell destCell = sheet.getCell(destStartRow+m+i*totalSrcRows,
	 * destStartCol+n+j*totalSrcCols); destCell.copyFrom(srcCell);
	 * destCells.add(destCell); }// for int n = 0
	 * 
	 * }// for int j = 0 } // for int m =0 }// for int i = 0
	 * 
	 * if(remainderRows == 0 && remainderCols == 0) { // Do nothing } else {
	 * //if(remainderRows == 0) {
	 * 
	 * for(int itr=0; itr<acrossRows; itr++) { for(int i=0; i<srcCells.length;
	 * i++) { for(int j=0; j<remainderCols; j++) { Cell srcCell =
	 * srcCells[i][j];
	 * 
	 * //Cell destCell = sheet.getCell(row+i, column+j+acrossCols*totalSrcCols);
	 * Cell destCell = sheet.getCell(destStartRow+i+itr*totalSrcRows,
	 * destStartCol+j+acrossCols*totalSrcCols); destCell.copyFrom(srcCell);
	 * destCells.add(destCell); } } } }
	 * 
	 * //else if(remainderCols == 0) { for(int i=0; i<remainderRows; i++) {
	 * for(int itr=0; itr<acrossCols; itr++) { for(int j=0;
	 * j<srcCells[i].length; j++) { Cell srcCell = srcCells[i][j];
	 * 
	 * //Cell destCell = sheet.getCell(row+i+acrossRows*totalSrcRows, column+j);
	 * Cell destCell = sheet.getCell(destStartRow+i+acrossRows*totalSrcRows,
	 * destStartCol+j+itr*totalSrcCols); destCell.copyFrom(srcCell);
	 * destCells.add(destCell); } } } } // else { // Copy the remainender rows
	 * and columns int computeRow = acrossRows*totalSrcRows; int computeCol =
	 * acrossCols*totalSrcCols; for(int i=0; i<remainderRows; i++) { for(int
	 * j=0; j<remainderCols; j++) { Cell srcCell = srcCells[i][j]; Cell destCell
	 * = sheet.getCell(destStartRow+i+computeRow, destStartCol+j+computeCol);
	 * destCell.copyFrom(srcCell); destCells.add(destCell); } }
	 * 
	 * 
	 * 
	 * 
	 * } }
	 * 
	 * // Calculate and set the value of newly pasted cells for(Cell destCell :
	 * destCells) { destCell.evaluate(); } return destCells; }
	 * 
	 */
	////
	/*
	 * public static Cell getEmptyCell(Row row, int colIndex) { Cell cell = new
	 * EmptyCell(row, colIndex); return cell; }
	 */
	public static void main(String args[]) {

		LOGGER.log(Level.INFO, "getRow : {0}", getRow("$A$1"));
		LOGGER.log(Level.INFO, "getColumn : {0}", getColumn("$A$1"));
		String r1c1RefStr = new Scanner(System.in).nextLine();
		try {
			Ref ref = getRefFromR1C1ReferenceString(r1c1RefStr);
			// System.out.println("sheet: "+ref.sheetName);
			// System.out.println("row: "+ (ref.getRowRef()==null ? (null) :
			// ref.getRowRef().getRowI()+" : "+ref.getRowRef().isRowR()));
			// System.out.println("col: "+ (ref.getColRef()==null ? (null) :
			// ref.getColRef().getColI()+" : "+ref.getColRef().isColR()));
		} catch (Exception e) {
			e.printStackTrace();
		}
		// logger.log(Level.INFO, "getColumnReference :
		// "+getColumnReference(255));
		// logger.log(Level.INFO, "getCellReference : "+getCellReference(0,0));
		// logger.log(Level.INFO, "isColumnRelative :
		// "+isColumnRelative(".$B$9"));
		// logger.log(Level.INFO, "isCellRangeBound :
		// "+isCellRangeBound("IW65536"));
	}

	public static class LinearRef {

		private int index;
		private boolean isRelative;

		public LinearRef(int index, boolean isRelative) {
			this.index = index;
			this.isRelative = isRelative;
		}

		public int getIndex() {
			return this.index;
		}

		public boolean isRelative() {
			return this.isRelative;
		}
	}

	public static class RowRef extends LinearRef {

		public RowRef(int rowI, boolean rowR) {
			super(rowI, rowR);
		}

		public int getRowI() {
			return super.getIndex();
		}

		public boolean isRowR() {
			return super.isRelative();
		}
	}

	public static class ColRef extends LinearRef {

		public ColRef(int colI, boolean colR) {
			super(colI, colR);
		}

		public int getColI() {
			return super.getIndex();
		}

		public boolean isColR() {
			return super.isRelative();
		}
	}

	public static class Ref {

		private String sheetName;
		private RowRef rowRef;
		private ColRef colRef;

		public Ref(String sheetName, RowRef rowRef, ColRef colRef) {
			this.sheetName = sheetName;
			this.rowRef = rowRef;
			this.colRef = colRef;
		}

		public Ref(String sheetName, int rowIndex, boolean isRowRelative, int colIndex, boolean isColRelative) {
			this(sheetName, new RowRef(rowIndex, isRowRelative), new ColRef(colIndex, isColRelative));
		}

		public String getSheetName() {
			return this.sheetName;
		}

		public RowRef getRowRef() {
			return this.rowRef;
		}

		public ColRef getColRef() {
			return this.colRef;
		}
	}

	private static class RowRefComparator implements Comparator<RowRef> {

		public RowRefComparator() {
		}

		@Override
		public int compare(RowRef obj1, RowRef obj2) {
			int i1 = obj1.getRowI();
			int i2 = obj2.getRowI();

			return i1 > i2 ? 1 : i1 < i2 ? -1 : 0;
		}
	}

	private static class ColRefComparator implements Comparator<ColRef> {

		public ColRefComparator() {
		}

		@Override
		public int compare(ColRef obj1, ColRef obj2) {
			int i1 = obj1.getColI();
			int i2 = obj2.getColI();

			return i1 > i2 ? 1 : i1 < i2 ? -1 : 0;
		}
	}
        
        public static Border getBorder(Workbook workbook, CellStyle cellStyle)
        {
			return new Border(null, cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERTOP, workbook), cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERBOTTOM, workbook), cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, workbook), cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERRIGHT, workbook));
	}
        
        public static class Border{
            private final String border;
            private final String borderTop;
            private final String borderBottom;
            private final String borderLeft;
            private final String borderRight;

            public Border(String border, String top, String bottom, String left, String right){
                this.border= border;
                this.borderTop= top;
                this.borderBottom= bottom;
                this.borderLeft= left;
                this.borderRight= right;
            }

            public String getBorder() {
                return border;
            }

            public String getBorderTop() {
                return borderTop;
            }

            public String getBorderBottom() {
                return borderBottom;
            }

            public String getBorderLeft() {
                return borderLeft;
            }

            public String getBorderRight() {
                return borderRight;
            }

            @Override
            public boolean equals(Object that){
                if(this==that){
                    return true;
                }

                if(!(that instanceof Border)){
                    return false;
                }

                Border thatBorder= (Border)that;

                return ((this.border==null ? thatBorder.border==null : this.border.equals(thatBorder.border))
                        && (this.borderTop==null? thatBorder.borderTop==null : this.borderTop.equals(thatBorder.borderTop))
                        && (this.borderBottom==null? thatBorder.borderBottom==null : this.borderBottom.equals(thatBorder.borderBottom))
                        && (this.borderLeft==null? thatBorder.borderLeft==null : this.borderLeft.equals(thatBorder.borderLeft))
                        && (this.borderRight==null? thatBorder.borderRight==null : this.borderRight.equals(thatBorder.borderRight)));
            }

            @Override
            public int hashCode(){
                int prime= 5;
                int hash=1;

                hash= prime*hash+ (this.border!=null ? this.border.hashCode() : 0);
                hash= prime*hash+ (this.borderTop!=null ? this.borderTop.hashCode() : 0);
                hash= prime*hash+ (this.borderBottom!=null ? this.borderBottom.hashCode() : 0);
                hash= prime*hash+ (this.borderLeft!=null ? this.borderLeft.hashCode() : 0);
                hash= prime*hash+ (this.borderRight!=null ? this.borderRight.hashCode() : 0);

                return hash;
            }
        }

}
