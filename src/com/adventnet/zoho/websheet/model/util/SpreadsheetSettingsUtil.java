package com.adventnet.zoho.websheet.model.util;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.pivot.PivotUtil;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.SheetStyle;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.zfsng.client.ZohoFS;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> N <PERSON> (ZT-0049)
 */
public class SpreadsheetSettingsUtil
{
	private static final Logger LOGGER = Logger.getLogger(SpreadsheetSettingsUtil.class.getName());

	public static boolean[] setSpreadsheetSettings_Old(WorkbookContainer container, Workbook workbook, String fontName, String fontSize, Locale locale, String timeZoneStr, SpreadsheetSettings.DisplayDirection sheetDir, Workbook.View view, Locale dictionaryLocale, boolean isIterativeCalc, int maxNoOfIterations, double threshold, Workbook.PatternSetting patternSetting)//tikeshwar sahu
	{
		try
		{
			JSONObjectWrapper additionalInfo = new JSONObjectWrapper();
			if(locale != null)
			{
				additionalInfo.put(Constants.DOCUMENT_LOCALE, locale.toString());
			}
			if(timeZoneStr != null)
			{
				additionalInfo.put(Constants.DOCUMENT_TIMEZONE, timeZoneStr);
			}
			if(sheetDir != null)
			{
				additionalInfo.put(Constants.SHEET_DIR, sheetDir.getId());
			}
			if(view != null)
			{
				additionalInfo.put(Constants.VIEW, view.getId());
			}
			if(dictionaryLocale != null)
			{
				additionalInfo.put(Constants.DICTIONARY_LOCALE, dictionaryLocale.toString());
			}
			additionalInfo.put(Constants.ITERATIVE_CALCULATION, isIterativeCalc);
			additionalInfo.put(Constants.MAX_NUMBER_ITERATIONS, maxNoOfIterations);
			additionalInfo.put(Constants.THRESHOLD, threshold);
			additionalInfo.put(Constants.PATTERN_SETTING, patternSetting.getId());
			if(!additionalInfo.isEmpty())
			{
				ZohoFS.updateResourceAdditionalInfo(container.getDocsSpaceId(), container.getResourceId(), additionalInfo.toString());
			}
		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "Exception while locale setting writting to the DB: EngineUtils1.documentSetting():{0}", e);
		}

		boolean isTimeZoneChanged = timeZoneStr != null && workbook.setUserTimezone(timeZoneStr);
		boolean isLocaleChanged = locale != null && workbook.setFunctionLocale(locale);
		boolean isSheetDirChanged = sheetDir != null && workbook.setSheetDir(sheetDir);
		boolean isViewChanged = view != null && workbook.setView(view);
		boolean isPatternSettingChanged = patternSetting != null && workbook.setPatternSetting(patternSetting);

		boolean isIterativeCalcChanged = (isIterativeCalc && (workbook.getCalculationSettings() == null || (workbook.getCalculationSettings().getIterationCount() != maxNoOfIterations && workbook.getCalculationSettings().getThreshold() != threshold))) || (!isIterativeCalc && workbook.getCalculationSettings() != null);
		if(isIterativeCalc)
		{
			workbook.setCalculationSettings(new ReEvaluate.CalculationSettings(maxNoOfIterations, threshold));
		}
		else
		{
			workbook.setCalculationSettings(null);
		}
		boolean isRecalcFormulaCells = isIterativeCalcChanged || isPatternSettingChanged;
		return new boolean[]{isTimeZoneChanged || isLocaleChanged || isSheetDirChanged || isViewChanged, isRecalcFormulaCells};
	}

	public static void updateSpreadsheetSettings(WorkbookContainer container, Workbook workbook, Locale locale, TimeZone timeZone, Locale dictionaryLocale, SpreadsheetSettings spreadsheetSettings, ReEvaluate.CalculationSettings calculationSettings, Workbook.PatternSetting patternSetting, Workbook.View view)
	{
		try
		{
			JSONObjectWrapper additionalInfo = new JSONObjectWrapper();
			additionalInfo.put(Constants.DOCUMENT_LOCALE, locale.toString());
			additionalInfo.put(Constants.DOCUMENT_TIMEZONE, timeZone.getID());
			additionalInfo.put(Constants.SHEET_DIR, spreadsheetSettings.getDisplayDirection().equals(SpreadsheetSettings.DisplayDirection.RIGHT_TO_LEFT) ? "true": "false");
			additionalInfo.put(Constants.VIEW, view.name());
			additionalInfo.put(Constants.DICTIONARY_LOCALE, dictionaryLocale.toString());

			additionalInfo.put(Constants.DECIMAL_SEP, spreadsheetSettings.getDecimalSeparator());
			additionalInfo.put(Constants.THOUSAND_SEP, spreadsheetSettings.getThousandSeparator());
			additionalInfo.put(Constants.DATE_FORMAT, spreadsheetSettings.getDateInputFormat().getId());
			additionalInfo.put(Constants.CURRENCY_LOCALE, spreadsheetSettings.getCurrencyLocale().toString());

			additionalInfo.put(Constants.ITERATIVE_CALCULATION, calculationSettings != null);
			additionalInfo.put(Constants.MAX_NUMBER_ITERATIONS, calculationSettings != null ? calculationSettings.getIterationCount() : EngineConstants.DEFAULT_MAX_NUMBER_OF_ITERATIONS);
			additionalInfo.put(Constants.THRESHOLD, calculationSettings != null ? calculationSettings.getIterationCount() : EngineConstants.DEFAULT_THRESHOLD_VALUE);
			additionalInfo.put(Constants.PATTERN_SETTING, patternSetting.getId());

			ZohoFS.updateResourceAdditionalInfo(container.getDocsSpaceId(), container.getResourceId(), additionalInfo.toString());
		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "[SPREADSHEET_SETTINGS][Exception] Exception while locale setting writting to the DB: EngineUtils1.documentSetting():{0}", e);
		}
		boolean isItrCalc = calculationSettings != null;
		boolean isSpreadsheetSettingsChanged = !workbook.getSpreadsheetSettings().equals(spreadsheetSettings);
		boolean isTimeZoneChanged = timeZone != null && workbook.setUserTimezone(timeZone.getID());
		boolean isViewChanged = view != null && workbook.setView(view);
		boolean isPatternSettingChanged = workbook.setPatternSetting(patternSetting);
		boolean isIterativeCalcChanged = (isItrCalc && (workbook.getCalculationSettings() == null || (workbook.getCalculationSettings().getIterationCount() != calculationSettings.getIterationCount() && workbook.getCalculationSettings().getThreshold() != calculationSettings.getThreshold()))) || (!isItrCalc && workbook.getCalculationSettings() != null);
		if(isItrCalc)
		{
			workbook.setCalculationSettings(calculationSettings);
		}
		else
		{
			workbook.setCalculationSettings(null);
		}
		boolean isRecalcFormulaCells = isIterativeCalcChanged || isPatternSettingChanged;
		boolean isRegenerateContent = isTimeZoneChanged || isSpreadsheetSettingsChanged || isViewChanged;
		List<Cell> changedCells = new ArrayList<>();
		if(isRegenerateContent)
		{
			long startTime = System.currentTimeMillis();
			changedCells.addAll(workbook.regenerateContent(isRecalcFormulaCells));

			long timeTaken = System.currentTimeMillis() - startTime;
			if(timeTaken > 2000)
			{
				LOGGER.log(Level.INFO, "[DOCUMENT_SETTINGS] Total time taken to regenerate the content is:{0} no of changedCells are:{1}", new Object[]{timeTaken, changedCells.size()});
			}
		}
		else if(isRecalcFormulaCells)
		{
			changedCells.addAll(workbook.recalculateFormulaCells());
		}

		for(Sheet sheet : workbook.getSheetList())
		{
			SheetStyle sheetStyle = sheet.getSheetStyle();
			sheetStyle.setProperty(SheetStyle.Property.WRITINGMODE, spreadsheetSettings.getDisplayDirection().getWritingMode());
			sheet.setSheetStyle(sheetStyle);
		}
		if(isSpreadsheetSettingsChanged)
		{
			workbook.setSpreadsheetSettings(spreadsheetSettings);
		}
	}

	public static void updateResourceAdditionalInfo(WorkbookContainer container, WorkbookAdditionalInfo workbookAdditionalInfo) throws Exception
	{
		try
		{
			ZohoFS.updateResourceAdditionalInfo(container.getDocsSpaceId(), container.getResourceId(), workbookAdditionalInfo.getJSON().toString());
		}
		catch(Exception e)
		{
			LOGGER.log(Level.WARNING, "[SPREADSHEET_SETTINGS][Exception] Exception while updating document settings", e);
			throw e;
		}
	}

	public static List<Cell> updateSpreadsheetSettings(WorkbookContainer container, Workbook workbook, String fontName, String fontSize, ZSColor fontColor, ZSColor gridColor, WorkbookAdditionalInfo workbookAdditionalInfo)
	{
		SpreadsheetSettings spreadsheetSettings = workbookAdditionalInfo.getSpreadsheetSettings();
		CellStyle defaultCellStyle = workbook.getDefaultCellStyle();

		boolean isItrCalc = workbookAdditionalInfo.isIterativeCalc();
		boolean isSpreadsheetSettingsChanged = !workbook.getSpreadsheetSettings().equals(spreadsheetSettings);
		boolean isTimeZoneChanged = workbookAdditionalInfo.getTimeZone() != null && workbook.setUserTimezone(workbookAdditionalInfo.getTimeZone().getID());
		boolean isViewChanged = workbookAdditionalInfo.getView() != null && workbook.setView(workbookAdditionalInfo.getView());
		boolean isSheetDirectionChanged = !(workbook.getSpreadsheetSettings().getDisplayDirection().equals(spreadsheetSettings.getDisplayDirection()));
		boolean isPatternSettingChanged = workbook.setPatternSetting(workbookAdditionalInfo.getPatternSetting());
		boolean isIterativeCalcChanged = (isItrCalc && (workbook.getCalculationSettings() == null || (workbook.getCalculationSettings().getIterationCount() != workbookAdditionalInfo.getCalculationSettings().getIterationCount() || workbook.getCalculationSettings().getThreshold() != workbookAdditionalInfo.getCalculationSettings().getThreshold()))) || (!isItrCalc && workbook.getCalculationSettings() != null);
		boolean isStyleChanged = !( (fontName != null && fontName.equals(defaultCellStyle.getPropertyAsString(TextStyle.Property.FONTNAME, workbook.getTheme())) && (fontSize != null && fontSize.equals(defaultCellStyle.getPropertyAsString(TextStyle.Property.FONTSIZE, workbook.getTheme()))) && (fontColor != null && fontColor.equals(defaultCellStyle.getProperty(TextStyle.Property.COLOR)))));
		boolean isReCalcFormulaCells = isIterativeCalcChanged || isPatternSettingChanged;
		boolean isRegenerateContent = isTimeZoneChanged || isSpreadsheetSettingsChanged || isViewChanged;

		workbook.getWorkbookSettings().setWorkbookGridColor(gridColor, workbook.getTheme());
		if(isIterativeCalcChanged)
		{
			workbook.setCalculationSettings(isItrCalc ? workbookAdditionalInfo.getCalculationSettings() : null);
		}
		if(isSpreadsheetSettingsChanged)
		{
			Condition.changeDatesinConditionalFormat(workbook, workbook.getSpreadsheetSettings(), spreadsheetSettings);
			workbook.setSpreadsheetSettings(spreadsheetSettings);
		}
		if(isStyleChanged)
		{
			defaultCellStyle.setProperty(TextStyle.Property.FONTNAME, fontName);
			defaultCellStyle.setProperty(TextStyle.Property.FONTSIZE, fontSize);
			defaultCellStyle.setProperty(TextStyle.Property.COLOR, fontColor);

			workbook.invalidateDefaultRowHeightCached();
			workbook.resetHeightsForDefaultCellStyle(false);
			workbook.setNamedStylesChanged(true);
		}
		if(isSheetDirectionChanged)
		{
			for(Sheet sheet : workbook.getSheetList())
			{
				SheetStyle style = sheet.getSheetStyle();
				style.setProperty(SheetStyle.Property.WRITINGMODE, spreadsheetSettings.getDisplayDirection().getWritingMode());
				sheet.setSheetStyle(style);
			}
		}

		List<Cell> changedCells = new ArrayList<>();
		if(isRegenerateContent)
		{
			long startTime = System.currentTimeMillis();
			changedCells.addAll(workbook.regenerateContent(isReCalcFormulaCells));
			long timeTaken = System.currentTimeMillis() - startTime;
			if(timeTaken > 2000)
			{
				LOGGER.log(Level.INFO, "[DOCUMENT_SETTINGS] Total time taken to regenerate the content is:{0} no of changedCells are:{1}", new Object[]{timeTaken, changedCells.size()});
			}
		}
		else if(isReCalcFormulaCells)
		{
			changedCells.addAll(workbook.recalculateFormulaCells());
		}
		return changedCells;
	}

	public static void validateSpreadsheetSettings(JSONObjectWrapper actionJSON) throws Exception
	{
		if(actionJSON.has(JSONConstants.SPREADSHEET_SETTINGS_NEW))
		{
			JSONObjectWrapper docSettings = actionJSON.getJSONObject(JSONConstants.SPREADSHEET_SETTINGS_NEW);
			JSONObjectWrapper localeJSON = docSettings.getJSONObject(JSONConstants.LOCALE);
			char decimalSep = (char)localeJSON.getInt(JSONConstants.DECIMAL_SEP);
			char thousandSep = (char)localeJSON.getInt(JSONConstants.THOUSAND_SEP);
			SpreadsheetSettings.DecimalSeparator decimalSeparator = SpreadsheetSettings.DecimalSeparator.getDecimalSeparator(decimalSep);
			SpreadsheetSettings.ThousandSeparator thousandSeparator = SpreadsheetSettings.ThousandSeparator.getThousandSeparator(thousandSep);
			if(decimalSeparator.getSeparator() == thousandSeparator.getSeparator())
			{
				LOGGER.log(Level.SEVERE, "[SPREADSHEET_SETTINGS][Exception] Decimal and Thousand Separator can't be same.");
				throw new Exception("[SPREADSHEET_SETTINGS][Exception] Decimal and Thousand Separator can't be same.");
			}
		}
	}

	public static List<Cell> setSpreadsheetSettings(WorkbookContainer container, Workbook workbook, JSONObjectWrapper actionJSON) throws Exception
	{
		JSONObjectWrapper newDocSettings = actionJSON.getJSONObject(JSONConstants.SPREADSHEET_SETTINGS_NEW);
		newDocSettings.put(JSONConstants.IS_NEW_SETTINGS, true);

		JSONObjectWrapper localeJSON = newDocSettings.getJSONObject(JSONConstants.LOCALE);
//        localeJSON.put(JSONConstants.LOCALE, workbook.getFunctionLocale().toString());
		localeJSON.put(JSONConstants.DICTIONARY_LOCALE, workbook.getDictionaryLocale().toString());

		JSONObjectWrapper fontJSON = newDocSettings.getJSONObject(JSONConstants.FONT);

		String fontName = fontJSON.optString(JSONConstants.FONT_NAME);
		String fontSize = fontJSON.optString(JSONConstants.FONT_SIZE);
		JSONObjectWrapper fontColorJSON = fontJSON.optJSONObject(JSONConstants.FONT_COLOR);
		// CLIENT WILL NOT SEND DEFAULT FONT COLOR. SO DEFAULT TEXT 1 FROM THEME COLOR SCHEME WILL BE SET.
		ZSColor fontColor = fontColorJSON != null && !fontColorJSON.isEmpty() ? ZSColor.getInstance(fontColorJSON) : ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.0);

		JSONObjectWrapper viewJSON = newDocSettings.getJSONObject(JSONConstants.VIEW);
		JSONObjectWrapper gridColorJSON = viewJSON.getJSONObject(JSONConstants.GRID_LINE_COLOR);
		ZSColor gridColor = ZSColor.getInstance(gridColorJSON);

		WorkbookAdditionalInfo workbookAdditionalInfo = WorkbookAdditionalInfo.getInstance(container, newDocSettings);
		updateResourceAdditionalInfo(container, workbookAdditionalInfo);
		return updateSpreadsheetSettings(container, workbook, fontName, fontSize, fontColor, gridColor, workbookAdditionalInfo);
	}

	public static List<Cell> setSpreadsheetSettings_Old(WorkbookContainer container, Workbook workbook, String fontName, String fontSize, String userDocLocale, String userDocTimeZone, boolean isSetDefault, String userZuid, String sheetDirection, Workbook.View view, String dictLocaleString, boolean isIterativeCalc, int maxNoOfItr, double threshold, Workbook.PatternSetting patternSetting)//tikeshwar sahu
	{
		Locale newLocale;
		if(userDocLocale != null)
		{
			String[] tempLocale = userDocLocale.split("_");//No I18N
			String languageCode = tempLocale[0];
			String countryCode = tempLocale[1];
			newLocale = LocaleUtil.getLocale(languageCode, countryCode);
		}
		else
		{
			newLocale = null;
		}
		Locale dictionaryLocale;
		if(dictLocaleString != null)
		{
			String[] tempLocale = dictLocaleString.split("_");//No I18N
			String languageCode = tempLocale[0];
			String countryCode = tempLocale[1];
			dictionaryLocale = LocaleUtil.getLocale(languageCode, countryCode);
		}
		else
		{
			dictionaryLocale = null;
		}
		boolean[] isRegenerateContent = setSpreadsheetSettings_Old(container, workbook, fontName, fontSize, newLocale, userDocTimeZone, sheetDirection != null ? (Boolean.parseBoolean(sheetDirection) ? SpreadsheetSettings.DisplayDirection.RIGHT_TO_LEFT : SpreadsheetSettings.DisplayDirection.LEFT_TO_RIGHT) : SpreadsheetSettings.DisplayDirection.LEFT_TO_RIGHT, view, dictionaryLocale, isIterativeCalc, maxNoOfItr, threshold, patternSetting);
		//Not Used anymore - If there is no more such action, parameters "userZuid" and "isSetDefault" itself should be removed
//        if(isSetDefault)
//        {
//            UserProfile userProfile = container.getUserProfile(userZuid);
//            long zuid = Long.parseLong(userProfile.getZUserId());
//            List<JSONObject> list = new ArrayList<>();
//            JSONObject jObjec = EngineUtils.getInstance().readUserProperty(zuid, container.getDocOwner());
//            String key = "UserLocale";//No I18N
//            jObjec.remove(key);
//            String key1 = "UserTimeZone";//No I18N
//            jObjec.remove(key1);
//            String key2 = "sheetDir"; //NO I18N
//
//            jObjec.put(key, userDocLocale);
//            jObjec.put(key1, userDocTimeZone);
//            jObjec.put(key2, sheetDirection);
//            list.add(jObjec);
//            EngineUtils.getInstance().saveUserProperty(zuid, container.getDocOwner(), list);
//        }
		long s = System.currentTimeMillis();
		List<Cell> changedCells = isRegenerateContent[1] ? workbook.recalculateFormulaCells() : new ArrayList<>();
		if(isRegenerateContent[0])
		{
			changedCells.addAll(workbook.regenerateContent(isRegenerateContent[1]));
			try {
				PivotUtil.refreshAllPivot(workbook);
			}
			catch (Exception e)
			{
				LOGGER.log(Level.INFO, "[Pivot] error while regenerateContent", e);
			}
		}
		CellStyle defaultCellStyle = workbook.getDefaultCellStyle();
		defaultCellStyle.setProperty(TextStyle.Property.FONTNAME, fontName);
		defaultCellStyle.setProperty(TextStyle.Property.FONTSIZE, fontSize);
		workbook.invalidateDefaultRowHeightCached();
		workbook.resetHeightsForDefaultCellStyle(false);
		workbook.setNamedStylesChanged(true);

		if(sheetDirection != null) {
			for(int i= 0; i<workbook.getSheetCount();i++){
				SheetStyle tstyle = workbook.getSheet(i).getSheetStyle();
				if(Boolean.parseBoolean(sheetDirection))
				{
					tstyle.setProperty(SheetStyle.Property.WRITINGMODE, "rl-tb"); //NO I18N
				}
				else
				{
					tstyle.setProperty(SheetStyle.Property.WRITINGMODE, "lr-tb"); //NO I18N
				}
				workbook.getSheet(i).setSheetStyle(tstyle);
			}
		}

		long timeTaken = System.currentTimeMillis() - s;
		if(timeTaken > 2000)
		{
			LOGGER.log(Level.INFO, "[DOCUMENT_SETTINGS] Total time taken to regenerate the content is:{0} no of changedCells are:{1}", new Object[]{timeTaken, changedCells.size()});
		}
		return changedCells;
	}

	public static JSONObjectWrapper fetchSpreadsheetSettings_Old(WorkbookContainer container, String userLocale, boolean isLocaleListUpdated) throws Exception{
	    JSONObjectWrapper localeSettings = new JSONObjectWrapper();
	    WorkbookAdditionalInfo workbookAdditionalInfo = container.getWorkbookAdditionalInfo();
	    Workbook workBook = container.getWorkbook(CurrentRealm.getWorkBookIdentity());
	    CellStyle defaultCellStyle = workBook.getDefaultCellStyle();
	    Locale wbLocale = workbookAdditionalInfo.getSpreadsheetLocale();
	    TimeZone timezone = workbookAdditionalInfo.getTimeZone();

	    String sheetDirection = String.valueOf(workbookAdditionalInfo.getSpreadsheetSettings().getDisplayDirection().equals(SpreadsheetSettings.DisplayDirection.RIGHT_TO_LEFT));

	    String currentLocale = wbLocale.toString();
	    String[] splitLocale = currentLocale.split("_");
	    String languageCode = splitLocale[0];
	    String countryCode = splitLocale[1];

	    String lastTimeZone = timezone.getID();

	    boolean isItrCalc = workbookAdditionalInfo.isIterativeCalc();
	    int maxNoOfItr = workbookAdditionalInfo.isIterativeCalc() ? workbookAdditionalInfo.getCalculationSettings().getIterationCount() : EngineConstants.DEFAULT_MAX_NUMBER_OF_ITERATIONS;
	    double threshold = workbookAdditionalInfo.isIterativeCalc() ? workbookAdditionalInfo.getCalculationSettings().getThreshold() : EngineConstants.DEFAULT_THRESHOLD_VALUE;

	    int patternSetting = workbookAdditionalInfo.getPatternSetting().getId();
	    String fontName = (String) defaultCellStyle.getProperty(TextStyle.Property.FONTNAME);
	    String fontSize = (String) defaultCellStyle.getProperty(TextStyle.Property.FONTSIZE);
	    String fontColor = ZSColor.getHexColor((ZSColor) defaultCellStyle.getProperty(TextStyle.Property.COLOR), workBook.getTheme());
	    String defaultGridColor = ActionUtil.getWorkbookGridColorHexCode(workBook);
	    String wblastTime = (String) workBook.getUserTimezone().getID();

	    localeSettings.put(String.valueOf(CommandConstants.LANGUAGE), languageCode);
	    localeSettings.put(String.valueOf(CommandConstants.COUNTRY), countryCode);
	    localeSettings.put(String.valueOf(CommandConstants.TIME_ZONE), lastTimeZone);
	    localeSettings.put(String.valueOf(CommandConstants.SHEET_DIRECTION), sheetDirection);

	    localeSettings.put(String.valueOf(CommandConstants.IS_ITERATIVE_CALCULATION), isItrCalc);
	    localeSettings.put(String.valueOf(CommandConstants.MAX_NUMBER_ITERATIONS), maxNoOfItr);
	    localeSettings.put(String.valueOf(CommandConstants.THRESHOLD), threshold);
	    localeSettings.put(String.valueOf(CommandConstants.PATTERN_SETTING), patternSetting);

	    localeSettings.put(String.valueOf(CommandConstants.FONT_NAME), fontName);
	    localeSettings.put(String.valueOf(CommandConstants.FONT_SIZE), fontSize);
	    localeSettings.put(String.valueOf(CommandConstants.DEFAULT_FONT_COLOR), fontColor);
	    localeSettings.put(String.valueOf(CommandConstants.DEFAULT_GRID_COLOR), defaultGridColor);

	    if(!isLocaleListUpdated){
	        JSONObjectWrapper localeList = LocaleUtil.getLocaleListWithTimeZone_Old();
	        localeSettings.put(String.valueOf(CommandConstants.LOCALE_LIST), localeList);
	    }

	    return localeSettings;
	}

	public static JSONObjectWrapper fetchSpreadsheetSettings(WorkbookContainer container) throws Exception{
	    WorkbookAdditionalInfo additionalInfo = container.getWorkbookAdditionalInfo();
	    return additionalInfo.getJSON(container.getWorkbook(null));
	}

}
