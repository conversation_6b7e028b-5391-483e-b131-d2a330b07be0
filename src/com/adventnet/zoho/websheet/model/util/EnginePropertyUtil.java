//$Id$
package com.adventnet.zoho.websheet.model.util;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.google.gson.*;
import com.google.gson.stream.JsonReader;
import com.zoho.conf.Configuration;
import com.zoho.sas.container.AppResources;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.util.Enumeration;
import java.util.List;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

public class EnginePropertyUtil
{
    private static Properties engineProperties = new Properties();
    private static Properties versionProperties = new Properties();
    private static Properties sheetProperties = new Properties();
    private static Properties countryCodeToCDNMap = new Properties();
    private static Properties handheldProperties = new Properties();

    private static Properties stockDataFeedProperties = new Properties();
    private static Properties stockSymbolsDataFeedMapping = new Properties();
    private static Properties dataConnectionProperties = new Properties();

    public  static Logger logger = Logger.getLogger(EnginePropertyUtil.class.getName());


	public static final String PICKLIST = "PICKLIST"; // No I18N
	public static final String ADMIN_CHANGE_ORG_LOCALE = "ADMIN_CHANGE_ORG_LOCALE"; // No I18N
	static
    {
	try
	{
	    initEngineProps();
	    initVersionProps();
	    initSheetProps();
		initCDNProps();
	    initHandheldProps();
	    initStockDataFeedProps();
	    initStockSymbolDataFeedMapping();
	    initDataConnectionProps();
	}
	catch (Exception e)
	{
	    // Do Nothing
	}
    }

    private static void initEngineProps() throws Exception
    {
	engineProperties = new Properties();
	FileInputStream fis = null;
	try
	{
	    fis = new FileInputStream(new File(Configuration.getString("app.home") + "/conf/engineproperties.conf"));
	    engineProperties.load(fis);
	}
	finally
	{
	    if (fis != null)
	    {
		fis.close();
	    }
	}
    }

    private static void initVersionProps() throws Exception
    {
	versionProperties = new Properties();
	FileInputStream fis = null;
	try
	{

	    fis = new FileInputStream(new File(Configuration.getString("app.home") + "/conf/sheetversions.conf"));
	    versionProperties.load(fis);
	}
	finally
	{
	    if (fis != null)
	    {
		fis.close();
	    }
	}
    }

    private static void initSheetProps() throws Exception
    {
	sheetProperties = new Properties();
	FileInputStream fis = null;
	try
	{
	    fis = new FileInputStream(new File(Configuration.getString("app.home") + "/conf/sheetproperties.conf"));
	    sheetProperties.load(fis);
	}
	catch(Exception e){
		logger.info("Exception while initSheetProps :"+e);
	}
	finally
	{
	    if (fis != null)
	    {
		fis.close();
	    }
	}
    }

	private static void initCDNProps() throws Exception
    {
		countryCodeToCDNMap = new Properties();
		FileInputStream fis = null;
		try
		{
			fis = new FileInputStream(new File(Configuration.getString("app.home") + "/conf/countryCodeCDNMap.conf"));
			countryCodeToCDNMap.load(fis);
		}
		catch(Exception e){
			logger.info("Exception while initCDNProps :"+e);
		}
		finally
		{
			if (fis != null)
			{
			fis.close();
			}
		}
    }

    public static JsonObject getStaticMap(){

        JsonObject jsonMap = new JsonObject();
        try{

            String appHome = Configuration.getString("app.home");

            String appsheetPath = appHome +"/../sheet/appsheet";//NO I18N
            FileReader fileReader = new FileReader(appsheetPath + "/json/staticMap.json");//NO I18N
            JsonParser jsonParser = new JsonParser();

            JsonElement jsonElement = jsonParser.parse(new JsonReader(fileReader));
            jsonMap = jsonElement.getAsJsonObject();

        } catch(JsonIOException | JsonSyntaxException | FileNotFoundException e){

            logger.log(Level.INFO, "Exception while reading CDN static map >> {0}", e);
        }

        return jsonMap;
    }
	public static JsonObject getIntegrityHashMap(){

		JsonObject jsonMap = new JsonObject();
		try{

			String appHome = Configuration.getString("app.home");

			String appsheetPath = appHome +"/../sheet/appsheet";//NO I18N
			FileReader fileReader = new FileReader(appsheetPath + "/json/integrityHashMap.json");//NO I18N
			JsonParser jsonParser = new JsonParser();

			JsonElement jsonElement = jsonParser.parse(new JsonReader(fileReader));
			jsonMap = jsonElement.getAsJsonObject();

		} catch(JsonIOException | JsonSyntaxException | FileNotFoundException e){

			logger.log(Level.INFO, "Exception while reading SubResourceIntegrity Hash Map >> {0}", new Object[]{e});
		}

		return jsonMap;
	}
	public static JsonObject integrityHashViewBasedMap(){

		JsonObject jsonMap = new JsonObject();
		try{

			String appHome = Configuration.getString("app.home");

			String appsheetPath = appHome +"/../sheet/appsheet";//NO I18N
			FileReader fileReader = new FileReader(appsheetPath + "/json/integrityHashViewBasedMap.json");//NO I18N
			JsonParser jsonParser = new JsonParser();

			JsonElement jsonElement = jsonParser.parse(new JsonReader(fileReader));
			jsonMap = jsonElement.getAsJsonObject();

		} catch(JsonIOException | JsonSyntaxException | FileNotFoundException e){

			logger.log(Level.INFO, "Exception while reading View Based SubResourceIntegrity Hash Map >> {0}", new Object[]{e});
		}

		return jsonMap;
	}

    public static JsonObject getclientStaticMap(){

        JsonObject jsonMap = new JsonObject();
        try{

            String appHome = Configuration.getString("app.home");

            String appsheetPath = appHome +"/../sheet/appsheet";//NO I18N
            FileReader fileReader = new FileReader(appsheetPath + "/json/clientStaticMap.json");
            JsonParser jsonParser = new JsonParser();

            JsonElement jsonElement = jsonParser.parse(new JsonReader(fileReader));
            jsonMap = jsonElement.getAsJsonObject();

        } catch(JsonIOException | JsonSyntaxException | FileNotFoundException e){

            logger.log(Level.INFO, "Exception while reading CDN static map >> {0}", e);
        }

        return jsonMap;
    }

    public static JsonObject getPreviewStaticMap(){

        JsonObject jsonMap = new JsonObject();
        try{

            String appHome = Configuration.getString("app.home");
            String appsheetPath = appHome +"/../sheet/appsheet";//NO I18N
            FileReader fileReader = new FileReader(appsheetPath + "/conf/staticMap.json");
            JsonParser jsonParser = new JsonParser();

            JsonElement jsonElement = jsonParser.parse(new JsonReader(fileReader));
            jsonMap = jsonElement.getAsJsonObject();

        } catch(JsonIOException | JsonSyntaxException | FileNotFoundException e){

            logger.log(Level.INFO, "Exception while reading CDN static map >> {0}", e);
        }

        return jsonMap;
    }

	public static JsonObject getPublicTemplatesMap() {
		JsonObject jsonMap = new JsonObject();
		try {
			String appHome = Configuration.getString("app.home");

			String appsheetPath = appHome +"/conf";//NO I18N
			FileReader fileReader = new FileReader(appsheetPath + "/publictemplates.json");//NO I18N
			JsonParser jsonParser = new JsonParser();

			JsonElement jsonElement = jsonParser.parse(new JsonReader(fileReader));
			jsonMap = jsonElement.getAsJsonObject();
		}catch (Exception e){
			logger.log(Level.INFO, "Exception while reading publicTemplates map >>> {0}", e);
		}
		return jsonMap;
	}

    private static void initHandheldProps() throws Exception {
    	handheldProperties = new Properties();
    	FileInputStream fis = null;
    	try{
    		fis = new FileInputStream(new File(Configuration.getString("app.home") + "/conf/handheldproperties.conf"));
    		handheldProperties.load(fis);
    	} catch(Exception e) {
    		logger.info("Enception while initHandheldProps :"+ e);
    	}
    	finally {
    		if(fis != null) {
    			fis.close();
    		}
    	}
    }

	private static void initStockDataFeedProps() throws Exception {
		stockDataFeedProperties = new Properties();
		try (FileInputStream fis = new FileInputStream(new File(Configuration.getString("app.home") + "/conf/stockdatafeedproperties.conf"))) {
			stockDataFeedProperties.load(fis);
		}
	}

	private static void initStockSymbolDataFeedMapping() throws Exception {
		stockSymbolsDataFeedMapping = new Properties();
		try (FileInputStream fis = new FileInputStream(new File(Configuration.getString("app.home") + "/conf/stocksymboldatafeedmapping.conf"))) {
			stockSymbolsDataFeedMapping.load(fis);
		}
	}
	private static void initDataConnectionProps() throws Exception {
		dataConnectionProperties = new Properties();
		try (FileInputStream fis = new FileInputStream(new File(Configuration.getString("app.home") + "/conf/"+sheetProperties.getProperty("DATA_CONNECTIONS_URL_CONF_FILE")))) {
			dataConnectionProperties.load(fis);
		}
	}

	public static String getDataConnectionInfo(String propertyName) {
		String property = dataConnectionProperties.getProperty(propertyName);
		return property;
	}


	public static String getStockDataFeedPropertyValue(String propertyName) {
		String property = stockDataFeedProperties.getProperty(propertyName);
		return property;
	}

	public static String getDataFeedOfSymbol(String propertyName)
	{
		String property = stockSymbolsDataFeedMapping.getProperty(propertyName);
		return property;
	}

	public static void setDataFeedOfSymbol(String propertyName, String propertyValue) throws Exception {
		stockSymbolsDataFeedMapping.setProperty(propertyName, propertyValue);
		try(FileOutputStream fout = new FileOutputStream(new File(Configuration.getString("app.home") + "/conf/stocksymboldatafeedmapping.conf"))) //No I18N
		{
			stockSymbolsDataFeedMapping.store(fout, "added/modified dataFeed for symbol"); //No I18N
		}
	}

    public static String getEnginePropertyValue(String propertyName)
    {
		return engineProperties.getProperty(propertyName);
    }

    public static void setEnginePropertyValue(String propertyName, String propertyValue) throws Exception
    {
	FileOutputStream fout = null;
	try
	{
	    engineProperties.setProperty(propertyName, propertyValue);
	    fout = new FileOutputStream(new File(Configuration.getString("app.home") + "/conf/engineproperties.conf")); //No I18N
	    engineProperties.store(fout, "Engine"); //No I18N
	}
	catch(Exception e)
	{
	    // Do Nothing
	}
	finally
	{
	    if (fout != null)
	    {
		fout.close();
	    }
	}
    }


    public static String getVersionPropertyValue(String propertyName)
    {
	String property = versionProperties.getProperty(propertyName);
	return property;
    }

	public static void setSheetPropertyValue(String propertyName, String propertyValue) throws Exception
	{
		FileOutputStream fout = null;
		try
		{
			sheetProperties.setProperty(propertyName, propertyValue);
			fout = new FileOutputStream(new File(Configuration.getString("app.home") + "/conf/sheetproperties.conf")); //No I18N
			sheetProperties.store(fout, "SheetClient"); //No I18N
		}
		catch(Exception e)
		{
			// Do Nothing
		}
		finally
		{
			if (fout != null)
			{
				fout.close();
			}
		}
	}

    public static String getSheetPropertyValue(String propertyName)
    {
	String property = sheetProperties.getProperty(propertyName);
	return property;
    }

	public static String getCountryCodeToCDN(String propertyName)
    {
		return countryCodeToCDNMap.getProperty(propertyName);
    }

    public static String getHandheldPropertyValue(String propertyName) {
    	String property = handheldProperties.getProperty(propertyName);
    	return property;
    }

    public static String getOldFeatureComponentsVersion()
    {

            FileInputStream fis = null;
            String version = null;

            try {

                    File f = new File(Configuration.getString("app.home") + "/blog/oldfcomponents/blog/featurecomponents.txt");
                    fis = new FileInputStream(f);
                    BufferedReader reader = new BufferedReader(new InputStreamReader(fis));
                    Properties properties = new Properties();
                    properties.load(reader);
                    //version = IOUtils.toString(fis).trim();
                    version = properties.getProperty("StaticVersion").trim();

            }
            catch(FileNotFoundException f)
            {
                    logger.info("FILE NOT FOUND EXCEPTION - while reading /blog/fcomponents_static.txt");
            }
            catch(IOException i) {
                    logger.info("IO EXCEPTION - while reading /blog/fcomponents_static.txt");
            }
            finally
            {
                    IOUtils.closeQuietly(fis);
            }

            return version;
    }

    public static String getFeatureComponentsVersion()
    {

            FileInputStream fis = null;
            String version = null;

            try {

                    File f = new File(Configuration.getString("app.home") + "/blog/featurecomponents.txt");
                    fis = new FileInputStream(f);
                    BufferedReader reader = new BufferedReader(new InputStreamReader(fis));
                    Properties properties = new Properties();
                    properties.load(reader);
                    //version = IOUtils.toString(fis).trim();
                    version = properties.getProperty("StaticVersion").trim();

            }
            catch(FileNotFoundException f)
            {
                    logger.info("FILE NOT FOUND EXCEPTION - while reading /blog/fcomponents_static.txt");
            }
            catch(IOException i) {
                    logger.info("IO EXCEPTION - while reading /blog/fcomponents_static.txt");
            }
            finally
            {
                    IOUtils.closeQuietly(fis);
            }

            return version;
    }
        public static String getVersionLabel()
        {
            String setup = getSheetPropertyValue("SETUP"); //No I18N
            if(setup != null){
                FileInputStream fis = null;
                String label = null;
                Properties prop = new Properties();
                try {
                     File f = new File(Configuration.getString("app.home") + "/blog/zohowebsheet.txt");
                     fis = new FileInputStream(f);
                     prop.load(fis);
                     label = prop.getProperty("BuildLabel");
                }
                catch(FileNotFoundException f)
                {
                        logger.info("FILE NOT FOUND EXCEPTION - while reading /blog/zohowebsheet.txt");
                }
                catch(IOException i) {
                            logger.info("IO EXCEPTION - while reading /blog/zohowebsheet.txt");
                }
                finally
                {
                        IOUtils.closeQuietly(fis);
                }

                return label;
            }
            else{
                return null;
            }

        }

        public static String getVersion() {
                FileInputStream fis = null;
                Properties prop = new Properties();
                String staticVersion = "";
                try {
                        File f = new File(Configuration.getString("app.home") + "/blog/zohowebsheet.txt");

                        fis = new FileInputStream(f);
                        prop.load(fis);

                        staticVersion = prop.getProperty("StaticVersion");

                } catch (FileNotFoundException f) {
                        logger.info("FILE NOT FOUND EXCEPTION - while reading /blog/zohowebsheet.txt");
                } catch (IOException i) {
                        logger.info("IO EXCEPTION - while reading /blog/zohowebsheet.txt");
                } finally {
                        IOUtils.closeQuietly(fis);
                }

                return staticVersion;
        }

		public static String getChartJSVersion() {
			String buildVersion = ""; // Should update this string if any patch update is done.
			if(!buildVersion.equalsIgnoreCase("")) {
				return buildVersion;
			}
			FileInputStream fis = null;
			Properties prop = new Properties();
			String staticVersion = "";
			try {
				File f = new File(Configuration.getString("app.home") + "/blog/version.txt");

				fis = new FileInputStream(f);
				prop.load(fis);

				staticVersion = prop.getProperty("StaticVersion");

			} catch (FileNotFoundException f) {
				logger.info("FILE NOT FOUND EXCEPTION - while reading /blog/version.txt");
			} catch (IOException i) {
				logger.info("IO EXCEPTION - while reading /blog/version.txt");
			} finally {
				IOUtils.closeQuietly(fis);
			}

			return staticVersion;
		}

        public static int getJsSplitCount()
	{
		FileInputStream fis = null;
		int count = 1;
                Properties prop = new Properties();
		try {

			File f = new File(Configuration.getString("app.home") + "/conf/build.conf");
			fis = new FileInputStream(f);
                        prop.load(fis);
                        count = Integer.parseInt(prop.getProperty("JS_SPLIT_COUNT"));

		}
		catch(FileNotFoundException f)
		{
			logger.info("FILE NOT FOUND EXCEPTION - while reading /conf/build.conf");
		}
		catch(IOException i) {
                            logger.info("IO EXCEPTION - while reading /conf/build.conf");
		}
		finally
		{
			IOUtils.closeQuietly(fis);
		}

		return count;
	}

	public static String gethtmlLocalVersion()
	{

		FileInputStream fis = null;
		String version = null;

		try {

			File f = new File(Configuration.getString("app.home") + "/conf/htmllocalversion.txt");
			fis = new FileInputStream(f);
			version = IOUtils.toString(fis).trim();

		}
		catch(FileNotFoundException f)
		{
			logger.info("FILE NOT FOUND EXCEPTION - while reading //conf/htmllocalversion.txt");
		}
		catch(IOException i) {
			logger.info("IO EXCEPTION - while reading /conf/htmllocalvesrion.txt");
		}
		finally
		{
			IOUtils.closeQuietly(fis);
		}

		return version;
	}

	public static String readContentSecurityPolicyHeader() {

		FileInputStream fis = null;

		Properties CSPProperties = new Properties();
		StringBuilder sb = new StringBuilder();

		try {

			File f = new File(Configuration.getString("app.home") + "/conf/content_security_policy.properties");
			fis = new FileInputStream(f);
			CSPProperties.load(fis);

			Enumeration e = CSPProperties.keys();
			while( e.hasMoreElements() ) {

				String key = (String) e.nextElement();
				sb.append(key).append(" ").append(CSPProperties.get(key)).append(";");

			}


		} catch (FileNotFoundException f) {
			logger.info("FILE NOT FOUND EXCEPTION - while reading //conf/htmllocalversion.txt");
		} catch (IOException i) {
			logger.info("IO EXCEPTION - while reading /conf/htmllocalvesrion.txt");
		} finally {
			IOUtils.closeQuietly(fis);
		}
		return sb.toString();
	}

        public static JSONObjectWrapper readZSTheme() {

            String filePath = AppResources.getProperty("app.contextPath") + File.separator + "sheet" + File.separator + "appsheet" + File.separator + "json" + File.separator + "Themes.json"; //No I18N

            JSONObjectWrapper json = null;
            FileInputStream fis = null;

            try {

                File f = new File(filePath);
                fis = new FileInputStream(f);
                String strjson = IOUtils.toString(fis).trim();
                json = new JSONObjectWrapper(strjson);

            } catch (FileNotFoundException f) {

                logger.log(Level.INFO, "FILE NOT FOUND EXCEPTION - while reading {0}", filePath);

            } catch (IOException i) {

                logger.log(Level.INFO, "IO EXCEPTION - while reading {0}", filePath);

            } finally {

                IOUtils.closeQuietly(fis);
            }

            return json;
	}

	public static Boolean isPicklistEnabled(Long zoid) {
		return getEnginePropertyValue(PICKLIST) != null && Boolean.parseBoolean(getEnginePropertyValue(PICKLIST));
	}

	public static boolean isAdminChangeOrgLocaleEnabled()
	{
		return getEnginePropertyValue(ADMIN_CHANGE_ORG_LOCALE) != null && Boolean.parseBoolean(getEnginePropertyValue(ADMIN_CHANGE_ORG_LOCALE));
	}

}
