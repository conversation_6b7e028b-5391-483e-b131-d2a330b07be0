//$Id$
package com.adventnet.zoho.websheet.model.util;

import com.adventnet.dfs.DFSException;
import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.SortColumn;
import com.adventnet.ds.query.Table;
import com.adventnet.iam.IAMException;
import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.User;
import com.adventnet.iam.UserContact;
import com.adventnet.persistence.DataAccessException;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.dd.FRAGMENTVERSIONDFSSTORE;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.beans.JobSchedulerBean;
import com.adventnet.zoho.websheet.model.externalRange.WorkbookLink;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.ziputil.DelugeIdMigrationHelper;
import com.adventnet.zoho.websheet.model.ziputil.ReplaceInXml.DataConnectionIdInStableObjectsNamedRange;
import com.adventnet.zoho.websheet.model.ziputil.ReplaceInXml.DelugeIdInSheetExpression;
import com.adventnet.zoho.websheet.model.ziputil.ReplaceInXml.DelugeIdInTableColumn;
import com.adventnet.zoho.websheet.model.ziputil.ReplaceInXml.ReplaceInValueI;
import com.adventnet.zoho.websheet.model.ziputil.ReplaceInXml.ZUIDInFilterFilterView;
import com.adventnet.zoho.websheet.model.ziputil.ReplaceInXml.ReplaceInElementNameAndAttributeI;
import com.adventnet.zoho.websheet.model.ziputil.ReplaceInXml.ZUIDInSheetTableProtection;
import com.adventnet.zoho.websheet.model.ziputil.ReplaceInZip;
import com.adventnet.zoho.websheet.model.zs.ZSZipInputStream;
import com.adventnet.zoho.websheet.model.zsparser.OrderFileReader;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.StoreUtil;
import com.zoho.scheduler.RepetitiveJob;
import com.zoho.sheet.action.externaldata.Task.TaskManager;
import com.zoho.sheet.action.externaldata.metastorage.DBActionManager;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.sheet.zuidMigration.helper.ZUIDMigrationHelper;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import org.apache.commons.io.IOUtils;
import org.json.JSONObject;
import org.xmlpull.v1.XmlPullParserException;

import java.io.*;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

public class DCMigrationUtil {
    public static final String DC_MIGRATION_FILES_IMPORTRANGE_LINKS_JSON = "dc_migration_files/importrange_links.json";//No I18N
    private static final Logger LOGGER = Logger.getLogger(DCMigrationUtil.class.getName());
    public static final String DC_MIGRATION_FILES_PREFIX = "dc_migration_files/";//No_I8N;
    public static final String DC_MIGRATION_FILES_CELL_EDIT_HISTORIES = "dc_migration_files" + File.separator + ZSStore.FileName.CELL_EDIT_HISTORIES;
    public static final String DC_MIGRATION_FILES_ACTIONS = "dc_migration_files" + File.separator + ZSStore.FileName.ACTIONS;
    public static final String DC_MIGRATION_FILES_VACTIONS = "dc_migration_files" + File.separator + ZSStore.FileName.VACTIONS;
    public static final String DC_MIGRATION_FILES_UNVERSIONEDACTIONS = "dc_migration_files" + File.separator + ZSStore.FileName.UNVERSIONEDACTIONS;
    public static final String DC_MIGRATION_FILES_DATACONNECTIONS = DC_MIGRATION_FILES_PREFIX + "dataconnections.json";//No I18N
    public static final String DC_MIGRATION_FILES_DISCUSSIONS = DC_MIGRATION_FILES_PREFIX + "discussions.json";//No I18N
    public static final String DC_MIGRATION_FILES_NOTIFICATION_SETTINGS = DC_MIGRATION_FILES_PREFIX + "notificationsettings.json";//No I18N
    public static final String DC_MIGRATION_FILES_RANGE_PUBLISH = DC_MIGRATION_FILES_PREFIX + "publish.json";//No I18N
    public static final String DC_MIGRATION_FILES_DOCUMENT_VERSION = DC_MIGRATION_FILES_PREFIX + "document_version_db.json";//No I18N
    public static final String DC_MIGRATION_FILES_VSEARCH_FL = DC_MIGRATION_FILES_PREFIX + "vsearch_fl.zip";//No I18N


    public static byte[] export_dc_migration_files(byte[] byteArrayZSFiles, WorkbookContainer container, String zfsngVersionId, boolean isTopVersion, boolean isAllVersionMigration) throws Exception {
        List<String> zip_entry_list = new ArrayList<>();
        StringBuilder export_stats = new StringBuilder("dc migration logs : export migration files ");//No I18N
        export_stats.append(container.getResourceId()).append("\n");
        try(ZSZipInputStream zipInputStream = new ZSZipInputStream(new ByteArrayInputStream(byteArrayZSFiles)); ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            try(ZipOutputStream outputStream = new ZipOutputStream(byteArrayOutputStream)) {
                for(ZipEntry zipEntry = zipInputStream.getNextEntry(); zipEntry != null; zipEntry = zipInputStream.getNextEntry()) {
                    Instant start = Instant.now();
                    String name = zipEntry.getName();
                    zip_entry_list.add(name);
                    ZipEntry zipEntry1 = new ZipEntry(name);
                    outputStream.putNextEntry(zipEntry1);
                    IOUtils.copy(zipInputStream, outputStream);
                    export_stats.append(name).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
                }

                if(isAllVersionMigration) {
                    Instant start = Instant.now();
                    try{
	                    LOGGER.log(Level.INFO, "getversionrow");
	                    JSONObjectWrapper versionMeta = getVersionRowDetails(container.getDocId(), container.getDocOwner(), zfsngVersionId);
	                    if(versionMeta != null && !versionMeta.isEmpty()) {
	                        outputStream.putNextEntry(new ZipEntry(DC_MIGRATION_FILES_DOCUMENT_VERSION));
	                        outputStream.write(versionMeta.toString().getBytes());
	                        export_stats.append(DC_MIGRATION_FILES_DOCUMENT_VERSION).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	                    }
                    }catch(Exception e) {
	                	LOGGER.log(Level.WARNING, "[ZSHEET] DC_MIGRATION_FILES_DOCUMENT_VERSION error ", e);
	                }
                }
                if(isTopVersion){
	
	                // additional_info.json
	                
                	 try{
                		 
	                	LOGGER.log(Level.INFO, "get additional info");
	                    Instant start = Instant.now();
	                    String zipEntryName = DC_MIGRATION_FILES_PREFIX+ZSStore.FileName.ADDITIONAL_INFO.toString().toLowerCase() + "." + ZSStore.FileExtn.JSON.toString().toLowerCase();
	                    outputStream.putNextEntry(new ZipEntry(zipEntryName));
	                    outputStream.write(WorkbookAdditionalInfo.getInstance(container).getJSON().toString().getBytes());
	                    export_stats.append(zipEntryName).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	                }catch(Exception e) {
	                	LOGGER.log(Level.WARNING, "[ZSHEET] ADDITIONAL_INFO error ", e);
	                }
	
	                try{
	                	LOGGER.log(Level.INFO, "get data connections info");
	                    Instant start = Instant.now();
	                    JSONArrayWrapper dataConnectionMetaForDocument = getDataConnectionMetaForDocument(container.getDocOwner(), container.getDocId());
	                    if(dataConnectionMetaForDocument != null && !dataConnectionMetaForDocument.isEmpty()) {
	                        outputStream.putNextEntry(new ZipEntry(DC_MIGRATION_FILES_DATACONNECTIONS));
	                        outputStream.write(dataConnectionMetaForDocument.toString().getBytes());
	                        export_stats.append(DC_MIGRATION_FILES_DATACONNECTIONS).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	                    }
	                }catch(Exception e) {
	                	LOGGER.log(Level.WARNING, "[ZSHEET] DC_MIGRATION_FILES_DATACONNECTIONS error ", e);
	                }
	
	                //Discussions
	                try{
	                    Instant start = Instant.now();
	                    LOGGER.log(Level.INFO, "get discussions info");
	                    JSONArrayWrapper discussionMeta = getCommentsJSON(container.getDocOwner(), container.getDocId());
	                    if(discussionMeta != null && !discussionMeta.isEmpty()) {
	                        outputStream.putNextEntry(new ZipEntry(DC_MIGRATION_FILES_DISCUSSIONS));
	                        outputStream.write(discussionMeta.toString().getBytes());
	                        export_stats.append(DC_MIGRATION_FILES_DISCUSSIONS).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	                    }
	                }catch(Exception e) {
	                	LOGGER.log(Level.WARNING, "[ZSHEET] DC_MIGRATION_FILES_DISCUSSIONS error ", e);
	                }
	                
	              //Notification settings

                    try{
                        Instant start = Instant.now();
                        LOGGER.log(Level.INFO, "get Notification settings info");
                        JSONArrayWrapper notificationMeta = getNotificationJSON(container.getDocOwner(), container.getDocId());
                        if(notificationMeta != null && !notificationMeta.isEmpty()) {
                            outputStream.putNextEntry(new ZipEntry(DC_MIGRATION_FILES_NOTIFICATION_SETTINGS));
                            outputStream.write(notificationMeta.toString().getBytes());
                            export_stats.append(DC_MIGRATION_FILES_NOTIFICATION_SETTINGS).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
                        }
                    }catch(Exception e) {
                        LOGGER.log(Level.WARNING, "[ZSHEET] DC_MIGRATION_FILES_NOTIFICATION_SETTINGS error ", e);
                    }

	      
	                //Published Range
	                try{
	                    Instant start = Instant.now();
	                    LOGGER.log(Level.INFO, "get publish info");
	                    JSONObjectWrapper publishedMeta = getPublishedRangeDetails(container.getDocOwner(), container.getDocId(), container.getCreatorZUID());
	                    if(publishedMeta != null && !publishedMeta.isEmpty()) {
	                        outputStream.putNextEntry(new ZipEntry(DC_MIGRATION_FILES_RANGE_PUBLISH));
	                        outputStream.write(publishedMeta.toString().getBytes());
	                        export_stats.append(DC_MIGRATION_FILES_RANGE_PUBLISH).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	                    }
	                }catch(Exception e) {
	                	LOGGER.log(Level.WARNING, "[ZSHEET] DC_MIGRATION_FILES_RANGE_PUBLISH error ", e);
	                }
	                
	                try{
	                {
	                    Instant start = Instant.now();

                        WorkbookLink workbookLink = new WorkbookLink();
                        workbookLink.reinitializeWorkbookLinks(container.getResourceId(), container.getDocOwner());
                        workbookLink.export_dc_migration_importrange_file(outputStream, container.getResourceId());
	                    export_stats.append(DC_MIGRATION_FILES_IMPORTRANGE_LINKS_JSON).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	                }
	                }catch(Exception e) {
	                	 LOGGER.log(Level.WARNING, "[ZSHEET] DC_MIGRATION_FILES_IMPORTRANGE_LINKS_JSON error ", e);
	                }
	
	               // String currentVersion = CurrentRealm.getWorkBookIdentity();
	                //String topVersion = ZohoFS.getTopVersion(container.getDocOwner(), container.getResourceId());
	                //if(currentVersion != null )
	                //{
	                  //  if(currentVersion.equals(topVersion))
	                    //{
	                        try
	                        {
	                            Instant start = Instant.now();
	                            Long fragId = container.getFragmentId(ZSStore.FileName.ACTIONS, false, null);
	                            writeActionsJSONInZSheet(container, outputStream, fragId, ZSStore.FileName.ACTIONS, DC_MIGRATION_FILES_ACTIONS);
	                            export_stats.append(DC_MIGRATION_FILES_ACTIONS).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	
	                            start = Instant.now();
	                            fragId = container.getFragmentId(ZSStore.FileName.UNVERSIONEDACTIONS, false, null);
	                            writeActionsJSONInZSheet(container, outputStream, fragId, ZSStore.FileName.UNVERSIONEDACTIONS, DC_MIGRATION_FILES_UNVERSIONEDACTIONS);
	                            export_stats.append(DC_MIGRATION_FILES_UNVERSIONEDACTIONS).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	                        } catch (Exception e) {
	                            LOGGER.log(Level.WARNING, "[ZSHEET] [ACTION] Exception while writing actions.json in ZSheet", e);
	                        }
	
	                        try {
	                            List<String> asnList = new ArrayList<>();
	                            for(String entry_name : zip_entry_list) {
	                                if(entry_name.matches("^[0-9]+#/[0-9]+#\\.xml")) {
	                                    asnList.add(entry_name.substring(0, entry_name.indexOf("/")));
	                                }
	                            }
	                            Instant start = Instant.now();
	                            EngineUtils1.writeCellEditHistoryInZSheet(container, asnList, outputStream, DC_MIGRATION_FILES_CELL_EDIT_HISTORIES + File.separator);
	                            export_stats.append(DC_MIGRATION_FILES_CELL_EDIT_HISTORIES).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	                        } catch (Exception e)
	                        {
	                            LOGGER.log(Level.WARNING, "[ZSHEET] [CELL_EDIT_HISTORY] Exception while writing cell edit history in ZSheet", e);
	                        }
	                    }
                        Long  versionId = null;
	                    try
	                    {
	                        Instant start = Instant.now();
	                       // String zfsngVersionId = ZohoFS.getVersionIDForEditor(container.getDocsSpaceId(), container.getResourceKey(), zfsngVersionId);
                            versionId = DocumentUtils.getVersionIdforZFSNGVersion(Long.parseLong(container.getDocId()), container.getDocOwner(), zfsngVersionId);
	                        writeActionsJSONInZSheet(container, outputStream, versionId, ZSStore.FileName.VACTIONS, DC_MIGRATION_FILES_VACTIONS);
	                        export_stats.append(DC_MIGRATION_FILES_VACTIONS).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
	                    } catch (Exception e) {
	                        LOGGER.log(Level.WARNING, "[ZSHEET] [VACTION] Exception while writing vactions.json in ZSheet", e);
	                    }

                        try {
                            Instant start = Instant.now();
                            writeVSearch_FL(container, outputStream, versionId);
                            export_stats.append(DC_MIGRATION_FILES_VSEARCH_FL).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
                        } catch(Exception e) {
                            LOGGER.log(Level.OFF, "failed to write "+DC_MIGRATION_FILES_VSEARCH_FL, e);
                        }
	                //}
                //}
            }
            byteArrayOutputStream.close();
            return byteArrayOutputStream.toByteArray();
        } finally {
            LOGGER.log(Level.INFO, export_stats.toString());
        }
    }

    /**
     * replace zuid, delugeId and dataConnectionId.
     *
     * @param container
     * @param byteArrayZSFiles
     * @param dataConnectionIdMap
     * @param zuidMigrationHelper
     * @param delugeIdMigrationHelper
     * @return
     * @throws IOException
     * @throws XmlPullParserException
     */
    public static byte[] replace_zuid_delugeId(WorkbookContainer container, byte[] byteArrayZSFiles, Map<String, String> dataConnectionIdMap, ZUIDMigrationHelper zuidMigrationHelper, DelugeIdMigrationHelper delugeIdMigrationHelper) throws IOException, XmlPullParserException {

        try(ZSZipInputStream inputStream = new ZSZipInputStream(new ByteArrayInputStream(byteArrayZSFiles)); ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            try(ZipOutputStream outputStream = new ZipOutputStream(byteArrayOutputStream)) {

            List<ReplaceInElementNameAndAttributeI> replaceInElementNameAndAttributeI_list = new ArrayList<>();
            ZUIDInFilterFilterView zuidInFilterFilterView = new ZUIDInFilterFilterView();
            replaceInElementNameAndAttributeI_list.add(zuidInFilterFilterView);
            ZUIDInSheetTableProtection zuidInSheetTableProtection = new ZUIDInSheetTableProtection();
            replaceInElementNameAndAttributeI_list.add(zuidInSheetTableProtection);
            if(!dataConnectionIdMap.isEmpty()) {
                DataConnectionIdInStableObjectsNamedRange dataConnectionIdInStableObjectsNamedRange = new DataConnectionIdInStableObjectsNamedRange(dataConnectionIdMap);
                replaceInElementNameAndAttributeI_list.add(dataConnectionIdInStableObjectsNamedRange);
            }

            if(container != null) {
                zuidInFilterFilterView.setZuidMigrationHelper(zuidMigrationHelper).setDocSpaceId(container.getDocsSpaceId());
                zuidInSheetTableProtection.setZuidMigrationHelper(zuidMigrationHelper).setDocSpaceId(container.getDocsSpaceId());
            } else {
                LOGGER.log(Level.OFF, "WorkbookContainer NULL");
            }

            replaceInElementNameAndAttributeI_list.add(new DelugeIdInTableColumn(delugeIdMigrationHelper));
            //                replaceInElementNameAndAttributeI_list.add(new DelugeId_StableObjects_NamedExpression());

            List<ReplaceInValueI> replaceInValueIList = new ArrayList<>();
            replaceInValueIList.add(new DelugeIdInSheetExpression(delugeIdMigrationHelper));

            List<String> zip_entry_list = ReplaceInZip.replace(inputStream, outputStream, replaceInElementNameAndAttributeI_list, replaceInValueIList);

            }
            byteArrayOutputStream.close();
            return byteArrayOutputStream.toByteArray();
        }
    }

    private static void writeActionsJSONInZSheet(WorkbookContainer container, ZipOutputStream zipOutputStream, Long resId, ZSStore.FileName fileName, String entryName) throws Exception
    {
        if(resId != -1)
        {
            try (InputStream stream = container.getReadStream(resId, fileName, ZSStore.FileExtn.JSON, null))
            {
                zipOutputStream.putNextEntry(new ZipEntry(entryName + EngineConstants.JSON_FILE_FORMAT));
                IOUtils.copy(stream, zipOutputStream);
            }
        }
    }

    public static void import_dc_migration_files(WorkbookContainer oldContainer, byte[] contentBytes, String zfsngVersionNo, Map<String, String> dataConnectionIdMap, ZUIDMigrationHelper zuidMigrationHelper) throws Exception {
        String currentVersion = zfsngVersionNo;
        LOGGER.info("zfsngVersionNo .."+zfsngVersionNo);
        List<String> sheetNames = null;
        String topVersion = ZohoFS.getTopVersion(oldContainer.getDocOwner(), oldContainer.getResourceId());
        LOGGER.info("topVersion .."+topVersion);
        StringBuilder import_stats = new StringBuilder("dc migration logs : import migration files ");//No I18N
        import_stats.append(oldContainer.getResourceId()).append("\n");
        try(ZSZipInputStream zipInputStream = new ZSZipInputStream(new ByteArrayInputStream(contentBytes))) {
            for(ZipEntry zipEntry = zipInputStream.getNextEntry(); zipEntry != null; zipEntry = zipInputStream.getNextEntry()) {
                if(zipEntry.getName().startsWith(DC_MIGRATION_FILES_PREFIX)) {
                    Instant start = Instant.now();
                    if((DC_MIGRATION_FILES_PREFIX+ZSStore.ADDITIONAL_INFO_JSON).equals(zipEntry.getName())) {
                    	LOGGER.info("WRITE ADDITIONAL INFO");
                        writeAdditionalInfo(oldContainer, zipInputStream);
                    } else if(zipEntry.getName().equals(DC_MIGRATION_FILES_IMPORTRANGE_LINKS_JSON)) {
                    	LOGGER.info("DC_MIGRATION_FILES_IMPORTRANGE_LINKS_JSON");
                    	try {
                    	WorkbookLink.import_dc_migration_importrange_file(zipInputStream, oldContainer.getResourceId(), oldContainer.getDocOwner(), oldContainer.getDocsSpaceId(), zuidMigrationHelper);
                    	}catch(Exception e) {
                    		LOGGER.log(Level.WARNING, "[ZSHEET] [DC_MIGRATION_FILES_IMPORTRANGE_LINKS_JSON] Exception while writing cell edit history in ZSheet", e);
                    	}
                    } else if(zipEntry.getName().equals(DC_MIGRATION_FILES_DATACONNECTIONS)) {
                    	LOGGER.info("DC_MIGRATION_FILES_DATACONNECTIONS");
                    	dataConnectionIdMap.putAll(insertDataConnectionFromJSON(oldContainer.getDocId(), oldContainer.getDocOwner(), oldContainer.getDocOwnerZUID(), IOUtils.toString(zipInputStream), oldContainer.getWorkbookAdditionalInfo().getTimeZone()));
                    } else if(zipEntry.getName().equals(DC_MIGRATION_FILES_DISCUSSIONS)) {
                    	LOGGER.info("DC_MIGRATION_FILES_DISCUSSIONS");
                    	 BufferedReader br = new BufferedReader(new InputStreamReader(zipInputStream));
                         String str = br.readLine();
                         JSONArrayWrapper jsonArray = new JSONArrayWrapper(str);
                         
                    	migrateAllComments(oldContainer.getDocOwner(), oldContainer.getDocId(), jsonArray, sheetNames);
                    } else if(zipEntry.getName().equals(DC_MIGRATION_FILES_NOTIFICATION_SETTINGS)) {
                        LOGGER.info("DC_MIGRATION_FILES_NOTIFICATION_SETTINGS");
                        BufferedReader br = new BufferedReader(new InputStreamReader(zipInputStream));
                        String str = br.readLine();
                        JSONArrayWrapper jsonArray = new JSONArrayWrapper(str);

                        migrateAllNotification(oldContainer.getDocOwner(), oldContainer.getDocId(), jsonArray);  

                    }else if(zipEntry.getName().equals(DC_MIGRATION_FILES_RANGE_PUBLISH)) {
                    	LOGGER.info("DC_MIGRATION_FILES_RANGE_PUBLISH");
                   	 BufferedReader br = new BufferedReader(new InputStreamReader(zipInputStream));
                        String str = br.readLine();
                        JSONObjectWrapper jsonObhect = new JSONObjectWrapper(str);
                        addPublishedRangeDetails( oldContainer.getDocOwner(), oldContainer.getDocId(), oldContainer.getDocsSpaceId(),jsonObhect, sheetNames);
                   	}else if(currentVersion != null) {
                   		LOGGER.info("DC_MIGRATION_FILES_CELL_EDIT_HISTORIES");
                        if(zipEntry.getName().startsWith(DC_MIGRATION_FILES_CELL_EDIT_HISTORIES)) {
                            if(currentVersion.equals(topVersion)) {
                            	LOGGER.info("DC_MIGRATION_FILES_CELL_EDIT_HISTORIES .. TOP VERION TRUE");
                            }else {
                            	LOGGER.info("DC_MIGRATION_FILES_CELL_EDIT_HISTORIES .. TOP VERION FALSE");
                            }
                                writeCellEditHistory(oldContainer, zipInputStream, zipEntry.getName(), zuidMigrationHelper);
                            
                        } else if(zipEntry.getName().startsWith(DC_MIGRATION_FILES_UNVERSIONEDACTIONS) || zipEntry.getName().startsWith(DC_MIGRATION_FILES_ACTIONS)) {
                           // if(currentVersion.equals(topVersion)){
                                writeActionsJSON(oldContainer, zipInputStream, zipEntry.getName(), zuidMigrationHelper);
                            //}
                        }
                    }else if(zipEntry.getName().equals(DC_MIGRATION_FILES_VSEARCH_FL)) {
                        oldContainer.writeSearchFileToDocumentStore(zipInputStream);
                    }
                    import_stats.append(zipEntry.getName()).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
                } if(zipEntry.getName().equals("order.json")) {
                    sheetNames = OrderFileReader.getSheetNamesFromOrderJson(zipInputStream);
                }
            }
        }catch(Exception e) {
        	LOGGER.log(Level.WARNING, "[ZSHEET] DC_MIGRATION  error ", e);
        } finally {
            LOGGER.log(Level.INFO, import_stats.toString());
        }
    }

    public static void import_dc_migration_files_after_version(WorkbookContainer oldContainer, byte[] contentBytes, String zfsngVersionNo, ZUIDMigrationHelper zuidMigrationHelper) throws Exception {
        String currentVersion = zfsngVersionNo;
        StringBuilder import_stats = new StringBuilder("dc migration logs : import migration files ");//No I18N
        import_stats.append(oldContainer.getResourceId()).append("\n");
        try(ZSZipInputStream zipInputStream = new ZSZipInputStream(new ByteArrayInputStream(contentBytes))) {
            for(ZipEntry zipEntry = zipInputStream.getNextEntry(); zipEntry != null; zipEntry = zipInputStream.getNextEntry()) {
                if(zipEntry.getName().startsWith(DC_MIGRATION_FILES_PREFIX)) {
                    Instant start = Instant.now();
                    if(currentVersion != null) {
                        if(zipEntry.getName().equals(DC_MIGRATION_FILES_VACTIONS)) {
                            String zfsngVersionId = ZohoFS.getVersionIDForEditor(oldContainer.getDocsSpaceId(), oldContainer.getResourceKey(), currentVersion);
                            Long  versionId = DocumentUtils.getVersionIdforZFSNGVersion(Long.parseLong(oldContainer.getDocId()), oldContainer.getDocOwner(), zfsngVersionId);
                            writeVActionsJSON(oldContainer, zipInputStream, versionId, zuidMigrationHelper);
                            import_stats.append(zipEntry.getName()).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
                        }
                    }
                }
            }
        }catch(Exception e) {
        	LOGGER.log(Level.WARNING, "[ZSHEET] DC_MIGRATION_FILES_IMPORTRANGE_LINKS_JSON error ", e);
        } finally {
            LOGGER.log(Level.INFO, import_stats.toString());
        }
        
    }
    public static String get_version_no( WorkbookContainer oldContainer, byte[] contentBytes) throws Exception {
        String versionNo = null;
        StringBuilder import_stats = new StringBuilder("dc migration logs : import migration files ");//No I18N
        import_stats.append(oldContainer.getResourceId()).append("\n");
        try(ZSZipInputStream zipInputStream = new ZSZipInputStream(new ByteArrayInputStream(contentBytes))) {
            for(ZipEntry zipEntry = zipInputStream.getNextEntry(); zipEntry != null; zipEntry = zipInputStream.getNextEntry()) {
                if(zipEntry.getName().startsWith(DC_MIGRATION_FILES_PREFIX)) {
                    Instant start = Instant.now();
                        if(zipEntry.getName().equals(DC_MIGRATION_FILES_DOCUMENT_VERSION)) {
                            
                        	BufferedReader br = new BufferedReader(new InputStreamReader(zipInputStream));
                            String str = br.readLine();
                            JSONObjectWrapper versionObj = new JSONObjectWrapper(str);
                            versionNo = String.valueOf(versionObj.getDouble("VERSION"));
                            import_stats.append(zipEntry.getName()).append(": ").append(Duration.between(start, Instant.now()).toMillis()).append("ms\n");
                            break;
                        }
                 }
            }
            return versionNo;
        }catch(Exception e) {
        	LOGGER.log(Level.WARNING, "[ZSHEET] DC_MIGRATION_FILES_IMPORTRANGE_LINKS_JSON error ", e);
        	throw e;
        } finally {
        	LOGGER.log(Level.INFO, import_stats.toString());
        }
        
    }
    
    private static void writeCellEditHistory(WorkbookContainer oldContainer, InputStream zipInputStream, String entryName, ZUIDMigrationHelper migrationHelper)
    {
        String docSpaceId = oldContainer.getDocsSpaceId();
        try {
            String[] fileNameWithASN = entryName.substring(DC_MIGRATION_FILES_CELL_EDIT_HISTORIES.length() + 1).split("/");
            String asn = fileNameWithASN[0];
            String fileName = fileNameWithASN[1];
            double versionNumber = Double.parseDouble(fileName.substring(0 ,fileName.lastIndexOf(".")));
            Long fragId = oldContainer.getCellEditHistoryFragmentId(asn, true);
            Long fragmentVersionId = ZSStore.addFragmentVersionRow(oldContainer, fragId, versionNumber, asn, ZSStore.FileExtn.ZSF);
            BufferedReader br = new BufferedReader(new InputStreamReader(zipInputStream));
            JSONArrayWrapper historyJsonArray = new JSONArrayWrapper(br.readLine());

            for(int j = 0; j < historyJsonArray.length(); j++)
            {
                JSONObjectWrapper historyJsonObjectWrapper = historyJsonArray.getJSONObject(j);
                for(String editHistoryKey : historyJsonObjectWrapper.keySet())
                {
                    if(!editHistoryKey.equals(JSONConstants.TRANSFORM_JSON_KEY))
                    {
                        JSONObjectWrapper historyJsonObject = historyJsonObjectWrapper.getJSONObject(editHistoryKey);
                        if (editHistoryKey.equals(JSONConstants.CELL_EDIT_HISTORY_KEY))
                        {
                            for (String rowKey : historyJsonObject.keySet())
                            {
                                JSONObjectWrapper rowJsonObject = historyJsonObject.getJSONObject(rowKey);
                                for (String colKey : rowJsonObject.keySet())
                                {
                                    JSONArrayWrapper colJsonArray = rowJsonObject.getJSONArray(colKey);
                                    for (int k = 0; k < colJsonArray.length(); k++)
                                    {
                                        JSONObjectWrapper historyObject = colJsonArray.getJSONObject(k);
                                        if (historyObject.has(JSONConstants.ZUID))
                                        {
                                            historyObject.set(JSONConstants.ZUID, migrationHelper.getMigratedZUID(docSpaceId, historyObject.getString(JSONConstants.ZUID)));
                                        }
                                    }
                                }
                            }
                        }
                        else if(editHistoryKey.equals(JSONConstants.REVERT_VERSION_JSON_KEY))
                        {
                            if(historyJsonObject.has(JSONConstants.ZUID))
                            {
                                historyJsonObject.set(JSONConstants.ZUID, migrationHelper.getMigratedZUID(docSpaceId, historyJsonObject.getString(JSONConstants.ZUID)));
                            }
                        }
                    }
                }
            }

            writeCellEditHistoryVersion(oldContainer, fragmentVersionId, versionNumber, asn, historyJsonArray.toString().getBytes());
        }
        catch (Exception e)
        {
            LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while writing cell edit history", e);
        }
    }

    private static List<JSONObjectWrapper> updateZUIDInActionJSON(WorkbookContainer container, InputStream inputStream, ZUIDMigrationHelper zuidMigrationHelper)
    {
        try
        {
            List<JSONObjectWrapper> actionJsons = new ArrayList<>();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            String str;
            while ((str = reader.readLine()) != null)
            {
                JSONObjectWrapper actionJson = new JSONObjectWrapper(str);
                actionJson.set(JSONConstants.ZUID, zuidMigrationHelper.getMigratedZUID(container.getDocsSpaceId(), actionJson.getString(JSONConstants.ZUID)));
                actionJsons.add(actionJson);
            }
            return actionJsons;
        }
        catch(Exception e)
        {
            LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while updating zuid", e);
        }
        return null;
    }


    private static void writeActionsJSON(WorkbookContainer oldContainer, InputStream zipInputStream, String entryName, ZUIDMigrationHelper zuidMigrationHelper)
    {
        try {
            ZSStore.FileName fileName = ZSStore.FileName.valueOf(entryName.split("/")[1].split("\\.")[0].toUpperCase());
            Long fragId = oldContainer.getFragmentId(fileName, true, null);
            List<JSONObjectWrapper> actionJsons = updateZUIDInActionJSON(oldContainer, zipInputStream, zuidMigrationHelper);
            EngineUtils1.writeActionsJson(oldContainer, fragId, fileName, ZSStore.FileExtn.JSON, actionJsons);
        }
        catch (Exception e)
        {
            LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while writing actionsJson", e);
        }
    }

    private static void writeVActionsJSON(WorkbookContainer oldContainer, InputStream zipInputStream, Long versionId, ZUIDMigrationHelper zuidMigrationHelper)
    {
        try {
            List<JSONObjectWrapper> vActionJsons = updateZUIDInActionJSON(oldContainer, zipInputStream, zuidMigrationHelper);
            EngineUtils1.writeVActionsJson(oldContainer, versionId, vActionJsons);
        }
        catch (Exception e)
        {
            LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while writing vActionsJson", e);
        }
    }

    private static void writeAdditionalInfo(WorkbookContainer oldContainer, InputStream inputStream) throws Exception
    {
    	LOGGER.info("writeAdditionalInfo called for "+oldContainer.getResourceId());
    	try {
	        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
	        String str;
	        while ((str = br.readLine()) != null)
	        {
	            JSONObjectWrapper additionalInfoJSON = new JSONObjectWrapper(str);
	            SpreadsheetSettingsUtil.updateResourceAdditionalInfo(oldContainer, WorkbookAdditionalInfo.getInstance(oldContainer, additionalInfoJSON));
	        }
    	}catch(Exception e) {
    		LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while writing writeAdditionalInfo ", e);
    	}
    }

    public static byte[] get_zsheet_without_migration_files(byte[] bytesWithMigrationFiles) throws IOException {
        try(ZSZipInputStream zipInputStream = new ZSZipInputStream(new ByteArrayInputStream(bytesWithMigrationFiles)); ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            try(ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
                for(ZipEntry zipEntry = zipInputStream.getNextEntry(); zipEntry != null; zipEntry = zipInputStream.getNextEntry()) {
                    if(!zipEntry.getName().startsWith(DC_MIGRATION_FILES_PREFIX)) {
                        zipOutputStream.putNextEntry(new ZipEntry(zipEntry.getName()));
                        IOUtils.copy(zipInputStream, zipOutputStream);
                    }
                }
            }
            byteArrayOutputStream.close();
            return byteArrayOutputStream.toByteArray();
        }
    }

    public static JSONArrayWrapper getDataConnectionMetaForDocument(String docOwner, String docId) throws Exception {
    	
        JSONArrayWrapper webDataMetaArray = new JSONArrayWrapper();
        DataObject webDataObj  = DBActionManager.getWebDataDO(docId, docOwner, -1);
        if(webDataObj == null)
        {
            return webDataMetaArray;
        }

        JobSchedulerBean JSbean = TaskManager.getJobSchedulerPersistence(docOwner);
        Iterator webDataRowItr = webDataObj.getRows("WebData");
        while (webDataRowItr.hasNext())
        {
            Row webDataRow = (Row) webDataRowItr.next();
            JSONObjectWrapper webDataMeta = new JSONObjectWrapper();

            long webDataId = webDataRow.getLong("WEBDATA_ID");//No I18N
            webDataMeta.put("WEBDATA_ID", webDataId);
            webDataMeta.put("SITE_URL", webDataRow.get("SITE_URL"));
            String webDataOwnerName = (String) webDataRow.get("WEBDATA_OWNER");
            long webDataOwnerId = DocumentUtils.getZUID(webDataOwnerName);
            webDataMeta.put("WEBDATA_OWNER", webDataOwnerId);
            webDataMeta.put("WEBDATA_TYPE", webDataRow.get("WEBDATA_TYPE"));
            webDataMeta.put("UNIQUE_KEY_ID", webDataRow.get("UNIQUE_KEY_ID"));
            webDataMeta.put("FAILEDCOUNT", webDataRow.get("FAILEDCOUNT"));
            webDataMeta.put("CREATED_TIME", webDataRow.get("CREATED_TIME"));
            webDataMeta.put("LAST_FETCHED_TIME", webDataRow.get("LAST_FETCHED_TIME"));
            webDataMeta.put("DOCUMENT_ID", webDataRow.get("DOCUMENT_ID"));
            webDataMeta.put("ERROR_CODE", webDataRow.get("ERROR_CODE"));
            webDataMeta.put("SERVICE_CONSTANT", webDataRow.get("SERVICE_CONSTANT"));
            webDataMeta.put("SERVICE_NAME", webDataRow.get("SERVICE_NAME"));
            webDataMeta.put("LIVE_STATUS_CODE", webDataRow.get("LIVE_STATUS_CODE"));
            webDataMeta.put("PROPERTIES", webDataRow.get("PROPERTIES"));

            try {
                RepetitiveJob repetitiveJob = JSbean.fetchRepetitiveJob(webDataId);
                if(repetitiveJob != null)
                {
                    webDataMeta.put("schName", repetitiveJob.getRepetition());
                    webDataMeta.put("adminStatus", repetitiveJob.getAdminStatus());
                }
            } catch (Exception e) {}



            Row fetchMetaInfoRow = webDataObj.getRow("FetchMetaInfo",webDataRow);
            if(fetchMetaInfoRow != null)
            {
                JSONObjectWrapper fetchMetaInfo = new JSONObjectWrapper();

                fetchMetaInfo.put("FETCH_META_INFO_ID", fetchMetaInfoRow.get("FETCH_META_INFO_ID"));
                fetchMetaInfo.put("WEBDATA_ID", fetchMetaInfoRow.get("WEBDATA_ID"));
                fetchMetaInfo.put("SERVICE_CONSTANT", fetchMetaInfoRow.get("SERVICE_CONSTANT"));
                fetchMetaInfo.put("SERVICE_NAME", fetchMetaInfoRow.get("SERVICE_NAME"));
                fetchMetaInfo.put("DATA_OWNER_ZUID", fetchMetaInfoRow.get("DATA_OWNER_ZUID"));
                fetchMetaInfo.put("DATA_OWNER", fetchMetaInfoRow.get("DATA_OWNER"));
                fetchMetaInfo.put("SERVICE_ORG_ID", fetchMetaInfoRow.get("SERVICE_ORG_ID"));
                fetchMetaInfo.put("DATABASE_NAME", fetchMetaInfoRow.get("DATABASE_NAME"));
                fetchMetaInfo.put("MODULE_NAME", fetchMetaInfoRow.get("MODULE_NAME"));
                fetchMetaInfo.put("FIELD_LIST", fetchMetaInfoRow.get("FIELD_LIST"));
                fetchMetaInfo.put("CRITERIA", fetchMetaInfoRow.get("CRITERIA"));
                fetchMetaInfo.put("LIMIT", fetchMetaInfoRow.get("LIMIT"));
                fetchMetaInfo.put("SERVICE_SPECIFIC_PARAMS", fetchMetaInfoRow.get("SERVICE_SPECIFIC_PARAMS"));

                webDataMeta.put("FetchMetaInfo", fetchMetaInfo);
            }

            Row webData_TablesListRow = webDataObj.getRow("WebData_TablesList",webDataRow);
            if(webData_TablesListRow != null)
            {
                JSONObjectWrapper webData_TablesListInfo = new JSONObjectWrapper();

                webData_TablesListInfo.put("WEBDATATABLESLIST_ID", webData_TablesListRow.get("WEBDATATABLESLIST_ID"));
                webData_TablesListInfo.put("WEBDATA_ID", webData_TablesListRow.get("WEBDATA_ID"));
                webData_TablesListInfo.put("WEBDATA_TABLE", webData_TablesListRow.get("WEBDATA_TABLE"));
                webData_TablesListInfo.put("WEBDATA_TABLECOLUMNS", webData_TablesListRow.get("WEBDATA_TABLECOLUMNS"));
                webData_TablesListInfo.put("DELIMITERS", webData_TablesListRow.get("DELIMITERS"));

                webDataMeta.put("WebData_TablesList", webData_TablesListInfo);
            }


            webDataMetaArray.put(webDataMeta);
        }

        return webDataMetaArray;
    }

    public static Map<String, String> insertDataConnectionFromJSON(String docId, String docOwner, String docOwnerZUID, String webDataMetaInfos, TimeZone timeZone) throws Exception {
    	Map<String, String > map = new HashMap<>();
    	try {
    	JSONArrayWrapper wedDataMetaArray = new JSONArrayWrapper(webDataMetaInfos);
        Iterator wedDataMetaItr = wedDataMetaArray.iterator();

        Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
        long oldWebDataID;
        long newWebDataID;
        ZUIDMigrationHelper zuidMigHelper =  ZUIDMigrationHelper.getInstance();
        while (wedDataMetaItr.hasNext()) {

            JSONObjectWrapper wedDataMeta = (JSONObjectWrapper) wedDataMetaItr.next();

            oldWebDataID = wedDataMeta.optLong("WEBDATA_ID");//No I18N

            Row webDataRow = new Row("WebData");
            webDataRow.set("SITE_URL", wedDataMeta.getString("SITE_URL"));
            webDataRow.set("WEBDATA_OWNER", DocumentUtils.getZUserName(zuidMigHelper.getMigratedZUID(docOwner, ""+wedDataMeta.getLong("WEBDATA_OWNER"))));
            webDataRow.set("WEBDATA_TYPE", wedDataMeta.getString("WEBDATA_TYPE"));
            long ukId = StoreUtil.getUniqueKeyId(docOwner, docOwnerZUID);
            webDataRow.set("UNIQUE_KEY_ID", ukId);
            DataObject wedataObj = persistence.constructDataObject();
            wedataObj.addRow(webDataRow);
            webDataRow.set("DOCUMENT_ID", docId);
            webDataRow.set("CREATED_TIME", wedDataMeta.getString("CREATED_TIME"));
            webDataRow.set("LAST_FETCHED_TIME", wedDataMeta.getString("LAST_FETCHED_TIME"));
            webDataRow.set("ERROR_CODE", wedDataMeta.getString("ERROR_CODE"));
            webDataRow.set("SERVICE_CONSTANT", wedDataMeta.getString("SERVICE_CONSTANT"));
            webDataRow.set("SERVICE_NAME", wedDataMeta.getString("SERVICE_NAME"));
            webDataRow.set("LIVE_STATUS_CODE", wedDataMeta.getString("LIVE_STATUS_CODE"));
            webDataRow.set("PROPERTIES", wedDataMeta.optString("PROPERTIES", null));
            wedataObj = persistence.add(wedataObj);
            newWebDataID = (Long) wedataObj.getValue("WebData", "WEBDATA_ID", wedataObj.getRow("WebData"));//No I18N

            // Insert into FetchMetaInfo table
            JSONObjectWrapper fetchMetaInfo = wedDataMeta.optJSONObject("FetchMetaInfo");//No I18N
            if (fetchMetaInfo != null) {
                Row fetchMetaRow = new Row("FetchMetaInfo");
                fetchMetaRow.set("WEBDATA_ID", newWebDataID);
                
                long oldZuid = fetchMetaInfo.getLong("DATA_OWNER_ZUID");//No I18N
                String newZuid = zuidMigHelper.getMigratedZUID(docOwner, ""+oldZuid);
                
                fetchMetaRow.set("DATA_OWNER_ZUID", newZuid);
                
                fetchMetaRow.set("DATA_OWNER", DocumentUtils.getZUserName(newZuid));
                
                fetchMetaRow.set("SERVICE_CONSTANT", fetchMetaInfo.getInt("SERVICE_CONSTANT"));
                fetchMetaRow.set("SERVICE_NAME", fetchMetaInfo.getString("SERVICE_NAME"));
                fetchMetaRow.set("SERVICE_ORG_ID", fetchMetaInfo.optString("SERVICE_ORG_ID", null));
                fetchMetaRow.set("DATABASE_NAME", fetchMetaInfo.optString("DATABASE_NAME", null));
                fetchMetaRow.set("MODULE_NAME", fetchMetaInfo.optString("MODULE_NAME", null));
                fetchMetaRow.set("FIELD_LIST", fetchMetaInfo.optString("FIELD_LIST", "[]"));
                fetchMetaRow.set("CRITERIA", fetchMetaInfo.optString("CRITERIA", "[]"));
                fetchMetaRow.set("LIMIT", fetchMetaInfo.getInt("LIMIT"));
                fetchMetaRow.set("SERVICE_SPECIFIC_PARAMS", fetchMetaInfo.optString("SERVICE_SPECIFIC_PARAMS", null));

                DataObject sfdDObj = persistence.constructDataObject();
                sfdDObj.addRow(fetchMetaRow);
                persistence.add(sfdDObj);
            }

            // Insert into WebData_TablesList table
            JSONObjectWrapper tablesList = wedDataMeta.optJSONObject("WebData_TablesList");//No I18N
            if (tablesList != null) {
                DataObject tblObj = persistence.constructDataObject();
                Row tableRow = new Row("WebData_TablesList");
                tableRow.set("WEBDATA_ID", newWebDataID);
                tableRow.set("WEBDATA_TABLE", tablesList.optString("WEBDATA_TABLE", null));
                tableRow.set("WEBDATA_TABLECOLUMNS", tablesList.optString("WEBDATA_TABLECOLUMNS", null));
                tableRow.set("DELIMITERS", tablesList.optString("DELIMITERS", null));
                tblObj.addRow(tableRow);
                persistence.add(tblObj);
            }

            if(wedDataMeta.has("schName"))
            {
                TaskManager.add(newWebDataID, wedDataMeta.getString("schName"), docOwner, timeZone);
            }

            if(wedDataMeta.has("adminStatus"))
            {
                TaskManager.enableOrDisable(newWebDataID, docOwner, wedDataMeta.getBoolean("adminStatus"));
            }

            map.put(String.valueOf(oldWebDataID), String.valueOf(newWebDataID));
        }

        return map;
        }catch(Exception e) {
        	LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while writing data connections ", e);	
        	return map;
        }
        
    }
    
    

	public static void migrateAllComments( String documentOwner, String docid, JSONArrayWrapper commentsArray, List<String> sheetNames) throws Exception{
try {
		String discussionsTableName = "Discussions";  //No I18N
		String repliesTableName = "DiscussionReplies";  //No I18N
		Persistence per = SheetPersistenceUtils.getPersistence(documentOwner);
		
		Iterator sheetItr = sheetNames.iterator();
		Map<String, String> sheetNameMap = new HashMap<String, String>();
		while (sheetItr.hasNext()) {
			String sheetName = (String) sheetItr.next();
			if(!sheetNameMap.containsKey(sheetName)){
			Long sheetId = DocumentUtils.getDocumentSheetId( Long.valueOf(docid), sheetName, documentOwner, true);
	    	sheetNameMap.put(sheetName, sheetId.toString());
			}
		}
	       

    	
    	ZUIDMigrationHelper zuidMigHelper =  ZUIDMigrationHelper.getInstance();
		for(int i = 0; i < commentsArray.length(); i++) {
			DataObject dobj = per.constructDataObject();
			Row row = new Row(discussionsTableName);

			JSONObjectWrapper commentFragment = commentsArray.getJSONObject(i);

			row.set("OBJECT_ID", commentFragment.getString("OBJECT_ID"));  //NO I18N
			row.set("DOCUMENT_ID", docid);  //NO I18N
			row.set("DOCUMENTSHEET_ID", sheetNameMap.get(commentFragment.getString("DOCUMENT_SHEET_NAME")));  //NO I18N
			row.set("OWNER_ID", zuidMigHelper.getMigratedZUID(documentOwner, commentFragment.getString("OWNER_ID"))); //NO I18N
			row.set("TYPE", commentFragment.getString("TYPE"));  //NO I18N
			String disContent =  getZuidUpdatedContent(zuidMigHelper, documentOwner, commentFragment.getString("CONTENT"));
			row.set("CONTENT", disContent);  //NO I18N
			row.set("ENCRYPTED_CONTENT", disContent);  //NO I18N
			row.set("STATUS", commentFragment.getString("STATUS").toString());
			if(commentFragment.has("LIKES")) {
				row.set("LIKES", getZuidUpdatedLikes(commentFragment.getString("LIKES").toString(), documentOwner, zuidMigHelper));
			}
			row.set("CREATED_TIME", commentFragment.getString("CREATED_TIME"));  //NO I18N
			row.set("LAST_MODIFIED_TIME", commentFragment.getString("LAST_MODIFIED_TIME"));  //NO I18N
			row.set("THREAD_MODIFIED_TIME", commentFragment.getString("THREAD_MODIFIED_TIME"));  //NO I18N
			dobj.addRow(row);

			DataObject doj = per.add(dobj);
			row = doj.getFirstRow(discussionsTableName);
			String newDiscussionId = row.get("DISCUSSION_ID").toString();

			// Adding Replies

			JSONArrayWrapper repliesArray = new JSONArrayWrapper();
			repliesArray = commentFragment.getJSONArray("REPLIES");

			for(int j= 0; j < repliesArray.length(); j++) {
				DataObject dataObj = per.constructDataObject();
				Row replyRow = new Row(repliesTableName);

				JSONObjectWrapper replyFragment = repliesArray.getJSONObject(j);

				replyRow.set("DISCUSSION_ID", newDiscussionId);  //NO I18N
				replyRow.set("OWNER_ID", zuidMigHelper.getMigratedZUID(documentOwner, replyFragment.getString("OWNER_ID"))); //NO I18N
				replyRow.set("TYPE", replyFragment.getString("TYPE"));  //NO I18N
				//replyRow.set("CONTENT", replyFragment.getString("CONTENT"));  //NO I18N
				String content = getZuidUpdatedContent(zuidMigHelper, documentOwner, replyFragment.getString("CONTENT"));
				replyRow.set("CONTENT",content);  //NO I18N
				replyRow.set("ENCRYPTED_CONTENT", content);  //NO I18N
				if(replyFragment.has("LIKES")) {
					replyRow.set("LIKES", getZuidUpdatedLikes(replyFragment.getString("LIKES").toString(), documentOwner, zuidMigHelper));
					//replyRow.set("LIKES", replyFragment.getString("LIKES").toString());
				}
				
				replyRow.set("CREATED_TIME", replyFragment.getString("CREATED_TIME"));  //NO I18N
				replyRow.set("LAST_MODIFIED_TIME", replyFragment.getString("LAST_MODIFIED_TIME"));  //NO I18N
				dataObj.addRow(replyRow);
				DataObject dataOj = per.add(dataObj);
			}
		}
}catch(Exception e) {
	LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while writing comments ", e);	
}
	}

	/* -------------------------- FUNCTION -- discussionsTable-------------------------- */

	public static JSONArrayWrapper getCommentsJSON(String documentOwner, String documentId) {

		String discussionsTableName = "Discussions";  //No I18N
		String repliesTableName = "DiscussionReplies";  //No I18N
		LOGGER.info(":::MIGRATION::getCommentsJSON:"+documentId);
		JSONArrayWrapper commentsArray = new JSONArrayWrapper();

		try {
			Persistence per = SheetPersistenceUtils.getPersistence(documentOwner);
			SelectQueryImpl sql = new SelectQueryImpl(new Table(discussionsTableName));
			sql.addSelectColumn(new Column(discussionsTableName, "*"));
			Criteria docIdCriteria = new Criteria(new Column(discussionsTableName, "DOCUMENT_ID"), documentId, QueryConstants.EQUAL);
			sql.setCriteria(docIdCriteria);
			DataObject dobj = per.get(sql);
			Iterator<Row> Iter = dobj.getRows(discussionsTableName);
			
			DataObject  dao = DocumentUtils.getDocumentSheetsDO(documentOwner, Long.valueOf(documentId));
        	Iterator<?> itr = dao.getRows("DocumentSheets");													//No I18N
        	Map<String, String> sheetNameMap = new HashMap();
        	while(itr.hasNext()){
        		Row docSheetsRow 		= 	(Row) itr.next();
        		Long docSheetId 				= 	(Long) docSheetsRow.get("DOCUMENTSHEET_ID"); 					//No I18N
    			String sheetName 			= 	(String) docSheetsRow.get("SHEET_NAME"); 	
    			sheetNameMap.put(docSheetId.toString(), sheetName);
        	}
    			
			while(Iter.hasNext()) {
				Row row = Iter.next();
				JSONObjectWrapper commentsObject = new JSONObjectWrapper();
				String commentId = row.get("DISCUSSION_ID").toString();
				commentsObject.put("DOCUMENT_ID", row.get("DOCUMENT_ID").toString());
				//commentsObject.put("DOCUMENTSHEET_ID", row.get("DOCUMENTSHEET_ID").toString());
				commentsObject.put("DOCUMENT_SHEET_NAME", sheetNameMap.get(row.get("DOCUMENTSHEET_ID").toString()));
				commentsObject.put("OBJECT_ID", row.get("OBJECT_ID").toString());
				commentsObject.put("TYPE", row.get("TYPE").toString());
				//commentsObject.put("OWNER_ID", getNewZuid(row.get("OWNER_ID").toString()));
				//String zuidUpdatedContent = getZuidUpdatedContent(row.get("CONTENT").toString());
				commentsObject.put("OWNER_ID", row.get("OWNER_ID").toString());
				String zuidUpdatedContent = row.get("CONTENT").toString();
				commentsObject.put("CONTENT", zuidUpdatedContent);
				commentsObject.put("ENCRYPTED_CONTENT", zuidUpdatedContent);
				commentsObject.put("STATUS", row.get("STATUS").toString());
				commentsObject.put("LIKES", row.get("LIKES")!= null ? row.get("LIKES").toString() : row.get("LIKES"));
				commentsObject.put("CREATED_TIME", row.get("CREATED_TIME").toString());
				commentsObject.put("LAST_MODIFIED_TIME", row.get("LAST_MODIFIED_TIME").toString());
				commentsObject.put("THREAD_MODIFIED_TIME", row.get("THREAD_MODIFIED_TIME").toString());
				commentsObject.put("REPLIES", getRepliesJSON(commentId, documentOwner, repliesTableName));

				commentsArray.put(commentsObject);
			}
		} catch (Exception e) {
			LOGGER.info("Problem while getting Comment Content::"+ e);
		}
		return commentsArray;
	}

	/* -------------------------- FUNCTION -- Reply Table-------------------------- */

	public static JSONArrayWrapper getRepliesJSON(String commentId, String documentOwner, String repliesTableName) {

		LOGGER.info(":::MIGRATION::getRepliesJSON:");
		JSONArrayWrapper repliesArray = new JSONArrayWrapper();

		try {
			Persistence per = SheetPersistenceUtils.getPersistence(documentOwner);
			SelectQueryImpl sql = new SelectQueryImpl(new Table(repliesTableName));
			sql.addSelectColumn(new Column(repliesTableName, "*"));
			Criteria disIdCriteria = new Criteria(new Column(repliesTableName, "DISCUSSION_ID"), commentId, QueryConstants.EQUAL);
			sql.setCriteria(disIdCriteria);
			DataObject dobj = per.get(sql);
			Iterator<Row> Iter = dobj.getRows(repliesTableName);

			while(Iter.hasNext()) {

				Row row = Iter.next();

				JSONObjectWrapper replyObject = new JSONObjectWrapper();
				replyObject.put("COMMENT_ID", row.get("COMMENT_ID").toString());
				replyObject.put("DISCUSSION_ID", row.get("DISCUSSION_ID").toString());
				//replyObject.put("OWNER_ID", getNewZuid(row.get("OWNER_ID").toString()));
				//String zuidUpdatedContent = getZuidUpdatedContent(row.get("CONTENT").toString());
				replyObject.put("OWNER_ID", row.get("OWNER_ID").toString());
				String zuidUpdatedContent = row.get("CONTENT").toString();
				replyObject.put("TYPE", row.get("TYPE").toString());
				
				replyObject.put("CONTENT", zuidUpdatedContent);
				replyObject.put("ENCRYPTED_CONTENT", zuidUpdatedContent);
				replyObject.put("LIKES", row.get("LIKES")!= null ? row.get("LIKES").toString() : row.get("LIKES"));
				replyObject.put("CREATED_TIME", row.get("CREATED_TIME").toString());
				replyObject.put("LAST_MODIFIED_TIME", row.get("LAST_MODIFIED_TIME").toString());

				repliesArray.put(replyObject);
			}
		} catch (Exception e) {
			LOGGER.info("Problem while getting reply content ::"+ e);
		}
		return repliesArray;
	}


	/* -------------------------- FUNCTION -- update new zuid in likes Column-------------------------- */

	public static String getZuidUpdatedLikes(String likes, String owner, ZUIDMigrationHelper migHelper) {

		LOGGER.info("OLD likes::"+likes);
		String newLikes = "";
		if(likes!= null) {
			String likeS[] = likes.split(",");
			for(int i = 1; i<likeS.length; i++) {
				newLikes = newLikes + "," + migHelper.getMigratedZUID(owner, likeS[i]);
			}
		}
		LOGGER.info("new likes::"+newLikes);
		return newLikes;
	}

	
	
	/* -------------------------- FUNCTION -- update new zuid in content Column-------------------------- */

	public static String getZuidUpdatedContent(ZUIDMigrationHelper migHelper, String owner, String content) {
try {
		LOGGER.info("getZuidUpdatedContent::"+content);
		Pattern pattern = Pattern.compile("(%3Cuser%3Amention%3E)([0-9$]+)%3A(.+?)(%3C%2Fuser%3Amention%3E)");
		StringBuffer contentBuffer = new StringBuffer();
		Matcher dbMatcher = pattern.matcher(content);
		IAMProxy proxy = IAMProxy.getInstance();
		User user = null;
		while(dbMatcher.find()) {
			String zuid = dbMatcher.group(2);
			String name = "";
			String newZuid = zuid;
			try {
				newZuid = migHelper.getMigratedZUID(owner, zuid);
				user = IAMProxy.getInstance().getUserAPI().getUserFromZUID(newZuid);
				name = user.getFirstName() + " " +user.getLastName();
				//contact = IAMProxy.getInstance().getContactAPI().getContact(Long.parseLong(this.performerId), mailId); //No I18N;
			} catch (IAMException e) {
				LOGGER.log(Level.WARNING,"Exception on getting UserInfo for Discussion @mention",e);
			}
			dbMatcher.appendReplacement(contentBuffer, ("<user:mention>" + newZuid + ":" + name + "</user:mention>"));  //No I18N
		}
		dbMatcher.appendTail(contentBuffer);
		//LOGGER.info("getZuidUpdatedContent::RESULT::"+contentBuffer.toString());
		return contentBuffer.toString();
}
catch(Exception e) {
	LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while writing getZuidUpdatedContent ", e);	
}
return content;
	}
	
	
	
	public static void addPublishedRangeDetails(String docOwner, String docid, String docsSpaceId, JSONObjectWrapper publishRangeDetails, List <String> sheetNames) throws Exception {
	try {
		JSONArrayWrapper enc_url_arr = (JSONArrayWrapper) publishRangeDetails.get(Constants.PUB_ENC_URL);
	       JSONArrayWrapper scol_arr = (JSONArrayWrapper) publishRangeDetails.get(Constants.PUB_RANGE_SCOL);
	       JSONArrayWrapper srow_arr = (JSONArrayWrapper) publishRangeDetails.get(Constants.PUB_RANGE_SROW);
	       JSONArrayWrapper ecol_arr = (JSONArrayWrapper) publishRangeDetails.get(Constants.PUB_RANGE_ECOL);
	       JSONArrayWrapper erow_arr = (JSONArrayWrapper) publishRangeDetails.get(Constants.PUB_RANGE_EROW);
	       JSONArrayWrapper pub_date = (JSONArrayWrapper) publishRangeDetails.get(Constants.PUB_DATE);
	       
	       JSONArrayWrapper sheetNamesArr = (JSONArrayWrapper) publishRangeDetails.get("SHEET_NAME");
	       
			Iterator sheetItr = sheetNames.iterator();
			Map<String, String> sheetNameMap = new HashMap<String, String>();
			while (sheetItr.hasNext()) {
				String sheetName = (String) sheetItr.next();
				if(!sheetNameMap.containsKey(sheetName)){
					Long sheetId = DocumentUtils.getDocumentSheetId( Long.valueOf(docid), sheetName, docOwner, true);
					sheetNameMap.put(sheetName, sheetId.toString());
				}
			}

	    	
	       JSONArrayWrapper additional_info = (JSONArrayWrapper) publishRangeDetails.get(Constants.ADDITIONAL_INFO);
	       JSONArrayWrapper type = (JSONArrayWrapper) publishRangeDetails.get("PUB_TYPE");
	       String zoid = null;
	       Persistence pers = null;
	       for(int i = 0; i < enc_url_arr.length(); i++) {
	          if("org".equals(type.get(i))) {
	        	  
	        	  String metaRid = RedisHelper.get(RedisHelper.DC_MIG_META+docsSpaceId, -1);
			        ResourceInfo resourceInfo = ZohoFS.getResourceInfo(metaRid);
			        byte[] bytes = ZohoFS.getResourceContent(resourceInfo.getOwner(), resourceInfo.getResourceId(), "1.0", resourceInfo.getExtension());
			        org.json.JSONObject jsonObj = new JSONObject(new String(bytes));
			        Iterator<String> keys = jsonObj.keys();
			        while (keys.hasNext()) {
			            String key = keys.next();
			            if(key.equals("org")) {
			            	
			            	org.json.JSONObject orgJsonObj = new JSONObject(jsonObj.get(key).toString());
			            	String oldZoid =  orgJsonObj.keys().next();
			            	zoid = (String) orgJsonObj.get(oldZoid);
			            	if(zoid != null) {
			            		LOGGER.info("ZOID ...."+zoid);
			            		DocumentUtils.reserveDataSpace(Constants.CORPORATEDBPREFIX + zoid);
			   	             pers = SheetPersistenceUtils.getPersistence(Constants.CORPORATEDBPREFIX + zoid);
			   	             break;
			   			     }else { 
			   			    	 return;
			   			     }
			            }
			        }
			     
	          } else {
	             pers = SheetPersistenceUtils.getPersistence("Public");//NO I18N
	          }
	          DataObject dobj = pers.constructDataObject();
	          Row row = new Row("RangePublish");

	          row.set("SHARE_OWNER", docOwner);
	          row.set("AUTHOR_ZUID", docOwner);
	          row.set("DOCUMENT_ID", docid);
	          row.set("SHEET_ID", sheetNameMap.get(sheetNamesArr.get(i)));
	          row.set("PUBLISHED_DATE", pub_date.get(i));
	          row.set("ENCRYPTED_URL", enc_url_arr.get(i));
	          row.set("RANGE_SROW", Integer.parseInt(String.valueOf(srow_arr.get(i))));
	          row.set("RANGE_SCOL", Integer.parseInt(String.valueOf(scol_arr.get(i))));
	          row.set("RANGE_EROW", Integer.parseInt(String.valueOf(erow_arr.get(i))));
	          row.set("RANGE_ECOL", Integer.parseInt(String.valueOf(ecol_arr.get(i))));
	          row.set("ADDITIONAL_INFO", additional_info.get(i));

	          dobj.addRow(row);
	          pers.add(dobj);

	       }
	}catch(Exception e) {
		LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while writing published data back ", e);	
	}
	    }

	    public static JSONObjectWrapper getPublishedRangeDetails(String docOwner, String doc_id, String creatorZuid) throws DataAccessException {

	       int cnt = 0;
	        JSONObjectWrapper publishRangeDetails = new JSONObjectWrapper();
	       try {

	          long scol_arr[];
	          long srow_arr[];
	          long ecol_arr[];
	          long erow_arr[];
	          String enc_url_arr[];
	          String pub_sheets[];
	          String share_owner[];
	          long pub_date[];
	          String sheetNames[];
	          long docIds[];
	          long authorZuids[];
	          String type[];
	          String additional_info[];
	          double trh[];
	          double tcw[];

	          DataObject dobj = null;
	          Persistence pers = SheetPersistenceUtils.getPersistence("Public");//No I18N

	          DataObject dobj1 = null;
	          Persistence pers1 = SheetPersistenceUtils.getPersistence(docOwner);
	          SelectQueryImpl sql1 = new SelectQueryImpl(new Table("DocumentSheets"));
	          sql1.addSelectColumn(new Column(null, "*"));
	          Criteria cri1 = new Criteria(new Column("DocumentSheets", "DOCUMENT_ID"), doc_id, QueryConstants.EQUAL);
	          sql1.setCriteria(cri1);
	          dobj1 = pers1.get(sql1);

	          SelectQueryImpl sql = new SelectQueryImpl(new Table("RangePublish"));
	          sql.addSelectColumn(new Column(null, "*"));
	          sql.addSortColumn(new SortColumn("RangePublish", "PUBLISHED_DATE", false));//No I18N
	          Criteria cri = new Criteria(new Column("RangePublish", "DOCUMENT_ID"), doc_id, QueryConstants.EQUAL);
	          sql.setCriteria(cri);
	          dobj = pers.get(sql);

	          
	          DataObject dobj2 = null;

	          
				DataObject  dao = DocumentUtils.getDocumentSheetsDO(docOwner, Long.valueOf(doc_id));
	        	Iterator<?> itr1 = dao.getRows("DocumentSheets");													//No I18N
	        	Map<String, String> sheetNameMap = new HashMap();
	        	while(itr1.hasNext()){
	        		Row docSheetsRow 		= 	(Row) itr1.next();
	        		Long docSheetId 				= 	(Long) docSheetsRow.get("DOCUMENTSHEET_ID"); 					//No I18N
	    			String sheetName 			= 	(String) docSheetsRow.get("SHEET_NAME"); 	
	    			sheetNameMap.put(docSheetId.toString(), sheetName);
	        	}
	        	
	        	
	          long zoid = -1;
	          // Code for Org Support

	          zoid = DocumentUtils.getZOIDFromZuid(creatorZuid);
	          Persistence pers2 = null;
	          if (zoid != -1) {
	             pers2 = SheetPersistenceUtils.getPersistence(Constants.CORPORATEDBPREFIX + zoid);
	             //Persistence pers2 = (Persistence)BeanUtil.lookup("Persistence", Constants.CORPORATEDBPREFIX + zoid);
	             dobj2 = pers2.get(sql);
	          }
	          cnt = dobj.size("RangePublish");

	          if (cnt == -1) {
	             cnt = 0;
	          }

	          if (dobj2 != null && !dobj2.isEmpty()) {
	             cnt += dobj2.size("RangePublish");
	          }

	          scol_arr = new long[cnt];
	          srow_arr = new long[cnt];
	          ecol_arr = new long[cnt];
	          erow_arr = new long[cnt];
	          enc_url_arr = new String[cnt];
	          pub_sheets = new String[cnt];
	          share_owner = new String[cnt];
	          pub_date = new long[cnt];
	          docIds = new long[cnt];
	          additional_info = new String[cnt];
	          authorZuids = new long[cnt];
	          sheetNames = new String[cnt];
	          trh = new double[cnt];
	          tcw = new double[cnt];

	          type = new String[cnt];
	          int i = 0;
	          if (!dobj.isEmpty()) {
	             Iterator itr = dobj.getRows("RangePublish");
	             Row row = new Row("RangePublish");
	             Row row1 = new Row("DocumentSheets");


	             while (itr.hasNext()) {
	                row = (Row) itr.next();
	                try {
	                   row1 = dobj1.getRow("DocumentSheets", new Criteria(new Column("DocumentSheets", "DOCUMENTSHEET_ID"), (Long) row.get("SHEET_ID"), QueryConstants.EQUAL));
	                   if (row1 == null) {
	                      //Sheet remove case (or) Invalid sheet entry
	                      pers.delete(new Criteria(new Column("RangePublish", "SHEET_ID"), (Long) row.get("SHEET_ID"), QueryConstants.EQUAL));
	                      LOGGER.info("Invlid sheet entry in Ext - Published range was deleted. docid: " + (Long) row.get("DOCUMENT_ID") + " ,SheetId: " + (Long) row.get("SHEET_ID"));
	                   } else {
	                      long scol = (Long) row.get("RANGE_SCOL");
	                      long srow = (Long) row.get("RANGE_SROW");
	                      long ecol = (Long) row.get("RANGE_ECOL");
	                      long erow = (Long) row.get("RANGE_EROW");
	                      long date = ((Long) row.get("PUBLISHED_DATE"));
	                      long docID = (Long) row.get("DOCUMENT_ID");
	                      String enc_url = (String) row.get("ENCRYPTED_URL");
	                      String add_info = (String)  row.get("ADDITIONAL_INFO");

	                      String shareowner = (String) row.get("SHARE_OWNER");
	                      Long sheetId = (Long) row.get("SHEET_ID");
	                      String sheetName = (String) sheetNameMap.get(sheetId.toString());
	                      long author = ((Long) row.get("AUTHOR_ZUID"));

	                      scol_arr[i] = scol;
	                      srow_arr[i] = srow;
	                      ecol_arr[i] = ecol;
	                      erow_arr[i] = erow;
	                      enc_url_arr[i] = enc_url;
	                      pub_date[i] = date;
	                      share_owner[i] = shareowner;

	                      additional_info[i] = add_info;
	                      docIds[i] = docID;
	                      authorZuids[i] = author;
	                      sheetNames[i] = sheetName;
	                      type[i] = "public";//No I18N

	                      i = i + 1;
	                   }
	                } catch (Exception e1) {
	                	LOGGER.log(Level.WARNING, "Problem while getting Ext Published Ranges.. " + (Long) row.get("DOCUMENT_ID"), e1);
	                }
	             }
	          }

	          if (dobj2 != null && !dobj2.isEmpty()) {
	             Iterator itr = dobj2.getRows("RangePublish");
	             Row row = new Row("RangePublish");
	             Row row1 = new Row("DocumentSheets");

	             while (itr.hasNext()) {
	                row = (Row) itr.next();
	                try {
	                   row1 = dobj1.getRow("DocumentSheets", new Criteria(new Column("DocumentSheets", "DOCUMENTSHEET_ID"), (Long) row.get("SHEET_ID"), QueryConstants.EQUAL));
	                   if (row1 == null) {
	                      //Sheet remove case (or) Invalid sheet entry
	                      pers2.delete(new Criteria(new Column("RangePublish", "SHEET_ID"), (Long) row.get("SHEET_ID"), QueryConstants.EQUAL));
	                      LOGGER.info("Invlid sheet entry in Org - Published range was deleted. docid: " + (Long) row.get("DOCUMENT_ID") + " ,SheetId: " + (Long) row.get("SHEET_ID"));
	                   } else {
	                      long scol = (Long) row.get("RANGE_SCOL");
	                      long srow = (Long) row.get("RANGE_SROW");
	                      long ecol = (Long) row.get("RANGE_ECOL");
	                      long erow = (Long) row.get("RANGE_EROW");
	                      long date = (Long) row.get("PUBLISHED_DATE");
	                      String enc_url = (String) row.get("ENCRYPTED_URL");
	                      long docID = (Long) row.get("DOCUMENT_ID");
	                      String add_info = (String)  row.get("ADDITIONAL_INFO");

	                      String shareowner = (String) row.get("SHARE_OWNER");
	                      Long sheetId = (Long) row.get("SHEET_ID");
	                      String sheetName = (String) sheetNameMap.get(sheetId.toString());
	                      long author = ((Long) row.get("AUTHOR_ZUID"));

	                      scol_arr[i] = scol;
	                      srow_arr[i] = srow;
	                      ecol_arr[i] = ecol;
	                      erow_arr[i] = erow;
	                      enc_url_arr[i] = enc_url;
	                      pub_date[i] = date;
	                      docIds[i] = docID;

	                      share_owner[i] = shareowner;
	                      additional_info[i] = add_info;
	                      authorZuids[i] = author;
	                      sheetNames[i] = sheetName;
	                      type[i] = "org";//No I18N

	                      i = i + 1;
	                   }
	                } catch (Exception e2) {
	                	LOGGER.log(Level.WARNING, "Problem while getting Org Published Ranges.. " + (Long) row.get("DOCUMENT_ID"), e2);
	                }
	             }
	          }

	          publishRangeDetails.put(Constants.PUB_ENC_URL, enc_url_arr);
	          publishRangeDetails.put(Constants.PUB_RANGE_SCOL, scol_arr);
	          publishRangeDetails.put(Constants.PUB_RANGE_SROW, srow_arr);
	          publishRangeDetails.put(Constants.PUB_RANGE_ECOL, ecol_arr);
	          publishRangeDetails.put(Constants.PUB_RANGE_EROW, erow_arr);
	          publishRangeDetails.put(Constants.PUB_DATE, pub_date);
	          publishRangeDetails.put(Constants.PUB_SHEETS, pub_sheets);
	          publishRangeDetails.put(Constants.ADDITIONAL_INFO, additional_info);
	          publishRangeDetails.put("SHARE_OWNER", share_owner);
	          publishRangeDetails.put("AUTHOR_ZUID", authorZuids);
	          publishRangeDetails.put("SHEET_NAME", sheetNames);
	          publishRangeDetails.put("DOCUMENT_ID", docIds);
	          publishRangeDetails.put("NORANGESPUBLISHED", "false");
	          publishRangeDetails.put("PUB_TYPE", type);

	       } catch (Exception e) {
	    	   LOGGER.log(Level.WARNING, null, e);
	       }

	       if (cnt == 0) {
	          publishRangeDetails.put("NORANGESPUBLISHED", "true");
	       }

//	     addPublishedRangeDetails(publishRangeDetails);
	       return publishRangeDetails;
	    }

		public static JSONObjectWrapper getVersionRowDetails(String docId, String docOwner, String zfsngVersionid) throws Exception {
			SelectQueryImpl sql = new SelectQueryImpl(new Table("DocumentVersion")); // No I18N
			sql.addSelectColumn(new Column("DocumentVersion", "*")); // No I18N
			Criteria cri = new Criteria(new Column("DocumentVersion", "DOCUMENT_ID"), new Long(docId), QueryConstants.EQUAL);
			cri = cri.and(new Criteria(new Column("DocumentVersion", "RESOURCE_VERSION_ID"), zfsngVersionid, QueryConstants.EQUAL));
			
			sql.setCriteria(cri);
			Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
			DataObject dObj = persistence.get(sql);
			Row rw = dObj.getFirstRow("DocumentVersion");// No I18N
			JSONObjectWrapper versionRowDetails = new JSONObjectWrapper();
			versionRowDetails.put("VERSION_TIME", rw.get("VERSION_TIME"));
			versionRowDetails.put("VERSION", ((Double) rw.get("VERSION")).toString());
			versionRowDetails.put("VERSION_MODE", rw.get("VERSION_MODE"));
			
			return versionRowDetails;
		}

    private static void writeVSearch_FL(WorkbookContainer container, ZipOutputStream outputStream, Long versionId) throws Exception {
        if(versionId != null) {
            try (InputStream stream = container.getReadStream(versionId, ZSStore.FileName.VSEARCH_FL, ZSStore.FileExtn.ZIP, null)) {
                outputStream.putNextEntry(new ZipEntry(DC_MIGRATION_FILES_VSEARCH_FL));
                IOUtils.copy(stream, outputStream);
            }
        }
    }

    private static void writeCellEditHistoryVersion(WorkbookContainer container, Long fragmentVersionId, Double versionNumber, String associatedSheetName, byte[] historyJSONArray) throws Exception
    {
        OutputStream os = null;
        Map writeInfo = null;
        try
        {
            if(fragmentVersionId > 0)
            {
                writeInfo = container.getWriteInfo(fragmentVersionId, ZSStore.FileName.CELL_EDIT_HISTORIES, ZSStore.FileExtn.ZSF, associatedSheetName);
                os = (OutputStream) writeInfo.get("OS");   // No I18N
                try (ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(os, 16 * 1024))) {
                    zos.putNextEntry(new ZipEntry(associatedSheetName + EngineConstants.JSON_FILE_FORMAT));
                    zos.write(historyJSONArray);
                }
                LOGGER.log(Level.INFO, "\uD83D\uDDC2️[WRITE-FORMAT-INKLING][ZSHEET][FRAGMENTS][CELL_EDIT_HISTORY] RESOURCE_KEY: {0} >>>> Versioning cell edit history with Fragment Version Id {1}, Version Number {2} for Fragment Sheet: {3}", new Object[]{container.getResourceKey(), fragmentVersionId, versionNumber, associatedSheetName});
            }
        }
        catch(DFSException dfse)
        {
            if(writeInfo != null)
            {
                writeInfo.put("fileWritten", false);
                EngineUtils1.abandonFileWrite(writeInfo);
                LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[WRITE-FORMAT-INKLING][ZSHEET][FRAGMENTS][CELL_EDIT_HISTORY_VERSION]", dfse);
            }

            throw new Exception("[CELL_EDIT_HISTORY][Exception] Exception while updating cell edit history version", dfse);
        }
        catch(Exception e)
        {
            if(writeInfo != null)
            {
                writeInfo.put("fileWritten", false);
                EngineUtils1.abandonFileWrite(writeInfo);
            }
            LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[WRITE-FORMAT-INKLING][ZSHEET][FRAGMENTS][CELL_EDIT_HISTORY_VERSION] Error Occurred while updating existing cell edit history version for : " + associatedSheetName + " RESOURCE_ID: " + container.getResourceKey(), e);
            throw new Exception("[CELL_EDIT_HISTORY][Exception] Exception while updating cell edit history version");
        }
        finally
        {
            if(os != null)
            {
                try
                {
                    os.close();
                }
                catch(IOException ioe)
                {
                    writeInfo.put("fileWritten", false);
                    EngineUtils1.abandonFileWrite(writeInfo);
                    container.finishWrite(writeInfo);

                    LOGGER.log(Level.WARNING, "\uD83D\uDDC2️[WRITE-FORMAT-INKLING][ZSHEET][FRAGMENTS][CELL_EDIT_HISTORY_VERSION] Error Occurred while writing cell edit history file: IOException:: " + associatedSheetName + " RESOURCE_ID: " + container.getResourceKey(), ioe);
                    throw new Exception("[CELL_EDIT_HISTORY][Exception] Exception while updating cell edit history version");
                }
            }
            if(writeInfo != null)
            {
                container.finishWrite(writeInfo);
            }
        }
    }
	    
    private static void migrateAllNotification(String docOwner, String docid, JSONArrayWrapper notificationArray) {

        String notificationTableName = "NotificationSettings";  //No I18N
        Persistence per = SheetPersistenceUtils.getPersistence(docOwner);
        ZUIDMigrationHelper zuidMigHelper =  ZUIDMigrationHelper.getInstance();
        for(int i = 0; i < notificationArray.length(); i++) {
            DataObject dobj = null;
            try {
                dobj = per.constructDataObject();
                Row row = new Row(notificationTableName);

                JSONObjectWrapper notificationFragment = notificationArray.getJSONObject(i);

                row.set("DOCUMENT_ID", Long.valueOf(docid));  //NO I18N
                row.set("SUBSCRIBER", Long.valueOf(notificationFragment.getString("SUBSCRIBER")));  //NO I18N
                row.set("SUBSCRIBER_ID", notificationFragment.getString("SUBSCRIBER_ID")); //NO I18N
                row.set("SUBSCRIBER_MEMBERS", getZuidUpdatedSubscribers(zuidMigHelper, docOwner, notificationFragment.getString("SUBSCRIBER_MEMBERS")));  //NO I18N
                row.set("LAST_MODIFIED_TIME", notificationFragment.getString("LAST_MODIFIED_TIME"));  //NO I18N
                dobj.addRow(row);
                per.add(dobj);
            } catch (Exception e) {
            	LOGGER.log(Level.WARNING, "[ZS_IMPORTER] Exception while writing comments ", e);	
            }
        }
    }

    public static JSONArrayWrapper getNotificationJSON(String documentOwner, String documentId) {

        String notificationTableName = "NotificationSettings";  //No I18N
        JSONArrayWrapper notificationArray = new JSONArrayWrapper();

        try {
            Persistence persistence = SheetPersistenceUtils.getPersistence(documentOwner);
            SelectQueryImpl sql = new SelectQueryImpl(new Table(notificationTableName));
            sql.addSelectColumn(new Column(notificationTableName, "*"));
            Criteria docIdCriteria = new Criteria(new Column(notificationTableName, "DOCUMENT_ID"), documentId, QueryConstants.EQUAL);
            sql.setCriteria(docIdCriteria);
            DataObject dobj = persistence.get(sql);
            Iterator<Row> Iter = dobj.getRows(notificationTableName);
            while(Iter.hasNext()) {
                Row row = Iter.next();
                JSONObjectWrapper notificationObject = new JSONObjectWrapper();
                String notificationID = row.get("NOTIFICATION_ID").toString();
                notificationObject.put("DOCUMENT_ID", row.get("DOCUMENT_ID").toString());
                notificationObject.put("SUBSCRIBER", row.get("SUBSCRIBER").toString());
                notificationObject.put("SUBSCRIBER_ID", row.get("SUBSCRIBER_ID").toString());
                notificationObject.put("SUBSCRIBER_MEMBERS", row.get("SUBSCRIBER_MEMBERS").toString());
                notificationObject.put("LAST_MODIFIED_TIME", row.get("LAST_MODIFIED_TIME").toString());
                notificationArray.put(notificationObject);
            }
        } catch (Exception e) {
            LOGGER.info("Problem while getting notification Content::"+ e);
        }
        return notificationArray;
    }

    public static String getZuidUpdatedSubscribers(ZUIDMigrationHelper migHelper, String owner, String subscribers) {

        LOGGER.info("OLD subscribers::"+subscribers);
        String newsubscribers = "";
        try {
            if(subscribers!= null) {
                String subscriberS[] = subscribers.split(",");
                for(int i = 1; i<subscriberS.length; i++) {
                    String newZuid = migHelper.getMigratedZUID(owner, subscriberS[i]);
                    newsubscribers = newsubscribers + (newsubscribers.length() == 0 ? "" : ",") + newZuid;
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.WARNING,"Exception on getting getZuidUpdatedSubscribers",e);
        }

        LOGGER.info("new subscribers::"+newsubscribers);
        return newsubscribers;
    }


    //    public static void main(String[] args) throws IOException, XmlPullParserException {
//        String input = "";
//        String output = "";
//        try(ZSZipInputStream inputStream = new ZSZipInputStream(new FileInputStream(input));
//            ZipOutputStream outputStream = new ZipOutputStream(new FileOutputStream(output))) {
//            replace_zuid_delugeId(null, inputStream, outputStream);
//        }
//    }
}



