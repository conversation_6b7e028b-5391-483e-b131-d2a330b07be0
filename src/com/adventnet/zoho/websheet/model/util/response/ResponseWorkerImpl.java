/*$Id:$*/

package com.adventnet.zoho.websheet.model.util.response;

import com.adventnet.wms.common.exception.WMSException;
import com.adventnet.zoho.websheet.model.ActionExecutor;
import com.adventnet.zoho.websheet.model.ContainerListener;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.response.ResponseObject;
import com.adventnet.zoho.websheet.model.response.VersionBasedResponseAnalyzer;
import com.adventnet.zoho.websheet.model.response.UserInfo;
import com.adventnet.zoho.websheet.model.response.viewport.Constraints;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.logs.common.util.logging.OnDemandSearchLogger;

import javax.naming.LimitExceededException;
import java.time.Duration;
import java.time.Instant;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * <AUTHOR> <PERSON> J (ZT-0049)
 */
public class ResponseWorkerImpl implements Runnable, ResponseWorker
{

	private static final Logger LOGGER = Logger.getLogger(ResponseWorkerImpl.class.getName());

	private final ContainerListener listener;
	private final JSONObjectWrapper actionObject;
	private final WorkbookContainer container;
	private final Constraints constraints;
	private final UserInfo userInfo;
	private final ResponseObject responseObject;
	private final MessageBean messageBean;
	private JSONObjectWrapper handHeldResponse;

	/**
	 *
	 * @param container
	 * @param listener
	 * @param actionObject
	 * @param responseObject Refresh tile response will be sent if null
	 * @param constraints
	 * @param userInfo
	 */
	public ResponseWorkerImpl(WorkbookContainer container, ContainerListener listener, JSONObjectWrapper actionObject, ResponseObject responseObject, Constraints constraints, UserInfo userInfo)
	{
		this.container = container;
		this.listener = listener;
		this.actionObject = actionObject;
		this.constraints = constraints;
		this.userInfo = userInfo;
		this.responseObject = responseObject;
		this.messageBean = new MessageBean();
	}

	public UserInfo getUserInfo()
	{
		return userInfo;
	}

	public void setHandHeldResponse(JSONObjectWrapper handHeldResponse)
	{
		this.handHeldResponse = handHeldResponse;
	}

	public JSONObjectWrapper getActionObject()
	{
		return this.actionObject;
	}

	private String getActionOwnerZUID()
	{
		return this.getActionObject().getString(JSONConstants.ZUID);
	}

	private int getAction()
	{
		return this.getActionObject().getInt(JSONConstants.ACTION);
	}

	private int getActionId()
	{
		return this.getActionObject().getInt(JSONConstants.ACTION_ID);
	}

	private boolean isActionOwner()
	{
		String senderRsid = (actionObject != null) ? actionObject.optString(JSONConstants.RSID, null) : null;
		String senderUtid = (actionObject != null) ? actionObject.optString(JSONConstants.UNIQUE_TAB_ID, null) : null;
		return (senderUtid != null && senderUtid.equals(this.getUserInfo().getUtid())) || (senderRsid != null && senderRsid.equals(userInfo.getRsid()));
	}

	private boolean isMacroRun()
	{
		return actionObject != null && actionObject.has(JSONConstants.ACTION) && actionObject.getInt(JSONConstants.ACTION) == ActionConstants.MACRO_RUN;
	}

	@Override
	public MessageBean getMessage()
	{
		messageBean.setHandHeldsResp(handHeldResponse);
		messageBean.setWmsRawSessionId(userInfo.getRsid());
		messageBean.setuserInfo(userInfo);
		return messageBean;
	}

	public void sendRefreshTileResponse()
	{
		try
		{
			this.generateRefreshTileResponse();
			this.sendMessage();
		}
		catch(Exception e)
		{
			LOGGER.log(Level.SEVERE, "[RESPONSE][Exception] Exception while sending refresh tile response.", e);
			LOGGER.log(Level.SEVERE, "[RESPONSE][Exception] Exception while sending refresh tile response for user {0}.", new Object[]{userInfo.getZuid()});
		}
	}

	private JSONObjectWrapper getActionIdentifier()
	{
		JSONObjectWrapper actionIdentifierJson = new JSONObjectWrapper();

		int aid = actionObject.optInt(JSONConstants.ACTION_ID, -1);
		int a = actionObject.optInt(JSONConstants.ACTION, -1);
		int lastExecutedActionId = actionObject.optInt(JSONConstants.LAST_EXECUTED_ACTION_ID, -1);
		int lastSavedId = actionObject.optInt(JSONConstants.LAST_SAVED_ID, -1);
		String rsid = actionObject.optString(JSONConstants.RSID, null);
		String utid = actionObject.optString(JSONConstants.UNIQUE_TAB_ID, rsid);

		int documentServedState = actionObject.optInt(JSONConstants.DOCUMENT_SERVED_STATE, -1);
		boolean isServerAction = actionObject.has(JSONConstants.IS_SERVER_ACTION);

		actionIdentifierJson.put(Integer.toString(CommandConstants.UNIQUE_TAB_ID), utid);
		if(a != -1)
		{
			actionIdentifierJson.put(Integer.toString(CommandConstants.ACTION_CONSTANT), a);
		}
		if(aid != -1)
		{
			actionIdentifierJson.put(Integer.toString(CommandConstants.EXECUTED_ACTION_ID), aid);
		}
		if(lastExecutedActionId != -1)
		{
			actionIdentifierJson.put(Integer.toString(CommandConstants.LAST_EXECUTED_ACTION_ID), lastExecutedActionId);
		}
		if(lastSavedId != -1)
		{
			actionIdentifierJson.put(Integer.toString(CommandConstants.LAST_SAVED_ID), lastSavedId);
		}
		if(documentServedState != -1)
		{
			actionIdentifierJson.put(Integer.toString(CommandConstants.DOCUMENT_SERVED_STATE), documentServedState);
		}
		if(!isServerAction)
		{
			actionIdentifierJson.put(Integer.toString(CommandConstants.RSID), rsid);
			actionIdentifierJson.put(Integer.toString(CommandConstants.IS_SERVER_ACTION), false);
		}
		else
		{
			actionIdentifierJson.put(Integer.toString(CommandConstants.IS_SERVER_ACTION), true);
		}

		return actionIdentifierJson;
	}

	private void generateRefreshTileResponse()
	{
		JSONObjectWrapper forcedRefreshTile = new JSONObjectWrapper();
		forcedRefreshTile.put(Integer.toString(CommandConstants.ACTION_IDENTIFIER), this.getActionIdentifier());
		forcedRefreshTile.put(Integer.toString(CommandConstants.FORCED_REFRESH_TILE), 1);

		JSONObjectWrapper documentMetaObj = new JSONObjectWrapper();
		documentMetaObj.put(container.getResourceKey(), forcedRefreshTile);

		JSONObjectWrapper responseData = new JSONObjectWrapper();
		responseData.put(Integer.toString(CommandConstants.DOCUMENT_META), documentMetaObj);
		responseData.put(Integer.toString(CommandConstants.IS_NEW_CLIENT_RESPONSE), true);

		this.setHandHeldResponse(responseData);
	}

	private void generateResponse() throws Exception
	{
		Instant startTime = Instant.now();
		JSONObjectWrapper generatedResponse;
		if(userInfo.getResponseVersion() == ResponseObject.ResponseVersion.V1 && !ActionExecutor.isActionResponseRequired(container, this.getActionOwnerZUID(), userInfo.getZuid(), this.getAction()))
		{
			generatedResponse = (JSONObjectWrapper) VersionBasedResponseAnalyzer.getResponse(responseObject, null, userInfo);
		}
		else
		{
			generatedResponse = (JSONObjectWrapper) VersionBasedResponseAnalyzer.getResponse(responseObject, constraints, userInfo);
		}
		/* Below if condition is added for macro run case,
		 * where actionId is not available. Hence it is added to the response at this point.  */
		if(this.isMacroRun())
		{
			generatedResponse = MessagePropagator.appendActionIdToResponse(this.getActionId(), container.getResourceKey(), generatedResponse);
		}
		this.setHandHeldResponse(generatedResponse);

		Instant endTime = Instant.now();
		long totalTime = Duration.between(startTime, endTime).toMillis();
		OnDemandSearchLogger.log(LOGGER, Level.INFO,"[RESPONSE] Time taken to generate response for the user {0} in worker thread is {1} ms.", new Object[]{userInfo.getZuid(), totalTime});
	}

	private void sendWMSMessage() throws WMSException, LimitExceededException
	{
		MessagePropagator.sendWMSResponse(container, actionObject, this.getMessage());
	}

	private void sendMessage() throws Exception
	{
		MessagePropagator.sendResponse(container, listener, actionObject, this.getMessage());
	}

	private void setThreadName()
	{
		String tName = "RESPONSE_THREAD"+ "#RESOURCE_ID#" + container.getResourceKey() + "#ACTION#" + this.getActionObject().optInt(JSONConstants.ACTION, -1) + "#AID#" + this.getActionObject().optInt(JSONConstants.ACTION_ID, -1) + "#USER#ZUID#" + this.getUserInfo().getZuid() +"#RSID#" + this.getUserInfo().getRsid() +"#TABID#"+ this.getUserInfo().getUtid(); //No I18N
		Thread.currentThread().setName(tName);
	}

	@Override
	public void run()
	{
		try
		{
			this.setThreadName();
//			if(this.responseObjectFixer == null)
//			{
//				LOGGER.log(Level.SEVERE, "[RESPONSE][Exception] Response Object is null. Refresh tiles message is being generated for user {0}.", new Object[]{userInfo.getZuid()});
//				this.generateRefreshTileResponse();
//			}
//			else
//			{
				this.generateResponse();
//			}
			this.sendMessage();
		}
		catch(LimitExceededException e)
		{
			LOGGER.log(Level.SEVERE, "[RESPONSE][Exception] Response Limit exceeded for the action "+this.getAction(), e);
			LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Response Limit exceeded. Refresh tiles message is being sent for user {0}.", new Object[]{userInfo.getZuid()});
			this.sendRefreshTileResponse();
		}
		catch(WMSException e)
		{
			LOGGER.log(Level.SEVERE, "[RESPONSE][Exception] Exception while sending wms response for user {0}.", new Object[]{userInfo.getZuid()});
		}
		catch(Exception e)
		{
			LOGGER.log(Level.SEVERE, "[RESPONSE][Exception] Exception while running response worker thread.", e);
			LOGGER.log(Level.WARNING, "[RESPONSE][Exception] Exception while generating response. Refresh tiles message is being sent for user {0}.", new Object[]{userInfo.getZuid()});
			this.sendRefreshTileResponse();
		}
	}
}