//$Id$
package com.adventnet.zoho.websheet.model.util;

import java.util.Enumeration;
import java.util.Hashtable;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR> [sasikumar-0877]
 * 
 * @Notes Controls the Thread's LifeSpan (to stop slow threads)
 */

public class LifeSpanController {
	
	public static Logger logger = Logger.getLogger(LifeSpanController.class.getName());
	
// ___________________________________________________________________________________________________________	

	/* -- HOLDER BLOCK -- */
	
	// 'Holder' used to hold the details
	private static Hashtable<String, Hashtable> holder = new Hashtable<String, Hashtable>();
	private static Long threadLifeSpan = (EnginePropertyUtil.getSheetPropertyValue("ThreadLifeSpan") != null) ? Long.parseLong(EnginePropertyUtil.getSheetPropertyValue("ThreadLifeSpan")) : 210000l; //3 min 30 sec - default [Since few openoffice import takes ~2 min] // NO I18N
	private static final String THREAD = "t"; //NO I18N
	private static final String THREAD_LIFE_SPAN = "tls"; //NO I18N
	private static final String THREAD_REQUEST = "treq"; //NO I18N
	private static final String THREAD_RESPONSE = "tres"; //NO I18N
	private static final String THREAD_REGISTERED_TIME = "trt"; //NO I18N

	/* Log print option:

		ThreadSpanRemove: Thread which was removed 
		ThreadSpanRedirectProblem: Thread which has problem while redirecting it to error page
		ThreadSpanRemoveProblem: Thread which has problem while removing
		ThreadSpanMaxTimeTaken: Execution time exceeds 100 ms.
		
	*/
	
	Thread thread;
	String threadName;
	Long timePersist;
	HttpServletRequest request;
	HttpServletResponse response;
	String uniqueKey;
	
	public LifeSpanController(Thread thread, String threadName, Long timePersist, HttpServletRequest request, HttpServletResponse response) {
		this.thread =  thread;
		this.threadName = threadName;
		this.timePersist = timePersist;
		this.request =  request;
		this.response = response;
	}
	
	public void control() {
		
		if(this.threadName != null) {
			thread.setName(this.threadName);
		}
	
		Long lifeSpan = (this.timePersist != null) ? this.timePersist : threadLifeSpan;
		
		this.uniqueKey = java.util.UUID.randomUUID().toString();
		
		Hashtable threadDetails = new Hashtable();
		threadDetails.put(THREAD_LIFE_SPAN, lifeSpan);
		threadDetails.put(THREAD_REGISTERED_TIME, System.currentTimeMillis());
		if(this.request != null) {
			threadDetails.put(THREAD_REQUEST, this.request);
		}
		if(this.response != null) {
			threadDetails.put(THREAD_RESPONSE, this.response);
		}
		threadDetails.put(THREAD, this.thread);
		
		holder.put(this.uniqueKey, threadDetails);
		
	}
	
	public void cancel() {
		
		holder.remove(this.uniqueKey);
		
		//logger.info(LOG_KEY + this.uniqueKey + ((this.threadName != null) ? (" [" + this.threadName +"]") : "") +" removed. {"+ holder.size() +"}");
	}
	
// _____________________________________________________________________________________________________________
	
	/* -- SCHEDULER BLOCK -- */
	
	static class ThreadCleanScheduler {
		
		private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
		
		public void clean() {
			
			final Runnable cleanHandler = new Runnable() {
		
				@Override
				public void run() {
					try {
				
						Long currTime = System.currentTimeMillis();
						int removedThreadCount = 0;
						
						Enumeration<String> elements = holder.keys();
				    	while(elements.hasMoreElements()) {
				    		
			    			String uniqueKey = (String) elements.nextElement();
				    		Hashtable threadDetails = (Hashtable) holder.get(uniqueKey);
				    	
				    		try{
							    	
					    		Thread thread = (Thread) threadDetails.get(THREAD);
					    		
					    		// In web-application, Apache threads are always alive. For safer end:
					    		Boolean isValidThread = true;
					    		if(!thread.isAlive() || Thread.State.TERMINATED == thread.getState()) {
					    			holder.remove(uniqueKey);
					    			removedThreadCount++;
					    			//logger.info(LOG_KEY + uniqueKey +" ["+thread.getName() +"] removed - Not alive/Terminated thread.{"+ holder.size()+"}");
					    			isValidThread = false;
					    		}
					    		
					    		if(isValidThread) {
						    		Long lifeSpan = (Long) threadDetails.get(THREAD_LIFE_SPAN);
						    		Long threadRegTime = (Long) threadDetails.get(THREAD_REGISTERED_TIME);
					    			if((currTime - threadRegTime) > lifeSpan){
					    			
					    				holder.remove(uniqueKey);
	
					    				logger.info(" ## ThreadSpanRemove ## " + uniqueKey +" ["+thread.getName() +"] removed. runs @ " + (currTime - threadRegTime)/60000 + " minute. {"+ holder.size()+"}");
					    				
					    				removedThreadCount++;
					    				HttpServletRequest request = (HttpServletRequest) threadDetails.get(THREAD_REQUEST);
					    				HttpServletResponse response = (HttpServletResponse) threadDetails.get(THREAD_RESPONSE);
					    				if(request != null && response != null) {
					    					response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
					    					try {
					    						request.getRequestDispatcher("/sheet/appsheet/pages/Error/Error.jsp").forward(request, response); //NO I18N
					    					} catch (Exception reEx){
					    						logger.log(Level.WARNING, " ## ThreadSpanRedirectProblem ## Problem while redirecting the request. req: " + request +" ,res: " + response + ", threadDetails: "  +threadDetails +" {"+holder.size()+"} ");
					    					}
					    				}
					    				
					    				//thread.stop(); - Not suggested
					    				thread.interrupt();
					    				
						    		}
					    		}
				    		} catch (Exception elEx) {
				    			logger.log(Level.WARNING, " ## ThreadSpanRemoveProblem ## Problem while removing the Thread Object. " + threadDetails +" {"+holder.size()+"} ");
				    		}
				    	}
				    	
				    	long timeTaken = System.currentTimeMillis()-currTime;
				    	if(timeTaken >= 100) {
				    		logger.log(Level.WARNING, " ## ThreadSpanMaxTimeTaken ##  Execution time exceeds 100 milliseconds. Time taken: " + timeTaken +" ms. {"+holder.size()+"}");
				    	}
				    	
				    	//logger.info(LOG_KEY + "Time taken: " + (System.currentTimeMillis()-currTime) +" ms. removed thread count: " + removedThreadCount +".{"+holder.size()+"}");
					} catch(Exception ex) {
						logger.log(Level.WARNING, "Exception occured while running the ThreadCleaner: {"+holder.size()+"}", ex);
					}
				}
			};

			String _lifeSpanSchTime = EnginePropertyUtil.getSheetPropertyValue("ThreadLifeSpan_SchedulerTime");    //No I18N
    		int lifeSpanSchTime = (_lifeSpanSchTime != null) ? Integer.parseInt(_lifeSpanSchTime) : 1; // 1 minute
    		
			scheduler.scheduleAtFixedRate(cleanHandler, 1, lifeSpanSchTime, TimeUnit.MINUTES); 
			// Started after 1 minute from server starts and runs @ every 2 minutes 
		}
	}
	
	private static ThreadCleanScheduler cleanScheduler = new ThreadCleanScheduler();
	static {
		cleanScheduler.clean();
	}

// ___________________________________________________________________________________________________________
	
}
