// $Id$
/*
 * RangeUtil.java
 *
 * Created on October 14, 2009, 3:36 AM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.util;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.CellImpl.BorderStyleKey;
import com.adventnet.zoho.websheet.model.exception.ActionTimeOutException;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.ext.IntegralSet;
import com.adventnet.zoho.websheet.model.ext.LinearIntegralRange;
import com.adventnet.zoho.websheet.model.ext.TreeBasedIntegralSet;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.ext.functions.RangeFunctions;
import com.adventnet.zoho.websheet.model.ext.parser.ASTRangeNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSRefEvaluator;
import com.adventnet.zoho.websheet.model.filter.DataType;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.filter.exception.FilterException;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants;
import com.adventnet.zoho.websheet.model.style.datastyle.DefaultPatternUtil;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.JepException;
import com.singularsys.jep.parser.ASTVarNode;
import com.singularsys.jep.parser.Node;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.chart.ChartUtils;
import com.zoho.sheet.chart.TableConstraints;
import com.zoho.sheet.chart.TablePrediction;

import java.time.Instant;
import java.util.*;
import java.util.function.Predicate;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 *
 * <AUTHOR>
 */
public class RangeUtil
{
    public static final Logger LOGGER = Logger.getLogger(RangeUtil.class.getName());

    private static Row getPreviousRow(Sheet sheet, int rowIndex)
    {
        return sheet.getPreviousRow(rowIndex);
    }

    private static Cell getPreviousCell(Sheet sheet, int rowIndex, int colIndex)
    {
        for(int i = (colIndex - 1); i >= 0; i--)
        {
            Cell prevCell = sheet.getCellReadOnly(rowIndex, i);
            if(prevCell != null)
            {
                return prevCell;
            }
        }
        return null;
    }

    public static Row createNewRow(Sheet sheet, int rowIndex, int endRowIndex)
    {
        Row newRow = null, prevRow = getPreviousRow(sheet, rowIndex);
        if(prevRow != null && prevRow.getRowsRepeated() > (rowIndex - prevRow.getRowIndex()))
        {
            newRow = sheet.getRow(rowIndex);
        }
        else
        {
            // so the row is null
            int rowsRepeated = GeneralUtil.getConsecutiveNullCount(sheet.getRows(), rowIndex, endRowIndex);
            newRow = sheet.getRow(rowIndex);
            newRow.setRowsRepeated(rowsRepeated);
        }

        return newRow;
    }

    public static Cell createNewCell(Sheet sheet, List<Cell> cells, int rowIndex, int colIndex, int endColIndex)
    {
        Cell newCell = null, prevCell = getPreviousCell(sheet, rowIndex, colIndex);
        if(prevCell != null && prevCell.getColsRepeated() > (colIndex - prevCell.getColumnIndex()))
        {
            newCell = sheet.getCell(rowIndex, colIndex);
        }
        else
        {
            // so the row is null
            int colsRepeated = GeneralUtil.getConsecutiveNullCount(cells, colIndex, endColIndex);

            newCell = sheet.getCell(rowIndex, colIndex);
            newCell.setColsRepeated(colsRepeated);
        }
        return newCell;
    }

    public static ColumnHeader createNewColumnHeader(Sheet sheet, int columnIndex, int endColumnIndex)
    {
        ColumnHeader newColumnHeader = null, prevColumnHeader = null;
        for(int i=(columnIndex-1); i>=0; i--)
        {
            prevColumnHeader = sheet.getColumnHeaderReadOnly(i);
            if(prevColumnHeader != null)
            {
                break;
            }
        }
        if(prevColumnHeader != null && prevColumnHeader.getColsRepeated() > (columnIndex - prevColumnHeader.getColumn().getColumnIndex()))
        {
            newColumnHeader = sheet.getColumnHeader(columnIndex);
        }
        else
        {
            // so the row is null
            int colsRepeated = GeneralUtil.getConsecutiveNullCount(sheet.getColumnHeaders(), columnIndex, endColumnIndex);
            newColumnHeader = sheet.getColumnHeader(columnIndex);
            newColumnHeader.setColsRepeated(colsRepeated);
        }
        return newColumnHeader;
    }

    public static void createOrUpdateColumnHeaders(Sheet sheet, int startColIndex, int endColIndex)
    {
        int endCol = Math.min(endColIndex, Math.max(startColIndex, sheet.getColNum() - 1));
        for(int colIndex = startColIndex; colIndex <= endCol; colIndex++)
        {
            ColumnHeader colHeader = sheet.getColumnHeaderReadOnly(colIndex) == null ? createNewColumnHeader(sheet, colIndex, endColIndex) : sheet.getColumnHeaderReadOnly(colIndex);
            // TODOD : if it exceeds 65536
            if(colHeader.getColsRepeated() > (endColIndex - colIndex + 1))
            {
                sheet.getColumnHeader(endColIndex + 1);
            }
            colIndex += colHeader.getColsRepeated() - 1;
        }
        if(endColIndex > endCol)
        {
            ColumnHeader colHeader = sheet.getColumnHeader(endCol + 1);
            colHeader.setColsRepeated(endColIndex - endCol);
        }
    }

    public static List<ColumnHeader> getColumnHeaders(Sheet sheet, int startColIndex, int endColIndex)
    {
        List<ColumnHeader> list = new ArrayList<>();
        int endCol = Math.min(endColIndex, Math.max(startColIndex, sheet.getColNum() - 1));
        for(int colIndex = startColIndex; colIndex <= endCol; colIndex++)
        {
            ColumnHeader colHeader = sheet.getColumnHeaderReadOnly(colIndex) == null ? createNewColumnHeader(sheet, colIndex, endColIndex) : sheet.getColumnHeaderReadOnly(colIndex);
            // TODOD : if it exceeds 65536
            if(colHeader.getColsRepeated() > (endColIndex - colIndex + 1))
            {
                sheet.getColumnHeader(endColIndex + 1);
            }

            // call the colstyle set property here
            list.add(colHeader);
            //////
            colIndex += colHeader.getColsRepeated() - 1;
        }

        if(endColIndex > endCol)
        {
            ColumnHeader colHeader = sheet.getColumnHeader(endCol + 1);
            colHeader.setColsRepeated(endColIndex - endCol);
            list.add(colHeader);
        }

        return list;
    }

    public static List<Row> getRows(Sheet sheet, int startRowIndex, int endRowIndex)
    {
        return getRows(sheet, startRowIndex, endRowIndex, false);
    }

    public static List<Row> getRows(Sheet sheet, int startRowIndex, int endRowIndex, boolean isVisibleRowsOnly)
    {
        List<Row> list = new ArrayList<>();
        int endRow = Math.min(endRowIndex, Math.max(startRowIndex, sheet.getRowNum() - 1));
        for(int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++)
        {
            Row row = sheet.getRowReadOnly(rowIndex) == null ? createNewRow(sheet, rowIndex, endRowIndex) : sheet.getRowReadOnly(rowIndex);
            // TODOD : if it exceeds 65536
            if(row.getRowsRepeated() > (endRowIndex - rowIndex + 1))
            {
                sheet.getRow(endRowIndex + 1);
            }
            Boolean condition = isVisibleRowsOnly ? RowUtil.isVisibleRow(row) : true;
            if(condition)
            {
                list.add(row);
            }
            //////
            rowIndex += row.getRowsRepeated() - 1;
        }

        if(endRowIndex > endRow)
        {
            Row row = sheet.getRow(endRow + 1);
            row.setRowsRepeated(endRowIndex - endRow);
            list.add(row);
        }
        return list;
    }

    public static void updateChartReGenStatus(Sheet sheet, DataRange dataRange, boolean checkForHiddenCells)
    {
        Workbook workbook = sheet.getWorkbook();
        if(workbook != null)
        {
            Map<String, Map<String, Chart>> chartMap = workbook.getChartMap();
            if(chartMap != null)
            {
                Map<String, Chart> sheetChartMap = chartMap.get(sheet.getAssociatedName());
                if(sheetChartMap != null)
                {
                    Collection<Chart> chartsList = sheetChartMap.values();
                    for(Chart chart : chartsList)
                    {
                        if(chart != null && chart.isInDataRanges(dataRange) && (!checkForHiddenCells || !chart.getisIncludeHiddenCells()))
                        {
                            chart.setReGenRequired(true);
                            if(ChartUtils.isChartPublished(chart.getChartId()))
                            {
                                try
                                {
                                    ChartUtils.updateChartsGodown(chart.getDocumentId(), chart.getSheetName(), chart.getChartId(), chart.getPublicChartName());
                                }
                                catch(Exception e)
                                {
                                    LOGGER.log(Level.INFO, "[CHART] Exception in updating charts godown {0}", e);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public static void updateChartReGenStatus(Sheet sheet, FilterView view, Boolean checkForHiddenCell)
    {
       updateChartReGenStatus(sheet, view.getDataRange(), checkForHiddenCell);
    }

    public static void setCellStyleProperty(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, Style.Property propName, Object propValue)
    {
        List<Style.Property> propNames = new ArrayList<>();
        propNames.add(propName);

        List<Object> propValues = new ArrayList<>();
        propValues.add(propValue);

        setCellStyleProperty(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, propNames, propValues);
    }

    public static void setCellStyleProperty(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, List<Style.Property> propNames, List<Object> propValues) throws IllegalArgumentException
    {
        if(!(propNames.size() == propValues.size()))
        {
            throw new IllegalArgumentException("The 3 lists must be uniform in size ...");//No I18N
        }

        if(startRowIndex == 0 && endRowIndex == Utility.MAXNUMOFROWS - 1)
        {
            setCHCellStyleProperty(sheet, startColIndex, endColIndex, propNames, propValues);
            return;
        }

        sheet.getTables().values().stream()
            .forEach(table -> table.notifyStyleChange(sheet.getWorkbook(), new DataRange(sheet.getAssociatedName(), startRowIndex, startColIndex, endRowIndex, endColIndex), propNames, propValues));


        sheet.setIsModified(true);

        boolean isCreateNewStyle = false;
        for(Object value : propValues)
        {
            if(value != null)// && !value.equals("none") && !value.equals(BorderProperties.BORDER_NONE))
            {
                isCreateNewStyle = true;
                break;
            }
        }

        //String newStyleName = "temp" + UUID.randomUUID(); //No I18N
        Map<String, String> changes = new HashMap<>();

        int endRow = Math.min(endRowIndex, Math.max(startRowIndex, sheet.getRowNum() - 1));
        for(int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++)
        {
            Row row = sheet.getRowReadOnly(rowIndex) == null ? createNewRow(sheet, rowIndex, endRowIndex) : sheet.getRowReadOnly(rowIndex);
            // TODOD : if it exceeds 65536
            if(row.getRowsRepeated() > (endRowIndex - rowIndex + 1))
            {
                sheet.getRow(endRowIndex + 1);
            }

            int endCol = Math.min(endColIndex, Math.max(startColIndex, row.getCellNum() - 1));
            for(int colIndex = startColIndex; colIndex <= endCol; colIndex++)
            {
                Cell cell = sheet.getCellReadOnly(rowIndex, colIndex) == null ? createNewCell(sheet, row.getCells(), rowIndex, colIndex, endColIndex) : sheet.getCellReadOnly(rowIndex, colIndex);
                // TODOD : if it exceeds 256
                if(cell.getColsRepeated() > (endColIndex - colIndex + 1))
                {
                    sheet.getCell(rowIndex, endColIndex + 1);
                }

                if(cell.getStyleName() == null)
                {
                    ColumnHeader ch = sheet.getColumnHeader(cell.getColumnIndex());
                    sheet.getCell(rowIndex, cell.getColumnIndex() + Math.min(cell.getColsRepeated(), ch.getColsRepeated()));
                }

                setNewCellStyle(cell, changes, propNames, propValues, isCreateNewStyle);

                colIndex += cell.getColsRepeated() - 1;
            }

            if(endColIndex > endCol)
            {
                Cell cell = sheet.getCell(rowIndex, endCol + 1);
                setNewCellStyle(cell, changes, propNames, propValues, isCreateNewStyle);

                cell.setColsRepeated(endColIndex - endCol);
            }

            rowIndex += row.getRowsRepeated() - 1;
        }

        if(endRowIndex > endRow)
        {
            //Row row = sheet.getRow(endRow + 1);
            Cell cell = sheet.getCell(endRow + 1, startColIndex);
            setNewCellStyle(cell, changes, propNames, propValues, isCreateNewStyle);

            cell.setColsRepeated(endColIndex - startColIndex + 1);
            Row row = cell.getRow();
            row.setRowsRepeated(endRowIndex - endRow);
        }
    }

    private static void setNewCellStyle(Object cellOrCH, Map<String, String> changes, List<Style.Property> propNames, List<Object> propValues, boolean isCreateNewStyle)
    {
        boolean isForSetPattern = propNames.contains(CellStyle.Property.PATTERN);

        CellStyle cellStyleReadOnly = (cellOrCH instanceof Cell) ? ((CellImpl) cellOrCH).getCellStyleReadOnly() : ((ColumnHeader)cellOrCH).getCellStyleReadOnly();
        String oldStyleName = (cellStyleReadOnly == null) ? null : cellStyleReadOnly.getStyleName();
        String newStyleName = changes.get(oldStyleName);
        if(newStyleName == null)
        {
            boolean isNewCellStyle = false;
            if(cellStyleReadOnly == null && isCreateNewStyle)
            {
                cellStyleReadOnly = new CellStyle();
                isNewCellStyle = true;
            }

            if(cellStyleReadOnly != null)
            {
                if(!isNewCellStyle)
                {
                    cellStyleReadOnly = cellStyleReadOnly.clone();
                }
                // set the properties
                cellStyleReadOnly.setProperty(propNames, propValues);
                // Set new name

                cellStyleReadOnly.setStyleName("temp" + UUID.randomUUID());//No I18N
                // Add to workbook
                Sheet sheet = (cellOrCH instanceof Cell) ? ((CellImpl) cellOrCH).getRow().getSheet() : ((ColumnHeader)cellOrCH).getColumn().getSheet();
                cellStyleReadOnly = sheet.getWorkbook().checkAndAddCellStyle(cellStyleReadOnly);
                newStyleName = cellStyleReadOnly.getStyleName();
                changes.put(oldStyleName, newStyleName);
            }
        }

        if(newStyleName != null)
        {
            if(cellOrCH instanceof Cell)
            {
                ((CellImpl) cellOrCH).setStyleName(newStyleName, false, isForSetPattern);
            }
            else
            {
                ((ColumnHeader)cellOrCH).setDefaultCellStyleNameFromWriter(newStyleName);
            }
        }
    }

    public static void setCHCellStyleNameAsDefault(Sheet sheet, int startColIndex, int endColIndex)
    {
        sheet.setIsModified(true);
        List<ColumnHeader> colHeaders = getColumnHeaders(sheet, startColIndex, endColIndex);
        for(ColumnHeader colHeader : colHeaders)
        {
            // Setting null cellStyle will set the defaultCellStyleName as "Default".
            colHeader.setCellStyle(null, false);
        }
    }

    public static void setCellStyleNameAsNull(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        setCellStyleName(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, null);
    }

    public static void setCellStyleNameAsDefault(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        setCellStyleName(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, EngineConstants.DEFAULT_CELLSTYLENAME);
    }

    private static void setCellStyleName(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, String inStyleName)
    {
        sheet.setIsModified(true);

        int endRow = Math.min(endRowIndex, Math.max(startRowIndex, sheet.getRowNum() - 1));
        for(int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++)
        {
            Row row = sheet.getRowReadOnly(rowIndex) == null ? createNewRow(sheet, rowIndex, endRowIndex) : sheet.getRowReadOnly(rowIndex);
            // TODOD : if it exceeds 65536
            if(row.getRowsRepeated() > (endRowIndex - rowIndex + 1))
            {
                sheet.getRow(endRowIndex + 1);
            }
            int endCol = Math.min(endColIndex, Math.max(startColIndex, row.getCellNum() - 1));
            for(int colIndex = startColIndex; colIndex <= endCol; colIndex++)
            {
                Cell cell = sheet.getCellReadOnly(rowIndex, colIndex) == null ? createNewCell(sheet, row.getCells(), rowIndex, colIndex, endColIndex) : sheet.getCellReadOnly(rowIndex, colIndex);
                // TODOD : if it exceeds 256
                if(cell.getColsRepeated() > (endColIndex - colIndex + 1))
                {
                    sheet.getCell(rowIndex, endColIndex + 1);
                }

                ((CellImpl)cell).setStyleName(inStyleName, false);

                colIndex += cell.getColsRepeated() - 1;
            }

            if(endColIndex > endCol && inStyleName != null)
            {
                Cell cell = sheet.getCell(rowIndex, endCol + 1);
                ((CellImpl)cell).setStyleName(inStyleName, false);
                cell.setColsRepeated(endColIndex - endCol);
            }

            rowIndex += row.getRowsRepeated() - 1;
        }

        if(endRowIndex > endRow && inStyleName != null)
        {
            //Row row = sheet.getRow(endRow + 1);
            Cell cell = sheet.getCell(endRow + 1, startColIndex);
            ((CellImpl)cell).setStyleName(inStyleName, false);

            cell.setColsRepeated(endColIndex - startColIndex + 1);
            Row row = cell.getRow();
            row.setRowsRepeated(endRowIndex - endRow);
        }
    }

    public static void setPattern(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, ZSPattern pattern)
    {
        setCellStyleProperty(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, CellStyle.Property.PATTERN, pattern);
    }

    public static void setRangeBorder(Sheet sheet, int startRow, int startCol, int endRow, int endCol, int type, BorderProperties borderValue) throws IllegalArgumentException
    {
        List<Style.Property> borderTypeList;
        switch(type)
        {
            case ActionConstants.BORDER_LEFT_RIGHT:
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_LEFT, borderValue);
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_RIGHT, borderValue);
                return;
            case ActionConstants.BORDER_TOP_BOTTOM:
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_TOP, borderValue);
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_BOTTOM, borderValue);
                return;
            case ActionConstants.BORDER_OUTSIDE:
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_LEFT_RIGHT, borderValue);
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_TOP_BOTTOM, borderValue);
                return;
            case ActionConstants.BORDER_VERTICAL:
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_LEFT, borderValue);
                setCellStyleProperty(sheet, startRow, startCol, endRow, endCol, CellStyle.Property.BORDERRIGHT , borderValue);
                return;
            case ActionConstants.BORDER_HORIZONTAL:
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_TOP, borderValue);
                setCellStyleProperty(sheet, startRow, startCol, endRow, endCol, CellStyle.Property.BORDERBOTTOM , borderValue);
                return;
            case ActionConstants.BORDER_VERTICAL_OUTSIDE:
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_VERTICAL, borderValue);
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_TOP_BOTTOM, borderValue);
                return;
            case ActionConstants.BORDER_HORIZONTAL_OUTSIDE:
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_HORIZONTAL, borderValue);
                setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_LEFT_RIGHT, borderValue);
                return;
            case ActionConstants.BORDER_INSIDE:
                if(endRow - startRow > 0)
                {
                    setCellStyleProperty(sheet, startRow, startCol, endRow - 1, endCol, CellStyle.Property.BORDERBOTTOM , borderValue);
                }
                if(endCol - startCol > 0)
                {
                    setCellStyleProperty(sheet, startRow, startCol, endRow, endCol - 1, CellStyle.Property.BORDERRIGHT, borderValue);
                }
                return;
            case ActionConstants.BORDER_ALL_LEFT:
                setRangeBorder(sheet, startRow, startCol, endRow, Math.max(startCol, endCol - 1), ActionConstants.BORDER_VERTICAL, borderValue);
                return;
            case ActionConstants.BORDER_ALL_RIGHT:
                setRangeBorder(sheet, startRow, Math.min(startCol + 1, endCol), endRow, endCol, ActionConstants.BORDER_VERTICAL, borderValue);
                return;
            case ActionConstants.BORDER_ALL_TOP:
                setRangeBorder(sheet, startRow, startCol, Math.max(startRow, endRow - 1), endCol, ActionConstants.BORDER_HORIZONTAL, borderValue);
                return;
            case ActionConstants.BORDER_ALL_BOTTOM:
                setRangeBorder(sheet, Math.min(startRow + 1, endRow), startCol, endRow, endCol, ActionConstants.BORDER_HORIZONTAL, borderValue);
                return;
            case ActionConstants.BORDER_INSIDE_VERTICAL:
                if(endCol - startCol >= 1)
                {
                    setRangeBorder(sheet, startRow, startCol + 1, endRow, endCol - 1, ActionConstants.BORDER_VERTICAL, borderValue);
                }
                return;
            case ActionConstants.BORDER_INSIDE_HORIZONTAL:
                if(endRow - startRow >= 1)
                {
                    setRangeBorder(sheet, startRow + 1, startCol, endRow - 1, endCol, ActionConstants.BORDER_HORIZONTAL, borderValue);
                }
                return;
            case ActionConstants.BORDER_ALL:
//                if ("none".equals(borderValue))
//                {
                //setCellStyleProperty(sheet, startRow, startCol, endRow, endCol, styleName, "border", borderValue);//No I18N
                borderTypeList = new ArrayList<>();
                borderTypeList.add(CellStyle.Property.BORDERTOP);
                borderTypeList.add(CellStyle.Property.BORDERBOTTOM);
                borderTypeList.add(CellStyle.Property.BORDERLEFT);
                borderTypeList.add(CellStyle.Property.BORDERRIGHT);
                break;
//                }
//                else
//                {
//                    setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_HORIZONTAL, borderValue);//No I18N
//                    setRangeBorder(sheet, startRow, startCol, endRow, endCol, ActionConstants.BORDER_VERTICAL, borderValue);//No I18N
//                }
//                return;
            case ActionConstants.BORDER_TOP:
                borderTypeList = new ArrayList<>();
                borderTypeList.add(CellStyle.Property.BORDERTOP);
                endRow = startRow;
                break;
            case ActionConstants.BORDER_BOTTOM:
                borderTypeList = new ArrayList<>();
                borderTypeList.add(CellStyle.Property.BORDERBOTTOM);
                startRow = endRow;
                break;
            case ActionConstants.BORDER_LEFT:
                borderTypeList = new ArrayList<>();
                borderTypeList.add(CellStyle.Property.BORDERLEFT);
                endCol = startCol;
                break;
            case ActionConstants.BORDER_RIGHT:
                borderTypeList = new ArrayList<>();
                borderTypeList.add(CellStyle.Property.BORDERRIGHT);
                startCol = endCol;
                break;
            default: // Any other type should throw this exception
                throw new IllegalArgumentException("WRONG BORDER TYPE ------> " + type);//No I18N
        }

        removeAdjacentBorders(sheet, startRow, startCol, endRow, endCol, type);

        List<Object> borderValueList = new ArrayList<>();
        for (Style.Property borderType : borderTypeList) {
            borderValueList.add(borderValue);
        }
        setCellStyleProperty(sheet, startRow, startCol, endRow, endCol, borderTypeList, borderValueList);
    }

    private static void replicateAdjacentBorders(Sheet sheet, int startRow, int startCol, int endRow, int endCol, int borderType, BorderProperties borderValue) {
        if((borderType == ActionConstants.BORDER_ALL || borderType == ActionConstants.BORDER_TOP) && startRow > 0)
        {
            setCellStyleProperty(sheet, startRow - 1, startCol, startRow - 1, endCol, CellStyle.Property.BORDERBOTTOM, borderValue);
        }
        if((borderType == ActionConstants.BORDER_ALL || borderType == ActionConstants.BORDER_LEFT) && startCol > 0)
        {
            setCellStyleProperty(sheet, startRow, startCol - 1, endRow, startCol - 1, CellStyle.Property.BORDERRIGHT, borderValue);
        }
        if((borderType == ActionConstants.BORDER_ALL || borderType == ActionConstants.BORDER_BOTTOM) && endRow < Utility.MAXNUMOFROWS - 1)
        {
            setCellStyleProperty(sheet, endRow + 1, startCol, endRow + 1, endCol, CellStyle.Property.BORDERTOP, borderValue);
        }
        if((borderType == ActionConstants.BORDER_ALL || borderType == ActionConstants.BORDER_RIGHT) && endCol < Utility.MAXNUMOFCOLS - 1)
        {
            setCellStyleProperty(sheet, startRow, endCol + 1, endRow, endCol + 1, CellStyle.Property.BORDERLEFT, borderValue);
        }
    }

    private static void removeAdjacentBorders(Sheet sheet, int startRow, int startCol, int endRow, int endCol, int type) throws IllegalArgumentException
    {
        /* while setting  border
         * go to the cells surrounding the range and clear the borders
         * that seems to be applied for this range
         *
         * like border-right of cell A1 will look like border left of cell B1
         * so that has to be cleared while setting borders for cell B1.
         */
        if((type == ActionConstants.BORDER_ALL || type == ActionConstants.BORDER_TOP) && startRow > 0)
        {
            setCellStyleProperty(sheet, startRow - 1, startCol, startRow - 1, endCol, CellStyle.Property.BORDERBOTTOM, BorderProperties.BORDER_NONE);
        }
        if((type == ActionConstants.BORDER_ALL || type == ActionConstants.BORDER_LEFT) && startCol > 0)
        {
            setCellStyleProperty(sheet, startRow, startCol - 1, endRow, startCol - 1, CellStyle.Property.BORDERRIGHT, BorderProperties.BORDER_NONE);
        }
        if((type == ActionConstants.BORDER_ALL || type == ActionConstants.BORDER_BOTTOM) && endRow < Utility.MAXNUMOFROWS - 1)
        {
            setCellStyleProperty(sheet, endRow + 1, startCol, endRow + 1, endCol,  CellStyle.Property.BORDERTOP, BorderProperties.BORDER_NONE);
        }
        if((type == ActionConstants.BORDER_ALL || type == ActionConstants.BORDER_RIGHT) && endCol < Utility.MAXNUMOFCOLS - 1)
        {
            setCellStyleProperty(sheet, startRow, endCol + 1, endRow, endCol + 1, CellStyle.Property.BORDERLEFT, BorderProperties.BORDER_NONE);
        }
    }

    public static void clearForMerge(WorkbookContainer container, Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, int type, boolean isMergeFirstNonEmptyCell){
//        if(type == ActionConstants.MERGE_ACROSS)
//        {
//            for(int i = startRowIndex; i <= endRowIndex; i++)
//            {
//                clearForMerge(sheet, i, startColIndex, i, endColIndex, ActionConstants.MERGE_RANGE, isMergeFirstNonEmptyCell);
//            }
//            return;
//        }
//        else if(type == ActionConstants.MERGE_DOWN)
//        {
//            for(int i = startColIndex; i <= endColIndex; i++)
//            {
//                clearForMerge(sheet, startRowIndex, i, endRowIndex, i, ActionConstants.MERGE_RANGE, isMergeFirstNonEmptyCell);
//            }
//            return;
//        }
        sheet.setIsModified(true);
        boolean isWholeColumnAction = (startRowIndex == 0 && endRowIndex == Utility.MAXNUMOFROWS - 1);

        if(isMergeFirstNonEmptyCell) {
            switch(type) {
                case ActionConstants.MERGE_RANGE:
                case ActionConstants.MERGE_AND_CENTER:
                    movefirstNonEmptyCellIntoFirstCell(container, sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
                    break;
                case ActionConstants.MERGE_ACROSS:
                    for(int i = startRowIndex; i <= endRowIndex; i++) {
                        movefirstNonEmptyCellIntoFirstCell(container, sheet, i, startColIndex, i, endColIndex);
                    }
                    break;
                case ActionConstants.MERGE_DOWN:
                    for(int i = startColIndex; i <= endColIndex; i++) {
                        movefirstNonEmptyCellIntoFirstCell(container, sheet, startRowIndex, i, endRowIndex, i);
                    }
                    break;
            }
        }

        String[] topBorders= new String[Utility.MAXNUMOFROWS];
        String[] bottomBorders= new String[Utility.MAXNUMOFROWS];
        String[] leftBorders= new String[Utility.MAXNUMOFCOLS];
        String[] rightBorders= new String[Utility.MAXNUMOFCOLS];
        HashMap<CellImpl.BorderStyleKey, String> borderStylesMap= new HashMap<>();

        sheet.getRow(endRowIndex);
        CellStyle firstcellStyle=sheet.getCell(startRowIndex, startColIndex).getCellStyle();
        for(int rowIndex=startRowIndex; rowIndex<=endRowIndex; rowIndex++){
            Row row=sheet.getRow(rowIndex);
            if((rowIndex+row.getRowsRepeated()-1)>endRowIndex){
                sheet.getRow(endRowIndex+1);
            }
            sheet.getCell(rowIndex, endColIndex);
            for(int colIndex=startColIndex; colIndex<=endColIndex; colIndex++){
                Cell cell= sheet.getCell(rowIndex, colIndex);

                if((colIndex+cell.getColsRepeated()-1)>endColIndex){
                    sheet.getCell(rowIndex, endColIndex+1);
                }

                String cellTopBorder=null;
                String cellBottomBorder=null;
                String cellLeftBorder=null;
                String cellRightBorder=null;
                boolean isRootCell=false;

                CellStyle cellStyle= ((CellImpl) cell).getCellStyleReadOnly();
                if(type!=ActionConstants.MERGE_DOWN){
                    int arrIndex= rowIndex;
                    if(type==ActionConstants.MERGE_ACROSS || (rowIndex==startRowIndex)){
                        topBorders[arrIndex]= updateBorder(cellStyle, ActionConstants.BORDER_TOP, topBorders[arrIndex], sheet.getWorkbook());
                    }
                    if(type==ActionConstants.MERGE_ACROSS || (rowIndex==endRowIndex)){
                        bottomBorders[arrIndex]= updateBorder(cellStyle, ActionConstants.BORDER_BOTTOM, bottomBorders[arrIndex], sheet.getWorkbook());
                    }
                } else{
                    if(rowIndex==startRowIndex){
                        isRootCell=true;
                        cellTopBorder=getBorderFromCellStyle(cellStyle, ActionConstants.BORDER_TOP, sheet.getWorkbook());
                    }
                    if(rowIndex==endRowIndex){
                        cellBottomBorder=getBorderFromCellStyle(cellStyle, ActionConstants.BORDER_BOTTOM, sheet.getWorkbook());
                    }
                }

                if(type!=ActionConstants.MERGE_ACROSS){
                    int arrIndex= colIndex;
                    if(type==ActionConstants.MERGE_DOWN || colIndex==startColIndex){
                        leftBorders[arrIndex]= updateBorder(cellStyle, ActionConstants.BORDER_LEFT, leftBorders[arrIndex], sheet.getWorkbook());
                    }
                    if(type==ActionConstants.MERGE_DOWN || colIndex==endColIndex){
                        rightBorders[arrIndex]= updateBorder(cellStyle, ActionConstants.BORDER_RIGHT, rightBorders[arrIndex], sheet.getWorkbook());
                    }
                } else{
                    if(colIndex==startColIndex){
                        isRootCell=true;
                        cellLeftBorder=getBorderFromCellStyle(cellStyle, ActionConstants.BORDER_LEFT, sheet.getWorkbook());
                    }
                    if(colIndex==endColIndex){
                        cellRightBorder=getBorderFromCellStyle(cellStyle, ActionConstants.BORDER_RIGHT, sheet.getWorkbook());
                    }
                }

                if(type==ActionConstants.MERGE_RANGE || type==ActionConstants.MERGE_AND_CENTER){
                    isRootCell= (rowIndex==startRowIndex && colIndex==startColIndex);
                }

                ((CellImpl) cell).clearForMerge(borderStylesMap, firstcellStyle, cellTopBorder, cellBottomBorder, cellLeftBorder, cellRightBorder, isRootCell);

                colIndex+=cell.getColsRepeated()-1;
            }
            rowIndex+=row.getRowsRepeated()-1;
        }
        sheet.removeMergeRanges(new DataRange(sheet.getAssociatedName(), startRowIndex, startColIndex, endRowIndex, endColIndex));

        if(isWholeColumnAction){
            clearCHStyleForMerge(sheet, startColIndex, endColIndex, leftBorders[startColIndex], rightBorders[endColIndex]);
        }

        List<Range> coveredRanges= new ArrayList<>();
        switch(type){
            case ActionConstants.MERGE_RANGE:
            case ActionConstants.MERGE_AND_CENTER:
                if(!isBorderNone(topBorders[startRowIndex])){
                    setBorderLookingupMap(borderStylesMap, type, sheet, startRowIndex, startColIndex, startRowIndex, endColIndex, ActionConstants.BORDER_TOP, topBorders[startRowIndex], true);
                }
                if(!isBorderNone(bottomBorders[endRowIndex])){
                    setBorderLookingupMap(borderStylesMap, type, sheet, endRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.BORDER_BOTTOM, bottomBorders[endRowIndex], true);
                }
                if(!isBorderNone(leftBorders[startColIndex])){
                    setBorderLookingupMap(borderStylesMap, type, sheet, startRowIndex, startColIndex, endRowIndex, startColIndex, ActionConstants.BORDER_LEFT, leftBorders[startColIndex], true);
                }
                if(!isBorderNone(rightBorders[endColIndex])){
                    setBorderLookingupMap(borderStylesMap, type, sheet, startRowIndex, endColIndex, endRowIndex, endColIndex, ActionConstants.BORDER_RIGHT, rightBorders[endColIndex], true);
                }

                if(endColIndex>startColIndex){
                    coveredRanges.add(new Range(sheet, startRowIndex, startColIndex+1, startRowIndex, endColIndex));
                }
                if(endRowIndex>startRowIndex){
                    coveredRanges.add(new Range(sheet, startRowIndex+1, startColIndex, endRowIndex, endColIndex));
                }
                break;
            case ActionConstants.MERGE_ACROSS:
                for(int rowIndex=startRowIndex; rowIndex<=endRowIndex; rowIndex++){
                    Row row= sheet.getRow(rowIndex);
                    if(!isBorderNone(topBorders[rowIndex])){
                        setBorderLookingupMap(borderStylesMap, type, sheet, rowIndex, startColIndex, rowIndex, endColIndex, ActionConstants.BORDER_TOP, topBorders[rowIndex], false);
                    }
                    if(!isBorderNone(bottomBorders[rowIndex])){
                        setBorderLookingupMap(borderStylesMap, type, sheet, rowIndex, startColIndex, rowIndex, endColIndex, ActionConstants.BORDER_BOTTOM, bottomBorders[rowIndex], false);
                    }
                    rowIndex+=row.getRowsRepeated()-1;
                }

                if(endColIndex>startColIndex){
                    coveredRanges.add(new Range(sheet, startRowIndex, startColIndex+1, endRowIndex, endColIndex));
                }
                break;
            case ActionConstants.MERGE_DOWN:
                for(int colIndex=startColIndex; colIndex<=endColIndex; colIndex++){
                    if(!isBorderNone(leftBorders[colIndex])){
                        setBorderLookingupMap(borderStylesMap, type, sheet, startRowIndex, colIndex, endRowIndex, colIndex, ActionConstants.BORDER_LEFT, leftBorders[colIndex], false);
                    }
                    if(!isBorderNone(rightBorders[colIndex])){
                        setBorderLookingupMap(borderStylesMap, type, sheet, startRowIndex, colIndex, endRowIndex, colIndex, ActionConstants.BORDER_RIGHT, rightBorders[colIndex], false);
                    }
                }

                if(endRowIndex>startRowIndex){
                    coveredRanges.add(new Range(sheet, startRowIndex+1, startColIndex, endRowIndex, endColIndex));
                }
                break;
            default:
                throw new IllegalArgumentException("type cannot be "+type); //No I18N
        }

        for(Range range: coveredRanges){
            sheet.removeCheckboxRanges(range);
            PicklistUtil.removePicklistRange(range.getSheet(),range.toDataRange());
        }
    }

    public static void movefirstNonEmptyCellIntoFirstCell(WorkbookContainer container, Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) {
        RangeIterator rIter = new RangeIterator(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, true, false, true, false, true);
        ReadOnlyCell nonEmptyROCell = null;
        while(rIter.hasNext()) {
            ReadOnlyCell rCell = rIter.next();
            if(rCell.getCell() != null && !Value.EMPTY_VALUE.equals(rCell.getCell().getValue())) {
                nonEmptyROCell = rCell;
                break;
            }
        }

        if(nonEmptyROCell == null || (nonEmptyROCell.getRowIndex() == startRowIndex && nonEmptyROCell.getColIndex() == startColIndex)) {
            return;
        }
        try {
            ActionUtil.cutPaste(container, sheet.getWorkbook(), sheet.getName(), nonEmptyROCell.getRowIndex(), nonEmptyROCell.getColIndex(), nonEmptyROCell.getRowIndex(), nonEmptyROCell.getColIndex(), sheet.getName(), startRowIndex, startColIndex);
        }
        catch (ActionTimeOutException e) {}
        //CutPaste cutPaste = new CutPaste(new Range(sheet, nonEmptyROCell.getRowIndex(), nonEmptyROCell.getColIndex(), nonEmptyROCell.getRowIndex(), nonEmptyROCell.getColIndex()), sheet.getCell(startRowIndex, startColIndex));
        //cutPaste.paste();
    }

    private static String updateBorder(CellStyle cellStyle, int borderType, String border, Workbook workbook){
        String cellBorder= getBorderFromCellStyle(cellStyle, borderType, workbook);
        cellBorder= cellBorder==null?"none":cellBorder; //No I18N
        if(border==null){
            border= cellBorder;
        }
        else if(!border.equalsIgnoreCase(cellBorder)){
            border="none"; //No I18N
        }
        return border;
    }

    private static void setBorderLookingupMap(HashMap<BorderStyleKey, String> borderStylesMap, int type, Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, int borderType, String border, boolean isResetRepeated){
        for(int rowIndex=startRowIndex; rowIndex<=endRowIndex; rowIndex++){
            Row row=sheet.getRow(rowIndex);
            if(isResetRepeated && (rowIndex+row.getRowsRepeated()-1)>endRowIndex){
                sheet.getRow(endRowIndex+1);
            }
            for(int colIndex=startColIndex; colIndex<=endColIndex; colIndex++){
                Cell cell=sheet.getCell(rowIndex, colIndex);
                if(isResetRepeated && (colIndex+cell.getColsRepeated()-1)>endColIndex){
                    sheet.getCell(rowIndex, endColIndex+1);
                }

                CellStyle inherentCellStyle= ((CellImpl)cell).getCellStyleReadOnly();
                String topBorder= getBorderFromCellStyle(inherentCellStyle, ActionConstants.BORDER_TOP, sheet.getWorkbook());
                String bottomBorder= getBorderFromCellStyle(inherentCellStyle, ActionConstants.BORDER_BOTTOM, sheet.getWorkbook());
                String leftBorder= getBorderFromCellStyle(inherentCellStyle, ActionConstants.BORDER_LEFT, sheet.getWorkbook());
                String rightBorder= getBorderFromCellStyle(inherentCellStyle, ActionConstants.BORDER_RIGHT, sheet.getWorkbook());

                switch(type){
                    case ActionConstants.MERGE_ACROSS:
                        inherentCellStyle= colIndex==startColIndex ? inherentCellStyle : null;
                        break;
                    case ActionConstants.MERGE_DOWN:
                        inherentCellStyle= rowIndex==startRowIndex ? inherentCellStyle : null;
                        break;
                    default:
                        inherentCellStyle= (rowIndex==startRowIndex && colIndex==startColIndex) ? inherentCellStyle : null;
                }

                switch(borderType){
                    case ActionConstants.BORDER_TOP:
                        topBorder=border;
                        break;
                    case ActionConstants.BORDER_BOTTOM:
                        bottomBorder=border;
                        break;
                    case ActionConstants.BORDER_LEFT:
                        leftBorder=border;
                        break;
                    case ActionConstants.BORDER_RIGHT:
                        rightBorder=border;
                        break;
                    default:
                        throw new IllegalArgumentException("borderType cannot be "+borderType); //No I18N
                }
                ((CellImpl)cell).setStyleForMerge(borderStylesMap, inherentCellStyle, topBorder, bottomBorder, leftBorder, rightBorder);

                colIndex+=cell.getColsRepeated()-1;
            }
            rowIndex+=row.getRowsRepeated()-1;
        }
    }

    private static String getBorderFromCellStyle(CellStyle cellStyle, int borderType, Workbook workbook){
        String border=null;
        if(cellStyle!=null){
            border= null;
            if(isBorderNone(border)){
                switch(borderType){
                    case ActionConstants.BORDER_TOP:
                        border=cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERTOP, workbook);
                        break;
                    case ActionConstants.BORDER_BOTTOM:
                        border=cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERBOTTOM, workbook);
                        break;
                    case ActionConstants.BORDER_LEFT:
                        border=cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, workbook);
                        break;
                    case ActionConstants.BORDER_RIGHT:
                        border=cellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERRIGHT, workbook);
                        break;
                    default:
                        throw new IllegalArgumentException("borderType cannot be "+borderType); //No I18N
                }
            }
        }
        return border;
    }

    public static boolean isBorderNone(String border){
        return (border==null || "none".equalsIgnoreCase(border)); //No I18N
    }

    public static void clearFormattingForTable(Table table) {
        DataRange range = table.getAsDataRange();
        Sheet sheet = table.getSheet();

        int startRowIndex = range.getStartRowIndex();
        int startColIndex = range.getStartColIndex();
        int endRowIndex = range.getEndRowIndex();
        int endColIndex = range.getEndColIndex();

        Map<String, CellStyle> styleMap = new HashMap<>();

        for (TableColumn column : table.getTableColumns()) {
            column.setStyleName(null);
        }

        int endRow = Math.min(endRowIndex, Math.max(startRowIndex, sheet.getRowNum() - 1));
        for(int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++)
        {
            Row row = sheet.getRowReadOnly(rowIndex) == null ? createNewRow(sheet, rowIndex, endRowIndex) : sheet.getRowReadOnly(rowIndex);

            // TODOD : if it exceeds 65536
            if(row.getRowsRepeated() > (endRowIndex - rowIndex + 1))
            {
                sheet.getRow(endRowIndex + 1);
            }


            int endCol = Math.min(endColIndex, Math.max(startColIndex, row.getCellNum() - 1));
            for(int colIndex = startColIndex; colIndex <= endCol; colIndex++)
            {
                Cell cell = sheet.getCellReadOnly(rowIndex, colIndex) == null ? RangeUtil.createNewCell(sheet, row.getCells(), rowIndex, colIndex, endColIndex) : sheet.getCellReadOnly(rowIndex, colIndex);
                // TODOD : if it exceeds 256
                if(cell.getColsRepeated() > (endColIndex - colIndex + 1))
                {
                    sheet.getCell(rowIndex, endColIndex + 1);
                }
                clearCellStyleForTable(cell, styleMap);

                colIndex += cell.getColsRepeated() - 1;
            }

            if(endColIndex > endCol)
            {
                Cell cell = sheet.getCell(rowIndex, endCol + 1);
                cell.setColsRepeated(endColIndex - endCol);
                clearCellStyleForTable(cell, styleMap);
            }

            rowIndex += row.getRowsRepeated() - 1;
        }

        RangeUtil.replicateAdjacentBorders(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.BORDER_ALL, BorderProperties.BORDER_NONE);
    }

    private static void clearCellStyleForTable(Cell cell, Map<String, CellStyle> styleMap) {
        CellStyle cellStyle = ((CellImpl)cell).getCellStyleReadOnly();
        if(cellStyle != null && !EngineConstants.DEFAULT_CELLSTYLENAME.equals(cellStyle.getStyleName())) {
            CellStyle newCellStyle = styleMap.get(cellStyle.getStyleName());
            if(newCellStyle == null) {
                newCellStyle = cellStyle.clone();
                newCellStyle.setStyleName("temp" + UUID.randomUUID()); // No I18N

                newCellStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, null);
                newCellStyle.setProperty(TextStyle.Property.COLOR, null);
                newCellStyle.setProperty(TextStyle.Property.FONTSIZE, null);
                newCellStyle.setProperty(TextStyle.Property.FONTNAME, null);
                newCellStyle.setProperty(TextStyle.Property.FONTWEIGHT, null);
                newCellStyle.setProperty(TextStyle.Property.FONTSTYLE, null);
                newCellStyle.setProperty(TextStyle.Property.TEXTLINETHROUGHSTYLE, null);
                newCellStyle.setProperty(TextStyle.Property.TEXTUNDERLINESTYLE, null);
                newCellStyle.setProperty(CellStyle.Property.BORDERBOTTOM, BorderProperties.BORDER_NONE);
                newCellStyle.setProperty(CellStyle.Property.BORDERTOP, BorderProperties.BORDER_NONE);
                newCellStyle.setProperty(CellStyle.Property.BORDERRIGHT, BorderProperties.BORDER_NONE);
                newCellStyle.setProperty(CellStyle.Property.BORDERLEFT, BorderProperties.BORDER_NONE);
                newCellStyle = ((CellImpl) cell).getSheet().getWorkbook().checkAndAddCellStyle(newCellStyle);

                styleMap.put(cellStyle.getStyleName(), newCellStyle);
            }

            ((CellImpl) cell).setStyleName(newCellStyle.getStyleName(), false);
        }
    }

    private static void clear(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, int type, boolean isVisibleRowsOnly)
    {
        sheet.setIsModified(true);

        boolean isClearFormat = (type == ActionConstants.CLEARALL || type == ActionConstants.CLEARSTYLES || type == ActionConstants.CUT_PASTE);
        boolean isWholeRowAction = (startColIndex == 0 && endColIndex == Utility.MAXNUMOFCOLS - 1);
        boolean isWholeColumnAction = (startRowIndex == 0 && endRowIndex == Utility.MAXNUMOFROWS - 1);

        //copy ColumnHeader properties first.
        if(isWholeColumnAction && isClearFormat)
        {
            clearCHStyle(sheet, startColIndex, endColIndex, type);
        }



        int endRow = Math.min(endRowIndex, Math.max(startRowIndex, sheet.getRowNum() - 1));
        for(int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++)
        {
            Row row = sheet.getRowReadOnly(rowIndex) == null ? createNewRow(sheet, rowIndex, endRowIndex) : sheet.getRowReadOnly(rowIndex);
            if(isVisibleRowsOnly && !RowUtil.isVisibleRow(row))
            {
                continue;
            }
            // TODOD : if it exceeds 65536
            if(row.getRowsRepeated() > (endRowIndex - rowIndex + 1))
            {
                sheet.getRow(endRowIndex + 1);
            }
            if(isWholeRowAction && type == ActionConstants.CUT_PASTE)
            {
                //clear RowStyle properties first.
                row.setRowStyle(sheet.getWorkbook().createDefaultRowStyle());
            }

            int endCol = Math.min(endColIndex, Math.max(startColIndex, row.getCellNum() - 1));
            for(int colIndex = startColIndex; colIndex <= endCol; colIndex++)
            {
                Cell cell = sheet.getCellReadOnly(rowIndex, colIndex) == null ? createNewCell(sheet, row.getCells(), rowIndex, colIndex, endColIndex) : sheet.getCellReadOnly(rowIndex, colIndex);
                // TODOD : if it exceeds 256
                if(cell.getColsRepeated() > (endColIndex - colIndex + 1))
                {
                    sheet.getCell(rowIndex, endColIndex + 1);
                }
                switch(type)
                {
                    case ActionConstants.CLEARCONTENTS:
                        cell.clearContent(false, true, true);
                        break;
                    case ActionConstants.CLEARSTYLES:
                        cell.clearStyle(false);
                        break;
                    case ActionConstants.CLEARALL:
                        cell.clearAll(false);
                        break;
                    case ActionConstants.CLEAR_ANNOTATION:
                        cell.clearAnnotation();
                        break;
                    case ActionConstants.CUT_PASTE:
                        ((CellImpl) cell).clearForCutPaste(false);
                        break;
                    case ActionConstants.CLEAR_RICHTEXT:
                        cell.clearRichText(false);
                        break;
                    default:
                        throw new IllegalArgumentException("argument type is invalid.." + type);//No I18N
                }

                colIndex += cell.getColsRepeated() - 1;
            }

            if(endColIndex > endCol)
            {
                Cell cell = sheet.getCell(rowIndex, endCol + 1);
                cell.setColsRepeated(endColIndex - endCol);
                switch(type)
                {
                    case ActionConstants.CLEARCONTENTS:
                        cell.clearContent(false, true, false);
                        break;
                    case ActionConstants.CLEARSTYLES:
                        cell.clearStyle(false);
                        break;
                    case ActionConstants.CLEARALL:
                        cell.clearAll(false);
                        break;
                    case ActionConstants.CLEAR_ANNOTATION:
                        cell.clearAnnotation();
                        break;
                    case ActionConstants.CUT_PASTE:
                        ((CellImpl) cell).clearForCutPaste(false);
                        break;
                    case ActionConstants.CLEAR_RICHTEXT:
                        cell.clearRichText(false);
                        break;
                    default:
                        throw new IllegalArgumentException("argument type is invalid.." + type);//No I18N
                }
            }

            rowIndex += row.getRowsRepeated() - 1;
        }

        if(isClearFormat) {
            replicateAdjacentBorders(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.BORDER_ALL, BorderProperties.BORDER_NONE);
        }

        boolean isCFColLevelRemoval = (type == ActionConstants.CLEARALL || type == ActionConstants.CLEARSTYLES);
        switch(type)
        {
            case ActionConstants.CLEARALL:
            case ActionConstants.CUT_PASTE:
                sheet.removeMergeRanges(new DataRange(sheet.getAssociatedName(), startRowIndex, startColIndex, endRowIndex, endColIndex));
                sheet.removeContentValidationRanges(new Range(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex));
                sheet.removeCheckboxRanges(new Range(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex));
            case ActionConstants.CLEARSTYLES:
                sheet.removeConditionalStyleRanges(new Range(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex), isCFColLevelRemoval, true);
        }
    }

    public static void clearRichText(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        clear(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.CLEAR_RICHTEXT);
    }

    public static void clearContent(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        clear(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.CLEARCONTENTS);
        // Moved to ActionUtil.
        //ActionUtil.clearFilter(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
    }

    public static void clearStyle(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        clear(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.CLEARSTYLES);
    }

    public static void clearAnnotation(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        clear(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.CLEAR_ANNOTATION);
    }

    public static void clearAll(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        clear(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.CLEARALL);
        // Moved to ActionUtil.
        //ActionUtil.clearFilter(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
    }

    private static void clear(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, int type)
    {
        clear(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, type, false);
    }


    public static void clearForCutPaste(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, boolean isVisibleRows)
    {
        clear(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.CUT_PASTE, isVisibleRows);
        // Moved to ActionUtil.
        //ActionUtil.clearFilter(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
    }

    public static void clearForCutPaste(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        clear(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, ActionConstants.CUT_PASTE, false);
        // Moved to ActionUtil.
        //ActionUtil.clearFilter(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
    }

    public static void setRowStyleProperty(Sheet sheet, int startRowIndex, int endRowIndex, List<Style.Property> proNames, List<Object> propValues)
    {
        setRowStyleProperty(sheet, startRowIndex, endRowIndex, proNames, propValues, true);
    }

    public static List<DataRange> setRowStyleProperty(Sheet sheet, int startRowIndex, int endRowIndex, List<Style.Property> proNames, List<Object> propValues, boolean isResetMaxHeightColIndex)
    {
        List<DataRange> rowList = new ArrayList<>();
        sheet.setIsModified(true);
        List<Row> rows = getRows(sheet,startRowIndex,endRowIndex, true);
        Map<String, String> rowStyleNamesMap= new HashMap<>();
        String asn = sheet.getAssociatedName();

        for(Row row : rows)
        {
            String oldStyleName = row.getStyleName();
            String newStyleName=rowStyleNamesMap.get(oldStyleName);

            if(newStyleName==null){
//                RowStyle oldRowStyle= sheet.getWorkbook().getRowStyle(oldStyleName);
                RowStyle oldRowStyle= row.getRowStyleReadOnly();
                RowStyle newRowStyle= oldRowStyle.clone();
                newRowStyle.setProperty(proNames, propValues);
                row.setRowStyle(newRowStyle, false);
                newStyleName = row.getStyleName();
                rowStyleNamesMap.put(oldStyleName, row.getStyleName());
            }
            else{
                row.setStyleNameFromWriter(newStyleName);
            }
            if(!oldStyleName.equals(newStyleName))
            {
                int sR = row.getRowIndex();
                rowList.add(new DataRange(asn, sR, 0, sR + row.getRowsRepeated() - 1 , Utility.MAXNUMOFCOLS - 1));
            }

            if(isResetMaxHeightColIndex)
            {
                row.setMaxHeightColIndex(-1);
            }
        }
        return rowList;
    }


    public static void setRowCellStyleProperty(Sheet sheet, int startRowIndex, int endRowIndex, List<Style.Property> propNames, List<Object> propValues)
    {
        sheet.setIsModified(true);
        RangeUtil.setCellStyleProperty(sheet, startRowIndex, 0, endRowIndex, Utility.MAXNUMOFCOLS - 1, propNames, propValues);
    }

    public static void setColumnStyleProperty(Sheet sheet, int startColIndex, int endColIndex, List<Style.Property> proNames, List<Object> propValues)
    {
        setColumnStyleProperty(sheet, startColIndex, endColIndex, proNames, propValues, true);
    }

    public static void setColumnStyleProperty(Sheet sheet, int startColIndex, int endColIndex, List<Style.Property> proNames, List<Object> propValues, boolean isResetMaxWidthRowIndex)
    {
        sheet.setIsModified(true);
        List<ColumnHeader> colHeaders = getColumnHeaders(sheet, startColIndex, endColIndex);
        Map<String, String> colStyleNamesMap= new HashMap<>();

        for(ColumnHeader colHeader : colHeaders)
        {
            String oldStyleName= colHeader.getStyleName();
            String newStyleName= colStyleNamesMap.get(oldStyleName);

            if(newStyleName==null){
//                ColumnStyle oldColStyle= sheet.getWorkbook().getColumnStyle(oldStyleName);
                ColumnStyle oldColStyle= colHeader.getColumnStyleReadOnly();
                ColumnStyle newColStyle = oldColStyle.clone();
                newColStyle.setProperty(proNames, propValues);
                colHeader.setColumnStyle(newColStyle, false);
                colStyleNamesMap.put(oldStyleName, newStyleName);
            }
            else{
                colHeader.setStyleName(newStyleName);
            }

            if(isResetMaxWidthRowIndex)
            {
                colHeader.setMaxWidthRowIndex(-1);
            }
        }
    }


    private static void clearCHStyle(Sheet sheet, int startColIndex, int endColIndex, int type)
    {
        sheet.setIsModified(true);

        // For ColumnHeader
        List<ColumnHeader> colHeaders = getColumnHeaders(sheet, startColIndex, endColIndex);
        for(ColumnHeader colHeader : colHeaders)
        {
            if(type == ActionConstants.CUT_PASTE)
            {
                colHeader.setColumnStyle(sheet.getWorkbook().createDefaultColumnStyle(), false);
            }
            colHeader.setCellStyle(null, false);
        }
    }

    private static void clearCHStyleForMerge(Sheet sheet, int startColIndex, int endColIndex, String leftBorder, String rightBorder){
        sheet.setIsModified(true);

        String colLeftBorder=null;
        if(!isBorderNone(leftBorder)){
            ColumnHeader leftColumnHeader= sheet.getReadOnlyColumnHeader(startColIndex).getColumnHeader();
            CellStyle leftColCellStyle= leftColumnHeader!=null?leftColumnHeader.getCellStyle():null;
            colLeftBorder= null;
            colLeftBorder= isBorderNone(colLeftBorder)?(leftColCellStyle==null?null:leftColCellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, sheet.getWorkbook())):colLeftBorder;
        }
        String colRightBorder=null;
        if(!isBorderNone(rightBorder)){
            ColumnHeader rightColumnHeader= sheet.getReadOnlyColumnHeader(endColIndex).getColumnHeader();
            CellStyle rightColCellStyle= rightColumnHeader!=null?rightColumnHeader.getCellStyle():null;
            colRightBorder= null;
            colRightBorder= isBorderNone(rightBorder)?(rightColCellStyle==null?null:rightColCellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERRIGHT, sheet.getWorkbook())):colRightBorder;
        }

        clearCHStyle(sheet, startColIndex, endColIndex, ActionConstants.CLEARALL);

        if(!isBorderNone(leftBorder) && leftBorder.equalsIgnoreCase(colLeftBorder)){
            CellStyle leftColCellStyle= sheet.getColumnHeader(startColIndex).getCellStyle();
            leftColCellStyle= (leftColCellStyle==null) ? new CellStyle() : leftColCellStyle;
            leftColCellStyle.setProperty(CellStyle.Property.BORDERLEFT, BorderProperties.getInstance(leftBorder));
            sheet.getColumnHeader(startColIndex).setCellStyle(leftColCellStyle, true);
        }
        if(!isBorderNone(rightBorder) && rightBorder.equalsIgnoreCase(colRightBorder)){
            CellStyle rightColCellStyle= sheet.getColumnHeader(endColIndex).getCellStyle();
            rightColCellStyle= (rightColCellStyle==null) ? new CellStyle() : rightColCellStyle;
            rightColCellStyle.setProperty(CellStyle.Property.BORDERRIGHT, BorderProperties.getInstance(rightBorder));
            sheet.getColumnHeader(endColIndex).setCellStyle(rightColCellStyle, true);
        }
    }

    public static void setCellStyle(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, CellStyle cellStyle) throws IllegalArgumentException
    {
//        if(startRowIndex == 0 && endRowIndex == Utility.MAXNUMOFROWS - 1)
//        {
//            setCHCellStyleProperty(sheet, startColIndex, endColIndex, styleClasses, propNames, propValues);
//            return;
//        }

        //We have to get visible ranges here and iterate the range
        List<DataRange> visibleRanges = getVisibleRowsRange(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
        if(!visibleRanges.isEmpty())
        {
            cellStyle = sheet.getWorkbook().checkAndAddCellStyle(cellStyle);

            for(DataRange dataRange : visibleRanges)
            {
                startRowIndex = dataRange.getStartRowIndex();
                endRowIndex = dataRange.getEndRowIndex();
                sheet.setIsModified(true);

                int endRow = Math.min(endRowIndex, Math.max(startRowIndex, sheet.getRowNum() - 1));
                for(int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++)
                {
                    Row row = sheet.getRowReadOnly(rowIndex);

                    if(row == null)
                    {
                        Row prevRow = null;
                        for(int i = (rowIndex - 1); i >= 0; i--)
                        {
                            prevRow = sheet.getRowReadOnly(i);
                            if(prevRow != null)
                            {
                                break;
                            }
                        }

                        if(prevRow != null && prevRow.getRowsRepeated() > (rowIndex - prevRow.getRowIndex()))
                        {
                            row = sheet.getRow(rowIndex);
                        }
                        else
                        {
                            // so the row is null
                            int rowsRepeated = GeneralUtil.getConsecutiveNullCount(sheet.getRows(), rowIndex, endRowIndex);

                            row = sheet.getRow(rowIndex);
                            row.setRowsRepeated(rowsRepeated);
                        }

                    }

                    // TODOD : if it exceeds 65536
                    if(row.getRowsRepeated() > (endRowIndex - rowIndex + 1))
                    {
                        sheet.getRow(endRowIndex + 1);
                    }

                    int endCol = Math.min(endColIndex, Math.max(startColIndex, row.getCellNum() - 1));
                    for(int colIndex = startColIndex; colIndex <= endCol; colIndex++)
                    {
                        Cell cell = sheet.getCellReadOnly(rowIndex, colIndex);

                        if(cell == null)
                        {
                            Cell prevCell = null;
                            for(int i = (colIndex - 1); i >= 0; i--)
                            {
                                prevCell = sheet.getCellReadOnly(rowIndex, i);
                                if(prevCell != null)
                                {
                                    break;
                                }
                            }

                            if(prevCell != null && prevCell.getColsRepeated() > (colIndex - prevCell.getColumnIndex()))
                            {
                                cell = sheet.getCell(rowIndex, colIndex);
                            }
                            else
                            {
                                // so the row is null
                                int colsRepeated = GeneralUtil.getConsecutiveNullCount(row.getCells(), colIndex, endColIndex);

                                cell = sheet.getCell(rowIndex, colIndex);
                                cell.setColsRepeated(colsRepeated);
                            }

                        }
                        // TODOD : if it exceeds 256
                        if(cell.getColsRepeated() > (endColIndex - colIndex + 1))
                        {
                            sheet.getCell(rowIndex, endColIndex + 1);
                        }

                        if(cell.getStyleName() == null)
                        {
                            ColumnHeader ch = sheet.getColumnHeader(cell.getColumnIndex());
                            sheet.getCell(rowIndex, cell.getColumnIndex() + Math.min(cell.getColsRepeated(), ch.getColsRepeated()));
                        }

                        cell.setStyleName(cellStyle.getStyleName());

                        colIndex += cell.getColsRepeated() - 1;
                    }

                    if(endColIndex > endCol)
                    {
                        Cell cell = sheet.getCell(rowIndex, endCol + 1);
                        cell.setStyleName(cellStyle.getStyleName());

                        cell.setColsRepeated(endColIndex - endCol);
                    }

                    rowIndex += row.getRowsRepeated() - 1;
                }

                if(endRowIndex > endRow)
                {
                    //Row row = sheet.getRow(endRow + 1);
                    Cell cell = sheet.getCell(endRow + 1, startColIndex);
                    cell.setStyleName(cellStyle.getStyleName());

                    cell.setColsRepeated(endColIndex - startColIndex + 1);
                    Row row = cell.getRow();
                    row.setRowsRepeated(endRowIndex - endRow);
                }
            }
        }
    }

    public static void setCHPattern(Sheet sheet, int startColIndex, int endColIndex, ZSPattern inPattern)
    {
        setCHCellStyleProperty(sheet, startColIndex, endColIndex, CellStyle.Property.PATTERN, inPattern);
    }

    public static void setCHCellStyleProperty(Sheet sheet, int startColIndex, int endColIndex, Style.Property propName, Object propValue)
    {

        List<Style.Property> propNames = new ArrayList<>();
        propNames.add(propName);

        List<Object> propValues = new ArrayList<>();
        propValues.add(propValue);

        setCHCellStyleProperty(sheet, startColIndex, endColIndex, propNames, propValues);
    }

    public static void setCHCellStyleProperty(Sheet sheet, int startColIndex, int endColIndex, List<Style.Property> propNames, List<Object> propValues)
    {
        boolean isForSetPattern = propNames.contains(CellStyle.Property.PATTERN);
        sheet.setIsModified(true);

        sheet.getTables(false).values().stream()
            .forEach(table -> table.notifyStyleChange(sheet.getWorkbook(), new DataRange(sheet.getAssociatedName(), 0, startColIndex, Utility.MAXNUMOFROWS-1, endColIndex), propNames, propValues));


        Map<String, String> changes = new HashMap<>();
        Map<Integer, CellStyle> oldCHCellStyleList = new HashMap<>();
        // For ColumnHeader
        List<ColumnHeader> colHeaders = getColumnHeaders(sheet, startColIndex, endColIndex);
        for(ColumnHeader colHeader : colHeaders)
        {
            CellStyle existingCellStyleReadOnly = colHeader.getCellStyleReadOnly();
            for(int i = 0; i < colHeader.getColsRepeated(); i++)
            {
                oldCHCellStyleList.put(colHeader.getColumn().getColumnIndex() + i, existingCellStyleReadOnly);
            }
            setNewCellStyle(colHeader, changes, propNames, propValues, true);
        }
        //////////////////////////////////////////////////

        // For Cells
        // set the props to all cellls of the column
        int rowNum = sheet.getRowNum();
        int colNum = sheet.getColNum();
        for(int rowIndex = 0; rowIndex < rowNum; rowIndex++)
        {
            Row row = sheet.getRowReadOnly(rowIndex);
            if(row != null)
            {
                // For loop
                int endCol = Math.min(endColIndex, Math.max(startColIndex, colNum - 1));
                for(int colIndex = startColIndex; colIndex <= endCol; colIndex++)
                {
                    Cell cell = sheet.getCellReadOnly(rowIndex, colIndex);

                    if(cell == null)
                    {
                        Cell prevCell = null;
                        for(int i = (colIndex - 1); i >= 0; i--)
                        {
                            prevCell = sheet.getCellReadOnly(rowIndex, i);
                            if(prevCell != null)
                            {
                                break;
                            }
                        }

                        if(prevCell != null && prevCell.getColsRepeated() > (colIndex - prevCell.getColumnIndex()))
                        {
                            cell = sheet.getCell(rowIndex, colIndex);
                        }
                        else
                        {
                            // so the row is null
                            int colsRepeated = GeneralUtil.getConsecutiveNullCount(row.getCells(), colIndex, endColIndex);

                            cell = sheet.getCell(rowIndex, colIndex);
                            cell.setColsRepeated(colsRepeated);
                        }

                    }
                    // TODOD : if it exceeds 256
                    if(cell.getColsRepeated() > (endColIndex - colIndex + 1))
                    {
                        sheet.getCell(rowIndex, endColIndex + 1);
                    }

                    if(cell.getStyleName() != null)
                    {
                        if(RowUtil.isVisibleRow(row))
                        {
                            setNewCellStyle(cell, changes, propNames, propValues, cell.getStyleName().equals("Default"));
                        }
                    }
                    // Should not set the properties on hidden rows. Hence making changes to make sure that old styles remain in hidden rows.
                    else if(!RowUtil.isVisibleRow(row))
                    {
                        CellStyle cStyle = oldCHCellStyleList.get(colIndex);
                        if(cStyle == null)
                        {
                            ((CellImpl) cell).setStyleName("Default", false, isForSetPattern);	// No I18N
                        }
                        else
                        {
                            ((CellImpl) cell).setStyleName(cStyle.getStyleName(), false, isForSetPattern);
                        }
                    }
                    else
                    {
                        ((CellImpl) cell).setStyleName(null, false, isForSetPattern); // setting dummy null name for updating height and width of a cell
                    }

                    // jump cell repeated
                    colIndex += cell.getColsRepeated() - 1;
                }

                // jump the rows
                rowIndex += row.getRowsRepeated() - 1;
            }
        }

        int maxNumOfRows = Utility.MAXNUMOFROWS;
        if(rowNum < maxNumOfRows)
        {
            /*
            int rowsRepeated = maxNumOfRows-rowNum;

            Row row = sheet.getRows().get(rowNum - 1);
            if(row == null || row.isEmpty()) // Set rows repeated to this row itself
            {
            row = sheet.getRow(rowNum - 1);
            rowsRepeated += 1;
            }
            else // must create a new row and set rowsRepeated.
            {
            row = sheet.getCell(rowNum, 0).getRow();// Creating next row with single cell.

            }
            row.setRowsRepeated(rowsRepeated);
             *
             */
            int rowsRepeated = maxNumOfRows - rowNum;
//            Row row = sheet.getRow(rowNum - 1);
            Row row = sheet.getRow(rowNum);
            row.setRowsRepeated(rowsRepeated);
        }
    }

    public static String getRangeAutoSumString(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, Locale locale)
    {
        Object sum = null;
        try
        {
            sum = RangeFunctions.getRangeSum(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, true);	//true -> read only visible rows.
        }catch(EvaluationException e)
        {
            sum = e;
        }

        ZSPattern pattern = null;
        Cell firstRCell = sheet.getReadOnlyCellFromShell(startRowIndex, startColIndex).getCell();
        if(firstRCell != null)
        {
            pattern = ((CellImpl)firstRCell).getPattern(2);
        }

        if(pattern == null)
        {
            pattern = DefaultPatternUtil.getDefaultPattern(sheet.getWorkbook(),Type.FLOAT);
        }

        return pattern.formatValue(sheet.getWorkbook(), Value.getInstance(Type.FLOAT, sum)).getContent();
    }


    public static List<LinearIntegralRange> getRepeatedRowRanges(Sheet sheet, int startRowIndex, int endRowIndex) {
        if(!CellUtil.isRowRangeBound(startRowIndex) || !CellUtil.isRowRangeBound(endRowIndex) || startRowIndex > endRowIndex) {
            throw new IllegalArgumentException();
        }

        List<LinearIntegralRange> lirs = new LinkedList<>();
        int currStartRowIndex = startRowIndex;
        while(currStartRowIndex <= endRowIndex) {
            ReadOnlyRow readOnlyRow = RangeIterator.getReadOnlyRowAtSheet(sheet, currStartRowIndex);
            int currEndRowIndex = Math.min(endRowIndex, readOnlyRow.getRowIndex() + readOnlyRow.getRowsRepeated() - 1);
            lirs.add(new LinearIntegralRange(currStartRowIndex, currEndRowIndex));
            currStartRowIndex = currEndRowIndex + 1;
        }

        return lirs;
    }


    public static List<Cell> getCellsHavingAnnotation(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        List<ReadOnlyCell> rCellList = Range.getReadOnlyRange(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, false);
        List<Cell> annotationCellList = new ArrayList<>();
        int size = rCellList.size();

        for(int i=0; i<size; i++)
        {
            ReadOnlyCell rCell = rCellList.get(i);
            Cell cell = rCell.getCell();
            if(cell != null && cell.getAnnotation() != null)
            {
                annotationCellList.add(rCell.getCell());
            }
        }

        return annotationCellList;
    }

    public static List<Cell> getCellsHavingLink(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        List<ReadOnlyCell> rCellList = Range.getReadOnlyRange(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, false);
        List<Cell> linkCellList = new ArrayList<>();
        int size = rCellList.size();

        for(int i=0; i<size; i++)
        {
            ReadOnlyCell rCell = rCellList.get(i);
            Cell cell = rCell.getCell();
            if(cell != null && cell.getLink() != null)
            {
                linkCellList.add(cell);
            }
        }

        return linkCellList;
    }

    public static Boolean hasFormula(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        Boolean hasFormula = null;

        for(int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++)
        {
            ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(rowIndex);
            Row row = rRow.getRow();
            int rowsRepeated = rRow.getRowsRepeated();
            int rRowIndex = rRow.getRowIndex();
            for(int colIndex = startColIndex; colIndex <= endColIndex; colIndex++)
            {
                boolean isFormula = false;
                ReadOnlyCell rCell = null;
                if(row != null)
                {
                    rCell = sheet.getReadOnlyCellFromShell(rRowIndex, colIndex);
                    Cell cell = rCell.getCell();
                    if(cell != null)
                    {
                        isFormula = cell.isFormula();
                    }
                }

                if(rowIndex == startRowIndex && colIndex == startColIndex)
                {
                    hasFormula = isFormula;
                }
                else if(hasFormula != isFormula)
                {
                    return null;
                }

                // rCell will be null only if row is null.
                if(rCell == null)
                {
                    break;
                }
                else
                {
                    colIndex += rCell.getColsRepeated() - 1;
                }
            }

            rowIndex += rowsRepeated - 1;
        }

        return hasFormula;
    }

    public static boolean containsFormula(Sheet sheet, DataRange dataRange)
    {
        RangeIterator rangeIter = new RangeIterator(dataRange.toRange(sheet.getWorkbook()), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, true, true);
        while (rangeIter.hasNext())
        {
            ReadOnlyCell readOnlyCell = rangeIter.next();
            Cell cell = readOnlyCell.getCell();
            if(cell != null && cell.isFormula())
            {
                return true;
            }
        }
        return false;
    }

    public static boolean containsOtherThanValue(Sheet sheet, DataRange dataRange)
    {
        RangeIterator rangeIter = new RangeIterator(dataRange.toRange(sheet.getWorkbook()), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, true, true);
        while (rangeIter.hasNext())
        {
            ReadOnlyCell readOnlyCell = rangeIter.next();
            Cell cell = readOnlyCell.getCell();
            if(cell != null && (cell.isFormula() || cell.isImage() || CellUtil.isCheckboxPresent(sheet, cell.getRowIndex(), cell.getColumnIndex())))
            {
                return true;
            }
        }
        return false;
    }

    public static boolean containsPicklistValue(Sheet sheet, DataRange dataRange)
    {
        RangeIterator rangeIter = new RangeIterator(dataRange.toRange(sheet.getWorkbook()), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, true,true);
        while (rangeIter.hasNext())
        {
            ReadOnlyCell readOnlyCell = rangeIter.next();
            Cell cell = readOnlyCell.getCell();
            if(cell != null && cell.getValue() instanceof PicklistValue)
            {
                return true;
            }
        }
        return false;
    }

    public static List<DataRange> splitRanges(Collection<DataRange> dataRanges, DataRange inDataRange)
    {
        List<DataRange> newDRangeList = new ArrayList<>();
        for (DataRange dataRange : dataRanges)
        {
            if(RangeUtil.intersection(inDataRange, dataRange)  != null)
            {
                int startRowIndex = dataRange.getStartRowIndex();
                int startColIndex = dataRange.getStartColIndex();
                int endRowIndex = dataRange.getEndRowIndex();
                int endColIndex = dataRange.getEndColIndex();
                for(int j = 0; j < 4; j++)
                {
                    if(j == 0)
                    {
                        if(startRowIndex < inDataRange.getStartRowIndex())
                        {
                            DataRange newDRange = new DataRange(dataRange.getAssociatedSheetName(),
                                startRowIndex,
                                startColIndex,
                                inDataRange.getStartRowIndex() - 1,
                                endColIndex);
                            newDRangeList.add(newDRange);
                            startRowIndex = inDataRange.getStartRowIndex();
                        }
                    }
                    else if(j == 1)
                    {
                        if(startColIndex < inDataRange.getStartColIndex())
                        {
                            DataRange newDRange = new DataRange(dataRange.getAssociatedSheetName(),
                                startRowIndex,
                                startColIndex,
                                endRowIndex,
                                inDataRange.getStartColIndex() - 1);
                            newDRangeList.add(newDRange);
                            startColIndex = inDataRange.getStartColIndex();
                        }
                    }
                    else if(j == 2)
                    {
                        if(endRowIndex > inDataRange.getEndRowIndex())
                        {
                            DataRange newDRange = new DataRange(dataRange.getAssociatedSheetName(),
                                inDataRange.getEndRowIndex() + 1,
                                startColIndex,
                                endRowIndex,
                                endColIndex);
                            newDRangeList.add(newDRange);
                            endRowIndex = inDataRange.getEndRowIndex();
                        }
                    }
                    else
                    {
                        if(endColIndex > inDataRange.getEndColIndex())
                        {
                            DataRange newDRange = new DataRange(dataRange.getAssociatedSheetName(),
                                startRowIndex,
                                inDataRange.getEndColIndex() + 1,
                                endRowIndex,
                                endColIndex);
                            newDRangeList.add(newDRange);
                        }
                    }
                }
            }
            else
            {
                newDRangeList.add(dataRange);
            }
        }

        return newDRangeList;
    }

    public static List<DataRange> breakRanges(Collection<DataRange> dataRanges, DataRange inDataRange) {
        List<DataRange> rangesInsideInDataRange = new ArrayList<>();
        for(DataRange dataRange: dataRanges) {
            DataRange intersection = RangeUtil.intersection(dataRange, inDataRange);
            if(intersection != null) {
                rangesInsideInDataRange.add(intersection);
            }
        }
        List<DataRange> rangesOutsideInDataRange = RangeUtil.splitRanges(dataRanges, inDataRange);

        List<DataRange> brokenRanges = new ArrayList<>(rangesInsideInDataRange);
        brokenRanges.addAll(rangesOutsideInDataRange);

        return brokenRanges;
    }

    /*
     * Will return null if there is no intersection.
     */
    public static DataRange intersection(DataRange dRange_1, DataRange dRange_2)
    {
        if(dRange_1.getAssociatedSheetName().equals(dRange_2.getAssociatedSheetName()))
        {
            int startRowIndex = Math.max(dRange_1.getStartRowIndex(), dRange_2.getStartRowIndex());
            int endRowIndex = Math.min(dRange_1.getEndRowIndex(), dRange_2.getEndRowIndex());
            int startColIndex = Math.max(dRange_1.getStartColIndex(), dRange_2.getStartColIndex());
            int endColIndex = Math.min(dRange_1.getEndColIndex(), dRange_2.getEndColIndex());

            if(startRowIndex <= endRowIndex && startColIndex <= endColIndex)
            {
                return new DataRange(dRange_1.getAssociatedSheetName(), startRowIndex, startColIndex, endRowIndex, endColIndex);
            }
        }

        return null;
    }

    public static boolean hasIntersection(DataRange testDataRange, List<DataRange> dataRangeFamily){
        for(DataRange familyMember : dataRangeFamily){
            if(intersection(familyMember, testDataRange) != null){
                return true;
            }
        }
        return false;
    }


    public static SheetRange intersection(SheetRange range0, SheetRange range1) {
        int newStartRowIndex = Math.max(range0.getStartRowIndex(), range1.getStartRowIndex());
        int newStartColIndex = Math.max(range0.getStartColIndex(), range1.getStartColIndex());
        int newEndRowIndex = Math.min(range0.getEndRowIndex(), range1.getEndRowIndex());
        int newEndColIndex = Math.min(range0.getEndColIndex(), range1.getEndColIndex());

        if(newEndRowIndex >= newStartRowIndex && newEndColIndex >= newStartColIndex) {
            return new SheetRange(
                newStartRowIndex,
                newStartColIndex,
                newEndRowIndex,
                newEndColIndex);
        }

        return null;
    }

    public static boolean isPartOfRange(DataRange testRange , DataRange inRange)
    {

        int startRow = inRange.getStartRowIndex();
        int startCol = inRange.getStartColIndex();
        int endRow = inRange.getEndRowIndex();
        int endCol = inRange.getEndColIndex();

        int testStartRow = testRange.getStartRowIndex();
        int testStartCol = testRange.getStartColIndex();
        int testEndRow = testRange.getEndRowIndex();
        int testEndCol = testRange.getEndColIndex();

        if((testStartRow >= startRow && testStartRow <= endRow) ||
            (testEndRow >= startRow && testEndRow <= endRow))
        {
            if((testStartCol >= startCol && testStartCol <= endCol) ||
                (testEndCol >= startCol && testEndCol <= endCol) ||
                (startCol > testStartCol && endCol < testEndCol))
            {
                return !(testStartCol <= startCol && testEndCol >= endCol && testStartRow <= startRow && testEndRow >= endRow);
            }
        }
        else if((testStartCol >= startCol && testStartCol <= endCol) ||
            (testEndCol >= startCol && testEndCol <= endCol))
        {
            if((testStartRow >= startRow && testStartRow <= endRow) ||
                (testEndRow >= startRow && testEndRow <= endRow) ||
                (startRow > testStartRow && endRow < testEndRow))
            {
                return !(testStartCol <= startCol && testEndCol >= endCol && testStartRow <= startRow && testEndRow >= endRow);
            }
        }
        return false;

    }

    public static boolean isContainsRange(DataRange testRange , DataRange inRange)
    {
        return (inRange.getStartRowIndex()<= testRange.getStartRowIndex() &&
            inRange.getStartColIndex()<= testRange.getStartColIndex()&&
            inRange.getEndRowIndex()>= testRange.getEndRowIndex()&&
            inRange.getEndColIndex()>= testRange.getEndColIndex());
    }

    public static boolean hasPartialIntersection(SheetRange range0, SheetRange range1) {
        SheetRange intersection = RangeUtil.intersection(range0, range1);
        if(intersection == null) {
            return false;
        }
        if(intersection.equals(range0) || intersection.equals(range1)) {
            return false;
        }
        return true;
    }

    private static String[][] getFormulaOrValueArray(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, boolean isGetFormula) throws IllegalArgumentException
    {
        int rowSize = (endRowIndex - startRowIndex) + 1;
        int colSize = (endColIndex - startColIndex) + 1;
        if(rowSize > Utility.MAXNUMOFROWS || colSize > Utility.MAXNUMOFCOLS)
        {
            throw new IllegalArgumentException("Exceeds sheet row/col limit");// No I18N
        }
        String values[][] = new String[rowSize][colSize];

        int endRow = Math.max(startRowIndex, Math.min(endRowIndex, sheet.getUsedRowIndex()));
        int endCol = Math.max(startColIndex, Math.min(endColIndex, sheet.getUsedColumnIndex()));
        for(int row = startRowIndex; row <= endRowIndex; row++)
        {
            for(int col = startColIndex; col <= endColIndex; col++)
            {
                String value = "";
                if(row <= endRow && col <= endCol)
                {
                    Cell cell = sheet.getReadOnlyCellFromShell(row, col).getCell();

                    if(cell != null)
                    {
                        value = isGetFormula ? cell.getLocalizedFormula() : cell.getLocalizedFormula();
                    }
                }

                values[row - startRowIndex][col - startColIndex] = value;
            }
        }

        return values;
    }

    public static String[][] getValueArray(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) throws IllegalArgumentException
    {
        return getFormulaOrValueArray(sheet, startRowIndex,
            startColIndex,
            endRowIndex,
            endColIndex, false);
    }

    public static String[][] getFormulaArray(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) throws IllegalArgumentException
    {
        return getFormulaOrValueArray(sheet, startRowIndex,
            startColIndex,
            endRowIndex,
            endColIndex, true);
    }

    public static boolean hasIntersection(List<SheetRange> ranges) {
        for(int i = 0; i < ranges.size(); i++) {
            SheetRange range0 = ranges.get(i);
            for(int j = i+1; j < ranges.size(); j++) {
                SheetRange range1 = ranges.get(j);
                SheetRange rangeIntersection = RangeUtil.intersection(range0, range1);
                if(rangeIntersection != null) {
                    return true;
                }
            }
        }
        return false;
    }

    public static String getCellStyleProperty(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, Style.Property propName) throws IllegalArgumentException
    {
        int rowNum = sheet.getRowNum();
        int colNum = sheet.getColNum();

        String propValue = null;
        for (int col = startColIndex; col <= endColIndex; col++)
        {
            int colsRepeated = 1;
            for (int row = startRowIndex; row <= endRowIndex; row++)
            {
                if ((row >= rowNum || col >= colNum))
                {
                    if (propValue == null || "none".equals(propValue))
                    {
                        break;
                    }
                    else
                    {
                        return null;
                    }
                }

                String newPropValue = null;
                ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(row, col);
                Cell cell = rCell.getCell();
                if (cell != null)
                {
                    if(colsRepeated == 1 || cell.getColsRepeated() < colsRepeated)
                    {
                        colsRepeated = cell.getColsRepeated();
                    }
                    CellStyle unClonedCStyle = ((CellImpl) cell).getCellStyleReadOnly();
                    if (unClonedCStyle != null)
                    {
                        Object o = unClonedCStyle.getProperty(propName);
                        if(o != null)
                        {
                            newPropValue = Style.Property.getPropertyString(o,sheet.getWorkbook().getTheme());
                        }
                    }
                }

                if (row == startRowIndex && col == startColIndex)
                {
                    propValue = newPropValue;
                }
                else if (newPropValue != null ? !newPropValue.equals(propValue) : !(propValue == null || "none".equals(propValue)))
                {
                    return null;
                }

                row += (rCell.getRowsRepeated() - 1);
            }

            if(propValue == null)
            {
                ReadOnlyColumnHeader rCH = sheet.getReadOnlyColumnHeader(col);
                ColumnHeader cH = rCH.getColumnHeader();
                if(cH != null)
                {
                    CellStyle unClonedCStyle = cH.getCellStyleReadOnly();
                    if(unClonedCStyle != null)
                    {
                        Object o = unClonedCStyle.getProperty(propName);
                        if(o != null)
                        {
                            propValue = Style.Property.getPropertyString(o,sheet.getWorkbook().getTheme());
                        }
                    }
                }
                if (rCH.getColsRepeated() < colsRepeated)
                {
                    colsRepeated = rCH.getColsRepeated();
                }
            }

            col += (colsRepeated - 1);
        }

        if (propValue == null)
        {
            CellStyle defCellStyle = sheet.getWorkbook().getCellStyle("Default");
            Object o = defCellStyle.getProperty(propName);
            if(o == null)
            {
                o = "";
            }
            propValue = Style.Property.getPropertyString(o,sheet.getWorkbook().getTheme());
        }

        //Properties property = new Properties();
        //property.setProperty(propName, propValue);
        //return property;
        return propValue;
    }

    public static ZSPattern getRangePattern(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) throws IllegalArgumentException
    {
        int rowNum = sheet.getRowNum();
        int colNum = sheet.getColNum();

        ZSPattern pattern = null;
        for (int row = startRowIndex; row <= endRowIndex; row++)
        {
            int rowsRepeated = sheet.getReadOnlyRowFromShell(row).getRowsRepeated();
            for (int col = startColIndex; col <= endColIndex; col++)
            {
                if ((row >= rowNum || col >= colNum))
                {
                    if (pattern == null || pattern.equals(DataStyleConstants.EMPTY_PATTERN))
                    {
                        break;
                    }
                    else
                    {
                        return null;
                    }
                }

                ZSPattern currPattern = null;
                ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(row, col);
                Cell cell = rCell.getCell();
                if (cell != null)
                {
                    currPattern = ((CellImpl)cell).getPattern(1);
                    if(currPattern == null)
                    {
                        ColumnHeader colHeader = sheet.getReadOnlyColumnHeader(col).getColumnHeader();
                        if(colHeader != null)
                        {
                            currPattern = colHeader.getPattern();
                        }
                    }
                    if(currPattern == null && cell.getType() != Type.FLOAT && cell.getType() != Type.STRING)
                    {
                        currPattern = DefaultPatternUtil.getDefaultPattern(sheet.getWorkbook(),cell.getType());
                    }
                }

                if (row == startRowIndex && col == startColIndex)
                {
                    pattern = currPattern;
                }
                else if (currPattern != null ? !currPattern.equals(pattern) : !(pattern == null || pattern.equals(DataStyleConstants.EMPTY_PATTERN)))
                {
                    return null;
                }

                col += rCell.getColsRepeated() - 1;
            }

            row += rowsRepeated - 1;
        }

        if(pattern == null)
        {
            return DataStyleConstants.EMPTY_PATTERN;
        }

        return pattern;
    }


    public static Map<Style.Property, Object> getColumnStyleProperty(Sheet sheet, int startColIndex, int endColIndex, Style.Property propName) throws IllegalArgumentException
    {
        String propValue = null;

        int endCol = Math.max(startColIndex, Math.min(endColIndex, sheet.getColNum()));
        for(int col = startColIndex; col <= endColIndex; col++)
        {
            if(col <= endCol)
            {
                ReadOnlyColumnHeader rColHeader = sheet.getReadOnlyColumnHeader(col);
                ColumnHeader colHeader = rColHeader.getColumnHeader();
                if(colHeader != null)
                {
                    ColumnStyle cStyle = colHeader.getColumnStyleReadOnly();
                    if(cStyle != null)
                    {
                        String newPropValue = Style.Property.getPropertyString(cStyle.getProperty(propName), sheet.getWorkbook().getTheme());
                        if(newPropValue != null)
                        {
                            if(col == startColIndex)
                            {
                                propValue = newPropValue;
                            }
                            //else if(propValue == null || !newPropValue.equals(propValue))
                            else if(!newPropValue.equals(propValue))
                            {
                                return null;
                            }
                        }
                        else if(propValue != null)
                        {
                            return null;
                        }
                    }
                }

                col += rColHeader.getColsRepeated() - 1;
            }
        }

        if(propValue == null)
        {
            propValue = Style.Property.getPropertyString(sheet.getWorkbook().createDefaultColumnStyle().getProperty(propName), sheet.getWorkbook().getTheme());
        }

        Map<Style.Property, Object> property = new HashMap();
        property.put(propName, propValue);
        return property;
        //return propValue != null ? propValue.toString() : null;
    }

    public static Map<Style.Property, Object> getRowStyleProperty(Sheet sheet, int startRowIndex, int endRowIndex, Style.Property propName) throws IllegalArgumentException
    {
        String propValue = null;

        int endRow = Math.max(startRowIndex, Math.min(endRowIndex, sheet.getRowNum()));
        for(int row = startRowIndex; row <= endRowIndex; row++)
        {
            if(row <= endRow)
            {
                ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(row);
                Row tempRow = rRow.getRow();
                int rowsRepeated = rRow.getRowsRepeated();
                if(tempRow != null)
                {
                    RowStyle rStyle = tempRow.getRowStyleReadOnly();
                    if(rStyle != null)
                    {
                        String newPropValue = Style.Property.getPropertyString(rStyle.getProperty(propName), sheet.getWorkbook().getTheme());
                        if(newPropValue != null)
                        {
                            if(row == startRowIndex)
                            {
                                propValue = newPropValue;
                            }
                            //else if(propValue == null || !newPropValue.equals(propValue))
                            else if(!newPropValue.equals(propValue))
                            {
                                return null;
                            }
                        }
                        else if(propValue != null)
                        {
                            return null;
                        }
                    }
                }

                row += rowsRepeated - 1;
            }
        }

        if(propValue == null)
        {
            propValue = Style.Property.getPropertyString(sheet.getWorkbook().createDefaultRowStyle().getProperty(propName), sheet.getWorkbook().getTheme());
        }

        Map<Style.Property, Object> property = new HashMap<>();
        property.put(propName, propValue);
        return property;
        //return propValue != null ? propValue.toString() : null;
    }

    public static Boolean isInMergedArea(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        List<DataRange> coveredArea1 = sheet.getCoveredArea(startRowIndex, startColIndex, startRowIndex, startColIndex);
        List<DataRange> coveredArea2 = sheet.getCoveredArea(endRowIndex, endColIndex, endRowIndex, endColIndex);
        if(coveredArea1.isEmpty() || coveredArea2.isEmpty())
        {
            return false;
        }
        DataRange dR = coveredArea1.get(0);
        DataRange endDR = coveredArea2.get(0);

        if(dR.isCell() && startRowIndex == endRowIndex && startColIndex == endColIndex)
        {
            return false;
        }

        boolean isStartInMergedArea = false;
        boolean isEndInMergedArea = false;
        if(!dR.isCell() && startRowIndex >= dR.getStartRowIndex() && startRowIndex <= dR.getEndRowIndex()
            && startColIndex >= dR.getStartColIndex() && startColIndex <= dR.getEndColIndex())
        {
            isStartInMergedArea = true;
        }

        if(endRowIndex >= dR.getStartRowIndex() && endRowIndex <= dR.getEndRowIndex()
            && endColIndex >= dR.getStartColIndex() && endColIndex <= dR.getEndColIndex() || !endDR.isCell())
        {
            isEndInMergedArea = true;
        }
        if(isStartInMergedArea)
        {
            if(isEndInMergedArea)
            {
                return Boolean.TRUE;
            }
        }
        else if(!isEndInMergedArea)
        {
            return Boolean.FALSE;
        }

        return null;
    }


    public static List<DataRange> getSpecialCells(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex,
                                                  String type, String value)
    {
        List<DataRange> dataRangeList = new ArrayList<>();

        int usedRowIndex = sheet.getUsedRowIndex();
        int usedColIndex = sheet.getUsedColumnIndex();

        endRowIndex = endRowIndex>usedRowIndex?usedRowIndex:endRowIndex;
        endColIndex = endColIndex>usedColIndex?usedColIndex:endColIndex;
        // end range is greater than start range
        if(startRowIndex > endRowIndex || startColIndex > endColIndex)
        {
            // return from here as its a invalid range
            return dataRangeList;
        }

        switch (type)
        {
            case "SameValidation"://NO I18N
                // not supported in engine
                return dataRangeList;
            case "SameFormatConditions"://NO I18N
                // not supported in engine
                return dataRangeList;
            case "AllValidation"://NO I18N
                // not supported in engine
                return dataRangeList;
            case "AllFormatConditions"://NO I18N
                // not supported in engine
                return dataRangeList;
            case EngineConstants.VISIBILITY_VISIBLE: {
                // code changed to read the Visible cells alone
                dataRangeList = getVisibleRanges(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
                if (value != null) {
                    return getSpecialCells(sheet, dataRangeList, value);
                }
                return dataRangeList;
            }
            case "LastCell"://NO I18N
            {
                // based on usedIndex
                DataRange dataRange = new DataRange(sheet.getAssociatedName(), sheet.getUsedRowIndex(), sheet.getUsedColumnIndex(), sheet.getUsedRowIndex(), sheet.getUsedColumnIndex());
                dataRangeList.add(dataRange);
                return dataRangeList;
            }
        }

        boolean isMarked = false;
        int markedRow = 0, markedCol = 0;//, rowsRepeated = 0;
        for(int row = startRowIndex; row <= endRowIndex; row++)
        {
            markedRow = 0;
            markedCol = 0;
            int rowsRepeated = 0;
            for(int col = startColIndex; col <= endColIndex; col++)
            {
                ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(row, col);
                Cell cell = rCell.getCell();
                if(type.equals("Formulas") && cell != null && cell.isFormula())
                {
                    if(!isMarked)
                    {
                        markedRow = row;
                        markedCol = col;
                        isMarked = true;
                    }
                }
                else if(type.equals("Blanks") && ( cell == null || cell.getType() == Type.UNDEFINED) )
                {
                    if(!isMarked)
                    {
                        markedRow = row;
                        markedCol = col;
                        isMarked = true;
                    }
                }
                else if(type.equals("Comments") && cell != null && cell.getAnnotation()!= null)
                {
                    if(!isMarked)
                    {
                        markedRow = row;
                        markedCol = col;
                        isMarked = true;
                    }
                }
                else if(type.equals("Constants") && cell != null && !cell.isFormula() && cell.getType() != Type.UNDEFINED)
                {
                    if(!isMarked)
                    {
                        markedRow = row;
                        markedCol = col;
                        isMarked = true;
                    }
                }
                else
                {
                    if(isMarked)
                    {
                        // till prev cell of same row
                        DataRange dataRange = new DataRange(sheet.getAssociatedName(), markedRow, markedCol, row, col-1);
                        dataRangeList.add(dataRange);
                        isMarked = false;
                    }
                }
                col += rCell.getColsRepeated() - 1;
                rowsRepeated = rCell.getRowsRepeated() - 1;
            }
            // for rows
            row += rowsRepeated;
            if(isMarked)
            {
                DataRange dataRange = new DataRange(sheet.getAssociatedName(), markedRow, markedCol, row, endColIndex);
                dataRangeList.add(dataRange);
                isMarked = false;
            }
        }

        if(isMarked)
        {
            DataRange dataRange = new DataRange(sheet.getAssociatedName(), markedRow, markedCol, endRowIndex, endColIndex);
            dataRangeList.add(dataRange);
        }

        /////////////////////////////////
        if(value != null)
        {
            return getSpecialCells(sheet, dataRangeList, value);
        }

        return dataRangeList;
    }

    private static List<DataRange> getSpecialCells(Sheet sheet, List<DataRange> inDataRangeList, String value)
    {
        List<DataRange> dataRangeList = new ArrayList<>();
        //////////////// Values//////////////////////
        // Errors
        // Logical
        // Numbers
        // TextValues
        /////////////////////////////////////////////
        for(DataRange dataRange : inDataRangeList)
        {
            int startRow = dataRange.getStartRowIndex();
            int startCol = dataRange.getStartColIndex();
            int endRow = dataRange.getEndRowIndex();
            int endCol = dataRange.getEndColIndex();

            int markedRow = 0, markedCol = 0;//, rowsRepeated = 0;
            boolean isMarked = false;
            // for cell precedents
            for(int row = startRow; row <= endRow; row++)
            {
                markedRow = 0;
                markedCol = 0;
                int rowsRepeated = 0;
                for(int col = startCol; col <= endCol; col++)
                {
                    ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(row, col);
                    Cell cell = rCell.getCell();
                    if(value.equals("Errors") && cell != null && cell.getType() == Type.ERROR)
                    {
                        if(!isMarked)
                        {
                            markedRow = row;
                            markedCol = col;
                            isMarked = true;
                        }
                    }
                    else if(value.equals("Logical") && cell != null && cell.getType() == Type.BOOLEAN)
                    {
                        if(!isMarked)
                        {
                            markedRow = row;
                            markedCol = col;
                            isMarked = true;
                        }
                    }
                    else if(value.equals("Numbers") && cell != null &&
                        (cell.getValue().getValue() instanceof Number || cell.getValue().getValue() instanceof Date))
                    {
                        if(!isMarked)
                        {
                            markedRow = row;
                            markedCol = col;
                            isMarked = true;
                        }
                    }
                    else if(value.equals("TextValues") && cell != null && cell.getType() == Type.STRING)
                    {
                        if(!isMarked)
                        {
                            markedRow = row;
                            markedCol = col;
                            isMarked = true;
                        }
                    }
                    else
                    {
                        if(isMarked)
                        {
                            // till prev cell of same row
                            DataRange newDataRange = new DataRange(sheet.getAssociatedName(), markedRow, markedCol, row, col-1);
                            dataRangeList.add(newDataRange);
                            isMarked = false;
                        }
                    }
                    col += rCell.getColsRepeated() - 1;
                    rowsRepeated = rCell.getRowsRepeated() - 1;
                } // end of for col
                // for rows
                row += rowsRepeated;
                if(isMarked)
                {
                    DataRange newDataRange = new DataRange(sheet.getAssociatedName(), markedRow, markedCol, row, endCol);
                    dataRangeList.add(newDataRange);
                    isMarked = false;
                }
            } // end of for row

            if(isMarked)
            {
                DataRange newDataRange = new DataRange(sheet.getAssociatedName(), markedRow, markedCol, endRow, endCol);
                dataRangeList.add(newDataRange);
            }
        } // end of for DataRange

        return dataRangeList;
    }

    public static List<Integer> getEmptyRows(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        List <Integer> emptyRows = new LinkedList ();
        for(int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++)
        {
            ReadOnlyRow roRow = RangeIterator.getReadOnlyRowAtSheet(sheet, rowIndex);
            int repeated = roRow.getRowsRepeated();

            Row row = roRow.getRow();

            if(row == null || RangeIterator.isRowEmpty(row, startColIndex, endColIndex))
            {
                repeated = Math.min(repeated, endRowIndex - rowIndex + 1);
                while(repeated > 0) {
                    emptyRows.add(rowIndex);
                    rowIndex ++;
                    repeated --;
                }
                rowIndex -= 1;
            }else {
                rowIndex += repeated - 1;
            }
        }
        return emptyRows;
    }

    public static List<DataRange> getVisibleRowsRange(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        List<DataRange> dataRangeList = new ArrayList<>();
        DataRange lastDataRange = null;

        boolean isMarked = false;
        int markedIndex = -1;
        for(int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++)
        {
            ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(rowIndex);
            int rowsRepeated = rRow.getRowsRepeated();
            Row row = rRow.getRow();
            if(RowUtil.isVisibleRow(row))
            {
                if(!isMarked)
                {
                    markedIndex = rowIndex;
                    isMarked = true;
                }
            }
            else
            {
                if(isMarked)
                {
                    DataRange dataRange = new DataRange(sheet.getAssociatedName(), markedIndex, startColIndex, rowIndex - 1, endColIndex);
                    dataRangeList.add(dataRange);
                    isMarked = false;
                }
            }

            rowIndex += rowsRepeated - 1;
        }

        if(isMarked)
        {
            DataRange dataRange = new DataRange(sheet.getAssociatedName(), markedIndex, startColIndex, endRowIndex, endColIndex);
            dataRangeList.add(dataRange);
        }

        if(lastDataRange != null)
        {
            dataRangeList.add(lastDataRange);
        }

        return dataRangeList;
    }

    public static List<DataRange> getVisibleColumnsRange(Sheet sheet, List<DataRange> dataRanges)
    {
        List<DataRange> visibleDataRanges = new ArrayList<>();
        for(DataRange dataRange : dataRanges)
        {
            visibleDataRanges.addAll(getVisibleColsRange(sheet, dataRange.getStartRowIndex(), dataRange.getStartColIndex(), dataRange.getEndRowIndex(), dataRange.getEndColIndex()));
        }
        return visibleDataRanges;
    }

    public static List<DataRange> getVisibleColsRange(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) {
        List<DataRange> dataRangeList = new ArrayList<>();

        Integer visibleStartColIndex = null;
        for(int colIndex = startColIndex; colIndex <= endColIndex; colIndex++) {
            ReadOnlyColumnHeader roColHeader = RangeIterator.getReadOnlyColumnHeaderAtSheet(sheet, colIndex);
            ColumnHeader colHeader = roColHeader.getColumnHeader();

            boolean isVisibleCol = (ColumnUtil.isVisibleColumn(colHeader));
            if(isVisibleCol && visibleStartColIndex == null) {
                visibleStartColIndex = colIndex;
            }else if(!isVisibleCol && visibleStartColIndex != null) {
                dataRangeList.add(new DataRange(sheet.getAssociatedName(), startRowIndex, visibleStartColIndex, endRowIndex, colIndex - 1));
                visibleStartColIndex = null;
            }
        }
        if(visibleStartColIndex != null)
        {
            dataRangeList.add(new DataRange(sheet.getAssociatedName(), startRowIndex, visibleStartColIndex, endRowIndex, endColIndex));
        }
        return dataRangeList;
    }

    public static List<DataRange> getRowVisibleRanges(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) {
        List<LinearIntegralRange> visibleRowLirs = getVisibleRows(sheet, startRowIndex, endRowIndex).toLinearIntegralRangeList();

        List<DataRange> visibleDataRanges = new LinkedList();
        for(LinearIntegralRange visibleRowLir: visibleRowLirs) {
            visibleDataRanges.add(new DataRange(sheet.getAssociatedName(), visibleRowLir.getStartInt(), startColIndex, visibleRowLir.getEndInt(), endColIndex));
        }

        return visibleDataRanges;
    }

    public static List<DataRange> getColVisibleRanges(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) {
        List<LinearIntegralRange> visibleColLirs = getVisibleCols(sheet, startColIndex, endColIndex).toLinearIntegralRangeList();

        List<DataRange> visibleDataRanges = new LinkedList();
        for(LinearIntegralRange visibleColLir: visibleColLirs) {
            visibleDataRanges.add(new DataRange(sheet.getAssociatedName(), startRowIndex, visibleColLir.getStartInt(), endRowIndex, visibleColLir.getEndInt()));
        }

        return visibleDataRanges;
    }

    public static List<DataRange> getVisibleRanges(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) {
        List<LinearIntegralRange> visibleRowLirs = getVisibleRows(sheet, startRowIndex, endRowIndex).toLinearIntegralRangeList();
        List<LinearIntegralRange> visibleColLirs = getVisibleCols(sheet, startColIndex, endColIndex).toLinearIntegralRangeList();

        List<DataRange> visibleDataRanges = new LinkedList();
        for(LinearIntegralRange visibleRowLir: visibleRowLirs) {
            for(LinearIntegralRange visibleColLir: visibleColLirs) {
                visibleDataRanges.add(new DataRange(sheet.getAssociatedName(), visibleRowLir.getStartInt(), visibleColLir.getStartInt(), visibleRowLir.getEndInt(), visibleColLir.getEndInt()));
            }
        }

        return visibleDataRanges;
    }

    public static List<DataRange> getUnFilteredRanges(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) {
        List<LinearIntegralRange> visibleRowLirs = getUnFilteredRows(sheet, startRowIndex, endRowIndex).toLinearIntegralRangeList();
        List<LinearIntegralRange> visibleColLirs = getColumnHeaders(sheet, startColIndex, endColIndex, false).toLinearIntegralRangeList();

        List<DataRange> visibleDataRanges = new LinkedList();
        for(LinearIntegralRange visibleRowLir: visibleRowLirs) {
            for(LinearIntegralRange visibleColLir: visibleColLirs) {
                visibleDataRanges.add(new DataRange(sheet.getAssociatedName(), visibleRowLir.getStartInt(), visibleColLir.getStartInt(), visibleRowLir.getEndInt(), visibleColLir.getEndInt()));
            }
        }

        return visibleDataRanges;
    }

    public static IntegralSet getColumnHeaders(Sheet sheet, int startColIndex, int endColIndex, boolean isVisible)
    {
        IntegralSet visibleCols = new TreeBasedIntegralSet();
        int currColIndex = startColIndex;
        while(currColIndex <= endColIndex) {
            ReadOnlyColumnHeader roColHeader = RangeIterator.getReadOnlyColumnHeaderAtSheet(sheet, currColIndex);
            ColumnHeader colHeader = roColHeader.getColumnHeader();
            if(isVisible ? ColumnUtil.isVisibleColumn(colHeader) : true)
            {
                int repeated = Math.min(roColHeader.getColsRepeated(), endColIndex - (currColIndex - 1));
                visibleCols.add(currColIndex, repeated);
            }
            currColIndex += roColHeader.getColsRepeated();
        }
        return visibleCols;
    }

    public static IntegralSet getUnFilteredRows(Sheet sheet, int startRowIndex, int endRowIndex) {
        IntegralSet visibleRows = new TreeBasedIntegralSet();
        int currRowIndex = startRowIndex;
        while(currRowIndex <= endRowIndex) {
            ReadOnlyRow roRow = RangeIterator.getReadOnlyRowAtSheet(sheet, currRowIndex);
            Row row = roRow.getRow();
            boolean isFiltered = RowUtil.isFilteredRow(row);
            if(!isFiltered)
            {
                int repeated = Math.min(roRow.getRowsRepeated(), endRowIndex - (currRowIndex - 1));
                visibleRows.add(currRowIndex, repeated);
            }
            currRowIndex += roRow.getRowsRepeated();
        }
        return visibleRows;
    }

    public static IntegralSet getVisibleRows(Sheet sheet, int startRowIndex, int endRowIndex) {
        IntegralSet visibleRows = new TreeBasedIntegralSet();
        int currRowIndex = startRowIndex;
        while(currRowIndex <= endRowIndex) {
            ReadOnlyRow roRow = RangeIterator.getReadOnlyRowAtSheet(sheet, currRowIndex);
            Row row = roRow.getRow();
            boolean isVisible = RowUtil.isVisibleRow(row);
            if(isVisible) {
                int repeated = Math.min(roRow.getRowsRepeated(), endRowIndex - (currRowIndex - 1));
                visibleRows.add(currRowIndex, repeated);
            }
            currRowIndex += roRow.getRowsRepeated();
        }
        return visibleRows;
    }

    public static IntegralSet getVisibleCols(Sheet sheet, int startColIndex, int endColIndex) {
        return getColumnHeaders(sheet, startColIndex, endColIndex, true);
    }

    //public static void setContentValidation(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, int condnNum, String value) throws IllegalArgumentException
    public static void setContentValidation(Sheet sheet, List<DataRange> listOfRanges, ContentValidation contentValidation) throws IllegalArgumentException
    {
        sheet.setIsModified(true);

        List<DataRange> ranges = new ArrayList<>();
        for(DataRange range: sheet.getContentValidationRanges()) {
            ContentValidation cv = sheet.getCell(range.getStartRowIndex(), range.getStartColIndex()).getContentValidation();
            if(contentValidation.equals(cv)) {
                ranges.add(range);
            }
        }

        List<DataRange> newListOfRanges = null;
        // No need to add the range if it is a subset of existing range.
        newListOfRanges = new ArrayList<>();
        for(DataRange appliedRange : listOfRanges)
        {
            boolean isSubsetOfExistingRange = false;
            for(DataRange range: ranges)
            {
                if(range.isMember(sheet.getAssociatedName(), appliedRange.getStartRowIndex(), appliedRange.getStartColIndex()) && range.isMember(sheet.getAssociatedName(), appliedRange.getEndRowIndex(), appliedRange.getEndColIndex()))
                {
                    isSubsetOfExistingRange = true;
                }
            }
            if(!isSubsetOfExistingRange) {
                newListOfRanges.add(appliedRange);
            }
        }
        if(newListOfRanges.isEmpty())
        {
            return;
        }

        contentValidation.setName("temp" + UUID.randomUUID());//No I18N
        contentValidation = sheet.getWorkbook().checkAndAddContentValidation(contentValidation);
        setContentValidationToCell(sheet,listOfRanges,contentValidation);

        List<DataRange> mergedRanges = RangeUtil.mergeDataRanges(listOfRanges);
        for(DataRange range: mergedRanges) {
            sheet.addContentValidationRange(sheet, range);
        }

    }

    public static void setContentValidationToCell(Sheet sheet, List<DataRange> rangeList, ContentValidation cv) {
        sheet.setIsModified(true);
        for(DataRange range : rangeList) {
            int startRowIndex = range.getStartRowIndex();
            int startColIndex = range.getStartColIndex();
            int endRowIndex = range.getEndRowIndex();
            int endColIndex = range.getEndColIndex();

            int endRow = Math.min(endRowIndex, Math.max(startRowIndex, sheet.getRowNum() - 1));
            for (int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++)
            {
                Row row = sheet.getRowReadOnly(rowIndex) == null ? createNewRow(sheet, rowIndex, endRowIndex) : sheet.getRowReadOnly(rowIndex);
                // TODOD : if it exceeds 65536
                if (row.getRowsRepeated() > (endRowIndex - rowIndex + 1))
                {
                    sheet.getRow(endRowIndex + 1);
                }

                int endCol = Math.min(endColIndex, Math.max(startColIndex, row.getCellNum() - 1));
                for (int colIndex = startColIndex; colIndex <= endCol; colIndex++)
                {
                    Cell cell = sheet.getCellReadOnly(rowIndex, colIndex) == null ? createNewCell(sheet, row.getCells(), rowIndex, colIndex, endColIndex) : sheet.getCellReadOnly(rowIndex, colIndex);
                    // TODOD : if it exceeds 256
                    if (cell.getColsRepeated() > (endColIndex - colIndex + 1))
                    {
                        sheet.getCell(rowIndex, endColIndex + 1);
                    }


                    // Set the name of the cellStyle
                    ((CellImpl) cell).setContentValidationName(cv.getName(), false);

                    colIndex += cell.getColsRepeated() - 1;
                }

                if (endColIndex > endCol)
                {
                    Cell cell = sheet.getCell(rowIndex, endCol + 1);
                    // Set the name of the cellStyle
                    ((CellImpl) cell).setContentValidationName(cv.getName(), false);

                    cell.setColsRepeated(endColIndex - endCol);
                }

                rowIndex += row.getRowsRepeated() - 1;
            }

            if (endRowIndex > endRow)
            {
                //Row row = sheet.getRow(endRow + 1);
                Cell cell = sheet.getCell(endRow + 1, startColIndex);
                // Set the name of the cellStyle
                ((CellImpl) cell).setContentValidationName(cv.getName(), false);

                cell.setColsRepeated(endColIndex - startColIndex + 1);
                Row row = cell.getRow();
                row.setRowsRepeated(endRowIndex - endRow);
            }

            DataRange appliedRange = new DataRange(sheet.getAssociatedName(), startRowIndex, startColIndex, endRowIndex, endColIndex);
            sheet.removeContentValidationRanges(appliedRange);
        }
    }

    public static List<Cell> setPicklistToCell(Sheet sheet, List<DataRange> rangeList, Value picklistValue, Picklist picklist) {
        List<Cell> changedCells = new ArrayList<>();
        for(DataRange range : rangeList) {
            int startRowIndex = range.getStartRowIndex();
            int startColIndex = range.getStartColIndex();
            int endRowIndex = range.getEndRowIndex();
            int endColIndex = range.getEndColIndex();


            int endRow = endRowIndex;
            for (int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++) {
                Row row = sheet.getRowReadOnly(rowIndex);
                if (row == null) {
                    Row prevRow = null;
                    for (int i = (rowIndex - 1); i >= 0; i--) {
                        prevRow = sheet.getRowReadOnly(i);
                        if (prevRow != null) {
                            break;
                        }
                    }

                    if (prevRow != null && prevRow.getRowsRepeated() > (rowIndex - prevRow.getRowIndex())) {
                        row = sheet.getRow(rowIndex);
                    } else {
                        // so the row is null
                        int rowsRepeated = GeneralUtil.getConsecutiveNullCount(sheet.getRows(), rowIndex, endRowIndex);

                        row = sheet.getRow(rowIndex);
                        row.setRowsRepeated(rowsRepeated);
                    }

                }

                // TODOD : if it exceeds 65536
                if (row.getRowsRepeated() > (endRowIndex - rowIndex + 1)) {
                    sheet.getRow(endRowIndex + 1);
                }

                int endCol = endColIndex;
                for (int colIndex = startColIndex; colIndex <= endCol; colIndex++) {
                    Cell cell = sheet.getCellReadOnly(rowIndex, colIndex);

                    if (cell == null) {
                        Cell prevCell = null;
                        for (int i = (colIndex - 1); i >= 0; i--) {
                            prevCell = sheet.getCellReadOnly(rowIndex, i);
                            if (prevCell != null) {
                                break;
                            }
                        }

                        if (prevCell != null && prevCell.getColsRepeated() > (colIndex - prevCell.getColumnIndex())) {
                            cell = sheet.getCell(rowIndex, colIndex);
                        } else {
                            // so the row is null
                            int colsRepeated = GeneralUtil.getConsecutiveNullCount(row.getCells(), colIndex, endColIndex);

                            cell = sheet.getCell(rowIndex, colIndex);
                            cell.setColsRepeated(colsRepeated);
                        }

                    }
                    // TODOD : if it exceeds 256
                    if (cell.getColsRepeated() > (endColIndex - colIndex + 1)) {
                        sheet.getCell(rowIndex, endColIndex + 1);
                    }

                    if(picklist != null) {
                        if(cell.getValue() instanceof PicklistValue) {
                            PicklistValue currentValue = (PicklistValue) cell.getValue();
                            List<PicklistItem> currentItems = currentValue.getPicklistItems();
                            List<Integer> itemIDs = new ArrayList<>(currentItems.size());

                            for(PicklistItem picklistItem: currentItems) {
                                if(picklistItem.getDisplayValue().isPresent()) {
                                    String content = picklistItem.getDisplayValue().get();
                                    PicklistItem equalItem = picklist.getEqualItem(content);
                                    if(equalItem != null) {
                                        itemIDs.add(equalItem.getId());
                                    }
                                }
                            }

                            if(itemIDs.isEmpty()) {
                                cell.setValue(picklist.getDefaultPicklistValue());
                            }
                            else {
                                cell.setValue(new PicklistValue(picklist, itemIDs));
                            }
                        }
                        else {
                            String content = cell.getContent();
                            PicklistItem equalItem = picklist.getEqualItem(content);
                            if(equalItem != null) {
                                cell.setValue(picklist.getPicklistValueObj(equalItem.getId()));
                            }
                            else {
                                cell.setValue(picklist.getDefaultPicklistValue());
                            }
                        }

                    }
                    else {
                        if(picklistValue != null) {
                            cell.setValue(picklistValue);
                        }
                        else {
                            Value value = cell.getValue();
                            Value newValue = Value.getInstance(value.getType(),value.getValue());
                            cell.setValue(newValue);
                        }
                    }
                    changedCells.add(cell);

                    colIndex += cell.getColsRepeated() - 1;
                }

                rowIndex += row.getRowsRepeated() - 1;
            }

        }
        return changedCells;
    }

    public static void setConditionalStyles(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, List<ConditionalFormatOperator.ConditionType> condnTypeList, List<Integer> condnNumList, List<String> valueList, List<CellStyle> cellStyleList) throws IllegalArgumentException
    {
        if (cellStyleList == null || cellStyleList.isEmpty())
        {
            return;
        }

        sheet.setIsModified(true);
        sheet.getWorkbook().setIsStylesChanged(true);

        // TODO: Its not a single range. Scope for discontinous ranges too...
        DataRange range = new DataRange(sheet.getAssociatedName(), startRowIndex, startColIndex, endRowIndex, endColIndex);
        List<DataRange> ranges = new ArrayList<>();
        ranges.add(range);

        for(int index = 0; index < cellStyleList.size(); index++)
        {
            CellStyle cellStyle = cellStyleList.get(index);
            cellStyle.setStyleName("temp" + UUID.randomUUID());//No I18N

            // Create corresponding mapStyle and ad to list.
            MapStyle mapStyle = new MapStyle(condnTypeList.get(index), condnNumList.get(index), valueList.get(index), cellStyle, sheet, startRowIndex, startColIndex);
            sheet.addMapStyleToFormatMap(ranges, mapStyle);
        }
        // Need to call clearconditionalstyle... Check that...
        // Don't need to call clearconditionalstyle since ranges can overlap...
//            clearConditionalStyles_New(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);

        // Don't need to call addToConditionFormatMapForCopy() for APPLY case as well as for EDIT case...
        // TODO: But in EDIT case, addToFormatMap should not be called.
//            sheet.addToConditionFormatMapForCopy(conditionalStyleFormats, isApply);
    }

    public static void clearProtection(Workbook workBook, List<DataRange> listOfDataRanges)
    {
    	for(DataRange dataRange : listOfDataRanges)
        {
//    		DataRange dataRange = listOfDataRanges.get(0);
            int startRow = dataRange.getStartRowIndex();
            int startCol = dataRange.getStartColIndex();
            int endRow = dataRange.getEndRowIndex();
            int endCol = dataRange.getEndColIndex();
            Sheet sheet = workBook.getSheetByAssociatedName(dataRange.getAssociatedSheetName());
            sheet.removeLockedRanges(new DataRange(sheet.getAssociatedName(), startRow, startCol, endRow, endCol));
    	}
    }

    public static void clearProtection(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        //        sheet.removeLockedRanges(new DataRange(sheet.getAssociatedName(), startRowIndex, startColIndex, endRowIndex, endColIndex));
        sheet.removeLockedRanges(new DataRange(sheet.getAssociatedName(), startRowIndex, startColIndex, endRowIndex, endColIndex));
    }

    public static void protectRange(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, EnumMap permissionMap)
    {
        protectRange(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, permissionMap, false);
    }

    public static void protectRange(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, EnumMap permissionMap, boolean isAllowInsert)
    {
        protectRange(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, permissionMap, isAllowInsert, false);
    }

    public static void protectRange(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, EnumMap permissionMap, boolean isAllowInsert, boolean isAllowFormats)
    {

        sheet.getWorkbook().setIsNamedExpressionChanged(true);
        sheet.setIsModified(true);
        DataRange range = new DataRange(sheet.getAssociatedName(), startRowIndex, startColIndex, endRowIndex, endColIndex);
        //        Map newProtectionRangeMap = sheet.removeSpecialRanges2(range, sheet.getProtectionRangeMap());
        //
        //        if(newProtectionRangeMap != null)
        //        {
        //            sheet.getProtectionRangeMap().clear();
        //            sheet.getProtectionRangeMap().putAll(newProtectionRangeMap);
        //        }

        sheet.removeLockedRanges(range);

        Protection protection = new Protection(permissionMap, isAllowInsert, isAllowFormats);

        List<DataRange> ranges = sheet.getProtectionRangeMap().get(protection);

        if(ranges == null)
        {
            ranges = new ArrayList<>();
            sheet.getProtectionRangeMap().put(protection, ranges);
        }
        ranges.add(range);

        //        sheet.addProtectedRangeFromParser(new ProtectedRange(range, permissionMap));
        //        mergeAndAddRangeToList(new ProtectedRange(range, permissionMap), sheet.getProtectedRanges());
    }
    public static void clearContentValidations(Workbook workbook, List<DataRange> listOfRanges) throws IllegalArgumentException
    {




        for(DataRange range : listOfRanges)
        {
            Sheet sheet = workbook.getSheetByAssociatedName(range.getAssociatedSheetName());
            // If there is no conditional format applied to this sheet, no need to iterate thro' the range.
            if(sheet.getContentValidationRanges().isEmpty())
            {
                return;
            }
            sheet.setIsModified(true);

            int startRowIndex = range.getStartRowIndex();
            int startColIndex = range.getStartColIndex();
            int endRowIndex = range.getEndRowIndex();
            int endColIndex = range.getEndColIndex();
            int endRow = Math.min(endRowIndex, Math.max(startRowIndex, sheet.getRowNum() - 1));
            for (int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++)
            {
                Row row = sheet.getRowReadOnly(rowIndex) == null ? createNewRow(sheet, rowIndex, endRowIndex) : sheet.getRowReadOnly(rowIndex);
                // TODOD : if it exceeds 65536
                if(row.getRowsRepeated() > (endRowIndex - rowIndex + 1))
                {
                    sheet.getRow(endRowIndex + 1);
                }
                int endCol = Math.min(endColIndex, Math.max(startColIndex, row.getCellNum() - 1));
                for (int colIndex = startColIndex; colIndex <= endCol; colIndex++)
                {
                    Cell cell = sheet.getCellReadOnly(rowIndex, colIndex) == null ? createNewCell(sheet, row.getCells(), rowIndex, colIndex, endColIndex) : sheet.getCellReadOnly(rowIndex, colIndex);
                    // TODOD : if it exceeds 256
                    if(cell.getColsRepeated() > (endColIndex - colIndex + 1))
                    {
                        sheet.getCell(rowIndex, endColIndex + 1);
                    }
                    ((CellImpl) cell).setContentValidationName(null, false);
                    colIndex += cell.getColsRepeated() - 1;
                }

                if (endColIndex > endCol)
                {
                    Cell cell = sheet.getCell(rowIndex, endCol + 1);
                    ((CellImpl) cell).setContentValidationName(null, false);

                    cell.setColsRepeated(endColIndex - endCol);
                }

                rowIndex += row.getRowsRepeated() - 1;
            }

            if (endRowIndex > endRow)
            {
                //Row row = sheet.getRow(endRow + 1);
                Cell cell = sheet.getCell(endRow + 1, startColIndex);
                ((CellImpl) cell).setContentValidationName(null, false);

                cell.setColsRepeated(endColIndex - startColIndex + 1);
                Row row = cell.getRow();
                row.setRowsRepeated(endRowIndex - endRow);
            }

            Range appliedRange = new Range(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
            sheet.removeContentValidationRanges(appliedRange);
        }
    }
    public static void clearConditionalStyles(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) throws IllegalArgumentException
    {
        // If there is no conditional format applied to this sheet, no need to iterate thro' the range.
        if(sheet.getConditionalStyleMap().isEmpty())
        {
            return;
        }

        sheet.setIsModified(true);

        Range appliedRange = new Range(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
        sheet.removeConditionalStyleRanges(appliedRange, true, true);
    }

    // Used to read visible ranges list if filter is applied
    public static List<Integer[]> getVisibleRangesList(Sheet sheet, int startRow, int endRow)
    {	//Have to remove this.
        Integer rangeArr[] = new Integer[2];
        List<Integer[]> rangeList = new ArrayList<>();

        boolean isFirstTime = true;
        for(int rowIndex = startRow; rowIndex <= endRow; rowIndex++)
        {
            Row row = sheet.getRow(rowIndex);
            if(row != null)
            {
                if(RowUtil.isFilteredRow(row))
                {
                    if(!isFirstTime)
                    {
                        if(rangeArr[1] == null)
                        {
                            rangeArr[1] = rangeArr[0];
                        }
                        rangeList.add(rangeArr);
                    }
                    isFirstTime = true;
                }
                else
                {
                    if(isFirstTime)
                    {
                        rangeArr = new Integer[2];
                        isFirstTime = false;
                        rangeArr[0] = rowIndex;
                    }
                    rangeArr[1] = rowIndex;
                    if(rowIndex == endRow)
                    {
                        rangeList.add(rangeArr);
                    }
                }
            }
        }
        return rangeList;
    }

    public static boolean isEntireRangeContainSameExpression(Expression expression, Range range)
    {
        if(expression == null || range == null)
        {
            return false;
        }

        Sheet sheet = range.getSheet();
        for(int rowIndex = range.getStartRowIndex() ; rowIndex <= range.getEndRowIndex() ; rowIndex++)
        {
            for(int colIndex = range.getStartColIndex() ;  colIndex <= range.getEndColIndex() ; colIndex++)
            {
                if(!expression.equals(sheet.getCell(rowIndex, colIndex).getExpression()))
                {
                    return false;
                }
            }
        }
        return true;
    }

    //public static DataRange expandFilterRange(Sheet sheet, int sR, int sC, int eR, int eC) throws Exception
    public static DataRange expandFilterRange(Sheet sheet, int sR, int sC, int eR, int eC, boolean isSkipHiddenRows) throws Exception
    {
        Instant currentInstant = Instant.now();
        for (int i = 0; i < 2; i++)
        {
            int row = (i == 0) ? (sR - 1) : (eR + 1);
            int increment = (i == 0) ? -1 : 1;
            for (; row >= 0 && row <= sheet.getUsedRowIndex(); row += increment)   //row += 1
            {
                int newSc = findColumnIndex(sheet, row, sC, -1, isSkipHiddenRows);
                int newEc = findColumnIndex(sheet, row, eC, 1, isSkipHiddenRows);
                if (newSc == sC && newEc == eC)
                {
                    RangeIterator rIterator = new RangeIterator(sheet, row, sC, row, eC, RangeIterator.IterationStartPositionEnum.TOP_LEFT, isSkipHiddenRows, false, false, false, false, true);
                    ReadOnlyCell rCell = rIterator.next();
                    if(rCell == null)
                    {
                        continue;
                    }

                    boolean isBreak = true;
                    while(rCell != null)
                    {
                        if(Instant.now().isAfter(currentInstant.plusSeconds(2)))
                        {
                            LOGGER.log(Level.SEVERE, "[FILTERS] Create Filter has taken more than 2 seconds");
                            throw new FilterException("[FILTERS] Create filter has taken more time.");  // No I18N
                        }
                        Cell cell = rCell.getCell();
                        if(cell != null)
                        {
                            if(sheet.isCoveredUnderMerge(cell.getRowIndex(), cell.getColumnIndex()))
                            {
                                cell = sheet.getMergeParentCell(cell.getRowIndex(),  cell.getColumnIndex());
                            }
                            if(cell.getValue().getValue() != null)
                            {
                                isBreak = false;
                                int[] spans = sheet.getMergeCellSpans(cell);
                                row += ((spans[0] - 1) * increment);
                            }
                        }
                        rCell = rIterator.next();
                    }
                    if(isBreak)
                    {
                        break;
                    }
                }
                else
                {
                    sC = newSc;
                    eC = newEc;
                }

                if (i == 0)
                {
                    sR = Math.max(0, row);
                }
                else
                {
                    eR = Math.min(Utility.MAXNUMOFROWS-1, row);
                }
            }
        }

        return new DataRange(sheet.getAssociatedName(), sR, sC, eR, eC);
    }

    public static DataRange expandPivotRange(Sheet sheet, int row, int col) throws Exception
    {
        DataRange expandedRange = expandFilterRange(sheet, row, col, row, col, false);
        int topRowIndex = expandedRange.getStartRowIndex();
        int trimmedStartCol = expandedRange.getStartColIndex();
        int trimmedEndCol = expandedRange.getEndColIndex();
        Cell cell = null;

        while(trimmedStartCol < trimmedEndCol)
        {

            cell = sheet.getCell(topRowIndex, trimmedStartCol);
            if((cell == null || cell.getValue().getValue() == null))
            {
                trimmedStartCol++;
            }
            else
            {
                break;
            }
        }

        while(trimmedEndCol > trimmedStartCol)
        {
            cell = sheet.getCell(topRowIndex, trimmedEndCol);
            if((cell == null || cell.getValue().getValue() == null))
            {
                trimmedEndCol--;
            }
            else
            {
                break;
            }
        }

        return new DataRange(sheet.getAssociatedName(), topRowIndex, trimmedStartCol, expandedRange.getEndRowIndex(), trimmedEndCol);
    }

    public static DataRange detectDataRange(Sheet sheet, int startRow, int startCol, int endRow, int endCol, boolean isForMacro) throws Exception
    {
        int stRow = startRow;
        int stCol = startCol;
        int enRow = endRow;
        int enCol = endCol;
        for (int row = startRow; row <= endRow; row++)
        {
            int newSc = findColumnIndex(sheet, startRow, startCol, -1, true);
            int newEc = findColumnIndex(sheet, startRow, endCol, 1, true);
            startCol = newSc;
            endCol = newEc;
        }
        DataRange filterRange = expandFilterRange(sheet, startRow, startCol, endRow, endCol, true);

        startRow = filterRange.getStartRowIndex();
        startCol = filterRange.getStartColIndex();
        endRow = filterRange.getEndRowIndex();
        endCol = filterRange.getEndColIndex();

        if (stRow == startRow && stCol == startCol && enRow == endRow &&  enCol == endCol && isBlankCell(sheet, startRow, startCol, true) && !isForMacro)
        {
            return null;
        }
        return new DataRange(sheet.getAssociatedName(), startRow, startCol, endRow, endCol);
    }


    public static int findColumnIndex(Sheet sheet, int row, int col, int inc, boolean isSkipHiddenRows) throws Exception
    {
        try
        {
            while (CellUtil.isColumnRangeBound(col + inc) && !isBlankCell(sheet, row, col + inc, isSkipHiddenRows))
            {
                col += inc;
            }
        } catch (Exception e)
        {
        }
        return col;
    }

    //public static boolean isBlankRange(Sheet sheetObj, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    public static boolean isBlankRange(Sheet sheetObj, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, boolean isSkipHiddenRows)
    {
        RangeIterator rIterator = new RangeIterator(sheetObj, startRowIndex, startColIndex, endRowIndex, endColIndex, RangeIterator.IterationStartPositionEnum.TOP_LEFT, isSkipHiddenRows, false, false, true, false, true);
        while(rIterator.hasNext())
        {
            ReadOnlyCell rCell = rIterator.next();
            Cell cell = rCell.getCell();
            if (cell != null && (cell.isFormula() || cell.getValue().getValue() != null))
            {
                return false;
            }
        }

        return true;
    }

    private static boolean isBlankCell(Sheet sheetObj, int row, int col, boolean isSkipHiddenRows)
    {
        return isBlankRange(sheetObj, row, col, row, col, isSkipHiddenRows);
    }


    public static String getRangeContent(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        String prevContent = null;
        if(endRowIndex > sheet.getUsedRowIndex() || endColIndex > sheet.getUsedColumnIndex())
        {
            return null;
        }

        
        for(int row = startRowIndex; row <= endRowIndex; row++)
        {
            for(int col = startColIndex; col <= endColIndex; col++)
            {
                Cell cell =  sheet.getReadOnlyCellFromShell(row, col).getCell();

                if (cell != null)
                {
                    if (row == startRowIndex && col == startColIndex)
                    {
                        prevContent = cell.getContent();
                    }
                    else if(prevContent == null ? cell.getContent() != null : !prevContent.equals(cell.getContent()))
                    {
                        return null;
                    }
                }
                else if(prevContent != null)
                {
                    return null;
                }
            }
        }

        if(prevContent == null)
        {
            prevContent = "";
        }
        return prevContent;
    }

    public static String getRangeFormula(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        String formula = null;
        for(int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++)
        {
            ReadOnlyCell rCell = null;
            for(int colIndex = startColIndex; colIndex <= endColIndex; colIndex++)
            {
                rCell = sheet.getReadOnlyCellFromShell(rowIndex, colIndex);
                Cell cell = rCell.getCell();

                String tempFormula = null;
                if(cell != null && cell.getValue().getType() != Type.UNDEFINED)
                {
                    tempFormula = cell.getLocalizedFormula();
                    // Should not cosider the curly braces for array cells.
                    if(((CellImpl)cell).isArrayCell())
                    {
                        tempFormula = tempFormula.substring(1, tempFormula.length() - 1);
                    }
                }

                if(rowIndex == startRowIndex && colIndex == startColIndex)
                {
                    formula = tempFormula;
                }
                else if (tempFormula == null)
                {
                    if (formula != null)
                    {
                        return null;
                    }
                }
                else if (!tempFormula.equals(formula))
                {
                    return null;
                }

                colIndex += rCell.getColsRepeated() - 1;
            }

            rowIndex += rCell.getRowsRepeated() - 1;
        }

        return formula == null ? "" : formula;
    }

    public static boolean isMember(List<DataRange> dataRanges, String asn, int row, int col) {
        for(DataRange dataRange : dataRanges){
            if(dataRange.isMember(asn, row, col)){
                return true;
            }
        }
        return false;
    }

    public static JSONObjectWrapper getSelectionJson(String associatedSheetName, int startRow, int startCol, int endRow, int endCol)
    {
        JSONObjectWrapper selectionJson = new JSONObjectWrapper();
        selectionJson.put(JSONConstants.SHEETLIST, ActionJsonUtil.addSheetInJsonArray(null, associatedSheetName, true));
        selectionJson.put(JSONConstants.RANGELIST, ActionJsonUtil.addRangeInJsonArray(null, startRow, startCol, endRow, endCol, true));
        return selectionJson;
    }

    public static class SheetCell {
        private final int rowIndex;
        private final int colIndex;

        public SheetCell(int rowIndex, int colIndex) {
            this.rowIndex = rowIndex;
            this.colIndex = colIndex;
        }

        public static SheetCell fromCell(Cell cell) {
            return new SheetCell(cell.getRowIndex(), cell.getColumnIndex());
        }

        public int getRowIndex() {
            return this.rowIndex;
        }

        public int getColIndex() {
            return this.colIndex;
        }

        public Cell toCell(Sheet sheet) {
            return sheet.getCell(this.rowIndex, this.colIndex);
        }

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 67 * hash + this.rowIndex;
            hash = 67 * hash + this.colIndex;
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final SheetCell other = (SheetCell) obj;
            if (this.rowIndex != other.rowIndex) {
                return false;
            }
            if (this.colIndex != other.colIndex) {
                return false;
            }
            return true;
        }
    }

    public static class SheetRow {
        private final int rowIndex;

        public SheetRow(int rowIndex) {
            this.rowIndex = rowIndex;
        }

        public int getRowIndex() {
            return rowIndex;
        }

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 97 * hash + this.rowIndex;
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final SheetRow other = (SheetRow) obj;
            if (this.rowIndex != other.rowIndex) {
                return false;
            }
            return true;
        }
    }

    public static class SheetColumn {
        private final int colIndex;

        public SheetColumn(int colIndex) {
            this.colIndex = colIndex;
        }

        public int getColIndex() {
            return colIndex;
        }

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 97 * hash + this.colIndex;
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final SheetColumn other = (SheetColumn) obj;
            if (this.colIndex != other.colIndex) {
                return false;
            }
            return true;
        }
    }

    public static class SheetRange {
        private final int startRowIndex;
        private final int startColIndex;
        private final int endRowIndex;
        private final int endColIndex;

        public SheetRange(int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) {
            this.startRowIndex = Math.min(startRowIndex, endRowIndex);
            this.startColIndex = Math.min(startColIndex, endColIndex);
            this.endRowIndex = Math.max(startRowIndex, endRowIndex);
            this.endColIndex = Math.max(startColIndex, endColIndex);
        }

        public static SheetRange fromRange(Range range) {
            return new SheetRange(range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
        }

        public static SheetRange fromDataRange(DataRange range) {
            return new SheetRange(range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
        }

        public int getStartRowIndex() {
            return startRowIndex;
        }

        public int getStartColIndex() {
            return startColIndex;
        }

        public int getEndRowIndex() {
            return endRowIndex;
        }

        public int getEndColIndex() {
            return endColIndex;
        }

        public Range toRange(Sheet sheet) {
            return new Range(sheet, this.startRowIndex, this.startColIndex, this.endRowIndex, this.endColIndex);
        }

        public DataRange toDataRange(String asn) {
            return new DataRange(asn, this.startRowIndex, this.startColIndex, this.endRowIndex, this.endColIndex);
        }

        @Override
        public int hashCode() {
            int hash = 3;
            hash = 59 * hash + this.startRowIndex;
            hash = 59 * hash + this.startColIndex;
            hash = 59 * hash + this.endRowIndex;
            hash = 59 * hash + this.endColIndex;
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final SheetRange other = (SheetRange) obj;
            if (this.startRowIndex != other.startRowIndex) {
                return false;
            }
            if (this.startColIndex != other.startColIndex) {
                return false;
            }
            if (this.endRowIndex != other.endRowIndex) {
                return false;
            }
            if (this.endColIndex != other.endColIndex) {
                return false;
            }
            return true;
        }

        @Override
        public String toString() {
            return CellUtil.getCellReference(startColIndex, startRowIndex) + ":" + CellUtil.getCellReference(endColIndex, endRowIndex); //No I18N
        }
    }

    // NOTE :  all cells should be from same sheet.
    public static class MergeCells
    {
        Sheet sheet = null;
        List<Integer> startRowList = new ArrayList<>();
        List<Integer> startColList = new ArrayList<>();
        List<Integer> endRowList = new ArrayList<>();
        List<Integer> endColList = new ArrayList<>();

        //need a small and quick revamp on these 3 constructors
        public MergeCells(Sheet sheet, Collection<SheetRange> sheetRanges) {
            this.sheet = sheet;
            List<SheetRange> sheetRangesList = sheetRanges.stream().sorted((sheetRange0, sheetRange1) -> {
                int i1 = sheetRange0.startRowIndex;
                int i2 = sheetRange1.startRowIndex;

                if (i1 == i2) {
                    i1 = sheetRange0.startColIndex;
                    i2 = sheetRange1.startColIndex;
                }

                return i1 > i2 ? 1 : i1 < i2 ? -1 : 0;
            }).collect(Collectors.toList());

            int count = 0;
            while(count < sheetRangesList.size())
            {
                SheetRange sheetRange = sheetRangesList.get(count);

                int startRow = sheetRange.startRowIndex;
                int startCol = sheetRange.startColIndex;
                int endRow = sheetRange.endRowIndex;
                int endCol = sheetRange.endColIndex;

                // update endCol...
                while(count < sheetRangesList.size() - 1)
                {
                    count += 1;
                    sheetRange = sheetRangesList.get(count);
                    if(sheetRange.startColIndex == endCol + 1 && sheetRange.startRowIndex == startRow)
                    {
                        endCol = sheetRange.endColIndex;
                    }
                    else
                    {
                        count -= 1;
                        break;
                    }
                }
                /////// update endRow...
                boolean isRowMerge = false;
                int mergeIndex = endRowList.indexOf(startRow - 1);
                if(mergeIndex != -1)
                {
                    int tempRowEndIndex = endRowList.lastIndexOf(startRow - 1);
                    for(int r = mergeIndex; r <= tempRowEndIndex; r++)
                    {
                        /*
                            Added another extra check endRowList.get(r)==startRow-1. To fix issue with certain cases like following :
                            When, sRList = [1,3,3,5,6,9] , scList = [0,0,8,2,0,0] , erList = [1,4,9,8,7,9] , ecList = [0,0,8,2,0,0]
                            and new entry (10,0,10,0) is added. It is updating the endRow of 4th index instead of 5th. erList becomes [1,4,9,8,10,9] instead of [1,4,9,8,7,10]
                        */
                        if(startColList.get(r) == startCol && endColList.get(r) == endCol && endRowList.get(r) == startRow-1)
                        {
                            mergeIndex = r;
                            isRowMerge = true;
                            break;
                        }
                    }
                }

                if(isRowMerge)
                {
                    endRowList.set(mergeIndex, endRow);
                }
                else
                {
                    startRowList.add(startRow);
                    startColList.add(startCol);
                    endRowList.add(endRow);
                    endColList.add(endCol);
                }
                count += 1;
            }
        }

        public MergeCells(List<Cell> cells)
        {
            this(cells.get(0).getRow().getSheet(), cells
                .stream()
                .map(cell ->
                    new SheetRange(cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex() + cell.getRow().getRowsRepeated() - 1, cell.getColumnIndex() + cell.getColsRepeated() - 1))
                .collect(Collectors.toList()));
        }

        public List<DataRange> toDataRanges()
        {
            List<DataRange> dataRanges = new ArrayList<>();
            for(int i = 0; i < startRowList.size(); i++)
            {
                dataRanges.add(new DataRange(sheet.getAssociatedName(),
                    startRowList.get(i),
                    startColList.get(i),
                    endRowList.get(i),
                    endColList.get(i)));
            }
            return dataRanges;
        }

        //Note: Range has to be created only on demand. Since Range will create two cell object intren.
        public List<Range> toRanges()
        {
            List<Range> tRanges = new ArrayList<>();
            for(int i = 0; i < startRowList.size(); i++)
            {
                tRanges.add(new Range(sheet,
                    startRowList.get(i),
                    startColList.get(i),
                    endRowList.get(i),
                    endColList.get(i)));
            }
            return tRanges;
        }
    }

    public static int getNthVisibleRowIndex(Sheet sheet, int fromIndex, int n, VisibleIndices visibleRows) {
        if(visibleRows == null) {
            return getNthVisibleRowIndex(sheet, fromIndex, n);
        }
        return visibleRows.getNthVisible(fromIndex, n);
    }

    public static int getNthVisibleRowIndex(Sheet sheet, int fromIndex, int n)
    {
        return RowUtil.getNthVisibleRowIndex(sheet, fromIndex, n);
    }

    public static int getNthUnfilteredRowIndex(Sheet sheet, int fromIndex, int n)
    {
        int nthVisibleRowIndex = n == 0 ? fromIndex : fromIndex + n - 1;
        if(sheet.getUserFilterViews() != null)
        {
            for(Map.Entry<Integer, FilterView> entry : sheet.getUserFilterViews().getFilterViews().entrySet())
            {
                if(!entry.getValue().isEmptyFilterView())
                {
                    DataRange filterRange = entry.getValue().getDataRange();
                    nthVisibleRowIndex = fromIndex;
//            if(fromIndex < filterRange.getStartRowIndex())
//            {
//                nthVisibleRowIndex = filterRange.getStartRowIndex();
//                n -= (filterRange.getStartRowIndex() - fromIndex);
//            }
                    int i, count;
                    for(i = nthVisibleRowIndex, count = 0; count < n && i < Utility.MAXNUMOFROWS && i <= filterRange.getEndRowIndex(); i++)
                    {
                        ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(i);
                        Row row = rRow.getRow();

                        int repeated = rRow.getRowsRepeated();
                        if(!RowUtil.isFilteredRow(row))
                        {
                            count += repeated;

                            if(count > n)
                            {
                                repeated -= (count - n);
                            }

                            nthVisibleRowIndex = rRow.getRowIndex() + repeated - 1;
                        }

                        // To avoid high cpu. Should find the reason for high cpu and fix.
                        if(repeated > 0)
                        {
                            i += repeated - 1;
                        }
                    }

                    if(count < n && i < Utility.MAXNUMOFROWS)
                    { // ==> i>filterRange.getEndRowIndex()
                        nthVisibleRowIndex = i - 1 + (n - count);
                        nthVisibleRowIndex = Math.min(nthVisibleRowIndex, Utility.MAXNUMOFROWS - 1);
                    }
                }
            }
        }

//        if(filterRange == null || fromIndex > filterRange.getEndRowIndex() || nthVisibleRowIndex < filterRange.getStartRowIndex())
//        {
//            return nthVisibleRowIndex;
//        }

        return nthVisibleRowIndex;
    }

    public static int getVisibleRowCount(Sheet sheet, int startIndex, int endIndex, VisibleIndices visibleRows)
    {
        if(startIndex > endIndex)
        {
            int temp = startIndex;
            startIndex = endIndex;
            endIndex = temp;
        }
        return visibleRows == null ? getVisibleRowCount(sheet, startIndex, endIndex) : visibleRows.getVisibleIndicesCount(startIndex, endIndex);
    }

    public static int getNthVisibleColumnIndex(Sheet sheet, int fromIndex, int n, VisibleIndices visibleColumns)
    {
        return visibleColumns.getNthVisible(fromIndex, n);
    }

    public static int getVisibleColumnCount(Sheet sheet, int startIndex, int endIndex, VisibleIndices visibleColumns)
    {
        if(startIndex > endIndex)
        {
            int temp = startIndex;
            startIndex = endIndex;
            endIndex = temp;
        }
        return visibleColumns == null ? getVisibleColumnCount(sheet, startIndex, endIndex) : visibleColumns.getVisibleIndicesCount(startIndex, endIndex);
    }

    public static int getVisibleColumnCount(Sheet sheet, int startColumnIndex, int endColumnIndex)
    {
        int visibleColsCount = 0;
        for(int columnIndex = startColumnIndex; columnIndex <= endColumnIndex; columnIndex++)
        {
            ReadOnlyColumnHeader readOnlyColumnHeader = RangeIterator.getReadOnlyColumnHeaderAtSheet(sheet, columnIndex);
            ColumnHeader columnHeader = readOnlyColumnHeader.getColumnHeader();
            if(ColumnUtil.isVisibleColumn(columnHeader))
            {
                visibleColsCount += Math.min(endColumnIndex - (columnIndex - 1), readOnlyColumnHeader.getColsRepeated());
            }
            columnIndex += readOnlyColumnHeader.getColsRepeated() - 1;
        }

        return visibleColsCount;
    }


    public static int getVisibleRowCount(Sheet sheet, int startIndex, int endIndex)
    {
        if(startIndex > endIndex)
        {
            int temp = startIndex;
            startIndex = endIndex;
            endIndex = temp;
        }
        return RowUtil.getVisibleRowsCount(sheet, startIndex, endIndex);
    }

    public static boolean isRangeContainsConditionalFormat(Range inRange)
    {
        if(inRange != null)
        {
            Sheet inRangeSheet = inRange.getSheet();
            if(!inRangeSheet.getConditionalStyleMap().isEmpty())
            {
                if(inRange.getStartRowIndex() == 0 && inRange.getStartColIndex() == 0
                    && inRange.getEndRowIndex() == (Math.min(Utility.MAXNUMOFROWS, inRangeSheet.getRowNum()) - 1)
                    && inRange.getEndColIndex() == (Math.min(Utility.MAXNUMOFCOLS, inRangeSheet.getColNum()) - 1))
                {
                    return true;
                }

                for(ConditionalStyleObject csf : inRangeSheet.getConditionalStyleMap().values())
                {
                    for(DataRange cRange : csf.getSpecialRange().getRanges())
                    {
                        DataRange dRange_1 = inRange.toDataRange();
                        if(RangeUtil.intersection(dRange_1, cRange) != null)
                        {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    public static List<Range> mergeRanges(Collection<Range> ranges) {
        List<Range> mergedRanges = new ArrayList<>();
        for(Range range: ranges) {
            RangeUtil.mergeAndAddRangeToList(range, mergedRanges);
        }
        return mergedRanges;
    }

    public static List<DataRange> mergeDataRanges(Collection<DataRange> ranges) {
        List<DataRange> mergedRanges = new ArrayList();
        for(DataRange range: ranges) {
            RangeUtil.mergeAndAddDataRangeToList(range, mergedRanges);
        }

        return mergedRanges;
    }


    // this method is to split the over laping ranges and then merge it
    public static List<DataRange> splitAndMergeDataRanges(Collection<DataRange> dataRanges) {
        List<DataRange> splitDataRanges = new ArrayList<>();
        for(DataRange dataRange : dataRanges)
        {
            splitDataRanges = splitDataRanges(splitDataRanges,dataRange);
        }
        List<DataRange> mergedDataRanges = new ArrayList<>();
        for(DataRange dataRange: splitDataRanges)
        {
            mergeAndAddDataRangeToList(dataRange, mergedDataRanges);
        }
        return mergedDataRanges;
    }

    public static List<DataRange> splitDataRanges(Collection<DataRange> dataRanges, DataRange inDataRange)
    {
        List<DataRange> newDRangeList = new ArrayList<>();
        for (DataRange dataRange : dataRanges)
        {
            if(RangeUtil.intersection(inDataRange, dataRange)  != null)
            {
                int startRowIndex = dataRange.getStartRowIndex();
                int startColIndex = dataRange.getStartColIndex();
                int endRowIndex = dataRange.getEndRowIndex();
                int endColIndex = dataRange.getEndColIndex();
                if(startRowIndex < inDataRange.getStartRowIndex())
                {
                    DataRange newDRange = new DataRange(dataRange.getAssociatedSheetName(),
                        startRowIndex,
                        startColIndex,
                        inDataRange.getStartRowIndex() - 1,
                        endColIndex);
                    newDRangeList.add(newDRange);
                    startRowIndex = inDataRange.getStartRowIndex();
                }
                if(startColIndex < inDataRange.getStartColIndex())
                {
                    DataRange newDRange = new DataRange(dataRange.getAssociatedSheetName(),
                        startRowIndex,
                        startColIndex,
                        endRowIndex,
                        inDataRange.getStartColIndex() - 1);
                    newDRangeList.add(newDRange);
                    startColIndex = inDataRange.getStartColIndex();
                }
                if(endRowIndex > inDataRange.getEndRowIndex())
                {
                    DataRange newDRange = new DataRange(dataRange.getAssociatedSheetName(),
                        inDataRange.getEndRowIndex() + 1,
                        startColIndex,
                        endRowIndex,
                        endColIndex);
                    newDRangeList.add(newDRange);
                    endRowIndex = inDataRange.getEndRowIndex();
                }
                if(endColIndex > inDataRange.getEndColIndex())
                {
                    DataRange newDRange = new DataRange(dataRange.getAssociatedSheetName(),
                        startRowIndex,
                        inDataRange.getEndColIndex() + 1,
                        endRowIndex,
                        endColIndex);
                    newDRangeList.add(newDRange);
                }

            }
            else
            {
                newDRangeList.add(dataRange);
            }
        }
        newDRangeList.add(inDataRange);
        return newDRangeList;
    }

    public static List<DataRange> getIntersectingConditionalFormatRanges(Range range) {

        List<DataRange> rangeList = new ArrayList<>();
        if(range != null) {
            Sheet sheet = range.getSheet();
            if(!sheet.getConditionalStyleMap().isEmpty()) {
                for(ConditionalStyleObject cso : sheet.getConditionalStyleMap().values()) {
                    for(DataRange csRange : cso.getSpecialRange().getRanges()) {
                        DataRange dRange_1 = range.toDataRange();
                        if(RangeUtil.intersection(dRange_1, csRange) != null) {
                            mergeAndAddDataRangeToList(csRange, rangeList);
                        }
                    }
                }
            }
        }
        return rangeList;
    }

    public static boolean mergeAndAddRangeToList(Range inRange, List<Range> rangeList)
    {
        Sheet inSheet = inRange.getSheet();
        int inStartRow = inRange.getStartRowIndex();
        int inStartCol = inRange.getStartColIndex();
        int inEndRow = inRange.getEndRowIndex();
        int inEndCol = inRange.getEndColIndex();

//        boolean isProtectedRange = (inRange instanceof ProtectedRange);
        Range tempRange;

        int size = rangeList.size();
        for(int i = 0; i < size; i++)
        {
            Range loopRange = rangeList.get(i);
            Sheet loopSheet = loopRange.getSheet();

// For protected Ranges, try to merge only the ranges that has same authorization.
//            if(isProtectedRange && !((ProtectedRange)loopRange).getPermissionMap().equals(((ProtectedRange)inRange).getPermissionMap()))
//            {
//                continue;
//            }
            //////////////////////////////////////////////////////////////////////////

            int loopStartRow = loopRange.getStartRowIndex();
            int loopStartCol = loopRange.getStartColIndex();
            int loopEndRow = loopRange.getEndRowIndex();
            int loopEndCol = loopRange.getEndColIndex();

            // inRange is subRange of loopRange.
            if(loopRange.isMember(inSheet, inStartRow, inStartCol) && loopRange.isMember(inSheet, inEndRow, inEndCol))
            {
                return true;
            }
            // inRange is superRange of loopRange.
            else if(inRange.isMember(loopSheet, loopStartRow, loopStartCol) && inRange.isMember(loopSheet, loopEndRow, loopEndCol))
            {
                rangeList.remove(i);
                i -= 1;
                size = rangeList.size();
            }
            else
            {
                // Merge the range rows if start and end col of the ranges are equal and the rows of the ranges are continuous
                if(inStartCol == loopStartCol && inEndCol == loopEndCol)
                {
                    if(inEndRow >= loopStartRow - 1 && inEndRow <= loopEndRow)
                    {
                        rangeList.remove(i);
                        tempRange = new Range(inRange.getSheet(), inStartRow, inStartCol, loopEndRow, inEndCol);
//                        if(isProtectedRange)
//                        {
//                            inRange = new ProtectedRange(tempRange, ((ProtectedRange)inRange).getPermissionMap());
//                        }else{
                        inRange = tempRange;
//                        }
                        mergeAndAddRangeToList(inRange, rangeList);
                        return true;
                    }
                    else if(inStartRow <= loopEndRow + 1 && inStartRow >= loopStartRow)
                    {
                        rangeList.remove(i);
                        tempRange = new Range(inRange.getSheet(), loopStartRow, inStartCol, inEndRow, inEndCol);
//                        if(isProtectedRange)
//                        {
//                            inRange = new ProtectedRange(tempRange, ((ProtectedRange)inRange).getPermissionMap());
//                        }else{
                        inRange = tempRange;
//                        }
                        mergeAndAddRangeToList(inRange, rangeList);
                        return true;
                    }
                }
                // Merge the range cols if start and end row of the ranges are equal and the cols of the ranges are continuous
                else if(inStartRow == loopStartRow && inEndRow == loopEndRow)
                {
                    if(inEndCol >= loopStartCol - 1 && inEndCol <= loopEndCol)
                    {
                        rangeList.remove(i);
                        tempRange = new Range(inRange.getSheet(), inStartRow, inStartCol, inEndRow, loopEndCol);
//                        if(isProtectedRange)
//                        {
//                            inRange = new ProtectedRange(tempRange, ((ProtectedRange)inRange).getPermissionMap());
//                        }else{
                        inRange = tempRange;
//                        }
                        mergeAndAddRangeToList(inRange, rangeList);
                        return true;
                    }
                    else if(inStartCol <= loopEndCol + 1 && inStartCol >= loopStartCol)
                    {
                        rangeList.remove(i);
                        tempRange = new Range(inRange.getSheet(), inStartRow, loopStartCol, inEndRow, inEndCol);
//                        if(isProtectedRange)
//                        {
//                            inRange = new ProtectedRange(tempRange, ((ProtectedRange)inRange).getPermissionMap());
//                        }else{
                        inRange = tempRange;
//                        }
                        mergeAndAddRangeToList(inRange, rangeList);
                        return true;
                    }
                }
            }
        }

        rangeList.add(inRange);
        return false;
    }

    public static boolean mergeAndAddDataRangeToList(DataRange inDataRange, List<DataRange> dataRangeList)
    {
        String inSheetAssociateName = inDataRange.getAssociatedSheetName();
        int inStartRow = inDataRange.getStartRowIndex();
        int inStartCol = inDataRange.getStartColIndex();
        int inEndRow = inDataRange.getEndRowIndex();
        int inEndCol = inDataRange.getEndColIndex();

        DataRange tempDataRange;

        int size = dataRangeList.size();
        for(int i = 0; i < size; i++)
        {
            DataRange loopDataRange = dataRangeList.get(i);
            String loopSheetAssociateName = loopDataRange.getAssociatedSheetName();

            int loopStartRow = loopDataRange.getStartRowIndex();
            int loopStartCol = loopDataRange.getStartColIndex();
            int loopEndRow = loopDataRange.getEndRowIndex();
            int loopEndCol = loopDataRange.getEndColIndex();

            // inRange is subRange of loopRange.
            if( loopDataRange.isMember(inSheetAssociateName, inStartRow,  inStartCol) && loopDataRange.isMember(inSheetAssociateName, inEndRow,  inEndCol))
            {
                return true;
            }
            // inRange is superRange of loopRange.
            else if(inDataRange.isMember(loopSheetAssociateName, loopStartRow,  loopStartCol) && inDataRange.isMember(loopSheetAssociateName, loopEndRow,  loopEndCol))
            {
                dataRangeList.remove(i);
                i -= 1;
                size = dataRangeList.size();
            }
            else
            {
                // Merge the range rows if start and end col of the ranges are equal and the rows of the ranges are continuous
                if(inStartCol == loopStartCol && inEndCol == loopEndCol)
                {
                    if(inEndRow >= loopStartRow - 1 && inEndRow <= loopEndRow)
                    {
                        dataRangeList.remove(i);
                        tempDataRange = new DataRange(inDataRange.getAssociatedSheetName(), inStartRow, inStartCol, loopEndRow, inEndCol);
                        inDataRange = tempDataRange;
                        mergeAndAddDataRangeToList(inDataRange, dataRangeList);
                        return true;
                    }
                    else if(inStartRow <= loopEndRow + 1 && inStartRow >= loopStartRow)
                    {
                        dataRangeList.remove(i);
                        tempDataRange = new DataRange(inDataRange.getAssociatedSheetName(), loopStartRow, inStartCol, inEndRow, inEndCol);
                        inDataRange = tempDataRange;
                        mergeAndAddDataRangeToList(inDataRange, dataRangeList);
                        return true;
                    }
                }
                // Merge the range cols if start and end row of the ranges are equal and the cols of the ranges are continuous
                else if(inStartRow == loopStartRow && inEndRow == loopEndRow)
                {
                    if(inEndCol >= loopStartCol - 1 && inEndCol <= loopEndCol)
                    {
                        dataRangeList.remove(i);
                        tempDataRange = new DataRange(inDataRange.getAssociatedSheetName(), inStartRow, inStartCol, inEndRow, loopEndCol);
                        inDataRange = tempDataRange;
                        mergeAndAddDataRangeToList(inDataRange, dataRangeList);
                        return true;
                    }
                    else if(inStartCol <= loopEndCol + 1 && inStartCol >= loopStartCol)
                    {
                        dataRangeList.remove(i);
                        tempDataRange = new DataRange(inDataRange.getAssociatedSheetName(), inStartRow, loopStartCol, inEndRow, inEndCol);
                        inDataRange = tempDataRange;
                        mergeAndAddDataRangeToList(inDataRange, dataRangeList);
                        return true;
                    }
                }
            }
        }
        dataRangeList.add(inDataRange);
        return false;
    }

    public static List<Integer> getUnHiddenRowIndices(Sheet sheet, int startRow, int endRow)
    {
        Iterator<ReadOnlyRow> rows = RowUtil.getUnHiddenRows(sheet, startRow, endRow);
        List<Integer> list = new ArrayList<>();
        while(rows.hasNext())
        {
            ReadOnlyRow row = rows.next();
            int rowsRepeated = row.getRowsRepeated();
            int repeatEnd = Math.min((row.getRowIndex() + rowsRepeated), (endRow + 1));
            for(int k = row.getRowIndex(); k < repeatEnd; k++)
            {
                list.add(k);
            }
        }
        return list;
    }

    public static List<Integer> getVisibleRowIndices(Sheet sheet, int startRow, int endRow)
    {

        Iterator<ReadOnlyRow> rows = RowUtil.getVisibleRows(sheet, startRow, endRow);
        List<Integer> list = new ArrayList<>();
        while(rows.hasNext())
        {
            ReadOnlyRow row = rows.next();
            int rowsRepeated = row.getRowsRepeated();
            int repeatEnd = Math.min((row.getRowIndex() + rowsRepeated), (endRow + 1));
            for(int k = row.getRowIndex(); k < repeatEnd; k++)
            {
                list.add(k);
            }
        }
        return list;
    }

    public static Protection getProtection(Sheet sheet,int startRow, int startCol, int endRow, int endCol){

        Map<Protection, List<DataRange>> protectionRangeMap = sheet.getProtectionRangeMap();
        for(Protection protection : protectionRangeMap.keySet())
        {
            List<DataRange> ranges = protectionRangeMap.get(protection);
            for(DataRange range : ranges)
            {
                if(startRow >= range.getStartRowIndex() && startCol >= range.getStartColIndex() &&
                    endRow <= range.getEndRowIndex() &&  endCol <= range.getEndColIndex())
                {
                    return protection;
                }

            }
        }
        return null;
    }
    public static Map getProtections(Sheet sheet,int startRow, int startCol, int endRow, int endCol)
    {
        DataRange inRange = new DataRange(sheet.getAssociatedName(), startRow, startCol, endRow, endCol);
        Map<Protection, List<DataRange>> protectionRangeMap = sheet.getProtectionRangeMap();
        Map<Range,Protection> protectionMap =  new HashMap<>();
        for(Protection protection : protectionRangeMap.keySet())
        {
            List<DataRange> ranges = protectionRangeMap.get(protection);
            for(DataRange range : ranges)
            {
                DataRange intRange = RangeUtil.intersection(range, inRange);
                if(intRange != null)
                {
                    protectionMap.put(new Range(sheet,intRange.getStartRowIndex(),intRange.getStartColIndex(),intRange.getEndRowIndex(),intRange.getEndColIndex()), protection);
                }

            }
        }
        return protectionMap;
    }

    public static boolean isAnyRangeLockedInSheet(Sheet sheet,String zuid, long exSharedLinkId)
    {

        boolean isExternalShare = ( exSharedLinkId == 0l ) ? false : true;

        Map<Protection,List<DataRange>> protectionRangeMap=sheet.getProtectionRangeMap();

        for(Protection p:protectionRangeMap.keySet())
        {
            boolean isProtected = isExternalShare ? p.isAuthorizedExternalShareLink(exSharedLinkId) : p.isAuthorized(zuid, false);

            if(!isProtected)
            {
                return true;
            }
        }


        return false;
    }
    public static boolean isRangeHasLockedRange(Sheet sheet, DataRange inDataRange, String zuId, int action){
        return isRangeHasLockedRange(sheet, inDataRange, zuId, action, 0L);
    }

    public static boolean isRangeHasLockedRange(Sheet sheet, DataRange inDataRange, String zuId, int action, long exSharedLinkId){
        sheet.getWorkbook().checkForRestriction(inDataRange);
        SpreadsheetSettings spreadsheetSettings = sheet.getWorkbook().getSpreadsheetSettings();
        if(sheet == null)
        {
            return false;
        }
        boolean isSheetLocked = sheet.isLocked(zuId, exSharedLinkId);
        if(isSheetLocked){
            return true;
        }
        Map<Protection, List<DataRange>> protectionRangeMap = sheet.getProtectionRangeMap();

        DataRange extendedRange = null;
        if(action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.TABLE_INSERT_COL || action == ActionConstants.TABLE_DELETE_COL)
        {
            extendedRange = new DataRange(inDataRange.getAssociatedSheetName(), inDataRange.getStartRowIndex(), inDataRange.getStartColIndex(), inDataRange.getEndRowIndex(), Utility.MAXNUMOFCOLS - 1);

        }else if(action == ActionConstants.DELETE_CELL_BOTTOM || action == ActionConstants.INSERT_CELL_TOP || action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.TABLE_DELETE_ROW)
        {
            extendedRange = new DataRange(inDataRange.getAssociatedSheetName(), inDataRange.getStartRowIndex(), inDataRange.getStartColIndex(), Utility.MAXNUMOFROWS - 1, inDataRange.getEndColIndex());
        }
        for(Protection protection : protectionRangeMap.keySet())
        {
            List<DataRange> ranges = protectionRangeMap.get(protection);
            for(DataRange range : ranges)
            {
                boolean isAuthorized = false;
                if(exSharedLinkId != 0l) {
                    isAuthorized = protection.isAuthorizedExternalShareLink(exSharedLinkId);
                } else {
                    isAuthorized = protection.isAuthorized(zuId, false);
                }
                if((action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.DELETE_CELL_BOTTOM
                        || action == ActionConstants.TABLE_DELETE_ROW || action == ActionConstants.TABLE_DELETE_COL
                        || action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.INSERT_CELL_TOP
                        || action == ActionConstants.TABLE_INSERT_ROW || action == ActionConstants.TABLE_INSERT_COL) &&
                        RangeUtil.isContainsRange(range, inDataRange) && !isAuthorized)
                {
                    return true;
                }
//                if(action == ActionConstants.INSERT_CELL_LEFT || action == ActionConstants.INSERT_CELL_TOP ||
//                        action == ActionConstants.DELETE_CELL_RIGHT || action == ActionConstants.DELETE_CELL_BOTTOM)
                if(extendedRange != null)
                {
                    if(RangeUtil.isPartOfRange(extendedRange, range))
                    {
                        return true;
                    }
                }
                else if(RangeUtil.intersection(inDataRange, range) != null)
                {
                    if(((action == ActionConstants.INSERT_ROW || action == ActionConstants.INSERT_CUT_ROW || action == ActionConstants.INSERT_COPY_ROW) && inDataRange.getStartRowIndex() <= range.getStartRowIndex()) ||
                        ((action == ActionConstants.INSERT_COL || action == ActionConstants.INSERT_CUT_COLUMN || action == ActionConstants.INSERT_COPY_COLUMN)&& inDataRange.getStartColIndex() <= range.getStartColIndex()))
                    {
//                        return false;
                        continue;
                    }
                    if(protection.isAllowInsert() && (action == ActionConstants.INSERT_ROW || action == ActionConstants.INSERT_COL))
                    {
                        return false;
                    }
                    if(protection.isAllowFormats() && ActionConstants.isFormatAction(action))
                    {
                        return false;
                    }
                    // ALLOW SORT IF COMPLETE LOCKED RANGE IS INSIDE THE SORT RANGE.
                    if(spreadsheetSettings.isAllowSortOnLockedRanges() && action == ActionConstants.SORT)
                    {
                        DataRange dataRange = RangeUtil.intersection(inDataRange, range);
                        if(dataRange.equals(inDataRange)) {
                            break;
                        }
                        boolean isColLevel = dataRange.getRowSize() == inDataRange.getRowSize();
                        boolean isRowLevel = dataRange.getRowSize() == 1 && dataRange.equals(range);
                        if(isColLevel || isRowLevel)
                        {
                            continue;
                        }
                    }
                    if(!isAuthorized){
                        return true;
                    }
                }

            }
        }
        return false;
    }

    public static boolean isSheetLockedWithRanges(Sheet sheet, DataRange dataRange, String zuid, long exSharedLinkId)
    {
        boolean isExternalShare = exSharedLinkId != 0L;
        Protection protection = RangeUtil.getProtection(sheet, dataRange.getStartRowIndex(), dataRange.getStartColIndex(), dataRange.getEndRowIndex(), dataRange.getEndColIndex());
        if(protection != null)
        {
            boolean isAuthorized = isExternalShare ? protection.isAuthorizedExternalShareLink(exSharedLinkId) : protection.isAuthorized(zuid, false);
            if(!isAuthorized)
            {
                return true;
            }
        }
        return false;
    }

    public static Boolean isAnyRangeHasLockedCells(Sheet sheet, JSONObjectWrapper actionJson, String zuid, List<Range> ranges)
    {
        for(Range range : ranges)
        {
            if(isRangeHasLockedCells(sheet, actionJson, getJSONRange(range), sheet.getAssociatedName(), zuid))
            {
                return true;
            }
        }
        return false;
    }

    public static boolean isRangeHasLockedCells(Sheet sheet, JSONObjectWrapper actionJson, String zuId)
    {
        if(sheet == null)
        {
            return false;
        }
        List<Range> listOfRangeInSheet = ActionJsonUtil.getRangesinASheetFromJsonObject(sheet, actionJson, false);
        if(listOfRangeInSheet != null)
        {
            return isAnyRangeHasLockedCells(sheet, actionJson, zuId, listOfRangeInSheet);
        }
        return false;
    }

    public static boolean isSheetHasLockedCells(Sheet sheet, String zuid)
    {
        return sheet.getProtectionRangeMap().keySet().stream().anyMatch(protection -> !protection.isAuthorized(zuid, false));
    }

    public static boolean isHiddenDataLocked(Workbook workbook, boolean isReadOnlyWorkbook, String zuid) {
        long timeStamp = System.currentTimeMillis();
        boolean hasHideAndLock = false;
        for(Sheet sheet : workbook.getSheetList()) {
            if(isHiddenDataLocked(sheet, new DataRange(sheet.getAssociatedName(), 0,0, Utility.MAXNUMOFROWS-1, Utility.MAXNUMOFCOLS-1), isReadOnlyWorkbook, zuid)) {
                hasHideAndLock = true;
                break;
            }
        }
        long timeStamp2 = System.currentTimeMillis();
        if(timeStamp2 - timeStamp > 2000) {
            LOGGER.log(Level.INFO, "[HideAndLock slowness] Time taken to check isHiddenDataLocked: {0} ms", (timeStamp2 - timeStamp));
        }
        return hasHideAndLock;
    }

    public static boolean isHiddenDataLocked(Sheet sheet, DataRange dataRange, boolean isReadOnlyWorkbook, String zuid) {
        if((isReadOnlyWorkbook || sheet.isLocked(zuid)) && sheet.isHidden()) {
            return true;
        }

        IntegralSet lockedRows = new TreeBasedIntegralSet();
        IntegralSet lockedColumns = new TreeBasedIntegralSet();
        if(isReadOnlyWorkbook || sheet.isLocked(zuid)) {
            lockedRows.add(dataRange.getStartRowIndex(), dataRange.getRowSize());
            lockedColumns.add(dataRange.getStartColIndex(), dataRange.getColSize());
        } else {
            sheet.getProtectionRangeMap().entrySet().stream()
                    .filter(entry -> !entry.getKey().isAuthorized(zuid, false))
                    .map(Map.Entry::getValue)
                    .flatMap(Collection::stream)
                    .filter(range -> RangeUtil.intersection(range, dataRange) != null)
                    .forEach(range -> {
                        DataRange intersection = RangeUtil.intersection(range, dataRange);
                        lockedRows.add(intersection.getStartRowIndex(), intersection.getRowSize());
                        lockedColumns.add(intersection.getStartColIndex(), intersection.getColSize());
                    });
        }

        for(LinearIntegralRange rowLIR: lockedRows.toLinearIntegralRangeList()) {
            if(hasHiddenRowsIn(sheet, rowLIR.getStartInt(), rowLIR.getEndInt())) {
                return true;
            }
        }

        for(LinearIntegralRange colLIR: lockedColumns.toLinearIntegralRangeList()) {
            if(hasHiddenColumnsIn(sheet, colLIR.getStartInt(), colLIR.getEndInt())) {
                return true;
            }
        }
        return false;
    }

    private static boolean hasHiddenRowsIn(Sheet sheet, int startRowIndex, int endRowIndex) {
        return RowUtil.getHiddenRows(sheet, startRowIndex, endRowIndex).hasNext();
    }

    private static boolean hasHiddenColumnsIn(Sheet sheet, int startColIndex, int endColIndex)
    {
        int currColIndex = startColIndex;
        while(currColIndex <= endColIndex)
        {
            ReadOnlyColumnHeader roColHeader = RangeIterator.getReadOnlyColumnHeaderAtSheet(sheet, currColIndex);
            ColumnHeader colHeader = roColHeader.getColumnHeader();
            if(ColumnUtil.isHiddenColumn(colHeader)) {
                return true;
            }
            currColIndex += roColHeader.getColsRepeated();
        }
        return false;
    }

    public static JSONObjectWrapper getJSONRange(Range range)
    {
        if(range == null)
        {
            return null;
        }
        JSONObjectWrapper jsonRange = new JSONObjectWrapper();
        jsonRange.put(JSONConstants.START_ROW, range.getStartRowIndex());
        jsonRange.put(JSONConstants.START_COLUMN, range.getStartColIndex());
        jsonRange.put(JSONConstants.END_ROW, range.getEndRowIndex());
        jsonRange.put(JSONConstants.END_COLUMN, range.getEndColIndex());
        return jsonRange;
    }

    public static boolean isRangeHasLockedCells(Sheet sheet,JSONObjectWrapper actionJson,JSONObjectWrapper actionRange,String asn,String  zuId)
    {
        Long exSharedLinkId = actionJson.optLong(JSONConstants.EXTERNAL_SHARE_LINK_ID);
        boolean isLocked = sheet.isLocked(zuId, exSharedLinkId);

        if(isLocked && actionRange != null && actionRange.has(JSONConstants.START_ROW) )
        {  //added start_row check to avoid document_setting case
            return true;
        }
        int action = actionJson.getInt(JSONConstants.ACTION);
        DataRange tempRange = null;
        switch(action)
        {
            case ActionConstants.FILLSERIES:
                String fillDirection = actionJson.getString("fd");
                int srcCount = actionJson.getInt("vc"); //NO I18N
                int startRow = actionRange.getInt(JSONConstants.START_ROW);
                int startCol = actionRange.getInt(JSONConstants.START_COLUMN);
                int endRow = actionRange.getInt(JSONConstants.END_ROW);
                int endCol = actionRange.getInt(JSONConstants.END_COLUMN);
                if(fillDirection != null)
                {
                    switch (fillDirection)
                    {
                        case "TO_BOTTOM":
                            startRow +=srcCount;
                            break;
                        case "TO_TOP":
                            endRow -=srcCount;
                            break;
                        case "TO_RIGHT":
                            startCol +=srcCount;
                            break;
                        case "TO_LEFT":
                            endCol -=srcCount;
                            break;
                    }
                }
                tempRange =  new DataRange(asn, startRow, startCol, endRow, endCol);
                return isRangeHasLockedRange(sheet,tempRange, zuId, action, exSharedLinkId);
            case ActionConstants.SORT:
                tempRange =  new DataRange(asn, actionRange.getInt(JSONConstants.START_ROW), actionRange.getInt(JSONConstants.START_COLUMN), actionRange.getInt(JSONConstants.END_ROW), actionRange.getInt(JSONConstants.END_COLUMN));
                if(actionJson.getBoolean("isc") == true) // for now, We're not allow left-right(column) sort with locked Ranges
                {
                    return isRangeHasLockedRange(sheet, tempRange, zuId, -1, exSharedLinkId);
                }
                else
                {
                    return isRangeHasLockedRange(sheet, tempRange, zuId, action, exSharedLinkId);
                }
            case ActionConstants.UPDATE_ROW_DATA:
                startRow = actionRange!= null && !actionRange.isEmpty() ? actionRange.getInt(JSONConstants.START_ROW) : sheet.getUsedRowIndex()+1;
                tempRange =  new DataRange(sheet.getAssociatedName(), startRow, 0, startRow, Utility.MAXNUMOFCOLS - 1);
                return isRangeHasLockedRange(sheet, tempRange, zuId, action, exSharedLinkId);
            case ActionConstants.COMPUTE_SUM:
                break;  //will be checked in preprocessor itself.

            case ActionConstants.COMPUTE_SUM_C:
                tempRange =  new DataRange(asn, actionJson.getInt("r"), actionJson.getInt("c"), actionJson.getInt("r"), actionJson.getInt("c")); // No I18N
                return isRangeHasLockedRange(sheet,tempRange, zuId, action, exSharedLinkId);

            case ActionConstants.TABLE_TOGGLE_HEADER_FOOTER:
                JSONArrayWrapper propArray = actionJson.getJSONArray(JSONConstants.TABLE_PROPERTIES);
                tempRange =  new DataRange(asn, actionRange.getInt(JSONConstants.START_ROW), actionRange.getInt(JSONConstants.START_COLUMN), actionRange.getInt(JSONConstants.END_ROW), actionRange.getInt(JSONConstants.END_COLUMN));
                Table table = TableUtil.getTableAtRange(sheet.getWorkbook(), tempRange, false);

                if(table != null) {
                    /* If header or footer is toggled, then it is a value change action, or else it can be treated as a style change action. */
                    if ((!table.isHeaderRowShown() && propArray.getInt(0) == 1) || (table.isHeaderRowShown() && propArray.getInt(0) == 0)
                            || (!table.isFooterRowShown() && propArray.getInt(1) == 1) || (table.isFooterRowShown() && propArray.getInt(1) == 0)) {
                        return isRangeHasLockedRange(sheet,table.getAsDataRange(), zuId, action, exSharedLinkId);
                    }
                    else {
                        return isRangeHasLockedRange(sheet,table.getAsDataRange(), zuId, ActionConstants.TABLE_STYLE_CHANGE, exSharedLinkId);
                    }
                }
                else {
                    return false;
                }
            case ActionConstants.TABLE_PROPERTIES_CHANGE:
            case ActionConstants.TABLE_STYLE_CHANGE:
                tempRange =  new DataRange(asn, actionRange.getInt(JSONConstants.START_ROW), actionRange.getInt(JSONConstants.START_COLUMN), actionRange.getInt(JSONConstants.END_ROW), actionRange.getInt(JSONConstants.END_COLUMN));
                table = TableUtil.getTableAtRange(sheet.getWorkbook(), tempRange, false);
                if(table != null) {
                    return isRangeHasLockedRange(sheet, table.getAsDataRange(), zuId, action, exSharedLinkId);
                }
                break;
            case ActionConstants.TABLE_RESIZE:
                tempRange =  new DataRange(asn, actionRange.getInt(JSONConstants.START_ROW), actionRange.getInt(JSONConstants.START_COLUMN), actionRange.getInt(JSONConstants.END_ROW), actionRange.getInt(JSONConstants.END_COLUMN));
                table = TableUtil.getTableAtRange(sheet.getWorkbook(), tempRange, false);
                if(table != null) {
                    DataRange tableRange = table.getTableDataRange();
                    int newDataEndIndex = table.isFooterRowShown() ? tempRange.getEndRowIndex() -1 : tempRange.getEndRowIndex();
                    DataRange bottomExpansion = newDataEndIndex == tableRange.getEndRowIndex() ? null : new DataRange(asn, Math.min(table.getEndRowIndex()+1, newDataEndIndex+1), tableRange.getStartColIndex(), Math.max(tempRange.getEndRowIndex(), tableRange.getEndRowIndex()) , tableRange.getEndColIndex());
                    DataRange rightExpansion = tempRange.getEndColIndex() == tableRange.getEndColIndex() ? null : new DataRange(asn, table.getStartRowIndex(), Math.min(tableRange.getEndColIndex()+1, tempRange.getEndColIndex()+1), table.getEndRowIndex(), Math.max(tempRange.getEndColIndex(), tableRange.getEndColIndex()));


                    return (bottomExpansion != null && isRangeHasLockedRange(sheet,bottomExpansion, zuId, action, exSharedLinkId)) || (rightExpansion != null && isRangeHasLockedRange(sheet,rightExpansion, zuId, action, exSharedLinkId));
                }
                break;

//            case ActionConstants.DELETE_CELL_RIGHT:
//                tempRange = new DataRange(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), actionJson.getInt(JSONConstants.START_ROW), actionJson.getInt(JSONConstants.START_COLUMN), actionJson.getInt(JSONConstants.END_ROW), actionJson.getInt(JSONConstants.END_COLUMN)); // No I18N
//                return isRangeHasLockedRange(sheet,tempRange, zuId, action);
//
//            case ActionConstants.INSERT_CELL_LEFT:
//
//                tempRange = new DataRange(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), actionJson.getInt(JSONConstants.START_ROW), actionJson.getInt(JSONConstants.START_COLUMN), actionJson.getInt(JSONConstants.END_ROW), Utility.MAXNUMOFCOLS - 1); // No I18N
//                return isRangeHasLockedRange(sheet,tempRange, zuId, action);
//
//            case ActionConstants.INSERT_CELL_TOP:
//                tempRange = new DataRange(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), actionJson.getInt(JSONConstants.START_ROW), actionJson.getInt(JSONConstants.START_COLUMN), Utility.MAXNUMOFROWS - 1, actionJson.getInt(JSONConstants.END_COLUMN)); // No I18N
//                return isRangeHasLockedRange(sheet,tempRange, zuId, action);
//
//            case ActionConstants.DELETE_CELL_BOTTOM:
//                tempRange = new DataRange(actionJson.getString(JSONConstants.ASSOCIATED_SHEET_NAME), actionJson.getInt(JSONConstants.START_ROW), actionJson.getInt(JSONConstants.START_COLUMN), actionJson.getInt(JSONConstants.END_ROW), actionJson.getInt(JSONConstants.END_COLUMN)); // No I18N
//                return isRangeHasLockedRange(sheet,tempRange, zuId, action);

            case ActionConstants.CHANGE_PIVOT_SUBTOTALOPTION:
            case ActionConstants.PIVOT_AUTO_EXPAND:
            case ActionConstants.PIVOT_AUTO_REFRESH:
            case ActionConstants.CHANGE_PIVOT_GRANDTOTALROWOPTION:
            case ActionConstants.CHANGE_PIVOT_GRANDTOTALCOLOPTION:
            case ActionConstants.PIVOT_HIDE_ERRORS:
            case ActionConstants.CHANGE_PIVOT_THEME:
            case ActionConstants.PIVOT_REPEAT_LABEL:
                PivotTable pivotTable = sheet.getWorkbook().getDataPivotTable(actionJson.optString(JSONConstants.ID, null));
                if(pivotTable != null)
                {
                    return isRangeHasLockedRange(sheet, pivotTable.getTargetCellRange(), zuId, action, exSharedLinkId);
                }
                break;
            case ActionConstants.INSERT_CSV_DATA_AT_END:
                startRow = sheet.getUsedRowIndex() + 1;
                endRow = startRow + actionJson.getInt(JSONConstants.COUNT) - 1;
                tempRange =  new DataRange(sheet.getAssociatedName(), startRow, actionRange.getInt(JSONConstants.START_COLUMN), endRow, actionRange.getInt(JSONConstants.END_COLUMN));
                return isRangeHasLockedRange(sheet, tempRange, zuId, action, exSharedLinkId);
            case ActionConstants.CUT_PASTE:
            case ActionConstants.INSERT_CUT_ROW:
            case ActionConstants.INSERT_CUT_COLUMN:
                JSONObjectWrapper actionSourceRange = ActionJsonUtil.getFirstRangeFromJsonArray(actionJson.optJSONArray(JSONConstants.SOURCE_RANGELIST));
                String sAsn = ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SOURCE_SHEETLIST));
                tempRange = (action == ActionConstants.CUT_PASTE) ? new DataRange(sAsn, actionSourceRange.getInt(JSONConstants.START_ROW), actionSourceRange.getInt(JSONConstants.START_COLUMN),actionSourceRange.getInt(JSONConstants.END_ROW), actionSourceRange.getInt(JSONConstants.END_COLUMN)) :
                    (action == ActionConstants.INSERT_CUT_ROW) ? new DataRange(sAsn, actionSourceRange.getInt(JSONConstants.START_ROW), actionRange.getInt(JSONConstants.START_COLUMN), actionSourceRange.getInt(JSONConstants.END_ROW), actionRange.getInt(JSONConstants.END_COLUMN)) :
                        new DataRange(sAsn, actionRange.getInt(JSONConstants.START_ROW), actionSourceRange.getInt(JSONConstants.START_COLUMN), actionRange.getInt(JSONConstants.END_ROW), actionSourceRange.getInt(JSONConstants.END_COLUMN));

                Sheet srcSheet = sAsn!= null? sheet.getWorkbook().getSheetByAssociatedName(asn) : null;
                if(isRangeHasLockedRange(srcSheet, tempRange, zuId, action, exSharedLinkId))
                {
                    return true;
                }
                //should not break for this case, as it should check for destination range too
            default:
                if(actionRange!= null && !actionRange.isEmpty())
                {
                    tempRange =  new DataRange(asn, actionRange.getInt(JSONConstants.START_ROW), actionRange.getInt(JSONConstants.START_COLUMN), actionRange.getInt(JSONConstants.END_ROW), actionRange.getInt(JSONConstants.END_COLUMN));
                    return isRangeHasLockedRange(sheet,tempRange, zuId, action, exSharedLinkId);
                }
                else if(actionJson.has(JSONConstants.CSTYLEJSON))
                {
                    JSONObjectWrapper cStyleJson = actionJson.getJSONObject(JSONConstants.CSTYLEJSON);
                    if(!(actionJson.getInt(JSONConstants.ACTION) == ActionConstants.CONDITIONAL_FORMAT_DELETE || actionJson.getInt(JSONConstants.ACTION) == ActionConstants.CONDITIONAL_STYLE_REORDER))
                    {
                        List<Range> ranges = ActionJsonUtil.getListOfRangesFromJsonObject(sheet.getWorkbook(), actionJson, false);
                        for(Range range : ranges)
                        {
                            if(isRangeHasLockedRange(sheet, range.toDataRange(), zuId, action, exSharedLinkId))
                            {
                                return true;
                            }
                        }
                    }
                }
                else{
                    LOGGER.log(Level.SEVERE, " NEED TO HANDLE FOR Action : {0} in isRangeHasLockedCells",action );
                }
        }
        return false;
    }
    //For auto-detection of cell value
    //public static String getDataType(Sheet sheet, ViewPort viewport, int sR, int sC, int eR, int eC) {
    public static DataType getDataType(List<Range> listOfRanges)
    {
        int numberValues = 0;
        int dateValues = 0;
        int textValues = 0;
        for(Range range : listOfRanges)
        {
            Sheet sheet = range.getSheet();
            int sR = range.getStartRowIndex();
            int sC = range.getStartColIndex();
            int eR = range.getEndRowIndex();
            int eC = range.getEndColIndex();
            if(sheet != null && sR <= sheet.getUsedRowIndex() && sC <= sheet.getUsedColumnIndex())
            {
                eR = Math.min(eR, sheet.getUsedRowIndex());
                eC = Math.min(eC, sheet.getUsedColumnIndex());

                int halfSize = (((eR - sR) + 1) * ((eC - sC) + 1)) / 2;

                rowIterate: for(int rowIndex = sR; rowIndex <= eR; rowIndex++)
                {
                    ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(rowIndex);
                    Row row = rRow.getRow();
                    int rowsRepeated = rRow.getRowsRepeated();
                    if(row == null)
                    {
                        rowIndex += rowsRepeated - 1;
                        continue;
                    }
                    int rangeRowsRepeated = Math.min(rowsRepeated, (eR - rowIndex) + 1);
                    for(int colIndex = sC; colIndex <= eC; colIndex++)
                    {
                        if(numberValues > halfSize || dateValues > halfSize || textValues > halfSize)
                        {
                            break rowIterate;
                        }

                        // IMPORTANT : Using the rowIndex of rRow.getRow() to avoid unnecessary Iterations.
                        // Hence should not use the rCell.getRowIndex or rCell.getRowsRepeated aftert this.
                        ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(row.getRowIndex(), colIndex);
                        Cell cell = rCell.getCell();
                        if(cell != null)
                        {
                            Type contentType = cell.getContentType();
                            if(contentType != Type.UNDEFINED)
                            {
                                int rangeColsRepeated = Math.min(rCell.getColsRepeated(), (eC - colIndex) + 1);
                                if(contentType.isDateType())
                                {
                                    dateValues += (rangeRowsRepeated * rangeColsRepeated);
                                }
                                else if(contentType.isNumberType())
                                {
                                    numberValues += (rangeRowsRepeated * rangeColsRepeated);
                                }
                                else
                                {
                                    textValues += (rangeRowsRepeated * rangeColsRepeated);
                                }
                            }
                        }
                        colIndex += rCell.getColsRepeated() - 1;
                    }
                    rowIndex += rowsRepeated - 1;
                }
            }
        }

        if(dateValues == 0 && textValues == 0)
        {
            return DataType.NUMBER;
        }

        if(dateValues > textValues && dateValues > numberValues)
        {
            return DataType.DATE;
        }
        else if(textValues > dateValues && textValues > numberValues)
        {
            return DataType.TEXT;
        }

        return DataType.NUMBER;
    }

    public static List<DataRange> addCheckboxValue(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        List<DataRange> resultRange = new ArrayList<>();
        List<DataRange> nonCheckboxRanges = new ArrayList<>();

        Value trueValueObj = Value.getInstance(Type.BOOLEAN, true);
        Value falseValueObj = Value.getInstance(Type.BOOLEAN, false);

//        int endRow = Math.min(endRowIndex, Math.max(startRowIndex, sheet.getRowNum() - 1));
        int endRow = endRowIndex;
        for(int rowIndex = startRowIndex; rowIndex <= endRow; rowIndex++)
        {
            Row row = sheet.getRowReadOnly(rowIndex) == null ? createNewRow(sheet, rowIndex, endRowIndex) : sheet.getRowReadOnly(rowIndex);

            // TODOD : if it exceeds 65536
            if(row.getRowsRepeated() > (endRowIndex - rowIndex + 1))
            {
                sheet.getRow(endRowIndex + 1);
            }


//            int endCol = Math.min(endColIndex, Math.max(startColIndex, row.getCellNum() - 1));
            int endCol = endColIndex;
            for(int colIndex = startColIndex; colIndex <= endCol; colIndex++)
            {
                Cell cell = sheet.getCellReadOnly(rowIndex, colIndex) == null ? createNewCell(sheet, row.getCells(), rowIndex, colIndex, endColIndex) : sheet.getCellReadOnly(rowIndex, colIndex);
                // TODOD : if it exceeds 256
                if(cell.getColsRepeated() > (endColIndex - colIndex + 1))
                {
                    sheet.getCell(rowIndex, endColIndex + 1);
                }
                Object cellValueObj = cell.getValueObject();

                Predicate<Cell> isCoveredUnderMerge = (c) ->  sheet.getMergeCellSpanRange(c) != null ? false : sheet.isCoveredUnderMerge(c.getRowIndex(), c.getColumnIndex());

                if(!(cellValueObj instanceof Date || cellValueObj instanceof String) && isCoveredUnderMerge.negate().test(cell))
                {
                    if(!(cellValueObj instanceof Boolean))
                    {
                        int cellValue = 0;

                        try
                        {
                            cellValue = FunctionUtil.objectToNumber(cellValueObj).intValue();
                        } catch (EvaluationException ex)
                        {
                        }

                        if(cellValue != 0) {
                            cell.setValue(trueValueObj);
                        } else
                        {
                            cell.setValue(falseValueObj);
                        }
                    }

                }
                else
                {
                    mergeAndAddDataRangeToList(new DataRange(sheet.getAssociatedName(), rowIndex, colIndex, rowIndex, colIndex), nonCheckboxRanges);
                }


                colIndex += cell.getColsRepeated() - 1;
            }

            rowIndex += row.getRowsRepeated() - 1;
        }
        resultRange.add(new DataRange(sheet.getAssociatedName(), startRowIndex, startColIndex, endRowIndex, endColIndex));

        if(!nonCheckboxRanges.isEmpty())
        {
            for(DataRange range:nonCheckboxRanges)
            {
                DataRange dRange = new DataRange(sheet.getAssociatedName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
                resultRange = RangeUtil.splitRanges(resultRange, dRange);
            }
        }

        return resultRange;
    }

    public static SheetRange getSheetRange(String sheetRangeString) throws SheetEngineException {
        String[] splitRefs = sheetRangeString.split(":");
        List<SheetRow> rowRefs = new LinkedList();
        List<SheetColumn> colRefs = new LinkedList();
        List<SheetCell> cellRefs = new LinkedList();

        for(int i = 0; i < splitRefs.length; i++) {
            String refStr = splitRefs[i];

            int currRow = CellUtil.getRow(refStr);
            int currCol = CellUtil.getColumn(refStr);

            if(currRow != -1 && currCol != -1) {
                cellRefs.add(new SheetCell(currRow, currCol));
            } else if(currRow != -1) {
                rowRefs.add(new SheetRow(currRow));
            } else if(currCol != -1) {
                colRefs.add(new SheetColumn(currCol));
            } else {
                throw new SheetEngineException("Invalid Range String: "+sheetRangeString); //No I18N
            }
        }

        /*
         * Rules for sheetRangeString (excluding the SheetName part) can be found in the wiki page "Rules defining a Range String in a Formula"
         * https://projects-143922000000006021.wiki.zoho.com/Rules-defining-a-Range-String-in-a-Formula.html
         */

        if(rowRefs.isEmpty() && colRefs.isEmpty() && cellRefs.isEmpty()) {
            throw new SheetEngineException("Invalid Range String: "+sheetRangeString); //No I18N
        }

        if(rowRefs.size() == 1 || colRefs.size() == 1) {
            throw new SheetEngineException("Invalid Range String: "+sheetRangeString); //No I18N
        }

        int startRowIndex = -1, startColIndex = -1, endRowIndex = -1, endColIndex = -1;

        if(!rowRefs.isEmpty()) {
            startColIndex = 0;
            endColIndex = Utility.MAXNUMOFCOLS - 1;
        }

        if(!colRefs.isEmpty()) {
            startRowIndex = 0;
            endRowIndex = Utility.MAXNUMOFROWS - 1;
        }

        if(startRowIndex == -1 && endRowIndex == -1) {
            startRowIndex = IntStream.concat(rowRefs.stream().mapToInt(SheetRow::getRowIndex), cellRefs.stream().mapToInt(SheetCell::getRowIndex)).min().getAsInt();
            endRowIndex = IntStream.concat(rowRefs.stream().mapToInt(SheetRow::getRowIndex), cellRefs.stream().mapToInt(SheetCell::getRowIndex)).max().getAsInt();
        }

        if(startColIndex == -1 && endColIndex == -1) {
            startColIndex = IntStream.concat(colRefs.stream().mapToInt(SheetColumn::getColIndex), cellRefs.stream().mapToInt(SheetCell::getColIndex)).min().getAsInt();
            endColIndex = IntStream.concat(colRefs.stream().mapToInt(SheetColumn::getColIndex), cellRefs.stream().mapToInt(SheetCell::getColIndex)).max().getAsInt();
        }

        return new SheetRange(startRowIndex, startColIndex, endRowIndex, endColIndex);
    }

    public static List<DataRange> getCheckboxRanges(Sheet sheet, int startRow, int startCol, int endRow, int endCol)
    {
        List<DataRange> resultRange = new ArrayList();
        DataRange range = new DataRange(sheet.getAssociatedName(), startRow, startCol, endRow, endCol);

        for(DataRange cRange : sheet.getCheckboxRangeList())
        {
            DataRange intRange = RangeUtil.intersection(range, cRange);

            if(intRange != null)
            {
                resultRange.add(new DataRange(sheet.getAssociatedName(),intRange.getStartRowIndex(), intRange.getStartColIndex(), intRange.getEndRowIndex(), intRange.getEndColIndex()));
            }

        }
        return resultRange;
    }

    public static int getVarTopRowIndex(ASTRangeNode rangeNode, int currRow) throws EvaluationException{
        return FormulaUtil.getVarRowIndex(getTop(rangeNode, currRow), currRow);
    }

    public static int getVarBottomRowIndex(ASTRangeNode rangeNode, int currRow) throws EvaluationException{
        return FormulaUtil.getVarRowIndex(getBottom(rangeNode, currRow), currRow);
    }

    public static int getVarLeftColIndex(ASTRangeNode rangeNode, int currCol) throws EvaluationException{
        return FormulaUtil.getVarColIndex(getLeft(rangeNode, currCol), currCol);
    }

    public static int getVarRightColIndex(ASTRangeNode rangeNode, int currCol) throws EvaluationException{
        return FormulaUtil.getVarColIndex(getRight(rangeNode, currCol), currCol);
    }

    public static boolean isCellInsideRange(String rangeASN, int rangeStartRow, int rangeStartCol, int rangeEndRow,  int rangeEndCol, String cellASN, int cellRow, int cellCol) {
        return rangeASN.equals(cellASN) && isCellInsideRange(rangeStartRow, rangeStartCol, rangeEndRow, rangeEndCol, cellRow, cellCol);
    }

    public static boolean isCellInsideRange(int rangeStartRow, int rangeStartCol, int rangeEndRow,  int rangeEndCol, int cellRow, int cellCol){
        return (rangeStartRow<= cellRow && cellRow<= rangeEndRow
            && rangeStartCol<= cellCol && cellCol<= rangeEndCol);
    }

    //shrink---> offset= positive;  expand----> offset= negative
    public static void shrinkTopBy(ASTRangeNode rangeNode, int currRow, int offset) throws EvaluationException{
        ASTVarNode topLeft= RangeUtil.getTop(rangeNode, currRow);
        topLeft.transposeBy(offset, 0, true, true);
    }

    //shrink---> offset= positive;  expand----> offset= negative
    public static void shrinkBottomBy(ASTRangeNode rangeNode, int currRow, int offset) throws EvaluationException{
        ASTVarNode bottomRight= RangeUtil.getBottom(rangeNode, currRow);
        bottomRight.transposeBy(-offset, 0, true, true);
    }

    //shrink---> offset= positive;  expand----> offset= negative
    public static void shrinkLeftBy(ASTRangeNode rangeNode, int currCol, int offset) throws EvaluationException{
        ASTVarNode topLeft= RangeUtil.getLeft(rangeNode, currCol);
        topLeft.transposeBy(0, offset, true, true);
    }

    //shrink---> offset= positive;  expand----> offset= negative
    public static void shrinkRightBy(ASTRangeNode rangeNode, int currCol, int offset) throws EvaluationException{
        ASTVarNode bottomRight= RangeUtil.getRight(rangeNode, currCol);
        bottomRight.transposeBy(0, -offset, true, true);
    }

    public static ASTVarNode getTop(ASTRangeNode rangeNode, int currRow) throws EvaluationException{
        int row0= FormulaUtil.getVarRowIndex((ASTVarNode)(rangeNode.jjtGetChild(0)), currRow);
        int row1= FormulaUtil.getVarRowIndex((ASTVarNode)(rangeNode.jjtGetChild(1)), currRow);

        if(row1<row0){
            return (ASTVarNode)(rangeNode.jjtGetChild(1));
        }
        else{
            return (ASTVarNode)(rangeNode.jjtGetChild(0));
        }
    }

    public static ASTVarNode getBottom(ASTRangeNode rangeNode, int currRow) throws EvaluationException{
        int row0= FormulaUtil.getVarRowIndex((ASTVarNode)(rangeNode.jjtGetChild(0)), currRow);
        int row1= FormulaUtil.getVarRowIndex((ASTVarNode)(rangeNode.jjtGetChild(1)), currRow);

        if(row0>row1){
            return (ASTVarNode)(rangeNode.jjtGetChild(0));
        }
        else{
            return (ASTVarNode)(rangeNode.jjtGetChild(1));
        }
    }

    public static ASTVarNode getLeft(ASTRangeNode rangeNode, int currCol) throws EvaluationException{
        int col0= FormulaUtil.getVarColIndex((ASTVarNode)(rangeNode.jjtGetChild(0)), currCol);
        int col1= FormulaUtil.getVarColIndex((ASTVarNode)(rangeNode.jjtGetChild(1)), currCol);

        if(col1<col0){
            return (ASTVarNode)(rangeNode.jjtGetChild(1));
        }
        else{
            return (ASTVarNode)(rangeNode.jjtGetChild(0));
        }
    }

    public static ASTVarNode getRight(ASTRangeNode rangeNode, int currCol) throws EvaluationException{
        int col0= FormulaUtil.getVarColIndex((ASTVarNode)(rangeNode.jjtGetChild(0)), currCol);
        int col1= FormulaUtil.getVarColIndex((ASTVarNode)(rangeNode.jjtGetChild(1)), currCol);

        if(col0>col1){
            return (ASTVarNode)(rangeNode.jjtGetChild(0));
        }
        else{
            return (ASTVarNode)(rangeNode.jjtGetChild(1));
        }
    }

    public enum RangeEdge{
        TOP,
        BOTTOM,
        LEFT,
        RIGHT
    }

    public enum RangeVertex{
        TOP_LEFT,
        TOP_RIGHT,
        BOTTOM_LEFT,
        BOTTOM_RIGHT
    }

    public static Set<RangeEdge> getContainingEdges(ASTRangeNode rangeNode, Cell currCell, Range enclosingRange) throws EvaluationException{
        Set<RangeVertex> containingVertices= getContainingVertices(rangeNode, currCell, enclosingRange);

        Set<RangeEdge> containingEdges= EnumSet.noneOf(RangeEdge.class);
        if(containingVertices.contains(RangeVertex.TOP_LEFT) && containingVertices.contains(RangeVertex.TOP_RIGHT)){
            containingEdges.add(RangeEdge.TOP);
        }
        if(containingVertices.contains(RangeVertex.BOTTOM_LEFT) && containingVertices.contains(RangeVertex.BOTTOM_RIGHT)){
            containingEdges.add(RangeEdge.BOTTOM);
        }
        if(containingVertices.contains(RangeVertex.TOP_LEFT) && containingVertices.contains(RangeVertex.BOTTOM_LEFT)){
            containingEdges.add(RangeEdge.LEFT);
        }
        if(containingVertices.contains(RangeVertex.TOP_RIGHT) && containingVertices.contains(RangeVertex.BOTTOM_RIGHT)){
            containingEdges.add(RangeEdge.RIGHT);
        }

        return containingEdges;
    }

    public static Set<RangeVertex> getContainingVertices(ASTRangeNode rangeNode, Cell currCell, Range enclosingRange) throws EvaluationException{
        Set<RangeVertex> containingVertices= EnumSet.noneOf(RangeVertex.class);

        Sheet sheet= rangeNode.getVarSheet(currCell.getRow().getSheet());

        if(sheet==enclosingRange.getSheet()){
            int top= RangeUtil.getVarTopRowIndex(rangeNode, currCell.getRowIndex());
            int bottom= RangeUtil.getVarBottomRowIndex(rangeNode, currCell.getRowIndex());
            int left= RangeUtil.getVarLeftColIndex(rangeNode, currCell.getColumnIndex());
            int right= RangeUtil.getVarRightColIndex(rangeNode, currCell.getColumnIndex());

            if(RangeUtil.isCellInsideRange(enclosingRange.getStartRowIndex(), enclosingRange.getStartColIndex(), enclosingRange.getEndRowIndex(), enclosingRange.getEndColIndex(), top, left)){
                containingVertices.add(RangeVertex.TOP_LEFT);
            }
            if(RangeUtil.isCellInsideRange(enclosingRange.getStartRowIndex(), enclosingRange.getStartColIndex(), enclosingRange.getEndRowIndex(), enclosingRange.getEndColIndex(), top, right)){
                containingVertices.add(RangeVertex.TOP_RIGHT);
            }
            if(RangeUtil.isCellInsideRange(enclosingRange.getStartRowIndex(), enclosingRange.getStartColIndex(), enclosingRange.getEndRowIndex(), enclosingRange.getEndColIndex(), bottom, left)){
                containingVertices.add(RangeVertex.BOTTOM_LEFT);
            }
            if(RangeUtil.isCellInsideRange(enclosingRange.getStartRowIndex(), enclosingRange.getStartColIndex(), enclosingRange.getEndRowIndex(), enclosingRange.getEndColIndex(), bottom, right)){
                containingVertices.add(RangeVertex.BOTTOM_RIGHT);
            }
        }

        return containingVertices;
    }

    public static List<ASTVarNode> getAsVarNodesList(ASTRangeNode rangeTree){
        List<ASTVarNode> varNodesList= new ArrayList<>();
        for(int i=0; i< rangeTree.jjtGetNumChildren(); i++){
            Node child= rangeTree.jjtGetChild(i);
            if(child instanceof ASTVarNode){
                varNodesList.add((ASTVarNode)child);
            }
            else{
                varNodesList.addAll(getAsVarNodesList((ASTRangeNode)child));
            }
        }
        return varNodesList;
    }

    public static boolean isSheetRelative(ASTRangeNode rangeTree){
        for(int i=0; i<rangeTree.jjtGetNumChildren(); i++){
            Node childTree= rangeTree.jjtGetChild(i);
            if(childTree instanceof ASTVarNode){
                if(!((ASTVarNode)childTree).isSheetRelative()){
                    return false;
                }
            }
            else if(childTree instanceof ASTRangeNode){
                boolean isChildRangeSheetRelative= isSheetRelative((ASTRangeNode)childTree);
                if(!isChildRangeSheetRelative){
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean isTreeContainsSheetAbsoluteRefs(Node tree){
        if(tree instanceof ASTVarNode){
            return !((ASTVarNode)tree).isSheetRelative();
        }
        else{
            for(int i= 0; i< tree.jjtGetNumChildren(); i++){
                boolean isChildTreeContainsSheetAbsoluteRefs= isTreeContainsSheetAbsoluteRefs(tree.jjtGetChild(i));
                if(isChildTreeContainsSheetAbsoluteRefs){
                    return true;
                }
            }
            return false;
        }
    }

    public static boolean isTreeContainsRelRefsOutsideOrAbsRefsInsideRange(Node tree, Cell currCell, Range range){
        if(tree instanceof ASTVarNode){
            ASTVarNode varNode= (ASTVarNode)tree;
            if(varNode.isVariableValid()){
                boolean isCellRefOutsideRange= isCellRefOutsideRange((ASTVarNode)tree, currCell, range);
                boolean isContainsAbsRefs= !varNode.isSheetRelative() || !varNode.isRowRelative() || !varNode.isColRelative();
                boolean isContainsRelRefs= varNode.isSheetRelative() || varNode.isRowRelative() || varNode.isColRelative();
                return isCellRefOutsideRange ? isContainsRelRefs : isContainsAbsRefs;
            }
        }
        else{
            for(int i= 0; i< tree.jjtGetNumChildren(); i++){
                boolean isChildContainsRelRefsOutsideOrAbsRefsInsideRange= isTreeContainsRelRefsOutsideOrAbsRefsInsideRange(tree.jjtGetChild(i), currCell, range);
                if(isChildContainsRelRefsOutsideOrAbsRefsInsideRange){
                    return true;
                }
            }
        }
        return false;
    }

    public static int numNodesOutsideRange(ASTRangeNode rangeReferenceNode, Cell currCell, Range range){
        int count=0;
        count+= isCellRefOutsideRange((ASTVarNode)rangeReferenceNode.jjtGetChild(0), currCell, range) ? 1 : 0;
        count+= isCellRefOutsideRange((ASTVarNode)rangeReferenceNode.jjtGetChild(1), currCell, range) ? 1 : 0;

        return count;
    }

    public static boolean isCellRefOutsideRange(ASTVarNode cellReferenceNode, Cell currCell, Range range){
        try{
            Sheet refSheet= cellReferenceNode.getVarSheet(currCell.getRow().getSheet());
            int refRow= FormulaUtil.getVarRowIndex(cellReferenceNode, currCell.getRowIndex());
            int refCol= FormulaUtil.getVarColIndex(cellReferenceNode, currCell.getColumnIndex());
            return !range.isMember(refSheet, refRow, refCol);
        }
        catch(EvaluationException e){
            return true;
        }
    }

    public static int getColSizeOfRectangularRanges(List<Range> listOfRanges)
    {
        int colSize = 0;
        Set<Integer> set = new HashSet<>();
        for(Range range : listOfRanges)
        {
            if(!set.contains(range.getStartColIndex()))
            {
                colSize += range.getColSize();
                set.add(range.getStartColIndex());
            }
        }
        return colSize;
    }

    public static int getColSizeOfRectangularRanges(Collection<RangeUtil.SheetRange> rangesSelected){
        int colSize = 0;
        Set<Integer> set = new HashSet<>();
        for(RangeUtil.SheetRange sheetRange : rangesSelected){
            if(!set.contains(sheetRange.getStartColIndex()))
            {
                colSize += sheetRange.getEndColIndex() - sheetRange.getStartColIndex() + 1;
                set.add(sheetRange.getStartColIndex());
            }
        }
        return colSize;
    }

    public static int getRowSizeOfRectangularRanges(List<Range> listOfRanges){
        int rowSize = 0;
        Set<Integer> set = new HashSet<>();
        for(Range range : listOfRanges){
            if(!set.contains(range.getStartRowIndex()))
            {
                rowSize += range.getRowSize();
                set.add(range.getStartRowIndex());
            }
        }
        return rowSize;
    }

    public static int getRowSizeOfRectangularRanges(Collection<RangeUtil.SheetRange> rangesSelected){
        int rowSize = 0;
        Set<Integer> set = new HashSet<>();
        for(RangeUtil.SheetRange sheetRange : rangesSelected){
            if(!set.contains(sheetRange.getStartRowIndex()))
            {
                rowSize += sheetRange.getEndRowIndex() - sheetRange.getStartRowIndex() + 1;
                set.add(sheetRange.getStartRowIndex());
            }
        }
        return rowSize;
    }

    public static int getMinStartRowIndex(List<Range> listOfRanges)
    {
        int startRow = Utility.MAXNUMOFROWS;
        for(Range range : listOfRanges)
        {
            if(range.getStartRowIndex() < startRow)
            {
                startRow = range.getStartRowIndex();
            }
        }
        return startRow;
    }

    public static int getMinStartColIndex(List<Range> listOfRanges)
    {
        int startCol = Utility.MAXNUMOFCOLS;
        for(Range range : listOfRanges)
        {
            if(range.getStartColIndex() < startCol)
            {
                startCol = range.getStartColIndex();
            }
        }
        return startCol;
    }

    public static int getMaxEndRowIndex(List<Range> listOfRanges)
    {
        int endRow = -1;
        for(Range range : listOfRanges)
        {
            if(range.getEndRowIndex() > endRow)
            {
                endRow = range.getEndRowIndex();
            }
        }
        return endRow;
    }

    public static int getMaxEndColIndex(List<Range> listOfRanges)
    {
        int endCol = -1;
        for(Range range : listOfRanges)
        {
            if(range.getEndColIndex() > endCol)
            {
                endCol = range.getEndColIndex();
            }
        }
        return endCol;
    }

    public static int getVisibleRowSizeOfRectangularRanges(Sheet sheet, List<Range> listOfRanges, VisibleIndices visibleRows)
    {
        int rowSize = 0;
        Set<Integer> set = new HashSet<>();
        for(Range range : listOfRanges)
        {
            if(!set.contains(range.getStartRowIndex()))
            {
                rowSize += visibleRows != null ? visibleRows.getVisibleIndicesCount(range.getStartRowIndex(), range.getEndRowIndex()) : getVisibleRowCount(sheet, range.getStartRowIndex(), range.getEndRowIndex());
                set.add(range.getStartRowIndex());
            }
        }
        return rowSize;
    }

    public static int getVisibleColSizeOfRectangularRanges(Sheet sheet, List<Range> listOfRanges, VisibleIndices visibleColumns)
    {
        int colSize = 0;
        Set<Integer> set = new HashSet<>();
        for(Range range : listOfRanges)
        {
            if(!set.contains(range.getStartColIndex()))
            {
                colSize += visibleColumns != null ? visibleColumns.getVisibleIndicesCount(range.getStartColIndex(), range.getEndColIndex()) : getVisibleColumnCount(sheet, range.getStartColIndex(), range.getEndColIndex());
                set.add(range.getStartColIndex());
            }
        }
        return colSize;
    }

    public static enum Orientation {
        VERTICAL,
        HORIZONTAL
    }

    public static Range getRange(ASTRangeNode rangeNode, Cell baseCell, Orientation expansionOrientation, int expansionCount) throws JepException {
        if(expansionCount < 0) {
            throw new IllegalArgumentException();
        }

        ASTRangeNode rangeNodeExpanded;
        if(expansionCount == 0) {
            rangeNodeExpanded = rangeNode;
        } else {
            rangeNodeExpanded = (ASTRangeNode) Workbook.getDeepCopyVisitor().deepCopy(rangeNode);

            switch(expansionOrientation) {
                case VERTICAL:
                    int startRow = RangeUtil.getVarTopRowIndex(rangeNodeExpanded, baseCell.getRowIndex());
                    int endRow = RangeUtil.getVarBottomRowIndex(rangeNodeExpanded, baseCell.getRowIndex());
                    if(startRow != endRow) {
                        throw new IllegalArgumentException();
                    }
                    RangeUtil.shrinkBottomBy(rangeNodeExpanded, baseCell.getRowIndex(), -expansionCount);
                    break;
                case HORIZONTAL:
                    int startCol = RangeUtil.getVarLeftColIndex(rangeNodeExpanded, baseCell.getColumnIndex());
                    int endCol = RangeUtil.getVarRightColIndex(rangeNodeExpanded, baseCell.getColumnIndex());
                    if(startCol != endCol) {
                        throw new IllegalArgumentException();
                    }
                    RangeUtil.shrinkRightBy(rangeNodeExpanded, baseCell.getColumnIndex(), -expansionCount);
                    break;
            }
        }

        ZSRefEvaluator refEvaluator = (ZSRefEvaluator) Workbook.getRefJep().getEvaluator();
        Set<Range> ranges = refEvaluator.evaluate(rangeNodeExpanded, baseCell);
        Range range;
        if(ranges.isEmpty()) {
            range = null;
        } else {
            range = ranges.iterator().next();
        }
        return range;
    }

    public static List<DataRange> [] split(List<DataRange> dataRanges, ReadOnlyCell readOnlyCell) {
        List<DataRange> [] splitDataRange = new List[]{dataRanges};

        if(readOnlyCell != null && !dataRanges.isEmpty()) {
            int rowIndex = readOnlyCell.getRowIndex();
            int colIndex = readOnlyCell.getColIndex();
            String asn = readOnlyCell.getSheet().getAssociatedName();

            List<DataRange> left = new ArrayList<>();
            int rangeIndexOfReadOnlyCell = -1;

            for(int i = 0; i < dataRanges.size(); i++) {
                DataRange dataRange = dataRanges.get(i);
                if(asn.equals(dataRange.getAssociatedSheetName())) {
                    if(rowIndex >= dataRange.getStartRowIndex() && rowIndex <= dataRange.getEndRowIndex()
                        && colIndex >= dataRange.getStartColIndex() && colIndex <= dataRange.getEndColIndex()) {
                        rangeIndexOfReadOnlyCell = i;
                        break;
                    }
                }
                left.add(dataRange);
            }

            if(rangeIndexOfReadOnlyCell != -1) {

                List<DataRange>[] split = split(dataRanges.get(rangeIndexOfReadOnlyCell), readOnlyCell.getRowIndex(), readOnlyCell.getColIndex());
                left.addAll(split[0]);

                List<DataRange> right = new ArrayList<>();
                right.addAll(split[1]);

                for(int i = rangeIndexOfReadOnlyCell + 1; i < dataRanges.size(); i++) {
                    right.add(dataRanges.get(i));
                }

                splitDataRange = new List[] {left, right};
                return splitDataRange;
            } else {
                return splitDataRange;
            }
        }
        return splitDataRange;
    }

    public static List<DataRange> [] split (DataRange dataRange, int rowIndex, int colIndex) {
        List<DataRange> [] splitDataRanges;
        if(rowIndex >= dataRange.getStartRowIndex() && rowIndex <= dataRange.getEndRowIndex()
            && colIndex >= dataRange.getStartColIndex() && colIndex <= dataRange.getEndColIndex()) {

            List<DataRange> left = new ArrayList<>();
            List<DataRange> right = new ArrayList<>();

            int topRowIndex = rowIndex - 1;
            int topColIndex = colIndex - 1;

            int bottomRowIndex = rowIndex + 1;
            int bottomColIndex = colIndex + 1;

            if(topRowIndex >= dataRange.getStartRowIndex()) {
                DataRange dataRange1 = new DataRange(dataRange.getAssociatedSheetName(), dataRange.getStartRowIndex(), dataRange.getStartColIndex(), topRowIndex, dataRange.getEndColIndex());
                left.add(dataRange1);
            }

            if(topColIndex >= dataRange.getStartColIndex()) {
                DataRange dataRange1 = new DataRange(dataRange.getAssociatedSheetName(), rowIndex, dataRange.getStartColIndex(), rowIndex, topColIndex);
                left.add(dataRange1);
            }

            if(bottomColIndex <= dataRange.getEndColIndex()) {
                DataRange dataRange1 = new DataRange(dataRange.getAssociatedSheetName(), rowIndex, bottomColIndex, rowIndex, dataRange.getEndColIndex());
                right.add(dataRange1);
            }

            if(bottomRowIndex <= dataRange.getEndRowIndex()) {
                DataRange dataRange1 = new DataRange(dataRange.getAssociatedSheetName(), bottomRowIndex, dataRange.getStartColIndex(), dataRange.getEndRowIndex(), dataRange.getEndColIndex());
                right.add(dataRange1);
            }

            splitDataRanges = new List[] {left, right};
        } else {
            List<DataRange> dataRanges = new ArrayList<>();
            dataRanges.add(dataRange);
            splitDataRanges = new List[] {dataRanges};
        }
        return splitDataRanges;
    }
    public static List<DataRange> sortRowDescendingOrder(List<DataRange> dataRanges){
        dataRanges.sort((DataRange o1, DataRange o2) ->
        {
            int sR1 = o1.getStartRowIndex();
            int sR2 = o2.getStartRowIndex();
            if(sR1 > sR2)
            {
                return -1;
            }
            else if(sR1 < sR2)
            {
                return 1;
            }
            return 0;
        });
        return dataRanges;
    }
    public static List<DataRange> sortRowAscendingOrder(List<DataRange> dataRanges){
        dataRanges.sort((DataRange o1, DataRange o2) ->
        {
            int sR1 = o1.getStartRowIndex();
            int sR2 = o2.getStartRowIndex();
            if(sR1 < sR2)
            {
                return -1;
            }
            else if(sR1 > sR2)
            {
                return 1;
            }
            return 0;
        });
        return dataRanges;
    }
    public static List<DataRange> sortColDescendingOrder(List<DataRange> dataRanges){
        dataRanges.sort((DataRange o1, DataRange o2) ->
        {
            int sC1 = o1.getStartColIndex();
            int sC2 = o2.getStartColIndex();
            if(sC1 > sC2)
            {
                return -1;
            }
            else if(sC1 < sC2)
            {
                return 1;
            }
            return 0;
        });
        return dataRanges;
    }
    public static List<DataRange> sortColAscendingOrder(List<DataRange> dataRanges){
        dataRanges.sort((DataRange o1, DataRange o2) ->
        {
            int sC1 = o1.getStartColIndex();
            int sC2 = o2.getStartColIndex();
            if(sC1 < sC2)
            {
                return -1;
            }
            else if(sC1 > sC2)
            {
                return 1;
            }
            return 0;
        });
        return dataRanges;
    }

    public static ReadOnlyCell getReadOnlyCellByCellValue(Sheet sheet, String cellValue, int rowIndex){
        int maxIndex = sheet.getUsedColumnIndex();
        for(int i =0; i <= maxIndex; i++){
            ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(rowIndex-1, i);
            if(rCell != null && rCell.getCell() != null && rCell.getCell().getContent() != null && rCell.getCell().getContent().equals(cellValue)){
                return rCell;
            }
        }
        return null;
    }

    /**
     * Uses Table Prediction to predict the exact range.
     * <br><br>
     * <b>Uses: Find exact sorting range when a single range is selected among a table range.</b>
     * @param range Range to predict
     * @return DataRange
     */
    public static DataRange predictDataRange(Range range)
    {
        TablePrediction predict = new TablePrediction(range);
        predict.applyPattern(new JSONObjectWrapper().put(TableConstraints.DETECTING_DATA_TYPE, true));
        DataRange d1 = predict.getTableRange().toDataRange();
        LOGGER.log(Level.INFO, "DataRange {0}", d1);

        return d1;
    }

    public static boolean isEntireSheet(DataRange range)
    {
        return isEntireRow(range) && isEntireColumn(range);
    }

    public static boolean isEntireRow(DataRange range)
    {
        return range.getRowSize() == Utility.MAXNUMOFROWS;
    }

    public static boolean isEntireColumn(DataRange range)
    {
        return range.getColSize() == Utility.MAXNUMOFCOLS;
    }

    public static List<Integer> getAbsoluteIndices(List<DataRange> visibleDataRanges, List<Integer> relativeIndices, boolean isColumnOrientation)
    {
        List<Integer> absSortOrders = new ArrayList<>();
        for(Integer relativeIndex : relativeIndices) {
            for (DataRange visibleRange : visibleDataRanges) {
                int currRangeSize = isColumnOrientation ? visibleRange.getColSize() : visibleRange.getRowSize();
                if (relativeIndex < currRangeSize) {
                    absSortOrders.add((isColumnOrientation ? visibleRange.getStartColIndex() : visibleRange.getStartRowIndex()) + relativeIndex);
                    break;
                }
                relativeIndex -= currRangeSize;
            }
        }
        return absSortOrders;
    }

    public static List<DataRange> getNonEmptyRowRanges(Sheet sheet, int startRow, int startCol, int endRow, int endCol, boolean skipHiddenRows){
        Integer visibleNonEmptyStartRowIndex = null;
        List<DataRange> visibleNonEmptyRowDataRanges = new ArrayList<>();
        String asn = sheet.getAssociatedName();

        for(int rowIndex = startRow; rowIndex <= endRow; rowIndex++){
            ReadOnlyRow roRow = RangeIterator.getReadOnlyRowAtSheet(sheet, rowIndex);
            Row row = roRow.getRow();
            if((skipHiddenRows && row != null && !RowUtil.isVisibleRow(row)) || RangeIterator.isRowEmpty(row, startCol, endCol)){
                if(visibleNonEmptyStartRowIndex != null){
                    visibleNonEmptyRowDataRanges.add(new DataRange(asn, visibleNonEmptyStartRowIndex, startCol, rowIndex - 1, endCol));
                    visibleNonEmptyStartRowIndex = null;
                }
            }else if(visibleNonEmptyStartRowIndex == null){
                visibleNonEmptyStartRowIndex = rowIndex;
            }
            rowIndex += (roRow.getRowsRepeated() - 1);
        }

        if(visibleNonEmptyStartRowIndex != null){
            visibleNonEmptyRowDataRanges.add(new DataRange(asn, visibleNonEmptyStartRowIndex, startCol, endRow, endCol));
        }
        return visibleNonEmptyRowDataRanges;
    }

    public static List<DataRange> getDataRangeFormRowIndices(Sheet sheet, int startCol, int endCol, List<Integer> rowIndices){
        List<DataRange> dataRanges = new ArrayList<>();

        rowIndices = new ArrayList<>(rowIndices);
        Collections.sort(rowIndices);

        int start = -1;
        int lastIndex = -1;
        for(int i : rowIndices)
        {
            if(start == -1)
            {
                start = i;
                lastIndex = i;
            }
            else if(lastIndex == i - 1)
            {
                lastIndex = i;
            }
            else
            {
                dataRanges.add(new DataRange(sheet.getAssociatedName(), start, startCol, lastIndex, endCol));
                start = i;
                lastIndex = i;
            }
        }

        if(start != -1)
        {
            dataRanges.add(new DataRange(sheet.getAssociatedName(), start, startCol, lastIndex, endCol));
        }

        return dataRanges;
    }

    /* If a picklist has source ranges Sheet1.A1:A10 and Sheet2.B1:B10, and Sheet1 is duplicated, then the Picklist in Sheet1_1 has the source ranges Sheet1_1.A1:A10 and Sheet2.B1:B10
     * This method is used to create new CellPicklist in the Sheet2.B1:B10 pointing to the new cloned picklist in Sheet1_1 */
    public static void cloneCellPicklistForDuplicate(Range range, Picklist oldPicklist, Picklist clonedPicklist) {
        for(int row=range.getStartRowIndex(); row<=range.getEndRowIndex(); row++) {
            for(int col=range.getStartColIndex(); col<=range.getStartColIndex(); col++) {
                Cell cell = range.getSheet().getCell(row,col);
                Collection<CellPicklist> cellPicklists = cell.getCellPicklists();
                if(cellPicklists != null) {
                    CellPicklist copyCellPicklist = null;
                    for(CellPicklist cellPicklist: cellPicklists) {
                        if(cellPicklist.getPicklist().getId() == oldPicklist.getId()) {
                            copyCellPicklist = cellPicklist;
                            break;
                        }
                    }
                    if(copyCellPicklist != null) {
                        cell.addCellPicklist(new CellPicklist(clonedPicklist, copyCellPicklist.getItemID()));
                    }
                }
                else {
                    cell.addCellPicklist(new CellPicklist(clonedPicklist));
                }
            }
        }
    }

    public static List<Range> splitDestRange(Range destRange, int srcRowSize, int srcColSize, VisibleIndices visibleRows, VisibleIndices visibleColumns)
    {
        List<Range> destinationRanges = new ArrayList<>();

        int destRowSize = RangeUtil.getVisibleRowCount(destRange.getSheet(), destRange.getStartRowIndex(), destRange.getEndRowIndex(), visibleRows);
        int destColSize = RangeUtil.getVisibleColumnCount(destRange.getSheet(), destRange.getStartColIndex(), destRange.getEndColIndex(), visibleColumns);
        int acrossRows= destRowSize/srcRowSize;
        int acrossCols= destColSize/srcColSize;
        int remRows= destRowSize%srcRowSize;
        int remCols= destColSize%srcColSize;

        int startRowIndex = destRange.getStartRowIndex();
        int endRowIndex = (acrossRows == 0) ? startRowIndex + remRows - 1: startRowIndex + srcRowSize -1;

        for(int i =0 ; i<= acrossRows; i++)
        {
            int startColIndex = destRange.getStartColIndex();
            int endColIndex = (acrossRows == 0) ? startColIndex + remCols - 1: startColIndex + srcColSize -1;

            for(int j= 0 ; j<= acrossCols; j++)
            {
                if((i == acrossRows && remRows == 0) || (j == acrossCols && remCols == 0)){
                    continue;
                }
                Range range = new Range(destRange.getSheet(),startRowIndex,startColIndex,endRowIndex,endColIndex);
                destinationRanges.add(range);
                startColIndex += srcColSize;
                endColIndex = (j == acrossCols-1) ? endColIndex + remCols : endColIndex + srcColSize;
            }
            startRowIndex += srcRowSize;
            endRowIndex = (i == acrossRows-1) ? endRowIndex + remRows : endRowIndex + srcRowSize;
        }
        return destinationRanges;
    }

    public static List<Range> trimRangesWithLimit(Workbook workbook, List<DataRange> listOfDataRanges, int availableCellLimit)
    {
        List<Range> suitableRanges = new ArrayList<>();
        for (int i = 0; i < listOfDataRanges.size() && availableCellLimit > 0; i++) {
            DataRange dataRange = listOfDataRanges.get(i);
            Sheet sheet = workbook.getSheetByAssociatedName(dataRange.getAssociatedSheetName());
            trimRangeWithLimit(dataRange, availableCellLimit);
            suitableRanges.add(new Range(sheet, dataRange.getStartRowIndex(), dataRange.getStartColIndex(), dataRange.getEndRowIndex(), dataRange.getEndColIndex()));
            availableCellLimit -= dataRange.getSize();
        }
        return suitableRanges;
    }
    public static void trimRangeWithLimit(DataRange dataRange, int availableLimit) {
        int rowCellCount = dataRange.getEndColIndex() - dataRange.getStartColIndex() + 1;
        if(availableLimit < dataRange.getSize()) {
            int endRow = dataRange.getStartRowIndex() + (availableLimit / rowCellCount);
            endRow = (availableLimit % rowCellCount == 0) ? endRow - 1 : endRow;
            dataRange.setEndRowIndex(endRow);
        }
    }

    public static List<DataRange> trimDataRangesWithLimit(List<DataRange> listOfDataRanges, int limit)
    {
        List<DataRange> trimmedRanges = new ArrayList<>();
        for(int i = 0; i < listOfDataRanges.size() && limit > 0; i++)
        {
            DataRange dataRange = listOfDataRanges.get(i);
            trimRangeWithLimit(dataRange, limit);
            trimmedRanges.add(dataRange);
            limit -= dataRange.getSize();
        }
        return trimmedRanges;
    }

//    public static DataRange excludeRanges(DataRange tableRange, List<List<DataRange>> inputRanges)
//    {
//        int startRowIndex = tableRange.getStartRowIndex();
//        int endRowIndex = tableRange.getEndRowIndex();
//        int startColumnIndex = tableRange.getStartColIndex();
//        int endColumnIndex = tableRange.getEndColIndex();
//
//        for (List<DataRange> inputRange : inputRanges) {
//            for (DataRange range : inputRange) {
//                int inputStartRowIndex = range.getStartRowIndex();
//                int inputEndRowIndex = range.getEndRowIndex();
//                int inputStartColumnIndex = range.getStartColIndex();
//                int inputEndColumnIndex = range.getEndColIndex();
//
//                // Check if input range overlaps with table range
//                if (inputStartRowIndex <= endRowIndex && inputEndRowIndex >= startRowIndex
//                    && inputStartColumnIndex <= endColumnIndex && inputEndColumnIndex >= startColumnIndex) {
//                    // Update the table range based on the overlapping portion
//
//                    // Top portion
//                    if (inputStartRowIndex > startRowIndex) {
//                        startRowIndex = inputStartRowIndex;
//                    }
//
//                    // Bottom portion
//                    if (inputEndRowIndex < endRowIndex) {
//                        endRowIndex = inputEndRowIndex;
//                    }
//
//                    // Left portion
//                    if (inputStartColumnIndex > startColumnIndex) {
//                        startColumnIndex = inputStartColumnIndex;
//                    }
//
//                    // Right portion
//                    if (inputEndColumnIndex < endColumnIndex) {
//                        endColumnIndex = inputEndColumnIndex;
//                    }
//                }
//            }
//        }
//
//        return new DataRange(tableRange.getAssociatedSheetName(), startRowIndex , startColumnIndex, endRowIndex, endColumnIndex);
//    }

    public static List<DataRange> excludeRanges(DataRange tableRange, List<DataRange> inputRanges) {
        List<DataRange> remainingRanges = new ArrayList<>();
        remainingRanges.add(tableRange); // Start with the table range

        for (DataRange inputRange : inputRanges) {
            List<DataRange> newRemainingRanges = new ArrayList<>();

            for (DataRange remainingRange : remainingRanges) {
                int startRowIndex = remainingRange.getStartRowIndex();
                int endRowIndex = remainingRange.getEndRowIndex();
                int startColumnIndex = remainingRange.getStartColIndex();
                int endColumnIndex = remainingRange.getEndColIndex();

                int inputStartRowIndex = inputRange.getStartRowIndex();
                int inputEndRowIndex = inputRange.getEndRowIndex();
                int inputStartColumnIndex = inputRange.getStartColIndex();
                int inputEndColumnIndex = inputRange.getEndColIndex();

                // Check if input range overlaps with remaining range
                if (inputStartRowIndex <= endRowIndex && inputEndRowIndex >= startRowIndex
                    && inputStartColumnIndex <= endColumnIndex && inputEndColumnIndex >= startColumnIndex) {

                    // Exclude input range from the remaining range

                    // Top portion
                    if (inputStartRowIndex > startRowIndex) {
                        DataRange topRange = new DataRange(tableRange.getAssociatedSheetName(), startRowIndex,  startColumnIndex, inputStartRowIndex - 1,endColumnIndex);
                        newRemainingRanges.add(topRange);
                    }

                    // Bottom portion
                    if (inputEndRowIndex < endRowIndex) {
                        DataRange bottomRange = new DataRange(tableRange.getAssociatedSheetName(), inputEndRowIndex + 1, startColumnIndex, endRowIndex, endColumnIndex);
                        newRemainingRanges.add(bottomRange);
                    }

                    // Left portion
                    if (inputStartColumnIndex > startColumnIndex) {
                        DataRange leftRange = new DataRange(tableRange.getAssociatedSheetName(), inputStartRowIndex, startColumnIndex, inputEndRowIndex, inputStartColumnIndex - 1);
                        newRemainingRanges.add(leftRange);
                    }

                    // Right portion
                    if (inputEndColumnIndex < endColumnIndex) {
                        DataRange rightRange = new DataRange(tableRange.getAssociatedSheetName(), inputStartRowIndex, inputEndColumnIndex + 1, inputEndRowIndex, endColumnIndex);
                        newRemainingRanges.add(rightRange);
                    }
                } else {
                    newRemainingRanges.add(remainingRange); // No overlap, add the entire remaining range
                }
            }

            remainingRanges = newRemainingRanges;
        }

        return remainingRanges;
    }

    public static List<DataRange> excludeRanges(List<DataRange> dataRangeList, List<DataRange> inputRanges)
    {
        List<DataRange> excludedDataRanges = new ArrayList<>();
        for(DataRange dataRange : dataRangeList)
        {
            excludedDataRanges.addAll(excludeRanges(dataRange, inputRanges));
        }
        return excludedDataRanges;
    }

    public static List<DataRange> duplicateDataRanges(List<DataRange> dataRangeList)
    {
        List<DataRange> dataRanges = new ArrayList<>();
        for(DataRange dataRange : dataRangeList)
        {
            dataRanges.add(dataRange.clone());
        }
        return dataRanges;
    }

    public static boolean isValidRange(DataRange dataRange)
    {
        int sR = dataRange.getStartRowIndex();
        int eR = dataRange.getEndRowIndex();
        int sC = dataRange.getStartColIndex();
        int eC = dataRange.getEndColIndex();
        if(sR < 0 || sR >= Utility.MAXNUMOFROWS)
        {
            return false;
        }
        if(eR < 0 || eR >= Utility.MAXNUMOFROWS)
        {
            return false;
        }
        if(sC < 0 || sC >= Utility.MAXNUMOFCOLS)
        {
            return false;
        }
        if(eC < 0 || eC >= Utility.MAXNUMOFCOLS)
        {
            return false;
        }
        return sR <= eR && sC <= eC;
    }

    public static JSONObjectWrapper getSelectionJson(List<DataRange> dataranges)
    {
        JSONObjectWrapper selectionJson = new JSONObjectWrapper();
        String associatedSheetName = dataranges.get(0).getAssociatedSheetName();
        selectionJson.put(JSONConstants.SHEETLIST, ActionJsonUtil.addSheetInJsonArray(null, associatedSheetName, true));
        selectionJson.put(JSONConstants.RANGELIST, ActionJsonUtil.addDataRangesInJsonArray(null,dataranges,true));
        return selectionJson;
    }
}

