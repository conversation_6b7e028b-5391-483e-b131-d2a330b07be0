/* $Id$ */

package com.adventnet.zoho.websheet.model.util;

/**
 *
 * <AUTHOR>
 */
public class DataAPIConstants
{
    public static final String GET = "get";                         //No I18N
    public static final String POST = "post";                       //No I18N
    public static final String HEADER_ALLOW = "Allow";              //No I18N
    public static final String SUCCESS = "success";                 //No I18N
    public static final String FALIURE = "failure";                 //No I18N
    public static final String ABORT = "abort";                 //No I18N
    public static final String PARTIAL_SUCCESS = "partial_success"; //No I18N
    public static final String STATUS = "status";                   //No I18N
    public static final String ERROR_CODE = "error_code";           //No I18N
    public static final String ERROR_MESSAGE = "error_message";     //No I18N
    public static final String ERROR_HEADER = "error_header";
    
    //used for JSONObject key
    public static final String EXPECTED = "expc";                   //No I18N
    public static final String MESSAGE = "msg";                     //No I18N
    public static final String ARGUMENT = "arg";                    //No I18N
    public static final String RESPONSE_STATUS = "rs";              //No I18N
    public static final String RESPONSE_BODY = "rb";                //No I18N
    public static final String ERROR = "err";                       //No I18N
    public static final String METHOD = "mtd";                      //No I18N
    public static final String CODE = "code";                       //No I18N
    public static final String ACCESS_TOKEN = "at";                 //No I18N
    public static final String ACCESS_TOKEN_VALIDITY = "at_val";    //No I18N
    public static final String TIME_TO_LIVE = "ttl";                //NO I18N
    
    public static final int STATUS_INITIATE = 0;
    public static final int STATUS_COMPLETED = 1;
    public static final int STATUS_ABORT = 2;
    public static final int STATUS_SCHEDULE = 3;
    
    public static final int STATUS_ERROR_MAIL_MERGE_LIMIT_EXCEEDED = 10; //Daily mail limit for your merge field action exceeded.
    public static final int STATUS_ERROR_MAIL_LIMIT_EXCEEDED = 11;      
    public static final int STATUS_ERROR_MAIL_DOC_LENGTH = 12;  //Document length exceeding 7 lakh characters won't be processed in mail merge.
    
    public static final int STATUS_ERROR_MAIL_TO = 20;          
    public static final int STATUS_ERROR_MAIL_TO_INVALID = 21;  //The "To" address is found to be invalid.
    public static final int STATUS_ERROR_MAIL_TO_BLOCKED = 22;  //Failure in sending email. Invalid / blocked recipients mail address present.
    public static final int STATUS_ERROR_MAIL_TO_EMPTY = 23;    //No receipent present
    
    public static final int STATUS_ERROR_MAIL_ATTACHMENT = 30;  //Attachment failed. An Error occurred when uploading the attachment.
    public static final int STATUS_ERROR_MAIL_ATTACHMENT_NAME_LENGTH_EXCEEDED = 31; //Mail sending failed. Attachment name exceeds the allowed limit of 150 characters.
    public static final int STATUS_ERROR_MAIL_ATTACHMENT_SIZE_EXCEEDED = 32;    //Attachments size exceeded. Attachments size greater than allowed limit.
    public static final int STATUS_ERROR_MAIL_ATTACHMENT_UNSUPPORTED_FILEFORMAT = 33;   //Mail sending aborted. Attachment contains unsupported file format.
    
    
    public static final int STATUS_ERROR_MAIL_SEND = 40;    //The email could not be sent.
    public static final int STATUS_ERROR_MAIL_SEND_PARAM_MISSING = 41;  //Mail sending was aborted. Mandatory parameter was missing.
    public static final int STATUS_ERROR_MAIL_HARD_BOUNCE = 42; //Mail hard bounce. Please check the recipient’s email address.
    public static final int STATUS_ERROR_MAIL_SOFT_BOUNCE = 43; //Mail soft bounce.
    
    public static final int STATUS_ERROR_MAIL_HTML_CREATE = 50; //Failed to create HTML.
    public static final int STATUS_ERROR_MAIL_INVALID_CHRACTER = 51;    //Unable to send mail, mandatory parameter values are invalid.
    public static final int STATUS_ERROR_MAIL_ABORT_SPAM_CONTENT = 52;  //Mail sending was aborted. Mail seems to have spam content.
    public static final int STATUS_ERROR_MAIL_ABORT_SPAM_ACC = 52;      //Mail sending was aborted. Your account has been flagged as spam.
    
    public static final int STATUS_ERROR_MAIL_SENDER_UNCONFIRMED = 60;  //Unconfirmed sender email. Sender email address not yet verified.
    public static final int STATUS_ERROR_MAIL_SENDER_INACTIVE = 61;     //Mail sending aborted. Sender account is inactive.
    
    public static final int STATUS_ERROR_MAIL_EMAILPOLICY_DISABLE = 70; //Email policy in Organization is disabled. Please contact your organization administrator.
    public static final int STATUS_ERROR_MAIL_ORG_MAIL_BLOCK = 71;  //Email has been blocked for this organization. Please contact your organization administrator.
    
    public static final int STATUS_ERROR_MAIL_FROM_VERIFY = 80; //Sender email address not yet verified.
    public static final int STATUS_ERROR_MAIL_FROM_DMARC_VERIFICATION = 81; //DMARC verification failed for 'From' address
    
    public static final int STATUS_ERROR_FOLDER_PERMISSION = 90;    //Permission denied for the selected folder.
    public static final int SKU_DAILY_LIMIT_REACHED = 91;
    public static final int SKU_CREDITPOINTS_LIMIT_REACHED = 92;
    public static final int NO_SOURCE_DATA = 93;
    public static final int NO_SOURCE_DATA_AFTER_FILTER = 94;
    public static final int CONNECTION_FAILED = 95;

    public static final int STATUS_ERROR_FETCH_SOURCE_DATA = 100; //Failed to fetch the source data
    
    public static final int STATUS_ERROR_CREATE_DOC = 110; //Failed while creating new document
    
    public static final int STATUS_ERROR_INTERNAL = 120;  //Internal server error
    
    

    public static final String KEY_RESPONSE = "response";//No I18N
}