//$Id$
package com.adventnet.zoho.websheet.model.util;

import com.adventnet.zoho.websheet.model.util.Discussions.Action;

/**
* <AUTHOR>
* @Notes Used to do the parse the discussion/comment content
*/
public class DiscussionActionGuideline {

	// Process conditions
	boolean updateSheetObject = true;
	boolean updateDBEntry = true;
	boolean updateNotificationSettings =  true;
	boolean sendWMSMessage = true;
	boolean sendPushNotification =true;
	
	DiscussionActionGuideline(Action action, boolean isGuest){
		
		if(isGuest) {
			updateNotificationSettings =  false;
		}
		
		switch(action) {
		
		case ADD:
		
			// Allowing all actions
			
			break;
		
//		case UPDATE_CONTENT:
//			
//			this.updateSheetObject = false;
//			break;
			
		case UPDATE_RANGE_CONTENT:
			
			this.sendPushNotification=false;
			break;
			
//		case UPDATE_RANGE:
//			
//			this.updateNotificationSettings = false;
//			break;
			
		case UPDATE_SHEET:
			
			this.updateNotificationSettings = false;
			this.updateSheetObject = false;
			this.sendPushNotification=false;
			this.sendWMSMessage = false;
			break;
		
		case DELETE:

			this.sendPushNotification=false;
			//this.updateNotificationSettings = false;
			break;
			
		case ADD_REPLY:
			
			this.updateSheetObject = false;
			break;
			
		case DELETE_REPLY:
			
			this.sendPushNotification=false;
			this.updateSheetObject = false;
			break;
			
		case UPDATE_REPLY:

			this.sendPushNotification=false;
			this.updateSheetObject = false;
			break;

		case LIKE_REPLY:
		case LIKE:

			this.updateSheetObject = false;
			this.updateNotificationSettings = false;
			break;
			
		case UNLIKE_REPLY:
		case UNLIKE:

			this.sendPushNotification=false;
			this.updateSheetObject = false;
			this.updateNotificationSettings = false;
			break;
			
		case DELETE_ALL:
			
			this.sendPushNotification=false;
			this.updateNotificationSettings = false;
			break;
		default:
			this.sendPushNotification = false;
			break;
		
		}
	}
	
	public String toString(){
		return (this.updateSheetObject +" :: " + this.updateDBEntry +" :: " + this.updateNotificationSettings +" :: " + this.sendWMSMessage +" :: " + this.sendPushNotification);
	}
		
}
