//$Id$
package com.adventnet.zoho.websheet.model.util;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.adventnet.iam.*;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.zoho.zfsng.client.ZohoFS;

import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.SharedUserInfoUtil;
import com.zoho.sheet.util.ZSIAmUtil;
import com.zoho.zfsng.constants.ShareConstants;

/**
* <AUTHOR>
* @Notes Used to do the parse the discussion/comment content
*/
public class DiscussionContentParser {
	
	public static final Logger LOGGER = Logger.getLogger(DiscussionContentParser.class.getName());
	
	private String content;
	private Long docId;
	private String docOwnerZuid;
	
	String databaseContent;
	String responseContent;
	String mailContent;
	String mentionedMembers = "";
	String resourceId;

	String docsSpaceId;
	Discussions dsn;
	String performerId;
	Boolean avoidDecode = false;
	String unSharedMentionedMembers = "";
	
	private Set sharedUsers = new HashSet<String>();
	
	DiscussionContentParser(String content, Long docId, String docOwnerZuid, String docsSpaceId, String resourceId, String performerId, Boolean avoidDecode) throws Exception{
		
		this.content = (avoidDecode) ? content : this.decode(content);
		this.docId = docId;
		this.docsSpaceId = docsSpaceId;
		this.docOwnerZuid = docOwnerZuid;
		this.resourceId = resourceId;
		this.performerId = performerId;
		this.dsn = new Discussions(docId.toString(), docOwnerZuid, docsSpaceId, resourceId, performerId);
//		this.sharedUsers.add(docOwnerZuid);	
		this.avoidDecode = avoidDecode;
	}
	
	DiscussionContentParser(String content, Boolean avoidDecode) {
		/* Used for Parsing the content */
		this.content = (avoidDecode) ? content : this.decode(content);
		this.avoidDecode = avoidDecode;
	}
	
	private String decode(String content){ 

		this.content = URLDecoder.decode(content);	
		Pattern patter = Pattern.compile("(&apos;)|(&#39;)|(%27)");
		Matcher matcher = patter.matcher(this.content);
		this.content = matcher.replaceAll("'");
		
		this.content = ClientUtils.replaceJsonSpclChars(this.content);
		return this.content;
	}
	
	public DiscussionContentParser parse() {
		
		Pattern pattern = Pattern.compile("(&lt;user:mention&gt;)([0-9$]+):(.+?)(&lt;/user:mention&gt;)");
		
		StringBuffer dbContentBuffer = new StringBuffer();
		StringBuffer mailContentBuffer = new StringBuffer();
		boolean isPersonallySharedUsersPopulated = false;
		boolean isGroupSharedUsersPopulated = false;
		boolean isPersonalContactsPopulated = false;
		IAMProxy proxy = IAMProxy.getInstance();
		UserAPI userAPI = proxy.getUserAPI();
		User user = null;

		String content = this.content;
//		content = decodeHTML(content);
		
		Matcher dbMatcher = pattern.matcher(content);
		Matcher mailMatcher = pattern.matcher(content);
		while(dbMatcher.find()) { 
			
			boolean isValidUser = false;
			
			if(!isPersonallySharedUsersPopulated) {
				this.populatePersonallySharedUsers();
				isPersonallySharedUsersPopulated = true;
			}
			
			String zuid = dbMatcher.group(2);
			String userName = dbMatcher.group(3);
//			try{
//				userName = DocumentUtils.getZFullName(zuid);
//			}catch(Exception e){
//				LOGGER.log(Level.WARNING, "Unable to get the user's full name:" + zuid);
//			}
			isValidUser = this.sharedUsers.contains(zuid);
			
			if(!isValidUser && !isGroupSharedUsersPopulated) {
				
				this.populateGroupSharedUsers();
				isGroupSharedUsersPopulated = true;
				
				isValidUser = this.sharedUsers.contains(zuid);
	        	
			}
			
			if(isValidUser) {
				this.mentionedMembers = this.mentionedMembers + "," + zuid; 
			}
			String mailId = "";
			UserContact contact = null;
			if(!isValidUser) {
				try {
					user = IAMProxy.getInstance().getUserAPI().getUserFromZUID(zuid);
					mailId = user.getContactEmail();
					contact = IAMProxy.getInstance().getContactAPI().getContact(Long.parseLong(this.performerId), mailId); //No I18N;
				} catch (IAMException e) {
					LOGGER.log(Level.WARNING,"Exception on getting UserInfo for Discussion @mention",e);
				}
				if(contact != null) {
					isValidUser = true;
				}
//				this.populatePersonalContactUsers();
//				isPersonalContactsPopulated = true;

				this.unSharedMentionedMembers = isValidUser ? this.unSharedMentionedMembers + "," + zuid : this.unSharedMentionedMembers; 
	        	
			}
	        	
			if(isValidUser) {
				dbMatcher.appendReplacement(dbContentBuffer, ("<user:mention>" + zuid.replace("$", "\\$") + ":" + userName+ "</user:mention>")); // note:replaceAll wont fix this //NO I18N
				if(mailMatcher.find()){
					mailMatcher.appendReplacement(mailContentBuffer, ("@<a href=\"mailto:" + getUserMailId(zuid, this.resourceId) + "\">" + userName+ " </a>")); //NO I18N
				}
			}
			
		}
		dbMatcher.appendTail(dbContentBuffer);
		this.databaseContent = dbContentBuffer.toString();
		this.responseContent = this.changeNewLineToBr(this.databaseContent);
		this.databaseContent = URLEncoder.encode(this.changeNewLineToBr(this.databaseContent));
		this.parseAtMailIds();

		mailMatcher.appendTail(mailContentBuffer);
		this.mailContent = changeNewLineToBr(mailContentBuffer.toString());
		return this;
	}

	private String getUserMailId(String zuid, String resourceId) {
		String mailId = "";
		try {
			if(zuid.startsWith("$")) {
				LOGGER.info("zuid::" + zuid);
				JSONObjectWrapper otpShareUserJson = new JSONObjectWrapper();
				String ownerID = null;
				ownerID = ZohoFS.getOwnerZID(resourceId);
				mailId = ZohoFS.getGuestName(ownerID, zuid);
				LOGGER.info("ownerID::" + ownerID + "guestName::" + mailId);
			} else {
				mailId = DocumentUtils.getZuserEmailIdFromZuid(zuid);
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return mailId;
	}
	
	private String decodeHTML(String dbContent)
	{
		return dbContent.replaceAll("&lt;", "<").replaceAll("&gt;", ">");
	}

	private String changeNewLineToBr(String dbContent){
		
		return dbContent.replaceAll("\n", "<br>");
	}
	
	private String changeBrToNewLine(String dbContent){
		
		return dbContent.replaceAll("&lt;br&gt;", "\n"); //No I18N
	}
	
	private void populatePersonallySharedUsers() {
		populateSharedUsers(ShareConstants.SHAREDTYPE_PERSONAL);		
	}

	private void populateGroupSharedUsers() {
		populateSharedUsers(ShareConstants.SHAREDTYPE_GROUP);
	}
	
	/*private void populatePersonalContactUsers() {
		populatePersonalContacts();
	}*/

	private void populateSharedUsers(int shareType) {
	
		SharedUserInfoUtil SharedUserInfo = new SharedUserInfoUtil(this.resourceId, this.performerId, this.docsSpaceId);
		JSONArrayWrapper shUsers;
		try {
			shUsers = SharedUserInfo.get(this.resourceId, this.performerId, this.docsSpaceId, 0); // SHARED USERS DETAILS (0) to getAllSharedUsers including Group
		    Iterator itr = shUsers.iterator();
		    while (itr.hasNext())
		    {
				JSONObjectWrapper json = (JSONObjectWrapper) itr.next();
		    	this.sharedUsers.add(json.getString("id"));
		    }
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
//		try {
//			String rid = DocumentUtils.getResourceId(this.docId.toString());
//			int reslimit = ZohoFS.getResourcePermissionsCount(this.docOwnerZuid, rid, "-1", list);
//			if(reslimit != 0) {
//				JSONArray sharedRPermissions = JSONArray.fromString(ZohoFS.getResourcePermissions(docOwnerZuid, rid, "-1", list, 0, reslimit));
//                for(int i=0; i<sharedRPermissions.length(); i++) {
//		        	this.sharedUsers.add(sharedRPermissions.getJSONObject(i).getString("shared_to"));
//				}
//			}
//			
//		} catch (Exception e) {
//			LOGGER.log(Level.WARNING, "Problem while populating shared users : " + list, e);
//		}
	}
	
	/*private void populatePersonalContacts() {
        
        Iterator<String> keys = this.dsn.populatePersonalContacts().keys();

        while(keys.hasNext()) {
        	this.sharedUsers.add(keys.next());
        }
	}*/
	
	public String parsedbToMailContent() { 
		
		Pattern pattern = Pattern.compile("(&lt;user:mention&gt;)([0-9$]+):(.+?)(&lt;/user:mention&gt;)");
		StringBuffer dbToMAilContentBuffer = new StringBuffer();
		Matcher userSuggMatcher = pattern.matcher(this.content);
		
		while(userSuggMatcher.find()) { 
			String zuid = userSuggMatcher.group(2);
			String userName = userSuggMatcher.group(3);
			userSuggMatcher.appendReplacement(dbToMAilContentBuffer, ("@<a href=\"mailto:" + getUserMailId(zuid, this.resourceId) + "\">" + userName+ "</a>")); //NO I18N
		}
		userSuggMatcher.appendTail(dbToMAilContentBuffer);
		return ClientUtils.revertSpclChars(dbToMAilContentBuffer.toString());
	}
	
	public DiscussionContentParser parseContent() {
		
		Pattern pattern = (this.avoidDecode) ? Pattern.compile("(<user:mention>)([0-9$]+):(.+?)(</user:mention>)") : Pattern.compile("(&lt;user:mention&gt;)([0-9$]+):(.+?)(&lt;/user:mention&gt;)");
		Matcher userSuggMatcher = pattern.matcher(this.content);
		
		while(userSuggMatcher.find()) { 
			String zuid = userSuggMatcher.group(2);
			this.mentionedMembers += (("".equals(this.mentionedMembers)) ?  (zuid) : (","+zuid));
		}
		return this;
	}
	
	public void parseAtMailIds() {
		
//		Pattern pattern = Pattern.compile("/((@)[a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\\.[a-zA-Z0-9_-]+)/gi");//a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~
//		String regex = "^(@)([_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,}))$";  //No I18N
		String regex = "(@)([_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9.!#$%&'*+-/=?^_`{|}~]+)*@[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,}))";  //No I18N
		Pattern pattern = Pattern.compile(regex);
		Matcher atMailIdsMatcher = pattern.matcher(this.content);
	    while (atMailIdsMatcher.find()) {
	    	String mailId = atMailIdsMatcher.group(2);
	    	User user = null;
			try {
				user = IAMProxy.getInstance().getUserAPI().getUser(mailId);
			} catch (IAMException e) {
				// TODO Auto-generated catch block
				LOGGER.log(Level.WARNING,"Exception on getting mentioned mailIds UserObj",e);
			}
	    	if(user != null) {
	    		String memberZuid = String.valueOf(user.getZuid());
	    		LOGGER.info("VALID USER FROM @MAILID::"+memberZuid);
	    		this.unSharedMentionedMembers += (("".equals(this.unSharedMentionedMembers)) ?  (memberZuid) : ("," + memberZuid));
	    	}
	    }
	    LOGGER.info("this.unSharedMentionedMembers @MAILID::"+ this.unSharedMentionedMembers);
	}

	//public static void main(String args[]) {
		
		//DiscussionContentParser dcp = new DiscussionContentParser("Hello, &lt;user:mention&gt;3549:Sasikumar&lt;/user:mention&gt; and", Long.valueOf("8000000139003"), "3549");
		//System.out.println("Mailcontent"+dcp.mailContent);
		//System.out.println("dbcontent"+dcp.databaseContent);
		
	//}
}
