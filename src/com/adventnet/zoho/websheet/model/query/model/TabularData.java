//$Id$
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.LinearIntegralRange;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.adventnet.zoho.websheet.model.util.ZSComparators;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by k<PERSON><PERSON>an-zt276 on  2019-05-20
 */
public class TabularData {

    private static final Logger LOGGER = Logger.getLogger(TabularData.class.getName());

    private final ZArrayI range;
    //private final BitSet rowIndices;
    private final boolean unGroupEmptyValues;
    private final Workbook workbook;

    private final boolean isSkipHiddenRows;
    private final boolean isSKipEmptyRows;

    private final Map<DataAggregatorResult, List<AggregatedNode>> aggregatedNodeMap = new HashMap<>();
    private final Map<DataAggregatorResult, List<AggregatedNode>> unsortedAggregatedNodeMap = new HashMap<>();

    protected TabularData(ZArrayI range, boolean skipHiddenRows, boolean skipEmptyRows, boolean unGroupEmptyValues, Workbook workbook) {
        if(range == null){
            throw new IllegalArgumentException("Invalid Range");  // No I18N
        }
        this.range = range;
        this.isSkipHiddenRows = skipHiddenRows;
        this.isSKipEmptyRows = skipEmptyRows;
        //this.rowIndices = new BitSet();
        //IntStream.range(0, range.getRowSize()).forEach(rowIndex -> rowIndices.set(rowIndex));
        this.unGroupEmptyValues = unGroupEmptyValues;
        this.workbook = workbook;
    }

    private void validateGroupByCriteria(GroupByCriteria groupByCriteria){
        if(!(groupByCriteria.getAggregationRange() == null ? groupByCriteria.getColIndex() < range.getColSize() : groupByCriteria.getAggregationRange().getRowSize() == range.getRowSize())) {
            throw new IllegalArgumentException(">>>>> Invalid Range || ColIndex"); // No I18N
        }
    }

    // Synchronized to fix highcpu while race conditions.
    // We use TabularData for Duplicate values CF and generate response in parallel threads that all will access this Tabulatdata in parallel.
    private synchronized List<AggregatedNode> getAggregatedNodes(AggregatedNode node, GroupByCriteria groupByCriteria, GroupFilterCriteria groupFilterCriteria) {

        DataAggregatorResult nodeGroupAndFilterKey = new DataAggregatorResult(node,this);
        nodeGroupAndFilterKey.setGroupByCriteria(groupByCriteria);
        nodeGroupAndFilterKey.setGroupFilterCriteria(groupFilterCriteria);
        List<AggregatedNode> nodeGroupedFilteredNodes = aggregatedNodeMap.get(nodeGroupAndFilterKey);
        if (nodeGroupedFilteredNodes == null) {
            List<AggregatedNode> groupedFilteredNodes;
            if(groupFilterCriteria == null)
            {
                groupedFilteredNodes = getAggregatedNodes(groupByCriteria);
                nodeGroupedFilteredNodes = node.getIntersectingNodes(groupedFilteredNodes);
            }
            else {

                if(groupFilterCriteria.isSpecialFilterCondition()){
                    List<AggregatedNode> groupedNodes = getAggregatedNodes(node, groupByCriteria, null);
                    nodeGroupedFilteredNodes = groupFilterCriteria.filter(groupedNodes, workbook);
                }
                else{
                    groupedFilteredNodes = getAggregatedNodes(groupByCriteria, groupFilterCriteria);
                    nodeGroupedFilteredNodes = node.getIntersectingNodes(groupedFilteredNodes);
                }
            }

            aggregatedNodeMap.put(nodeGroupAndFilterKey, nodeGroupedFilteredNodes);
        }
        return nodeGroupedFilteredNodes;
    }

    // Synchronized to fix highcpu while race conditions.
    // We use TabularData for Duplicate values CF and generate response in parallel threads that all will access this Tabulatdata in parallel.
    private synchronized List<AggregatedNode> getAggregatedNodes(GroupByCriteria groupByCriteria, GroupFilterCriteria groupFilterCriteria) {
        DataAggregatorResult groupAndFilterKey = new DataAggregatorResult(groupByCriteria, groupFilterCriteria,this);
        List<AggregatedNode> groupFilteredNodes = aggregatedNodeMap.get(groupAndFilterKey);

        if (groupFilteredNodes == null) {
            List<AggregatedNode> groupedNodes;
//            if(groupFilterCriteria instanceof GroupValueFilter && (((GroupValueFilter) groupFilterCriteria).groupFilterOperator == GroupValueFilter.GroupFilterOperator.FIRST_MATCH || ((GroupValueFilter) groupFilterCriteria).groupFilterOperator == GroupValueFilter.GroupFilterOperator.LAST_MATCH)){
//                groupedNodes = getAggregatedNodesUnsorted(groupByCriteria);
//            }else {
                groupedNodes = getAggregatedNodes(groupByCriteria);
//            }

            if(groupFilterCriteria instanceof GroupValueFilter && ((GroupValueFilter) groupFilterCriteria).isRelationalFilterCondition()){
                groupFilteredNodes = GroupValueFilter.relationalFilters(groupAndFilterKey, this.aggregatedNodeMap, groupedNodes, null, (GroupValueFilter)groupFilterCriteria);
            }else {
                groupFilteredNodes = groupFilterCriteria.filter(groupedNodes, workbook);
            }
            aggregatedNodeMap.put(groupAndFilterKey, groupFilteredNodes);
        }
        return groupFilteredNodes;
    }

    private List<AggregatedNode> getAggregatedNodesUnsorted(GroupByCriteria groupByCriteria) {
        return unsortedAggregatedNodeMap.computeIfAbsent(new DataAggregatorResult(groupByCriteria,this), k -> {
            List<AggregatedNode> sortedNodes = getAggregatedNodes(groupByCriteria);
            return (new GroupValueFilter.NodeComparator(SortOrder.NATURAL_ORDER, null)).sort(sortedNodes);
        });
    }

    private List<AggregatedNode> getAggregatedNodes(GroupByCriteria groupByCriteria) {
        return aggregatedNodeMap.computeIfAbsent(new DataAggregatorResult(groupByCriteria,this), k -> {
            validateGroupByCriteria(groupByCriteria);
            return doAggregation(groupByCriteria);
        });
    }

    public DataAggregatorResult getRootNode() {
        return new DataAggregatorResult(new AggregatedNode(null, null, 0, null, null),this);
    }

    public List<DataAggregatorResult> getGroupedNodes(DataAggregatorResult dataAggregatorResult, GroupByCriteria groupByCriteria) {

        return getGroupedNodes(dataAggregatorResult, groupByCriteria, null);
    }

    public List<DataAggregatorResult> getGroupedNodes(DataAggregatorResult dataAggregatorResult, GroupByCriteria groupByCriteria, GroupFilterCriteria groupFilterCriteria) {

        if(groupByCriteria.getAggregationRange() == null){
            groupByCriteria.setAggregationRange(range);
        }

        List<AggregatedNode> resultNodes;
        if(groupFilterCriteria != null && !groupFilterCriteria.isCacheable()) {
            List<AggregatedNode> groupedNodes = getAggregatedNodes(dataAggregatorResult.getAggregatedNode(), groupByCriteria, null);
            resultNodes = groupFilterCriteria.filter(groupedNodes, workbook);
        }else{
            resultNodes = getAggregatedNodes(dataAggregatorResult.getAggregatedNode(), groupByCriteria, groupFilterCriteria);
        }

        List<DataAggregatorResult> results = new ArrayList<>();
        for(AggregatedNode node : resultNodes){
            results.add(new DataAggregatorResult(node,this));
        }
        dataAggregatorResult.setGroupByCriteria(groupByCriteria);
        dataAggregatorResult.setGroupFilterCriteria(groupFilterCriteria);
        dataAggregatorResult.setNextLevelNodes(results);
        return results;
    }

    public List<Value> getUniqueValues(GroupByCriteria groupByCriteria){

        if(groupByCriteria.getAggregationRange() == null){
            groupByCriteria.setAggregationRange(range);
        }

        List<AggregatedNode> aggregatedNodes = aggregatedNodeMap.get(new DataAggregatorResult(groupByCriteria,this));
        if(aggregatedNodes == null){
            throw new IllegalArgumentException("GROUPBY_CRITERIA_NOT_YET_ADDED"); // No I18N
        }
        List<Value> uniqueValues = new ArrayList<>();
        aggregatedNodes.forEach(a -> {
            uniqueValues.add(a.getValue());
        });
        return uniqueValues;
    }

    private List<AggregatedNode> doAggregation(GroupByCriteria groupByCriteria)  {
        ZArrayI aggregationRange = groupByCriteria.getAggregationRange();
        Map<Value, BitSet> aggregateMap = new HashMap<>();

        boolean isTranspose = aggregationRange instanceof TransposedZArrayI;
        boolean isRangeCase = (isTranspose ? ((TransposedZArrayI)aggregationRange).getContaingZArrayI() : aggregationRange) instanceof Range;
        long startTime = System.currentTimeMillis();
        int count = 0;
        int defaultBitSize = Math.min(aggregationRange.getRowSize(), 4096); // (262144/64)
        if(isRangeCase)
        {
            Range range = (Range)(isTranspose ? ((TransposedZArrayI)aggregationRange).getContaingZArrayI() : aggregationRange);
            // Change the range into single column/row range accordingto isTranspose. So that aggregarion will freely work on that range.
            if(isTranspose && range.getRowSize() > 1)
            {
                int row = range.getStartRowIndex() + groupByCriteria.getColIndex();
                range = new Range(range.getSheet(), row, range.getStartColIndex(), row, range.getEndColIndex());
            }
            else if(!isTranspose && range.getColSize() > 1)
            {
                int col = range.getStartColIndex() + groupByCriteria.getColIndex();
                range = new Range(range.getSheet(), range.getStartRowIndex(), col, range.getEndRowIndex(), col);
            }

            int start = isTranspose ? range.getStartColIndex() : range.getStartRowIndex();

            // creating new Iterator for grouping colIndex of given range.
            Iterator<ReadOnlyCell> rCellIterator = new RangeIterator(range.getSheet(),
                                                                    range.getStartRowIndex(),
                                                                    range.getStartColIndex(),
                                                                    range.getEndRowIndex(),
                                                                    range.getEndColIndex(),
                                                                    RangeIterator.IterationStartPositionEnum.TOP_LEFT,
                                                     isSkipHiddenRows && !isTranspose ,
                                                                    isSKipEmptyRows,
                                                      isSkipHiddenRows && isTranspose,
                                                        false,
                                                     false,
                                                            true);
            while (rCellIterator.hasNext())
            {
                if(count % 100 == 0) {
                    long endTime = System.currentTimeMillis();
                    if (endTime - startTime > 5000) {
                        LOGGER.log(Level.INFO, "[DO_AGGREGATION] Total time taken to do Aggregate is {0} ms", new Object[]{endTime - startTime}); // NO I18N
                        throw new RuntimeException("Time taking for doAggregation limit reaches more than 5 secs"); // NO I18N
                    }
                }
                count++;
                boolean isLastRow = false;
                ReadOnlyCell rCell = rCellIterator.next();
                int index = isTranspose ? rCell.getColIndex() : rCell.getRowIndex();
                int repeated = isTranspose ? rCell.getColsRepeated() : rCell.getRowsRepeated();
                int usedIndex = isTranspose ? range.getSheet().getUsedColumnIndex() : range.getSheet().getUsedRowIndex();
                if(index > usedIndex)
                {
                    isLastRow = true;
                    repeated = (isTranspose ? range.getEndColIndex() : range.getEndRowIndex()) - index + 1;
                }
                Value value;
                if (rCell.getCell() == null) {
                    value = Value.EMPTY_VALUE;
                } else {
                    value = rCell.getCell().getValue();
                    if (value instanceof PicklistValue) {
                        value = Value.getInstance(value.getType(), value.getValue());
                    }
                    if(value.getType().isNumberType() && rCell.getCell().getContentType().isDateType())
                    {
                        value = Value.getInstance(rCell.getCell().getContentType(), DateUtil.convertNumberToDate((Number)value.getValue()));
                    }
                }
                Value groupLabelValue = groupByCriteria.getGroupingFunction().extractGroupValue(value, workbook);
                if(groupLabelValue != null) {
                    BitSet indices = aggregateMap.computeIfAbsent(groupLabelValue, k -> new BitSet(defaultBitSize));
                    indices.set(index - start, (index - start) + repeated);
                }
                if(isLastRow)
                {
                    break;
                }
            }
        }
        else
        {
            List<List<LinearIntegralRange>> usedAndUnusedRowsInNode = getUsedAndUnusedIndices(aggregationRange);
            /// Grouping values in used Indices.
            List<LinearIntegralRange> usedRowsInNode = usedAndUnusedRowsInNode.get(0);
            for (LinearIntegralRange linearIntegralRange : usedRowsInNode) {
                for (int rowIndex = linearIntegralRange.getStartInt(); rowIndex <= linearIntegralRange.getEndInt(); rowIndex++) {
                    if(count % 100 == 0) {
                        long endTime = System.currentTimeMillis();
                        if (endTime - startTime > 5000) {
                            LOGGER.log(Level.INFO, "[DO_AGGREGATION] Total time taken to do Aggregate is {0} ms", new Object[]{endTime - startTime}); // NO I18N
                            throw new RuntimeException("Time taking for doAggregation limit reaches more than 5 secs"); // NO I18N
                        }
                    }
                    count++;
                    Value value = aggregationRange.getValue(rowIndex, groupByCriteria.getColIndex());
                    Value groupLabelValue = groupByCriteria.getGroupingFunction().extractGroupValue(value, workbook);
                    if (groupLabelValue != null) {
                        aggregateMap.computeIfAbsent(groupLabelValue, k -> new BitSet(defaultBitSize)).set(rowIndex);
                    }
                }
            }

            List<LinearIntegralRange> unUsedRowsInNode = usedAndUnusedRowsInNode.get(1);
            Value groupLabelValue = null;
            boolean isExtracted = false;
            for (LinearIntegralRange linearIntegralRange : unUsedRowsInNode) {
                for (int rowIndex = linearIntegralRange.getStartInt(); rowIndex <= linearIntegralRange.getEndInt(); rowIndex++) {
                    if(count % 100 == 0) {
                        long endTime = System.currentTimeMillis();
                        if (endTime - startTime > 5000) {
                            LOGGER.log(Level.INFO, "[DO_AGGREGATION] Total time taken to do Aggregate is {0} ms", new Object[]{endTime - startTime}); // NO I18N
                            throw new RuntimeException("Time taking for doAggregation limit reaches more than 5 secs"); // NO I18N
                        }
                    }
                    count++;
                    if (!isExtracted) {
                        Value value = aggregationRange.getValue(rowIndex, groupByCriteria.getColIndex());
                        groupLabelValue = groupByCriteria.getGroupingFunction().extractGroupValue(value, workbook);
                        isExtracted = true;
                    }
                    if (groupLabelValue != null) {
                        aggregateMap.computeIfAbsent(groupLabelValue, k -> new BitSet(defaultBitSize)).set(rowIndex);
                    }
                }
            }
        }

        BitSet emptyRowindices = null;
        if(unGroupEmptyValues)
        {
            emptyRowindices = aggregateMap.remove(Value.EMPTY_VALUE);
        }

        List<Value> sortedValues = aggregateMap.keySet().stream()
                .sorted(new ZSComparators.ValueComparator(false, false))
                .collect(Collectors.toList());

        int sortIndex = 0;
        List <AggregatedNode> aggregatedNodes = new ArrayList<>();
        for (Value groupLabel : sortedValues) {

            BitSet rowIndices = aggregateMap.get(groupLabel);

            if(emptyRowindices != null)
            {
                rowIndices.or(emptyRowindices);
            }
            aggregatedNodes.add(new AggregatedNode(null, groupByCriteria, groupLabel.equals(Value.EMPTY_VALUE) ? null : sortIndex++, groupLabel, rowIndices));
        }
        return aggregatedNodes;

    }

    /*
     * @return the return List Contain only 2 Set<Integer> in this 1st one is usedIndices
     * and 2nd one is unUsedIndices
     */
    private static List<List<LinearIntegralRange>> getUsedAndUnusedIndices(ZArrayI aggregationRange){
        return isUsedIndices(aggregationRange, false, 0);
    }

    private static List<List<LinearIntegralRange>> isUsedIndices(ZArrayI zArrayI, boolean isTransposed, int preRangeSize){

        List<LinearIntegralRange> used =  new ArrayList<>();
        List<LinearIntegralRange> unUsed =  new ArrayList<>();
        if(zArrayI instanceof ZArray) {
            int size = isTransposed ? zArrayI.getColSize() : zArrayI.getRowSize();

            used.add(new LinearIntegralRange(preRangeSize, preRangeSize + size - 1));
        }else if(zArrayI instanceof Range) {
            Range tempRange = (Range)zArrayI;
            Sheet sheet = tempRange.getSheet();
            int sheetUsedIndex = isTransposed ? sheet.getUsedColumnIndex() : sheet.getUsedRowIndex();
            int rangeStartIndex = isTransposed ? tempRange.getStartColIndex() : tempRange.getStartRowIndex();
            int rangeEndIndex = isTransposed ? tempRange.getEndColIndex() : tempRange.getEndRowIndex();

            //List<LinearIntegralRange> result =  new ArrayList<>(2);
            if(rangeStartIndex <= sheetUsedIndex)
            {
                used.add(new LinearIntegralRange(preRangeSize, preRangeSize + (Math.min(sheetUsedIndex, rangeEndIndex) - rangeStartIndex)));
                preRangeSize += ((sheetUsedIndex - rangeStartIndex) + 1);
            }

            if(rangeEndIndex > sheetUsedIndex)
            {
                unUsed.add(new LinearIntegralRange(preRangeSize, preRangeSize + (rangeEndIndex - Math.max(sheetUsedIndex, rangeStartIndex))));
            }

        }else if(zArrayI instanceof TransposedZArrayI) {
            return isUsedIndices(((TransposedZArrayI)zArrayI).getContaingZArrayI(), !isTransposed, preRangeSize);
        }
//        else if(zArrayI instanceof StackedRange) {
//
//            StackedRange sRange = (StackedRange) zArrayI;
//            for(ZArrayI arrayI : sRange.getRanges()) {
//                List<List<LinearIntegralRange>> tempList = isUsedIndices(arrayI, isTransposed, preRangeSize);
//                used.addAll(tempList.get(0));
//                unUsed.addAll(tempList.get(1));
//
//                if(!isTransposed && sRange.getStackDirection() == StackedRange.StackDirection.VERTICAL) {
//                    preRangeSize += arrayI.getRowSize();
//                }else if(isTransposed && sRange.getStackDirection() == StackedRange.StackDirection.HORIZONTAL) {
//                    preRangeSize += arrayI.getColSize();
//                }
//            }
//
//        }
        else if(zArrayI instanceof ZArrayI) {
            int size = isTransposed ? zArrayI.getColSize() : zArrayI.getRowSize();

            used.add(new LinearIntegralRange(preRangeSize, preRangeSize + size - 1));
        }

        return Arrays.asList(used, unUsed);

    }

    public void dismantle() {
        this.aggregatedNodeMap.clear();
        this.unsortedAggregatedNodeMap.clear();
    }

}
