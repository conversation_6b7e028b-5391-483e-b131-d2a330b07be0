/* $Id$ */
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.Expression;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON>an-zt276 on  2019-04-09
 */
public class ExpressionFilter extends GroupFilterCriteria {
    private final Expression filterExpression;

    public ExpressionFilter(Expression filterExpression){
        this.filterExpression = filterExpression;
    }

    public List<AggregatedNode> filter(List<AggregatedNode> aggregatedNodes, Workbook workbook){
        List<AggregatedNode> filteredNodes = new ArrayList<>();
        for(AggregatedNode aggregatedNode : aggregatedNodes) {
            Value result = ExpressionsLibrary.getEvaluatedValue(filterExpression, aggregatedNode.getValue(), workbook);
            if (result.getValue().equals(1) || result.getValue().equals(Boolean.TRUE)){
                filteredNodes.add(aggregatedNode);
            }
        }
        return filteredNodes;
    }

    @Override
    protected boolean isSpecialFilterCondition() {
        return false;
    }

    protected boolean isCacheable(){
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (o == null || getClass() != o.getClass()){
            return false;
        }
        ExpressionFilter that = (ExpressionFilter) o;
        return Objects.equals(filterExpression, that.filterExpression);
    }

    @Override
    public int hashCode() {
        return Objects.hash(filterExpression);
    }
}
