/* $Id$ */
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.*;


import java.util.*;

/**
 * Created by k<PERSON><PERSON><PERSON>-zt276 on  2019-04-09
 */
public class Summary {

    public static enum SummarizeOperation {

        SUM,
        AVERAGE,
        MAX,
        MIN,
        COUNT,
        MEDIAN,
        UNIQUES;

    }

    private final ZArrayI singleColRange;
    private final SummarizeOperation summarizeOperation;

    public Summary(ZArrayI singleColRange, SummarizeOperation summarizeOperation) {
        if(singleColRange == null || singleColRange.getColSize() != 1){
            throw new IllegalArgumentException(">>>>> InvalidRange"); // No I18N
        }
        this.singleColRange = singleColRange;
        this.summarizeOperation = summarizeOperation;
    }

    public ZArrayI getSummaryRange() {
        return singleColRange;
    }

    public SummarizeOperation getSummarizeOperation() {
        return summarizeOperation;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if(o instanceof Summary){
            Summary that = (Summary)o;
            return this.singleColRange.equals(that.singleColRange)
                    && this.summarizeOperation.equals(that.summarizeOperation);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(singleColRange, summarizeOperation);
    }

    @Override
    public String toString() {
        return "range=" + singleColRange +", summarizeOperation=" + summarizeOperation; // No I18N
    }

}
