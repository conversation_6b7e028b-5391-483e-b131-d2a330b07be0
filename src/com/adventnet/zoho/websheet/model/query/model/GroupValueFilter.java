/* $Id$ */
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.functions.Comparative;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>-zt276 on  2019-04-09
 */
public class GroupValueFilter extends GroupFilterCriteria {
    private static final Logger LOGGER = Logger.getLogger(GroupValueFilter.class.getName());

    public static enum GroupFilterOperator {
        EQUALS,
        DOES_NOT_EQUALS,
        LESS_THAN,
        GREATER_THAN,
        LESS_THAN_OR_EQUAL_TO,
        GREATER_THAN_OR_EQUAL_TO,
        EXACT_OR_NEXT_LARGER,
        EXACT_OR_NEXT_SMALLER,
        BEGINS_WITH,
        CONTAINS,
        DOES_NOT_CONTAIN,
        ENDS_WITH,
        DOES_NOT_BEGIN_WITH,
        DOES_NOT_END_WITH,
        BOTTOM_PERCENT,
        BOTTOM_VALUES,
        TOP_PERCENT,
        TOP_VALUES,
    }

    protected interface FilterFunctions<D extends List<AggregatedNode>, S extends Summary, R extends Value>{
        public List<AggregatedNode> apply(D aggregatedNodes, S summary, R filterValue);
    }

    private interface StringFunctions<D extends List<AggregatedNode>, R extends Value>{
        public List<AggregatedNode> apply(D aggregatedNodes, R filterValue);
    }

    protected final static EnumMap<GroupFilterOperator, FilterFunctions<List<AggregatedNode>, Summary, Value>> FILTER_FUNCTIONS_MAP;
    private final static EnumMap<GroupFilterOperator, StringFunctions<List<AggregatedNode>, Value>> STRING_FILTERFUNCTIONS_MAP;

    static {
        FILTER_FUNCTIONS_MAP = new EnumMap<>(GroupFilterOperator.class);

        //Relative Functions..
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.EQUALS, GroupValueFilter::equals);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.DOES_NOT_EQUALS, GroupValueFilter::notEquals);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.GREATER_THAN, GroupValueFilter::greaterThan);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.GREATER_THAN_OR_EQUAL_TO, GroupValueFilter::greaterThanOrEqualTo);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.LESS_THAN, GroupValueFilter::lessThan);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.LESS_THAN_OR_EQUAL_TO, GroupValueFilter::lessThanOrEqualTo);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.EXACT_OR_NEXT_SMALLER, GroupValueFilter::equalOrNextSmaller);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.EXACT_OR_NEXT_LARGER, GroupValueFilter::equalOrNextLarger);

        //Spical Functions..
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.TOP_VALUES, GroupValueFilter::topValues);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.BOTTOM_VALUES, GroupValueFilter::bottomValues);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.TOP_PERCENT, GroupValueFilter::topPercent);
        FILTER_FUNCTIONS_MAP.put(GroupFilterOperator.BOTTOM_PERCENT, GroupValueFilter::bottomPercent);

        //String Functions..
        STRING_FILTERFUNCTIONS_MAP = new EnumMap<>(GroupFilterOperator.class);
        STRING_FILTERFUNCTIONS_MAP.put(GroupFilterOperator.BEGINS_WITH, GroupValueFilter::beginWith);
        STRING_FILTERFUNCTIONS_MAP.put(GroupFilterOperator.ENDS_WITH, GroupValueFilter::endsWith);
        STRING_FILTERFUNCTIONS_MAP.put(GroupFilterOperator.CONTAINS, GroupValueFilter::contains);
        STRING_FILTERFUNCTIONS_MAP.put(GroupFilterOperator.DOES_NOT_BEGIN_WITH, GroupValueFilter::notBeginWith);
        STRING_FILTERFUNCTIONS_MAP.put(GroupFilterOperator.DOES_NOT_END_WITH, GroupValueFilter::notEndsWith);
        STRING_FILTERFUNCTIONS_MAP.put(GroupFilterOperator.DOES_NOT_CONTAIN, GroupValueFilter::notContains);

    }

    protected final GroupFilterOperator groupFilterOperator;
    protected final Value filterValue;

    public GroupValueFilter(GroupFilterOperator groupFilterOperator, Value filterValue) {
        this.groupFilterOperator = groupFilterOperator;
        this.filterValue = refineValue(filterValue);
    }

    protected boolean isSpecialFilterCondition(){
        return (groupFilterOperator == GroupFilterOperator.TOP_VALUES || groupFilterOperator == GroupFilterOperator.BOTTOM_VALUES || groupFilterOperator == GroupFilterOperator.TOP_PERCENT || groupFilterOperator == GroupFilterOperator.BOTTOM_PERCENT);
    }

    protected boolean isStringFilterCondition(){
        return (groupFilterOperator == GroupFilterOperator.BEGINS_WITH || groupFilterOperator == GroupFilterOperator.DOES_NOT_BEGIN_WITH || groupFilterOperator == GroupFilterOperator.ENDS_WITH || groupFilterOperator == GroupFilterOperator.DOES_NOT_END_WITH
                || groupFilterOperator == GroupFilterOperator.CONTAINS || groupFilterOperator == GroupFilterOperator.DOES_NOT_CONTAIN);
    }

    protected boolean isRelationalFilterCondition(){
        return (groupFilterOperator == GroupFilterOperator.EQUALS || groupFilterOperator == GroupFilterOperator.DOES_NOT_EQUALS || groupFilterOperator == GroupFilterOperator.GREATER_THAN_OR_EQUAL_TO || groupFilterOperator == GroupFilterOperator.LESS_THAN_OR_EQUAL_TO
                || groupFilterOperator == GroupFilterOperator.GREATER_THAN || groupFilterOperator == GroupFilterOperator.LESS_THAN || groupFilterOperator == GroupFilterOperator.EXACT_OR_NEXT_LARGER || groupFilterOperator == GroupFilterOperator.EXACT_OR_NEXT_SMALLER);
    }

    protected List<AggregatedNode> filter(List<AggregatedNode> aggregatedNodes, Workbook workbook){
        if(isStringFilterCondition()){
            return STRING_FILTERFUNCTIONS_MAP.get(groupFilterOperator).apply(aggregatedNodes, (Value) filterValue);
        }else{
            return FILTER_FUNCTIONS_MAP.get(groupFilterOperator).apply(aggregatedNodes, null, (Value) filterValue);
        }
    }

    protected boolean isCacheable(){
        return true;
    }

    private static Value refineValue(Value value){
        if(value.getValue() instanceof Throwable){
            value = Value.getInstance(Cell.Type.STRING, ((Throwable) value.getValue()).getMessage());
        }
        return value;
    }


    private static List<AggregatedNode> equals(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue){
        return doRelationalFilter(aggregatedNodes, summary, GroupFilterOperator.EQUALS, filterValue);
    }

    private static List<AggregatedNode> equalOrNextSmaller(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue) {
        return doRelationalFilter(aggregatedNodes, summary, GroupFilterOperator.EXACT_OR_NEXT_SMALLER, filterValue);

    }

    private static List<AggregatedNode> equalOrNextLarger(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue){
        return doRelationalFilter(aggregatedNodes, summary, GroupFilterOperator.EXACT_OR_NEXT_LARGER, filterValue);
    }

    private static List<AggregatedNode> notEquals(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue){
        return doRelationalFilter(aggregatedNodes, summary, GroupFilterOperator.DOES_NOT_EQUALS, filterValue);
    }


    private static List<AggregatedNode> lessThan(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue){
        return doRelationalFilter(aggregatedNodes, summary, GroupFilterOperator.LESS_THAN, filterValue);
    }


    private static List<AggregatedNode> greaterThan(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue){
        return doRelationalFilter(aggregatedNodes, summary, GroupFilterOperator.GREATER_THAN, filterValue);
    }


    private static List<AggregatedNode> lessThanOrEqualTo(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue){
        return doRelationalFilter(aggregatedNodes, summary, GroupFilterOperator.LESS_THAN_OR_EQUAL_TO, filterValue);
    }


    private static List<AggregatedNode> greaterThanOrEqualTo(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue){
        return doRelationalFilter(aggregatedNodes, summary, GroupFilterOperator.GREATER_THAN_OR_EQUAL_TO, filterValue);
    }

    private static List<AggregatedNode> doRelationalFilter(List<AggregatedNode> aggregatedNodes, Summary summary, GroupFilterOperator groupFilterOperator, Value filterValue){
        if(summary != null) {
            aggregatedNodes.sort(new NodeComparator(SortOrder.ASCENDING, summary));
        }
        return relationalFilters(null, null, aggregatedNodes, summary, new GroupValueFilter(groupFilterOperator, filterValue));
    }



    private static List<AggregatedNode> beginWith(List<AggregatedNode> aggregatedNodes, Value filterValue){
        if(filterValue.getType() == Cell.Type.STRING) {
            aggregatedNodes = relationalFilters(null, null, aggregatedNodes, null, new GroupValueFilter(GroupFilterOperator.GREATER_THAN_OR_EQUAL_TO, filterValue));
            String filterStr = ((String) filterValue.getValue()) + (char) 65535;
            aggregatedNodes = relationalFilters(null, null, aggregatedNodes, null, new GroupValueFilter(GroupFilterOperator.LESS_THAN_OR_EQUAL_TO, Value.getInstance(Cell.Type.STRING, filterStr)));
            return aggregatedNodes;
        }
        return Collections.emptyList();
    }

    private static List<AggregatedNode> contains(List<AggregatedNode> aggregatedNodes, Value filterValue){

        List<AggregatedNode> result = new ArrayList<>();
        for (AggregatedNode aggregatedNode : aggregatedNodes) {
            Value valueToCompare = aggregatedNode.getValue();
            if (valueToCompare.getType() == Cell.Type.STRING && filterValue.getType() == Cell.Type.STRING) {
                String strToCompare = ((String) valueToCompare.getValue()).toLowerCase();
                String filterStr = ((String) filterValue.getValue()).toLowerCase();
                if(strToCompare.contains(filterStr)) {
                    result.add(aggregatedNode);
                }
            }
        }
        return result;
    }

    private static List<AggregatedNode> notContains(List<AggregatedNode> aggregatedNodes, Value filterValue){
        List<AggregatedNode> result = new ArrayList<>();
        for (AggregatedNode aggregatedNode : aggregatedNodes) {
            Value valueToCompare = aggregatedNode.getValue();
            if (valueToCompare.getType() == Cell.Type.STRING && filterValue.getType() == Cell.Type.STRING) {
                String strToCompare = ((String) valueToCompare.getValue()).toLowerCase();
                String filterStr = ((String) filterValue.getValue()).toLowerCase();
                if(!strToCompare.contains(filterStr)) {
                    result.add(aggregatedNode);
                }
            }else {
                result.add(aggregatedNode);
            }
        }
        return result;
    }

    private static List<AggregatedNode> endsWith(List<AggregatedNode> aggregatedNodes, Value filterValue){

        List<AggregatedNode> result = new ArrayList<>();
        for (AggregatedNode aggregatedNode : aggregatedNodes) {
            Value valueToCompare = aggregatedNode.getValue();
            if (valueToCompare.getType() == Cell.Type.STRING && filterValue.getType() == Cell.Type.STRING) {
                String strToCompare = ((String) valueToCompare.getValue()).toLowerCase();
                String filterStr = ((String) filterValue.getValue()).toLowerCase();
                if(strToCompare.endsWith(filterStr)) {
                    result.add(aggregatedNode);
                }
            }
        }
        return result;
    }

    private static List<AggregatedNode> notBeginWith(List<AggregatedNode> aggregatedNodes, Value filterValue){
        List<AggregatedNode> result = new ArrayList<>();
        for (AggregatedNode aggregatedNode : aggregatedNodes) {
            Value valueToCompare = aggregatedNode.getValue();
            if (valueToCompare.getType() == Cell.Type.STRING && filterValue.getType() == Cell.Type.STRING) {
                String strToCompare = ((String) valueToCompare.getValue()).toLowerCase();
                String filterStr = ((String) filterValue.getValue()).toLowerCase();
                if(!strToCompare.startsWith(filterStr)) {
                    result.add(aggregatedNode);
                }
            }else {
                result.add(aggregatedNode);
            }
        }
        return result;
    }

    private static List<AggregatedNode> notEndsWith(List<AggregatedNode> aggregatedNodes, Value filterValue){

        List<AggregatedNode> result = new ArrayList<>();
        for (AggregatedNode aggregatedNode : aggregatedNodes) {
            Value valueToCompare = aggregatedNode.getValue();
            if (valueToCompare.getType() == Cell.Type.STRING && filterValue.getType() == Cell.Type.STRING) {
                String strToCompare = ((String) valueToCompare.getValue()).toLowerCase();
                String filterStr = ((String) filterValue.getValue()).toLowerCase();
                if(!strToCompare.endsWith(filterStr)) {
                    result.add(aggregatedNode);
                }
            }else {
                result.add(aggregatedNode);
            }
        }
        return result;
    }

    private static List<AggregatedNode> topValues(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue) {

        return topOrBottom(aggregatedNodes, summary, filterValue, SortOrder.DESCENDING, true);

    }

    private static List<AggregatedNode> bottomValues(List<AggregatedNode> aggregatedNodes, Summary summary,Value filterValue) {

        return topOrBottom(aggregatedNodes, summary, filterValue, SortOrder.ASCENDING, true);

    }

    private static List<AggregatedNode> topPercent(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue) {

        return topOrBottom(aggregatedNodes, summary, filterValue, SortOrder.DESCENDING, false);

    }

    private static List<AggregatedNode> bottomPercent(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue) {

        return topOrBottom(aggregatedNodes, summary, filterValue, SortOrder.ASCENDING, false);

    }

    private static List<AggregatedNode> topOrBottom(List<AggregatedNode> aggregatedNodes, Summary summary, Value filterValue, SortOrder sortOrder, boolean isValue){

        if(filterValue.getValue() instanceof Number){

            int count = ((Number) filterValue.getValue()).intValue();
            int totalRows = 0;
            List<AggregatedNode> numericalNodes = new LinkedList<>();
            for(AggregatedNode aggregatedNode : aggregatedNodes) {
                if ((summary != null || aggregatedNode.getValue().getValue() instanceof Number || aggregatedNode.getValue().getValue() instanceof Date)){
                    numericalNodes.add(aggregatedNode);
                }
                totalRows += isValue ? 0 : aggregatedNode.getRowsBitSet().cardinality();
            }
            count = isValue ? count : (totalRows * count) / 100;
            NodeComparator nodeComparator = new NodeComparator(sortOrder, summary);

            List<AggregatedNode> sortedNodes = nodeComparator.sort(numericalNodes);

            List<AggregatedNode> filteredNodes = new ArrayList<>(count);
            for (AggregatedNode aggregatedNode : sortedNodes) {
                if (count > 0 || (summary != null && !filteredNodes.isEmpty() && Summarizer.getSummarizedValue(summary, filteredNodes.get(filteredNodes.size() - 1).getRowsBitSet())
                        .equals(Summarizer.getSummarizedValue(summary, aggregatedNode.getRowsBitSet())))) {
                    filteredNodes.add(aggregatedNode);
                    count -= summary != null ? 1 : aggregatedNode.getRowsBitSet().cardinality();
                } else {
                    break;
                }
            }
            return filteredNodes;
        }
        throw new IllegalArgumentException(">>>>> FilterValue Must Be a Number"); // No I18N
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || !(o instanceof GroupValueFilter)){
            return false;
        }
        GroupValueFilter that = (GroupValueFilter) o;
        if (this.groupFilterOperator == that.groupFilterOperator ){
            return Objects.equals(this.filterValue, that.filterValue);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(groupFilterOperator, filterValue);
    }

    //    NOTE: This comparator is used for Top/Bottom filters
    protected static class NodeComparator implements Comparator<AggregatedNode>{

        private final SortOrder sortOrder;
        private final Summary summary;
        private Map<AggregatedNode, Value> cacheMap = new HashMap<>();

        protected NodeComparator(SortOrder sortOrder, Summary summary){
            this.sortOrder = sortOrder;
            this.summary = summary;
        }

        protected List<AggregatedNode> sort(List<AggregatedNode> dataAggregatorResults){
            List<AggregatedNode> sortedNodes = dataAggregatorResults.stream()
                    .sorted(this).collect(Collectors.toList());
            cacheMap.clear();
            return sortedNodes;
        }

        @Override
        public int compare(AggregatedNode o1, AggregatedNode o2) {
            if(sortOrder == SortOrder.NATURAL_ORDER){
                return o1.getFirstOccurrenceIndex() < o2.getFirstOccurrenceIndex() ? -1 : 1;
            }else if (summary == null){
                Integer lhsSortIdx = o1.getSortAscIndex();
                Integer rhsSortIdx = o2.getSortAscIndex();

                if (lhsSortIdx == null || rhsSortIdx == null) {
                    return lhsSortIdx == null ? 1 : -1;
                }

                if (lhsSortIdx < rhsSortIdx) {
                    return sortOrder == SortOrder.DESCENDING ? 1 : -1;
                } else if (lhsSortIdx > rhsSortIdx) {
                    return sortOrder == SortOrder.DESCENDING ? -1 : 1;
                } else {
                    return 0;
                }
            }
            else {
                Value lhs = cacheMap.get(o1);
                Value rhs = cacheMap.get(o2);

                if(lhs == null){
                    lhs = Summarizer.getSummarizedValue(summary, o1.getRowsBitSet());
                    cacheMap.put(o1, lhs);
                }
                if(rhs == null){
                    rhs = Summarizer.getSummarizedValue(summary, o2.getRowsBitSet());
                    cacheMap.put(o2, rhs);
                }

                int val = lhs.compareTo(rhs);
                val *= sortOrder == SortOrder.DESCENDING ? -1 : 1;

                return val;
            }
        }
    }

    /**
     * This function will return the index of the Equal_Or_Next_Smaller Node.
     * @param filterValue
     * @param groupedNodes (Must Be IN ASC Order)
     * @return
     * @throws EvaluationException
     */
    private static int binarySearch(Value filterValue, List<AggregatedNode> groupedNodes, Summary summary) throws EvaluationException {
        int resultNodeIndex = -1;
        {
            int size = groupedNodes.size();

            int lookupMidIndex = size/2;
            AggregatedNode midNode = groupedNodes.get(lookupMidIndex);
            Value midValue = summary == null ? midNode.getValue() : Summarizer.getSummarizedValue(summary, midNode.getRowsBitSet());

            boolean result = false;
            // No comparing NULL in MidValue with Non-NULL FilterValue to avoid Zero filter value matching null in midValue.
            // ISSUE FIXED WITH THIS CHANGE : 0 in cells getting highlighted by Duplicate Values CF, If zero matches with null node.
            if (((midValue == null || midValue.getValue() == null) ?  (filterValue == null || filterValue.getValue() == null) : true)
                    && !(midValue.getValue() instanceof Throwable)) {
                result = greaterThanOrEqualTo(filterValue, midValue);
                if(result)
                {
                    resultNodeIndex = lookupMidIndex;
                }
            }

            if (size > 1) {
                if (result) {
                    int recursiveResult = binarySearch(filterValue, groupedNodes.subList(lookupMidIndex, size), summary);
                    if (recursiveResult != -1) {
                        resultNodeIndex = recursiveResult + lookupMidIndex;
                    }
                } else {
                    lookupMidIndex -= 1;
                    int recursiveResult = binarySearch(filterValue, groupedNodes.subList(0, lookupMidIndex + 1), summary);
                    if (recursiveResult != -1) {
                        resultNodeIndex = recursiveResult;
                    }
                }
            }

        }
        return resultNodeIndex;
    }

    private static boolean greaterThanOrEqualTo(Value filterValue, Value valueToCompare) throws EvaluationException {

        if (filterValue.getType() == Cell.Type.BOOLEAN && valueToCompare.getType() != Cell.Type.BOOLEAN){
            return valueToCompare.getType() != Cell.Type.ERROR;
        }else if(filterValue.getType() != Cell.Type.BOOLEAN && valueToCompare.getType() == Cell.Type.BOOLEAN){
            return filterValue.getType() == Cell.Type.ERROR;
        }
        else {
            return (new Comparative(Comparative.GE)).compare(filterValue, valueToCompare);
        }
    }

    protected static List<AggregatedNode> relationalFilters(DataAggregatorResult cacheKey, Map<DataAggregatorResult, List<AggregatedNode>> aggregatedNodeMap, List<AggregatedNode> groupedNodes, Summary summary, GroupValueFilter groupValueFilter){
        int lessThanOrEqualIndex = -1;
        if(groupedNodes.isEmpty()){
            return Collections.emptyList();
        }
        if(!groupValueFilter.isRelationalFilterCondition() || !(groupValueFilter.filterValue instanceof Value)){
            throw new IllegalArgumentException(" >>> InValid FilterOperator");//No I18N
        }

        Value filterValue = (Value) groupValueFilter.filterValue;
        try {
            if(groupedNodes.size() > 1 && Value.EMPTY_VALUE.equals(groupedNodes.get(groupedNodes.size() - 1).getValue())){
                AggregatedNode emptyValueNode = groupedNodes.remove(groupedNodes.size() - 1);
                groupedNodes.add(0, emptyValueNode);
            }
            lessThanOrEqualIndex = GroupValueFilter.binarySearch(filterValue, groupedNodes, summary);
        } catch (EvaluationException e) {
            return Collections.emptyList();
        }

        boolean isEqual = false;
        if(lessThanOrEqualIndex != -1){
            Value valueToCompare = summary != null ? Summarizer.getSummarizedValue(summary, groupedNodes.get(lessThanOrEqualIndex).getRowsBitSet()): groupedNodes.get(lessThanOrEqualIndex).getValue();
            try {
                isEqual = new Comparative(Comparative.EQ).compare(valueToCompare, filterValue);
            } catch (EvaluationException ignored) {}
        }

        List<AggregatedNode> resultNodes = Collections.emptyList();
        GroupFilterOperator groupFilterOperator;

//        if(aggregatedNodeMap != null && cacheKey != null){
//            groupFilterOperator = GroupFilterOperator.EQUALS;
//        }else {
        groupFilterOperator = groupValueFilter.groupFilterOperator;
//        }

        switch (groupFilterOperator){
            case EQUALS:
                List<AggregatedNode> tempResultNodes;
                if(isEqual){
                    tempResultNodes = Collections.singletonList(groupedNodes.get(lessThanOrEqualIndex));
                }else {
                    tempResultNodes = Collections.emptyList();
                }
                tempResultNodes = Collections.unmodifiableList(tempResultNodes);
                if(aggregatedNodeMap != null && cacheKey != null){
                    if(groupValueFilter.groupFilterOperator == GroupFilterOperator.EQUALS){
                        resultNodes = tempResultNodes;
                    }else {
                        DataAggregatorResult tempCacheKey = new DataAggregatorResult(cacheKey.getAggregatedNode(),cacheKey.getTabularData());
                        tempCacheKey.setGroupByCriteria(cacheKey.getGroupByCriteria());
                        GroupFilterCriteria filterCriteria = new GroupValueFilter(GroupFilterOperator.EQUALS, filterValue);
                        tempCacheKey.setGroupFilterCriteria(filterCriteria);

                        aggregatedNodeMap.put(tempCacheKey, tempResultNodes);
                    }
                }else {
                    return tempResultNodes;
                }
                break;
            case LESS_THAN_OR_EQUAL_TO:

                if(lessThanOrEqualIndex != -1){
                    tempResultNodes = groupedNodes.subList(0, lessThanOrEqualIndex + 1);
                }else {
                    tempResultNodes = Collections.emptyList();
                }
                tempResultNodes = Collections.unmodifiableList(tempResultNodes);
                if(aggregatedNodeMap != null && cacheKey != null){
                    if(groupValueFilter.groupFilterOperator == GroupFilterOperator.LESS_THAN_OR_EQUAL_TO){
                        resultNodes = tempResultNodes;
                    }else {
                        DataAggregatorResult tempCacheKey = new DataAggregatorResult(cacheKey.getAggregatedNode(),cacheKey.getTabularData());
                        tempCacheKey.setGroupByCriteria(cacheKey.getGroupByCriteria());
                        GroupFilterCriteria filterCriteria = new GroupValueFilter(GroupFilterOperator.LESS_THAN_OR_EQUAL_TO, filterValue);
                        tempCacheKey.setGroupFilterCriteria(filterCriteria);

                        aggregatedNodeMap.put(tempCacheKey, tempResultNodes);
                    }
                }else {
                    return tempResultNodes;
                }
                break;
            case LESS_THAN:

                if(lessThanOrEqualIndex != -1){
                    if(isEqual){
                        tempResultNodes = groupedNodes.subList(0, lessThanOrEqualIndex);
                    }else {
                        tempResultNodes = groupedNodes.subList(0, lessThanOrEqualIndex + 1);
                    }
                }else {
                    tempResultNodes = Collections.emptyList();
                }
                tempResultNodes = Collections.unmodifiableList(tempResultNodes);
                if(aggregatedNodeMap != null && cacheKey != null){
                    if(groupValueFilter.groupFilterOperator == GroupFilterOperator.LESS_THAN){
                        resultNodes = tempResultNodes;
                    }else {
                        DataAggregatorResult tempCacheKey = new DataAggregatorResult(cacheKey.getAggregatedNode(),cacheKey.getTabularData());
                        tempCacheKey.setGroupByCriteria(cacheKey.getGroupByCriteria());
                        GroupFilterCriteria filterCriteria = new GroupValueFilter(GroupFilterOperator.LESS_THAN, filterValue);
                        tempCacheKey.setGroupFilterCriteria(filterCriteria);

                        aggregatedNodeMap.put(tempCacheKey, tempResultNodes);
                    }
                }else {
                    return tempResultNodes;
                }
                break;
            case EXACT_OR_NEXT_SMALLER:

                if(lessThanOrEqualIndex != -1){
                    tempResultNodes = Collections.singletonList(groupedNodes.get(lessThanOrEqualIndex));
                }else {
                    tempResultNodes = Collections.emptyList();
                }
                tempResultNodes = Collections.unmodifiableList(tempResultNodes);
                if(aggregatedNodeMap != null && cacheKey != null){
                    if(groupValueFilter.groupFilterOperator == GroupFilterOperator.EXACT_OR_NEXT_SMALLER){
                        resultNodes = tempResultNodes;
                    }else {
                        DataAggregatorResult tempCacheKey = new DataAggregatorResult(cacheKey.getAggregatedNode(),cacheKey.getTabularData());
                        tempCacheKey.setGroupByCriteria(cacheKey.getGroupByCriteria());
                        GroupFilterCriteria filterCriteria = new GroupValueFilter(GroupFilterOperator.EXACT_OR_NEXT_SMALLER, filterValue);
                        tempCacheKey.setGroupFilterCriteria(filterCriteria);

                        aggregatedNodeMap.put(tempCacheKey, tempResultNodes);
                    }
                }else {
                    return tempResultNodes;
                }
                break;
            case DOES_NOT_EQUALS:

                tempResultNodes = new ArrayList<>(groupedNodes);
                if(isEqual){
                    tempResultNodes.remove(lessThanOrEqualIndex);
                }
                tempResultNodes = Collections.unmodifiableList(tempResultNodes);
                if(aggregatedNodeMap != null && cacheKey != null){
                    if(groupValueFilter.groupFilterOperator == GroupFilterOperator.DOES_NOT_EQUALS){
                        resultNodes = tempResultNodes;
                    }else {
                        DataAggregatorResult tempCacheKey = new DataAggregatorResult(cacheKey.getAggregatedNode(),cacheKey.getTabularData());
                        tempCacheKey.setGroupByCriteria(cacheKey.getGroupByCriteria());
                        GroupFilterCriteria filterCriteria = new GroupValueFilter(GroupFilterOperator.DOES_NOT_EQUALS, filterValue);
                        tempCacheKey.setGroupFilterCriteria(filterCriteria);

                        aggregatedNodeMap.put(tempCacheKey, tempResultNodes);
                    }

                }else {
                    return tempResultNodes;
                }
                break;
            case GREATER_THAN_OR_EQUAL_TO:

                if(lessThanOrEqualIndex == -1){
                    tempResultNodes = new ArrayList<>(groupedNodes);
                }else if(isEqual){
                    tempResultNodes = groupedNodes.subList(lessThanOrEqualIndex, groupedNodes.size());
                }else if(lessThanOrEqualIndex + 1 < groupedNodes.size()){
                    tempResultNodes = groupedNodes.subList(lessThanOrEqualIndex + 1, groupedNodes.size());
                }else {
                    tempResultNodes = Collections.emptyList();
                }
                tempResultNodes = Collections.unmodifiableList(tempResultNodes);
                if(aggregatedNodeMap != null && cacheKey != null){
                    if(groupValueFilter.groupFilterOperator == GroupFilterOperator.GREATER_THAN_OR_EQUAL_TO){
                        resultNodes = tempResultNodes;
                    }else {
                        DataAggregatorResult tempCacheKey = new DataAggregatorResult(cacheKey.getAggregatedNode(),cacheKey.getTabularData());
                        tempCacheKey.setGroupByCriteria(cacheKey.getGroupByCriteria());
                        GroupFilterCriteria filterCriteria = new GroupValueFilter(GroupFilterOperator.GREATER_THAN_OR_EQUAL_TO, filterValue);
                        tempCacheKey.setGroupFilterCriteria(filterCriteria);

                        aggregatedNodeMap.put(tempCacheKey, tempResultNodes);
                    }

                }else {
                    return tempResultNodes;
                }
                break;
            case GREATER_THAN:

                if(lessThanOrEqualIndex == -1){
                    tempResultNodes = new ArrayList<>(groupedNodes);
                }else if(lessThanOrEqualIndex + 1 < groupedNodes.size()){
                    tempResultNodes = groupedNodes.subList(lessThanOrEqualIndex + 1, groupedNodes.size());
                }else {
                    tempResultNodes = Collections.emptyList();
                }
                tempResultNodes = Collections.unmodifiableList(tempResultNodes);
                if(aggregatedNodeMap != null && cacheKey != null){
                    if(groupValueFilter.groupFilterOperator == GroupFilterOperator.GREATER_THAN){
                        resultNodes = tempResultNodes;
                    }else {
                        DataAggregatorResult tempCacheKey = new DataAggregatorResult(cacheKey.getAggregatedNode(),cacheKey.getTabularData());
                        tempCacheKey.setGroupByCriteria(cacheKey.getGroupByCriteria());
                        GroupFilterCriteria filterCriteria = new GroupValueFilter(GroupFilterOperator.GREATER_THAN, filterValue);
                        tempCacheKey.setGroupFilterCriteria(filterCriteria);

                        aggregatedNodeMap.put(tempCacheKey, tempResultNodes);
                    }

                }else {
                    return tempResultNodes;
                }
                break;
            case EXACT_OR_NEXT_LARGER:

                if(isEqual){
                    tempResultNodes = Collections.singletonList(groupedNodes.get(lessThanOrEqualIndex));
                }else if(lessThanOrEqualIndex + 1 < groupedNodes.size()){
                    tempResultNodes = Collections.singletonList(groupedNodes.get(lessThanOrEqualIndex + 1));
                }else {
                    tempResultNodes = Collections.emptyList();
                }
                tempResultNodes = Collections.unmodifiableList(tempResultNodes);
                if(aggregatedNodeMap != null && cacheKey != null){
                    if(groupValueFilter.groupFilterOperator == GroupFilterOperator.EXACT_OR_NEXT_LARGER){
                        resultNodes = tempResultNodes;
                    }else {
                        DataAggregatorResult tempCacheKey = new DataAggregatorResult(cacheKey.getAggregatedNode(),cacheKey.getTabularData());
                        tempCacheKey.setGroupByCriteria(cacheKey.getGroupByCriteria());
                        GroupFilterCriteria filterCriteria = new GroupValueFilter(GroupFilterOperator.EXACT_OR_NEXT_LARGER, filterValue);
                        tempCacheKey.setGroupFilterCriteria(filterCriteria);

                        aggregatedNodeMap.put(tempCacheKey, tempResultNodes);
                    }
                }else {
                    return tempResultNodes;
                }
                break;
        }
        return resultNodes;
    }
}
