/* $Id$ */
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.*;
import com.singularsys.jep.JepException;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>-zt276 on  2019-04-23
 */
public class TabularDataExample {

    private static void example1(Range range) throws JepException {

        Sheet sheet = range.getSheet();
        Workbook workbook = sheet.getWorkbook();

        //Creating TabularData.
        TabularData tabularData = workbook.getTabularDataPool().getTabularData(workbook, range);

//        Sample Data.
//        *-----------------------------*
//        |Region   |Product Category   |
//        *---------|-------------------*
//        |North	|Stationery         |
//        *---------|-------------------*
//        |East	    |Grocery            |
//        *---------|-------------------*
//        |East	    |Furniture          |
//        *---------|-------------------*
//        |West	    |Stationery         |
//        *---------|-------------------*
//        |Notrh	|Grocery            |
//        *---------|-------------------*
//        |West	    |Furniture          |
//        *-----------------------------*

        //Adding Two GroupByCriterias.
        List<GroupByCriteria> groupByCriterias = new ArrayList<>();
/*1*/   GroupByCriteria groupByCriteria = new GroupByCriteria(0, GroupByCriteria.GroupByOperation.DEFAULT); // GroupBy Region.
        groupByCriterias.add(groupByCriteria);

/*2*/   groupByCriteria = new GroupByCriteria(1, GroupByCriteria.GroupByOperation.DEFAULT); // GroupBy Product Category.
        groupByCriterias.add(groupByCriteria);

        DataAggregatorResult rootNode = tabularData.getRootNode();
//                  Query                                               Result
        List<DataAggregatorResult> regionGroupNodes = tabularData.getGroupedNodes(rootNode, groupByCriterias.get(0));      // -> North, East, West.
        List<DataAggregatorResult> pCategoryGroupNodes = tabularData.getGroupedNodes(rootNode, groupByCriterias.get(1));      // -> Stationery, Grocery, Furniture.

//      Printing The Result.
        for(DataAggregatorResult dataAggregatorResult: regionGroupNodes){
            Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, dataAggregatorResult.getValue().getValue().toString());
        }

        for(DataAggregatorResult dataAggregatorResult: pCategoryGroupNodes){
            Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, dataAggregatorResult.getValue().getValue().toString());
        }

//     To Query Region wise ProductCategory
        for(DataAggregatorResult dataAggregatorResult : regionGroupNodes){
//                                              Result
//          dataAggregatorResult : North           ->          Stationery, Grocery.
//          dataAggregatorResult : West            ->          Stationery, Furniture.
//          dataAggregatorResult : East            ->          Grocery, Furniture.
            List<DataAggregatorResult> results = tabularData.getGroupedNodes(dataAggregatorResult, groupByCriterias.get(1));
            for (DataAggregatorResult result : results){
                Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, "{0} -> {1}", new Object[]{dataAggregatorResult.getValue().getValue(), result.getValue().getValue()});
            }
        }

    }
//
    private static void example2(Range range) throws JepException {

        Sheet sheet = range.getSheet();
        Workbook workbook = sheet.getWorkbook();

        //Creating TabularData
        TabularData tabularData = workbook.getTabularDataPool().getTabularData(workbook, range);

//        Sample Data.
//        *-------------------------------------*
//        |Region   |Product Category   |Profit |
//        *---------|-------------------*-------*
//        |North	|Stationery         |1200.72|
//        *---------|-------------------*-------*
//        |East	    |Grocery            |928.16 |
//        *---------|-------------------*-------*
//        |East	    |Furniture          |517.75 |
//        *---------|-------------------*-------*
//        |West	    |Stationery         |2381.21|
//        *---------|-------------------*-------*
//        |Notrh	|Grocery            |1816.44|
//        *---------|-------------------*-------*
//        |West	    |Furniture          |3928.38|
//        *-----------------------------*-------*


        List<GroupByCriteria> groupByCriterias = new ArrayList<>();
        //Adding GroupByCriteria
        /*
         * addGroupBy Using External range(Range not used to create TabularData)
         * external range must have single colum
         *
         */
        Range externalRange = new Range(sheet, 1, 0, 700, 0);
        GroupByCriteria groupByCriteria = new GroupByCriteria(externalRange, GroupByCriteria.GroupByOperation.DEFAULT);  // GroupBy Product Category.
        groupByCriterias.add(groupByCriteria);
        groupByCriteria = new GroupByCriteria(0, GroupByCriteria.GroupByOperation.DEFAULT);  // GroupBy Region.
        groupByCriterias.add(groupByCriteria);

        //Creating Summary
        ZArrayI summaryRange = new Range(sheet, 1, 2, 5, 2);
        Summary summary = new Summary(summaryRange, Summary.SummarizeOperation.SUM);

        DataAggregatorResult rootNode = tabularData.getRootNode();
//      WithOutFilter
        List<DataAggregatorResult> pCategoryGroupNodes = tabularData.getGroupedNodes(rootNode, groupByCriterias.get(0));      // -> Stationery, Grocery, Furniture.

//      To Query Region wise Sum Of Profit for Product Category.
        for(DataAggregatorResult dataAggregatorResult : pCategoryGroupNodes){
            List<DataAggregatorResult> results = tabularData.getGroupedNodes(dataAggregatorResult, groupByCriterias.get(1));
//                                                  Result(SummayValue)
//          dataAggregatorResult : Stationery(3581.93)           ->          North(1200.72), West(2381.21).
//          dataAggregatorResult : Grocery(2744.6)               ->          East(928.16), North(1816.44).
//          dataAggregatorResult : Furniture(4446.13)            ->          East(517.75), West(3928.38).
            for (DataAggregatorResult result : results){
                Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, "{0} -> {1}Sum : {2}", new Object[]{dataAggregatorResult.getValue().getValue(), result.getValue().getValue(), dataAggregatorResult.getSummarizedValue(summary)});
            }
            Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, "{0}Sum : {1}", new Object[]{dataAggregatorResult.getValue().getValue(), dataAggregatorResult.getSummarizedValue(summary)});
        }

//      WithFilter
        GroupValueFilter groupValueFilter = new GroupValueFilter(GroupValueFilter.GroupFilterOperator.EQUALS, Value.getInstance(Cell.Type.STRING,"Grocery"));
        pCategoryGroupNodes = tabularData.getGroupedNodes(rootNode, groupByCriterias.get(0), groupValueFilter);      // -> Grocery.

//      To Query Region wise Sum Of Profit for Product Category.
        for(DataAggregatorResult dataAggregatorResult : pCategoryGroupNodes){
            groupValueFilter = new GroupValueFilter(GroupValueFilter.GroupFilterOperator.EQUALS, Value.getInstance(Cell.Type.STRING,"North"));
            List<DataAggregatorResult> results = tabularData.getGroupedNodes(dataAggregatorResult, groupByCriterias.get(1), groupValueFilter);
//                                                  Result(SummayValue)
//          dataAggregatorResult : Grocery(1816.44)           ->         North(1816.44).
            /*
                NOTE: Appling filter will affect the Summary and RowIndices of the AppliedNode. (i.e., Grocery before filter (2744.6) after filter (1816.44))
             */
            for (DataAggregatorResult result : results){
                Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, "{0} -> {1}Sum : {2}", new Object[]{dataAggregatorResult.getValue().getValue(), result.getValue().getValue(), dataAggregatorResult.getSummarizedValue(summary)});
            }
            Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, "{0}Sum : {1}", new Object[]{dataAggregatorResult.getValue().getValue(), dataAggregatorResult.getSummarizedValue(summary)});
        }

    }

    private static void example3(Range range, ZArrayI summaryRange) throws JepException {

        Sheet sheet = range.getSheet();
        Workbook workbook = sheet.getWorkbook();

        //Creating TabularData
        TabularData tabularData = workbook.getTabularDataPool().getTabularData(workbook, range);


//        Sample Data.
//        *-------------------------------------*
//        |Region   |Product Category   |Profit |
//        *---------|-------------------*-------*
//        |North	|Stationery         |1200.72|
//        *---------|-------------------*-------*
//        |East	    |Grocery            |928.16 |
//        *---------|-------------------*-------*
//        |East	    |Furniture          |517.75 |
//        *---------|-------------------*-------*
//        |West	    |Stationery         |2381.21|
//        *---------|-------------------*-------*
//        |Notrh	|Grocery            |1816.44|
//        *---------|-------------------*-------*
//        |West	    |Furniture          |3928.38|
//        *-----------------------------*-------*

        //Adding GroupByCriteria
        List<GroupByCriteria> groupByCriterias = new ArrayList<>();
        GroupByCriteria groupByCriteria = new GroupByCriteria(0, GroupByCriteria.GroupByOperation.CASE_INSENSITIVE);
        groupByCriterias.add(groupByCriteria);

        groupByCriteria = new GroupByCriteria(0, GroupByCriteria.GroupByOperation.DEFAULT);
        groupByCriterias.add(groupByCriteria);
        DataAggregatorResult rootNode = tabularData.getRootNode();
        List<DataAggregatorResult> regionGroupNodes = tabularData.getGroupedNodes(rootNode, groupByCriterias.get(0));

        //Creating Summary
        Summary summary = new Summary(summaryRange, Summary.SummarizeOperation.SUM);
        // Group Sort on Summary
        GroupSortCriteria groupSortCriteria = new GroupSortBySummary(summary, SortOrder.ASCENDING);

        //Getting Nodes
        regionGroupNodes = tabularData.getGroupedNodes(rootNode, groupByCriterias.get(0));
        //Sorting the Nodes
        regionGroupNodes = groupSortCriteria.sort(regionGroupNodes);
        //Printing the Result
        Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO,"Lable  |  Summary:SUM | Rowindices");
        for (DataAggregatorResult dataAggregatorResult : regionGroupNodes) {
            Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, "{0}Sum : {1}RowIndices : {2}", new Object[]{dataAggregatorResult.getValue().getValue(), dataAggregatorResult.getSummarizedValue(summary), dataAggregatorResult.getRows()});
        }
    }
//
    private static void example4(Range range, ZArrayI summaryRange) throws JepException {

        Sheet sheet = range.getSheet();
        Workbook workbook = sheet.getWorkbook();

        //Creating TabularData
        TabularData tabularData = workbook.getTabularDataPool().getTabularData(workbook, range);

        //Adding GroupByCriteria using Expression
        Expression groupingExpression = ExpressionsLibrary.toExpression("=LEFT(" + ExpressionsLibrary.TD_GROUP_VAL + ";4)", workbook); // No I18N
        GroupByCriteria groupByCriteria = new GroupByCriteria(2, groupingExpression);
        DataAggregatorResult rootNode = tabularData.getRootNode();
        //Creating Summary
        Summary summary = new Summary(summaryRange, Summary.SummarizeOperation.SUM);
        //Getting Result
        List<DataAggregatorResult> resultNodes = tabularData.getGroupedNodes(rootNode, groupByCriteria);
        //Printing the Result
        Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO,"Lable | Summary:SUM | Rowindices");
        for (DataAggregatorResult dataAggregatorResult : resultNodes) {
            Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, "{0} | {1} | {2}", new Object[]{dataAggregatorResult.getValue().getValue(), dataAggregatorResult.getSummarizedValue(summary), dataAggregatorResult.getRows()});
        }
    }
//
    private static void example5(Range range) throws JepException {

        Sheet sheet = range.getSheet();
        Workbook workbook = sheet.getWorkbook();

        //Creating TabularData
        TabularData tabularData = workbook.getTabularDataPool().getTabularData(workbook, range);

        //Adding GroupByCriteria
        GroupByCriteria groupByCriteria = new GroupByCriteria(2, GroupByCriteria.GroupByOperation.DEFAULT);

        List<GroupFilterCriteria> groupFilterCriteria = new LinkedList<>();
        groupFilterCriteria.add(new GroupValueFilter(GroupValueFilter.GroupFilterOperator.TOP_VALUES, Value.getInstance(Cell.Type.FLOAT, 5)));//adding filterCondition
        groupFilterCriteria.add(new GroupValueFilter(GroupValueFilter.GroupFilterOperator.BOTTOM_VALUES, Value.getInstance(Cell.Type.FLOAT, 5)));//adding filterCondition
        groupFilterCriteria.add(new GroupValueFilter(GroupValueFilter.GroupFilterOperator.EQUALS, Value.getInstance(Cell.Type.FLOAT, 2518)));//adding filterCondition
        DataAggregatorResult rootNode = tabularData.getRootNode();
        MultipleFilter multipleFilter = new MultipleFilter(groupFilterCriteria, MultipleFilter.FilterType.OR);// creating MultipleFilters

        //Getting Result
        List<DataAggregatorResult> resultNodes = tabularData.getGroupedNodes(rootNode, groupByCriteria, multipleFilter);
        //Printing the Result
        Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO,"Lable | Rowindices");
        for(DataAggregatorResult dataAggregatorResult : resultNodes){
            Logger.getLogger(TabularDataExample.class.getName()).log(Level.INFO, "{0} | {1}", new Object[]{dataAggregatorResult.getValue().getValue(), dataAggregatorResult.getRows()});
        }
    }
//
    public static void test(Sheet sheet) throws JepException {

        Range range = new Range(sheet, 1, 0, 700, 12);

        /*
         *GroupBy using GroupByOperation.
         */
        example1(range);

        /*
         *GroupBy using
         *Filter using Value.
         *
         */
        example2(range);

        /*
         *GroupBy using GroupByOperation.
         *Creating Summary.
         *Sort by Summary.
         *
         */
        ZArrayI summaryRange = new Range(sheet, 1, 7, 700, 7);
        example3(range, summaryRange);

        /*
         *GroupBy using GroupByExpression.
         *Creating Summary.
         */
        ZArrayI summaryRange1 = new Range(sheet, 1, 10, 700, 10);
        example4(range, summaryRange1);

        /*
         *GroupBy using GroupByOperation.
         *Filter using MultipleFilter(AND/OR).
         */
        example5(range);


    }
}
