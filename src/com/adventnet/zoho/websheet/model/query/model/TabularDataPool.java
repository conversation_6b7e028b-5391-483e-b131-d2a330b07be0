//$Id$

package com.adventnet.zoho.websheet.model.query.model;

/**
 * <AUTHOR>
 *
 */

import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ZArrayI;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class TabularDataPool {
        private Map<Key, TabularData> tabularDataPool;
        
        public TabularDataPool() {
            this.tabularDataPool = new HashMap<>();
        }
        //Workbook is used to Construct TabularData
        public TabularData getTabularData(Workbook workbook, ZArrayI range) {
          
            return getTabularData(workbook, range, false, false);
        }

    public TabularData getTabularData(Workbook workbook, ZArrayI range, boolean inSkipHiddenRows, boolean inSkipEmptyRows) {

        TabularData tabularData;
        synchronized (tabularDataPool) {
            Key key = new Key(range,inSkipHiddenRows,inSkipEmptyRows);
            tabularData = tabularDataPool.get(key);

            if(tabularData == null)
            {
                tabularData = new TabularData(range, key.skipHiddenRows, key.skipEmptyRows, false, workbook);
                this.tabularDataPool.put(key, tabularData);
            }
        }
        return tabularData;
    }

        public int size(){
            return tabularDataPool.size();
        }
        
        public void invalidateAllTabularData() {
            this.tabularDataPool.clear();
        }

    public void dismantle() {
        for (Key key : this.tabularDataPool.keySet()) {
            this.tabularDataPool.get(key).dismantle();
        }
        this.tabularDataPool.clear();
    }
    
    @Override
    public String toString() {
        return this.getClass().getName() +" + Size : "+ tabularDataPool.size(); //No I18N
    }

    private class Key
    {
        private final ZArrayI array;
        private final boolean skipHiddenRows;
        private final boolean skipEmptyRows;

        private Key(ZArrayI inArray, boolean inSkipHiddenRows, boolean inSkipEmptyRows)
        {
            this.array = inArray;
            this.skipHiddenRows = inSkipHiddenRows;
            this.skipEmptyRows = inSkipEmptyRows;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o)
            {
                return true;
            }
            if (!(o instanceof Key))
            {
                return false;
            }
            Key key = (Key) o;
            return skipHiddenRows == key.skipHiddenRows && skipEmptyRows == key.skipEmptyRows && array.equals(key.array);
        }

        @Override
        public int hashCode() {
            return Objects.hash(array, skipHiddenRows, skipEmptyRows);
        }
    }
}
