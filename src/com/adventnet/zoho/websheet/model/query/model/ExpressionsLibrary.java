/* $Id$ */
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.singularsys.jep.JepException;
import com.singularsys.jep.NodeFactory;
import com.singularsys.jep.ParseException;
import com.singularsys.jep.parser.ASTVarNode;
import com.singularsys.jep.parser.Node;

import java.time.Instant;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ExpressionsLibrary {
    private static final Logger LOGGER = Logger.getLogger(ExpressionsLibrary.class.getName());
    public static final String TD_GROUP_VAL = "td_group_Val"; //No I18N

    public static Expression toExpression(String formula, Workbook workbook) {
        return ExpressionImpl.getInstanceFromXMLCellFormula(workbook, formula, 0, 0, CellReference.ReferenceMode.R1C1);
    }

    private static Expression transformGroupRefExpression(Expression expression, Value groupValue) throws ParseException, JepException, SheetEngineException {
        Node node = expression.getNode();
        Node newNode = Workbook.getDeepCopyVisitor().deepCopy(node);
        newNode = constructGroupRefNode(newNode, groupValue);
        return new ExpressionImpl(newNode);
    }

    private static Node constructGroupRefNode(Node node, Value groupValue) throws SheetEngineException, JepException {
        if (node instanceof ASTVarNode) {
            String name = ((ASTVarNode) node).getName();
            NodeFactory nodeFactory = new NodeFactory();

            if (TD_GROUP_VAL.equals(name)) {
                node = nodeFactory.buildConstantNode(groupValue);
            }
        }

        for (int i = 0; i < node.jjtGetNumChildren(); i++) {
            Node childNode = node.jjtGetChild(i);
            Node newChildNode = constructGroupRefNode(childNode, groupValue);
            if(childNode != newChildNode) {
                node.jjtAddChild(newChildNode, i);
                newChildNode.jjtSetParent(node);
            }
        }

        return node;
    }

    protected static Value getEvaluatedValue(Expression expressionTemplate, Value value, Workbook workbook) {
        ZSEvaluator evaluator = new ZSEvaluator(false);
        Expression transformedExpression = null;
        try {
            transformedExpression = ExpressionsLibrary.transformGroupRefExpression(expressionTemplate, value);
        } catch (JepException | SheetEngineException e) {
            LOGGER.log(Level.WARNING, ">>> Exception Occurred While Transforming Expressions", e);
            return Value.getInstance(Cell.Type.ERROR, Cell.Error.NAME);
        }

        Sheet dummySheet = workbook.getSheet(0);
        Object valueObject;
        try {
            valueObject = evaluator.evaluate(transformedExpression.getNode(), new CellImpl(new Row(dummySheet, 0), new Column(dummySheet, 0)), false, false);
        } catch(Exception ex) {
            valueObject = ex;
        }
        if(valueObject instanceof ZArrayI){
            return Value.getInstance(Cell.Type.ERROR, Cell.Error.NA);
        }else {
            return CellImpl.getAsValueObject(valueObject);
        }
    }

}
