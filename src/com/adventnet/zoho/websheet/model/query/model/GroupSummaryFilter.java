/* $Id$ */
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>-zt276 on  2019-04-11
 */
public class GroupSummaryFilter extends GroupValueFilter{

    private final Summary summary;

    public GroupSummaryFilter(Summary summary, GroupFilterOperator groupFilterOperator, Value filterValue) {
        super(groupFilterOperator, filterValue);
        this.summary = summary;
    }

    public Summary getSummary() {
        return summary;
    }

    protected List<AggregatedNode> filter(List<AggregatedNode> aggregatedNodes, Workbook workbook){

        if(!isStringFilterCondition()){
            return FILTER_FUNCTIONS_MAP.get(groupFilterOperator).apply(aggregatedNodes, summary, (Value)filterValue);
        }else {
            throw new IllegalArgumentException("String Functions Con't Be Applied Here.");//No I18N
        }
    }

    protected boolean isCacheable(){
        return false;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (o == null || getClass() != o.getClass()){
            return false;
        }
        if (!super.equals(o)){
            return false;
        }
        GroupSummaryFilter that = (GroupSummaryFilter) o;
        return Objects.equals(summary, that.summary);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), summary);
    }
}
