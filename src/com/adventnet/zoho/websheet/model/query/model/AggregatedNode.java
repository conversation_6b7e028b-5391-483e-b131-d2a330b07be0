//$Id$
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.Value;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>zt276 on  2019-05-29
 */
public class AggregatedNode {

    private final AggregatedNode parentNode;
    private final GroupByCriteria groupByCriteria;
    private final Integer sortAscIndex;
    private Integer firstOccurrenceIndex;
    private final Value value;

    private final BitSet rowIndices;
    private List<Integer> rows = null;

    protected AggregatedNode(AggregatedNode parentNode, GroupByCriteria groupByCriteria, Integer sortAscIndex, Value value, BitSet rowIndices) {
        this.parentNode = parentNode;
        this.groupByCriteria = groupByCriteria;
        this.sortAscIndex = sortAscIndex;
        this.value = value;
        this.rowIndices = rowIndices;
    }

    protected Integer getSortAscIndex(){
        return sortAscIndex;
    }

    protected Integer getFirstOccurrenceIndex() {
        if(firstOccurrenceIndex == null){
            firstOccurrenceIndex = rowIndices.nextSetBit(0);
        }
        return firstOccurrenceIndex;
    }

    protected Value getValue() {
        return value;
    }

    protected BitSet getRowsBitSet() {
        return rowIndices;
    }

    protected List<Integer> getRows()
    {
        if(rows == null)
        {
            rows = this.rowIndices.stream().boxed().collect(Collectors.toList());
        }

        return Collections.unmodifiableList(rows);
    }

    protected List<AggregatedNode> getIntersectingNodes(List<AggregatedNode> aggregatedNodes){
        if(this.rowIndices == null)
        {
            return aggregatedNodes;
        }

        long start = System.currentTimeMillis();
        List<AggregatedNode> resultNodes = new ArrayList<>();
        int rowSize = this.rowIndices.cardinality();
        int rowCount = 0;
        BitSet intersectingRowIndices = new BitSet();
        int count = 0;
        for (AggregatedNode node : aggregatedNodes) {
            if(count % 100 == 0) {
                if (System.currentTimeMillis() - start > 3000) {
                    throw new RuntimeException("Time taking for getIntersectingNodes limit reaches more than 3 secs"); // NO I18N
                }
            }
            count++;
            // If the parent and the child node values are present in same rows, we shall just use one of the bitset instead of creating a new memory.
            if(this.rowIndices.equals(node.rowIndices))
            {
                intersectingRowIndices = node.rowIndices;
            }
            else {
                intersectingRowIndices.or(this.rowIndices);
                intersectingRowIndices.and(node.rowIndices);
            }


            if (!intersectingRowIndices.isEmpty()) {
                // If the child bitset is a subset of parent, then we shall use child's bitset for the new node. instead of creating a new bitset in memory.
                if(intersectingRowIndices.equals(node.rowIndices))
                {
                    intersectingRowIndices = node.rowIndices;
                }
                Value nodeValue = node.getValue();
                rowCount += intersectingRowIndices.cardinality();
                resultNodes.add(new AggregatedNode(this, node.groupByCriteria, node.getSortAscIndex(), nodeValue, intersectingRowIndices));

                // reset intersectingRowIndices
                intersectingRowIndices = new BitSet();
            }
            if (rowCount == rowSize) {
                break;
            }
        }

        return resultNodes;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || !(o instanceof AggregatedNode)) {
            return false;
        }
        AggregatedNode that = (AggregatedNode) o;
        return Objects.equals(parentNode, that.parentNode)
                && Objects.equals(value, that.value)
                && Objects.equals(groupByCriteria, that.groupByCriteria);
    }

    @Override
    public int hashCode() {
        return Objects.hash(parentNode, value, groupByCriteria);
    }
}
