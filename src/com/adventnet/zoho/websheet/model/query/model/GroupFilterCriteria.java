/* $Id$ */
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;

import java.util.List;
import java.util.regex.Pattern;

public abstract class GroupFilterCriteria {
    protected abstract List<AggregatedNode> filter(List<AggregatedNode> aggregatedNodes, Workbook workbook);
    protected abstract boolean isSpecialFilterCondition();
    protected abstract boolean isCacheable();

    public static GroupFilterCriteria getInstance(GroupPatternFilter.GroupFilterOperator groupFilterOperator, Value filterValue, Workbook.PatternSetting patternSetting){

        Pattern pattern = null;
        if(filterValue.getType() == Cell.Type.STRING)
        {
            String filterValueStr = (String)filterValue.getValue();
            pattern = FunctionUtil.getIfPattern(filterValueStr, patternSetting);
        }

        if(pattern == null)
        {
            GroupValueFilter.GroupFilterOperator valueFilterOperator =  GroupValueFilter.GroupFilterOperator.EQUALS;
            if(groupFilterOperator == GroupPatternFilter.GroupFilterOperator.DOES_NOT_MATCH)
            {
                valueFilterOperator = GroupValueFilter.GroupFilterOperator.DOES_NOT_EQUALS;
            }

//            if(filterValue.getType() == Cell.Type.STRING && patternSetting == Workbook.PatternSetting.WILDCARD)
//            {
//                String filterValueStr = (String)filterValue.getValue();
//                filterValue = Value.getInstance(Cell.Type.STRING, filterValueStr.replaceAll("~([?*~])","$1"));
//            }
            return new GroupValueFilter(valueFilterOperator, filterValue);
        }
        else
        {
            return new GroupPatternFilter(groupFilterOperator, pattern);
        }
    }
}