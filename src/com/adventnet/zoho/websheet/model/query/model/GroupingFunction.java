/* $Id$ */
package com.adventnet.zoho.websheet.model.query.model;

import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Expression;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ValueWithPattern;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.query.model.GroupByCriteria.GroupByOperation;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.singularsys.jep.EvaluationException;

import java.util.Calendar;
import java.util.Date;
import java.util.logging.Logger;

/**
 * Created by k<PERSON><PERSON><PERSON>-zt276 on  2019-04-03
 */
public interface GroupingFunction {
    public Value extractGroupValue(Value value, Workbook workbook);

    public class GroupByExpression implements GroupingFunction{
        private static final Logger LOGGER = Logger.getLogger(GroupByExpression.class.getName());
        private final Expression expressionTemplate;


        public GroupByExpression(Expression expressionTemplate) {
            this.expressionTemplate = expressionTemplate;
        }

        @Override
        public Value extractGroupValue(Value value, Workbook workbook){
            return ExpressionsLibrary.getEvaluatedValue(expressionTemplate, value, workbook);
        }

        @Override
        public int hashCode() {
            return 31 + expressionTemplate.hashCode();
        }

        @Override
        public boolean equals(Object obj) {

            if(this == obj){
                return true;
            }

            if(obj instanceof GroupByExpression){
                GroupByExpression that = (GroupByExpression) obj;
                return this.expressionTemplate.equals(that.expressionTemplate);
            }
            return false;
        }
    }

    public static class GroupByValue implements GroupingFunction{

        private final GroupByOperation groupByOperation;

        public GroupByValue(GroupByOperation groupByOperation) {
            this.groupByOperation = groupByOperation;
        }


        @Override
        public Value extractGroupValue(Value value, Workbook workbook){
            return getEvaluateValue(value, workbook);
        }

        private Value getEvaluateValue(Value value, Workbook workbook){
            if(value == null) {
                return Value.EMPTY_VALUE;
            }
            Type type = value.getType();
            Value evaluatedValue = null;
            switch (groupByOperation) {
                case DEFAULT:
                    evaluatedValue = value;
                    break;
                case DATA_TYPE:
                    if(value instanceof ValueWithPattern){
                        evaluatedValue = Value.getInstance(Type.STRING, ((ValueWithPattern) value).getPattern().getType().toString());
                    }else {
                        evaluatedValue = Value.getInstance(Type.STRING, value.getType().toString());
                    }
                    break;
                case CASE_INSENSITIVE:
                    if (Type.STRING == type) {
                        evaluatedValue = ((ZSString) value.getRawValue()).getLowercaseString();
                    }
                    break;
                case CONVERTED_VALUES:
                    if (Type.STRING == type) {
                        evaluatedValue = ((String)value.getValue()).isEmpty() ? Value.EMPTY_VALUE : ((ZSString) value.getRawValue()).getConvertedValue(workbook.getSpreadsheetSettings());
                    }else{
                        evaluatedValue = value;
                    }

                    if(value.getValue() instanceof Throwable){
                        evaluatedValue = Value.getInstance(Type.STRING, ((Throwable) value.getValue()).getMessage());
                    }else if(evaluatedValue.getType() == Type.STRING){
                        evaluatedValue = ((ZSString) evaluatedValue.getRawValue()).getLowercaseString();
                    }else if(Type.BOOLEAN == evaluatedValue.getType()){
                        evaluatedValue = (Boolean) evaluatedValue.getValue() ? Value.getInstance(Type.FLOAT, 1) : Value.getInstance(Type.FLOAT, 0);
                    } else if(evaluatedValue.getType().isDateType()) {
                        try {
                            evaluatedValue = Value.getInstance(Type.FLOAT,FunctionUtil.objectToNumber(evaluatedValue.getValue()));
                        } catch (EvaluationException e) {
                            evaluatedValue = Value.getInstance(Type.ERROR, e);
                        }
                    }

                    break;
                case CONVERTED_NON_STRING_VALUES:
                    if (Type.STRING == type) {
                        evaluatedValue = ((String)value.getValue()).isEmpty() ? Value.EMPTY_VALUE : ((ZSString) value.getRawValue()).getConvertedValue(workbook.getSpreadsheetSettings());
                    }else {
                        evaluatedValue = value;
                    }

                    if(Type.BOOLEAN == evaluatedValue.getType()){
                        evaluatedValue = (Boolean) evaluatedValue.getValue() ? Value.getInstance(Type.FLOAT, 1) : Value.getInstance(Type.FLOAT, 0);
                    }else if(evaluatedValue.getType() == Type.UNDEFINED || evaluatedValue.getType() == Type.STRING || evaluatedValue.getType() == Type.ERROR){
                        return null;
                    }
                    break;
                case CONVERTED_STRING_VALUES:
                    if (Type.STRING == type) {
                        evaluatedValue = ((String)value.getValue()).isEmpty() ? Value.EMPTY_VALUE : ((ZSString) value.getRawValue()).getConvertedValue(workbook.getSpreadsheetSettings());
                    }else {
                        evaluatedValue = value;
                    }

                    if(evaluatedValue.getType() == Type.STRING){
                        evaluatedValue = ((ZSString) evaluatedValue.getRawValue()).getLowercaseString();
                    }else if(evaluatedValue.getValue() instanceof Throwable){
                        evaluatedValue = Value.getInstance(Type.STRING, ((Throwable) evaluatedValue.getValue()).getMessage());
                    }else {
                        return null;
                    }
                    break;
                case DAY:
                    if (type.isDateType()) {
                        Object valueObject = value.getValue();
                        Date date = valueObject instanceof Number ? DateUtil.convertNumberToDate((Number) valueObject) : (Date) valueObject;
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(date);
                        evaluatedValue = Value.getInstance(Type.FLOAT, calendar.get(Calendar.DATE));
                    }
                    break;
                case WEEKDAY:
                    if (type.isDateType()) {
                        Object valueObject = value.getValue();
                        Date date = valueObject instanceof Number ? DateUtil.convertNumberToDate((Number) valueObject) : (Date) valueObject;
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(date);
                        evaluatedValue = Value.getInstance(Type.FLOAT, calendar.get(Calendar.DAY_OF_WEEK));
                    }
                    break;
                case MONTH:
                    if (type.isDateType()) {
                        Object valueObject = value.getValue();
                        Date date = valueObject instanceof Number ? DateUtil.convertNumberToDate((Number) valueObject) : (Date) valueObject;
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(date);
                        evaluatedValue = Value.getInstance(Type.FLOAT, calendar.get(Calendar.MONTH));
                    }
                    break;
                case QUARTER:
                    if (type.isDateType()) {
                        Object valueObject = value.getValue();
                        Date date = valueObject instanceof Number ? DateUtil.convertNumberToDate((Number) valueObject) : (Date) valueObject;
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(date);
                        evaluatedValue = Value.getInstance(Type.FLOAT, calendar.get(Calendar.MONTH)/3);
                    }
                    break;
                case YEAR:
                    if (type.isDateType()) {
                        Object valueObject = value.getValue();
                        Date date = valueObject instanceof Number ? DateUtil.convertNumberToDate((Number) valueObject) : (Date) valueObject;
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(date);
                        evaluatedValue = Value.getInstance(Type.FLOAT, calendar.get(Calendar.YEAR));
                    }
                    break;
                default:
                    throw new IllegalArgumentException(">>>>> Invalid GroupBy Operation"); // No I18N
            }

            return evaluatedValue == null ? value : evaluatedValue;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if(obj instanceof GroupByValue){
                GroupByValue that = (GroupByValue) obj;
                return this.groupByOperation.equals(that.groupByOperation);
            }
            return false;
        }

        @Override
        public int hashCode() {
            return groupByOperation.hashCode();
        }
    }


}
