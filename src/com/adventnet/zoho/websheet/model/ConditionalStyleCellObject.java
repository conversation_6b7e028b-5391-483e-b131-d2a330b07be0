//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;

import java.util.HashSet;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */

public class ConditionalStyleCellObject 
{
    private Set<ConditionalStyleObject> conditionalStyleObjects;
    
    public Set<ConditionalStyleObject> getConditionalStyleObjects() {
        return conditionalStyleObjects;
    }

    public boolean addConditionalStyleObjects(ConditionalStyleObject conditionalStyleObject) 
    {
        if(conditionalStyleObjects == null)
        {
            conditionalStyleObjects = new HashSet<>();
        }
        return conditionalStyleObjects.add(conditionalStyleObject);
    }
}