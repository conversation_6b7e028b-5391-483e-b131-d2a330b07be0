//$Id$
/*
 * NodeProcessorFactory.java
 *
 * Created on September 22, 2009, 12:40 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.parser;

import org.apache.commons.collections4.map.MultiKeyMap;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.util.XmlPullUtil;

import java.io.IOException;

/**
 *
 * <AUTHOR>
 */
class NodeProcessorFactory implements Names
{

   static NodeProcessorFactory npFactory = new NodeProcessorFactory();
   static NodeProcessorFactory npFactory1 = new NodeProcessorFactory1();
    ////    NodeProcessorFactory -> START   ////

        NodeProcessor getInstance(String prefix, String lName)
        {
            // do null check for args...
            if (mToHandleNodes.containsKey(prefix, lName))
            {
                return (NodeProcessor) mToHandleNodes.get(prefix, lName);
            }
            return skipImpl;
        }

        MultiKeyMap mToHandleNodes = new MultiKeyMap();
        /** remaining nodes will just be skipped */
        {
            put(nOfficeDoc, formDocumentNode(nOfficeDoc));
//          |___
                put(nOfficeScripts, formScriptsNode(nOfficeScripts));
                put(nFontDecls, formFontDeclsNode());
            put(nFontDecls, formFontDeclsNode());
//          +   |___
            put(nFontDecl, formFontNode());
//          |___
            put(nAutomaticStyles, formAutoStylesNode());
//          +   |___
            put(nStyle, formStyleNode());
//          +   +	|___
                        put(nTableColProps, formStylePropsNode());
                        put(nTableRowProps, formStylePropsNode());
                        put(nTableProps, formStylePropsNode());

                        put(nTableCellProps, formStylePropsNode());
                        put(nParagraphProps, formStylePropsNode());
                        put(nTextProps, formStylePropsNode());
                        put(nGraphicProps, formStylePropsNode());
                        
            put(nPatterns, formPatternsNode());
//          +   +	|___
                        put(nPattern, formPatternNode());

//          +   |___  To handle Number-Styles
            put(nNumberStyle, formNumberStyleNode());
//          +   +       |___
                        put(nNumber,formNumberNode());
//          +   +       +       |___
                                put(nEmbeddedText, formEmbeddedTextNode());
//          +   +       +       |___    
                                put(nSpaceIndexs, formSpaceIndexsNode());

                                put(nRepeatIndexs, formRepeatIndexNode());
//          +   +       |___
                        put(nScientificNumber,formNumberNode());
//          +   +       |___
                        put(nFraction,formNumberNode());
//          +   +       |___
                        put(nText_Number,formNumberNode());
//          +   +	|___
                        put(nTextProps, formStylePropsNode());
//          +   +	|___
                        put(nMapStyle, formStylePropsNode());

                        put(nFill, formNumberNode());

                        put(nText_Loext, formNumberNode());

//          +   |___To handle Number:text-style
            //put(nTextStyle_Number, formNumberTextStyleNode());


//          +   |___  To handle Percentage-Styles
            put(nPercentageStyle, formPercentageStyleNode());
//          +   +       |___
                        put(nNumber,formNumberNode());
//          +   +	|___
                        put(nTextProps, formStylePropsNode());
//          +   +	|___
                        put(nMapStyle, formStylePropsNode());
//          +   +       |___
                        put(nText_Number,formNumberNode());

//          +   |___  To handle currency-Styles
            put(nCurrencyStyle, formCurrencyStyleNode());
//          +   +       |___
                        put(nNumber,formNumberNode());
//          +   +       |___
                        put(nCurrencySymbol,formNumberNode());
//          +   +       |___
                        put(nText_Number,formNumberNode());
//          +   +	|___
                        put(nTextProps, formStylePropsNode());
//          +   +	|___
                        put(nMapStyle, formStylePropsNode());

//            put(nAccountingStyle, formAccountingStyleNode());
//                        put(nNumber,formNumberNode());
////          +   +       |___
//                        put(nCurrencySymbol,formNumberNode());
////          +   +       |___
//                        put(nText_Number,formNumberNode());
////          +   +	|___
//                        put(nTextProps, formStylePropsNode());
////          +   +	|___
//                        put(nMapStyle, formStylePropsNode());
//          +   |___  To handle Date-Styles
            put(nDateStyle, formDateStyleNode());
//          +   +	|___
                        put(nQuarter, formNumberNode());
//          +   +	|___
                        put(nWeekOfYear, formNumberNode());
//          +   +	|___
                        put(nDayOfWeek, formNumberNode());
//          +   +	|___
                        put(nEra, formNumberNode());
//          +   +	|___
                        put(nYear, formNumberNode());
//          +   +	|___
                        put(nMonth, formNumberNode());
//          +   +	|___
                        put(nDay, formNumberNode());
//          +   +	|___
                        put(nHours, formNumberNode());
//          +   +	|___
                        put(nMinutes, formNumberNode());
//          +   +	|___
                        put(nSeconds, formNumberNode());
//          +   +	|___
                        put(nAM_PM, formNumberNode());
//          +   +       |___
                        put(nText_Number,formNumberNode());
//          +   +	|___
                        put(nTextProps, formStylePropsNode());
//          +   +	|___
                        put(nMapStyle, formStylePropsNode());

//          +   |___  To handle boolean-Styles
            put(nBooleanStyle, formBooleanStyleNode());
//          +   +	|___
                        put(nBoolean, formNumberNode());
//          +   +       |___
                        put(nText_Number,formNumberNode());
//          +   +	|___
                        put(nTextProps, formStylePropsNode());
//          +   +	|___
                        put(nMapStyle, formStylePropsNode());

//          +   |___  To handle Number-Styles
            put(nTimeStyle, formTimeStyleNode());
//          +   +	|___
                        put(nHours, formNumberNode());
//          +   +	|___
                        put(nMinutes, formNumberNode());
//          +   +	|___
                        put(nSeconds, formNumberNode());
//          +   +	|___
                        put(nAM_PM, formNumberNode());
//          +   +       |___
                        put(nText_Number,formNumberNode());
//          +   +	|___
                        put(nTextProps, formStylePropsNode());
//          +   +	|___
                        put(nMapStyle, formStylePropsNode());

            put(nTextStyle, formTextStyleNode());
//          +   +       |___
                        put(nTextContent, formNumberNode());
//          +   +       |___
                        put(nText_Number,formNumberNode());
//          +   +	|___
                        put(nTextProps, formStylePropsNode());
//          +   +	|___
                        put(nMapStyle, formStylePropsNode());
//          +   +	|___TODO DO THIS
                        //put(nTextContent, formStylePropsNode());

//          |___
            put(nBody, formTraverseNode(nBody));
//          +   |___ this part is for chart content.xml
//                put(nChart_Office, formTraverseNode(nChart_Office));
////          +   +   |___
//                    put(nChart, formChartNode());
////          +   +   +   |___
//                        put(nTitle, formTitleNode());
////          +   +   +   +   |___
//                            put(nTextP, formTextNode());
////          +   +   +   |___
//                        put(nLegend, formLegendNode());
////          +   +   +   |___
//            put(nPlotArea, formPlotAreaNode());
////          +   +   +   |___
//                        put(nAxis, formAxisNode());
            // chart content ends here
//          +   |___
            put(nSpreadSheet, formSpreadsheetNode());
//          +   |___
            //put(nShapes, formTraverseNode(nShapes));
//          +   +   |___
           // put(nFrame, formFrameNode());
//          +   +   |___
            //put(nImage, formImageNode());
//          +   +   +   |___
            //put(nTextP, formTextNode());
//          +   |___
            put(NCONTENT_VALIDATIONS, formContentValidationsNode());
  //                  |____
                       put(NCONTENT_VALIDATION, formContentValidationNode());
 //                          |___
                                 put(NTABLE_HELPMESSAGE, formHelpMessageNode());
 //                                |___
                                 put(NTABLE_ERRORMESSAGE, formErrorMessageNode());

            put(NPICKLISTS, formPicklistsNode());
//                    |___
                        put(NPICKLIST, formPicklistNode());
//                        |__
                            put(NPICKLISTITEM, formPicklistItemNode());

            put(nNamedExpressions, formNamedExpressionsNode());
//  		+   +   |___
		    put(nNamedRange, formNamedRangeNode());
//  		+   +   |___
		    put(nNamedExpression, formNamedExpressionNode());
//          +   |___

            put(nFilters, formTraverseNode(nFilters)); // table:zs-filters

	        put(nDatabaseRanges, formTraverseNode(nDatabaseRanges)); // table:database-ranges
//  		+   +   |___

            put(nDatabaseRange, formDataRangeNode()); // table:database-range

//            put(nFilterViews, formTraverseNode(nFilterViews)); // table:filter-views
//            put(nDefaultFilterView, formDefaultFilterView()); // table:default-filter-view
//            put(nFilterView, formFilterViewNode()); // table:filter-view
//	  		+   +   |___
			    put(nFilter, formFilterNode()); //  table:filter
//		  		+   +   |___
				    put(nFilterOR, formFilterORNode()); //  table:filter-or
				    put(nFilterAND, formFilterANDNode());// table:filter-and
//			  		+   +   |___ // this can be child or at same level of or/and
				    put(nFilterCondition, formFilterConditionNode());   //  table:filter-condition
				    
				    

//          +   |___
	    put(nTable, formSheetNode());
    //          +   |___
		put(nTableHeaderColumns, formTraverseNode(nTableHeaderColumns)); // not there
    //          +   |___
		put(nTableColumns, formTraverseNode(nTableColumns)); // not there
    //          +   |___
		put(nTableColumnGroup, formColumnGroupNode()); // not there
    //          +   |___
		put(nTableColumn, formSheetColumnNode());
    //          +   |___
		put(nShapes, formShapesNode(nShapes));
    //          +   |___
                put(nOfficeForms, formOfficeFormsNode(nOfficeForms));//ADDED BY GANESH
//              +   |___
	                put(nFormForm, formFormFormNode(nFormForm));//ADDED BY Nilam
//		      +   |___
			    put(nFormButton, formFormButtonNode(nFormButton));//ADDED BY Nilam
//	              +   |___
		            put(nFormProperties, formTraverseNode(nFormProperties));//ADDED BY Nilam
//		          +   |___
			        put(nFormProperty, formFormPropertyNode(nFormProperty));//ADDED BY Nilam
//	              +   |___
		            put(nOfficeEventListeners, formTraverseNode(nOfficeEventListeners));//ADDED BY Nilam
//		          +   |___
			        put(nScriptEventListener, formScriptEventListenerNode(nScriptEventListener));//ADDED BY Nilam
				
    //          +   |___
                put(nTableHeaderRows, formTraverseNode(nTableHeaderRows));
//          +   |___
            put(nTableRows, formTraverseNode(nTableRows));
//          +   |___
            put(nTableRow, formSheetRowNode());
//          +   +	|___
            put(nTableRowGroup, formRowGroupNode());
//          +   +	|___
            put(nCoveredTableCell, formCoveredCellNode());
//          +   +	+	|___
                put(nAnnotation, formAnnotateNode());
//          +   +	+	|___
                put(nAttachment, formAttachmentNode());
//          +   +   +   |___                
                put(nDate, formDateNode());
//          +   +	+	|___
                put(nTextP, formTextNode());
//          +   +	|___
            put(nTableCell, formCellNode());
//          +   +	+	|___
                put(nAnnotation, formAnnotateNode());
//          +   +	+	|___
                put(nDate, formDateNode());
//          +   +	+	|___
                put(nTextP, formTextNode());

                put(N_DRAW_CUSTOM_SHAPE, formCustomShapeNode());
                
                put(N_DRAW_ENHANCED_GEOMETRY, formEnhancedGeometryNode());
                
                put(N_DRAW_EQUATION, formEquationNode());
            
                put(N_DRAW_HANDLE, formHandleNode());

                put(N_DRAW_LINE, formLineNode());
                
                put(N_DRAW_PATH, formPathNode());
                
                put(N_DRAW_POLY_LINE, formPolyLineNode());
                
                put(N_DRAW_ELLIPSE, formEllipseNode());
                
                put(nDrawG, formGroupNode());
                
                put(nDrawA, formAnchorNode());
/*          +   +	+	|___    
                put(NTABLE_DETECTIVE,formDetectiveNode());
//                   +   +	+	|___    
                                put(NTABLE_HIGHLIGHTEDRANGE,formHighlightedRangeNode());*/
//	    +	+	+	|_
		put(nDrawControl, formDrawControlNode());
                put(nCondition_formats, formConditionalFormatsNode());
//                    |____
                        put(nCondition_format, formConditionFormatNode());
//                        |__
                            put(nDateCALC, formConditionDateNode());
                            put(nCondition, formConditionNode());
                            put(nColor_scale, formColorScaleNode());
//                            |__
                                put(nColor_Scale_entry, formColorScaleEntryNode());
                            put(nIcon_Set, formIconSetNode());
                            put(nData_Bar, formDataBarNode());
//                            |__
                                put(nFormatting_entry, formIconSetAndDataBarEntry());
            put(ZSsparklines,formSparklinesNode());
//                    |____
                        put(ZSsparklines_group,formSparklinesGroupNode());
//                        |__
                            put(properties,formSparklineProperties());
                            put(ZSsparkline,formSparklineNode());

            put(A_IMAGES, formSheetImagesNode());
//          +   |
            put(A_IMAGE, formSheetImageNode());

            //For styles.xml
            put(nOfficeDocStyle, formDocumentNode(nOfficeDocStyle));
//          +   |__office-styles
                put(nStyles, formStylesNode());
//          +   |___
                put(defaultColumnWidth, formDefaultColumnWidthNode());
//          +   |___style-default-style (cellStyle)
                put(nDefaultStyle, formStyleNode());
//          +   +	|___
                        put(nTableCellProps, formStylePropsNode());
                        put(nParagraphProps, formStylePropsNode());
                        put(nTextProps, formStylePropsNode());
                        put(nGraphicProps, formStylePropsNode());
//          +   +   |___  To handle currency-Styles
                    put(nCurrencyStyle, formCurrencyStyleNode());
//          +   +   +   |___
                        put(nNumber,formNumberNode());
//          +   +   +   |___
                        put(nCurrencySymbol,formNumberNode());
//          +   +   +   |___
                        put(nText_Number,formNumberNode());
//          +   +   +   |___
                        put(nTextProps, formStylePropsNode());
//          +   +   +   |___
                        put(nMapStyle, formStylePropsNode());

                    put(nDateStyle, formDateStyleNode());
//          +   +   +   |___
                        put(nQuarter, formNumberNode());
//          +   +   +	|___
                        put(nWeekOfYear, formNumberNode());
//          +   +   +	|___
                        put(nDayOfWeek, formNumberNode());
//          +   +   +	|___
                        put(nEra, formNumberNode());
//          +   +   +	|___
                        put(nYear, formNumberNode());
//          +   +   +	|___
                        put(nMonth, formNumberNode());
//          +   +   +	|___
                        put(nDay, formNumberNode());
//          +   +   +	|___
                        put(nHours, formNumberNode());
//          +   +   +	|___
                        put(nMinutes, formNumberNode());
//          +   +   +	|___
                        put(nSeconds, formNumberNode());
//          +   +   +	|___
                        put(nAM_PM, formNumberNode());
//          +   +   +   |___
                        put(nText_Number,formNumberNode());
//          +   +   +	|___
                        put(nTextProps, formStylePropsNode());
//          +   +   +	|___
                        put(nMapStyle, formStylePropsNode());
//          +   |
            put(nDataPilotTables, formTraverseNode(nDataPilotTables));
//          +   |
                put(nDataPilotTable, formDataPilotTableNode());
//          +   +   |
                    put(nSourceCellRange, formSourceCellRangeNode());
//          +   +   |
                    put(nDataPilotField, formDataPilotFieldNode());
//          +   +   +   |
                        put(nDataPilotFieldReference, fromDataPilotFieldReferenceNode());
//          +   +   +   |
                        put(nDataPilotLevel, formDataPilotLevelNode());
//          +   +   +   +   |
                            put(nDataPilotDisplayInfo, formDataPilotDisplayInfoNode());
//          +   +   +   +   |
                            put(nDataPilotSortInfo, formDataPilotSortInfoNode());
//          +   +   +   +   |
                            put(nDataPilotLayoutInfo, formDataPilotLayoutInfoNode());
//          +   +   +   +   |
                            put(nDataPilotFilterInfo, formDataPilotFilterInfoNode());
//          +   +   +   +   |
                            put(nDataPilotMembers, formTraverseNode(nDataPilotMembers));
//          +   +   +   +   +   |
                                put(nDataPilotMember, formDataPilotMemberNode());
//          +   +   +   +   |
                            put(nDataPilotSubtotals, formTraverseNode(nDataPilotSubtotals));
//          +   +   +   +   +   |
                                put(nDataPilotSubtotal, formDataPilotSubtotalNode());
//          +   +   +   |
                        put(nDataPilotGroups, formDataPilotGroupsNode());
//          +   +   +   +   |
                        	put(nDataPilotGroup, formDataPilotGroupNode());
//          +   +   +   +   +   |
                        		put(nDataPilotGroupMember, formDataPilotGroupMemberNode());
/*                //by bhawani for frame
 //We don't parse Shapes from 'ods' as of now, below code can be used when we do.                                
            put(nDrawFrame, formDrawFrameNode());
            put(nDrawImage, formDrawImageNode());
            put(nSvgTitle, formSvgTitleNode());
            put(nSvgDesc, formSvgDescNode());
            
            //Node:draw:frame might be child element of this tag if these are not added this node will be escaped
                 //their might be other parent element of draw:frame which might be added later 
            put(nDrawA, formTraverseNode(nDrawA));
            put(nDrawG, formTraverseNode(nDrawG));
        //end bhawani
*/


            put(expressions, formExpressionsNode());
//          +   |
                put(expression, formExpressionNode());
                put(nDrawFrame, formDrawFrameNode());
                put(nDrawImage, formDrawImageNode());
                put(N_DRAW_OBJECT, formDrawObjectNode());
        }

        void put(XmlName node, NodeProcessor nodeProcessor)
        {
            mToHandleNodes.put(node.prefix, node.lName, nodeProcessor);
        }

        private NodeProcessor skipImpl = new NodeProcessor()
        {
            public void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                XmlPullUtil.skipSubTree(parser.xpp);
            }
        };

        private NodeProcessor formTraverseNode(XmlName name)
        {
            NodeProcessor toRet = new NodeProcessor(name)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.traverseNode(name);
                }
            };
            return toRet;
        }

        private NodeProcessor formDocumentNode(XmlName name)
        {
            NodeProcessor toRet = new NodeProcessor(name)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDocumentNode(name);
                }
            };
            return toRet;
        }
       ///////////////////////ADDED BY GANESH
        private NodeProcessor formScriptsNode(XmlName name)
        {
            NodeProcessor toRet = new NodeProcessor(name)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processScriptsNode(name);
                }
            };
            return toRet;
        }

	private NodeProcessor formShapesNode(XmlName name)
        {
            NodeProcessor toRet = new NodeProcessor(name)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processShapesNode(name);
                }
            };
            return toRet;
        }


        private NodeProcessor formOfficeFormsNode(XmlName name)
        {
            NodeProcessor toRet = new NodeProcessor(name)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFormsNode(name);
                }
            };
            return toRet;
        }

	private NodeProcessor formFormFormNode(XmlName name)
        {
            NodeProcessor toRet = new NodeProcessor(name)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFormFormNode(name);
                }
            };
            return toRet;
        }

	private NodeProcessor formFormButtonNode(XmlName name)
        {
            NodeProcessor toRet = new NodeProcessor(name)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFormButtonNode(name);
                }
            };
            return toRet;
        }

	private NodeProcessor formFormPropertyNode(XmlName name)
        {
            NodeProcessor toRet = new NodeProcessor(name)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFormPropertyNode(name);
                }
            };
            return toRet;
        }

	private NodeProcessor formScriptEventListenerNode(XmlName name)
        {
            NodeProcessor toRet = new NodeProcessor(name)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processScriptEventListenerNode(name);
                }
            };
            return toRet;
        }
	
        private NodeProcessor formNumberNode()
        {
            NodeProcessor toRet = new NodeProcessor(nNumber)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processNumberNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formEmbeddedTextNode()
        {
            NodeProcessor toRet = new NodeProcessor(nEmbeddedText)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processEmbeddedTextNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formSpaceIndexsNode()
        {
            NodeProcessor toRet = new NodeProcessor(nSpaceIndexs)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processSpaceIndexsNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formRepeatIndexNode()
        {
            NodeProcessor toRet = new NodeProcessor(nRepeatIndexs)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processRepeatIndexNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formPatternsNode() {
            NodeProcessor toRet = new NodeProcessor(nPatterns) {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException {
                    parser.processPatternsNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formPatternNode() {
            NodeProcessor toRet = new NodeProcessor(nPattern) {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException {
                    parser.processPatternNode();
                }
            };
            return toRet;
        }
        
         private NodeProcessor formNumberStyleNode()
        {
            NodeProcessor toRet = new NodeProcessor(nNumberStyle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processNumberStyleNode();
                }
            };
            return toRet;
        }

         private NodeProcessor formPercentageStyleNode()
        {
            NodeProcessor toRet = new NodeProcessor(nPercentageStyle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processPercentageStyleNode();
                }
            };
            return toRet;
        }

	 // Duplicate of formTextStyleNode()
//         private NodeProcessor formNumberTextStyleNode()
//        {
//            NodeProcessor toRet = new NodeProcessor(nTextStyle_Number)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processNumberTextStyleNode();
//                }
//            };
//            return toRet;
//        }

        private NodeProcessor formDateStyleNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDateStyle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDateStyleNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formCurrencyStyleNode()
        {
            NodeProcessor toRet = new NodeProcessor(nCurrencyStyle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processCurrencyStyleNode();
                }
            };
            return toRet;
        }

//        private NodeProcessor formAccountingStyleNode()
//        {
//            NodeProcessor toRet = new NodeProcessor(nAccountingStyle)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processAccountingStyleNode();
//                }
//            };
//            return toRet;
//        }

        private NodeProcessor formBooleanStyleNode()
        {
            NodeProcessor toRet = new NodeProcessor(nBooleanStyle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processBooleanStyleNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formTimeStyleNode()
        {
            NodeProcessor toRet = new NodeProcessor(nTimeStyle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processTimeStyleNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formTextStyleNode()
        {
            NodeProcessor toRet = new NodeProcessor(nTextStyle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processTextStyleNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formAnnotateNode()
        {
            NodeProcessor toRet = new NodeProcessor(nAnnotation)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processAnnotateNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formAttachmentNode()
        {
            NodeProcessor toRet = new NodeProcessor(nAttachment)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processAttachmentNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formDateNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDate)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDateNode();
                }
            };
            return toRet;
        }
     /*   private NodeProcessor formDetectiveNode()
        {
            NodeProcessor toRet = new NodeProcessor(NTABLE_DETECTIVE)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDetectiveNode();
                }
            };
            return toRet;
        }
        private NodeProcessor formHighlightedRangeNode()
        {
            NodeProcessor toRet = new NodeProcessor(NTABLE_HIGHLIGHTEDRANGE)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processHighlightedRangeNode();
                }
            };
            return toRet;
        }*/



        //For styles.xml
//        private NodeProcessor formDocStyleNode(XmlName name)
//        {
//            NodeProcessor toRet = new NodeProcessor(name)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processDocStyleNode(name);
//                }
//            };
//            return toRet;
//        }

        //For styles.xml
        private NodeProcessor formStylesNode()
        {
            NodeProcessor toRet = new NodeProcessor(nStyle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processStylesNode();
                }
            };
            return toRet;
        }
        ////////////END OF GANESH
        private NodeProcessor formFontDeclsNode()
        {
            NodeProcessor toRet = new NodeProcessor(nFontDecls)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFontDeclsNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formAutoStylesNode()
        {
            NodeProcessor toRet = new NodeProcessor(nAutomaticStyles)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processAutoStylesNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formFontNode()
        {
            NodeProcessor toRet = new NodeProcessor(nFontDecl)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFontNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formTextNode()
        {
            NodeProcessor toRet = new NodeProcessor(nTextP)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processTextNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formCellNode()
        {
            NodeProcessor toRet = new NodeProcessor(nTableCell)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processCellNode();
                }
            };
            return toRet;
        }

	private NodeProcessor formDrawControlNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDrawControl)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDrawControlNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formCoveredCellNode()
        {
            NodeProcessor toRet = new NodeProcessor(nCoveredTableCell)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processCoveredCellNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formSheetRowNode()
        {
            NodeProcessor toRet = new NodeProcessor(nTableRow)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processSheetRowNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formSheetColumnNode()
        {
            NodeProcessor toRet = new NodeProcessor(nTableColumn)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processSheetColumnNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formConditionalFormatsNode()
        {
            NodeProcessor toRet = new NodeProcessor(nCondition_formats)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processConditionalFormatsNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formConditionFormatNode()
        {
            NodeProcessor toRet = new NodeProcessor(nCondition_format)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processConditionFormatNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formConditionNode()
        {
            NodeProcessor toRet = new NodeProcessor(nCondition)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processConditionNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formConditionDateNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDateCALC)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processConditionDateNode();
                }
            };
            return toRet;
        }

    private NodeProcessor formSparklineNode() {
        NodeProcessor toRet = new NodeProcessor(ZSsparkline)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processSparklineNode();
            }
        };
        return toRet;
    }

    private NodeProcessor formSparklinesGroupNode() {
        NodeProcessor toRet = new NodeProcessor(ZSsparklines_group)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processSparklinesGroupNode();
            }
        };
        return toRet;
    }

    private NodeProcessor formSparklineProperties() {
        NodeProcessor toRet = new NodeProcessor(properties)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processSparklinePropertiesNode();
            }
        };
        return toRet;
    }
    private NodeProcessor formSparklinesNode() {
        NodeProcessor toRet = new NodeProcessor(ZSsparklines)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processSparklinesNode();
            }
        };
        return toRet;
    }

        
        private NodeProcessor formColorScaleNode()
        {
            NodeProcessor toRet = new NodeProcessor(nColor_scale)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processColorScaleNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formColorScaleEntryNode()
        {
            NodeProcessor toRet = new NodeProcessor(nColor_Scale_entry)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processColorScaleEntryNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formIconSetNode()
        {
            NodeProcessor toRet = new NodeProcessor(nIcon_Set)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processIconSetNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formDataBarNode()
        {
            NodeProcessor toRet = new NodeProcessor(nData_Bar)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataBarNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formIconSetAndDataBarEntry()
        {
            NodeProcessor toRet = new NodeProcessor(nFormatting_entry) 
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processIconSetAndDataBarEntryNode();
                }
            };
            return toRet;
        }

    private NodeProcessor formPicklistsNode()
    {
        NodeProcessor toRet = new NodeProcessor(NPICKLISTS) {
            @Override
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException {
                parser.processPicklistsNode();
            }
        };

        return toRet;
    }

    private NodeProcessor formPicklistNode()
    {
        NodeProcessor toRet = new NodeProcessor(NPICKLIST) {
            @Override
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException {
                parser.processPicklistNode();
            }
        };

        return toRet;
    }

    private NodeProcessor formPicklistItemNode()
    {
        NodeProcessor toRet = new NodeProcessor() {
            @Override
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException {
                parser.processListItemNode();
            }
        };

        return toRet;
    }

        private NodeProcessor formContentValidationsNode()
        {
            NodeProcessor toRet = new NodeProcessor(NCONTENT_VALIDATIONS)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processContentValidationsNode();
                }
            };

            return toRet;
        }

        private NodeProcessor formContentValidationNode()
        {
            NodeProcessor toRet = new NodeProcessor(NCONTENT_VALIDATION)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processContentValidationNode();
                }
            };

            return toRet;
        }


        private NodeProcessor formHelpMessageNode()
        {
            NodeProcessor toRet = new NodeProcessor(NTABLE_HELPMESSAGE)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processHelpMessageNode();
                }
            };

            return toRet;
        }


        private NodeProcessor formErrorMessageNode()
        {
            NodeProcessor toRet = new NodeProcessor(NTABLE_ERRORMESSAGE)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processErrorMessageNode();
                }
            };

            return toRet;
        }

        private NodeProcessor formNamedExpressionsNode()
        {
            NodeProcessor toRet = new NodeProcessor(nNamedExpressions)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processNamedExprssionsNode();
                }
            };

            return toRet;
        }

        private NodeProcessor formNamedExpressionNode()
        {
            NodeProcessor toRet = new NodeProcessor(nNamedExpression)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processNamedExpressionNode();
                }
            };

            return toRet;
        }

        private NodeProcessor formNamedRangeNode()
        {
            NodeProcessor toRet = new NodeProcessor(nNamedRange)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processNamedRangeNode();
                }
            };

            return toRet;
        }


    private NodeProcessor formDataRangeNode()
    {
        NodeProcessor toRet = new NodeProcessor(nDatabaseRange)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processDataRangeNode();
            }
        };

        return toRet;
    }

	private NodeProcessor formFilterNode()
        {
            NodeProcessor toRet = new NodeProcessor(nFilter)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFilterNode();
                }
            };

            return toRet;
        }

	private NodeProcessor formFilterORNode()
        {
            NodeProcessor toRet = new NodeProcessor(nFilterOR)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFilterORNode();
                }
            };

            return toRet;
        }

	private NodeProcessor formFilterANDNode()
        {
            NodeProcessor toRet = new NodeProcessor(nFilterAND)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFilterANDNode();
                }
            };

            return toRet;
        }

	private NodeProcessor formFilterConditionNode()
        {
            NodeProcessor toRet = new NodeProcessor(nFilterCondition)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFilterConditionNode();
                }
            };

            return toRet;
        }

//        private NodeProcessor formFrameNode()
//        {
//            NodeProcessor toRet = new NodeProcessor(nFrame)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processFrameNode();
//                }
//            };
//
//            return toRet;
//        }

//        private NodeProcessor formImageNode()
//        {
//            NodeProcessor toRet = new NodeProcessor(nImage)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processImageNode();
//                }
//            };
//
//            return toRet;
//        }

//        private NodeProcessor formChartNode()
//        {
//            NodeProcessor toRet = new NodeProcessor(nChart)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processChartNode();
//                }
//            };
//
//            return toRet;
//        }

//        private NodeProcessor formTitleNode()
//        {
//            NodeProcessor toRet = new NodeProcessor(nTitle)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processTitleNode();
//                }
//            };
//
//            return toRet;
//        }

//        private NodeProcessor formLegendNode()
//        {
//            NodeProcessor toRet = new NodeProcessor(nLegend)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processLegendNode();
//                }
//            };
//
//            return toRet;
//        }

//        private NodeProcessor formPlotAreaNode()
//        {
//            NodeProcessor toRet = new NodeProcessor(nPlotArea)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processPlotAreaNode();
//                }
//            };
//
//            return toRet;
//        }

//        private NodeProcessor formAxisNode()
//        {
//            NodeProcessor toRet = new NodeProcessor(nAxis)
//            {
//                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
//                {
//                    parser.processAxisNode();
//                }
//            };
//
//        }

        private NodeProcessor formSpreadsheetNode()
        {
            NodeProcessor toRet = new NodeProcessor(nSpreadSheet)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processSpreadSheetNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formSheetNode()
        {
            NodeProcessor toRet = new NodeProcessor(nTable)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processSheetNode(true);
                }
            };
            return toRet;
        }


        private NodeProcessor formStylePropsNode()
        {
            NodeProcessor toRet = new NodeProcessor(nStyleProps)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processStylePropsNode();
                }
            };
            return toRet;
        }

        private NodeProcessor formStyleNode()
        {
            NodeProcessor toRet = new NodeProcessor(nStyle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processStyleNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formDefaultColumnWidthNode()
        {
            NodeProcessor toRet = new NodeProcessor(defaultColumnWidth)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDefaultColumnWidthNode();
                }
            };
            return toRet;
        }

        
        //// Pivot
        
        private NodeProcessor formDataPilotTableNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDataPilotTable)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataPilotTableNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formSourceCellRangeNode()
        {
            NodeProcessor toRet = new NodeProcessor(nSourceCellRange)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processSourceCellRangeNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formDataPilotFieldNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDataPilotField)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataPilotFieldNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor fromDataPilotFieldReferenceNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDataPilotFieldReference)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataPilotFieldReferenceNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formDataPilotLevelNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDataPilotLevel)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataPilotLevelNode();
                }
            };
            return toRet;
        }
        private NodeProcessor formDataPilotGroupsNode()
        {
        	NodeProcessor toRet = new NodeProcessor(nDataPilotGroups)
        	{
        		@Override
        		void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
        		{
        			parser.processDataPilotGroupsNode();
        		}
        	};
        	return toRet;
        }
        private NodeProcessor formDataPilotGroupNode()
        {
        	NodeProcessor toRet = new NodeProcessor(nDataPilotGroup)
        	{
        		@Override
        		void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
        		{
        			parser.processDataPilotGroupNode();
        		}
        	};
        	return toRet;
        }
        private NodeProcessor formDataPilotGroupMemberNode()
        {
        	NodeProcessor toRet = new NodeProcessor(nDataPilotGroupMember)
        	{
        		@Override
        		void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
        		{
        			parser.processDataPivotGroupMemberNode();
        		}
        	};
        	return toRet;
        }
        
        private NodeProcessor formDataPilotDisplayInfoNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDataPilotDisplayInfo)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataPilotDisplayInfoNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formDataPilotSortInfoNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDataPilotSortInfo)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataPilotSortInfoNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formDataPilotLayoutInfoNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDataPilotLayoutInfo)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataPilotLayoutInfoNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formDataPilotFilterInfoNode()
        {
        	NodeProcessor toRet = new NodeProcessor(nDataPilotFilterInfo)
        	{
        		@Override
        		void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
        		{
        			parser.processDataPilotFilterInfoNode();
        		}
        	};
        	return toRet;
        }
        
        
        private NodeProcessor formDataPilotMemberNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDataPilotMember)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataPilotMemberNode();
                }
            };
            return toRet;
        }
        
        
        private NodeProcessor formDataPilotSubtotalNode()
        {
            NodeProcessor toRet = new NodeProcessor(nDataPilotSubtotal)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processDataPilotSubtotalNode();
                }
            };
            return toRet;
        }
        
  private NodeProcessor formDrawFrameNode() {
        NodeProcessor toRet = new NodeProcessor(nDrawFrame)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processFrameNode();
                }
            };
            return toRet;
    }

    private NodeProcessor formDrawImageNode() {
        NodeProcessor toRet = new NodeProcessor(nDrawImage)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processImageNode();
                }
            };
            return toRet;
    }

    private NodeProcessor formDrawObjectNode() {
        NodeProcessor toRet = new NodeProcessor(N_DRAW_OBJECT)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processDrawObject();
            }
        };
        return toRet;
    }

    private NodeProcessor formSvgTitleNode() {
        NodeProcessor toRet = new NodeProcessor(nSvgTitle)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processSvgTitleNode();
                }
            };
            return toRet;
    }

    private NodeProcessor formSvgDescNode() {
        NodeProcessor toRet = new NodeProcessor(nSvgDesc)
            {
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processSvgDescNode();
                }
            };
            return toRet;
    }

private NodeProcessor formExpressionsNode()
        {
            NodeProcessor toRet = new NodeProcessor(expressions)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processExpressionsNode();
                }
            };
            return toRet;
        }
        
        private NodeProcessor formExpressionNode()
        {
            NodeProcessor toRet = new NodeProcessor(expression)
            {
                @Override
                void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
                {
                    parser.processExpressionNode();
                }
            };
            return toRet;
        }

    private NodeProcessor formSheetImagesNode()
    {
        NodeProcessor toRet = new NodeProcessor(A_IMAGES)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processSheetImagesNode();
            }
        };
        return toRet;
    }

    private NodeProcessor formSheetImageNode()
    {
        NodeProcessor toRet = new NodeProcessor(A_IMAGE)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processSheetImageNode();
            }
        };
        return toRet;
    }

    private NodeProcessor formAnchorNode() {
    	NodeProcessor toRet = new NodeProcessor(nDrawA)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processAnchorNode();
            }
        };
        return toRet;
	}

	private NodeProcessor formGroupNode() {
		NodeProcessor toRet = new NodeProcessor(nDrawG)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processGroupNode();
            }
        };
        return toRet;
	}

    private NodeProcessor formEllipseNode() {
    	NodeProcessor toRet = new NodeProcessor(N_DRAW_ELLIPSE)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processEllipseNode();
            }
        };
        return toRet;
	}

	private NodeProcessor formPolyLineNode() {
    	NodeProcessor toRet = new NodeProcessor(N_DRAW_POLY_LINE)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processPolyLineNode();
            }
        };
        return toRet;
	}

	private NodeProcessor formPathNode() {
		NodeProcessor toRet = new NodeProcessor(N_DRAW_PATH)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processPathNode();
            }
        };
        return toRet;
	}

	private NodeProcessor formLineNode() {
		NodeProcessor toRet = new NodeProcessor(N_DRAW_LINE)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processLineNode();
            }
        };
        return toRet;
	}

	

    private NodeProcessor formCustomShapeNode()
    {
        NodeProcessor toRet = new NodeProcessor(N_DRAW_CUSTOM_SHAPE)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processCustomShapeNode();
            }
        };
        return toRet;
    }
    
    private NodeProcessor formEnhancedGeometryNode()
    {
        NodeProcessor toRet = new NodeProcessor(N_DRAW_ENHANCED_GEOMETRY)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processEnhancedGeometryNode();
            }
        };
        return toRet;
    }
    
    private NodeProcessor formEquationNode()
    {
        NodeProcessor toRet = new NodeProcessor(N_DRAW_EQUATION)
        {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processEquationNode();
            }
        };
        return toRet;
    }
    
    private NodeProcessor formHandleNode() {
        NodeProcessor toRet = new NodeProcessor(N_DRAW_HANDLE) {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processHandleNode();
            }
        };
        return toRet;
    }

    private NodeProcessor formRowGroupNode() {
        NodeProcessor toRet = new NodeProcessor(nTableRowGroup) {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processRowGroupNode();
            }
        };
        return toRet;
    }

    private NodeProcessor formColumnGroupNode() {
        NodeProcessor toRet = new NodeProcessor(nTableColumnGroup) {
            void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException
            {
                parser.processColumnGroupNode();
            }
        };
        return toRet;
    }
    ////    NodeProcessorFactory -> END   ////

}



 class NodeProcessorFactory1 extends NodeProcessorFactory
 {
     void put(XmlName node, NodeProcessor nodeProcessor)
     {
	 mToHandleNodes.put(null, node.prefix+":"+node.lName, nodeProcessor);
     }
 }
/**
 * To process a Node
 */
abstract class NodeProcessor
{
    public final XmlName name;

    NodeProcessor(XmlName name)
    {
	this.name = name;
    }

    NodeProcessor()
    {
	this.name = new XmlName(null, null, null);
    }

    abstract void processNode(ODSWorkbookParser parser) throws IOException, XmlPullParserException;
}
////    NodeProcessor -> END    ////

