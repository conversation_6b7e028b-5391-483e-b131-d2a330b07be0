/* $Id$ */
package com.adventnet.zoho.websheet.model.parser;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.iterator.RowIterator;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.RowUtil;

import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */

public abstract class ClipboardRenderer
{
	public static final Logger LOGGER = Logger.getLogger(ClipboardRenderer.class.getName());
	protected WorkbookContainer container;
	protected int startRow;
	protected int startCol;
	protected int endRow;
	protected int endCol;
	protected Sheet sheet;
	private ImageBook imageBook;
	protected AtomicLong totalImageSize = new AtomicLong();

	/////
	StringBuilder builder;

	public static ClipboardRenderer getInstance(Sheet sheet, int startRow, int startCol, int endRow, int endCol,boolean	isRequiredTabSeparatedValues, boolean isRequiredMarkDownValues,WorkbookContainer container,String version) throws Exception
	{
		ImageBook imageBook = container.getImageBook(version);
		endRow = Math.min(sheet.getUsedRowIndex(), endRow);
		endCol = Math.min(sheet.getUsedColumnIndex(), endCol);
		int visibleCellSize = 0;
		// COPY CAN HAPPEN BEYOND USED INDEX
		if(startRow <= endRow && startCol <= endCol)
		{
			int rowCount = sheet.getVisibleRowCount(startRow, endRow, true, true);
			int colCount = sheet.getVisibleColumnCount(startCol, endCol,true);
		    visibleCellSize = rowCount * colCount;
		}
		//Added this for handhelds devices -- it will be always false for desktop client
		if(isRequiredTabSeparatedValues){
			return new TSVRenderer(container, sheet, startRow, startCol, endRow, endCol,imageBook);
		}else if(isRequiredMarkDownValues){
			return new MarkDownRenderer(container, sheet, startRow, startCol, endRow, endCol,imageBook);
		}

		boolean isHtml = (visibleCellSize <= EngineConstants.HTML_GENERATION_LIMIT_WHILE_COPY);
		if(isHtml)
		{
			return new HTMLRenderer(container, sheet, startRow, startCol, endRow, endCol,imageBook);
		}
		else{
			return new SimpleHTMLRenderer(container, sheet, 0, 0, -1, -1,imageBook);
		}
	}

	protected ClipboardRenderer(WorkbookContainer container, Sheet sheet, int startRow, int startCol, int endRow, int endCol,ImageBook imageBook)
	{
		this.container = container;
		this.startRow = startRow;
		this.startCol = startCol;
		this.endRow = endRow;
		this.endCol = endCol;
		this.sheet = sheet;
		this.imageBook = imageBook;
		this.builder = new StringBuilder();
	}

	public String render(boolean isDownloadOffAndCopyOn)
	{
		if(!isDownloadOffAndCopyOn)
		{
			DataRange dataRange = new DataRange(this.sheet.getAssociatedName(), this.startRow, this.startCol, this.endRow, this.endCol);
			this.generateTableStyleMap(this.sheet, dataRange);
			RowIterator rowIterator = RowIterator.getInstance(sheet, startRow, endRow, RowVisibility.VISIBLE);
			while(rowIterator.hasNext())
			{
				ReadOnlyRow rRow = rowIterator.next();
				int rowIndex = rRow.getRowIndex();
				int rowsRepeated = Math.min(rRow.getRowsRepeated(), endRow - rowIndex + 1);
				Row row = rRow.getRow();
				if(row != null)
				{
					startRow(rRow.getRow());
					int rowHeight = RowUtil.getRowHeight(row);
					int colsRepeated;
					for(int j = startCol; j <= endCol; j += colsRepeated)
					{
						// IMPORTANT :  Do not use the rowIndex or rowsRepeated of the below rCell anywhere.
						// Hence limiting its scope using the braces.
						// Update only cV and colsRepeated inside this block.
						ReadOnlyCell rCell = sheet.getReadOnlyCell(row.getRowIndex(), j);
						int rowSpan = 1, colSpan = 1;
						if(rCell.getCell() != null)
						{
							int[] spans = sheet.getMergeCellSpans(rCell.getCell());
							rowSpan = spans[0];
							colSpan = spans[1];
						}
						colsRepeated = getReadOnlyCell(rCell, rowSpan, colSpan, rowHeight);
						if(rCell.getCell() != null && colsRepeated < colSpan)
						{
							colsRepeated = colSpan;
						}
					}
					endRow(rowIndex >= endRow);
					for(int p = 1; p < rowsRepeated; p++) // rows repeated
					{
						repeatLastRow((rowIndex + p) == endRow);
					}
				}
			}
		}
		//        LOGGER.log(Level.INFO, "Clipboard String: {0}", resultStr);
		return getString();
	}
	public abstract void setHost(String host, boolean isDownloadOff, boolean isRemoteMode);
	public String getString()
	{
		return builder.toString();
	}
	protected ImageBook getImagebook(){
		return  this.imageBook;
	}
	protected abstract int getReadOnlyCell(ReadOnlyCell rCell, int rowSpan, int colSpan,int rowHeight); //It returns number of repeated cols
	protected abstract void startRow(Row row);
	protected abstract void endRow(boolean isLastRowInRange);
	protected abstract void generateTableStyleMap(Sheet sheet, DataRange range);
	public abstract void repeatLastRow(boolean isLastRowInRange);
}
