/*$Id$*/
package com.adventnet.zoho.websheet.model.parser.chart;

import com.adventnet.zoho.websheet.model.parser.Names;
import com.adventnet.zoho.websheet.model.parser.XmlName;
import com.zoho.sas.container.AppResources;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;
import org.xmlpull.v1.util.XmlPullUtil;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class ODSChartParser implements Names {
    private ChartNodeProcessorFactory npFactory = ChartNodeProcessorFactory.npFactory;
    protected XmlPullParser xpp = null;
    public static Logger logger = Logger.getLogger(ODSChartParser.class.getName());
    ChartObject chartObject;
    private boolean isNameSpaceAware = false;
    private boolean stopped = false;
    public static final String DOMAIN_SEPERATOR = ";;;";//No I18N
    private ChartObject.ChartStyle chartStyle = null;
    public ODSChartParser() throws XmlPullParserException {
        XmlPullParserFactory factory = XmlPullParserFactory.newInstance(AppResources.getProperty(XmlPullParserFactory.PROPERTY_NAME), null);
        factory.setNamespaceAware(true);
        xpp = factory.newPullParser();
    }

    void assertStartTag(XmlName node) throws IOException, XmlPullParserException
    {
        xpp.require(XmlPullParser.START_TAG, node.namespace, node.lName);
    }

    void assertEndTag() throws IOException, XmlPullParserException
    {
        xpp.require(XmlPullParser.END_TAG, null, null);
    }

    String getAttribute(XmlName node)
    {
        return xpp.getAttributeValue(node.namespace, node.lName);
    }

    void traverseNode(XmlName nodeName) throws IOException, XmlPullParserException
    {
        assertStartTag(nodeName);
        while (!stopped)
        {
            int eventType = xpp.next();
            if (eventType == XmlPullParser.END_TAG)
            {
                if(!isNameSpaceAware && (nodeName.prefix+":"+nodeName.lName).equals(xpp.getName()))
                {
                    break;
                }
                else if (nodeName.lName.equals(xpp.getName()) && nodeName.prefix.equals(xpp.getPrefix()))
                {
                    break;
                }
            }
            else if (eventType == XmlPullParser.START_TAG)
            {
                ChartNodeProcessorFactory.NodeProcessor node = npFactory.getInstance(xpp.getPrefix(), xpp.getName());
                node.processNode(this);
            }
        }// ~while

        if(!stopped) {
            assertEndTag();
        }
    }
    public void process_chart_chartNode() throws XmlPullParserException, IOException{
        XmlName node =CHART_CHART;
        assertStartTag(node);
        chartObject.chartClass = getAttribute(CHART_CLASS);
        chartObject.style_name = getAttribute(CHART_STYLE_NAME);
        traverseNode(node);
    }

    private boolean isChartTile = false;
    public void process_chart_title() throws XmlPullParserException, IOException{
        XmlName node =CHART_TITLE;
        assertStartTag(node);
        isChartTile = true;
        traverseNode(node);
        isChartTile = false;
    }
    public void process_chart_title_text() throws XmlPullParserException, IOException{
        XmlName node =nTextP;
        assertStartTag(node);
        if(isChartTile){
            if(xpp.next()==XmlPullParser.TEXT){
                chartObject.title=xpp.getText();
                xpp.next();
            }
        }
//        traverseNode(nTextP);
    }

    public void process_chart_legend() throws XmlPullParserException, IOException{
        XmlName node =CHART_LEGEND;
        assertStartTag(node);
    }

    public void process_chart_plotArea() throws XmlPullParserException, IOException{
        XmlName node =CHART_PLOT_AREA;
        assertStartTag(node);
        chartObject.plotArea_styleName = getAttribute(CHART_STYLE_NAME);
        traverseNode(node);
    }

    public void process_chart_axis() throws XmlPullParserException, IOException{
        XmlName node =CHART_AXIS;
        assertStartTag(node);
        List<ChartObject.Axis> axisList=chartObject.axisList;
        axisList.add(new ChartObject.Axis());
        axisList.get(axisList.size()-1).dimension=getAttribute(CHART_DIMENSION);
        axisList.get(axisList.size()-1).name=getAttribute(CHART_NAME);
        axisList.get(axisList.size()-1).style_name=getAttribute(CHART_STYLE_NAME);
        traverseNode(node);
    }

    public void process_chart_categories() throws XmlPullParserException, IOException{
        XmlName node =CHART_CATEGORIES;
        assertStartTag(node);
        List<ChartObject.Axis> axisList=chartObject.axisList;
        axisList.get(axisList.size()-1).categories_cellRangeAddress=getAttribute(aCellRangeAddress);
    }

    public void process_chart_grid() throws XmlPullParserException, IOException{
        XmlName node =CHART_GRID;
        assertStartTag(node);
    }

    public void process_chart_series() throws XmlPullParserException, IOException{
        XmlName node =CHART_SERIES;
        assertStartTag(node);
        List<ChartObject.Series> seriesList=chartObject.seriesList;
        ChartObject.Series series=new ChartObject.Series();
        series.labelCellAddress=getAttribute(CHART_LABEL_CELL_ADDRESS);
        series.valuesCellRangeAddress=getAttribute(CHART_VALUES_CELL_RANGE_ADDRESS);
        series.chart_class=getAttribute(CHART_CLASS);
        series.attachedAxis=getAttribute(CHART_ATTACHED_AXIS);
        series.style_name=getAttribute(CHART_STYLE_NAME);
        if( series.labelCellAddress != null || series.valuesCellRangeAddress != null) {
            seriesList.add(series);
        }

        traverseNode(node);
    }
    public void process_chart_domain() throws XmlPullParserException, IOException{
        XmlName node=CHART_DOMAIN;
        assertStartTag(node);

        List<ChartObject.Series> seriesList=chartObject.seriesList;
        if(!seriesList.isEmpty()){
            String aCRA = getAttribute(aCellRangeAddress);
            if(aCRA!=null){
                seriesList.get(seriesList.size()-1).valuesCellRangeAddress+=DOMAIN_SEPERATOR+aCRA;
            }
        }
    }

    public void process_chart_dataPoint() throws XmlPullParserException, IOException{
        XmlName node =CHART_DATA_POINT;
        assertStartTag(node);
    }

    public Map<String, ChartObject> parseCharts(byte[] contentBytes, Set<String> chartHrefs){
        Map<String, ChartObject> map = new HashMap<>();
        try(InputStream inputStream = new ByteArrayInputStream(contentBytes)) {
            try(ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
                for(ZipEntry zipEntry=zipInputStream.getNextEntry(); zipEntry!=null; zipEntry=zipInputStream.getNextEntry()){
                    if(chartHrefs.contains(zipEntry.getName())){
                        try
                        {
                            this.chartObject=new ChartObject();
                            xpp.setInput(zipInputStream, "UTF-8");//No I18N
                            xpp.next();// go to the start_tag of the root-node 'office:document-content'
                            ChartNodeProcessorFactory.NodeProcessor node = npFactory.getInstance(xpp.getPrefix(), xpp.getName());
                            node.processNode(this);
                            map.put(zipEntry.getName(), chartObject);
                        }
                        catch(Exception e)
                        {
                            logger.log(Level.WARNING,"Engine: Exception while parsing File from parseStream() method.",e);
                            throw (new Exception("Document.Parse.Error", e));
                        }
                    }
                }
            }catch(Exception e){
                logger.log(Level.OFF, "", e);
            }
        }catch(IOException e){
            logger.log(Level.OFF, "", e);
        }
        return map;
    }

    public void process_chart_properties() throws XmlPullParserException, IOException{
        XmlName node =CHART_PROPERTIES;
        assertStartTag(node);
        if(chartStyle!=null){
            chartStyle.chart_properties.put(CHART_VERTICAL, getAttribute(CHART_VERTICAL));
            chartStyle.chart_properties.put(CHART_STACKED, getAttribute(CHART_STACKED));
            chartStyle.chart_properties.put(CHART_PERCENTAGE, getAttribute(CHART_PERCENTAGE));
            chartStyle.chart_properties.put(CHART_3D, getAttribute(CHART_3D));
            chartStyle.chart_properties.put(SYMBOL_TYPE, getAttribute(SYMBOL_TYPE));
            chartStyle.chart_properties.put(CHART_INTERPOLATION, getAttribute(CHART_INTERPOLATION));
        }
    }

    public void process_graphic_properties() throws XmlPullParserException, IOException{
        XmlName node =nGraphicProps;
        assertStartTag(node);
        if(chartStyle!=null){
            chartStyle.graphic_properties.put(aStroke, getAttribute(aStroke));
        }
    }

    public void process_style()throws XmlPullParserException, IOException{
        XmlName node = nStyle;
        assertStartTag(node);
        String name=getAttribute(aStyleName);
        chartStyle= null;
        if(name != null){
            chartStyle=new ChartObject.ChartStyle();
            chartObject.styles.put(name, chartStyle);
        }

    }

    public void process_AutomaticStyles()throws XmlPullParserException, IOException{
        XmlName node = nAutomaticStyles;
        assertStartTag(node);
        traverseNode(node);
    }

    public void process_office_document_content()throws XmlPullParserException, IOException{
        XmlName node = nOfficeDoc;
        assertStartTag(node);
        traverseNode(node);
    }

    public void process_office_chart()throws XmlPullParserException, IOException{
        XmlName node =OFFICE_CHART;
        assertStartTag(node);
        traverseNode(node);
    }

    public void process_chart_officeBody()throws XmlPullParserException, IOException{
        XmlName node = nBody;
        assertStartTag(node);
        traverseNode(node);
    }
}
