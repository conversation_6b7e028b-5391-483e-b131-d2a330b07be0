/*$Id$*/
package com.adventnet.zoho.websheet.model.parser.chart;

import com.adventnet.zoho.websheet.model.parser.Names;
import com.adventnet.zoho.websheet.model.parser.XmlName;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.util.XmlPullUtil;

import java.io.IOException;

class ChartNodeProcessorFactory implements Names {
    static ChartNodeProcessorFactory npFactory = new ChartNodeProcessorFactory();
    NodeProcessor getInstance(String prefix, String lName)
    {
        // do null check for args...
        if (mToHandleNodes.containsKey(prefix, lName))
        {
            return (NodeProcessor) mToHandleNodes.get(prefix, lName);
        }
        return skipImpl;
    }

    MultiKeyMap mToHandleNodes = new MultiKeyMap();
    {
        put(CHART_CHART, formchart_chartNode());
        put(CHART_TITLE, formchart_titleNode());
        put(nTextP, formchart_textNode());
        put(CHART_LEGEND, formchart_legendNode());
        put(CHART_PLOT_AREA, formchart_plotAreaNode());
        put(CHART_AXIS, formchart_axisNode());
        put(CHART_CATEGORIES, formchart_categoriesNode());
        put(CHART_GRID, formchart_gridNode());
        put(CHART_SERIES, formchart_seriesNode());
        put(CHART_DOMAIN, formchart_domainNode());
        put(CHART_DATA_POINT, formchart_dataPointNode());

        put(nOfficeDoc, form_office_document_contentNode());
        put(nAutomaticStyles, form_AutomaticStylesNode());
        put(nStyle, form_styleNode());
        put(CHART_PROPERTIES, form_chartPropertiesNode());
        put(nGraphicProps, form_graphicPropertiesNode());
        put(nBody, form_OfficeBodyNode());
        put(OFFICE_CHART, form_OfficeChartNode());

    }

    private NodeProcessor form_OfficeChartNode(){
        NodeProcessor toRet = new NodeProcessor(OFFICE_CHART)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_office_chart();
            }
        };
        return toRet;
    }

    private NodeProcessor form_OfficeBodyNode(){
        NodeProcessor toRet = new NodeProcessor(nBody)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_officeBody();
            }
        };
        return toRet;
    }

    private NodeProcessor form_chartPropertiesNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_PROPERTIES)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_properties();
            }
        };
        return toRet;
    }
    private NodeProcessor form_graphicPropertiesNode(){
        NodeProcessor toRet = new NodeProcessor(nGraphicProps)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_graphic_properties();
            }
        };
        return toRet;
    }

    private NodeProcessor form_styleNode(){
        NodeProcessor toRet = new NodeProcessor(nStyle)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_style();
            }
        };
        return toRet;
    }

    private NodeProcessor form_AutomaticStylesNode(){
        NodeProcessor toRet = new NodeProcessor(nAutomaticStyles)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_AutomaticStyles();
            }
        };
        return toRet;
    }

    private NodeProcessor form_office_document_contentNode(){
        NodeProcessor toRet = new NodeProcessor(nOfficeDoc)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_office_document_content();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_dataPointNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_DATA_POINT)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_dataPoint();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_seriesNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_SERIES)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_series();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_domainNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_DOMAIN)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_domain();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_gridNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_GRID)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_grid();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_categoriesNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_CATEGORIES)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_categories();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_axisNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_AXIS)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_axis();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_plotAreaNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_PLOT_AREA)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_plotArea();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_legendNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_LEGEND)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_legend();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_titleNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_TITLE)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_title();
            }
        };
        return toRet;
    }
    private NodeProcessor formchart_textNode(){
        NodeProcessor toRet = new NodeProcessor(nTextP)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_title_text();
            }
        };
        return toRet;
    }

    private NodeProcessor formchart_chartNode(){
        NodeProcessor toRet = new NodeProcessor(CHART_CHART)
        {
            void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
            {
                parser.process_chart_chartNode();
            }
        };
        return toRet;
    }
    void put(XmlName node, NodeProcessor nodeProcessor)
    {
        mToHandleNodes.put(node.prefix, node.lName, nodeProcessor);
    }

    private NodeProcessor skipImpl = new NodeProcessor()
    {
        public void processNode(ODSChartParser parser) throws IOException, XmlPullParserException
        {
            XmlPullUtil.skipSubTree(parser.xpp);
        }
    };

    abstract class NodeProcessor
    {
        public final XmlName name;

        NodeProcessor(XmlName name)
        {
            this.name = name;
        }

        NodeProcessor()
        {
            this.name = new XmlName(null, null, null);
        }

        abstract void processNode(ODSChartParser parser) throws IOException, XmlPullParserException;
    }
}
