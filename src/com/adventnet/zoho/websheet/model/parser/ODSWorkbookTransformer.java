/* $Id$ */
/*
 * ODSEventListenerImpl.java
 *
 * Created on May 7, 2007, 4:29 PM
 */


package com.adventnet.zoho.websheet.model.parser;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.CellReference.ReferenceMode;
import com.adventnet.zoho.websheet.model.SparklinesGroup.SparklineProperties;
import com.adventnet.zoho.websheet.model.exception.AbortParseError;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.filter.DefaultFilterView;
import com.adventnet.zoho.websheet.model.filter.Filter;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.shapes.*;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.DefaultPatternUtil;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.*;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.*;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;



/**
 *
 * <AUTHOR>
 */
public class ODSWorkbookTransformer implements ODSEventListener
{
    public static final Logger LOGGER = Logger.getLogger(ODSWorkbookTransformer.class.getName());
    private Workbook workbook = null;
    //private int tempRowHeight = 0;

    private int sheetIndex = 0;
    private int rowIndex = 0;
    private int colIndex = 0;

    private int colHeaderIndex = 0;

    // To avoid the last empty rows
    private boolean isRowWithEmptyCell = true;
    private int rowHasDataIndex = 0;

    private int colHasDataIndex = 0;

    private Map<Cell, int[]> mergeCellDetails = new HashMap<>();
    private List<Cell> hyperLinkCells = new LinkedList<Cell>();
    private List<Cell> richTextLinkCells = new LinkedList<Cell>();
    private Map<Sheet,List<TempSparklinesGroup>> sparklinesMap = new HashMap<>();

    private List<Cell> autoArrayParentCells= new ArrayList<>();
//    private List<Cell> mergedCells = new ArrayList<Cell>();

    private final Map<String, ContentValidationTemp> contentValidationMap = new HashMap<>();
    private final Map<Sheet, List<ColumnHeader>> conditionalStyleColumnsMap = new HashMap<>();    

    private Map<String, NumberStyle> numberStyleMap = new HashMap();
    private final Map<Integer,PicklistTemp> picklistTempMap = new HashMap<>();

    private boolean isUpdateConditionStyle = false;
    Map<String, List<MapStyleTempOld>> mapStyleTempOldMap = new HashMap();
    private final Map<Sheet, List<Cell>> conditionalcellStyleMap = new HashMap();
    private final com.google.common.collect.Table<Sheet, String, Collection<RangeUtil.SheetRange>> sheetToCVToRanges = HashBasedTable.create();
    
    // Cellstyle Name to DataStyleName map
    private Map<String, String> dataStyleNameToCellStyleMap = new HashMap<>();
    
    private int currentPriority = 0;
    private boolean isCFInReverseOrder = false;
    private List<DataRange> currentCFTargetRanges = new ArrayList();
    private final Map<String, List<DataRange>> existingCFRangeMap = new HashMap();
    private List<ConditionalFormatEntryTemp> conditionalStyleList = new ArrayList();
    Map<Sheet, Map<Integer, ConditionalStyleTemp>> conditionalStyleMapForSheets = new HashMap();
    
    private final com.google.common.collect.Table<Sheet, String, String> sheetExpressionNameToExpressionStringMap= HashBasedTable.create();
    private final com.google.common.collect.Table<Sheet, String, Collection<Cell>> sheetExpressionNameRefferredCells= HashBasedTable.create();

    private final Map<Cell, String> cellToFormulaString = new HashMap();
    
    private BitSet filteredRowsIndexes = new BitSet();
    
    private ShapeAnchor anchor;
    List<ShapeGroup> groups = new ArrayList<>();
    private Shape currentShape;
    private Stack<Integer> startIndicesOfGroups = new Stack<>();
//    private ODSRendererListener listener = null;

    /** Creates a new instance of ODSEventListenerImpl */
    public ODSWorkbookTransformer()
    {
    }

    @Override
    public void constructWorkbook(String workbookName)
    {
	if(this.workbook == null)
	{
	    this.workbook = new Workbook();
	}
        constructWorkbook(workbook,  workbookName);
    }

    public void constructWorkbook(Workbook workbook, String workbookName)
    {
	this.workbook = workbook;
    }

    @Override
    public void endWorkbook()
    {   
        // Calling sheet.getAssociatedName() so that asn will get generated if its not in xml.
        // We need to do this. otherwise, In some scenario different asn get generated for sheets when workbook is loaded in two different servers leading to wrong data display in client.
        for(Sheet sheet : this.workbook.getSheetList())
        {
            sheet.getAssociatedName();
        }
        //////////////////////////////////////////
        
        for(String validationName : contentValidationMap.keySet())
        {
            try{
                ContentValidationTemp contentValidationTemp = contentValidationMap.get(validationName);
                ContentValidation contentValidation = new ContentValidation(workbook,contentValidationTemp.condition, contentValidationTemp.baseCellAddress);
                contentValidation.setName(validationName);
                contentValidation.setIsAllowEmptyCell(contentValidationTemp.isAllowEmptyCell);
                if(contentValidationTemp.displayList != null)
                {
                    contentValidation.setDisplayList(contentValidationTemp.displayList.equalsIgnoreCase("SORT-ASCENDING") ? ContentValidation.LIST_DISPLAY_TYPE.SORT_ASCENDING : ContentValidation.LIST_DISPLAY_TYPE.valueOf(contentValidationTemp.displayList.toUpperCase())); 
                }
                contentValidation.setHelpMessage(contentValidationTemp.helpMessage);
                contentValidation.setErrorMessage(contentValidationTemp.errorMessage);
                workbook.addContentValidation_Parser(contentValidation);
            }
            catch(IllegalArgumentException e){}// Do nothing
        }
        
        for(Cell cell : this.hyperLinkCells)
        {
            String[] links = EngineUtils1.splitCellLink(cell.getLink());
            if(links.length > 1)
            {
                String sheetName = links[0];
                Sheet sheet = workbook.getSheet(sheetName);
                if(sheet != null)
                {
                    cell.setLink(EngineUtils1.getCellLinkForParser(sheet, cell.getLink()));
                }
            }
        }

        for(Cell cell : this.richTextLinkCells)
        {
            ZSString zsString = ((ZSString)cell.getValue().getRawValue());
            String baseStringValue = zsString.getBaseStringValue();
            List<RichStringProperties> newRichStringProperties = new ArrayList<>();
            for(RichStringProperties richStringProperties : zsString.getProperties())
            {
                String url = richStringProperties.getUrl();
                if(url != null)
                {
                    String[] links = EngineUtils1.splitCellLink(url);
                    if(links.length > 1)
                    {
                        String sheetName = links[0];
                        Sheet sheet = workbook.getSheet(sheetName);
                        if(sheet != null)
                        {
                            String newLink = EngineUtils1.getCellLinkForParser(sheet, richStringProperties.getUrl());
                            newRichStringProperties.add(new RichStringProperties(richStringProperties.getStyleName(), newLink, richStringProperties.getLabel(), richStringProperties.getStartIndex()));
                        }
                    }
                }
            }
            ZSString richString = new ZSString(baseStringValue, false, newRichStringProperties);
            Value value = Value.getInstance(Type.STRING, richString);
            ((CellImpl)cell).setValueFromParser(value);
        }

        sheetToCVToRanges.rowMap().entrySet().forEach(entry -> {
            Sheet sheet = entry.getKey();
            Map<String, Collection<RangeUtil.SheetRange>> cvNameToSheetRanges = entry.getValue();
            Map<String, Collection<DataRange>> cvNameToMergedRanges = cvNameToSheetRanges.entrySet().stream()
                    .collect(
                            Collectors.toMap(e -> e.getKey(),e -> {
                                    Collection<RangeUtil.SheetRange> sheetRanges = e.getValue();
                                    List<DataRange> ranges = new RangeUtil.MergeCells(sheet, sheetRanges).toDataRanges();
                                    return ranges;
                                }
                            )
                    );
            sheet.addContentValidationRangesFromParser(cvNameToMergedRanges);
        });
        
        Map<String, List<MapStyle>> msMap = new HashMap();
        for(String styleName : mapStyleTempOldMap.keySet())
        {
            List<MapStyleTempOld> msTempList = mapStyleTempOldMap.get(styleName);
            if (msTempList != null) 
            {
                if(!isUpdateConditionStyle || (msTempList.size() > 0 && (msTempList.get(0).style instanceof NumberStyle)))
                {
                    for (MapStyleTempOld msTemp : msTempList) 
                    {
                        msTemp.createAndAddMapStyleToStyle(msMap);
                    } 
                }
            }
        }

        convertNumberStylesToPattern();

        if(!isUpdateConditionStyle)
        {            
            for(Sheet sheet : conditionalcellStyleMap.keySet())
            {
                List<Cell> conditionalCellStyleList = conditionalcellStyleMap.get(sheet);
                sheet.addConditionalStyleRangesFromParser(msMap, conditionalCellStyleList);
            }

            for(Sheet sheet : conditionalStyleColumnsMap.keySet())
            {
                List<ColumnHeader> conditionalStyleColumnList = conditionalStyleColumnsMap.get(sheet);
                for(ColumnHeader ch : conditionalStyleColumnList)
                {
                    CellStyle temp = workbook.getCellStyle(ch.getDefaultCellStyleName());
                    List<MapStyle> mapStyles = msMap.get(temp.getStyleName());
                    if(mapStyles != null && !mapStyles.isEmpty())
                    {
                        int startCol = ch.getColumn().getColumnIndex();
                        int endCol = startCol + ch.getColsRepeated() - 1;
                        if(endCol >= Utility.MAXNUMOFCOLS)
                        {
                            endCol = Utility.MAXNUMOFCOLS - 1;
                        }
                        DataRange range = new DataRange(sheet.getAssociatedName(), 0, startCol, Utility.MAXNUMOFROWS - 1, endCol);
                        List<DataRange> ranges = new ArrayList<>();
                        ranges.add(range);
                        for(MapStyle ms : mapStyles)
                        {
                            sheet.addMapStyleToFormatMap(ranges, ms);
                        }
                    }
                }
            }
        }       
        

        else{
            for(Entry<Sheet, Map<Integer, ConditionalStyleTemp>> entry : conditionalStyleMapForSheets.entrySet())
            {
                Sheet sheet = entry.getKey();
                Map<Integer, ConditionalStyleTemp> csTempMap = entry.getValue();

                List<ConditionalStyleTemp> csTempsDuplicatesRemovedAndHighToLowPrio = new ArrayList();
                Set<String> addedClassicCSTempStrs = new HashSet();
                csTempMap.entrySet()
                    .stream()
                    .sorted(Comparator.comparingInt(
                        //isCFInReverseOrder, will be true on imported files alone. Because need to reverse the order of the rules to show it in the way, the libo file is.
                        //Since can't predict the number of rules, priority starts with '0'. And will get reversed in case of imported files.
                        entryTemp -> isCFInReverseOrder ? entryTemp.getKey() : -entryTemp.getKey()
                    ))
                    .forEachOrdered(entryTemp -> {
                        ConditionalStyleTemp csTempObj = entryTemp.getValue();
                        if(csTempObj.condition == null || csTempObj.baseCellAddress == null || csTempObj.applyStyleName == null) {
                            csTempsDuplicatesRemovedAndHighToLowPrio.add(csTempObj);
                        } else {
                            // if multiple classic styles
                            String csTempStr = csTempObj.condition + "  |||  " + csTempObj.baseCellAddress + "  |||  " + csTempObj.ranges;
                            if (!addedClassicCSTempStrs.contains(csTempStr)) {
                                csTempsDuplicatesRemovedAndHighToLowPrio.add(csTempObj);
                                addedClassicCSTempStrs.add(csTempStr);
                            }
                        }
                    });

                List<ConditionalStyleTemp> csTempsDuplicatesRemovedAndLowToHighPrio = Lists.reverse(csTempsDuplicatesRemovedAndHighToLowPrio);

                for(ConditionalStyleTemp conditionalStyleTemp: csTempsDuplicatesRemovedAndLowToHighPrio) {
                    try{
                        conditionalStyleTemp.createConditionalStyle(sheet);
                    }catch(Exception e)
                    {
                        LOGGER.log(Level.INFO, "ERROR : CF ", e);
                    }
                }
            }
        }

        Collection<String> intersectingCellStyleNames = new ArrayList<>(Sets.intersection(this.workbook.getAdhocCellStyleNames(), this.workbook.getStableCellStyleNames()));
        if(!intersectingCellStyleNames.isEmpty()) {
            LOGGER.log(Level.INFO, "DUPLICATE CELL STYLES FOUND BETWEEN NAMED AND UNNAMED: {0}", intersectingCellStyleNames);
        }
        for (String intersectingCellStyleName: intersectingCellStyleNames) {
            this.workbook.removeCellStyle(intersectingCellStyleName);
        }
        
        // Updating mergecell details.
        for(Cell cell : mergeCellDetails.keySet())
        {
            int spans[] = mergeCellDetails.get(cell);
            cell.getRow().getSheet().addMergeCellDetails(cell, spans[0], spans[1]);
        }
        
        createAndSetExpressionForReferredCells();
        createAndSetExpressionForCellsWithODSFormula();

        for(Map.Entry<Integer,PicklistTemp> entry : this.picklistTempMap.entrySet()) {
            Map<String,List<DataRange>> rangeMap = new HashMap<>();
            PicklistTemp tempPicklist = entry.getValue();

            for(String rangeString : tempPicklist.rangeStrings.split(";")) {
                try {
                    if(!rangeString.isEmpty()) {
                        DataRange range = new Range(workbook, rangeString, null, ReferenceMode.A1, true).toDataRange();

                        List<DataRange> rangeList = rangeMap.computeIfAbsent(range.getAssociatedSheetName(), k -> new ArrayList<>());
                        rangeList.add(range);
                    }
                }
                catch (SheetEngineException e) {
                    LOGGER.log(Level.WARNING,"[PICKLIST] Error while create Picklist from XML >> ",e);
                }
            }

            if(tempPicklist.sourceRangeString != null) {
                for(String rangeString : tempPicklist.sourceRangeString.split(";")) {
                    if(!rangeString.isEmpty()) {
                        try {
                            DataRange range = new Range(workbook, rangeString, null, ReferenceMode.A1, true).toDataRange();
                            Picklist picklist = workbook.getPicklist(tempPicklist.picklistID);
                            picklist.addSourceRange(range);
                        }
                        catch(SheetEngineException e) {
                            LOGGER.log(Level.WARNING,"[PICKLIST] Error while create Picklist source from XML >> ",e);
                        }
                    }
                }
            }

            for(Map.Entry<String,List<DataRange>> entry2 : rangeMap.entrySet()) {
                Sheet sheet = workbook.getSheetByAssociatedName(entry2.getKey());
                sheet.addPicklistRanges(tempPicklist.picklistID,entry2.getValue());
            }

        }

        for(Entry<Sheet,List<TempSparklinesGroup>> entry : sparklinesMap.entrySet()) {
            List<TempSparklinesGroup> tempSparklinesGroupList = entry.getValue();
            for (TempSparklinesGroup tempGroup : tempSparklinesGroupList) {
                int size = tempGroup.destinationList.size();
                List<SparklinesGroup.Sparkline> sparklinesList = new ArrayList();
                for (int i = 0; i < size; i++) {
                    try {
                        RangeUtil.SheetRange sheetRange = RangeUtil.getSheetRange(tempGroup.destinationList.get(i));
                        DataRange destinationRange = new DataRange(entry.getKey().getAssociatedName(),sheetRange.getStartRowIndex(),sheetRange.getStartColIndex(),sheetRange.getEndRowIndex(),sheetRange.getEndColIndex());
                        //Range destinationRange = new Range(workbook,tempGroup.destinationList.get(i),null,CellReference.ReferenceMode.A1,true);
                        SparklinesGroup.SparklineOrientation sourceOrientation = SparklinesGroup.SparklineOrientation.valueOf(tempGroup.orientationList.get(i));
                        SparklinesGroup.SparklineOrientation destinationOrientation = ActionUtil.getDestinationOrientation(destinationRange);

                        /////
                        /* Sheet name should be written for source expression string only if source sheet and destination sheet are different.
                        *  But for sparklines created before this checkin, sheet name is written even for same source and dest sheets.
                        *  This code is to remove the sheet name if it is present */
                        String sourceString = tempGroup.sourceList.get(i);
                        String sourceSheetString = null;
                        int dotIndex = sourceString.lastIndexOf('.');
                        if(dotIndex != -1) {
                            sourceSheetString = sourceString.substring(0, dotIndex);
                        }
                        if(sourceSheetString!= null && !sourceSheetString.isEmpty()) {
                            sourceSheetString = sourceSheetString.substring(1);
                            if(sourceSheetString.charAt(0) == '\'') {
                                /* Sheet name has space so is enclosed with single quotes. So quotes should be removed */
                                sourceSheetString = sourceSheetString.substring(1,sourceSheetString.length()-1);
                            }
                            Sheet sourceSheet = workbook.getSheet(sourceSheetString);

                            if (sourceSheet != null && sourceSheet.equals(entry.getKey())) {
                                sourceString = sourceString.substring(sourceString.lastIndexOf('.')+1);
                            }
                        }
                        ////

                        Expression sourceExpression = new ExpressionImpl(workbook, sourceString, 0, 0, false, ReferenceMode.R1C1);
                        SparklinesGroup.Sparkline sparklines = new SparklinesGroup.Sparkline(sourceExpression, destinationRange, sourceOrientation, destinationOrientation);
                        sparklinesList.add(sparklines);
                    } catch (Exception e) {
                        LOGGER.log(Level.INFO, "Error while creating SparklinesGroups from XML >> ", e);
                    }
                }
                if(!sparklinesList.isEmpty()) {
                    SparklinesGroup sparklinesGroup = new SparklinesGroup(sparklinesList, tempGroup.property, tempGroup.minValue, tempGroup.maxValue, entry.getKey().getNewSparklinesGroupID());
                    entry.getKey().addSparklinesGroup(sparklinesGroup);
                }
            }
        }

        if(workbook.getDefaultTableStyle() == null) {
            workbook.setDefaultTableStyle(TableUtil.getDefaultTableStyle());
        }



        repairLostStyles();
    }

    private void repairLostStyles() {
        /*
        // Avoiding cellStyle repair anticipating heavy Set operations
        // Will uncomment if needed
        Set<String> definedCellStyleNames = workbook.getCellStyleMap().keySet();
        Collection<String> undefinedCellStyleNames = new ArrayList<>();
        */
        Set<String> definedRowStyleNames = workbook.getRowStyleMap().keySet();
        Collection<String> undefinedRowStyleNames = new ArrayList<>();
        Set<String> definedColStyleNames = workbook.getColumnStyleMap().keySet();
        Collection<String> undefinedColStyleNames = new ArrayList<>();
        for(Sheet sheet : workbook.getSheetList()) {
            /*
            Collection<String> undefinedCellStyleNamesTemp = Sets.difference(sheet.getUsedCellStyleNameList(), definedCellStyleNames);
            undefinedCellStyleNames.addAll(undefinedCellStyleNamesTemp);
            */
            Collection<String> undefinedRowStyleNamesTemp = Sets.difference(sheet.getUsedRowStyleNameList(), definedRowStyleNames);
            undefinedRowStyleNames.addAll(undefinedRowStyleNamesTemp);
            Collection<String> undefinedColStyleNamesTemp = Sets.difference(sheet.getUsedColumnStyleNameList(), definedColStyleNames);
            undefinedColStyleNames.addAll(undefinedColStyleNamesTemp);
        }

        /*
        LOGGER.log(Level.INFO, "REPAIRING UNDEFINED CELL STYLE NAMES IN WORKBOOK: {0}", undefinedCellStyleNames);
        if(!undefinedCellStyleNames.isEmpty()) {
            LOGGER.log(Level.INFO, "REPAIRING UNDEFINED ROW STYLE NAMES IN WORKBOOK: {0}", undefinedRowStyleNames);
        }
        for(String undefinedCellStyleName: undefinedCellStyleNames) {
            CellStyle cellStyle = new CellStyle(null, null, null);
            cellStyle.setStyleName(undefinedCellStyleName);
            cellStyle.setParenStyleName(EngineConstants.DEFAULT_CELLSTYLENAME);
            workbook.addCellStyle(cellStyle);
        }
        */

        if(!undefinedRowStyleNames.isEmpty()) {
            LOGGER.log(Level.INFO, "REPAIRING UNDEFINED ROW STYLE NAMES IN WORKBOOK: {0}", undefinedRowStyleNames);
        }
        for(String undefinedRowStyleName: undefinedRowStyleNames) {
            RowStyle rowStyle = this.workbook.createDefaultRowStyle();
            rowStyle.setStyleName(undefinedRowStyleName);
            workbook.addRowStyle(rowStyle);
        }

        if(!undefinedColStyleNames.isEmpty()) {
            LOGGER.log(Level.INFO, "REPAIRING UNDEFINED COLUMN STYLE NAMES IN WORKBOOK: {0}", undefinedColStyleNames);
        }
        for(String undefinedColStyleName: undefinedColStyleNames) {
            ColumnStyle columnStyle = workbook.createDefaultColumnStyle();
            columnStyle.setStyleName(undefinedColStyleName);
            workbook.addColumnStyle(columnStyle);
        }
    }
    
    @Override
    public void startSheet(Sheet sheet)
    {
        String[] sheetNames = workbook.getSheetNames(true);
        if(Arrays.asList(sheetNames).contains(sheet.getName())) {
            throw new IllegalStateException("Duplicate Sheets attempted to add: " + sheet.getName()); //No I18N
        }
        if (sheetNames.length > Utility.MAXNUMOFSHEETS) {
            throw new RuntimeException("number of sheets limit exceeded");//No I18N
        }
        //logger.info("in start sheet : "+sheet.getName());
        sheet.setWorkbook(workbook);
        //sheet.setSheetIndex(sheetIndex);
        workbook.addSheetFromParser(sheet);
        existingCFRangeMap.clear();

        // Reset merged cells list for new sheet.
//        mergedCells = new ArrayList<Cell>();
    }

    private void updateCustomShapeRange(Sheet sheet, ShapeGroup shapeGroup)
    {
        try
        {
            if(shapeGroup.getShape() instanceof CustomShape)
            {
                CustomShape customShape = (CustomShape) shapeGroup.getShape();
                customShape.initEndCellRange(workbook);
                for(ShapeGroup shapeGroup1 : shapeGroup.getGroups())
                {
                    updateCustomShapeRange(sheet, shapeGroup1);
                }
            }
        }
        catch(Exception e)
        {
            LOGGER.log(Level.WARNING, "[SHAPES] Problem updating endCellAddress in the CustomShape.",e);
        }
    }


    @Override
    public void endSheet(Sheet sheet)
    {
        // if any of the columnHeaders have some cellStyle other than Default one then
        // if the no. of rows in this sheet is equal to MAXNUMOFROWS then the cellStyle is applicanle to the entire row
        // else the cellStyle is applicable only for a range.
        // should not check not equal because our maxRows is 65536 while of open office is 1048576.
        // so in an imported file if a style is applied at column level, row Num will be 1048576.
        if(sheet.getRowNum() < Utility.MAXNUMOFROWS)
        {
            int size = sheet.getColNum();
            for(int cIndex = 0; cIndex < size; cIndex++)
            {
                ColumnHeader columnHeader = sheet.getColumnHeaderReadOnly(cIndex);
                if(columnHeader != null && !columnHeader.getDefaultCellStyleName().equals("Default"))
                {
                    this.transferCellStyleFromColumnToRange(columnHeader, sheet);
                }
            }
        }

        for(Cell cell : autoArrayParentCells){
            ((CellImpl)cell).updateAutoArrayParentCellFromParser();
        }

        for(ShapeGroup shapeGroup : sheet.getShapeGroups())
        {
            updateCustomShapeRange(sheet, shapeGroup);
        }

        sheetIndex++;
        rowIndex = 0;
        colIndex = 0;
        colHeaderIndex = 0;
	//tempRowHeight = 0;
        autoArrayParentCells.clear();

        ////
        //Set the value in Sheet of the used row index
        sheet.setUsedRowIndex(rowHasDataIndex);
        rowHasDataIndex = 0;
        isRowWithEmptyCell = true;

	sheet.setUsedColumnIndex(colHasDataIndex);
        colHasDataIndex = 0;

	// set the sheet modified to false
	sheet.setIsModified(false);       
    }


    // This is to tranfer the cellStyle from the columnHeader to the range of cells to which the cellStyle is actually specified
    // TODO : may change this approach later.
    private void transferCellStyleFromColumnToRange(ColumnHeader columnHeader, Sheet sheet)
    {
	Column column = columnHeader.getColumn();
        String cellStyleName = columnHeader.getDefaultCellStyleName();

        boolean isConditionalCellStyle = false;
        CellStyle cellStyle = workbook.getCellStyle(cellStyleName);
        // Cannot check cellStyle.isConditionalCesllStyle as MapStyle list will be added to cellStyle only at endWorkbook.
        if(cellStyle != null && mapStyleTempOldMap.containsKey(cellStyle.getStyleName()))
        {
            isConditionalCellStyle = true;
        }

        int c = column.getColumnIndex();
	int colsRepeated = columnHeader.getColsRepeated();
	int rowNum = sheet.getRowNum();
        for(int r = 0; r < rowNum; r++)
        {
            Row row = sheet.getRowReadOnly(r);
            if(row != null)
            {
                Cell cell = sheet.getCell(r, c);
                int tempColsRepeated = 0;
                while(tempColsRepeated != colsRepeated)
                {
                    tempColsRepeated += cell.getColsRepeated();
                    if(tempColsRepeated > colsRepeated)
                    {
                        tempColsRepeated -= cell.getColsRepeated();
                        // if the cell has colsRepeated we need to shift that to next cell as we are changing the cells Style
                        // Actually, calling setStylename() instead of setStyleNameFromParser() will do this but,
                        // that will call setUsedIndex() which has to be avoided. so getCell() here which will twke care of shifting colsRepeated.
                        sheet.getCell(r, c + columnHeader.getColsRepeated());
                        tempColsRepeated += cell.getColsRepeated();
                    }
                    // If the column has cols repeated 3 but the cell has cols repeated 1
                    // Then just set the cellstyle to the next cell as it lies inside column headers
                    // colsRepeated range.
                    if(cell.getStyleName() == null)
                    {
                        ((CellImpl)cell).setStyleNameFromParser(cellStyleName);
                        if(isConditionalCellStyle)
                        {
                            List<Cell> conditionalCellStyleList = conditionalcellStyleMap.get(sheet);
                            if(conditionalCellStyleList == null)
                            {
                                conditionalCellStyleList = new ArrayList<>();
                                conditionalcellStyleMap.put(sheet, conditionalCellStyleList);
                            }
                            conditionalCellStyleList.add(cell);
                        }
                    }
                    else if(cell.getStyleName().equals("Default"))
                    {
                        ((CellImpl)cell).setStyleNameFromParser(null);
                    }

                    if(tempColsRepeated < colsRepeated)
                    {
                        cell = sheet.getCell(r, c + tempColsRepeated);
                        //tempColsRepeated += cell.getColsRepeated();
                    }
                }

                // jump the count if row has rows repeated
                // as we treat the repeated rows as null
                if(row.getRowsRepeated() > 1)
                {
                    // null rows jumped
                    r += (row.getRowsRepeated()-1);
                }
            }
        }

        columnHeader.setDefaultCellStyleNameFromParser("Default"); //NO I18N
        
        // as we have moved the cellstyle to cells from this column header, should remove this columnheader from conditionalColumnsList.
        List<ColumnHeader> conditionalStyleColumnList = conditionalStyleColumnsMap.get(sheet);
        if(conditionalStyleColumnList != null && conditionalStyleColumnList.contains(columnHeader))
        {
            conditionalStyleColumnList.remove(columnHeader);
        }
    }

    @Override
    public void startRow(Row row)
    {
        Sheet sheet = workbook.getSheet(sheetIndex);
        row.setRowIndex(rowIndex);
        row.setSheet(sheet);
        sheet.addRowFromParser(row);
    }

    /**
     * Set Row visibility only if it's visible/collapse.
     * Filtered row will be updated to bitset while executing filter conditions of default filter
     * @param row
     * @param visibility
     */
    public void setRowVisibility(Row row, RowVisibility visibility)
    {
        if(RowUtil.isFiltered(visibility))
        {
            filteredRowsIndexes.set(row.getRowIndex());
        }
    	else if(RowUtil.isHidden(visibility))
    	{
            row.getSheet().getHiddenRowsBitSet().set(row.getRowIndex(), row.getRowIndex() + row.getRowsRepeated());
    	}
    }

    @Override
    public void endRow(Row row)
    {
        Sheet sheet = workbook.getSheet(sheetIndex);
        int nRowsRepeated = row.getRowsRepeated();

        if(!isRowWithEmptyCell)
        {
            rowHasDataIndex = rowIndex;
	    // Add here the rows repeated
	    rowHasDataIndex += nRowsRepeated-1;
            isRowWithEmptyCell = true;
        }
        rowIndex += nRowsRepeated;
        colIndex = 0;

        String styleName = row.getStyleName();
        if(styleName != null && !"default_ro".equals(styleName))
        {
            sheet.addUsedRowStyleName(styleName);
        }
    }
    
    @Override
    public void updateMergeCells(Cell cell, int rowSpan, int colSpan)
    {
        if(rowSpan > 1 || colSpan > 1)
        {
            mergeCellDetails.put(cell, new int[]{rowSpan, colSpan});
        }
    }

    @Override
    public void updateCell(Cell cell, String picklistAndItemIndex, String picklistAndItemID, String picklistSourceID, boolean isArrayCell)
    {
        Sheet sheet = workbook.getSheet(sheetIndex);
        Row row = sheet.getRow(rowIndex);
        Column column = sheet.getColumn(colIndex);
        cell.setRow(row); // set the row handle
        cell.setColumn(column);
        
        sheet.addCellFromParser(cell);
        
        if(cell.getLink() != null && cell.getLink().startsWith("#"))
        {
        	hyperLinkCells.add(cell);
        }
        if(cell.isZSString())
        {
            ZSString richString = (ZSString)cell.getValue().getRawValue();
            for(RichStringProperties link : richString.getProperties())
            {
                if(link.getUrl() != null && link.getUrl().startsWith("#"))
                {
                    richTextLinkCells.add(cell);
                }
            }
        }

        sheet.updateAutoArrayCell(cell);
        if(((CellImpl)cell).getAutoArrayRowSpan() > 1 || ((CellImpl)cell).getAutoArrayColSpan() > 1) {
            autoArrayParentCells.add(cell);
        }

        if(isArrayCell){
            sheet.updateArrayCell(cell, ((CellImpl)cell).getAutoArrayRowSpan(), ((CellImpl)cell).getAutoArrayColSpan());
        }

        if(picklistAndItemID != null) {
            int picklistID = Integer.parseInt(picklistAndItemID.split(":")[0]);
            int itemID = Integer.parseInt(picklistAndItemID.split(":")[1]);
            Picklist picklist = workbook.getPicklist(picklistID);

            if(picklist != null) {
                ((CellImpl)cell).setValueFromParser(picklist.getPicklistValueObj(itemID));
            }
            else {
                LOGGER.log(Level.WARNING, "[PICKLIST] Picklist with id: {0} not available in ODSworkbookTransformer (new)",picklistID);
            }
        }
        else if(picklistAndItemIndex != null) {
            int picklistIndex = Integer.parseInt(picklistAndItemIndex.split(":")[0]);
            int itemIndex = Integer.parseInt(picklistAndItemIndex.split(":")[1]);
            Picklist picklist = workbook.getPicklist(picklistIndex);

            if(picklist != null) {
                int itemID = itemIndex != -1 ? picklist.getItemAtIndex(itemIndex).getId() : -1;
                ((CellImpl) cell).setValueFromParser(picklist.getPicklistValueObj(itemID));
            }
            else {
                LOGGER.log(Level.WARNING, "[PICKLIST] Picklist with id: {0} not available in ODSworkbookTransformer",picklistIndex);
            }
        }
        if(picklistSourceID != null) {
            int picklistID;
            Integer itemID = null;
            for(String idString : picklistSourceID.split(";")) {
                if (idString.contains(":")) {
                    picklistID = Integer.parseInt(idString.split(":")[0]);
                    itemID = Integer.parseInt(idString.split(":")[1]);
                } else {
                    picklistID = Integer.parseInt(idString);
                }

                Picklist picklist = workbook.getPicklist(picklistID);

                if (picklist != null) {
                    CellPicklist cellPicklist = new CellPicklist(picklist, itemID);
                    cell.addCellPicklist(cellPicklist);
                }
            }
        }


        int nColsRepeated = cell.getColsRepeated();

        // to check if rows has some cells with data
        if(cell.getValue().getValue() != null || cell.getAnnotation() != null)
        {
            isRowWithEmptyCell = false;
        }

        //if((colHasDataIndex+(nColsRepeated-1)) < (cellColIndex))
        if(colHasDataIndex < (colIndex + nColsRepeated-1))
        {
            if( cell.getValue().getValue() != null || cell.getAnnotation() != null)// || cell.getStyleName() != null)
            {
                colHasDataIndex = colIndex;
                colHasDataIndex += nColsRepeated-1;
            }
        }
        
        colIndex += nColsRepeated;

	////////// draw control handling
	if(cell.getDrawControlList() != null)
	{
	    sheet.addDrawControlCell(cell);
	}

        String styleName = cell.getStyleName();
        if(styleName != null && !"Default".equals(styleName))
        {
            sheet.addUsedCellStyleName(styleName);
        }

        if(cell.getValue().getRawValue() instanceof ZSString)
        {
            int stringLength = ((CellImpl) cell).getStringLength();
            row.reviseStringLength(stringLength);
            sheet.reviseStringLength(stringLength);
            if(workbook.reviseStringLength(stringLength) > EngineConstants.FILE_STRING_LIMIT) {
                throw new AbortParseError(AbortParseError.Cause.STRING_LIMIT);
            }
            List<RichStringProperties> richStringProperties = ((ZSString)cell.getValue().getRawValue()).getProperties();
            if(richStringProperties != null && !richStringProperties.isEmpty())
            {
                for(RichStringProperties richText : richStringProperties)
                {
                    String textStyleName = richText.getStyleName();
                    if(textStyleName != null)
                    {
                        sheet.addUsedTextStyleName(textStyleName);
                    }
                }
            }
        }

        CellStyle temp = workbook.getCellStyle(styleName);
        // Cannot check CellStyle.isConditionalCellStyle as the mapstyle list will be added to CellStyle only at endWorkbook.
        if(temp != null && mapStyleTempOldMap.containsKey(temp.getStyleName()))//conditionalcellStyle
        {
            List<Cell> conditionalCellStyleList = conditionalcellStyleMap.get(sheet);
            if(conditionalCellStyleList == null)
            {
                conditionalCellStyleList = new ArrayList<>();
                conditionalcellStyleMap.put(sheet, conditionalCellStyleList);
            }
            conditionalCellStyleList.add(cell);
        }

        if(cell.getContentValidationName() != null)
        {
            if(!CellUtil.isRowRangeBound(cell.getRowIndex()) || !CellUtil.isColumnRangeBound(cell.getColumnIndex())) {
                // If cell is outside (0, 0, 65535, 255) removing the ContentValidation for it
                // However, if the cell is within (0, 0, 65535, 255) and its repeated extends outside (0, 0, 65535, 255), cell's CV is not removed, but only the CV range is trimmed - causing an inconsistency betwn the cell and range!!!
                ((CellImpl) cell).setContentValidationNameFromParser(null);
            } else {
                sheet.addUsedContentValidation(cell.getContentValidationName());

                Collection<RangeUtil.SheetRange> existingCells = this.sheetToCVToRanges.get(sheet, cell.getContentValidationName());
                if(existingCells == null) {
                    existingCells = new ArrayList<>();
                    this.sheetToCVToRanges.put(sheet, cell.getContentValidationName(), existingCells);
                }
                // Trim the CV range if it projects outside (0, 0, 65535, 255), still the cell has the CV - inconsistency!!!
                existingCells.add(new RangeUtil.SheetRange(cell.getRowIndex(), cell.getColumnIndex(), Math.min(Utility.MAXNUMOFROWS - 1, cell.getRowIndex() + cell.getRow().getRowsRepeated() - 1), Math.min(Utility.MAXNUMOFCOLS - 1, cell.getColumnIndex() + cell.getColsRepeated() - 1)));
            }
        }
    }

    @Override
    public void updateCellStyle(CellStyle cellStyle)
    {
        workbook.addCellStyle(cellStyle);
    }
    
    @Override
    public void updateNamedCellStyle(CellStyle cellStyle) {
        // Code to merge the properties in nodes style:default-style and style:style styleName="Default"
        if(cellStyle.getStyleName().equals("Default"))
        {
            CellStyle existingStyle = workbook.getCellStyle(cellStyle.getStyleName());
            if(existingStyle != null)
            {
                existingStyle = (CellStyle) Style.merge(cellStyle, existingStyle);
            }
        }

        workbook.addNamedCellStyle(cellStyle);
    }
    
    
    public void updateNamedGraphicStyle(GraphicStyle graphicStyle) {
        // Code to merge the properties in nodes style:default-style and style:style styleName="Default"
        if(graphicStyle.getStyleName().equals("Default"))
        {
            GraphicStyle existingStyle = workbook.getGraphicStyle(graphicStyle.getStyleName());
            if(existingStyle != null)
            {
                existingStyle = (GraphicStyle) Style.merge(graphicStyle, existingStyle);
            }
        }
        workbook.addNamedGraphicStyle(graphicStyle);
    }
    
    @Override
    public void updateColumnHeader(ColumnHeader columnHeader, ColumnVisibility columnvisibility)
    {
    	Sheet sheet = workbook.getSheet(sheetIndex);
        int nColsRepeated = columnHeader.getColsRepeated();
        if(columnvisibility.equals(ColumnVisibility.COLLAPSE))
        {
            sheet.getHiddenColumnsBitSet().set(colHeaderIndex, colHeaderIndex + nColsRepeated);
        }
    	sheet.addColumnHeaderFromParser(colHeaderIndex, columnHeader);

    	colHeaderIndex += nColsRepeated - 1;
    	colHeaderIndex++;

    	String styleName = columnHeader.getDefaultCellStyleName();
    	if(styleName != null && !"Default".equals(styleName))
    	{
    		sheet.addUsedCellStyleName(styleName);
    		/// If column header has conditionalStyle, ad full column to conditionalRangeMap of that sheet.
    		CellStyle temp = workbook.getCellStyle(styleName);
    		// Cannot check CellStyle.isConditionalCellStyle as the mapstyle list will be added to CellStyle only at endWorkbook.
    		if(temp != null && mapStyleTempOldMap.containsKey(temp.getStyleName()))
    		{
    			List<ColumnHeader> conditionalStyleColumnList = conditionalStyleColumnsMap.get(sheet);
    			if(conditionalStyleColumnList == null)
    			{
    				conditionalStyleColumnList = new ArrayList<>();
    				conditionalStyleColumnsMap.put(sheet, conditionalStyleColumnList);
    			}
    			conditionalStyleColumnList.add(columnHeader);
    		}
    		////////////////
    	}

    	styleName = columnHeader.getStyleName();
    	if(styleName != null && !"default_co".equals(styleName))
    	{
    		sheet.addUsedColumnStyleName(styleName);
    	}
    }

    @Override
    public void updateColumnStyle(ColumnStyle columnStyle)
    {
        workbook.addColumnStyle(columnStyle);
    }

    @Override
    public void updateDummyCellRepeated(int colRepeated)
    {
        colIndex += colRepeated;
    }
    @Override
    public void updateStylesVersionId(int stylesVID){
        workbook.setStylesCorrectionId(stylesVID);
    }
    @Override
    public void updateFontFace(FontFace fontFace)
    {

        //logger.info(fontFace);
        workbook.addFontFace(fontFace);
    }

    @Override
    public void updateRowStyle(RowStyle rowStyle)
    {
        workbook.addRowStyle(rowStyle);
    }

    @Override
    public void updateSheetStyle(SheetStyle sheetStyle)
    {
        workbook.addSheetStyle(sheetStyle);
    }


    public Workbook transform()
    {
        return workbook;
    }

    @Override
    public void updateForms(Forms forms)
    {
        Sheet sheet = workbook.getSheet(sheetIndex);
        sheet.setForms(forms);
    }


    @Override
    public void updateGraphicStyle(GraphicStyle graphicStyle)
    {
        workbook.addGraphicsStyle(graphicStyle);
    }


    @Override
    public void updateParagraphStyle(ParagraphStyle paragraphStyle)
    {
        workbook.addParagraphStyle(paragraphStyle);
    }

    @Override
    public void updateTextStyle(TextStyle textStyle)
    {
        workbook.addTextStyle(textStyle);
    }
    
    @Override
    public ZSPattern updatePattern(String patternStr, boolean isAccountingFormat, boolean isAutoOrder) {
        // Temp fix for minutes attribute missing in pattern string.
        if("[HH]:".equalsIgnoreCase(patternStr.trim())) {
            patternStr = "[HH]:mm"; //No I18N
        }
        /////////////
        
        // Temp Fix for repeated strings ")" 
        patternStr = patternStr.replaceAll("([\"][)][\"])+", "\")\"");
         // Temp Fix for repeated strings "-" 
        patternStr = patternStr.replaceAll("([\"][-][\"])+", "\"-\"");
         // Temp Fix for repeated strings " " 
        patternStr = patternStr.replaceAll("([\"][ ][\"])+", "\" \"");
        
        // Temp Fix for repeated string )
        if(patternStr.contains(")))))")) {
            patternStr = patternStr.replaceAll("[)]+", ")");
        }
        /////////////
        
        // Temp fix for 0 not becoming -
        if(patternStr.equals("[<0]\\-[pt-BR]#0.00_-;[>0]_-[pt-BR]#0.00_-;\"\"_-\"-\"[pt-BR]#00.??_-;\"_-\"@\"_-\""))
        {
            patternStr = "[<0]\\-[pt-BR]#0.00_-;[>0]_-[pt-BR]#0.00_-;\"\"_-\"-\"[pt-BR]#??_-;\"_-\"@\"_-\"";//No I18N
        }
        /////////
        
        long s0 = System.currentTimeMillis();
        ZSPattern pattern;
        if(patternStr.startsWith("\"\"-\"[$en-CA]\"\"\"")) { //No I18N
            /* Temporary for fix*/
            Locale canadaLocale = LocaleUtil.getLocale("en", "CA"); //No I18N
            pattern  = DefaultPatternUtil.getDefaultPattern(this.workbook, Cell.Type.CURRENCY, canadaLocale, canadaLocale);
        }
        else {
            // The number style name is used as such for the pattern name
            pattern = new ZSPattern(patternStr,SpreadsheetSettings.defaultSpreadsheetSettings, isAccountingFormat,isAutoOrder, workbook.getSpreadsheetSettings());
        }


        long s2 = System.currentTimeMillis();
        
        if(s2 - s0 > 1000) {
            String patternStrTrimmed = patternStr.substring(0, Math.min(200, patternStr.length()));
            LOGGER.log(Level.WARNING, "Time for patternString: {0} >>>> create pattern {1}", new Object[]{patternStrTrimmed, s2 - s0});
        }
        return pattern;
    }

    @Override
    public void updateNumberStyle(NumberStyle numberStyle)
    {
        // This method should be called from parser alone
        // this will initialize the pattern here
        //numberStyle.initPattern(workbook.getFunctionLocale());

        /////////////////////////////////////////////////////////
        /// FIX for styleName and mapStyleName having same name
//        if (numberStyle.getStyleName().endsWith("P0") && !numberStyle.getMapStyleList().isEmpty())
//        {
//            numberStyle.getMapStyleList().clear();
////            com.adventnet.zoho.websheet.model.style.Pattern pattern = numberStyle.getPattern();
////            String sName = numberStyle.getStyleName();
////            List<NumberStyle> numberStyleList = Pattern.createNumberStyle(sName.substring(0, sName.length() - 2), numberStyle.getPattern(), workbook.getFunctionLocale(), workbook);
////            for (NumberStyle nStyle : numberStyleList)
////            {
////                nStyle.initPattern(workbook.getFunctionLocale());
////                this.updateNumberStyle(nStyle);
////            }
//        }
        //// FIX Ends
        ////////////////////////////////////////////////////////////////////////////

        this.numberStyleMap.put(numberStyle.getStyleName(), numberStyle);
    }

     /*
     * Adding named Ranges to the workbook.
     *
     *
    public void updateNamedExpressions(String name, String cellRangeAddress, String baseCellAddress)
    {
	try
        {

            Range namedRange = new Range(workbook, name, cellRangeAddress, baseCellAddress);
	    //logger.info("Named Ranges : "+name+" :: CellRangeAddress : "+cellRangeAddress+" :: BaseCellAddress : "+baseCellAddress);

//	    Sheet tempSheet = namedRange.getSheet();
//	    tempSheet.addNamedRange(namedRange);
	    workbook.addNamedRangeFromParser(namedRange);
        }catch(Exception e)
        {
	    logger.info("********Error in creating NamedRange : "+name+" :: CellRangeAddress : "+cellRangeAddress+" :: BaseCellAddress : "+baseCellAddress);
            //e.printStackTrace();
        }
    }
      *
      */

    @Override
    public void updateProtectedRange(String cellRangeAddress, String baseCellAddress, String authUsersStr, String unAuthUsersStr, String authGroupsStr, String authOrgsStr, String isPubAuthorizedStr, boolean isAllowInsert, boolean isAllowFormats, String authExternalShareLinksStr, String unAuthExternalShareLinksStr)
    {
        try
        {
            Range range = new Range(workbook, cellRangeAddress, baseCellAddress, ReferenceMode.A1, true);

//            if(range == null)
//            {
//                LOGGER.log(Level.INFO, "Unable to create the protected range {0} : with permissions: AUTHUSERS {1} , UNAUTHUSERS {2}, AUTHGROUPS {3}, UNAUTHGROUPS {4}", new Object[]
//                        {
//                            cellRangeAddress, authUsersStr, unAuthUsersStr, authGroupsStr, authOrgsStr
//                        });
//                return;
//            }
            
            Set<String> authoriedUsers = ODSWorkbookParser.createSetFromString(authUsersStr);
            Set<String> unAuthoriedUsers = ODSWorkbookParser.createSetFromString(unAuthUsersStr);
            Set<String> authoriedGroups = ODSWorkbookParser.createSetFromString(authGroupsStr);
            Set<String> authoriedOrgs = ODSWorkbookParser.createSetFromString(authOrgsStr);
            Boolean isPubAuthorized = isPubAuthorizedStr == null ? null : Boolean.parseBoolean(isPubAuthorizedStr);
            Set<String> authExternalShareLinks = ODSWorkbookParser.createSetFromString(authExternalShareLinksStr);
            Set<String> unAuthExternalShareLinks = ODSWorkbookParser.createSetFromString(unAuthExternalShareLinksStr);
            
            EnumMap permissionMap = Protection.createPermissionMap(authoriedUsers, unAuthoriedUsers, authoriedGroups, authoriedOrgs, isPubAuthorized, authExternalShareLinks, unAuthExternalShareLinks);
//            if(protectedObj instanceof Range)
//            {
            Protection protection = new Protection(permissionMap, isAllowInsert, isAllowFormats);

            List<DataRange> ranges = range.getSheet().getProtectionRangeMap().computeIfAbsent(protection, k -> new ArrayList<>());
            ranges.add(range.toDataRange());
//            }
//            else
//            {
//                ((Sheet) protectedObj).lock(authoriedUsers, authoriedGroups, unAuthoriedUsers, unAuthoriedGroups);
//            }
        }catch(SheetEngineException e)
        {
            LOGGER.log(Level.INFO, "Unable to create the protected range {0} : with permissions: AUTHUSERS {1} , UNAUTHUSERS {2}, AUTHGROUPS {3}, UNAUTHGROUPS {4}", new Object[]
                    {
                        cellRangeAddress, authUsersStr, unAuthUsersStr, authGroupsStr, authOrgsStr
                    });
        }
    }

    @Override
    public void updateFormRange(String cellRangeAddress, String baseCellAddress)
    {
        try
        {
             FormRange formRange = new FormRange(workbook, cellRangeAddress, baseCellAddress);
             formRange.getSheet().addFormRangeFromParser(formRange);
//            if(formRange != null)
//            {
//                formRange.getSheet().addFormRangeFromParser(formRange);
//            }
//            else
//            {
//                LOGGER.log(Level.INFO, "Unable to create the form range {0} ", new Object[]{cellRangeAddress});
//                return;
//            }
        }
        catch(SheetEngineException e)
        {
            LOGGER.log(Level.INFO, "Unable to create the form range {0} : ", new Object[]{cellRangeAddress});
        }
    }
    
    @Override
    public void updateCheckboxRange(String cellRangeAddress, String baseCellAddress)
    {
        try
        {
            Range range = new Range(workbook, cellRangeAddress, baseCellAddress, ReferenceMode.A1, true);
            RangeUtil.mergeAndAddDataRangeToList(range.toDataRange(), range.getSheet().getCheckboxRangeList());
            
        } catch (SheetEngineException ex)
        {
            LOGGER.log(Level.INFO, "Unable to create checkbox range {0} : ", new Object[]{cellRangeAddress});
        }
    }


    /*
     * Adding named Ranges to the workbook.
     *
     */
    @Override
    public void updateNamedExpression(String name, String expression, String baseCellAddress, boolean isNamedRange)
    {
	try
        {
            Sheet sheet = workbook.getSheet(sheetIndex);
            NamedExpression namedExpression = new NamedExpression(workbook, name, (sheet != null) ? sheet.getAssociatedName() : null, expression, null, baseCellAddress, isNamedRange, ReferenceMode.A1);
	        workbook.addNamedExpressionFromParser(namedExpression);
        }catch(Exception e)
        {
	        LOGGER.log(Level.INFO, "********Error in creating NamedRange : {0} :: Expression : {1} :: BaseCellAddress : {2}", new Object[]{name, expression, baseCellAddress});
            //e.printStackTrace();
        }
    }

    @Override
    public void updatePivotTable(PivotTable pivotTable)
    {
    	try{
            pivotTable.setName(ActionUtil.getNewPivotName(workbook));
            workbook.addPivotTable(pivotTable);
            pivotTable.init(workbook);
    	}catch(Exception e){
    		LOGGER.log(Level.WARNING,"Exception ",e);
    	}
    }

    public void updateDefaultFilterView(String rangeAddress, Filter filters)
    {
        try
        {
            Sheet sheet = this.workbook.getSheet(this.sheetIndex);
            Range range = new Range(this.workbook, rangeAddress, null, ReferenceMode.A1, false);
            DefaultFilterView defaultFilterView = new DefaultFilterView(range.toDataRange(), filters);
            sheet.setDefaultFilterView(defaultFilterView);
        }
        catch(Exception ex)
        {
            LOGGER.log(Level.WARNING,"[FILTERS][Exception] Exception while setting default filter view in the sheet.",ex);
        }
    }

    @Override
    public void updateFrameList(Frame frame) {
//        if(frame.getImage() != null) {
            if((frame.getImage() != null && frame.getImage().getXlinkHref() != null && !frame.getImage().getXlinkHref().equals(""))
                    || (frame.getDrawObject() != null && frame.getDrawObject().getXlinkHref() != null && !frame.getDrawObject().getXlinkHref().equals(""))) {
                frame.setRow(this.rowIndex);//todo rowIndex might be incorrect.
                frame.setColumn(this.colIndex);//todo colIndex is incorrect, in <cell> colIndex += colRepeated.
                workbook.getSheet(sheetIndex).addFrame(frame);
            }
//        }
    }
    
    @Override
    public void updateCommentRange(String name, String cellRangeAddress, String baseCellAddress)
    {
    	 try
         {
             Range range = new Range(workbook, cellRangeAddress, baseCellAddress, ReferenceMode.A1, true);
//             List<Range> ranges = new ArrayList<Range>(); 
//             ranges.add(range);
             range.getSheet().getDiscussionRangeMap().put(name, range.toDataRange());
         }catch(SheetEngineException e)
         {
        	 LOGGER.log(Level.INFO, "********Error in creating NamedRange : {0} :: Expression : {1} :: BaseCellAddress : {2}", new Object[]{name, cellRangeAddress, baseCellAddress}); //No I18N
         }
    }

    /*
    public void updateNumberStyle(NumberStyle numberStyle) {
        workbook.addNumberStyle(numberStyle);
    }
     */
    

    // A class to maintain the values of MapStyle's attributes temporarily.
    // Those values will be used to create MapStyle object at endWorkbook and will be added to appropriate cell/NumberStyle object.
    private class MapStyleTempOld
    {
        private final String condition;
        private String applyStyleName;
        private final String baseCellAddress;
        private final Object style;

        MapStyleTempOld(String inCondition, String inBaseCellAddress, String inApplyStyleName, Object inStyle)
        {
            this.condition = inCondition;
            this.baseCellAddress = inBaseCellAddress;
            this.applyStyleName = inApplyStyleName;
            this.style = inStyle;
        }

        @Override
        public String toString()
        {
            return condition+" : "+applyStyleName;
        }
        
        // This method will create mapStyle Object from MapStyleTemp and set that to style object. 
        private void createAndAddMapStyleToStyle(Map<String, List<MapStyle>> mapStyleMap)
        {
            try
            {
                boolean isMemberOfNumberStyle = style instanceof NumberStyle;
                
                if (isMemberOfNumberStyle)
                {
                    NumberStyle nStyle = (NumberStyle) style;
                    NumberStyle childNumberStyle = numberStyleMap.get(this.applyStyleName);
                    if(childNumberStyle == null) {
                        LOGGER.log(Level.SEVERE, "Cannot find NumberStyle for : parentStyleName: {0}  condition: {1}  applyStyleName: {2}", new Object[]{nStyle.getStyleName(), condition, applyStyleName});
                        childNumberStyle = nStyle.absoluteClone(workbook);
                        childNumberStyle.setStyleName(applyStyleName);
                        List<NumberElement> numberElementList = childNumberStyle.getNumberElementList();
                        if(numberElementList.get(0).getContent() != null && numberElementList.get(0).getContent().equals("-")) {
                            numberElementList.remove(0);
                        }
                        numberStyleMap.put(this.applyStyleName, childNumberStyle);
                    }
                    MapStyle mapStyle = new MapStyleAdapter(workbook, this.condition, childNumberStyle);
                    nStyle.addMapStyle(mapStyle);
                }
                else
                {
                    MapStyle mapStyle = new MapStyleAdapter(workbook, this.condition, this.baseCellAddress, this.applyStyleName);
                    List<MapStyle> mapStyleList = mapStyleMap.get(((CellStyle) style).getStyleName());
                    if(mapStyleList == null)
                    {
                        mapStyleList = new ArrayList<>();
                        mapStyleMap.put(((CellStyle) style).getStyleName(), mapStyleList);
                    }
                    mapStyleList.add(mapStyle);
                }
            } catch (IllegalArgumentException e)
            {
                LOGGER.log(Level.INFO, e.toString());
            }
        }
    }
    
    public enum ConditionalStyleType
    {
        CLASSIC,
        COLORSCALE,
        ICONSET,
        DATABAR
    }
    
    private class ConditionalFormatEntryTemp{
        private final ConditionalFormatEntry.Entry_Type entryType;
        private final String value;
        private final ConditionalStyleType type;
        private Object conditionalFormatEntry;
        private String baseCellAddress;
        
        //Colorscale;
        public ConditionalFormatEntryTemp(ConditionalFormatEntry.Entry_Type entryType, String value, ZSColor color, String baseCellAddress)
        {
            this.type = ConditionalStyleType.COLORSCALE;
            this.entryType = entryType;
            this.value = value;
            this.baseCellAddress = baseCellAddress;
            this.conditionalFormatEntry = new ConditionalFormatEntry.ColorScaleObj(color);
        }
        
        //Iconset
        public ConditionalFormatEntryTemp(ConditionalFormatEntry.Entry_Type entryType, String value, String name, Integer id, String iconCriteria, String baseCellAddress)
        {
            this.type = ConditionalStyleType.ICONSET;
            this.entryType = entryType;
            this.value = value;
            this.baseCellAddress = baseCellAddress;
            this.conditionalFormatEntry = new ConditionalFormatEntry.IconSetObj(name, id, iconCriteria);
        }
        
        //Databar
        public ConditionalFormatEntryTemp(ConditionalFormatEntry.Entry_Type entryType, String value, String baseCellAddress)
        {
            this.type = ConditionalStyleType.DATABAR;
            this.entryType = entryType;
            this.value = value;
            this.baseCellAddress = baseCellAddress;
        }

        public ConditionalFormatEntry createInstance() {
            Object valueObj = value;
            if(value.startsWith("="))
            {
                // if baseCellAddress == null for a formula value
                // it was either written by
                // (a) an application like LIBO
                // (b) older build
                // There is a difference between the 2
                // (a) :::: baseCellAddress is TopLeft of TargetRange
                // (b) :::: baseCellAddress is A1 (written by mistake without properly studying about (a))
                //
                // In order to make sure that files written by older builds should open in tact, we go with (b) by sending baseCellAddress = null (i.e. A1)
                // After a few days, when the number of (b) files go less, we can switch to (a) by adding the snippet
                // if(this.baseCellAddress == null) { this.baseCellAddress = topLeft of targetRange }
                valueObj = ExpressionImpl.getInstanceFromXMLConditionFormula(workbook, value, this.baseCellAddress, CellReference.ReferenceMode.A1);
            }
            switch(type)
            {
                case COLORSCALE:
                    ZSColor color = ((ConditionalFormatEntry.ColorScaleObj)conditionalFormatEntry).getZSColor();
                    return new ConditionalFormatEntry(entryType, valueObj, color);
                case ICONSET:
                    ConditionalFormatEntry.IconSetObj iconSetObj = (ConditionalFormatEntry.IconSetObj)conditionalFormatEntry;
                    return new ConditionalFormatEntry(entryType, valueObj, iconSetObj.getName(), iconSetObj.getId(), iconSetObj.getIconCriteria().toString());
                default: //case DATABAR:
                    return new ConditionalFormatEntry(entryType, valueObj);
            } 
        }
    }
    
    private class ConditionalStyleTemp{
        private final List<DataRange> ranges;
        private final List<ConditionalFormatEntryTemp> csTempList;
        private boolean isAutoColor;
        private boolean isHideText;
        private final ConditionalStyleType type;
        
        private String iconSetName;
        private boolean iconDefaultSize;
        private boolean iconReverseOrder;
        
        private DataBar.Direction dbDirection;
        private DataBar.Type fillType;
        private DataBar.Type borderType;
        private DataBar.Axis axisPosition;

        private ZSColor fillPColor;
        private ZSColor fillNColor;
        private ZSColor borderPColor;
        private ZSColor borderNColor;
        private ZSColor axisColor;
        
        private String applyStyleName;
        private String condition;
        private String baseCellAddress;
        
        //Classic
        public ConditionalStyleTemp(ConditionalStyleType type, List<DataRange> ranges, List<ConditionalFormatEntryTemp> csTempList, String applyStyleName, String condition, String baseCellAddress)
        {
            this(type, ranges, csTempList);
            this.applyStyleName = applyStyleName;
            this.condition = condition;
            this.baseCellAddress = baseCellAddress;
        }
        
        //Colorscale
        public ConditionalStyleTemp(ConditionalStyleType type, List<DataRange> ranges, List<ConditionalFormatEntryTemp> csTempList, boolean isAutoColor, boolean isHideText)
        {
            this(type, ranges, csTempList);
            this.isAutoColor = isAutoColor;
            this.isHideText = isHideText;
        }
        
        //Iconset
        public ConditionalStyleTemp(ConditionalStyleType type, List<DataRange> ranges, List<ConditionalFormatEntryTemp> csTempList, String iconSetName, boolean iconDefaultSize, boolean iconReverseOrder, boolean isAutoColor, boolean isHideText)
        {
            this(type, ranges, csTempList, isAutoColor, isHideText);
            this.iconSetName = iconSetName;
            this.iconDefaultSize = iconDefaultSize;
            this.iconReverseOrder = iconReverseOrder;
        }
        
        //Databar
        public ConditionalStyleTemp(ConditionalStyleType type, List<DataRange> ranges, List<ConditionalFormatEntryTemp> csTempList, DataBar.Direction dbDirection, DataBar.Type fillType, ZSColor fillPColor, ZSColor fillNColor, DataBar.Type borderType, ZSColor borderPColor, ZSColor borderNColor, DataBar.Axis axisPosition, ZSColor axisColor, boolean isAutoColor, boolean isHideText)
        {
            this(type, ranges, csTempList, isAutoColor, isHideText);
            this.dbDirection = dbDirection;
            this.fillType = fillType;
            this.fillPColor = fillPColor;
            this.fillNColor = fillNColor;
            this.borderType = borderType;
            this.borderPColor = borderPColor;
            this.borderNColor = borderNColor;
            this.axisPosition = axisPosition;
            this.axisColor = axisColor;
        }
        
        private ConditionalStyleTemp(ConditionalStyleType type, List<DataRange> ranges, List<ConditionalFormatEntryTemp> csTempList)
        {
            this.type = type;
            this.ranges = ranges;
            this.csTempList = csTempList;
        }
        

        @Override
        public String toString() {
            return "ConditionalStyleTemp{" + "ranges=" + ranges + ", csTempList=" + csTempList + ", isAutoColor=" + isAutoColor + ", isHideText=" + isHideText + '}'; // No I18N
        }
        
        public void createConditionalStyle(Sheet sheet)
        {
            try{
                ConditionalStyle conditionalStyle;
                if(type.equals(ConditionalStyleType.CLASSIC))
                {
                    conditionalStyle = new MapStyleAdapter(workbook, condition, baseCellAddress, applyStyleName);
                }
                else{
                    List<ConditionalFormatEntry> csEntries = new ArrayList();
                    for(ConditionalFormatEntryTemp csTemp : csTempList)
                    {
                        csEntries.add(csTemp.createInstance());
                    }
                    switch(type)
                    {
                        case COLORSCALE:
                            conditionalStyle = new ColorScale(csEntries);
                            break;
                        case ICONSET:
                            conditionalStyle = new IconSet(iconSetName, csEntries, iconDefaultSize, iconReverseOrder);
                            break;
                        default: //case DATABAR:
                            conditionalStyle = new DataBar(csEntries, dbDirection, fillType, fillPColor, fillNColor, borderType, borderPColor, borderNColor, axisPosition, axisColor);
                    }
                }
                sheet.addToFormatMap(ranges, conditionalStyle, ConditionalStyleObject.CONDITIONALSTYLE_THEME.NO_THEME, isAutoColor, isHideText);
            }
            catch(IllegalArgumentException e)
            {
//                LOGGER.log(Level.INFO, "Unable to create ConditionalStyle ", e); // No I18N
            }
        }
    }
    
    public void updateSparkline(String source, String destination, String orientation, String minValue, String maxValue){
        Sheet sheet = this.workbook.getSheet(this.sheetIndex);
        List<TempSparklinesGroup> tempSparklinesGroupList = sparklinesMap.get(sheet);
        TempSparklinesGroup sparklinesGroup = tempSparklinesGroupList.get(tempSparklinesGroupList.size()-1);
        sparklinesGroup.sourceList.add(source);
        sparklinesGroup.destinationList.add(destination);
        sparklinesGroup.orientationList.add(orientation);
        sparklinesGroup.minValue = Double.parseDouble(minValue);
        sparklinesGroup.maxValue = Double.parseDouble(maxValue);
                
    }
    
    @Override
    public void updateSparklineProperties(String type, String sparklineColor, String sparklineTint, String sparklineTheme, boolean isMarkerRequired, String markerColor, String markerTint, String markerTheme, String negativeColor, String negativeTint, String negativeTheme, String highPointColor, String highPointTint, String highPointTheme, String lowPointColor, String lowPointTint, String lowPointTheme, String firstPointColor, String firstPointTint, String firstPointTheme, String lastPointColor, String lastPointTint, String lastPointTheme, String minType, String maxType, boolean isXAxisRequired, String hiddenCells, String emptyCells, boolean isReversed) {
        Sheet sheet = this.workbook.getSheet(this.sheetIndex);
        List<TempSparklinesGroup> tempSparklinesGroupList = sparklinesMap.get(sheet);
        TempSparklinesGroup sparklinesGroup = tempSparklinesGroupList.get(tempSparklinesGroupList.size()-1);
        boolean highPointRequired = true;
        boolean firstPointRequired = true;
        boolean lowPointRequired = true;
        boolean lastPointRequired = true;
        boolean negativePointRequired = true;

        if(highPointColor == null && highPointTint == null && highPointTheme == null) {
            highPointRequired = false;
            highPointColor = SparklineProperties.DefaultSparklineColors.HIGH_POINT.getHexColor();
            highPointTheme = SparklineProperties.DefaultSparklineColors.HIGH_POINT.getThemeName();
            highPointTint = SparklineProperties.DefaultSparklineColors.HIGH_POINT.getTint();
        }
        if(firstPointColor == null && firstPointTint == null && firstPointTheme == null) {
            firstPointRequired = false;
            firstPointColor = SparklineProperties.DefaultSparklineColors.FIRST_POINT.getHexColor();
            firstPointTheme = SparklineProperties.DefaultSparklineColors.FIRST_POINT.getThemeName();
            firstPointTint = SparklineProperties.DefaultSparklineColors.FIRST_POINT.getTint();
        }
        if(lowPointColor == null && lowPointTint == null && lowPointTheme == null) {
            lowPointRequired = false;
            lowPointColor = SparklineProperties.DefaultSparklineColors.LOW_POINT.getHexColor();
            lowPointTheme = SparklineProperties.DefaultSparklineColors.LOW_POINT.getThemeName();
            lowPointTint = SparklineProperties.DefaultSparklineColors.LOW_POINT.getTint();
        }
        if(lastPointColor == null && lastPointTint == null && lastPointTheme == null) {
            lastPointRequired = false;
            lastPointColor = SparklineProperties.DefaultSparklineColors.LAST_POINT.getHexColor();
            lastPointTheme = SparklineProperties.DefaultSparklineColors.LAST_POINT.getThemeName();
            lastPointTint = SparklineProperties.DefaultSparklineColors.LAST_POINT.getTint();
        }
        if(negativeColor == null && negativeTint == null && negativeTheme == null) {
            negativePointRequired = false;
            negativeColor = SparklineProperties.DefaultSparklineColors.NEGATIVE.getHexColor();
            negativeTheme = SparklineProperties.DefaultSparklineColors.NEGATIVE.getThemeName();
            negativeTint = SparklineProperties.DefaultSparklineColors.NEGATIVE.getTint();
        }
        if(markerColor == null && markerTint == null && markerTheme == null) {
            isMarkerRequired = false;
            markerColor = SparklineProperties.DefaultSparklineColors.MARKER.getHexColor();
            markerTheme = SparklineProperties.DefaultSparklineColors.MARKER.getThemeName();
            markerTint = SparklineProperties.DefaultSparklineColors.MARKER.getTint();
        }


        SparklineProperties sparklineProperties = new SparklineProperties(SparklinesGroup.SparklineType.valueOf(type.toUpperCase()),
                highPointRequired,
                ZSColor.getColor(highPointColor,highPointTheme,highPointTint),
                firstPointRequired,
                ZSColor.getColor(firstPointColor,firstPointTheme,firstPointTint),
                lowPointRequired,
                ZSColor.getColor(lowPointColor,lowPointTheme,lowPointTint),
                lastPointRequired,
                ZSColor.getColor(lastPointColor,lastPointTheme,lastPointTint),
                negativePointRequired,
                ZSColor.getColor(negativeColor,negativeTheme,negativeTint),
                isMarkerRequired,
                ZSColor.getColor(sparklineColor,sparklineTheme,sparklineTint),
                ZSColor.getColor(markerColor,markerTheme,markerTint),
                isReversed,
                SparklinesGroup.VerticalAxesValue.valueOf(maxType),
                SparklinesGroup.VerticalAxesValue.valueOf(minType),
                SparklinesGroup.EmptyCells.valueOf(emptyCells),
                Boolean.valueOf(hiddenCells),
                isXAxisRequired);
        sparklinesGroup.property = sparklineProperties;
    }
    
    @Override
    public void updateSparklinesGroup() {
        Sheet sheet = this.workbook.getSheet(this.sheetIndex);
        List<TempSparklinesGroup> list = sparklinesMap.get(sheet);
        if(list == null) {
            list = new ArrayList<>();
            sparklinesMap.put(sheet,list);
        }
        list.add(new TempSparklinesGroup());
    }
    
    static class TempSparklinesGroup {
        public List<String> sourceList;
        public List<String> destinationList;
        public List<String> orientationList;
        SparklineProperties property;
        double minValue;
        double maxValue;
        
        TempSparklinesGroup() {
            this.sourceList = new ArrayList();
            this.destinationList = new ArrayList();
            this.orientationList = new ArrayList();
        }
                
    }
    
    @Override
    public void startConditionalFormats()
    {       
        isUpdateConditionStyle = true;
    }
    
    @Override
    public void startConditionalFormat(String targetRangeAddress, String csPriority) throws SheetEngineException
    {
        currentCFTargetRanges = existingCFRangeMap.get(targetRangeAddress);
        if(currentCFTargetRanges == null)
        {
            currentCFTargetRanges = new ArrayList();
            if(targetRangeAddress.contains(".")){
                targetRangeAddress = targetRangeAddress.replaceAll("('(.*?)'.)|([^: ]+[.])", ""); //No I18N
            }
            String[] ranges = targetRangeAddress.split(" ");
            for(String range : ranges)
            {
                currentCFTargetRanges.add(new Range(workbook, range, "'"+workbook.getSheet(sheetIndex).getName()+"'.A1", ReferenceMode.A1, true).toDataRange()); //No I18N
            }
            existingCFRangeMap.put(targetRangeAddress, currentCFTargetRanges);
        }
        isCFInReverseOrder = isCFInReverseOrder || (csPriority == null);
        currentPriority = (csPriority == null) ? currentPriority + 1 : Integer.parseInt(csPriority);
    }

    @Override
    public void updateMapStyle(String condition, String baseCellAddress, String applyStyleName, Object style)
    {
        MapStyleTempOld msTemp = new MapStyleTempOld(condition, baseCellAddress, applyStyleName, style);
        String styleName = (style instanceof Style) ? ((Style)style).getStyleName() : ((NumberStyle)style).getStyleName();
        List<MapStyleTempOld> mapStyleTempList = mapStyleTempOldMap.get(styleName);
        if(mapStyleTempList == null)
        {
            mapStyleTempList = new ArrayList<>();
            mapStyleTempOldMap.put(styleName, mapStyleTempList);
        }
        mapStyleTempList.add(msTemp);
    }
    
    @Override
    public void updateCondition(String applyStyleName, String condition, String baseCellAddress)
    {
        workbook.getSheet(this.sheetIndex).addUsedCellStyleName(applyStyleName);
        addToConditionalStyleMap(new ConditionalStyleTemp(ConditionalStyleType.CLASSIC, currentCFTargetRanges, null, applyStyleName, condition, baseCellAddress));
    }
    
    @Override
    public void updateColorScale(boolean isAutoColor, boolean isHideText) {
        addToConditionalStyleMap(new ConditionalStyleTemp(ConditionalStyleType.COLORSCALE, currentCFTargetRanges, conditionalStyleList, isAutoColor, isHideText));
        conditionalStyleList = new ArrayList();
    }
    
     @Override
    public void updateColorScaleEntry(ConditionalFormatEntry.Entry_Type type, String value, ZSColor color, String baseCellAddress) {
        conditionalStyleList.add(new ConditionalFormatEntryTemp(type, value, color, baseCellAddress));
    }
    
    @Override
    public void updateIconSet(String iconSetName, boolean iconDefaultSize, boolean iconReverseOrder, boolean isAutoColor, boolean isHideText)
    {
        addToConditionalStyleMap(new ConditionalStyleTemp(ConditionalStyleType.ICONSET, currentCFTargetRanges, conditionalStyleList, iconSetName, iconDefaultSize, iconReverseOrder, isAutoColor, isHideText));
        conditionalStyleList = new ArrayList();
    }
    
    @Override
    public void updateIconSetEntry(ConditionalFormatEntry.Entry_Type cstype, String aValue, String aIconName, Integer aIconID, String iconCriteria, String baseCellAddress)
    {
        conditionalStyleList.add(new ConditionalFormatEntryTemp(cstype, aValue, aIconName, aIconID, iconCriteria, baseCellAddress));
    }
   
    @Override
    public void updateDataBar(DataBar.Direction dbDirection, DataBar.Type fillType, ZSColor fillPColor, ZSColor fillNColor, DataBar.Type borderType, ZSColor borderPColor, ZSColor borderNColor, DataBar.Axis axisPosition, ZSColor axisColor, boolean isAutoColor, boolean isHideText)
    {
        addToConditionalStyleMap(new ConditionalStyleTemp(ConditionalStyleType.DATABAR, currentCFTargetRanges, conditionalStyleList, dbDirection, fillType, fillPColor, fillNColor, borderType, borderPColor, borderNColor, axisPosition, axisColor, isAutoColor, isHideText));
        conditionalStyleList = new ArrayList();
    }
     
    @Override
    public void updateDataBarEntry(ConditionalFormatEntry.Entry_Type cstype, String aValue, String baseCellAddress)
    {
        conditionalStyleList.add(new ConditionalFormatEntryTemp(cstype, aValue, baseCellAddress));
    }
    
    private void addToConditionalStyleMap(ConditionalStyleTemp conditionalStyleTemp)
    {
        Sheet sheet = workbook.getSheet(sheetIndex);
        Map<Integer, ConditionalStyleTemp> csMapForSheets = conditionalStyleMapForSheets.get(sheet);
        if(csMapForSheets == null)
        {
            csMapForSheets = new HashMap();
            conditionalStyleMapForSheets.put(sheet, csMapForSheets);
        }
        if(csMapForSheets.containsKey(currentPriority))
        {
            currentPriority++;
        }
        csMapForSheets.put(currentPriority, conditionalStyleTemp);
    }

    private class PicklistTemp {
        int picklistID;
        int defaultIndex;
        int defaultID;
        String rangeStrings;
        String sourceRangeString;
        boolean isRangePicklist;
        List<PicklistItemTemp> items;

        PicklistTemp(int picklistID, int defaultIndex, int defaultID, String rangeStrings, String sourceRangeString, boolean isRangePicklist) {
            this.picklistID = picklistID;
            this.defaultIndex = defaultIndex;
            this.defaultID = defaultID;
            this.rangeStrings = rangeStrings;
            this.sourceRangeString = sourceRangeString;
            this.items = new ArrayList<>();
            this.isRangePicklist = isRangePicklist;
        }

        class PicklistItemTemp {
            int itemID;
            String displayString;
            String valueString;
            PicklistStyle style;

            PicklistItemTemp(int itemID, String displayString, String valueString, String textHex, String textTheme, String textTint, String bgHex, String bgTheme, String bgTint) {
                this.itemID = itemID;
                this.displayString = displayString;
                this.valueString = valueString;

                ZSColor textColor = ZSColor.getColor(textHex,textTheme,textTint);
                ZSColor bgColor = ZSColor.getColor(bgHex,bgTheme,bgTint);
                if(textColor != null || bgColor != null) {
                    this.style = new PicklistStyle(textColor, bgColor);
                }
                else {
                    this.style = null;
                }
            }
        }

        public void addItem(int itemID, String displayString, String valueString, String textHex, String textTheme, String textTinit, String bgColor, String bgTheme, String bgTint) {
            this.items.add(new PicklistItemTemp(itemID,displayString,valueString,textHex,textTheme,textTinit,bgColor,bgTheme,bgTint));
        }
    }



    @Override
    public void updatePicklist(String picklistID, String startsWith, String startsWithID, String rangeStrings, String sourceRangeString, String isRangePicklist) {
        PicklistTemp picklist = new PicklistTemp(Integer.parseInt(picklistID),startsWith != null ? Integer.parseInt(startsWith) : -1, startsWithID!=null ? Integer.parseInt(startsWithID) : -1,rangeStrings, sourceRangeString, isRangePicklist == null ? false : Boolean.valueOf(isRangePicklist));
        int count = picklistTempMap.size();
        picklistTempMap.put(count,picklist);
    }

    @Override
    public void endPicklists() {
        if(!workbook.getPicklistMap().isEmpty()) {
            /* endPicklists() will be called from both Content Validation tag and Picklists tag
             * The picklists should be added only once. */
            return;
        }
        for(Map.Entry<Integer,PicklistTemp> entry : this.picklistTempMap.entrySet()) {
            PicklistItem defaultItem = null;
            int index = entry.getKey();
            PicklistTemp tempPicklist = entry.getValue();
            List<PicklistItem> itemList = new ArrayList<>();
            for (int i = 0; i < tempPicklist.items.size(); i++) {
                PicklistTemp.PicklistItemTemp item = tempPicklist.items.get(i);
                String displayString = item.displayString;
                String valueString = item.valueString;
                int itemID = item.itemID;
                PicklistStyle style = item.style;
                PicklistItem picklistItem = new PicklistItem(itemID, Optional.ofNullable(displayString), Value.getInstance(valueString, SpreadsheetSettings.defaultSpreadsheetSettings), style);
                if(tempPicklist.defaultID != -1) {
                    if(tempPicklist.defaultID == itemID) {
                        defaultItem = picklistItem;
                    }
                }
                else if(tempPicklist.defaultIndex == i) {
                    defaultItem = picklistItem;
                }
                itemList.add(picklistItem);
            }
            if(defaultItem == null) {
                defaultItem = new PicklistItem(-1,Optional.empty(),Value.EMPTY_VALUE,null);
            }
            Picklist picklist = new Picklist(tempPicklist.picklistID, itemList, defaultItem, tempPicklist.isRangePicklist, false, false, null, false, Picklist.DropdownStyle.ALWAYS, Picklist.StackDirection.HORIZONTAL);
            workbook.addPicklist(picklist);
        }
    }

    @Override
    public void updateListItem(String itemID, String displayString, String valueString, String color, String textTheme, String textTint, String bgColor, String bgTheme, String bgTint) {
        int count = picklistTempMap.size();
        PicklistTemp picklistTemp = picklistTempMap.get(count-1);
        picklistTemp.addItem(Integer.parseInt(itemID),displayString,valueString,color,textTheme,textTint,bgColor,bgTheme,bgTint);
    }

    private class ContentValidationTemp
    {
       String condition;
       String baseCellAddress;
       String validationName;
       boolean isAllowEmptyCell;
       String displayList;
       DVHelpMessage helpMessage;
       DVErrorMessage errorMessage;
       ContentValidationTemp(String condition, String baseCellAddress, String validationName, boolean isAllowEmptyCell, String displayList, DVHelpMessage helpMessage, DVErrorMessage errorMessage)
       {
           this.condition = condition;
           this.baseCellAddress = baseCellAddress;
           this.validationName = validationName;
           this.isAllowEmptyCell = isAllowEmptyCell;
           this.displayList = (displayList != null && displayList.equalsIgnoreCase("SORTED-ASCENDING")) ? "SORT-ASCENDING" : displayList; //NO I18N
           this.helpMessage = helpMessage;  
           this.errorMessage = errorMessage;
       }
    }

    @Override
    public void updateContentValidation(String condition, String baseCellAddress, String validationName, boolean isAllowEmptyCell, String displayList, DVHelpMessage helpMessage, DVErrorMessage errorMessage)
    {
        ContentValidationTemp contentValidationTemp = new ContentValidationTemp(condition, baseCellAddress, validationName, isAllowEmptyCell, displayList, helpMessage, errorMessage);
        contentValidationMap.put(validationName, contentValidationTemp);
    }
    
    @Override
    public void putCellExpressionNameEntry(String expressionName, Cell cell){
        Sheet currSheet= this.workbook.getSheet(this.sheetIndex);
        Collection<Cell> cells= this.sheetExpressionNameRefferredCells.get(currSheet, expressionName);
        if(cells == null){
            cells= new ArrayList<>();
            this.sheetExpressionNameRefferredCells.put(currSheet, expressionName, cells);
        }
        cells.add(cell);
    }

    public void putCellToODSFormulaEntry(String formulaString, Cell cell) {
        this.cellToFormulaString.put(cell, formulaString);
    }
    
    @Override
    public void putExpressionNameExpressionEntry(String expressionName, String expressionString){
        Sheet currSheet= this.workbook.getSheet(this.sheetIndex);
        this.sheetExpressionNameToExpressionStringMap.put(currSheet, expressionName, expressionString);
    }

    public void createAndSetExpressionForCellsWithODSFormula() {
        for(Map.Entry<Cell, String> odsFormulaEntry: this.cellToFormulaString.entrySet()) {
            Cell cell = odsFormulaEntry.getKey();
            String odsFormula = odsFormulaEntry.getValue();
            Expression expression = this.workbook.getExpressionPool().getExpression(this.workbook, odsFormula, cell.getRowIndex(), cell.getColumnIndex(), false, true, !((CellImpl)cell).isArrayCell(), CellReference.ReferenceMode.A1);
            cell.setExpressionFromParser(expression, false, false);
        }
    }

    @Override
    public void createAndSetExpressionForReferredCells(){
        
        for(Sheet sheet: this.workbook.getSheetList()){
            Map<String, String> expressionNameExpressionStringMap= this.sheetExpressionNameToExpressionStringMap.row(sheet);
            Map<String, Collection<Cell>> expressionNameReferredCellsMap= this.sheetExpressionNameRefferredCells.row(sheet);
            
            for(Entry<String, String> expressionNameExpressionStringEntry: expressionNameExpressionStringMap.entrySet()){
                String formulaNameString= expressionNameExpressionStringEntry.getKey();
                String expressionString= expressionNameExpressionStringEntry.getValue();
                
                Expression expression = this.workbook.getExpressionPool().getExpression(this.workbook, expressionString, 0, 0, false, true, true, ReferenceMode.R1C1);
                Expression expressionForArrayCell = this.workbook.getExpressionPool().getExpression(this.workbook, expressionString, 0, 0, false, true, false, ReferenceMode.R1C1);
                Collection<Cell> referredCells= expressionNameReferredCellsMap.get(formulaNameString);
                
                if(referredCells!=null){
                    for(Cell referredCell: referredCells){
                        if(((CellImpl) referredCell).isArrayCell()){
                            referredCell.setExpressionFromParser(expressionForArrayCell, false, false);
                        }else {
                            referredCell.setExpressionFromParser(expression, false, false);
                        }
                        if(referredCell.getValue() instanceof PicklistValue) {
                            PicklistValue picklistValue = (PicklistValue) referredCell.getValue();
                            ((CellImpl)referredCell).setValueFromParser(Value.getInstance(picklistValue.getType(), picklistValue.getValue()));
                        }
                        referredCell.setExpressionFromParser(expression, false, false);
                    }
                }
            }
        }
    }

    private void convertNumberStylesToPattern() {
        Map<String, ZSPattern> patternCache = new HashMap<>();
        for(Map.Entry<String, NumberStyle> entry: this.numberStyleMap.entrySet()) {
            String numberStyleName = entry.getKey();
            if(!numberStyleName.contains("P")) {
                NumberStyle numberStyle = entry.getValue();
                ZSPattern pattern = numberStyle.getPattern(this.workbook);
                patternCache.put(numberStyleName, pattern);
            }
        }

        for(String cellStyleName : dataStyleNameToCellStyleMap.keySet())
        {
            CellStyle cellStyle = workbook.getCellStyle(cellStyleName);
            if(cellStyle != null) {
                String dataStyleName = dataStyleNameToCellStyleMap.get(cellStyleName);
                cellStyle.setProperty(CellStyle.Property.PATTERN, patternCache.get(dataStyleName));
            }
        }
    }

    
    /*
    public void setAnnotation(Annotation annotation)
    {
        Sheet sheet = workbook.getSheet(sheetIndex);
        Row row = sheet.getRow(cellRowIndex);
        Cell cell = row.getCell(cellColIndex);
        cell.setAnnotation(annotation);
        logger.info("Cell in Transformer : "+cell);
        logger.info("Annotation in Transformer : "+cell.getAnnotation());
        logger.info("...");
    }
     */

    @Override
    public void publishDefaultColumnWidth(String columnWidthStr) {
        this.workbook.setDefaultColumnWidthFromParser(Integer.valueOf(columnWidthStr));
    }

    @Override
    public void updatePlaceholder(String name, String baseCellAddress, String rangeAddress, String desc) {
        String placeholderName = name.substring(EngineConstants.PLACEHOLDER_NAME.length());
        this.workbook.addNewPlaceholder(placeholderName, true);
        if(desc != null) {
            /**
             * From parser, description should be set only if it's not null, as description attribute is written in
             * first placeholder tag, and not in later tags of the same placeholder.
             */
            this.workbook.setPlaceholderDescription(placeholderName, desc, true);
        }
        try {
            if(baseCellAddress != null && !baseCellAddress.isEmpty()) {
                Range range = new Range(this.workbook, rangeAddress, baseCellAddress, ReferenceMode.A1, true);
                this.workbook.addRangeForPlaceholder(placeholderName, range, true);
            }
        } catch (SheetEngineException e) {
            LOGGER.log(Level.SEVERE,e, () -> "Can't add cell : " + rangeAddress +", to placeHolder : "+placeholderName );
        }
    }

    @Override
    public void addSheetImage(int id, int imageID, double height, double width, int rowIndex, int columnIndex, double rowDiff, double columnDiff)
    {
        Sheet currSheet= this.workbook.getSheet(this.sheetIndex);
        if(id == -1)
        {
            currSheet.addSheetImage(imageID, height, width, rowIndex, columnIndex, rowDiff, columnDiff);
        }
        else
        {
            currSheet.addSheetImageWithID(id, imageID, height, width, rowIndex, columnIndex, rowDiff, columnDiff);
        }

        currSheet.addUsedImageId(imageID);
    }
    
    public void updatePolyLine(String id, String zIndex, String styleName, String textStyleName, String width, String height, String viewBox, String transform, String points) {
    	PolyLine polyLine = null;
    	
    	if(id == null) {
    		polyLine = (PolyLine) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.POLYLINE, zIndex, styleName, textStyleName);        	
    	}
    	else {
    		polyLine = (PolyLine) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.POLYLINE, id, zIndex, styleName, textStyleName);
    	}
    	polyLine.setWidth(width);
    	polyLine.setHeight(height);
    	polyLine.setViewBox(viewBox);
    	polyLine.setTransform(transform);
    	polyLine.setPoints(points);    	
    	addShape(polyLine);
	}
	
	public void updatePath(String id, String zIndex, String styleName, String textStyleName, String width, String height, String viewBox, String transform, String d) {
		Path path = null;
    	
    	if(id == null) {
    		path = (Path) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.PATH, zIndex, styleName, textStyleName);        	
    	}
    	else {
    		path = (Path) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.PATH, id, zIndex, styleName, textStyleName);
    	}
		path.setWidth(width);
    	path.setHeight(height);
    	path.setViewBox(viewBox);
    	path.setTransform(transform);
    	path.setD(d);    	
		addShape(path);
	}
	
	public void updateLine(String id, String zIndex, String styleName, String textStyleName, String x1, String x2, String y1, String y2) {
		Line line = null;
    	
    	if(id == null) {
    		line = (Line) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.LINE, zIndex, styleName, textStyleName);        	
    	}
    	else {
    		line = (Line) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.LINE, id, zIndex, styleName, textStyleName);
    	}
    	line.setX1(x1);
    	line.setY1(y1);
    	line.setX2(x2);
    	line.setY2(y2);		
		addShape(line);
	}
	
	public void updateCustomShape(String id, String endCellAddress, String endX, String endY, String zIndex, String name, String styleName, String textStyleName, String width, String height, String svgX, String svgY) {
		CustomShape customShape = null;
    	
    	if(id == null) {
    		customShape = (CustomShape) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.CUSTOMSHAPE, zIndex, styleName, textStyleName);        	
    	}
    	else {
    		customShape = (CustomShape) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.CUSTOMSHAPE, id, zIndex, styleName, textStyleName);
    	}
    	customShape.setWidth(width);
    	customShape.setHeight(height);
    	customShape.setName(name);
    	customShape.setEndCellAddress(endCellAddress);
    	customShape.setEndX(endX);
    	customShape.setEndY(endY);
		customShape.setSvgX(svgX);
		customShape.setSvgY(svgY);
        addShape(customShape);
	}
	
	public void updateEllipse(String id, String zIndex, String styleName, String textStyleName, String height, String width,
			String svgX, String svgY, String startAngle, String endAngle, String kind) {
		Ellipse ellipse = null;
    	
    	if(id == null) {
    		ellipse = (Ellipse) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.ELLIPSE, zIndex, styleName, textStyleName);        	
    	}
    	else {
    		ellipse = (Ellipse) workbook.getSheet(sheetIndex).createShape(Shape.ShapeType.ELLIPSE, id, zIndex, styleName, textStyleName);
    	}
    	ellipse.setWidth(width);
    	ellipse.setHeight(height);
    	ellipse.setStartAngle(startAngle);
    	ellipse.setEndAngle(endAngle);
    	ellipse.setKind(kind);
    	ellipse.setX(svgX);
    	ellipse.setY(svgY);		
		addShape(ellipse);
	}
  
  public void updateEnhancedGeometry(String viewBox, String textArea, String type, String modifiers, String enhancedPath, String gluePoints, String pathStretchPointX, String pathStretchPointY, String mirrorHorizontal, String mirrorVertical) {
      EnhancedGeometry enhancedGeometry = new EnhancedGeometry(viewBox, textArea, type, modifiers, enhancedPath, gluePoints, pathStretchPointX, pathStretchPointY, mirrorHorizontal, mirrorVertical);
      ((CustomShape) currentShape).setEnhancedGeometry(enhancedGeometry);
      
  }  

  public void updateEquation(Equation equation) {
	  ((CustomShape) currentShape).getEnhancedGeometry().addEquation(equation);	  
  }
  
  public void updateHandle(Handle handle) {
      ((CustomShape) currentShape).getEnhancedGeometry().addHandle(handle);      
  }
  
  public void updateTextInCustomShape(RichStringProperties text) {
	  currentShape.addText(text);
  }
  
  public void updateCustomShapeParagraph(String paragraphStyle) {
	  currentShape.setParagraphStyle(paragraphStyle);
  }
  
  public void updateAnchor(ShapeAnchor anchor) {
	  this.anchor = anchor;
  }
  
  public void updateGroup(ShapeGroup group) {
	  group.setAnchor(this.anchor);
	  this.anchor = null;
	  this.groups.add(group);
  }
  
  public void addShapeGroupToSheet() {
	  int index = groups.size()-1;
	  if(groups.size() == 1) {
		  workbook.getSheet(sheetIndex).addShapeGroup(groups.get(index));
	  }else {
		  groups.get(index-1).addGroup(groups.get(index));
	  }	  
	  groups.remove(index);
  }
  
  private void addShape(Shape shape) {
	  ShapeGroup group = new ShapeGroup();
	  group.setShape(shape);		
	  group.setAnchor(anchor);
	  this.anchor = null;
	  if(groups.isEmpty()) {		  
		  workbook.getSheet(sheetIndex).addShapeGroup(group);
      }
      else {
    	groups.get(groups.size()-1).addGroup(group);
      }	  
	  currentShape = shape;
  	}

    public void updateRowGroupStart()
    {
        startIndicesOfGroups.push(rowIndex);
    }

    public void updateColumnGroupStart()
    {
        startIndicesOfGroups.push(this.colHeaderIndex);
    }

    public void updateRowGroupEnd(boolean isCollapse)
    {
        int startIndex = startIndicesOfGroups.pop();
        int endIndex = rowIndex - 1;
        Sheet sheet = workbook.getSheet(sheetIndex);
        ActionUtil.groupRowsFromParser(sheet, startIndex, endIndex);
        if(isCollapse)
        {
            boolean isCollapsedSetToGroup = false;
            for(List<Group> groups : sheet.getRowGroups()) {
                for (Group group : groups) {
                    if (group.getStartIndex() == startIndex && group.getEndIndex() == endIndex) {
                        group.setCollapse(isCollapse);
                        isCollapsedSetToGroup = true;
                        break;
                    }
                }
                if(isCollapsedSetToGroup)
                {
                    break;
                }
            }
        }
    }

    public void updateColumnGroupEnd(boolean isCollapse)
    {
        int startIndex = startIndicesOfGroups.pop();
        int endIndex = colHeaderIndex - 1;
        Sheet sheet = workbook.getSheet(sheetIndex);
        ActionUtil.groupColsFromParser(sheet, startIndex, endIndex);
        if(isCollapse)
        {
            boolean isCollapsedSetToGroup = false;
            for(List<Group> groups : sheet.getColGroups()) {
                for (Group group : groups) {
                    if (group.getStartIndex() == startIndex && group.getEndIndex() == endIndex) {
                        group.setCollapse(isCollapse);
                        isCollapsedSetToGroup = true;
                        break;
                    }
                }
                if(isCollapsedSetToGroup)
                {
                    break;
                }
            }
        }
    }

    public void updateDataStyleNameToCellStyleMap(String cellStyleName, String dataStyleName)
    {
        this.dataStyleNameToCellStyleMap.put(cellStyleName, dataStyleName);
    }
}
