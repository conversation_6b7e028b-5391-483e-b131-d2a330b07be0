/* $Id$ */
package com.adventnet.zoho.websheet.model.parser;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.DataRange;

/**
 *
 * <AUTHOR>
 */

public class TSVRenderer extends ClipboardRenderer
{

    String lastCell = null;
    StringBuilder lastRow = null;

    public TSVRenderer(WorkbookContainer container, Sheet sheet, int startRow, int startCol, int endRow, int endCol,ImageBook imagebook)
    {
        super(container, sheet, startRow, startCol, endRow, endCol,imagebook);
        builder = new StringBuilder();
        lastCell = "";
        lastRow = new StringBuilder();
    }

    @Override
    public void startRow(Row row)
    {
        lastRow = new StringBuilder();
    }

    @Override
    public void endRow(boolean isLastRowInRange)
    {
        if(!isLastRowInRange)
        {
            lastRow.append("\n"); // No I18N
        }
        builder.append(lastRow);
    }

    @Override
    public void repeatLastRow(boolean isLastRowInRange)
    {
        builder.append(lastRow);
    }


    @Override
    protected int getReadOnlyCell(ReadOnlyCell rCell, int rowSpan, int colSpan,int rowHeight)
    {
        String cV = "";
        int colIndex = rCell.getColIndex();
        int colsRepeated = Math.min(rCell.getColsRepeated(), endCol - colIndex + 1);
        Cell cell = rCell.getCell();
        if(cell != null && cell.getContent() != null)
        {
            cV = cell.getContent();
            // Adding double quotes to the cell, If it contains multi lines - To achieve excel behaviour
            if(cV.contains("\n"))
            {
                if(cV.contains("\""))
                {
                    cV = cV.replaceAll("\"", "\"\"");
                }
                cV = "\"" + cV + "\"";
            }
            ////////////////////////
        }

        for(int q = 0; q < colsRepeated; q++) // Cols repeated
        {
            if(colIndex != startCol || q != 0)
            {
                lastRow.append("\t");// No I18N
            }
            lastRow.append(cV);
        }
        return colsRepeated;
    }

	@Override
	public void setHost(String host, boolean isDownloadOff, boolean isRemoteMode) {
//		if(!isSolidCopy){
//			builder.append("# This content is copied from ZohoSheet\n\n");//No I18N
//		}
	}

    @Override
    protected void generateTableStyleMap(Sheet sheet, DataRange range) { }
}
