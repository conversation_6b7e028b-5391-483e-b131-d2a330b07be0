/* $Id$ */
package com.adventnet.zoho.websheet.model.parser;

import com.adventnet.iam.xss.IAMEncoder;
import com.adventnet.zoho.websheet.dd.SHEETIMAGESDFSSTORE;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.image.Image;
import com.adventnet.zoho.websheet.model.pivot.PivotUtil;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ConditionalStyleResponse.CellObject;
import com.adventnet.zoho.websheet.model.style.ConditionalStyleResponse.ConditionalStyleCellStyles;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.util.*;
import com.google.common.collect.HashBasedTable;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.ImageUtils;
import com.zoho.sheet.util.RemoteUtils;
import org.apache.commons.codec.binary.Base64;

import java.io.InputStream;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */

public class HTMLRenderer extends ClipboardRenderer
{

	static final Logger LOGGER = Logger.getLogger(HTMLRenderer.class.getName());
    private static final String TD_BORDER = "1px solid #CCC";   //No I18N
    private static String borderColor = null;
    StringBuilder lastRow = new StringBuilder();
    Map<String, String> styles = new HashMap<>();
    Map<String, String> stylesAttr = new HashMap<>();
    boolean firstRow = true;
    Map<ConditionalStyleResponse.CellObject, ConditionalStyleResponse.ConditionalStyleCellStyles> validatedMapStyleMap = new HashMap<>();
    com.google.common.collect.Table<Integer,Integer,String> tableStyleMap;

    public HTMLRenderer(WorkbookContainer container, Sheet sheet, int startRow, int startCol, int endRow, int endCol,ImageBook imagebook)
    {
        super(container, sheet, startRow, startCol, endRow, endCol,imagebook);

        validatedMapStyleMap = ConditionalStyleResponse.getValidatedConditionalStyles(sheet, startRow, startCol, endRow, endCol, false);
    }

    private String getStyle(List<CellStyle> cellStyleList, Cell cell)
    {
        String consolidatedName = "";
        for(int i = cellStyleList.size() - 1 ; i >= 0; i--)
        {
            consolidatedName += cellStyleList.get(i).getStyleName();
        }

        boolean isPivotCell = cell != null && PivotUtil.isPivotCell(cell.getRow().getSheet(), cell.getRowIndex(), cell.getColumnIndex());
        if(!isPivotCell) {
            String existingStyle = styles.get(consolidatedName);
            if (existingStyle != null) {
                return existingStyle;
            }
        }

        Workbook workBook = sheet.getWorkbook();
        StringBuilder style = new StringBuilder();
        StringBuilder attr = new StringBuilder();
        String zindent = null;
        String zrotate = null;

        JSONObjectWrapper styleJSON = new JSONObjectWrapper();

        for(int i = cellStyleList.size() - 1 ; i >= 0; i--)
        {
            CellStyle cellStyle = cellStyleList.get(i);
            styleJSON.putOpt(HtmlCellConstants.BACKGROUNDCOLOR, cellStyle.getPropertyAsString_deep(CellStyle.Property.BACKGROUNDCOLOR, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.TEXTCOLOR, cellStyle.getPropertyAsString_deep(TextStyle.Property.COLOR, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.FONTWEIGHT, cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTWEIGHT, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.FONTSTYLE, cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTSTYLE, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.TEXTDECORATION, cellStyle.getPropertyAsString_deep(TextStyle.Property.TEXTUNDERLINESTYLE, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.FONTSIZE, cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTSIZE, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.TEXTALIGN, cellStyle.getPropertyAsString_deep(ParagraphStyle.Property.TEXTALIGN, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.WRAP, cellStyle.getPropertyAsString_deep(CellStyle.Property.DISPLAYTYPE, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.VALIGN, cellStyle.getPropertyAsString_deep(CellStyle.Property.VERTICALALIGN, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.STRIKETHROUGH, cellStyle.getPropertyAsString_deep(TextStyle.Property.TEXTLINETHROUGHSTYLE, sheet.getWorkbook()));
            styleJSON.putOpt(HtmlCellConstants.FONTFAMILY, cellStyle.getPropertyAsString_deep(TextStyle.Property.FONTNAME, sheet.getWorkbook()));
            zindent = Utility.masknull(cellStyle.getPropertyAsString_deep(ParagraphStyle.Property.ZSINDENT, sheet.getWorkbook()), zindent);
            zrotate = Utility.masknull(cellStyle.getPropertyAsString_deep(CellStyle.Property.ROTATIONANGLE, sheet.getWorkbook()), zrotate);
            FontFace tempFontFace = workBook.getFontFace(styleJSON.optString(HtmlCellConstants.FONTFAMILY));
            if(tempFontFace != null)
            {
                styleJSON.putOpt(HtmlCellConstants.FONTFAMILY, tempFontFace.getFontFamily());
            }
        }
        if(isPivotCell) {
            JSONObjectWrapper tempCell = new JSONObjectWrapper();
            tempCell.put(HtmlCellConstants.CELLSTYLE, styleJSON);
            styleJSON = PivotUtil.mergePivotStyle(styleJSON, cell.getRow().getSheet(), cell.getRowIndex(), cell.getColumnIndex()).optJSONObject(HtmlCellConstants.CELLSTYLE);
        }

        if(zindent != null){
            attr.append(" zindent=").append(zindent);//No I18N
        }
        if(zrotate != null){
            attr.append(" zrotate=").append(zrotate);//No I18N
        }
        if(styleJSON.has(HtmlCellConstants.WRAP)){
            attr.append(" zwrap=").append(styleJSON.get(HtmlCellConstants.WRAP));//No I18N
        }

        if(styleJSON.has(HtmlCellConstants.BACKGROUNDCOLOR))
        {
            style.append("background-color:").append(styleJSON.get(HtmlCellConstants.BACKGROUNDCOLOR)).append(";");    //No I18N
        }

        if(styleJSON.has(HtmlCellConstants.TEXTALIGN))
        {
            String hAlign = styleJSON.getString(HtmlCellConstants.TEXTALIGN);
            if("start".equals(hAlign))
            {
                hAlign = "left"; //No I18N
            }
            else if("end".equals(hAlign))
            {
                hAlign = "right"; //No I18N
            }
            styleJSON.put(HtmlCellConstants.TEXTALIGN, hAlign);
        }

        if(styleJSON.has(HtmlCellConstants.FONTFAMILY))
        {
            style.append("font-family:").append(IAMEncoder.encodeHTMLAttribute(styleJSON.getString(HtmlCellConstants.FONTFAMILY))).append(";");  //No I18N
        }

        if(styleJSON.has(HtmlCellConstants.FONTSIZE))
        {
            style.append("font-size:").append(styleJSON.getString(HtmlCellConstants.FONTSIZE)).append(";");    //No I18N
        }

        if(styleJSON.has(HtmlCellConstants.FONTWEIGHT))
        {
            style.append("font-weight:").append(styleJSON.getString(HtmlCellConstants.FONTWEIGHT)).append(";");    //No I18N
        }

        if(styleJSON.has(HtmlCellConstants.FONTSTYLE))
        {
            style.append("font-style:").append(IAMEncoder.encodeHTMLAttribute(styleJSON.getString(HtmlCellConstants.FONTSTYLE))).append(";");  //No I18N
        }

        String decoration = "";
        String textUnderLine = styleJSON.optString(HtmlCellConstants.TEXTDECORATION, null);
        if (textUnderLine != null && !textUnderLine.equals("none"))//No I18N
        {
            decoration += " underline";    //No I18N
        }
        String strikeThrough = styleJSON.optString(HtmlCellConstants.STRIKETHROUGH, null);
        if (strikeThrough != null && !strikeThrough.equals("none")) //No I18N
        {
            decoration += " line-through"; //No I18N
        }
        if(!decoration.isEmpty())
        {
            style.append("text-decoration:").append(decoration).append(";");//No I18N
        }

        if(CellStyleProperties.DisplayType.WRAP.getDisplayTypeValue().equals(styleJSON.optString(HtmlCellConstants.WRAP)))
        {
            style.append("word-wrap: break-word;"); //No I18N
        }
        else
        {
            style.append("word-wrap: normal;"); //No I18N
        }

        if(cell != null)
        {
            Cell.Type patternType = cell.getContentType();

            if(!styleJSON.has(HtmlCellConstants.TEXTALIGN) && cell.getType() != Cell.Type.STRING)
            {
                if(patternType == Cell.Type.STRING)
                {
                    styleJSON.put(HtmlCellConstants.TEXTALIGN, "left"); //No I18N
                }else if(cell.isImage() || patternType == Cell.Type.ERROR){
                    styleJSON.put(HtmlCellConstants.TEXTALIGN, "center"); //No I18N
                }
                else
                {
                    styleJSON.put(HtmlCellConstants.TEXTALIGN, "right");   //No I18N
                }
            }

            if(cell.getContentColor() != null) {
                styleJSON.put(HtmlCellConstants.TEXTCOLOR, cell.getContentColor());
            }
        }

        /* Hide/show gridlines and gridlines color handling */
        ZSColor sheetGridColor = workBook.getWorkbookSettings().getGridColor(sheet.getName());
        String sheetGridColorCode = "";
        if(sheetGridColor != null) {
            sheetGridColorCode = ZSColor.getHexColor(sheetGridColor, sheet.getWorkbook().getTheme());
        }



        String workbookGridColor = ActionUtil.getWorkbookGridColorHexCode(workBook);
        workbookGridColor = (workbookGridColor != null && !workbookGridColor.equalsIgnoreCase("")) ? workbookGridColor : EngineConstants.DEFAULT_GRID_COLOR;
        boolean addGridLines = workBook.getWorkbookSettings().isShowGrid(sheet.getName());
        String defaultGridColor = (sheetGridColorCode != null && !sheetGridColorCode.equalsIgnoreCase("")) ? sheetGridColorCode : workbookGridColor;
        if(addGridLines) {
            borderColor = "1px solid " +defaultGridColor; //NO I18N
        } else {
            borderColor = "1px solid transparent;";   //NO I18N
        }

        String bgColor = null;
        if (styleJSON.has(HtmlCellConstants.BACKGROUNDCOLOR)) {
            if (!styleJSON.getString(HtmlCellConstants.BACKGROUNDCOLOR).equalsIgnoreCase("transparent")) {
                bgColor = styleJSON.getString(HtmlCellConstants.BACKGROUNDCOLOR);
            }
        }


        CellStyle defaultCellStyle = workBook.getDefaultCellStyle();
        ZSColor fColor = ((ZSColor) defaultCellStyle.getProperty(TextStyle.Property.COLOR));
        String fontColor = ZSColor.getHexColor(fColor, workBook.getTheme());

        if (styleJSON.has(HtmlCellConstants.TEXTCOLOR)) {
            fontColor = styleJSON.getString(HtmlCellConstants.TEXTCOLOR);
        }


        if(EngineConstants.TEXTCOLOR_AUTOMATIC.equals(fontColor)) {
            bgColor = (bgColor != null) ? bgColor : "#FFFFFF"; //No I18N
            ZSColor color = ZSColor.getAdjustedTextColor(bgColor);
            fontColor =  ZSColor.getHexColor(color, workBook.getTheme());
        }

        if(styleJSON.has(HtmlCellConstants.TEXTCOLOR))
        {
            style.append("color:").append(fontColor).append(";");   //No I18N
        }

        if(styleJSON.has(HtmlCellConstants.TEXTALIGN))
        {
            style.append("text-align:").append(styleJSON.getString(HtmlCellConstants.TEXTALIGN)).append(";"); //No I18N
        }

        String vAlign = styleJSON.optString(HtmlCellConstants.VALIGN, null);
        if(vAlign == null || "automatic".equals(vAlign))
        {
            vAlign = "top";//No internationalization
        }
        style.append("vertical-align:").append(vAlign).append(";"); //No I18N

        String cStyle = style.toString();
        String cAttr = attr.toString();
        if(!isPivotCell) {
            styles.put(consolidatedName, cStyle);
            stylesAttr.put(consolidatedName, cAttr);
        }
        return cStyle;
    }

      private String getStyleAttr(List<CellStyle> cellStyleList)
    {
        String consolidatedName = "";
        for(int i = cellStyleList.size() - 1 ; i >= 0; i--)
        {
            consolidatedName += cellStyleList.get(i).getStyleName();
        }
        String existingStyle = stylesAttr.get(consolidatedName);
        return existingStyle;
    }

    @Override
    public void startRow(Row row)
    {
        lastRow = new StringBuilder();
        String hs = "";
        if(row != null)
        {
            int rowHeight = RowUtil.getRowHeight(row);

            hs = " height=\"" + rowHeight + "\"";  //No I18N
        }
        lastRow.append("<tr").append(hs).append(">");       //No I18N
    }

    public StringBuilder getCellImage(ImageValue imageValue, double cellWidth,double cellHeight)
    {
        StringBuilder imageHTML = new StringBuilder();
        String data = getImageData(imageValue.getImageID());
        double actualHeight = imageValue.getImageHeight();
        double actualWidth = imageValue.getImageWidth();
        String objectFitProperty="";
        if(imageValue.getMode().equals(ImageDisplayMode.FIT))
        {
            objectFitProperty = "scale-down";   // No I18N
        }
        else if(imageValue.getMode().equals(ImageDisplayMode.STRETCH))
        {
            objectFitProperty = "fill";   // No I18N
        }
        else if(imageValue.getMode().equals(ImageDisplayMode.ORIGINAL))
        {
            objectFitProperty = "fill";   // No I18N
        }

        if(data!= null)
        {
            String divHTML = "<div style ='height:"+cellHeight+"px;display:inline-block'>";
            imageHTML.append(divHTML);
            String innerImageHTML = imageValue.getMode().equals(ImageDisplayMode.CUSTOM) ? "<img width:"+actualWidth+";height:"+actualHeight+" src="+data+" />" : "<img style='width:inherit;height:inherit;object-fit:"+objectFitProperty+"' src="+data+" />";
            imageHTML.append(innerImageHTML);
            imageHTML.append("</div>");
        }

        return imageHTML;
    }
    private String getImageData(int imageID)
    {
        Image image = getImagebook().getImage(imageID);
        boolean isRemote = container.isRemoteMode();
        String docID = container.getDocId();
        String docOwner = container.getDocOwner();
        String imageUrl = image.getUrl(isRemote, docID);
        boolean isFromUrl = !image.isUploadedImage();
        String imageData = null;

        if(isFromUrl)
        {
            imageData = URLDecoder.decode(imageUrl);
        }
        else
        {
            try
            {
                com.adventnet.persistence.Row imageRow = ImageUtils.getImageRow(docID, docOwner, image.getUniqueKey());
                if(imageRow != null)
                {
                    Long imgDFSID = (Long) imageRow.get(SHEETIMAGESDFSSTORE.IMAGES_DFSSTORE_ID);
                    String imageResourceID = imageRow.get(SHEETIMAGESDFSSTORE.IMG_RID) != null ? imageRow.get(SHEETIMAGESDFSSTORE.IMG_RID).toString() : null;
                    String imgExtn = imageRow.get(SHEETIMAGESDFSSTORE.FILE_EXTN).toString();
                    ZSStore.FileExtn extn = ImageUtils.getImageExtn(imgExtn);
                    InputStream imageInputStream = ImageUtils.getImageInputStream(container, imgDFSID, imageResourceID, imageUrl, extn, this.totalImageSize);
                    if(imageInputStream == null)
                    {
                        LOGGER.log(Level.WARNING, "[HTML][IMAGES][Exception] InputStream for Image {0} is null", image.toString());
                        throw new Exception("[HTML][IMAGES][Exception] InputStream for Uploaded Image with Unique Key "+ image.getUniqueKey() +" is null");
                    }
                    byte[] imageBytes = ImageUtils.getImageByteArray(imageInputStream);
                    String imageStr = Base64.encodeBase64String(imageBytes);
                    imageData = "data:image/png;base64," + imageStr; //No I18N
                }
            }
            catch(Exception e)
            {
                LOGGER.log(Level.WARNING, "[HTML][IMAGES][Exception] Exception while generating image bytes. Skipping image generation for image id "+imageID, e);
            }
        }
        return imageData;
    }
    private String getUniqueIDFromUrl(String imageUrl){
        String un = "";
        int startIndex = imageUrl.indexOf("u_n") + 4;
        int endIndex = imageUrl.indexOf("&",startIndex);
        un = imageUrl.substring(startIndex, endIndex);
        return un;
    }

    @Override
    public int getReadOnlyCell(ReadOnlyCell rCell, int rowSpan, int colSpan,int rowHeight)
    {

        int colIndex = rCell.getColIndex();
        int colsRepeated = Math.min(rCell.getColsRepeated(), endCol - colIndex + 1);

        if(colsRepeated > 1)
        {
            colsRepeated = Math.min(colsRepeated, sheet.getReadOnlyColumnHeader(colIndex).getColsRepeated());
        }

        String cellContent = "";
        StringBuilder imageContent = new StringBuilder();

        Cell cell = rCell.getCell();

        if(cell != null && (sheet.isCoveredUnderMerge(cell.getRowIndex(), cell.getColumnIndex()))){
            return colsRepeated;
        }

        int width = sheet.getWorkbook().getDefaultColumnWidth();
        CellStyle cs = null;
        ColumnHeader ch = sheet.getReadOnlyColumnHeader(colIndex).getColumnHeader();
        if(ch != null)
        {
            cs = ch.getCellStyleReadOnly();
            width = ch.getColumnWidth();
        }

        StringBuilder lastCell = new StringBuilder();
        lastCell.append("<td"); //No I18N
        lastCell.append(" width=\"").append(width).append("\"");    //No I18N

        if(cell != null)
        {

            if(cell.getStyleName() != null)
            {
                cs = ((CellImpl) cell).getCellStyleReadOnly();
            }

            if(colSpan > 1)
            {
                lastCell.append(" colspan=\"").append(colSpan).append("\"");    //No I18N
            }

            if(rowSpan > 1)
            {
                lastCell.append(" rowspan=\"").append(rowSpan).append("\"");    //No I18N
            }
            if(cell.getContent() != null)
            {
                if(cell.getLinks()!=null && !cell.getLinks().isEmpty()){
                    cellContent= EngineUtils.getLink(cell);
                }
                else{
                    cellContent = cell.getContent();
                    cellContent = IAMEncoder.encodeHTML(cellContent);
                }
                if (cellContent != null) {
                	cellContent = cellContent.replaceAll("\\n", "<br>"); //No I18N
                }
            }

            if(cell.isImage()){
                ImageValue imageValue = (ImageValue)cell.getValue();
                double imageHeigth = imageValue.getImageHeight();
                double imageWidth = imageValue.getImageWidth();
                if(imageValue.getMode().equals(ImageDisplayMode.FIT))
                {
                    imageHeigth = rowHeight;
                    imageWidth = width;
                }
                imageContent = getCellImage(imageValue,imageWidth,imageHeigth);
                if(cellContent != ""){
                    cellContent = "";
                }
            }
        }

        CellObject cellObject = new ConditionalStyleResponse.CellObject(rCell.getRowIndex(), colIndex);
        ConditionalStyleCellStyles csCellStyles = validatedMapStyleMap.get(cellObject);
        CellStyle nonCSCellStyle = cell!=null ? cell.getCellStyle() : null;

        List<CellStyle> cellStyles = new ArrayList<>();

        if(csCellStyles != null)
        {
            for(CellStyleHolder csHolder : csCellStyles.getCellStylesHolder())
            {
                cellStyles.add(csHolder.getCellStyle());
            }
        }

        String tableStyleName = this.tableStyleMap.get(rCell.getRowIndex(), rCell.getColIndex());
        if(nonCSCellStyle!=null && tableStyleName == null){
            cellStyles.add(nonCSCellStyle);
        }
        else if(tableStyleName != null) {
            CellStyle tableStyle = sheet.getWorkbook().getCellStyle(tableStyleName);
            if(tableStyle != null) {
                cellStyles.add(tableStyle);
            }
        }

        String styleDef = getStyle(cellStyles, cell);
        String styleAttr = getStyleAttr(cellStyles);

       String _borderStyle = cell!=null ? getExistingBorderStyle(cell) : null;

       if(_borderStyle == null) {
    	   _borderStyle = "border:"+borderColor; //No I18N
       }

       lastCell.append(styleAttr);
       lastCell.append(" style=\"").append(styleDef).append(_borderStyle).append("\"");   //No I18N

        if("".equals(cellContent))
        {
            cellContent = "&nbsp;"; //No I18N
        }
        lastCell.append(">").append(imageContent).append(cellContent).append("</td>");

        String lastCellHtml = lastCell.toString();
        String readOnlyCell = lastCellHtml;

        if(cell != null)// && !cell.isCoveredCell())
        {
            for(int q = 1; q < colsRepeated; q++) // Cols repeated
            {
                int repeatedCellWidth = sheet.getColumnHeader(colIndex+q).getColumnWidth();
                readOnlyCell += (repeatedCellWidth == width) ? lastCellHtml : lastCellHtml.replaceFirst("width=\""+width, "width=\""+repeatedCellWidth);    //No I18N
            }
        }
        lastRow.append(readOnlyCell);
        return colsRepeated;
    }

    public static String getExistingBorderStyle(Cell cell) {
    	try {
	    	CellStyle _readOnlyCellStyle = ((CellImpl)cell).getCellStyleReadOnly();
	    	Workbook workbook = cell.getRow().getSheet().getWorkbook();
	    	JSONObjectWrapper pivotStyle = PivotUtil.mergePivotStyle(new JSONObjectWrapper(), ((CellImpl) cell).getSheet(), cell.getRowIndex(), cell.getColumnIndex()).optJSONObject(HtmlCellConstants.CELLSTYLE);
	    	if(_readOnlyCellStyle != null || pivotStyle != null) {
                JSONObjectWrapper borderJobj = new JSONObjectWrapper();
                String _botttom = "b";  //No I18N
                String _top     = "t";  //No I18N
                String _left    = "l";  //No I18N
                String _right   = "r";  //No I18N
                String borderBottom;
                String borderTop;
                String borderLeft;
                String borderRight;
                if(_readOnlyCellStyle != null)
                {
                    borderBottom = Utility.masknull(_readOnlyCellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERBOTTOM, workbook), pivotStyle != null ? pivotStyle.optString(HtmlCellConstants.BORDERBOTTOM) : null);
                    borderTop = Utility.masknull(RemoteUtils.maskNull(_readOnlyCellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERTOP, workbook)),  pivotStyle != null ? pivotStyle.optString(HtmlCellConstants.BORDERTOP) : null);
                    borderLeft = Utility.masknull(RemoteUtils.maskNull(_readOnlyCellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERLEFT, workbook)),  pivotStyle != null ? pivotStyle.optString(HtmlCellConstants.BORDERLEFT) : null);
                    borderRight = Utility.masknull(RemoteUtils.maskNull(_readOnlyCellStyle.getPropertyAsString_deep(CellStyle.Property.BORDERRIGHT, workbook)),  pivotStyle != null ? pivotStyle.optString(HtmlCellConstants.BORDERRIGHT) : null);
                }else {
                    borderBottom = pivotStyle.optString(HtmlCellConstants.BORDERBOTTOM);
                    borderTop = pivotStyle.optString(HtmlCellConstants.BORDERTOP);
                    borderLeft = pivotStyle.optString(HtmlCellConstants.BORDERLEFT);
                    borderRight = pivotStyle.optString(HtmlCellConstants.BORDERRIGHT);
                }
		    	borderJobj.put(_botttom, borderBottom);
		    	borderJobj.put(_left, borderLeft);
		    	borderJobj.put(_right, borderRight);
		    	borderJobj.put(_top, borderTop);
		    	String _borderStyleString = null;
		    	if(borderJobj.has(_botttom)) {
		    		String _tempStyleString = "border-bottom:"+borderJobj.getString(_botttom)+";";  //No I18N
		    		_borderStyleString = _borderStyleString == null ?_tempStyleString : _borderStyleString.concat(_tempStyleString);
		    	}
		    	else {
		    		String _tempStyleString = "border-bottom:"+borderColor+";";  //No I18N
		    		_borderStyleString = _borderStyleString == null ?_tempStyleString : _borderStyleString.concat(_tempStyleString);
		    	}

		    	if(borderJobj.has(_left)) {
					String _tempStyleString = "border-left:"+borderJobj.getString(_left)+";";  //No I18N
		    		_borderStyleString = _borderStyleString == null ?_tempStyleString : _borderStyleString.concat(_tempStyleString);
				}
				else {
		    		String _tempStyleString = "border-left:"+borderColor+";";  //No I18N
		    		_borderStyleString = _borderStyleString == null ?_tempStyleString : _borderStyleString.concat(_tempStyleString);
		    	}

				if(borderJobj.has(_right)) {
					String _tempStyleString = "border-right:"+borderJobj.getString(_right)+";";  //No I18N
		    		_borderStyleString = _borderStyleString == null ?_tempStyleString : _borderStyleString.concat(_tempStyleString);
				}
				else {
		    		String _tempStyleString = "border-right:"+borderColor+";"; //No I18N
		    		_borderStyleString = _borderStyleString == null ?_tempStyleString : _borderStyleString.concat(_tempStyleString);
		    	}

				if(borderJobj.has(_top)) {
					String _tempStyleString = "border-top:"+borderJobj.getString(_top)+";";  //No I18N
		    		_borderStyleString = _borderStyleString == null ?_tempStyleString : _borderStyleString.concat(_tempStyleString);
				}
				else {
		    		String _tempStyleString = "border-top:"+borderColor+";";  //No I18N
		    		_borderStyleString = _borderStyleString == null ?_tempStyleString : _borderStyleString.concat(_tempStyleString);
		    	}
		    	return _borderStyleString;
	    	}
    	}
    	catch(Exception e) {
    	 LOGGER.log(Level.INFO, "Getting cell Style caught Exception {0}",e);
    	}
    	return null;
    }

    @Override
    public void endRow(boolean isLastRowInRange)
    {
        lastRow.append("</tr>");
        builder.append(lastRow);
        firstRow = false;
    }

    @Override
    public void repeatLastRow(boolean isLastRowInRange)
    {
        builder.append(lastRow);
    }

    @Override
    public String getString()
    {
        builder.append("</table>");
        return builder.toString();
    }

	@Override
	public void setHost(String host, boolean isDownloadOff, boolean isRemoteMode) {
		String sourceAttribute = null;
    	if(isDownloadOff) {
    		sourceAttribute = EnginePropertyUtil.getSheetPropertyValue("NO_DOWNLOAD_COPY_SOURCE");//No I18N
    	}else {
            if(isRemoteMode){
                String rmName = "";
                UserProfile userProfile = CurrentRealm.getUserProfile();
                if(userProfile!=null && userProfile.getZUserId()!=null){
                    rmName = userProfile.getZUserId();
                }
                sourceAttribute = EnginePropertyUtil.getSheetPropertyValue("CopySource")+" "+EnginePropertyUtil.getSheetPropertyValue("CopyRemoteSource")+"_"+rmName+" "+EnginePropertyUtil.getSheetPropertyValue("CopyRangeSource");//No I18N
            }else{
                sourceAttribute = EnginePropertyUtil.getSheetPropertyValue("CopySource")+" "+EnginePropertyUtil.getSheetPropertyValue("CopyRangeSource");//No I18N
            }
    	}

		builder.append("<table source=\""+sourceAttribute+"\" cellspacing=\"0\" cellpadding=\"2\" style=\"font-size:13px;table-layout:fixed;border-collapse:collapse;\"").append(">");

		if(isDownloadOff) {
			builder.append("<tr><td></td></tr>");

		}
	}

    @Override
    protected void generateTableStyleMap(Sheet sheet, DataRange range) {
        try {
            this.tableStyleMap = HashBasedTable.create();
            TableUtil.generateTableStyleMap(this.tableStyleMap, sheet, range, false, true);
        }
        catch (Exception e) {
            LOGGER.log(Level.INFO, "[HTMLRender] Exception while generating table style {0}",e);
            this.tableStyleMap = HashBasedTable.create();
        }
    }
}
