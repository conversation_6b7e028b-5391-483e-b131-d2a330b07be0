/* $Id$ */
package com.adventnet.zoho.websheet.model.parser;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.ConditionalFormatEntry.Entry_Type;
import com.adventnet.zoho.websheet.model.exception.AbortParseError;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.filter.*;
import com.adventnet.zoho.websheet.model.filter.operator.FilterLogicalOperator;
import com.adventnet.zoho.websheet.model.filter.operator.FilterOperator;
import com.adventnet.zoho.websheet.model.pivot.*;
import com.adventnet.zoho.websheet.model.shapes.Equation;
import com.adventnet.zoho.websheet.model.shapes.Handle;
import com.adventnet.zoho.websheet.model.shapes.ShapeAnchor;
import com.adventnet.zoho.websheet.model.shapes.ShapeGroup;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSFontScheme;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.model.zs.ZSZipInputStream;
import com.adventnet.zoho.websheet.store.Store;
import com.zoho.sas.container.AppResources;
import com.zoho.sheet.parse.sxc.SxcEventListener;
import com.zoho.sheet.util.ZSLogger;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;
import org.xmlpull.v1.util.XmlPullUtil;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static com.adventnet.zoho.websheet.model.pivot.PivotUtil.generatePivotComponent;
import static com.adventnet.zoho.websheet.model.zs.ZSModelConstants.*;


/**
 * Parser to extract details of a complete workbook from content.xml of a .sxc file.
 * On reaching important nodes, events will be published to the registered SXCEvent listener
 *
 * @see SxcEventListener
 */
public class ODSWorkbookParser implements Names
{
    public static Logger logger = Logger.getLogger(ODSWorkbookParser.class.getName());
    //private static NodeProcessorFactory npFactory = new NodeProcessorFactory();
    private NodeProcessorFactory npFactory = null;
    protected XmlPullParser xpp = null;
    private ODSEventListener listener = null;
    protected boolean stopped = false;
    private boolean insideAnnotate = false;
    private boolean dataOnly = false; // if true, only data-content will be extracted
    private boolean isNameSpaceAware = true;
    private StringBuilder commentBuff = new StringBuilder();
    private StringBuilder textBuff = new StringBuilder();
    
    private boolean isPatternsParsed = false;
    private final Map<String, ZSPattern> patternMap = new HashMap<>();

    protected Style style = null;
    
    private List<MapStyleTemp> mapStyles = new ArrayList<>();
    
    
    private NumberStyle numberStyle = null;
    private NumberElement numberElement = null;

    //////// Added by ganesh
    //for forms
    private Forms forms = new Forms();
    private Form form;
    private Form formElement;
    private boolean isForm = false;
    private List<DrawControl> drawControlList = null;
    private List<DrawControl> drawControlShapeList = null;
    private String csShowValue = null;
    private String iconSetName = null;
    private Integer iconId = null;
    private boolean isSheetScopeShape = false;
    //end of forms
    
    private PivotTable pivotTable = null;
    private PivotField pivotField = null;

    //ZSTHEME
    private ZSColorScheme colorScheme;
    private ZSFontScheme fontScheme;

    // for filters
    private Filter filter = null;

    private Annotation annotation = null;

    private Collection<Attachment> attachments = new LinkedList();
   // private Boolean isValidContent=null;
    
    
    private DVHelpMessage helpMessage = null;
    private DVErrorMessage errorMessage = null;

    // This variable identifies if the current file parsed is syles.xml
    // This is used only while reading the attrinute isStyleVolatile of NumberStyle.
    // If the isStyleAttribute is not specified, should take the default value as FALSE if
    // the number style is present in Content.xml and true if it is in Styles.xml.
    private boolean isInsideStylesTag = false;
//    boolean isChartContent = false;
    //private String prefix = null;
    //////////

    //private Sheet sheet;

    //protected String styleFamily = "";

    //private String parentName = null;//used in processnumbernode() method
    //private int sheetIndex = 0;
    //private int rowIndex = 0;
    //private int colIndex = 0;
    private List<RichStringProperties> linksInValue = new ArrayList();
    //private Image image;
    /////////////////////
    //private int nRowsRepeated = 1;

    ////////////////////
    boolean isTextNodeExist = false;
    
    boolean isShapeNode = false;
    
    Frame frame;
    private long stringSize = 0;
    private  int formulasCount = 0;
    public ODSWorkbookParser(ODSEventListener listener) throws XmlPullParserException
    {
        this(listener,false, true, NodeProcessorFactory.npFactory);
    }

    public ODSWorkbookParser(ODSEventListener listener, boolean dataOnly) throws XmlPullParserException
    {
        this(listener,dataOnly, true, NodeProcessorFactory.npFactory);
    }

    ODSWorkbookParser(ODSEventListener listener, boolean dataOnly, boolean isNameSpaceAware, NodeProcessorFactory npFactory) throws XmlPullParserException
    {
    this.npFactory = npFactory;
    XmlPullParserFactory factory = XmlPullParserFactory.newInstance(AppResources.getProperty(XmlPullParserFactory.PROPERTY_NAME), null);
        factory.setNamespaceAware(isNameSpaceAware);
        xpp = factory.newPullParser();
        this.listener = (listener != null)?listener:(new DummyTransformer());
        this.dataOnly = dataOnly;
    this.isNameSpaceAware = isNameSpaceAware;
    }

    ////    util methods -> START   ////
    String getAttribute(XmlName node)
    {
        return xpp.getAttributeValue(node.namespace, node.lName);
    }
    
    String getText(){
        return xpp.getText();
    }


    void assertStartTag(XmlName node) throws IOException, XmlPullParserException
    {
        xpp.require(XmlPullParser.START_TAG, node.namespace, node.lName);
    }

    void assertEndTag(XmlName node) throws IOException, XmlPullParserException
    {
        xpp.require(XmlPullParser.END_TAG, node.namespace, node.lName);
    }

    void assertEndTag() throws IOException, XmlPullParserException
    {
        xpp.require(XmlPullParser.END_TAG, null, null);
    }
    ////    util methods -> END   ////

    public boolean isNameSpaceAware()
    {
    return isNameSpaceAware;
    }

    /** place the stream cursor at content.xml */
    private InputStream getInputStream(InputStream stream, String fileName) throws IOException
    {
    if(fileName == null)
    {
        fileName = "content.xml";
    }
        ZSZipInputStream zin = new ZSZipInputStream(stream);
        ZipEntry zEntry;
        while((zEntry = zin.getNextEntry()) != null)
        {
            if(fileName.equals(zEntry.getName()))
            {
                return zin;
            }
        }
        return null;
    }

    /**
     * @param container
     * @param store
     * @param resourceId
     * @param fileExtension
     * @throws Exception
     */
   // public void parse(WorkbookContainer container, long resourceId) throws Exception
     public void parse(WorkbookContainer container, Store store, long resourceId, ZSStore.FileExtn fileExtension) throws Exception
    {
        logger.log(Level.INFO, "parse called: filePath::documentId {0}", resourceId);
        InputStream stream = null;
        ZSZipInputStream zipInputStream = null;
        try
        {
            long t0 = System.currentTimeMillis();

            stream = container.getReadStream(store, resourceId, null, fileExtension, null); //It may be version doc or normal ODS file. ~Mani.
            String fileToParse = "styles.xml"; //No I18N
            zipInputStream = new ZSZipInputStream(stream);
            List<String> traversedEntries = StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, fileToParse);
            if (!traversedEntries.isEmpty() && (fileToParse).equals(traversedEntries.get(traversedEntries.size() - 1))) {
                parseStream(zipInputStream);
            } else {
                logger.log(Level.WARNING, "️[READ-FORMAT-INKLING][ODS]  traversedEntries : {0}", new Object[]{traversedEntries});
                throw new Exception("Entry not available in stream for sheet: " + fileToParse);
            }

            long t1 = System.currentTimeMillis();

            fileToParse = "content.xml"; //No I18N
            // If already traversed, get the stream again and traverse from top
            if (traversedEntries.contains(fileToParse)) {
                StreamUtil.close(zipInputStream);
                StreamUtil.close(stream);
                stream = container.getReadStream(store, resourceId, null, fileExtension, null); //It may be version doc or normal ODS file. ~Mani.
                zipInputStream = new ZSZipInputStream(stream);
                traversedEntries = new ArrayList();
            }
            traversedEntries.addAll(StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, fileToParse));
            if (!traversedEntries.isEmpty() && fileToParse.equals(traversedEntries.get(traversedEntries.size() - 1))) {
                parseStream(zipInputStream);
            } else {
                logger.log(Level.WARNING, "️[READ-FORMAT-INKLING][ODS]  traversedEntries : {0}", new Object[]{traversedEntries});
                throw new Exception("Entry not available in stream for sheet: " + fileToParse);
            }

            long t2 = System.currentTimeMillis();

            ZSLogger.log(logger, Level.INFO, "RESOURCE_KEY: {0} >>> Time taken in ODS File parser \n\tstyles: {1}\n\tcontent: {2}",
                new Object[]{
                    container.getResourceKey(),
                    (t1 - t0),
                    (t2 - t1)
                },
                new ZSLogger.ZSCustomAppLogFieldKey[]{
                    ZSLogger.ZSCustomAppLogFieldKey.TIME_TAKEN
                },
                new Object[] {
                    (t2 - t0)
                });

            }
        catch(Exception e)
        {
//    	    System.out.println("Engine: Exception while parsing ODS File.");
            e.printStackTrace();
            throw (new Exception("Document.Parse.Error", e));
        }
        finally
        {
            try
            {
                if(zipInputStream != null) {
                    StreamUtil.close(zipInputStream);
                }
                if(stream != null) {
                    StreamUtil.close(stream);
                }
            }
            catch(Exception e1)
            {
            e1.printStackTrace();
            }
        }

    }

    // used as a API
    public void parse(InputStream stream, String fileName) throws Exception
    {
        InputStream content = getInputStream(stream, fileName);

        try
        {
            xpp.setInput(content, "UTF-8");
            xpp.next(); // go to the start_tag of the root-node 'office:document-content'
            NodeProcessor node = npFactory.getInstance(xpp.getPrefix(), xpp.getName());
            node.processNode(this);
        }
        catch(Exception e)
        {
            logger.log(Level.WARNING, "Engine: Exception while parsing File from parse() method.", e);
            throw (new Exception("Document.Parse.Error", e));
        }
        finally
        {
            try
            {
                if(content != null)
                {
                    content.close();
                }
                if(stream != null)
                {
                    stream.close();
                }
            }
            catch(IOException exp)
            {
                logger.log(Level.WARNING, null, exp);
            }
        }
    }

    // used as a API used from EngineUtils
    // to parse only style.xml like
    public void parseStream(InputStream stream) throws Exception
    {

        try
        {
            xpp.setInput(stream, "UTF-8");
            xpp.next(); // go to the start_tag of the root-node 'office:document-content'
            NodeProcessor node = npFactory.getInstance(xpp.getPrefix(), xpp.getName());
            node.processNode(this);
        }
    catch(Exception e)
    {
        logger.log(Level.WARNING,"Engine: Exception while parsing File from parseStream() method.",e);
        throw (new Exception("Document.Parse.Error", e));
    }
    }

    // used as a API
    // sheetIndex -1 to parse full file
    // filePath is a ods file path
   // OLD FileStore Method ~Mani
    // public void parse(FileStore store, String filePath) throws Exception
   /* SAS18_IMPL FS comment ~Mani
    * public void parse(WorkbookContainer container, String filePath) throws Exception
    {
    InputStream stream = null;
    try
    {
        String fileToParse = "styles.xml"; //No I18N
            isFromStylesXML = true;
        stream = container.getReadStream(filePath);
        parse(stream, fileToParse);
        stream.close();

        fileToParse = "content.xml"; //No I18N
            isFromStylesXML = false;
        stream = container.getReadStream(filePath);
        parse(stream, fileToParse);
        stream.close();
    }
    catch(Exception e)
    {
        logger.log(Level.WARNING,"Engine: Exception while parsing ODS File.",e);
        throw (new Exception("Document.Parse.Error", e));
    }
    finally
    {
        try
        {
        if(stream != null)
        {
            stream.close();
        }
        }
        catch(Exception e1)
        {
            logger.log(Level.WARNING,null,e1);
        }
    }

    }*/


    ////////////////////////////////////////////////////////////////////////////////
    // For LOCAL usage only
    public void parse(String fileName) throws Exception
    {
        File file = new File(fileName);
        if(file.exists() && file.isFile())
        {
        parse(fileName, "styles.xml"); //No I18N
            parse(fileName, "content.xml"); //No I18N


//            try
//            {
//                for(int i = 1; ; i++)
//                {
//                    isChartContent = true;
//                    parse(fileName, "Object "+i+"/content.xml");
//                }
//            }catch(IllegalArgumentException e)
//            {
//                logger.info(e.getMessage());
//            }
//            isChartContent = false;
        }
        else
        {
            logger.log(Level.INFO, "The specified file not found : {0}", fileName);
        }
//        File file = new File(fileName);
//        if(file.exists() && file.isFile())
//        {
//            InputStream is = new FileInputStream(fileName);
//            parse(is);
//        }
//        else
//        {
//            logger.info("The specified file not found : "+fileName);
//        }
    }

    private void parse(String odsFileName, String fileName)throws Exception
    {

//        if(fileName.equals("Object 1/content.xml"))
//        {
//            isChartContent = true;
//        }
//        else
//        {
//            isChartContent = false;
//        }

        File file = new File(odsFileName);

        InputStream is = new FileInputStream(odsFileName);
        parse(is, fileName);

    }
    // For LOCAL usage only ends
    ////////////////////////////////////////////////////////////////////////////////

    /** to stop parsing */
    public void stop()
    {
        stopped = true;
    }

    void traverseNode(XmlName nodeName) throws IOException, XmlPullParserException
    {
        assertStartTag(nodeName);
        while (!stopped)
        {
        	int eventType = xpp.next();
        	if (eventType == XmlPullParser.END_TAG)
        	{
        		if(!isNameSpaceAware && (nodeName.prefix+":"+nodeName.lName).equals(xpp.getName()))
        		{
        			break;
        		}
        		else if (nodeName.lName.equals(xpp.getName()) && nodeName.prefix.equals(xpp.getPrefix()))
        		{
        			break;
        		}
        	}
        	else if (eventType == XmlPullParser.START_TAG)
        	{
        		NodeProcessor node = npFactory.getInstance(xpp.getPrefix(), xpp.getName());
        		node.processNode(this);
        	}
        }// ~while

        if(!stopped) {
            assertEndTag();
        }
    }

    void processDocumentNode(XmlName name) throws IOException, XmlPullParserException
    {
//        logger.info(System.currentTimeMillis());
        //logger.info("Document Node Name : "+name.lName);

        // Construct the Workbook object only when the Content.xml is parsed
        //if("document-content".equals(name.lName) && !isChartContent)
    // Creation and end of workbook call moved to EngineUtils parsing method
//        if("document-content".equals(name.lName) && listener != null)
//        {
//            listener.constructWorkbook(workbookName);
//        }
        traverseNode(name);
    // call the end workbook
//	listener.endWorkbook();
    }

    //********ADDED BY GANESH

    void processScriptsNode(XmlName name) throws IOException, XmlPullParserException
    {
        traverseNode(name);
    }

    void processShapesNode(XmlName name) throws IOException, XmlPullParserException
    {
    isSheetScopeShape = true;
        traverseNode(name);
    isSheetScopeShape = false;
    }

    void processFormsNode(XmlName name) throws IOException,XmlPullParserException
    {
        forms = new Forms();
        forms.setAutomaticFocus(getAttribute(aAutomaticFocus));
        forms.setApplyDesignMode(getAttribute(aApplyDesignMode));

    traverseNode(nOfficeForms);
        listener.updateForms(forms);
    }

    void processFormFormNode(XmlName name) throws IOException,XmlPullParserException
    {
    form = new Form();
    form.setType("form");
    forms.addForm(form);
    setFormData(form, name);
    isForm = true;
    traverseNode(nFormForm);
    }

    void processFormButtonNode(XmlName name) throws IOException,XmlPullParserException
    {
    formElement = new Form();
    formElement.setType("button");
    setFormData(formElement, name);
    ////////////
    form.addFormElement(formElement);
    isForm = false;
    traverseNode(nFormButton);
    }

    private void setFormData(Form form, XmlName name) throws IOException,XmlPullParserException
    {
        form.setName(getAttribute(aFormName));
    form.setApplyFilter(getAttribute(aApplyFilter));
    form.setControlImplementation(getAttribute(aCtrlImpl));
        form.setId(getAttribute(aFormId));
    form.setLabel(getAttribute(aLabel));
    form.setDelayForRepeat(getAttribute(aApplyFilter));
    form.setImageData(getAttribute(aImageData));
    form.setImagePosition(getAttribute(aImagePosition));
    form.setDelayForRepeat(getAttribute(aDelayForRepeat));
    form.setTargetFrame(getAttribute(aTargetFrame));
    form.setHref(getAttribute(aHref));
    form.setCommandType(getAttribute(aCommandType));
    }

    void processFormPropertyNode(XmlName name) throws IOException,XmlPullParserException
    {
    String propName = getAttribute(aPropertyName);
    String valueType = getAttribute(aTableValueType);
    String value = valueType.equals("date")?getAttribute(aTableDateVal):
        valueType.equals("time")?getAttribute(aTableTimeVal):
        valueType.equals("boolean")?getAttribute(aTableBoolVal):
    valueType.equals("string")?getAttribute(aTableStringVal):getAttribute(aTableValue);
    FormProperty formProperty = new FormProperty(propName, valueType, value);

    if(isForm)
    {
        form.addFormProperty(formProperty);
    }
    else
    {
        formElement.addFormProperty(formProperty);
    }
    }

    void processScriptEventListenerNode(XmlName name) throws IOException,XmlPullParserException
    {
        if(isShapeNode)
        {
            return;
        }
    String language = getAttribute(aScriptEventLanguage);
    String eventName = getAttribute(aScriptEventName);
    String href = getAttribute(aHref);
    ScriptEventListener scpEventListener = new ScriptEventListener(language, eventName, href);
    if(isForm)
    {
        form.addScriptEventListener(scpEventListener);
    }
    else
    {
        formElement.addScriptEventListener(scpEventListener);
    }
    }

    //*******************END OF GANESH

    void processFontDeclsNode() throws IOException, XmlPullParserException
    {
        if(dataOnly)
        {
            XmlPullUtil.skipSubTree(xpp);
            return;
        }
        traverseNode(nFontDecls);
    }

    void processAutoStylesNode() throws IOException, XmlPullParserException
    {
        this.isPatternsParsed = false;
        listener.updateStylesVersionId(Utility.masknull(getAttribute(aStyleVersion), 0));
        traverseNode(nAutomaticStyles);
        this.isPatternsParsed = false;
    }


    void processFontNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nFontDecl);

        FontFace fontFace = new FontFace();
    String styleName = getAttribute(aStyleName);
    if(styleName != null)
    {
        if(styleName.startsWith("'") && styleName.endsWith("'"))
        {
        styleName = styleName.substring(1, styleName.length()-1);
        }
    }

    String fontFamily = getAttribute(aFontFamily);
    if(fontFamily != null)
    {
        if(fontFamily.startsWith("'") && fontFamily.endsWith("'"))
        {
        fontFamily = fontFamily.substring(1, fontFamily.length()-1);
        }
    }

    fontFace.setStyleName(styleName);
        fontFace.setFontFamily(fontFamily);
        fontFace.setFontFamilyGeneric(getAttribute(aFontFamilyGeneric));
        fontFace.setFontAdornments(getAttribute(aFontAdornments));
        fontFace.setFontPitch(getAttribute(aFontPitch));
        fontFace.setFontCharset(getAttribute(aFontCharset));
        XmlPullUtil.skipSubTree(xpp);
        listener.updateFontFace(fontFace);
    }


    void processStyleNode() throws IOException, XmlPullParserException
    {
    //XmlName node = null;
//        //From styles.xml we may get style:style or style:default-style
//        //this try catch is just to support both.
//        try
//        {
//            node = nStyle;
//            assertStartTag(node);
//        }catch(Exception ex)
//        {
//            node = nDefaultStyle;
//            assertStartTag(node);
//        }

    XmlName node = nStyle;
    String eleName = xpp.getName();
    if(!isNameSpaceAware)
    {
        eleName = eleName.substring(eleName.indexOf(":")+1);
    }

    if("default-style".equals(eleName))
    {
        node = nDefaultStyle;
    }
    ////////////////////////////////////
    assertStartTag(node);

        // Global variable
        String styleFamily = getAttribute(aStyleFamily);
        
        //If dataOnly, parse SheetStyle alone => bcoz, SheetStyle contains sheet-hide related info, which defines the data's structure
        if(dataOnly && !"table".equals(styleFamily))
        {
            XmlPullUtil.skipSubTree(xpp);
        } else {
        
            String styleName = getAttribute(aStyleName);

            if (null != styleFamily)
            {
                switch (styleFamily)
                {
                    case "table-column"://No I18N
                        
                        //columnStyle.setStyleFamily(styleFamily);
                        this.style = new ColumnStyle();
                        this.style.setStyleName(styleName);
                        traverseNode(node);
                        listener.updateColumnStyle((ColumnStyle) this.style);
                        break;
                    case "table-row"://No I18N
                        
                        //rowStyle.setStyleFamily(styleFamily);
                        this.style = new RowStyle();
                        this.style.setStyleName(styleName);
                        traverseNode(node);
                        listener.updateRowStyle((RowStyle)this.style);
                        break;
                    case "table-cell"://No I18N
                        if(node.equals(nStyle) || node.equals(nDefaultStyle))
                        {
                            // node as a default
                            if(node.equals(nDefaultStyle))
                            {
                                styleName = "Default";
                            }
                            
                            String displayName = getAttribute(aDisplayName);
                            String parentStyleName = getAttribute(aParentStyleName);
                            String dataStyleName = getAttribute(aDataStyleName);

                            this.style = new CellStyle();
                            this.style.setStyleName(styleName);
                            this.style.setDisplayName(displayName);
                            this.style.setParenStyleName(parentStyleName);
                            if(dataStyleName != null) {
                                if(dataStyleName.equals("Default"))
                                {
                                    this.style.setProperty(CellStyle.Property.PATTERN, DataStyleConstants.EMPTY_PATTERN);
                                }
                                else {
                                    if(isPatternsParsed)
                                    {
                                        this.style.setProperty(CellStyle.Property.PATTERN, this.patternMap.get(dataStyleName));
                                    }
                                    else {
                                        ((ODSWorkbookTransformer) listener).updateDataStyleNameToCellStyleMap(styleName, dataStyleName);
                                    }
                                }
                            }

                            traverseNode(node);
                            
                            for(MapStyleTemp ms : this.mapStyles)
                            {
                                listener.updateMapStyle(ms.condition, ms.basecellAddress, ms.applyStyleName, this.style);
                            }

                            if(this.isInsideStylesTag) {
                                listener.updateNamedCellStyle((CellStyle)this.style);
                            }  else {
                                listener.updateCellStyle((CellStyle)this.style);
                            }
                        }
                        else
                        {
                            //logger.info("Inside skip tree for Style.xml ::: "+styleName);
                            // Skip the sun tree say text-properties as we dont need to parse it
                            // if it is from styles.xml
                            XmlPullUtil.skipSubTree(xpp);
                        }       break;
                    case "table"://No I18N
                        String masterPageName = getAttribute(aMasterPageName);
                        this.style = new SheetStyle();
                        this.style.setStyleName(styleName);
                        //sheetStyle.setStyleFamily(styleFamily);
                        this.style.setMasterPageName(masterPageName);
                        traverseNode(node);

                        listener.updateSheetStyle((SheetStyle)this.style);
                        break;
                    case "graphic"://No I18N
//                        XmlPullUtil.skipSubTree(xpp);

                        /*
                        * Not supporting graphic styles now
                        * the below code must be uncommented when support for graphic style is added.
                        */
                        /*
                        graphicStyle = new GraphicStyle();

                        graphicStyle.setStyleName(getAttribute(aStyleName));
                        graphicStyle.setStyleFamily(getAttribute(aStyleFamily));

                        // TODO : This will be done later when the Graphic
                        // properties supported. Currently we are not intend to support this
                        //traverseNode(nStyle);
                        // this is required as we don't process the graphic elements yet
                        XmlPullUtil.skipSubTree(xpp);
                listener.updateGraphicStyle(graphicStyle);
                 *
                        */     
                    	if(node.equals(nDefaultStyle))
                        {
                            styleName = "Default";// No I18N
                        }
                    	try {
	                    	this.style = new GraphicStyle();
	                    	this.style.setStyleName(styleName);
	                    	traverseNode(node);
	                    	if(this.isInsideStylesTag) {
	                    		listener.updateNamedGraphicStyle((GraphicStyle)this.style);
	//                    		listener.updateDefaultGraphicStyle((GraphicStyle)this.style);
	                    	}            
	                    	else {
	                    		listener.updateGraphicStyle((GraphicStyle)this.style);
	                    	}
                    	}
                    	catch(Exception e) {
                    		logger.log(Level.INFO, "[GRAPHIC STYLE] exception in parsing graphic styles", e);
                    	}
                    	break;
                    case "paragraph"://No I18N

                        this.style = new ParagraphStyle();
                        this.style.setStyleName(styleName);
                        traverseNode(node);
                        listener.updateParagraphStyle((ParagraphStyle) this.style);
                        break;
                    case "text"://No I18N
                        
                        //String textStyleName = getAttribute(aStyleName);
                        //String styleFamily = getAttribute(aStyleFamily);

                        this.style = new TextStyle();
                        this.style.setStyleName(styleName);
                        traverseNode(node);
                        listener.updateTextStyle((TextStyle) this.style);
                        break;
                    default:
                        XmlPullUtil.skipSubTree(xpp);
                //traverseNode(node);
                        break;
                }
            }
        }
        this.style = null;
        this.mapStyles.clear();
    }

     // process 'style:properties' node
    void processStylePropsNode() throws IOException, XmlPullParserException
    {
        String eleName = xpp.getName();
        if(!isNameSpaceAware)
        {
            eleName = eleName.substring(eleName.indexOf(":")+1);
        }
        //assertStartTag(nStyleProps);
    
        if(null != eleName)
        {
            switch (eleName)
            {
                case "table-properties"://No I18N
                    initSheetStyleProperties();
                    break;
                case "table-row-properties"://No I18N
                    initRowStyleProperties();
                    break;
                case "table-column-properties"://No I18N
                    initColumnStyleProperties();
                    break;
                case "table-cell-properties"://No I18N
                    initCellStyleProperties();
                    break;
                case "paragraph-properties"://No I18N  
                    initParagraphStyleProperties();
                    break;
                case "text-properties"://No I18N
                    initTextStyleProperties();
                    break;
                case "graphic-properties":
                	initGraphicStyleProperties();
                	break;
                case "map"://No I18N                        
                    String condition = changeFormula(getAttribute(aCondition_Style));
                    if(!unsupportedCondition(condition))
                    {
                        MapStyleTemp ms = new MapStyleTemp(condition, getAttribute(aBaseCellAddress), getAttribute(aApplyStyleName));
                        this.mapStyles.add(ms);
                    }
                    break;
            }
        }

        XmlPullUtil.skipSubTree(xpp);
    }
    
    void processPatternsNode() throws IOException, XmlPullParserException {
        this.assertStartTag(nPatterns);
        this.traverseNode(nPatterns);
        this.isPatternsParsed = true;
    }
    
    void processPatternNode() throws IOException, XmlPullParserException {
        assertStartTag(nPattern);
        
        String patternName = this.getAttribute(aPatternName);
        String patternStr = this.getAttribute(aPatternString);
        boolean isAutoOrderBool = Boolean.valueOf(this.getAttribute(aIsAutoOrder));
        boolean isAccountingFormatBool = Boolean.valueOf(this.getAttribute(aIsAccountingFormat));

        patternMap.put(patternName, this.listener.updatePattern(patternStr, isAccountingFormatBool, isAutoOrderBool));
    }

    //process number:number-style node
    void processNumberStyleNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nNumberStyle);
        setNumberStyleAttributes(nNumberStyle);
    }

    void processPercentageStyleNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nPercentageStyle);
        setNumberStyleAttributes(nPercentageStyle);
    }

    void processDateStyleNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDateStyle);
        setNumberStyleAttributes(nDateStyle);
    }

    void processCurrencyStyleNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nCurrencyStyle);
        setNumberStyleAttributes(nCurrencyStyle);
    }

    void processBooleanStyleNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nBooleanStyle);
        setNumberStyleAttributes(nBooleanStyle);
    }

    void processTimeStyleNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nTimeStyle);
        setNumberStyleAttributes(nTimeStyle);
    }

    void processTextStyleNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nTextStyle);
        setNumberStyleAttributes(nTextStyle);
    }

    // for styles.xml
    void processStylesNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nStyles);
        
        this.listener.publishDefaultColumnWidth(Integer.toString(EngineConstants.DEFAULT_COLUMN_WIDTH_OLD));
        
        this.isInsideStylesTag = true;
        this.isPatternsParsed = false;
        
        traverseNode(nStyles);
        
        this.isInsideStylesTag = false;
        this.isPatternsParsed = false;
    }
    
    void processDefaultColumnWidthNode() throws IOException, XmlPullParserException {
        assertStartTag(defaultColumnWidth);
        
        this.listener.publishDefaultColumnWidth(getAttribute(aTableValue));
        
        traverseNode(defaultColumnWidth);
    }

    /*
     *Process the children of nunber:number-style
     *number:percentage-style, number:currency-style, number:date-style
     *number:time-style, number:boolean-style
     *
     */
    void processNumberNode() throws IOException, XmlPullParserException
    {
        
        String eleName = xpp.getName();
        if(!isNameSpaceAware)
        {
            eleName = eleName.substring(eleName.indexOf(":")+1);
        }

        numberElement = new NumberElement(eleName);

        numberElement.setDecimalPlaces(Utility.masknull(getAttribute(aDecimalPlaces),numberElement.getDecimalPlaces()));
        numberElement.setDecimalReplacement(getAttribute(aDecimalReplacement));
        numberElement.setDisplayFactor((new Double(Utility.masknull(getAttribute(aDisplayFactor), "1"))));
        //numberStyle.setGrouping(getAttribute(aGrouping));
        numberElement.setMinIntDigits(Utility.masknull(getAttribute(aMinIntDigits),0));
        numberElement.setMinExpDigits(Utility.masknull(getAttribute(aMinExpDigits),0));
        numberElement.setGroupingUsed(Boolean.parseBoolean(getAttribute(aGrouping)));
        numberElement.setDenomValue(Utility.masknull(getAttribute(aDenomValue),0));
        numberElement.setMinNumDigits(Utility.masknull(getAttribute(aMinNumDigits),0));
        numberElement.setMinDenomDigits(Utility.masknull(getAttribute(aMinDenomDigits),0));

        // numberElement.setTagName(xpp.getName());
        numberElement.setCalender(getAttribute(aCalender));
        numberElement.setPossesiveForm(getAttribute(aPossesiveForm));
        numberElement.setStyle(Utility.masknull(getAttribute(aStyle),"short"));     // No i18N
        numberElement.setTextual(getAttribute(aTextual));
        numberElement.setLanguage(getAttribute(aLanguage_Number));
        numberElement.setCountry(getAttribute(aCountry_Number));


        //numberElement.setPosition(Integer.parseInt(getAttribute(aPosition)));

        // number:number can have zero or move embedded-text elements.
        if("number".equals(eleName))
        {
            traverseNode(nNumber);
        }
        else if(xpp.next()==XmlPullParser.TEXT)
        {
            String text = xpp.getText();
            numberElement.setContent(text);
            if(eleName.equals("fill-character")) {
                numberElement.setRepeatChar(text);
            }
        }

        numberStyle.addNumberElement(numberElement);
    }

    void processEmbeddedTextNode() throws IOException, XmlPullParserException
    {
        EmbeddedText embeddedText = new EmbeddedText();
        embeddedText.setPosition(Utility.masknull(getAttribute(aPosition),0));

        if(xpp.next()==XmlPullParser.TEXT)
        {
            embeddedText.setContent(xpp.getText());
        }

        numberElement.addEmbeddedText(embeddedText);
    }
    
    void processSpaceIndexsNode() throws IOException, XmlPullParserException
    {
        if(numberElement != null) {
            String indexOfSpacesString = getAttribute(aSpaceIndexsString);
            if (indexOfSpacesString == null || indexOfSpacesString.isEmpty()) {
                return;
            }
            List<Integer> spaceIndexs = new ArrayList<>();
            String[] indexOfSpacesStringArr = indexOfSpacesString.split(";");
            for (String s : indexOfSpacesStringArr) {
                if (!s.isEmpty()) {
                    spaceIndexs.add(Integer.parseInt(s));
                }
            }
            numberElement.setSpaceIndexs(spaceIndexs);
        }
        else {
            logger.log(Level.INFO, " SHOULD FIX NumberElement is null when processing space-index nodes in ODSWorkbookParser.");
        }
    }

    void processRepeatIndexNode() throws IOException, XmlPullParserException
    {
        String repeatIndexString = getAttribute(aRepeatIndexString);
        String repeatChar = getAttribute(aRepeatCharString);
        if(repeatIndexString == null || repeatIndexString.equals("-1")) {
            return;
        }
        numberElement.setRepeatIndex(Integer.parseInt(repeatIndexString));
        numberElement.setRepeatChar(repeatChar);
    }

    public void setNumberStyleAttributes(XmlName node)throws IOException, XmlPullParserException
    {
        // If Patterns has been already parsed, they would have already populated NumberStyles
        // hence, need not process NumberStyles again
        
        // ODS-ZS hybrid documents have both Patterns and NumberStyles definitions
        
        if(this.isPatternsParsed) {
            try {
                XmlPullUtil.skipSubTree(xpp);
            } catch(XmlPullParserException xe) {
                logger.log(Level.SEVERE, "Exception in skipSubTree(). Navigating xpp pointer manually", xe);
                while(
                        !((xpp.getEventType() == XmlPullParser.END_TAG)
                            && ((!isNameSpaceAware && (node.prefix+":"+node.lName).equals(xpp.getName()))
                                || (node.lName.equals(xpp.getName()) && node.prefix.equals(xpp.getPrefix()))
                        ))) {
                    xpp.next();
                }
            }
            return;
        }
        
        String styleName = getAttribute(aStyleName);
        boolean isAccountingFormat = false;

        if(styleName == null)
        {
            XmlPullUtil.skipSubTree(xpp);
            return;
        }
        //logger.info("STYLENAME : "+styleName);
        String language = getAttribute(aLanguage_Number);
    String country = getAttribute(aCountry_Number);
       // Locale locale = null;

//        if(language != null && country != null)
//        {
//            locale = LocaleUtil.getLocale(language, country);
//        }
//        else
//        {
//            locale = Locale.getDefault();
//        }

        //if(!(styleName.equals("N0") || styleName.equals("N104") || styleName.equals("N104P0")))
        //if(! getDefaultCurrencyStyles().contains(styleName))
//        {
            //logger.info(xpp.getName()+"  "+styleName);
            //prefix = xpp.getPrefix();
        String eleName = xpp.getName();

        if(!isNameSpaceAware)
        {
        int index = eleName.indexOf(":");
        //prefix = eleName.substring(0, index);
        eleName = eleName.substring(index+1);
        }

            Type type = Type.UNDEFINED;
            //logger.info("number style ocuuring cell's type  : "+xpp.getName());
            //logger.info("style name : "+styleName);
            switch (eleName)
            {
                case "number-style"://No I18N
                    type = Type.FLOAT;
                    break;
                case "percentage-style"://No I18N
                    type = Type.PERCENTAGE;
                    break;
                case "currency-style"://No I18N
                    type = Type.CURRENCY;
                    isAccountingFormat = getAttribute(aAccounting) == null ? false : true;
                    break;
                case "date-style"://No I18N
                    type = Type.DATE;
                    break;
                case "time-style"://No I18N
                    type = Type.TIME;
                    break;
                case "boolean-style"://No I18N
                    type = Type.BOOLEAN;
                    break;
                case "text-style"://No I18N
                    type = Type.STRING;
                    break;
//                case "accounting-style"://No I18N
//                    type= Type.ACCOUNTING;
//                    break;
            }


            String displayName = getAttribute(aDisplayName);
//            String language = getAttribute(aLanguage);
            String sName = getAttribute(aStyleName);
            
            //Reverted back isStyleVolatile, by default as TRUE in case of isFromStylesXML, due to lose in styles(support came but couldn't reproduce)
            String styleVolatile = getAttribute(aStyleVolatile);
            boolean isStyleVolatile = Boolean.parseBoolean(Utility.masknull(styleVolatile, isInsideStylesTag ? "true" : "false"));
            String autoOrder = isInsideStylesTag && styleVolatile == null ? "true" : getAttribute(aAutoOrder);
            Boolean isTruncateOnOverflow = Boolean.parseBoolean(Utility.masknull(getAttribute(aTruncateOnOverflow), "true"));

            String title = getAttribute(aTitle);
            String formatSource = getAttribute(aFormatSource);
            String formatCode = getAttribute(aFormatCode);
            //String parentName = xpp.getName();

            numberStyle = new NumberStyle();
            numberStyle.setStyleName(sName);
            this.style = new TextStyle();
            traverseNode(node);

            // Handled for Number-Style here the styleFamily will be set to null
            numberStyle.setTextStyle((TextStyle)this.style);
            this.style = null;

            for(MapStyleTemp ms : this.mapStyles)
            {
                listener.updateMapStyle(ms.condition, ms.basecellAddress, ms.applyStyleName, numberStyle);
            }
            this.mapStyles.clear();

            List<NumberElement> numberElementList = numberStyle.getNumberElementList();

        // Set the locale again here as there may be language or coutry given again
            //numberStyle.setLocale(locale);


            if(!numberElementList.isEmpty() && numberElementList.get(0).getTagName().equals("fraction"))
            {
                type = Type.FRACTION;
            }
            if(!numberElementList.isEmpty() && numberElementList.get(0).getTagName().equals("scientific-number"))
            {
                type = Type.SCIENTIFIC;
            }
            if(type.equals(Type.DATE) && !numberElementList.isEmpty())
            {
                for(NumberElement ne : numberElementList)
                {
                    if(ne.getTagName().equals("hours")
                          || ne.getTagName().equals("minutes")
                          || ne.getTagName().equals("seconds")
                          || ne.getTagName().equals("am-pm"))
                    {
                        type = Type.DATETIME;
                        break;
                    }
                }
            }

            // set type here.
            numberStyle.setType(type);
            numberStyle.setAccountingBoolean(isAccountingFormat);

        // this is to initialize the pattern after setting the element list


//            numberStyle.setCountry(getAttribute(aCountry));
//            numberStyle.setDisplayName(getAttribute(aDisplayName));
//            numberStyle.setLanguage(language);
//            numberStyle.setStyleName(getAttribute(aStyleName));
//            numberStyle.setStyleVolatile(Boolean.parseBoolean(getAttribute(aStyleVolatile)));
//            numberStyle.setTitle(getAttribute(aTitle));
//            numberStyle.setFormatSource(getAttribute(aFormatSource));
//            numberStyle.setAutoOrder(getAttribute(aAutoOrder));
            //logger.info(prefix);

            numberStyle.setLanguage(language);
            numberStyle.setCountry(country);
            numberStyle.setDisplayName(displayName);
            numberStyle.setStyleVolatile(isStyleVolatile);
            numberStyle.setTruncateOnOverflow(isTruncateOnOverflow);
            numberStyle.setTitle(title);
            numberStyle.setFormatSource(formatSource);
            numberStyle.setFormatCode(formatCode);
            numberStyle.setAutoOrder(autoOrder);

          
            
            listener.updateNumberStyle(numberStyle);
        // Just comment the above code and uncomment
        // the below line if things works fine for a month
        //listener.updateNumberStyle(numberStyle);

        numberStyle = null;
        //prefix = null;
            // adding the child nodes text-properties and mapStyle which
            // were done in the method processstylepropsnode has been moved here.
        /*
            if(mapStyle != null)
            {
                numberStyle.addMapStyle(mapStyle);
                mapStyle = null;
            }

            if(textStyle != null)
            {
                numberStyle.setTextStyle(textStyle);
                textStyle = null;
            }

         */


//        }
//        else
//        {
//            // Need to skip tree for N104 like....
//            XmlPullUtil.skipSubTree(xpp);
//        }
    }
    
    private void initColumnStyleProperties()
    {
        this.style.setProperty(ColumnStyle.Property.COLUMNWIDTH, getAttribute(aColumnWidth));
        this.style.setProperty(ColumnStyle.Property.BREAKBEFORE, getAttribute(aBreakBefore));
        this.style.setProperty(ColumnStyle.Property.BREAKAFTER, getAttribute(aBreakAfter));
        this.style.setProperty(ColumnStyle.Property.RELCOLUMNWIDTH, getAttribute(aRelColumnWidth));
        this.style.setProperty(ColumnStyle.Property.USEOPTIMALCOLUMNWIDTH, getAttribute(aUseOptimalColumnWidth));
    }
    
    private void initRowStyleProperties()
    {
        this.style.setProperty(RowStyle.Property.ROWHEIGHT, getAttribute(aRowHeight));
        this.style.setProperty(RowStyle.Property.MINROWHEIGHT, getAttribute(aMinRowHeight));
        this.style.setProperty(RowStyle.Property.BACKGROUNDCOLOR, getAttribute(aBackgroundColor));
        this.style.setProperty(RowStyle.Property.BREAKBEFORE, getAttribute(aBreakBefore));
        this.style.setProperty(RowStyle.Property.BREAKAFTER, getAttribute(aBreakAfter));
        this.style.setProperty(RowStyle.Property.KEEPTOGETHER, getAttribute(aKeepTogether));
        String useOptimalRowHeight = getAttribute(aUseOptimalRowHeight);
        this.style.setProperty(RowStyle.Property.USEOPTIMALROWHEIGHT, useOptimalRowHeight == null ? true : Boolean.valueOf(useOptimalRowHeight));
    }

    private void initTextStyleProperties()
    {
        this.style.setProperty(TextStyle.Property.BACKGROUNDCOLOR, getAttribute(aBackgroundColor));
        this.style.setProperty(TextStyle.Property.COLOR, ZSColor.getColor(getAttribute(aColor), getAttribute(A_THEME_COLOR), getAttribute(A_COLOR_TINT)));
        this.style.setProperty(TextStyle.Property.CONDITION, getAttribute(aCondition));
        this.style.setProperty(TextStyle.Property.COUNTRY, getAttribute(aCountry));
        this.style.setProperty(TextStyle.Property.COUNTRYASIAN, getAttribute(aCountryAsian));
        this.style.setProperty(TextStyle.Property.COUNTRYCOMPLEX, getAttribute(aCountryComplex));
        this.style.setProperty(TextStyle.Property.DISPLAY, getAttribute(aDisplay));
        this.style.setProperty(TextStyle.Property.FONTCHARSET, getAttribute(aFontCharSet));
        this.style.setProperty(TextStyle.Property.FONTCHARSETASIAN, getAttribute(aFontCharSetAsian));
        this.style.setProperty(TextStyle.Property.FONTCHARSETCOMPLEX, getAttribute(aFontCharSetComplex));
        this.style.setProperty(TextStyle.Property.FONTFAMILY, getAttribute(aFontFamily_FO));
        this.style.setProperty(TextStyle.Property.FONTFAMILYASIAN, getAttribute(aFontFamilyAsian));
        this.style.setProperty(TextStyle.Property.FONTFAMILYCOMPLEX, getAttribute(aFontFamilyComplex));
        this.style.setProperty(TextStyle.Property.FONTFAMILYGENERIC, getAttribute(aFontFamilyGeneric));
        this.style.setProperty(TextStyle.Property.FONTFAMILYGENERICASIAN, getAttribute(aFontFamilyGenericAsian));
        this.style.setProperty(TextStyle.Property.FONTFAMILYGENERICCOMPLEX, getAttribute(aFontFamilyGenericComplex));
        this.style.setProperty(TextStyle.Property.FONTNAME, getAttribute(aFontName));
        this.style.setProperty(TextStyle.Property.FONTNAMEASIAN, getAttribute(aFontNameAsian));
        this.style.setProperty(TextStyle.Property.FONTNAMECOMPLEX, getAttribute(aFontNameComplex));
        this.style.setProperty(TextStyle.Property.FONTPITCHNAME, getAttribute(aFontPitchName));
        this.style.setProperty(TextStyle.Property.FONTPITCHNAMEASIAN, getAttribute(aFontPitchNameAsian));
        this.style.setProperty(TextStyle.Property.FONTPITCHNAMECOMPLEX, getAttribute(aFontPitchNameComplex));
        this.style.setProperty(TextStyle.Property.FONTRELIEF, getAttribute(aFontRelief));
        this.style.setProperty(TextStyle.Property.FONTSIZE, getAttribute(aFontSize));
        this.style.setProperty(TextStyle.Property.FONTSIZEASIAN, getAttribute(aFontSizeAsian));
        this.style.setProperty(TextStyle.Property.FONTSIZECOMPLEX, getAttribute(aFontSizeComplex));
        this.style.setProperty(TextStyle.Property.FONTSIZEREL, getAttribute(aFontSizeRel));
        this.style.setProperty(TextStyle.Property.FONTSIZERELASIAN, getAttribute(aFontSizeRelAsian));
        this.style.setProperty(TextStyle.Property.FONTSIZERELCOMPLEX, getAttribute(aFontSizeRelComplex));
        this.style.setProperty(TextStyle.Property.FONTSTYLE, getAttribute(aFontStyle));
        this.style.setProperty(TextStyle.Property.FONTSTYLEASIAN, getAttribute(aFontStyleAsian));
        this.style.setProperty(TextStyle.Property.FONTSTYLECOMPLEX, getAttribute(aFontStyleComplex));
        this.style.setProperty(TextStyle.Property.FONTVARIANT, getAttribute(aFontVariant));
        this.style.setProperty(TextStyle.Property.FONTWEIGHT, getAttribute(aFontWeight));
        this.style.setProperty(TextStyle.Property.FONTWEIGHTASIAN, getAttribute(aFontWeightAsian));
        this.style.setProperty(TextStyle.Property.FONTWEIGHTCOMPLEX, getAttribute(aFontWeightComplex));
        this.style.setProperty(TextStyle.Property.HYPHENATIONPUSHCHARCOUNT, getAttribute(aHyphenationPushCharCount));
        this.style.setProperty(TextStyle.Property.HYPHENATIONREMAINCHARCOUNT, getAttribute(aHyphenationRemainCharCount));
        this.style.setProperty(TextStyle.Property.LANGUAGE, getAttribute(aLanguage));
        this.style.setProperty(TextStyle.Property.LANGUAGEASIAN, getAttribute(aLanguageAsian));
        this.style.setProperty(TextStyle.Property.LANGUAGECOMPLEX, getAttribute(aLanguageComplex));
        this.style.setProperty(TextStyle.Property.LETTERKERNING, getAttribute(aLetterKerning));
        this.style.setProperty(TextStyle.Property.LETTERSPACING, getAttribute(aLetterSpacing));
        this.style.setProperty(TextStyle.Property.SCRIPTTYPE, getAttribute(aScriptType));
        this.style.setProperty(TextStyle.Property.TEXTBLINKING, getAttribute(aTextBlinking));
        this.style.setProperty(TextStyle.Property.TEXTCOMBINE, getAttribute(aTextCombine));
        this.style.setProperty(TextStyle.Property.TEXTCOMBINEENDCHAR, getAttribute(aTextCombineEndChar));
        this.style.setProperty(TextStyle.Property.TEXTCOMBINESTARTCHAR, getAttribute(aTextCombineStartChar));
        this.style.setProperty(TextStyle.Property.TEXTEMPHASIZE, getAttribute(aTextEmphasize));
        this.style.setProperty(TextStyle.Property.TEXTLINETHROUGHCOLOR, getAttribute(aTextLineThroughColor));
        this.style.setProperty(TextStyle.Property.TEXTLINETHROUGHMODE, getAttribute(aTextLineThroughMode));
        this.style.setProperty(TextStyle.Property.TEXTLINETHROUGHSTYLE, getAttribute(aTextLineThroughStyle));
        this.style.setProperty(TextStyle.Property.TEXTLINETHROUGHTEXT, getAttribute(aTextLineThroughText));
        this.style.setProperty(TextStyle.Property.TEXTLINETHROUGHTEXTSTYLE, getAttribute(aTextLineThroughTextStyle));
        this.style.setProperty(TextStyle.Property.TEXTLINETHROUGHTYPE, getAttribute(aTextLineThroughType));
        this.style.setProperty(TextStyle.Property.TEXTLINETHROUGHWIDTH, getAttribute(aTextLineThroughWidth));
        this.style.setProperty(TextStyle.Property.TEXTOUTLINE, getAttribute(aTextOutline));
        this.style.setProperty(TextStyle.Property.TEXTPOSITION, getAttribute(aTextPosition));
        this.style.setProperty(TextStyle.Property.TEXTROTATIONANGLE, getAttribute(aTextRotationAngle));
        this.style.setProperty(TextStyle.Property.TEXTROTATIONSCALE, getAttribute(aTextRotationScale));
        this.style.setProperty(TextStyle.Property.TEXTSCALE, getAttribute(aTextScale));
        this.style.setProperty(TextStyle.Property.TEXTSHADOW, getAttribute(aTextShadow));
        this.style.setProperty(TextStyle.Property.TEXTTRANSFORM, getAttribute(aTextTransform));
        this.style.setProperty(TextStyle.Property.TEXTUNDERLINECOLOR, getAttribute(aTextUnderlineColor));
        this.style.setProperty(TextStyle.Property.TEXTUNDERLINEMODE, getAttribute(aTextUnderlineMode));
        this.style.setProperty(TextStyle.Property.TEXTUNDERLINESTYLE, getAttribute(aTextUnderlineStyle));
        this.style.setProperty(TextStyle.Property.TEXTUNDERLINETYPE, getAttribute(aTextUnderlineType));
        this.style.setProperty(TextStyle.Property.TEXTUNDERLINEWIDTH, getAttribute(aTextUnderlineWidth));
    }
    
    private void initGraphicStyleProperties()
    {
        this.style.setProperty(GraphicStyle.Property.MARKEREND, getAttribute(aMarkerEnd));
        this.style.setProperty(GraphicStyle.Property.MARKERENDCENTER, getAttribute(aMarkerEndCenter));
        this.style.setProperty(GraphicStyle.Property.MARKERENDWIDTH, getAttribute(aMarkerEndWidth));
        this.style.setProperty(GraphicStyle.Property.MARKERSTART, getAttribute(aMarkerStart));
        this.style.setProperty(GraphicStyle.Property.MARKERSTARTCENTER, getAttribute(aMarkerStartCenter));
        this.style.setProperty(GraphicStyle.Property.MARKERSTARTWIDTH, getAttribute(aMarkerStartWidth));
        this.style.setProperty(GraphicStyle.Property.STROKE, getAttribute(aStroke));
        this.style.setProperty(GraphicStyle.Property.STROKECOLOR, getAttribute(aStrokeColor));
        this.style.setProperty(GraphicStyle.Property.STROKEDASH, getAttribute(aStrokeDash));
        this.style.setProperty(GraphicStyle.Property.STROKEDASHNAMES, getAttribute(aStrokeDashNames));
        this.style.setProperty(GraphicStyle.Property.STROKELINEJOIN, getAttribute(aStrokeLinejoin));
        this.style.setProperty(GraphicStyle.Property.STROKEOPACITY, getAttribute(aStrokeOpacity));
        this.style.setProperty(GraphicStyle.Property.STROKEWIDTH, getAttribute(aStrokeWidth));
        this.style.setProperty(GraphicStyle.Property.ANIMATION, getAttribute(aAnimation));
        this.style.setProperty(GraphicStyle.Property.ANIMATIONDELAY, getAttribute(aAnimationDelay));
        this.style.setProperty(GraphicStyle.Property.ANIMATIONDIRECTION, getAttribute(aAnimationDirection));
        this.style.setProperty(GraphicStyle.Property.ANIMATIONREPEAT, getAttribute(aAnimationRepeat));
        this.style.setProperty(GraphicStyle.Property.ANIMATIONSTARTINSIDE, getAttribute(aAnimationStartInside));
        this.style.setProperty(GraphicStyle.Property.ANIMATIONSTEPS, getAttribute(aAnimationSteps));
        this.style.setProperty(GraphicStyle.Property.ANIMATIONSTOPINSIDE, getAttribute(aAnimationStopInside));
        this.style.setProperty(GraphicStyle.Property.FILL, getAttribute(aFill));
        this.style.setProperty(GraphicStyle.Property.FILLCOLOR, getAttribute(aFillColor));
        this.style.setProperty(GraphicStyle.Property.FILLGRADIENTNAME, getAttribute(aFillGradientName));
        this.style.setProperty(GraphicStyle.Property.FILLHATCHNAME, getAttribute(aFillHatchName));
        this.style.setProperty(GraphicStyle.Property.FILLHATCHSOLID, getAttribute(aFillHatchSolid));
        this.style.setProperty(GraphicStyle.Property.FILLIMAGEHEIGHT, getAttribute(aFillImageHeight));
        this.style.setProperty(GraphicStyle.Property.FILLIMAGENAME, getAttribute(aFillImageName));
        this.style.setProperty(GraphicStyle.Property.FILLIMAGEREFPOINT, getAttribute(aFillImageRefPoint));
        this.style.setProperty(GraphicStyle.Property.FILLIMAGEREFPOINTX, getAttribute(aFillImageRefPointX));
        this.style.setProperty(GraphicStyle.Property.FILLIMAGEREFPOINTY, getAttribute(aFillImageRefPointY));
        this.style.setProperty(GraphicStyle.Property.FILLIMAGEWIDTH, getAttribute(aFillRule));
        this.style.setProperty(GraphicStyle.Property.FILLRULE, getAttribute(aFillRule));
        this.style.setProperty(GraphicStyle.Property.SECONDARYFILLCOLOR, getAttribute(aSecondaryFillColor));
        this.style.setProperty(GraphicStyle.Property.GRADIENTSTEPCOUNT, getAttribute(aGradientStepCount));
        this.style.setProperty(GraphicStyle.Property.REPEAT, getAttribute(aRepeat));
        this.style.setProperty(GraphicStyle.Property.TILEREPEATOFFSET, getAttribute(aTileRepeatOffset));
        this.style.setProperty(GraphicStyle.Property.OPACITY, getAttribute(aOpacity));
        this.style.setProperty(GraphicStyle.Property.OPACITYNAME, getAttribute(aOpacityName));
        this.style.setProperty(GraphicStyle.Property.SYMBOLCOLOR, getAttribute(aSymbolColor));
        this.style.setProperty(GraphicStyle.Property.AUTOGROWHEIGHT, getAttribute(aAutoGrowHeight));
        this.style.setProperty(GraphicStyle.Property.AUTOGROWWIDTH, getAttribute(aAutoGrowWidth));
        this.style.setProperty(GraphicStyle.Property.FITTOSIZE, getAttribute(aFitToSize));
        this.style.setProperty(GraphicStyle.Property.FITTOCOUNTOUR, getAttribute(aFitToCountour));
        this.style.setProperty(GraphicStyle.Property.TEXTAREAHORIZONTALALIGN, getAttribute(aTextAreaHorizontalAlign));
        this.style.setProperty(GraphicStyle.Property.TEXTAREAVERTICALALIGN, getAttribute(aTextareaVerticalAlign));
        this.style.setProperty(GraphicStyle.Property.WRAPOPTION, getAttribute(aWrapOption));
        this.style.setProperty(GraphicStyle.Property.COLORINVERSION, getAttribute(aColorInversion));
        this.style.setProperty(GraphicStyle.Property.COLORMODE, getAttribute(aColorMode));
        this.style.setProperty(GraphicStyle.Property.CONTRAST, getAttribute(aContrast));
        this.style.setProperty(GraphicStyle.Property.LUMINANCE, getAttribute(aLuminance));
        this.style.setProperty(GraphicStyle.Property.GAMMA, getAttribute(aGamma));
        this.style.setProperty(GraphicStyle.Property.GREEN, getAttribute(aGreen));
        this.style.setProperty(GraphicStyle.Property.RED, getAttribute(aRed));
        this.style.setProperty(GraphicStyle.Property.BLUE, getAttribute(aBlue));
        this.style.setProperty(GraphicStyle.Property.IMAGEOPACITY, getAttribute(aImageOpacity));
        this.style.setProperty(GraphicStyle.Property.SHADOW, getAttribute(aShadow_Draw));
        this.style.setProperty(GraphicStyle.Property.SHADOWCOLOR, getAttribute(aShadowColor));
        this.style.setProperty(GraphicStyle.Property.SHADOWOFFSETX, getAttribute(aShadowOffsetX));
        this.style.setProperty(GraphicStyle.Property.SHADOWOFFSETY, getAttribute(aShadowOffsetY));
        this.style.setProperty(GraphicStyle.Property.SHADOWOPACITY, getAttribute(aShadowOpacity));
        this.style.setProperty(GraphicStyle.Property.MINHEIGHT, getAttribute(A_MIN_HEIGHT));
        this.style.setProperty(GraphicStyle.Property.MINWIDTH, getAttribute(A_MIN_WIDTH));
    }

    private void initParagraphStyleProperties()
    {
        this.style.setProperty(ParagraphStyle.Property.AUTOTEXTINDENT, getAttribute(aAutoTextIndent));
        this.style.setProperty(ParagraphStyle.Property.BACKGROUNDCOLOR, getAttribute(aBackgroundColor));
        this.style.setProperty(ParagraphStyle.Property.BACKGROUNDTRANSPARENCY, getAttribute(aBackgroundTransparency));
        this.style.setProperty(ParagraphStyle.Property.BORDER, getAttribute(aBorder));
        this.style.setProperty(ParagraphStyle.Property.BORDERBOTTOM, getAttribute(aBorderBottom));
        this.style.setProperty(ParagraphStyle.Property.BORDERLINEWIDTH, getAttribute(aBorderLeft));
        this.style.setProperty(ParagraphStyle.Property.BORDERRIGHT, getAttribute(aBorderRight));
        this.style.setProperty(ParagraphStyle.Property.BORDERTOP, getAttribute(aBorderTop));
        this.style.setProperty(ParagraphStyle.Property.BORDERLINEWIDTH, getAttribute(aBorderLineWidth));
        this.style.setProperty(ParagraphStyle.Property.BORDERLINEWIDTHBOTTOM, getAttribute(aBorderLineWidthBottom));
        this.style.setProperty(ParagraphStyle.Property.BORDERLINEWIDTHLEFT, getAttribute(aBorderLineWidthLeft));
        this.style.setProperty(ParagraphStyle.Property.BORDERLINEWIDTHRIGHT, getAttribute(aBorderLineWidthRight));
        this.style.setProperty(ParagraphStyle.Property.BORDERLINEWIDTHTOP, getAttribute(aBorderLineWidthTop));
        this.style.setProperty(ParagraphStyle.Property.BREAKAFTER, getAttribute(aBreakAfter));
        this.style.setProperty(ParagraphStyle.Property.FONTINDEPENDENTLINESPACING, getAttribute(aIndependentLineSpacing));
        this.style.setProperty(ParagraphStyle.Property.HYPHENATIONKEEP, getAttribute(aHyphenationKeep));
        this.style.setProperty(ParagraphStyle.Property.HYPHENATIONLADDERCOUNT, getAttribute(aHyphenationLadderCount));
        this.style.setProperty(ParagraphStyle.Property.JUSTIFYSINGLEWORD, getAttribute(aJustifySingleWord));
        this.style.setProperty(ParagraphStyle.Property.KEEPTOGETHER, getAttribute(aKeepTogether));
        this.style.setProperty(ParagraphStyle.Property.KEEPWITHNEXT, getAttribute(aKeepWithNext));
        this.style.setProperty(ParagraphStyle.Property.LINEBREAK, getAttribute(aLineBreak));
        this.style.setProperty(ParagraphStyle.Property.LINEHEIGHT, getAttribute(aLineHeight));
        this.style.setProperty(ParagraphStyle.Property.LINEHEIGHTATLEAST, getAttribute(aLineHeightAtleast));
        this.style.setProperty(ParagraphStyle.Property.LINENUMBER, getAttribute(aLineNumber));
        this.style.setProperty(ParagraphStyle.Property.LINESPACING, getAttribute(aLineSpacing));
        this.style.setProperty(ParagraphStyle.Property.MARGIN, getAttribute(aMargin));
        this.style.setProperty(ParagraphStyle.Property.MARGINBOTTOM, getAttribute(aMarginBottom));
//        this.style.setProperty(ParagraphStyle.Property.MARGINLEFT, getAttribute(aMarginLeft));
//        this.style.setProperty(ParagraphStyle.Property.MARGINRIGHT, getAttribute(aMarginRight));
        this.style.setProperty(ParagraphStyle.Property.MARGINTOP, getAttribute(aMarginTop));
        this.style.setProperty(ParagraphStyle.Property.NUMBERLINES, getAttribute(aNumberLines));
        this.style.setProperty(ParagraphStyle.Property.ORPHANS, getAttribute(aOrphans));
        this.style.setProperty(ParagraphStyle.Property.PADDING, getAttribute(aPadding));
        this.style.setProperty(ParagraphStyle.Property.PADDINGBOTTOM, getAttribute(aPaddingBottom));
        this.style.setProperty(ParagraphStyle.Property.PADDINGLEFT, getAttribute(aPaddingLeft));
//        this.style.setProperty(ParagraphStyle.Property.PADDINGRIGHT, getAttribute(aPaddingRight));
        this.style.setProperty(ParagraphStyle.Property.PADDINGTOP, getAttribute(aPaddingTop));
        this.style.setProperty(ParagraphStyle.Property.PAGENUMBER, getAttribute(aPageNumber));
        this.style.setProperty(ParagraphStyle.Property.PUNCTUATIONWRAP, getAttribute(aPunctuationWrap));
        this.style.setProperty(ParagraphStyle.Property.REGISTERTRUE, getAttribute(aRegisterTrue));
        this.style.setProperty(ParagraphStyle.Property.SHADOW, getAttribute(aShadow));
        this.style.setProperty(ParagraphStyle.Property.SNAPTOLAYOUTGRID, getAttribute(aSnapToLayoutGrid));
        this.style.setProperty(ParagraphStyle.Property.TEXTALIGN, getAttribute(aTextAlign));
        this.style.setProperty(ParagraphStyle.Property.TEXTALIGNLAST, getAttribute(aTextAlignLast));
        this.style.setProperty(ParagraphStyle.Property.TEXTAUTOSPACE, getAttribute(aTextAutospace));
        this.style.setProperty(ParagraphStyle.Property.TEXTINDENT, getAttribute(aTextIndent));
        this.style.setProperty(ParagraphStyle.Property.VERTICALALIGN, getAttribute(aVerticalAlign));
        this.style.setProperty(ParagraphStyle.Property.WINDOWS, getAttribute(aWindows));
        this.style.setProperty(ParagraphStyle.Property.WRITINGMODE, getAttribute(aWritingMode));
        this.style.setProperty(ParagraphStyle.Property.WRITINGMODEAUTOMATIC, getAttribute(aWritingModeAutomatic));

        /*
         * Indentation details are written as differently by Excel, LIBO and Google.
         * Indentation length can be mentioned in different units ("cm" or "pt")
         * 0.353cm equals 1 Indent and 10pt equals 1 Indent
         * Excel supports indentation for left and right alignment where as LIBO supports indentation only for left alignment.
         * In Excel, if left aligned, the indent length is mentioned in "fo:margin-left" or if right aligned, the indent length is mentioned in "fo:margin-right"
         * In LIBO, if left aligned or right aligned, the indent length is mentioned in "fo:margin-left"
         * In some cases, libo reads value from "fo:margin-right" and write it in "fo:padding-right"
         */

        String indent = getAttribute(aZSIndent);
        if(indent == null)
        {
            String hAlign = (String)this.style.getProperty(ParagraphStyle.Property.TEXTALIGN);
            String margin = (hAlign == null || "start".equals(hAlign) || "end".equals(hAlign)) ? (getAttribute(aMarginLeft) == null ? (getAttribute(aMarginRight) == null ? getAttribute(aPaddingRight) : getAttribute(aMarginRight)) : getAttribute(aMarginLeft)) : null ; //No I18N
            if(margin != null)
            {
                if(margin.contains(CM))
                {
                    margin = margin.replaceAll(CM, EMPTY);
                    indent = String.valueOf(Math.round(Double.parseDouble(margin)/0.353));
                }
                else if(margin.contains(PT))
                {
                    margin = margin.replaceAll(PT, EMPTY);
                    indent = String.valueOf(Math.round(Double.parseDouble(margin)/10));
                }
            }
        }
        this.style.setProperty(ParagraphStyle.Property.ZSINDENT, indent);
    }



    private void initCellStyleProperties()
    {
        this.style.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getColor(getAttribute(aBackgroundColor), getAttribute(A_THEME_COLOR), getAttribute(A_COLOR_TINT)));
        this.style.setProperty(CellStyle.Property.BACKGROUNDIMAGE, getAttribute(aFontBGImage));
        this.style.setProperty(CellStyle.Property.TEXTALIGNSOURCE, getAttribute(aTextAlignSource));
        this.style.setProperty(CellStyle.Property.VERTICALALIGN, getAttribute(aVAlign));
        this.style.setProperty(CellStyle.Property.GLYPHORIENTATIONVERTICAL, getAttribute(aGOVertical));
        this.style.setProperty(CellStyle.Property.DIRECTION, getAttribute(aDirection));
        this.style.setProperty(CellStyle.Property.BORDERLEFT, getAttribute(aBorderLeft));
        this.style.setProperty(CellStyle.Property.BORDERRIGHT, getAttribute(aBorderRight));
        this.style.setProperty(CellStyle.Property.BORDERTOP, getAttribute(aBorderTop));
        this.style.setProperty(CellStyle.Property.BORDERBOTTOM, getAttribute(aBorderBottom));
        String allBorder = getAttribute(aBorder);
        if(allBorder != null){
            if(!"none".equals(allBorder)){ //No I18N
                this.style.setProperty(CellStyle.Property.BORDERLEFT, allBorder);
                this.style.setProperty(CellStyle.Property.BORDERRIGHT, allBorder);
                this.style.setProperty(CellStyle.Property.BORDERTOP, allBorder);
                this.style.setProperty(CellStyle.Property.BORDERBOTTOM, allBorder);
            }
            else if(this.style.getProperty(CellStyle.Property.BORDERLEFT) == null && this.style.getProperty(CellStyle.Property.BORDERRIGHT) == null && this.style.getProperty(CellStyle.Property.BORDERTOP) == null && this.style.getProperty(CellStyle.Property.BORDERBOTTOM) == null){
                this.style.setProperty(CellStyle.Property.BORDERLEFT, allBorder);
                this.style.setProperty(CellStyle.Property.BORDERRIGHT, allBorder);
                this.style.setProperty(CellStyle.Property.BORDERTOP, allBorder);
                this.style.setProperty(CellStyle.Property.BORDERBOTTOM, allBorder);
                }
        }
        this.style.setProperty(CellStyle.Property.BORDERLINEWIDTH, getAttribute(aBorderLineWidth));
        this.style.setProperty(CellStyle.Property.BORDERLINEWIDTHBOTTOM, getAttribute(aBorderLineWidthBottom));
        this.style.setProperty(CellStyle.Property.BORDERLINEWIDTHLEFT, getAttribute(aBorderLineWidthLeft));
        this.style.setProperty(CellStyle.Property.BORDERLINEWIDTHRIGHT, getAttribute(aBorderLineWidthRight));
        this.style.setProperty(CellStyle.Property.BORDERLINEWIDTHTOP, getAttribute(aBorderLineWidthTop));
        this.style.setProperty(CellStyle.Property.DIAGONALBLTR, getAttribute(aDiagonalBLTR));
        this.style.setProperty(CellStyle.Property.DIAGONALBLTR_WIDTHS, getAttribute(aDiagonalBLTRWidths));
        this.style.setProperty(CellStyle.Property.DIAGONALTLBR, getAttribute(aDiagonalTLBR));
        this.style.setProperty(CellStyle.Property.DIAGONALTLBR_WIDTHS, getAttribute(aDiagonalTLBRWidths));
        this.style.setProperty(CellStyle.Property.PADDING, getAttribute(aPadding));
        this.style.setProperty(CellStyle.Property.PADDINGBOTTOM, getAttribute(aPaddingBottom));
        this.style.setProperty(CellStyle.Property.PADDINGLEFT, getAttribute(aPaddingLeft));
        this.style.setProperty(CellStyle.Property.PADDINGRIGHT, getAttribute(aPaddingRight));
        this.style.setProperty(CellStyle.Property.PADDINGTOP, getAttribute(aPaddingTop));
        this.style.setProperty(CellStyle.Property.CELLPROTECT, getAttribute(aCellProtect));
        this.style.setProperty(CellStyle.Property.DECIMALPLACES, getAttribute(aDecimalPlaces));
        this.style.setProperty(CellStyle.Property.PRINTCONTENT, getAttribute(aPrintContent));
        this.style.setProperty(CellStyle.Property.REPEATCONTENT, getAttribute(aRepeatContent));
        this.style.setProperty(CellStyle.Property.ROTATIONALIGN, getAttribute(aRotationAlign));
        this.style.setProperty(CellStyle.Property.ROTATIONANGLE, getAttribute(aRotationAngle));
        this.style.setProperty(CellStyle.Property.SHADOW, getAttribute(aShadow));
        String displayType = getAttribute(ADISPLAYTYPE);
        if(displayType == null){
            String wrap = getAttribute(aWrap);
            if("wrap".equals(wrap)){ // No I18N
                displayType = "1"; // No I18N
            }
            else{
                String shrinkToFit = getAttribute(aShrinkToFit);
                if("true".equals(shrinkToFit)){ // No I18N
                    displayType = "3";// No I18N
                }
            }
        }
        this.style.setProperty(CellStyle.Property.DISPLAYTYPE, displayType);
    }

    private void initSheetStyleProperties()
    {
        this.style.setProperty(SheetStyle.Property.ALIGN, getAttribute(aAlign));
        this.style.setProperty(SheetStyle.Property.WIDTH, getAttribute(aWidth));
        this.style.setProperty(SheetStyle.Property.RELWIDTH, getAttribute(aRelWidth));
        this.style.setProperty(SheetStyle.Property.BORDERMODEL, getAttribute(aBorderModel));
        this.style.setProperty(SheetStyle.Property.BACKGROUNDCOLOR, getAttribute(aBackgroundColor));
        this.style.setProperty(SheetStyle.Property.BACKGROUNDIMAGE, getAttribute(aFontBGImage));
        this.style.setProperty(SheetStyle.Property.BREAKAFTER, getAttribute(aBreakAfter));
        this.style.setProperty(SheetStyle.Property.BREAKBEFORE, getAttribute(aBreakBefore));
        this.style.setProperty(SheetStyle.Property.DISPLAY, getAttribute(aDisplay));
        this.style.setProperty(SheetStyle.Property.KEEPWITHNEXT, getAttribute(aKeepWithNext));
        this.style.setProperty(SheetStyle.Property.MARGIN, getAttribute(aMargin));
        this.style.setProperty(SheetStyle.Property.MARGINBOTTOM, getAttribute(aMarginBottom));
        this.style.setProperty(SheetStyle.Property.MARGINLEFT, getAttribute(aMarginLeft));
        this.style.setProperty(SheetStyle.Property.MARGINRIGHT, getAttribute(aMarginRight));
        this.style.setProperty(SheetStyle.Property.MARGINTOP, getAttribute(aMarginTop));
        this.style.setProperty(SheetStyle.Property.MAYBREAKBETWEENROWS, getAttribute(aMayBreakBetweenRows));
        this.style.setProperty(SheetStyle.Property.PAGENUMBER, getAttribute(aPageNumber));
        this.style.setProperty(SheetStyle.Property.SHADOW, getAttribute(aShadow));
        this.style.setProperty(SheetStyle.Property.WRITINGMODE, getAttribute(aWritingMode));
        this.style.setProperty(SheetStyle.Property.TABCOLOR, ZSColor.getColor(getAttribute(ATABCOLOR), getAttribute(A_THEME_COLOR), getAttribute(A_COLOR_TINT)));
    }

    void processPicklistsNode() throws IOException, XmlPullParserException
    {
        assertStartTag(NPICKLISTS);
        traverseNode(NPICKLISTS);
        listener.endPicklists();
    }

    void processPicklistNode() throws IOException, XmlPullParserException
    {
        assertStartTag(NPICKLIST);
        String picklistID = getAttribute(aTableId);
        String defaultIndex = getAttribute(aStartWith);
        String defaultID = getAttribute(aStartsWithID);
        String rangeStrings = getAttribute(aRange);
        String sourceRangeString = getAttribute(aSourceRange);
        String isRangePicklist = getAttribute(aIsRangePicklist);

        listener.updatePicklist(picklistID,defaultIndex,defaultID,rangeStrings, sourceRangeString, isRangePicklist);
    }

    void processListItemNode() throws IOException, XmlPullParserException
    {
        assertStartTag(NPICKLISTITEM);
        String color = getAttribute(aColor);
        String textTheme = getAttribute(A_THEME_COLOR);
        String textTint = getAttribute(A_COLOR_TINT);

        String bgColor = getAttribute(aBackgroundColor);
        String bgTheme = getAttribute(A_BG_THEME_COLOR);
        String bgTint = getAttribute(A_BG_COLOR_TINT);

        String displayString = getAttribute(aDisplayString);
        String id = getAttribute(aTableId);
        String valueString = xpp.nextText();

        listener.updateListItem(id,displayString, valueString, color,textTheme,textTint, bgColor,bgTheme,bgTint);

    }


    void processContentValidationsNode() throws IOException, XmlPullParserException
    {
        assertStartTag(NCONTENT_VALIDATIONS);
        traverseNode(NCONTENT_VALIDATIONS);
        listener.endPicklists();
    }

    void processContentValidationNode() throws IOException, XmlPullParserException
    {
        assertStartTag(NCONTENT_VALIDATION);
        String validationName = getAttribute(aTableName);
        String condition = getAttribute(ATABLE_CONDITION);
        if(condition != null )
        {
            condition = changeFormula(condition);
            boolean isAllowEmptyCell = Boolean.parseBoolean(getAttribute(ATABLE_ALLOWEMPTYCELL));
            String displayList  = getAttribute(ATABLE_DISPLAYLIST);
            String baseCellAddress = getAttribute(aBaseCellAddress_Range);
            traverseNode(NCONTENT_VALIDATION);
            listener.updateContentValidation(condition, baseCellAddress, validationName, isAllowEmptyCell, displayList, helpMessage, errorMessage);
        }
        helpMessage = null;
        errorMessage = null;
    }

    void processHelpMessageNode() throws IOException, XmlPullParserException
    {
        assertStartTag(NTABLE_HELPMESSAGE);
        String title = getAttribute(ATABLE_TITLE);
        boolean isDisplay = Boolean.parseBoolean(getAttribute(aDisplay));
        traverseNode(NTABLE_HELPMESSAGE);
        helpMessage = new DVHelpMessage(isDisplay, title, textBuff.toString());
        textBuff.setLength(0);
    }

    void processErrorMessageNode() throws IOException, XmlPullParserException
    {
        assertStartTag(NTABLE_ERRORMESSAGE);
        String messageType = Utility.masknull(getAttribute(ATABLE_MESSAGETYPE), "information"); // No I18N
        String title = getAttribute(ATABLE_TITLE);
        boolean isDisplay = Boolean.parseBoolean(getAttribute(aDisplay));
        traverseNode(NTABLE_ERRORMESSAGE);
        errorMessage = new DVErrorMessage(ErrorCode.MsgType.valueOf(messageType.toUpperCase()), isDisplay, title, textBuff.toString());
        textBuff.setLength(0);
    }

    /*
     * To process the node named-expression.
     *
     */
    void processNamedExprssionsNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nNamedExpressions);
        traverseNode(nNamedExpressions);
    }

//    void processFrameNode() throws IOException, XmlPullParserException
//    {
//        assertStartTag(nFrame);
//
//        String height = getAttribute(aHeight_SVG);
//        String width = getAttribute(aWidth_SVG);
//        String x = getAttribute(aX);
//        String y = getAttribute(aY);
//
//        // TODO :
//        image = new Image();
//        image.setHeight(height);
//        image.setWidth(width);
//        image.setXPosition(x);
//        image.setYPosition(y);
//
//        //listener.updateChart(chart);
//        listener.updateImage(image);
//
////        //traverseNode(nFrame);
//    }

//    void processImageNode() throws IOException, XmlPullParserException
//    {
//        assertStartTag(nImage);
//
//        image.setHref(getAttribute(aHref));
//        image.setType(getAttribute(aType));
//        image.setShow(getAttribute(aShow));
//        image.setActuate(getAttribute(aActuate));
//
//        if(textBuff.length() > 0)
//        {
//            // TODO : dont know as what will come in this <text:p> tag
//
//            textBuff.setLength(0);
//        }
//    }

//    void processChartNode() throws IOException, XmlPullParserException
//    {
//        assertStartTag(nChart);
//
//        chart.setType(getAttribute(aClass));// get the chat type
//
//        traverseNode(nFrame);
//    }

//    void processLegendNode() throws IOException, XmlPullParserException
//    {
//        assertStartTag(nLegend);
//
//        chart.setShowLegend(true);
//    }

//    void processTitleNode() throws IOException, XmlPullParserException
//    {
//        assertStartTag(nTitle);
//
//        traverseNode(nTitle);
//
//        if(textBuff.length() > 0)
//        {
//            chart.setTitle(textBuff.toString());
//            textBuff.setLength(0);
//        }
//    }

//    void processPlotAreaNode() throws IOException, XmlPullParserException
//    {
//        assertStartTag(nPlotArea);
//
//
//    }

//    void processAxisNode() throws IOException, XmlPullParserException
//    {
//        assertStartTag(nAxis);
//
//        String dimension = getAttribute(aDimension);
//
//        traverseNode(nAxis);
//
//        if(textBuff.length() > 0)
//        {
//            if("x".equals(dimension))
//            {
//                chart.setXAxisTitle(textBuff.toString());
//            }
//            else if("y".equals(dimension))
//            {
//                chart.setYAxisTitle(textBuff.toString());
//            }
//            else if("z".equals(dimension))
//            {
//                chart.setZAxisTitle(textBuff.toString());
//            }
//            textBuff.setLength(0);
//        }
//    }

    void processNamedRangeNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nNamedRange);

        String name = getAttribute(aTableName);//getAttribute(aRangeName);
        String cellRangeAddress = getAttribute(aCellRangeAddress);
        String baseCellAddress = getAttribute(aBaseCellAddress_Range);
        //logger.info(name);

        if(name.startsWith(EngineConstants.PROTECTEDRANGE_NAME)) // Not a named Range but protected range.
        {
            String authUsers = getAttribute(ATABLE_AUTHUSERS);
            String unAuthUsers = getAttribute(ATABLE_UNAUTHUSERS);
            String authGroups = getAttribute(ATABLE_AUTHGROUPS);
            String authOrgs = getAttribute(ATABLE_AUTHORGS);
            String isPubAuthorized = getAttribute(ATABLE_IS_PUBLIC_AUTHORIZED);
            boolean isAllowInsert = getAttribute(ATABLE_IS_ALLOW_INSERT) != null && Boolean.parseBoolean(getAttribute(ATABLE_IS_ALLOW_INSERT));
            boolean isAllowFormats = getAttribute(ATABLE_IS_ALLOW_FORMATS) != null && Boolean.parseBoolean(getAttribute(ATABLE_IS_ALLOW_FORMATS));
            String authExternalShareLinks = getAttribute(ATABLE_AUTHORIZEDEXTERNALSHARELINKS);
            String unAuthExternalShareLinks = getAttribute(ATABLE_UNAUTHORIZEDEXTERNALSHARELINKS);

            listener.updateProtectedRange(changeFormula(cellRangeAddress), baseCellAddress, authUsers, unAuthUsers, authGroups, authOrgs, isPubAuthorized, isAllowInsert, isAllowFormats, authExternalShareLinks, unAuthExternalShareLinks);
        }
        else if(name.startsWith(EngineConstants.FORMRANGE_NAME)) // Not a named Range but protected range.
        {
            listener.updateFormRange(changeFormula(cellRangeAddress), baseCellAddress);
        } 
        else if(name.startsWith(EngineConstants.DISCUSSIONRANGE_NAME)) //  Comment range.
        {
            listener.updateCommentRange(name, changeFormula(cellRangeAddress), baseCellAddress);
        }
        else if(name.startsWith(EngineConstants.CHECKBOXRANGE_NAME))
        {
            listener.updateCheckboxRange(changeFormula(cellRangeAddress), baseCellAddress);
        }
        else if(name.startsWith(EngineConstants.PLACEHOLDER_NAME)) {
            String placeholderDesc = getAttribute(ATABLE_PLACEHOLDER_DESC);
            listener.updatePlaceholder(name, baseCellAddress, changeFormula(cellRangeAddress), placeholderDesc);
        }
        else
        {
            listener.updateNamedExpression(name, changeFormula(cellRangeAddress), baseCellAddress, true);
        }

        //traverseNode(nNamedRange);
    }

    protected void processNamedExpressionNode() throws IOException, XmlPullParserException
    {

        assertStartTag(nNamedExpression);

        String name = getAttribute(aTableName);
        String namedExpression = getAttribute(aExpression);
        String baseCellAddress = getAttribute(aBaseCellAddress_Range);

        listener.updateNamedExpression(name, changeFormula(namedExpression), baseCellAddress, false);

        //traverseNode(nNamedRange);
    }

    protected void processDataRangeNode() throws IOException, XmlPullParserException
    {
    	assertStartTag(nDatabaseRange);
        String targetRangeAddress = getAttribute(aTargetRangeAddress);
        if(targetRangeAddress != null)
        {
            traverseNode(nDatabaseRange);
            listener.updateDefaultFilterView(targetRangeAddress, filter);
        }
        filter = null;
    }

    protected void processFilterNode() throws IOException, XmlPullParserException
    {
        setFilterNode(FilterLogicalOperator.NONE);
    	traverseNode(nFilter);
    }

    protected void processFilterORNode() throws IOException, XmlPullParserException
    {
    	setFilterNode(FilterLogicalOperator.OR);
    	traverseNode(nFilterOR);
    }

    protected void processFilterANDNode() throws IOException, XmlPullParserException
    {
    	setFilterNode(FilterLogicalOperator.AND);
    	traverseNode(nFilterAND);
    }

    protected void setFilterNode(FilterLogicalOperator type)
    {
        Filter newFilter = new Filter(type);
        if(filter == null)
        {
            filter = newFilter;
        }
        else
        {
            while(filter.getChildFilter() != null)
            {
                filter = filter.getChildFilter();
            }
            filter.setChildFilter(newFilter);
        }
    }

    protected void processFilterConditionNode()
    {
        try
        {
            int fieldNumber = Utility.masknull(getAttribute(aFieldNumber),0);
            FilterOperator operator = FilterOperator.getFilterOperator(getAttribute(aOperator));
            DataType dataType = DataType.getDataType(getAttribute(aDataType));
            String value = getAttribute(aDataValue);
            FilterType filterType = FilterType.getFilterType(value);
            FilterCondition filterCondition;
            if(filterType.equals(FilterType.FILTER_BY_VALUE))
            {
                filterCondition = new FilterCondition(filterType, operator, dataType, fieldNumber, value);
            }
            else
            {
                ZSColor hexColor = ZSColor.getInstance(FilterType.getExactData(filterType, value));
                filterCondition = new FilterCondition(filterType, operator, dataType, fieldNumber, hexColor);
            }
            Filter filterToAddCondition = filter;
            while(filterToAddCondition.getChildFilter() != null)
            {
                filterToAddCondition = filterToAddCondition.getChildFilter();
            }
            filterToAddCondition.addCondition(filterCondition); 
        }
        catch(Exception e)
        {
            logger.log(Level.SEVERE, "[FILTERS][Exception] Exception while adding filter condition", e);
        }
    }

    protected static Set<String> createSetFromString(String setStr)
    {
        if(setStr != null && !setStr.trim().isEmpty())
        {
            Set<String> set = new HashSet<>();
            String zuids[] = setStr.trim().substring(1, setStr.length() - 1).split(",");
            for(String zuid : zuids)
            {
                set.add(zuid.trim());
            }
            return set;
        }

        return null;
    }
    
    protected static Map<String, Integer> createMapFromString(String setStr)
    {
        if(setStr != null && !setStr.trim().isEmpty())
        {
            Map<String, Integer> map = new HashMap<>();
            setStr = setStr.trim().substring(1, setStr.length() - 1);
            if(!setStr.isEmpty()) // ELSE --> Doesn't have sheet level dependencies.
            {
                String zuids[] = setStr.split(",");            
                for(String zuid : zuids)
                {   
                    String objects[] = zuid.trim().split("=");
                    map.put(objects[0], Integer.parseInt(objects[1]));
                }
            }
            return map;
        }
        return null;
    }
    
    void processSpreadSheetNode() throws IOException, XmlPullParserException {
        assertStartTag(nSpreadSheet);
        String protectionKey = getAttribute(aTableProtectionKey);
        this.listener.startWorkbook(protectionKey);
        traverseNode(nSpreadSheet);
    }

    void processSheetNode(boolean isTraverseData) throws IOException, XmlPullParserException
    {
        assertStartTag(nTable);
        //incrSheetIndex();
        //sheet.sheetName = getAttribute(aTableName);
        //sheet.index = sheetIndex;
        Sheet tempSheet = new Sheet();
        tempSheet.setName(getAttribute(aTableName));
        tempSheet.setStyleName(getAttribute(aTableStyleName));

        // Reading custom Attributes.
    // for associated name
    tempSheet.setAssociatedName(getAttribute(aTableId));

        EnumMap permissionMap = Protection.createPermissionMap(createSetFromString(getAttribute(ATABLE_AUTHUSERS)), 
                                           createSetFromString(getAttribute(ATABLE_UNAUTHUSERS)), 
                                           createSetFromString(getAttribute(ATABLE_AUTHGROUPS)),
                                           createSetFromString(getAttribute(ATABLE_AUTHORGS)),
                                           getAttribute(ATABLE_IS_PUBLIC_AUTHORIZED) == null ? null : Boolean.parseBoolean(getAttribute(ATABLE_IS_PUBLIC_AUTHORIZED)),
                                        		   createSetFromString(getAttribute(ATABLE_AUTHORIZEDEXTERNALSHARELINKS)), 
                                                   createSetFromString(getAttribute(ATABLE_UNAUTHORIZEDEXTERNALSHARELINKS)));
        
        Protection protection = new Protection(permissionMap);
        if(!protection.isMapEmpty())
        {
            tempSheet.setProtection(protection);
        }
        
        ////// End of custom Attributes.

        //ADDED BY GANESH
        tempSheet.setPrintStatus(getAttribute(aTablePrint));
        tempSheet.setPrintRanges(getAttribute(aTablePrintRanges));
        tempSheet.setIsProtected(Boolean.parseBoolean(getAttribute(aTableProtected)));
        tempSheet.setProtectionKey(getAttribute(aTableProtectionKey));
        //END OF GANESH

        try {
            listener.startSheet(tempSheet);
        } catch(IllegalStateException e) {
            /********************* TEMPORARY CHECK ******************/
            /* TO FIX A ORDER.JSONs / content.xml corrupt by a bug (support issue: #22557974 #22553140) */
            /*******************************************************/
            XmlPullUtil.skipSubTree(xpp);
            return;
        }
        
        ///// To get the workbook, the below block of code should be after startsheet call.
        //// sheetDependencyMap --> null, not yet updated the sheet level dependencies. Else otherwise.
        if(tempSheet.getWorkbook() != null)
        {
            Map<String, Integer> sheetDependencyMap = createMapFromString(getAttribute(aTableSheetDependencyMap));
            if(sheetDependencyMap == null)
            {
                tempSheet.getWorkbook().setIsSheetDependencyUpdated(false);
            }
            else
            {
                    for(String key : sheetDependencyMap.keySet())
                    {
                            tempSheet.putSheetDependencyMap(key, sheetDependencyMap.get(key));
                    }
                    //tempSheet.getSheetDependencyMap().putAll(sheetDependencyMap);
                    tempSheet.getWorkbook().setIsSheetDependencyUpdated(true);
            }
        }
        ////
        
        // set the rowIndex to zero
    if(isTraverseData)
    {
        traverseNode(nTable);
    }
    else
    {
        XmlPullUtil.skipSubTree(xpp);
    }

    if(drawControlShapeList != null)
    {
        for(DrawControl drawControl : drawControlShapeList)
        {
        tempSheet.addDrawControlShape(drawControl);
        }
        drawControlShapeList = null;
    }
        listener.endSheet(tempSheet);

    }



    void processSheetColumnNode() throws IOException, XmlPullParserException
    {
    	assertStartTag(nTableColumn);
    	String styleName = getAttribute(aTableStyleName);
    	int nColsRepeated = Utility.masknull(getAttribute(aNoColsRepeated),1);
    	String defCellStyleName = getAttribute(aDefCellStyleName);

    	ColumnHeader columnHeader = new ColumnHeader();
    	columnHeader.setColsRepeated(nColsRepeated);
    	columnHeader.setStyleName(styleName);
    	columnHeader.setDefaultCellStyleName(defCellStyleName);

    	ColumnVisibility visibility = getAttribute(aVisibility) == null ? ColumnVisibility.VISIBLE : ColumnUtil.getVisibilityFromParser(getAttribute(aVisibility));

    	xpp.nextTag();
    	assertEndTag(nTableColumn);
    	//listener.updateColumnHeader(columnHeader);
    	// for RangeParser change
        publishSheetColumnNode(columnHeader, visibility);
    }

    void publishSheetColumnNode(ColumnHeader columnHeader, ColumnVisibility visibility) throws IOException, XmlPullParserException
    {
        listener.updateColumnHeader(columnHeader, visibility);
    }
    void processSheetRowNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nTableRow);
        Row row = new Row();
        row.setStyleName(getAttribute(aTableStyleName));
        //logger.info(getAttribute(aDefCellStyleName));
        row.setDefaultCellStyleName(getAttribute(aDefCellStyleName));
        int nRowsRepeated = Utility.masknull(getAttribute(aNoRowsRepeated),1);
        row.setRowsRepeated(nRowsRepeated);

        listener.startRow(row);
        // ROW VISIBILITY - VISIBLE DOESN'T REQUIRE TO BE SET.
        if(getAttribute(aVisibility) != null)
        {
            RowVisibility visibility = RowUtil.getVisibilityFromParser(getAttribute(aVisibility));
            listener.setRowVisibility(row, visibility);
        }
        traverseNode(nTableRow);
        //listener.endRow(row);
    // for RangeParser
    publishSheetRowNode(row);
    }

    void publishSheetRowNode(Row row) throws IOException, XmlPullParserException
    {
        listener.endRow(row);
    }


    void processCoveredCellNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nCoveredTableCell);
        //sheet.row.vCell.rep = Utility.masknull(getAttribute(aNoColsRepeated),1);
        //int colRepeated = Utility.masknull(getAttribute(aNoColsRepeated),1);

        //listener.updateDummyCell(sheet);

        //listener.updateDummyCellRepeated(colRepeated);
        //logger.info("Covered Cell : "+xpp.getAttributeCount());
        setCellDetails(nCoveredTableCell);
    }


    void processCellNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nTableCell);
//        sheet.row.cell.styleName = getAttribute(aTableStyleName);
//        sheet.row.cell.rep = Utility.masknull(getAttribute(aNoColsRepeated),1);
//        sheet.row.cell.colSpan = Utility.masknull(getAttribute(aNoColsSpanned),1);
//        sheet.row.cell.rowSpan = Utility.masknull(getAttribute(aNoRowsSpanned),1);
//        sheet.row.cell.valueType = Utility.masknull(getAttribute(aTableValueType),"string");
        setCellDetails(nTableCell);
    }

    void processDrawControlNode() throws IOException, XmlPullParserException
    {
        DrawControl drawControl = new DrawControl();
        drawControl.setDrawControl(getAttribute(aDrawControl));
        drawControl.setEndCellAddress(getAttribute(aEndCellAddress));
        drawControl.setEndX(getAttribute(aEndX));
        drawControl.setEndY(getAttribute(aEndY));
        drawControl.setZIndex(getAttribute(aZIndex));

        drawControl.setSvgHeight(getAttribute(aHeight_SVG));
        drawControl.setSvgWidth(getAttribute(aWidth_SVG));
        drawControl.setSvgX(getAttribute(aX));
        drawControl.setSvgY(getAttribute(aY));

        drawControl.setTextStyleName(getAttribute(aTextStyleName));
        if(isSheetScopeShape)
        {
            if(drawControlShapeList == null)
            {
            drawControlShapeList = new ArrayList<>();
            }
            drawControlShapeList.add(drawControl);
        }
        else
        {
            if(drawControlList == null)
            {
            drawControlList = new ArrayList<>();
            }
            drawControlList.add(drawControl);
        }
        // used in to be set to cell and then it will be set to null there
    }
    
    void processSparklinesNode() throws IOException, XmlPullParserException {        
        assertStartTag(ZSsparklines);
    }
    
    void processSparklinesGroupNode() throws IOException, XmlPullParserException {        
        assertStartTag(ZSsparklines_group);
        listener.updateSparklinesGroup();
    }
    
    void processSparklinePropertiesNode() throws IOException, XmlPullParserException {
        assertStartTag(properties);
        String typeString = getAttribute(type);
        boolean isMarkerRequired = Boolean.parseBoolean(getAttribute(Names.isMarkerRequired));

        String sparklineColor = getAttribute(Names.sparklineColor);
        sparklineColor = (sparklineColor == null || sparklineColor.equals("null")) ? null : sparklineColor;
        String sparklineColorTint = getAttribute(Names.SPARKLINE_COLOR_TINT);
        String sparklineColorTheme = getAttribute(Names.SPARKLINE_COLOR_THEME);

        String negativeColor = getAttribute(Names.negativeColor);
        negativeColor = (negativeColor == null || negativeColor.equals("null")) ? null : negativeColor;
        String negativeColorTint = getAttribute(Names.NEGATIVE_COLOR_TINT);
        String negativeColorTheme = getAttribute(Names.NEGATIVE_COLOR_THEME);

        String markerColor = getAttribute(Names.markerColor);
        markerColor = (markerColor == null || markerColor.equals("null")) ? null : markerColor;
        String markerColorTint = getAttribute(Names.MARKER_COLOR_TINT);
        String markerColorTheme = getAttribute(Names.MARKER_COLOR_THEME);

        String highPointColor = getAttribute(Names.highPointColor);
        highPointColor = (highPointColor == null || highPointColor.equals("null")) ? null : highPointColor;
        String highPointColorTint = getAttribute(Names.HIGH_POINT_TINT);
        String highPointColorTheme = getAttribute(Names.HIGH_POINT_THEME);

        String lowPointColor = getAttribute(Names.lowPointColor);
        lowPointColor = (lowPointColor == null || lowPointColor.equals("null")) ? null : lowPointColor;
        String lowPointColorTint = getAttribute(Names.LOW_POINT_TINT);
        String lowPointColorTheme = getAttribute(Names.LOW_POINT_THEME);

        String firstPointColor = getAttribute(Names.firstPointColor);
        firstPointColor = (firstPointColor == null || firstPointColor.equals("null")) ? null : firstPointColor;
        String firstPointColorTint = getAttribute(Names.FIRST_POINT_TINT);
        String firstPointColorTheme = getAttribute(Names.FIRST_POINT_THEME);

        String lastPointColor = getAttribute(Names.lastPointColor);
        lastPointColor = (lastPointColor == null || lastPointColor.equals("null")) ? null : lastPointColor;
        String lastPointColorTint = getAttribute(Names.LAST_POINT_TINT);
        String lastPointColorTheme = getAttribute(Names.LAST_POINT_THEME);

        String minType = getAttribute(Names.minimumType);
        String maxType = getAttribute(Names.maximumType);
        boolean isXAxisRequired = Boolean.parseBoolean(getAttribute(Names.isXAxisRequired));
        String hiddenCells = getAttribute(Names.showHiddenCells);
        String emptyCells = getAttribute(Names.showEmptyCells);
        boolean isReversed = Boolean.parseBoolean(getAttribute(Names.isXAxisReversed));
        listener.updateSparklineProperties(typeString,sparklineColor, sparklineColorTint, sparklineColorTheme, isMarkerRequired, markerColor, markerColorTint, markerColorTheme, negativeColor, negativeColorTint, negativeColorTheme, highPointColor, highPointColorTint, highPointColorTheme, lowPointColor, lowPointColorTint, lowPointColorTheme, firstPointColor, firstPointColorTint, firstPointColorTheme, lastPointColor, lastPointColorTint, lastPointColorTheme, minType, maxType, isXAxisRequired, hiddenCells, emptyCells, isReversed);
    }
    
    void processSparklineNode() throws IOException, XmlPullParserException {        
        assertStartTag(ZSsparkline);
        String sourceString = getAttribute(source);
        String destinationString = getAttribute(destination);
        String orientationString = getAttribute(orientation);
        String minValue = getAttribute(yAxisMinimum);
        String maxValue = getAttribute(yAxisMaximum);
        
        listener.updateSparkline(sourceString, destinationString, orientationString,minValue,maxValue);
    }

    void processConditionalFormatsNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nCondition_formats);
        listener.startConditionalFormats();
        traverseNode(nCondition_formats);
    }
    
    void processConditionFormatNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nCondition_format);
        String targetAddress = getAttribute(aTarget_address);
        String csPriority = getAttribute(aCSPriority);
        try
        {
            listener.startConditionalFormat(targetAddress, csPriority);
            traverseNode(nCondition_format);   
        }catch(SheetEngineException e)
        {
            logger.log(Level.INFO, "Unable to create range "+targetAddress, e);
            XmlPullUtil.skipSubTree(xpp);
        }            
    }
    
    void processConditionNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nCondition);
        String condition = changeFormula(getAttribute(aValueCALC));
        if(!unsupportedCondition(condition))
        {
            listener.updateCondition(getAttribute(aApplyStyleNameCALC), condition, getAttribute(aBaseCellAddressCALC));
        }
    }
    
    void processConditionDateNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDateCALC);
        String condition = getAttribute(aDateCALC);
        if(!unsupportedCondition(condition))
        {
            listener.updateCondition(getAttribute(aStyleCALC), condition, null);
        }
    }
    
    void processColorScaleNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nColor_scale);
        boolean csAutoColor = Boolean.valueOf(getAttribute(aCSAutoColor));
        String isShowValueString = getAttribute(aCSShowValue);
        boolean isHideText = (isShowValueString == null) ? Boolean.valueOf(getAttribute(aCSHideText)) 
                                                                        : !Boolean.valueOf(isShowValueString);
        traverseNode(nColor_scale);
        
        listener.updateColorScale(csAutoColor, isHideText);
    }
    
    void processColorScaleEntryNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nColor_Scale_entry);
        String cseColor = getAttribute(aColorScale_Color);
        String cseThemeColor = getAttribute(A_THEME_COLOR);
        String cseColorTint = getAttribute(A_COLOR_TINT);
        String aValue = getAttribute(aCS_Value);
        String cseType = getAttribute(aCS_Type);
        Entry_Type cstype = Entry_Type.valueOf(cseType.toUpperCase());
        String baseCellAddress;
        if(cstype == Entry_Type.FORMULA){
            aValue = changeFormula(aValue);
            baseCellAddress = getAttribute(aBaseCellAddressCALC);
        } else {
            baseCellAddress = null;
        }

        ZSColor cseZSColor = ZSColor.getColor(cseColor, cseThemeColor, cseColorTint);
        listener.updateColorScaleEntry(cstype, aValue, cseZSColor, baseCellAddress);
    }
    
    void processIconSetNode() throws IOException, XmlPullParserException
    {
        csShowValue = null;
        assertStartTag(nIcon_Set);
        //Maintaing a new "icon_set_name" which is maintained for unsupported icons which are available in ZSheet.
        iconSetName = getAttribute(aIcon_Set_Name);
        iconSetName = IconSet.replaceUnSupportedIconSetName((iconSetName == null) ? getAttribute(aIcon_Set_Type) : iconSetName, 0);
        iconId = 0;
        boolean iconDefaultSize = Boolean.valueOf(getAttribute(aIcon_Default_Size));
        boolean iconReverseOrder = Boolean.valueOf(getAttribute(aIcon_Reverse_Order));
        traverseNode(nIcon_Set);
        
        boolean isHideText = csShowValue != null && !Boolean.valueOf(csShowValue);
        listener.updateIconSet(iconSetName, iconDefaultSize, iconReverseOrder, false, isHideText);
    }
    
    void processIconSetAndDataBarEntryNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nFormatting_entry);
        csShowValue = csShowValue == null ? getAttribute(aCSShowValue) : csShowValue;
        
        String aValue = getAttribute(aCS_Value);
        String cseType = getAttribute(aCS_Type).replace('-', '_'); 
        Entry_Type cstype = Entry_Type.valueOf(cseType.toUpperCase());
        String baseCellAddress;
        if(cstype == Entry_Type.FORMULA){
            aValue = changeFormula(aValue);
            baseCellAddress = getAttribute(aBaseCellAddressCALC);
        } else {
            baseCellAddress = null;
        }
        String aIconName = getAttribute(aIconSet_Name);
        aIconName = aIconName== null ? iconSetName : aIconName;
        String tempIconID = getAttribute(aIconSet_ID);
        Integer aIconID = (tempIconID == null && iconId == null) ? null : tempIconID != null ? Integer.parseInt(tempIconID) : iconId++;

        if(aIconName == null || aIconID == null)
        {
            listener.updateDataBarEntry(cstype, aValue, baseCellAddress);
        }
        else{
            listener.updateIconSetEntry(cstype, aValue, aIconName, aIconID, getAttribute(aIconSet_Criteria), baseCellAddress);
        }
    }
    
    void processDataBarNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nData_Bar);
        
        boolean autoColor = Boolean.valueOf(getAttribute(aCSAutoColor));
        csShowValue = getAttribute(aCSShowValue);
        String direction = getAttribute(aData_Bar_Direction);
        DataBar.Direction dbDirection = direction != null ? DataBar.Direction.valueOf(direction.toUpperCase()) : DataBar.Direction.CONTEXT;
        String type = getAttribute(A_GRADIENT);
        DataBar.Type fillType;
        if(type != null) {
            fillType = Boolean.valueOf(type) ? DataBar.Type.GRADIENT : DataBar.Type.SOLID;
        } else {
            type = getAttribute(aData_Bar_Fill_Type);
            fillType = type != null ? DataBar.Type.valueOf(type.toUpperCase()) : DataBar.Type.GRADIENT;
        }
        ZSColor fillPColor = ZSColor.getColor(getAttribute(A_FILL_POSITIVE_COLOR), getAttribute(A_FILL_POSITIVE_THEME_COLOR), getAttribute(A_FILL_POSITIVE_TINT_COLOR));
        ZSColor fillNColor = ZSColor.getColor(getAttribute(A_FILL_NEGATIVE_COLOR), getAttribute(A_FILL_NEGATIVE_THEME_COLOR), getAttribute(A_FILL_NEGATIVE_TINT_COLOR));
        DataBar.Type borderType = type != null ? DataBar.Type.valueOf(type.toUpperCase()) : DataBar.Type.NONE;
        ZSColor borderPColor = ZSColor.getColor(getAttribute(A_BORDER_POSITIVE_COLOR),getAttribute(A_BORDER_POSITIVE_THEME_COLOR), getAttribute(A_BORDER_POSITIVE_TINT_COLOR));
        ZSColor borderNColor = ZSColor.getColor(getAttribute(A_BORDER_NEGATIVE_COLOR),getAttribute(A_BORDER_NEGATIVE_THEME_COLOR), getAttribute(A_BORDER_NEGATIVE_TINT_COLOR));
        String axis = getAttribute(A_AXIS_POSITION);
        DataBar.Axis axisPosition = axis != null ? DataBar.Axis.valueOf(axis.toUpperCase()) : DataBar.Axis.AUTOMATIC;
        ZSColor axisColor = ZSColor.getColor(getAttribute(A_AXIS_COLOR),getAttribute(A_AXIS_THEME_COLOR), getAttribute(A_AXIS_TINT_COLOR));
        iconId = null;
        traverseNode(nData_Bar);
        
        listener.updateDataBar(dbDirection, fillType, fillPColor, fillNColor, borderType, borderPColor, borderNColor, axisPosition, axisColor, autoColor, !Boolean.valueOf(csShowValue));
    }
    
    void processExpressionsNode() throws IOException, XmlPullParserException
    {
        assertStartTag(expressions);
        traverseNode(expressions);
    }
    
    void processExpressionNode() throws IOException, XmlPullParserException
    {
        assertStartTag(expression);
        String expressionNameString= getAttribute(expressionName);
        String expressionString= xpp.nextText();
        listener.putExpressionNameExpressionEntry(expressionNameString, expressionString);
    }
       
    
 
     
    private Date parseTime(String timeValue) throws ParseException
    {
        SimpleDateFormat sdf = new SimpleDateFormat("'PT'HH'H'mm'M'ss'S'");
        Date date = null;
        try
        {
            date = sdf.parse(timeValue);
        }
        catch(ParseException ex)
        {
            sdf = new SimpleDateFormat("-'PT'HH'H'mm'M'ss'S'");

            try
            {
                date = sdf.parse(timeValue);
            }
            catch(ParseException ex1)
            {
                sdf = new SimpleDateFormat("'PT'HH'H'mm'M'ss.S'S'");

                try
                {
                    date = sdf.parse(timeValue);
                }
                catch(ParseException ex2)
                {
                    sdf = new SimpleDateFormat("-'PT'HH'H'mm'M'ss.S'S'");

                    try
                    {
                        date = sdf.parse(timeValue);
                    }catch(ParseException ex3)
                    {
                        throw ex3;
                    }
                }
            }
        }

        Number tValue = DateUtil.convertDateToNumber(date);
        tValue = tValue.doubleValue() - 25569;         // Just parsing time will take date as '1/1/1970' in java
        return DateUtil.convertNumberToDate(tValue);    // So to get Oo's start date 30/12/1899 subtract 25569 days.
    }

    private static String changeFormula(String formula)
    {
        // For NamedExpressions or Ranges: $Sheet2.$A$10:.$A$14
        formula = formula.replace(":.", ":");

        // When an Excel file with formula =sum(1:1), Oo will change that to =sum(A1:AMJ1)
        // To avoid Error while formula evaluation change AMJ to IV( The maximum number of columns we support(256)).
        //formula = formula.replaceAll("(?<=[:[$]?]{1})AMJ", "IV");

        formula = Utility.trimAddInFunctions(Utility.parseFormula(formula));
        formula = Utility.changeDepricatedFunctionNames(formula);
        formula = Utility.changeXMLToObjectFunctionNames(formula);
        return formula;
    }
    
    private static Cell.Error convertOOErrorCode(String errorCode)
    {
        switch (errorCode) {
            case "Err:501":// NO I18N
            case "Err:511":// NO I18N
            case "Err:525":// NO I18N
                    return Cell.Error.NAME;
            case "Err:532":// NO I18N
                    return Cell.Error.DIV;
            case "Err:504":// NO I18N
            case "Err:519":// NO I18N
                    return Cell.Error.VALUE;
            // else if(errorCode.equals("Err:503") || errorCode.equals("Err:502"))
            case "Err:522":// NO I18N
                    return Cell.Error.CIRCULARREF;
            case "Err:503":// NO I18N
            case "Err:502":// NO I18N
            case "Err:523":// NO I18N
                    return Cell.Error.NUM;
            case "Err:508":// NO I18N
            case "Err:509":// NO I18N
            case "Err:510":// NO I18N
            case "Err:524":// NO I18N
                    return Cell.Error.REF;
            }

            return Cell.Error.UNKNOWN_ERROR;
    }

    /*
     *sets the attributes of cell or covered-cell tag
     *
     */
    public void setCellDetails(XmlName nodeName) throws IOException, XmlPullParserException 
    {
        String styleName = getAttribute(aTableStyleName);
        String valueType = Utility.masknull(getAttribute(aTableValueType), "empty");
        String calcextValueType = getAttribute(aCalcextValueType);
        //String repeatIndexString = getAttribute(aTableRepeatIndex);
        //String repeatChar = getAttribute(aTableRepeatChar);
        String contentValidationName = getAttribute(ATABLE_CONTENTVALIDATIONNAME);
        //String cellValue = getAttribute(aTableValue);
        String ignoreTypeMismatchError = getAttribute(aTypeError);
        

        int rowSpan = Utility.masknull(getAttribute(aNoRowsSpanned), 1);
        int colSpan = Utility.masknull(getAttribute(aNoColsSpanned), 1);
        int nColsRepeated = Utility.masknull(getAttribute(aNoColsRepeated), 1);

        int arrayRowsize = Utility.masknull(getAttribute(aMatrixRowsSpanned), 0);
        int arrayColsize = Utility.masknull(getAttribute(aMatrixColsSpanned), 0);

        boolean isArrayCell = arrayRowsize > 0 || arrayColsize > 0;
        if(!isArrayCell) {
            arrayRowsize = Utility.masknull(getAttribute(aMatrixAutoRowsSpanned), 1);
            arrayColsize = Utility.masknull(getAttribute(aMatrixAutoColsSpanned), 1);
        }

        int autoArrayErrorRowOffset = Utility.masknull(getAttribute(aMatrixAutoErrorRowOffset), -1);
        int autoArrayErrorColOffset = Utility.masknull(getAttribute(aMatrixAutoErrorColOffset), -1);

        boolean isPrefixApos = Utility.masknull(getAttribute(aQuotePrefix), 0) == 1;

        String cellLink = getAttribute(aCellLink);

        String picklistAndItemIndex = getAttribute(aPicklistValue);
        String picklistAndItemID = getAttribute(aPicklistId);
        picklistAndItemIndex = picklistAndItemIndex == null ? picklistAndItemID : picklistAndItemIndex;
        String picklistSourceID = getAttribute(aPicklistSourceID);

        Cell cell = new CellImpl();

        ((CellImpl)cell).setAutoArraySpanFromParser(arrayRowsize, arrayColsize, autoArrayErrorRowOffset, autoArrayErrorColOffset);

        ((CellImpl)cell).setLinkFromParser(cellLink);
        ((CellImpl)cell).setIgnoreError(ignoreTypeMismatchError != null ? Integer.parseInt(ignoreTypeMismatchError) : 0);
        ((CellImpl) cell).setStyleNameFromParser(styleName);
        ((CellImpl) cell).setContentValidationNameFromParser(contentValidationName);
        listener.updateMergeCells(cell, rowSpan, colSpan);

//        ((CellImpl)cell).setRowSpanFromParser(rowSpan);
//        ((CellImpl)cell).setColSpanFromParser(colSpan);
        cell.setColsRepeated(nColsRepeated);

        //String patternStr = this.getPattern(cell);
        Type type = Type.UNDEFINED;
        Object valueObj = null;

//	String eleName = xpp.getName();
//	if(!isNameSpaceAware)
//	{
//	    eleName = eleName.substring(eleName.indexOf(":")+1);
//	}
//        if(eleName.equals("covered-table-cell"))
//        {
//            ((CellImpl)cell).setIsCoveredCellFromParser(true);
//        }
        try 
        {
            if (null != valueType && picklistAndItemIndex == null)
            {
                switch (valueType) 
                {
                    case "float"://No I18N
                        type = Type.FLOAT;
                        valueObj = Double.parseDouble(getAttribute(aTableValue));
                        break;
                    case "percentage"://No I18N
                        type = Type.PERCENTAGE;
                        valueObj = Double.parseDouble(getAttribute(aTableValue));
                        break;

                    case "currency"://No I18N
                        type = Type.CURRENCY;
                        valueObj = Double.parseDouble(getAttribute(aTableValue));
                        ((CellImpl) cell).setCurrencyCode(getAttribute(aCurrency));
                        break;
                    case "time"://No I18N
                        type = Type.TIME;
                        valueObj = this.parseTime(getAttribute(aTableTimeVal));
                        break;
                    case "date"://No I18N
                        String tempValue = getAttribute(aTableDateVal);
                        // for data-type 1899-12-30T20:59:17.48161259804
                        try 
                        {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.S");	// No I18N
                            valueObj = sdf.parse(tempValue);
                            type = Type.DATETIME;
                        } catch (ParseException ex) 
                        {
                            try
                            {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");	// No I18N
                                valueObj = sdf.parse(tempValue);
                                type = Type.DATETIME;
                            } catch (ParseException exc)
                            {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");	// No I18N
                                valueObj = sdf.parse(tempValue);
                                type = Type.DATE;
                            }
                        }
                        break;
                    case "boolean"://No I18N
                        type = Type.BOOLEAN;
                        valueObj = Boolean.parseBoolean(getAttribute(aTableBoolVal));
                        break;
                    case "string"://No I18N
                        type = Type.STRING;
                        String strVal = getAttribute(aTableStringVal);
                        if (strVal != null) {

                            this.stringSize += strVal.length();
                            if(this.stringSize > EngineConstants.FILE_STRING_LIMIT)
                            {
                                throw new AbortParseError(AbortParseError.Cause.STRING_LIMIT);
                            }

                            valueObj = new ZSString(strVal, isPrefixApos);
                        }
                        break;
                    default:
                        break;
                }
            }
        } catch (ParseException e)
        {
            logger.log(Level.WARNING, null, e);
        }

        String expressionName = getAttribute(cellExpressionName);
        String formula;
        if (expressionName != null) {
            listener.putCellExpressionNameEntry(expressionName, cell);
            formula = null;
        } else {
            formula = getAttribute(aTableFormula);
            if(formula != null) {
                if(this.formulasCount++ > 100000) {
                    throw new RuntimeException("Formulas-limit-Exceeded");//No I18N
                }
                formula = changeFormula(formula);
                int equalIndex = formula.indexOf("=");
                if(equalIndex != -1) {
                    formula = formula.substring(equalIndex);
                }
                // ((CellImpl) cell).setFormulaFromParser(formula);
                this.listener.putCellToODSFormulaEntry(formula, cell);
            }
        }
        
        isTextNodeExist = false;
        traverseNode(nodeName);

        String cellContent = null;
        // If the length of textbuff is 0, it means the  <text:p> tag contains only empty string
        // if type is String then should create a Value object with type as string and empty string as valueObj
        // if type is not a String then just skip this part.

        // isTextNodeExist is a global variable previously if there is empty <text:p/> tag
        // and no value and type attribute then we don't create the content and value object
        // and next time when we write, we also don't create the empty <text:p/> tag. This case
        // arise when the formula result is "" empty string. This created problem in writing wrong file
        // and created problem for files which got corrupted during colsrepeated handling added.
        // Now this is fixed to write the empty <text:p/> tag.
        //if(textBuff.length() > 0 || type.equals(Type.STRING))
        if (isTextNodeExist)
        {
            if (type == Type.UNDEFINED && (formula != null || expressionName != null))
            {
                type = Type.STRING;
            }

            cellContent = textBuff.toString();
            //cell.setContent(textBuff.toString(),repeatIndexString,repeatChar);
            //logger.info("Cell Content : "+cell.getContent()+" Cell Type : "+cell.getType());
            if (type.equals(Type.STRING) && valueObj == null)
            {
                String strVal = cellContent;
                if (strVal != null) 
                {
                    this.stringSize += strVal.length();
                    if(this.stringSize > EngineConstants.FILE_STRING_LIMIT)
                    {
                        throw new AbortParseError(AbortParseError.Cause.STRING_LIMIT);
                    }

                    valueObj = new ZSString(strVal, isPrefixApos, linksInValue);
                }
            }
            textBuff.setLength(0);
        }
        
        ///////// This part is added for Older files. Can be removed after a while. Added on Apr 1, 2019
        if(cell.getLink() == null && linksInValue != null && linksInValue.size() == 1 && linksInValue.get(0).getLabel().equals(cellContent))
        {
            ((CellImpl)cell).setLinkFromParser(linksInValue.get(0).getUrl());
        }
        //////////////////////////////////////////////////
        

        ///////////////////////
        isTextNodeExist = false;
        ///////////////////////

        if (commentBuff.length() > 0) 
        {
            ((CellImpl) cell).setAnnotationFromParser(annotation);
            annotation.setContent(commentBuff.toString());
            commentBuff.setLength(0);
        }
        
        if(attachments.size() > 0)
        {
            cell.addAllAttachment(attachments);
            attachments = new LinkedList();
        }

        // if the cell has no value but has some pattern set the value type as undefined.
        if(picklistAndItemIndex == null)
        {
            if (valueObj == null) {
                type = Type.UNDEFINED;
            }
            Value value;
            // If the cell contains a Formula, with cellType != String and caclextValueType == ERROR
            // and the content starts with # or Error value Object must be of type Error
            if ((formula != null || expressionName != null) && (!type.equals(Type.STRING) || "ERROR".equalsIgnoreCase(calcextValueType)) && cellContent != null) {
                if (cellContent.startsWith("Err:")) {
                    Cell.Error error = convertOOErrorCode(cellContent);
                    value = Value.getInstance(Type.ERROR, error);
                } else if (cellContent.startsWith("#")) {
                    value = Value.getInstance(Type.ERROR, new Throwable(cellContent));
                } else {
                    value = Value.getInstance(type, valueObj);
                }
            } else {
                value = Value.getInstance(type, valueObj);
            }


            ((CellImpl) cell).setValueFromParser(value);

        }

        if (drawControlList != null) 
        {
            for (DrawControl drawControl : drawControlList) 
            {
                cell.addDrawControl(drawControl);
            }
            drawControlList = null;
        }

        //listener.updateCell(cell);
        //for RangeParser
        publishCellNode(cell,picklistAndItemIndex,picklistAndItemID, picklistSourceID, isArrayCell);

        // Reinitialize the links arraylist
        linksInValue = new ArrayList();
    }

    void publishCellNode(Cell cell, String picklistAndItemIndex, String picklistAndItemID, String picklistSourceID, boolean isArrayCell) throws IOException, XmlPullParserException
    {
            listener.updateCell(cell,picklistAndItemIndex, picklistAndItemID, picklistSourceID, isArrayCell);
    }

    void processAnnotateNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nAnnotation);
        // init
        insideAnnotate = true;

        annotation = new Annotation();


        annotation.setDisplay(getAttribute(aDisplay_OFFICE));
        annotation.setX(getAttribute(aX));
//        annotation.setAnchorPageNumber(getAttribute(aAnchorPageNumber));
//        annotation.setAnchorType(getAttribute(aAnchorType));
        annotation.setCaptionPointX(getAttribute(aCaptionPointX));
        annotation.setCaptionPointY(getAttribute(aCaptionPointY));
        annotation.setCornerRadius(getAttribute(aCornerRadius));
        annotation.setEndCellAddress(getAttribute(aEndCellAddress));
        annotation.setEndX(getAttribute(aEndX));
        annotation.setEndY(getAttribute(aEndY));
        annotation.setHeight(getAttribute(aHeight_SVG));
        annotation.setId(getAttribute(aId));
        annotation.setLayer(getAttribute(aLayer));
        annotation.setName(getAttribute(aName));
        /*
         * Not supporting graphic styles now
         * the below line must be uncommented when support for graphic style is added.
         */
        annotation.setStyleName(getAttribute(aDrawStyleName));
        annotation.setTextStyleName(getAttribute(aTextStyleName));
        annotation.setTransform(getAttribute(aTransform));
        annotation.setWidth(getAttribute(aWidth_SVG));
        annotation.setY(getAttribute(aY));
        annotation.setZIndex(getAttribute(aZIndex));


        traverseNode(nAnnotation);

        //String comment = commentBuff.toString();

        // reset
        insideAnnotate = false;
        linksInValue = new ArrayList<>();
        //commentBuff.setLength(0);

    }
    
    void processAttachmentNode()
    {
        String attachmentId = getAttribute(aAttachmentID);
        String attachmentName = getAttribute(aAttachmentName);
        
        Attachment attachment = new Attachment(attachmentId, attachmentName);
        attachments.add(attachment);
    }

    void processDateNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDate);

        if(xpp.next()==XmlPullParser.TEXT)
        {
            annotation.setDate(xpp.getText());
        }
    }

    void processTextNode() throws IOException, XmlPullParserException
    {
    	assertStartTag(nTextP);
        if (insideAnnotate)
        {
            annotation.setParagraphStyle(getAttribute(aParagraphStyle));
            commentBuff.append(extractFromTextNode());
            commentBuff.append("\n");
        }
        else if(isShapeNode) { 
            listener.updateCustomShapeParagraph(getAttribute(aParagraphStyle));            
            extractFromTextNode();
        }
        else
        {
        isTextNodeExist = true;
            if(textBuff.length() > 0)
            {
                textBuff.append("\n");
            }
            textBuff.append(extractFromTextNode());
        }
    }

    void processFrameNode() throws IOException, XmlPullParserException {
        
        XmlName node = nDrawFrame;
        assertStartTag(node);
        
        String name = getAttribute(aDrawName);//    draw:name

        String styleName = getAttribute(aDrawStyleName); //= "gr1";//    draw:style-name
        String textStyleName = getAttribute(aTextStyleName); //= "P1";//    draw:text-style-name

        String zIndex = getAttribute(aZIndex);//    draw:z-index
        String svgHeight = getAttribute(aHeight_SVG);//    svg:height
        String svgWidth = getAttribute(aWidth_SVG);//    svg:width
        String svgX = getAttribute(aX);//    svg:x
        String svgY = getAttribute(aY);//    svg:y

        String endCellAddress = getAttribute(aEndCellAddress);//    table:end-cell-address
        String endX = getAttribute(aEndX); //    table:end-x
        String endY = getAttribute(aEndY);//    table:end-y

        String tableBackground = getAttribute(aTableBackground);//    table:table-background

        String anchorPageNumber = getAttribute(aAnchorPageNumber);//    text:anchor-page-number
        String anchorType = getAttribute(aAnchorType);//    text:anchor-type

        String transform = getAttribute(aTransform);//    draw:transform
        frame = new Frame();
        frame.setName(name);
        frame.setStyleName(styleName);
        frame.setTextStyleName(textStyleName);
        frame.setzIndex(zIndex);
        frame.setSvgHeight(svgHeight);
        frame.setSvgWidth(svgWidth);
        frame.setSvgX(svgX);
        frame.setSvgY(svgY);
        frame.setEndCellAddress(endCellAddress);
        frame.setEndX(endX);
        frame.setEndY(endY);
        frame.setTableBackground(tableBackground);
        frame.setAnchorPageNumber(anchorPageNumber);
        frame.setAnchorType(anchorType);
        
        traverseNode(node);
        
        listener.updateFrameList(frame);
    }
    
    void processImageNode() throws IOException, XmlPullParserException {
        
        XmlName node = nDrawImage;
        assertStartTag(node);
        
        String actuate = getAttribute(aActuate);
        String href = getAttribute(aHref);
        String show = getAttribute(aShow);
        String type = getAttribute(aImageType);
        
        Image image = new Image();
        image.setXlinkActuate(actuate);
        image.setXlinkHref(href);
        image.setXlinkShow(show);
        image.setXlinkType(type);
        
        frame.setImage(image);
    }

    void processDrawObject() throws IOException, XmlPullParserException {
        XmlName node =N_DRAW_OBJECT;
        assertStartTag(node);
        String href = getAttribute(aHref);
        frame.setDrawObject(new DrawObject(href));
    }

    void processSvgTitleNode() throws IOException, XmlPullParserException {
        XmlName node = nSvgTitle;
        assertStartTag(node);
        int eventType = xpp.next();
        if(eventType == XmlPullParser.TEXT) {
            String title = xpp.getText();
            frame.setTitle(title);
        }
        
    }
    
    void processSvgDescNode() throws IOException, XmlPullParserException {
        XmlName node = nSvgDesc;
        assertStartTag(node);
        int eventType = xpp.next();
        if(eventType == XmlPullParser.TEXT) {
            String desc = xpp.getText();
            frame.setDesc(desc);
        }
        
    }
    
    private StringBuilder extractFromTextNode() throws IOException, XmlPullParserException
    {
    assertStartTag(nTextP);
        StringBuilder buff = new StringBuilder();
        String styleName = null;
        String link = null;
        int level = 1;
        //boolean isEmptySpaceNode = false;
    //logger.info("nTxtP : "+nTextP.lName);
        while (level > 0)
        {
            int eventType = xpp.next();
            int length = buff.length();
            switch(eventType)
            {
                case XmlPullParser.TEXT:
                	int flag = 0;
                    if((link != null || styleName != null) && !isShapeNode)
                    {                                           
                                            
                        RichStringProperties linkObj = new RichStringProperties(styleName, link, xpp.getText(), textBuff.length()+buff.length());
                        linksInValue.add(linkObj);             
                    
                        link = null; // reset
                        styleName = null;
                    }
                    else if(isShapeNode){
                        flag = 1;
                        RichStringProperties linkObj = new RichStringProperties(styleName, link,xpp.getText(), 0);
                        listener.updateTextInCustomShape(linkObj);
                        link = null; // reset
                        styleName = null;
                    }
                      if(flag == 0)
                        {                           
                            buff.append(xpp.getText());
                        }
                    break;
                case XmlPullParser.END_TAG:
                    --level;
                    break;
                case XmlPullParser.START_TAG:
                    //isEmptySpaceNode = false;
            String eleName = xpp.getName();
            String prefixTemp = xpp.getPrefix();
            ////////////
            if(!isNameSpaceAware)
            {
            prefixTemp = eleName.substring(0, eleName.indexOf(":"));
            eleName = eleName.substring(eleName.indexOf(":")+1);
            }
                    if(eleName.equals("tab"))//ZS7316 issue fix
                    {
                        buff.append("\t"); //No I18N
                    }
                    if(eleName.equals("s"))
                    {
                        String spaces = Utility.masknull(getAttribute(aTextC), "0");
                        int numOfSpaces = Integer.parseInt(spaces);
                        //logger.info("numOfSpaces >>>>> "+numOfSpaces);
                        if(numOfSpaces > 32)
                        {
                            numOfSpaces = 1;
                        }
                        do
                        {
                            buff.append(" ");
                            numOfSpaces--;
                        }while(numOfSpaces > 0);
                        //isEmptySpaceNode = true;
                    }
                    if(!insideAnnotate && nTextA.prefix.equals(prefixTemp) && nTextA.lName.equals(eleName))
            {
                        link = getAttribute(aHref);
                    }
                    if(eleName.equals("span"))
                    {
                        styleName = getAttribute(aTextStyle);
                        // The span may even appear for Link so here we now
                        // only save the style for Annotation. So lets check for null
                        // before setting the textstyle.
                        if(insideAnnotate)
                        {
                            annotation.setTextStyle(styleName);
                        }
                    }
                    ++level;
                    break;
            }
        }
        return buff;
    }

    void processDataPilotTableNode() throws IOException, XmlPullParserException
    {
        pivotTable = new PivotTable(true);
        pivotTable.setApplicationData(getAttribute(aApplicationData));
        pivotTable.setTargetRangeAddress(getAttribute(aTargetRangeAddress));
        pivotTable.setGrandTotalFromParser(getAttribute(aGrandTotal));
        pivotTable.setIgnoreEmptyRows(Boolean.valueOf(getAttribute(aIgnoreEmptyRows)));
        pivotTable.setIdentifyCatagories(Boolean.valueOf(getAttribute(aIdentifyCatagories)));
        
        if(getAttribute(aTheme)!=null && getAttribute(aSubTotal)==null){
            pivotTable.setOldVersion(true);
        }
        
        String themeConstant = null;
        
        if(getAttribute(aTheme)!=null){
            themeConstant = getAttribute(aTheme) ;
        }
        pivotTable.setThemeConstant(themeConstant);

        String styleName = Utility.masknull(getAttribute(A_STYLEINFO_NAME), Utility.masknull(themeConstant, "3tc1")); //No I18N
        pivotTable.setStyleName(styleName.contains("_")? styleName.split("_")[1]: styleName);

        if(getAttribute(aSubTotal)!=null){
             pivotTable.setSubTotalON(Boolean.valueOf(getAttribute(aSubTotal)));
        }
        if(getAttribute(A_REPEAT_LABELS)!=null){
            pivotTable.setRepeatLabelEnabled(Boolean.valueOf(getAttribute(A_REPEAT_LABELS)));
        }
        if(getAttribute(aRefresh)!=null){
            pivotTable.setRefreshOptFromParser(getAttribute(aRefresh));
        }else {
        	pivotTable.setAutoRefresh(true);
        }

        pivotTable.setHideErrors(Boolean.parseBoolean(Utility.masknull(getAttribute(aHideErrors), "true")));
        
        if(getAttribute(aExpand)!=null){
        	pivotTable.setExpandOptFromParser(getAttribute(aExpand));
        }else {
        	pivotTable.setAutoExpand(true);
        }
        
        if(getAttribute(aFaulty)!=null){
            String fltyStr = getAttribute(aFaulty);
            int faulty;
            if("false".equalsIgnoreCase(fltyStr))
            {
                faulty = 0;
            }
            else if("true".equalsIgnoreCase(fltyStr))
            {
                faulty = 1;
            }
            else {
                faulty = Integer.parseInt(fltyStr);
            }
        	pivotTable.setPivotFaulty(faulty);
        }
       
        String buttons = getAttribute(aButtons);
        if(buttons != null)
        {
            String[] buttonsArray = buttons.split(" ");
            for(String button : buttonsArray)
            {
                pivotTable.addButton(button);
            }
        }
        traverseNode(nDataPilotTable);
        listener.updatePivotTable(pivotTable);
    }
    
    void processSourceCellRangeNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nSourceCellRange);
        pivotTable.setSourceRangeAddress(getAttribute(aCellRangeAddress));
    }
    
    void processDataPilotFieldNode() throws IOException, XmlPullParserException
    {
        pivotField = new PivotField();
        
        pivotField.setSourceFieldName(getAttribute(aSourceFieldName));
        pivotField.setOrientationFromParser(getAttribute(aOrientation));
        String sUsedHierarchy = getAttribute(aUsedHierarchy);
        if(sUsedHierarchy != null)
        {
            pivotField.setUsedHierarchy(Integer.parseInt(getAttribute(aUsedHierarchy)));
        }
        pivotField.setFunctionFromParser(getAttribute(aFunction));
        pivotField.setIsDataLayoutField(getAttribute(aIsDataLayoutField));
        /////
        traverseNode(nDataPilotField);
        /////

        pivotTable.addPivotField(pivotField);
    }
    
    void processDataPilotFieldReferenceNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDataPilotFieldReference);
        PivotFieldReference pivotFieldReference = new PivotFieldReference();
        pivotFieldReference.setFieldName(getAttribute(aFieldName));
        pivotFieldReference.setMemberTypeFromParser(getAttribute(aMemberType));
        pivotFieldReference.setTypeFromParser(getAttribute(aType));
        /////
        pivotField.setPivotFieldReference(pivotFieldReference);
    }
    
    void processDataPilotLevelNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDataPilotLevel);
        PivotLevel pivotLevel = new PivotLevel();
        pivotLevel.setShowEmpty(Boolean.valueOf(getAttribute(aShowEmpty)));
        /////
        pivotField.setPivotLevel(pivotLevel);
    }
    
    void processDataPilotGroupsNode() throws IOException, XmlPullParserException
    {
    	assertStartTag(nDataPilotGroups);
    	 if(getAttribute(aStep)!=null){
    		 PivotGroups pivotGroups = new PivotGroups();
    	    	pivotGroups.setSourceFieldName(getAttribute(aSourceFieldName));
    	    	pivotGroups.setStart(getAttribute(aStart));
    	    	pivotGroups.setEnd(getAttribute(aEnd));
    	    	pivotGroups.setStep(Double.parseDouble(getAttribute(aStep)));
    	    	/////
    	    	pivotField.setPivotGroups(pivotGroups);
     }
    	
    }
    
    void processDataPilotGroupNode() throws IOException, XmlPullParserException
    {
    	assertStartTag(nDataPilotGroup);
    	PivotGroup pivotGroup = new PivotGroup();
    	pivotGroup.setName(getAttribute(aTableName));
    	
    	 /////
    	PivotGroups tempGroups = pivotField.getPivotGroups();
        tempGroups.setPivotGroup(pivotGroup);
    }
    void processDataPivotGroupMemberNode() throws IOException, XmlPullParserException
    {
    	assertStartTag(nDataPilotGroupMember);
    	PivotGroupMember pivotGroupMember = new PivotGroupMember();
    	pivotGroupMember.setGroupMemberName(getAttribute(aTableName));
    	
    	/////
    	PivotGroups tempGroups = pivotField.getPivotGroups();
    	PivotGroup tempGroup = tempGroups.getPivotGroup();
    	tempGroup.setPivotGroupMember(pivotGroupMember);
    }
    
    void processDataPilotDisplayInfoNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDataPilotDisplayInfo);
        PivotDisplayInfo pivotDisplayInfo = new PivotDisplayInfo();
        pivotDisplayInfo.setEnabled(Boolean.valueOf(getAttribute(aEnabled)));
        pivotDisplayInfo.setDataField(getAttribute(aDataField));
        pivotDisplayInfo.setMemberCount(Integer.parseInt(getAttribute(aMemberCount)));
        pivotDisplayInfo.setDisplayMemberModeFromParser(getAttribute(aDisplayMemberMode));
        if(getAttribute(aDataFieldIndex)!=null){
        		pivotDisplayInfo.setDataFieldIndex(Integer.parseInt(getAttribute(aDataFieldIndex)));
        }
        /////
        PivotLevel tempLevel = pivotField.getPivotLevel();
        tempLevel.setPivotDisplayInfo(pivotDisplayInfo);
    }
    void processDataPilotSortInfoNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDataPilotSortInfo);
        PivotSortInfo pivotSortInfo = new PivotSortInfo();
        pivotSortInfo.setOrderFromParser(getAttribute(aOrder));
        pivotSortInfo.setSortModeFromParser(getAttribute(aSortMode));
        
        String dataField = getAttribute(aDataField);
        //logger.info("dataField111: "+dataField);
        if(dataField != null)
        {
            //logger.info("dataField: "+dataField);
            pivotSortInfo.setDatafield(dataField);
            //logger.info("aDataFieldIndex: "+getAttribute(aDataFieldIndex));
            if(getAttribute(aDataFieldIndex)!=null){
                pivotSortInfo.setDataFieldIndex(Integer.parseInt(getAttribute(aDataFieldIndex)));
            }
            if(getAttribute(aFunction)!=null){
                pivotSortInfo.setFunction(getAttribute(aFunction));
            }
        }
        /////
        PivotLevel tempLevel = pivotField.getPivotLevel();
        tempLevel.setPivotSortInfo(pivotSortInfo);
    }
    
    void processDataPilotLayoutInfoNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDataPilotLayoutInfo);
        PivotLayoutInfo pivotLayoutInfo = new PivotLayoutInfo();
        pivotLayoutInfo.setAddEmptyLines(Boolean.valueOf(getAttribute(aAddEmptyLines)));
        pivotLayoutInfo.setLayoutModeFromParser(getAttribute(aLayoutMode));
        /////
        PivotLevel tempLevel = pivotField.getPivotLevel();
        tempLevel.setPivotLayoutInfo(pivotLayoutInfo);
    }
    
    void processDataPilotFilterInfoNode() throws IOException, XmlPullParserException
    {
    	assertStartTag(nDataPilotFilterInfo);
    	PivotFilterInfo pivotFilterInfo = new PivotFilterInfo();
    	pivotFilterInfo.setType(getAttribute(aType));
    	pivotFilterInfo.setOperator(getAttribute(aOperator));
    	pivotFilterInfo.setValue(getAttribute(aDataValue));
    	
    	String value1 = getAttribute(aDataValue1);
    	if(value1!=null) {
    		pivotFilterInfo.setValue1(value1);
    	}
    	String dataFieldIndex = getAttribute(aDataFieldIndex);
    	if(dataFieldIndex!=null) {
    		pivotFilterInfo.setDataFieldIndex(Integer.parseInt(getAttribute(aDataFieldIndex)));
    	}
    	/////
    	PivotLevel tempLevel = pivotField.getPivotLevel();
    	tempLevel.setPivotFilterInfo(pivotFilterInfo);
    }
    
    void processDataPilotMemberNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDataPilotMember);
        PivotMember pivotMember = new PivotMember();
        pivotMember.setName(getAttribute(aTableName));
        pivotMember.setDisplay(Boolean.valueOf(getAttribute(aDisplay)));
        pivotMember.setShowDetails(Boolean.valueOf(getAttribute(aShowDetails)));
        /////
        PivotLevel tempLevel = pivotField.getPivotLevel();
        tempLevel.addPivotMember(pivotMember);
    }
    
    void processDataPilotSubtotalNode() throws IOException, XmlPullParserException
    {
        assertStartTag(nDataPilotSubtotal);
        PivotSubtotal pivotSubtotal = new PivotSubtotal();
        pivotSubtotal.setFunctionFromParser(getAttribute(aFunction));
        /////
        PivotLevel tempLevel = pivotField.getPivotLevel();
        tempLevel.addPivotSubtotals(pivotSubtotal);
    }

    /********************** SHEET IMAGES ************************/

    void processSheetImagesNode() throws IOException, XmlPullParserException
    {
        assertStartTag(A_IMAGES);
        traverseNode(A_IMAGES);
    }

    void processSheetImageNode() throws IOException, XmlPullParserException
    {
        assertStartTag(A_IMAGE);
        int id = getAttribute(A_SHEET_IMAGE_ID) != null ? Integer.parseInt(getAttribute(A_SHEET_IMAGE_ID)) : -1;
        int imageID = Integer.parseInt(getAttribute(A_IMAGE_ID));
        int rowIndex = Integer.parseInt(getAttribute(A_IMAGE_ROW_INDEX));
        int columnIndex = Integer.parseInt(getAttribute(A_IMAGE_COLUMN_INDEX));
        double rowDiff = getAttribute(A_IMAGE_ROW_DIFF) != null ? Double.parseDouble(getAttribute(A_IMAGE_ROW_DIFF)) : 0.0;
        double columnDiff = getAttribute(A_IMAGE_COLUMN_DIFF) != null ? Double.parseDouble(getAttribute(A_IMAGE_COLUMN_DIFF)) : 0.0;
        double height = getAttribute(A_IMAGE_HEIGHT) != null ? Double.parseDouble(getAttribute(A_IMAGE_HEIGHT)) : 0.0;
        double width = getAttribute(A_IMAGE_WIDTH) != null ? Double.parseDouble(getAttribute(A_IMAGE_WIDTH)) : 0.0;
        listener.addSheetImage(id, imageID, height, width, rowIndex, columnIndex, rowDiff, columnDiff);
    }

    /********************** SHEET IMAGES ************************/
    
public void processCustomShapeNode() throws IOException, XmlPullParserException {
        try {
        	assertStartTag(N_DRAW_CUSTOM_SHAPE);
            isShapeNode = true;
            String id = getAttribute(aId);
            String endCellAddress = getAttribute(aEndCellAddress);
            String endX = getAttribute(aEndX);
            String endY = getAttribute(aEndY);
            String zIndex = getAttribute(aZIndex);
            String name = getAttribute(aName);
            String styleName = getAttribute(aDrawStyleName);
            String textStyleName = getAttribute(A_DRAW_TEXT_STYLE_NAME);
            String width = getAttribute(aWidth_SVG);
            String height = getAttribute(aHeight_SVG);
            String svgX = getAttribute(aX);
            String svgY = getAttribute(aY);
            listener.updateCustomShape(id, endCellAddress, endX, endY, zIndex, name, styleName, textStyleName, width, height, svgX, svgY);        	       
            traverseNode(N_DRAW_CUSTOM_SHAPE);

            isShapeNode = false;
        }
        catch(Exception e){
        	logger.log(Level.INFO, "[SHAPES] exception in parse CustomShapeNode", e);
        }
        
    }
    
    public void processEnhancedGeometryNode() throws IOException, XmlPullParserException {
        try
        {
	    	assertStartTag(N_DRAW_ENHANCED_GEOMETRY);
	        
	        String viewBox = getAttribute(A_DRAW_VIEW_BOX);
	        String textArea = getAttribute(A_DRAW_TEXT_AREAS);
	        String type = getAttribute(A_DRAW_TYPE);
	        String modifiers = getAttribute(A_DRAW_MODIFIERS);
	        String enhancedPath = getAttribute(A_DRAW_ENHANCED_PATH);
	        String gluePoints = getAttribute(A_DRAW_GLUE_POINTS);
	        String pathStretchPointX = getAttribute(A_DRAW_PATH_STRETCHPOINT_X);
	        String pathStretchPointY = getAttribute(A_DRAW_PATH_STRETCHPOINT_Y);
	        String mirrorHorizontal = getAttribute(A_DRAW_MIRROR_HORIZONTAL);
	        String mirrorVertical = getAttribute(A_DRAW_MIRROR_VERTICAL);
	        listener.updateEnhancedGeometry(viewBox, textArea, type, modifiers, enhancedPath, gluePoints, pathStretchPointX, pathStretchPointY, mirrorHorizontal, mirrorVertical);
	        traverseNode(N_DRAW_ENHANCED_GEOMETRY);
        }
        catch(Exception e){
        	logger.log(Level.INFO, "[SHAPES] exception in parse EnhancedGeometryNode", e);
        }
    }
//    
    public void processEquationNode() throws IOException, XmlPullParserException{
       try {
    	   String name = getAttribute(aDrawName);
           String formula = getAttribute(A_DRAW_FORMULA);
           Equation equation = new Equation();
           equation.setName(name);
           equation.setFormula(formula); 
           listener.updateEquation(equation);
       }
       catch(Exception e){
       	logger.log(Level.INFO, "[SHAPES] exception in parse EquationNode", e);
       }
    }
//    
    public void processHandleNode() throws IOException, XmlPullParserException{
    	try {
	        Handle handle = new Handle();
	        handle.setHandlePosition(getAttribute(A_DRAW_HANDLE_POSITION));
	        handle.setHandleRangeXMinimum(getAttribute(A_DRAW_HANDLE_RANGE_X_MINIMUM));
	        handle.setHandleRangeXMaximum(getAttribute(A_DRAW_HANDLE_RANGE_X_MAXIMUM));
	        handle.setHandleRangeYMinimum(getAttribute(A_DRAW_HANDLE_RANGE_Y_MINIMUM));
	        handle.setHandleRangeYMaximum(getAttribute(A_DRAW_HANDLE_RANGE_Y_MAXIMUM));
	        handle.setHandlePolar(getAttribute(A_DRAW_HANDLE_POLAR));
	        
	        handle.setHandleRadiusRangeMaximum(getAttribute(A_DRAW_HANDLE_RADIUS_RANGE_MAXIMUM));
	        handle.setHandleRadiusRangeMinimum(getAttribute(A_DRAW_HANDLE_RADIUS_RANGE_MINIMUM));
	        handle.setHandleSwitched(getAttribute(A_DRAW_HANDLE_SWITCHED));
	        
	        listener.updateHandle(handle);
    	}
    	catch(Exception e){
           	logger.log(Level.INFO, "[SHAPES] exception in parse HandleNode", e);
        }
    }
    
    public void processPolyLineNode() throws IOException, XmlPullParserException {
    	try {
	    	isShapeNode = true;
	    	String id = getAttribute(aId);
			String zIndex = getAttribute(aZIndex);
	        String styleName = getAttribute(aDrawStyleName);
	        String textStyleName = getAttribute(A_DRAW_TEXT_STYLE_NAME);
	        String width = getAttribute(aWidth_SVG);
	        String height = getAttribute(aHeight_SVG);
	        String viewBox = getAttribute(A_DRAW_VIEW_BOX);
	        String transform = getAttribute(A_DRAW_TRANSFORM);
	        String points = getAttribute(A_DRAW_POINTS);
	        listener.updatePolyLine(id, zIndex, styleName, textStyleName, width, height, viewBox, transform, points);
	        traverseNode(N_DRAW_POLY_LINE);

            isShapeNode = false;
    	}
    	catch(Exception e){
           	logger.log(Level.INFO, "[SHAPES] exception in parse PolyLineNode", e);
        }
    }

	public void processPathNode() throws IOException, XmlPullParserException {
		try {
			isShapeNode = true;
			String id = getAttribute(aId);
			String zIndex = getAttribute(aZIndex);
	        String styleName = getAttribute(aDrawStyleName);
	        String textStyleName = getAttribute(A_DRAW_TEXT_STYLE_NAME);
	        String width = getAttribute(aWidth_SVG);
	        String height = getAttribute(aHeight_SVG);
	        String viewBox = getAttribute(A_DRAW_VIEW_BOX);
	        String transform = getAttribute(A_DRAW_TRANSFORM);
	        String d = getAttribute(A_SVG_D);
	        listener.updatePath(id, zIndex, styleName, textStyleName, width, height, viewBox, transform, d);
	        traverseNode(N_DRAW_PATH);
            isShapeNode = false;
		}
		catch(Exception e){
           	logger.log(Level.INFO, "[SHAPES] exception in parse PathNode", e);
        }
	}

	public void processLineNode() throws IOException, XmlPullParserException {
		try {
			isShapeNode = true;
			String id = getAttribute(aId);
			String zIndex = getAttribute(aZIndex);
	        String styleName = getAttribute(aDrawStyleName);
	        String textStyleName = getAttribute(A_DRAW_TEXT_STYLE_NAME);
	        String x1 = getAttribute(A_SVG_X_1);
	        String x2 = getAttribute(A_SVG_X_2);
	        String y1 = getAttribute(A_SVG_Y_1);
	        String y2 = getAttribute(A_SVG_Y_2);
	        listener.updateLine(id, zIndex, styleName, textStyleName, x1, x2, y1, y2);
	        traverseNode(N_DRAW_LINE);
            isShapeNode = false;
		}
		catch(Exception e){
           	logger.log(Level.INFO, "[SHAPES] exception in parse LineNode", e);
        }
	}
	
	public void processEllipseNode() throws IOException, XmlPullParserException{
		try {
			isShapeNode = true;
			String id = getAttribute(aId);
			String zIndex = getAttribute(aZIndex);
	        String styleName = getAttribute(aDrawStyleName);
	        String textStyleName = getAttribute(A_DRAW_TEXT_STYLE_NAME);
	        String svgX = getAttribute(aX);
	        String svgY = getAttribute(aY); 
	        String width = getAttribute(aWidth_SVG);
	        String height = getAttribute(aHeight_SVG);
	        String kind = getAttribute(A_DRAW_KIND);
	        String startAngle = getAttribute(A_DRAW_START_ANGLE);
	        String endAngle = getAttribute(A_DRAW_END_ANGLE);
	        listener.updateEllipse(id, zIndex, styleName, textStyleName, height, width, svgX, svgY, startAngle, endAngle, kind);
	        traverseNode(N_DRAW_ELLIPSE);
            isShapeNode = false;
		}
		catch(Exception e){
           	logger.log(Level.INFO, "[SHAPES] exception in parse EllipseNode", e);
        }
	}	
	
	public void processAnchorNode() throws IOException, XmlPullParserException{
		try {
			String href = getAttribute(aHref);
			String type = getAttribute(A_TYPE_ANCHOR);
			ShapeAnchor anchor = new ShapeAnchor();	
			anchor.setHref(href);
			anchor.setType(type);
			listener.updateAnchor(anchor);
			traverseNode(nDrawA);
		}
		catch(Exception e){
           	logger.log(Level.INFO, "[SHAPES] exception in parse AnchorNode", e);
        }
	}

	public void processGroupNode() throws IOException, XmlPullParserException{
		try {
			String zIndex = getAttribute(aZIndex);
			ShapeGroup group = new ShapeGroup();
			group.setzIndex(zIndex);
			listener.updateGroup(group);
			traverseNode(nDrawG);
			listener.addShapeGroupToSheet();
		}
		catch(Exception e){
           	logger.log(Level.INFO, "[SHAPES] exception in parse GroupNode", e);
        }
	}

	public void processRowGroupNode()throws IOException, XmlPullParserException
    {
        listener.updateRowGroupStart();
        boolean isCollapse = "false".equals(getAttribute(aDisplay)); //No I18N
        this.traverseNode(nTableRowGroup);
        listener.updateRowGroupEnd(isCollapse);

    }

    public void processColumnGroupNode()throws IOException, XmlPullParserException
    {
        listener.updateColumnGroupStart();
        boolean isCollapse = "false".equals(getAttribute(aDisplay)); //No I18N
        this.traverseNode(nTableColumnGroup);
        listener.updateColumnGroupEnd(isCollapse);
    }

    //////////////////////////////////////

    String workbookName = null;
    /**
     * Getter for property workbookName.
     * @return Value of property workbookName.
     */
    public java.lang.String getWorkbookName()
    {
        return workbookName;
    }

    /**
     * Setter for property workbookName.
     * @param workbookName New value of property workbookName.
     */
    public void setWorkbookName(java.lang.String workbookName)
    {
        this.workbookName = workbookName;
    }

    public List<DrawControl> getDrawControlShapeList() {
        return this.drawControlShapeList;
    }

    public void setDrawControlShapeList(List<DrawControl> drawControlShapeList) {
        this.drawControlShapeList = drawControlShapeList;
    }
    
    public static void main(String args[]) throws Exception
    {
         AppResources.setProperty("server.home","/Users/<USER>/Documents/sheet/Build/xlsxAug3016_1/AdventNet/Sas/tomcat/webapps/ROOT/WEB-INF");
        ODSWorkbookTransformer transformer = new ODSWorkbookTransformer();
        transformer.constructWorkbook("workbook");//No I18N
        ODSWorkbookParser parser = new ODSWorkbookParser(transformer);
        parser.workbookName = "/Users/<USER>/SpreadSheets/PivotTables/Only page.ods";//No I18N
        parser.parse(parser.workbookName);

        Workbook book = transformer.transform();
        for(PivotTable pivotTable: book.getPivotTables()) {
            PivotComponent pComp = generatePivotComponent(book, null, pivotTable, true);
        pComp.populatePivotModel(book);
        }
//        Sheet sheet = book.getSheet(2);
//        book.updateCellDependencies();
//        logger.log(Level.INFO, "Sheet Name : {0}", sheet.getName());
//        List rows = sheet.getRows();
//        Iterator itrRow = rows.iterator();
//        //Cell c = sheet.getCell("A13");
//        Cell c = sheet.getCell(3,2);
//        logger.log(Level.INFO, "C here111 {0} - {1} : {2}", new Object[]{c, c.getRowIndex(), c.getColumnIndex()});
//        while(itrRow.hasNext())
//        {
//            Row row = (Row)itrRow.next();
//            List list = row.getCells();
//            Iterator itr = list.iterator();
//            while(itr.hasNext())
//            {
//                Cell obj = (Cell)itr.next();
//                //logger.info("Cell : "+obj.getCellRef() + " : "+obj.getCellType() + " : "+obj.getContent());
//                if(obj != null)
//                {
//                    logger.log(Level.INFO, "{0} : {1} : {2}", new Object[]{obj.getRowIndex(), obj.getColumnIndex(), obj.toString()});
//				}
//                else
//                {
//                    logger.info("Its null ........");
//				}
//            }
//        }
//
//        List list1 = new ArrayList();
//        Cell cell = new CellImpl();
//        cell.setValue(new Value("Nilam", Locale.getDefault()));//NO I18N
//        cell.setContent("nilam");//NO I18N
//        list1.add(cell);
//        List list2 = (List)((ArrayList)list1).clone();

        //Parser.eval(sheet, cell);


    }

    private static boolean unsupportedCondition(String condStr)
    {
        return condStr.startsWith(UnsupportedMapStyles.XML_TOP_10_ELEMENTS) || condStr.startsWith(UnsupportedMapStyles.XML_BOTTOM_10_ELEMENTS) || condStr.startsWith(UnsupportedMapStyles.XML_TOP_10_PERCENT) 
                ||condStr.startsWith(UnsupportedMapStyles.XML_BOTTOM_10_PERCENT) || condStr.startsWith(UnsupportedMapStyles.XML_ABOVE_AVERAGE) || condStr.startsWith(UnsupportedMapStyles.XML_BELOW_AVERAGE) 
                || condStr.startsWith(UnsupportedMapStyles.XML_ABOVE_OR_EQUAL_AVERAGE) || condStr.startsWith(UnsupportedMapStyles.XML_BELOW_OR_EQUAL_AVERAGE) || condStr.startsWith(UnsupportedMapStyles.XML_DUPLICATE) 
                || condStr.startsWith(UnsupportedMapStyles.XML_NOT_DUPLICATE);
    }

    private class UnsupportedMapStyles{
        // Unsupproted Conditions.
        private static final String XML_TOP_10_ELEMENTS = "top-elements"; // No I18N
        private static final String XML_BOTTOM_10_ELEMENTS = "bottom-elements"; // No I18N
        private static final String XML_TOP_10_PERCENT = "top-percent"; // No I18N
        private static final String XML_BOTTOM_10_PERCENT = "bottom-percent"; // No I18N
        private static final String XML_ABOVE_AVERAGE = "above-average"; // No I18N
        private static final String XML_BELOW_AVERAGE = "below-avarage"; // No I18N
        private static final String XML_ABOVE_OR_EQUAL_AVERAGE = "above-equal-average"; // No I18N
        private static final String XML_BELOW_OR_EQUAL_AVERAGE = "below-equal-average"; // No I18N
        private static final String XML_DUPLICATE = "duplicate"; // No I18N
        private static final String XML_NOT_DUPLICATE = "unique"; // No I18N
    }


    ///////////////////////Dummy Transformer
     private class DummyTransformer implements ODSEventListener
    {
    public DummyTransformer()
    {
    }

        @Override
        public void updateStylesVersionId(int stylesVID){

        }

        @Override
        public void updateFontFace(FontFace fontFace)
        {
        }

        @Override
        public void updateColumnStyle(ColumnStyle columnStyle)
        {
        }

        @Override
        public void updateRowStyle(RowStyle rowStyle)
        {
        }

        @Override
        public void updateCellStyle(CellStyle cellStyle)
        {
        }

        @Override
        public void startSheet(Sheet sheet)
        {
        }

        @Override
        public void endSheet(Sheet sheet)
        {
        }

        @Override
        public void updateColumnHeader(ColumnHeader columnHeader, ColumnVisibility columnvisibility)
        {
        }

        @Override
        public void startRow(Row row)
        {
        }

        @Override
        public void endRow(Row row)
        {
        }

        @Override
        public void updateDummyCellRepeated(int colRepeated)
        {
        }

        @Override
        public void updateCell(Cell cell, String picklistAndItemIndex, String picklistAndItemID, String picklistSourceID, boolean isArrayCell)
        {
        }

        @Override
        public void constructWorkbook(String workbookName)
        {
        }

        @Override
        public void endWorkbook()
        {
        }

        @Override
        public void updateSheetStyle(SheetStyle sheetStyle)
        {
        }

        @Override
        public void updateForms(Forms forms)
        {
        }

        @Override
        public void updateGraphicStyle(GraphicStyle graphicStyle)
        {
        }

        @Override
        public void updateParagraphStyle(ParagraphStyle paragraphStyle)
        {
        }

        @Override
        public void updateTextStyle(TextStyle textStyle)
        {
        }

        @Override
        public void updateNumberStyle(NumberStyle numberStyle)
        {
        }

        @Override public ZSPattern updatePattern(String patternString, boolean isAccountingPattern, boolean isAutoOrder) {
        return null;
        }
        
        @Override
        public void updateMapStyle(String condition, String baseCellAddress, String applyStyleName, Object style)
        {
        }

        @Override
        public void updateNamedExpression(String name, String expression, String baseCellAddress, boolean isNamedRange)
        {
        }
        
        @Override
        public void updatePivotTable(PivotTable pivotTable)
        {
        }

        @Override
        public void updateProtectedRange(String cellRangeAddress, String baseCellAddress, String authUsersStr, String unAuthUsersStr, String authGroupsStr, String authOrgsStr, String isPubAuthorized, boolean isAllowInsert, boolean isAllowFormats, String authExternalShareLinksStr,
				String unAuthExternalShareLinksStr)
        {
        }

        @Override
        public void updateContentValidation(String condition, String baseCellAddress, String validationName, boolean isAllowEmptyCell, String displayList, DVHelpMessage helpMessage, DVErrorMessage errorMessage)
        {
        }

        @Override
        public void updatePicklist(String picklistID, String startsWith, String startsWithID, String rangeString, String sourceRangeString, String isRangePicklist) {
        }

        @Override
        public void updateListItem(String itemID, String displayString, String valueString, String textHex, String textTheme, String textTint, String bgHex, String bgTheme, String bgTint) {
        }

        @Override
        public void endPicklists() {
        }

        @Override
        public void updateFormRange(String cellRangeAddress, String baseCellAddress)
        {
        }
        
        @Override
        public void startConditionalFormats()
        {
        }

        @Override
        public void startConditionalFormat(String targetRangeAddress, String priority) throws SheetEngineException{}
    
        @Override
        public void updateCondition(String applyStyleName, String condition, String baseCellAddress){}

        @Override
        public void updateColorScale(boolean isAutoColor, boolean isHideText){}

        @Override
        public void updateColorScaleEntry(Entry_Type type, String value, ZSColor color, String baseCellAddress){}

        @Override
        public void updateIconSet(String iconSetName, boolean iconDefaultSize, boolean iconReverseOrder, boolean isAutoColor, boolean isHideText){}

        @Override
        public void updateIconSetEntry(Entry_Type cstype, String aValue, String aIconName, Integer aIconID, String iconCriteria, String baseCellAddress){}

        @Override
        public void updateDataBar(DataBar.Direction dbDirection, DataBar.Type fillType, ZSColor fillPColor, ZSColor fillNColor, DataBar.Type borderType, ZSColor borderPColor, ZSColor borderNColor, DataBar.Axis axisPosition, ZSColor axisColor, boolean isAutoColor, boolean isHideText){}

        @Override
        public void updateDataBarEntry(Entry_Type cstype, String aValue, String baseCellAddress){}
    
        @Override
        public void updateCommentRange(String name, String changeFormula, String baseCellAddress) {
        }

        @Override
        public void updateFrameList(Frame frame) {
            
        }
        @Override
        public void updateMergeCells(Cell cell, int rowSpan, int colSpan) {
        }

        @Override
        public void updateCheckboxRange(String cellRangeAddress, String baseCellAddress)
        {            
        }
        
        @Override
        public void putCellExpressionNameEntry(String formulaName, Cell cell){
        }

        @Override
        public void putCellToODSFormulaEntry(String formulaString, Cell cell){}
        
        @Override
        public void putExpressionNameExpressionEntry(String formulaName, String expressionString){
            
        }
        
        @Override
        public void createAndSetExpressionForReferredCells(){
        }
        
        @Override
        public void updateSparkline(String source, String destination, String orientation, String minValue, String maxValue) {
        }

        @Override
        public void updateSparklinesGroup() {            
        }

        @Override
        public void updateSparklineProperties(String type, String sparklineColor, String sparklineTint, String sparklineTheme, boolean isMarkerRequired, String markerColor, String markerTint, String markerTheme, String negativeColor, String negativeTint, String negativeTheme, String highPointColor, String highPointTint, String highPointTheme, String lowPointColor, String lowPointTint, String lowPointTheme, String firstPointColor, String firstPointTint, String firstPointTheme, String lastPointColor, String lastPointTint, String lastPointTheme, String minType, String maxType, boolean isXAxisRequired, String hiddenCells, String emptyCells, boolean isReversed) {
        }
        
        @Override
        public void publishDefaultColumnWidth(String defaultColumnWidthStr) {}

        @Override
        public void updatePlaceholder(String name, String baseCellAddress, String rangeAddress, String desc) {

        }

        @Override
        public void updateDefaultFilterView(String filterTable, Filter filteredRowsSet) {

        }

        @Override
		public void setRowVisibility(Row row, RowVisibility visibility) {
			// TODO Auto-generated method stub
			
		}

        @Override
        public void addSheetImage(int id, int imageID, double height, double width, int rowIndex, int columnIndex, double rowDiff, double columnDiff) {

        }

		@Override
		public void updateEnhancedGeometry(String viewBox, String textArea, String type, String modifiers,
				String enhancedPath, String gluePoints, String pathStretchPointX, String pathStretchPointY,
				String mirrorHorizontal, String mirrorVertical) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updateEquation(Equation equation) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updateHandle(Handle handle) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updateTextInCustomShape(RichStringProperties linkObj) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updateCustomShapeParagraph(String paragraphStyle) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updatePolyLine(String id, String zIndex, String styleName, String textStyleName, String width,
				String height, String viewBox, String transform, String points) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updatePath(String id, String zIndex, String styleName, String textStyleName, String width,
				String height, String viewBox, String transform, String d) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updateLine(String id, String zIndex, String styleName, String textStyleName, String x1, String x2,
				String y1, String y2) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updateEllipse(String id, String zIndex, String styleName, String textStyleName, String height,
				String width, String svgX, String svgY, String startAngle, String endAngle, String kind) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updateCustomShape(String id, String endCellAddress, String endX, String endY, String zIndex,
				String name, String styleName, String textStyleName, String width, String height, String svgX,
				String svgY){
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updateAnchor(ShapeAnchor anchor) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void updateGroup(ShapeGroup group) {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void addShapeGroupToSheet() {
			// TODO Auto-generated method stub
			
		}

    }

     private class MapStyleTemp
     {
         String condition;
         String basecellAddress;
         String applyStyleName;
         
         private MapStyleTemp(String condition, String baseCellAddress, String applyStyleName)
         {
             this.condition = condition;
             this.basecellAddress = baseCellAddress;
             this.applyStyleName = applyStyleName;
         }
     }

    public void parseODSFile(byte[] byteArrayInputStream) throws Exception{
        InputStream stream = null;
        ZSZipInputStream zipInputStream = null;
        try
        {
            long t0 = System.currentTimeMillis();

            stream = new ByteArrayInputStream(byteArrayInputStream);
            String fileToParse = "styles.xml"; //No I18N
            zipInputStream = new ZSZipInputStream(stream);
            List<String> traversedEntries = StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, fileToParse);
            if (!traversedEntries.isEmpty() && (fileToParse).equals(traversedEntries.get(traversedEntries.size() - 1))) {
                parseStream(zipInputStream);
            } else {
                logger.log(Level.WARNING, "️[READ-FORMAT-INKLING][ODS]  traversedEntries : {0}", new Object[]{traversedEntries});
                throw new Exception("Entry not available in stream for sheet: " + fileToParse);
            }

            long t1 = System.currentTimeMillis();

            fileToParse = "content.xml"; //No I18N
            // If already traversed, get the stream again and traverse from top
            if (traversedEntries.contains(fileToParse)) {
                StreamUtil.close(zipInputStream);
                StreamUtil.close(stream);
                stream = new ByteArrayInputStream(byteArrayInputStream);
                zipInputStream = new ZSZipInputStream(stream);
                traversedEntries = new ArrayList();
            }
            traversedEntries.addAll(StreamUtil.traverseZipInputStreamTillEntry(zipInputStream, fileToParse));
            if (!traversedEntries.isEmpty() && fileToParse.equals(traversedEntries.get(traversedEntries.size() - 1))) {
                parseStream(zipInputStream);
            } else {
                logger.log(Level.WARNING, "️[READ-FORMAT-INKLING][ODS]  traversedEntries : {0}", new Object[]{traversedEntries});
                throw new Exception("Entry not available in stream for sheet: " + fileToParse);
            }

            long t2 = System.currentTimeMillis();

            ZSLogger.log(logger, Level.INFO, "RESOURCE_KEY: {0} >>> Time taken in ODS File parser \n\tstyles: {1}\n\tcontent: {2}",
                    new Object[]{
                            "",
                            (t1 - t0),
                            (t2 - t1)
                    },
                    new ZSLogger.ZSCustomAppLogFieldKey[]{
                            ZSLogger.ZSCustomAppLogFieldKey.TIME_TAKEN
                    },
                    new Object[] {
                            (t2 - t0)
                    });

        }
        catch(Exception e)
        {
            throw (new Exception("Document.Parse.Error", e));
        }
        finally
        {
            try
            {
                if(zipInputStream != null) {
                    StreamUtil.close(zipInputStream);
                }
                if(stream != null) {
                    StreamUtil.close(stream);
                }
            }
            catch(Exception e1)
            {
                logger.log(Level.OFF, "", e1);
            }
        }

    }
}

