/* $Id$ */
package com.adventnet.zoho.websheet.model.parser;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.DataRange;

import java.util.Iterator;

/**
 * author - vengatesh.ss - 10141
 */
public class MarkDownRenderer extends ClipboardRenderer{
    String lastCell;
    StringBuilder lastRow;
    Boolean isHeaderNeeded = true;

    private static class NumberGenerator implements Iterator<Integer> {
        private final int end;
        private int current;
        public NumberGenerator(int start, int end) {
            this.end = end;
            this.current = start;
        }

        @Override
        public boolean hasNext() {
            return current <= end;
        }

        @Override
        public Integer next() {
            int result = current;
            current++;
            return result;
        }
    }


    private NumberGenerator numberGenerator = null;
    public MarkDownRenderer(WorkbookContainer container, Sheet sheet, int startRow, int startCol, int endRow, int endCol,ImageBook imagebook)
    {
        super(container,sheet, startRow, startCol, endRow, endCol,imagebook);
        builder = generateBuilderWithHeader(startCol, endCol);
        lastCell = "";
        lastRow = new StringBuilder();
        numberGenerator = new NumberGenerator(1, endRow - startRow + 2);
    }

    private  static String generateColumnNames(int colNum){
        StringBuilder columnName = new StringBuilder();
        while (colNum >= 0) {
            int remainder = colNum % 26;
            columnName.insert(0, (char) ('A' + remainder));//NO I18N
            colNum = (colNum / 26) - 1;
        }
        return columnName.toString();
    }

    private static String generateRowName(int rowNum){
        return Integer.toString(rowNum);
    }

    private String generateRowName(){
        //As of now, we are not using rowNum as it return current Row Number
        if(numberGenerator.hasNext()){
            return Integer.toString(numberGenerator.next());
        }
        return null;
    }

    private StringBuilder generateBuilderWithHeader(int startCol, int endCol){
        builder = new StringBuilder();
        int totalCol = endCol - startCol + 1;
        if(totalCol > 0){
            for(int start = 1; start <= totalCol; start++){
                builder.append("|");//NO I18N
                if(isHeaderNeeded){
                    builder.append(generateColumnNames(start - 1));
                }
                if(start == totalCol){
                    builder.append("|\n");//NO I18N
                }
            }
            for(int start = 1; start <= totalCol; start++){
                builder.append("|-");//NO I18N
                if(start == totalCol){
                    builder.append("|\n");//NO I18N
                }
            }
        }
        return builder;
    }

    @Override
    public void startRow(Row row)
    {
//        int rowNum = row.getRowIndex();
        lastRow = new StringBuilder("|");//NO I18N
        if(isHeaderNeeded){
            lastRow.append(generateRowName()).append("|");//NO I18N
        }
    }

    @Override
    public void endRow(boolean isLastRowInRange)
    {
        if(!isLastRowInRange)
        {
            lastRow.append("|\n"); // No I18N
        }else{
            lastRow.append("|"); // No I18N
        }
        builder.append(lastRow);
    }

    @Override
    public void repeatLastRow(boolean isLastRowInRange)
    {
        builder.append(lastRow);
    }


    @Override
    protected int getReadOnlyCell(ReadOnlyCell rCell, int rowSpan, int colSpan,int rowHeight)
    {
        String cV = "";
        int colIndex = rCell.getColIndex();
        int colsRepeated = Math.min(rCell.getColsRepeated(), endCol - colIndex + 1);
        Cell cell = rCell.getCell();
        if(cell != null && cell.getContent() != null)
        {
            cV = cell.getContent();
            if(cV.contains("\n"))
            {
                cV = cV.replaceAll("\n", " ");//NO I18N
            }
            if(cV.contains("|")){
                cV = cV.replaceAll("\\|", "\\\\|");//NO I18N
            }
            ////////////////////////
        }

        for(int q = 0; q < colsRepeated; q++) // Cols repeated
        {
            if(colIndex != startCol || q != 0)
            {
                lastRow.append("|");// No I18N
            }
            lastRow.append(cV);
        }
        return colsRepeated;
    }

    @Override
    public void setHost(String host, boolean isDownloadOff, boolean isRemoteMode) {}

    @Override
    protected void generateTableStyleMap(Sheet sheet, DataRange range) { }
}
