// $Id$
/*
 * ODSSheetParser.java
 *
 * Created on December 3, 2008, 4:36 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.parser;

import java.io.IOException;

import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.util.XmlPullUtil;

/**
 *
 * <AUTHOR>
 */
public class ODSSheetParser extends ODSWorkbookParser1
{

    private String sheetName;
    private int sheetIndex;
    private int curSheetIndex = 0;

    /** Creates a new instance of ODSSheetParser */
    public ODSSheetParser(ODSEventListener listener, String sheetName) throws XmlPullParserException
    {
	this(listener, sheetName, false);
    }

    public ODSSheetParser(ODSEventListener listener, String sheetName, boolean dataOnly) throws XmlPullParserException
    {
	
	super(listener, dataOnly);
	this.sheetName = sheetName;
    }

    public ODSSheetParser(ODSEventListener listener, int sheetIndex, boolean dataOnly) throws XmlPullParserException
    {
	super(listener, dataOnly);
	this.sheetIndex = sheetIndex;
    }

    protected void processSheetNode(boolean isTraverseData) throws IOException, XmlPullParserException
    {
	++curSheetIndex;
	if (isThisSheet())
        {
            super.processSheetNode(true);
            stop();
        }
        else
        {
            XmlPullUtil.skipSubTree(xpp);
        }

    }

    private boolean isThisSheet()
    {
        if(sheetName == null)
        {
            return (sheetIndex == curSheetIndex);
        }
        return sheetName.equalsIgnoreCase(getAttribute(aTableName));
    }


}
