//// $Id$
package com.adventnet.zoho.websheet.model.paste.clean;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.EmptyCellPredicates;
import com.adventnet.zoho.websheet.model.util.RangeUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.parser.Node;

import java.time.Instant;
import java.util.Collection;
import java.util.HashSet;

public class FillEmptyCells {
    private final Sheet sheet;
    private final int columnIndex;
    private final int startRowIndex;
    private final int endRowIndex;

    private final Collection<RangeUtil.SheetCell> filledCells = new HashSet<>();

    public FillEmptyCells(Sheet sheet, int columnIndex, int startRowIndex, int endRowIndex) {
        this.sheet = sheet;
        this.columnIndex = columnIndex;
        this.startRowIndex = startRowIndex;
        this.endRowIndex = endRowIndex;
    }

    public void fillWithPreviousValue() {
        Cell previousNonEmptyCell = null;
        Value previousValue = null;
        ZSPattern previousPattern = null;

        int continuousEmptyCellCount = 0;
        for(int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {
            Cell cell = sheet.getCell(rowIndex, columnIndex);
            if(EmptyCellPredicates.EMPTY_CELL_PREDICATE1.test(cell)) {
                if(previousNonEmptyCell != null) {

                    if (continuousEmptyCellCount == 0) {
                        previousValue = previousNonEmptyCell.getValue();
                        previousPattern = ((CellImpl) previousNonEmptyCell).getPattern(2);
                    }
                    cell.setValue(previousValue);
                    cell.setPattern(previousPattern);
                    continuousEmptyCellCount += 1;
                }
            }
            else
            {
                previousNonEmptyCell = cell;
                continuousEmptyCellCount = 0;
            }
        }
    }

    public void fillWithValue(Value value)
    {
        for(int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {
            Cell cell = sheet.getCell(rowIndex, columnIndex);
            if(EmptyCellPredicates.EMPTY_CELL_PREDICATE1.test(cell)) {
                cell.setValue(value);
            }
        }
    }

    public void fillWithMode() throws EvaluationException
    {
        Node modeExpressionNode = getNodeFor(sheet.getWorkbook(), new DataRange(sheet.getAssociatedName(), startRowIndex, columnIndex, endRowIndex, columnIndex), FillEmptyCells.Operation.MODE);
        Object valueObject = new ZSEvaluator(false).evaluate(modeExpressionNode, new CellImpl(new Row(sheet, 0), new Column(sheet, 0)), false, false);
        Value value = CellImpl.getAsValueObject(valueObject);
        if(value.getType() == Cell.Type.ERROR) {
            value = Value.EMPTY_VALUE;
        }
        fillWithValue(value);
    }

    public void fillWithAverage() throws EvaluationException
    {
        Node modeExpressionNode = getNodeFor(sheet.getWorkbook(), new DataRange(sheet.getAssociatedName(), startRowIndex, columnIndex, endRowIndex, columnIndex), FillEmptyCells.Operation.AVERAGE);
        Object valueObject = new ZSEvaluator(false).evaluate(modeExpressionNode, new CellImpl(new Row(sheet, 0), new Column(sheet, 0)), false, false);
        Value value = CellImpl.getAsValueObject(valueObject);
        fillWithValue(value);
    }

    private static Node getNodeFor(Workbook workbook, DataRange sheetRange, FillEmptyCells.Operation op) {
        String rangeString = sheetRange.toRange(workbook).getCompleteRangeStringForXML();

        StringBuilder formulaStringBuilder = new StringBuilder();
        switch(op) {
            case MODE:
                formulaStringBuilder
                        .append("=INDEX(")
                        .append(rangeString)
                        .append(";MODE(MATCH(FILTER(")
                        .append(rangeString)
                        .append(";NOT(ISBLANK(")
                        .append(rangeString)
                        .append(")));")
                        .append(rangeString)
                        .append(";0)))");
                break;
            case AVERAGE:
                formulaStringBuilder
                        .append("=AVERAGE(") //No I18N
                        .append(rangeString)
                        .append(")"); //No I18N
                break;
            default:
                throw new IllegalArgumentException();
        }

        Expression expression = workbook.getExpressionPool().getExpression(workbook, formulaStringBuilder.toString(), 0, 0, false, true, false, CellReference.ReferenceMode.A1);
        return expression.getNode();
    }
    private static enum Operation {
        MODE,
        AVERAGE
    }
}
