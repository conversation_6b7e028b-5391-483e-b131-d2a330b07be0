//// $Id$
package com.adventnet.zoho.websheet.model.paste.pastetype;

import com.adventnet.zoho.websheet.model.paste.pasteblock.Block;

/**
*
* <AUTHOR> - 2291
*/

public class PasteDiscussions extends Paste {
	
	public PasteDiscussions(Block pasteBlock){
        this.pasteBlock= pasteBlock;
    }

	@Override
	public boolean shouldCopyEntity(PasteEntitiesEnum entity) {
		// TODO Auto-generated method stub
		return entity == PasteEntitiesEnum.DISCUSSIONS;
	}

}
