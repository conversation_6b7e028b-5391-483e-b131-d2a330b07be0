//// $Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.paste.pastetype;

import com.adventnet.zoho.websheet.model.paste.pasteblock.Block;

/**
 *
 * <AUTHOR>
 */
public class PasteFormulasAndNumberFormats extends Paste{
    
    public PasteFormulasAndNumberFormats(Block pasteBlock){
        this.pasteBlock= pasteBlock;
    }
    
    @Override
    public boolean shouldCopyEntity(PasteEntitiesEnum entity){
        return (entity==PasteEntitiesEnum.FORMULA || entity==PasteEntitiesEnum.PATTERN || entity == PasteEntitiesEnum.PICKLIST_RANGE);
    }
}
