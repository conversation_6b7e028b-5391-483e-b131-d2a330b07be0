/* $Id$ */

package com.adventnet.zoho.websheet.model.field;

import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */

public class MergeFilter
{

    public enum Operator
    {
        EQUALS,
        NOT_EQUALS,
        CONTAINS,
        NOT_CONTAINS,
        GREATER_THAN,
        LESS_THAN,
        IS_EMPTY,
        IS_NOT_EMPTY;

        public static Operator fromString(String operator)
        {
            if (operator == null) {
                return Operator.CONTAINS;
            }

            switch (operator.toLowerCase()) {
                case "equals":
                    return Operator.EQUALS;
                case "not_equals":
                    return Operator.NOT_EQUALS;
                case "contains":
                    return Operator.CONTAINS;
                case "not_contains":
                    return Operator.NOT_CONTAINS;
                case "greater_than":
                    return Operator.GREATER_THAN;
                case "less_than":
                    return Operator.LESS_THAN;
                case "is_empty":
                    return Operator.IS_EMPTY;
                case "is_not_empty":
                    return Operator.IS_NOT_EMPTY;
                default:
                    return Operator.CONTAINS;
            }
        }
    }


    private String opVariable;
    private Operator operator;
    private String opConstant;

    public MergeFilter(JSONObject jObj)
    {
        if(jObj != null) {
            this.opVariable = jObj.optString("op_var");    //No I18N
            this.opConstant = jObj.optString("op_const");    //No I18N
            this.operator = Operator.fromString(jObj.optString("operator"));    //No I18N
        }
    }

    public MergeFilter(String opVariable, Operator operator, String opConstant)
    {
        this.opVariable = opVariable;
        this.operator = operator;
        this.opConstant = opConstant;
    }

    public String getOpVariable()
    {
        return this.opVariable;
    }

    public void setOpVariable(String opVariable)
    {
        this.opVariable = opVariable;
    }

    public Operator getOperator()
    {
        return this.operator;
    }

    public void setOperator(Operator operator)
    {
        this.operator = operator;
    }

    public String getOpConstant()
    {
        return this.opConstant;
    }

    public void setOpConstant(String opConstant)
    {
        this.opConstant = opConstant;
    }

    public JSONObject getAsJSONObj()
    {
        JSONObject jObj = new JSONObject();
        jObj.putOpt("op_var", this.opVariable);    //No I18N
        jObj.put("operator", (this.operator != null) ? this.operator.toString().toLowerCase() : null);    //No I18N
        jObj.putOpt("op_const", this.opConstant);    //No I18N
        return jObj;
    }

    public boolean isFilterConditionMatch(String variable)
    {
        if(variable == null)
        {
            return false;
        }

        switch(this.operator)
        {
            case EQUALS:
                return variable.equals(this.opConstant);
            case NOT_EQUALS:
                return !variable.equals(this.opConstant);
            case CONTAINS:
                return variable.contains(this.opConstant);
            case NOT_CONTAINS:
                return !variable.contains(this.opConstant);
            case IS_EMPTY:
                return variable.isEmpty();
            case IS_NOT_EMPTY:
                return !variable.isEmpty();
            case GREATER_THAN:
            case LESS_THAN:
                try
                {
                    Double var = Double.parseDouble(variable);
                    Double constant = Double.parseDouble(this.opConstant);
                    return (this.operator == Operator.GREATER_THAN) ? var > constant : var < constant;
                }
                catch(NumberFormatException e)
                {
                    return false;
                }
            default:
                return false;
        }
    }
}
