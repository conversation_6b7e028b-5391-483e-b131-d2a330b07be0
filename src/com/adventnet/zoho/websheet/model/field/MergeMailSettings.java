/* $Id$ */

package com.adventnet.zoho.websheet.model.field;

import com.adventnet.zoho.websheet.model.field.MergeFileSettings.FileType;

import java.util.ArrayList;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class MergeMailSettings
{
    private FileType fileType;
    private List<String> sendTo = null;
    private String subject;
    private String message;
    private String fileName;
    private boolean sendMeACopy;

    public MergeMailSettings(JSONObject jObj)
    {
        if(jObj != null)
        {
            List<String> sendTo = null;
            JSONArray sendToArray = jObj.optJSONArray("send_to");   //No I18N
            if(sendToArray != null)
            {
                sendTo = new ArrayList<>();
                for(int i = 0; i < sendToArray.length(); i++)
                {
                    sendTo.add(sendToArray.getString(i));
                }
            }
            this.sendTo = sendTo;
            this.subject = jObj.optString("subject");       //No I18N
            this.message = jObj.optString("message");       //No I18N
            this.fileName = jObj.optString("file_name");    //No I18N
            this.fileType = FileType.fromString(jObj.optString("file_type", "pdf"));   //No I18N
            this.sendMeACopy = jObj.optBoolean("send_me_a_copy", false); //No I18N
        }
    }

    public MergeMailSettings(List<String> sendTo, String subject, String message, String fileName, FileType fileType, boolean sendMeACopy)
    {
        this.sendTo = sendTo;
        this.subject = subject;
        this.message = message;
        this.fileName = fileName;
        this.fileType = fileType;
        this.sendMeACopy = sendMeACopy;
    }
    
    public void addMailID(String mailID)
    {
        if(sendTo == null)
        {
            sendTo = new ArrayList<>();
        }
        sendTo.add(mailID);
    }
    
    public void setSubject(String subject)
    {
        this.subject = subject;
    }

    public String getSubject() { return subject; }
    
    public void setMessage(String message)
    {
        this.message = message;
    }

    public String getMessage() { return message; }
    
    public boolean isSendMeACopy()
    {
        return this.sendMeACopy;
    }
    
    public FileType getFileType()
    {
        return this.fileType;
    }
    
    public String getFileName()
    {
        return this.fileName;
    }
    
    public void setFileName(String fileName)
    {
        this.fileName = fileName;
    }

    public String getSendToMailIDs() {
        JSONArray sendToArray = new JSONArray();
        if(!(this.sendTo == null || this.sendTo.isEmpty()))
        {
            this.sendTo.forEach((st) -> {
                sendToArray.put(st);
            });
        }
        return sendToArray.toString();
    }

    public JSONArray getSendToJSONArray() {
        JSONArray sendToArray = new JSONArray();
        if(!(this.sendTo == null || this.sendTo.isEmpty()))
        {
            this.sendTo.forEach((st) -> {
                sendToArray.put(st);
            });
        }
        return sendToArray;
    }
    
    public JSONObject getAsJSONObject()
    {
        JSONObject mergeMailSettingsObj = new JSONObject();
        mergeMailSettingsObj.putOpt("send_to", getSendToJSONArray());                //No I18N
        mergeMailSettingsObj.putOpt("subject", this.subject);                              //No I18N
        mergeMailSettingsObj.putOpt("message", this.message);                              //No I18N
        mergeMailSettingsObj.putOpt("file_name", fileName);                                //No I18N
        mergeMailSettingsObj.put("file_type", (this.fileType != null) ? this.fileType.toString().toLowerCase() : null);     //No I18N
        mergeMailSettingsObj.put("send_me_a_copy", this.sendMeACopy);                      //No I18N
        return mergeMailSettingsObj;
    }
}