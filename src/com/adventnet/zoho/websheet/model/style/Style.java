// $Id$
/*
 * Style.java
 *
 * Created on May 9, 2007, 4:09 PM
 */

package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.EngineTimeStamp;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import java.util.*;
import java.util.logging.Logger;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import java.util.logging.Level;

/**
 *
 * <AUTHOR>
 */
public abstract class Style extends EngineTimeStamp
{
    private static final Logger LOGGER = Logger.getLogger(Style.class.getName());
    
    private String styleName; 
    
    // CellStyle
    private String parenStyleName = EngineConstants.DEFAULT_CELLSTYLENAME;
    private ZSPattern pattern;

    // SheetStyle
    private String masterPageName;

    private String displayName;

    public abstract void getStylePropsFromPool(Workbook workbook);

    /**
     * Getter for property styleName.
     * @return Value of property styleName.
     */
    public java.lang.String getStyleName()
    {
        return styleName;
    }

    /**
     * Setter for property styleName.
     * @param styleName New value of property styleName.
     */
    public void setStyleName(java.lang.String styleName)
    {
        this.styleName = styleName;
    }

    /**
     * Getter for property parenStyleName.
     * @return Value of property parenStyleName.
     */
    public java.lang.String getParenStyleName()
    {
        return parenStyleName;
    }

    public Style getParentStyle(Workbook workbook)
    {
        if(this.parenStyleName == null)
        {
            if(this instanceof CellStyle)
            {
                return workbook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME);
            }
            return null;
        }
        else if(this instanceof CellStyle){
            return workbook.getCellStyle(this.parenStyleName);
        }
        else if(this instanceof TextStyle){
            return workbook.getTextStyle(this.parenStyleName);
        }
        else if(this instanceof ParagraphStyle){
            return workbook.getParagraphStyle(this.parenStyleName);
        }
        else if(this instanceof RowStyle){
            return workbook.getRowStyle(this.parenStyleName);
        }
        else if(this instanceof ColumnStyle){
            return workbook.getColumnStyle(this.parenStyleName);
        }
        else if(this instanceof SheetStyle){
            return workbook.getSheetStyle(this.parenStyleName);
        }
        else if(this instanceof GraphicStyle){
            return workbook.getGraphicStyle(this.parenStyleName);
        }
        else
        {
            throw new IllegalArgumentException("This is not yet supported..." + this); //No I18N
        }
    }
    
    /**
     * Setter for property parenStyleName.
     * @param inParentStyleName
     */
    public void setParenStyleName(java.lang.String inParentStyleName)
    {
        if(inParentStyleName ==  null)
        {
            inParentStyleName = EngineConstants.DEFAULT_CELLSTYLENAME;
        }
        this.parenStyleName = inParentStyleName;
    }
    
    /**
     * Getter for property masterPageName.
     * @return Value of property masterPageName.
     */
    public java.lang.String getMasterPageName()
    {
        return masterPageName;
    }
    
    /**
     * Setter for property masterPageName.
     * @param masterPageName New value of property masterPageName.
     */
    public void setMasterPageName(java.lang.String masterPageName)
    {
        this.masterPageName = masterPageName;
    }
      //////Added by Ganesh
    public ZSPattern getPattern()
    {
        return this.pattern;
    }
   
    public void setPattern(ZSPattern pattern)
    {
        this.pattern = pattern;
    }

    public String getDisplayName()
    {
        return displayName;
    }

    public void setDisplayName(String displayName)
    {
        this.displayName = displayName;
    }
    
    public abstract String getStyleFamily();
    
    public String[] getAttributes()
    {
        String[] styleAttrs = new String[]{"style:name", 
                                  "style:display-name",
                                  "style:family",
                                  "style:master-page-name",
                                  "style:parent-style-name",
                                  "style:data-style-name"};
        return styleAttrs;
    }
   
    public String[] getValues(Map<String, ZSPattern> patternMap)
    {
        if(pattern != null && !pattern.equals(DataStyleConstants.EMPTY_PATTERN) && (patternMap == null || patternMap.isEmpty()))
        {
            throw new IllegalArgumentException("Pattern map cannot be null or empty when style has a Pattern"); //NO I18N
        }

        String patternName = null;
        if(pattern != null)
        {
            if(pattern.equals(DataStyleConstants.EMPTY_PATTERN))
            {
                patternName = "Default"; // NO I18N
            }
            else
            {
                for (String storedName : patternMap.keySet()) {
                    ZSPattern zsPattern = patternMap.get(storedName);
                    if (pattern.equals(zsPattern)) {
                        patternName = storedName;
                        break;
                    }
                }
            }
        }

         String[] styleValues = new String[]{getStyleName(),
                                    getDisplayName(),
                                    getStyleFamily(),
                                    getMasterPageName(),
                                    EngineConstants.DEFAULT_CELLSTYLENAME.equals(this.parenStyleName) ? null : this.parenStyleName,
                                    patternName}; // datastylename should be last entry, if this is changed need to change in equalsIgnoreDataStyle() method
        return styleValues;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){ return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        Style style = (Style) o;
        return Objects.equals(parenStyleName, style.parenStyleName)
                && Objects.equals(masterPageName, style.masterPageName)
                && Objects.equals(displayName, style.displayName)
                && Objects.equals(pattern, style.pattern);
    }

    public boolean equalsIgnoreDataStyle(Object o) {
        if (this == o){ return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        Style style = (Style) o;
        return Objects.equals(parenStyleName, style.parenStyleName)
                && Objects.equals(masterPageName, style.masterPageName)
                && Objects.equals(displayName, style.displayName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(parenStyleName, masterPageName, displayName, pattern);
    }

    @Override
    public String toString() {
        return "Style{" + //NO I18N
                "styleName='" + styleName + '\'' + //NO I18N
                ", parenStyleName='" + parenStyleName + '\'' + //NO I18N
                ", masterPageName='" + masterPageName + '\'' + //NO I18N
                ", displayName='" + displayName + '\'' + //NO I18N
                ", pattern=" + (pattern == null ? "null" : pattern.toPatternString()) + //NO I18N
                '}';
    }

    public void setProperty(List<Property> propList, List<Object> values){
        if(propList.size() != values.size()){
            throw new IllegalArgumentException("Proplist and value list must be of same size..."); //No I18N
        }
        for (int i = 0; i < propList.size(); i++)
        {
            Property prop = propList.get(i);
            Object value = values.get(i);
            setProperty(prop, value);
        }
    }

    protected boolean isPropertyChanged(Style.Property propName, Object propValue){
        Object oldPropValue= this.getProperty(propName);
        Object newPropValue= propValue;
        return !(oldPropValue==null ? newPropValue == null : oldPropValue.equals(newPropValue));
    }

    public static interface Property{

        public static String getPropertyString(Object propertyValue, ZSTheme theme)
        {
            if(propertyValue == null)
            {
                return null;
            }
            else if(propertyValue instanceof String)
            {
                return (String)propertyValue;
            }
            else if(propertyValue instanceof ZSColor)
            {
                return ZSColor.getHexColor((ZSColor)propertyValue, theme);
            }
            else if(propertyValue instanceof BorderProperties)
            {
                return ((BorderProperties)propertyValue).getBorderString(theme);
            }
            else if(propertyValue instanceof CellStyleProperties.DisplayType)
            {
                return ((CellStyleProperties.DisplayType) propertyValue).getDisplayTypeValue();
            }
            else
            {
                LOGGER.log(Level.INFO, "SHOULD BE FIXED ::: Property value type is not handled... {0}", new Object[]{propertyValue});
                return String.valueOf(propertyValue);
            }
        }
    }

    enum PropertyInstance{
        CELLSTYLE,
        TEXTSTYLE,
        PARAGRAPHSTYLE,
        ROWSTYLE,
        COLUMNSTYLE,
        SHEETSTYLE,
        GRAPHICSTYLE
    }

    abstract StyleProperties getStyleProperties(PropertyInstance propertyInstance);

    public Object getProperty(Property property){
        if(property instanceof CellStyle.Property){
            if(property == CellStyle.Property.PATTERN)
            {
                return this.pattern;
            }
            return CellStyle.getProperty((CellStyleProperties) getStyleProperties(PropertyInstance.CELLSTYLE), (CellStyle.Property)property);
        }
        else if(property instanceof TextStyle.Property){
            return TextStyle.getProperty((TextStyleProperties) getStyleProperties(PropertyInstance.TEXTSTYLE), (TextStyle.Property)property);
        }
        else if(property instanceof ParagraphStyle.Property){
            return ParagraphStyle.getProperty((ParagraphStyleProperties) getStyleProperties(PropertyInstance.PARAGRAPHSTYLE), (ParagraphStyle.Property)property);
        }
        else if(property instanceof RowStyle.Property){
            return RowStyle.getProperty((RowStyleProperties) getStyleProperties(PropertyInstance.ROWSTYLE), (RowStyle.Property)property);
        }
        else if(property instanceof ColumnStyle.Property){
            return ColumnStyle.getProperty((ColumnStyleProperties) getStyleProperties(PropertyInstance.COLUMNSTYLE), (ColumnStyle.Property)property);
        }
        else if(property instanceof SheetStyle.Property){
            return SheetStyle.getProperty((SheetStyleProperties) getStyleProperties(PropertyInstance.SHEETSTYLE), (SheetStyle.Property)property);
        }
        else if(property instanceof GraphicStyle.Property){
            return GraphicStyle.getProperty((GraphicStyleProperties) getStyleProperties(PropertyInstance.GRAPHICSTYLE), (GraphicStyle.Property)property);
        }
        else{
            throw new IllegalArgumentException("This is not yet supported..." + property); //No I18N
        }
    }

    /**
     * This class variable {@code visited} controls looping of cellstyles.
     * The function execution is as like follows,
     * Eg [cs1 -> cs2 -> cs3 -> default_cs] depicts the relationship of cellstyles
     * Once a CS is visited, it is marked. If contains value it returns.
     * If value not found, getParentCS from workbook and repeat.
     * If looping occurs, recursion breaks and present value is returned.
     */
    private boolean visited = false;
    public Object getProperty_Deep(Property property, Workbook workbook){
        Object propertyValue = this.getProperty(property);
        if(propertyValue == null && !visited){
            this.visited = true;
            Style style = this.getParentStyle(workbook);
//            if(style == null && this instanceof CellStyle)
//            {
//                style = workbook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME);
//            }
            
            if(style != null)
            {
                propertyValue = style.getProperty_Deep(property, workbook);
            }
            this.visited = false;
        }
        return propertyValue;
    }

    public void setProperty(Property property, Object value){
        if(!isPropertyChanged(property, value)){
            return;
        }
        if(property instanceof CellStyle.Property){
            if(property == CellStyle.Property.PATTERN){
                this.pattern = (ZSPattern)value;
            }
            else {
                CellStyle.setProperty((CellStyleProperties) getStyleProperties(PropertyInstance.CELLSTYLE), (CellStyle.Property)property, value);
            }
        }
        else if(property instanceof TextStyle.Property){
            TextStyle.setProperty((TextStyleProperties) getStyleProperties(PropertyInstance.TEXTSTYLE), (TextStyle.Property)property, value);
        }
        else if(property instanceof ParagraphStyle.Property){
            ParagraphStyle.setProperty((ParagraphStyleProperties) getStyleProperties(PropertyInstance.PARAGRAPHSTYLE), (ParagraphStyle.Property)property, value);
        }
        else if(property instanceof RowStyle.Property){
            RowStyle.setProperty((RowStyleProperties) getStyleProperties(PropertyInstance.ROWSTYLE), (RowStyle.Property)property, value);
        }
        else if(property instanceof ColumnStyle.Property){
            ColumnStyle.setProperty((ColumnStyleProperties) getStyleProperties(PropertyInstance.COLUMNSTYLE), (ColumnStyle.Property)property, value);
        }
        else if(property instanceof SheetStyle.Property){
            SheetStyle.setProperty((SheetStyleProperties) getStyleProperties(PropertyInstance.SHEETSTYLE), (SheetStyle.Property)property, value);
        }
        else if(property instanceof GraphicStyle.Property){
            GraphicStyle.setProperty((GraphicStyleProperties) getStyleProperties(PropertyInstance.GRAPHICSTYLE), (GraphicStyle.Property)property, value);
        }
        else{
            throw new IllegalArgumentException("This is not yet supported..." + property); //No I18N
        }
    }
    
    public String getPropertyAsString_deep(Property property, Workbook workbook){
        Object propertyValue = this.getProperty_Deep(property, workbook);
        return Property.getPropertyString(propertyValue, workbook.getTheme());
    }

    public String getPropertyAsString(Property property, ZSTheme theme) {
        Object propertyValue = this.getProperty(property);
        return Property.getPropertyString(propertyValue, theme);
    }

    public static Style merge(Style source, Style destination){
        if(source.getClass().equals(destination.getClass())){
            if(destination instanceof CellStyle){
                merge(CellStyle.Property.values(), source, destination);
                merge(TextStyle.Property.values(), source, destination);
                merge(ParagraphStyle.Property.values(), source, destination);
            }
            else if(destination instanceof ParagraphStyle){
                merge(TextStyle.Property.values(), source, destination);
                merge(ParagraphStyle.Property.values(), source, destination);
            }
            else if(destination instanceof TextStyle){
                merge(TextStyle.Property.values(), source, destination);
            }
            else if(destination instanceof RowStyle){
                merge(RowStyle.Property.values(), source, destination);
            }
            else if(destination instanceof ColumnStyle){
                merge(ColumnStyle.Property.values(), source, destination);
            }
            else if(destination instanceof SheetStyle){
                merge(SheetStyle.Property.values(), source, destination);
            }
            else if(destination instanceof GraphicStyle){
                merge(GraphicStyle.Property.values(), source, destination);
            }
            else {
                throw new IllegalArgumentException("Unsupported style...");//No I18N
            }
            return destination;
        }
        else{
            throw new IllegalArgumentException("Merge cannot happen between two different style..."); //No I18N
        }
    }

    /**
     *
     * Method overwrites base style with non null values of overwritingstyle.
     */
    private static void merge(Property[] properties, Style source, Style destination){
        for(Property property : properties){
            Object value = source.getProperty(property);
            if(value != null){
                destination.setProperty(property, value);
            }
        }
    }
}
