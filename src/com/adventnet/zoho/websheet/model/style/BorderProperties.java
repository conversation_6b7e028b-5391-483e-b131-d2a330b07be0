// $Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSHexColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.EngineUtils;
import com.adventnet.zoho.websheet.model.zsparser.Names;
import com.adventnet.zoho.websheet.model.zsparser.XmlName;

import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class BorderProperties {
    public static final Logger LOGGER = Logger.getLogger(BorderProperties.class.getName());
    
    private static final String NONE_BORDERTYPE = "none";//No I18N
    public static final BorderProperties BORDER_NONE = new BorderProperties(NONE_BORDERTYPE, null, 0.01);
    public static final BorderProperties BORDER_TRANSPARENT = new BorderProperties("solid", null, 0.01);//No I18N
    
    
    private final String type;
    private final ZSColor color;
    private final double size; // in INCHES

    private BorderProperties(String type, ZSColor color, double size)
    {
        this.type = type;
        this.color = color;
        this.size = size;
    }
    
    public static BorderProperties getInstance(String type, ZSColor color, double size)
    {        
        if(NONE_BORDERTYPE.equals(type))
        {
            return BORDER_NONE;
        }
        BorderProperties borderProps = null;
        try
        {
            if(type == null)
            {
                throw new IllegalArgumentException("Type should not be null while creating border....."); //No I18N
            }
            borderProps = new BorderProperties(type, color, size);
        }catch(Exception e)
        {
            LOGGER.log(Level.INFO, "Couldn''t create BorderProperties for string :: Type : {0} color : {1} size : {2}", new Object[]{type, color, size});
        }
        
        return borderProps;
    }
    
    // To be used for imported files or files written in old format.
    public static BorderProperties getInstance(String borderString)
    {
        if(NONE_BORDERTYPE.equals(borderString))
        {
            return BORDER_NONE;
        }
        BorderProperties borderProps = null;
        if(borderString != null)
        {        
            try
            {
                String params[] = borderString.split(" ");//[size]-[type]-[color]
                double inches = EngineUtils.getInchValue(params[0]);
                ZSColor color;
                if(params[2].charAt(0) == '#'){
                    color = ZSColor.getColor(params[2], null, null);
                }
                else{
                    color = ZSColor.getColor(null, params[2], params.length > 3 ? params[3] : null);
                }
                return getInstance(params[1], color, inches);
            }
            catch(Exception e)
            {
                LOGGER.log(Level.INFO, "Couldn''t create BorderProperties object for string :: {0}", borderString);
            }
        }
        
        return borderProps;
    }
    
    /**
     * @return the type
     */
    public String getType() {
        return type;
    }

    /**
     * @return the color
     */
    public ZSColor getColor() {
        return color;
    }

    /**
     * @return the size
     */
    public double getSize() {
        return size;
    }
    
    public String getBorderString(ZSTheme theme)
    {
        if(this.equals(BORDER_NONE))
        {
            return "none";//No I18N
        }
        else if(this.equals(BORDER_TRANSPARENT))
        {
            LOGGER.log(Level.INFO, "There should be no need to get border string for BORDER_TRANSPARENT");
            return "none";//No I18N
        }
        return size+"in "+type+" "+ ZSColor.getHexColor(color, theme);//No I18N
    }

    public String getBorderStringZS()
    {
        if(this.equals(BORDER_NONE))
        {
            return "none";//No I18N
        }
        else if(this.equals(BORDER_TRANSPARENT))
        {
            LOGGER.log(Level.INFO, "There should be no need to get border string for BORDER_TRANSPARENT");
            return "none";//No I18N
        }
        if(this.color instanceof ZSHexColor){
            return size+"in "+type+" "+color.getHexColorToWrite();//No I18N
        }
        else{//ZSThemeColor
            return size+"in "+type+" "+color.getThemeColorToWrite()+(color.getColorTintToWrite() != null ? " "+color.getColorTintToWrite() : "");//No I18N
        }
    }

    public String[] getValues(String borderName){
        return new String[]{
                borderName,
                this.type,
                this.color == null ? null : this.color.getHexColorToWrite(),
                this.color == null ? null : this.color.getThemeColorToWrite(),
                this.color == null ? null : this.color.getColorTintToWrite(),
                this.size + "in" // No I18N
        };
    }
    public XmlName[] getAttributes(){
        return new XmlName[]{
                Names.A_STYLE_NAME,
                Names.BORDER_TYPE,
                Names.BORDER_COLOR,
                Names.BORDER_THEME,
                Names.BORDER_TINT,
                Names.BORDER_SIZE
        };
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (o == null || getClass() != o.getClass()){
            return false;
        }
        BorderProperties that = (BorderProperties) o;
        return Double.compare(that.size, size) == 0 &&
                Objects.equals(type, that.type) &&
                Objects.equals(color, that.color);
    }

    @Override
    public int hashCode() {
        return Objects.hash(type, color, size);
    }

    @Override
    public String toString() {
        return "BorderProperties{" +  //NO I18N
                "type='" + type + '\'' + //NO I18N
                ", color=" + color + //NO I18N
                ", size=" + size + //NO I18N
                '}'; //NO I18N
    }
}
