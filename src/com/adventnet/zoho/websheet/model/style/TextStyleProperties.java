// $Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSHexColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSFontScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.Utility;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

/**
 *
 * <AUTHOR>
 */
public class TextStyleProperties extends StyleProperties
{
    public TextStyleProperties()
    {        
    }

    public enum Property implements StyleProperties.Property{
        FONTVARIANT,                                            //fo:font-variant
        TEXTTRANSFORM,                                          //fo:text-transform
        COLOR,                                                  //fo:color
        USEWINDOWFONTCOLOR,                                     //style:use-window-font-color (its a boolean value)
        TEXTOUTLINE,                                            //style:text-outline (boolean value)
        TEXTLINETHROUGHTYPE,                                    //style:text-line-through-type
        TEXTLINETHROUGHSTYLE,                                   //style:text-line-through-style
        TEXTLINETHROUGHWIDTH,                                   //style:text-line-through-width
        TEXTLINETHROUGHCOLOR,                                   //style:text-line-through-color
        TEXTLINETHROUGHTEXT,                                    //style:text-line-through-text
        TEXTLINETHROUGHTEXTSTYLE,                               //style:text-line-through-text-style
        TEXTPOSITION,                                           //style:text-position
        FONTNAME,                                               //style:font-name
        FONTNAMEASIAN,                                          //style:font-name-asian
        FONTNAMECOMPLEX,                                        //style:font-name-complex
        FONTFAMILY,                                             //fo:font-family
        FONTFAMILYASIAN,                                        //style:font-name-asian
        FONTFAMILYCOMPLEX,                                      //style:font-family-complex
        FONTFAMILYGENERIC,                                      //style:font-family-generic
        FONTFAMILYGENERICASIAN,                                 //style:font-family-generic-asian
        FONTFAMILYGENERICCOMPLEX,                               //style:font-family-generic-complex
        FONTSTYLENAME,                                          //style:font-style-name
        FONTSTYLENAMEASIAN,                                     //style:font-style-name-asian
        FONTSTYLENAMECOMPLEX,                                   //style:font-style-name-complex
        FONTPITCHNAME,                                          //style:font-pitch-name
        FONTPITCHNAMEASIAN,                                     //style:font-pitch-name-asian
        FONTPITCHNAMECOMPLEX,                                   //style:font-pitch-name-complex
        FONTCHARSET,                                            //style:font-charset
        FONTCHARSETASIAN,                                       //style:font-charset-asian
        FONTCHARSETCOMPLEX,                                     //style:font-charset-complex
        FONTSIZE,                                               //fo:font-size
        FONTSIZEASIAN,                                          //fo:font-size-asian
        FONTSIZECOMPLEX,                                        //fo:font-size-complex
        FONTSIZEREL,                                            //style:font-size-rel
        FONTSIZERELASIAN,                                       //style:font-size-rel-asian
        FONTSIZERELCOMPLEX,                                     //style:font-size-rel-complex
        SCRIPTTYPE,                                             //style:script-type (latin,asian,complex,ignore)
        LETTERSPACING,                                          //fo:letter-spacing
        LANGUAGE,                                               //fo:language
        LANGUAGEASIAN,                                          //fo:language-asian
        LANGUAGECOMPLEX,                                        //fo:language-complex
        COUNTRY,                                                //fo:country
        COUNTRYASIAN,                                           //fo:country-asian
        COUNTRYCOMPLEX,                                         //fo:country-complex
        FONTSTYLE,                                              //fo:font-style
        FONTSTYLEASIAN,                                         //fo:font-style-asian
        FONTSTYLECOMPLEX,                                       //fo:font-style-complex
        FONTRELIEF,                                             //style:font-relief
        TEXTSHADOW,                                             //fo:text-shadow
        TEXTUNDERLINETYPE,                                      //style:text-underline-type
        TEXTUNDERLINESTYLE,                                     //style:text-underline-style
        TEXTUNDERLINEWIDTH,                                     //style:text-underline-width
        TEXTUNDERLINECOLOR,                                     //style:text-underline-color
        TEXTUNDERLINEMODE,                                      //style:text-underline-mode
        FONTWEIGHT,                                             //fo:font-weight
        FONTWEIGHTASIAN,                                        //style:font-weight-asian
        FONTWEIGHTCOMPLEX,                                      //style:font-weight-complex
        TEXTLINETHROUGHMODE,                                    //style:text-line-through-mode
        LETTERKERNING,                                          //style:letter-kerning (boolean value)
        TEXTBLINKING,                                           //style:text-blinking (boolean value)
        BACKGROUNDCOLOR,                                        //fo:background-color
        TEXTCOMBINE,                                            //style:text-combine
        TEXTCOMBINESTARTCHAR,                                   //style:text-combine-start-char
        TEXTCOMBINEENDCHAR,                                     //style:text-combine-end-char
        TEXTEMPHASIZE,                                          //style:text-emphasize
        TEXTSCALE,                                              //style:text-scale
        TEXTROTATIONANGLE,                                      //style:text-rotation-angle
        TEXTROTATIONSCALE,                                      //style:text-rotation-scale
        HYPHENATE,                                              //fo:hyphenate
        HYPHENATIONREMAINCHARCOUNT,                             //fo:hyphenation-remain-char-count
        HYPHENATIONPUSHCHARCOUNT,                               //fo:hyphenation-push-char-count
        DISPLAY,                                                //text:display
        CONDITION;                                              //text:condition:

        public String[] getAttributes(){
            switch (this){
                case FONTVARIANT:
                    return new String[]{"fo:font-variant"};//No I18N
                case TEXTTRANSFORM:
                    return new String[]{"fo:text-transform"};//No I18N
                case COLOR:
                    return new String[]{"fo:color", "fo:theme-color", "fo:color-tint"};//No I18N
                case USEWINDOWFONTCOLOR:
                    return new String[]{"style:use-window-font-color"};//No I18N
                case TEXTOUTLINE:
                    return new String[]{"style:text-outline"};//No I18N
                case TEXTLINETHROUGHTYPE:
                    return new String[]{"style:text-line-through-type"};//No I18N
                case TEXTLINETHROUGHSTYLE:
                    return new String[]{"style:text-line-through-style"};//No I18N
                case TEXTLINETHROUGHWIDTH:
                    return new String[]{"style:text-line-through-width"};//No I18N
                case TEXTLINETHROUGHCOLOR:
                    return new String[]{"style:text-line-through-color"};//No I18N
                case TEXTLINETHROUGHTEXT:
                    return new String[]{"style:text-line-through-text"};//No I18N
                case TEXTLINETHROUGHTEXTSTYLE:
                    return new String[]{"style:text-line-through-text-style"};//No I18N
                case TEXTPOSITION:
                    return new String[]{"style:text-position"};//No I18N
                case FONTNAME:
                    return new String[]{"style:font-name"};//No I18N
                case FONTNAMEASIAN:
                    return new String[]{"style:font-name-asian"};//No I18N
                case FONTNAMECOMPLEX:
                    return new String[]{"style:font-name-complex"};//No I18N
                case FONTFAMILY:
                    return new String[]{"fo:font-family"};//No I18N
                case FONTFAMILYASIAN:
                    return new String[]{"style:font-family-asian"};//No I18N
                case FONTFAMILYCOMPLEX:
                    return new String[]{"style:font-family-complex"};//No I18N
                case FONTFAMILYGENERIC:
                    return new String[]{"style:font-family-generic"};//No I18N
                case FONTFAMILYGENERICASIAN:
                    return new String[]{"style:font-family-generic-asian"};//No I18N
                case FONTFAMILYGENERICCOMPLEX:
                    return new String[]{"style:font-family-generic-complex"};//No I18N
                case FONTSTYLENAME:
                    return new String[]{"style:font-style-name"};//No I18N
                case FONTSTYLENAMEASIAN:
                    return new String[]{"style:font-style-name-asian"};//No I18N
                case FONTSTYLENAMECOMPLEX:
                    return new String[]{"style:font-style-name-complex"};//No I18N
                case FONTPITCHNAME:
                    return new String[]{"style:font-pitch-name"};//No I18N
                case FONTPITCHNAMEASIAN:
                    return new String[]{"style:font-pitch-name-asian"};//No I18N
                case FONTPITCHNAMECOMPLEX:
                    return new String[]{"style:font-pitch-name-complex"};//No I18N
                case FONTCHARSET:
                    return new String[]{"style:font-charset"};//No I18N
                case FONTCHARSETASIAN:
                    return new String[]{"style:font-charset-asian"};//No I18N
                case FONTCHARSETCOMPLEX:
                    return new String[]{"style:font-charset-complex"};//No I18N
                case FONTSIZE:
                    return new String[]{"fo:font-size"};//No I18N
                case FONTSIZEASIAN:
                    return new String[]{"style:font-size-asian"};//No I18N
                case FONTSIZECOMPLEX:
                    return new String[]{"style:font-size-complex"};//No I18N
                case FONTSIZEREL:
                    return new String[]{"style:font-size-rel"};//No I18N
                case FONTSIZERELASIAN:
                    return new String[]{"style:font-size-rel-asian"};//No I18N
                case FONTSIZERELCOMPLEX:
                    return new String[]{"style:font-size-rel-complex"};//No I18N
                case SCRIPTTYPE:
                    return new String[]{"style:script-type"};//No I18N
                case LETTERSPACING:
                    return new String[]{"fo:letter-spacing"};//No I18N
                case LANGUAGE:
                    return new String[]{"fo:language"};//No I18N
                case LANGUAGEASIAN:
                    return new String[]{"style:language-asian"};//No I18N
                case LANGUAGECOMPLEX:
                    return new String[]{"style:language-complex"};//No I18N
                case COUNTRY:
                    return new String[]{"fo:country"};//No I18N
                case COUNTRYASIAN:
                    return new String[]{"style:country-asian"};//No I18N
                case COUNTRYCOMPLEX:
                    return new String[]{"style:country-complex"};//No I18N
                case FONTSTYLE:
                    return new String[]{"fo:font-style"};//No I18N
                case FONTSTYLEASIAN:
                    return new String[]{"style:font-style-asian"};//No I18N
                case FONTSTYLECOMPLEX:
                    return new String[]{"style:font-style-complex"};//No I18N
                case FONTRELIEF:
                    return new String[]{"style:font-relief"};//No I18N
                case TEXTSHADOW:
                    return new String[]{"fo:text-shadow"};//No I18N
                case TEXTUNDERLINETYPE:
                    return new String[]{"style:text-underline-type"};//No I18N
                case TEXTUNDERLINESTYLE:
                    return new String[]{"style:text-underline-style"};//No I18N
                case TEXTUNDERLINEWIDTH:
                    return new String[]{"style:text-underline-width"};//No I18N
                case TEXTUNDERLINECOLOR:
                    return new String[]{"style:text-underline-color"};//No I18N
                case TEXTUNDERLINEMODE:
                    return new String[]{"style:text-underline-mode"};//No I18N
                case FONTWEIGHT:
                    return new String[]{"fo:font-weight"};//No I18N
                case FONTWEIGHTASIAN:
                    return new String[]{"style:font-weight-asian"};//No I18N
                case FONTWEIGHTCOMPLEX:
                    return new String[]{"style:font-weight-complex"};//No I18N
                case TEXTLINETHROUGHMODE:
                    return new String[]{"style:text-line-through-mode"};//No I18N
                case LETTERKERNING:
                    return new String[]{"style:letter-kerning"};//No I18N
                case TEXTBLINKING:
                    return new String[]{"tyle:text-blinking"};//No I18N
                case BACKGROUNDCOLOR:
                    return new String[]{"fo:background-color"};//No I18N
                case TEXTCOMBINE:
                    return new String[]{"style:text-combine"};//No I18N
                case TEXTCOMBINESTARTCHAR:
                    return new String[]{"style:text-combine-start-char"};//No I18N
                case TEXTCOMBINEENDCHAR:
                    return new String[]{"style:text-combine-end-char"};//No I18N
                case TEXTEMPHASIZE:
                    return new String[]{"style:text-emphasize"};//No I18N
                case TEXTSCALE:
                    return new String[]{"style:text-scale"};//No I18N
                case TEXTROTATIONANGLE:
                    return new String[]{"style:text-rotation-angle"};//No I18N
                case TEXTROTATIONSCALE:
                    return new String[]{"style:text-rotation-scale"};//No I18N
                case HYPHENATE:
                    return new String[]{"fo:hyphenate"};//No I18N
                case HYPHENATIONREMAINCHARCOUNT:
                    return new String[]{"fo:hyphenation-remain-char-count"};//No I18N
                case HYPHENATIONPUSHCHARCOUNT:
                    return new String[]{"fo:hyphenation-push-char-count"};//No I18N
                case DISPLAY:
                    return new String[]{"text:display"};//No I18N
                case CONDITION:
                    return new String[]{"text:condition"};//No I18N
                default:
                    return null;
            }
        }
    }

    protected Object getProperty(Property property){
        return this.propertiesMap.get(property);
    }

    protected void setProperty(Property key, Object value){
        try{
            switch (key) {
                case COLOR:
                    this.propertiesMap.put(key, (ZSColor)value);
                    break;
                default:
                    this.propertiesMap.put(key, (String)value);
                    break;
            }
        }
        catch (Exception e){
            throw new IllegalArgumentException(e + "the data type of value has no relationship with property..."); //No I18N
        }
    }

    public String[] getAttributes()
    {

        Property[] properties = Property.values();
        List<String> attributes =  new ArrayList<>();
        for(Property property : properties){
            String[] propertyAttributes = property.getAttributes();
            for(String attribute : propertyAttributes){
                attributes.add(attribute);
            }
        }
        return attributes.toArray(new String[0]);
    }

    public void getZSValues(String[] propValues)
    {
        Property[] properties = Property.values();
        int index = 0;
        for(Property key : properties){
            switch (key){
                case COLOR:
                    ZSColor color = ((ZSColor)this.propertiesMap.get(Property.COLOR));
                    propValues[index++] = color == null ? null : color.getHexColorToWrite();
                    propValues[index++] = color == null ? null : color.getThemeColorToWrite();
                    propValues[index++] = color == null ? null : color.getColorTintToWrite();
                    break;
                default:
                    propValues[index++] = (String)this.propertiesMap.get(key);
            }
        }
    }

    public String[] getValues(Workbook workbook, Style style)
    {
        ZSTheme theme = workbook.getTheme();
        Property[] properties = Property.values();
        List<String> values = new ArrayList<>();
        for(Property key : properties){
            switch (key){
                case COLOR:
                    ZSColor color;
                    if(style != null && style instanceof CellStyle)
                    {

                        color = Utility.getAutomaticTextColorAdjusted((CellStyle)style, workbook);
                    }
                    else
                    {
                        color = (ZSColor)this.propertiesMap.get(Property.COLOR);
                        if (color instanceof ZSHexColor && EngineConstants.TEXTCOLOR_AUTOMATIC.equals(color.getHexColorToWrite()))
                        {
                            color = null;
                        }
                    }
                    values.add(color == null ? null : ZSColor.getHexColor(color, theme));
                    values.add(null);
                    values.add(null);
                    break;
                case FONTNAME:
                case FONTNAMEASIAN:
//                case FONTNAMECOMPLEX:
                    String fontName = (String)this.propertiesMap.get(key);
                    if(key != Property.FONTNAME && fontName == null)
                    {
                        fontName = (String)this.propertiesMap.get(Property.FONTNAME);
                    }
                    if(ZSFontScheme.HEADING_FONTNAME.equals(fontName))
                    {
                        values.add(theme.getFontScheme().getFonts().get(ZSFontScheme.Fonts.HEADING));
                    }
                    else if(ZSFontScheme.BODY_FONTNAME.equals(fontName))
                    {
                        values.add(theme.getFontScheme().getFonts().get(ZSFontScheme.Fonts.BODY));
                    }
                    else
                    {
                        values.add(fontName);
                    }
                    break;
//                case FONTWEIGHT:
//                case FONTWEIGHTASIAN:
//                case FONTWEIGHTCOMPLEX:
//                    Object fontWeight = this.propertiesMap.get(key);
//                    if(fontWeight == null && key != Property.FONTWEIGHT) {
//                        fontWeight = this.propertiesMap.get(Property.FONTWEIGHT);
//                    }
//                    if(fontWeight == null) {
//                        fontWeight = "normal";//No I18N
//                    }
//                    values.add((String) fontWeight);
//                    break;
//                case FONTSTYLE:
//                case FONTSTYLEASIAN:
//                case FONTSTYLECOMPLEX:
//                    Object fontStyle = this.propertiesMap.get(key);
//                    if(fontStyle == null && key != Property.FONTSTYLE) {
//                        fontStyle = this.propertiesMap.get(Property.FONTSTYLE);
//                    }
//                    if(fontStyle == null) {
//                        fontStyle = "normal";//No I18N
//                    }
//                    values.add((String) fontStyle);
//                    break;
//                case FONTSIZEASIAN:
//                case FONTSIZECOMPLEX:
//                    Object fontSize = this.propertiesMap.get(key);
//                    if(fontSize == null && key != Property.FONTSIZE) {
//                        fontSize = this.propertiesMap.get(Property.FONTSIZE);
//                    }
//                    values.add((String) fontSize);
//                    break;
//                case TEXTUNDERLINESTYLE:
//                case TEXTLINETHROUGHTYPE:
//                case TEXTSHADOW:
//                    Object underlinestyle = this.propertiesMap.get(key);
//                    if (underlinestyle == null) {
//                        underlinestyle = "none";//No I18N
//                    }
//                    values.add((String) underlinestyle);
//                    break;
//                case TEXTUNDERLINECOLOR:
//                    Object underlinecolor = this.propertiesMap.get(key);
//                    if (underlinecolor == null) {
//                        underlinecolor = "font-color";//No I18N
//                    }
//                    values.add((String) underlinecolor);
//                    break;
//                case TEXTOUTLINE:
//                    Object textoutline = this.propertiesMap.get(key);
//                    if (textoutline == null) {
//                        textoutline = "false";//No I18N
//                    }
//                    values.add((String) textoutline);
//                    break;
//                case TEXTPOSITION:
//                    Object textposition = this.propertiesMap.get(key);
//                    if (textposition == null) {
//                        textposition = "0%";//No I18N
//                    }
//                    values.add((String) textposition);
//                    break;
                case FONTFAMILYGENERIC:
                case FONTFAMILYGENERICASIAN:
                case FONTFAMILYGENERICCOMPLEX:
                    values.add(null);
                    break;
                default:
                    values.add((String)this.propertiesMap.get(key));
            }
        }
        return values.toArray(new String[0]);
    }
     
    public String getTagName()
    {
        return "style:text-properties";//No I18N
    }
}
