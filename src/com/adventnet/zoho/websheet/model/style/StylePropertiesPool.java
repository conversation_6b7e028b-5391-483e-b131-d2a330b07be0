package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;

import java.util.concurrent.ExecutionException;

public class StylePropertiesPool {

    private final LoadingCache<StyleProperties, StyleProperties> stylePropertiesCache;

    public StylePropertiesPool(){
        this.stylePropertiesCache= CacheBuilder.newBuilder()
                .maximumSize(EngineConstants.MAX_STYLE_PROPERTIES_POOL_SIZE)
                .build(new CacheLoader<StyleProperties, StyleProperties>(){
                    @Override
                    public StyleProperties load(StyleProperties s) {
                        return s;
                    }
                });
    }

    public StyleProperties getStyleProperties(StyleProperties properties){
        if(properties == null || properties.isThroughPool())
        {
            return properties;
        }
        StyleProperties styleProperties;
        try{
            styleProperties = stylePropertiesCache.get(properties);
        }
        catch (ExecutionException e){
            styleProperties = properties;
        }
        styleProperties.setIsThroughPool(true);
        return styleProperties;
    }

    public long size(){
        return stylePropertiesCache.size();
    }

    public void clearCache()
    {
        this.stylePropertiesCache.invalidateAll();
    }

    public LoadingCache<StyleProperties, StyleProperties> getStylePropertiesCache()
    {
        return stylePropertiesCache;
    }
}

