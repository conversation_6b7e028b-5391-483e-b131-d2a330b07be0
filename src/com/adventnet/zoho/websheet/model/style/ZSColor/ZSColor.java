// $Id$
package com.adventnet.zoho.websheet.model.style.ZSColor;

import com.adventnet.zoho.websheet.model.ext.functions.Hex2Dec;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;

public interface ZSColor
{

    public String getHexColorToWrite();
    public String getThemeColorToWrite();
    public String getColorTintToWrite();

    public static ZSColor getInstance(ZSColorScheme.Colors color, double tint)
    {
        return ZSThemeColor.getInstance(color, tint);
    }

    public static ZSColor getInstance(String hexColor){
        return ZSHexColor.getInstance(hexColor);
    }

    public static ZSColor getInstance(JSONObjectWrapper colorJSON){
        return ZSColor.getColor(colorJSON.has(JSONConstants.HEXCOLOR) ? colorJSON.getString(JSONConstants.HEXCOLOR) : null, colorJSON.has(JSONConstants.THEMECOLOR) ? colorJSON.getString(JSONConstants.THEMECOLOR) : null, colorJSON.has(JSONConstants.TINT) ? colorJSON.getString(JSONConstants.TINT) : null);
    }

    public static ZSColor getColor(String hexColor, String themeColor, String tint)
    {
        ZSColor color = null;
        if(themeColor != null){
            double tintValue = tint != null ? Double.parseDouble(tint): 0.0;
            color = getInstance(ZSColorScheme.Colors.valueOf(themeColor), tintValue);
        }
        else if(hexColor != null){
            color = getInstance(hexColor);
        }

        return color;
    }

    public static ZSColor getColorFromJson(JSONObjectWrapper colorJson) {
        if(colorJson == null) {
            return null;
        }

        String hex = colorJson.optString(JSONConstants.HEXCOLOR,null);
        String theme = colorJson.optString(JSONConstants.THEMECOLOR,null);
        String tint = colorJson.optString(JSONConstants.TINT,null);

        return getColor(hex, theme, tint);
    }

    // TO BE USED ONLY FROM XLSX PARSER.
    public static ZSColor getColor(String hexColor, int themeColor, double tint)
    {
        ZSColor color = null;
        if(themeColor != -1){
            ZSColorScheme.Colors colorName;
            switch (themeColor) {
                case 0:
                    colorName = ZSColorScheme.Colors.BACKGROUND1;
                    break;
                case 1:
                    colorName = ZSColorScheme.Colors.TEXT1;
                    break;
                case 2:
                    colorName = ZSColorScheme.Colors.BACKGROUND2;
                    break;
                case 3:
                    colorName = ZSColorScheme.Colors.TEXT2;
                    break;
                case 4:
                    colorName = ZSColorScheme.Colors.ACCENT1;
                    break;
                case 5:
                    colorName = ZSColorScheme.Colors.ACCENT2;
                    break;
                case 6:
                    colorName = ZSColorScheme.Colors.ACCENT3;
                    break;
                case 7:
                    colorName = ZSColorScheme.Colors.ACCENT4;
                    break;
                case 8:
                    colorName = ZSColorScheme.Colors.ACCENT5;
                    break;
                case 9:
                    colorName = ZSColorScheme.Colors.ACCENT6;
                    break;
                case 10:
                    colorName = ZSColorScheme.Colors.HYPERLINK;
                    break;
                case 11:
                    colorName = ZSColorScheme.Colors.FOLHYPERLINK;
                    break;
                default: throw new IllegalArgumentException("Wrong Theme Color index : Theme color index should be between (0 - 11) : "+themeColor);//No I18N
            }
            color = getInstance(colorName, tint);
        }
        else if(hexColor != null){
            color = getInstance(hexColor);
        }

        return color;
    }

    static double hueToRgb(double t1, double t2, double hue) {
        if (hue < 0)
        {
            hue += 6;
        }
        if (hue >= 6)
        {
            hue -= 6;
        }
        if (hue < 1)
        {
            return (t2 - t1) * hue + t1;
        }
        else if(hue < 3)
        {
            return t2;
        }
        else if(hue < 4)
        {
            return (t2 - t1) * (4 - hue) + t1;
        }
        else
        {
            return t1;
        }
    }

    static String getTintAppliedHex(String base, double tint)
    {
        double min, max, l, s, maxcolor, h = 0;
        double[] rgb = new double[3];
        for(int i = 0, channel = 0; i < 5; i += 2, channel++)
        {
            int hls = Integer.parseInt(base.substring(i, i + 2), 16);
            rgb[channel] = (double) hls / 255;
        }
        min = rgb[0];
        max = rgb[0];
        maxcolor = 0;
        for(int i = 0; i < rgb.length - 1; i++)
        {
            if(rgb[i + 1] <= min)
            {
                min = rgb[i + 1];
            }
            if(rgb[i + 1] >= max)
            {
                max = rgb[i + 1];
                maxcolor = i + 1;
            }
        }
        if(max != min)
        {
            if(maxcolor == 0)
            {
                h = (rgb[1] - rgb[2]) / (max - min);
            }
            if(maxcolor == 1)
            {
                h = 2 + (rgb[2] - rgb[0]) / (max - min);
            }
            if(maxcolor == 2)
            {
                h = 4 + (rgb[0] - rgb[1]) / (max - min);
            }
        }

        h = h * 60;
        if(h < 0)
        {
            h = h + 360;
        }
        l = (min + max) / 2;
        if(min == max)
        {
            s = 0;
        }
        else
        {
            if(l < 0.5)
            {
                s = (max - min) / (max + min);
            }
            else
            {
                s = (max - min) / (2 - max - min);
            }
        }

        ////// Applying tint on the luminescence value
        if(tint < 0)
        {
            l = l * (1 + tint);
        }
        else
        {
            l = (l * (1 - tint)) + (tint);
        }
        //////

        double t1, t2;
        h = h / 60;
        if(l <= 0.5)
        {
            t2 = l * (s + 1);
        }
        else
        {
            t2 = l + s - (l * s);
        }
        t1 = l * 2 - t2;

        rgb[0] = hueToRgb(t1, t2, h + 2) * 255;
        rgb[1] = hueToRgb(t1, t2, h) * 255;
        rgb[2] = hueToRgb(t1, t2, h - 2) * 255;

        StringBuilder hexResult = new StringBuilder();
        for(int i = 0; i < rgb.length; i++)
        {
            String hexString = Integer.toHexString((int) Math.round(rgb[i]));
            if(hexString.length() == 1)
            {
                //hexString should be of length 2 here, but it's 1, prefixing it with '0'
                hexString = "0" + hexString;//No I18N
            }

            hexResult.append(hexString);
        }
        return hexResult.toString();
    }

    static String deriveHexColorForThemeColor(ZSThemeColor color, ZSTheme theme)
    {
        String hex = theme.getColorScheme().getColors().get(color.getColor()).substring(1);
        String hexResult = getTintAppliedHex(hex, color.getTint());
        hexResult = "#" + hexResult;
        theme.getDerivedHexColors().put(color, hexResult);
        return hexResult;
    }

    static int applyTint(int hlsValue, Double tintValue) {
        if (tintValue < 0) {
            hlsValue =  (int) Math.round((double)hlsValue * (1.0d + tintValue));
        } else {
            hlsValue =  (int) Math.round((double)hlsValue * (1.0d - tintValue) + 255.0d - (255.0d * (1.0d - tintValue)));
        }
        return hlsValue;
    }


    public static String getHexColor(ZSColor color, ZSTheme theme)
    {
        if(color == null)
        {
            return null;
        }
        String value;
        if(color instanceof ZSThemeColor)
        {
            if((value = theme.getDerivedHexColors().get((ZSThemeColor) color)) == null)
            {
                value = deriveHexColorForThemeColor((ZSThemeColor) color, theme);
                theme.getDerivedHexColors().put((ZSThemeColor) color, value);
            }
        }
        else
        {
            value = color.getHexColorToWrite();
        }
        return value;
    }

    /**
     * Move this method once org json is used everywhere
     * @param zsColor
     * @param zsTheme
     * @return
     */
    public static org.json.JSONObject getColorJSON(ZSColor zsColor, ZSTheme zsTheme)
    {
        org.json.JSONObject colorJson = new org.json.JSONObject();

        String hexColor = zsColor.getHexColorToWrite();
        String tint = zsColor.getColorTintToWrite();
        String accent = zsColor.getThemeColorToWrite();
        if(hexColor != null)
        {
            colorJson.put(JSONConstants.HEX_COLOR, hexColor);
        }
        else
        {
            colorJson.put(JSONConstants.HEX_COLOR, getHexColor(zsColor, zsTheme));
            colorJson.put(JSONConstants.TINT_COLOR, tint);
            colorJson.put(JSONConstants.THEME_COLOR, accent);
        }
        return colorJson;
    }

    public static JSONObjectWrapper getColorJSON_Old(ZSColor color, ZSTheme zsTheme)
    {
        if(color == null)
        {
            return null;
        }

        JSONObjectWrapper colorJSON = new JSONObjectWrapper();

        String hexColor = color.getHexColorToWrite();
        String tint = color.getColorTintToWrite();
        String accent = color.getThemeColorToWrite();

        if(hexColor != null)
        {
            colorJSON.put(JSONConstants.HEXCOLOR, hexColor);
        }
        else
        {
            colorJSON.put(JSONConstants.HEXCOLOR, getHexColor(color, zsTheme));
            colorJSON.put(JSONConstants.TINT, tint);
            colorJSON.put(JSONConstants.THEMECOLOR, accent);
        }
        return colorJSON;
    }

    /**
     * Returns the alternate text color based on input background color.
     * White color will be returned in case of dark, Black color otherwise
     * @param bgColor
     * @return
     */
    static ZSColor getAdjustedTextColor(String bgColor)
    {
        try
        {
            String hexColor = bgColor.substring(1);
            String []hexSplit = hexColor.split("(?<=\\G.{2})"); // No I18N
            long r = Hex2Dec.hex2Dec(hexSplit[0]);
            long g = Hex2Dec.hex2Dec(hexSplit[1]);
            long b = Hex2Dec.hex2Dec(hexSplit[2]);

            /*
                 * Max color value = 255
                 * Mid = 0.5 * 255 = 127.5
                 * Prefer lighter text than dark, so adding threshold of 25.5
                 * (0.5 * 255) + 25.5 = 153
             */
            boolean isDark = (r * 0.2126 + g * 0.7152 + b * 0.0722) < 153;
            String adjustedColor = isDark ? "#FFFFFF" : "#000000"; // No I18N
            return getInstance(adjustedColor);
        }catch(Exception e)
        {
            return getInstance("#000000"); // No I18N
        }
    }
}
