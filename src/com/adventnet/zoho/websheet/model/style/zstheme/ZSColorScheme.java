// $Id$
package com.adventnet.zoho.websheet.model.style.zstheme;

import com.adventnet.zoho.websheet.model.style.ZSColor.ZSThemeColor;
import com.adventnet.zoho.websheet.model.zsparser.Names;
import com.adventnet.zoho.websheet.model.zsparser.XmlName;
import com.singularsys.jep.functions.Round;

import java.util.*;

public class ZSColorScheme {
    public enum Colors{
        BACKGROUND1, TEXT1, BACKGROUND2, TEXT2, ACCENT1, ACCENT2, ACCENT3, ACCENT4, ACCENT5, ACCENT6, HYPERLINK, FOLHYPERLINK;
    }

    private Map<Colors, String> colors = new EnumMap<Colors, String>(Colors.class);

    public ZSColorScheme(String background, String text, String accent1, String accent2, String accent3, String accent4, String accent5, String accent6, String hyperlink, String folHyperlink){
        this("#FFFFFF", "#000000", background,  text,  accent1,  accent2,  accent3,  accent4,  accent5,  accent6,  hyperlink,  folHyperlink); //No I18N
    }

    public ZSColorScheme(String background1, String text1, String background2, String text2, String accent1, String accent2, String accent3, String accent4, String accent5, String accent6, String hyperlink, String folHyperlink){
        this.colors.put(Colors.BACKGROUND1, background1.toLowerCase());
        this.colors.put(Colors.TEXT1, text1.toLowerCase());
        this.colors.put(Colors.BACKGROUND2, background2.toLowerCase());
        this.colors.put(Colors.TEXT2, text2.toLowerCase());
        this.colors.put(Colors.ACCENT1, accent1.toLowerCase());
        this.colors.put(Colors.ACCENT2, accent2.toLowerCase());
        this.colors.put(Colors.ACCENT3, accent3.toLowerCase());
        this.colors.put(Colors.ACCENT4, accent4.toLowerCase());
        this.colors.put(Colors.ACCENT5, accent5.toLowerCase());
        this.colors.put(Colors.ACCENT6, accent6.toLowerCase());
        this.colors.put(Colors.HYPERLINK, hyperlink.toLowerCase());
        this.colors.put(Colors.FOLHYPERLINK, folHyperlink.toLowerCase());
    }

    public Map<Colors, String> getColors(){
        return Collections.unmodifiableMap(this.colors);
    }

    private String getAllColorHex(){
        StringJoiner colorJoiner = new StringJoiner(",");
        for(String color : this.getColors().values()){
            colorJoiner.add(color);
        }
        return colorJoiner.toString();
    }

    public XmlName[] getAttributes(){
        XmlName attributes[] = new XmlName[]{ Names.A_COLORS };
        return attributes;
    }

    public String[] getValues(){
        String values[] = new String[]{ getAllColorHex()};
        return values;
    }

    @Override
    public String toString() {
        return "ColorSchemeName : "+" { colors : "+ this.colors.toString() + " }"; //No I18N
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
        {
            return true;
        }
        if (!(o instanceof ZSColorScheme))
        {
            return false;
        }
        ZSColorScheme that = (ZSColorScheme) o;
        return Objects.equals(getColors(), that.getColors());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getColors());
    }
}
