// $Id$
/*
 * ColumnStyle.java
 *
 * Created on April 19, 2007, 12:24 PM
 */

package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.Workbook;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class ColumnStyle extends Style implements Cloneable
{
    private static final Logger LOGGER = Logger.getLogger(ColumnStyle.class.getName());

    private ColumnStyleProperties columnStyleProperties;

    public enum Property implements Style.Property {
        COLUMNWIDTH,
        RELCOLUMNWIDTH,
        BREAKBEFORE,
        BREAKAFTER,
        USEOPTIMALCOLUMNWIDTH;
    }

    public static void setProperty(ColumnStyleProperties columnStyleProperties, ColumnStyle.Property property, Object value){
        try{
            ColumnStyleProperties.Property key = Enum.valueOf(ColumnStyleProperties.Property.class, property.name());
            columnStyleProperties.setProperty(key, value);
        }
        catch (IllegalArgumentException ex){
            throw new IllegalArgumentException(ex + "Either property name or value mismatched..."); //No I18N
        }
    }

    public static Object getProperty(ColumnStyleProperties columnStyleProperties, ColumnStyle.Property property){
        ColumnStyleProperties.Property key = Enum.valueOf(ColumnStyleProperties.Property.class, property.name());
        return columnStyleProperties.getProperty(key);
    }

    StyleProperties getStyleProperties(PropertyInstance propertyInstance){
        if(propertyInstance == PropertyInstance.COLUMNSTYLE){
            if(this.columnStyleProperties == null)
            {
                this.columnStyleProperties = new ColumnStyleProperties();
            }
            return this.columnStyleProperties;
        }
        else{
            throw new IllegalArgumentException("Unsupported property..." + propertyInstance); // No I18N
        }
    }

    public ColumnStyle(){

    }

    public ColumnStyleProperties getColumnStyleProperties()
    {
        return (ColumnStyleProperties) getStyleProperties(PropertyInstance.COLUMNSTYLE);
    }

    public ColumnStyle clone()
    {
        ColumnStyle columnStyle = null;
        try
        {
            columnStyle = (ColumnStyle)super.clone();
            columnStyle.columnStyleProperties = (ColumnStyleProperties)this.getColumnStyleProperties().clone();
        }catch(CloneNotSupportedException e)
        {
			LOGGER.log(Level.WARNING,null,e);
        }

        return columnStyle;
    }

    public String toString()
    {
        StringBuilder sbuff = new StringBuilder();

        sbuff.append(super.toString());

        sbuff.append(this.getColumnStyleProperties().clone());
        return sbuff.toString();
    }

    public boolean equals(Object o)
    {
        if(this == o)
        {
            return true;
        }

        if(o != null && o instanceof ColumnStyle && super.equals(o))
        {
            ColumnStyle columnStyle = (ColumnStyle)o;
            return this.getColumnStyleProperties().equals(columnStyle.getColumnStyleProperties());
        }

        return false;
    }

    @Override
    public void getStylePropsFromPool(Workbook workbook) {
        if(this.columnStyleProperties != null && !this.columnStyleProperties.isThroughPool()) {
            this.columnStyleProperties = (ColumnStyleProperties) workbook.stylePropertiesPool.getStyleProperties(this.getColumnStyleProperties());
        }
    }

    public String getStyleFamily()
    {
        return "table-column";//No I18N
    }
}
