//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.style.datastyle.PatternComponent;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSSimpleDateFormat;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.singularsys.jep.EvaluationException;
import org.apache.commons.lang.time.DurationFormatUtils;

import java.text.*;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ZSDurationFormat extends Format implements PatternComponent {

    private final String patternChars = "HmsS"; // No I18N
    private final String format;
    private final String formattedPattern;

    private boolean  isDuration = false;

    public ZSDurationFormat(String format)
    {
        if(format == null || format.isEmpty())
        {
            throw new IllegalArgumentException("Cannot format the given String as Duration >>> "+format); // No I18N
        }

        boolean isParsingBroken = false;

        int qoutesItr = 0;
        boolean isInsideSQB = false;
        boolean isDurationFormat = false;
        StringBuilder formatStringBuilder = new StringBuilder();
        format = format.replaceAll("(?<=\\.)[0]{3}","SSS");
        parsing:
        for(int i = 0; i < format.length(); i++)
        {
            char ch = format.charAt(i);
            Character newCh = ch;
            boolean isInsideQuotes = (qoutesItr % 2) != 0;

            if(ch == '\'')
            {
                qoutesItr++;
            }
            else if(!isInsideQuotes)
            {
                switch(ch)
                {
                    case '[':
                        if(isDurationFormat || isInsideSQB)
                        {
                            isParsingBroken = true;
                            break parsing;
                        }
                        isInsideSQB = true;
                        newCh = null; // Need replacement of '[' in resultant formatted string
                        break;

                    case ']':
                        if(!isInsideSQB)
                        {
                            isParsingBroken = true;
                            break parsing;
                        }
                        isInsideSQB = false;
                        isDurationFormat = true;
                        newCh = null; // Need replacement of ']' in resultant formatted string
                        break;

                    default:
                        if(String.valueOf(ch).matches("(?i)[A-Z]")) // No I18N
                        {
                            boolean isSQBParsed = isDurationFormat || isInsideSQB;
                            if(patternChars.indexOf(ch) == -1)
                            {
                                isParsingBroken = true;
                                break parsing;
                            }

                        }
                }
            }

            if(newCh != null)
            {
                formatStringBuilder.append(newCh);
            }
        }

        if(isParsingBroken)
        {
            throw new IllegalArgumentException("Cannot format the given String as Duration >>> "+format); // No I18N
        }

        this.format = formatStringBuilder.toString();
        this.formattedPattern = format;
        this.isDuration = isDurationFormat;
    }

    @Override
    public StringBuffer format(Object obj, StringBuffer toAppendTo, FieldPosition pos) {
        try {
            String tempFormat = format;
            if(!isDuration)
            {
                String upperProp;
                char c  = format.charAt(0);
                switch (c)
                {
                    case 'm':
                        upperProp = "HH";// No I18N
                        break;
                    case 's':
                        upperProp = "mm";// No I18N
                        break;
                    case 'S':
                        upperProp = "ss";// No I18N
                        break;
                    default:
                        upperProp = "dd";// No I18N
                        break;
                }
                tempFormat = upperProp+":"+format;
            }

            double val = FunctionUtil.objectToNumber(obj).doubleValue();
            long milliSecs = Math.round(val * DateUtil.MILLI_SECS_PER_DAY);
            String result = DurationFormatUtils.formatDuration(milliSecs, tempFormat);
            if(!isDuration)
            {
                result = result.substring(result.indexOf(":")+1);
            }

            return new StringBuffer(result);
        }catch(EvaluationException e)
        {
            throw new NumberFormatException("input is not a number ::: "+obj);// No I18N
        }
    }

    @Override
    public Object parseObject(String source, ParsePosition pos) {
        return null;
    }

    @Override
    public void createNumberElements(List<NumberElement> numberElementList) {
        String durationString = "";
        for(int i=0; i<this.format.length(); i++) {
            char character = this.format.charAt(i);
            if(character == '[' || character == ']') {
                continue;
            }
            durationString += character;
        }
        if(!durationString.isEmpty()) {
            NumberElement dateElement = ZSSimpleDateFormat.getNumberElement(durationString);
            numberElementList.add(dateElement);
        }
    }

    @Override
    public String toPattern(DecimalFormatSymbols dfs, boolean prependLocale)
    {
        return formattedPattern;
    }

    public ZSDurationFormat clone() {
        return (ZSDurationFormat) super.clone();
    }

    public boolean equals(Object obj) {
        if(obj == null) {
            return false;
        }

        if(!(obj instanceof ZSDurationFormat)) {
            return false;
        }

        ZSDurationFormat format = (ZSDurationFormat) obj;

        String pattern1 = this.toPattern(null,true);
        String pattern2 = format.toPattern(null,true);

        if(pattern1.equals(pattern2)) {
            return true;
        }
        else {
            return false;
        }
    }

    public int hashCode() {
        return super.hashCode();
    }
}
