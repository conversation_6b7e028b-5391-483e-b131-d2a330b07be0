// $Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.Workbook;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ParagraphStyleProperties extends StyleProperties{
    
    public ParagraphStyleProperties()
    {
        
    }

    public enum Property implements StyleProperties.Property{
        LINEHEIGHT,
        LINEHEIGHTATLEAST,
        LINESPACING,
        FONTI<PERSON>EPENDENTLINESPACING,
        TEX<PERSON>LIGN,
        TEX<PERSON>LIGNLAST,
        JUS<PERSON>FYSINGLEWORD,
        <PERSON><PERSON><PERSON><PERSON><PERSON>TH<PERSON>,
        WIN<PERSON>OW<PERSON>,
        <PERSON><PERSON><PERSON>NS,
        <PERSON><PERSON><PERSON><PERSON><PERSON>IONKEEP,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>LADDERCOUNT,
        REGISTERTRUE,
        MARGINLEFT,
        MARGINRIGHT,
        MARGINTOP,
        MARGINBOTTOM,
        MARGIN,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>EN<PERSON>,
        <PERSON>TOTEXTINDENT,
        BR<PERSON>KBEFOR<PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        BACKGROUNDC<PERSON>OR,
        BORDER,
        <PERSON><PERSON><PERSON>RT<PERSON>,
        <PERSON><PERSON><PERSON>RBOTTOM,
        B<PERSON>DERLEFT,
        BORDERRIGHT,
        BORDERLINEWIDTH,
        BORDERLINEWIDTHTOP,
        BORDERLINEWIDTHBOTTOM,
        BORDERLINEWIDTHLEFT,
        BORDERLINEWIDTHRIGHT,
        PADDING,
        PADDINGLEFT,
        PADDINGRIGHT,
        PADDINGTOP,
        PADDINGBOTTOM,
        SHADOW,
        KEEPWITHNEXT,
        NUMBERLINES,
        LINENUMBER,
        TEXTAUTOSPACE,
        PUNCTUATIONWRAP,
        LINEBREAK,
        VERTICALALIGN,
        WRITINGMODE,
        WRITINGMODEAUTOMATIC,
        SNAPTOLAYOUTGRID,
        PAGENUMBER,
        BACKGROUNDTRANSPARENCY;

        public String[] getAttributes(){
            switch (this){
                case    LINEHEIGHT:
                    return new String[]{"fo:line-height"}; //NO I18N
                case    LINEHEIGHTATLEAST:
                    return new String[]{"style:line-height-at-least"}; //NO I18N
                case    LINESPACING:
                    return new String[]{"style:line-spacing"}; //NO I18N
                case    FONTINDEPENDENTLINESPACING:
                    return new String[]{"style:font-independent-line-spacing"}; //NO I18N
                case    TEXTALIGN:
                    return new String[]{"fo:text-align"}; //NO I18N
                case    TEXTALIGNLAST:
                    return new String[]{"fo:text-align-last"}; //NO I18N
                case    JUSTIFYSINGLEWORD:
                    return new String[]{"style:justify-single-word"}; //NO I18N
                case    KEEPTOGETHER:
                    return new String[]{"fo:keep-together"}; //NO I18N
                case    WINDOWS:
                    return new String[]{"fo:widows"}; //NO I18N
                case    ORPHANS:
                    return new String[]{"fo:orphans"}; //NO I18N
                case    HYPHENATIONKEEP:
                    return new String[]{"fo:hyphenation-keep"}; //NO I18N
                case    HYPHENATIONLADDERCOUNT:
                    return new String[]{"fo:hyphenation-ladder-count"}; //NO I18N
                case    REGISTERTRUE:
                    return new String[]{"style:register-true"}; //NO I18N
                case    MARGINLEFT:
                    return new String[]{"fo:margin-left"}; //NO I18N
                case    MARGINRIGHT:
                    return new String[]{"fo:margin-right"}; //NO I18N
                case    MARGINTOP:
                    return new String[]{"fo:margin-top"}; //NO I18N
                case    MARGINBOTTOM:
                    return new String[]{"fo:margin-bottom"}; //NO I18N
                case    MARGIN:
                    return new String[]{"fo:margin"}; //NO I18N
                case    ZSINDENT:
                    return new String[]{"fo:zsindent"}; //NO I18N
                case    TEXTINDENT:
                    return new String[]{"fo:text-indent"}; //NO I18N
                case    AUTOTEXTINDENT:
                    return new String[]{"style:auto-text-indent"}; //NO I18N
                case    BREAKBEFORE:
                    return new String[]{"fo:break-before"}; //NO I18N
                case    BREAKAFTER:
                    return new String[]{"fo:break-after"}; //NO I18N
                case    BACKGROUNDCOLOR:
                    return new String[]{"fo:background-color"}; //NO I18N
                case    BORDER:
                    return new String[]{"fo:border"}; //NO I18N
                case    BORDERTOP:
                    return new String[]{"fo:border-top"}; //NO I18N
                case    BORDERBOTTOM:
                    return new String[]{"fo:border-bottom"}; //NO I18N
                case    BORDERLEFT:
                    return new String[]{"fo:border-left"}; //NO I18N
                case    BORDERRIGHT:
                    return new String[]{"fo:border-right"}; //NO I18N
                case    BORDERLINEWIDTH:
                    return new String[]{"style:border-line-width"}; //NO I18N
                case    BORDERLINEWIDTHTOP:
                    return new String[]{"style:border-line-width-top"}; //NO I18N
                case    BORDERLINEWIDTHBOTTOM:
                    return new String[]{"style:border-line-width-bottom"}; //NO I18N
                case    BORDERLINEWIDTHLEFT:
                    return new String[]{"style:border-line-width-left"}; //NO I18N
                case    BORDERLINEWIDTHRIGHT:
                    return new String[]{"style:border-line-width-right"}; //NO I18N
                case    PADDING:
                    return new String[]{"fo:padding"}; //NO I18N
                case    PADDINGLEFT:
                    return new String[]{"fo:padding-left"}; //NO I18N
                case    PADDINGRIGHT:
                    return new String[]{"fo:padding-right"}; //NO I18N
                case    PADDINGTOP:
                    return new String[]{"fo:padding-top"}; //NO I18N
                case    PADDINGBOTTOM:
                    return new String[]{"fo:padding-bottom"}; //NO I18N
                case    SHADOW:
                    return new String[]{"style:shadow"}; //NO I18N
                case    KEEPWITHNEXT:
                    return new String[]{"fo:keep-with-next"}; //NO I18N
                case    NUMBERLINES:
                    return new String[]{"text:number-lines"}; //NO I18N
                case    LINENUMBER:
                    return new String[]{"text:line-number"}; //NO I18N
                case    TEXTAUTOSPACE:
                    return new String[]{"style:text-autospace"}; //NO I18N
                case    PUNCTUATIONWRAP:
                    return new String[]{"style:punctuation-wrap"}; //NO I18N
                case    LINEBREAK:
                    return new String[]{"style:line-break"}; //NO I18N
                case    VERTICALALIGN:
                    return new String[]{"style:vertical-align"}; //NO I18N
                case    WRITINGMODE:
                    return new String[]{"style:writing-mode"}; //NO I18N
                case    WRITINGMODEAUTOMATIC:
                    return new String[]{"style:writing-mode-automatic"}; //NO I18N
                case    SNAPTOLAYOUTGRID:
                    return new String[]{"style:snap-to-layout-grid"}; //NO I18N
                case    PAGENUMBER:
                    return new String[]{"style:page-number"}; //NO I18N
                case    BACKGROUNDTRANSPARENCY:
                    return new String[]{"style:background-transparency"}; //NO I18N
                default:
                    return null;
            }
        }
    }

    protected Object getProperty(Property property){
        return this.propertiesMap.get(property);
    }

    protected void setProperty(Property key, Object value){
        try{
            this.propertiesMap.put(key, (String)value);
        }
        catch (Exception e){
            throw new IllegalArgumentException(e + "the data type of value has no relationship with property..."); //No I18N
        }
    }

    public String[] getAttributes()
    {

        Property[] properties = Property.values();
        List<String> attributes =  new ArrayList<>();
        for(Property property : properties){
            String[] propertyAttributes = property.getAttributes();
            for(String attribute : propertyAttributes){
                attributes.add(attribute);
            }
        }
        return attributes.toArray(new String[0]);
    }

    public String[] getValues(Workbook workbook, Style style)
    {
        Property[] properties = Property.values();
        List<String> values = new ArrayList<>();
        for(Property key : properties){
            switch (key)
            {
                case MARGINLEFT:
                    String indent = null;
                    if(propertiesMap.get(Property.TEXTALIGN) == null || "start".equals(propertiesMap.get(Property.TEXTALIGN)) || "end".equals(propertiesMap.get(Property.TEXTALIGN))) //No I18N
                    {
                        indent = (String)this.propertiesMap.get(Property.ZSINDENT);
                        if(indent != null)
                        {
                            int indentValue = Integer.parseInt(indent);
                            indent = 0.353 * indentValue + "cm"; //No I18N
                        }
                    }
                    values.add(indent);
                    break;
                case ZSINDENT:
                    values.add(null);
                    break;
                default:
                    values.add((String)this.propertiesMap.get(key));
            }
        }
        return values.toArray(new String[0]);
    }

    public void getZSValues(String[] propValues)
    {
        Property[] properties = Property.values();
        int index = 0;
        for(Property key : properties){
            propValues[index++] = (String)this.propertiesMap.get(key);
        }
    }
    
    public String getTagName()
    {
        return "style:paragraph-properties";//No I18N
    }
}
