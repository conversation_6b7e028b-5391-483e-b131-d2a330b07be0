// $Id$

/*
 * MapStyle.java
 *
 * Created on October 17, 2007, 11:43 AM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */
 
package com.adventnet.zoho.websheet.model.style;
 
import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.CellImpl;
import com.adventnet.zoho.websheet.model.Column;
import com.adventnet.zoho.websheet.model.Condition;
import com.adventnet.zoho.websheet.model.ConditionalFormatEntry;
import com.adventnet.zoho.websheet.model.ConditionalStyle;
import com.adventnet.zoho.websheet.model.ConditionalStyleObject;
import com.adventnet.zoho.websheet.model.ReadOnlyCell;
import com.adventnet.zoho.websheet.model.Row;
import com.adventnet.zoho.websheet.model.Sheet; 
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.style.ConditionalStyleResponse.CellObject;
import com.adventnet.zoho.websheet.model.style.ConditionalStyleResponse.ConditionalStyleCellStyles;
import com.adventnet.zoho.websheet.model.util.ConditionalFormatOperator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.ConditionalFormatOperator.ConditionType;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.writer.XMLWriter;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.parser.Node;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 *
 * <AUTHOR>
 */
/*
 *
 *To support style:map tag
 *can be a child of cell style, number style, percentage style, currency style
 *date style, time style, boolen style
 */
public class MapStyle extends Condition implements ConditionalStyle
{
    private Object applyStyle;// "style:apply-style-name"
    
    /** Creates a new instance of MapStyle
     * @param inWorkbook
     * @param inCondition
     * @param inApplyStyleName
     * @param inBaseCellAddress*/

    // From parser MapStyleAdapter's constructor should be used.
    // Use this constructor only from parser and while undo.
    // For ConditionalStyle
    protected MapStyle(Workbook inWorkbook, String inCondition, String inBaseCellAddress, String inApplyStyleName)
    {
        this(inWorkbook, inCondition, inBaseCellAddress, inApplyStyleName, false, -1);
    }


    protected MapStyle(Workbook inWorkbook, String inCondition, String inBaseCellAddress, String inApplyStyleName, boolean isZSParser, int dependingColIndex)
    {
        super(inWorkbook, inCondition, inBaseCellAddress, isZSParser, dependingColIndex);

        // For import case, xlsx refers 'style:display-name' as conditional formatting 'apply-style-name' instead of 'style:name'.
        // Thus checking whether inApplyStyleName present in cellstyle's display-name.
        Object tempApplyStyle = inWorkbook.getCellStyle(inApplyStyleName);
        if(tempApplyStyle == null)
        {
            tempApplyStyle = inWorkbook.getCellStyleByDisplayName(inApplyStyleName);
        }
        this.applyStyle = tempApplyStyle;
        if(this.applyStyle == null)
        {
            // Style not available so creating a CellStyle to prevent the lose.
            LOGGER.log(Level.INFO, "Unable to create MapStyle for {0} : {1}", new Object[]{inCondition, inApplyStyleName});
            this.applyStyle = inWorkbook.checkAndAddCellStyle(new CellStyle());

        }
    }

    // From parser MapStyleAdapter's constructor should be used.
    // Use this constructor only from parser and while undo.
    // For NumberStyle
    protected MapStyle(Workbook inWorkbook, String inCondition, NumberStyle applyNumberStyle) {
        super(inWorkbook, inCondition, null);
        this.applyStyle = applyNumberStyle;
    }

    public MapStyle(ConditionType condType, int condnNum, String value, Object inApplyStyle, Sheet currSheet, int currRow, int currCol) {
        this(condType, condnNum, value, inApplyStyle, currSheet, currRow, currCol, -1);
    }
    
    public MapStyle(ConditionType condType, int condnNum, String value, Object inApplyStyle, Sheet currSheet, int currRow, int currCol, int dependingColIndex)
    {
        super(currSheet, currRow, currCol, condType, condnNum, value, dependingColIndex);
        if(inApplyStyle instanceof CellStyle)
        {
            this.applyStyle = currSheet.getWorkbook().checkAndAddCellStyle((CellStyle)inApplyStyle);
        }
        else
        {
            throw new IllegalArgumentException("Apply Cell should be CellStyle and not ... " + inApplyStyle);//No I18N
        }
    }
    
    private String[] getCFAttributes()
    {
        String[] attrs = new String[]{"calcext:apply-style-name", //No I18N
                             "calcext:value", //No I18N
                             "calcext:base-cell-address", //No I18N
                             "calcext:apply-col-index"}; //No I18N
        return attrs;
    }
    
    private String[] getCFValues(Workbook workbook, int baseRowIndex, int baseColIndex)
    {
        String applyStyleName = Utility.masknull(((CellStyle)getApplyStyle()).getDisplayName(), ((CellStyle)getApplyStyle()).getStyleName());
        String condition = getConditionToWrite(workbook, baseRowIndex, baseColIndex);
        if(condition.startsWith("is-true-formula"))
        {
            condition = condition.replace("is-true-formula", "formula-is"); // No I18N
        }
        String[] values = new String[]{applyStyleName,  
                                condition,
                                CellUtil.getCellReference(workbook.getSheet(0), baseRowIndex, baseColIndex, true, true, true),
                                this.getDependingColIndex() == -1 ? null : String.valueOf(this.getDependingColIndex())};
        return values;
    }

    // For NumberStyle MapStyle.
    public String[] getNumberStyleAttributes()
    {
        String[] attrs = new String[]{"style:apply-style-name", //No I18N
                                      "style:condition",        //No I18N                      
                                      "style:base-cell-address"}; //No I18N
        return attrs;
    }
    
    public String[] getNumberStyleValues(Workbook workbook)
    {
        String applyStyleName = ((NumberStyle)getApplyStyle()).getStyleName();
        String[] values = new String[]{applyStyleName,  
                                getConditionToWrite(workbook, 0, 0),
                                CellUtil.getCellReference(workbook.getSheet(0), 0, 0, true, true, true)};
        return values;
    }
    //////////////////////

    
    
    public String[] getDateAttributes(Workbook workbook)
    {
        String[] attrs = new String[]{"calcext:style", //No I18N
                             "calcext:date"}; //No I18N
        return attrs;
    }

    public String[] getDateValues(Workbook workbook)
    {
        String[] values = new String[]{ ((CellStyle)getApplyStyle()).getStyleName(),
                                getConditionToWrite(workbook, 0, 0)};
        return values;
    }
    
    public Object getApplyStyle()
    {
        return applyStyle;
    }

    // This method is not needed unless mapStyle comes alone(ie.. not as a child element).
    // Compare current object with another MapStyle object.
    //
    // The above comment is invalid as we support conditional formatting from out UI
    // So there can be 2 same CellStyle objects with same mapStyles and
    // To find the equality in this case we need equals method.
    @Override
    public boolean equals(Object obj)
    {
        if(this == obj)
        {
            return true;
        }

        if(obj == null || !(obj instanceof MapStyle))
        {
            return false;
        }

         MapStyle inMapStyle = (MapStyle)obj;
         if(this.isMemberOfNumberStyle() != inMapStyle.isMemberOfNumberStyle())
         {
             return false;
         }

        if(super.equals(inMapStyle))
        {
            Object thisStyle = this.getApplyStyle();
            Object inStyle = inMapStyle.getApplyStyle();
            if(thisStyle == inStyle)
            {
                return true;
            }
            else
            {
                if(this.isMemberOfNumberStyle())
                {
                    if(thisStyle == null ? inStyle == null : thisStyle.equals(inStyle))
                    {
                        return true;
                    }
                }
                else
                {
                    return (thisStyle == null) ? inStyle == null : ((CellStyle)thisStyle).equals(inStyle);
                }
            }
        }

        return false; 
    }

    @Override
    public int hashCode()
    {
        int hash = 3;
          hash = 37 * hash + super.hashCode();
        //hash = 37 * hash + this.applyStyleName.hashCode();         
          Object style = this.applyStyle;
          if(style != null)
          {
              hash = 37 * hash + style.hashCode();
          }
        //hash = 37 * hash + this.value.hashCode();
        return hash;
    }

    @Override
    public MapStyle clone()
    {
        MapStyle o = (MapStyle)super.clone();
	return o;
    }
    
    @Override
    public ConditionalStyle absoluteClone(Workbook workbook)
    {
        MapStyle o = (MapStyle)super.absoluteClone();
        if(this.applyStyle instanceof CellStyle){
            CellStyle appliedCellStyle= (CellStyle)this.applyStyle;
            String appliedStyleName= appliedCellStyle.getStyleName();
            CellStyle newAppliedCellStyle= workbook.getCellStyle(appliedStyleName);
            if(newAppliedCellStyle == null) {
                LOGGER.log(Level.WARNING, "[MapStyle] Cell Style doesn't exist during clone ::  {0}", new Object[]{appliedStyleName});
                newAppliedCellStyle = workbook.checkAndAddCellStyle(new CellStyle());
            }
            o.applyStyle= newAppliedCellStyle;
        }
        /* Was working without this block for a long while since UNDO/REDO release
         * Should uncomment and add this block once found necessary
        else if(this.applyStyle instanceof NumberStyle) {
            NumberStyle appliedNumberStyle = (NumberStyle) this.applyStyle;
            String appliedStyleName = appliedNumberStyle.getStyleName();
        
            // Before using the following statement, confirm whether workbook.getNumberStyle(appliedStyleName) will return the child numberStyle and not null!!!
            NumberStyle newAppliedNumberStyle = workbook.getNumberStyle(appliedStyleName);
        
            o.applyStyle = newAppliedNumberStyle;
        }
        */
	return o;
    }

    @Override
    public String toString()
    {
        return ((this.applyStyle instanceof CellStyle) ? ((CellStyle)this.applyStyle).getStyleName() : ((NumberStyle)this.applyStyle).getStyleName()) + " = "+super.toString();
    }

    public boolean isMemberOfNumberStyle()
    {
        return applyStyle instanceof NumberStyle;
    }

    @Override
    public String getStyleXML(Workbook workbook, int baseRowIndex, int baseColIndex, boolean autoColor, boolean hideText) {
        String mapStyleEntryStr = null;
        // Need to uncomment these lines when all conditions are to be written like libreoffice.
//        if(isSpecialDateCondition(this.getCondnNum()))
//        {
//            mapStyleEntryStr = XMLWriter.createStartTagClose("calcext:date-is", //NO I18N
//                                                                     getDateAttributes(),
//                                                                     getDateValues(),
//                                                                     true);
//        }
//        else
//        {
            mapStyleEntryStr = XMLWriter.createStartTagClose("calcext:condition", //NO I18N
                                                                         getCFAttributes(),
                                                                         getCFValues(workbook, baseRowIndex, baseColIndex),
                                                                         true);
//        }
        return mapStyleEntryStr;
    }

    public void changeApplyStyleFromWriter(Object newApplyStyle)            
    {
        this.applyStyle = newApplyStyle;
    }
    
    @Override
    public boolean updateConditionalStyle(Sheet sheet, ReadOnlyCell rCell, ConditionalStyleObject csf, long currentTime, int csUID, Map<CellObject, ConditionalStyleCellStyles> cellStylesMap, boolean isIncludeCellStyle)
    {
        int rRepeat = 1;
        int cRepeat = 1;
        boolean isFormulaMapStyle = ((MapStyle)csf.getConditionalStyle()).getConditionType() == ConditionalFormatOperator.ConditionType.FORMULA;
        if(isFormulaMapStyle)
        {
            rRepeat = rCell.getRowsRepeated();
            cRepeat = rCell.getColsRepeated();
        }
        
        for(int rI = rCell.getRowIndex(); rI < rCell.getRowIndex()+rRepeat; rI++)
        {
            for(int cI = rCell.getColIndex(); cI < rCell.getColIndex()+cRepeat; cI++)
            {
                Cell cell = new CellImpl(new Row(sheet, rI), new Column(sheet, cI));
                CellObject cellObject = new CellObject(rI, cI);
                ConditionalStyleCellStyles csCellStyles = cellStylesMap.get(cellObject);
                Object csResult = getResult(cell, csf);
                if(csResult != null) {
                    csResult = new CellStyleHolder((CellStyle)csResult, false);
                }

                if(isIncludeCellStyle || csResult != null)
                {
                    if(csCellStyles == null)
                    {
                        csCellStyles = new ConditionalStyleCellStyles();
                        if(isIncludeCellStyle)
                        {
                            // Sending CellSTyle details too as client does not maintain both CF and CellSTyle props.
                            // Should be commented once handled in client.
                            csCellStyles.setCellStyle(sheet.getCellStyleReadOnly(rI, cI));
                        }
                        cellStylesMap.put(cellObject, csCellStyles);

                        Cell readOnlyCell = sheet.getReadOnlyCellFromShell(rI, cI).getCell();
                        if(readOnlyCell != null)
                        {
                            csCellStyles.setPatternTextColor(readOnlyCell.getContentColor());
                        }
                    }
                    if(csResult != null)
                    {
                        this.setConditionalStyleResult(csCellStyles, csResult);
                    }
                    
                    if(!isFormulaMapStyle)
                    {
                        String ptc = csCellStyles.getPatternTextColor();
                        for(int r = rI; r < rI+rCell.getRowsRepeated(); r++)
                        {
                            for(int c = cI; c < cI+rCell.getColsRepeated(); c++)
                            {
                                if(r != rI || c != cI)
                                {
                                    cellObject = new CellObject(r, c);
                                    ConditionalStyleCellStyles newCSCellStyles = cellStylesMap.get(cellObject);
                                    if(newCSCellStyles == null)
                                    {
                                        newCSCellStyles = new ConditionalStyleCellStyles();
                                        if(isIncludeCellStyle)
                                        {
                                            // Sending CellSTyle details too as client does not maintain both CF and CellSTyle props.
                                            // Should be commented once handled in client.
                                            newCSCellStyles.setCellStyle(sheet.getCellStyleReadOnly(r, c));
                                        }
                                        cellStylesMap.put(cellObject, newCSCellStyles);
                                        if(ptc != null)
                                        {
                                            newCSCellStyles.setPatternTextColor(ptc);
                                        }
                                    }
                                    if(csResult != null)
                                    {
                                        this.setConditionalStyleResult(newCSCellStyles, csResult);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean hasPercentileEntry() {
        return false;
    }

    @Override
    public boolean hasFormulaEntry() {
        return isFormulaType();
    }

    @Override
    public List<ConditionalFormatEntry> getConditionalStyleEntries() {
        ConditionalFormatEntry tempCFEntry = new ConditionalFormatEntry(ConditionalFormatEntry.Entry_Type.FORMULA, this.getExpression());
        return Arrays.asList(tempCFEntry);
    }

    @Override
    public Object getResult(Cell cell, ConditionalStyleObject conditionalStyleFormat)
    {
        try 
        { 
            Node node = this.getExpression().getNode();
            ZSEvaluator zsEvaluator = new ZSEvaluator(true);
            Object result = zsEvaluator.evaluate(node, cell, false, false);
            result = (result instanceof Value) ? ((Value) result).getValue() : result;
            if(result instanceof Boolean || result instanceof Date || result instanceof Number)
            {
                double resultNum = FunctionUtil.objectToNumber(result).doubleValue();
                if(resultNum != 0)
                { 
                    return (CellStyle)this.getApplyStyle();
                }
            }                                
        }
        catch (EvaluationException ex){} // Do nothing
        return null;
    }

    public Object getResult(Cell cell)
    {
        try 
        { 
            Node node = this.getExpression().getNode();
            ZSEvaluator zsEvaluator = new ZSEvaluator(true);
            Object result = zsEvaluator.evaluate(node, cell, false, false);
            result = (result instanceof Value) ? ((Value) result).getValue() : result;
            if(result instanceof Boolean || result instanceof Date || result instanceof Number)
            {
                double resultNum = FunctionUtil.objectToNumber(result).doubleValue();
                if(resultNum != 0)
                { 
                    return (CellStyle)this.getApplyStyle();
                }
            }                                
        }
        catch (EvaluationException ex){} // Do nothing
        return null;
    }
    @Override
    public void setConditionalStyleResult(ConditionalStyleCellStyles csCellStyles, Object csResult) {
        csCellStyles.addCfCellStyles((CellStyleHolder)csResult);
    }

    @Override
    public boolean isConditionalStyleExists(ConditionalStyleCellStyles csCellStyles) {
        return false;
    }
}
