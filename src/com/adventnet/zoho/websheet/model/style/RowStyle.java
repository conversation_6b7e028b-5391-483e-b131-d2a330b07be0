// $Id$
/*
 * RowStyle.java
 *
 * Created on April 19, 2007, 12:25 PM
 */

package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.Workbook;
import java.util.logging.Level;
import java.util.logging.Logger; 

/**
 *
 * <AUTHOR>
 */
public class RowStyle extends Style implements Cloneable
{
	public static Logger logger = Logger.getLogger(RowStyle.class.getName());

    private RowStyleProperties rowStyleProperties;

    public enum Property implements Style.Property {
        ROWHEIGHT,
        MINROWHEIGHT,
        USEOPTIMALROWHEIGHT,
        BACKGROUNDCOLOR,
        KEEPTOGETHER,
        BREA<PERSON>BEFORE,
        BREAKAFTER;
    }

    public static void setProperty(RowStyleProperties rowStyleProperties, RowStyle.Property property, Object value){
        try{
            RowStyleProperties.Property key = Enum.valueOf(RowStyleProperties.Property.class, property.name());
            rowStyleProperties.setProperty(key, value);
        }
        catch (IllegalArgumentException ex){
            throw new IllegalArgumentException(ex + " Either property name or value mismatched..."); //No I18N
        }
    }

    public static Object getProperty(RowStyleProperties rowStyleProperties, RowStyle.Property property){
        RowStyleProperties.Property key = Enum.valueOf(RowStyleProperties.Property.class, property.name());
        return rowStyleProperties.getProperty(key);
    }

    StyleProperties getStyleProperties(PropertyInstance propertyInstance){
        if(propertyInstance == PropertyInstance.ROWSTYLE){
            if(this.rowStyleProperties == null)
            {
                this.rowStyleProperties = new RowStyleProperties();
            }
            return this.rowStyleProperties;
        }
        else{
            throw new IllegalArgumentException("Unsupported property..." + propertyInstance); // No I18N
        }
    }

    public RowStyle(){

    }
    
    public RowStyleProperties getRowStyleProperties()
    {
        return (RowStyleProperties) getStyleProperties(PropertyInstance.ROWSTYLE);
    }

        @Override
    public RowStyle clone()
    {
        RowStyle rowStyle = null;
        try
        {
            rowStyle = (RowStyle)super.clone();
            rowStyle.rowStyleProperties = (RowStyleProperties)this.getRowStyleProperties().clone();
        }catch(CloneNotSupportedException e)
        {
			logger.log(Level.WARNING,null,e);
        }

        return rowStyle;
    }

        @Override
    public String toString()
    {
        StringBuilder sbuff = new StringBuilder();

        sbuff.append(super.toString());

        sbuff.append(this.getRowStyleProperties().toString());
        return sbuff.toString();
    }

        @Override
    public boolean equals(Object o)
    {
        if(this == o)
        {
            return true;
        }

        if(o != null && o instanceof RowStyle && super.equals(o))
        {
            RowStyle rowStyle = (RowStyle)o;
            return this.getRowStyleProperties().equals(rowStyle.getRowStyleProperties());
        }

        return false;
    }
    @Override
    public void getStylePropsFromPool(Workbook workbook) {
        if(this.rowStyleProperties != null && !this.rowStyleProperties.isThroughPool()) {
            this.rowStyleProperties = (RowStyleProperties) workbook.stylePropertiesPool.getStyleProperties(this.getRowStyleProperties());
        }
    }
    
    public String getStyleFamily()
    {
        return "table-row";//No I18N
    }
}
