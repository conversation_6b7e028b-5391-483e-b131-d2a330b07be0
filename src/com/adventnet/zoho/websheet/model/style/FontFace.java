// $Id$
/*
 * FontFace.java
 *
 * Created on May 3, 2007, 12:37 PM
 */

package com.adventnet.zoho.websheet.model.style;


import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class FontFace implements Cloneable
{
    String styleName; // style:name="Arial"
    String fontFamily; // svg:font-family="Arial"
    String fontFamilyGeneric; // style:font-family-generic="swiss" 
    String fontPitch; // style:font-pitch="variable"
    String fontAdornments;//style:font:adornments="regular"  ADDED by ganesh
    /** Creates a new instance of FontFace */
    
    private String fontCharset;
    
    public FontFace()
    {
    }
    
   
    /**
     * Getter for property fontFamily.
     * @return Value of property fontFamily.
     */
    public java.lang.String getFontFamily()
    {
        return fontFamily;
    }
    
    /**
     * Setter for property fontFamily.
     * @param fontFamily New value of property fontFamily.
     */
    public void setFontFamily(java.lang.String fontFamily)
    {
        this.fontFamily = fontFamily;
    }
    
    /**
     * Getter for property fontFamilyGeneric.
     * @return Value of property fontFamilyGeneric.
     */
    public java.lang.String getFontFamilyGeneric()
    {
        return fontFamilyGeneric;
    }
    
    /**
     * Setter for property fontFamilyGeneric.
     * @param fontFamilyGeneric New value of property fontFamilyGeneric.
     */
    public void setFontFamilyGeneric(java.lang.String fontFamilyGeneric)
    {
        this.fontFamilyGeneric = fontFamilyGeneric;
    }
    
    /**
     * Getter for property fontPitch.
     * @return Value of property fontPitch.
     */
    public java.lang.String getFontPitch()
    {
        return fontPitch;
    }
    
    /**
     * Setter for property fontPitch.
     * @param fontPitch New value of property fontPitch.
     */
    public void setFontPitch(java.lang.String fontPitch)
    {
        this.fontPitch = fontPitch;
    }
    
    /**
     * Getter for property styleName.
     * @return Value of property styleName.
     */
    public java.lang.String getStyleName()
    { 
        return styleName;
    }
    
    /**
     * Setter for property styleName.
     * @param styleName New value of property styleName.
     */
    public void setStyleName(java.lang.String styleName)
    {
        this.styleName = styleName;
    }
    
//    public String toString()
//    {
//        return (styleName+" : "+fontFamily);
//    }
    
    //ADDED BY GANESH
    
     public java.lang.String getFontAdornments()
    {
        return fontAdornments;
    }
   
    public void setFontAdornments(java.lang.String fontAdornments)
    {
        this.fontAdornments = fontAdornments;
    }

    @Override
    /**
     * For now, We are checking only font family name for equality check.
     * TODO Update equals method with other fields if needed in future.
     */
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        FontFace fontFace = (FontFace) o;
        return  Objects.equals(fontFamily, fontFace.fontFamily);
    }

    @Override
    /**
     * For now, We are checking only font family name for equality check.
     * TODO Update hashCode method with other fields if needed in future.
     */
    public int hashCode()
    {
        return Objects.hash(fontFamily);
    }

    public String getFontCharset() {
        return fontCharset;
    }

    public void setFontCharset(String fontCharset) {
        this.fontCharset = fontCharset;
    }
    
    public String[] getAttributes()
    {
       String[] attrs = new String[]{"style:name",
                            "svg:font-family",
                            "style:font-family-generic",
                            "style:font-pitch",
                            "style:font-adornments",
                            "style:font-charset"};
       return attrs;                     
    }
    
    public String[] getValues()
    {
        String[] values = new String[]{getStyleName(),
                              getFontFamily(),
                              getFontFamilyGeneric(),
                              getFontPitch(),
                              getFontAdornments(),
                              getFontCharset()};
                            
        return values;                    
    } 
    
    public String toString()
    {
        StringBuffer sbuff = new StringBuffer();
        String values1[] = this.getValues();
        String attrs1[] = this.getAttributes();
        for(int i=0; i<values1.length; i++)
        {
            String value = values1[i];
            if(value != null)
            {
                sbuff.append(attrs1[i]+" : "+value+" , ");
            }
            
        }
        return sbuff.toString();
    }
    public FontFace clone()
    {
        try{
            return (FontFace)super.clone();
        }catch(Exception e)
        {
            e.printStackTrace();
        }
        return null;  
    }
}
