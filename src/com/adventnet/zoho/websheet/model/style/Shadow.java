package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.zsparser.Names;
import com.adventnet.zoho.websheet.model.zsparser.XmlName;

import java.util.Objects;
import java.util.logging.Logger;

public class Shadow implements Cloneable{
// x = r × cos( θ ) y = r × sin( θ )
    private final ZSColor color;
    private int blur = 1; //px
    private int transparency = 60;
    private int spread = 0;

    private double angle = 45;
    private double distance = 10;

    private boolean isInner = false;

    public static Logger logger = Logger.getLogger(Shadow.class.getName());

    public Shadow(ZSColor color) {
        if(color == null)
        {
            throw new IllegalArgumentException("shadow color should not be null"); //No I18N
        }
        this.color = color;
    }

    public double getAngle() {
        return angle;
    }

    public int getBlur() {
        return blur;
    }

    public double getDistance() {
        return distance;
    }

    public boolean isInner() {
        return isInner;
    }

    public void setTransparency(int transparency) {
        if(transparency < 0 || transparency > 100)
        {
            transparency = 60;
        }
        this.transparency = transparency;
    }

    public int getSpread() {
        return spread;
    }

    public void setSpread(int spread) {
        if(spread < 0)
        {
            spread = 0;
        }
        this.spread = spread;
    }

    public void setAngle(double angle) {
        this.angle = angle % 360;
    }

    public void setBlur(int blur) {
        if(blur < 0 || blur > 100) {
             blur = 4;
        }
        this.blur = blur;
    }

    public void setDistance(double distance) {
        if(distance < 0)
        {
            distance = 10;
        }
        this.distance = distance;
    }

    public void setInner(boolean inner) {
        isInner = inner;
    }

    public ZSColor getColor() {
        return color;
    }

    public int getTransparency() {
        return transparency;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) { return true; }
        if (!(object instanceof Shadow)) { return false; }
        Shadow that = (Shadow) object;
        return this.transparency == that.transparency &&
                this.color.equals(that.color) &&
                this.blur == that.blur &&
                this.isInner == that.isInner &&
                this.angle == that.angle &&
                this.distance == that.distance;

    }

    @Override
    public int hashCode() {
        return Objects.hash(color, angle, blur, distance, isInner, transparency);
    }

    @Override
    public Shadow clone() {
        try {
            return (Shadow) super.clone();
        } catch (CloneNotSupportedException e) {
            logger.info("Shadow can't be cloned");//No I18N
        }
        return null;
    }

    public XmlName[] getAttributes() {
        return new XmlName[] {
                Names.SHADOW_NAME,
                Names.SHADOW_HEX_COLOR,
                Names.SHADOW_THEME_COLOR,
                Names.SHADOW_COLOR_TINT,
                Names.SHADOW_ANGLE,
                Names.SHADOW_BLUR,
                Names.SHADOW_DISTANCE,
                Names.SHADOW_SPREAD,
                Names.SHADOW_TRANSPARENCY,
                Names.SHADOW_INNER
        };
    }
    public String[] getValues(String shadowName){

        return new String[] {
                shadowName,
                this.color.getHexColorToWrite(),
                this.color.getThemeColorToWrite(),
                this.color.getColorTintToWrite(),
                String.valueOf(this.angle),
                String.valueOf(this.blur),
                String.valueOf(this.distance),
                String.valueOf(this.spread),
                String.valueOf(this.transparency),
                this.isInner ? String.valueOf(true) : null
        };
    }
}
