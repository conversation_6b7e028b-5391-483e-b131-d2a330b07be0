// $Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SheetStyleProperties extends StyleProperties
{
    public enum Property implements StyleProperties.Property{
        WIDTH,
        RELWIDTH,
        ALIGN,
        MARGIN,
        MARGINLEFT,
        MARGINRIGHT,
        MARGINTOP,
        MARGINBOTTOM,
        PAGENUMBER,
        BREAKBEFORE,
        BREAKAFTER,
        BACKGROUNDCOLOR,
        BACKGROUNDIMAGE,
        SHADOW,
        KEEPWITHNEXT,
        MAYBREAKBETWEENROWS,
        BOR<PERSON>R<PERSON>DE<PERSON>,
        <PERSON><PERSON><PERSON><PERSON>DE,
        <PERSON><PERSON><PERSON><PERSON>Y,
        TABCOLOR;

        public String[] getAttributes(){
            switch (this){
                case    WIDTH:
                    return new String[]{"style:width"}; //NO I18N
                case    RELWIDTH:
                    return new String[]{"style:rel-width"}; //NO I18N
                case    ALIGN:
                    return new String[]{"table:align"}; //NO I18N
                case    MARGIN:
                    return new String[]{"fo:margin"}; //NO I18N
                case    MARGINLEFT:
                    return new String[]{"fo:margin-left"}; //NO I18N
                case    MARGINRIGHT:
                    return new String[]{"fo:margin-right"}; //NO I18N
                case    MARGINTOP:
                    return new String[]{"fo:margin-top"}; //NO I18N
                case    MARGINBOTTOM:
                    return new String[]{"fo:margin-bottom"}; //NO I18N
                case    PAGENUMBER:
                    return new String[]{"style:page-number"}; //NO I18N
                case    BREAKBEFORE:
                    return new String[]{"fo:break-before"}; //NO I18N
                case    BREAKAFTER:
                    return new String[]{"fo:break-after"}; //NO I18N
                case    BACKGROUNDCOLOR:
                    return new String[]{"fo:background-color"}; //NO I18N
                case    BACKGROUNDIMAGE:
                    return new String[]{"style:background-image"}; //NO I18N
                case    SHADOW:
                    return new String[]{"style:shadow"}; //NO I18N
                case    KEEPWITHNEXT:
                    return new String[]{"fo:keep-with-next"}; //NO I18N
                case    MAYBREAKBETWEENROWS:
                    return new String[]{"style:may-break-between-rows"}; //NO I18N
                case    BORDERMODEL:
                    return new String[]{"table:border-model"}; //NO I18N
                case    WRITINGMODE:
                    return new String[]{"style:writing-mode"}; //NO I18N
                case    DISPLAY:
                    return new String[]{"table:display"}; //NO I18N
                case TABCOLOR:
                    return new String[]{"tableooo:tab-color", "fo:theme-color", "fo:color-tint"}; //NO I18N
                default:
                    return null;
            }
        }
    }

    protected Object getProperty(Property property){
        Object value = this.propertiesMap.get(property);
        if(property == Property.WRITINGMODE && value == null){
            value = "lr-tb"; //NO I18N
        }
        return value;
    }

    protected void setProperty(Property key, Object value){
        try{
            switch (key) {
                case TABCOLOR:
                    this.propertiesMap.put(key, (ZSColor)value);
                    break;
                default:
                    this.propertiesMap.put(key, (String)value);
                    break;
            }
        }
        catch (Exception e){
            throw new IllegalArgumentException(e + "the data type of value has no relationship with property..."); //No I18N
        }
    }

    public void getZSValues(String[] propValues)
    {
        Property[] properties = Property.values();
        int index = 0;
        for(Property key : properties){
            switch (key){
                case TABCOLOR:
                    ZSColor color = ((ZSColor)this.propertiesMap.get(Property.TABCOLOR));
                    propValues[index++] = color == null ? null : color.getHexColorToWrite();
                    propValues[index++] = color == null ? null : color.getThemeColorToWrite();
                    propValues[index++] = color == null ? null : color.getColorTintToWrite();
                    break;
                default:
                    propValues[index++] = (String)this.propertiesMap.get(key);
            }
        }
    }

    public String[] getAttributes()
    {
        Property[] properties = Property.values();
        List<String> attributes =  new ArrayList<>();
        for(Property property : properties){
            String[] propertyAttributes = property.getAttributes();
            for(String attribute : propertyAttributes){
                attributes.add(attribute);
            }
        }
        return attributes.toArray(new String[0]);
    }

    public String[] getValues(Workbook workbook, Style style)
    {
        ZSTheme theme = workbook.getTheme();
        Property[] properties = Property.values();
        List<String> values = new ArrayList<>();
        for(Property key : properties){
            switch (key){
                case TABCOLOR:
                    ZSColor color = ((ZSColor)this.propertiesMap.get(Property.TABCOLOR));
                    values.add(color == null ? null : ZSColor.getHexColor(color, theme));
                    values.add(null);
                    values.add(null);
                    break;
                default:
                    values.add((String)this.propertiesMap.get(key));
            }
        }
        return values.toArray(new String[0]);
    }

    
    public String getTagName()
    {
        return "style:table-properties";//No I18N
    }
}
