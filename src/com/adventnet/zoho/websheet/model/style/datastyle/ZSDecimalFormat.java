/* $Id$ */
package com.adventnet.zoho.websheet.model.style.datastyle;

import com.adventnet.zoho.websheet.model.SpreadsheetSettings;
import com.adventnet.zoho.websheet.model.ext.functions.Rept;
import com.adventnet.zoho.websheet.model.style.EmbeddedText;
import com.adventnet.zoho.websheet.model.style.NumberElement;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.GeneralUtil;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.*;

public class ZSDecimalFormat implements PatternComponent{
    private final DecimalFormat decimalFormat;
    private double displayFactor;
    private boolean isGroupingNeeded;
    private int displayFactorCount = 0;
    private Map<Integer,List<PatternComponent>> embeddedComponentMap;
    private SpreadsheetSettings spreadsheetSettings;
    private List<Integer> spaceIndexArray;

    public ZSDecimalFormat(DecimalFormat df, Map<Integer,List<PatternComponent>> embeddedComponentMap, double displayFactor, SpreadsheetSettings spreadsheetSettings, List<Integer> spaceIndexArray, boolean useGrouping) {
        this.decimalFormat = df;
        this.decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
        if(embeddedComponentMap != null &&  embeddedComponentMap.isEmpty()) {
           embeddedComponentMap = null;
        }
        this.embeddedComponentMap = embeddedComponentMap;
        this.displayFactor = displayFactor;
        if(displayFactor > 1)
        {
            this.displayFactor = displayFactor;
            double tempDisPlayFactor = displayFactor;
            while(tempDisPlayFactor > 1)
            {
                this.displayFactorCount++;
                tempDisPlayFactor /= 1000;
            }
        }
        this.spreadsheetSettings = spreadsheetSettings;
        if(spaceIndexArray != null && spaceIndexArray.isEmpty()) {
            spaceIndexArray = null;
        }
        this.spaceIndexArray = spaceIndexArray;
        this.isGroupingNeeded = useGrouping;

        df.setGroupingUsed(false);
        df.setNegativeSuffix("");
        df.setPositiveSuffix("");
        df.setNegativePrefix("");
        df.setPositivePrefix("");
    }

    public String toPattern(DecimalFormatSymbols dfs, boolean prependLocale) {
//        DecimalFormatSymbols dfs = this.decimalFormat.getDecimalFormatSymbols();
        char groupingSeparator = dfs.getGroupingSeparator();
        char decimalSeperator = dfs.getDecimalSeparator();
//            String integralPart = "[" + formatLocale.getLanguage() + "-" + formatLocale.getCountry() + "]";
        String integralPart = "";
        integralPart += this.decimalFormat.toPattern();  /* The pattern string should be in sheet locale not in the format locale so not using toLocalisedPattern() */
        if(integralPart.indexOf(";") != -1) {
            integralPart = integralPart.substring(0,integralPart.indexOf(";"));
        }

        integralPart = translateToLocale(new DecimalFormatSymbols(EngineConstants.DEFAULT_LOCALE), dfs, integralPart);

        int decimalIndex = integralPart.indexOf(decimalSeperator);
        integralPart = (this.spaceIndexArray == null || this.spaceIndexArray.isEmpty()) ? integralPart : insertQuestionFormat(integralPart,dfs);
        String decimalPart = "";
        if (decimalIndex != -1) {
            decimalPart = integralPart.substring(decimalIndex + 1);
            integralPart = integralPart.substring(0, decimalIndex);
        }
        int integralPartLength = integralPart.length();
        boolean isGroupingSeparatorNeeded = this.isGroupingNeeded;

        if(this.embeddedComponentMap != null) {
            Iterator<Map.Entry<Integer, List<PatternComponent>>> iterator = embeddedComponentMap.entrySet().iterator();
            Map.Entry<Integer, List<PatternComponent>> entry = null;
            while (iterator.hasNext()) {
                entry = iterator.next();
                int key = entry.getKey();
                if (key < 0) {
                    key = Math.abs(key + 1);
                    List<PatternComponent> componentList = entry.getValue();
                    String componentString = "";
                    for (PatternComponent component : componentList) {
                        componentString += component.toPattern(dfs,prependLocale);
                    }
                    decimalPart = decimalPart.substring(0, key) + componentString + decimalPart.substring(key);
                    entry = null;
                } else {
                    break;
                }
            }

            int index = integralPartLength;
            StringBuilder integralStringBuilder = new StringBuilder(integralPart);
            if (entry != null) {
                index = Math.max(entry.getKey(), index);
            }
            index = index < 3 ? isGroupingSeparatorNeeded ? 3 : index : index;

            while (entry != null || index >= 0 || isGroupingSeparatorNeeded) {
                if (index >= integralStringBuilder.length()) {
                    integralStringBuilder.insert(0, Rept.rept("#", index - integralStringBuilder.length() + 1));
                }

                if (index == 3 && isGroupingSeparatorNeeded) {
                    isGroupingSeparatorNeeded = false;
                    int indexFromLeft = integralStringBuilder.length() - index;
                    integralStringBuilder.insert(indexFromLeft, groupingSeparator);
                }

                if (entry != null) {
                    int key = entry.getKey();
                    List<PatternComponent> componentList = entry.getValue();
                    if (key == index) {
                        String componentString = "";
                        int indexFromLeft = integralStringBuilder.length() - index;
                        for (int i = componentList.size() - 1; i >= 0; i--) {
                            PatternComponent component = componentList.get(i);
                            integralStringBuilder.insert(indexFromLeft, component.toPattern(dfs,prependLocale));
                        }
                        if (iterator.hasNext()) {
                            entry = iterator.next();
                        } else {
                            entry = null;
                        }
                    }
                }
                index--;
            }
            integralPart = integralStringBuilder.toString();
        }
        else {
            if(isGroupingSeparatorNeeded) {
                if(integralPartLength <= 3) {
                    integralPart = Rept.rept("#",4-integralPartLength) + integralPart;
                    integralPartLength += 4 - integralPartLength;
                }
                integralPart = integralPart.substring(0,integralPartLength-3) + groupingSeparator + integralPart.substring(integralPartLength-3,integralPartLength);
            }
        }

        String displayFactorString = "";
        if(displayFactorCount >= 1) {
            displayFactorString = Rept.rept(String.valueOf(groupingSeparator),displayFactorCount);
        }

        if(prependLocale) {
            String localeString = "[" + this.spreadsheetSettings.getLocale().getLanguage() + "-" + this.spreadsheetSettings.getLocale().getCountry() + "]";
            integralPart = localeString + integralPart;
        }
        return decimalIndex == -1 ? integralPart + displayFactorString : integralPart + decimalSeperator + decimalPart + displayFactorString ;
    }

    public String format(Object obj) {
        return format(obj, null, false);
    }

    public String format(Object obj, Map<Integer, Character> invisibleCharacters, boolean isScientificFormat) {
        if(decimalFormat != null) {
            boolean isIndianGrouping = this.spreadsheetSettings.getNumberGroupingType() == SpreadsheetSettings.NumberGroupingType.INDIAN;
            String valueStr = "";
            if (!(decimalFormat.getMinimumIntegerDigits() == 0 && decimalFormat.getMinimumFractionDigits() == 0 && ((Number) obj).doubleValue() == 0)) {

                if(displayFactorCount > 0)
                {
                    obj = ((Number) obj).doubleValue()/displayFactor;
                }
                /* When the value has more decimal places than the decimalFormat's MaxFractionDigits, it needs to be round down.
                * But certain values like 0.705 will round to 0.70 instead of 0.71 because of limitation in java floating point number.
                * So the number is rounded using our code, before passing to format() */
                if(isScientificFormat) {
                    int dp = getAdditionalDPCountForScientificFormat((Number) obj, decimalFormat);
                    if(dp != -1) {
                        obj = GeneralUtil.round((Number)obj, dp, 0);
                    }
                }
                else {
                    obj = GeneralUtil.round((Number)obj, decimalFormat.getMultiplier() == 100 ? decimalFormat.getMaximumFractionDigits() + 2 : decimalFormat.getMaximumFractionDigits(), 0);
                }
                valueStr = decimalFormat.format((Number)obj);
                char groupingSeparator = decimalFormat.getDecimalFormatSymbols().getGroupingSeparator();
                char decimalSeperator = decimalFormat.getDecimalFormatSymbols().getDecimalSeparator();

                int decimalIndex = valueStr.indexOf(decimalSeperator);
                String modifiedString = formatSpaces(valueStr);
                valueStr =  modifiedString == null ? valueStr : modifiedString;

                String integralPart = valueStr;
                String decimalPart = "";
                if(decimalIndex != -1) {
                    decimalPart = valueStr.substring(decimalIndex+1);
                    integralPart = valueStr.substring(0,decimalIndex);
                }
                int integralPartLength = integralPart.length();

                Map<Integer, Character> invisCharFromRight = null;
                Map<Integer, Character> decimalInvisCharFromRight = null;
                if(embeddedComponentMap != null && !embeddedComponentMap.isEmpty()) {
                /* Negative keys will always come first. Will break when positive key in encountered.
                 * Integral part will be processed separately because grouping separator has to be added for integral part. */
                    for (Map.Entry<Integer, List<PatternComponent>> entry : embeddedComponentMap.entrySet()) {
                        int key = entry.getKey();
                        List<PatternComponent> componentList = entry.getValue();
                        if (key < 0) {
                            key = Math.abs(key + 1);
                            String componentString = "";
                            for (int j=componentList.size()-1; j>=0; j--) {
                                PatternComponent component = componentList.get(j);
                                if(component instanceof SpaceChar && invisibleCharacters != null) {
                                    if(decimalInvisCharFromRight == null) {
                                        decimalInvisCharFromRight = new HashMap<>();
                                    }
                                    decimalInvisCharFromRight.put(decimalPart.length() - key + componentString.length(), ((SpaceChar) component).getCharacter());
                                }
                                componentString = component.format(obj) + componentString;
                            }
                            if(decimalPart.length() < key) {
                                decimalPart += componentString;
                            }
                            else {
                                decimalPart = decimalPart.substring(0, key) + componentString + decimalPart.substring(key);
                            }
                        } else {
                            break;
                        }
                    }

                    for (int i = 0; i <= integralPartLength; i++) {
                        int indexFromLeft = integralPartLength - i;
                        List<PatternComponent> componentList = embeddedComponentMap.get(i);
                        boolean insertGroupingChar = isGroupingNeeded && ((isIndianGrouping && (((i-3)%2==0 && i>3) || (i==3))) || (!isIndianGrouping && i%3 == 0)) && i != 0;
                        if (insertGroupingChar) {
                            String leftPart = integralPart.substring(0, indexFromLeft);
                            if(!leftPart.isEmpty()) {
                                integralPart = leftPart + groupingSeparator + integralPart.substring(indexFromLeft);
                            }
                        }
                        if (componentList != null) {
                            String componentString = "";
                            for (int j=componentList.size()-1; j>=0; j--) {
                                PatternComponent component = componentList.get(j);
                                if(component instanceof SpaceChar && invisibleCharacters != null) {
                                    if(invisCharFromRight == null) {
                                        invisCharFromRight = new HashMap<>();
                                    }
                                    invisCharFromRight.put(integralPart.length() - indexFromLeft + componentString.length() , ((SpaceChar) component).getCharacter());
                                }
                                componentString = component.format(obj) + componentString;
                            }
                            integralPart = integralPart.substring(0, indexFromLeft) + componentString + integralPart.substring(indexFromLeft);

                        }
                    }
                }
                else if(isGroupingNeeded){
                    for (int i = 0; i <= integralPartLength; i++) {
                        int indexFromLeft = integralPartLength - i;
                        boolean insertGroupingChar = ((isIndianGrouping && (((i - 3) % 2 == 0 && i > 3) || (i == 3))) || (!isIndianGrouping && i % 3 == 0)) && i != 0;
                        if (insertGroupingChar) {
                            String leftPart = integralPart.substring(0, indexFromLeft);
                            if (!leftPart.isEmpty()) {
                                integralPart = leftPart + groupingSeparator + integralPart.substring(indexFromLeft);
                            }
                        }
                    }
                }

                if(invisCharFromRight != null) {
                    for(Map.Entry<Integer, Character> entry: invisCharFromRight.entrySet()) {
                        invisibleCharacters.put(integralPart.length()-entry.getKey()-1, entry.getValue());
                    }
                }

                valueStr = integralPart + (!decimalPart.isEmpty() ? (decimalSeperator + decimalPart) : "");

                if(decimalInvisCharFromRight != null) {
                    for(Map.Entry<Integer, Character> entry: decimalInvisCharFromRight.entrySet()) {
                        invisibleCharacters.put(valueStr.length()-entry.getKey()-1, entry.getValue());
                    }
                }

            }
            return valueStr;
        }
        return "";
    }

    private int getAdditionalDPCountForScientificFormat(Number num, DecimalFormat decimalFormat) {
        int additionalDPCount = 1;
        num = num.doubleValue();
        String str = num.toString();

        for(int index = 0; index < str.length(); index++) {
            char c = str.charAt(index);
            if(c == 'E' || c == 'e') {
                return -1;
            }
        }

        int i = 0;
        i = (!str.isEmpty() && str.charAt(0) == '-') ? i+1 : i;

        while(i < str.length() && str.charAt(i) != '.') {
            if(str.charAt(i) != '0') {
                return decimalFormat.getMaximumFractionDigits();
            }
            i++;
        }

        if(i < str.length() && str.charAt(i) == '.') {
            i++;
        }
        else {
            return decimalFormat.getMaximumFractionDigits();
        }

        while(i < str.length() && str.charAt(i) == '0') {
            additionalDPCount++;
            i++;
        }

        return decimalFormat.getMaximumFractionDigits() + additionalDPCount;
    }


    public String formatSpaces(String formatString)
    {
        if(this.spaceIndexArray == null || this.spaceIndexArray.isEmpty()) {
            return formatString;
        }
        char decimalSeparator = decimalFormat.getDecimalFormatSymbols().getDecimalSeparator();
        char zeroDigit = decimalFormat.getDecimalFormatSymbols().getZeroDigit();
        int indexOfDecimalSeparator = formatString.indexOf(decimalSeparator);
        boolean isContainDecimal = indexOfDecimalSeparator != -1;
        int indexOfExponent = formatString.indexOf("E");
        String exponent = "";
        if(indexOfExponent != -1)
        {
            exponent = formatString.substring(indexOfExponent);
            formatString = formatString.substring(0,indexOfExponent);
        }
        String integralPart = formatString.substring(0, isContainDecimal ? indexOfDecimalSeparator : formatString.length());
        String decimalPart = isContainDecimal ? formatString.substring(indexOfDecimalSeparator+1) : "";

        boolean isFoundSignificantNum = false;
        int j = 0;
        List<Integer> indexOfSpacesBDS = new ArrayList<>();
        List<Integer> indexOfSpacesADS = new ArrayList<>();
        for(int index : this.spaceIndexArray)
        {
            if(index >=0 )
            {
                indexOfSpacesBDS.add(index);
            }
            else
            {
                index = -index -1 ; // removing 1 and the negative sign which was added while creating format
                indexOfSpacesADS.add(index);
            }
        }
        if(!indexOfSpacesBDS.isEmpty())
        {
            String tempIntegralPart = "";
            for (int i = 0; i < integralPart.length() ; i++)
            {
                char ch = integralPart.charAt(i);
                if( j < indexOfSpacesBDS.size() && (integralPart.length() - 1 - i == indexOfSpacesBDS.get(j)) )
                {
                    if (integralPart.charAt(i) == zeroDigit)
                    {
                        if(!isFoundSignificantNum)
                        {
                            ch = ' ';
                        }
                    }
                    else
                    {
                        isFoundSignificantNum = true;
                    }
                    j++;
                }
                else if (integralPart.charAt(i) != zeroDigit)
                {
                    isFoundSignificantNum = true;
                }
                tempIntegralPart += ch;
            }
            integralPart = tempIntegralPart;
        }

        if(!indexOfSpacesADS.isEmpty())
        {
            String tempDecimalPart = "";
            isFoundSignificantNum = false;
            j = indexOfSpacesADS.size() - 1;
            for (int i = decimalPart.length() - 1; i >= 0 ; i--)
            {
                String ch = decimalPart.charAt(i)+"";
                if(j >= 0 && i == indexOfSpacesADS.get(j))
                {
                    if (decimalPart.charAt(i) == zeroDigit)
                    {
                        if(!isFoundSignificantNum)
                        {
                            ch = " ";
                        }
                    }
                    else
                    {
                        isFoundSignificantNum = true;
                    }
                    j--;
                }
                else if(decimalPart.charAt(i) != zeroDigit)
                {
                    isFoundSignificantNum = true;
                }
                tempDecimalPart = ch + tempDecimalPart;
            }
            decimalPart =  tempDecimalPart;
        }
        formatString = integralPart + (isContainDecimal? decimalSeparator: "") + decimalPart + exponent;
        return formatString;
    }

    private String insertQuestionFormat(String patternString, DecimalFormatSymbols dfs)
    {
        char decimalSeparator = dfs.getDecimalSeparator();
        int indexOfDecimalSeparator = patternString.indexOf(decimalSeparator);
        boolean isContainDecimal = indexOfDecimalSeparator != -1;
        char[] integralChars = patternString.substring(0, isContainDecimal ? indexOfDecimalSeparator : patternString.length()).toCharArray();
        char[] decimalChars = isContainDecimal ? patternString.substring(indexOfDecimalSeparator+1).toCharArray() : "".toCharArray();
        for(int index : this.spaceIndexArray)
        {
            if(index >=0 )
            {
                index = integralChars.length - 1 - index;
                if(index>=0 && index < integralChars.length)
                {
                    integralChars[index] = '?';
                }
            }
            else
            {
                index = -index -1 ;
                if(index >= 0 && index <  decimalChars.length)
                {
                    decimalChars[index] = '?';
                }
            }
        }

        patternString = String.copyValueOf(integralChars) + (isContainDecimal? decimalSeparator : "") + String.copyValueOf(decimalChars);
        return patternString;
    }

    private String translateToLocale(DecimalFormatSymbols fromLocaleDFS, DecimalFormatSymbols toLocaleDFS, String patternString) {
        StringBuilder translatedPattern = new StringBuilder();
        char decimalSeperator = fromLocaleDFS.getDecimalSeparator();
        char groupingSeperator = fromLocaleDFS.getGroupingSeparator();
        char zero = fromLocaleDFS.getZeroDigit();

        for(int i=0; i<patternString.length(); i++) {
            char character = patternString.charAt(i);
            if(character == decimalSeperator) {
                translatedPattern.append(toLocaleDFS.getDecimalSeparator());
            }
            else if(character == groupingSeperator) {
                translatedPattern.append(toLocaleDFS.getGroupingSeparator());
            }
            else if(character == zero) {
                translatedPattern.append(toLocaleDFS.getZeroDigit());
            }
            else {
                translatedPattern.append(character);
            }
        }

        return translatedPattern.toString();
    }

    @Override
    public void createNumberElements(List<NumberElement> numberElementList) {
        if(decimalFormat != null) {
            String pattern = decimalFormat.toLocalizedPattern();
            NumberElement numberElement;
            if(pattern.contains("E"))
            {
                //toLocalisedPattern will give both positive and negative pattern.we should take positive pattern alone.
                int temp = pattern.indexOf(";");
                if(temp != -1)
                {
                    pattern = pattern.substring(0, temp);
                }
                numberElement = new NumberElement("scientific-number");//No I18N

                int count = 0;
                String str = pattern.substring(pattern.indexOf("E"));

                for(int i = 0; i < str.length(); i++)
                {
                    if(str.charAt(i) == decimalFormat.getDecimalFormatSymbols().getZeroDigit())
                    {
                        count++;
                    }
                }
                numberElement.setMinExpDigits(count);
            }
            else
            {
                numberElement = new NumberElement("number"); // No I18N
                numberElement.setGroupingUsed(this.isGroupingNeeded);

                // add embedded text elements..
                if(embeddedComponentMap != null && !embeddedComponentMap.isEmpty())
                {
                    for(int pos : embeddedComponentMap.keySet()) {
                        List<PatternComponent> componentList = embeddedComponentMap.get(pos);
                        String componentString = "";
                        for(PatternComponent component : componentList) {
                            componentString += component.toPattern(new DecimalFormatSymbols(EngineConstants.DEFAULT_LOCALE),false);
                        }
                        EmbeddedText embeddedText = new EmbeddedText();
                        embeddedText.setPosition(pos);
                        embeddedText.setContent(componentString);
                        numberElement.addEmbeddedText(embeddedText);
                    }
                }
                if(this.spaceIndexArray != null && !this.spaceIndexArray.isEmpty())
                {
                    numberElement.setSpaceIndexs(this.spaceIndexArray);
                }
                numberElement.setDisplayFactor(this.displayFactor);
            }

            // If the decimal places are not fixed, LIBO writes a dummy decimalReplacement.
            // Should change this if LIBO changes its behaviour in next versions.
            if(decimalFormat.getMinimumFractionDigits() != decimalFormat.getMaximumFractionDigits())
            {
                numberElement.setDecimalReplacement("");
            }
            numberElement.setDecimalPlaces(decimalFormat.getMaximumFractionDigits());

            numberElement.setMinIntDigits(decimalFormat.getMinimumIntegerDigits());
            numberElementList.add(numberElement);
        }
    }

   /* public Locale getFormatLocale() {
        return this.formatLocale;
    }*/

    /*public void setFormatLocale(Locale locale) {
        this.formatLocale = locale;
    }*/

    public SpreadsheetSettings getSpreadsheetSettings()
    {
        return this.spreadsheetSettings;
    }
    public void setSpreadsheetSettings(SpreadsheetSettings spreadsheetSettings)
    {
        this.spreadsheetSettings = spreadsheetSettings;
    }


    public List<Integer> getSpaceIndexArray() {
        return this.spaceIndexArray;
    }

    public boolean equals(Object o) {
        if(this == o) {
            return true;
        }

        if(o == null ||!(o instanceof ZSDecimalFormat)) {
            return false;
        }

        ZSDecimalFormat informat = (ZSDecimalFormat) o;


        if((this.spaceIndexArray == null && informat.getSpaceIndexArray() != null) || (this.spaceIndexArray != null && informat.spaceIndexArray == null)) {
            return false;
        }

        if(this.spaceIndexArray != null && !this.spaceIndexArray.equals(informat.spaceIndexArray)) {
            return false;
        }

        if(this.displayFactor != informat.displayFactor) {
            return false;
        }

        if(!this.decimalFormat.equals(informat.decimalFormat)) {
            return false;
        }

        if(this.spreadsheetSettings.getNumberGroupingType() != informat.getSpreadsheetSettings().getNumberGroupingType()) {
            return false;
        }

        if(this.isGroupingNeeded != informat.isGroupingNeeded) {
            return false;
        }

        if(this.embeddedComponentMap == null && informat.embeddedComponentMap == null) {
            return true;
        }

        if(this.embeddedComponentMap == null || informat.embeddedComponentMap == null) {
            return false;
        }

        if(this.embeddedComponentMap.size() != informat.embeddedComponentMap.size()) {
            return false;
        }

        for(Integer thisPos : this.embeddedComponentMap.keySet())
        {
            List<PatternComponent> componentList1 = informat.embeddedComponentMap.get(thisPos);
            List<PatternComponent> componentList2 = this.embeddedComponentMap.get(thisPos);

            if(componentList1 == null) {
                return false;
            }

            if(componentList1.size() != componentList2.size()) {
                return false;
            }

            for(int i=0;i<componentList2.size();i++) {
                if(!componentList2.get(i).equals(componentList1.get(i))) {
                    return false;
                }
            }
        }
        return true;
    }

    public int hashCode() {
        return super.hashCode();
    }

    public DecimalFormat getDecimalFormat() {
        return decimalFormat;
    }

    public boolean isGroupingUsed() {
        return this.isGroupingNeeded;
    }

    public void setGroupingUsed(boolean grouping) {
        this.isGroupingNeeded = grouping;
    }

    public Map<Integer,List<PatternComponent>> getEmbeddedComponentMap() {
        return this.embeddedComponentMap;
    }

    public double getDisplayFactor() {
        return this.displayFactor;
    }

    public ZSDecimalFormat clone() {
        ZSDecimalFormat clone = new ZSDecimalFormat((DecimalFormat) this.decimalFormat.clone(),this.embeddedComponentMap,this.displayFactor,this.spreadsheetSettings,this.spaceIndexArray, this.isGroupingNeeded);
        return clone;
    }
}
