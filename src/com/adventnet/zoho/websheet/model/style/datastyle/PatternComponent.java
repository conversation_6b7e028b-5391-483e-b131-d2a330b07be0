/* $Id$ */
package com.adventnet.zoho.websheet.model.style.datastyle;

import com.adventnet.zoho.websheet.model.style.NumberElement;

import java.text.DecimalFormatSymbols;
import java.util.List;

public interface PatternComponent {
    String toPattern(DecimalFormatSymbols dfs, boolean prependLocaleString);
    String format(Object object);
    void createNumberElements(List<NumberElement> numberElementList);
    /* <PERSON><PERSON><PERSON><PERSON><PERSON>har, EscapeChar, PercentChar, QuottedText, RepeatChar, SpaceChar, ZSErrorFormat, ZSMessageFormat are immutable, so clone() will just return the object.
     * ZFractionFormat, ZSDecimalFormat, ZSDurationFormat, ZSSimpleDateFormat will return the cloned object */
    PatternComponent clone();
}
