/* $Id$ */
package com.adventnet.zoho.websheet.model.style.datastyle;

import com.adventnet.zoho.websheet.model.Expression;
import com.adventnet.zoho.websheet.model.ExpressionImpl;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.parser.ASTParseErrorNode;
import com.singularsys.jep.Jep;
import com.singularsys.jep.ParseException;
import com.singularsys.jep.parser.Node;

import java.text.DecimalFormatSymbols;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class ConditionalPatterns {

    List<ConditionalPattern> conditionalPatternList;
    ZSPattern textPattern;

    ConditionalPatterns(List<ConditionalPattern> patternList, int numOfSubPatterns) {
        this.conditionalPatternList = new ArrayList<>();
        initConditionalPatterns(patternList,numOfSubPatterns);
    }

    ConditionalPatterns() {
        this.conditionalPatternList = new ArrayList<>();
    }

    private void initConditionalPatterns(List<ConditionalPattern> patternList,int numOfSubPatterns) {
        DataStyleConstants.SubPatternConditionPosition positionValues[] = DataStyleConstants.SubPatternConditionPosition.values();
        for(int i=0;i<patternList.size();i++) {
            ConditionalPattern conditionalPattern = patternList.get(i);
            DataStyleConstants.SubPatternConditionPosition position = positionValues[i];
            Expression condition = conditionalPattern.getCondition();
            if(position != DataStyleConstants.SubPatternConditionPosition.TEXT) {
                if (!conditionalPattern.hasExplicitCondition()) {
                    String conditionString = null;
                    switch (numOfSubPatterns) {
                        case 2:
                            if (position == DataStyleConstants.SubPatternConditionPosition.POSITIVE) {
                                conditionString = ">=0";
                            } else {
                                conditionString = "<0";
                            }
                            condition = getConditionalExpressionFromConditionString(conditionString);
                            break;
                        case 3:
                            if (position == DataStyleConstants.SubPatternConditionPosition.POSITIVE) {
                                conditionString = ">0";
                            } else if (position == DataStyleConstants.SubPatternConditionPosition.NEGATIVE) {
                                conditionString = "<0";
                            } else if (position == DataStyleConstants.SubPatternConditionPosition.ZERO) {
                                conditionString = "";
                            }
                            if (conditionString != null) {
                                condition = getConditionalExpressionFromConditionString(conditionString);
                            }
                            break;
                        case 4:
                            if (position == DataStyleConstants.SubPatternConditionPosition.POSITIVE) {
                                conditionString = ">0";
                            } else if (position == DataStyleConstants.SubPatternConditionPosition.NEGATIVE) {
                                conditionString = "<0";
                            } else if (position == DataStyleConstants.SubPatternConditionPosition.ZERO) {
                                conditionString = "=0";
                            } else {
                            /* Text case */
                                conditionString = null;

                            }
                            if (conditionString != null) {
                                condition = getConditionalExpressionFromConditionString(conditionString);
                            }
                            break;
                    }
                }

                this.conditionalPatternList.add(new ConditionalPattern(condition, conditionalPattern.getPattern(), conditionalPattern.hasExplicitCondition()));
            }
            else {
                textPattern = conditionalPattern.getPattern();
            }
        }
    }

    public void addConditionalPatternToList(ConditionalPattern conditionalPattern) {
        this.conditionalPatternList.add(conditionalPattern);
    }

    public static Expression getConditionalExpressionFromConditionString(String conditionString) {
        conditionString= DataStyleConstants.CURRVAL_VAR_STR+conditionString;
        Jep jep = Workbook.getJepForOtherActions();
        Node conditionalNode;
        try{
            conditionalNode = jep.parse(conditionString);
        }
        catch(ParseException e){
            conditionalNode = new ASTParseErrorNode(conditionString);
        }
        return new ExpressionImpl(conditionalNode);
    }

    public static String getConditionStringFromConditionalExpression(Node conditionalNode){
        Jep jep= Workbook.getJepForOtherActions();
        String conditionString;
        if(conditionalNode instanceof ASTParseErrorNode){
            conditionString = ((ASTParseErrorNode)conditionalNode).getNonParsableFormulaString();
        }
        else{
            conditionString = jep.getPrintVisitor().toString(conditionalNode);
        }
        conditionString = conditionString.replace(DataStyleConstants.CURRVAL_VAR_STR, "");

        return conditionString;
    }

    public String toPatternString(DecimalFormatSymbols dfs) {
        StringBuilder patternString = new StringBuilder();
        for(int i=0; i<this.conditionalPatternList.size(); i++) {
            ConditionalPattern conditionalPattern = this.conditionalPatternList.get(i);
            if(i!=0) {
                patternString.append(';');
            }
            patternString.append('[');
            patternString.append(conditionalPattern.getConditionString());
            patternString.append(']');
            ZSPattern pattern = conditionalPattern.getPattern();
            patternString.append(pattern.toPatternString(dfs));
        }
        return patternString.toString();
    }

    public void setConditionalPatternList(List<ConditionalPattern> conditionalPatternList) {
        this.conditionalPatternList = conditionalPatternList;
    }

    public List<ConditionalPattern> getConditionalPatternList() {
        return this.conditionalPatternList;
    }

    public ZSPattern getTextPattern() {
        return this.textPattern;
    }

    public void setTextPattern(ZSPattern pattern) {
        this.textPattern = pattern;
    }

    public boolean equals(Object o) {
        if(this == o) {
            return true;
        }

        if(o == null || !(o instanceof ConditionalPatterns)) {
            return false;
        }

        ConditionalPatterns inCondPatterns = (ConditionalPatterns) o;

        if(this.conditionalPatternList.size() != inCondPatterns.conditionalPatternList.size()) {
            return false;
        }

        for(int i=0;i<this.conditionalPatternList.size();i++) {
            ConditionalPattern cPattern1 = this.conditionalPatternList.get(i);
            ConditionalPattern cPattern2 = inCondPatterns.conditionalPatternList.get(i);
            if(!cPattern1.equals(cPattern2)) {
                return false;
            }
        }
        return true;
    }

    public int hashCode() {
        return super.hashCode();
    }
}

