/* $Id$ */
package com.adventnet.zoho.websheet.model.style.datastyle;

import com.adventnet.zoho.websheet.model.style.NumberElement;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;

import java.text.DecimalFormatSymbols;
import java.util.List;
import java.util.Locale;

public class CurrencyChar implements PatternComponent {
    private Locale locale;
    private String symbol;

    CurrencyChar(Locale currencyLocale) {
        this.locale = currencyLocale;
        this.symbol = LocaleUtil.getCurrencySymbol(new DecimalFormatSymbols(currencyLocale).getCurrency().getCurrencyCode());
    }

    @Override
    public String toPattern(DecimalFormatSymbols inLocale, boolean prependLocale) {
        return "[$" + locale.getLanguage() + "-" + locale.getCountry() + "]";
    }

    @Override
    public String format(Object obj) {
        return symbol;
    }

    @Override
    public void createNumberElements(List<NumberElement> numberElementList) {
        NumberElement currencyElement = new NumberElement("currency-symbol"); // No I18N
        currencyElement.setLanguage(this.locale.getLanguage());
        currencyElement.setCountry(this.locale.getCountry());
        currencyElement.setContent(this.symbol);
        numberElementList.add(currencyElement);
    }

    @Override
    public boolean equals(Object o) {
        if(this == o) {
            return true;
        }

        if(o == null || !(o instanceof CurrencyChar)) {
            return false;
        }

        CurrencyChar component = (CurrencyChar) o;

        if(!this.symbol.equals(component.symbol)) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        return locale.hashCode();
    }

    public Locale getCurrencyLocale() {
        return this.locale;
    }

    public CurrencyChar clone() {
        return this;
    }
}
