package com.adventnet.zoho.websheet.model.style.datastyle;

import com.adventnet.zoho.websheet.model.style.NumberElement;
import com.adventnet.zoho.websheet.model.util.EngineConstants;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ZSMillisecondComponent implements PatternComponent{

    private final SimpleDateFormat format;
    private final DecimalFormat decimalFormat;

    private final Locale locale;
    private final int count;

    public static final Logger LOGGER = Logger.getLogger(ZSMillisecondComponent.class.getName());

    public ZSMillisecondComponent(int zeroCount, Locale locale) {
        this.locale = locale;
        DecimalFormatSymbols dfs = new DecimalFormatSymbols(locale);
        this.decimalFormat = new DecimalFormat("0." + new String(new char[zeroCount]).replace("\0", "0"), dfs);

        this.format = new SimpleDateFormat(".SSS", EngineConstants.DEFAULT_LOCALE);
        this.count = zeroCount;

    }

    @Override
    public String toPattern(DecimalFormatSymbols dfs, boolean prependLocaleString) {
        char decimalSeparator = dfs.getDecimalSeparator();
        char zero = dfs.getZeroDigit();

        return decimalSeparator + new String(new char[this.count]).replace("\0", String.valueOf(zero));
    }

    public boolean incrementSecondsNeeded(Object object) {
        if(this.count < 3) {
            String result = this.format.format(object);
            BigDecimal bd = new BigDecimal(result);
            bd = bd.setScale(this.count, RoundingMode.HALF_UP);
            return bd.stripTrailingZeros().scale() <= 0;
        }

        return false;
    }

    @Override
    public String format(Object object) {
        String result = this.format.format(object);

        try {
            BigDecimal bd = new BigDecimal(result);
            bd = bd.setScale(this.count, RoundingMode.HALF_UP);
            return this.decimalFormat.format(bd.doubleValue()).substring(1);
        }
        catch(Exception e) {
            LOGGER.log(Level.WARNING, "[ZSPattern][Millisecond] Error while formatting millisecond :", e);
        }

        return result;
    }

    @Override
    public void createNumberElements(List<NumberElement> numberElementList) {
        for(int i=numberElementList.size()-1; i>=0; i--) {
            NumberElement element = numberElementList.get(i);
            if("seconds".equals(element.getTagName())) {
                element.setDecimalPlaces(this.count);
                return;
            }
        }

    }

    @Override
    public PatternComponent clone() {
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj == null) {
            return false;
        }

        if(!(obj instanceof ZSMillisecondComponent)) {
            return false;
        }

        ZSMillisecondComponent other = (ZSMillisecondComponent) obj;

        if(this.count != other.count) {
            return false;
        }

        if(!this.locale.equals(other.locale)) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
