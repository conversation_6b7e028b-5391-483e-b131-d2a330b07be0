/* $Id$ */
package com.adventnet.zoho.websheet.model.style.datastyle;

import com.adventnet.zoho.websheet.model.style.NumberElement;

import java.text.DecimalFormatSymbols;
import java.util.List;

public class EscapeChar implements PatternComponent {
    private String character;

    EscapeChar(String character) {
        this.character = character;
    }

    public String getEscapedString() {
        return this.character;
    }

    public String toPattern(DecimalFormatSymbols inLocale, boolean prependLocale) {
        return "\\" + this.character;
    }

    @Override
    public void createNumberElements(List<NumberElement> numberElementList) {
        if(!numberElementList.isEmpty()) {
            NumberElement lastElement = numberElementList.get(numberElementList.size() - 1);
            if (lastElement.getTagName().equals("text")) {
                lastElement.setContent(lastElement.getContent() + character);
                return;
            }
        }
        NumberElement textElement = new NumberElement("text"); // No I18N
        textElement.setContent(character);
        numberElementList.add(textElement);
    }

    public String format(Object object) {
        return this.getEscapedString();
    }

    public boolean equals(Object o) {
        if(this ==o) {
            return true;
        }

        if(o == null || !(o instanceof EscapeChar)) {
            return false;
        }

        EscapeChar component = (EscapeChar) o;

        if(!this.getEscapedString().equals(component.getEscapedString())) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        return this.character.hashCode();
    }

    /* EscapeChar is immutable, so just returning the same object */
    public EscapeChar clone() {
        return this;
    }
}
