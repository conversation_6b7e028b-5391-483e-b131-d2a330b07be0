/* $Id$ */
/*
 * ZSPattern.java
 *
 * Created on September 18, 2018
 *
 * Author : <PERSON><PERSON>
 *
 */
package com.adventnet.zoho.websheet.model.style.datastyle;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.ext.BooleanFormat;
import com.adventnet.zoho.websheet.model.ext.ParsedDate;
import com.adventnet.zoho.websheet.model.ext.ZFractionFormat;
import com.adventnet.zoho.websheet.model.ext.ZSErrorFormat;
import com.adventnet.zoho.websheet.model.ext.functions.Rept;
import com.adventnet.zoho.websheet.model.ext.parser.ASTParseErrorNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants.PatternConditionOperator;
import com.adventnet.zoho.websheet.model.util.DateFormatUtil;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;
import com.singularsys.jep.Jep;
import com.singularsys.jep.parser.Node;

import static com.adventnet.zoho.websheet.model.Cell.Type.*;
import static com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants.*;
import static java.lang.Math.pow;

import java.text.*;
import java.time.Instant;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


public class ZSPattern implements Cloneable{

    /* Class variables */
    private List<PatternComponent> componentList;
    private Type type;
    private String color;
    boolean isAutoOrder = true;
    boolean isCustomPattern = false;
    boolean hasExplicitLocale = false;
    Boolean isAccountingPattern = null;
    private ConditionalPatterns conditionalPatterns= null;

    /* For Caching */
    private Boolean isDefault = null;
    private String prefix = null;
    private String suffix = null;
    private Boolean hasMilliSeconds = null;
    /* End */

    public static final Logger LOGGER = Logger.getLogger(ZSPattern.class.getName());

    private static DecimalFormatSymbols defaultDFS = new DecimalFormatSymbols(EngineConstants.DEFAULT_LOCALE);


    private String regionalCountryCode;
    private RegionalType regionalType;
    public enum RegionalType{
        NONE("none"), //No I18N
        SSN("SSN"), //No I18N
        ZIPCODE4("ZipCode4"), //No I18N
        PHONENUMBER("PhoneNumber"), //No I18N
        ZIPCODE("ZipCode"); //No I18N

        private final String regionalTypeString;
        private RegionalType(String regionalTypeString){
            this.regionalTypeString = regionalTypeString;
        }

        public String getString(){
            return regionalTypeString;
        }
    }

    public ZSPattern(String patternString, SpreadsheetSettings parseSettings, boolean isAccountingPattern, boolean isAutoOrder, SpreadsheetSettings spreadsheetSettings) {
        componentList = new ArrayList<>();
        this.isAccountingPattern = isAccountingPattern;
        this.isAutoOrder = isAutoOrder;
        initPattern(patternString, parseSettings, spreadsheetSettings);
    }

    /* Will be used from initPattern of parent pattern or when creating default pattern like EmptyPattern */
    public ZSPattern(List<PatternComponent> componentList, Type type, String textColor, boolean isAccountingPattern, boolean hasExplicitLocale, boolean isAutoOrder) {
        this.componentList = componentList;
        this.type = type;
        this.color = textColor;
        this.isAccountingPattern = isAccountingPattern;
        this.hasExplicitLocale = hasExplicitLocale;
        this.isAutoOrder = isAutoOrder;
    }

    /* For new formatcells request */
    public ZSPattern(String patternString, SpreadsheetSettings parseSettings, boolean isCustomPattern, SpreadsheetSettings spreadsheetSettings) {
        componentList = new ArrayList<>();
        initPattern(patternString, parseSettings, spreadsheetSettings);
        if(this.type != ERROR) {

            isAutoOrder = !isCustomPattern;
            if(!isCustomPattern) {
                getRegionalType();  /* Check and set the regional type*/
            }
        }
    }

    private void initPattern(String patternString, SpreadsheetSettings parseSettings, SpreadsheetSettings spreadsheetSettings){
        String textColor = null;
        Locale currencyLocale = null;
        Locale formatLocale = spreadsheetSettings.getLocale();
        boolean isDuration = false;
        int numOfConditions = 0;
        int subPatternCount = 1;
        PatternConditionOperator operator = null;
        Expression conditionExpression = null;


        Type subType = null;
        List<PatternComponent> subPatternComponentList = new ArrayList<>();
        Map<Integer,List<PatternComponent>> tempMap = new TreeMap<>((o1,o2) -> {return o2.compareTo(o1);});
        List<ConditionalPattern> conditionList = new ArrayList<>();
        boolean hasExplicitCondition = false;
        boolean hasExplicitLocale = false;

        DecimalFormatSymbols parseDFS = new DecimalFormatSymbols(parseSettings.getLocale());
        parseDFS.setGroupingSeparator(parseSettings.getThousandSeparator());
        parseDFS.setDecimalSeparator(parseSettings.getDecimalSeparator());

        final char zero = parseDFS.getZeroDigit();
        char decimalSeperator = parseDFS.getDecimalSeparator();
        char groupingSeperator = parseDFS.getGroupingSeparator();

        boolean hourCharSeen = false;
        boolean numberFormatStarted = false;
        boolean decimalFormatStarted = false;
        boolean hasDateChars = false, hasTimeChars = false;
        int listLastIndex;
        StringBuilder decimalFormatStr = new StringBuilder();


        try {
            for (int i = 0; i < patternString.length(); i++) {
                char character = patternString.charAt(i);
                switch (character) {
                    case DOUBLE_QUOTE:
                    case SINGLE_QUOTE:
                        int indexOfNextQuote = patternString.indexOf(character, i + 1);
                        if (indexOfNextQuote == -1) {
                            this.type = Type.ERROR;
                            return;
                        }
                        String quottedString = patternString.substring(i + 1, indexOfNextQuote);
                        i = indexOfNextQuote;
                        QuottedText quottedText = new QuottedText(quottedString, true, character == SINGLE_QUOTE);
                        if (numberFormatStarted) {
                            int index = decimalFormatStr.length();
                            List<PatternComponent> componentList = tempMap.get(index);
                            if (componentList == null) {
                                componentList = new ArrayList<>();
                                tempMap.put(index, componentList);
                            }
                            componentList.add(quottedText);
                        } else {
                            subPatternComponentList.add(quottedText);
                        }

                        break;
                    case ESCAPE_CHAR:
                        i++;  /* Have to get the char next to the Escape char. */
                        EscapeChar escapeChar = new EscapeChar(String.valueOf(patternString.charAt(i)));
                        if (numberFormatStarted) {
                            int index = decimalFormatStr.length();
                            List<PatternComponent> componentList = tempMap.get(index);
                            if (componentList == null) {
                                componentList = new ArrayList<>();
                                tempMap.put(index, componentList);
                            }
                            componentList.add(escapeChar);
                        } else {
                            subPatternComponentList.add(escapeChar);
                        }

                        break;
                    case REPEAT_CHAR:
                        i++;    /* Have to get the char next to the Repeat char. */
                        RepeatChar repeatChar = new RepeatChar(String.valueOf(patternString.charAt(i)));
                        if (numberFormatStarted) {
                            int index = decimalFormatStr.length();
                            List<PatternComponent> componentList = tempMap.get(index);
                            if (componentList == null) {
                                componentList = new ArrayList<>();
                                tempMap.put(index, componentList);
                            }
                            componentList.add(repeatChar);
                        } else {
                            subPatternComponentList.add(repeatChar);
                        }

                        break;
                    case SPACE_CHAR:
                        i++;
                        SpaceChar spaceChar = new SpaceChar(String.valueOf(patternString.charAt(i)));
                        if (numberFormatStarted) {
                            int index = decimalFormatStr.length();
                            List<PatternComponent> componentList = tempMap.get(index);
                            if (componentList == null) {
                                componentList = new ArrayList<>();
                                tempMap.put(index, componentList);
                            }
                            componentList.add(spaceChar);
                        } else {
                            subPatternComponentList.add(spaceChar);
                        }

                        break;
                    case PERCENT:
                        PercentChar percentChar = PercentChar.getInstance();
                        if (numberFormatStarted) {
                            int index = decimalFormatStr.length();
                            List<PatternComponent> componentList = tempMap.get(index);
                            if (componentList == null) {
                                componentList = new ArrayList<>();
                                tempMap.put(index, componentList);
                            }
                            componentList.add(percentChar);
                        } else {
                            subPatternComponentList.add(percentChar);
                        }
                        if (subType != null) {
                            this.type = Type.ERROR;
                            return;
                        }
                        subType = Type.PERCENTAGE;
                        break;

                    case '[':
                        /* Condition, Locale, Currency Locale, Color, Duration(HMS)  */
                        int closingSBIndex = patternString.indexOf(']', i);
                        if (closingSBIndex == -1) {
                            this.type = Type.ERROR;
                            return;
                        }
                        String insideBracket = patternString.substring(i + 1, closingSBIndex);
                        for (int j = 0; j < insideBracket.length(); j++) {
                            if (insideBracket.charAt(j) == '[') {
                                this.type = Type.ERROR;
                                return;
                            }
                        }
                        boolean isValidDuration = insideBracket.matches("(?i)([HMS])\\1*"); // No I18N
                        isDuration = isDuration || isValidDuration;
                        if (isValidDuration) {
                            int index;
                            if (numberFormatStarted) {
                                this.type = Type.ERROR;
                                return;
                            } else {
                                subPatternComponentList.add(new ZSDurationFormat(validateDateFormat("[" + insideBracket + "]",false)));
                                hasTimeChars = true;
                                subType = TIME;
                            }
                        }
                        if (!isValidDuration) {
                            if (insideBracket.startsWith("<") || insideBracket.startsWith(">") || insideBracket.startsWith("=")) {
                                numOfConditions++;
                                if(numOfConditions > 2) {
                                    this.type = Type.ERROR;
                                    return;
                                }
                                operator = getOperator(insideBracket);
                                if(operator == null) {
                                    this.type = Type.ERROR;
                                    return;
                                }
                                Value val = Value.getInstance(insideBracket.substring(conditionalOperatorLength(operator)),parseSettings);
                                if(!val.getType().isNumberType()) {
                                    this.type = Type.ERROR;
                                    return;
                                }
                                hasExplicitCondition = true;
                                conditionExpression = ConditionalPatterns.getConditionalExpressionFromConditionString(insideBracket);
                                if(conditionExpression.getNode() instanceof ASTParseErrorNode) {
                                    this.type = Type.ERROR;
                                    return;
                                }

                            } else if (insideBracket.startsWith("$")) {
                                String localeStrings[] = insideBracket.substring(1).split("-");
                                if (localeStrings.length != 2) {
                                    this.type = Type.ERROR;
                                    return;
                                }
                                currencyLocale = LocaleUtil.getLocale(localeStrings[0], localeStrings[1]);
                                if(currencyLocale == null) {
                                    this.type = Type.ERROR;
                                    return;
                                }
                                if (decimalFormatStarted) {
                                    int index = decimalFormatStr.length();
                                    List<PatternComponent> list = tempMap.get(index);
                                    if (list == null) {
                                        list = new ArrayList<>();
                                        tempMap.put(index, list);
                                    }
                                    list.add(new CurrencyChar(currencyLocale));
                                } else {
                                    subPatternComponentList.add(new CurrencyChar(currencyLocale));
                                }
                                subType = Type.CURRENCY;

                            } else if (insideBracket.startsWith("#")) {
                                textColor = insideBracket;
                            } else if(COLOR_NAME_HASHVALUE_MAP.get(insideBracket.toUpperCase()) != null) {
                                textColor = COLOR_NAME_HASHVALUE_MAP.get(insideBracket.toUpperCase());
                            }else{
                                String localeStrings[] = insideBracket.split("-");
                                if (localeStrings.length != 2) {
                                    this.type = Type.ERROR;
                                    return;
                                }
                                formatLocale = LocaleUtil.getLocale(localeStrings[0], localeStrings[1]);
                                hasExplicitLocale = true;
                            }

                        }
                        i = closingSBIndex;

                        break;

                    case ';':
                        subType = initSubPattern(textColor, subPatternComponentList, subType, parseDFS, formatLocale, decimalFormatStr.toString(), tempMap, isDuration, hasExplicitLocale, spreadsheetSettings);

                        ZSPattern conditionalPattern = new ZSPattern(subPatternComponentList,subType,textColor,false,hasExplicitLocale, true);
                        conditionList.add(new ConditionalPattern(conditionExpression,conditionalPattern,hasExplicitCondition));

                        /* Resetting variables for the next subpattern */
                        subPatternCount++;
                        textColor = null;
                        subType = null;
                        subPatternComponentList = new ArrayList<>();
                        tempMap = new HashMap<>();
                        decimalFormatStr = new StringBuilder();
                        numberFormatStarted = false;
                        conditionExpression = null;
                        hasExplicitCondition = false;
                        decimalFormatStarted = false;
                        hasExplicitLocale = false;

                        isDuration = false;
                        break;

                    case PLACEHOLDER_CHAR:
                        subPatternComponentList.add(new ZSMessageFormat());
                        if (subType != null && subType != Type.STRING) {
                            this.type = Type.ERROR;
                            return;
                        }
                        subType = Type.STRING;
                        break;
                    case '{':
                        if (patternString.charAt(i + 1) == '0' && patternString.charAt(i + 2) == '}') {
                            subPatternComponentList.add(new ZSMessageFormat());
                            if (subType != null && subType != Type.STRING) {
                                this.type = Type.ERROR;
                                return;
                            }
                            subType = Type.STRING;
                            i = i + 2;
                            break;
                        }
                    case 'G':
                    case 'g':
                        if(patternString.startsWith("GENERAL",i) || patternString.startsWith("general",i) || patternString.startsWith("General",i)) {
                            if(subType != null) {
                                this.type = ERROR;
                                return;
                            }
                            if(i+7 < patternString.length() && patternString.charAt(i+7) != ';') {
                                this.type = ERROR;
                                return;
                            }

                            subPatternComponentList.add(new ZSGeneralFormat());
                            subType = FLOAT;
                            i += 6;
                            break;

                        }
                    case 'B':
                        if(patternString.startsWith("BOOLEAN",i)) {
                            if(subType != null) {
                                this.type = Type.ERROR;
                                return;
                            }
                            subType = Type.BOOLEAN;
                            subPatternComponentList.add(new BooleanFormat());
                            i += 6;
                            break;
                        }
                    default:

                        if(character == decimalSeperator && (subType == DATE || subType == TIME || subType == DATETIME)) {
                            int count = 0;
                            while(++i < patternString.length() && patternString.charAt(i) == zero) {
                                count++;
                            }
                            i--;
                            if(count > 0) {
                                if(count > 3) {
                                    this.type = ERROR;
                                    return;
                                }
                                subPatternComponentList.add(new ZSMillisecondComponent(count, formatLocale));
                                continue;
                            }
                        }
                        if (character == '#' || character == '?' || character == zero || character == decimalSeperator || (character == groupingSeperator && !hasDateChars && !hasTimeChars) || character == '/' || (character == 'E' && decimalFormatStarted)) {
                            /* Decimal Format */
                            decimalFormatStarted = true;
                            if (subType == Type.DATE || subType == Type.TIME || subType == Type.DATETIME) {
                                this.type = Type.ERROR;
                                return;
                            }
                            numberFormatStarted = true;
                            int index = i;
                            while (character == '#' || character == zero || character == '?' || character == groupingSeperator || character == decimalSeperator || character == '/' || (character == 'E' || character == ' ')) {
                                decimalFormatStr.append(character);
                                index++;

                                if (character == 'E') {
                                    /* If next character is + or - it should be ignored. */
                                    if (index < patternString.length()) {
                                        char nextCharacter = patternString.charAt(index);
                                        if (nextCharacter == '+' || nextCharacter == '-') {
                                            index++;
                                        }
                                    }
                                    if (subType != null) {
                                        this.type = Type.ERROR;
                                        return;
                                    }
                                    subType = Type.SCIENTIFIC;
                                }
                                if (index >= patternString.length()) {
                                    break;
                                }
                                character = patternString.charAt(index);
                            }
                            i = index - 1;

                            /* Last char is a space. Space at the end won't be retained in decimal format. So it has to be added to the Component Map*/
                            int decimalFormatStrLen = decimalFormatStr.length() - 1;
                            if (decimalFormatStr.charAt(decimalFormatStrLen) == ' ') {
                                decimalFormatStr.deleteCharAt(decimalFormatStrLen);
                                List<PatternComponent> componentList = tempMap.get(decimalFormatStr.length());
                                if (componentList == null) {
                                    componentList = new ArrayList<>();
                                    tempMap.put(decimalFormatStr.length(), componentList);
                                }
                                componentList.add(new QuottedText(" ", false, false));
                            }

                            break;
                        }

                        if ((stringContains(DATE_CHARS, character)) || (stringContains(TIME_CHARS, character))) {

                            if (((subType != Type.DATE && subType != TIME && subType != Type.DATETIME) && subType != null) || decimalFormatStarted) {
                                this.type = Type.ERROR;
                                return;
                            }
                            int index = i;
                            StringBuilder datePatternString = new StringBuilder();
                            while ((stringContains(DATE_CHARS, character)) || (stringContains(TIME_CHARS, character)) || character == ' ' || character == '/' || character == '.' || character == ':' || character == '-' ||
                                    character == ',' || character == decimalSeperator || character == groupingSeperator || Character.toLowerCase(character) == 'a' || Character.toLowerCase(character)== 'p' || character == zero) {
                                datePatternString.append(character);
                                index++;
                                if (index >= patternString.length()) {
                                    break;
                                }
                                character = patternString.charAt(index);
                            }

                            String validatedString = validateDateFormat(datePatternString.toString(),isDuration);
                            for(char validatedChar : validatedString.toCharArray()) {
                                hasDateChars = hasDateChars || stringContains(DATE_CHARS,validatedChar);
                                hasTimeChars = hasTimeChars || stringContains(TIME_CHARS,validatedChar);
                            }

                            subType = !hasTimeChars ? Type.DATE : !hasDateChars ? TIME : Type.DATETIME;

                            if(subType == TIME && isDuration)
                            {
                                Pattern pattern = Pattern.compile("(?<=\\.)0{1,3}");
                                Matcher matcher = pattern.matcher(validatedString);
                                if(matcher.find())
                                {
                                    if(matcher.group().length() > 3)
                                    {
                                        this.type = ERROR;
                                        return;
                                    }
                                    String[] durationString = validateDateFormat(validatedString, true).split("." + matcher.group());
                                    subPatternComponentList.add(new ZSDurationFormat(durationString[0]));
                                    subPatternComponentList.add(new ZSMillisecondComponent(matcher.group().length(), formatLocale));
                                }
                                else
                                {
                                    subPatternComponentList.add(new ZSDurationFormat(validatedString));
                                }
                            }
                            else {
                                List<PatternComponent> list = handleDateTimeComponent(validatedString, formatLocale, decimalSeperator, zero);
                                if (list == null) {
                                    this.type = ERROR;
                                    return;
                                } else {
                                    subPatternComponentList.addAll(list);
                                }
                            }

                            i = index - 1;
                            break;
                        } else {
                            int index = i;
                            while (!stringContains(DATE_CHARS, character) && !stringContains(TIME_CHARS, character) && !stringContains(SPECIAL_CHARS, character) &&
                                    character != '#' && character != '?' && character != zero && character != decimalSeperator && ((character == groupingSeperator && (hasDateChars || hasTimeChars)) || character != groupingSeperator) && character != '/' && character!= ';') {
                                index++;
                                if (index >= patternString.length()) {
                                    break;
                                }
                                character = patternString.charAt(index);
                            }
                            PatternComponent text = new QuottedText(patternString.substring(i, index), false, false);
                            if (numberFormatStarted) {
                                List<PatternComponent> componentList = tempMap.get(decimalFormatStr.length());
                                if (componentList == null) {
                                    componentList = new ArrayList<>();
                                    tempMap.put(decimalFormatStr.length(), componentList);
                                }
                                componentList.add(text);
                            } else {
                                subPatternComponentList.add(text);
                            }
                            i = index - 1;
                        }
                }
            }
        }
        catch(Exception e) {
            this.type = Type.ERROR;
            return;
        }

        subType = initSubPattern(textColor,subPatternComponentList,subType,parseDFS,formatLocale,decimalFormatStr.toString(),tempMap,isDuration, hasExplicitLocale, spreadsheetSettings);

        if(subType == Type.ERROR) {
            this.type = Type.ERROR;
            return;
        }

        boolean defaultParent = false;

        if(hasExplicitCondition || subPatternCount == 4) {
            ZSPattern conditionalPattern = new ZSPattern(subPatternComponentList,subType,textColor,false,hasExplicitLocale, true);
            conditionList.add(new ConditionalPattern(conditionExpression,conditionalPattern,hasExplicitCondition));
            ZSPattern parentPattern;

            if(subPatternCount == 4) {
                ConditionalPattern thirdPattern = conditionList.get(2);

                /* All the three subpatterns have explicit condition, so a default pattern has to be the parent pattern */
                if(thirdPattern.hasExplicitCondition()) {
                    parentPattern = getDefaultNumberPattern(formatLocale, spreadsheetSettings, hasExplicitLocale);
                    defaultParent = true;
                }
                else {
                    parentPattern = thirdPattern.getPattern();
                }
            }
            else {
                parentPattern = getDefaultNumberPattern(formatLocale, spreadsheetSettings, hasExplicitLocale);
                defaultParent = true;
            }
            this.componentList = parentPattern.componentList;
            this.color = parentPattern.color;
            this.type = parentPattern.type;
        }
        else {
            this.componentList = subPatternComponentList;
            this.color = textColor;
            this.type = subType;
            this.hasExplicitLocale = hasExplicitLocale;
        }

        if(!conditionList.isEmpty()) {
            if(subPatternCount > 4) {
                this.type = Type.ERROR;
                return;
            }
            ConditionalPatterns temp = new ConditionalPatterns(conditionList,conditionList.size()+(hasExplicitCondition||subPatternCount==4?0:1));
            if(conditionList.size() >= 3 && !conditionList.get(2).hasExplicitCondition()) { /* The third ConditionPattern has to become the parent pattern */
                temp.conditionalPatternList.remove(2);
            }
            conditionalPatterns = temp;
        }
    }

    private List<PatternComponent> handleDateTimeComponent(String format, Locale locale, char decimalSeparator, char zero) {
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("[" + decimalSeparator + "]"+ zero+"+");
        Matcher matcher = pattern.matcher(format);
        List<PatternComponent> list = new ArrayList<>();
        if(matcher.find()) {
            String match = matcher.group();
            String[] split = format.split(match);
            if(match.length()-1 > 3) {
                return null;
            }
            if(split.length == 1) {
                list.add(new ZSSimpleDateFormat(split[0], locale));
                list.add(new ZSMillisecondComponent(match.length()-1, locale));
            }
            else if(split.length == 2) {
                list.add(new ZSSimpleDateFormat(split[0], locale));
                list.add(new ZSMillisecondComponent(match.length()-1, locale));
                list.add(new ZSSimpleDateFormat(split[1], locale));
            }
            else {
                return null;
            }
        }
        else {
            pattern = java.util.regex.Pattern.compile("[" + zero + "]+");
            matcher = pattern.matcher(format);
            if(matcher.find()) {
                return null;
            }
            list.add(new ZSSimpleDateFormat(format, locale));
        }

        return list;
    }

    private Type initSubPattern(String textColor, List<PatternComponent> subPatternComponentList, Type subType, DecimalFormatSymbols parseDFS,Locale formatLocale, String formatString, Map<Integer,List<PatternComponent>> tempEmbeddedComponentMap, boolean isDuration, boolean hasExplicitLocale, SpreadsheetSettings spreadsheetSettings) {
        try {
            if (subType == null) {
                subType = findPatternType(subPatternComponentList,formatString, parseDFS);

                if (subType == Type.ERROR) {
                    return subType;
                }
            }

            if (subType.equals(Type.FRACTION)) {

                ZFractionFormat zFractionFormat = new ZFractionFormat(formatString,formatLocale);
                subPatternComponentList.add(zFractionFormat);

            } else if(!subType.equals(Type.DATE) && !subType.equals(Type.DATETIME) && !subType.equals(TIME) && !subType.equals(Type.STRING)){
                List<PatternComponent> suffix = new ArrayList<>();
                ZSDecimalFormat zsDecimalFormat = processDecimalFormat(subPatternComponentList, subType, parseDFS,formatLocale, formatString ,tempEmbeddedComponentMap,suffix, spreadsheetSettings, hasExplicitLocale);
                if(zsDecimalFormat != null) {
                    subPatternComponentList.add(zsDecimalFormat);

                }
                if(!suffix.isEmpty()) {
                    subPatternComponentList.addAll(suffix);
                }
            }
        }
        catch(Exception e) {
            e.printStackTrace();
            this.type = Type.ERROR;
            return Type.ERROR;
        }
        return subType;
    }

    private ZSDecimalFormat processDecimalFormat(List<PatternComponent> subPatternComponentList, Type subType, DecimalFormatSymbols parseDFS, Locale formatLocale, String decimalFormatString, Map<Integer,List<PatternComponent>> tempComponentMap, List<PatternComponent> suffix, SpreadsheetSettings spreadsheetSettings, boolean hasExplicitLocale) throws Exception {
        char decimalSeparator = parseDFS.getDecimalSeparator();
        char groupingSeparator = parseDFS.getGroupingSeparator();

        /*
         * While iterating this TreeMap, negative indices will come first, followed by positive.
         * Negative indices will be in ascending order.
         * Positive indices will be in descending order.
         */
        Map<Integer,List<PatternComponent>> embeddedComponentMap = new TreeMap<>((o1, o2) -> {
            if(o1 >= 0 && o2 >= 0) {
                if(o1 < o2) {
                    return 1;
                }
                else if(o1 == o2){
                    return 0;
                }
                else {
                    return -1;
                }
            }
            else if(o1 < 0 && o2 < 0) {
                if(o1 < o2) {
                    return -1;
                }
                else if(o1 == o2) {
                    return 0;
                }
                else {
                    return 1;
                }
            }
            else {
                return o1.compareTo(o2);
            }
        });

        List<Integer> commaIndexArray = new ArrayList<>();
        List<Integer> spaceIndexArray = new ArrayList<>();
        StringBuilder integralString = new StringBuilder();
        DecimalFormat decimalFormat = null;
        String newDecimalFormatString = decimalFormatString;
        double displayFactor = 1;

        if(!decimalFormatString.isEmpty()) {
            int integralEndIndex = processIntegralPart(decimalFormatString, parseDFS, tempComponentMap, embeddedComponentMap, commaIndexArray, integralString,subPatternComponentList,spaceIndexArray);

            for (int i = integralEndIndex + 1; i < decimalFormatString.length(); i++) {
                char character = decimalFormatString.charAt(i);
                if (character == decimalSeparator) {
                    break;
                }
                if (character == groupingSeparator) {
                    displayFactor = displayFactor * 1000;
                }
            }

            boolean hasDecimalPart = true;
            integralEndIndex = integralEndIndex == -1 ? 0 : integralEndIndex;
            while (decimalFormatString.charAt(integralEndIndex) != decimalSeparator) {
                integralEndIndex++;
                if (integralEndIndex >= decimalFormatString.length()) {
                    hasDecimalPart = false;
                    break;
                }
            }
            if (hasDecimalPart) {
                StringBuilder decimalPart = new StringBuilder();
                int dfEndIndex = processDecimalPart(integralEndIndex, decimalFormatString, parseDFS, tempComponentMap, embeddedComponentMap,decimalPart, spaceIndexArray, suffix);
                newDecimalFormatString = integralString.toString() + decimalPart;

                /* Grouping Separator after decimal end index will modify display factor */
                StringBuilder suffixStr = new StringBuilder();
                for (int i = dfEndIndex; i < decimalFormatString.length(); i++) {
                    if (decimalFormatString.charAt(i) == groupingSeparator) {
                        displayFactor = displayFactor * 1000;
                    }
                    else {
                        /* When ? is after a # in decimal part it needs to be treated as a Text. Eg: #.#?
                         * Because we replace '?' with '0'. 0 after # will make DecimalFormat throw Exception.  */
                        suffixStr.append(decimalFormatString.charAt(i));
                    }
                }
                if(suffixStr.length() > 0) {
                    suffix.add(new QuottedText(suffixStr.toString(), false, false));
                }
            } else {
                /* If there is no decimal part, the embeddedcomponent at index 0 from right should be removed from embeddedComponent map and
                 * added to ComponentList. Eg: ##")"
                 * If there is decimal part, this will be handled by processDecimalPart()*/
                if(embeddedComponentMap.get(0) != null) {
                    suffix.addAll(embeddedComponentMap.get(0));
                    embeddedComponentMap.remove(0);
                }
                else {
                    Iterator<Map.Entry<Integer, List<PatternComponent>>> iterator = tempComponentMap.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<Integer, List<PatternComponent>> entry = iterator.next();

                        suffix.addAll(entry.getValue());
                        iterator.remove();
                    }
                }

                newDecimalFormatString = integralString.toString();
            }



            if(!newDecimalFormatString.isEmpty())
            {
                DecimalFormatSymbols formatDFS = new DecimalFormatSymbols(formatLocale);
                char zero = formatDFS.getZeroDigit();
                DecimalFormatSymbols defaultDFS = new DecimalFormatSymbols(EngineConstants.DEFAULT_LOCALE);
                int tempDecimalseparatorIndex = newDecimalFormatString.indexOf(decimalSeparator);
                newDecimalFormatString = newDecimalFormatString.replace(groupingSeparator, defaultDFS.getGroupingSeparator());
                if(tempDecimalseparatorIndex != -1)
                {
                    newDecimalFormatString = newDecimalFormatString.substring(0, tempDecimalseparatorIndex)+defaultDFS.getDecimalSeparator()+newDecimalFormatString.substring(tempDecimalseparatorIndex + 1);
                }
                if(zero != '0')
                {
                    newDecimalFormatString = newDecimalFormatString.replace(zero, '0');
                }
                if(!hasExplicitLocale) {
                    formatDFS.setDecimalSeparator(spreadsheetSettings.getDecimalSeparator());
                    formatDFS.setGroupingSeparator(spreadsheetSettings.getThousandSeparator());
                }

                decimalFormat = new DecimalFormat(newDecimalFormatString, formatDFS);

                if(!commaIndexArray.isEmpty()) {
                    decimalFormat.setGroupingUsed(true);
                    decimalFormat.setGroupingSize(3);
                }
                decimalFormat.setGroupingUsed(commaIndexArray.isEmpty() ? false : true);
                if(subType == Type.PERCENTAGE) {
                    decimalFormat.setMultiplier(100);
                }
            }
            else
            {
                LOGGER.log(Level.INFO, "Not creating Decimal format for string >>> {0}", decimalFormatString);
            }

        }

        ZSDecimalFormat zsDecimalFormat = null;
        if(decimalFormat != null) {
            zsDecimalFormat = new ZSDecimalFormat(decimalFormat, embeddedComponentMap, displayFactor, spreadsheetSettings, spaceIndexArray, decimalFormat.isGroupingUsed());
        }
        return zsDecimalFormat;
    }

    private int processIntegralPart(String decimalFormatString, DecimalFormatSymbols dfs, Map<Integer,List<PatternComponent>> tempComponentMap, Map<Integer,List<PatternComponent>> embeddedComponentMap, List<Integer> commaIndexArray, StringBuilder integralString, List<PatternComponent> subPatternComponentList,List<Integer> spaceIndexArray) {
        char zero = dfs.getZeroDigit();
        char decimalSeparator = dfs.getDecimalSeparator();
        char groupingSeparator = dfs.getGroupingSeparator();
        int lastIndexOfQuestion = -1;
        int lastIndexOfZero = -1;
        int lastIndexOfHash = -1;
        int firstIndexOfQuestion = -1;
        int firstIndexOfZero = -1;
        int firstIndexOfHash = -1;
        int decimalSeparatorIndex = -1;

        outer : for(int i=0;i<decimalFormatString.length();i++) {
            char character = decimalFormatString.charAt(i);
            switch(character) {
                case '?':
                    lastIndexOfQuestion = i;
                    firstIndexOfQuestion = firstIndexOfQuestion == -1 ? i : firstIndexOfQuestion;
                    break;
                case '#':
                    lastIndexOfHash = i;
                    firstIndexOfHash = firstIndexOfHash == -1 ? i : firstIndexOfHash;
                    break;
                default:
                    if(character == zero) {
                        lastIndexOfZero = i;
                        firstIndexOfZero = firstIndexOfZero == -1 ? i : firstIndexOfZero;
                    }
                    else if(character == decimalSeparator) {
                        decimalSeparatorIndex = i;
                        break outer;
                    }
            }
        }

        int integralStartIndex = firstIndexOfHash < firstIndexOfZero ? firstIndexOfHash != -1 ? firstIndexOfHash : firstIndexOfZero : firstIndexOfZero == -1 ? firstIndexOfHash : firstIndexOfZero;
        integralStartIndex = firstIndexOfQuestion != -1 ? firstIndexOfQuestion < integralStartIndex ? firstIndexOfHash == -1 ? firstIndexOfQuestion : (firstIndexOfZero < firstIndexOfHash ? firstIndexOfQuestion : integralStartIndex) : (integralStartIndex==-1 ? firstIndexOfQuestion : integralStartIndex): integralStartIndex;
        int integralEndIndex = Math.max(lastIndexOfZero,Math.max(lastIndexOfHash,lastIndexOfQuestion));

        if(integralStartIndex == -1) {
            return -1;
        }
        if(integralStartIndex != 0) {
            String prefix = decimalFormatString.substring(0,integralStartIndex);
            subPatternComponentList.add(new QuottedText(prefix,false, false));
        }

        for(int i=integralStartIndex; i<=integralEndIndex;i++) {
            char character = decimalFormatString.charAt(i);
            if(character == groupingSeparator) {
                commaIndexArray.add(i);
            }
            else if(character == '?') {
                integralString.append(zero);
                spaceIndexArray.add(integralEndIndex-i);
            }
            else {
                integralString.append(character);
            }
        }

        tempComponentMap = commaIndexArray.size()>0? modifyMapIndices(commaIndexArray,tempComponentMap) : tempComponentMap;

        int integralLength = integralString.length();
        Iterator<Map.Entry<Integer,List<PatternComponent>>> iterator = tempComponentMap.entrySet().iterator();
        while(iterator.hasNext()) {
            Map.Entry<Integer,List<PatternComponent>> entry = iterator.next();
            int indexFromLeft = entry.getKey();
            if(indexFromLeft <= integralLength) {
                int indexFromRight = integralLength - indexFromLeft;
                embeddedComponentMap.put(indexFromRight, entry.getValue());
                iterator.remove();
            }
        }

        return integralEndIndex;

    }

    private int processDecimalPart(int integralLength, String decimalFormatString, DecimalFormatSymbols dfs, Map<Integer,List<PatternComponent>> tempComponentMap, Map<Integer,List<PatternComponent>> embeddedComponentMap, StringBuilder decimalPart,List<Integer> spaceIndexArray, List<PatternComponent> suffix) {
        char decimalSeparator = dfs.getDecimalSeparator();
        char zero = dfs.getZeroDigit();
        char groupingSeparator = dfs.getGroupingSeparator();
        int lastIndexOfQuestion = -1;
        int lastIndexOfHash = -1;
        int lastIndexOfZero = -1;

        int i=integralLength;


        int decimalPartStartIndex = i;
        int decimalFormatStringLength = decimalFormatString.length();
        for(;i<decimalFormatStringLength;i++) {
            char character = decimalFormatString.charAt(i);
            switch(character) {
                case '?':
                    lastIndexOfQuestion = i ;
                    break;
                case '#':
                    lastIndexOfHash = i ;
                    break;
                default:
                    if(character == zero) {
                        lastIndexOfZero = i ;
                    }
            }
        }

        int dfEndIndex =  lastIndexOfQuestion > lastIndexOfZero ? lastIndexOfZero >= lastIndexOfHash ? lastIndexOfQuestion : lastIndexOfZero  : lastIndexOfZero;
        dfEndIndex = Math.max(dfEndIndex,lastIndexOfHash);

        int decimalPartIndex = -1;
        for(int index=decimalPartStartIndex;index<=dfEndIndex;index++) {
            decimalPartIndex++;
            char character = decimalFormatString.charAt(index);
            if(character == '?') {
                decimalPart.append(zero);
                spaceIndexArray.add(-decimalPartIndex);
            }
            else {
                decimalPart.append(character);
            }
        }

        if(dfEndIndex != -1) {
            int decimalStringLength = dfEndIndex - decimalPartStartIndex;
            Iterator<Map.Entry<Integer, List<PatternComponent>>> iterator = tempComponentMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Integer, List<PatternComponent>> entry = iterator.next();
                if (entry.getKey() < integralLength) {
                    iterator.remove();
                    continue;
                }
                int indexFromLeft = entry.getKey() - decimalPartStartIndex;
                if (indexFromLeft <= decimalStringLength && indexFromLeft >= 0) {
                    embeddedComponentMap.put(-(indexFromLeft), entry.getValue());
                    iterator.remove();
                }
                else {
                    suffix.addAll(entry.getValue());
                    iterator.remove();
                }
            }
            return dfEndIndex+1;
        }

        return decimalPartStartIndex;

    }

    private Map<Integer,List<PatternComponent>> modifyMapIndices(List<Integer> indexArray, Map<Integer,List<PatternComponent>> integralMap) {
        List<Integer> keyList = new ArrayList<>(integralMap.keySet());
        Map<Integer,List<PatternComponent>> newIntegralMap = new HashMap<>();
        int indexArrayLength = indexArray.size();
        for(int i=0; i<keyList.size(); i++) {
            int key = keyList.get(i);
            List<PatternComponent> component = integralMap.get(key);
            for(int j=0; j<indexArrayLength; j++) {
                if(key > indexArray.get(j)) {
                    newIntegralMap.put(key-indexArrayLength-j,component);
                    break;
                }
                else if(j==0) {
                    newIntegralMap.put(key,component);
                }
            }
        }
        return newIntegralMap;
    }

    public static ZSPattern changeFormatLocale(ZSPattern inPattern, SpreadsheetSettings spreadsheetSettings) {
        if(inPattern == EMPTY_PATTERN) {
            return inPattern;
        }
        if(inPattern.hasExplicitLocale) {
            return inPattern.clone(); //TODO : is cloning required?
        }
        List<PatternComponent> newComponentList = new ArrayList<>();

        for(PatternComponent component : inPattern.getComponentList()) {
            PatternComponent clonedComponent = component.clone();
            if(clonedComponent instanceof ZSDecimalFormat) {
                DecimalFormatSymbols dfs = new DecimalFormatSymbols(spreadsheetSettings.getLocale());
                dfs.setDecimalSeparator(spreadsheetSettings.getDecimalSeparator());
                dfs.setGroupingSeparator(spreadsheetSettings.getThousandSeparator());
                ((ZSDecimalFormat) clonedComponent).getDecimalFormat().setDecimalFormatSymbols(dfs);
                ((ZSDecimalFormat) clonedComponent).setSpreadsheetSettings(spreadsheetSettings);
                newComponentList.add(clonedComponent);
            }
            else if(clonedComponent instanceof ZSSimpleDateFormat) {
                boolean isDurationFormat = clonedComponent instanceof ZSDurationFormat;
                String patternString = null;
                if(!isDurationFormat) {
                    if(inPattern.isAutoOrder) {
                        List<NumberElement> numberElementList = new ArrayList<>();
                        clonedComponent.createNumberElements(numberElementList);
                        DateUtil.DateFormatType dateFormatType = spreadsheetSettings.getDateInputFormat();
                        reorderNumberElementList(numberElementList,dateFormatType);
                        patternString = NumberStyle.generatePatternStringFromNumberElementList(numberElementList, true);
                    }
                    else {
                        patternString = clonedComponent.toPattern(null,false);
                    }

                    newComponentList.add(new ZSSimpleDateFormat(patternString, spreadsheetSettings.getLocale()));
                }
                else {
                    newComponentList.add(clonedComponent);
                }
            }
            else if(clonedComponent instanceof ZFractionFormat) {
                DecimalFormatSymbols dfs = new DecimalFormatSymbols(spreadsheetSettings.getCurrencyLocale());

                dfs.setDecimalSeparator(spreadsheetSettings.getDecimalSeparator());
                dfs.setGroupingSeparator(spreadsheetSettings.getThousandSeparator());
                ((DecimalFormat)((ZFractionFormat) clonedComponent).getWholeFormat()).setDecimalFormatSymbols(dfs);
                ((DecimalFormat)((ZFractionFormat) clonedComponent).getNumeratorFormat()).setDecimalFormatSymbols(dfs);
                ((DecimalFormat)((ZFractionFormat) clonedComponent).getDenominatorFormat()).setDecimalFormatSymbols(dfs);

                newComponentList.add(clonedComponent);
            }
            else {
                newComponentList.add(clonedComponent);
            }

        }

        ZSPattern newPattern = new ZSPattern(newComponentList,inPattern.getType(),inPattern.getColor(),inPattern.isAccountingFormat(),inPattern.isHasExplicitLocale(), inPattern.isAutoOrder);

        if(inPattern.isConditionalPattern()) {
            for(ConditionalPattern conditionalPattern : inPattern.getConditionalPatterns().getConditionalPatternList()) {
                ZSPattern pattern = conditionalPattern.getPattern();
                ZSPattern newChildPattern = changeFormatLocale(pattern, spreadsheetSettings);
                newPattern.addConditionalPattern(conditionalPattern.getConditionString(),newChildPattern,conditionalPattern.hasExplicitCondition());
            }
        }

        return newPattern;
    }

    private static void reorderNumberElementList(List<NumberElement> numberElements, DateUtil.DateFormatType dateFormatType){
        NumberElement dayNumElement=null;
        NumberElement monthNumElement=null;
        NumberElement yearNumElement=null;

        String dateFormatTypeString= dateFormatType.toString();

        int elementIndex[]= new int[3];
        int counter=0;
        String regex="";
        for(int i=0; i<numberElements.size(); i++){
            NumberElement numberElement= numberElements.get(i);
            switch(numberElement.getTagName()){
                case "day": //No I18N
                    if(dayNumElement == null)
                    {
                        dayNumElement=numberElement;
                        elementIndex[counter++]=i;
                        regex+="D"; //No I18N
                    }
                    break;
                case "month": //No I18N
                    if(monthNumElement == null)
                    {
                        monthNumElement=numberElement;
                        elementIndex[counter++]=i;
                        regex+="M"; //No I18N
                    }
                    break;
                case "year": //No I18N
                    if(yearNumElement == null)
                    {
                        yearNumElement=numberElement;
                        elementIndex[counter++]=i;
                        regex+="Y"; //No I18N
                    }
                    break;
            }
        }

        if(!regex.isEmpty()){
            regex="[^"+regex+"]";
            dateFormatTypeString= dateFormatTypeString.replaceAll(regex, "");

            int dayPosition=dateFormatTypeString.indexOf("D");
            if(dayPosition!=-1){
                numberElements.set(elementIndex[dayPosition], dayNumElement);
            }

            int monthPosition= dateFormatTypeString.indexOf("M");
            if(monthPosition!=-1){
                numberElements.set(elementIndex[monthPosition], monthNumElement);
            }

            int yearPosition= dateFormatTypeString.indexOf("Y");
            if(yearPosition!=-1){
                numberElements.set(elementIndex[yearPosition], yearNumElement);
            }
        }
    }


    /* returns toPatternString() for only the main pattern and will ignore the child patterns */
    public String getSubPatternString(boolean isFromUndo) {
        StringBuilder patternString = new StringBuilder();
        if(this.getColor() != null) {
            String color = this.getColor();
            if(!isFromUndo) {
                color = DataStyleConstants.COLOR_HASHVALUE_NAME_MAP.get(color);
            }
            patternString.append('[').append(color).append(']');

        }
        DecimalFormatSymbols defaultDFS = new DecimalFormatSymbols(EngineConstants.DEFAULT_LOCALE);
        for(PatternComponent component : this.componentList) {
            patternString.append(component.toPattern(defaultDFS,this.hasExplicitLocale));
        }

        return patternString.toString();
    }

    // TODO : Check if any Change is needed.
    private Type findPatternType(List<PatternComponent> subPatternComponentList,String formatString,DecimalFormatSymbols dfs) {
        boolean hasEmbeddedComponent = false;
        char zero = dfs.getZeroDigit();
        char decimalSeparator = dfs.getDecimalSeparator();
        char percentSymbol = dfs.getPercent();

        int prevKey = -1;

        if(formatString.contains("/")) {
            String fractionCharsRegex = "([\\s]*[#]*["+zero+"]*[?]*)"; // No I18N
            //String fractionFormatRegex = fractionCharsRegex+"?(\\s)+"+fractionCharsRegex+"(\\s)*[/](\\s)*"+fractionCharsRegex;
            String fractionFormatRegex = "((\\s)*"+fractionCharsRegex+"(\\s)+)?"+fractionCharsRegex+"(\\s)*[/](\\s)*"+fractionCharsRegex + "(\\s)*";//No I18N
            if((formatString.startsWith("/") || formatString.endsWith("/")) || !java.util.regex.Pattern.matches(fractionFormatRegex, formatString))
            {
                return Type.ERROR;
            }

            return Type.FRACTION;
        }



        return Type.FLOAT;
    }

    private boolean stringContains(String str, char c) {
        int strLen = str.length();
        for(int i=0; i<strLen; i++) {
            char character = str.charAt(i);
            if(character == c) {
                return true;
            }
        }
        return false;
    }

    private static String replaceTimeZone(String s, Workbook workbook) {
        boolean insideQuote = false;
        boolean timeZoneAppended = false;
        char quote = '\'';

        StringBuilder newStr = new StringBuilder();

        for(int i=0; i<s.length(); i++) {
            char c = s.charAt(i);

            if(c == '\'' || c == '"') {
                if(insideQuote) {
                    if(quote == c) {
                        insideQuote = false;
                    }
                }
                else {
                    insideQuote = true;
                    quote = c;
                }
            }

            if(insideQuote) {
                newStr.append(c);
            }
            else if(c != 'z' && c != 'Z') {
                newStr.append(c);
            }
            else if(!timeZoneAppended){
                int timeZoneLength = TimeZone.SHORT;
                if(i+1 < s.length() && (s.charAt(i+1) == 'z' || s.charAt(i+1) == 'Z')) {
                    timeZoneLength = TimeZone.LONG;
                }
                String timeZoneStr = workbook.getUserTimezone().getDisplayName(false, timeZoneLength, workbook.getFunctionLocale());
                newStr.append('\'').append(timeZoneStr).append('\'');
                timeZoneAppended = true;
            }
        }

        return newStr.toString();
    }

    private String validateDateFormat(String format,boolean isDuration) {
        format = format.replace("m","M");//No I18N
        format = format.replace("h","H");//No I18N
        format = format.replace("S","s");//No I18N
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(H[^M]*M+)|(M+[^M]*s)|(\\[M+\\])");
        Matcher matcher = pattern.matcher(format);
        StringBuffer sb = new StringBuffer();
        boolean result = matcher.find();
        while(result)
        {
            matcher.appendReplacement(sb,matcher.group().replace("M", "m"));//No I18N
            result = matcher.find();
        }
        matcher.appendTail(sb);
        format = sb.toString();
        if(isDuration) {
            format = format.replace("M","m"); // No I18N
        }
        format = format.replaceAll("H{2,}","HH");//No I18N
        format = format.replaceAll("s{2,}","ss");//No I18N
        format = format.replace("D","d");//No I18N
        format = format.replaceAll("(?i)[dan]{4,}","EEEE");//No I18N
        format = format.replaceAll("((?i)([da]{3}|[n]+))", "EEE");//No I18N
        format = format.replace("Y", "y");//No I18N
        format = format.replaceAll("y{3,}", "yyyy");//No I18N
        if(java.util.regex.Pattern.matches(".*((?i)[a]([m]?([/][p])[m]?)?).*",format)) // supports HH:mm a, HH:mm a/p, HH:mm am/pm, HH:mm a/pm, HH:mm am/p
        {
            format = format.replace("H","h");//No I18N
        }
        format = format.replaceAll("(?<=.*)((?i)[a][m]?)/((?i)[p][m]?)(?=.*)", "a");//No I18N
        format = format.replace("A","a");//No I18N
        format = format.replace("g","G");//No I18N
        format = format.replace("f","F");//No I18N

        return format;
    }

    /* Conditional Pattern methods */

    private int conditionalOperatorLength(PatternConditionOperator operator)
    {
        if(operator == PatternConditionOperator.LESSEREQUAL || operator == PatternConditionOperator.GREATEREQUAL)
        {
            return 2;
        }
        else if(operator == PatternConditionOperator.LESSER || operator == PatternConditionOperator.GREATER || operator == PatternConditionOperator.EQUAL)
        {
            return 1;
        }
        return 0;
    }

    private String getConditionValue(String str, PatternConditionOperator operator) {
        int operatorLength = conditionalOperatorLength(operator);
        return str.substring(operatorLength,str.length()-1);
    }

    private PatternConditionOperator getOperator(String str) {
        char firstChar = str.charAt(0);
        if(firstChar == '>' || firstChar == '<') {
            char secondChar = str.charAt(1);
            if(secondChar == '=') {
                if(firstChar == '>') {
                    return PatternConditionOperator.GREATEREQUAL;
                }
                else {
                    return PatternConditionOperator.LESSEREQUAL;
                }
            }
            else {
                if(firstChar == '>') {
                    return PatternConditionOperator.GREATER;
                }
                else {
                    return PatternConditionOperator.LESSER;
                }
            }
        }
        else if(firstChar == '=') {
            return PatternConditionOperator.EQUAL;
        }
        return null;
    }


    public void addConditionalPattern(String condition, ZSPattern pattern, boolean hasExplicitCondition) {
        Expression conditionExpr =  ConditionalPatterns.getConditionalExpressionFromConditionString(condition);
        if(this.conditionalPatterns == null) {
            this.conditionalPatterns = new ConditionalPatterns();
        }
        this.conditionalPatterns.addConditionalPatternToList(new ConditionalPattern(conditionExpr,pattern,hasExplicitCondition));
    }

    public void addConditionalPattern(Expression condition, ZSPattern pattern, boolean hasExplicitCondition) {
        if(this.conditionalPatterns == null) {
            this.conditionalPatterns = new ConditionalPatterns();
        }
        this.conditionalPatterns.addConditionalPatternToList(new ConditionalPattern(condition,pattern,hasExplicitCondition));
    }

    public void addConditionalPattern(ConditionalPattern conditionalPattern) {
        if(this.conditionalPatterns == null) {
            this.conditionalPatterns = new ConditionalPatterns();
        }
        this.conditionalPatterns.addConditionalPatternToList(conditionalPattern);
    }

    public void addTextConditionalPattern(ZSPattern pattern) {
        if(this.conditionalPatterns == null) {
            this.conditionalPatterns = new ConditionalPatterns();
        }
        this.conditionalPatterns.setTextPattern(pattern);
    }

    /* Getter Methods */


    public Locale getLocale() {
        Locale locale = null;
        for(PatternComponent component : this.componentList) {
            if(component instanceof ZSDecimalFormat) {
                locale = ((ZSDecimalFormat) component).getSpreadsheetSettings().getLocale();
            }
            if(component instanceof ZSSimpleDateFormat) {
                locale = ((ZSSimpleDateFormat) component).getFormatLocale();
            }
        }
        return locale;
    }

    public boolean isAutoOrder() {
        return this.isAutoOrder;
    }

    public boolean isAccountingFormat() {
        if(this.isAccountingPattern == null) {
            String patternString = this.toPatternString();
            Pattern accWithoutSymbol = Pattern.compile("^\\[>0\\]_\\(\\* [#0,.]*_\\);\\[<0\\]_\\(\\* \\([#0,.]*\\);_\\(\\* -[#]*\\?\\?_\\);_\\(@_\\)$");
            Pattern accounting = Pattern.compile("^\\[>0\\]_\\(\\[\\$[a-z]{2}-[A-Z]{2}\\]\\* [#0,\\.]*_\\);\\[<0\\]_\\(\\[\\$[a-z]{2}-[A-Z]{2}\\]\\* \\([#0,\\.]*\\);_\\(\\[\\$[a-z]{2}-[A-Z]{2}\\]\\* -[#]*\\?\\?_\\);_\\(@_\\)$");
            if(accounting.matcher(patternString).matches() || accWithoutSymbol.matcher(patternString).matches()) {
                this.isAccountingPattern = true;
            }
            else {
                this.isAccountingPattern = false;
            }
        }
        return this.isAccountingPattern;
    }

    public boolean isHasExplicitLocale() {
        return this.hasExplicitLocale;
    }

    public boolean isCustomPattern() {
        return this.isCustomPattern;
    }

    public String getColor() {
        return this.color;
    }

    public Type getType() {
        return this.type;
    }


    public boolean isConditionalPattern() {
        return this.conditionalPatterns!=null;
    }

    public List<PatternComponent> getComponentList() {
        return this.componentList;
    }

    public String getColorForValue(Workbook workbook, Value value) {
        ZSPattern conditionalPattern = getConditionalPattern(workbook,value);
        return conditionalPattern.getColor();
    }

    public Type getTypeForValue(Workbook workbook, Value value) {
        ZSPattern conditionalPattern = getConditionalPattern(workbook,value);
        return conditionalPattern.getType();
    }

    public ZSPattern getPatternForCondition(Expression inCondition) {
        if(this.conditionalPatterns == null) {
            return null;
        }
        for(ConditionalPattern conditionalPattern : this.conditionalPatterns.conditionalPatternList) {
            Expression condition = conditionalPattern.getCondition();
            if(condition.equals(inCondition)) {
                return conditionalPattern.getPattern();
            }
        }
        return null;
    }

    public static ZSPattern getPatternForNumberWithThousandSeparator(Locale locale, SpreadsheetSettings spreadsheetSettings){
        ZSDecimalFormat decimalFormat = DefaultPatternUtil.getDefaultNumberFormat(spreadsheetSettings).clone();
        decimalFormat.setGroupingUsed(true);
        return new ZSPattern(Arrays.asList(decimalFormat), Cell.Type.FLOAT,null,false,false, true);
    }

    public static ZSPattern getPatternforDate(ParsedDate.DateProperties dateProperties, ParsedDate.TimeProperties timeProperties , Type formatType, SpreadsheetSettings spreadsheetSettings)
    {
        ZSPattern pattern = null;
        String datePatternString = getPatternStringforDate(dateProperties, timeProperties, spreadsheetSettings);
        if(!datePatternString.isEmpty())
        {
            boolean isCustom = true;
            List dateFrmtList = DateFormatUtil.getDateFormats(spreadsheetSettings.getLocale());
            List timeFrmtList = DateFormatUtil.getTimeFormats(spreadsheetSettings.getLocale());
            List durationFrmtList = DateFormatUtil.getDurationFormats();

            if (formatType == TIME && durationFrmtList.contains(datePatternString))
            {
                isCustom = false;
            }
            else if ((formatType == Type.DATE && dateFrmtList.contains(datePatternString))
                    || (formatType == TIME && timeFrmtList.contains(datePatternString)))
            {
                isCustom = false;
            }
            else if (formatType == Type.DATETIME)
            {
                String timeFrmt = "";
                String dateFrmt = "";

                String[] dateTimeSplitStrings = {"z h", "ah", "a h", "'kl", "h", "H"}; // NO I18N
                for (String dateTimeSplitStr : dateTimeSplitStrings)
                {
                    if (datePatternString.contains(dateTimeSplitStr))
                    {
                        dateFrmt = datePatternString.substring(0, datePatternString.indexOf(dateTimeSplitStr) - 1);
                        timeFrmt = datePatternString.substring(datePatternString.indexOf(dateTimeSplitStr));
                        break;
                    }
                }

                dateFrmt = dateFrmt.trim();
                timeFrmt = timeFrmt.trim();
                if (dateFrmtList.contains(dateFrmt) && timeFrmtList.contains(timeFrmt))
                {
                    isCustom = false;
                }
            }

            if(isCustom)
            {
                try
                {
                    pattern = getCustomPattern(datePatternString, spreadsheetSettings.getLocale(), spreadsheetSettings);
                }
                catch(IllegalArgumentException iAE)
                {
                    LOGGER.log(Level.WARNING, "Illegal arguement exception while creating custom format for date inputs");
                }
            }
            else
            {
                pattern = new ZSPattern(datePatternString, spreadsheetSettings, false,true, spreadsheetSettings);
            }
        }

        return pattern;
    }

    private static String getPatternStringforDate(ParsedDate.DateProperties datePropsObj, ParsedDate.TimeProperties timePropsObj, SpreadsheetSettings spreadsheetSettings)
    {
        String datePatternString = "";

        if(datePropsObj != null)
        {
            datePatternString = getDatePatternString(datePropsObj);
            if(timePropsObj != null)
            {
                datePatternString += " " + getTimePatternString(timePropsObj, spreadsheetSettings);
            }
            return datePatternString;
        }
        if(timePropsObj != null)
        {
            datePatternString = getTimePatternString(timePropsObj, spreadsheetSettings);
        }
        return datePatternString;
    }

    private static String getTimePatternString(ParsedDate.TimeProperties timeProperties, SpreadsheetSettings spreadsheetSettings)
    {
        if(timeProperties == null)
        {
            return null;
        }
        int hourLen = timeProperties.getHourLen();
        int minLen = timeProperties.getMinLen();
        int secLen = timeProperties.getSecLen();
        boolean hasMilliSeconds = timeProperties.hasMilliSeconds();
        boolean hasAMPM = timeProperties.isHasAMPM();
        boolean hasTimeZone = timeProperties.isHasTimeZone();
        ParsedDate.TimeType timeType = timeProperties.getTimeType();

        StringBuilder timePatternString = new  StringBuilder();
        if(hourLen > 0 || hasMilliSeconds)
        {
            if(hourLen > 0) {
                switch (timeType) {
                    case DURATION:
                        timePatternString.append("[").append(Rept.rept("H", hourLen)).append("]"); // NO I18N
                        break;
                    case TWELVE_HR:
                        timePatternString.append(Rept.rept("h", hourLen)); // NO I18N
                        break;
                    case TWENTY_FOUR_HR:
                        timePatternString.append(Rept.rept("H", hourLen)); // NO I18N
                        break;
                }
            }

            if(minLen > 0) {
                if(hourLen > 0) {
                    timePatternString.append(":");
                }
                if(timeType == ParsedDate.TimeType.DURATION && hourLen <= 0) {
                    timePatternString.append("[").append(Rept.rept("m", minLen)).append("]");
                }
                else {
                    timePatternString.append(Rept.rept("m", minLen));  // NO I18N
                }
            }

            if(secLen > 0)
            {
                timePatternString.append(":").append(Rept.rept("s",secLen));   // NO I18N
            }

            if(hasMilliSeconds) {
                DecimalFormatSymbols dfs = new DecimalFormatSymbols(spreadsheetSettings.getLocale());
                timePatternString.append(spreadsheetSettings.getDecimalSeparator()).append(Rept.rept(String.valueOf(dfs.getZeroDigit()), 3));
            }

            if(hasAMPM)
            {
                timePatternString.append(" a"); // NO I18N
            }
            if(hasTimeZone)
            {
                timePatternString.append(" z"); // NO I18N
            }
        }

        return timePatternString.toString();
    }

    private static String getDatePatternString(ParsedDate.DateProperties dateProperties)
    {
        if(dateProperties == null)
        {
            return null;
        }
        DateUtil.DateFormatType dateFormatType = dateProperties.getDateFormatType();
        int weekOfDayLen = dateProperties.getWeekOfDayLen();
        int dateLen = dateProperties.getDateLen();
        int monthLen = dateProperties.getMonthLen();
        int yearLen = dateProperties.getYearLen();
        String firstDateSeparator = dateProperties.getFirstDateSeparator();
        String lastDateSeparator = dateProperties.getLastDateSeparator();
        boolean isWeekOfDayWithComma = dateProperties.isWeekOfDayWithComma();

        StringBuilder datePatternString = new  StringBuilder();
        Object[][] eDMYLenArray = null;

        switch(dateFormatType)
        {
            case DMY:
                eDMYLenArray =  new Object[][]{{"d",dateLen},{"m",monthLen},{"y",yearLen}}; // NO I18N
                break;
            case MDY:
                eDMYLenArray =  new Object[][]{{"m",monthLen},{"d",dateLen},{"y",yearLen}}; // NO I18N
                break;
            case YMD:
                eDMYLenArray =  new Object[][]{{"y",yearLen},{"m",monthLen},{"d",dateLen}}; // NO I18N
                break;
            case YDM:
                eDMYLenArray = new Object[][]{{"y",yearLen},{"d",dateLen},{"m",monthLen}}; // NO I18N
                break;
            default:
        }

        if(weekOfDayLen > 0)
        {
            datePatternString.append(Rept.rept("E",weekOfDayLen)).append(isWeekOfDayWithComma? ", ":" "); // NO I18N
        }

        int i = 0;
        int repeatLen;

        for(Object[] entityLen : eDMYLenArray)
        {
            repeatLen = (Integer)entityLen[1];
            if(repeatLen > 0)
            {
                String entity = (String)entityLen[0];
                datePatternString.append(Rept.rept(entity,repeatLen));
                if(i == 0 && firstDateSeparator != null)
                {
                    if(!firstDateSeparator.isEmpty())
                    {
                        datePatternString.append(firstDateSeparator);
                    }
                    else
                    {
                        datePatternString.append(" ");
                    }
                }
                else if(i == 1 && lastDateSeparator != null)
                {
                    if(!lastDateSeparator.isEmpty())
                    {
                        datePatternString.append(lastDateSeparator);
                    }
                    else
                    {
                        datePatternString.append(" ");
                    }
                }
                i++;
            }
        }
        return datePatternString.toString();
    }

    public String getPrefix() {
        if(this.type != Type.FLOAT) {
            return null;
        }

        ZSPattern pattern = this;

        if(pattern.isConditionalPattern()) {
            pattern = pattern.getConditionalPatterns().getConditionalPatternList().get(0).getPattern();
        }

        if(pattern.prefix == null) {
            List<PatternComponent> componentList = pattern.getComponentList();
            for(int i=0; i<componentList.size(); i++) {
                if(componentList.get(i) instanceof ZSDecimalFormat) {
                    if(i != 0 && componentList.get(i-1) instanceof QuottedText) {
                        pattern.prefix = ((QuottedText) componentList.get(i-1)).getString();
                    }

                    if(i != componentList.size()-1 && componentList.get(i+1) instanceof QuottedText) {
                        pattern.suffix = ((QuottedText) componentList.get(i+1)).getString();
                    }
                }
            }
        }

        return pattern.prefix;
    }

    public String getSuffix() {
        if(this.type != Type.FLOAT) {
            return null;
        }

        ZSPattern pattern = this;

        if(pattern.isConditionalPattern()) {
            pattern = pattern.getConditionalPatterns().getConditionalPatternList().get(0).getPattern();
        }

        getPrefix();

        return pattern.suffix;
    }


    public  boolean isDefaultPattern(Workbook workbook) {
        if(this.isDefault != null) {
            return this.isDefault;
        }
        Locale workbookLocale = workbook.getFunctionLocale();
        String patternString = this.toPatternString();
        if(regionalType != null && regionalType != RegionalType.NONE) {
            return this.isDefault = true;
        }
        if(this.type == TIME) {
            if(DateFormatUtil.getDurationFormats().contains(patternString))
            {
                return this.isDefault = true;
            }
            else
            {
                List timeFrmtList = DateFormatUtil.getTimeFormats(workbookLocale);
                return this.isDefault = (this.type == TIME && timeFrmtList.contains(patternString));
            }
        }
        else if(this.type == Type.DATE || this.type == TIME ) {
            List dateFrmtList = DateFormatUtil.getDateFormats(workbookLocale);
            return this.isDefault = (this.type == Type.DATE && dateFrmtList.contains(patternString));
        }
        else if(this.type == DATETIME) {
            String timeFrmt = "";
            String dateFrmt = "";
            List dateFrmtList = DateFormatUtil.getDateFormats(workbookLocale);
            List timeFrmtList = DateFormatUtil.getTimeFormats(workbookLocale);

            String[] dateTimeSplitStrings = {"z h", "ah", "a h", "'kl", "h", "H"}; // NO I18N
            for (String dateTimeSplitStr : dateTimeSplitStrings) {
                if (patternString.contains(dateTimeSplitStr)) {
                    int index = patternString.indexOf(dateTimeSplitStr);
                    dateFrmt = patternString.substring(0, index);
                    timeFrmt = patternString.substring(index);
                    break;
                }
            }

            dateFrmt = dateFrmt.trim();
            timeFrmt = timeFrmt.trim();
            if (dateFrmtList.contains(dateFrmt) && timeFrmtList.contains(timeFrmt)) {
                return this.isDefault = true;
            }
            return this.isDefault = false;
        }

        boolean defaultPattern = false;
        switch(this.type) {
            case CURRENCY:
                DecimalFormat df = (DecimalFormat) DecimalFormat.getCurrencyInstance(workbookLocale);
                df.setDecimalFormatSymbols(new DecimalFormatSymbols(EngineConstants.DEFAULT_LOCALE));
                String baseCurrencyRegex;
                if(df.toLocalizedPattern().indexOf(CURRENCY_UNICODE_CHAR) == 0) {
                    baseCurrencyRegex = "\\[\\$[a-z]{2}-[A-Z]{2}\\][ ]?[#0,\\.]*"; // No I18N
                }
                else {
                    baseCurrencyRegex = "[#0,\\.]*[ ]?\\[\\$[a-z]{2}-[A-Z]{2}\\]"; // No I18N
                }
                Pattern currency1 = Pattern.compile("^\\[>=0\\]" + baseCurrencyRegex +";-"+ baseCurrencyRegex +"$");
                Pattern currency2 = Pattern.compile("^\\[>=0\\]" + baseCurrencyRegex +";\\[RED\\]" + baseCurrencyRegex +"$");
                Pattern currency3 = Pattern.compile("^\\[>=0\\]" + baseCurrencyRegex +";\\[RED\\]-" + baseCurrencyRegex +"$");
                Pattern currency4 = Pattern.compile("^\\[>=0\\]" + baseCurrencyRegex +";\\(" + baseCurrencyRegex +"\\)$");
                Pattern currency5 = Pattern.compile("^\\[>=0\\]" + baseCurrencyRegex +";\\[RED\\]\\(" + baseCurrencyRegex +"\\)$");
                Pattern accounting = Pattern.compile("^\\[>0\\]_\\(\\[\\$[a-z]{2}-[A-Z]{2}\\]\\* [#0,\\.]*_\\);\\[<0\\]_\\(\\[\\$[a-z]{2}-[A-Z]{2}\\]\\* \\([#0,\\.]*\\);_\\(\\[\\$[a-z]{2}-[A-Z]{2}\\]\\* [\"-]{1,3}[#]*\\?\\?_\\)[;_\\(@_\\)]*$");

                if(currency1.matcher(patternString).matches() || currency2.matcher(patternString).matches() || currency3.matcher(patternString).matches() ||
                        currency4.matcher(patternString).matches() || currency5.matcher(patternString).matches()) {
                    defaultPattern = true;
                }
                if(accounting.matcher(patternString).matches()) {
                    defaultPattern = true;
                    this.isAccountingPattern = true;
                }
                break;

            case FLOAT:
                String baseNumberRegex = "(\"\")?[#0,.]*(\"\")?";
                Pattern num1 = Pattern.compile("^"+ baseNumberRegex +"$");
                Pattern num2 = Pattern.compile("^\\[>=0\\]"+ baseNumberRegex +";\\[RED\\]"+ baseNumberRegex +"$");
                Pattern num3 = Pattern.compile("^\\[>=0\\]"+ baseNumberRegex +";\\[RED\\]-"+ baseNumberRegex +"$");
                Pattern num4 = Pattern.compile("^\\[>=0\\]"+ baseNumberRegex +";\\("+ baseNumberRegex +"\\)$");
                Pattern num5 = Pattern.compile("^\\[>=0\\]"+ baseNumberRegex +";\\[RED\\]\\("+ baseNumberRegex +"\\)$");

                String prefix = this.getPrefix();
                String suffix = this.getSuffix();
                if(prefix != null) {
                    patternString = patternString.replace("\""+prefix+"\"","\"\""); // Replace with empty quotes
                }
                if(suffix != null) {
                    patternString = patternString.replace("\""+suffix+"\"","\"\""); // Replace with empty quotes
                }

                if(num1.matcher(patternString).matches() || num2.matcher(patternString).matches() || num3.matcher(patternString).matches() ||
                        num4.matcher(patternString).matches() || num5.matcher(patternString).matches()) {
                    defaultPattern = true;
                }
                Pattern accWithoutSymbol = Pattern.compile("^\\[>0\\]_\\(\\* [#0,.]*_\\);\\[<0\\]_\\(\\* \\([#0,.]*\\);_\\(\\* [-\"]{1,3}[#]*\\?\\?_\\)[;_\\(@_\\)]*$");
                if(accWithoutSymbol.matcher(patternString).matches()) {
                    defaultPattern = true;
                    this.isAccountingPattern = true;
                }
                break;

            case FRACTION:
                Pattern fraction1 = Pattern.compile("^[#]*[ ]*[\\?]{1,3}\\/[\\?]{1,3}$");
                Pattern fraction2 = Pattern.compile("0\\/0(\\?){1,3}");
                if(fraction1.matcher(patternString).matches() || fraction2.matcher(patternString).matches()) {
                    defaultPattern = true;
                }
                break;

            case PERCENTAGE:
                Pattern percent = Pattern.compile("^[#0.]*[ ]*%$");
                if(percent.matcher(patternString).matches()) {
                    defaultPattern = true;
                }
                break;

            case SCIENTIFIC:
                Pattern scientific = Pattern.compile("^[#0.]*E[0]*$");
                if(scientific.matcher(patternString).matches()) {
                    defaultPattern = true;
                }
                break;
            case STRING:
                if(patternString.equals("@")) {
                    defaultPattern = true;
                }
                break;
        }
        this.isDefault = defaultPattern;


        return this.isDefault;
    }


    public ConditionalPatterns getConditionalPatterns() {
        return conditionalPatterns;
    }

    /* Used only from parser. */
    public void setConditionalPatternsList(List<ConditionalPattern> conditionalPatternsList) {
        this.conditionalPatterns = new ConditionalPatterns();
        conditionalPatterns.setConditionalPatternList(conditionalPatternsList);
    }

    public static ZSPattern getCustomPattern(String patternString, Locale parseLocale, SpreadsheetSettings spreadsheetSettings) {
        ZSPattern customPattern = new ZSPattern(patternString,SpreadsheetSettings.getInstance(parseLocale),false,false, spreadsheetSettings);
        if(customPattern.getType() == Type.ERROR) {
            throw new IllegalArgumentException("Illegal Format String..."); // No I18N
        }
        customPattern.isCustomPattern = true;
        customPattern.isAutoOrder  =false;
        return customPattern;
    }

//    private ZSPattern getDefaultNumberPattern(Locale locale) {
//        DecimalFormat decimalFormat = LocaleUtil.getDefaultNumberFormat(locale);
//        List<PatternComponent> componentList = new ArrayList<>();
//        componentList.add(new ZSDecimalFormat(decimalFormat,locale));
//        return new ZSPattern(componentList,Type.FLOAT,null);
//    }

    private static ZSPattern getDefaultNumberPattern(Locale locale, SpreadsheetSettings spreadsheetSettings, boolean hasExplicitLocale)
    {
        DecimalFormat df = new DecimalFormat();
        DecimalFormatSymbols dfs = new DecimalFormatSymbols(locale);
        if(!hasExplicitLocale) {
            dfs.setGroupingSeparator(spreadsheetSettings.getThousandSeparator());
            dfs.setDecimalSeparator(spreadsheetSettings.getDecimalSeparator());
        }
        df.setDecimalFormatSymbols(dfs);
        df.setMinimumFractionDigits(0);
        df.setMaximumFractionDigits(9);
        df.setMinimumIntegerDigits(1);
        df.setGroupingUsed(false);

        ZSDecimalFormat defaultFormat = new ZSDecimalFormat(df,null,1,spreadsheetSettings,null,false);
        List<PatternComponent> componentList = new ArrayList<>();
        componentList.add(defaultFormat);
        return new ZSPattern(componentList,Type.FLOAT,null,false,false, true);
    }

    public String getRegionalCountry() {
        return this.regionalCountryCode;
    }

    public RegionalType getRegionalType()
    {
        if(regionalType == null)
        {
            regionalType = RegionalType.NONE;
            String formatString = toPatternString();
            Locale patternLocale = null;
            for(PatternComponent component : this.componentList) {
                if(component instanceof ZSDecimalFormat) {
                    patternLocale = ((ZSDecimalFormat) component).getSpreadsheetSettings().getLocale();
                }
            }
            if(patternLocale == null) {
                return regionalType;
            }

            String[] localeList = new String[]{"US","DZ","HR","CZ","CA","DE","FI","FR","IT"}; //No I18N

            country : for(String country : localeList) {
                Map<RegionalType, String> formats = DefaultPatternUtil.getRegionalPatterns(country);
                for (Map.Entry<RegionalType, String> entry : formats.entrySet()) {
                    if (formatString.equals(entry.getValue())) {
                        regionalType = entry.getKey();
                        regionalCountryCode = country;
                        break country;
                    }
                }
            }
            /* To support US Phone format created by older Patterns. */
            if(formatString.equals("[>9999999]#(###)###-###0;#-###0")) {
                regionalType = RegionalType.PHONENUMBER;
                regionalCountryCode = "US"; // No I18N
            }
        }

        return regionalType;
    }

    public ZSPattern clone()
    {
        if(this == EMPTY_PATTERN) {
            return this;
        }

        ZSPattern pattern = null;
        try
        {
            pattern = (ZSPattern)super.clone();

            pattern.componentList = new ArrayList<>();
            for(PatternComponent component : this.componentList) {
                pattern.componentList.add(component);
            }

            pattern.isDefault = null;
            pattern.suffix = null;
            pattern.prefix = null;

            pattern.conditionalPatterns = null;
            if(this.conditionalPatterns != null) {
                for(ConditionalPattern conditionalPattern : this.conditionalPatterns.conditionalPatternList) {
                    pattern.addConditionalPattern(conditionalPattern.getCondition(),conditionalPattern.getPattern().clone(),conditionalPattern.hasExplicitCondition());
                }
                if(this.conditionalPatterns.getTextPattern() != null) {
                    pattern.addTextConditionalPattern(this.getConditionalPatterns().getTextPattern().clone());
                }
            }
        }
        catch(CloneNotSupportedException e)
        {
            LOGGER.log(Level.WARNING,null,e);
        }
        return pattern;
    }

    public boolean equals(Object o) {
        if(this == o) {
            return true;
        }

        if(o == null || !(o instanceof ZSPattern)) {
            return false;
        }

        ZSPattern inPattern = (ZSPattern) o;

        if(this.isAccountingFormat() != inPattern.isAccountingFormat()) {
            return false;
        }

        if(this.hasExplicitLocale != inPattern.hasExplicitLocale) {
            return false;
        }

        if((this.color == null ? inPattern.color != null : !this.color.equals(inPattern.color)) || (this.getType() != inPattern.getType())) {
            return false;
        }

        if(this.isConditionalPattern() != inPattern.isConditionalPattern()) {
            return false;
        }

        if(this.componentList.size() != inPattern.componentList.size()) {
            return false;
        }

        if(this.conditionalPatterns != null && (!this.conditionalPatterns.equals(inPattern.conditionalPatterns))) {
            return false;
        }

        for(int i=0;i<this.componentList.size();i++) {
            PatternComponent component1 = this.componentList.get(i);
            PatternComponent component2 = inPattern.componentList.get(i);
            if(!component1.equals(component2)) {
                return false;
            }
        }

        return true;
    }

    public int hashCode() {
        return super.hashCode();
    }

    public boolean hasDateComponent() {
        for(PatternComponent component : this.componentList) {
            if(component instanceof ZSSimpleDateFormat || component instanceof ZSDurationFormat) {
                return true;
            }
        }
        return false;
    }

    public boolean hasDurationComponent() {
        for(PatternComponent component : this.componentList) {
            if(component instanceof ZSDurationFormat) {
                return true;
            }
        }
        return false;
    }

    /* To XML methods */
    public NumberStyle createNumberStyle(Workbook workbook, String styleName) {
        NumberStyle numberStyle = createStyle(workbook,styleName);
        /* Pattern String will be added only to the parent number style */
        numberStyle.setFormatCode(this.toPatternString());
        if (this.conditionalPatterns != null)
        {
            int i = 0;
            for (ConditionalPattern conditionalPattern : this.conditionalPatterns.getConditionalPatternList())
            {
                String name = styleName + "P" + (i++);//No I18N
                ZSPattern childPattern = conditionalPattern.getPattern();
                NumberStyle childNumberStyle = childPattern.createStyle(workbook,name);
                String conditionString = conditionalPattern.getConditionString();
                conditionString = conditionString.replace(CURRVAL_VAR_STR, Condition.VAL_PLACE_HOLDER_STR);
                MapStyle mStyle = new MapStyleAdapter(workbook, conditionString, childNumberStyle);
                //new MapStyle(ConditionalFormatOperator.ConditionType.FORMULA, -1, condition, childPattern.getNumberStyle(), workbook.getSheet(0), 0, 0);//(null ,condition, null, name);
                numberStyle.addMapStyle(mStyle);
            }
        }

        return numberStyle;
    }

    private NumberStyle createStyle(Workbook workbook, String styleName) {
        Locale dfLocale = null;
        boolean isTruncateOnOverflow = true;
        List<NumberElement> numberElementList = new ArrayList<>();
        for(PatternComponent component : componentList) {
            if(this.hasExplicitLocale) {
                if (component instanceof ZSDecimalFormat) {
                    dfLocale = ((ZSDecimalFormat) component).getSpreadsheetSettings().getLocale();
                }
            }
            if(component instanceof ZSDurationFormat) {
                isTruncateOnOverflow = false;
            }
            component.createNumberElements(numberElementList);
            // TODO : Should repeat index be written to <Number> if repeat index is in embeddedComponents
        }

        NumberStyle numberStyle = new NumberStyle();
        numberStyle.setStyleName(styleName);
        numberStyle.setNumberElementList(numberElementList);
        numberStyle.setAutoOrder(this.isAutoOrder?"true":null);
        numberStyle.setStyleVolatile(this.isCustomPattern);
        numberStyle.setType(this.getType());
        numberStyle.setTruncateOnOverflow(isTruncateOnOverflow);
        numberStyle.setAccountingBoolean(this.isAccountingFormat());

        if(this.getColor() != null)
        {
            TextStyle textStyle = new TextStyle();
            textStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(this.getColor()));
            numberStyle.setTextStyle(textStyle);
        }

        if(dfLocale != null) {
            numberStyle.setLanguage(dfLocale.getLanguage());
            String lCountry = dfLocale.getCountry();
            if(!"".equals(lCountry))
            {
                numberStyle.setCountry(lCountry);
            }
        }

        //workbook.addNumberStyle(numberStyle);
        return numberStyle;

    }

    /* Formatting methods */

    public ZSPattern getConditionalPattern(Workbook workbook, Value value) {

        if(value == null || value.getType() == Type.UNDEFINED) {
            return DataStyleConstants.EMPTY_PATTERN;
        }
        else if(this.getType() == Type.BOOLEAN && value.getType() != Type.BOOLEAN) {
            return DefaultPatternUtil.getDefaultPattern(workbook,value.getType()).getConditionalPattern(workbook, value);
        }
        else if(this.getType() == Type.UNDEFINED) {
            return DefaultPatternUtil.getDefaultPattern(workbook,value.getType()).getConditionalPattern(workbook, value);
        }
        if(!(value.getValue() instanceof Number || value.getValue() instanceof Date))
        {
            if(this.conditionalPatterns != null && this.conditionalPatterns.getTextPattern() != null) {
                return this.conditionalPatterns.getTextPattern();
            }
            else if(this.getType() == Type.STRING)
            {
                return this;
            }
            else if(this.getType() != value.getType())
            {
                // Get default pattern for the value type and return.
                return DefaultPatternUtil.getDefaultPattern(workbook,value.getType()).getConditionalPattern(workbook,value);
            }
            else
            {
                return this;
            }
        }
        if(!isConditionalPattern()) {
            return this;
        }

        Jep jep = Workbook.getJepForOtherActions();
        for(ConditionalPattern conditionalPattern : this.conditionalPatterns.getConditionalPatternList()) {
            Expression condition = conditionalPattern.getCondition();
            ZSPattern pattern = conditionalPattern.getPattern();
            Node conditionNode = condition.getNode();

            try {
                if(conditionNode instanceof ASTParseErrorNode){
                    continue;
                }
                Node node = Workbook.getDeepCopyVisitor().deepCopy(conditionNode);

                Object valueObj;
                if(value.getType() == Type.UNDEFINED){
                    valueObj = 0;
                }
                else{
                    valueObj = value.getValue();
                    if(valueObj instanceof Date){
                        valueObj = DateUtil.convertDateToNumber((Date) valueObj);
                    }
                }

                Node constNode = jep.getNodeFactory().buildConstantNode(valueObj);
                constNode.jjtSetParent(node);
                node.jjtAddChild(constNode, 0);

                Cell dummyCell = new CellImpl();
                dummyCell.setRow(new Row(workbook.getSheet(0), -1));
                dummyCell.setColumn(new Column(workbook.getSheet(0), -1));
                Object result = new ZSEvaluator(true).evaluate(node, dummyCell, false, false);
                result = (result instanceof Value)?((Value)result).getValue():result;
                if(result instanceof Boolean && ((Boolean)result))
                {
                    return pattern;
                }
            }
            catch(Exception e) {

            }
        }
        return this;
    }



    public ContentObject formatValue(Workbook workbook, Value valueObj, boolean addNegativeSign) {
        Object value = valueObj.getValue();
        String repeatChar = null;
        int repeatIndex = -1;
        Map<Integer, Character> invisibleChars = null;
        boolean signAdded = false;

        boolean addSign = false;
        boolean isDuration = false;

        if(value != null) {
            String formattedString = "";
            for (PatternComponent component : componentList) {
                addSign = false;
                PatternComponent tempComponent = component;

                if(component instanceof SpaceChar) {
                    if(invisibleChars == null) {
                        invisibleChars = new HashMap<>();
                    }
                    invisibleChars.put(formattedString.length(), ((SpaceChar)component).getCharacter());
                }
                else if(component instanceof RepeatChar) {
                    repeatChar = ((RepeatChar) component).getRepeatChar();
                    repeatIndex = formattedString.length();
                }
                else if(component instanceof ZSDecimalFormat) {
                    if(((ZSDecimalFormat) component).getEmbeddedComponentMap() != null && invisibleChars == null) {
                        invisibleChars = new HashMap<>();
                    }
                    DecimalFormat decimalFormat = ((ZSDecimalFormat) component).getDecimalFormat();
                    if (value instanceof Date)
                    {
                        value = DateUtil.convertDateToNumber((Date) value);
                    }

                    boolean isScientific = false;
                    if(this.getType() == Type.FLOAT) {
                        if(decimalFormat.getMinimumFractionDigits() == 0) {
                            double tempNum = ((Number) value).doubleValue();
                            if (tempNum != 0) {
                                double absNum = Math.abs(tempNum);
                                if ((absNum < 0.000000001d && this.isDefaultPattern(workbook)) || (absNum > 999999999999999d || absNum < 0.000000000000001d)) {
                                    DecimalFormatSymbols dfs = new DecimalFormatSymbols(workbook.getFunctionLocale());
                                    dfs.setDecimalSeparator(workbook.getSpreadsheetSettings().getDecimalSeparator());
                                    dfs.setGroupingSeparator(workbook.getSpreadsheetSettings().getThousandSeparator());
                                    DecimalFormat tempDF = new DecimalFormat("0.0#####E00", dfs);//No I18N
                                    tempComponent = new ZSDecimalFormat(tempDF,null,((ZSDecimalFormat) component).getDisplayFactor(),((ZSDecimalFormat) component).getSpreadsheetSettings(),null,false);
                                    isScientific = true;
                                }
                            }

                        }
                    }
                    if(addNegativeSign && (value instanceof Number && (((Number)value).doubleValue() < 0))) {
                        if(isScientific || this.getType() == SCIENTIFIC){
                            addSign = true;
                        }
                        else {
                            double displayFactor = ((ZSDecimalFormat) component).getDisplayFactor();
                            DecimalFormat tempDecimalFormat = ((ZSDecimalFormat) tempComponent).getDecimalFormat();
                            Double num = -5 / pow(10, tempDecimalFormat.getMaximumFractionDigits() + 1);
                            Double val = displayFactor > 1 ? ((Number) value).doubleValue() / displayFactor : ((Number) value).doubleValue();
                            val = val * tempDecimalFormat.getMultiplier();
                            if (num >= val) {
                                addSign = true;
                            }
                        }
                    }
                }
                else if(component instanceof ZSSimpleDateFormat) {
                    /* Setting the timeZone object to format object changes the time value. so shud not do that.
                     * Rather replace the timeZone character with TimeZone name String. */
                    String formatStr = component.toPattern(defaultDFS,false); // TODO : Should this be toLocalizedPattern()

                    if(formatStr.contains("z") || formatStr.contains("Z")) { //No I18N
                        formatStr = replaceTimeZone(formatStr, workbook);
                    }

                    tempComponent = new ZSSimpleDateFormat(formatStr,((ZSSimpleDateFormat) component).getFormatLocale());

                    if(value instanceof Number)
                    {
                        value = DateUtil.convertNumberToDate((Number) value);
                    }
                }
                else if(component instanceof ZSDurationFormat) {
                    if(value instanceof Date) {
                        value = DateUtil.convertDateToNumber((Date) value).doubleValue();
                    }
                    if(value instanceof Number && ((Number)value).doubleValue() < 0) {
                        /* Convert to a positive value because we are adding the negative sign in this method, so ZSDurationFormat should not add the negative sign */
                        value = Math.abs(((Number)value).doubleValue());
                        //value = DateUtil.convertNumberToDate(dateAsNumber);
                        addSign = addNegativeSign && true;
                    }
                    isDuration = true;
                }
                else if(component instanceof ZFractionFormat) {
                    if(invisibleChars == null) {
                        invisibleChars = new HashMap<>();
                    }
                    if (value instanceof Date)
                    {
                        value = DateUtil.convertDateToNumber((Date) value);
                    }

//                    if(addNegativeSign) {
//                        Double num = -5 / pow(10, ((ZFractionFormat) component).getMaximumFractionDigits() + 1);
//                        Double val = ((Number) value).doubleValue();
//
//                        if (num >= val) {
//                            addSign = true;
//                        }
//                    }
                }
                else if(component instanceof ZSMessageFormat) {
                    if(!(value instanceof String)) {
                        value = Value.getInstance(Type.STRING,valueObj.getValueString(workbook.getSpreadsheetSettings())).getValue();
                    }
//                    if(value instanceof Throwable) {
//                        value = ((Throwable) value).getMessage();
//                    }
                }
                else if(component instanceof ZSGeneralFormat) {
                    if(value instanceof Date) {
                        tempComponent = workbook.getSpreadsheetSettings().getDateTimeFormat(Cell.Type.DATE, false);
                    }
                }
                else if(component instanceof ZSMillisecondComponent) {
                    if(value instanceof Number)
                    {
                        value = DateUtil.convertNumberToDate((Number) value);
                    }
                }

                if(addSign) {
                    repeatIndex = repeatIndex==-1? -1 : repeatIndex + 1;
                    formattedString = "-" + formattedString;
                }

                Object finalValue = value;
                if(component instanceof ZSSimpleDateFormat) {
                    try {
                        if (incrementSeconds(finalValue)) {
                            if (finalValue instanceof Date) {
                                finalValue = DateUtil.convertDateToNumber((Date) finalValue);
                            }
                            if (finalValue instanceof Number) {
                                double dateAsNumber = ((Number) finalValue).doubleValue();
                                dateAsNumber = dateAsNumber + ((double) 1 / 100000);

                                finalValue = DateUtil.convertNumberToDate(dateAsNumber);
                            }

                        }
                    }
                    catch (Exception e) {
                        LOGGER.log(Level.WARNING, "[ZSPattern][Millisecond] Error while handling millisecond precision :", e);
                        LOGGER.log(Level.WARNING, "[ZSPattern][Millisecond] Pattern {0}", new String[]{this.toPatternString()});
                    }
                }
                if(component instanceof ZSErrorFormat && !(finalValue instanceof Throwable)) {
                    formattedString = formattedString + valueObj.getValueString(workbook.getSpreadsheetSettings());
                }
                else if(tempComponent instanceof ZSGeneralFormat && !(finalValue instanceof Number)) {
                    formattedString = formattedString + valueObj.getValueString(workbook.getSpreadsheetSettings());
                }
                else if(tempComponent instanceof ZSDecimalFormat) {
                    formattedString = formattedString + ((ZSDecimalFormat)tempComponent).format(finalValue, invisibleChars, this.type == SCIENTIFIC);
                }
                else if(tempComponent instanceof ZFractionFormat) {
                    formattedString += ((ZFractionFormat)tempComponent).format(finalValue, invisibleChars);
                }
                else {
                    formattedString = formattedString + tempComponent.format(finalValue);
                }

                if(addSign  && invisibleChars != null && !signAdded) {
                    signAdded = true;
                    List<Integer> keys = invisibleChars.keySet().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                    for(int key: keys) {
                        invisibleChars.put(key+1, invisibleChars.get(key));
                        invisibleChars.remove(key);
                    }
                }


            }
            if(this.type == ERROR && componentList.isEmpty()) {
                formattedString = valueObj.getValueString(workbook.getSpreadsheetSettings());
            }
            return new ContentObject(formattedString,this.getColor(),this.getType(), repeatIndex,repeatChar, invisibleChars, this, valueObj);
        }
        return ContentObject.EMPTY_CONTENTOBJECT;
    }

    public ContentObject formatValue(Workbook workbook, Value value) {
        if(this.getType() == Type.UNDEFINED && value != null && value.getType() != Type.UNDEFINED) {
            ZSPattern pattern = DefaultPatternUtil.getDefaultPattern(workbook,value.getType());
            return pattern.formatValue(workbook,value);
        }

        ZSPattern conditionalPattern = getConditionalPattern(workbook,value);
        boolean isAddNegativeSign = isAddNegativeSign();

        return conditionalPattern.formatValue(workbook,value,isAddNegativeSign);
    }

    private boolean isAddNegativeSign() {
        if(this.isConditionalPattern()) {
            List<ConditionalPattern> conditionalPatterns = this.getConditionalPatterns().getConditionalPatternList();
            int size = conditionalPatterns.size();

            for(ConditionalPattern conditionalPattern : conditionalPatterns) {
                String condition = conditionalPattern.getConditionString();

                if(">=0".equals(condition))
                {
                    if(size == 1)
                    {
                        return false;
                    }
                    else
                    {
                        continue;
                    }
                }

                if(size > 1 && "<0".equals(condition))
                {
                    if(size == 2)
                    {
                        return false;
                    }
                    else
                    {
                        continue;
                    }
                }

                if(size > 2 && "=0".equals(condition))
                {
                    return false;
                }
            }
        }

        return true;
    }

    private boolean incrementSeconds(Object value) {
        if(hasMilliSeconds != null && hasMilliSeconds == false) {
            return false;
        }

        for(PatternComponent component: componentList) {
            if(component instanceof ZSMillisecondComponent) {
                hasMilliSeconds = true;
                return ((ZSMillisecondComponent) component).incrementSecondsNeeded(value);
            }
        }

        hasMilliSeconds = false;
        return false;
    }

    /* This will use EngineConstants.DEFAULT_LOCALE (en_US) as the locale. */
    public String toPatternString() {
        return toPatternString(new DecimalFormatSymbols(EngineConstants.DEFAULT_LOCALE));
    }

    /* This will use the Locale, grouping seperator, decimal seperator, from the spreadsheet settings. */
    public String toLocalisedPatternString(SpreadsheetSettings spreadsheetSettings) {
        DecimalFormatSymbols dfs = new DecimalFormatSymbols(spreadsheetSettings.getLocale());
        dfs.setDecimalSeparator(spreadsheetSettings.getDecimalSeparator());
        dfs.setGroupingSeparator(spreadsheetSettings.getThousandSeparator());
        return toPatternString(dfs);
    }

    protected String toPatternString(DecimalFormatSymbols dfs) {
        String patternString = "";
        ZSPattern textPattern = null;
        if(this.isConditionalPattern()) {
            patternString = this.conditionalPatterns.toPatternString(dfs);
            if(this.conditionalPatterns.getConditionalPatternList().size() >= 3) {
                return patternString;
            }
            patternString += ";";
            textPattern = this.conditionalPatterns.getTextPattern();
        }
        if(this.color != null) {
            patternString = patternString + "[" + DataStyleConstants.COLOR_HASHVALUE_NAME_MAP.get(this.color) + "]";
        }
        for(PatternComponent component : componentList) {
            patternString += component.toPattern(dfs,this.hasExplicitLocale);
        }
        if(textPattern != null) {
            patternString += ";" + textPattern.toPatternString();
        }

        if(this.type == ERROR && patternString.isEmpty()) {
            patternString = "General"; //No I18N
        }
        return patternString;
    }

    /* Temporary method. just for checking*/
//    public static void main(String... args) {
//        Scanner scanner = new Scanner(System.in);
//        String patternString = scanner.nextLine();
//
////        new ZSPattern("#.0\"\"%",Locale.ENGLISH);
//        double tf1 = System.nanoTime();
//        ZSPattern pattern = new ZSPattern(patternString,Locale.ENGLISH);
//        double tf2 = System.nanoTime();
//
//        Value value = Value.getInstance(Type.FLOAT,322.52323232);
////        Value value = Value.getInstance(Type.FLOAT,1231312.23232);
////        Value value = Value.getInstance(Type.FLOAT,1.1);
////        Value value = Value.getInstance(Type.DATE,new Date(System.currentTimeMillis()));
////        Value value = Value.getInstance(Type.STRING,"text");
//
////        System.out.println("formatted Value >> " + pattern.formatValue(,value));
//
//        System.out.println("PatternString from Component List >>> " + pattern.toPatternString());
//        System.out.println("Time to create pattern >> " + ((tf2-tf1)/1000000));
////        pattern.createStyle("",null);
////        for(int i=0;i<2;i++) {
////            Value v;
////            ZSPattern zspat;
////            if(i==0) {
////                zspat = new ZSPattern("\"CDE\"*-#",Locale.ENGLISH);
//////                zspat = new ZSPattern("[H] dd");
////                v = Value.getInstance(Type.FLOAT,322.52323232);
//////                System.out.println(zspat.formatValue(v));
////            }
////            else {
//////                zspat  =new ZSPattern("\"AD\"000.0E+0");
////                zspat  =new ZSPattern("#\"ASD\"\\G,#.0\"XC\"\\g0",Locale.ENGLISH);
//////                zspat  =new ZSPattern("dd-MMMM yyyy");
//////                zspat = new ZSPattern("000\"AD\".0E+0");
////                v = Value.getInstance(Type.FLOAT,1231312.23232);
//////                v = Value.getInstance(Type.FLOAT,1232);
////            }
////            double t1 = System.nanoTime();
////            System.out.println(zspat.formatValue(v));
////            double t2 = System.nanoTime();
////            System.out.println("pattern >> " + zspat.toPatternString());
////            System.out.println("\nTime taken > " + ((t2-t1)/1000000.0));
////        }
//
//    }
//
//    public void printTags(NumberStyle numberStyle) {
//        StringBuilder str = new StringBuilder();
//        for(NumberElement elem : numberStyle.getNumberElementList()) {
//            addNumberElementTag(elem,str);
//        }
//        System.out.println("\n\n" + str.toString());
//    }
//
//    private void addNumberElementTag(NumberElement numberElement, StringBuilder buff)
//    {
//        String elementTagName = numberElement.getTagName();
//        if(elementTagName.equals("fill-character")) {
//            String str = XMLWriter.createStartTagOpen("loext:fill-character",new String[0], new String[0],false); // No I18N
//            buff.append(str);
//            buff.append(XMLWriter.xmlEncode(numberElement.getRepeatChar()));
//            buff.append("</loext:fill-character>"); // No I18N
//        }
//        else {
//            String str = XMLWriter.createStartTagOpen("number:" + numberElement.getTagName(),
//                    numberElement.getAttributes(),
//                    numberElement.getValues(),
//                    false);
//            buff.append(str);
////      |___
////          To get the content if the tag is <number:text>
//            if (numberElement.getContent() != null) {
//                buff.append(XMLWriter.xmlEncode(numberElement.getContent()));
//            }
////      |___
////          child Tags
//            if (numberElement.getEmbeddedTextsList() != null && !numberElement.getEmbeddedTextsList().isEmpty()) {
//                for (EmbeddedText eT : numberElement.getEmbeddedTextsList()) {
//                    addEmbeddedTextTag(eT,buff);
//                }
//            }
//
//
////      End of corresponding tag
//            buff.append("</number:" + numberElement.getTagName() + ">");
//        }
//    }
//
//    private void addEmbeddedTextTag(EmbeddedText embeddedText, StringBuilder buff)
//    {
//        String str = XMLWriter.createStartTagOpen("number:embedded-text",//No I18N
//                embeddedText.getAttributes(),
//                embeddedText.getValues(),
//                false);
//        buff.append(str);
////      |___
////          To get the content if the tag is <number:text>
//        if(embeddedText.getContent()!=null)
//        {
//            buff.append(XMLWriter.xmlEncode(embeddedText.getContent()));
//        }
//
////      End of corresponding tag
//        buff.append("</number:embedded-text>");//No I18N
//    }

}
