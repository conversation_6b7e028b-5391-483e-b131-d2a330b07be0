//$Id$
/**
 * 
 */
package com.adventnet.zoho.websheet.model.style;

/**
 * <AUTHOR>
 * 
 */
public class Theme {/*
	
	public Name themeName;
	public ColorShade backGround1;
	public ColorShade text1;
	public ColorShade backGround2;
	public ColorShade text2;
	public ColorShade accent1;
	public ColorShade accent2;
	public ColorShade accent3;
	public ColorShade accent4;
	public ColorShade accent5;
	public ColorShade accent6;

	public String getThemeColors() {
		return themeName + " A1: " + accent1 + " A2: " + accent2 + " A3: " + accent3 + " A4: " + accent4 + " A5: " + accent5;// NO I18N
	}

	

	public Theme(Name themeName, String backGround, String text, String accent1, String accent2, String accent3, String accent4,String accent5, String accent6){
		this.themeName = themeName;
		this.backGround2 = new ColorShade(backGround);
		this.text2 = new ColorShade(text);
		this.accent1 = new ColorShade(accent1);
		this.accent2 = new ColorShade(accent2);
		this.accent3 = new ColorShade(accent3);
		this.accent4 = new ColorShade(accent4);
		this.accent5 = new ColorShade(accent5);
		this.accent6 = new ColorShade(accent6);
		
	}

	public ColorShade getColorShade(Colors color) {
		switch (color) {
		case BACKGROUND1:
			return this.backGround1;
		case TEXT1:
			return this.text1;
		case BACKGROUND2:
			return this.backGround2;
		case TEXT2:
			return this.text2;
		case ACCENT1:
			return this.accent1;
		case ACCENT2:
			return this.accent2;
		case ACCENT3:
			return this.accent3;
		case ACCENT4:
			return this.accent4;
		case ACCENT5:
			return this.accent5;
		case ACCENT6:
			return this.accent6;
		}
		return null;
	}
	
	

	public static class ShadesName {
		public static final String ACCENT = Shades.ACCENT.toString().toLowerCase();
		public static final String LIGHT80 = Shades.LIGHT80.toString().toLowerCase();
		public static final String LIGHT60 = Shades.LIGHT60.toString().toLowerCase();
		public static final String LIGHT40 = Shades.LIGHT40.toString().toLowerCase();
		public static final String DARK25 = Shades.DARK25.toString().toLowerCase();
		public static final String DARK50 =  Shades.DARK50.toString().toLowerCase();
	}

	public static class ColorShade {
		
		String accent;
		String light80;
		String light60;
		String light40;
		String dark25;
		String dark50;
		JSONObject colors;
		
		public ColorShade(String primaryColor) {
			this.accent = primaryColor;
			this.colors = Colorpalette.deriveSecondaryColors(primaryColor);
			this.colors.put(ShadesName.ACCENT, accent);
			this.light80 = this.colors.getString(ShadesName.LIGHT80);
			this.light60 = this.colors.getString(ShadesName.LIGHT60);
			this.light40 = this.colors.getString(ShadesName.LIGHT40);
			this.dark25 = this.colors.getString(ShadesName.DARK25);
			this.dark50 = this.colors.getString(ShadesName.DARK50);
			System.out.println("mani test "+dark50);
		}

		public JSONObject getShades() {
			return this.colors;
		}

		public String getShade(Shades shade) {
			switch (shade) {
			case LIGHT80:
				return this.light80;
			case LIGHT60:
				return this.light60;
			case LIGHT40:
				return this.light40;
			case DARK25:
				return this.dark25;
			case DARK50:
				return this.dark50;
			}
			return "";
		}
	}

*/}
