/* $Id$ */
package com.adventnet.zoho.websheet.model.style;

import com.adventnet.zoho.websheet.model.Workbook;
import com.google.common.collect.ImmutableMap;

import java.util.*;
import java.util.stream.Collectors;

public class TableStyle {
    public enum TableStylePropertyKey {
        WHOLE_TABLE("wholeTable", "wholeTable"), //No I18N
        HEADER_ROW("headerRow", "headerRow"), //No I18N
        FIRST_HEADER_CELL("firstHeaderCell", "firstHeaderCell"), //No I18N
        LAST_HEADER_CELL("lastHeaderCell", "lastHeaderCell"), //No I18N
        TOTAL_ROW("totalRow", "totalRow"), //No I18N
        FIRST_TOTAL_CELL("firstTotalCell", "firstTotalCell"), //No I18N
        LAST_TOTAL_CELL("lastTotalCell", "lastTotalCell"), //No I18N//No I18N
        FIRST_COLUMN_STRIPE("firstColumnStripe", "firstColumnStripe"), //No I18N
        SECOND_COLUMN_STRIPE("secondColumnStripe", "secondColumnStripe"), //No I18N
        FIRST_ROW_STRIPE("firstRowStripe", "firstRowStripe"), //No I18N
        SECOND_ROW_STRIPE("secondRowStripe", "secondRowStripe"), //No I18N
        FIRST_COLUMN("firstColumn", "firstColumn"), //No I18N
        LAST_COLUMN("lastColumn", "lastColumn"); //No I18N

        private final String zsString;
        private final String jsonString;

        TableStylePropertyKey(String zsString, String jsonString) {
            this.zsString = zsString;
            this.jsonString = jsonString;
        }

        public String getZSString(){
            return this.zsString;
        }

        public String getJsonString(){
            return this.jsonString;
        }

        @Override
        public String toString() {
            return this.jsonString;
        }

        public static TableStylePropertyKey fromXmlName(String zsString) {
            return ZS_STRING_TO_TABLE_STYLE_ELEMENT_KEY_MAP.get(zsString);
        }

        public static TableStylePropertyKey fromJsonString(String jsonString) {
            return JSON_STRING_TO_TABLE_STYLE_ELEMENT_KEY_MAP.get(jsonString);
        }

        private static final Map<String, TableStylePropertyKey> ZS_STRING_TO_TABLE_STYLE_ELEMENT_KEY_MAP = ImmutableMap.copyOf(
            Arrays.stream(TableStylePropertyKey.values())
                .collect(
                    Collectors.toMap(
                        TableStylePropertyKey::getZSString,
                        tableStyleElementType -> tableStyleElementType
                    )
                )
        );
        private static final Map<String, TableStylePropertyKey> JSON_STRING_TO_TABLE_STYLE_ELEMENT_KEY_MAP = ImmutableMap.copyOf(
            Arrays.stream(TableStylePropertyKey.values())
                .collect(
                    Collectors.toMap(
                        TableStylePropertyKey::getJsonString,
                        tableStyleElementType -> tableStyleElementType
                    )
                )
        );
    }

    public static class TableStylePropertyValue {
        private int stripeSize;
        private CellStyle cellStyle;

        public TableStylePropertyValue(int stripeSize, CellStyle cellStyle) {
            this.stripeSize = stripeSize;
            this.cellStyle = cellStyle;
            if(cellStyle == null) {
                this.cellStyle = new CellStyle();
            }
        }

        public int getStripeSize() {
            return stripeSize;
        }

        @Deprecated
        public void setStripeSize(int stripeSize) {
            this.stripeSize = stripeSize;
        }

        public CellStyleProperties getCellStyleProperties() {
            return this.cellStyle.getCellStyleProperties();
        }

        public ParagraphStyleProperties getParagraphStyleProperties() {
            return this.cellStyle.getParagraphStyleProperties();
        }

        public TextStyleProperties getTextStyleProperties() {
            return this.cellStyle.getTextStyleProperties();
        }

        public CellStyle getCellStyle(){
            return this.cellStyle;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof TableStylePropertyValue)) {
                return false;
            }
            TableStylePropertyValue that = (TableStylePropertyValue) o;
            return stripeSize == that.stripeSize &&
                Objects.equals(cellStyle, that.cellStyle);
        }

        @Override
        public int hashCode() {
            return Objects.hash(stripeSize, cellStyle);
        }

        public TableStylePropertyValue clone() {
            return new TableStylePropertyValue(
                this.stripeSize,
                    this.cellStyle.clone()
            );
        }
    }

    private final String name;
    private final boolean isCustomStyle;
    private final EnumMap<TableStylePropertyKey, TableStylePropertyValue> elementsMap;

    public TableStyle(String name, boolean isCustomStyle) {
        this.name = name;
        this.isCustomStyle = isCustomStyle;
        this.elementsMap = new EnumMap(TableStylePropertyKey.class);
    }

    public TableStyle(String name, Map<TableStylePropertyKey, TableStylePropertyValue> elementsMap, boolean isCustomStyle) {
        this.name = name;
        this.isCustomStyle = isCustomStyle;
        this.elementsMap = new EnumMap(elementsMap);
    }

    public String getName() {
        return this.name;
    }

    public boolean getIsCustomStyle() {
        return this.isCustomStyle;
    }

    public TableStylePropertyValue get(TableStylePropertyKey tableStylePropertyKey) {
        if(tableStylePropertyKey == null) {
            throw new IllegalArgumentException("TableStylePropertyKey NULL"); //No I18N
        }
        return this.elementsMap.get(tableStylePropertyKey);
    }

    public Map<TableStylePropertyKey, TableStylePropertyValue> getProperties() {
        return Collections.unmodifiableMap(this.elementsMap);
    }

    @Deprecated
    public void put(TableStylePropertyKey tableStylePropertyKey, TableStylePropertyValue tableStylePropertyValue) {
        if(tableStylePropertyKey == null) {
            throw new IllegalArgumentException("TableStylePropertyKey NULL"); //No I18N
        }
        if(tableStylePropertyValue == null) {
            this.remove(tableStylePropertyKey);
        }
        this.elementsMap.put(tableStylePropertyKey, tableStylePropertyValue);
    }

    @Deprecated
    public void remove(TableStylePropertyKey tableStylePropertyKey) {
        this.elementsMap.remove(tableStylePropertyKey);
    }

    @Override
    public TableStyle clone() {
        return cloneWithNewName(this.name);
    }


    public boolean equalsIgnoreName(TableStyle otherStyle) {
        if(otherStyle == null) {
            return false;
        }
        if(this.elementsMap.size() != otherStyle.elementsMap.size()) {
            return false;
        }
        if(this.isCustomStyle != otherStyle.isCustomStyle) {
            return false;
        }

        for(Map.Entry<TableStylePropertyKey, TableStylePropertyValue> property: this.elementsMap.entrySet()) {
            TableStylePropertyKey propertyKey = property.getKey();
            TableStylePropertyValue propertyValue = property.getValue();
            TableStylePropertyValue otherValue = otherStyle.get(propertyKey);

            if((propertyValue == null && otherValue != null) || (otherValue == null && propertyValue != null)) {
                return false;
            }
            if(propertyValue == null) {
                continue;
            }

            if(!propertyValue.equals(otherValue)) {
                return false;
            }
        }

        return true;
    }

    public TableStyle cloneWithNewName(String name) {
        Map<TableStylePropertyKey, TableStylePropertyValue> elementsMapCloned = new EnumMap(TableStylePropertyKey.class);
        for(Map.Entry<TableStylePropertyKey, TableStylePropertyValue> property: this.elementsMap.entrySet()) {
            TableStylePropertyKey tableStylePropertyKey = property.getKey();
            TableStylePropertyValue tableStylePropertyValue = property.getValue();
            TableStylePropertyValue tableStylePropertyValueCloned = tableStylePropertyValue.clone();
            elementsMapCloned.put(tableStylePropertyKey, tableStylePropertyValueCloned);
        }
        return new TableStyle(name, elementsMapCloned, this.isCustomStyle);
    }

    /*
    protected TableStyle clone(String name) {
        return new TableStyle(name, this.elementsMap);
    }
    */

    public void getStylePropsFromPool(Workbook workbook) {
        for(TableStylePropertyValue tableStylePropertyValue: this.elementsMap.values()) {
            if(tableStylePropertyValue.cellStyle != null){
                tableStylePropertyValue.cellStyle.getStylePropsFromPool(workbook);
            }
        }
    }
}
