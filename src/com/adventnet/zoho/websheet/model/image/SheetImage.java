//$Id$

package com.adventnet.zoho.websheet.model.image;

import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.writer.zs.XMLWriter;
import com.adventnet.zoho.websheet.model.zsparser.Names;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * This class represents the image in a sheet, referred from {@link com.adventnet.zoho.websheet.model.ImageBook} by {@code image id}
 *
 * <AUTHOR> N J (ZT-0049)
 */
public class SheetImage implements Cloneable
{
    private static final Logger LOGGER = Logger.getLogger(SheetImage.class.getName());

    private int id;
    private int imageID;
    private double height;
    private double width;
    private int rowIndex;
    private int columnIndex;
    private double rowDiff;
    private double columnDiff;

    /**
     * @param id unique id of the sheet image
     * @param imageID Image id created / referred from Image book.
     * @param rowIndex row index.
     * @param columnIndex column index.
     * @param rowDiff row difference from the row index.
     * @param columnDiff column difference from the column index.
     */
    public SheetImage(int id, int imageID, double height, double width, int rowIndex, int columnIndex, double rowDiff, double columnDiff)
    {
        this.id = id;
        this.imageID = imageID;
        this.height = height;
        this.width = width;
        this.rowIndex = rowIndex;
        this.columnIndex = columnIndex;
        this.rowDiff = rowDiff;
        this.columnDiff = columnDiff;
    }

    public int getId()
    {
        return id;
    }

    public int getImageID()
    {
        return imageID;
    }

    public void setImageID(int imageID)
    {
        this.imageID = imageID;
    }

    public double getHeight()
    {
        return height;
    }

    public void setHeight(double height)
    {
        this.height = height;
    }

    public double getWidth()
    {
        return width;
    }

    public void setWidth(double width)
    {
        this.width = width;
    }

    public int getRowIndex()
    {
        return rowIndex;
    }

    public void setRowIndex(int rowIndex)
    {
        this.rowIndex = rowIndex > Utility.MAXNUMOFROWS -1 ? Utility.MAXNUMOFROWS - 1  : rowIndex;
    }

    public int getColumnIndex()
    {
        return columnIndex;
    }

    public void setColumnIndex(int columnIndex)
    {
        this.columnIndex = columnIndex > Utility.MAXNUMOFCOLS - 1 ? Utility.MAXNUMOFCOLS - 1 : columnIndex;
    }

    public double getRowDiff()
    {
        return rowDiff;
    }

    public void setRowDiff(double rowDiff)
    {
        this.rowDiff = rowDiff;
    }

    public double getColumnDiff()
    {
        return columnDiff;
    }

    public void setColumnDiff(double columnDiff)
    {
        this.columnDiff = columnDiff;
    }

    @Override
    public SheetImage clone()
    {
        try
        {
            SheetImage sheetImage = (SheetImage)super.clone();
            return sheetImage;
        }
        catch(CloneNotSupportedException e)
        {
            LOGGER.log(Level.INFO, e.getMessage());
            return null;
        }
    }

    public SheetImage clone(int imageID)
    {
        try
        {
            SheetImage sheetImage = (SheetImage)super.clone();
            sheetImage.setImageID(imageID);
            return sheetImage;
        }
        catch(CloneNotSupportedException e)
        {
            LOGGER.log(Level.INFO, e.getMessage());
            return null;
        }
    }

    public List<String> getAttributes()
    {
        List<String> attributes = new ArrayList<>();
        attributes.add(XMLWriter.toString(Names.A_SHEET_IMAGE_ID));
        attributes.add(XMLWriter.toString(Names.A_IMAGE_ID));
        attributes.add(XMLWriter.toString(Names.A_IMAGE_ROW_INDEX));
        attributes.add(XMLWriter.toString(Names.A_IMAGE_COLUMN_INDEX));
        attributes.add(XMLWriter.toString(Names.A_IMAGE_ROW_DIFF));
        attributes.add(XMLWriter.toString(Names.A_IMAGE_COLUMN_DIFF));
        attributes.add(XMLWriter.toString(Names.A_IMAGE_HEIGHT));
        attributes.add(XMLWriter.toString(Names.A_IMAGE_WIDTH));
        return attributes;
    }

    public List<String> getValues()
    {
        List<String> values = new ArrayList<>();
        values.add(String.valueOf(this.getId()));
        values.add(String.valueOf(this.getImageID()));
        values.add(String.valueOf(this.getRowIndex()));
        values.add(String.valueOf(this.getColumnIndex()));
        values.add(this.getRowDiff() > 0 ? String.valueOf(this.getRowDiff()) : null);
        values.add(this.getColumnDiff() > 0 ? String.valueOf(this.getColumnDiff()) : null);
        values.add(this.getHeight() > 0 ? String.valueOf(this.getHeight()) : null);
        values.add(this.getWidth() > 0 ? String.valueOf(this.getWidth()) : null);
        return values;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        SheetImage that = (SheetImage) o;
        return id == that.id &&
                imageID == that.imageID &&
                rowIndex == that.rowIndex &&
                columnIndex == that.columnIndex &&
                Double.compare(that.rowDiff, rowDiff) == 0 &&
                Double.compare(that.columnDiff, columnDiff) == 0;
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(id, imageID, rowIndex, columnIndex, rowDiff, columnDiff);
    }
}
