//// $Id$

package com.adventnet.zoho.websheet.model.stats;

import com.adventnet.zoho.websheet.model.Expression;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.FormulaUtil;
import com.adventnet.zoho.websheet.model.util.RangeUtil;
import com.adventnet.zoho.websheet.model.util.RangeUtil.SheetCell;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.HashMultiset;
import com.google.common.collect.ImmutableTable;
import com.google.common.collect.Multiset;
import com.google.common.collect.Table;
import java.util.Collection;
import java.util.LinkedList;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class FormulaStatsExtractor {
    private final Workbook workbook;
    
    public FormulaStatsExtractor(Workbook workbook) {
        this.workbook = workbook;
        this.formulaUsageMaster = null;
    }
    
    private Table<Expression, String, Collection<RangeUtil.SheetCell>> formulaUsageMaster;
    private Table<String, String, Multiset<RangeUtil.SheetCell>> functionUsageMaster;
    
    public ImmutableTable<Expression, String, Collection<RangeUtil.SheetCell>> getFormulaUsageTable() {
        if(!isMastersPopulated()) {
            populateMasters();
        }
        return ImmutableTable.copyOf(formulaUsageMaster);
    }
    
    public ImmutableTable<String, String, Multiset<SheetCell>> getFunctionUsageTable() {
        if(!isMastersPopulated()) {
            populateMasters();
        }
        return ImmutableTable.copyOf(functionUsageMaster);
    }
    
    private boolean isMastersPopulated() {
        return Objects.nonNull(formulaUsageMaster);
    }
    
    private void populateMasters() {
        formulaUsageMaster = HashBasedTable.create(Utility.MAXNUMOFROWS, workbook.getSheets().length);
        functionUsageMaster = HashBasedTable.create(Workbook.getJepForOtherActions().getFunctionTable().size(), workbook.getSheets().length);
        
        
        for(Sheet sheet: this.workbook.getSheetList()) {
            String asn = sheet.getAssociatedName();
            formulaUsageMaster.columnKeySet().add(asn);
            functionUsageMaster.columnKeySet().add(asn);
            
            sheet.getFormulaCells().stream().forEach((cell) -> {
                SheetCell sheetCell = RangeUtil.SheetCell.fromCell(cell);
                Expression expression = cell.getExpression();
                
                //Update formulaUsageMaster
                Collection<RangeUtil.SheetCell> cells = formulaUsageMaster.get(expression, asn);
                if(cells == null) {
                    cells = new LinkedList<>();
                    formulaUsageMaster.put(expression, asn, cells);
                }
                cells.add(sheetCell);
                
                //Update functionUsageMaster
                Multiset<String> functionsUsed = FormulaUtil.getContainingFunctionNames(expression.getNode());
                functionsUsed.elementSet()
                        .stream()
                        .forEach(function -> {
                            int count = functionsUsed.count(function);
                            Multiset<SheetCell> oldCells = functionUsageMaster.get(function, asn);
                            if(oldCells == null) {
                                oldCells = HashMultiset.create();
                                functionUsageMaster.put(function, asn, oldCells);
                            }
                            oldCells.add(sheetCell, count);
                        });
            });
        }
    }
}
