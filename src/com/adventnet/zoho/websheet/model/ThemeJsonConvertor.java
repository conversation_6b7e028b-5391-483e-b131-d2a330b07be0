/* $Id$ */
package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.MapStyle;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.Collections;
import java.util.List;

import static com.zoho.sheet.util.ConditionFormatUtils.createConditionalStyleRanges;

public class ThemeJsonConvertor {

    public static JSONObjectWrapper getCFNewModelJSON(JSONObjectWrapper inputJSON, int action){
        switch (action){
            case ActionConstants.COLOR_SCALES_APPLY:
                inputJSON.put(JSONConstants.RULES, getCSJSONArray(inputJSON));
                break;
            case ActionConstants.ICON_SET_APPLY:
                inputJSON.put(JSONConstants.RULES, getISJSONArray(inputJSON));
                break;
            case ActionConstants.CONDITIONAL_FORMAT_APPLY:
                inputJSON.put(JSONConstants.RULES, getCFClassicJSONArray(inputJSON));
                break;
        }
        return inputJSON;
    }

    public static JSONObjectWrapper decodeCValAndConvertToNewCFJson(String oldCFJSONString, String validateFor, int forCFAction){

        JSONObjectWrapper inputCFJson;
        inputCFJson = new JSONObjectWrapper(oldCFJSONString);
        for(int i=1;;i++) {
            String iterKey = JSONConstants.CRITERIA_VALUE + i;
            if (inputCFJson.has(iterKey)) {
                String valString = inputCFJson.getString(iterKey);
                valString = Utility.getDecodedString(valString);
                inputCFJson.put(iterKey, valString);
            }
            else {
                break;
            }
        }

        if("colorscales".equals(validateFor) || forCFAction == ActionConstants.COLOR_SCALES_APPLY){
            getCFNewModelJSON(inputCFJson, ActionConstants.COLOR_SCALES_APPLY);
        }
        else if("iconset".equals(validateFor) || forCFAction == ActionConstants.ICON_SET_APPLY){
            getCFNewModelJSON(inputCFJson, ActionConstants.ICON_SET_APPLY);
        }
        else if("conditionalformat".equals(validateFor) || forCFAction == ActionConstants.CONDITIONAL_FORMAT_APPLY){
            getCFNewModelJSON(inputCFJson, ActionConstants.CONDITIONAL_FORMAT_APPLY);
        }
        return inputCFJson;
    }

    private static JSONArrayWrapper getCSJSONArray(JSONObjectWrapper condParamJSON){
        JSONArrayWrapper c_sJSONArray = new JSONArrayWrapper();
        int n = condParamJSON.getInt(JSONConstants.NOOFCOND);
        for(int i = 1; i <= n; i++){
            JSONObjectWrapper c_sVal = new JSONObjectWrapper();
            c_sVal.put(JSONConstants.CRITERIA_TYPE, condParamJSON.optString(JSONConstants.CRITERIA_TYPE+i, null));
            /*      |__ */ condParamJSON.remove(JSONConstants.CRITERIA_TYPE+i);                             //Removing original after creating duplicate.
            c_sVal.put(JSONConstants.CRITERIA_VALUE, condParamJSON.optString(JSONConstants.CRITERIA_VALUE+i, ""));
            /*      |__ */ condParamJSON.remove(JSONConstants.CRITERIA_VALUE+i);                            //Removing original after creating duplicate.
            c_sVal.put(JSONConstants.COLOR, new JSONObjectWrapper().put(JSONConstants.HEXCOLOR, condParamJSON.optString(JSONConstants.COLORSCALE_COLOR+i, null)));
            /*      |__ */ condParamJSON.remove(JSONConstants.COLORSCALE_COLOR+i);                          //Removing original after creating duplicate.
            c_sJSONArray.put(c_sVal);
        }
        condParamJSON.remove(JSONConstants.NOOFCOND);
        return c_sJSONArray;
    }

    private static JSONArrayWrapper getISJSONArray(JSONObjectWrapper condParamJSON){
        JSONArrayWrapper i_sJSONArray = new JSONArrayWrapper();
        int n = condParamJSON.getInt(JSONConstants.NOOFCOND);
        for(int i = 1; i <= n; i++){
            JSONObjectWrapper i_sVal = new JSONObjectWrapper();
            i_sVal.put(JSONConstants.CRITERIA_TYPE, condParamJSON.optString(JSONConstants.CRITERIA_TYPE+i, null));
            /*      |__ */ condParamJSON.remove(JSONConstants.CRITERIA_TYPE+i);                             //Removing original after creating duplicate.
            i_sVal.put(JSONConstants.CRITERIA_VALUE, condParamJSON.optString(JSONConstants.CRITERIA_VALUE+i, "0"));
            /*      |__ */ condParamJSON.remove(JSONConstants.CRITERIA_VALUE+i);                            //Removing original after creating duplicate.
            i_sVal.put(JSONConstants.ICON_ID, condParamJSON.optInt(JSONConstants.ICON_ID+i, i));
            /*      |__ */ condParamJSON.remove(JSONConstants.ICON_ID+i);                                   //Removing original after creating duplicate.
            i_sVal.put(JSONConstants.ICON_NAME, condParamJSON.optString(JSONConstants.ICON_NAME+i, null));
            /*      |__ */ condParamJSON.remove(JSONConstants.ICON_NAME+i);                                 //Removing original after creating duplicate.
            i_sVal.put(JSONConstants.ICON_CRITERIA, condParamJSON.optString(JSONConstants.ICON_CRITERIA+i, null));
            /*      |__ */ condParamJSON.remove(JSONConstants.ICON_CRITERIA+i);                             //Removing original after creating duplicate.
            i_sJSONArray.put(i_sVal);
        }
        condParamJSON.remove(JSONConstants.NOOFCOND);
        return i_sJSONArray;
    }

    private static JSONArrayWrapper getCFClassicJSONArray(JSONObjectWrapper cfJSON){
        int noOfRules = cfJSON.getInt(JSONConstants.NOOFRULES);
        /*      |__ */ cfJSON.remove(JSONConstants.NOOFRULES);
        JSONArrayWrapper rulesJSONArray = new JSONArrayWrapper();
        for(int i = 1 ; i <= noOfRules; i++){
            JSONObjectWrapper ruleJSON = new JSONObjectWrapper();
            ruleJSON.put(JSONConstants.CRITERIA_TYPE, cfJSON.getString(JSONConstants.CRITERIA_TYPE+i));
            /*      |__ */ cfJSON.remove(JSONConstants.CRITERIA_TYPE+i);                                  //Removing original after creating duplicate.
            ruleJSON.put(JSONConstants.CRITERIA_CONDITION, cfJSON.getInt(JSONConstants.CRITERIA_CONDITION+i));
            /*      |__ */ cfJSON.remove(JSONConstants.CRITERIA_CONDITION+i);                             //Removing original after creating duplicate.
            ruleJSON.put(JSONConstants.CRITERIA_VALUE, cfJSON.getString(JSONConstants.CRITERIA_VALUE+i));
            /*      |__ */ cfJSON.remove(JSONConstants.CRITERIA_VALUE+i);                                 //Removing original after creating duplicate.

            String style = cfJSON.optString(JSONConstants.CRITERIA_STYLE + i, null);
            if (style != null) {
                JSONObjectWrapper cStyleJSON = new JSONObjectWrapper();
                /*      |__ */
                cfJSON.remove(JSONConstants.CRITERIA_STYLE + i);                                 //Removing original after creating duplicate.
                String[] cellStyles = style.split("~");

                if ((cellStyles[0] != null) && (!cellStyles[0].equalsIgnoreCase("none"))) {
                    int startIndex = cellStyles[0].indexOf("(");
                    String bgcolor = cellStyles[0];
                    if (startIndex >= 0) {
                        bgcolor = EngineUtils.getColorValueAsHex(cellStyles[0].substring(startIndex + 1, cellStyles[0].indexOf(")")));
                    }
                    JSONObjectWrapper bgColorJSON = new JSONObjectWrapper();
                    bgColorJSON.put(JSONConstants.HEXCOLOR, bgcolor);
                    cStyleJSON.put(JSONConstants.BACKGROUNDCOLOR, bgColorJSON);
                }

                if ((cellStyles[1] != null) && (!cellStyles[1].equalsIgnoreCase("none"))) {
                    int startIndex = cellStyles[1].indexOf("(");
                    String color = cellStyles[1];
                    if (startIndex >= 0) {
                        color = EngineUtils.getColorValueAsHex(cellStyles[1].substring(startIndex + 1, cellStyles[1].indexOf(")")));
                    }
                    JSONObjectWrapper textColorJSON = new JSONObjectWrapper();
                    textColorJSON.put(JSONConstants.HEXCOLOR, color);
                    cStyleJSON.put(JSONConstants.TEXTCOLOR, textColorJSON);
                }

                cStyleJSON.put(JSONConstants.BOLD, "true".equals(cellStyles[2]) ? 1 : 0);
                cStyleJSON.put(JSONConstants.ITALIC, "true".equals(cellStyles[3]) ? 1 : 0);
                cStyleJSON.put(JSONConstants.UNDERLINE, cellStyles[4]);
                cStyleJSON.put(JSONConstants.STRIKE, "true".equals(cellStyles[5]) ? 1 : 0);

                ruleJSON.put(JSONConstants.CRITERIA_STYLE, cStyleJSON);
            }
            rulesJSONArray.put(ruleJSON);
        }
        return rulesJSONArray;
    }

    public static void getRangeListForOldClient(JSONObjectWrapper actionJson, Sheet activeSheet){
        boolean FromNewClient = (actionJson.has(JSONConstants.RANGELIST) ) ? true : false;
        if(!FromNewClient)
        {
            JSONObjectWrapper paramJsonObj = actionJson.has(JSONConstants.CSTYLEJSON) ? actionJson.optJSONObject(JSONConstants.CSTYLEJSON) : actionJson.getJSONObject("valInpsJson");
            if(paramJsonObj != null)
            {
                String dataRange = paramJsonObj.optString(JSONConstants.DATARANGE);
                if(dataRange != null)
                {
                    List<DataRange> ranges = createConditionalStyleRanges(activeSheet,dataRange);
                    JSONArrayWrapper jsonArrayOfRanges = new JSONArrayWrapper();
                    ranges.forEach((range) -> {
                        ActionJsonUtil.addRangeInJsonArray(jsonArrayOfRanges, range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(),true);
                    });
                    actionJson.put(JSONConstants.RANGELIST, jsonArrayOfRanges);
                    String asn = ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST));
                    JSONObjectWrapper jsonRange = ActionJsonUtil.getFirstRangeFromJsonArray(actionJson.optJSONArray(JSONConstants.RANGELIST));
                    if(asn != null)
                    {
                        actionJson.put(JSONConstants.CURRENT_ACTIVE_SHEET, asn);
                    }
                    if(jsonRange != null)
                    {
                        actionJson.put(JSONConstants.CURRENT_ACTIVE_ROW, jsonRange.getInt(JSONConstants.START_ROW));
                        actionJson.put(JSONConstants.CURRENT_ACTIVE_COLUMN, jsonRange.getInt(JSONConstants.START_COLUMN));
                    }
                }
            }
        }
    }


    public static String readConditionalFormat(Workbook workBook, Sheet sheet, String dateFormat, String timeFormat, int sR, int sC, int eR, int eC, int mode)
    {
        String resp = "";
        switch(mode){
            case ActionConstants.CONDITIONAL_FORMAT_READ_RANGE_FOR_MOBILE:
                resp = readConditionalFormatForRange(sheet, dateFormat, timeFormat, sR, sC, eR, eC);
                break;
            case ActionConstants.CONDITIONAL_FORMAT_READ_SHEET_FOR_MOBILE:
                resp = readConditionalFormatForSheet(sheet, dateFormat, timeFormat);
                break;
            case ActionConstants.CONDITIONAL_FORMAT_READ_WORKBOOK_FOR_MOBILE:
                resp = readConditionalFormatForWorkbook(workBook, dateFormat, timeFormat);
                break;
        }
        return resp;
    }

    public static String readConditionalFormatForRange(Sheet sheet, String dateFormat, String timeFormat, int sR, int sC, int eR, int eC)
    {
        JSONArrayWrapper jsonArray = new JSONArrayWrapper();
        generateConditionalStyleJSON_ForMobile(sheet,dateFormat, timeFormat, sR, sC, eR, eC, jsonArray);
        return jsonArray.isEmpty() ? "" : jsonArray.toString();
    }

    private static String readConditionalFormatForSheet(Sheet sheet, String dateFormat, String timeFormat)
    {
        JSONArrayWrapper jsonArray = new JSONArrayWrapper();
        generateConditionalStyleJSON_ForMobile(sheet,dateFormat, timeFormat, 0, 0, Utility.MAXNUMOFROWS-1, Utility.MAXNUMOFCOLS-1, jsonArray);
        return jsonArray.isEmpty() ? "" : jsonArray.toString();
    }

    private static String readConditionalFormatForWorkbook(Workbook workBook, String dateFormat, String timeFormat)
    {
        JSONArrayWrapper jsonArray = new JSONArrayWrapper();
        for(Sheet sheet : workBook.getSheetList())
        {
            generateConditionalStyleJSON_ForMobile(sheet,dateFormat, timeFormat, 0, 0, Utility.MAXNUMOFROWS-1, Utility.MAXNUMOFCOLS-1, jsonArray);
        }
        return jsonArray.isEmpty() ? "" : jsonArray.toString();
    }


    private static void generateConditionalStyleJSON_ForMobile(Sheet sheet, String dateFormatStr, String timeFormatStr, int sR, int sC, int eR, int eC, JSONArrayWrapper jsonArray)
    {
        DataRange dataRange = null;
        ZSTheme zsTheme = sheet.getWorkbook().getTheme();
        if(sR != 0 || sC != 0 || eR != (Utility.MAXNUMOFROWS-1) || eC != (Utility.MAXNUMOFCOLS-1))
        {
            dataRange = new DataRange(sheet.getAssociatedName(), sR, sC, eR, eC);
        }

        List<Integer> csuid = sheet.getConditionalStyleUIDList();
        for(Integer csId : csuid)
        {
            ConditionalStyleObject cso = sheet.getConditionalStyleMap().get(csId);
            ConditionalStyle cs = cso.getConditionalStyle();
            // NOTE: Need to remove this if check once ICONSET has been supported completely.
            if(cs instanceof DataBar)
            {
                continue;
            }

            List<DataRange> ranges = cso.getSpecialRange().getRanges();
            Collections.sort(ranges, new CellUtil.ColMajorDataRangeStartIndexComparator());
            boolean isWholeSheet = dataRange == null;

            String rangeName = "";
            for(DataRange range : ranges)
            {
                rangeName += range.getRangeStringForClient(sheet.getWorkbook())+";";
                isWholeSheet = (!isWholeSheet && RangeUtil.intersection(dataRange, range) != null) ? true : isWholeSheet;
            }
            if(!isWholeSheet)
            {
                continue;
            }
            rangeName = rangeName.substring(0, rangeName.length() - 1);
            String type = null;
            JSONObjectWrapper obj = new JSONObjectWrapper();
            int rowIndex = ranges.get(0).getStartRowIndex();
            int colIndex = ranges.get(0).getStartColIndex();

            if(cs instanceof MapStyle)
            {
                type = JSONConstants.CONDITION_FORMAT;
                MapStyle mapStyle = (MapStyle)cs;
                obj.put(JSONConstants.CRITERIA_CONDITION+"1", mapStyle.getCondnNum());
                obj.put(JSONConstants.CRITERIA_TYPE+"1", mapStyle.getConditionType().name());
                JSONArrayWrapper varr = new JSONArrayWrapper();

                String value =  (mapStyle.getConditionType() == ConditionalFormatOperator.ConditionType.FORMULA) ? FormulaUtil.getLocalizedFormula(mapStyle.getExpression().getNode(), sheet.getWorkbook(), null, rowIndex, colIndex) : mapStyle.getValue(sheet.getWorkbook(),rowIndex,colIndex,";",true);
                String values[] = (mapStyle.getCondnNum() == ConditionalFormatOperator.BETWEEN || mapStyle.getCondnNum() == ConditionalFormatOperator.NOT_BETWEEN) ? value.split(";") : new String[]{value};
                for(String str : values)
                {
                    varr.put(str);
                }
                obj.put(JSONConstants.CRITERIA_VALUE+"1", varr);
                obj.put(JSONConstants.CRITERIA_STYLE+"1", getCellStyle_ForMobile((CellStyle)mapStyle.getApplyStyle(), sheet.getWorkbook().getTheme()));
            }
            else
            {
                type = (cs instanceof ColorScale) ? JSONConstants.COLORSCALE : (cs instanceof IconSet) ? JSONConstants.ICONSET : JSONConstants.DATABAR;
                List<ConditionalFormatEntry> cfeList = cs.getConditionalStyleEntries();
                JSONObjectWrapper styleObj = new JSONObjectWrapper();
                for(int st = 1; st <= cfeList.size(); st++)
                {
                    ConditionalFormatEntry cse = cfeList.get(st-1);
                    styleObj.put(JSONConstants.CRITERIA_TYPE+st, cse.getType().name());
                    String value = cse.regenerateConditionalExpression(sheet.getWorkbook(), rowIndex, colIndex);
                    value = cse.hasFormula() ? "="+ value : value;
                    styleObj.put(JSONConstants.CRITERIA_VALUE+st, value);
                    switch (type)
                    {
                        case JSONConstants.COLORSCALE:
                            ZSColor color = ((ConditionalFormatEntry.ColorScaleObj)cse.getCSEObj()).getZSColor();
                            styleObj.put(JSONConstants.COLORSCALE_COLOR+st, ZSColor.getHexColor(color, sheet.getWorkbook().getTheme()));
                            break;
                        case JSONConstants.ICONSET:
                            if(st == 1)
                            {
                                obj.put(JSONConstants.REVERSE_ICON_ORDER, ((IconSet)cs).isReverseIconOder());
                                obj.put(JSONConstants.DEFAULT_ICON_SIZE, ((IconSet)cs).isDefaultIconSize());
                            }
                            styleObj.put(JSONConstants.ICON_NAME+st, ((ConditionalFormatEntry.IconSetObj)cse.getCSEObj()).getName());
                            styleObj.put(JSONConstants.ICON_ID+st, ((ConditionalFormatEntry.IconSetObj)cse.getCSEObj()).getId());
                            styleObj.put(JSONConstants.ICON_CRITERIA+st, ((ConditionalFormatEntry.IconSetObj)cse.getCSEObj()).getIconCriteria().name());
                            break;
                    }
                }
                if(type.equals(JSONConstants.DATABAR))
                {
                    DataBar dataBar = (DataBar)cs;

                    styleObj.put(JSONConstants.DBDIRECTION, dataBar.getDB_direction());
                    styleObj.put(JSONConstants.DBFILLTYPE, dataBar.getFill_type());
                    styleObj.put(JSONConstants.DBFILLPCOLOR, ZSColor.getHexColor(dataBar.getPositive_fill_color(), zsTheme));
                    styleObj.put(JSONConstants.DBFILLNCOLOR, ZSColor.getHexColor(dataBar.getNegative_fill_color(), zsTheme));
                    styleObj.put(JSONConstants.DBBORDERTYPE, dataBar.getBorder_type());
                    styleObj.put(JSONConstants.DBBORDERPCOLOR, ZSColor.getHexColor(dataBar.getPositive_border_color(), zsTheme));
                    styleObj.put(JSONConstants.DBBORDERNCOLOR, ZSColor.getHexColor(dataBar.getNegative_border_color(), zsTheme));
                    styleObj.put(JSONConstants.DBAXISTYPE, dataBar.getAxis_position());
                    styleObj.put(JSONConstants.DBAXISCOLOR, ZSColor.getHexColor(dataBar.getAxis_color(), zsTheme));
                }
                obj.put(JSONConstants.NOOFCOND, cfeList.size());
                obj.put(JSONConstants.CRITERIA_STYLE+"1", styleObj);
                obj.put(JSONConstants.AUTOTEXTCOLOR, cso.isChangeTextColor());
                obj.put(JSONConstants.HIDECELLCONTENT, cso.isHideText());
            }

            if(!obj.isEmpty())
            {
                obj.put(JSONConstants.SHEETLIST, ActionJsonUtil.addSheetInJsonArray(null, sheet.getAssociatedName(), true));
                obj.put(JSONConstants.ASSOCIATED_SHEET_NAME, sheet.getAssociatedName());
                obj.put(JSONConstants.CSUID, csId);
                obj.put(JSONConstants.DATARANGE, rangeName);
                obj.put(JSONConstants.CS_TYPE, type);
                jsonArray.put(obj);
            }
        }
    }


    public static String getCellStyle_ForMobile(CellStyle cStyle, ZSTheme theme) {
        String cstyle = "";
        if (cStyle != null) {

            ZSColor color;

            if((color = (ZSColor) cStyle.getProperty(CellStyle.Property.BACKGROUNDCOLOR)) != null){
                cstyle += ZSColor.getHexColor(color, theme) + "~"; //No I18N
            } else{
                cstyle += "none~";//No I18N
            }
            if ((color = (ZSColor) cStyle.getProperty(TextStyle.Property.COLOR)) != null) {
                cstyle += ZSColor.getHexColor(color, theme) + "~";//No I18N
            } else {
                cstyle += "none~";//No I18N
            }
            if (cStyle.getProperty(TextStyle.Property.FONTWEIGHT) == null || cStyle.getProperty(TextStyle.Property.FONTWEIGHT).equals("normal")) { //No I18N
                cstyle += "false~";//No I18N
            } else {
                cstyle += "true~";//No I18N
            }
            if (cStyle.getProperty(TextStyle.Property.FONTSTYLE) == null || cStyle.getProperty(TextStyle.Property.FONTSTYLE).equals("normal")) {//No I18N
                cstyle += "false~";//No I18N
            } else {
                cstyle += "true~";//No I18N
            }
            if (cStyle.getProperty(TextStyle.Property.TEXTLINETHROUGHSTYLE) == null || cStyle.getProperty(TextStyle.Property.TEXTLINETHROUGHSTYLE).equals("none")) {//No I18N
                cstyle += "none~"; //No I18N
            } else {
                cstyle += "single~";//No I18N
            }
            if (cStyle.getProperty(TextStyle.Property.TEXTUNDERLINESTYLE) == null || cStyle.getProperty(TextStyle.Property.TEXTUNDERLINESTYLE).equals("none")) {//No I18N
                cstyle += "false";//No I18N
            } else {
                cstyle += "true";//No I18N
            }
        } else {
            cstyle = "none~none~false~false~none~false";//No I18N
        }
        return cstyle;
    }
}
