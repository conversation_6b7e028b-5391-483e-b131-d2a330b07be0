//$Id$
/*
 * ScriptEventListener.java
 *
 * Created on April 28, 2011, 3:33 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;

import java.util.StringTokenizer;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ScriptEventListener implements Cloneable
{
    public static Logger logger = Logger.getLogger(ScriptEventListener.class.getName());
    /////////
    private String language = "ooo:script"; // script:language="ooo:script"
    private String eventName; // script:event-name="dom:mousedown"
    private String href; // xlink:href="vnd.sun.star.script:Standard.Module1.test?language=Basic&location=document"
    
    ////////////
    private String libraryName;
    private String moduleName;
    private String macroName;
    
    /** Creates a new instance of ScriptEventListener */
    // from parser
    public ScriptEventListener(String language, String eventName, String href)
    {
	setLanguage(language);
	setEventName(eventName);
	setHref(href);
    }
    
    public ScriptEventListener(String eventName, String libraryName, String moduleName, String macroName)
    {
	setEventName(eventName);
	setHref(libraryName, moduleName, macroName);
    }
    
    public String getLanguage()
    {
	return language;
    }
    
    public void setLanguage(String language)
    {
	this.language = language;
    }
    
    public String getEventName()
    {
	return eventName;
    }
    
    public void setEventName(String eventName)
    {
	this.eventName = eventName;
    }
    
    public String getHref()
    {
	return href;
    }
    
    public void setHref(String tempHref)
    {
	this.href = tempHref;
	try
	{
	    tempHref = tempHref.substring(tempHref.indexOf(":")+1);
	    tempHref = tempHref.substring(0, tempHref.indexOf("?"));
	    // tempHref will look like Standard.Module1.test
	    // Tokenize it
	    StringTokenizer stt = new StringTokenizer(tempHref, ".");
	    
	    int count = stt.countTokens();
	    switch(count)
	    {
		case 3:
		    libraryName = stt.nextToken();
		    moduleName = stt.nextToken();
		    macroName = stt.nextToken();
		    break;
		case 2:
		    libraryName = stt.nextToken();
		    moduleName = "";
		    macroName = stt.nextToken();
		    break;
	    }
	    
	}
	catch(Exception ex)
	{
	    logger.log(Level.INFO, ex.getMessage());
	}
    }
    
    public void setHref(String libraryName, String moduleName, String macroName)
    {
	this.libraryName = libraryName;
	this.moduleName = moduleName;
	this.macroName = macroName;
	
	this.href = "vnd.sun.star.script:"+libraryName+"."+moduleName+"."+macroName+"?language=Basic&location=document";
    }
    
    
    //////////////
    public String getLibraryName()
    {
	return libraryName;
    }
    public String getModuleName()
    {
	return moduleName;
    }
    
    public String getMacroName()
    {
	return macroName;
    }
    
    public String[] getAttributes()
    {
	String[] attrs = new String[]{"script:language",
	"script:event-name",
	"xlink:href"};
	return attrs;
    }

    public String[] getValues()
    {
	String[] values = new String[]{language,
	eventName,
	href};
	return values;
    }
    
    public ScriptEventListener clone()
    {
	ScriptEventListener o = null;
	try
	{
	    o = (ScriptEventListener) super.clone();
	}
	catch (CloneNotSupportedException e)
	{
	    logger.info("ScriptEventListener can't clone");
	}
	
	return o;
    }
}
