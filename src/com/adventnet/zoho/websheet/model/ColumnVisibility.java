/* $Id$ */
package com.adventnet.zoho.websheet.model;

/**
 * This class will have list of enumerations for column visibilities.
 * <ul>
 * <li>
 * {@code VISIBLE}
 * </li>
 * <li>
 * {@code COLLAPSE}
 * </li>
 * </ul>
 * 
 * <AUTHOR> N J ( ZT-0049 )
 *
 */
public enum ColumnVisibility
{
	VISIBLE(0, "visible"), //No I18N
	COLLAPSE(1, "collapse"); //No I18N
	
	private int index;
	private String text;
	
	ColumnVisibility(int index, String text)
	{
		this.index = index;
		this.text = text;
	}
	
	public int getInt()
	{
		return this.index;
	}
	
	@Override
	public String toString()
	{
		return this.text;
	}
	
	public static ColumnVisibility getColumnVisibility(int visibility)
	{
		for(ColumnVisibility cv : ColumnVisibility.values())
		{
			if(cv.getInt() == visibility)
			{
				return cv;
			}
		}
		return VISIBLE;
	}
	
	public static ColumnVisibility getColumnVisibility(String visibility)
	{
		for(ColumnVisibility cv : ColumnVisibility.values())
		{
			if(cv.toString().equalsIgnoreCase(visibility))
			{
				return cv;
			}
		}
		return VISIBLE;
	}
}
