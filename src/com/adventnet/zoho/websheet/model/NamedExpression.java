// $Id$
package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.ext.parser.ASTRangeNode;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.FormulaUtil;
import com.singularsys.jep.parser.ASTVarNode;
import com.singularsys.jep.parser.Node;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;


public class NamedExpression extends ExpressionImpl
{
	public static Logger logger = Logger.getLogger(NamedExpression.class.getName());
    public boolean isMarked = false;

    private String name;
    private String scopeASN = null;

    private String comment = null;

    //This constructor is to be used only from Parser.
    public NamedExpression(Workbook inWorkbook, String inName, String scopeASN, String inExpression, String comment, String inBaseCellAddress, boolean isRange, CellReference.ReferenceMode referenceMode)
    {
        super(ExpressionImpl.getInstanceFromXMLConditionFormula(inWorkbook, inExpression, inBaseCellAddress, referenceMode).getNode());
        this.name = inName;
        if(scopeASN != null){
            this.scopeASN = inWorkbook.getSheetByAssociatedName(scopeASN) != null ? scopeASN : null;
        }
        this.name = inName;
        this.comment = comment;
        //this.baseCellAddress = inBaseCellAddress;
    }

    public NamedExpression(String inName, String scopeASN, String inExpression, String comment, Sheet currSheet, int currRow, int currCol, CellReference.ReferenceMode referenceMode)
    {
        super(currSheet.getWorkbook(), inExpression.replace("\\", "\\\\"), currRow, currCol, true, referenceMode);
        this.name = inName;
        if(scopeASN != null){
            this.scopeASN = currSheet.getWorkbook().getSheetByAssociatedName(scopeASN) != null ? scopeASN : null;
        }
        this.comment = comment;
    }

    public NamedExpression(String name, Node node, String scopeASN, String comment){
        super(node);
        this.name= name;
        this.scopeASN = scopeASN;
        this.comment = comment;
    }

    public String getName()
    {
        return this.name;
    }

    @Override
    public String toString()
    {
        return this.name + " = " + super.toString();
    }

    private List<String> addDependentExpressionNames(Workbook workbook, List<String> dependentExpressionNames)
    {
        this.isMarked = true;
        for(NamedExpression nExp : workbook.getNamedExpressions())
        {
            if(nExp.isMarked)
            {
                continue;
            }

            if(FormulaUtil.isTreeContainsNamedExpression(nExp.getNode(), Arrays.asList(this.getName())))
            {
                dependentExpressionNames.add(nExp.getName());
                nExp.addDependentExpressionNames(workbook, dependentExpressionNames);
            }
        }
        this.isMarked = false;
        return dependentExpressionNames;
    }

    protected List<Cell> updateDependencies(Workbook workbook)
    {
        List<String> affectedExpressions = new ArrayList<>();
        affectedExpressions.add(this.getName());
        this.addDependentExpressionNames(workbook, affectedExpressions);

        List<Cell> cellList = new ArrayList<>();
        for(Sheet sheet : workbook.getSheetList())
	{
	    for(Cell cell : sheet.getFormulaCells())
	    {
                if(FormulaUtil.isTreeContainsNamedExpression(cell.getExpression().getNode(), affectedExpressions))
                {
                    ((CellImpl)cell).updateDependencies();
                    cellList.add(cell);
                }
	    }
	}
        
        return cellList;
    }

    public boolean isRange()
    {
        Node node = this.getNode();
        return CellUtil.isCellReferenceNode(node) || CellUtil.isRangeReferenceNode(node);
    }
    
    /**
     * @return the baseCell
     *
    public Cell getBaseCell()
    {
        //logger.info(this.getName()+" this.baseCellAddress >>> "+this.baseCellAddress);
        String sheetName = this.baseCellAddress.substring(0, this.baseCellAddress.lastIndexOf("."));
        if(sheetName.startsWith("$"))
        {
            sheetName = sheetName.substring(1); // lets trim the $
        }
        if(sheetName.indexOf("'") != -1)
        {
            sheetName = sheetName.replaceAll("'", "");
        }
        //logger.info("sheetName >>>>> "+sheetName);
        Sheet sheet = getWorkbook().getSheet(sheetName);
        Cell baseCell = sheet.getCell(CellUtil.getRow(this.baseCellAddress), CellUtil.getColumn(this.baseCellAddress));
        return baseCell;
    }
     */

    /**
     * @return the workbook
     *
    public Workbook getWorkbook()
    {
        return workbook;
    }
     */

    public String[] getAttributes()
    {
        String[] attrs =
        {
            "table:name",//No I18N
            "table:base-cell-address",//No I18N
            (isRange()) ? "table:cell-range-address" : "table:expression"//No I18N
        };

        return attrs;
    }

    public String[] getValues(Workbook workbook)
    {
        String[] values =
        {
            getName(),
            CellUtil.getCellReference(workbook.getSheet(0), 0, 0, true, true, true),
            FormulaUtil.getFormula(this, workbook)
        };

        return values;
    }

    public String[] getZSAttributes()
    {
        String[] attrs =
                {
                        "table:name",//No I18N
                        "table:sheet-scope", //No I18N
                        "table:comment", //No I18N
                        "table:base-cell-address",//No I18N
                        (isRange()) ? "table:cell-range-address" : "table:expression"//No I18N
                };

        return attrs;
    }

    public String[] getZSValues(Workbook workbook)
    {
        String[] values =
                {
                        getName(),
                        getScopeASN(),
                        getComment(),
                        CellUtil.getCellReference(workbook.getSheet(0), 0, 0, true, true, true),
                        FormulaUtil.getFormula(this, workbook)
                };

        return values;
    }

    // This method will take currentcell as argument when relative reference is handled.
    public static Range getRange(NamedExpression namedExpression, Sheet currSheet, int currRow, int currCol)
    {
        Range range = null;
        if (namedExpression.isRange())
        {
            try
            {
                Node o = namedExpression.getNode();
                if (o instanceof ASTRangeNode)
                {
                    range = CellUtil.getRange((ASTRangeNode) o, currSheet, currRow, currCol);
                } else
                {
                    CellReference cellRef = CellUtil.getCellRefFromVarNode((ASTVarNode) o, currSheet, currRow, currCol);
                    range = new Range(cellRef.getCell().getRow().getSheet(), cellRef, cellRef);
                }
            } catch (Exception e)
            {
                //return listVals;
                LOGGER.log(Level.WARNING, "Exception while getting the list range :", e.getMessage());
            }
        }
        return range;
    }

    @Override
    public NamedExpression clone()
    {
        NamedExpression o = (NamedExpression)super.clone();
        if(o != null)
        {
            o.name = this.name;
        }
	return o;
    }

    @Override
    public boolean equals(Object ne)
    {
        if(ne == null || !(ne instanceof NamedExpression))
        {
            return false;
        }

        return super.equals(ne) && this.name.equals(((NamedExpression)ne).getName()) && Objects.equals(this.scopeASN,((NamedExpression)ne).getScopeASN());
    }

    @Override
    public int hashCode()
    {
        int hash = 7;
        hash = 23 * super.hashCode();
        hash = 23 * hash + (this.name != null ? this.name.hashCode() : 0) + (this.scopeASN != null ? this.scopeASN.hashCode() : 0);
        return hash;
    }

    public String getScopeASN() {
        return this.scopeASN;
    }

    public String getComment() {
        return this.comment;
    }
}
