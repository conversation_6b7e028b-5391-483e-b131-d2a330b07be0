//$Id$

package com.adventnet.zoho.websheet.model;

/**
 * <AUTHOR>
 *
 */
import java.util.Stack;

public class TransposedZArrayI implements ZArrayI{

    private final ZArrayI zArrayI;
    
    public TransposedZArrayI(ZArrayI zArrayI) {
        this.zArrayI = zArrayI;
    }
    
    @Override
    public int getRowSize() {
        return zArrayI.getColSize();
    }
    
    @Override
    public int getColSize() {
        return zArrayI.getRowSize();
    }
    
    @Override
    public boolean isBound(int rowIndex, int colIndex) {
        return zArrayI.isBound(colIndex, rowIndex);
    }
    
    @Override
    public Value getValue(int rowIndex, int colIndex) {
        return zArrayI.getValue(colIndex, rowIndex);
    }

    @Override
    public Value getValueWithPattern(int rowIndex, int colIndex)
    {
        return zArrayI.getValueWithPattern(colIndex, rowIndex);
    }
    
    @Override
    public int getSize() {
        return zArrayI.getSize();
    }
    
    public ZArrayI getContaingZArrayI() {
        if(zArrayI instanceof TransposedZArrayI) {
            ((TransposedZArrayI) zArrayI).getContaingZArrayI();
        }
        return zArrayI;
    }

    @Override
    public void addArrayValues(Stack stack) {
        for(int i = 0; i < this.getRowSize(); i++)
        {
            for(int j = 0; j < this.getColSize(); j++)
            {
                Object value = getValue(i,j);
                stack.push(value);
            }
        }
        
    }
    
    @Override
    public int hashCode() {
        return 17 + this.zArrayI.hashCode() * this.getRowSize() * (this.getColSize() * 7);
    }
    
    @Override
    public boolean equals(Object obj) {
        if(this == obj) {
            return true;
        }
        if(obj == null || !(obj instanceof TransposedZArrayI)) {
            return false;
        }
        TransposedZArrayI tRange = (TransposedZArrayI) obj;
        if(!this.zArrayI.equals(tRange.zArrayI)) {
            return false;
        }
        return true;
    }
    
    @Override
    public String toString() {
        return "Transposed["+this.zArrayI.toString() + "]"; //No I18N
    }

    @Override
    public boolean isMadeofRange() {
        return this.zArrayI.isMadeofRange();
    }
}
