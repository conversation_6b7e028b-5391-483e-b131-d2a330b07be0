// $Id$
package com.adventnet.zoho.websheet.model.externalRange;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.DataRange;

import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExternalRangeSupplierResponse {
    private static final Logger LOGGER = Logger.getLogger(ExternalRangeSupplierResponse.class.getName());
    private final String supplierRsid;
    private final String taskId;
    private final boolean isError;
    private Map<DataRange, CompactZArray> externalRanges;

    private final Instant responseInitInstant;
    JSONArrayWrapper dataRangesArray;
    JSONObjectWrapper jsonObject;

    public ExternalRangeSupplierResponse(String supplierRsid, Map<DataRange, CompactZArray> externalRanges,
                                         JSONArrayWrapper dataRangesArray, JSONObjectWrapper jsonObject, String taskId, boolean isError) {
        this.supplierRsid = supplierRsid;
        this.externalRanges = externalRanges;
        this.responseInitInstant = Instant.now();
        this.dataRangesArray = dataRangesArray;
        this.jsonObject = jsonObject;
        this.taskId = taskId;
        this.isError = isError;
    }

    public String getSupplierRsid() {
        return supplierRsid;
    }

    public Map<DataRange, CompactZArray> getExternalRanges() {
        LOGGER.log(Level.OFF, "[IMPORTRANGE][PERFORMANCE-ANALYSIS][RESPONSE-APPLIED] taskId: {0}, Time taken:{1}ms", new Object[]{this.taskId,
                Duration.between(responseInitInstant, Instant.now()).toMillis()});
        if(this.externalRanges == null) {
            parse();
        }
        return externalRanges;
    }

    public void parse() {
        Instant conversionStartInstant = Instant.now();

        int countOfCells = 0;
        int countOfRanges = 0;

        externalRanges = new HashMap<>();
        if (jsonObject == null || dataRangesArray == null) {
            LOGGER.log(Level.OFF, "response null taskId:{0}, supplierRsid:{1}, jsonObject:{2}, dataRangesArray{3}", new Object[]{this.taskId, this.supplierRsid, this.jsonObject, this.dataRangesArray});
            return;
        }

        Iterator iterator = dataRangesArray.iterator();
        while (iterator.hasNext()) {
            JSONObjectWrapper next = new JSONObjectWrapper(iterator.next());
            JSONArrayWrapper jsonArray = null;
            if (jsonObject.has(next.toString())) {
                jsonArray = jsonObject.getJSONArray(next.toString());
            }

            DataRange dataRange = ExternalRangeUtils.parseDataRange(next);

            CompactZArray zArray = null;
            if (jsonArray != null) {
                try {
                    zArray = ExternalRangeUtils.convert(ExternalRangeUtils.uncompress(jsonArray), dataRange.getRowSize(), dataRange.getColSize(), this.supplierRsid, dataRange);
                } catch (Exception ex) {
                    LOGGER.log(Level.SEVERE, "[IMPORTRANGE] JSON to ZArray parsing failed!!!", ex);
                    LOGGER.log(Level.SEVERE, "[IMPORTRANGE] JSON to ZArray parsing failed!!! taskId:{3} Response rangesToBeFetched:{0}, supplierRsid:{1}, range:{2}",
                            new Object[]{ExternalRangeUtils.rangeJsonToString(dataRangesArray),
                                    supplierRsid, ExternalRangeUtils.rangeJsonToString(next), this.taskId});
                }
            }
            if (zArray == null) {
                zArray = new CompactZArray(Collections.singletonList( Value.getInstance(Cell.Type.ERROR, Cell.Error.REF)), 1, 1,1,1, supplierRsid, dataRange);
            }

            externalRanges.put(dataRange, zArray);

            countOfRanges++;
            countOfCells+=((zArray.getUsedRowSize()+1)* (zArray.getUsedColSize()+1));
        }

        long millis=Duration.between(conversionStartInstant, Instant.now()).toMillis();
        if(countOfCells > 200000 || countOfRanges > 100 || millis > 2000) {
            LOGGER.log(Level.WARNING, "[IMPORTRANGE]countOfCells:{0}, countOfRanges{1}, taskId:{2}, conversionTime:{3}", new Object[]{countOfCells, countOfRanges, this.taskId, millis});
        }

        LOGGER.log(Level.OFF, "[IMPORTRANGE][PERFORMANCE-ANALYSIS]Api-Call-And-Conversion Finished taskId:{0}, Supplier: {1}, JsonToZArrayConversion-Duration\u231B: {2}, rangesToBeFetched:{3}, countOfCells:{4}, countOfRanges{5}",
                new Object[]{this.taskId, this.supplierRsid, millis, ExternalRangeUtils.rangeJsonToString(dataRangesArray),countOfCells, countOfRanges});
    }

    public boolean isError(){
        return isError;
    }
}
