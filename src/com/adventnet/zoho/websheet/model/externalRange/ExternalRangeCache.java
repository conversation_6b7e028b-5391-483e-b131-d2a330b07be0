// $Id$
package com.adventnet.zoho.websheet.model.externalRange;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.functions.ImportRange;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.zoho.sheet.action.ExternalRangeAction;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExternalRangeCache {
    private Map<String, Map<DataRange, CompactZArray>> importRangeValueCache = new ConcurrentHashMap<>(); // {SUPPLIER_WORKBOOK_ID : {DataRange : ZArray}}
    private Map<String, Map<DataRange, Set<DataRange>>> importRangeDependents = new ConcurrentHashMap<>(); // {SUPPLIER_WORKBOOK_ID : {DataRange : Formula_Cells}}

    private Map<String, Set<DataRange>> importRangesToBeFetched = new ConcurrentHashMap<>(); // {SUPPLIER_WORKBOOK_ID : {DataRange : Formula_Cells}}

    private Map<String, Long> linkedSupplierWorkbooks = new ConcurrentHashMap<>();//{SupplierRSID, CreatedBy}
    private Set<String> referredUnlinkedSuppliers = ConcurrentHashMap.newKeySet();

    private final AtomicBoolean isExternalRangesChanged = new AtomicBoolean(false);//this will be set to true, if a new range is entered or if any existing range is effectively deleted (range has no dependents any more)
    private final AtomicBoolean isInitialFetchCompleted = new AtomicBoolean(false);

    private static final Logger LOGGER = Logger.getLogger(ExternalRangeCache.class.getName());
    private static final Map<String, Set<DataRange>> EMPTY_MAP = Collections.unmodifiableMap(new HashMap<>());
    private static final Set<String> EMPTY_SET = Collections.unmodifiableSet(new HashSet<>());

    private final Set<String> operationalSuppliers = new HashSet<>();

    public void addToImportRangesToBeFetched(String supplierId, Set<DataRange> dataRanges) {
        this.importRangesToBeFetched
                .computeIfAbsent(supplierId, s -> new HashSet<>())
                .addAll(dataRanges);
    }

    public void addToImportRangesToBeFetched(String supplierId, DataRange dataRange) {
        Set<DataRange> dataRangeSetMap = new HashSet<>();
        dataRangeSetMap.add(dataRange);
        addToImportRangesToBeFetched(supplierId, dataRangeSetMap);
    }

    public Map<String, Set<DataRange>> getImportRangesToBeFetchedAndClear() {
        if(this.importRangesToBeFetched.isEmpty()) {
            return EMPTY_MAP;
        }
        Map<String, Set<DataRange>> o = new HashMap<>();
        for (Map.Entry<String, Set<DataRange>> stringSetEntry : this.importRangesToBeFetched.entrySet()) {
            o.put(stringSetEntry.getKey(),new HashSet<>(stringSetEntry.getValue()));
        }
        this.importRangesToBeFetched.clear();
        return o;
    }

    public boolean hasImportRangesToBeFetched() {
        return !this.importRangesToBeFetched.isEmpty();
    }


    public Map<String, Map<DataRange, Set<DataRange>>> getImportRangeDependents() {
        return this.importRangeDependents;
    }

    public CompactZArray getExternalRange(String targetWorkbookIdentity, DataRange dataRange) {
        Map<DataRange, CompactZArray> dataRangeZArrayMap = this.importRangeValueCache.get(targetWorkbookIdentity);
        if(dataRangeZArrayMap == null) {
            return null;
        }
        return dataRangeZArrayMap.get(dataRange);
    }

    public void cacheExternalRange(String targetWorkbookIdentity, DataRange dataRange, CompactZArray zArray, boolean isUpdate) {
        if(!isUpdate) {
            this.importRangeValueCache
                .computeIfAbsent(targetWorkbookIdentity, k -> new ConcurrentHashMap<>())
                .put(dataRange, zArray);
            return;
        }

        Map<DataRange, CompactZArray> dataRangeZArrayMap = this.importRangeValueCache.get(targetWorkbookIdentity);
        if (dataRangeZArrayMap == null) {
            //precautionary
            dataRangeZArrayMap = new ConcurrentHashMap<>();
            this.importRangeValueCache.put(targetWorkbookIdentity, dataRangeZArrayMap);
            dataRangeZArrayMap.put(dataRange, zArray);
        } else {
            for (DataRange completeDataRange : dataRangeZArrayMap.keySet()) {
                CompactZArray oldZArray = dataRangeZArrayMap.get(completeDataRange);
                CompactZArray newZArray = ExternalRangeUtils.mergeZArrays(completeDataRange, oldZArray, dataRange, zArray);
                if (newZArray != null) {
                    dataRangeZArrayMap.put(completeDataRange, newZArray);
                }
            }
        }
    }

    public void addImportRangeDependent(String targetWorkbookIdentity, DataRange dataRange, Set<DataRange> newCells) {
        this.importRangeDependents
                .computeIfAbsent(targetWorkbookIdentity, k -> new ConcurrentHashMap<>())
                .computeIfAbsent(dataRange, k -> {
                    this.isExternalRangesChanged.compareAndSet(false, true);
                    return ConcurrentHashMap.newKeySet();})
                .addAll(newCells);
    }

    /**
     * removing the entries with Error Values, so they could be fetched again.
     * @return supplierWithEvalError -  supplier which throws #EVAL! should be checked for broken links
     */
    public Set<String> refresh() {
        if (!isInitialFetchCompleted.get()) {
            //if initial fetch is not completed yet, don't refresh the cache, this use case will trigger for pending actions.
            return EMPTY_SET;
        }
        if (this.importRangeValueCache.isEmpty()) {
            return EMPTY_SET;
        }
        Set<String> supplierWithEvalError = null;
        for (Map.Entry<String, Map<DataRange, CompactZArray>> supplier_ranges : this.importRangeValueCache.entrySet()) {
            String supplier = supplier_ranges.getKey();
            Map<DataRange, CompactZArray> ranges = supplier_ranges.getValue();
            List<DataRange> dataRangesNeedToBeRefreshed = new ArrayList<>();
            for (DataRange dataRange : ranges.keySet()) {
                CompactZArray zArray = ranges.get(dataRange);
                if(zArray != null) {
                    //zArray shouldn't be null , it's precautionary
                    if(zArray.getSyncState() == ImportRange.SYNC_STATE.TIMEOUT) {
                        dataRangesNeedToBeRefreshed.add(dataRange);
                    } else if(zArray.getSize() == 1 && zArray.getValue(0,0).getType() == Cell.Type.ERROR) {
                        dataRangesNeedToBeRefreshed.add(dataRange);
                        Object value1 = zArray.getValue(0, 0).getValue();
                        if (value1 instanceof Throwable) {
                            if (((Throwable)value1).getMessage().equals(Cell.Error.EVAL.getErrorString())) {
                                if (supplierWithEvalError == null) {
                                    supplierWithEvalError = new HashSet<>();
                                }
                                supplierWithEvalError.add(supplier);
                            }
                        }
                    }
                }
            }
            for (DataRange dataRange : dataRangesNeedToBeRefreshed) {
                ranges.remove(dataRange);
            }
        }
        if (supplierWithEvalError == null) {
            return EMPTY_SET;
        }
        return supplierWithEvalError;
    }

    public Set<String> removerErrorValues() {
        Set<String> supplierWithEvalError = new HashSet<>();
        if (!isInitialFetchCompleted.get()) {
            //if initial fetch is not completed yet, don't refresh the cache, this use case will trigger for pending actions.
            return supplierWithEvalError;
        }
        this.importRangeValueCache.forEach((supplier, ranges) -> {
            List<DataRange> dataRangesNeedToBeRefreshed = new ArrayList<>();
            for (DataRange dataRange : ranges.keySet()) {
                CompactZArray zArray = ranges.get(dataRange);
                if(zArray != null && zArray.getSize() == 1) {
                    Value value = zArray.getValue(0, 0);
                    if(value.getType() == Cell.Type.ERROR) {
                        //zArray shouldn't be null , it's precautionary
                        dataRangesNeedToBeRefreshed.add(dataRange);
                        switch((Cell.Error)value.getRawValue()) {
                            case TIMEOUT:
                            case EVAL:
                            case LOADING:
                                supplierWithEvalError.add(supplier);
                                break;
                        }
                    }
                }
            }
            for (DataRange dataRange : dataRangesNeedToBeRefreshed) {
                ranges.remove(dataRange);
            }
        });
        return supplierWithEvalError;
    }

    public CompactZArray getExternalRange(String targetWorkbookIdentity, DataRange dataRange, DataRange cell) {
        final HashSet<DataRange> dependentCells = new HashSet<>();
        if (cell != null) {
            dependentCells.add(cell);
        }
        this.operationalSuppliers.add(targetWorkbookIdentity);
        addImportRangeDependent(targetWorkbookIdentity, dataRange, dependentCells);

        if (!this.linkedSupplierWorkbooks.containsKey(targetWorkbookIdentity)) {
            if(cell != null) {
                this.referredUnlinkedSuppliers
                        .add(targetWorkbookIdentity);
            }

            return new CompactZArray(Collections.singletonList(Value.getInstance(Cell.Type.ERROR, Cell.Error.NOT_LINKED)), 1, 1,1,1, targetWorkbookIdentity, dataRange);
        }

        CompactZArray cachedValue = getExternalRange(targetWorkbookIdentity, dataRange);
        if (cachedValue != null) {
            return cachedValue;
        }

        CompactZArray zArrayLoading = new CompactZArray(Collections.singletonList(Value.getInstance(Cell.Type.ERROR, Cell.Error.LOADING)), 1, 1, 1, 1, targetWorkbookIdentity, dataRange);
        zArrayLoading.setSyncState(ImportRange.SYNC_STATE.FETCHING);
        cacheExternalRange(targetWorkbookIdentity, dataRange, zArrayLoading, false);

        addToImportRangesToBeFetched(targetWorkbookIdentity, dataRange);

        return zArrayLoading;
    }

    public Map<String, Long> getSuppliers() {
        return this.linkedSupplierWorkbooks;
    }

    public Set<String> getReferredUnlinkedSuppliers() {
        return this.referredUnlinkedSuppliers;
    }

    public void removeCachedValues(String supplier) {
        if (supplier == null) {
            return;
        }

        if(this.importRangeValueCache.containsKey(supplier)) {
            this.importRangeValueCache.remove(supplier);
        }
    }

    public JSONObjectWrapper getJSONObjectForOrderFile() {
        final JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        try {
            this.importRangeDependents.forEach((supplier, v) -> {
                JSONArrayWrapper jsonArray = new JSONArrayWrapper();
                v.forEach((dataRange, dependentCells) -> {
                    if (!dependentCells.isEmpty()) {
                        jsonArray.put(ExternalRangeUtils.toJSONObject(dataRange));
                    }
                });
                if (!jsonArray.isEmpty()) {
                    /**
                     * Unused External Ranges will get removed on second save.
                     */
                    jsonObject.put(supplier, jsonArray);
                }
            });
            if (jsonObject.isEmpty()) {
                return jsonObject;
            }
            JSONObjectWrapper jsonObject1 = new JSONObjectWrapper();
            jsonObject1.put(ExternalRangeAction.EXTERNAL_RANGES, jsonObject);
            return jsonObject1;
        } catch (Exception e) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE] problem writing external range json in order file", e);
            return jsonObject;
        }
    }

    private static Map<String, Set<DataRange>> parseJSONObject(JSONObjectWrapper jsonObject) {
        if (jsonObject == null || jsonObject.isEmpty()) {
            return new HashMap<>();
        }

        final Map<String, Set<DataRange>> result = new HashMap<>();

        final JSONObjectWrapper externalRanges = jsonObject.getJSONObject(ExternalRangeAction.EXTERNAL_RANGES);
        final Iterator supplierIterator = externalRanges.keys();
        while (supplierIterator.hasNext()) {
            String supplier = (String) supplierIterator.next();
            final Iterator dataRanges = externalRanges.getJSONArray(supplier).iterator();
            Set<DataRange> dataRangesResult = new HashSet<>();
            while (dataRanges.hasNext()) {
                final JSONObjectWrapper dataRange = new JSONObjectWrapper(dataRanges.next());
                dataRangesResult.add(ExternalRangeUtils.parseDataRange(dataRange));
            }
            result.put(supplier, dataRangesResult);
        }
        return result;
    }

    /**
     * side-effect : removes external ranges which doesn't have any dependents.
     * @return
     */
    public boolean isExternalRangesChanged() {
        boolean changed = false;
        try {
            for (String supplier : this.importRangeDependents.keySet()) {
                final Map<DataRange, Set<DataRange>> externalRangeAndDependents = this.importRangeDependents.get(supplier);
                for (DataRange externalRange : externalRangeAndDependents.keySet()) {
                    if (externalRangeAndDependents.get(externalRange).isEmpty()) {
                        changed = true;
                        externalRangeAndDependents.remove(externalRange);//removing the externalRangeWhichDoesn't have any dependents
                    }
                }
            }

            if (changed) {
                return true;
            }

            if (this.isExternalRangesChanged.get()) {
                //this check should be later, as not referred external ranges are being removed above.
                return true;
            }
        } catch (Exception e) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE] problem writing external range json in order file", e);
            return true;
        }
        return false;
    }

    public void addExternalRangesToBeFetchedForStartupSync(String rsid, JSONObjectWrapper externalRangeJSON) {
        try {
//            LOGGER.log(Level.OFF, "[IMPORTRANGE] starting initial fetch for consumer {0}, getting the external ranges from order.json", new Object[]{resourceId});
            final Map<String, Set<DataRange>> externalRange = ExternalRangeCache.parseJSONObject(externalRangeJSON);
            if(externalRange.isEmpty()) {
                return;
            }
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(rsid).append(",");
            externalRange.forEach((targetWorkbookIdentity, v) -> {
                int count_range = 0;
                int count_cell = 0;
                for (DataRange dataRange : v) {
                    count_range++;
                    count_cell+=dataRange.getSize();
                    getExternalRange(targetWorkbookIdentity, dataRange, null);//this will update importRangeToBeFetched, add EVAL error as value to corresponding dataRange, and initialize empty dependents.
                }
                stringBuilder.append(targetWorkbookIdentity).append(" ranges:").append(count_range).append(" cells:").append(count_cell).append(";");
            });
            LOGGER.log(Level.OFF, stringBuilder.toString());
            this.isExternalRangesChanged.set(false);//resetting it to false, as it shouldn't be true for parsing.
            //this will not do anything if there are no externalRange To Fetch, but if there are it will start the fetch, and rest the fetching list
        } catch (Exception e) {
            LOGGER.log(Level.OFF, String.join("","[IMPORTRANGE] starting initial fetch for consumer ",rsid,", using order.json failed"), e);
        }
    }

    public AtomicBoolean getIsInitialFetchCompleted() {
        return isInitialFetchCompleted;
    }

    public void dismantle() {
        importRangeValueCache = null;
        importRangeDependents = null;
        importRangesToBeFetched = null;
        linkedSupplierWorkbooks = null;
        referredUnlinkedSuppliers = null;
    }

    public String rangesHavingErrorValue() {
        StringJoiner stringBuilder = new StringJoiner(",");
        try {
            this.importRangeValueCache.forEach((supplier, ranges) -> {
                for (DataRange dataRange : ranges.keySet()) {
                    CompactZArray zArray = ranges.get(dataRange);
                    if (zArray != null && zArray.getSize() == 1) {
                        Value value = zArray.getValue(0, 0);
                        if (value.getType() == Cell.Type.ERROR) {
                            stringBuilder.add(dataRange.toString());
                        }
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.log(Level.OFF, "", e);
        }
        return stringBuilder.toString();
    }

    public Set<String> getReferredLinksInCell(Cell cell) {
        Set<String> result = new HashSet<>();
        DataRange asDataRange = ExternalRangeUtils.getAsDataRange(cell);
        for(Map.Entry<String, Map<DataRange, Set<DataRange>>> rsid_externalRangeDependents : this.importRangeDependents.entrySet()) {
            for(Map.Entry<DataRange, Set<DataRange>> externalRange_Dependents : rsid_externalRangeDependents.getValue().entrySet()) {
                if(externalRange_Dependents.getValue().contains(asDataRange)) {
                    result.add(rsid_externalRangeDependents.getKey());
                }
            }
        }
        return result;
    }

    public Set<String> getOperationalSuppliers() {
        return this.operationalSuppliers;
    }

    public void clearOperationalSupplierSet() {
        this.operationalSuppliers.clear();
    }
}
