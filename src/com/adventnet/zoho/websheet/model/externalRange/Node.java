// $Id$
package com.adventnet.zoho.websheet.model.externalRange;

import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.zoho.zfsng.client.ZohoFS;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class Node implements Cloneable {
    private static final Logger LOGGER = Logger.getLogger(Node.class.getName());
    private final String rsid;
    private Set<Node> suppliers = new HashSet<>();
    private String docOwner;
    private boolean isLive = false;
    private Long creator;

    public Node(String rsid, HashMap<String, Node> cache, String documentOwner) {
        this.rsid = rsid;
        this.docOwner = documentOwner;

        if(cache != null && !cache.isEmpty()) {
            //to avoid fetching docOwner again.
            Node node = cache.get(this.rsid);
            if(node != null) {
                this.docOwner = this.docOwner == null ? node.docOwner : this.docOwner;
                this.isLive = node.isLive;
                this.creator = node.creator;
            }
        }

        if(this.docOwner == null){
            this.docOwner = getDocumentOwner(this.rsid);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        Node node = (Node) o;
        return rsid.equals(node.rsid);
    }


    @Override
    public int hashCode() {
        return Objects.hash(rsid);
    }

    public String getRsid() {
        return rsid;
    }

    public Set<Node> getSuppliers() {
        return suppliers;
    }

    public void setSuppliers(Set<Node> suppliers) {
        this.suppliers = suppliers;
    }

    public String getDocOwner() {
        return docOwner;
    }

    public void setDocOwner(String docOwner) {
        this.docOwner = docOwner;
    }

    public boolean isLive() {
        return isLive;
    }

    public void setLive(boolean live) {
        isLive = live;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    @Override
    public Node clone() throws CloneNotSupportedException {
        Node clone = (Node) super.clone();
        clone.suppliers = new HashSet<>();
        return clone;
    }

    @Override
    public String toString() {
        StringJoiner stringJoiner = new StringJoiner(",", "{", "}");
        stringJoiner.add(rsid).add(docOwner).add(String.valueOf(isLive)).add(String.valueOf(creator));
        return stringJoiner.toString();
    }

    static String getSpaceID(String rsid) {
        if (rsid == null) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE]rsid null");
            return null;
        }

        try {
            String spaceId = ZohoFS.getSpaceId(rsid);
            if (spaceId == null) {
                LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK] (RSID: {0}) - Exception while getting SpaceID. SpaceID is null", new Object[]{rsid});
            }
            return spaceId;
        } catch (Exception exception) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK]Exception in getting SpaceID "+rsid,exception);
        }
        return null;
    }

    private static String getDocumentOwner(String rsid) {
//        if(true) {
//            return "test_owner";
//        }
        if (rsid == null) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE]rsid null");
            return null;
        }

        String spaceId = getSpaceID(rsid);
        if(spaceId == null) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE]spaceId null {0}", new Object[]{rsid});
            return null;
        }
        try {
            String owner = DocumentUtils.getZUserName(spaceId);
            if(owner == null) {
                LOGGER.log(Level.OFF, "[IMPORTRANGE] Exception in getting DocOwner. rsid: {0}, spaceId: {1}", new Object[]{rsid, spaceId});
            }
            return owner;
        } catch (Exception e) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE] Exception in getting DocOwner. rsid: "+rsid+",spaceId: "+spaceId, e);
        }
        return null;
    }
}
