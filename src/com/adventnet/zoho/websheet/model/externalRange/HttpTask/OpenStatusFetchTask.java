// $Id$
package com.adventnet.zoho.websheet.model.externalRange.HttpTask;

import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.externalRange.ExternalRangeManager;
import com.adventnet.zoho.websheet.model.externalRange.ExternalRangeUtils;
import com.adventnet.zoho.websheet.model.externalRange.HttpTaskRequest.HttpTaskRequest;
import com.adventnet.zoho.websheet.model.externalRange.HttpTaskRequest.OpenStatusFetch;
import com.adventnet.zoho.websheet.model.externalRange.WorkbookLink;
import com.adventnet.zoho.websheet.model.util.DataAPIConstants;
import com.zoho.sheet.util.ContainerSynchronizer;

import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;
import java.util.logging.Level;
import java.util.logging.Logger;

class OpenStatusFetchTask implements HttpTask{
    private static final Logger LOGGER = Logger.getLogger(OpenStatusFetchTask.class.getName());
    private final OpenStatusFetch openStatusFetch;
    OpenStatusFetchTask(OpenStatusFetch openStatusFetch) {
        this.openStatusFetch = openStatusFetch;
    }

    private static boolean isWorkbookOpenNetworkCall(String rsid, String taskId) {
        StringJoiner postRequestArguments = new StringJoiner("&");
        postRequestArguments
                .add(ExternalRangeUtils.KEY_PROXYURL+"="+ WorkbookLink.IS_CONTAINER_LIVE_INTERNAL)//No I18N
                .add(HttpTaskManager.TASK_ID_KEY+"="+taskId);//No I18N
        try {
            postRequestArguments.add("iscsignature" + "=" + SecurityUtil.sign());//No I18N

            JSONObjectWrapper resp = new JSONObjectWrapper(ContainerSynchronizer.postURLConnectionForImportrange(ExternalRangeUtils.EXTERNAL_RANGE_API_HOSTNAME, rsid, postRequestArguments.toString(), null, UserProfile.AccessType.AUTH, false, null, 200, 700));
            if(!resp.has(DataAPIConstants.RESPONSE_BODY)) {
                LOGGER.log(Level.SEVERE, "[IMPORTRANGE][WORKBOOKLINK][Exception] is_container_live_internal - no response body [Supplier Id: {0}, Response: {1}, taskId:{2}]", new Object[]{rsid, resp.toString(),taskId});
                return false;
            }

            try {
                JSONObjectWrapper responseBody = resp.getJSONObject(DataAPIConstants.RESPONSE_BODY);
                if(!responseBody.has(WorkbookLink.IS_CONTAINER_LIVE_INTERNAL_RESPONSE_KEY_ICL)) {
                    LOGGER.log(Level.SEVERE, "[IMPORTRANGE][WORKBOOKLINK][Exception] is_container_live_internal - incorrect response [Supplier Id: {0}, Response: {1}, taskId:{2}]", new Object[]{rsid, resp.toString(),taskId});
                    return false;
                }
                return responseBody.getBoolean(WorkbookLink.IS_CONTAINER_LIVE_INTERNAL_RESPONSE_KEY_ICL);
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[IMPORTRANGE][WORKBOOKLINK][Exception] is_container_live_internal - incorrect response [Supplier Id: {0}, Response: {1}, taskId:{2}]", new Object[]{rsid, resp.toString(),taskId});
            }
        } catch (Exception exception){
            LOGGER.log(Level.OFF,HttpTaskManager.lMgJ("[IMPORTRANGE][WORKBOOKLINK] is_container_live_internal - exception in http call","rsid",rsid,"taskId",taskId),exception);
        }
        return false;
    }

    @Override
    public void process() {
        Map<String, Boolean> result = new HashMap<>();
        this.openStatusFetch.getConsumers().remove(this.openStatusFetch.getRsid());

        for (String consumer : this.openStatusFetch.getConsumers()) {
            result.put(consumer, isWorkbookOpenNetworkCall(consumer, this.openStatusFetch.getTaskId()));
        }

        if(this.openStatusFetch.isBroadcastOpen()) {
            for (Map.Entry<String, Boolean> stringBooleanEntry : result.entrySet()) {
                if(stringBooleanEntry.getValue()) {
                    WorkbookLink.broadcastHttpRequest(this.openStatusFetch.getRsid(), WorkbookLink.BROADCASTER_ACTION.OPEN, WorkbookLink.BROADCASTER_RELATIONSHIP.CONSUMER, stringBooleanEntry.getKey(), this.openStatusFetch.getTaskId());
                }
            }
        }

        WorkbookContainer container=this.openStatusFetch.getContainer();
        if(container == null){
            LOGGER.log(Level.WARNING, "sync of live stats failed because container is not available {0}, taskId:{1}", new Object[]{this.openStatusFetch.getRsid(), openStatusFetch.getTaskId()});
        } else {
            ExternalRangeManager externalRangeManager=container.getExternalRangeManager();
            if(externalRangeManager!=null){
                WorkbookLink workbookLink=externalRangeManager.getWorkbookLink();
                workbookLink.syncLiveStats(this.openStatusFetch.getRsid(), result);
            }else{
                LOGGER.log(Level.WARNING, "sync of live stats failed because container is dismantled {0}, taskId:{1}", new Object[]{this.openStatusFetch.getRsid(), openStatusFetch.getTaskId()});
            }
        }
    }

    @Override
    public HttpTaskRequest getHttpTaskRequest() {
        return openStatusFetch;
    }
}
