// $Id$
package com.adventnet.zoho.websheet.model.externalRange.HttpTask;

import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.externalRange.ExternalRangeUtils;
import com.adventnet.zoho.websheet.model.externalRange.HttpTaskRequest.Fetch;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Level;
import java.util.logging.Logger;

public class HttpDelayScheduler {
    private static final Logger LOGGER = Logger.getLogger(HttpTaskManager.class.getName());
    private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE = Executors.newSingleThreadScheduledExecutor();
    private static final Map<String, HttpDelayEntry> FETCH_REQUESTS = new ConcurrentHashMap<>();

    static {
        SCHEDULED_EXECUTOR_SERVICE.scheduleWithFixedDelay(() -> playTasks(), 10, 2, TimeUnit.SECONDS);
    }

    public static void playTasks() {
        try {
            Set<String> set = new HashSet<>(FETCH_REQUESTS.keySet());
            if(set.size() > 50) {
                LOGGER.log(Level.OFF, "too many requests pending :{0}", set.size());
            }
            for(String key : set) {
                HttpDelayEntry httpDelayEntry = FETCH_REQUESTS.get(key);
                Fetch next = httpDelayEntry.next();
                if(next != null) {
                    HttpTaskManager.add(next);
                }
                if(httpDelayEntry.isRemove()) {
                    FETCH_REQUESTS.remove(key);
                }
            }
        } catch(Exception e) {
            LOGGER.log(Level.OFF, "", e);
        }
    }

    public static void add(Fetch req) {
        HttpDelayEntry httpDelayEntry = FETCH_REQUESTS.get(req.getRsid());
        if(httpDelayEntry == null) {
            httpDelayEntry = new HttpDelayEntry(5);
        }
        httpDelayEntry.add(req);
        FETCH_REQUESTS.put(req.getRsid(), httpDelayEntry);
    }

    public static void resetNextFetchInstant(Fetch req) {
        if(!req.isDelayedFetch()) {
            return;
        }
        HttpDelayEntry httpDelayEntry = FETCH_REQUESTS.get(req.getRsid());
        if(httpDelayEntry != null) {
            httpDelayEntry.resetNextFetchInstant();
        }
    }

    private static class HttpDelayEntry {
        private Instant nextFetchInstant;
        private List<Fetch> fetches = new ArrayList<>();
        private ReentrantLock lock = new ReentrantLock();

        private final int delayInSeconds;

        private HttpDelayEntry(int delayInSeconds) {
            this.delayInSeconds = delayInSeconds;
            nextFetchInstant = Instant.now().plusSeconds(delayInSeconds);
        }


        private Fetch next() {
            try {
                Instant lock_tried = Instant.now();
                lock.lock();
                Instant now = Instant.now();
                if(Duration.between(lock_tried, now).getSeconds()>1) {
                    LOGGER.log(Level.OFF, "taking too much time");
                }
                int size = fetches.size();
                if(size == 0) {
                    return null;
                }
                if(size > 20) {
                    LOGGER.log(Level.OFF, "too many requests pending :{0}", size);
                }
                if(now.isBefore(nextFetchInstant)) {
                    return null;
                }
                //start
                /*
                task should complete in 60sec and reset the nextFetchInstant
                other task will not wait more than 60sec for the current task to complete.

                if nextFetchInstant is missed to reset after current task finishes, each next task will start after 60sec of current task.
                 */
                nextFetchInstant = now.plusSeconds(60);
                //end

                return fetches.remove(0);
            } finally {
                lock.unlock();
            }
        }

        private boolean isRemove() {
            try {
                Instant lock_tried = Instant.now();
                lock.lock();
                Instant now = Instant.now();
                if(Duration.between(lock_tried, now).getSeconds()>1) {
                    LOGGER.log(Level.OFF, "taking too much time");
                }
                if(fetches.isEmpty() && now.isAfter(this.nextFetchInstant)) {
                    /*
                    Instant.now().isAfter(this.instant)
                    HttpDelayAgent should not be removed until the last fetch is not finished.
                    It will be removed after 5 sec when the last fetch finishes.
                     */
                    return true;
                }
                return false;
            } finally {
                lock.unlock();
            }
        }

        private void add(Fetch newFetch) {
            try {
                Instant lock_tried = Instant.now();
                lock.lock();
                if(Duration.between(lock_tried, Instant.now()).getSeconds()>1) {
                    LOGGER.log(Level.OFF, "taking too much time");
                }
                newFetch.setDelayedFetch(true);

                boolean needToBeAdded = true;
                for(Fetch existingFetch : this.fetches) {
                    JSONObjectWrapper newGridAction = newFetch.getGridAction();
                    JSONObjectWrapper existingGridAction = existingFetch.getGridAction();
                    if(newGridAction == null && existingGridAction == null) {
                        existingFetch.mergeAndupdateExternalRangeSuppliers(newFetch.getExternalRangeSuppliers());
                        needToBeAdded = false;
                        break;
                    } else if(existingGridAction != null && newGridAction != null) {
                        JSONArrayWrapper existingJsonArray = existingGridAction.optJSONArray(ExternalRangeUtils.NOTIFICATION_SUPPLY_CHAIN);
                        JSONArrayWrapper newJsonArray = newGridAction.optJSONArray(ExternalRangeUtils.NOTIFICATION_SUPPLY_CHAIN);
                        if(existingJsonArray != null && newJsonArray != null) {
                            if(existingJsonArray.toString().equals(newJsonArray.toString())) {
                                existingFetch.mergeAndupdateExternalRangeSuppliers(newFetch.getExternalRangeSuppliers());
                                needToBeAdded = false;
                                break;
                            }
                        }
                    }
                }
                if(needToBeAdded) {
                    fetches.add(newFetch);
                }
            } finally {
                lock.unlock();
            }
        }

        private void resetNextFetchInstant() {
            try {
                Instant lock_tried = Instant.now();
                lock.lock();
                Instant now = Instant.now();
                if(Duration.between(lock_tried, now).getSeconds()>1) {
                    LOGGER.log(Level.OFF, "taking too much time");
                }
                nextFetchInstant = now.plusSeconds(delayInSeconds);
            } finally {
                lock.unlock();
            }
        }

    }
}
