// $Id$
package com.adventnet.zoho.websheet.model.externalRange.HttpTaskRequest;

import com.adventnet.zoho.websheet.model.externalRange.ExternalRangeUtils;
import com.adventnet.zoho.websheet.model.externalRange.HttpTask.HttpTaskManager;
import com.adventnet.zoho.websheet.model.util.DataRange;

import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.StringJoiner;
import java.util.logging.Level;

public class NotifyChange extends HttpTaskRequest {
    private List<NotifyChangeBean> list;
    private List<String> notificationSupplyChain;

    public static class NotifyChangeBean {
        private final String consumerRsid;
        private final Set<DataRange> dataRanges;
        public NotifyChangeBean(String consumerRsid, Set<DataRange> dataRanges) {
            this.consumerRsid = consumerRsid;
            this.dataRanges = dataRanges;
        }

        public String getConsumerRsid() {
            return consumerRsid;
        }
        public Set<DataRange> getDataRanges() {return this.dataRanges;}

        @Override
        public String toString() {
            StringJoiner stringJoiner = new StringJoiner(",","{","}");
            stringJoiner.add("consumer:"+ ExternalRangeUtils.last4Chars(consumerRsid));
            StringJoiner stringJoiner1 = new StringJoiner(",","{","}");
            for (DataRange dataRange : dataRanges) {
                stringJoiner1.add(dataRange.toString());
            }
            stringJoiner.add(stringJoiner1.toString());
            return stringJoiner.toString();
        }
    }

    public NotifyChange(String rsid, String taskId) {
        super(rsid, taskId);
    }

    @Override
    public void log() {
        StringJoiner stringJoiner = new StringJoiner(",","{","}");
        for (NotifyChangeBean s : this.list) {
            stringJoiner.add(s.toString());
        }
        LOGGER.log(Level.OFF, "[IMPORTRANGE]rsid:{0},taskId:{1},chain:{2},:{3}", new Object[]{getRsid(), getTaskId(),ExternalRangeUtils.last4Chars(notificationSupplyChain),stringJoiner.toString()});
    }

    @Override
    public void endLog(Duration wait, Duration finish, int pendingTasks) {
        long time=HttpTaskManager.getDuration(getTaskId());
        LOGGER.log(Level.OFF, HttpTaskManager.lMgJ("[IMPORTRANGE]",//No I18N
                "rsid",getRsid(),//No I18N
                "taskId",getTaskId(),//No I18N
                "wait",getDurationSlab(wait),"",wait.toMillis(),//No I18N
                "finish",finish.toMillis(),//No I18N
                "time",getDurationSlab(time),"",time,//No I18N
                "pendingTasks",getThreadPoolTasksSlab(pendingTasks),"",pendingTasks//No I18N
                ));
    }

    public List<NotifyChangeBean> getList() {
        return list;
    }

    public List<String> getNotificationSupplyChain() {
        return notificationSupplyChain;
    }

    public NotifyChange setNotifyChangeBeanList(List<NotifyChangeBean> list) {
        this.list = list;
        return this;
    }

    public NotifyChange setNotificationSupplyChain(List<String> notificationSupplyChain) {
        this.notificationSupplyChain = notificationSupplyChain;
        return this;
    }
}
