// $Id$
package com.adventnet.zoho.websheet.model.externalRange.HttpTaskRequest;

import com.adventnet.zoho.websheet.model.externalRange.ExternalRangeUtils;
import com.adventnet.zoho.websheet.model.externalRange.HttpTask.HttpTaskManager;

import java.time.Duration;
import java.util.Set;
import java.util.logging.Level;

public class NotifyClose extends HttpTaskRequest {
    private Set<String> suppliers;

    public NotifyClose(String rsid, String taskId) {
        super(rsid, taskId);
    }

    @Override
    public void log() {
        LOGGER.log(Level.OFF, "[IMPORTRANGE]rsid:{0},taskId:{1},:{2}", new Object[]{getRsid(), getTaskId(), ExternalRangeUtils.last4Chars(this.suppliers)});
    }

    @Override
    public void endLog(Duration wait, Duration finish, int pendingTasks) {
        long time=HttpTaskManager.getDuration(getTaskId());
        LOGGER.log(Level.OFF, HttpTaskManager.lMgJ("[IMPORTRANGE]",//No I18N
                "rsid",getRsid(),//No I18N
                "taskId",getTaskId(),//No I18N
                "wait",getDurationSlab(wait),"",wait.toMillis(),//No I18N
                "finish",finish.toMillis(),//No I18N
                "time",getDurationSlab(time),"",time,//No I18N
                "pendingTasks",getThreadPoolTasksSlab(pendingTasks),"",pendingTasks//No I18N
                ));
    }

    public Set<String> getSuppliers() {
        return suppliers;
    }

    public NotifyClose setSuppliers(Set<String> suppliers) {
        this.suppliers = suppliers;
        return this;
    }
}
