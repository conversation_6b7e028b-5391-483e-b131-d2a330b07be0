// $Id$
package com.adventnet.zoho.websheet.model.externalRange.HttpTaskRequest;

import com.adventnet.zoho.websheet.model.externalRange.ExternalRangeUtils;
import com.adventnet.zoho.websheet.model.externalRange.HttpTask.HttpTaskManager;
import com.adventnet.zoho.websheet.model.externalRange.WorkbookLink;

import java.time.Duration;
import java.util.Set;
import java.util.logging.Level;

public class NotifyReset extends HttpTaskRequest {
    private WorkbookLink.BROADCASTER_ACTION broadcasterAction;
    private WorkbookLink.BROADCASTER_RELATIONSHIP relationship;
    private Set<String> receivers;

    public NotifyReset(String rsid, String taskId) {
        super(rsid, taskId);
    }

    @Override
    public void log() {
        LOGGER.log(Level.OFF, "[IMPORTRANGE]rsid:{0},taskId:{1},action:{2},:{3}", new Object[]{getRsid(), getTaskId(),broadcasterAction.toString(), ExternalRangeUtils.last4Chars(receivers)});
    }

    @Override
    public void endLog(Duration wait, Duration finish, int pendingTasks) {
        long time=HttpTaskManager.getDuration(getTaskId());
        LOGGER.log(Level.OFF, HttpTaskManager.lMgJ("[IMPORTRANGE]",//No I18N
                "rsid",getRsid(),//No I18N
                "taskId",getTaskId(),//No I18N
                "wait",getDurationSlab(wait),"",wait.toMillis(),//No I18N
                "finish",finish.toMillis(),//No I18N
                "time",getDurationSlab(time),"",time,//No I18N
                "pendingTasks",getThreadPoolTasksSlab(pendingTasks),"",pendingTasks//No I18N
                ));
    }

    public WorkbookLink.BROADCASTER_ACTION getBroadcasterAction() {
        return broadcasterAction;
    }

    public WorkbookLink.BROADCASTER_RELATIONSHIP getRelationship() {
        return relationship;
    }

    public Set<String> getReceivers() {
        return receivers;
    }

    public NotifyReset setBroadcasterAction(WorkbookLink.BROADCASTER_ACTION broadcasterAction) {
        this.broadcasterAction = broadcasterAction;
        return this;
    }

    public NotifyReset setRelationship(WorkbookLink.BROADCASTER_RELATIONSHIP relationship) {
        this.relationship = relationship;
        return this;
    }

    public NotifyReset setReceivers(Set<String> receivers) {
        this.receivers = receivers;
        return this;
    }
}
