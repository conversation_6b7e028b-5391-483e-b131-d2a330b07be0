package com.adventnet.zoho.websheet.model.externalRange;

import com.adventnet.ds.query.*;
import com.adventnet.persistence.DataAccessException;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.externalRange.HttpTask.HttpTaskManager;
import com.adventnet.zoho.websheet.model.externalRange.HttpTaskRequest.NotifyReset;
import com.adventnet.zoho.websheet.model.externalRange.HttpTaskRequest.OpenStatusFetch;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.DCMigrationUtil;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.zoho.sheet.action.ActionObject;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.sheet.zuidMigration.helper.ZUIDMigrationHelper;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ResourceStatus;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.SocketTimeoutException;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class WorkbookLink {
    public static final String BROADCASTER_RSID = "bro_rsid";//No I18N
    private static final String BROADCASTER_ACTION_KEY = "bro_act";//No I18N
    private static final String BROADCASTER_RELATIONSHIP_KEY = "bro_rel";//No I18N
    private static final String BROADCASTER_SUPPLIER_RSID = "bro_supplier_rsid";//No I18N

    public static final String IS_CONTAINER_LIVE_INTERNAL = "is_container_live_internal";//No I18N
    public static final String IS_CONTAINER_LIVE_INTERNAL_RESPONSE_KEY_ICL = "icl";//No I18N
    private static final Logger LOGGER = Logger.getLogger(WorkbookLink.class.getName());

//    static {
//        //test_debug
//        AppResources.setProperty("server.home", "/Users/<USER>/ZohoSheet/Default/builds/latest/AdventNet/Sas/tomcat/webapps/ROOT/WEB-INF");
//    }

    private static final int NO_OF_ALLOWED_API_CALLS = 100;

    private boolean isInitializationDone = false;

    private Map<String, Node> nodes = new ConcurrentHashMap<>();
    private Set<String> effectivelyLiveConsumerContainers = ConcurrentHashMap.newKeySet();

    public WorkbookLink() {
    }

    public static void broadcastHttpRequest(String broadcasterRSID, BROADCASTER_ACTION broadcasterAction, BROADCASTER_RELATIONSHIP relationship, String receiverRSID, String taskId) {
        //todo immediate supplier need not to be notified on SUPPLIER_ADD and SUPPLIER_REMOVE as they can infer that from WEBHOOK_SUBSCRIBE and WEBHOOK_UNSUBSCRIBE
//        LOGGER.log(Level.OFF, ExternalRangeUtils.importrangeLogMessageBuilder(broadcasterRSID, receiverRSID, null, broadcasterAction.toString()).toString());
        JSONObjectWrapper payloadObj = ActionObject.getActionInfo(ActionConstants.WORKBOOKLINK_BROADCAST).toJSON();
        payloadObj.put(BROADCASTER_RSID, broadcasterRSID);
        payloadObj.put(BROADCASTER_ACTION_KEY, broadcasterAction.name());
        payloadObj.put(BROADCASTER_RELATIONSHIP_KEY, relationship.name());

        ExternalRangeUtils.addCommonAttributesForInternalDataApiCall(payloadObj, ExternalRangeUtils.METHOD_LINKEDWORKBOOK_BROADCAST);

        try {
            ExternalRangeUtils.internalDataApiCall(payloadObj, receiverRSID, 300, 700, taskId);
        } catch (SocketTimeoutException e) {
            LOGGER.log(Level.OFF, HttpTaskManager.lMgJ("[IMPORTRANGE][WORKBOOKLINK] broadcastHttpRequest Failed!","taskId",taskId),e);
        } catch (Exception e) {
            LOGGER.log(Level.OFF, HttpTaskManager.lMgJ("[IMPORTRANGE][WORKBOOKLINK] broadcastHttpRequest Failed!","taskId",taskId),e);
        }

    }

    public static Map<String, Long> getAllConsumersInDB(String supplierRSID, String docOwner) {
//        if (true) {
//            //test_debug
//            return getAllSuppliersOrConsueumersInDB_test(supplierRSID, docOwner, false);
//        }

        Map<String, Long> consumers = new HashMap<>();

        Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
        SelectQuery sql = new SelectQueryImpl(new Table(ExternalRangeUtils.TABLE_SUPPLIERWORKBOOKS));
        sql.addSelectColumn(new Column(null, "*"));
        sql.setCriteria(new Criteria(new Column(ExternalRangeUtils.TABLE_SUPPLIERWORKBOOKS, ExternalRangeUtils.COL_SUPPLIER_RSID), supplierRSID, QueryConstants.EQUAL));
        DataObject webhooksDO = null;
        try {
            webhooksDO = persistence.get(sql);
        } catch (DataAccessException e) {
            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] supplier not found in DB [consumerResourceId: {0} , DocOwner: {1} ]", new Object[]{supplierRSID, docOwner});
            return consumers;
        }

        if(webhooksDO != null) {
            Row row;
            Iterator iterator = null;
            try {
                iterator = webhooksDO.getRows(ExternalRangeUtils.TABLE_SUPPLIERWORKBOOKS);
            } catch (DataAccessException e) {
                LOGGER.log(Level.SEVERE, "[IMPORTRANGE] SupplierWorkbooks-Table access failed [consumerResourceId: {0} , DocOwner: {1} ]", new Object[]{supplierRSID, docOwner});
                return consumers;
            }
            while (iterator.hasNext()) {
                row = (Row) iterator.next();
                int status = row.get(ExternalRangeUtils.COL_STATUS) == null ? ExternalRangeUtils.ADD_SUPPLIER_RESPONSE.SUCCESS.getCode() : (Integer) row.get(ExternalRangeUtils.COL_STATUS);
                if(status == ExternalRangeUtils.ADD_SUPPLIER_RESPONSE.SUCCESS.getCode()) {
                    String resource_id = (String) row.get(ExternalRangeUtils.COL_RESOURCE_ID);
                    Long createdBy = (Long) row.get(ExternalRangeUtils.COL_CREATED_BY);
                    consumers.put(resource_id, createdBy);
                }
            }
        }
        return consumers;
    }

    private static Map<String, Long> getAllSuppliersFromDB(String rsid, String documentOwner) {
//        if (true) {
//            //test_debug
//            return getAllSuppliersOrConsueumersInDB_test(rsid, documentOwner, true);
//        }
        Map<String, Long> suppliersFromDB = null;
        try {
            suppliersFromDB = ExternalRangeUtils.getSuppliersFromDB(documentOwner, rsid);
        } catch (Exception e) {
            LOGGER.log(Level.OFF,
                    "[IMPORTRANGE][WORKBOOKLINK] (RSID: {0}) - Exception while getting Suppliers From DB",//No I18N
                    new Object[]{rsid});
            LOGGER.log(Level.OFF,
                    "[IMPORTRANGE][WORKBOOKLINK] (RSID: {0}) - Exception while getting Suppliers From DB",//No I18N
                    e);
            return new HashMap<>();
        }
        return suppliersFromDB;
    }

    @Override
    public String toString() {
        StringJoiner stringJoiner = new StringJoiner(",","[","]");
        for (Node value : this.nodes.values()) {
            stringJoiner.add(value.toString());
        }
        return stringJoiner.toString();
    }

    public void syncLiveStats(String rsid, Map<String, Boolean> liveStats) {
        for (Map.Entry<String, Boolean> stringBooleanEntry : liveStats.entrySet()) {
            Node node = this.nodes.get(stringBooleanEntry.getKey());
            if(node != null) {
                node.setLive(stringBooleanEntry.getValue());
            }
        }
        setEffectivelyLiveDirectConsumers(rsid);
    }


    public Set<String> getAllLinkedSpreadsheet() {
        return new HashSet<>(this.nodes.keySet());
    }
    public void reinitializeWorkbookLinks(String rsid, String documentOwner) {
        if(!EngineConstants.IMPORTRANGE_ENABLED || !RedisHelper.is_importrange_enabled()) {
            return;
        }
        final Instant start = Instant.now();
        synchronized (this) {
            long wait = Duration.between(start, Instant.now()).toMillis();
            this.isInitializationDone = true;

            if (rsid == null) {
                LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK][DEBUG]rsid null");
                return;
            }
            if (EngineConstants.FILENAME_REMOTEDOC.equals(rsid)) {
                LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK][DEBUG]rsid is remotedoc");
                return;
            }

            HashMap<String, Node> cache = new HashMap<>(this.nodes);
            this.nodes.clear();

            Node node = new Node(rsid, cache, documentOwner);
            if(node.getDocOwner() == null) {
                LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK][DEBUG] documentOwner null (rsid: {0})", new Object[]{rsid});
                return;
            }

            this.nodes.putAll(linkConsumersAndSuppliers(node, cache));
            setEffectivelyLiveDirectConsumers(rsid);
            if(this.nodes.size() != 1) {
                LOGGER.log(Level.OFF, "[IMPORTRANGE] link: {0} Time [total:{1},wait:{2}], countOfNodes:{3}, {4}\n{5}", new Object[]{rsid, Duration.between(start, Instant.now()).toMillis(), wait, this.nodes.size(),toString(), getEdgeList(this.nodes)});
            }
        }
    }

    public static void link(WorkbookLink link, String rsid, String documentOwner) {
        if(!EngineConstants.IMPORTRANGE_ENABLED || !RedisHelper.is_importrange_enabled()) {
            return;
        }
        final Instant start = Instant.now();
        long wait = Duration.between(start, Instant.now()).toMillis();

        LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK] rsid: {0}", new Object[]{rsid});
        if (rsid == null) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK][DEBUG]rsid null");
            return;
        }
        if (EngineConstants.FILENAME_REMOTEDOC.equals(rsid)) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK][DEBUG]rsid is remotedoc");
            return;
        }


        HashMap<String, Node> cache = new HashMap<>();
        Node node = new Node(rsid, cache, documentOwner);
        if(node.getDocOwner() == null) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK][DEBUG] documentOwner null (rsid: {0})", new Object[]{rsid});
            return;
        }

        link.nodes.putAll(linkAll(node, cache));
        LOGGER.log(Level.OFF, "[IMPORTRANGE] link: {0} Time [total:{1},wait:{2}], countOfNodes:{3}", new Object[]{
                rsid, Duration.between(start, Instant.now()).toMillis(),wait, link.nodes.size()});
        LOGGER.log(Level.OFF, "[IMPORTRANGE] link: {0} {1}\n{2}", new Object[]{rsid, link.toString(), getEdgeList(link.nodes)});

    }

    private static Map<String, Node> linkConsumersAndSuppliers(Node firstNode, HashMap<String, Node> cache) {
        Map<String, Node> node_cache = new HashMap<>();
        node_cache.put(firstNode.getRsid(), firstNode);

        Map<String, Node> visited = new HashMap<>();
        Stack<Node> stack = new Stack<>();

        stack.add(firstNode);
        while (!stack.isEmpty()) {
            Node node = stack.pop();
            if(visited.containsKey(node.getRsid())) {
                continue;
            }
            visited.put(node.getRsid(), node);
            for (Map.Entry<String, Long> entry : getAllSuppliersFromDB(node.getRsid(), node.getDocOwner()).entrySet()) {
                Node supplier = visited.get(entry.getKey());
                if (supplier == null) {
                    supplier = node_cache.get(entry.getKey());
                    if(supplier == null) {
                        supplier = new Node(entry.getKey(), cache, null);
                        supplier.setCreator(entry.getValue());

                        node_cache.put(supplier.getRsid(), supplier);
                    }
                    if(supplier.getDocOwner() == null) {
                        visited.put(supplier.getRsid(),supplier);
                    } else {
                        stack.add(supplier);
                    }
                }
                node.getSuppliers().add(supplier);
            }
        }

        Map<String, Node> visited_consumers = new HashMap<>();
        stack.add(firstNode);
        while (!stack.isEmpty()) {
            Node node = stack.pop();
            if(visited_consumers.containsKey(node.getRsid())) {
                continue;
            }
            visited_consumers.put(node.getRsid(), node);
            for (Map.Entry<String, Long> entry : getAllConsumersInDB(node.getRsid(), node.getDocOwner()).entrySet()) {
                Node consumer = visited_consumers.get(entry.getKey());
                if (consumer == null) {
                    consumer = node_cache.get(entry.getKey());
                    if(consumer == null) {
                        consumer = new Node(entry.getKey(), cache, null);
                        consumer.setCreator(entry.getValue());
                        node_cache.put(consumer.getRsid(), consumer);
                    }
                    if(consumer.getDocOwner() == null) {
                        visited_consumers.put(consumer.getRsid(),consumer);
                    } else {
                        stack.add(consumer);
                    }
                }
                consumer.getSuppliers().add(node);
            }
        }

        visited.putAll(visited_consumers);
        return visited;
    }
    private static Map<String, Node> linkAll(Node firstNode, HashMap<String, Node> cache) {
        Map<String, Node> node_cache = new HashMap<>();
        node_cache.put(firstNode.getRsid(), firstNode);

        Map<String, Node> visited = new HashMap<>();
        Stack<Node> stack = new Stack<>();
        stack.add(firstNode);

        while (!stack.isEmpty()) {
            Node node = stack.pop();
            if(visited.containsKey(node.getRsid())) {
                continue;
            }
            visited.put(node.getRsid(), node);
            for (Map.Entry<String, Long> entry : getAllSuppliersFromDB(node.getRsid(), node.getDocOwner()).entrySet()) {
                Node supplier = visited.get(entry.getKey());
                if (supplier == null) {
                    supplier = node_cache.get(entry.getKey());
                    if(supplier == null) {
                        supplier = new Node(entry.getKey(), cache, null);
                        supplier.setCreator(entry.getValue());
                        node_cache.put(supplier.getRsid(), supplier);
                    }
                    if(supplier.getDocOwner() == null) {
                        visited.put(supplier.getRsid(),supplier);
                    } else {
                        stack.add(supplier);
                    }
                }
                node.getSuppliers().add(supplier);
            }
            for (Map.Entry<String, Long> entry : getAllConsumersInDB(node.getRsid(), node.getDocOwner()).entrySet()) {
                Node consumer = visited.get(entry.getKey());
                if (consumer == null) {
                    consumer = node_cache.get(entry.getKey());
                    if(consumer == null) {
                        consumer = new Node(entry.getKey(), cache, null);
                        consumer.setCreator(entry.getValue());
                        node_cache.put(consumer.getRsid(), consumer);
                    }
                    if(consumer.getDocOwner() == null) {
                        visited.put(consumer.getRsid(),consumer);
                    } else {
                        stack.add(consumer);
                    }
                }
                consumer.getSuppliers().add(node);
            }
        }

        return visited;
    }

    public String getCheckedOutVersion(String supplierRSID, String consumerRSID) {
        Node node=this.nodes.get(consumerRSID);
        if(node==null){
            LOGGER.log(Level.OFF,"importrange consumer not present. consumer :{0}, supplier :{1}", new Object[]{consumerRSID, supplierRSID});
            return null;
        }
        Long linkCreatorZUID = node.getCreator();
        if (linkCreatorZUID == null) {//precautionary
            return null;
        }
        String docOwnerZuid = Node.getSpaceID(supplierRSID);
        if (docOwnerZuid == null) {
            return null;
        }

        return getCheckedOutVersion(supplierRSID, docOwnerZuid, String.valueOf(linkCreatorZUID));
    }

    private static String getCheckedOutVersion(String rsid, String docOwnerZUID, String linkCreatorZUID) {
        HashMap<String, Object> lockedInfoMap;
        try {
            lockedInfoMap = ZohoFS.getLockedResourceInfo(ZohoFS.getOwnerZID(rsid), rsid);
        } catch (Exception exception) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK] couldn't get LockedResourceInfo rsid: {0}",new Object[]{rsid});
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK] couldn't get LockedResourceInfo",exception);
            return null;
        }

        if (lockedInfoMap != null) {
            String lockedBy = (String) lockedInfoMap.get("LOCKED_BY");
            if (!lockedBy.equals(String.valueOf(linkCreatorZUID))) {
                return  (String) lockedInfoMap.get("VERSION");
            }
        }
        return null;
    }

    public String getDocumentOwner(String rsid) {
//        if (true) {
//            //test_debug
//            this.documentOwnersCache.put(rsid, "bsc");
//            return "bsc";
//        }
        return this.nodes.get(rsid).getDocOwner();
    }

    public boolean isExceedsLinkSize(String rsid, WorkbookLink workbookLink, String supplierRSID) {
        return isLimitExceeding(this, rsid, workbookLink, supplierRSID);
    }

    public Set<String> getLiveConsumerContainers() {
        return this.effectivelyLiveConsumerContainers;
    }
    public void setEffectivelyLiveDirectConsumers(String rsid) {
        if (rsid == null) {
            return;
        }
        this.effectivelyLiveConsumerContainers.clear();
        for (String cs : getDirectConsumer(rsid)) {
            if (isConsumerEffectivelyLive(cs)) {
                this.effectivelyLiveConsumerContainers.add(cs);
            }
        }
    }

    private boolean isConsumerEffectivelyLive(String node) {
        Stack<String> stack = new Stack<>();
        stack.add(node);
        Set<String> visited = new HashSet<>();

        while (!stack.isEmpty()) {
            String pop = stack.pop();
            if(visited.contains(pop)) {
                continue;
            }
            visited.add(pop);

            if(this.nodes.get(pop).isLive()) {
                return true;
            }

            for (String s : getDirectConsumer(pop)) {
                if (this.nodes.get(s).isLive()) {
                    return true;
                }
                stack.add(s);
            }
        }
        return false;
    }

    public Set<String> getDirectConsumer(String rsid) {
        Set<String> directedConsumer = new HashSet<>();
        for (Node value : this.nodes.values()) {
            for (Node supplier : value.getSuppliers()) {
                if(supplier.getRsid().equals(rsid)) {
                    directedConsumer.add(value.getRsid());
                }
            }
        }
        return directedConsumer;
    }

    public void updateDirectConsumerLiveStatus(String rsid, String consumerId) {
        Node node=this.nodes.get(consumerId);
        if(node == null) {
            LOGGER.log(Level.OFF,"importrange consumer not present. consumer :{0}, supplier :{1}", new Object[]{consumerId, rsid});
            return;
        }
        node.setLive(true);
        setEffectivelyLiveDirectConsumers(rsid);
    }

    public void broadCastToResetLink(String rsid, AtomicInteger noOfAllowedApiRequest, String taskId) {
        Set<String> set = new HashSet<>();
        for (Node supp : this.nodes.values()) {
            if(!supp.getRsid().equals(rsid) && supp.isLive()) {
                set.add(supp.getRsid());
            }
        }
        HttpTaskManager.add(new NotifyReset(rsid, taskId).setBroadcasterAction(BROADCASTER_ACTION.ADD).setRelationship(BROADCASTER_RELATIONSHIP.CONSUMER).setReceivers(set));
        //todo remove un-necessary parameters like relationship, broadcaster_supplier_rsid
    }

    public void broadcastReceiver(WorkbookContainer container, JSONObjectWrapper jObj, String rsid, String taskId) {
        String broadcasterRSID = jObj.getString(BROADCASTER_RSID);
        BROADCASTER_ACTION broadcasterAction = BROADCASTER_ACTION.valueOf(jObj.getString(BROADCASTER_ACTION_KEY));
        switch (broadcasterAction) {
            case OPEN:
                this.nodes.get(broadcasterRSID).setLive(true);
                setEffectivelyLiveDirectConsumers(container.getResourceId());
                break;
            case CLOSE:
                this.nodes.get(broadcasterRSID).setLive(false);
                setEffectivelyLiveDirectConsumers(container.getResourceId());
                break;
            case ADD:
            case REMOVE:
                Set<String> before=this.nodes.keySet();
                reinitializeWorkbookLinks(rsid, null);
                Set<String> after = this.nodes.keySet();
                after.remove(rsid);
                after.removeAll(before);
                if(!after.isEmpty()){
                    HttpTaskManager.add(new OpenStatusFetch(rsid, taskId).setContainer(container).setConsumers(after).setBroadcastOpen(false));
                }
                break;
        }
    }

    public void copyCaches(WorkbookLink tempLink) {
        for (Node value : tempLink.nodes.values()) {
            Node node = this.nodes.get(value.getRsid());
            if (node != null) {
                if(node.getDocOwner() == null) {
                    node.setDocOwner(value.getDocOwner());
                }
                node.setCreator(value.getCreator());
                node.setLive(value.isLive());
            }
        }
    }

    public static boolean isResourceActive(String rsid, boolean ifErrorReturn) {
        if (rsid == null) {
            return ifErrorReturn;
        }

        String ownerzuid;
        try {
            ownerzuid = ZohoFS.getOwnerZID(rsid);
        } catch (Exception exception) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK] failed to get isResourceActive as failed to get ownerZID, rsid: {0}", new Object[]{rsid});
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK] failed to get isResourceActive as failed to get ownerZID, ", exception);
            return ifErrorReturn;
        }

        int status;
        try {
            status = ZohoFS.getResourceStatus(ownerzuid, rsid);
        } catch (Exception exception) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK] failed to get isResourceActive, rsid: {0}", new Object[]{rsid});
            LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK] failed to get isResourceActive, ", exception);
            return ifErrorReturn;
        }

        if(status == ResourceStatus.ACTIVE || status == ResourceStatus.DRAFT || status == ResourceStatus.INITIATE){
            return true;
        }

        return false;
    }

    public void syncExternalRangeCacheWithWorkbookLink(ExternalRangeCache externalRangeCache, String consumerRSID) {
        //sync supplier of ExternalRangeCache with WorkbookLink
        //sync consumer of ExternalRangeManager with WorkbookLink

        if (consumerRSID == null) {
            return;
        }

        Node node = this.nodes.get(consumerRSID);
        if (node == null) {
            return;
        }

        Set<String> removedSuppliers = externalRangeCache.getSuppliers().keySet().stream().filter(s -> !node.getSuppliers().contains(s)).collect(Collectors.toSet());
        removedSuppliers.forEach(externalRangeCache.getSuppliers()::remove);

        for (Node supplier : node.getSuppliers()) {
            if (supplier.getCreator() != null) {
                externalRangeCache.getSuppliers().put(supplier.getRsid(), supplier.getCreator());
            }
        }
    }

    public void broadcastAllToResetLinkExcept(String consumerRSID, String taskId) {
        Set<String> set = new HashSet<>();
        for (String supp : this.nodes.keySet()) {
            if(!supp.equals(consumerRSID) && this.nodes.get(supp).isLive()) {
                set.add(supp);
            }
        }
        HttpTaskManager.add(new NotifyReset(consumerRSID, taskId).setBroadcasterAction(BROADCASTER_ACTION.ADD).setRelationship(BROADCASTER_RELATIONSHIP.CONSUMER).setReceivers(set));
    }

    public void dismantle() {
        this.nodes = null;
    }

    public Set<String> getOpenLinks() {
        return this.nodes.values()
                .stream()
                .filter(Node::isLive)
                .map(Node::getRsid)
                .collect(Collectors.toSet());
    }

    public enum BROADCASTER_RELATIONSHIP {SUPPLIER, CONSUMER}

    public enum BROADCASTER_ACTION {OPEN, CLOSE, ADD, REMOVE}

    public boolean isInitializationDone() {
        return isInitializationDone;
    }
    private static boolean isLimitExceeding(WorkbookLink workbookLink, String consumer, WorkbookLink workbookLink1, String supplier) {
        Map<String, Node> mergeGraphs = mergeGraphs(workbookLink, consumer, workbookLink1, supplier);
        Object[] stringIntegerPair = highestReachableNode(mergeGraphs);
        LOGGER.log(Level.OFF, "node : {0}, count {1}", new Object[]{stringIntegerPair[0], stringIntegerPair[1]});
        if(((int)stringIntegerPair[1]) > EngineConstants.OVERALL_SUPPLIER_LIMIT) {
            LOGGER.log(Level.OFF, "consumer: {0}, supplier: {1}, {2}", new Object[]{consumer, supplier, getEdgeList(mergeGraphs)});
            return true;
        }
        return false;
    }

    private static Map<String, Node> clone(Map<String, Node> nodes) {
        Map<String, Node> cloneMap = new HashMap<>();
        for (Node original : nodes.values()) {
            Node clone = null;
            try {
                clone = original.clone();
            } catch (CloneNotSupportedException e) {
                //todo
            }
            cloneMap.put(clone.getRsid(), clone);
        }
        for (Node original : nodes.values()) {
            Node clone = cloneMap.get(original.getRsid());
            for (Node supplier : original.getSuppliers()) {
                clone.getSuppliers().add(cloneMap.get(supplier.getRsid()));
            }
        }
        return cloneMap;
    }

    private static Map<String, Node> mergeGraphs(WorkbookLink workbookLink, String consumer, WorkbookLink workbookLink1, String supplier) {
        Map<String, Node> mergedGraph = clone(workbookLink.nodes);
        for (Node value : clone(workbookLink1.nodes).values()) {
            if(!mergedGraph.containsKey(value.getRsid())) {
                mergedGraph.put(value.getRsid(), value);
            }
        }
        mergedGraph.get(consumer).getSuppliers().add(mergedGraph.get(supplier));
        return mergedGraph;
    }

    private static Object[] highestReachableNode(Map<String, Node> graph1) {
        int max = 0;
        Node max_node = null;
        for (Node node : graph1.values()) {
            int countOfReachableNodes = getReachability(node, new HashSet<>());
            if (countOfReachableNodes > max) {
                max = countOfReachableNodes;
                max_node = node;
            }
        }
        return new Object[]{max_node.getRsid(), max};
    }

    private static int getReachability(Node node, Set<Node> visitedNodes) {
        Set<Node> directNodes = node.getSuppliers();
        if(visitedNodes.contains(node)) {
            return 0;
        }
        visitedNodes.add(node);
        if(directNodes.isEmpty()) {
            return 0;
        }
        int sum = 0;
        for (Node directNode : directNodes) {
            if(!visitedNodes.contains(directNode)) {
                sum += getReachability(directNode, visitedNodes) + 1;
            }
        }
        return sum;
    }
    private static String getAdjacencyMatrix(Map<String, Node> graph) {
        String newLineChar = ";";//No I18N
        StringJoiner stringJoiner = new StringJoiner(newLineChar);
        try {
            List<String> nodes = new ArrayList<>(graph.keySet());
            Map<String, Integer> map = new HashMap<>();
            int i = 0;
            for (String node : nodes) {
                map.put(node, i++);
            }
            boolean[][] adjacencyMatrix = new boolean[nodes.size()][nodes.size()];
            for (Node node : graph.values()) {
                for (Node s : node.getSuppliers()) {
                    adjacencyMatrix[map.get(node.getRsid())][map.get(s.getRsid())] = true;
                }
            }


            StringJoiner stringJoiner1 = new StringJoiner(newLineChar);
            for (String node : nodes) {
                if (node.length() > 4) {
                    stringJoiner1.add(node.substring(node.length()-4));
                } else {
                    stringJoiner1.add(node);
                }
            }

            stringJoiner.add(stringJoiner1.toString());
            for (int j = 0; j < nodes.size(); j++) {
                stringJoiner1 = new StringJoiner(",");
                for (int k = 0; k < nodes.size(); k++) {
                    stringJoiner1.add(String.valueOf(adjacencyMatrix[j][k] ? 1 : 0));
                }
                stringJoiner.add(stringJoiner1.toString());
            }
        } catch (Exception e) {
            LOGGER.log(Level.OFF, "", e);
        }
        return stringJoiner.toString();
    }

    private static String getEdgeList(Map<String, Node> graph) {
        String newLineChar = ";";//No I18N
        StringJoiner edgeList = new StringJoiner(newLineChar);
        StringJoiner rsid_list = new StringJoiner(newLineChar);
        try {
            Map<String, Integer> rsid_index_map = new HashMap<>();
            Function<String, Integer> calc_index = (rsid) -> {
                Integer con_index = rsid_index_map.get(rsid);
                if(con_index == null) {
                    con_index = rsid_index_map.size();
                    rsid_index_map.put(rsid, con_index);
                    rsid_list.add(rsid.length() > 4 ? rsid.substring(rsid.length()-4) : rsid);
                }
                return con_index;
            };
            for (Node consumer : graph.values()) {
                Integer con_index = calc_index.apply(consumer.getRsid());
                for (Node supplier : consumer.getSuppliers()) {
                    Integer supp_index = calc_index.apply(supplier.getRsid());
                    edgeList.add(con_index+">"+supp_index);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.OFF, "", e);
        }
        return rsid_list.add("").add(edgeList.toString()).toString();
    }


    public static void main(String[] args) {
        WorkbookLink workbookLink = new WorkbookLink();
//        workbookLink.reinitializeWorkbookLinks("680", null);
//                WorkbookLink.link(workbookLink, "680", null);
        Object[] objects = highestReachableNode(workbookLink.nodes);
//        System.out.println(objects[0]+","+objects[1]);

        WorkbookLink workbookLink1 = new WorkbookLink();
//        workbookLink1.reinitializeWorkbookLinks("1a", null);

//        System.out.println(workbookLink1.isExceedsLinkSize("680", workbookLink, "1a"));
    }

    public static Map<String, Long> getAllSuppliersOrConsueumersInDB_test(String supplierRSID, String docOwner, boolean getAllSuppliers)  {
        Map<String, Long> consumers_or_supplier = new HashMap<>();
        List<String> consumer_supplier_list = new ArrayList<>();
//        consumer_supplier_list.add("1a>1b");

//        consumer_supplier_list.add("33>00");
//        consumer_supplier_list.add("22>44");
//        consumer_supplier_list.add("00>44");

//        try (BufferedReader br = new BufferedReader(new FileReader("/Users/<USER>/Library/Application Support/JetBrains/IdeaIC2023.2/scratches/scratch_65.txt"))) {
//            String line;
//            while ((line = br.readLine()) != null) {
//                for(String string : line.split(";")) {
//                    consumer_supplier_list.add(string);
//                }
//            }
//        } catch(FileNotFoundException e) {
//            throw new RuntimeException(e);
//        } catch(IOException e) {
//            throw new RuntimeException(e);
//        }

        if(getAllSuppliers) {
            for(String consumer_supplier : consumer_supplier_list) {
                String[] split = consumer_supplier.split(">");
                String consumer = split[0];
                String supplier = split[1];
                if(consumer.equals(supplierRSID)) {
                    consumers_or_supplier.put(supplier, 1l);
                }
            }
            return consumers_or_supplier;
        }

        for(String consumer_supplier : consumer_supplier_list) {
            String[] split = consumer_supplier.split(">");
            String consumer = split[0];
            String supplier = split[1];
            if(supplier.equals(supplierRSID)) {
                consumers_or_supplier.put(consumer, 1l);
            }
        }
        return consumers_or_supplier;
    }

    public static byte[] get_IMPORTRANGE_NETWORK_AS_GRAPHML(String rsid) {
        WorkbookLink workbookLink = new WorkbookLink();
        try {
            WorkbookLink.link(workbookLink, rsid, null);
        } catch(Exception e) {
            LOGGER.log(Level.OFF, "rsid: ", e);
        }
        return getGraphML(getEdgeList(workbookLink.nodes));
    }
    public static byte[] getGraphML(String graph) {
        String[] split = graph.split(";;");
        String[] rsid_list = split[0].split(";");
        String[] edges = split[1].split(";");

        StringBuilder stringBuilder2 = new StringBuilder();
        stringBuilder2.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?><graphml><graph id=\"Graph\" uidGraph=\"788\" uidEdge=\"12232\">");
        StringBuilder nodes_sb = new StringBuilder();
        StringBuilder edge_sb = new StringBuilder();
        Set<String> nodes = new HashSet<>();
        for(String string : edges) {
            String[] split1 = string.split(">");
            String string1 = split1[0];
            String string2 = split1[1];
            nodes.add(string1);
            nodes.add(string2);
            edge_sb.append("<edge ");
            edge_sb.append("source=\"").append(rsid_list[Integer.parseInt(string1)]).append("\"");
            edge_sb.append(" target=\"").append(rsid_list[Integer.parseInt(string2)]).append("\"");
            edge_sb.append("directed=\"true\"></edge>");
        }
        for(String node : nodes) {
            nodes_sb.append("<node ");
            nodes_sb.append("id=\"").append(rsid_list[Integer.parseInt(node)]).append("\"");
            nodes_sb.append("></node>");
        }
        stringBuilder2
                .append(nodes_sb)
                .append(edge_sb)
                .append("</graph></graphml>");

        return stringBuilder2.toString().getBytes();
    }

    public void export_dc_migration_importrange_file(ZipOutputStream zipOutputStream, String rsid) throws IOException {
        ZipEntry zipEntry = new ZipEntry(DCMigrationUtil.DC_MIGRATION_FILES_IMPORTRANGE_LINKS_JSON);
        zipOutputStream.putNextEntry(zipEntry);
        zipOutputStream.write(getLinksForDCMigration(rsid).toString().getBytes());
    }

    public static void import_dc_migration_importrange_file(InputStream inputStream, String rsid, String docOwner, String docSpaceId, ZUIDMigrationHelper zuidMigrationHelper) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
        String str = br.readLine();
        JSONObjectWrapper jObj = new JSONObjectWrapper(str);
        import_dc_migration_importrange_file(jObj, rsid, docOwner, docSpaceId, zuidMigrationHelper);
    }

    private JSONObjectWrapper getLinksForDCMigration(String rsid) {

        JSONArrayWrapper jsonArrayWrapper_supplier = new JSONArrayWrapper();
        for(Node supplier : this.nodes.get(rsid).getSuppliers()) {
            JSONArrayWrapper jsonArrayWrapper1 = new JSONArrayWrapper();
            jsonArrayWrapper1.put(supplier.getRsid());
            jsonArrayWrapper1.put(String.valueOf(supplier.getCreator()));
            jsonArrayWrapper_supplier.put(jsonArrayWrapper1);
        }

        JSONArrayWrapper jsonArrayWrapper_consumer = new JSONArrayWrapper();
        for(String consumer : getDirectConsumer(rsid)) {
            Node node = this.nodes.get(consumer);
            JSONArrayWrapper jsonArrayWrapper1 = new JSONArrayWrapper();
            jsonArrayWrapper1.put(node.getRsid());
            jsonArrayWrapper1.put(String.valueOf(node.getCreator()));
            jsonArrayWrapper_consumer.put(jsonArrayWrapper1);
        }

        JSONObjectWrapper jsonObjectWrapper = new JSONObjectWrapper();
        jsonObjectWrapper.put("suppliers", jsonArrayWrapper_supplier);
        jsonObjectWrapper.put("consumers", jsonArrayWrapper_consumer);
        return jsonObjectWrapper;
    }

    private static void import_dc_migration_importrange_file(JSONObjectWrapper jsonObjectWrapper, String rsid, String docOwner, String docSpaceId, ZUIDMigrationHelper zuidMigrationHelper) {
        JSONArrayWrapper jsonArrayWrapper_supplier = jsonObjectWrapper.getJSONArray("suppliers");
        for(int i = 0; i < jsonArrayWrapper_supplier.length(); i++) {
            JSONArrayWrapper jsonArray = jsonArrayWrapper_supplier.getJSONArray(i);
            String supplierRSID = jsonArray.getString(0);
            Long userId = Long.valueOf(zuidMigrationHelper.getMigratedZUID(docSpaceId, String.valueOf(jsonArray.getString(1))));
            ExternalRangeUtils.addOrUpdateSupplierToDB(docOwner, rsid, supplierRSID, userId, ExternalRangeUtils.ADD_SUPPLIER_RESPONSE.SUCCESS);
        }

        JSONArrayWrapper jsonArrayWrapper_consumer = jsonObjectWrapper.getJSONArray("consumers");
        for(int i = 0; i < jsonArrayWrapper_consumer.length(); i++) {
            JSONArrayWrapper jsonArray = jsonArrayWrapper_consumer.getJSONArray(i);
            String consumerRSID = jsonArray.getString(0);
            Long userId = Long.valueOf(zuidMigrationHelper.getMigratedZUID(docSpaceId, String.valueOf(jsonArray.getString(1))));
            ExternalRangeUtils.addOrUpdateSupplierToDB(docOwner, consumerRSID, rsid, userId, ExternalRangeUtils.ADD_SUPPLIER_RESPONSE.SUCCESS);
        }
    }
}
