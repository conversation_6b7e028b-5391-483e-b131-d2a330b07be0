// $Id$
package com.adventnet.zoho.websheet.model.externalRange;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Table;
import com.adventnet.ds.query.*;
import com.adventnet.iam.security.SecurityUtil;
import com.adventnet.persistence.DataAccessException;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.ext.functions.ImportRange;
import com.adventnet.zoho.websheet.model.externalRange.HttpTask.HttpTaskManager;
import com.adventnet.zoho.websheet.model.externalRange.HttpTaskRequest.OpenStatusFetch;
import com.adventnet.zoho.websheet.model.util.*;
import com.google.common.io.CharStreams;
import com.zoho.sheet.action.ActionObject;
import com.zoho.sheet.action.ExternalRangeAction;
import com.zoho.sheet.authorization.util.AuthorizationUtil;
import com.zoho.sheet.util.ContainerSynchronizer;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ShareConstants;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;

import java.io.*;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.InflaterInputStream;

public class ExternalRangeUtils {
    public static final String NOTIFICATION_SUPPLY_CHAIN = "nsc";//No I18N
    public static final String IMPORTRANGE_TAG  = "[IMPORTRANGE] ";//No I18N
    public static final String EXTERNAL_RANGES_DETAILS = "exrd";  //No I18N
    public static final String REQUESTED_EXTERNAL_RANGES = "reqexr";//No I18N
    public static final String EXTERNAL_RANGES = "exr";//No I18N
    public static final String VALUES_OF_REQUESTED_EXTERNAL_RANGES = "vreqexr";//No I18N
    public static final String ERRMSG_NO_WRITE_ACCESS = "Don't have write access";//No I18N
    public static final String ERRMSG_ALREADY_LINKED = "Already Linked";//No I18N
    public static final String ERRMSG_NO_READ_ACCESS_TO_SUPPLIER = "Don't have read access for the supplier";//No I18N
    public static final String ERRMSG_INCORRECT_URL = "Incorrect Url";//No I18N
    public static final String ERRMSG_CIRCULARCHAIN_CHECK_FAIL = "Couldn't complete Circular-Chain check";//No I18N
    public static final String ERRMSG_CREATES_CIRCULARCHAIN = "Creates CIRCULAR-CHAIN";//No I18N
    public static final String ERRMSG_EXCEEDS_LINK_SIZE = "Exceeds Link Size";//No I18N
    public static final String ERRMSG_SUPPLIER_LINKING_FAILED  = "Couldn't Link Supplier";//No I18N
    public static final String ERRMSG_NO_EDIT_ACCESS  = "No Edit Access";//No I18N
    public static final String ERRMSG_SUPPLIER_NOT_LINKED  = "Supplier Not Linked";//No I18N
    public static final String ERRMSG_SUPPLIER_REMOVAL_FAILED  = "Supplier Removal Failed";//No I18N
    public static final String ERRMSG_CONSUMER_REMOVE_FAILED  = "Consumer Removal Failed";//No I18N
    public static final String ERRMSG_CONSUMER_NOT_LINKED  = "Consumer not linked";//No I18N
    public static final String SUCCESSMSG_SUPPLIER_ADDED  = "Supplier ADDED";//No I18N
    public static final String SUCCESSMSG_CONSUMER_REMOVED  = "Consumer Removed";//No I18N
    public static final String KEY_ACCESS = "access";//No I18N
    public static final String KEY_CONSUMERS  = "consumers";//No I18N
    public static final String KEY_SUPPLIERS  = "suppliers";//No I18N
    //internal data api call
    public static final String EXTERNAL_RANGE_API_HOSTNAME = EnginePropertyUtil.getSheetPropertyValue("ZohoSheetURL")+"/sheet/";//No I18N
    public static final String EXTERNAL_RANGE_API_URL = "https://" + EXTERNAL_RANGE_API_HOSTNAME;//No I18N
    public static final String KEY_ACTIONOBJECT  = "actionObject";//No I18N
    public static final String KEY_PROXYURL  = "proxyURL";//No I18N
    public static final String MODIFIED_EXTERNAL_RANGES = "modexr";//No I18N
    public static final String KEY_LINKED_ID = "linked_id";//No I18N
    public static final String VAL_ZOHOSHEET = "ZohoSheet";//No I18N
    public static final String VAL_EXTERNALRANGE_ACTION_INTERNAL = "externalrangeaction_internal";//No I18N
    public static final String EXTERNALRANGE_ACTION_INTERNAL_USER = "externalrangeaction_internal_user";//No I18N
    public static final String METHOD_FETCH_RANGES = "fetch.ranges";                //NO I18N
    public static final String METHOD_SUPPLIER_REMOVE = "supplier.remove";                //NO I18N
    public static final String METHOD_WORKBOOK_UPDATE = "workbook.update";                //NO I18N
    public static final String METHOD_LINKEDWORKBOOK_BROADCAST = "workbooklink.broadcast";                //NO I18N
    //Table
    public static final String TABLE_SUPPLIERWORKBOOKS  = "SupplierWorkbooks";//No I18N
    public static final String COL_RESOURCE_ID  = "RESOURCE_ID";//No I18N
    public static final String COL_SUPPLIER_RSID  = "SUPPLIER_RSID";//No I18N
    public static final String COL_CREATED_BY  = "CREATED_BY";//No I18N
    public static final String COL_STATUS  = "STATUS";//No I18N
    public static final String KEY_WEBDATA_URL = "webdataUrl";//No I18N

    private static final Logger LOGGER = Logger.getLogger(ExternalRangeUtils.class.getName());
    public static DataRange getAsDataRange(Cell cell) {
        return new DataRange(cell.getRow().getSheet().getAssociatedName(), cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex());
    }

    public static DataRange getAsDataRangeWithSheetName(Cell cell) {
        return new DataRange(cell.getRow().getSheet().getName(), cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex());
    }

    public static Cell getAsCell(DataRange dataRange, Workbook workbook) {
        Sheet sheetByAssociatedName = workbook.getSheetByAssociatedName(dataRange.getAssociatedSheetName());
        if (sheetByAssociatedName == null) {
            return null;
        }
        return sheetByAssociatedName.getCell(dataRange.getStartRowIndex(), dataRange.getStartColIndex());
    }

    public static CompactZArray mergeZArrays(DataRange originalDtaRange, CompactZArray originalArray, DataRange subDataRange, CompactZArray subArray) {

        if (!originalDtaRange.getAssociatedSheetName().equals(subDataRange.getAssociatedSheetName())
                || subDataRange.getStartRowIndex() > originalDtaRange.getEndRowIndex() || subDataRange.getEndRowIndex() < originalDtaRange.getStartRowIndex()
                || subDataRange.getStartColIndex() > originalDtaRange.getEndColIndex() || subDataRange.getEndColIndex() < originalDtaRange.getStartColIndex()) {
            return null;
        }
        if (originalDtaRange.getStartRowIndex() == subDataRange.getStartRowIndex()
                && originalDtaRange.getStartColIndex() == subDataRange.getStartColIndex()
                && originalDtaRange.getEndRowIndex() == subDataRange.getEndRowIndex()
                && originalDtaRange.getEndColIndex() == subDataRange.getEndColIndex()) {
            //complete overlap
            if(subArray.getSize() == 1 && subArray.getValue(0,0).getType() == Cell.Type.ERROR && subArray.getValue(0,0).getRawValue() == Cell.Error.TIMEOUT) {
                subArray.setSyncState(ImportRange.SYNC_STATE.TIMEOUT);
            }

            return subArray;
        }

        if(subArray.getSize() == 1 && subArray.getValue(0,0).getType() == Cell.Type.ERROR && subArray.getValue(0,0).getRawValue() == Cell.Error.TIMEOUT) {
            originalArray.setSyncState(ImportRange.SYNC_STATE.TIMEOUT);
            /* todo: use-case not covered.
            importedrange A1:A10
            A1 updated, fetch timed-out, syncState=2
            A1 updated, fetch completed, syncState=2 (can not change to 0 as we won't know which cell fetch was incomplete)
             */
            return null;
        }

        return originalArray.updateSubArray(subArray, subDataRange.getStartRowIndex()-originalDtaRange.getStartRowIndex(), subDataRange.getStartColIndex()-originalDtaRange.getStartColIndex());
    }

    public enum ADD_SUPPLIER_RESPONSE {
        SUCCESS(1), TIMEOUT(2), DUPLICATE_ENTERY(3), UNKNOWN(4), NO_RESPONSE(5),//api-response
        ACCESS_DENIED(6);
        private final int code;
        ADD_SUPPLIER_RESPONSE(int code) {
            this.code = code;
        }
        public int getCode() {
            return code;
        }

        public ADD_SUPPLIER_RESPONSE getInstance(int code) {
            switch (code) {
                case 1: return SUCCESS;
                case 2: return TIMEOUT;
                case 3: return DUPLICATE_ENTERY;
                case 5: return NO_RESPONSE;
                case 6: return ACCESS_DENIED;
                default:
                    return UNKNOWN;
            }
        }
    }

    public static void addCommonAttributesForInternalDataApiCall(JSONObjectWrapper actionJson, String method) {
        actionJson
                .put(JSONConstants.IS_REPEAT_ACTION, false)
                .put(JSONConstants.RECORD_UNDO, false)
                .put(DataAPIConstants.METHOD, method)
                .put(JSONConstants.API_ACTION, true);
    }

    public static JSONObjectWrapper internalDataApiCall(JSONObjectWrapper actionJson, String targetRSID, int connectionTimeOut, int readTimeOut, String taskId) throws Exception {
        final String actionJsonString = actionJson.toString();
        StringJoiner postRequestArguments = new StringJoiner("&");
        if(taskId == null) {
            taskId = HttpTaskManager.getNewTaskIdIfNull(taskId);
        }
        postRequestArguments
                .add(KEY_PROXYURL+"="+ VAL_EXTERNALRANGE_ACTION_INTERNAL)//No I18N
                .add(KEY_ACTIONOBJECT+"="+ actionJsonString)//No I18N
                .add(HttpTaskManager.TASK_ID_KEY+"="+taskId);//No I18N

        postRequestArguments.add("iscsignature"+"="+SecurityUtil.sign());//No I18N

        JSONObjectWrapper resp = new JSONObjectWrapper(ContainerSynchronizer.postURLConnectionForImportrange(EXTERNAL_RANGE_API_HOSTNAME, targetRSID, postRequestArguments.toString(), null, UserProfile.AccessType.AUTH, false, null, connectionTimeOut, readTimeOut));

        try {
            return resp.getJSONObject(DataAPIConstants.RESPONSE_BODY);
        } catch (Exception e) {
            LOGGER.log(Level.OFF, String.join("","[IMPORTRANGE][API_CALL][TARGET-RSID]Exception ",targetRSID," actionJson: ",actionJsonString,",response:",resp.toString()));
            throw e;
        }
    }

//    public static boolean isUserHasReadAccessForDoc(String zuid, String rid, boolean defaultValue) {
//        ResourcePermissionInfo permissionInfo = null;
//        try {
//            permissionInfo = ZohoFS.getAllPermissions(zuid, rid);
//
//            if(permissionInfo == null) {
//                LOGGER.log(Level.OFF, "permission info null {0} ", rid);
//                return defaultValue;
//            }
//            JSONObjectWrapper jsonObject = new JSONObjectWrapper(String.valueOf(permissionInfo));
//            if (!jsonObject.has("maxpermission")) { // NO I18N
//                LOGGER.log(Level.OFF, "maxpermission not available {0} ", rid);
//                return defaultValue;
//            }
//            int maxPermission = jsonObject.getInt("maxpermission"); // NO I18N
//            return (maxPermission & ShareConstants.PERMISSION_READ) == ShareConstants.PERMISSION_READ;
//        } catch (Exception e) {
//            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Document Permission Check Incomplete/Failed", e);
//            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Document Permission Check Incomplete/Failed [Zuid: {0}, RSID: {1}]", new Object[]{zuid, rid});
//            return defaultValue;
//        }
//    }
//
//    public static boolean isUserHasWriteAccessForDoc(String zuid, String rid, boolean defaultValue) {
//
//        ResourcePermissionInfo permissionInfo = null;
//        try {
//            permissionInfo = ZohoFS.getAllPermissions(zuid, rid);
//
//            if(permissionInfo == null) {
//                LOGGER.log(Level.OFF, "permission info null {0} ", rid);
//                return defaultValue;
//            }
//            JSONObjectWrapper jsonObject = new JSONObjectWrapper(String.valueOf(permissionInfo));
//            if (!jsonObject.has("maxpermission")) { // NO I18N
//                LOGGER.log(Level.OFF, "maxpermission not available {0} ", rid);
//                return defaultValue;
//            }
//            int maxPermission = jsonObject.getInt("maxpermission"); // NO I18N
//            return (maxPermission & ShareConstants.PERMISSION_WRITE) == ShareConstants.PERMISSION_WRITE;
//
//        } catch (Exception e) {
//            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Document Permission Check Incomplete/Failed", e);
//            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Document Permission Check Incomplete/Failed [Zuid: {0}, RSID: {1}]", new Object[]{zuid, rid});
//            return defaultValue;
//        }
//    }

    public static Map<String, Long> getSuppliersFromDB(String docOwner, String consumerRSID) {
        Map<String, Long> suppliers = new HashMap<>();

        Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
        SelectQuery sql = new SelectQueryImpl(new Table(TABLE_SUPPLIERWORKBOOKS));
        sql.addSelectColumn(new Column(null, "*"));
        sql.setCriteria(new Criteria(new Column(TABLE_SUPPLIERWORKBOOKS, COL_RESOURCE_ID), consumerRSID, QueryConstants.EQUAL));
        DataObject webhooksDO = null;
        try {
            webhooksDO = persistence.get(sql);
        } catch (DataAccessException e) {
            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] supplier not found in DB [consumerResourceId: {0} , DocOwner: {1} ]", new Object[]{consumerRSID, docOwner});
            return suppliers;
        }

        if(webhooksDO != null) {
            Row row;
            Iterator iterator = null;
            try {
                iterator = webhooksDO.getRows(TABLE_SUPPLIERWORKBOOKS);
            } catch (DataAccessException e) {
                LOGGER.log(Level.SEVERE, "[IMPORTRANGE] SupplierWorkbooks-Table access failed [consumerResourceId: {0} , DocOwner: {1} ]", new Object[]{consumerRSID, docOwner});
                return suppliers;
            }
            while (iterator.hasNext()) {
                row = (Row) iterator.next();
                int status = row.get(COL_STATUS) == null ? ADD_SUPPLIER_RESPONSE.SUCCESS.getCode() : (Integer) row.get(COL_STATUS);
                if(status == ADD_SUPPLIER_RESPONSE.SUCCESS.getCode()) {
                    String resource_id = (String) row.get(COL_SUPPLIER_RSID);
                    Long createdBy = (Long) row.get(COL_CREATED_BY);
                    suppliers.put(resource_id, createdBy);
                }
            }
        }
        return suppliers;
    }


    public static boolean addOrUpdateSupplierToDB(String docOwner, String consumerRSID, String supplierRSID, Long userId, ADD_SUPPLIER_RESPONSE addSupplierResponse)
    {
        Row row = new Row(TABLE_SUPPLIERWORKBOOKS);
        row.set(COL_RESOURCE_ID, consumerRSID);
        row.set(COL_SUPPLIER_RSID, supplierRSID);
        row.set(COL_CREATED_BY, userId);
        row.set(COL_STATUS, addSupplierResponse.getCode());

        Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
        SelectQuery sql = new SelectQueryImpl(new Table(TABLE_SUPPLIERWORKBOOKS));
        sql.addSelectColumn(new Column(null, "*"));
        sql.setCriteria(new Criteria(new Column(TABLE_SUPPLIERWORKBOOKS, COL_RESOURCE_ID), consumerRSID, QueryConstants.EQUAL)
                .and(new Criteria(new Column(TABLE_SUPPLIERWORKBOOKS, COL_SUPPLIER_RSID), supplierRSID, QueryConstants.EQUAL)));
        try {
            DataObject dataObject = persistence.get(sql);
            if (!dataObject.isEmpty()) {
                dataObject.updateRow(row);
                return true;
            }
        } catch (DataAccessException e) {
            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] supplier not found in DB [consumerResourceId: {0} , DocOwner: {1} ]", new Object[]{consumerRSID, docOwner});
            return false;
        }

        try
        {
            DataObject dObj = persistence.constructDataObject();
            dObj.addRow(row);
            persistence.add(dObj);
        }
        catch(Exception e) {
            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Supplier DB Update Failed!  [consumerResourceId: {0} , supplierRSID: {1}, DocOwner: {2} ]", new Object[]{consumerRSID,supplierRSID, docOwner});
            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Supplier DB Update Failed!", e);
            return false;
        }

        return true;
    }

    public static boolean removeSupplierFromDB(String docOwner, String consumerRSID, String supplierRSID) {
        try {
            Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
            Criteria consumerCriteria = new Criteria(new Column(TABLE_SUPPLIERWORKBOOKS, COL_RESOURCE_ID), consumerRSID, QueryConstants.EQUAL);
            Criteria suplierCriteria = new Criteria(new Column(TABLE_SUPPLIERWORKBOOKS, COL_SUPPLIER_RSID), supplierRSID, QueryConstants.EQUAL);
            persistence.delete(consumerCriteria.and(suplierCriteria));
        }
        catch(Exception e) {
            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Supplier DB Remove Failed!  [consumerResourceId: {0} , supplierRSID: {1}, DocOwner: {2} ]", new Object[]{consumerRSID,supplierRSID, docOwner});
            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Supplier DB Remove Failed!", e);
            return false;
        }
        return true;
    }

    public static JSONObjectWrapper constructSuppDependentsReEvalGridAction(String documentOwnerZUID, Map<String, Set<DataRange>> importRangeDetails, String userName, String taskId) {
        LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Adding action (ReEvaluate Supplier Dependents) to GridAction");
        return ActionObject.getActionInfo(ActionConstants.WORKBOOK_UPDATE)
                .toJSON()
                . put(JSONConstants.ZUID, documentOwnerZUID)
                .put(JSONConstants.IS_VALUE_CHANGED, true)
                .put(JSONConstants.USER_NAME, userName)
                .put(EXTERNAL_RANGES_DETAILS, getJSONObjectOfImportRangeDetails(importRangeDetails))
                .put(HttpTaskManager.TASK_ID_KEY, taskId);
    }

    public static JSONObjectWrapper construct_RE_EVALUATE_IMPORTRANGE_GridAction(String documentOwnerZUID, String userName, String taskId) {
        LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Adding action  RE_EVALUATE_IMPORTRANGE to GridAction");
        return ActionObject.getActionInfo(ActionConstants.RE_EVALUATE_IMPORTRANGE)
                .toJSON()
                .put(JSONConstants.ZUID, documentOwnerZUID)
                .put(JSONConstants.IS_VALUE_CHANGED, true)
                .put(JSONConstants.USER_NAME, userName)
                .put(HttpTaskManager.TASK_ID_KEY, taskId);
    }

    public static JSONObjectWrapper getJSONObjectOfImportRangeDetails(Map<String, Set<DataRange>> importRangeDetails) {
        JSONObjectWrapper suppliers = new JSONObjectWrapper();

        for (String supplierId : importRangeDetails.keySet()) {
            JSONObjectWrapper dataRanges = new JSONObjectWrapper();
            suppliers.put(supplierId, dataRanges);

            for (DataRange dataRange : importRangeDetails.get(supplierId)) {
                dataRanges.put(toJSONObject(dataRange).toString(), getCellsJSONArray(new HashSet<>()));
            }
        }
        return suppliers;
    }

    private static JSONArrayWrapper getCellsJSONArray(Set<Cell> cells) {
        JSONArrayWrapper jsonArray = new JSONArrayWrapper();
        for (Cell cell : cells) {
            DataRange dataRange = new DataRange(cell.getRow().getSheet().getName(), cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex());
            jsonArray.put(toJSONObject(dataRange));
        }
        return jsonArray;
    }

    public static JSONObjectWrapper toJSONObject(DataRange dataRange) {
        return  new JSONObjectWrapper()
                .put(JSONConstants.SHEET_NAME, dataRange.getAssociatedSheetName())
                .put(JSONConstants.START_ROW, dataRange.getStartRowIndex())
                .put(JSONConstants.START_COLUMN, dataRange.getStartColIndex())
                .put(JSONConstants.END_ROW, dataRange.getEndRowIndex())
                .put(JSONConstants.END_COLUMN, dataRange.getEndColIndex());
    }

    public static DataRange parseDataRange(JSONObjectWrapper dataRangeJSON){
        return new DataRange(
                dataRangeJSON.getString(JSONConstants.SHEET_NAME),
                dataRangeJSON.getInt(JSONConstants.START_ROW),
                dataRangeJSON.getInt(JSONConstants.START_COLUMN),
                dataRangeJSON.getInt(JSONConstants.END_ROW),
                dataRangeJSON.getInt(JSONConstants.END_COLUMN));
    }

    /**
     * should be moved to a different util
     * @param targetRangeReference
     * @return
     * @throws SheetEngineException
     */
    public static DataRange parseDataRange(String targetRangeReference) throws SheetEngineException {

        String[] sheetNameAndRef = CellUtil.getSheetNameAndRef(targetRangeReference);
        String sheetName = sheetNameAndRef[0];
        if(sheetName == null) {
            throw  new SheetEngineException("Can not parse reference["+targetRangeReference+"] to DataRange, no Sheet Name!");//No I18N
        }

        String reference = sheetNameAndRef[1];
        String[] split = reference.split(":");
        String startCellRef = split[0];

        int startRowIndex = getRow(startCellRef);
        int startColIndex = getColumn(startCellRef);

        if (startRowIndex >= Utility.MAXNUMOFROWS || startColIndex >= Utility.MAXNUMOFCOLS) {
            throw new SheetEngineException("[IMPORTRANGE]incorrect reference - out of bound" + targetRangeReference);//No I18N
        }

        int endRowIndex;
        int endColIndex;

        if(split.length > 1) {
            String endCellRef = split[1];
            endRowIndex = getRow(endCellRef);
            endColIndex = getColumn(endCellRef);

            if (endRowIndex >= Utility.MAXNUMOFROWS || endColIndex >= Utility.MAXNUMOFCOLS) {
                throw new SheetEngineException("[IMPORTRANGE]incorrect reference - out of bound" + targetRangeReference);//No I18N
            }

            if(startRowIndex == -1 && endRowIndex == -1) {
                startRowIndex = 0;
                endRowIndex = Utility.MAXNUMOFROWS - 1;
            }

            if (startColIndex == -1 && endColIndex == -1) {
                startColIndex = 0;
                endColIndex = Utility.MAXNUMOFCOLS - 1;
            }

        } else {
            endRowIndex = startRowIndex;
            endColIndex = startColIndex;
        }

        if (startRowIndex == -1 || startColIndex == -1) {
            throw new SheetEngineException("[IMPORTRANGE]incorrect reference " + targetRangeReference);//No I18N
        }
        return new DataRange(sheetName, startRowIndex, startColIndex, endRowIndex, endColIndex);
    }

    public static Set<DataRange> getOverLappingWebhookRanges(List<DataRange> ranges, Set<DataRange> webhookRanges) {
        Set<DataRange> overlappingWebhookRanges = new HashSet<>();
        for (DataRange range : ranges) {
            overlappingWebhookRanges.addAll(getOverLappingWebhookRanges(range, webhookRanges));
        }
        return overlappingWebhookRanges;
    }

    public static Set<DataRange> getOverLappingWebhookRanges(DataRange range, Set<DataRange> webhookRanges) {
        Set<DataRange> overlappingWebhookRanges = new HashSet<>();
        for (DataRange webhookRange : webhookRanges) {
            DataRange overLappingWebhookRange = getOverLappingDataRange(range, webhookRange);
            if(overLappingWebhookRange != null) {
                overlappingWebhookRanges.add(overLappingWebhookRange);
            }
        }
        return overlappingWebhookRanges;
    }

    public static boolean isDataRangeOverLapping(List<DataRange> dataRanges, DataRange testRange) {
        for (DataRange dataRange : dataRanges) {
            if(dataRange.getAssociatedSheetName().equals(testRange.getAssociatedSheetName())) {
                DataRange overLappingDataRange = getOverLappingDataRange(dataRange, testRange);
                if(overLappingDataRange != null) {
                    return true;
                }
            }
        }
        return false;
    }

    public static DataRange getOverLappingDataRange(DataRange a, DataRange b) {

        if(a.getAssociatedSheetName().equals(b.getAssociatedSheetName()) &&
           a.getStartRowIndex() <= b.getEndRowIndex() && a.getEndRowIndex() >= b.getStartRowIndex() &&
           a.getStartColIndex() <= b.getEndColIndex() && a.getEndColIndex() >= b.getStartColIndex()) {
            return new DataRange(
                    a.getAssociatedSheetName(),
                    Math.max(a.getStartRowIndex(), b.getStartRowIndex()),
                    Math.max(a.getStartColIndex(), b.getStartColIndex()),
                    Math.min(a.getEndRowIndex(), b.getEndRowIndex()),
                    Math.min(a.getEndColIndex(), b.getEndColIndex())
            );
        }
        return null;
    }

    public static void main(String[] args) {
        /**
         *
         *                       A-side
         *                       ------------
         *                       |          |
         *      D-side           |          |  B-side
         *                       ------------
         *                        C-side
         */
        final Function<DataRange, String> toString_ = (d) -> d == null ? null : toJSONObject(d).toString();
        DataRange dataRange = new DataRange(VAL_ZOHOSHEET, 2,2,4,4);
        LOGGER.log(Level.INFO, "dataRage : {0}", toString_.apply(dataRange));


        DataRange fullyInside = new DataRange(VAL_ZOHOSHEET, 2,2,4,4);
        LOGGER.log(Level.INFO, "fullyInside : {0}", toString_.apply(getOverLappingDataRange(dataRange, fullyInside)));
        DataRange completelyOutSide = new DataRange(VAL_ZOHOSHEET, 5, 5, 7, 7);
        LOGGER.log(Level.INFO, "completelyOutSide : {0}", toString_.apply((getOverLappingDataRange(dataRange, completelyOutSide))));
        DataRange aAndBInside = new DataRange(VAL_ZOHOSHEET, 3, 1, 6, 3);
        LOGGER.log(Level.INFO, "aAndBInside : {0}", toString_.apply((getOverLappingDataRange(dataRange, aAndBInside))));


        DataRange bInside = new DataRange(VAL_ZOHOSHEET, 5,1,6,3);
        LOGGER.log(Level.INFO, "bInside : {0}", toString_.apply((getOverLappingDataRange(dataRange, bInside))));
        DataRange dAndbInside = new DataRange(VAL_ZOHOSHEET, 5,2,6,3);
        LOGGER.log(Level.INFO, "dAndbInside : {0}", toString_.apply((getOverLappingDataRange(dataRange, dAndbInside))));
        DataRange aInside = new DataRange(VAL_ZOHOSHEET, 3, 5, 7, 7);
        LOGGER.log(Level.INFO, "aInside : {0}", toString_.apply((getOverLappingDataRange(dataRange, aInside))));
        DataRange aAndCInside = new DataRange(VAL_ZOHOSHEET, 3, 5, 4, 7);
        LOGGER.log(Level.INFO, "aAndCInside : {0}", toString_.apply((getOverLappingDataRange(dataRange, aAndCInside))));

    }

    public static ZArray convert(JSONArrayWrapper range_details, int sr, int sc) {
        List arr = new ArrayList();

        int rowSize = 0;
        int colSize = 0;

        for(int i = 0; i < range_details.length(); i++) {
            JSONObjectWrapper rowJSONObject = range_details.getJSONObject(i);
            int row_index = rowJSONObject.getInt(ROW_INDEX);
            JSONArrayWrapper row_details = rowJSONObject.getJSONArray(ROW_DETAILS);
            for(int j = 0; j < row_details.length(); j++) {
                JSONObjectWrapper cellObject = row_details.getJSONObject(j);
                int column_index = cellObject.getInt(COLUMN_INDEX);

                int relativeSr = (row_index - 1) - sr;
                int relativeSc = (column_index - 1) - sc;

                rowSize = Math.max(rowSize,relativeSr + 1);
                colSize = Math.max(colSize, relativeSc + 1);
            }
        }

        if(rowSize == 0) {
            arr.add(Value.EMPTY_VALUE);

            rowSize = 1;
            colSize = 1;
        } else {
            for(int i = 0; i < range_details.length(); i++) {
                JSONObjectWrapper rowJSONObject = range_details.getJSONObject(i);
                int row_index = rowJSONObject.getInt(ROW_INDEX);
                JSONArrayWrapper row_details = rowJSONObject.getJSONArray(ROW_DETAILS);
                for(int j = 0; j < row_details.length(); j++) {
                    JSONObjectWrapper cellObject = row_details.getJSONObject(j);
                    String content = cellObject.getString(CONTENT);
                    int column_index = cellObject.getInt(COLUMN_INDEX);

                    int relativeSr = (row_index - 1) - sr;
                    int relativeSc = (column_index - 1) - sc;

                    int arrayIndex = relativeSr*colSize + relativeSc;
                    for(int k = arr.size(); k < arrayIndex; k++) {
                        arr.add(Value.EMPTY_VALUE);
                    }
                    if(content.isEmpty()) {
                        arr.add(Value.EMPTY_VALUE);
                    } else {
                        arr.add(Value.getInstance(content, (SpreadsheetSettings) null));
                    }
                }
            }
        }

        return new ZArray(arr, rowSize, colSize);
    }

    public static String extractRSID(String url) {
        if(url == null) {
            return null;
        }
        String sheetOpen="/sheet/open/";//No I18N
        int start_i = url.indexOf(sheetOpen);
        if(start_i != -1) {
            start_i += sheetOpen.length();
            int end_i = url.indexOf("?");
            if(end_i == -1) {
                return url.substring(start_i);
            }
            return url.substring(start_i, end_i);
        }
        return Arrays.stream(url.split("[^A-Za-z0-9]"))//No I18N
                .filter(u -> u.length() > 15)
                .findFirst()
                .orElse(null);
    }

    public static void initWorkbookLink(WorkbookContainer container, ExternalRangeManager externalRangeManager, String rsid, String docOwner, String taskId) {
        try {
            final WorkbookLink workbookLink = externalRangeManager.getWorkbookLink();
            if(workbookLink.isInitializationDone()) {
                LOGGER.log(Level.OFF, "[IMPORTRANGE][WORKBOOKLINK] Initialization already Done (rsid: {0})", new Object[]{rsid});
                return;
            }
            workbookLink.reinitializeWorkbookLinks(rsid, docOwner);
            Set<String> allLinkedSpreadsheet = workbookLink.getAllLinkedSpreadsheet();
            allLinkedSpreadsheet.remove(rsid);
            if(!allLinkedSpreadsheet.isEmpty()) {
                HttpTaskManager.add(
                        new OpenStatusFetch(rsid, taskId)
                                .setContainer(container)
                                .setConsumers(allLinkedSpreadsheet)
                                .setBroadcastOpen(true));
            }
            workbookLink.syncExternalRangeCacheWithWorkbookLink(externalRangeManager.getExternalRangeCache(), rsid);
        } catch(Exception ex) {
            LOGGER.log(Level.OFF, "[IMPORTRANGE] precautionary - can remove after debugging ", ex);
        }
    }

    public static JSONObjectWrapper fetchRangeApiCall(String consumerId, String supplierRsid, JSONArrayWrapper allImportRangesJSONArray, JSONArrayWrapper dataRangesArray, boolean doNotOpenWorkbook, String taskId) throws Exception {
        JSONObjectWrapper actionJsonForRangeContent = ActionObject.getActionInfo(ActionConstants.FETCH_RANGES).toJSON();
        actionJsonForRangeContent.put(EXTERNAL_RANGES, allImportRangesJSONArray);

        actionJsonForRangeContent.put(REQUESTED_EXTERNAL_RANGES, dataRangesArray);
        actionJsonForRangeContent.put(KEY_LINKED_ID, consumerId);
        if(doNotOpenWorkbook) {
            actionJsonForRangeContent.put(ExternalRangeAction.DO_NOT_OPEN_WORKBOOK, doNotOpenWorkbook);
        }
        ExternalRangeUtils.addCommonAttributesForInternalDataApiCall(actionJsonForRangeContent, METHOD_FETCH_RANGES);

        return ExternalRangeUtils.internalDataApiCall(actionJsonForRangeContent, supplierRsid, 300, 7000, taskId);
    }

    public static DataRange getAsDataRange(Range range) {
        return new DataRange(range.getSheet().getName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
    }

    public static DataRange getDataRangeWithSheetNameInsteadOfASN(Workbook workbook, DataRange dr) {
        Sheet sheet = workbook.getSheetByAssociatedName(dr.getAssociatedSheetName());
        if (sheet == null) {
            return null;
        }
        return new DataRange(sheet.getName(), dr.getStartRowIndex(), dr.getStartColIndex(), dr.getEndRowIndex(), dr.getEndColIndex());
    }

    private static int getRow(String s) {
        try {
            return (Integer.parseInt(s.substring(CellUtil.getNumberIndex(s))) - 1);
        } catch (NumberFormatException e) {
            // e.printStackTrace();
            // return 0xffff;
            return -1;
        }
    }

    private static int getColumn(String s) {
        int colnum = 0;
        try {
            int numindex = CellUtil.getNumberIndex(s);
            String s2 = s.toUpperCase();

            int startPos = s.lastIndexOf(".") + 1;
            if (s.charAt(startPos) == '$') {
                startPos++;
            }
            int endPos = numindex;
            if (s.charAt(numindex - 1) == '$') {
                endPos--;
            }

            if (startPos >= endPos) {
                return -1;
            }

            for (int i = startPos; i < endPos; i++) {

                if (i != startPos) {
                    colnum = (colnum + 1) * 26;
                }
                char c = s2.charAt(i);
                if(!(c >= 'A' && c <= 'Z')) {
                    return -1;
                }
                colnum += (int) c - (int) 'A';
            }

        } catch (StringIndexOutOfBoundsException ex) {
            return -1;
        }
        return colnum;
    }

    public static JSONObjectWrapper addSupplierGriddAction(WorkbookContainer container, ExternalRangeManager externalRangeManager, String consumerOwner, String supplierWorkbookUrl, String userId, String taskId) throws Exception {
        String consumerResourceId = container.getResourceId();
        JSONObjectWrapper addSupplierWorkbookResponse = new JSONObjectWrapper();

        String supplierRSID = ExternalRangeUtils.extractRSID(supplierWorkbookUrl);
        if(supplierRSID == null) {
            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 1);
//            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_INCORRECT_URL);
            LOGGER.log(Level.OFF, "[IMPORTRANGE]{0},taskId:{1},supplier:{2} -url incorrect", new Object[]{consumerResourceId, taskId,supplierWorkbookUrl});
            return addSupplierWorkbookResponse;
        }

        externalRangeManager.getFailToAddSuppliers().add(supplierRSID);

        if (!WorkbookLink.isResourceActive(supplierRSID, false)) {
            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 7);
//            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_INCORRECT_URL);
            LOGGER.log(Level.OFF, "[IMPORTRANGE]{0},taskId:{1},supplier:{2} -resource inactive", new Object[]{consumerResourceId, taskId,supplierRSID});
            return addSupplierWorkbookResponse;
        }

        if (externalRangeManager.getExternalRangeCache().getSuppliers().containsKey(supplierRSID)) {
            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 3);
//            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_ALREADY_LINKED);
            LOGGER.log(Level.OFF, "[IMPORTRANGE]{0},taskId:{1},supplier:{2} -already linked", new Object[]{consumerResourceId, taskId,supplierRSID});
            return addSupplierWorkbookResponse;
        }

        if (!AuthorizationUtil.isUserHasAccessForDoc(ShareConstants.PERMISSION_WRITE, userId, consumerResourceId, false)) {
            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 4);
//            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_NO_WRITE_ACCESS);
            LOGGER.log(Level.OFF, "[IMPORTRANGE]{0},taskId:{1},supplier:{2} -no write access", new Object[]{consumerResourceId, taskId,supplierRSID});
            return addSupplierWorkbookResponse;
        }


        if(!AuthorizationUtil.isUserHasAccessForDoc(ShareConstants.PERMISSION_READ, userId, supplierRSID, false)) {
            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 4);
            LOGGER.log(Level.OFF, "[IMPORTRANGE]{0},taskId:{1},supplier:{2} -no read access", new Object[]{consumerResourceId, taskId,supplierRSID});
            return addSupplierWorkbookResponse;
        }

        if (consumerResourceId.equalsIgnoreCase(supplierRSID)) {
            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 6);
//            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_CREATES_CIRCULARCHAIN);
            LOGGER.log(Level.OFF, "[IMPORTRANGE]{0},taskId:{1},supplier:{2} -same spreadsheet", new Object[]{consumerResourceId, taskId,supplierRSID});
            return addSupplierWorkbookResponse;
        }

        //reinitialize WorkbookLinks -in case it is out of sync
//        workbookLink.reinitializeWorkbookLinks(consumerResourceId, null);
//        HttpTaskManager.addOpenStatsFetchTask(container, consumerResourceId, workbookLink.getAllLinkedSpreadsheet(), false, taskId);


        WorkbookLink temLink_consumer = new WorkbookLink();
        WorkbookLink.link(temLink_consumer, consumerResourceId, null);

        WorkbookLink tempLink_supplier = new WorkbookLink();
//        tempLink_supplier.copyCaches(workbookLink);
        WorkbookLink.link(tempLink_supplier, supplierRSID, null);
//        HttpTaskManager.addOpenStatsFetchTask(container, supplierRSID, tempLink.getAllLinkedSpreadsheet(), false, taskId);

        String supplierDocumentOwner = tempLink_supplier.getDocumentOwner(supplierRSID);
        if (supplierDocumentOwner == null) {
            //precautionary
            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 7);
//            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_SUPPLIER_LINKING_FAILED+", because of unknown reason");
            LOGGER.log(Level.OFF, "[IMPORTRANGE]{0},taskId:{1},supplier:{2} -supplier owner null", new Object[]{consumerResourceId, taskId,supplierRSID});
            return addSupplierWorkbookResponse;
        }

//        try {
//            boolean createsCircularReference = workbookLink.isCreatesCircularLink(consumerResourceId, tempLink, supplierRSID);
//            if(createsCircularReference)  {
//                addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 6);
////            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_CREATES_CIRCULARCHAIN);
//                return addSupplierWorkbookResponse;
//            }
//        } catch (Exception ex) {
//            LOGGER.log(Level.SEVERE, DataAPIConstants.IMPORTRANGE_TAG+ DataAPIConstants.ERRMSG_CIRCULARCHAIN_CHECK_FAIL, ex);
//            LOGGER.log(Level.SEVERE, "[IMPORTRANGE] Could not complete Circular-Chain check[consumerResourceId:{0} ,supplierRSID:{1} ", new Object[]{consumerResourceId,supplierRSID});
//
//            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 5);
////            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_CIRCULARCHAIN_CHECK_FAIL);
//            return addSupplierWorkbookResponse;
//        }

        if (temLink_consumer.isExceedsLinkSize(consumerResourceId, tempLink_supplier, supplierRSID)) {
            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 11);
//            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_EXCEEDS_LINK_SIZE);
            return addSupplierWorkbookResponse;
        }

        boolean isSupplierAdded = ExternalRangeUtils.addOrUpdateSupplierToDB(supplierDocumentOwner, consumerResourceId, supplierRSID, Long.valueOf(userId), ADD_SUPPLIER_RESPONSE.SUCCESS);

        if(!(isSupplierAdded)) {
            addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 7);
//            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_SUPPLIER_LINKING_FAILED+", because of unknown reason");
            LOGGER.log(Level.OFF, "[IMPORTRANGE]{0},taskId:{1},supplier:{2} -supplier db update fail", new Object[]{consumerResourceId, taskId,supplierRSID});
            return addSupplierWorkbookResponse;
        }


        if(!supplierDocumentOwner.equals(consumerOwner)) {
            //since the DB will be same no need to add duplicate entry.
            if(!ExternalRangeUtils.addOrUpdateSupplierToDB(consumerOwner, consumerResourceId, supplierRSID, Long.valueOf(userId), ADD_SUPPLIER_RESPONSE.SUCCESS)) {
                addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 7);
//            addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_SUPPLIER_LINKING_FAILED+", because of unknown reason");
                LOGGER.log(Level.OFF, "[IMPORTRANGE]{0},taskId:{1},supplier:{2} -consumer db update fail", new Object[]{consumerResourceId, taskId,supplierRSID});
                return addSupplierWorkbookResponse;
            }
        }

        externalRangeManager.getExternalRangeCache().getSuppliers().put(supplierRSID, Long.valueOf(userId));

        WorkbookLink workbookLink = externalRangeManager.getWorkbookLink();
//        workbookLink.copyCaches(tempLink_supplier);
        workbookLink.setEffectivelyLiveDirectConsumers(consumerResourceId);
        workbookLink.reinitializeWorkbookLinks(consumerResourceId, null);
        HttpTaskManager.add(
                new OpenStatusFetch(consumerResourceId, taskId)
                        .setContainer(container)
                        .setConsumers(workbookLink.getAllLinkedSpreadsheet())
                        .setBroadcastOpen(false));

        externalRangeManager.getFailToAddSuppliers().remove(supplierRSID);
        externalRangeManager.getExternalRangeCache().getReferredUnlinkedSuppliers().remove(supplierRSID);

        workbookLink.broadcastAllToResetLinkExcept(consumerResourceId, taskId);
        //todo startApiFetch should start only after, response for the boradCastToRestLink is received.

        ResourceInfo resourceInfo = ZohoFS.getResourceInfo(supplierRSID);
        String documentName = resourceInfo.getName();

        addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 0);
//        addSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.SUCCESSMSG_SUPPLIER_ADDED);
        addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.RSID), supplierRSID);
        addSupplierWorkbookResponse.put(String.valueOf(CommandConstants.DOCUMENT_NAME), documentName);

        if(externalRangeManager.getExternalRangeCache().getImportRangeDependents().getOrDefault(supplierRSID, new HashMap<>()).isEmpty()) {
            return addSupplierWorkbookResponse;
        }

        Map<String, Set<DataRange>> importRangesToBeFetched = new HashMap<>();
        importRangesToBeFetched.put(supplierRSID, new HashSet<>(externalRangeManager.getExternalRangeCache().getImportRangeDependents().getOrDefault(supplierRSID, new HashMap<>()).keySet()));
        externalRangeManager.startApiFetch(container, false, "because supplier added", null, true, importRangesToBeFetched, taskId, false);//No I18N

        return addSupplierWorkbookResponse;
    }

    public static JSONObjectWrapper removeSupplierGridAction(WorkbookContainer container, String currentUserZuid, String consumerOwner, String supplierRSID, ExternalRangeManager externalRangeManager, String taskId) {
        String consumerRSID = container.getResourceId();
        JSONObjectWrapper removeSupplierWorkbookResponse = new JSONObjectWrapper();

        if (!WorkbookLink.isResourceActive(supplierRSID, true)) {
            removeSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 10);
//            removeSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_SUPPLIER_REMOVAL_FAILED);
            return removeSupplierWorkbookResponse;
        }

        if(!AuthorizationUtil.isUserHasAccessForDoc(ShareConstants.PERMISSION_WRITE, currentUserZuid, consumerRSID, false)) {
            removeSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 8);
//            removeSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_NO_EDIT_ACCESS);
            return removeSupplierWorkbookResponse;
        }
        if (!externalRangeManager.getExternalRangeCache().getSuppliers().keySet().contains(supplierRSID)) {
            removeSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 9);
//            removeSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_SUPPLIER_NOT_LINKED);
            return removeSupplierWorkbookResponse;
        }

        if (!externalRangeManager.removeSupplier(supplierRSID, consumerRSID, consumerOwner)) {
            removeSupplierWorkbookResponse.put(String.valueOf(CommandConstants.MSG), 10);
//            removeSupplierWorkbookResponse.put(JSONConstants.MESSAGE, DataAPIConstants.ERRMSG_SUPPLIER_REMOVAL_FAILED);
            return removeSupplierWorkbookResponse;
        }

        externalRangeManager.getWorkbookLink().broadCastToResetLink(consumerRSID, new AtomicInteger(0), taskId);
        externalRangeManager.getWorkbookLink().reinitializeWorkbookLinks(consumerRSID, consumerOwner);
        HttpTaskManager.add(
                new OpenStatusFetch(consumerRSID, taskId)
                        .setContainer(container)
                        .setConsumers(externalRangeManager.getWorkbookLink().getAllLinkedSpreadsheet())
                        .setBroadcastOpen(false));

        removeSupplierWorkbookResponse.put(String.valueOf(CommandConstants.RSID), supplierRSID);

        if(externalRangeManager.getExternalRangeCache().getImportRangeDependents().getOrDefault(supplierRSID, new HashMap<>()).isEmpty()) {
            return removeSupplierWorkbookResponse;
        }

        Map<String, Set<DataRange>> importRangesToBeFetched = new HashMap<>();
        importRangesToBeFetched.put(supplierRSID, new HashSet<>(externalRangeManager.getExternalRangeCache().getImportRangeDependents().getOrDefault(supplierRSID, new HashMap<>()).keySet()));
        externalRangeManager.startApiFetch(container, false, "because supplier removed by consumer", null, true, importRangesToBeFetched, taskId, false);//No I18N

        externalRangeManager.getExternalRangeCache().removeCachedValues(supplierRSID);
//        externalRangeManager.getFailToAddSuppliers().add(supplierRSID);
        externalRangeManager.getExternalRangeCache().getReferredUnlinkedSuppliers().add(supplierRSID);

        return removeSupplierWorkbookResponse;
    }
    public static List<DataRange> getRangesToNotify(Workbook workBook, int majorAction, List<Cell> formulaCells, List<Range> destRanges, List<DataRange> listOfSourceDataRanges, List<Range> refreshedPivotRangeList) {
        List<DataRange> rangesToNotify = new ArrayList<>();
        if (formulaCells != null) {
            for (Cell formulaCell : formulaCells) {
                rangesToNotify.add(ExternalRangeUtils.getAsDataRangeWithSheetName(formulaCell));
            }
        }

        if (destRanges != null) {
            for (Range destRange : destRanges) {
                switch (majorAction) {
                    case ActionConstants.INSERT_ROW:
                        if(destRange.getStartRowIndex() <= destRange.getSheet().getUsedRowIndex()) {
                            rangesToNotify.add(new DataRange(destRange.getSheet().getName(),
                                    destRange.getStartRowIndex(), 0,
                                    destRange.getSheet().getUsedRowIndex(), destRange.getSheet().getUsedColumnIndex()));
                        }
                        break;
                    case ActionConstants.DELETE_ROW:
                        if(destRange.getStartRowIndex() <= destRange.getSheet().getUsedRowIndex()+destRange.getRowSize()) {
                            rangesToNotify.add(new DataRange(destRange.getSheet().getName(),
                                    destRange.getStartRowIndex(), 0,
                                    destRange.getSheet().getUsedRowIndex()+destRange.getRowSize(), destRange.getSheet().getUsedColumnIndex()));
                        }
                        break;
                    case ActionConstants.INSERT_COL:
                        if(destRange.getStartColIndex() <= destRange.getSheet().getUsedColumnIndex()) {
                            rangesToNotify.add(new DataRange(destRange.getSheet().getName(),
                                    0, destRange.getStartColIndex(),
                                    destRange.getSheet().getUsedRowIndex(),destRange.getSheet().getUsedColumnIndex()));
                        }
                        break;
                    case ActionConstants.DELETE_COL:
                        if(destRange.getStartColIndex() <= destRange.getSheet().getUsedColumnIndex()+destRange.getColSize()) {
                            rangesToNotify.add(new DataRange(destRange.getSheet().getName(),
                                    0, destRange.getStartColIndex(),
                                    destRange.getSheet().getUsedRowIndex(),destRange.getSheet().getUsedColumnIndex()+destRange.getColSize()));
                        }
                        break;
                    default:
                        rangesToNotify.add(ExternalRangeUtils.getAsDataRange(destRange));
                }
            }
        }
        if (listOfSourceDataRanges != null) {
            //for cut-paste
            for (DataRange lsdr : listOfSourceDataRanges) {
                DataRange dataRangeWithSheetNameInsteadOfASN = ExternalRangeUtils.getDataRangeWithSheetNameInsteadOfASN(workBook, lsdr);
                if (dataRangeWithSheetNameInsteadOfASN != null) {
                    rangesToNotify.add(dataRangeWithSheetNameInsteadOfASN);
                }
            }
        }
        if (refreshedPivotRangeList != null) {
            for (Range refreshedPivotRange : refreshedPivotRangeList) {
                rangesToNotify.add(ExternalRangeUtils.getAsDataRange(refreshedPivotRange));
            }
        }

        return rangesToNotify;
    }

    public static final String RANGE_DETAILS = "rd";//No I18N
    private static final String ROW_INDEX = "r";//No I18N
    private static final String ROW_DETAILS = "d";//No I18N
    private static final String COLUMN_INDEX = "c";//No I18N
    private static final String CONTENT = "v";//No I18N

    public static JSONObjectWrapper getRangeDetails(Sheet sheet, int startRow, int startCol, int endRow, int endCol) {
        JSONObjectWrapper jObj = new JSONObjectWrapper();
        JSONArrayWrapper rowArray = getRangeAsArray(sheet, startRow, startCol, endRow, endCol);
        jObj.put(RANGE_DETAILS, rowArray);

        return jObj;
    }

    private static JSONArrayWrapper getRangeAsArray(Sheet sheet, int startRow, int startCol, int endRow, int endCol) {
        endRow = Math.min(endRow, sheet.getUsedRowIndex());
        endCol = Math.min(endCol, sheet.getUsedColumnIndex());
        JSONArrayWrapper rowArray = new JSONArrayWrapper();
        for (int r = startRow; r <= endRow; r++) {
            com.adventnet.zoho.websheet.model.Row row = sheet.getRow(r);
            JSONObjectWrapper rowJson = getRowJson(row, startCol, endCol, r == endRow);
            if (rowJson != null) {
                rowArray.put(rowJson);
            }
        }
        return rowArray;
    }

    private static JSONObjectWrapper getRowJson(com.adventnet.zoho.websheet.model.Row row, int startCol, int endCol, boolean isLastCell) {
        JSONObjectWrapper rowJson = null;

        JSONArrayWrapper cellArray = new JSONArrayWrapper();
        Cell cell = null;
        for (int c = startCol; c <= endCol; c++) {
            cell = row.getSheet().getReadOnlyCellFromShell(row.getRowIndex(), c).getCell();
            if (!(cell == null || cell.isEmpty() || cell.getContent() == null)) {
                cellArray.put(getCellDetails(cell, c));
            } else if (isLastCell) {
                cellArray.put(getEmptyCellJSON(c));
            }
        }
        if (!cellArray.isEmpty()) {
            rowJson = new JSONObjectWrapper();
            rowJson.put(ROW_INDEX, row.getRowIndex() + 1);
            rowJson.put(ROW_DETAILS, cellArray);
        }
        return rowJson;
    }

    private static JSONObjectWrapper getCellDetails(Cell cell, int colIndex)   //for repeated cell col index might differ
    {
        JSONObjectWrapper cellJson = new JSONObjectWrapper();
        cellJson.put(COLUMN_INDEX, colIndex + 1);
        cellJson.put(CONTENT, cell.getContent());
        return cellJson;
    }

    private static JSONObjectWrapper getEmptyCellJSON(int colIndex) {
        JSONObjectWrapper cellJson = new JSONObjectWrapper();
        cellJson.put(COLUMN_INDEX, colIndex + 1);
        cellJson.put(CONTENT, "");
        return cellJson;
    }

    public static String getRangeDetailsInCSV(Sheet sheet, int startRow, int startCol, int endRow, int endCol) throws Exception {
        final Instant instant = Instant.now().plusSeconds(10);
        Writer writer = new StringWriter();
        CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT);
        final String empty_string = "";//No I18N

        endRow = Math.min(endRow, sheet.getUsedRowIndex());
        endCol = Math.min(endCol, sheet.getUsedColumnIndex());

        int r = startRow;
        while (r <= endRow) {
            com.adventnet.zoho.websheet.model.Row row = null;
            if(r < sheet.getRows().size()) {
                row = sheet.getRows().get(r);
            }
            if(r == startRow) {
                int temp_r = r - 1;
                while (row == null && temp_r >-1) {
                    if(temp_r < sheet.getRows().size()) {
                        row = sheet.getRows().get(temp_r);
                    }
                    temp_r--;
                }
                if(row != null && (row.getRowIndex()+row.getRowsRepeated() < r)) {
                    row = null;
                }
            } else if(r >= sheet.getRows().size()) {
                for (int i = r; i <= endRow; i++) {
                    for (int j = startCol; j <= endCol; j++) {
                        csvPrinter.print(empty_string);
                    }
                    if(i < endRow) {
                        csvPrinter.println();
                    }
                }
                break;
            }

            if(row == null) {
                for (int j = startCol; j <= endCol; j++) {
                    csvPrinter.print(empty_string);
                }
                if(r < endRow) {
                    csvPrinter.println();
                }
                r++;
                continue;
            }
            if(Instant.now().isAfter(instant)) {
                throw new Exception("response preparation taking too much time");
            }
            List<String> col_values = new ArrayList<>();
            int c = startCol;
            while (c <= endCol) {
                Cell cell = null;
                if (c < row.getCells().size()) {
                    cell = row.getCells().get(c);
                }
                if (c == startCol) {
                    int temp_c = c - 1;
                    while (cell == null && temp_c > -1) {
                        if (temp_c < row.getCells().size()) {
                            cell = row.getCells().get(temp_c);
                        }
                        temp_c--;
                    }
                    if (cell != null && (cell.getColumnIndex() + cell.getColsRepeated() < c)) {
                        cell = null;
                    }
                } else if (c >= row.getCells().size()) {
                    for (int i = c; i <= endCol; i++) {
                        col_values.add(empty_string);
                    }
                    break;
                }

                String valueString;

                if (!(cell == null || cell.getValue().getValue() == null)) {
                    final Value value = cell.getValue();
                    valueString = value.getValueString(SpreadsheetSettings.defaultSpreadsheetSettings);
                    final Object rawValue = value.getRawValue();
                    if(value.isPrefixApos() || (rawValue != null && rawValue instanceof ZSString && ((ZSString)rawValue).getConvertedValue(SpreadsheetSettings.defaultSpreadsheetSettings).getType() == Cell.Type.FLOAT)) {
                        valueString = "'".concat(valueString);
                    }
//                    if (r == startRow && c == startCol) {
//                        LOGGER.log(Level.OFF, "[IMPORTRANGE]debugging sent-value : {0}, sheet-name: {1}, r : {2}", new Object[]{valueString, sheet.getName(), cell.getCellRef()});
//                    }
                } else {
                    valueString = empty_string;
                }
                final int colsRepeated = cell == null ? 1 : cell.getColsRepeated() -(c - cell.getColumnIndex());
                int current_endCol = Math.min(c + colsRepeated - 1, endCol);
                for (int i = c; i <= current_endCol; i++) {
                    col_values.add(valueString);
                }
                c += colsRepeated;
            }

            int rowsRepeated = row.getRowsRepeated() - (r - row.getRowIndex());
            int current_endRow = Math.min(r+ rowsRepeated -1, endRow);
            for (int i = r; i <= current_endRow; i++) {
                for (String col_value : col_values) {
                    csvPrinter.print(col_value);
                }
                if(r < endRow) {
                    csvPrinter.println();
                }
            }
            r += rowsRepeated;
        }
        long millis = Duration.between(instant.minusSeconds(10), Instant.now()).toMillis();
        if(millis > 1000) {
            LOGGER.log(Level.OFF, "{0}", millis);
        }
        return writer.toString();
    }

    public static CompactZArray convert(String range_details, int row_size, int col_size, String supplier, DataRange dataRange) throws IOException {
        try(final CSVParser csvParser = new CSVParser(new StringReader(range_details), CSVFormat.DEFAULT)) {
            final List arr = new ArrayList();
            int usedRowSize = 0;
            int usedColSize = 0;

            Iterator<CSVRecord> csvRecordIterator = csvParser.iterator();
            if (csvRecordIterator.hasNext()) {
                usedRowSize++;
                Iterator<String> stringIterator = csvRecordIterator.next().iterator();
                while (stringIterator.hasNext()) {
                    usedColSize++;
                    String next = stringIterator.next();
                    if (next.isEmpty()) {
                        arr.add(Value.EMPTY_VALUE);
                    } else {
//                        if(usedColSize == 1 && usedRowSize == 1) {
//                            LOGGER.log(Level.OFF,"[IMPORTRANGE]debugging received-value : {0}", new Object[]{next});
//                        }
                        arr.add(Value.getInstance(next, SpreadsheetSettings.defaultSpreadsheetSettings));
                    }
                }
            }

            while (csvRecordIterator.hasNext()) {
                usedRowSize++;
                Iterator<String> stringIterator = csvRecordIterator.next().iterator();
                while (stringIterator.hasNext()) {
                    String next = stringIterator.next();
                    if (next.isEmpty()) {
                        arr.add(Value.EMPTY_VALUE);
                    } else {
                        arr.add(Value.getInstance(next, SpreadsheetSettings.defaultSpreadsheetSettings));
                    }
                }
            }

            if (usedRowSize == 0 || usedColSize == 0) {
                arr.add(Value.EMPTY_VALUE);
                usedRowSize = 1;
                usedColSize = 1;
            }

            return new CompactZArray(arr, usedRowSize, usedColSize, row_size, col_size, supplier, dataRange);
        }
    }
    public static byte[] compress(byte[] input) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try (DeflaterOutputStream deflaterOutputStream = new DeflaterOutputStream(os)) {
            deflaterOutputStream.write(input);
        }
        return os.toByteArray();
    }

    static String uncompress(JSONArrayWrapper input) throws IOException {
        int length = input.length();
        byte[] conbyte = new byte[length];
        for (int i = 0; i < length; i++) {
            conbyte[i] = (byte)input.getInt(i);
        }

        try (InputStream byteArrayInputStream = new ByteArrayInputStream(conbyte); InputStream inflaterInputStream = new InflaterInputStream(byteArrayInputStream)) {
            return CharStreams.toString(new InputStreamReader(inflaterInputStream));
        }
    }

    public static String rangeJsonToString(JSONObjectWrapper jsonObject) {
        try {
            return new StringBuilder()
                    .append("'").append(jsonObject.getString(JSONConstants.SHEET_NAME)).append("'.")
                    .append(CellUtil.getColumnReference(jsonObject.getInt(JSONConstants.START_COLUMN))).append(jsonObject.getInt(JSONConstants.START_ROW) + 1)
                    .append(":")
                    .append(CellUtil.getColumnReference(jsonObject.getInt(JSONConstants.END_COLUMN))).append(jsonObject.getInt(JSONConstants.END_ROW) + 1)
                    .toString();
        } catch (Exception e) {
            LOGGER.log(Level.OFF, String.join("","[IMPORTRANGE]", jsonObject.toString()), e);
            return "";
        }
    }

    public static String rangeJsonToString(JSONArrayWrapper jsonArray) {
        try {
            StringJoiner stringJoiner = new StringJoiner(",");
            for (int i = 0; i < jsonArray.length(); i++) {
                stringJoiner.add(rangeJsonToString(jsonArray.getJSONObject(i)));
            }

            return stringJoiner.toString();
        } catch (Exception e) {
            LOGGER.log(Level.OFF, String.join("","[IMPORTRANGE]", jsonArray.toString()), e);
            return "";
        }
    }
    public static List<Cell> getImportrangeFormulaCells(Workbook workbook) {
        List<Cell> importRangeCells = new ArrayList<>();
        for (Sheet workbookSheet : workbook.getSheetList()) {
            importRangeCells.addAll(workbookSheet.getFormulaCells()
                    .stream()
                    .filter(c-> c.getExpression().hasImportRangeFormula(workbook))
                    .collect(Collectors.toList()));
        }
        return importRangeCells;
    }

    public static List<DataRange> getMergedRanges(Set<DataRange> dataRanges) {
        Map<String, Map<Integer, Set<Integer>>> asn_colIndex_map = new HashMap<>();
        for (DataRange dataRange : dataRanges) {
            Map<Integer, Set<Integer>> col_row_index_map = asn_colIndex_map.get(dataRange.getAssociatedSheetName());
            if (col_row_index_map == null) {
                col_row_index_map = new TreeMap<>();
                asn_colIndex_map.put(dataRange.getAssociatedSheetName(), col_row_index_map);
            }
            for (int i = dataRange.getStartColIndex(); i <= dataRange.getEndColIndex(); i++) {
                for (int j = dataRange.getStartRowIndex(); j <= dataRange.getEndRowIndex(); j++) {
                    Set<Integer> row_index_set = col_row_index_map.get(i);
                    if (row_index_set == null) {
                        row_index_set = new TreeSet<>();
                        col_row_index_map.put(i, row_index_set);
                    }
                    row_index_set.add(j);
                }
            }
        }
        List<DataRange> mergedRanges = new ArrayList<>();
        for (Map.Entry<String, Map<Integer, Set<Integer>>> asn_row_index_entrySet : asn_colIndex_map.entrySet()) {
            final String asn = asn_row_index_entrySet.getKey();

            int start_col = -1;
            int start_row = -1;
            int end_col = -1;
            int end_row = -1;
            int previous_row = -1;
            for (Map.Entry<Integer, Set<Integer>> col_row_index_entrySet : asn_row_index_entrySet.getValue().entrySet()) {
                final Integer col_index = col_row_index_entrySet.getKey();
                for (Integer row_index : col_row_index_entrySet.getValue()) {
                    if (start_col == -1) {
                        start_col = col_index;
                        start_row = row_index;
                        end_col = col_index;
                        end_row = row_index;
                        previous_row = row_index;
                    } else {
                        if (col_index == end_col) {
                            if (row_index - 1 == previous_row) {
                                previous_row = row_index;
                                if (previous_row > end_row) {
                                    if(col_index == start_col) {
                                        end_row = previous_row;
                                    } else {
                                        mergedRanges.add(new DataRange(asn,  start_row, start_col,  end_row, end_col));
                                        start_col = col_index;
                                        start_row = row_index;
                                        end_col = col_index;
                                        end_row = row_index;
                                        previous_row = row_index;
                                    }
                                }
                            } else {
                                mergedRanges.add(new DataRange(asn,  start_row, start_col,  end_row, end_col));
                                start_col = col_index;
                                start_row = row_index;
                                end_col = col_index;
                                end_row = row_index;
                                previous_row = row_index;
                            }
                        } else {
                            if(col_index - 1 == end_col) {
                                if (row_index == start_row) {
                                    if(previous_row == end_row) {
                                        end_col = col_index;
                                        previous_row = start_row;
                                    } else {
                                        mergedRanges.add(new DataRange(asn,  start_row, start_col,  end_row, end_col-1));
                                        start_col = end_col;
                                        start_row = row_index;
                                        end_col = col_index;
                                        end_row = previous_row;
                                        previous_row = row_index;
                                    }
                                } else {
                                    mergedRanges.add(new DataRange(asn,  start_row, start_col,  end_row, end_col));
                                    start_col = col_index;
                                    start_row = row_index;
                                    end_col = col_index;
                                    end_row = row_index;
                                    previous_row = row_index;
                                }
                            } else {
                                mergedRanges.add(new DataRange(asn,  start_row, start_col,  end_row, end_col));
                                start_col = col_index;
                                start_row = row_index;
                                end_col = col_index;
                                end_row = row_index;
                                previous_row = row_index;
                            }
                        }
                    }
                }
            }

            mergedRanges.add(new DataRange(asn, start_row, start_col, end_row, end_col));
        }
        return mergedRanges;
    }

    public static String last4Chars(String s) {
        if(s == null || s.length()<5) {
            return s;
        }
        return s.substring(s.length()-4);
    }
    public static String last4Chars(Collection<String> s) {
        if(s == null) {
            return "";
        }
        StringJoiner stringJoiner = new StringJoiner(",","[","]");
        for (String s1 : s) {
            stringJoiner.add(last4Chars(s1));
        }
        return stringJoiner.toString();
    }

    /**
     * links operational suppliers.
     * unlinks non-operational suppliers.
     * @param container
     * @param workBook
     * @param actionJson
     * @param importrangeTaskID
     * @param majorAction
     * @return
     */
    public static List<Cell> manage_suppliers(WorkbookContainer container, Workbook workBook, JSONObjectWrapper actionJson, String importrangeTaskID, int majorAction) {
        if(!(actionJson.has(JSONConstants.IS_EXECUTED) && actionJson.getBoolean(JSONConstants.IS_EXECUTED)) && majorAction != ActionConstants.RE_EVALUATE_IMPORTRANGE && majorAction != ActionConstants.WORKBOOK_UPDATE && majorAction != ActionConstants.RECALCULATE) {
            Set<String> referredUnlinkedSuppliers = container.getExternalRangeManager().getExternalRangeCache().getReferredUnlinkedSuppliers();
            if (!referredUnlinkedSuppliers.isEmpty()) {
                container.getExternalRangeManager().getExternalRangeCache().clearOperationalSupplierSet();

                List<Cell> importRangeCells = ExternalRangeUtils.getImportrangeFormulaCells(workBook);
                ReEvaluate.getReEvaluateDependents(null, importRangeCells, true);
                container.getExternalRangeManager().remove_non_operational_suppliers(container.getResourceId());

                HashSet<String> unlinkedSuppliers = new HashSet<>(referredUnlinkedSuppliers);
                unlinkedSuppliers.removeAll(container.getExternalRangeManager().getFailToAddSuppliers());//no need to try to link the supplier if it's already been tried once.
                for (String unlinkedSupplier : unlinkedSuppliers) {
                    try {
                        JSONObjectWrapper addSupplierWorkbookResponse = ExternalRangeUtils.addSupplierGriddAction(
                                container, container.getExternalRangeManager(),
                                container.getDocOwner(),
                                unlinkedSupplier,
                                actionJson.getString(JSONConstants.ZUID), HttpTaskManager.getNewTaskIdIfNull(importrangeTaskID));
                    } catch(Exception e) {
                        LOGGER.log(Level.OFF, String.join("", "could not link: ", unlinkedSupplier), e);
                    }
                }
            }
            Set<DataRange> supplierDependents = container.getExternalRangeManager().getSupplierDependents();
            if (!supplierDependents.isEmpty()) {
                List<Cell> importrangeFormulaCells = new ArrayList<>();
                for (DataRange supplierDependent : supplierDependents) {
                    Cell asCell = ExternalRangeUtils.getAsCell(supplierDependent, workBook);
                    if (asCell != null) {
                        importrangeFormulaCells.add(asCell);
                    }
                }
                return importrangeFormulaCells;
            }
        }

        return null;
    }
}
