//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.ConditionalFormatEntry.DataBarObj;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.style.ConditionalStyleResponse.CellObject;
import com.adventnet.zoho.websheet.model.style.ConditionalStyleResponse.ConditionalStyleCellStyles;
import com.adventnet.zoho.websheet.model.style.SheetStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.writer.zs.XMLWriter;
import com.adventnet.zoho.websheet.model.zsparser.Names;
import com.singularsys.jep.EvaluationException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class DataBar implements ConditionalStyle
{
    public static final Logger LOGGER = Logger.getLogger(DataBar.class.getName());
    private final List<ConditionalFormatEntry> dataBarEntries;
    private final Direction db_direction;
    private final Type fill_type;

    private final Type border_type;
    private final Axis axis_position;
    private Boolean hasPercentileEntry = null;
    private Boolean hasFormulaEntry = null;

    private final ZSColor positiveFillColor;
    private final ZSColor negativeFillColor;
    private final ZSColor positiveBorderColor;
    private final ZSColor negativeBorderColor;
    private final ZSColor axisColor;

    public enum Direction{
        CONTEXT,
        LEFT_TO_RIGHT,
        RIGHT_TO_LEFT
    }
    public enum Type{
        SOLID,
        GRADIENT,
        NONE
    }
    public enum Axis{
        AUTOMATIC,
        MIDPOINT,
        NONE
    }

    public DataBar(List<ConditionalFormatEntry> dataBarEntries, Direction db_direction, Type fill_type, ZSColor positive_fill_color, ZSColor negative_fill_color, Type border_type, ZSColor positive_border_color, ZSColor negative_border_color, Axis axis_position, ZSColor axis_color)
    {
        if(dataBarEntries == null || dataBarEntries.size() != 2)
        {
            throw new IllegalArgumentException("A data bar should have 2 DataBarEntries : "+dataBarEntries);//No I18N
        }
        this.dataBarEntries = dataBarEntries;
        this.db_direction = db_direction;
        this.fill_type = fill_type;
        this.positiveFillColor = positive_fill_color;
        this.negativeFillColor = negative_fill_color;
        this.border_type = border_type;
        this.positiveBorderColor = positive_border_color;
        this.negativeBorderColor = negative_border_color;
        this.axis_position = axis_position;
        this.axisColor = axis_color;
    }

    public Direction getDB_direction() {
        return db_direction;
    }

    public Type getFill_type() {
        return fill_type;
    }

    public ZSColor getPositive_fill_color() {
        return positiveFillColor;
    }

    public ZSColor getNegative_fill_color() {
        return negativeFillColor;
    }

    public Type getBorder_type() {
        return border_type;
    }

    public ZSColor getPositive_border_color() {
        return positiveBorderColor;
    }

    public ZSColor getNegative_border_color() {
        return negativeBorderColor;
    }

    public Axis getAxis_position() {
        return axis_position;
    }

    public ZSColor getAxis_color() {
        return axisColor;
    }

    @Override
    public String getStyleXML(Workbook workbook, int baseRowIndex, int baseColIndex, boolean autoColor, boolean hideText) {


        StringBuilder dataBarXML = new StringBuilder();
        String dataBarStr = XMLWriter.createStartTagOpen("calcext:data-bar", //NO I18N
                                                                     getAttributes(),
                                                                     getValues(workbook.getTheme(), autoColor, hideText),
                                                                     false);
        dataBarXML.append(dataBarStr);

        for(ConditionalFormatEntry entry : getConditionalStyleEntries())
        {
            String dataBarEntryStr = XMLWriter.createStartTagClose("calcext:formatting-entry", //NO I18N
                                                                         entry.getDBAttributes(),
                                                                         entry.getDBValues(workbook, baseRowIndex, baseColIndex),
                                                                         true);
            dataBarXML.append(dataBarEntryStr);
        }
        dataBarXML.append("</calcext:data-bar>"); //No I18N
        return dataBarXML.toString();
    }

    private String[] getAttributes()
    {
        String[] attrs = new String[]{

            XMLWriter.toString(Names.A_GRADIENT),
            XMLWriter.toString(Names.A_C_S_AUTO_COLOR),
            XMLWriter.toString(Names.A_C_S_SHOW_VALUE),
            XMLWriter.toString(Names.A_DATA__BAR__DIRECTION),
            XMLWriter.toString(Names.A_DATA__BAR__FILL__TYPE),
            XMLWriter.toString(Names.A_FILL_POSITIVE__COLOR),
            XMLWriter.toString(Names.A_FILL_POSITIVE_THEME_COLOR),
            XMLWriter.toString(Names.A_FILL_POSITIVE_TINT_COLOR),
            XMLWriter.toString(Names.A_FILL_NEGATIVE__COLOR),
            XMLWriter.toString(Names.A_FILL_NEGATIVE_THEME_COLOR),
            XMLWriter.toString(Names.A_FILL_NEGATIVE_TINT_COLOR),
            XMLWriter.toString(Names.A_DATA__BAR__BORDER__TYPE),
            XMLWriter.toString(Names.A_BORDER__POSITIVE__COLOR),
            XMLWriter.toString(Names.A_BORDER__POSITIVE_THEME_COLOR),
            XMLWriter.toString(Names.A_BORDER__POSITIVE_TINT_COLOR),
            XMLWriter.toString(Names.A_BORDER__NEGATIVE__COLOR),
            XMLWriter.toString(Names.A_BORDER__NEGATIVE_THEME_COLOR),
            XMLWriter.toString(Names.A_BORDER__NEGATIVE_TINT_COLOR),
            XMLWriter.toString(Names.A_AXIS__POSITION),
            XMLWriter.toString(Names.A_AXIS__COLOR),
            XMLWriter.toString(Names.A_AXIS_THEME_COLOR),
            XMLWriter.toString(Names.A_AXIS_TINT_COLOR),
        };
        return attrs;
    }

    private String[] getValues(ZSTheme zsTheme, boolean autoColor, boolean hideText)
    {
        String[] vals = new String[]{

                                    getFill_type() == Type.SOLID ? Boolean.FALSE.toString() : null,
                                    String.valueOf(autoColor),
                                    String.valueOf(!hideText),
                                    getDB_direction().toString().toLowerCase(),
                                    getFill_type().toString().toLowerCase(),
            ZSColor.getHexColor(getPositive_fill_color(), zsTheme),
            getPositive_fill_color() != null ? getPositive_fill_color().getThemeColorToWrite() : null,
            getPositive_fill_color() != null ? getPositive_fill_color().getColorTintToWrite() : null,
            ZSColor.getHexColor(getNegative_fill_color(), zsTheme),
            getNegative_fill_color() != null ? getNegative_fill_color().getThemeColorToWrite() : null,
            getNegative_fill_color() != null ? getNegative_fill_color().getColorTintToWrite() : null,
                                    getBorder_type().toString().toLowerCase(),
            ZSColor.getHexColor(getPositive_border_color(), zsTheme),
            getPositive_border_color() != null ? getPositive_border_color().getThemeColorToWrite() : null,
            getPositive_border_color() != null ? getPositive_border_color().getColorTintToWrite() : null,
            ZSColor.getHexColor(getNegative_border_color(), zsTheme),
            getNegative_border_color() != null ? getNegative_border_color().getThemeColorToWrite() : null,
            getNegative_border_color() != null ? getNegative_border_color().getColorTintToWrite() : null,
                                    getAxis_position().toString().toLowerCase(),
            ZSColor.getHexColor(getAxis_color(), zsTheme),
            getAxis_color() != null ? getAxis_color().getThemeColorToWrite() : null,
            getAxis_color() != null ? getAxis_color().getColorTintToWrite() : null,
                            };
        return vals;
    }

    @Override
    public boolean isConditionalStyleExists(ConditionalStyleCellStyles csCellStyles)
    {
        return csCellStyles.isDataBarExists();
    }

    @Override
    public void setConditionalStyleResult(ConditionalStyleCellStyles csCellStyles, Object csResult)
    {
        csCellStyles.setDatabar((DataBarObj)csResult);
    }

    @Override
    public Object getResult(Cell cell, ConditionalStyleObject conditionalStyleFormat)
    {
        try{
            Double value = FunctionUtil.objectToNumber(cell.getValue().getValue()).doubleValue();
            List<Double> intervals = conditionalStyleFormat.getIntervals();
            double min = intervals.get(0);
            double max = intervals.get(intervals.size()-1);

            if(axis_position == Axis.MIDPOINT) {
                double temp = Math.max(Math.abs(min), Math.abs(max));
                min = -temp;
                max = temp;
            }

            // When the min is automatic, the min vaue will be the lowest value in the range. Should set that to zero if all the values in range are positive.

            // If the value is less than the min interval, takes the min width and axispoint.
            // If the value is more then the max interval, takes the max width and axispoint.
            //value = value < min ? min : value > max ? max : value;

            double noOfGroups = max - min;
            double noOfPositiveGroups = max - Math.max(0, min);
            double noOfNegativeGroups = Math.min(0, max) - min;//Math.abs(min) - 0;

            double axisPoint;
            switch(getAxis_position())
            {
                case AUTOMATIC:
                    double point = value < 0 ? 100 : 0;
                    if(min < 0 && max >= 0)
                    {
                        point = ((noOfGroups - max) * 100) / noOfGroups;
                    }
                    //axisPoint = getDB_direction() == Direction.RIGHT_TO_LEFT ? 100 - point : point;
                    axisPoint = point;
                    break;
                case MIDPOINT:
                    axisPoint = 50;
                    break;
                default: //case NONE :
                    axisPoint = 0;
                    break;
            }
            value = Math.min(Math.max(value, min), max);
            double width;

            if (noOfGroups == 0) {
                width = value == 0 ? 50 : value > 0 ? 100 : 0;
            }
            else
            {
                if(getAxis_position() == Axis.NONE){
                    width = ((Math.abs(value - min) * 100) / noOfGroups);
                }
                else if(value >= 0)
                {
                    width = ((value - Math.max(min, 0)) * 100) / noOfPositiveGroups;
                    width = width * (1 - axisPoint/100);
                }
                else
                {
                    width = ((Math.abs(Math.min(0, max) - value) * 100) / noOfNegativeGroups);
                    width = -(width * (axisPoint/100));
                }
            }


            if(getDB_direction() == Direction.RIGHT_TO_LEFT || (getDB_direction() == Direction.CONTEXT && cell.getRow().getSheet().getSheetStyle().getProperty(SheetStyle.Property.WRITINGMODE).equals("rl-tb"))) { //No I18N
                axisPoint = 100 - axisPoint;
                width = -width;
            }
            // Since in few cases LIBRO missing -ve fill color. In those cases at least +ve fill color has to be shown.
            ZSColor fillColor = value < 0 ? getNegative_fill_color() : null;
            fillColor = fillColor == null ? getPositive_fill_color() : fillColor;
            ZSColor borderColor = value < 0 ? getNegative_border_color() : getPositive_border_color();
            return new DataBarObj(axisPoint, width, getFill_type(), fillColor, getBorder_type(), borderColor, getAxis_color());
        }catch (EvaluationException ex){} // Do nothing

        return null;
    }

    @Override
    public boolean updateConditionalStyle(Sheet sheet, ReadOnlyCell rCell, ConditionalStyleObject csf, long currentTime, int csUID, Map<CellObject, ConditionalStyleCellStyles> cellStylesMap, boolean isIncludeCellStyle)
    {
        return ConditionalFormatEntry.updateConditionalStyleResult(sheet, rCell, csf, currentTime, csUID, cellStylesMap, isIncludeCellStyle);
    }

    @Override
    public boolean hasPercentileEntry() {
        if(hasPercentileEntry == null)
        {
            hasPercentileEntry = ConditionalFormatEntry.hasPercentileOrFormulaEntry(dataBarEntries, false);
        }

        return hasPercentileEntry;
    }

    @Override
    public boolean hasFormulaEntry() {
        if(hasFormulaEntry == null)
        {
            hasFormulaEntry = ConditionalFormatEntry.hasPercentileOrFormulaEntry(dataBarEntries, true);
        }

        return hasFormulaEntry;
    }

    @Override
    public List<ConditionalFormatEntry> getConditionalStyleEntries() {
        return Collections.unmodifiableList(dataBarEntries);
    }

    @Override
    public ConditionalStyle absoluteClone(Workbook workbook) {
        List<ConditionalFormatEntry> newCSEList = new ArrayList<>();
        for(ConditionalFormatEntry cse : this.getConditionalStyleEntries())
        {
            ConditionalFormatEntry newCSE = cse.clone();
            newCSEList.add(newCSE);
        }
        return new DataBar(newCSEList, this.getDB_direction(), this.getFill_type(), this.getPositive_fill_color(), this.getNegative_fill_color(), this.getBorder_type(), this.getPositive_border_color(), this.getNegative_border_color(), this.getAxis_position(), this.getAxis_color());
    }

    @Override
    public boolean changeTableNodeIfPresent(Workbook srcWorkbook, Map<String, String> tableNameMap) {
        boolean isChanged = false;
        for(ConditionalFormatEntry cse : this.getConditionalStyleEntries()) {
            isChanged = cse.changeTableNodeIfPresent(srcWorkbook, tableNameMap) || isChanged;
        }

        return isChanged;
    }

    @Override
    public boolean changeTableColumnsIfPresent(Workbook srcWorkbook, Table table, Map<String, String> colNameMap) {
        boolean isChanged = false;
        for(ConditionalFormatEntry cse : this.getConditionalStyleEntries()) {
            boolean change = cse.changeTableColumnsIfPresent(srcWorkbook, table, colNameMap);
            isChanged = change || isChanged;
        }

        return isChanged;
    }

    @Override
    public boolean hasStructuredReferences(Workbook workbook) {
        for(ConditionalFormatEntry cse: this.getConditionalStyleEntries()) {
            if(cse.hasStructuredReferences(workbook)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean convertStructuredReferenceToRange(Workbook workbook, String tableName, int baseRow, int baseCol) {
        boolean isChanged = false;
        for(ConditionalFormatEntry cse: this.getConditionalStyleEntries()) {
            isChanged = cse.convertStructuredReferencesToRange(workbook, tableName, baseRow, baseCol) || isChanged;
        }

        return isChanged;
    }
}
