// $Id$

package com.adventnet.zoho.websheet.model.zsparser;

import java.io.IOException;

import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.util.XmlPullUtil;


public class ZSSheetParser extends ZSWorkbookParser1
{

    private String sheetName;
    private int sheetIndex;
    private int curSheetIndex = 0;

    /** Creates a new instance of ZSSheetParser */
    public ZSSheetParser(ZSEventListener listener, String sheetName) throws XmlPullParserException
    {
        this(listener, sheetName, false);
    }

    public ZSSheetParser(ZSEventListener listener, String sheetName, boolean dataOnly) throws XmlPullParserException
    {

        super(listener, dataOnly);
        this.sheetName = sheetName;
    }

    public ZSSheetParser(ZSEventListener listener, int sheetIndex, boolean dataOnly) throws XmlPullParserException
    {
        super(listener, dataOnly);
        this.sheetIndex = sheetIndex;
    }

    protected void processSheetNode(boolean isTraverseData) throws IOException, XmlPullParserException
    {
        ++curSheetIndex;
        if (isThisSheet())
        {
            super.processSheetNode(true);
            stop();
        }
        else
        {
            xpp.skipSubTree();
        }

    }

    private boolean isThisSheet()
    {
        if(sheetName == null)
        {
            return (sheetIndex == curSheetIndex);
        }
        return sheetName.equalsIgnoreCase(getAttribute(Names.A_TABLE_NAME));
    }


}
