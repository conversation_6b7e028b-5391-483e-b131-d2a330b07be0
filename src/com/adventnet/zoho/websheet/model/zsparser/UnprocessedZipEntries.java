// $Id$
package com.adventnet.zoho.websheet.model.zsparser;

import com.adventnet.zoho.websheet.model.util.ZSStore;
import com.zoho.sheet.conversion.ZSImporter;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class UnprocessedZipEntries{
    public static class UnprocessedZipEntry{
        private String name;
        private byte[] bytes;

        public String getName(){
            return name;
        }
        public byte[] getBytes(){
            return bytes;
        }
    }
    private List<UnprocessedZipEntry> zipEntryList = new ArrayList<>();
    private boolean isEntriesAvailable = false;
    private static Set<String> equalsNamesSet = new HashSet<>();
    private static Set<String> startsWithNamesSet = new HashSet<>();
    static {
        equalsNamesSet.add("Basic/script-lc.xml");
        equalsNamesSet.add(ZSStore.FileName.IMAGES_META_INFO.toString().toLowerCase() + "." + ZSStore.FileExtn.JSON.toString().toLowerCase());
        equalsNamesSet.add(ZSImporter.CHART_FILE_NAME);
        startsWithNamesSet.add("Basic");
    }
    public boolean isEntriesAvailable(){
        return isEntriesAvailable;
    }

    public void setEntriesAvailable(boolean entriesAvailable){
        isEntriesAvailable=entriesAvailable;
    }

    public void add(String name, byte[] bytes){
        for(UnprocessedZipEntry unprocessedZipEntry : zipEntryList){
            if(unprocessedZipEntry.name.equals(name)){
                //same entry shouldn't be added twice.
                return;
            }
        }
        UnprocessedZipEntry zipEntry = new UnprocessedZipEntry();
        zipEntry.name = name;
        zipEntry.bytes = bytes;
        zipEntryList.add(zipEntry);
    }

    public byte[] get(String name){
        for(UnprocessedZipEntry zipEntry : zipEntryList){
            if(zipEntry.name.equals(name)){
                return zipEntry.bytes;
            }
        }
        return null;
    }

    public List<UnprocessedZipEntry> getEntriesStartingWith(String name) {
        List<UnprocessedZipEntry> list = new ArrayList<>();
        for(UnprocessedZipEntry zipEntry : zipEntryList){
            if(zipEntry.name.startsWith(name)){
               list.add(zipEntry);
            }
        }
        return list;
    }

    public static boolean isRegistered(String name){
        if(equalsNamesSet.contains(name)) {
            return true;
        }
        for(String string : startsWithNamesSet){
            if(name.startsWith(string)){
                return true;
            }
        }
        return false;
    }

}
