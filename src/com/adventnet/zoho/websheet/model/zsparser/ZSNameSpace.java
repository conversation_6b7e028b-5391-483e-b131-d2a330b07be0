//$Id$
/*
 * ODSNameSpace.java
 *
 * Created on September 24, 2007, 1:07 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.zsparser;


/**
 *
 * <AUTHOR>
 */
public class ZSNameSpace implements Names {
    
    /** Creates a new instance of ODSNameSpace */
    
    String lineSeparator = System.getProperty("line.separator");
    
    
    public ZSNameSpace() {
    }
    
    
    public String getAllNS()
    {
        StringBuffer buff = new StringBuffer();
        buff.append(getNS(N_OFFICE_DOC)).append(lineSeparator);
        buff.append(getNS(N_STYLE)).append(lineSeparator);
        buff.append(getNS(N_TEXT)).append(lineSeparator);
        buff.append(getNS(N_TABLE)).append(lineSeparator);
        buff.append(getNS(N_DRAW)).append(lineSeparator);
        buff.append(getNS(N_F_O)).append(lineSeparator);
        buff.append(getNS(N_HREF)).append(lineSeparator);
        buff.append(getNS(N_D_C)).append(lineSeparator);
        buff.append(getNS(N_META)).append(lineSeparator);
        //buff.append(getNS(N_NUMBER)).append(lineSeparator);
        buff.append(getNS(N_SVG)).append(lineSeparator);
        buff.append(getNS(N_CHART)).append(lineSeparator);
        buff.append(getNS(N_DR3D)).append(lineSeparator);
        buff.append(getNS(N_MATH)).append(lineSeparator);
        buff.append(getNS(N_FORM)).append(lineSeparator);
        buff.append(getNS(N_SCRIPT)).append(lineSeparator);
        buff.append(getNS(N_OOO)).append(lineSeparator);
        buff.append(getNS(N_OOOW)).append(lineSeparator);
        buff.append(getNS(N_OOOC)).append(lineSeparator);
        buff.append(getNS(N_DOM)).append(lineSeparator);
        buff.append(getNS(N_XFORMS)).append(lineSeparator);
        buff.append(getNS(N_XSD)).append(lineSeparator);
        buff.append(getNS(N_XSI)).append(lineSeparator);
        
	// newly added
	buff.append(getNS(N_PRESENTATION)).append(lineSeparator);
	buff.append(getNS(N_RPT)).append(lineSeparator);
	buff.append(getNS(N_OF)).append(lineSeparator);
	buff.append(getNS(N_RDFA)).append(lineSeparator);
	buff.append(getNS(N_FIELD)).append(lineSeparator);

	buff.append(getNS(N_ZSTABLES)).append(lineSeparator);
	
	return buff.toString();
    }

    public String getSettingsNS()
    {
        StringBuffer buff = new StringBuffer();
        buff.append(getNS(N_DOUCUMENT_SETTINGS)).append(lineSeparator);
        //buff.append(getNS(N_D_C)).append(lineSeparator);
	buff.append(getNS(N__XL_I_N_L)).append(lineSeparator);
        buff.append(getNS(N_CONFIG)).append(lineSeparator);
        buff.append(getNS(N_OOO)).append(lineSeparator);
        return buff.toString();
    }
    
    private String getNS(XmlName ns)
    {
        StringBuffer str = new StringBuffer("xmlns:"); //No I18N
        str.append(ns.prefix).append("=\"");
        str.append(ns.namespace).append("\" ");
        
        return str.toString();
    }
   
}
