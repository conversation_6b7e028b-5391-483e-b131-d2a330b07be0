/* $Id$ */
package com.adventnet.zoho.websheet.model.zsparser;

/** some constants representing the nodes/attributes in content.xml of .sxc file */
public interface Names
{

    /** Namespaces */
    String OFFICE = "urn:oasis:names:tc:opendocument:xmlns:office:1.0"; //No I18N 
    String STYLE = "urn:oasis:names:tc:opendocument:xmlns:style:1.0"; //No I18N 
    String FO = "urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0"; //No I18N 
    String TABLE = "urn:oasis:names:tc:opendocument:xmlns:table:1.0"; //No I18N 
    String TEXT = "urn:oasis:names:tc:opendocument:xmlns:text:1.0"; //No I18N 
    String XLINK = "http://www.w3.org/1999/xlink"; //No I18N 
    String SVG = "urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0"; //No I18N 

    // Added by <PERSON><PERSON><PERSON> few more namespaces

    String DRAW="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0"; //No I18N 
    String DC="http://purl.org/dc/elements/1.1/"; //No I18N 
    String META="urn:oasis:names:tc:opendocument:xmlns:meta:1.0"; //No I18N 
    //String NUMBER="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0"; //No I18N 
    String CHART="urn:oasis:names:tc:opendocument:xmlns:chart:1.0"; //No I18N 
    String DR3D="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0"; //No I18N 
    String MATH="http://www.w3.org/1998/Math/MathML"; //No I18N 
    String FORM="urn:oasis:names:tc:opendocument:xmlns:form:1.0"; //No I18N 
    String SCRIPT="urn:oasis:names:tc:opendocument:xmlns:script:1.0"; //No I18N 
    String OOO="http://openoffice.org/2004/office"; //No I18N 
    String OOOW="http://openoffice.org/2004/writer"; //No I18N 
    String OOOC="http://openoffice.org/2004/calc"; //No I18N 
    String DOM="http://www.w3.org/2001/xml-events"; //No I18N 
    String XFORMS="http://www.w3.org/2002/xforms"; //No I18N 
    String XSD="http://www.w3.org/2001/XMLSchema"; //No I18N 
    String XSI="http://www.w3.org/2001/XMLSchema-instance"; //No I18N 
    
   // newly added for new namespace support
    String PRESENTATION="urn:oasis:names:tc:opendocument:xmlns:presentation:1.0"; //No I18N 
    String RPT="http://openoffice.org/2005/report"; //No I18N 
    String OF="urn:oasis:names:tc:opendocument:xmlns:of:1.2"; //No I18N 
    String RDFA="http://docs.oasis-open.org/opendocument/meta/rdfa#"; //No I18N 
    String FIELD="urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0"; //No I18N 
    String TABLEOOO="http://openoffice.org/2009/table"; //No I18N
    String CALCEXT="urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0"; //No I18N
    //ZOHO SHEET Namespaces
    String ZS_TABLES = "urn:com:zoho:sheet:xmlns:tables:1.0"; //No I18N
    // end here

    //CALCEXT prefixes
    String P_CALCEXT = "calcext"; //No I18N
    String P_ZS_TABLES = "zstables"; //No I18N
    /** prefixes */
    public final String P_OFFICE = "office"; //No I18N 
    String P_STYLE = "style"; //No I18N 
    String P_FO = "fo"; //No I18N 
    String P_TABLE = "table"; //No I18N
    String P_TEXT = "text"; //No I18N 
    String P_XLINK = "xlink"; //No I18N 
    String P_SVG = "svg"; //No I18N 
    
    //ADDED BY GANESH

    String P_DRAW="draw"; //No I18N 
    String P_DC="dc"; //No I18N 
    String P_META="meta"; //No I18N 
    String P_NUMBER="number"; //No I18N 
    String P_EXT = "loext"; //No I18N  
    String P_CHART="chart"; //No I18N 
    String P_DR3D="dr3d"; //No I18N 
    String P_MATH="math"; //No I18N 
    String P_FORM="form"; //No I18N 
    String P_SCRIPT="script"; //No I18N 
    String P_OOO="ooo"; //No I18N 
    String P_OOOW="ooow"; //No I18N 
    String P_OOOC="oooc"; //No I18N 
    String P_DOM="dom"; //No I18N 
    String P_XFORMS="xforms"; //No I18N 
    String P_XSD="xsd"; //No I18N 
    String P_XSI="xsi"; //No I18N 

    String P_PRESENTATION = "presentation"; //No I18N 
    String P_RPT = "rpt"; //No I18N 
    String P_OF = "of"; //No I18N 
    String P_RDFA="rdfa"; //No I18N 
    String P_FIELD="field"; //No I18N 

    String P_TABLEOOO = "tableooo"; //No I18N

    //END GANESH

    /** Node Names */

    XmlName N_PRESENTATION = new XmlName(PRESENTATION, P_PRESENTATION, ""); //No I18N 
    XmlName N_RPT = new XmlName(RPT, P_RPT, ""); //No I18N 
    XmlName N_OF = new XmlName(OF, P_OF, ""); //No I18N 
    XmlName N_RDFA = new XmlName(RDFA, P_RDFA, ""); //No I18N 
    XmlName N_FIELD = new XmlName(FIELD, P_FIELD, ""); //No I18N 
    XmlName N__XL_I_N_L = new XmlName(XLINK, P_XLINK, ""); //No I18N 
    
    XmlName N_OFFICE_DOC = new XmlName(OFFICE, P_OFFICE, "document-content"); //No I18N 
    XmlName N_OFFICE_SCRIPTS=new XmlName(OFFICE,P_OFFICE,"scripts"); //No I18N 
    XmlName N_OFFICE_FORMS=new XmlName(OFFICE,P_OFFICE,"forms"); //No I18N 
    ////
    XmlName N_FORM_FORM=new XmlName(FORM,P_FORM,"form"); //No I18N 
    XmlName N_FORM_BUTTON=new XmlName(FORM,P_FORM,"button"); //No I18N 
    XmlName N_FORM_PROPERTIES=new XmlName(FORM,P_FORM,"properties"); //No I18N 
    XmlName N_FORM_PROPERTY=new XmlName(FORM,P_FORM,"property"); //No I18N 
    
    XmlName NOFFICE_EVENT_LISTENERS = new XmlName(OFFICE, P_OFFICE, "event-listeners"); //No I18N 
    XmlName N_SCRIPT_EVENT_LISTENER = new XmlName(SCRIPT,P_SCRIPT,"event-listener"); //No I18N 

    XmlName N_FONT_DECLS = new XmlName(OFFICE, P_OFFICE, "font-face-decls"); //No I18N 
    XmlName N_FONT_DECL = new XmlName(STYLE, P_STYLE, "font-face"); //No I18N 

    XmlName N_AUTOMATIC_STYLES = new XmlName(OFFICE, P_OFFICE, "automatic-styles"); //No I18N 
    XmlName N_STYLE = new XmlName(STYLE, P_STYLE, "style"); //No I18N 
    XmlName N_STYLE_PROPS = new XmlName(STYLE, P_STYLE, "properties"); //No I18N 

    XmlName N_TABLE_ROW_PROPS = new XmlName(STYLE, P_STYLE, "table-row-properties"); //No I18N 
    XmlName N_TABLE_COL_PROPS = new XmlName(STYLE, P_STYLE, "table-column-properties"); //No I18N 
    XmlName N_TABLE_PROPS = new XmlName(STYLE, P_STYLE, "table-properties"); //No I18N 


    XmlName N_GRAPHIC_PROPS = new XmlName(STYLE, P_STYLE, "graphic-properties"); //No I18N 
    

    XmlName N_TABLE_CELL_PROPS = new XmlName(STYLE, P_STYLE, "table-cell-properties"); //No I18N 
    XmlName N_PARAGRAPH_PROPS = new XmlName(STYLE, P_STYLE, "paragraph-properties"); //No I18N 
    XmlName N_TEXT_PROPS = new XmlName(STYLE, P_STYLE, "text-properties"); //No I18N 
    XmlName N_MAP_STYLE = new XmlName(STYLE, P_STYLE, "map"); //No I18N 

    XmlName N_BODY = new XmlName(OFFICE, P_OFFICE, "body"); //No I18N 
    XmlName N_SPREAD_SHEET = new XmlName(OFFICE, P_OFFICE, "spreadsheet"); //No I18N 
    XmlName N_TABLE = new XmlName(TABLE, P_TABLE, "table"); //No I18N 

    // new nodes for sheet level dependencies.
    XmlName A_TABLE_SHEET_DEPENDENCY_MAP = new XmlName(TABLE, P_TABLE, "sheetDependencyMap"); //No I18N 
    
    // new nodes for named ranges.
    XmlName N_NAMED_EXPRESSIONS = new XmlName(TABLE, P_TABLE, "named-expressions"); //No I18N 
    XmlName N_NAMED_RANGE = new XmlName(TABLE, P_TABLE, "named-range"); //No I18N 
    XmlName N_NAMED_EXPRESSION = new XmlName(TABLE, P_TABLE, "named-expression"); //No I18N 

    XmlName N_TABLE_HEADER_COLUMNS = new XmlName(TABLE, P_TABLE, "table-header-columns"); //No I18N 
    XmlName N_TABLE_COLUMNS = new XmlName(TABLE, P_TABLE, "table-columns"); //No I18N 
    XmlName N_TABLE_COLUMN_GROUP = new XmlName(TABLE, P_TABLE, "table-column-group"); //No I18N 
    XmlName N_TABLE_COLUMN = new XmlName(TABLE, P_TABLE, "table-column"); //No I18N 

    XmlName N_TABLE_HEADER_ROWS = new XmlName(TABLE, P_TABLE, "table-header-rows"); //No I18N 
    XmlName N_TABLE_ROWS = new XmlName(TABLE, P_TABLE, "table-rows"); //No I18N 
    XmlName N_TABLE_ROW_GROUPS = new XmlName(TABLE, P_TABLE, "table-row-group"); //No I18N 
    XmlName N_TABLE_ROW = new XmlName(TABLE, P_TABLE, "table-row"); //No I18N 

    XmlName N_COVERED_TABLE_CELL = new XmlName(TABLE, P_TABLE, "covered-table-cell"); //No I18N

    XmlName N_TABLE_CELL = new XmlName(TABLE, P_TABLE, "table-cell"); //No I18N 
    XmlName N_ANNOTATION = new XmlName(OFFICE, P_OFFICE, "annotation"); //No I18N 
    XmlName N_DATE = new XmlName(DC, P_DC, "date"); //No I18N 

    XmlName N_TEXT_P = new XmlName(TEXT, P_TEXT, "p"); //No I18N 
    XmlName N_TEXT_A = new XmlName(TEXT, P_TEXT, "a"); //No I18N 
    
    //need for Attachments
    XmlName N_ATTACHMENT = new XmlName(TABLE, P_TABLE, "attachment"); //No I18N 
    XmlName A_ATTACHMENT_I_D = new XmlName(TABLE, P_TABLE, "attachment-id"); //No I18N 
    XmlName A_ATTACHMENT_NAME = new XmlName(TABLE, P_TABLE, "attachment-name"); //No I18N 
    //End of Attachments
    
     XmlName N_DRAW=new XmlName(DRAW,P_DRAW,"draw"); //No I18N 
     XmlName N_F_O=new XmlName(FO,P_FO,"fo"); //No I18N 
     XmlName N_HREF=new XmlName(XLINK,P_XLINK,"href"); //No I18N 
     XmlName N_META=new XmlName(META,P_META,""); //No I18N 
     XmlName N_SVG=new XmlName(SVG,P_SVG,""); //No I18N 
     XmlName N_CHART=new XmlName(CHART,P_CHART,""); //No I18N 
     XmlName N_DR3D=new XmlName(DR3D,P_DR3D,""); //No I18N 
     XmlName N_FORM=new XmlName(FORM,P_FORM,"forms"); //No I18N 
     XmlName N_SCRIPT=new XmlName(SCRIPT,P_SCRIPT,"scripts"); //No I18N 
     XmlName N_OOOW=new XmlName(OOOW,P_OOOW,""); //No I18N 
     XmlName N_DOM=new XmlName(DOM,P_DOM,""); //No I18N 
     XmlName N_XFORMS=new XmlName(XFORMS,P_XFORMS,""); //No I18N 
     XmlName N_XSD=new XmlName(XSD,P_XSD,""); //No I18N 
     XmlName N_XSI=new XmlName(XSI,P_XSI,""); //No I18N 
     XmlName N_TEXT=new XmlName(TEXT,P_TEXT,"text"); //No I18N 
     XmlName N_TEXT__LOEXT=new XmlName(TEXT,P_EXT,"text"); //No I18N 
     XmlName N_D_C=new XmlName(DC,P_DC,""); //No I18N 
     XmlName N_MATH=new XmlName(MATH,P_MATH,""); //No I18N 
     XmlName N_OOO=new XmlName(OOO,P_OOO,""); //No I18N 
     XmlName N_OOOC=new XmlName(OOOC,P_OOOC,""); //No I18N 
     //
     XmlName N_DRAW_CONTROL=new XmlName(DRAW,P_DRAW,"control"); //No I18N 

     // filters
     XmlName N_FILTERVIEWS = new XmlName(TABLE, P_TABLE, "filter-views"); // No I18N
     XmlName N_FILTERVIEW = new XmlName(TABLE, P_TABLE, "filter-view"); // No I18N
     XmlName N_DEFAULT_FILTERVIEW = new XmlName(TABLE, P_TABLE, "default-filter-view"); // No I18N


     XmlName N_FILTERS = new XmlName(TABLE, P_TABLE, "zs-filters"); //No I18N
     XmlName N_DATABASE_RANGES = new XmlName(TABLE, P_TABLE, "database-ranges"); //No I18N
     XmlName N_DATABASE_RANGE = new XmlName(TABLE, P_TABLE, "database-range"); //No I18N
     XmlName N_SHEET_ASN = new XmlName(TABLE, P_TABLE, "sheet"); //No I18N


     XmlName N_FILTER = new XmlName(TABLE, P_TABLE, "filter"); //No I18N
     XmlName N_FILTER_O_R = new XmlName(TABLE, P_TABLE, "filter-or"); //No I18N
     XmlName N_FILTER_A_N_D = new XmlName(TABLE, P_TABLE, "filter-and"); //No I18N 
     XmlName N_FILTER_CONDITION = new XmlName(TABLE, P_TABLE, "filter-condition"); //No I18N 
           

     // filter attributes
     XmlName A_ZUID = new XmlName(TABLE, P_TABLE, "zuid");	//No I18N
     XmlName A_FILTERNAME = new XmlName(TABLE, P_TABLE, "zsfiltername");	//No I18N
     XmlName A_DEFAULTVIEW = new XmlName(TABLE, P_TABLE, "default");	//No I18N
     XmlName A_FILTEREDROWS = new XmlName(TABLE, P_TABLE, "filtered-rows");	//No I18N

     XmlName A_TARGET_RANGE_ADDRESS = new XmlName(TABLE, P_TABLE, "target-range-address"); //No I18N
     XmlName A_FILTER_VIEW_ID = new XmlName(TABLE, P_TABLE, "Fid"); //No I18N
     XmlName A_DISPLAY_FILTER_BUTTONS = new XmlName(TABLE, P_TABLE, "display-filter-buttons"); //No I18N
     XmlName A_CONTAINS_HEADER = new XmlName(TABLE, P_TABLE, "contains-header"); //No I18N
     XmlName A_FIELD_NUMBER = new XmlName(TABLE, P_TABLE, "field-number"); //No I18N
     XmlName A_DATA_TYPE = new XmlName(TABLE, P_TABLE, "data-type"); //No I18N
     XmlName A_FILTER_TYPE = new XmlName(TABLE, P_TABLE, "filter-type"); //No I18N
     XmlName A_DATA_VALUE = new XmlName(TABLE, P_TABLE, "value"); //No I18N
     XmlName A_DATA_VALUE1 = new XmlName(TABLE, P_TABLE, "value1"); //No I18N
     XmlName A_OPERATOR = new XmlName(TABLE, P_TABLE, "operator"); //No I18N
     XmlName PICKLIST_ID = new XmlName(TABLE, P_TABLE, "picklist_id"); // No I18N
     XmlName PICKLIST_ITEM_IDS = new XmlName(TABLE, P_TABLE, "item-ids"); // No I18N


     //////
     XmlName N_SHAPES = new XmlName(TABLE, P_TABLE, "shapes"); //No I18N 
     XmlName N_FRAME = new XmlName(DRAW, P_DRAW, "frame"); //No I18N // No I18N
     XmlName N_IMAGE = new XmlName(DRAW, P_DRAW, "image"); //No I18N // No I18N
     XmlName N_TITLE = new XmlName(SVG, P_SVG, "title"); //No I18N // No I18N
     XmlName N_DESC = new XmlName(SVG, P_SVG, "desc"); //No I18N // No I18N
     
     //pivot
    XmlName N_DATA_PILOT_TABLES = new XmlName(TABLE, P_TABLE, "data-pilot-tables"); //No I18N                     
    XmlName N_DATA_PILOT_TABLE = new XmlName(TABLE, P_TABLE, "data-pilot-table"); //No I18N                       
    XmlName N_SOURCE_CELL_RANGE = new XmlName(TABLE, P_TABLE, "source-cell-range"); //No I18N                     
    XmlName N_DATA_PILOT_FIELD = new XmlName(TABLE, P_TABLE, "data-pilot-field"); //No I18N                       
    XmlName N_DATA_PILOT_FIELD_REFERENCE = new XmlName(TABLE, P_TABLE, "data-pilot-field-reference"); //No I18N    
    XmlName N_DATA_PILOT_LEVEL = new XmlName(TABLE, P_TABLE, "data-pilot-level"); //No I18N                       
    XmlName N_DATA_PILOT_GROUPS = new XmlName(TABLE, P_TABLE, "data-pilot-groups"); //No I18N                     
    XmlName N_DATA_PILOT_GROUP = new XmlName(TABLE, P_TABLE, "data-pilot-group"); //No I18N                     
    XmlName N_DATA_PILOT_GROUP_MEMBER = new XmlName(TABLE, P_TABLE, "data-pilot-group-member"); //No I18N                     
    XmlName N_DATA_PILOT_MEMBERS = new XmlName(TABLE, P_TABLE, "data-pilot-members"); //No I18N                   
    XmlName N_DATA_PILOT_MEMBER = new XmlName(TABLE, P_TABLE, "data-pilot-member"); //No I18N
    XmlName N_DATA_PILOT_DISPLAY_INFO = new XmlName(TABLE, P_TABLE, "data-pilot-display-info"); //No I18N          
    XmlName N_DATA_PILOT_SORT_INFO = new XmlName(TABLE, P_TABLE, "data-pilot-sort-info"); //No I18N                
    XmlName N_DATA_PILOT_LAYOUT_INFO = new XmlName(TABLE, P_TABLE, "data-pilot-layout-info"); //No I18N            
    XmlName N_DATA_PILOT_FILTER_INFO = new XmlName(TABLE, P_TABLE, "data-pilot-filter-info"); //No I18N            
    XmlName N_DATA_PILOT_SUBTOTALS = new XmlName(TABLE, P_TABLE, "data-pilot-subtotals"); //No I18N               
    XmlName N_DATA_PILOT_SUBTOTAL = new XmlName(TABLE, P_TABLE, "data-pilot-subtotal"); //No I18N                 
    XmlName N_PILOT_ELEMENTS = new XmlName(TABLE, P_TABLE, "pivot-elements"); //No I18N
    XmlName N_PILOT_GRID = new XmlName(TABLE, P_TABLE, "grid"); //No I18N
    XmlName N_PILOT_DATAFIELD = new XmlName(TABLE, P_TABLE, "dataField"); //No I18N
    XmlName N_PILOT_ROWHEADER = new XmlName(TABLE, P_TABLE, "rowHeader"); //No I18N
    XmlName N_PILOT_SUBTOTAL_ROW = new XmlName(TABLE, P_TABLE, "subtotalRow"); //No I18N
    XmlName N_PILOT_SUBTOTAL_COLUMN = new XmlName(TABLE, P_TABLE, "subtotalColumn"); //No I18N
    XmlName N_PILOT_SUBTOTAL_ROWCELL = new XmlName(TABLE, P_TABLE, "subtotalRowCell"); //No I18N
    XmlName N_PILOT_INDEX = new XmlName(TABLE, P_TABLE, "index"); //No I18N
    XmlName N_PILOT_RANGES = new XmlName(TABLE, P_TABLE, "ranges"); //No I18N

    //ZS TABLE
    XmlName N_ZSTABLES = new XmlName(ZS_TABLES, P_ZS_TABLES, "tables"); //No I18N

    XmlName N_ZSTABLE = new XmlName(ZS_TABLES, P_ZS_TABLES, "table"); //No I18N
    XmlName A_ZSTABLENAME = new XmlName(ZS_TABLES, P_ZS_TABLES, "name"); //No I18N
    XmlName A_ZSTABLE_ID = new XmlName(ZS_TABLES, P_ZS_TABLES, "id"); //No I18N
    XmlName A_START_ROW = new XmlName(ZS_TABLES, P_ZS_TABLES, "startRow"); //No I18N
    XmlName A_END_ROW = new XmlName(ZS_TABLES, P_ZS_TABLES, "endRow"); //No I18N
    XmlName A_START_COL = new XmlName(ZS_TABLES, P_ZS_TABLES, "startCol"); //No I18N
    XmlName A_HEADER_ROW_SHOWN = new XmlName(ZS_TABLES, P_ZS_TABLES, "headerRowShown"); //No I18N
    XmlName A_FOOTER_ROW_SHOWN = new XmlName(ZS_TABLES, P_ZS_TABLES, "footerRowShown"); //No I18N
    XmlName A_ZSTABLE_STYLE_NAME = new XmlName(ZS_TABLES, P_ZS_TABLES, "tableStyleName"); //No I18N
    XmlName A_SHOW_FIRST_COLUMN = new XmlName(ZS_TABLES, P_ZS_TABLES, "showFirstColumn"); //No I18N
    XmlName A_SHOW_LAST_COLUMN = new XmlName(ZS_TABLES, P_ZS_TABLES, "showLastColumn"); //No I18N
    XmlName A_SHOW_ROW_STRIPES = new XmlName(ZS_TABLES, P_ZS_TABLES, "showRowStripes"); //No I18N
    XmlName A_SHOW_COLUMN_STRIPES = new XmlName(ZS_TABLES, P_ZS_TABLES, "showColumnStripes"); //No I18N
    XmlName A_TABLE_MAX_ROWS = new XmlName(ZS_TABLES, P_ZS_TABLES, "maxRows"); //No I18N
    XmlName A_TABLE_ALLOW_RESIZE = new XmlName(ZS_TABLES, P_ZS_TABLES, "allowResize"); //No I18N
    XmlName A_TABLE_ALLOW_COL_EXPANSION = new XmlName(ZS_TABLES, P_ZS_TABLES, "allowColExpansion"); //No I18N
    XmlName A_TABLE_IS_FILTER_TABLE = new XmlName(ZS_TABLES, P_ZS_TABLES, "isFilterTable"); //No I18N
    XmlName A_TABLE_FILL_FORMULA = new XmlName(ZS_TABLES, P_ZS_TABLES, "tableAutoFillFormula"); //No I18N

    XmlName N_ZSTABLE_COLUMNS = new XmlName(ZS_TABLES, P_ZS_TABLES, "tableColumns"); //No I18N

    XmlName N_ZSTABLE_COLUMN = new XmlName(ZS_TABLES, P_ZS_TABLES, "tableColumn"); //No I18N
    XmlName A_CALCULATED_EXPRESSION = new XmlName(ZS_TABLES, P_ZS_TABLES, "calculatedExpression"); //No I18N
    XmlName A_FOOTER_LABEL = new XmlName(ZS_TABLES, P_ZS_TABLES, "footerLabel"); //No I18N
    XmlName A_FOOTER_EXPRESSION = new XmlName(ZS_TABLES, P_ZS_TABLES, "footerExpression"); //No I18N

    //ZS TABLE STYLE
    XmlName N_TABLE_STYLES = new XmlName(STYLE, P_STYLE, "tableStyles"); //No I18N
    XmlName N_TABLE_STYLE = new XmlName(STYLE, P_STYLE, "tableStyle"); //No I18N
    XmlName N_DEFAULT_TABLE_STYLE = new XmlName(STYLE, P_STYLE, "defaultTableStyle"); //No I18N
    XmlName N_TABLE_STYLE_PROPERTY = new XmlName(STYLE, P_STYLE, "tableStyleProperty"); //No I18N

    //adhoc(automatic style + data-validation) and stable(fontfaces + named-styles + themes + table-styles + named-expression)
    XmlName N_ADHOC = new XmlName(TABLE, P_TABLE, "adhoc"); // No I18N
    XmlName N_STABLE = new XmlName(TABLE, P_TABLE, "stable"); // No I18N

    XmlName N_TABLE_DISCUSSIONS = new XmlName(TABLE, P_TABLE, "table-discussions"); // No I18N
    XmlName N_TABLE_DISCUSSION = new XmlName(TABLE, P_TABLE, "table-discussion"); // No I18N

  XmlName N_TABLE_NOTIFICATIONS = new XmlName(TABLE, P_TABLE, "table-notifications"); // No I18N
  XmlName N_TABLE_NOTIFICATION = new XmlName(TABLE, P_TABLE, "table-notification"); // No I18N
    XmlName N_TABLE_CHECKBOXES = new XmlName(TABLE, P_TABLE, "table-checkboxes"); // No I18N
    XmlName N_TABLE_CHECKBOX = new XmlName(TABLE, P_TABLE, "table-checkbox"); // No I18N
    XmlName N_TABLE_PROTECTIONS = new XmlName(TABLE, P_TABLE, "table-protections"); // No I18N
    XmlName N_TABLE_PROTECTION = new XmlName(TABLE, P_TABLE, "table-protection"); // No I18N
    XmlName N_TABLE_FORM = new XmlName(TABLE, P_TABLE, "table-form"); // No I18N

    /** Attribute Names */
    XmlName A_STYLE_VERSION = new XmlName(STYLE, P_STYLE, "styles-correction");//No I18N
    XmlName A_STYLE_NAME = new XmlName(STYLE, P_STYLE, "name"); //No I18N
    XmlName A_CUSTOM_STYLE = new XmlName(STYLE, P_STYLE, "custom-style"); //No I18N
    XmlName A_HEADER_STYLE_NAME = new XmlName(STYLE, P_STYLE, "header-style-name"); //No I18N

    XmlName A_FOOTER_STYLE_NAME = new XmlName(STYLE, P_STYLE, "footer-style-name"); //No I18N
    XmlName A_STYLE_FAMILY = new XmlName(STYLE, P_STYLE, "family"); //No I18N
    XmlName A_PARENT_STYLE_NAME = new XmlName(STYLE, P_STYLE, "parent-style-name"); //No I18N
    XmlName A_MASTER_PAGE_NAME = new XmlName(STYLE, P_STYLE, "master-page-name"); //No I18N

    // FontFace
    XmlName A_FONT_FAMILY = new XmlName(SVG, P_SVG, "font-family"); //No I18N
    XmlName A_FONT_FAMILY_GENERIC = new XmlName(STYLE, P_STYLE, "font-family-generic"); //No I18N
    XmlName A_FONT_PITCH = new XmlName(STYLE, P_STYLE, "font-pitch"); //No I18N

    //ADDED BY GANESH
    XmlName A_FONT_CHARSET = new XmlName(STYLE, P_STYLE, "font-charset"); //No I18N
    XmlName A_FONT_ADORNMENTS = new XmlName(STYLE, P_STYLE, "font-adornments"); //No I18N
    XmlName A_DATA_STYLE_NAME = new XmlName(STYLE, P_STYLE, "data-style-name"); //No I18N
    //END OF GANESH

    // ColumnStyle
    XmlName A_COLUMN_WIDTH = new XmlName(STYLE, P_STYLE, "column-width"); //No I18N
    XmlName A_BREAK_BEFORE = new XmlName(FO, P_FO, "break-before"); //No I18N
    XmlName A_BREAK_AFTER = new XmlName(FO, P_FO, "break-after"); //No I18N
    XmlName A_REL_COLUMN_WIDTH = new XmlName(STYLE, P_STYLE, "style:rel-column-width"); //No I18N
    XmlName A_USE_OPTIMAL_COLUMN_WIDTH = new XmlName(STYLE, P_STYLE, "style:use-optimal-column-width"); //No I18N

    // RowStyle
    XmlName A_ROW_HEIGHT = new XmlName(STYLE, P_STYLE, "row-height"); //No I18N
    XmlName A_MIN_ROW_HEIGHT = new XmlName(STYLE, P_STYLE, "min-row-height"); //No I18N
    XmlName A_USE_OPTIMAL_ROW_HEIGHT = new XmlName(STYLE, P_STYLE, "use-optimal-row-height"); //No I18N
    XmlName A_KEEP_TOGETHER = new XmlName(FO, P_FO, "keep-together"); //No I18N

    // text:c
    XmlName A_TEXT_C = new XmlName(TEXT, P_TEXT, "c"); //No I18N

    // CellStyle
//    Added by GANESH

    //Named-Range
    
    XmlName A_BASE_CELL_ADDRESS__RANGE = new XmlName(TABLE, P_TABLE, "base-cell-address"); //No I18N
    XmlName A_CELL_RANGE_ADDRESS = new XmlName(TABLE, P_TABLE, "cell-range-address"); //No I18N
    XmlName A_SHEET_SCOPE = new XmlName(TABLE, P_TABLE, "sheet-scope"); //No I18N

    XmlName A_COMMENT = new XmlName(TABLE, P_TABLE, "comment"); //No I18N
    XmlName A_EXPRESSION = new XmlName(TABLE, P_TABLE, "expression"); //No I18N


    //ANNOTATION
    XmlName A_X = new XmlName(SVG, P_SVG, "x"); //No I18N
    XmlName A_DRAW_STYLE_NAME = new XmlName(DRAW, P_DRAW, "style-name"); //No I18N
    XmlName A_TEXT_STYLE_NAME = new XmlName(DRAW, P_DRAW, "text-style-name"); //No I18N
    XmlName A_DRAW_CONTROL = new XmlName(DRAW, P_DRAW, "control"); //No I18N
    XmlName A_DRAW_NAME = new XmlName(DRAW, P_DRAW, "name"); //No I18N
    XmlName A_WIDTH__S_V_G = new XmlName(SVG, P_SVG, "width"); //No I18N
    XmlName A_HEIGHT__S_V_G = new XmlName(SVG, P_SVG, "height"); //No I18N
    XmlName A_Y = new XmlName(SVG, P_SVG, "y"); //No I18N
    XmlName A_VIEW_BOX = new XmlName(SVG, P_SVG, "viewbox"); //No I18N 
    XmlName A_TABLE_BACKGROUND = new XmlName(TABLE, P_TABLE, "table-background"); //No I18N 
    XmlName A_ANCHOR = new XmlName(TEXT, P_TEXT, "anchor"); //No I18N 
    
    XmlName A_ACTUAL_HEIGHT = new XmlName(SVG, P_SVG, "actualHeight"); //No I18N 
    XmlName A_ACTUAL_WIDTH = new XmlName(SVG, P_SVG, "actualWidth"); //No I18N 
    
    XmlName N_DRAW_A = new XmlName(DRAW, P_DRAW, "a"); //No I18N 
    
    XmlName A_CAPTION_POINT_X = new XmlName(DRAW, P_DRAW, "caption-point-x"); //No I18N
    XmlName A_CAPTION_POINT_Y = new XmlName(DRAW, P_DRAW, "caption-point-y"); //No I18N
    XmlName A_DISPLAY__O_F_F_I_C_E = new XmlName(OFFICE, P_OFFICE, "display"); //No I18N

    XmlName A_CORNER_RADIUS = new XmlName(DRAW, P_DRAW, "corner-radius"); //No I18N
    XmlName A_END_CELL_ADDRESS = new XmlName(TABLE, P_TABLE, "end-cell-address"); //No I18N
    XmlName A_END_X = new XmlName(TABLE, P_TABLE, "end-x"); //No I18N
    XmlName A_END_Y = new XmlName(TABLE, P_TABLE, "end-y"); //No I18N
    XmlName A_ANCHOR_TYPE = new XmlName(TEXT, P_TEXT, "anchor-type"); //No I18N
    XmlName A_ANCHOR_PAGE_NUMBER = new XmlName(TEXT, P_TEXT , "anchor-page-number"); //No I18N
    XmlName A_LAYER = new XmlName(DRAW, P_DRAW, "layer"); //No I18N
    XmlName A_TRANSFORM = new XmlName(DRAW, P_DRAW, "transform"); //No I18N
    XmlName A_NAME = new XmlName(DRAW, P_DRAW, "name"); //No I18N
    XmlName A_Z_INDEX = new XmlName(DRAW, P_DRAW, "z-index"); //No I18N

    //For element dc:date of annotation
    XmlName A_ID = new XmlName(DRAW, P_DRAW, "id"); //No I18N
    XmlName A_TEXT_STYLE = new XmlName(TEXT, P_TEXT, "style-name"); //No I18N
    XmlName A_PARAGRAPH_STYLE = new XmlName(TEXT, P_TEXT, "style-name"); //No I18N

    //Paragraph-properties
    XmlName A_LINE_HEIGHT = new XmlName(FO, P_FO,"line-height"); //No I18N
    XmlName A_LINE_HEIGHT_ATLEAST = new XmlName(STYLE, P_STYLE,"line-height-at-least"); //No I18N
    XmlName A_LINE_SPACING = new XmlName(STYLE, P_STYLE, "line-spacing"); //No I18N
    XmlName A_INDEPENDENT_LINE_SPACING = new XmlName(STYLE, P_STYLE, "font-independent-line-spacing"); //No I18N
    XmlName A_TEXT_ALIGN = new XmlName(FO, P_FO, "text-align"); //No I18N
    XmlName A_TEXT_ALIGN_LAST = new XmlName(FO, P_FO, "text-align-last"); //No I18N
    XmlName A_JUSTIFY_SINGLE_WORD = new XmlName(STYLE, P_STYLE, "justify-single-word"); //No I18N
    XmlName A_WINDOWS = new XmlName(FO, P_FO, "windows"); //No I18N
    XmlName A_ORPHANS = new XmlName(FO, P_FO, "orphans"); //No I18N
    XmlName A_HYPHENATION_KEEP = new XmlName(FO, P_FO, "hyphenation-keep"); //No I18N
    XmlName A_HYPHENATION_LADDER_COUNT = new XmlName(FO, P_FO, "hyphenation-ladder-count"); //No I18N
    XmlName A_REGISTER_TRUE = new XmlName(STYLE, P_STYLE, "register-true"); //No I18N
    XmlName A_ZSINDENT = new XmlName(FO, P_FO, "zsindent"); //No I18N
    XmlName A_TEXT_INDENT = new XmlName(FO, P_FO, "text-indent"); //No I18N
    XmlName A_AUTO_TEXT_INDENT = new XmlName(STYLE, P_STYLE, "auto-text-indent"); //No I18N

    XmlName A_BACKGROUND_COLOR = new XmlName(FO, P_FO, "background-color"); //No I18N
    XmlName A_THEME_COLOR = new XmlName(FO, P_FO, "theme-color"); //No I18N
    XmlName A_COLOR_TINT = new XmlName(FO, P_FO, "color-tint"); //No I18N
    XmlName A_NUMBER_LINES = new XmlName(TEXT, P_TEXT, "number-lines"); //No I18N
    XmlName A_LINE_NUMBER = new XmlName(TEXT, P_TEXT, "line-number"); //No I18N
    XmlName A_TEXT_AUTOSPACE = new XmlName(STYLE, P_STYLE, "text-autospace"); //No I18N
    XmlName A_PUNCTUATION_WRAP = new XmlName(STYLE, P_STYLE, "punctuation-wrap"); //No I18N
    XmlName A_LINE_BREAK = new XmlName(STYLE, P_STYLE, "line-break"); //No I18N
    XmlName A_VERTICAL_ALIGN = new XmlName(STYLE, P_STYLE, "vertical-align"); //No I18N
    XmlName A_WRITING_MODE_AUTOMATIC = new XmlName(STYLE, P_STYLE, "writing-mode=automatic"); //No I18N
    XmlName A_SNAP_TO_LAYOUT_GRID = new XmlName(STYLE, P_STYLE, "snap-to-layout-grid"); //No I18N
    XmlName A_BACKGROUND_TRANSPARENCY = new XmlName(STYLE, P_STYLE, "background-transparency"); //No I18N

   
    // Start graphic-properties
    XmlName A_STROKE = new XmlName(DRAW, P_DRAW, "stroke"); //No I18N
    XmlName A_STROKE_DASH = new XmlName(DRAW, P_DRAW, "stroke-dash"); //No I18N
    XmlName A_STROKE_DASH_NAMES = new XmlName(DRAW, P_DRAW, "stroke-dash-names"); //No I18N
    XmlName A_STROKE_WIDTH = new XmlName(SVG, P_SVG, "stroke-width"); //No I18N
    XmlName A_STROKE_COLOR = new XmlName(SVG, P_SVG, "stroke-color"); //No I18N
    XmlName A_MARKER_START = new XmlName(DRAW, P_DRAW, "marker-start"); //No I18N
    XmlName A_MARKER_END = new XmlName(DRAW, P_DRAW, "marker-end"); //No I18N
    XmlName A_MARKER_START_WIDTH = new XmlName(DRAW, P_DRAW, "marker-start-width"); //No I18N
    XmlName A_MARKER_END_WIDTH = new XmlName(DRAW, P_DRAW, "marker-end-width"); //No I18N
    XmlName A_MARKER_START_CENTER = new XmlName(DRAW, P_DRAW, "marker-start-center"); //No I18N
    XmlName A_MARKER_END_CENTER = new XmlName(DRAW, P_DRAW, "marker-end-center"); //No I18N
    XmlName A_STROKE_OPACITY = new XmlName(SVG, P_SVG, "stroke-opacity"); //No I18N
    XmlName A_STROKE_LINEJOIN = new XmlName(DRAW, P_DRAW, "stroke-linejoin"); //No I18N

    XmlName A_ANIMATION = new XmlName(TEXT, P_TEXT, "animation"); //No I18N
    XmlName A_ANIMATION_DIRECTION = new XmlName(TEXT, P_TEXT, "animation-direction"); //No I18N
    XmlName A_ANIMATION_START_INSIDE = new XmlName(TEXT, P_TEXT, "animation-start-inside"); //No I18N
    XmlName A_ANIMATION_STOP_INSIDE = new XmlName(TEXT, P_TEXT, "animation-stop-inside"); //No I18N
    XmlName A_ANIMATION_REPEAT = new XmlName(TEXT, P_TEXT, "animation-repeat"); //No I18N
    XmlName A_ANIMATION_DELAY = new XmlName(TEXT, P_TEXT, "animation-delay"); //No I18N
    XmlName A_ANIMATION_STEPS = new XmlName(TEXT, P_TEXT, "animation-steps"); //No I18N

    XmlName A_FILL = new XmlName(DRAW, P_DRAW, "fill"); //No I18N
    XmlName A_FILL_COLOR = new XmlName(DRAW, P_DRAW, "fill-color"); //No I18N
    XmlName A_SECONDARY_FILL_COLOR = new XmlName(DRAW, P_DRAW, "secondary-fill-color"); //No I18N
    XmlName A_FILL_GRADIENT_NAME = new XmlName(DRAW, P_DRAW, "fill-gradient-name"); //No I18N
    XmlName A_GRADIENT_STEP_COUNT = new XmlName(DRAW, P_DRAW, "gradient-step-count"); //No I18N
    XmlName A_FILL_HATCH_NAME = new XmlName(DRAW, P_DRAW, "fill-hatch-name"); //No I18N
    XmlName A_FILL_HATCH_SOLID = new XmlName(DRAW, P_DRAW, "fill-hatch-solid"); //No I18N
    XmlName A_FILL_IMAGE_NAME = new XmlName(DRAW, P_DRAW, "draw:fill-image-name"); //No I18N
    XmlName A_REPEAT = new XmlName(STYLE, P_STYLE, "repeat"); //No I18N
    XmlName A_FILL_IMAGE_WIDTH = new XmlName(DRAW, P_DRAW, "fill-image-width"); //No I18N
    XmlName A_FILL_IMAGE_HEIGHT = new XmlName(DRAW, P_DRAW, "fill-image-height"); //No I18N
    XmlName A_FILL_IMAGE_REF_POINT_X = new XmlName(DRAW, P_DRAW, "fill-image-ref-point-x"); //No I18N
    XmlName A_FILL_IMAGE_REF_POINT_Y = new XmlName(DRAW, P_DRAW, "fill-image-ref-point-Y"); //No I18N
    XmlName A_FILL_IMAGE_REF_POINT = new XmlName(DRAW, P_DRAW, "fill-image-ref-point"); //No I18N
    XmlName A_TILE_REPEAT_OFFSET = new XmlName(DRAW, P_DRAW, "tile-repeat-offset"); //No I18N
    XmlName A_OPACITY = new XmlName(DRAW, P_DRAW, "opacity"); //No I18N
    XmlName A_OPACITY_NAME = new XmlName(DRAW, P_DRAW, "opacity-name"); //No I18N
    XmlName A_FILL_RULE = new XmlName(SVG, P_SVG, "fill-rule"); //No I18N
    XmlName A_SYMBOL_COLOR = new XmlName(DRAW, P_DRAW, "symbol-color"); //No I18N
    XmlName A_AUTO_GROW_HEIGHT = new XmlName(DRAW, P_DRAW, "auto-grow-height"); //No I18N
    XmlName A_AUTO_GROW_WIDTH = new XmlName (DRAW, P_DRAW, "auto-grow-width"); //No I18N
    XmlName A_FIT_TO_SIZE = new XmlName(DRAW, P_DRAW, "fit-to-size"); //No I18N
    XmlName A_FIT_TO_COUNTOUR = new XmlName(DRAW, P_DRAW, "fit-to-contour"); //No I18N
    XmlName A_TEXTAREA_VERTICAL_ALIGN = new XmlName(DRAW, P_DRAW, "textarea-vertical-align"); //No I18N
    XmlName A_TEXT_AREA_HORIZONTAL_ALIGN = new XmlName(DRAW, P_DRAW, "textarea-horizontal-align"); //No I18N
    XmlName A_WRAP_OPTION = new XmlName(FO, P_FO, "wrap-option"); //No I18N

    XmlName A_COLOR_MODE = new XmlName(DRAW, P_DRAW, "color-mode"); //No I18N
    XmlName A_COLOR_INVERSION = new XmlName(DRAW, P_DRAW, "color-inversion"); //No I18N
    XmlName A_LUMINANCE = new XmlName(DRAW, P_DRAW, "luminance"); //No I18N
    XmlName A_CONTRAST = new XmlName(DRAW, P_DRAW, "contrast"); //No I18N
    XmlName A_GAMMA = new XmlName(DRAW, P_DRAW, "gamma"); //No I18N
    XmlName A_RED = new XmlName(DRAW, P_DRAW, "red"); //No I18N
    XmlName A_GREEN = new XmlName(DRAW, P_DRAW, "green"); //No I18N
    XmlName A_BLUE = new XmlName(DRAW, P_DRAW, "blue"); //No I18N
    XmlName A_IMAGE_OPACITY = new XmlName(DRAW, P_DRAW, "image-opacity"); //No I18N

    XmlName A_SHADOW__DRAW = new XmlName(DRAW, P_DRAW, "shadow"); //No I18N
    XmlName A_SHADOW_OFFSET_X = new XmlName(DRAW, P_DRAW, "shadow-offset-x"); //No I18N
    XmlName A_SHADOW_OFFSET_Y = new XmlName(DRAW, P_DRAW, "shadow-offset-y"); //No I18N
    XmlName A_SHADOW_COLOR = new XmlName(DRAW, P_DRAW, "shadow-color"); //No I18N
    XmlName A_SHADOW_OPACITY = new XmlName(DRAW, P_DRAW, "shadow-opacity"); //No I18N

    // End graphic-properties
    
    //Sparklines
    XmlName Z_SSPARKLINE = new XmlName(CALCEXT,P_CALCEXT,"Sparkline"); //No I18N // No I18N
    XmlName Z_SSPARKLINES_GROUP = new XmlName(CALCEXT,P_CALCEXT,"SparklinesGroup"); //No I18N // No I18N
    XmlName Z_SSPARKLINES = new XmlName(CALCEXT,P_CALCEXT,"Sparklines"); //No I18N // No I18N
    XmlName SOURCE = new XmlName(CALCEXT,P_CALCEXT,"source"); //No I18N // No I18N
    XmlName DESTINATION = new XmlName(CALCEXT,P_CALCEXT,"destination"); //No I18N // No I18N
    XmlName ORIENTATION = new XmlName(CALCEXT,P_CALCEXT,"orientation"); //No I18N // No I18N
    XmlName PROPERTIES = new XmlName(CALCEXT,P_CALCEXT,"Property"); //No I18N // No I18N
    XmlName TYPE = new XmlName(CALCEXT,P_CALCEXT,"type"); //No I18N// No I18N 
    XmlName FIRST_POINT_COLOR = new XmlName(CALCEXT,P_CALCEXT,"firstColor"); //No I18N // No I18N
    XmlName FIRST_POINT_TINT = new XmlName(CALCEXT,P_CALCEXT,"firstTint"); //No I18N
    XmlName FIRST_POINT_THEME = new XmlName(CALCEXT,P_CALCEXT,"firstTheme"); //No I18N
    XmlName LAST_POINT_COLOR = new XmlName(CALCEXT,P_CALCEXT,"lastColor"); //No I18N// No I18N
    XmlName LAST_POINT_TINT = new XmlName(CALCEXT,P_CALCEXT,"lastTint"); //No I18N
    XmlName LAST_POINT_THEME = new XmlName(CALCEXT,P_CALCEXT,"lastTheme"); //No I18N
    XmlName HIGH_POINT_COLOR = new XmlName(CALCEXT,P_CALCEXT,"highColor"); //No I18N// No I18N
    XmlName HIGH_POINT_TINT = new XmlName(CALCEXT,P_CALCEXT,"highTint"); //No I18N
    XmlName HIGH_POINT_THEME = new XmlName(CALCEXT,P_CALCEXT,"highTheme"); //No I18N
    XmlName LOW_POINT_COLOR = new XmlName(CALCEXT,P_CALCEXT,"lowColor"); //No I18N// No I18N
    XmlName LOW_POINT_TINT = new XmlName(CALCEXT,P_CALCEXT,"lowTint"); //No I18N
    XmlName LOW_POINT_THEME = new XmlName(CALCEXT,P_CALCEXT,"lowTheme"); //No I18N
    XmlName IS_MARKER_REQUIRED = new XmlName(CALCEXT,P_CALCEXT,"marker"); //No I18N// No I18N
    XmlName MARKER_COLOR = new XmlName(CALCEXT,P_CALCEXT,"markerColor"); //No I18N// No I18N
    XmlName MARKER_TINT = new XmlName(CALCEXT,P_CALCEXT,"markerTint"); //No I18N
    XmlName MARKER_THEME = new XmlName(CALCEXT,P_CALCEXT,"markerTheme"); //No I18N
    XmlName SPARKLINE_COLOR = new XmlName(CALCEXT,P_CALCEXT,"sparklineColor"); //No I18N// No I18N
    XmlName SPARKLINE_TINT = new XmlName(CALCEXT, P_CALCEXT, "sparklineTint"); //No I18N
    XmlName SPARKLINE_THEME = new XmlName(CALCEXT, P_CALCEXT, "sparklineTheme"); //No I18N
    XmlName NEGATIVE_COLOR = new XmlName(CALCEXT,P_CALCEXT,"negativeColor"); //No I18N// No I18N
    XmlName NEGATIVE_TINT = new XmlName(CALCEXT,P_CALCEXT,"negativeTint"); //No I18N
    XmlName NEGATIVE_THEME = new XmlName(CALCEXT,P_CALCEXT,"negativeTheme"); //No I18N
    XmlName Y_AXIS_MAXIMUM = new XmlName(CALCEXT,P_CALCEXT,"maximum"); //No I18N// No I18N
    XmlName Y_AXIS_MINIMUM = new XmlName(CALCEXT,P_CALCEXT,"minimum"); //No I18N// No I18N
    XmlName IS_X_AXIS_REQUIRED = new XmlName(CALCEXT,P_CALCEXT,"xaxis"); //No I18N// No I18N
    XmlName SHOW_HIDDEN_CELLS = new XmlName(CALCEXT,P_CALCEXT,"hidden"); //No I18N// No I18N
    XmlName SHOW_EMPTY_CELLS = new XmlName(CALCEXT,P_CALCEXT,"emptycell"); //No I18N// No I18N
    XmlName IS_X_AXIS_REVERSED = new XmlName(CALCEXT,P_CALCEXT,"reverse"); //No I18N// No I18N
    XmlName MINIMUM_TYPE = new XmlName(CALCEXT,P_CALCEXT,"minimumType"); //No I18N// No I18N
    XmlName MAXIMUM_TYPE = new XmlName(CALCEXT,P_CALCEXT,"maximumType"); //No I18N// No I18N
//    style:map

    // For existing Files, to read the MapStyle
//    XmlName A_CONDITION__STYLE = new XmlName(STYLE, P_STYLE, "condition"); //No I18N
//    XmlName A_APPLY_STYLE_NAME = new XmlName(STYLE, P_STYLE, "apply-style-name"); //No I18N
//    XmlName A_BASE_CELL_ADDRESS = new XmlName(STYLE, P_STYLE, "base-cell-address"); //No I18N
    
    // changed style:map PROPERTIES for LIBRE OFFICE update
    XmlName N_CONDITION_FORMATS = new XmlName(CALCEXT, P_CALCEXT, "conditional-formats"); //No I18N 
    XmlName N_CONDITION_FORMAT = new XmlName(CALCEXT, P_CALCEXT, "conditional-format"); //No I18N 
    XmlName A_TARGET_ADDRESS = new XmlName(CALCEXT, P_CALCEXT, "target-range-address"); //No I18N  
    XmlName A_C_S_PRIORITY = new XmlName(CALCEXT, P_CALCEXT, "priority"); //No I18N // No I18N
    
    // Conditional Format - Nodes and attributes
    XmlName N_CONDITION = new XmlName(CALCEXT, P_CALCEXT, "condition"); //No I18N 
    XmlName A_APPLY_STYLE_NAME_C_A_L_C = new XmlName(CALCEXT, P_CALCEXT, "apply-style-name"); //No I18N 
    XmlName A_VALUE_C_A_L_C = new XmlName(CALCEXT, P_CALCEXT, "value"); //No I18N // No I18N
    XmlName A_BASE_CELL_ADDRESS_C_A_L_C = new XmlName(CALCEXT, P_CALCEXT, "base-cell-address"); //No I18N // No I18N
    XmlName A_APPLY_COL_INDEX = new XmlName(CALCEXT, P_CALCEXT, "apply-col-index"); //No I18N
    XmlName A_C_S_AUTO_COLOR = new XmlName(CALCEXT, P_CALCEXT, "autocolor"); //No I18N // No I18N
    XmlName A_C_S_HIDE_TEXT = new XmlName(CALCEXT, P_CALCEXT, "hidetext"); //No I18N // No I18N
    XmlName A_C_S_SHOW_VALUE = new XmlName(CALCEXT, P_CALCEXT, "show-value"); //No I18N // No I18N
    XmlName A_C_S__VALUE  =   new XmlName(CALCEXT, P_CALCEXT, "value"); //No I18N  
    XmlName A_C_S__TYPE   =   new XmlName(CALCEXT, P_CALCEXT, "type"); //No I18N  
    
    // Conditional Format - exclusively for Date Rules.
    XmlName N_DATE_C_A_L_C = new XmlName(CALCEXT, P_CALCEXT, "date-is"); //No I18N // No I18N
    XmlName A_STYLE_C_A_L_C = new XmlName(CALCEXT, P_CALCEXT, "style"); //No I18N // No I18N
    XmlName A_DATE_C_A_L_C = new XmlName(CALCEXT, P_CALCEXT, "date"); //No I18N // No I18N
    
    // ColorScales - Nodes and attributes
    XmlName N_COLOR_SCALE = new XmlName(CALCEXT, P_CALCEXT, "color-scale"); //No I18N 
    XmlName N_COLOR__SCALE_ENTRY = new XmlName(CALCEXT, P_CALCEXT, "color-scale-entry"); //No I18N 
    XmlName A_COLOR_SCALE__COLOR  =   new XmlName(CALCEXT, P_CALCEXT, "color"); //No I18N  
    
    
     // IconSets - Nodes and attributes
    XmlName N_ICON__SET = new XmlName(CALCEXT, P_CALCEXT, "icon-set"); //No I18N 
    XmlName N_FORMATTING_ENTRY = new XmlName(CALCEXT, P_CALCEXT, "formatting-entry"); //No I18N 
    XmlName A_ICON__SET__TYPE  =   new XmlName(CALCEXT, P_CALCEXT, "icon-set-type"); //No I18N  
    XmlName A_ICON__SET__NAME  =   new XmlName(CALCEXT, P_CALCEXT, "icon-set-name"); //No I18N  
    XmlName A_ICON__DEFAULT__SIZE = new XmlName(CALCEXT, P_CALCEXT, "icon-default-size"); //No I18N 
    XmlName A_ICON__REVERSE__ORDER = new XmlName(CALCEXT, P_CALCEXT, "icon-reverse-order"); //No I18N 
    
    XmlName A_ICON_SET__NAME = new XmlName(CALCEXT, P_CALCEXT, "name"); //No I18N // No I18N
    XmlName A_ICON_SET__I_D = new XmlName(CALCEXT, P_CALCEXT, "id"); //No I18N // No I18N
    XmlName A_ICON_SET__CRITERIA = new XmlName(CALCEXT, P_CALCEXT, "criteria"); //No I18N // No I18N
    
    // DataBars - Nodes and attributes
    XmlName N_DATA__BAR = new XmlName(CALCEXT, P_CALCEXT, "data-bar"); //No I18N // No I18N
    XmlName A_DATA__BAR__DIRECTION = new XmlName(CALCEXT, P_CALCEXT, "direction"); //No I18N // No I18N
    XmlName A_DATA__BAR__FILL__TYPE = new XmlName(CALCEXT, P_CALCEXT, "fill-type"); //No I18N // No I18N
    XmlName A_GRADIENT = new XmlName(CALCEXT, P_CALCEXT, "gradient"); //No I18N // No I18N
 XmlName A_FILL_NEGATIVE__COLOR = new XmlName(CALCEXT, P_CALCEXT, "negative-color"); //No I18N // No I18N
 XmlName A_FILL_NEGATIVE_THEME_COLOR = new XmlName(CALCEXT, P_CALCEXT, "negative-theme-color"); //No I18N // No I18N
 XmlName A_FILL_NEGATIVE_TINT_COLOR = new XmlName(CALCEXT, P_CALCEXT, "negative-tint-color"); //No I18N // No I18N
    XmlName A_FILL_POSITIVE__COLOR = new XmlName(CALCEXT, P_CALCEXT, "positive-color"); //No I18N // No I18N
    XmlName A_FILL_POSITIVE_THEME_COLOR = new XmlName(CALCEXT, P_CALCEXT, "positive-theme-color"); //No I18N
    XmlName A_FILL_POSITIVE_TINT_COLOR = new XmlName(CALCEXT, P_CALCEXT, "positive-tint-color"); //No I18N
    XmlName A_DATA__BAR__BORDER__TYPE = new XmlName(CALCEXT, P_CALCEXT, "border-type"); //No I18N
 XmlName A_BORDER__NEGATIVE__COLOR = new XmlName(CALCEXT, P_CALCEXT, "border-negative-color"); //No I18N
 XmlName A_BORDER__NEGATIVE_THEME_COLOR = new XmlName(CALCEXT, P_CALCEXT, "border-negative-theme-color"); //No I18N
 XmlName A_BORDER__NEGATIVE_TINT_COLOR = new XmlName(CALCEXT, P_CALCEXT, "border-negative-tint-color"); //No I18N
 XmlName A_BORDER__POSITIVE__COLOR = new XmlName(CALCEXT, P_CALCEXT, "border-positive-color"); //No I18N
 XmlName A_BORDER__POSITIVE_THEME_COLOR = new XmlName(CALCEXT, P_CALCEXT, "border-positive-theme-color"); //No I18N
 XmlName A_BORDER__POSITIVE_TINT_COLOR = new XmlName(CALCEXT, P_CALCEXT, "border-positive-tint-color"); //No I18N
    XmlName A_AXIS__POSITION = new XmlName(CALCEXT, P_CALCEXT, "axis-position"); //No I18N
 XmlName A_AXIS__COLOR = new XmlName(CALCEXT, P_CALCEXT, "axis-color"); //No I18N
 XmlName A_AXIS_THEME_COLOR = new XmlName(CALCEXT, P_CALCEXT, "axis-theme-color"); //No I18N
 XmlName A_AXIS_TINT_COLOR = new XmlName(CALCEXT, P_CALCEXT, "axis-tint-color"); //No I18N

//    Text-properties

    XmlName A_COLOR = new XmlName(FO, P_FO, "color"); //No I18N
    XmlName A_CONDITION = new XmlName(TEXT, P_TEXT, "condition"); //No I18N

    XmlName A_COUNTRY = new XmlName(FO, P_FO, "country"); //No I18N
    XmlName A_COUNTRY_ASIAN = new XmlName(STYLE, P_STYLE, "country-asian"); //No I18N
    XmlName A_COUNTRY_COMPLEX = new XmlName(STYLE, P_STYLE, "country-complex"); //No I18N

    XmlName A_FONT_VARIANT = new XmlName(FO, P_FO, "font-variant"); //No I18N
    XmlName A_TEXT_TRANSFORM = new XmlName(FO, P_FO, "text-transform"); //No I18N
    XmlName A_USE_WINDOW_FONT_COLOR = new XmlName(STYLE, P_STYLE, "use-window-font-color"); //No I18N
    XmlName A_TEXT_OUTLINE = new XmlName(STYLE, P_STYLE, "text-outline"); //No I18N
    XmlName A_TEXT_LINE_THROUGH_TYPE = new XmlName(STYLE, P_STYLE, "text-line-through-type"); //No I18N
    XmlName A_TEXT_LINE_THROUGH_STYLE = new XmlName(STYLE, P_STYLE, "text-line-through-style"); //No I18N
    XmlName A_TEXT_LINE_THROUGH_WIDTH = new XmlName(STYLE, P_STYLE, "text-line-through-width"); //No I18N
    XmlName A_TEXT_LINE_THROUGH_COLOR = new XmlName(STYLE, P_STYLE, "text-line-through-color"); //No I18N
    XmlName A_TEXT_LINE_THROUGH_TEXT = new XmlName(STYLE, P_STYLE, "text-line-through-text"); //No I18N
    XmlName A_TEXT_LINE_THROUGH_TEXT_STYLE = new XmlName(STYLE, P_STYLE, "text-line-through-text-style"); //No I18N
    XmlName A_TEXT_POSITION = new XmlName(STYLE, P_STYLE, "text-position"); //No I18N
    XmlName A_FONT_NAME_ASIAN = new XmlName(STYLE, P_STYLE, "font-name-asian"); //No I18N
    XmlName A_FONT_NAME_COMPLEX = new XmlName(STYLE, P_STYLE, "font-name-complex"); //No I18N

    XmlName A_FONT_FAMILY__F_O = new XmlName(FO, P_FO, "font-family"); //No I18N
    XmlName A_FONT_FAMILY_ASIAN = new XmlName(STYLE, P_STYLE, "font-family-asian"); //No I18N
    XmlName A_FONT_FAMILY_COMPLEX = new XmlName(STYLE, P_STYLE, "font-family-complex"); //No I18N

    XmlName A_FONT_FAMILY_GENERIC_ASIAN = new XmlName(STYLE, P_STYLE, "font-family-generic-asian"); //No I18N
    XmlName A_FONT_FAMILY_GENERIC_COMPLEX = new XmlName(STYLE, P_STYLE, "font-family-generic-complex"); //No I18N

    XmlName A_FONT_STYLE_NAME = new XmlName(STYLE, P_STYLE, "font-style-name"); //No I18N
    XmlName A_FONT_STYLE_NAME_ASIAN = new XmlName(STYLE, P_STYLE, "font-style-name-asian"); //No I18N
    XmlName A_FONT_STYLE_NAME_COMPLEX = new XmlName(STYLE, P_STYLE, "font-style-name-complex"); //No I18N

    XmlName A_FONT_PITCH_NAME = new XmlName(STYLE, P_STYLE, "font-pitch-name"); //No I18N
    XmlName A_FONT_PITCH_NAME_ASIAN = new XmlName(STYLE, P_STYLE, "font-pitch-name-asian"); //No I18N
    XmlName A_FONT_PITCH_NAME_COMPLEX = new XmlName(STYLE, P_STYLE, "font-pitch-name-complex"); //No I18N

    XmlName A_FONT_CHAR_SET = new XmlName(STYLE, P_STYLE, "font-charset"); //No I18N
    XmlName A_FONT_CHAR_SET_ASIAN = new XmlName(STYLE, P_STYLE, "font-charset-asian"); //No I18N
    XmlName A_FONT_CHAR_SET_COMPLEX = new XmlName(STYLE, P_STYLE, "font-charset-complex"); //No I18N

    XmlName A_FONT_SIZE_ASIAN = new XmlName(STYLE, P_STYLE, "font-size-asian"); //No I18N
    XmlName A_FONT_SIZE_COMPLEX = new XmlName(STYLE, P_STYLE, "font-size-complex"); //No I18N

    XmlName A_FONT_SIZE_REL = new XmlName(STYLE, P_STYLE, "font-size-rel"); //No I18N
    XmlName A_FONT_SIZE_REL_ASIAN = new XmlName(STYLE, P_STYLE, "font-size-rel-asian"); //No I18N
    XmlName A_FONT_SIZE_REL_COMPLEX = new XmlName(STYLE, P_STYLE, "font-size-rel-complex"); //No I18N

    XmlName A_SCRIPT_TYPE = new XmlName(STYLE, P_STYLE, "script-type"); //No I18N
    XmlName A_LETTER_SPACING = new XmlName(FO, P_FO, "letter-spacing"); //No I18N

    XmlName A_LANGUAGE = new XmlName(FO, P_FO, "language"); //No I18N
    XmlName A_LANGUAGE_ASIAN = new XmlName(STYLE, P_STYLE, "language-asian"); //No I18N
    XmlName A_LANGUAGE_COMPLEX = new XmlName(STYLE, P_STYLE, "language-complex"); //No I18N

    XmlName A_FONT_STYLE_ASIAN = new XmlName(STYLE, P_STYLE, "font-style-asian"); //No I18N
    XmlName A_FONT_STYLE_COMPLEX = new XmlName(STYLE, P_STYLE, "font-style-complex"); //No I18N

    XmlName A_FONT_RELIEF = new XmlName(STYLE, P_STYLE, "font-relief"); //No I18N
    XmlName A_TEXT_SHADOW = new XmlName(FO, P_FO, "text-shadow"); //No I18N

    XmlName A_TEXT_UNDERLINE_TYPE = new XmlName(STYLE, P_STYLE, "text-underline-type"); //No I18N
    XmlName A_TEXT_UNDERLINE_STYLE = new XmlName(STYLE, P_STYLE, "text-underline-style"); //No I18N
    XmlName A_TEXT_UNDERLINE_COLOR = new XmlName(STYLE, P_STYLE, "text-underline-color"); //No I18N
    XmlName A_TEXT_UNDERLINE_MODE = new XmlName(STYLE, P_STYLE, "text-underline-mode"); //No I18N
    XmlName A_TEXT_UNDERLINE_WIDTH = new XmlName(STYLE, P_STYLE, "text-underline-width"); //No I18N

    XmlName A_FONT_WEIGHT_ASIAN = new XmlName(STYLE, P_STYLE, "font-weight-asian"); //No I18N
    XmlName A_FONT_WEIGHT_COMPLEX = new XmlName(STYLE, P_STYLE, "font-weight-complex"); //No I18N

    XmlName A_TEXT_LINE_THROUGH_MODE = new XmlName(STYLE, P_STYLE, "text-line-through-mode"); //No I18N
    XmlName A_LETTER_KERNING = new XmlName(STYLE, P_STYLE, "letter-kerning"); //No I18N

    XmlName A_TEXT_BLINKING = new XmlName(STYLE, P_STYLE, "text-blinking"); //No I18N
    XmlName A_TEXT_COMBINE = new XmlName(STYLE, P_STYLE, "text-combine"); //No I18N
    XmlName A_TEXT_COMBINE_START_CHAR = new XmlName(STYLE, P_STYLE, "text-combine-start-char"); //No I18N
    XmlName A_TEXT_COMBINE_END_CHAR = new XmlName(STYLE, P_STYLE, "text-combine-end-char"); //No I18N

    XmlName A_TEXT_EMPHASIZE = new XmlName(STYLE, P_STYLE, "text-emphasize"); //No I18N
    XmlName A_TEXT_SCALE = new XmlName(STYLE, P_STYLE, "text-scale"); //No I18N

    XmlName A_TEXT_ROTATION_ANGLE = new XmlName(STYLE, P_STYLE, "text-rotation-angle"); //No I18N
    XmlName A_TEXT_ROTATION_SCALE = new XmlName(STYLE, P_STYLE, "text-rotation-scale"); //No I18N

    XmlName A_HYPHENATE = new XmlName(FO, P_FO, "hyphenate"); //No I18N

    XmlName A_HYPHENATION_REMAIN_CHAR_COUNT = new XmlName(FO, P_FO, "hyphenation-remain-char-count"); //No I18N
    XmlName A_HYPHENATION_PUSH_CHAR_COUNT = new XmlName(FO, P_FO, "hyphenation-push-char-count"); //No I18N

//  Number-style
    XmlName A_DISPLAY_NAME = new XmlName(STYLE, P_STYLE, "display-name"); //No I18N
    
    XmlName A_STYLE_VOLATILE = new XmlName(STYLE, P_STYLE, "volatile"); //No I18N

    XmlName A_V_ALIGN = new XmlName(STYLE, P_STYLE, "vertical-align"); //No I18N
    XmlName A_TEXT_ALIGN_SOURCE = new XmlName(STYLE, P_STYLE, "text-align-source"); //No I18N
    XmlName A_DIRECTION = new XmlName(STYLE, P_STYLE, "direction"); //No I18N
    XmlName A_G_O_VERTICAL = new XmlName(STYLE, P_STYLE, "glyph-orientation-vertical"); //No I18N
    XmlName A_SHADOW = new XmlName(STYLE, P_STYLE, "shadow"); //No I18N

    XmlName A_BORDER = new XmlName(FO,P_FO, "border"); //No I18N
    XmlName A_BORDER_TOP = new XmlName(FO,P_FO,"border-top"); //No I18N
    XmlName A_BORDER_BOTTOM = new XmlName(FO,P_FO,"border-bottom"); //No I18N
    XmlName A_BORDER_LEFT = new XmlName(FO,P_FO,"border-left"); //No I18N
    XmlName A_BORDER_RIGHT = new XmlName(FO,P_FO,"border-right"); //No I18N
    XmlName A_BORDER_INNER_VERTICAL = new XmlName(FO, P_FO, "border-inner-vertical"); //No I18N
    XmlName A_BORDER_INNER_HORIZONTAL = new XmlName(FO, P_FO, "border-inner-horizontal"); //No I18N

    XmlName A_BORDER_LINE_WIDTH = new XmlName(STYLE,P_STYLE, "border-line-width"); //No I18N
    XmlName A_BORDER_LINE_WIDTH_TOP = new XmlName(STYLE,P_STYLE,"border-line-width-top"); //No I18N
    XmlName A_BORDER_LINE_WIDTH_BOTTOM = new XmlName(STYLE,P_STYLE,"border-line-width-bottom"); //No I18N
    XmlName A_BORDER_LINE_WIDTH_LEFT = new XmlName(STYLE,P_STYLE,"border-line-width-left"); //No I18N
    XmlName A_BORDER_LINE_WIDTH_RIGHT = new XmlName(STYLE,P_STYLE,"border-line-width-right"); //No I18N

    // Border Style tag and attributes
    XmlName BORDERS = new XmlName(FO,P_STYLE,"borders"); //No I18N
    XmlName BORDER_PROPERTIES = new XmlName(FO,P_STYLE,"border-properties"); //No I18N
    XmlName BORDER_TYPE = new XmlName(FO,P_STYLE,"type"); //No I18N
    XmlName BORDER_COLOR = new XmlName(FO,P_STYLE,"color"); //No I18N
    XmlName BORDER_THEME = new XmlName(FO,P_STYLE,"theme-color"); //No I18N
    XmlName BORDER_TINT = new XmlName(FO,P_STYLE,"tint"); //No I18N
    XmlName BORDER_SIZE = new XmlName(FO,P_STYLE,"size"); //No I18N

    XmlName A_FONT_B_G_IMAGE = new XmlName(STYLE, P_STYLE, "background-image"); //No I18N

    XmlName A_PADDING = new XmlName(FO,P_FO, "padding"); //No I18N
    XmlName A_PADDING_TOP = new XmlName(FO,P_FO,"padding-top"); //No I18N
    XmlName A_PADDING_BOTTOM = new XmlName(FO,P_FO,"padding-bottom"); //No I18N
    XmlName A_PADDING_LEFT = new XmlName(FO,P_FO,"padding-left"); //No I18N
    XmlName A_PADDING_RIGHT = new XmlName(FO,P_FO,"padding-right"); //No I18N

    XmlName A_DIAGONAL_T_L_B_R = new XmlName(STYLE,P_STYLE, "diagonal-tl-br"); //No I18N
    XmlName A_DIAGONAL_B_L_T_R = new XmlName(STYLE,P_STYLE,"diagonal-bl-tr"); //No I18N
    XmlName A_DIAGONAL_T_L_B_R_WIDTHS = new XmlName(STYLE,P_STYLE, "diagonal-tl-br-widths"); //No I18N
    XmlName A_DIAGONAL_B_L_T_R_WIDTHS = new XmlName(STYLE,P_STYLE,"diagonal-bl-tr-widths"); //No I18N

    XmlName A_ROTATION_ANGLE = new XmlName(STYLE,P_STYLE, "rotation-angle"); //No I18N
    XmlName A_ROTATION_ALIGN = new XmlName(STYLE,P_STYLE, "rotation-align"); //No I18N

    XmlName A_CELL_PROTECT = new XmlName(STYLE,P_STYLE, "cell-protect"); //No I18N
    XmlName A_DECIMAL_PLACES = new XmlName(STYLE, P_STYLE, "decimal-places");//No I18N
    XmlName A_PRINT_CONTENT = new XmlName(STYLE,P_STYLE, "print-content"); //No I18N

    XmlName A_REPEAT_CONTENT = new XmlName(STYLE,P_STYLE, "repeat-content"); //No I18N
    XmlName A_SHRINK_TO_FIT = new XmlName(STYLE,P_STYLE, "shrink-to-fit"); //No I18N
    XmlName A_WRAP = new XmlName(FO, P_FO, "wrap-option"); //No I18N
    XmlName A_DISPLAY_TYPE = new XmlName(STYLE,P_STYLE,"display-type"); // No I18N

    /////////

    // SheetStyle
    XmlName A_WIDTH = new XmlName(STYLE, P_STYLE, "width"); //No I18N
    XmlName A_REL_WIDTH = new XmlName(STYLE, P_STYLE, "rel-width"); //No I18N

    XmlName A_ALIGN = new XmlName(TABLE, P_TABLE, "align"); //No I18N

    XmlName A_MARGIN = new XmlName(FO,P_FO, "margin"); //No I18N
    XmlName A_MARGIN_TOP = new XmlName(FO,P_FO,"margin-top"); //No I18N
    XmlName A_MARGIN_BOTTOM = new XmlName(FO,P_FO,"margin-bottom"); //No I18N
    XmlName A_MARGIN_LEFT = new XmlName(FO,P_FO,"margin-left"); //No I18N
    XmlName A_MARGIN_RIGHT = new XmlName(FO,P_FO,"margin-right"); //No I18N

    XmlName A_PAGE_NUMBER = new XmlName(STYLE, P_STYLE, "page-number"); //No I18N
    XmlName A_KEEP_WITH_NEXT = new XmlName(FO,P_FO,"keep-with-next"); //No I18N
    XmlName A_MAY_BREAK_BETWEEN_ROWS = new XmlName(STYLE, P_STYLE, "may-break-between-rows"); //No I18N

    XmlName A_BORDER_MODEL = new XmlName(TABLE, P_TABLE, "border-model"); //No I18N
    XmlName A_DISPLAY = new XmlName(TABLE, P_TABLE, "display"); //No I18N
    XmlName A_WRITING_MODE = new XmlName(STYLE, P_STYLE, "writing-mode"); //No I18N
    XmlName ATABCOLOR = new XmlName(TABLEOOO, P_TABLEOOO, "tab-color"); //No I18N

    //////
    XmlName A_FONT_NAME = new XmlName(STYLE, P_STYLE, "font-name"); //No I18N
    XmlName A_FONT_SIZE = new XmlName(FO, P_FO, "font-size"); //No I18N
    XmlName A_FONT_STYLE = new XmlName(FO, P_FO, "font-style"); //No I18N
    XmlName A_FONT_WEIGHT = new XmlName(FO, P_FO, "font-weight"); //No I18N
    XmlName A_H_ALIGN = new XmlName(FO, P_FO, "text-align"); //No I18N
    XmlName A_TEXT_UNDERLINE = new XmlName(STYLE, P_STYLE, "text-underline"); //No I18N

    ///// ZSThemes
    XmlName N_ZSTHEME = new XmlName(OFFICE, P_OFFICE, "zstheme");//No I18N
    XmlName A_THEME_NAME = new XmlName(STYLE, P_STYLE, "theme-name");//No I18N
    XmlName N_COLOR_SCHEME = new XmlName(STYLE, P_STYLE, "color-scheme");//No I18N
    XmlName A_COLOR_SCHEME_NAME = new XmlName(STYLE, P_STYLE, "color-scheme-name");//No I18N
    XmlName A_COLORS = new XmlName(STYLE, P_STYLE, "colors");//No I18N
    XmlName N_FONT_SCHEME = new XmlName(STYLE, P_STYLE, "font-scheme");//No I18N
    XmlName A_FONT_SCHEME_NAME = new XmlName(STYLE, P_STYLE, "font-scheme-name");//No I18N
    XmlName A_FONTS = new XmlName(STYLE, P_STYLE, "fonts");//No I18N

    //ADDED BY GANESH
    XmlName A_TABLE_PRINT = new XmlName(TABLE, P_TABLE, "print"); //No I18N
    XmlName A_TABLE_PRINT_RANGES = new XmlName(TABLE, P_TABLE, "print-ranges"); //No I18N
    XmlName A_TABLE_PROTECTED = new XmlName(TABLE, P_TABLE, "protected"); //No I18N
    XmlName A_TABLE_PROTECTION_KEY = new XmlName(TABLE, P_TABLE, "protection-key"); //No I18N
    //END OF GANESH
    XmlName A_TABLE_NAME = new XmlName(TABLE, P_TABLE, "name"); //No I18N
    XmlName A_TABLE_STYLE_NAME = new XmlName(TABLE, P_TABLE, "style-name"); //No I18N
    XmlName A_TABLE_VALUE_TYPE = new XmlName(OFFICE, P_OFFICE, "value-type"); //No I18N
    XmlName A_CALCEXT_VALUE_TYPE = new XmlName(CALCEXT, P_CALCEXT, "value-type"); //No I18N // No I18N
    
    XmlName A_TABLE_IS_DYNAMIC_ARRAY = new XmlName(TABLE, P_TABLE, "is-dynamic-array"); //No I18N
    XmlName A_TABLE_FORMULA = new XmlName(TABLE, P_TABLE, "formula"); //No I18N
    XmlName A_TABLE_LOCALIZED_FORMULA = new XmlName(TABLE, P_TABLE, "localized-formula"); //No I18N // No I18N
    XmlName A_TABLE_DISCUSSION_I_D = new XmlName(TABLE, P_TABLE, "discussionid"); //No I18N 
    XmlName A_TABLE_VALUE = new XmlName(OFFICE, P_OFFICE, "value"); //No I18N
    XmlName A_CURRENCY = new XmlName(OFFICE, P_OFFICE, "currency"); //No I18N
    XmlName A_QUOTE_PREFIX = new XmlName(TABLE, P_TABLE, "quotePrefix"); //No I18N 
    XmlName A_CELL_LINK = new XmlName(TABLE, P_TABLE, "link"); //No I18N 
//    XmlName A_IS_DERIVED_PATTERN = new XmlName(TABLE, P_TABLE, "isDerivedPattern"); //No I18N

    XmlName A_TABLE_BOOL_VAL = new XmlName(OFFICE, P_OFFICE, "boolean-value"); //No I18N
    XmlName A_TABLE_STRING_VAL = new XmlName(OFFICE, P_OFFICE, "string-value"); //No I18N
    XmlName A_TABLE_DATE_VAL = new XmlName(OFFICE, P_OFFICE, "date-value"); //No I18N
    XmlName A_TABLE_TIME_VAL = new XmlName(OFFICE, P_OFFICE, "time-value"); //No I18N

    XmlName A_NO_COLS_REPEATED = new XmlName(TABLE, P_TABLE, "number-columns-repeated"); //No I18N
    XmlName A_NO_COLS_SPANNED = new XmlName(TABLE, P_TABLE, "number-columns-spanned"); //No I18N
    XmlName A_NO_ROWS_REPEATED = new XmlName(TABLE, P_TABLE, "number-rows-repeated"); //No I18N
    XmlName A_NO_ROWS_SPANNED = new XmlName(TABLE, P_TABLE, "number-rows-spanned"); //No I18N

    XmlName A_MATRIX_COLS_SPANNED = new XmlName(TABLE, P_TABLE, "number-matrix-columns-spanned"); //No I18N
    XmlName A_MATRIX_ROWS_SPANNED = new XmlName(TABLE, P_TABLE, "number-matrix-rows-spanned"); //No I18N

    XmlName A_IS_ARRAY_CELL = new XmlName(TABLE, P_TABLE, "isarray-cell"); //No I18N
    XmlName A_ARRAY_COLS_SPANNED = new XmlName(TABLE, P_TABLE, "array-columns-spanned"); //No I18N
    XmlName A_ARRAYS_ROWS_SPANNED = new XmlName(TABLE, P_TABLE, "array-rows-spanned"); //No I18N
    XmlName A_MATRIX_AUTO_COLS_SPANNED = new XmlName(TABLE, P_TABLE, "number-matrix-auto-columns-spanned"); //No I18N
    XmlName A_MATRIX_AUTO_ROWS_SPANNED = new XmlName(TABLE, P_TABLE, "number-matrix-auto-rows-spanned"); //No I18N
    XmlName A_MATRIX_AUTO_ERROR_ROWOFFSET = new XmlName(TABLE, P_TABLE, "number-matrix-auto-error-rowoffset");//No I18N
    XmlName A_MATRIX_AUTO_ERROR_COLOFFSET = new XmlName(TABLE, P_TABLE, "number-matrix-auto-error-coloffset");//No I18N

    XmlName A_DEF_CELL_STYLE_NAME = new XmlName(TABLE, P_TABLE, "default-cell-style-name"); //No I18N
    XmlName A_VISIBILITY = new XmlName(TABLE, P_TABLE, "visibility"); //No I18N

    //Group
    XmlName A_SUMMARYBELOW = new XmlName(TABLE, P_TABLE, "summaryBelow"); //No I18N
    XmlName A_SUMMARYRIGHT = new XmlName(TABLE, P_TABLE, "summaryRight"); //No I18N

    //Forms
    XmlName A_APPLY_DESIGN_MODE = new XmlName(FORM,P_FORM,"apply-design-mode"); //No I18N
    XmlName A_AUTOMATIC_FOCUS = new XmlName(FORM,P_FORM,"automatic-focus"); //No I18N

    XmlName A_HREF = new XmlName(XLINK,P_XLINK,"href"); //No I18N
    XmlName A_TYPE_IMAGE = new XmlName(XLINK, P_XLINK, "type"); //No I18N 
    XmlName A_SHOW_IMAGE = new XmlName(XLINK, P_XLINK, "show"); //No I18N 
    XmlName A_ACTUATE_IMAGE = new XmlName(XLINK, P_XLINK, "actuate"); //No I18N 

    XmlName A_FORM_NAME = new XmlName(FORM,P_FORM,"name"); //No I18N
    XmlName A_APPLY_FILTER = new XmlName(FORM,P_FORM,"apply-filter"); //No I18N
    XmlName A_CTRL_IMPL = new XmlName(FORM,P_FORM,"control-implementation"); //No I18N
    XmlName A_FORM_ID = new XmlName(FORM,P_FORM,"id"); //No I18N
    XmlName A_LABEL = new XmlName(FORM,P_FORM,"label"); //No I18N
    XmlName A_IMAGE_DATA = new XmlName(FORM,P_FORM,"image-data"); //No I18N
    XmlName A_IMAGE_POSITION = new XmlName(FORM,P_FORM,"image-position"); //No I18N
    XmlName A_DELAY_FOR_REPEAT = new XmlName(FORM,P_FORM,"delay-for-repeat"); //No I18N
    XmlName A_COMMAND_TYPE = new XmlName(FORM,P_FORM,"command-type"); //No I18N

    XmlName A_TARGET_FRAME = new XmlName(OFFICE,P_OFFICE,"target-frame"); //No I18N

    XmlName A_PROPERTY_NAME = new XmlName(FORM,P_FORM,"property-name"); //No I18N


    /*nodes for styles.xml
     */

    XmlName N_OFFICE_DOC_STYLE = new XmlName(OFFICE, P_OFFICE, "document-styles"); //No I18N
    XmlName N_STYLES = new XmlName(OFFICE, P_OFFICE, "styles"); //No I18N
    XmlName N_DEFAULT_STYLE = new XmlName(STYLE, P_STYLE, "default-style"); //No I18N
    //End


    ////For Macros Parsing
    String LIBRARY="http://openoffice.org/2000/library"; //No I18N 
    String SCRIPTS="http://openoffice.org/2000/script"; //No I18N 
    String P_LIBRARY = "library"; //No I18N 



    XmlName N_LIBRARY = new XmlName(LIBRARY,P_LIBRARY, "library"); //No I18N 
    XmlName N_LIBRARY_ELEMENT = new XmlName(LIBRARY, P_LIBRARY, "element"); //No I18N 
    XmlName N_LIBRARIES = new XmlName(LIBRARY, P_LIBRARY, "libraries"); //No I18N 
    XmlName N_SCRIPT_MODULE = new XmlName(SCRIPTS, P_SCRIPT, "module"); //No I18N 
    //Macro Attributes
    XmlName A_LIBRARY_NAME = new XmlName(LIBRARY,P_LIBRARY,"name"); //No I18N 
    XmlName A_LIBRARY_READ_ONLY = new XmlName(LIBRARY,P_LIBRARY,"readonly"); //No I18N 
    XmlName A_LIBRARY_PASSWORD_PROTECTED = new XmlName(LIBRARY,P_LIBRARY,"passwordprotected"); //No I18N 
    XmlName A_LIBRARY_LINK = new XmlName(LIBRARY,P_LIBRARY,"link"); //No I18N 
    XmlName A_SCRIPT_NAME = new XmlName(SCRIPTS,P_SCRIPT,"name"); //No I18N 
    XmlName A_SCRIPT_LANGUAGE = new XmlName(SCRIPTS,P_SCRIPT,"language"); //No I18N 
    
    // event listeners
    XmlName A_SCRIPT_EVENT_LANGUAGE = new XmlName(SCRIPT,P_SCRIPT,"language"); //No I18N 
    XmlName A_SCRIPT_EVENT_NAME = new XmlName(SCRIPT,P_SCRIPT,"event-name"); //No I18N
    
    //////// For Macros ends
    
    // custom attributes name
    XmlName A_TABLE_ID = new XmlName(TABLE, P_TABLE, "id"); //No I18N
    XmlName ATABLE_AUTHUSERS = new XmlName(TABLE, P_TABLE, "authusers"); // protectedRange - authorized user ids. //No I18N
    XmlName ATABLE_UNAUTHUSERS = new XmlName(TABLE, P_TABLE, "unauthusers"); // protectedRange - authorized user ids. //No I18N
    XmlName ATABLE_AUTHGROUPS = new XmlName(TABLE, P_TABLE, "authgroups"); // protectedRange - authorized user ids. //No I18N
    XmlName ATABLE_AUTHORGS = new XmlName(TABLE, P_TABLE, "authorgs"); // protectedRange - authorized org ids. //No I18N
    XmlName ATABLE_IS_PUBLIC_AUTHORIZED = new XmlName(TABLE, P_TABLE, "ispublicauthorized"); // protectedRange - authorized org ids. //No I18N
    XmlName ATABLE_IS_ALLOW_INSERT = new XmlName(TABLE, P_TABLE, "is-allow-insert"); // protectedRange - allow insert row/column actions //No I18N
    XmlName ATABLE_IS_ALLOW_FORMATS = new XmlName(TABLE, P_TABLE, "is-allow-formats"); // protectedRange - allow format actions //No I18N
    XmlName ATABLE_PLACEHOLDER_DESC = new XmlName(TABLE, P_TABLE, "placeholder-desc");//No I18N
    XmlName ATABLE_AUTHORIZEDEXTERNALSHARELINKS = new XmlName(TABLE, P_TABLE, "authexsharelinks");// protectedRange - authorized external sharred link ids. //No I18N
    XmlName ATABLE_UNAUTHORIZEDEXTERNALSHARELINKS = new XmlName(TABLE, P_TABLE, "unauthexsharelinks");// protectedRange - unauthorized external sharred link user ids. //No I18N
    
    //formula tags and attributes
    XmlName EXPRESSIONS= new XmlName(TABLE, P_TABLE, "zsexpressions"); //No I18N 
    XmlName EXPRESSION= new XmlName(TABLE, P_TABLE, "expression"); //No I18N 
    XmlName EXPRESSION_NAME= new XmlName(TABLE, P_TABLE, "name"); //No I18N 
    
    XmlName CELL_EXPRESSION_NAME= new XmlName(TABLE, P_TABLE, "zsexpressionname"); //No I18N 

    //pattern
    XmlName N_PATTERNS = new XmlName(TABLE, P_TABLE, "zsPatterns"); //No I18N 

  XmlName N_PATTERN = new XmlName(TABLE, P_TABLE, "zsPattern"); //No I18N
   XmlName N_DEFAULT_DATE_PATTERN = new XmlName(TABLE, P_TABLE, "zsDefaultDatePattern"); //No I18N
    XmlName A_PATTERN_NAME = new XmlName(TABLE, P_TABLE, "name"); //No I18N
    XmlName A_PATTERN_STRING = new XmlName(TABLE, P_TABLE, "patternString"); //No I18N 
    XmlName A_IS_AUTO_ORDER = new XmlName(TABLE, P_TABLE, "isAutoOrder"); //No I18N 
    XmlName A_IS_ACCOUNTING_FORMAT = new XmlName(TABLE, P_TABLE, "isAccounting"); //No I18N 
    XmlName A_PATTERN_COUNTRY = new XmlName(TABLE, P_TABLE, "country"); //No I18N
    XmlName A_PATTERN_LANGUAGE = new XmlName(TABLE, P_TABLE,"language"); //No I18N
    
    XmlName ZS_CELL_STYLE_PATTERN_NAME = new XmlName(TABLE, P_TABLE, "zsPatternName"); //No I18N 
    
    /// Names for settings.xml
    String CONFIG = "urn:oasis:names:tc:opendocument:xmlns:config:1.0"; //No I18N
    String P_CONFIG = "config"; //No I18N
    //Nodes
    XmlName N_CONFIG = new XmlName(CONFIG, P_CONFIG, ""); //No I18N
    XmlName N_DOUCUMENT_SETTINGS = new XmlName(OFFICE, P_OFFICE, "document-settings"); //No I18N
    XmlName N_SETTINGS = new XmlName(OFFICE, P_OFFICE, "settings"); //No I18N
    XmlName N_CONFIG_ITEM_SET = new XmlName(CONFIG, P_CONFIG, "config-item-set"); //No I18N
    XmlName N_CONFIG_ITEM_MAP_INDEXED = new XmlName(CONFIG, P_CONFIG, "config-item-map-indexed"); //No I18N
    XmlName N_CONFIG_ITEM_MAP_ENTRY = new XmlName(CONFIG, P_CONFIG, "config-item-map-entry"); //No I18N
    XmlName N_CONFIG_ITEM_MAP_NAMED = new XmlName(CONFIG, P_CONFIG, "config-item-map-named"); //No I18N
    XmlName N_CONFIG_ITEM = new XmlName(CONFIG, P_CONFIG, "config-item"); //No I18N
    //Attributes
    XmlName A_CONFIG_NAME = new XmlName(CONFIG, P_CONFIG, "name"); //No I18N
    XmlName A_CONFIG_TYPE = new XmlName(CONFIG, P_CONFIG, "type"); //No I18N
    
    
    //pivot
    XmlName A_APPLICATION_DATA = new XmlName(TABLE, P_TABLE, "application-data"); //No I18N          
    XmlName A_GRAND_TOTAL = new XmlName(TABLE, P_TABLE, "grand-total"); //No I18N                    
    XmlName A_IGNORE_EMPTY_ROWS = new XmlName(TABLE, P_TABLE, "ignore-empty-rows "); //No I18N        
    XmlName A_IDENTIFY_CATAGORIES = new XmlName(TABLE, P_TABLE, "identify-categories"); //No I18N    
    XmlName A_SHOW_FILTER_BUTTON = new XmlName(TABLE, P_TABLE, "show-filter-button"); //No I18N    
    XmlName A_THEME = new XmlName(TABLE, P_TABLE, "theme"); //No I18N    
    XmlName A_STYLEINFO_NAME = new XmlName(TABLE, P_TABLE, "styleInfo-name"); //No I18N
    XmlName A_SUB_TOTAL = new XmlName(TABLE, P_TABLE, "sub-total"); //No I18N
    XmlName A_REPEAT_LABELS = new XmlName(TABLE, P_TABLE, "repeat-labels"); //No I18N
    XmlName A_REFRESH = new XmlName(TABLE, P_TABLE, "refresh");    //No I18N
    XmlName A_EXPAND = new XmlName(TABLE, P_TABLE, "expand");    //No I18N
    XmlName A_HIDEERRORS = new XmlName(TABLE, P_TABLE, "hide-errors");    //No I18N
    XmlName A_FAULTY = new XmlName(TABLE, P_TABLE, "faulty");    //No I18N
    XmlName A_BUTTONS = new XmlName(TABLE, P_TABLE, "buttons"); //No I18N                           
    XmlName A_SOURCE_FIELD_NAME = new XmlName(TABLE, P_TABLE, "source-field-name"); //No I18N         
    XmlName A_ORIENTATION = new XmlName(TABLE, P_TABLE, "orientation"); //No I18N                   
    XmlName A_USED_HIERARCHY = new XmlName(TABLE, P_TABLE, "used-hierarchy"); //No I18N              
    XmlName A_FUNCTION = new XmlName(TABLE, P_TABLE, "function"); //No I18N                         
    XmlName A_IS_DATA_LAYOUT_FIELD = new XmlName(TABLE, P_TABLE, "is-data-layout-field"); //No I18N    
    XmlName A_FIELD_NAME = new XmlName(TABLE, P_TABLE, "field-name"); //No I18N                      
    XmlName A_MEMBER_TYPE = new XmlName(TABLE, P_TABLE, "member-type"); //No I18N                    
    XmlName A_TYPE = new XmlName(TABLE, P_TABLE, "type"); //No I18N                                 
    XmlName A_SHOW_EMPTY = new XmlName(TABLE, P_TABLE, "showEmpty"); //No I18N                      
    
    XmlName A_SHOW_DETAILS = new XmlName(TABLE, P_TABLE, "show-details"); //No I18N
    XmlName A_BUTTON_ENABLED = new XmlName(TABLE, P_TABLE, "button-enabled"); //No I18N
    XmlName A_ENABLED = new XmlName(TABLE, P_TABLE, "enabled"); //No I18N                           
    XmlName A_DISPLAY_MEMBER_MODE = new XmlName(TABLE, P_TABLE, "display-member-mode"); //No I18N     
    XmlName A_MEMBER_COUNT = new XmlName(TABLE, P_TABLE, "member-count"); //No I18N                  
    XmlName A_DATA_FIELD = new XmlName(TABLE, P_TABLE, "data-field"); //No I18N                      
    XmlName A_ORDER = new XmlName(TABLE, P_TABLE, "order"); //No I18N                               
    XmlName A_START = new XmlName(TABLE, P_TABLE, "start"); //No I18N                               
    XmlName A_END = new XmlName(TABLE, P_TABLE, "end"); //No I18N                               
    XmlName A_STEP = new XmlName(TABLE, P_TABLE, "step"); //No I18N
    XmlName A_DATE_START = new XmlName(TABLE, P_TABLE, "date-start"); //No I18N
    XmlName A_DATE_END = new XmlName(TABLE, P_TABLE, "date-end"); //No I18N
    XmlName A_CONTAINS_INTEGER = new XmlName(TABLE, P_TABLE, "contains-integer"); //No I18N
    XmlName A_CONTAINS_NUMBER = new XmlName(TABLE, P_TABLE, "contains-number"); //No I18N
    XmlName A_CONTAINS_STRING = new XmlName(TABLE, P_TABLE, "contains-string"); //No I18N
    XmlName A_CONTAINS_DATE = new XmlName(TABLE, P_TABLE, "contains-date"); //No I18N
    XmlName A_CONTAINS_BLANK = new XmlName(TABLE, P_TABLE, "contains-blank"); //No I18N
    XmlName A_CONTAINS_LONG_TEXT = new XmlName(TABLE, P_TABLE, "contains-long-text"); //No I18N
    XmlName A_CONTAINS_MIXED_TYPES = new XmlName(TABLE, P_TABLE, "contains-mixed-types"); //No I18N

    XmlName A_SORT_MODE = new XmlName(TABLE, P_TABLE, "sort-mode"); //No I18N
    XmlName A_DATA_FIELD_INDEX = new XmlName(TABLE, P_TABLE, "data-field-index"); //No I18N                        
    XmlName A_ADD_EMPTY_LINES = new XmlName(TABLE, P_TABLE, "add-empty-lines"); //No I18N             
    XmlName A_LAYOUT_MODE = new XmlName(TABLE, P_TABLE, "layout-mode"); //No I18N
    XmlName A_PIVOT_DISPLAY_NAME = new XmlName(TABLE, P_TABLE, "display-name"); //No I18N
    
     //Names for data validation
    XmlName NCONTENT_VALIDATIONS = new XmlName(TABLE, P_TABLE, "content-validations"); //No I18N// No I18N
    XmlName NCONTENT_VALIDATION = new XmlName(TABLE, P_TABLE, "content-validation"); //No I18N// No I18N
    XmlName ATABLE_CONDITION = new XmlName(TABLE, P_TABLE, "condition"); //No I18N// No I18N
    XmlName ATABLE_ALLOWEMPTYCELL = new XmlName(TABLE, P_TABLE, "allow-empty-cell"); //No I18N// No I18N
    XmlName ATABLE_DISPLAYLIST = new XmlName(TABLE, P_TABLE, "display-list"); //No I18N// No I18N
    XmlName NTABLE_HELPMESSAGE = new XmlName(TABLE, P_TABLE, "help-message"); //No I18N// No I18N
    XmlName ATABLE_TITLE = new XmlName(TABLE, P_TABLE, "title"); //No I18N// No I18N
    XmlName NTABLE_ERRORMESSAGE = new XmlName(TABLE, P_TABLE, "error-message"); //No I18N// No I18N
    XmlName ATABLE_MESSAGETYPE = new XmlName(TABLE, P_TABLE, "message-type"); //No I18N// No I18N
    XmlName ATABLE_CONTENTVALIDATIONNAME = new XmlName(TABLE, P_TABLE, "content-validation-name"); //No I18N// No I18N
    /*XmlName NTABLE_DETECTIVE=new XmlName(TABLE, pTable,"detective"); //No I18N// No I18N
    XmlName NTABLE_HIGHLIGHTEDRANGE=new XmlName(TABLE, pTable,"highlighted-range"); //No I18N// No I18N
    XmlName ATABLE_MARKEDINVALID=new XmlName(TABLE, pTable,"marked-invalid"); //No I18N// No I18N*/

    XmlName NPICKLISTS = new XmlName(TABLE,P_TABLE,"picklists"); // No I18N
    XmlName NPICKLIST = new XmlName(TABLE,P_TABLE,"picklist"); //No I18N
    XmlName A_SHOW_BUBBLE = new XmlName(TABLE, P_TABLE, "bubble"); //No I18N
    XmlName A_ALLOW_MULTI_SELECT = new XmlName(TABLE, P_TABLE, "multi-select"); //No I18N
    XmlName MULTI_VALUE = new XmlName(TEXT, P_TEXT, "multi-value"); //No I18N
    XmlName PICKLIST_MULTI_VALUE = new XmlName(TEXT, P_TEXT, "picklist-multi-value"); //No I18N
    XmlName VALUE = new XmlName(TEXT, P_TEXT, "value"); //No I18N
    XmlName PICKLIST_VALUE = new XmlName(TEXT, P_TEXT, "picklist-value"); //No I18N

    XmlName A_SHOW_DROPDOWN_ICON = new XmlName(TABLE, P_TABLE, "dropdown-icon"); //No I18N
    XmlName A_STACK_DIRECTION = new XmlName(TABLE, P_TABLE, "stack-dir"); //No I18N

    XmlName A_AUTO_COLORS = new XmlName(TABLE, P_TABLE, "auto-colors"); //No I18N
    XmlName NITEM_PICKLIST = new XmlName(TABLE,P_TABLE,"picklist-items"); //No I18N
    XmlName NRANGE_PICKLIST = new XmlName(TABLE,P_TABLE,"picklist-range"); //No I18N
    XmlName NPICKLIST_AUTO_COLORS = new XmlName(TABLE,P_TABLE,"picklist-auto-colors"); //No I18N
    XmlName NPICKLIST_AUTO_COLOR = new XmlName(TABLE,P_TABLE,"picklist-auto-color"); //No I18N
    XmlName NPICKLISTITEM = new XmlName(TABLE,P_TABLE,"listitem"); //No I18N
    XmlName A_PICKLIST_VALUE = new XmlName(TABLE,P_TABLE,"picklistValue"); //No I18N
    XmlName A_PICKLIST_ID = new XmlName(TABLE,P_TABLE,"picklist-id"); //No I18N
    XmlName A_ITEM_ID = new XmlName(TABLE,P_TABLE,"item-id"); //No I18N
    XmlName A_STYLE_ID = new XmlName(TABLE,P_TABLE,"style-id"); //No I18N
    XmlName A_PICKLIST_SOURCE_ID = new XmlName(TABLE, P_TABLE, "picklist-source"); //No I18N
    XmlName A_PICKLIST_SORT = new XmlName(TABLE, P_TABLE, "sort"); //No I18N
    XmlName A_DISPLAY_STRING = new XmlName(TABLE,P_TABLE,"displayString"); // No I18N
    XmlName A_VALUE = new XmlName(TABLE,P_TABLE,"value"); // No I18N
    XmlName A_BG_THEME_COLOR = new XmlName(FO, P_FO, "bg-theme-color"); //No I18N
    XmlName A_BG_COLOR_TINT = new XmlName(FO, P_FO, "bg-color-tint"); //No I18N
    XmlName A_FONT_COLOR_HEX = new XmlName(FO, P_FO, "font-color-hex"); //No I18N
    XmlName A_FONT_COLOR_TINT = new XmlName(FO, P_FO, "font-color-tint"); //No I18N
    XmlName A_FONT_COLOR_THEME = new XmlName(FO, P_FO, "font-color-theme"); //No I18N
    XmlName A_IGNORE_TYPE_ERROR = new XmlName(TABLE,P_TABLE,"ignore-type-error"); // No I18N

    XmlName A_VALUE_ID = new XmlName(TABLE, P_TABLE, "value-id"); //No I18N
    //START BHAWANI
    // for draw:frame and draw:image 
    
    XmlName N_DRAW_FRAME = new XmlName(DRAW, P_DRAW, "frame"); //No I18N

    
    XmlName N_DRAW_IMAGE = new XmlName(DRAW, P_DRAW, "image"); //No I18N
    XmlName A_ACTUATE = new XmlName(XLINK, P_XLINK, "actuate"); //No I18N
    XmlName A_SHOW = new XmlName(XLINK, P_XLINK, "show"); //No I18N
    XmlName A_IMAGE_TYPE = new XmlName(XLINK, P_XLINK, "type"); //No I18N
    
    XmlName N_SVG_TITLE = new XmlName(SVG, P_SVG, "title"); //No I18N
    XmlName N_SVG_DESC = new XmlName(SVG, P_SVG, "desc"); //No I18N
    

    XmlName N_DRAW_G = new XmlName(DRAW, P_DRAW, "g"); //No I18N
    //END BHAWANI


    //ZS TABLE STYLE
    XmlName A_KEY = new XmlName(STYLE, P_STYLE, "key"); //No I18N
    XmlName A_STRIPE_SIZE = new XmlName(STYLE, P_STYLE, "stripeSize"); //No I18N
    XmlName A_CELL_STYLE_NAME = new XmlName(STYLE, P_STYLE, "cellStyleName"); //No I18N

    XmlName DEFAULT_COLUMN_WIDTH = new XmlName(OFFICE, P_OFFICE, "defaultColumnWidth"); //No I18N
    XmlName MIN_ROW_HEIGHT = new XmlName(OFFICE, P_OFFICE, "minRowHeight"); //No I18N
    XmlName A_STARTS_WITH = new XmlName(TABLE,P_TABLE,"startsWith"); // No I18N
    XmlName A_STARTS_WITH_ID = new XmlName(TABLE,P_TABLE,"startsWith-id"); //No I18N
    XmlName A_RANGE = new XmlName(TABLE,P_TABLE,"range"); // No I18N
    XmlName A_SOURCE_RANGE = new XmlName(TABLE,P_TABLE,"source-range"); // No I18N
    XmlName A_IS_RANGE_PICKLIST = new XmlName(TABLE,P_TABLE,"is-range-picklist"); // No I18N


    // SHEET IMAGES - START

    XmlName A_IMAGES = new XmlName(CALCEXT, P_CALCEXT, "images"); // No I18N
    XmlName A_IMAGE = new XmlName(CALCEXT, P_CALCEXT, "image"); // No I18N

    XmlName A_SHEET_IMAGE_ID = new XmlName(CALCEXT, P_CALCEXT, "id"); // No I18N
    XmlName A_IMAGE_ID = new XmlName(CALCEXT, P_CALCEXT, "image-id"); // No I18N
    XmlName A_IMAGE_DISPLAY_MODE = new XmlName(CALCEXT, P_CALCEXT, "display-mode"); // No I18N
    XmlName A_IMAGE_HEIGHT = new XmlName(CALCEXT, P_CALCEXT, "height"); // No I18N
    XmlName A_IMAGE_WIDTH = new XmlName(CALCEXT, P_CALCEXT, "width"); // No I18N
    XmlName A_IMAGE_ROW_INDEX = new XmlName(CALCEXT, P_CALCEXT, "row-index"); // No I18N
    XmlName A_IMAGE_COLUMN_INDEX = new XmlName(CALCEXT, P_CALCEXT, "column-index"); // No I18N
    XmlName A_IMAGE_ROW_DIFF = new XmlName(CALCEXT, P_CALCEXT, "row-diff"); // No I18N
    XmlName A_IMAGE_COLUMN_DIFF = new XmlName(CALCEXT, P_CALCEXT, "column-diff"); // No I18N

    // SHEET IMAGES - END


    //Workbook Fields
    XmlName N_OFFICE_FIELDS = new XmlName(OFFICE, P_OFFICE, "fields"); // No I18N
    XmlName N_OFFICE_FIELD = new XmlName(OFFICE, P_OFFICE, "field"); // No I18N
    XmlName A_FIELDID = new XmlName(TEXT, P_TEXT, "id"); // No I18N
    XmlName A_ICON_NAME = new XmlName(TEXT, P_TEXT, "icon-name"); // No I18N
    XmlName A_FIELDNAME = new XmlName(TEXT, P_TEXT, "name"); // No I18N
    
    XmlName N_DRAW_CUSTOM_SHAPE = new XmlName(DRAW, P_DRAW, "custom-shape"); //No I18N
    XmlName N_DRAW_ENHANCED_GEOMETRY = new XmlName(DRAW, P_DRAW, "enhanced-geometry"); //No I18N
    XmlName N_DRAW_EQUATION = new XmlName(DRAW, P_DRAW, "equation"); //No I18N
    XmlName N_DRAW_HANDLE = new XmlName(DRAW, P_DRAW, "handle"); //No I18N
    XmlName N_DRAW_LINE = new XmlName(DRAW, P_DRAW, "line"); //No I18N
    XmlName N_DRAW_PATH = new XmlName(DRAW, P_DRAW, "path"); //No I18N
    XmlName N_DRAW_POLY_LINE = new XmlName(DRAW, P_DRAW, "polyline"); //No I18N
    XmlName N_DRAW_ELLIPSE = new XmlName(DRAW, P_DRAW, "ellipse");   //No I18N

    XmlName A_DRAW_VIEW_BOX = new XmlName(SVG, P_SVG, "viewBox"); //No I18N
    XmlName A_DRAW_TEXT_AREAS = new XmlName(DRAW, P_DRAW, "text-areas"); //No I18N
    XmlName A_DRAW_TYPE = new XmlName(DRAW, P_DRAW, "type"); //No I18N
    XmlName A_DRAW_MODIFIERS = new XmlName(DRAW, P_DRAW, "modifiers"); //No I18N
    XmlName A_DRAW_ENHANCED_PATH = new XmlName(DRAW, P_DRAW, "enhanced-path"); //No I18N
    XmlName A_DRAW_GLUE_POINTS = new XmlName(DRAW, P_DRAW, "glue-points"); //No I18N
    XmlName A_DRAW_PATH_STRETCHPOINT_X = new XmlName(DRAW, P_DRAW, "path-stretchpoint-x"); //No I18N
    XmlName A_DRAW_PATH_STRETCHPOINT_Y = new XmlName(DRAW, P_DRAW, "path-stretchpoint-y"); //No I18N
    XmlName A_DRAW_MIRROR_HORIZONTAL = new XmlName(DRAW, P_DRAW, "mirror-horizontal"); //No I18N
    XmlName A_DRAW_MIRROR_VERTICAL = new XmlName(DRAW, P_DRAW, "mirror-vertical"); //No I18N
    XmlName A_DRAW_FORMULA = new XmlName(DRAW, P_DRAW, "formula"); //No I18N
    XmlName A_DRAW_TEXT_STYLE_NAME = new XmlName(DRAW, P_DRAW, "text-style-name"); //No I18N
    
    XmlName A_DRAW_HANDLE_POSITION = new XmlName(DRAW, P_DRAW, "handle-position"); //No I18N
    XmlName A_DRAW_HANDLE_RANGE_X_MINIMUM = new XmlName(DRAW, P_DRAW, "handle-range-x-minimum");//No I18N
    XmlName A_DRAW_HANDLE_RANGE_X_MAXIMUM = new XmlName(DRAW, P_DRAW, "handle-range-x-maximum"); //No I18N
    XmlName A_DRAW_HANDLE_RANGE_Y_MINIMUM = new XmlName(DRAW, P_DRAW, "handle-range-y-minimum"); //No I18N
    XmlName A_DRAW_HANDLE_RANGE_Y_MAXIMUM = new XmlName(DRAW, P_DRAW, "handle-range-y-maximum"); //No I18N
    XmlName A_DRAW_HANDLE_RADIUS_RANGE_MAXIMUM = new XmlName(DRAW, P_DRAW, "handle-radius-range-maximum"); //No I18N
    XmlName A_DRAW_HANDLE_RADIUS_RANGE_MINIMUM = new XmlName(DRAW, P_DRAW, "handle-radius-range-minimum"); //No I18N
    XmlName A_DRAW_HANDLE_SWITCHED = new XmlName(DRAW, P_DRAW, "handle-switched"); //No I18N
    XmlName A_DRAW_HANDLE_POLAR = new XmlName(DRAW, P_DRAW, "handle-polar"); //No I18N
    XmlName A_DRAW_HANDLE_MIRROR_HORIZONTAL = new XmlName(DRAW, P_DRAW, "handle-mirror-horizontal"); //No I18N
    XmlName A_DRAW_HANDLE_MIRROR_VERTICAL = new XmlName(DRAW, P_DRAW, "handle-mirror-vertical"); //No I18N
    
    XmlName A_DRAW_POINTS = new XmlName(DRAW, P_DRAW, "points"); //No I18N
    XmlName A_SVG_D = new XmlName(SVG, P_SVG, "d"); //No I18N
    XmlName A_DRAW_TRANSFORM = new XmlName(DRAW, P_DRAW, "transform"); //No I18N
    XmlName A_SVG_X_1 =  new XmlName(SVG, P_SVG, "x1"); //No I18N
    XmlName A_SVG_X_2 =  new XmlName(SVG, P_SVG, "x2"); //No I18N
    XmlName A_SVG_Y_1 =  new XmlName(SVG, P_SVG, "y1"); //No I18N
    XmlName A_SVG_Y_2 =  new XmlName(SVG, P_SVG, "y2"); //No I18N
    XmlName A_DRAW_KIND = new XmlName(DRAW, P_DRAW, "kind"); //No I18N
    XmlName A_DRAW_START_ANGLE = new XmlName(DRAW, P_DRAW, "start-angle"); //No I18N
    XmlName A_DRAW_END_ANGLE = new XmlName(DRAW, P_DRAW, "end-angle");     //No I18N
    XmlName A_TYPE_ANCHOR = new XmlName(XLINK, P_XLINK, "type"); //No I18N

    /** Grouping tags */
    XmlName N_ROWGROUP = new XmlName(OFFICE, P_OFFICE, "rowGroup"); //No I18N
    XmlName N_COLGROUP = new XmlName(OFFICE, P_OFFICE, "colGroup"); //No I18N
    XmlName N_GROUPS = new XmlName(OFFICE, P_OFFICE, "groups"); //No I18N
    XmlName A_GROUPID = new XmlName(OFFICE, P_OFFICE, "id"); //No I18N
    XmlName A_STARTINDEX = new XmlName(OFFICE, P_OFFICE, "startIndex"); //No I18N
    XmlName A_ENDINDEX = new XmlName(OFFICE, P_OFFICE, "endIndex"); //No I18N
    XmlName A_LEVEL = new XmlName(OFFICE, P_OFFICE, "level"); //No I18N
    XmlName A_ISCOLLAPSE = new XmlName(OFFICE, P_OFFICE, "isCollapse"); //No I18N

   XmlName A_MAX_WIDTH_ROW_INDEX = new XmlName(TABLE, P_TABLE, "row-index"); // No I18N
   XmlName A_MAX_HEIGHT_COL_INDEX = new XmlName(TABLE, P_TABLE, "col-index"); // No I18N
    /** SLICER tags */
    String SLICER = "urn:com:zoho:sheet:xmlns:slicer:1.0"; //No I18N
    String P_SLICER = "slicer"; //No I18N
    XmlName S_SLICERS = new XmlName(SLICER,P_SLICER,"slicers"); //No I18N
    XmlName S_SLICER = new XmlName(SLICER,P_SLICER,"slicer"); //No I18N
    XmlName S_SLICER_NAME = new XmlName(SLICER,P_SLICER,"name"); //No I18N
    XmlName S_SLICER_CONNECTION_NAME = new XmlName(SLICER,P_SLICER,"connection-name"); //No I18N
    XmlName S_PERIOD_TYPE = new XmlName(SLICER,P_SLICER,"periodType"); //No I18N
    XmlName S_SLICER_STYLES = new XmlName(SLICER,P_SLICER,"styles"); //No I18N
    XmlName S_SLICER_STYLE = new XmlName(SLICER,P_SLICER,"style");  //No I18N
    XmlName S_HEADER_STYLE = new XmlName(SLICER,P_SLICER,"headerStyle"); //No I18N
    XmlName S_ITEM_ON_STYLE = new XmlName(SLICER,P_SLICER,"itemOnStyle"); //No I18N
    XmlName S_ITEM_OFF_STYLE = new XmlName(SLICER,P_SLICER,"ItemOffStyle"); //No I18N
//    XmlName S_IS_HEADER_VISIBLE = new XmlName(SLICER,P_SLICER,"isHeaderVisible"); //No I18N
    XmlName S_IS_MULTI_SELECT = new XmlName(SLICER,P_SLICER,"isMultiSelect"); //No I18N
    XmlName S_COLUMNS = new XmlName(SLICER,P_SLICER,"columns"); //No I18N
    XmlName S_HEIGHT = new XmlName(SLICER,P_SLICER,"height"); //No I18N
    XmlName S_WIDTH = new XmlName(SLICER,P_SLICER,"width"); //No I18N
    XmlName  S_POSITION_ROW_INDEX = new XmlName(SLICER,P_SLICER,"rowIndex"); //No I18N
    XmlName  S_POSITION_COLUMN_INDEX = new XmlName(SLICER,P_SLICER,"columnIndex"); //No I18N
    XmlName S_POSITION_ROW_DIFF = new XmlName(SLICER,P_SLICER,"rowDiff"); //No I18N
    XmlName  S_POSITION_COLUMN_DIFF = new XmlName(SLICER,P_SLICER,"columnDiff"); //No I18N

 /** SLICER connection tags */
    String CONNECTION = "urn:com:zoho:sheet:xmlns:connection:1.0"; //No I18N
    String P_CONNECTION = "connection"; //No I18N
    XmlName S_SLICER_PIVOT_CONNECTIONS = new XmlName(SLICER,P_SLICER,"pivot-connections"); //No I18N
    XmlName S_SLICER_TABLE_CONNECTIONS = new XmlName(SLICER,P_SLICER,"table-connections"); //No I18N
    XmlName S_SLICER_CONNECTION = new XmlName(CONNECTION,P_CONNECTION,"connection"); //No I18N
    XmlName S_CONNECTION_NAME  = new XmlName(CONNECTION,P_CONNECTION,"name"); //No I18N
    XmlName S_CONNECTION_FIELD_NAME = new XmlName(CONNECTION,P_CONNECTION,"fieldName"); //No I18N
    XmlName S_CONNECTION_TABLE_NAME  = new XmlName(CONNECTION,P_CONNECTION,"tableName"); //No I18N

    /** addtional tables/pivot fot slicer connection */
    String ANNEX = "urn:com:zoho:sheet:xmlns:annex:1.0"; //No I18N
    String P_ANNEX = "annex"; //No I18N
    XmlName S_CONNECTION_ANNEX = new XmlName(CONNECTION,P_CONNECTION,"annex"); //No I18N
    XmlName A_ANNEX_TABLE_NAME = new XmlName(ANNEX,P_ANNEX,"tableName"); //No I18N
    XmlName S_SLICER_ID = new XmlName(SLICER,P_SLICER,"slicer-id"); //No I18N
    XmlName A_SLICER_BUTTON_HEIGHT = new XmlName(TABLE,P_SLICER,"button-height");  //NO I18N
//    XmlName A_SLICER_TRANSPARENCY = new XmlName(TABLE,P_SLICER,"slicer-transparency");  //NO I18N
    XmlName A_SLICER_SORT_ORDER = new XmlName(TABLE,P_SLICER,"sort-order");  //NO I18N
    XmlName A_SLICER_SCROLL_POSITION = new XmlName(TABLE,P_SLICER,"scroll-position");  //NO I18N
    XmlName A_SLICER_LAYOUT = new XmlName(TABLE,P_SLICER,"slicer-layout"); //NO I18N

   /** TIMELINE tags */
    XmlName S_TIMELINE_SLICER = new XmlName(SLICER,P_SLICER,"timeLineSlicer"); //No I18N
    XmlName S_SLIDER_STYLE = new XmlName(SLICER,P_SLICER,"sliderStyle"); //No I18N
    XmlName S_TIME_LEVEL_STYLE = new XmlName(SLICER,P_SLICER,"timeLevelStyle"); //No I18N
    XmlName S_SLIDER_LABEL_STYLE = new XmlName(SLICER,P_SLICER,"sliderLabelStyle"); //No I18N
    XmlName S_PERIOD_LABEL1_STYLE = new XmlName(SLICER,P_SLICER,"periodLabel1Style"); //No I18N
    XmlName S_PERIOD_LABEL2_STYLE = new XmlName(SLICER,P_SLICER,"periodLabel2Style"); //No I18N
    XmlName S_ITEM_ON_SPACE_STYLE = new XmlName(SLICER,P_SLICER,"itemOnSpaceStyle"); //No I18N
    XmlName S_ITEM_OFF_SPACE_STYLE = new XmlName(SLICER,P_SLICER,"itemOffSpaceStyle"); //No I18N

    String ZS_STYLE = "urn:com:zoho:sheet:xmlns:style:1.0"; //No I18N
    //Fill
    String P_ZS_FILL = "zsfill";//No I18N
    XmlName F_FILLS = new XmlName(ZS_STYLE, P_ZS_FILL ,"zsFills"); //No I18N
    XmlName F_NAME = new XmlName(ZS_STYLE, P_ZS_FILL,"name"); //No I18N
    XmlName F_DEGREE = new XmlName(ZS_STYLE, P_ZS_FILL,"degree"); //No I18N
    XmlName F_LEFT = new XmlName(ZS_STYLE, P_ZS_FILL,"left"); //No I18N
    XmlName F_RIGHT = new XmlName(ZS_STYLE, P_ZS_FILL,"right"); //No I18N
    XmlName F_TOP = new XmlName(ZS_STYLE, P_ZS_FILL,"top"); //No I18N
    XmlName F_BOTTOM = new XmlName(ZS_STYLE, P_ZS_FILL,"bottom"); //No I18N
    XmlName F_PATTERN_TYPE = new XmlName(ZS_STYLE, P_ZS_FILL,"patternType"); //No I18N
    XmlName F_GRADIENT_TYPE = new XmlName(ZS_STYLE, P_ZS_FILL,"gradientType"); //No I18N
    XmlName F_GRADIENT_COLOR = new XmlName(ZS_STYLE, P_ZS_FILL,"gradientColor"); //No I18N
    XmlName F_STOP_HEX_COLOR = new XmlName(ZS_STYLE, P_ZS_FILL,"stop-hex-color"); //No I18N
    XmlName F_STOP_THEME_COLOR = new XmlName(ZS_STYLE, P_ZS_FILL,"stop-theme-color"); //No I18N
    XmlName F_STOP_COLOR_TINT = new XmlName(ZS_STYLE, P_ZS_FILL,"stop-color-tint"); //No I18N
    XmlName F_START_HEX_COLOR = new XmlName(ZS_STYLE, P_ZS_FILL,"start-hex-color"); //No I18N
    XmlName F_START_THEME_COLOR = new XmlName(ZS_STYLE, P_ZS_FILL,"start-theme-color"); //No I18N
    XmlName F_START_COLOR_TINT = new XmlName(ZS_STYLE, P_ZS_FILL,"start-color-tint"); //No I18N
    XmlName F_FG_HEX_COLOR = new XmlName(ZS_STYLE, P_ZS_FILL,"fg-hex-color"); //No I18N
    XmlName F_FG_THEME_COLOR = new XmlName(ZS_STYLE, P_ZS_FILL,"fg-theme-color"); //No I18N
    XmlName F_FG_COLOR_TINT = new XmlName(ZS_STYLE, P_ZS_FILL,"fg-color-tint"); //No I18N
    XmlName F_BG_HEX_COLOR = new XmlName(ZS_STYLE, P_ZS_FILL,"bg-hex-color"); //No I18N
    XmlName F_BG_THEME_COLOR = new XmlName(ZS_STYLE, P_ZS_FILL,"bg-theme-color"); //No I18N
    XmlName F_BG_COLOR_TINT = new XmlName(ZS_STYLE, P_ZS_FILL,"bg-color-tint"); //No I18N
    XmlName F_PATTERN = new XmlName(ZS_STYLE, P_ZS_FILL,"pattern"); //No I18N
    XmlName F_GRADIENT = new XmlName(ZS_STYLE, P_ZS_FILL,"gradient"); //No I18N
    XmlName F_START = new XmlName(ZS_STYLE, P_ZS_FILL,"start"); //No I18N
    XmlName F_STOP = new XmlName(ZS_STYLE, P_ZS_FILL,"stop"); //No I18N

 // shadow
 String P_ZS_SHADOW = "shadow";//No I18N
 XmlName SHADOW = new XmlName(ZS_STYLE, P_ZS_SHADOW,"shadow"); //No I18N
 XmlName SHADOW_NAME = new XmlName(ZS_STYLE, P_ZS_SHADOW,"name"); //No I18N
 XmlName SHADOW_TRANSPARENCY = new XmlName(ZS_STYLE, P_ZS_SHADOW,"transparency");  //NO I18N
 XmlName SHADOW_SPREAD = new XmlName(ZS_STYLE, P_ZS_SHADOW,"spread"); //No I18N
 XmlName SHADOW_HEX_COLOR = new XmlName(ZS_STYLE, P_ZS_SHADOW,"hex-color");  //NO I18N
 XmlName SHADOW_THEME_COLOR = new XmlName(ZS_STYLE, P_ZS_SHADOW,"theme-color");  //NO I18N
 XmlName SHADOW_COLOR_TINT = new XmlName(ZS_STYLE, P_ZS_SHADOW,"color-tint");  //NO I18N
 XmlName SHADOW_ANGLE = new XmlName(ZS_STYLE, P_ZS_SHADOW,"angle"); //No I18N
 XmlName SHADOW_BLUR = new XmlName(ZS_STYLE, P_ZS_SHADOW,"blur"); //No I18N
 XmlName SHADOW_DISTANCE = new XmlName(ZS_STYLE, P_ZS_SHADOW,"distance"); //No I18N
 XmlName SHADOW_INNER = new XmlName(ZS_STYLE, P_ZS_SHADOW,"inner"); //No I18N

    //names for notification Settings
    XmlName ATABLE_NOTIFY_CATEGORY = new XmlName(TABLE, P_TABLE, "category"); //No I18N
    XmlName ATABLE_NOTIFY_RANGES = new XmlName(TABLE, P_TABLE, "range"); //No I18N
    XmlName ATABLE_NOTIFY_VALUE = new XmlName(TABLE, P_TABLE, "value"); //No I18N
}


