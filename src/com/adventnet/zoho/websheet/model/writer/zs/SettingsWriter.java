// $Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.writer.zs;

import com.adventnet.zoho.websheet.model.ConfigItem;
import com.adventnet.zoho.websheet.model.ConfigItemMap;
import com.adventnet.zoho.websheet.model.WorkbookSettings;
import com.adventnet.zoho.websheet.model.zsparser.ZSNameSpace;
import java.io.IOException;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 *
 * <AUTHOR>
 */
public class SettingsWriter
{
    public static Logger logger = Logger.getLogger(SettingsWriter.class.getName());
    /////////////
    public static void writeSettings(ZipOutputStream zos, WorkbookSettings wbSettings) throws Exception
    {
        try
        {
            String fileName = "settings.xml"; //No I18N
            zos.putNextEntry(new ZipEntry(fileName));
            //writeSettings(zos,wbSettings);
            StringBuilder buff = new StringBuilder();
            XMLWriter w = new XMLWriter(zos);
            w.writeXMLDeclaration();

            buff.append("<office:document-settings "); //No I18N
            buff.append((new ZSNameSpace()).getSettingsNS());
            buff.append("office:version=\"1.2\">"); //No I18N
            buff.append("<office:settings>"); //No I18N
            String[] attrNames = new String[]{"config:name"}; //No I18N
            String[] attrValues = new String[]{wbSettings.getConfigItemSetName()};
            String str = w.createStartTagOpen("config:config-item-set", attrNames, attrValues, false); //No I18N
            buff.append(str);
            str = getConfigItemMapIndexed(wbSettings);
            buff.append(str);
            buff.append("</config:config-item-set>"); //No I18N
            buff.append("</office:settings>"); //No I18N
            buff.append("</office:document-settings>"); //No I18N
            zos.write(buff.toString().getBytes());
        }
        catch(Exception ex)
	{
            logger.log(Level.WARNING, "Engine: Exception while writing Settings.", ex);
	    throw ex;
	}
    }

    public static String getConfigItemMapIndexed(WorkbookSettings wbSettings) throws IOException
    {
        StringBuilder buff = new StringBuilder();
        String[] attrNames = new String[]{"config:name"}; //No I18N
        String[] attrValues = new String[]{wbSettings.getConfigMapIndexedName()};// views
        String str = XMLWriter.createStartTagOpen("config:config-item-map-indexed", attrNames, attrValues, false); //views //No I18N
        buff.append(str);        
        List<ConfigItemMap> configItemMapList = wbSettings.getConfigItemMapList();        
        for(ConfigItemMap configItemMap : configItemMapList)
        {
            str = getConfigItemMapEntry(configItemMap);
            buff.append(str);
            if(configItemMap.getConfigName() == null)
            {
                attrValues = new String[]{wbSettings.getConfigMapName()};//Tables
                str = XMLWriter.createStartTagOpen("config:config-item-map-named", attrNames, attrValues, false); //No I18N
                buff.append(str);               
            }            
        }        
        buff.append("</config:config-item-map-named>"); //No I18N
        buff.append("</config:config-item-map-entry>"); // closing of null map entry here for configName = null //No I18N
        buff.append("</config:config-item-map-indexed>"); //No I18N
        return buff.toString();        
    }
    private static String getConfigItemMapEntry(ConfigItemMap configItemMap) throws IOException
    {
        StringBuilder buff = new StringBuilder();
        String[] attrNames = new String[]{"config:name"}; //No I18N
        String configName = configItemMap.getConfigName();
        String[] attrValues = new String[]{configName};
        String str = XMLWriter.createStartTagOpen("config:config-item-map-entry", attrNames, attrValues, false);//sheet1,sheet2 //No I18N
        buff.append(str);
        List<ConfigItem> configItemList = configItemMap.getConfigItemList();
        for(ConfigItem configItem : configItemList)
        {
            str = XMLWriter.createStartTagOpen("config:config-item", configItem.getAttributes(), configItem.getValues(), false); //No I18N
            buff.append(str);
            buff.append(XMLWriter.xmlEncode(configItem.getValue()));
            buff.append("</config:config-item>"); //No I18N
        }
        if(configName != null)
        {
            buff.append("</config:config-item-map-entry>"); //No I18N
        }
        return buff.toString();        
    }
}
