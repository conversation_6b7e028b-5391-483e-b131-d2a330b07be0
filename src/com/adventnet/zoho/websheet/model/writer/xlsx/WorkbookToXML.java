/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.ext.parser.ASTParseErrorNode;
import com.adventnet.zoho.websheet.model.filter.operator.FilterOperator;
import com.adventnet.zoho.websheet.model.filter.util.FilterUtil;
import com.adventnet.zoho.websheet.model.pivot.*;
import com.adventnet.zoho.websheet.model.style.SheetStyle;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EngineUtils1;
import com.adventnet.zoho.websheet.model.writer.xlsx.image.DrawingProperties;
import com.adventnet.zoho.websheet.model.writer.xlsx.image.XlsxWriterImageContainer;
import com.adventnet.zoho.websheet.model.writer.xlsx.pivotTable.PivotCacheDefinitionToXML;
import com.adventnet.zoho.websheet.model.writer.xlsx.pivotTable.PivotTableToXML;
import com.adventnet.zoho.websheet.model.xlsxaparser_.AttributeNameConstants;
import com.adventnet.zoho.websheet.model.xlsxaparser_.ElementNameConstants;
import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXSlicerStyleContainer;
import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXTimelineStyleContainer;
import com.singularsys.jep.parser.ASTConstant;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class WorkbookToXML {
    public static final Logger LOGGER = Logger.getLogger(WorkbookToXML.class.getName());
    public static final String XML_VERSION_ENCODING = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>";//No I18N
    public static final String XMLNS_URL ="http://schemas.openxmlformats.org/spreadsheetml/2006/main";//No I18N
    public static final String XMLNS_REL_URL = "http://schemas.openxmlformats.org/officeDocument/2006/relationships";//No I18N

    private final ZipOutputStream zipOutputStream;
    private final Workbook workbook;

    private final XlsxWriterImageContainer xlsxWriterImageContainer;
    private final XlsxConversionStats xLSX_CONVERSION_STATS;
    private final Set<String> sheetsToBeParsed;
    private final List<CellStyle> slicerCellStyles = new ArrayList<>();
    List<String> slicerCustomStyles = new ArrayList<>();
    private final Map<String, XLSXSlicerStyleContainer> slicerStyleMap = new HashMap<>();
    private final Map<String, XLSXTimelineStyleContainer> timelineStyleMap = new HashMap<>();

    public WorkbookToXML(ZipOutputStream zipOutputStream, Workbook workbook, XlsxWriterImageContainer xlsxWriterImageContainer, XlsxConversionStats xlsxConversionStats, Set<String> sheetsToBeParsed) {
        this.zipOutputStream = zipOutputStream;
        this.workbook = workbook;
        this.xlsxWriterImageContainer = xlsxWriterImageContainer;
        this.xLSX_CONVERSION_STATS = xlsxConversionStats;
        this.sheetsToBeParsed = sheetsToBeParsed;
    }

    public void write() throws Exception {
        if (!workbook.isDependenciesUpdated()) {
            workbook.updateCellDependencies();
        }
        //xl/_rels/workbook.xml.rels
        List<XmlElementWriter> workbookXmlRelsRelationshipElements = new ArrayList<>();

        //xl/workbook.xml
        Map<String, Integer> sheetASN_OrderMap = new HashMap<>();
        List<XmlElementWriter> workbookXmlSheetElements = new ArrayList<>();
        List<XmlElementWriter> workbookXmlPivotCacheElements = new ArrayList<>();
        List<XmlElementWriter> workbookXmlSlicerCacheElements = new ArrayList<>();
        List<XmlElementWriter> workbookXmlTimelineCacheElements = new ArrayList<>();

        //[Content_Types].xml
        List<XmlElementWriter> contentTypesOverrideElements = new ArrayList<>();
        final Set<String> imageExtensionSet = new HashSet<>();

        List<PivotTable> pivotTables = new ArrayList<>();
        if(this.workbook.getPivotTables() != null) {
            for (PivotTable pt : this.workbook.getPivotTables()) {
                try {
                    if(pt == null) {
                        LOGGER.log(Level.OFF, "pivottable null");
                        continue;
                    }
                    if(pt.getTargetCellRange() == null) {
                        LOGGER.log(Level.OFF, "pivottable TargetCellRange null");
                        continue;
                    }
                    if(pt.getSourceCellRange() == null && pt.getSourceName() == null) {
                        LOGGER.log(Level.OFF, "pivottable SourceCellRange null");
                        continue;
                    }
                    if(this.sheetsToBeParsed.contains(pt.getTargetCellRange().getAssociatedSheetName()) && this.sheetsToBeParsed.contains(pt.getSourceCellRange(workbook).getAssociatedSheetName())) {
                        pivotTables.add(pt);
                    }
                } catch(Exception e) {
                    LOGGER.log(Level.OFF, "pivot-table export failed", e);
                }
            }
        }

        Map<DataRange, Integer> pivotCacheDefinitionXmlIdOfPivotCellRange = new HashMap<>();
        this.xlsxWriterImageContainer.initDrawingPropertiesOfSheetImages();

        RelationshipHelper relationshipHelper = new RelationshipHelper();
        int sheetXmlId = 1;
        int pivotTableXmlId = 1;
        int commentVmlDrawingShapeId = 1;
        int chartXmlId = 1;
        int slicerCacheXmlId = 1;
        int timelineCacheXmlId = 1;

        Map<DataRange, List<PivotField>> pivotCacheFieldsOfPivotCellRange = getCacheFields(pivotTables);
        Map<DataRange, Map<String, String>> dataRangeToChangedFieldNamesMap = new HashMap<>();
        final StylesBuilder.Font defaultFont = Utility.getDefaultFont(workbook.getCellStyle("Default"), workbook.getTheme());//No I18N
        List<SlicerConnection> pivotSlicerConnections = getUniqueConnections(new ArrayList<>(workbook.getPivotSlicers()));
        List<SlicerConnection> timelineConnections = getUniqueConnections(new ArrayList<>(workbook.getTimelines()));
        SheetToXml sheetToXml = new SheetToXml(this.zipOutputStream, contentTypesOverrideElements, this.xlsxWriterImageContainer, xLSX_CONVERSION_STATS, workbook.getTheme(), defaultFont, StylesBuilder.getCellAlignment(workbook.getCellStyle("Default"), false, null),workbook);//No I18N
        for(Sheet sheet: this.workbook.getSheetList()) {
            if(!this.sheetsToBeParsed.contains(sheet.getAssociatedName())) {
                continue;
            }
            String sheetXmlName = "sheet"+ (sheetXmlId);//No I18N
            {
                int workbookXmlRelsRelationshipId = workbookXmlRelsRelationshipElements.size() + 1;
                workbookXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                        .addAttribute(AttributeNameConstants.TARGET, "worksheets/" + sheetXmlName + ".xml")
                        .addAttribute(AttributeNameConstants.REL_ID, "rId" + workbookXmlRelsRelationshipId)
                        .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet"));
                contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                        .addAttribute(AttributeNameConstants.PARTNAME, "/xl/worksheets/" + sheetXmlName + ".xml")
                        .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"));
                XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance()
                        .addAttribute(AttributeNameConstants.NAME, Utility.xmlEncode(sheet.getName()))
                        .addAttribute(AttributeNameConstants.R_ID, "rId" + workbookXmlRelsRelationshipId)
                        .addAttribute(AttributeNameConstants.SHEETID, String.valueOf(sheetXmlId++));
                if (sheet.getSheetStyle()!= null && "false".equals(sheet.getSheetStyle().getProperty(SheetStyle.Property.DISPLAY))) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.STATE, "hidden");//No I18N
                }
                workbookXmlSheetElements.add(xmlElementWriter);
                int size_sheetASN_OrderMap = sheetASN_OrderMap.size();
                sheetASN_OrderMap.put(sheet.getAssociatedName(), size_sheetASN_OrderMap);
            }
            List<XmlElementWriter> sheetXmlRelsRelationshipElements = new ArrayList<>();
            this.zipOutputStream.putNextEntry(new ZipEntry("xl/worksheets/" + sheetXmlName+".xml")); //No I18N
            sheetToXml.write_sheet(sheet, sheetXmlRelsRelationshipElements, relationshipHelper);
            if (sheet.getTables(false) != null)
            {
                Collection<Table> values = sheet.getTables(false).values();
                if(! values.isEmpty()) {
                    this.xLSX_CONVERSION_STATS.addStats(Table.class.getSimpleName());
                    for(Table table : values) {
                        this.zipOutputStream.putNextEntry(new ZipEntry("xl/tables/table" + (relationshipHelper.tableXmlId) + ".xml"));//No I18N
                        new TableToXml(this.zipOutputStream, table, xLSX_CONVERSION_STATS, defaultFont).write(relationshipHelper.tableXmlId++);
                    }
                }
            }
            if(!pivotTables.isEmpty()) {
                this.xLSX_CONVERSION_STATS.addStats(PivotTable.class.getSimpleName());
                for (PivotTable pivotTable : pivotTables) {
                    List<PivotField> pivotFieldsToWrite = pivotTable.getPivotFieldsForXLSX();
                    if(pivotFieldsToWrite == null || pivotFieldsToWrite.isEmpty())
                    {
                        continue;
                    }


                    if (pivotTable.getTargetCellRange().getAssociatedSheetName().equals(sheet.getAssociatedName())) {
                        DataRange sourceCellRange = pivotTable.getSourceCellRange(this.workbook);
                        Integer pivotCacheDefinitionXmlId = pivotCacheDefinitionXmlIdOfPivotCellRange.get(sourceCellRange);
                        if (pivotCacheDefinitionXmlId == null) {
                            pivotCacheDefinitionXmlId = pivotCacheDefinitionXmlIdOfPivotCellRange.size() + 1;
                            pivotCacheDefinitionXmlIdOfPivotCellRange.put(sourceCellRange, pivotCacheDefinitionXmlId);

                            String pivotCacheDefinitionXmlName =  "pivotCacheDefinition" + pivotCacheDefinitionXmlId;//No I18N
                            {
                                int workbookXmlRelsRelationshipId = workbookXmlRelsRelationshipElements.size() + 1;
                                workbookXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                                        .addAttribute(AttributeNameConstants.TARGET, "pivotCache/" + pivotCacheDefinitionXmlName + ".xml")//No I18N
                                        .addAttribute(AttributeNameConstants.REL_ID, "rId" + workbookXmlRelsRelationshipId)//No I18N
                                        .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/pivotCacheDefinition"));//No I18N
                                contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                                        .addAttribute(AttributeNameConstants.PARTNAME, "/xl/pivotCache/" + pivotCacheDefinitionXmlName + ".xml")//No I18N
                                        .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml"));//No I18N
                                workbookXmlPivotCacheElements.add(XmlElementWriter.newInstance()
                                        .addAttribute(AttributeNameConstants.CACHEID, String.valueOf(pivotCacheDefinitionXmlId))
                                        .addAttribute(AttributeNameConstants.R_ID, "rId" + workbookXmlRelsRelationshipId));//No I18N
                            }
                            this.zipOutputStream.putNextEntry(new ZipEntry("xl/pivotCache/"+ pivotCacheDefinitionXmlName + ".xml"));//No I18N
                            PivotCacheDefinitionToXML pivotCacheDefinition = new PivotCacheDefinitionToXML(this.zipOutputStream);
                            Map<String, String> changedFieldNames = dataRangeToChangedFieldNamesMap.computeIfAbsent(sourceCellRange, k -> new HashMap<>());
                            pivotCacheDefinition.write_pivotCacheDefinition(workbook, sourceCellRange, pivotTable.getSourceName(), pivotCacheDefinitionXmlId, pivotCacheFieldsOfPivotCellRange.get(sourceCellRange), changedFieldNames);
                        }
                        List<XmlElementWriter> pivotTableXmlRelsRelationshipElements = new ArrayList<>();
                        pivotTableXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                                .addAttribute(AttributeNameConstants.TARGET, "../pivotCache/" + "pivotCacheDefinition" + pivotCacheDefinitionXmlId + ".xml")//No I18N
                                .addAttribute(AttributeNameConstants.REL_ID, "rId" + (pivotTableXmlRelsRelationshipElements.size() + 1))//No I18N
                                .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/pivotCacheDefinition"));//No I18N

                        String pivotTableXmlName = "pivotTable" + (pivotTableXmlId++);//No I18N
                        contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                                .addAttribute(AttributeNameConstants.PARTNAME, "/xl/pivotTables/" + pivotTableXmlName + ".xml")//No I18N
                                .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml"));//No I18N
                        sheetXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                                .addAttribute(AttributeNameConstants.TARGET, "../pivotTables/"+pivotTableXmlName+ ".xml")//No I18N
                                .addAttribute(AttributeNameConstants.REL_ID, "rId"+(sheetXmlRelsRelationshipElements.size()+1))//No I18N
                                .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/pivotTable"));//No I18N
                        this.zipOutputStream.putNextEntry(new ZipEntry("xl/pivotTables/" + pivotTableXmlName + ".xml"));//No I18N
                        PivotTableToXML pivotTableToXML = new PivotTableToXML(this.zipOutputStream);
                        pivotTableToXML.write_pivotTableDefinition(workbook, pivotTable, pivotCacheDefinitionXmlId, pivotCacheFieldsOfPivotCellRange.get(sourceCellRange));

                        this.zipOutputStream.putNextEntry(new ZipEntry("xl/pivotTables/_rels/" + pivotTableXmlName + ".xml.rels"));//No I18N
                        writeXmlRels(pivotTableXmlRelsRelationshipElements);
                    }
                }
            }

            if (!sheetToXml.getAnnotatedCells().isEmpty()) {
                this.xLSX_CONVERSION_STATS.addStats("Annotations");//No I18N
                String vmlDrawingXmlName = "vmlDrawing" + (relationshipHelper.commentsXmlId) + ".xml";//No I18N
                this.zipOutputStream.putNextEntry(new ZipEntry("xl/drawings/"+vmlDrawingXmlName));//No I18N
                VmlDrawingToXml vmlDrawingToXml = new VmlDrawingToXml();
                vmlDrawingToXml.write(this.zipOutputStream, sheetToXml.getAnnotatedCells(), commentVmlDrawingShapeId);
                commentVmlDrawingShapeId+=sheetToXml.getAnnotatedCells().size();

                String commentsXmlName = "comments" + (relationshipHelper.commentsXmlId++) + ".xml";//No I18N
                this.zipOutputStream.putNextEntry(new ZipEntry("xl/"+commentsXmlName));//No I18N
                CommentToXml commentToXml = new CommentToXml(sheetToXml.getAnnotatedCells());
                commentToXml.write(this.zipOutputStream);
            }

            List<Slicer> slicers = new ArrayList<>();
            for(Slicer slicer : workbook.getPivotSlicers()) {
                if(slicer.getPosition().getAssociatedSheetName().equals(sheet.getAssociatedName())) {
                    slicers.add(slicer);
                }
            }
            writeSlicers(slicers, pivotSlicerConnections, relationshipHelper);

            List<TimeLineSlicer> timelines = new ArrayList<>();
            for(TimeLineSlicer timeline : workbook.getTimelines()) {
                if(timeline.getPosition().getAssociatedSheetName().equals(sheet.getAssociatedName())) {
                    timelines.add(timeline);
                }
            }
            writeTimelines(timelines, timelineConnections, relationshipHelper);

            final List<DrawingProperties> imageDrawingPropertiesList = this.xlsxWriterImageContainer.getDrawingPropertiesOfSheet(sheet.getName());

            final Map<String, Chart> sheetChartMap = sheet.getWorkbook().getChartsContainer().getChartsMap().get(sheet.getAssociatedName());
            final Map<String, com.zoho.sheet.chart.Chart> oldChartMap = sheet.getWorkbook().getSheetChartMap(sheet.getAssociatedName());
            if (!imageDrawingPropertiesList.isEmpty() || ((sheetChartMap != null && !sheetChartMap.isEmpty()) || (oldChartMap != null && !oldChartMap.isEmpty())) || !slicers.isEmpty() || !timelines.isEmpty()) {
                List<DrawingProperties> drawingPropertiesList = new ArrayList<>(imageDrawingPropertiesList);
                if ((sheetChartMap != null && !sheetChartMap.isEmpty())) {
                    this.xLSX_CONVERSION_STATS.addStats(Chart.class.getSimpleName());
                    for (Chart chart : sheetChartMap.values()) {

                        List<DataRange> dataRange = ChartToXml.getDataRanges(workbook, SheetChartAPI.getDataSources(chart.getSheetMeta()));
                        if (dataRange != null) {
                            if(dataRange.stream()
                                    .map(dataRange1->workbook.getSheet(dataRange1.getAssociatedSheetName()).getAssociatedName())
                                    .filter(asn-> !this.sheetsToBeParsed.contains(asn))
                                    .findAny().isPresent()) {
                                continue;
                            }
                        }
                        final int currentChartXmlId = chartXmlId++;

                        contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                                .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.drawingml.chart+xml")//No I18N
                                .addAttribute(AttributeNameConstants.PARTNAME,"/xl/charts/chart"+ currentChartXmlId+".xml"));//No I18N

                        this.zipOutputStream.putNextEntry(new ZipEntry("xl/charts/chart"+ currentChartXmlId+".xml")); //No I18N

                        ChartToXml chartToXml = new ChartToXml(chart, this.zipOutputStream, this.workbook);
                        chartToXml.write();

                        final DrawingProperties drawingProperties = chartToXml.getDrawingProperties(sheet);
                        drawingProperties.setImagePath("../charts/chart"+currentChartXmlId+".xml");//No I18N
                        drawingPropertiesList.add(drawingProperties);
                    }
                } else if((oldChartMap != null && !oldChartMap.isEmpty())) {
                    for (com.zoho.sheet.chart.Chart chart : oldChartMap.values()) {

                        List<Range> dataRange = chart.getDataRange();
                        if (dataRange != null) {
                            if(dataRange.stream()
                                    .map(dataRange1->dataRange1.getSheet().getAssociatedName())
                                    .filter(asn-> !this.sheetsToBeParsed.contains(asn))
                                    .findAny().isPresent()) {
                                continue;
                            }
                        }
                        final int currentChartXmlId = chartXmlId++;

                        contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                                .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.drawingml.chart+xml")//No I18N
                                .addAttribute(AttributeNameConstants.PARTNAME,"/xl/charts/chart"+ currentChartXmlId+".xml"));//No I18N

                        this.zipOutputStream.putNextEntry(new ZipEntry("xl/charts/chart"+ currentChartXmlId+".xml")); //No I18N

                        SecondGenChartToXml chartToXml = new SecondGenChartToXml(chart, this.zipOutputStream);
                        chartToXml.write();

                        final DrawingProperties drawingProperties = chartToXml.getDrawingProperties(sheet);
                        drawingProperties.setImagePath("../charts/chart"+currentChartXmlId+".xml");//No I18N
                        drawingPropertiesList.add(drawingProperties);
                    }
                }

                List<SlicerBody> slicerBodies = new ArrayList<>(slicers);
                slicerBodies.addAll(timelines);

                if(!slicerBodies.isEmpty())
                {
                    for (SlicerBody slicerBody : slicerBodies)
                    {
                        DrawingProperties drawingProperty = new DrawingProperties();
                        int startRowIndex = slicerBody.getPosition().getRowIndex();
                        int startColIndex = slicerBody.getPosition().getColIndex();
                        int startRowDiff = (int) slicerBody.getPosition().getRowDiff();
                        int startColDiff = (int) slicerBody.getPosition().getColDiff();
                        int height = (int) slicerBody.getHeight();
                        int width = (int) slicerBody.getWidth();

                        if(slicerBody instanceof  Slicer) {
                            drawingProperty.setSlicer(true);
                        } else if(slicerBody instanceof TimeLineSlicer){
                            drawingProperty.setTimeline(true);
                        }

                        drawingProperty.setHeight(height)
                                .setWidth(width)
                                .setRowIndex(startRowIndex)
                                .setColIndex(startColIndex)
                                .setRowDiff(startRowDiff)
                                .setColDiff(startColDiff)
                                .setName(slicerBody.getSlicerID());

                        drawingPropertiesList.add(drawingProperty);
                    }
                }
                String drawingXmlName = "drawing"+(relationshipHelper.drawingXmlId++)+".xml";//No I18N

                this.zipOutputStream.putNextEntry(new ZipEntry("xl/drawings/"+drawingXmlName)); //No I18N
                DrawingToXML drawingToXML = new DrawingToXML();
                drawingToXML.write(zipOutputStream, drawingPropertiesList);

                List<XmlElementWriter> drawingXmlRelsRelationshipElements = new ArrayList<>();
                for (DrawingProperties drawingProperties : drawingPropertiesList) {
                    String relType = null;
                    final String imagePath = drawingProperties.getImagePath();
                    if (drawingProperties.isImage()) {
                        imageExtensionSet.add(imagePath.substring(imagePath.lastIndexOf(".")+1));
                        relType = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/image";//No I18N
                    } else if(drawingProperties.isChart()) {
                        relType = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart";//No I18N
                    }
                    else if(drawingProperties.isSlicer()) {
                        continue;
                    }else if(drawingProperties.isTimeline()) {
                        continue;
                    }
                    drawingXmlRelsRelationshipElements.add(
                            XmlElementWriter.newInstance()
                                    .addAttribute(AttributeNameConstants.TARGET, imagePath)
                                    .addAttribute(AttributeNameConstants.REL_ID, "rId"+(drawingXmlRelsRelationshipElements.size()+1))//No I18N
                                    .addAttribute(AttributeNameConstants.RELTYPE, relType)
                    );
                }

                if(!drawingXmlRelsRelationshipElements.isEmpty()) {
                    this.zipOutputStream.putNextEntry(new ZipEntry("xl/drawings/_rels/"+drawingXmlName+".rels")); //No I18N
                    writeXmlRels(drawingXmlRelsRelationshipElements);
                }
            }

            if(!sheetXmlRelsRelationshipElements.isEmpty()) {
                this.zipOutputStream.putNextEntry(new ZipEntry("xl/worksheets/_rels/" + sheetXmlName + ".xml.rels"));
                writeXmlRels(sheetXmlRelsRelationshipElements);
            }
        }
        for(SlicerConnection connection : pivotSlicerConnections)
        {
            String slicerCacheXmlName = "slicerCache" + (slicerCacheXmlId++); //No I18N
            String rid = "rId"+ (workbookXmlRelsRelationshipElements.size() + 1); //No I18N
            workbookXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.TARGET, "slicerCaches/" + slicerCacheXmlName + ".xml") //No I18N
                    .addAttribute(AttributeNameConstants.REL_ID, rid)
                    .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.microsoft.com/office/2007/relationships/slicerCache")); //No I18N
            workbookXmlSlicerCacheElements.add(XmlElementWriter.newInstance().addAttribute(AttributeNameConstants.R_ID, rid));
            DataRange sourceCellRange = workbook.getDataPivotTable(connection.getTablesNames().get(0)).getSourceCellRange(workbook);
            writeSlicerCache(slicerCacheXmlName, connection, pivotTables, pivotCacheDefinitionXmlIdOfPivotCellRange, contentTypesOverrideElements, pivotCacheFieldsOfPivotCellRange.get(sourceCellRange), dataRangeToChangedFieldNamesMap.get(sourceCellRange));
        }

        for(SlicerConnection connection : timelineConnections)
        {
            String timelineCacheName = "NativeTimeline_" + timelineCacheXmlId; //No I18N
            String timelineCacheXmlName = "timelineCache" + (timelineCacheXmlId++); //No I18N
            String rid = "rId"+ (workbookXmlRelsRelationshipElements.size() + 1); //No I18N
            workbookXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.TARGET, "timelineCaches/" + timelineCacheXmlName + ".xml") //No I18N
                    .addAttribute(AttributeNameConstants.REL_ID, rid)
                    .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.microsoft.com/office/2011/relationships/timelineCache")); //No I18N
            workbookXmlTimelineCacheElements.add(XmlElementWriter.newInstance().addAttribute(AttributeNameConstants.R_ID, rid));
            writeTimelineCache(timelineCacheName, timelineCacheXmlName, connection, pivotTables, pivotCacheDefinitionXmlIdOfPivotCellRange, contentTypesOverrideElements);
        }

        for(int i = 1; i < relationshipHelper.ctrlPropXmlId; i++) {
            this.zipOutputStream.putNextEntry(new ZipEntry("xl/ctrlProps/ctrlProp"+i+".xml"));//No I18N
            this.zipOutputStream.write("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><formControlPr xmlns=\"http://schemas.microsoft.com/office/spreadsheetml/2009/9/main\" objectType=\"Button\" lockText=\"1\"/>".getBytes());//No I18N
        }

        if (sheetToXml.isDynamicArrayFormulaPresent()) {
            workbookXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.TARGET, "metadata.xml")//No I18N
                    .addAttribute(AttributeNameConstants.REL_ID, "rId" + (workbookXmlRelsRelationshipElements.size() + 1))//No I18N
                    .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata"));//No I18N
            contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.PARTNAME, "/xl/metadata.xml")//No I18N
                    .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml"));//No I18N
            this.zipOutputStream.putNextEntry(new ZipEntry("xl/metadata.xml"));//No I18N
            String metadatXml = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n" +//No I18N
                    "<metadata xmlns=\"http://schemas.openxmlformats.org/spreadsheetml/2006/main\" xmlns:xlrd=\"http://schemas.microsoft.com/office/spreadsheetml/2017/richdata\" xmlns:xda=\"http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray\"><metadataTypes count=\"2\"><metadataType name=\"XLDAPR\" minSupportedVersion=\"120000\" copy=\"1\" pasteAll=\"1\" pasteValues=\"1\" merge=\"1\" splitFirst=\"1\" rowColShift=\"1\" clearFormats=\"1\" clearComments=\"1\" assign=\"1\" coerce=\"1\" cellMeta=\"1\"/><metadataType name=\"XLRICHVALUE\" minSupportedVersion=\"120000\" copy=\"1\" pasteAll=\"1\" pasteValues=\"1\" merge=\"1\" splitFirst=\"1\" rowColShift=\"1\" clearFormats=\"1\" clearComments=\"1\" assign=\"1\" coerce=\"1\"/></metadataTypes><futureMetadata name=\"XLDAPR\" count=\"1\"><bk><extLst><ext uri=\"{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}\"><xda:dynamicArrayProperties fDynamic=\"1\" fCollapsed=\"0\"/></ext></extLst></bk></futureMetadata><futureMetadata name=\"XLRICHVALUE\" count=\"1\"><bk><extLst><ext uri=\"{3e2802c4-a4d2-4d8b-9148-e3be6c30e623}\"><xlrd:rvb i=\"0\"/></ext></extLst></bk></futureMetadata><cellMetadata count=\"1\"><bk><rc t=\"1\" v=\"0\"/></bk></cellMetadata><valueMetadata count=\"1\"><bk><rc t=\"2\" v=\"0\"/></bk></valueMetadata></metadata>";//No I18N

            this.zipOutputStream.write(metadatXml.getBytes());
        }


        this.xLSX_CONVERSION_STATS.addStats("images");//No I18N
        this.xlsxWriterImageContainer.writeImages(this.zipOutputStream, "xl");//No I18N

        this.xLSX_CONVERSION_STATS.addStats("sharedStrings");//No I18N
        if(!sheetToXml.sharedStrings.isEmpty()) {
            workbookXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.TARGET, "sharedStrings.xml")
                    .addAttribute(AttributeNameConstants.REL_ID, "rId" + (workbookXmlRelsRelationshipElements.size() + 1))
                    .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings"));
            contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.PARTNAME, "/xl/sharedStrings.xml")
                    .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"));
            this.zipOutputStream.putNextEntry(new ZipEntry("xl/sharedStrings.xml"));//No I18N
            this.write_sharedStrings(sheetToXml.sharedStrings, workbook.getTheme(), defaultFont);
        }


        workbookXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                .addAttribute(AttributeNameConstants.TARGET, "styles.xml")
                .addAttribute(AttributeNameConstants.REL_ID, "rId" + (workbookXmlRelsRelationshipElements.size() + 1))
                .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles"));
        contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                .addAttribute(AttributeNameConstants.PARTNAME, "/xl/styles.xml")
                .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml"));
        this.zipOutputStream.putNextEntry(new ZipEntry("xl/styles.xml"));
        List<Table> tables = new ArrayList<>();
        for (Sheet sheet : workbook.getSheetList()) {
            if(this.sheetsToBeParsed.contains(sheet.getAssociatedName())) {
                tables.addAll(sheet.getTables(false).values());
            }
        }
        this.xLSX_CONVERSION_STATS.addStats("styles");//No I18N
        StylesToXML stylesToXML = new StylesToXML(this.zipOutputStream, sheetToXml.stylesBuilder, sheetToXml.dxfBuilder, workbook.getTheme());
        StylesBuilder ext14Dxf = new StylesBuilder(true, xLSX_CONVERSION_STATS, workbook.getTheme(), defaultFont, StylesBuilder.getCellAlignment(workbook.getCellStyle("Default"), false, null),workbook); //No I18N
        StylesBuilder ext15Dxf = new StylesBuilder(true, xLSX_CONVERSION_STATS, workbook.getTheme(), defaultFont, StylesBuilder.getCellAlignment(workbook.getCellStyle("Default"), false, null),workbook); //No I18N
        stylesToXML.write_styles(tables, slicerStyleMap, timelineStyleMap, ext14Dxf, ext15Dxf);

        this.xLSX_CONVERSION_STATS.addStats("workbook");//No I18N
        List<XmlElementWriter> relsXmlRelationshipElements = new ArrayList<>();
        relsXmlRelationshipElements.add(XmlElementWriter.newInstance()
                .addAttribute(AttributeNameConstants.TARGET, "xl/workbook.xml")
                .addAttribute(AttributeNameConstants.REL_ID, "rId"+ (relsXmlRelationshipElements.size() + 1))
                .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument"));
        this.zipOutputStream.putNextEntry(new ZipEntry("xl/workbook.xml"));
        this.write_Workbook(workbookXmlSheetElements, workbookXmlPivotCacheElements, workbookXmlSlicerCacheElements, workbookXmlTimelineCacheElements, workbook, sheetASN_OrderMap);

        this.xLSX_CONVERSION_STATS.addStats("app");//No I18N
        relsXmlRelationshipElements.add(XmlElementWriter.newInstance()
                .addAttribute(AttributeNameConstants.TARGET, "docProps/app.xml")
                .addAttribute(AttributeNameConstants.REL_ID, "rId"+ (relsXmlRelationshipElements.size() + 1))
                .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties"));
        this.zipOutputStream.putNextEntry(new ZipEntry("docProps/app.xml"));
        this.write_app();

        relsXmlRelationshipElements.add(XmlElementWriter.newInstance()
                .addAttribute(AttributeNameConstants.TARGET, "docProps/core.xml")
                .addAttribute(AttributeNameConstants.REL_ID, "rId"+ (relsXmlRelationshipElements.size() + 1))
                .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties"));
        this.zipOutputStream.putNextEntry(new ZipEntry("docProps/core.xml"));
        this.write_core();

        this.zipOutputStream.putNextEntry(new ZipEntry("xl/_rels/workbook.xml.rels"));
        writeXmlRels(workbookXmlRelsRelationshipElements);

        this.zipOutputStream.putNextEntry(new ZipEntry("[Content_Types].xml"));
        this.write_ContentTypes(contentTypesOverrideElements, imageExtensionSet);

        this.zipOutputStream.putNextEntry(new ZipEntry("_rels/.rels"));
        writeXmlRels(relsXmlRelationshipElements);
    }

    private Map<DataRange, List<PivotField>> getCacheFields(List<PivotTable> pivotTables) throws Exception {
        Map<DataRange, Map<Integer,List<PivotField>>> pivotCacheFieldsOfPivotCellRange = new HashMap<>();

        for (PivotTable pivotTable : pivotTables) {
            List<PivotField> tableFields = pivotTable.getPivotFieldsForXLSX();
            if(tableFields == null || tableFields.isEmpty())
            {
                continue;
            }
            //dataRangePivotTableMap.putIfAbsent(pivotTable.getSourceCellRange(), pivotTable);
            DataRange currentDataRange = pivotTable.getSourceCellRange(workbook);
            Map<Integer, List<PivotField>> pivotFieldsCache = pivotCacheFieldsOfPivotCellRange.computeIfAbsent(currentDataRange, k -> new HashMap<>());
            int startCol = currentDataRange.getStartColIndex();
            for(PivotField tableField : tableFields) {
                if(tableField.getColIndex() == -1)
                {
                    continue;
                }

                int pfColIndex = tableField.getColIndex() - startCol;
                List<PivotField> cachedFields = pivotFieldsCache.computeIfAbsent(pfColIndex, k -> new ArrayList<>());

                boolean addPivotField = true;
                for (PivotField cachedField : cachedFields) {
                    // Skipping comparing with Already added Data field with current NON-Data field.
                    if(cachedField.getOrientation() == tableField.getOrientation() || cachedField.getOrientation() != PivotField.Orientation.DATA) {
                        if (cachedField.equalsForXLSXxml(tableField)) {
                            addPivotField = false;
                            break;
                        }
                    }
                }

                if (addPivotField) {
                    cachedFields.add(tableField);
                }
            }
        }


        // Remove unnecessary Data Field and sort fields in writing order.
        Map<DataRange, List<PivotField>> rangeToOrderedFieldList = new HashMap<>();
        for(DataRange dataRange : pivotCacheFieldsOfPivotCellRange.keySet()) {
            Map<Integer, List<PivotField>> cacheFieldIndexMap = pivotCacheFieldsOfPivotCellRange.get(dataRange);

            List<PivotField> orderedPivotFields = new ArrayList<>();
            List<PivotField> extraFields = new ArrayList<>();
            for (int i = 0; i < dataRange.getColSize(); i++) {
                List<PivotField> columnFields = cacheFieldIndexMap.get(i);

                if(columnFields == null)
                {
                    PivotField dummyField = new PivotField();
                    orderedPivotFields.add(dummyField);
                }
                else {
                    Collections.sort(columnFields, new Comparator<PivotField>() {
                        @Override
                        public int compare(PivotField o1, PivotField o2) {

                            PivotField.Orientation t1 = o1.getOrientation();
                            PivotField.Orientation t2 = o2.getOrientation();
                            if (t1 == PivotField.Orientation.DATA) {
                                return 1;
                            } else if (t2 == PivotField.Orientation.DATA) {
                                return -1;
                            } else {

                                CalendarValue.PeriodType p1 = o1.getPeriodType();
                                CalendarValue.PeriodType p2 = o2.getPeriodType();
                                int sortIndex1 = p1 == null ? -1 : p1.getSortIndex();
                                int sortIndex2 = p2 == null ? -1 : p2.getSortIndex();

                                return Integer.compare(sortIndex1, sortIndex2);
                            }
                        }
                    });

                    orderedPivotFields.add(columnFields.get(0));

                    int size = columnFields.size();
                    // Remove the filed at the end if it is DATA field.
                    if(size > 1 && columnFields.get(size - 1).getOrientation() == PivotField.Orientation.DATA)
                    {
                        size -= 1;
                    }
                    // ADD if any extra field present
                    if(size > 1)
                    {
                        extraFields.addAll(columnFields.subList(1, size));
                    }
                }
            }

            orderedPivotFields.addAll(extraFields);

            rangeToOrderedFieldList.put(dataRange, orderedPivotFields);
        }


        return rangeToOrderedFieldList;
    }

    private String getSlicerCellStyleName(CellStyle slicerCellStyle)
    {
        for (CellStyle cellStyle : slicerCellStyles) {
            if (cellStyle.equals(slicerCellStyle)) {
                return cellStyle.getStyleName();
            }
        }

        slicerCellStyles.add(slicerCellStyle);
        return slicerCellStyle.getStyleName();
    }

    private void writeTimelines(List<TimeLineSlicer> timelines, List<SlicerConnection> connections, RelationshipHelper relationshipHelper) throws IOException {
        if(!timelines.isEmpty())
        {
            StringBuilder sb = new StringBuilder(XML_VERSION_ENCODING);
            XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance()
                    .setElementName(ElementNameConstants.TIMELINES)
                    .addAttribute(AttributeNameConstants.XMLNS, "http://schemas.microsoft.com/office/spreadsheetml/2010/11/main") //No I18N
                    .write(sb);

            for(TimeLineSlicer timeline : timelines)
            {
                String styleName = getTimelineStyleName(timeline);
                String cacheName = "NativeTimeline_" + (connections.indexOf(timeline.getConnection()) + 1); //No I18N

                DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                xmlElementWriter.setElementName(ElementNameConstants.TIMELINE)
                        .addAttribute(AttributeNameConstants.NAME, timeline.getSlicerID())
                        .addAttribute(AttributeNameConstants.CACHE, cacheName)
                        .addAttribute(AttributeNameConstants.CAPTION, timeline.getName());
                if(timeline.getScrollStartDate() != null) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.SCROLL_POSITION, formatter.format(timeline.getScrollStartDate()));
                }
                xmlElementWriter.addAttribute(AttributeNameConstants.STYLE, styleName);

                int xlSelectionTimeLevel = SlicerBody.PeriodType.DAYS.getXLSXPeriodTypeValue();
                int xlTimeLevel = timeline.getPeriodType().getXLSXPeriodTypeValue();
                xmlElementWriter.addAttribute(AttributeNameConstants.LEVEL, String.valueOf(xlTimeLevel));
                xmlElementWriter.addAttribute(AttributeNameConstants.SELECTION_LEVEL, String.valueOf(xlSelectionTimeLevel));

                boolean showHeader = timeline.getSelectedLayout() == SlicerBody.SlicerLayout.TITLE_AND_BUTTONS ||
                        timeline.getSelectedLayout() == SlicerBody.SlicerLayout.TITLE_AND_BUTTONS_NOFILL ||
                        timeline.getSelectedLayout() == SlicerBody.SlicerLayout.ALL_ELEMENTS;
                boolean showSelectionLabelAndTimeLevel = timeline.getSelectedLayout() == SlicerBody.SlicerLayout.TIME_LEVEL_BUTTONS ||
                        timeline.getSelectedLayout() == SlicerBody.SlicerLayout.TIME_LEVEL_BUTTONS_NOFILL ||
                        timeline.getSelectedLayout() == SlicerBody.SlicerLayout.ALL_ELEMENTS;
                if (!showHeader) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.SHOW_HEADER, String.valueOf(0));
                }
                if (!showSelectionLabelAndTimeLevel) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.SHOW_TIME_LEVEL, String.valueOf(0));
                    xmlElementWriter.addAttribute(AttributeNameConstants.SHOW_SELECTION_LABEL, String.valueOf(0));
                }

                xmlElementWriter.closeElement().write(sb);
            }

            xmlElementWriter.setElementName(ElementNameConstants.TIMELINES)
                    .closeElement()
                    .write(sb);
            String timelineFileName = "timeline" + relationshipHelper.timelineXmlId++; //No I18N
            this.zipOutputStream.putNextEntry(new ZipEntry("xl/timelines/" + timelineFileName +".xml")); //No I18N
            this.zipOutputStream.write(sb.toString().getBytes());
        }
    }

    private void writeSlicers(List<Slicer> slicers, List<SlicerConnection> connections, RelationshipHelper relationshipHelper) throws Exception
    {
        if(!slicers.isEmpty()) {
            StringBuilder sb = new StringBuilder(XML_VERSION_ENCODING);
            String slicerXmlName = "slicer" + relationshipHelper.slicerXmlId++; //No I18N
            XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance()
                    .setElementName(ElementNameConstants.SLICERS)
                    .addAttribute(AttributeNameConstants.XMLNS, "http://schemas.microsoft.com/office/spreadsheetml/2009/9/main") //No I18N
                    .write(sb);
            for (Slicer slicer : slicers) {
                String styleName = getSlicerStyleName(slicer);
                String cacheName = "slicerCache" + (connections.indexOf(slicer.getConnection()) + 1); //No I18N

                xmlElementWriter.setElementName(ElementNameConstants.SLICER)
                        .addAttribute(AttributeNameConstants.NAME, slicer.getSlicerID())
                        .addAttribute(AttributeNameConstants.CACHE, cacheName)
                        .addAttribute(AttributeNameConstants.CAPTION, slicer.getName())
                        .addAttribute(AttributeNameConstants.COLUMNS, String.valueOf(slicer.getColumnLayout()))
                        .addAttribute(AttributeNameConstants.ROWHEIGHT, String.valueOf((int) (EngineUtils1.convertPixelsToInchesDouble(String.valueOf(slicer.getButtonHeight()), EngineConstants.ROWDPI) * 914400)))
                        .addAttribute(AttributeNameConstants.STYLE, styleName);
                if (slicer.getSelectedLayout() == Slicer.SlicerLayout.BUTTONS || slicer.getSelectedLayout() == Slicer.SlicerLayout.BUTTONS_NOFILL) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.SHOWCAPTION, String.valueOf(0));
                }
                xmlElementWriter.closeElement().write(sb);
            }

            xmlElementWriter.setElementName(ElementNameConstants.SLICERS)
                    .closeElement()
                    .write(sb);
            this.zipOutputStream.putNextEntry(new ZipEntry("xl/slicers/" + slicerXmlName + ".xml")); //No I18N
            this.zipOutputStream.write(sb.toString().getBytes());
        }
    }

    private String getSlicerStyleName(Slicer slicer) {

        String wholeTableStyleName = getSlicerCellStyleName(slicer.getCellStyle(Slicer.SlicerStyle.WHOLETABLESTYLE));
        String headerStyleName = getSlicerCellStyleName(slicer.getCellStyle(Slicer.SlicerStyle.HEADERSTYLE));
        String itemOnStyleName = getSlicerCellStyleName(slicer.getCellStyle(Slicer.SlicerStyle.ITEMONSTYLE));
        String itemOffStyleName = getSlicerCellStyleName(slicer.getCellStyle(Slicer.SlicerStyle.ITEMOFFSTYLE));
        final String SLICER_CUSTOM_STYLE = "custom_slicer_style "; //No I18N
        String slicerStyle;
        String slicerStyleCombination = wholeTableStyleName + headerStyleName + itemOnStyleName + itemOffStyleName;
        for(int i=0; i<slicerCustomStyles.size(); i++)
        {
            if(slicerCustomStyles.get(i).equals(slicerStyleCombination))
            {
                slicerStyle = SLICER_CUSTOM_STYLE + (i+1);
                return  slicerStyle;
            }
        }

        slicerCustomStyles.add(slicerStyleCombination);
        slicerStyle =  SLICER_CUSTOM_STYLE +slicerCustomStyles.size();
        XLSXSlicerStyleContainer xlsxSlicerStyleContainer = new XLSXSlicerStyleContainer();
        xlsxSlicerStyleContainer.setCellStyle(Slicer.SlicerStyle.WHOLETABLESTYLE, slicer.getCellStyle(Slicer.SlicerStyle.WHOLETABLESTYLE));
        xlsxSlicerStyleContainer.setCellStyle(Slicer.SlicerStyle.HEADERSTYLE, slicer.getCellStyle(Slicer.SlicerStyle.HEADERSTYLE));
        xlsxSlicerStyleContainer.setCellStyle(Slicer.SlicerStyle.ITEMONSTYLE, slicer.getCellStyle(Slicer.SlicerStyle.ITEMONSTYLE));
        xlsxSlicerStyleContainer.setCellStyle(Slicer.SlicerStyle.ITEMOFFSTYLE, slicer.getCellStyle(Slicer.SlicerStyle.ITEMOFFSTYLE));
        slicerStyleMap.put(slicerStyle, xlsxSlicerStyleContainer);
        return slicerStyle;
    }

    private String getTimelineStyleName(TimeLineSlicer timeLineSlicer) {

        String wholeTableStyleName = getSlicerCellStyleName(timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE));
        String headerStyleName = getSlicerCellStyleName(timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.HEADER_STYLE));
        String itemOnStyleName = getSlicerCellStyleName(timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE));
        String itemOffStyleName = getSlicerCellStyleName(timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE));
        String timeLevelStyle = getSlicerCellStyleName(timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE));
        String sliderLabelStyle = getSlicerCellStyleName(timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE));
        String periodLabel1Style = getSlicerCellStyleName(timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE));
        String periodLabel2Style = getSlicerCellStyleName(timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE));
        String itemOnSpaceStyleName = getSlicerCellStyleName(timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE));
        final String TIMELINE_CUSTOM_STYLE = "custom_timeline_style "; //No I18N
        String slicerStyle;
        String slicerStyleCombination = wholeTableStyleName + headerStyleName + itemOnStyleName + itemOffStyleName + timeLevelStyle
                                         + sliderLabelStyle + periodLabel1Style + periodLabel2Style + itemOnSpaceStyleName;
        for(int i=0; i<slicerCustomStyles.size(); i++)
        {
            if(slicerCustomStyles.get(i).equals(slicerStyleCombination))
            {
                slicerStyle = TIMELINE_CUSTOM_STYLE + (i+1);
                return  slicerStyle;
            }
        }

        slicerCustomStyles.add(slicerStyleCombination);
        slicerStyle =  TIMELINE_CUSTOM_STYLE +slicerCustomStyles.size();
        XLSXTimelineStyleContainer xlsxTimelineStyle = new XLSXTimelineStyleContainer();
        xlsxTimelineStyle.setCellStyle(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE, timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE));
        xlsxTimelineStyle.setCellStyle(TimeLineSlicer.TimeLineStyle.HEADER_STYLE, timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.HEADER_STYLE));
        xlsxTimelineStyle.setCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE, timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE));
        xlsxTimelineStyle.setCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE, timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE));
        xlsxTimelineStyle.setCellStyle(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE, timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE));
        xlsxTimelineStyle.setCellStyle(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE, timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE));
        xlsxTimelineStyle.setCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE, timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE));
        xlsxTimelineStyle.setCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE, timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE));
        xlsxTimelineStyle.setCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE, timeLineSlicer.getCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE));
        timelineStyleMap.put(slicerStyle, xlsxTimelineStyle);
        return slicerStyle;
    }

    private void writeTimelineCache(String timelineCacheName, String timelineCacheXmlName, SlicerConnection connection, List<PivotTable> pivotTables, Map<DataRange, Integer> pivotCacheDefinitionXmlIdOfPivotCellRange, List<XmlElementWriter> contentTypesOverrideElements) throws IOException {
        StringBuilder sb = new StringBuilder(XML_VERSION_ENCODING);
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.setElementName(ElementNameConstants.TIMELINE_CACHE_DEFINITION)
                .addAttribute(AttributeNameConstants.XMLNS, "http://schemas.microsoft.com/office/spreadsheetml/2010/11/main") //No I18N
                .addAttribute(AttributeNameConstants.NAME,timelineCacheName)
                .addAttribute(AttributeNameConstants.SOURCENAME, connection.getFieldName())
                .write(sb);
        xmlElementWriter.setElementName(ElementNameConstants.PIVOTTABLES).write(sb);
        PivotTable pivotTable = null;
        for(String tableName : connection.getTablesNames())
        {
            pivotTable = getPivotTable(pivotTables, tableName);
            if(pivotTable != null) {
                xmlElementWriter.setElementName(ElementNameConstants.PIVOTTABLE)
                        .addAttribute(AttributeNameConstants.TAB_ID, String.valueOf(workbook.getSheetIndex(pivotTable.getTargetCellRange().toRange(workbook).getSheet().getName()) + 1))
                        .addAttribute(AttributeNameConstants.NAME, tableName)
                        .closeElement()
                        .write(sb);
            }
        }
        xmlElementWriter.setElementName(ElementNameConstants.PIVOTTABLES)
                .closeElement()
                .write(sb);
        if(pivotTable != null)
        {
            PivotField pivotField = PivotUtil.getTimelineField(pivotTable, connection.getFieldName());
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            FilterOperator filterOperator = FilterUtil.getFilterOperator(pivotField.getPivotLevel().getPivotFilterInfo().getOperator());

            String xlsxFilterType;
            try{
                PivotFilterInfo.PivotFilterType filterType = PivotFilterInfo.PivotFilterType.getFilterTypeFromInt(pivotField.getPivotLevel().getPivotFilterInfo().getFilterType());
                xlsxFilterType = filterOperator.getXlsxFilterType(pivotField.getColRangeType(), filterType);
            } catch (Exception e) {
                xlsxFilterType = "unknown"; //No I18N
            }
            boolean isSingleRangeFilter = !(filterOperator == FilterOperator.BETWEEN || filterOperator == FilterOperator.EQUALS);//pivotField.getPivotLevel().getPivotFilterInfo().getValue1().isEmpty();
            xmlElementWriter.setElementName(ElementNameConstants.STATE);

            int pivotCacheId = pivotCacheDefinitionXmlIdOfPivotCellRange.get(pivotTable.getSourceCellRange(this.workbook));
            xmlElementWriter.addAttribute(AttributeNameConstants.MINIMAL_REFRESH_VERSION, String.valueOf(6))
                    .addAttribute(AttributeNameConstants.LAST_REFRESH_VERSION, String.valueOf(6))
                    .addAttribute(AttributeNameConstants.FILTER_TYPE, xlsxFilterType)
                    .addAttribute(AttributeNameConstants.PIVOTCACHEID, String.valueOf(pivotCacheId))
                    .write(sb);
            Date[] bounds = PivotUtil.getDateFieldRange(connection, workbook);
            if(isSingleRangeFilter) {
                xmlElementWriter.setElementName(ElementNameConstants.BOUNDS)
                        .addAttribute(AttributeNameConstants.STARTDATE, formatter.format(bounds[0]))
                        .addAttribute(AttributeNameConstants.ENDDATE, formatter.format(bounds[1]))
                        .closeEmptyElement()
                        .write(sb);
                xmlElementWriter.closeElementAndWrite(sb, ElementNameConstants.STATE);

                if(!xlsxFilterType.equals("unknown")) {
                    xmlElementWriter.setElementName(ElementNameConstants.TIMELINE_PIVOT_FILTER)
                            //.addAttribute(AttributeNameConstants.USE_WHOLE_DAY, String.valueOf(1))
                            .addAttribute(AttributeNameConstants.FLD, String.valueOf(pivotField.getColIndex()))
                            .addAttribute(AttributeNameConstants.ID, String.valueOf(1))
                            .write(sb);
                    xmlElementWriter.setElementName(ElementNameConstants.AUTOFILTER)
                            //.addAttribute(AttributeNameConstants.REF, CellReference.ReferenceMode.A1.name())
                            .write(sb);

                    xmlElementWriter.setElementName(ElementNameConstants.X_FILTERCOLUMN)
                            .addAttribute(AttributeNameConstants.XMLNS_X, "http://schemas.openxmlformats.org/spreadsheetml/2006/main") //No I18N
                            .addAttribute(AttributeNameConstants.COLD, String.valueOf(0))
                            .write(sb);

                    xmlElementWriter.write(sb, ElementNameConstants.X_CUSTOMFILTERS);

                    String val = String.valueOf(DateUtil.convertDateToNumber(pivotField.getPivotLevel().getPivotFilterInfo().getDateValue()));

                    xmlElementWriter.setElementName(ElementNameConstants.X_CUSTOMFILTER);
                    xmlElementWriter.addAttribute(AttributeNameConstants.OPERATOR, filterOperator.getZSToXlsxFilterOperator());
                    xmlElementWriter.addAttribute(AttributeNameConstants.VAL, val)
                            .closeEmptyElement()
                            .write(sb);


                    xmlElementWriter.closeElementAndWrite(sb, ElementNameConstants.X_CUSTOMFILTERS);

                    xmlElementWriter.closeElementAndWrite(sb, ElementNameConstants.X_FILTERCOLUMN);
                    xmlElementWriter.closeElementAndWrite(sb, ElementNameConstants.AUTOFILTER);
                    xmlElementWriter.closeElementAndWrite(sb, ElementNameConstants.TIMELINE_PIVOT_FILTER);
                }
            }
            else {
                Date d1 = pivotField.getPivotLevel().getPivotFilterInfo().getDateValue();
                if(d1 != null) {
                    Date d2 = pivotField.getPivotLevel().getPivotFilterInfo().getDateValue1();
                    d2 = d2 == null ? d1 : d2;
                    xmlElementWriter.setElementName(ElementNameConstants.SELECTION)
                            .addAttribute(AttributeNameConstants.STARTDATE, formatter.format(d1))
                            .addAttribute(AttributeNameConstants.ENDDATE, formatter.format(d2))
                            .closeEmptyElement()
                            .write(sb);
                }
                xmlElementWriter.setElementName(ElementNameConstants.BOUNDS)
                        .addAttribute(AttributeNameConstants.STARTDATE, formatter.format(bounds[0]))
                        .addAttribute(AttributeNameConstants.ENDDATE, formatter.format(bounds[1]))
                        .closeEmptyElement()
                        .write(sb);

                xmlElementWriter.closeElementAndWrite(sb, ElementNameConstants.STATE);
            }
        }
        xmlElementWriter.closeElementAndWrite(sb, ElementNameConstants.TIMELINE_CACHE_DEFINITION);

        contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                .addAttribute(AttributeNameConstants.PARTNAME, "/xl/timelineCaches/" + timelineCacheXmlName + ".xml") //No I18N
                .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.ms-excel.timelineCache+xml")); //No I18N
        this.zipOutputStream.putNextEntry(new ZipEntry("xl/timelineCaches/" + timelineCacheXmlName + ".xml")); //No I18N
        this.zipOutputStream.write(sb.toString().getBytes());
    }

    private void writeSlicerCache(String slicerCacheXmlName, SlicerConnection connection, List<PivotTable> pivotTables, Map<DataRange, Integer> pivotCacheDefinitionXmlIdOfPivotCellRange, List<XmlElementWriter> contentTypesOverrideElements, List<PivotField> cacheFields, Map<String, String> changedFieldNames) throws Exception
    {
        StringBuilder sb = new StringBuilder(XML_VERSION_ENCODING);
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        String sourceName = changedFieldNames.get(connection.getFieldName()) != null ? changedFieldNames.get(connection.getFieldName()) : connection.getFieldName();
        xmlElementWriter.setElementName(ElementNameConstants.SLICERCACHEDEFINITION)
                .addAttribute(AttributeNameConstants.XMLNS, "http://schemas.microsoft.com/office/spreadsheetml/2009/9/main") //No I18N
                .addAttribute(AttributeNameConstants.NAME, slicerCacheXmlName)
                .addAttribute(AttributeNameConstants.SOURCENAME, sourceName)
                .write(sb);
        xmlElementWriter.setElementName(ElementNameConstants.PIVOTTABLES).write(sb);
        PivotTable pivotTable = null;
        for(String tableName : connection.getTablesNames())
        {
            pivotTable = getPivotTable(pivotTables, tableName);
            if(pivotTable != null) {
                xmlElementWriter.setElementName(ElementNameConstants.PIVOTTABLE)
                        .addAttribute(AttributeNameConstants.TAB_ID, String.valueOf(workbook.getSheetIndex(pivotTable.getTargetCellRange().toRange(workbook).getSheet().getName()) + 1))
                        .addAttribute(AttributeNameConstants.NAME, tableName)
                        .closeElement()
                        .write(sb);
            }
        }
        xmlElementWriter.setElementName(ElementNameConstants.PIVOTTABLES)
                .closeElement()
                .write(sb);
        if(pivotTable != null)
        {
            String sortOrder = connection.getSortOrder() == 1 ? "descending" : null; //No I18N
            DataRange sourceCellRange = pivotTable.getSourceCellRange(this.workbook);
            xmlElementWriter.setElementName(ElementNameConstants.DATA).write(sb)
                    .setElementName(ElementNameConstants.TABULAR)
                    .addAttribute(AttributeNameConstants.PIVOTCACHEID,String.valueOf(pivotCacheDefinitionXmlIdOfPivotCellRange.get(sourceCellRange)));
                    if(sortOrder != null)
                    {
                        xmlElementWriter.addAttribute(AttributeNameConstants.SORT_ORDER, sortOrder);
                    }
            xmlElementWriter.write(sb);

            PivotField pivotField = pivotTable.getPivotFieldExceptDataAndHidden(connection.getFieldName()).clone(workbook);
            PivotField cacheField = null;
            for(PivotField cf : cacheFields) {
                if(cf.equalsForXLSXxml(pivotField)) {
                    cacheField = cf;
                    break;
                }
            }

            // write items
            List<Integer> indices = new ArrayList<>();
            List<Boolean> isVisibleList = new ArrayList<>();
            List<Boolean> isNotDisabledList = new ArrayList<>();
            boolean isFieldGroup = (pivotField.getPivotGroups() != null && pivotField.getPivotGroups().getDateStart() == null)
                    || (pivotField.getPeriodType() != null && pivotField.getPeriodType() != CalendarValue.PeriodType.NONE);
            // Adding startValue field
            if(isFieldGroup)
            {
                indices.add(0);
                isVisibleList.add(true);
                isNotDisabledList.add(true);
            }
            //////

            int skippedMembers = 0;
            Map<String, String> zsToXLSXEquivalents = pivotField.getPeriodType().getZStoXLSXEquivalents();
            List<String> cacheMembers = new ArrayList<>();
            List<String> fieldMembers = new ArrayList<>();
            if (isFieldGroup && pivotField.getPeriodType() != CalendarValue.PeriodType.YEAR)
            {
                cacheMembers.addAll(zsToXLSXEquivalents.values());
                fieldMembers.addAll(zsToXLSXEquivalents.keySet());
            }
            else {
                cacheMembers.addAll(cacheField.getPivotLevel().getPivotMembers().keySet());
                fieldMembers.addAll(pivotField.getPivotLevel().getPivotMembers().keySet());
            }
            for (String fieldValue :  fieldMembers) {
                PivotMember member = pivotField.getPivotLevel().getPivotMembers().get(fieldValue);
                String cacheValue = zsToXLSXEquivalents.get(fieldValue);
                cacheValue = cacheValue == null ? fieldValue : cacheValue;

                if (member == null || pivotField.getDataPivotUniqueContentDetails(fieldValue) != null) {
                    indices.add(cacheMembers.indexOf(cacheValue) - skippedMembers + (isFieldGroup ?  1 : 0));
                    isVisibleList.add(member == null || member.getDisplay());
                    isNotDisabledList.add(member == null || !member.isButtonEnabled());
                } else {
                    skippedMembers += 1;
                }
            }

            // Adding endValue field
            if(isFieldGroup)
            {
                indices.add(indices.size());
                isVisibleList.add(true);
                isNotDisabledList.add(true);
            }
            /////// writing items
            xmlElementWriter.setElementName(ElementNameConstants.ITEMS).addAttribute(AttributeNameConstants.COUNT, String.valueOf(indices.size())).write(sb);
            for (int i = 0; i < indices.size(); i++) {
                xmlElementWriter.setElementName(ElementNameConstants.I)
                        .addAttribute(AttributeNameConstants.X, String.valueOf(i));
                if (isVisibleList.get(i)) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.S, String.valueOf(1));
                }
                if (isNotDisabledList.get(i)) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.ND, String.valueOf(1));
                }
                xmlElementWriter.closeEmptyElement().write(sb);
            }
            xmlElementWriter.closeElementAndWrite(sb, ElementNameConstants.ITEMS);

            xmlElementWriter.closeElementAndWrite(sb, ElementNameConstants.TABULAR)
                    .closeElementAndWrite(sb, ElementNameConstants.DATA);
        }
        xmlElementWriter.setElementName(ElementNameConstants.SLICERCACHEDEFINITION)
                .closeElement()
                .write(sb);
        contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                .addAttribute(AttributeNameConstants.PARTNAME, "/xl/slicerCaches/" + slicerCacheXmlName + ".xml") //No I18N
                .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.ms-excel.slicerCache+xml")); //No I18N
        this.zipOutputStream.putNextEntry(new ZipEntry("xl/slicerCaches/" + slicerCacheXmlName + ".xml")); //No I18N
        this.zipOutputStream.write(sb.toString().getBytes());
    }

    private PivotTable getPivotTable(List<PivotTable> pivotTables, String tableName) {
        for(PivotTable pivot : pivotTables)
        {
            if(pivot.getName().equals(tableName))
            {
                return pivot;
            }
        }
        return null;
    }

    private List<SlicerConnection> getUniqueConnections(List<SlicerBody> slicers) {
        if(!slicers.isEmpty()) {
            List<SlicerConnection> connections = new ArrayList<>();
            for (SlicerBody slicer : slicers) {
                SlicerConnection slicerConnection = slicer.getConnection();
                boolean available = true;
                for (SlicerConnection connection : connections) {
                    if (connection.equals(slicerConnection)) {
                        available = false;
                        break;
                    }
                }
                if (available) {
                    connections.add(slicerConnection);
                }
            }
            return connections;
        }
        return Collections.emptyList();
    }

    private void writeXmlRels(List<XmlElementWriter> xmlRels) throws IOException {
        StringBuilder stringBuilder = new StringBuilder(XML_VERSION_ENCODING);

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.RELATIONSHIPS)
                .addAttribute(AttributeNameConstants.XMLNS, "http://schemas.openxmlformats.org/package/2006/relationships")//No I18N
                .write(stringBuilder);

        for (XmlElementWriter xmlElementWriter : xmlRels) {
            xmlElementWriter
                    .setElementName(ElementNameConstants.REALATIONSHIP)
                    .closeElement()
                    .write(stringBuilder);
        }

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.RELATIONSHIPS)
                .closeElement()
                .write(stringBuilder);

        this.zipOutputStream.write(stringBuilder.toString().getBytes());
    }

    private void write_app() throws IOException {
        StringBuilder stringBuilder = new StringBuilder(XML_VERSION_ENCODING);

        String[] keys = new String[]{
                AttributeNameConstants.XMLNS,
                AttributeNameConstants.XMLNS_VT};
        String[] values = new String[]{
                "http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",//No I18N
                "http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes"};//No I18N
        stringBuilder.append(Utility.getTag(ElementNameConstants.PROPERTIES, keys, values, false));

        stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.APPLICATION));
        stringBuilder.append(("Zoho Sheet Writer"));//No I18N
        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.APPLICATION));

        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.PROPERTIES));

        this.zipOutputStream.write(stringBuilder.toString().getBytes());
    }

    private void write_core() throws IOException {
        StringBuilder stringBuilder = new StringBuilder(XML_VERSION_ENCODING);

        String[] keys = new String[]{
                AttributeNameConstants.XMLNS_CP,
                AttributeNameConstants.XMLNS_DC,
                AttributeNameConstants.XMLNS_DCMITYPE,
                AttributeNameConstants.XMLNS_DCTERMS,
                AttributeNameConstants.XMLNS_XSI
        };
        String[] values = new String[]{
                "http://schemas.openxmlformats.org/package/2006/metadata/core-properties",//No I18N
                "http://purl.org/dc/elements/1.1/",//No I18N
                "http://purl.org/dc/dcmitype/",//No I18N
                "http://purl.org/dc/terms/",//No I18N
                "http://www.w3.org/2001/XMLSchema-instance"//No I18N
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.CP_COREPROPERTIES, keys, values, false));

        keys = new String[]{
                AttributeNameConstants.XSI_TYPE
        };
        values = new String[]{
                "dcterms:W3CDTF"//No I18N
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.DCTERMS_CREATED, keys, values, false));

        TimeZone tz = TimeZone.getTimeZone("UTC");//No I18N
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");//No I18N
        df.setTimeZone(tz);
        String nowAsISO = df.format(new Date());
        stringBuilder.append(nowAsISO);

        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.DCTERMS_CREATED));

        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.CP_COREPROPERTIES));

        this.zipOutputStream.write(stringBuilder.toString().getBytes());
    }


    private void write_ContentTypes(List<XmlElementWriter> overrideElements, Set<String> imageExtensionSet) throws IOException {
        final StringBuilder stringBuilder = new StringBuilder(XML_VERSION_ENCODING);

        String[] keys = new String[]{
                AttributeNameConstants.XMLNS
        };
        String[] values = new String[] {
                "http://schemas.openxmlformats.org/package/2006/content-types"//No I18N
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.TYPES, keys, values, false));

        keys = new String[]{
                AttributeNameConstants.CONTENTTYPE,
                AttributeNameConstants.EXTENSION
        };
        values = new String[] {
                "application/vnd.openxmlformats-package.relationships+xml",//No I18N
                "rels"//No I18N
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.DEFAULT, keys, values, true));

        keys = new String[]{
                AttributeNameConstants.CONTENTTYPE,
                AttributeNameConstants.EXTENSION
        };
        values = new String[] {
                "application/xml",//No I18N
                "xml"//No I18N
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.DEFAULT, keys, values, true));

        for (String imageExtension : imageExtensionSet) {
            XmlElementWriter.newInstance(ElementNameConstants.DEFAULT)
                    .addAttribute(AttributeNameConstants.EXTENSION, imageExtension)
                    .addAttribute(AttributeNameConstants.CONTENTTYPE, "image/"+imageExtension)//No I18N
                    .closeElement()
                    .write(stringBuilder);
        }

        keys = new String[]{
                AttributeNameConstants.CONTENTTYPE,
                AttributeNameConstants.PARTNAME
        };
        values = new String[] {
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",//No I18N
                "/xl/workbook.xml"//No I18N
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.OVERRIDE, keys, values, true));

        for (XmlElementWriter overrideElement : overrideElements) {
            overrideElement.setElementName(ElementNameConstants.OVERRIDE)
                    .closeElement()
                    .write(stringBuilder);
        }

        keys = new String[]{
                AttributeNameConstants.CONTENTTYPE,
                AttributeNameConstants.PARTNAME
        };
        values = new String[] {
                "application/vnd.openxmlformats-package.core-properties+xml",//No I18N
                "/docProps/core.xml"//No I18N
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.OVERRIDE, keys, values, true));

        keys = new String[]{
                AttributeNameConstants.CONTENTTYPE,
                AttributeNameConstants.PARTNAME
        };
        values = new String[] {
                "application/vnd.openxmlformats-officedocument.extended-properties+xml",//No I18N
                "/docProps/app.xml"//No I18N
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.OVERRIDE, keys, values, true));

        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.TYPES));

        this.zipOutputStream.write(stringBuilder.toString().getBytes());
    }

    private void write_Workbook(List<XmlElementWriter> sheetElements, List<XmlElementWriter> pivotCacheElements, List<XmlElementWriter> slicerCacheElements, List<XmlElementWriter> timelineCacheElements, Workbook workbook, Map<String, Integer> sheetASN_OrderMap) throws IOException {
        final StringBuilder stringBuilder = new StringBuilder(XML_VERSION_ENCODING);

        String[] keys = new String[]{
                AttributeNameConstants.XMLNS,
                AttributeNameConstants.XMLNS_R
        };
        String[] values = new String[] {
                XMLNS_URL,
                XMLNS_REL_URL
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.WORKBOOK, keys, values, false));

        keys = new String[]{AttributeNameConstants.APPNAME};
        values = new String[]{"ZohoSheetWriter"};//No I18N
        stringBuilder.append(Utility.getTag(ElementNameConstants.FILEVERSION, keys, values, true));

        stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.BOOKVIEWS));
        keys = new String[]{AttributeNameConstants.WINDOWHEIGHT,
                AttributeNameConstants.WINDOWWIDTH
        };
        values = new String[]{"8192",
                "16384"
        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.WORKBOOKVIEWS, keys, values, true));
        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.BOOKVIEWS));

        stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.SHEETS));

        for(XmlElementWriter xmlElementWriter: sheetElements){
            xmlElementWriter
                    .setElementName(ElementNameConstants.SHEET)
                    .closeElement()
                    .write(stringBuilder);
        }

        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.SHEETS));

        final List<NamedExpression> namedRanges = workbook.getNamedExpressions();
        if(!namedRanges.isEmpty() || !slicerCacheElements.isEmpty() || !timelineCacheElements.isEmpty()) {

            final XmlElementWriter w = XmlElementWriter.newInstance();
            w.write(stringBuilder, ElementNameConstants.DEFINENAMES);
            for (NamedExpression namedRange : namedRanges) {
                final String name = namedRange.getName();
                String value;
                Cell cell = null;
                if(namedRange.getScopeASN() != null) {
                    Sheet sheetByAssociatedName = workbook.getSheetByAssociatedName(namedRange.getScopeASN());
                    if(sheetByAssociatedName != null) {
                        cell = sheetByAssociatedName.getCell(0,0);
                    }
                }
                if(namedRange.getNode() instanceof ASTParseErrorNode) {
                    value = "\""+Utility.escapeDoubleQuotes(Utility.toString(namedRange.getNode(), workbook, 0, 0, false, cell))+"\"";//No I18N
                    if(value.length()>255) {
                        value = value.substring(0, 255)+"\"";//No I18N
                    }
                } else {
                    value = Utility.toString(namedRange.getNode(), workbook, 0, 0, false, cell);
                    if(namedRange.getNode() instanceof ASTConstant && value.length()>256 && value.startsWith("\"")) {
                        value = value.substring(0, 255)+"\"";//No I18N
                    }
                }

                w.addAttribute(AttributeNameConstants.NAME, Utility.xmlEncode(name))
                        .addValue(Utility.xmlEncode(value));
                String scopeASN = namedRange.getScopeASN();
                String comment = namedRange.getComment();
                if(scopeASN != null) {
                    w.addAttribute(AttributeNameConstants.LOCAL_SHEET_ID, String.valueOf(sheetASN_OrderMap.get(scopeASN)));
                }
                if(comment != null) {
                    w.addAttribute(AttributeNameConstants.COMMENT, comment);
                }
                w.closeElementAndWrite(stringBuilder, ElementNameConstants.DEFINENAME);
            }
            if(!slicerCacheElements.isEmpty()) {
                for(int slicerCacheXmlId = 1; slicerCacheXmlId <= slicerCacheElements.size(); slicerCacheXmlId++)
                {
                    String slicerCacheXmlName = "slicerCache" + (slicerCacheXmlId); //No I18N
                    w.addAttribute(AttributeNameConstants.NAME, Utility.xmlEncode(slicerCacheXmlName))
                            .addValue(Utility.xmlEncode("#N/A")) //No I18N
                            .closeElementAndWrite(stringBuilder, ElementNameConstants.DEFINENAME);
                }
            }

            if(!timelineCacheElements.isEmpty()) {
                for(int timelineCacheXmlId = 1; timelineCacheXmlId <= timelineCacheElements.size(); timelineCacheXmlId++)
                {
                    String timelineCacheXmlName = "NativeTimeline_"+ timelineCacheXmlId; //No I18N
                    w.addAttribute(AttributeNameConstants.NAME, Utility.xmlEncode(timelineCacheXmlName))
                            .addValue(Utility.xmlEncode("#N/A")) //No I18N
                            .closeElementAndWrite(stringBuilder, ElementNameConstants.DEFINENAME);
                }
            }


            w.closeElementAndWrite(stringBuilder, ElementNameConstants.DEFINENAMES);
        }
        if(!pivotCacheElements.isEmpty()) {
            XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
            xmlElementWriter.setElementName(ElementNameConstants.PIVOTCACHES)
                    .write(stringBuilder);

            for (XmlElementWriter pivotCacheElement : pivotCacheElements) {
                pivotCacheElement.setElementName(ElementNameConstants.PIVOTCACHE)
                        .closeElement()
                        .write(stringBuilder);
            }

            xmlElementWriter.setElementName(ElementNameConstants.PIVOTCACHES)
                    .closeElement()
                    .write(stringBuilder);
        }

        XmlElementWriter w = XmlElementWriter.newInstance();
        w.write(stringBuilder, ElementNameConstants.EXTLST);

        if(!slicerCacheElements.isEmpty())
        {
            w.addAttribute(AttributeNameConstants.URI, Utility.xmlEncode("{BBE1A952-AA13-448e-AADC-164F8A28A991}")) //No I18N
                    .addAttribute(AttributeNameConstants.XMLNS_X14, "http://schemas.microsoft.com/office/spreadsheetml/2009/9/main") //No I18N
                    .write(stringBuilder, ElementNameConstants.EXT);

            w.write(stringBuilder, ElementNameConstants.X14_SLICERCACHES);
            for(XmlElementWriter xmlElementWriter : slicerCacheElements)
            {
                xmlElementWriter.closeEmptyElementAndWrite(stringBuilder,ElementNameConstants.X14_SLICERCACHE);
            }
            w.closeElementAndWrite(stringBuilder,ElementNameConstants.X14_SLICERCACHES);
            w.closeElementAndWrite(stringBuilder, ElementNameConstants.EXT);
        }

        if(!timelineCacheElements.isEmpty())
        {
            w.addAttribute(AttributeNameConstants.URI, Utility.xmlEncode("{D0CA8CA8-9F24-4464-BF8E-62219DCF47F9}")) //No I18N
                    .addAttribute(AttributeNameConstants.XMLNS_X15, "http://schemas.microsoft.com/office/spreadsheetml/2010/11/main") //No I18N
                    .write(stringBuilder, ElementNameConstants.EXT);

            w.write(stringBuilder, ElementNameConstants.X15_TIMELINE_CACHE_REFS);
            for(XmlElementWriter xmlElementWriter : timelineCacheElements)
            {
                xmlElementWriter.closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.X15_TIMELINE_CACHE_REF);
            }
            w.closeElementAndWrite(stringBuilder,ElementNameConstants.X15_TIMELINE_CACHE_REFS);
            w.closeElementAndWrite(stringBuilder, ElementNameConstants.EXT);
        }

        w.closeElementAndWrite(stringBuilder, ElementNameConstants.EXTLST);
        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.WORKBOOK));
        this.zipOutputStream.write(stringBuilder.toString().getBytes());
    }


    private void write_sharedStrings(Map<XlsxString, Integer> sharedStrings, ZSTheme theme, StylesBuilder.Font default_font) throws IOException {
        final StringBuilder stringBuilder = new StringBuilder(XML_VERSION_ENCODING);

        String[] keys = new String[]{
                AttributeNameConstants.XMLNS,
                AttributeNameConstants.COUNT,
                AttributeNameConstants.UNIQUECOUNT
        };
        String[] values = new String[] {
                XMLNS_URL,
                String.valueOf(sharedStrings.size()),
                String.valueOf(sharedStrings.size())

        };
        stringBuilder.append(Utility.getTag(ElementNameConstants.SST, keys, values, false));
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        for(XlsxString t : sharedStrings.keySet()) {
            final ZSString zsString = t.getZsString();
            stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.SI));
            if(zsString.getProperties() == null || zsString.getProperties().isEmpty()) {
                final String string = Utility.xmlEncode(zsString.getBaseStringValue());
                if (string.startsWith(" ") || string.endsWith(" ") || string.startsWith("\n") || string.endsWith("\n")) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.XML_SPACE, "preserve");//No I18N
                }
                xmlElementWriter.addValue(string)
                                .closeElementAndWrite(stringBuilder, ElementNameConstants.T);
            } else {
                int index = 0;
                for (RichStringProperties property : zsString.getProperties()) {
                    int propertyStartIndex = property.getStartIndex();
                    int propertyEndIndex = property.getEndIndex();
                    if(propertyEndIndex > propertyStartIndex) {
                        propertyEndIndex++;
                    }

                    if (index < propertyStartIndex) {
                        xmlElementWriter.write(stringBuilder, ElementNameConstants.R);
                        final String string = Utility.xmlEncode(zsString.getBaseStringValue().substring(index, propertyStartIndex));
                        if (string.startsWith(" ") || string.endsWith(" ") || string.startsWith("\n") || string.endsWith("\n")) {
                            xmlElementWriter.addAttribute(AttributeNameConstants.XML_SPACE, "preserve");//No I18N
                        }
                        xmlElementWriter.addValue(string)
                                .closeElementAndWrite(stringBuilder, ElementNameConstants.T);
                        xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.R);
                    }
                    if (property.getStyleName() == null) {
                        xmlElementWriter.write(stringBuilder, ElementNameConstants.R);
                        final String string = Utility.xmlEncode(zsString.getBaseStringValue().substring(propertyStartIndex, propertyEndIndex));
                        if (string.startsWith(" ") || string.endsWith(" ") || string.startsWith("\n") || string.endsWith("\n")) {
                            xmlElementWriter.addAttribute(AttributeNameConstants.XML_SPACE, "preserve");//No I18N
                        }
                        xmlElementWriter.addValue(string)
                                .closeElementAndWrite(stringBuilder, ElementNameConstants.T);
                        xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.R);
                    } else if(workbook.getTextStyle(property.getStyleName()) == null) {
                        LOGGER.log(Level.WARNING, "xlsx-writer - writing rich text as normal text, as text-style ({0}) not present in workbook-text-styles", property.getStyleName());
                        xmlElementWriter.write(stringBuilder, ElementNameConstants.R);
                        final String string = Utility.xmlEncode(zsString.getBaseStringValue().substring(propertyStartIndex, propertyEndIndex));
                        if (string.startsWith(" ") || string.endsWith(" ") || string.startsWith("\n") || string.endsWith("\n")) {
                            xmlElementWriter.addAttribute(AttributeNameConstants.XML_SPACE, "preserve");//No I18N
                        }
                        xmlElementWriter.addValue(string)
                                .closeElementAndWrite(stringBuilder, ElementNameConstants.T);
                        xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.R);
                    } else {
                        final StylesBuilder.Font font = StylesBuilder.Font.getInstance(workbook.getTextStyle(property.getStyleName()),workbook, false, false, default_font);
                        if (font == null) {
                            xmlElementWriter.write(stringBuilder, ElementNameConstants.R);
                            final String string = Utility.xmlEncode(zsString.getBaseStringValue().substring(propertyStartIndex, propertyEndIndex));
                            if (string.startsWith(" ") || string.endsWith(" ") || string.startsWith("\n") || string.endsWith("\n")) {
                                xmlElementWriter.addAttribute(AttributeNameConstants.XML_SPACE, "preserve");//No I18N
                            }
                            xmlElementWriter.addValue(string)
                                    .closeElementAndWrite(stringBuilder, ElementNameConstants.T);
                            xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.R);
                        } else {
                            xmlElementWriter.write(stringBuilder, ElementNameConstants.R);
                            xmlElementWriter.write(stringBuilder, ElementNameConstants.RPR);
                            StylesToXML.write_rich_text(stringBuilder, font, false, theme);
                            xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.RPR);
                            final String string = Utility.xmlEncode(zsString.getBaseStringValue().substring(propertyStartIndex, propertyEndIndex));
                            if (string.startsWith(" ") || string.endsWith(" ") || string.startsWith("\n") || string.endsWith("\n")) {
                                xmlElementWriter.addAttribute(AttributeNameConstants.XML_SPACE, "preserve");//No I18N
                            }
                            xmlElementWriter.addValue(string)
                                    .closeElementAndWrite(stringBuilder, ElementNameConstants.T);
                            xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.R);
                        }
                    }
                    index = propertyEndIndex;
                }
                if(index < zsString.getBaseStringValue().length()) {
                    xmlElementWriter.write(stringBuilder, ElementNameConstants.R);
                    final String string = Utility.xmlEncode(zsString.getBaseStringValue().substring(index));
                    if (string.startsWith(" ") || string.endsWith(" ") || string.startsWith("\n") || string.endsWith("\n")) {
                        xmlElementWriter.addAttribute(AttributeNameConstants.XML_SPACE, "preserve");//No I18N
                    }
                    xmlElementWriter.addValue(string)
                            .closeElementAndWrite(stringBuilder, ElementNameConstants.T);
                    xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.R);
                }
            }
            stringBuilder.append(Utility.getCloseTag(ElementNameConstants.SI));
            if(stringBuilder.length() > 40960) {
                this.zipOutputStream.write(stringBuilder.toString().getBytes());
                stringBuilder.setLength(0);
            }
        }
        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.SST));

        this.zipOutputStream.write(stringBuilder.toString().getBytes());
    }

    public static boolean allFeaturesExportable(Workbook workBook, ImageBook imageBook, String resourceKey) {
        //todo add other feature exportable checks.
        try {
//            if (!imageBook.getImages().isEmpty()) {
//                LOGGER.log(Level.OFF, "xlsx-writer not exportable :{0}, {1}", new Object[]{"images", resourceKey});
//                return false;
//            }
            if (!allWorkbookFeaturesExportable(workBook, resourceKey)) {
                return false;
            }
            return true;
        } catch (Exception e) {
            LOGGER.log(Level.OFF, "xlsx-writer exportable check failed",e);
            return false;
        }
    }

    public static boolean allWorkbookFeaturesExportable(Workbook workbook, String resourceKey) {
//        if(!chartExportable(workbook)){
//            LOGGER.log(Level.OFF, "xlsx-writer not exportable :{0}, {1}", new Object[]{"charts", resourceKey});
//            return false;
//        }
//        if (!workbook.getPicklistMap().isEmpty()) {
//            LOGGER.log(Level.OFF, "xlsx-writer not exportable :{0}, {1}", new Object[]{"picklist", resourceKey});
//            return false;
//        }
//        if(!pivotExportable(workbook)){
//            LOGGER.log(Level.OFF, "xlsx-writer not exportable :{0}, {1}", new Object[]{"pivot-tables", resourceKey});
//            return false;
//        }
        if (!allSheetFeaturesExportable(workbook, resourceKey)) {
            return false;
        }
        return true;
    }

    private static boolean allSheetFeaturesExportable(Workbook workbook, String resourceKey) {
        for (Sheet sheet : workbook.getSheetList()) {
//            if(!imageExportable(sheet)){
//                LOGGER.log(Level.OFF, "xlsx-writer not exportable :{0}, {1}", new Object[]{"images", resourceKey});
//                return false;
//            }
//            if(!filterExportable(sheet)){
//                return false;
//            }
//            if(!conditionalFormatsExportable(sheet)){
//                return false;
//            }
//            if(!dataValidationExportable(sheet)){
//                return false;
//            }
//            if (sheet.getSparklinesGroupList() != null && !sheet.getSparklinesGroupList().isEmpty()) {
//                LOGGER.log(Level.OFF, "xlsx-writer not exportable :{0}, {1}", new Object[]{"sparklines", resourceKey});
//                return false;
//            }
//            if(!sheet.getPicklistRangeMap().isEmpty()) {
//                LOGGER.log(Level.OFF, "xlsx-writer not exportable :{0}, {1}", new Object[]{"picklist", resourceKey});
//                return false;
//            }
//            if (sheet.getSheetStyle() != null && sheet.getSheetStyle().getProperty(SheetStyle.Property.TABCOLOR) != null) {
//                LOGGER.log(Level.OFF, "xlsx-writer not exportable :{0}, {1}", new Object[]{"tab-color", resourceKey});
//                return false;
//            }
//            if (sheet.getSheetStyle() != null && "rl-tb".equals(sheet.getSheetStyle().getProperty(SheetStyle.Property.WRITINGMODE))) {
//                LOGGER.log(Level.OFF, "xlsx-writer not exportable :{0}, {1}", new Object[]{"right to left writing mode", resourceKey});
//                return false;
//            }
        }
        return true;
    }

    private static boolean dataValidationExportable(Sheet sheet) {
        return sheet.getContentValidationRanges().isEmpty();
    }

    private static boolean conditionalFormatsExportable(Sheet sheet) {
        return sheet.getConditionalStyleMap().isEmpty();
    }

    private static boolean pivotExportable(Workbook workbook) {
        final List<PivotTable> pivotTables = workbook.getPivotTables();
        if (pivotTables == null) {
            return true;
        }
        return pivotTables.isEmpty();
    }

    private static boolean filterExportable(Sheet sheet) {
        return !sheet.isFilterExist();
    }

    private static boolean imageExportable(Sheet sheet) {
        return sheet.getSheetImages().isEmpty();
    }

    private static boolean chartExportable(Workbook workbook) {
        final Map<String, Map<String, Chart>> chartMap = workbook.getChartsContainer().getChartsMap();
        if(chartMap != null) {
            for (Map<String, Chart> value : chartMap.values()) {
                if (value != null) {
                    if (!value.isEmpty()) {
                        return false;
                    }
                }
            }
        }

        return true;
    }
}
