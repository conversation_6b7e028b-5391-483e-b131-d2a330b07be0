/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.filter.Filter;
import com.adventnet.zoho.websheet.model.filter.FilterCondition;
import com.adventnet.zoho.websheet.model.filter.FilterType;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.filter.operator.FilterLogicalOperator;
import com.adventnet.zoho.websheet.model.filter.operator.FilterOperator;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.xlsxaparser_.AttributeNameConstants;
import com.adventnet.zoho.websheet.model.xlsxaparser_.ElementNameConstants;

import java.util.*;

class AutoFilterToXML {
    private  enum ExcelFilterCategory {
        filters,
        top10,
        customFilters,
        dynamicFilter,
        colorFilter,
        iconFilter
    }

    private static Map<Integer, Map<FilterLogicalOperator, Map<ExcelFilterCategory, List<FilterCondition>>>> getExcelFilterStructure(Filter filter) {
        Map<Integer, Map<FilterLogicalOperator, Map<ExcelFilterCategory, List<FilterCondition>>>> excelFilterStructure = new LinkedHashMap<>();
        while (filter !=  null) {
            FilterLogicalOperator type = filter.getType();
            List<FilterCondition> filterConditionList = new ArrayList<>();

            for (List<FilterCondition> value : filter.getFilterConditions().values()) {
                filterConditionList.addAll(value);
            }

            for(FilterCondition filterCondition: filterConditionList) {
                int colId = filterCondition.getFieldNumber();
                Map<FilterLogicalOperator, Map<ExcelFilterCategory, List<FilterCondition>>> typeMapMap = excelFilterStructure.get(colId);
                if(typeMapMap == null) {
                    typeMapMap = new HashMap<>();
                    excelFilterStructure.put(colId, typeMapMap);
                }

                Map<ExcelFilterCategory, List<FilterCondition>> excelFilterCategoryListMap = typeMapMap.get(type);
                if(excelFilterCategoryListMap == null) {
                    excelFilterCategoryListMap = new LinkedHashMap<>();
                    typeMapMap.put(type, excelFilterCategoryListMap);
                }

                ExcelFilterCategory excelFilterCategory = ExcelFilterCategory.filters;
                FilterOperator operator = filterCondition.getOperator();
                switch (operator) {
                    case EQUALS:
                        excelFilterCategory = ExcelFilterCategory.filters;
                        break;
                    case DOES_NOT_EQUALS:
                    case LESS_THAN:
                    case GREATER_THAN:
                    case LESS_THAN_OR_EQUAL_TO:
                    case GREATER_THAN_OR_EQUAL_TO:
                        excelFilterCategory = ExcelFilterCategory.customFilters;
                        break;
                    case BETWEEN:
                        break;
                    case BEGINS_WITH:
                        break;
                    case CONTAINS:
                        break;
                    case DOES_NOT_CONTAIN:
                        break;
                    case ENDS_WITH:
                        break;
                    case DOES_NOT_BEGIN_WITH:
                        break;
                    case DOES_NOT_END_WITH:
                        break;
                    case EMPTY:
                        break;
                    case NOT_EMPTY:
                        break;
                    case MATCHES:
                        break;
                    case DOES_NOT_MATCH:
                        break;
                    case TOP_PERCENT:
                        break;
                    case BOTTOM_PERCENT:
                        break;
                    case TOP_VALUES:
                    case BOTTOM_VALUES:
                        excelFilterCategory = ExcelFilterCategory.top10;
                        break;
                    case NONE:
                        break;
                }
                if(filterCondition.isColorFilter()) {
                    excelFilterCategory = ExcelFilterCategory.colorFilter;
                }

                List<FilterCondition> filterConditions = excelFilterCategoryListMap.get(excelFilterCategory);
                if(filterConditions == null) {
                    filterConditions = new ArrayList<>();
                    excelFilterCategoryListMap.put(excelFilterCategory, filterConditions);
                }

                filterConditions.add(filterCondition);
            }
            filter = filter.getChildFilter();
        }
        return excelFilterStructure;
    }

    static void write_autoFilter(StringBuilder stringBuilder, FilterView filterView, ZSTheme theme, StylesBuilder dxfBuilder) {
        if(filterView != null) {
            final String ref = Utility.toString(new DataRange(null, filterView.getStartRowIndex(), filterView.getStartColumnIndex(), filterView.getEndRowIndex(), filterView.getEndColumnIndex()), true, true);
            Filter filter = filterView.getFilter();
            Map<Integer, Map<FilterLogicalOperator, Map<ExcelFilterCategory, List<FilterCondition>>>> excelFilterStructure = getExcelFilterStructure(filter);

            stringBuilder.append(Utility.getTag(ElementNameConstants.AUTOFILTER, (new String[]{AttributeNameConstants.REF}), (new String[]{ref}), false));
            excelFilterStructure.forEach((k, v) -> {
                stringBuilder.append(Utility.getTag(ElementNameConstants.FILTERCOLUMN, (new String[]{AttributeNameConstants.COLD}), (new String[]{k.toString()}), false));
                v.forEach((filterType,excelFilterCategoryListMap) -> {
                    excelFilterCategoryListMap.forEach((excelFilterCategory, filterConditionList) -> {
                        switch (excelFilterCategory) {
                            case filters:
                                stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.FILTERS));
                                filterConditionList.forEach(filterCondition -> {
                                    stringBuilder.append(Utility.getTag(ElementNameConstants.FILTER,
                                            (new String[]{AttributeNameConstants.VAL}),
                                            (new String[]{Utility.xmlEncode(filterCondition.getValue(theme))}),
                                            true));
                                });
                                stringBuilder.append(Utility.getCloseTag(ElementNameConstants.FILTERS));
                                break;
                            case top10:
                                filterConditionList.forEach(filterCondition -> {
                                    List<String> keys = new ArrayList<>();
                                    List<String> values = new ArrayList<>();
                                    if(filterCondition.getOperator().toString().equals("bottom values")) {
                                        keys.add(AttributeNameConstants.TOP);
                                        values.add("0");
                                    }
                                    keys.add(AttributeNameConstants.VAL);
                                    values.add(filterCondition.getValue(theme));

                                    stringBuilder.append(Utility.getTag(ElementNameConstants.TOP10,
                                            (keys.toArray(new String[]{})),
                                            (values.toArray(new String[]{})),
                                            true));
                                });
                                break;
                            case customFilters:
                                List<String> keys = new ArrayList<>();
                                List<String> values = new ArrayList<>();
                                if(filterType == FilterLogicalOperator.AND && filterConditionList.size() > 1) {
                                    keys.add(AttributeNameConstants.AND);
                                    values.add("1");
                                }

                                stringBuilder.append(Utility.getTag(ElementNameConstants.CUSTOMFILTERS,
                                        (keys.toArray(new String[]{})),
                                        (values.toArray(new String[]{})),
                                        false));
                                filterConditionList.forEach(filterCondition -> {
                                    String operator = "equal";//No I18N
                                    switch(filterCondition.getOperator()) {
                                        case EQUALS:
                                            break;
                                        case DOES_NOT_EQUALS:
                                            operator = "notEqual";//No I18N
                                            break;
                                        case LESS_THAN:
                                            operator = "lessThan";//No I18N
                                            break;
                                        case GREATER_THAN:
                                            operator = "greaterThan";//No I18N
                                            break;
                                        case LESS_THAN_OR_EQUAL_TO:
                                            operator = "lessThanOrEqual";//No I18N
                                            break;
                                        case GREATER_THAN_OR_EQUAL_TO:
                                            operator = "greaterThanOrEqual";//No I18N
                                            break;
                                        case BETWEEN:
                                            break;
                                        case BEGINS_WITH:
                                            break;
                                        case CONTAINS:
                                            break;
                                        case DOES_NOT_CONTAIN:
                                            break;
                                        case ENDS_WITH:
                                            break;
                                        case DOES_NOT_BEGIN_WITH:
                                            break;
                                        case DOES_NOT_END_WITH:
                                            break;
                                        case EMPTY:
                                            break;
                                        case NOT_EMPTY:
                                            break;
                                        case MATCHES:
                                            break;
                                        case DOES_NOT_MATCH:
                                            break;
                                        case TOP_PERCENT:
                                            break;
                                        case TOP_VALUES:
                                            break;
                                        case BOTTOM_PERCENT:
                                            break;
                                        case BOTTOM_VALUES:
                                            break;
                                        case NONE:
                                            break;
                                    }

                                    stringBuilder.append(Utility.getTag(ElementNameConstants.CUSTOMFILTER,
                                            (new String[]{AttributeNameConstants.OPERATOR,AttributeNameConstants.VAL}),
                                            (new String[]{operator, filterCondition.getValue(theme)}),
                                            true));
                                });
                                stringBuilder.append(Utility.getCloseTag(ElementNameConstants.CUSTOMFILTERS));
                                break;
                            case dynamicFilter:
                                //todo:
                                break;
                            case colorFilter:
//                                todo:

                                filterConditionList.forEach(filterCondition -> {
                                    CellStyle cellStyle = new CellStyle();
                                    ZSColor color = (ZSColor) filterCondition.getValue();
                                    boolean isFontColor = filterCondition.getFilterType() == FilterType.FILTER_BY_FONT_COLOR;
                                    cellStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, color);

                                    Integer dxfId = dxfBuilder.getXfIndex(cellStyle, 0, false, false);

                                    List<String> keys_temp = new ArrayList<>();
                                    List<String> values_temp = new ArrayList<>();

                                    if(isFontColor) {
                                        keys_temp.add(AttributeNameConstants.CELLCOLOR);
                                        values_temp.add("0");
                                    }
                                    keys_temp.add(AttributeNameConstants.DXFID);
                                    values_temp.add(dxfId.toString());

                                    stringBuilder.append(Utility.getTag(ElementNameConstants.COLORFILTER,
                                            (keys_temp.toArray(new String[]{})),
                                            (values_temp.toArray(new String[]{})),
                                            true));

                                });
                                break;
                            case iconFilter:
                                //todo:
                                break;
                        }
                    });
                });
                stringBuilder.append(Utility.getCloseTag(ElementNameConstants.FILTERCOLUMN));
            });
            stringBuilder.append(Utility.getCloseTag(ElementNameConstants.AUTOFILTER));
        }
    }
}
