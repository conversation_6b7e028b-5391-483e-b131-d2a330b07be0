/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx.pivotTable;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.pivot.DataPivotUniqueContentDetails;
import com.adventnet.zoho.websheet.model.pivot.PivotField;
import com.adventnet.zoho.websheet.model.pivot.PivotUtil;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.writer.xlsx.Utility;
import com.adventnet.zoho.websheet.model.writer.xlsx.WorkbookToXML;
import com.adventnet.zoho.websheet.model.writer.xlsx.XmlElementWriter;
import com.adventnet.zoho.websheet.model.xlsxaparser_.AttributeNameConstants;
import com.adventnet.zoho.websheet.model.xlsxaparser_.ElementNameConstants;
import com.singularsys.jep.EvaluationException;

import java.io.IOException;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;


public class PivotCacheDefinitionToXML {
    private final OutputStream outputStream;

    public PivotCacheDefinitionToXML(OutputStream outputStream) {
        this.outputStream = outputStream;
    }

    private final Map<Integer, String> cacheFieldNameMap = new HashMap<>();

    public void write_pivotCacheDefinition(Workbook workbook, DataRange pivotCellRange, String sourceName, Integer cacheId, List<PivotField> orderedPivotFields, Map<String, String> changedFieldNames) throws IOException {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        StringBuilder stringBuilder = new StringBuilder(WorkbookToXML.XML_VERSION_ENCODING);
        xmlElementWriter.setElementName(ElementNameConstants.PIVOTCACHEDEFINITION)
                .addAttribute(AttributeNameConstants.XMLNS, WorkbookToXML.XMLNS_URL)
                .addAttribute(AttributeNameConstants.XMLNS_R, WorkbookToXML.XMLNS_REL_URL)
                .addAttribute(AttributeNameConstants.CREATEDVERSION, "6")
                .addAttribute(AttributeNameConstants.INVALID, "1")//NO I18N
                .addAttribute(AttributeNameConstants.REFRESH_ON_LOAD, "1")//NO I18N
                .write(stringBuilder);

        xmlElementWriter.setElementName(ElementNameConstants.CACHESOURCE)
                .addAttribute(AttributeNameConstants.TYPE, "worksheet")//No I18N
                .write(stringBuilder);

        if (sourceName != null) {
            xmlElementWriter.setElementName(ElementNameConstants.WORKSHEETSOURCE)
                    .addAttribute(AttributeNameConstants.NAME, sourceName)
                    .closeElement()
                    .write(stringBuilder);
        } else {
            xmlElementWriter.setElementName(ElementNameConstants.WORKSHEETSOURCE)
                    .addAttribute(AttributeNameConstants.REF, pivotCellRange.getRangeString())
                    .addAttribute(AttributeNameConstants.SHEET, workbook.getSheetByAssociatedName(pivotCellRange.getAssociatedSheetName()).getName())
                    .closeElement()
                    .write(stringBuilder);
        }

        xmlElementWriter.setElementName(ElementNameConstants.CACHESOURCE)
                .closeElement()
                .write(stringBuilder);

        xmlElementWriter.setElementName(ElementNameConstants.CACHEFIELDS)
                .addAttribute(AttributeNameConstants.COUNT, String.valueOf(orderedPivotFields.size()))
                .write(stringBuilder);

        this.outputStream.write(stringBuilder.toString().getBytes());
        stringBuilder = new StringBuilder();

        int index = 0;
        List<String> uniqueFieldNameList = new ArrayList<>();
        Sheet sheet = workbook.getSheetByAssociatedName(pivotCellRange.getAssociatedSheetName());
        for(PivotField pf : orderedPivotFields)
        {
            String fieldName;
            int fieldIndex = pf.getColIndex() - pivotCellRange.getStartColIndex();
            if(pf.getOrientation() != null && fieldIndex < uniqueFieldNameList.size())
            {
                fieldName = uniqueFieldNameList.get(fieldIndex);
                if(pf.getPeriodType() != null)
                {
                    switch (pf.getPeriodType()) {
                        case MONTH:
                            fieldName = "Months ("+ fieldName+")"; //No I18N
                            break;
                        case QUARTER:
                            fieldName = "Quarters ("+ fieldName+")"; //No I18N
                            break;
                        case YEAR:
                            fieldName = "Years ("+ fieldName+")"; //No I18N
                            break;
                        case HOUR:
                            fieldName = "Hours ("+ fieldName+")"; //No I18N
                            break;
                        case MINUTE:
                            fieldName = "Minutes ("+ fieldName+")"; //No I18N
                            break;
                        case SECOND:
                            fieldName = "Seconds ("+ fieldName+")"; //No I18N
                            break;
                    }
                }
            } else {
                Cell cell = sheet.getCell(pivotCellRange.getStartRowIndex(), pivotCellRange.getStartColIndex() + index);
                fieldName = cell.getContent();
            }

            int counter = 1;
            String suffix = "";
            while(uniqueFieldNameList.contains(fieldName+suffix)) {
                counter++;
                suffix = String.valueOf(counter);
            }
            fieldName = fieldName+suffix;
            uniqueFieldNameList.add(fieldName);
            if(pf.getSourceFieldName() != null && !pf.getSourceFieldName().equals(fieldName)) {
                changedFieldNames.put(pf.getSourceFieldName(), fieldName);
            }
            if(pf.getOrientation() == null)
            {
                xmlElementWriter.setElementName(ElementNameConstants.CACHEFIELD)
                        .addAttribute(AttributeNameConstants.NAME, Utility.xmlEncode(fieldName))
                        .write(stringBuilder);
                xmlElementWriter.closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.SHAREDITEMS);
                xmlElementWriter.setElementName(ElementNameConstants.CACHEFIELD)
                        .closeElement()
                        .write(stringBuilder);
                this.outputStream.write(stringBuilder.toString().getBytes());
                stringBuilder = new StringBuilder();
            }
            else
            {
                writeCacheField(pf, fieldName, pivotCellRange.getStartColIndex(), workbook, index < pivotCellRange.getColSize());
            }
            index++;
        }
        //////////// END OF GANESH //////////////////




        xmlElementWriter.setElementName(ElementNameConstants.CACHEFIELDS)
                .closeElement()
                .write(stringBuilder);

        xmlElementWriter.setElementName(ElementNameConstants.EXTLST).write(stringBuilder)
                .setElementName(ElementNameConstants.EXT)
                .addAttribute(AttributeNameConstants.URI, "{725AE2AE-9491-48be-B2B4-4EB974FC3084}") //NO I18N
                .addAttribute(AttributeNameConstants.XMLNS_X14, "http://schemas.microsoft.com/office/spreadsheetml/2009/9/main") //NO I18N
                .write(stringBuilder)
                .setElementName(ElementNameConstants.X14_PIVOTCACHEDEFINITION)
                .addAttribute(AttributeNameConstants.PIVOTCACHEID, cacheId.toString())
                .closeEmptyElement().write(stringBuilder)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.EXT)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.EXTLST);

        xmlElementWriter.setElementName(ElementNameConstants.PIVOTCACHEDEFINITION)
                .closeElement()
                .write(stringBuilder);

        this.outputStream.write(stringBuilder.toString().getBytes());
    }

    private void writeCacheField(PivotField pv, String fieldName, int startColIndex, Workbook workbook, boolean isFirstField) throws IOException {
        StringBuilder cacheFieldStringBuilder = new StringBuilder();
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

        {
            if(!isFirstField)
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.DATABASEFIELD,  "0");
            }
            xmlElementWriter
                    .addAttribute(AttributeNameConstants.NAME, Utility.xmlEncode(fieldName));
            xmlElementWriter.write(cacheFieldStringBuilder, ElementNameConstants.CACHEFIELD);
        }

        if (isFirstField && pv.getOrientation() != PivotField.Orientation.DATA) {
            xmlElementWriter.setElementName(ElementNameConstants.SHAREDITEMS);

            EnumSet<PivotField.ContentType> contentTypes = pv.getContentTypes();
            boolean containsLongText = contentTypes.contains(PivotField.ContentType.LONG_TEXT);
            boolean containsText = contentTypes.contains(PivotField.ContentType.STRING);
            boolean containsDate = contentTypes.contains(PivotField.ContentType.DATE);
            boolean containsInteger = contentTypes.contains(PivotField.ContentType.INTEGER);
            boolean containsNumeric =  contentTypes.contains(PivotField.ContentType.NUMBER);
            boolean containsBlank = contentTypes.contains(PivotField.ContentType.BLANK);


            if(containsInteger && !containsNumeric && !containsDate)
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.CONTAINS_INTEGER, Utility.ONE_STRING);
                xmlElementWriter.addAttribute(AttributeNameConstants.CONTAINS_NUMBER, Utility.ONE_STRING);
            }

            if(containsNumeric && !containsDate)
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.CONTAINS_NUMBER, Utility.ONE_STRING);
            }

            if(containsDate)
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.CONTAINS_DATE, Utility.ONE_STRING);
            }

            if(containsBlank)
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.CONTAINS_BLANK, Utility.ONE_STRING);
            }

            if(containsLongText)
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.LONG_TEXT, Utility.ONE_STRING);
            }

            if(!containsText && !containsLongText)
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.CONTAINS_STRING, Utility.ZERO_STRING);
            }
            // A & B || A & C || B & C
            // (A & ( B | C )) || ( B & C)
            if(( (containsInteger || containsNumeric) && (containsDate || containsText || containsLongText) ) || ((containsDate) && (containsText || containsLongText)))
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.CONTAINS_MIXED_TYPES, Utility.ONE_STRING);
            }

            if(!(containsNumeric || containsInteger || containsLongText || containsText))
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.CONTAINS_NON_DATE, Utility.ZERO_STRING);
            }

            if(!containsText && !containsLongText && !containsBlank)
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.CONTAINS_SEMI_MIXED_TYPES, Utility.ZERO_STRING);
            }

            xmlElementWriter.write(cacheFieldStringBuilder);

            boolean writeSharedItems = (pv.getPivotGroups() == null || pv.getPivotGroups().getDateStart() != null) && (pv.getPeriodType() == null || pv.getPeriodType() == CalendarValue.PeriodType.NONE);
            if(writeSharedItems) {
                StringBuilder sharedItemsStringBuilder = new StringBuilder();

                for (String pvContent : pv.getPivotLevel().getPivotMembers().keySet()) {
                    DataPivotUniqueContentDetails sourceContent = pv.getDataPivotUniqueContentDetails(pvContent);
                    if (sourceContent != null) {
                        String sharedItemType;
                        String valueString;
                        switch (sourceContent.type) {
                            case FLOAT:
                            case SCIENTIFIC:
                            case FRACTION:
                            case PERCENTAGE:
                            case CURRENCY:
                                valueString = sourceContent.value.getValueString(workbook.getSpreadsheetSettings());
                                sharedItemType = "n";//No I18N
                                break;
                            case DATE:
                            case TIME:
                            case DATETIME:
                                sharedItemType = "d";//No I18N
                                DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");//No I18N
                                try {
                                    valueString = df.format(FunctionUtil.objectToDate(sourceContent.value.getValue()));
                                    //todo this value may be incorrect.
                                } catch (EvaluationException evaluationException) {
                                    //todo log this.
                                    sharedItemType = "n";//No I18N
                                    valueString = sourceContent.value.getValueString(workbook.getSpreadsheetSettings());
                                }
                                break;
                            case BOOLEAN:
                            case ERROR:
                            case UNDEFINED:
                            case STRING:
                            default:
                                valueString = sourceContent.value.getValueString(workbook.getSpreadsheetSettings());
                                if (valueString.isEmpty()) {
                                    sharedItemType = "m";//No I18N
                                } else {
                                    sharedItemType = "s";//No I18N
                                }

                        }
                        xmlElementWriter.setElementName(sharedItemType)
                                .addAttribute(AttributeNameConstants.V, Utility.xmlEncode(valueString))
                                .closeElement()
                                .write(sharedItemsStringBuilder);
                    }
                }
                cacheFieldStringBuilder.append(sharedItemsStringBuilder);
            }
                //xmlElementWriter.closeEmptyElement();

            xmlElementWriter.setElementName(ElementNameConstants.SHAREDITEMS)
                    .closeElement()
                    .write(cacheFieldStringBuilder);
        }

        if((pv.getPivotGroups() != null && pv.getPivotGroups().getDateStart() == null) || (pv.getUsedHierarchy() != -1 && pv.getUsedHierarchy() != 0)){
            writeFieldGroup(pv, startColIndex, cacheFieldStringBuilder);
        }

        xmlElementWriter.setElementName(ElementNameConstants.CACHEFIELD)
                .closeElement()
                .write(cacheFieldStringBuilder);

        this.outputStream.write(cacheFieldStringBuilder.toString().getBytes());
    }

    private void writeFieldGroup(PivotField pivotField, int startColIndex, StringBuilder cacheFieldStringBuilder) throws IOException {
            XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
            xmlElementWriter.setElementName(ElementNameConstants.FIELDGROUP);

                xmlElementWriter.addAttribute(AttributeNameConstants.BASE, String.valueOf(pivotField.getColIndex() - startColIndex));
                    xmlElementWriter.write(cacheFieldStringBuilder)
                    .setElementName(ElementNameConstants.RANGEPR);

            List<String> fieldList = new ArrayList<>();
            if(pivotField.getColRangeType() == PivotUtil.FieldType.Tn && pivotField.getColInfo() != null)
            {

                double minVal = pivotField.getColInfo().getDouble("minVal"); //No I18N
                double maxVal = pivotField.getColInfo().getDouble("maxVal"); //No I18N
                xmlElementWriter.addAttribute(AttributeNameConstants.STARTNUM, String.valueOf(minVal));
                xmlElementWriter.addAttribute(AttributeNameConstants.ENDNUM, String.valueOf(maxVal));

                xmlElementWriter.addAttribute(AttributeNameConstants.GROUPINTERVAL, String.valueOf(pivotField.getPivotGroups().getStep()))
                        .closeElement()
                        .write(cacheFieldStringBuilder);

                NumberFormat nf = new DecimalFormat("0.##"); //No I18N
                fieldList.add("&lt;"+ nf.format(minVal)); //No I18N
                for(String content : pivotField.getPivotLevel().getPivotMembers().keySet()) {
                    if(pivotField.getDataPivotUniqueContentDetails(content) != null) {
                        content = content.replace("'", ""); //No I18N
                        String[] range = content.split("-"); //No I18N
                        if(range.length > 1) {
                            double startValue = Double.parseDouble(range[0]);
                            double endValue = Double.parseDouble(range[1]);
                            fieldList.add(nf.format(startValue) + "-" + nf.format(endValue)); //No I18N
                        }
                    }
                }
                fieldList.add("&gt;" + nf.format((maxVal + 1)));
            }
            else if (pivotField.getColRangeType() == PivotUtil.FieldType.Td)
            {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"); //No I18N
                Date startDate = null;
                Date endDate = null;
                if(pivotField.getPivotGroups() != null) {
                    startDate = pivotField.getPivotGroups().getDateStart();
                    endDate = pivotField.getPivotGroups().getDateEnd();


                    xmlElementWriter.addAttribute(AttributeNameConstants.GROUPBY, String.valueOf(pivotField.getPeriodType()).toLowerCase() + "s") //No I18N
                            .addAttribute(AttributeNameConstants.STARTDATE, formatter.format(startDate))
                            .addAttribute(AttributeNameConstants.ENDDATE, formatter.format(endDate));

                    xmlElementWriter.closeElement()
                            .write(cacheFieldStringBuilder);
                }

                formatter = new SimpleDateFormat("MM-dd-yyyy"); //No I18N

                if(startDate != null) {
                    fieldList.add("&lt;" + formatter.format(startDate)); //No I18N
                }
                if(pivotField.getPeriodType() != CalendarValue.PeriodType.YEAR) {
                    fieldList.addAll(pivotField.getPeriodType().getZStoXLSXEquivalents().values());
                }
                else {
                    for (String content : pivotField.getPivotLevel().getPivotMembers().keySet()) {
                        if (pivotField.getDataPivotUniqueContentDetails(content) != null) {
                            fieldList.add(content);
                        }
                    }
                }
                if(endDate != null)
                {
                    fieldList.add("&gt;" + formatter.format(endDate)); //No I18N
                }
            }

            xmlElementWriter.setElementName(ElementNameConstants.GROUP_ITEMS)
                    .addAttribute(AttributeNameConstants.COUNT, String.valueOf(fieldList.size()))
                    .write(cacheFieldStringBuilder);


            for(String value : fieldList)
            {
                xmlElementWriter.setElementName(ElementNameConstants.S)
                        .addAttribute(AttributeNameConstants.V, value)
                        .closeEmptyElement().write(cacheFieldStringBuilder);
            }

           xmlElementWriter.closeElementAndWrite(cacheFieldStringBuilder, ElementNameConstants.GROUP_ITEMS);
           xmlElementWriter.closeElementAndWrite(cacheFieldStringBuilder, ElementNameConstants.FIELDGROUP);
    }

}
//todo: handle multiple types for a single column/field.
//todo: handle empty values in column/field.