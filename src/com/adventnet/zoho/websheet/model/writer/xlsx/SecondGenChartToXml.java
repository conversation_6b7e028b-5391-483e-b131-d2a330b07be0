/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.ext.functions.RandBetween;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.writer.xlsx.chart.ChartSeriesTransformer;
import com.adventnet.zoho.websheet.model.writer.xlsx.chart.SecondGenChartSeriesTransformer;
import com.adventnet.zoho.websheet.model.writer.xlsx.image.DrawingProperties;
import com.adventnet.zoho.websheet.model.xlsxaparser_.AttributeNameConstants;
import com.adventnet.zoho.websheet.model.xlsxaparser_.ElementNameConstants;
import com.zoho.sheet.chart.Chart;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipOutputStream;

public class SecondGenChartToXml {
    private static final Logger LOGGER = Logger.getLogger(SecondGenChartToXml.class.getName());
    private final Chart chart;
    private final ZipOutputStream zipOutputStream;

    private final SecondGenChartSeriesTransformer chartSeriesTransformer;
    int idxVal = 0, orderVal = 0;

    public SecondGenChartToXml(Chart chart, ZipOutputStream zipOutputStream) {
        this.chart = chart;
        this.zipOutputStream = zipOutputStream;
        this.chartSeriesTransformer = new SecondGenChartSeriesTransformer(chart);
    }

    public void write() throws IOException {

        StringBuilder stringBuilder = new StringBuilder(WorkbookToXML.XML_VERSION_ENCODING);
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

        xmlElementWriter.setElementName(ElementNameConstants.C_CHART_SPACE)
                .addAttribute(AttributeNameConstants.XMLNS_A, "http://schemas.openxmlformats.org/drawingml/2006/main")//No I18N
                .addAttribute(AttributeNameConstants.XMLNS_C, "http://schemas.openxmlformats.org/drawingml/2006/chart")//No I18N
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_CHART)
                .write(stringBuilder);

        if (chart.getChartTitleObject() != null && chart.getChartTitleObject().has("text") && chart.getChartTitleObject().get("text") != null) {
            String title = chart.getChartTitleObject().getString("text");
            try {
                title = URLDecoder.decode(title, "UTF-8");
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "input string: "+ title, e);
            }
            title = Utility.xmlEncode(unescape(title));
            xmlElementWriter.write(stringBuilder, ElementNameConstants.C_TITLE)
                    .write(stringBuilder, ElementNameConstants.C_TX)
                    .write(stringBuilder, ElementNameConstants.C_RICH)
                    .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.A_BODYPR)
                    .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.A_LSTSTYLE)
                    .write(stringBuilder, ElementNameConstants.A_P)
                    .write(stringBuilder, ElementNameConstants.A_R)
                    .addValue(title)
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.A_T)
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.A_R)
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.A_P)
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_RICH)
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_TX)
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_TITLE);
        }

        xmlElementWriter.setElementName(ElementNameConstants.C_PLOTAREA)
                .write(stringBuilder);
        final String catAxId = String.valueOf(RandBetween.randBetween(10000000, *********));
        final String valAxId = String.valueOf(RandBetween.randBetween(10000000, *********));
        final String majorType = this.chart.getMajorType();
        final List<List<DataRange>> series = this.chartSeriesTransformer.getSeries();
        final List<DataRange> category = this.chartSeriesTransformer.getCategory();
        boolean isWriteAxElement = false;
        if(majorType.contains("COMBOCHART") && !series.isEmpty()) {
            final String[] comboChartTypes = this.chart.getComboChartTypes();
            if (series.size() + category.size() == comboChartTypes.length) {
                String comboChartType = comboChartTypes[0];
                List<List<DataRange>> alteredSeriesList = new ArrayList<>();
                alteredSeriesList.add(series.get(0));
                for (int i = 1; i < series.size(); i++) {
                    if (comboChartType.equals(comboChartTypes[i])) {
                        alteredSeriesList.add(series.get(i));
                        continue;
                    }
                    if (!(comboChartType.contains("PIE") || comboChartType.contains("DOUGHNUT"))) {
                        isWriteAxElement = true;
                    }
                    writeChartElement(comboChartType, stringBuilder, catAxId, valAxId, category, alteredSeriesList);
                    comboChartType = comboChartTypes[i];
                    alteredSeriesList.clear();
                    alteredSeriesList.add(series.get(i));
                }
                if (!(comboChartType.contains("PIE") || comboChartType.contains("DOUGHNUT"))) {
                    isWriteAxElement = true;
                }
                writeChartElement(comboChartType, stringBuilder, catAxId, valAxId, category, alteredSeriesList);
            } else {
                isWriteAxElement = true;
                writeChartElement(majorType, stringBuilder, catAxId, valAxId, category, series);
            }
        } else {
            isWriteAxElement = true;
            writeChartElement(majorType, stringBuilder, catAxId, valAxId, category, series);
        }
        if (majorType.contains("COMBOCHART") ? isWriteAxElement : !(majorType.contains("PIE") || majorType.contains("DOUGHNUT"))) {
            if (majorType.contains("CANDLESTICK") || majorType.contains("OHLCCHART")) {
                writeDateAxElement(stringBuilder, catAxId, valAxId);
            } else {
                writeCatAxElement(stringBuilder, catAxId, valAxId);
            }
            writeValAxElement(stringBuilder, catAxId, valAxId);
        }
        xmlElementWriter.setElementName(ElementNameConstants.C_PLOTAREA)
                .closeElement().write(stringBuilder);
        writeLegendElement(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_PLOTVISONLY)
                .addAttribute(AttributeNameConstants.VAL, "1")
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_DISPBLAKSAS)
                .addAttribute(AttributeNameConstants.VAL, "gap")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_CHART)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_CHART_SPACE)
                .closeElement().write(stringBuilder);

        this.zipOutputStream.write(stringBuilder.toString().getBytes());
    }

    public static String unescape(String s) {
        StringBuffer buf = new StringBuffer();
        Matcher m = Pattern.compile("%u([0-9A-Fa-f]{4})|%([0-9A-Fa-f]{2})").matcher(s);
        while (m.find()) {
            m.appendReplacement(buf, new String(new int[]{Integer.parseInt(m.group(m.start(1) != -1 ? 1 : 2), 16)}, 0, 1));
        }
        return m.appendTail(buf).toString();
    }

    private void writeChartElement(String majorChartType, StringBuilder stringBuilder, String catAxId, String valAxId, List<DataRange> category, List<List<DataRange>> series) {
        if(majorChartType.contains("SPIDERWEB")) {
            writeSpiderWebChart(stringBuilder, ElementNameConstants.C_RADARCHART, catAxId, valAxId, category, series);
        } else if(majorChartType.contains("COL")) {
            if (majorChartType.contains("3D")) {
                writeBarChart(majorChartType, stringBuilder, ElementNameConstants.C_BAR3DCHART, "col", catAxId, valAxId, category, series);//No I18N
            } else {
                writeBarChart(majorChartType, stringBuilder, ElementNameConstants.C_BARCHART, "col", catAxId, valAxId, category, series);//No I18N
            }
        } else if(majorChartType.contains("BAR")) {
            if(majorChartType.contains("3D")) {
                writeBarChart(majorChartType, stringBuilder, ElementNameConstants.C_BAR3DCHART, "bar", catAxId, valAxId, category, series);//No I18N
            } else {
                writeBarChart(majorChartType, stringBuilder, ElementNameConstants.C_BARCHART, "bar", catAxId, valAxId, category, series);//No I18N
            }
        } else if(majorChartType.contains("AREA")) {
            if(majorChartType.contains("3D")) {
                writeAreaChart(stringBuilder, ElementNameConstants.C_AREA3DCHART, catAxId, valAxId, category, series, majorChartType);
            } else {
                writeAreaChart(stringBuilder, ElementNameConstants.C_AREACHART, catAxId, valAxId, category, series, majorChartType);
            }
        } else if(majorChartType.contains("PIE")) {
            if (majorChartType.contains("3D")) {
                writePieChart(stringBuilder, ElementNameConstants.C_PIE3DCHART, category, series);
            } else {
                writePieChart(stringBuilder, ElementNameConstants.C_PIECHART, category, series);
            }
        } else if(majorChartType.contains("DOUGHNUT")) {
            writePieChart(stringBuilder, ElementNameConstants.C_DOUGHNUTCHART, category, series);
        }else if(majorChartType.contains("SCATTER")) {//No I18N
            writeScatterChart(stringBuilder, ElementNameConstants.C_SCATTERCHART, catAxId, valAxId, category, series, majorChartType);
        }else if(majorChartType.contains("BUBBLE")) {//No I18N
            writeBubbleChart(stringBuilder, ElementNameConstants.C_BUBBLECHART, catAxId, valAxId, category, series, majorChartType);
        }else if(majorChartType.contains("CANDLESTICK") || majorChartType.contains("OHLCCHART")) {//No I18N
            writeStockChart(stringBuilder, ElementNameConstants.C_STOCKCHART, catAxId, valAxId, category, series);
        } else if(majorChartType.contains("LINE")) {
            if(majorChartType.contains("3D")) {
                writeLineChart(stringBuilder, ElementNameConstants.C_LINE3DCHART, catAxId, valAxId, category, series, majorChartType);
            } else {
                writeLineChart(stringBuilder, ElementNameConstants.C_LINECHART, catAxId, valAxId, category, series, majorChartType);
            }
        } else {
            writeBarChart(majorChartType, stringBuilder, ElementNameConstants.C_BARCHART, "col", catAxId, valAxId, category, series);//No I18N
        }
    }

    private void writeStockChart(StringBuilder stringBuilder, String chartElement, String dateAxId, String valAxId, List<DataRange> category, List<List<DataRange>> series) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter
                .write(stringBuilder, chartElement);
        writeStockChartSerElement(stringBuilder, category, series);
        xmlElementWriter
                .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.C_HILOWLINES)
                .write(stringBuilder, ElementNameConstants.C_UPDOWNBARS)
                .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.C_GAPWIDTH)
                .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.C_UPBARS)
                .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.C_DOWNBARS)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_UPDOWNBARS);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, dateAxId)
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, valAxId)
                .closeElement().write(stringBuilder)
                .setElementName(chartElement)
                .closeElement().write(stringBuilder);
    }

    private void writeScatterChart(StringBuilder stringBuilder, String chartElement, String xAxId, String yAxId, List<DataRange> category, List<List<DataRange>> series, String majorType) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.write(stringBuilder, chartElement);
        xmlElementWriter
                .addAttribute(AttributeNameConstants.VAL, Utility.ZERO_STRING)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_VARYCOLORS);
        writeScatterChartSerElement(stringBuilder, category, series, majorType);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, xAxId)
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, yAxId)
                .closeElement().write(stringBuilder)
                .setElementName(chartElement)
                .closeElement().write(stringBuilder);
    }

    private void writeBubbleChart(StringBuilder stringBuilder, String chartElement, String xAxId, String yAxId, List<DataRange> category, List<List<DataRange>> series, String majorType) {
        if (series.size() < 2) {
            writeScatterChart(stringBuilder, ElementNameConstants.C_SCATTERCHART, xAxId, yAxId, category, series, majorType);
            return;
        }
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.write(stringBuilder, chartElement);
        writeBubbleChartSerElement(stringBuilder, series);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, xAxId)
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, yAxId)
                .closeElement().write(stringBuilder)
                .setElementName(chartElement)
                .closeElement().write(stringBuilder);
    }

    private void writeAreaChart(StringBuilder stringBuilder, String chartElement, String catAxId, String valAxId, List<DataRange> category, List<List<DataRange>> series, String majorChartType) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.write(stringBuilder, chartElement);
        xmlElementWriter
                .addAttribute(AttributeNameConstants.VAL, Utility.ZERO_STRING)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_VARYCOLORS);
        if (majorChartType.contains("PERCENT")) {
            xmlElementWriter.addAttribute(AttributeNameConstants.VAL, "percentStacked")//No I18N
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_GROUPING);
        } else if (majorChartType.contains("STACKED")) {
            xmlElementWriter.addAttribute(AttributeNameConstants.VAL, "stacked")//No I18N
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_GROUPING);
        }
        writeAreaChartSerElement(stringBuilder, category, series);
        if (majorChartType.contains("PERCENT") || majorChartType.contains("STACKED")) {
            xmlElementWriter
                    .addAttribute(AttributeNameConstants.VAL, "100")
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_OVERLAP);
        }
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, catAxId)
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, valAxId)
                .closeElement().write(stringBuilder)
                .setElementName(chartElement)
                .closeElement().write(stringBuilder);
    }

    private void writeAreaChartSerElement(StringBuilder stringBuilder, List<DataRange> cat, List<List<DataRange>> series_list) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

        for (List<DataRange> series : series_list) {
            xmlElementWriter
                    .setElementName(ElementNameConstants.C_SER)
                    .write(stringBuilder)
                    .setElementName(ElementNameConstants.C_IDX)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(idxVal++))
                    .closeElement().write(stringBuilder)
                    .setElementName(ElementNameConstants.C_ORDER)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(orderVal++))
                    .closeElement().write(stringBuilder);
//                                xmlElementWriter.setElementName(ElementNameConstants.C_DLBLS)
//                                        .write(stringBuilder);
//                                xmlElementWriter.setElementName(ElementNameConstants.C_DLBLS)
//                                        .closeElement().write(stringBuilder);
            final DataRange name = this.chartSeriesTransformer.getName(series);
            if(name != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, name);
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .closeElement().write(stringBuilder);
            }
            write_dLbls_element(stringBuilder);
            if(cat != null && !cat.isEmpty()) {
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, cat);
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .closeElement().write(stringBuilder);
            }
            final List<DataRange> value = this.chartSeriesTransformer.getValue(series);
            if(value != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .write(stringBuilder);
                writeNumRefElement(stringBuilder, value);
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .closeElement().write(stringBuilder);
            }
            xmlElementWriter.setElementName(ElementNameConstants.C_SER)
                    .closeElement().write(stringBuilder);
        }
        write_single_series_no_category(stringBuilder,series_list );
    }

    private void writeLineChart(StringBuilder stringBuilder, String chartElement, String catAxId, String valAxId, List<DataRange> category, List<List<DataRange>> series, String majorChartType) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.write(stringBuilder, chartElement);
        xmlElementWriter
                .addAttribute(AttributeNameConstants.VAL, Utility.ZERO_STRING)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_VARYCOLORS);
        writeLineChartSerElement(stringBuilder, category, series, majorChartType);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, catAxId)
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, valAxId)
                .closeElement().write(stringBuilder)
                .setElementName(chartElement)
                .closeElement().write(stringBuilder);
    }

    private void writeSpiderWebChart(StringBuilder stringBuilder, String chartElement, String catAxId, String valAxId, List<DataRange> category, List<List<DataRange>> series) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter
                .write(stringBuilder, chartElement)
                .addAttribute(AttributeNameConstants.VAL, "marker")//No I18N
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_RADARSTYLE);
        xmlElementWriter
                .addAttribute(AttributeNameConstants.VAL, Utility.ZERO_STRING)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_VARYCOLORS);
        writeSerElement(stringBuilder, category, series);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, catAxId)
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, valAxId)
                .closeElement().write(stringBuilder)
                .setElementName(chartElement)
                .closeElement().write(stringBuilder);
    }

    private void writeBarChart(String majorChartType, StringBuilder stringBuilder, String chartElement, String barDir, String catAxId, String valAxId, List<DataRange> category, List<List<DataRange>> series) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter
                .write(stringBuilder, chartElement)
                .addAttribute(AttributeNameConstants.VAL, barDir)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_BARDIR);

        xmlElementWriter
                .addAttribute(AttributeNameConstants.VAL, Utility.ZERO_STRING)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_VARYCOLORS);

        if (majorChartType.contains("PERCENT")) {
            xmlElementWriter.addAttribute(AttributeNameConstants.VAL, "percentStacked")//No I18N
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_GROUPING);
        } else if (majorChartType.contains("STACKED")) {
            xmlElementWriter.addAttribute(AttributeNameConstants.VAL, "stacked")//No I18N
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_GROUPING);
        }
        writeBarSerElement(stringBuilder, category, series);
        if (majorChartType.contains("PERCENT") || majorChartType.contains("STACKED")) {
            xmlElementWriter
                    .addAttribute(AttributeNameConstants.VAL, "100")
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_OVERLAP);
        }
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, catAxId)
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, valAxId)
                .closeElement().write(stringBuilder)
                .setElementName(chartElement)
                .closeElement().write(stringBuilder);
    }

    private void writePieChart(StringBuilder stringBuilder, String chartElement, List<DataRange> category, List<List<DataRange>> series) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.write(stringBuilder, chartElement);
        writeSerElement(stringBuilder, category, series);
        if (chartElement.equals(ElementNameConstants.C_DOUGHNUTCHART)) {
            xmlElementWriter.addAttribute(AttributeNameConstants.VAL, "50")
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_HOLE_SIZE);
        }
        xmlElementWriter.closeElementAndWrite(stringBuilder,chartElement);
    }

    private void writeStockChartSerElement(StringBuilder stringBuilder, List<DataRange> cat, List<List<DataRange>> series_list) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

        for (List<DataRange> series : series_list) {
            xmlElementWriter
                    .setElementName(ElementNameConstants.C_SER)
                    .write(stringBuilder)
                    .setElementName(ElementNameConstants.C_IDX)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(idxVal++))
                    .closeElement().write(stringBuilder)
                    .setElementName(ElementNameConstants.C_ORDER)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(orderVal++))
                    .closeElement().write(stringBuilder);
//                                xmlElementWriter.setElementName(ElementNameConstants.C_DLBLS)
//                                        .write(stringBuilder);
//                                xmlElementWriter.setElementName(ElementNameConstants.C_DLBLS)
//                                        .closeElement().write(stringBuilder);
            final DataRange name = this.chartSeriesTransformer.getName(series);
            if(name != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, name);
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .closeElement().write(stringBuilder);
            }
            write_dLbls_element(stringBuilder);
            xmlElementWriter
                    .write(stringBuilder, ElementNameConstants.C_SPPR)
                    .write(stringBuilder, ElementNameConstants.A_LN)
                    .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.A_NOFILL)
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.A_LN)
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_SPPR)
                    .write(stringBuilder, ElementNameConstants.C_MARKER)
                    .addAttribute(AttributeNameConstants.VAL, "none").closeElementAndWrite(stringBuilder, ElementNameConstants.C_SYMBOL)//No I18N
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_MARKER);
            if(cat != null && !cat.isEmpty()) {
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, cat);
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .closeElement().write(stringBuilder);
            }
            final List<DataRange> value = this.chartSeriesTransformer.getValue(series);
            if(value != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .write(stringBuilder);
                writeNumRefElement(stringBuilder, value);
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .closeElement().write(stringBuilder);
            }
            xmlElementWriter.setElementName(ElementNameConstants.C_SER)
                    .closeElement().write(stringBuilder);
        }
        write_single_series_no_category(stringBuilder,series_list );
    }

    private void writeLineChartSerElement(StringBuilder stringBuilder, List<DataRange> cat, List<List<DataRange>> series_list, String majorChartType) {
        boolean isMarker = false;
        try {
            if (this.chart.getMarkerObject() != null && !this.chart.getMarkerObject().isEmpty()) {
                isMarker = this.chart.getMarkerObject().getJSONObject(0).getBoolean("enabled");
            }
        } catch (Exception e) {

        }
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

        for (List<DataRange> series : series_list) {
            xmlElementWriter
                    .setElementName(ElementNameConstants.C_SER)
                    .write(stringBuilder)
                    .setElementName(ElementNameConstants.C_IDX)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(idxVal++))
                    .closeElement().write(stringBuilder)
                    .setElementName(ElementNameConstants.C_ORDER)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(orderVal++))
                    .closeElement().write(stringBuilder);
//                                xmlElementWriter.setElementName(ElementNameConstants.C_DLBLS)
//                                        .write(stringBuilder);
//                                xmlElementWriter.setElementName(ElementNameConstants.C_DLBLS)
//                                        .closeElement().write(stringBuilder);
            final DataRange name = this.chartSeriesTransformer.getName(series);
            if(name != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, name);
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .closeElement().write(stringBuilder);
            }
            write_dLbls_element(stringBuilder);
            if(!isMarker) {
                xmlElementWriter.write(stringBuilder, ElementNameConstants.C_MARKER)
                        .addAttribute(AttributeNameConstants.VAL, "none").closeElementAndWrite(stringBuilder, ElementNameConstants.C_SYMBOL)//No I18N
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.C_MARKER);
            }
            if(cat != null && !cat.isEmpty()) {
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, cat);
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .closeElement().write(stringBuilder);
            }
            final List<DataRange> value = this.chartSeriesTransformer.getValue(series);
            if(value != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .write(stringBuilder);
                writeNumRefElement(stringBuilder, value);
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .closeElement().write(stringBuilder);
            }
            if(!majorChartType.contains("SPLINE")){//No I18N
                xmlElementWriter.addAttribute(AttributeNameConstants.VAL, "0")
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.C_SMOOTH);
            }
            xmlElementWriter.setElementName(ElementNameConstants.C_SER)
                    .closeElement().write(stringBuilder);
        }
        write_single_series_no_category(stringBuilder, series_list);
    }

    private void write_single_series_no_category(StringBuilder stringBuilder, List<List<DataRange>> series_list) {
        if (!series_list.isEmpty()) {
            return;
        }
        if(this.chart.getDataRange().isEmpty()) {
            return;
        }
        List<DataRange> dataRanges = new ArrayList<>();
        for (Range range : this.chart.getDataRange()) {
            dataRanges.add(new DataRange(
                    range.getSheet().getName(),
                    range.getStartRowIndex(),
                    range.getStartColIndex(),
                    range.getEndRowIndex() == com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFROWS - 1 ? Math.min(range.getSheet().getUsedRowIndex(),range.getEndRowIndex()) : range.getEndRowIndex(),
                    range.getEndColIndex() == com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFCOLS - 1 ? Math.min(range.getSheet().getUsedColumnIndex(),range.getEndColIndex()) : range.getEndColIndex()));
        }

        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter
                .setElementName(ElementNameConstants.C_SER)
                .write(stringBuilder)
                .setElementName(ElementNameConstants.C_IDX)
                .addAttribute(AttributeNameConstants.VAL, String.valueOf(idxVal++))
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_ORDER)
                .addAttribute(AttributeNameConstants.VAL, String.valueOf(orderVal++))
                .closeElement().write(stringBuilder);
        final DataRange name = this.chartSeriesTransformer.getName(dataRanges);
        if (name != null) {
            xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                    .write(stringBuilder);
            writeStrRefElement(stringBuilder, name);
            xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                    .closeElement().write(stringBuilder);
        }
        write_dLbls_element(stringBuilder);

        xmlElementWriter.write(stringBuilder, ElementNameConstants.C_MARKER)
                .addAttribute(AttributeNameConstants.VAL, "none").closeElementAndWrite(stringBuilder, ElementNameConstants.C_SYMBOL)//No I18N
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_MARKER);

        final List<DataRange> value = this.chartSeriesTransformer.getValue(dataRanges);
        if (value != null) {
            xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                    .write(stringBuilder);
            writeNumRefElement(stringBuilder, value);
            xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                    .closeElement().write(stringBuilder);
        }
        xmlElementWriter.setElementName(ElementNameConstants.C_SER)
                .closeElement().write(stringBuilder);
    }


    private void writeSerElement(StringBuilder stringBuilder, List<DataRange> cat, List<List<DataRange>> series_list) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

        for (List<DataRange> series : series_list) {
            xmlElementWriter
                    .setElementName(ElementNameConstants.C_SER)
                    .write(stringBuilder)
                    .setElementName(ElementNameConstants.C_IDX)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(idxVal++))
                    .closeElement().write(stringBuilder)
                    .setElementName(ElementNameConstants.C_ORDER)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(orderVal++))
                    .closeElement().write(stringBuilder);
            final DataRange name = this.chartSeriesTransformer.getName(series);
            if(name != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, name);
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .closeElement().write(stringBuilder);
            }
            write_dLbls_element(stringBuilder);
            if(cat != null && !cat.isEmpty()) {
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, cat);
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .closeElement().write(stringBuilder);
            }
            final List<DataRange> value = this.chartSeriesTransformer.getValue(series);
            if(value != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .write(stringBuilder);
                writeNumRefElement(stringBuilder, value);
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .closeElement().write(stringBuilder);
            }
            xmlElementWriter.setElementName(ElementNameConstants.C_SER)
                    .closeElement().write(stringBuilder);
        }
        write_single_series_no_category(stringBuilder,series_list );
    }

    private void writeBarSerElement(StringBuilder stringBuilder, List<DataRange> cat, List<List<DataRange>> series_list) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

        for (List<DataRange> series : series_list) {
            xmlElementWriter
                    .setElementName(ElementNameConstants.C_SER)
                    .write(stringBuilder)
                    .setElementName(ElementNameConstants.C_IDX)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(idxVal++))
                    .closeElement().write(stringBuilder)
                    .setElementName(ElementNameConstants.C_ORDER)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(orderVal++))
                    .closeElement().write(stringBuilder);
            final DataRange name = this.chartSeriesTransformer.getName(series);
            if(name != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, name);
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .closeElement().write(stringBuilder);
            }
            xmlElementWriter.addAttribute(AttributeNameConstants.VAL, Utility.ZERO_STRING)
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_INVERT_IF_NEGATIVE);
            write_dLbls_element(stringBuilder);
            if(cat != null && !cat.isEmpty()) {
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, cat);
                xmlElementWriter.setElementName(ElementNameConstants.C_CAT)
                        .closeElement().write(stringBuilder);
            }
            final List<DataRange> value = this.chartSeriesTransformer.getValue(series);
            if(value != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .write(stringBuilder);
                writeNumRefElement(stringBuilder, value);
                xmlElementWriter.setElementName(ElementNameConstants.C_VAL)
                        .closeElement().write(stringBuilder);
            }
            xmlElementWriter.setElementName(ElementNameConstants.C_SER)
                    .closeElement().write(stringBuilder);
        }
        write_single_series_no_category(stringBuilder,series_list );
    }
    private void writeBubbleChartSerElement(StringBuilder stringBuilder,List<List<DataRange>> seriesList) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

        List<DataRange> xValueSeries = seriesList.get(0);
        List<DataRange> yValueSeries = seriesList.get(1);
        List<DataRange> bubbleSizeSeries = null;
        if (seriesList.size() > 2) {
            bubbleSizeSeries = seriesList.get(2);
        }
        xmlElementWriter
                .setElementName(ElementNameConstants.C_SER)
                .write(stringBuilder)
                .setElementName(ElementNameConstants.C_IDX)
                .addAttribute(AttributeNameConstants.VAL, String.valueOf(idxVal++))
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_ORDER)
                .addAttribute(AttributeNameConstants.VAL, String.valueOf(orderVal++))
                .closeElement().write(stringBuilder);
        final DataRange name = this.chartSeriesTransformer.getName(yValueSeries);
        if(name != null) {
            xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                    .write(stringBuilder);
            writeStrRefElement(stringBuilder, name);
            xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                    .closeElement().write(stringBuilder);
        }
        write_dLbls_element(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_XVAL)
                .write(stringBuilder);
        writeNumRefElement(stringBuilder, this.chartSeriesTransformer.getValue(xValueSeries));
        xmlElementWriter.setElementName(ElementNameConstants.C_XVAL)
                .closeElement().write(stringBuilder);
        final List<DataRange> value = this.chartSeriesTransformer.getValue(yValueSeries);
        if(value != null) {
            xmlElementWriter.setElementName(ElementNameConstants.C_YVAL)
                    .write(stringBuilder);
            writeNumRefElement(stringBuilder, value);
            xmlElementWriter.setElementName(ElementNameConstants.C_YVAL)
                    .closeElement().write(stringBuilder);
        }
        if (bubbleSizeSeries == null) {
            final int size = value.stream().mapToInt(d -> d.getSize()).sum();
            xmlElementWriter.write(stringBuilder, ElementNameConstants.C_BUBBLESIZE);
            xmlElementWriter.write(stringBuilder, ElementNameConstants.C_NUMLIT);
            xmlElementWriter.addAttribute(AttributeNameConstants.VAL, String.valueOf(size))
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_PTCOUNT);
            for (int i = 0; i < size; i++) {
                xmlElementWriter
                        .addAttribute(AttributeNameConstants.IDX, String.valueOf(i))
                        .write(stringBuilder, ElementNameConstants.C_PT)
                        .addValue("1")//No I18N
                        .write(stringBuilder, ElementNameConstants.C_V)
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.C_PT);
            }
            xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.C_NUMLIT);
            xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.C_BUBBLESIZE);
        } else {
            xmlElementWriter.setElementName(ElementNameConstants.C_BUBBLESIZE)
                    .write(stringBuilder);
            writeNumRefElement(stringBuilder, this.chartSeriesTransformer.getValue(bubbleSizeSeries));
            xmlElementWriter.setElementName(ElementNameConstants.C_BUBBLESIZE)
                    .closeElement().write(stringBuilder);
        }
        xmlElementWriter.setElementName(ElementNameConstants.C_SER)
                .closeElement().write(stringBuilder);
    }
    private void writeScatterChartSerElement(StringBuilder stringBuilder, List<DataRange> cat, List<List<DataRange>> series_list, String majorType) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

        int initial = 0;
        if ((cat == null || cat.isEmpty()) && series_list.size() > 1) {
            cat = series_list.get(0);
            initial++;
        }

        for (int i = initial; i < series_list.size(); i++) {
            List<DataRange> series = series_list.get(i);
            xmlElementWriter
                    .setElementName(ElementNameConstants.C_SER)
                    .write(stringBuilder)
                    .setElementName(ElementNameConstants.C_IDX)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(idxVal++))
                    .closeElement().write(stringBuilder)
                    .setElementName(ElementNameConstants.C_ORDER)
                    .addAttribute(AttributeNameConstants.VAL, String.valueOf(orderVal++))
                    .closeElement().write(stringBuilder);
//                                xmlElementWriter.setElementName(ElementNameConstants.C_DLBLS)
//                                        .write(stringBuilder);
//                                xmlElementWriter.setElementName(ElementNameConstants.C_DLBLS)
//                                        .closeElement().write(stringBuilder);
            final DataRange name = this.chartSeriesTransformer.getName(series);
            if(name != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .write(stringBuilder);
                writeStrRefElement(stringBuilder, name);
                xmlElementWriter.setElementName(ElementNameConstants.C_TX)
                        .closeElement().write(stringBuilder);
            }
            write_dLbls_element(stringBuilder);
            if(!majorType.contains("XYLINE")) {
                writeScatterSpPrElement(stringBuilder);
            }
            if(majorType.equals("XYLINE_SCATTER")){
                xmlElementWriter.write(stringBuilder, ElementNameConstants.C_MARKER)
                        .addAttribute(AttributeNameConstants.VAL, "none").closeElementAndWrite(stringBuilder, ElementNameConstants.C_SYMBOL)//No I18N
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.C_MARKER);
            }
            if(cat != null && !cat.isEmpty()) {
                xmlElementWriter.setElementName(ElementNameConstants.C_XVAL)
                        .write(stringBuilder);
                if (initial == 1) {
                    writeStrRefElement(stringBuilder, this.chartSeriesTransformer.getValue(cat));
                } else {
                    writeStrRefElement(stringBuilder, cat);
                }
                xmlElementWriter.setElementName(ElementNameConstants.C_XVAL)
                        .closeElement().write(stringBuilder);
            }
            final List<DataRange> value = this.chartSeriesTransformer.getValue(series);
            if(value != null) {
                xmlElementWriter.setElementName(ElementNameConstants.C_YVAL)
                        .write(stringBuilder);
                writeNumRefElement(stringBuilder, value);
                xmlElementWriter.setElementName(ElementNameConstants.C_YVAL)
                        .closeElement().write(stringBuilder);
            }
            xmlElementWriter.setElementName(ElementNameConstants.C_SER)
                    .closeElement().write(stringBuilder);
        }
        write_single_series_no_category(stringBuilder,series_list );
    }

    private void writeScatterSpPrElement(StringBuilder stringBuilder) {
        XmlElementWriter.newInstance()
                .write(stringBuilder, ElementNameConstants.C_SPPR)
                .write(stringBuilder, ElementNameConstants.A_LN)
                .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.A_NOFILL)
                .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.A_ROUND)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.A_LN)
                .closeEmptyElementAndWrite(stringBuilder, ElementNameConstants.A_EFFECTLST)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_SPPR);
    }


    private void writeStrRefElement(StringBuilder stringBuilder, DataRange name) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.setElementName(ElementNameConstants.C_STRREF)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_F)
                .addValue(Utility.xmlEncode(ChartSeriesTransformer.toString(name)))
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_STRREF)
                .closeElement().write(stringBuilder);
    }

    private void writeStrRefElement(StringBuilder stringBuilder, List<DataRange> name) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.setElementName(ElementNameConstants.C_STRREF)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_F)
                .addValue(Utility.xmlEncode(ChartSeriesTransformer.toString(name)))
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_STRREF)
                .closeElement().write(stringBuilder);
    }

    private void writeNumRefElement(StringBuilder stringBuilder, List<DataRange> name) {
        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.C_NUMREF)
                .write(stringBuilder)
                .setElementName(ElementNameConstants.C_F)
                .addValue(Utility.xmlEncode(ChartSeriesTransformer.toString(name)))
                .closeElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_NUMCACHE)
                .closeEmptyElement().write(stringBuilder)
                .setElementName(ElementNameConstants.C_NUMREF)
                .closeElement().write(stringBuilder);
    }

    private void writeDateAxElement(StringBuilder stringBuilder, String catAxId, String valAxId) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.setElementName(ElementNameConstants.C_DATEAX)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, catAxId)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SCALING)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_ORIENTATION)
                .addAttribute(AttributeNameConstants.VAL, "minMax")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SCALING)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_DELETE)
                .addAttribute(AttributeNameConstants.VAL, "0")
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXPOS)
                .addAttribute(AttributeNameConstants.VAL, "b")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MAJORTICKMARK)
                .addAttribute(AttributeNameConstants.VAL, "none")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MINORTICKMARK)
                .addAttribute(AttributeNameConstants.VAL, "none")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SPPR)
                .closeEmptyElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_CROSSAX)
                .addAttribute(AttributeNameConstants.VAL, valAxId)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_DATEAX)
                .closeElement().write(stringBuilder);
    }

    private static void writeCatAxElement(StringBuilder stringBuilder, String catAxId, String valAxId) {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.setElementName(ElementNameConstants.C_CATAX)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, catAxId)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SCALING)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_ORIENTATION)
                .addAttribute(AttributeNameConstants.VAL, "minMax")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SCALING)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_DELETE)
                .addAttribute(AttributeNameConstants.VAL, "0")
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXPOS)
                .addAttribute(AttributeNameConstants.VAL, "b")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MAJORTICKMARK)
                .addAttribute(AttributeNameConstants.VAL, "none")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MINORTICKMARK)
                .addAttribute(AttributeNameConstants.VAL, "none")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SPPR)
                .closeEmptyElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_CROSSAX)
                .addAttribute(AttributeNameConstants.VAL, valAxId)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_CATAX)
                .closeElement().write(stringBuilder);
    }

    private static void writeValAxElement(StringBuilder stringBuilder, String catAxId, String valAxId) {
        final XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.setElementName(ElementNameConstants.C_VALAX)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXID)
                .addAttribute(AttributeNameConstants.VAL, valAxId)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SCALING)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_ORIENTATION)
                .addAttribute(AttributeNameConstants.VAL, "minMax")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SCALING)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_DELETE)
                .addAttribute(AttributeNameConstants.VAL, "0")
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_AXPOS)
                .addAttribute(AttributeNameConstants.VAL, "l")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MAJORGRIDLINES)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SPPR)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.A_LN)
                .closeEmptyElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SPPR)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MAJORGRIDLINES)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MINORGRIDLINES)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SPPR)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.A_LN)
                .closeEmptyElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SPPR)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MINORGRIDLINES)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MAJORTICKMARK)
                .addAttribute(AttributeNameConstants.VAL, "none")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_MINORTICKMARK)
                .addAttribute(AttributeNameConstants.VAL, "none")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_TICKLBLPOS)
                .addAttribute(AttributeNameConstants.VAL, "nextTo")//No I18N
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SPPR)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.A_LN)
                .closeEmptyElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_SPPR)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_CROSSAX)
                .addAttribute(AttributeNameConstants.VAL, catAxId)
                .closeElement().write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.C_VALAX)
                .closeElement().write(stringBuilder);
    }

    private void writeLegendElement(StringBuilder stringBuilder) {
        String legendPos;
        final int legendPosition = this.chart.getLegendPosition();
        switch (legendPosition) {
            case 0: legendPos = null;
                break;
            case 1: legendPos = "b";//No I18N
                break;
            case 2: legendPos = "t";//No I18N
                break;
            case 3: legendPos = "r";//No I18N
                break;
            case 4: legendPos = "l";//No I18N
                break;
            case 5: legendPos = "tr";//No I18N
                break;
            default:
                legendPos = "r";//No I18N
        }

        if(legendPos != null) {
            final XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
            xmlElementWriter.setElementName(ElementNameConstants.C_LEGEND)
                    .write(stringBuilder);
            xmlElementWriter.setElementName(ElementNameConstants.C_LEGENDPOS)
                    .addAttribute(AttributeNameConstants.VAL, legendPos)
                    .closeElement().write(stringBuilder);
            xmlElementWriter.setElementName(ElementNameConstants.C_OVERLAY)
                    .addAttribute(AttributeNameConstants.VAL, "0")//No I18N
                    .closeElement().write(stringBuilder);
            xmlElementWriter.setElementName(ElementNameConstants.C_LEGEND)
                    .closeElement().write(stringBuilder);
        }
    }

    public DrawingProperties getDrawingProperties(Sheet sheet) {
        return getDrawingProperties(sheet, this.chart);
    }

    public static DrawingProperties getDrawingProperties(Sheet sheet, Chart chart) {
        final JSONObjectWrapper positionJSON = chart.getPosition();
        final JSONObjectWrapper sizeJSON = chart.getSize();
        int sr = Integer.parseInt(positionJSON.getString("sr"));
        if (sr == -1) {
            //for xls imports
            final int top = positionJSON.getInt("top");
            sr = Utility.calculateRowAndRowDiff(top, sheet)[0];
        }
        int sc = Integer.parseInt(positionJSON.getString("sc"));
        if (sc == -1) {
            //for xls imports
            final int left = positionJSON.getInt("left");
            sc = Utility.calculateColumnAndColumnDiff(left, sheet)[0];
        }
        Double srd = Double.valueOf(positionJSON.getString("srd"));
        if (srd < 0.0) {
            srd = 0.0;
        }
        Double scd = Double.valueOf(positionJSON.getString("scd"));
        if(scd < 0.0) {
            scd = 0.0;
        }
        Double wd = Double.valueOf(sizeJSON.getString("wd"));
        if(wd < 1.0) {
            wd = 615.0;
        }
        Double ht = Double.valueOf(sizeJSON.getString("ht"));
        if (ht < 1.0) {
            ht = 380.0;
        }
        int left = 0;
        if(positionJSON.has("left")){
            left = positionJSON.getInt("left");
        }
        int top = 0;
        if(positionJSON.has("top")){
            top = positionJSON.getInt("top");
        }
        return new DrawingProperties()
                .setChart(true)
                .setRowIndex(sr)
                .setColIndex(sc)
                .setRowDiff(srd)
                .setColDiff(scd)
                .setHeight(wd)
                .setWidth(ht)
                .setLeft(left)
                .setTop(top);
    }

    public void write_dLbls_element(StringBuilder stringBuilder) {
        final String dataLabel = chart.getDataLabel();
        if (dataLabel == null) {
            return;
        }
        if (dataLabel.equals("N")) {
            return;
        }

        String dlblPos = "outEnd";//No I18N

        XmlElementWriter.newInstance()
                .write(stringBuilder, ElementNameConstants.C_DLBLS)
//                    .addAttribute(AttributeNameConstants.VAL, dlblPos)
//                    .closeElementAndWrite(stringBuilder, ElementNameConstants.C_DLBLPOS)
                .addAttribute(AttributeNameConstants.VAL, "0")//No I18N
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_SHOWLEGENDKEY)
                .addAttribute(AttributeNameConstants.VAL, String.valueOf(dataLabel.contains("V")))//No I18N
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_SHOWVAL)
                .addAttribute(AttributeNameConstants.VAL, String.valueOf(dataLabel.contains("L")))//No I18N
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_SHOWCATNAME)
                .addAttribute(AttributeNameConstants.VAL, String.valueOf(dataLabel.contains("S")))//No I18N
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_SHOWSERNAME)
                .addAttribute(AttributeNameConstants.VAL, String.valueOf(dataLabel.contains("P")))//No I18N
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_SHOWPERCENT)
                .addAttribute(AttributeNameConstants.VAL, "0")//No I18N
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_SHOWBUBBLESIZE)
                .closeElementAndWrite(stringBuilder, ElementNameConstants.C_DLBLS);
    }
}
