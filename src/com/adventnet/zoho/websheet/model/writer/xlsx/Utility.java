/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.functions.*;
import com.adventnet.zoho.websheet.model.ext.functions.Currency;
import com.adventnet.zoho.websheet.model.ext.parser.ASTRangeNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSPrintVisitor;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSFontScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.singularsys.jep.PostfixMathCommandI;
import com.singularsys.jep.parser.ASTFunNode;
import com.singularsys.jep.parser.ASTVarNode;
import com.singularsys.jep.parser.Node;
import com.zoho.sheet.util.ColorUtil;

import java.security.SecureRandom;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Utility {

    public static final String ZERO_STRING = "0";//No I18N
    public static final String ONE_STRING = "1";//No I18N
    private static final Pattern IN_QUOTES_PATTERN = Pattern.compile("\"[^\"]*\"");//No I18N
    private static final Pattern REF_ERROR_PATTERN = Pattern.compile("\\$?'?#REF!'?(!|\\.)((\\[\\-)|(R-)|(C-)|\\[|\\]|[0-9]|[A-Za-z]|\\$|:)*");//No I18N
    private static final Pattern REF_ERROR_PATTERN_2 = Pattern.compile("\\$?(('[^']+')|([A-Za-z0-9]+))(!|\\.)#REF!");//No I18N
    private static final Logger LOGGER = Logger.getLogger(Utility.class.getName());

    public static String getWorkbookRel_Type(String target) {
        //todo:
        return null;
    }

    static Function<Boolean, String> stringValueOf = (b -> b ? "1" : "0");

    public static String getTag(String name, String[] keys, String[] values, boolean isClose) {
        StringJoiner stringJoiner = new StringJoiner(" ");
        for(int i = 0; i < keys.length; i++) {
            stringJoiner.add(keys[i]+"=\""+values[i]+"\"");
        }

        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("<");

        stringBuilder.append(name);
        if(keys.length != 0) {
            stringBuilder.append(" ");
            stringBuilder.append(stringJoiner.toString());
        }
        if(isClose) {
            stringBuilder.append("/");
        }
        stringBuilder.append(">");

        return stringBuilder.toString();
    }

    public static String getEmptyTag(String name) {
        return ("<"+name+">");
    }

    public static String getCloseTag(String name) {
        return ("</"+name+">");
    }

    public final static String xmlEncode(String text)
    {
        if (text == null)
        {
            return null;
        }
        char c;
        StringBuilder n = new StringBuilder(text.length() * 2);
        for (int i = 0; i < text.length(); i++)
        {
            c = text.charAt(i);
            switch (c)
            {
                case '&' :
                    n.append("&amp;");//No I18N
                    break;
                case '<' :
                    n.append("&lt;");//No I18N
                    break;
                case '>' :
                    n.append("&gt;");//No I18N
                    break;
                case '"' :
                    n.append("&quot;");//No I18N
                    break;
                case '\'' :
                    n.append("&apos;");//No I18N
                    break;
//                case '\n':
//                    n.append("_x000a_");//No I18N
//                    break;
                default :
                {
                    int cc = c;
                    // Skip all Control characters -ascii- from 17 t0 20 (device control chars)
                    // skip vertical tab character too. ascii value 11.
                    // skip vertical tab character too. ascii value 8.
                    // Skipping all characrters < 32 and not 9, 10 or 13.
                    if(cc > 31 || cc ==9  || cc == 10 || cc == 13)
                    {
                        n.append(c);
                    }
                    // n.append(c);
                    break;
                }
            }
        }
        return n.toString();
    }

    public static <K,V> V putIfAbsent(Map<K,V> map, K k, V v) {
        V v1 = map.get(k);
        if(v1 == null) {
            map.put(k, v);
            return v;
        } else {
            return v1;
        }
    }

    public static String toString(DataRange range, boolean isRowRelative, boolean isColRelative) {
        StringBuilder stringBuilder = new StringBuilder();
        final String sheetName = range.getAssociatedSheetName();
        if(sheetName != null) {
            if (sheetName.matches(".*[^A-Za-z0-9_].*")) {
                stringBuilder
                        .append("'")
                        .append(sheetName)
                        .append("'");
            } else {
                stringBuilder.append(sheetName);
            }
            stringBuilder
                    .append("!");
        }
        stringBuilder
                .append(CellUtil.getCellReferenceString(range.getStartRowIndex(), range.getStartColIndex(), isRowRelative, isColRelative));
        if(range.getStartRowIndex() != range.getEndRowIndex() || range.getStartColIndex() != range.getEndColIndex()) {
            stringBuilder
                    .append(":")
                    .append(CellUtil.getCellReferenceString(range.getEndRowIndex(), range.getEndColIndex(), isRowRelative, isColRelative));
        }
        return stringBuilder.toString();
    }

    public static String toString(Node node, Workbook workbook, int rowIndex, int colIndex, boolean convertStructuredReferences, Cell cell) {
        final ZSPrintVisitor zsPrintVisitor = new ZSPrintVisitor(ZSPrintVisitor.FORMULA_CLIENT.XLSX_WRITER);
        zsPrintVisitor.setConvertStructuredReferencesToRange(convertStructuredReferences);
        if(cell == null) {
            Sheet sheet = new Sheet(workbook);
            Row row = new Row(sheet, rowIndex);
            Column col = new Column(sheet, colIndex);
            cell = new CellImpl(row, col);
        }
        final String formula_string = zsPrintVisitor.toString(node, cell);
        return replace_ref_error_in_formula(formula_string);
    }
    public static String getHexColor(ZSColor color, ZSTheme theme) {
        if (color == null) {
            LOGGER.log(Level.WARNING, "xlsx-writer color is null");
            return null;
        }
        String hexColor = ZSColor.getHexColor(color, theme);
        if (hexColor == null) {
            LOGGER.log(Level.WARNING, "xlsx-writer color is null after conversion, object: {0}", new Object[]{color.toString()});
            return null;
        }
        if(!hexColor.startsWith("#")) {
            if(hexColor.equals("transparent") || hexColor.equals("windowtext")) {
                return null;
            }
            try
            {
                final String hexColorTemp = ColorUtil.getHexColor(hexColor);
                if(hexColorTemp == null)
                {
                    LOGGER.log(Level.WARNING, "xlsx-writer non-hex color is null after conversion : {0}, object : {1}", new Object[]{hexColor, color.toString()});
                    return null;
                }
                hexColor = hexColorTemp;
            }
            catch(Exception e)
            {
                LOGGER.log(Level.WARNING, "xlsx-writer non-hex color is invalid after conversion", e);
                LOGGER.log(Level.WARNING, "xlsx-writer non-hex color is invalid after conversion : {0}, object : {1}", new Object[]{hexColor, color.toString()});
                return null;
            }
        }
        if (!hexColor.startsWith("#")) {
            LOGGER.log(Level.WARNING, "xlsx-writer couldn't get hex-color for {0}", hexColor);
            return null;
        }
        return "FF"+hexColor.substring(1).toUpperCase();//No I18N
    }

    public static boolean isInteger(Number number) {
        return number.doubleValue() - number.intValue() == 0;
    }
    private static String replace_ref_error_in_formula(String formula) {
        final Matcher matcher = IN_QUOTES_PATTERN.matcher(formula);
        StringBuilder stringBuilder = new StringBuilder();
        int start1 = 0;
        while (matcher.find()) {
            String not_matched = formula.substring(start1, matcher.start());
            stringBuilder.append(replace_ref_error_in_non_text_formula(not_matched));
            String matched = formula.substring(matcher.start(), matcher.end());
            stringBuilder.append(matched);
            start1 = matcher.end();
        }
        String not_matched = formula.substring(start1);
        stringBuilder.append(replace_ref_error_in_non_text_formula(not_matched));
        return stringBuilder.toString();
    }


    private static String replace_ref_error_in_non_text_formula(String non_text_formula) {
        final Matcher matcher = REF_ERROR_PATTERN.matcher(non_text_formula);
        StringBuffer stringBuffer = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(stringBuffer, "#REF!");//No I18N
        }
        matcher.appendTail(stringBuffer);
        return replace_ref_error_2_in_non_text_formula(stringBuffer.toString());
    }

    private static String replace_ref_error_2_in_non_text_formula(String non_text_formula) {
        final Matcher matcher = REF_ERROR_PATTERN_2.matcher(non_text_formula);
        StringBuffer stringBuffer = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(stringBuffer, "#REF!");//No I18N
        }
        matcher.appendTail(stringBuffer);
        return stringBuffer.toString();
    }

    public static StylesBuilder.Font getDefaultFont(CellStyle default_cellStyle, ZSTheme theme) {
        StylesBuilder.Font font = new StylesBuilder.Font();
        if (default_cellStyle == null || default_cellStyle.getTextStyleProperties() == null) {
            font.fontSize = "10pt";//No I18N
            font.fontName = "Tahoma";//No I18N
        }

        final Object fontName_obj = TextStyle.getProperty(default_cellStyle.getTextStyleProperties(), TextStyle.Property.FONTNAME);
        if (fontName_obj == null) {
            font.fontName = "Tahoma";//No I18N
        } else {
            String fontName = (String) fontName_obj;
            if(ZSFontScheme.HEADING_FONTNAME.equals(fontName))
            {
                font.fontName = theme.getFontScheme().getFonts().get(ZSFontScheme.Fonts.HEADING);
            }
            else if(ZSFontScheme.BODY_FONTNAME.equals(fontName))
            {
                font.fontName = theme.getFontScheme().getFonts().get(ZSFontScheme.Fonts.BODY);
            } else {
                font.fontName = fontName;
            }
        }

        final Object fontSize_obj = TextStyle.getProperty(default_cellStyle.getTextStyleProperties(), TextStyle.Property.FONTSIZE);
        if (fontSize_obj == null) {
            font.fontSize = "10pt";//No I18N
        } else {
            font.fontSize = (String) fontSize_obj;
        }

        ZSColor color = (ZSColor)  TextStyle.getProperty(default_cellStyle.getTextStyleProperties(), TextStyle.Property.COLOR);
        if(color != null && !EngineConstants.TEXTCOLOR_AUTOMATIC.equals(color.getHexColorToWrite())) {
            font.color = Utility.getHexColor(color, theme);
        }

        return font;
    }

    public static String escapeDoubleQuotes(String value) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < value.length(); i++) {
            final char c = value.charAt(i);
            if(c == '"') {
                if (i + 1 < value.length() && value.charAt(i+1) == '"') {
                    i++;
                }
                stringBuilder.append(c);
            }
            stringBuilder.append(c);
        }
        return stringBuilder.toString();
    }

    public static String generate_extId() {
        //This simple type's contents shall match the following regular expression pattern: \{[0-9A-F]{8}-[0-9A- F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}\}.
        SecureRandom random = new SecureRandom();
        String text = "ABCDEF0123456789";//No I18N
        Function<Integer, StringBuilder> g = (v) -> {
            StringBuilder b = new StringBuilder();
            for (int i = 0; i < v; i++) {b.append(text.charAt(random.nextInt(text.length())));}
            return b;
        };

        return new StringBuilder().append("{")
                .append(g.apply(8)).append("-")
                .append(g.apply(4)).append("-")
                .append(g.apply(4)).append("-")
                .append(g.apply(4)).append("-")
                .append(g.apply(12))
                .append("}").toString();
    }
    public enum FormulaMeta {
        HAS_ABSOLUTE_SHEETNAME_REF,HAS_NON_EXPORTABLE_FORMULA;

        public static boolean has(EnumSet<FormulaMeta> enumSet, FormulaMeta formulaMeta) {
            if(enumSet != null) {
                return enumSet.contains(formulaMeta);
            }
            return false;
        }
    }

    public static void initFormulaMeta(Node tree, Workbook workbook, EnumSet<FormulaMeta> formulaMeta) {
        if(tree instanceof ASTVarNode){
            ASTVarNode varNode= (ASTVarNode)tree;
            if(!varNode.isSheetRelative()) {
                formulaMeta.add(FormulaMeta.HAS_ABSOLUTE_SHEETNAME_REF);
            }

            NamedExpression namedExp = workbook.getNamedExpression(varNode.getName());
            if(namedExp != null){
                if(!namedExp.isMarked){
                    namedExp.isMarked = true;
                    initFormulaMeta(namedExp.getNode(), workbook, formulaMeta);
                    namedExp.isMarked = false;
                }
            }
        }
        else if(tree instanceof ASTRangeNode){
            ASTRangeNode rangeNode= (ASTRangeNode)tree;
            if(!rangeNode.isSheetRelative()) {
                formulaMeta.add(FormulaMeta.HAS_ABSOLUTE_SHEETNAME_REF);
            }
        } else {
            if (tree instanceof ASTFunNode) {
                PostfixMathCommandI pfmc = ((ASTFunNode) tree).getPFMC();
                if (pfmc instanceof CustomFunction) {
                    NamedExpression namedExp = workbook.getLambdaExpression(((ASTFunNode) tree).getName());
                    if (namedExp != null) {
                        if (!namedExp.isMarked) {
                            namedExp.isMarked = true;
                            initFormulaMeta(namedExp.getNode(), workbook, formulaMeta);
                            namedExp.isMarked = false;
                        }
                    } else {
                        formulaMeta.add(FormulaMeta.HAS_NON_EXPORTABLE_FORMULA);
                    }
                } else if(pfmc instanceof ImportRange || pfmc instanceof Stock || pfmc instanceof Currency
                        || pfmc instanceof Top || pfmc instanceof Bottom || pfmc instanceof Split){
                    formulaMeta.add(FormulaMeta.HAS_NON_EXPORTABLE_FORMULA);
                }
            }

            for(int i=0; i<tree.jjtGetNumChildren(); i++){
                initFormulaMeta(tree.jjtGetChild(i),workbook, formulaMeta);
            }
        }
    }

    public static int[] calculateRowAndRowDiff(int top, Sheet sheet) {
        int row = 0;
        int lastHeight = 0;
        int rowPadding = 4;
        int height = sheet.getRow(row).getRowHeight()+rowPadding;
        while (height < top) {
            row++;
            lastHeight = height;
            height += (sheet.getRow(row).getRowHeight())+rowPadding;
        }
        return new int[]{row, top-lastHeight};
    }

    public static int[] calculateColumnAndColumnDiff(int left, Sheet sheet) {
        final int colPadding = 4;
        int col = 0;
        int lastWidth = 0;
        int width = sheet.getColumnHeader(col).getColumnWidth() + colPadding;
        while (width < left) {
            col++;
            lastWidth = width;
            width += (sheet.getColumnHeader(col).getColumnWidth() + colPadding);
        }
        return new int[]{col, left-lastWidth};
    }
}
