//$Id$
package com.adventnet.zoho.websheet.model.writer.xlsx.image;

import com.adventnet.zoho.websheet.model.ImageBook;
import com.adventnet.zoho.websheet.model.util.ZSStore;

public class DrawingProperties {
    private long imageId;
    private double height;
    private double width;
    private double rowDiff;
    private double colDiff;
    private String url;
    private String name;
    private String imagePath;
    private Integer left;
    private int rowIndex;
    private int columnIndex;
    private int endRowIndex = -1;
    private int endColumnIndex = -1;
    private Integer top;
    private boolean isCellImage = false;
    private ZSStore.FileName zsStoreFileName;
    private ZSStore.FileExtn zsStoreExtension;
    private String imageResourceID;
    private Long imgDFSID;
    private boolean isImage;
    private boolean isChart;
    private boolean isSlicer;
    private boolean isTimeline;

    public boolean isImage() {
        return isImage;
    }

    public DrawingProperties setImage(boolean image) {
        isImage = image;
        return this;
    }

    public boolean isChart() {
        return isChart;
    }

    public DrawingProperties setChart(boolean chart) {
        isChart = chart;
        return this;
    }

    public boolean isSlicer() {
        return isSlicer;
    }
    public DrawingProperties setSlicer(boolean slicer) {
        isSlicer = slicer;
        return this;
    }

    public boolean isTimeline() {
        return isTimeline;
    }
    public DrawingProperties setTimeline(boolean timeline) {
        isTimeline = timeline;
        return this;
    }

    public long getImageID() {
        return imageId;
    }

    public String getUrl(Boolean isRemoteMode, String docID) {
        String imgUrl = "";
        String proxyUrl = "";
        if(this.url.contains("&u_n=")) //No I18N
        {
            proxyUrl = (isRemoteMode ? ImageBook.REMOTE_IMAGE_URI : ImageBook.IMAGE_URI) + "?";  //No I18N
            imgUrl = proxyUrl + "d_n="+docID + this.url + "&isR=" +isRemoteMode;  //No I18N
            return imgUrl;
        }
        return this.url;
    }

    public DrawingProperties setImageId(long imageID) {
        this.imageId = imageID;
        return this;
    }

    public DrawingProperties setHeight(double height) {
        this.height = height;
        return this;
    }

    public DrawingProperties setWidth(double width) {
        this.width = width;
        return this;
    }

    public DrawingProperties setRowDiff(double rowDiff) {
        this.rowDiff = rowDiff;
        return this;
    }

    public DrawingProperties setColDiff(double colDiff) {
        this.colDiff = colDiff;
        return this;
    }

    public DrawingProperties setUrl(String url) {
        this.url = url;
        return this;
    }

    public DrawingProperties setName(String name) {
        this.name = name;
        return this;
    }

    public DrawingProperties setImagePath(String imagePath) {
        this.imagePath = imagePath;
        return this;
    }

    public DrawingProperties setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex;
        return this;
    }

    public DrawingProperties setColIndex(int columnIndex) {
        this.columnIndex = columnIndex;
        return this;
    }

    public DrawingProperties setTop(Integer imgXPos) {
        this.top = imgXPos;
        return this;
    }

    public DrawingProperties setLeft(Integer imgYPos) {
        this.left = imgYPos;
        return this;
    }

    public DrawingProperties setAsCellImage() {
        this.isImage = true;
        this.isCellImage = true;
        return this;
    }

    public String getImagePath() {
        return imagePath;
    }

    public int getColIndex() {
        return columnIndex;
    }

    public double getColDiff() {
        return colDiff;
    }

    public int getRowIndex() {
        return rowIndex;
    }

    public double getRowDiff() {
        return rowDiff;
    }

    public boolean isCellImage() {
        return isCellImage;
    }

    public double getHeight() {
        return height;
    }

    public double getWidth() {
        return width;
    }

    public String getName() {
        if(name == null){
            return "image1";//No I18N
        }
        return name;
    }

    public DrawingProperties setZSStoreFileName(ZSStore.FileName image) {
        this.zsStoreFileName = image;
        return this;
    }

    public DrawingProperties setFileExtension(ZSStore.FileExtn extn) {
        this.zsStoreExtension = extn;
        return this;
    }

    public ZSStore.FileName getZsStoreFileName() {
        return zsStoreFileName;
    }

    public ZSStore.FileExtn getZsStoreExtension() {
        return zsStoreExtension;
    }

    public DrawingProperties setImageDFSId(Long imgDFSID) {
        this.imgDFSID = imgDFSID;
        return this;
    }

    public Long getImgDFSID() {
        return imgDFSID;
    }

    public int getEndRowIndex() {
        return endRowIndex;
    }

    public DrawingProperties setEndRowIndex(int endRowIndex) {
        this.endRowIndex = endRowIndex;
        return this;
    }

    public int getEndColumnIndex() {
        return endColumnIndex;
    }

    public DrawingProperties setEndColumnIndex(int endColumnIndex) {
        this.endColumnIndex = endColumnIndex;
        return this;
    }

    public String getImageResourceID()
    {
        return imageResourceID;
    }

    public DrawingProperties setImageResourceID(String imageResourceID)
    {
        this.imageResourceID = imageResourceID;
        return this;
    }

    public Integer getLeft(){
        return left;
    }

    public Integer getTop(){
        return top;
    }
}

