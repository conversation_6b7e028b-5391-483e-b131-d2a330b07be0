/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.parser.XmlName;

import java.util.ArrayList;
import java.util.List;


public class XmlElementWriter {

    private String elementName;
    private String[] attributes = new String[30];
    private int no_of_attributes = 0;
    private String value;
    private boolean isClose = false;
    private boolean isCloseEmptyElement = false;

    private XmlElementWriter() {
    }

    public static XmlElementWriter newInstance() {
        return new XmlElementWriter();
    }

    public static XmlElementWriter newInstance(String name) {
        final XmlElementWriter xmlElementWriter = new XmlElementWriter();
        xmlElementWriter.setElementName(name);
        return xmlElementWriter;
    }

    public XmlElementWriter setElementName(String name) {
        this.elementName = name;
        return this;
    }

    public XmlElementWriter addAttribute(XmlName xml_name, String value) {
        String name = (xml_name.getPrefix() == null) ? xml_name.getLocalName() : (xml_name.getPrefix()+":"+xml_name.getLocalName());
        return addAttribute(name, value);
    }
    public XmlElementWriter addAttribute(String name, String value) {
        nonNull(name, "attribute-name");//No I18N
        nonNull(value, "attribute-value");//No I18N

        if(no_of_attributes == attributes.length) {
            String[] new_attributes = new String[this.attributes.length+20];
            for (int i = 0; i < this.attributes.length; i++) {
                new_attributes[i] = this.attributes[i];
            }
            this.attributes = new_attributes;
        }

        this.attributes[no_of_attributes++] = name;
        this.attributes[no_of_attributes++] = value;
        return this;
    }

    public XmlElementWriter addValue(String value) {
        nonNull(value, "value");//No I18N
        this.value = value;
        return this;
    }

    public XmlElementWriter closeElement() {
        this.isClose = true;
        return this;
    }

    /**
     * for empty element it will return <element/> instead of </element>
     * @return
     */
    public XmlElementWriter closeEmptyElement() {
        this.isClose = true;
        this.isCloseEmptyElement = true;
        return this;
    }

    public XmlElementWriter closeEmptyElementAndWrite(StringBuilder stringBuilder, XmlName xml_name) {
        String elementNam = (xml_name.getPrefix() == null) ? xml_name.getLocalName() : (xml_name.getPrefix()+":"+xml_name.getLocalName());
        return this.closeEmptyElementAndWrite(stringBuilder, elementNam);
    }

    public XmlElementWriter closeEmptyElementAndWrite(StringBuilder stringBuilder, String elementNam) {
        return this.closeEmptyElement().write(stringBuilder, elementNam);
    }

    public XmlElementWriter closeElementAndWrite(StringBuilder stringBuilder, String elementNam) {
        return this.closeElement().write(stringBuilder, elementNam);
    }

    public XmlElementWriter closeElementAndWrite(StringBuilder stringBuilder, XmlName xml_name) {
        String elementNam = (xml_name.getPrefix() == null) ? xml_name.getLocalName() : (xml_name.getPrefix()+":"+xml_name.getLocalName());
        return this.closeElementAndWrite(stringBuilder, elementNam);
    }
    public XmlElementWriter write(StringBuilder stringBuilder, String elementName) {
        return this.setElementName(elementName).write(stringBuilder);
    }

    public XmlElementWriter write(StringBuilder stringBuilder) {
        nonNull(this.elementName, "element-name");//No I18N

        if(this.isClose && this.no_of_attributes == 0 && this.value == null) {
            if (this.isCloseEmptyElement) {
                stringBuilder.append("<")
                        .append(this.elementName)
                        .append("/>");
            } else {
                stringBuilder.append("</")
                        .append(this.elementName)
                        .append(">");
            }
        } else {
            stringBuilder
                    .append("<")
                    .append(this.elementName);

            int i = 0;
            while (i < this.no_of_attributes) {
                stringBuilder
                        .append(" ")
                        .append(this.attributes[i++])
                        .append("=\"")
                        .append(this.attributes[i++])
                        .append("\"");
            }

            if (this.value == null && this.isClose) {
                stringBuilder.append("/>");
            } else {
                stringBuilder.append(">");
            }

            if (this.value != null) {
                stringBuilder
                        .append(this.value)
                        .append("</")
                        .append(this.elementName)
                        .append(">");
            }
        }

        this.elementName = null;
        this.no_of_attributes = 0;
        this.value = null;
        this.isClose = false;
        this.isCloseEmptyElement = false;

        return this;
    }

    private static <T> void nonNull(T t, String name) {
        if(t == null) {
            throw new IllegalArgumentException(name + " can't be null");//No I18N
        }
    }

    public static void main(String[] args) {
        StringBuilder stringBuilder = new StringBuilder();
        XmlElementWriter.newInstance()
                .setElementName("A")//No I18N
//                .addAttribute("time", "7:33PM")
//                .addAttribute("name", "bsc")
//                .addValue("100")
                .closeElement()
                .write(stringBuilder);
//        System.out.println(stringBuilder.toString());

    }
}
