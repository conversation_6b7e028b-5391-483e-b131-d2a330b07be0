/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSHexColor;
import com.adventnet.zoho.websheet.model.style.datastyle.*;
import com.adventnet.zoho.websheet.model.style.fill.*;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.EngineConstants;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;

public class StylesBuilder {
    private final Pattern optionalDecimalInPercentage = Pattern.compile("^#+0\\.#+%$");//No I18N
    private final Pattern optionalDecimal = Pattern.compile("^(#|0|\\,)+\\.#+$");//No I18N

    static class NumFmt {
        String pattern;
        int id;

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (o == null || getClass() != o.getClass()) {return false;}
            NumFmt numFmt = (NumFmt) o;
            return Objects.equals(pattern, numFmt.pattern);
        }

        @Override
        public int hashCode() {
            return Objects.hash(pattern);
        }
    }

    static class Font{
        String fontWeight;
        String fontStyle;
        String textUnderlineStyle;
        String textLineThroughStyle;
        String fontSize;
        String color;
        String fontName;

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (o == null || getClass() != o.getClass()) {return false;}
            Font font = (Font) o;
            return Objects.equals(fontWeight, font.fontWeight) && Objects.equals(fontStyle, font.fontStyle) && Objects.equals(textUnderlineStyle, font.textUnderlineStyle) && Objects.equals(textLineThroughStyle, font.textLineThroughStyle) && Objects.equals(fontSize, font.fontSize) && Objects.equals(color, font.color) && Objects.equals(fontName, font.fontName);
        }

        @Override
        public int hashCode() {
            return Objects.hash(fontWeight, fontStyle, textUnderlineStyle, textLineThroughStyle, fontSize, color, fontName);
        }

        static Font getInstance(com.adventnet.zoho.websheet.model.style.Style style, Workbook workbook, boolean cellHasHyperLink, boolean isDxf, Font default_font) {

            String fontColor = cellHasHyperLink ?  "FF0563C1" : null;//No I18N
            Object fontWeight = null;
            Object fontStyle = null;
            String textUnderlineStyle = cellHasHyperLink ?  "solid" : null;//No I18N
            Object fontLineThroughStyle = null;
            Object fontSize = null;
            Object fontName = null;

            if(style != null) {
                ZSColor fontZSColor;
                if (style instanceof CellStyle) {
                    fontZSColor = com.adventnet.zoho.websheet.model.util.Utility.getAutomaticTextColorAdjusted((CellStyle) style, workbook);
                } else {
                    fontZSColor = (ZSColor) style.getProperty(com.adventnet.zoho.websheet.model.style.TextStyle.Property.COLOR);
                    if (fontZSColor instanceof ZSHexColor && EngineConstants.TEXTCOLOR_AUTOMATIC.equals(fontZSColor.getHexColorToWrite()))
                    {
                        fontZSColor = null;
                    }
                }

                if (fontZSColor != null) {
                    fontColor = Utility.getHexColor(fontZSColor, workbook.getTheme());
                }

                fontWeight = style.getProperty(TextStyle.Property.FONTWEIGHT);
                fontStyle = style.getProperty(TextStyle.Property.FONTSTYLE);
                textUnderlineStyle = com.adventnet.zoho.websheet.model.util.Utility.masknull((String)style.getProperty(TextStyle.Property.TEXTUNDERLINESTYLE), textUnderlineStyle);
                fontLineThroughStyle = style.getProperty(TextStyle.Property.TEXTLINETHROUGHSTYLE);
                fontSize = style.getProperty(TextStyle.Property.FONTSIZE);
                fontName = style.getProperty(TextStyle.Property.FONTNAME);
            }

            Font font = new Font();
            font.fontName = fontName == null ? default_font.fontName : (String) fontName;
            font.color = fontColor == null ? default_font.color : fontColor;
            font.fontWeight = (String) fontWeight;
            font.fontStyle = (String) fontStyle;
            font.textLineThroughStyle = (String)fontLineThroughStyle;
            font.textUnderlineStyle = textUnderlineStyle == null ? default_font.textUnderlineStyle : textUnderlineStyle;

            if (fontSize == null || ((String)fontSize).endsWith("em")) {
                if(!isDxf) {
                    font.fontSize = default_font.fontSize;
                }
            } else {
                font.fontSize = (String) fontSize;
            }
            
            if(font.color != null && font.color.equals("FF000000")) {
                font.color = null;
            }
            return font;
        }
    }

    static class Border {
        BorderPr left;
        BorderPr right;
        BorderPr top;
        BorderPr bottom;
        BorderPr diagonal;
        BorderPr vertical;
        BorderPr horizontal;

        Boolean diagonalUp;
        Boolean diagonalDown;

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (o == null || getClass() != o.getClass()) {return false;}
            Border border = (Border) o;
            return Objects.equals(left, border.left) &&
                    Objects.equals(right, border.right) &&
                    Objects.equals(top, border.top) &&
                    Objects.equals(bottom, border.bottom) &&
                    Objects.equals(diagonal, border.diagonal) &&
                    Objects.equals(vertical, border.vertical) &&
                    Objects.equals(horizontal, border.horizontal) &&
                    Objects.equals(diagonalUp, border.diagonalUp) &&
                    Objects.equals(diagonalDown, border.diagonalDown);
        }

        @Override
        public int hashCode() {
            return Objects.hash(left, right, top, bottom, diagonal, vertical, horizontal, diagonalUp, diagonalDown);
        }


    }

    static class BorderPr {
        ZSColor color;
        String borderStyle;

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (o == null || getClass() != o.getClass()) {return false;}
            BorderPr borderPr = (BorderPr) o;
            return Objects.equals(color, borderPr.color) &&
                    Objects.equals(borderStyle, borderPr.borderStyle);
        }

        @Override
        public int hashCode() {
            return Objects.hash(color, borderStyle);
        }

        static BorderPr build(String borderPr) {
            if(borderPr == null || "none".equals(borderPr)) {
                return null;
            }

            String[] prop = borderPr.split(" ");
            String type = "thick";//No I18N

            int index = prop[0].indexOf("in");
            if(index == - 1) {
              index = prop[0].indexOf("pt");
                float size = Float.parseFloat(prop[0].substring(0, index));
                if(size <= 0.74) {
                    type = "thin";//No I18N
                } else if(size < 2.49) {
                    type = "medium";//No I18N
                }
            } else {
                float size = Float.parseFloat(prop[0].substring(0, index));
                if(size <= 0.013799999840557575) {
                    type = "thin";//No I18N
                } else if(size < 0.0346) {
                    type = "medium";//No I18N
                }
            }

            switch (prop[1]) {
                case "solid":
                    break;
                case "dashed":
                case "fine-dashed":
                    switch (type) {
                        case "medium":
                            type = "mediumDashed";//No I18N
                            break;
                        default:
                            type = "dashed";//No I18N
                    }
                    break;
                case "dotted":
                    type = "dotted";//No I18N
                    break;
                case "double":
                case "double-thin":
                    type = "double";//No I18N
                    break;
                case "dash-dot":
                    switch (type) {
                        case "medium":
                            type = "mediumDashed";//No I18N
                            break;
                        default:
                            type = "dashDot";//No I18N
                    }
                    break;

                case "dash-dot-dot":
                    switch (type) {
                        case "medium":
                            type = "mediumDashDotDot";//No I18N
                            break;
                        default:
                            type = "dashDotDot";//No I18N
                    }
                    break;
            }

            BorderPr pr = new BorderPr();
            pr.borderStyle = type;
            pr.color = BorderProperties.getInstance(borderPr).getColor();

            return pr;
        }
    }

    static class CellAlignment {
        String horizontal;
        String vertical;
        Boolean wrapText;
        Boolean shrinkToFit;
        String rotationAngle;
        String indent;

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (o == null || getClass() != o.getClass()) {return false;}
            CellAlignment that = (CellAlignment) o;
            return Objects.equals(horizontal, that.horizontal) &&
                    Objects.equals(vertical, that.vertical) &&
                    Objects.equals(wrapText, that.wrapText) &&
                    Objects.equals(shrinkToFit, that.shrinkToFit) &&
                    Objects.equals(rotationAngle, that.rotationAngle) &&
                    Objects.equals(indent, that.indent);
        }

        @Override
        public int hashCode() {
            return Objects.hash(horizontal, vertical, wrapText, shrinkToFit, rotationAngle, indent);
        }
    }

    static class Style {
        int fillId;
        int fontId;
        int numFmtId;
        int borderId;

        CellAlignment cellAlignment;

        @Override
        public boolean equals(Object o) {

            if (this == o) {return true;}
            if (o == null || getClass() != o.getClass()) {return false;}
            Style style = (Style) o;
            return fillId == style.fillId &&
                    fontId == style.fontId &&
                    numFmtId == style.numFmtId &&
                    borderId == style.borderId &&
                    Objects.equals(cellAlignment, style.cellAlignment);
        }

        @Override
        public int hashCode() {
            return Objects.hash(fillId, fontId, numFmtId, borderId, cellAlignment);
        }
    }

    final Map<NumFmt, Integer> numFmts = new LinkedHashMap<>();
    final Map<StylesBuilder.Font, Integer> fonts = new LinkedHashMap<>();
    final Map<Fill, Integer> fills = new LinkedHashMap<>();
    final Map<StylesBuilder.Border, Integer> borders = new LinkedHashMap<>();
    final Map<StylesBuilder.Style, Integer> xfs = new LinkedHashMap<>();
    private final boolean isDxf;
    private final XlsxConversionStats xLSX_CONVERSION_STATS;
    private final StylesBuilder.Font default_font;
    private final ZSTheme theme;
    private final CellAlignment default_cell_alignment;
    private final Map<Integer, Integer> xfs_hash = new HashMap<>();
    private final Workbook workbook;

    public StylesBuilder(boolean isDxf, XlsxConversionStats xlsxConversionStats, ZSTheme theme, Font default_font, CellAlignment cellAlignment, Workbook workbook) {
        this.isDxf = isDxf;
        this.theme = theme;
        this.xLSX_CONVERSION_STATS = xlsxConversionStats;

        this.default_font = default_font;
        this.default_cell_alignment = cellAlignment;

        this.workbook = workbook;
        if(this.isDxf) {
            return;
        }
        fonts.put(default_font, 0);

        Fill fill = PatternFill.getInstance(PatternFill.PatternType.NONE, null, null);
        fills.put(fill, 0);

        fill = PatternFill.getInstance(PatternFill.PatternType.GREY_125, null, null);
        fills.put(fill, 1);

        StylesBuilder.Border border = new StylesBuilder.Border();
        borders.put(border, 0);

        StylesBuilder.Style defaultStyle = new StylesBuilder.Style();

        defaultStyle.cellAlignment = this.default_cell_alignment;

        xfs.put(defaultStyle, 0);
    }

    public int getXfIndex(CellStyle cellStyle, int numFmtId, boolean cellHasHyperLink, boolean isMultiLine) {

        if(cellStyle == null && numFmtId == 0 && !cellHasHyperLink && !isMultiLine) {
            return 0;
        }

        ZSPattern pattern = cellStyle == null ? null : cellStyle.getPattern();
        if (DataStyleConstants.EMPTY_PATTERN == pattern) {
            pattern = null;
        }

        final int hash = Objects.hash(cellStyle == null ? null : cellStyle.getStyleName(), pattern, numFmtId, cellHasHyperLink, isMultiLine);
        final Integer temp_xf_id = xfs_hash.get(hash);
        if (temp_xf_id != null) {
            return temp_xf_id;
        }
        StylesBuilder.Style style = new StylesBuilder.Style();
        if (pattern == null) {
            style.numFmtId = numFmtId;
        } else {
            style.numFmtId = getNumFmtId(pattern);
        }

        if(cellStyle != null) {
            style.fontId = getFontIndex(cellStyle, cellHasHyperLink, this.isDxf);
            style.fillId = getFillIndex(cellStyle);
            style.borderId = getBorderIndex(cellStyle);
            if(!isDxf) {
                style.cellAlignment = getCellAlignment(cellStyle, isMultiLine, this.default_cell_alignment);
            }
        } else {
            if (cellHasHyperLink) {
                style.fontId = getFontIndex(null, cellHasHyperLink, this.isDxf);
            }
        }
        if(!isDxf) {
            style.cellAlignment = getCellAlignment(cellStyle, isMultiLine, this.default_cell_alignment);
        }

        Integer integer = xfs.get(style);
        if(integer == null) {
            integer = xfs.size();
            xfs.put(style, integer);
            this.xLSX_CONVERSION_STATS.noOfStyles++;
        }
        xfs_hash.put(hash, integer);
        return integer;
    }

    public static StylesBuilder.CellAlignment getCellAlignment(CellStyle cellStyle, boolean isMultiLine, CellAlignment default_cell_alignment) {
        if (cellStyle == null && isMultiLine) {
            CellAlignment cellAlignment = new CellAlignment();
            cellAlignment.wrapText = true;
            if (default_cell_alignment != null) {
                cellAlignment.vertical = default_cell_alignment.vertical;
            }
            return cellAlignment;
        } else if (cellStyle == null) {
            if (default_cell_alignment != null) {
                return default_cell_alignment;
            }
            CellAlignment cellAlignment = new CellAlignment();
            cellAlignment.vertical = "top";//No I18N
            return cellAlignment;
        }

        String textAlign = (String)cellStyle.getProperty(ParagraphStyle.Property.TEXTALIGN);
        String verticalAlign = (String)cellStyle.getProperty(CellStyle.Property.VERTICALALIGN);
        CellStyleProperties.DisplayType displayType = (CellStyleProperties.DisplayType)cellStyle.getProperty(CellStyle.Property.DISPLAYTYPE);
        String rotationAngle = (String)cellStyle.getProperty(CellStyle.Property.ROTATIONANGLE);
        String indent = (String)cellStyle.getProperty(ParagraphStyle.Property.ZSINDENT);

        CellAlignment cellAlignment = new CellAlignment();
        if (CellStyleProperties.DisplayType.WRAP.equals(displayType) || isMultiLine) {
            cellAlignment.wrapText = true;
        }
        else if(CellStyleProperties.DisplayType.SHRINKTOFIT.equals(displayType))
        {
            cellAlignment.shrinkToFit = true;
        }

        if(textAlign != null) {
            switch (textAlign) {
                case "start"://No I18N
                    cellAlignment.horizontal = "left";//No I18N
                    break;
                case "end"://No I18N
                    cellAlignment.horizontal = "right";//No I18N
                    break;
                case "center"://No I18N
                case "centerContinuous"://No I18N
                case "distributed"://No I18N
                case "fill"://No I18N
                case "general"://No I18N
                case "justify"://No I18N
                case "left"://No I18N
                case "right"://No I18N
                    cellAlignment.horizontal = textAlign;
                    break;
            }
        }

        if(verticalAlign != null) {
            switch (verticalAlign) {
                case "baseline":
                    cellAlignment.vertical = "bottom";//No I18N
                    break;
                case "middle":
                    cellAlignment.vertical = "center";//No I18N
                    break;
                case "automatic":
                    cellAlignment.vertical = "top";//No I18N
                    break;
                default:
                    cellAlignment.vertical = verticalAlign;
            }
        } else {
            if (default_cell_alignment != null) {
                cellAlignment.vertical = default_cell_alignment.vertical;
            } else {
                cellAlignment.vertical = "top";//No I18N
            }
        }

        if(indent != null)
        {
            cellAlignment.indent = indent;
        }

        if(rotationAngle != null)
        {
            int angle = Integer.parseInt(rotationAngle);
            if(angle < 0)
            {
                angle = 90 - angle;
            }
            cellAlignment.rotationAngle = String.valueOf(angle); //xlsx convention
        }

        return cellAlignment;
    }

    private int getNumFmtId(ZSPattern pattern) {
//        switch (type) {
//            case TIME:
//                return 18;
//            case DATETIME:
//                return 22;
//            case DATE:
//                return 15;
//            case BOOLEAN:
//                return 0;
//            case STRING:
//                return 0;
//            case FLOAT:
//                return 2;
//            case CURRENCY:
//                return 0;
//            case PERCENTAGE:
//                return 9;
//            case SCIENTIFIC:
//                return 48;
//            case FRACTION:
//                return 12;
//            case ERROR:
//            case UNDEFINED:
//        }

        if(pattern != null) {
            String s = pattern.toPatternString();
            if(optionalDecimal.matcher(s).matches() || "null".equals(s) || "BOOLEAN".equals(s)) {
                return 0;
            }
            switch (s) {
                case "":
                    return 0;
                case "###0.00":
                    return 2;
                case "#,##0":
                    return 3;
                case "#,##0.00":
                    return 4;
                case "###0.##%":
                case "###0%":
                    return 9;
                case "###0.00%":
                    return 10;
                case "0/0?":
                    return 12;
                case "0/0??":
                    return 13;
                case "0.00E0":
                    return 11;
            }
            if(optionalDecimalInPercentage.matcher(s).matches()) {
                return 9;
            }

            s = s.replaceAll("\\[[a-zA-Z]{2,}\\-[a-zA-Z]{2,}\\]","");
            final List<PatternComponent> componentList = pattern.getComponentList();
            if (componentList != null && !componentList.isEmpty()) {
                boolean isTextPattern = pattern.getType() != null && pattern.getType() == Cell.Type.STRING;
                String currencySymbol = null;
                for (PatternComponent patternComponent : componentList) {
                    if(isTextPattern && !(patternComponent instanceof QuottedText)) {
                        isTextPattern = false;
                    }
                    if (patternComponent instanceof CurrencyChar) {
                        final String target = patternComponent.toPattern(null, true);
                        String replacement = patternComponent.format(null);
                        if(replacement.equals("$")) {
                            replacement = "\""+ replacement + "\"";
                        } else {
                            replacement = "[$" + replacement + "]";
                        }
                        s = s.replace(target, replacement);
                        currencySymbol = replacement;
                    }
                }
                if (currencySymbol != null) {
                    if(s.equals("[>=0]"+currencySymbol+"#,##0.00;-"+currencySymbol+"#,##0.00")) {
                        if(currencySymbol.equals("[$$]")) {
                            s = "\"$\"#,##0.00";//No I18N
                        } else {
                            s = currencySymbol+"#,##0.00";//No I18N
                        }
                    } else if(s.equals("[>=0]"+currencySymbol+"#,##0;-"+currencySymbol+"#,##0")) {
                        if(currencySymbol.equals("[$$]")) {
                            s = "\"$\"#,##0";//No I18N
                        } else {
                            s = currencySymbol+"#,##0";//No I18N
                        }
                    }
                }
                if(isTextPattern) {
                    if(!s.startsWith("\"")) {
                        s = "\""+s+"\"";
                    }
                }
            }
            s = transformPatternString(pattern.getType(), s);
            s = Utility.xmlEncode(s);

            StylesBuilder.NumFmt numFmt = new StylesBuilder.NumFmt();
            numFmt.pattern = s;

            Integer integer = numFmts.get(numFmt);
            if(integer == null) {
                integer = 164+numFmts.size();
                numFmt.id = integer;
                numFmts.put(numFmt, integer);
            }

            return integer;
        }
        return 0;
    }

    private static String transformPatternString(Cell.Type type, String zsPatterString) {
            if(zsPatterString == null) {
                return zsPatterString;
            }

            switch (type) {
                case DATE:
                case DATETIME:
                    zsPatterString = zsPatterString.replace('M', 'm');//No I18N
                    zsPatterString = zsPatterString.replace('E', 'd');//No I18N
                case TIME:
                    if(!zsPatterString.isEmpty()) {
                        char am_pm_symbol = zsPatterString.charAt(zsPatterString.length() - 1);
                        if('a' == am_pm_symbol) {
                            return zsPatterString.substring(0, zsPatterString.length() - 1) + "AM/PM";//No I18N
                        }
                    }
                    zsPatterString = zsPatterString.replace(" a", "AM/PM");//No I18N
                    zsPatterString = zsPatterString.replace(" z","");//No I18N
                    break;
                case FRACTION:
                        switch (zsPatterString) {
                            case "0/0?":
                                return  "# ?/?";			// NO I18N
                            case "0/0??":
                                return  "# ??/??";			// NO I18N
                            case "0/0???":
                                return "# ???/???";			// NO I18N
                        }
                        break;
                case SCIENTIFIC:
                    zsPatterString = zsPatterString.replace("E", "E+");//No I18N
                    break;
            }
        return zsPatterString;
    };

    private int getFontIndex(CellStyle cellStyle, boolean cellHasHyperLink, boolean isDxf) {
        StylesBuilder.Font font = null;
        if(cellStyle !=null) {
            font = Font.getInstance(cellStyle, workbook, cellHasHyperLink, isDxf, default_font);
        }if (font == null) {
            if(isDxf) {
                return -1;
            }
            return 0;
        }

        Integer integer = fonts.get(font);
        if(integer == null) {
            integer = fonts.size();
            fonts.put(font, integer);
        }
        return integer;
    }

    private int getFillIndex(CellStyle cellStyle) {
        Fill fill = (Fill) cellStyle.getProperty(CellStyle.Property.FILL);
        if(fill == null) {
            if (this.isDxf) {
                return -1;
            }
            return 0;
        }

        else {
            if (fill instanceof PatternFill) {
                PatternFill patternFill = (PatternFill) fill;
                if (PatternFill.getInstance(PatternFill.PatternType.NONE, null, null).equals(patternFill)) {
                    if(this.isDxf) {
                        return -1;
                    }
                    return 0;
                }
            }
            //else if (fill instanceof GradientFill) {
            //}
        }

        Integer integer = fills.get(fill);
        if(integer == null) {
            integer = fills.size();
            fills.put(fill, integer);
        }
        return integer;
    }

    private int getBorderIndex(CellStyle cellStyle) {
        Function<BorderProperties, BorderPr> tRANSFORM = (c -> {
            if(c == null) {
                return null;
            }
            return BorderPr.build(c.getBorderStringZS());
        });

        StylesBuilder.Border border = new StylesBuilder.Border();
        border.left = tRANSFORM.apply(((BorderProperties)cellStyle.getProperty(CellStyle.Property.BORDERLEFT)));
        border.right = tRANSFORM.apply(((BorderProperties)cellStyle.getProperty(CellStyle.Property.BORDERRIGHT)));
        border.top = tRANSFORM.apply(((BorderProperties)cellStyle.getProperty(CellStyle.Property.BORDERTOP)));
        border.bottom = tRANSFORM.apply(((BorderProperties)cellStyle.getProperty(CellStyle.Property.BORDERBOTTOM)));

        border.diagonal = tRANSFORM.apply(((BorderProperties)cellStyle.getProperty(CellStyle.Property.DIAGONALTLBR)));
        if(border.diagonal != null) {
            border.diagonalDown = true;
        } else {
            border.diagonal = tRANSFORM.apply(((BorderProperties)cellStyle.getProperty(CellStyle.Property.DIAGONALBLTR)));
            if (border.diagonal != null) {
                border.diagonalUp = true;
            }
        }

        if(this.isDxf) {
            border.horizontal = tRANSFORM.apply(((BorderProperties)cellStyle.getProperty(CellStyle.Property.BORDERINNERHORIZONTAL)));
            border.vertical = tRANSFORM.apply(((BorderProperties)cellStyle.getProperty(CellStyle.Property.BORDERINNERVERTICAL)));
            if(border.left == null && border.right == null && border.top == null && border.bottom == null && border.diagonal == null && border.horizontal == null && border.vertical == null) {
                return -1;
            }
        }
        Integer integer = borders.get(border);
        if(integer == null) {
            integer = borders.size();
            borders.put(border, integer);
        }

        return integer;
    }
}
