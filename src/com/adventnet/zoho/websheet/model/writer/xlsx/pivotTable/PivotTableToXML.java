/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx.pivotTable;

import com.adventnet.zoho.websheet.model.SpreadsheetSettings;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Webhook;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.CalendarValue;
import com.adventnet.zoho.websheet.model.filter.util.FilterUtil;
import com.adventnet.zoho.websheet.model.filter.operator.FilterOperator;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.pivot.*;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.writer.xlsx.Utility;
import com.adventnet.zoho.websheet.model.writer.xlsx.WorkbookToXML;
import com.adventnet.zoho.websheet.model.writer.xlsx.XmlElementWriter;
import com.adventnet.zoho.websheet.model.xlsxaparser_.AttributeNameConstants;
import com.adventnet.zoho.websheet.model.xlsxaparser_.ElementNameConstants;

import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class PivotTableToXML {
    public static final Logger LOGGER = Logger.getLogger(PivotTableToXML.class.getName());
    private final OutputStream outputStream;

    /*
    pivotCache
    -----------
    <pivotCacheDefinition>
        <cacheField>
            <sharedItems/>
        </cacheField>
    </pivotCacheDefinition>
     */
    private final XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

    public PivotTableToXML(OutputStream outputStream) {
        this.outputStream = outputStream;
    }

    public void write_pivotTableDefinition(Workbook workbook, PivotTable pivotTable, Integer cachId, List<PivotField> cachedPivotFields) throws Exception {
        StringBuilder stringBuilder = new StringBuilder(WorkbookToXML.XML_VERSION_ENCODING);

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.PIVOTTABLEDEFINITION)
                .addAttribute(AttributeNameConstants.XMLNS, WorkbookToXML.XMLNS_URL)
                .addAttribute(AttributeNameConstants.NAME, pivotTable.getName())
                .addAttribute(AttributeNameConstants.CACHEID, cachId.toString())
                .addAttribute(AttributeNameConstants.DATACAPTION, "")//todo: find out the Value logic of this element
                .addAttribute(AttributeNameConstants.UPDATEDVERSION, "8")
                .addAttribute(AttributeNameConstants.CREATEDVERSION, "8")
                .addAttribute(AttributeNameConstants.COMPACT, "0")//Zoho Sheet only has classic view for PivotTable
                .addAttribute(AttributeNameConstants.COMPACTDATA, "0")//Zoho Sheet only has classic view for PivotTable
                .write(stringBuilder);
        this.outputStream.write(stringBuilder.toString().getBytes());


        write_location(pivotTable);

        stringBuilder = new StringBuilder();

        PivotComponent pivotComponent = pivotTable.getPivotComponent();

        if(pivotComponent == null) {
            JSONObjectWrapper jsonObj = new JSONObjectWrapper();
            jsonObj.put(JSONConstants.ID, pivotTable.getName());
            pivotComponent = PivotUtil.generatePivotComponent(workbook, jsonObj, pivotTable, false);
        }

        write_pivotFields(pivotTable.getSourceCellRange(workbook), pivotTable.getPivotFieldsForXLSX(), cachedPivotFields);

        write_rowFields(pivotTable.getPivotComponent().getRowList(), cachedPivotFields);

        write_colFields(pivotTable.getPivotComponent().getColumnList(), cachedPivotFields);

        write_PageFields(pivotTable.getPivotComponent().getPageList(), cachedPivotFields);

        write_dataFields(pivotTable.getPivotComponent().getDataList(), pivotTable.getSourceCellRange(workbook).getStartColIndex());

        write_customFilters(pivotTable, workbook);

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.PIVOTTABLEDEFINITION)
                .closeElement()
                .write(stringBuilder);

        this.outputStream.write(stringBuilder.toString().getBytes());
    }

    public static int getCacheIndex(PivotField field, List<PivotField> cachedFields)
    {
        //for Value field, Excel writes -2 as cache index.
        if(field.getColIndex() == -1)
        {
            return -2;
        }

        int i = 0;
        for(PivotField pf : cachedFields)
        {
            if(field.equalsForXLSXxml(pf))
            {
                return i;
            }
            i++;
        }

        throw new IllegalArgumentException("fields doesn't match"); //No I18N
    }

    private void write_PageFields(List<PivotField> pageFields, List<PivotField> cachedPivotFields) throws IOException {
        if(pageFields.isEmpty()) {
            return;
        }

        StringBuilder stringBuilder = new StringBuilder();
        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.PAGEFIELDS)
                .addAttribute(AttributeNameConstants.COUNT, String.valueOf(pageFields.size()))
                .write(stringBuilder);

        for (PivotField pageField : pageFields) {
            XmlElementWriter.newInstance()
                    .setElementName(ElementNameConstants.PAGEFIELD)
                    .addAttribute(AttributeNameConstants.FLD, String.valueOf(getCacheIndex(pageField, cachedPivotFields)))
                    .closeElement()
                    .write(stringBuilder);
        }

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.PAGEFIELDS)
                .closeElement()
                .write(stringBuilder);

        this.outputStream.write(stringBuilder.toString().getBytes());
    }

    private void write_location(PivotTable pivotTable) throws IOException {
        String targetCellRange = pivotTable.getTargetCellRange().getRangeString();

        StringBuilder stringBuilder = new StringBuilder();

        long noOfRows = pivotTable.getPivotComponent().getRowList().size();

        long noOfCols = pivotTable.getPivotComponent().getColumnList().size();


        xmlElementWriter
                .setElementName(ElementNameConstants.LOCATION)
                .addAttribute(AttributeNameConstants.REF, targetCellRange)
                .addAttribute(AttributeNameConstants.FIRSTHEADERROW, "1")//since Zoho Sheet doesn't support compact view, first-header will always be 1
                .addAttribute(AttributeNameConstants.FIRSTDATAROW, String.valueOf(noOfCols + 1))
                .addAttribute(AttributeNameConstants.FIRSTDATACOL, String.valueOf(noOfRows))
                .closeElement()
                .write(stringBuilder);


        this.outputStream.write(stringBuilder.toString().getBytes());
    }

    private void write_pivotFields(DataRange pivotCellRange, List<PivotField> pivotFields, List<PivotField> cachedPivotFields) throws IOException {

        StringBuilder stringBuilder = new StringBuilder();
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();


        xmlElementWriter.setElementName(ElementNameConstants.PIVOTFIELDS)
                .addAttribute(AttributeNameConstants.COUNT, String.valueOf(cachedPivotFields.size()))
                .write(stringBuilder);

        this.outputStream.write(stringBuilder.toString().getBytes());
        stringBuilder = new StringBuilder();

        int index = 0;
        for(PivotField cachedField : cachedPivotFields)
        {
            PivotField matchedPivotField = null;
            boolean isInDataToo = false;
            // if orientation is null, that means this is a dummy field. No pivot actually has a field for this column
            if(cachedField.getOrientation() != null) {
                int column = cachedField.getColIndex();
                for (PivotField pivotField : pivotFields) {
                    if(column == pivotField.getColIndex()) {
                        if ((matchedPivotField == null || matchedPivotField.getOrientation() == PivotField.Orientation.DATA)
                                && cachedField.equalsForXLSXxml(pivotField)) {
                            matchedPivotField = pivotField;
                        }

                        if (pivotField.getOrientation() == PivotField.Orientation.DATA) {
                            isInDataToo = true;
                        }
                    }
                }
            }

            if(matchedPivotField == null)
            {
                xmlElementWriter.addAttribute(AttributeNameConstants.SHOWALL, Utility.ZERO_STRING)
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.PIVOTFIELD);
                this.outputStream.write(stringBuilder.toString().getBytes());
                stringBuilder = new StringBuilder();
            }
            else {
                writePivotField(matchedPivotField, index < pivotCellRange.getColSize(), cachedField, isInDataToo);
            }

            index++;
        }
        // END OF GANESH ////////////////////////////////////////////////


        xmlElementWriter.setElementName(ElementNameConstants.PIVOTFIELDS)
                .closeElement()
                .write(stringBuilder);
        this.outputStream.write(stringBuilder.toString().getBytes());

    }

    private void writePivotField(PivotField curPivotField, boolean isFirstField, PivotField cacheField, boolean isInDataToo) throws IOException
    {
        StringBuilder stringBuilder = new StringBuilder();

        PivotSortInfo.Order order = curPivotField.getPivotLevel().getPivotSortInfo().getOrder();
        //Set<PivotField.Orientation> orientationSet = new HashSet<>();

        /*
        for (PivotField pivotField : pivotFields) {
            if (pivotField.getColIndex() == cachedField.getColIndex()) {
                if(pivotField.getOrientation() == PivotField.Orientation.DATA)
                {
                    orientationSet.add(pivotField.getOrientation());
                }
                else if (Objects.equals(pivotField.getPivotGroups(), cachedField.getPivotGroups()) && pivotField.getPeriodType() == cachedField.getPeriodType()) {
                    curPivotField = pivotField;
                    orientationSet.add(pivotField.getOrientation());
                    order = pivotField.getPivotLevel().getPivotSortInfo().getOrder();
                }
//                        if (orientation == PivotField.Orientation.PAGE) {
//                            selectedValuesInPageField.addAll(pivotField.getFilteredUniqueContentList());
//                        }
            }
        }
        */


        //if(curPivotField == null)
       // {
//            xmlElementWriter.addAttribute(AttributeNameConstants.SHOWALL, Utility.ZERO_STRING)
//                    .closeElementAndWrite(stringBuilder, ElementNameConstants.PIVOTFIELD);
//            return;
        //}

        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance()
            .setElementName(ElementNameConstants.PIVOTFIELD)
            .addAttribute(AttributeNameConstants.COMPACT, "0") //Zoho Sheet only has classic view for PivotTable
            .addAttribute(AttributeNameConstants.OUTLINE, "0")
            .addAttribute(AttributeNameConstants.SUBTOTALTOP, "0")
            .addAttribute(AttributeNameConstants.SHOWALL, "0")
            .addAttribute(AttributeNameConstants.MULTIPLE_ITEM_SELECTION_ALLOWED, Utility.ONE_STRING);

//                Set<String> selectedValuesInPageField = new HashSet<>();


        switch (curPivotField.getOrientation()) {
            case ROW:
                xmlElementWriter.addAttribute(AttributeNameConstants.AXIS, "axisRow");//No I18N
                break;
            case COLUMN:
                xmlElementWriter.addAttribute(AttributeNameConstants.AXIS, "axisCol");//No I18N
                break;
            case DATA:
                xmlElementWriter.addAttribute(AttributeNameConstants.DATAFIELD, "1");
                break;
            case PAGE:
                xmlElementWriter.addAttribute(AttributeNameConstants.AXIS, "axisPage");//No I18N
                break;
            case HIDDEN:
                break;
        }
        if(isInDataToo && curPivotField.getOrientation() != PivotField.Orientation.DATA)
        {
            xmlElementWriter.addAttribute(AttributeNameConstants.DATAFIELD, "1"); //No I18N
        }
        //}
        if (order != null) {
            switch (order) {
                case ASCENDING:
                    xmlElementWriter.addAttribute(AttributeNameConstants.SORTTYPE, "ascending");//No I18N
                    break;
                case DESCENDING:
                    xmlElementWriter.addAttribute(AttributeNameConstants.SORTTYPE, "descending");//No I18N
                    break;
            }
        }
        if (!isFirstField) {
            xmlElementWriter.addAttribute(AttributeNameConstants.DEFAULTSUBTOTAL, "0");
        }

        xmlElementWriter.write(stringBuilder);

        //Set<Integer> selectedValueIndexInPageField = new HashSet<>();
        List<Integer> indices = new ArrayList<>();
        List<Boolean> isVisibleList = new ArrayList<>();
        boolean isFieldgroup = (curPivotField.getPivotGroups() != null && curPivotField.getPivotGroups().getDateStart() == null)
                            || (curPivotField.getPeriodType() != null && curPivotField.getPeriodType() != CalendarValue.PeriodType.NONE);
        // Adding startValue field
        if(isFieldgroup)
        {
            indices.add(0);
            isVisibleList.add(true);
        }
        //////

        int skippedMembers = 0;
        Map<String, String> zsToXLSXEquivalents = curPivotField.getPeriodType().getZStoXLSXEquivalents();
        List<String> cacheMembers = new ArrayList<>();
        List<String> fieldMembers = new ArrayList<>();
        if(isFieldgroup && curPivotField.getPeriodType() != CalendarValue.PeriodType.YEAR)
        {
            cacheMembers.addAll(zsToXLSXEquivalents.values());
            fieldMembers.addAll(zsToXLSXEquivalents.keySet());
        }
        else {
            cacheMembers.addAll(cacheField.getPivotLevel().getPivotMembers().keySet());
            fieldMembers.addAll(curPivotField.getPivotLevel().getPivotMembers().keySet());
        }
        for (String fieldValue :  fieldMembers) {
            PivotMember member = curPivotField.getPivotLevel().getPivotMembers().get(fieldValue);
            String cacheValue = zsToXLSXEquivalents.get(fieldValue);
            cacheValue = cacheValue == null ? fieldValue : cacheValue;

            if (member == null || curPivotField.getDataPivotUniqueContentDetails(fieldValue) != null) {
                indices.add(cacheMembers.indexOf(cacheValue) - skippedMembers + (isFieldgroup ?  1 : 0));
                isVisibleList.add(member == null || member.getDisplay());
            }
            else
            {
                skippedMembers += 1;
            }
        }

        // Adding endValue field
        if(isFieldgroup)
        {
            indices.add(indices.size());
            isVisibleList.add(true);
        }
        ///////

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.ITEMS)
                .addAttribute(AttributeNameConstants.COUNT, String.valueOf(indices.size()))
                .write(stringBuilder);

        for (int j = 0; j < indices.size(); j++) {
            XmlElementWriter xew = XmlElementWriter.newInstance()
                    .setElementName(ElementNameConstants.ITEM)
                    .addAttribute(AttributeNameConstants.X, String.valueOf(j));
            if (!isVisibleList.get(j)) {
                xew.addAttribute(AttributeNameConstants.H, Utility.ONE_STRING);
            }
            //                    else if(!selectedValueIndexInPageField.isEmpty() && !selectedValueIndexInPageField.contains(j)) {
            //                        xew.addAttribute(AttributeNameConstants.H, Utility.ONE_STRING);
            //                    }
            xew.closeElement()
                    .write(stringBuilder);
        }

        if(isFirstField)
        {
            xmlElementWriter.setElementName(ElementNameConstants.ITEM)
                    .addAttribute(AttributeNameConstants.T, "default") //No I18N
                    .closeElement().write(stringBuilder);
        }

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.ITEMS)
                .closeElement()
                .write(stringBuilder);

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.PIVOTFIELD)
                .closeElement()
                .write(stringBuilder);

        this.outputStream.write(stringBuilder.toString().getBytes());
        //todo: take care of MergedRange In PivotTargetRange.
    }

    private void write_rowFields(List<PivotField> rowFields, List<PivotField> cachedPivotFields) throws IOException {
        if(rowFields.isEmpty()) {
            return;
        }

        StringBuilder stringBuilder = new StringBuilder();
        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.ROWFIELDS)
                .addAttribute(AttributeNameConstants.COUNT, String.valueOf(rowFields.size()))
                .write(stringBuilder);

        for (PivotField rowField : rowFields) {
            XmlElementWriter.newInstance()
                    .setElementName(ElementNameConstants.FIELD)
                    .addAttribute(AttributeNameConstants.X, String.valueOf(getCacheIndex(rowField, cachedPivotFields)))
                    .closeElement()
                    .write(stringBuilder);
        }

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.ROWFIELDS)
                .closeElement()
                .write(stringBuilder);

        this.outputStream.write(stringBuilder.toString().getBytes());
    }

    private void write_colFields(List<PivotField> colFields, List<PivotField> cachedPivotFields) throws IOException {
        if(colFields.isEmpty()) {
            return;
        }

        StringBuilder stringBuilder = new StringBuilder();
        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.COLFIELDS)
                .addAttribute(AttributeNameConstants.COUNT, String.valueOf(colFields.size()))
                .write(stringBuilder);

        for (PivotField colField : colFields) {
            XmlElementWriter.newInstance()
                    .setElementName(ElementNameConstants.FIELD)
                    .addAttribute(AttributeNameConstants.X, String.valueOf(getCacheIndex(colField, cachedPivotFields)))
                    .closeElement()
                    .write(stringBuilder);
        }

        XmlElementWriter.newInstance()
                .setElementName(ElementNameConstants.COLFIELDS)
                .closeElement()
                .write(stringBuilder);

        this.outputStream.write(stringBuilder.toString().getBytes());
    }

    private void write_dataFields(List<PivotField> dataFields, int startColIndex) throws IOException {
        if(dataFields.isEmpty()) {
            return;
        }

        StringBuilder sB = new StringBuilder();

        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter
                .setElementName(ElementNameConstants.DATAFIELDS)
                .addAttribute(AttributeNameConstants.COUNT, String.valueOf(dataFields.size()))
                .write(sB);

        for(PivotField fld: dataFields) {

            xmlElementWriter
                    .setElementName(ElementNameConstants.DATAFIELD)
                    .addAttribute(AttributeNameConstants.FLD, String.valueOf(fld.getColIndex()-startColIndex));
            final PivotField.Function function = fld.getFunction();
            String subTotalFunName = null;
            if(function != null) {
                switch (function) {
                    case AUTO:
                        subTotalFunName = "sum";//No I18N
                        break;
                    case AVERAGE:
                        subTotalFunName = "average";//No I18N
                        break;
                    case COUNT:
                        subTotalFunName = "count";//No I18N
                        break;
                    case COUNTNUMS:
                        subTotalFunName = "countNums";//No I18N
                        break;
                    case MAX:
                        subTotalFunName = "max";//No I18N
                        break;
                    case MIN:
                        subTotalFunName = "min";//No I18N
                        break;
                    case PRODUCT:
                        subTotalFunName = "product";//No I18N
                        break;
                    case STDEV:
                        subTotalFunName = "stdDev";//No I18N
                        break;
                    case STDEVP:
                        subTotalFunName = "stdDevp";//No I18N
                        break;
                    case SUM:
                        subTotalFunName = "sum";//No I18N
                        break;
                    case VAR:
                        subTotalFunName = "var";//No I18N
                        break;
                    case VARP:
                        subTotalFunName = "varp";//No I18N
                        break;
//                    case MEDIAN:
//                        subTotalFunName = "sum";
//                        break;
                }
            }
            if(fld.getSourceFieldName() != null) {
                StringBuilder stringBuilder = new StringBuilder();
                String modifiedSubTotalFunName = subTotalFunName == null ? "sum" : subTotalFunName;//No I18N
                stringBuilder
                        .append(modifiedSubTotalFunName.substring(0,1).toUpperCase().concat(modifiedSubTotalFunName.substring(1)))
                        .append(" of ")
                        .append(fld.getSourceFieldName());
                xmlElementWriter.addAttribute(AttributeNameConstants.NAME, Utility.xmlEncode(stringBuilder.toString()));
                if (subTotalFunName != null) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.SUBTOTAL, subTotalFunName);
                }
            }
            xmlElementWriter.closeElement()
                    .write(sB);
        }

        xmlElementWriter
                .setElementName(ElementNameConstants.DATAFIELDS)
                .closeElement()
                .write(sB);
        this.outputStream.write(sB.toString().getBytes());
    }

    private void write_customFilters(PivotTable pivotTable, Workbook workbook) throws IOException {
        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        StringBuilder eachfilter = new StringBuilder();

        int count = 0;
        for(PivotField pv : pivotTable.getPivotFieldsWithCustomFilter())
        {
            PivotFilterInfo.PivotFilterType filterType = PivotFilterInfo.PivotFilterType.getFilterTypeFromInt(pv.getPivotLevel().getPivotFilterInfo().getFilterType());
            if( pv.getColRangeType() == PivotUtil.FieldType.Td && filterType == PivotFilterInfo.PivotFilterType.LABEL)
            {
                try {

                    PivotFilterInfo pivotFilterInfo = pv.getPivotLevel().getPivotFilterInfo();
                    FilterOperator filterOperator = FilterUtil.getFilterOperator(pivotFilterInfo.getOperator());

                    String val = String.valueOf(DateUtil.convertDateToNumber(pivotFilterInfo.getDateValue()));


                    xmlElementWriter.setElementName(ElementNameConstants.FILTER)
                            .addAttribute(AttributeNameConstants.FLD, String.valueOf(pv.getColIndex() - pivotTable.getSourceCellRange(workbook).getStartColIndex()))
                            .addAttribute(AttributeNameConstants.TYPE, filterOperator.getXlsxFilterType(pv.getColRangeType(), filterType))
                            //.addAttribute(AttributeNameConstants.EVAL_ORDER, String.valueOf(-1))
                            .addAttribute(AttributeNameConstants.ID, String.valueOf(count + 1));

                    xmlElementWriter.write(eachfilter);

                    xmlElementWriter.setElementName(ElementNameConstants.AUTOFILTER)
                            //.addAttribute(AttributeNameConstants.REF, CellReference.ReferenceMode.A1.name())
                            .write(eachfilter);

                    xmlElementWriter.setElementName(ElementNameConstants.FILTERCOLUMN)
                            .addAttribute(AttributeNameConstants.COLD, String.valueOf(pv.getColIndex()))
                            .write(eachfilter);

                    xmlElementWriter.setElementName(ElementNameConstants.CUSTOMFILTERS);

                    if (filterOperator == FilterOperator.BETWEEN) {
                        xmlElementWriter.addAttribute(AttributeNameConstants.AND, String.valueOf(1))
                                .write(eachfilter)
                                .setElementName(ElementNameConstants.CUSTOMFILTER)
                                .addAttribute(AttributeNameConstants.OPERATOR, "greaterThanOrEqual") //No I18N
                                .addAttribute(AttributeNameConstants.VAL, val)
                                .closeEmptyElement()
                                .write(eachfilter);

                        String val1 = String.valueOf(DateUtil.convertDateToNumber(pivotFilterInfo.getDateValue1()));
                        xmlElementWriter.setElementName(ElementNameConstants.CUSTOMFILTER)
                                .addAttribute(AttributeNameConstants.OPERATOR, "lessThanOrEqual") //No I18N
                                .addAttribute(AttributeNameConstants.VAL, val1)
                                .closeEmptyElement()
                                .write(eachfilter);
                    } else {
                        xmlElementWriter.write(eachfilter)
                                .setElementName(ElementNameConstants.CUSTOMFILTER);
                        String xlsxFilterOperator = filterOperator.getZSToXlsxFilterOperator();
                        if (xlsxFilterOperator != null) {
                            xmlElementWriter.addAttribute(AttributeNameConstants.OPERATOR, xlsxFilterOperator);
                        }
                        xmlElementWriter.addAttribute(AttributeNameConstants.VAL, val)
                                .closeEmptyElement()
                                .write(eachfilter);
                    }

                    xmlElementWriter.closeElementAndWrite(eachfilter, ElementNameConstants.CUSTOMFILTERS);

                    xmlElementWriter.closeElementAndWrite(eachfilter, ElementNameConstants.FILTERCOLUMN);
                    xmlElementWriter.closeElementAndWrite(eachfilter, ElementNameConstants.AUTOFILTER);
                    xmlElementWriter.closeElementAndWrite(eachfilter, ElementNameConstants.FILTER);
                    count += 1;
                }catch(Exception e)
                {
                    LOGGER.log(Level.INFO, "Unable to create custom filter", e); //No I18N
                }
            }
        }

        if(count > 0) {
            StringBuilder filters = new StringBuilder();
            xmlElementWriter.setElementName(ElementNameConstants.FILTERS)
                    .addAttribute(AttributeNameConstants.COUNT, String.valueOf(count))
                    .write(filters);
            filters.append(eachfilter);

            xmlElementWriter.closeElementAndWrite(filters, ElementNameConstants.FILTERS);
            this.outputStream.write(filters.toString().getBytes());
        }
    }

}
