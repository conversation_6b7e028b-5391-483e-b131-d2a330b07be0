/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.Expression;
import com.adventnet.zoho.websheet.model.Table;
import com.adventnet.zoho.websheet.model.TableColumn;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.xlsxaparser_.AttributeNameConstants;
import com.adventnet.zoho.websheet.model.xlsxaparser_.ElementNameConstants;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipOutputStream;

public class TableToXml {
    private ZipOutputStream zipOutputStream;
    private Table table;
    private XlsxConversionStats xlsxConversionStats;
    private StylesBuilder.Font default_font;
    public static final Logger LOGGER = Logger.getLogger(TableToXml.class.getName());
    public TableToXml(ZipOutputStream zipOutputStream, Table table, XlsxConversionStats xlsxConversionStats, StylesBuilder.Font default_font) {

        this.zipOutputStream = zipOutputStream;
        this.table = table;
        this.xlsxConversionStats = xlsxConversionStats;
        this.default_font = default_font;
    }

    public void write(int tableXmlId) throws IOException {
        StringBuilder stringBuilder = new StringBuilder(WorkbookToXML.XML_VERSION_ENCODING);
        final XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        String name = this.table.getName();
        if (name == null) {
            name = "Table"+tableXmlId;//No I18N
        }
        String reference = null;
        try {
            reference = Utility.toString(new DataRange(null, table.getStartRowIndex(), table.getStartColIndex(), table.getEndRowIndex(), table.getEndColIndex()), true, true);
        } catch (Exception e) {
            LOGGER.log(
                    Level.OFF,
                    String.join(",",String.valueOf(table.getStartRowIndex()), String.valueOf(table.getStartColIndex()), String.valueOf(table.getEndRowIndex()), String.valueOf(table.getEndColIndex())),
                    e);
            throw e;
        }
        xmlElementWriter
                .addAttribute(AttributeNameConstants.XMLNS, WorkbookToXML.XMLNS_URL)
                .addAttribute(AttributeNameConstants.ID, String.valueOf(tableXmlId))
                .addAttribute(AttributeNameConstants.NAME, name)
                .addAttribute(AttributeNameConstants.DISPLAY_NAME, name)
                .addAttribute(AttributeNameConstants.REF, reference);
        if (table.isFooterRowShown()) {
            xmlElementWriter.addAttribute(AttributeNameConstants.TOTALS_ROW_COUNT, Utility.ONE_STRING);
        }
        xmlElementWriter.write(stringBuilder, ElementNameConstants.TABLE);

        FilterView filterView = table.getFilterView();
        Workbook workbook = table.getSheet().getWorkbook();
        if(filterView != null) {
            AutoFilterToXML.write_autoFilter(stringBuilder, filterView, workbook.getTheme(), new StylesBuilder(true, xlsxConversionStats, workbook.getTheme(), default_font, StylesBuilder.getCellAlignment(workbook.getCellStyle("Default"), false, null), workbook)); //No I18N
        }
        xmlElementWriter
                .addAttribute(AttributeNameConstants.COUNT, String.valueOf(this.table.getTableColumns().size()))
                .write(stringBuilder, ElementNameConstants.TABLECOLUMNS);
        for(int i = 0; i < this.table.getTableColumns().size(); i++) {
            final TableColumn tableColumn = this.table.getTableColumns().get(i);
            xmlElementWriter
                    .addAttribute(AttributeNameConstants.ID, String.valueOf((i+1)))
                    .addAttribute(AttributeNameConstants.NAME, Utility.xmlEncode(tableColumn.getColumnHeader()));
            Expression footerExpression = tableColumn.getFooterExpression();
            if(footerExpression == null && table.isFooterRowShown()) {
                footerExpression = table.getSheet().getCell(table.getEndRowIndex(), table.getStartColIndex()+i).getExpression();
            }

            if(tableColumn.getFooterLabel() != null) {
                xmlElementWriter.addAttribute(AttributeNameConstants.TOTALS_ROW_LABEL, tableColumn.getFooterLabel());
            }
            if(footerExpression == null && tableColumn.getCalculatedExpression() == null) {
                xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.TABLECOLUMN);
            } else {
                if(footerExpression != null) {
                    //todo: handle non custom function - now these are also written as custom
                    xmlElementWriter.addAttribute(AttributeNameConstants.TOTALS_ROW_FUNCTION, "custom");//No I18N
                }
                xmlElementWriter.write(stringBuilder, ElementNameConstants.TABLECOLUMN);
                if(tableColumn.getCalculatedExpression() != null) {
                    String formula = Utility.xmlEncode(Utility.toString(tableColumn.getCalculatedExpression().getNode(), table.getSheet().getWorkbook(), 0, 0, false, null));
                    xmlElementWriter.addValue(formula);
                    xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.CALCULATED_COLUMN_FORMULA);
                }
                if(footerExpression != null) {
                    String formula = Utility.xmlEncode(Utility.toString(footerExpression.getNode(), table.getSheet().getWorkbook(), 0, 0, false, null));
                    xmlElementWriter.addValue(formula);
                    xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.TABLE_TOTALS_ROW_FORMULA);
                }
                xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.TABLECOLUMN);
            }
        }
        xmlElementWriter
                .closeElementAndWrite(stringBuilder, ElementNameConstants.TABLECOLUMNS);

        if(table.getTableStyleName() != null && !table.getTableStyleName().isEmpty()) {
            xmlElementWriter
                    .addAttribute(AttributeNameConstants.NAME, table.getTableStyleName());
        }
        xmlElementWriter
                .addAttribute(AttributeNameConstants.SHOW_FIRST_COLUMN, Utility.stringValueOf.apply(this.table.isShowFirstColumn()))
                .addAttribute(AttributeNameConstants.SHOW_LAST_COLUMN, Utility.stringValueOf.apply(this.table.isShowLastColumn()))
                .addAttribute(AttributeNameConstants.SHOW_ROW_STRIPES, Utility.stringValueOf.apply(this.table.isShowRowStripes()))
                .addAttribute(AttributeNameConstants.SHOW_COLUMN_STRIPES, Utility.stringValueOf.apply(this.table.isShowColumnStripes()))
                .closeElementAndWrite(stringBuilder, ElementNameConstants.TABLE_STYLEINFO);

        xmlElementWriter
                .closeElementAndWrite(stringBuilder, ElementNameConstants.TABLE);
        this.zipOutputStream.write(stringBuilder.toString().getBytes());
    }
}
