/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.ZSDate;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.ext.parser.ASTParseErrorNode;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.style.RowStyle;
import com.adventnet.zoho.websheet.model.style.SheetStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.model.writer.xlsx.image.DrawingProperties;
import com.adventnet.zoho.websheet.model.writer.xlsx.image.XlsxWriterImageContainer;
import com.adventnet.zoho.websheet.model.writer.xlsx.preprocessing.PreprocessedCell;
import com.adventnet.zoho.websheet.model.writer.xlsx.preprocessing.PreprocessedCol;
import com.adventnet.zoho.websheet.model.writer.xlsx.preprocessing.PreprocessedRow;
import com.adventnet.zoho.websheet.model.writer.xlsx.preprocessing.PreprocessedSheet;
import com.adventnet.zoho.websheet.model.xlsxaparser_.AttributeNameConstants;
import com.adventnet.zoho.websheet.model.xlsxaparser_.ElementNameConstants;
import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXMLDefaults;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.container.ChartContainer;

import java.io.OutputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class SheetToXml {

    private final OutputStream outputStream;
    final Map<XlsxString, Integer> sharedStrings = new LinkedHashMap<>();
    final StylesBuilder stylesBuilder;
    final StylesBuilder dxfBuilder;
    private final List<XmlElementWriter> contentTypesOverrideElements;
    private final XlsxWriterImageContainer xlsxWriterImageContainer;

    private List<Cell> annotatedCells = new ArrayList<>();
    private List<Cell> cellsHavingLinks = new ArrayList<>();
    private List<XmlElementWriter> sheetXmlRelsRelationshipElements;
    private RelationshipHelper relationshipHelper;
    private boolean isDynamicArrayFormulaPresent = false;
    private final XlsxConversionStats xLSX_CONVERSION_STATS;
    private final XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();

    public static final Logger LOGGER = Logger.getLogger(SheetToXml.class.getName());
    public SheetToXml(OutputStream outputStream, List<XmlElementWriter> contentTypesOverrideElements, XlsxWriterImageContainer xlsxWriterImageContainer, XlsxConversionStats xlsxConversionStats, ZSTheme theme, StylesBuilder.Font default_font, StylesBuilder.CellAlignment cellAlignment, Workbook workbook) {
        this.outputStream = outputStream;
        this.contentTypesOverrideElements = contentTypesOverrideElements;
        this.xlsxWriterImageContainer = xlsxWriterImageContainer;
        this.xLSX_CONVERSION_STATS = xlsxConversionStats;
        this.stylesBuilder = new StylesBuilder(false, xlsxConversionStats, theme, default_font, cellAlignment,workbook);
        this.dxfBuilder = new StylesBuilder(true, xlsxConversionStats, theme, default_font, cellAlignment,workbook);
    }

    void write_sheet(Sheet sheet, List<XmlElementWriter> sheetXmlRelsRelationshipElements, RelationshipHelper relationshipHelper) throws Exception {
        this.annotatedCells = new ArrayList<>();
        this.cellsHavingLinks = new ArrayList<>();
        this.sheetXmlRelsRelationshipElements = sheetXmlRelsRelationshipElements;
        this.relationshipHelper = relationshipHelper;

        this.xLSX_CONVERSION_STATS.addStats(PreprocessedSheet.class.getSimpleName());

        final PreprocessedSheet preprocessedSheet = new PreprocessedSheet(this.stylesBuilder);
        try {
            Map<Integer, Integer> colOutlineLevelFlatMap = new HashMap<>();
            List<Integer> columnCollapsedIndices = new ArrayList<>();
            for (List<Group> groups : sheet.getColGroups()) {
                for (Group group : groups) {
                    if (group.isCollapse()) {
                        int collapsedIndex = sheet.isSummaryRight() ? group.getEndIndex() + 1 : group.getStartIndex() - 1;
                        sheet.getColumnHeader(collapsedIndex);
                        if (sheet.isSummaryRight()) {
                            //In xlsx, Collapse col should be a single col, its repeated must be given to next col by creating it
                            sheet.getColumnHeader(collapsedIndex + 1);
                        }
                        columnCollapsedIndices.add(collapsedIndex);
                    }
                    IntStream.rangeClosed(group.getStartIndex(), group.getEndIndex())
                            .forEach(i -> colOutlineLevelFlatMap.put(i, colOutlineLevelFlatMap.getOrDefault(i, 0) + 1));
                }
            }

            Map<Integer, Integer> rowOutlineLevelFlatMap = new HashMap<>();
            List<Integer> rowCollapsedIndices = new ArrayList<>();
            for (List<Group> groups : sheet.getRowGroups()) {
                for (Group group : groups) {
                    if (group.isCollapse()) {
                        int collapsedIndex = sheet.isSummaryBelow() ? group.getEndIndex() + 1 : group.getStartIndex() - 1;
                        sheet.getRow(collapsedIndex);
                        if (sheet.isSummaryBelow()) {
                            //In xlsx, Collapse row should be a single row, its repeated must be given to next row by creating it
                            sheet.getRow(collapsedIndex + 1);
                        }
                        rowCollapsedIndices.add(collapsedIndex);
                    }
                    IntStream.rangeClosed(group.getStartIndex(), group.getEndIndex())
                            .forEach(i -> rowOutlineLevelFlatMap.put(i, rowOutlineLevelFlatMap.getOrDefault(i, 0) + 1));
                }
            }

            preprocessedSheet.preprocess(sheet);

            this.xLSX_CONVERSION_STATS.addStats(sheet.getName());
            StringBuilder stringBuilder = new StringBuilder(WorkbookToXML.XML_VERSION_ENCODING);
            String[] keys = new String[]{
                    AttributeNameConstants.XMLNS,
                    AttributeNameConstants.XMLNS_R,
                    "xmlns:xdr",//No I18N
                    "xmlns:x14"//No I18N
            };
            String[] values = new String[]{
                    WorkbookToXML.XMLNS_URL,
                    WorkbookToXML.XMLNS_REL_URL,
                    "http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing",//No I18N
                    "http://schemas.microsoft.com/office/spreadsheetml/2009/9/main"//No I18N
            };

            stringBuilder.append(Utility.getTag(ElementNameConstants.WORKSHEET, keys, values, false));
            writeSheetPr(sheet, stringBuilder);
            boolean sheetHasSlicers = false;
            boolean sheetHasTimelines = false;
            boolean sheetHasChart = false;
            ConditionalFormatToXML conditionalFormatToXML = new ConditionalFormatToXML();
            {

                keys = new String[]{
                        AttributeNameConstants.REF
                };

                String dimension;
                if (sheet.getUsedRowIndex() == 0 && sheet.getUsedColumnIndex() == 0) {
                    dimension = "A1";//No I18N
                } else {
                    dimension = "A1:" + CellUtil.getCellReference(sheet.getUsedColumnIndex(), sheet.getUsedRowIndex());//No I18N
                }
                values = new String[]{
                        dimension
                };
                stringBuilder.append(Utility.getTag(ElementNameConstants.DIMENSION, keys, values, true));

                stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.SHEETVIEWS));

                XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance(ElementNameConstants.SHEETVIEW)
                        .addAttribute(AttributeNameConstants.WORKBOOKVIEWID, String.valueOf(0));
                String workbookGridColor = ActionUtil.getWorkbookGridColorHexCode(sheet.getWorkbook());
                int workbookGridColorIndex = !workbookGridColor.isEmpty() ? XLSXMLDefaults.getNearestColorIndex(workbookGridColor) : -1;
                String sheetGridColor = ActionUtil.getSheetGridColorHexCode(sheet);
                int sheetGridColorIndex = !sheetGridColor.isEmpty() ? XLSXMLDefaults.getNearestColorIndex(sheetGridColor) : -1;
                if(sheetGridColorIndex != -1) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.DEFAULT_GRID_COLOR, String.valueOf(0));
                    xmlElementWriter.addAttribute(AttributeNameConstants.COLOR_ID, String.valueOf(sheetGridColorIndex));
                }
                else if (workbookGridColorIndex != -1)
                {
                    xmlElementWriter.addAttribute(AttributeNameConstants.DEFAULT_GRID_COLOR, String.valueOf(0));
                    xmlElementWriter.addAttribute(AttributeNameConstants.COLOR_ID, String.valueOf(workbookGridColorIndex));
                }
                if (sheet.getSheetStyle() != null
                        && sheet.getSheetStyle().getProperty(SheetStyle.Property.WRITINGMODE) != null
                        && "rl-tb".equals(sheet.getSheetStyle().getProperty(SheetStyle.Property.WRITINGMODE))) {//No I18N
                    xmlElementWriter.addAttribute(AttributeNameConstants.RTL, "1");//No I18N
                }
                final WorkbookSettings workbookSettings = sheet.getWorkbook().getWorkbookSettings();
                if (!workbookSettings.isShowGrid(sheet.getName())) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.SHOWGRIDLINES, Utility.ZERO_STRING);
                }
                final int freezePaneColumnEnd = workbookSettings.getFreezePaneColumnEnd(sheet.getName());
                final int freezePaneRowEnd = workbookSettings.getFreezePaneRowEnd(sheet.getName());
                if (freezePaneColumnEnd > 0 || freezePaneRowEnd > 0) {
                    xmlElementWriter.write(stringBuilder);
                    if (freezePaneColumnEnd > -1) {
                        xmlElementWriter.addAttribute(AttributeNameConstants.XSPLIT, String.valueOf(freezePaneColumnEnd + 1));
                    }
                    if (freezePaneRowEnd > -1) {
                        xmlElementWriter.addAttribute(AttributeNameConstants.YSPLIT, String.valueOf(freezePaneRowEnd + 1));
                    }
                    String topLeftCell = CellUtil.getCellReference(freezePaneColumnEnd > -1 ? freezePaneColumnEnd + 1 : 0, freezePaneRowEnd > -1 ? freezePaneRowEnd + 1 : 0);
                    xmlElementWriter.addAttribute(AttributeNameConstants.TOPLEFTCELL, topLeftCell);
                    xmlElementWriter.addAttribute(AttributeNameConstants.STATE, "frozen");//No I18N
                    xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.PANE);

                    xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.SHEETVIEW);
                } else {
                    xmlElementWriter.closeElement()
                            .write(stringBuilder);
                }



                stringBuilder.append(Utility.getCloseTag(ElementNameConstants.SHEETVIEWS));

//        keys = new String[]{
//                AttributeNameConstants.BASECOLWIDTH,
//                AttributeNameConstants.DEFAULTROWHEIGHT
//        };
//        values = new String[] {
//                String.valueOf(10),
//                String.valueOf(16)
//        };
//
//        stringBuilder.append(Utility.getTag(ElementNameConstants.SHEETFORMATPR, keys, values, true));

                if (preprocessedSheet.height != 0) {
                    XmlElementWriter.newInstance()
                            .addAttribute(AttributeNameConstants.DEFAULTROWHEIGHT, String.valueOf(getHeightInPt_ImportReverseCalculation(preprocessedSheet.height)))
                            .addAttribute(AttributeNameConstants.CUSTOMHEIGHT, "1")
                            .closeElementAndWrite(stringBuilder, ElementNameConstants.SHEETFORMATPR);
                }

                write_cols(stringBuilder, sheet.getColumnHeaders(), preprocessedSheet, colOutlineLevelFlatMap, columnCollapsedIndices);

                if (sheet.getRows().isEmpty() && sheet.getUsedColumnIndex() == 0) {
                    stringBuilder.append(Utility.getTag(ElementNameConstants.SHEETDATA, new String[]{}, new String[]{}, true));
                } else {
                    stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.SHEETDATA));
                    final List<PivotTable> pivotTables = sheet.getWorkbook().getPivotTables();
                    List<DataRange> pivotTargetRanges;
                    if (pivotTables == null) {
                        pivotTargetRanges = new ArrayList<>();
                    } else {
                        pivotTargetRanges = pivotTables
                                .stream()
                                .map(pt -> pt.getTargetCellRange())
                                .filter(tr -> tr.getAssociatedSheetName().equals(sheet.getAssociatedName()))
                                .collect(Collectors.toList());
                    }

                    final int rowSize = sheet.getRows().size();
                    int rowIndex = 0;
                    while (rowIndex < rowSize && rowIndex < com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFROWS) {
                        final Row row = sheet.getRows().get(rowIndex);
                        if (row == null) {
                            rowIndex += 1;
                            continue;
                        }
                        final PreprocessedRow preprocessedRow = preprocessedSheet.preprocessedRows.get(rowIndex);
                        final int rowsRepeated = row.getRowsRepeated();
                        boolean isRowNeedToBeWrittenForStyle = false;
                        if (preprocessedRow.xf_index > 0) {
                            if (preprocessedRow.xf_index != preprocessedSheet.xf_index) {
                                isRowNeedToBeWrittenForStyle = true;
                            } else if (preprocessedRow.hasManyCellStyles) {
                                isRowNeedToBeWrittenForStyle = true;
                            }
                        } else if (preprocessedRow.hasCellStyle) {
                            isRowNeedToBeWrittenForStyle = true;
                        }
                        if (preprocessedRow.hasValue || isRowNeedToBeWrittenForStyle || preprocessedRow.height != preprocessedSheet.height || rowOutlineLevelFlatMap.get(row.getRowIndex()) != null || rowCollapsedIndices.contains(row.getRowIndex())) {
                            int size = Math.min(rowIndex + rowsRepeated, com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFROWS);
//                            if ((size == com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFROWS || (rowIndex == rowSize - 1)) && !preprocessedRow.hasValue && (rowIndex + 1000) < size) {
//                                size = rowIndex + 1000;//writing only 1000 repetition of last row.
//                            }
                            if(!preprocessedRow.hasValue) {
                                if(rowsRepeated > 2000) {
                                    size = rowIndex+1;
                                }
                            }
                            for (int j = rowIndex; j < size; j++) {
                                write_row(stringBuilder, row, j, pivotTargetRanges, preprocessedRow, preprocessedSheet, rowOutlineLevelFlatMap, rowCollapsedIndices);
                            }
                        }
                        rowIndex += rowsRepeated;
                    }

                    stringBuilder.append(Utility.getCloseTag(ElementNameConstants.SHEETDATA));
                }
                {
                    final FilterView filterView = sheet.getFilterView(EngineConstants.DEFAULT_SHEET_FILTER_ID);
                    final ZSTheme theme = sheet.getWorkbook().getTheme();
                    AutoFilterToXML.write_autoFilter(stringBuilder, filterView, theme, dxfBuilder);
//                if(filterTable != null) {
//                    final FilterRange filterRange = filterView.getFilterRange();
//                    boolean tableFilter = false;
//                    if(filterRange != null) {
//                        final Range range = filterRange.getRange();
//                        if (sheet.getUserTables() != null) {
//                            for (Table value : sheet.getUserTables().values()) {
//                                if(value.getStartColIndex() >= range.getStartColIndex() && value.getStartColIndex() <= range.getEndColIndex() &&
//                                        value.getStartRowIndex() >= range.getStartRowIndex() && value.getStartRowIndex() <= range.getEndRowIndex()) {
//                                    tableFilter = true;
//                                    break;
//                                }
//                            }
//                        }
//                    }
//                    if (!tableFilter) {
//                        AutoFilterToXML.write_autoFilter(stringBuilder, filterRange, dxfBuilder);
//                    }
//                }
                }
                write_mergeCells(sheet.getWorkbook(), stringBuilder, sheet.getMergeCellDetails().values());
                conditionalFormatToXML.write_conditionalFormats(stringBuilder, sheet, dxfBuilder);

                DataValidationsToXML.write_dataValidations(sheet, stringBuilder, sheet.getContentValidationRanges());

                for(Slicer slicer : sheet.getWorkbook().getPivotSlicers())
                {
                    if(slicer.getPosition().getAssociatedSheetName().equals(sheet.getAssociatedName()))
                    {
                        sheetHasSlicers = true;
                        break;
                    }
                }

                for(TimeLineSlicer timeline : sheet.getWorkbook().getTimelines())
                {
                    if(timeline.getPosition().getAssociatedSheetName().equals(sheet.getAssociatedName()))
                    {
                        sheetHasTimelines = true;
                        break;
                    }
                }

                ChartContainer chartContainer = sheet.getWorkbook().getChartsContainer();

                sheetHasChart = !chartContainer.isEmpty(sheet.getAssociatedName()) || !(sheet.getWorkbook().getChartMap() == null || sheet.getWorkbook().getChartMap().get(sheet.getAssociatedName()) == null);

                writeHyperLinks(stringBuilder);
                writeDrawingElement(stringBuilder, sheet, sheetHasChart, sheetHasSlicers, sheetHasTimelines);
                writelegacyDrawing(stringBuilder);
            }
            writeTableParts(stringBuilder, sheet.getTables(false));
            //writeButtons(stringBuilder, sheet.getForms(), sheet.getDrawControlShapes());
            final XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
            xmlElementWriter.write(stringBuilder, ElementNameConstants.EXTLST);
            if(sheetHasSlicers) {
                writeSlicerRid(stringBuilder);
            }
            if(sheetHasTimelines) {
                writeTimelineRid(stringBuilder);
            }
            conditionalFormatToXML.write_x14conditionalFormattings(stringBuilder, sheet);

            if (sheet.getSparklinesGroupList() != null && !sheet.getSparklinesGroupList().isEmpty()) {
                writeSparkLineGroups(stringBuilder, sheet.getSparklinesGroupList(), sheet.getWorkbook());
            }
            xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.EXTLST);
            stringBuilder.append(Utility.getCloseTag(ElementNameConstants.WORKSHEET));
            this.outputStream.write(stringBuilder.toString().getBytes());
        } finally {
            for (ExpressionImpl modifiedExpression : preprocessedSheet.modifiedExpressions) {
                modifiedExpression.setNameToWrite_TEMP(null);
            }
            this.xLSX_CONVERSION_STATS.addStats(null);
        }
    }

    private void writeButtons(StringBuilder sb, Forms forms, List<DrawControl> drawControlShapes) {
        if(drawControlShapes.isEmpty()) {
           return;
        }

        XmlElementWriter w = XmlElementWriter.newInstance();
        w.addAttribute("xmlns:mc", "http://schemas.openxmlformats.org/markup-compatibility/2006")//No I18N
                .write(sb, "mc:AlternateContent")//No I18N
                .addAttribute("Requires", "x14")//No I18N
                .write(sb, "mc:Choice")//No I18N
                .write(sb, "controls");//No I18N
        for(int i = 0; i < drawControlShapes.size(); i++) {
            DrawControl drawControlShape = drawControlShapes.get(i);

            String rId = "rId"+(sheetXmlRelsRelationshipElements.size()+1);//No I18N
            int ctrlPropXmlId = this.relationshipHelper.ctrlPropXmlId++;
            contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.PARTNAME, "/xl/ctrlProps/ctrlProp"+ctrlPropXmlId+".xml")//No I18N
                    .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.ms-excel.controlproperties+xml"));//No I18N
            sheetXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.TARGET, "../ctrlProps/ctrlProp"+ ctrlPropXmlId +".xml")//No I18N
                    .addAttribute(AttributeNameConstants.REL_ID, rId)
                    .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/ctrlProp"));//No I18N

            w.addAttribute("xmlns:mc", "http://schemas.openxmlformats.org/markup-compatibility/2006")//No I18N
                    .write(sb, "mc:AlternateContent")//No I18N
                        .addAttribute("Requires", "x14")//No I18N
                        .write(sb, "mc:Choice")//No I18N
                            .addAttribute("shapeId", drawControlShape.getDrawControl())//No I18N
                            .addAttribute("r:id", rId)//No I18N
                            .addAttribute("name", "Button "+(i+1))//No I18N
                            .write(sb, "control")//No I18N
                                .addAttribute("defaultSize", "0")//No I18N
                                .addAttribute("print", "0")//No I18N
                                .addAttribute("autoFill", "0")//No I18N
                                .addAttribute("autoPict", "0")//No I18N
                                .addAttribute("macro", "[0]!Button1_Click")//No I18N
                                .write(sb, "controlPr")//No I18N
                                    .addAttribute("moveWithCells","1")//No I18N
                                    .addAttribute("sizeWithCells","1")//No I18N
                                    .write(sb, "anchor")//No I18N
                                        .write(sb, "from")//No I18N
                                            .addValue("1").closeElementAndWrite(sb, "xdr:col")
                                            .addValue("698500").closeElementAndWrite(sb, "xdr:colOff")
                                            .addValue("5").closeElementAndWrite(sb, "xdr:row")
                                            .addValue("190500").closeElementAndWrite(sb, "xdr:rowOff")
                                        .closeElementAndWrite(sb, "from")//No I18N
                                        .write(sb, "to")//No I18N
                                            .addValue("3").closeElementAndWrite(sb, "xdr:col")
                                            .addValue("571500").closeElementAndWrite(sb, "xdr:colOff")
                                            .addValue("7").closeElementAndWrite(sb, "xdr:row")
                                            .addValue("165100").closeElementAndWrite(sb, "xdr:rowOff")
                                        .closeElementAndWrite(sb, "to")//No I18N
                                    .closeElementAndWrite(sb, "anchor")//No I18N
                                .closeElementAndWrite(sb, "controlPr")//No I18N
                            .closeElementAndWrite(sb, "control")//No I18N
                        .closeElementAndWrite(sb, "mc:Choice")//No I18N
                    .closeElementAndWrite(sb, "mc:AlternateContent");//No I18N
        }
        w.closeElementAndWrite(sb, "controls")//No I18N
                .closeElementAndWrite(sb, "mc:Choice")//No I18N
                .closeElementAndWrite(sb, "mc:AlternateContent");//No I18N
    }

    private void writeSlicerRid(StringBuilder stringBuilder) {

            String slicerXmlName = "slicer" + relationshipHelper.slicerXmlId + ".xml"; //No I18N
            String rId = "rId" + (sheetXmlRelsRelationshipElements.size() + 1);//No I18N
            sheetXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.TARGET, "../slicers/" + slicerXmlName)//No I18N
                    .addAttribute(AttributeNameConstants.REL_ID, rId)
                    .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.microsoft.com/office/2007/relationships/slicer"));//No I18N
            contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.PARTNAME, "/xl/slicers/" + slicerXmlName) //No I18N
                    .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.ms-excel.slicer+xml")); //No I18N

            XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
            xmlElementWriter.setElementName(ElementNameConstants.EXT)
                    .addAttribute(AttributeNameConstants.URI, "{A8765BA9-456A-4dab-B4F3-ACF838C121DE}") //No I18N
                    .addAttribute(AttributeNameConstants.XMLNS_X14, "http://schemas.microsoft.com/office/spreadsheetml/2009/9/main") //No I18N
                    .write(stringBuilder);
            xmlElementWriter.setElementName(ElementNameConstants.X14_SLICERLIST)
                    .write(stringBuilder);
            xmlElementWriter.setElementName(ElementNameConstants.X14_SLICER)
                    .addAttribute(AttributeNameConstants.R_ID, rId)
                    .closeElement()
                    .write(stringBuilder);
            xmlElementWriter.setElementName(ElementNameConstants.X14_SLICERLIST)
                    .closeElement()
                    .write(stringBuilder);
            xmlElementWriter.setElementName(ElementNameConstants.EXT)
                    .closeElement()
                    .write(stringBuilder);
    }

    private void writeTimelineRid(StringBuilder stringBuilder) {

        String timelineXmlName = "timeline" + relationshipHelper.timelineXmlId + ".xml"; //No I18N
        String rId = "rId" + (sheetXmlRelsRelationshipElements.size() + 1);//No I18N
        sheetXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                .addAttribute(AttributeNameConstants.TARGET, "../timelines/" + timelineXmlName)//No I18N
                .addAttribute(AttributeNameConstants.REL_ID, rId)
                .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.microsoft.com/office/2011/relationships/timeline"));//No I18N
        contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                .addAttribute(AttributeNameConstants.PARTNAME, "/xl/timelines/" + timelineXmlName) //No I18N
                .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.ms-excel.timeline+xml")); //No I18N

        XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter.setElementName(ElementNameConstants.EXT)
                .addAttribute(AttributeNameConstants.URI, "{7E03D99C-DC04-49d9-9315-930204A7B6E9}") //No I18N
                .addAttribute(AttributeNameConstants.XMLNS_X15, "http://schemas.microsoft.com/office/spreadsheetml/2010/11/main") //No I18N
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.X15_TIMELINE_REFS)
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.X15_TIMELINE_REF)
                .addAttribute(AttributeNameConstants.R_ID, rId)
                .closeElement()
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.X15_TIMELINE_REFS)
                .closeElement()
                .write(stringBuilder);
        xmlElementWriter.setElementName(ElementNameConstants.EXT)
                .closeElement()
                .write(stringBuilder);
    }

    private void writeTableParts(StringBuilder stringBuilder, Map<String, Table> tables) {
        if (tables != null && !tables.isEmpty()) {
            XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
            xmlElementWriter.addAttribute(AttributeNameConstants.COUNT, String.valueOf(tables.size()))
                    .write(stringBuilder, "tableParts");//No I18N
            for (int i = 0; i < tables.size(); i++) {
                String tableXmlName = "table"+(relationshipHelper.tableXmlId+i)+".xml";//No I18N
                String rId = "rId" + (sheetXmlRelsRelationshipElements.size() + 1);//No I18N
                contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                        .addAttribute(AttributeNameConstants.PARTNAME, "/xl/tables/" + tableXmlName)//No I18N
                        .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml"));//No I18N
                sheetXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                        .addAttribute(AttributeNameConstants.TARGET, "../tables/" + tableXmlName)//No I18N
                        .addAttribute(AttributeNameConstants.REL_ID, rId)
                        .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/table"));//No I18N
                xmlElementWriter
                        .addAttribute(AttributeNameConstants.R_ID, rId)
                        .closeElementAndWrite(stringBuilder, "tablePart");//No I18N
            }
            xmlElementWriter.closeElementAndWrite(stringBuilder, "tableParts");//No I18N
        }
    }

    private void writeSparkLineGroups(StringBuilder stringBuilder, List<SparklinesGroup> sparklinesGroupList, Workbook workbook) {
        ZSTheme theme = workbook.getTheme();
        final XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance();
        xmlElementWriter
                .addAttribute(AttributeNameConstants.URI, "{05C60535-1F16-4fd2-B633-F4F36F0B64E0}")//No I18N
                .addAttribute(AttributeNameConstants.XMLNS_X14, "http://schemas.microsoft.com/office/spreadsheetml/2009/9/main")//No I18N
                .write(stringBuilder, ElementNameConstants.EXT);

        xmlElementWriter.addAttribute(AttributeNameConstants.XMLNS_XM, "http://schemas.microsoft.com/office/excel/2006/main")//No I18N
                .write(stringBuilder, ElementNameConstants.X14_SPARKLINEGROUPS);
        for (SparklinesGroup sparklinesGroup : sparklinesGroupList) {
            final SparklinesGroup.SparklineProperties sparklineProperties = sparklinesGroup.getSparklineProperties();
            final SparklinesGroup.SparklineType sparklineType = sparklineProperties.getSparklineType();
            switch (sparklineType) {
                case COLUMN:
                    xmlElementWriter.addAttribute(AttributeNameConstants.TYPE, "column");//No I18N
                    break;
                case WINLOSS:
                    xmlElementWriter.addAttribute(AttributeNameConstants.TYPE, "stacked");//No I18N
                    break;
            }
            xmlElementWriter
                    .addAttribute(AttributeNameConstants.DISPLAY_EMPTY_CELL_AS, "gap")//No I18N
                    .write(stringBuilder, ElementNameConstants.X14_SPARKLINEGROUP);
            String rgb = sparklineProperties.getSparklineColor(theme);
            if(rgb != null) {
                xmlElementWriter
                        .addAttribute(AttributeNameConstants.RGB, "FF"+rgb.substring(1))//No I18N
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.X14_COLOR_SERIES);
            }
            rgb = sparklineProperties.getNegativePointColor(theme);
            if (rgb != null) {
                xmlElementWriter.addAttribute(AttributeNameConstants.RGB, "FF"+rgb.substring(1))//No I18N
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.X14_COLOR_NEGATIVE);
            }
//                    .addAttribute(AttributeNameConstants.RGB, sparklineProperties.getSparklineColor(theme))
//                    .closeElementAndWrite(stringBuilder, ElementNameConstants.X14_COLOR_AXIS)
            rgb = sparklineProperties.getMarkerColor(theme);
            if (rgb != null) {
                xmlElementWriter.addAttribute(AttributeNameConstants.RGB, "FF"+rgb.substring(1))//No I18N
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.X14_COLOR_MARKERS);
            }
            rgb = sparklineProperties.getFirstPointColor(theme);
            if (rgb != null) {
                xmlElementWriter.addAttribute(AttributeNameConstants.RGB, "FF"+rgb.substring(1))//No I18N
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.X14_COLOR_FIRST);
            }
            rgb = sparklineProperties.getLastPointColor(theme);
            if (rgb != null) {
                xmlElementWriter.addAttribute(AttributeNameConstants.RGB, "FF"+rgb.substring(1))//No I18N
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.X14_COLOR_LAST);
            }
            rgb = sparklineProperties.getHighPointColor(theme);
            if (rgb != null) {
                xmlElementWriter.addAttribute(AttributeNameConstants.RGB, "FF"+rgb.substring(1))//No I18N
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.X14_COLOR_HIGH);
            }
            rgb = sparklineProperties.getLowPointColor(theme);
            if (rgb != null) {
                xmlElementWriter.addAttribute(AttributeNameConstants.RGB, "FF"+rgb.substring(1))//No I18N
                        .closeElementAndWrite(stringBuilder, ElementNameConstants.X14_COLOR_LOW);
            }
            xmlElementWriter.write(stringBuilder, ElementNameConstants.X14_SPARKLINES);
            for (SparklinesGroup.Sparkline sparkline : sparklinesGroup.getSparklinesList()) {

                final Range sourceRange = sparkline.getSourceRange(workbook);
                final DataRange destinationRange = sparkline.getDestinationRange();
                if (sourceRange == null) {
                    switch (sparkline.getDestinationOrientation()) {
                        case SINGLE_CELL:
                        case VERTICAL:
                            for (int i = 0; i < destinationRange.getRowSize(); i++) {
                                xmlElementWriter.write(stringBuilder, ElementNameConstants.X14_SPARKLINE);
                                xmlElementWriter.addValue(Utility.toString(new DataRange(null, destinationRange.getStartRowIndex() + i, destinationRange.getStartColIndex(), destinationRange.getStartRowIndex() + i, destinationRange.getEndColIndex()), true, true))
                                        .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_SQREF);
                                xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.X14_SPARKLINE);
                            }
                            break;
                        case HORIZONTAL:
                            for (int i = 0; i < destinationRange.getColSize(); i++) {
                                xmlElementWriter.write(stringBuilder, ElementNameConstants.X14_SPARKLINE);
                                xmlElementWriter.addValue(Utility.toString(new DataRange(null, destinationRange.getStartRowIndex(), destinationRange.getStartColIndex() + i, destinationRange.getEndRowIndex(), destinationRange.getStartColIndex() + i), true, true))
                                        .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_SQREF);
                                xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.X14_SPARKLINE);
                            }
                            break;
                    }
                } else {
                    switch (sparkline.getSourceOrientation()) {
                        case HORIZONTAL:
                        case SINGLE_CELL:
                            for (int i = 0; i < sourceRange.getRowSize(); i++) {
                                xmlElementWriter.write(stringBuilder, ElementNameConstants.X14_SPARKLINE);
                                int rowIndex = sourceRange.getStartRowIndex() + i;
                                xmlElementWriter
                                        .addValue(Utility.toString(new DataRange(sourceRange.getSheet().getName(), rowIndex, sourceRange.getStartColIndex(), rowIndex, sourceRange.getEndColIndex()), true, true))
                                        .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_F);
                                switch (sparkline.getDestinationOrientation()) {
                                    case VERTICAL:
                                        xmlElementWriter.addValue(Utility.toString(new DataRange(null, destinationRange.getStartRowIndex() + i, destinationRange.getStartColIndex(), destinationRange.getStartRowIndex() + i, destinationRange.getEndColIndex()), true, true))
                                                .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_SQREF);
                                        break;
                                    case HORIZONTAL:
                                        xmlElementWriter.addValue(Utility.toString(new DataRange(null, destinationRange.getStartRowIndex(), destinationRange.getStartColIndex() + i, destinationRange.getEndRowIndex(), destinationRange.getStartColIndex() + i), true, true))
                                                .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_SQREF);
                                        break;
                                    case SINGLE_CELL:
                                        xmlElementWriter.addValue(Utility.toString(new DataRange(null, destinationRange.getStartRowIndex(), destinationRange.getStartColIndex(), destinationRange.getEndRowIndex(), destinationRange.getEndColIndex()), true, true))
                                                .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_SQREF);
                                }
                                xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.X14_SPARKLINE);
                            }
                            break;
                        case VERTICAL:
                            for (int i = 0; i < sourceRange.getColSize(); i++) {
                                xmlElementWriter.write(stringBuilder, ElementNameConstants.X14_SPARKLINE);
                                int colIndex = sourceRange.getStartColIndex() + i;
                                xmlElementWriter
                                        .addValue(Utility.toString(new DataRange(sourceRange.getSheet().getName(), sourceRange.getStartRowIndex(), colIndex, sourceRange.getEndRowIndex(), colIndex), true, true))
                                        .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_F);
                                switch (sparkline.getDestinationOrientation()) {
                                    case VERTICAL:
                                        xmlElementWriter.addValue(Utility.toString(new DataRange(null, destinationRange.getStartRowIndex() + i, destinationRange.getStartColIndex(), destinationRange.getStartRowIndex() + i, destinationRange.getEndColIndex()), true, true))
                                                .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_SQREF);
                                        break;
                                    case HORIZONTAL:
                                        xmlElementWriter.addValue(Utility.toString(new DataRange(null, destinationRange.getStartRowIndex(), destinationRange.getStartColIndex() + i, destinationRange.getEndRowIndex(), destinationRange.getStartColIndex() + i), true, true))
                                                .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_SQREF);
                                        break;
                                    case SINGLE_CELL:
                                        xmlElementWriter.addValue(Utility.toString(new DataRange(null, destinationRange.getStartRowIndex(), destinationRange.getStartColIndex(), destinationRange.getEndRowIndex(), destinationRange.getEndColIndex()), true, true))
                                                .closeElementAndWrite(stringBuilder, ElementNameConstants.XM_SQREF);
                                }
                                xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.X14_SPARKLINE);
                            }
                            break;
                    }
                }
            }
            xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.X14_SPARKLINES);
            xmlElementWriter
                    .closeElementAndWrite(stringBuilder, ElementNameConstants.X14_SPARKLINEGROUP);
        }
        xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.X14_SPARKLINEGROUPS);
        xmlElementWriter.closeElementAndWrite(stringBuilder, ElementNameConstants.EXT);
    }

    private void writeDrawingElement(StringBuilder stringBuilder, Sheet sheet, boolean hasChart, boolean hasSlicers, boolean hasTimelines) {
        final List<DrawingProperties> drawingPropertiesList = this.xlsxWriterImageContainer.getDrawingPropertiesOfSheet(sheet.getName());
        if (!drawingPropertiesList.isEmpty() || hasChart || hasSlicers || hasTimelines) {
            String drawingXmlName = "drawing" + (relationshipHelper.drawingXmlId) + ".xml";//No I18N
            String rId = "rId" + (sheetXmlRelsRelationshipElements.size() + 1);//No I18N
            contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.PARTNAME, "/xl/drawings/" + drawingXmlName)//No I18N
                    .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.drawing+xml"));//No I18N
            sheetXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.TARGET, "../drawings/" + drawingXmlName)//No I18N
                    .addAttribute(AttributeNameConstants.REL_ID, rId)
                    .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing"));//No I18N

            XmlElementWriter.newInstance(ElementNameConstants.DRAWING)
                    .addAttribute(AttributeNameConstants.R_ID, rId)
                    .closeElement()
                    .write(stringBuilder);
        }
    }

    private void writeSheetPr(Sheet sheet, StringBuilder stringBuilder) {
        if (sheet.getSheetStyle() != null) {
            final Object tabColorObj = sheet.getSheetStyle().getProperty(SheetStyle.Property.TABCOLOR);
            if(tabColorObj != null && tabColorObj instanceof ZSColor) {

                ZSColor color = (ZSColor) tabColorObj;
                final String hexColor = Utility.getHexColor(color, sheet.getWorkbook().getTheme());
                if (hexColor == null) {
                    return;
                }

                final XmlElementWriter sheetPr = XmlElementWriter.newInstance(ElementNameConstants.SHEETPR);
                sheetPr.write(stringBuilder);
                sheetPr.setElementName(ElementNameConstants.TABCOLOR);
                sheetPr.addAttribute(AttributeNameConstants.RGB, hexColor);
//                if (color instanceof ZSHexColor) {
//                } else {
//                    sheetPr.addAttribute(AttributeNameConstants.THEME, String.valueOf(ZSColorScheme.Colors.valueOf(color.getThemeColorToWrite()).ordinal()));
//                    if (color.getColorTintToWrite() != null) {
//                        sheetPr.addAttribute(AttributeNameConstants.TINT, color.getColorTintToWrite());
//                    }
//                }
                sheetPr.closeElement().write(stringBuilder);

                sheetPr.setElementName(ElementNameConstants.SHEETPR)
                        .closeElement()
                        .write(stringBuilder);
            }
        }
    }

    private void writelegacyDrawing(StringBuilder stringBuilder) {
        if (!getAnnotatedCells().isEmpty()) {
            String commentsXmlName = "comments" + (this.relationshipHelper.commentsXmlId) + ".xml";//No I18N
            sheetXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.TARGET, "../" + commentsXmlName)
                    .addAttribute(AttributeNameConstants.REL_ID, "rId" + (sheetXmlRelsRelationshipElements.size() + 1))//No I18N
                    .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments"));//No I18N
            contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.PARTNAME, "/xl/" + commentsXmlName)//No I18N
                    .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml"));//No I18N

            String vmlDrawingRid = "rId" + (sheetXmlRelsRelationshipElements.size() + 1);//No I18N
            String vmlDrawingXmlName = "vmlDrawing" + (this.relationshipHelper.commentsXmlId) + ".xml";//No I18N
            sheetXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.TARGET, "../drawings/" + vmlDrawingXmlName)//No I18N
                    .addAttribute(AttributeNameConstants.REL_ID, vmlDrawingRid)
                    .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing"));//No I18N
            contentTypesOverrideElements.add(XmlElementWriter.newInstance()
                    .addAttribute(AttributeNameConstants.PARTNAME, "/xl/drawings/" + vmlDrawingXmlName)//No I18N
                    .addAttribute(AttributeNameConstants.CONTENTTYPE, "application/vnd.openxmlformats-officedocument.vmlDrawing"));//No I18N

            XmlElementWriter.newInstance(ElementNameConstants.LEGACY_DRAWING)
                    .addAttribute(AttributeNameConstants.R_ID, vmlDrawingRid)
                    .closeElement()
                    .write(stringBuilder);
        }
    }


    private void write_row(StringBuilder stringBuilder, Row row, int rowIndex, List<DataRange> pivotTargetRanges, final PreprocessedRow preprocessedRow, final PreprocessedSheet preprocessedSheet, Map<Integer, Integer> rowOutlineLevels, List<Integer> collapsedIndices) throws Exception {

        Sheet sheet = row.getSheet();

        RowVisibility visibility = sheet.getRowVisibility(rowIndex);

        if(visibility == RowVisibility.FILTER || visibility == RowVisibility.COLLAPSE) {
            xmlElementWriter.addAttribute(AttributeNameConstants.HIDDEN, Utility.ONE_STRING);
        }

        xmlElementWriter.addAttribute(AttributeNameConstants.R, String.valueOf(rowIndex+1));

        RowStyle rowStyleReadOnly = row.getRowStyleReadOnly();
        if (rowStyleReadOnly != null
                && rowStyleReadOnly.getProperty(RowStyle.Property.USEOPTIMALROWHEIGHT) != null
                && "false".equals(rowStyleReadOnly.getProperty(RowStyle.Property.USEOPTIMALROWHEIGHT).toString())) {
            xmlElementWriter.addAttribute(AttributeNameConstants.CUSTOMHEIGHT, Utility.ONE_STRING);

            double rowHeight = getHeightInPt_ImportReverseCalculation(row.getRowHeight());
            xmlElementWriter.addAttribute(AttributeNameConstants.HT, String.valueOf(rowHeight));
        } else if(preprocessedRow.height != preprocessedSheet.height) {
            double rowHeight = getHeightInPt_ImportReverseCalculation(row.getRowHeight());
            xmlElementWriter.addAttribute(AttributeNameConstants.HT, String.valueOf(rowHeight));
        }

        Integer outlineLevel = rowOutlineLevels.get(rowIndex);
        if(outlineLevel != null)
        {
            xmlElementWriter.addAttribute(AttributeNameConstants.OUTLINELEVEL, String.valueOf(outlineLevel));
        }

        if(collapsedIndices.contains(rowIndex))
        {
            xmlElementWriter.addAttribute(AttributeNameConstants.COLLAPSED, Utility.ONE_STRING);
        }

        if(preprocessedRow.xf_index > 0) {
            xmlElementWriter.addAttribute(AttributeNameConstants.CUSTOMFORMAT, Utility.ONE_STRING);
            xmlElementWriter.addAttribute(AttributeNameConstants.S, String.valueOf(preprocessedRow.xf_index));
        }

        final int cellArraySize = row.getCells().size();
        xmlElementWriter.write(stringBuilder, ElementNameConstants.ROW);
        xLSX_CONVERSION_STATS.noOfRows++;

        boolean isNonEmptyRow = false;
        for(int i = 0; i < cellArraySize && i < com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFCOLS;) {
            final Cell cell = row.getCells().get(i);
            if (cell == null) {
                i+=1;
                continue;
            }
            final int colsRepeated = cell.getColsRepeated();
            final PreprocessedCell preprocessedCell = preprocessedSheet.preprocessedCells.get(row.getRowIndex()).get(i);

            boolean hasNonInheritedCellStyle = preprocessedCell.xf_index > 0;
            if (hasNonInheritedCellStyle) {
                if (preprocessedRow.xf_index > 0) {
                    hasNonInheritedCellStyle = (preprocessedCell.xf_index != preprocessedRow.xf_index);
                } else {
                    final PreprocessedCol preprocessedCol = preprocessedSheet.preprocessedCols.get(i);
                    if (preprocessedCol != null && preprocessedCol.xf_index > 0) {
                        hasNonInheritedCellStyle = (preprocessedCell.xf_index != preprocessedCol.xf_index);
                    }
                }
            }
            if(preprocessedCell.hasValue || hasNonInheritedCellStyle) {
                final int size = Math.min(i + colsRepeated, com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFCOLS);
                for(int colIndex = i; colIndex < size; colIndex++) {
                    boolean isInsidePivotTargetRange = false;
                    for (DataRange pivotTargetRange : pivotTargetRanges) {
                        final int startRowIndex = pivotTargetRange.getStartRowIndex();
                        final int endRowIndex = pivotTargetRange.getEndRowIndex();
                        if (rowIndex >= startRowIndex && rowIndex <= endRowIndex) {
                            final int startColIndex = pivotTargetRange.getStartColIndex();
                            final int endColIndex = pivotTargetRange.getEndColIndex();
                            if (colIndex >= startColIndex && colIndex <= endColIndex) {
                                isInsidePivotTargetRange = true;
                                break;
                            }
                        }
                    }
                    if (!isInsidePivotTargetRange) {
                        isNonEmptyRow = true;
                        write_cell(stringBuilder, cell, rowIndex, colIndex, preprocessedCell, preprocessedSheet.preprocessedCols.get(colIndex), preprocessedSheet);
                    }
                }
            }
            i+=colsRepeated;
        }
        if (isNonEmptyRow) {
            if(stringBuilder.length() > 40960) {
                this.outputStream.write(stringBuilder.toString().getBytes());
                stringBuilder.setLength(0);
            }
            xLSX_CONVERSION_STATS.noOfNonEmptyRows++;
        }
        xLSX_CONVERSION_STATS.testViolation();
        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.ROW));
    }

    public static boolean isEmpty(Cell cell) {
        return !(cell.isImage()
                || cell.isFormula()
                || cell.getValue() instanceof PicklistValue
                || cell.getValue().getValue() != null
                //|| cell.getStyleName() != null
//                || cell.getContentValidationName() != null
                || cell.getAnnotation() != null
                || cell.getDrawControlList() != null || cell.getRow().getSheet().getMergeCellDetails().get(cell) != null) ;
    }

    public static double getHeightInPt_ImportReverseCalculation(double height) {
        return (double) Math.round(height/(0.0138888889*100.0));
    }
    public static double getHeightInPt(double height){
//        double inches =EngineUtils1.convertPixelsToInchesDouble(String.valueOf(height), EngineConstants.ROWDPI);

        //inches = num * .0138888889;
        return (double) Math.round((height*72/96)*100)/100;
//        return height;
    }


    private void write_cell(StringBuilder stringBuilder, Cell cell, int rowIndex, int colIndex, PreprocessedCell preprocessedCell, PreprocessedCol preprocessedCol, PreprocessedSheet preprocessedSheet) throws Exception {
        this.xLSX_CONVERSION_STATS.noOfCells++;
        boolean isCellHasValueOrFormula = false;
        if (cell.getAnnotation() != null && cell.getAnnotation().getContent() != null) {
            this.annotatedCells.add(cell);
        }
        if (preprocessedCell.hasHyperLink) {
            this.cellsHavingLinks.add(cell);
        }
        Value value = cell.getValue();

        xmlElementWriter
                .addAttribute(AttributeNameConstants.R, CellUtil.getCellReference(colIndex, rowIndex));
        if (preprocessedCell.xf_index > 0) {
            xmlElementWriter
                    .addAttribute(AttributeNameConstants.S, String.valueOf(preprocessedCell.xf_index));
        } else if(preprocessedCell.isValidValue && preprocessedCol != null && preprocessedCol.xf_index > 0) {
            xmlElementWriter
                    .addAttribute(AttributeNameConstants.S, String.valueOf(preprocessedCol.xf_index));
        }

        if(cell.isFormula() && (cell.getExpression().getNode() instanceof ASTParseErrorNode)) {
            xmlElementWriter.addAttribute(AttributeNameConstants.T, "s");//No I18N
            xmlElementWriter.write(stringBuilder, ElementNameConstants.C);

            String formula = Utility.toString(cell.getExpression().getNode(), cell.getRow().getSheet().getWorkbook(), cell.getRowIndex(), cell.getColumnIndex(), false, cell);

            XlsxString rawValue = new XlsxString(new ZSString(formula, false));
            Integer index = sharedStrings.getOrDefault(rawValue, sharedStrings.size());
            sharedStrings.put(rawValue, index);

            stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.V));
            stringBuilder.append(index.toString());
            stringBuilder.append(Utility.getCloseTag(ElementNameConstants.V));

            stringBuilder.append(Utility.getCloseTag(ElementNameConstants.C));

            xLSX_CONVERSION_STATS.noOfValues++;
            xLSX_CONVERSION_STATS.noOfNonEmptyCells++;
            return;
        }

        String type = null;
        switch (value.getType()) {
            case STRING:
                type = "s";//No I18N
                break;
            case BOOLEAN:
                type = "b";//No I18N
                break;
        }
        if(type != null) {
            xmlElementWriter.addAttribute(AttributeNameConstants.T, type);
        }

        if(cell.isFormula()) {
            ExpressionImpl expression = (ExpressionImpl) cell.getExpression();
            if(Utility.FormulaMeta.has(preprocessedSheet.formulaMeta.get(expression.getNameToWrite_TEMP()), Utility.FormulaMeta.HAS_NON_EXPORTABLE_FORMULA)) {
                if ((value != null && value.getType() == Cell.Type.ERROR)) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.T, "str");//No I18N
                }
                xmlElementWriter.write(stringBuilder, ElementNameConstants.C);
            } else {
                XmlElementWriter formulaXMLW = XmlElementWriter.newInstance();
                boolean isWriteFormula = true;
                int arrayRowSpan = ((CellImpl) cell).getAutoArrayRowSpan();
                if (arrayRowSpan > 0 && ((CellImpl) cell).isSignedAutoArrayFormula()) {
                    formulaXMLW.addAttribute(AttributeNameConstants.T, "array");////No I18N
                    int arrayColSpan = ((CellImpl) cell).getAutoArrayColSpan();
                    String startCellReference = CellUtil.getCellReference(colIndex, rowIndex);
                    String endCellReference = CellUtil.getCellReference(colIndex + arrayColSpan - 1, rowIndex + arrayRowSpan - 1);
                    formulaXMLW.addAttribute(AttributeNameConstants.REF, startCellReference + ":" + endCellReference);

                    if (!((CellImpl) cell).isArrayCell()) {
                        xmlElementWriter.addAttribute(AttributeNameConstants.CM, "1");//No I18N
                        isDynamicArrayFormulaPresent = true;
                    }
                } else if (((CellImpl) cell).isAutoArrayError()) {
                    isDynamicArrayFormulaPresent = true;
                    formulaXMLW.addAttribute(AttributeNameConstants.T, "array");////No I18N
                    formulaXMLW.addAttribute(AttributeNameConstants.REF, cell.getCellRef());

                    xmlElementWriter
                            .addAttribute(AttributeNameConstants.CM, "1")
                            .addAttribute("vm", "1");//No I18N
                } else if(!cell.getExpression().hasStructuredReference(((CellImpl) cell).getSheet().getWorkbook())) {
                    PreprocessedSheet.XlsxSharedId xlsxSharedId = preprocessedSheet.getDataRangeOfExpression(cell);
                    boolean isSharedFormula = xlsxSharedId != null
                            && ((xlsxSharedId.start != xlsxSharedId.end) && xlsxSharedId.sharedId != -1)
                            && !Utility.FormulaMeta.has(preprocessedSheet.formulaMeta.get(expression.getNameToWrite_TEMP()), Utility.FormulaMeta.HAS_ABSOLUTE_SHEETNAME_REF);
                    if (isSharedFormula) {
                        boolean isTopCell = xlsxSharedId.start == cell.getRowIndex();
                        formulaXMLW.addAttribute(AttributeNameConstants.T, "shared");////No I18N
                        if (isTopCell) {
                            String startCellReference = CellUtil.getCellReference(cell.getColumnIndex(), xlsxSharedId.start);
                            String endCellReference = CellUtil.getCellReference(cell.getColumnIndex(), xlsxSharedId.end);
                            formulaXMLW.addAttribute(AttributeNameConstants.REF, startCellReference + ":" + endCellReference);
                        } else {
                            isWriteFormula = false;
                        }
                        formulaXMLW.addAttribute(AttributeNameConstants.SI, String.valueOf(xlsxSharedId.sharedId));
                    }
                }
                if(value != null && value.getType() == Cell.Type.ERROR) {
                    xmlElementWriter.addAttribute(AttributeNameConstants.T, "e");//No I18N
                }
                xmlElementWriter.write(stringBuilder, ElementNameConstants.C);
                if(isWriteFormula) {
                    String formula = Utility.toString(cell.getExpression().getNode(), cell.getRow().getSheet().getWorkbook(), cell.getRowIndex(), cell.getColumnIndex(), false, cell);
                    formulaXMLW.addValue(Utility.xmlEncode(formula));
                }
                formulaXMLW.closeElementAndWrite(stringBuilder, ElementNameConstants.F);

                xLSX_CONVERSION_STATS.noOfFormulas++;
                isCellHasValueOrFormula = true;
            }
        } else {
            if(value != null && value.getType() == Cell.Type.ERROR) {
                xmlElementWriter.addAttribute(AttributeNameConstants.T, "e");//No I18N
            }
            xmlElementWriter.write(stringBuilder, ElementNameConstants.C);
        }

        if(preprocessedCell.isValidValue) {
            Object valueObject = value.getValue();
            String alteredValue = null;
            switch (value.getType()) {
                case STRING:
                    XlsxString rawValue = new XlsxString((ZSString) value.getRawValue());
                    Integer index = sharedStrings.getOrDefault(rawValue, sharedStrings.size());
                    sharedStrings.put(rawValue, index);
                    alteredValue = index.toString();
                    break;
                case BOOLEAN:
                    alteredValue = String.valueOf( ((boolean)valueObject == true) ? 1 : 0);
                    break;
                case DATE:
                    if(valueObject instanceof ZSDate) {
                        alteredValue = String.valueOf(((ZSDate) valueObject).getNumberValue().longValue());
                    } else {
                        alteredValue = String.valueOf(FunctionUtil.objectToNumber(valueObject).longValue());
                    }
                    break;
                case DATETIME:
                case TIME:
                    if(valueObject instanceof ZSDate) {
                        alteredValue = String.valueOf(((ZSDate) valueObject).getNumberValue().doubleValue());
                    } else {
                        alteredValue = String.valueOf(FunctionUtil.objectToNumber(valueObject).doubleValue());
                    }
                    break;
                case ERROR:
                    Cell.Error errorValue = (Cell.Error) value.getRawValue();
                    switch (errorValue) {
                        case SPILL:
                            alteredValue = Cell.Error.VALUE.getErrorString();
                            break;
                        case NUM:
                        case REF:
                        case VALUE:
                        case DIV:
                        case NULL:
                            alteredValue = errorValue.getErrorString();
                            break;
                        case NAME:
                            alteredValue = "#NAME?";//No I18N
                            break;
                        case NA:
                            alteredValue = "#N/A";//No I18N
                            break;
                        case CIRCULARREF:
                        case TIMEOUT:
                        case FN_NOT_SUPPORTED:
                        case EVAL:
                        case NOT_LINKED:
                        case ACCESS_DENIED:
                        case UNKNOWN_ERROR:
                        case LOADING:
                            alteredValue = "#ERROR!";//No I18N
                            break;
                    }
                    break;
                default:
                    alteredValue = String.valueOf(valueObject);
            }

            if(alteredValue != null) {
                stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.V));
                stringBuilder.append(alteredValue);
                stringBuilder.append(Utility.getCloseTag(ElementNameConstants.V));
                xLSX_CONVERSION_STATS.noOfValues++;
                isCellHasValueOrFormula = true;
            }
        }

        stringBuilder.append(Utility.getCloseTag(ElementNameConstants.C));
        if (isCellHasValueOrFormula) {
            xLSX_CONVERSION_STATS.noOfNonEmptyCells++;
        }
        if (value instanceof ImageValue) {
            xlsxWriterImageContainer.addCellImage(cell);
        }
    }

    private void write_cols(StringBuilder stringBuilder, List<ColumnHeader> columnHeaders, PreprocessedSheet preprocessedSheet, Map<Integer, Integer> outlineLevels, List<Integer> collapsedIndices) {
        if(!columnHeaders.isEmpty()) {
            stringBuilder.append(Utility.getEmptyTag(ElementNameConstants.COLS));
            int colHeaderIndex = 0;
            while (colHeaderIndex < columnHeaders.size()) {
                final ColumnHeader columnHeader = columnHeaders.get(colHeaderIndex);
                final int colsRepeated = columnHeader.getColsRepeated();
                write_col(stringBuilder, columnHeader, preprocessedSheet.preprocessedCols.get(colHeaderIndex), preprocessedSheet, outlineLevels, collapsedIndices);
                colHeaderIndex+=colsRepeated;
            }
            stringBuilder.append(Utility.getCloseTag(ElementNameConstants.COLS));
        }
    }

    private void write_col(StringBuilder stringBuilder, ColumnHeader columnHeader, PreprocessedCol preprocessedCol, PreprocessedSheet preprocessedSheet, Map<Integer, Integer> outlineLevels, List<Integer> collapsedIndices) {
        if(columnHeader == null) {
            return;
        }
        List<String> keys = new ArrayList<>();
        List<String> values = new ArrayList<>();

        Column column = columnHeader.getColumn();
        int min = column.getColumnIndex() + 1;
        if(min > com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFCOLS) {
            return;
        }

        int colsRepeated = columnHeader.getColsRepeated();
        int max = min + colsRepeated - 1;
        max = Math.min(max, com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFCOLS);

        keys.add(AttributeNameConstants.MIN);
        values.add(String.valueOf(min));

        keys.add(AttributeNameConstants.MAX);
        values.add(String.valueOf(max));

        Sheet sheet = column.getSheet();
        ColumnVisibility visibility = sheet.getColumnVisibility(column.getColumnIndex());

        if(visibility == ColumnVisibility.COLLAPSE) {
            keys.add(AttributeNameConstants.HIDDEN);
            values.add("1");
        }

        if(preprocessedCol.xf_index > 0) {
            keys.add(AttributeNameConstants.STYLE);
            values.add(String.valueOf(preprocessedCol.xf_index));
        } else if (preprocessedSheet.xf_index > 0) {
            keys.add(AttributeNameConstants.STYLE);
            values.add(String.valueOf(preprocessedSheet.xf_index));
        }

        keys.add(AttributeNameConstants.WIDTH);
        values.add(String.valueOf(widthPixelToCharacters_reversOfImportCalculation(columnHeader.getColumnWidth())));

        Integer outlineLevel = outlineLevels.get(column.getColumnIndex());
        if(outlineLevel != null)
        {
            keys.add(AttributeNameConstants.OUTLINELEVEL);
            values.add(String.valueOf(outlineLevel));
        }

        if(collapsedIndices.contains(column.getColumnIndex()))
        {
            keys.add(AttributeNameConstants.COLLAPSED);
            values.add("1");
        }

        stringBuilder.append(Utility.getTag(ElementNameConstants.COL, keys.toArray(new String[]{}), values.toArray(new String[]{}), true));
    }

    private void write_mergeCells(Workbook workbook, StringBuilder stringBuilder, Collection<DataRange> mergeRanges) {
        if(!mergeRanges.isEmpty()) {
            List<DataRange> mergeRangesList = new ArrayList<>(mergeRanges);
            List<DataRange> nonOverlappingRanges = new ArrayList<>();
            for (int i = 0; i < mergeRangesList.size(); i++) {
                final DataRange range = mergeRangesList.get(i);
                boolean isOverLap = false;
                for (DataRange nonOverlappingRange : nonOverlappingRanges) {
                    if(!(range.getStartRowIndex() > nonOverlappingRange.getEndRowIndex() || range.getEndRowIndex() < nonOverlappingRange.getStartRowIndex()
                    || range.getStartColIndex() > nonOverlappingRange.getEndColIndex() || range.getEndColIndex() < nonOverlappingRange.getStartColIndex())) {
                        isOverLap = true;
                        LOGGER.log(Level.OFF, "not writing mergeCell {0}, since it is overlapping with {1}", new Object[]{range.getCompleteRangeStringForXML(workbook), nonOverlappingRange.getCompleteRangeStringForXML(workbook)});
                        break;
                    }
                }
                if (!isOverLap) {
                    nonOverlappingRanges.add(range);
                }
            }

            String[] keys = new String[]{AttributeNameConstants.COUNT};
            String[] values = new String[]{String.valueOf(nonOverlappingRanges.size())};
            stringBuilder.append(Utility.getTag(ElementNameConstants.MERGECELLS, keys, values, false));
                for(DataRange range: nonOverlappingRanges) {
                    keys = new String[]{AttributeNameConstants.REF};
                    values = new String[]{range.getRangeString()};
                    stringBuilder.append(Utility.getTag(ElementNameConstants.MERGECELL, keys, values, true));
                }
            stringBuilder.append(Utility.getCloseTag(ElementNameConstants.MERGECELLS));
        }
    }

    public static double widthPixelToCharacters_reversOfImportCalculation(int pixels) {
        double width = pixels/(1.006319444*6.00*0.0138888889*90.0);
        return ((int)(width*100))/100.0;
    }

    public static double widthPixelToCharacters(int pixels) {
        final double maximum_digit_width = 7.0;//using the Calibri font as an example, the maximum digit width of 11 point font size is 7 pixels (at 96 dpi).
        /*
        todo: get the maximum_digit_width for default font and font-size.
         */
        double number_of_characters = Math.round((pixels-5.0)/maximum_digit_width*100.0+0.5)/100.0;
        double width = Math.round((number_of_characters*maximum_digit_width****)/maximum_digit_width*256.0)/256.0;
        return ((int)(width*100))/100.0;
    }

    public List<Cell> getAnnotatedCells() {
        return this.annotatedCells;
    }

    private void writeHyperLinks(StringBuilder stringBuilder) throws Exception {
        if (!this.cellsHavingLinks.isEmpty()) {
            XmlElementWriter.newInstance(ElementNameConstants.HYPERLINKS)
                    .write(stringBuilder);
            for (Cell cellsHavingLink : this.cellsHavingLinks) {
                final RichStringProperties link = cellsHavingLink.getLinks().get(0);
                final String url = link.getUrl();
                final XmlElementWriter xmlElementWriter = XmlElementWriter.newInstance(ElementNameConstants.HYPERLINK);
                xmlElementWriter.addAttribute(AttributeNameConstants.REF, CellUtil.getCellReference(cellsHavingLink.getColumnIndex(), cellsHavingLink.getRowIndex()));
                if (url.startsWith("mailto:") || url.startsWith("https:") || url.startsWith("http:") || url.startsWith("tel:") || url.startsWith("file:///")) {
                    //todo tel: will probably throw error when open by Excel.
                    final String hyperlinkRId = "rId" + (this.sheetXmlRelsRelationshipElements.size() + 1);//No I18N
                    this.sheetXmlRelsRelationshipElements.add(XmlElementWriter.newInstance()
                            .addAttribute(AttributeNameConstants.REL_ID, hyperlinkRId)
                            .addAttribute(AttributeNameConstants.RELTYPE, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink")
                            .addAttribute(AttributeNameConstants.TARGET, Utility.xmlEncode(url))
                            .addAttribute(AttributeNameConstants.TARGETMODE, "External")
                    );
                    xmlElementWriter.addAttribute(AttributeNameConstants.R_ID, hyperlinkRId);
                } else if (url.startsWith("#")) {
                    boolean isNamedExpression = true;
                    final int sheetNameEndIndex = url.indexOf('.');
                    if (sheetNameEndIndex != -1) {
                        final String asnSheetName = url.substring(1, sheetNameEndIndex);
                        String reference = url.substring(sheetNameEndIndex + 1);

                        String sheetName = null;
                        for (Sheet sheet : cellsHavingLink.getRow().getSheet().getWorkbook().getSheetList()) {
                            if (sheet.getAssociatedName().equals(asnSheetName)) {
                                sheetName = sheet.getName();
                            }
                        }
                        if (sheetName != null) {
                            Pattern p = Pattern.compile("[^a-z0-9]", Pattern.CASE_INSENSITIVE);
                            final boolean sheetNameContainsSpecialCharacter = p.matcher(sheetName).find();
                            if (sheetNameContainsSpecialCharacter) {
                                sheetName = "'" + sheetName + "'";
                            }
                            xmlElementWriter.addAttribute(AttributeNameConstants.LOCATION, Utility.xmlEncode(sheetName + "!" + reference));
                            if ((sheetName + "." + reference).equals(link.getLabel())) {
                                xmlElementWriter.addAttribute(AttributeNameConstants.DISPLAY, Utility.xmlEncode(sheetName + "!" + reference));
                            } else {
                                xmlElementWriter.addAttribute(AttributeNameConstants.DISPLAY, Utility.xmlEncode(link.getLabel()));
                            }
                            isNamedExpression = false;
                        }
                    }
                    if (isNamedExpression) {
                        xmlElementWriter.addAttribute(AttributeNameConstants.LOCATION, Utility.xmlEncode(url.substring(1)));
                        if (link.getLabel() == null) {
                            xmlElementWriter.addAttribute(AttributeNameConstants.DISPLAY, Utility.xmlEncode(url.substring(1)));
                        } else {
                            xmlElementWriter.addAttribute(AttributeNameConstants.DISPLAY, Utility.xmlEncode(link.getLabel()));
                        }
                    }
                } else {
                    throw new Exception("xlsx-writer exception - unknown hyperlink : " + url);
                }
                xmlElementWriter
                        .closeElement()
                        .write(stringBuilder);
            }
            XmlElementWriter.newInstance(ElementNameConstants.HYPERLINKS)
                    .closeElement()
                    .write(stringBuilder);
        }
    }

    public boolean isDynamicArrayFormulaPresent() {
        return isDynamicArrayFormulaPresent;
    }
}
