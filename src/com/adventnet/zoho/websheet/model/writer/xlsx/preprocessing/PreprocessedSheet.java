/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx.preprocessing;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants;
import com.adventnet.zoho.websheet.model.style.datastyle.DefaultPatternUtil;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.writer.xlsx.SheetToXml;
import com.adventnet.zoho.websheet.model.writer.xlsx.StylesBuilder;

import java.util.*;
import java.util.function.Supplier;

public class PreprocessedSheet {
    private final StylesBuilder stylesBuilder;
    public int height;
    public int width;
    public int xf_index = -1;

    public Map<Integer, Map<Integer, PreprocessedCell>> preprocessedCells = new HashMap<>();
    public Map<Integer, PreprocessedRow> preprocessedRows = new HashMap<>();
    public Map<Integer, PreprocessedCol> preprocessedCols = new HashMap<>();
    private Map<String, Map<Integer, List<XlsxSharedId>>> expression_sharedId_map = new HashMap<>();
    public Map<String, EnumSet<com.adventnet.zoho.websheet.model.writer.xlsx.Utility.FormulaMeta>> formulaMeta = new HashMap<>();
    private int si_id_count = -1;
    private int expression_unique_id_count = -1;
    public List<ExpressionImpl> modifiedExpressions = new ArrayList<>();//to reset the temp name in the Expression.


    public PreprocessedSheet(StylesBuilder stylesBuilder) {
        this.stylesBuilder = stylesBuilder;
    }

    private void preprocess(Row row, PreprocessedRow preprocessedRow) {
        int colIndex = 0;
        boolean hasAnyNullCell = false;
        boolean atLeastOneCellhasValue = false;
        boolean atLeastOneCellHasStyle = false;

        Map<Integer, Integer> styleCounter = new HashMap<>();
        Map<Integer, PreprocessedCell> cellBuilderForRow = new HashMap<>();
        preprocessedCells.put(row.getRowIndex(), cellBuilderForRow);

        while (colIndex < row.getCells().size() && colIndex < Utility.MAXNUMOFCOLS) {
            final Cell cell = row.getCells().get(colIndex);
            if (cell == null) {
                colIndex+=1;
                hasAnyNullCell = true;
                continue;
            }

            final int colsRepeated = cell.getColsRepeated();
            PreprocessedCell preprocessedCell = new PreprocessedCell();
            preprocess(cell, preprocessedCell);
            cellBuilderForRow.put(colIndex, preprocessedCell);
            final PreprocessedCol preprocessedCol = this.preprocessedCols.get(colIndex);
            if (preprocessedCell.xf_index < 1) {
                if (preprocessedCol != null && preprocessedCol.xf_index > 0 && colsRepeated == preprocessedCol.colsRepeated) {
                    final Integer integer = styleCounter.get(preprocessedCol.xf_index);
                    if (integer == null) {
                        styleCounter.put(preprocessedCol.xf_index, colsRepeated);
                    } else {
                        styleCounter.put(preprocessedCol.xf_index, integer + colsRepeated);
                    }
                } else {
                    final Integer integer = styleCounter.get(preprocessedCell.xf_index);
                    if (integer == null) {
                        styleCounter.put(preprocessedCell.xf_index, colsRepeated);
                    } else {
                        styleCounter.put(preprocessedCell.xf_index, integer + colsRepeated);
                    }
                }
            } else {
                final Integer integer = styleCounter.get(preprocessedCell.xf_index);
                if (integer == null) {
                    styleCounter.put(preprocessedCell.xf_index, colsRepeated);
                } else {
                    styleCounter.put(preprocessedCell.xf_index, integer + colsRepeated);
                }
            }
            colIndex+=colsRepeated;
            if(preprocessedCell.hasValue) {
                atLeastOneCellhasValue = true;
            }
            if (preprocessedCell.xf_index > 0 && preprocessedCol.xf_index != preprocessedCell.xf_index) {
                atLeastOneCellHasStyle = true;
            }
        }

        if (row.getDefaultCellStyleName() == null) {
            if (!hasAnyNullCell && colIndex >= Utility.MAXNUMOFCOLS) {
                preprocessedRow.xf_index = getKeyWithMaxValue(styleCounter);
                preprocessedRow.hasManyCellStyles = styleCounter.size() > 1;
            }
        } else {
            CellStyle cellStyle = row.getSheet().getWorkbook().getCellStyle(row.getDefaultCellStyleName());
            preprocessedRow.xf_index = this.stylesBuilder.getXfIndex(cellStyle, 0, false, false);
        }
        preprocessedRow.hasValue = atLeastOneCellhasValue;
        preprocessedRow.hasCellStyle = atLeastOneCellHasStyle;
    }

    private void preprocess(Cell cell, PreprocessedCell preprocessedCell) {
        Value value = cell.getValue();
        Workbook workbook = cell.getRow().getSheet().getWorkbook();
        int numFmtId = 0;
        if (((CellImpl) cell).getPattern(1) == null) {
            switch (value.getType()) {
                case FLOAT:
                    numFmtId = 0;
                    break;
                case SCIENTIFIC:
                    numFmtId = 11;
                    break;
                case FRACTION:
                    numFmtId = 12;
                    break;
                case PERCENTAGE:
                    numFmtId = 10;
                    break;
                case CURRENCY:
                    ((CellImpl) cell).setPattern(DefaultPatternUtil.getDefaultCurrencyPattern(EngineConstants.DEFAULT_LOCALE, workbook.getFunctionLocale(), false, workbook.getSpreadsheetSettings()));
                    break;
                case DATE:
                    numFmtId = 14;
                    break;
                case TIME:
                    numFmtId = 21;
                    break;
                case DATETIME:
                    numFmtId = 22;
                    break;
            }
        }
        preprocessedCell.hasHyperLink = hasHyperLink(cell);
        preprocessedCell.isValidValue = value != null && value.getType() != Cell.Type.UNDEFINED && value.getValue() != null;
        boolean isMultiLine = !cell.isFormula() && preprocessedCell.isValidValue && value.getType() == Cell.Type.STRING && ((String) value.getValue()).contains("\n");//No I18N
        preprocessedCell.xf_index = this.stylesBuilder.getXfIndex(cell.getRow().getSheet().getWorkbook().getCellStyle(cell.getStyleName()), numFmtId, preprocessedCell.hasHyperLink, isMultiLine);
        preprocessedCell.hasValue = !SheetToXml.isEmpty(cell);
        if(cell.isFormula()) {
            updateDataRangeExpressionMap(cell);
        }
    }

    private static boolean hasHyperLink(Cell cell) {
        final List<RichStringProperties> links = cell.getLinks();
        if (links.size() != 1) {
            return false;
        }
        final RichStringProperties richStringProperties = links.get(0);
        if (richStringProperties == null) {
            return false;
        }
        final String url = richStringProperties.getUrl();
        if (url == null) {
            return false;
        }
        if (url.isEmpty()) {
            return false;
        }
        return  (url.startsWith("https:") || url.startsWith("http:") || url.startsWith("#") || url.startsWith("mailto:") || url.startsWith("tel:") || url.startsWith("file:///"));
    }

    public void preprocess(Sheet sheet) {
        preprocessCols(sheet);
        preprocessRows(sheet);
    }

    private void preprocessCols(Sheet sheet) {
        Map<Integer, Integer> widthCounter = new HashMap<>();
        int colHeaderIndex = 0;
        int size_colHeader = sheet.getColumnHeaders().size();
        while (colHeaderIndex < size_colHeader) {
            ColumnHeader columnHeader = sheet.getColumnHeaders().get(colHeaderIndex);
            if(columnHeader == null) {
                /*
                 * Should create the column header in workbook object  itself
                 * otherwise all the nulls at the end oh the columnHeader list
                 * will get removed resulting in wrong calculation of colNum value and
                 * hence in some data loss while saving.
                 *
                 * This handling is not needed for rows or cells
                 * as we don't consider their size for writing any other stuff like we depend on colNum while writing cells.
                 *
                 */
                columnHeader = new ColumnHeader(sheet.getColumn(colHeaderIndex));
                int repeated = 1;
                for(int j = colHeaderIndex+1; j < size_colHeader; j++)
                {
                    ColumnHeader tempColumnHeader = sheet.getColumnHeaderReadOnly(j);
                    if(tempColumnHeader == null)
                    {
                        repeated += 1;
                        //i += 1; not needed as we are jumping the noOfCols repeated below.
                    }
                    else
                    {
                        break;
                    }
                }
                columnHeader.setColsRepeated(repeated);
                /*
                 * Should create the column header in workbook object  itself
                 * otherwise all the nulls at the end oh the columnHeader list
                 * will get removed resulting in wrong calculation of colNum value and
                 * hence in some data loss while saving.
                 *
                 * This handling is not needed for rows or cells
                 * as we don't consider their size for writing any other stuff like we depend on colNum while writing cells.
                 *
                 */
                sheet.getColumnHeaders().set(colHeaderIndex, columnHeader);
            }

            final int colsRepeated = columnHeader.getColsRepeated();
            final PreprocessedCol preprocessedCol = new PreprocessedCol();

            CellStyle cellStyle = columnHeader.getCellStyleReadOnly();
            if(cellStyle != null && !"Default".equals(cellStyle.getStyleName())) {
                int xfIndex = this.stylesBuilder.getXfIndex(cellStyle, 0, false, false);
                if (xfIndex > 0) {
                    preprocessedCol.xf_index = xfIndex;
                }
            }
            preprocessedCol.width = columnHeader.getColumnWidth();

            this.preprocessedCols.put(colHeaderIndex, preprocessedCol);
            preprocessedCol.colsRepeated = colsRepeated;

            for (int i = colHeaderIndex + 1; i < colHeaderIndex + colsRepeated; i++) {
                PreprocessedCol clone = new PreprocessedCol();
                clone.xf_index = preprocessedCol.xf_index;
                clone.width = preprocessedCol.width;
                clone.colsRepeated = 0;
                this.preprocessedCols.put(i, preprocessedCol);
            }

            colHeaderIndex+=colsRepeated;

            final Integer integer = widthCounter.get(preprocessedCol.width);
            if (integer == null) {
                widthCounter.put(preprocessedCol.width, colsRepeated);
            } else {
                widthCounter.put(preprocessedCol.width, integer + colsRepeated);
            }
        }
        this.width = getKeyWithMaxValue(widthCounter);
    }

    private void preprocessRows(Sheet sheet) {
        int rowIndex = 0;
        Map<Integer, Integer> heightCounter = new HashMap<>();
        Map<Integer, Integer> xf_index_Counter = new HashMap<>();
        boolean styleAppliedToAllRows = true;
        while (rowIndex < sheet.getRows().size() && rowIndex < Utility.MAXNUMOFROWS) {
            final Row row = sheet.getRows().get(rowIndex);
            if (row == null) {
                styleAppliedToAllRows = false;
                rowIndex+=1;
                continue;
            }
            final int rowsRepeated = row.getRowsRepeated();
            PreprocessedRow preprocessedRow = new PreprocessedRow();
            this.preprocessedRows.put(rowIndex, preprocessedRow);
            preprocess(row, preprocessedRow);
            rowIndex+=rowsRepeated;
            preprocessedRow.height = row.getRowHeight();
            final Integer integer = heightCounter.get(preprocessedRow.height);
            if (integer == null) {
                heightCounter.put(preprocessedRow.height, rowsRepeated);
            } else {
                heightCounter.put(preprocessedRow.height, integer + rowsRepeated);
            }

            if (styleAppliedToAllRows) {
                if (preprocessedRow.xf_index < 1) {
                    styleAppliedToAllRows = false;
                } else {
                    final Integer integer1 = xf_index_Counter.get(preprocessedRow.xf_index);
                    if (integer1 == null) {
                        xf_index_Counter.put(preprocessedRow.xf_index, rowsRepeated);
                    } else {
                        xf_index_Counter.put(preprocessedRow.xf_index, integer1 + rowsRepeated);
                    }
                }
            }
        }
        this.height = getKeyWithMaxValue(heightCounter);
        if (styleAppliedToAllRows) {
            this.xf_index = getKeyWithMaxValue(xf_index_Counter);
        }

    }
    private static int getKeyWithMaxValue(Map<Integer, Integer> countMap) {
        int maxValue = -1;
        int defaultHeight = -1;
        for (Map.Entry<Integer, Integer> integerIntegerEntry : countMap.entrySet()) {
            if(integerIntegerEntry.getValue() > maxValue) {
                maxValue = integerIntegerEntry.getValue();
                defaultHeight = integerIntegerEntry.getKey();
            }
        }
        return defaultHeight;
    }

    public XlsxSharedId getDataRangeOfExpression(Cell cell) {
        String nameToWrite_temp = ((ExpressionImpl) cell.getExpression()).getNameToWrite_TEMP();
        if (nameToWrite_temp == null) {
            return null;
        }
        Map<Integer, List<XlsxSharedId>> expressionSharedRangeTemps = expression_sharedId_map.get(nameToWrite_temp);
        if(expressionSharedRangeTemps == null) {
            return null;
        }
        List<XlsxSharedId> sharedIdList = expressionSharedRangeTemps.get(cell.getColumnIndex());
        if(sharedIdList == null) {
            return null;
        }
        for (XlsxSharedId xlsxSharedId : sharedIdList) {
            if(cell.getRowIndex() < xlsxSharedId.start) {
                return null;
            }
            if(cell.getRowIndex() >= xlsxSharedId.start && cell.getRowIndex() <= xlsxSharedId.end) {
                if(xlsxSharedId.start == xlsxSharedId.end) {
                    return null;
                }
                return xlsxSharedId;
            }
        }
        return null;
    }

    private void updateDataRangeExpressionMap(Cell cell) {
        Supplier<XlsxSharedId> xlsxSharedIdSupplier = () -> {
            XlsxSharedId drExObj = new XlsxSharedId();
            drExObj.sharedId = ++si_id_count;
            drExObj.start = cell.getRowIndex();
            drExObj.end = cell.getRowIndex();
            return drExObj;
        };
        ExpressionImpl expression = (ExpressionImpl) cell.getExpression();
        String nameToWrite_temp = expression.getNameToWrite_TEMP();
        if(nameToWrite_temp == null) {
            nameToWrite_temp = String.valueOf(++expression_unique_id_count);
            expression.setNameToWrite_TEMP(nameToWrite_temp);
            modifiedExpressions.add(expression);

            EnumSet<com.adventnet.zoho.websheet.model.writer.xlsx.Utility.FormulaMeta> formulaMetas = EnumSet.noneOf(com.adventnet.zoho.websheet.model.writer.xlsx.Utility.FormulaMeta.class);
            com.adventnet.zoho.websheet.model.writer.xlsx.Utility.initFormulaMeta(cell.getExpression().getNode(), cell.getRow().getSheet().getWorkbook(), formulaMetas);
            formulaMeta.put(nameToWrite_temp, formulaMetas);
        }
        Map<Integer, List<XlsxSharedId>> integerListMap = expression_sharedId_map.get(nameToWrite_temp);
        if(integerListMap == null) {
            integerListMap = new HashMap<>();
            expression_sharedId_map.put(nameToWrite_temp, integerListMap);
            List<XlsxSharedId> list = new ArrayList<>();
            list.add(xlsxSharedIdSupplier.get());
            integerListMap.put(cell.getColumnIndex(), list);
        } else if(integerListMap.isEmpty()) {
            List<XlsxSharedId> list = new ArrayList<>();
            list.add(xlsxSharedIdSupplier.get());
            integerListMap.put(cell.getColumnIndex(), list);
        } else {
            List<XlsxSharedId> sharedIdList = integerListMap.get(cell.getColumnIndex());
            if(sharedIdList == null) {
                sharedIdList = new ArrayList<>();
                sharedIdList.add(xlsxSharedIdSupplier.get());
                integerListMap.put(cell.getColumnIndex(), sharedIdList);
            } else if(sharedIdList.isEmpty()) {
                sharedIdList.add(xlsxSharedIdSupplier.get());
            } else {
                XlsxSharedId xlsxSharedId = sharedIdList.get(sharedIdList.size() - 1);
                if (xlsxSharedId.end+1 == cell.getRowIndex()) {
                    xlsxSharedId.end = cell.getRowIndex();
                } else if(xlsxSharedId.start == xlsxSharedId.end){
                    sharedIdList.set(sharedIdList.size()-1, xlsxSharedIdSupplier.get());
                } else {
                    sharedIdList.add(xlsxSharedIdSupplier.get());
                }
            }
        }
    }

    public static class XlsxSharedId {
        public int start;
        public int end;
        public int sharedId;
    }
}
