/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.xlsxaparser_.AttributeNameConstants;

import java.io.IOException;
import java.util.List;
import java.util.zip.ZipOutputStream;

public class VmlDrawingToXml {

    public void write(ZipOutputStream zipOutputStream, List<Cell> annotatedCells, int i) throws IOException {
        StringBuilder stringBuilder = new StringBuilder(WorkbookToXML.XML_VERSION_ENCODING);
        stringBuilder.append("<xml xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:x=\"urn:schemas-microsoft-com:office:excel\" xmlns:w10=\"urn:schemas-microsoft-com:office:word\"><v:shapetype id=\"_x005F_x0000_t202\" coordsize=\"21600,21600\" o:spt=\"202\" path=\"m,l,21600l21600,21600l21600,xe\"><v:stroke joinstyle=\"miter\"/><v:path gradientshapeok=\"t\" o:connecttype=\"rect\"/></v:shapetype>");
        for (Cell annotatedCell : annotatedCells) {
            XmlElementWriter.newInstance("v:shape")//No I18N
                    .addAttribute(AttributeNameConstants.ID, "shape_"+(i++))//No I18N
                    .addAttribute("fillcolor", "#ffffc0")//No I18N
                    .addAttribute("stroked", "t")//No I18N
                    .addAttribute(AttributeNameConstants.TYPE, "_x005F_x0000_t202")//No I18N
                    .addAttribute("style", "position:absolute;margin-left:97pt;margin-top:0pt;width:82.15pt;height:17.3pt;visibility:hidden")//No I18N
                    .write(stringBuilder);
            stringBuilder.append("<v:shadow on=\"t\" obscured=\"t\" color=\"black\"/><w10:wrap type=\"none\"/><v:fill o:detectmouseclick=\"t\" type=\"solid\" color2=\"#00003f\"/><v:stroke color=\"#3465a4\" startarrow=\"block\" startarrowwidth=\"medium\" startarrowlength=\"medium\" joinstyle=\"round\" endcap=\"flat\"/><x:ClientData ObjectType=\"Note\"><x:MoveWithCells/><x:SizeWithCells/><x:Anchor>0, 0, 0, 0, 3, 0, 10, 0</x:Anchor><x:AutoFill>False</x:AutoFill>");

            stringBuilder
                    .append("<x:Row>")
                    .append(annotatedCell.getRowIndex())
                    .append("</x:Row>")
                    .append("<x:Column>")
                    .append(annotatedCell.getColumnIndex())
                    .append("</x:Column>");

            stringBuilder.append("</x:ClientData></v:shape>");
        }
        stringBuilder.append("</xml>");
        zipOutputStream.write(stringBuilder.toString().getBytes());
    }
}
