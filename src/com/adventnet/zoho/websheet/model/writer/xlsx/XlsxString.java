/* $Id$ */
package com.adventnet.zoho.websheet.model.writer.xlsx;

import com.adventnet.zoho.websheet.model.RichStringProperties;
import com.adventnet.zoho.websheet.model.ext.ZSString;

import java.util.Objects;

public class XlsxString {
    private final ZSString zsString;
    public XlsxString(ZSString zsString) {
        this.zsString = zsString;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        XlsxString that = (XlsxString) o;
        if (!zsString.getBaseStringValue().equals(that.zsString.getBaseStringValue())) {
            return false;
        }
        if (zsString.isApostrophePrefixed() !=  zsString.isApostrophePrefixed()) {
            return false;
        }
        if ((zsString.getProperties() == null) && that.zsString.getProperties() != null) {
            return false;
        }
        if ((zsString.getProperties() != null) && that.zsString.getProperties() == null) {
            return false;
        }
        if (zsString.getProperties().size() != that.zsString.getProperties().size()) {
            return false;
        }
        for (int i = 0; i < zsString.getProperties().size(); i++) {
            final RichStringProperties richStringProperties = zsString.getProperties().get(i);
            final RichStringProperties that_richStringProperties = that.zsString.getProperties().get(i);
            if (!equals(richStringProperties, that_richStringProperties)) {
                return false;
            }
        }
        return true;
    }
    public static boolean equals(RichStringProperties one, RichStringProperties that) {
        if (one == that) {return true;}
        return one.getStartIndex() == that.getStartIndex() && one.getEndIndex() == that.getEndIndex() && Objects.equals(one.getStyleName(), that.getStyleName()) && Objects.equals(one.getUrl(), that.getUrl()) && Objects.equals(one.getLabel(), that.getLabel());
    }

    @Override
    public int hashCode() {
        return Objects.hash(zsString);
    }

    public ZSString getZsString() {
        return zsString;
    }
}
