//$Id$
package com.adventnet.zoho.websheet.model.writer;

import com.adventnet.zoho.websheet.model.*;
import java.util.logging.Logger;
import java.util.zip.ZipOutputStream;

/**
 *
 * <AUTHOR>
 */
public class ODSWriteUtils {
    public static void writeODS(Workbook workbook, ImageBook imagebook, ZipOutputStream zos) {
        // TODO
        // implement this and delegate createTempODSFile() to this method
    }

    /*
    public static void writeSheetFragment(Sheet sheet, WorkbookToXML workbookToXML, DocumentInfo documentInfo) throws Exception {
        long time = System.currentTimeMillis();
        ZipOutputStream zos = null;
        Map writeInfo = null;
        FileName name = FileName.SHEETS;
        FileExtn extn = FileExtn.XML;
        String fileName = sheet.getAssociatedName();
        try {
            Long sheetId = getFragmentId(documentInfo, name, true, sheet.getAssociatedName());
            writeInfo = getWriteInfo(documentInfo, sheetId, name, FileExtn.ZIP, sheet.getAssociatedName());
            OutputStream os = (OutputStream) writeInfo.get("OS");
            zos = new ZipOutputStream(os);
            zos.putNextEntry(new ZipEntry(fileName + "." + extn.toString().toLowerCase()));
            workbookToXML.writeSheetXML(sheet, zos);
        } catch (DFSException dfse) {
            if (writeInfo != null) {
                writeInfo.put("fileWritten", false);
                DFSClient dfsClient = (DFSClient) writeInfo.get("DFSClient");
                dfsClient.abandonFileWrite((String) writeInfo.get("path"), (String) writeInfo.get("existingBlockId"));
                LOGGER.log(Level.WARNING, "DFSException in writing: " + fileName + " RESOURCE_ID: " + documentInfo.getResourceID(), dfse);
            }
            
            throw new Exception("Problem while saving document", dfse);
        } catch (Exception e) {
            if (writeInfo != null) {
                writeInfo.put("fileWritten", false);
            }
            LOGGER.log(Level.WARNING, "Error Occured while writing: " + fileName + " RESOURCE_ID: " + documentInfo.getResourceID(), e);
            throw new Exception("Problem while saving document");
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException ioe) {
                    DFSClient dfsClient = (DFSClient) writeInfo.get("DFSClient");
                    dfsClient.abandonFileWrite((String) writeInfo.get("path"), (String) writeInfo.get("existingBlockId"));
                    
                    LOGGER.log(Level.WARNING, "Error Occured while writing: IOException:: " + fileName + " RESOURCE_ID: " + documentInfo.getResourceID(), ioe);
                    throw new Exception("Problem while saving document");
                }

                //	zos.close();
                //	zos.close();
            }
            if (writeInfo != null) {
                ZSStore.finishWrite(writeInfo);
            }
        }
        LOGGER.log(Level.OFF, "[time-taken-for-fragment-file]:{0} :{1} ", new Object[]{name, (System.currentTimeMillis() - time)});
    }
    
    public static void writeFragment(Workbook workbook, WorkbookToXML workBookToXML, Fragment fragment, DocumentInfo documentInfo) throws Exception {
        long time = System.currentTimeMillis();
        ZipOutputStream zos = null;
        Map writeInfo = null;
        FileName name = getFragmentFileName(fragment);
        String fileName = name.toString().toLowerCase();
        try {
            Long sheetId = getFragmentId(documentInfo, name, true, null);
            writeInfo = getWriteInfo(documentInfo, sheetId, name, FileExtn.ZIP, null);
            OutputStream os = (OutputStream) writeInfo.get("OS");
            zos = new ZipOutputStream(os);

            switch (fragment) {
                case NAMEDRANGES:
                    writeNamedRangesFragmentFilesOntoZip(workBookToXML, zos);
                    break;
                case PIVOTS:
                    writePivotsFragmentFilesOntoZip(workBookToXML, zos);
                    break;
                case CONTENTVALIDATIONS:
                    writeContentValidationsFragmentFilesOntoZip(workBookToXML, zos);
                    break;
                case FILTERS:
                    writeFiltersFragmentFilesOntoZip(workBookToXML, zos);
                    break;
                case ASTYLES:
                    writeAStylesFragmentFilesOntoZip(workBookToXML, zos);
                    break;
                case FONTFACES:
                    writeFontFacesFragmentFilesOntoZip(workBookToXML, zos);
                    break;
                case STYLES:
                    writeStylesFragmentFilesOntoZip(workBookToXML, documentInfo, zos);
                    break;
                case MACROS:
                    writeMacrosFragmentFilesOntoZip(workbook, zos);
                    break;
                case SETTINGS:
                    writeSettingsFragmentFilesOntoZip(workbook.getWorkbookSettings(), zos);
                    break;
                default:
                    throw new IllegalArgumentException("Fragment: " + fragment); //No I18N
            }
        } catch (DFSException dfse) {
            if (writeInfo != null) {
                writeInfo.put("fileWritten", false);
                DFSClient dfsClient = (DFSClient) writeInfo.get("DFSClient");
                dfsClient.abandonFileWrite((String) writeInfo.get("path"), (String) writeInfo.get("existingBlockId"));
                LOGGER.log(Level.WARNING, "DFSException in writing: " + fileName + " RESOURCE_ID: " + documentInfo.getResourceID(), dfse);
            }
            
            throw new Exception("Problem while saving document", dfse);
        } catch (Exception e) {
            if (writeInfo != null) {
                writeInfo.put("fileWritten", false);
            }
            LOGGER.log(Level.WARNING, "Error Occured while writing: " + fileName + " RESOURCE_ID: " + documentInfo.getResourceID(), e);
            throw new Exception("Problem while saving document");
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException ioe) {
                    DFSClient dfsClient = (DFSClient) writeInfo.get("DFSClient");
                    dfsClient.abandonFileWrite((String) writeInfo.get("path"), (String) writeInfo.get("existingBlockId"));
                    
                    LOGGER.log(Level.WARNING, "Error Occured while writing: IOException:: " + fileName + " RESOURCE_ID: " + documentInfo.getResourceID(), ioe);
                    throw new Exception("Problem while saving document");
                }

                //	zos.close();
                //	zos.close();
            }
            if (writeInfo != null) {
                ZSStore.finishWrite(writeInfo);
            }
        }
        LOGGER.log(Level.OFF, "[time-taken-for-fragment-file]:{0} :{1} ", new Object[]{name, (System.currentTimeMillis() - time)});
    }

    private static FileName getFragmentFileName(Fragment fragment) {
        switch(fragment) {
            case NAMEDRANGES:
                return FileName.NAMEDRANGES;
            case PIVOTS:
                return FileName.PIVOTS;
            case CONTENTVALIDATIONS:
                return FileName.CONTENTVALIDATIONS;
            case FILTERS:
                return FileName.FILTERS;
            case ASTYLES:
                return FileName.ASTYLES;
            case FONTFACES:
                return FileName.FONTFACES;
            case STYLES:
                return FileName.STYLES;
            case MACROS:
                return FileName.MACROS;
            case SETTINGS:
                return FileName.SETTINGS;
        }
        throw new IllegalArgumentException("Fragment: " + fragment); //No I18N
    }

    // zos can be
    // an ODS ZipOutputStream (or)
    // a simple ZipOutputStream for the fragment (eg. styles.xml stored as a fragment [styles.zip [styles.xml]]
    public static void writeStylesFragmentFilesOntoZip(WorkbookToXML workBookToXML, DocumentInfo documentInfo, ZipOutputStream zos) throws Exception {
        String fileName = "styles.xml"; //No I18N
        zos.putNextEntry(new ZipEntry(fileName));
        // If we have styles.zip written already should use that instead
        // of the one in template folder.
        // Otherwise the conditional formats already written in
        // styles.zip will be lost.
        BufferedReader br = null;
        ZipInputStream zin = null;
        InputStream stream1 = null;
        try {
            try {
                long sheetId = getFragmentId(documentInfo, FileName.STYLES, true, null);
                boolean isFileExist = isFileExist(documentInfo, sheetId, FileName.STYLES, FileExtn.ZIP);
                if (isFileExist) {
                    stream1 = getReadStream(documentInfo, sheetId, FileName.STYLES, FileExtn.ZIP, null);
                    zin = new ZipInputStream(stream1);
                    zin.getNextEntry();
                    br = new BufferedReader(new InputStreamReader(zin));
                }
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Exception while getting styles.xml template", e);
                throw e;
            }
            workBookToXML.writeStylesXML(zos, br);
        } finally {
            if (zin != null) {
                try {
                    zin.close();
                } catch (Exception e) {
                    LOGGER.log(Level.SEVERE, "Exception while closing styles.xml template", e);
                }
            }
            if (stream1 != null) {
                try {
                    stream1.close();
                } catch (Exception e) {
                    LOGGER.log(Level.SEVERE, "Exception while closing styles.xml template", e);
                }
            }
            if (br != null) {
                try {
                    br.close();
                } catch (Exception e) {
                    LOGGER.log(Level.SEVERE, "Exception while closing styles.xml template", e);
                }
            }
        }
    }

    public static void writeNamedRangesFragmentFilesOntoZip(WorkbookToXML workBookToXML, ZipOutputStream zos) throws Exception {
        String fileName = "namedranges.xml"; //No I18N
        zos.putNextEntry(new ZipEntry(fileName));
        workBookToXML.writeNamedExpressionsXML(zos);
    }

    public static void writePivotsFragmentFilesOntoZip(WorkbookToXML workBookToXML, ZipOutputStream zos) throws Exception {
        String fileName = "pivots.xml"; //No I18N
        zos.putNextEntry(new ZipEntry(fileName));
        workBookToXML.writePivotTablesXML(zos);
    }

    public static void writeContentValidationsFragmentFilesOntoZip(WorkbookToXML workBookToXML, ZipOutputStream zos) throws Exception {
        String fileName = "contentvalidations.xml"; //No I18N
        zos.putNextEntry(new ZipEntry(fileName));
        workBookToXML.writeContentValidationsXml(zos);
    }

    public static void writeFiltersFragmentFilesOntoZip(WorkbookToXML workBookToXML, ZipOutputStream zos) throws Exception {
        String fileName = "filters.xml"; //No I18N
        zos.putNextEntry(new ZipEntry(fileName));
        workBookToXML.writeFilterRangesXML(zos);
    }

    public static void writeAStylesFragmentFilesOntoZip(WorkbookToXML workBookToXML, ZipOutputStream zos) throws Exception {
        String fileName = "astyles.xml"; //No I18N
        zos.putNextEntry(new ZipEntry(fileName));
        workBookToXML.writeAutomaticStyleXML(zos);
    }

    public static void writeFontFacesFragmentFilesOntoZip(WorkbookToXML workBookToXML, ZipOutputStream zos) throws Exception {
        String fileName = "fontfaces.xml"; //No I18N
        zos.putNextEntry(new ZipEntry(fileName));
        workBookToXML.writeFontFaceDeclsXML(zos);
    }

    public static void writeMacrosFragmentFilesOntoZip(Workbook workbook, ZipOutputStream zos) throws Exception {
        MacroWriter.writeMacros(zos, workbook);
    }

    public static void writeSettingsFragmentFilesOntoZip(WorkbookSettings workbookSettings, ZipOutputStream zos) throws Exception {
        String fileName = "settings.xml"; //No I18N
        zos.putNextEntry(new ZipEntry(fileName));
        SettingsWriter.writeSettings(zos, workbookSettings);
    }

    public static Long getFragmentId(DocumentInfo documentInfo, FileName name, boolean insertIfNotFound, String associatedName) throws Exception {
        return ZSStore.getFragmentId(documentInfo.getDocumentOwner(), documentInfo.getDocumentId(), documentInfo.getDocumentOwnerZUID(), name, insertIfNotFound, associatedName);
    }
    
    public static Map getWriteInfo(DocumentInfo documentInfo, Long resourceId, FileName name, ZSStore.FileExtn extn, String associatedName) throws Exception {
        return	ZSStore.getWriteInfo(documentInfo.getSpace(), documentInfo.getDocumentId(), resourceId, name, extn, associatedName);
    }

    public static InputStream getReadStream(DocumentInfo documentInfo, Long resourceId, FileName name, FileExtn extn, String associatedName) throws Exception {
        return ZSStore.getReadStream(documentInfo.getSpace(), documentInfo.getDocumentId(), resourceId, name, extn, associatedName);
    }

    public static boolean isFileExist(DocumentInfo documentInfo, Long resourceId, FileName name, FileExtn extn) throws Exception {
        // Do not pass the FileName.SHEETS to this method
        return ZSStore.isFileExist(documentInfo.getSpace(), documentInfo.getDocumentId(), resourceId, name, extn);
    }
    
    public static enum Fragment {
        FONTFACES,
        ASTYLES,
        STYLES,
        NAMEDRANGES,
        PIVOTS,
        CONTENTVALIDATIONS,
        FILTERS,
        SETTINGS,
        MACROS,
        IMAGES,
        SHEET
    }
    */
    
    private static final Logger LOGGER = Logger.getLogger(ODSWriteUtils.class.getName());
}
