//$Id$ 
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.action;

import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.action.VirtualTable.HeaderElement;
import com.adventnet.zoho.websheet.model.action.VirtualTable.Headers;
import com.adventnet.zoho.websheet.model.action.VirtualTable.TableElement;
import static com.adventnet.zoho.websheet.model.util.DocumentUtils.LOGGER;

import com.adventnet.zoho.websheet.model.util.Utility;
import com.google.common.collect.Table;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 *
 * <AUTHOR>
 */
public abstract class Cluster {//implements ClusterActions {

    protected final List<Range> sourceRanges;
    
    //RowLabel will maintain traversal order.
    protected Label rowLabel;
    
    //ColumnLabel will maintain traversal order.
    protected Label columnLabel;
    
    protected FunctionType functionType;
    
    //Grouping logic will get added.
    //Apart from grouping, formula details will always be there inspite of this boolean.
    protected boolean isCreateLinks;// = false; // By default
    
    protected boolean isIncludeHiddenCells;
    
    private VirtualTable virtualTable; //T (ClusterTable)
    
    private boolean isReEvaluate = false; 
    
    private boolean isReGenerateVirtualTable = true; 
    
    private boolean isContentChanged = false;
    
    private boolean isGetNonHeaderLabels = false;
    
    private final Map<String, String> depthOperations;
    
    protected Object[][] clusteredData;
    
    private final CombineRangeType combineRangeType;

    private final RangesStackingWay rangesStackingWay;
    
    private List<Integer> skips;

    public enum RangesStackingWay {
        HORIZONTAL,
        VERTICAL
    }
    
    public enum CombineRangeType {
        MERGERANGE,
        APPENDRANGE
    }
    
    public enum Label {
        NONE,
        TOPAUTO,
        LEFTAUTO,
        TOP,
        LEFT
    }
    
    public enum FunctionType {
        SUM,
        AVERAGE,
        COUNT,
        COUNTNUMBERS,
        MAX,
        MIN,
        MEDIAN,
        PRODUCT,
        STDDEV,
        STDDEVP,
        VAR,
        VARP,
        COUNT_DISTINCT
    }
    
    protected abstract void setHeaderAttributes(Object value, int objectRI, int objectCI);
    protected abstract int setDataAttributes(TableElement tableElement, int objectRI, int objectCI);
    
    public Cluster(List<Range> sourceRanges, FunctionType functionType, Label rowLabel, Label columnLabel, boolean isCreateLinks, boolean isIncludeHiddenCells, boolean isGetNonHeaderLabels, Map<String, String> depthOperations, String combinedRange, List<Integer> skips) {
        this.functionType = functionType;
        this.rowLabel = rowLabel;
        this.columnLabel = columnLabel;
        this.isCreateLinks = isCreateLinks;
        this.isIncludeHiddenCells = isIncludeHiddenCells;
        this.isGetNonHeaderLabels = isGetNonHeaderLabels;
        this.depthOperations = depthOperations;
        this.combineRangeType = getCombineRangeType(combinedRange);
        this.sourceRanges = isNeedRangeTrimming(combineRangeType) ? trimRangesToFirstRangeSize(sourceRanges, rowLabel, columnLabel) : sourceRanges;
        this.skips = skips;
        this.rangesStackingWay = getRangesStackingWay(combinedRange);
        createVirtualTable();
    }

    private RangesStackingWay getRangesStackingWay(String combinedRange) {
        if(combinedRange.equals("VERTICAL")) { return RangesStackingWay.VERTICAL; }
        else { return RangesStackingWay.HORIZONTAL; }
    }
    
    private CombineRangeType getCombineRangeType(String combinedRange)
    {
        if((combinedRange.equals("VERTICAL") && columnLabel == Label.LEFT) // No I18N
                || (combinedRange.equals("HORIZONTAL") && rowLabel == Label.TOP)) // No I18N
        {
            return CombineRangeType.APPENDRANGE;
        } 
        return  CombineRangeType.MERGERANGE;
    }
    
    private boolean isNeedRangeTrimming(CombineRangeType combineRangeType)
    {
        return (combineRangeType == CombineRangeType.MERGERANGE);
    }
    
    private List<Range> trimRangesToFirstRangeSize(List<Range> ranges, Label rowLabel, Label columnLabel)
    {
        List<Range> trimmedRanges = new ArrayList();
        for(Range range : ranges)
        {
            int endColIndex = range.getEndColIndex();
            if(rowLabel == Label.TOP && range.getColSize() > ranges.get(0).getColSize())
            {
//                endColIndex = range.getStartColIndex() + ranges.get(0).getColSize() - 1;
                endColIndex = Math.min(range.getStartColIndex() + ranges.get(0).getColSize(), Utility.MAXNUMOFCOLS) - 1;

            }
            int endRowIndex = range.getEndRowIndex();
            if(columnLabel == Label.LEFT && range.getRowSize() > ranges.get(0).getRowSize())
            {
//                endRowIndex = range.getStartRowIndex() + ranges.get(0).getRowSize() - 1;
                endRowIndex = Math.min(range.getStartRowIndex() + ranges.get(0).getRowSize(), Utility.MAXNUMOFROWS) - 1;

            }
            trimmedRanges.add(new Range(range.getSheet(), range.getStartRowIndex(), range.getStartColIndex(), endRowIndex, endColIndex));
        }
        
        return trimmedRanges;
    }

    public void setRowLabel(Label rowLabel) {
        this.isReGenerateVirtualTable = (this.rowLabel != rowLabel);
        this.rowLabel = rowLabel;
    }

    public void setColumnLabel(Label columnLabel) {
        this.isReGenerateVirtualTable = (this.columnLabel != columnLabel);
        this.columnLabel = columnLabel;
    }

    public void setFunctionType(FunctionType functionType) {
        this.isReEvaluate = (this.functionType != functionType);
        this.functionType = functionType;
    }
    
    public void setIsCreateLinks(boolean isCreateLinks) {
        this.isCreateLinks = isCreateLinks;
    }
    
    public void setIsIncludeHiddenCells(boolean isIncludeHiddenCells) {
        this.isReGenerateVirtualTable = (this.isIncludeHiddenCells != isIncludeHiddenCells);
        this.isIncludeHiddenCells = isIncludeHiddenCells;
    }
    
    public void setIsContentChanged(boolean isContentChanged) {
        this.isContentChanged = isContentChanged;
    }
    
    private boolean isClusterBooleansChanged() {
        return isReEvaluate || isReGenerateVirtualTable || isContentChanged;
    }
    
    public void resetClusterBooleans() {
        isReGenerateVirtualTable = false;
        isContentChanged = false;
        isReEvaluate = false;
    }
    
    private void createVirtualTable() {
        try {
            virtualTable = new VirtualTable();
            //Part-1
            //Traversal takes place.
            //Add all the labels row and col wise and keep the list of collected values(Can have more than one value in row and col level)
            List<Range> headerRanges = new ArrayList();
            Map<Range, List<Range>> sourceToHeader = ClusterUtils.generateHeadersRange(sourceRanges, rowLabel, columnLabel, depthOperations, virtualTable, combineRangeType, true, functionType, headerRanges, skips, rangesStackingWay);
            //Part-1 end
            //Part-2
            List<String> depthList = new ArrayList();
            if(depthOperations != null)
            {
                depthList.addAll(depthOperations.keySet());
            }
            boolean isSkipHeaders = false;
            Headers headers = columnLabel == Label.LEFT ? virtualTable.getColumnHeaders() : virtualTable.getRowHeaders();
            if(combineRangeType == CombineRangeType.APPENDRANGE)
            {
                int labStartIndex = (columnLabel == Label.LEFT) ? headerRanges.get(0).getStartRowIndex() : headerRanges.get(0).getStartColIndex();
                List<Integer> depthIndexOrder = new ArrayList();
                Boolean isFirstValue = null;
                for(List<Range> ranges : sourceToHeader.values())
                {
                    isFirstValue = isFirstValue == null ? true : false;
                    if(ranges != null && !ranges.isEmpty())
                    {
                        int size = columnLabel == Label.LEFT ? ranges.get(0).getRowSize() : ranges.get(0).getColSize();
                        for(int index = 0; index < size; index++)
                        {
                            List<String> newHeadings = new ArrayList();
                            for(Range hRange : ranges)
                            {
                                isSkipHeaders = ClusterUtils.iterateHeaders(virtualTable, rowLabel, columnLabel, hRange, index, isIncludeHiddenCells, depthOperations, combineRangeType, labStartIndex, depthList, newHeadings, depthIndexOrder, isFirstValue && index == 0);
                            }
                            if(!isSkipHeaders && (index != 0 || !isFirstValue))
                            {
                                headers.addHeadings(newHeadings, depthIndexOrder);
                            }
                        }
                    }
                }
            }
            else
            {
                int labStartIndex = (columnLabel == Label.LEFT) ? headerRanges.get(0).getStartRowIndex() : headerRanges.get(0).getStartColIndex();
                List<Integer> depthIndexOrder = new ArrayList();
                int size = columnLabel == Label.LEFT ? headerRanges.get(0).getRowSize() : headerRanges.get(0).getColSize();

                for(int index = 0; index < size; index++)
                {
                    List<String> newHeadings = new ArrayList();
                    for(Range hRange : headerRanges)
                    {
                        isSkipHeaders = ClusterUtils.iterateHeaders(virtualTable, rowLabel, columnLabel, hRange, index, isIncludeHiddenCells, depthOperations, combineRangeType, labStartIndex, depthList, newHeadings, depthIndexOrder, index==0);
                    }
                    if(!isSkipHeaders && index != 0)
                    {
                        headers.addHeadings(newHeadings, depthIndexOrder);
                    }
                }
            }
            //Part-2 end
            //Part-3
            List<Integer> depthIndices = new ArrayList();
            List<Integer> availableIndices = new ArrayList();
            int rowLimitSize = 0, colLimitSize = 0;
            for(int i = 0; i < sourceRanges.size(); i++)
            {
                Range currentRange = sourceRanges.get(i);
                List<Range> ranges = sourceToHeader.get(currentRange);
                if(ranges != null && !ranges.isEmpty())
                {
                    if(rowLabel != Label.NONE)
                    {
                        rowLimitSize += currentRange.getRowSize();
                        if(i != 0 && virtualTable.getRowHeaders().getHeadersCount() == rowLimitSize)
                        {
                            continue;
                        }
                    }
                    if(columnLabel != Label.NONE)
                    {
                        colLimitSize += currentRange.getColSize();
                        if(i != 0 && virtualTable.getColumnHeaders().getHeadersCount() == colLimitSize)
                        { 
                            continue;
                        }
                    }
                }
                int startIndex = sourceRanges.get(0).getStartRowIndex();
                int startCI = sourceRanges.get(0).getStartColIndex();
                headerRanges = combineRangeType == CombineRangeType.APPENDRANGE ? sourceToHeader.get(currentRange) : headerRanges;
                String sourceSheetName = null;
                depthIndices = new ArrayList();
                availableIndices = new ArrayList();
                if(combineRangeType == CombineRangeType.APPENDRANGE)
                {
                    if(!currentRange.getSheet().getName().equals(sourceRanges.get(0).getSheet().getName()))
                    {
                        sourceSheetName = sourceRanges.get(0).getSheet().getName();
                    }
                }
                ClusterUtils.createDataTable(currentRange, virtualTable, functionType, rowLabel, columnLabel, depthOperations, isIncludeHiddenCells, startIndex, startCI, depthIndices, sourceRanges, availableIndices, headerRanges, combineRangeType, sourceSheetName, skips);
            }
            //Part-3 end
            this.isReEvaluate = true;
            this.isReGenerateVirtualTable = false;
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, " Problem while creating createVirtualTable.", e);
        }
    }
    
    public VirtualTable getVirtualTable() {
        if(isReGenerateVirtualTable)
        {
            createVirtualTable();
        }
//        else{
//            if(isContentChanged)
//            {
//                VirtualTable newVirtualTable = new VirtualTable();
//                List<Cell> headerCells = new ArrayList();
//                int rowSize = 0, colSize = 0;
//                for(int i = 0; i < sourceRanges.size(); i++)
//                {
//                    Range currentRange = sourceRanges.get(i);
//                    if(!(rowLabel != Label.NONE && columnLabel != Label.NONE))
//                    {
//                        rowSize += currentRange.getRowSize();
//                        colSize += currentRange.getColSize();
//                        Sheet currentSheet = currentRange.getSheet();
//                        int startRowIndex = currentRange.getStartRowIndex();
//                        int startColIndex = currentRange.getStartColIndex();
//                        int endRowIndex = currentRange.getEndRowIndex();
//                        int endColIndex = currentRange.getEndColIndex();
//                        if(i == 0 && depthOperations != null)
//                        {
//                            virtualTable.getRowLabels().addAll(depthOperations.keySet());
//                            virtualTable.getColumnLabels().addAll(depthOperations.keySet());
//                        }
//                        if(rowLabel != Label.NONE)
//                        {
//                            ClusterUtils.createLabels(currentSheet, startRowIndex, startColIndex, endRowIndex, startColIndex, virtualTable.getRowLabels(), isIncludeHiddenCells, functionType, depthOperations, rowLabel);
//                        }
//                        if(columnLabel != Label.NONE)
//                        {
//                            ClusterUtils.createLabels(currentSheet, startRowIndex, startColIndex, startRowIndex, endColIndex, virtualTable.getColumnLabels(), isIncludeHiddenCells, functionType, depthOperations, columnLabel);
//                        }
//                    }
//
////                    int startIndex;
////                    if(sourceRanges.size() > 1)
////                    {
////                        startIndex = (rowLabel == Label.TOP) ? sourceRanges.get(0).getStartColIndex() : sourceRanges.get(0).getStartRowIndex();
////                    }
////                    else {
////                        startIndex = (rowLabel == Label.TOP) ? currentRange.getStartColIndex() : currentRange.getStartRowIndex();
////                    }
////                    if((isCombineRanges && columnLabel == Label.LEFT ? i == 0 : true) || depthOperations != null)
////                    {
////                        ClusterUtils.createRowColumnHeaders(currentRange, virtualTable, rowLabel, columnLabel, isIncludeHiddenCells, depthOperations, functionType, startIndex, headerCells, combineRangeType);
////                    }
//                }
//                
//                
//                boolean isHeadersChanged = !virtualTable.getRowHeaders().equals(newVirtualTable.getRowHeaders()) 
//                                    || !virtualTable.getColumnHeaders().equals(newVirtualTable.getColumnHeaders());
//                
//                if(isHeadersChanged) //need to create the datatable also.
//                {
//                    List<Integer> depthIndices = new ArrayList();
//                    List<Integer> availableIndices = new ArrayList();
//                    List<Range> headerRanges = new RangeUtil.MergeCells(headerCells).toRanges();
//                    for(Range currentRange : sourceRanges)
//                    {
//                        int startIndex;
//                        int startCI;
////                        if(isCombineRanges)
//                        {
//                            startIndex = sourceRanges.get(0).getStartRowIndex();
//                            startCI = sourceRanges.get(0).getStartColIndex();
//                        }
////                        else 
//                        {
//                            startIndex = currentRange.getStartRowIndex();
//                            startCI = currentRange.getStartColIndex();
//                        }
//                        depthIndices = ClusterUtils.createDataTable(currentRange, newVirtualTable, functionType, rowLabel, columnLabel, depthOperations, isIncludeHiddenCells, startIndex, startCI, depthIndices, sourceRanges, availableIndices, headerRanges, combineRangeType);
//                    }
//                    virtualTable = newVirtualTable;
//                }
//            }
//        }
//        resetClusterBooleans();
        return virtualTable;
    }
    
    protected boolean generateClusteredData() {
        
        if(isClusterBooleansChanged())
        {
            virtualTable = getVirtualTable();
            if(virtualTable.isDataEmpty())
            {
                return true;
            }
            Headers rowHeaders = virtualTable.getRowHeaders();
            Headers columnHeaders = virtualTable.getColumnHeaders();
            Table<Integer, Integer, TableElement> dataTable = virtualTable.getDataTable();
            
            boolean isRowLabelNone = (rowLabel == Label.NONE);
            boolean isColumnLabelNone = (columnLabel == Label.NONE);
            
            int rowSize = dataTable.rowKeySet().size() + (isRowLabelNone ? 1 : rowHeaders.getHeadersCount());
            int columnSize = dataTable.columnKeySet().size() + (isColumnLabelNone ? 1 : columnHeaders.getHeadersCount());
            clusteredData = new Object[rowSize][columnSize];
            int objectRI = (rowHeaders.getHeadersCount() > 0 ? rowHeaders.getHeadersCount() : 1);
            int objectCI = (columnHeaders.getHeadersCount() > 0 ? columnHeaders.getHeadersCount() : 1);
            //row header part
            if(!isRowLabelNone && rowHeaders.getSize() > 0)
            {
                int j = 0;
                for(HeaderElement headerElement : rowHeaders.getHeadings())
                {
                    int i = 0;
                    for(Integer index : headerElement.getDepthOrder())
                    {
                        String heading = headerElement.getHeadings().get(i++);
                        setHeaderAttributes(heading, index, j+objectCI);
                    }
                    j++;
                }
            }
            
            //column header part
            if(!isColumnLabelNone && columnHeaders.getSize() > 0)
            {
                int i = 0;
                for(HeaderElement headerElement : columnHeaders.getHeadings())
                {
                    int j = 0;
                    for(Integer index : headerElement.getDepthOrder())
                    {
                        String heading = headerElement.getHeadings().get(j++);
                        setHeaderAttributes(heading, i+objectRI, index);
                    }
                    i++;
                }
            }
            
            //data part
            int rowCounter = 0;
            for(int i = 0; i < dataTable.rowKeySet().size(); i++)
            {
            	int  colCounter = 0;
                Map<Integer, TableElement> currentColumnMap = dataTable.row(i);
                for(int j = 0; j < currentColumnMap.size(); j++)
                {
                    TableElement tableElement = currentColumnMap.get(j);
                    if(tableElement != null)
                    {
                        setDataAttributes(tableElement, i+objectRI, j+objectCI);
                    }
                }
            }
            
            if(isGetNonHeaderLabels)
            {
                if(isColumnLabelNone)
                {
                    int i = 0;
                    for(Object label : virtualTable.getRowLabels())
                    {
                        if(i >= rowSize)
                        {
                            break;
                        }
                        int tempI = i;
                        while(skips.contains(tempI)) {
                        	tempI++;
                        	continue;
                        }
                        int j = 0;
                        setHeaderAttributes(label, i, j);
                        i++;
                    }
                }
                
                if(isRowLabelNone)
                {
                    int j = 0;
                    for(Object label : virtualTable.getColumnLabels())
                    {
                        if(j >= columnSize)
                        {
                            break;
                        }
                        int tempJ = j;
                        while(skips.contains(tempJ)) {
                        	tempJ++;
                        	continue;
                        }
                        int i = 0;
                        setHeaderAttributes(label, i, j);
                        j++;
                    }
                }
            }
            return true;
        }
        return false;
    }
}
