//$Id$ 
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.action;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.action.VirtualTable.TableElement;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import java.util.List;
import java.util.Locale;

/**
 *
 * <AUTHOR>
 */
public class Consolidate extends Cluster {
    Cell srcCell;
    
    private Consolidate(List<Range> sourceRanges, FunctionType functionType, Label rowLabel, Label columnLabel, boolean isCreateLinks, boolean isIncludeHiddenCells, String combinedRange, List<Integer> skips)
    {
        super(sourceRanges, functionType, rowLabel, columnLabel, isCreateLinks, isIncludeHiddenCells, false, null, combinedRange, skips);
    }
    
    public void getConsolidatedRange(Cell destCell)
    {
        this.srcCell = destCell;
        boolean isGenerated = generateClusteredData();
        if(!isGenerated)
        {
            Sheet destSheet = destCell.getRow().getSheet();
            SpreadsheetSettings spreadsheetSettings = destSheet.getWorkbook().getSpreadsheetSettings();
            for(int i = 0; i < clusteredData.length; i++)
            {
                for(int j = 0; j < clusteredData[i].length; j++)
                {
                    Cell cell = new CellImpl(new Row(destSheet, i+destCell.getRowIndex()), new Column(destSheet, j+destCell.getColumnIndex()));
                    cell.setValue(Value.getInstance(String.valueOf(clusteredData[i][j]), spreadsheetSettings));
                }
            }
        }
    }

    @Override
    protected void setHeaderAttributes(Object value, int objectRI, int objectCI) {
        if(value != null)
        {
            int cellRowIndex = objectRI + (srcCell == null ? 0 : srcCell.getRowIndex());
            int cellColIndex = objectCI + (srcCell == null ? 0 : srcCell.getColumnIndex());
            Sheet sheet = srcCell == null ? null : srcCell.getRow().getSheet();

            Cell cell = new CellImpl(new Row(sheet, cellRowIndex), new Column(sheet, cellColIndex));
            cell.setValue(Value.getInstance(Cell.Type.STRING, value));
        }
        clusteredData[objectRI][objectCI] = value;
    }

    @Override
    protected int setDataAttributes(TableElement tableElement, int objectRI, int objectCI)
    {
        int cellRowIndex = (srcCell == null ? 0 : srcCell.getRowIndex());
        int cellColIndex = (srcCell == null ? 0 : srcCell.getColumnIndex());
        Sheet sheet = (srcCell == null ? null : srcCell.getRow().getSheet());

        if(isCreateLinks)
        {
            List<Integer> rowIndices = tableElement.getRowIndices();
            List<Integer> colIndices = tableElement.getColIndices();
            List<Sheet> sheets = tableElement.getSheets();
            int size = sheets.size();

            for(int k = 0; k < size; k++)
            {
                String sheetName = sheets.get(k).getName();
                int rowIndex = rowIndices.get(k);
                int colIndex = colIndices.get(k);
                String cellRef = "="+CellUtil.getCellReference(sheetName, rowIndex, colIndex, true, true, true);

                int tempCellRowIndex = cellRowIndex + k;
                int tempCellColIndex = cellColIndex;
                Cell cell = new CellImpl(new Row(sheet, tempCellRowIndex), new Column(sheet, tempCellColIndex));
                cell.setFormula(cellRef, true);

                clusteredData[objectRI++][objectCI] = cellRef;
            }

            int tempEndRowIndex = cellRowIndex + size;
            int tempEndColIndex = cellColIndex;
            String rangeRef = CellUtil.getRangeReferenceString(sheet.getName(), cellRowIndex, cellColIndex, tempEndRowIndex-1, tempEndColIndex);
            String formula = ClusterUtils.getformula(functionType, rangeRef);
            Cell cell = new CellImpl(new Row(sheet, tempEndRowIndex), new Column(sheet, tempEndColIndex));
            cell.setFormula(formula, true);
            cellRowIndex = tempEndRowIndex;
            cellColIndex = tempEndColIndex;

            clusteredData[objectRI++][objectCI] = formula;
        }
        else{
            Object result = null;
            try{
                result = ClusterUtils.calculate(functionType, tableElement.getRowIndices(), tableElement.getColIndices(), tableElement.getSheets());
            }catch(EvaluationException e){}

            if(result != null)
            {
                Cell cell = new CellImpl(new Row(sheet, cellRowIndex), new Column(sheet, cellColIndex));
                Value value = Value.getInstance(String.valueOf(result), sheet.getWorkbook().getSpreadsheetSettings());
                cell.setValue(value); 
            }

            clusteredData[objectRI][objectCI] = result;
        }
        return objectRI;
    }
    
    public static Consolidate doConsolidate(List<Range> sourceRanges, boolean isRowLabel, boolean isColLabel, String functionName, boolean isCreateLinks, boolean isIncludeHiddenCells, List<Integer> skips)
    {
        Label rowLabel = isRowLabel ? Label.TOP : Label.NONE;
        Label columnLabel = isColLabel ? Label.LEFT : Label.NONE;
        FunctionType functionType = ClusterUtils.getFunctionType(functionName);
        
        Consolidate consolidate = new Consolidate(sourceRanges, functionType, rowLabel, columnLabel, isCreateLinks, isIncludeHiddenCells, "HORIZONTAL", skips); //No I18N
        return consolidate;
    }
}
