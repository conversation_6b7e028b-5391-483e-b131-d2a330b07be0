//// $Id$
package com.adventnet.zoho.websheet.model.action;

import java.util.Collection;
import java.util.LinkedList;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.RangeIterator;
import com.adventnet.zoho.websheet.model.ReadOnlyCell;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.EmptyCellPredicates;
import com.adventnet.zoho.websheet.model.util.RangeUtil.SheetRange;

public class EmptyCellsInRange {
    
    private final DataRange dataRange;
    
    private Collection<SheetRange> result;
    
    public EmptyCellsInRange(DataRange dataRange) {
        this.dataRange = dataRange;
    }
    
    public void execute(Workbook workbook) {
        this.result = new LinkedList();
        RangeIterator rangeIterator = new RangeIterator(dataRange.toRange(workbook), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, false, true);
        while(rangeIterator.hasNext()) {
            ReadOnlyCell roCell = rangeIterator.next();
   
            if(isEmpty(roCell.getCell())) {
                int startRow = roCell.getRowIndex();
                int startCol = roCell.getColIndex();
                int endRow = startRow + roCell.getRowsRepeated() - 1; 
                int endCol = startCol + roCell.getColsRepeated() - 1;
                SheetRange sheetRange= new SheetRange(startRow, startCol, endRow, endCol);
                result.add(sheetRange);
            }
        }
    }
    
    public Collection<SheetRange> getResult() {
        return result;
    }

    private static boolean isEmpty(Cell cell) {
        return cell == null || EmptyCellPredicates.EMPTY_CELL_PREDICATE1.test(cell);
    }
    
}
