package com.adventnet.zoho.websheet.model.celledithistory;

import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CellEditHistory extends CellEditHistories implements Cloneable
{
	private static final Logger LOGGER = Logger.getLogger(CellEditHistory.class.getName());

	private Map<Integer, Map<Integer, List<CellEditHistoryBean>>> cellEditHistoryMap;

	public CellEditHistory()
	{
		super(Type.CELL_EDIT_HISTORY);
		this.cellEditHistoryMap = new ConcurrentHashMap<>();
	}

	public CellEditHistory(JSONObject cellEditHistoryJSON)
	{
		this();
		for(String rowIndex : cellEditHistoryJSON.keySet())
		{
			int rI = Integer.parseInt(rowIndex);
			this.cellEditHistoryMap.computeIfAbsent(rI, k -> new HashMap<>());
			for(String colIndex : cellEditHistoryJSON.getJSONObject(rowIndex).keySet())
			{
				int cI = Integer.parseInt(colIndex);
				this.cellEditHistoryMap.get(rI).computeIfAbsent(cI, k -> new ArrayList<>());
				JSONArray jsonArray = cellEditHistoryJSON.getJSONObject(rowIndex).getJSONArray(colIndex);
				for(int i=0; i<jsonArray.length(); i++)
				{
					JSONObject cellEditHistory = jsonArray.getJSONObject(i);
					List<CellEditHistoryBean> cellEditHistoryBeans = this.cellEditHistoryMap.get(rI).get(cI);
					cellEditHistoryBeans.add(new CellEditHistoryBean(cellEditHistory));
				}
			}
		}
	}

	public boolean addCellEditHistory(int rowIndex, int columnIndex, CellEditHistoryBean cellEditHistoryBean)
	{
		this.cellEditHistoryMap.computeIfAbsent(rowIndex, k -> new HashMap<>());
		List<CellEditHistoryBean> cellEditHistoryBeans = this.cellEditHistoryMap.get(rowIndex).computeIfAbsent(columnIndex, k -> new ArrayList<>());
		if(cellEditHistoryBeans.size() > 0)
		{
			CellEditHistoryBean existingBean = cellEditHistoryBeans.get(cellEditHistoryBeans.size() - 1);
			// UPDATE SAME BEAN WITH NEW VALUES IF SAME ACTION ID
			if(existingBean.getAid() == cellEditHistoryBean.getAid())
			{
				return existingBean.update(cellEditHistoryBean);
			}
		}
		return this.cellEditHistoryMap.get(rowIndex).get(columnIndex).add(cellEditHistoryBean);
	}

	public List<CellEditHistoryBean> getCellEditHistory(int rowIndex, int columnIndex)
	{
		return this.cellEditHistoryMap.get(rowIndex) != null ? this.cellEditHistoryMap.get(rowIndex) != null ? this.cellEditHistoryMap.get(rowIndex).get(columnIndex) : null : null;
	}

	public org.json.JSONArray getCellEditHistoryJSON(Workbook workbook, SimpleDateFormat sdf, int rowIndex, int colIndex, List<CellEditHistoryBean> cellEditHistories)
	{
		org.json.JSONArray cellHistoryJSONList = new org.json.JSONArray();
		if(cellEditHistories == null)
		{
			return cellHistoryJSONList;
		}
		for(CellEditHistoryBean cellEditHistoryBean : cellEditHistories)
		{
			org.json.JSONObject cellEditHistoryJson = cellEditHistoryBean.getJSON(workbook, sdf, rowIndex, colIndex);
			cellHistoryJSONList.put(cellEditHistoryJson);
		}
		return cellHistoryJSONList;
	}

	@Override
	public JSONObject getJSON(Workbook workbook, SimpleDateFormat sdf)
	{
		long startTime = System.currentTimeMillis();
		JSONObject cellEditHistoryJson = new JSONObject();
		for(Map.Entry<Integer, Map<Integer, List<CellEditHistoryBean>>> rowEntry : this.cellEditHistoryMap.entrySet())
		{
			int rowIndex = rowEntry.getKey();
			String rowIndexStr = String.valueOf(rowIndex);
			JSONObject columnJSONObject = cellEditHistoryJson.has(rowIndexStr) ? cellEditHistoryJson.getJSONObject(rowIndexStr) : new org.json.JSONObject();

			for(Map.Entry<Integer, List<CellEditHistoryBean>> colEntry: rowEntry.getValue().entrySet())
			{
				int colIndex = colEntry.getKey();
				String colIndexStr = String.valueOf(colIndex);
				org.json.JSONArray cellHistoryJSONList = columnJSONObject.has(colIndexStr) ? columnJSONObject.getJSONArray(colIndexStr) : new org.json.JSONArray();
				org.json.JSONArray cellEditHistoryList = getCellEditHistoryJSON(workbook, sdf, rowIndex, colIndex, colEntry.getValue());
				for(int i=0; i<cellHistoryJSONList.length(); i++)
				{
					cellEditHistoryList.put(cellHistoryJSONList.get(i));
				}
				columnJSONObject.put(colIndexStr, cellEditHistoryList);
			}
			cellEditHistoryJson.put(rowIndexStr, columnJSONObject);
		}

		JSONObject finalJSON = new JSONObject();
		finalJSON.put(JSONConstants.CELL_EDIT_HISTORY_KEY, cellEditHistoryJson);

		long endTime = System.currentTimeMillis();
		LOGGER.log(Level.INFO, "Total time taken to iterate cell edit histories {0}", endTime-startTime);
		return finalJSON;
	}

	public JSONArray getCellEditHistoryJSON(Workbook workbook, SimpleDateFormat sdf, int rowIndex, int columnIndex)
	{
		if(this.cellEditHistoryMap.get(rowIndex) != null && this.cellEditHistoryMap.get(rowIndex).get(columnIndex) != null)
		{
			List<CellEditHistoryBean> cellEditHistoryBeans = this.cellEditHistoryMap.get(rowIndex).get(columnIndex);
			return getCellEditHistoryJSON(workbook, sdf, rowIndex, columnIndex, cellEditHistoryBeans);
		}
		return null;
	}

	public Map<Integer, Map<Integer, List<CellEditHistoryBean>>> getCellEditHistoryMap()
	{
		return cellEditHistoryMap;
	}

	@Override
	public boolean equals(Object o)
	{
		if(this == o)
		{
			return true;
		}
		if(o == null || getClass() != o.getClass())
		{
			return false;
		}
		CellEditHistory that = (CellEditHistory) o;
		return Objects.equals(cellEditHistoryMap, that.cellEditHistoryMap);
	}

	@Override
	public int hashCode()
	{
		return Objects.hash(cellEditHistoryMap);
	}

	@Override
	public CellEditHistory clone()
	{
		try
		{
			CellEditHistory cellEditHistory = new CellEditHistory();
			for(Map.Entry<Integer, Map<Integer, List<CellEditHistoryBean>>> rowEntry : this.cellEditHistoryMap.entrySet())
			{
				Map<Integer, List<CellEditHistoryBean>> columnEntry = new LinkedHashMap<>();
				for(Map.Entry<Integer, List<CellEditHistoryBean>> colEntry: rowEntry.getValue().entrySet())
				{
					List<CellEditHistoryBean> cellEditHistories = new ArrayList<>();
					for(CellEditHistoryBean cellEditHistoryBean : colEntry.getValue())
					{
						cellEditHistories.add(cellEditHistoryBean.clone());
					}
					columnEntry.put(colEntry.getKey(), cellEditHistories);
				}
				this.cellEditHistoryMap.put(rowEntry.getKey(), columnEntry);
			}
			return cellEditHistory;
		}
		catch (CloneNotSupportedException e)
		{
			LOGGER.log(Level.SEVERE, "[CELL_EDIT_HISTORY][Exception] CellEditHistory can't cloned", e); // No I18N
		}
		return null;
	}

}
