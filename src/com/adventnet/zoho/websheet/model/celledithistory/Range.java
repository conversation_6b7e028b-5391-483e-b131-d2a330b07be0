package com.adventnet.zoho.websheet.model.celledithistory;

import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.adventnet.zoho.websheet.model.util.Utility;
import org.json.JSONObject;

import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

public class Range implements Cloneable
{
	private static final Logger LOGGER = Logger.getLogger(Range.class.getName());

	private int sr;
	private int sc;
	private int er;
	private int ec;

	public static final Range INVALID_RANGE = new Range(-1, -1, -1, -1);

	public Range(int sr, int sc, int er, int ec)
	{
		this.sr = sr;
		this.sc = sc;
		this.er = er;
		this.ec = ec;
	}

	public Range(JSONObject rangeObject)
	{
		this(rangeObject.getInt(JSONConstants.START_ROW), rangeObject.getInt(JSONConstants.START_COLUMN), rangeObject.getInt(JSONConstants.END_ROW), rangeObject.getInt(JSONConstants.END_COLUMN));
	}

	public int getSr()
	{
		return sr;
	}

	public void setSr(int sr)
	{
		this.sr = sr;
	}

	public int getSc()
	{
		return sc;
	}

	public void setSc(int sc)
	{
		this.sc = sc;
	}

	public int getEr()
	{
		return er;
	}

	public void setEr(int er)
	{
		this.er = er;
	}

	public int getEc()
	{
		return ec;
	}

	public void setEc(int ec)
	{
		this.ec = ec;
	}

	public boolean isEntireColumn()
	{
		return (this.getEr() - this.getSr())+1 == Utility.MAXNUMOFROWS;
	}

	public boolean isEntireRow()
	{
		return (this.getEc() - this.getSc())+1 == Utility.MAXNUMOFCOLS;
	}

	public Range setRange(Range range)
	{
		this.sr = range.getSr();
		this.sc = range.getSc();
		this.er = range.getEr();
		this.ec = range.getEc();

		return this;
	}

	public String toString()
	{
		return CellUtil.getCellReference(getSc(), getSr()) + ":" + CellUtil.getCellReference(getEc(), getEr());
	}

	public JSONObject getJSON()
	{
		JSONObject range = new JSONObject();
		range.put("sr", this.sr);
		range.put("sc", this.sc);
		range.put("er", this.er);
		range.put("ec", this.ec);
		return range;
	}

	@Override
	public boolean equals(Object o)
	{
		if(this == o)
		{
			return true;
		}
		if(o == null || getClass() != o.getClass())
		{
			return false;
		}
		Range range = (Range) o;
		return sr == range.sr && sc == range.sc && er == range.er && ec == range.ec;
	}

	@Override
	public int hashCode()
	{
		return Objects.hash(sr, sc, er, ec);
	}

	@Override
	public Range clone()
	{
		try
		{
			Range range = (Range)super.clone();
			return range;
		}
		catch(CloneNotSupportedException e)
		{
			LOGGER.log(Level.SEVERE, "[CELL_EDIT_HISTORY] Range can't cloned"); // No I18N
		}
		return null;
	}
}
