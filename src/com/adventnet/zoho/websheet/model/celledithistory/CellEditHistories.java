package com.adventnet.zoho.websheet.model.celledithistory;

import com.adventnet.zoho.websheet.model.Workbook;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.logging.Level;
import java.util.logging.Logger;

public abstract class CellEditHistories implements Cloneable
{
	private static final Logger LOGGER = Logger.getLogger(CellEditHistories.class.getName());
	public enum Type
	{
		CELL_EDIT_HISTORY,
		RANGE_TRANSFORMATION,
		REVERTED_VERSION
	}
	private Type type;
	public CellEditHistories(Type type)
	{
		this.type = type;
	}

	public Type getType()
	{
		return this.type;
	}

	public JSONObject getJSON(Workbook workbook, SimpleDateFormat sdf)
	{
		if(this.type == Type.CELL_EDIT_HISTORY)
		{
			return this.getJSON(workbook, sdf);
		}
		else if(this.type == Type.REVERTED_VERSION)
		{
			return ((RevertVersion)this).getJSON();
		}
		else
		{
			return ((RangeTransform)this).getJSON();
		}
	}

	@Override
	public int hashCode()
	{
		return super.hashCode();
	}

	@Override
	public boolean equals(Object obj)
	{
		return super.equals(obj);
	}

	@Override
	public CellEditHistories clone()
	{
		try
		{
			CellEditHistories cellEditHistories = (CellEditHistories) super.clone();
			return cellEditHistories;
		}
		catch (CloneNotSupportedException e)
		{
			LOGGER.log(Level.SEVERE, "[CELL_EDIT_HISTORY][Exception] CellEditHistories can't cloned", e); // No I18N
		}
		return null;
	}
}
