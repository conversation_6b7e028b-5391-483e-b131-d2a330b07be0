package com.adventnet.zoho.websheet.model.celledithistory;

import com.adventnet.zoho.websheet.model.util.JSONConstants;
import org.json.JSONObject;

public class RevertVersion extends CellEditHistories implements Cloneable
{

	private final String zuid;
	private final int aid;
	private final long timestamp;

	public RevertVersion(String zuid, int aid, long timestamp)
	{
		super(Type.REVERTED_VERSION);
		this.zuid = zuid;
		this.aid = aid;
		this.timestamp = timestamp;
	}

	public RevertVersion(JSONObject revertJSON)
	{
		super(Type.REVERTED_VERSION);
		this.zuid = revertJSON.getString(JSONConstants.ZUID);
		this.aid = revertJSON.getInt(JSONConstants.ACTION_ID);
		this.timestamp = revertJSON.getLong(JSONConstants.TIME_STAMP);
	}

	public String getZuid()
	{
		return zuid;
	}

	public int getAid()
	{
		return this.aid;
	}

	public long getTimestamp()
	{
		return timestamp;
	}

	@Override
	public RevertVersion clone()
	{
		return (RevertVersion) super.clone();
	}

	public JSONObject getJSON()
	{
		JSONObject revertJSON = new JSONObject();

		JSONObject revert = new JSONObject();
		revert.put(JSONConstants.TIME_STAMP, this.timestamp);
		revert.put(JSONConstants.ZUID, this.zuid);
		revert.put(JSONConstants.ACTION_ID, this.aid);

		revertJSON.put(JSONConstants.REVERT_VERSION_JSON_KEY, revert);
		return revertJSON;
	}
}
