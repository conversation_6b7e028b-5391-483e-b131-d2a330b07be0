package com.adventnet.zoho.websheet.model.celledithistory;

import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.dd.FRAGMENTVERSIONDFSSTORE;
import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.*;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.adventnet.zoho.websheet.model.celledithistory.CellEditHistories.Type.RANGE_TRANSFORMATION;
import static com.adventnet.zoho.websheet.model.celledithistory.CellEditHistories.Type.REVERTED_VERSION;
import static com.adventnet.zoho.websheet.model.celledithistory.Range.INVALID_RANGE;
import static com.adventnet.zoho.websheet.model.util.EngineConstants.CELL_EDIT_HISTORY_LIMIT;
import static com.adventnet.zoho.websheet.model.util.JSONConstants.*;

public class CellEditHistoryUtil
{
	private static final Logger LOGGER = Logger.getLogger(CellEditHistoryUtil.class.getName());

	private static Map<String, Object> getResponseMap(WorkbookContainer container, Map<Integer, Map<Integer, List<CellEditHistoryBean>>> cellEditHistories, double currentVersionNumber, double previousVersionNumber, int parentIndex, int nextIndex) throws Exception
	{
		Workbook workbook = container.getWorkbook(null);
		WorkbookAdditionalInfo workbookAdditionalInfo = container.getWorkbookAdditionalInfo();
		Locale locale = workbookAdditionalInfo.getSpreadsheetLocale();
		TimeZone tz = workbookAdditionalInfo.getTimeZone();
		SimpleDateFormat sdf = EngineUtils1.getDefaultDateFormat(locale, tz);
		HashMap<String, Object> cellEditHistoryMap = new HashMap<>();
		org.json.JSONObject userInfo = new org.json.JSONObject();
		org.json.JSONObject cellEditHistoryJSON = new org.json.JSONObject();
		for(Map.Entry<Integer, Map<Integer, List<CellEditHistoryBean>>> rowEntry : cellEditHistories.entrySet())
		{
			int rowIndex = rowEntry.getKey();
			String rowIndexStr = String.valueOf(rowIndex);
			org.json.JSONObject columnJSONObject = cellEditHistoryJSON.has(rowIndexStr) ? cellEditHistoryJSON.getJSONObject(rowIndexStr) : new org.json.JSONObject();
			for(Map.Entry<Integer, List<CellEditHistoryBean>> columnEntry : rowEntry.getValue().entrySet())
			{
				int colIndex = columnEntry.getKey();
				String colIndexStr = String.valueOf(colIndex);
				org.json.JSONArray cellHistoryJSONList = columnJSONObject.has(colIndexStr) ? columnJSONObject.getJSONArray(colIndexStr) : new org.json.JSONArray();

				List<CellEditHistoryBean> cellEditHistoryBeansList = columnEntry.getValue();
				for(CellEditHistoryBean cellEditHistoryBean : cellEditHistoryBeansList)
				{
//					if(previousVersionNumber == -1 && cellEditHistoryList.length() == 1 && cellHistoryJSON.has(JSONConstants.VALUE) && cellHistoryJSON.getString(JSONConstants.VALUE).isEmpty())
//					{
//						break;
//					}
					String userZUID = cellEditHistoryBean.getZuid();
					if(userZUID != null && !userInfo.has(userZUID))
					{
						String userName = DocumentUtils.getUserName(container.getDocsSpaceId(), userZUID);
						org.json.JSONObject user = new org.json.JSONObject();
						user.put(JSONConstants.USER_NAME, userName);
						userInfo.put(userZUID, user);
					}
					cellHistoryJSONList.put(cellEditHistoryBean.getJSON(workbook, sdf, rowIndex, colIndex));
				}
				columnJSONObject.put(colIndexStr, cellHistoryJSONList);
			}
			cellEditHistoryJSON.put(rowIndexStr, columnJSONObject);

		}
		cellEditHistoryMap.put(CREATED_TIME_STAMP, container.getCreatedTime());
		cellEditHistoryMap.put(USER_INFO, userInfo);
		cellEditHistoryMap.put(PARENT_INDEX, parentIndex);
		cellEditHistoryMap.put(INDEX, nextIndex);
		cellEditHistoryMap.put(VERSION_NO, currentVersionNumber);
		cellEditHistoryMap.put(PREVIOUS_VERSION_NO, previousVersionNumber);
		cellEditHistoryMap.put(JSONConstants.CELL_EDIT_HISTORY, cellEditHistoryJSON);
		return cellEditHistoryMap;
	}

	public static List<CellEditHistories> getCellEditHistories(JSONArrayWrapper cellEditHistories)
	{
		List<CellEditHistories> cellEditHistoriesList = new ArrayList<>();
		for(int i=0; i<cellEditHistories.length(); i++)
		{
			JSONObjectWrapper historyJSON = cellEditHistories.getJSONObject(i);
			if(historyJSON.has(CELL_EDIT_HISTORY_KEY))
			{
				cellEditHistoriesList.add(new CellEditHistory(historyJSON.getJSONObject(CELL_EDIT_HISTORY_KEY).getJsonObject()));
			}
			else if(historyJSON.has(TRANSFORM_JSON_KEY))
			{
				cellEditHistoriesList.add(new RangeTransform(historyJSON.getJSONArray(TRANSFORM_JSON_KEY).getJsonArray()));
			}
			else if(historyJSON.has(REVERT_VERSION_JSON_KEY))
			{
				cellEditHistoriesList.add(new RevertVersion(historyJSON.getJSONObject(REVERT_VERSION_JSON_KEY).getJsonObject()));
			}
		}
		return cellEditHistoriesList;
	}



	private static RangeTransform getRangeTransform(List<CellEditHistories> cellEditHistoriesList)
	{
		RangeTransform rangeTransform = new RangeTransform();
		for(int i=cellEditHistoriesList.size()-1; i>=0; i--)
		{
			CellEditHistories cellEditHistories = cellEditHistoriesList.get(i);
			if(!cellEditHistories.getType().equals(CellEditHistories.Type.RANGE_TRANSFORMATION))
			{
				return rangeTransform;
			}
			rangeTransform.mergeBefore(((RangeTransform) cellEditHistories).clone());
		}
		return rangeTransform;
	}

	private static int updateCellEditHistoryList(Workbook workbook, Range range, List<CellEditHistoryBean> existingList, List<CellEditHistoryBean> cellEditHistoryBeans, int index, AtomicInteger historyLimit)
	{
		int length = cellEditHistoryBeans.size();
		index = index == -1 ? length-1 : index;
//		index = index == 0 ? (limit == -1 ? length-1 : Math.min(length-1, index + limit)) : index;
		for(; index>=0; index--)
		{
			// UPDATE EXPRESSION STRING WITH TRANSFORMED RANGE INDEXES
			CellEditHistoryBean cellEditHistoryBean = cellEditHistoryBeans.get(index);
			cellEditHistoryBean.getValue(workbook, range.getSr(), range.getSc());
			// NO LIMIT SET IF -1
			if(historyLimit.get() == -1)
			{
				existingList.add(cellEditHistoryBean);
				continue;
			}
			if(historyLimit.get() <= 0)
			{
				break;
			}
			existingList.add(cellEditHistoryBean);
			historyLimit.decrementAndGet();
		}
		return index;
	}

	private static Map<String, Object> fetchCellEditHistories(WorkbookContainer container, Workbook workbook, Range range, Range rangeToTransform, RangeTransform rangeTransform, Iterator<Row> fragmentVerRowsItr, List<CellEditHistories> cellEditHistoriesList, double verNo, double prevVerNo, int pIndex, int index, AtomicInteger historyLimit, Map<Integer, Map<Integer, List<CellEditHistoryBean>>> cellEditHistoriesMap) throws Exception
	{
		pIndex = pIndex == -1 ? cellEditHistoriesList.size()-1 : pIndex;
		LOGGER.log(Level.SEVERE, "[CELL_EDIT_HISTORY] cellEditHistoriesList size {0}, pIndex {1} index {2} verno {3} prevVerNo {4} history limit {5}", new Object[]{cellEditHistoriesList.size(), pIndex, index, verNo, prevVerNo, historyLimit.get()});
		for(int i=cellEditHistoriesList.size()-1; i>=0; i--)
		{
			CellEditHistories cellEditHistories = cellEditHistoriesList.get(i);
			CellEditHistories.Type type = cellEditHistories.getType();
			if(i <= pIndex && type.equals(CellEditHistories.Type.CELL_EDIT_HISTORY))
			{
				CellEditHistory cellEditHistory = (CellEditHistory) cellEditHistories;
				rangeTransform.transformRange(rangeToTransform);
				// BREAK, IF TRANSFORMED RANGE IS NOT VALID
				if(rangeToTransform.equals(INVALID_RANGE))
				{
					return getResponseMap(container, cellEditHistoriesMap, verNo, -1, -1, -1);
				}
				int updatedRowIndex = rangeToTransform.getSr();
				int updatedColIndex = rangeToTransform.getSc();
				List<CellEditHistoryBean> cellEditHistoryBeans = cellEditHistory.getCellEditHistory(updatedRowIndex, updatedColIndex);
				if(cellEditHistoryBeans != null)
				{
					List<CellEditHistoryBean> existingList = cellEditHistoriesMap.get(range.getSr()).get(range.getSc());
					index = updateCellEditHistoryList(workbook, rangeToTransform, existingList, cellEditHistoryBeans, index, historyLimit);
					if(historyLimit.get() != -1)
					{
						if(historyLimit.get() <= 0)
						{
							if(index < 0)
							{
								i--;
							}
							// COMPLETE CELL EDIT HISTORY HAS BEEN SERVED, GET PREVIOUS VERSION NUMBER
							if(i < 0)
							{
								prevVerNo = fragmentVerRowsItr.hasNext() ? (double) fragmentVerRowsItr.next().get(FRAGMENTVERSIONDFSSTORE.VERSION) : -1;
								i=-1;
								index=-1;
							}
							return getResponseMap(container, cellEditHistoriesMap, verNo, prevVerNo, i, index);
						}
					}
					index=-1;
				}
				// RESET RANGE TRANSFORM
				rangeTransform.clear();
			}
			else if(type.equals(RANGE_TRANSFORMATION))
			{
				// MERGE IN SUCH A WAY THAT THE MERGING TRANSFORM IS ALWAYS ON TOP
				rangeTransform.mergeBefore((RangeTransform) cellEditHistories);
			}
			else if(i<=pIndex && type.equals(REVERTED_VERSION))
			{
				RevertVersion revertVersion = (RevertVersion) cellEditHistories;
				CellEditHistoryBean revertedVersionCard = new CellEditHistoryBean(revertVersion.getZuid(), revertVersion.getAid(), revertVersion.getTimestamp(), true);
				List<CellEditHistoryBean> existingList = cellEditHistoriesMap.get(range.getSr()).get(range.getSc());
				index = updateCellEditHistoryList(workbook, rangeToTransform, existingList, Collections.singletonList(revertedVersionCard), index, historyLimit);
				if(historyLimit.get() != -1)
				{
					if(historyLimit.get() <= 0)
					{
						if(index < 0)
						{
							i--;
						}
						// COMPLETE CELL EDIT HISTORY HAS BEEN SERVED, GET PREVIOUS VERSION NUMBER
						if(i < 0)
						{
							prevVerNo = fragmentVerRowsItr.hasNext() ? (double) fragmentVerRowsItr.next().get(FRAGMENTVERSIONDFSSTORE.VERSION) : -1;
							i=-1;
							index=-1;
						}
						return getResponseMap(container, cellEditHistoriesMap, verNo, prevVerNo, i, index);
					}
				}
				index=-1;
			}
		}
		return null;
	}

	public static Map<String, Object> getCellEditHistory(WorkbookContainer container, Sheet sheet, RangeUtil.SheetRange sheetRange, double verNo, int pIndex, int index) throws Exception
	{

        /* 1. GET LIVE EDIT HISTORY MAP AND RETURN IF IT HAS SATISFIED THE SET LIMIT
           2. IF CELL EDIT HISTORY FRAGMENT ISN'T AVAILABLE, RETURN EMPTY MAP
           2. IF LIVE EDIT HISTORY MAP HASN'T SATISFIED LIMIT, GO TO 4
           3. READ THE CELL EDIT HISTORY WITH REQUESTED VERSION NUMBER
           4. IF CELL EDIT HISTORY IS NOT AVAILABLE FOR REQUESTED CELL OR CELL HISTORY LENGTH IS LESS THAN REQUESTED LIMIT
               4A. GET ALL THE VERSION ROWS
               4B. LOOP UNTIL THERE'S ENOUGH CELL EDIT HISTORY WITH SET LIMIT FOR THE REQUESTED CELL INDEX.
               4C. EXIT IF CELL EDIT HISTORY IS FOUND WITH SET LIMIT. SET ACTUAL VERSION NUMBER AND PREVIOUS VERSION NUMBER
           5. RETURN THE MAP CONTAINING CELL EDIT HISTORY, USER INFO, ACTUAL VERSION NUMBER AND PREVIOUS NUMBER
         */
		Map<Integer, Map<Integer, List<CellEditHistoryBean>>> cellEditHistoriesMap = new HashMap<>();
		Workbook workbook = container.getWorkbook(null);
		AtomicInteger limit = new AtomicInteger();
		limit.set(CELL_EDIT_HISTORY_LIMIT);
		String docOwner = container.getDocOwner();
		String asn = sheet.getAssociatedName();
		long fragId = container.getCellEditHistoryFragmentId(asn, false);
		double lastVerNo = ZSStore.getLastFragmentVersionNumber(docOwner, fragId);
		double noMoreVersions = -1;
		RangeIterator rangeItr = new RangeIterator(sheet, sheetRange.getStartRowIndex(), sheetRange.getStartColIndex(), sheetRange.getEndRowIndex(), sheetRange.getEndColIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, true, true);
		while(rangeItr.hasNext())
		{
			RangeTransform rangeTransform = new RangeTransform();
			ReadOnlyCell rOCell = rangeItr.next();
			int rowIndex = rOCell.getRowIndex();
			int columnIndex = rOCell.getColIndex();
			Range range = new Range(rowIndex, columnIndex, rowIndex, columnIndex);
			Range rangeToTransform = new Range(rowIndex, columnIndex, rowIndex, columnIndex);

			cellEditHistoriesMap.computeIfAbsent(range.getSr(), k -> new HashMap<>());
			cellEditHistoriesMap.get(range.getSr()).computeIfAbsent(range.getSc(), k -> new ArrayList<>());
			// READ LIVE VERSION FIRST ON EACH ITERATION OF CELLS
			if(verNo == 0d)
			{
				Map<String, Object> responseMap = fetchCellEditHistories(container, workbook, range, rangeToTransform, rangeTransform, null, sheet.getCellEditHistoriesList(), verNo, lastVerNo, -1, -1, new AtomicInteger(-1), cellEditHistoriesMap);
				if(responseMap != null)
				{
					return responseMap;
				}
			}
			else
			{
				rangeTransform = sheet.getRangeTransform();
			}
			// READING VERSION IN DESCENDING ORDER I.E SORTED BY VERSION NUMBER IN DESCENDING ORDER BY DEFAULT
			Iterator<Row> fragmentVerRowsItr = ZSStore.getFragmentVersionRows(docOwner, fragId);
			verNo = verNo == 0 ? lastVerNo : verNo;
			while(fragmentVerRowsItr.hasNext())
			{
				Row fragVerRow = fragmentVerRowsItr.next();
				long fragVerId = (long) fragVerRow.get(FRAGMENTVERSIONDFSSTORE.FRAGMENT_VERSION_DFSSTORE_ID);
				lastVerNo = (double) fragVerRow.get(FRAGMENTVERSIONDFSSTORE.VERSION);
				String history = EngineUtils1.readCellEditHistoryVersion(container, fragVerId, asn, false);
				if(history == null)
				{
					LOGGER.log(Level.SEVERE, "[CELL_EDIT_HISTORY][Exception] Cell Edit history for fragment version id {0}, asn {1} is null", new Object[]{fragVerId, asn});
					throw new Exception("[CELL_EDIT_HISTORY][Exception] Cell Edit history for fragment version id " + fragVerId + ", asn " + asn + " is null");
				}
				JSONArrayWrapper historyJSONArray = new JSONArrayWrapper(history);
				List<CellEditHistories> cellEditHistoriesList = getCellEditHistories(historyJSONArray);
				if(lastVerNo <= verNo)
				{
					Map<String, Object> responseMap = fetchCellEditHistories(container,  workbook, range, rangeToTransform, rangeTransform, fragmentVerRowsItr, cellEditHistoriesList, verNo, lastVerNo, pIndex, index, limit, cellEditHistoriesMap);
					if(responseMap != null)
					{
						return responseMap;
					}
					pIndex = -1;
				}
				else
				{
					rangeTransform.mergeBefore(getRangeTransform(cellEditHistoriesList));
				}
			}
			verNo = 0d;
		}
		return getResponseMap(container, cellEditHistoriesMap, lastVerNo, noMoreVersions, -1, -1);
	}

	/****************************************** CELL EDIT HISTORY ******************************************************/

	public static class CellEditHistoryBuilder
	{
		private final StringBuilder builder;
		private long size;

		public CellEditHistoryBuilder()
		{
			this.builder = new StringBuilder();
			this.size = 0;
		}

		public StringBuilder getBuilder()
		{
			return this.builder;
		}

		public long getSizeInBytes()
		{
			return this.size;
		}

		public int length()
		{
			return this.builder.length();
		}

		public CellEditHistoryBuilder append(String s)
		{
			if(s != null)
			{
				this.builder.append(s);
				this.size += s.length();
			}
			return this;
		}

		public CellEditHistoryBuilder append(StringBuilder s)
		{
			if(s != null)
			{
				this.builder.append(s);
				this.size += s.length();
			}
			return this;
		}

		public CellEditHistoryBuilder appendAlongWithSize(String s, long size)
		{
			if(s != null)
			{
				this.builder.append(s);
				this.size += size;
			}
			return this;
		}

		public void replace(int start, int end, String s)
		{
			if(s != null)
			{
				this.builder.replace(start, end, s);
			}
		}

		public void reset()
		{
			this.builder.delete(0, this.builder.length());
			this.size = 0;
		}
	}
}
