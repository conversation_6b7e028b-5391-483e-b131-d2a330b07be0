package com.adventnet.zoho.websheet.model.celledithistory;

public enum Direction
{
	UP(1), // DELETE ROW
	DOWN(2), // INSERT ROW
	LEFT(3), // DELETE COLUMN
	RIGHT(4);   //  INSERT COLUMN

	private int id;
	Direction(int id)
	{
		this.id = id;
	}

	public int getId()
	{
		return this.id;
	}

	@Override
	public String toString()
	{
		return String.valueOf(this.getId());
	}

	public static Direction getDirection(int id)
	{
		for(Direction direction : Direction.values())
		{
			if(direction.getId() == id)
			{
				return direction;
			}
		}
		return null;
	}

}
