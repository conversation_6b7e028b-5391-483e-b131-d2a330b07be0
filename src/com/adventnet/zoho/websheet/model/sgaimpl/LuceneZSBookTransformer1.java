/* $Id$ */
package com.adventnet.zoho.websheet.model.sgaimpl;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.filter.Filter;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.shapes.Equation;
import com.adventnet.zoho.websheet.model.shapes.Handle;
import com.adventnet.zoho.websheet.model.shapes.ShapeAnchor;
import com.adventnet.zoho.websheet.model.shapes.ShapeGroup;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.EngineUtils1;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSFontScheme;
import com.adventnet.zoho.websheet.model.util.EngineUtils1;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.zsparser.ZSEventListener;
import com.adventnet.zoho.websheet.model.zsparser.ZSWorkbookParser1;
import com.zoho.sas.container.AppResources;
import org.xmlpull.v1.XmlPullParserException;

import java.io.*;
import java.util.BitSet;
import java.util.List;
import java.util.StringTokenizer;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * To extract details from .sxc file for Lucene indexing.
 * Right now, sheet names, cell text values and cell comments are extracted..
 *
 */
public class LuceneZSBookTransformer1 implements ZSEventListener
{
    public Throwable throwable = null;
    public static void main(String[] args) throws IOException
    {
        /*Reader reader = new LuceneODSBookTransformer1("ods").transform(new FileInputStream(args[0]));//i18N
        PrintWriter myout = new PrintWriter(new FileOutputStream("lucene.txt"));
        char[] buff = new char[100];
        int size = -1;
        while ((size = reader.read(buff)) != -1)
        {
            myout.write(buff, 0, size);
        }
        System.in.read();
        reader.close();
        myout.close();      */

    	/*LuceneODSBookTransformer1  odsparserObj = new LuceneODSBookTransformer1("zfgmt");
    	InputStreamReader inStreamReader = (InputStreamReader) odsparserObj.transform(new FileInputStream(new File("c:\\searchagent.xml")));
        BufferedReader buffReader = new BufferedReader(inStreamReader);

        String contents = null;
        while((contents = buffReader.readLine()) != null)
        {
        	System.out.println(contents);
        }
        inStreamReader.close();
        buffReader.close();*/

    }


    @Override
    public void updateProtectedRange(String cellRangeAddress, String authUsersStr, String unAuthUsersStr, String authGroupsStr, String authOrgsStr, String isPubAuthorized, boolean isAllowInsert, boolean isAllowFormats, String authExternalShareLinksStr, String unAuthExternalShareLinksStr) {
//        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void updateDefaultFilterView(String rangeAddress, Filter filters)
    {

    }

    @Override
    public void updateNamedFilterView(String name, String targetRangeAddress, Filter filter)
    {

    }

    public void updateConditionStyle(String targetAddress, String applyStyleName, String condition, String baseCellAddress, String priority) {
    }

    public void updateConditionalFormats(String targetAddress, ConditionalStyle conditionalStyle) {
    }

    @Override
    public void startConditionalFormats()
    {
    }

    @Override
    public void startConditionalFormat(String targetRangeAddress, String priority) throws SheetEngineException{}

    @Override
    public void updateCondition(String applyStyleName, String condition, String baseCellAddress, int applyColIndex){}

    @Override
    public void updateColorScale(boolean isAutoColor, boolean isHideText){}

    @Override
    public void updateColorScaleEntry(ConditionalFormatEntry.Entry_Type type, String value, ZSColor color, String baseCellAddress){}

    @Override
    public void updateIconSet(String iconSetName, boolean iconDefaultSize, boolean iconReverseOrder, boolean isAutoColor, boolean isHideText){}

    @Override
    public void updateIconSetEntry(ConditionalFormatEntry.Entry_Type cstype, String aValue, String aIconName, Integer aIconID, String iconCriteria, String baseCellAddress){}

    @Override
    public void updateDataBar(DataBar.Direction dbDirection, DataBar.Type fillType, ZSColor fillPColor, ZSColor fillNColor, DataBar.Type borderType, ZSColor borderPColor, ZSColor borderNColor, DataBar.Axis axisPosition, ZSColor axisColor, boolean isAutoColor, boolean isHideText){}

    @Override
    public void updateDataBarEntry(ConditionalFormatEntry.Entry_Type cstype, String aValue, String baseCellAddress){}

    @Override
    public void updateMergeCells(Cell cell, int rowSpan, int colSpan) {
    }

    @Override
    public void updateCellLink(Cell cell, String link) {

    }

    @Override
    public void updateSparkline(String source, String destination, String orientation, String minValue, String maxValue) {
    }

    @Override
    public void updateSparklinesGroup() {
    }

    @Override
    public void updateSparklineProperties(String type, String sparklineColor, String sparklineColorTint, String sparklineColorTheme, boolean isMarkerRequired, String markerColor, String markerColorTint, String markerColorTheme, String negativeColor, String negativeTint, String negativeTheme, String highPointColor, String highPointTint, String highPointTheme, String lowPointColor, String lowPointTint, String lowPointTheme, String firstPointColor, String firstPointTint, String firstPointTheme, String lastPointColor, String lastPointTint, String lastPointTheme, String minType, String maxType, boolean isXAxisRequired, String hiddenCells, String emptyCells, boolean isReversed) {
    }

    /**
     * Just overrided close() method - so that we will get control when Lucene closes the PipedReader stream
     */
    public static class MyPipedInStream extends PipedInputStream
    {
        Thread readerThread;

        public void setReaderThread(Thread readerThread)
        {
            this.readerThread = readerThread;
        }

        @Override
        public void close() throws IOException
        {
            super.close();
            if (readerThread.isAlive())
            {
                readerThread.interrupt();
            }
        }
    }

    private Writer pipedWriter;
    private ZSWorkbookParser1 parser;
    private int wordCount;
    private static final char DELIMITER = ' ';
    public StringBuffer workbookLinks = new StringBuffer();
    private static final int DEFAULT_MAX_LENGTH = (Integer.parseInt(AppResources.getProperty("org.apache.lucene.maxFieldLength", "30000")) * 5) / 6;
    /**
     * Default Anti Spam content limit - 10KB
     */
    private static final int ANTI_SPAM_MAX_LENGTH = com.zoho.conf.Configuration.getInteger("antispam.content.length", 10240);   // NO I18N
    private final static Logger LOG = Logger.getLogger("com.zoho.sheet.lucene");

    /**
     * Distinguish request from anti spam team for hyperlink with content
     */
    private boolean isFromAntiSpam = false;
    /**
     * Distinguish request from anti spam team for hyperlink with content
     */
    private boolean isParseLinks = false;
    /**
     * Maximum length to parse the content. A value of -1 indicates parsing full content.
     */
    private int maxLength;

    private String fileFormat = null;

    /**
     * @param writer The parsed details will be written to this writer
     */
    private LuceneZSBookTransformer1(Writer writer)
    {
        this.pipedWriter = writer;
        this.maxLength = DEFAULT_MAX_LENGTH;
    }

    /**
     *
     * @param writer
     * @param isFromAntiSpam - Set to true to parse all the content of the file without limits
     */
    public LuceneZSBookTransformer1(Writer writer, boolean isFromAntiSpam)
    {
        this.pipedWriter = writer;
        this.isFromAntiSpam = isFromAntiSpam;
        this.isParseLinks = isFromAntiSpam;
        this.maxLength = isFromAntiSpam ? ANTI_SPAM_MAX_LENGTH : DEFAULT_MAX_LENGTH;
    }

    /**
     *
     * @param writer
     * @param isFromAntiSpam - Set to true to parse all the content of the file without limits
     * @param maxLength - Set the maximum length of the content to be parsed.
     *                  0 indicates to take the default length from {@link #DEFAULT_MAX_LENGTH}
     *                  -1 indicates to parse all data irrespective of its length
     */
    public LuceneZSBookTransformer1(Writer writer, boolean isFromAntiSpam, int maxLength)
    {
        this.pipedWriter = writer;
        this.isFromAntiSpam = isFromAntiSpam;
        this.isParseLinks = isFromAntiSpam;
        this.maxLength = maxLength == 0 ? (isFromAntiSpam ? ANTI_SPAM_MAX_LENGTH : DEFAULT_MAX_LENGTH) : maxLength;
    }

    public LuceneZSBookTransformer1()
    {
        this.maxLength = DEFAULT_MAX_LENGTH;
    }

    public LuceneZSBookTransformer1(String fileFormat)
    {
        this.fileFormat = fileFormat;
        this.maxLength = DEFAULT_MAX_LENGTH;
    }

    public boolean isFromAntiSpam()
    {
        return this.isFromAntiSpam;
    }

    public boolean isParseLinks()
    {
        return this.isParseLinks;
    }

    /**
     * This method will start a seperate thread and dump the output to a 'PipedOutputStream'. Using the 'Reader' object
     * returned by this method, one can read the piped output
     * <p> Make sure to close the returned 'Reader' once reading is done !!
     * @param sxcIn InputStream of the .sxc file which needs to be parsed for Lucene indexing
     * @return The Reader for reading the piped output details
     * @throws IOException
     */
    public Reader transform(final InputStream sxcIn, boolean isFromAntiSpam, int maxLength) throws IOException
    {
        MyPipedInStream pipeIn = new MyPipedInStream();
        final OutputStreamWriter pipedWriter = new OutputStreamWriter(new PipedOutputStream(pipeIn));

        LuceneZSBookTransformer1 transformer1 = this;
        // start parsing the .sxc in a seperate thread
        // -------------------------------------------
        Thread readerThread = new Thread("Lucene-")//NO I18N
        {
            @Override
            public void run()
            {
                // LOG.info(getName() + " - START");
                LuceneZSBookTransformer1 trans = new LuceneZSBookTransformer1(pipedWriter, isFromAntiSpam, maxLength);
                try
                {
                    trans.parser = new ZSWorkbookParser1(trans, true);
                    trans.parser.parse(sxcIn, null);
                    transformer1.workbookLinks = trans.workbookLinks;
                }
                catch (XmlPullParserException e)
                {
                    LOG.log(Level.SEVERE, "XPP Error in workbook " , e);
                    throwable = e;
                }
                catch (IOException e)
                {
                    LOG.log(Level.SEVERE, "IO Error(.sxc stream) in workbook " , e);
                    throwable = e;
                }
                catch(Throwable e)
                {
                    LOG.log(Level.SEVERE, "Parsing Error(.sxc stream) in workbook ", e);
                    throwable = e;
                }
                finally
                {
                    try
                    {
                        trans.pipedWriter.close();
                    }
                    catch (IOException e)
                    {
                        LOG.log(Level.WARNING, "Stream closing error in workbook " , e);
                        throwable = e;
                    }
                }
                // LOG.info(getName() + " - END");
            }
        };
        pipeIn.setReaderThread(readerThread);
        readerThread.start();
        return new InputStreamReader(pipeIn);
    }


    /**
     * This method will start a seperate thread and dump the output to a 'PipedOutputStream'. Using the 'Reader' object
     * returned by this method, one can read the piped output
     * <p> Make sure to close the returned 'Reader' once reading is done !!
     * @param sxcIn InputStream of the .sxc file which needs to be parsed for Lucene indexing
     * @return The Reader for reading the piped output details
     * @throws IOException
     */
    public Reader transform(final InputStream sxcIn, boolean isFromAntiSpam) throws IOException
    {
        return transform(sxcIn, isFromAntiSpam, 0);
    }

    /**
     * This method will start a seperate thread and dump the output to a 'PipedOutputStream'. Using the 'Reader' object
     * returned by this method, one can read the piped output
     * <p> Make sure to close the returned 'Reader' once reading is done !!
     * @param sxcIn InputStream of the .sxc file which needs to be parsed for Lucene indexing
     * @return The Reader for reading the piped output details
     * @throws IOException
     */
    public Reader transform(final InputStream sxcIn) throws IOException
    {
        return transform(sxcIn, false);
    }

    /**
     * Method for writing a content to PipedWriter. If MAX_LENGTH is reached, content won't be written to the 'pipe'
     * @param content The content to be written to the 'pipe'
     * @return true, if the content is written to the 'pipe', false otherwise
     */
    private boolean writeToPipe(String content)
    {
        if (content == null || content.isEmpty())
        {
            return true;
        }

        if (maxLength != -1)
        {
            wordCount += new StringTokenizer(content, " \n\t\r\f,.:;@&-_()[]{}/\\=?").countTokens();//NO I18N
            if(wordCount > maxLength)
            {
                LOG.log(Level.INFO, "maxLength({0})reached for workbook ", maxLength);//NO I18N
                return false;
            }
        }
        try
        {
            pipedWriter.write(content + DELIMITER);
        }
        catch (InterruptedIOException ioE)
        {
            LOG.log(Level.INFO, "INTERRUPT - ; WordCount - {0}", wordCount);//NO I18N
            return false;
        }
        catch (IOException e)
        {
            LOG.log(Level.WARNING, "Write(content) error in workbook ", e);
            return false;
        }
        return true;
    }

    @Override
    public void updateStylesVersionId(int stylesVID){

    }

    // ODSWorkbookTransformer start here
    /**
     * Will be invoked, when end of 'style:font-decl' node is reached
     */
    @Override
    public void updateFontFace(FontFace fontFace)
    {
    }

    /**
     * Will be invoked, when end of 'style:style' node is reached, whose
     * 'style:family' attribute value is 'table-column'
     */
    @Override
    public void updateColumnStyle(ColumnStyle columnStyle)
    {
    }

    /**
     * Will be invoked, when end of 'style:style' node is reached, whose
     * 'style:family' attribute value is 'table-row'
     */
    @Override
    public void updateRowStyle(RowStyle rowStyle)
    {
    }

    /**
     * Will be invoked, when end of 'style:style' node is reached, whose
     * 'style:family' attribute value is 'table-cell'
     */
    @Override
    public void updateCellStyle(CellStyle cellStyle)
    {
    }

    /**
     * Will be invoked, when start of 'table:table' node is reached
     * @param sheet has sheet details
     */
    @Override
    public void startSheet(Sheet sheet)
    {
        if (!writeToPipe(sheet.getName()))
        {
            parser.stop();
            return;
        }
    }

    /**
     * Will be invoked, when end of 'table:table' node is reached
     * @param sheet has sheet details
     */
    @Override
    public void endSheet(Sheet sheet)
    {
    }
    public void updateNonDynamicArraySheet(Sheet sheet){
    }

    /**
     * Will be invoked, when end of 'table:table-column' node is reached
     */
    @Override
    public void updateColumnHeader(ColumnHeader columnHeader, ColumnVisibility columnvisibility)
    {
    }


    /**
     * Will be invoked, when start of 'table:table-row' node is reached
     */
    @Override
    public void startRow(Row row)
    {
    }

    /**
     * Will be invoked, when end of 'table:table-row' node is reached
     */
    @Override
    public void endRow(Row row)
    {
    }

    /**
     * Will be invoked, when end of 'table:covered-table-cell' node is reached
     */
    @Override
    public void updateDummyCellRepeated(int colRepeated)
    {
    }

    /**
     * Will be invoked, when end of 'table:table-cell' node is reached
     * @param frame
     */
    @Override
    public void updateFrameList(Frame frame) {
        throw new UnsupportedOperationException("Not supported yet.");//No I18N
        //To change body of generated methods, choose Tools | Templates.
    }

    public String getLinks()
    {
        return this.workbookLinks.toString();
    }

    private void updateCellContents(Cell cell)
    {
        Value value = cell.getValue();
        String content = value.getValueString(SpreadsheetSettings.defaultSpreadsheetSettings);
        if (!writeToPipe(content))
        {
            parser.stop();
            return;
        }
    }

    private void updateCellLinks(Cell cell)
    {
        Value value = cell.getValue();
        String cellLink = cell.getLink();
        // DIRECT HYPERLINK IN THE CELL
        if(cellLink != null && !cellLink.isEmpty() && !cellLink.startsWith("#") && !cellLink.startsWith("mailto:") && !cellLink.startsWith("tel:"))
        {
            workbookLinks.append(cell.getLink() + DELIMITER);
        }
        // INSTANCE OF ZSSTRING THAT WILL CONTAIN MULTIPLE HYPERLINKS IN THE CELL VALUE
        if(value.getRawValue() instanceof ZSString)
        {
            ZSString richString = (ZSString)value.getRawValue();
            List<RichStringProperties> richStringPropertiesList = richString.getProperties();
            for(RichStringProperties stringProperty : richStringPropertiesList)
            {
                String label = stringProperty.getLabel();
                String url = stringProperty.getUrl();
                // TO AVOID WRITING DUPLICATE LINKS WHEN THERE IS HYPERLINK LINKED TO THE CELL
                if(url != null && (cellLink == null || (cellLink != null && !cellLink.equals(url))))
                {
                    workbookLinks.append(url + DELIMITER);
                }
                boolean isLabelURL = EngineUtils1.validateURL(label);
                if(isLabelURL && !url.contains(label))
                {
                    workbookLinks.append(label + DELIMITER);
                }
            }
        }
    }

    @Override
    public void updateCell(Cell cell, String picklistAndItemIndex, String picklistAndItemID, String picklistSourceID, boolean isArrayCell, List<Integer> picklistItemIDs)
    {
        updateCellContents(cell);
        if(isParseLinks)
        {
            updateCellLinks(cell);
        }
        Annotation annot = cell.getAnnotation();
        if(annot != null && !writeToPipe(cell.getAnnotation().getContent()))
        {
            /* if (!writeToPipe(cell.getAnnotation().getContent()))
             {*/
            parser.stop();
            return;
            //}
        }
        if(cell.isFormula() && (!writeToPipe(Utility.masknull(cell.getFormula(), ""))))
        {
			 /*if (!writeToPipe(cell.getFormula()))
			 {*/
            parser.stop();
            return;
            /* }*/
        }
    }

    @Override
    public void constructWorkbook(String workbookName)
    {
    }

    @Override
    public void endWorkbook()
    {
    }

    @Override
    public void updateSheetStyle(SheetStyle sheetStyle)
    {
    }

    @Override
    public void updateForms(Forms forms)
    {
    }

    // TODO : Just the style name suppopublic rted, may be use it in future
    @Override
    public void updateGraphicStyle(GraphicStyle graphicStyle)
    {
    }

    @Override
    public void updateParagraphStyle(ParagraphStyle paragraphStyle)
    {
    }

    @Override
    public void updateTextStyle(TextStyle textStyle)
    {
    }

    @Override public ZSPattern updatePattern(String patternString, boolean isAccountingPattern, boolean isAutoOrder) {
        return null;
    }


    @Override
    public void updateDefaultDatePattern(String patternStr)
    {

    }

//    public void updateNamedExpressions(String name, String cellRangeAddress, String baseCellAddress)
//    {
//    }

    @Override
    public void updatePivotTable(PivotTable pivotTable)
    {
    }
    public void updateSlicer(Slicer slicer)
    {
    }
    @Override
    public void startTableStylesNode() {}

    @Override
    public void endTableStylesNode() {}

    @Override
    public void startTableStyleNode(String tableStyleName, boolean isCustom) {}

    @Override
    public void endTableStyleNode() {}

    @Override
    public void endDefaultTableStyleNode(){}

    @Override
    public void updateCellStyleForTableStyles(CellStyle cellStyle) {}

    @Override
    public void updateTableStyleProperty(String propertyKey, String stripeSizeStr, String cellStyleName) {}

//    @Override
//    public void updateNamedExpression(String arg0, String arg1, String arg2, boolean arg3) {
//        // TODO Auto-generated method stub
//
//    }

    @Override
    public void updateMapStyle(String condition, String baseCellAddress, String applyStyleName, Object style)
    {
    }

    @Override
    public void updateNamedExpression(String name, String scopeASN, String comment, String expression, String baseCellAddress, boolean isNamedRange) {

    }

    @Override
    public void updateContentValidation(String condition, String baseCellAddress, String validationName, boolean isAllowEmptyCell, String displayList, DVHelpMessage helpMessage, DVErrorMessage errorMessage)
    {
    }

    @Override
    public void updatePicklist(String picklistID, String startsWith, String startsWithID, String ranges, String sourceRangeString, String isRangePicklist, boolean showAsBubble, String autoStyleID, boolean allowMultiSelect, Picklist.DropdownStyle dropdownIcon, Picklist.StackDirection stackDirection) {
    }

    @Override
    public void updateRangePicklist(String picklistID, String rangeStrings, String sourceRangeStrings, String sort, String autoStyleID, boolean showAsBubble, boolean allowMultiSelect, Picklist.DropdownStyle dropdownIcon, Picklist.StackDirection stackDirection) {
    }

    @Override
    public void endPicklists() {
    }

    @Override
    public Picklist getPicklist(int picklistID) {
        return null;
    }

    @Override
    public void updateListItem(String itemID, String displayString, String valueString, String color, String colorTheme, String colorTint, String bgColor, String bgTheme, String bgTint, String valueID) {
    }

    @Override
    public void createPicklistAutoColorsList() {

    }

    @Override
    public void addAutoColor(String textColor, String textColorTheme, String textColorTint, String bgColor, String bgTheme, String bgTint) {

    }

    @Override
    public void endPicklistAutoColorNode(int id) {

    }

    @Override
    public void updateFormRange(String cellRangeAddress)
    {}

    @Override
    public void updateCommentRange(String name, String changeFormula) {

    }
    @Override
    public void updateNotificationRange(String name, JSONObjectWrapper notifyJson) {

    }
    @Override
    public void updateCheckboxRange(String cellRangeAddress)
    {
    }

    @Override
    public void putCellExpressionNameEntry(String formulaName, Cell cell){
    }

    @Override
    public void putExpressionNameExpressionEntry(String formulaName, String expressionString){
    }

    @Override
    public List<Cell> createAndSetExpressionForReferredCells(){
        return null;
    }

    @Override
    public void publishDefaultColumnWidth(String columnWidthStr) {}

    @Override
    public void updatePlaceholder(String name, String baseCellAddress, String rangeAddress, String desc) {

    }


    @Override
    public void updateEmptyFilterView(int id, String zuid, String sheetAsn) {

    }


    @Override
    public void setRowVisibility(Row row, RowVisibility visibility) {
        // TODO Auto-generated method stub

    }
    //////////////////


    @Override
    public void updateFilterView(int id, int tableID, String rangeAddress, Filter filter, BitSet filteredRowsSet, boolean displayFilterButtons, List<String> zuids) {
        // TODO Auto-generated method stub

    }    //////////////////

    @Override
    public void updateThemeProperties(ZSColorScheme colorScheme, ZSFontScheme fontScheme){

    }
    
    @Override
    public  void addSheetImage(int id, int imageID, double height, double width, int rowIndex, int columnIndex, double rowDiff, double columnDiff)
    {

    }


    public void updateField(Field field)
    {

    }

    public Field getField(int id)
    {
        return null;
    }
    @Override
    public void updateEnhancedGeometry(String viewBox, String textArea, String type, String modifiers, String enhancedPath, String gluePoints, String pathStretchPointX, String pathStretchPointY, String mirrorHorizontal, String mirrorVertical) {

    }

    @Override
    public void updateEquation(Equation equation) {

    }

    @Override
    public void updateHandle(Handle handle) {

    }

    @Override
    public void updateTextInCustomShape(RichStringProperties linkObj) {

    }

    @Override
    public void updateCustomShapeParagraph(String paragraphStyle) {

    }

    @Override
    public void updatePolyLine(String id, String zIndex, String styleName, String textStyleName, String width, String height, String viewBox, String transform, String points) {

    }

    @Override
    public void updatePath(String id, String zIndex, String styleName, String textStyleName, String width, String height, String viewBox, String transform, String d) {

    }

    @Override
    public void updateLine(String id, String zIndex, String styleName, String textStyleName, String x1, String x2, String y1, String y2) {

    }

    @Override
    public void updateEllipse(String id, String zIndex, String styleName, String textStyleName, String height, String width, String svgX, String svgY, String startAngle, String endAngle, String kind) {

    }

    @Override
    public void updateCustomShape(String id, String endCellAddress, String endX, String endY, String zIndex, String name, String styleName, String textStyleName, String width, String height, String svgX, String svgY) {

    }

    @Override
    public void updateAnchor(ShapeAnchor anchor) {

    }

    @Override
    public void updateGroup(ShapeGroup group) {

    }

    @Override
    public void addShapeGroupToSheet() {

    }

    @Override
    public void updateDataConnection(String name, String baseCellAddress, String rangeAddress) {

    }

    @Override
    public void updateWorkbookSettings(WorkbookSettings workbookSettings) {

    }

}
