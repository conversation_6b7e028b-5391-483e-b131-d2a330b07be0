package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.util.DataRange;

import java.util.*;
import java.util.stream.Collectors;

public class TableDataChangeTracker {
    private Map<Integer, Set<Integer>> cellMap = new HashMap<>();
    private boolean disableTrackingForPaste = false;

    public void notifyChange(int row, int col) {
        if(this.disableTrackingForPaste) {
            return;
        }

        Set<Integer> colSet = cellMap.computeIfAbsent(row, s-> new HashSet<>());
        colSet.add(col);
    }

    public Map<Integer, Set<Integer>> getAndFlushChanges() {
        Map<Integer, Set<Integer>> modifiedMap = this.cellMap;
        this.cellMap = new HashMap<>();

        return Collections.unmodifiableMap(modifiedMap);
    }

    public TableDataChangeTracker clone() {
        TableDataChangeTracker cloned = new TableDataChangeTracker();

        for(Map.Entry<Integer, Set<Integer>> entry: cellMap.entrySet()) {
            cloned.cellMap.put(entry.getKey(), new HashSet<>(entry.getValue()));
        }

        return cloned;
    }

    public void disableForPaste() {
        this.disableTrackingForPaste = true;
    }

    public void enableTracking() {
        this.disableTrackingForPaste = false;
    }

    public void shiftRange(Table table, DataRange dataRange, boolean isRowAction, boolean isDeleteAction) {
        int tableStartRow = table.isHeaderRowShown() ? table.getStartRowIndex() + 1 : table.getStartRowIndex();
        int tableStartCol = table.getStartColIndex();

        if(isDeleteAction) {
            if(isRowAction) {
                int relativeStartRow = dataRange.getStartRowIndex() - tableStartRow;
                int relativeEndRow = dataRange.getEndRowIndex() - tableStartRow;
                int count = dataRange.getEndRowIndex() - dataRange.getStartRowIndex() + 1;
                List<Integer> sortedRows = this.cellMap.keySet().stream().sorted().collect(Collectors.toList());

                for(int row: sortedRows) {
                    if(row >= relativeStartRow && row <= relativeEndRow) {
                        this.cellMap.remove(row);
                    }
                    else if(row > relativeEndRow) {
                        Set<Integer> cols = this.cellMap.get(row);
                        this.cellMap.remove(row);
                        this.cellMap.put(row-count, cols);
                    }
                }
            }
            else {
                int relativeStartCol = dataRange.getStartColIndex() - tableStartCol;
                int relativeEndCol = dataRange.getEndColIndex() - tableStartCol;
                int count = dataRange.getEndColIndex() - dataRange.getStartColIndex() + 1;
                for(int row: this.cellMap.keySet()) {
                    Set<Integer> cols = this.cellMap.get(row);
                    List<Integer> sortedCols = cols.stream().sorted().collect(Collectors.toList());

                    for(int col: sortedCols) {
                        if(col >= relativeStartCol && col <= relativeEndCol) {
                            cols.remove(col);
                        }
                        else if(col > relativeEndCol){
                            cols.remove(col);
                            cols.add(col-count);
                        }
                    }
                }
            }
        }
        else {
            if(isRowAction) {
                int relativeStartRow = dataRange.getStartRowIndex() - tableStartRow;
                int count = dataRange.getEndRowIndex() - dataRange.getStartRowIndex() + 1;
                List<Integer> sortedRows = this.cellMap.keySet().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());

                for(int row: sortedRows) {
                    if(row >= relativeStartRow) {
                        Set<Integer> cols = this.cellMap.get(row);
                        this.cellMap.put(row+count, cols);
                        this.cellMap.remove(row);
                    }
                    else {
                        break;
                    }
                }
            }
            else {
                int relativeStartCol = dataRange.getStartColIndex() - tableStartCol;
                int count = dataRange.getEndColIndex() - dataRange.getStartColIndex() + 1;
                for(int row: this.cellMap.keySet()) {
                    Set<Integer> cols = this.cellMap.get(row);
                    List<Integer> sortedCols = cols.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());

                    for(int col: sortedCols) {
                        if(col >= relativeStartCol) {
                            cols.remove(col);
                            cols.add(col+count);
                        }
                    }
                }
            }
        }
    }
}
