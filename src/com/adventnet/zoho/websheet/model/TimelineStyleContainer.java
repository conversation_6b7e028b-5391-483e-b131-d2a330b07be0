package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.style.BorderProperties;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.Shadow;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSThemeColor;
import com.adventnet.zoho.websheet.model.style.fill.Fill;
import com.adventnet.zoho.websheet.model.style.fill.GradientFill;

import com.adventnet.zoho.websheet.model.style.fill.PatternFill;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;

import com.adventnet.zoho.websheet.model.util.EngineConstants;

import java.util.*;
import java.util.List;


public class TimelineStyleContainer {

    private static final List< Map<TimeLineSlicer.TimeLineStyle,CellStyle>> SLICER_STYLE_LIST= new ArrayList<>();
    static {
        //   STYLE1
        CellStyle wholeTableStyle = new CellStyle();

        Fill gradient = GradientFill.getLinearInstance(90.0, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.95), ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.89));
        wholeTableStyle.setProperty(CellStyle.Property.FILL, gradient);
        BorderProperties borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.80), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        CellStyle headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, -0.24));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        CellStyle itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0));


        //ITEM OFF STYLE
        CellStyle itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.75));


        //TIME LEVEL STYLE
        CellStyle timeLevelStyle = new CellStyle();
        timeLevelStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //SLIDER_LABEL_STYLE
        CellStyle sliderLabelStyle = new CellStyle();
        sliderLabelStyle.setProperty(TextStyle.Property.COLOR,ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.10));

        //PERIOD_LABEL1_STYLE
        CellStyle periodType1Style = new CellStyle();
        periodType1Style.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //PERIOD_LABEL2_STYLE
        CellStyle periodType2Style = new CellStyle();
        periodType2Style.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, -0.09));

        //ITEM_ON_SPACE_STYLE
        CellStyle itemOnSpaceStyle = new CellStyle();
        itemOnSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.50));

        //ITEM_OFF_SPACE_STYLE
        CellStyle itemOffSpaceStyle = new CellStyle();
        itemOffSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR,ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.50));

        Map<TimeLineSlicer.TimeLineStyle,CellStyle> styleMap = new HashMap<>();
        styleMap.put(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE,wholeTableStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.HEADER_STYLE,headerStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE,itemOnStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE,itemOffStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE,timeLevelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE,sliderLabelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE,periodType1Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE,periodType2Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE,itemOnSpaceStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE,itemOffSpaceStyle);

        SLICER_STYLE_LIST.add(styleMap);


        //   STYLE2
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.90), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.0));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.30));


        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.91));

        //TIME LEVEL STYLE
        timeLevelStyle = new CellStyle();
        timeLevelStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //SLIDER_LABEL_STYLE
        sliderLabelStyle = new CellStyle();
        sliderLabelStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //PERIOD_LABEL1_STYLE
        periodType1Style = new CellStyle();
        periodType1Style.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //PERIOD_LABEL2_STYLE
        periodType2Style = new CellStyle();
        periodType2Style.setProperty(TextStyle.Property.COLOR,  ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //ITEM_ON_SPACE_STYLE
        itemOnSpaceStyle = new CellStyle();
        itemOnSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.82));

        //ITEM_OFF_SPACE_STYLE
        itemOffSpaceStyle = new CellStyle();
        itemOffSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.82));

        styleMap = new HashMap<>();
        styleMap.put(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE,wholeTableStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.HEADER_STYLE,headerStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE,itemOnStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE,itemOffStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE,timeLevelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE,sliderLabelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE,periodType1Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE,periodType2Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE,itemOnSpaceStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE,itemOffSpaceStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE3
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.90), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.0));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0));


        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.80));

        //TIME LEVEL STYLE
        timeLevelStyle = new CellStyle();
        timeLevelStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //SLIDER_LABEL_STYLE
        sliderLabelStyle = new CellStyle();
        sliderLabelStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //PERIOD_LABEL1_STYLE
        periodType1Style = new CellStyle();
        periodType1Style.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //PERIOD_LABEL2_STYLE
        periodType2Style = new CellStyle();
        periodType2Style.setProperty(TextStyle.Property.COLOR,  ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //ITEM_ON_SPACE_STYLE
        itemOnSpaceStyle = new CellStyle();
        itemOnSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.85));

        //ITEM_OFF_SPACE_STYLE
        itemOffSpaceStyle = new CellStyle();
        itemOffSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR,ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.85));

        styleMap = new HashMap<>();
        styleMap.put(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE,wholeTableStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.HEADER_STYLE,headerStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE,itemOnStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE,itemOffStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE,timeLevelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE,sliderLabelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE,periodType1Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE,periodType2Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE,itemOnSpaceStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE,itemOffSpaceStyle);

        SLICER_STYLE_LIST.add(styleMap);


        //   STYLE4

        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.97));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.90), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.0));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0));


        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1, 0));

        //TIME LEVEL STYLE
        timeLevelStyle = new CellStyle();
        timeLevelStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //SLIDER_LABEL_STYLE
        sliderLabelStyle = new CellStyle();
        sliderLabelStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //PERIOD_LABEL1_STYLE
        periodType1Style = new CellStyle();
        periodType1Style.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //PERIOD_LABEL2_STYLE
        periodType2Style = new CellStyle();
        periodType2Style.setProperty(TextStyle.Property.COLOR,  ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //ITEM_ON_SPACE_STYLE
        itemOnSpaceStyle = new CellStyle();
        itemOnSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.88));

        //ITEM_OFF_SPACE_STYLE
        itemOffSpaceStyle = new CellStyle();
        itemOffSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.88));

        styleMap = new HashMap<>();
        styleMap.put(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE,wholeTableStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.HEADER_STYLE,headerStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE,itemOnStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE,itemOffStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE,timeLevelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE,sliderLabelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE,periodType1Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE,periodType2Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE,itemOnSpaceStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE,itemOffSpaceStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE5
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.98));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.90), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.0));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.0));


        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.0));



        //TIME LEVEL STYLE
        timeLevelStyle = new CellStyle();
        timeLevelStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //SLIDER_LABEL_STYLE
        sliderLabelStyle = new CellStyle();
        sliderLabelStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //PERIOD_LABEL1_STYLE
        periodType1Style = new CellStyle();
        periodType1Style.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //PERIOD_LABEL2_STYLE
        periodType2Style = new CellStyle();
        periodType2Style.setProperty(TextStyle.Property.COLOR,  ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

       //ITEM_ON_SPACE_STYLE
        itemOnSpaceStyle = new CellStyle();
        itemOnSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.85));

        //ITEM_OFF_SPACE_STYLE
        itemOffSpaceStyle = new CellStyle();
        itemOffSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.85));

        styleMap = new HashMap<>();
        styleMap.put(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE,wholeTableStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.HEADER_STYLE,headerStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE,itemOnStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE,itemOffStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE,timeLevelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE,sliderLabelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE,periodType1Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE,periodType2Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE,itemOnSpaceStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE,itemOffSpaceStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE6
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1,0));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.20));


        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, -0.15));


        //TIME LEVEL STYLE
        timeLevelStyle = new CellStyle();
        timeLevelStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //SLIDER_LABEL_STYLE
        sliderLabelStyle = new CellStyle();
        sliderLabelStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //PERIOD_LABEL1_STYLE
        periodType1Style = new CellStyle();
        periodType1Style.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //PERIOD_LABEL2_STYLE
        periodType2Style = new CellStyle();
        periodType2Style.setProperty(TextStyle.Property.COLOR,  ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //ITEM_ON_SPACE_STYLE
        itemOnSpaceStyle = new CellStyle();
        itemOnSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.50));

        //ITEM_OFF_SPACE_STYLE
        itemOffSpaceStyle = new CellStyle();
        itemOffSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.50));

        styleMap = new HashMap<>();
        styleMap.put(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE,wholeTableStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.HEADER_STYLE,headerStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE,itemOnStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE,itemOffStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE,timeLevelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE,sliderLabelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE,periodType1Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE,periodType2Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE,itemOnSpaceStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE,itemOffSpaceStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE7
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.21));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.21), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,-0.23));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0));


        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.12));


        //TIME LEVEL STYLE
        timeLevelStyle = new CellStyle();
        timeLevelStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR,  ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //SLIDER_LABEL_STYLE
        sliderLabelStyle = new CellStyle();
        sliderLabelStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //PERIOD_LABEL1_STYLE
        periodType1Style = new CellStyle();
        periodType1Style.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //PERIOD_LABEL2_STYLE
        periodType2Style = new CellStyle();
        periodType2Style.setProperty(TextStyle.Property.COLOR,  ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //ITEM_ON_SPACE_STYLE
        itemOnSpaceStyle = new CellStyle();
        itemOnSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //ITEM_OFF_SPACE_STYLE
        itemOffSpaceStyle = new CellStyle();
        itemOffSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        styleMap = new HashMap<>();
        styleMap.put(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE,wholeTableStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.HEADER_STYLE,headerStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE,itemOnStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE,itemOffStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE,timeLevelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE,sliderLabelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE,periodType1Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE,periodType2Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE,itemOnSpaceStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE,itemOffSpaceStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE8
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0));


        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.90));


        //TIME LEVEL STYLE
        timeLevelStyle = new CellStyle();
        timeLevelStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //SLIDER_LABEL_STYLE
        sliderLabelStyle = new CellStyle();
        sliderLabelStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));

        //PERIOD_LABEL1_STYLE
        periodType1Style = new CellStyle();
        periodType1Style.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //PERIOD_LABEL2_STYLE
        periodType2Style = new CellStyle();
        periodType2Style.setProperty(TextStyle.Property.COLOR,  ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.38));

        //ITEM_ON_SPACE_STYLE
        itemOnSpaceStyle = new CellStyle();
        itemOnSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.85));

        //ITEM_OFF_SPACE_STYLE
        itemOffSpaceStyle = new CellStyle();
        itemOffSpaceStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0.85));

        styleMap = new HashMap<>();
        styleMap.put(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE,wholeTableStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.HEADER_STYLE,headerStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE,itemOnStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE,itemOffStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE,timeLevelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE,sliderLabelStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE,periodType1Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE,periodType2Style);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE,itemOnSpaceStyle);
        styleMap.put(TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE,itemOffSpaceStyle);

        SLICER_STYLE_LIST.add(styleMap);


    }
    private static ZSColor applyTint(Workbook workbook, ZSColorScheme.Colors color , double tint , double darkness){
        String hexString = workbook.getTheme().getColorScheme().getColors().get(color).substring(1);
        String tintAppliedHex = ZSColor.getTintAppliedHex(hexString,tint);
        String darkenedHex = ZSColor.getTintAppliedHex(tintAppliedHex,darkness);
        return ZSColor.getInstance("#".concat(darkenedHex));
    }
    private static CellStyle replaceAccentInStyle(int styleInd, TimeLineSlicer.TimeLineStyle  styleKey, ZSColorScheme.Colors themeColor){
        CellStyle cellStyle = SLICER_STYLE_LIST.get(styleInd).get(styleKey);
        CellStyle newCellStyle = cellStyle.clone();
        Object backgroundObj = newCellStyle.getProperty(CellStyle.Property.BACKGROUNDCOLOR);
        if(backgroundObj != null){
            String tint = ((ZSColor)backgroundObj).getColorTintToWrite();
            String theme = ((ZSColor)backgroundObj).getThemeColorToWrite();
            if(!ZSColorScheme.Colors.BACKGROUND1.toString().equals(theme) && !ZSColorScheme.Colors.TEXT1.toString().equals(theme)){
                newCellStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR,ZSColor.getInstance(themeColor,tint!= null ? Double.parseDouble(tint) : 0.0));
            }
//            if(styleInd == 0 ){
//                if(styleKey == TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE) {
//                    newCellStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, applyTint(workbook, themeColor, tint != null ? Double.parseDouble(tint) : 0.0, -0.05));
//                }
////                if(styleKey == TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE){
////                    newCellStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR,applyTint(workbook,themeColor,tint!= null ? Double.parseDouble(tint) : 0.0,-0.2));
////                }
//                if(styleKey == TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE){
//                    newCellStyle.setProperty(CellStyle.Property.COLOR,applyTint(workbook,themeColor,tint!= null ? Double.parseDouble(tint) : 0.0,-0.5));
//                }
//            }
//            if(styleInd == 2 && styleKey == TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE){
//                newCellStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR,applyTint(workbook,themeColor,tint!= null ? Double.parseDouble(tint) : 0.0,-0.05));
//            }
//            if(styleInd == 3 && styleKey == TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE){
//                newCellStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR,applyTint(workbook,themeColor,tint!= null ? Double.parseDouble(tint) : 0.0,-0.5));
//            }
        }
        Fill fill =  (Fill)cellStyle.getProperty(CellStyle.Property.FILL);
        if(fill != null) {
            if (fill instanceof PatternFill) {
                ZSColor fgCcolor = ((PatternFill) fill).getFgColor();
                ZSColor bgCcolor = ((PatternFill) fill).getBgColor();
                if (fgCcolor instanceof ZSThemeColor) {
                    String tint = fgCcolor.getColorTintToWrite();
                    String theme = fgCcolor.getThemeColorToWrite();
                    if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(theme) && !ZSColorScheme.Colors.TEXT1.toString().equals(theme)) {
                        fgCcolor = ZSColor.getInstance(themeColor, tint != null ? Double.parseDouble(tint) : 0.0);
                    }
                }

                if (bgCcolor instanceof ZSThemeColor) {
                    String tint = bgCcolor.getColorTintToWrite();
                    String theme = bgCcolor.getThemeColorToWrite();
                    if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(theme) && !ZSColorScheme.Colors.TEXT1.toString().equals(theme)) {
                        bgCcolor = ZSColor.getInstance(themeColor, tint != null ? Double.parseDouble(tint) : 0.0);
                    }
                }
                fill = PatternFill.getInstance(((PatternFill) fill).getPatternType(), fgCcolor, bgCcolor);

            } else {
                Map<Double, ZSColor> newMap = new HashMap<>();
                for (Double position : ((GradientFill) fill).getGradientColors().keySet()) {
                    ZSColor color = ((GradientFill) fill).getGradientColors().get(position);
                    if (color instanceof ZSThemeColor) {
                        String tint = color.getColorTintToWrite();
                        String theme = color.getThemeColorToWrite();
                        if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(theme) && !ZSColorScheme.Colors.TEXT1.toString().equals(theme)) {
                            color = ZSColor.getInstance(themeColor, tint != null ? Double.parseDouble(tint) : 0.0);
                        }
                    }
                    newMap.put(position, color);
                }

                fill = GradientFill.getInstance(((GradientFill) fill).getDegree(), ((GradientFill) fill).getGradientType(), ((GradientFill) fill).getLeft(), ((GradientFill) fill).getRight(), ((GradientFill) fill).getTop(), ((GradientFill) fill).getBottom(), newMap);
            }

            newCellStyle.setProperty(CellStyle.Property.FILL, fill);
        }


        Object colorObj = newCellStyle.getProperty(TextStyle.Property.COLOR);

        if(colorObj != null){
            String tint = ((ZSColor)colorObj).getColorTintToWrite();
            String theme = ((ZSColor)colorObj).getThemeColorToWrite();
            if(theme != null && !ZSColorScheme.Colors.BACKGROUND1.toString().equals(theme) && !ZSColorScheme.Colors.TEXT1.toString().equals(theme)){
                newCellStyle.setProperty(TextStyle.Property.COLOR,ZSColor.getInstance(themeColor,tint!= null ? Double.parseDouble(tint) : 0.0));
            }
        }
        BorderProperties props = (BorderProperties) CellStyle.getProperty(newCellStyle.getCellStyleProperties(),CellStyle.Property.BORDERLEFT);
        if(props != null) {
            ZSColor color = props.getColor();
            if (color != null) {
                if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(color.getThemeColorToWrite()) && !ZSColorScheme.Colors.TEXT1.toString().equals(color.getThemeColorToWrite())) {
                    ZSColor bgColor = ZSColor.getInstance(themeColor,color.getColorTintToWrite() != null ? Double.parseDouble( color.getColorTintToWrite()) : 0.0);
                    newCellStyle.setProperty(CellStyle.Property.BORDERLEFT, BorderProperties.getInstance(props.getType(),bgColor,props.getSize()));
//                    if((styleInd == 0 || styleInd == 7) && styleKey.equals(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE)){
//                        newCellStyle.setProperty(CellStyle.Property.BORDERLEFT, BorderProperties.getInstance(props.getType(),applyTint(workbook,themeColor,Double.parseDouble(color.getColorTintToWrite()),-0.05),props.getSize()));
//                    }
                }
            }
        }
        props = (BorderProperties) CellStyle.getProperty(newCellStyle.getCellStyleProperties(),CellStyle.Property.BORDERRIGHT);
        if(props != null) {
            ZSColor color = props.getColor();
            if (color != null) {
                if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(color.getThemeColorToWrite()) && !ZSColorScheme.Colors.TEXT1.toString().equals(color.getThemeColorToWrite())) {
                    ZSColor bgColor = ZSColor.getInstance(themeColor,color.getColorTintToWrite() != null ? Double.parseDouble( color.getColorTintToWrite()) : 0.0);
                    newCellStyle.setProperty(CellStyle.Property.BORDERRIGHT, BorderProperties.getInstance(props.getType(),bgColor,props.getSize()));
//                    if((styleInd == 0 || styleInd == 7) && styleKey.equals(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE)){
//                        newCellStyle.setProperty(CellStyle.Property.BORDERRIGHT, BorderProperties.getInstance(props.getType(),applyTint(workbook,themeColor,Double.parseDouble(color.getColorTintToWrite()),-0.05),props.getSize()));
//                    }
                }
            }
        }
        props = (BorderProperties) CellStyle.getProperty(newCellStyle.getCellStyleProperties(),CellStyle.Property.BORDERTOP);
        if(props != null) {
            ZSColor color = props.getColor();
            if (color != null) {
                if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(color.getThemeColorToWrite()) && !ZSColorScheme.Colors.TEXT1.toString().equals(color.getThemeColorToWrite())) {
                    ZSColor bgColor = ZSColor.getInstance(themeColor,color.getColorTintToWrite() != null ? Double.parseDouble( color.getColorTintToWrite()) : 0.0);
                    newCellStyle.setProperty(CellStyle.Property.BORDERTOP, BorderProperties.getInstance(props.getType(),bgColor,props.getSize()));
//                    if((styleInd == 0 || styleInd == 7) && styleKey.equals(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE)){
//                        newCellStyle.setProperty(CellStyle.Property.BORDERTOP, BorderProperties.getInstance(props.getType(),applyTint(workbook,themeColor,Double.parseDouble(color.getColorTintToWrite()),-0.05),props.getSize()));
//                    }
                }
            }
        }
        props = (BorderProperties) CellStyle.getProperty(newCellStyle.getCellStyleProperties(),CellStyle.Property.BORDERBOTTOM);
        if(props != null) {
            ZSColor color = props.getColor();
            if (color != null) {
                if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(color.getThemeColorToWrite()) && !ZSColorScheme.Colors.TEXT1.toString().equals(color.getThemeColorToWrite())) {
                    ZSColor bgColor = ZSColor.getInstance(themeColor,color.getColorTintToWrite() != null ? Double.parseDouble( color.getColorTintToWrite()) : 0.0);
                    newCellStyle.setProperty(CellStyle.Property.BORDERBOTTOM, BorderProperties.getInstance(props.getType(),bgColor,props.getSize()));
//                    if((styleInd == 0 || styleInd == 7)  && styleKey.equals(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE)){
//                        newCellStyle.setProperty(CellStyle.Property.BORDERBOTTOM, BorderProperties.getInstance(props.getType(),applyTint(workbook,themeColor,Double.parseDouble(color.getColorTintToWrite()),-0.05),props.getSize()));
//                    }
                }
            }
        }
        return newCellStyle;
    }
    public static CellStyle getSlicerStyle(int styleInd, int accent, TimeLineSlicer.TimeLineStyle styleKey){
        ZSColorScheme.Colors themeColor = ZSColorScheme.Colors.ACCENT1;
        switch(accent)
        {
            case 2:
                themeColor = ZSColorScheme.Colors.ACCENT2;
                break;
            case 3:
                themeColor = ZSColorScheme.Colors.ACCENT3;
                break;
            case 4:
                themeColor = ZSColorScheme.Colors.ACCENT4;
                break;
            case 5:
                themeColor = ZSColorScheme.Colors.ACCENT5;
                break;
            case 6:
                themeColor = ZSColorScheme.Colors.ACCENT6;
                break;
        }
        return  replaceAccentInStyle(styleInd,styleKey,themeColor );

    }

    public static Shadow getSlicerShadow(int styleIndex, ZSColorScheme.Colors accent)
    {
        switch(styleIndex)
        {
            case 0:
                Shadow shadow = new Shadow(ZSColor.getInstance(accent, 0));
                shadow.setTransparency(90);
                shadow.setBlur(1);
                shadow.setAngle(72);
                shadow.setDistance(3.16);
                return shadow;
            case 1:
                shadow = new Shadow(ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0));
                shadow.setTransparency(90);
                shadow.setBlur(3);
                shadow.setAngle(45);
                shadow.setDistance(1.41);
                return shadow;
            case 7:
                shadow = new Shadow(ZSColor.getInstance(accent,0.82));
                shadow.setTransparency(0);
                shadow.setBlur(0);
                shadow.setInner(true);
                shadow.setSpread(6);
                return shadow;
            default:
                return null;
        }
    }

}
