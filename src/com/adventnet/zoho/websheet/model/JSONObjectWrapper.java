package com.adventnet.zoho.websheet.model;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Field;
import java.util.*;

/**
 * JSONObjectWrapper is a utility class designed to simplify interactions with the org.json.JSONObject class.
 * <AUTHOR>
 */
public class JSONObjectWrapper implements JSO<PERSON><PERSON>rapper{

    public static final Object NULL = JSONObject.NULL;

    private final JSONObject jsonObject;

    /**
     * Creates an empty JSON object.
     */
    public JSONObjectWrapper() {
        this.jsonObject = new JSONObject();
    }

    /**
     * @param source the json object string to create J<PERSON>NObject
     * @throws J<PERSON><PERSON><PERSON>rapperException throws JSO<PERSON><PERSON>rapperException if the string is not valid JSON
     */
    public JSONObjectWrapper(String source) throws JSONWrapperException{
        try {
            this.jsonObject = new JSONObject(source);
        } catch (JSONException e){
            throw new JSONWrapperException(e);
        }
    }

    /**
     * Wraps an existing JSONObject instance.
     * @param jsonObject Create an instance with jsonObject
     */
    public JSONObjectWrapper(JSONObject jsonObject) {
        this.jsonObject = jsonObject;
    }

    /**
     * Creates a wrapper from another JSONObjectWrapper.
     * @param jsonObject The existing JSONObjectWrapper instance to copy from.
     */
    public JSONObjectWrapper(JSONObjectWrapper jsonObject) {
        this.jsonObject = jsonObject.getJsonObject();
    }

    /**
     * Create a JSONObjectWrapper from a Java bean.
     * @param bean The Java Bean to convert into a JSONObjectWrapper.
     * @throws JSONWrapperException If the bean cannot be converted into a JSON object.
     */
    public JSONObjectWrapper(Object bean) throws JSONWrapperException {
        if(bean instanceof JSONObjectWrapper){
            this.jsonObject = ((JSONObjectWrapper)bean).getJsonObject();
        } else if(bean instanceof JSONObject){
            this.jsonObject = (JSONObject) bean;
        } else if(bean instanceof Map<?,?>) {
            bean = getProperObjectFromMap((Map<?, ?>) bean);
            this.jsonObject = new JSONObject((Map<?, ?>) bean);
        } else if (bean instanceof String) {
            this.jsonObject = new JSONObject((String) bean);
        } else {
            this.jsonObject = new JSONObject(bean);
        }
    }

    public JSONObjectWrapper set(String key, Object value) throws JSONWrapperException {
        return this.put(key, value);
    }

    /**
     * Adds a key-value pair to the JSONObjectWrapper.
     *
     * @param key The key to be added or updated in the JSONObject. Must not be null.
     * @param value The value associated with the key. Can be any object supported by JSONObject.
     *              If the value is a complex object, it will be converted to a JSON-compatible format.
     * @return The current instance of JSONObjectWrapper.
     * @throws JSONWrapperException If the value cannot be converted to a JSON-compatible format or
     *                              if the key is null.
     */
    public JSONObjectWrapper put(String key, Object value) throws JSONWrapperException{
        value = this.getAsJSONForPut(value);
        this.jsonObject.put(key, value);
        return this;
    }

    /**
     * Adds a value into the JSONObject without parsing.
     * @param key The key to be added or updated in the JSONObject. It can't be null.
     * @param value The value associated with the key. Must be only string
     * @return The current instance of JSONObjectWrapper
     * @throws JSONWrapperException Rethrows the captured JSONException from the underlying JSON library
     */
    public JSONObjectWrapper putString(String key, String value) throws JSONWrapperException{
        try {
            this.jsonObject.put(key, value);
            return this;
        } catch (JSONException e){
            throw new JSONWrapperException(e);
        }
    }

    /**
     * Puts a key-value pair in the JSON object where the value is an integer.
     *
     * @param key The key to be added or updated in the JSONObject. Must not be null.
     * @param value The integer value associated with the key.
     * @return The current instance of JSONObjectWrapper.
     * @throws JSONWrapperException If the value cannot be converted to a JSON-compatible format or
     *                              if the key is null.
     */
    public JSONObjectWrapper put(String key, int value) throws JSONWrapperException{
        this.jsonObject.put(key, value);
        return this;
    }

    /**
     * Puts a key-value pair in the JSON object only if the key and value is not null.
     *
     * @param key   the key to set.
     * @param value the value to associate with the key.
     * @return the current JSONObjectWrapper instance.
     * @throws JSONWrapperException if the value cannot be converted to a JSON-compatible type.
     */
    public JSONObjectWrapper putOpt(String key, Object value) throws JSONWrapperException {
        value = this.getAsJSONForPut(value);
        this.jsonObject.putOpt(key, value);
        return this;
    }

    /**
     * Retrieves the value associated with a key.
     *
     * @param key the key to look up.
     * @return the value associated with the key.
     * @throws JSONWrapperException if the key is not found.
     */
    public Object get(String key) throws JSONWrapperException{
        Object result = getProperObject(key);
        if(result == null){
            throw new JSONWrapperException("JSONOBJECY["+ key +"] is not found"); //No I18N
        }
        return result;
    }

    /**
     * Checks if the JSON object has a non-null value for the given key.
     *
     * @param key the key to check.
     * @return true if the key is present and not null; false otherwise.
     */
    public boolean has(String key) {
        Object result = this.jsonObject.opt(key);
        return Objects.nonNull(result) && !result.equals(NULL);
    }

    /**
     * Removes a key-value pair from the JSON object.
     *
     * @param key the key to remove.
     * @return the previous value associated with the key, or null if the key was not present.
     */
    public Object remove(String key) {
        return this.jsonObject.remove(key);
    }

    /**
     * Retrieves the integer value associated with a key.
     *
     * @param key the key to look up.
     * @return the integer value associated with the key.
     * @throws JSONWrapperException if the value cannot be converted to an integer.
     */
    public int getInt(String key) throws JSONWrapperException{
        try {
            Object o = this.jsonObject.get(key);
            return o instanceof Number ? ((Number)o).intValue() : (int)this.getDouble(key);
        } catch (Exception e){
            throw new JSONWrapperException(e);
        }

    }

    /**
     * Retrieves the boolean value associated with the given key.
     *
     * @param key the key whose associated boolean value is to be returned
     * @return the boolean value associated with the key
     * @throws JSONWrapperException If the key does not exist or the value is not a boolean
     */
    public boolean getBoolean(String key) throws JSONWrapperException{
        Object o = this.get(key);
        if (!o.equals(Boolean.FALSE) && (!(o instanceof String) || !((String)o).equalsIgnoreCase("false"))) {  //NO I18N
            if (!o.equals(Boolean.TRUE) && (!(o instanceof String) || !((String)o).equalsIgnoreCase("true"))) {//NO I18N
                throw new JSONWrapperException("JSONObject["+key+"] is not a Boolean.");//NO I18N
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    /**
     * Retrieves the string value associated with the given key.
     *
     * @param key the key whose associated string value is to be returned
     * @return the string value associated with the key
     * @throws JSONWrapperException If the key does not exist or the value is not a string
     */
    public String getString(String key) throws JSONWrapperException{
        try {
            return this.jsonObject.get(key).toString();
        } catch (Exception e){
            throw new JSONWrapperException(e);
        }
    }

    /**
     * Retrieves the double value associated with the given key.
     *
     * @param key the key whose associated double value is to be returned
     * @return the double value associated with the key
     * @throws JSONWrapperException If the key does not exist or the value is not a double
     */
    public double getDouble(String key) throws JSONWrapperException{
        Object o = this.jsonObject.get(key);
        try {
            return o instanceof Number ? ((Number)o).doubleValue() : Double.parseDouble((String)o);
        } catch (Exception e){
            throw new JSONWrapperException(e);
        }
    }

    /**
     * Retrieves the long value associated with the given key.
     *
     * @param key the key whose associated long value is to be returned
     * @return the long value associated with the key
     * @throws JSONWrapperException If the key does not exist or the value is not a long
     */
    public long getLong(String key) throws JSONWrapperException{
        try {
            Object o = this.jsonObject.get(key);
            return o instanceof Number ? ((Number)o).longValue() : (long)this.getDouble(key);
        } catch (Exception e){
            throw new JSONWrapperException(e);
        }
    }

    /**
     * Retrieves the JSONObjectWrapper associated with the given key.
     *
     * @param key the key whose associated JSONObjectWrapper is to be returned
     * @return the JSONObjectWrapper associated with the key
     * @throws JSONWrapperException If the key does not exist or the value is not a JSONObjectWrapper
     */
    public JSONObjectWrapper getJSONObject(String key) throws JSONWrapperException {
        Object obj = getProperObject(key);
        if(obj instanceof JSONObjectWrapper) {
            return (JSONObjectWrapper) obj;
        }
        throw new JSONWrapperException("THE VALUE [" + key + "] NOT A JSONOBJECT");//No I18N
    }

    /**
     * Retrieves the JSONArrayWrapper associated with the given key.
     *
     * @param key the key whose associated JSONArrayWrapper is to be returned
     * @return the JSONArrayWrapper associated with the key
     * @throws JSONWrapperException If the key does not exist or the value is not a JSONArrayWrapper
     */
    public JSONArrayWrapper getJSONArray(String key) throws JSONWrapperException {
        Object obj = getProperObject(key);
        if(obj instanceof JSONArrayWrapper) {
            return (JSONArrayWrapper) obj;
        }
        throw new JSONWrapperException("THE VALUE [" + key + "] NOT A JSONARRAY");//No I18N
    }

    /**
     * Retrieves the object associated with the given key, or null if the key does not exist.
     *
     * @param key the key whose associated object is to be returned
     * @return the object associated with the key, or null if not found
     */
    public Object opt(String key) {
        return getProperObject(key);
    }

    /**
     * Retrieves the integer value associated with the given key, or 0 if the key does not exist.
     *
     * @param key the key whose associated integer to be returned
     * @return the integer associated with the key, or 0 if not found
     */
    public int optInt(String key) {
        return this.optInt(key, 0);
    }

    /**
     * Retrieves the integer value associated with the given key, or a default value if not found.
     *
     * @param key the key whose associated integer value is to be returned
     * @param defaultValue the default value to return if the key does not exist
     * @return the integer value associated with the key, or the default value
     */
    public int optInt(String key, int defaultValue) {
        return this.jsonObject.optInt(key, defaultValue);
    }

    /**
     * Retrieves the long value associated with the given key, or 0L if the key does not exist.
     *
     * @param key the key whose associated long to be returned
     * @return the long associated with the key, or 0L if not found
     */
    public long optLong(String key) {
        return this.optLong(key, 0L);
    }

    /**
     * Retrieves the long value associated with the given key, or a default value if not found.
     *
     * @param key the key whose associated long to be returned
     * @param defaultValue the default value to return if the key does not exist
     * @return the long associated with the key, or default value if not found
     */
    public long optLong(String key, long defaultValue) {
        return this.jsonObject.optLong(key, defaultValue);
    }

    /**
     * Retrieves the string value associated with the given key, or empty string if not found.
     *
     * @param key the key whose associated string value is to be returned
     * @return the string value associated with the key, or empty string
     */
    public String optString(String key) {
        return this.optString(key, "");
    }

    /**
     * Retrieves the string value associated with the given key, or a default value if not found.
     *
     * @param key the key whose associated string value is to be returned
     * @param defaultValue the default value to return if the key does not exist
     * @return the string value associated with the key, or the default value
     */
    public String optString(String key, String defaultValue) {
        return this.jsonObject.optString(key, defaultValue);
    }

    /**
     * Retrieves the boolean value associated with the given key, or false if not found.
     *
     * @param key the key whose associated boolean value is to be returned
     * @return the boolean value associated with the key, or false value
     */
    public boolean optBoolean(String key) {
        return this.optBoolean(key, false);
    }

    /**
     * Retrieves the boolean value associated with the given key, or a default value if not found.
     *
     * @param key the key whose associated boolean value is to be returned
     * @param defaultValue the default value to return if the key does not exist
     * @return the boolean value associated with the key, or the default value
     */
    public boolean optBoolean(String key, boolean defaultValue) {
        return this.jsonObject.optBoolean(key, defaultValue);
    }

    /**
     * Retrieves the double value associated with the given key, or Double.NaN value if not found.
     *
     * @param key the key whose associated double value is to be returned
     * @return the double value associated with the key, or Double.NaN value
     */
    public double optDouble(String key) {
        return this.optDouble(key, Double.NaN);
    }

    /**
     * Retrieves the double value associated with the given key, or a default value if not found.
     *
     * @param key the key whose associated double value is to be returned
     * @param defaultValue the default value to return if the key does not exist
     * @return the double value associated with the key, or the default value
     */
    public double optDouble(String key, double defaultValue) {
        return this.jsonObject.optDouble(key, defaultValue);
    }

    /**
     * Retrieves the JSONArrayWrapper associated with the given key, or a null if not found.
     *
     * @param key the key whose associated JSONArrayWrapper is to be returned
     * @return the JSONArrayWrapper associated with the key, or null value
     */
    public JSONArrayWrapper optJSONArray(String key) {
        return this.optJSONArray(key, null);
    }

    /**
     * Retrieves the JSONArrayWrapper associated with the given key, or a default value if not found.
     *
     * @param key the key whose associated JSONArrayWrapper is to be returned
     * @param defaultValue the default value to return if the key does not exist
     * @return the JSONArrayWrapper associated with the key, or the default value
     */
    public JSONArrayWrapper optJSONArray(String key, JSONArrayWrapper defaultValue) {
        Object result = this.getProperObject(key);
        if(result instanceof JSONArrayWrapper) {
            return (JSONArrayWrapper) result;
        }
        return defaultValue;
    }

    /**
     * Retrieves the JSONObjectWrapper associated with the given key, or a null if not found.
     *
     * @param key the key whose associated JSONObjectWrapper is to be returned
     * @return the JSONObjectWrapper associated with the key, or null
     */
    public JSONObjectWrapper optJSONObject(String key) {
        return this.optJSONObject(key, null);
    }

    /**
     * Retrieves the JSONObjectWrapper associated with the given key, or a default value if not found.
     *
     * @param key the key whose associated JSONObjectWrapper is to be returned
     * @param defaultValue the default value to return if the key does not exist
     * @return the JSONObjectWrapper associated with the key, or the default value
     */
    public JSONObjectWrapper optJSONObject(String key, JSONObjectWrapper defaultValue) {
        Object result = this.getProperObject(key);
        if(result instanceof JSONObjectWrapper) {
            return (JSONObjectWrapper) result;
        }
        return defaultValue;
    }

    /**
     * Returns the underlying JSONObject.
     * @return the org.Json.JSONObject in the JSONObjectWrapper
     */
    public JSONObject getJsonObject() {
        return this.jsonObject;
    }

    /**
     * Returns an iterator over the keys of the underlying JSONObject.
     *
     * @return an iterator over the keys of the JSONObject
     */
    public Iterator<String> keys() {
        return this.jsonObject.keys();
    }

    /**
     * Returns a set of the keys of the underlying JSONObject.
     *
     * @return a set of keys in the JSONObject
     */
    public Set<String> keySet() {
        return this.jsonObject.keySet();
    }

    /**
     * Returns the number of key-value pairs in the underlying JSONObject.
     *
     * @return the number of key-value pairs in the JSONObject
     */
    @Override
    public int length() {
        return this.jsonObject.length();
    }

    /**
     * Clears the underlying JSONObject, removing all key-value pairs.
     */
    @Override
    public void clear() {
        this.jsonObject.clear();
    }

    /**
     * Checks if the underlying JSONObject is empty.
     *
     * @return true if the JSONObject has no key-value pairs, false otherwise
     */
    @Override
    public boolean isEmpty() {
        return this.jsonObject.isEmpty();
    }

    /**
     * Checks if the JSONObjectWrapper represents an array.
     *
     * @return false, as this class represents a JSONObject, not a JSONArray
     */
    @Override
    public boolean isArray() {
        return false;
    }

    /**
     * Returns a JSONArrayWrapper of the names of the keys in the underlying JSONObject.
     *
     * @return a JSONArrayWrapper of the keys in the JSONObject
     */
    public JSONArrayWrapper names() {
        return new JSONArrayWrapper(this.jsonObject.names());
    }

    /**
     * Converts a Java bean into a JSONObjectWrapper.
     *
     * @param bean the Java bean to convert
     * @return a JSONObjectWrapper representing the bean
     */
    public static JSONObjectWrapper toJSONObject(Object bean) {
        if (bean == null) {
            return null;
        }

        JSONObjectWrapper jsonObject = new JSONObjectWrapper();
        java.lang.reflect.Field[] fields = bean.getClass().getDeclaredFields();

        for (Field field : fields) {
            try {
                // Make the private fields accessible
                field.setAccessible(true);

                String fieldName = field.getName();
                Object value = field.get(bean);

                if (value != null) {
                    if (isPrimitiveOrWrapper(value.getClass()) || value instanceof String) {
                        jsonObject.put(fieldName, value);
                    } else if (value instanceof Collection) {
                        jsonObject.put(fieldName, new JSONArrayWrapper((Collection<?>) value));
                    } else if (value instanceof Map) {
                        jsonObject.put(fieldName, new JSONObjectWrapper((Map<?, ?>) value));
                    } else if(value instanceof JSONObjectWrapper || value instanceof JSONArrayWrapper) {
                        jsonObject.put(fieldName, value);
                    } else {
                        jsonObject.put(fieldName, toJSONObject(value));
                    }
                }

            } catch (Exception e) {
                throw new JSONException(e);
            }
        }
        return jsonObject;
    }

    /**
     * Checks if a class is a primitive type or its wrapper class.
     *
     * @param clazz the class to check
     * @return true if the class is a primitive or a wrapper class, false otherwise
     */
    private static boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() || clazz == Boolean.class || clazz == Integer.class || clazz == Character.class ||
                clazz == Byte.class || clazz == Short.class || clazz == Double.class || clazz == Long.class || clazz == Float.class;
    }

    /**
     * Returns the string representation of the underlying JSONObject.
     *
     * @return the string representation of the JSONObject
     */
    public String toString(){
        return this.jsonObject.toString();
    }

    /**
     * Creates a JSONObjectWrapper from a JSON string.
     *
     * @param jsonStr the JSON string to parse
     * @return a JSONObjectWrapper representing the JSON string
     * @throws JSONWrapperException If the string cannot be parsed into a valid JSONObject
     */
    public static JSONObjectWrapper fromString(String jsonStr) throws JSONWrapperException {
        if(jsonStr == null || jsonStr.isEmpty()){
            return null;
        }
        return new JSONObjectWrapper(jsonStr);
    }

    /**
     * Creates a JSONObjectWrapper from a Java object.
     *
     * @param object the object to convert into a JSONObjectWrapper
     * @return a JSONObjectWrapper representing the object
     * @throws JSONWrapperException If the object cannot be converted into a valid JSONObject
     */
    public static JSONObjectWrapper fromObject(Object object) throws JSONWrapperException{
        if(object == null){
            return null;
        }
        return new JSONObjectWrapper(object);
    }

    /**
     * Retrieves the proper object associated with the given key from the JSONObject.
     * This method checks if the value associated with the key is a string, collection, map, or other types,
     * and converts it to a corresponding JSONWrapper (JSONArrayWrapper or JSONObjectWrapper) if necessary.
     *
     * @param key the key whose associated value is to be retrieved and processed
     * @return the processed object, which could be a regular object or a wrapped JSON object/array (JSONArrayWrapper or JSONObjectWrapper)
     *         or null if the value does not exist or is invalid
     */
    private Object getProperObject(String key) {
        Object obj = getAsJSONWrapper(this.jsonObject.opt(key));
        if(obj == null){
            return null;
        }
        try {
            if(obj instanceof String) {
                if(((String)obj).startsWith("[") && ((String)obj).endsWith("]") && isValidJsonString((String) obj)) { //No I18N
                    return new JSONArrayWrapper((String) obj);
                }
                if(((String)obj).startsWith("{") && ((String)obj).endsWith("}") && isValidJsonString((String) obj)) { //No I18N
                    return new JSONObjectWrapper((String) obj);
                }
                return obj;
            } else if(obj instanceof Collection<?>) {
                obj = getProperObjectFromList((Collection<?>) obj);
                obj = new JSONArrayWrapper((Collection<?>) obj);
            } else if(obj instanceof Map<?,?>) {
                obj = getProperObjectFromMap((Map<?, ?>) obj);
                obj = new JSONObjectWrapper(obj);
            } else {
                return obj;
            }
            this.jsonObject.put(key, obj);
            return obj;
        } catch (Exception e) {
            return obj;
        }
    }
}





