// $Id$
/*
 * Link.java
 *
 * Created on May 14, 2007, 6:45 PM
 */

package com.adventnet.zoho.websheet.model;

import java.util.Objects;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class RichStringProperties
{
    public static final Logger LOGGER = Logger.getLogger(RichStringProperties.class.getName());

    private String styleName;
    private final String url;
    private final String label;
    private final int startIndex;
    private final int endIndex;
    private final Field field;
    private final Icon icon;

    public RichStringProperties(String inStyleName, String inURL, String inLabel, int inStartIndex)
    {
        this(inStyleName, inURL, inLabel == null ? "" : inLabel, inStartIndex, null);
    }

    public RichStringProperties(String inStyleName, String inURL, String inLabel, int inStartIndex, Field field)
    {
        this.styleName = inStyleName;
        this.url = inURL;
        this.label = inLabel == null ? "" : inLabel;
        this.startIndex = inStartIndex;
        this.endIndex = inStartIndex + Math.max(0, this.label.length() - 1);
        this.field = field;
        this.icon = null;
    }

    public RichStringProperties(String inStyleName, String inURL, int inStartIndex, Field field, Icon icon)
    {
        this(inStyleName, inURL, "", inStartIndex, field);
    }

    @Override
    public String toString()
    {
        return "StyleName : "+this.getStyleName()+" ;  URL : " + this.getUrl() + " ; Label : " + this.getLabel() +  " ; Icon : " + (this.getIcon() != null ? this.getIcon().getName() : null) + " ; startIndex : " + this.getStartIndex() + " ; endIndex: " + this.getEndIndex(); //No I18N
    }

    public String getUrl()
    {
	return url;
    }

    public String getLabel()
    {
	return label;
    }

    public int getStartIndex()
    {
	return startIndex;
    }

    public int getEndIndex()
    {
	return endIndex;
    }  
    
    public String getStyleName()
    {
        return styleName;
    }

    public Field getField()
    {
        return this.field;
    }

    public Icon getIcon() {
        return this.icon;
    }

    @Override
    public boolean equals(Object o)
    {
        if (!(o instanceof RichStringProperties)) {
            return false;
        }
        if(this == o) {
            return true;
        }
        RichStringProperties that = (RichStringProperties) o;
        return this.startIndex == that.startIndex && this.endIndex == that.endIndex &&
                Objects.equals(this.styleName, that.styleName) && Objects.equals(this.url, that.url)
                && Objects.equals(this.label, that.label) && Objects.equals(this.field, that.field) && Objects.equals(this.icon, that.icon);
    }

    @Override
    public int hashCode() {
        return Objects.hash(styleName, url, label, startIndex, endIndex, field, icon);
    }

    public void changeStyleNameFromSave(String styleName)
    {
        this.styleName = styleName;
    }
}
