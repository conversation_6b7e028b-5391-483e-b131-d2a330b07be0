//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model;

import com.adventnet.iam.IAMUtil;
import com.adventnet.wms.api.CollaborationApi;
import com.adventnet.wms.api.WmsApi;
import com.adventnet.wms.common.WmsService;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.chart.ChartUtils;
import com.zoho.sheet.conversion.Importer;
import com.zoho.sheet.conversion.ImporterFactory;
import com.zoho.sheet.knitcharts.constants.ChartActionConstants;
import com.zoho.sheet.knitcharts.utils.PublicChartUtils;
import com.zoho.sheet.util.CurrentRealm;
import com.zoho.sheet.util.LocaleMsg;
import com.zoho.zfsng.client.ZohoFS;

import javax.servlet.http.HttpServletRequest;
import java.util.Hashtable;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;
public class DocumentActionManager
{
    public static Logger logger = Logger.getLogger(DocumentActionManager.class.getName());
    public static String doAction(int action, WorkbookContainer container, String containerIdentity, AccessType accessType, String loginName, HttpServletRequest request, JSONObjectWrapper actionObject) throws Exception
    {
//            JSONObject responseJson = new JSONObject();
        String response = null;
        String doNotParse = request.getParameter("parseWorkbook");
        Workbook workbook = null;
        if (doNotParse == null || !"false".equals(doNotParse)) {
        		workbook = container.getWorkbook(null);
		}
        
        //In internal server calls, the document might be in closed state; so we are setting workbook here
         
        switch (action) {
            case ActionConstants.SEND_WMS_NOTIFICATION:
                try{
                    String msgObj = request.getParameter("wmsResponse");
                    String collabID = container.getCollabId();
                    logger.log(Level.INFO, ":: SendWMSCollabMessageAction :: collabId: {0} :: loginName : {1} :: msgObj : {2} ::", new Object[]{collabID, loginName, msgObj});//NO I18N
                    MessagePropagator.sendMessage(collabID, loginName, msgObj);
                    response = "true";
                }catch (Exception e){
                    logger.log(Level.WARNING, ":: SendWMSCollabMessageAction :: Failed", e);//NO I18N
                    response = "false";
                }
                break;
            case ActionConstants.RENAME_DOCUMENT:
                String newDocName = request.getParameter("newName");
                try {
                    
                    ZohoFS.renameResource(container.getDocsSpaceId(), container.getResourceId(), newDocName, String.valueOf(IAMUtil.getCurrentUser()!=null?IAMUtil.getCurrentUser().getZUID():DocumentUtils.getZUID(loginName)));
                    //sending wms notification to docs while renamning
                    Hashtable<String, String> msgobj = new Hashtable<String, String>();
                    msgobj.put("resourceId", container.getResourceId());
                    msgobj.put("action", "RENAME_DOCUMENT");// No I18N
                    msgobj.put("newDocumentName", newDocName);// No I18N
                    WmsApi.sendCrossProductMessage(container.getDocOwnerZUID(), msgobj, WmsService.EXPLORER);
                    //Sending the message to our client :- it is required for new client as we are handling at one place collabimpl.handlecrossproductmessage
                    WmsApi.sendCrossProductMessage(container.getDocOwnerZUID(), msgobj, WmsService.SHEET);
                    container.rename(newDocName);
                    response = "true";
                }
                catch (Exception e) {
                    logger.log(Level.WARNING, null, e);
                    response = "false";
                    break;
                }
                try {
                    MessagePropagator.sendSaveDocumentDetails(container, loginName, newDocName, request);
                }
                catch (Exception e) {
                    logger.log(Level.WARNING, "Problem while sending the Document name during RENAME action via WMS.");
                }
                break;
            case ActionConstants.DOCUMENT_TRASH:
                DocumentUtils.moveDocumentToTrash(container, loginName, accessType);
                //This block is used to send message to all collaborators when document is trashed
                String collabId = container.getCollabId();
                if (collabId != null) {
                    Hashtable collabUserTable = CollaborationApi.getSessions(collabId);
                    if (!collabUserTable.isEmpty()) {
                        JSONObjectWrapper respObj = new JSONObjectWrapper();
                        respObj.put(JSONConstants.ACTION, ActionConstants.DOCUMENT_TRASH); //NO I18N
                        respObj.put(JSONConstants.DIRECT_UPDATE, true); //NO I18N
                        MessagePropagator.sendMessage(collabId, String.valueOf(collabUserTable.keys().nextElement()), respObj);
                    }
                }
                break;
            case ActionConstants.DOCUMENT_DELETE:
                DocumentUtils.purgeDocument(container, containerIdentity, accessType);
                break;
//            case ActionConstants.DOCUMENT_RESTORE:
//ZUID_Persistence_Changes 
//            case ActionConstants.DOCUMENT_RESTORE_RETENTION:
//                response = DocumentUtils.restoreDocument(container, loginName);
//                break;
//            case ActionConstants.CHANGE_OWNER:
//                String docName = request.getParameter("docName");
//                String toUser = request.getParameter("toUser");
//                String zoid = request.getParameter("zoid");
//                String retainAccess = request.getParameter("retainAccess");
//                response = DocumentUtils.changeOwner(container, null, docName, toUser, zoid, "", "true".equals(retainAccess) ? true : false, accessType);
//                if(response != null){
//                    EngineFactory.removeWorkbookContainer(CurrentRealm.getContainerIdentity(), CurrentRealm.getAccessIdentity());
//                }
//                break;
            case ActionConstants.CREATE_VERSION:
                String label = request.getParameter("versionLabel");
                Boolean success = container.save(label, loginName, true);
                response = (success == true) ? "true" : "false";
                break;
            case ActionConstants.REVERT_VERSION:
                response = "true";
                String zfsngVersionNo = request.getParameter("zfsngVersionNo");
                try {
                    container.save(null, loginName, true);
//                    EngineUtils1.revertToVersion(container, zfsngVersionNo);
                    container.revertToVersion(zfsngVersionNo);
                }
                catch (Exception e) {
                    logger.log(Level.WARNING, null, e);
                    response = "false";
                }
                break;
            case ActionConstants.CHECKIN_CHECKOUT:
                response = "true";
                zfsngVersionNo = request.getParameter("zfsngVersionNo");
                String userName = DocumentUtils.getZUserName(request.getParameter("zuid"));

                try {
//                                            this check is added for document close case.In this case we need to create workbook
//                    if (!container.hasWorkbook(null)) {
//                        container.getWorkbook(null);
//                    }
                    if (zfsngVersionNo != null) {
                        container.setIsMinorVersion(true);
                        String versionLabel = LocaleMsg.getMsg("VersionLabel.BeforeDiscardCheckout");        //No I18N
                        container.save(versionLabel, userName, true);
                        container.setIsMinorVersion(false);
//                        EngineUtils1.revertToVersion(container, zfsngVersionNo);
                        container.revertToVersion(zfsngVersionNo);
//                                                  EngineUtils1.versionDocument(container, null, versionLabel, loginName);
                        container.save("auto-version", userName, true); //NO I18N
                    }
                    else {
                        String lastActionPerformer = container.getLastActionPerformer();
                        container.save("auto-version", (lastActionPerformer != null && !lastActionPerformer.isEmpty()) ? lastActionPerformer : userName, true); //NO I18N
                    }
                }
                catch (Exception e) {
                    logger.log(Level.WARNING, null, e);
                    response = "false";
                }
                break;
            case ActionConstants.OVERWRITE_API_POSTPROCESS:
            		EngineFactory.removeWorkbookContainer(containerIdentity, accessType);
            		break;
            case ActionConstants.OVERWRITE_API_PREPROCESS:
                String ownerZuid = request.getParameter(JSONConstants.ZUID);
                String resourceId = request.getParameter("rid");
                String documentId = request.getParameter("docId");
                String docOwner = request.getParameter("docowner");
                String ZFSNGversionId = request.getParameter("versionid");
                String fileName = request.getParameter("filename");
                String format = request.getParameter("format");
//                boolean isSaveRequired = Boolean.parseBoolean(request.getParameter("isSaveRequired"));
                
//                boolean isSaved = true;
                
                                        //String versionInfo      =   ZohoFS.getVersionInfoForId(ownerZuid, resourceId, ZFSNGversionId);
                                       // if(versionInfo != null){
                //    JSONArray versionInfoJson = new JSONArray(versionInfo);
                //  String zuid = versionInfoJson.getJSONObject(0).getString("creator");
                // String[] zuids = zuid.split(",");
                //actionObject.set(JSONConstants.ZUID, zuids[0]);
                //}
                //actionObject.set("rsid", "-1");//NO I18N
                //container.addMutedActionToQueue(actionObject);
                //if(CurrentRealm.getAccessIdentity() != UserProfile.AccessType.PUBLIC_EXTERNAL && CurrentRealm.getAccessIdentity() != UserProfile.AccessType.PUBLIC_ORG) {
                //  container.addVersionAuthor(actionObject.getString(JSONConstants.ZUID));
                //}
//                if(isSaveRequired) {
                        //isSaved = container.save(null, null, true);
//                }else {
//                        isSaved = true;
//                }
                
               
                    
        //conversionSuccess = importer.importSheetDocument(zuid, resourceId, documentId, docOwner, zfsngVersionId, fileName, format, contentBytes)
                    int conversionSuccess;
                    try {
                    		   Importer importer = ImporterFactory.getImporterInstance(format);
                            conversionSuccess = importer.importSheetDocument(ownerZuid, resourceId, documentId, docOwner, loginName, IAMUtil.getCurrentUser() != null ? ""+IAMUtil.getCurrentUser().getZUID():ownerZuid, ZFSNGversionId, fileName, format, Constants.SHEET_DOCUMENT, null,false, null);
                    } catch(Exception e) {
                            conversionSuccess = -1;
                            logger.log(Level.INFO, "Exception while importing in DocumentActionManager :: "+e);
                    } 
                EngineFactory.removeWorkbookContainer(containerIdentity, accessType);
                    response = Integer.toString(conversionSuccess);
                /*int importDocument = DocumentUtils.importDocument(request, ownerZuid, resourceId, documentId, docOwner, ZFSNGversionId, fileName, format);
                if (importDocument == ZFSNGConstants.CONVERSION_SUCCESS){
                    response = "true";
                }else{
                    response = "false";
                }
                String senderUserId = null;
                Hashtable collabUserTable = CollaborationApi.getSessions(container.getCollabId());
                if (!collabUserTable.isEmpty()) {
                    Enumeration e = collabUserTable.keys();
                    senderUserId = (String) e.nextElement();
                }
                if (senderUserId != null) {
                    JSONObject respObj = new JSONObject();
                    respObj.put(JSONConstants.ACTION, ActionConstants.VERSION_UPLOAD_NOTIFICATION); //NO I18N
                    respObj.put(JSONConstants.DIRECT_UPDATE, true); //NO I18N
                    respObj.put(JSONConstants.DOC_ID, documentId); //NO I18N
                    respObj.put(JSONConstants.COLLAB_ID, container.getCollabId()); //NO I18N
                    MessagePropagator.sendMessage(container.getCollabId(), senderUserId, respObj);
                }*/
                break;
            case ActionConstants.CHART_PUBLISH:
                String chartId = request.getParameter("chartId");
                String sheetName = request.getParameter("sheetName");
                String mode = request.getParameter("mode");
//                Workbook workbook = container.getWorkbook(null);
                if (workbook != null) {
                    if (String.valueOf(ActionConstants.CHART_PUBLISH_INTERACTIVE).equals(mode)) {
                        Chart chart = workbook.getChart(workbook.getSheet(sheetName).getAssociatedName(), chartId);
                        response = ChartUtils.getChartResponseJson(workbook.getSheet(sheetName), chart, ActionConstants.CHART_EDIT, false, action, null).toString();
                    }
                    else {
                        String publicChartId = request.getParameter("pubChartId");
                        ChartUtils.saveAsImage( workbook.getSheet(sheetName), chartId, publicChartId, true, container.getDocOwner());
                    }
                }
                break;
            case ActionConstants.PUBLISH_CHART_REGENERATE: {
                String chartID = request.getParameter(ChartActionConstants.JSONConstants.CHART_ID);
                String publicSpace = request.getParameter(ChartActionConstants.JSONConstants.PUBLIC_SPACE);
                String publicSpaceChartID = request.getParameter(ChartActionConstants.JSONConstants.PUBLIC_SPACE_CHART_ID);
                int regenerationMode = Integer.parseInt(request.getParameter(ChartActionConstants.JSONConstants.MODE));

                if(Objects.nonNull(workbook)) {
                    if (ChartActionConstants.Constants.INTERACTIVE == regenerationMode) {
                        PublicChartUtils.regeneratePublishChartCache(workbook, chartID, publicSpaceChartID, publicSpace);
                    } else if(ChartActionConstants.Constants.IMAGE == regenerationMode) {
                        PublicChartUtils.regeneratePublishChartImage(workbook, chartID, publicSpaceChartID, publicSpace);
                    }
                }
                response = "ok";    // NO I18N
                break;
            }
            case ActionConstants.CHART_DETAILS:
                String intraction = request.getParameter("mode");
                if (workbook != null) {
                    if (String.valueOf(ActionConstants.CHART_PUBLISH_INTERACTIVE).equals(intraction)) {
                        Chart chart = workbook.getChart(workbook.getSheet(request.getParameter("sheetName")).getAssociatedName(), request.getParameter("chartId"));
                        response = ChartUtils.getChartResponseJson(workbook.getSheet(request.getParameter("sheetName")), chart, ActionConstants.CHART_EDIT, false, action, null).toString();
                    }else{
                        byte[] imageBytes = ChartUtils.convertToImageBytes(workbook.getSheet(request.getParameter("sheetName")), request.getParameter("chartId"), false);
                        if(imageBytes != null){
                            response = ChartUtils.encodeImageIntoString(imageBytes);
                        }
                    }
                }
                break;
            case ActionConstants.CREATE_SHEET_FOR_FORM: 
                JSONObjectWrapper formResp = new JSONObjectWrapper();
                Sheet currSheet = workbook.getSheet(0);
                formResp.put(JSONConstants.IS_SUCCESS, true);
                JSONObjectWrapper formObj = new JSONObjectWrapper();
                formObj.put(JSONConstants.FORM_RID, request.getParameter(JSONConstants.FORM_RID));
                formResp.put(JSONConstants.FORM_CREATERESPONSE, formObj);
                request.setAttribute("formResponse", formResp.toString());
                JSONObjectWrapper jObj = new JSONObjectWrapper();
                UserProfile uProfile = CurrentRealm.getUserProfile();
                if (uProfile != null) {
                    jObj.put(JSONConstants.ZUID, uProfile.getZUserId());
                    jObj.put(JSONConstants.USER_NAME, uProfile.getUserName());
                }
                jObj.put(JSONConstants.RSID, "-1");
                jObj.put(JSONConstants.FORM_RESPONSE, formResp.toString());
                jObj.put(JSONConstants.ASSOCIATED_SHEET_NAME, currSheet.getAssociatedName());
                jObj.put("iae", Boolean.valueOf(request.getParameter("insertAtEnd")));
                                        // new ActionInfo(ActionConstants.CREATE_SHEET_FOR_FORM, "create_sheet_for_form", SHEET_JSON, false, false, false, false, false, true, false));
                                        /*     *  1  - recordUndo
                 *  2  - isValueChange
                 *  3  - isStyleChange
                 *  4  - isCalcRowHeight
                 *  5  - isNamedRangeChange
                 *  6  - isClientAction
                 *  7  - isRepeatAction
                 */
                jObj.put(JSONConstants.ACTION, ActionConstants.CREATE_SHEET_FOR_FORM);
                jObj.put("is_vc", false);
                jObj.put("is_sc", false);
                jObj.put("is_calc_rh", false);
                jObj.put("is_nrc", false);
                jObj.put("is_ca", true);
                jObj.put("is_ra", false);
                jObj.put("ru", false);
                container.addActionToQueue(jObj);
                break;
        }
        return response;
    }
}
