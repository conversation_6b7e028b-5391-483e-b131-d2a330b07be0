//$Id$
package com.adventnet.zoho.websheet.model.ziputil.ReplaceInXml;

import com.adventnet.zoho.websheet.model.writer.zs.XMLWriter;
import com.adventnet.zoho.websheet.model.zsparser.Names;
import com.adventnet.zoho.websheet.model.zsparser.ZSWorkbookParser;
import com.zoho.sheet.zuidMigration.helper.ZUIDMigrationHelper;

import java.util.List;
import java.util.Set;

public class ZUIDInSheetTableProtection implements ReplaceInElementNameAndAttributeI {
    private ZUIDMigrationHelper zuidMigrationHelper = null;
    private String docSpaceId = null;
    private int matched = 0;
    private int replaced = 0;

    private String protectionElement = XMLWriter.toString(Names.N_TABLE_PROTECTION);

    @Override
    public boolean zipEntryMatches(String zipEntryName) {
        return zipEntryName.matches("^[0-9]+#/[0-9]+#\\.xml");//No I18N
    }

    @Override
    public boolean elementMatches(String elementName) {
        return elementName.equals(protectionElement);
    }

    @Override
    public int matched() {
        return matched;
    }

    @Override
    public int replaced() {
        return replaced;
    }

    @Override
    public String replace(String name, List<String> keys, List<String> values) {
        for(int i = 0; i < keys.size(); i++) {
            Set<String> setFromString = null;
            if(keys.get(i).equals("table:authusers")) {
                setFromString = ZSWorkbookParser.createSetFromString(values.get(i));
            } else if(keys.get(i).equals("table:unauthusers")) {
                setFromString = ZSWorkbookParser.createSetFromString(values.get(i));
            } else if(keys.get(i).equals("table:authgroups")) {
                setFromString = ZSWorkbookParser.createSetFromString(values.get(i));
            } else if(keys.get(i).equals("table:authorgs")) {
                setFromString = ZSWorkbookParser.createSetFromString(values.get(i));
            }
            if(zuidMigrationHelper != null && setFromString != null) {
                this.matched+=setFromString.size();
                Set<String> setFromString_new = zuidMigrationHelper.getMigratedZUIDList(docSpaceId, setFromString);
                if(setFromString_new != null && setFromString_new.size() == setFromString.size()) {
                    for(String s : setFromString) {
                        if(!setFromString_new.contains(s)) {
                            this.replaced++;
                        }
                    }
                    values.set(i, setFromString_new.toString());
                }
            }
        }
        return name;
    }

    public ZUIDInSheetTableProtection setZuidMigrationHelper(ZUIDMigrationHelper zuidMigrationHelper) {
        this.zuidMigrationHelper = zuidMigrationHelper;
        return this;
    }

    public ZUIDInSheetTableProtection setDocSpaceId(String docSpaceId) {
        this.docSpaceId = docSpaceId;
        return this;
    }
}
