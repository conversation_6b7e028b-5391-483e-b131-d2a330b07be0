//$Id$
package com.adventnet.zoho.websheet.model.ziputil.ReplaceInXml;


import com.adventnet.zoho.websheet.model.ziputil.DelugeIdMigrationHelper;

import java.util.HashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DelugeIdInSheetExpression implements ReplaceInValueI {
    public static  final String DELUGEID_PATTERN_STR = "DFID_[0-9]+_[0-9]+";//No I18N
    private final DelugeIdMigrationHelper delugeIdMigrationHelper;
    private  String table_expression = "table:expression";//No I18N
    private Pattern delugeId_pattern = Pattern.compile(DELUGEID_PATTERN_STR);
    private AtomicInteger matched = new AtomicInteger(0);
    private AtomicInteger replaced = new AtomicInteger(0);

    public DelugeIdInSheetExpression(DelugeIdMigrationHelper delugeIdMigrationHelper) {
        this.delugeIdMigrationHelper = delugeIdMigrationHelper;
    }

    @Override
    public boolean zipEntryMatches(String zipEntryName) {
        return zipEntryName.matches("^[0-9]+#/[0-9]+#\\.xml");//No I18N
    }

    @Override
    public boolean elementMatches(String elementName) {
        return elementName.equals(table_expression);
    }

    @Override
    public int matched() {
        return this.matched.get();
    }

    @Override
    public int replaced() {
        return this.replaced.get();
    }

    @Override
    public String replace(String value) {
        return replaceDelugeIdInExpression(value, this.delugeId_pattern, this.delugeIdMigrationHelper, this.matched, this.replaced);
    }

    public static String replaceDelugeIdInExpression(String expression, Pattern pattern, DelugeIdMigrationHelper delugeIdMigrationHelper, AtomicInteger matched, AtomicInteger replaced) {

        if(!expression.contains("DFID_")) {
            return expression;
        }

        Matcher matcher = pattern.matcher(expression);
        StringBuffer stringBuffer = new StringBuffer();
        while(matcher.find()) {
            matched.incrementAndGet();
            String delugeId = expression.substring(matcher.start(), matcher.end());
            String newDelugeId = delugeIdMigrationHelper.getNewDelugeId(delugeId);
            if(newDelugeId == null) {
                newDelugeId = delugeId;
            } else if(!newDelugeId.equals(delugeId)){
                replaced.incrementAndGet();
            }
            matcher.appendReplacement(stringBuffer, newDelugeId);
        }
        matcher.appendTail(stringBuffer);

        return stringBuffer.toString();
    }
}
