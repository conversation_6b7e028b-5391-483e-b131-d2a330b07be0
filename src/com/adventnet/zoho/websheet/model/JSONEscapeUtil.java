package com.adventnet.zoho.websheet.model;

import com.fasterxml.jackson.core.io.JsonStringEncoder;


/**
 * <AUTHOR>
 */
public class JSONEscapeUtil
{
    private static final JsonStringEncoder ENCODER = JsonStringEncoder.getInstance();

    /**
     * This method will be used to escape the special characters like \n, \r, \t etc.
     */
    public static String escape(String jsonString)
    {
        if (jsonString == null)
        {
            return null;
        }
        if(jsonString.isEmpty())
        {
            return jsonString;
        }
        if(!needsEscaping(jsonString))
        {
            return jsonString;
        }
        return new String(ENCODER.quoteAsString(jsonString));
    }

    private static boolean needsEscaping(String s)
    {
        for(int index = 0; index < s.length(); index++)
        {
            char c = s.charAt(index);
            if(c == '"' || c == '\\' || c < 0x20)
            {
                return true;
            }
        }
        return false;
    }

}
