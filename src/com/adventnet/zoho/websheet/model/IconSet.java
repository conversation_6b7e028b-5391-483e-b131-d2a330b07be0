//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.ConditionalFormatEntry.IconSetObj;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.style.ConditionalStyleResponse.CellObject;
import com.adventnet.zoho.websheet.model.style.ConditionalStyleResponse.ConditionalStyleCellStyles;
import com.adventnet.zoho.websheet.model.util.IconSetConstants;
import com.adventnet.zoho.websheet.model.writer.XMLWriter;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.functions.Comparative;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class IconSet implements ConditionalStyle
{
    public static final Logger LOGGER = Logger.getLogger(IconSet.class.getName());
    private final String name;
    private final List<ConditionalFormatEntry> iconSetEntries;
    private Boolean hasPercentileEntry = null;
    private Boolean hasFormulaEntry = null;
    private boolean defaultIconSize = false;
    private boolean reverseIconOder = false;
    
    public IconSet(String name, List<ConditionalFormatEntry> iconSetEntries, boolean defaultIconSize, boolean reverseIconOder)
    {
        if(iconSetEntries == null || iconSetEntries.size() < 2 || iconSetEntries.size() > 5)
        {
            throw new IllegalArgumentException("A icon set should have 2 to 5 IconSetEntries : "+iconSetEntries);//No I18N
        }
        this.name = name;
        this.iconSetEntries = iconSetEntries;
        this.defaultIconSize = defaultIconSize;
        this.reverseIconOder = reverseIconOder;
    }
    
    public String getName()
    {
        return name;
    }
    
    public String getNameForXML()
    {
        return replaceUnSupportedIconSetName(name, iconSetEntries.size());
    }

    public boolean isDefaultIconSize() 
    {
        return defaultIconSize;
    }

    public boolean isReverseIconOder() 
    {
        return reverseIconOder;
    }
    
    @Override
    public List<ConditionalFormatEntry> getConditionalStyleEntries()
    {
        return Collections.unmodifiableList(iconSetEntries);
    }
    
    @Override
    public int hashCode()
    {
        int hash = 5;
        hash = 97 * hash + (this.iconSetEntries != null ? this.iconSetEntries.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj)
    {
        if(this == obj)
        {
            return true;
        }
        if(obj == null)
        {
            return false;
        }
        if(getClass() != obj.getClass())
        {
            return false;
        }
        final IconSet other = (IconSet) obj;
        return !(this.iconSetEntries == null ? other.iconSetEntries !=  null : !this.iconSetEntries.equals(other.iconSetEntries));
    }

    @Override
    public ConditionalStyle absoluteClone(Workbook workbook) {
        List<ConditionalFormatEntry> newCSEList = new ArrayList<>();
        for(ConditionalFormatEntry cse : this.getConditionalStyleEntries())
        {
            ConditionalFormatEntry newCSE = cse.clone();
            newCSEList.add(newCSE);
        }
        return new IconSet(this.name, newCSEList, this.isDefaultIconSize(), this.isReverseIconOder());
    }
    
    @Override
    public boolean updateConditionalStyle(Sheet sheet, ReadOnlyCell rCell, ConditionalStyleObject csf, long currentTime, int csUID, Map<CellObject, ConditionalStyleCellStyles> cellStylesMap, boolean isIncludeCellStyle)
    {
        return ConditionalFormatEntry.updateConditionalStyleResult(sheet, rCell, csf, currentTime, csUID, cellStylesMap, isIncludeCellStyle);
    }
    
    @Override
    public boolean isConditionalStyleExists(ConditionalStyleCellStyles csCellStyles)
    {
        return csCellStyles.isIconExists();
    }
    
    @Override
    public void setConditionalStyleResult(ConditionalStyleCellStyles csCellStyles, Object csResult)
    {
        csCellStyles.setIcon((IconSetObj)csResult, this.defaultIconSize);
    }
    
    
    @Override
    public Object getResult(Cell cell, ConditionalStyleObject conditionalStyleFormat) 
    {
        try{
            Double value = FunctionUtil.objectToNumber(cell.getValue().getValue()).doubleValue();
            List<Double> intervals = conditionalStyleFormat.getIntervals();
            //NOTE: IconSetEntry is maintained in reverse order.(Coz of XML).
            int size = getConditionalStyleEntries().size();
            int j = reverseIconOder ? size-1 : 0;
            int i = size - 1;

            while(i > 0)
            {
                Comparative comp = new Comparative(((IconSetObj)getConditionalStyleEntries().get(Math.abs(i)).getCSEObj()).getIconCriteriaComparative());
                if(comp.compare(value, intervals.get(Math.abs(i - 1))))
                {
                    return (IconSetObj)getConditionalStyleEntries().get(Math.abs(i-j)).getCSEObj();
                }
                i--;
            }
            return (IconSetObj)getConditionalStyleEntries().get(Math.abs(i-j)).getCSEObj();
        }catch (EvaluationException ex){} // Do nothing
        
        return null;
    }
    
    public String[] getAttributes()
    {
        List<String> attributes = new ArrayList<>();
        attributes.add("calcext:icon-set-type"); //NO I18N
        if(!getName().equals(getNameForXML()))
        {
            attributes.add("calcext:icon-set-name"); //NO I18N
        }
        attributes.add("calcext:icon-default-size"); //NO I18N
        attributes.add("calcext:icon-reverse-order"); //NO I18N
        return attributes.toArray(new String[attributes.size()]);
    }
    
    public String[] getValues()
    {
        List<String> values = new ArrayList<>();
        values.add(getNameForXML());
        if(!getName().equals(getNameForXML()))
        {
            values.add(getName());
        }
        values.add(Boolean.toString(isDefaultIconSize()));
        values.add(Boolean.toString(isReverseIconOder()));
        return values.toArray(new String[values.size()]);
    }
    
    @Override
    public String getStyleXML(Workbook workbook, int baseRowIndex, int baseColIndex, boolean autoColor, boolean hideText) {
        
        StringBuilder iconSetXML = new StringBuilder();
        String iconSetStr = XMLWriter.createStartTagOpen("calcext:icon-set", //NO I18N
                                                                     getAttributes(),
                                                                     getValues(), 
                                                                     false);
        iconSetXML.append(iconSetStr);
        
        int i = 0;
        for(ConditionalFormatEntry entry : getConditionalStyleEntries())
        {
            boolean isFirstEntry = (i++ == 0);
            String iconSetEntryStr = XMLWriter.createStartTagClose("calcext:formatting-entry", //NO I18N
                                                                         entry.getISAttributes(isFirstEntry),
                                                                         entry.getISValues(workbook, baseRowIndex, baseColIndex, isFirstEntry, !hideText),
                                                                         true);
            iconSetXML.append(iconSetEntryStr);
        }
        iconSetXML.append("</calcext:icon-set>"); //NO I18N
        return iconSetXML.toString();
    }
    
    @Override
    public boolean hasPercentileEntry() {
        if(hasPercentileEntry == null)
        {
            hasPercentileEntry = ConditionalFormatEntry.hasPercentileOrFormulaEntry(iconSetEntries, false);
        }
        
        return hasPercentileEntry;
    }
    
    @Override
    public boolean hasFormulaEntry(){
        if(hasFormulaEntry == null)
        {
            hasFormulaEntry = ConditionalFormatEntry.hasPercentileOrFormulaEntry(iconSetEntries, true);
        }
        
        return hasFormulaEntry;
    }
    
    public static String getIconClassName(String iconName, int iconId)
    {
        String iconClassName = iconName + iconId;
        
        switch(iconClassName)
        {
            case IconSetConstants.IN_3ARROWS0: 
            case IconSetConstants.IN_4ARROWS0:
            case IconSetConstants.IN_5ARROWS0:
            case IconSetConstants.IN_4RATING0:
                return IconSetConstants.IS_5ARROWS0;
                
            case IconSetConstants.IN_3ARROWS1:
            case IconSetConstants.IN_5ARROWS2:
                return IconSetConstants.IS_5ARROWS2;
                
            case IconSetConstants.IN_3ARROWS2:
            case IconSetConstants.IN_4ARROWS3:
            case IconSetConstants.IN_5ARROWS4:
            case IconSetConstants.IN_4RATING3:
                return IconSetConstants.IS_5ARROWS4;
                
            case IconSetConstants.IN_4ARROWS1:
            case IconSetConstants.IN_5ARROWS1:
            case IconSetConstants.IN_4RATING1:
                return IconSetConstants.IS_5ARROWS1;
            case IconSetConstants.IN_4ARROWS2:
            case IconSetConstants.IN_5ARROWS3:
            case IconSetConstants.IN_4RATING2:
                return IconSetConstants.IS_5ARROWS3;
            
                
         
            case IconSetConstants.IN_3ARROWSGRAY0:
            case IconSetConstants.IN_4ARROWSGRAY0:
            case IconSetConstants.IN_5ARROWSGRAY0:
                return IconSetConstants.IS_5ARROWSGRAY0;
                
           
            case IconSetConstants.IN_3ARROWSGRAY1:
            case IconSetConstants.IN_5ARROWSGRAY2:
                return IconSetConstants.IS_5ARROWSGRAY2;
                
            
            case IconSetConstants.IN_3ARROWSGRAY2:
            case IconSetConstants.IN_4ARROWSGRAY3:
            case IconSetConstants.IN_5ARROWSGRAY4:
                return IconSetConstants.IS_5ARROWSGRAY4;
                
            
            case IconSetConstants.IN_4ARROWSGRAY1:
            case IconSetConstants.IN_5ARROWSGRAY1:
                return IconSetConstants.IS_5ARROWSGRAY1;
                
            case IconSetConstants.IN_4ARROWSGRAY2:
            case IconSetConstants.IN_5ARROWSGRAY3:
                return IconSetConstants.IS_5ARROWSGRAY3;
                
            case IconSetConstants.IN_3TRAFFICLIGHTS0:
            case IconSetConstants.IN_4TRAFFICLIGHTS1:
            case IconSetConstants.IN_5TRAFFICLIGHTS2:
//            case IconSetConstants.IN_4REDTOBLACK1:
            case IconSetConstants.IN_3TRAFFICLIGHTS10:
                return IconSetConstants.IS_5TRIFFICLIGHTS2;
                
            case IconSetConstants.IN_3TRAFFICLIGHTS1:
            case IconSetConstants.IN_4TRAFFICLIGHTS2:
            case IconSetConstants.IN_5TRAFFICLIGHTS3:
//            case IconSetConstants.IN_4REDTOBLACK2:
            case IconSetConstants.IN_3TRAFFICLIGHTS11:
                return IconSetConstants.IS_5TRIFFICLIGHTS3;
            case IconSetConstants.IN_3TRAFFICLIGHTS2:
            case IconSetConstants.IN_4TRAFFICLIGHTS3:
            case IconSetConstants.IN_5TRAFFICLIGHTS4:
            case IconSetConstants.IN_3SIGNS2:
//            case IconSetConstants.IN_4REDTOBLACK3:
            case IconSetConstants.IN_3TRAFFICLIGHTS12:
                return IconSetConstants.IS_5TRIFFICLIGHTS4; 
            case IconSetConstants.IN_4TRAFFICLIGHTS0:
            case IconSetConstants.IN_5TRAFFICLIGHTS0:
            case IconSetConstants.IN_5RATINGS0:
//            case IconSetConstants.IN_5QUARTERS0:
            case IconSetConstants.IN_5BOXES0:
                return IconSetConstants.IS_5TRIFFICLIGHTS0;
            case IconSetConstants.IN_5TRAFFICLIGHTS1:
                return IconSetConstants.IS_5TRIFFICLIGHTS1;
                
//            case IconSetConstants.IN_3SIGNS1:
//                return IconSetConstants.IS_TRIANGLEBK;
//            case IconSetConstants.IN_3SIGNS0:
//                return IconSetConstants.IS_PENTAGON;
            case IconSetConstants.IN_3SYMBOLS10:
            case IconSetConstants.IN_3SYMBOLS20:
                return IconSetConstants.IS_3SYMBOLS0;
            case IconSetConstants.IN_3SYMBOLS11:
            case IconSetConstants.IN_3SYMBOLS21:
                return IconSetConstants.IS_3SYMBOLS1;
            
            case IconSetConstants.IN_3SYMBOLS12:
            case IconSetConstants.IN_3SYMBOLS22:
                return IconSetConstants.IS_3SYMBOLS2;
                
//            case IconSetConstants.IN_5RATINGS0:
//            case IconSetConstants.IN_5QUARTERS0:
//            case IconSetConstants.IN_5BOXES0:
//                return IconSetConstants.IS_5QUARTERS0;
                
            case IconSetConstants.IN_5RATINGS1:
            case IconSetConstants.IN_5QUARTERS1:
            case IconSetConstants.IN_5BOXES1:
                return IconSetConstants.IS_5QUARTERS1;
            
            case IconSetConstants.IN_5RATINGS2:
            case IconSetConstants.IN_5QUARTERS2:
            case IconSetConstants.IN_5BOXES2:
                return IconSetConstants.IS_5QUARTERS2;
                
            case IconSetConstants.IN_5RATINGS3:
            case IconSetConstants.IN_5QUARTERS3:
            case IconSetConstants.IN_5BOXES3:
                return IconSetConstants.IS_5QUARTERS3;
                
            case IconSetConstants.IN_5RATINGS4:
            case IconSetConstants.IN_5QUARTERS4:
            case IconSetConstants.IN_5BOXES4:
                return IconSetConstants.IS_5QUARTERS4;
            case IconSetConstants.IN_3HEARTRATING2:
            case IconSetConstants.IN_2LOVE0:
                return IconSetConstants.IS_3HEART0; 
                
            default:
                return "iS-"+iconClassName; //No I18N
        }
    }
    
    public static String replaceUnSupportedIconSetName(String iconName, int size)
    {
        switch(iconName)
        {
            case IconSetConstants.LOVE2:
            case IconSetConstants.LIKES2:
            case IconSetConstants.STARS3:
            case IconSetConstants.TRIANGLES3:
            case IconSetConstants.INDICATOR3:
            case IconSetConstants.HEARTRATING3:
                return IconSetConstants.ARROWS3;
            case "4Rating": //No I18N
                return IconSetConstants.ARROWS4;
            case IconSetConstants.SMILEYS3:
                return "3Smilies"; //No I18N
            case IconSetConstants.SMILEYS5:
            case IconSetConstants.TRAFFICLIGHTS5:
            case IconSetConstants.WEATHER5:
            case "5Rating": //No I18N
                return IconSetConstants.ARROWS5;
            case IconSetConstants.TRAFFICLIGHTS3:
                return "3TrafficLights1"; //No I18N
            case "custom":
                switch(size)
                {
                    case 2:
                    case 3:
                        return IconSetConstants.ARROWS3;
                    case 4:
                        return IconSetConstants.ARROWS4;
                    case 5:
                        return IconSetConstants.ARROWS5;
                }
        }
        
        return iconName;
    }

    @Override
    public boolean changeTableNodeIfPresent(Workbook srcWorkbook, Map<String, String> tableNameMap) {
        boolean isChanged = false;
        for(ConditionalFormatEntry cse : this.getConditionalStyleEntries()) {
            isChanged = cse.changeTableNodeIfPresent(srcWorkbook, tableNameMap) || isChanged;
        }

        return isChanged;
    }

    @Override
    public boolean changeTableColumnsIfPresent(Workbook srcWorkbook, Table table, Map<String, String> colNameMap) {
        boolean isChanged = false;
        for(ConditionalFormatEntry cse : this.getConditionalStyleEntries()) {
            boolean change = cse.changeTableColumnsIfPresent(srcWorkbook, table, colNameMap);
            isChanged = change || isChanged;
        }

        return isChanged;
    }

    @Override
    public boolean hasStructuredReferences(Workbook workbook) {
        for(ConditionalFormatEntry cse: this.getConditionalStyleEntries()) {
            if(cse.hasStructuredReferences(workbook)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean convertStructuredReferenceToRange(Workbook workbook, String tableName, int baseRow, int baseCol) {
        boolean isChanged = false;
        for(ConditionalFormatEntry cse: this.getConditionalStyleEntries()) {
            isChanged = cse.convertStructuredReferencesToRange(workbook, tableName, baseRow, baseCol) || isChanged;
        }

        return isChanged;
    }
}
