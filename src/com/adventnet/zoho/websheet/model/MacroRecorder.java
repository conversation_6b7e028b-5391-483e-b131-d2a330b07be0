/* $Id$ */
/**
 * 
 */
package com.adventnet.zoho.websheet.model;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import com.adventnet.zoho.websheet.macros.excel.engineimpl.ChartImpl;
import com.adventnet.zoho.websheet.macros.excel.engineimpl.MacroEngineUtils;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.chart.ChartConstants;
import com.zoho.sheet.knitcharts.macros.converter.ActionToMacrosConverter;
import com.zoho.sheet.knitcharts.macros.converter.MacrosType;
import com.zoho.sheet.knitcharts.chart.frameworkchart.meta.type.ChartType;
import com.zoho.sheet.knitcharts.utils.ChartUtils;
import com.zoho.sheet.knitcharts.utils.ChartsBCUtils;
import com.zoho.sheet.util.ClientUtils;

/**
 * <AUTHOR>
 *
 */
public class MacroRecorder {
	final static ArrayList<Integer> UNSUPPORTED_ACTIONS = new ArrayList<Integer>(Arrays.asList(
    		ActionConstants.FREEZE_PANES,
    		ActionConstants.UNFREEZE_PANES,
    		ActionConstants.CTRL_COMP_ADD,
    		ActionConstants.CTRL_COMP_ASSIGNMACRO,
    		ActionConstants.CTRL_COMP_CLONE,
    		ActionConstants.CTRL_COMP_DELETE,
    		ActionConstants.CTRL_COMP_MOVE,
    		ActionConstants.CTRL_COMP_RESIZE,
    		ActionConstants.CTRL_COMP_SETCAPTION,
    		ActionConstants.CREATE_FILTER,
    		ActionConstants.REMOVE_FILTER,
    		ActionConstants.APPLY_FILTER,
    		ActionConstants.CONDITIONAL_FORMAT_APPLY,
    		ActionConstants.COLOR_SCALES_APPLY,
    		ActionConstants.ICON_SET_APPLY,
    		ActionConstants.DATA_BAR_APPLY,
    		ActionConstants.CONDITIONAL_FORMAT_EDIT,
    		ActionConstants.CONDITIONAL_FORMAT_DELETE,
    		ActionConstants.CREATE_PIVOT,
    		ActionConstants.EDIT_PIVOT,
    		ActionConstants.REFRESH_PIVOT,
    		ActionConstants.DELETE_PIVOT,
    		ActionConstants.APPLYFILTER_PIVOT,
    		ActionConstants.MOVE_PIVOT,
    		ActionConstants.SORT_PIVOT,
    		ActionConstants.CHANGE_PIVOT_SOURCE,
    		ActionConstants.REFRESH_ALL_PIVOT,
    		ActionConstants.CHANGE_PIVOT_THEME
	));
	
	final static ArrayList<Integer> EXCLUDE_RANGE_SELECTIONS = new ArrayList<Integer>(Arrays.asList(
			ActionConstants.NAMEDRANGE_ADD,
			ActionConstants.NAMEDRANGE_DELETE,
			ActionConstants.NAMEDRANGE_MODIFY,
			ActionConstants.SHEET_DUPLICATE,
			ActionConstants.SHEET_INSERT,
			ActionConstants.SHEET_MOVE,
			ActionConstants.SHEET_REMOVE,
			ActionConstants.SHEET_RENAME,
			
			ActionConstants.CHART_NEW,
			ActionConstants.CHART_DELETE,
			ActionConstants.CHART_MOVE,
			ActionConstants.CHART_RESIZE,
			ActionConstants.CHART_QUICK_EDIT
	));
	
	final static ArrayList<Integer> EXCLUDE_SHEET_SELECTIONS = new ArrayList<Integer>(Arrays.asList(
			ActionConstants.NAMEDRANGE_ADD,
			ActionConstants.NAMEDRANGE_DELETE,
			ActionConstants.NAMEDRANGE_MODIFY,
			ActionConstants.SHEET_DUPLICATE,
			ActionConstants.SHEET_INSERT,
			ActionConstants.SHEET_MOVE,
			ActionConstants.SHEET_REMOVE,
			ActionConstants.SHEET_RENAME
	));
	
	ArrayList<StringBuffer> contentList;
	ArrayList<StringBuffer> undoList;
	String macroName;
	String macroComments;
	String activeSheetName;
	boolean isRelativeReference;
	int startRow, startCol, endRow, endCol;
	int row, column;
	Map<String, String> sheetMap;
	String sheetArrayCache = "", rangeArrayCache = "";
	
	final static String STARTLINE = "\n    ";		//No I18N
    final static String SUBLINE = "    ";
    final static String COMMENTSTART = "\n'  ";		//No I18N
	
	public MacroRecorder(JSONObjectWrapper recordObject, String comments) {
		this.macroName 			= recordObject.getString("mn");	//No I18N	
		this.macroComments 		= comments;
        this.startRow 			= recordObject.getInt("sr");		//No I18N
        this.startCol 			= recordObject.getInt("sc");		//No I18N
        this.endRow 				= recordObject.getInt("er");		//No I18N
        this.endCol 				= recordObject.getInt("ec");		//No I18N
        
        this.row 				= recordObject.getInt("r");		//No I18N
        this.column 				= recordObject.getInt("c");		//No I18N
        this.activeSheetName 	= recordObject.getString("sn");	//No I18N

        this.isRelativeReference = recordObject.getBoolean("rr");//No I18N
        
        this.contentList 		= new ArrayList<StringBuffer>();
        this.undoList 			= new ArrayList<StringBuffer>(); 
        this.sheetMap 			= new HashMap<String, String>();
        
	}
	
	private void setActiveSheetName(String activeSheetName)
    {
        this.activeSheetName = activeSheetName;
    }
	
	private String getActiveSheetName() {
		return this.activeSheetName;
	}
	
	private boolean isRelativeReference() {
		return this.isRelativeReference;
	}
	
	private void setStartRow(int startRow) {
		this.startRow = startRow;
	}
	
	private void setEndRow(int endRow) {
		this.endRow = endRow;
	}
	
	private void setStartCol(int startCol) {
		this.startCol = startCol;
	}
	
	private void setEndColumn(int endCol) {
		this.endCol = endCol;
	}
	
	private void setRow(int row) {
		this.row = row;
	}
	
	private void setColumn(int column) {
		this.column = column;
	}
	
	private int getStartRow() {
		return this.startRow;
	}
	
	private int getStartCol() {
		return this.startCol;
	}
	
	private static StringBuffer getMacroHeader(String macroName, String comments) {
		StringBuffer content 	= new StringBuffer();
		content.append("Sub ");	//No I18N
        content.append(macroName);
        content.append("()");
        content.append(comments);
		return content;
	}
	
	private static StringBuffer getMacroFooter() {
		StringBuffer content 	= new StringBuffer();
		content.append("\nEnd Sub"); //No I18N
		return content;
	}
	
	private static void resetSelections(MacroRecorder mr, JSONObjectWrapper json)
    {
    		JSONArrayWrapper rangeList = json.getJSONArray(JSONConstants.RANGELIST).getJSONArray(0);
    		JSONObjectWrapper rangeJson = rangeList.getJSONObject(0);
        mr.setRow(rangeJson.getInt("sr"));			//No I18N
        mr.setColumn(rangeJson.getInt("sc"));		//No I18N
        mr.setStartRow(rangeJson.getInt("sr"));		//No I18N
        mr.setStartCol(rangeJson.getInt("sc"));		//No I18N
        mr.setEndRow(rangeJson.getInt("er"));		//No I18N
        mr.setEndColumn(rangeJson.getInt("ec"));		//No I18N
    }
	
	private String getAbsoluteRangeScript(JSONArrayWrapper rangeArray) {
		String rangeSelection = "";
		JSONArrayWrapper rangeList = rangeArray.getJSONArray(0);
		String selectionType = "";
		
		int size = rangeList.length();		
		for(int i = 0; i < size; i++) {
			JSONObjectWrapper rangeJson = rangeList.getJSONObject(i);
            int actionStartRow = rangeJson.getInt("sr");		//No I18N
            int actionStartCol = rangeJson.getInt("sc");		//No I18N

            int actionEndRow = rangeJson.getInt("er");		//No I18N
            int actionEndCol = rangeJson.getInt("ec");		//No I18N
            
            String selection = "";
            if(actionStartRow == 0 && actionEndRow == Utility.MAXNUMOFROWS - 1) {
            		selectionType = "Columns(\"";	//No I18N
            		selection = selection + CellUtil.getColumnReference(actionStartCol) + ":" + CellUtil.getColumnReference(actionEndCol);
            } else if(actionStartCol == 0 && actionEndCol == Utility.MAXNUMOFCOLS - 1) {
            		selectionType = "Rows(\"";		//No I18N
            		selection = selection + CellUtil.getRowReferenceForFormula(actionStartRow) + ":" + CellUtil.getRowReferenceForFormula(actionEndRow);
            } else {
            		selectionType = "Range(\"";		//No I18N
            		selection = CellUtil.getCellReference(actionStartRow, actionStartCol, false, false);
                if (actionStartRow != actionEndRow || actionStartCol != actionEndCol)
                {
                		selection = selection + ":" + CellUtil.getCellReference(actionEndRow, actionEndCol, false, false);
                }
            }
            
            rangeSelection = rangeSelection + selection + ",";
		}
		
		if(size > 1) {	//Selecting more than one rows/columns will be treated as Range only.
			selectionType = "Range(\"";		//No I18N
		}
		
		if(rangeSelection.equals("")) {
			return null;			
		}		  
		rangeSelection = rangeSelection.substring(0, rangeSelection.length() - 1);
		
		return selectionType + rangeSelection + "\").Select";	//No I18N
	}
	
	private String getRelativeRangeScript(JSONArrayWrapper rangeArray) {
		
		JSONArrayWrapper rangeList = rangeArray.getJSONArray(0);
		int startRow = Utility.MAXNUMOFROWS;
		int startCol = Utility.MAXNUMOFCOLS;
		
		String rangeSelection = "";
		int size = rangeList.length();	
		for(int i = 0; i < size; i++) {
			JSONObjectWrapper rangeJson = rangeList.getJSONObject(i);
            int actionStartRow = rangeJson.getInt("sr") - this.getStartRow();		//No I18N
            int actionStartCol = rangeJson.getInt("sc") - this.getStartCol();		//No I18N
                        
            startRow = actionStartRow < startRow ? actionStartRow : startRow;
            startCol = actionStartCol < startCol ? actionStartCol : startCol;
		}			
		String offsetString = getOffsetLine(startRow, startCol);
		
		int offsetRow = startRow + this.getStartRow();
		int offsetCol = startCol + this.getStartCol();
		String selectionType = "";
		
		for(int i = 0; i<rangeList.length(); i++) {
			JSONObjectWrapper rangeJson = rangeList.getJSONObject(i);
			int actionStartRow = rangeJson.getInt("sr");		//No I18N
            int actionStartCol = rangeJson.getInt("sc");		//No I18N

            int actionEndRow = rangeJson.getInt("er");		//No I18N
            int actionEndCol = rangeJson.getInt("ec");		//No I18N
            String selection = "";
            if(actionStartRow == 0 && actionEndRow == Utility.MAXNUMOFROWS - 1) {
            		selectionType = "Columns(\"";	//No I18N
	        		selection = selection + CellUtil.getColumnReference(actionStartCol - offsetCol) + ":" + CellUtil.getColumnReference(actionEndCol - offsetCol);
	        } else if(actionStartCol == 0 && actionEndCol == Utility.MAXNUMOFCOLS - 1) {
	        		selectionType = "Rows(\"";		//No I18N
	        		selection = selection + CellUtil.getRowReferenceForFormula(actionStartRow - offsetRow) + ":" + CellUtil.getRowReferenceForFormula(actionEndRow - offsetRow);
	        } else {
	        		selectionType = "Range(\"";		//No I18N
	            selection = CellUtil.getCellReference(actionStartRow - offsetRow, actionStartCol - offsetCol, false, false);
	            if (actionStartRow != actionEndRow || actionStartCol != actionEndCol)
	            {
	            		selection = selection + ":" + CellUtil.getCellReference(actionEndRow - offsetRow, actionEndCol - offsetCol, false, false);
	            }
	        }
            rangeSelection = rangeSelection + selection + ",";            
		}	
		
		if(size > 1) {	//Selecting more than one rows/columns will be treated as Range only.
			selectionType = "Range(\"";		//No I18N
		}
		if(rangeSelection.equals("")) {
			return null;			
		}		  
		rangeSelection = rangeSelection.substring(0, rangeSelection.length() - 1);
		return offsetString + selectionType + rangeSelection + "\").Select";	//No I18N
	}
	
	private static String getOffsetLine(int row, int col)
    {
        if (row == 0 && col == 0)
        {
            return "ActiveCell.";//No I18N
        } else
        {
            return "ActiveCell.Offset(" + row + "," + col + ").";//No I18N
        }
    }
	
	private String getSheetSelectionScript(JSONArrayWrapper sheetArray,  WorkbookContainer container) throws Exception {		// Yet to handle for MultiRange
		String sheetSelection = "";
		JSONArrayWrapper sheetList = sheetArray.getJSONArray(0);
		Workbook workbook = container.getWorkbook(null);
		
		for(int i = 0; i<sheetList.length(); i++) {
			String asn 	= sheetList.getString(i);
			String sheetName;
			
			if(this.sheetMap.containsKey(asn)) {
				sheetName = this.sheetMap.get(asn);
			} else {
				Sheet sheet 			= workbook.getSheetByAssociatedName(asn);	
				sheetName  			= sheet.getName();
				this.sheetMap.put(asn, sheetName);
			}
			
			if(!this.getActiveSheetName().equals(sheetName)) {
				sheetSelection = sheetSelection + sheetName + ",";
				this.setActiveSheetName(sheetName);
			}			
		}
		if(sheetSelection.equals("")) {
			return null;
		}
		sheetSelection = sheetSelection.substring(0, sheetSelection.length() - 1);  
		return "Sheets(\""+sheetSelection+"\").Select";	//No I18N
	}
	
	public StringBuffer getSelectionBuffer(JSONObjectWrapper actionJson, WorkbookContainer container, boolean isFromSrcRange) throws Exception {
		
		int action = actionJson.getInt("a");		//No I18N
		JSONArrayWrapper rangeArray;
		JSONArrayWrapper sheetArray;
		
		if(isFromSrcRange) {
			switch(action) {
				case ActionConstants.COPY_PASTE:
			    case ActionConstants.PASTESPECIAL_FORMATS:
			    case ActionConstants.CUT_PASTE:
			    case ActionConstants.INSERT_COPY_ROW:
	            case ActionConstants.INSERT_CUT_ROW:
	            case ActionConstants.INSERT_COPY_COLUMN:
	            case ActionConstants.INSERT_CUT_COLUMN:
	            		rangeArray = actionJson.has(JSONConstants.SOURCE_RANGELIST) ? actionJson.getJSONArray(JSONConstants.SOURCE_RANGELIST) : null;
	            		sheetArray = actionJson.has(JSONConstants.SOURCE_SHEETLIST) ? actionJson.getJSONArray(JSONConstants.SOURCE_SHEETLIST) : null;
	            		break;
	            	default:
	            		rangeArray = actionJson.has(JSONConstants.RANGELIST) ? actionJson.getJSONArray(JSONConstants.RANGELIST) : null;
	            		sheetArray = actionJson.has(JSONConstants.SHEETLIST) ? actionJson.getJSONArray(JSONConstants.SHEETLIST) : null;
			}			
		} else {
			rangeArray = actionJson.has(JSONConstants.RANGELIST) ? actionJson.getJSONArray(JSONConstants.RANGELIST) : null;
			sheetArray = actionJson.has(JSONConstants.SHEETLIST) ? actionJson.getJSONArray(JSONConstants.SHEETLIST) : null;
		}

		StringBuffer selectionBuffer = new StringBuffer();
		if(sheetArray != null && !EXCLUDE_SHEET_SELECTIONS.contains(action) && !(sheetArray.toString().equals(sheetArrayCache.toString()))) {   
			sheetArrayCache = sheetArray.toString();
			String destSheetSelection = getSheetSelectionScript(sheetArray, container);
			if(destSheetSelection != null) {
				selectionBuffer.append(STARTLINE);
				selectionBuffer.append(destSheetSelection);
			}
		}
		
		if(rangeArray != null && !EXCLUDE_RANGE_SELECTIONS.contains(action) && !(rangeArray.toString().equals(rangeArrayCache.toString()))) {
			rangeArrayCache = rangeArray.toString();
			String destRangeSelection = null;
			if(this.isRelativeReference()) {
				destRangeSelection = getRelativeRangeScript(rangeArray);
			} else {
				destRangeSelection = getAbsoluteRangeScript(rangeArray);
			}
			if(destRangeSelection != null) {
				selectionBuffer.append(STARTLINE);
				selectionBuffer.append(destRangeSelection);
			}
		}		
		return selectionBuffer;
	}
	
	private StringBuffer getContentBuffer(JSONObjectWrapper actionJson, WorkbookContainer container) throws Exception {
		int action = actionJson.getInt("a");		//No I18N
		StringBuffer contentBuffer = new StringBuffer();
		switch(action) {
	
	        case ActionConstants.SUBMIT:
	        case ActionConstants.SYSTEMCLIP_PASTE:
	        case ActionConstants.COPY_PASTE_CONTENT:
	            String cellValue = Utility.getDecodedString(actionJson.getString("v"));//NO I18N
	            if (cellValue != null)
	            {
	            		JSONArrayWrapper rangeList = actionJson.getJSONArray(JSONConstants.RANGELIST).getJSONArray(0);
	                
	                JSONObjectWrapper rangeJson = rangeList.getJSONObject(0);
	                int curRow = rangeJson.getInt("sr");	//No I18N
	                int curCol = rangeJson.getInt("sc");	//No I18N
	                
	                if (!actionJson.has("isCSE"))
	                {
	                    if (this.isRelativeReference() && cellValue.indexOf("=") == 0)
	                    {
	                        String regexp = "([a-zA-Z]|([a-hA-H][a-zA-Z]?)|([iI][a-vA-V]))(([1-6][0-5][0-5][0-3][0-6])|([1-9][0-9][0-9][0-9])|([1-9][0-9][0-9])|([1-9][0-9])|[1-9])";//No I18N
	                        String[] str = cellValue.split(regexp);
	                        if (str.length != 0)
	                        {
	                            int cnt = 0;
	                            int start, end;
	                            boolean isReplace = true;
	                            StringBuilder replacedString = new StringBuilder();
	                            for (int j = 0; j < str.length; j++)
	                            {
	                                replacedString.append(str[j]);
	                                int pos = str[j].indexOf("\"");
	                                if (pos >= 0 && (str[j].indexOf("\"", pos + 1) < 0))
	                                {
	                                    if (isReplace)
	                                    {
	                                        isReplace = false;
	                                    } else
	                                    {
	                                        isReplace = true;
	                                    }
	                                }
	                                start = cellValue.indexOf(str[j], cnt) + str[j].length();
	                                cnt = start;
	                                if (cnt == cellValue.length())
	                                {
	                                    break;
	                                }
	                                if (j + 1 < str.length)
	                                {
	                                    end = cellValue.indexOf(str[j + 1], cnt);
	                                } else
	                                {
	                                    end = cellValue.length();
	                                }
	                                cnt = end - 1;
	                                if (isReplace)
	                                {
	                                    Integer[] colRow = ClientUtils.getCellPosByLabel((cellValue.substring(start, end)).toUpperCase());
	                                    replacedString.append("R");//No I18N
	                                    if (curRow != colRow[1])
	                                    {
	                                        replacedString.append("[").append(colRow[1] - curRow).append("]");
	                                    }
	                                    replacedString.append("C");//No I18N
	                                    if (curCol != colRow[0])
	                                    {
	                                        replacedString.append("[").append(colRow[0] - curCol).append("]");
	                                    }
	                                } else
	                                {
	                                    replacedString.append(cellValue.substring(start, end));
	                                }
	                            }
	                           	String temp = (replacedString.toString()).replace("\"", "\"\"");
	                            temp = temp.replace(System.lineSeparator(),"\" & Chr(10) & \"");
	                            replacedString = new StringBuilder();
	                            replacedString.append(temp);
	                            
	                            contentBuffer.append(STARTLINE + "ActiveCell.FormulaR1C1=\"");//No I18N
	                            contentBuffer.append(replacedString.toString());
	                            contentBuffer.append("\"");
	                        } else
	                        {
	                        	cellValue = cellValue.replace("\"", "\"\"");
								cellValue = cellValue.replace(System.lineSeparator(),"\" & Chr(10) & \"");
		                    	
								contentBuffer.append(STARTLINE + "ActiveCell.Formula=\"");//No I18N
	                            contentBuffer.append(cellValue);
	                            contentBuffer.append("\"");
	                        }
	                    } else
	                    {
	                    	cellValue = cellValue.replace("\"", "\"\"");
							cellValue = cellValue.replace(System.lineSeparator(),"\" & Chr(10) & \"");
	                    	
							contentBuffer.append(STARTLINE + "ActiveCell.Formula=\"");//No I18N
                            contentBuffer.append(cellValue);
                            contentBuffer.append("\"");
                        }
	                } else
	                {
	                    cellValue = MacroEngineUtils.convertA1toR1C1Format(cellValue.replace("\"", "\"\""), curRow, curCol);
	                    cellValue = cellValue.replace(System.lineSeparator(),"\" & Chr(10) & \"");
	                    
	                    contentBuffer.append(STARTLINE + "Selection.FormulaArray=\"").append(cellValue).append("\"");//No I18N
	                }
	            }
	            break;
	        case ActionConstants.ITALIC: //1
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Font.Italic = ");//No I18N
	            contentBuffer.append(actionJson.getInt("v") == 1 ? "True" : "False");//No I18N
	            break;
	        case ActionConstants.UNDERLINE://2
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Font.Underline = ");//No I18N
	            contentBuffer.append(actionJson.getInt("v") == 1 ? "xlUnderlineStyleSingle" : "xlUnderlineStyleNone");//No I18N
	            break;
	        case ActionConstants.FONTFAMILY://3
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Font.Name = \"");//No I18N
	            contentBuffer.append(actionJson.getString("v"));
	            contentBuffer.append("\"");
	            break;
	        case ActionConstants.FONTSIZE://4
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Font.Size = ");//No I18N
	            contentBuffer.append(actionJson.getString("v").replaceAll("pt", ".0"));//No I18N
	            break;
	        case ActionConstants.RECALCULATE: 
	        		contentBuffer.append(STARTLINE);
	        		contentBuffer.append("Calculate");//No I18N
	        		break;
	        case ActionConstants.VALIGN: //5
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.VerticalAlignment = ");//No I18N
	            String styleValue = actionJson.getString("v");
	            if (styleValue.equals("middle"))
	            {
	                contentBuffer.append("xlCenter");//No I18N
	            } else if (styleValue.equals("top"))
	            {
	                contentBuffer.append("xlTop");//No I18N
	            } else if (styleValue.equals("bottom"))
	            {
	                contentBuffer.append("xlBottom");//No I18N
	            }
	            break;
	        case ActionConstants.HALIGN: //6
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.HorizontalAlignment = ");//No I18N
	            String halign = actionJson.getString("v");
	            if (halign.equals("center"))
	            {
	                contentBuffer.append("xlCenter");//No I18N
	            } else if (halign.equals("end"))
	            {
	                contentBuffer.append("xlRight");//No I18N
	            } else if (halign.equals("start"))
	            {
	                contentBuffer.append("xlLeft");//No I18N
	            }
	            break;
	        case ActionConstants.COLOR: //7
	            String colorValue = actionJson.has(JSONConstants.COLOR) ? actionJson.getJSONObject(JSONConstants.COLOR).getString(JSONConstants.HEXCOLOR) : actionJson.getString("v");
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Interior.Color = ");//No I18N
	            if(colorValue.startsWith("#")) {
	            		colorValue = StyleActionUtil.getRGBColorValue(colorValue);
	            }
	            contentBuffer.append(MacroActionUtil.getRGB(colorValue.substring(colorValue.indexOf("(") + 1, colorValue.indexOf(")"))));
	            break;
	        case ActionConstants.TEXTCOLOR: //8
	            String textColor = actionJson.has(JSONConstants.COLOR) ? actionJson.getJSONObject(JSONConstants.COLOR).getString(JSONConstants.HEXCOLOR) : actionJson.getString("v");
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Font.Color = ");//No I18N
	            if(textColor.startsWith("#")) {
	            		textColor = StyleActionUtil.getRGBColorValue(textColor);
	            }
	            contentBuffer.append(MacroActionUtil.getRGB(textColor.substring(textColor.indexOf("(") + 1, textColor.indexOf(")"))));                
	            break;
	        case ActionConstants.WRAP: //9
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.WrapText = ");//No I18N
	            contentBuffer.append(actionJson.getInt("v") == 1 ? "True" : "False");//No I18N
	            break;
	        //not handled in repeat action
	        case ActionConstants.HEIGHT: //10
	            String value = actionJson.getString("v");//No I18N
	            contentBuffer.append(STARTLINE);
	            if (value.equals("-1"))
	            {
	                contentBuffer.append("Selection.EntireRow.AutoFit");//No I18N
	            } else
	            {
	                contentBuffer.append("Selection.EntireRow.RowHeight =");//No I18N
	                contentBuffer.append(MacroEngineUtils.getExcelRowHeightFromInches(value, true));
	            }
	            break;
	        case ActionConstants.CLEARSTYLES: //31
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.ClearFormats");//No I18N
	            break;
	        case ActionConstants.CLEARCONTENTS: //32
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.ClearContents");//No I18N
	            break;
	        case ActionConstants.CLEARALL: //61
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Clear");//No I18N
	            break;
	            
	        case ActionConstants.FORMAT_TEXT:
	        case ActionConstants.FORMAT_CURRENCY:
			case ActionConstants.FORMAT_ACCOUNTING:
	            contentBuffer.append(STARTLINE);
	            String patternString = Utility.getDecodedString(actionJson.getString(JSONConstants.PATTERN_STRING));
	            patternString = patternString.replaceAll("\"", "\"\"");
	            contentBuffer.append("Selection.Numberformat=\"").append(patternString).append("\"");//No I18N
	            break;
	        case ActionConstants.FORMAT_PERCENT:
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Style=\"Percent\"");//No I18N
	            break;
	        case ActionConstants.FORMAT_DATE:
	            contentBuffer.append(STARTLINE);
	            String formatString = actionJson.getString("fs");
                formatString = formatString.replaceAll("d", "D");//No I18N
                formatString = formatString.replaceAll("y", "Y");//No I18N
                formatString = formatString.replaceAll("NNNN, ", "NNNN");//No I18N
                formatString = formatString.replaceAll("E", "N");//No I18N
                formatString = formatString.replaceAll(":mm", ":MM");//No I18N
                formatString = formatString.replaceAll(":ss", ":SS");//No I18N
                formatString = formatString.replaceAll("a", "AM/PM");//No I18N
                if (formatString.contains("AM/PM"))
                {
                    formatString = formatString.replaceAll("h", "H");//No I18N
                }
                if (actionJson.has("cy") && "JP".equals(actionJson.getString("cy")))
                {
                    formatString = formatString.replaceAll("AAA", "EEE");//No I18N
                }

                contentBuffer.append("Selection.Numberformat = \"");//No I18N
                contentBuffer.append(formatString);
                contentBuffer.append("\"");
	            break;
	        case ActionConstants.BORDERS: //36
	        		String borderStyle = actionJson.has(JSONConstants.BORDER_STYLE) ? actionJson.getString(JSONConstants.BORDER_STYLE) : null;
	        		String Weight = null, LineStyle = null, ColorIndex = null;
	        		if(borderStyle != null) {
	        			String[] borderArray = borderStyle.split(" ");
	            		Weight = borderArray[0].equals("0.0138in") ? "xlThin" : "xlMedium";   	//No I18N         		
	            		LineStyle = MacroEngineUtils.getBorderLineStyle(borderArray[1]);
	            		ColorIndex = borderArray[2];
	            		if(ColorIndex.startsWith("#")) {
	            			ColorIndex = StyleActionUtil.getRGBColorValue(ColorIndex);
	            		}
	            		ColorIndex = Integer.toString(MacroActionUtil.getRGB(ColorIndex.substring(ColorIndex.indexOf("(") + 1, ColorIndex.indexOf(")"))));
	        		}          		
	        		
	            switch (actionJson.getInt("v"))//No I18N
	            {
	                case ActionConstants.BORDER_NONE:
	                    contentBuffer.append(STARTLINE + "Selection.borders(xlEdgeLeft).LineStyle = xlNone");//No I18N
	                    contentBuffer.append(STARTLINE + "Selection.borders(xlEdgeTop).LineStyle = xlNone");//No I18N
	                    contentBuffer.append(STARTLINE + "Selection.borders(xlEdgeBottom).LineStyle = xlNone");//No I18N
	                    contentBuffer.append(STARTLINE + "Selection.borders(xlEdgeRight).LineStyle = xlNone");//No I18N
	                    contentBuffer.append(STARTLINE + "Selection.borders(xlInsideVertical).LineStyle = xlNone");//No I18N
	                    contentBuffer.append(STARTLINE + "Selection.borders(xlInsideHorizontal).LineStyle = xlNone");//No I18N
	                    break;
	                case ActionConstants.BORDER_LEFT:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeLeft)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N 
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_RIGHT:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeRight)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_LEFT_RIGHT:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeLeft)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeRight)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_TOP:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeTop)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_BOTTOM:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_TOP_BOTTOM:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeTop)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_OUTSIDE:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeLeft)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeTop)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeRight)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_VERTICAL:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeLeft)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeRight)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlInsideVertical)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_HORIZONTAL:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeTop)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlInsideHorizontal)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_INSIDE:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlInsideHorizontal)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlInsideVertical)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".borders(xlEdgeLeft).LineStyle = xlNone");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".borders(xlEdgeTop).LineStyle = xlNone");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".borders(xlEdgeBottom).LineStyle = xlNone");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".borders(xlEdgeRight).LineStyle = xlNone");//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_ALL:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeLeft)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeTop)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeRight)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlInsideHorizontal)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlInsideVertical)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_BOTTOM_DOUBLE:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle ="+ (LineStyle != null ? LineStyle : "xlDouble"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_TOP_BOTTOM_DOUBLE:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeTop)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+(LineStyle != null ? LineStyle : "xlDouble"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_TOP_BOTTOM_THICK:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeTop)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlMedium"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_BOTTOM_THICK:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlMedium"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    break;
	                case ActionConstants.BORDER_HORIZONTAL_OUTSIDE:
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeLeft)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeTop)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeRight)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlEdgeBottom)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	                    contentBuffer.append(STARTLINE + "With Selection.borders(xlInsideHorizontal)");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".LineStyle = "+ (LineStyle != null ? LineStyle : "xlContinuous"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Weight = "+ (Weight != null ? Weight : "xlThin"));//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Color = "+ (ColorIndex != null ? ColorIndex : "0"));//No I18N
	                    contentBuffer.append(STARTLINE + "End With");//No I18N
	            }
	            break;
	        case ActionConstants.BOLD: //38
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Font.bold = ");//No I18N
	            contentBuffer.append(actionJson.getInt("v") == 1 ? "True" : "False");//No I18N
	            break;
	
	        case ActionConstants.WIDTH: //41
	            // not handled in repeat action
	            String width = actionJson.getString("v");//No I18N
	            contentBuffer.append(STARTLINE);
	            if (width.equals("-1"))
	            {
	                contentBuffer.append("Selection.EntireColumn.AutoFit");//No I18N
	            } else
	            {
	                contentBuffer.append("Selection.EntireColumn.ColumnWidth = ");//No I18N
	                contentBuffer.append(MacroEngineUtils.getExcelRowHeightFromInches(width, false));
	            }
	            break;
	        case ActionConstants.INSERT_ROW: //42
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.EntireRow.insert");//No I18N
	            break;
	        case ActionConstants.INSERT_COL: //43
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.EntireColumn.insert");//No I18N
	            break;
	        case ActionConstants.DELETE_ROW: //44
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.EntireRow.Delete");//No I18N
	            break;
	        case ActionConstants.DELETE_COL: //45
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.EntireColumn.Delete");//No I18N
	            break;
	        case ActionConstants.INSERT_COPY_ROW:
	        		contentBuffer.append(STARTLINE+"Selection.Copy");		//No I18N
	        		StringBuffer descSelectionBuffer = getSelectionBuffer(actionJson, container, false);
	        		contentBuffer.append(descSelectionBuffer);
				contentBuffer.append(STARTLINE+"Selection.Insert Shift:=xlDown");		//No I18N
				contentBuffer.append(STARTLINE+"ActiveSheet.Paste");			//No I18N
	        		break;
	        case ActionConstants.INSERT_CUT_ROW:	
				contentBuffer.append(STARTLINE+"Selection.Cut");//No I18N
				descSelectionBuffer = getSelectionBuffer(actionJson, container, false);
	            contentBuffer.append(descSelectionBuffer);
				contentBuffer.append(STARTLINE+"Selection.Insert Shift:=xlDown");		//No I18N
				contentBuffer.append(STARTLINE+"ActiveSheet.Paste");						//No I18N
				break;
	        case ActionConstants.INSERT_COPY_COLUMN:
	        		contentBuffer.append(STARTLINE+"Selection.Copy");//No I18N
	        		descSelectionBuffer = getSelectionBuffer(actionJson, container, false);
	        		contentBuffer.append(descSelectionBuffer);
				contentBuffer.append(STARTLINE+"Selection.Insert Shift:=xlToRight");//No I18N
				contentBuffer.append(STARTLINE+"ActiveSheet.Paste");//No I18N
	        		break;
	        case ActionConstants.INSERT_CUT_COLUMN:	        	
				contentBuffer.append(STARTLINE+"Selection.Cut");//No I18N
				descSelectionBuffer = getSelectionBuffer(actionJson, container, false);
	            contentBuffer.append(descSelectionBuffer);
				contentBuffer.append(STARTLINE+"Selection.Insert Shift:=xlToRight");//No I18N
				contentBuffer.append(STARTLINE+"ActiveSheet.Paste");//No I18N
				break;
	        case ActionConstants.HIDE_COLUMNS:
		        	contentBuffer.append(STARTLINE);
	        		contentBuffer.append("Selection.EntireColumn.Hidden = true");	//No I18N
	        		break;
	        case ActionConstants.HIDE_ROWS:
	        		contentBuffer.append(STARTLINE);
	        		contentBuffer.append("Selection.EntireRow.Hidden = true");		//No I18N
	        		break;
	        case ActionConstants.UNHIDE_COLUMNS:
	         	contentBuffer.append(STARTLINE);
	         	contentBuffer.append("Selection.EntireColumn.Hidden = false");	//No I18N
	         	break;
	        case ActionConstants.UNHIDE_ROWS:
		        	contentBuffer.append(STARTLINE);
	        		contentBuffer.append("Selection.EntireRow.Hidden = false");		//No I18N
	        		break;
	        case ActionConstants.FORMAT_DECREMENT_DECIMAL:
	        case ActionConstants.FORMAT_INCREMENT_DECIMAL:
	            patternString = Utility.getDecodedString(actionJson.getString(JSONConstants.PATTERN_STRING));
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Numberformat = \"");//No I18N
	            contentBuffer.append(patternString);
	            contentBuffer.append("\"");
	            break;
	        case ActionConstants.FORMAT_COMMA:
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Style = \"Comma\"");//No I18N
	            break;
	        case ActionConstants.STRIKETHROUGH: //77
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Font.Strikethrough = ");//No I18N
	            contentBuffer.append(actionJson.getInt("v") == 1 ? "True" : "False");//No I18N
	            break;
	        case  ActionConstants.FORMATCELLS_GRID:
	            formatString = Utility.getDecodedString(actionJson.getString("fs"));  //No I18N
	            if(formatString.contains("#ff0000"))
	            {
	                formatString = formatString.replace("#ff0000", "RED");		//No I18N
	            }
	            if(formatString.isEmpty())
	            {                   
	                formatString = "General";   //NO I18N
	            }
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Selection.Numberformat = \"").append(formatString).append("\"");//No I18N
	            break;
			case ActionConstants.NUMBER_FORMAT_APPLY:
				formatString = Utility.getDecodedString(actionJson.getString(JSONConstants.PATTERN_STRING));
				if(formatString.contains("#ff0000"))
				{
					formatString = formatString.replace("#ff0000", "RED");		//No I18N
				}
				if(formatString.isEmpty())
				{
					formatString = "General";   //NO I18N
				}
				contentBuffer.append(STARTLINE);
				contentBuffer.append("Selection.Numberformat = \"").append(formatString).append("\""); // No I18N
				break;
	        case ActionConstants.MERGE_RANGE:
	            contentBuffer.append(STARTLINE + "Selection.Merge");//No I18N
	            break;
	        case ActionConstants.MERGE_SPLIT:
	            contentBuffer.append(STARTLINE + "Selection.UnMerge");//No I18N
	            break;
	        case ActionConstants.MERGE_ACROSS:
	            contentBuffer.append(STARTLINE + "Selection.Merge True");//No I18N
	            break;
	        case ActionConstants.MERGE_DOWN:
	            contentBuffer.append(STARTLINE + "For Each Column In Selection.Columns");//No I18N
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append(SUBLINE);
	            contentBuffer.append("Column.Merge");//No I18N
	            contentBuffer.append(STARTLINE);
	            contentBuffer.append("Next Column");//No I18N
	            break;
	        case ActionConstants.MERGE_AND_CENTER:
	            contentBuffer.append(STARTLINE + "With Selection");//No I18N
	            contentBuffer.append(STARTLINE + SUBLINE + ".HorizontalAlignment = xlCenter");//No I18N
	            contentBuffer.append(STARTLINE + SUBLINE + ".Merge");//No I18N
	            contentBuffer.append(STARTLINE + "End With");//No I18N
	            break;
	        case ActionConstants.COPY_PASTE:
	        case ActionConstants.PASTESPECIAL_FORMATS:
	        case ActionConstants.CUT_PASTE:
	            if (action == ActionConstants.CUT_PASTE)
	            {
	                contentBuffer.append(STARTLINE + "Selection.cut");//No I18N
	            } else
	            {
	                contentBuffer.append(STARTLINE + "Selection.copy");//No I18N
	            }
	            StringBuffer selectionBuffer = new StringBuffer();
	            selectionBuffer = getSelectionBuffer(actionJson, container, false);
	            contentBuffer.append(selectionBuffer);
	            
	            if (action == ActionConstants.PASTESPECIAL_FORMATS)
	            {
	                contentBuffer.append(STARTLINE + "Selection.PasteSpecial Paste:=xlPasteFormats, SkipBlanks:=False, Transpose:=False");//No I18N
	            } else if(actionJson.has(JSONConstants.PASTESPECIAL_TYPE))
	            {
	                String pasteType = MacroActionUtil.getXlPasteType(actionJson.getString(JSONConstants.PASTESPECIAL_TYPE));
	                boolean isSkipBlankCells = actionJson.has(JSONConstants.PASTESPECIAL_SKIPBLANKCELLS) ? Boolean.valueOf(actionJson.getString(JSONConstants.PASTESPECIAL_SKIPBLANKCELLS)): false;
	                boolean isTranspose = actionJson.has(JSONConstants.PASTESPECIAL_TRANSPOSE) ? Boolean.valueOf(actionJson.getString(JSONConstants.PASTESPECIAL_TRANSPOSE)): false;
	
	                contentBuffer.append(STARTLINE + "Selection.PasteSpecial Paste:=");//No I18N                    
	                contentBuffer.append(pasteType).append(", SkipBlanks:=");                    //No I18N
	                contentBuffer.append(isSkipBlankCells ? "True" : "False").append(", Transpose:=");//No I18N
	                contentBuffer.append(isTranspose ? "True" : "False");//No I18N
	                
	            }else 
	            {
	                contentBuffer.append(STARTLINE + "ActiveSheet.Paste");//No I18N
	            }
	            break;
	
	        case ActionConstants.HYPERLINK:
	            String mode = actionJson.getString("m");
	            if (mode.equals("remove"))
	            {
	                contentBuffer.append(STARTLINE + "Selection.Hyperlinks.Delete");//No I18N
	            } else
	            {
	                String url = actionJson.getString("l");
	                url = Utility.getDecodedString(url);
	                if (!url.equals(""))
	                {
	                    String label = Utility.getDecodedString(actionJson.getString("la"));
	                    if (url.startsWith("#"))
	                    {
	                        contentBuffer.append(STARTLINE + "ActiveSheet.Hyperlinks.Add Anchor:=Selection, Address:=\"\", SubAddress:=\"");//No I18N
	                        contentBuffer.append(url.substring(1));
	                    } else
	                    {
	                        contentBuffer.append(STARTLINE + "ActiveSheet.Hyperlinks.Add Anchor:=Selection, Address:=\"");//No I18N
	                        contentBuffer.append(url);
	                    }
	                    contentBuffer.append("\", TextToDisplay:=\"");//No I18N
	                    contentBuffer.append(label);
	                    contentBuffer.append("\"");
	                }
	            }
	            break;
	        case ActionConstants.CELL_COMMENT:
	            mode = actionJson.getString("m");
	            String comment = actionJson.getString("v");
	            comment = Utility.getDecodedString(comment);
	            if (!comment.equals(""))
	            {
	                comment = comment.replaceAll("\"", "\"\"");
	                comment = comment.replaceAll("\n", "\"& Chr(10) &\"");//No I18N
	            }
	            if (mode.equals("add"))
	            {
	                contentBuffer.append(STARTLINE + "ActiveCell.AddComment Text:=\"");//No I18N
	                contentBuffer.append(comment);
	                contentBuffer.append("\"");
	            } else if (mode.equals("edit"))
	            {
	                contentBuffer.append(STARTLINE + "ActiveCell.Comment.Text Text:=\"");//No I18N
	                contentBuffer.append(comment);
	                contentBuffer.append("\"");
	            } else if (mode.equals("remove"))
	            {
	                contentBuffer.append(STARTLINE + "Selection.ClearComments");//No I18N
	            }
	            break;
	        case ActionConstants.NAMEDRANGE_ADD:
	        case ActionConstants.NAMEDRANGE_MODIFY:
	
	            String expName = actionJson.getString("nor");
	            String exp = actionJson.getString("v");
	            try
	            {
	                if (expName != null)
	                {
	                    expName = URLDecoder.decode(expName, "UTF-8");
	                }
	                if (exp != null)
	                {
	                    exp = URLDecoder.decode(exp, "UTF-8");
	
	                    if (exp.length() != 0)
	                    {
	                        if (exp.charAt(0) == '=')
	                        {
	                            exp = exp.substring(1);
	                        }
	                        if (exp.charAt(0) != '\"')
	                        {
	                            exp = '=' + exp;
	                        } else if (exp.startsWith("\"") && exp.endsWith("\""))
	                        {
	                            exp = exp.substring(1, exp.length() - 1);
	                        }
	                        exp = exp.replaceAll("\"", "\"\"");
	                    }
	                }
	            } catch (Exception e)
	            {
	            }
	            if (action == ActionConstants.NAMEDRANGE_ADD)
	            {
	                contentBuffer.append(STARTLINE + "ActiveWorkbook.Names.Add Name:=\"");//No I18N
	            } else
	            {
	                contentBuffer.append(STARTLINE + "ActiveWorkbook.Names.Item(\"");//No I18N
	            }
	            contentBuffer.append(expName);
	            contentBuffer.append("\", RefersTo:=\"");//No I18N
	            contentBuffer.append(exp);
	            contentBuffer.append("\"");
	            break;
	
	        case ActionConstants.NAMEDRANGE_DELETE:
	            exp = actionJson.getString("v");
	            contentBuffer.append(STARTLINE + "ActiveWorkbook.Names.Item(\"");//No I18N
	            contentBuffer.append(exp);
	            contentBuffer.append("\").Delete");//No I18N
	            break;
	
	        case ActionConstants.SORT:
	            boolean containsHeader = actionJson.getBoolean("ich");//No I18N
	            boolean casesensitive = actionJson.getBoolean("ics");//No I18N
	            String sortColumns = actionJson.getString("isc");
	            boolean isSortColumns = false;
	            if ("TRUE".equals(sortColumns))
	            {
	                isSortColumns = true;
	            }
	            contentBuffer.append(STARTLINE + "Selection.Sort ");//No I18N
	            int cnt = 1;
	
	            String[] indexOrder = (actionJson.getString("fo")).split(",");
	            String[] sortOrder = (actionJson.getString("so")).split(",");
	            
	            JSONArrayWrapper rangeArray = actionJson.getJSONArray(JSONConstants.RANGELIST);
	            JSONObjectWrapper rangeJson = rangeArray.getJSONArray(0).getJSONObject(0);
	            
	            int actionStartRow = rangeJson.getInt("sr");	//No I18N
	            int actionStartCol = rangeJson.getInt("sc");	//No I18N
	            int actionEndRow = rangeJson.getInt("er");	//No I18N
	            int actionEndCol = rangeJson.getInt("er");	//No I18N
	            
	            for (int i = 0; i < indexOrder.length; i++)
	            {
	                if (i > 2)
	                {
	                    break;
	                }
	                boolean order = Boolean.parseBoolean(sortOrder[i]);
	                int index = Integer.parseInt(indexOrder[i]);
	
	                if (index != -1)
	                {
	                    if (isSortColumns)
	                    {
	                        if (this.isRelativeReference())
	                        {
	                            contentBuffer.append("Key:=").append(cnt).append(":=ActiveCell");//No I18N
	                            if (index > 0)
	                            {
	                                contentBuffer.append(".Offset(").append(actionStartRow + index - this.getStartRow()).append(",").append(actionStartCol - this.getStartCol()).append(")");//No I18N
	                            }
	                            contentBuffer.append(".Range(\"A1:").append(CellUtil.getCellReference(actionEndCol - actionStartCol, 0)).append("\"),");//No I18N
	                        } else
	                        {
	                            contentBuffer.append("Key").append(cnt).append(":=Range(\"").append(CellUtil.getCellReference(actionStartCol, actionStartRow + index)).append(":").append(CellUtil.getCellReference(actionEndCol, actionStartRow + index)).append("\"),");//No I18N
	                        }
	                    } else
	                    {
	                        if (this.isRelativeReference())
	                        {
	                            contentBuffer.append("Key").append(cnt).append(":=ActiveCell");//No I18N
	                            if (index > 0)
	                            {
	                                contentBuffer.append(".Offset(").append(actionStartRow - this.getStartRow()).append(",").append(actionStartCol + index - this.getStartCol()).append(")");//No I18N
	                            }
	                            contentBuffer.append(".Range(\"A1:").append(CellUtil.getCellReference(0, actionEndRow - actionStartRow)).append("\"),");//No I18N
	                        } else
	                        {
	                            contentBuffer.append("Key").append(cnt).append(":=Range(\"").append(CellUtil.getCellReference(actionStartCol + index, actionStartRow)).append(":").append(CellUtil.getCellReference(actionStartCol + index, actionEndRow)).append("\"),");//No I18N
	                        }
	                    }
	                    if (order)
	                    {
	                        contentBuffer.append("Order").append(cnt).append(":=xlAscending,");//No I18N
	                    } else
	                    {
	                        contentBuffer.append("Order").append(cnt).append(":=xlDescending,");//No I18N
	                    }
	                    cnt++;
	                }
	            }
	            if (containsHeader)
	            {
	                contentBuffer.append("Header:=xlYes,");//No I18N
	            }
	            contentBuffer.append("MatchCase:=").append(casesensitive).append(",");//No I18N
	            if (isSortColumns)
	            {
	                contentBuffer.append("Orientation:=xlLeftToRight");//No I18N
	            } else
	            {
	                contentBuffer.append("Orientation:=xlTopToBottom");//No I18N
	            }
	            break;
	        case ActionConstants.SHEET_RENAME:
	            String currentSheet = actionJson.getString("osn");
	            String newsheetname = actionJson.getString("sn");
	            contentBuffer.append(STARTLINE + "Sheets(\"").append(currentSheet).append("\").Name = \"").append(newsheetname).append("\"");//No I18N
	            this.setActiveSheetName(newsheetname);
	            break;
	        case ActionConstants.SHEET_INSERT: //TODO : get position of the inserted sheet
	            String sheetadd = actionJson.getString("iae");
	            if (sheetadd.equals("true"))
	            {
	                contentBuffer.append(STARTLINE + "Sheets.Add");//No I18N
	            } else
	            {
	                String before = actionJson.getJSONArray(JSONConstants.SHEET_NAMES_LIST).getJSONArray(0).getString(0);
	                contentBuffer.append(STARTLINE + "Sheets(\"" + before + "\").Select");//No I18N
	                contentBuffer.append(STARTLINE + "Sheets.Add Before:=ActiveSheet");//No I18N
	            }
	            this.clearPosition();
	            break;
	        case ActionConstants.SHEET_REMOVE:
	        		String sheetName = actionJson.getJSONArray(JSONConstants.SHEET_NAMES_LIST).getJSONArray(0).getString(0);
	            contentBuffer.append(STARTLINE + "Sheets(\"").append(sheetName).append("\").Select");//No I18N
	            contentBuffer.append(STARTLINE + "Sheets(\"").append(sheetName).append("\").Delete");//No I18N
	            this.clearPosition();
	            break;
	        case ActionConstants.SHEET_MOVE:
	        		currentSheet = actionJson.getJSONArray(JSONConstants.SHEET_NAMES_LIST).getJSONArray(0).getString(0);
	            contentBuffer.append(STARTLINE + "Sheets(\"").append(currentSheet).append("\").Select");//No I18N
	            int position = actionJson.getInt("pos");//No I18N
	            if (position == 0)
	            {
	                contentBuffer.append(STARTLINE + "Sheets(\"").append(currentSheet).append("\").Move Before:=Sheets(1)");//No I18N
	            } else
	            {
	                contentBuffer.append(STARTLINE + "Sheets(\"").append(currentSheet).append("\").Move After:=Sheets(").append(position).append(")");//No I18N
	            }
	            this.clearPosition();
	            break;
	        case ActionConstants.SHEET_DUPLICATE://TODO : Parameters
	        case ActionConstants.SHEET_COPY://TODO : Parameters
	            currentSheet = actionJson.getJSONArray(JSONConstants.SHEET_NAMES_LIST).getJSONArray(0).getString(0);//(String) request.getParameter("sheettocopy");
	            String sheetname = actionJson.getString("nsn");//(String) request.getParameter("sheetname");
	            contentBuffer.append(STARTLINE + "Sheets(\"").append(currentSheet).append("\").Select");//No I18N
	            position = actionJson.getInt(JSONConstants.POSITION);    //Integer.parseInt(paramStr);
	            if (position == 0)
	            {
	                position = 1;
	                contentBuffer.append(STARTLINE + "Sheets(\"").append(currentSheet).append("\").Copy Before:=Sheets(").append(position).append(")");//No I18N
	                contentBuffer.append(STARTLINE + "Sheets(").append(position).append(").Name = \"").append(sheetname).append("\"");//No I18N
	            } else
	            {
	                contentBuffer.append(STARTLINE + "Sheets(\"").append(currentSheet).append("\").Copy After:=Sheets(").append(position).append(")");//No I18N
	                contentBuffer.append(STARTLINE + "Sheets(").append(position + 1).append(").Name = \"").append(sheetname).append("\"");//No I18N
	            }
	            this.clearPosition();
	            break;
			case ActionConstants.INSERT_RECOMMENDED_CHART:
			case ActionConstants.INSERT_RECOMMENDED_CHART_VIA_CACHE:
			case ActionConstants.INSERT_CHART: {
				Workbook workbook = container.getWorkbook(null);
				JSONArrayWrapper sheetList = actionJson.getJSONArray(JSONConstants.SHEETLIST).getJSONArray(0);
				Sheet sheet = workbook.getSheetByAssociatedName(sheetList.getString(0));
				ActionToMacrosConverter converter = ActionToMacrosConverter.getInstance(MacrosType.VBA, action);
				if(converter != null) { converter.convert(sheet, actionJson, contentBuffer);}
				break;
			}
	        case ActionConstants.CHART_NEW:
	        case ActionConstants.CHART_EDIT:
	            String left = actionJson.getString("left");
	            String top = actionJson.getString("top");
	            String cWidth = actionJson.getString("wd");
	            String height = actionJson.getString("ht");
	            String dataRange = actionJson.getString("dataRange");
	            String chartTitle = actionJson.getString("title");
	            String xlabel = actionJson.getString("xAxisTitle");
	            String ylabel = actionJson.has("yAxisTitle") ? actionJson.getString("yAxisTitle") : "";  //NO I18N
	            int legend = actionJson.getInt("lpos");  //NO I18N
	            String GraphType = actionJson.getString("chartType");
	            String seriesIn = actionJson.getString("seriesIn");
	            String dataLabel = actionJson.getString("lf");
	            
	            Object chartTypeObj = ChartImpl.types.mapToValue(ChartUtils.getChartForOldChartName(GraphType));
	            if(chartTypeObj != null){
	                int chartType = (Integer)ChartImpl.types.mapToValue(ChartUtils.getChartForOldChartName(GraphType));
	                String chartName = "";
	                String chartStartline = "";
	                if(action == ActionConstants.CHART_NEW)
	                {
	                    chartStartline = "With ActiveSheet.ChartObjects.Add(Left:=" + left + ", Width:=" + cWidth + ", Top:=" + top + ", Height:=" + height + ")";//No I18N
	                }
	                else
	                {
	                		Workbook workbook = container.getWorkbook(null);
	                		JSONArrayWrapper sheetList = actionJson.getJSONArray(JSONConstants.SHEETLIST).getJSONArray(0);
	                		Sheet sheet = workbook.getSheetByAssociatedName(sheetList.getString(0));
	                    Chart chart = workbook.getChart(sheet.getAssociatedName(), actionJson.getString("chartId"));
	                    chartName = chart.getName();//paramStr;
	                    chartStartline = "With ActiveSheet.ChartObjects(\"" + chartName + "\")";//No I18N
	                }
	                contentBuffer.append(STARTLINE).append(chartStartline);
	                contentBuffer.append(STARTLINE + SUBLINE + ".Chart.SetSourceData Source:=Range(\"").append(dataRange).append("\")");//No I18N
	
	                //series in
	                if (seriesIn.equals("COLS"))
	                {
	                    contentBuffer.append(", PlotBy:=xlColumns");//No I18N
	                } else if (seriesIn.equals("ROWS"))
	                {
	                    contentBuffer.append(", PlotBy:=xlRows");//No I18N
	                }
	
	                //datalabel
	                if (dataLabel.equals("V"))
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.ApplyDataLabels Type:=xlDataLabelsShowValue");//No I18N
	                } else if (dataLabel.equals("P"))
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.ApplyDataLabels Type:=xlDataLabelsShowPercent");//No I18N
	                } else if (dataLabel.equals("L"))
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.ApplyDataLabels Type:=xlDataLabelsShowLabel");//No I18N
	                } else if (dataLabel.equals("N"))
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.ApplyDataLabels Type:=xlDataLabelsShowNone");//No I18N
	                } else if (dataLabel.equals("L_P"))
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.ApplyDataLabels Type:=xlDataLabelsShowLabelAndPercent");//No I18N
	                } else if (dataLabel.equals("B"))
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.ApplyDataLabels Type:=xlDataLabelsShowBubbleSizes");//No I18N
	                }
	
	                contentBuffer.append(STARTLINE + SUBLINE + ".Chart.ChartType =").append(MacroActionUtil.getExcelChartType(chartType));// xlColumnClustered//No I18N
	                if (!chartTitle.equals(""))
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.HasTitle = True");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.ChartTitle.Text = \"").append(chartTitle).append("\"");//No I18N
	                }
	                if (!xlabel.equals(""))
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.Axes(xlCategory, xlPrimary).HasTitle = True");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.Axes(xlCategory, xlPrimary).AxisTitle.Text = \"").append(xlabel).append("\"");//No I18N
	                }
	                if (!ylabel.equals(""))
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.Axes(xlValue, xlPrimary).HasTitle = True");//No I18N
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.Axes(xlValue, xlPrimary).AxisTitle.Text = \"").append(ylabel).append("\"");//No I18N
	                }
	                if (legend != 0)
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.HasLegend = True");//No I18N
	                } else
	                {
	                    contentBuffer.append(STARTLINE + SUBLINE + ".Chart.HasLegend = False");//No I18N
	                }
	                if(("XYLINE".equals(GraphType)  || "SPLINE".equals(GraphType) || "STEPCHART".equals(GraphType)))
	                {
	                    if(actionJson.getBoolean("isMarker"))
	                    {
	                        contentBuffer.append(STARTLINE + SUBLINE + ".Chart.HasMarker = True");//No I18N
	                    }
	                    else
	                    {
	                        contentBuffer.append(STARTLINE + SUBLINE + ".Chart.HasMarker = False");//No I18N
	                    }
	                }
	                contentBuffer.append(STARTLINE + "End With");//No I18N                               
	            }
	            break;
	        case ActionConstants.CHART_QUICK_EDIT:  
	            int subAction = actionJson.getInt("subAction");  //NO I18N
	            value = actionJson.getString("value");
	            Workbook workbook = container.getWorkbook(null);
	        		JSONArrayWrapper sheetList = actionJson.getJSONArray(JSONConstants.SHEETLIST).getJSONArray(0);
	        		Sheet sheet = workbook.getSheetByAssociatedName(sheetList.getString(0));
	            Chart chart = workbook.getChart(sheet.getAssociatedName(), actionJson.getString("chartId"));
	            String chartName = chart.getName();//paramStr;
	            switch(subAction)
	            {
	                case ChartConstants.CHART_TYPE: 
	                    chartTypeObj = ChartImpl.types.mapToValue(ChartUtils.getChartForOldChartName(value));
	                    if(chartTypeObj != null)
	                    {
	                        int chartType = (Integer) chartTypeObj;
	                        contentBuffer.append(STARTLINE + "With ActiveSheet.ChartObjects(\"" + chartName + "\")");//No I18N
	                        contentBuffer.append(STARTLINE + SUBLINE + ".Chart.ChartType =").append(MacroActionUtil.getExcelChartType(chartType));// xlColumnClustered//No I18N
	                        contentBuffer.append(STARTLINE + "End With");//No I18N   
	                    }
	                    break;
	                case ChartConstants.LEGEND:
	                    contentBuffer.append(STARTLINE + "With ActiveSheet.ChartObjects(\"" + chartName + "\")");//No I18N
	                    if("0".equals(value))
	                    {
	                        contentBuffer.append(STARTLINE + SUBLINE + ".Chart.HasLegend = False");//No I18N
	                    }
	                    else
	                    {
	                        contentBuffer.append(STARTLINE + SUBLINE + ".Chart.HasLegend = True");//No I18N                            
	                    }
	                    contentBuffer.append(STARTLINE + "End With");//No I18N   
	                    break;
	            }
	            break;
	        case ActionConstants.CHART_MOVE:
	            
	            left = actionJson.getString("left");
	            top = actionJson.getString("top");
	            workbook = container.getWorkbook(null);
	        		sheetList = actionJson.getJSONArray(JSONConstants.SHEETLIST).getJSONArray(0);
	        		sheet = workbook.getSheetByAssociatedName(sheetList.getString(0));
	            chart = workbook.getChart(sheet.getAssociatedName(), actionJson.getString("chartId"));
	            chartName = chart.getName();//paramStr;
	            contentBuffer.append(STARTLINE + "With ActiveSheet.ChartObjects(\"").append(chartName).append("\")");//No I18N
	            contentBuffer.append(STARTLINE + SUBLINE + ".Left = ").append(left);//No I18N
	            contentBuffer.append(STARTLINE + SUBLINE + ".Top = ").append(top);//No I18N
	            contentBuffer.append(STARTLINE + "End With");//No I18N
	            
	            break;
	            
	        case ActionConstants.CHART_RESIZE:
	            
	            left = actionJson.getString("left");
	            top = actionJson.getString("top");
	            cWidth = actionJson.getString("wd");
	            height = actionJson.getString("ht");
	            workbook = container.getWorkbook(null);
	        		sheetList = actionJson.getJSONArray(JSONConstants.SHEETLIST).getJSONArray(0);
	        		sheet = workbook.getSheetByAssociatedName(sheetList.getString(0));
	            chart = workbook.getChart(sheet.getAssociatedName(), actionJson.getString("chartId"));
	            chartName = chart.getName();//paramStr;
	            contentBuffer.append(STARTLINE + "With ActiveSheet.ChartObjects(\"").append(chartName).append("\")");//No I18N
	            contentBuffer.append(STARTLINE + SUBLINE + ".Width = ").append(cWidth);//No I18N
	            contentBuffer.append(STARTLINE + SUBLINE + ".Height = ").append(height);//No I18N
	            contentBuffer.append(STARTLINE + SUBLINE + ".Left = ").append(left);//No I18N
	            contentBuffer.append(STARTLINE + SUBLINE + ".Top = ").append(top);//No I18N
	            contentBuffer.append(STARTLINE + "End With");//No I18N
	            
	            break;
	        case ActionConstants.CHART_DELETE:
	            
		        	workbook = container.getWorkbook(null);
	        		sheetList = actionJson.getJSONArray(JSONConstants.SHEETLIST).getJSONArray(0);
	        		sheet = workbook.getSheetByAssociatedName(sheetList.getString(0));
	            chart = workbook.getChart(sheet.getAssociatedName(), actionJson.getString("chartId"));
	            chartName = chart.getName();//paramStr;
	            contentBuffer.append(STARTLINE + "ActiveSheet.ChartObjects(\"").append(chartName).append("\").Delete");//No I18N	            
	            break;
	    
		}
		return contentBuffer;
	}
	
	public String getContent()
    {     
        StringBuilder content = new StringBuilder();
        StringBuffer header 		= getMacroHeader(this.macroName, this.macroComments);
        content.append(header);
        Iterator<StringBuffer> itr = this.contentList.iterator();
        while (itr.hasNext())
        {
            content.append((StringBuffer) itr.next());
        }
        StringBuffer footer = getMacroFooter();
        content.append(footer);
        return content.toString();
    }
	
	private void pushContent(StringBuffer content, ArrayList<StringBuffer> list)
    {
		list.add(content);
    }
	
	private StringBuffer popContent(ArrayList<StringBuffer> list)
    {
        int index = list.size() - 1;
        if (index >= 1)
        {
            return (StringBuffer) list.remove(index);
        }
        return null;
    }    
    
    private void undo(JSONObjectWrapper param)
    {
        StringBuffer undoContent = popContent(this.contentList);
        if (undoContent != null)
        {
        		this.pushContent(undoContent, this.undoList);
        }
    }   
    
    private void redo(JSONObjectWrapper param)
    {
        StringBuffer redoContent = popContent(this.undoList);
        if (redoContent != null)
        {
        		this.pushContent(redoContent, this.contentList);
        }
    }
	
	private void clearPosition()
    {
		this.startRow = 0;
		this.startCol = 0;
		this.endRow = 0;
		this.endCol = 0;
		this.row = 0;
		this.column = 0;
    }		
	
	public void record(JSONObjectWrapper actionJson, WorkbookContainer container) throws Exception {
		int action = actionJson.getInt("a");		//No I18N
		StringBuffer actionBuffer = new StringBuffer();
        if(!UNSUPPORTED_ACTIONS.contains(action) && (action != ActionConstants.MACRO_START_RECORDING)) {   
        		
        		if(action == ActionConstants.UNDO) {
        			this.undo(actionJson);
        			return;
        		}
        		
        		if(action == ActionConstants.REDO) {
        			this.redo(actionJson);
        			return;
        		}             		
        		
        		StringBuffer selectionBuffer = getSelectionBuffer(actionJson, container, true);
        		actionBuffer.append(selectionBuffer);
        		
        		StringBuffer contentBuffer = getContentBuffer(actionJson, container);
        		actionBuffer.append(contentBuffer);
        		
        		this.pushContent(actionBuffer, this.contentList);
        		resetSelections(this, actionJson);
        }        
	}
}
