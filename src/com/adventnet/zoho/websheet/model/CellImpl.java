//$Id$

/*
 * CellImpl.java
 *
 * Created on September 10, 2007, 3:46 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */


package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.ReEvaluate.CellReEvaluateObject;
import com.adventnet.zoho.websheet.model.celledithistory.CellEditHistoryBean;
import com.adventnet.zoho.websheet.model.ext.ZSString;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.ext.functions.ImportRange;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.ext.standard.ZSFormatExtractor;
import com.adventnet.zoho.websheet.model.ext.standard.ZSPrintVisitor;
import com.adventnet.zoho.websheet.model.ext.standard.ZSRefEvaluator;
import com.adventnet.zoho.websheet.model.style.*;
import com.adventnet.zoho.websheet.model.style.datastyle.CurrencyChar;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants;
import com.adventnet.zoho.websheet.model.style.datastyle.PatternComponent;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSFontScheme;
import com.adventnet.zoho.websheet.model.util.*;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.functions.Round;
import com.zoho.sheet.authorization.AppUtil;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.chart.ChartUtils;

import javax.swing.*;
import java.awt.*;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.Format;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class CellImpl implements Cell //, Comparable<Cell>
{
    public static final Logger LOGGER = Logger.getLogger(CellImpl.class.getName());

    private Row row;
    private Column column;

//    private int rowSpan = 1;
//    private int colSpan = 1;
//    private transient Cell mergeParentCell = null;

    private int colsRepeated = 1;

    private Annotation annotation;
    //private String tagName;
    //private java.util.Currency currency;
    // private ZSPattern pattern;
    private ZSPattern derivedPattern;

    private List<Attachment> attachments = null;

    //private String content;
    private ContentObject contentObject = ContentObject.EMPTY_CONTENTOBJECT;
    private Boolean contentValid=null;

    private String styleName;

    private String currencyCode;
    private Map<Integer, CellPicklist> cellPicklists;

    //private List<Link> links;
    private String link;

    private static class OffsetIndices {
        private static final OffsetIndices OFFSET_INDICES = new OffsetIndices(-1, -1);
        private final int rowOffset;
        private final int colOffset;

        private OffsetIndices(Cell parentCell, Cell nextCell) {
            this.rowOffset = parentCell.getRowIndex() - nextCell.getRowIndex();
            this.colOffset = parentCell.getColumnIndex() - nextCell.getColumnIndex();
        }

        private OffsetIndices(int rowOffset, int colOffset){
            this.rowOffset = rowOffset;
            this.colOffset = colOffset;
        }
        private boolean isThisParent(Cell cell, Cell nextCell) {
            return this.rowOffset == cell.getRowIndex() - nextCell.getRowIndex() && this.colOffset == cell.getColumnIndex() - nextCell.getColumnIndex();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o){
                return true;
            }
            if (o == null || getClass() != o.getClass()){
                return false;
            }
            OffsetIndices that = (OffsetIndices) o;
            return rowOffset == that.rowOffset &&
                colOffset == that.colOffset;
        }

        @Override
        public int hashCode() {

            return Objects.hash(rowOffset, colOffset);
        }
    }

    //For AutoArray
    private OffsetIndices autoArraySuccessParent = null;
    private List<OffsetIndices> autoArrayErrorParents = null;
    //    private List<Cell> autoArrayErrorParent = null;
    private int autoArrayRowSpan = 1;
    private int autoArrayColSpan = 1;
    private OffsetIndices autoArrayError = null;

//    private int autoArrayErrorRowOffset = -1;
//    private int autoArrayErrorColOffset = -1;

    // If the cell is a Merge Range's parent
    private boolean isMergeParent = false;

    /** The cell's dependents */
    private Set<Cell> dependents;

    /** The cell's precedents */
    // private SortedSet<Cell> precedents = new TreeSet<Cell>();
    // precedents may contain Range or CellReference objects
    //private Set precedents;

    private ValueInterface value = SimpleValue.getInstance(Value.EMPTY_VALUE);//Value.EMPTY_VALUE; // A static value

    //The expression transposed to "this" cell
    //private String xmlFormula;
    //private String localizedFormula;

    private transient CellReEvaluateObject reEvaluateObject;

    boolean isMarked = false;
    // This us marked true, if the cell is formula and
    // signed as formula cell with sheet
    // marking of true/false done in sign/unsign methods
    // in Sheet
    private int isSigned = 0;  /* 0 - NORMAL, 1 - FORMULACELL, 10 - FIELDCELL, 11 - (FORMULA + FIELD)CELL */

    private int formulaLevel = 0;

    //////////////////
    private List<DrawControl> drawControlList = null;

    private String contentValidationName;

    private ConditionalStyleCellObject conditionalStyleCellObject;

    private long valueTimestamp=-1;

    private Dimention cellHeight;

    private Dimention cellWidth;

    private Integer imageID = null;

    private int ignoreError = 0;
    private boolean ignoreTypeMismatchError = false;

    private boolean isArrayValue = false;

    private boolean isEdited = true;

    private int lazyEvalDepth = 0;
    private ImportRange.SYNC_STATE importrangeSyncState = ImportRange.SYNC_STATE.IN_SYNC;

    // this must be used only from the Parser.
    public CellImpl()
    {

    }

    public CellImpl(Row row, Column column)
    {
        this.row = row;
        this.column = column;

    }

    public static class Dimention
    {
        private final int dimention;
        private final Timestamp timestamp;
        private final boolean hasDefaultFont;
        private final boolean hasThemeFont;

        public Dimention(int dimention, Timestamp timestamp)
        {
            this.dimention = dimention;
            this.timestamp = timestamp;
            this.hasDefaultFont = false;
            this.hasThemeFont = false;
        }

        public Dimention(int dimention, Timestamp timestamp, boolean hasDefaultFont, boolean hasThemeFont)
        {
            this.dimention = dimention;
            this.timestamp = timestamp;
            this.hasDefaultFont = hasDefaultFont;
            this.hasThemeFont = hasThemeFont;
        }

        public int getDimention()
        {
            return dimention;
        }

        public Timestamp getTimestamp()
        {
            return timestamp;
        }

        public boolean isHasDefaultFont()
        {
            return hasDefaultFont;
        }

        public boolean isHasThemeFont()
        {
            return hasThemeFont;
        }
    }



    public ConditionalStyleCellObject getConditionalStyleCellObject()
    {
        return conditionalStyleCellObject;
    }

    public void setConditionalStyleCellObject(ConditionalStyleCellObject conditionalStyleCellObject)
    {
        this.conditionalStyleCellObject = conditionalStyleCellObject;
    }

    @Override
    public List<DrawControl> getDrawControlList()
    {
        return drawControlList;
    }

    @Override
    public void addDrawControl(DrawControl drawControl)
    {
        if(drawControlList == null)
        {
            drawControlList = new ArrayList<>();
        }
        drawControlList.add(drawControl);
    }


    @Override
    public Row getRow()
    {
        return row;
    }


    @Override
    public void setRow(Row row)
    {
        this.row = row;
    }

    @Override
    public Column getColumn()
    {
        return column;
    }


    @Override
    public void setColumn(Column column)
    {
        this.column = column;
    }


    @Override
    public int getColumnIndex()
    {
        return column.getColumnIndex();
    }


    public void setCellPicklists(Map<Integer,CellPicklist> cellPicklists) {
        this.cellPicklists = cellPicklists;
    }

    public boolean addCellPicklist(CellPicklist cellPicklist) {
        if(this.cellPicklists == null) {
            this.cellPicklists = new HashMap<>();
        }
        if(this.cellPicklists.containsKey(cellPicklist.getPicklist().getId())) {
            if(this.cellPicklists.get(cellPicklist.getPicklist().getId()).getItemID() != null) {
                this.cellPicklists.put(cellPicklist.getPicklist().getId(), cellPicklist);
                return false;
            }
        }
        this.setUsedIndex();
        this.cellPicklists.put(cellPicklist.getPicklist().getId(), cellPicklist);
        return true;
    }

    public CellPicklist getCellPicklist(int picklistID) {
        if(this.cellPicklists == null) {
            return null;
        }
        return this.cellPicklists.get(picklistID);
    }

    public Collection<CellPicklist> getCellPicklists() {
        return this.cellPicklists != null ? Collections.unmodifiableCollection(this.cellPicklists.values()) : null;
    }

    public void removeCellPicklist(int id) {
        if(this.cellPicklists == null) {
            return;
        }
        this.cellPicklists.remove(id);
    }

    @Override
    public int getRowIndex()
    {
        return row.getRowIndex();
    }

    @Override
    public String getCellRef()
    {
        return CellUtil.getCellReference(column.getColumnIndex(), row.getRowIndex());
    }

    public String getContentFromWriter(){
        Value val = this.getValue();
        if(val != null && val.getType() == Type.ERROR)
        {
            return ((Throwable)val.getValue()).getMessage();
        }
        return this.contentObject.getContent();
    }

    @Override
    public String getContent()
    {
        return this.getContentObject().getContent();
    }

    public boolean hasFieldNode() {
        if(this.getValue().getRawValue() instanceof ZSString) {
            List<RichStringProperties> richStrings = ((ZSString)this.getValue().getRawValue()).getProperties();
            if(richStrings != null) {
                for(RichStringProperties properties: richStrings) {
                    if(properties.getField() != null) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
    public String getContentWithFieldNode() {
        if(!this.hasFieldNode()) {
            return this.getContent();
        }

        StringBuilder content = new StringBuilder(this.getContent() != null ? this.getContent() : "");

        int offset = 0;
        List<RichStringProperties> richStrings = ((ZSString)this.getValue().getRawValue()).getProperties();
        for(RichStringProperties properties: richStrings) {
            if(properties.getField() != null) {
                content.insert(properties.getStartIndex() + offset, EngineConstants.FIELD_NODE_PREFIX + properties.getField().getName() + EngineConstants.FIELD_NODE_SUFFIX);
                offset += properties.getField().getNameLength();
            }
        }

        return content.toString();
    }

    @Override
    public int getPatternRepeatIndex() {
        return this.getContentObject().getRepeatIndex();
    }

    public String getPatternRepeatChar() {
        return this.getContentObject().getRepeatChar();
    }

    public Map<Integer, Character> getInvisibleCharacters() {
        return this.getContentObject().getInvisibleCharacters();
    }

    @Override
    public String getContentColor()
    {
        return this.getContentObject().getColor();
    }

    @Override
    public Type getContentType()
    {
        return this.getContentObject().getType();
    }

    private ContentObject getContentObject()
    {
        if(this.getValue() instanceof PicklistValue)
        {
            String value = this.getValue().getValueString(null);
            setContentObj(new ContentObject(value, null, this.getValue().getType(), -1, null, null, null, null));
            return this.contentObject;
        }
        else
        {
            ZSPattern aPattern = this.getPattern(2);
                // Fetch any pattern set in DEFAULT style.
            if((this.value instanceof ExpressionValue && ((ExpressionValue) this.value).hasUserSpecificResult())
                    || this.contentObject == null || !this.contentObject.isRelevant(aPattern, this.getValue()))
            {

                if(aPattern == null){
                    aPattern = this.getType().isDateType() ? this.getSheet().getWorkbook().getDefaultDatePattern() : DataStyleConstants.EMPTY_PATTERN;

                }
                ContentObject contentGenerated = aPattern.formatValue(this.getRow().getSheet().getWorkbook(), this.getValue());
                // Recreate content object with cell's pattern and value. Because, the above formatValue method can chnage pattern to default pattern in case this is EMPTY_PATTERN and also, would have added the final conditional pattern. That would lead to isRelevant returning false always and we will endup creating content object everytime.
                contentGenerated = new ContentObject(contentGenerated.getContent(), contentGenerated.getColor(), contentGenerated.getType(), contentGenerated.getRepeatIndex(), contentGenerated.getRepeatChar(), contentGenerated.getInvisibleCharacters(), aPattern, this.getValue());
                if(this.value instanceof ExpressionValue
                        && ((ExpressionValue) this.value).hasUserSpecificResult()) {
                    return contentGenerated;
                }
                setContentObj(contentGenerated);
            }
        }
        return this.contentObject;
    }

    private void setContentObj(ContentObject contentObject)
    {
        this.contentObject = contentObject == null ? ContentObject.EMPTY_CONTENTOBJECT : contentObject;
        setCellHeight(null);
        setCellWidth(null);
    }

    @Override
    public Boolean isContentValid()
    {
        if( contentValid == null)
        {
            validateContent();
        }
        return contentValid;
    }

    @Override
    public void setContentValid(Boolean contentValid)
    {
        this.contentValid=contentValid;
    }

    @Override
    public String getCurrencyCode()
    {
        try
        {
            if (this.getContentType()== Type.CURRENCY)
            {
                if (this.currencyCode == null)
                {
                    Locale currencyLocale = null;

                    ZSPattern tempPattern = this.getPattern(2, true);
                    if(tempPattern != null && tempPattern.getType() != Type.UNDEFINED)
                    {
                        for(PatternComponent component : tempPattern.getComponentList()) {
                            if(component instanceof CurrencyChar) {
                                currencyLocale = ((CurrencyChar) component).getCurrencyLocale();
                                break;
                            }
                        }

                        if(currencyLocale != null) {
                            this.setCurrencyCode(LocaleUtil.getCurrencyInstance(currencyLocale).getCurrencyCode());
                        }
                        else {
                            this.setCurrencyCode(null);
                        }
                    }
                }

                return currencyCode;
            }
        }catch(Exception e)
        {
            LOGGER.log(Level.INFO, "Exception in getCurrencyCode ::: ", e);
        }
        return null;
    }

    public void setCurrencyCode(String currencyCode)
    {
        this.currencyCode = currencyCode;
    }

    @Override
    public int getArrayColSpan()
    {
        return isSignedArrayFormula() ? autoArrayColSpan : 0;
    }

    @Override
    public int getArrayRowSpan()
    {
        return isSignedArrayFormula() ? autoArrayRowSpan : 0;
    }

    @Override
    public void setArraySpan(int arrayRowSize, int arrayColSize)
    {

        int prevRowSpan = 1;
        int prevColSpan = 1;

        if(!isAutoArrayError()){
            prevRowSpan = this.getAutoArrayRowSpan();
            prevColSpan = this.getAutoArrayColSpan();
        }

        //Clearing the Expanded Rows
        for (int r = prevRowSpan; r < arrayRowSize; r++)
        {
            for (int c = 0; c < arrayColSize; c++)
            {
                Cell nextCell = this.getSheet().getCell(this.getRowIndex() + r, this.getColumnIndex() + c);
                nextCell.setValue(Value.EMPTY_VALUE);
            }
        }
        //Clearing the Expanded Cols
        for (int c = prevColSpan; c < arrayColSize; c++)
        {
            for (int r = 0; r < prevRowSpan; r++)
            {
                Cell nextCell = this.getSheet().getCell(this.getRowIndex() + r, this.getColumnIndex() + c);
                nextCell.setValue(Value.EMPTY_VALUE);
            }
        }

        getSheet().updateArrayCell(this, arrayRowSize, arrayColSize);
    }

    public void setAutoArraySpanFromParser(int autoArrayRowSpan, int autoArrayColSpan, int autoArrayErrorRowOffset, int autoArrayErrorColOffset){
        this.autoArrayRowSpan = autoArrayRowSpan;
        this.autoArrayColSpan = autoArrayColSpan;
        if(autoArrayErrorRowOffset == -1 && autoArrayErrorColOffset == -1) {
            this.autoArrayError = OffsetIndices.OFFSET_INDICES;
        } else {
            this.autoArrayError = new OffsetIndices(autoArrayErrorRowOffset, autoArrayErrorColOffset);
        }
    }

    public void updateAutoArrayParentCellFromParser(){
        if(!isAutoArrayError())
        {
            Sheet sheet = this.getSheet();
            int rowIndex = this.getRowIndex();
            int colIndex = this.getColumnIndex();
            for(int r = 0; r < autoArrayRowSpan; r++){
                Row row = sheet.getRow(rowIndex + r);
                if(r != 0 && row.getRowsRepeated() > (autoArrayRowSpan-r)) {
                    sheet.getRow(rowIndex +(autoArrayRowSpan-r));
                }
                if(row != null) {
                    for (int c = 0; c < autoArrayColSpan; c++) {
                        if (r != 0 || c != 0) {
                            Cell nextCell = sheet.getCell(rowIndex + r, colIndex + c);
                            if(nextCell.getColsRepeated() > (autoArrayColSpan-c)) {
                                sheet.getCell(rowIndex + r, colIndex + (autoArrayColSpan-c));
                            }
                            ((CellImpl) nextCell).isArrayValue = true;
                            ((CellImpl) nextCell).autoArraySuccessParent = new OffsetIndices(this, nextCell);
                            c += (nextCell.getColsRepeated() - 1);
                        }
                    }
                    r += (row.getRowsRepeated() - 1);
                }
            }
        }else {
            Cell errorCell = this.getAutoArrayErrorCell();
            if(errorCell != null) {
                ((CellImpl) errorCell).addAutoArrayErrorParents(this);
            }
        }

    }

    protected void setAutoArraySpan(int autoArrayRowSize, int autoArrayColSize, boolean isResetRepeatedCount) {
        if(isResetRepeatedCount) {
            resetRepeatedCounts();
        }

        Sheet sheet = this.getRow().getSheet();
        int rowIndex = this.getRowIndex();
        int colIndex = this.getColumnIndex();

        boolean isClearParents = true;
        if(isAutoArrayError()) {
            Cell errorChild = this.getAutoArrayErrorCell();

            if(errorChild != null){
                if(errorChild.getRowIndex() - rowIndex < autoArrayRowSize && errorChild.getColumnIndex() - colIndex < autoArrayColSize
                    && (((CellImpl) errorChild).getAutoArraySuccessParent() != null || errorChild.isFormula() || !Value.EMPTY_VALUE.equals(errorChild.getValue()))){
                    isClearParents = false;
                }
                ((CellImpl) errorChild).removeAutoArrayParent(this);
            }
            else if(this.getRowIndex() + getAutoArrayRowSpan() > Utility.MAXNUMOFROWS || this.getColumnIndex() + getAutoArrayColSpan() > Utility.MAXNUMOFCOLS)
            {
                isClearParents = false;
            }
        }

        if(isClearParents)
        {
            int rowsToItr = this.getAutoArrayRowSpan();
            int colsToItr = this.getAutoArrayColSpan();
            if(!(this.getRowIndex() + autoArrayRowSize > Utility.MAXNUMOFROWS || this.getColumnIndex() + autoArrayColSize > Utility.MAXNUMOFCOLS) ) {
                rowsToItr = Math.max(this.getAutoArrayRowSpan(), autoArrayRowSize);
                colsToItr = Math.max(this.getAutoArrayColSpan(), autoArrayColSize);
            }

            Cell errorCell = null;
            IterateArray :
            {
                for (int r = 0; r < rowsToItr; r++){
                    Row nextRow = sheet.getRowReadOnly(rowIndex + r);
                    if(nextRow != null) {
                        for (int c = 0; c < colsToItr; c++) {
                            if (r > this.getAutoArrayRowSpan() && c > this.getAutoArrayColSpan() && errorCell != null) {
                                break IterateArray;
                            }

                            if (r != 0 || c != 0) {
                                Cell nextCell = sheet.getCell(rowIndex + r, colIndex + c);
                                int colsRepeated = 1;

                                if (nextCell != null) {
                                    if ((((CellImpl) nextCell).isArrayValue ? (((CellImpl) nextCell).autoArraySuccessParent != null && ((CellImpl) nextCell).autoArraySuccessParent.isThisParent(this, nextCell)) : !nextCell.isFormula() && Value.EMPTY_VALUE.equals(((CellImpl) nextCell).value.getValue()))) {
                                        ((CellImpl) nextCell).internalSetValue(Value.EMPTY_VALUE, false);
                                    } else if ((r < autoArrayRowSize && c < autoArrayColSize) && errorCell == null) {
                                        errorCell = nextCell;
                                    }

                                    ((CellImpl) nextCell).removeAutoArrayParent(this);
                                }
                                c += (colsRepeated - 1);
                            }
                        }

                        r += nextRow.getRowsRepeated() - 1;
                    }
                }
            }

            if(errorCell != null)
            {
                this.autoArrayError = new OffsetIndices(errorCell, this);
            }
            else
            {
                this.autoArrayError = OffsetIndices.OFFSET_INDICES;
            }
        }

        this.autoArrayRowSpan = autoArrayRowSize;
        this.autoArrayColSpan = autoArrayColSize;

        sheet.updateAutoArrayCell(this);
    }

    public boolean isArrayCell()
    {
        return this.isSignedArrayFormula() || this.autoArraySuccessParent != null && ((CellImpl)(this.getSheet().getCell(this.getRowIndex() + this.autoArraySuccessParent.rowOffset, this.getColumnIndex() + this.autoArraySuccessParent.colOffset))).isSignedArrayFormula();
    }

    /**
     * Do not call it from anywhere other than 'merge' bound methods in Sheet
     * @return
     */
    public boolean isMergeParent(){
        return this.isMergeParent;
    }

    /**
     * Do not call it from anywhere other than 'merge' bound methods in Sheet
     * @param isMergeParent
     */
    public void setIsMergeParent(boolean isMergeParent){
        this.isMergeParent = isMergeParent;
    }

    public void setLinkFromParser(String linkUrl)
    {
        this.link = linkUrl;
    }

    @Override
    public void setLink(String linkUrl)
    {
        resetRepeatedCounts();
        this.link = linkUrl;
    }

    @Override
    public String getLink()
    {
        return this.link;
    }

    @Override
    public List<RichStringProperties> getLinks()
    {
        List<RichStringProperties> links = new ArrayList();
        if(this.link != null && !this.link.isEmpty())
        {
            try
            {
                RichStringProperties tempLink = new RichStringProperties(null, this.link, this.getContent(), 0);
                links.add(tempLink);
            }catch(Exception e)
            {
                LOGGER.log(Level.INFO, "Unable to create Content and regenerate links >>> ", e);
            }
        }
        else if(this.getValue().getRawValue() instanceof ZSString)
        {
            ZSString richString = (ZSString)this.getValue().getRawValue();
            for(RichStringProperties link : richString.getProperties())
            {
                if(link.getUrl() != null)
                {
                    links.add(link);
                }
            }
        }
        return links;
    }

    /**
     * Getter for property styleName.
     * @return Value of property styleName.
     */
    @Override
    public java.lang.String getStyleName()
    {
        return styleName;
    }

    /**
     * Setter for property styleName.
     * @param styleName New value of property styleName.
     */

    @Override
    public void setStyleName(java.lang.String styleName)
    {
        setStyleName(styleName, true);
    }

    public void setStyleName(java.lang.String styleName, boolean resetRepeatedCounts)
    {
        setStyleName(styleName, resetRepeatedCounts, false);
    }

    public void setStyleName(java.lang.String styleName, boolean resetRepeatedCounts, boolean isForSetPattern)
    {
        if(resetRepeatedCounts)
        {
            resetRepeatedCounts();
        }

        if(isForSetPattern)
        {
            derivedPattern = DataStyleConstants.EMPTY_PATTERN;
        }

        this.styleName = styleName;
        setCellHeight(null);
        setCellWidth(null);
        this.resetMaxWidthHeightIndex();
    }

    public void setStyleNameFromParser(java.lang.String styleName)
    {
        this.styleName = styleName;
    }

    /**
     * Getter for property colsRepeated.
     * @return Value of property colsRepeated.
     */
    @Override
    public int getColsRepeated()
    {
        return this.colsRepeated;
    }

    /**
     * Setter for property colsRepeated.
     * @param colsRepeated New value of property colsRepeated.
     */
    @Override
    public void setColsRepeated(int colsRepeated)
    {
        // the value should never be less than 1
        this.colsRepeated = Math.max(colsRepeated, 1);
    }

    //// Formula
    @Override
    public boolean isFormula()
    {
        return (value instanceof ExpressionValue);
    }

    public void signAsFormula() {
        this.formulaLevel += 1;
    } //Flagging on depth for formula

    public void signAsField() { this.formulaLevel += 2; } //Flagging on breadth for field

    public void signAsAutoArrayFormula() {
        this.formulaLevel += 10;
    }

    public void signAsArrayFormula() {
        this.formulaLevel += 100;
    }

    public boolean isSignedFormula(){
        return this.formulaLevel % 10 == 1;
    }

    public boolean isSignedField() { return (this.formulaLevel % 10 == 2 || this.formulaLevel % 10 == 3); }

    public boolean isSignedAutoArrayFormula(){
        return this.formulaLevel >= 10;
    }

    public boolean isSignedArrayFormula(){
        return this.formulaLevel >= 100;
    }

    public void unSignFormula(){
        this.formulaLevel -= 1;
    }

    public void unSignField() { this.formulaLevel -= 2; }

    public void unSignAutoArrayFormula(){
        this.formulaLevel -= 10;
    }

    public void unSignArrayFormula(){
        this.formulaLevel -= 100;
    }

    public boolean isOfType(Type type)
    {
        return this.getValue().getType() == type;
    }

    @Override
    public String getLocalizedFormula()
    {
        return this.value.getValueString(this.getSpreadsheetSettings(), this);
    }

    @Override
    public String getFormula()
    {
        if(isFormula())
        {
            return "="+FormulaUtil.getFormula(this.getExpression().getNode(), this.getRow().getSheet().getWorkbook(), this, this.getRowIndex(), this.getColumnIndex());
        }
        else
        {
            return getValueString(SpreadsheetSettings.defaultSpreadsheetSettings);
        }
    }

    public String getLocalizedValueString()
    {
        return this.getValueString(this.getSpreadsheetSettings());
    }

    private String getValueString(SpreadsheetSettings spreadsheetSettings)
    {
        Value coreValue;
        if(this.value instanceof ExpressionValue) {
            String zuid = AppUtil.getUserZuid();
            coreValue = ((ExpressionValue)this.value).getValue(zuid);
        } else {
            coreValue = this.value.getValue();
        }
        SimpleValue simpleValue = SimpleValue.getInstance(coreValue);
        return simpleValue.getValueString(spreadsheetSettings, this);
    }

    @Override
    public void setExpressionFromParser(Expression inExpression, boolean isUpdateDependencies, boolean isUpdateCellEditHistory)
    {
        this.setExpression(inExpression, isUpdateDependencies, isUpdateCellEditHistory);
    }

    public void setExpression(Expression inExpression, boolean isUpdateDependencies, boolean isUpdateCellEditHistory)
    {
        resetRepeatedCounts();
        this.setUsedIndex();

        this.translateObject = null;

        boolean prevHasFilterFunc = this.getExpression() != null && this.getExpression().hasFilterFunction(this.getSheet().getWorkbook());
        boolean newHasFilterFunc = inExpression.hasFilterFunction(this.getSheet().getWorkbook());
        if(inExpression.hasImportRangeFormula(this.getSheet().getWorkbook())) {
            this.importrangeSyncState = ImportRange.SYNC_STATE.FETCHING;
        }

        if (!prevHasFilterFunc && newHasFilterFunc) {
            this.getSheet().increaseFilterFormulaCells();
        }
        else if (prevHasFilterFunc && !newHasFilterFunc) {
            this.getSheet().decreaseFilterFormulaCells();
        }


        if(this.isFormula()){
            this.invalidatePrecedents();
        }

        if(!inExpression.isThroughPool()){
            inExpression = this.getRow().getSheet().getWorkbook().getExpressionPool().getExpression(inExpression.getNode());
        }

        // Create expressionValue with old Value object. Old value is required in internalSetValue.
        if(this.value instanceof ExpressionValue)
        {
            this.value = ((ExpressionValue)this.value).getNewInstance(inExpression, this.getValue(false));
        } else {
            this.value = new ExpressionValue(inExpression, this.getValue());
        }

        this.getRow().getSheet().signAsFormulaCell(this);
        if(isUpdateDependencies){
            this.updateDependencies();

        }

        if(isUpdateCellEditHistory)
        {
            this.updateCellHistory(System.currentTimeMillis(), inExpression);
        }
    }


    @Override
    public void setExpression(Expression inExpression, boolean isUpdateDependencies)
    {
        this.setExpression(inExpression, isUpdateDependencies, true);
    }

    private void invalidateExpression(){
        if(this.value instanceof ExpressionValue)
        {
            String zuid = AppUtil.getUserZuid();
            this.value = SimpleValue.getInstance(((ExpressionValue)this.value).getValue(zuid));
        }
    }

    @Override
    public void setFormula(String inFormula, boolean isUpdateDependencies)
    {
        this.valueTimestamp=System.currentTimeMillis();
        Expression inExpression= this.getRow().getSheet().getWorkbook().getExpressionPool().getExpression(this.getRow().getSheet().getWorkbook(), inFormula, this.getRowIndex(), this.getColumnIndex(), false, false, false, CellReference.ReferenceMode.A1);
        //
        //
        //
        this.setExpression(inExpression, isUpdateDependencies);

        //this.derivedPattern = null;// Reset derivedPattern to null. So that it will getderived for current formula.
    }

    /*
    public void flushExpressionMetaData(){
        if(this.isFormula()){
            if(!(this.value instanceof ExpressionValue)){
                this.getExpression();
            }
            invalidateFormula();
        }
    }
    */

    public void removeFormula()
    {
        // Remove this cell from the Formula Cell list maintained by Sheet
        this.getRow().getSheet().unsignAsFormulaCell(this);
        if (this.getExpression().hasFilterFunction(this.getSheet().getWorkbook())) {
            this.getSheet().decreaseFilterFormulaCells();
        }
        this.invalidatePrecedents();
        this.invalidateExpression();

//        this.setAutoArraySpan(0, 0, true);
//        this.formula = null;
//        this.localizedFormula = null;
//        this.node = null;
        //this.isFormula = false;
        //if(this.arrayRowSpan > 0 || this.arrayColSpan > 0) // Moved these checks to setArraySpan()
        //{
        // Moved setting arrayspan zero to unsignAsFormulaCell method.
//            setArraySpan(0, 0, false);
        //}
        //updateDependencies();
    }

    ////

    public CellReEvaluateObject getReEvaluateObject()
    {
        if(this.reEvaluateObject == null ||
            this.reEvaluateObject.getReEvaluate() != this.getRow().getSheet().getWorkbook().getReEvaluate())
        {
            reEvaluateObject = new CellReEvaluateObject(this);
        }
        return reEvaluateObject;
    }

    @Override
    public Type getType()
    {
        return getContentType();
    }

    public Sheet getSheet()
    {
        return this.getRow().getSheet();
    }

    @Override
    /**
     * Returns the value based on the current filter view.
     */
    public Value getValue()
    {
        return this.getValue(true);
    }

    private Value getValue(boolean onlyForCurrentUser)
    {
        if(value == null)
        {
            value = SimpleValue.getInstance(Value.EMPTY_VALUE);//Value.EMPTY_VALUE;
        }

        /**
         * RETRIEVE FILTER VIEW FOR CURRENT USER ZUID AND GET THE VALUE OBJECT FROM THE FILTER VIEW VS VALUE MAP
         */
        if(this.reEvaluateObject != null && this.reEvaluateObject.isMarkedCircularRef())
        {
            if(this.getSheet().getWorkbook().getCalculationSettings() == null)
            {
                return Value.getInstance(Type.ERROR, Cell.Error.CIRCULARREF);
            }
            else if(this.value.getValue().getRawValue() == Error.CIRCULARREF)
            {
                return Value.EMPTY_VALUE;
            }
        }

        if(onlyForCurrentUser && this.value instanceof ExpressionValue) {
            String zuid = AppUtil.getUserZuid();
            return ((ExpressionValue) this.value).getValue(zuid);
        } else {
            Value value = this.value.getValue();
            return value;
        }
    }

    @Override
    public Object getValueObject()
    {
        return this.getValue().getValue();
    }

    // This will be only for parser
    public void setValueFromParser(Value valueObj)
    {
        this.value = SimpleValue.getInstance(valueObj);
    }

    public void setValueFromParser(ValueInterface valueObj)
    {
        this.value = valueObj;
    }

    private void internalSetValue(Value valueObj, boolean resetRepeatedCounts)
    {
        this.importrangeSyncState = ImportRange.SYNC_STATE.IN_SYNC;
        if(valueObj == null)
        {
            valueObj = Value.EMPTY_VALUE;
        }
        // Date object usage has to be removed completely. First removing the usage for Time values alone to avoid precision loss while calculation.
        else if(valueObj.getType() == Type.TIME)
        {
            valueObj = Value.getInstance(Type.FLOAT, DateUtil.convertDateToNumber((Date)valueObj.getValue()));
        }

        this.contentValid=null;    //reset
        this.ignoreError = this.ignoreError == 1 ? 0 : this.ignoreError; // Reseting here only if the error ignored is TypeMismatchError.

//    	Sheet inSheet = this.getRow().getSheet();
//    	(inSheet.getWorkbook().getTabularDataPool()).invalidateTabularData(inSheet, this.getRowIndex(), this.getColumnIndex());


        // set the used row/column index
        if (resetRepeatedCounts)
        {
            resetRepeatedCounts();
            if (valueObj != null && !Value.EMPTY_VALUE.equals(valueObj))
            {
                setUsedIndex();
            }
        }

        Value tempVal = this.getValue();

        /////////////////////////////////////////
        if(valueObj instanceof ValueWithPattern)
        {
            ZSPattern inPattern = ((ValueWithPattern)valueObj).getPattern();
            valueObj = ((ValueWithPattern)valueObj).getValueObject();
            if(inPattern == null)
            {
                inPattern = DataStyleConstants.EMPTY_PATTERN;
            }
            if(this.isFormula())
            {
                this.derivedPattern = inPattern;
            }
            else
            {
                this.setPattern(inPattern, false);
                this.derivedPattern = inPattern;
            }
        }else {
            this.derivedPattern = null;
        }
        /////////////////////////////////////////

        if(this.value instanceof ExpressionValue)
        {
            if(this.reEvaluateObject != null && this.reEvaluateObject.isInFilterReevaluate() && AppUtil.getUserZuid() != null)
            {
                this.value = ((ExpressionValue) this.value).getNewInstance(AppUtil.getUserZuid(), valueObj);
            }
            else
            {
                this.value = new ExpressionValue(((ExpressionValue) this.value).getExpression(), valueObj);
            }
        }
        else
        {
            this.value = SimpleValue.getInstance(valueObj);
        }

        if(this.value.getValue().getType() == Type.STRING || tempVal.getType() == Type.STRING)
        {
            int stringLength = this.value.getValue().getStringLength() - tempVal.getStringLength();
            if(stringLength != 0) {
                this.getRow().reviseStringLength(stringLength);
                this.getRow().getSheet().reviseStringLength(stringLength);
                this.getRow().getSheet().getWorkbook().reviseStringLength(stringLength);
            }
            this.setCellHeight(null);
            this.setCellWidth(null);
        }

        this.isArrayValue = false;

        ConditionalStyleCellObject cscellObj = this.getConditionalStyleCellObject();
        if(cscellObj != null)
        {
            Double oldValue = null;
            Double currentVal = null;
            try{
                oldValue = FunctionUtil.objectToNumber(tempVal.getValue()).doubleValue();
            }
            catch(EvaluationException e){}
            try{
                currentVal = FunctionUtil.objectToNumber(this.getValue().getValue()).doubleValue();
            }
            catch(EvaluationException e){}

            if(!Objects.equals(oldValue, currentVal))
            {
                Set<ConditionalStyleObject> conditionalStyleObjects = cscellObj.getConditionalStyleObjects();
                if(conditionalStyleObjects != null)
                {
                    Iterator csObjIterator = conditionalStyleObjects.iterator();
                    while(csObjIterator.hasNext())
                    {
                        ConditionalStyleObject csObj = (ConditionalStyleObject)csObjIterator.next();
                        if(csObj.isInvalid())
                        {
                            csObjIterator.remove();
                            continue;
                        }

                        SpecialRange specialRange = csObj.getSpecialRange();
                        ConditionalStyle cs = csObj.getConditionalStyle();
                        if(cs.hasPercentileEntry())
                        {
                            csObj.setIsResetRangeLevel(true);
                            continue;
                        }

                        boolean setIsResetRangeLevel = csObj.isResetRangeLevel();
                        for(DataRange range : specialRange.getRanges())
                        {
                            if(range.getConditionalMinValue() == null || range.getConditionalMaxValue() == null)
                            {
                                setIsResetRangeLevel = true;
                            }
                            else if(range.isMember(this.getRow().getSheet().getAssociatedName(), this.getRowIndex(), this.getColumnIndex()))
                            {
                                Double min = range.getConditionalMinValue();
                                Double max = range.getConditionalMaxValue();

                                if(Objects.equals(min, oldValue))
                                {
                                    min = null;
                                    setIsResetRangeLevel = true;
                                }
                                else if(Objects.equals(max, oldValue))
                                {
                                    max = null;
                                    setIsResetRangeLevel = true;
                                }
                                else if(currentVal != null)
                                {
                                    if(min > currentVal)
                                    {
                                        min = currentVal;
                                        setIsResetRangeLevel = true;
                                    }
                                    else if(max < currentVal)
                                    {
                                        max = currentVal;
                                        setIsResetRangeLevel = true;
                                    }
                                }
                                range.setConditionalValues(min, max);
                            }
                        }
                        csObj.setIsResetRangeLevel(setIsResetRangeLevel);
                    }

                    if(conditionalStyleObjects.isEmpty())
                    {
                        this.setConditionalStyleCellObject(null);
                    }
                }
            }
        }

        if(cellPicklists != null) {
            for(CellPicklist cellPicklist: this.getCellPicklists()) {
                cellPicklist.setValue(this.value.getValue(), this.getContent(), getSheet().getWorkbook());
            }
        }

        if(!this.getValue().equals(Value.EMPTY_VALUE)) {
            List<Picklist> picklists = CellUtil.getPicklistsForSourceCell(this.getSheet(), this.getRowIndex(), this.getColumnIndex());
            if(!picklists.isEmpty()) {
                if(cellPicklists == null) {
                    cellPicklists  = new HashMap<>();
                }
                for(Picklist picklist: picklists) {
                    if(!cellPicklists.containsKey(picklist.getId())) {
                        CellPicklist cellPicklist = new CellPicklist(picklist);
                        cellPicklist.setValue(this.getValue(), this.getContent(), getSheet().getWorkbook());
                        cellPicklists.put(picklist.getId(), cellPicklist);
                    }

                }
            }
        }
        this.getSheet().updateFieldCell(this);

        for(SparklinesGroup sparklinesGroup: this.getSheet().getSparklinesGroupList()) {
            sparklinesGroup.onCellValueChange(this, tempVal, this.getValue());
        }

        if(this.getSheet().getWorkbook().trackChangesInTable()) {
            if (!Objects.equals(tempVal, this.getValue())) {
                for (Table table : this.getSheet().getTables().values()) {
                    table.saveModifiedCells(this);
                }
            }
        }
        this.resetMaxWidthHeightIndex();
    }

    public void updateCellHistory(long valueTimestamp, Value value)
    {
        if(!this.getSheet().getWorkbook().isRecordEditHistory)
        {
            return;
        }
        if(AppUtil.getUserZuid() == null)
        {
            return;
        }
        if(AppUtil.getAid() == -1)
        {
            return;
        }

        int rowIndex = this.getRowIndex();
        int colIndex = this.getColumnIndex();
        CellEditHistoryBean cellEditHistoryBean = new CellEditHistoryBean(AppUtil.getUserZuid(), AppUtil.getAid(), valueTimestamp, value);
        this.getSheet().addCellEditHistory(rowIndex, colIndex, cellEditHistoryBean);
    }

    public void updateCellHistory(long valueTimestamp, Expression expression)
    {
        if(!this.getSheet().getWorkbook().isRecordEditHistory)
        {
            return;
        }
        if(AppUtil.getUserZuid() == null)
        {
            return;
        }
        if(AppUtil.getAid() == -1)
        {
            return;
        }

        int rowIndex = this.getRowIndex();
        int colIndex = this.getColumnIndex();

        CellEditHistoryBean cellEditHistoryBean = new CellEditHistoryBean(AppUtil.getUserZuid(), AppUtil.getAid(), valueTimestamp, expression);
        this.getSheet().addCellEditHistory(rowIndex, colIndex, cellEditHistoryBean);
    }

    @Override
    public void setValue(Value valueObj)
    {
        this.valueTimestamp=System.currentTimeMillis();
        //logger.info(valueObj)eva
        // Available
        // Value &
        // Pattern
        // Remove the formula if any set
        if(this.isFormula())// && valueObj.getType() != Type.ERROR) // dont remove formula if its a error object
        {
            this.removeFormula();
            // Remove this cell from the Sheet maintaining the Formula Cell
            // references
            //this.getRow().getSheet().unsignAsFormulaCell(this); // Already done in removeFormula
        }

        internalSetValue(valueObj, true);
        if(!(valueObj instanceof PicklistValue && valueObj.getValue() == null))  // TO avoid EditHistory for empty Picklist Creation
        {
            updateCellHistory(valueTimestamp, valueObj);
        }
    }

//    private void formatValue_Pattern()
//    {
//        Value tempValue = this.getValue(false);
//
//        if(this.value instanceof ExpressionValue)
//        {
//            Map<String, Value> valueMapOld = ((ExpressionValue) this.value).getValueMap();
//            Map<String, Value> valueMapNew = new HashMap<>();
//            for(Map.Entry<String, Value> valueEntry: valueMapOld.entrySet()) {
//                Value valueNew = getFormattedValue(valueEntry.getValue());
//                valueMapNew.put(valueEntry.getKey(), valueNew);
//            }
//            this.value = new ExpressionValue(this.getExpression(), tempValue, valueMapNew);
//        }
//        else
//        {
//            this.value = SimpleValue.getInstance(tempValue);
//        }
//    }

    private Value getFormattedValue(Value value) {
        Object val = value.getValue();
        if(val instanceof Number) {
            value = Value.getInstance(Type.FLOAT, val);
        }

        ZSPattern aPattern = this.getPattern(2);
        if(aPattern != null) {
            if(val instanceof Number && aPattern.getType().isDateType())
            {
                value = DateUtil.convertNumberToDateValue((Number)val);
            }
            else if(val instanceof Date && aPattern.getType().isNumberType())
            {
                value  = Value.getInstance(Type.FLOAT, DateUtil.convertDateToNumber((Date)val));
            }
        }

        return value;
    }

    /*
     * Change the value according to the pattern specified for the cell.
     * should be called whenever value is set for any cell.
     * ie. from setValue(Value value) either directly or indirectly.
     *
     *
    void formatValue_Value()
    {
        if(this.getValue().getType() != Type.STRING &&
            this.getValue().getType() != Type.ERROR &&
            this.getValue().getType() != Type.UNDEFINED &&
            this.getValue().getType() != Type.BOOLEAN)
        {
            // Should get pattern from Column Header too(escalation level 2) as we set empty_pattern in next line assuming there is no explicit pattern set to this cell neither directly not through column header.
            ZSPattern cellPattern = this.getPattern(2);
            if(cellPattern == null || cellPattern == DataStyleConstants.EMPTY_PATTERN)
            {
                this.pattern = DataStyleConstants.EMPTY_PATTERN;
            }
            else if(!this.isFormula() // For direct set values, patterns shall be removed if it doesn't match with input value
                && cellPattern.getType() != this.getValue().getType())
            {
                if(this.getValue().getType() == Type.FLOAT)
                {
                    if(cellPattern.hasDateComponent()) {
                        this.pattern = DataStyleConstants.EMPTY_PATTERN;
                    }
                }
                else
                {
                    this.pattern = DataStyleConstants.EMPTY_PATTERN;
                }
            }
        }
    }*/



    public static void updateChartReGenStatus(Cell cell)
    {
        try
        {
            ////// check if chart require any regen
            Workbook	workbook	=	cell.getRow().getSheet().getWorkbook();

            if(workbook != null)
            {
                Map<String, Map<String, Chart>>  chartMap = workbook.getChartMap();

                if(chartMap != null)
                {
                    for(Sheet sheet : workbook.getSheetList())
                    {
                        Map<String, Chart>	sheetChartMap	=	chartMap.get(sheet.getAssociatedName());

                        if(sheetChartMap != null)
                        {
                            for (Chart chart : sheetChartMap.values())
                            {
                                if(chart != null && !chart.isReGenRequired() && chart.isCellInDataRanges(cell))
                                {
                                    if(ChartUtils.isChartPublished(chart.getChartId()))
                                    {
                                        ChartUtils.updateChartsGodown(chart.getDocumentId(), chart.getSheetName(), chart.getChartId(), chart.getPublicChartName());
                                    }
                                    chart.setReGenRequired(true);
                                }
                            }
                        }

                    }
                }
            }
        }
        catch(Exception e)
        {
            LOGGER.log(Level.INFO, "Exception in updating public charts [CHART] >>>>{0}", e);
        }
    }
    
    public void updateLazyEvalDepth(boolean isIncreaseDepth)
    {
        updateLazyEvalDepth(isIncreaseDepth ? 1 : -1);
    }

    private synchronized int updateLazyEvalDepth(int increment)
    {
        this.lazyEvalDepth += increment;
        return this.lazyEvalDepth;
    }

    public int getLazyEvalDepth() {
        return updateLazyEvalDepth(0);
    }
    public TranslateObject translateObject= null;

    public TranslateObject getTranslationObject() {
        if(this.translateObject == null){
            this.translateObject = new TranslateObject(System.currentTimeMillis());
        }
        return this.translateObject;
    }

    public class TranslateObject {
        private long startTime;

        private boolean isStarted;
        private Queue<String> translateId = new ArrayDeque<>();
        private Queue<String> translateResult = new ArrayDeque<>();


        public TranslateObject(long startTime) {
            this.isStarted = true;
            this.startTime = startTime;
        }

        public long getStartTime() {
            return this.startTime;
        }

        public void addId(String id) {
            this.translateId.add(id);
        }

        public List<String> getIdList() {
            return new ArrayList<>(this.translateId);
        }

        public void addResult(String result) {
            this.translateResult.add(result);
        }

        public String getResult() {
            this.translateId.poll();
            String result = this.translateResult.poll();
            if(this.translateResult.isEmpty()) {
//                this.translateId.clear();
                this.startTime = 0;
            }
            return result;
        }

        public boolean isComplete(){
            if(!this.translateId.isEmpty() && this.translateId.size() == this.translateResult.size()){
                return true;
            }
            return false;
        }

        public boolean isStarted() {
            return this.isStarted;
        }

        public void setAsAlreadyStarted() {
            this.isStarted = false;
        }

        public void clearResult() {
            this.translateResult.clear();
        }
    }

    public void evaluate()
    {
        if(!this.isFormula()) {
            setAutoArraySpan(1,1, false);
            return;// this.getValue();
        }

        Object result;


        //final Instant evaluationStartInstant = Instant.now();


        if (this.reEvaluateObject != null && this.reEvaluateObject.getReEvaluate() != null && this.reEvaluateObject.getReEvaluate().isEvaluationCrossedTimeLimit()) {
            result = Value.getInstance(Type.ERROR, Error.TIMEOUT);
        } else if (this.reEvaluateObject != null && this.reEvaluateObject.isLazyEvaluateCell()) {
            result = Value.getInstance(Type.ERROR, Error.LAZY_EVAL);
            this.getReEvaluateObject().setLazyEvaluateCell(false);
        } else {
            Expression evaluatingExpression = this.getExpression();
            ImportRange.SYNC_STATE syncState1 = this.importrangeSyncState;
            this.importrangeSyncState = ImportRange.SYNC_STATE.IN_SYNC;
            try {
                ZSEvaluator zsEvaluator = new ZSEvaluator(isArrayCell(), ((ExpressionImpl) evaluatingExpression).hasCacheableNode());

                if (this.translateObject != null) {
                    this.translateObject.setAsAlreadyStarted();
                    Long time = translateObject.getStartTime();
                    if(System.currentTimeMillis() - time > 5000) {
                        result = Value.getInstance(Cell.Type.ERROR, Error.TIMEOUT);
                        this.translateObject = null;
                    } else {
                        result = zsEvaluator.evaluate(this.getExpression().getNode(), this, false, false);
                        if(!result.equals(Value.getInstance(Cell.Type.ERROR, Cell.Error.LAZY_EVAL))) {
                            this.translateObject = null;
                        }
                    }
                } else {
                    result = zsEvaluator.evaluate(this.getExpression().getNode(), this, false, false);
                }
//                result = zsEvaluator.evaluate(this.getExpression().getNode(), this, false, false, evaluationStartInstant);
            } catch (EvaluationException ex) {
                result = ex;//CellUtil.getError(ex);
                this.importrangeSyncState = syncState1;
            }
            if(!evaluatingExpression.equals(this.getExpression()))
            {
                this.importrangeSyncState = syncState1;
                return;
            }
        }

        synchronized (this) {
            Sheet sheet = this.getRow().getSheet();
            int rowIndex = this.getRowIndex();
            int colIndex = this.getColumnIndex();

            result = checkCellCountLimit(result, sheet, rowIndex, colIndex);

            if(this.getExpression().hasImportRangeFormula(this.getSheet().getWorkbook())) {
                if(this.importrangeSyncState != ImportRange.SYNC_STATE.IN_SYNC) {
                    return;
                }
            }

            int[] arraySpan = sheet.getArraySpan(this);

            if (arraySpan != null) {

                if (result instanceof ZArrayI && ((ZArrayI) result).getSize() == 1) {
                    result = ((ZArrayI) result).getValue(0, 0);
                }

                int rowSpan = arraySpan[0];
                int colSpan = arraySpan[1];

                if (!(result instanceof ZArrayI) || ((ZArrayI) result).getRowSize() != rowSpan || ((ZArrayI) result).getColSize() != colSpan) {
                    List<Object> resultArray = new ArrayList<>();
                    for (int r = 0; r < rowSpan; r++) {
                        for (int c = 0; c < colSpan; c++) {
                            Object valueObject;
                            if ((r != 0 || c != 0) && this.getExpression().hasDynamicFunction(this.getSheet().getWorkbook())) {
                                ZSEvaluator zsEvaluator = new ZSEvaluator(isArrayCell());
                                try {
                                    valueObject = zsEvaluator.evaluate(this.getExpression().getNode(), this, false, false);
                                    valueObject = (valueObject instanceof ZArrayI) ? ((ZArrayI) valueObject).getValue(r, c) : valueObject;
                                } catch (EvaluationException e) {
                                    valueObject = e;
                                }
                            } else {
                                valueObject = (result instanceof ZArrayI) ? ((ZArrayI) result).getValue(r, c) : result;
                            }
                            resultArray.add(valueObject);
                        }
                    }
                    result = new ZArray(resultArray, rowSpan, colSpan);
                }
            }


            if (!(result instanceof ZArrayI)) {
                result = new ZArray(Arrays.asList(result), 1, 1);
            }

            setAutoArraySpan(((ZArrayI) result).getRowSize(), ((ZArrayI) result).getColSize(), false);
            if (isAutoArrayError()) {
                result = new ZArray(Arrays.asList(Value.getInstance(Type.ERROR, Error.SPILL)), 1, 1);
                Cell errorCell = this.getAutoArrayErrorCell();
                if (errorCell != null) {
                    ((CellImpl) errorCell).addAutoArrayErrorParents(this);
                }
            }

            Iterator rangeValueIterator = null;
            if (result instanceof Range) {
                rangeValueIterator = ((Range) result).valueWithPatternIterator(false, false, true);
            }

            int zArrayRowSize = ((ZArrayI) result).getRowSize();
            int zArrayColSize = ((ZArrayI) result).getColSize();
            // Set results to cells or set error accourding to the span or error offet set by setAutoArraySpan function.
            int rowsRepeated = 1;
            for (int r = 0; r < zArrayRowSize; r++) {

                if (rowIndex + r == sheet.getRowNum()) {
                    Row newRow = sheet.getRow(rowIndex + r);
                    newRow.setRowsRepeated(zArrayRowSize - r);
                }

                int colsRepeated = rowsRepeated > 1 ? zArrayColSize + 1 : 1;
                for (int c = 0; c < zArrayColSize; c++) {

                    Value value = rangeValueIterator != null ? (Value) rangeValueIterator.next() : ((ZArrayI) result).getValueWithPattern(r, c);
                    // RANGE ITERATOR WILL RETURN VALUE WITH PATTERN OBJECT WHILE ZARRAY WITH RETURN VALUE OBJECT
                    boolean isImageValue = value instanceof ValueWithPattern ? ((ValueWithPattern) value).getValueObject() instanceof ImageValue : value instanceof ImageValue;

                    if(value instanceof PicklistValue || (value instanceof ValueWithPattern && ((ValueWithPattern) value).getValueObject() instanceof PicklistValue)) {
                        PicklistValue picklistValue = value instanceof ValueWithPattern ? (PicklistValue) ((ValueWithPattern) value).getValueObject() : (PicklistValue) value;
                        value = picklistValue.convertToNormalValue();
                    }
//                    else {
                    // RICH STRINGS WILL NOT BE PERSISTED FOR REFERENCED CELLS
                    //value = !isImageValue && value.getRawValue() instanceof ZSString ? Value.getInstance(Type.STRING, ((ZSString) value.getRawValue()).getBaseStringValue()) : value;
//                    }
                    boolean isEmptyVal = Value.EMPTY_VALUE.equals(value);
                    if (isEmptyVal && colsRepeated > 1) {
                        colsRepeated -= 1;
                    } else {
                        if (sheet.getRow(rowIndex + r).getCellNum() == colIndex + c) {
                            Cell newCell = sheet.getCell(rowIndex + r, colIndex + c);
                            newCell.setColsRepeated(zArrayColSize - c);
                        }

                        Cell nextCell;
                        if (r == 0 && c == 0) {
                            this.internalSetValue(value, !isEmptyVal);
                            nextCell = this;
                        } else {
                            nextCell = sheet.getCell(rowIndex + r, colIndex + c);
                            if (isEmptyVal) {
                                int remRowSpan = zArrayRowSize - r;
                                int remColSpan = zArrayColSize - c;
                                if (nextCell.getColsRepeated() > remColSpan) {
                                    sheet.getCell(rowIndex + r, colIndex + c + colsRepeated);
                                }
                                if (nextCell.getRow().getRowsRepeated() > remRowSpan) {
                                    sheet.getCell(rowIndex + r + remRowSpan, colIndex + c);
                                }
                            }
                            ((CellImpl) nextCell).internalSetValue(value, !isEmptyVal);
                            ((CellImpl) nextCell).isArrayValue = true;
                            ((CellImpl) nextCell).autoArraySuccessParent = new OffsetIndices(this, nextCell);

                        }

                        if (nextCell.getColsRepeated() > zArrayColSize - c) {
                            sheet.getCell(rowIndex + r, colIndex + zArrayColSize - c);
                        }
                        colsRepeated = nextCell.getColsRepeated();
                    }
                }

                Row nextRow = sheet.getRowReadOnly(rowIndex + r);
                if (nextRow == null) {
                    rowsRepeated -= 1;
                } else {
                    if (nextRow.getRowsRepeated() > zArrayRowSize - r) {
                        sheet.getRow(rowIndex + zArrayRowSize - r);
                    }
                    rowsRepeated = nextRow.getRowsRepeated();
                }
            }
        }
    }

    private Object checkCellCountLimit(Object result, Sheet sheetCurr, int rowIndex, int colIndex) {
        if(!(result instanceof ZArrayI)) {
            return result;
        }

        int rowSize = ((ZArrayI)result).getRowSize();
        int colSize = ((ZArrayI)result).getColSize();
        if(rowSize*colSize == 1) {
            return result;
        }

        int newUsedRowIndex = rowIndex+rowSize;
        int newUsedColIndex = colIndex+colSize;
        if(newUsedRowIndex <= sheetCurr.getUsedRowIndex() && newUsedColIndex <= sheetCurr.getUsedColumnIndex()) {
            return result;
        }

        int usedCellCount = 0;
        String associatedName = sheetCurr.getAssociatedName();
        for(Sheet sheet : this.getSheet().getWorkbook().getSheetList())
        {
            if(associatedName.equals(sheet.getAssociatedName())) {
                usedCellCount += (Math.max(newUsedRowIndex, sheetCurr.getUsedRowIndex())+1)*(Math.max(newUsedColIndex, sheetCurr.getUsedColumnIndex()));
            } else {
                usedCellCount += sheet.getNoOfUsedCells();
            }
        }

        if(usedCellCount > Utility.CELLCOUNTLIMIT) {
            return Value.getInstance(Type.ERROR, Error.EVAL);
        }

        return result;
    }

    private void addAutoArrayErrorParents(Cell errorParent){
        if(errorParent == null)
        {
            return;
        }
        this.resetRepeatedCounts();
        if(this.autoArrayErrorParents == null){
            this.autoArrayErrorParents = new ArrayList<>();
        }
        this.autoArrayErrorParents.add(new OffsetIndices(errorParent, this));
    }

    protected void removeAutoArrayParent(Cell autoArrayParent){
        if(this.autoArraySuccessParent != null && this.autoArraySuccessParent.isThisParent(autoArrayParent, this)){
            this.autoArraySuccessParent = null;
        }else if(this.autoArrayErrorParents != null){
            OffsetIndices temp = new OffsetIndices(autoArrayParent, this);
            this.autoArrayErrorParents.remove(temp);
        }
    }

    public Cell getAutoArraySuccessParent() {
        return this.autoArraySuccessParent != null ? this.getSheet().getCell(this.getRowIndex() + this.autoArraySuccessParent.rowOffset, this.getColumnIndex() + this.autoArraySuccessParent.colOffset) : null;
    }

    public List<Cell> getAutoArrayErrorParents() {
        if(this.autoArrayErrorParents != null){
            List<Cell> tempAutoArrayErrorParent = new ArrayList<>();
            for(OffsetIndices offset : this.autoArrayErrorParents){
                tempAutoArrayErrorParent.add(this.getSheet().getCell(this.getRowIndex() + offset.rowOffset, this.getColumnIndex() + offset.colOffset));
            }
            return tempAutoArrayErrorParent;
        }
        return Collections.emptyList();
    }

    public boolean isAutoArrayError() {
        return (this.autoArrayError != null && this.autoArrayError.rowOffset + this.autoArrayError.colOffset > 0) ||
            this.getRowIndex() + this.autoArrayRowSpan > Utility.MAXNUMOFROWS || this.getColumnIndex() + this.autoArrayColSpan > Utility.MAXNUMOFCOLS;
    }

    public int getAutoArrayRowSpan() {
        return autoArrayRowSpan;
    }

    public int getAutoArrayColSpan() {
        return autoArrayColSpan;
    }

    public Cell getAutoArrayErrorCell() {
        if(this.autoArrayError != null && this.autoArrayError.rowOffset + this.autoArrayError.colOffset > 0) {
            return getSheet().getCell(this.getRowIndex() + this.autoArrayError.rowOffset, this.getColumnIndex() + this.autoArrayError.colOffset);
        }
        return null;
    }

    protected void changeOffset(int rowIndex, int colIndex, int tempRowIndex, int tempColIndex){
        if(this.autoArraySuccessParent != null){
            int rowOffset = this.autoArraySuccessParent.rowOffset + (tempRowIndex - rowIndex);
            int colOffset = this.autoArraySuccessParent.colOffset + (tempColIndex - colIndex);
            this.autoArraySuccessParent = new OffsetIndices(rowOffset, colOffset);
        }
    }

    public static Value getAsValueObject(Object result)
    {
        Value resultObject;
        Object actualResult = result;

        if(result instanceof ValueWithPattern) {
            result = ((ValueWithPattern) result).getValueObject();
        }

        if(result instanceof PicklistValue) {
            resultObject = ((PicklistValue) result).convertToNormalValue();
        }
        else if (result instanceof Value)
        {
            resultObject = (Value) result;
        }
        else if (result == null)
        {
            resultObject = Value.getInstance(Type.UNDEFINED, null);
        }
        else if (result instanceof Throwable)
        {
            resultObject = Value.getInstance(Type.ERROR, result);
        }
        else if(result instanceof Date)
        {
            resultObject = DateUtil.convertDateToDateValue((Date)result);
        }
        else if(result instanceof String)
        {
            resultObject = Value.getInstance(Type.STRING, result);
        }
        else if(result instanceof Boolean)
        {
            resultObject = Value.getInstance(Type.BOOLEAN, result);
        }
        else if(result instanceof Number)
        {
            resultObject = Value.getInstance(Type.FLOAT, result);
        }
        else // Ideally, every result object should get converted in above part of code itself. Adding the below code, so that even something is miissed in above ifs, it will get converted to value object below.
        {
            // Should use default Locale here as we are passing result.toString which will be in defaultLocale.
            resultObject = Value.getInstance(result.toString(), SpreadsheetSettings.defaultSpreadsheetSettings);
        }

        result = resultObject.getValue();

        if (result instanceof Double &&
            (Double.isInfinite(((Double) result)) || Double.isNaN(((Double) result))))
        {
            resultObject = Value.getInstance(Type.ERROR, Error.NUM);
        }

        if(actualResult instanceof ValueWithPattern) {
            resultObject = new ValueWithPattern((ValueI) resultObject, ((ValueWithPattern) actualResult).getPattern());
        }
        return resultObject;
    }

    //////////////////Cell Dependencies capturing//////////////////////
    // Cell Dependency capturing
    /**
     * Updates the cell's dependencies.
     * @return
     */

    @Override
    public Set getPrecedents()
    {
        if(this.isFormula()) {
            return ((ZSRefEvaluator) Workbook.getRefJep().getEvaluator()).evaluate(this.getExpression().getNode(), this);
        }

        return Collections.emptySet();
    }

    private void invalidatePrecedents()
    {
            for (Object precedent : getPrecedents())
            {
                if (precedent instanceof Range)
                {
                    if (((Range) precedent).getSize() == 1)
                    {
                        precedent = ((Range) precedent).getTopLeft();
                        ((CellImpl) ((CellReference) precedent).getCell()).removeDependendent(this);
                    } else
                    {
                        this.getRow().getSheet().getWorkbook().unsignDependentRange((Range) precedent, this);
                    }
                }
                else
                {
                    ((CellImpl)((CellReference)precedent).getCell()).removeDependendent(this);
                }
            }
    }

    public void updateDependencies()
    {
        updateDependencies(true);
    }

    // To be used directly only from Workbook.updateDynamicDependencies.
    protected void updateDependencies(boolean isCheckRestriction)
    {
        Workbook workbook = this.getRow().getSheet().getWorkbook();

        if (this.getExpression() == null)
        {
            return;
        }

        Set stack = this.getPrecedents();
        for (Object obj : stack)
        {
            if (obj instanceof Range && ((Range) obj).getSize() == 1) {
                    obj =  ((Range) obj).getTopLeft();
            }
            Sheet precedentSheet;
            if (obj instanceof Range)
            {
                Range range = (Range) obj;
                range = workbook.signDependentRange(range, this);
                precedentSheet = range.getSheet();
                if (isCheckRestriction) {
                    workbook.checkForRestriction(range.toDataRange());
                }
            }
            else
            {
                CellReference cellReference = (CellReference) obj;
                ((CellImpl)cellReference.getCell()).addDependent(this);
                precedentSheet = cellReference.getCell().getRow().getSheet();
                if (isCheckRestriction) {
                    workbook.checkForRestriction(new DataRange(cellReference.getCell().getRow().getSheet().getAssociatedName(), cellReference.getCell().getRowIndex(), cellReference.getCell().getColumnIndex(), cellReference.getCell().getRowIndex(), cellReference.getCell().getColumnIndex()));
                }
            }

            if (!precedentSheet.getWorkbook().isSheetDependencyUpdated() && precedentSheet != this.getRow().getSheet())
            {
                this.getRow().getSheet().updateSheetDependencyMap(precedentSheet.getAssociatedName(), false);
            }
        }
    }

    @Override
    public Set<Cell> getDependents(){
        if(this.dependents == null){
            return Collections.EMPTY_SET;
        }

        return this.dependents;
    }

    private void addDependent(Cell dependentCell){
        if(this.dependents==null){
            this.dependents= new HashSet<>();
        }
        this.dependents.add(dependentCell);
    }

    private void removeDependendent(Cell dependentCell){
        if(this.dependents != null){
            this.dependents.remove(dependentCell);
        }
    }

    @Override
    public String toString()
    {
        //logger.info("Here :"+getCellRef());
        //return (getCellRef() + " : "+getFormula() + " : "+getContent()+" : "+getValue());
        //return getCellRef()+" : "+getFormula();
        //return getCellRef()+" : "+colsRepeated;
        //return getRowIndex()+" : "+getColumnIndex()+" : "+getCellRef()+" : "+((getPattern()!=null)?getPattern().getLocaizedPatternString():"");
        return getCellRef()+" : "+getColsRepeated();
    }

    @Override
    public Cell clone()
    {
        CellImpl o = null;

        try
        {
            o = (CellImpl) super.clone();

            //Reset this meta data(, believing that o.(...).sheet.mergeCellDetails map is not updated for o now
            //and the boolean will be set with a consistent update on Sheet.mergeCellDetails)
            o.setIsMergeParent(false);

            // clear the precedents and dependents
            o.dependents = null;
            o.isSigned = 0;
            o.formulaLevel = 0;

            if(this.cellPicklists != null) {
                o.cellPicklists = null;
                for(CellPicklist cellPicklist: this.getCellPicklists()) {
                    o.addCellPicklist(new CellPicklist(cellPicklist.getPicklist(),cellPicklist.getItemID()));
                }
            }
//            o.cellPicklists = null;

            if (annotation != null)
            {
                o.annotation = annotation.clone();
            }

            if(this.attachments != null) {
                o.addAllAttachment(this.attachments);
            }
            // Handling for DrawControl
            if (this.drawControlList != null) {
                o.drawControlList = new ArrayList<>();
                for (DrawControl drawControl : this.drawControlList) {
                    o.drawControlList.add(drawControl.clone());
                }
            }

            // Set the row handle so no cloning for this
            o.row = row;
            o.column = column;

        } catch (CloneNotSupportedException e)
        {
            LOGGER.log(Level.INFO, "AbstractCell can't clone");
        }

        return o;
    }

    @Override
    public Cell absoluteClone(Row row)
    {
        Cell o = clone();
        o.setRow(row);
        return o;
    }

    public void setAnnotationFromParser(Annotation annotation)
    {
        this.annotation = annotation;
    }

    @Override
    public void setAnnotation(Annotation annotation)
    {
        resetRepeatedCounts();
        if(annotation!=null){
            setUsedIndex();
            if(this.annotation == null)
            {
                getSheet().addAnnotedCell(this);
            }
        }else if(this.annotation != null)
        {
            getSheet().removeAnnotedCell(this);
        }
        this.annotation = annotation;
    }

    @Override
    public Annotation getAnnotation()
    {

        return annotation;
    }

    public void setIgnoreError(int ignoreType) {
        this.ignoreError = ignoreType;
    }

    public int getIgnoreError() {
        return this.ignoreError;
    }

    public ZSPattern getPattern(int escalationLevel)
    {
        return getPattern(escalationLevel, false);
    }

    public void addAttachment(Attachment attachment)
    {
        if(attachments == null){
            attachments = new ArrayList<>();
        }
        attachments.add(attachment);
    }

    public void addAttachment(int index, Attachment attachment)
    {
        if(attachments == null){
            attachments = new ArrayList<>();
        }
        attachments.add(index, attachment);
    }

    public void removeAttachment(int index)
    {
        attachments.remove(index);
    }

    public List<Attachment> getAttachments()
    {
        if (attachments == null) {
            return Collections.EMPTY_LIST;
        }
        return attachments;
    }

    public void addAllAttachment (Collection <Attachment> attachments) {
        if(attachments != null) {
            if(this.attachments == null){
                this.attachments = new ArrayList<>();
            }
            this.attachments.addAll(attachments);
        }
    }

    public void removeAllAttachments() {
        attachments = null;
    }

    /*
     * Pattern can be set at 2 levels. Cell and ColumnHeader leval
     * and If pattern is not set at both the levels, Cell will get the
     * default pattern of its Vale's dependentType.
     *
     * So we define 3 levels of precCellCount in pattern here.
     *
     * 1 : Check at the Cell level alone if there is any pattern. Should be used for Undo purpose only.
     * 2 : If Cell level null, then esaclate to ColumnHeader. Used internally for formatting Value from setPattern()
     * 3 : Default behaviour. Used for rendering to client
     */
    private ZSPattern getPattern(int escalationLevel, boolean isGetConditionalPattern)
    {
        ZSPattern tempPattern = null;

        if (this.getStyleName() != null)
        {
            tempPattern = DataStyleConstants.EMPTY_PATTERN;
            // Try to fetch from the CellStyle
            CellStyle cellStyle = this.getCellStyleReadOnly();
            if (cellStyle != null)
            {
                ZSPattern temp = (ZSPattern) cellStyle.getProperty_Deep(CellStyle.Property.PATTERN, this.getRow().getSheet().getWorkbook());
                tempPattern = temp == null || temp.equals(DataStyleConstants.EMPTY_PATTERN) ? tempPattern : temp ;
            }
        }

        // Fetching Column level Pattern.
        if (tempPattern == null && escalationLevel > 1)
        {
            // check whether the column has some pattern
            ReadOnlyColumnHeader rCH = this.getRow().getSheet().getReadOnlyColumnHeader(this.getColumnIndex());
            ColumnHeader cHeader = rCH.getColumnHeader();
            if (cHeader != null)
            {
                ZSPattern colPattern = cHeader.getPattern();
                if (colPattern != null)
                {
                    tempPattern = colPattern;
                }
            }
        }

        // Getting pattern from Default Style if available
        if(tempPattern == null) {
            CellStyle defaultCellStyle = this.getRow().getSheet().getWorkbook().getDefaultCellStyle();
            if(defaultCellStyle != null)
            {
                tempPattern = defaultCellStyle.getPattern();
            }

            //when pattern not found we look for a derived pattern
            if(tempPattern == null){
                tempPattern = DataStyleConstants.EMPTY_PATTERN;
            }
        }

        // Try to derive a pattern from formula precedent cells.
        if (tempPattern == DataStyleConstants.EMPTY_PATTERN)// && this.getValue().getType() == Type.FLOAT)
        {
            tempPattern = this.getDerivedPattern();
        }
        ///////////

        if (tempPattern != null && isGetConditionalPattern)
        {
            tempPattern = tempPattern.getConditionalPattern(this.getRow().getSheet().getWorkbook(), this.getValue());
        }

        return tempPattern;
    }

    public ZSPattern getDerivedPattern() {
        if(!this.isMarked && this.derivedPattern == null) {
            if(this.isFormula()) {
                this.isMarked = true;
                ZSFormatExtractor zSFormatExtractor = this.getSheet().getWorkbook().getFormatJep();
                this.derivedPattern = zSFormatExtractor.evaluate(this.getExpression().getNode(), this);
                this.isMarked = false;
            }
            else{
                Cell arrayParent = this.getAutoArraySuccessParent();
                if(arrayParent != null)
                {
                    this.derivedPattern = ((CellImpl)arrayParent).getDerivedPattern();
                }
            }
        }

        if(this.derivedPattern == null)
        {
            this.derivedPattern = DataStyleConstants.EMPTY_PATTERN;
        }

        return this.derivedPattern;
    }

    @Override
    public void setPattern(ZSPattern inPattern)
    {
        setPattern(inPattern, true);
    }

    public void setPattern(ZSPattern inPattern, boolean resetRepeatedCounts) // used in sheet(insert/ remove row/column)
    {
        // set the used row/column index
        if(resetRepeatedCounts)
        {
            resetRepeatedCounts();
        }

        CellStyle cs = this.getCellStyleReadOnly();
        if (cs != null)
        {
            cs = cs.clone();
        }
        else {
            cs = new CellStyle();
        }
        cs.setProperty(CellStyle.Property.PATTERN, inPattern);
        this.setCellStyle(cs, false);

        this.setCurrencyCode(null);

        //////chart regen marking handling
        updateChartReGenStatus(this);
//        ZiaManager.updateZIAStatus(this);
        this.resetMaxWidthHeightIndex();
    }
    
    private Type getValueTypeForODS()
    {
        Type valueType = this.getValue().getType();

        if (this.getValue().getType() == Type.FLOAT)
        {
            ZSPattern aPattern = this.getPattern(2, false);
            if (aPattern != null)
            {
                Type pat_Type = aPattern.getType();
                if (pat_Type == Type.PERCENTAGE
                    || pat_Type == Type.CURRENCY)
                {
                    valueType = pat_Type;
                }
                else if(pat_Type == Type.DATE || pat_Type == Type.TIME || pat_Type == Type.DATETIME)
                {
                    valueType = DateUtil.getDateType((Number)this.getValue().getValue());
                }
            }
        }

        return valueType;
    }

    @Override
    public String[] getAttributes()
    {
        Type type = getValueTypeForODS();


        String[] attrs = new String[]{"table:style-name",
            "table:content-validation-name",    //No I18N
            "table:formula",
            "office:value-type",
            "office:currency",
            (type == Type.DATE || type == Type.DATETIME)?"office:date-value"://No I18N
                type.equals(Type.TIME)?"office:time-value"://No I18N
                    type.equals(Type.BOOLEAN)?"office:boolean-value"://No I18N
                        type.equals(Type.STRING)?"office:string-value":"office:value",//No I18N
            "table:number-columns-spanned",
            "table:number-rows-spanned",
            "table:number-columns-repeated",
            "table:number-matrix-columns-spanned",
            "table:number-matrix-rows-spanned", //No I18N
            "table:ignore-type-error" //No I18N
        };



        return attrs;
    }

    private String getUpdatedLinkforWriter()
    {
        return EngineUtils1.getCellLinkForWriter(this.getRow().getSheet(), link);
    }

    @Override
    public String[] getValues(ZSPrintVisitor zsPrintVisitor, int repeated)
    {
        int[] spans = this.getRow().getSheet().getMergeCellSpans(this);

        Type vType = getValueTypeForODS();

        String valueStr = null;
        if(getValue().getValue() != null)
        {
            if(vType == Type.ERROR)
            {
                valueStr = "0";//No I18N
            }
            else if(vType == Type.DATE || vType == Type.TIME || vType == Type.DATETIME)
            {
                Format dateFormat = this.getSheet().getWorkbook().getDateFormatToWriteOds(vType);
                Object obj = this.getValue().getValue();
                if(obj instanceof Number)
                {
                    obj = DateUtil.convertNumberToDate((Number)obj);
                }
                valueStr = dateFormat.format(obj);
            }
            else if(vType == Type.STRING)
            {
                // String values will be always written as content.
                // As it may contain info like links or text formatting, it cannot be written to the value attribute.
                valueStr = null;
            }
            else
            {
                valueStr = getValue().getValue().toString();
            }
        }

        String formulaTemp = null;
        boolean hasNonExportableFormula = false;
        if(isFormula())
        {
            hasNonExportableFormula = this.getExpression().hasNonExportableFormula(this.getSheet().getWorkbook());
            if(!hasNonExportableFormula) {
                if(isFormula())
                {
                    formulaTemp = "="+FormulaUtil.getFormula(zsPrintVisitor, this.getExpression().getNode(), this.getRow().getSheet().getWorkbook(), this, this.getRowIndex(), this.getColumnIndex(), false);
                }
                else
                {
                    formulaTemp = getValueString(SpreadsheetSettings.defaultSpreadsheetSettings);
                }

                formulaTemp = "of:" + Utility.changeObjectToXMLFunctionNames(formulaTemp); // No I18N
            }
        }

        boolean isArrayCell = !hasNonExportableFormula && (isSignedArrayFormula() || !isAutoArrayError() && getAutoArrayRowSpan() * getAutoArrayColSpan() > 1);

        // the cell may have styleName as Default also.
        // this happens when we do setting cellStyle for a range of cells because
        // the cellStyle set for the range, instead of getting set for each of the cells in that range
        // gets set in the columnHeader, and the remaining cells which will not take this cellStyle geta styleName = Default.
        String values[] = new String[]{
            getStyleName() == null ? null : getStyleName(),

            getContentValidationName(),

            formulaTemp, //No I18N

            vType == Type.UNDEFINED ? null :
                (vType == Type.DATETIME ? Type.DATE : (vType == Type.ERROR || vType == Type.FRACTION || vType == Type.SCIENTIFIC) ? Type.FLOAT : vType).toString().toLowerCase(),

            //!this.getType().equals(Type.CURRENCY) || getCurrency() == null ? null : getCurrency().toString(),
            getCurrencyCode(),

            valueStr,

            (spans[0] > 1 || spans[1] > 1) ? String.valueOf(spans[1]) : null,

            (spans[0] > 1 || spans[1] > 1) ? String.valueOf(spans[0]) : null,

                repeated == 1 ? null : String.valueOf(repeated),

            isArrayCell ? String.valueOf(getAutoArrayColSpan()) : null,

            isArrayCell ? String.valueOf(getAutoArrayRowSpan()) : null,

            getIgnoreError() == 0 ? null : String.valueOf(getIgnoreError())
        };
        return values;
    }

    @Override
    public void getZSValues(int repeated, String expressionName, String[] valuesShell)
    {
        int[] spans = this.getRow().getSheet().getMergeCellSpans(this);

        Type vType = this.getValue().getType();

        String valueStr = null;
        if(getValue().getValue() != null)
        {
            if(vType == Type.ERROR)
            {
                valueStr = "0";//No I18N
            }
            else if(vType == Type.DATE || vType == Type.TIME || vType == Type.DATETIME)
            {
                DateFormat dateFormat = this.getSheet().getWorkbook().getDateFormatToWrite(vType);
                Object obj = this.getValue().getValue();
                if(obj instanceof Number)
                {
                    obj = DateUtil.convertNumberToDate((Number)obj);
                }
                valueStr = dateFormat.format(obj);
            }
            else if(vType == Type.STRING)
            {
                // String values will be always written as content.
                // As it may contain info like links or text formatting, it cannot be written to the value attribute.
                valueStr = null;
            }
            else
            {
                valueStr = getValue().getValue().toString();
            }
        }

        String picklistID = null;
        /* If Picklist has only one value we write as attribute, otherwise we write as <multi-value> tag inside the cell*/
        if(this.getValue() instanceof PicklistValue) {
            Picklist picklist= ((PicklistValue)this.getValue()).getPicklist();

            String idString = null;
            int itemCount = 0;
            int lastItemId = -1;
            List<Integer> ids = ((PicklistValue) this.getValue()).getItemIDs();
            for(int id: ids) {
                PicklistItem item = picklist.getItem(id);
                if(item == null) {
                    continue;
                }
                lastItemId = id;
                itemCount++;
            }

            if(itemCount == 1) {
                idString = String.valueOf(lastItemId);
            } else if(itemCount == 0) {
                idString = String.valueOf(-1);
            }

            if(idString != null) {
                picklistID = picklist.getId() + ":" + idString;
            }
        }

        String cellPcicklistID = null;
        if(this.getCellPicklists() != null && !this.getValue().equals(Value.EMPTY_VALUE)) {
            Collection<CellPicklist> cellPicklists = this.getCellPicklists();
            Workbook workbook = this.getRow().getSheet().getWorkbook();
            cellPcicklistID = "";
            for(CellPicklist cellPicklist: cellPicklists) {
                /* Check if picklist with the same ID exists in Workbook. Because on Picklist remove we will not iterate all the CellPicklist and remove. */
                if (workbook.getPicklist(cellPicklist.getPicklist().getId()) != null) {
                    cellPcicklistID = cellPcicklistID + cellPicklist.getPicklist().getId() + "";
                    if (cellPicklist.getItemID() != null && cellPicklist.getPicklist().getItem(cellPicklist.getItemID()) != null) {
                        cellPcicklistID = cellPcicklistID + ":" + cellPicklist.getItemID();
                    }
                    cellPcicklistID += ";";
                }
            }
            if(cellPcicklistID.length() == 0) {
                cellPcicklistID = null;
            }
            else {
                cellPcicklistID = cellPcicklistID.substring(0, cellPcicklistID.length()-1);
            }
        }

        String valueID = null;
        if(this.getValue() instanceof ValueWithID) {
            valueID = ((ValueWithID) this.getValue()).getID();
        }

        String mode=null,imageID=null,height=null,width=null;
        if(this.isImage())
        {
            ImageValue imageValue = (ImageValue)this.getValue();
            mode = imageValue.getMode().equals(ImageDisplayMode.FIT) ? null : imageValue.getMode().toString();
            imageID = String.valueOf(imageValue.getImageID());
            height = imageValue.getImageHeight() != -1 ? String.valueOf(imageValue.getImageHeight()) : null;
            width = imageValue.getImageWidth() != -1 ? String.valueOf(imageValue.getImageWidth()) : null;
        }
        // the cell may have styleName as Default also.
        // this happens when we do setting cellStyle for a range of cells because
        // the cellStyle set for the range, instead of getting set for each of the cells in that range
        // gets set in the columnHeader, and the remaining cells which will not take this cellStyle geta styleName = Default.

        valuesShell[0] = getStyleName() == null ? null : getStyleName();

        valuesShell[1] = getContentValidationName();

        valuesShell[2] = vType == Type.UNDEFINED ? null :
                (vType == Type.DATETIME ? Type.DATE : (vType == Type.ERROR || vType == Type.FRACTION || vType == Type.SCIENTIFIC) ? Type.FLOAT : vType).toString().toLowerCase();
        valuesShell[3] = valueStr;

        valuesShell[4] =   (spans[0] > 1 || spans[1] > 1) ? String.valueOf(spans[1]) : null;

        valuesShell[5] = (spans[0] > 1 || spans[1] > 1) ? String.valueOf(spans[0]) : null;

        valuesShell[6] = repeated == 1 ? null : String.valueOf(repeated);//getColsRepeated() == 1 ? null : String.valueOf(getColsRepeated()),

        valuesShell[7] =   isSignedArrayFormula() ? "1" : null;

        valuesShell[8] = this.getAutoArrayColSpan() == 1 ? null : String.valueOf(getAutoArrayColSpan());

        valuesShell[9] = this.getAutoArrayRowSpan() == 1 ? null : String.valueOf(getAutoArrayRowSpan());

        valuesShell[10] = getValue().isPrefixApos() ? "1" : null;

//            (this.pattern == DataStyleConstants.EMPTY_PATTERN && this.derivedPattern != null) ? "1" : null,

        valuesShell[11] = this.link;

        valuesShell[12] = (this.autoArrayError == null || this.autoArrayError.rowOffset == -1) ? null : String.valueOf(this.autoArrayError.rowOffset);

        valuesShell[13] = (this.autoArrayError == null || this.autoArrayError.colOffset == -1) ? null : String.valueOf(this.autoArrayError.colOffset);

        valuesShell[14] = mode;
        valuesShell[15] = imageID;
        valuesShell[16] = height;
        valuesShell[17] = width;
        valuesShell[18] = picklistID;
        valuesShell[19] = this.getIgnoreError() == 0 ? null : String.valueOf(this.getIgnoreError());
        valuesShell[20] = cellPcicklistID;
        valuesShell[21] = valueID;
        valuesShell[22] = expressionName;
    }

    /*
     * Returns the CellStyle object used by this Cell.
     */
    private CellStyle getCellStyle(boolean isGetCloned)
    {
        CellStyle cStyle = null;
        try
        {
            if (this.getStyleName() == null)
            {
                // check whether the column has some pattern
                ReadOnlyColumnHeader rCH = this.getRow().getSheet().getReadOnlyColumnHeader(this.getColumnIndex());
                ColumnHeader columnHeader = rCH.getColumnHeader();
                if (columnHeader != null)// && !columnHeader.getDefaultCellStyleName().equals("Default"))
                {
                    // Get uncloned cellstyle as we do clone befire returning if isGetCloned is true in this method.
                    cStyle = columnHeader.getCellStyleReadOnly();
                }
            }
            else if (!this.getStyleName().equals("Default"))
            {
                Workbook workbook = this.getRow().getSheet().getWorkbook();
                CellStyle cellStyle = workbook.getCellStyle(this.getStyleName());
                if (cellStyle == null)
                {
                    ////////////NULL Handling
                    //MailUtil.reportError("NA", "CellStyle found null. StyleName : "+this.getStyleName(), new NullPointerException("CellStyle(CellImpl) is Null")); //No I18N
                    //new NullPointerException("CellStyle(CellImpl) found null. StyleName : "+this.getStyleName()).printStackTrace(); //No I18N
                    LOGGER.log(Level.INFO, "Engine : CellStyle(CellImpl) found null. StyleName : {0}", this.getStyleName());

                    cellStyle = new CellStyle();
                    cellStyle.setStyleName(this.getStyleName());
                    cellStyle.setParenStyleName("Default"); //No I18N
                    workbook.addCellStyle(cellStyle);
                }
                cStyle = cellStyle;
            }
//            }
        }catch(Exception ex)
        {
            LOGGER.log(Level.WARNING,"Engine: ***Exception while getting CellStyle. This needs to be corrected. Cell : "+this+" :: Style Name : "+this.getStyleName(),ex);
        }

        if(isGetCloned && cStyle != null)
        {
            cStyle = cStyle.clone();
        }

        return cStyle;
    }

    /*
     * Returns the CellStyle object used by this Cell.
     */
    @Override
    public CellStyle getCellStyle()
    {
        return getCellStyle(true);
    }

    /*
     * Returns the CellStyle object used by this Cell.
     */
    public CellStyle getCellStyleReadOnly()
    {
        return getCellStyle(false);
    }

    @Override
    public boolean isEmpty()
    {
        return !(this.isImage()
            || this.isFormula()
            || this.getValue() instanceof PicklistValue
            || this.getValue().getValue() != null
            || this.getStyleName() != null
            || this.getContentValidationName() != null
            || this.getAnnotation() != null
            || this.getDrawControlList() != null || this.getRow().getSheet().getMergeCellDetails().get(this) != null) ;
    }

    private void setCellStyle(CellStyle cellStyle, boolean resetRepeatedCounts)
    {
        // set the used row/column index
        if (resetRepeatedCounts)
        {
            resetRepeatedCounts();
        }

        if (cellStyle != null)
        {
            //cellStyle.setStyleName("ce"+this.getCellRef());
            cellStyle.setStyleName("temp" + UUID.randomUUID());//No I18N
            cellStyle = this.getRow().getSheet().getWorkbook().checkAndAddCellStyle(cellStyle);
            setStyleName(cellStyle.getStyleName(), false);

        }
        else
        {
            setStyleName("Default", false); //No I18N
        }
    }

    @Override
    public void clearContent(boolean resetRepeatedCounts, boolean isUpdateCellEditHistory, boolean resetPicklistValue)
    {
        if(resetRepeatedCounts)
        {
            resetRepeatedCounts();
        }

        boolean isEmptyCell = !this.isFormula() && this.getType() == Type.UNDEFINED;
        if(this.isFormula())
        {
            removeFormula();
        }
        //setArraySpan(0, 0);
        this.link  = null;
        Value emptyValue = Value.EMPTY_VALUE;

        if(resetPicklistValue && this.getValue() instanceof PicklistValue) {
            PicklistValue picklistValue = (PicklistValue) this.getValue();
            emptyValue = picklistValue.getPicklist().getDefaultPicklistValue();
        }

        internalSetValue(emptyValue, false);

        if(isUpdateCellEditHistory && !isEmptyCell)
        {
            updateCellHistory(System.currentTimeMillis(), emptyValue);
        }
    }


    @Override
    public void clearStyle(boolean resetRepeatedCounts)
    {
        clearRichText(resetRepeatedCounts);

        if(resetRepeatedCounts)
        {
            resetRepeatedCounts();
        }

        setStyleName(EngineConstants.DEFAULT_CELLSTYLENAME, false);
        this.derivedPattern = DataStyleConstants.EMPTY_PATTERN;
        this.link = null;
    }

    @Override
    public void clearAnnotation()
    {
        // assume here the there will not be any repeated of rows/columns
        // hence no need to call the resetRepeatedCounts() method
        if(this.annotation != null){
            getSheet().removeAnnotedCell(this);
        }
        this.annotation = null;
    }

    @Override
    public void clearAll(boolean resetRepeatedCounts, boolean isUpdateCellEditHistory)
    {
        if(resetRepeatedCounts)
        {
            resetRepeatedCounts();
        }
        if(this.cellPicklists != null) {
            for(CellPicklist cellPicklist: this.getCellPicklists()) {
                cellPicklist.setValue(Value.EMPTY_VALUE, null, getSheet().getWorkbook()); // To remove the PicklistItem from Picklist.
            }
            this.cellPicklists = null;
        }
        clearContent(false, isUpdateCellEditHistory, false);
        clearStyle(false);
        //this.setAnnotation(null);
        // dont want to call resetRepeatedCount and setUsedIndex in setAnnotation method
        //this.annotation = null;
        clearAnnotation();
        removeAllAttachments();
//        this.setSpan(1, 1, false);
        this.setContentValidationName(null, false);
//        this.setColSpan(1, false);
//        this.setRowSpan(1, false);
    }

    @Override
    public void clearAll(boolean resetRepeatedCounts)
    {
        clearAll(resetRepeatedCounts, true);
    }

    @Override
    public void clearAllFromShift(boolean isSetUsedIndex)
    {
        clearAll(isSetUsedIndex, false);
    }

    public void clearForCutPaste(boolean resetRepeatedCounts)
    {
        if(resetRepeatedCounts)
        {
            resetRepeatedCounts();
        }

        this.cellPicklists = null;

        clearContent(false, true, false);

        this.setStyleName(null, resetRepeatedCounts);
        this.setContentValidationName(null, resetRepeatedCounts);
        //this.derivedPattern = null;//DataStyleConstants.EMPTY_PATTERN;
        this.link = null;
        //clearStyle(false);
        //this.setAnnotation(null);
        // dont want to call resetRepeatedCount and setUsedIndex in setAnnotation method
        //this.annotation = null;
        clearAnnotation();
        removeAllAttachments();
//        this.setSpan(1, 1, false);
//        this.setColSpan(1, false);
//        this.setRowSpan(1, false);
    }

    @Override
    public void clearRichText(boolean resetRepeatedCounts)
    {
        if(resetRepeatedCounts)
        {
            resetRepeatedCounts();
        }
        if(this.isZSString())
        {
            ZSString zsString = (ZSString) this.getValue().getRawValue();

            List<RichStringProperties> richStringPropertiesList = zsString.getProperties();
            if(!richStringPropertiesList.isEmpty()) {
                List<RichStringProperties> linksAndFields = new ArrayList();
                for (RichStringProperties property : richStringPropertiesList) {
                    if (property.getUrl() != null) {
                        RichStringProperties link = new RichStringProperties(null, property.getUrl(), property.getLabel(), property.getStartIndex());
                        linksAndFields.add(link);
                    } else if (property.getField() != null) {
                        RichStringProperties field = new RichStringProperties(null, null, property.getStartIndex(), property.getField(), null);
                        linksAndFields.add(field);
                    }
                }

                String baseStringValue = ((ZSString) this.getValue().getRawValue()).getBaseStringValue();
                boolean apostrophePrefixed = ((ZSString) this.getValue().getRawValue()).isApostrophePrefixed();
                zsString = new ZSString(baseStringValue, apostrophePrefixed, linksAndFields);
                Value value = Value.getInstance(Type.STRING, zsString);

                internalSetValue(value, false);
            }
        }
    }

    public void clearForMerge(HashMap<BorderStyleKey, String> borderStylesMap, CellStyle oldCellStyle, String cellTopBorder, String cellBottomBorder, String cellLeftBorder, String cellRightBorder, boolean isRootCell){
        if(isRootCell){
            resetRepeatedCounts();
        }







        CellStyle inherentCellStyle= oldCellStyle;
//        String inherentStyleName= (isRootCell&&oldCellStyle!=null)?oldCellStyle.getStyleName():null;
//        BorderStyleKey key= new BorderStyleKey(inherentStyleName, cellTopBorder, cellBottomBorder, cellLeftBorder, cellRightBorder);
//
//        String newStyleName= borderStylesMap.get(key);
//        if(newStyleName==null){
//            if(inherentStyleName==null && isBorderNone(cellTopBorder) && isBorderNone(cellBottomBorder) && isBorderNone(cellLeftBorder) && isBorderNone(cellRightBorder)){
//                newStyleName="null";
//            }
//            else{
//                CellStyle newCellStyle= (inherentStyleName==null?new CellStyle():oldCellStyle.clone());
//                newCellStyle.setStyleName("temp"+UUID.randomUUID());//No I18N
//                newCellStyle.setBorder(null);
//                newCellStyle.setBorderTop(cellTopBorder);
//                newCellStyle.setBorderBottom(cellBottomBorder);
//                newCellStyle.setBorderLeft(cellLeftBorder);
//                newCellStyle.setBorderRight(cellRightBorder);
//
//                this.getRow().getSheet().getWorkbook().addCellStyle(newCellStyle);
//
//                newStyleName= newCellStyle.getStyleName();
//            }
//            borderStylesMap.put(key, newStyleName);
//        }
//
//        this.setStyleName("null".equals(newStyleName)?null:newStyleName, false);
//
        setStyleForMerge(borderStylesMap, inherentCellStyle, cellTopBorder, cellBottomBorder, cellLeftBorder, cellRightBorder);
        if(!isRootCell){
            this.clearContent(false, true, false);
            this.clearAnnotation();
            removeAllAttachments();
            this.setContentValidationName(null, false);
        }
    }

    public void setStyleForMerge(HashMap<BorderStyleKey, String> borderStylesMap, CellStyle inherentCellStyle,String cellTopBorder, String cellBottomBorder, String cellLeftBorder, String cellRightBorder){
        String inherentStyleName= inherentCellStyle==null?null:inherentCellStyle.getStyleName();
        BorderStyleKey key= new BorderStyleKey(inherentStyleName, cellTopBorder, cellBottomBorder, cellLeftBorder, cellRightBorder);
        String newStyleName= borderStylesMap.get(key);
        if(newStyleName==null){
            if(inherentStyleName==null && isBorderNone(cellTopBorder) && isBorderNone(cellBottomBorder) && isBorderNone(cellLeftBorder) && isBorderNone(cellRightBorder)){
                newStyleName="null"; //No I18N
            }
            else{
                CellStyle newCellStyle= (inherentCellStyle==null?new CellStyle():inherentCellStyle.clone());
                newCellStyle.setStyleName("temp"+UUID.randomUUID()); //No I18N
                newCellStyle.setProperty(CellStyle.Property.BORDERTOP, BorderProperties.getInstance(cellTopBorder));
                newCellStyle.setProperty(CellStyle.Property.BORDERBOTTOM, BorderProperties.getInstance(cellBottomBorder));
                newCellStyle.setProperty(CellStyle.Property.BORDERLEFT, BorderProperties.getInstance(cellLeftBorder));
                newCellStyle.setProperty(CellStyle.Property.BORDERRIGHT, BorderProperties.getInstance(cellRightBorder));

                newCellStyle = this.getRow().getSheet().getWorkbook().checkAndAddCellStyle(newCellStyle);

                newStyleName= newCellStyle.getStyleName();
            }
            borderStylesMap.put(key, newStyleName);
        }

        this.setStyleName("null".equals(newStyleName)?null:newStyleName, false); //No I18N
    }

    public class BorderStyleKey{
        String topBorder;
        String bottomBorder;
        String leftBorder;
        String rightBorder;
        String inherentStyleName;

        public BorderStyleKey(String inherentStyleName, String topBorder, String bottomBorder, String leftBorder, String rightBorder){
            this.inherentStyleName= inherentStyleName;
            this.topBorder=topBorder;
            this.bottomBorder=bottomBorder;
            this.leftBorder=leftBorder;
            this.rightBorder=rightBorder;
        }

        //hope not necessary
        @Override
        public boolean equals(Object o){
            if(o instanceof BorderStyleKey){
                BorderStyleKey b= (BorderStyleKey) o;
                boolean equals= b.inherentStyleName==null?this.inherentStyleName==null:b.inherentStyleName.equalsIgnoreCase(this.inherentStyleName);
                equals = equals && ((isBorderNone(this.topBorder)==isBorderNone(b.topBorder)) || (b.topBorder==null?false:b.topBorder.equals(this.topBorder)));
                equals = equals && ((isBorderNone(this.bottomBorder)==isBorderNone(b.bottomBorder)) || (b.bottomBorder==null?false:b.bottomBorder.equals(this.bottomBorder)));
                equals = equals && ((isBorderNone(this.leftBorder)==isBorderNone(b.leftBorder)) || (b.leftBorder==null?false:b.leftBorder.equals(this.leftBorder)));
                equals = equals && ((isBorderNone(this.rightBorder)==isBorderNone(b.rightBorder)) || (b.rightBorder==null?false:b.rightBorder.equals(this.rightBorder)));
                return equals;
            }
            return false;
        }

        @Override
        public int hashCode() {
            int hash = 5;
            hash = 37 * hash + Objects.hashCode(this.inherentStyleName);
            hash = 37 * hash + Objects.hashCode("none".equalsIgnoreCase(this.topBorder)?null:this.topBorder); //No I18N
            hash = 37 * hash + Objects.hashCode("none".equalsIgnoreCase(this.bottomBorder)?null:this.bottomBorder); //No I18N
            hash = 37 * hash + Objects.hashCode("none".equalsIgnoreCase(this.leftBorder)?null:this.leftBorder); //No I18N
            hash = 37 * hash + Objects.hashCode("none".equalsIgnoreCase(this.rightBorder)?null:this.rightBorder); //No I18N
            return hash;
        }
    }

    private boolean isBorderNone(String border){
        return (border==null || "none".equalsIgnoreCase(border)); //No I18N
    }

    public void resetRepeatedCounts()
    {
        if(this.getRow() == null)
        {
            return;
        }
        // rows repeated handling
        if(this.getRow().getRowsRepeated() > 1)
        {
            // this will take care of setting the new rows repeated values
            // cols repeated taken care as we clone the cell also
            // check the implementation of getRow() from sheet for more details
            this.getRow().getSheet().getRow(this.getRowIndex()+1);
        }

        // Cols repeated handling
        if(this.getColsRepeated() > 1)
        {
            this.getRow().getCell(this.getColumnIndex()+1);
        }
        //////////////////////////////////
        this.getRow().getSheet().setIsModified(true);
        this.isEdited = true;
    }

    private void setUsedIndex()
    {
        // if this gets called from parser
        // then it the row will be null and hence we can return from here
        Sheet sheet = this.getRow().getSheet();
        int rowIndex = this.getRowIndex();
        int colIndex = this.getColumnIndex();

        int usedRowIndex = sheet.getUsedRowIndex();
        int usedColumnIndex = sheet.getUsedColumnIndex();
        if (rowIndex > usedRowIndex)
        {
            sheet.setUsedRowIndex(rowIndex);
            ////
        }

        if (colIndex > usedColumnIndex)
        {
            sheet.setUsedColumnIndex(colIndex);
            ////
        }

    }

//    private static final JFrame FRAME = new JFrame();
    private static float dpi = 96f; // as of now fixed dpi; ideally should come from client
//    static{
//        dpi = 96f; // as of now fixed dpi; ideally should come from client
//    }



    public void setCellHeight(Dimention cellHeight) {
        this.cellHeight = cellHeight;
    }

    public void setCellWidth(Dimention cellWidth) {
        this.cellWidth = cellWidth;
    }

    public Dimention getCellHeight()
    {
        return this.cellHeight;
    }

    public Dimention getCellWidth()
    {
        return this.cellWidth;
    }


    @Override
    public int getOptimalHeight()
    {
        if(cellHeight == null)
        {
            cellHeight = calculateOptimalRowHeight(true,  -1);
        }
        else
        {
            ReadOnlyColumnHeader rCH = this.getRow().getSheet().getReadOnlyColumnHeader(this.getColumnIndex());
            if(rCH.getColumnHeader() != null)
            {
                Timestamp colStyleChangeTimeStamp = rCH.getColumnHeader().getStyleChangeTimestamp();
                if(colStyleChangeTimeStamp == null || colStyleChangeTimeStamp.after(cellHeight.getTimestamp()))
                {
                    cellHeight = calculateOptimalRowHeight(true,  -1);
                }
            }
        }

        return cellHeight.getDimention();
    }

    /*
     * This method is used to take the decision on which side rendering (left / bottom)
     * For no_rotation or rotation_angle = 0 or rotation_angle = 90. It is clear we should render in left and bottom respectively.
     * The above case will be handled by this method.
     * For nonzero rotation angle, decision needs to be taken for rendering.
     */
    private Dimention calculateOptimalRowHeight(boolean isConsiderRotation, int colWidth)
    {
        int height = 0;

        boolean hasDefaultFont = false;
        boolean hasThemeFont = false;
        try
        {
            Workbook workbook = this.getRow().getSheet().getWorkbook();
            JFrame FRAME = workbook.jFRAME;
            CellStyle defaultCellStyle = workbook.getDefaultCellStyle();
            String defaultFontName = defaultCellStyle != null ? (String)defaultCellStyle.getProperty(TextStyle.Property.FONTNAME) : "";
            String defaultFontSize = defaultCellStyle != null ? (String)defaultCellStyle.getProperty(TextStyle.Property.FONTSIZE) : "";

            CellStyle cStyle = this.getCellStyleReadOnly();
            if(cStyle == null || cStyle.getStyleName() == null || cStyle.getStyleName().equals(EngineConstants.DEFAULT_CELLSTYLENAME))
            {
                hasDefaultFont = true;
                hasThemeFont = ZSFontScheme.isThemeFont(defaultFontName);
            }
            else
            {
                // GET FONT NAME TILL PARENT CELL STYLE
                String fontName = cStyle.getPropertyAsString_deep(TextStyle.Property.FONTNAME, this.getSheet().getWorkbook());
                String fontSize = cStyle.getPropertyAsString_deep(TextStyle.Property.FONTSIZE, this.getSheet().getWorkbook());
                hasDefaultFont = defaultFontName.equals(fontName) || defaultFontSize.equals(fontSize);
                hasThemeFont = ZSFontScheme.isThemeFont(fontName);
            }

            int rotationAngle = 0;
            int propRowHeight = 0;
            Font defaultFont = getFont(this.getRow().getSheet().getWorkbook(), cStyle, null);

            if(cStyle != null)
            {
                propRowHeight = getPropotionalRowHeight(defaultFont, FRAME);
                if(cStyle.getPropertyAsString_deep(CellStyle.Property.ROTATIONANGLE, workbook) != null)
                {
                    rotationAngle = Integer.parseInt(cStyle.getPropertyAsString_deep(CellStyle.Property.ROTATIONANGLE, workbook));
                    if(rotationAngle >= 270)
                    {
                        rotationAngle = rotationAngle - 360;
                    }
                    rotationAngle = Math.min(rotationAngle, 90);
                }
            }
            else
            {
                propRowHeight = this.getRow().getSheet().getWorkbook().getDefaultRowHeight();
            }

            int rowSpan = 1;
            if(this.isMergeParent)
            {
                int[] spans = this.getRow().getSheet().getMergeCellSpans(this);
                rowSpan = spans[0];
            }

            boolean picklistBubble  = false;
            boolean isMultiValue = false;

            if(this.getValue() instanceof PicklistValue) {
                PicklistValue picklistValue = (PicklistValue) this.getValue();
                picklistBubble = picklistValue.getPicklist().isShowAsBubble();
                isMultiValue = picklistValue.getPicklist().isAllowMultiSelect();
            }

            if(this.getValue() instanceof MultiValue) {
                picklistBubble = ((MultiValue) this.getValue()).isShowAsBubble();
                isMultiValue = true;
            }

            boolean isWrapContent = this.isOfType(Type.STRING) && isWrapped(this.getRow().getSheet().getWorkbook(), cStyle, this.getValue());
            String cellContent = (rotationAngle != 0
                                        || (this.isOfType(Type.STRING) && (isWrapContent || ((ZSString)this.getValue().getRawValue()).isMultiLine() || ((ZSString)this.getValue().getRawValue()).containsField() || ((ZSString)this.getValue().getRawValue()).hasStyle()))) ? this.getContentWithFieldNode() : null;
            if (rowSpan == 1 && (cellContent == null || cellContent.equals("")))
            {
                return new Dimention(Math.max(propRowHeight, workbook.getDefaultRowHeight())+ (picklistBubble ? EngineConstants.BUBBLE_HEIGHT_PADDING : 0), new Timestamp(System.currentTimeMillis()), hasDefaultFont, hasThemeFont);//height < EngineConstants.DEFAULT_ROW_HEIGHT ? EngineConstants.DEFAULT_ROW_HEIGHT : height;
            }



            if (colWidth == -1) {
                colWidth = getActualCellWidth();
                String indent = cStyle == null ? null : cStyle.getPropertyAsString_deep(ParagraphStyle.Property.ZSINDENT, workbook);
                if(indent != null && !"none".equals(indent))
                {
                    int indentCount = Integer.parseInt(indent);
                    int indentWidth = EngineConstants.ZSINDENT * indentCount; //Applied Indent width
                    colWidth = colWidth - indentWidth;
                }
                colWidth = Math.max(colWidth - 5, 5);// cell padding deduction 2px left, right each and 1px for border
            }
            //colWidth -= 5; // cell padding deduction 2px left, right each and 1px for border

            if(rotationAngle == 0 || !isConsiderRotation || picklistBubble)
            {
                Map<Font, Integer> fontHeights = new HashMap<>();
                Map<Font, FontMetrics> fontMetrics = null;
                List<RichStringProperties> richStringProperties = this.getValue().getRawValue() instanceof ZSString ? ((ZSString)this.getValue().getRawValue()).getProperties() : new ArrayList<>();
                for(RichStringProperties richText : richStringProperties)
                {
                    TextStyle textStyle = workbook.getTextStyle(richText.getStyleName());
                    if(textStyle != null) {
                        String fontName = (String) textStyle.getProperty(TextStyle.Property.FONTNAME);
                        if (!hasThemeFont) {
                            hasThemeFont = ZSFontScheme.isThemeFont(fontName);
                        }
                    }
                    Font richFont = getFont(workbook, cStyle, textStyle);
                    int fontHeight = getPropotionalRowHeight(richFont, FRAME);
                    fontHeights.put(richFont, fontHeight);
                    if(isWrapContent)
                    {
                        fontMetrics = fontMetrics == null ? new HashMap<>() : fontMetrics;
                        FontMetrics richFontM = FRAME.getFontMetrics(richFont);
                        fontMetrics.put(richFont, richFontM);
                    }
                }
                /* Default height and metrics*/
                fontHeights.put(defaultFont, propRowHeight);
                if(isWrapContent)
                {
                    fontMetrics = fontMetrics == null ? new HashMap<>() : fontMetrics;
                    FontMetrics metrics = FRAME.getFontMetrics(defaultFont);
                    fontMetrics.put(defaultFont, metrics);
                }

                if(cellContent == null)
                {
                    cellContent = "";
                }

                if(picklistBubble) {
                    String[] words;
                    boolean verticalStacking;
                    if(this.getValue() instanceof PicklistValue) {
                        PicklistValue picklistValue = (PicklistValue) this.getValue();
                        words = picklistValue.getAsWords();
                        verticalStacking = picklistValue.getPicklist().getStackDirection() == Picklist.StackDirection.VERTICAL;
                    }
                    else {
                        MultiValue multiValue = (MultiValue) this.getValue();
                        words = multiValue.getAsWords();
                        verticalStacking = multiValue.getStackDirection() == Picklist.StackDirection.VERTICAL;
                    }

                    int combinedWidth = 0;
                    int combinedHeight = 0;

                    if(!isWrapContent || verticalStacking) {
                        if(verticalStacking) {
                            combinedHeight = words.length * (fontHeights.get(defaultFont) + EngineConstants.BUBBLE_HEIGHT_PADDING);
                        }
                        else {
                            combinedHeight = fontHeights.get(defaultFont) + EngineConstants.BUBBLE_HEIGHT_PADDING;
                        }
                    }
                    else {
                        boolean newLine = true;
                        for (String word : words) {
                            int wordWidth = fontMetrics.get(defaultFont).stringWidth(word);
                            wordWidth += EngineConstants.BUBBLE_SPACING;
                            wordWidth += defaultFont.getSize();

                            if (combinedWidth + wordWidth > colWidth) {
                                combinedHeight += fontHeights.get(defaultFont) + EngineConstants.BUBBLE_HEIGHT_PADDING;
                                combinedWidth = wordWidth;
                            } else {
                                if (newLine) {
                                    combinedHeight += fontHeights.get(defaultFont) + EngineConstants.BUBBLE_HEIGHT_PADDING;
                                    newLine = false;
                                }
                                combinedWidth += wordWidth;
                            }
                        }
                    }

                    if(combinedHeight == 0) {
                        combinedHeight = fontHeights.get(defaultFont);
                    }

                    height += combinedHeight;
                }
                else {
                    StringTokenizer lines = new StringTokenizer(cellContent, "\n\r", true);//No I18N
                    int begin = 0;
                    int richIndex = 0;
                    int lineHeight = 0;
                    int offset = 0; // FieldNode text is appended to content, so the rich string property's indices should be offset by the length of the field node.

                    while (lines.hasMoreTokens()) {
                        String line = lines.nextToken();

                        // No sure as why not rowSizeRatio incremented for this condition. Need to analyze.
                        if (line.equals("\r") || line.equals("\n\r")) {
                            continue;
                        }

                        if (line.equals("\n")) {
                            begin++;
                            height += (lineHeight == 0 ? propRowHeight : lineHeight);
                            lineHeight = 0;
                            continue;
                        }

                        int end = 0;
                        int tokenSize = 0;
                        boolean isSpaceEncountered = false;

                        while (end < line.length()) {
                            Font font = defaultFont;
                            RichStringProperties richText = null;
                            if (richIndex < richStringProperties.size()) {
                                richText = richStringProperties.get(richIndex);
                                if (end == richText.getStartIndex() + offset - begin) {
                                    TextStyle textStyle = workbook.getTextStyle(richText.getStyleName());
                                    font = getFont(workbook, cStyle, textStyle);
                                }
                            }

                            boolean fieldNode = false;
                            int oldOffset = offset;
                            if (!isWrapContent) {
                                if (richText == null) {
                                    end = line.length();
                                } else {
                                    if (end == richText.getStartIndex() + offset - begin) {
                                        offset += richText.getField() != null ? richText.getField().getNameLength() : 0;
                                        end = richText.getEndIndex() + offset - begin + 1;
                                    } else {
                                        end = Math.min(richText.getStartIndex() + offset - begin, line.length());
                                    }
                                }
                                /* Field Node will be inside a bubble, so need to add the bubble's height padding */
                                int textHeight = fontHeights.get(font) + (richText != null && richText.getField() != null ? EngineConstants.BUBBLE_HEIGHT_PADDING : 0);
                                if (picklistBubble) {
                                    textHeight += EngineConstants.BUBBLE_HEIGHT_PADDING;
                                }
                                lineHeight = Math.max(lineHeight, textHeight);
                            } else {
                                int subContentEnd;


                                if (richText == null) {
                                    subContentEnd = line.length();
                                } else {
                                    if (end == richText.getStartIndex() + offset - begin) {
                                        offset += richText.getField() != null ? richText.getField().getNameLength() : 0;
                                        int richTextEnd = richText.getField() == null ? richText.getEndIndex() : richText.getStartIndex() + offset - 1;
                                        subContentEnd = richTextEnd - begin + 1;
                                        fieldNode = richText.getField() != null;
                                    } else {
                                        subContentEnd = Math.min(richText.getStartIndex() + offset - begin, line.length());
                                    }
                                }

                                String subContent = line.substring(end, subContentEnd);

                                int contentHeight = fontHeights.get(font);
                                /* Field Node will be inside a bubble, so need to add the bubble's height padding */
                                if (richText != null && richText.getField() != null && end == richText.getStartIndex() + oldOffset - begin) {
                                    contentHeight += EngineConstants.BUBBLE_HEIGHT_PADDING;
                                }

                                /* For FieldNode, always take the entire word, since it should not be split into multiple lines. */
                                String[] words;
                                if (fieldNode || picklistBubble) {
                                    words = new String[]{subContent};
                                } else {
                                    words = subContent.split("((?<= )|(?= ))");
                                }

                                for (String word : words) {
                                    if (word.equals(" ")) {
                                        isSpaceEncountered = true;
                                    }

                                    int wordEnd = end + word.length();

                                    while (end < wordEnd) {
                                        /* For FieldNode, always take the entire word, since it should not be split into multiple lines. */
                                        String subWord = isSpaceEncountered || fieldNode ? word : String.valueOf(line.charAt(end));
                                        int size = fontMetrics.get(font).stringWidth(subWord);

                                        if (richText != null && richText.getField() != null && end == richText.getStartIndex() + oldOffset - begin) {
                                            size += fontMetrics.get(font).getHeight();
//                                        size += EngineConstants.BUBBLE_WIDTH_PADDING;
                                        }

                                        if (tokenSize + size > colWidth && tokenSize == 0 && fieldNode) {
                                            /* For fieldNode, the entire text should be inside the bubble.
                                             *  So we should not break into multiple characters.
                                             *  So if FieldNode is at the start of the line and it exceeds the width, it should just be displayed without wrapping. */
                                            lineHeight = contentHeight;
                                            end += subWord.length();
                                        } else if (tokenSize + size > colWidth) {
                                            if (tokenSize == 0) {
                                                end++;
                                                lineHeight = contentHeight;
                                            }
                                            height += lineHeight;
                                            lineHeight = tokenSize = 0;
                                            isSpaceEncountered = false;
                                        } else {
                                            tokenSize += size;
                                            end = end + subWord.length();
                                            lineHeight = Math.max(lineHeight, contentHeight);
                                        }
                                    }
                                }

                            }

                            if (richText != null) {
                                int richTextEndIndex = fieldNode ? richText.getStartIndex() + oldOffset + richText.getField().getNameLength() - 1 : richText.getEndIndex() + offset;
                                if (end > richTextEndIndex - begin) {
                                    richIndex++;
                                }
                            }
                        }
                        begin += line.length();
                    }
                    height += (lineHeight == 0? propRowHeight : lineHeight);
                }
            }
            else
            {
                if(rotationAngle == 90 || rotationAngle == -90)
                {
                    height = calculateOptimalWidth(false,  isWrapContent ? colWidth : -1, getActualRowHeight());
                }
                else
                {
                    int bottomRenderHeight = calculateOptimalWidth(false,  isWrapContent ? colWidth : -1, getActualRowHeight());
                    if(getRotatedHeightOrWidth(false, rotationAngle, isWrapContent, bottomRenderHeight, cellContent) > colWidth)
                    {
                        height = getRotatedHeightOrWidth(true, rotationAngle, isWrapContent, colWidth, cellContent);
                    }
                    else
                    {
                        height = Math.abs((int) Round.roundUp(bottomRenderHeight * Math.sin(Math.toRadians(rotationAngle)), 0)) + Math.abs((int) Round.roundUp(propRowHeight * Math.cos(Math.toRadians(rotationAngle)), 0));
                    }
                }
            }
            if (isConsiderRotation) {

                ///// This is to skip the rowHeight of mergeChildCells
                //int[] spans = this.getRow().getSheet().getMergeCellSpans(this);
                //int rowSpan = spans[0];
                for(int i = 1; i < rowSpan; i++)
                {
                    ReadOnlyRow roRow = this.getRow().getSheet().getReadOnlyRowFromShell(this.getRowIndex() + i);
                    int repeated = Math.min(roRow.getRowsRepeated(), rowSpan - 1);
                    int rowHeight = roRow.getRow() != null ? roRow.getRow().getRowHeight() : workbook.getDefaultRowHeight();

                    height -= (rowHeight * repeated);
                    i += (repeated - 1);
                }
            }
        }
        catch(Exception e)
        {
            LOGGER.log(Level.SEVERE, "Exception while calculating optimal row height", e);
        }
        return new Dimention(Math.max(height, this.getRow().getSheet().getWorkbook().getDefaultRowHeight()),  new Timestamp(System.currentTimeMillis()), hasDefaultFont, hasThemeFont);//height < EngineConstants.DEFAULT_ROW_HEIGHT ? EngineConstants.DEFAULT_ROW_HEIGHT : height;
    }

    private int getActualCellWidth()
    {
        int colWidth = 0;

        int[] spans = this.getRow().getSheet().getMergeCellSpans(this);
        int colSpan = spans[1];
        for(int i = 0; i < colSpan; i++)
        {
            ReadOnlyColumnHeader rCH = this.getRow().getSheet().getReadOnlyColumnHeader(this.getColumnIndex() + i);
            int repeated = Math.min(rCH.getColsRepeated(), colSpan);
            int width = rCH.getColumnHeader() != null ? rCH.getColumnHeader().getColWidth() : this.getRow().getSheet().getWorkbook().getDefaultColumnWidth();

            colWidth += (width * repeated);
            i += (repeated - 1);
        }
        return colWidth;
    }

    private int getTextWidth(String word, int begin, FontMetrics defaultFontM, Map<RichStringProperties, FontMetrics> fontMetrices)
    {
        int width = 0;
        int start = 0;
        int end = word.length() - 1;
        int offset = 0;  // Offset due to appending the Field Name to content.
        for(Map.Entry<RichStringProperties, FontMetrics> entry : fontMetrices.entrySet())
        {
            RichStringProperties richText = entry.getKey();
            int richStart = offset + richText.getStartIndex() - begin;
            offset += (richText.getField() != null ? richText.getField().getNameLength() : 0);
            int richEnd = offset + richText.getEndIndex() - begin;
            if(richEnd >= start && richStart <= end)
            {
                String subContent = word.substring(start, Math.max(richStart, start));
                width += defaultFontM.stringWidth(subContent);
                subContent = word.substring(richStart, Math.min(richEnd, end) + 1);
                width += entry.getValue().stringWidth(subContent);
                start = Math.min(richEnd, end) + 1;
                if(richText.getField() != null) {
                    width += entry.getValue().getAscent() + entry.getValue().getDescent();
                }
            }
        }
        if(start <= end)
        {
            String subContent = word.substring(start);
            width += defaultFontM.stringWidth(subContent);
        }
        return width;
    }

    private int getActualRowHeight()
    {
        int rowHeight = 0;
        ///// This is to skip the rowHeight of mergeChildCells
        int[] spans = this.getRow().getSheet().getMergeCellSpans(this);
        int rowSpan = spans[0];
        for(int i = 0; i < rowSpan; i++)
        {
            ReadOnlyRow roRow = this.getRow().getSheet().getReadOnlyRowFromShell(this.getRowIndex() + i);
            int repeated = Math.min(roRow.getRowsRepeated(), rowSpan);
            int height = roRow.getRow() != null ? roRow.getRow().getRowHeight() : this.getRow().getSheet().getWorkbook().getDefaultRowHeight();

            rowHeight += (height * repeated);
            i += (repeated - 1);
        }
        return rowHeight;
    }

    @Override
    public int getOptimalWidth()
    {
        if(cellWidth == null)
        {
            cellWidth = new Dimention(calculateOptimalWidth(true,  -1, getActualCellWidth()), new Timestamp(System.currentTimeMillis()));
        }
        else
        {
            ReadOnlyColumnHeader rCH = this.getRow().getSheet().getReadOnlyColumnHeader(this.getColumnIndex());
            if(rCH.getColumnHeader() != null)
            {
                Timestamp colStyleChangeTimestamp = rCH.getColumnHeader().getStyleChangeTimestamp();
                if(colStyleChangeTimestamp == null || colStyleChangeTimestamp.after(cellWidth.getTimestamp()))
                {
                    cellWidth = new Dimention(calculateOptimalWidth(true,  -1, getActualCellWidth()), new Timestamp(System.currentTimeMillis()));
                }
            }
        }
        return cellWidth.getDimention();
    }
    private int calculateOptimalWidth(boolean isConsiderRotation, int rowHeight, int marker) {
        Workbook workbook = this.getRow().getSheet().getWorkbook();
        int width = workbook.getDefaultColumnWidth(); // 80
        String content = this.getContentWithFieldNode();

        if (content != null && !content.equals(""))
        {
            CellStyle cStyle = this.getCellStyleReadOnly();
            int rotationAngle = 0;
            if(cStyle != null && cStyle.getPropertyAsString_deep(CellStyle.Property.ROTATIONANGLE, workbook) != null)
            {
                rotationAngle = Integer.parseInt(cStyle.getPropertyAsString_deep(CellStyle.Property.ROTATIONANGLE, workbook));
                if(rotationAngle >= 270)
                {
                    rotationAngle = rotationAngle - 360;
                }
                rotationAngle = Math.min(rotationAngle, 90);
            }

            ///////////
            if(rowHeight == -1)
            {
                rowHeight = getActualRowHeight();
            }
            boolean isWrapContent = this.isOfType(Type.STRING) && isWrapped(this.getRow().getSheet().getWorkbook(), cStyle, this.getValue());
            // Get the font size set for the text
            Font font = getFont(this.getRow().getSheet().getWorkbook(), cStyle, null);
            width = calculateCellContentWidth(content);

            boolean hasPicklistBubble = false;
            if(this.getValue() instanceof PicklistValue) {
                PicklistValue picklistValue = (PicklistValue) this.getValue();
                hasPicklistBubble = picklistValue.getPicklist().isShowAsBubble();
            }

            if (isWrapContent)
            {
                    if(isConsiderRotation && !hasPicklistBubble)
                {
                    RowStyle rStyle = this.getRow().getRowStyleReadOnly();
                    if ((boolean)rStyle.getProperty(RowStyle.Property.USEOPTIMALROWHEIGHT) && rotationAngle != 90 && rotationAngle != -90)
                    {
                        ReadOnlyColumnHeader rCH = this.getRow().getSheet().getReadOnlyColumnHeader(this.getColumnIndex());
                        int colWidth = rCH.getColumnHeader() != null ? rCH.getColumnHeader().getColumnWidth() : this.getRow().getSheet().getWorkbook().getDefaultColumnWidth();
                        if(colWidth < width)
                        {
                            return colWidth;
                        }
                    }

                    int actualHeightOnNoWrap = getRotatedHeightOrWidth(true, 0,false, marker, content);
                    /* CONTENT CAN BE WRAPPED */
                    if(actualHeightOnNoWrap < rowHeight)
                    {
                        int optimalRowHeight = calculateOptimalRowHeight(false,  marker).getDimention();
                        if(optimalRowHeight <= rowHeight)
                        {
                            width = Math.min(marker, width);
                        }
                        else
                        {
                            for(int count = 0 ; count < 3 ; count++)
                            {
                                int extraArea = marker * (optimalRowHeight - rowHeight);
                                marker += extraArea / rowHeight;
                                optimalRowHeight = calculateOptimalRowHeight(false,  marker).getDimention();

                                if(optimalRowHeight < rowHeight)
                                {
                                    break;
                                }

                                if(count == 2 && optimalRowHeight > rowHeight)
                                {
                                    marker += 10;
                                }
                            }
                            width = marker;
                        }
                    }
                }
            }

            if(isConsiderRotation && !hasPicklistBubble)
            {
                if(rotationAngle == 90 || rotationAngle == -90)
                {
                    width = calculateOptimalRowHeight(false,  isWrapContent ? rowHeight : -1).getDimention();
                }
                else if(rotationAngle != 0)
                {
                    int leftRenderedWidth = width;//calculateOptimalWidth(false, isWrapContent ? rowHeight : -1, getActualCellWidth());
                    if(getRotatedHeightOrWidth(true, rotationAngle, isWrapContent, leftRenderedWidth, content) > rowHeight)
                    {
                        width = getRotatedHeightOrWidth(false, rotationAngle, isWrapContent, rowHeight, content);
                    }
                    else
                    {
                        width = Math.abs((int) Round.roundUp(leftRenderedWidth * Math.cos(Math.toRadians(rotationAngle)),0)) + Math.abs((int) Round.roundUp(getPropotionalRowHeight(font, workbook.jFRAME) * Math.sin(Math.toRadians(rotationAngle)),0));
                    }
                }

                //// This is to skip the ColWidth of mergeChildCells
                int[] spans = this.getRow().getSheet().getMergeCellSpans(this);
                int colSpan = spans[1];
                for(int i = 1; i < colSpan; i++)
                {
                    ReadOnlyColumnHeader rCH = this.getRow().getSheet().getReadOnlyColumnHeader(this.getColumnIndex() + i);
                    int repeated = Math.min(rCH.getColsRepeated(), colSpan - 1);
                    int colWidth = rCH.getColumnHeader() != null ? rCH.getColumnHeader().getColumnWidth() : workbook.getDefaultColumnWidth();

                    width -= (colWidth * repeated);
                    i += (repeated - 1);
                }

                String indent = cStyle == null ? null : cStyle.getPropertyAsString_deep(ParagraphStyle.Property.ZSINDENT, workbook);
                if(indent != null && !"none".equals(indent))
                {
                    int indentCount = Integer.parseInt(indent);
                    width = width + EngineConstants.ZSINDENT * indentCount; //Appied Indent width
                }
                ///////////////////////////////////////////
            }
            width += 9; // Cell Padding of left and right each 2px and 1px for border
            if(this.getValue() instanceof PicklistValue) { // For Picklist Icon
                Picklist picklist = ((PicklistValue) this.getValue()).getPicklist();
                if(picklist.showDropdownIcon() == Picklist.DropdownStyle.ALWAYS) {
                    JFrame FRAME = this.getSheet().getWorkbook().jFRAME;
                    FontMetrics fontM = FRAME.getFontMetrics(font);
                    width += (int) Math.round((font.getSize()*72f)/dpi*1.3) + 2;
//                    width += EngineConstants.PICKLIST_ICON_WIDTH;
                }
            }
        }
        return width;
    }


    public int calculateCellContentWidth(String cellContent)
    {
        int width = 0;//this.getRow().getSheet().getWorkbook().getDefaultColumnWidth(); // 80
        CellStyle cStyle = this.getCellStyleReadOnly();

        // Get the font size set for the text
        Font font = getFont(this.getRow().getSheet().getWorkbook(), cStyle, null);
        JFrame FRAME = this.getSheet().getWorkbook().jFRAME;
        FontMetrics fontM = FRAME.getFontMetrics(font);

        /* For PicklistValue, no need to go with further calculation, since there won't be any rich string properties. */
        if(this.getValue() instanceof PicklistValue) {
            PicklistValue picklistValue = (PicklistValue) this.getValue();
            Picklist picklist = picklistValue.getPicklist();
            List<PicklistItem> picklistItems = picklistValue.getPicklistItems();
            for(PicklistItem item: picklistItems) {
                if(item.getDisplayValue().isPresent()) {
                    int itemWidth = fontM.stringWidth(item.getDisplayValue().get());
                    if(picklist.isShowAsBubble()) {
                        itemWidth += font.getSize();
//                        width += Math.round((font.getSize()*72f)/dpi);
                    }

                    if(picklistValue.getPicklist().getStackDirection() == Picklist.StackDirection.VERTICAL) {
                        width = Math.max(width, itemWidth);
                    }
                    else {
                        width += itemWidth;
                    }
                }
            }
            if(picklistItems.size() > 1) {
                width += (picklistItems.size()-1) * EngineConstants.BUBBLE_SPACING;
            }
            if(width > 0) {
                return width;
            }
        }
        if(this.getValue() instanceof MultiValue) {
            MultiValue multiValue = (MultiValue) this.getValue();
            List<MultiValue.BubbleValue> list = multiValue.getValueList();
            for(MultiValue.BubbleValue bubbleValue: list) {
                int itemWidth = fontM.stringWidth(bubbleValue.getDisplayString()) + font.getSize();
                if(multiValue.getStackDirection() == Picklist.StackDirection.VERTICAL) {
                    width = Math.max(width, itemWidth);
                }
                else {
                    width += itemWidth;
                }
            }
            if(list.size() > 1) {
                width += (list.size()-1) * 3;
            }

            if(width > 0) {
                return width;
            }
        }
        //int fSize = font.getSize();
        //int contentLength = content.length();
        if(cellContent == null || cellContent.equals(""))
        {
            return this.getRow().getSheet().getWorkbook().getDefaultColumnWidth(); // 80;
        }

        List<RichStringProperties> richStringProperties = this.getValue().getRawValue() instanceof ZSString ? ((ZSString)this.getValue().getRawValue()).getProperties() : new ArrayList<>();
        Map<RichStringProperties, FontMetrics> fontMetrices = new LinkedHashMap<>();
        Workbook workbook = this.getRow().getSheet().getWorkbook();
        for(RichStringProperties richText : richStringProperties)
        {
            TextStyle textStyle = workbook.getTextStyle(richText.getStyleName());
            Font richFont = getFont(workbook, cStyle, textStyle);
            FontMetrics richFontM = FRAME.getFontMetrics(richFont);
            fontMetrices.put(richText, richFontM);
        }

        int begin = 0;
        for(String contentStr : cellContent.split("\n"))
        {
            width = Math.max(width, getTextWidth(contentStr, begin, fontM, fontMetrices));
            begin += contentStr.length();
            begin++; //For new line char
        }
        if(this.getValue() instanceof PicklistValue) {
            Picklist picklist = ((PicklistValue) this.getValue()).getPicklist();
            if(picklist.isShowAsBubble()) {
                width += ((PicklistValue) this.getValue()).getPicklistItems().size() * EngineConstants.PICKLIST_BUBBLE_WIDTH_PADDING;
            }
        }
        if(this.getValue() instanceof MultiValue) {
            width += ((MultiValue) this.getValue()).getValueList().size() * EngineConstants.PICKLIST_BUBBLE_WIDTH_PADDING;
        }
        return width;
    }

    public static boolean isWrapped(Workbook workbook, CellStyle cStyle, Value cellValue) {
        if(cStyle == null) {
            cStyle= workbook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME);
        }

        //If cell has picklist bubble, then data should not wrap.
        if(cellValue instanceof PicklistValue) {
            PicklistValue picklistValue = (PicklistValue) cellValue;
            Picklist picklist = picklistValue.getPicklist();
            if(picklist.isShowAsBubble() && !picklist.isAllowMultiSelect()) {
                return false;
            }
        }

        CellStyleProperties.DisplayType wrapOptionValue = (CellStyleProperties.DisplayType) cStyle.getProperty_Deep(CellStyle.Property.DISPLAYTYPE, workbook);

        return wrapOptionValue != null && CellStyleProperties.DisplayType.WRAP.equals(wrapOptionValue);
    }

    public static boolean hasRotation(Workbook workbook, CellStyle cStyle)
    {
        if(cStyle == null)
        {
            cStyle = workbook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME);
        }
        return cStyle.getProperty_Deep(CellStyle.Property.ROTATIONANGLE, workbook) != null && Integer.parseInt(cStyle.getPropertyAsString_deep(CellStyle.Property.ROTATIONANGLE, workbook)) != 0;
    }

    public static Font getFont(Workbook workbook, Style cStyle, Style richStyle) {
        try{
            Map<String, String> fontPropsMap = getFontPropsMap(workbook, cStyle);
            if(richStyle != null)
            {
                if(richStyle.getProperty(TextStyle.Property.FONTNAME) != null)
                {
                    fontPropsMap.put("fontName", richStyle.getPropertyAsString(TextStyle.Property.FONTNAME, workbook.getTheme()));
                }
                if(richStyle.getProperty(TextStyle.Property.FONTSTYLE) != null)
                {
                    fontPropsMap.put("fontStyle", richStyle.getPropertyAsString(TextStyle.Property.FONTSTYLE, workbook.getTheme()));
                }
                if(richStyle.getProperty(TextStyle.Property.FONTSIZE) != null)
                {
                    fontPropsMap.put("fontSize", richStyle.getPropertyAsString(TextStyle.Property.FONTSIZE, workbook.getTheme()));
                }
            }
            return getFont(fontPropsMap);
        } catch(Exception e) {
            // CATCHING ANY EXCEPTION HERE
            // SHOULD KEEP CHECKING REGULARLY AND REMOVE
            LOGGER.log(Level.WARNING, "Engine: Exception in getFont() for CellStyle: " + cStyle, e);
            if(!EngineConstants.DEFAULT_CELLSTYLENAME.equals(cStyle.getStyleName())) {
                CellStyle defaultCellStyle = workbook.getCellStyle(EngineConstants.DEFAULT_CELLSTYLENAME);
                Map<String, String> fontPropsMap = getFontPropsMap(workbook, defaultCellStyle);
                return getFont(fontPropsMap);
            }
            throw e;
        }
    }


    private static final Map<String, Font> PROPS_TO_FONTMAP = new ConcurrentHashMap<>();
    public static Font getFont(Map<String, String> fontPropsMap){
        String fontStyle = fontPropsMap.get("fontStyle");
        String fontName = fontPropsMap.get("fontName");
        String strFSize = fontPropsMap.get("fontSize");

        String propsKey = fontStyle+"-"+fontName+"-"+strFSize;
        Font font = PROPS_TO_FONTMAP.get(propsKey);

        if(font == null) {
            int fStyle = Font.PLAIN;
            if (fontPropsMap.get("fontStyle") != null) {
                switch (fontPropsMap.get("fontStyle")) {
                    case "bold"://No I18N
                        fStyle = Font.BOLD;
                        break;
                    case "italic"://No I18N
                        fStyle = Font.ITALIC;
                        break;
                }
            }

            // For "em" and "rem" units, we were throwing exception and returning default font. Maintaining the same behaviour.
            if (strFSize == null || strFSize.contains("em") || strFSize.equals("x-small")) {
                strFSize = EngineConstants.DEFAULT_FONT_SIZE_OLD;
            }

            int fSize;
            try {
                fSize = getPointSize(Float.parseFloat(strFSize.replace("pt", ""))); //No I18N
            }catch(NumberFormatException nfe)
            {
                LOGGER.log(Level.INFO, "Engine: Exception in getFont() for CellStyle - 2: "+strFSize, nfe);
                strFSize = EngineConstants.DEFAULT_FONT_SIZE_OLD;
                fSize = getPointSize(Float.parseFloat(strFSize.replace("pt", ""))); //No I18N
            }
            font = new Font(fontName, fStyle, fSize);
            PROPS_TO_FONTMAP.put(propsKey, font);
        }

        return font;
    }

    private static Map<String, String> getFontPropsMap(Workbook workbook, Style cStyle){

        if(cStyle == null) {
            cStyle= workbook.getDefaultCellStyle();
        }

        Map<String, String> fontPropsMap = new HashMap<>();
        String fontName = cStyle.getPropertyAsString_deep(TextStyle.Property.FONTNAME, workbook);
        FontFace tempFontFace = workbook.getFontFace(fontName);
        if (tempFontFace != null) {
            fontName = tempFontFace.getFontFamily();
        }
        fontPropsMap.put("fontName", fontName);
        fontPropsMap.put("fontStyle", cStyle.getPropertyAsString_deep(TextStyle.Property.FONTSTYLE, workbook));
        fontPropsMap.put("fontSize", cStyle.getPropertyAsString_deep(TextStyle.Property.FONTSIZE, workbook));

        return fontPropsMap;
    }

    public static int getPointSize(float size)
    {
        return Math.round(size*dpi/72f); // used float for more correct precision
    }

    public static int getPropotionalRowHeight(Font font, JFrame frame)
    {
        FontMetrics fm = frame.getFontMetrics(font);
        return fm.getHeight() + Math.max(fm.getHeight() * 2/16, 2);
        //float lineGap = height*0.11f;
        //return Math.round(height+lineGap);
//	int fontSize = font.getSize();
////	if(fontSize < 11)
////	{
////	    return EngineConstants.DEFAULT_ROW_HEIGHT; // 17
////	}
//
//	switch(fontSize)
//	{
//	    case 10:
//		return EngineConstants.DEFAULT_ROW_HEIGHT; // 17
//	    case 11:
//		return EngineConstants.DEFAULT_ROW_HEIGHT+2; //19
//	    case 12:
//		return EngineConstants.DEFAULT_ROW_HEIGHT+4; // 21
//	    case 14:
//		return EngineConstants.DEFAULT_ROW_HEIGHT+8; // 25
//	    case 16:
//		return EngineConstants.DEFAULT_ROW_HEIGHT+11; // 28
//	    case 18:
//		return EngineConstants.DEFAULT_ROW_HEIGHT+14; // 31
//	    case 20:
//		return EngineConstants.DEFAULT_ROW_HEIGHT+17; //34
//	    case 22:
//		return EngineConstants.DEFAULT_ROW_HEIGHT+19; //36
//	    case 24:
//		return EngineConstants.DEFAULT_ROW_HEIGHT+22; //39
//	    case 26:
//		return EngineConstants.DEFAULT_ROW_HEIGHT+26; //43
//	    default:
//	    {
//		FontMetrics fm = frame.getFontMetrics(font);
//		fontSize = fm.getHeight() + (fm.getDescent() * 2);
//		return fontSize;
//	    }
//	}
    }

    /*
     * This method returns the rotated content height or width depending on the boolean isHeight...
     * For height calculation this method render content in left side and calculate the height produced by the rendered content
     * For width calculation this method render content in bottom side and calculate the width produced by the rendered content.
     */
    private int getRotatedHeightOrWidth(boolean isHeight, int rotationAngle, boolean isWrapContent, int markerForWrap, String cellContent)
    {
        Workbook workbook = this.getRow().getSheet().getWorkbook();
        CellStyle cStyle = this.getCellStyleReadOnly();

        int propRowHeight = 0;
        Font defaultFont = getFont(this.getRow().getSheet().getWorkbook(), cStyle, null);
        JFrame FRAME = this.getSheet().getWorkbook().jFRAME;
        propRowHeight = getPropotionalRowHeight(defaultFont, FRAME);

        int dimension = 0;
        StringTokenizer lines = new StringTokenizer(cellContent, "\n\r", true);//No I18N
        int begin = 0;
        int richIndex = 0;
        int lineHeight = 0;

        Map<Font, Integer> fontHeights = new HashMap<>();
        Map<Font, FontMetrics> fontMetrics = null;
        List<RichStringProperties> richStringProperties = this.getValue().getRawValue() instanceof ZSString ? ((ZSString)this.getValue().getRawValue()).getProperties() : new ArrayList<>();
        for(RichStringProperties richText : richStringProperties)
        {
            TextStyle textStyle = workbook.getTextStyle(richText.getStyleName());
            Font richFont = getFont(workbook, cStyle, textStyle);
            int fontHeight = getPropotionalRowHeight(richFont, FRAME);
            fontHeights.put(richFont, fontHeight);
            if(isWrapContent)
            {
                fontMetrics = fontMetrics == null ? new HashMap<>() : fontMetrics;
                FontMetrics richFontM = FRAME.getFontMetrics(richFont);
                fontMetrics.put(richFont, richFontM);
            }
        }
        /* Default height and metrics*/
        fontHeights.put(defaultFont, propRowHeight);
        if(isWrapContent)
        {
            fontMetrics = fontMetrics == null ? new HashMap<>() : fontMetrics;
            FontMetrics metrics = FRAME.getFontMetrics(defaultFont);
            fontMetrics.put(defaultFont, metrics);
        }

        int rotatedWrapMarker;
        if(isHeight)
        {
            rotatedWrapMarker = Math.abs((int) Round.roundUp(markerForWrap / Math.cos(Math.toRadians(rotationAngle)), 0));
        }
        else
        {
            rotatedWrapMarker = Math.abs((int) Round.roundUp(markerForWrap / Math.sin(Math.toRadians(rotationAngle)), 0));
        }

        while (lines.hasMoreTokens())
        {
            String line = lines.nextToken();

            // No sure as why not rowSizeRatio incremented for this condition. Need to analyze.
            if (line.equals("\r") || line.equals("\n\r"))
            {
                continue;
            }

            if (line.equals("\n"))
            {
                begin++;
                if(isHeight)
                {
                    dimension += Math.abs((int) Round.roundUp((lineHeight == 0? propRowHeight : lineHeight) / Math.cos(Math.toRadians(rotationAngle)), 0));
                }
                else
                {
                    dimension += Math.abs((int) Round.roundUp((lineHeight == 0? propRowHeight : lineHeight) / Math.sin(Math.toRadians(rotationAngle)), 0));
                }
                lineHeight = 0;
                continue;
            }

            int end = 0;
            int tokenSize = 0;
            boolean isSpaceEncountered = false;

            while(end < line.length())
            {
                Font font = defaultFont;
                RichStringProperties richText = null;
                if(richIndex < richStringProperties.size())
                {
                    richText = richStringProperties.get(richIndex);
                    if(end == richText.getStartIndex()-begin)
                    {
                        TextStyle textStyle = workbook.getTextStyle(richText.getStyleName());
                        font = getFont(workbook, cStyle, textStyle);
                    }
                }

                if(!isWrapContent)
                {
                    end = richText == null ? line.length() : (end == richText.getStartIndex()-begin ? richText.getEndIndex()-begin+1 : Math.min(richText.getStartIndex()-begin, line.length()));
                    lineHeight = Math.max(lineHeight, fontHeights.get(font));
                }
                else
                {
                    int subContentEnd = richText == null ? line.length() : (end == richText.getStartIndex()-begin ? richText.getEndIndex()-begin+1 : Math.min(richText.getStartIndex()-begin, line.length()));
                    String subContent = line.substring(end, subContentEnd);
                    int contentHeight = fontHeights.get(font);
                    StringTokenizer words = new StringTokenizer(subContent, " ", true);//No I18N
                    while (words.hasMoreTokens())
                    {
                        String word = words.nextToken();

                        if(word.equals(" "))
                        {
                            isSpaceEncountered = true;
                        }

                        int wordEnd = end + word.length();
                        while(end < wordEnd)
                        {
                            String subWord = isSpaceEncountered ? word : String.valueOf(line.charAt(end));
                            int size = fontMetrics.get(font).stringWidth(subWord);

                            if(tokenSize+size > rotatedWrapMarker)
                            {
                                if(tokenSize == 0)
                                {
                                    end++;
                                    lineHeight = contentHeight;
                                }
                                if(isHeight)
                                {
                                    dimension += Math.abs((int) Round.roundUp(lineHeight / Math.cos(Math.toRadians(rotationAngle)), 0));
                                }
                                else
                                {
                                    dimension += Math.abs((int) Round.roundUp(lineHeight / Math.sin(Math.toRadians(rotationAngle)), 0));
                                }
                                lineHeight = tokenSize = 0;
                                isSpaceEncountered = false;
                            }
                            else
                            {
                                tokenSize += size;
                                end = end + subWord.length();
                                lineHeight = Math.max(lineHeight, contentHeight);
                            }
                        }
                    }
                }
                if(richText != null && end > richText.getEndIndex()-begin)
                {
                    richIndex++;
                }
            }
            begin += line.length();
        }


        if(isHeight)
        {
            dimension += Math.abs((int) Round.roundUp((lineHeight == 0? propRowHeight : lineHeight) / Math.cos(Math.toRadians(rotationAngle)), 0));
            dimension += Math.abs((int) Round.roundUp((isWrapContent ? markerForWrap : calculateCellContentWidth(cellContent))* Math.sin(Math.toRadians(rotationAngle)), 0));
        }
        else
        {
            dimension += Math.abs((int) Round.roundUp((lineHeight == 0? propRowHeight : lineHeight) / Math.sin(Math.toRadians(rotationAngle)), 0));
            dimension += Math.abs((int) Round.roundUp((isWrapContent ? markerForWrap : calculateCellContentWidth(cellContent))* Math.cos(Math.toRadians(rotationAngle)), 0));
        }

        return dimension;
    }


    @Override
    public Expression getExpression()
    {
        if(this.isFormula()){
            return ((ExpressionValue)value).getExpression();
        }
        return null;
    }


    @Override
    public void setContentValidationName(String inContentValidationName)
    {
        setContentValidationName(inContentValidationName, true);
    }

    public void setContentValidationName(String inContentValidationName, boolean resetRepeatedCounts)
    {
        if(resetRepeatedCounts)
        {
            resetRepeatedCounts();
        }
        this.contentValid=null;      //resetting for dv apply/edit/delete
        this.contentValidationName = inContentValidationName;
    }

    public void setContentValidationNameFromParser(String inContentValidationName)
    {
        setContentValidationName(inContentValidationName, false);
    }

    @Override
    public String getContentValidationName()
    {
        return this.getRow().getSheet().getWorkbook().getCVCorrectedName(this.contentValidationName);
    }

    public ContentValidation getContentValidationReadOnly()
    {
        return getContentValidation(false);
    }

    // Will return a cloned object.
    @Override
    public ContentValidation getContentValidation()
    {
        return getContentValidation(false);
    }

    private ContentValidation getContentValidation(boolean isCloned)
    {
        ContentValidation cv = null;
        if(this.getContentValidationName() != null)
        {
            cv =  this.getRow().getSheet().getWorkbook().getContentValidation(this.getContentValidationName());
            if(isCloned && cv != null)
            {
                cv = cv.clone();
            }
        }

        return cv;
    }
    ///////////////////////////////////////////////////////////////////////////////

    @Override
    public Locale getFunctionLocale()
    {
        return this.getRow().getSheet().getWorkbook().getSpreadsheetSettings().getLocale();
    }

    @Override
    public Locale getAccountsLocale()
    {
        return this.getRow().getSheet().getWorkbook().getAccountsLocale();
    }

    @Override
    public SpreadsheetSettings getSpreadsheetSettings()
    {
        return this.getRow().getSheet().getWorkbook().getSpreadsheetSettings();
    }

    public boolean validateContent()
    {
        ContentValidation cv = this.getContentValidationReadOnly();
        if(cv != null)
        {
            if(this.getValue() == null || this.getValue().getValue() == null)
            {
                if(cv.isAllowEmptyCell())
                {
                    return (this.contentValid=true);
                }
                else
                {
                    return (this.contentValid=false);
                }
            }


            //     DVErrorMessage errMsg = cv.getErrorMessage();
            //     if((errMsg != null && errMsg.isDisplay())  )
            //      {

            return (this.contentValid=cv.validate(this));
            //     }
        }

        return (this.contentValid=true);
    }

    public boolean isSignedAsFormulaCell()
    {
        return isSigned%10 == 1;
    }

    public boolean isSignedAsFieldCell()
    {
        return isSigned%100 >= 10;
    }

    public void signAsFormulaCell()
    {
        isSigned = isSigned +  1;
    }

    public void unsignAsFormulaCell()
    {
        isSigned = isSigned - 1;
    }

    public void signAsFieldCell()
    {
        isSigned = isSigned + 10;
    }

    public void unSignAsFieldCell()
    {
        isSigned = isSigned - 10;
    }

    protected void dismantle() {
        this.dependents = null;
        this.annotation = null;
        this.attachments = null;
        this.drawControlList = null;
        this.row = null;
        this.column = null;
    }

    @Override
    public long getValueTimestamp()
    {
        return this.valueTimestamp;
    }

    public boolean isImage()
    {
        return this.getValue() != null ? this.getValue() instanceof ImageValue : false;
    }

    public boolean isZSString()
    {
        return this.getValue() != null && this.getValue().getRawValue() instanceof ZSString;
    }

    public boolean isEdited() {
        // Many contnuous empty cells were getting written leading to memory issue. 
        // This will solve that issue.
        if(!this.isFormula() && this.getValue().getType() == Type.UNDEFINED)
        {
            return true;
        }
        return isEdited;
    }

    public void setEdited(boolean edited) {
        isEdited = edited;
    }

    public void resetMaxWidthHeightIndex()
    {
        this.getRow().setMaxHeightColIndex(-1);
        ReadOnlyColumnHeader readOnlyColumnHeader = this.getSheet().getReadOnlyColumnHeader(this.getColumnIndex());
        if(readOnlyColumnHeader.getColumnHeader() != null)
        {
            readOnlyColumnHeader.getColumnHeader().setMaxWidthRowIndex(-1);
        }
    }

    @Override
    public ImportRange.SYNC_STATE getImportrangeSyncState() {
        return this.importrangeSyncState;
    }

    public void setImportrangeSyncState(ImportRange.SYNC_STATE importrangeSyncState) {
        this.importrangeSyncState = importrangeSyncState;
    }

    public int getStringLength() {
        if(this.value != null) {
            Value value1 = this.value.getValue();
            if(value1 == null) {
                return 0;
            }
            return value1.getStringLength();
        }
        return 0;
    }
}
