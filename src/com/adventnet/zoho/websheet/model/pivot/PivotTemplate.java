//$Id$
/**
 * 
 */
package com.adventnet.zoho.websheet.model.pivot;

import com.adventnet.zoho.websheet.model.util.EngineUtils;

import java.util.HashMap;

/**
 * <AUTHOR>
 * 
 */
public class PivotTemplate {

	public static enum Element {// templateelement
		WHOLE_TABLE, REPORTFILTERLABELS, REPORT_FILTER_VALUES, FIRST_COLUMN_STRIPE, SECOND_COLUMN_STRIPE, FIRST_ROW_STRIPE, SECOND_ROW_STRIPE, FIRST_COLUMN, HEADER_ROW, FIRST_HEADER_CELL, SUBTOTAL_COLUMN_1, SUBTOTAL_COLUMN_2, SUBTOTAL_COLUMN_3, BLANK_ROW, SUBTOTAL_ROW_1, SUBTOTAL_ROW_2, SUBTOTAL_ROW_3, COLUMN_SUBHEADING_1, COLUMN_SUBHEADING_2, COLUMN_SUBHEADING_3, ROW_SUBHEADING_1, ROW_SUBHEADING_2, ROW_SUBHEADING_3, GRAND_TOTAL_COLUMN, GRAND_TOTAL_ROW,
		// ---
		SUBTOTALCELL_ROW_1,SUBTOTALCELL_ROW_2,SUBTOTALCELL_ROW_3, SUBTOTALCELL_COLUMN_1,SUBTOTALCELL_COLUMN_2,SUBTOTALCELL_COLUMN_3, DATA_RANGE,
		
		HEADER_BLOCK_ROW_1,HEADER_BLOCK_ROW_2,HEADER_BLOCK_ROW_3,HEADER_BLOCK_COLUMN_1,HEADER_BLOCK_COLUMN_2,HEADER_BLOCK_COLUMN_3
	}

	public static class FStyle {
		public static final int NONE = -1;
		public static final int REGULAR = 0;
		public static final int BOLD = 1;
		public static final int ITALIC = 2;
		public static final int BOLDITALIC = 3;
	}

	public static class BStyle {
		public static final String DEFAULT_TYPE = "solid"; // NO I18N
		public static final String DEFAULT_SIZE = "0.0138in"; // NO I18N
		public static final String THICK_TYPE = "solid"; // NO I18N
		public static final String THICK_SIZE = "0.0346in"; // NO I18N
		public static final String DOUBLE_TYPE = "double"; // NO I18N
		public static final String DOUBLE_SIZE = "0.0555in"; // NO I18N
		public static final String THICK_DOUBLE_TYPE = "solid"; // NO I18N
		public static final String THICK_DOUBLE_SIZE = "0.0346in"; // NO I18N

	}

	int templateConstant;
	HashMap<Element, ElementStyle> styleMap = new HashMap<Element, ElementStyle>();
	

	public PivotTemplate(int templateConstant) {
		this.templateConstant = templateConstant;
	}

	public HashMap<Element, ElementStyle> getStyleMap() {
		return this.styleMap;
	}

	public int getTemplateConstant() {
		return this.templateConstant;
	}

	public void addStyle(Element element, ElementStyle elementStyle) {
		this.styleMap.put(element, elementStyle);
	}
}
