/*$Id$*/

package com.adventnet.zoho.websheet.model.pivot;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;
import com.adventnet.zoho.websheet.model.util.DataRange;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.Join;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.ds.query.SelectQuery;
import com.adventnet.ds.query.SelectQueryImpl;
import com.adventnet.ds.query.Table;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.FormRange;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.WorkbookContainer;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.ActionUtil;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.chart.ChartUtils;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.sheet.util.WebFormUtils;

/**
 * <AUTHOR>
 *
 */

/*Check following before release
 * ZUID is in hardcode ~Mani
 * */
public class FormReportUtils {
	public static final int FORM_REPORT_LIMIT = 10;
	private static final Logger LOGGER = Logger.getLogger(FormReportUtils.class.getName());
	public static JSONObjectWrapper getRecommendedPivot(WorkbookContainer container, JSONObjectWrapper actionJson, String docOwner)throws Exception {
		
		PivotComponent pComp =null;
		JSONObjectWrapper pivotJson = new JSONObjectWrapper();
		
		Workbook workbook = container.getWorkbook(null);
		 //FormRange formRange = formSheet.getFormRange();
		 //int rowIndex = formRange.getEndRowIndex()-1;
			
		 String zuid = actionJson.getString("zuid");
		 
		 String formRid = actionJson.getString(JSONConstants.FORM_RID);
		 Row webFormRow = WebFormUtils.readFormInfo(formRid, docOwner);
		 if (webFormRow != null) {
			  Long	 documentId = (Long) webFormRow.get("DOCUMENT_ID");
			  
			 String formSheetName = WebFormUtils.getSheetName(documentId.toString(), formRid, docOwner);
			 Sheet formSheet = workbook.getSheet(formSheetName);


			 JSONObjectWrapper fieldInfoObj = null;
				String fieldInfo = (String) webFormRow.get("FIELD_INFO");
				if(fieldInfo!=null){
					fieldInfoObj = new JSONObjectWrapper(fieldInfo);
				}
				 
				String fieldMapInfo = (String) webFormRow.get("FIELD_MAP_INFO");
			 	JSONObjectWrapper fieldMappingInfo = new JSONObjectWrapper(fieldMapInfo);
				JSONArrayWrapper sortedFieldArray = getSortedFieldArray(fieldMappingInfo, fieldInfoObj);
				
				ArrayList possiblePivotFields  = new ArrayList();
				
				ArrayList<Integer> waitingPossiblePivotFields  = new ArrayList();
			 	JSONObjectWrapper fieldChoices  = new JSONObjectWrapper();
				
				for(int i=1;i<sortedFieldArray.length();i++){
					
					//LOGGER.info("fieldSortedArray: "+sortedFieldArray.getString(i)+" :: "+sortedFieldArray.getString(i).toLowerCase());
					if(sortedFieldArray.getString(i).toLowerCase().contains("dropdown") || sortedFieldArray.getString(i).toLowerCase().contains("radio")){
						possiblePivotFields.add(i);
						JSONArrayWrapper choices = (JSONArrayWrapper)fieldInfoObj.get(sortedFieldArray.getString(i));
						fieldChoices.put(""+i, choices);
					}
					if(sortedFieldArray.getString(i).toLowerCase().contains("checkbox") || sortedFieldArray.getString(i).toLowerCase().contains("multiplechoice")){
						waitingPossiblePivotFields.add(i);
					}
				}
				possiblePivotFields.add(0);//Timestamp is default
				if(possiblePivotFields.size()<FORM_REPORT_LIMIT){
					for(int i : waitingPossiblePivotFields){
						possiblePivotFields.add(i);
					}
				}
				
				LOGGER.info("allPossiblePivotFields: "+possiblePivotFields);
				if(possiblePivotFields.size()>0){

					JSONArrayWrapper pivotList = new JSONArrayWrapper();
					JSONObjectWrapper pivotTableAndChartJson = null;
					int i=0;
					String recommendedpivotTableName = "RPilot_"+zuid+"_"+formSheet.getAssociatedName()+"_"; // No I18N
					workbook.removeRecommendedPivotTables(recommendedpivotTableName);
	            	 ArrayList<JSONObjectWrapper> createPivotJsonList = getCreatePivotJsonObj(workbook, formSheet, null, possiblePivotFields, fieldChoices, zuid);
					 for(JSONObjectWrapper pivotActionJson : createPivotJsonList){
						 pivotTableAndChartJson = new JSONObjectWrapper();
						LOGGER.info("actionJson: ==>"+pivotActionJson);
						
						 //PivotTable pivotTable = ActionUtil.createRecommendedPivotModel(workbook, pivotActionJson);
						 //pComp = pivotTable.getPivotComponent();
						pComp  = ActionUtil.createRecommendedPivotModel(workbook, pivotActionJson, false);
						 JSONObjectWrapper pivot = pComp.populatePivotGrid(workbook);
						 pivotTableAndChartJson.put("p"+i, pivot);
						 
						// String chartTitle = (String)((JSONArray)pivot.get(0)).get(0);
		    		
						 pivotTableAndChartJson.put("c"+i, ChartUtils.getPivotChartDetails( formSheet, pivotActionJson.getString(JSONConstants.ID)));
						 LOGGER.info("========completed====> ");
						 /*PivotGrid pivotGrid = new PivotGrid();
						 pivotGrid.setPivotComponent(pivotTable.getPivotComponent());
						 pivotGrid.populatePivotModel();*/
							 i++;
							 pivotList.put(pivotTableAndChartJson);
					 }
					 LOGGER.info("========createPivotJsonList====> ");
		          
					 pivotJson.put(JSONConstants.PIVOT_DETAILS,pivotList);
				}else{
					throw new Exception();
				}
				
			}
		 LOGGER.info("pivotJson: "+pivotJson);
		 return pivotJson;
		 
	}
	/*public static ArrayList createFormReports(WorkbookContainer container, Sheet reportSheet,  JSONObject actionJson, String docOwner)throws Exception {
		Workbook workbook = container.getWorkbook(null);
		ArrayList destRangeList = null;
		 String formRid = actionJson.getString(JSONConstants.FORM_RID);
		Sheet	formSheet = workbook.getSheetByAssociatedName(actionJson.getString(JSONConstants.ASSOCIATED_SOURCE_SHEET_NAME));
		 LOGGER.info("formRid: "+formRid);
		 JSONArray selectedReport = actionJson.getJSONArray("selectedReport");// No I18N
		
                
                FormRange formRange = formSheet.getFormRange();
       		 int responseCount = formRange.getEndRowIndex()-1;
                
                Cell cell = reportSheet.getCell(0, 0);
                ActionUtil.setCellValue(cell, "Total Responses");// No I18N
                cell = reportSheet.getCell(0, 1);
                ActionUtil.setCellValue(cell, ""+responseCount);
                
                List<String> styleClasses = new ArrayList<>();
                styleClasses.add("TextStyle");// No I18N
                styleClasses.add("TextStyle");// No I18N

                List<String> propNames = new ArrayList<>();
                propNames.add("fontWeight");// No I18N
                propNames.add("fontSize");// No I18N

                List<String> propValues = new ArrayList<>();
                propValues.add("bold");// No I18N
                propValues.add("18pt");// No I18N
                
                RangeUtil.setCellStyleProperty(reportSheet, 0, 0, 0, 1, styleClasses, propNames, propValues);
                ActionUtil.calculateOptimalRowHeight(reportSheet, 0,0, true);
                
                //WebFormUtils.updateReportSheetName(docOwner, formRid, reportSheet.getAssociatedName());
                JSONArray jsonArrPivotList = new JSONArray();
                int pivotEndRowIndex = 0;
                destRangeList = new ArrayList(selectedReport.length());
                for(int i=0;i<selectedReport.length();i++){
                	
                	String recommendedpivotTableName = "RecommendedPilot_"+formSheet.getAssociatedName()+"_"+selectedReport.getInt(i); // No I18N
                	String newPivotName = ActionUtil.generateNewPivotName(workbook);
                	PivotTable pivotTable = workbook.updateRecommededPivotTable(recommendedpivotTableName, newPivotName);
                	//LOGGER.info("pivotTable : "+pivotTable);
                	PivotComponent pComp = pivotTable.getPivotComponent();
                	PivotTargetGrid pivotTargetGrid = new PivotTargetGrid(reportSheet, i==0?3:pivotEndRowIndex+12,0);
                	pComp.setPivotTargetGrid(pivotTargetGrid);
                	
                	pComp.constructPivotTable(actionJson); 
                	pComp.populatePivotModel();
                	Range dataRange = pComp.getPivotTargetCellRange();
                	destRangeList.add(dataRange);
					pivotEndRowIndex = dataRange.getEndRowIndex();
					jsonArrPivotList.put(newPivotName);
					 int uniqueSize = pivotTable.getPivotComponent().getRowGridList().size();
					 String chartTitle = null;
					 
						 chartTitle = ""+formRange.getValue(0, pComp.getRowList().get(0).getColIndex());
		    		  
                	createPivotChart(container, pivotTable.getTargetCellRange(), pivotTable.getName(), chartTitle, uniqueSize);
                	
                }
                
                actionJson.set(JSONConstants.CREATEPIVOTID_ARRAY, jsonArrPivotList);
                //actionJson.set(JSONConstants.FORMREPORT_SHEET_NAME, reportSheet.getAssociatedName());
       // }
                return destRangeList;
	}*/

	public static String getReportSheetName( String docOwner, String formRid) {
		 String reportSheetName = null;
		try{
			 
			 Row webFormRow = WebFormUtils.readFormInfo(formRid, docOwner);
			
			 if (webFormRow != null) {
				 reportSheetName = (String) webFormRow.get("REPORT_ASSOCIATE_NAME");
			 }
			
		}catch(Exception e){
			LOGGER.log(Level.WARNING,"Exception : ",e.getMessage());
		}
		return reportSheetName;
	}
	
	public static void updateReportSheet(WorkbookContainer container, Sheet formSheet,  JSONObjectWrapper formActionJson, String docOwner) {
		try{
			String formRid = formActionJson.getString(JSONConstants.FORM_RID);
        	//String reportSheetName = FormReportUtils.getReportSheetName(container.getDocOwner(), formRid);
			Row webFormRow = WebFormUtils.readFormInfo(formRid, docOwner);
			if(webFormRow != null){
				
				 //FormRange formRange = formSheet.getFormRange();
				 //int rowIndex = formRange.getEndRowIndex()-1;
				
				String reportSheetName = (String) webFormRow.get("REPORT_ASSOCIATE_NAME");
				LOGGER.info("reportSheetName: "+reportSheetName);
				 if (reportSheetName != null) {
					 
					 String zuid = formActionJson.getString("zuid");
					 Workbook workbook = container.getWorkbook(null);
					/* String fieldInfo = (String) webFormRow.get("FIELD_INFO");
						JSONObject fieldInfoObj = new JSONObject(fieldInfo);
						 
						String fieldMapInfo = (String) webFormRow.get("FIELD_MAP_INFO");
						JSONObject fieldMappingInfo = new JSONObject(fieldMapInfo);
						//JSONArray sortedFieldArray = getSortedFieldArray(fieldMappingInfo, fieldInfoObj);
						*/
						
						 List<PivotTable> pivotTableList = workbook.getPivotTables();
						 Sheet reportSheet = null;
				            if(pivotTableList != null){
								JSONArrayWrapper jsonArrPivotList = new JSONArrayWrapper();
								JSONObjectWrapper actionJson = null;
				                for(PivotTable pivotTable : pivotTableList){
				                	if(pivotTable.getSourceCellRange().getAssociatedSheetName().equals(formSheet.getAssociatedName())){
				                		reportSheet = workbook.getSheetByAssociatedName(pivotTable.getTargetCellRange().getAssociatedSheetName());
				                		
				                		actionJson = getJsonObjforAction(formSheet, zuid);
				                		actionJson.put(JSONConstants.ID, pivotTable.getName());
				                		try{
				                			  Range dataRange = null;//PivotUtil.changePivotSource(container, workbook, actionJson);
				                			  
//				                			  LOGGER.info("Now lastUsedRowIndex is: "+lastUsedRowIndex);
							            }catch(Exception e){
							            	LOGGER.info(" Exception e  "+e);
							            }
				                		jsonArrPivotList.put(actionJson.getString(JSONConstants.ID));
				                	}
				                }
				                formActionJson.set(JSONConstants.UPDATEPIVOTID_ARRAY, jsonArrPivotList);
				            }
				            FormRange formRange = formSheet.getFormRange();
				            int responseCount = formRange.getEndRowIndex()-1;
				           
				            Cell cell = reportSheet.getCell(0, 1);
			                ActionUtil.setCellValue(cell, ""+responseCount);
				            formActionJson.set(JSONConstants.FORMREPORT_SHEET_NAME, reportSheet.getAssociatedName());
					}
			}
			
			 
			 
					
		}catch(Exception e){
			e.printStackTrace();
		}
		
	}
	

	public static void createPivotChart(WorkbookContainer container, Workbook workbook, DataRange targetCellRange, String pivotId, String chartTitle, int uniqueCount) {
		 	Sheet pivotTargetSheet = workbook.getSheetByAssociatedName(targetCellRange.getAssociatedSheetName());
			JSONObjectWrapper actionJson = getJsonObjforPivotChartAction( workbook,  pivotId, targetCellRange, chartTitle, uniqueCount);
			
			try{
			ChartUtils.createNewChart(container,  pivotTargetSheet, actionJson, false, false, true);
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	public static JSONObjectWrapper getJsonObjforPivotChartAction(Workbook workbook, String pivotId,  DataRange targetCellRange, String chartTitle, int uniqueCount){
		JSONObjectWrapper jObj = null;
		
		jObj = new JSONObjectWrapper();
		jObj.put(JSONConstants.ASSOCIATED_SOURCE_SHEET_NAME, targetCellRange.getAssociatedSheetName());
		jObj.put("chartId", "");
		if(uniqueCount <= 7){
			 jObj.put("chartType", 	"PIE");
		}else{
			 jObj.put("chartType", 	"COL");
		}
		
	        jObj.put("dataRange", 	pivotId);	
	        jObj.put("frl",  		true);
	        jObj.put("fcl", 		true);
	        jObj.put("seriesIn", 		"COLS");
	        int sr = targetCellRange.getStartRowIndex();
	        int sc = targetCellRange.getEndColIndex()+5;
	        jObj.put("sr",sr);	
	        jObj.put("sc",sc);	
	        jObj.put("srd",1);
	        jObj.put("scd",1);
	        
	        int left = workbook.getDefaultColumnWidth() * sc;
	        int top = workbook.getDefaultRowHeight() * sr;
	        jObj.put("left",left);
	        jObj.put("top",top);
	        jObj.put("wd",500);
	        jObj.put("ht",250);	
	        
	       
	        
	        jObj.put("title",chartTitle);
	        jObj.put("subTitle","");	
	        jObj.put("xAxisTitle","");
	        jObj.put("yAxisTitle","");
	        
	        jObj.put("tooltip",true);
	        jObj.put("zoom","xy");	
	        jObj.put("animation",true);
	        jObj.put("tl",false);
	        jObj.put("isFloat",false);
	        
	        jObj.put("majorGridLineWidth",1);
	        jObj.put("minorTickInterval","");
	        jObj.put("xLabel",true);
	        jObj.put("yLabel",true);
	        jObj.put("isSingleYAxis",false); 
	        jObj.put("ihc", false);  
	        jObj.put("cR", "HORIZONTAL");
	        
	        jObj.put("tlType","None");
	        jObj.put("ae", true);
	        
	        jObj.put("isPivotChart", true);
	        jObj.put("pivotId",pivotId);
	        
	        jObj.put("isClone", false);
		
		//--------
		//jObj.put(JSONConstants.ZUID, "2441");
		
		
		
		
		return jObj;
	}
	

	public static JSONObjectWrapper getFieldChoices(String docOwner, String documentId, String formSheetName ) {
		JSONObjectWrapper fieldChoices = new JSONObjectWrapper();
		try {
			Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
			SelectQuery sql = null;
			sql = new SelectQueryImpl(new Table("DocumentSheets"));
			sql.addSelectColumn(new Column(null, "*"));
			sql.addJoin(new Join("DocumentSheets", "WebForms", new String[]{"DOCUMENTSHEET_ID"}, new String[]{"DOCUMENTSHEET_ID"}, Join.INNER_JOIN));
			
			Criteria cri = new Criteria(new Column("DocumentSheets", "SHEET_NAME"), formSheetName, QueryConstants.EQUAL);
			sql.setCriteria(cri.and(new Criteria(new Column("DocumentSheets", "DOCUMENT_ID"), documentId, QueryConstants.EQUAL)));
			DataObject dataObject = persistence.get(sql);
			Row webFormRow = dataObject.getRow("WebForms"); //No I18N

			JSONArrayWrapper sortedFieldArray = null;

			JSONObjectWrapper fieldInfoObj = null;
			
			String fieldValue = "";
			if (webFormRow != null) {
				String fieldInfo = (String) webFormRow.get("FIELD_INFO");
				 
				 fieldInfoObj = new JSONObjectWrapper(fieldInfo);
				 
				String fieldMapInfo = (String) webFormRow.get("FIELD_MAP_INFO");
				JSONObjectWrapper fieldMappingInfo = new JSONObjectWrapper(fieldMapInfo);

				JSONArrayWrapper nameArray = fieldMappingInfo.names();
				sortedFieldArray = new JSONArrayWrapper();
				for (int i = 0; i < nameArray.length(); i++) {
					fieldValue = nameArray.getString(i);
					int columnId = Integer.parseInt(fieldMappingInfo.getString(fieldValue));
					sortedFieldArray.put(columnId, fieldValue);
				}
				if(!sortedFieldArray.isEmpty()){
					for(int i=0;i<sortedFieldArray.length();i++){
						if(sortedFieldArray.getString(i).toLowerCase().contains("dropdown") || sortedFieldArray.getString(i).toLowerCase().contains("radio")){
							JSONArrayWrapper choices = (JSONArrayWrapper)fieldInfoObj.get(sortedFieldArray.getString(i));
							fieldChoices.put(""+i, choices);
						}
					}
				}
				
			}

			
		
		} catch (Exception e) {
			LOGGER.log(Level.WARNING,"Exception ",e);
			//e.printStackTrace();
		}
		return fieldChoices;
	}
	public static JSONArrayWrapper getSortedFieldArray(JSONObjectWrapper fieldMappingInfo, JSONObjectWrapper fieldInfoObj){
		JSONArrayWrapper sortedFieldArray = null;
		String fieldValue = "";

		JSONArrayWrapper nameArray = fieldMappingInfo.names();
		sortedFieldArray = new JSONArrayWrapper();
		for (int i = 0; i < nameArray.length(); i++) {
			fieldValue = nameArray.getString(i);
			int columnId = Integer.parseInt(fieldMappingInfo.getString(fieldValue));
			sortedFieldArray.put(columnId, fieldValue);
		}
		return sortedFieldArray;
	}
	public static ArrayList<JSONObjectWrapper> getCreatePivotJsonObj(Workbook workbook, Sheet formSheet, Sheet reportSheet, ArrayList possiblePivotFields, JSONObjectWrapper fieldChoices, String zuid){
		
		ArrayList<JSONObjectWrapper> createPivotJsonList = new ArrayList<JSONObjectWrapper>();
		FormRange formRange = formSheet.getFormRange();
		int ssr = formRange.getStartRowIndex();
		int ser = formRange.getEndRowIndex()-1;
		int ssc = formRange.getStartColIndex();
		int sec = formRange.getEndColIndex()-1;

		JSONArrayWrapper pageArray = new JSONArrayWrapper();
		//JSONArray colArray = new JSONArray();
		JSONObjectWrapper grpJObj = new JSONObjectWrapper();
		JSONObjectWrapper fieldRefJObj = new JSONObjectWrapper();
		JSONArrayWrapper fieldRefJArray ;

		JSONArrayWrapper functionArray = new JSONArrayWrapper();
		functionArray.put("count");
		//functionArray.put("count");
		
		int reportlimit = possiblePivotFields.size() > FORM_REPORT_LIMIT ? FORM_REPORT_LIMIT:possiblePivotFields.size();
		
		//LOGGER.info("reportlimit : "+reportlimit);
		for(int i=0; i<reportlimit; i++){
			JSONArrayWrapper rowArray = new JSONArrayWrapper();
			JSONArrayWrapper colArray = new JSONArrayWrapper();
			JSONArrayWrapper dataArray = new JSONArrayWrapper();

			JSONObjectWrapper actionJson = new JSONObjectWrapper();
			actionJson.put(JSONConstants.ASSOCIATED_SOURCE_SHEET_NAME, formSheet.getAssociatedName());
			actionJson.put(JSONConstants.SOURCE_START_ROW, ssr);
			actionJson.put(JSONConstants.SOURCE_END_ROW, ser);
			actionJson.put(JSONConstants.SOURCE_START_COLUMN, ssc);
			actionJson.put(JSONConstants.SOURCE_END_COLUMN, sec);
			int colIndex = (Integer)possiblePivotFields.get(i);
			//LOGGER.info("colIndex: "+colIndex+" i value:"+i);
			if(i==0){
				grpJObj.put("0",4);//Month
				actionJson.put(JSONConstants.GROUP, grpJObj);
			}
			/*String json =" [ 5,colIndex,0 ] ";
			fieldRefJArray = new JSONArray(json);
			fieldRefJObj.put("1", fieldRefJArray);*/
			
			rowArray.put(colIndex);
			dataArray.put(colIndex);
			//dataArray.put(colIndex);
			
			actionJson.put("chartTitle", ""+formRange.getValueForAbsoluteReference(0, colIndex));
			
			actionJson.put("choices", fieldChoices);
			
			
			
			actionJson.put(JSONConstants.PAGE_FIELDS, pageArray);
			actionJson.put(JSONConstants.ROW_FIELDS, rowArray);
			actionJson.put(JSONConstants.COLUMN_FIELDS, colArray);
			actionJson.put(JSONConstants.DATA_FIELDS, dataArray);
			
			actionJson.put(JSONConstants.FUNCTION, functionArray);
			actionJson.put(JSONConstants.PIVOT_FIELD_REFERENCE, fieldRefJObj);
			actionJson.put(JSONConstants.GROUP, grpJObj);
			actionJson.put(JSONConstants.IS_NEW_SHEET, false);
			actionJson.put(JSONConstants.IS_NEW_SHEET, false);
			actionJson.put(JSONConstants.IS_DUMMY_PIVOT, true);
			//--------
			String newPivotName = ActionUtil.getNewRecommendedPivotName(workbook, formSheet.getAssociatedName(), zuid);
			actionJson.put(JSONConstants.ID, newPivotName);
			
			actionJson.put(JSONConstants.ZUID, zuid);
			
			
			//actionJson.put(JSONConstants.ASSOCIATED_SHEET_NAME, reportSheet.getAssociatedName());
			//actionJson.put(JSONConstants.START_ROW, 0);
			//actionJson.put(JSONConstants.START_COLUMN, 0);
			//LOGGER.info("actionJson: "+actionJson);
			createPivotJsonList.add(actionJson);
		}
		
		
		return createPivotJsonList;
	}
	
	public static JSONObjectWrapper getJsonObjforAction(Sheet formSheet, String zuid){
		JSONObjectWrapper jObj = null;
		FormRange formRange = formSheet.getFormRange();
		int ssr = formRange.getStartRowIndex();
		int ser = formRange.getEndRowIndex()-1;
		int ssc = formRange.getStartColIndex();
		int sec = formRange.getEndColIndex()-1;
		
		
		jObj = new JSONObjectWrapper();
		jObj.put(JSONConstants.ACTION, ActionConstants.CHANGE_PIVOT_SOURCE);
		jObj.put(JSONConstants.ASSOCIATED_SOURCE_SHEET_NAME, formSheet.getAssociatedName());
		jObj.put(JSONConstants.SOURCE_START_ROW, ssr);
		jObj.put(JSONConstants.SOURCE_END_ROW, ser);
		jObj.put(JSONConstants.SOURCE_START_COLUMN, ssc);
		jObj.put(JSONConstants.SOURCE_END_COLUMN, sec);
		
		jObj.put(JSONConstants.ZUID, zuid);
		
		
		
		
		return jObj;
	}
}
