/* $Id$ */
/**
 * 
 */
package com.adventnet.zoho.websheet.model.pivot.grid;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;




/**
 * <AUTHOR>
 *
 */
public class PivotRow {
	public static final Logger LOGGER = Logger.getLogger(PivotRow.class.getName());
	private int rowIndex;
	List<PivotCell> pivotCells;
	PivotGrid pivotgrid;
	 public PivotGrid getPivotGrid() {
		return pivotgrid;
	}

	public void setPivotGrid(PivotGrid pivotgrid) {
		this.pivotgrid = pivotgrid;
	}

	public PivotRow(PivotGrid pivotgrid, int rowIndex, List<PivotCell> pivotCells)
	    {
	        this.setPivotGrid(pivotgrid);
	        this.pivotCells = pivotCells;
	        this.rowIndex = rowIndex;
	    }

	    public PivotRow(PivotGrid pivotgrid, int rowIndex)
	    {
	    	this.setPivotGrid(pivotgrid);
	        this.rowIndex = rowIndex;
	        this.pivotCells = new ArrayList<>();
	    }


	    public PivotRow()
	    {
	        this.pivotCells = new ArrayList<>();
	    }
	    

	    /**
	     * Getter for property rowNumber.
	     * @return Value of property rowNumber.
	     */
	    public int getRowIndex()
	    {
	        return rowIndex;
	    }

	    /**
	     * Setter for property rowNumber.
	     * @param rowIndex
	     */
	    public void setRowIndex(int rowIndex)
	    {
	        this.rowIndex = rowIndex;
	    }
	    synchronized PivotCell getCell(int colIndex)
	    {
	        // As of now no empty cells are added to the cells[]
	        // later need to see if this is required
	    	//LOGGER.info("pivotgrid: "+pivotgrid);
	    	//LOGGER.info("colIndex: "+colIndex);
	    	PivotColumn column = pivotgrid.getColumn(colIndex);
	    	PivotCell pivotCell = null;
	        if(colIndex >= pivotCells.size()){
	        	pivotCell = new PivotCell(pivotgrid.getSheet(), this, column);
			    addCell(pivotCell);  
	        }else{
	    	 pivotCell = pivotCells.get(colIndex);
	        }

	        if(pivotCell == null)
	        {
	        	pivotCell = new PivotCell(pivotgrid.getSheet(), this, column);
	        pivotCells.set(colIndex, pivotCell);
	        }

	        //System.out.println("getCell for: "+cell);
	        return pivotCell;
	    }
	    void addCell(PivotCell cell)
	    {
	        // Add the null values till the colIndex
	        int currentSize = pivotCells.size();
	        int colIndex = cell.getColumnIndex();
	 
	        for(int i=currentSize; i < colIndex; i++)
	        {
	        	pivotCells.add(null);
	        }

	        pivotCells.add(cell);

	    }

}
