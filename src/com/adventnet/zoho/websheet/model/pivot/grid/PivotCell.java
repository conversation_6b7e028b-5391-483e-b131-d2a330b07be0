/* $Id$ */
package com.adventnet.zoho.websheet.model.pivot.grid;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.pivot.PivotTargetGrid.Grid;
import com.adventnet.zoho.websheet.model.style.datastyle.DataStyleConstants;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.ActionUtil;

import java.util.logging.Logger;


public class PivotCell {
	public static final Logger LOGGER = Logger.getLogger(PivotCell.class.getName());
    
    private PivotRow pivotRow;
    private PivotColumn pivotColumn;
    private String content;
    private Sheet sheet;
    private Grid gridPos;      //table:orientation
    public PivotCell(Sheet sheet, PivotRow pivotRow, PivotColumn pivotColumn){
    	this.sheet = sheet;
        this.pivotRow = pivotRow;
        this.pivotColumn = pivotColumn;
    }

    public PivotCell(PivotRow pivotRow, PivotColumn pivotColumn){
        this.pivotRow = pivotRow;
        this.pivotColumn = pivotColumn;
    }

    public void setContent(String content)
    {
        this.content = content;
    }
    public void setValue(String content, Grid gridPos)
    {
    	this.gridPos = gridPos;
    	this.content = content;
    	if(this.sheet!=null) {
    		Cell cell = sheet.getCell(pivotRow.getRowIndex(), pivotColumn.getColumnIndex());
            cell.setPattern(DataStyleConstants.EMPTY_PATTERN);
		    //List<RichStringProperties> richStringProperties = EngineUtils1.setLink(content, sheet.getWorkbook());
		    ActionUtil.setCellValue(cell, content, null,null,null,null,null, null);
		    if(cell.getType() == Cell.Type.STRING) {
                cell.setIgnoreError(1);
            }
    	}
    	
    }
    public void setValue(Object cellvalue, Grid gridPos, ZSPattern cellPattern) {
		//logger.log(Level.INFO, "sheet: "+sheet);
    	this.gridPos = gridPos;
    	this.content = cellvalue.toString();
			Cell cell = sheet.getCell(pivotRow.getRowIndex(), pivotColumn.getColumnIndex());
			cell.setPattern(cellPattern);
			ActionUtil.setCellValue(cell, cellvalue);
	}
    public String getContent(){
        return this.content;
    }
    public String getGridPos(){
    	//LOGGER.info("this.content "+this.content);
    	
    	if(this.content==null){
    		return null;
    	}
    	//LOGGER.info("this.gridPos "+this.gridPos+" "+this.gridPos.toString());
    	return this.gridPos.toString();
    }
    public int getRowIndex()
    {
    	return pivotRow.getRowIndex();
    }
    
    public int getColumnIndex()
    {
        return pivotColumn.getColumnIndex();
    }
    
}
