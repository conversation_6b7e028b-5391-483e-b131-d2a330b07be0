/* $Id$ */
/**
 * 
 */
package com.adventnet.zoho.websheet.model.pivot.grid;

import java.util.logging.Logger;


/**
 * <AUTHOR>
 *
 */
public class PivotColumn {
	public static Logger logger = Logger.getLogger(PivotColumn.class.getName());
    private int columnIndex;
    private PivotGrid pivotgrid;

    /** Creates a new instance of Column */
    public PivotColumn(PivotGrid pivotgrid, int columnIndex){
	this.columnIndex = columnIndex;
	this.pivotgrid = pivotgrid;
    }

    public int getColumnIndex()
    {
	return columnIndex;
    }

    void setColumnIndex(int columnIndex)
    {
	this.columnIndex = columnIndex;
    }

    public PivotGrid getPivotGrid()
    {
	return pivotgrid;
    }

    void setPivotGrid(PivotGrid pivotgrid)
    {
	this.pivotgrid = pivotgrid;
    }

}
