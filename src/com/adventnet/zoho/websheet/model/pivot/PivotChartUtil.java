/*  $Id$ */
/**
 *
 */
package com.adventnet.zoho.websheet.model.pivot;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.ds.query.Column;
import com.adventnet.ds.query.Criteria;
import com.adventnet.ds.query.QueryConstants;
import com.adventnet.persistence.DataObject;
import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.ActionConstants;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.chart.ChartConstants;
import com.zoho.sheet.chart.ChartUtils;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.JSONArrayWrapper;

/**
 * <AUTHOR>
 *
 */
public class PivotChartUtil {

    private static final Logger LOGGER = Logger.getLogger(PivotChartUtil.class.getName());

    public static JSONArrayWrapper constructSubGroupedCategory(ArrayList<ArrayList<ArrayList>> labelGroupList, int linePos, int jPos) {
        int labelGroupSize = labelGroupList.size();
        JSONArrayWrapper groupArray = null;
        ArrayList<ArrayList> labelAryLst = labelGroupList.get(linePos);
        int incr = 0, inIncr = 0;
        for (int j = 0; j <= jPos; j++) {
            ArrayList<String> labelArray = labelAryLst.get(j);
            if (j == jPos) {

                groupArray = new JSONArrayWrapper();

                if (linePos == labelGroupSize - 1) {
                    for (String label : labelArray) {
                    	if(label.equals("")){
                    		label = "(Blank)"; //NO I18N
                    	}
                        if(label.charAt(0) == '\''){
                            label = label.substring(1);
                        }
                        groupArray.put(label);
                    }

                } else {
                    JSONObjectWrapper category = null;
                    inIncr = 0;
                    for (String label : labelArray) {
                    	if(label.equals("")){
                    		label = "(Blank)";//NO I18N
                    	}
                        category = new JSONObjectWrapper();
                        category.put("categories", constructSubGroupedCategory(labelGroupList, linePos + 1, inIncr + incr));
                        if(label.charAt(0) == '\''){
                            label = label.substring(1);
                        }
                        category.put("name", label);
                        inIncr++;
                        groupArray.put(category);
                    }
                }

            } else {
                incr = incr + labelArray.size();
            }

        }
//		 LOGGER.info("groupArray: "+groupArray);
        return groupArray;

        // }
    }

    public static JSONArrayWrapper constructGroupedCategory(ArrayList<ArrayList<ArrayList>> labelGroupList) {
//        LOGGER.info("=============constructGroupedCategory=========>");

        JSONArrayWrapper finalCategory = new JSONArrayWrapper();

        if (labelGroupList == null) {
//				LOGGER.info("labelGroupList nulls>> "+labelGroupList);
            finalCategory.put("Total");
        } else {
            int labelGroupSize = labelGroupList.size();
            //LOGGER.info("labelGroupList size: "+labelGroupList.size());
            ArrayList<ArrayList> labelAryLst = labelGroupList.get(0);
            //LOGGER.info("labelAryLst size: "+labelAryLst.size());
            for (ArrayList<String> labelArray : labelAryLst) {
                //LOGGER.info("labelArray size: "+labelArray.size());
                int i = 0;
              //  LOGGER.info("=============labelGroupSize=========>" + labelGroupSize);
                if (labelGroupSize > 1) {
                    for (String label : labelArray) {
                    	if(label.equals("")){
                    		label = "(Blank)";//NO I18N
                    	}
                        if(label.charAt(0) == '\''){
                            label = label.substring(1);
                        }
                        JSONObjectWrapper category = new JSONObjectWrapper();
                        category.put("categories", constructSubGroupedCategory(labelGroupList, 1, i));
                        category.put("name", label);
                        finalCategory.put(category);
                        i++;
                    }
                } else {
                    for (String label : labelArray) {
                    	if(label.equals("")){
                    		label = "(Blank)";//NO I18N
                    	}
                        if(label.charAt(0) == '\''){
                            label = label.substring(1);
                        }
                        finalCategory.put(label);
                    }

                }

            }
        }

//		LOGGER.info("======================>"+finalCategory);
        return finalCategory;
    }

    public static String getPreviousColLabel(ArrayList<ArrayList<ArrayList>> labelGroupList, int linePos, int jPos) {
        //LOGGER.info("linePos: "+linePos+" jPos: "+jPos);
        String finalLabel = "";
        ArrayList<ArrayList> labelAryLst = labelGroupList.get(linePos);
        int curJ = 0;
        for (int j = 0; j < labelAryLst.size(); j++) {
            ArrayList<String> labelArray = labelAryLst.get(j);
            for (String label : labelArray) {
            	if(label.equals("")){
            		label = "(Blank)";//NO I18N
            	}
                if(label.charAt(0) == '\''){
                    label = label.substring(1);
                }
                if (curJ == jPos) {
                    if (linePos == 0) {
                        finalLabel = label;
                    } else {
                        finalLabel = getPreviousColLabel(labelGroupList, linePos - 1, j) + " - " + label;
                    }
                }
                curJ++;
            }

        }
        //LOGGER.info("finalLabel: "+finalLabel);
        return finalLabel;
    }

    public static ArrayList<String> constructColGroupList(ArrayList<ArrayList<ArrayList>> labelGroupList) {
        ArrayList<String> colGroupedCatagory = null;
        try {
            if (labelGroupList != null) {
                colGroupedCatagory = new ArrayList();

                int labelGroupSize = labelGroupList.size() - 1;
                ArrayList<ArrayList> labelAryLst = labelGroupList.get(labelGroupSize);
                // LOGGER.info("labelAryLst size: "+labelAryLst.size());
                for (int j = 0; j < labelAryLst.size(); j++) {
                    ArrayList<String> labelArray = labelAryLst.get(j);
                    for (String label : labelArray) {
                         if(label.equals("")){
                     		label = "(Blank)";//NO I18N
                     	}
                        if(label.charAt(0) == '\''){
                            label = label.substring(1);
                        }
                        if (labelGroupList.size() > 1) {
                            colGroupedCatagory.add(getPreviousColLabel(labelGroupList, labelGroupSize - 1, j) + " - " + label);
                        } else {
                            colGroupedCatagory.add(label);
                        }
                        //LOGGER.info("getPre Label: "+getPreviousColLabel(labelGroupList,i-1,j));
                    }
                }
                /*for(String colCatagory: colGroupedCatagory){
					 LOGGER.info("colCatagory "+colCatagory);
				 }*/
            }

        } catch (Exception e) {
            LOGGER.log(Level.WARNING,"[Pivot-Exception] constructColGroupList " , e);
        }
        return colGroupedCatagory;
    }

    public static ArrayList<ArrayList> getColumSeriesDataList(ArrayList<ArrayList> dataChartGridLst) {
        ArrayList<ArrayList> columSeriesDataList = new ArrayList();
        if(dataChartGridLst !=null && dataChartGridLst.size()>0) {
        	int size = dataChartGridLst.get(0).size();
            for (int i = 0; i < size; i++) {
                ArrayList seriesData = new ArrayList();
                for (ArrayList datachart : dataChartGridLst) {
                    Object value = datachart.get(i);
                    seriesData.add(value);

                }
                columSeriesDataList.add(seriesData);
            }
        }
        
        return columSeriesDataList;
    }
    
    private static int getPrecision(JSONObjectWrapper fraction, int index) {
        try {
            if (fraction.has("" + index)) {
                return fraction.getInt("" + index);
            }
        }catch (Exception e)
        {
            LOGGER.log(Level.INFO, "Fraction : "+ fraction +", Index : "+ index, e.getMessage());
        }
    		
        return 0;
    }

    public static JSONArrayWrapper constructColGroupedCategory(ArrayList<ArrayList> dataChartGridLst, ArrayList<String> colGroupedCatagory, JSONObjectWrapper fraction) {
        return constructColGroupedCategory(dataChartGridLst, colGroupedCatagory, false, "", null, false,  fraction);
    }

    public static JSONObjectWrapper constructBasicCategory() {
        JSONObjectWrapper constructedData = new JSONObjectWrapper();
        JSONArrayWrapper dataArray = new JSONArrayWrapper();
        constructedData.put("series", dataArray);
        constructedData.put("categories", dataArray);
        return constructedData;
    }

    public static JSONArrayWrapper constructColGroupedCategory(ArrayList<ArrayList> dataChartGridLst, ArrayList<String> colGroupedCatagory, boolean ndc, String chartType, String[] comboChartTypes, boolean isSingleYAxis, JSONObjectWrapper fraction) {
        JSONArrayWrapper colGroup = new JSONArrayWrapper();
        try {
            boolean isRadar = chartType == null ? false : (chartType.contains("SPIDERWEB")); //No I18N
            boolean isStepChart = chartType == null ? false : (chartType.equals(ChartConstants.STEP_CHART));
            boolean isComboChart = chartType == null ? false : (chartType.contains("COMBOCHART"));//No I18N
            ArrayList<ArrayList> seriesDataList = getColumSeriesDataList(dataChartGridLst);
            /*for(ArrayList seriesData: seriesDataList){
				 LOGGER.info("seriesData "+seriesData);
			 }*/
            int index = 0;
            if (colGroupedCatagory == null) {
                JSONObjectWrapper category = new JSONObjectWrapper();
                category.put("name", "Total");
                ArrayList seriesData = seriesDataList.get(index);
                JSONArrayWrapper dataArray = new JSONArrayWrapper();
                int valueInc = (ndc) ? 1 : 0;
                for (Object value : seriesData) {
                	int precision = getPrecision(fraction, index);
                    JSONObjectWrapper valueObj = new JSONObjectWrapper();
                    if(value instanceof Throwable || value.equals("")){
                    	value = 0.0;
                    }else if(value instanceof Integer){
                    	value = ((Integer) value).doubleValue();
                    }
                    valueObj.put("x", valueInc);
                    valueObj.put("y", Value.getInstance(Cell.Type.FLOAT, (Double) value * 1).getValue());
                    valueObj.put("decimals", precision);
                    //need x,y data to support custom point color ~ pawan
//                    if (ndc || chartType.equals(ChartConstants.POLAR_CHART)) {
//                        JSONArray data1 = new JSONArray();
//                        data1.put(valueInc);
//                        data1.put(new Value(Cell.Type.FLOAT, (Double) value * 1).getValue());
//                        dataArray.put(data1);
//                    } else {
//                        dataArray.put(new Value(Cell.Type.FLOAT, (Double) value * 1).getValue());
//                    }
                    dataArray.put(valueObj);
                    valueInc++;
                }
                category.put("data", dataArray);
                index++;
                colGroup.put(category);
            } else {
            	int ht = 10, wd=10;
                for (String colLabel : colGroupedCatagory) {
                    JSONObjectWrapper category = new JSONObjectWrapper();
                    if(colLabel.equals("")){
                    	colLabel = "(Blank)";//NO I18N
                	}
                    category.put("name", colLabel);
                    ArrayList seriesData = seriesDataList.get(index);
                    JSONArrayWrapper dataArray = new JSONArrayWrapper();
                    int valueInc = (ndc) ? 1 : 0;
                    for (Object value : seriesData) {
                    	int precision = getPrecision(fraction, index);
//                    	 LOGGER.info("value: "+value+" pValue "+ Value.getInstance(Cell.Type.FLOAT, (Double) value * 1).getValue()+" precision: "+precision);
                        JSONObjectWrapper valueObj = new JSONObjectWrapper();
                        if(value instanceof Throwable || value.equals("")){
                        	value = 0.0;
                        }else if(value instanceof Integer){
                        	value = ((Integer) value).doubleValue();
                        }
                       
                        valueObj.put("x", valueInc);
                        valueObj.put("y", Value.getInstance(Cell.Type.FLOAT, (Double) value * 1).getValue());
                        valueObj.put("decimals", precision);
                    //need x,y data to support custom point color ~ pawan
                    dataArray.put(valueObj);
                    valueInc++;
//                        if (ndc || chartType.equals(ChartConstants.POLAR_CHART)) {
//                            JSONArray data1 = new JSONArray();
//                            data1.put(valueInc);
//                            data1.put(new Value(Cell.Type.FLOAT, (Double) value * 1).getValue());
////                             LOGGER.info("data1: "+data1);
//                            dataArray.put(data1);
//                        } else {
//                            dataArray.put(new Value(Cell.Type.FLOAT, (Double) value * 1).getValue());
//                        }
//                        /* LOGGER.info("value:  "+value);
//						 LOGGER.info(value+" value:  "+new Value(Cell.Type.FLOAT, (Double) value * 1).getValue());*/
//                        valueInc++;
                    }
                    category.put("data", dataArray);
                    if (isRadar) {
                        category.put("pointPlacement", "on");
                    }
                    if (isStepChart) {
                        category.put("step", true);
                    }
                    if (isComboChart) {
                    	List<String> comboChartTypesList  = new ArrayList<String>();
                    	if(comboChartTypes != null) {
                    		for(int i=0;i<comboChartTypes.length; i++) {
                    			comboChartTypesList.add(i, comboChartTypes[i]);
                    		} 
                    		if(comboChartTypesList.size() < colGroupedCatagory.size()){
                    			for(int i=comboChartTypesList.size(); i<colGroupedCatagory.size(); i++) {
                    				comboChartTypesList.add(i, "XYLINE");	//No I18N
                    			}
                    		}
                    	} else{ 
                    		comboChartTypesList.add(0, "COL");	//No I18N
                    		if(comboChartTypesList.size() < colGroupedCatagory.size()){
                    			for(int i=comboChartTypesList.size(); i<colGroupedCatagory.size(); i++) {
                    				comboChartTypesList.add(i, "XYLINE");	//No I18N
                    			}
                    		}
                    	}
                    	boolean isColPresent = (comboChartTypesList.contains("COL") || comboChartTypesList.contains("STACKEDCOL"))?true:false;	//No I18N
                    	if(isColPresent && index == 0) {
                    		ht=20; wd=0;
                    	} else if(index == 0 && !isColPresent) {
                    		ht=100; wd=0;
                    	}
                    	
//                        int yAxis = index;
                       // String type = (index == 0) ? "column" : ""; //No I18N
                        String type = "line";//No I18N
                        if(index < comboChartTypesList.size() && comboChartTypesList.get(index) != null) {
                        	type= ChartUtils.getChartNameFromChartCode(comboChartTypesList.get(index));
                        }
//                        if(isSingleYAxis) {
//                        	category.put("yAxis", yAxis);
//                        } else{
//                        	category.put("yAxis", 0);
//                        }
                        
                        if(type.equals("stackedcol")) {
                        	category.put("type", "column");
                        	category.put("stacking", "normal");
                        } else if(type.equals("stackedbar")) {
                        	category.put("type", "bar");
                        	category.put("stacking", "normal");
                        } else if(type.equals("stackedarea")) {
                        	category.put("type", "area");
                        	category.put("stacking", "normal");
                        }else if(type.equals("stepline")) {
                        	category.put("type", "line");
                        	category.put("step", true);
                        } else if(type.equals("doughnut")) {
                        	category.put("type", "pie");
                        	category.put("major", "doughnut");
                        	category.put("size","20%");
                        	category.put("innerSize","50%");
                        	if(ht==20 && wd==0 && !isColPresent) {
                        		ht=85; wd=10;
                        	}
                        	category.put("center", new String[]{String.valueOf(ht)+"%",String.valueOf(wd)+"%"});
                        	if(isColPresent) {
                        		ht=(ht+25 > 100)?ht=10:ht+25;
                            	wd=(ht==10)?wd=wd+25:wd;
                        	} else{
                        		ht=(ht-25 < 0)?ht=100:ht-25;
                            	wd=(ht==85)?wd=wd+25:wd;
                        	}
                        } else if(type.equals("pie")){
                        	category.put("type", "pie");
                        	category.put("size","20%");
                        	if(ht==20 && wd==0 && !isColPresent) {
                        		ht=85; wd=10;
                        	}
                        	category.put("center", new String[]{String.valueOf(ht)+"%",String.valueOf(wd)+"%"});
                        	if(isColPresent) {
                        		ht=(ht+25 > 100)?ht=10:ht+25;
                            	wd=(ht==10)?wd=wd+25:wd;
                        	} else{
                        		ht=(ht-25 < 0)?ht=100:ht-25;
                            	wd=(ht==85)?wd=wd+25:wd;
                        	}
                        }else if(type.equals("semidoughnut")) {
                        	category.put("type", "pie");
                        	category.put("major", "semidoughnut");
                        	category.put("startAngle","-90");
                        	category.put("endAngle","90");
                        	category.put("size","20%");
                        	category.put("innerSize","50%");
                        	if(ht==20 && wd==0 && !isColPresent) {
                        		ht=85; wd=10;
                        	}
                        	category.put("center",new String[]{String.valueOf(ht)+"%",String.valueOf(wd)+"%"});
                        	if(isColPresent) {
                        		ht=(ht+25 > 100)?ht=10:ht+25;
                            	wd=(ht==10)?wd=wd+25:wd;
                        	} else{
                        		ht=(ht-25 < 0)?ht=100:ht-25;
                            	wd=(ht==85)?wd=wd+25:wd;
                        	}
                        }
                        else{
                        	category.put("type", type);
                        }
                    }
                    index++;
                    colGroup.put(category);
                }
            }

        } catch (Exception e) {
        	LOGGER.log(Level.INFO, ">>>> [CHART] >>>> Error while forming series data for pivot charts >>>", e);       // No I18N
            //e.printStackTrace();
        }
        return colGroup;

    }

    public static JSONObjectWrapper constructDataWithCategory(ArrayList<String> linearCategory, ArrayList<String> linearLegend, ArrayList<ArrayList> columnSeriesDataList, String chartType, JSONObjectWrapper fraction) {
        chartType = chartType == null ? ChartConstants.DOUGHNUT_CHART : chartType;
        JSONObjectWrapper constructedData = new JSONObjectWrapper();
        JSONArrayWrapper finalDataConstruction = new JSONArrayWrapper();
        JSONObjectWrapper firstRowData = new JSONObjectWrapper();
        int index = 0;
        int legendCount = 0;
        int legendSize = 0;
        int templegendCount = 0;
        String name = null;
        if (linearLegend == null) {
            legendSize = columnSeriesDataList.size();
            name = "Total"; //NO I18N
        } else {
            legendSize = linearLegend.size();
        }
        int noOfMultipleSeries = legendSize < EngineConstants.PIE_MAX_SERIES ? legendSize : EngineConstants.PIE_MAX_SERIES;
        for (int j = 0; j < noOfMultipleSeries; j++) {

            if (templegendCount == 0 || templegendCount < legendCount) {
                ArrayList colSeriesData = columnSeriesDataList.get(legendCount);
                index = 0;
                for (String category : linearCategory) {
                    firstRowData = new JSONObjectWrapper();
                    firstRowData.put("name", category);
                    Object value = colSeriesData.get(index);
                    int precision = getPrecision(fraction, index);
                    if(value instanceof Throwable || value.equals("")){
                    	value = 0.0;
                    }else if(value instanceof Integer){
                    	value = ((Integer) value).doubleValue();
                    }
                    /*LOGGER.info(value+" value:  "+new Value(Cell.Type.FLOAT, (Double) value * 1).getValue());*/
                    Object cellValue = Value.getInstance(Cell.Type.FLOAT, (Double) value * 1).getValue();
                    
                    firstRowData.put("y", cellValue);
                    firstRowData.put("decimals", precision);
                    finalDataConstruction.put(firstRowData);
                    index++;

                }
                if (name == null) {
                    name = linearLegend.get(j);
                }

                constructedData.put("data", finalDataConstruction);
                constructedData.put("name", name);
                if (chartType.equalsIgnoreCase("DOUGHNUT3D")) {
                    constructedData.put("innerSize", "40%");
                }

            }
            templegendCount++;

        }

//		 LOGGER.info("constructedData: "+constructedData);
        return constructedData;

    }
    // DOUGHNUT

    public static JSONObjectWrapper constructDataSeriesWithCategory(ArrayList<String> linearCategory, ArrayList<String> linearLegend, ArrayList<ArrayList> columnSeriesDataList, String chartType, JSONObjectWrapper fraction) {
        chartType = chartType == null ? ChartConstants.DOUGHNUT_CHART : chartType;
        int size[] = new int[5];
        size[0] = 100;
        size[1] = 80;
        size[2] = 60;
        size[3] = 40;
        size[4] = 20;
        int innerDepth[] = new int[5];
        innerDepth[0] = 70;
        innerDepth[1] = 60;
        innerDepth[2] = 40;
        innerDepth[3] = 40;
        innerDepth[4] = 40;
        JSONObjectWrapper constructedData = new JSONObjectWrapper();
        JSONArrayWrapper finalDataConstruction = new JSONArrayWrapper();
        JSONObjectWrapper firstRowData = new JSONObjectWrapper();
        int index = 0;
        int legendCount = 0;
        //if(chartType.contains(ChartConstants.DOUGHNUT_CHART)){
        legendCount = 4;
        //}
        int templegendCount = 0;
        String magnitude = "", innerSize = "";
        int legendSize = linearLegend == null ? 1 : linearLegend.size();
        int noOfMultipleSeries = legendSize < EngineConstants.PIE_MAX_SERIES ? legendSize : EngineConstants.PIE_MAX_SERIES;
        for (int j = 0; j < noOfMultipleSeries; j++) {
            JSONArrayWrapper finalColContruction = new JSONArrayWrapper();
//			 LOGGER.info("j::"+j);
            JSONObjectWrapper series = new JSONObjectWrapper();
            if (j == 0 && j == noOfMultipleSeries - 1) {
//				 LOGGER.info("j::"+j);
                magnitude = "100%";
                innerSize = "50%";

            } else {
                magnitude = size[j] + "%";
                innerSize = innerDepth[j] + "%";
            }

            //for(ArrayList colSeriesData : columnSeriesDataList){
            if (templegendCount == 0 || templegendCount < legendCount) {
                ArrayList colSeriesData = columnSeriesDataList.get(templegendCount);
                index = 0;
                int idValue = 1;
                for (String category : linearCategory) {

                    firstRowData = new JSONObjectWrapper();
//    					 LOGGER.info("category: "+category);
                    firstRowData.put("id", idValue);
                    firstRowData.put("name", category);
                    Object value = colSeriesData.get(index);
                    int precision = getPrecision(fraction, index);
                    if(value instanceof Throwable || value.equals("")){
                    	value = 0.0;
                    }else if(value instanceof Integer){
                    	value = ((Integer) value).doubleValue();
                    }
//    						 LOGGER.info("value:  "+value);
                    /*LOGGER.info(value+" value:  "+new Value(Cell.Type.FLOAT, (Double) value * 1).getValue());*/
                    Object cellValue = Value.getInstance(Cell.Type.FLOAT, (Double) value * 1).getValue();
                    firstRowData.put("y", cellValue);
                    firstRowData.put("decimals", precision);
                    finalColContruction.put(firstRowData);
                    index++;
                    idValue++;

                }
                String name = linearLegend == null ? "Total" : linearLegend.get(j);//NO I18N

                series.put("data", finalColContruction);
                series.put("name", name);
                series.put("size", magnitude);
                series.put("innerSize", innerSize);
                finalDataConstruction.put(series);

            }
            templegendCount++;

            //}
        }

//		 LOGGER.info("finalDataConstruction: "+finalDataConstruction);
//		 LOGGER.info("constructedData: "+constructedData);
        constructedData.put("series", finalDataConstruction);
        return constructedData;

    }

    public static JSONObjectWrapper getSeriesData(PivotComponent pComp, String chartType, boolean ndc, String[] comboChartTypes, boolean isSingleYAxis) {
        JSONObjectWrapper constructedData = new JSONObjectWrapper();
        JSONArrayWrapper categories;
        JSONArrayWrapper finalDataConstruction;
        /*LOGGER.info("chartType: "+chartType);
			LOGGER.info("pComp Row == > " + pComp.getRowLabelGroupList());
			LOGGER.info("pComp Col == > " + pComp.getColLabelGroupList());
			LOGGER.info("pComp getDataChartGridLst == > " + pComp.getDataChartGridLst());*/
        //Locale functionLocale = pComp.getPivotSourceCellRange().getSheet().getWorkbook().getFunctionLocale();
        JSONObjectWrapper fraction = pComp.getFraction();
        if (chartType == null) {

            ArrayList<ArrayList> columSeriesDataList = getColumSeriesDataList(pComp.getDataChartGridLst());
            ArrayList<String> linearCategory = PivotChartUtil.constructColGroupList(pComp.getRowLabelGroupList());
            ArrayList<String> linearLegend = PivotChartUtil.constructColGroupList(pComp.getColLabelGroupList());
            //LOGGER.info(" linearCategory  : "+linearCategory);

            constructedData = constructDataWithCategory(linearCategory, linearLegend, columSeriesDataList, chartType, fraction);
        } else if (chartType.contains("BAR") || chartType.contains("COL") || chartType.equals(ChartConstants.STEP_CHART) || chartType.equals("XYLINE") || chartType.equals(ChartConstants.SPLINE_CHART) || chartType.equals("XYAREA") || chartType.equals("XYSTACKEDAREA") || chartType.contains("SPIDERWEB") || chartType.equals(ChartConstants.POLAR_CHART) || chartType.contains("COMBOCHART")) {//No I18N 
            if (ndc || chartType.equals(ChartConstants.STEP_CHART) || chartType.contains("SPIDERWEB") || chartType.equals(ChartConstants.POLAR_CHART) || chartType.contains("COMBOCHART")) {
                //LOGGER.info("pComp ndc is true == > "+ndc);
            	
                finalDataConstruction = PivotChartUtil.constructColGroupedCategory(pComp.getDataChartGridLst(), pComp.getColGroupedList(), ndc, chartType, comboChartTypes, isSingleYAxis, fraction);
            } else {
                finalDataConstruction = pComp.getColGroupedCategory();
            }
            //LOGGER.info("finalDataConstruction == > "+finalDataConstruction);
            if (!chartType.equals(ChartConstants.POLAR_CHART)) {
                if (pComp.getRowLabelGroupList() == null) {
                    constructedData.put("categories", constructGroupedCategory(null));
                } else {
                    categories = pComp.getRowGroupedCategory();
                    if (categories.length() != 0) {
                        if (ndc) {
                            categories = new JSONArrayWrapper();
                            categories.put("");
                            for (int i = 0; i < pComp.getRowGroupedCategory().length(); i++) {
                                categories.put(pComp.getRowGroupedCategory().get(i));
                            }
                        }
                        constructedData.put("categories", categories);
                    }
                }
            }
            LOGGER.info("Piovt chart finalDataConstruction: "+finalDataConstruction);

            finalDataConstruction = finalDataConstruction == null ? new JSONArrayWrapper() : finalDataConstruction;
            constructedData.put("series", finalDataConstruction);
        } else if (chartType.contains(ChartConstants.PIE_CHART) || chartType.contains(ChartConstants.FUNNEL_CHART) || chartType.equalsIgnoreCase("DOUGHNUT3D")) {

            ArrayList<ArrayList> columSeriesDataList = getColumSeriesDataList(pComp.getDataChartGridLst());
            ArrayList<String> linearCategory = PivotChartUtil.constructColGroupList(pComp.getRowLabelGroupList());
            ArrayList<String> linearLegend = PivotChartUtil.constructColGroupList(pComp.getColLabelGroupList());

            constructedData = constructDataWithCategory(linearCategory, linearLegend, columSeriesDataList, chartType, fraction);
        } else if (chartType.contains(ChartConstants.DOUGHNUT_CHART)) {

            ArrayList<ArrayList> columSeriesDataList = getColumSeriesDataList(pComp.getDataChartGridLst());
            ArrayList<String> linearCategory = PivotChartUtil.constructColGroupList(pComp.getRowLabelGroupList());
            ArrayList<String> linearLegend = PivotChartUtil.constructColGroupList(pComp.getColLabelGroupList());
            //LOGGER.info(" linearCategory  : "+linearCategory);

            constructedData = constructDataSeriesWithCategory(linearCategory, linearLegend, columSeriesDataList, chartType, fraction);
        }

        //constructedData.put("groupPadding", (groupPadding != defaultGroupPadding) ? groupPadding : null);
        LOGGER.info("Piovt chart constructedData: "+constructedData);
        return constructedData;
    }

    public static JSONObjectWrapper getSeriesData(Sheet sheet, String pivotId, String chartType, boolean ndc, String[] comboChartTypes,boolean isSingleYAxis) {
        JSONObjectWrapper constructedData = null;
        PivotComponent pComp = getPivotCompObj(sheet, pivotId);
        if (pComp != null) {
            constructedData = PivotChartUtil.getSeriesData(pComp, chartType, ndc, comboChartTypes, isSingleYAxis);
        } else {
            constructedData = constructBasicCategory();
        }
        return constructedData;

    }
	public static PivotComponent getPivotCompObj (Sheet sheet, String pivotId) {
		Workbook workbook =  sheet.getWorkbook();
		PivotTable pivotTable = null;
		if(pivotId.startsWith("Recommended")){//NO I18N
			pivotTable = workbook.getRecommendedPivotTable(pivotId);
		}else{
			pivotTable = workbook.getDataPivotTable(pivotId);
		}
		PivotComponent pComp = null;
		


 
        if (pivotTable != null) {
            pComp = pivotTable.getPivotComponent();

            if (pComp == null || !pComp.hasChartStoredInfo()) {
//				LOGGER.info("pComp is null");
                JSONObjectWrapper jObj = new JSONObjectWrapper();
                jObj.put(JSONConstants.ID, pivotId);
                jObj.put(JSONConstants.ACTION, ActionConstants.REFRESH_PIVOT);
                try {
                    pComp = PivotUtil.generatePivotComponent(workbook, jObj, null, false);
                    pComp.populatePivotModel(workbook);
                } catch (Exception e) {
                    LOGGER.log(Level.INFO, "Error While generate PivotComponent -> Pivot :: "+pivotId, e);
                    pComp = null;
                }
//				LOGGER.info("pComp: "+pComp);

            }
        }

        return pComp;
    }
	public static JSONObjectWrapper getCurrencySymbol(Sheet sheet, String pivotId) {
        JSONObjectWrapper currencySymbol = new JSONObjectWrapper();
		PivotComponent pComp = getPivotCompObj( sheet, pivotId);
        if (pComp != null) {
            currencySymbol = pComp.getCurrencySymbol();
        }
        return currencySymbol;
    }
	public static String getXAxisTitle(Sheet sheet, String pivotId) {
        String xAxisTitle = "";
		PivotComponent pComp = getPivotCompObj(sheet, pivotId);
        int rowListSize = pComp.getRowList().size();
        if (rowListSize == 1) {
            PivotField pivotField = pComp.getRowList().get(0);
            xAxisTitle = pivotField.getSourceFieldName();
        }
        return xAxisTitle;
    }
	public static String getYAxisTitle(Sheet sheet, String pivotId) {
        String yAxisTitle = "";
		PivotComponent pComp = getPivotCompObj( sheet, pivotId);

        if (pComp.getDataList().size() == 1) {
            yAxisTitle = pComp.getFunctionofDataFieldMsg(sheet.getWorkbook(), pComp.getDataList().get(0).getFunction(), pComp.getDataList().get(0).getSourceFieldName());

        }
        return yAxisTitle;
    }

    public static ArrayList getChartList(String docId, String docOwner, String pivotId) {
        ArrayList<String> chartIdList = null;
        try {
            DataObject chartsDO = ChartUtils.getDocumentChartsDO(docId, docOwner);
            Criteria cri = new Criteria(new Column("Charts", "CHART_RANGE"), pivotId, QueryConstants.EQUAL);

            Iterator chartsItr = chartsDO.getRows("Charts", cri);

            while (chartsItr.hasNext()) {
                com.adventnet.persistence.Row row = (com.adventnet.persistence.Row) chartsItr.next();
                Long chartId = (Long) row.get("CHART_ID");
                if (chartIdList == null) {
                    chartIdList = new ArrayList<String>();
                }
                chartIdList.add("" + chartId);
            }
        } catch (Exception e) {

        }
        return chartIdList;
    }

    public static JSONArrayWrapper getAffChartListBasedonSheet(Workbook workBook, String sheetName) throws Exception {
        JSONArrayWrapper chartIdArr = new JSONArrayWrapper();
        try {
            if (workBook != null) {
                Map<String, Map<String, Chart>> chartMap = workBook.getChartMap();
                if (chartMap != null) {
                    for (Sheet sheetObj : workBook.getSheetList()) {
                        Map<String, Chart> sheetChartMap = chartMap.get(sheetObj.getAssociatedName());
                        if (sheetChartMap != null) {
                            for (Chart chart : sheetChartMap.values()) {
                                if (chart.isPivotChart()) {
                                    String pivotId = chart.getPivotId();
                                    PivotTable pivotTable = workBook.getDataPivotTable(pivotId);
                                    if (pivotTable != null && workBook.getSheetByAssociatedName(pivotTable.getTargetCellRange().getAssociatedSheetName()).getName().equals(sheetName)) {
                                        JSONObjectWrapper sheetAndChartId = new JSONObjectWrapper();
                                        sheetAndChartId.put("sheetName", chart.getSheetName());
                                        sheetAndChartId.put("chartId", chart.getChartId());
                                        chartIdArr.put(sheetAndChartId);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            LOGGER.log(Level.INFO, ">>>> [CHART] >>>> Error while changing the dataranges of charts >>>", ex);       // No I18N
        }
        return chartIdArr;
    }

    /*public static void updatePivotChartIds(String docId, String docOwner, String oldPivotId, String newPivotId) {
		try{
			ArrayList<String> chartIdList = PivotChartUtil.getChartList(docId, docOwner, oldPivotId);
	         if(chartIdList != null){
	        	 Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
	         	for(String chartId : chartIdList){
	         		 UpdateQuery uq = new UpdateQueryImpl("Charts");							//No I18N
	                 uq.setUpdateColumn("CHART_RANGE", newPivotId);	//No I18N
	                 Criteria cri = new Criteria(new Column("Charts", "CHART_ID"), chartId, QueryConstants.EQUAL);
	                 uq.setCriteria(cri);
	                 persistence.update(uq);
	         	}
	         }
		}catch(Exception e){
			LOGGER.log(Level.WARNING,"updatePivotChartIds == ",e);
		}
		 
	}*/
	public static boolean setGrouping(Sheet sheet, String pivotId) {
        boolean isGrouping = false;
		PivotComponent pComp = getPivotCompObj( sheet, pivotId);
        if (pComp != null) {
            int rowListSize = pComp.getRowList().size();
            if (rowListSize > 1) {
                isGrouping = true;
            }
        }

        return isGrouping;
    }

    public static String getPivotIdForTargetRange(Workbook workBook, String oldTargetRange, String newSheetName) {
        String pivotId = null;
        List<PivotTable> pivotTableList = workBook.getPivotTables();
        if (pivotTableList != null) {
            for (PivotTable pivotTable : pivotTableList) {
                if (workBook.getSheetByAssociatedName(pivotTable.getTargetCellRange().getAssociatedSheetName()).getName().equals(newSheetName) && pivotTable.getTargetCellRange().getRangeString().equals(oldTargetRange)) {
                    pivotId = pivotTable.getName();
                    break;
                }

            }
        }
        return pivotId;
    }

}
