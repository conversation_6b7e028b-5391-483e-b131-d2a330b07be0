//$Id$
/**
 * 
 */
package com.adventnet.zoho.websheet.model.pivot;

/**
 * <AUTHOR>
 *
 */
public class DataPivotMember {

	private String name;
	private boolean display;
	private boolean showDetails;
	/**
	 * @return the name
	 */
	private String getName() {
		return name;
	}
	/**
	 * @param name the name to set
	 */
	private void setName(String name) {
		this.name = name;
	}
	/**
	 * @return the display
	 */
	private boolean isDisplay() {
		return display;
	}
	/**
	 * @param display the display to set
	 */
	private void setDisplay(boolean display) {
		this.display = display;
	}
	/**
	 * @return the showDetails
	 */
	private boolean isShowDetails() {
		return showDetails;
	}
	/**
	 * @param showDetails the showDetails to set
	 */
	private void setShowDetails(boolean showDetails) {
		this.showDetails = showDetails;
	}
	

}
