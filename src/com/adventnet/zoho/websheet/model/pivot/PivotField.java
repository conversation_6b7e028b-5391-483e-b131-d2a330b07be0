//$Id$

package com.adventnet.zoho.websheet.model.pivot;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.*;

import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.CalendarValue.PeriodType;
import com.adventnet.zoho.websheet.model.pivot.PivotUtil.FieldType;
import com.adventnet.zoho.websheet.model.pivot.PivotSortInfo.Order;

/**
 *
 * <AUTHOR>
 */

//table:data-pilot-field

public class PivotField implements Cloneable
{
	public static Logger logger = Logger.getLogger(PivotField.class.getName());
	
    private String sourceFieldName;                     //table:source-field-name
    private Orientation orientation;                    //table:orientation
    private String isDataLayoutField;                   //table:is-data-layout-field
    private Function function;                          //table:function    //Only evaluated if table orientation value is data
    private int usedHierarchy = -1;                     //table:used-hierarchy :::: default : -1
    private PivotLevel pivotLevel;                      //table-data-pilot-level
    private PivotFieldReference pivotFieldReference;    //table:data-pilot-field-reference
    private PivotGroups pivotGroups;    		//table:data-pilot-groups
    /*****/
   // private Sheet sheet = null;
    private int fieldIndex = -1;
    private PeriodType periodType;
    private UniqueContentContainer uniqueContentContainer = new UniqueContentContainer();
	private List<String> uniqueContentList 				= new ArrayList<String>();
	private List<Integer> dataRowIndexList 				= new ArrayList<Integer>();
	private List<Integer> filteredDataRowIndexList 				= new ArrayList<Integer>();
	private List<String> filteredUniqueContentList 				= new ArrayList<String>();
	private ArrayList<Integer> tobefilteredRowIndexList = new ArrayList<Integer>();
	
	private Map<String, IndexHolder>  dataColIdxFunctionMap = new HashMap<String, IndexHolder>();
	
	
	private JSONArrayWrapper fieldChoices = null;
	
	private JSONObjectWrapper colInfo = null;

	private FieldType colRangeType = FieldType.Tt;
    public enum ContentType{
        INTEGER,
        NUMBER,
        DATE,
        STRING,
        BLANK,
        LONG_TEXT
    }
    private EnumSet<ContentType> contentTypes = EnumSet.noneOf(ContentType.class);
    public void addContentType(ContentType type) {
        contentTypes.add(type);
    }
    public void removeContentType(ContentType type) {
        contentTypes.remove(type);
    }
	//private boolean dummy = false;
	public EnumSet<ContentType> getContentTypes()
    {
        return this.contentTypes;
    }
  
	public JSONArrayWrapper getFieldChoices() {
		return fieldChoices;
	}

	public void setFieldChoices(JSONArrayWrapper fieldChoices) {
		this.fieldChoices = fieldChoices;
	}

	/*****/
    
    public enum Orientation
    {
        ROW,
        COLUMN,
        DATA,
        PAGE,
        HIDDEN,
        SLICER
    }
    
    public enum Function
    {
        AUTO,               //No function is applied at all
        AVERAGE,
        COUNT,
        COUNTNUMS,
        MAX,
        MIN,
        PRODUCT,
        STDEV,
        STDEVP,
        SUM,
        VAR,
        VARP,
        MEDIAN,
        DISTINCTCOUNT
    }
    
    public String[] getAttributes()
    {
        String[] attrs =
        {
            "table:source-field-name",      //No I18N
            "table:orientation",            //No I18N
            "table:is-data-layout-field",   //No I18N
            "table:function",               //No I18N
            "table:used-hierarchy",         //No I18N
            "table:contains-integer",       //No I18N
            "table:contains-number",       //No I18N
            "table:contains-string",          //No I18N
            "table:contains-date",          //No I18N
            "table:contains-blank",          //No I18N
            "table:contains-long-text"          //No I18N
        };
        return attrs;
    }

    public String[] getValues()
    {
        String[] values =
        {
            sourceFieldName,
            (orientation == null) ? null : orientation.toString().toLowerCase(),
            isDataLayoutField,
            (function == null) ? null : function.toString().toLowerCase(),
            String.valueOf(usedHierarchy),
            contentTypes.contains(ContentType.INTEGER) ? "true" : null,
            contentTypes.contains(ContentType.NUMBER) ? "true" : null,
            contentTypes.contains(ContentType.STRING) ? "true" : null,
            contentTypes.contains(ContentType.DATE) ? "true" : null,
            contentTypes.contains(ContentType.BLANK) ? "true" : null,
            contentTypes.contains(ContentType.LONG_TEXT) ? "true" : null

        };
        return values;
    }
    
    public String getSourceFieldName()
    {
        return this.sourceFieldName;
    }
    
    public void setSourceFieldName(String sourceFieldName)
    {
        this.sourceFieldName = sourceFieldName;
    }

    public Orientation getOrientation()
    {
        return this.orientation;
    }
    
    public void setOrientation(Orientation orientation)
    {
        this.orientation = orientation;
    }
    
    public void setOrientationFromParser(String orientation)
    {
        if(orientation != null)
        {
            if("row".equals(orientation))
            {
                this.orientation = Orientation.ROW;
            }
            else if("column".equals(orientation))
            {
                this.orientation = Orientation.COLUMN;
            }
            else if("data".equals(orientation))
            {
                this.orientation = Orientation.DATA;
            }
            else if("page".equals(orientation))
            {
                this.orientation = Orientation.PAGE;
            }
            else if("slicer".equals(orientation)){
                this.orientation = Orientation.SLICER;
            }
            else
            {
                this.orientation = Orientation.HIDDEN;
            }
        }
    }
    
    public String getIsDataLayoutField()
    {
        return this.isDataLayoutField;
    }
    
    public void setIsDataLayoutField(String isDataLayoutField)
    {
        this.isDataLayoutField = isDataLayoutField;
    }
    
    public Function getFunction()
    {
        return this.function;
    }
    
    public void setFunction(Function function)
    {
        this.function = function;
    }
    
    public void setFunctionFromParser(String function)
    {
        if(this.orientation == Orientation.DATA)
        {
            this.function = getFunctionFromString(function);
        }
    }
    
    public int getUsedHierarchy()
    {
        return this.usedHierarchy;
    }
    public void setUsedHierarchy(int usedHierarchy)
    {
    	this.usedHierarchy = usedHierarchy;
    }
    
    public PivotLevel getPivotLevel()
    {
        return this.pivotLevel;
    }
    
    public void setPivotLevel(PivotLevel pivotLevel)
    {
        this.pivotLevel = pivotLevel;
    }
    
    public PivotFieldReference getPivotFieldReference()
    {
        return this.pivotFieldReference;
    }
    
    public void setPivotFieldReference(PivotFieldReference pivotFieldReference)
    {
        this.pivotFieldReference = pivotFieldReference;
    }
    public PivotGroups getPivotGroups()
    {
    	return this.pivotGroups;
    }
    
    public void setPivotGroups(PivotGroups pivotGroups)
    {
    	this.pivotGroups = pivotGroups;
    }
    
    public PivotGroups setPivotGroups(){
    	if(this.pivotGroups == null) {
    		PivotGroups pivotGroups = new PivotGroups();
        	setPivotGroups(pivotGroups);
    	}
    	return this.pivotGroups;
    }
    
    public static Function getFunctionFromString(String function)
    {
        if(function != null)
        {
            if("average".equals(function))
            {
                return Function.AVERAGE;
            }
            else if("count".equals(function))
            {
                return Function.COUNT;
            }
            else if("countnums".equals(function))
            {
                return Function.COUNTNUMS;
            }
            else if("max".equals(function))
            {
                return Function.MAX;
            }
            else if("median".equals(function))
            {
                return Function.MEDIAN;
            }
            else if("min".equals(function))
            {
                return Function.MIN;
            }
            else if("product".equals(function))
            {
                return Function.PRODUCT;
            }
            else if("stdev".equals(function))
            {
                return Function.STDEV;
            }
            else if("stdevp".equals(function))
            {
                return Function.STDEVP;
            }
            else if("sum".equals(function))
            {
                return Function.SUM;
            }
            else if("var".equals(function))
            {
                return Function.VAR;
            }
            else if("varp".equals(function))
            {
                return Function.VARP;
            }
            else if("distinctcount".equals(function)){
                return Function.DISTINCTCOUNT;
            }
            else
            {
                return Function.AUTO;
            }
        }
        return null;
    }
    
  /*  public static PeriodType getPeriodTypeFromString(String periodType)
    {
        if(periodType != null){
            if("Year".equals(periodType)){//NO I18N
                return PeriodType.YEAR;
            }else if("Quarter".equals(periodType)){//NO I18N
            	return PeriodType.QUARTER;
            }else if("Month".equals(periodType)){//NO I18N
            	return PeriodType.MONTH;
            }else if("Day".equals(periodType)){//NO I18N
            	return PeriodType.DAY;
            }
        }
        return null;
    }*/
    /*************************************/
    public PivotField() {
    	
    }
    
    public PeriodType getPeriodType()
    {
        if(this.periodType == null){
            setPeriodType();
        }
        return this.periodType;
    }
    
    public void setPeriodType(PeriodType periodType){
    	this.periodType = periodType;
    }
    public void setPeriodType()
    {
    	switch(this.usedHierarchy){
    	case -1:
    	case 0:
    		periodType = PeriodType.NONE; //NO I18N
			break;
		case 1:
			periodType = PeriodType.YEAR; //NO I18N
			break;
		case 2:
			periodType = PeriodType.QUARTER; //NO I18N
			break;
		case 3:
			periodType = PeriodType.MONTH; //NO I18N
			break;
		case 4:
			periodType = PeriodType.DAY;
			break;
		case 5:
			periodType = PeriodType.DAY_OF_WEEK;
			break;
		case 6:
			periodType = PeriodType.QUARTERBYYEAR;
			break;
		case 7:
			periodType = PeriodType.MONTHBYYEAR;
			break;
        case 8:
            periodType = PeriodType.HOUR;
            break;
        case 9:
            periodType = PeriodType.MINUTE;
            break;
        case 10:
            periodType = PeriodType.WEEK;
            break;
        case 11:
            periodType = PeriodType.WEEK_OF_YEAR;
            break;
	
		default:
			periodType = PeriodType.DAY;//NO I18N
		}
    }
    
   /* public boolean isDummy() {
		return dummy;
	}

	public void setDummy(boolean dummy) {
		this.dummy = dummy;
	}*/
    
    public FieldType getColRangeType()
    {
        return this.colRangeType;
    }
    
    public void setColRangeType(FieldType colRangeType){
    	this.colRangeType = colRangeType;
    }

	/**dummyPivotField 
     * @param fieldRange
     * @param colIndex
     * @param orientation
     * @param usedHierarchy
     * @throws Exception
     */
    public PivotField(Workbook workbook, PivotComponent pCom, Orientation orientation) throws Exception {
    	this.orientation = orientation;
    	this.isDataLayoutField = Boolean.toString(true);
    	setUniqueContentDataLayoutList(workbook, pCom, pCom.getDataList());
	}
    /**
     * @param fieldRange
     * @param colIndex
     * @param orientation
     * @param usedHierarchy
     * @throws Exception
     */
    public PivotField(Workbook workbook, DataRange sourceRange, boolean hasHeaderRow, int colIndex, Orientation orientation, int usedHierarchy, JSONArrayWrapper fieldChoices, JSONObjectWrapper colInfo) throws Exception {
        String fieldNameFromColInfo = colInfo!=null ? colInfo.getString("content") : null;
		this.sourceFieldName = fieldNameFromColInfo==null? workbook.getSheetByAssociatedName(sourceRange.getAssociatedSheetName()).getCell(sourceRange.getStartRowIndex(), colIndex).getContent() : fieldNameFromColInfo;
        this.fieldIndex = colIndex;
		this.orientation = orientation;
		this.usedHierarchy = usedHierarchy;
		this.fieldChoices = fieldChoices;
		this.colInfo = colInfo;
		setPeriodType();
		/*if(this.orientation != Orientation.DATA){
			setUniqueContentMap();
		}else{
			setUniqueContentList();
		}*/
		
		setUniqueContent(workbook, sourceRange, hasHeaderRow);
		setPivotLevel();
		setFilteredUniqueContentList(workbook.getSpreadsheetSettings());
	}
    //Applied filter will be gone while calling below method
    public void getRefresh(Workbook workbook, DataRange sourceRange, boolean hasHeaderRow) throws Exception{
    	uniqueContentContainer = new UniqueContentContainer();
    	uniqueContentList 				= new ArrayList<String>();
    	setUniqueContentMap(workbook, sourceRange, hasHeaderRow);
    	setPivotLevel();
    	setFilteredUniqueContentList(workbook.getSpreadsheetSettings());
    }
    /**
	 * @return the uniqueContentList
	 */
	public List<String> getUniqueCotentList() {
		return uniqueContentList;
	}
	/**
	 * @return the filteredUniqueContentList
	 */
	public List<String> getFilteredUniqueContentList() {
		return filteredUniqueContentList;
	}
	/**
	 * @param uniqueContentList the uniqueContentList to set
	 */
	public void setUniqueContentList(List<String> uniqueContentList) {
		this.uniqueContentList = uniqueContentList;
	}
	public DataPivotUniqueContentDetails getDataPivotUniqueContentDetails(String cellContent){
		return this.uniqueContentContainer.getContentDetails(cellContent);
	}
	public void setUniqueContent(Workbook workbook, DataRange sourceRange, boolean hasHeaderRow)throws Exception{
		if(this.orientation != Orientation.DATA){
			setUniqueContentMap(workbook, sourceRange, hasHeaderRow);
		}else{
			setUniqueContentList(workbook, sourceRange, hasHeaderRow);
		}
	}
	public void setUniqueContentDataLayoutList(Workbook workbook, PivotComponent pCom, List<PivotField> dataPivotFields){
		this.sourceFieldName = pCom.getI18nMsg(workbook.getSpreadsheetSettings().getLocale()).getMsg("ISet.Values"); //NO I18N
		filteredUniqueContentList 				= new ArrayList<String>();
    	uniqueContentContainer = new UniqueContentContainer();
    	DataPivotUniqueContentDetails uniqueContentDetails = null;
		int dataFieldPos = 0;
		for(PivotField dataPivotField : dataPivotFields){
    		String functionoffieldName = pCom.getFunctionofDataFieldMsg(workbook, dataPivotField.getFunction(), dataPivotField.getSourceFieldName());
    		filteredUniqueContentList.add(functionoffieldName);
    		uniqueContentDetails = new DataPivotUniqueContentDetails(functionoffieldName);
    		for(int rowIndex : dataPivotField.getDataRowIndexList()){
    			uniqueContentDetails.addRowIndexList(rowIndex);
    		}
    		IndexHolder indexHolder = new IndexHolder();
    		indexHolder.setColIndex(dataPivotField.getColIndex());
    		indexHolder.setFunction(dataPivotField.getFunction());
    		if(dataPivotField.getPivotFieldReference()!=null){
    			indexHolder.setPivotFieldRefType(dataPivotField.getPivotFieldReference().getType());
    		}
    		dataColIdxFunctionMap.put(""+dataFieldPos, indexHolder);
    		uniqueContentContainer.addContentDetails(functionoffieldName, uniqueContentDetails);
    		//logger.info(dataPivotField.getDataRowIndexList().size()+ "getDataRowIndexList  :: "+dataPivotField.getDataRowIndexList());
    		dataFieldPos++;
    	}
	}
	//Following method is written with the reference of filterRange.java updateCellDetails method ~Mani
	public void setUniqueContentList(Workbook workbook, DataRange sourceRange, boolean hasHeaderRow){
		uniqueContentList 				= new ArrayList<String>();
		dataRowIndexList 				= new ArrayList<Integer>();
		List<String> tempList 				= new ArrayList<String>();
		int 	startRowIndex  = hasHeaderRow ? (sourceRange.getStartRowIndex()+1) : sourceRange.getStartRowIndex();
		int 	endRowIndex    = sourceRange.getEndRowIndex();
		Sheet 	sheet 		   = workbook.getSheetByAssociatedName(sourceRange.getAssociatedSheetName());
		Cell 		 cell      = null;
		String cellContent = "";
		for(int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {
                ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(rowIndex, getColIndex());
				cell  = rCell.getCell();
	            if(cell != null)
	            {
	                cellContent = Utility.masknull(cell.getContent(), "").trim();
	               
	             }
	            String contentLowerCase = cellContent.toLowerCase();
	            tempList.add(contentLowerCase);
	            dataRowIndexList.add(rowIndex);
            rowIndex += (rCell.getRowsRepeated() - 1);
		}
		Set<String> uniqueContentSet = new HashSet<String>(tempList);
		uniqueContentList.addAll(uniqueContentSet);
		
		
		
	}
	public List<Integer> getDataRowIndexList() {
		return dataRowIndexList;
	}
	public UniqueContentContainer setUniqueContentMap(Workbook workbook, DataRange sourceRange, boolean hasHeaderRow) throws Exception{
		//logger.info("*** ContentMap "+this.orientation+" ***");
		uniqueContentContainer = new UniqueContentContainer();
    	uniqueContentList 				= new ArrayList<String>();
		int 	startRowIndex  = hasHeaderRow ? (sourceRange.getStartRowIndex()+1) : sourceRange.getStartRowIndex();
		int 	endRowIndex    = sourceRange.getEndRowIndex();
		Sheet 	sheet 		   = workbook.getSheetByAssociatedName(sourceRange.getAssociatedSheetName());
		
		//logger.info("startRowIndex: "+startRowIndex+" endRowIndex: "+endRowIndex+" colIndex: "+colIndex);
		
		Cell 		 cell      = null;
		Value 		 value     = null; 
		CalendarValue calenderValue = null;
		String cellContent ;
//		List<String> tempList 				= new ArrayList<String>();
		Map<Value, String> valueContentMap = new HashMap<Value, String>();
		Map<CalendarValue, String> CalValueContentMap = new HashMap<CalendarValue, String>();
		DataPivotUniqueContentDetails uniqueContentDetails = null;
		String colRangeStr =  PivotUtil.getOptimalColInfo(sheet,startRowIndex,endRowIndex,getColIndex(),null).getString("type");
        this.colRangeType = FieldType.valueOf(colRangeStr);
        boolean isBlankOrText = false;
        ArrayList<String> blankAndTextArray = new ArrayList<>();
        Date startDate = null ,endDate = null;
        contentTypes.clear();
		for(int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {
			value     = null; 
			ReadOnlyCell rCell = sheet.getReadOnlyCellFromShell(rowIndex, getColIndex());
			cell  = rCell.getCell();
			if (cell != null) {
				value = cell.getValue();
				//cellContent = cell.getContent();
                cellContent = valueContentMap.getOrDefault(value, Utility.masknull(cell.getContent(), "").trim());
					switch(cell.getContentType()){
	                 case FLOAT:
	                 case CURRENCY:
                     case PERCENTAGE:
                     case FRACTION:
                         if (((Number)value.getValue()).doubleValue() - ((Number)value.getValue()).intValue() == 0) {
                             addContentType(ContentType.INTEGER);
                         }
                         else {
                             addContentType(ContentType.NUMBER);
                         }
	                     break;
	                 case DATETIME:
	                 case DATE:
	                 case TIME:
                         addContentType(ContentType.DATE);
	                     break;
	                 case UNDEFINED:
                         addContentType(ContentType.BLANK);
	                 	break;
	                 default:
                         if(value.getValue() instanceof String && ((String)value.getValue()).length() > 255){
                             addContentType(ContentType.LONG_TEXT);
                         }
                         else {
                             addContentType(ContentType.STRING);
                         }
	             }
				
			} else {
				value = Value.EMPTY_VALUE;
				cellContent = "";
			}
			 
			if (cellContent == null) {
				cellContent = "";
			}
			try{
				calenderValue = null;
				 Object valObj = value.getValue();
				 Type type = cell != null ? cell.getContentType() : null;
				if(this.pivotGroups != null && this.pivotGroups.getDateStart() == null && (type != null && type != Type.SCIENTIFIC && type.isNumberType())) {
					if(valObj instanceof Number)
                    {	
						if(colInfo == null || !this.colInfo.has("maxVal")) {
							this.colInfo = PivotUtil.getColInfo(sheet, startRowIndex, endRowIndex, getColIndex());
						}
						
						Number startInt = pivotGroups.getStart().contains("auto") ? (Number)this.colInfo.get("minVal") : Double.parseDouble(pivotGroups.getStart()); // NO I18N
						Number endInt = pivotGroups.getEnd().contains("auto") ? (Number)this.colInfo.get("maxVal") : Double.parseDouble(pivotGroups.getEnd()); // NO I18N
						double step = pivotGroups.getStep();
						GroupValue gValue = new PivotNumericGroup(startInt, endInt, step, valObj, this.colInfo.getBoolean("isDec"));// NO I18N
						cellContent = gValue.getGroupedValue();
                    }
				}else if( cell != null && cell.getContentType().isDateType()){

                    if (valObj instanceof Number) {
                        valObj = DateUtil.convertNumberToDate((Number) value.getValue());
                    }
                    if(this.getPeriodType() != PeriodType.NONE ) {
                        calenderValue = new CalendarValue((Date) valObj);
                        //calenderValue = value.getCalenderValue();
                        cellContent = calenderValue.getGroupedValue(this.getPeriodType());
                    }
                    else if(valObj instanceof Date){
                        cellContent = value.getValueString(workbook.getSpreadsheetSettings(),cell.getType());
                    }
                    startDate = (startDate == null || ((Date) valObj).compareTo(startDate) < 0 ) ?(Date) valObj : startDate;
                    endDate = (endDate == null || ((Date) valObj).compareTo(endDate) > 0 ) ?(Date) valObj : endDate;
                }
			}catch(ClassCastException e){
				logger.info("ClassCastException thorwn :"+e);
				cellContent = cell.getContent();
			}
            uniqueContentDetails = uniqueContentContainer.getContentDetails(cellContent);
			if(uniqueContentDetails == null){
				if(this.getPeriodType() != PeriodType.NONE ){
                    if(calenderValue != null){
					    CalValueContentMap.put(calenderValue, cellContent);
                    }
                    else{
                        isBlankOrText = true;
                        blankAndTextArray.add(cellContent);
                    }
				}else if(value     != null ){
					valueContentMap.put(value, cellContent);
				}
				if(isBlankOrText || calenderValue != null || value     != null){
                    Type type = cell != null ? cell.getContentType() : Type.UNDEFINED;
					uniqueContentDetails = new DataPivotUniqueContentDetails(cellContent, value,type);
					uniqueContentContainer.addContentDetails(cellContent, uniqueContentDetails);
				}
			}
			
			if(isBlankOrText || calenderValue != null || value     != null){
				uniqueContentDetails.addRowIndexList(rowIndex);
			}
            rowIndex += (rCell.getRowsRepeated() - 1);
		}
//		logger.info("fieldChoices is  null: "+fieldChoices);
		if(fieldChoices!=null){
			//logger.info("fieldChoices is not null: "+valueContentMap.size()+" "+fieldChoices.length());
			if(valueContentMap.size()!=fieldChoices.length()){
				//logger.info("tempList: "+tempList);
				for(int i=0;i<fieldChoices.length();i++){
					if(!uniqueContentContainer.hasContentDetails(fieldChoices.getString(i))){
						//logger.info("fieldChoices.get(i): "+fieldChoices.get(i));
						Value valueObj1 = Value.getInstance(Type.STRING, fieldChoices.get(i));
						uniqueContentDetails = new DataPivotUniqueContentDetails((String)fieldChoices.get(i), valueObj1,Type.STRING);
						uniqueContentContainer.addContentDetails((String)fieldChoices.get(i), uniqueContentDetails);
						valueContentMap.put(valueObj1, (String)fieldChoices.get(i));
					}
				}
			}
		}
		
		
		Value[] valueArray = null;
		CalendarValue[] calValueArray = null;
		
		if(this.getPeriodType() == PeriodType.NONE){
			//logger.info("valueContentMap.keySet() ==>: "+valueContentMap.keySet());
			valueArray = valueContentMap.keySet().toArray(new Value[]{});
		}else{
			//logger.info("CalValueContentMap.keySet() ==>: "+CalValueContentMap.keySet());
			calValueArray = CalValueContentMap.keySet().toArray(new CalendarValue[]{});
			
		}
		//logger.info("valueArray 222==>: "+valueArray.length);
		//logger.info("================================== ");
		/*logger.info("valueArray: "+valueArray);
		for (Value o : valueArray) { 
			
			logger.info("value ==>: "+o);
			 
			}*/
		try{
			
			if(this.pivotLevel == null || this.pivotLevel.getPivotSortInfo().getOrder().equals(Order.ASCENDING)){
				if(this.getPeriodType() == PeriodType.NONE){
				Arrays.sort(valueArray, new Comparator<Value>() {
					    public int compare(Value o1, Value o2) {
					        if(o1.getType() == Type.STRING && o2.getType() == Type.STRING){
					            return ((String)o1.getValue()).toLowerCase().compareTo(((String)o2.getValue()).toLowerCase());
                            }
					        return o1.compareTo(o2);
					    }
					});
				}else{
					Arrays.sort(calValueArray, new Comparator<CalendarValue>() {
					    public int compare(CalendarValue o1, CalendarValue o2) {
					        return o2.compareTo(o1);
					    }
					});
				}
			}else{
				if(this.getPeriodType() == PeriodType.NONE){
					Arrays.sort(valueArray, new Comparator<Value>() {
					    public int compare(Value o1, Value o2) {
					        if(o1.getType() == Type.STRING && o2.getType() == Type.STRING){
					            return ((String)o2.getValue()).toLowerCase().compareTo(((String)o1.getValue()).toLowerCase());
                            }
					        return o2.compareTo(o1);
					    }
					});
					}else{
						Arrays.sort(calValueArray, new Comparator<CalendarValue>() {
						    public int compare(CalendarValue o1, CalendarValue o2) {
						        return o1.compareTo(o2);
						    }
						});
					}
				
			}
		}catch(Exception e){
			//e.printStackTrace();
			logger.log(Level.WARNING,"Exception :", e); 
		}


                //Arrays.sort(valueArray);
		String data = null; 
		if(this.getPeriodType() == PeriodType.NONE){
			boolean isContainEmpty = false;
			for (Value o : valueArray) { 
			data = valueContentMap.get(o).toString(); 
			if(!data.equals("")){
				uniqueContentList.add(data); 
			}else{
				isContainEmpty = true;
			}
			
			}
			if(isContainEmpty){
				uniqueContentList.add(""); 
			}
		}else{
			for (CalendarValue o : calValueArray) { 
				data = CalValueContentMap.get(o).toString(); 
				uniqueContentList.add(data);
				}
            if(isBlankOrText){
                uniqueContentList.addAll(blankAndTextArray);
            }
		}
		if(startDate != null && endDate != null &&
                 ((contentTypes.contains(ContentType.DATE) &&
                         (usedHierarchy == -1 && this.getOrientation() == Orientation.HIDDEN) ||
                         (contentTypes.contains(ContentType.BLANK) ? contentTypes.size() == 2 : contentTypes.size() == 1))
                 )){
            //start and endDate is needed for xlsx writing
            PivotGroups groups = this.setPivotGroups();
            groups.setDateStart(startDate);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            endDate = calendar.getTime();
            groups.setDateEnd(endDate);
        }
		if(uniqueContentList.size() > EngineConstants.PIVOT_ROW_UNIQUE_LIMIT && this.orientation == Orientation.ROW){
			throw new ProhibitedActionException(ErrorCode.ERROR_ROW_LIMIT_EXCEED);
		}else if(uniqueContentList.size() > EngineConstants.PIVOT_COL_UNIQUE_LIMIT && this.orientation == Orientation.COLUMN){
			throw new ProhibitedActionException(ErrorCode.ERROR_COLUMN_LIMIT_EXCEED);
		}
		//logger.info(sourceFieldName +" : "+uniqueContentMap.size());
		/*for (String rowCellContent : uniqueContentList) {
			logger.info(rowCellContent +" : "+uniqueContentMap.get(rowCellContent).rowIndexList);
			
		}*/
		return uniqueContentContainer;
	}

	/**
	 * @return the colIndex
	 */
	public int getColIndex() {
		return fieldIndex;
	}
	
	public JSONObjectWrapper getColInfo() {
		return colInfo;
	}

	public void setColInfo(JSONObjectWrapper colInfo) {
		this.colInfo = colInfo;
	}
	/**
	 * @param colIndex the colIndex to set
	 */
	public void setColIndex(int colIndex) {
        this.fieldIndex = colIndex;
	}
	
	  public Map<String, IndexHolder> getDataColIdxFunctionMap() {
			return dataColIdxFunctionMap;
		}

		public void setDataColIdxFunctionMap(Map<String, IndexHolder> dataColIdxFunctionMap) {
			this.dataColIdxFunctionMap = dataColIdxFunctionMap;
		}
	
	/**
	 * @param 
	 */
	public void setPivotLevel() {
		//logger.info("setPivotLevel: called");
		PivotLevel pivotLevel = new PivotLevel();
		for(String uniqueContent : uniqueContentList){
			PivotMember pivotMember = new PivotMember();
			pivotMember.setName(uniqueContent);
			pivotLevel.addPivotMember(pivotMember);
		}
		
		
		
		setPivotLevel(pivotLevel);
		
		setSubTotal();
	}
	public void setSubTotal() {
		// if(!Boolean.getBoolean(this.getIsDataLayoutField()) && this.getOrientation().equals(Orientation.ROW) || this.getOrientation().equals(Orientation.COLUMN)){
		 if(this.getIsDataLayoutField()==null && (this.getOrientation().equals(Orientation.ROW) || this.getOrientation().equals(Orientation.COLUMN) || this.getOrientation().equals(Orientation.SLICER)) ){
				PivotSubtotal pivotSubtotal = new PivotSubtotal();
				pivotSubtotal.setFunction(Function.AUTO);
				pivotLevel.addPivotSubtotals(pivotSubtotal);
			 }
		
	}
	/**
	 * @param 
	 */
	public void setFilteredUniqueContentList(SpreadsheetSettings spreadsheetSettings) {
        //Note: Here locale is used for Date parsing only.
		//logger.info("setFilteredUniqueContentList: called");
		filteredUniqueContentList 				= new ArrayList<String>();
		tobefilteredRowIndexList 				= new ArrayList<Integer>();
		//for (PivotMember pivotMember : this.pivotLevel.getPivotMembers()) {
        Map<String, PivotMember> pivotMembers = new LinkedHashMap<>();
		for (String uniqueContent : uniqueContentList) {
			//logger.info("uniqueContent: "+uniqueContent);
			PivotMember pivotMember = getPivotMember(uniqueContent, spreadsheetSettings);
			String pivotMemberName = pivotMember.getName();
            pivotMembers.put(pivotMemberName, pivotMember);
			if(pivotMember.getDisplay()){
				filteredUniqueContentList.add(pivotMemberName);
			}else{
				for(int rowIndex : getDataPivotUniqueContentDetails(pivotMemberName).getRowIndexList()){
					tobefilteredRowIndexList.add(rowIndex);
				}
			}
		}
        this.pivotLevel.setPivotMembers(pivotMembers);
		//logger.info("tobefilteredRowIndexList :"+tobefilteredRowIndexList);
	}
	public ArrayList<Integer> getTobeFilteredRowIndexList() {
		return this.tobefilteredRowIndexList;
	}
	private PivotMember getPivotMember(String uniqueContent, SpreadsheetSettings spreadsheetSettings){
        //Note: Here locale is used for Date parsing only.
		PivotMember pivotMember = null;
		//logger.info("pivotMember is isFilterApplied:"+ this.pivotLevel.getIsFilterApplied());
		pivotMember = this.pivotLevel.getPivotMembers().get(uniqueContent);
		if(pivotMember == null){
			//logger.info("pivotMember is null:"+ uniqueContent);
			pivotMember = new PivotMember();
			pivotMember.setName(uniqueContent);
			if(this.pivotLevel.getIsFilterApplied()){
                if(this.pivotLevel.getPivotFilterInfo().getFilterType() == 1)
                {
                    PivotUtil.applySpecialFilter(this, pivotMember,spreadsheetSettings);
                }
                else
                {
                    pivotMember.setDisplay(false);
                }
			}
//			pivotLevel.addPivotMember(pivotMember);
            if(!pivotMember.getDisplay())
            {
                pivotLevel.setIsFilterApplied(true);
            }
		}
		return pivotMember;
	}
	//Used for Page Field alone
	
	public ArrayList getPageFilterRowIndexList() {
		ArrayList<Integer> pageFilterRowIndexList = new ArrayList<Integer>();
		for (String uniqueContent : uniqueContentList) {
			PivotMember pivotMember = this.pivotLevel.getPivotMembers().get(uniqueContent);
			String pivotMemberName = pivotMember.getName();
			if(pivotMember.getDisplay()){
				for (int rowIndex : uniqueContentContainer.getContentDetails(pivotMemberName).getRowIndexList()) {
					pageFilterRowIndexList.add(rowIndex);
				}
			}
		}
		return pageFilterRowIndexList;
	}
	public List<Integer> getFilteredDataRowIndexList(Set<Integer> pageFilterRowIndexList) {
		//logger.info("pageFilterRowIndexList: "+pageFilterRowIndexList+" dataRowIndexList: "+dataRowIndexList);	
				filteredDataRowIndexList = new ArrayList<Integer>();
				if(pageFilterRowIndexList != null ){
					for(int rowIndex : dataRowIndexList){
						
							if(pageFilterRowIndexList.contains(rowIndex)){
								filteredDataRowIndexList.add(rowIndex);
							}
						
	             	}
				}else{
					filteredDataRowIndexList = dataRowIndexList;
				}
			//logger.info("filteredDataRowIndexList: "+filteredDataRowIndexList);	
			
		return filteredDataRowIndexList;
	}
        
        public PivotField clone(Workbook workbook)
        {
            try
            {
                PivotField clonedField = (PivotField) super.clone();
                if (this.pivotLevel != null)
                {
                    clonedField.pivotLevel = this.pivotLevel.clone();
                }
                if (this.pivotFieldReference != null)
                {
                    clonedField.pivotFieldReference = this.pivotFieldReference.clone();
                }

                clonedField.fieldIndex = this.fieldIndex;

                if (this.uniqueContentContainer != null)
                {
                    clonedField.uniqueContentContainer = this.uniqueContentContainer.clone();
                }
                if (this.uniqueContentList != null)
                {
                    clonedField.uniqueContentList = new ArrayList(this.uniqueContentList);
                }
                if (this.dataRowIndexList != null)
                {
                    clonedField.dataRowIndexList = new ArrayList(this.dataRowIndexList);
                }
                if (this.filteredDataRowIndexList != null)
                {
                    clonedField.filteredDataRowIndexList = new ArrayList(this.filteredDataRowIndexList);
                }
                if (this.filteredUniqueContentList != null)
                {
                    clonedField.filteredUniqueContentList = new ArrayList(this.filteredUniqueContentList);
                }
                if (this.tobefilteredRowIndexList != null)
                {
                    clonedField.tobefilteredRowIndexList = new ArrayList(this.tobefilteredRowIndexList);
                }
                if (this.dataColIdxFunctionMap != null)
                {
                    clonedField.dataColIdxFunctionMap = new HashMap();
                    Set<String> keys = this.dataColIdxFunctionMap.keySet();
                    for (String key : keys)
                    {
                        clonedField.dataColIdxFunctionMap.put(key, this.dataColIdxFunctionMap.get(key).clone());
                    }
                }
                return clonedField;
            } catch (Exception e)
            {
                logger.log(Level.WARNING, "Error in cloning {0}", e);
            }
            return null;
        }
        
        /*****/

        private static class UniqueContentContainer
        {
            // Here Keys are only in LowerCase...
            private final Map<String, DataPivotUniqueContentDetails>  uniqueContentMap;

            private UniqueContentContainer() {
                this.uniqueContentMap = new HashMap<>();
            }

            private DataPivotUniqueContentDetails getContentDetails(String content) {
                return this.uniqueContentMap.get(content.toLowerCase());
            }

            private void addContentDetails(String content, DataPivotUniqueContentDetails uniqueContentDetails) {
                this.uniqueContentMap.put(content.toLowerCase(), uniqueContentDetails);
            }

            private boolean hasContentDetails(String content) {
                return uniqueContentMap.containsKey(((String)content).toLowerCase());
            }

            private void removeContentDetails(String content) {
                uniqueContentMap.remove(((String)content).toLowerCase());
            }

            public UniqueContentContainer clone() {
                UniqueContentContainer contentContainer = new UniqueContentContainer();
                contentContainer.uniqueContentMap.putAll(this.uniqueContentMap);
                return contentContainer;
            }
        }

        public boolean equalsForXLSXxml(PivotField that)
        {
            if(that == null)
            {
                return false;
            }
            if(this == that)
            {
                return true;
            }
            return  (this.getColIndex() == that.getColIndex() && Objects.equals(this.getPivotGroups(), that.getPivotGroups()) && this.getPeriodType() == that.getPeriodType());
        }
        public boolean isFieldGroupingApplied(){
            return orientation != Orientation.DATA && ((pivotGroups != null && pivotGroups.getDateStart() == null  ) || (getPeriodType() != PeriodType.NONE ));
        }
}
