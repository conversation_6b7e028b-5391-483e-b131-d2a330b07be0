//$Id$
package com.adventnet.zoho.websheet.model.theme;
/*
public class ColorShade {

	String accent;
	String light80;
	String light60;
	String light40;
	String dark25;
	String dark50;
	JSONObject colors;

	public ColorShade(String primaryColor) {
		this.accent = primaryColor;
		this.colors = ColorPaletteUtils.deriveSecondaryColors(primaryColor);
		this.colors.put(ShadesName.ACCENT, accent);
		this.light80 = this.colors.getString(ShadesName.LIGHT80);
		this.light60 = this.colors.getString(ShadesName.LIGHT60);
		this.light40 = this.colors.getString(ShadesName.LIGHT40);
		this.dark25 = this.colors.getString(ShadesName.DARK25);
		this.dark50 = this.colors.getString(ShadesName.DARK50);
	}

	public JSONObject getShades() {
		return this.colors;
	}

	public String getShade(Shades shade) {
		switch (shade) {
		case LIGHT80:
			return this.light80;
		case LIGHT60:
			return this.light60;
		case LIGHT40:
			return this.light40;
		case DARK25:
			return this.dark25;
		case DARK50:
			return this.dark50;
		}
		return "";
	}
}
*/