/* $Id$ */

package com.adventnet.zoho.websheet.model.usersettings;

import com.adventnet.iam.User;
import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.SpreadsheetSettings;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSThemesLibrary;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
//import static com.zoho.sheet.util.UserSettingsBean.UserSettings.*;

/**
 *
 * <AUTHOR> N J (ZT-0049)
 */
public class UserSpreadSheetSettings
{
	private static final Logger LOGGER = Logger.getLogger(UserSpreadSheetSettings.class.getName());
	private static JSONObjectWrapper readUserInfoProperty(Long zuid, String userName)
	{
		return EngineUtils.readUserProperty(zuid, userName);
	}

	private static JSONObjectWrapper readUserSettings(Long zuid, String userName)
	{
		JSONObjectWrapper userInfoProperty = readUserInfoProperty(zuid, userName);
		JSONObjectWrapper userSettings = userInfoProperty.optJSONObject(JSONConstants.USER_SPREADSHEET_SETTINGS);
		if(userSettings == null)
		{
			LOGGER.log(Level.INFO, "[USER_SETTINGS] Generating User settings for the user zuid {0}", new Object[]{zuid});
			userSettings = generateUserSettings(zuid);
			userInfoProperty.put(JSONConstants.USER_SPREADSHEET_SETTINGS, userSettings);
//			saveUserInfoProperty(zuid, userName, userInfoProperty);
		}
		return userSettings;
	}

	public static void removeUserSettings(Long zuid, String userName)
	{
		JSONObjectWrapper userInfoProperty = readUserInfoProperty(zuid, userName);
		JSONObjectWrapper userSettings = userInfoProperty.optJSONObject(JSONConstants.USER_SPREADSHEET_SETTINGS);
		if(userSettings != null)
		{
			userInfoProperty.remove(JSONConstants.USER_SPREADSHEET_SETTINGS);
			saveUserInfoProperty(zuid, userName, userInfoProperty);
		}
	}

	private static void saveUserInfoProperty(Long zuid, String userName, JSONObjectWrapper userInfoJSON)
	{
		EngineUtils.saveUserProperty(zuid, userName, new ArrayList<>(Arrays.asList(userInfoJSON)));
	}

	private static void validateUserSettings(JSONObjectWrapper userSettings) throws Exception
	{
		JSONObjectWrapper localeJSON = userSettings.getJSONObject(JSONConstants.LOCALE);
		String[] currencyLocaleStr = localeJSON.getString(JSONConstants.CURRENCY_LOCALE).split("_");
		Locale currencyLocale = new Locale(currencyLocaleStr[0], currencyLocaleStr[1]);
		Locale[] availableLocales = Locale.getAvailableLocales();
		boolean isLocaleExist = false;
		for(Locale locale : availableLocales)
		{
			if(locale.equals(currencyLocale))
			{
				isLocaleExist = true;
				break;
			}
		}
		if(!isLocaleExist)
		{
			LOGGER.log(Level.SEVERE, "[USER_SETTINGS][Exception] Currency Locale doesn't exist.");
			throw new Exception("[USER_SETTINGS][Exception] Currency Locale doesn't exist.");
		}
		try
		{
			char decimalSep = (char)localeJSON.getInt(JSONConstants.DECIMAL_SEP);
			char thousandSep = (char)localeJSON.getInt(JSONConstants.THOUSAND_SEP);
			SpreadsheetSettings.DecimalSeparator decimalSeparator = SpreadsheetSettings.DecimalSeparator.getDecimalSeparator(decimalSep);
			SpreadsheetSettings.ThousandSeparator thousandSeparator = SpreadsheetSettings.ThousandSeparator.getThousandSeparator(thousandSep);
			if(decimalSeparator.equals(thousandSeparator))
			{
				LOGGER.log(Level.SEVERE, "[USER_SETTINGS][Exception] Decimal and Thousand Separator can't be same.");
				throw new Exception("[USER_SETTINGS][Exception] Decimal and Thousand Separator can't be same.");
			}
		}
		catch(Exception e)
		{
			LOGGER.log(Level.SEVERE, "[USER_SETTINGS][Exception] Invalid Decimal/Thousand Separator", e);
			throw new Exception("[USER_SETTINGS][Exception] Invalid Decimal/Thousand Separator");
		}

	}
	/**
	 * @param user
	 * @param userSettingsJSON
	 */
	public static void saveUserSettings(User user, JSONObjectWrapper userSettingsJSON) throws Exception
	{
		validateUserSettings(userSettingsJSON);
		saveUserSettings(Long.valueOf(user.getZuid()), user.getLoginName(), userSettingsJSON);
	}

	private static void saveUserSettings(Long zuid, String userName, JSONObjectWrapper userSettingsJSON)
	{
		JSONObjectWrapper userInfoJSON = readUserInfoProperty(zuid, userName);
		userInfoJSON.put(JSONConstants.USER_SPREADSHEET_SETTINGS, userSettingsJSON);
		saveUserInfoProperty(zuid, userName, userInfoJSON);
	}
	
	private static JSONObjectWrapper getDefaultThemeJSON()
	{
		String defaultThemeName = EngineConstants.DEFAULT_THEME_NAME;
		JSONObjectWrapper themeJSON = ZSThemesLibrary.getTheme(defaultThemeName).getAsJSON();
		themeJSON.put(JSONConstants.THEME_NAME, defaultThemeName);
		return themeJSON;
	}

	private static JSONObjectWrapper getDefaultFontJSON()
	{
		String defaultFontName = EngineConstants.DEFAULT_THEME_FONT_NAME;
		String defaultFontSize = EngineConstants.DEFAULT_FONT_SIZE;
//		String defaultFontColor = EngineConstants.DEFAULT_TEXTCOLOR_HEXVALUE;

		JSONObjectWrapper fontJSON = new JSONObjectWrapper();
		fontJSON.put(JSONConstants.FONT_NAME, defaultFontName);
		fontJSON.put(JSONConstants.FONT_SIZE, defaultFontSize);
//		fontJSON.put(JSONConstants.FONT_COLOR, defaultFontColor);

		return fontJSON;
	}

	private static JSONObjectWrapper getDefaultCalcJSON()
	{
		JSONObjectWrapper itrCalcJSON = new JSONObjectWrapper();
		itrCalcJSON.put(JSONConstants.PATTERN_SETTING, EngineConstants.DEFAULT_PATTERN_SETTING.getId());
		itrCalcJSON.put(JSONConstants.IS_ITERATIVE_CALCULATION, EngineConstants.DEFAULT_IS_ITERATIVE_CALC);
		itrCalcJSON.put(JSONConstants.MAX_NUMBER_ITERATIONS, EngineConstants.DEFAULT_MAX_NUMBER_OF_ITERATIONS);
		itrCalcJSON.put(JSONConstants.THRESHOLD, EngineConstants.DEFAULT_THRESHOLD_VALUE);

		return itrCalcJSON;
	}

	private static JSONObjectWrapper getDefaultViewJSON()
	{
		Workbook.View view = EngineConstants.DEFAULT_SHEET_VIEW;
		JSONObjectWrapper viewJSON = new JSONObjectWrapper();
		viewJSON.put(JSONConstants.GRID_SPACING, view.getId());

		return viewJSON;
	}

	private static JSONObjectWrapper getDefaultEditJSON()
	{
		JSONObjectWrapper editJSON = new JSONObjectWrapper();
		editJSON.put(JSONConstants.PASTE_TYPE, EngineConstants.DEFAULT_PASTE_TYPE.getId());
		editJSON.put(JSONConstants.TURN_OFF_AUTOFILL, false);

		return editJSON;
	}

	private static JSONObjectWrapper getLocaleJSON(Locale currencyLocale, int sheetDirection, char decimalSep, char thousandSep, int dateFormat, String dateDisplayFormat, int groupingType)
	{
		JSONObjectWrapper localeJSON = new JSONObjectWrapper();

		localeJSON.put(JSONConstants.CURRENCY_LOCALE, currencyLocale.toString());
		localeJSON.put(JSONConstants.DECIMAL_SEP, decimalSep);
		localeJSON.put(JSONConstants.THOUSAND_SEP, thousandSep);
		localeJSON.put(JSONConstants.DATE_FORMAT, dateFormat);
		localeJSON.put(JSONConstants.DATE_DISPLAY_FORMAT, dateDisplayFormat);
		localeJSON.put(JSONConstants.SHEET_DIRECTION, sheetDirection);
		localeJSON.put(JSONConstants.NUMBER_GROUPING_TYPE, groupingType);

		return localeJSON;
	}

	private static JSONObjectWrapper getUserSettingsJSON(JSONObjectWrapper defaultThemeJSON, JSONObjectWrapper defaultFontJSON, JSONObjectWrapper defaultLocaleJSON, JSONObjectWrapper defaultEditJSON, JSONObjectWrapper defaultViewJSON, JSONObjectWrapper defaultItrCalcJSON)
	{
		JSONObjectWrapper userSettingsJSON = new JSONObjectWrapper();

		userSettingsJSON.put(JSONConstants.FONT, defaultFontJSON);
		userSettingsJSON.put(JSONConstants.THEME, defaultThemeJSON);
		userSettingsJSON.put(JSONConstants.LOCALE, defaultLocaleJSON);
		userSettingsJSON.put(JSONConstants.VIEW, defaultViewJSON);
		userSettingsJSON.put(JSONConstants.EDIT, defaultEditJSON);
		userSettingsJSON.put(JSONConstants.CALCULATION, defaultItrCalcJSON);

		userSettingsJSON.put(JSONConstants.IS_NEW_SETTINGS, true);
		return userSettingsJSON;
	}

	private static JSONObjectWrapper getUserSettingsJSON(SpreadsheetSettings defaultSettings)
	{
//		String dateDisplayFormat = DateUtil.getDefaultDateFormatString(DateUtil.DateFormatType.getDateFormatType(dateFormatType), true, true);
//		String currencyCode = LocaleUtil.getCurrencySymbol(dfs.getCurrency().getCurrencyCode());
		JSONObjectWrapper defaultLocaleJSON = defaultSettings.getJSON();
		JSONObjectWrapper defaultFontJSON = getDefaultFontJSON();
		JSONObjectWrapper defaultThemeJSON = getDefaultThemeJSON();
		JSONObjectWrapper defaultViewJSON = getDefaultViewJSON();
		JSONObjectWrapper defaultEditJSON = getDefaultEditJSON();
		JSONObjectWrapper defaultItrCalcJSON = getDefaultCalcJSON();
//		JSONObjectWrapper defaultLocaleJSON = getLocaleJSON(currencyLocale, displayDirection, decimalSep, thousandSep, dateFormatType, dateDisplayFormat, groupingType);

		return getUserSettingsJSON(defaultThemeJSON, defaultFontJSON, defaultLocaleJSON, defaultEditJSON, defaultViewJSON, defaultItrCalcJSON);
	}

	/**
	 * Generates user settings json from given parameter
	 * @param locale
	 * @param patternSetting
	 * @return
	 */
	public static JSONObjectWrapper generateUserSettings(Locale locale, TimeZone timeZone, Workbook.PatternSetting patternSetting, DateUtil.DateFormatType dateFormatType)
	{
		SpreadsheetSettings spreadsheetSettings = SpreadsheetSettings.getInstance(locale);

		JSONObjectWrapper userSettingsJSON = getUserSettingsJSON(spreadsheetSettings);
		if(timeZone != null)
		{
			userSettingsJSON.getJSONObject(JSONConstants.LOCALE).put(JSONConstants.TIME_ZONE, timeZone.getID());
		}
		if(patternSetting != null)
		{
			userSettingsJSON.getJSONObject(JSONConstants.CALCULATION).put(JSONConstants.PATTERN_SETTING, patternSetting.getId());
		}
		if(dateFormatType != null)
		{
			userSettingsJSON.getJSONObject(JSONConstants.LOCALE).put(JSONConstants.DATE_FORMAT, dateFormatType.getId());
		}
		return userSettingsJSON;
	}

	/**
	 * Generates user settings json from given parameter
	 * @param locale
	 * @return
	 */
	private static JSONObjectWrapper generateUserSettings(Locale locale, TimeZone timeZone, SpreadsheetSettings.DisplayDirection displayDirection)
	{
		SpreadsheetSettings spreadsheetSettings = SpreadsheetSettings.getInstance(locale, displayDirection);
		JSONObjectWrapper userSettings = getUserSettingsJSON(spreadsheetSettings);
		JSONObjectWrapper localeJSON = userSettings.getJSONObject(JSONConstants.LOCALE);
		localeJSON.put(JSONConstants.TIME_ZONE, timeZone.getID());

		return userSettings;
	}

	public static JSONObjectWrapper generateUserSettings(Locale locale)
	{
		return generateUserSettings(locale, new GregorianCalendar(locale).getTimeZone(), DocumentUtils.isRTLLanguage(locale.getLanguage()) ? SpreadsheetSettings.DisplayDirection.RIGHT_TO_LEFT : SpreadsheetSettings.DisplayDirection.LEFT_TO_RIGHT);
	}

	/**
	 * To be removed once all users has user settings.
	 *
	 */
	public static JSONObjectWrapper generateUserSettings(Long zuid)
	{
		User user = DocumentUtils.getZUserObject(zuid);
		Locale localeForSpreadsheet = EngineConstants.DEFAULT_LOCALE;
		Locale iamLocale = EngineConstants.DEFAULT_LOCALE;
		TimeZone timeZone = new GregorianCalendar(iamLocale).getTimeZone();
		if(user != null)
		{
			localeForSpreadsheet = LocaleUtil.getLocaleForSpreadSheet(user);
			iamLocale = LocaleUtil.getIAMLocale(user);
			timeZone = (TimeZone) LocaleUtil.getLocaleTimeZone(user)[1];
		}
		// UPDATE DISPLAY DIRECTION BASED ON IAM LANGUAGE
		SpreadsheetSettings.DisplayDirection displayDirection = DocumentUtils.isRTLLanguage(iamLocale.getLanguage()) ? SpreadsheetSettings.DisplayDirection.RIGHT_TO_LEFT : SpreadsheetSettings.DisplayDirection.LEFT_TO_RIGHT;
		JSONObjectWrapper userSettings = generateUserSettings(localeForSpreadsheet, timeZone, displayDirection);
		return userSettings;
	}

	public static JSONObjectWrapper generateUserSettings(User user)
	{
		return generateUserSettings(Long.parseLong(user.getZuid()));
	}

	private static JSONObjectWrapper getUserSettings(Long zuid, String userName)
	{
		return readUserSettings(zuid, userName);
	}

	public static JSONObjectWrapper getUserSettings(String zuid, String userName)
	{
		if(zuid == null || DocumentUtils.isAnonUser(zuid) || zuid.equals("-1"))
		{
			LOGGER.log(Level.WARNING, "[USER_SETTINGS] Invalid ZUID {0}, Generating User settings for default locale.", zuid);
			return generateUserSettings(EngineConstants.DEFAULT_LOCALE);
		}
		return readUserSettings(Long.parseLong(zuid), userName);
	}

	public static JSONObjectWrapper getUserSettings(User user)
	{
		if(user != null)
		{
			return getUserSettings(Long.valueOf(user.getZuid()), user.getLoginName());
		}
		LOGGER.log(Level.WARNING, "[USER_SETTINGS] IAM User is null, Generating User settings for default locale.");
		return generateUserSettings(EngineConstants.DEFAULT_LOCALE);
	}
}
