//$Id$
package com.adventnet.zoho.websheet.model.exception;

public class AbortParseError extends Error {
    public enum Cause {
        CELL_LIMIT,
        STRING_LIMIT,
        ZIP_ENTRIES_LIMIT,
        ZIP_SIZE_LIMIT
    }

    private final Cause cause;

    public AbortParseError(Cause cause) {
        super(cause.name());
        this.cause = cause;
    }

    public Cause getCauseType() {
        return cause;
    }
}
