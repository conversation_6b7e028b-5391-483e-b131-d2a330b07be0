/* $Id$ */
package com.adventnet.zoho.websheet.model;

import java.util.logging.Level;
import java.util.logging.Logger;

import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.response.ErrorResponseObject;
import com.adventnet.zoho.websheet.model.response.ResponseObject;
import com.adventnet.zoho.websheet.model.util.*;

/**
 * <AUTHOR>
 */

/***
 * 	PURPOSE : Now this is starting point for execution of an action.
 * 			  Added to new method, that will be responsible to send response to new client.
 * 			  Old implementation are same.
 */
public class ActionExecutorFixer extends ActionExecutor
{
	private static final Logger LOGGER = Logger.getLogger(ActionExecutorFixer.class.getName());

	public ActionExecutorFixer(WorkbookContainer wbContainer)
	{
		super(wbContainer);
	}

	protected void sendResponse(WorkbookContainer wbContainer, ResponseObject responseObject, JSONObjectWrapper actionObject, ContainerListener listener)
	{
		try
		{
			MessagePropagator.dispatchMessage_ThroughWorker(wbContainer, responseObject, actionObject, listener);
		}
		catch(Exception ex)
		{
			LOGGER.log(Level.INFO, "[RESPONSE] Exception in ActionExecutorFixer , while processing multi response to client", ex);
		}
	}

	protected static void sendErrorMessage(WorkbookContainer container, Workbook workbook, ContainerListener listener, JSONObjectWrapper actionObject, JSONObjectWrapper errorMsgObj)
	{
		try
		{
			String actionOwnerRSID = actionObject.has("rsid") ? actionObject.getString("rsid") : ""; //No I18N
			JSONObjectWrapper undoRedoCountJson = RedisHelper.getUndoRedoCount(container.getResourceKey(), actionOwnerRSID);
			errorMsgObj.put(String.valueOf(ActionConstants.UNDOREDOCOUNT), undoRedoCountJson);
			ResponseObject responseObject = new ErrorResponseObject(container, workbook, actionObject, errorMsgObj);
			MessagePropagator.dispatchMessage_ThroughWorker(container, responseObject, actionObject, listener);
		}
		catch(Exception ex)
		{
			LOGGER.log(Level.WARNING, "[RESPONSE][Exception] ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Error in sendErrorMessage() method. ErrorMessage: {3}", new Object[]{container.getResourceKey(), actionObject.get("zuid"), actionObject.has("rsid") ? actionObject.get("rsid") : "null", errorMsgObj});
			LOGGER.log(Level.WARNING, "[RESPONSE][Exception] ERROR RESOURCE_ID: " + container.getResourceKey() + " Error in sendErrorMessage() method.", ex);
		}
	}

}
