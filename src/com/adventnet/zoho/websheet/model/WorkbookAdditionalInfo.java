package com.adventnet.zoho.websheet.model;

import com.adventnet.iam.User;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.util.*;

import java.util.GregorianCalendar;
import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;

public final class WorkbookAdditionalInfo
{
	private final static Logger LOGGER = Logger.getLogger(WorkbookAdditionalInfo.class.getName());
	private final Locale dictionaryLocale;
	private final TimeZone timeZone;
//	private final ZSPattern dateDisplayFormat;
	private final Workbook.PatternSetting patternSetting;
	private final SpreadsheetSettings spreadsheetSettings;
	private final ReEvaluate.CalculationSettings calculationSettings;
	private final Workbook.View view;

	private WorkbookAdditionalInfo(Locale locale, TimeZone timeZone)
	{
		this.timeZone = timeZone;
		this.dictionaryLocale = locale;
		this.spreadsheetSettings = SpreadsheetSettings.getInstance(locale);
		this.calculationSettings = null;
		this.patternSetting = EngineConstants.DEFAULT_PATTERN_SETTING;
		this.view = EngineConstants.DEFAULT_SHEET_VIEW;
	}

	private WorkbookAdditionalInfo(Locale locale, TimeZone timeZone, SpreadsheetSettings.DisplayDirection displayDirection)
	{
		this.timeZone = timeZone;
		this.dictionaryLocale = locale;
		this.spreadsheetSettings = SpreadsheetSettings.getInstance(locale, displayDirection);
		this.calculationSettings = null;
		this.patternSetting = EngineConstants.DEFAULT_PATTERN_SETTING;
		this.view = EngineConstants.DEFAULT_SHEET_VIEW;
	}

	private WorkbookAdditionalInfo(TimeZone timeZone, Locale dictionaryLocale, Workbook.View view, SpreadsheetSettings spreadsheetSettings, ReEvaluate.CalculationSettings calculationSettings, Workbook.PatternSetting patternSetting)
	{
		this.timeZone = timeZone;
		this.dictionaryLocale = dictionaryLocale;
		this.view = view;
//		this.dateDisplayFormat = dateDisplayFormat;
		this.spreadsheetSettings = spreadsheetSettings;
		this.calculationSettings = calculationSettings;
		this.patternSetting = patternSetting;
	}

	private WorkbookAdditionalInfo(Locale inLocale, TimeZone timeZone, Locale dictionaryLocale, SpreadsheetSettings.DisplayDirection inDisplayDirection, Workbook.View view, boolean isItrCalc, int maxNoOfItr, double threshold, Workbook.PatternSetting patternSetting)
	{
		this.timeZone = timeZone;
		this.dictionaryLocale = dictionaryLocale;
		this.patternSetting = patternSetting;
		this.spreadsheetSettings = SpreadsheetSettings.getInstance(inLocale, inDisplayDirection);
//		this.dateDisplayFormat = dateDisplayFormat == null ? DefaultPatternUtil.getDefaultDatePattern(spreadsheetSettings) : dateDisplayFormat;
		if(isItrCalc)
		{
			this.calculationSettings = new ReEvaluate.CalculationSettings(maxNoOfItr, threshold);
		}
		else
		{
			this.calculationSettings = null;
		}
		this.view = view;
	}

	private WorkbookAdditionalInfo(WorkbookContainer container, JSONObjectWrapper newDocSettings)
	{
//		JSONObjectWrapper fontJSON = newDocSettings.getJSONObject(JSONConstants.FONT);
		JSONObjectWrapper localeJSON = newDocSettings.getJSONObject(JSONConstants.LOCALE);
//		JSONObjectWrapper viewJSON = newDocSettings.getJSONObject(JSONConstants.VIEW);
		JSONObjectWrapper calcJSON = newDocSettings.getJSONObject(JSONConstants.CALCULATION);

//		String fontName = fontJSON.getString(JSONConstants.FONT_NAME);
//		String fontSize = fontJSON.getString(JSONConstants.FONT_SIZE);
//		JSONObjectWrapper fontColorJSON = fontJSON.getJSONObject(JSONConstants.FONT_COLOR);
//		ZSColor fontColor = ZSColor.getColor(fontColorJSON.has(JSONConstants.HEXCOLOR) ? fontColorJSON.getString(JSONConstants.HEXCOLOR) : null, fontColorJSON.has(JSONConstants.THEMECOLOR) ? fontColorJSON.getString(JSONConstants.THEMECOLOR) : null, fontColorJSON.has(JSONConstants.TINT) ? fontColorJSON.getString(JSONConstants.TINT) : null);

		// LOCALE JSON
		SpreadsheetSettings spreadsheetSettings;
		String[] localeStr = localeJSON.getString(JSONConstants.LOCALE).split("_");
		Locale locale = new Locale(localeStr[0], localeStr[1]);
//		boolean isCustomLocale = localeJSON.optBoolean(JSONConstants.IS_CUSTOM_LOCALE, false);
//		if(!isCustomLocale)
//		{
//			spreadsheetSettings = SpreadsheetSettings.getInstance(locale);
//		}
//		else
		{
			// FETCH IAM ACCOUNTS LOCALE IF CUSTOM LOCALE IS SET IN SPREADSHEET SETTINGS
//			Locale locale = EngineConstants.DEFAULT_LOCALE;
//			User user = container.getCreatorIAMUser();
//			if(user != null)
//			{
//				locale = LocaleUtil.getCorrectedLocale(user);
//			}

			String[] currencyLocaleStr = localeJSON.getString(JSONConstants.CURRENCY_LOCALE).split("_");
			Locale currencyLocale = new Locale(currencyLocaleStr[0], currencyLocaleStr[1]);

			SpreadsheetSettings.DisplayDirection sheetDirection = SpreadsheetSettings.DisplayDirection.getDisplayDirection(localeJSON.getInt(JSONConstants.SHEET_DIRECTION));
			char decimalSep = (char)localeJSON.getInt(JSONConstants.DECIMAL_SEP);
			char thousandSep = (char)localeJSON.getInt(JSONConstants.THOUSAND_SEP);
			DateUtil.DateFormatType dateFormatType = DateUtil.DateFormatType.getDateFormatType(localeJSON.getInt(JSONConstants.DATE_FORMAT));
			// Checking for presence of NUMBER_GROUPING_TYPE just for the case if user settings was already created without this key.
			SpreadsheetSettings.NumberGroupingType groupingType = localeJSON.has(JSONConstants.NUMBER_GROUPING_TYPE) ? SpreadsheetSettings.NumberGroupingType.valueOf(localeJSON.getInt(JSONConstants.NUMBER_GROUPING_TYPE)) : SpreadsheetSettings.NumberGroupingType.GLOBAL;
//			ZSPattern dateDisplayFormat = DefaultPatternUtil.getDefaultDatePattern(locale, localeJSON.getString(JSONConstants.DATE_DISPLAY_FORMAT));
			spreadsheetSettings = SpreadsheetSettings.getInstance(locale, currencyLocale, decimalSep, thousandSep, dateFormatType, sheetDirection, groupingType, false);
		}

		Locale dictionaryLocale = EngineConstants.DEFAULT_LOCALE;
		if(localeJSON.has(JSONConstants.DICTIONARY_LOCALE))
		{
			String[] dictionaryLocaleStr = localeJSON.getString(JSONConstants.DICTIONARY_LOCALE).split("_");
			dictionaryLocale = new Locale(dictionaryLocaleStr[0], dictionaryLocaleStr[1]);
		}

		TimeZone timeZone = TimeZone.getTimeZone(localeJSON.getString(JSONConstants.TIME_ZONE));

		// LOCALE JSON

		// VIEW JSON

//		int viewID = viewJSON.getInt(JSONConstants.GRID_SPACING);
		Workbook.View view = EngineConstants.DEFAULT_SHEET_VIEW;

		// VIEW JSON


		// CALC JSON

		boolean isItrCalc = calcJSON.getBoolean(JSONConstants.IS_ITERATIVE_CALCULATION);
		int maxNoOfItr = calcJSON.getInt(JSONConstants.MAX_NUMBER_ITERATIONS);
		double threshold = calcJSON.getDouble(JSONConstants.THRESHOLD);
		Workbook.PatternSetting patternSetting = Workbook.PatternSetting.getPatternSettings(calcJSON.getInt(JSONConstants.PATTERN_SETTING));
		ReEvaluate.CalculationSettings calculationSettings = isItrCalc ? new ReEvaluate.CalculationSettings(maxNoOfItr, threshold) : null;

		// CALC JSON


//		this.fontName = fontName;
//		this.fontSize = fontSize;
//		this.fontColor = fontColor;
		this.timeZone = timeZone;
		this.dictionaryLocale = dictionaryLocale;
//		this.dateDisplayFormat = dateDisplayFormat;
		this.patternSetting = patternSetting;
		this.view = view;
		this.spreadsheetSettings = spreadsheetSettings;
		this.calculationSettings = calculationSettings;
	}

	public WorkbookAdditionalInfo(Locale locale, TimeZone timeZone, Locale currencyLocale, Locale dictionaryLocale, char decimalSeparator, char groupingSeparator, DateUtil.DateFormatType dateFormatType, SpreadsheetSettings.DisplayDirection sheetDirection, Workbook.View view, boolean isItrCalc, int maxNoOfItr, double threshold, Workbook.PatternSetting defaultPatternSetting, SpreadsheetSettings.NumberGroupingType groupingType)
	{

		this.timeZone = timeZone;
		this.dictionaryLocale = dictionaryLocale;
		this.patternSetting = defaultPatternSetting;
		this.spreadsheetSettings = SpreadsheetSettings.getInstance(locale, currencyLocale, decimalSeparator, groupingSeparator, dateFormatType, sheetDirection, groupingType, false);
//		this.dateDisplayFormat = dateDisplayFormat == null ? DefaultPatternUtil.getDefaultDatePattern(spreadsheetSettings) : dateDisplayFormat;
		if(isItrCalc)
		{
			this.calculationSettings = new ReEvaluate.CalculationSettings(maxNoOfItr, threshold);
		}
		else
		{
			this.calculationSettings = null;
		}
		this.view = view;
	}

	private static WorkbookAdditionalInfo getInstanceForOldSettings(JSONObjectWrapper docSettings)
	{
		String[] tempDocLocale = docSettings.getString(Constants.DOCUMENT_LOCALE).split("_");
		Locale locale = new Locale(tempDocLocale[0], tempDocLocale[1]);
		GregorianCalendar calendar = new GregorianCalendar(locale);
		String defaultTimeZoneId = calendar.getTimeZone().getID();
		TimeZone timeZone = TimeZone.getTimeZone(docSettings.optString(Constants.DOCUMENT_TIMEZONE, defaultTimeZoneId));
		Workbook.View view;
		if(docSettings.has(Constants.VIEW))
		{
			String viewStr = docSettings.getString(Constants.VIEW);
			try
			{
				int viewID = Integer.parseInt(viewStr);
				view = Workbook.View.getView(viewID);
			}
			catch(NumberFormatException nfe)
			{
				view = Workbook.View.valueOf(viewStr);
			}
		}
		else
		{
			view = Workbook.View.COZY;
		}
		SpreadsheetSettings.DisplayDirection direction;
		if(docSettings.has(Constants.SHEET_DIR))
		{
			String sheetDirStr = docSettings.getString(Constants.SHEET_DIR);
			if(Objects.equals(sheetDirStr, "true"))
			{
				direction = SpreadsheetSettings.DisplayDirection.RIGHT_TO_LEFT;
			}
			else
			{
				try
				{
					int sheetDirID = Integer.parseInt(sheetDirStr);
					direction = SpreadsheetSettings.DisplayDirection.getDisplayDirection(sheetDirID);
				}
				catch(NumberFormatException nfe)
				{
					direction = SpreadsheetSettings.DisplayDirection.LEFT_TO_RIGHT;
				}
			}
		}
		else
		{
			direction = SpreadsheetSettings.DisplayDirection.LEFT_TO_RIGHT;
		}
		// POPULATED FROM SPREADSHEET SETTINGS IN CONSTRUCTOR IF NULL
//		String dateDisplayFormatStr =  docSettings.optString(Constants.DATE_DISPLAY_FORMAT, null);
//		ZSPattern dateDisplayFormat = dateDisplayFormatStr != null ? DefaultPatternUtil.getDefaultDatePattern(locale, dateDisplayFormatStr) : null;

		String[] dictionaryLocaleStr = docSettings.has(Constants.DICTIONARY_LOCALE) ? docSettings.getString(Constants.DICTIONARY_LOCALE).split("_") : tempDocLocale;
		Locale dictionaryLocale = new Locale(dictionaryLocaleStr[0], dictionaryLocaleStr[1]);
		boolean isIterativeCalc = docSettings.has(Constants.ITERATIVE_CALCULATION) && docSettings.getBoolean(Constants.ITERATIVE_CALCULATION);
		int maxNoOfItr = docSettings.optInt(Constants.MAX_NUMBER_ITERATIONS, -1);
		double threshold = docSettings.optDouble(Constants.THRESHOLD, -1);
		Workbook.PatternSetting patternSetting = Workbook.PatternSetting.getPatternSettings(docSettings.optInt(Constants.PATTERN_SETTING, 1));
		return new WorkbookAdditionalInfo(locale, timeZone, dictionaryLocale, direction, view, isIterativeCalc, maxNoOfItr, threshold, patternSetting);
	}

	public static WorkbookAdditionalInfo getInstance(WorkbookContainer container, JSONObjectWrapper docSettings)
	{
		if(docSettings.has(JSONConstants.IS_NEW_SETTINGS))
		{
			return new WorkbookAdditionalInfo(container, docSettings);
		}
		return getInstanceForOldSettings(docSettings);
	}

	public static WorkbookAdditionalInfo getInstance(WorkbookContainer container) throws Exception
	{
		String resourceAdditionalInfo = container.getResourceAdditionalInfo();
		if(resourceAdditionalInfo == null || resourceAdditionalInfo.equalsIgnoreCase("NA"))
		{
			LOGGER.log(Level.WARNING, "[SPREADSHEET_SETTINGS] Returning Default Spreadsheet Settings as resource additional info is {0}", new Object[]{resourceAdditionalInfo});
			try {
				User creatorIAMUser = container.getCreatorIAMUser();
				if(creatorIAMUser != null) {
					WorkbookAdditionalInfo instance = WorkbookAdditionalInfo.getInstance(creatorIAMUser);
					if(instance == null) {
						LOGGER.log(Level.OFF, "getting null locale from user");
					} else {
						return instance;
					}
				}
			} catch(Exception e) {
				LOGGER.log(Level.OFF, "getting locale from user failed", e);
			}
			return WorkbookAdditionalInfo.getInstance(EngineConstants.DEFAULT_LOCALE);
		}
		JSONObjectWrapper docSettings = new JSONObjectWrapper(container.getResourceAdditionalInfo());
		if(docSettings.has(JSONConstants.IS_NEW_SETTINGS))
		{
			return new WorkbookAdditionalInfo(container, docSettings);
		}
		return getInstanceForOldSettings(docSettings);
	}

	public static WorkbookAdditionalInfo getInstance(Workbook workbook)
	{
		return new WorkbookAdditionalInfo(workbook.getUserTimezone(), workbook.getDictionaryLocale(), workbook.getView(), workbook.getSpreadsheetSettings(), workbook.getCalculationSettings(), workbook.getPatternSetting());
	}

	public static WorkbookAdditionalInfo getInstance(String localeStr)
	{
		Locale locale = LocaleUtil.getLocale(localeStr);
		GregorianCalendar calendar = new GregorianCalendar(locale);
		TimeZone timeZone = calendar.getTimeZone();
		return new WorkbookAdditionalInfo(locale, timeZone);
	}

	public static WorkbookAdditionalInfo getInstance(Locale locale)
	{
		return getInstance(locale.toString());
	}

	public static WorkbookAdditionalInfo getInstance(User user)
	{
		Locale locale = LocaleUtil.getCorrectedLocale(user);
		TimeZone timeZone = TimeZone.getTimeZone(user.getTimezone());
		SpreadsheetSettings.DisplayDirection displayDirection = DocumentUtils.isRTLLanguage(user.getLanguage()) ? SpreadsheetSettings.DisplayDirection.RIGHT_TO_LEFT : SpreadsheetSettings.DisplayDirection.LEFT_TO_RIGHT;
		return new WorkbookAdditionalInfo(locale, timeZone, displayDirection);
	}

	public Locale getDictionaryLocale()
	{
		return dictionaryLocale;
	}

	public TimeZone getTimeZone()
	{
		return timeZone;
	}

//	public ZSPattern getDateDisplayFormat()
//	{
//		return dateDisplayFormat;
//	}
//
//	public String getDateDisplayFormatStr()
//	{
//		return dateDisplayFormat.toPatternString(EngineConstants.DEFAULT_LOCALE);
//	}

	public Workbook.PatternSetting getPatternSetting()
	{
		return patternSetting;
	}

	public SpreadsheetSettings getSpreadsheetSettings()
	{
		return spreadsheetSettings;
	}

	public Locale getSpreadsheetLocale()
	{
		return this.getSpreadsheetSettings().getLocale();
	}

	public ReEvaluate.CalculationSettings getCalculationSettings()
	{
		return calculationSettings;
	}

	public boolean isIterativeCalc()
	{
		return this.calculationSettings != null;
	}

	public Workbook.View getView()
	{
		return view;
	}

	public JSONObjectWrapper getJSON(Workbook workbook)
	{
		CellStyle defaultCellStyle = workbook.getDefaultCellStyle();
		JSONObjectWrapper additionalInfo = new JSONObjectWrapper();
//		additionalInfo.put(JSONConstants.IS_NEW_SETTINGS, true);

		JSONObjectWrapper fontJSON = new JSONObjectWrapper();
		fontJSON.put(JSONConstants.FONT_NAME, defaultCellStyle.getProperty(TextStyle.Property.FONTNAME));
		fontJSON.put(JSONConstants.FONT_SIZE, defaultCellStyle.getProperty(TextStyle.Property.FONTSIZE));
		fontJSON.put(JSONConstants.FONT_COLOR, ZSColor.getHexColor((ZSColor)defaultCellStyle.getProperty(TextStyle.Property.COLOR), workbook.getTheme()));
		additionalInfo.put(JSONConstants.FONT, fontJSON);

		JSONObjectWrapper localeJSON = spreadsheetSettings.getJSON();
		localeJSON.put(JSONConstants.DICTIONARY_LOCALE, dictionaryLocale.toString());
		localeJSON.put(JSONConstants.TIME_ZONE, timeZone.getID());
//		localeJSON.put(JSONConstants.DATE_DISPLAY_FORMAT, dateDisplayFormat.toPatternString(EngineConstants.DEFAULT_LOCALE));

		additionalInfo.put(JSONConstants.LOCALE, localeJSON);

		JSONObjectWrapper calcJSON = new JSONObjectWrapper();
		int maxNoOfItr = isIterativeCalc() ? this.getCalculationSettings().getIterationCount() : EngineConstants.DEFAULT_MAX_NUMBER_OF_ITERATIONS;
		double threshold = isIterativeCalc() ? this.getCalculationSettings().getThreshold() : EngineConstants.DEFAULT_THRESHOLD_VALUE;

		calcJSON.put(JSONConstants.IS_ITERATIVE_CALCULATION, isIterativeCalc());
		calcJSON.put(JSONConstants.MAX_NUMBER_ITERATIONS, maxNoOfItr);
		calcJSON.put(JSONConstants.THRESHOLD, threshold);
		calcJSON.put(JSONConstants.PATTERN_SETTING, patternSetting.getId());

		additionalInfo.put(JSONConstants.CALCULATION, calcJSON);

		JSONObjectWrapper viewJSON = new JSONObjectWrapper();
		viewJSON.put(JSONConstants.GRID_LINE_COLOR, ZSColor.getHexColor(workbook.getWorkbookSettings().getWorkbookGridColor(), workbook.getTheme()));
		additionalInfo.put(JSONConstants.VIEW, viewJSON);

		return additionalInfo;
	}

	public JSONObjectWrapper getJSON()
	{
		JSONObjectWrapper additionalInfo = new JSONObjectWrapper();
		additionalInfo.put(JSONConstants.IS_NEW_SETTINGS, true);

		JSONObjectWrapper localeJSON = spreadsheetSettings.getJSON();
		localeJSON.put(JSONConstants.DICTIONARY_LOCALE, dictionaryLocale.toString());
		localeJSON.put(JSONConstants.TIME_ZONE, timeZone.getID());

//		localeJSON.put(JSONConstants.DATE_DISPLAY_FORMAT, dateDisplayFormat.toPatternString(EngineConstants.DEFAULT_LOCALE));
		additionalInfo.put(JSONConstants.LOCALE, localeJSON);

		JSONObjectWrapper calcJSON = new JSONObjectWrapper();
		int maxNoOfItr = isIterativeCalc() ? this.getCalculationSettings().getIterationCount() : EngineConstants.DEFAULT_MAX_NUMBER_OF_ITERATIONS;
		double threshold = isIterativeCalc() ? this.getCalculationSettings().getThreshold() : EngineConstants.DEFAULT_THRESHOLD_VALUE;

		calcJSON.put(JSONConstants.IS_ITERATIVE_CALCULATION, isIterativeCalc());
		calcJSON.put(JSONConstants.MAX_NUMBER_ITERATIONS, maxNoOfItr);
		calcJSON.put(JSONConstants.THRESHOLD, threshold);
		calcJSON.put(JSONConstants.PATTERN_SETTING, patternSetting.getId());

		additionalInfo.put(JSONConstants.CALCULATION, calcJSON);
//		additionalInfo.put(JSONConstants.VIEW, this.getView().getId());
		return additionalInfo;
	}
}
