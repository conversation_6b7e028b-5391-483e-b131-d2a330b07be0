//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.style.MapStyle;
import static com.adventnet.zoho.websheet.model.util.CellUtil.LOGGER;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ConditionalStyleObject implements Cloneable{
    
    private boolean isInvalid;
    private SpecialRange specialRange;
    private List<Double> intervals = new ArrayList<>();
    private ConditionalStyle conditionalStyle;
    private boolean isResetRangeLevel = false; // Is used only to reset the Intervals of ConditionalStyleObject.
    private CONDITIONALSTYLE_THEME themeType;
    private boolean isChangeTextColor;
    private boolean isHideText;
    
    public enum CONDITIONALSTYLE_THEME{
        NO_THEME,
        COLORSCALE,
        ICONSET,
        DATABAR
    }
    
    public boolean isInvalid() { 
        return isInvalid;
    }

    public void setIsInvalid(boolean isInvalid) {
        this.isInvalid = isInvalid;
    }
    
    public SpecialRange getSpecialRange() {
        return specialRange;
    }
    
    public boolean isResetRangeLevel() {
        return isResetRangeLevel;
    }
    
    public void setIsResetRangeLevel(boolean isResetRangeLevel) {
        if(!(conditionalStyle instanceof MapStyle))
        {
            this.isResetRangeLevel = isResetRangeLevel;
        }
    }
    
    public void setSpecialRange(SpecialRange specialRange) {
        this.specialRange = specialRange;
    }
    
    public List<Double> getIntervals() {
        return intervals;
    }
    
    public void setIntervals(List<Double> intervals) {
        this.intervals = intervals;
    }

    public ConditionalStyle getConditionalStyle() {
        return conditionalStyle;
    }
    
    public void setConditionalStyle(ConditionalStyle conditionalStyle) {
        this.conditionalStyle = conditionalStyle;
    }

    public CONDITIONALSTYLE_THEME getThemeType() {
        return themeType;
    }

    public void setThemeType(CONDITIONALSTYLE_THEME themeType) {
        this.themeType = themeType;
    }

    public boolean isChangeTextColor() {
        return isChangeTextColor;
    }

    public void setIsChangeTextColor(boolean isChangeTextColor) {
        this.isChangeTextColor = isChangeTextColor;
    }

    public boolean isHideText() {
        return isHideText;
    }

    public void setIsHideText(boolean isHideText) {
        this.isHideText = isHideText;
    }

    @Override
    public String toString() {
        return "ConditionalStyleObject{" + "isInvalid=" + isInvalid + ", specialRange=" + specialRange + ", intervals=" + intervals + ", conditionalStyle=" + conditionalStyle + ", themeType=" + themeType + ", isChangeTextColor=" + isChangeTextColor + ", isHideText=" + isHideText + '}'; // No I18N
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 29 * hash + Objects.hashCode(this.specialRange);
        hash = 29 * hash + Objects.hashCode(this.conditionalStyle);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ConditionalStyleObject other = (ConditionalStyleObject) obj;
        if (!Objects.equals(this.specialRange, other.specialRange)) {
            return false;
        }
        return Objects.equals(this.conditionalStyle, other.conditionalStyle);
    }

    @Override
    public ConditionalStyleObject clone() 
    {
        ConditionalStyleObject o = null;
        try{
            o = (ConditionalStyleObject) super.clone();
        }
        catch(CloneNotSupportedException e)
        {
            LOGGER.severe("ConditionalStyleObject can't clone"); //No I18N
        }
        return o; 
    }
}
