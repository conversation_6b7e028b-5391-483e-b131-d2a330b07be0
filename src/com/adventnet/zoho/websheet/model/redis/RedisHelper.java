//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.redis;

import com.adventnet.zoho.websheet.model.JSONObjectWrapper;
import com.adventnet.zoho.websheet.model.UserProfile;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.EnginePropertyUtil;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
import com.zoho.cache.cg.RedisConnectionPoolHandler;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Transaction;
import redis.clients.jedis.params.ScanParams;
import redis.clients.jedis.resps.ScanResult;
import redis.clients.jedis.resps.Tuple;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class RedisHelper
{
    private static Logger logger = Logger.getLogger(RedisHelper.class.getName());
    
    public static final String REDIS_POOL_NAME = "ZSheet"; //No I18N
    
    private static final String SERVER_INDEX = "doc:si:"; //No I18N
    
//    public static final String PUBLIC_SERVER_INDEX = "doc:psi:"; //No I18N
//    public static final String PRE_SERVER_INDEX = "doc:presi:"; //No I18N
    
    public static final String ACTION_ID = "doc:aid:"; //No I18N
    public static final String ACTIONS_LIST = "doc:list:"; //No I18N
    public static final String TEMP_ACTIONS_LIST = "doc:alist:"; //No I18N
    public static final String EXECUTED_ACTIONS_LIST = "doc:xlist:"; //No I18N
    public static final String EXECUTED_ACTIONS_CACHE_LIST = "doc:cache:xlist:"; //No I18N
    public static final String DC_MIG_META = "dc_mig_meta_rid:"; //No I18N
    
    public static final String UNDO_LIST = "doc:ulist:"; // "doc:ulist:RESOURCEID:RSID"       //No I18N
    public static final String REDO_LIST = "doc:rlist:"; //"doc:rlist:RESOURCEID:RSID"        //No I18N
    public static final String API_TOKEN_LIST = "doc:api:tokens:";       //No I18N
    
    public static final String THUMBNAIL_RID_LIST = "doc:thumbnailrids";   //NO I18N
    
    public static final String UNSAVED_WORKBOOKS = "doc:unsaved";   //NO I18N
    public static final String SAVE_SCHEDULER_WORKBOOKS = "doc:savescheduler";   //NO I18N
    public static final int SAVE_SCHEDULER_INTERVAL = 1 * 60 * 60 * 1000;   //in milliSeconds
    public static final int SAVE_SCHEDULER_INTERVAL_SAVE_SERVER = 1 * 60 * 1000;   //in milliSeconds
    
    public static final String SAVE_PRISONED_WORKBOOKS = "doc:saveprison";   //NO I18N
    public static final String PARSE_PRISONED_WORKBOOKS = "doc:parseprison";   //NO I18N
    private static final String PARTIALLY_SAVED = "doc:partially_saved";   //NO I18N

    public static final int UNDO_REDO_LIST_EXPIRE_TIME = 3 * 60 * 60;   //in seconds - 3hrs
    public static final int CACHE_LIST_EXPIRE_TIME = 30 * 60;   //in seconds - 30 mins
    public static final int CACHE_LIST_MAX_LIMIT = 200;
    public static final int ACTION_ID_EXPIRE_TIME = 45 * 24 * 60 * 60;   //in seconds - 45days
    public static final int SAR_DATA_EXPIRE_DATE = 7 * 24 * 60 * 60;   //in seconds - 45days
    public static final int PUBLISH_CHART_EXPIRE_TIME = 3 * 24 * 60 * 60;   //in seconds - 3days
    //public static final int CHART_CUTOMIZATION_EXPIRE_TIME = 7 * 24 * 60 * 60; // in seconds - 7 days
    
    
    public static final String AUTHORS_LIST = "doc:authors:";  // "doc:authors:RESOURCEID"       //No I18N
            
    public static final String SAVING_WORKBOOKS = "doc:issaving"; //NO I18N
    public static final int MAX_SAVING_TIME_PERIOD = 3 * 60;   //in seconds - 3 minutes
    public static final String EXCEPTION_COUNTER = "doc:expcounter"; //NO I18N
    
    public static final String PUBLISH_CHART = "doc:pchart:";	//NO I18N
    public static final String COPY_CHART_STYLE = "doc:chart:copy:style"; //NO I18N
    public static final String COPY_CHART = "doc:copychart:"; //NO I18N
    public static final String COPY_RANGE = "doc:copyrange:"; //NO I18N
//    public static final String COPY_CHART_CLIP = "doc:chart:copy:clip"; //NO I18N
//    public static final int COPY_CHART_CLIP_EXPIRE_TIME =  15 * 60;   //in seconds - 15 minutes same as server clip
    public static final int COPY_CHART_STYLE_EXPIRE_TIME =  15 * 60;   //in seconds - 15 minutes same as server clip
    public static final int USER_CLIP_EXPIRE_TIME =  15 * 60;   //in seconds - 15 minutes same as server clip
    //public static final String CHART_CUSTOMIZATION = "doc:cchart:"; //NO I18N
    public static final String PUBLIC_VIEW_COUNT = "doc:publicviewcount"; //NO I18N
    
    public static final String SHARE_REMOVE_INFO = "doc:share:removeinfo";  //NO I18N
    public static final String CHAT_LIST = "doc:chatlist:";  //NO I18N
    public static final String SPELL_CHECK_RANGE = "doc:spellcheck:range:";      //NO I18N
    public static final int SPELL_CHECK_EXPIRE_TIME = 10 * 60 * 60;    // in milliseconds

    public static final String TOUR_NAME = "doc:tour:name";  //NO I18N
    public static final String TOUR_ZUIDS = "doc:tour:zuids";  //NO I18N
    public static final String WALK_THROUGH_NEWVERSION_ZUIDS = "doc:walkthrough:newversion:zuids";  //NO I18N
    public static final String TOUR_RIPPLE_NEWVERSION_ZUIDS = "doc:tourripple:newversion:zuids";    //NO I18N
    public static final String ENABLE_SPFEEDBACKICON_NEWVERSION_ZUIDS = "doc:feedbackicon:newversion:zuids";    //NO I18N
    public static final String FEEDBACK_VIEW_STATUS_NEWVERSION_ZUIDS = "doc:feedbackviewstatus:newversion:zuids";    //NO I18N
    public static final String NEW_ANNOUNCEMENT_ZUIDS = "doc:newannouncement:zuids";    //NO I18N
    public static final String NEW_TAG_ACCESSED_ZUIDS = "doc:newtagaccessed:zuids";    //NO I18N
    public static final String HOEPN_ZUIDS_YES = "doc:hopen:zuids:yes";  //NO I18N
    public static final String HOEPN_ZUIDS_NO = "doc:hopen:zuids:no";  //NO I18N
    public static final String HOEPN_ZOIDS_YES = "doc:hopen:zoids:yes";  //NO I18N
    public static final String HOEPN_ZOIDS_NO = "doc:hopen:zoids:no";  //NO I18N
    public static final String ROEPN_ZUIDS_YES = "doc:ropen:zuids:yes";  //NO I18N
    public static final String ROEPN_ZOIDS_YES = "doc:ropen:zoids:yes";  //NO I18N
    public static final String IS_TOUR_ENABLED = "doc:tour:isenabled";  //NO I18N
    public static final String ADMIN_ANNOUNCEMENT_LEVEL = "doc:zsadmin:announcement:level";  //NO I18N
    public static final String ADMIN_ANNOUNCEMENT_MSG = "doc:zsadmin:announcement:msg";  //NO I18N
   
	 public static final String COPY_CLIP = "doc:copyclip:"; //NO I18N
	 public static final int COPYCLIP_EXPIRE_TIME = 15 * 60;   //in seconds - 15 minutes

    public static final String GDOC_ID = "doc:gdocid:";  //NO I18N
    public static final int GDOC_ID_EXPIRE_TIME = 1 * 60 * 60;   //in seconds - 1hr
    
    public static final String VERSIONS_LAST_ACTION_INFO = "doc:versionslastactioninfo:";        //No I18N
    public static final String VERSION_REVERT_MAP = "doc:versionrevertmap:"; //No I18N

	public static final String ADMIN_RELOADCLIENT_BUILD_VERSION_IN_REDIS = "doc:zsadmin:build_version";//No I18N
	public static final String ADMIN_RELOADCLIENT_MANDATORY_BANNER = "doc:zsadmin:mandatory_banner";//No I18N
        public static final String ADMIN_RELOADCLIENT_HIDDEN_REFRESH_BANNER = "doc:zsadmin:hiddenrefresh_banner";//No I18N
	
	// For landing page
	public static final String LISTING_PAGE_ORDER = "doc:zslistingpage:sorting_order";//No I18N
	public static final String LISTING_PAGE_ORDER_TYPE = "doc:zslistingpage:sorting_type";//No I18N
	public static final String LISTING_PAGE_THEME = "doc:zslistingpage:theme";//No I18N
	public static final String LISTING_PAGE_VIEW = "doc:zslistingpage:view";//No I18N
	public static final String LISTING_PAGE_LAST_VIEWED_PRODUCT = "doc:zslistingpage:last_viewed_product";//No I18N
        
    public static final String FOCUS_INFO = "doc:focusinfo:";//No I18N
    public static final String FOCUS_INFO_ZUID = "doc:focusinfo:zuid:";//No I18N
    public static final String EXCELVIEW_SHOW_BANNER = "doc:view:showbanner:";//No I18N

    public static final String ZUID_VS_USER_SETTINGS = ":user_settings";    // No I18N
    
    public static final String CELL_DATA_ERROR_RID = "doc:celldataerror:rid";//No I18N
    public static final String CELL_DATA_ERROR_DETAILS = "doc:celldataerror:errordetails";//No I18N

    public static final String STOCK_SYMBOLS_FETCH_TIME = "stock:symbols:fetch:time";//No I18N
    public static final String STOCK_QUOTE_FETCH_TIME = "stock:quote:fetch:time";//No I18N
    public static final String CURRENCY_FETCH_TIME = "currency:fetch:time";//No I18N
    public static final String IMPORTRANGE_ENABLED = "importrange:enabled";//No I18N
    public static final String AUTOMATION_MERGE_LIST = "doc:automation:merge:list"; //No I18N
    public static final String PUBLISHED_RANGES = "pub_ranges";	//NO I18N
    public static final int PUBLISHED_RANGES_EXPIRE_TIME = 15 * 60;

    public static final String CUSTOM_COLORS = "doc:custom_colors:";//No I18N
    public static final String LIST_NEW_TEMPLATES = "list_new_templates";//No I18N

    public static final String IP_TO_COUNTRYCODE = "ip:countrycode"; //NO I18N

    public static final int IP_COUNTRYCODE_EXPIRE_TIME = 24*60*60;// 1 day

    public static final String ZUID_USERNAME = "zuid:username:"; //NO I18N - Used for auditTrail/VersionHistory
    public static final int ZUID_USERNAME_EXPIRE_TIME = 15 * 60; //15mins
    public static final String CONVERSION_SERVER_LIBO_ENABLE="CS:Libo:Enable";//NO I18N
    public static final String CONVERSION_SERVER_MINI_ENABLE="CS:Mini:Enable";//NO I18N
    public static final String UPLOAD_SERVER_ID = "doc:import:uploadid";

    static {
        try {
            if(! Boolean.valueOf(EnginePropertyUtil.getEnginePropertyValue("IS_PARSE_ZSFRAGMENTS_IN_PARALLEL"))) {
                Jedis jedis = null;
                try {
                    jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
                    jedis.unlink(PARTIALLY_SAVED);
                } catch(Exception ex) {
                    logger.log(Level.WARNING, "unlink PARTIALLY_SAVED", ex);
                } finally {
                    if(jedis != null) {
                        RedisConnectionPoolHandler.returnRedis(jedis);
                    }
                }
            }
        } catch(Exception ex) {
            logger.log(Level.WARNING, "unlink PARTIALLY_SAVED", ex);
        }
    }
    public static boolean isRedisEnabled(String channel) throws Exception
    {
        Jedis jedis = null;
        try
        {
            String actionIdKey = ACTION_ID + channel;
            
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return (jedis.get(actionIdKey) != null);
            
        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while getting isRedisEnabled... KEY :: "+channel, ex);
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
    }
    
    
//    public static long addToTempActionsList(String resourceKey, JSONObjectWrapper actionObject) throws Exception
//    {
//        Jedis jedis = null;
//        Long actionId = 0L;
//        try
//        {
//            String key = TEMP_ACTIONS_LIST + resourceKey;
//            String actionIdKey = ACTION_ID + resourceKey;
//            
//            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
//            
//            actionId = jedis.incr(actionIdKey);
//            jedis.expire(actionIdKey, ACTION_ID_EXPIRE_TIME);
//            actionObject.set(JSONConstants.ACTION_ID, actionId);
//            
//            ////////
//            jedis.hset(key, actionId.toString(), actionObject.toString());
//            
//            jedis.zadd(UNSAVED_WORKBOOKS, System.currentTimeMillis(), resourceKey);
//        }
//        catch(Exception ex)
//        {
//            logger.log(Level.WARNING, "REDIS: Error while addToTempActionsList()... KEY :: "+resourceKey, ex);
//            throw ex;
//        }
//        finally
//        {
//            if(jedis != null)
//            {
//                // Do the disconnect now
//                RedisConnectionPoolHandler.returnRedis(jedis);
//            }
//        }
//        
//        return actionId;
//    }
    
    public static long addToActionsList(String resourceKey, JSONObjectWrapper actionObject) throws Exception
    {
        Jedis jedis = null;
        Long actionId = 0L;
        try
        {
            String key = ACTIONS_LIST + resourceKey;
            String actionIdKey = ACTION_ID + resourceKey;
            
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            
            actionId = jedis.incr(actionIdKey);
            jedis.expire(actionIdKey, ACTION_ID_EXPIRE_TIME);
            actionObject.set(JSONConstants.ACTION_ID, actionId);
            
            ////////
            jedis.hset(key, actionId.toString(), actionObject.toString());
            
            if(!hexists(RedisHelper.SAVE_PRISONED_WORKBOOKS, resourceKey))
            {
                jedis.zadd(UNSAVED_WORKBOOKS, System.currentTimeMillis(), resourceKey);
            }
        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while addToTempActionsList()... KEY :: "+resourceKey, ex);
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
        return actionId;
    }
    
    
    public static List loadTempActionsList(String channel) throws Exception {

        Jedis jedis = null;

        try {
            String hashKey = TEMP_ACTIONS_LIST + channel;

            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            
            Set<String> actionIds = jedis.hkeys(hashKey);
            
            //sorting hash keys
            List list = new ArrayList(actionIds);
            Collections.sort(list, new Comparator<String>() {
                @Override
                public int compare(String s1, String s2) {
                    Long l1 = Long.parseLong(s1);
                    Long l2 = Long.parseLong(s2);
                    return (l1 < l2 ? -1 : (l1 == l2 ? 0 : 1));
                }
            });
            
            List actionsList = new ArrayList();
            for (int i = 0; i < list.size(); i++) {
                actionsList.add(jedis.hget(hashKey, list.get(i).toString()));
            }
            
            return actionsList;

        } catch (Exception ex) {
            logger.log(Level.WARNING, "REDIS: Error while addToTempActionsList()... KEY :: " + channel, ex);
            throw ex;
        } finally {
            if (jedis != null) {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }

    public static List<JSONObjectWrapper> loadActionsList(String channel, int startId, int endId) throws Exception {

        Jedis jedis = null;

        try {
            String hashKey = ACTIONS_LIST + channel;

            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);

            Set<String> actionIds = jedis.hkeys(hashKey);

            List<JSONObjectWrapper> actionsList = new ArrayList<>();

            if(actionIds.size() > 0)
            {
//            sorting hash keys
                if(endId == -1 || startId <= 0)
                {
                    List list = new ArrayList(actionIds);
                    Collections.sort(list, (String s1, String s2) ->
                    {
                        Long l1 = Long.parseLong(s1);
                        Long l2 = Long.parseLong(s2);
                        return (l1 < l2 ? -1 : (l1 == l2 ? 0 : 1));
                    });
                    startId = startId <= 0 ? Integer.parseInt(list.get(0).toString()) : startId;
                    endId = endId == -1 ? Integer.parseInt (list.get(list.size() - 1).toString()) : endId;
                }

                for(int i = startId ; i <= endId; i++)
                {
                    String actionJson = jedis.hget(hashKey, String.valueOf(i));
                    if(actionJson != null)
                    {
                        actionsList.add(new JSONObjectWrapper(actionJson));
                    }
                }

//                String limitStr = endId == -1 ? null : String.valueOf(endId);
//                for (Object ele : list)
//                {
//                    String actionId = ele.toString();
//                    actionsList.add(jedis.hget(hashKey, actionId));
//                    if(actionId.equals(limitStr))
//                    {
//                        break;
//                    }
//                }
            }
//            if(actionIds.size() > 0)
//            {
//                int startActionId = Integer.parseInt(Collections.min(actionIds));
//                int endActionId = limit == -1 ? Integer.parseInt(Collections.max(actionIds)) : limit;
//
//                for(int actionId = startActionId; actionId <= endActionId; actionId++)
//                {
//                    actionsList.add(jedis.hget(hashKey, String.valueOf(actionId)));
//                }
//            }

            return actionsList;

        } catch (Exception ex) {
            logger.log(Level.WARNING, "REDIS: Error while addToActionsList()... KEY :: " + channel, ex);
            throw ex;
        } finally {
            if (jedis != null) {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static void addToList(String key, List actionsList) throws Exception
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            
            for(Object obj : actionsList)
            {
                jedis.rpush(key, obj.toString());
            }
        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while addToList()... KEY :: "+key, ex);
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
       
    }
    
    public static void addToListFromTop(String key, List actionsList) throws Exception
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            
            for(int i=actionsList.size()-1; i>=0; i--)
            {
                jedis.lpush(key, actionsList.get(i).toString());
            }
        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while addToList()... KEY :: "+key, ex);
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
       
    }
    public static void addToHash(String key, List<JSONObjectWrapper> actionsList, int expireInSeconds) throws Exception
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);

            for(int i=actionsList.size()-1; i>=0; i--)
            {
                JSONObjectWrapper actionJson = actionsList.get(i);
                String actionJsonStr = actionsList.get(i).toString();
                jedis.hset(key, actionJson.getString(JSONConstants.ACTION_ID), actionJsonStr);
            }
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }
        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while addToList()... KEY :: "+key, ex);
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
       
    }
    
//    public static void moveToXActionsList(String channel, JSONObjectWrapper actionObject) throws Exception
//    {
//        Jedis jedis = null;
//        
//        try
//        {
//            String tempActionListKey = TEMP_ACTIONS_LIST + channel;
//            String xActionListKey = EXECUTED_ACTIONS_LIST + channel;
//            
//            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);            
//            
//            long currSize = jedis.llen(xActionListKey);
//            if(currSize == EngineConstants.EXECUTED_ACTIONS_LIST_WARNING_LIMIT) {
//                logger.log(Level.INFO, "Executed actions list size crossed {0} for rid {1}", new Object[]{EngineConstants.EXECUTED_ACTIONS_LIST_WARNING_LIMIT, channel}); //No I18N
//            }
//            
//            jedis.hdel(tempActionListKey, actionObject.getString(JSONConstants.ACTION_ID));
//            
//            jedis.rpush(xActionListKey, actionObject.toString());
//            
//            hset(EXECUTED_ACTIONS_CACHE_LIST + channel, actionObject.getString(JSONConstants.ACTION_ID), actionObject.toString(), UNDO_REDO_LIST_EXPIRE_TIME);
//                        
//        }
//        catch(Exception ex)
//        {
//            logger.log(Level.WARNING, "REDIS: Error while moving to xActionList()... RESOURCE_ID :: " + channel, ex);
//            throw ex;
//        }
//        finally
//        {
//            if(jedis != null)
//            {
//                // Do the disconnect now
//                RedisConnectionPoolHandler.returnRedis(jedis);
//            }
//        }
//       
//    }
//    public static void moveToXActionsList(String channel, List<JSONObjectWrapper> list ) throws Exception
//    {
//        Jedis jedis = null;
//        
//        try
//        {
//            String tempActionListKey = TEMP_ACTIONS_LIST + channel;
//            String xActionListKey = EXECUTED_ACTIONS_LIST + channel;
//            
//            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);            
//            
//            for(int i=0; i<list.size(); i++)
//            {
//                JSONObjectWrapper actionObject = list.get(i);
//                jedis.hdel(tempActionListKey, actionObject.getString(JSONConstants.ACTION_ID));
//            
//                jedis.rpush(xActionListKey, actionObject.toString());
//            }
//                        
//        }
//        catch(Exception ex)
//        {
//            logger.log(Level.WARNING, "REDIS: Error while moving to xActionList()... RESOURCE_ID :: " + channel, ex);
//            throw ex;
//        }
//        finally
//        {
//            if(jedis != null)
//            {
//                // Do the disconnect now
//                RedisConnectionPoolHandler.returnRedis(jedis);
//            }
//        }
//       
//    }
    public static void markAsExecutedAction(String channel, JSONObjectWrapper actionObject) throws Exception
    {
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);

            String actionsListKey = ACTIONS_LIST + channel;
            long currSize = jedis.hlen(actionsListKey);
            if(currSize == EngineConstants.EXECUTED_ACTIONS_LIST_WARNING_LIMIT) {
                logger.log(Level.INFO, "Executed actions list size crossed {0} for rid {1}", new Object[]{EngineConstants.EXECUTED_ACTIONS_LIST_WARNING_LIMIT, channel}); //No I18N
            }

            actionObject.set(JSONConstants.IS_EXECUTED, true);
            jedis.hset(ACTIONS_LIST + channel, actionObject.getString(JSONConstants.ACTION_ID), actionObject.toString());
            hset(EXECUTED_ACTIONS_CACHE_LIST + channel, actionObject.getString(JSONConstants.ACTION_ID), actionObject.toString(), CACHE_LIST_EXPIRE_TIME);

        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while moving to xActionList()... RESOURCE_ID :: " + channel, ex);
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }

    }

    public static void setPropertyInActionsList(String channel, String actionId, String key, String value) throws Exception
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            String hashKey = ACTIONS_LIST + channel;
            String actionObjectStr = jedis.hget(hashKey, actionId);
            if(actionObjectStr == null)
            {
                throw new Exception("Action ID doesn't exist in actions list");
            }
            JSONObjectWrapper actionObject = new JSONObjectWrapper(actionObjectStr);
            actionObject.set(key, value);
            jedis.hset(ACTIONS_LIST + channel, actionId, actionObject.toString());
        }
        catch(Exception ex)
        {
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static void removePropertyInActionsList(String channel, String actionId, String key) throws Exception
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            String hashKey = ACTIONS_LIST + channel;
            String actionObjectStr = jedis.hget(hashKey, actionId);
            if(actionObjectStr == null)
            {
                throw new Exception("Action ID doesn't exist in actions list");
            }
            JSONObjectWrapper actionObject = new JSONObjectWrapper(actionObjectStr);
            actionObject.remove(key);
            jedis.hset(ACTIONS_LIST + channel, actionId, actionObject.toString());
        }
        catch(Exception ex)
        {
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static void moveToActionsList(String resourceKey) throws Exception
    {
        List<String> list = lrange(EXECUTED_ACTIONS_LIST + resourceKey, 0, -1);
        moveToActionsList(resourceKey, list, true);
        
        list = loadTempActionsList(resourceKey);
        moveToActionsList(resourceKey, list, false);
    }
    
    public static void moveToActionsList(String channel, List<String> list, boolean isExecuted ) throws Exception
    {
        if(!list.isEmpty())
        {
            Jedis jedis = null;

            try
            {

                String actionListKey = ACTIONS_LIST + channel;

                jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);            

                if(!isExecuted)
                {
                    long currSize = jedis.hlen(actionListKey);
                    if(currSize <= EngineConstants.EXECUTED_ACTIONS_LIST_WARNING_LIMIT && currSize + list.size() > EngineConstants.EXECUTED_ACTIONS_LIST_WARNING_LIMIT) {
                        logger.log(Level.INFO, "Executed actions list size crossed {0} for rid {1}", new Object[]{EngineConstants.EXECUTED_ACTIONS_LIST_WARNING_LIMIT, channel}); //No I18N
                    }
                }

                for (String actionObjectStr : list)
                {
                    JSONObjectWrapper actionObject = new JSONObjectWrapper(actionObjectStr);
                    String actionId = actionObject.getString(JSONConstants.ACTION_ID);
                    if(isExecuted)
                    {
                        actionObject.set(JSONConstants.IS_EXECUTED, true);
                    }

                    jedis.hset(actionListKey, actionId, actionObject.toString());
                }

                RedisHelper.del(isExecuted ? RedisHelper.EXECUTED_ACTIONS_LIST + channel : RedisHelper.TEMP_ACTIONS_LIST + channel);

            }
            catch(Exception ex)
            {
                logger.log(Level.WARNING, "REDIS: Error while moving to actionList... RESOURCE_ID :: " + channel, ex);
                throw ex;
            }
            finally
            {
                if(jedis != null)
                {
                    // Do the disconnect now
                    RedisConnectionPoolHandler.returnRedis(jedis);
                }
            }
        }
    }
    
    public static void deleteUndoRedoList(String channel, String wmsRawSessionId){
        Jedis jedis = null;
        
        try {
            
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);   
            
            jedis.del(getUndoListKey(channel, wmsRawSessionId), getRedoListKey(channel, wmsRawSessionId));

        } catch (Exception e) {
            logger.log(Level.WARNING, "Error Occurred while deleting undo/redo files: resourceId :: {0} :: rsid :: {1} :: {2}", new Object[]{channel, wmsRawSessionId, e});
        
        }finally{
            
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
//    public static void deleteAllUndoRedoList(String channel){
////        TODO : remove usage of keys command
//        Jedis jedis = null;
//        
//        try {
//            
//            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);   
//            
//            Set<String> listKeys = jedis.keys(UNDO_LIST + channel + ":*");
//            listKeys.addAll(jedis.keys(REDO_LIST + channel + ":*"));
//            
//            Iterator<String> listItr = listKeys.iterator();            
//            while (listItr.hasNext()) {
//                
//                jedis.del(listItr.next());
//            }
//        
//        } catch (Exception e) {
//            logger.log(Level.WARNING, "Error Occurred while deleting all undo/redo files{0}: "+ channel, e);
//        
//        }finally{
//            
//            if(jedis != null)
//            {
//                RedisConnectionPoolHandler.returnRedis(jedis);
//            }
//        }
//        
//    }
    
    public static JSONObjectWrapper getUndoRedoCount(String channel, String wmsRawSessionId){
        Jedis jedis = null;
        JSONObjectWrapper countJson = new JSONObjectWrapper();
        try {
            
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);   
            
            countJson.put("u", jedis.llen(getUndoListKey(channel, wmsRawSessionId)));
            countJson.put("r", jedis.llen(getRedoListKey(channel, wmsRawSessionId)));
            return countJson;
            
        } catch (Exception e) {
            logger.log(Level.WARNING, "Error Occurred while getting undo/redo count {0}: "+ channel, e);
            
            countJson.put("u", 0);
            countJson.put("r", 0);
            return countJson;
        
        }finally{
            
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
       
    public static List updateOrderedAuthorsList(int iteration, String resourceId, String... zuids) {

        Jedis jedis = null;
        boolean modified = false;

        try {

            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            String orderedListKey = RedisHelper.AUTHORS_LIST + resourceId;

            ///////////
            jedis.watch(orderedListKey);

            List<String> orderedList = RedisHelper.lrange(orderedListKey, 0, -1);
            Transaction trans = jedis.multi();

            for (String zuid : zuids) {

                if (!orderedList.contains(zuid)) {

                    modified = true;
                    trans.rpush(orderedListKey, zuid);
                }

            }

            List result = trans.exec();
            
            ///////////     
            
            if (result == null && iteration < 3) {
                iteration++;
                updateOrderedAuthorsList(iteration, resourceId, zuids);
            }
            
            orderedList = RedisHelper.lrange(orderedListKey, 0, -1);
            return modified ? orderedList : null;

        } catch (Exception ex) {
            logger.log(Level.WARNING, "REDIS: Error while updating authors list... :: " + Arrays.toString(zuids), ex);

        } finally {
            if (jedis != null) {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }

        return null;
    }

    
    public static String getUndoListKey(String channel, String wmsRawSessionId){
        
        return UNDO_LIST + channel + ":" + wmsRawSessionId;
    }
    
    public static String getRedoListKey(String channel, String wmsRawSessionId){
        
        return REDO_LIST + channel + ":" + wmsRawSessionId;
    }
    
    public static Long addToSavePrison(String resourceId, double noOfExceptions)
    {
        try
        {
            if(!hexists(RedisHelper.SAVE_PRISONED_WORKBOOKS, resourceId))
            {
                JSONObjectWrapper jObj = new JSONObjectWrapper();
                jObj.put("ts", System.currentTimeMillis());
                jObj.put("exp", noOfExceptions);
                return hset(RedisHelper.SAVE_PRISONED_WORKBOOKS, resourceId, jObj.toString());
            }
            
        }catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while adding workbook to savePrison... RESOURCE_ID :: "+resourceId, ex);
        }
        return 0L;
    }

    public static Long addToParsePrison(String resourceId, String comment, double noOfExceptions)
    {
        try
        {
            if(!hexists(RedisHelper.PARSE_PRISONED_WORKBOOKS, resourceId))
            {
                JSONObjectWrapper jObj = new JSONObjectWrapper();
                jObj.put("ts", System.currentTimeMillis());
                jObj.put("exp", noOfExceptions);
                jObj.put("comment", comment);
                return hset(RedisHelper.PARSE_PRISONED_WORKBOOKS, resourceId, jObj.toString());
            }

        }catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while adding workbook to parse Prison... RESOURCE_ID :: "+resourceId, ex);
        }
        return 0L;
    }
       
    public static String getServerIndexKey(String resourceKey, AccessType accessType)
    {
        
        String serverIndexKeyPrefix = RedisHelper.SERVER_INDEX + EnginePropertyUtil.getSheetPropertyValue("ServerIndexKeyPrefix") + ":";    //NO I18N
                    
        if(accessType == UserProfile.AccessType.PUBLIC_ORG 
            || accessType == UserProfile.AccessType.PUBLIC_EXTERNAL ||
            accessType == UserProfile.AccessType.RANGE_PUBLIC_EXTERNAL 
            || accessType == UserProfile.AccessType.RANGE_PUBLIC_ORG ||
            accessType == UserProfile.AccessType.SHEET_PUBLIC_EXTERNAL
            || accessType == UserProfile.AccessType.SHEET_PUBLIC_ORG) // || Constants.IS_DISASTER_RECOVERY_SERVER)
        {
            serverIndexKeyPrefix = serverIndexKeyPrefix + "pub:";    //NO I18N
        }
        
        return serverIndexKeyPrefix + resourceKey;
        
    }
    
    public static void removeSaveRelatedKeys(String resourceKey)
    {
        try
        {
            logger.log(Level.INFO, "REDIS: removing save related keys... RESOURCE_ID :: {0}",resourceKey);
            zrem(RedisHelper.UNSAVED_WORKBOOKS, resourceKey);
            zrem(RedisHelper.EXCEPTION_COUNTER, resourceKey);
            zrem(RedisHelper.SAVE_SCHEDULER_WORKBOOKS, resourceKey);
            hdel(RedisHelper.SAVE_PRISONED_WORKBOOKS, resourceKey);
            hdel(RedisHelper.SHARE_REMOVE_INFO, resourceKey);
            
        } catch (Exception ex)
        {
            logger.log(Level.INFO, "REDIS: Error in removing save related keys... RESOURCE_ID :: "+resourceKey, ex);
        }
    }
    
//    public static long getLastExecutedActionId(String resourceKey)
//    {
//        try
//        {
//            if(RedisHelper.get(RedisHelper.ACTION_ID + resourceKey, -1) != null)
//            {
//                return Long.parseLong(RedisHelper.get(RedisHelper.ACTION_ID + resourceKey, -1));
//            }
//            
//            if(RedisHelper.hlen(RedisHelper.ACTIONS_LIST+resourceKey) > 0)
//            {
//                Set<String> set = RedisHelper.hkeys(RedisHelper.ACTIONS_LIST+resourceKey);
//                return Integer.parseInt(Collections.max(set, (String s1, String s2) ->
//                {
//                    Long l1 = Long.parseLong(s1);
//                    Long l2 = Long.parseLong(s2);
//                    return (l1 < l2 ? -1 : (l1 == l2 ? 0 : 1));
//                }));
//            }
//
////            if (RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + resourceKey) > 0)  //TODO : to be removed
////            {
////                List<String> list = RedisHelper.lrange(RedisHelper.EXECUTED_ACTIONS_LIST + resourceKey, -1, -1);
////                return new JSONObjectWrapper(list.get(0)).getLong(JSONConstants.ACTION_ID);
////            }                        
//            
//        } catch (Exception e)
//        {
//            return -1;
//        }
//        return -1;
//    }
    
    public static long getLastExecutedActionId(String resourceKey)
    {
        try
        {
            List<JSONObjectWrapper> list = RedisHelper.loadActionsList(resourceKey, -1, -1);
            if(!list.isEmpty())
            {
                for (int i = list.size() - 1; i >= 0; i--)
                {
                    JSONObjectWrapper jObj = list.get(i);
                    if (jObj.has(JSONConstants.IS_EXECUTED) && jObj.getBoolean(JSONConstants.IS_EXECUTED))
                    {
                        return jObj.getInt(JSONConstants.ACTION_ID);
                    }

                }
            }else
            {
            		String jkey = RedisHelper.get(RedisHelper.ACTION_ID + resourceKey, -1);
            		if (jkey!=null) {
            			return Long.parseLong(jkey);
				}
            }
            
        } catch (Exception e)
        {
            return -1;
        }
        
        return -1;	
        
    }
    public static void removeFromActionsList(String resourceKey, long startId, long endId)
    {
        
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            for(long actionId = startId; actionId <= endId; actionId++)
            {
                jedis.hdel(ACTIONS_LIST+resourceKey, String.valueOf(actionId));                
            }
        }
        catch(Exception ex)
        {
            logger.log(Level.INFO, "REDIS: Error in removeFromActionsList... RESOURCE_ID :: "+resourceKey, ex);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
    }

    public static boolean restartMergeJob(String redisFieldStr) throws Exception {
        //may return false if already started by another server
        Jedis jedis = null;
        try {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            String mergeJobStr = jedis.hget(AUTOMATION_MERGE_LIST, redisFieldStr);
            JSONObjectWrapper jObj = JSONObjectWrapper.fromString(mergeJobStr);
            long t1 = System.currentTimeMillis();
            if (System.currentTimeMillis() - jObj.getLong("last_accessed_time") > 10 * 60 * 1000) {
                jObj.put("last_accessed_time", t1);
                jedis.hset(AUTOMATION_MERGE_LIST, redisFieldStr, jObj.toString());
                return true;
            }
            return false;
        } finally {
            if(jedis != null) {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }

//    public static void removeFromActionsList(String resourceKey, long actionId)
//    {
//        long isDeleted = 1;
//        Jedis jedis = null;
//        try
//        {
//            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
//            while(isDeleted != 0)
//            {
//                isDeleted = jedis.hdel(ACTIONS_LIST+resourceKey, String.valueOf(actionId));
//                actionId--;
//            }
//        }
//        catch(Exception ex)
//        {
//            logger.log(Level.INFO, "REDIS: Error in removeFromActionsList... RESOURCE_ID :: "+resourceKey, ex);
//        }
//        
//        finally
//        {
//            if(jedis != null)
//            {
//                RedisConnectionPoolHandler.returnRedis(jedis);
//            }
//        }
//        
//    }
    
    
    
    
//    public static void lpoprpush(String fromKey, String toKey) throws Exception
//    {
//        Jedis jedis = null;
//        
//        try
//        {
//            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
//            jedis.rpush(toKey, jedis.lpop(fromKey));
//        }
//        catch(Exception ex)
//        {
//            logger.log(Level.WARNING, "REDIS: Error while lpoprpush()... FROM_KEY :: "+fromKey+" :: TO_KEY :: "+toKey, ex);
//            throw ex;
//        }
//        finally
//        {
//            if(jedis != null)
//            {
//                // Do the disconnect now
//                RedisConnectionPoolHandler.returnRedis(jedis);
//            }
//        }
//    }

    public static void limitXActionsCacheList(String resourceKey)
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            long xActionsCacheListLength = jedis.hlen(EXECUTED_ACTIONS_CACHE_LIST+resourceKey);
            if( xActionsCacheListLength > CACHE_LIST_MAX_LIMIT)
            {

                Set<String> actionIds = jedis.hkeys(EXECUTED_ACTIONS_CACHE_LIST+resourceKey);
                if( xActionsCacheListLength > 1000) {    //To check redis high memory issue
                    logger.log(Level.INFO, "REDIS: Keys in limiting xactions cache list... Count:{0} KEY:{1} actionIds {2}:: ", new Object[]{xActionsCacheListLength, resourceKey, actionIds});
                }
                int maxActionId = actionIds.stream().mapToInt(Integer::parseInt).max().orElse(-1);
                int startActionId = maxActionId - CACHE_LIST_MAX_LIMIT;

                String[] excessKeys = actionIds.stream().filter(str->Integer.valueOf(str)<=startActionId).toArray(String[]::new);
                jedis.hdel(EXECUTED_ACTIONS_CACHE_LIST+resourceKey, excessKeys);

            }

        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error in limiting xactions cache list... KEY :: " + resourceKey, ex);
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static void set(String key, String value, int expireInSeconds) throws Exception
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.set(key, value);
            
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }
            
        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while getting set()... KEY :: "+key, ex);
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
       
    }
    
    public static String get(String key, int expireInSeconds) throws Exception
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            String value = jedis.get(key);
            
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }

            return value;
        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "REDIS: Error while getting get()... KEY :: "+key, ex);
            throw ex;
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
    }
    
    public static void del(String... keys) throws Exception
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.del(keys);
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
    }
    
    
    public static long llen(String key) throws Exception
    {
        
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.llen(key);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static void lpush(String key, String value, int expireInSeconds) throws Exception{
        
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.lpush(key, value);
            
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }

    public static void lpush(String key, List<String> values, int expireInSeconds) throws Exception {
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.lpush(key, values.toArray(new String[values.size()-1]));

            if(expireInSeconds != -1)
            {
                jedis.expire(key, (long) expireInSeconds);
            }
        }

        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
 
    public static void lpushtrim(String key, String value, long maxLength, int expireInSeconds) throws Exception{
        
        Jedis jedis = null;
        
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.lpush(key, value);
            jedis.ltrim(key, 0, maxLength);
            
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }

    public static String lpop(String key, int expireInSeconds) throws Exception{
        
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }
            return jedis.lpop(key);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }

    public static List<String> lpop(String key, int count, int expireInSeconds) throws Exception
    {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }
            return jedis.lpop(key, count);
        }
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }

    public static List<String> lrange(String key, long startIndex, long endIndex) throws Exception{
        
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.lrange(key, startIndex, endIndex);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    //removes elements from the list from start index till the given length
    public static void rtrim(String key, long length) throws Exception
    {
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            long startIndex = length - jedis.llen(key);
            if (startIndex >= 0)
            {
                jedis.del(key);
            } else
            {
                jedis.ltrim(key, startIndex, -1);
            }
        } finally
        {
            if (jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static void hset(String key, String field, String value, int expireInSeconds) throws Exception {
        
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.hset(key, field, value);
            
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static long hlen(String key) throws Exception
    {
        
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.hlen(key);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    public static long ttl(String key) throws Exception{
        
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.ttl(key);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
                
    }
    
    public static void lset(String key, long index, String value) throws Exception{
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.lset(key, index, value);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static long zadd(String key, double score, String member, int expireInSeconds) throws Exception{
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }
            return jedis.zadd(key, score, member);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
        
    public static Double zscore(String key, String member) throws Exception{
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
          
            return jedis.zscore(key, member);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static Double zincrby(String key, double increment, String member) throws Exception{
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.zincrby(key, increment, member);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static List<String> zrangeByScore(String key, double min, double max) throws Exception{
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.zrangeByScore(key, min, max);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static List<String> zrange(String key, long min, long max) throws Exception{
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.zrange(key, min, max);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static List<Tuple> zrangeByScoreWithScores(String key, String min, String max) throws Exception{
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.zrangeByScoreWithScores(key, min, max);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static List<Tuple> zrangeWithScores(String key, long min, long max) throws Exception{
        Jedis jedis = null;

        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.zrangeWithScores(key, min, max);
        }
        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static long zrem(String key, String member) throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.zrem(key, member);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
    }
    public static long zremByScore(String key, double min,double max) throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.zremrangeByScore(key, min,max);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
    }

    public static long zcard(String key) throws Exception {
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.zcard(key);
        }
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    // -1 will not set expiration
    public static void sadd(String key, String member, int expireInSeconds)  throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.sadd(key, member);     
            if(expireInSeconds != -1)
            {
                jedis.expire(key, expireInSeconds);
            }
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
    }
    
    public static boolean sismember(String key, String member)  throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.sismember(key, member);  // member is resourceid .. but it may defer who calls it          
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
    }
    
    public static void srem(String key, String member)  throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.srem(key, member);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
    }
    
    public static Long hincrBy(String key, String field, long value)   throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.hincrBy(key, field, value);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static Long hset(String key, String field, String value)   throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.hset(key, field, value);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static String hget(String key, String field)   throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.hget(key, field);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static Map hgetAll(String key)   throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.hgetAll(key);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static long hdel(String key, String field)   throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.hdel(key, field);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis); 
            }
        }
    }
    
    public static Set<String> hkeys(String key)   throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.hkeys(key);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    public static boolean hexists(String key, String field)   throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            return jedis.hexists(key, field);            
        }        
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    public static void rpush(String key, String value)   throws Exception{
        Jedis jedis = null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            jedis.rpush(key, value);            
        } 
        catch(Exception e){
                logger.info("Exception "+e);
        }
        finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    public static String  rename(String oldkey,String newkey){
    	Jedis jedis=null;
    	try{
    		jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
    		String result = jedis.rename(oldkey, newkey);
    		return result;
    	
    	} catch(Exception e){
    		logger.info("Exception "+e);
        	return null;
    	}finally{
    		if(jedis != null)
    		{
    			RedisConnectionPoolHandler.returnRedis(jedis);
    		}
    	}
    }
    
    public static boolean  exists(String key){
    	Jedis jedis=null;
    	try{
    		jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
    		boolean result = jedis.exists(key);
    		return result;
    	
    	} catch(Exception e){
    		logger.info("Exception "+e);
        	return false;
    	}finally{
    		if(jedis != null)
    		{
    			RedisConnectionPoolHandler.returnRedis(jedis);
    		}
    	}
    }
        public static String type(String key){
        Jedis jedis=null;
        try{
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            String result = jedis.type(key);
            return result;
        } catch(Exception e){
            logger.info("Exception "+e);
            return null;
        }finally{
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    public static Set<String> smembers(String key){
        Jedis jedis=null;
        try{
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);
            Set<String> result = jedis.smembers(key);
            return result;
        } catch(Exception e){
            logger.info("Exception "+e);
            return null;
        }finally{
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }
    
    public static void expire(String key, int expireInSeconds)
    {
        Jedis jedis=null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);        
            jedis.expire(key, expireInSeconds);
            
        } catch(Exception e)
        {
            logger.log(Level.WARNING, "REDIS: Error while getting set()... KEY :: "+key, e);
        }finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }

    public static long memoryUsage(String key, int count)
    {
        Jedis jedis=null;
        try
        {
            jedis = RedisConnectionPoolHandler.getRedisConnection(REDIS_POOL_NAME);

            ScanParams scanParams = new ScanParams().count(count).match(key);
            String cursor = ScanParams.SCAN_POINTER_START;

            long totalMem = 0;
            do {
                ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                List<String> keys = scanResult.getResult();

                for(String result :scanResult.getResult()) {
                    totalMem = totalMem + jedis.memoryUsage(result);
                }
                cursor = scanResult.getCursor();
            } while (!cursor.equals(ScanParams.SCAN_POINTER_START));
            return totalMem;

        } catch(Exception e)
        {
            logger.log(Level.WARNING, "REDIS: Error while getting memoryUsage()... KEY :: "+key, e);
            return 0;
        }finally
        {
            if(jedis != null)
            {
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
    }

    public static void set_importrange_enabled(boolean value) throws Exception {
        RedisHelper.set(RedisHelper.IMPORTRANGE_ENABLED, value ? "1" : "0", -1);
    }

    public static boolean is_importrange_enabled() {
        final String s;
        try {
            s = RedisHelper.get(RedisHelper.IMPORTRANGE_ENABLED, -1);
        } catch (Exception e) {
            logger.log(Level.OFF, "", e);
            return true;
        }
        if (s == null) {
            return true;
        } else {
            return s.equals("1");
        }
    }
    
//    public static void updateUserSettings(Long zuid, JSONObjectWrapper userSettings)
//    {
//        try
//        {
//            set(zuid+ZUID_VS_USER_SETTINGS, userSettings.toString(), 45 * 24 * 60 * 60);
//        }
//        catch(Exception e)
//        {
//            logger.log(Level.WARNING, "REDIS: Error while updating user settings for user {0}", new Object[]{zuid});
//        }
//    }
//
//    public static JSONObjectWrapper readUserSettings(Long zuid)
//    {
//        try
//        {
//            String userSettings = get(zuid + ZUID_VS_USER_SETTINGS, -1);
//            return new JSONObjectWrapper(userSettings);
//        }
//        catch(Exception e)
//        {
//            logger.log(Level.WARNING, "REDIS: Error while reading user settings for user {0}", new Object[]{zuid});
//            return null;
//        }
//    }

    public static boolean isPartiallySaved(String resourceKey) {
        try {
            return sismember(PARTIALLY_SAVED, resourceKey);
        } catch (Exception e) {
            logger.log(Level.OFF, "", e);
        }
        return false;
    }

    public static void setPartiallySaved(String resourceKey) {
        if(!Boolean.valueOf(EnginePropertyUtil.getEnginePropertyValue("IS_PARSE_ZSFRAGMENTS_IN_PARALLEL"))) {
            return;
        }
        try {
            sadd(PARTIALLY_SAVED, resourceKey, -1);
        } catch (Exception e) {
            logger.log(Level.OFF, "", e);
        }
    }

    public static void removePartiallySaved(String resourceKey) {
        if(!Boolean.valueOf(EnginePropertyUtil.getEnginePropertyValue("IS_PARSE_ZSFRAGMENTS_IN_PARALLEL"))) {
            return;
        }
        try {
            srem(PARTIALLY_SAVED, resourceKey);
        } catch (Exception e) {
            logger.log(Level.OFF, "", e);
        }
    }

    /**************************** CUSTOM COLORS ******************************/

    public static List<String> getCustomColors(String rid) throws Exception
    {
        return lrange(CUSTOM_COLORS + rid, 0, 9);
    }

    public static void setCustomColors(String rid, String customColor) throws Exception
    {
        lpushtrim(CUSTOM_COLORS + rid, customColor, EngineConstants.CUSTOM_COLORS_LIMIT, -1);
    }

    public static void deleteCustomColors(String rid) throws Exception
    {
        lpop(CUSTOM_COLORS + rid, EngineConstants.CUSTOM_COLORS_LIMIT, -1);
    }

    /**************************** CUSTOM COLORS ******************************/

    public static void updateFocusInfo(String resourceKey, String zuid, JSONObjectWrapper focusInfoJSON) throws Exception
    {
        RedisHelper.hset(RedisHelper.FOCUS_INFO + resourceKey, zuid, focusInfoJSON.toString(), RedisHelper.UNDO_REDO_LIST_EXPIRE_TIME);
        RedisHelper.set(RedisHelper.FOCUS_INFO_ZUID + resourceKey, zuid, RedisHelper.UNDO_REDO_LIST_EXPIRE_TIME);
    }
}


/////////// PUB SUB IMPL //////////////////////

/*
 * MAIN CLASS
 * 
 * 
 * private static boolean isClientSubscribed = false;
    
    private static RedisListener redisListener = new RedisListener();
    
    
    private static void initRedisSubscription()
    {
        
        if(!isClientSubscribed)
        {
            isClientSubscribed = true;
            new Thread(new Runnable()
            {
                public void run()
                {
                    Jedis jedis = null;
                    try{                        
                        
                        jedis = RedisConnectionPoolHandler.getRedisConnection();
                        logger.log(Level.INFO, "before subscribed to the DUMMY channel");
                        jedis.subscribe(redisListener, "dummy");
                        logger.log(Level.INFO, "after subscribed to the DUMMY channel");
                    
                    }catch(Exception ex)
                    {
                        logger.log(Level.WARNING, "Unable to subsribe to Redis server... ",ex);
                        logger.log(Level.WARNING, "Unable to subsribe to Redis server so halting the server... ");
                        
                        if(jedis != null)
                        {
                            RedisConnectionPoolHandler.returnRedis(jedis);
                        }
                        System.exit(1);
                    }
                    
                }
            }).start();
            
        }
        
    }
    
    static Map map = new HashMap();
    public static void subscribeChannel(String channel)
    {
        if(!isClientSubscribed)
        {
            // Only once to make this server subscribe to server
            initRedisSubscription();
            try
            {
                Thread.sleep(1000);
            }
            catch(Exception ex)
            {
            }
            
        }
        
        
        
        if(!map.containsKey(channel))
        {
            logger.log(Level.INFO, "11before subscribed to the channel >>> "+channel);
        
            redisListener.subscribe(channel);
            
            logger.log(Level.INFO, "11after subscribed to the channel >>> "+channel);
            map.put(channel, channel);
        }
        
    }
    
    public static void unsubscribeChannel(String channel)
    {
        redisListener.unsubscribe(channel);
    }
    
    
    public static long publishMessage(String channel, JSONObjectWrapper actionObject)
    {
        long actionId = -1;
        Jedis jedis = null;
        try
        {
            String actionIdKey = channel+"_aid";
            
            jedis = RedisConnectionPoolHandler.getRedisConnection();
            
            //jedis.watch(actionIdKey);
            actionId = jedis.incr(actionIdKey);
            //jedis.unwatch();
            
            logger.log(Level.INFO, channel+" :: ActionId from Redis :: "+actionId);
            ////////
            actionObject.set(JSONConstants.ACTION_ID, actionId);
            String msg = actionObject.toString();
            //logger.log(Level.WARNING, " Action 111>>>>>>>>>>  "+msg);
            jedis.rpush(channel+"_alist", msg);
            //jedis.lpush(channel+"_alist", "nilam");
            //////
            //logger.log(Level.WARNING, " MESSAGES IN LIST :: >>>>>>>>>>  "+jedis.lrange(channel+"_alist", 0, -1));
            jedis.publish(channel, msg);
            
        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "Error while publishing the message... RESID :: {0} :: MSG :: {1}",new Object[]{channel, actionObject});
            logger.log(Level.WARNING, "Error while publishing the message... ",ex);
        }
        finally
        {
            if(jedis != null)
            {
                // Do the disconnect now
                RedisConnectionPoolHandler.returnRedis(jedis);
            }
        }
        
        return actionId;
    }

 * 
 */

/*
 * LISTENER CLASS
 * 
 * public void onMessage(String channel, String message)
    {
        
        long s = System.currentTimeMillis();
         
        //throw new UnsupportedOperationException("Not supported yet.");
        //logger.log(Level.INFO, "Message recieved for processing0000 >>>>>>>>>>>>>>>>>>");
        // Get the WorkbookContainer
        WorkbookContainer contianer = EngineFactory.getWorkbookContainer(channel, UserProfile.AccessType.AUTH, false);
        //logger.log(Level.INFO, "Message recieved for processing1111 >>>>>>>>>>>>>>>>>>");
        try{
            contianer.addActionToQueue1(new JSONObjectWrapper(message));
        }
        catch(Exception ex)
        {
            logger.log(Level.WARNING, "Unable to send the message for processing RID :: {0} :: MSG :: {1} ", new Object[]{channel, message});
            logger.log(Level.WARNING, "Unable to send the message for processing ... ",ex);
                
        }
        //logger.log(Level.INFO, "Message recieved for processing2222 >>>>>>>>>>>>>>>>>>");
        logger.log(Level.INFO, "Time taken in onMessage() >>> "+(System.currentTimeMillis()-s));
    }

    @Override
    public void onPMessage(String string, String string1, String string2)
    {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void onSubscribe(String string, int i)
    {
        //throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void onUnsubscribe(String string, int i)
    {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void onPUnsubscribe(String string, int i)
    {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void onPSubscribe(String string, int i)
    {
        throw new UnsupportedOperationException("Not supported yet.");
    }
 * 
 */
