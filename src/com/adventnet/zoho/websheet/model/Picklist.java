//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.Style;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.RangeUtil;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.zsparser.Names;
import com.adventnet.zoho.websheet.model.zsparser.XmlName;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Table;

import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class Picklist {
    private int id;
    private Map<Integer, PicklistItem> itemList;
    private PicklistItem defaultItem;
    private Set<Integer> deletedIds = new HashSet<>();
    private final PicklistValue cachedDefaultValue = new PicklistValue(this,-1);

    private List<DataRange> sourceRanges = new ArrayList<>();
    private boolean isRangePicklist;
    private boolean showAsBubble;

    private List<PicklistStyle> autoStyleList = new ArrayList<>();
    private boolean sort;
    private boolean allowMultiSelect;
    private Map<Pair<Value, String>, Pair<PicklistItem, Integer>> valueMap = new HashMap<>();
    private int colorIndex = 0;

    private StackDirection stackDirection;
    private DropdownStyle showDropdownIcon;

    public enum StackDirection {
        HORIZONTAL("hrz","1"), //No I18N
        VERTICAL("vrt","2"); //No I18N

        private final String json;
        private final String xml;

        StackDirection(String json, String xml) {
            this.json = json;
            this.xml = xml;
        }

        public String getForJson() {
            return this.json;
        }

        public String getForXml() {
            return this.xml;
        }

        public static StackDirection fromJson(String num) {
            if(VERTICAL.json.equals(num)) {
                return VERTICAL;
            }
            else {
                return HORIZONTAL;
            }
        }

        public static StackDirection fromXML(String num) {
            if(VERTICAL.xml.equals(num)) {
                return VERTICAL;
            }
            else {
                return HORIZONTAL;
            }
        }
    }

    public enum DropdownStyle {
        ALWAYS("Always", "1"), //No I18N
        FOCUS("Focus","2"), //No I18N
        NEVER("Never", "3"); //No I18N

        private final String json;
        private final String xml;

        DropdownStyle(String json, String xml) {
            this.json = json;
            this.xml = xml;
        }

        public String getForJson() {
            return this.json;
        }

        public static DropdownStyle fromJson(String name) {
            if(FOCUS.json.equals(name)) {
                return FOCUS;
            }
            else if(NEVER.json.equals(name)) {
                return NEVER;
            }
            else {
                return ALWAYS;
            }
        }

        public static DropdownStyle fromXml(String num) {
            if("false".equals(num))
            {
                return NEVER; // Handled for older files
            }
            if(FOCUS.xml.equals(num)) {
                return FOCUS;
            }
            else if(NEVER.xml.equals(num)) {
                return NEVER;
            }
            else {
                return ALWAYS;
            }
        }
    }


    /* defaultItem should never be null. If there is no default item, create a dummy default item with id -1 and assign*/
    public Picklist(int id, List<PicklistItem> picklistItemsList, PicklistItem defaultItem, boolean isRangePicklist, boolean sort, boolean showBubble, List<PicklistStyle> picklistStyles, boolean allowMultiSelect, DropdownStyle showDropdownIcon, StackDirection stackDirection) {
        this.id = id;
        this.itemList = new LinkedHashMap<>();

        for(PicklistItem item: picklistItemsList) {
            this.itemList.put(item.getId(), item);
        }

        this.defaultItem = defaultItem;
        this.isRangePicklist = isRangePicklist;
        this.sort = sort;
        this.showAsBubble = showBubble;
        this.showDropdownIcon = showDropdownIcon;
        this.autoStyleList = picklistStyles != null ? picklistStyles : new ArrayList<>();
        this.allowMultiSelect = allowMultiSelect;
        this.stackDirection = stackDirection;
    }

    public Picklist(int id, List<DataRange> picklistSourceRanges, boolean sort, boolean showAsBubble, List<PicklistStyle> autoStyleList, boolean allowMultiSelect, DropdownStyle showDropdownIcon, StackDirection stackDirection) {
        this.id = id;
        this.itemList = new LinkedHashMap<>();
        this.defaultItem = new PicklistItem(-1,Optional.empty(),Value.EMPTY_VALUE,null);
        this.sourceRanges = picklistSourceRanges;
        this.isRangePicklist = true;
        this.sort = sort;
        this.showAsBubble = showAsBubble;
        this.showDropdownIcon = showDropdownIcon;
        this.autoStyleList = autoStyleList == null ? new ArrayList<>() : autoStyleList;
        this.allowMultiSelect = allowMultiSelect;
        this.stackDirection = stackDirection;
    }

    public PicklistValue getDefaultPicklistValue() {
        return this.cachedDefaultValue;
    }

    public void setItemList(Map<Integer, PicklistItem> itemList) {
        this.itemList = itemList;
    }

    public void setDefaultItem(PicklistItem defaultItem) {
        this.defaultItem = defaultItem;
    }

    public ValueI getPicklistValueObj(int itemID) {
        return new PicklistValue(this, itemID); // TODO : Use caching
    }

    public ValueI getPicklistValueObj(List<Integer> itemIDs) {
        return new PicklistValue(this, itemIDs);
    }




    public void deleteItem(int itemID) {
        if(this.itemList.containsKey(itemID)) {
            this.itemList.remove(itemID);
            this.deletedIds.add(itemID);
        }

    }

    public int getNewIDForItem() {
        int id = 0;
        while(true) {
            if(!this.itemList.containsKey(id)) {
                if(!this.deletedIds.contains(id)) {
                    return id;
                }
            }
            id++;
        }
    }

    public PicklistItem getDefaultItem() {
        return this.defaultItem;
    }

    public void addIdsToDeletedIdList(int id) {
        this.deletedIds.add(id);
    }

    public Set<Integer> getDeletedIds() {
        return this.deletedIds;
    }
    
    public Value getActualValue(int id)
    {
        PicklistItem picklistItem = itemList.get(id);

        if(picklistItem == null) {
            return (Value) defaultItem.getActualValue();
        }

        return (Value) picklistItem.getActualValue();
    }


    public int getId() {
        return this.id;
    }

    private void setId(int id) {
        this.id = id;
    }

    public Collection<PicklistItem> getItemList() {
        return this.itemList.values();
    }

    public PicklistItem getItemAtIndex(int index) {
        int i = 0;
        for(Map.Entry<Integer, PicklistItem> entry: this.itemList.entrySet()) {
            if(i == index) {
                return entry.getValue();
            }
            i++;
        }

        return null;
    }

    public PicklistItem getItem(int id) {

        return this.itemList.get(id);
    }

    public Picklist clone() {
        List<PicklistItem> picklistItems = new ArrayList<>(this.itemList.values());

        Picklist newPicklist = new Picklist(this.id,picklistItems,this.defaultItem, this.isRangePicklist, this.sort, this.showAsBubble, this.autoStyleList, this.allowMultiSelect, this.showDropdownIcon, this.stackDirection);
        newPicklist.valueMap = new HashMap<>();
        newPicklist.colorIndex = colorIndex;

        for(Map.Entry<Pair<Value, String>, Pair<PicklistItem, Integer>> entry: this.valueMap.entrySet()) {
            newPicklist.valueMap.put(entry.getKey(), entry.getValue());
        }

        newPicklist.getDeletedIds().addAll(this.deletedIds);

        return newPicklist;
    }

    public Picklist cloneWithNewId(int id) {
        Picklist clonedPicklist = this.clone();
        clonedPicklist.setId(id);

        return clonedPicklist;
    }


    private String getRangeStringForXml(Workbook workbook) {
        String rangeString = "";
        for(Sheet sheet : workbook.getSheetList()) {
            Map<Integer,List<DataRange>> picklistMap = sheet.getPicklistRangeMap();
            List<DataRange> rangeList = picklistMap.get(this.id);
            if(rangeList != null) {
                for (DataRange range : rangeList) {
                    rangeString += range.getCompleteRangeStringForXML(workbook) + ";";
                }
            }
        }
        if(!rangeString.isEmpty()) {
            rangeString = rangeString.substring(0,rangeString.length()-1);
        }
        return rangeString;
    }

    private String getSourceRangeStringForXml(Workbook workbook) {
        String rangeString = "";
        if(sourceRanges == null || sourceRanges.isEmpty()) {
            return null;
        }

        for(DataRange range: sourceRanges) {
            rangeString += range.getCompleteRangeStringForXML(workbook) + ";";
        }
        if(!rangeString.isEmpty()) {
            rangeString = rangeString.substring(0,rangeString.length()-1);
        }

        return rangeString;
    }

    public XmlName[] getAttributes() {
        XmlName[] attr = new XmlName[] {Names.A_TABLE_ID ,Names.A_STARTS_WITH_ID, Names.A_RANGE, Names.A_SHOW_BUBBLE, Names.A_STYLE_ID, Names.A_ALLOW_MULTI_SELECT, Names.A_SHOW_DROPDOWN_ICON, Names.A_STACK_DIRECTION};
        return attr;
    }

    public String[] getValues(Workbook workbook, int styleID) {
        String[] val = new String[] {
                Integer.toString(this.getId()),
                Integer.toString(this.defaultItem.getId()),
                getRangeStringForXml(workbook),
                this.isShowAsBubble() ? "1" : null,
                styleID != -1 ? String.valueOf(styleID) : null,
                this.allowMultiSelect ? "1" : null,
                this.showDropdownIcon == DropdownStyle.ALWAYS ? null : this.showDropdownIcon.xml,
                this.stackDirection == StackDirection.HORIZONTAL ? null : this.stackDirection.xml
        };

        return val;
    }

    public XmlName[] getRangeAttributes() {
        XmlName[] attr = new XmlName[] {Names.A_TABLE_ID, Names.A_RANGE, Names.A_SOURCE_RANGE, Names.A_PICKLIST_SORT, Names.A_SHOW_BUBBLE, Names.A_STYLE_ID, Names.A_ALLOW_MULTI_SELECT, Names.A_SHOW_DROPDOWN_ICON};
        return attr;
    }

    public String[] getRangeValues(Workbook workbook, int styleID) {
        String[] val = new String[] {
                Integer.toString(this.getId()),
                getRangeStringForXml(workbook),
                getSourceRangeStringForXml(workbook),
                this.sort ? "1" : null,
                this.isShowAsBubble() ? "1" : null,
                styleID != -1 ? String.valueOf(styleID) : null,
                this.allowMultiSelect ? "1" : null,
                this.showDropdownIcon == DropdownStyle.ALWAYS ? null : this.showDropdownIcon.xml
        };
        return val;
    }


    public boolean itemExists(int itemID) {
        return this.itemList.get(itemID) != null;
    }

    public PicklistItem getEqualItem(String value) {
        if(value == null || value.isEmpty()) {
            return null;
        }

        for(PicklistItem item : this.itemList.values()) {
            if(item.getDisplayValue().isPresent() && item.getDisplayValue().get().equalsIgnoreCase(value)) {
                return item;
            }
        }
        return null;
    }

    /* To be used from JsonTableParser, to get the PicklistItem ID, given the valueID */
    public int getEqualItemID(String valueID) {
        for(PicklistItem item: this.itemList.values()) {
            if(item.getActualValue() instanceof ValueWithID) {
                if(((ValueWithID)item.getActualValue()).getID().equals(valueID)) {
                    return item.getId();
                }
            }
        }

        return -1;
    }

    public Map<PicklistStyle, JSONObjectWrapper> getPicklistStyleMap(ZSTheme theme) {
        Map<PicklistStyle,JSONObjectWrapper> styleMap = new HashMap<>();

        int count = 0;

        Collection<PicklistItem> itemList = this.getItemList();
        for(PicklistItem item : itemList) {
            if(item.hasStyle()) {
                PicklistStyle style = item.getStyle();
                JSONObjectWrapper styleJson = styleMap.get(style);
                if(styleJson == null) {
                    styleJson = style.getJSON(theme);
                    styleMap.put(style,styleJson);
                }
            }
        }

        return styleMap;
    }

    public boolean isRangePicklist() {
        return this.isRangePicklist;
    }

    public boolean isSort() {
        return this.sort;
    }

    public void setSort(boolean sort) {
        this.sort = sort;
    }

    public boolean isShowAsBubble() {
        return showAsBubble;
    }

    public boolean isAllowMultiSelect() {
        return this.allowMultiSelect;
    }

    public void setShowAsBubble(boolean showAsBubble) {
        this.showAsBubble = showAsBubble;
    }

    public void setAllowMultiSelect(boolean multiSelect) {
        this.allowMultiSelect = multiSelect;
    }

    public void setShowDropdownIcon(DropdownStyle showDropdownIcon) {
        this.showDropdownIcon = showDropdownIcon;
    }

    public void setStackDirection(StackDirection stackDirection) {
        this.stackDirection = stackDirection;
    }

    public DropdownStyle showDropdownIcon() {
        return this.showDropdownIcon;
    }

    public StackDirection getStackDirection() {
        return this.stackDirection;
    }

    public void setAutoStyleList(List<PicklistStyle> styleList) {
        this.autoStyleList = styleList;
    }

    public List<PicklistStyle> getAutoStyleList() {
        return this.autoStyleList;
    }

    public JSONArrayWrapper getAutoStylesJson(ZSTheme theme) {
        JSONArrayWrapper jsonArray = new JSONArrayWrapper();
        for(PicklistStyle style: this.autoStyleList) {
            jsonArray.put(style.getJSON(theme));
        }

        return jsonArray;
    }

    public List<DataRange> getSourceRanges() {
        return this.sourceRanges;
    }

    public void setSourceRanges(List<DataRange> sourceRanges) {
        this.sourceRanges = sourceRanges;
    }

    public void removeSourceRange(DataRange rangeToRemove) {
        this.sourceRanges = RangeUtil.splitRanges(this.sourceRanges,rangeToRemove);
    }

    public void addSourceRange(DataRange range) {
        RangeUtil.mergeAndAddDataRangeToList(range, this.sourceRanges);
    }

    public boolean isSingleSheetSource(Sheet sheet) {
        if(this.isRangePicklist()) {
            for(DataRange range : this.sourceRanges) {
                if(!sheet.getAssociatedName().equals(range.getAssociatedSheetName())) {
                    return false;
                }
            }
            return true;
        }

        return false;
    }

    public void setIsPicklistRange(boolean isPicklistRange) {
        this.isRangePicklist = isPicklistRange;
        if(!this.isRangePicklist) {
            this.sourceRanges = new ArrayList<>();
        }
    }

    public Integer onSrcCellModify(Workbook workbook, Value oldValue, String oldContent, Value newValue, String newContent, Integer retainedItemID) {
        Integer oldItemID;
        boolean oldValueExistsInMap = false;
        Pair<Value, String> oldValuePair = new Pair<>(oldValue, oldContent);
        Pair<Value, String> newValuePair = new Pair<>(newValue, newContent);

        if(oldValue != null && !oldValue.equals(Value.EMPTY_VALUE) && valueMap.containsKey(oldValuePair)) {
            Pair<PicklistItem, Integer> pair = valueMap.get(oldValuePair);

            if(pair.getValue() <= 1) {
                this.deleteItem(pair.getKey().getId());
                valueMap.remove(oldValuePair);
            }
            else {
                valueMap.put(oldValuePair, new Pair<>(pair.getKey(), pair.getValue()-1));
                oldValueExistsInMap = true;
            }
            oldItemID = pair.getKey().getId();
        }
        else {
            oldItemID = retainedItemID;
        }

        /* Unique Values in list greater than the limit. */
        if(newValue.getType() != Cell.Type.UNDEFINED && !valueMap.containsKey(newValuePair) && valueMap.size() >= EngineConstants.PICKLIST_MAX_NUM_OF_ITEMS) {
            return null;
        }

        /* User deleted the cell value first in order to enter a new value.
         * In this case, the PicklistValues referring to this Cell should remain intact. */
        if(newValue.getType() == Cell.Type.UNDEFINED && oldItemID != null) {
            return oldValueExistsInMap ? null: oldItemID;
        }

        Integer newItemID = null;
        if(newValue.getType() != Cell.Type.UNDEFINED) {
            if (valueMap.containsKey(newValuePair)) {
                Pair<PicklistItem, Integer> pair = valueMap.get(newValuePair);
                valueMap.put(newValuePair, new Pair<>(pair.getKey(), pair.getValue() + 1));
                newItemID = pair.getKey().getId();
                if (oldItemID != null && !oldValueExistsInMap) {
                    changePicklistValues(workbook, oldItemID, newItemID);
                }
            } else {
                int itemID = oldItemID == null ? this.getNewIDForItem() : oldItemID;
                if (oldValueExistsInMap) {
                    itemID = this.getNewIDForItem();
                }
                PicklistItem item = new PicklistItem(itemID, Optional.ofNullable(newContent), newValue, getAutoColor());
                this.itemList.put(item.getId(),item);
                valueMap.put(newValuePair, new Pair<>(item, 1));
                newItemID = item.getId();
            }
        }

        return newItemID;
    }

    public PicklistStyle getAutoColor() {
        if(this.autoStyleList.isEmpty()) {
            return null;
        }
        return this.autoStyleList.get(colorIndex++ % this.autoStyleList.size());
    }


    public void changePicklistValues(Workbook workbook, Integer oldID, Integer newID) {
        if(oldID == null || newID == null) {
            return;
        }
        for(Sheet sheet : workbook.getSheetList()) {
            List<DataRange> rangeList = sheet.getPicklistRangeMap().get(this.getId());
            if(rangeList != null) {
                for(DataRange range: rangeList) {
                    RangeIterator iter = new RangeIterator(workbook.getSheetByAssociatedName(range.getAssociatedSheetName()), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, true, false, true);
                    while(iter.hasNext()) {
                        ReadOnlyCell rCell = iter.next();
                        if(rCell != null && rCell.getCell() != null) {
                            Cell cell = rCell.getCell();
                            if(cell.getValue() instanceof PicklistValue) {
                                PicklistValue value = (PicklistValue) cell.getValue();

                                boolean needsNew = false;
                                for(int val: value.getItemIDs()) {
                                    if(val == oldID) {
                                        needsNew = true;
                                        break;
                                    }
                                }
                                if(needsNew) {
                                    List<Integer> newIdList = new ArrayList<>();
                                    for(int val: value.getItemIDs()) {
                                        if(val == oldID) {
                                            val = newID;
                                        }
                                        newIdList.add(val);
                                    }
                                    ((CellImpl)cell).setValueFromParser(new PicklistValue(this, newIdList));
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void addItem(Value value, String content, Integer itemID) {
        Pair<Value, String> valuePair = new Pair<>(value, content);
        if(valueMap.containsKey(valuePair)) {
            Pair<PicklistItem, Integer> pair = valueMap.get(valuePair);
            valueMap.put(valuePair, new Pair<>(pair.getKey(), pair.getValue()+1));
        }
        else {
            PicklistItem item = new PicklistItem(itemID, Optional.ofNullable(content), value, getAutoColor());
            valueMap.put(valuePair, new Pair<>(item, 1));
            this.itemList.put(item.getId(),item);
        }
    }

    public void getMergedStyleMap(Sheet sheet, DataRange dataRange, Table<Integer, Integer, String> styleTable) {
        BiMap<String, CellStyle> tempCellStyles = HashBiMap.create();
        RangeIterator iterator = new RangeIterator(sheet, dataRange.getStartRowIndex(), dataRange.getStartColIndex(), dataRange.getEndRowIndex(), dataRange.getEndColIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT,
                false, true, false, true, true, true);

        while(iterator.hasNext()) {
            ReadOnlyCell rCell = iterator.next();
            Cell cell;
            if(rCell != null && (cell = rCell.getCell()) != null) {
                Value value = cell.getValue();
                String cellStyleName = cell.getStyleName();
                CellStyle cellStyle;
                if(cellStyleName != null) {
                    cellStyle = sheet.getWorkbook().getCellStyle(cellStyleName);
                }
                else {
                    ColumnHeader colHeader = sheet.getColumnHeader(cell.getColumnIndex());
                    cellStyle = colHeader.getCellStyle();
                }

                if(value instanceof PicklistValue) {
                    PicklistValue picklistValue = (PicklistValue) value;
                    PicklistItem item = picklistValue.getPicklistItems().get(0);
                    PicklistStyle style = item.getStyle();
                    if(style != null) {
                        ZSColor bgColor = style.getBgZSColor();
                        ZSColor textColor = style.getTextZSColor();

                        CellStyle newCellStyle = new CellStyle();
                        if(bgColor != null) {
                            newCellStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getColor(bgColor.getHexColorToWrite(), bgColor.getThemeColorToWrite(), bgColor.getColorTintToWrite()));
                        }
                        if(textColor != null) {
                            newCellStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getColor(textColor.getHexColorToWrite(), textColor.getThemeColorToWrite(), textColor.getColorTintToWrite()));
                        }

                        if(cellStyle != null) {
                            CellStyle tempCellStyle = new CellStyle();
                            Style.merge(cellStyle, tempCellStyle);
                            Style.merge(newCellStyle, tempCellStyle);
                            newCellStyle = tempCellStyle;
                        }

                        String newStyleName = tempCellStyles.inverse().get(newCellStyle);
                        if(newStyleName == null) {
                            boolean equals = false;
                            CellStyle mergedStyle = newCellStyle;
                            for(Map.Entry<String,CellStyle> entry : sheet.getWorkbook().getCellStyleMap().entrySet()) {
                                if(entry.getValue().equals(newCellStyle)) {
                                    equals = true;
                                    mergedStyle = entry.getValue();
                                    break;
                                }
                            }
                            if(!equals) {
                                mergedStyle = sheet.getWorkbook().checkAndAddCellStyle(newCellStyle);
                            }
                            tempCellStyles.put(mergedStyle.getStyleName(), mergedStyle);
                            newStyleName = mergedStyle.getStyleName();
                        }
                        styleTable.put(rCell.getRowIndex(), rCell.getColIndex(), newStyleName);

                    }
                }
            }
        }
    }


    public static class Pair<K,V> {
        K key;
        V value;

        Pair(K key, V value) {
            this.key = key;
            this.value = value;
        }

        public K getKey() {
            return this.key;
        }

        public V getValue() {
            return this.value;
        }

        @Override
        public int hashCode() {
            return (key == null ? 0 : key.hashCode()) * 13 + (value == null ? 0 : value.hashCode());
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o instanceof Pair) {
                Pair pair = (Pair) o;
                if (key != null ? !key.equals(pair.key) : pair.key != null) {
                    return false;
                }
                if (value != null ? !value.equals(pair.value) : pair.value != null) {
                    return false;
                }
                return true;
            }
            return false;
        }
    }
}
