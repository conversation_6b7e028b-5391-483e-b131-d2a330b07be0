//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.zschart;

import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXParserUtility;

/**
 *
 * <AUTHOR>
 */
public class Wall {
    private String styleName;
    
    private float width;
    
    private ChartStyle chartStyle;

    public ChartStyle getChartStyleFromParser() {
        if(chartStyle == null) {
            chartStyle = new ChartStyle();
        }
        return chartStyle;
    }
    
    public ChartStyle getChartStyle() {
        return chartStyle;
    }

    public void setChartStyle(ChartStyle chartStyle) {
        this.chartStyle = chartStyle;
    }
    
    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public float getWidth() {
        return width;
    }

    public void setWidth(float width) {
        this.width = width;
    }
    
    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        stringBuilder.append(XLSXParserUtility.toString("chartStyle",chartStyle));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("styleName",styleName));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("width",width));//No I18N
        stringBuilder.append("}");
        return stringBuilder.toString();
    }
}
