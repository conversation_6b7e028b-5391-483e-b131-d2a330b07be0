//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.zschart;

import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXParserUtility;

/**
 *
 * <AUTHOR>
 */

public class Grid {
    
    private CLASS class1 = CLASS.MAJOR;
    private String styleName;
    
    private ChartStyle chartStyle;

    
    
    public static enum CLASS {
        MAJOR, MINOR
    }
    
    public ChartStyle getChartStyleFromParser() {
        if(chartStyle == null) {
            chartStyle = new ChartStyle();
        }
        return chartStyle;
    }
    
    public ChartStyle getChartStyle() {
        return chartStyle;
    }

    public void setChartStyle(ChartStyle chartStyle) {
        this.chartStyle = chartStyle;
    }
    
    public CLASS getClass_() {
        return class1;
    }

    public void setClass_(CLASS class1) {
        this.class1 = class1;
    }

    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }
    
    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        stringBuilder.append(XLSXParserUtility.toString("class1", class1));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("chartStyle", chartStyle));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("styleName", styleName));//No I18N
        stringBuilder.append("}");
        return stringBuilder.toString();
    }
}
