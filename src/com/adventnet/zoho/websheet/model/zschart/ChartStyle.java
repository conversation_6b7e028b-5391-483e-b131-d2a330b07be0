//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.zschart;

import com.adventnet.zoho.websheet.model.style.GraphicStyle;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXParserUtility;

/**
 *
 * <AUTHOR>
 */
public class ChartStyle {
    
    private String styleName;
    private String dataStyleName;
    private ChartProperties chartProperties;
    private GraphicStyle    graphicStyle;
    private TextStyle textStyle;
    
    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public ChartProperties getChartPropertiesFromParser() {
        if(chartProperties == null) {
            chartProperties = new ChartProperties();
        }
        return chartProperties;
    }
    
    public ChartProperties getChartProperties() {
        return chartProperties;
    }

    public void setChartProperties(ChartProperties chartProperties) {
        this.chartProperties = chartProperties;
    }

    public GraphicStyle getGraphicStyleFromParser() {
        if(graphicStyle == null) {
            graphicStyle = new GraphicStyle();
        }
        return graphicStyle;
    }
    
    public GraphicStyle getGraphicStyle() {
        return graphicStyle;
    }

    public void setGraphicStyle(GraphicStyle graphicStyle) {
        this.graphicStyle = graphicStyle;
    }

    public TextStyle getTextStyleFromParser() {
        if(textStyle == null) {
            textStyle = new TextStyle();
        }
        return textStyle;
    }
    
    public TextStyle getTextStyle() {
        return textStyle;
    }

    public void setTextStyle(TextStyle textStyle) {
        this.textStyle = textStyle;
    }

    public String getDataStyleName() {
        return dataStyleName;
    }

    public void setDataStyleName(String dataStyleName) {
        this.dataStyleName = dataStyleName;
    }
    
    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        stringBuilder.append(XLSXParserUtility.toString("chartProperties", chartProperties));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("dataStyleName", dataStyleName));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("graphicStyle", graphicStyle));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("styleName", styleName));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("textStyle", textStyle));//No I18N
        stringBuilder.append("}");
        return stringBuilder.toString();
    }
}
