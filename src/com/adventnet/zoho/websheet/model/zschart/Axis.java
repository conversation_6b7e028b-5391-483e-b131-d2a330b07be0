//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.zschart;

import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXParserUtility;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */

public class Axis {
    
    private DIMENSION dimension;
    //markup language don't specify any list of name's but didn't notice any other name then these
    private NAME name = null;
    private String styleName;
    
    private String categories = null;
    private List<Grid> grids;
    private Title title;
    
    private ChartStyle chartStyle;
    
    public static enum DIMENSION {
        X, Y, Z
    }
    
    public static enum NAME {
        PRIMARY_X, PRIMARY_Y, SECONDARY_X, SECONDARY_Y, PRIMARY_Z
    }
    
    public DIMENSION getDimension() {
        return dimension;
    }

    public void setDimension(DIMENSION dimension) {
        this.dimension = dimension;
    }

    public NAME getName() {
        return name;
    }

    public void setName(NAME name) {
        this.name = name;
    }

    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public String getCategories() {
        return categories;
    }

    public void setCategories(String categories) {
        this.categories = categories;
    }
    
    private void setGrids() {
        if(grids == null) {
            grids = new ArrayList<>();
        }
    }
    
    public List<Grid> getGridsFromParser() {
        setGrids();
        return grids;
    }
    
    public Grid getGridFromParser(int index) {
        setGrids();
        for(int i = grids.size(); i <= index; i++) {
            grids.add(null);
        }
        Grid grid = grids.get(index);
        if(grid == null) {
            grid = new Grid();
            grids.set(index, grid);
        }
        return grid;
    }
    
    public List<Grid> getGrids() {
        return grids;
    }
    
    public void setGrids(List<Grid> grid) {
        this.grids = grid;
    }

    public Title getTitleFromParser() {
        if(title == null) {
            title = new Title();
        }
        return title;
    }
    
    public Title getTitle() {
        return title;
    }

    public void setTitle(Title title) {
        this.title = title;
    }
    
    public ChartStyle getChartStyleFromParser() {
        if(chartStyle == null) {
            chartStyle = new ChartStyle();
        }
        return chartStyle;
    }
    
    public ChartStyle getChartStyle() {
        return chartStyle;
    }

    public void setChartStyle(ChartStyle chartStyle) {
        this.chartStyle = chartStyle;
    }
    
    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        stringBuilder.append(XLSXParserUtility.toString("title", title));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("category",categories));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("style-name",styleName));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("name", name));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("dimension", dimension));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("chart-styel",chartStyle));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("grids", grids));//No I18N
        stringBuilder.append("}");
        return stringBuilder.toString();
    }
}
