//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.zschart;

import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXParserUtility;

/**
 *
 * <AUTHOR>
 */

public class DataPoint {
    
    private int repeated;
    private String styleName;
    private String id;
    
    private DataLabel dataLabel;
    
    private ChartStyle chartStyle;

    public ChartStyle getChartStyleFromParser() {
        if(chartStyle == null) {
            chartStyle = new ChartStyle();
        }
        return chartStyle;
    }
    
    public ChartStyle getChartStyle() {
        return chartStyle;
    }

    public void setChartStyle(ChartStyle chartStyle) {
        this.chartStyle = chartStyle;
    }
    
    public int getRepeated() {
        return repeated;
    }

    public void setRepeated(int repeated) {
        this.repeated = repeated;
    }

    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public DataLabel getDataLabelFromParser() {
        if(dataLabel == null) {
            dataLabel = new DataLabel();
        }
        return dataLabel;
    }
    
    public DataLabel getDataLabel() {
        return dataLabel;
    }

    public void setDataLabel(DataLabel dataLabel) {
        this.dataLabel = dataLabel;
    }
    
    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        stringBuilder.append(XLSXParserUtility.toString("chartStyle", chartStyle));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("dataLabel", dataLabel));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("id", id));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("repeated", repeated));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("styleName", styleName));//No I18N
        stringBuilder.append("}");
        return stringBuilder.toString();
    }
}
