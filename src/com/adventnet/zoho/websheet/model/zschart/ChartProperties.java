//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.zschart;

import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXParserUtility;

/**
 *
 * <AUTHOR>
 */
public class ChartProperties {
    
    private int angleOffset;// = "90deg";//circle,ring,polar
    private boolean autoPosition = true;
    private boolean autoSize = true;

//Legend-style******************************************************************
    
    
//End-Legend-style**************************************************************

    
//Axis-style *******************************************************************
    private boolean reverseDirectioin = false;
    private boolean logarithmic;
    private float minimum = 0.0f;
    private float maximum = 0.0f;
    private float intervalMajor = 0.0f;
    private int intervalMinorDivisor = 0;
    
    private AXIS_POSITION axisPosition;
        // The axis line is placed at the given value on the crossing axis.
        //If the crossing axis is an axis displaying categories rather than values,
        //a value of 1 indicates that the axis should be placed at the first category
    private float axisPosition1;
    
    private AXIS_LABEL_POSITION axisLabelPosition = AXIS_LABEL_POSITION.NEAR_AXIS;
    
    private boolean tickMarksMajorInner;
    private boolean tickMarksMajorOuter;
    private boolean tickMarksMinorInner;
    private boolean tickMarksMinorOuter;
    private TICK_MARK_POSITION tickMarkPosition;
    
    private boolean displayLabel = true;
    private LABEL_ARRANGEMENT labelArrangement = null;
    private boolean textOverlap;
    private boolean lineBreak;
    private String rotationAngle;
    private DIRECTION direction;
    
//End Axis-style ***************************************************************

//series-style******************************************************************
    private boolean linkDataStyleToSource;
    private boolean connectBars;//bar
    
//End series-style**************************************************************
    
//data-label-style******************************************************************
    private XLSXDLBls xlsxdlBls = null;
    private LABEL_POSITION labelPosition;
    private LABEL_POSITION labelPositionNegative;
    private SOLID_TYPE soliType;//bar
    private String labelSeparator;
    
//End data-label-style**************************************************************  
    
//regression-curve-style******************************************************************
    private REGRESSION_TYPE regressionType = null;
    //prefix - loext
    private float regressionExtrapolateBackward;
    private float regressionExtrapolateForward;
    private boolean regressionForceIntercept;
    private float regressionInterceptValue;
    private byte regressionMaxDegree = 2;
    private String regressionName;
    private byte regressionPeriod = 2;
//End-regression-curve-style******************************************************************

    private ERROR_CATEGORY errorCategory;
    private boolean errorLowerIndicator;
    private float errorLowerLimit;
    private String errorLowerRange;
    private float errorMargin;
    private float errorPercentage;
    private boolean errorUpperIndicator;
    private float errorUpperLimit;
    private String errorUpperRange;
    
    
    private boolean groupBarsPerAxis;//bar
    private float holeSize;//ring
    private boolean includeHiddenCells = false;
    private INTERPOLATION interpolation = INTERPOLATION.NONE;//line,scatter
    
    private boolean japaneseCandleStick = false; //stock
    
    private int gapWidth;//bar
//deprecated //    private boolean lines;
    
    private boolean meanValue;
    
    private float origin;
    private int overlap;//bar
    private int pieOffset;//circle
    private boolean percentage = false;
    
    
    private boolean scaleText;
// deprecated // series-source
    @Deprecated
    private SERIES_SOURCE seriesSource = SERIES_SOURCE.COLUMNS;
    
    private boolean sortByXValue;//scatter
    
    private int splineOrder;
    private int splineResolution;
    private boolean stacked = false;
    
    private float symbolHeight; //in cm
    private SYMBOL_NAME symbolName;
    private SYMBOL_TYPE symbolType = null;
    private float symbolWidth; //in cm
    
    private TREAT_EMPTY_CELLS treatEmptyCells = TREAT_EMPTY_CELLS.LEAVE_GAP; // default for scatter : ignore
    private boolean vertical = false; // bar (chart:vertical=”true”) and column (chart:vertical=”false”) 
    private boolean visible = true;
    
    //3d
    private boolean threeDimensional = false;
    private boolean rightAngledAxes;
    private boolean deep;
    //3d-end
    
    private String symbolImage;

    public static enum AXIS_LABEL_POSITION {
        NEAR_AXIS,
        NEAR_AXIS_OTHER_SIDE,
        OUTSIDE_END,
        OUTSIDE_START
    }
    public static enum AXIS_POSITION {
        START,
        END
    }
    public static enum ERROR_CATEGORY {
        CELL_RANGE,
        CONSTANT,
        ERROR_MARGIN,
        NONE,
        PERCENTAGE,
        STANDARD_DEVIATION,
        STANDARD_ERROR,
        VARIANCE
    }
    public static enum INTERPOLATION {
        NONE,
        CUBIC_SPLINE,
        B_SPLINE
    }
    public static enum LABEL_ARRANGEMENT {
        SIDE_BY_SIDE,
        STAGGER_EVEN,
        STAGGER_ODD
    }
    public static enum LABEL_POSITION {
        AVOID_OVERLAP,
        BOTTOM,
        BOTTOM_LEFT,
        BOTTOM_RIGHT,
        CENTER,
        INSIDE,
        LEFT,
        NEAR_ORIGIN,
        OUTSIDE,
        RIGHT,
        TOP,
        TOP_LEFT,
        TOP_RIGHT
    }
    public static enum REGRESSION_TYPE {
        EXPONENTIAL,
        LINEAR,
        LOGARITHMIC,
        NONE,
        POWER,
        POLYNOMIAL,
        MOVING_AVERAGE
    }
    public static enum SOLID_TYPE {
        CONE,
        CUBOID,
        CYLINDER,
        PYRAMID
    }
    public static enum SYMBOL_NAME {
        SQUARE,
        DIAMOND,
        ARROW_DOWN,
        ARROW_UP,
        ARROW_RIGHT,
        ARROW_LEFT,
        BOW_TIE,
        HOURGLASS,
        CIRCLE,
        STAR,
        X,
        PLUS,
        ASTERISK,
        HORIZONTAL_BAR,
        VERTICAL_BAR
    }
    public static enum SYMBOL_TYPE {
        NONE,
        AUTOMATIC,
        NAME_SYMBOL,
        IMAGE
    }
    public static enum TICK_MARK_POSITION {
        AT_LABELS,
        AT_AXIS,
        AT_LABELS_AND_AXIS
    }
    public static enum TREAT_EMPTY_CELLS {
        IGNORE,
        LEAVE_GAP,
        USE_ZERO
    }
    public static enum DIRECTION {
        LTR,
        TTB
    }
    public static enum SERIES_SOURCE {
        ROWS,
        COLUMNS
    }
    
    public SERIES_SOURCE getSeriesSource() {
        return seriesSource;
    }

    public void setSeriesSource(SERIES_SOURCE seriesSource) {
        this.seriesSource = seriesSource;
    }
    
    public float getRegressionExtrapolateBackward() {
        return regressionExtrapolateBackward;
    }

    public void setRegressionExtrapolateBackward(float regressionExtrapolateBackward) {
        this.regressionExtrapolateBackward = regressionExtrapolateBackward;
    }

    public float getRegressionExtrapolateForward() {
        return regressionExtrapolateForward;
    }

    public void setRegressionExtrapolateForward(float regressionExtrapolateForward) {
        this.regressionExtrapolateForward = regressionExtrapolateForward;
    }

    public boolean isRegressionForceIntercept() {
        return regressionForceIntercept;
    }

    public void setRegressionForceIntercept(boolean regressionForceIntercept) {
        this.regressionForceIntercept = regressionForceIntercept;
    }

    public float getRegressionInterceptValue() {
        return regressionInterceptValue;
    }

    public void setRegressionInterceptValue(float regressionInterceptValue) {
        this.regressionInterceptValue = regressionInterceptValue;
    }

    public int getRegressionMaxDegree() {
        return regressionMaxDegree;
    }

    public void setRegressionMaxDegree(byte regressionMaxDegree) {
        this.regressionMaxDegree = regressionMaxDegree;
    }

    public String getRegressionName() {
        return regressionName;
    }

    public void setRegressionName(String regressionName) {
        this.regressionName = regressionName;
    }

    public int getRegressionPeriod() {
        return regressionPeriod;
    }

    public void setRegressionPeriod(byte regressionPeriod) {
        this.regressionPeriod = regressionPeriod;
    }
    

    
    public int getAngleOffset() {
        return angleOffset;
    }

    public void setAngleOffset(int angleOffset) {
        this.angleOffset = angleOffset;
    }

    public boolean isAutoPosition() {
        return autoPosition;
    }

    public void setAutoPosition(boolean autoPosition) {
        this.autoPosition = autoPosition;
    }

    public boolean isAutoSize() {
        return autoSize;
    }

    public void setAutoSize(boolean autoSize) {
        this.autoSize = autoSize;
    }

    public boolean isReverseDirectioin() {
        return reverseDirectioin;
    }

    public void setReverseDirectioin(boolean reverseDirectioin) {
        this.reverseDirectioin = reverseDirectioin;
    }

    public boolean isLogarithmic() {
        return logarithmic;
    }

    public void setLogarithmic(boolean logarithmic) {
        this.logarithmic = logarithmic;
    }

    public float getMinimum() {
        return minimum;
    }

    public void setMinimum(float minimum) {
        this.minimum = minimum;
    }

    public float getMaximum() {
        return maximum;
    }

    public void setMaximum(float maximum) {
        this.maximum = maximum;
    }

    public float getIntervalMajor() {
        return intervalMajor;
    }

    public void setIntervalMajor(float intervalMajor) {
        this.intervalMajor = intervalMajor;
    }

    public int getIntervalMinorDivisor() {
        return intervalMinorDivisor;
    }

    public void setIntervalMinorDivisor(int intervalMinorDivisor) {
        this.intervalMinorDivisor = intervalMinorDivisor;
    }

    public AXIS_POSITION getAxisPosition() {
        return axisPosition;
    }

    public void setAxisPosition(AXIS_POSITION axisPosition) {
        this.axisPosition = axisPosition;
    }

    public float getAxisPosition_() {
        return axisPosition1;
    }

    public void setAxisPosition_(float axisPosition1) {
        this.axisPosition1 = axisPosition1;
    }

    public AXIS_LABEL_POSITION getAxisLabelPosition() {
        return axisLabelPosition;
    }

    public void setAxisLabelPosition(AXIS_LABEL_POSITION axisLabelPosition) {
        this.axisLabelPosition = axisLabelPosition;
    }

    public boolean isTickMarksMajorInner() {
        return tickMarksMajorInner;
    }

    public void setTickMarksMajorInner(boolean tickMarksMajorInner) {
        this.tickMarksMajorInner = tickMarksMajorInner;
    }

    public boolean isTickMarksMajorOuter() {
        return tickMarksMajorOuter;
    }

    public void setTickMarksMajorOuter(boolean tickMarksMajorOuter) {
        this.tickMarksMajorOuter = tickMarksMajorOuter;
    }

    public boolean isTickMarksMinorInner() {
        return tickMarksMinorInner;
    }

    public void setTickMarksMinorInner(boolean tickMarksMinorInner) {
        this.tickMarksMinorInner = tickMarksMinorInner;
    }

    public boolean isTickMarksMinorOuter() {
        return tickMarksMinorOuter;
    }

    public void setTickMarksMinorOuter(boolean tickMarksMinorOuter) {
        this.tickMarksMinorOuter = tickMarksMinorOuter;
    }

    public TICK_MARK_POSITION getTickMarkPosition() {
        return tickMarkPosition;
    }

    public void setTickMarkPosition(TICK_MARK_POSITION tickMarkPosition) {
        this.tickMarkPosition = tickMarkPosition;
    }

    public boolean isDisplayLabel() {
        return displayLabel;
    }

    public void setDisplayLabel(boolean displayLabel) {
        this.displayLabel = displayLabel;
    }

    public LABEL_ARRANGEMENT getLabelArrangement() {
        return labelArrangement;
    }

    public void setLabelArrangement(LABEL_ARRANGEMENT labelArrangement) {
        this.labelArrangement = labelArrangement;
    }

    public boolean isTextOverlap() {
        return textOverlap;
    }

    public void setTextOverlap(boolean textOverlap) {
        this.textOverlap = textOverlap;
    }

    public boolean isLineBreak() {
        return lineBreak;
    }

    public void setLineBreak(boolean lineBreak) {
        this.lineBreak = lineBreak;
    }

    public String getRotationAngle() {
        return rotationAngle;
    }

    public void setRotationAngle(String rotationAngle) {
        this.rotationAngle = rotationAngle;
    }

    public DIRECTION getDirection() {
        return direction;
    }

    public void setDirection(DIRECTION direction) {
        this.direction = direction;
    }

    public boolean isLinkDataStyleToSource() {
        return linkDataStyleToSource;
    }

    public void setLinkDataStyleToSource(boolean linkDataStyleToSource) {
        this.linkDataStyleToSource = linkDataStyleToSource;
    }

    public XLSXDLBls getDLBls() {
        if(this.xlsxdlBls == null) {
            this.xlsxdlBls = new XLSXDLBls();
        }
        return this.xlsxdlBls;
    }

    public LABEL_POSITION getLabelPosition() {
        return labelPosition;
    }

    public void setLabelPosition(LABEL_POSITION labelPosition) {
        this.labelPosition = labelPosition;
    }

    public LABEL_POSITION getLabelPositionNegative() {
        return labelPositionNegative;
    }

    public void setLabelPositionNegative(LABEL_POSITION labelPositionNegative) {
        this.labelPositionNegative = labelPositionNegative;
    }

    public SOLID_TYPE getSoliType() {
        return soliType;
    }

    public void setSoliType(SOLID_TYPE soliType) {
        this.soliType = soliType;
    }

    public String getLabelSeparator() {
        return labelSeparator;
    }

    public void setLabelSeparator(String labelSeparator) {
        this.labelSeparator = labelSeparator;
    }

    public boolean isThreeDimensional() {
        return threeDimensional;
    }

    public void setThreeDimensional(boolean threeDimensional) {
        this.threeDimensional = threeDimensional;
    }

    public boolean isConnectBars() {
        return connectBars;
    }

    public void setConnectBars(boolean connectBars) {
        this.connectBars = connectBars;
    }

    public boolean isDeep() {
        return deep;
    }

    public void setDeep(boolean deep) {
        this.deep = deep;
    }

    public ERROR_CATEGORY getErrorCategory() {
        return errorCategory;
    }

    public void setErrorCategory(ERROR_CATEGORY errorCategory) {
        this.errorCategory = errorCategory;
    }

    public boolean isErrorLowerIndicator() {
        return errorLowerIndicator;
    }

    public void setErrorLowerIndicator(boolean errorLowerIndicator) {
        this.errorLowerIndicator = errorLowerIndicator;
    }

    public float getErrorLowerLimit() {
        return errorLowerLimit;
    }

    public void setErrorLowerLimit(float errorLowerLimit) {
        this.errorLowerLimit = errorLowerLimit;
    }

    public String getErrorLowerRange() {
        return errorLowerRange;
    }

    public void setErrorLowerRange(String errorLowerRange) {
        this.errorLowerRange = errorLowerRange;
    }

    public float getErrorMargin() {
        return errorMargin;
    }

    public void setErrorMargin(float errorMargin) {
        this.errorMargin = errorMargin;
    }

    public float getErrorPercentage() {
        return errorPercentage;
    }

    public void setErrorPercentage(float errorPercentage) {
        this.errorPercentage = errorPercentage;
    }

    public boolean isErrorUpperIndicator() {
        return errorUpperIndicator;
    }

    public void setErrorUpperIndicator(boolean errorUpperIndicator) {
        this.errorUpperIndicator = errorUpperIndicator;
    }

    public float getErrorUpperLimit() {
        return errorUpperLimit;
    }

    public void setErrorUpperLimit(float errorUpperLimit) {
        this.errorUpperLimit = errorUpperLimit;
    }

    public String getErrorUpperRange() {
        return errorUpperRange;
    }

    public void setErrorUpperRange(String errorUpperRange) {
        this.errorUpperRange = errorUpperRange;
    }

    public int getGapWidth() {
        return gapWidth;
    }

    public void setGapWidth(int gapWidth) {
        this.gapWidth = gapWidth;
    }

    public boolean isGroupBarsPerAxis() {
        return groupBarsPerAxis;
    }

    public void setGroupBarsPerAxis(boolean groupBarsPerAxis) {
        this.groupBarsPerAxis = groupBarsPerAxis;
    }

    public float getHoleSize() {
        return holeSize;
    }

    public void setHoleSize(float holeSize) {
        this.holeSize = holeSize;
    }

    public boolean isIncludeHiddenCells() {
        return includeHiddenCells;
    }

    public void setIncludeHiddenCells(boolean includeHiddenCells) {
        this.includeHiddenCells = includeHiddenCells;
    }

    public INTERPOLATION getInterpolation() {
        return interpolation;
    }

    public void setInterpolation(INTERPOLATION interpolation) {
        this.interpolation = interpolation;
    }

    public boolean isJapaneseCandleStick() {
        return japaneseCandleStick;
    }

    public void setJapaneseCandleStick(boolean japaneseCandleStick) {
        this.japaneseCandleStick = japaneseCandleStick;
    }

    public boolean isMeanValue() {
        return meanValue;
    }

    public void setMeanValue(boolean meanValue) {
        this.meanValue = meanValue;
    }

    public float getOrigin() {
        return origin;
    }

    public void setOrigin(float origin) {
        this.origin = origin;
    }

    public int getOverlap() {
        return overlap;
    }

    public void setOverlap(int overlap) {
        this.overlap = overlap;
    }

    public int getPieOffset() {
        return pieOffset;
    }

    public void setPieOffset(int pieOffset) {
        this.pieOffset = pieOffset;
    }

    public boolean isPercenRtage() {
        return percentage;
    }

    public void setPercentage(boolean percentage) {
        this.percentage = percentage;
    }

    public REGRESSION_TYPE getRegressionType() {
        return regressionType;
    }

    public void setRegressionType(REGRESSION_TYPE regressionType) {
        this.regressionType = regressionType;
    }

    public boolean isRightAngledAxes() {
        return rightAngledAxes;
    }

    public void setRightAngledAxes(boolean rightAngledAxes) {
        this.rightAngledAxes = rightAngledAxes;
    }

    public boolean isScaleText() {
        return scaleText;
    }

    public void setScaleText(boolean scaleText) {
        this.scaleText = scaleText;
    }

    public boolean isSortByXValue() {
        return sortByXValue;
    }

    public void setSortByXValue(boolean sortByXValue) {
        this.sortByXValue = sortByXValue;
    }

    public int getSplineOrder() {
        return splineOrder;
    }

    public void setSplineOrder(int splineOrder) {
        this.splineOrder = splineOrder;
    }

    public int getSplineResolution() {
        return splineResolution;
    }

    public void setSplineResolution(int splineResolution) {
        this.splineResolution = splineResolution;
    }

    public boolean isStacked() {
        return stacked;
    }

    public void setStacked(boolean stacked) {
        this.stacked = stacked;
    }

    public float getSymbolHeight() {
        return symbolHeight;
    }

    public void setSymbolHeight(float symbolHeight) {
        this.symbolHeight = symbolHeight;
    }

    public SYMBOL_NAME getSymbolName() {
        return symbolName;
    }

    public void setSymbolName(SYMBOL_NAME symbolName) {
        this.symbolName = symbolName;
    }

    public SYMBOL_TYPE getSymbolType() {
        return symbolType;
    }

    public void setSymbolType(SYMBOL_TYPE symbolType) {
        this.symbolType = symbolType;
    }

    public float getSymbolWidth() {
        return symbolWidth;
    }

    public void setSymbolWidth(float symbolWidth) {
        this.symbolWidth = symbolWidth;
    }

    public TREAT_EMPTY_CELLS getTreatEmptyCells() {
        return treatEmptyCells;
    }

    public void setTreatEmptyCells(TREAT_EMPTY_CELLS treatEmptyCells) {
        this.treatEmptyCells = treatEmptyCells;
    }

    public boolean isVertical() {
        return vertical;
    }

    public void setVertical(boolean vertical) {
        this.vertical = vertical;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public String getSymbolImage() {
        return symbolImage;
    }

    public void setSymbolImage(String symbolImage) {
        this.symbolImage = symbolImage;
    }
    
    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        stringBuilder.append(XLSXParserUtility.toString("angleOffset",angleOffset));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("autoPosition ",autoPosition));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("autoSize ",autoSize));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("reverseDirectioin ",reverseDirectioin));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("logarithmic",logarithmic));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("minimum ",minimum));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("maximum ",maximum));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("intervalMajor ",intervalMajor));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("intervalMinorDivisor ",intervalMinorDivisor));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("axisPosition",axisPosition));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("axisPosition1",axisPosition1));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("axisLabelPosition ",axisLabelPosition));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("tickMarksMajorInner",tickMarksMajorInner));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("tickMarksMajorOuter",tickMarksMajorOuter));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("tickMarksMinorInner",tickMarksMinorInner));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("tickMarksMinorOuter",tickMarksMinorOuter));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("tickMarkPosition",tickMarkPosition));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("displayLabel ",displayLabel));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("labelArrangement ",labelArrangement));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("textOverlap",textOverlap));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("lineBreak",lineBreak));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("rotationAngle",rotationAngle));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("direction",direction));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("linkDataStyleToSource",linkDataStyleToSource));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("connectBars",connectBars));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("labelPosition",labelPosition));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("labelPositionNegative",labelPositionNegative));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("soliType",soliType));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("labelSeparator",labelSeparator));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("regressionType ",regressionType));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("regressionExtrapolateBackward",regressionExtrapolateBackward));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("regressionExtrapolateForward",regressionExtrapolateForward));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("regressionForceIntercept",regressionForceIntercept));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("regressionInterceptValue",regressionInterceptValue));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("regressionMaxDegree ",regressionMaxDegree));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("regressionName",regressionName));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("regressionPeriod ",regressionPeriod));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("errorCategory",errorCategory));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("errorLowerIndicator",errorLowerIndicator));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("errorLowerLimit",errorLowerLimit));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("errorLowerRange",errorLowerRange));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("errorMargin",errorMargin));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("errorPercentage",errorPercentage));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("errorUpperIndicator",errorUpperIndicator));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("errorUpperLimit",errorUpperLimit));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("errorUpperRange",errorUpperRange));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("groupBarsPerAxis",groupBarsPerAxis));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("holeSize",holeSize));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("includeHiddenCells ",includeHiddenCells));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("interpolation ",interpolation));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("japaneseCandleStick ",japaneseCandleStick));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("gapWidth",gapWidth));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("meanValue",meanValue));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("origin",origin));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("overlap",overlap));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("pieOffset",pieOffset));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("percentage ",percentage));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("scaleText",scaleText));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("seriesSource ",seriesSource));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("sortByXValue",sortByXValue));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("splineOrder",splineOrder));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("splineResolution",splineResolution));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("stacked ",stacked));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("symbolHeight",symbolHeight));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("symbolName",symbolName));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("symbolType ",symbolType));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("symbolWidth",symbolWidth));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("treatEmptyCells ",treatEmptyCells));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("vertical ",vertical));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("visible ",visible));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("threeDimensional ",threeDimensional));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("rightAngledAxes",rightAngledAxes));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("deep",deep));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("symbolImage",symbolImage));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("dlbls", this.xlsxdlBls));//No I18N
        stringBuilder.append("}");
        return stringBuilder.toString();
    }

    public static class XLSXDLBls {
        private boolean showLegendKey = false;
        private boolean showVal = false;
        private boolean showCatName = false;
        private boolean showPercent = false;
        private boolean showBubbleSize = false;
        private boolean showSeries = false;
        private String separator = null;

        public boolean isShowLegendKey() {
            return showLegendKey;
        }

        public void setShowLegendKey(boolean showLegendKey) {
            this.showLegendKey = showLegendKey;
        }

        public boolean isShowVal() {
            return showVal;
        }

        public void setShowVal(boolean showVal) {
            this.showVal = showVal;
        }

        public boolean isShowCatName() {
            return showCatName;
        }

        public void setShowCatName(boolean showCatName) {
            this.showCatName = showCatName;
        }

        public boolean isShowPercent() {
            return showPercent;
        }

        public void setShowPercent(boolean showPercent) {
            this.showPercent = showPercent;
        }

        public boolean isShowBubbleSize() {
            return showBubbleSize;
        }

        public void setShowBubbleSize(boolean showBubbleSize) {
            this.showBubbleSize = showBubbleSize;
        }

        public boolean isShowSeries() {
            return showSeries;
        }

        public void setShowSeries(boolean showSeries) {
            this.showSeries = showSeries;
        }

        public void setLabelSeparator(String separator) {
            this.separator = separator;
        }

        @Override
        public String toString() {
            return "{" +
                    "showLegendKey=" + showLegendKey +//No I18N
                    ", showVal=" + showVal +//No I18N
                    ", showCatName=" + showCatName +//No I18N
                    ", showPercent=" + showPercent +//No I18N
                    ", showBubbleSize=" + showBubbleSize +//No I18N
                    ", showSeries=" + showSeries +//No I18N
                    ", separator='" + separator + '\'' +//No I18N
                    '}';
        }
    }
}