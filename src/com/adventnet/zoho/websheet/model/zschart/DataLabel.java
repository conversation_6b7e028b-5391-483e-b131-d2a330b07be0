//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.zschart;

import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXParserUtility;

/**
 *
 * <AUTHOR>
 */

public class DataLabel {
    
    private String styleName;
    
    private float x;
    private float y;
    
    private String text;
    
    private ChartStyle chartStyle;

    public ChartStyle getChartStyleFromParser() {
        if(chartStyle == null) {
            chartStyle = new ChartStyle();
        }
        return chartStyle;
    }
    
    public ChartStyle getChartStyle() {
        return chartStyle;
    }

    public void setChartStyle(ChartStyle chartStyle) {
        this.chartStyle = chartStyle;
    }
    
    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
    
    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        stringBuilder.append(XLSXParserUtility.toString("chartStyle",chartStyle));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("styleName",styleName));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("x",x));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("y",y));//No I18N
        stringBuilder.append("}");
        return stringBuilder.toString();
    }
}
