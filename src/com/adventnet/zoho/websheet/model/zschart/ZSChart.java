//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.zschart;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXDrawing;
import com.adventnet.zoho.websheet.model.xlsxaparser_.XLSXParserUtility;

/**
 *
 * <AUTHOR>
 */
public class ZSChart {
    
    private CLASS class1;
    
    private String styleName;
    private float height;
    private float width;
    private String href;
    private String type;
    private String id;
    
    private Title footer;
    private Legend legend;
    private PlotArea plotArea;
    private Title subtitle;
    private Title title;
    
    private ChartStyle chartStyle;

    //not ods-properties
    private Sheet sheet;
    private XLSXDrawing xlsxd;
    
    private String pivotId = null;

    private String combined_Range = "HORIZONTAL";//No I18N

    private boolean isCombinationChart = false;
    private String scatterStyle;//values: lineMarker, smoothMarker
    private String pivotSourceSheetName;

    public void setScatterStyle(String scatterStyle) {
        this.scatterStyle = scatterStyle;
    }

    public void setPivotSourceSheetName(String sourceSheetName) {
        this.pivotSourceSheetName = sourceSheetName;
    }

    public String getPivotSourceSheetName() {
        return pivotSourceSheetName;
    }

    public static enum CLASS {
        AREA, BAR, BUBBLE, CIRCLE, FILLED_RADAR, 
        GANTT, LINE, RADAR, RING, SCATTER, STOCK,
        SURFACE
    }
    
    public CLASS getClass_() {
        return class1;
    }

    public void setClass_(CLASS clas1) {
        this.class1 = clas1;
        
    }

    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public float getHeight() {
        return height;
    }

    public void setHeight(float height) {
        this.height = height;
    }

    public float getWidth() {
        return width;
    }

    public void setWidth(float width) {
        this.width = width;
    }

    public String getHref() {
        return href;
    }

    public void setHref(String href) {
        this.href = href;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Title getFooterFromParser() {
        if(footer == null) {
            footer = new Title();
        }
        return footer;
    }
    
    public Title getFooter() {
        return footer;
    }

    public void setFooter(Title footer) {
        this.footer = footer;
    }

    public Legend getLegendFromParser() {
        if(legend == null) {
            legend = new Legend();
        }
        return legend;
    }
    
    public Legend getLegend() {
        return legend;
    }

    public void setLegend(Legend legend) {
        this.legend = legend;
    }

    public PlotArea getPlotAreaFromParser() {
        if(plotArea == null) {
            plotArea = new PlotArea();
        }
        return plotArea;
    }
    
    public PlotArea getPlotArea() {
        return plotArea;
    }

    public void setPlotArea(PlotArea plotArea) {
        this.plotArea = plotArea;
    }

    public Title getSubtitleFromParser() {
        if(subtitle == null) {
            subtitle = new Title();
        }
        return subtitle;
    }
    
    public Title getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(Title subtitle) {
        this.subtitle = subtitle;
    }

    public Title getTitleFromParser() {
        if(title == null) {
            title = new Title();
        }
        return title;
    }
    
    public Title getTitle() {
        return title;
    }

    public void setTitle(Title title) {
        this.title = title;
    }

    public ChartStyle getChartStyleFromParser() {
        if(chartStyle == null) {
            chartStyle = new ChartStyle();
        }
        return chartStyle;
    }
    
    public ChartStyle getChartStyle() {
        return chartStyle;
    }

    public void setChartStyle(ChartStyle chartStyle) {
        this.chartStyle = chartStyle;
    }
    
    public Sheet getSheet() {
        return sheet;
    }

    public void setSheet(Sheet sheet) {
        this.sheet = sheet;
    }

    public XLSXDrawing getXlsxd() {
        return xlsxd;
    }

    public void setXlsxd(XLSXDrawing xlsxd) {
        this.xlsxd = xlsxd;
    }
    
    
    public String getPivotId() {
        return pivotId;
    }
    
    public void setPivotId(String pivotId) {
        this.pivotId = pivotId;
    }
    
    public String getCombinedRange() {
        return this.combined_Range;
    }
    
    public void setCombinedRange(String cR) {
        this.combined_Range = cR;
    }

    public boolean isCombinationChart() {
        return isCombinationChart;
    }

    public void setCombinationChart(boolean combinationChart) {
        isCombinationChart = combinationChart;
    }

    @Override
    public String toString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        stringBuilder.append(XLSXParserUtility.toString("class1",class1));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("pivotId",pivotId));//No I18N

        stringBuilder.append(XLSXParserUtility.toString("styleName",styleName));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("height",height));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("width",width));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("href",href));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("type",type));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("id",id));//No I18N

        stringBuilder.append(XLSXParserUtility.toString("footer",footer));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("legend",legend));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("plotArea",plotArea));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("subtitle",subtitle));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("title",title));//No I18N

        stringBuilder.append(XLSXParserUtility.toString("chartStyle",chartStyle));//No I18N

//        stringBuilder.append(XLSXParserUtility.toString("xLSXChartClass",xLSXChartClass));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("sheet",sheet));//No I18N
        stringBuilder.append(XLSXParserUtility.toString("xlsxd",xlsxd));//No I18N
        stringBuilder.append("}");
        return stringBuilder.toString();
    }
    
}
