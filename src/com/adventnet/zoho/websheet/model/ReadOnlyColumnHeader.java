//$Id$
/*
 * ReadOnlyColumnHeader.java
 *
 * Created on February 7, 2011, 5:01 PM
 *
 * To change this template, choose Tools | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;

/**
 *
 * <AUTHOR>
 */
public class ReadOnlyColumnHeader implements Cloneable
{
    private Sheet sheet;
    private ColumnHeader columnHeader;
    private int colIndex;
    private int  colsRepeated = 1;
    

    /** Creates a new instance of ReadOnlyColumnHeader */
    public ReadOnlyColumnHeader()
    {

    }
    public ReadOnlyColumnHeader(Sheet sheet, ColumnHeader columnHeader, int colIndex, int colsRepeated)
    {
        this.sheet= sheet;
	this.columnHeader = columnHeader;
        this.colIndex = colIndex;
	this.colsRepeated = colsRepeated;
    }

    public void setProperties(Sheet sheet, ColumnHeader columnHeader, int colIndex, int colsRepeated)
    {
        this.sheet = sheet;
        this.columnHeader = columnHeader;
        this.colIndex = colIndex;
        this.colsRepeated = colsRepeated;
    }

    
    public Sheet getSheet(){
        return this.sheet;
    }
    
    public ColumnHeader getColumnHeader()
    {
	return columnHeader;
    }

    public int getColIndex()
    {
        return colIndex;
    }
    
    public int getColsRepeated()
    {
        return colsRepeated;
    }

    @Override
    public ReadOnlyColumnHeader clone()
    {
        ReadOnlyColumnHeader readOnlyColumnHeader = new ReadOnlyColumnHeader();
        readOnlyColumnHeader.setProperties(this.sheet, this.columnHeader, this.colIndex, this.colsRepeated);
        return readOnlyColumnHeader;
    }
    
}
