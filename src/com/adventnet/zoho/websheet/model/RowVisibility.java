/* $Id$ */
package com.adventnet.zoho.websheet.model;

/**
 * The enum will have list of classifications for row visibilities.
 * <ul>
 * <li>
 * {@code VISIBLE}
 * </li>
 * <li>
 * {@code COLLAPSE}
 * </li>
 * <li>
 * {@code FILTER}
 * </li>
 * <li>
 * {@code UN_HIDDEN}
 * </li>
 * <li>
 * {@code UN_FILTERED}
 * </li>
 * </ul>
 * 
 * <AUTHOR> N J ( ZT-0049 )
 *
 */
public enum RowVisibility
{
	VISIBLE(0, "visible"), //No I18N
	COLLAPSE(1, "collapse"), //No I18N
	FILTER(2, "filter"), //No I18N
	UN_HIDDEN(3, "un-hidden"), //No I18N
	UN_FILTERED(4, "un-filtered"); //No I18N
	
	private int index;
	private String text;
	
	RowVisibility(int index, String text)
	{
		this.index = index;
		this.text = text;
	}
	
	public int getInt()
	{
		return this.index;
	}
	
	@Override
	public String toString()
	{
		return this.text;
	}
	
	public static RowVisibility getRowVisibility(int visibility)
	{
		for(RowVisibility rv : RowVisibility.values())
		{
			if(rv.getInt() == visibility)
			{
				return rv;
			}
		}
		return VISIBLE;
	}
	
	public static RowVisibility getRowVisibility(String visibility)
	{
		for(RowVisibility rv : RowVisibility.values())
		{
			if(rv.toString().equalsIgnoreCase(visibility))
			{
				return rv;
			}
		}
		return VISIBLE;
	}
}
