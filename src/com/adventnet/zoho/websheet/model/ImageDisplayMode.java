/* $Id$ */
package com.adventnet.zoho.websheet.model;

/**
 * <AUTHOR> N J (ZT-0049)
 *
 */
public enum ImageDisplayMode
{
    FIT(1),
    STRETCH(2),
    ORIGINAL(3),
    CUSTOM(4);
    private int id;
    ImageDisplayMode(int id)
    {
        this.id = id;
    }

    public static ImageDisplayMode getImageDisplayMode(int id)
    {
        for(ImageDisplayMode mode : ImageDisplayMode.values())
        {
            if(mode.getId() == id)
            {
                return mode;
            }
        }
        return FIT;
    }

    public int getId()
    {
        return this.id;
    }

    @Override
    public String toString()
    {
        return String.valueOf(this.getId());
    }
}
