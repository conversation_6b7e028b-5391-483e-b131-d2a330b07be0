//$Id$

package com.adventnet.zoho.websheet.model;

import com.adventnet.iam.User;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Row;
import com.adventnet.wms.api.CollaborationApi;
import com.adventnet.wms.api.WmsApi;
import com.adventnet.zoho.websheet.model.Book.BookType;
import com.adventnet.zoho.websheet.model.TabInfo.TabType;
import com.adventnet.zoho.websheet.model.ext.ZSConcurrentHashMap;
import com.adventnet.zoho.websheet.model.externalRange.ExternalRangeManager;
import com.adventnet.zoho.websheet.model.externalRange.ExternalRangeUtils;
import com.adventnet.zoho.websheet.model.externalRange.HttpTask.HttpTaskManager;
import com.adventnet.zoho.websheet.model.redis.RedisHelper;
import com.adventnet.zoho.websheet.model.response.ResponseObject;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.model.util.ZSStore.FileExtn;
import com.adventnet.zoho.websheet.model.util.ZSStore.FileName;
import com.adventnet.zoho.websheet.model.writer.zs.WorkbookToZS;
import com.adventnet.zoho.websheet.store.Store;
import com.adventnet.zoho.websheet.store.dfs.SheetFileInfo;
import com.zoho.sheet.action.externaldata.metastorage.DBActionManager;
import com.zoho.sheet.authorization.AppUtil;
import com.zoho.sheet.chartengine.ChartEngine;
import com.zoho.sheet.chartengine.ChartEngineManager;
import com.zoho.sheet.conversion.XlsxImporter;
import com.zoho.sheet.util.ChatUtils;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.RemoteUtils;
import com.zoho.sheet.util.WebDataUtils;
import com.zoho.zfsng.client.ZohoFS;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.lang.ref.SoftReference;
import java.nio.file.Files;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import static com.adventnet.zoho.websheet.model.redis.RedisHelper.EXECUTED_ACTIONS_CACHE_LIST;

/**
 *
 * <AUTHOR>
 */
public class WorkbookContainer
{


	public static Logger logger = Logger.getLogger(WorkbookContainer.class.getName());

	//private ExecutorService actionExecutorPool = Executors.newSingleThreadExecutor();

	private Map<BookType, Book> books = new HashMap();
	private SoftReference<Map<BookType, Book>> cachedBooks = null;
	private Workbook mergePreviewWorkbook = null;
	private String cachedVersionNumber=null;

	private SoftReference<SrcBooksCached> srcBooksCachedRef = new SoftReference<>(null);

	private BlockingQueue<JSONObjectWrapper> actionQueue = new ArrayBlockingQueue<>(EngineConstants.ACTIONS_QUEUE_SIZE);

	//private List<JSONObjectWrapper> xActionList = new ArrayList<JSONObjectWrapper>();

	private int actionId = 0; // while reading this will be initialized from Actions.List file (containing actionObject)
	private int xActionId	=	0;
	//private int lastVersionedActionId = -1;
	//private int lastSavedActionId = 0;
//private transient int transientXActionId = 0;
	private int libraryType = -1;
	private String docId = null;
	private String resourceId = null;
	private String docName = null;
	private String docUrl = null;
	private String docOwner = null;
	private String creatorZUID = null;
	private String creatorEmailId = null;
	private String creatorFullName = null;
	private String docOwnerId = null;
	private String docOwnerFullName =null;
	private String collabId = null;
	private String docOwnerZUID = null;
	private String docsSpaceId = null;
	private long createdTime = -1;
	private String delTime = null;
	private String lastActionPerformer	=	null;
	private String lastActionPerformerZUID = null;
	private JSONObjectWrapper jsonImportCache = new JSONObjectWrapper();
	private boolean isJSONSave = false;

	private transient boolean isReadOnly = false;

	private boolean isExcelOrScratchView = false;
	private boolean showFormulas = true;
	private boolean isGdriveDoc = false;

	//private boolean isResetWorkbook = false;

	private String currCursorPosSheetCodeName = null;

//    private transient Set<String> orderedAuthorsList = null; //Stores usersList in the order, users opened the workbook; used to get userpresence colors
//    private transient Map<String, String> userPresenceColorMap = new ZSConcurrentHashMap<String, String>(); //zuid vs. color code

	private Map<String, UserProfile> userProfileMap = null;
	private List<ContainerListener> listenerList = null;
	private ActionExecutor executor = null;
	private Long lastAccessTimeStamp = System.currentTimeMillis();
	private Long lastEditTimeStamp = System.currentTimeMillis();
	private Long lastSavedTimeStamp = System.currentTimeMillis();
	private Map<String, Long> feedUpdateTimeStamp = new ZSConcurrentHashMap<>();
	private Map<Long, String> documentSheetsTable = new ZSConcurrentHashMap<>();
	private MacroInterface macroInterface = null;

	//////////
	private final ReentrantLock writeLock = new ReentrantLock();
	private final ReentrantLock readLock = new ReentrantLock();

	private String	apiKeyId	=	null;
	private String	remoteBookId	=	null;
	private boolean remoteAutoSaveMode = false;

	private Hashtable<String, Map<BookType, Book>> versionBooksMap = new Hashtable();

	private String pushFormat;
	private String rmUserDocId;
	private String handBackId;
	private String integrationMetaData;

	private transient AtomicBoolean isExecuting = new AtomicBoolean(); // default value false
	private transient Long tempActionsResId = null;
	private transient boolean isNewDoc = false;
	private	transient boolean isJustCreated = true;

	private Set<String> versionAuthorsList = new LinkedHashSet<>();    //Authors zuid
	private transient List<String> transientVersionAuthorsList;
	private transient boolean isMinorVersion = false; // to tag the save version as minor in checkin/checkout case.

	private transient List<JSONObjectWrapper> auditTrailActionList = null; // used to maintain for auditTrail filter purpose.

	//Variable used to remve the container for transient Aceess
	private	boolean	isTransient		=	false;
	private long thumbnailCreatedTime = 0;

	private String lastVersionNo = null;

	private String tmpContainerIdentity = null;

	private transient int publicStartAid = 0;//for public docs

//        private transient boolean isNextSaveMajorVersion = false;

	private transient int highlightInvalidCell=0;

	private transient FocusObject focusObject = null;

	private Map<String, JSONObjectWrapper> rangeIDMap = new HashMap<String, JSONObjectWrapper>();
	private int jsonMaxRow = -1;
	private int jsontrackCount = -1;

	public int getJsonMaxRow(){
		return this.jsonMaxRow;
	}

	public void setJsonMaxRow(int jsonMaxRow){
		this.jsonMaxRow = jsonMaxRow;
	}
	public int getjsontrackCount(){
		return this.jsontrackCount;
	}

	public void setjsontrackCount(int jsontrackCount){
		this.jsontrackCount = jsontrackCount;
	}
	public JSONObjectWrapper getRangeFromID(String id) {
		if(this.rangeIDMap.containsKey(id)) {
			return this.rangeIDMap.get(id);
		}
		return null;
	}

	public void addIDtoRangeMap(String id, JSONObjectWrapper json) {
		this.rangeIDMap.put(id, json);
	}
	private Boolean isPicklistEnabled = null;
	private Boolean isLinkSpreadsheetEnabled = null;
	private Boolean isTableEnabled = null;
	private boolean isWorkDrive = false;

	private transient Boolean isPaidOrg = null;

	private ExternalRangeManager externalRangeManager = new ExternalRangeManager();

	public ExternalRangeManager getExternalRangeManager() {
		return externalRangeManager;
	}

	public Boolean isPicklistEnabled() {
		if(isPicklistEnabled == null) {
			isPicklistEnabled = EnginePropertyUtil.isPicklistEnabled(DocumentUtils.getZOID(this.getDocOwner()));
		}
		return isPicklistEnabled;
	}


	public Boolean isTableEnabled() {
		if(isTableEnabled == null) {
			/* If this boolean is true, then enable Table UI to all accounts. If false, enable only to Zoho Corp Accounts. */
			if(EngineConstants.IS_TABLE_FEATURE_ENABLED) {
				isTableEnabled = true;
			}
			else {
				isTableEnabled = ClientUtils.isFeatureEnabled("isTableUIEnabled", "ZohoCorpId"); // No I18N
			}
		}
		return isTableEnabled;
	}
	public void setMergePreviewWorkbook(Workbook mergePreviewWorkbook)
	{
		this.mergePreviewWorkbook = mergePreviewWorkbook;
	}

	public Workbook getMergePreviewWorkbook()
	{
		return this.mergePreviewWorkbook;
	}


//        private DocMetaInfo docMetaInfo = null;
//
//        public void setDocMetaInfo(DocMetaInfo docMetaInfo)
//        {
//            this.docMetaInfo = docMetaInfo;
//        }

//        private Map<String, focusObject> focusObjectMap= null;

//        public class focusObject{
////                public String activeSheet;
////                public Map<String, Map<String,String>> sheetProps;
//                public int rowIndex;
//                public int colIndex;
//                public int posLeft;
//                public int posBottom;
//
//                public focusObject(int rowIndex,int colIndex,int posLeft,int posBottom)
//                {
//                    this.rowIndex = rowIndex;
//                    this.colIndex = colIndex;
//                    this.posLeft = posLeft;
//                    this.posBottom = posBottom;
//                }
//            }


//            public void addTofocusObjectMap(String ascSheetName, focusObject focusObj){
//                focusObjectMap.put(ascSheetName, focusObj);
//            }

	public void setFocusObject(FocusObject focusObject)
	{
		this.focusObject = focusObject;
	}

	public FocusObject getFocusObject()
	{
		return this.focusObject;
	}
	public JSONObjectWrapper getJsonCache()
	{
		return this.jsonImportCache;
	}
	public void setJsonCache(JSONObjectWrapper cache)
	{
		this.jsonImportCache = cache;
	}
	public boolean getJsonsave()
	{
		return this.isJSONSave;
	}
	public void setJsonsave(Boolean isJsonSave)
	{
		this.isJSONSave = isJsonSave;
	}

	public	static	class	ContainerEntity	{
		public	String	documentId;
		public	String	resourceId;
		public	String	documentName;
		public	String	docOwner;
		public	String	docsSpaceId;
		//public	String 	docOwnerId;
		public  String  docType;
		public  long  createdTime = -1;
		public  String  delTime;
		public	String	collabId;
		public	String	apiKeyId;
		public	String	remoteBookId;
		public	boolean	remoteAutoSaveMode;

		public	String	docUrl;
		public	String	creatorZuid;
		public	String	pushFormat;
		public	String	rmUserDocId;
		public	String	handBackId;
		public boolean isScratchOrExcelView;
		public  String  integrationMetaData;
		public	long	lastSavedTime;
		public boolean isReadOnly;
		public boolean showFormulas = true;
		public boolean isJSONSave = false;
		public int jsontrackCount = -1;
		public int jsonmaxRow = -1;
	}

	public	static	ContainerEntity	getEntity()	{
		return	new	ContainerEntity();
	}

	public WorkbookContainer(String docId) throws Exception
	{


	}

	public WorkbookContainer(long docid, String docOwner) throws Exception
	{
		Map map = DocumentUtils.getDocumentDetails(docid, docOwner);


		this.resourceId = String.valueOf(map.get("RESOURCE_ID"));
		this.docName = String.valueOf(map.get("DOCUMENT_NAME"));
		this.docId = String.valueOf(map.get("DOCUMENT_ID"));
		this.docOwner = String.valueOf(map.get("AUTHOR_NAME"));
		this.docOwnerId = String.valueOf(map.get("OWNER_ZUID"));
		String docType = String.valueOf(map.get("DOCUMENT_TYPE"));
		if(Integer.toString(Constants.REMOTE_DOCUMENT).equals(docType) || Integer.toString(Constants.AUTHENTICATED_REMOTE_DOCUMENT).equals(docType)) {
			long docIdLong = Long.parseLong(docId);
			this.remoteBookId = RemoteUtils.getRemoteBookId(docIdLong, this.docOwner).toString();
		} else if (this.resourceId != null) {
			this.docsSpaceId = ZohoFS.getOwnerZID(this.resourceId);
			try {
				this.isWorkDrive  = ZohoFS.isNewUI(this.resourceId);
			} catch (Exception e) {
				// TODO: handle exception
			}

		}
		this.collabId = map.get("COLLAB_ID")==null?null:String.valueOf(map.get("COLLAB_ID"));
		this.docUrl = String.valueOf(map.get("DOCUMENT_NAME_URL"));
		this.createdTime = (long) map.get("CREATED_DATE");
		this.delTime = String.valueOf(map.get("DEL_TIME"));

		if(docType != null)
		{
			if((String.valueOf(Constants.NEW_DOCUMENT)).equals(docType)){
				setIsNewDoc(true);
			}else{
				this.isGdriveDoc = (String.valueOf(Constants.GDRIVE_DOCUMENT)).equals(docType);
			}
		}
		if (this.resourceId != null && this.creatorZUID == null) {
			try {
				this.creatorZUID = ZohoFS.getResourceInfo(this.resourceId).getCreator();
			} catch (Exception e) {
				logger.log(Level.WARNING, "Exception while setting creator zuid details ", e.getMessage());
			}
		}
		/////////
		//Assumed It's remote Case, added check for AUTH remote case
		if(RemoteUtils.isRemoteDocument(docOwner)) {
			docOwnerZUID = String.valueOf(-1L);
		}	else {
			//////////////////
			init();
		}
		this.userProfileMap = new ConcurrentHashMap<>();

		try
		{
			this.libraryType = (int) new JSONObjectWrapper(ZohoFS.getLibraryInfo(this.docsSpaceId, this.resourceId)).get("LIBRARY_TYPE");  //No I18N
		}
		catch(Exception e)
		{
			logger.log(Level.WARNING, "Exception while setting library type.", e);
		}
	}


	public WorkbookContainer(String rid, String docOwner) throws Exception
	{
		if (docOwner == null) {
			String spaceId = ZohoFS.getSpaceId(rid);
			docOwner = DocumentUtils.getZUserName(spaceId);
		}
		Map map = DocumentUtils.getDocumentDetails(rid, docOwner);
		if(!map.isEmpty())
		{
			this.resourceId = String.valueOf(map.get("RESOURCE_ID"));
			this.docName = String.valueOf(map.get("DOCUMENT_NAME"));
			this.docId = String.valueOf(map.get("DOCUMENT_ID"));
			this.docOwner = String.valueOf(map.get("AUTHOR_NAME"));
			this.docOwnerId = String.valueOf(map.get("ACCOUNT_ID"));
			String docType = String.valueOf(map.get("DOCUMENT_TYPE"));
			if(Integer.toString(Constants.REMOTE_DOCUMENT).equals(docType) || Integer.toString(Constants.AUTHENTICATED_REMOTE_DOCUMENT).equals(docType)) {
				long docIdLong = Long.parseLong(docId);
				this.remoteBookId = RemoteUtils.getRemoteBookId(docIdLong, this.docOwner).toString();
			} else if (this.resourceId != null) {

				try
				{
					this.docsSpaceId = ZohoFS.getOwnerZID(this.resourceId);
				}
				catch (Exception e)
				{
					logger.log(Level.WARNING, "Exception while setting docsSpaceId details", e);
				}
				try
				{
					this.isWorkDrive  = ZohoFS.isNewUI(this.resourceId);
				}
				catch (Exception e)
				{
					logger.log(Level.WARNING, "Exception while checking workdrive document", e);
				}

			}
			this.collabId = map.get("COLLAB_ID")==null?null:String.valueOf(map.get("COLLAB_ID"));
			this.docUrl = String.valueOf(map.get("DOCUMENT_NAME_URL"));
			this.delTime = String.valueOf(map.get("DEL_TIME"));
			this.createdTime = (long) map.get("CREATED_DATE");

			if(docType != null)
			{
				if((String.valueOf(Constants.NEW_DOCUMENT)).equals(docType)){
					setIsNewDoc(true);
				}else{
					this.isGdriveDoc = (String.valueOf(Constants.GDRIVE_DOCUMENT)).equals(docType);
				}
			}
		}
		else
		{
			this.resourceId = rid;
			this.docOwner = docOwner;
			try
			{
				this.docsSpaceId = ZohoFS.getOwnerZID(this.resourceId);
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Exception while setting docsSpaceId details", e);
			}
			try
			{
				this.isWorkDrive  = ZohoFS.isNewUI(this.resourceId);
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "Exception while checking workdrive document", e);
			}
		}
		try
		{
			this.creatorZUID = ZohoFS.getResourceInfo(this.resourceId).getCreator();
		}
		catch (Exception e)
		{
			logger.log(Level.WARNING, "Exception while setting creator zuid details ", e.getMessage());
		}
		try
		{
			this.libraryType = (int) new JSONObjectWrapper(ZohoFS.getLibraryInfo(this.docsSpaceId, this.resourceId)).get("LIBRARY_TYPE");  //No I18N
		}
		catch(Exception e)
		{
			logger.log(Level.WARNING, "Exception while setting library type.", e);
		}

		/////////
		//Assumed It's remote Case, added check for AUTH remote case
		if(RemoteUtils.isRemoteDocument(docOwner)) {
			docOwnerZUID = String.valueOf(-1L);
		}	else {
			//////////////////
			init();
		}
		this.userProfileMap = new ConcurrentHashMap<>();

	}

	//	public WorkbookContainer(String documentId, String documnetName, String docOwner, String docOwnerId, String collabId, String apiKeyId, String remoteBookId) throws Exception
	//	{
	//		this.docId 			=	documentId;
	//		this.docName 		=	documnetName;
	//		this.docOwner		=	docOwner; // can be 'Public' incase of Remote mode
	//		this.docOwnerId 	= 	docOwnerId; // can be 'apistore' incase of Remote mode
	//		this.collabId 		=	collabId;
	//		this.apiKeyId		=	apiKeyId;
	//		this.remoteBookId	=	remoteBookId;
	//		/////////
	//		initDocOnwerZUID();
	//	}

	public WorkbookContainer(ContainerEntity entity)
	{
		this.docId 			=	entity.documentId;
		this.resourceId 	=	entity.resourceId;
		this.docName 		=	entity.documentName;
		this.docOwner		=	entity.docOwner;

		if(this.resourceId != null && entity.creatorZuid == null)
		{
			try
			{
				this.creatorZUID = ZohoFS.getResourceInfo(this.resourceId).getCreator();
			}
			catch(Exception e)
			{
				logger.log(Level.WARNING, "Exception while setting creator zuid details ", e.getMessage());
			}
		}
		else
		{
			this.creatorZUID    =   entity.creatorZuid;
		}
		this.isExcelOrScratchView = entity.isScratchOrExcelView;
		if(entity.docsSpaceId != null) {
			this.docsSpaceId    =   entity.docsSpaceId;
		}
		if(this.resourceId != null) {
			try {
				this.isWorkDrive  = ZohoFS.isNewUI(this.resourceId);
			} catch (Exception e1) {
				// TODO Auto-generated catch block
			}
			if(entity.docsSpaceId == null){

				try {
					this.docsSpaceId = ZohoFS.getOwnerZID(this.resourceId);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					logger.log(Level.WARNING, "Exception while setting  docsSpaceId details ", e.getMessage());
				}
			}
			try
			{
				this.libraryType = (int) new JSONObjectWrapper(ZohoFS.getLibraryInfo(this.docsSpaceId, this.resourceId)).get("LIBRARY_TYPE");  //No I18N
			}
			catch(Exception e)
			{
				logger.log(Level.WARNING, "Exception while setting library type.", e);
			}
		}

		//this.docOwnerId 	= 	entity.docOwnerId;
		this.createdTime		=	entity.createdTime;
		this.delTime		=	entity.delTime;
		this.collabId 		=	entity.collabId;
		this.apiKeyId		=	entity.apiKeyId;
		this.remoteBookId	=	entity.remoteBookId;
		this.remoteAutoSaveMode = 	entity.remoteAutoSaveMode;
		this.docUrl			=	entity.docUrl;
		this.pushFormat		=	entity.pushFormat;
		this.rmUserDocId	=	entity.rmUserDocId;
		this.handBackId		=	entity.handBackId;
		this.integrationMetaData = entity.integrationMetaData;
		//this.lastSavedTimeStamp = entity.lastSavedTime;
		this.isReadOnly 	=	entity.isReadOnly;
		this.showFormulas	=	entity.showFormulas;
		this.isJSONSave = entity.isJSONSave;
		this.jsonMaxRow = entity.jsonmaxRow;
		this.jsontrackCount = entity.jsontrackCount;

		if (entity.docType != null) {
			if ((String.valueOf(Constants.NEW_DOCUMENT)).equals(entity.docType)) {
				setIsNewDoc(true);
			} else {
				this.isGdriveDoc = (String.valueOf(Constants.GDRIVE_DOCUMENT))
						.equals(entity.docType);
			}
		}
		/////////
		//Assumed It's remote Case, added check for AUTH remote case
		if(RemoteUtils.isRemoteDocument(docOwner)) {
			docOwnerZUID = String.valueOf(-1L);
		}	else {
			//////////////////
			init();
		}
		this.userProfileMap = new ConcurrentHashMap<>();
	}

	//private void initDocOwnerZUIDandFullName()
	private void init()
	{
		try {
			Object[] ZUIDandFullName=DocumentUtils.getZUIDandFullName(this.docOwner);
			this.docOwnerZUID =String.valueOf(ZUIDandFullName[0]);
			this.docOwnerFullName=String.valueOf(ZUIDandFullName[1]);

			if(this.resourceId != null && this.creatorZUID != null){
				if(!this.creatorZUID.equals(this.docOwnerZUID) && !this.creatorZUID.startsWith("$")){
					User user = DocumentUtils.getZUserObject(Long.valueOf(this.creatorZUID));
					if(user != null){
						this.creatorFullName = user.getFullName();
						this.creatorEmailId = user.getPrimaryEmail();
					}
				}else {
					this.creatorFullName = this.docOwnerFullName;
					this.creatorEmailId  = DocumentUtils.getZuserEmailId(this.docOwner);
				}
			}
		}
		catch(Exception e) {
			logger.log(Level.WARNING, "Exception while setting  creatorFullName details ", e.getMessage());
		}
		//////////////
//            since the list is retrieved when it is empty, initialization is not required.
//            initOrderedAuthorsList();
	}
	///////////////////////////



	public String getDocId()
	{
		return docId;
	}

	public int getLibraryType()
	{
		return libraryType;
	}

	public boolean isWorkDriveSpreadsheet()
	{
		return this.isWorkDrive;
	}

	public String getResourceId()
	{
		return resourceId;
	}

	public String getResourceKey()
	{
		return isRemoteMode() ? this.remoteBookId : this.resourceId;
	}

	public String getDocOwner()
	{
		return docOwner;
	}

	public String getDocOwnerId()
	{
		return docOwnerId;
	}
	public String getDocOwnerFullName()
	{
		return docOwnerFullName;
	}
	public String getDocOwnerZUID()
	{
		return docOwnerZUID;
	}
	public String getDocsSpaceId()
	{
		return docsSpaceId;
	}

	public String getCreatorZUID()
	{
		return creatorZUID;
	}
	public String getCreatorFullName()
	{
		return creatorFullName;
	}
	public String getCreatorEmailId()
	{
		return creatorEmailId;
	}
	public String getPushFormat()
	{
		return pushFormat;
	}
	public boolean getRemoteAutoSaveMode(){
		return this.remoteAutoSaveMode;
	}
	public void setRemoteAutoSaveMode(boolean remoteSaveMode){
		this.remoteAutoSaveMode = remoteSaveMode;
	}

	public String getDocName()
	{
		return docName;
	}

	//	public void setDocName(String docName)
	//    {
	//		// TODO: DB handling to update the docName
	//        this.docName = docName;
	//    }

	public String getDocUrl()
	{
		return docUrl;
	}

	//	public void setDocName(String docName)
	//    {
	//		// TODO: DB handling to update the docName
	//        this.docName = docName;
	//    }

	public boolean isRemoteMode()
	{
		return this.remoteBookId != null;
	}

	public boolean isAuthRemote(){
		return isRemoteMode() && getResourceId() != null;
	}
	public boolean isGdriveDoc(){
		return this.isGdriveDoc;
	}

	public boolean isPaidOrgDocument()
	{
		if(isPaidOrg == null)
		{
			try
			{
				isPaidOrg = !ZSCreditPointsUtil.isWDFreeUser(this.docsSpaceId);
			}
			catch (Exception e)
			{
				logger.log(Level.INFO, "error while getting license API detais :: rid: {0} :: docsSpaceId : {1}", new Object[]{this.getResourceId(), this.docsSpaceId});
				logger.log(Level.INFO, "error:", e);
				isPaidOrg = Boolean.FALSE;
			}
		}
		return isPaidOrg.booleanValue();
	}

	public List<JSONObjectWrapper> getAuditTrailActionList()
	{

		if(auditTrailActionList == null || auditTrailActionList.isEmpty())
		{
			auditTrailActionList = AuditTrailHelper.getVersionActionList(this, null);
		}
		return this.auditTrailActionList;
	}

	public List<JSONObjectWrapper> getAuditTrailActionList(String zfsngVersionNo)
	{
		List<JSONObjectWrapper> aTActionList = AuditTrailHelper.getVersionActionList(this, zfsngVersionNo);
		return aTActionList;
	}

	public void clearAuditTrailActionList()
	{
		if(auditTrailActionList != null)
		{
			this.auditTrailActionList.clear();
			// Should be done as null as we do the lazy loading in getAuditTrailActionList() method
			this.auditTrailActionList = null;
		}
	}


	/**
	 * @return the tempActionsResId
	 * @throws java.lang.Exception
	 */ public Long getTempActionsResId() throws Exception
	{
		if(tempActionsResId == null)
		{
			tempActionsResId = this.getFragmentId(FileName.ACTIONSTEMP, true, null);
		}
		return tempActionsResId;
	}

	/**
	 * @param tempActionsResId the tempActionsResId to set
	 */ public void setTempActionsResId(Long tempActionsResId)
	{
		this.tempActionsResId = tempActionsResId;
	}

	/**
	 * @return the isNewDoc
	 */ public boolean isNewDoc()
	{
		return isNewDoc;
	}

	/**
	 * @param isNewDoc the isNewDoc to set
	 */ private void setIsNewDoc(boolean isNewDoc)
	{
		this.isNewDoc = isNewDoc;
	}

	public boolean isIsMinorVersion() {
		return isMinorVersion;
	}

	public void setIsMinorVersion(boolean isMinorVersion) {
		this.isMinorVersion = isMinorVersion;
	}

	private boolean checkAndCreateSavingState() {
		try {
//                    return RedisHelper.sismember(RedisHelper.SAVING_WORKBOOKS, getResourceKey());

			Double score = RedisHelper.zscore(RedisHelper.SAVING_WORKBOOKS, getResourceKey());
			if(score == null){
				RedisHelper.zadd(RedisHelper.SAVING_WORKBOOKS, System.currentTimeMillis(),getResourceKey(), -1);
				return false;
			}
			long currTime = System.currentTimeMillis();

			if((currTime - score) >= RedisHelper.MAX_SAVING_TIME_PERIOD*1000){
//                        RedisHelper.zrem(RedisHelper.SAVING_WORKBOOKS, getResourceKey());
				RedisHelper.zadd(RedisHelper.SAVING_WORKBOOKS, System.currentTimeMillis(),getResourceKey(), -1);
				return false;
			}

		} catch (Exception ex) {
			logger.log(Level.WARNING, "Exception while checking isSaving varaible", ex);
		}

		return true;
	}

	// Should not do any write operations here
	boolean isSaving() {
		try {
//                    return RedisHelper.sismember(RedisHelper.SAVING_WORKBOOKS, getResourceKey());

			Double score = RedisHelper.zscore(RedisHelper.SAVING_WORKBOOKS, getResourceKey());
			if(score == null){
				return false;
			}

			long currTime = System.currentTimeMillis();
			if((currTime - score) >= RedisHelper.MAX_SAVING_TIME_PERIOD*1000){
				return false;
			}

		} catch (Exception ex) {
			logger.log(Level.WARNING, "Exception while checking isSaving varaible", ex);
		}

		return true;
	}


//            public int getTransientXActionId()
//            {
//                return transientXActionId;
//            }

//            private void setTransientXActionId(int transientXActionId)
//            {
//                this.transientXActionId = transientXActionId;
//            }

	public Set<String> getVersionAuthorsList()
	{
		return this.versionAuthorsList;
	}

	public void addVersionAuthor(String author)
	{
		this.versionAuthorsList.add(author);
	}

	public List<String> getTransientVersionAuthorsList()
	{
		return this.transientVersionAuthorsList;
	}

//            public boolean isNextSaveMajorVersion()
//            {
//                return this.isNextSaveMajorVersion;
//            }


	// This method is called during the initialization of WorkbookContainer
	private void initOrderedAuthorsList()
	{
		//TODO : need to remove the logger prints
		// For REMOTE case the remoteBookId will not be NULL
		if(this.remoteBookId != null)
		{
			return;
		}


		try
		{
			getAuthorsList();
//                    if(getAuthorsList().isEmpty())
//                    {
//
//                        String additionalInfo = ZohoFS.getResourceAdditionalInfo(this.docsSpaceId, this.resourceId);
//
//                        logger.log(Level.INFO, "AUTHORS LIST Initialising...for RID :: {0} :: {1}", new Object[]{resourceId, additionalInfo});
//
//                        JSONObjectWrapper additionalInfoJson = new JSONObjectWrapper();
//                        if(additionalInfo != null && !"NA".equals(additionalInfo))
//                        {
//                            additionalInfoJson = new JSONObjectWrapper(additionalInfo);
//                        }
//
//                        if(additionalInfoJson.has(Constants.ORDERED_USERS_LIST))
//                        {
//                            JSONArrayWrapper usersList = additionalInfoJson.getJSONArray(Constants.ORDERED_USERS_LIST);
//                            String[] zuids = new String[usersList.length()];
//                            for(int i = 0; i < usersList.length(); i++)
//                            {
//                                zuids[i] = usersList.getString(i);
//                            }
//                            RedisHelper.updateOrderedAuthorsList(0, getResourceKey(), zuids);
//                        }
//                    }
		}catch(Exception ex)
		{

			logger.log(Level.WARNING, "RESOURCE_ID: "+this.getResourceKey()+" Exception while fetching the Authors List from ZFSNG : ", ex);
		}

	}


	public void updateOrderedAuthorsList(String userZUID){
		try{
			getAuthorsList(); //calling this to initialize redis list

            BufferedWriter bw = null;
			Store documentStore = null;
			JSONObjectWrapper jObj = null;
			InputStream is = null;
			HashMap writeInfo = null;
			SheetFileInfo sheetFileInfo = null;

			List orderedUsersList = RedisHelper.updateOrderedAuthorsList(0, getResourceKey(), userZUID);
			if(orderedUsersList != null && !this.isRemoteMode())
			{
				try {
					documentStore = this.getStore(Long.valueOf(docId), FileName.AUTHORS);


					sheetFileInfo = documentStore.getFileInfo(Long.valueOf(docId), ZSStore.FILENAME_AUTHORS, ZSStore.FileExtn.TXT.toString().toLowerCase());


					if(sheetFileInfo != null)
					{
						List<String> userList = new ArrayList<String>();
						is = documentStore.read(Long.valueOf(docId), ZSStore.FILENAME_AUTHORS, ZSStore.FileExtn.TXT.toString().toLowerCase());
						if(is != null)
						{
                            try(BufferedReader br = new BufferedReader(new InputStreamReader(is))) {
                                String str = null;
                                while((str = br.readLine()) != null) {
                                    userList.add(str);
                                }
                            }
                        }
						if(! userList.contains(userZUID)) {
							userList.add(userZUID);
							writeInfo = documentStore.write(Long.valueOf(docId), sheetFileInfo);
							OutputStream os = (OutputStream) writeInfo.get("OS");
							bw = new BufferedWriter(new OutputStreamWriter(os));
							for(String userStr : userList)
							{
								bw.write(userStr + System.lineSeparator());
							}
						}
					}else {
						//Removing user list from additional info while first time writing authors list in file - venkat
						try {
							String additionalInfoStr = ZohoFS.getResourceAdditionalInfo(docsSpaceId, resourceId);
							JSONObjectWrapper additionalInfo = new JSONObjectWrapper(additionalInfoStr);
							if(additionalInfo.has(Constants.ORDERED_USERS_LIST)) {
								additionalInfo.remove(Constants.ORDERED_USERS_LIST);
								ZohoFS.updateResourceAdditionalInfo(docsSpaceId, resourceId, additionalInfo.toString());
							}

						}catch(Exception ex) {
							logger.log(Level.WARNING, null, ex);
						}
						sheetFileInfo = documentStore.createFileInfo(Long.valueOf(docId), ZSStore.FILENAME_AUTHORS, ZSStore.FileExtn.TXT.toString().toLowerCase());
						writeInfo = documentStore.write(Long.valueOf(docId), sheetFileInfo);
						OutputStream os = (OutputStream) writeInfo.get("OS");
						bw = new BufferedWriter(new OutputStreamWriter(os));
						for(Object userObj : orderedUsersList)
						{
							bw.write((String)userObj + System.lineSeparator());
						}
					}
				}catch(Exception ex) {
					logger.log(Level.WARNING, null, ex);
				}finally
				{
					if(bw != null)
					{
						try
						{
							bw.flush();
							bw.close();
							documentStore.finishWrite(writeInfo);
						}
						catch(Exception ex1)
						{
							logger.log(Level.WARNING, null, ex1);
						}
					}

				}

			}

		}catch(Exception ex)
		{
			logger.log(Level.WARNING, "RESOURCE_ID: "+this.getResourceKey()+" Exception while fetching/Updating the Authors List from ZFSNG : ", ex);
		}
	}

	public String getUserPresenceColor(List<String> authorsList, String zuid){

		int index = authorsList.indexOf(zuid) % Constants.USERPRESENCE_COLORS.length;
		return index != -1 ? Constants.USERPRESENCE_COLORS[index] : null;

	}

	public List<String> getAuthorsList()
	{
        BufferedWriter bw = null;
		Store documentStore = null;
		JSONObjectWrapper jObj = null;
		InputStream is = null;
		HashMap writeInfo = null;
		SheetFileInfo sheetFileInfo = null;
		try{
			List<String> authorsList = RedisHelper.lrange(RedisHelper.AUTHORS_LIST+getResourceKey(), 0, -1);

			if(!isRemoteMode() && authorsList.isEmpty())
			{

				documentStore = this.getStore(Long.valueOf(docId), FileName.AUTHORS);
				sheetFileInfo = documentStore.getFileInfo(Long.valueOf(docId), ZSStore.FILENAME_AUTHORS, ZSStore.FileExtn.TXT.toString().toLowerCase());
				if(sheetFileInfo != null)
				{
					logger.info("[ORDER_LIST] sheet file info available");
					is = documentStore.read(Long.valueOf(docId), ZSStore.FILENAME_AUTHORS, ZSStore.FileExtn.TXT.toString().toLowerCase());
					if(is != null)
					{
                        try(BufferedReader br = new BufferedReader(new InputStreamReader(is))) {
                            String str = null;

                            int i = 0;
                            while((str = br.readLine()) != null) {
                                authorsList.add(str);
                            }
                        }
                        String zuids[ ] = new String[authorsList.size()];
						authorsList.toArray(zuids);
						RedisHelper.updateOrderedAuthorsList(0, getResourceKey(), zuids);
					}

				}else {
					logger.info("[ORDER_LIST] sheet file info not available ... taking the info from zohofs and removing them ");
					String additionalInfo = ZohoFS.getResourceAdditionalInfo(docsSpaceId, resourceId);


					JSONObjectWrapper additionalInfoJson = new JSONObjectWrapper();
					if(additionalInfo != null && !"NA".equals(additionalInfo))
					{
						additionalInfoJson = new JSONObjectWrapper(additionalInfo);
					}

					if(additionalInfoJson.has(Constants.ORDERED_USERS_LIST))
					{
						JSONArrayWrapper usersList = additionalInfoJson.getJSONArray(Constants.ORDERED_USERS_LIST);
						String[] zuids = new String[usersList.length()];
						sheetFileInfo = documentStore.createFileInfo(Long.valueOf(docId), ZSStore.FILENAME_AUTHORS, ZSStore.FileExtn.TXT.toString().toLowerCase());
						writeInfo = documentStore.write(Long.valueOf(docId), sheetFileInfo);
						OutputStream os = (OutputStream) writeInfo.get("OS");
						bw = new BufferedWriter(new OutputStreamWriter(os));

						for(int i = 0; i < usersList.length(); i++)
						{
							zuids[i] = usersList.getString(i);
							bw.write(zuids[i] + System.lineSeparator());
						}
						RedisHelper.updateOrderedAuthorsList(0, getResourceKey(), zuids);
						/*additionalInfoJson.remove(Constants.ORDERED_USERS_LIST);
						logger.info(" before update .."+additionalInfoJson.toString());
						ZohoFS.updateResourceAdditionalInfo(docsSpaceId, resourceId, additionalInfoJson.toString());
						logger.info(" after update .."+ZohoFS.getResourceAdditionalInfo(docsSpaceId, resourceId));*/

					}
				}
			}
			return RedisHelper.lrange(RedisHelper.AUTHORS_LIST+getResourceKey(), 0, -1);
		}catch(Exception ex){
			logger.log(Level.WARNING , "Exception while getting authorsList" , ex);
		}finally
		{
			if(bw != null)
			{
				try
				{
					bw.flush();
					bw.close();
					documentStore.finishWrite(writeInfo);
				}
				catch(Exception ex1)
				{
					logger.log(Level.WARNING, null, ex1);
				}
			}

		}
		return new ArrayList<>();
	}



	/**
	 * @return the isJustCreated
	 */
	public boolean isJustCreated()
	{
		return isJustCreated;
	}

	/**
	 * @param isJustCreated the isJustCreated to set
	 */
	public void setIsJustCreated(boolean isJustCreated)
	{
		this.isJustCreated = isJustCreated;
	}


	/**
	 * @return the activeSheetCodeName
	 */ public String getCurrCursorPosSheetCodeName()
	{
		return currCursorPosSheetCodeName;
	}

	/**
	 * @param activeSheetCodeName the activeSheetCodeName to set
	 */
	public void setCurrCursorPosSheetCodeName(String activeSheetCodeName)
	{
		this.currCursorPosSheetCodeName = activeSheetCodeName;
	}

	public boolean isTrashed()
	{
		return (delTime != null && (Long.parseLong(delTime) > 0));
	}

	public boolean isPurged()
	{
		return (delTime != null && (Long.parseLong(delTime) < 0));
	}

	public	boolean	isTransient()	{
		return this.isTransient;
	}

	public	void	setTransient(boolean	isTransient)	{
		this.isTransient	=	isTransient;
	}


	public Long getLastAccessTimeStamp()
	{
		return lastAccessTimeStamp;
	}

	public void setLastAccessTimeStamp(Long lastAccessTimeStamp)
	{
		this.lastAccessTimeStamp = lastAccessTimeStamp;
	}

	public Long getLastEditTimeStamp()
	{
		return lastEditTimeStamp;
	}

	public void setLastEditTimeStamp(Long lastEditTimeStamp)
	{
		this.lastEditTimeStamp = lastEditTimeStamp;
	}

	public Long getLastSavedTimeStamp()
	{
		return lastSavedTimeStamp;
	}

	public void setLastSavedTimeStamp(Long lastSavedTimeStamp)
	{
		this.lastSavedTimeStamp = lastSavedTimeStamp;
	}
	public String getLastVersionNo()
	{
		initializeLastVersionNo();
		return lastVersionNo;
	}

	public void setLastVersionNo(String lastVersionNo)
	{
		this.lastVersionNo = lastVersionNo;
	}

	public void initializeLastVersionNo()
	{
		try
		{
			if (lastVersionNo == null)
			{
				if (this.getCachedVersion() == null && !this.isReadOnly() && !this.isRemoteMode())
				{
					String ver = ZohoFS.getTopVersion(this.getDocsSpaceId(), this.getResourceKey());
					if (ver == null)
					{
						ver = "0.0";
					}
					this.setLastVersionNo(ver);
					//UndoRedoManager.initializeClone(this, null);

				}
			}
		} catch (Exception e){
			logger.log(Level.INFO, "Exception while initializing lastversionNO; RESOURCE_ID: "+ this.getResourceKey(), e);
		}
	}
	// WebData - Roseline

	public Long getFeedUpdateTimeStamp(String webDataID)
	{
		if(feedUpdateTimeStamp.containsKey(webDataID))
		{
			return feedUpdateTimeStamp.get(webDataID);
		}
		return null;
	}

	public void setFeedUpdateTimeStamp(String webDataID)
	{
		long currtime = System.currentTimeMillis();
		feedUpdateTimeStamp.put(webDataID, currtime);

		for (Map.Entry<String, Long> entry : feedUpdateTimeStamp.entrySet()) {
			String webdataid = entry.getKey();
			Long timestamp = (currtime - entry.getValue())/60000;
			if(timestamp >= WebDataUtils.webdata_min_interval){ //checking for >= 15 mins interval
				feedUpdateTimeStamp.remove(webdataid);
			}
		}
	}

	public String getSheetNameFromId(long sheetId){
		if(!documentSheetsTable.containsKey(sheetId)){
			documentSheetsTable = DocumentUtils.getDocumentSheetTable(Long.valueOf(docId), docOwner);
		}
		return documentSheetsTable.get(sheetId);
	}

	public void updateDocumentSheetsTable(String sheetName, String newSheetName)
	{
		for(Map.Entry<Long, String> entry : documentSheetsTable.entrySet())
		{
			if(entry.getValue().equals(sheetName))
			{
				documentSheetsTable.replace(entry.getKey(), newSheetName);
			}
		}
	}

	public void deleteFromDocumentSheetsTable(String sheetName)
	{
		for(Map.Entry<Long, String> entry : documentSheetsTable.entrySet())
		{
			if(entry.getValue().equals(sheetName))
			{
				documentSheetsTable.remove(entry.getKey());
			}
		}
	}

	public String getLastActionPerformer()
	{
		return lastActionPerformer;
	}
	public String getLastActionPerformerZUID() {
		return lastActionPerformerZUID;
	}

	public void rename(String newDocName) throws Exception
	{
		rename(newDocName, getDocOwner());
	}
	public void rename(String newDocName, String userName) throws Exception
	{
		DocumentUtils.renameDocument(this, newDocName);
		this.docName = newDocName;
		//this.docUrl = String.valueOf(DocumentUtils.getDocumentDetails(docId).get("DOCUMENT_NAME_URL"));  //No I18N
		//////////////
		changeIsNewDocStatus(userName);
		/////////////
		//SyncUpdater.enQ("rename",String.valueOf(docId),docName,docOwner,docOwner,System.currentTimeMillis(),"0");//No I18N

	}


	public enum LocaleAdapter{
		NL("nl"), // Netherlands //No I18N
		CZ("cs"), // Czech Republic //No I18N
		CH("fr"); // Switzerland //No I18N

		private final String language;
		private LocaleAdapter(String language) {
			this.language = language;
		}

		public String getLanguage(){
			return language;
		}

		public String getCountry(){
			return this.name();
		}
	}

	public WorkbookAdditionalInfo getWorkbookAdditionalInfo()
	{
		Workbook thisWorkbook = this.getWorkbookForSave();

		if(thisWorkbook != null && thisWorkbook.isReadyToRender())
		{
			return WorkbookAdditionalInfo.getInstance(thisWorkbook);
		}
		try
		{
			if(RemoteUtils.isRemoteDocument(this.docOwner) &&!isAuthRemote())// Only for RemotAPI
			{
				String tempDocLocale = EngineUtils1.readSheetMetaInfo(this).getProperty("REMOTE_DOC_LOCALE");
				return tempDocLocale != null ? WorkbookAdditionalInfo.getInstance(tempDocLocale) : WorkbookAdditionalInfo.getInstance(EngineConstants.DEFAULT_LOCALE);
			}
			else
			{
				return WorkbookAdditionalInfo.getInstance(this);
			}
		}
		catch(Exception e)
		{
			logger.log(Level.WARNING, "[SPREADSHEET_SETTINGS][Exception] Exception while retrieving spreadsheet settings", e);
			User user = this.getCreatorIAMUser();
			if(user != null)
			{
				logger.log(Level.WARNING, "[SPREADSHEET_SETTINGS][Exception] Generating spreadsheet settings based on IAM User");
				return WorkbookAdditionalInfo.getInstance(user);
			}
			logger.log(Level.WARNING, "[SPREADSHEET_SETTINGS][Exception] Returning Default Spreadsheet Settings");
			return WorkbookAdditionalInfo.getInstance(EngineConstants.DEFAULT_LOCALE);
		}
	}

	public void setHighlightInvalidCell(boolean highlightInvalidCell)
	{
		if (highlightInvalidCell)
		{
			this.highlightInvalidCell++;
		} else
		{
			if (this.highlightInvalidCell > 0)
			{
				this.highlightInvalidCell--;
			}
		}
	}

	public boolean getHighlightInvalidCell()
	{
		return (this.highlightInvalidCell > 0);
	}

	public Workbook getWorkbookForSave()
	{
		return (Workbook)books.get(BookType.WORKBOOK);
	}

	public ImageBook getImageBookForSave()
	{
		return (ImageBook)books.get(BookType.IMAGEBOOK);
	}

	public Workbook getWorkbook(String zfsngVersionNo) throws Exception
	{

		Workbook workbook =  getWorkbook(zfsngVersionNo, false);
		return workbook;
	}
	// Pass null if you do not want any version
	public Workbook getWorkbook(String zfsngVersionNo,boolean ignorePendingActions) throws Exception
	{
		Workbook workbook = (Workbook)this.getBook(BookType.WORKBOOK, zfsngVersionNo, ignorePendingActions);
		return workbook;
	}

	public ImageBook getImageBook(String zfsngVersionNo) throws Exception
	{
		return (ImageBook)this.getBook(BookType.IMAGEBOOK, zfsngVersionNo, true);
	}

	public void addToVersionBooksMap(String zfsngVersionNo) throws Exception
	{
		if(!versionBooksMap.containsKey(zfsngVersionNo))
		{
			if(versionBooksMap.size() >= 3)
			{
				logger.log(Level.INFO, "version workbook hashtable size before remove :: {0}", versionBooksMap.size());
				versionBooksMap.remove(versionBooksMap.keys().nextElement());
				logger.log(Level.INFO, "version workbook hashtable size after remove :: {1}", versionBooksMap.size());
			}
			if(!this.isReadOnly())
			{
				Map verBooks = new HashMap();
				verBooks.put(BookType.WORKBOOK, this.getWorkbook(zfsngVersionNo));
				verBooks.put(BookType.IMAGEBOOK, this.getImageBook(zfsngVersionNo));
				versionBooksMap.put(zfsngVersionNo, verBooks);
			}
		}
	}

	public boolean hasWorkbook(String zfsngVersionNo)
	{
		if(zfsngVersionNo == null)
		{
			return books.get(BookType.WORKBOOK) != null;
		}

		return versionBooksMap.containsKey(zfsngVersionNo);
	}

	//default value for overrideIsReadyToRender = false
	public void setWorkbook_New(Workbook inWorkBook, boolean isCleanUp, boolean overrideIsReadyToRender) throws Exception
	{
		if(EngineConstants.IS_WORKBOOK_DISMANTLE_ENABLED)
		{
			try
			{
				Workbook existingWorkbook = this.getWorkbookForSave();
				if(existingWorkbook != null && existingWorkbook != inWorkBook)
				{
					existingWorkbook.dismantle();
				}
			}catch(Exception e)
			{

			}
		}

		setWorkbook(inWorkBook, isCleanUp, overrideIsReadyToRender);
	}

	//default value for overrideIsReadyToRender = false
	public void setWorkbook(Workbook inWorkBook, boolean isCleanUp, boolean overrideIsReadyToRender) throws Exception
	{
		// This can happen only when workBookSnapShot is being sent from UndoRedoManager in case of undo failure.
		if(inWorkBook != null && inWorkBook.isDismantled())
		{
			inWorkBook = null;
		}
		/////////

		this.setBook(BookType.WORKBOOK, inWorkBook, isCleanUp, overrideIsReadyToRender);
	}

	public Workbook resetWorkbook() throws Exception
	{
		Workbook workBook = new Workbook();

		this.setWorkbook(workBook, false, true);
		return workBook;
	}

	public void setImageBook(ImageBook imageBook) throws Exception
	{
		this.setBook(BookType.IMAGEBOOK, imageBook, false, true);
		if(imageBook != null)
		{
			imageBook.setIsImagesChanged(true);
		}
	}

	public boolean removeVersionBooks(String zfsngVersionNo)
	{
		if(zfsngVersionNo == null)
		{
			return false;
		}
		return (versionBooksMap.remove(zfsngVersionNo) != null);
	}

	public void removeAllVersionBooks() {
		versionBooksMap.clear();
	}
	public boolean isTemplate() throws Exception
	{
		if(!this.isRemoteMode()) {
			return DocumentUtils.isTemplate(this.getResourceId(), this.getDocOwnerZUID());
		}else {
			return false;
		}
	}

	public boolean isPublicTemplate()
	{
		return "template".equals(this.getDocsSpaceId()); // No I18N
	}

	private void writeUnVersionedActions(List<JSONObjectWrapper> vactions) throws Exception{
		try {
			if (vactions != null && !vactions.isEmpty()) {
				long resourceId = this.getFragmentId(FileName.UNVERSIONEDACTIONS, true, null);
				Map writeInfo = this.getWriteInfo(resourceId, FileName.UNVERSIONEDACTIONS, FileExtn.JSON, null);
				PrintWriter writer = new PrintWriter((OutputStream) writeInfo.get("OS"));
				try
				{
					for (Object action : vactions) {
						writer.println(action);
					}
				} finally {
					writer.close();
					this.finishWrite(writeInfo);
				}

			}
		}
		catch (Exception e)
		{
			logger.log(Level.INFO, "Error while writing unversioned Actions, ", e);
		}
	}

	private List<JSONObjectWrapper> getUnVersionedActions() throws Exception
	{
		try {
			long resourceId = this.getFragmentId(FileName.UNVERSIONEDACTIONS, false, null);
			return EngineUtils1.loadActionsList(this, resourceId, FileName.UNVERSIONEDACTIONS);
		}catch (Exception e)
		{
			logger.log(Level.INFO, "Error while loading unversioned Actions, ", e);
			throw  e;
		}
	}
	//	// This should be called only from Save schedulers.
//             public boolean saveonNewContainer(String label, String loginName, boolean isDocSaveRequired) //throws Exception
//             {
//                 return saveByActionId(label, loginName, isDocSaveRequired, -1);
//             }
	// the label here is user given where he wants to save the version with his annotation
	private boolean save(String label, String loginName, boolean isDocSaveRequired, boolean isSaveAsOneFile, boolean isForceSave) //throws Exception
	{
		if (isReadOnly())
		{
			logger.log(Level.INFO, "RESOURCE_ID: {0} Document not saved since it is a readonly container. This save call is rejected.", this.getResourceKey());
			return false;
		}

		if (checkAndCreateSavingState() && label == null)
		{
			logger.log(Level.INFO, "RESOURCE_ID: {0} Document not saved as the save is already in progress. This save call is rejected.", this.getResourceKey());
			return false;
		}

		// Update Complexity Level of the workbook for every save
		if (!RemoteUtils.isRemoteDocument(this.docOwner) && !this.isPublicTemplate()) {
			updateSpreadsheetComplexity();
		}

		// DocumentUtils.triggerDocChangeNotification(this);

		long t1 = System.currentTimeMillis();

		if(writeLock.tryLock()){
			Workbook workbook = null;
			try {
				// This lock should be inside try
				logger.log(Level.INFO, "writeLock : locking : save : {0}", this.getResourceKey());
				ExternalRangeManager.setIsPauseStartupSync(this.externalRangeManager, true);

				// Do the save for Chat here
				if (isChatSaveRequired()) {
					// TODO: code here to save the chat and the method save should have the Exception handling
					ChatUtils.moveChatInfoToDb(getResourceKey(),this.docId,this.docOwner);
					// If there is only need of saving the Chat alone then
					// there is no need to call of the save of the document and we can return
					// from here. As of now isDocSaveRequired will be false if saved from EngineFactory-SaveWorkbookScheduler
					if (!isDocSaveRequired) {
						return true;
					}
				}

				//setTransientXActionId(getExecutedActionId());
				List<JSONObjectWrapper> allActions = RedisHelper.loadActionsList(this.getResourceKey(), -1, -1);
				boolean containsOnlyApiActions = true;

				boolean containsOnlyReEvaluateImportrangeActions = !isForceSave && !isSaveAsOneFile && label == null && allActions.size() > 0 && allActions.size() < 100;
				if(containsOnlyReEvaluateImportrangeActions) {
					long lastSavedTime = DocumentUtils.getLastSavedTime(docOwnerZUID, this.getResourceKey());
					if(lastSavedTime == -1l || ((System.currentTimeMillis()-lastSavedTime) > 60*60*1000)) {
						containsOnlyReEvaluateImportrangeActions = false;
					}
				}
				for (JSONObjectWrapper jsonObject : allActions) {
					if(containsOnlyReEvaluateImportrangeActions && jsonObject.getInt(JSONConstants.ACTION) != ActionConstants.RE_EVALUATE_IMPORTRANGE) {
						containsOnlyReEvaluateImportrangeActions = false;
					}
					if(!jsonObject.has(JSONConstants.API_ACTION)) {
						containsOnlyApiActions = false;
					}
				}
				if(containsOnlyReEvaluateImportrangeActions) {
					logger.log(Level.OFF, "{0} not saved as it contains only importrange actions", this.getResourceKey());
					try{
						RedisHelper.zrem(RedisHelper.SAVE_SCHEDULER_WORKBOOKS, this.getResourceKey());
					} catch(Exception e) {
						logger.log(Level.OFF, String.join("", "issue in removing SAVE_SCHEDULER_WORKBOOKS for ",this.getResourceKey()), e);
					}
					return true;
				}

				transientVersionAuthorsList = new ArrayList<>(getVersionAuthorsList());
				if (transientVersionAuthorsList.size() > 20) {
					transientVersionAuthorsList = transientVersionAuthorsList.subList(0, 20);
				}
				/// clear the list here
				this.versionAuthorsList.clear();

				//Removing invalid shared users info in protection objects
				CellProtectionUtil.validateSharedUsers(this);

				/////////////////
				//getworkbook() should be called before getting executedActionId since we use this id to move to actions to vActions list
				workbook = this.getWorkbook(null);
				if(workbook != null){
					workbook.lockDismantle();
					logger.log(Level.INFO, "StylePropertiesPool size >>> {0}{1}", new Object[]{workbook.stylePropertiesPool.size(), (workbook.stylePropertiesPool.size() > EngineConstants.MAX_STYLE_PROPERTIES_POOL_SIZE*0.7) ? ", StylePropertiesPool limit almost reached" : ""});
				}

				//////////////////

				//save prison check moved after getworkbook, since it may be to prison while executing actions in getWorkbook
				if(!isForceSave) {
					try {
						if (RedisHelper.hexists(RedisHelper.SAVE_PRISONED_WORKBOOKS, this.getResourceKey())) {
							logger.log(Level.INFO, "RESOURCE_ID: {0} Document not saved as it is in prison ;) . This save call is rejected.", this.getResourceKey());
							return false;
						}
					} catch (Exception e) {
						logger.log(Level.WARNING, "REDIS: Error while getting prisioned workbooks... KEY :: " + this.getResourceKey(), e);
					}
				}

				int lastExecutedActionId = this.getExecutedActionId();
				List<JSONObjectWrapper> xList = getUnVersionedActions();
				xList.addAll(RedisHelper.loadActionsList(this.getResourceKey(), -1, lastExecutedActionId));
				boolean isMakeVersion = ((label != null) || (xList.size() > 0)) && !isSaveAsOneFile; // true if label is not null

				WorkbookContainer saveContainer = this;

				EngineUtils1.updateWorkbookFromRedis(saveContainer, true);
				boolean isSaved;
				if(isSaveAsOneFile) {
					isSaved = executeAroundWriter(EngineConstants.FILENAME_DOCUMENT, EngineConstants.FILEEXTN_ZSHEET, outputStream -> {
						try(ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream)) {
							logger.log(Level.SEVERE, "\uD83D\uDDC2️[WRITE-FORMAT-INKLING][ZSHEET]Writing Workbook to zsheet for resource : " + resourceId);
							Workbook workbookForSave = this.getWorkbookForSave();
							WorkbookToZS workbookToZS = new WorkbookToZS(workbookForSave);
							EngineUtils1.writeZSHEET(workbookForSave, this.getImageBookForSave(), zipOutputStream, workbookToZS);
							updateSearchFileInDocStore(workbookToZS.getAsn_contentSet_map(), workbookForSave);
						} catch (Exception e) {
							logger.log(Level.SEVERE, "\uD83D\uDDC2️[WRITE-FORMAT-INKLING][ZSHEET]Writing Workbook to zsheet failed for resource : " + resourceId, e);
							return false;
						}
						return true;
					});
				} else {
					isSaved = EngineUtils1.saveAsNewZSheetFragments(saveContainer, isForceSave);
				}
				// TODO: need to check if we need to version in REMOTE case
				if (isSaved)
				{
					// Clearing the styleProperties Cache.
					try
					{
						if(this.getWorkbookForSave() != null)
						{
							this.getWorkbookForSave().stylePropertiesPool.clearCache();
						}
					}catch(Exception e)
					{
						logger.log(Level.INFO, "Error while clearing style properties pool...");
					}
					/////////

					if(!isRemoteMode())
					{
						if (isMakeVersion || isForceSave) {
							try
							{
								if (label != null)
								{
									EngineUtils1.versionDocument(saveContainer, "MANUAL", label, loginName, xList, true, containsOnlyApiActions); // No I18N
								} else
								{
									EngineUtils1.versionDocument(saveContainer, "AUTOMATIC", loginName != null ? "user-save" : "auto-version", loginName, xList, true, containsOnlyApiActions); // No I18N
								}
							}catch (Exception e) {
								// NO need to throw error while AUTOMATIC verioning as we have written the file to unversioned file, we consider save to be success.
								// This is done to avoid files adding and remaining in prison, when there are some unsaved actions but user deltes the file.
								// In this case, we will be able to write fragments but version will fail.
								logger.log(Level.INFO, "Unable to create Version... So writing actiones in Unversioned actions file");
								writeUnVersionedActions(xList);
								if (label != null)
								{
									throw e;
								}
							}
							finally
							{
								if(!xList.isEmpty())
								{
									long startActionId = xList.get(0).getInt(JSONConstants.ACTION_ID);
									RedisHelper.removeFromActionsList(this.getResourceKey(), startActionId, lastExecutedActionId);
								}
							}
						}
					}
					else
					{
						//Clearing xlist for remote case alone as for other workbooks it will be cleared in versionDocument itself
						RedisHelper.removeFromActionsList(this.getResourceKey(), -1, lastExecutedActionId);
					}
					if(RedisHelper.hlen(RedisHelper.ACTIONS_LIST + getResourceKey()) == 0)
					{
						RedisHelper.removeSaveRelatedKeys(getResourceKey());
					}

					//Limiting X actions cache list to 200
					RedisHelper.limitXActionsCacheList(getResourceKey());
					if(this.getWorkbookForSave() != null)
					{
						if(this.getWorkbookForSave().noOfCellsCreatedAfterParse > 2000000)
						{
							logger.log(Level.INFO, "too many cells created after parsing... {0} - cells count : {1}", new Object[]{this.getResourceKey(), this.getWorkbookForSave().noOfCellsCreatedAfterParse});
							//logger.log(Level.INFO, "setting ISRESET true as too many cells created after parsing... {0} - cells count : {1}", new Object[]{this.getResourceKey(), this.getWorkbookForSave().noOfCellsCreatedAfterParse});
							//this.isResetWorkbook = true;
						}

						if(this.getWorkbookForSave().noOfTempStylesCreated > 300)
						{
							logger.log(Level.INFO, "too many temp Styles created after parsing... {0} - tempCStyle Count : {1}", new Object[]{this.getResourceKey(), this.getWorkbookForSave().noOfTempStylesCreated});
							//logger.log(Level.INFO, "setting ISRESET true as too many temp Styles created after parsing... {0} - tempCStyle Count : {1}", new Object[]{this.getResourceKey(), this.getWorkbookForSave().noOfTempStylesCreated});
							//this.isResetWorkbook = true;
						}
					}
				}
				else
				{
//                        if (EngineConstants.IS_SAVE_ON_NEW_SERVER)
					if(!isRemoteMode() && isSaveServer() && xList.size() > 0)
					{
						logger.log(Level.INFO, "File not saved : moving to prison : {0}", this.getResourceKey());
						RedisHelper.zrem(RedisHelper.UNSAVED_WORKBOOKS, resourceId);
						RedisHelper.addToSavePrison(resourceId, 0);
					}
				}
				return isSaved;
			} catch (Exception ex) {
				String errorMsgKey = ex.getMessage();
				String[] params = null;
				if(ErrorCode.ERROR_DOCUMENT_SAVE_SIZEEXCEED.equals(errorMsgKey)){
					params = new String[3];
					params[0] = Utility.MAXNUMOFROWS+"";
					params[1] = Utility.MAXNUMOFCOLS+"";
					params[2] = Utility.CELLCOUNTLIMIT/1000000 + "";    // Total cells - 5 million
				}

				JSONObjectWrapper errorMsgObj = ErrorCode.getErrorMessage(errorMsgKey, null, ErrorCode.MsgType.ERROR, ErrorCode.DisplayType.BANNER, params);
				logger.log(Level.INFO, "[ZS_ERROR_BANNER][WORKBOOK_CONTAINER][{0}][RID:{1}]", new Object[]{errorMsgKey, this.getResourceKey()});
				// its a instance of Throwable // handle to send error message to all users
				if (!this.getUserProfileMap().isEmpty()) {
					String senderName = this.getFirstUserProfile().getUserName(); // get the name of first user
					try {
						MessagePropagator.sendMessage(this.getCollabId(), senderName, errorMsgObj);
					} catch (Exception ex1) {
						logger.log(Level.SEVERE, null, ex1);
					}
				}
				//loginName = loginName!=null?loginName:"System";
				logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} >>> Error on save document.", new Object[]{this.getResourceKey(), loginName});
				logger.log(Level.WARNING, "ERROR RESOURCE_ID: " + this.getResourceKey()+ " Error on save document : ", ex);
				if(!isRemoteMode())
				{
					try{
						double expCount = RedisHelper.zincrby(RedisHelper.EXCEPTION_COUNTER, 1, getResourceKey());
//                            if (EngineConstants.IS_SAVE_ON_NEW_SERVER)
						if(isSaveServer())
						{
							RedisHelper.zrem(RedisHelper.UNSAVED_WORKBOOKS, resourceId);
							RedisHelper.addToSavePrison(resourceId, expCount);
						}
					}catch(Exception e){
						logger.log(Level.WARNING, "REDIS : Exception while adding value to EXCEPTION_COUNTER ");
					}
				}
			} finally {
				ExternalRangeManager.setIsPauseStartupSync(this.externalRangeManager, false);

				try {
					if(workbook != null){
						workbook.unLockDismantle();
						if(workbook.getDismantleAttempts() > 0) {
							workbook.dismantle();
						}
					}

					//                        RedisHelper.srem(RedisHelper.SAVING_WORKBOOKS, getResourceKey());
					RedisHelper.zrem(RedisHelper.SAVING_WORKBOOKS, getResourceKey());

				} catch (Exception ex) {
					logger.log(Level.WARNING, "Exception while removing resourceId from SAVING_WORKBOOKS list :: resoucreId :: {0} ::: {1}", new Object[]{getResourceKey(), ex});
				}
				if (EngineConstants.IS_LOGGER_ENABLED) {
					long t2 = System.currentTimeMillis();
					logger.log(Level.INFO, "[SAVE] TIME TAKEN : {0} :: RESOURCE_ID : {1} :: RID : {2}", new Object[]{t2 - t1, this.getResourceKey(), this.getResourceId()});
				}
				writeLock.unlock();
				logger.log(Level.INFO, "writeLock : Unlocking : save : {0}", this.getResourceKey());
			}
		}
		else {
			logger.log(Level.INFO, "writeLock: held by other thread : so not saving : {0}", this.getResourceKey());
		}
		return false;
	}

//	    public boolean saveFromAdminByActions(String label, String loginName, boolean isDocSaveRequired,int noOfActions) //throws Exception
//	    {
//
//            if (isReadOnly())
//            {
//                logger.log(Level.INFO, "RESOURCE_ID: {0} Document not saved since it is a readonly container. This save call is rejected.", this.getResourceKey());
//                return false;
//            }

	//	    public boolean saveFromAdminByActions(String label, String loginName, boolean isDocSaveRequired,int noOfActions) //throws Exception
//	    {
//
//            if (isReadOnly())
//            {
//                logger.log(Level.INFO, "RESOURCE_ID: {0} Document not saved since it is a readonly container. This save call is rejected.", this.getResourceKey());
//                return false;
//            }
//
//            try{
//                if (RedisHelper.hexists(RedisHelper.SAVE_PRISONED_WORKBOOKS, this.getResourceKey()))
//                {
//                    logger.log(Level.INFO, "RESOURCE_ID: {0} Document not saved as it is in prison ;) . This save call is rejected.", this.getResourceKey());
//                    return false;
//                }
//            }catch(Exception e)
//            {
//                logger.log(Level.WARNING, "REDIS: Error while getting prisioned workbooks... KEY :: "+this.getResourceKey(), e);
//            }
//
//            if (checkAndCreateSavingState() && label == null)
//            {
//                logger.log(Level.INFO, "RESOURCE_ID: {0} Document not saved as the save is already in progress. This save call is rejected.", this.getResourceKey());
//                return false;
//            }
//
//
//
//            long t1 = System.currentTimeMillis();
//
//            try {
//            	// This lock should be inside try
//                logger.log(Level.INFO, "writeLock : locking : saveFromAdminByActions : {0}", this.getResourceKey());
//                writeLock.lock();
//
//                // Do the save for Chat here
//                if (isChatSaveRequired()) {
//                // TODO: code here to save the chat and the method save should have the Exception handling
//                     ChatUtils.moveChatInfoToDb(getResourceKey(),this.docId);
//                    // If there is only need of saving the Chat alone then
//                    // there is no need to call of the save of the document and we can return
//                    // from here. As of now isDocSaveRequired will be false if saved from EngineFactory-SaveWorkbookScheduler
//                    if (!isDocSaveRequired) {
//                        return true;
//                    }
//                }
//
//                boolean isMakeVersion = (label != null); // true if label is not null
//
//                if (!isMakeVersion) {
//                    //isMakeVersion = (this.getExecutedActionId() > this.getLastSavedActionId());
//                    isMakeVersion = RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + this.getResourceKey()) > 0;
//                }
//                //setTransientXActionId(getExecutedActionId());
//
//                transientVersionAuthorsList = new ArrayList<String>(getVersionAuthorsList());
//                if (transientVersionAuthorsList.size() > 20) {
//                    transientVersionAuthorsList = transientVersionAuthorsList.subList(0, 20);
//                }
//                /// clear the list here
//                this.versionAuthorsList.clear();
//
//                //Removing invalid shared users info in protection objects
//                CellProtectionUtil.validateSharedUsers(this);
//                long xListSize = noOfActions;
//                boolean b = EngineUtils1.saveDocument(this);
//                // TODO: need to check if we need to version in REMOTE case
//                if (!isRemoteMode() && b) {
//
//                    if (!isNewDoc() && isMakeVersion) {
//
//                        if (label != null) {
//                            EngineUtils1.versionDocument(this, "MANUAL", label, loginName, xListSize); // No I18N
//                        } else {
//                            label = loginName != null ? "user-save" : "auto-version";				//No I18N
//                            EngineUtils1.versionDocument(this, "AUTOMATIC", label, loginName, xListSize); // No I18N
//                        }
//                    }
//                    long originalXListSize = RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + this.getResourceKey());
//                    if(originalXListSize == 0){
//                    	RedisHelper.zrem(RedisHelper.UNSAVED_WORKBOOKS, this.getResourceKey());
//                    }
//                    RedisHelper.zrem(RedisHelper.EXCEPTION_COUNTER, this.getResourceKey());
//                    RedisHelper.zrem(RedisHelper.SAVE_SCHEDULER_WORKBOOKS, this.getResourceKey());
//                    RedisHelper.hdel(RedisHelper.SAVE_PRISONED_WORKBOOKS, this.getResourceKey());
//                    RedisHelper.hdel(RedisHelper.SHARE_REMOVE_INFO, this.getResourceKey());
//
//                }
//                return b;
//            } catch (Exception ex) {
//
//                String errorMsgKey = ex.getMessage();
//                String[] params = null;
//                if(ErrorCode.ERROR_DOCUMENT_SAVE_SIZEEXCEED.equals(errorMsgKey)){
//                        params = new String[3];
//                        params[0] = Utility.MAXNUMOFROWS+"";
//                        params[1] = Utility.MAXNUMOFCOLS+"";
//                        params[2] = "2";    // Total cells - 2 million
//                }
//
//                JSONObjectWrapper errorMsgObj = ErrorCode.getErrorMessage(errorMsgKey, null, ErrorCode.MsgType.ERROR, ErrorCode.DisplayType.BANNER, params);
//                logger.log(Level.INFO,"[ZS_ERROR_BANNER][WORKBOOK_CONTAINER]["+errorMsgKey+"][RID:"+this.getResourceKey()+"]");
//                // its a instance of Throwable // handle to send error message to all users
//                if (this.getUserProfileList().size() > 0) {
//                    String senderName = this.getUserProfileList().get(0).getUserName(); // get the name of first user
//                    try {
//                        MessagePropagator.sendMessage(this.getCollabId(), senderName, errorMsgObj);
//                    } catch (Exception ex1) {
//                        logger.log(Level.SEVERE, null, ex1);
//                    }
//                }
//                //loginName = loginName!=null?loginName:"System";
//                logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} >>> Error on save document.", new Object[]{this.getResourceKey(), loginName});
//                logger.log(Level.WARNING, "ERROR RESOURCE_ID: " + this.getResourceKey()+ " Error on save document : ", ex);
//                if(!isRemoteMode())
//                {
//                    try{
//                        RedisHelper.zincrby(RedisHelper.EXCEPTION_COUNTER, 1, getResourceKey());
//                    }catch(Exception e){
//                        logger.log(Level.WARNING, "REDIS : Exception while adding value to EXCEPTION_COUNTER ");
//                    }
//                }
//            } finally {
//
//                try {
////                    RedisHelper.srem(RedisHelper.SAVING_WORKBOOKS, getResourceKey());
//                    RedisHelper.zrem(RedisHelper.SAVING_WORKBOOKS, getResourceKey());
//
//                } catch (Exception ex) {
//                    logger.log(Level.WARNING, "Exception while removing resourceId from SAVING_WORKBOOKS list :: resoucreId :: {0} ::: {1}", new Object[]{getResourceKey(), ex});
//                }
//                if (EngineConstants.IS_LOGGER_ENABLED) {
//                    long t2 = System.currentTimeMillis();
//                    logger.log(Level.INFO, "[SAVE] TIME TAKEN : {0} :: RESOURCE_ID : {1} :: RID : {2}", new Object[]{t2 - t1, this.getResourceKey(), this.getResourceId()});
//                }
//                writeLock.unlock();
//                logger.log(Level.INFO, "writeLock : Unlocking : saveFromAdminByActions : {0}", this.getResourceKey());
//            }
//            return false;
//	    }
//
	public void syncAllUserProfileSessions()
	{
		try
		{
			Hashtable collabUserTable = CollaborationApi.getSessions(this.getCollabId());
			//Iterator keysItr = collabUserTable.keySet().iterator();
			Iterator<Map.Entry<String, UserProfile>> itr = this.userProfileMap.entrySet().iterator();
			while(itr.hasNext())
			{
				Map.Entry<String, UserProfile> entry = itr.next();
				String zUserId = entry.getKey();
				UserProfile userProfile = entry.getValue();
				ArrayList wmsUserSessions = (ArrayList) collabUserTable.get(zUserId);

				//If WMS has no session for an user, we can remove the user profile.
				if(wmsUserSessions == null) {
					itr.remove();
				} else {

					String[] sheetUserSessions = userProfile.getWmsRawSessionIds();

					for(int j = 0; j < sheetUserSessions.length; j++)
					{
						if(!wmsUserSessions.contains(sheetUserSessions[j])) // session removed from the WMS Server
						{
							//removeUserProfile(userProfile, sheetUserSessions[j]);
							removeUserProfileUsingRSID(userProfile, sheetUserSessions[j]);
						}
					}
				}

			}

		}
		catch(Exception ex)
		{
			logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: Error while cleaning up user list. Exception :: {1}", new Object[]
					{
							this.getResourceKey(), ex
					});

		}

	}


//	public void writeSheetMetaInfo(WidgetInfo widgetInfo) throws Exception
//	{
//		logger.log(Level.INFO, "writeLock : locking : writeSheetMetaInfo : {0}", this.getResourceKey());
//		writeLock.lock();
//		try
//		{
//			EngineUtils1.writeSheetMetaInfo(this, widgetInfo,null);
//		}
//		finally
//		{
//			writeLock.unlock();
//			logger.log(Level.INFO, "writeLock : Unlocking : writeSheetMetaInfo : {0}", this.getResourceKey());
//		}
//	}

	public Map<String, UserProfile> getUserProfileMap()
	{
		return Collections.unmodifiableMap(this.userProfileMap != null ? this.userProfileMap : new ConcurrentHashMap<>());
	}

	public UserProfile getFirstUserProfile()
	{
		Map<String, UserProfile> userProfileMap = this.getUserProfileMap();
		if (userProfileMap != null && !userProfileMap.isEmpty())
		{
			return userProfileMap.values().iterator().next();
		}
		return null;
	}

	public void addUserProfile(UserProfile inUserProfile)
	{
		// The inUserProfile.getZUserId() comes null in some logout cases where redirection to login page
		// does not happen properly
		if(inUserProfile.getZUserId() != null
				&& getUserProfile(inUserProfile.getZUserId()) == null)
		{
			userProfileMap.put(inUserProfile.getZUserId(), inUserProfile);

		}

	}

	public void registerListener(ContainerListener listener)
	{
		if(listenerList == null)
		{
			listenerList = new ArrayList<>();
		}
		listenerList.add(listener);
	}

	public ContainerListener getListener(int id)
	{
		ContainerListener containerListener = null;
		if(listenerList != null)
		{
			for(ContainerListener listener : listenerList)
			{
				if(listener.getId() == id)
				{
					containerListener = listener;
					break;
				}
			}
		}
		return containerListener;
	}

	public void removeListener(ContainerListener containerListener)
	{
		if(containerListener != null)
		{
			listenerList.remove(containerListener);
		}
	}

	public void registerToCollaboration(UserProfile inUserProfile, String tabKey, String wmsRawSessionId, String wmsSessionId, TabType tabType, ResponseObject.ResponseVersion responseVersion) throws Exception
	{
		//logger.log(Level.INFO, "registerToCollaboration for wmsRawSessionId: {0}", wmsRawSessionId);
		//boolean isWmsRawSessionIdAvailable = inUserProfile.addWmsRawSessionId(wmsRawSessionId,tabType);
		//logger.log(Level.INFO, "isWmsRawSessionIdAvailable: {0}" , isWmsRawSessionIdAvailable);
		///////////////
		inUserProfile.addNewTabInfo(tabKey, wmsRawSessionId, tabType, responseVersion);
		addUserToCollaboration(inUserProfile, wmsSessionId, wmsRawSessionId); // use sid
	}

//	     private UserProfile getUserProfile(String zUserId, String wmsRawSessionId)
//	     {
//                 UserProfile userProfiles[] = userProfileList.toArray(new UserProfile[]{});
//	    	 for(UserProfile userProfile : userProfiles)
//	    	 {
//	    		 if(userProfile.getZUserId().equals(zUserId))
//	    		 {
//	    			 if(userProfile.isWmsRawSessionIdAvailable(wmsRawSessionId))
//	    			 {
//	    				 return userProfile;
//	    			 }
//	    			 break;
//	    		 }
//	    	 }
//
//	    	 return null;
//	     }


	public boolean isReadOnly(){
		return isReadOnly;
	}

	public boolean isShowFormulas()
	{
		return this.showFormulas;
	}

	public void setShowFormulas(boolean isShow)
	{
		this.showFormulas = isShow;
	}

	public boolean removeUserProfile(String zUserId, String tabKey)
	{
		UserProfile userProfile = getUserProfile(zUserId);
		return removeUserProfile1(userProfile, tabKey);
	}

	private boolean removeUserProfile1(UserProfile userProfile, String tabKey)
	{
		boolean b = false;
		if(userProfile != null)
		{
			//b = userProfile.removeWmsRawSessionId(wmsRawSessionId, this, true);
			b = userProfile.closeTab(tabKey, this.getResourceKey());
			//TODO : Below Api is not working. Reported to wms.
			// remove the session id from Collab - WMS
			//				try{
			//					CollaborationApi.deleteMemberSession(zUserId, collabId, zUserId, wmsRawSessionId);
			//				}catch(WMSException wse)
			//				{
			//					logger.log(Level.WARNING, null, wse);
			//				}
			// if there are no raw session id, lets remove the userprofile object
			//if(userProfile.getWmsRawSessionIds().length == 0)
			//CloseDoc is sent via sendBeacon; So close might reach after workbook open on reload;
			//For this case, we should not remove userprofile
//                    if(userProfile.isTabInfoMapEmpty())
//                    {
//                        userProfileList.remove(userProfile);
//                    }
		}
		return b;
	}

	private void removeUserProfileUsingRSID(UserProfile userProfile, String wmsRawSessionId)
	{
		if(userProfile != null)
		{
			userProfile.closeTabUsingRSID(wmsRawSessionId, this.getResourceKey());
			if(userProfile.isTabInfoMapEmpty())
			{
				this.userProfileMap.remove(userProfile.getZUserId());
			}
		}
	}

	public void removeUserProfile(UserProfile userProfile)
	{
		//String[] wmsRawSessionIds = userProfile.getWmsRawSessionIds();
		//for(int i = 0; i < wmsRawSessionIds.length; i++)
		//{
		//    userProfile.removeWmsRawSessionId(wmsRawSessionIds[i], this, true);
		//}
		userProfile.closeAllTabs(this.getResourceKey());
		userProfileMap.remove(userProfile.getZUserId());
	}

	public void removeOtherUserProfile(UserProfile profile)
	{
		List<UserProfile> userProfiles = new ArrayList<>(userProfileMap.values());
		for(int i = userProfiles.size()-1; i >= 0; i--)
		{
			UserProfile _profile = userProfiles.get(i);
			if(!profile.equals(_profile)){
				removeUserProfile(_profile);
			}
		}
	}

	public void removeAllUserProfile()
	{
		//iterating in reverse ordeas it removes the items from list
		List<UserProfile> userProfiles = new ArrayList<>(userProfileMap.values());
		for(int i = userProfiles.size() - 1; i >= 0; i--)
		{
			removeUserProfile(userProfiles.get(i));
		}
	}

	/*
	 * Shall we give contains implementation here.
	 */
	public UserProfile getUserProfile(String zUserId)
	{
		for(Map.Entry<String, UserProfile> entry : this.getUserProfileMap().entrySet())
		{
			UserProfile userProfile = entry.getValue();
			if(userProfile.getZUserId() != null && userProfile.getZUserId().equals(zUserId))
			{
				return userProfile;
			}
		}
		return null;
	}

	public String getCollabId()
	{
		return this.collabId;
	}

	// returns actionId
//	   public int addActionToQueue_DFS(JSONObjectWrapper actionObject) throws IllegalStateException, Exception
//    {
//        try
//        {
//            if(this.workBook == null)
//            {
//                this.workBook = getWorkbook(null);
//            }
//
//            if(!this.workBook.isReadyToRender())
//            {
//                synchronized(this.workBook)
//                {
//                    try
//                    {
//                        this.workBook.wait();
//                    }
//                    catch(InterruptedException ie)
//                    {
//                        logger.log(Level.WARNING, "ERROR DOCID: {0} >>> InterruptedException in addActionToQueue() method.", new Object[]
//                            {
//                                this.getDocId()
//                            });
//                        logger.log(Level.WARNING, "ERROR DOCID: " + this.getDocId() + " InterruptedException in addActionToQueue() method.", ie);
//                    }
//                }
//            }
//            /////////////
//            try
//            {
//                // TODO: Check if user has the permission to execute the Action
//                // Check if user has the permission to execute the Action
//                // "un" - userName
//                //				System.out.println("q correctaid "+ (actionId + 1));
//                //logger.log(Level.INFO, "locking : {0}", actionObject);
//                writeLock.lock();
//                /////////
//                ActionManager.preProcessor(this.workBook, actionObject);
//                actionObject.set("aid", actionId + 1);  // No I18N
//                actionObject.set(JSONConstants.TIME_STAMP, System.currentTimeMillis());  // TIME_STAMP = "ts"
//                if(CurrentRealm.getAccessIdentity() != AccessType.PUBLIC_EXTERNAL && CurrentRealm.getAccessIdentity() != AccessType.PUBLIC_ORG)
//                {
//                    EngineUtils1.saveActionsTemp(this, actionObject);
//                }
//                actionQueue.add(actionObject);
//                actionId++; // Increment here the actioId after adding to queue
//                /////////
//            }
//            catch(IllegalStateException ise)
//            {
//                // reset the actionId if its not added to queue
//                logger.log(Level.WARNING, "ERROR DOCID: {0} :: USERID : {1} :: RSID : {2} >>> Queue size exceed the permissiable ({3}) limit, current size : {4}. ActionObject :: {5}", new Object[]
//                    {
//                        this.getDocId(), actionObject.get("zuid"), actionObject.get("rsid"), EngineConstants.ACTIONS_QUEUE_SIZE, actionQueue.size(), actionObject
//                    });
//                logger.log(Level.WARNING, "ERROR DOCID: " + this.getDocId() + " Error Queue size exceed the permissiable limit.", ise);
//                throw ise;
//            }
//            catch(Exception ex)
//            {
//                //logger.log(Level.WARNING, "Error in adding action to queue :: ", ex);
//                logger.log(Level.WARNING, "ERROR DOCID: {0} :: USERID : {1} :: RSID : {2} >>> Error in addActionToQueue() method. ActionObject :: {3}", new Object[]
//                    {
//                        this.getDocId(), actionObject.get("zuid"), actionObject.get("rsid"), actionObject
//                    });
//                //				if(ex.getMessage() != null)
//                //                {
//                throw ex;
//                //                }
//                //                throw new Exception(ErrorCode.ERROR_ADD_ACTION_TO_QUEUE, ex);
//            }
//            finally
//            {
//                try
//                {
//                    if(executor == null)
//                    {
//                        executor = new ActionExecutor(this);
//                    }
//                    /////////////
//                    if(!actionQueue.isEmpty() && !isExecuting())
//                    {
//                        setIsExecuting(true);
//                        EngineFactory.getActionExecutorPool().execute(executor);
//                    }
//                }
//                catch(Exception ex1)
//                {
//                    logger.log(Level.WARNING, "ERROR DOCID: {0} :: USERID : {1} :: RSID : {2} >>> Error in addActionToQueue() method. ActionObject :: {3}", new Object[]
//                        {
//                            this.getDocId(), actionObject.get("zuid"), actionObject.get("rsid"), actionObject
//                        });
//                    //logger.log(Level.WARNING, "Error Trace", ex1);
//                    throw ex1;
//                }
//                ///////////////
//                writeLock.unlock();
//            }
//            ///////////
//        }
//        catch(Exception ex)
//        {
//            logger.log(Level.WARNING, ">>>>>>>>>>>>>>>>>>>>ERROR DOCID: {0} :: USERID : {1} :: RSID : {2} >>> Error in addActionToQueue() method. ActionObject :: {3}", new Object[]
//                {
//                    this.getDocId(), actionObject.get("zuid"), actionObject.get("rsid"), actionObject
//                });
//            throw ex;
////				if(ex.getMessage() != null)
////                {
//            //throw ex;
////                }
////                throw new Exception(ErrorCode.ERROR_ADD_ACTION_TO_QUEUE, ex);
//        }
//
//        return actionId;
//    }

	private void addRestrictionToActionObject(JSONObjectWrapper rangeJson, JSONObjectWrapper actionObject) {
		JSONObjectWrapper restrictionJson = new JSONObjectWrapper();
		JSONArrayWrapper rangeArray = new JSONArrayWrapper();
		rangeArray.put(rangeJson);
		restrictionJson.put(JSONConstants.RANGE_RESTRICTION, rangeArray);
		actionObject.put(JSONConstants.RESTRICTION, restrictionJson);
	}

	private void setRestriction(JSONObjectWrapper actionObject) throws Exception {
		String id = actionObject.getString(JSONConstants.RANGE_ID);
		JSONObjectWrapper json = this.getRangeFromID(id);
		if(json == null) {
			json = PublishRangeUtils.getRangeDetailsFromID(this, id);
			if(json.getBoolean(Constants.PUB_RANGE)) {
				this.addIDtoRangeMap(id, json);
				addRestrictionToActionObject(json, actionObject);
			}
		} else {
			addRestrictionToActionObject(json, actionObject);
		}
	}

	public int addActionToQueue(JSONObjectWrapper actionObject) throws IllegalStateException, Exception
	{
		return addActionToQueue(actionObject, null);
	}

	public int addActionToQueue(JSONObjectWrapper actionObject, ContainerListener listener) throws IllegalStateException, Exception
	{
		if(RedisHelper.hexists(RedisHelper.PARSE_PRISONED_WORKBOOKS, this.getResourceKey())) {
			logger.log(Level.INFO, "Engine: not adding action to queue: {0} as workbook is in PARSE_PRISONED_WORKBOOKS", new Object[]{this.getResourceKey()});
			throw new Exception("not adding action to queue as the workbook is in PARSE_PRISONED_WORKBOOKS");//No I18N
		}

		String rs_id = actionObject.has("rsid") ? actionObject.getString("rsid") : null; //NO I18N
		if(actionObject.has(JSONConstants.RANGE_ID)) {
			this.setRestriction(actionObject);
		}

		try
		{

			try
			{
				// TODO: Check if user has the permission to execute the Action
				// Check if user has the permission to execute the Action
				// "un" - userName
				//                System.out.println("q correctaid "+ (actionId + 1));
				//logger.log(Level.INFO, "locking : {0}", actionObject);
				//writeLock.lock();
				/////////
//            		this.workBook.setDocumentId(docId);
//                    ActionManager.preProcessor(this, actionObject, false);
				this.getWorkbook(null); //When workbook is not there while executing action, the action gets performed twice. So calling getworkbook before adding action in queue - Venkat
				synchronized(this) {
					actionObject.set(JSONConstants.TIME_STAMP, System.currentTimeMillis());  // TIME_STAMP = "ts"
					//                    if(CurrentRealm.getAccessIdentity() != AccessType.PUBLIC_EXTERNAL
					//                        && CurrentRealm.getAccessIdentity() != AccessType.PUBLIC_ORG)
					if(!isReadOnly()) //&& !isRemoteMode())
					{
						//EngineUtils1.saveActionsTemp(this, actionObject);
						//                        int actionIdFromRedis = (int)RedisHelper.addToTempActionsList(getResourceKey(), actionObject);++actionId;


						// IMPORTANT :  DO NOT change the order of below 2 lines.
						++actionId;
						RedisHelper.addToActionsList(getResourceKey(), actionObject);
						////////////////


//                            if(actionIdFromRedis != ++actionId) {
//                                logger.log(Level.INFO, "RESOURCE_ID: {0} Mismatch in the ActionID {1} ::: Last RedisActionID ::: {2}, UserName ::: {3}", new Object[]{this.getResourceKey(), actionId, actionIdFromRedis, actionObject.getString("un")});
//                                this.setWorkbook(null, false, false);
//                            }

					}
					else //////////
					{
						// Need to do dummy incr of actionId in case of public documents
						actionObject.set(JSONConstants.ACTION_ID, ++actionId);
					}

					//////////////
					/*if(listener != null)
					{
						listener.setId(actionId);
						this.registerListener(listener);
					}*/
					//////////////

					// TO BE REMOVED
					// Log for Debugging
					int action = actionObject.getInt(JSONConstants.ACTION);
					int actionID = actionObject.getInt(JSONConstants.ACTION_ID);
					//registering listener here
					if(listener != null)
					{
						listener.setId(actionID);
						this.registerListener(listener);
					}

					if (action == ActionConstants.SERVERCLIP_PASTE_SHEET
							|| action == ActionConstants.COPY_PASTE
							|| action == ActionConstants.CUT_PASTE
							|| action == ActionConstants.INSERT_COPY_ROW
							|| action == ActionConstants.INSERT_COPY_COLUMN
							|| action == ActionConstants.INSERT_CUT_ROW
							|| action == ActionConstants.INSERT_CUT_COLUMN) {
						JSONObjectWrapper sourceWorkbookState = actionObject.getJSONObject(JSONConstants.SOURCE_WORKBOOK_STATE);
						String srcDocId = sourceWorkbookState.getString(JSONConstants.SOURCE_DOCID);
						int xaidc = sourceWorkbookState.optInt(JSONConstants.EXECUTED_ACTION_ID_WHILE_COPY, -1);
					}

					actionQueue.add(actionObject);
					//actionId++; // Increment here the actioId after adding to queue
				}
				/////////
			}
			catch(IllegalStateException ise)
			{
				// reset the actionId if its not added to queue
				logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Queue size exceed the permissiable ({3}) limit, current size : {4}. ActionObject :: {5}", new Object[]
						{
								this.getResourceKey(), actionObject.get("zuid"), rs_id, EngineConstants.ACTIONS_QUEUE_SIZE, actionQueue.size(), actionObject //No I18N
						});
				logger.log(Level.WARNING, "ERROR RESOURCE_ID: " + this.getResourceKey()+ " Error Queue size exceed the permissiable limit.", ise);
				throw ise;
			}
			catch(Exception ex)
			{
				//logger.log(Level.WARNING, "Error in adding action to queue :: ", ex);
				logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Error in addActionToQueue() method. ActionObject :: {3}", new Object[]
						{
								this.getResourceKey(), actionObject.get("zuid"), actionObject.has("rsid") ? actionObject.get("rsid") : "", actionObject //No I18N
						});
				//                if(ex.getMessage() != null)
				//                {
				throw ex;
				//                }
				//                throw new Exception(ErrorCode.ERROR_ADD_ACTION_TO_QUEUE, ex);
			}
			finally
			{
				this.executeThread();

//				try
//				{
//					if(executor == null)
//					{
//						// executor = new ActionExecutor(this);
//						executor	=	new	ActionExecutorFixer(this);
//					}
//					/////////////
//					if(!actionQueue.isEmpty() && !isExecuting())
//					{
//						setIsExecuting(true);
//
//						Executor executorThreadPool = EngineFactory.getActionExecutorPool();
//
//						int activeThreadCount = ((ThreadPoolExecutor)executorThreadPool).getActiveCount();
//						if(activeThreadCount > 0.9 * EngineConstants.ACTION_THREAD_POOL_SIZE )
//						{
//							logger.log(Level.INFO, "Engine: >>>>>>>>>>> Active Executor thread count : {0} >>> RESOURCE_ID: {1}", new Object[]{activeThreadCount, this.getResourceKey()});
//						}
//
//						executorThreadPool.execute(executor);
//					}
//				}
//				catch(Exception ex1)
//				{
//					logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Error in addActionToQueue() method. ActionObject :: {3}", new Object[]
//							{
//									this.getResourceKey(), actionObject.get("zuid"), rs_id, actionObject //No I18N
//							});
//					//logger.log(Level.WARNING, "Error Trace", ex1);
//					throw ex1;
//				}

				///////////////
				//writeLock.unlock();
			}
			///////////
		}
		catch(Exception ex)
		{
			logger.log(Level.WARNING, ">>>>>>>>>>>>>>>>>>>>ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Error in addActionToQueue() method. ActionObject :: {3}", new Object[]
					{
							this.getResourceKey(), actionObject.get("zuid"), rs_id, actionObject //No I18N
					});
			throw ex;
//                if(ex.getMessage() != null)
//                {
			//throw ex;
//                }
//                throw new Exception(ErrorCode.ERROR_ADD_ACTION_TO_QUEUE, ex);
		}
		return actionId;
	}

	public synchronized void executeThread() {
		try
		{
			if(executor == null)
			{
				// executor = new ActionExecutor(this);
				executor	=	new	ActionExecutorFixer(this);
			}
			/////////////
			if(!actionQueue.isEmpty() && !isExecuting())
			{
				setIsExecuting(true);

				Executor executorThreadPool = EngineFactory.getActionExecutorPool();

				int activeThreadCount = ((ThreadPoolExecutor)executorThreadPool).getActiveCount();
				if(activeThreadCount > 0.9 * EngineConstants.ACTION_THREAD_POOL_SIZE )
				{
					logger.log(Level.INFO, "Engine: >>>>>>>>>>> Active Executor thread count : {0} >>> RESOURCE_ID: {1}", new Object[]{activeThreadCount, this.getResourceKey()});
				}

				executorThreadPool.execute(executor);
			}
		}
		catch(Exception ex1)
		{
			logger.log(Level.WARNING, "ERROR >>>> Error in executeThread() method. ");
			//logger.log(Level.WARNING, "Error Trace", ex1);
			throw ex1;
		}
	}

	// **** Used only for certain Image, Chart and Macro Run actions. Should not be used for any other actions
	// this method will be removed in future
	public void addMutedActionToQueue(JSONObjectWrapper actionObject) throws Exception{
		try
		{
			int queueActionId = addActionToQueue(actionObject);
			JSONObjectWrapper responseObj = new JSONObjectWrapper();
			responseObj.put(JSONConstants.EXECUTED_ACTION_ID, queueActionId);
			MessagePropagator.sendMessage(getCollabId(), actionObject.getString(JSONConstants.ZUID), responseObj);

		}
		catch(Exception e)
		{
			logger.log(Level.WARNING, ">>>>>>>>>>>>>>>>>>>>ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Error in addMutedActionToQueue() method. ActionObject :: {3}", new Object[] {this.getResourceKey(), actionObject.get("zuid"), actionObject.get("rsid"), actionObject });
			throw e;
		}
	}

	public void rollbackAction(JSONObjectWrapper actionObject, String errorCode, boolean isFromCache)
	{
		logger.log(Level.WARNING, "[ROLLBACK] Rolling back the action with id {0}, action {1} with error code {1}", new Object[]{actionObject.optInt(JSONConstants.ACTION_ID, -1), actionObject.optInt(JSONConstants.ACTION, -1), errorCode} );

		EngineUtils1.rollbackAction(actionObject, errorCode);

//        if (actionObject.getBoolean(JSONConstants.RECORD_UNDO))
		{
			try
			{
				UserProfile sUserProfile = this.getUserProfile(actionObject.getString("zuid")); //No I18N
				String		tabTypeStr	 =	actionObject.has(JSONConstants.TAB_TYPE) ? actionObject.getString(JSONConstants.TAB_TYPE) : null;
				TabType		tabType		 =	ClientUtils.getTabType(tabTypeStr);
				if(!isFromCache && actionObject.has(JSONConstants.RSID) && !actionObject.getString(JSONConstants.RSID).equals("-1"))    //rsid will be -1 for API actions
				{
					//new UndoRedoManager().rollbackAction(this, actionObject, sUserProfile.getMacroRecorder(actionObject.getString(JSONConstants.RSID), tabType), isFromCache);
					new UndoRedoManager().rollbackAction(this, actionObject, sUserProfile.getTabInfo(actionObject.has(JSONConstants.UNIQUE_TAB_ID) ? actionObject.getString(JSONConstants.UNIQUE_TAB_ID) : actionObject.getString(JSONConstants.RSID), tabType).getMacroRecorder(), isFromCache);
				}
			}
			catch (Exception e)
			{
				logger.log(Level.WARNING, "exception while rolling back snapshot", e);
			}
		}

//		if (ErrorCode.ERROR_DOCUMENT_SAVE_SIZEEXCEED.equals(errorCode))
//		{
//			this.books.put(BookType.WORKBOOK, null);
//		}
		// Moved to ActionExecutor.
		// send the error message
		//ActionExecutor.sendErrorMessage(this, actionObject, errorCode, MsgType.ERROR, displayType);
	}

//    public void updateUndoRedoAction(int actionId, JSONObjectWrapper orgActionObject)
//    {
//        try
//        {
//            writeLock.lock();
//            EngineUtils1.updateUndoRedoAction(this, actionId, orgActionObject);
//        }
//        finally
//        {
//            writeLock.unlock();
//        }
//
//    }

//    public void addToTempActions(List list) throws Exception
//    {
//        writeLock.lock();
//        try
//        {
//                EngineUtils1.addToTempActions(this, list);
//        }
//        finally
//        {
//            writeLock.unlock();
//        }
//
//    }



	public int getActionId()
	{
		return actionId;
	}

	public void setXAndActionId(int actionId)
	{
		this.actionId = actionId;
		this.xActionId = actionId;
		this.publicStartAid = actionId;
	}

	public long getLastSavedActionId()
	{
		Workbook workbook = (Workbook)books.get(BookType.WORKBOOK);
		if(workbook != null && workbook.isReadyToRender())
		{
			return xActionId;
		}
		try
		{

			RedisHelper.moveToActionsList(this.getResourceKey());

			if(RedisHelper.hlen(RedisHelper.ACTIONS_LIST+this.getResourceKey()) > 0)
			{
				Set<String> set = RedisHelper.hkeys(RedisHelper.ACTIONS_LIST+this.getResourceKey());
				return Integer.parseInt(Collections.min(set, (String s1, String s2) ->
				{
					Long l1 = Long.parseLong(s1);
					Long l2 = Long.parseLong(s2);
					return (l1 < l2 ? -1 : (l1 == l2 ? 0 : 1));
				})) - 1;
			}
//                    if(RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + this.getResourceKey()) > 0)
//                    {
//                        List<String> list = RedisHelper.lrange(RedisHelper.EXECUTED_ACTIONS_LIST + this.getResourceKey(), 0, 0);
//                        return (new JSONObjectWrapper(list.get(0)).getLong(JSONConstants.ACTION_ID)) - 1;
//                    }
//                    if(RedisHelper.hlen(RedisHelper.TEMP_ACTIONS_LIST+this.getResourceKey()) > 0)
//                    {
//                        Set<String> set = RedisHelper.hkeys(RedisHelper.TEMP_ACTIONS_LIST+this.getResourceKey());
//                        return Integer.parseInt(Collections.min(set)) - 1;
//                    }
			String aid = RedisHelper.get(RedisHelper.ACTION_ID+this.getResourceKey(), RedisHelper.ACTION_ID_EXPIRE_TIME);
			if(aid != null)
			{
				return Integer.parseInt(aid);
			}

		}catch(Exception ex)
		{
			logger.log(Level.WARNING, "ERROR RESOURCE_ID: "+this.getResourceKey()+" Error in getLastSavedActionId  method", ex);
		}
		return 0;
	}

//	     public void setXActionId(int xActionId)
//	     {
//	    	 this.xActionId = xActionId;
//	     }

//	     public int getLastSavedActionId()
//	     {
//	    	 return lastSavedActionId;
//	     }
//
//	     public void setLastSavedActionId(int lastSavedActionId)
//	     {
//	    	 this.lastSavedActionId = lastSavedActionId;
//	     }

	public void setDelTime(String delTime)
	{
		this.delTime = delTime;
	}

	     /*	public UserProfile createUserProfile(String loginName) throws Exception
	{
		return DocumentUtils.createUserProfile(this.getDocId(), this.getDocOwner(), loginName);
	}*/

	public BlockingQueue<JSONObjectWrapper> getActionQueue()
	{
		return actionQueue;
	}


	public void addExecutedAction(JSONObjectWrapper actionObject)
	{
		// writeLock.lock();
		try{
			////////
			/////
//			int size = xActionList.size();
//			if(size > EngineConstants.X_ACTIONS_LIST_SIZE)
//			{
//				xActionList = xActionList.subList(size-EngineConstants.X_ACTIONS_LIST_SIZE, size);
//			}
//			// Set the time stamp in for the executed action
//			//actionObject.set("ts", System.currentTimeMillis());  // No I18N
//			//EngineUtils1.saveActionsTemp(this, actionObject);
//
//			xActionList.add(actionObject);
//
//			this.xActionId	=	xActionList.get(xActionList.size() - 1).getInt("aid");  // No I18N

			this.xActionId 					= 	actionObject.getInt("aid");  // No I18N
//                        this.isNextSaveMajorVersion = (this.isNextSaveMajorVersion || this.xActionId % 100 == 0) ? true : false;
			this.lastActionPerformer		=	actionObject.has(JSONConstants.USER_NAME) ? actionObject.getString(JSONConstants.USER_NAME) : null;
			this.lastActionPerformerZUID = actionObject.has(JSONConstants.ZUID) ? actionObject.getString(JSONConstants.ZUID) : null;
			String associatedSheetName		=	null;
			if(actionObject.has(JSONConstants.CURRENT_ACTIVE_SHEET)) {
				associatedSheetName = actionObject.getString(JSONConstants.CURRENT_ACTIVE_SHEET);
			} else if(actionObject.has(JSONConstants.SHEETLIST)) {
				associatedSheetName = ActionJsonUtil.getFirstSheetFromJsonArray(actionObject.optJSONArray(JSONConstants.SHEETLIST));
			} else if (actionObject.has(JSONConstants.ASSOCIATED_SHEET_NAME)) {
				associatedSheetName		= actionObject.getString(JSONConstants.ASSOCIATED_SHEET_NAME);
			}
			if(associatedSheetName != null)
			{
				this.setCurrCursorPosSheetCodeName(associatedSheetName);
			}
//                         addAuthor(actionObject.getString("zuid"));

			///////////////Add the XActions to another list/////////
//            if (isRemoteMode())
//            {
//                RedisHelper.hset(EXECUTED_ACTIONS_CACHE_LIST + getResourceKey(), actionObject.getString(JSONConstants.ACTION_ID), actionObject.toString(), UNDO_REDO_LIST_EXPIRE_TIME);
//            } else
			if (isReadOnly())
			{
				RedisHelper.hset(EXECUTED_ACTIONS_CACHE_LIST + this.getTmpContainerIdentity(), actionObject.getString(JSONConstants.ACTION_ID), actionObject.toString(), RedisHelper.CACHE_LIST_EXPIRE_TIME);
			} else
			{
				RedisHelper.markAsExecutedAction(getResourceKey(), actionObject);
			}

			///////////////////////////////////////////////

		}catch(Exception ex)
		{
			logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Error in addExecutedAction() method. ActionObject :: {3}", new Object[]{this.getResourceKey(), actionObject.get("zuid"), actionObject.get("rsid"), actionObject});
			logger.log(Level.WARNING, "ERROR RESOURCE_ID: "+this.getResourceKey()+" Error in addExecutedAction() method", ex);
		}
		finally
		{
			//writeLock.unlock();
		}
		/////////////
		changeIsNewDocStatus(this.lastActionPerformer != null ? this.lastActionPerformer : getDocOwner());
	}

	public void changeIsNewDocStatus()
	{
		changeIsNewDocStatus(getDocOwner());
	}
	public void changeIsNewDocStatus(String userName)
	{
		if(isNewDoc())
		{
			try
			{
				DocumentUtils.updateDocumentType(this, 1);
				setIsNewDoc(false);
				/////
				save("auto-version", userName, true);   //No I18N

				//SyncUpdater.enQ("create",String.valueOf(docId),docName,docOwner,docOwner,System.currentTimeMillis(),"0");//No I18N
			}
			catch(Exception ex)
			{
				logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} >>> Error in changeIsNewDocStatus() method.", new Object[]{this.getResourceKey()});
				logger.log(Level.WARNING, "ERROR RESOURCE_ID: "+this.getResourceKey()+" Error in changeIsNewDocStatus() method.", ex);
			}
		}
	}

//	public List<JSONObjectWrapper> getExecutedActionList()
//	{
//		return xActionList;
//	}

	public int getExecutedActionId()
	{
		//		writeLock.lock();
		//		try
		//		{
		//			if(!xActionList.isEmpty())
		//			{
		//				int xActionId = xActionList.get(xActionList.size() - 1).getInt("aid");  // No I18N
		//				//logger.log(Level.INFO, "Executed Action Id : {0}", xActionId);
		//				return xActionId;
		//			}
		//		}
		//		finally
		//		{
		//			writeLock.unlock();
		//		}
		return this.xActionId;
	}

	void setIsExecuting(boolean isExecuting)
	{
		this.isExecuting.compareAndSet(!isExecuting, isExecuting);
	}

	public boolean isExecuting()
	{
		return this.isExecuting.get();
	}

//             public int getLastVersionedActionId()
//             {
//                 return this.lastVersionedActionId;
//             }
//
//             public void setLastVersionedActionId(int lastVersionedActionId)
//             {
//                 this.lastVersionedActionId = lastVersionedActionId;
//             }

//	public JSONObjectWrapper getActionJson(int inActionId)
//	{
//		for(int i = xActionList.size() - 1; i >= 0; i--)
//		{
//			JSONObjectWrapper actionJson = xActionList.get(i);
//			int actionId = actionJson.getInt("aid");// No I18N
//			if(inActionId == actionId)
//			{
//				return actionJson;
//			}
//		}
//		return null;
//	}


	////////////////////////
	//SAS18_IMPL FS Comment ~mani
	    /* private FileStore getFileStore() throws Exception
	     {
	    	 StoreBean bean = (StoreBean)BeanUtil.lookup("StoreBean", docOwner);
	    	 return bean.getStore(docOwnerId);
	     }

	     public InputStream getReadStream(String filePath) throws Exception
	     {
	    	 StoreBean bean = (StoreBean)BeanUtil.lookup("StoreBean", docOwner);
	    	 return bean.getReadStream(docOwnerId, filePath);
	     }

	     public OutputStream getWriteStream(String filePath) throws Exception
	     {
	    	 StoreBean bean = (StoreBean)BeanUtil.lookup("StoreBean", docOwner);
	    	 return bean.getWriteStream(docOwnerId, filePath, false);
	     }

	     public OutputStream getWriteStream(String filePath, boolean isAppend) throws Exception
	     {
	    	 StoreBean bean = (StoreBean)BeanUtil.lookup("StoreBean", docOwner);
	    	 return bean.getWriteStream(docOwnerId, filePath, isAppend);
	     }

	     public boolean deleteFile(String filePath)// throws Exception
	     {
	    	 try{
	    		 StoreBean bean = (StoreBean)BeanUtil.lookup("StoreBean", docOwner);
	    		 return bean.deleteFile(docOwnerId, filePath);
	    	 }catch(Exception e){
	    		 logger.info("Error occured while getting file format from fileStore: "+e);
	    		 return false;
	    	 }
	     }

	     public boolean isFileExist(String filePath) //throws Exception
	     {
	    	 try{
	    		 StoreBean bean = (StoreBean)BeanUtil.lookup("StoreBean", docOwner);
	    		 return bean.isFileExist(docOwnerId, filePath);
	    	 }catch(Exception e){
	    		 logger.info("Error occured while getting file format from fileStore: "+e);
	    		 return false;
	    	 }
	     }

	     public FileDetail getFileDetail(String filePath) throws Exception
	     {
	    	 StoreBean bean = (StoreBean)BeanUtil.lookup("StoreBean", docOwner);
	    	 return bean.getFileDetail(docOwnerId, filePath);
	     }

	     public List listFiles(String dirPath) throws Exception
	     {
	    	 StoreBean bean = (StoreBean)BeanUtil.lookup("StoreBean", docOwner);
	    	 return bean.listFiles(docOwnerId, dirPath);
	     }

	     // works only in Unix file system
	     public boolean fileRename(String srcFileName, String destFileName) throws Exception
	     {
	    	 StoreBean bean = (StoreBean)BeanUtil.lookup("StoreBean", docOwner);
	    	 return bean.fileRename(docOwnerId, srcFileName, destFileName);
	     }
*/
	   /* SAS18_IMPL FS Comment ~Mani
	    *  public String getFileFormat(String dirPath) throws Exception {
	    	 String fileFormat	=	null;
	    	 //	String dirPath		=	this.getDirPath();
	    	 if(this.isFileExist(dirPath+ EngineConstants.ENGINE_FILE_FORMAT)) {
	    		 fileFormat = EngineConstants.ENGINE_FILE_FORMAT;
	    	 } else if(this.isFileExist(dirPath	+ EngineConstants.ENGINE_FRAGMENT_IDENTIFIER_FILE)) {
	    		 fileFormat = EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT;
	    	 } else if(this.isFileExist(dirPath + EngineConstants.XLS_FILE_FORMAT)) {
	    		 fileFormat = EngineConstants.XLS_FILE_FORMAT;
	    	 } else if(this.isFileExist(dirPath + EngineConstants.CSV_FILE_FORMAT)) {
	    		 fileFormat = EngineConstants.CSV_FILE_FORMAT;
	    	 } else if(this.isFileExist(dirPath + EngineConstants.TSV_FILE_FORMAT)) {
	    		 fileFormat = EngineConstants.TSV_FILE_FORMAT;
	    	 } else if(this.isFileExist(dirPath + EngineConstants.DEFAULT_FILE_FORMAT)) {
	    		 fileFormat = EngineConstants.DEFAULT_FILE_FORMAT; // for .sxc
	    	 }
	    	 return fileFormat;
	     }*/
	public String getFileFormat() {
		String fileFormat	=	EngineConstants.ENGINE_ZS_FRAGMENTED_FILE_FORMAT;
		//
		try {
			long resourceId = ZSStore.getFragmentId(this.docOwner, Long.parseLong(this.docId), ZSStore.FILENAME_ADHOCOBJECTS);
			if(this.isFileExist(resourceId, FileName.ADHOCOBJECTS, ZSStore.FileExtn.ZSF))
			{
				return EngineConstants.ENGINE_ZS_FRAGMENTED_FILE_FORMAT;
			}

			resourceId = ZSStore.getFragmentId(this.docOwner, Long.parseLong(this.docId), ZSStore.FILENAME_FONTFACES);
			if (this.isFileExist(resourceId, ZSStore.FileName.FONTFACES, ZSStore.FileExtn.ZIP))
			{
				fileFormat = EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT;
			} else if (this.isFileExist(Long.parseLong(this.docId), ZSStore.FileName.DOCUMENT, ZSStore.FileExtn.ODS)) {
				fileFormat = EngineConstants.ENGINE_ODS_FILE_FORMAT;
			} else if (this.isFileExist(Long.parseLong(this.docId), FileName.DOCUMENT, FileExtn.ZSHEET)) {
				fileFormat = EngineConstants.ENGINE_ZSHEET_FILE_FORMAT;
			} else if (this.isFileExist(Long.parseLong(this.docId), FileName.DOCUMENT, FileExtn.ZXLSX)) {
				fileFormat = EngineConstants.ZXLSX_FILE_FORMAT;
			} else if (this.isFileExist(Long.parseLong(this.docId), FileName.DOCUMENT, FileExtn.XLSX)) {
				fileFormat = EngineConstants.XLSX_FILE_FORMAT;
			}
			// Only temporary -- can be removed after going live
			else if (this.isFileExist(Long.parseLong(this.docId), ZSStore.FileName.REMOTEDOC, ZSStore.FileExtn.ODS)) {
				fileFormat = EngineConstants.ENGINE_ODS_FILE_FORMAT;
			}

	    	 /*else if(this.isFileExist(dirPath	+ EngineConstants.ENGINE_FRAGMENT_IDENTIFIER_FILE)) {
			fileFormat = EngineConstants.ENGINE_FRAGMENTED_FILE_FORMAT;
		} else if(this.isFileExist(dirPath + EngineConstants.XLS_FILE_FORMAT)) {
			fileFormat = EngineConstants.XLS_FILE_FORMAT;
		} else if(this.isFileExist(dirPath + EngineConstants.CSV_FILE_FORMAT)) {
			fileFormat = EngineConstants.CSV_FILE_FORMAT;
		} else if(this.isFileExist(dirPath + EngineConstants.TSV_FILE_FORMAT)) {
			fileFormat = EngineConstants.TSV_FILE_FORMAT;
		} else if(this.isFileExist(dirPath + EngineConstants.DEFAULT_FILE_FORMAT)) {
			fileFormat = EngineConstants.DEFAULT_FILE_FORMAT; // for .sxc
		}*/
	    	 /*if(fileFormat==null){
	    		 String dirPath		=	this.getDirPath();
	    		 return getFileFormat(dirPath);
	    	 }*/
		}catch(Exception e)
		{
			fileFormat	=	EngineConstants.ENGINE_ZS_FRAGMENTED_FILE_FORMAT;
		}
		return fileFormat;
	}

	public String getFileFormatForVersion(long sheetVersionId) {
		try {
			//String zfsngVersionId = ZohoFS.getVersionIDForEditor(this.getDocsSpaceId(), this.getResourceKey(), zfsngVersionNo);
			//Long versionResourceId = DocumentUtils.getVersionIdforZFSNGVersion(Long.parseLong(this.getDocId()), this.getDocOwner(), zfsngVersionId);
			//long versionResourceId = ZSStore.getVersionId(this.getDocOwner(), Long.parseLong(this.getDocId()), sheetVersionNo);
			if (this.isFileExist(sheetVersionId, FileName.VERSION, FileExtn.ZSHEET)) {
				return EngineConstants.ENGINE_ZSHEET_FILE_FORMAT;
			} else if (this.isFileExist(sheetVersionId, FileName.VERSION, FileExtn.ZXLSX)) {
				return EngineConstants.ZXLSX_FILE_FORMAT;
			} else if (this.isFileExist(sheetVersionId, FileName.VERSION, FileExtn.XLSX)) {
				return EngineConstants.XLSX_FILE_FORMAT;
			} else {
				return EngineConstants.ENGINE_ODS_FILE_FORMAT;
			}
		} catch(Exception e) {
			// Fallback for previous implementations!!!
			logger.log(Level.WARNING, "Engine: Exception when getting File Format for version :::: RKey: {0}  ~~~  sheetVersionId: {1}", new Object[]{this.getResourceKey(), sheetVersionId});
			return EngineConstants.ENGINE_ZSHEET_FILE_FORMAT;
		}
	}


	public String	getDirPath(){
		String	fileId			=	remoteBookId	!=	null	?	remoteBookId	:	docId;	//Will get removed, once we move to DFS.
		StringBuilder	buffer	=	new StringBuilder();
		buffer.append(getBasePath());
		buffer.append(EngineConstants.LOCATION).append(fileId);
		return buffer.toString();
	}

	public String	getBasePath(){
		StringBuilder	basePath	=	new StringBuilder();
		if(apiKeyId	!=	null)	{
			basePath.append(EngineConstants.LOCATION).append(apiKeyId);
		}
		return basePath.toString();
	}

	//	public String	getMetaFilePath(){
	//		StringBuffer	buffer	=	new StringBuffer(getDirPath());
	//		buffer
	//			.append(EngineConstants.LOCATION)
	//			.append("cache")			//No I18N
	//			.append(EngineConstants.LOCATION)
	//			.append("cache.props");		//No I18N
	//		return buffer.toString();
	//	}
	//
	//	public Properties getMetaInfo()throws Exception {
	//		String filePath = getMetaFilePath();	//No internationalization
	//		logger.info("FileStore file is searchig ::"+filePath);
	//		if(isFileExist(filePath))
	//		{
	//			InputStream fis = getReadStream(filePath);
	//			Properties props = new Properties();
	//			props.load(fis);
	//			fis.close();
	//			return props;
	//		}
	//		return null;
	//	}

	// DFS Impl
//	public Properties getMetaInfo()
//	{
//		return EngineUtils1.readSheetMetaInfo(this);
//	}
	///////////////////////


	public void cleanUp() throws Exception
	{
		// clean up the undo/redo files
		// clean up the revert block
		for(Map.Entry<String, UserProfile> entry : this.getUserProfileMap().entrySet())
		{
			UserProfile userProfile = entry.getValue();
			//for(String wmsRawSessionId : userProfile.getWmsRawSessionIds())
			//{
			//    userProfile.cleanUndoRedoManager(wmsRawSessionId, this);
			//}
			userProfile.cleanUndoRedoManager(this.getResourceKey());
		}
	}

	     /*
	// Generally used for sending error or exception messages
	// which needs to be sent to all; like error while saving
	public void sendErrorMessageToAll(String message)
	{
		try
		{
			for(UserProfile uProfile : this.getUserProfileList())
			{
				String uName = uProfile.getUserName();
				Hashtable ht = new Hashtable();
				ht.put("docid", getDocId());
				ht.put("em", message);
				WmsApi.sendCustomMessage(uName, ht);
			}
		}
		catch(Exception ex)
		{
			logger.log(Level.WARNING, null, ex);
		}
	}

	public void sendErrorMessage(int actionId, String userName, String message)
	{
		try
		{
			for(UserProfile uProfile : this.getUserProfileList())
			{
				String uName = uProfile.getUserName();
				Hashtable ht = new Hashtable();
				ht.put("docid", getDocId());
				ht.put("em", message);
				WmsApi.sendCustomMessage(uName, ht);
			}
		}
		catch(Exception ex)
		{
			logger.log(Level.WARNING, null, ex);
		}
	}

	      *
	      */

	private void addUserToCollaboration(UserProfile userProfile, String wmsSessionId, String wmsRawSessionId) throws Exception
	{
		try
		{
			String creatCollborationStatus="NA"; //NO I18N
			String addMemberStatus = "NA"; //NO I18N
			String joinCollaborationStatus = "NA"; //NO I18N
			String collabId = getCollabId();
			Hashtable collabUserTable = CollaborationApi.getSessions(collabId);
			if(collabUserTable == null || collabUserTable.isEmpty())
			{
				// create a new Collaboration
				WmsApi.createCollaboration(userProfile.getZUserId(), "", collabId);
				creatCollborationStatus = "S"; //NO I18N
				// TODO: code to send the custome message to ask user to rejoin
			}
			else if(!collabUserTable.containsKey(userProfile.getZUserId()))
			{
				String existingCollabZUID = null;
				// User validation
				Enumeration userIds = collabUserTable.keys();
				while(userIds.hasMoreElements()){
					String userId = (String) userIds.nextElement();
					if(userId.indexOf("$") == 0) {
						existingCollabZUID = userId;
						break;
					} else {
						String userDisplayName = DocumentUtils.getZDisplayName(userId);
						if(!"".equals(userDisplayName)){
							existingCollabZUID = userId;
							break;
						} else {
							logger.log(Level.INFO, "## Invalid user detected and not used for CollabJoin. ID:{0} ##", userId);
						}
					}
				}

				if(existingCollabZUID != null) {
					addMemberStatus = WmsApi.addMember(collabId, existingCollabZUID, userProfile.getZUserId()) +"";
				} else {
					WmsApi.createCollaboration(userProfile.getZUserId(), "", collabId);
					creatCollborationStatus = "S"; //NO I18N
				}
			}
			joinCollaborationStatus = WmsApi.joinCollaboration(collabId, userProfile.getZUserId(), wmsSessionId) + "";
			DocumentUtils.sendRefreshWMSUserSession();
			logger.log(Level.INFO, "[COLLAB LOG] CID :{0} , userId: {1} , rawSId: {2} , cStatus: {3} ,aStatus: {4} , jStatus: {5}", new Object[]{collabId, userProfile.getZUserId(), wmsRawSessionId, creatCollborationStatus, addMemberStatus, joinCollaborationStatus});

		}
		catch(Exception ex)
		{
			//logger.log(Level.WARNING, "Error while making user to join collaboration :: userName : "+userProfile.getZUserId(), ex);
			logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} :: WMSSESSIONID : {2} >>> Error in making user to join collaboration.", new Object[]{this.getResourceKey(), userProfile.getZUserId(), wmsSessionId});
			logger.log(Level.WARNING, "ERROR RESOURCE_ID: "+this.getResourceKey()+" Error in making user to join collaboration", ex);
			throw ex;
		}
	}

	public void setMacroInterface(MacroInterface macroInterface)
	{
		this.macroInterface = macroInterface;
	}
	public MacroInterface getMacroInterface()
	{
		return macroInterface;
	}

	public String getIntegrationMetaData() {
		return integrationMetaData;
	}

	public void setIntegrationMetaData(String integrationMetaData) {
		this.integrationMetaData = integrationMetaData;
	}


	//////////////////////////////DFS START/////////////////////////////////////////////////
	public Long getFragmentId(FileName name, boolean insertIfNotFound, String associatedName) throws Exception
	{
		return ZSStore.getFragmentId(this.getDocOwner(), Long.valueOf(this.getDocId()), Long.valueOf(this.docOwnerZUID), name, insertIfNotFound, associatedName);
	}

	public Store getStore(Long resourceId, FileName name) throws Exception
	{
		return ZSStore.getStore(getSpace(), docOwner, Long.valueOf(docId), resourceId, name);
	}

	public Store getStore(Long resourceIds[], FileName name) throws Exception
	{
		return ZSStore.getStore(getSpace(), docOwner,Long.valueOf(docId), resourceIds, name);
	}

	public Map getWriteInfo(Long resourceId, FileName name, FileExtn extn, String associatedName, Long fileSize) throws Exception
	{
		return	ZSStore.getWriteInfo(getSpace(), docOwner, Long.valueOf(docId), resourceId, name, extn, associatedName, fileSize);
	}

	//map.get("OS") to get OutputStream
	public Map getWriteInfo(Long resourceId, FileName name, FileExtn extn, String associatedName) throws Exception
	{
		return	getWriteInfo(resourceId, name, extn, associatedName, -1L);
	}

	public void finishWrite(Map writeInfo) throws Exception
	{
		ZSStore.finishWrite(writeInfo);
	}

	public InputStream getReadStream(Long resourceId, FileName name, FileExtn extn, String associatedName) throws Exception
	{
		return ZSStore.getReadStream(getSpace(), this.getDocOwner(), Long.valueOf(docId), resourceId, name, extn, associatedName);
	}
	public InputStream getReadStream(Store store, Long resourceId, FileName name, FileExtn extn, String associatedName) throws Exception
	{
		return ZSStore.getReadStream(store, resourceId, name, extn, associatedName);
	}
	public void deleteFile(Long resourceId, FileName name, FileExtn extn, String associatedName) throws Exception
	{
		ZSStore.deleteFile(getSpace(), this.docOwner, Long.valueOf(docId), resourceId, name, extn, associatedName);
	}

	public void deleteFile(Long resourceIds[], FileName name, FileExtn extn) throws Exception
	{
		ZSStore.deleteFile(getSpace(), docOwner, Long.valueOf(docId), resourceIds, name, extn);
	}

	public boolean isFileExist(Long resourceId, FileName name, FileExtn extn) throws Exception
	{
		// Do not pass the FileName.SHEETS to this method
		return ZSStore.isFileExist(getSpace(), docOwner, Long.valueOf(docId), resourceId, name, extn);
	}
	public boolean isFileExist(Store store, Long resourceId, FileName name, FileExtn extn) throws Exception
	{
		// Do not pass the FileName.SHEETS to this method
		return ZSStore.isFileExist(getSpace(), docOwner, Long.valueOf(docId), resourceId, name, extn);
	}
	public SheetFileInfo getFileInfo(Long resourceId, FileName name, FileExtn extn, boolean createIfNotFound) throws Exception
	{
		return ZSStore.getFileInfo(getSpace(), docOwner, Long.valueOf(docId), resourceId, name, extn, createIfNotFound);
	}
	public SheetFileInfo getFileInfo(Store store, Long resourceId, FileName name, FileExtn extn, boolean createIfNotFound) throws Exception
	{
		return ZSStore.getFileInfo(store, resourceId, name, extn, createIfNotFound, -1L);
	}

	public void migrateToDFS() throws Exception
	{
		//All the Doc has been migrated in zoho sheet ~Mani
		// EngineUtils1.migrateToDFS(this, Long.valueOf(docOwnerZUID), docOwner, Long.valueOf(docId));
	}
	//////////////////////////////DFS END/////////////////////////////////////////////////

	private	Object	getSpace()	{
		long  ownerZUID	=	Long.valueOf(docOwnerZUID);
		Object	spaceName;
		if(ownerZUID	!=	-1L)	{
			spaceName	=	ownerZUID;
		}	else {
			spaceName	=	docOwner;
		}
		return	spaceName;
	}

	boolean isChatSaveRequired(){

		// TODO - Use the LLEN to get the size of the key - can use direclty the RedisHelper
		// "doc:chat:" should be a variable in RedisHelper
		try{
			long chatLength =  RedisHelper.llen(RedisHelper.CHAT_LIST + getResourceKey());
			//return (chatLength <= 0 ? false : true );
			return (chatLength > 0);
		}catch(Exception e){
			return false;
		}
	}

	private class SrcBooksCached{
		private final String docId;
		private final int lastExecutedActionID;
		private final String version;
		private final Map<BookType, Book> books;

		SrcBooksCached(String docId, int lastExecutedActionID, String version, Map books) {
			this.docId = docId;
			this.lastExecutedActionID = lastExecutedActionID;
			this.version = version;
			this.books = books;
		}

		public String getDocId() {
			return docId;
		}

		public int getLastExecutedActionID() {
			return lastExecutedActionID;
		}

		public String getVersion() {
			return this.version;
		}

		public Map getBooks() {
			return books;
		}
	}

	public void setSourceBooksForPaste(String docId, String version, int lastExecutedActionID, Workbook workbook, ImageBook imageBook){
		Map books = new HashMap();
		books.put(BookType.WORKBOOK, workbook);
		books.put(BookType.IMAGEBOOK, imageBook);
		this.srcBooksCachedRef = new SoftReference<>(new SrcBooksCached(docId, lastExecutedActionID, version, books));
	}

	public Map getSourceBooksForPaste(JSONObjectWrapper workbookState) throws Exception {
		String srcRid = workbookState.getString(JSONConstants.SOURCE_RID);
		// TODO - SHIRU - FALLBACK FOR SERVER CLIP WHERE ZUID WILL NOT BE AVAILABLE.
		String zuid = workbookState.optString(JSONConstants.ZUID, AppUtil.getUserZuid());
		String srcDocId = workbookState.getString(JSONConstants.SOURCE_DOCID);
		String srcWorkbookStateType = workbookState.optString(JSONConstants.SOURCE_WORKBOOK_STATE_TYPE, null);
		String docOwner = workbookState.optString(JSONConstants.OWNER, null);
		int lastExecutedActionId;
		String version;
		if(srcWorkbookStateType == null) {
			lastExecutedActionId = -1;
			version = null;
		} else if(JSONConstants.SOURCE_WORKBOOK_STATE_TYPE_VAL_VERSION.equals(srcWorkbookStateType)) {
			lastExecutedActionId = -1;
			version = workbookState.getString(JSONConstants.SOURCE_VERSION_NO);
		} else if(JSONConstants.SOURCE_WORKBOOK_STATE_TYPE_VAL_AID.equals(srcWorkbookStateType)) {
			version = null;
			lastExecutedActionId = workbookState.getInt(JSONConstants.EXECUTED_ACTION_ID_WHILE_COPY);
		} else {
			throw new IllegalArgumentException("WRONG STATE in sourceWorkbookState: " + workbookState); //No I18N
		}

		return this.getSourceBooksForPaste(srcDocId, srcRid, docOwner, version, zuid, lastExecutedActionId);
	}

	public Map getSourceBooksForPaste(String srcDocId, String srcRid, String docOwner, String version, String zuid, int lastExecutedActionID) throws Exception{

		// TO BE REMOVED ASAP
		// Temporary Fix for a Bug
		// "xaidc" exceeded "aid" by some bug!!! Causing infinite loops in such paste actions!!!
		if(this.getDocId().equals(srcDocId) && lastExecutedActionID >= this.getExecutedActionId()) {
			logger.log(Level.SEVERE, "Workaround for the bug setting wrong aid in clips. lastExecutedActionID ::  from Clip: {0}  ~  in container: {1}", new Object[]{lastExecutedActionID, this.getExecutedActionId()});
			return this.books;
		}

		SrcBooksCached srcdocumnetEntry = this.srcBooksCachedRef.get();

		Map srcBooks = null;
		if (srcdocumnetEntry != null
				&& srcDocId.equals(srcdocumnetEntry.getDocId())
				&& Objects.equals(version, srcdocumnetEntry.getVersion())
				&& srcdocumnetEntry.getLastExecutedActionID() == lastExecutedActionID) {
			srcBooks = srcdocumnetEntry.getBooks();
			Workbook srcWorkbook = (Workbook) srcBooks.get(BookType.WORKBOOK);
			if(srcWorkbook == null || srcWorkbook.isDismantled())
			{
				srcBooks = null;
			}
		}

		if(srcBooks == null) {
			boolean includeSubVersion = false;
			try
			{
				HashMap<String, Object> lockedInfo = ZohoFS.getLockedResourceInfo(docOwner, srcRid);
				if (lockedInfo != null) {
					String lockedBy = (String) lockedInfo.get("LOCKED_BY");
					includeSubVersion = lockedBy.equals(zuid);
				}
			}
			catch(Exception e)
			{
				logger.log(Level.SEVERE, "Exception while getting resource lock info", e);
			}

			if(version != null) { //version should be null or any valid version number, not empty string or any other value
				srcBooks = EngineUtils1.getBooks(srcDocId, srcRid, docOwner, version);
			} else if(lastExecutedActionID != -1) {
				try {
					srcBooks = EngineUtils1.getBooks(srcDocId, srcRid, docOwner, lastExecutedActionID, includeSubVersion);
				}
				catch(Exception e)
				{
					//Get Top
					srcBooks = EngineUtils1.getBooks(srcDocId, srcRid, docOwner, -1, includeSubVersion);
				}
			} else {
				//Get Top
				srcBooks = EngineUtils1.getBooks(srcDocId, srcRid, docOwner, -1, includeSubVersion);
			}
			this.srcBooksCachedRef = new SoftReference<>(new SrcBooksCached(srcDocId, lastExecutedActionID, version, srcBooks));
		}

		return srcBooks;
	}

	public Map getSourceBooksForImport(JSONObjectWrapper workbookState) throws Exception
	{
		String srcDocId = workbookState.getString(JSONConstants.SOURCE_DOCID);
		String srcRid = workbookState.getString(JSONConstants.SOURCE_RID);
		String docOwner = workbookState.getString(JSONConstants.OWNER);
		String srcWorkbookStateType = workbookState.optString(JSONConstants.SOURCE_WORKBOOK_STATE_TYPE, null);
		if(JSONConstants.SOURCE_WORKBOOK_STATE_TYPE_VAL_AID.equals(srcWorkbookStateType))
		{
			String version = null;
			int lastExecutedActionId = workbookState.getInt(JSONConstants.EXECUTED_ACTION_ID_WHILE_COPY);
			return this.getSourceBooksForImport(srcDocId, srcRid, docOwner, version, lastExecutedActionId);
		}
		else
		{
			throw new IllegalArgumentException("WRONG STATE in sourceWorkbookState: " + workbookState); //No I18N
		}
	}

	/**
	 * Get the source map containing workbook and image book of the given document id
	 * @param srcDocId
	 * @param version
	 * @param lastExecutedActionID
	 * @return
	 * @throws Exception
	 */
	private Map getSourceBooksForImport(String srcDocId, String srcRid, String srcDocOwner, String version, int lastExecutedActionID) throws Exception
	{
		SrcBooksCached cachedSourceBooks = this.srcBooksCachedRef.get();

		if (cachedSourceBooks != null && srcDocId.equals(cachedSourceBooks.getDocId()) && Objects.equals(version, cachedSourceBooks.getVersion()) && cachedSourceBooks.getLastExecutedActionID() == lastExecutedActionID)
		{
			Workbook workbook = (Workbook) cachedSourceBooks.getBooks().get(BookType.WORKBOOK);
			if (workbook != null && !workbook.isDismantled()) {
				return cachedSourceBooks.getBooks();
			}
		}

		WorkbookContainer srcContainer = new WorkbookContainer(srcRid, srcDocOwner);
		Workbook sourceWorkbook = srcContainer.getWorkbook(version);
		ImageBook sourceImageBook = srcContainer.getImageBook(version);

		Map books = new HashMap();
		books.put(BookType.WORKBOOK, sourceWorkbook);
		books.put(BookType.IMAGEBOOK, sourceImageBook);

		this.srcBooksCachedRef = new SoftReference<>(new SrcBooksCached(srcDocId, lastExecutedActionID, version, books));

		return this.srcBooksCachedRef.get().getBooks();
	}
	public void setThumbnailCreatedTime(long time){

		this.thumbnailCreatedTime = time;
	}

	public long getThumbnailCreatedTime(){

		return this.thumbnailCreatedTime;
	}


	public long getCreatedTime()
	{
		return createdTime;
	}


	public	Properties	getDocMetaInfo()
	{
		return	EngineUtils1.readDocMetaInfo(this);
	}

	//	    public void writeDocMetaInfo(IWidgetInfo iwidgetInfo) throws Exception
//	     {
//                 logger.log(Level.INFO, "writeLock : locking : writeDocMetaInfo : {0}", this.getResourceKey());
//	    	 writeLock.lock();
//	    	 try
//	    	 {
//	    		 EngineUtils1.writeDocMetaInfo(this, iwidgetInfo, null);
//	    	 }
//	    	 finally
//	    	 {
//	    		 writeLock.unlock();
//                         logger.log(Level.INFO, "writeLock : Unlocking : writeDocMetaInfo : {0}", this.getResourceKey());
//	    	 }
//	     }
	/*
	 * We need to update this client with each response
	 */
//	    public long getLastSavedActionId()
//	    {
//	    	try{
//		    	if(RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + this.getResourceId()) > 0)
//		    	{
//		    		List<String> actionObj = RedisHelper.lrange(RedisHelper.EXECUTED_ACTIONS_LIST + this.getResourceId(), 0, 0);
//		    		return new JSONObjectWrapper(actionObj.get(0)).getInt(JSONConstants.ACTION_ID);
//		    	}
//		    	return this.getExecutedActionId();
//	    	}
//	    	catch(Exception e)
//	    	{
//	    		return -1;
//	    	}
//	    }
	public String getCachedVersion()
	{
		return this.cachedVersionNumber;
	}

	public boolean isBooksCached()
	{
		return (this.cachedBooks != null && this.cachedBooks.get() != null);
	}

	public int getCachedWorkbooksSize()
	{
		return (this.cachedBooks != null && this.cachedBooks.get() != null)  ? this.cachedBooks.get().size() : 0;
	}

	public Workbook getCachedWorkbook()
	{
		Workbook workbook = (this.cachedBooks != null && this.cachedBooks.get() != null)  ? (Workbook)this.cachedBooks.get().get(BookType.WORKBOOK) : null;
		return (workbook != null && !workbook.isDismantled()) ? workbook : null;
	}

	public ImageBook getCachedImageBook()
	{
		return (this.cachedBooks != null && this.cachedBooks.get() != null) ? (ImageBook)this.cachedBooks.get().get(BookType.IMAGEBOOK) : null;
	}

	public void setBooksCache(Map cachedBooks, String cachedVersion)
	{
		this.cachedBooks = new SoftReference<>(cachedBooks);
		this.cachedVersionNumber = cachedVersion;
	}

	public boolean clearCachedBooks()
	{
		if (this.cachedBooks != null && this.cachedBooks.get() != null)
		{
			this.cachedBooks.clear();
			return true;
		}
		return false;
	}
	public void setTmpContainerIdentity(String containerIdentity)
	{
		this.tmpContainerIdentity = containerIdentity;
	}

	public String getTmpContainerIdentity()
	{
		return this.tmpContainerIdentity;
	}

	public void setPublicStartAid(int startaid)
	{
		this.publicStartAid = startaid;
	}

	public int getPublicStartAid()
	{
		return publicStartAid;
	}

	public boolean isSaveRequired()
	{
		try
		{
//                    return RedisHelper.llen(RedisHelper.EXECUTED_ACTIONS_LIST + this.getResourceKey()) > 0 ||
//                            RedisHelper.hlen(RedisHelper.TEMP_ACTIONS_LIST + this.getResourceKey()) > 0 ||
//                            RedisHelper.hlen(RedisHelper.ACTIONS_LIST + this.getResourceKey()) > 0;

			return RedisHelper.hlen(RedisHelper.ACTIONS_LIST + this.getResourceKey()) > 0;

		}
		catch (Exception ex)
		{
			logger.log(Level.WARNING, "Error while getting xlist length : "+this.getResourceKey(), ex);
			return true;
		}
	}

	public void revertToVersion(String zfsngVersionNo) throws Exception {
		revertToVersion(zfsngVersionNo, true, true, true);
	}

	public void revertToVersion(String zfsngVersionNo,boolean ignorePendingActions, boolean cleanUp, boolean isRevertDB) throws Exception {

		String zfsngVersionId = ZohoFS.getVersionIDForEditor(this.getDocsSpaceId(), this.getResourceKey(), zfsngVersionNo);
		String versionToRevert = DocumentUtils.getVersionNoforZFSNGVersion(this.getDocId(), this.getDocOwner(), zfsngVersionId);
		if (versionToRevert == null) {
			DataObject dObj = DocumentUtils.getVersionDO(this.getDocId(), this.getDocOwner());
			if (dObj == null || dObj.isEmpty()) {
				logger.info("No version to revert");
				throw (new Exception());
			}
			Row row = dObj.getFirstRow("DocumentVersion"); // No I18N
			Double d = (Double) row.get("VERSION"); // No I18N
			zfsngVersionId = (String) row.get("RESOURCE_VERSION_ID");
			versionToRevert = d.toString();

			zfsngVersionNo = (new JSONArrayWrapper(ZohoFS.getVersionInfoForId(this.getDocsSpaceId(), this.resourceId, zfsngVersionId))).getJSONObject(0).getString("version_number");

		}

		Long versionId = ZSStore.getVersionId(this.getDocOwner(), Long.parseLong(this.getDocId()), versionToRevert);

		long t1 = System.currentTimeMillis();
//                Workbook workbook = this.getWorkbook(null);

//                if(!EngineUtils1.isSaveServer())
//                {
		while(checkAndCreateSavingState())
		{
			synchronized(this)
			{
				try
				{
					this.wait(100);
					if(System.currentTimeMillis() - t1 > 10 * 1000)
					{
						throw new Exception(ErrorCode.ERROR_ACTION_EXECUTION);
					}
				}
				catch(InterruptedException ie)
				{
					logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} >>> InterruptedException in revertODS() method.", new Object[]
							{
									this.getResourceKey()
							});
					logger.log(Level.WARNING, "ERROR RESOURCE_ID: " + this.getResourceKey()+ " InterruptedException in revertODS() method.", ie);
				}
			}
		}
//                }
		logger.log(Level.INFO, "writeLock : locking : revertToVersion : {0}", this.getResourceKey());
		writeLock.lock();

		try
		{

			///////////////
			//revertODS

			Store store = this.getStore(versionId, FileName.VERSION);

			FileExtn verFileFormat;
			if(this.isFileExist(store, versionId, FileName.VERSION, FileExtn.ZSHEET)) {
				verFileFormat = FileExtn.ZSHEET;
			} else if(this.isFileExist(store, versionId, FileName.VERSION, FileExtn.ZXLSX)) {
				verFileFormat = FileExtn.ZXLSX;
				XlsxImporter.copyXlsxMetaFileFromVersionToDocumentStore(versionId, this);
			} else if(this.isFileExist(store, versionId, FileName.VERSION, FileExtn.XLSX)) {
				verFileFormat = FileExtn.XLSX;
				XlsxImporter.copyXlsxMetaFileFromVersionToDocumentStore(versionId, this);
			} else {
				verFileFormat = FileExtn.ODS;
			}
            try(InputStream is = this.getReadStream(store, versionId, FileName.VERSION, verFileFormat, null)) {
                if(is == null) {
                    logger.log(Level.INFO, "STREAM NULL. VERSIONID : {0} VERSION NUMBER : {1}", new Object[]{versionId, versionToRevert});
                    throw (new Exception());
                }
                Map writeInfo = this.getWriteInfo(Long.parseLong(this.getDocId()), FileName.DOCUMENT, verFileFormat, null);
                EngineUtils1.copyFile(this, is, writeInfo);
            }
            copySearchFileFromVersionToDocumentStore(versionId);

			// clear the fragments if any
			EngineUtils1.clearFragmentsFile(this);


			///////////////

			RedisHelper.zrem(RedisHelper.SAVING_WORKBOOKS, getResourceKey());
			//Get workbook should not be called when workbook is in saving state, since we have saving check inside getWorkbook

			String sheetNames[] = this.getWorkbook(null, ignorePendingActions).getSheetNames(false);

			// this will take care to create the latest workbook by parsing the reverted version of file
			this.setWorkbook(null, cleanUp, false); // setting new workbook with old cleanup

			String revSheetNames[] = this.getWorkbook(null, ignorePendingActions).getSheetNames(false);


			/////////////
			this.setImageBook(this.getImageBook(zfsngVersionNo));

			/////////////
			//revertODS
			if(isRevertDB)
			{
				revertDB(versionToRevert, versionId, sheetNames, revSheetNames);
			}
			if(this.getWorkbook(null).getChartMap() != null)
			{
				this.getWorkbook(null).getChartMap().clear();
			}

//			try
//			{
//				ZSStore.deleteCellEditHistory(this);
//			}
//			catch(Exception e)
//			{
//				logger.log(Level.SEVERE, "[CELL_EDIT_HISTORY] Exception while deleting cell edit history while reverting version", e);
//			}

			try
			{
				logger.log(Level.SEVERE, "[CELL_EDIT_HISTORY] Adding Reverted Version Card in cell edit history");
				String zuid = AppUtil.getUserZuid();
				int aid = AppUtil.getAid();
				Long timeStamp = System.currentTimeMillis();
				this.getWorkbook(null).markIRVInCellEditHistory(zuid, aid, timeStamp);
			}
			catch(Exception e)
			{
				logger.log(Level.SEVERE, "[CELL_EDIT_HISTORY][Exception] Exception on adding reverted version card to cell edit history while reverting version", e);
			}
			//ChartUtils.setChartsInfoInWorkBook(this.getWorkbook(null), this.getDocId(), this.getResourceKey(), this.getDocOwner(), null);
			Workbook workbook = this.getWorkbook(null);
			ChartEngineManager.setChartEngineVersion(this, workbook);
			ChartEngineManager.getChartEngine(workbook).populateWithCharts(this, workbook, getDocId(), getResourceId(), getDocOwner(), null);
			///////////////

			RedisHelper.hdel(RedisHelper.SAVE_PRISONED_WORKBOOKS, this.getResourceKey());

		}
		finally
		{
			try {
				RedisHelper.zrem(RedisHelper.SAVING_WORKBOOKS, getResourceKey());

			} catch (Exception ex) {
				logger.log(Level.WARNING, "Exception while removing resourceId from SAVING_WORKBOOKS list :: resoucreId :: {0} ::: {1}", new Object[]{getResourceKey(), ex});
			}

			writeLock.unlock();
			logger.log(Level.INFO, "writeLock : Unlocking : revertToVersion : {0}", this.getResourceKey());
		}
	}

	public void revertDB(String versionNo, long versionId, String[] sheetNames, String[] revSheetNames) throws Exception
	{
		EngineUtils1.deleteOldVersionData(this, sheetNames, revSheetNames);
		if (versionNo != null)
		{
			EngineUtils1.revertCharts(this, String.valueOf(versionNo));
		}

		DBActionManager.markDataLinksForRevert(this.docOwner, this.docId, this.getWorkbook( null).getDataConnection().getDataConnectionIDs());
//                InsertHTMLUtils.revertVersion(this.getDocOwner(), versionId, this.getDocId());
	}

	public Workbook cloneWorkbook(Workbook workbook) throws Exception
	{
		logger.log(Level.INFO, "Calling workbook clone from WorkbookCloneExecutor thread for document >>> RESOURCE_ID: {0}", this.getResourceKey());

		long t1 = System.currentTimeMillis();

		Workbook clonedWorkbook = workbook.clone();

		logger.log(Level.INFO, "Total Time Taken to Clone ===>{0}ms.....{1}", new Object[]{System.currentTimeMillis() - t1, this.getResourceKey()});

		return clonedWorkbook;
	}

	public ImageBook cloneImages(ImageBook imageBook)
	{
		logger.log(Level.INFO, "Calling imageBook clone from WorkbookCloneExecutor thread for document >>> RESOURCE_ID: {0}", this.getResourceKey());

		long t1 = System.currentTimeMillis();

		ImageBook clonedImageBook = imageBook.clone();

		logger.log(Level.INFO, "Total Time Taken to Clone ===>{0}ms.....{1}", new Object[]{System.currentTimeMillis() - t1, this.getResourceKey()});

		return clonedImageBook;
	}

	private void setBook(BookType bookType, Book inBook, boolean isCleanUp, boolean overrideIsReadyToRender) throws Exception
	{
		logger.log(Level.INFO, "writeLock : locking : book : {0} of booktype {1}", new Object[]{this.getResourceKey(), bookType});
		writeLock.lock();
		try
		{
			if(executor != null && !executor.isPaused())
			{
				executor.pause();
			}

			Book book = books.get(bookType);
			if(!overrideIsReadyToRender && book != null && !book.isReadyToRender())
			{
				synchronized(book)
				{
					try{
						book.wait(12000);
					}catch(InterruptedException ie)
					{
						logger.log(Level.WARNING, "ERROR RESOURCE_ID: " + this.getResourceKey()+ " Error in setBook() "+bookType+" method", ie);
					}
				}
			}

			if(isCleanUp)
			{
				this.cleanUp();
			}

			books.put(bookType, inBook);
		}
		catch(InterruptedException ie)
		{
			logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} >>> InterruptedException in setBook method of booktype {1}.", new Object[]{this.getResourceKey(), bookType});
			logger.log(Level.WARNING, "ERROR RESOURCE_ID: "+this.getResourceKey()+" InterruptedException in setBook() method.", ie);
		}
		finally
		{
			if(executor != null && executor.isPaused())
			{
				executor.resume();
			}
			writeLock.unlock();
			logger.log(Level.INFO, "writeLock : Unlocking : book :{0} of booktype {1}", new Object[]{this.getResourceKey(), bookType});
		}
	}

	public boolean isLockedInSameThread() {
		return readLock.isHeldByCurrentThread();
	}

	private Book getBook(BookType bookType, String zfsngVersionNo, boolean ignorePendingActions) throws Exception
	{
		// if(this.isResetWorkbook)
		// {
		// 	this.setWorkbook(null, false, false);
		// 	this.setImageBook(null);
		// 	this.setXAndActionId(0);
		// 	this.isResetWorkbook = false;
		// }

		Book book = books.get(bookType);
		synchronized (this) {
			if (book != null && bookType == BookType.WORKBOOK && ((Workbook) book).isDismantled()) {
				book = null;
				books.put(BookType.WORKBOOK, null);
			}
		}
		zfsngVersionNo = (zfsngVersionNo != null && zfsngVersionNo.isEmpty()) ? null : zfsngVersionNo;
		if(zfsngVersionNo == null)
		{
			if(book != null && book.isReadyToRender())
			{
				if(bookType == BookType.WORKBOOK) {
					((Workbook) book).accessedFromContainer = System.currentTimeMillis();
				}
				Instant performStartupSync_start = Instant.now();
				ExternalRangeManager.performStartupSync(book.isReadyToRender(), this, this.externalRangeManager, zfsngVersionNo, ignorePendingActions, bookType);
				if(Duration.between(performStartupSync_start, Instant.now()).getSeconds()>4) {
					logger.log(Level.OFF, "{1} performStartupSync took:{0}s", new Object[]{Duration.between(performStartupSync_start, Instant.now()).getSeconds(), this.getResourceKey()});
				}
				return book;
			}
			else
			{
				long lockedTime = System.currentTimeMillis();
				try
				{
					/////////////////
					readLock.lock();
					/////////////////

					book = books.get(bookType);
					// The below check is must, do not remove it
					if(book != null)
					{
						Instant performStartupSync_start = Instant.now();
						ExternalRangeManager.performStartupSync(book.isReadyToRender(), this, this.externalRangeManager, zfsngVersionNo, ignorePendingActions, bookType);
						if(Duration.between(performStartupSync_start, Instant.now()).getSeconds()>4) {
							logger.log(Level.OFF, "{1} performStartupSync took:{0}s", new Object[]{Duration.between(performStartupSync_start, Instant.now()).getSeconds(), this.getResourceKey()});
						}
						return book;
					}

					long t1 = System.currentTimeMillis();
//                            if(!isSaveServer())
//                            {
//                                while(isSaving())
//                                {
//                                    synchronized(this)
//                                    {
//                                        try
//                                        {
//                                            this.wait(100);
//
//                                            if(System.currentTimeMillis() - t1 > 10 * 1000)
//                                            {
//                                                throw new Exception(ErrorCode.ERROR_DOCUMENT_LOAD);
//                                            }
//                                        }
//                                        catch(InterruptedException ie)
//                                        {
//                                            logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} >>> InterruptedException in getWorkbook() method.{1}", new Object[]
//                                                {
//                                                    this.getResourceKey(), ie
//                                                });
//                                        }
//                                    }
//                                }
//                            }

					switch(bookType)
					{
						case WORKBOOK:
							book = new Workbook();

							books.put(bookType, book); //Do not remove this code.
							ExternalRangeUtils.initWorkbookLink(this, this.externalRangeManager, this.resourceId, this.docOwner, HttpTaskManager.getNewTaskIdIfNull(null));
							book = EngineUtils1.getWorkBook(this, (Workbook)book, zfsngVersionNo, ignorePendingActions);
//									this.getExternalRangeManager().getWorkbookLink().reEvaluateImportRange((Workbook) book, this);
							logger.log(Level.INFO, "StylePropertiesPool size >>> {0}{1}", new Object[]{((Workbook)book).stylePropertiesPool.size(), (((Workbook)book).stylePropertiesPool.size() > 700) ? ", StylePropertiesPool limit almost reached" : ""});
							logger.log(Level.INFO, "Initial CellStyle size >>> {0} ::: {1}", new Object[]{((Workbook) book).getCellStyleCount(), this.getResourceKey()});
							break;
						case IMAGEBOOK:
							book = new ImageBook();
							books.put(bookType, book);
							EngineUtils1.getImageBook(this, (ImageBook)book, zfsngVersionNo);
							break;
						default:
							logger.log(Level.INFO, "Booktype {0} is not yet supported in getBook.", bookType);
							book = null;
					}
					return book;
				}
				catch(Exception ex)
				{
					// Reset the book to null.
					book = null;
					books.put(bookType, null);
					///////////

					logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} >>> Error in creating book object of type {1}.", new Object[]{this.getResourceKey(), bookType});
					logger.log(Level.WARNING, "ERROR RESOURCE_ID: "+this.getResourceKey()+" Error in creating book object : ", ex);
					throw ex;
				}
				finally
				{
					readLock.unlock();
					long timeMillis = System.currentTimeMillis() - lockedTime;
					if(timeMillis > 1000)
					{
						logger.log(Level.INFO, "getBook1 : rid :{0} booktype :{1} locked for  :{2} secs", new Object[]{this.getResourceKey(), bookType, (timeMillis/1000)});
					}
				}
			}
		}
		else
		{
			if (this.getCachedVersion() != null && this.getCachedVersion().equals(zfsngVersionNo) && this.isBooksCached())
			{
				switch(bookType)
				{
					case WORKBOOK:
						return this.getCachedWorkbook();
					case IMAGEBOOK:
						return this.getCachedImageBook();
				}
			}

			if(versionBooksMap.containsKey(zfsngVersionNo))
			{
				// TODO : Here we can put the isReadyToRender() check before we return the version workbook object
				Book versionBook = versionBooksMap.get(zfsngVersionNo).get(bookType);
				if(versionBook != null &&  versionBook.isReadyToRender())
				{
					// If Version workbook, locale and timezone will be set during parsing.
					// during version view we should not try to parse the main workbook.
					//Reason : if the main workbook is corrupted, we wont be able to view the version workbooks.
					//                    Workbook currentWorkbook = this.getWorkbook(null);
					//                    verWorkBook.setFunctionLocale(currentWorkbook.getFunctionLocale());
					//                    verWorkBook.setUserTimezone(currentWorkbook.getUserTimezone().getID());
					/////////////////////////////////////////////////////////
					return versionBook;
				}
				//return verWorkBookMap.get(version);
			}

			Book versionBook = null;
			long lockedTime = System.currentTimeMillis();
			try
			{
				/////////////////
				readLock.lock();
				/////////////////

				// After acquiring the Lock, we should again check the verWorkBookMap, if the verWorkbook Object is available and isReadyToRender and if true then return,
				// the object, otherwise go ahead with parsing again.
				if(versionBooksMap.containsKey(zfsngVersionNo))
				{
					versionBook = versionBooksMap.get(zfsngVersionNo).get(bookType);
					if(versionBook != null )    //&& verWorkBook.isReadyToRender())
					{
						return versionBook;
					}
				}

				switch(bookType)
				{
					case WORKBOOK:
						versionBook = new Workbook();
						versionBook = EngineUtils1.getWorkBook(this, (Workbook)versionBook, zfsngVersionNo, ignorePendingActions);
						break;
					case IMAGEBOOK:
						versionBook = new ImageBook();
						EngineUtils1.getImageBook(this, (ImageBook)versionBook, zfsngVersionNo);
						break;
					default:
						logger.log(Level.INFO, "Booktype {0} is not yet supported in getBook1.", bookType);
						versionBook = null;
				}

				return versionBook;
			}
			catch(Exception ex)
			{
				versionBook = null;
				logger.log(Level.WARNING, "ERROR RESOURCE_ID: {0} >>> Error in creating Version ({1}) book object of booktype {2}.", new Object[]
						{
								this.getResourceKey(), zfsngVersionNo, bookType
						});
				logger.log(Level.WARNING, "ERROR RESOURCE_ID: " + this.getResourceKey() + " Error in creating Version book object : ", ex);
				throw ex;
			}
			finally
			{
				readLock.unlock();
				long timeMillis = System.currentTimeMillis() - lockedTime;
				if(timeMillis > 1000)
				{
					logger.log(Level.INFO, "getBook2 : rid :{0} booktype :{1} locked for  :{2} secs", new Object[]{this.getResourceKey(), bookType, (timeMillis/1000)});
				}
			}
		}
	}


	protected void dismantle(){

		if(writeLock.tryLock()){
			try
			{
				this.externalRangeManager.dismantle();
				this.externalRangeManager = null;
				if(this.cachedBooks != null && this.cachedBooks.get() != null) {
					this.cachedBooks.get().values().forEach(book -> {
						if(book instanceof Workbook){
							((Workbook)book).dismantle();
						}
					});
					this.cachedBooks = null;
				}
				this.books.values().forEach(book -> {
					if(book instanceof Workbook){
						((Workbook)book).dismantle();
					}
				});
				this.books = null;
				SrcBooksCached srcBooksCached = this.srcBooksCachedRef.get();
				if( srcBooksCached != null) {
					for (Book book : srcBooksCached.books.values()) {
						if (book instanceof Workbook) {
							((Workbook) book).dismantle();
						}
					}
				}
				this.srcBooksCachedRef = null;

				if(this.mergePreviewWorkbook != null)
				{
					this.mergePreviewWorkbook.dismantle();
					this.mergePreviewWorkbook = null;
				}
			}finally
			{
				writeLock.unlock();
			}
		}
		else
		{
			logger.log(Level.INFO, "Write log held for saving. So NOT dismantling the workbook.");
		}
	}



	public static boolean isSaveServer()
	{
		return EngineConstants.IS_SAVE_ON_NEW_SERVER && EngineConstants.IS_SAVE_ENABLED;
	}

	public boolean forceSave() {
		return save(null, null, true, false, true);
	}
	public boolean save(String label, String loginName, boolean isDocSaveRequired) {
		return save(label, loginName, isDocSaveRequired, false, false);
	}

	public boolean saveAsOneFile(String label, String loginName, boolean isDocSaveRequired, boolean isSaveAsOneFile) {
		return save(label, loginName, isDocSaveRequired, isSaveAsOneFile, false);
	}

	/**
	 * BoilerPlate code to write OutPutStream to ZStore
	 * @param fileType
	 * @param fileExtension
	 * @param writerFunction
	 * @return
	 */
	private boolean executeAroundWriter(String fileType, String fileExtension, Function<OutputStream, Boolean> writerFunction) {
		Store store;
		SheetFileInfo sheetfileInfo;
		String resourceId = this.getResourceId();
		try {
			Long docId = Long.valueOf(this.getDocId());
			store = this.getStore(docId, FileName.DOCUMENT);
			//store = StoreFactory.getInstance().getDocumentStore(Long.parseLong(this.getDocOwnerZUID()), docId);
			sheetfileInfo = store.getFileInfo(docId, fileType, fileExtension);
			if(sheetfileInfo == null){
				logger.log(Level.INFO, "CREATING FILE INFO for document.zsheet, resourceid: {0}", new Object[]{resourceId});
				sheetfileInfo = store.createFileInfo(docId, fileType, fileExtension); // Assumed that, it is the first write ~mani
			}
		} catch (Exception e) {
			logger.log(Level.SEVERE, "Couldn't obtain Store for resource : " + resourceId, e);
			return false;
		}

		try {
			long resId = sheetfileInfo.getResourceId();
			HashMap<String, Object> writeInfo = store.write(resId, sheetfileInfo);
			OutputStream os = (OutputStream) writeInfo.get("OS");

			try {
				if(!writerFunction.apply(os)) {
					try {
						store.delete(resId, fileType, fileExtension);
					} catch (Exception e) {
						logger.log(Level.SEVERE, "delete of zsheet failed: " + resourceId, e);
					}
					return false;
				}
			} catch (Exception e) {
				try {
					store.delete(resId, fileType, fileExtension);
				} catch (Exception exception) {
					logger.log(Level.SEVERE, "delete of zsheet failed: " + resourceId, e);
				}
				logger.log(Level.SEVERE, "Writing Workbook to zsheet failed for resource : " + resourceId, e);
				return false;
			}

			store.finishWrite(writeInfo);
		} catch (Exception e) {
			logger.log(Level.SEVERE, "Couldn't write zsheet to Store for resource : " + resourceId, e);
			return false;
		}

		return true;
	}

	public InputStream getThumbnailHtml() {

		try {
			DataObject dataObj = ZSStore.getFragmentDO(this.getDocOwner(), Long.parseLong(this.getDocId()));
			long resourceId = ZSStore.getFragmentId(dataObj, ZSStore.FILENAME_THUMBNAIL_HTML);
			if (resourceId > 0) {
				Long[] fragmentResourceIds = ZSStore.getAllFragmentIdS(dataObj);
				Store fragmentStore = this.getStore(fragmentResourceIds, FileName.THUMBNAIL_HTML);
				InputStream stream = this.getReadStream(fragmentStore, resourceId, FileName.THUMBNAIL_HTML, FileExtn.ZSF, null);
				ZipInputStream zipInputStream = new ZipInputStream(stream);
				zipInputStream.getNextEntry();
				return zipInputStream;
			} else {
				logger.log(Level.OFF, "thumbnail-html not present in fragments, for {0}", new Object[]{this.getResourceKey()});
			}
		} catch (Exception e) {
			logger.log(Level.OFF, "problem in reading thumbnail-html", e);
			logger.log(Level.OFF, "problem in reading thumbnail-html for {0}", new Object[]{this.getResourceKey()});
		}
		return null;
	}

	public boolean writeThumbnailImage(byte[] bytes) {
		try {
			Long thumbnail_html_id = this.getFragmentId(FileName.THUMBNAIL_IMAGE, true, null);
			Map writeInfo = this.getWriteInfo(thumbnail_html_id, FileName.THUMBNAIL_IMAGE, FileExtn.ZSF, null);
			OutputStream os = (OutputStream) writeInfo.get("OS");
			try (ZipOutputStream zos = new ZipOutputStream(os)) {
				zos.putNextEntry(new ZipEntry(ZSStore.THUMBNAIL_IMAGE));
				zos.write(bytes);
			}
			this.finishWrite(writeInfo);
			return true;
		} catch (Exception e) {
			logger.log(Level.WARNING, "[THUMBNAIL CREATION DEBUG] Error while writing thumbnail image to fragments, rID:{0}", new Object[]{this.getResourceId()});
			logger.log(Level.WARNING, "[THUMBNAIL CREATION DEBUG] Error while writing thumbnail image to fragments", e);
		}
		return false;
	}

	public void deleteThumbnailHtml() {
		try {
			final Long fragmentId = this.getFragmentId(FileName.THUMBNAIL_HTML, false, null);
			if (fragmentId > 0) {
				this.deleteFile(fragmentId, FileName.THUMBNAIL_HTML, FileExtn.ZSF, null);
			}
		} catch (Exception e) {
			logger.log(Level.WARNING, "[THUMBNAIL CREATION DEBUG] Error while deleting thumbnail Html from fragments, rID:{0}", new Object[]{this.getResourceId()});
			logger.log(Level.WARNING, "[THUMBNAIL CREATION DEBUG] Error while deleting thumbnail Html from fragments", e);
		}
	}

	public InputStream getThumbnailImage() {
		try {
			DataObject dataObj = ZSStore.getFragmentDO(this.getDocOwner(), Long.parseLong(this.getDocId()));
			long resourceId = ZSStore.getFragmentId(dataObj, ZSStore.FILENAME_THUMBNAIL_IMAGE);
			if (resourceId > 0) {
				Long[] fragmentResourceIds = ZSStore.getAllFragmentIdS(dataObj);
				Store fragmentStore = this.getStore(fragmentResourceIds, FileName.THUMBNAIL_IMAGE);
				InputStream stream = this.getReadStream(fragmentStore, resourceId, FileName.THUMBNAIL_IMAGE, FileExtn.ZSF, null);
				ZipInputStream zipInputStream = new ZipInputStream(stream);
				zipInputStream.getNextEntry();
				return zipInputStream;
			} else {
				logger.log(Level.OFF, "thumbnail-image not present in fragments, for {0}", new Object[]{this.getResourceKey()});
			}
		} catch (Exception e) {
			logger.log(Level.OFF, "problem in reading thumbnail-image", e);
			logger.log(Level.OFF, "problem in reading thumbnail-image for {0}", new Object[]{this.getResourceKey()});
		}
		return null;
	}

	public JSONObjectWrapper getWorkbookProperties()
	{
		try
		{
			return this.getWorkbook(null).getWorkbookPropertiesJSON(this);
		}
		catch(Exception e)
		{
			logger.log(Level.SEVERE, "[WORKBOOK_PROPERTIES] Error while reading workbook properties from sheet order file", e);
			return new JSONObjectWrapper();
		}
	}

	/**
	 *
	 * @param associatedSheetName
	 * @param insertIfNotFound
	 * @return
	 * @throws Exception
	 */
	public Long getCellEditHistoryFragmentId(String associatedSheetName, boolean insertIfNotFound) throws Exception
	{
		String fileName = ZSStore.getCellEditHistoryFileName(associatedSheetName);
		return getFragmentId(FileName.CELL_EDIT_HISTORIES, insertIfNotFound, fileName);
	}

	/**
	 * Returns the custom colors in reverse order. i.e latest color will be on the top
	 * This will return empty list for remote documents
	 * @return
	 * @throws Exception
	 */
	public List<String> getCustomColors() throws Exception
	{
		if(!this.isRemoteMode() && !this.isPublicTemplate())
		{
			List<String> customColors = RedisHelper.getCustomColors(this.getResourceKey());
			Collections.reverse(customColors);
			String additionalInfoStr = this.getResourceAdditionalInfo();
			if(additionalInfoStr != null)
			{
				JSONObjectWrapper additionalInfo = new JSONObjectWrapper(additionalInfoStr);
				JSONArrayWrapper customColorsArr = additionalInfo.has(Constants.CUSTOM_COLORS) ? additionalInfo.getJSONArray(Constants.CUSTOM_COLORS) : new JSONArrayWrapper();
				for(int i=0; i<customColorsArr.length(); i++)
				{
					customColors.add(i, customColorsArr.getString(i));
				}
				Collections.reverse(customColors);
			}
			return customColors;
		}
		return new ArrayList<>();
	}

	public void updateCustomColors(String customColor) throws Exception
	{
		RedisHelper.setCustomColors(this.getResourceKey(), customColor);
	}

	public void updateCustomColors(JSONArrayWrapper customColors) throws Exception
	{
		List<String> existingCustomColors = RedisHelper.getCustomColors(this.getResourceKey());
//		RedisHelper.deleteCustomColors(this.getResourceKey());
		for(int i=0; i<customColors.length(); i++)
		{
			if(customColors.getString(i) != null && !existingCustomColors.contains(customColors.getString(i)))
			{
				RedisHelper.setCustomColors(this.getResourceKey(), customColors.getString(i));
			}
		}
	}

	/**
	 * Update the custom colors from redis to document settings and clear them from redis cache.
	 * Custom colors will not be updated for remote books. However, Auth remote books will store custom colors if any.
	 * @throws Exception
	 */
	public void updateCustomColorsToDocumentSettings() throws Exception
	{
		try
		{
			if(!RemoteUtils.isRemoteDocument(this.docOwner) && !this.isPublicTemplate())
			{
				String rid = this.getResourceId();
				List<String> customColors = RedisHelper.getCustomColors(rid);
				Collections.reverse(customColors);
				if(customColors.isEmpty())
				{
					return;
				}
				String additionalInfoStr = ZohoFS.getResourceAdditionalInfo(this.getDocsSpaceId(), rid);
				JSONObjectWrapper additionalInfo = additionalInfoStr != null ? new JSONObjectWrapper(additionalInfoStr) : new JSONObjectWrapper();
				JSONArrayWrapper customColorsArr = additionalInfo.has(Constants.CUSTOM_COLORS) ? additionalInfo.getJSONArray(Constants.CUSTOM_COLORS) : new JSONArrayWrapper();
				List<String> existingColors = new ArrayList<>();
				for(int i = 0; i < customColorsArr.length(); i++)
				{
					existingColors.add(customColorsArr.getString(i));
				}
				// ENSURE THE LATEST COLORS ARE AVAILABLE BY REPLACING OLD ONE IF LIMIT EXCEEDED
				if(existingColors.size() + customColors.size() > EngineConstants.CUSTOM_COLORS_LIMIT)
				{
					int index = existingColors.size() + customColors.size() - EngineConstants.CUSTOM_COLORS_LIMIT;
					existingColors.subList(0, index).clear();
				}
				existingColors.addAll(customColors);
				customColorsArr = new JSONArrayWrapper(existingColors);
				if(customColorsArr.length() > 0)
				{
					ZohoFS.updateResourceAdditionalInfo(this.getDocsSpaceId(), rid, Constants.CUSTOM_COLORS, customColorsArr.toString());
				}
				RedisHelper.deleteCustomColors(rid);
			}
		}
		catch(Exception e)
		{
			logger.log(Level.SEVERE, "[CUSTOM_COLORS][Exception] Exception while saving custom colors to workbook additional info", e);
		}
	}

	public boolean updateSearchFileInDocStore(Map<String, Set<String>> asn_contentSet_map, Workbook workbook) {
		BiConsumer<Set<String>, OutputStream> toCSV = (s, os) -> {
			try {
				BufferedWriter osw = new BufferedWriter(new OutputStreamWriter(os));
				CSVPrinter csvPrinter = new CSVPrinter(osw, CSVFormat.DEFAULT);
				for (String content : s) {
					if(content != null) {
						csvPrinter.print(content);
					}
				}
				osw.flush();
			} catch (Exception e) {
				logger.log(Level.OFF, "", e);
			}
		};
		File tempSearchFile = null;//EngineConstants.TEMPODSPATH+File.separator+new Random().nextInt(Integer.MAX_VALUE)+"-"+System.currentTimeMillis()+File.separator
		try {
			tempSearchFile = Files.createTempFile(FileName.SEARCH_FL.toString(), EngineConstants.FILEEXTN_ZIP).toFile();
			Set<String> all_asns = Arrays.stream(workbook.getSheets()).map(s->s.getAssociatedName()).collect(Collectors.toSet());
			try(InputStream searchFileIN = getSearchFileInDocumentStore()) {
				if (searchFileIN == null) {
					if(all_asns.size() == asn_contentSet_map.size()) {
						//new document
						try (ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(tempSearchFile))) {
							for (String asn : asn_contentSet_map.keySet()) {
								zipOutputStream.putNextEntry(new ZipEntry(asn + ".csv"));//No I18N
								toCSV.accept(asn_contentSet_map.get(asn), zipOutputStream);
							}
						}
					} else {
						//old document
						try (ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(tempSearchFile))) {
							for (String asn : all_asns) {
								zipOutputStream.putNextEntry(new ZipEntry(asn + ".csv"));//No I18N
								Set<String> set = new HashSet<>();
								Sheet sheet = workbook.getSheetByAssociatedName(asn);
								RangeIterator rangeIterator = new RangeIterator(sheet, 0, 0, sheet.getUsedRowIndex(), sheet.getUsedColumnIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, true, false, true, false, true);
								while (rangeIterator.hasNext()) {
									ReadOnlyCell next = rangeIterator.next();
									Cell cell = next.getCell();
									if (cell != null && cell.getValue() != null && cell.getValue().getType() == Cell.Type.STRING) {
										set.add((String) cell.getValue().getValue());
									}
								}
								if (!set.isEmpty()) {
									toCSV.accept(set, zipOutputStream);
								}
							}
						}
					}
				} else {
					try (ZipInputStream zipInputStream = new ZipInputStream(searchFileIN); ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(tempSearchFile))) {
						ZipEntry zipEntry;
						Set<String> writtenASN = new HashSet<>();
						while ((zipEntry = zipInputStream.getNextEntry()) != null) {
							String asn = zipEntry.getName().substring(0, zipEntry.getName().indexOf("."));
							if(!all_asns.contains(asn)) {
								continue;//sheet removed
							}
							zipOutputStream.putNextEntry(new ZipEntry(zipEntry.getName()));
							writtenASN.add(asn);
							if(asn_contentSet_map.containsKey(asn)) {
								toCSV.accept(asn_contentSet_map.get(asn), zipOutputStream);
							} else {
								byte[] buffer = new byte[2048];
								int len = 0;
								while ((len = zipInputStream.read(buffer)) > 0) {
									zipOutputStream.write(buffer, 0, len);//sheet unchanged
								}
							}
						}
						Set<String> temp_asns = new HashSet<>(asn_contentSet_map.keySet());
						temp_asns.removeAll(writtenASN);
						for (String asn : temp_asns) {//sheet added
							zipOutputStream.putNextEntry(new ZipEntry(asn+".csv"));//No I18N
							toCSV.accept(asn_contentSet_map.get(asn), zipOutputStream);
						}
					}
				}
			}
			try (InputStream in = new FileInputStream(tempSearchFile)){
				writeSearchFileToDocumentStore(in);
			}
			return true;
		} catch (Exception e) {
			logger.log(Level.SEVERE, "write of search_file to docstore failed : ".concat(resourceId), e);
			return false;
		} finally {
			if(tempSearchFile != null) {
				tempSearchFile.delete();
			}
		}
	}

	public boolean copySearchFileFromDocumentStore(OutputStream os) {
		try(InputStream inputStream = getSearchFileInDocumentStore()) {
			if(inputStream != null) {
				IOUtils.copy(inputStream, os);
				return true;
			}
		} catch (Exception e) {
			logger.log(Level.OFF, getResourceId(), e);
		}
		return false;
	}

	public boolean writeSearchFileToDocumentStore(InputStream in) {
		try {
			Long docId = Long.valueOf(this.getDocId());
			Store store = this.getStore(docId, FileName.DOCUMENT);
			SheetFileInfo sheetfileInfo = store.getFileInfo(docId, FileName.SEARCH_FL.toString(), EngineConstants.FILEEXTN_ZIP);
			if (sheetfileInfo == null) {
				if(in == null) {
					return true;
				}
				logger.log(Level.INFO, "creating file info for search_file, resourceid: {0}", new Object[]{resourceId});
				sheetfileInfo = store.createFileInfo(docId, FileName.SEARCH_FL.toString(), EngineConstants.FILEEXTN_ZIP);//todo check if it's required.
			}
			if (in == null) {
				//delete on revert
				store.delete(sheetfileInfo.getResourceId(), FileName.SEARCH_FL.toString(), EngineConstants.FILEEXTN_ZIP);
				return true;
			}

			HashMap<String, Object> writeInfo = store.write(sheetfileInfo.getResourceId(), sheetfileInfo);
			try (OutputStream os = (OutputStream) writeInfo.get("OS")){
				IOUtils.copy(in, os);
			} finally {
				store.finishWrite(writeInfo);
			}
			return true;
		} catch (Exception e) {
			logger.log(Level.SEVERE, "write of search_file to docstore failed : ".concat(resourceId), e);
			return false;
		}
	}

	public InputStream getSearchFileInDocumentStore() {
		try {
			Long docId = Long.valueOf(this.getDocId());
			Store store = this.getStore(docId, FileName.DOCUMENT);
			SheetFileInfo sheetfileInfo = store.getFileInfo(docId, FileName.SEARCH_FL.toString(), EngineConstants.FILEEXTN_ZIP);
			if (sheetfileInfo == null) {
				logger.log(Level.OFF, "search_file not present in document store {0}", getResourceId());
				return null;
			} else {
				return store.read(sheetfileInfo.getResourceId(), FileName.SEARCH_FL.toString(), EngineConstants.FILEEXTN_ZIP);
			}
		} catch (Exception e) {
			logger.log(Level.OFF, getResourceId(), e);
			return null;
		}
	}
	public boolean copySearchFileFromDocumentToVersionStore(long versionId) {
		try(InputStream inputStream = getSearchFileInDocumentStore()) {
			if(inputStream != null) {
				Map writeInfo = getWriteInfo(versionId, FileName.VSEARCH_FL, FileExtn.ZIP, null);
				try(OutputStream os = (OutputStream) writeInfo.get("OS")) {
					IOUtils.copy(inputStream, os);
				}
				finishWrite(writeInfo);
				return true;
			}
		} catch (Exception e) {
			logger.log(Level.OFF, String.join("", getResourceId(), String.valueOf(versionId)), e);
		}
		return false;
	}

	public boolean copySearchFileFromVersionToDocumentStore(long versionId) {
		try {
			Store store = this.getStore(versionId, FileName.VERSION);
			try(InputStream is = this.getReadStream(store, versionId, FileName.VSEARCH_FL, FileExtn.ZIP, null)) {
				writeSearchFileToDocumentStore(is);
				return true;
			}
		} catch (Exception e) {
			logger.log(Level.OFF, String.join("", getResourceId(), String.valueOf(versionId)), e);
		}
		return false;
	}

	/**
	 * Returns the IAM User object of the creator of the container. This will return null if the creator is a template or anonymous user or null.
	 * @return
	 */
	public User getCreatorIAMUser()
	{
		return (this.getCreatorZUID() == null || DocumentUtils.isAnonUser(this.getCreatorZUID())) ||  "template".equals(this.getCreatorZUID()) ? null : DocumentUtils.getZUserObject(Long.parseLong(this.getCreatorZUID())); // No I18N
	}

	/**
	 * Returns JSON String containing the additional info set to the container or "NA" if the container is a template or null in other cases.
	 * @return
	 * @throws Exception
	 */
	public String getResourceAdditionalInfo() throws Exception
	{
		return ZohoFS.getResourceAdditionalInfo(this.getDocsSpaceId(), this.getResourceId());
	}

	public void updateFocusInfo(JSONObjectWrapper focusInfoJson) throws Exception
	{
		Workbook workBook = this.getWorkbookForSave();
		if(workBook == null)
		{
			return;
		}
		JSONObjectWrapper focusJson = focusInfoJson.has(JSONConstants.FOCUS_OBJECT) ? focusInfoJson.getJSONObject(JSONConstants.FOCUS_OBJECT) : null;
		if (focusJson != null)
		{
			String activeSheetId = focusInfoJson.getString(JSONConstants.SHEET_ID);
			Iterator<String> keys = focusJson.keys();

			//for activeSheet only,
			int activeSheet_positionTop = 0;
			int activeSheet_positionLeft = 0;
			int activeSheet_row = 0;
			int activeSheet_col = 0;

			while (keys.hasNext())
			{
				String sheetId = (String) keys.next();

				JSONObjectWrapper sheetObj = focusJson.getJSONObject(sheetId);

				int sheet_PositionBtm = sheetObj.has("positionTop") ? sheetObj.getInt("positionTop") : 0; //NO I18N
				int sheet_PositionLft = sheetObj.has("positionLeft") ? sheetObj.getInt("positionLeft") : 0; //NO I18N
				int sheet_activeRIdx = sheetObj.has("activeRowIndex") ? sheetObj.getInt("activeRowIndex") : 0; //NO I18N
				int sheet_activeCIdx = sheetObj.has("activeColIndex") ? sheetObj.getInt("activeColIndex") : 0; //NO I18N
				//int sheet_freezedRow = sheetObj.has("FreezedRow") ? sheetObj.getInt("FreezedRow") : 0; //NO I18N
				//int sheet_freezedCol = sheetObj.has("FreezedCol") ? sheetObj.getInt("FreezedCol") : 0; //NO I18N

				Sheet sheet = workBook.getSheetByAssociatedName(sheetId);
				if (sheet != null)
				{
					String sheetName = sheet.getName();
					WorkbookSettings workBookSettings = workBook.getWorkbookSettings();
					workBookSettings.setScrollPosition(sheetName, sheet_PositionBtm, sheet_PositionLft);
					workBookSettings.setActiveCellIndex(sheetName, sheet_activeRIdx, sheet_activeCIdx);

				}
				//for active sheet only : -- Finding active sheet Info
				if (sheetId.equals(activeSheetId))
				{
					activeSheet_positionTop = sheetObj.has("positionTop") ? sheetObj.getInt("positionTop") : 0; //NO I18N
					activeSheet_positionLeft = sheetObj.has("positionLeft") ? sheetObj.getInt("positionLeft") : 0; //NO I18N
					activeSheet_row = sheetObj.has("activeRowIndex") ? sheetObj.getInt("activeRowIndex") : 0; //NO I18N
					activeSheet_col = sheetObj.has("activeColIndex") ? sheetObj.getInt("activeColIndex") : 0; //NO I18N
				}

			}
			//// set the active sheet cursor position
			Sheet activeSheet = workBook.getSheetByAssociatedName(activeSheetId);
			if (activeSheet != null)
			{
				// Need to set ActiveSheet name in container to be written as metaInfo.
				this.setCurrCursorPosSheetCodeName(activeSheetId);
				///////////////////////////////////////////////////
				FocusObject focusObj = new FocusObject(activeSheet_row, activeSheet_col, activeSheet_positionLeft, activeSheet_positionTop);
				this.setFocusObject(focusObj);
			}
		}
	}

	/// Complexity Level of Workbook
	public enum Complexity {
		LOW(1),
		MEDIUM(2),
		HIGH(3),

		LOW_ADMIN(10),
		MEDIUM_ADMIN(20),
		HIGH_ADMIN(30);

		private final int id;

		Complexity(int id) {
			this.id = id;
		}

		public int getComplexityIndex() {
			return this.id;
		}

		public static Complexity getComplexity(int index) {
			for (Complexity c : Complexity.values()) {
				if (c.getComplexityIndex() == index) {
					return c;
				}
			}
			throw new IllegalArgumentException("Invalid complexity index: " + index); // NO I18N
		}

		public static boolean isAdminLevel(int complexityIndex) {
			return complexityIndex % 10 == 0;
		}

		public boolean isAdminLevel() {
			return isAdminLevel(this.id);
		}
	}

	public static void setComplexity(String docsSpaceId, String resourceId, Complexity level) throws Exception {
		if(level == null)
		{
			return;
		}
		setComplexity(docsSpaceId, resourceId, level.id);
	}

	public static void setComplexity(String docsSpaceId, String resourceId, int level) throws Exception {
		ZohoFS.updateResourceAdditionalInfo(docsSpaceId, resourceId, Constants.COMPLEXITY_LEVEL, String.valueOf(level));
	}

	/**
	 * This method is to find the complexity level before creating container for server-group redirection
	 */
	public static Complexity getComplexity(String docSpaceId, String resourceId) {
		Complexity complexity = getComplexity_ForSave(docSpaceId, resourceId);
		if(complexity != null && complexity.isAdminLevel())
		{
			complexity = Complexity.getComplexity(complexity.getComplexityIndex() / 10);
		}

		return complexity;
	}

	private static Complexity getComplexity_ForSave(String docSpaceId, String resourceId) {
		int existingComplexityLevel;

		try {
			String additionalInfoStr = ZohoFS.getResourceAdditionalInfo(docSpaceId, resourceId);
			if (additionalInfoStr != null) {
				JSONObjectWrapper additionalInfo = new JSONObjectWrapper(additionalInfoStr);
				if(additionalInfo.has(Constants.COMPLEXITY_LEVEL)) {
					existingComplexityLevel = additionalInfo.getInt(Constants.COMPLEXITY_LEVEL);
					return Complexity.getComplexity(existingComplexityLevel);
				}
			}
		}
		catch (Exception e) {
			logger.log(Level.SEVERE, "[Complexity_Level][Exception] Exception while getComplexity for workbook rid : {0}, Exception : {1} ", new Object[]{resourceId, e.getMessage()});
			// return server Complexity if any exception occurs
			return Complexity.getComplexity(EngineConstants.SERVER_COMPLEXITY);
		}

		return null;
	}

	public void updateSpreadsheetComplexity() {
		try {
			Workbook workbook = this.getWorkbookForSave();
			if (workbook == null) {
				return;
			}

			Complexity existingComplexityLevel = getComplexity_ForSave(this.getDocsSpaceId(), this.getResourceId());
			if (existingComplexityLevel != null && existingComplexityLevel.isAdminLevel()) {
				return;  // No need to update, as it's an admin-level complexity
			}
			Complexity currentLevel;

			// Determine current complexity level based on workbook's used cell count
			int usedCellCount = workbook.getUsedCellCount();
			if (usedCellCount > 5000000) {
				currentLevel = Complexity.HIGH;
			} else if (usedCellCount > 1000000) {
				currentLevel = Complexity.MEDIUM;
			} else {
				currentLevel = Complexity.LOW;
			}

			if (existingComplexityLevel != currentLevel) {
				setComplexity(this.getDocsSpaceId(), this.getResourceId(), currentLevel);
				logger.log(Level.INFO, "[Complexity_Level] Updated complexity level while save, from {0} to {1}", new Object[]{existingComplexityLevel, currentLevel});
			}
		} catch (Exception e) {
			logger.log(Level.SEVERE, "[Complexity_Level][Exception] Exception while update workbook additional info for complexity level, rid : {0}, Exception : {1}", new Object[]{this.getResourceId(), e.getMessage()});
		}
	}
	/// ////
}
