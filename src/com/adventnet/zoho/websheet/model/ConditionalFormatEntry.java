//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.ext.functions.RelativeRank;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.CellStyleHolder;
import com.adventnet.zoho.websheet.model.style.ConditionalStyleResponse;
import com.adventnet.zoho.websheet.model.style.MapStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.util.*;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.parser.Node;
import com.zoho.sheet.util.ConditionFormatUtils;
import com.zoho.sheet.util.DataValidationUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ConditionalFormatEntry {
    public enum Entry_Type
    {
        AUTO_MINIMUM,
        AUTO_MAXIMUM,   
        MINIMUM,
        MAXIMUM,
        PERCENTILE,
        NUMBER,
        PERCENT,
        FORMULA
    }
    
    private final boolean hasFormula;
    private final Entry_Type type;  
    private Object value;
    
    private Object obj;
    
    // COLORSCALE
    public ConditionalFormatEntry(Entry_Type type, Object value, ZSColor color)
    {
        this(type, value);
        this.obj = new ColorScaleObj(color);
    }
    
    //ICONSET
    public ConditionalFormatEntry(Entry_Type type, Object value, String name, Integer id, String iconCriteria)
    {
        this(type, value);
        this.obj = new IconSetObj(name, id, iconCriteria);
    }
    
    public ConditionalFormatEntry(Entry_Type type, Object value)
    {
        this.type = type;
        this.value = value;
        this.hasFormula = (value instanceof ExpressionImpl);
    }
    
    public Object getValue()
    {
        return value;
    }
    
    public String getValueString(Workbook workbook)
    {
        if(hasFormula)
        {
            return FormulaUtil.getFormula(((Expression)value).getNode(), workbook);
        }
        return (String)value;
    }
  
    public boolean hasFormula()
    {
        return hasFormula;
    }
    
    public Condition getCondition(Sheet sheet)
    {
        return new Condition(sheet, 0, 0, ConditionalFormatOperator.ConditionType.FORMULA, -1, regenerateConditionalExpression(sheet.getWorkbook(), 0, 0));
    }
    
    public boolean isDependsOnOtherCell()
    {
        return hasFormula && FormulaUtil.isContainsVarNode(((Expression)value).getNode());
    }
    
    public Entry_Type getType()
    {
        return type;
    }

    public Object getCSEObj()
    {
        return obj;
    }
    
    public void changeFormulaCSE(Workbook workbook, String value)
    {
        if(value.startsWith("="))
        {
            value = value.substring(1);
        }
        this.value = new ExpressionImpl(workbook, value, 0, 0, true, CellReference.ReferenceMode.A1);
    }

    @Override
    public String toString() {
        return "ConditionalFormatEntry{" + "type=" + type + ", value=" + value + ", obj=" + obj + '}'; // No I18N
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 89 * hash + Objects.hashCode(this.type);
        hash = 89 * hash + Objects.hashCode(this.value);
        hash = 89 * hash + Objects.hashCode(this.obj);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ConditionalFormatEntry other = (ConditionalFormatEntry) obj;
        if (this.type != other.type) {
            return false;
        }
        if (!Objects.equals(this.value, other.value)) {
            return false;
        }
        return Objects.equals(this.obj, other.obj);
    }

    @Override
    protected ConditionalFormatEntry clone() 
    {
        ConditionalFormatEntry o = null;
        Object newValue = value;
        if(newValue instanceof Expression) {
            Expression expr = (Expression) newValue;
            newValue = FormulaUtil.cloneExpression(expr);
        }

        if(this.obj != null)
        {
            if(this.obj instanceof ColorScaleObj)
            {
                o = new ConditionalFormatEntry(type, newValue, ((ColorScaleObj)this.obj).color);
            }
            else if(this.obj instanceof IconSetObj)
            {
                o = new ConditionalFormatEntry(type, newValue, ((IconSetObj)this.obj).name, ((IconSetObj)this.obj).id, ((IconSetObj)this.obj).iconCriteria.name());
            }
        }
        else{
            o = new ConditionalFormatEntry(type, newValue);
        }
        
        return o; 
    }

    public boolean changeTableNodeIfPresent(Workbook srcWorkbook, Map<String, String> tableNameMap) {
        if(value instanceof Expression) {
            Expression expr = (Expression) value;
            if(expr.hasStructuredReference(srcWorkbook)) {
                return FormulaUtil.replaceTableNameInExpression(expr.getNode(), tableNameMap);
            }
        }
        return false;
    }

    public boolean changeTableColumnsIfPresent(Workbook srcWorkbook, Table table, Map<String, String> colNameMap) {
        boolean isChanged = false;
        if(value instanceof Expression) {
            Expression expr = (Expression) value;
            if(expr.hasStructuredReference(srcWorkbook)) {
                FormulaUtil.replaceColHeaderInFooterColExpression(expr.getNode(), table, colNameMap);
                isChanged = true;
            }
        }
        return isChanged;
    }

    public boolean hasStructuredReferences(Workbook srcWorkbook) {
        if(value instanceof Expression) {
            Expression expression = (Expression) value;
            if(expression.hasStructuredReference(srcWorkbook)) {
                return true;
            }
        }

        return false;
    }

    public boolean convertStructuredReferencesToRange(Workbook workbook, String tableName, int baseRow, int baseCol) {
        if(value instanceof Expression) {
            Expression expression = (Expression) value;
            if(expression.hasStructuredReference(workbook)) {
                if(FormulaUtil.hasTableReference(workbook, expression.getNode(), tableName)) {
                    String expression_string = FormulaUtil.getFormula(expression.getNode(), workbook, null, baseRow, baseCol, true, true);
                    this.value = new ExpressionImpl(workbook, expression_string, baseRow, baseCol, false, CellReference.ReferenceMode.R1C1);
                    return true;
                }
            }
        }

        return false;
    }

    public static Object validateConditionalFormatEntry(Workbook workBook, int rowIndex, int colIndex, Entry_Type type, String value)
    {
        if(type == Entry_Type.PERCENT || type == Entry_Type.PERCENTILE || type == Entry_Type.NUMBER)
        {
            try{
                Value valObj = Value.getInstance(value, workBook.getSpreadsheetSettings());
                double val = FunctionUtil.objectToNumber(valObj.getValue()).doubleValue();
                if(type != Entry_Type.NUMBER && (val < 0 || val >100))
                {
                    throw new IllegalArgumentException(DataValidationUtils.DV_INPUTERROR+"1");
                }
                value = String.valueOf(val);
            }
            catch(EvaluationException e)
            {
                //In case of formula in types (PERCENT, PERCENTILE, NUMBER)
                return new ExpressionImpl(workBook, value, rowIndex, colIndex, true, CellReference.ReferenceMode.A1);
            }
        }
        else if(type == Entry_Type.FORMULA)
        {
            if(value.startsWith("="))
            {
                value = value.substring(1);
            }
            if(value.isEmpty())
            {
                throw new IllegalArgumentException(ConditionFormatUtils.CF_FIELDSBLANK);
            }

            return new ExpressionImpl(workBook, value, rowIndex, colIndex, true, CellReference.ReferenceMode.A1);
        }
        return value;
    }
    
    public String regenerateConditionalExpression(Workbook workbook, int rowIndex, int colIndex)
    {
        SpreadsheetSettings spreadsheetSettings = workbook.getSpreadsheetSettings();
        if(hasFormula)
        {
            return LocaleUtil.getLocalizedFormula(FormulaUtil.getLocalizedFormula(((Expression)value).getNode(), workbook, null, rowIndex, colIndex), spreadsheetSettings);
        }
        else 
        {
            Value valueObj = Value.getInstance((String)value, SpreadsheetSettings.defaultSpreadsheetSettings);
            if(valueObj.getValue() instanceof Number)
            {
                return valueObj.getValueString(spreadsheetSettings);
            }
            else{
                return (String)value;
            }
        }
    }

    private String getValueToWrite(Workbook workbook, int baseRowIndex, int baseColIndex) {
        if(hasFormula)// && (value instanceof Expression))
        {
            return "=" + FormulaUtil.getFormula(((Expression)value).getNode(), workbook, null, baseRowIndex, baseColIndex, false, false);
        }
        else{
            Value valueObj = Value.getInstance((String)value, SpreadsheetSettings.defaultSpreadsheetSettings);
            return valueObj.getValueString(SpreadsheetSettings.defaultSpreadsheetSettings);
        }
    }
    
    private static List<ConditionalFormatEntry> adjustFormulaCSEntries(Sheet sheet, List<ConditionalFormatEntry> entryList, int rowIndex, int colIndex) throws EvaluationException
    {
        List<ConditionalFormatEntry> newCSEList = new ArrayList<>();
        for(ConditionalFormatEntry cse : entryList)
        {
            ConditionalFormatEntry newCSE = cse;
            if(cse.hasFormula())
            {
                Cell dummyCell = new CellImpl(new Row(sheet, rowIndex), new Column(sheet, colIndex));

                Node node = ((ExpressionImpl)cse.getValue()).getNode();
                Object result = new ZSEvaluator(true).evaluate(node, dummyCell, false, false);
                result = (result instanceof Value) ? ((Value) result).getValue() : result;
                if(result == null)
                {
                    result = 0;
                }
                if(result instanceof Boolean || result instanceof Date || result instanceof Number)
                {
                    result = FunctionUtil.objectToNumber(result).doubleValue();
                    Object obj = cse.getCSEObj();
                    if(obj instanceof ColorScaleObj)
                    {
                        newCSE = new ConditionalFormatEntry(cse.getType(), String.valueOf(result), ((ColorScaleObj)obj).getZSColor());
                    }
                    else if(obj instanceof IconSetObj)
                    {
                        newCSE = new ConditionalFormatEntry(cse.getType(), String.valueOf(result), ((IconSetObj)obj).getName(), ((IconSetObj)obj).getId(), ((IconSetObj)obj).getIconCriteria().name());
                    }
                    else
                    {
                        newCSE = new ConditionalFormatEntry(cse.getType(), String.valueOf(result));
                    }
                }
                else
                {
                    throw Cell.Error.EVAL.getThrowableObject();
                }
            }
            newCSEList.add(newCSE);
        }
        return newCSEList;
    }
    
    private static List<Double> getRangeSortedNumbers(Sheet sheet, SpecialRange specialRange, boolean isGetRangeMinMaxValue, boolean hasPercentileEntry)
    {
        List<Double> finalList = new ArrayList<>();
        for(DataRange range : specialRange.getRanges())
        {
            List<Double> listOfNums = new ArrayList<>();
            if(isGetRangeMinMaxValue && !hasPercentileEntry 
                    && range.getConditionalMinValue() != null && range.getConditionalMaxValue() != null)
            {
                finalList.add(range.getConditionalMinValue());
                finalList.add(range.getConditionalMaxValue());
                continue;
            }
            
            // TODO : Why range iterator is not used???
            int startRow = range.getStartRowIndex();
            int startCol = range.getStartColIndex();
            int endRow = Math.min(range.getEndRowIndex(), sheet.getUsedRowIndex());
            int endCol = Math.min(range.getEndColIndex(), sheet.getUsedColumnIndex());
            
            if(endRow < startRow || endCol < startCol)
            {
                continue;
            }
            
            int repeatedRows;
            for (int row = startRow; row <= endRow; row += repeatedRows)
            {
                ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(row);
                Row tempRow = rRow.getRow();
                repeatedRows = rRow.getRowsRepeated();
                int rowsRepeated = Math.min(repeatedRows, endRow - row + 1);
                if(tempRow != null)
                {
                    ReadOnlyCell rCell;
                    for (int col = startCol; col <= endCol; col += rCell.getColsRepeated())
                    {
                        rCell = sheet.getReadOnlyCellFromShell(tempRow.getRowIndex(), col);
                        int colsRepeated = Math.min(rCell.getColsRepeated(), endCol - col + 1);
                        Cell targetCell = rCell.getCell();
                        Object cellValue = (targetCell != null) ? targetCell.getValue().getValue() : null;
                        if (cellValue != null && ((cellValue instanceof Number) || (cellValue instanceof Date) || (cellValue instanceof Boolean)))
                        {
                            try
                            {
                                double d = FunctionUtil.objectToNumber(cellValue).doubleValue();
                                // DO WE REALLY NEED DUPLICATE VALUES IN THIS LIST?
                                // Yes we need to add since for PERCENTILE we need all the values in the range is neccesary.
                                for(int repeat = rowsRepeated * colsRepeated; repeat > 0; repeat--)
                                {
                                    listOfNums.add(d);
                                }
                            }catch(EvaluationException e){} // Do Nothing here...
                        }
                    }
                }
            }
            if(!listOfNums.isEmpty())
            {
                Collections.sort(listOfNums);
                if(hasPercentileEntry)
                {
                    finalList.addAll(listOfNums);
                }
                else
                {
                    finalList.add(listOfNums.get(0));
                    if(listOfNums.size() > 1)
                    {
                        finalList.add(listOfNums.get(listOfNums.size() - 1));
                    }
                }
                range.setConditionalValues(listOfNums.get(0), listOfNums.get(listOfNums.size() - 1)); 
            }
        }  
        Collections.sort(finalList);
        return finalList;
    }
    
    private static List<Double> adjustCSEntries(List<ConditionalFormatEntry> entryList, List<Double> sortedRangeNumbers, boolean isIconSetConditionalStyle) throws EvaluationException
    {
        List<Double> intervals = new ArrayList<>();
        
        try{
            boolean isNotEmpty = sortedRangeNumbers != null && !sortedRangeNumbers.isEmpty();
            Double minVal = isNotEmpty ? sortedRangeNumbers.get(0) : null;
            Double maxVal = isNotEmpty ? sortedRangeNumbers.get(sortedRangeNumbers.size() - 1) : null;

            /**
             * For IconSet, we shouldn't add first ConditionalFormatEntry's value in Intervals.
             * Because formatting of first ConditionalFormatEntry's should be applied by default,
             * when non of the other conditions match.
             * First ConditionalFormatEntry's is always of type PERCENT with value 0.
             */
            final int start_index = isIconSetConditionalStyle ? 1 : 0;
            for (int i = start_index; i < entryList.size(); i++)
            {
               ConditionalFormatEntry cse = entryList.get(i);

               Double value = null;
               if(cse.getType() == Entry_Type.NUMBER || cse.getType() == Entry_Type.FORMULA)
               { 
                    value = Double.valueOf((String)cse.getValue());
               }
               else if(isNotEmpty)
               {
                    if(cse.getType() == Entry_Type.AUTO_MINIMUM)
                   {
                        value = sortedRangeNumbers.get(0) < 0 ? sortedRangeNumbers.get(0) : 0.0;
                    }
                    else if(cse.getType() == Entry_Type.AUTO_MAXIMUM)
                    {
                        value = sortedRangeNumbers.get(sortedRangeNumbers.size() - 1) < 0 ? 0.0 : sortedRangeNumbers.get(sortedRangeNumbers.size() - 1);
                    }
                    else if(cse.getType() == Entry_Type.MINIMUM)
                    {
                       value = minVal;
                    }
                    else if(cse.getType() == Entry_Type.MAXIMUM)
                    {
                       value = maxVal;
                    }
                    else if(cse.getType() == Entry_Type.PERCENT)
                    {
                        value = minVal + ((maxVal - minVal) * Double.valueOf((String)cse.getValue())/100);
                    }
                    else //if(cse.getType() == ConditionalFormat.ConditionFormat_Entry_Type.PERCENTILE)
                                        {
                        value = RelativeRank.percentile(sortedRangeNumbers, Double.valueOf((String)cse.getValue())/100, false, true);
                    }
               }
               if(value != null)
               {
                   intervals.add(value);
               }
            }

            Collections.sort(intervals);
        }
        catch(NumberFormatException e)
        {
            throw Cell.Error.NUM.getThrowableObject();
        }
        return intervals;
    }
    
    private static boolean isSortNeeded(List<ConditionalFormatEntry> entryList)
    {   
        for(ConditionalFormatEntry cse : entryList)
        { 
            if(cse.getType() != Entry_Type.NUMBER && !cse.hasFormula())
            {
                return true;
            }
        }
        return false;
    }
    
    public static boolean updateConditionalStyleIntervals(Sheet sheet, ConditionalStyleObject csf, long currentTime) throws EvaluationException
    {
        ConditionalStyle cs = csf.getConditionalStyle();
        if(cs instanceof MapStyle)
        {
            return false;
        }
        List<Double> prevIntervals = csf.getIntervals();
        SpecialRange specialRange = csf.getSpecialRange();
        int rowIndex = specialRange.getRanges().get(0).getStartRowIndex();
        int colIndex = specialRange.getRanges().get(0).getStartColIndex();
        List<ConditionalFormatEntry> newEntrys = adjustFormulaCSEntries(sheet, cs.getConditionalStyleEntries(), rowIndex, colIndex);
        List<Double> sortedNumbers = isSortNeeded(newEntrys) ? getRangeSortedNumbers(sheet, specialRange, (specialRange.getTime() == currentTime), cs.hasPercentileEntry()) : null; 
        csf.setIntervals(adjustCSEntries(newEntrys, sortedNumbers, (cs instanceof IconSet)));
        csf.setIsResetRangeLevel(false);
        specialRange.setTime(currentTime);
        List<Double> currIntervals = csf.getIntervals();
        return !currIntervals.equals(prevIntervals);
    }
    
    public static boolean updateConditionalStyleResult(Sheet sheet, ReadOnlyCell rCell, ConditionalStyleObject cso, long currentTime, int csUID, Map<ConditionalStyleResponse.CellObject, ConditionalStyleResponse.ConditionalStyleCellStyles> cellStylesMap, boolean isIncludeCellStyle)
    {
        try{
            ConditionalStyle conditionalStyle = cso.getConditionalStyle();
            SpecialRange specialRange = cso.getSpecialRange();
            
            Cell cell = rCell.getCell();
            int rI = rCell.getRowIndex();
            int cI = rCell.getColIndex();

            ConditionalStyleResponse.CellObject cellObject = new ConditionalStyleResponse.CellObject(rI, cI);
            ConditionalStyleResponse.ConditionalStyleCellStyles csCellStyles = cellStylesMap.get(cellObject);

            boolean isNewCellObject = csCellStyles == null;
            // No need to consider colorScale result if BG color is already occured.
            boolean isContinued = !isNewCellObject && csCellStyles.iscsUidExists(csUID) && conditionalStyle.isConditionalStyleExists(csCellStyles);

            Object csResult = null;
            if(cell != null)
            {
                ConditionalStyleCellObject conditionalSpecialCellFormat = ((CellImpl)cell).getConditionalStyleCellObject();

                if(conditionalSpecialCellFormat == null)
                {
                    conditionalSpecialCellFormat = new ConditionalStyleCellObject();
                    ((CellImpl) cell).setConditionalStyleCellObject(conditionalSpecialCellFormat);
                }

                if(specialRange.getTime() != currentTime)
                {
                    boolean isUpdated = ConditionalFormatEntry.updateConditionalStyleIntervals(cell.getRow().getSheet(), cso, currentTime);
                    if(isUpdated)
                    {
                        return true;
                    }
                }
                
                Object cellValue = cell.getValue().getValue();
                if(cellValue != null)
                {
                    conditionalSpecialCellFormat.addConditionalStyleObjects(cso);
                    if(((cellValue instanceof Number) || (cellValue instanceof Date) || (cellValue instanceof Boolean)))
                    {
                        csResult = conditionalStyle.getResult(cell, cso);
                        if(csResult != null && (csResult instanceof CellStyle)) {
                            csResult = new CellStyleHolder((CellStyle)csResult, true);
                        }
                    }
                }
            }
                
            if(isIncludeCellStyle || csResult != null)
            {
                if(!isContinued)
                {
                    if(isNewCellObject)
                    {
                        csCellStyles = new ConditionalStyleResponse.ConditionalStyleCellStyles();
                        if(isIncludeCellStyle)
                        {
                            // Sending CellSTyle details too as client does not maintain both CF and CellSTyle props.
                            // Should be commented once handled in client.
                            csCellStyles.setCellStyle(sheet.getCellStyleReadOnly(rI, cI));
                        }
                        cellStylesMap.put(cellObject, csCellStyles);

                        Cell readOnlyCell = sheet.getReadOnlyCellFromShell(rI, cI).getCell();
                        if(readOnlyCell != null)
                        {
                            csCellStyles.setPatternTextColor(readOnlyCell.getContentColor());
                        }
                    }
                    if(csResult != null)
                    {
                        conditionalStyle.setConditionalStyleResult(csCellStyles, csResult);
                        csCellStyles.setIsHideText(cso.isHideText());
                    }
                    csCellStyles.setCSUID(csUID);
                }
                
                for(int r = rI; r < rI+rCell.getRowsRepeated(); r++)
                {
                    for(int c = cI; c < cI+rCell.getColsRepeated(); c++)
                    {
                        if(r != rI || c != cI)
                        {
                            cellObject = new ConditionalStyleResponse.CellObject(r, c);
                            ConditionalStyleResponse.ConditionalStyleCellStyles newCSCellStyles = cellStylesMap.get(cellObject);
                            if(newCSCellStyles == null)
                            {
                                newCSCellStyles = new ConditionalStyleResponse.ConditionalStyleCellStyles();
                                if(isIncludeCellStyle)
                                {
                                    // Sending CellSTyle details too as client does not maintain both CF and CellSTyle props.
                                    // Should be commented once handled in client.
                                    newCSCellStyles.setCellStyle(sheet.getCellStyleReadOnly(r, c));
                                }
                                cellStylesMap.put(cellObject, newCSCellStyles);
                                String ptc = csCellStyles.getPatternTextColor();
                                if(ptc != null)
                                {
                                    newCSCellStyles.setPatternTextColor(ptc);
                                }
                            }
                            if(newCSCellStyles.iscsUidExists(csUID) && conditionalStyle.isConditionalStyleExists(newCSCellStyles))
                            {
                                continue;
                            }
                            if(csResult != null)
                            {
                                conditionalStyle.setConditionalStyleResult(newCSCellStyles, csResult);
                                newCSCellStyles.setIsHideText(cso.isHideText());
                            }
                            newCSCellStyles.setCSUID(csUID);
                        }
                    }
                }
            }
        }
        catch(EvaluationException e){} // do nothing.
        return false;
    }
    
    public static boolean hasPercentileOrFormulaEntry(List<ConditionalFormatEntry> entries, boolean checkForFormulaEntry)
    {
        for(ConditionalFormatEntry entry : entries)
        {
            if(checkForFormulaEntry ? entry.hasFormula() : entry.getType() == Entry_Type.PERCENTILE)
            {
                return true;
            }
        }
        return false;
    }
    
    public String[] getCSAttributes()
    {
        String[] attrs = new String[]{"calcext:value","calcext:type","calcext:color", "fo:theme-color", "fo:color-tint",  "calcext:base-cell-address"}; //NO I18N
        return attrs;
    }
    
    public String[] getCSValues(Workbook workbook, int baseRowIndex, int baseColIndex)
    {
        String[] vals = new String[]{getValueToWrite(workbook, baseRowIndex, baseColIndex), getType().toString().toLowerCase(), ZSColor.getHexColor(((ColorScaleObj)getCSEObj()).getZSColor(), workbook.getTheme()), ((ColorScaleObj)getCSEObj()).getZSColor().getThemeColorToWrite(),((ColorScaleObj)getCSEObj()).getZSColor().getColorTintToWrite(), this.hasFormula() ? CellUtil.getCellReference(baseColIndex, baseRowIndex) : null};
        return vals;
    }
    
    public String[] getISAttributes(boolean isFirstEntry)
    {
        List<String> attributes = new ArrayList<>();
        if(isFirstEntry)
        {
            attributes.add("calcext:show-value"); //NO I18N
        }
        attributes.add("calcext:value"); //NO I18N
        attributes.add("calcext:type"); //NO I18N
        attributes.add("calcext:name"); //NO I18N
        attributes.add("calcext:id"); //NO I18N
        attributes.add("calcext:criteria"); //NO I18N
        attributes.add("calcext:base-cell-address"); //No I18N
        
        return attributes.toArray(new String[attributes.size()]);
    }
    
    public String[] getISValues(Workbook workbook, int baseRowIndex, int baseColIndex, boolean isFirstEntry, boolean isShowValue)
    {
        List<String> values = new ArrayList<>();
        if(isFirstEntry)
        {
            values.add(String.valueOf(isShowValue));
        }
        values.add(getValueToWrite(workbook, baseRowIndex, baseColIndex));
        values.add(getType().toString().toLowerCase());
        values.add(((IconSetObj)getCSEObj()).getName());
        values.add(String.valueOf(((IconSetObj)getCSEObj()).getId()));
        values.add(((IconSetObj)getCSEObj()).getIconCriteria().name());
        values.add(CellUtil.getCellReference(baseColIndex, baseRowIndex));
                
        return values.toArray(new String[values.size()]);
    }
    
    public String[] getDBAttributes()
    {
        String[] attrs = new String[]{"calcext:value","calcext:type", "calcext:base-cell-address"}; // No I18N
        return attrs;
    }
    
    public String[] getDBValues(Workbook workbook, int baseRowIndex, int baseColIndex)
    {
        String[] vals = new String[]{getValueToWrite(workbook, baseRowIndex, baseColIndex), getType().toString().toLowerCase().replace('_', '-'), this.hasFormula() ? CellUtil.getCellReference(baseColIndex, baseRowIndex) : null};
        return vals;
    }
    
    public static class ColorScaleObj
    {
        private final ZSColor color;

        public ColorScaleObj(ZSColor color){
            this.color = color;
        }

        public ZSColor getZSColor(){
            return this.color;
        }

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 17 * hash + Objects.hashCode(this.color);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final ColorScaleObj other = (ColorScaleObj) obj;
            return Objects.equals(this.color, other.color);
        }

        @Override
        public String toString() {
            return "ColorScaleObj{" + "color=" + color + '}'; // No I18N
        }
    }
    
    public static class IconSetObj
    {
        private final String name;
        private final Integer id;
        private final IconCriteria iconCriteria;
        public enum IconCriteria{
            gt(1),
            gteq(3);
            
            private final int id;
            private IconCriteria(int id){
                this.id = id;
            }
        }
        
        public IconSetObj(String name, Integer id, String iconCriteria) {
            this.name = name;
            this.id = id;
            this.iconCriteria = iconCriteria == null ? IconCriteria.gteq : IconCriteria.valueOf(iconCriteria);
        }

        public String getName() {
            return name;
        }

        public Integer getId() {
            return id;
        }
        
        public IconCriteria getIconCriteria(){
            return iconCriteria;
        }
        
        public int getIconCriteriaComparative(){
            return iconCriteria.id;
        }

        @Override
        public int hashCode() {
            int hash = 3;
            hash = 79 * hash + Objects.hashCode(this.name);
            hash = 79 * hash + Objects.hashCode(this.id);
            hash = 79 * hash + Objects.hashCode(this.iconCriteria);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final IconSetObj other = (IconSetObj) obj;
            if (!Objects.equals(this.name, other.name)) {
                return false;
            }
            if (!Objects.equals(this.id, other.id)) {
                return false;
            }
            if (this.iconCriteria != other.iconCriteria) {
                return false;
            }
            return true;
        }

        @Override
        public String toString() {
            return "IconSetObj{" + "name=" + name + ", id=" + id + ", iconCriteria=" + iconCriteria + '}'; // No I18N
        }
    }
    
    public static class DataBarObj
    {
        private final double axisPoint;
        private final double width;
        private final DataBar.Type fillType;
        private final ZSColor fillColor;
        private final DataBar.Type borderType;
        private final ZSColor borderColor;
        private final ZSColor axisColor;

        public DataBarObj(double axisPoint, double width, DataBar.Type fillType, ZSColor fillColor, DataBar.Type borderType, ZSColor borderColor, ZSColor axisColor) {
            this.axisPoint = axisPoint;
            this.width = width;
            this.fillType = fillType;
            this.fillColor = fillColor;
            this.borderType = borderType;
            this.borderColor = borderColor;
            this.axisColor = axisColor;
        }

        public double getAxisPoint() {
            return axisPoint;
        }

        public double getWidth() {
            return width;
        }

        public DataBar.Type getFillType() {
            return fillType;
        }

        public ZSColor getFillColor() {
            return fillColor;
        }

        public DataBar.Type getBorderType() {
            return borderType;
        }

        public ZSColor getBorderColor() {
            return borderColor;
        }

        public ZSColor getAxisColor() {
            return axisColor;
        }

        @Override
        public int hashCode() {
            int hash = 7;
            hash = 19 * hash + (int) (Double.doubleToLongBits(this.axisPoint) ^ (Double.doubleToLongBits(this.axisPoint) >>> 32));
            hash = 19 * hash + (int) (Double.doubleToLongBits(this.width) ^ (Double.doubleToLongBits(this.width) >>> 32));
            hash = 19 * hash + Objects.hashCode(this.fillType);
            hash = 19 * hash + Objects.hashCode(this.fillColor);
            hash = 19 * hash + Objects.hashCode(this.borderType);
            hash = 19 * hash + Objects.hashCode(this.borderColor);
            hash = 19 * hash + Objects.hashCode(this.axisColor);
            return hash;
        }

        @Override
        public boolean equals(Object obj) {
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            final DataBarObj other = (DataBarObj) obj;
            if (Double.doubleToLongBits(this.axisPoint) != Double.doubleToLongBits(other.axisPoint)) {
                return false;
            }
            if (Double.doubleToLongBits(this.width) != Double.doubleToLongBits(other.width)) {
                return false;
            }
            if (this.fillType != other.fillType) {
                return false;
            }
            if (!Objects.equals(this.fillColor, other.fillColor)) {
                return false;
            }
            if (this.borderType != other.borderType) {
                return false;
            }
            if (!Objects.equals(this.borderColor, other.borderColor)) {
                return false;
            }
            return Objects.equals(this.axisColor, other.axisColor);
        }
        
        @Override
        public String toString() {
            return "DataBarObj{" + "axisPoint=" + axisPoint + ", width=" + width + ", fillType=" + fillType + ", fillColor=" + fillColor + ", borderType=" + borderType + ", borderColor=" + borderColor + ", axisColor=" + axisColor + '}'; // No I18N
        }
    }
}
