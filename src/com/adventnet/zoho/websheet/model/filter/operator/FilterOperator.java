/* $Id$ */
package com.adventnet.zoho.websheet.model.filter.operator;

import com.adventnet.zoho.websheet.model.pivot.PivotFilterInfo;
import com.adventnet.zoho.websheet.model.pivot.PivotUtil;

/**
 * This enum holds a list of supported operators that are necessary for filter.
 * <ul>
 * <li>
 * {@code EQUALS}
 * </li>
 * <li>
 * {@code DOES_NOT_EQUALS}
 * </li>
 * <li>
 * {@code LESS_THAN}
 * </li>
 * <li>
 * {@code GREATER_THAN}
 * </li>
 * <li>
 * {@code LESS_THAN_OR_EQUAL_TO}
 * </li>
 * <li>
 * {@code GREATER_THAN_OR_EQUAL_TO}
 * </li>
 * <li>
 * {@code BETWEEN}
 * </li>
 * <li>
 * {@code BEGINS_WITH}
 * </li>
 * <li>
 * {@code CONTAINS}
 * </li>
 * <li>
 * {@code DOES_NOT_CONTAIN}
 * </li>
 * <li>
 * {@code DOES_NOT_BEGIN_WITH}
 * </li>
 * <li>
 * {@code DOES_NOT_END_WITH}
 * </li>
 * <li>
 * {@code EMPTY}
 * </li>
 * <li>
 * {@code NOT_EMPTY}
 * </li>
 * <li>
 * {@code MATCHES}
 * </li>
 * <li>
 * {@code DOES_NOT_MATCH}
 * </li>
 * <li>
 * {@code NONE}
 * </li>
 * <li>
 * {@code BOTTOM_PERCENT}
 * </li>
 * <li>
 * {@code BOTTOM_VALUES}
 * </li>
 * <li>
 * {@code TOP_VALUES}
 * </li>
 * <li>
 * {@code TOP_PERCENT}
 * </li>
 * </ul>
 * <AUTHOR> N J ( ZT-0049 )
 *
 */
public enum FilterOperator
{
	EQUALS("=", "eq"), //No I18N
	DOES_NOT_EQUALS("!=", "!eq"), //No I18N
	LESS_THAN("<", "lt"), //No I18N
	GREATER_THAN(">", "gt"), //No I18N
	LESS_THAN_OR_EQUAL_TO("<=", "lte"), //No I18N
	GREATER_THAN_OR_EQUAL_TO(">=", "gte"), //No I18N
	BETWEEN("btwn", "btwn"),    // No I18N
	BEGINS_WITH("begins", "begins"),    // No I18N
	CONTAINS("contains", "contains"),    // No I18N
	DOES_NOT_CONTAIN("!contains", "!contains"), //No I18N
	ENDS_WITH("ends", "ends"), //No I18N
	DOES_NOT_BEGIN_WITH("!begins", "!begins"), //No I18N
	DOES_NOT_END_WITH("!ends", "!ends"), //No I18N
	EMPTY("empty", "empty"), //No I18N
	NOT_EMPTY("!empty", "!empty"), //No I18N
	MATCHES("match", "match"), //No I18N
	DOES_NOT_MATCH("!match", "!match"), //No I18N
	TOP_PERCENT("top percent", "top percent"), //No I18N
	TOP_VALUES("top values", "top values"), //No I18N
	BOTTOM_PERCENT("bottom percent", "bottom percent"), //No I18N
	BOTTOM_VALUES("bottom values", "bottom values"), //No I18N
	NONE("", ""), //No I18N
	HAS_ANY_OF("any", "any"), //No I18N
	HAS_ALL_OF("all", "all"), //No I18N
	HAS_NONE_OF("none", "none"), //No I18N
	IS_EXACTLY("exactly", "exactly"); //No I18N

	private final String operatorText;
	private final String operatorInWords;
	FilterOperator(String operatorText, String operatorInWords)
	{
		this.operatorInWords = operatorInWords;
		this.operatorText = operatorText;
	}

	public String getOperatorText()
	{
		return this.operatorText;
	}

	public String getOperatorInWords()
	{
		return this.operatorInWords;
	}

	public static FilterOperator getFilterOperator(String text)
	{
		for (FilterOperator op : FilterOperator.values()) {
			if (op.getOperatorInWords().equalsIgnoreCase(text) || op.getOperatorText().equalsIgnoreCase(text)) {
				return op;
			}
		}
		return FilterOperator.EQUALS;
	}
	
	@Override
	public String toString()
	{
		return this.operatorText;
	}
	
	public boolean isSpecialOperator()
	{
		return FilterSpecialOperator.getFilterSpecialOperator(this) != null;
	}
	
	public boolean isStringOperator()
	{
		return EQUALS.equals(this) || BEGINS_WITH.equals(this) || ENDS_WITH.equals(this) || CONTAINS.equals(this)
				|| MATCHES.equals(this) || DOES_NOT_BEGIN_WITH.equals(this) || DOES_NOT_CONTAIN.equals(this)
				|| DOES_NOT_END_WITH.equals(this) || DOES_NOT_MATCH.equals(this);
    }

	public boolean isNegationOperator()
	{
		return DOES_NOT_BEGIN_WITH.equals(this) || DOES_NOT_END_WITH.equals(this) ||
				DOES_NOT_CONTAIN.equals(this) || DOES_NOT_MATCH.equals(this);
	}

	public FilterSpecialOperator getFilterSpecialOperator()
	{
		return FilterSpecialOperator.getFilterSpecialOperator(this);
	}

//	public static FilterOperator getFilterOperator(String filterOperator)
//	{
//		for(FilterOperator fo : FilterOperator.values())
//		{
//			if(fo.toString().equalsIgnoreCase(filterOperator))
//			{
//				return fo;
//			}
//		}
//		return FilterOperator.EQUALS;
//	}


	public static String getFilterOperatorFromXLSXValue(String filterOperatorStr)
	{
		String zsOperatorStr = null;
		filterOperatorStr = filterOperatorStr
				.replaceFirst("caption", "") //No I18N
				.replaceFirst("value", "") //No I18N
				.replaceFirst("date", "") //No I18N
				.replaceFirst("Newer", "Greater") //No I18N
				.replaceFirst("Older", "Less"); //No I18N
		switch (filterOperatorStr) {
			case "Equal":
				zsOperatorStr = FilterOperator.EQUALS.toString();
				break;
			case "NotEqual":
				zsOperatorStr = FilterOperator.DOES_NOT_EQUALS.toString();
				break;
			case "GreaterThan":
				zsOperatorStr = FilterOperator.GREATER_THAN.toString();
				break;
			case "GreaterThanOrEqual":
				zsOperatorStr = FilterOperator.GREATER_THAN_OR_EQUAL_TO.toString();
				break;
			case "LessThan":
				zsOperatorStr = FilterOperator.LESS_THAN.toString();
				break;
			case "LessThanOrEqual":
				zsOperatorStr = FilterOperator.LESS_THAN_OR_EQUAL_TO.toString();
				break;
			case "Between":
				zsOperatorStr = FilterOperator.BETWEEN.toString();
				break;
			case "BeginsWith":
				zsOperatorStr = FilterOperator.BEGINS_WITH.toString();
				break;
			case "NotBeginsWith":
				zsOperatorStr = FilterOperator.DOES_NOT_BEGIN_WITH.toString();
				break;
			case "EndsWith":
				zsOperatorStr = FilterOperator.ENDS_WITH.toString();
				break;
			case "NotEndsWith":
				zsOperatorStr = FilterOperator.DOES_NOT_END_WITH.toString();
				break;
			case "Contains":
				zsOperatorStr = FilterOperator.CONTAINS.toString();
				break;
			case "NotContains":
				zsOperatorStr = FilterOperator.DOES_NOT_CONTAIN.toString();
				break;
			default:
				throw new IllegalArgumentException("xlsx operator : "+filterOperatorStr+" is not supported yet."); //No I18N
		}
		return zsOperatorStr;
	}


	public String getXlsxFilterType(PivotUtil.FieldType fieldType, PivotFilterInfo.PivotFilterType filterType) {
		boolean isDateField = fieldType == PivotUtil.FieldType.Td;
		String prefix;
		if(filterType == PivotFilterInfo.PivotFilterType.VALUE)
		{
			if(this == FilterOperator.BOTTOM_VALUES || this == FilterOperator.BOTTOM_PERCENT
					|| this == FilterOperator.TOP_VALUES || this == FilterOperator.TOP_PERCENT)
			{
				return "items"; //No I18N
			}
			else {
				prefix = "value"; //No I18N
			}
		}
		else if(isDateField) {
			prefix = "date"; //No I18N
		}
		else {
			prefix = "caption"; //No I18N
		}

		String xlsxFilterType;
		switch (this)
		{
			case EQUALS:
			case MATCHES:
				xlsxFilterType  = "Equal"; //No I18N
				break;
			case DOES_NOT_EQUALS:
			case DOES_NOT_MATCH:
				xlsxFilterType = "NotEqual"; //No I18N
				break;
			case LESS_THAN:
				xlsxFilterType = isDateField ? "OlderThan" : "LessThan"; //No I18N
				break;
			case LESS_THAN_OR_EQUAL_TO:
				xlsxFilterType = isDateField ? "OlderThanOrEqual" : "LessThanOrEqual"; //No I18N
				break;
			case GREATER_THAN:
				xlsxFilterType = isDateField ? "NewerThan" : "GreaterThan"; //No I18N
				break;
			case GREATER_THAN_OR_EQUAL_TO:
				xlsxFilterType = isDateField ? "NewerThanOrEqual" : "GreaterThanOrEqual"; //No I18N
				break;
			case BETWEEN:
				xlsxFilterType = "Between"; //No I18N
				break;
			case CONTAINS:
				xlsxFilterType = "Contains"; //No I18N
				break;
			case DOES_NOT_CONTAIN:
				xlsxFilterType = "NotContains"; //No I18N
				break;
			case ENDS_WITH:
				xlsxFilterType = "EndsWith"; //No I18N
				break;
			case DOES_NOT_END_WITH:
				xlsxFilterType = "NotEndsWith"; //No I18N
				break;
			case BEGINS_WITH:
				xlsxFilterType = "BeginsWith"; //No I18N
				break;
			case DOES_NOT_BEGIN_WITH:
				xlsxFilterType = "NotBeginsWith"; //No I18N
				break;
			default:
				throw new IllegalArgumentException("filter type not supported in excel"); //No I18N
		}
		return prefix+xlsxFilterType;
	}

	public String getZSToXlsxFilterOperator() {
		switch (this)
		{
			case EQUALS:
			case CONTAINS:
			case BEGINS_WITH:
			case ENDS_WITH:
			case MATCHES:
				return null;
			case DOES_NOT_EQUALS:
			case DOES_NOT_CONTAIN:
			case DOES_NOT_BEGIN_WITH:
			case DOES_NOT_END_WITH:
			case DOES_NOT_MATCH:
				return "notEqual"; //No I18N
			case LESS_THAN:
				return "lessThan"; //No I18N
			case LESS_THAN_OR_EQUAL_TO:
				return "lessThanOrEqual"; //No I18N
			case GREATER_THAN:
				return "greaterThan"; //No I18N
			case GREATER_THAN_OR_EQUAL_TO:
				return "greaterThanOrEqual"; //No I18N
			case BETWEEN:
			case EMPTY:
			case NOT_EMPTY:
			case NONE:
			case BOTTOM_VALUES:
			case BOTTOM_PERCENT:
			case TOP_VALUES:
			case TOP_PERCENT:
			default:
				throw new IllegalArgumentException("filter operator not supported in excel"); //No I18N
		}
	}
}