/* $Id$ */
package com.adventnet.zoho.websheet.model.filter;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.filter.executor.FilterAction;
import com.adventnet.zoho.websheet.model.filter.operator.FilterLogicalOperator;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.writer.zs.XMLWriter;
import com.adventnet.zoho.websheet.model.zsparser.Names;

import java.util.*;
import java.util.Map.Entry;
import java.util.function.Predicate;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.adventnet.zoho.websheet.model.filter.operator.FilterLogicalOperator.NONE;

/**
 * The class {@code FilterView} implements {@link Cloneable} and includes methods for filtering range based
 * on a list of {@code FilterCondition}, list the rows present in the filter range
 * & also list the data from a column specified by
 * a column index.
 * <br><br>
 * FilterView plays an important role for filtering a list of ranges with the given condition.
 * <br><br>
 * <b>Note:The class will throw {@link NullPointerException} on various methods if the instance is an {@link #EMPTY_FILTER_VIEW}.</b>
 * @see FilterRange
 * @see FilterAction
 * @see FilterCondition
 * @see com.adventnet.zoho.websheet.model.filter.executor.FilterSpecialConditionExecutor
 * @see FilterLogicalOperator
 * @see com.adventnet.zoho.websheet.model.filter.operator.FilterOperator
 * <AUTHOR> N J (ZT-0049)
 *
 */
public class FilterView implements Cloneable
{
	private static final Logger LOGGER = Logger.getLogger(FilterView.class.getName());

	private int id;

	private Table table;
	private Filter filter;
	private BitSet bitSet;
	private boolean isCreatedFromDefault = false;
	private boolean displayFilterButtons = true;

	public FilterView(int id)
	{
		this.id = id;
	}

	public FilterView(int id, Table table)
	{
		this.id = id;
		this.table = table;
		this.bitSet = new BitSet();
	}

	public FilterView(int id, Table table, Filter filter, BitSet bitSet)
	{
		this.id = id;
		this.table = table;
		this.filter = filter;
		this.bitSet = bitSet;
	}

	public boolean isEmptyFilterView()
	{
		return this.table == null;
	}

	public boolean isSheetFilterView()
	{
		return this.getId() == EngineConstants.DEFAULT_SHEET_FILTER_ID;
	}

	public int getId()
	{
		return id;
	}

	public Table getTable()
	{
		return table;
	}

	public void setTable(Table table)
	{
		this.table = table;
	}

	public void removeTable()
	{
		this.setTable(null);
	}

	public Sheet getSheet()
	{
		return this.getTable().getSheet();
	}

	public Filter getFilter()
	{
		return filter;
	}

	public void setFilter(Filter filter)
	{
		this.filter = filter;
	}

	public BitSet getBitSet()
	{
		return bitSet;
	}

	public void setBitSet(BitSet bitSet)
	{
		this.bitSet = bitSet;
	}

	public boolean isDisplayFilterButtons() {
		return displayFilterButtons;
	}

	public void setDisplayFilterButtons(boolean displayFilterButtons) {
		this.displayFilterButtons = displayFilterButtons;
		getSheet().setIsModified(true);
	}

	public void setVisibleBitSet(BitSet visibleBitSet)
	{
		BitSet filteredRowsSet = new BitSet(this.getDataRange().getRowSize());
		int startIndex = this.getStartRowIndexWithoutHeader();
		int endIndex = this.getEndRowIndexWithoutFooter();
		filteredRowsSet.set(startIndex, endIndex + 1, true);
		visibleBitSet.or(this.getSheet().getHiddenRowsBitSet());
		filteredRowsSet.andNot(visibleBitSet);
		this.setBitSet(filteredRowsSet);
	}

	public BitSet getVisibleBitSet()
	{
		BitSet visibleBitSet = new BitSet(getDataRange().getRowSize());
		visibleBitSet.set(getStartRowIndexWithoutHeader(), getEndRowIndexWithoutFooter() + 1);
		visibleBitSet.andNot(getBitSet());
		return visibleBitSet;
	}

	public int getNoOfFilteredRows()
	{
		return this.getBitSet().cardinality();
	}

	public boolean isCreatedFromDefault()
	{
		return isCreatedFromDefault;
	}

	public void setCreatedFromDefault(boolean createdFromDefault)
	{
		isCreatedFromDefault = createdFromDefault;
	}

	/**
	 * Returns the starting row index excluding the first header row index
	 * @return
	 */
	public int getStartRowIndex()
	{
		return this.getTable().getStartRowIndex();
	}

	public int getEndRowIndex()
	{
		return this.getTable().getEndRowIndex();
	}

	public int getStartColumnIndex()
	{
		return this.getTable().getStartColIndex();
	}

	public int getEndColumnIndex()
	{
		return this.getTable().getEndColIndex();
	}

	private int getRestrictedEndRow(int startIndex, int rowsRepeated)
	{
		return Math.min(Math.min(startIndex + rowsRepeated, Utility.MAXNUMOFROWS), this.getEndRowIndex() + 1);
	}

	public int getStartRowIndexWithoutHeader()
	{
		return table.getStartRowIndex() + 1;
	}

	public int getEndRowIndexWithoutFooter()
	{
		return table.isFooterRowShown() ? table.getEndRowIndex() - 1 : table.getEndRowIndex();
	}

	public Cell getHeaderCell(int columnIndex)
	{
		return this.getSheet().getCell(this.getStartRowIndex() < 1 ? this.getStartRowIndex() : this.getStartRowIndex() - 1, columnIndex);
	}

	public DataRange getDataRange()
	{
		return this.getTable().getAsDataRange();
	}

	public boolean isFilteredRow(int rowIndex)
	{
		return this.getBitSet().get(rowIndex);
	}

	private BitSet setFilteredBitSet(BitSet visibleBitSet)
	{
		BitSet filteredRowsSet = new BitSet(this.getDataRange().getRowSize() - 1);
		int startIndex = this.getStartRowIndex();
		int endIndex = this.getEndRowIndex();
		filteredRowsSet.set(startIndex, endIndex + 1, true);
		filteredRowsSet.xor(this.getSheet().getHiddenRowsBitSet());

		startIndex = 0;
		while(visibleBitSet.nextSetBit(startIndex) != -1)
		{
			int rowIndex = visibleBitSet.nextSetBit(startIndex);
			ReadOnlyRow row = this.getSheet().getReadOnlyRowFromShell(rowIndex);
			endIndex = this.getRestrictedEndRow(startIndex, row.getRowsRepeated());
			filteredRowsSet.set(startIndex, endIndex, false);

			startIndex++;
		}
		return filteredRowsSet;
	}

	private Filter createNewFilter(FilterLogicalOperator type, List<FilterCondition> filterConditionList)
	{
		Filter filter = new Filter(type);
		filter.addAllConditions(filterConditionList);
		return filter;
	}

	/**
	 * Returns an instance of {@code Filter} matching the given {@code type} or null if no match is found.
	 * @param type
	 * @return
	 */
	public Filter getFilterByType(FilterLogicalOperator type)
	{
		Filter filter = this.getFilter();
		while(filter != null)
		{
			if(filter.getType().equals(type))
			{
				return filter;
			}
			filter = filter.getChildFilter();
		}
		return null;
	}

	/**
	 * Add the given filter to the existing filter linked list by traversal.
	 * @param childFilter
	 */
	private void addFilter(Filter childFilter)
	{
		Filter filter = this.getFilter();
		//	UPDATE THE GIVEN FILTER AS MAIN FILTER IF FILTER ISN'T CREATED.
		if(filter == null)
		{
			this.setFilter(childFilter);
			return;
		}
		while(filter.getChildFilter() != null)
		{
			filter = filter.getChildFilter();
		}
		filter.setChildFilter(childFilter);
	}

	public void addOrUpdateFilter(FilterLogicalOperator type, List<FilterCondition> filterConditionList)
	{
		Filter filter = this.getFilterByType(type);
		//	CREATE AND ADD THE FILTER AS CHILD FILTER IS TYPE IS NOT CREATED.
		if(filter == null)
		{
			filter = this.createNewFilter(type, filterConditionList);
			this.addFilter(filter);
			return;
		}
		//	ADD FILTER CONDITIONS IF FILTER OF GIVEN TYPE IS ALREADY CREATED.
		else
		{
			filter.addAllConditions(filterConditionList);
		}
	}

	/**
	 * Add the filter conditions to the filter based on the filter type
	 * @param filterConditionList
	 * @param type
	 */
	public BitSet populateAndExecuteConditions(int columnIndex, List<FilterCondition> filterConditionList, FilterLogicalOperator type)
	{
		if(!filterConditionList.isEmpty() && type != NONE)
		{
			//	1. TRAVERSE THE CHILD FILTERS AND REMOVE FILTER CONDITIONS FOR THE COLUMN INDEX
			//	2. ADD THE FILTER CONDITIONS TO THE FILTER IF THE FILTER TYPE MATCHES WITH GIVEN TYPE
			//	3. EXECUTE ALL CONDITIONS IF SAME COLUMN INDEX FILTER CONDITIONS ARE UPDATED ELSE EXECUTE ONLY THE CURRENT COLUMN CONDITIONS.
			Boolean isExecuteAll = this.removeFilterConditions(columnIndex);
			this.addOrUpdateFilter(type, filterConditionList);
			if(isExecuteAll)
			{
				return this.executeAllConditions();
			}
			return this.executeConditions(columnIndex);
		}
		return null;
	}

	/**
	 * Removes all the filter condition in the filter range and resets the visibility.
	 */
	public void removeFilter()
	{
		this.setFilter(null);
	}

	/**
	 * Remove the filter specified at the field index and re-arranges the filter in the view
	 * @param columnIndex
	 * @return true if condition has existed in the column index or false otherwise.
	 */
	public Boolean removeFilterConditions(int columnIndex)
	{
		Filter filter = this.getFilter();
		while(filter != null)
		{
			if(filter.getFilterConditions().containsKey(columnIndex))
			{
				filter.getFilterConditions().remove(columnIndex);
				return true;
			}
			filter = filter.getChildFilter();
		}
		return false;
	}

	public Boolean hasFilterConditionOnColumn(int columnIndex)
	{
		Filter filter = this.getFilter();
		while(filter != null)
		{
			if(filter.getFilterConditions().containsKey(columnIndex))
			{
				return true;
			}
			filter = filter.getChildFilter();
		}
		return false;
	}

	private Map<Filter, Map<Integer, List<FilterCondition>>> filterConditions(Predicate<? super Entry<Integer, List<FilterCondition>>> predicate)
	{
		Map<Filter, Map<Integer, List<FilterCondition>>> filteredConditionsMap = new LinkedHashMap<>();
		this.getAllFilterConditions().forEach((key, value) ->
		{
			Map<Integer, List<FilterCondition>> filterConditions = value.entrySet().stream().filter(predicate).collect((Collectors.toMap(Entry::getKey, Entry::getValue, (v1, v2) -> {
				throw new IllegalStateException();
			}, LinkedHashMap::new)));
			if(!filterConditions.isEmpty())
			{
				filteredConditionsMap.put(key, filterConditions);
			}
		});

		return filteredConditionsMap;
	}

	public Map<Filter, Map<Integer, List<FilterCondition>>> getColumnFilterConditions(int columnIndex)
	{
		return this.filterConditions(ent -> ent.getKey() == columnIndex);
	}

	/**
	 *
	 * @param columnIndex
	 * @return
	 */
	public Map<Filter, Map<Integer, List<FilterCondition>>> excludeFilterConditionForColumn(int columnIndex)
	{
		return this.filterConditions(ent -> ent.getKey() != columnIndex);
	}

	public Map<Filter, Map<Integer, List<FilterCondition>>> getAllFilterConditions()
	{
		Map<Filter, Map<Integer, List<FilterCondition>>> filterConditionsMap = new LinkedHashMap<>();
		Filter filter = this.getFilter();
		while(filter != null)
		{
			filterConditionsMap.put(filter, filter.getFilterConditions());
			filter = filter.getChildFilter();
		}
		return filterConditionsMap;
	}

	/**
	 * Executes the set of filter conditions on the specified column index with the filtered rows set
	 * @param columnIndex
	 * @return
	 */
	public BitSet executeConditions(int columnIndex)
	{
		FilterAction filterAction = new FilterAction(this, this.getColumnFilterConditions(columnIndex));
		return filterAction.process();
	}

	/**
	 * Executes all sets of filter conditions on the filter view considering all the un hidden rows in the range
	 * and updates the bitset accordingly.
	 * <br><br>
	 * <b>Note: This is a performance over head operation when there are many conditions over columns.</b>
	 * @see #executeConditions(int)
	 * @return iterator containing list of read only rows
	 */
	public BitSet executeAllConditions()
	{
		FilterAction filterAction = new FilterAction(this, this.getAllFilterConditions());
		return filterAction.processAll();
	}

	private List<String> getEmptyFilterViewAttributes()
	{
		List<String> attributes = new ArrayList<>();
		attributes.add(XMLWriter.toString(Names.A_FILTER_VIEW_ID));
		attributes.add(XMLWriter.toString(Names.N_SHEET_ASN));
		return attributes;
	}

	public List<String> getAttributes(boolean isUserFilter)
	{
		if(this.isEmptyFilterView())
		{
			return this.getEmptyFilterViewAttributes();
		}
		List<String> attributes = new ArrayList<>();
		attributes.add(XMLWriter.toString(Names.A_FILTER_VIEW_ID));
		attributes.add(XMLWriter.toString(Names.A_TABLE_ID));
		attributes.add(XMLWriter.toString(Names.A_TARGET_RANGE_ADDRESS));
		attributes.add(XMLWriter.toString(Names.A_DISPLAY_FILTER_BUTTONS));
		if(isUserFilter)
		{
			attributes.add(XMLWriter.toString(Names.A_FILTEREDROWS));
		}
		return attributes;
	}

	@Override
	public boolean equals(Object o)
	{
		if(this == o)
		{
			return true;
		}
		if(o == null || getClass() != o.getClass())
		{
			return false;
		}
		FilterView that = (FilterView) o;
		return id == that.id && isCreatedFromDefault == that.isCreatedFromDefault && Objects.equals(table, that.table) && Objects.equals(filter, that.filter) && Objects.equals(bitSet, that.bitSet);
	}

	@Override
	public int hashCode()
	{
		return Objects.hash(id, table, filter, bitSet, isCreatedFromDefault);
	}

	public List<String> getValues(boolean isUserFilter)
	{
		List<String> values = new ArrayList<>();
		values.add(String.valueOf(this.getId()));
		if(this.isEmptyFilterView())
		{
			return values;
		}
		values.add(String.valueOf(this.getTable().getID()));
		values.add(new Range(this.getSheet(),this.getStartRowIndex(),this.getStartColumnIndex(),this.getEndRowIndex(),this.getEndColumnIndex()).getCellRangeAddress());
		values.add(String.valueOf(this.isDisplayFilterButtons()));
		if(isUserFilter)
		{
			values.add(this.getBitSet().isEmpty() ? null : String.valueOf(this.getBitSet()));
		}
		return values;
	}

	public FilterView clone(Sheet sheet, boolean isNeedToCloneTable)
	{
		FilterView filterView = new FilterView(this.getId());
		filterView.table = this.table != null ? (isNeedToCloneTable ? table.clone(sheet) : sheet.getTable(table.getName())) : null;
		filterView.filter = this.filter != null ? this.filter.clone() : null;
		filterView.bitSet = (BitSet) this.bitSet.clone();
		filterView.displayFilterButtons = this.displayFilterButtons;
		return filterView;
	}
}
