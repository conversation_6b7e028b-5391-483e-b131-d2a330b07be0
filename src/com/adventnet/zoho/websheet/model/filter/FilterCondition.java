/* $Id$ */
/*
 * FilterCondition.java
 *
 * Created on June 4, 2009, 4:02 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.filter;


import com.adventnet.zoho.websheet.model.PicklistValue;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.filter.operator.FilterOperator;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.writer.zs.XMLWriter;
import com.adventnet.zoho.websheet.model.zsparser.Names;
import com.zoho.sheet.util.ClientUtils;

import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * This class {@code FilterCondition} maintains the condition of the filter that are specified by column index,
 * filter value, data type and operator to compare with.
 * 
 * <AUTHOR>
 * <AUTHOR> N J
 */
public class FilterCondition implements Cloneable
{
	public static final Logger LOGGER = Logger.getLogger(FilterCondition.class.getName());

	private int fieldNumber;
	private final DataType dataType;
	private final FilterType filterType;
	private final FilterOperator operator;
	private final Object value;

	private FilterCondition(FilterType filterType,  FilterOperator operator, DataType dataType, int fieldNumber, Object value) throws Exception
	{
		this.dataType = dataType == null ? DataType.TEXT : dataType;
		this.operator = operator;
		this.filterType = filterType;
		this.value = value;
		this.setFieldNumber(fieldNumber);
		if(this.fieldNumber > Utility.MAXNUMOFCOLS)
		{
			throw new SheetEngineException("[FILTERS][Exception] Field Number "+fieldNumber+" is greater than column limit");   // No I18N
		}
	}

	// For Empty and Non-Empty filter operators, as filterValue is not required
	public FilterCondition(FilterType filterType, FilterOperator operator, DataType dataType, int fieldNumber) throws Exception
	{
		this.dataType = dataType == null ? DataType.TEXT : dataType;
		this.operator = operator;
		this.filterType = filterType;
		this.value = null;
		this.setFieldNumber(fieldNumber);
		if (this.fieldNumber > Utility.MAXNUMOFCOLS)
		{
			throw new SheetEngineException("[FILTERS][Exception] Field Number " + fieldNumber + " is greater than column limit");   // No I18N
		}
	}

	public FilterCondition(FilterType filterType,  FilterOperator operator, DataType dataType, int fieldNumber, PicklistValue value) throws Exception
	{
		this(filterType, operator, dataType, fieldNumber, (Object)value);
	}

	public FilterCondition(FilterType filterType,  FilterOperator operator, DataType dataType, int fieldNumber, String value) throws Exception
	{
		this(filterType, operator, dataType, fieldNumber, (Object)value);
	}

	public FilterCondition(FilterType filterType,  FilterOperator operator, DataType dataType, int fieldNumber, ZSColor zsColor) throws Exception
	{
		this.dataType = dataType == null ? DataType.TEXT : dataType;
		this.operator = operator;
		this.filterType = filterType;
		this.value = zsColor;
		this.setFieldNumber(fieldNumber);

		if(this.fieldNumber > Utility.MAXNUMOFCOLS)
		{
			throw new SheetEngineException("[FILTERS][Exception] Field Number "+fieldNumber+" is greater than column limit");   // No I18N
		}
	}

	public void setFieldNumber(int fieldNumber)
	{
		if(fieldNumber < 0)
		{
			LOGGER.log(Level.WARNING, "[FILTERS] TO BE CHECKED: Field Number {0} can't be less than 0", fieldNumber);
		}
		this.fieldNumber = Math.max(fieldNumber, 0);
	}

	public int getFieldNumber()
	{
		return this.fieldNumber;
	}

	public DataType getDataType()
	{
		return this.dataType;
	}
	
	public FilterType getFilterType()
	{
		return this.filterType;
	}

	public FilterOperator getOperator()
	{
		return this.operator;
	}

	public boolean isColorFilter()
	{
		return this.getFilterType().isColorFilter();
	}

	private String getHexColor(ZSTheme theme)
	{
		return ZSColor.getHexColor((ZSColor) this.value, theme);
	}

	private String getOldValue(ZSTheme theme)
	{
		if(this.getFilterType().equals(FilterType.FILTER_BY_VALUE))
		{
			return (String)this.value;
		}
		else
		{
			return this.getFilterType().getFilterValue(this.getHexColor(theme));
		}
	}

	public Object getValue()
	{
		return this.value;
	}

	public String getValue(ZSTheme theme)
	{
		if(this.getFilterType().isColorFilter())
		{
			return this.getHexColor(theme);
		}
		return (String) this.value;
	}

	public String[] getAttributes()
	{
		return new String[]
			{
				XMLWriter.toString(Names.A_OPERATOR),
				XMLWriter.toString(Names.A_DATA_TYPE),
				XMLWriter.toString(Names.A_FIELD_NUMBER),
				XMLWriter.toString(Names.A_VALUE)
			};
	}

	public String[] getValues(ZSTheme zsTheme)
	{
		return new String[]
			{
				getOperator().toString(),
				getDataType().toString(),
				String.valueOf(getFieldNumber()),
				getOldValue(zsTheme)
			};
	}

	public String[] getZSAttributes()
	{
		if(this.filterType.equals(FilterType.FILTER_BY_VALUE))
		{
			return new String[]
				{
					XMLWriter.toString(Names.A_FILTER_TYPE),
					XMLWriter.toString(Names.A_OPERATOR),
					XMLWriter.toString(Names.A_DATA_TYPE),
					XMLWriter.toString(Names.A_FIELD_NUMBER),
					XMLWriter.toString(Names.A_VALUE)
				};
		}
		else if(this.filterType.equals(FilterType.FILTER_BY_PICKLIST))
		{
			return new String[]
				{
						XMLWriter.toString(Names.A_FILTER_TYPE),
						XMLWriter.toString(Names.A_OPERATOR),
						XMLWriter.toString(Names.A_DATA_TYPE),
						XMLWriter.toString(Names.A_FIELD_NUMBER),
						XMLWriter.toString(Names.PICKLIST_ID),
						XMLWriter.toString(Names.PICKLIST_ITEM_IDS)
				};
		}
		else
		{
			return new String[]
				{
					XMLWriter.toString(Names.A_FILTER_TYPE),
					XMLWriter.toString(Names.A_OPERATOR),
					XMLWriter.toString(Names.A_DATA_TYPE),
					XMLWriter.toString(Names.A_FIELD_NUMBER),
					XMLWriter.toString(Names.A_COLOR),
					XMLWriter.toString(Names.A_THEME_COLOR),
					XMLWriter.toString(Names.A_COLOR_TINT),
				};
		}
	}

	public String[] getZSValues(ZSTheme zsTheme)
	{
		if(this.filterType.equals(FilterType.FILTER_BY_VALUE))
		{
			return new String[]
				{
					getFilterType().toString(),
					getOperator().toString(),
					getDataType().toString(),
					String.valueOf(getFieldNumber()),
					getValue(zsTheme)
				};
		}
		else if(this.filterType.equals(FilterType.FILTER_BY_PICKLIST))
		{
			return new String[]
					{
							getFilterType().toString(),
							getOperator().toString(),
							getDataType().toString(),
							String.valueOf(getFieldNumber()),
							String.valueOf(((PicklistValue)getValue()).getPicklist().getId()),
							ClientUtils.convertListtoString(((PicklistValue)getValue()).getItemIDs(), ",")
					};
		}
		else
		{
			return new String[]
				{
					getFilterType().toString(),
					getOperator().toString(),
					getDataType().toString(),
					String.valueOf(getFieldNumber()),
					ZSColor.getHexColor((ZSColor) this.value, zsTheme),
					this.value != null ? ((ZSColor)this.value).getThemeColorToWrite() : null,
					this.value != null ? ((ZSColor)this.value).getColorTintToWrite() : null,
				};
		}
	}


	@Override
	public boolean equals(Object o)
	{
		if(this == o)
		{
			return true;
		}
		if(o == null || getClass() != o.getClass())
		{
			return false;
		}
		FilterCondition that = (FilterCondition) o;
		return fieldNumber == that.fieldNumber && dataType == that.dataType && filterType == that.filterType && Objects.equals(value, that.value) && operator == that.operator;
	}

	@Override
	public int hashCode()
	{
		return Objects.hash(fieldNumber, dataType, filterType, value, operator);
	}

	@Override
	public FilterCondition clone()
	{
		FilterCondition clonedFilterCondition = null;
		try
		{
			clonedFilterCondition = (FilterCondition)super.clone();
		}
		catch(CloneNotSupportedException ce)
		{
			LOGGER.log(Level.WARNING, "[FILTERS] Filter Condition can't be cloned.");     //No I18N
		}
		return clonedFilterCondition;
	}
}