/* $Id$ */
package com.adventnet.zoho.websheet.model.filter.update.action;

import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.filter.exception.FilterException;
import com.adventnet.zoho.websheet.model.filter.update.FilterViewUpdater;
import com.adventnet.zoho.websheet.model.util.RangeUtil;

import static com.adventnet.zoho.websheet.model.util.ActionConstants.DELETE_ROW;

/**
 * This class will perform the necessary changes in the filter range or remove the filter when delete row action is executed on a filter range.
 *
 * <AUTHOR> N J (ZT-0049)
 */
public class DeleteRow extends FilterViewUpdater
{
	public DeleteRow(FilterView view, int rowIndex, int noOfRows) throws FilterException
	{
		super(view, DELETE_ROW, rowIndex, noOfRows);
	}

	protected Boolean checkAndRemoveFilterView()
	{
		int index = this.getIndex();
		int count = this.getCount();
		int startRowIndex = this.getStartRowIndex();
		return index <= startRowIndex && ((index + count) > startRowIndex);
	}

	protected void updateFilterConditions()
	{
		return;
	}

	protected void updateRange()
	{
		int index = this.getIndex();
		int count = this.getCount();
		int startColIndex = this.getStartColumnIndex();
		int endColIndex = this.getEndColumnIndex();
		int startRowIndex = this.getStartRowIndex();
		int endRowIndex = this.getEndRowIndex();
		if(index <= (endRowIndex + 1) && ((index + count) > endRowIndex))    //If last rows are deleted
		{
			int newEndRow = (index + count);

			while(!RangeUtil.isBlankRange(this.getSheet(), newEndRow, startColIndex, newEndRow, endColIndex, true))
			{
				newEndRow += 1;
			}

			if(newEndRow == (index + count))
			{
				newEndRow = index;
			}
			this.updateViewRange(new Range(this.getSheet(), startRowIndex, startColIndex, newEndRow - 1, endColIndex));
		}
	}

	@Override
	protected int getUpdatedFieldNumber(int fieldNumber)
	{
		return fieldNumber;
	}
}