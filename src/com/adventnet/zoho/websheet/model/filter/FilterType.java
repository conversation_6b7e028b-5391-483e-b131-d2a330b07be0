/* $Id$ */
package com.adventnet.zoho.websheet.model.filter;

public enum FilterType
{
	FILTER_BY_VALUE("", 1, "fbv"),	// No I18N
	FILTER_BY_CELL_COLOR("bycellcolor", 2, "fbcc"),	// No I18N
	FILTER_BY_FONT_COLOR("byfontcolor", 3, "fbtc"),	// No I18N
	CUSTOM_FILTER("", 4, "fc"),	// No I18N
	FILTER_BY_PICKLIST("byPickList", 5, "fbpl"); // No I18N
	
	private final String prefix;
	private final int id;
	private final String key;
	FilterType(String prefix, int id, String key)
	{
		this.prefix = prefix;
		this.id = id;
		this.key = key;
	}
	
	public String getPrefix()
	{
		return this.prefix;
	}

	public int getId()
	{
		return id;
	}

	public boolean isColorFilter()
	{
		return this.equals(FILTER_BY_FONT_COLOR) || this.equals(FILTER_BY_CELL_COLOR);
	}

	public static FilterType getFilterType(int id)
	{
		for(FilterType filterType : FilterType.values())
		{
			if(filterType.getId() == id)
			{
				return filterType;
			}
		}
		return FilterType.FILTER_BY_VALUE;
	}

	public static FilterType getFilterType(String prefix)
	{
		if(prefix.startsWith(FILTER_BY_CELL_COLOR.getPrefix()))
		{
			return FILTER_BY_CELL_COLOR;
		}
		else if(prefix.startsWith(FILTER_BY_FONT_COLOR.getPrefix()))
		{
			return FILTER_BY_FONT_COLOR;
		}
		else if(prefix.startsWith(FILTER_BY_PICKLIST.getPrefix()))
		{
			return FILTER_BY_PICKLIST;
		}
		return FILTER_BY_VALUE;
	}

	public String getFilterValue(String hexCode)
	{
		return this.getPrefix() + "(" + hexCode + ")";
	}

	public static String getExactData(FilterType appliedFilterType, String value)
	{
		int endIndex = value.length() - 1;
		return appliedFilterType.equals(FILTER_BY_VALUE) ? value : value.substring((appliedFilterType.getPrefix() + "(").length(), endIndex);
	}
	
	public static String getData(FilterType appliedFilterType, String data)
	{ 
		return appliedFilterType != null ? ( appliedFilterType.isColorFilter() ? appliedFilterType.getPrefix() + "(" + data + ")" : data ) : data;	//	No I18N
	}

	public String getKey()
	{
		return this.key;
	}

	@Override
	public String toString()
	{
		return String.valueOf(this.id);
	}
}
