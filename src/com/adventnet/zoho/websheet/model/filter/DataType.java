package com.adventnet.zoho.websheet.model.filter;

public enum DataType
{
	TEXT(0),
	NUMBER(1),
	DATE(2),
	DATETIME(4);

	private final int id;
	DataType(int id)
	{
		this.id = id;
	}

	public int getId()
	{
		return this.id;
	}

	public String getName()
	{
		return this.name();
	}

	public static DataType getDataType(String dataType)
	{
		for(DataType dT : DataType.values())
		{
			if(dT.name().equalsIgnoreCase(dataType))
			{
				return dT;
			}
		}
		return TEXT;
	}

	public static DataType getDataType(int id)
	{
		for(DataType dT : DataType.values())
		{
			if(dT.getId() == id)
			{
				return dT;
			}
		}
		return TEXT;
	}

	@Override
	public String toString()
	{
		return String.valueOf(this.id);
	}
}
