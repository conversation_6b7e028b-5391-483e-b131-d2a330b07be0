/* $Id$ */
package com.adventnet.zoho.websheet.model.filter.exception;

/**
 * 
 * <AUTHOR> N J ( ZT-0049 )
 *
 */
public class FilterRangeOutOfBoundException extends IndexOutOfBoundsException
{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4352650185413220523L;

	/**
     * Constructs a {@code FilterRangeOutOfBoundException} with no
     * detail message.
     *
     * @since   JDK1.0.
     */
    public FilterRangeOutOfBoundException() {
        super();
    }

    /**
     * Constructs a {@code FilterRangeOutOfBoundException} with
     * the specified detail message.
     *
     * @param   s   the detail message.
     */
    public FilterRangeOutOfBoundException(String s) {
        super(s);
    }

    /**
     * Constructs a new {@code FilterRangeOutOfBoundException}
     * class with an argument indicating the illegal index.
     *
     * @param   index   the illegal index.
     */
    public FilterRangeOutOfBoundException(int index) {
        super("Filter Range index out of range: " + index);	//No I18N
    }
}
