/* $Id$ */
package com.adventnet.zoho.websheet.model.filter.executor.action;

import com.adventnet.zoho.websheet.model.ConditionalStyleObject;
import com.adventnet.zoho.websheet.model.ReadOnlyCell;
import com.adventnet.zoho.websheet.model.filter.FilterCondition;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.filter.executor.FilterConditionExecutor;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;

import java.util.List;

import static com.adventnet.zoho.websheet.model.filter.FilterType.FILTER_BY_CELL_COLOR;

/**
 * The {@code FilterByCellColor} class compares the cell background color with the color given in the filter condition.
 * @see FilterCondition
 * <AUTHOR> N J ( ZT-0049 )
 *
 */
public class FilterByCellColor extends FilterConditionExecutor
{
	public FilterByCellColor(FilterView view, List<ConditionalStyleObject> filteredConditionalStyleList, ReadOnlyCell readOnlyCell, FilterCondition filterCondition)
	{
		super(view, filteredConditionalStyleList, readOnlyCell, filterCondition);
		if(filterCondition.getFilterType() != FILTER_BY_CELL_COLOR)
		{
			this.throwUnSupportedConditionError();
		}
	}
	
	@Override
	public boolean execute()
	{
		ZSColor backgroundColor = this.getCellBackgroundColor();
		ZSColor filterConditionValue = (ZSColor) this.getFilterCondition().getValue();
		// TODO - REMOVE HEX CODE ONLY CHECK ONCE MOBILE TEAM HAS HANDLED THEME COLORS
		String filterValueHexColor = ZSColor.getHexColor(filterConditionValue, this.getZSTheme());
		String cellBackgroundHexColor = ZSColor.getHexColor(backgroundColor, this.getZSTheme());
		// TODO- REMOVE NULL CHECK FOR FILTER VALUE ONCE CONFIRMING THAT IT WILL HAVE NO NULL VALUE FOR ANY SPREADSHEET
		return filterValueHexColor != null && filterValueHexColor.equals(cellBackgroundHexColor);
	}
}
