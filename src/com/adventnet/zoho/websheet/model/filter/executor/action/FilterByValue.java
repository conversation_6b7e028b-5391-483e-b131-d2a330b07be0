/* $Id$ */
package com.adventnet.zoho.websheet.model.filter.executor.action;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.filter.FilterCondition;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.filter.executor.FilterConditionExecutor;
import com.adventnet.zoho.websheet.model.filter.operator.FilterOperator;

import java.util.List;

import static com.adventnet.zoho.websheet.model.filter.FilterType.FILTER_BY_VALUE;
import static com.adventnet.zoho.websheet.model.filter.operator.FilterOperator.*;

/**
 * 
 * <AUTHOR> N J ( ZT-0049 )
 *
 */
public class FilterByValue extends FilterConditionExecutor
{
	public FilterByValue(FilterView view, List<ConditionalStyleObject> filteredConditionalStyleList, ReadOnlyCell readOnly<PERSON>ell, FilterCondition filterCondition)
	{
		super(view, filteredConditionalStyleList, readOnlyCell, filterCondition);
		if(filterCondition.getFilterType() != FILTER_BY_VALUE)
		{
			this.throwUnSupportedConditionError();
		}
	}
	
	private boolean isMatchingOperator(FilterOperator fOperator)
	{
		return (fOperator == EQUALS || fOperator == MATCHES || fOperator == DOES_NOT_MATCH);
	}

	@Override
	public boolean execute()
	{
		FilterOperator fOperator = this.getFilterOperator();
		if (fOperator == FilterOperator.EMPTY || fOperator == FilterOperator.NOT_EMPTY)
		{
			boolean isEmpty = this.getCurrentCell().getCell().getValue() == null
					|| this.getCurrentCell().getCell().getValue().getType() == Cell.Type.UNDEFINED;
			return (fOperator == FilterOperator.EMPTY) == isEmpty;
		}
		Object obj1 = isMatchingOperator(fOperator) ? this.getCellContent().trim() : this.getCellValue().getValue();
		Object obj2 = this.getFilterConditionValue();
		return this.compare(obj1, obj2, fOperator);
	}
}
