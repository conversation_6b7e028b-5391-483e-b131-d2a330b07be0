/* $Id$ */
package com.adventnet.zoho.websheet.model.filter.executor.action;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.ReadOnlyRow;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.filter.FilterCondition;
import com.adventnet.zoho.websheet.model.filter.FilterView;
import com.adventnet.zoho.websheet.model.filter.executor.FilterSpecialConditionExecutor;
import com.adventnet.zoho.websheet.model.filter.util.FilterUtil;

import java.util.*;

/**
 * 
 * <AUTHOR> N J ( ZT-0049 )
 *
 */
public class FilterBySpecialConditions extends FilterSpecialConditionExecutor
{	
	private final BitSet bitSet;
	public FilterBySpecialConditions(FilterView view, FilterCondition filterCondition, BitSet bitSet)
	{
		super(view, filterCondition);
		if(!FilterUtil.isSpecialCondition(filterCondition))
		{
			this.throwUnSupportedConditionError();
		}
		this.bitSet = bitSet;
	}

	public BitSet executeSpecialCondition()
	{
		int totalRows = this.getTotalRows(this.getFilterCondition());
		Map<Integer, Double> rowValueMap = new HashMap<>();
		int columnIndex = this.getColumnIndex();
		BitSet filteredRowsBitSet = new BitSet();
		int startIndex = getFilterView().getStartRowIndexWithoutHeader();
		Sheet sheet = getSheet();
		while(startIndex != -1 && bitSet.nextSetBit(startIndex) != -1)
		{
			int rowIndex = bitSet.nextSetBit(startIndex);
			startIndex = rowIndex;
			Cell cell = this.getReadOnlyCell(rowIndex, columnIndex).getCell();
			if(cell != null)
			{
				Object obj = cell.getValue().getValue();
				if(this.isSupportedObject(obj))
				{
					Object val = this.getObjectValue(obj);
					Double valueNum = ((Number) val).doubleValue();
					rowValueMap.put(rowIndex, valueNum);
					filteredRowsBitSet.set(rowIndex);
				}
			}
			ReadOnlyRow readOnlyRow = sheet.getReadOnlyRowFromShell(rowIndex);
			startIndex += readOnlyRow.getRowsRepeated();
		}
		int size = rowValueMap.size();

		//	IF TOTAL ROWS IS LESS THAN 10 OR 10 PERCENT, NO NEED TO COMPARE. WE CAN ADD ALL THE RECORDS.
		if(size <= totalRows)
		{
			return filteredRowsBitSet;
		}
		filteredRowsBitSet = new BitSet();

		List<Double> values = new ArrayList<>(rowValueMap.values());
		Collections.sort(values);
		Double minVal = values.get(size - totalRows);
		Double maxVal = values.get(totalRows - 1);

		rowValueMap.keySet().stream().filter(row->
		{
			Double value = rowValueMap.get(row);
			return this.getOperator().isTypeTop() ? value >= minVal : this.getOperator().isTypeBottom() && value <= maxVal;
		}).forEach(filteredRowsBitSet::set);

		return filteredRowsBitSet;
	}

	@Override
	public boolean execute()
	{
		return false;
	}
}
