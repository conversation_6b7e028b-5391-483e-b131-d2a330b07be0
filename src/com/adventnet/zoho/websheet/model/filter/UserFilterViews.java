/* $Id$ */
package com.adventnet.zoho.websheet.model.filter;

import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.util.EngineConstants;

import java.util.BitSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> N J (ZT-0049)
 */
public class UserFilterViews
{
	private Map<Integer, FilterView> filterViews;
	private BitSet bitSet;

	public UserFilterViews()
	{
		this.filterViews = new HashMap<>();
		this.bitSet = new BitSet();
	}

	public Map<Integer, FilterView> getFilterViews()
	{
		return this.filterViews;
	}

	private void mapFilterView(int id, FilterView filterView)
	{
		this.filterViews.put(id, filterView);
	}

	public void addFilterView(FilterView filterView)
	{
		this.mapFilterView(filterView.getId(), filterView);
	}

	public FilterView getFilterView(int id)
	{
		FilterView filterView = this.getFilterViews().get(id);
		return filterView != null && !filterView.isEmptyFilterView() ? filterView : null;
	}

	public void removeFilterView(int id)
	{
		FilterView filterView = this.getFilterView(id);
		this.clearFilter(filterView);
		if(id == EngineConstants.DEFAULT_SHEET_FILTER_ID || !hasEmptyFilterView())
		{
			filterView.setTable(null);
		}
		else
		{
			this.getFilterViews().remove(id);
		}
	}

	public boolean hasEmptyFilterView()
	{
		for(FilterView filterView : filterViews.values())
		{
			if(filterView.isEmptyFilterView())
			{
				return true;
			}
		}
		return false;
	}

	public void removeSheetFilterView()
	{
		this.removeFilterView(EngineConstants.DEFAULT_SHEET_FILTER_ID);
	}

	public FilterView getSheetFilterView()
	{
		return this.getFilterView(EngineConstants.DEFAULT_SHEET_FILTER_ID);
	}

	public void setBitSet(BitSet bitSet)
	{
		this.bitSet = bitSet;
	}

	public void orBitSet(BitSet bitSet)
	{
		this.bitSet.or(bitSet);
	}

	public void setVisibleBitSet(BitSet visibleBitSet, FilterView view)
	{
		BitSet filteredBitSet = new BitSet(view.getDataRange().getRowSize());
		filteredBitSet.set(view.getStartRowIndexWithoutHeader(), view.getEndRowIndexWithoutFooter() + 1, true);
		filteredBitSet.and(visibleBitSet);
		this.getBitSet().andNot(filteredBitSet);
	}

	public BitSet getBitSet()
	{
		return bitSet;
	}

	public FilterView getFilterViewCreatedByDefault()
	{
		for(FilterView filterView : filterViews.values())
		{
			if(filterView.isCreatedFromDefault() && !filterView.isEmptyFilterView())
			{
				return filterView;
			}
		}
		return null;
	}

	/**
	 * Resets the visible rows in the filter range to default and all filtered rows will be made as visible.
	 * <br><br>
	 * <b>Caution : Mainly, this method is used when filter is removed / condition is reset to all / new filter is not created properly</b>
	 */
	private void resetVisibility()
	{
		this.bitSet.clear();
	}

	public void clearFilter(FilterView filterView)
	{
		if(filterView != null && !filterView.isEmptyFilterView())
		{
			this.bitSet.andNot(filterView.getBitSet());
		}
	}

	public UserFilterViews clone(Sheet sheet)
	{
		UserFilterViews userFilterViews = new UserFilterViews();
		for(Map.Entry<Integer, FilterView> entry : this.filterViews.entrySet())
		{
			userFilterViews.addFilterView(entry.getValue().clone(sheet, false));
		}
		userFilterViews.bitSet = (BitSet) this.bitSet.clone();
		return userFilterViews;
	}

	public boolean isFilteredRow(int rowIndex)
	{
		return bitSet.get(rowIndex);
	}
}