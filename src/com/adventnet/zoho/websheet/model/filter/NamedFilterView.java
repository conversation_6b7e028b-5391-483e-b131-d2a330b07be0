package com.adventnet.zoho.websheet.model.filter;


import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.writer.zs.XMLWriter;
import com.adventnet.zoho.websheet.model.zsparser.Names;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class NamedFilterView implements Cloneable{

    private final static Logger LOGGER = Logger.getLogger(NamedFilterView.class.getName());

    private String name;
    private DataRange dataRange;
    private Filter filter;

    public NamedFilterView(String name, DataRange dataRange, Filter filter)
    {
        this.name = name;
        this.dataRange = dataRange;
        this.filter = filter;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }

    public DataRange getDataRange()
    {
        return dataRange;
    }

    public void setDataRange(DataRange dataRange)
    {
        this.dataRange = dataRange;
    }

    public Filter getFilter()
    {
        return filter;
    }


    public List<String> getAttributes()
    {
        List<String> attributes = new ArrayList<>();
        attributes.add(XMLWriter.toString(Names.A_FILTERNAME));
        attributes.add(XMLWriter.toString(Names.A_TARGET_RANGE_ADDRESS));
        return attributes;
    }

    public List<String> getValues(Workbook workbook)
    {
        List<String> values = new ArrayList<>();
        values.add(this.name);
        values.add(this.dataRange.toRange(workbook).getCellRangeAddress());
        return values;
    }

    public NamedFilterView clone(Sheet sheet, boolean isSheetClone)
    {
        try
        {
            NamedFilterView clone = (NamedFilterView) super.clone();
            if(isSheetClone)
            {
                clone.dataRange = dataRange.clone();
                clone.dataRange.setAssociatedSheetName(sheet.getAssociatedName());
            }
            clone.filter = filter != null ? filter.clone() : null;
            return clone;
        }
        catch (CloneNotSupportedException e)
        {
            LOGGER.log(Level.SEVERE, "[FILTERS] Named Filter View can't be cloned.");
            return null;
        }
    }
}
