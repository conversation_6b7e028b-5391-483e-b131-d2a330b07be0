//$Id$
package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSTheme;
import com.adventnet.zoho.websheet.model.util.CommandConstants;
import com.adventnet.zoho.websheet.model.zsparser.Names;
import com.adventnet.zoho.websheet.model.zsparser.XmlName;

public class PicklistStyle {

    private ZSColor textColor;
    private ZSColor bgColor;

    public PicklistStyle(ZSColor textColor, ZSColor bgColor) {
        this.textColor = textColor;
        this.bgColor = bgColor;
    }

    public JSONObjectWrapper getJSON(ZSTheme theme) {
        JSONObjectWrapper styleJson = new JSONObjectWrapper();
        if(textColor != null) {
            styleJson.put("tc", ZSColor.getHexColor(this.textColor, theme));
            styleJson.put(Integer.toString(CommandConstants.COLOR), getColorJson(this.textColor, theme));
        }
        if(bgColor != null) {
            styleJson.put("bg", ZSColor.getHexColor(this.bgColor, theme));
            styleJson.put(Integer.toString(CommandConstants.BG_COLOR), getColorJson(this.bgColor, theme));
        }

        return styleJson;
    }

    private static JSONObjectWrapper getColorJson(ZSColor color, ZSTheme theme) {
        JSONObjectWrapper colorJson = new JSONObjectWrapper();
        String hexColor = color.getHexColorToWrite();
        String tint = color.getColorTintToWrite();
        String accent = color.getThemeColorToWrite();

        if(hexColor != null) {
            colorJson.put(Integer.toString(CommandConstants.HEX_COLOR), hexColor);
        }
        else {
            colorJson.put(Integer.toString(CommandConstants.HEX_COLOR),  ZSColor.getHexColor(color, theme));
            colorJson.put(Integer.toString(CommandConstants.TINT), tint);
            colorJson.put(Integer.toString(CommandConstants.THEME_COLOR), accent);
        }

        return colorJson;
    }

    /* commented while removing the usage of ZSColor.getHexColor() method */
//    public String getTextColor() {
//        return textColor != null ? textColor.getHexColor() : null;
//    }
//
//    public String getBgColor() {
//        return bgColor != null ? bgColor.getHexColor() : null;
//    }

    public String getTextThemeColor() {
        String themeColor = null;
        if(this.textColor != null) {
            themeColor = this.textColor.getThemeColorToWrite();
        }
        return themeColor;
    }

    public String getTextTint() {
        String tint = null;
        if(this.textColor != null) {
            tint = this.textColor.getColorTintToWrite();
        }

        return tint;
    }

    public String getTextHex() {
        if(this.textColor == null) {
            return null;
        }

        return this.textColor.getHexColorToWrite();
    }

    public String getBgThemeColor() {
        String themeColor = null;
        if(this.bgColor != null) {
            themeColor = this.bgColor.getThemeColorToWrite();
        }
        return themeColor;
    }

    public String getBgTint() {
        String tint = null;
        if(this.bgColor != null) {
            tint = this.bgColor.getColorTintToWrite();
        }
        return tint;
    }

    public String getBgHex() {
        if(this.bgColor == null) {
            return null;
        }

        return this.bgColor.getHexColorToWrite();
    }

    public ZSColor getTextZSColor() {
        return this.textColor;
    }

    public ZSColor getBgZSColor() {
        return this.bgColor;
    }

    public XmlName[] getAttributes() {
        return new XmlName[]{Names.A_BACKGROUND_COLOR, Names.A_BG_COLOR_TINT, Names.A_BG_THEME_COLOR, Names.A_FONT_COLOR_HEX, Names.A_FONT_COLOR_TINT, Names.A_FONT_COLOR_THEME};
    }

    public String[] getValues() {
        return new String[] {
                this.getBgHex(),
                this.getBgTint(),
                this.getBgThemeColor(),
                this.getTextHex(),
                this.getTextTint(),
                this.getTextThemeColor()
        };
    }

}
