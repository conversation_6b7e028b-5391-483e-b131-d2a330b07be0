// $Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.style.MapStyle;
import com.adventnet.zoho.websheet.model.style.NumberStyle;

/**
 *
 * <AUTHOR>
 */
public class MapStyleAdapter extends MapStyle{
    
    private static final String XML_FORMULA_CONDITION = "is-true-formula"; //No I18N
    private static final String XML_CELLCONTENT_BETWEEN = "cell-content-is-between"; //No I18N 
    private static final String XML_CELLCONTENT_NOTBETWEEN = "cell-content-is-not-between"; //No I18N
    private static final String XML_CELLCONTENT = "cell-content()"; // No I18N

    //For ConditionalStyle
    public MapStyleAdapter(Workbook inWorkbook, String inCondition, String inBaseCellAddress, String inApplyStyleName) throws IllegalArgumentException
    {
        super(inWorkbook, updateCondition(inCondition), inBaseCellAddress, inApplyStyleName);
    }

    public MapStyleAdapter(Workbook inWorkbook, String inCondition, String inBaseCellAddress, String inApplyStyleName, boolean isZSParser, int dependingColIndex) throws IllegalArgumentException
    {
        super(inWorkbook, updateCondition(inCondition), inBaseCellAddress, inApplyStyleName, isZSParser, dependingColIndex);
    }

    //For NumberStyle
    public MapStyleAdapter(Workbook inWorkbook, String inCondition, NumberStyle applyNumberStyle) throws IllegalArgumentException
    {
        super(inWorkbook, updateCondition(inCondition), applyNumberStyle);
    }
    
    private static String updateCondition(String inCondition) throws IllegalArgumentException
    {
        if(inCondition.startsWith(XML_FORMULA_CONDITION))
        {
            inCondition = inCondition.replace(XML_FORMULA_CONDITION, "formula-is"); // No I18N
        }
        else if(inCondition.startsWith(XML_CELLCONTENT_BETWEEN))
        {
            inCondition = inCondition.replace(XML_CELLCONTENT_BETWEEN, "between"); // No I18N
        }
        else if(inCondition.startsWith(XML_CELLCONTENT_NOTBETWEEN))
        {
            inCondition = inCondition.replace(XML_CELLCONTENT_NOTBETWEEN, "not-between"); // No I18N
        }
        else if(inCondition.startsWith(XML_CELLCONTENT))//isComparatorOperator(inCondition.charAt(0)))
        {
            inCondition = inCondition.replace(XML_CELLCONTENT, "");
        }
        return inCondition;
    }
}
