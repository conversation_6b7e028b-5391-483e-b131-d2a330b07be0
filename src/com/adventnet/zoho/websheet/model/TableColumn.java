// $Id$
package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.ext.standard.ZSDeepCopyVisitor;
import com.adventnet.zoho.websheet.model.util.ActionUtil;
import com.adventnet.zoho.websheet.model.util.FormulaUtil;
import com.adventnet.zoho.websheet.model.zsparser.Names;
import com.adventnet.zoho.websheet.model.zsparser.XmlName;
import com.singularsys.jep.JepException;
import com.singularsys.jep.parser.Node;

import java.util.logging.Level;
import java.util.logging.Logger;

public class TableColumn {
    private String columnHeader;
    private Expression calculatedExpression;
    //Mutually Exclusive
    private String footerLabel;
    private Expression footerExpression;
    private String styleName;
    private String headerStyleName;
    private String footerStyleName;

    public static final Logger LOGGER = Logger.getLogger(ActionUtil.class.getName());

    public String getColumnHeader() {
        return columnHeader;
    }

    public void setColumnHeader(String columnHeader) {
        this.columnHeader = columnHeader;
    }

    public Expression getCalculatedExpression() {
        return calculatedExpression;
    }

    public void setCalculatedExpression(Expression calculatedExpression) {
        this.calculatedExpression = calculatedExpression;
    }

    public String getStyleName() {
        return this.styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public void setHeaderStyleName(String styleName) {
        this.headerStyleName = styleName;
    }

    public String getHeaderStyleName() {
        return this.headerStyleName;
    }

    public void setFooterStyleName(String styleName) {
        this.footerStyleName = styleName;
    }

    public String getFooterStyleName() {
        return this.footerStyleName;
    }

    public Expression getFooterExpression() {
        return footerExpression;
    }

    public void setFooterExpression(Expression footerExpression) {
        this.footerExpression = footerExpression;
    }

    public String getFooterLabel() {
        return this.footerLabel;
    }

    public void setFooterLabel(String footerLabel) {
        this.footerLabel = footerLabel;
    }

    public XmlName[] getZSAttributes() {
        return new XmlName[] {
            Names.A_ZSTABLENAME,
            Names.A_CALCULATED_EXPRESSION,
            Names.A_FOOTER_LABEL,
            Names.A_FOOTER_EXPRESSION,
            Names.A_STYLE_NAME,
            Names.A_HEADER_STYLE_NAME,
            Names.A_FOOTER_STYLE_NAME
        };
    }

    public String[] getZSValues(Workbook workbook, String mergedStyleName, String headerStyleName, String footerStyleName) {
        String calculatedExpressionStr = this.getCalculatedExpression() != null ?  FormulaUtil.getFormula(this.getCalculatedExpression().getNode(), workbook, null, true) : null;
        String footerExpressionStr = this.getFooterExpression() != null ? FormulaUtil.getFormula(this.getFooterExpression().getNode(), workbook, null, true) : null;

        return new String[] {
            this.getColumnHeader(),
            calculatedExpressionStr,
            this.getFooterLabel(),
            footerExpressionStr,
            mergedStyleName,
            headerStyleName,
            footerStyleName
        };
    }

    public TableColumn clone(Workbook workbook, ZSDeepCopyVisitor visitor) {
        TableColumn tableColumnCloned = new TableColumn();
        tableColumnCloned.setColumnHeader(this.getColumnHeader());

        Expression calculatedExpression = this.getCalculatedExpression();
        if(calculatedExpression != null && calculatedExpression.hasStructuredReference(workbook)) {
            try {
                Node newNode = visitor.deepCopy(calculatedExpression.getNode());
                calculatedExpression = new ExpressionImpl(newNode);
            }
            catch(JepException e) {
                calculatedExpression = this.getCalculatedExpression();
                LOGGER.log(Level.WARNING, " [Table][Formula] exception during clone CalculatedExpression for {0} exception {1}  : ", new Object[]{this.getCalculatedExpression().toString(), e.toString()});
            }
            tableColumnCloned.setCalculatedExpression(calculatedExpression);
        }

        Expression footerExpression = this.getFooterExpression();
        if(footerExpression != null && footerExpression.hasStructuredReference(workbook)) {
            try {
                Node newNode = visitor.deepCopy(footerExpression.getNode());
                footerExpression = new ExpressionImpl(newNode);
            }
            catch(JepException e) {
                footerExpression = this.getFooterExpression();
                LOGGER.log(Level.WARNING, " [Table][Formula] exception during clone FooterExpressions for {0} exception {1}  : ", new Object[]{this.getFooterExpression().toString(), e.toString()});
            }
            tableColumnCloned.setFooterExpression(footerExpression);
        }
        tableColumnCloned.setFooterLabel(this.getFooterLabel());
        tableColumnCloned.setStyleName(this.getStyleName());
        tableColumnCloned.setHeaderStyleName(this.getHeaderStyleName());
        tableColumnCloned.setFooterStyleName(this.getFooterStyleName());

        return tableColumnCloned;
    }
}
