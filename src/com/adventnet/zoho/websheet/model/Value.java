// $Id:$
/*
 * Value.java
 *
 * Created on January 3, 2008, 5:05 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;


import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.ext.*;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.LocaleUtil;

import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.Format;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.StringTokenizer;
import java.util.regex.Pattern;

import org.apache.commons.math.fraction.Fraction;

/**
 *
 * <AUTHOR>
 */
public interface Value extends Cloneable, Comparable<Value>
{


    public static final Value EMPTY_VALUE = new ValueI(Type.UNDEFINED, null);
    static final Value VALUE_BOOLEAN_TRUE = new ValueI(Type.BOOLEAN, true);
    static final Value VALUE_BOOLEAN_FALSE = new ValueI(Type.BOOLEAN, false);
    static final Value ZERO_VALUE = new ValueI(Type.FLOAT, 0);

    static boolean isTypeValueMatch(Type type, Object value)
    {
        switch(type)
        {
            case STRING:
                return (value instanceof String || value instanceof ZSString);
            case FLOAT:
            case CURRENCY:
            case FRACTION:
            case PERCENTAGE:
            case SCIENTIFIC:
                return value instanceof Number;
            case DATE:
            case TIME:
            case DATETIME:
                return value instanceof Date;
            case BOOLEAN:
                return value instanceof Boolean;
            case ERROR:
                return (value instanceof Throwable || value instanceof Cell.Error);
            case UNDEFINED:
                return value == null;
            default:
                return true;
        }
    }

    public static Value getInstance(Type type, Object value)
    {
        if(!isTypeValueMatch(type, value))
        {
            throw new IllegalArgumentException("Should be fixed : TYPE - VALUE mismatch >>> "+type+" : "+value); //No I18N
        }

        if(type == Type.FLOAT && ((Number)value).equals(0))
        {
            return ZERO_VALUE;
        }

        switch (type) {
            case UNDEFINED:
                return EMPTY_VALUE;
            case BOOLEAN:
                if(((Boolean)value) == true)
                {
                    return VALUE_BOOLEAN_TRUE;
                }
                else
                {
                    return VALUE_BOOLEAN_FALSE;
                }
            case ERROR:
                Cell.Error errorValue = null;
                if(value instanceof Throwable)
                {
                    errorValue = Error.getError(((Throwable)value).getMessage());
                }
                else
                {
                    errorValue = (Cell.Error)value;
                }

                return errorValue.getValueObject();
            default:
                break;
        }

        return new ValueI(type, value);
    }

    public static Value getInstance(String value, Locale locale)
    {
        if(locale == null){
            locale = EngineConstants.DEFAULT_LOCALE;
        }
        return getInstance(value, SpreadsheetSettings.getInstance(locale));
    }

    public static Value getInstance(String value, SpreadsheetSettings spreadsheetSettings){
        if(spreadsheetSettings == null){
            spreadsheetSettings = SpreadsheetSettings.defaultSpreadsheetSettings;
        }
        return parseAsValueObject(value, spreadsheetSettings, true).getValue();
    }

    //This will take the cell value as String and return the corresponding Object.
    public static com.adventnet.zoho.websheet.model.ext.ParsedValue parseAsValueObject(String cellValue, SpreadsheetSettings spreadsheetSettings, boolean considerLargeNumberAsText)
    {
        if(spreadsheetSettings == null)
        {
            spreadsheetSettings = SpreadsheetSettings.defaultSpreadsheetSettings;
        }
        Locale userLocale = spreadsheetSettings.getLocale();
        if(userLocale == null){
            userLocale = EngineConstants.DEFAULT_LOCALE;
        }

        ParsedDate.DateProperties dateProperties = null;
        ParsedDate.TimeProperties timeProperties = null;


        if(cellValue == null)
        {
            return new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.EMPTY_VALUE, Type.UNDEFINED, userLocale, dateProperties, timeProperties);
        }

        //Check whether the cellValue is a boolean.
        if("true".equalsIgnoreCase(cellValue))
        {
            return new com.adventnet.zoho.websheet.model.ext.ParsedValue(VALUE_BOOLEAN_TRUE, Type.BOOLEAN, userLocale, dateProperties, timeProperties);
        }
        else if("false".equalsIgnoreCase(cellValue))
        {
            return new com.adventnet.zoho.websheet.model.ext.ParsedValue(VALUE_BOOLEAN_FALSE, Type.BOOLEAN, userLocale, dateProperties, timeProperties);
        }

        // if cellValue starts with '= then it is a String
        if(cellValue.startsWith("'"))
        {
            // set value without "'"
            Value value = Value.getInstance(Type.STRING, new ZSString(cellValue.substring(1), true));
            return new com.adventnet.zoho.websheet.model.ext.ParsedValue(value, Type.UNDEFINED, userLocale, dateProperties, timeProperties);
        }
        else if(cellValue.length() > 255 || cellValue.contains("\n"))
        {
            // set value without "'"
            Value value = Value.getInstance(Type.STRING, cellValue);
            return new com.adventnet.zoho.websheet.model.ext.ParsedValue(value, Type.UNDEFINED, userLocale, dateProperties, timeProperties);
        }

        String trimmedValue = cellValue.trim();

        DecimalFormatSymbols userDFS = new DecimalFormatSymbols(userLocale);

        //Locale specific Symbols.
        String percentSymbol = String.valueOf(userDFS.getPercent());

        Locale currencyLocale = spreadsheetSettings.getCurrencyLocale();
        DecimalFormatSymbols currencyDFS = new DecimalFormatSymbols(currencyLocale);
        String currSymbol = LocaleUtil.getCurrencySymbol(currencyDFS.getCurrency().getCurrencyCode());

        //This has to be done before creating the value object.
        // If user Locale is India and value contains Rs. replace it with new Rupee symbol unicode and proceed.
        String currencyCountry = currencyLocale.getCountry();
        if (currencyCountry != null && currencyCountry.equals("IN")) {
            DecimalFormatSymbols tempDFS = new DecimalFormatSymbols(LocaleUtil.getLocale("en", "IN"));//No I18N
            if (cellValue.contains(tempDFS.getCurrencySymbol()))// Checking whether value contains Rs.
            {
                // Change Rs. to new Rupee symbol in value String
                cellValue = cellValue.replace(tempDFS.getCurrencySymbol(), LocaleUtil.getCurrencySymbol(tempDFS.getCurrency().getCurrencyCode()));
            }
        }
        ////////////////////////////////////////

        boolean isScientific = (trimmedValue.contains("e") || trimmedValue.contains("E"));
        boolean isPercent = trimmedValue.endsWith(percentSymbol) || trimmedValue.endsWith(percentSymbol+")");
        boolean isCurrency = trimmedValue.contains(currSymbol);
        boolean isFraction = trimmedValue.contains("/");


        //if(!(isPercent || isCurrency))
        // We are identifying both user locale currency and $ as currency values.
        // The below code checks whether the value str contains $ if user locale curr symbol is not there.
        if(!isCurrency)
        {
            currencyLocale = EngineConstants.DEFAULT_LOCALE;
            String default_CurrSymbol = LocaleUtil.getCurrencySymbol(EngineConstants.DEFAULT_CURRENCY_CODE);
            isCurrency = trimmedValue.contains(default_CurrSymbol);
            if(isCurrency)
            {
                currSymbol = default_CurrSymbol;
            }
        }

        // if it contains more than one "/", its not a Fraction.
//     if(isFraction)
//     {
//         String temp = trimmedValue.replaceFirst("/", "");
//         isFraction = !temp.contains("/");
//     }

        com.adventnet.zoho.websheet.model.ext.ParsedValue parsedValue;

        //Check whether the trimmedValue is a number, percentage, currency, date, time or string
        //and convert it to the corresponding object.
        if(isPercent)
        {
            if(isCurrency)
            {
                parsedValue = getValue(Type.STRING, trimmedValue, spreadsheetSettings, currSymbol, considerLargeNumberAsText);
            }
            else if(isScientific)
            {
                String temp = trimmedValue.substring(0, trimmedValue.length() - 1);
                parsedValue = getValue(Type.SCIENTIFIC, temp, spreadsheetSettings, currSymbol, considerLargeNumberAsText);

                if(parsedValue != null && parsedValue.getPatternType() != Type.STRING)
                {
                    if(parsedValue.getValue().getValue() instanceof Number)
                    {
                        parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.FLOAT, ((Number)parsedValue.getValue().getValue()).doubleValue() / 100), parsedValue.getPatternType(), currencyLocale, null, null);
                    }
                    else
                    {
                        parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.STRING, cellValue), Type.UNDEFINED, currencyLocale, null, null);
                    }
                }
            }
            else if(isFraction)
            {
                String temp = trimmedValue.substring(0, trimmedValue.length() - 1);
                parsedValue = getValue(Type.FRACTION, temp, spreadsheetSettings, currSymbol, considerLargeNumberAsText);

                if(parsedValue != null && parsedValue.getPatternType() != Type.STRING)
                {
                    if(parsedValue.getValue().getValue() instanceof Number)
                    {
                        parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.FLOAT, ((Number)parsedValue.getValue().getValue()).doubleValue() / 100), Type.PERCENTAGE, currencyLocale, null, null);
                    }
                    else
                    {
                        parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.STRING, cellValue), Type.UNDEFINED, currencyLocale, null, null);
                    }
                }
            }
            else
            {
                parsedValue = getValue(Type.PERCENTAGE, trimmedValue, spreadsheetSettings, currSymbol, considerLargeNumberAsText);
            }
        }
        else if(isScientific)
        {
            parsedValue = getValue(Type.SCIENTIFIC, trimmedValue, spreadsheetSettings, currSymbol, considerLargeNumberAsText);
        }
        else if(isFraction)
        {
            parsedValue = getValue(Type.FRACTION, trimmedValue, spreadsheetSettings, currSymbol, considerLargeNumberAsText);
        }
        else if(isCurrency)
        {
            parsedValue = getValue(Type.CURRENCY, trimmedValue, spreadsheetSettings, currSymbol, considerLargeNumberAsText);
        }
        else
        {
            parsedValue = getValue(Type.UNDEFINED, trimmedValue, spreadsheetSettings, currSymbol, considerLargeNumberAsText);
        }

        if(cellValue.equalsIgnoreCase(CellUtil.getErrorString(Error.NA)))
        {
            parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.ERROR, Error.NA), Type.ERROR, currencyLocale, null, null);
        }
        else if(parsedValue == null)
        {
            parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.STRING, cellValue), Type.UNDEFINED, currencyLocale, null, null);
        }

//     Type patternType = parsedValue.getType();
//     if(patternType.isNumberType())
//     {
//         if(isCurrency)
//         {
//             patternType = Type.CURRENCY;
//         }
//         else if(isFraction)
//         {
//             patternType = Type.FRACTION;
//         }
//         else if(isPercent)
//         {
//             patternType = Type.PERCENTAGE;
//         }
//         else if(isScientific)
//         {
//             patternType = Type.SCIENTIFIC;
//         }
//     }else if(patternType.isDateType()){
//         ZSDate zsDate = ((ZSDate)parsedValue.getRawValue());
//         patternType = zsDate.getType();
//         dateProperties = zsDate.getDateProperties();
//         timeProperties = zsDate.getTimeProperties();
//     }
        parsedValue.setCurrencyLocale(currencyLocale);
        return parsedValue;
    }

    static com.adventnet.zoho.websheet.model.ext.ParsedValue getValue(Type type, String trimmedValue, SpreadsheetSettings spreadsheetSettings, String currSymbol, boolean considerLargeNumberAsText)
    {

        Locale locale = spreadsheetSettings.getLocale();
        if(locale == null){
            locale = EngineConstants.DEFAULT_LOCALE;
        }


        //logger.info("TYPE =====> "+type);
        com.adventnet.zoho.websheet.model.ext.ParsedValue parsedValue = null;

        if (type == Type.UNDEFINED || type == Type.CURRENCY || type == Type.FRACTION || type == Type.PERCENTAGE || type == Type.SCIENTIFIC || type == Type.FLOAT)
        {
            // Do not change anything in trimmed value inside this if block. Use numberStr alone.
            String numberStr = trimmedValue;

            if ((numberStr.startsWith("(") && (numberStr.endsWith(")") || numberStr.endsWith(")" + currSymbol) || numberStr.endsWith(")%")))
                    || (numberStr.startsWith(currSymbol + "(") && numberStr.endsWith(")")))
            {
                numberStr = numberStr.replaceFirst("[(]", "");
                numberStr = numberStr.replaceFirst("[)]", "");
                numberStr = "-" + numberStr;
            }

            char groupingSeparator = spreadsheetSettings.getThousandSeparator();
            char decimalSeparator = spreadsheetSettings.getDecimalSeparator();
            parsedValue = getNumberValue(numberStr, type, locale, currSymbol, groupingSeparator, decimalSeparator, considerLargeNumberAsText);

            // Handling for space, Non-breaking space and Narrow No-breaking space as grouping separator
            if(spreadsheetSettings.isCheckWithSpaceGrouping()) {
                if (parsedValue == null && groupingSeparator != 8239) // Narrow Non-breaking Space
                {
                    parsedValue = getNumberValue(numberStr, type, locale, currSymbol, (char)8239, decimalSeparator, considerLargeNumberAsText);
                }
                if (parsedValue == null && groupingSeparator != 160) // No-breaking Space
                {
                    parsedValue = getNumberValue(numberStr, type, locale, currSymbol, (char)160, decimalSeparator, considerLargeNumberAsText);
                }
                if (parsedValue == null && groupingSeparator != ' ') // Space
                {
                    parsedValue = getNumberValue(numberStr, type, locale, currSymbol, ' ', decimalSeparator, considerLargeNumberAsText);
                }
            }
        }

        if(parsedValue == null)
        {
            
            ParsedDate parsedDate = DateUtil.parseDateForValue(trimmedValue, spreadsheetSettings);
            if(parsedDate != null)
            {
                parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(parsedDate.getType(), new ZSDate(parsedDate.getTime())), parsedDate.getType(), locale, parsedDate.getDateProperties(), parsedDate.getTimeProperties());
            }
            else
            {
                // to capture special OpenOffice type date values.
                // According to the present implementation if the trimmed value is
                // a date, then it will have a ":".
                if(trimmedValue.contains(":"))
                {
                    SimpleDateFormat sdf;
                    // for data-type 1899-12-30T20:59:17.48161259804
                    try
                    {
                        sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.S");
                        parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.DATETIME, sdf.parse(trimmedValue)), Type.DATETIME, locale, null, null);
                    }
                    catch(ParseException ex)
                    {
                        try
                        {
                            sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                            parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.DATETIME, sdf.parse(trimmedValue)), Type.DATETIME, locale, null, null);
                        }
                        catch(ParseException exc)
                        {
                            try
                            {
                                sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm");
                                parsedValue = new ParsedValue(Value.getInstance(Type.DATETIME, sdf.parse(trimmedValue)), Type.DATETIME, locale, null, null);
                            }
                            catch(ParseException e)
                            {
                                //logger.info("NOT A DATE......");
                            }
                        }
                    }
                }
            }
        }

        return parsedValue;
    }

    private static ParsedValue getNumberValue(String numberStr, Type type, Locale locale, String currSymbol, char groupingSeparator, char decimalSeparator,  boolean considerLargeNumberAsText)
    {
        ParsedValue parsedValue = null;
        //Locale locale = spreadsheetSettings.getLocale();
        if(locale == null){
            locale = EngineConstants.DEFAULT_LOCALE;
        }

        DecimalFormatSymbols dfs = new DecimalFormatSymbols(locale);
        dfs.setDecimalSeparator(decimalSeparator);
        dfs.setGroupingSeparator(groupingSeparator);

        Pattern[] patterns = LocaleUtil.getRegexPatternArray(locale, decimalSeparator, groupingSeparator, currSymbol);
        try
        {
            //Check whether the numberStr is a number, percentage, currency, date, time or string
            //and convert it to the corresponding object.
            switch (type) {
                case UNDEFINED:
                case FLOAT:
                case SCIENTIFIC:
                    Pattern numberRegexPattern = type.equals(Type.SCIENTIFIC) ? patterns[1] : patterns[0];
                    if (numberRegexPattern.matcher(numberStr).matches())
                    {
                        DecimalFormat decf = new DecimalFormat();
                        decf.setDecimalFormatSymbols(dfs);// This will set the locale to DecimalFormat.

                        // if "+" is used to represent positive numbers
                        if (numberStr.startsWith("+"))
                        {
                            decf.setPositivePrefix("+");
                        }

                        if(type.equals(Type.SCIENTIFIC))
                        {
                            //Decimal format doesnt support prefix"+" in tne exponent so remove that before parsing.
                            numberStr = numberStr.replace("e", "E");//No I18N
                            numberStr = numberStr.replace("E+", "E");//No I18N
                        }

                        Number number = decf.parse(numberStr);
                        // if the given number is too large. the cell Vtype will become a string.
                        if(((Double)number.doubleValue()).isInfinite())
                        {
                            return null;
                        }

                        if(considerLargeNumberAsText && type != Type.SCIENTIFIC && Math.abs(number.doubleValue()) > 999999999999999D)
                        {
                            int len = 0;
                            char d = dfs.getDecimalSeparator();
                            char g = dfs.getGroupingSeparator();
                            char z = dfs.getZeroDigit();
                            for(int i = 0; i < numberStr.length(); i++)
                            {
                                char c = numberStr.charAt(i);
                                if(c == g || c == '-' || c =='+')
                                {
                                    continue;
                                }
                                else if(c == d)
                                {
                                    break;
                                }
                                if(len > 0 || c != z)
                                {
                                    len += 1;
                                    if(len > 15)
                                    {
                                        // NOTE: When NonZeroDigit Occurs after the 15th Digit. the cell value type will become string.
                                        return null;
                                    }
                                }
                            }
                        }

                        Value value = Value.getInstance(Type.FLOAT, number);
                        Type patternType = Type.UNDEFINED;
                        if(type == Type.SCIENTIFIC){
                            patternType = Type.SCIENTIFIC;
                        }else if(numberStr.indexOf(dfs.getGroupingSeparator()) != -1){
                            // Setting PatternType as Number.
                            patternType = Type.FLOAT;
                        }
                        parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(value, patternType, locale, null, null);

                    }
                    break;
                case FRACTION:
                    if(patterns[2].matcher(numberStr).matches() || patterns[3].matcher(numberStr).matches())
                    {
                        if(numberStr.startsWith("+"))
                        {
                            numberStr = numberStr.replace("+", "");
                        }
                        // Fraction format does not depend on locale.
                        Format f = new ZFractionFormat(locale);

                        // if the value given is like "0 a/b" Properfraction format makes it zero
                        // so remove the zero with the space before parsing.
                        StringTokenizer stt = new StringTokenizer(numberStr, " ");//No I18N
                        //logger.info("count token : "+stt.countTokens());

                        if(stt.countTokens() == 2)
                        {
                            Number num = NumberFormat.getNumberInstance().parse(stt.nextToken());
                            if((float)num.floatValue() == 0)
                            {
                                numberStr = stt.nextToken(); // 2nd element in the token
                            }
                        }
                        ///

                        try
                        {
                            Fraction fr = ((ZFractionFormat) f).parse(numberStr);
                            Number number = fr.doubleValue();
                            parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.FLOAT, number), Type.FRACTION, locale, null, null);
                        }
                        catch(ArithmeticException e)
                        {
                            return null;
                        }
                    }
                    break;
                case PERCENTAGE:
                    if(patterns[4].matcher(numberStr).matches())
                    {
                        numberStr = numberStr.replace("%", "").trim();
                        parsedValue = getNumberValue(numberStr, Type.FLOAT, locale, currSymbol, groupingSeparator, decimalSeparator, considerLargeNumberAsText);
                        if(parsedValue != null && parsedValue.getValue().getType() == Type.FLOAT)
                        {
                            parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(Value.getInstance(Type.FLOAT, ((Number)parsedValue.getValue().getValue()).doubleValue()/100),Type.PERCENTAGE, locale, null, null);
                        }
                    }
                    break;
                case CURRENCY:
                    if(patterns[5].matcher(numberStr).matches() || patterns[6].matcher(numberStr).matches() || patterns[7].matcher(numberStr).matches())
                    {
                        numberStr = numberStr.replace(currSymbol, "").trim();
                        parsedValue = getNumberValue(numberStr, Type.FLOAT, locale, currSymbol, groupingSeparator, decimalSeparator, considerLargeNumberAsText);
                        if(parsedValue != null && parsedValue.getValue().getType() == Type.FLOAT)
                        {
                            parsedValue = new com.adventnet.zoho.websheet.model.ext.ParsedValue(parsedValue.getValue(), Type.CURRENCY, locale, null, null);
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        catch(ParseException ex)
        {
            //ex.printStackTrace();
            //logger.info("THE GIVEN STRING IS NOT A NUMBER, SCIENTIFIC NUMBER, FRACTION, PERCENTAGE or CURRENCY.");
        }

        return parsedValue;
    }

    public static Double roundTo15DP(Number num)
    {
        return roundTo15DP(num, 0);
    }

    // P is used while calling this method recusively.
    // Recursive call will happen if the number is not rounding to 15 digits. In that case we will try to round to 14 digits and go on. For this p is used. P = 0 by default and on every recursive call p will be incremented. We have a limit for the recursive calls p<3.
    private static Double roundTo15DP(Number num, int p)
    {
        boolean isNegative =num.doubleValue() < 0;
        boolean hasIntegralPart = num.intValue() != 0;
        String numStr = num.toString();

        int e_Index = numStr.indexOf("E");
        String expStr = "";
        if (e_Index != -1)
        {
            expStr = numStr.substring(e_Index);
            numStr = numStr.substring(0, e_Index);
        }

        int dotIndex = numStr.indexOf(".");
        if (dotIndex != -1)
        {
            String decimalStr = numStr.substring(dotIndex + 1, numStr.length());
            int decPlaces = decimalStr.length();

            int dp = 15;
            if(hasIntegralPart)
            {
                dp = 15 - dotIndex;
                if(isNegative)
                {
                    dp += 1;
                }
            }else {
                for(char c : decimalStr.toCharArray()){
                    if(c == '0'){
                        dp++;
                    }else {
                        break;
                    }
                }
            }
            if (decPlaces > dp)
            {
                dp = dp - p;
                if (numStr.charAt(dotIndex + dp) >= '5' // 15th digit
                        && numStr.charAt(dotIndex + dp + 1) >= '5')// 16th digit
                {
                    numStr = numStr.substring(0, dotIndex + dp + 1);
                    Number result = Double.parseDouble(numStr) + Math.pow(10.0, -dp);

                    if(p < 3) {
                        result = roundTo15DP(result, p + 1);
                    }

                    if(e_Index == -1)
                    {
                        return result.doubleValue();
                    }
                    else
                    {
                        numStr = result.toString();
                    }
                }
                else
                {
                    numStr = numStr.substring(0, dotIndex + dp + 1);
                }

                if(e_Index != -1)
                {
                    numStr += expStr;
                }

                return Double.parseDouble(numStr);
            }
        }

        return num.doubleValue();
    }

    public int compareTo(Value value);

    public Cell.Type getType();

    public Object getValue();

    public Object getRawValue();

    public String getValueString(SpreadsheetSettings spreadsheetSettings);

    public String getValueString(SpreadsheetSettings spreadsheetSettings, Cell.Type patternType);

    public boolean isPrefixApos();

    default int getStringLength() {
        return 0;
    }
}
