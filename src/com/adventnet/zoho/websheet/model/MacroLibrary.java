//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class MacroLibrary implements Cloneable
{
    private static Logger logger = Logger.getLogger(MacroLibrary.class.getName());

    private String libraryName;
    private String readOnly = "false";
    private String passwordProtected = "false";
    private String libraryLink = "false";
    private List<MacroModule> macroModuleList= new ArrayList<>();
    ////
    private transient List<String> macroModuleNameList= new ArrayList<>(); // to maintain order

    public MacroLibrary(String libraryName)
    {
	this.libraryName = libraryName;
    }

    public void setLibraryName(String libraryName)
    {
        this.libraryName = libraryName;
    }

    public String getLibraryName( )
    {
        return(this.libraryName);
    }

    public void setReadOnly(String readOnly)
    {
        this.readOnly = "false"; //hard coded
    }

    public void setPasswordProtected(String passwordProtected)
    {
        this.passwordProtected = "false"; //hard coded
    }

    public void setLibraryLink(String libraryLink)
    {
        this.libraryLink = "false"; //hard coded
    }

    public List<MacroModule> getMacroModuleList()
    {
	return macroModuleList;
    }

    public void addMacroModule(MacroModule macroModule)
    {
        macroModuleList.add(macroModule);
    }

    public MacroModule getMacroModule(String macroModuleName)
    {
        for(int i=0; i<macroModuleList.size(); i++)
	{
	    MacroModule macroModule = macroModuleList.get(i);
	    if(macroModule.getName().equals(macroModuleName))
	    {
		return macroModule;
	    }
	}
	return null;
    }

    public void removeMacroModule(String macroModuleName)
    {
        for(int i=0; i<macroModuleList.size(); i++)
	{
	    MacroModule macroModule = macroModuleList.get(i);
	    if(macroModule.getName().equals(macroModuleName))
	    {
		macroModuleList.remove(i);
		break;
	    }
	}
    }

    public void addMacroModuleName(String macroModuleName)
    {
	macroModuleNameList.add(macroModuleName);
    }

    public void reorder()
    {
	// executes only once
	if(!macroModuleNameList.isEmpty())
	{
	    List<MacroModule> temp = new ArrayList<>();
	    for(String name : macroModuleNameList)
	    {
		temp.add(getMacroModule(name));
	    }
	    macroModuleList = temp;
	    macroModuleNameList = new ArrayList<>();
	}
    }

    public String[] getAttributes()
    {
        String[] attrs = new String[]{"library:name",                //NO I18N
                                      "library:readonly",            //NO I18N
                                      "library:passwordprotected"};  //NO I18N
        return attrs;
    }
    public String[] getValues()
    {
        String[] values = new String[]{libraryName,
                                       readOnly,
                                       passwordProtected};
        return values;
    }

    // for library:libraries child
    public String[] getLibraryAttributes()
    {
        String[] attrs = new String[]{"library:name",//no i18n
                                      "library:link"};//no i18n
        return attrs;
    }
    public String[] getLibraryValues()
    {
        String[] values = new String[]{libraryName,
                                      libraryLink};//no i18n
        return values;
    }

    public MacroLibrary clone()
    {
	MacroLibrary o = null;
	try
	{
	    o = (MacroLibrary) super.clone();
	}
	catch (CloneNotSupportedException e)
	{
	    logger.info("MacroLibrary can't clone");
	}

	o.macroModuleList = new ArrayList<>();
	for(MacroModule macroModule : macroModuleList)
	{
	    o.macroModuleList.add(macroModule.clone());
	}

	return o;
    }
}