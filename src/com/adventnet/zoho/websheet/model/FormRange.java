/* $Id$ */
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.CellReference.ReferenceMode;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.util.EngineConstants;

/**
 *
 * <AUTHOR>
 */
public class FormRange extends Range
{
    // used from Transformer.
    public FormRange(Workbook workbook, String cellRangeAddress, String baseCellAddress) throws SheetEngineException
    {
        super(workbook, cellRangeAddress, baseCellAddress, ReferenceMode.A1, false);
    }

    public FormRange(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        super(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex);
    }

    public String[] getAttributes()
    {
        String[] attrs =
        {
            "table:name",//No I18N
            "table:base-cell-address",//No I18N
            "table:cell-range-address"//No I18N
        };

        return attrs;
    }

    public String[] getValues()
    {
        String[] values =
        {
            EngineConstants.FORMRANGE_NAME+this.getSheet().getSheetIndex(),//No I18N
            "$'"+this.getSheet().getName()+"'.$A$1", // ProtectedRange will be always absolute. so basecelladdress has no significance. //No I18N
            this.getCellRangeAddress(),
        };

        return values;
    }
}
