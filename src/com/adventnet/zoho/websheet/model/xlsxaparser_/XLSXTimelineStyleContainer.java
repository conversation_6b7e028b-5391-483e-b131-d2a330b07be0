package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.TimeLineSlicer;
import com.adventnet.zoho.websheet.model.style.BorderProperties;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.fill.Fill;
import com.adventnet.zoho.websheet.model.style.fill.GradientFill;
import com.adventnet.zoho.websheet.model.style.fill.PatternFill;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;

public class XLSXTimelineStyleContainer {

    private enum ThemeType
    {
        LIGHT,
        DARK,
    }


    public static XLSXTimelineStyleContainer getPredefinedTimelineStyle(String styleName)
    {
        ThemeType themeType = styleName.contains("Light") ? ThemeType.LIGHT : ThemeType.DARK;//No I18N
        char accentNumber = styleName.charAt(styleName.length() - 1);

        XLSXTimelineStyleContainer style = new XLSXTimelineStyleContainer();
        style.setCellStyle(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE, getCellStyle(TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE, themeType, accentNumber));
        style.setCellStyle(TimeLineSlicer.TimeLineStyle.HEADER_STYLE, getCellStyle(TimeLineSlicer.TimeLineStyle.HEADER_STYLE,themeType, accentNumber));
        style.setCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE, getCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE, themeType, accentNumber));
        style.setCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE, getCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE, themeType, accentNumber));
        style.setCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE, getCellStyle(TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE, themeType, accentNumber));
        style.setCellStyle(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE, getCellStyle(TimeLineSlicer.TimeLineStyle.TIME_LEVEL_STYLE, themeType, accentNumber));
        style.setCellStyle(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE, getCellStyle(TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE, themeType, accentNumber));
        style.setCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE, getCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL1_STYLE, themeType, accentNumber));
        style.setCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE, getCellStyle(TimeLineSlicer.TimeLineStyle.PERIOD_LABEL2_STYLE, themeType, accentNumber));

        return style;

    }

    private static CellStyle getCellStyle(TimeLineSlicer.TimeLineStyle styleType, ThemeType themeType, char accentNumber)
    {
        if(styleType == TimeLineSlicer.TimeLineStyle.ITEM_OFF_SPACE_STYLE)
        {
            return null;
        }

        ZSColorScheme.Colors accent = ZSColorScheme.Colors.valueOf("ACCENT"+accentNumber); //No I18N

        CellStyle cellStyle = new CellStyle();
        if(styleType == TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE)
        {
            ZSColor accentColor = ZSColor.getInstance(accent, 0.0);
            BorderProperties border = BorderProperties.getInstance("solid", accentColor, 0.0138);
            cellStyle.setProperty(CellStyle.Property.BORDERBOTTOM, border);
            cellStyle.setProperty(CellStyle.Property.BORDERTOP, border);
            cellStyle.setProperty(CellStyle.Property.BORDERLEFT, border);
            cellStyle.setProperty(CellStyle.Property.BORDERRIGHT, border);
        }
        else if (styleType == TimeLineSlicer.TimeLineStyle.HEADER_STYLE){
            cellStyle.setProperty(TextStyle.Property.FONTWEIGHT, "bold");
        }


        // set text properties to whole table, header, slider label, periodLabel1, periodLabel2 & time level styles.
        if( styleType != TimeLineSlicer.TimeLineStyle.ITEM_ON_STYLE &&
                styleType != TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE &&
                styleType != TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE )
        {
            ZSColorScheme.Colors textAccent = styleType == TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE ? ZSColorScheme.Colors.ACCENT1 : ZSColorScheme.Colors.TEXT1;
            double textColorTint = styleType == TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE || styleType == TimeLineSlicer.TimeLineStyle.HEADER_STYLE ? 0.0 :
                    styleType == TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE ? -0.249977 : 0.499984;
            double textSize = styleType == TimeLineSlicer.TimeLineStyle.HEADER_STYLE || styleType == TimeLineSlicer.TimeLineStyle.WHOLE_TABLE_STYLE ? 11 : styleType == TimeLineSlicer.TimeLineStyle.SLIDER_LABEL_STYLE ? 10 : 9;

            ZSColor textColor = ZSColor.getInstance(textAccent, textColorTint);
            cellStyle.setProperty(TextStyle.Property.COLOR, textColor);
            cellStyle.setProperty(TextStyle.Property.FONTSIZE, String.valueOf(textSize));
        }
        // set gradients to item on/off/onSpace styles.
        else {
            if(styleType == TimeLineSlicer.TimeLineStyle.ITEM_ON_SPACE_STYLE)
            {
                ZSColor spaceColor = ZSColor.getInstance(accent, 0.399955);
                cellStyle.setProperty(CellStyle.Property.FILL, PatternFill.getInstance(PatternFill.PatternType.SOLID, spaceColor, spaceColor));
            }
            else {
                double degree = 90;
                double startTint;
                double endTint;

                if(styleType == TimeLineSlicer.TimeLineStyle.ITEM_OFF_STYLE)
                {
                    accent = ZSColorScheme.Colors.BACKGROUND1;
                    startTint = themeType == ThemeType.LIGHT ? -0.1499 : -0.2499;
                    endTint = startTint;
                }
                else {
                    startTint = themeType == ThemeType.LIGHT ? 0.59999 : 0.0;
                    endTint = themeType == ThemeType.LIGHT ? 0.0 : -0.49999;
                }
                ZSColor stopColor = ZSColor.getInstance(accent, startTint);
                ZSColor startColor = ZSColor.getInstance(accent, endTint);

                Fill fill = GradientFill.getLinearInstance(degree, startColor, stopColor);
                cellStyle.setProperty(CellStyle.Property.FILL, fill);
            }
        }
        return cellStyle;
    }

    private CellStyle wholeTableStyle;
    private CellStyle headerStyle;
    private CellStyle itemOnStyle;
    private CellStyle itemOffStyle;
    private CellStyle sliderLabelStyle;
    private CellStyle timeLevelStyle;
    private CellStyle periodLabel1Style;
    private CellStyle periodLabel2Style;
    private CellStyle itemOnSpaceStyle;

    public void setCellStyle(TimeLineSlicer.TimeLineStyle styleType, CellStyle cellStyle)
    {
        switch (styleType)
        {
            case WHOLE_TABLE_STYLE: this.wholeTableStyle = cellStyle; break;
            case HEADER_STYLE: this.headerStyle = cellStyle; break;
            case ITEM_ON_STYLE: this.itemOnStyle = cellStyle; break;
            case ITEM_OFF_STYLE: this.itemOffStyle = cellStyle; break;
            case ITEM_ON_SPACE_STYLE: this.itemOnSpaceStyle = cellStyle; break;
            case TIME_LEVEL_STYLE: this.timeLevelStyle = cellStyle; break;
            case SLIDER_LABEL_STYLE: this.sliderLabelStyle = cellStyle; break;
            case PERIOD_LABEL1_STYLE: this.periodLabel1Style = cellStyle; break;
            case PERIOD_LABEL2_STYLE: this.periodLabel2Style = cellStyle; break;

        }
    }

    public CellStyle getCellStyle(TimeLineSlicer.TimeLineStyle styleType)
    {
        switch (styleType)
        {
            case WHOLE_TABLE_STYLE: return this.wholeTableStyle;
            case HEADER_STYLE: return this.headerStyle;
            case ITEM_ON_STYLE: return this.itemOnStyle;
            case ITEM_OFF_STYLE: return this.itemOffStyle;
            case ITEM_ON_SPACE_STYLE: return this.itemOnSpaceStyle;
            case TIME_LEVEL_STYLE: return this.timeLevelStyle;
            case SLIDER_LABEL_STYLE: return this.sliderLabelStyle;
            case PERIOD_LABEL1_STYLE: return this.periodLabel1Style;
            case PERIOD_LABEL2_STYLE: return this.periodLabel2Style;
            default:
                throw new IllegalArgumentException("timeline style is available" + styleType.name()); //No I18N
        }
    }
}
