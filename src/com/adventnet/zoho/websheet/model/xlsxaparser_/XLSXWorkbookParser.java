//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.xmlpull.v1.XmlPullParser;

/**
 *
 * <AUTHOR>
 */
class XLSXWorkbookParser extends  XMLFileParser implements XMLParser {
    private XLSXWorkbookRepo repo;
    private Map<String, String> rIdOfName;
    private final XLSXTransformer xlsxt;

    XLSXWorkbookParser(XMLFile xLSXFile, XLSXTransformer xLSXTransformer, XMLPullParserWrapper xpp, List<XLSXException> xlsxException) {
        super(xLSXFile, xpp, xlsxException);
        this.xlsxt = xLSXTransformer;
    }

    @Override
    public void beforeParse() throws XLSXException {
        repo = (XLSXWorkbookRepo) this.xmlFile.getXlsxRepo();
        rIdOfName = new HashMap<>();
    }
    
    @Override
    public void afterParse() {
        
    } 
    
    private void parseSheetNode() {
        String name = xpp.getAttribute(AttributeNameConstants.NAME);
        String rId = xpp.getAttribute(AttributeNameConstants.R_ID);
        int sheetId = Integer.parseInt(xpp.getAttribute(AttributeNameConstants.SHEETID));
        String state = xpp.getAttribute(AttributeNameConstants.STATE);

        repo.addNameOfRId(rId, name);
        repo.addSheetIdOfRid(sheetId,rId);
        this.rIdOfName.put(name, rId);
        
        boolean isHidden = "hidden".equals(state) || "veryHidden".equals(state);//No I18N
        
        this.xlsxt.addSheet(name, isHidden);
    }

    private void parseDefinedNameNode() throws XLSXException {
        String isHidden = xpp.getAttribute(AttributeNameConstants.HIDDEN);
        if(isHidden == null || XLSXParserUtility.isEqualsFalse(isHidden)) {
            String name = xpp.getAttribute(AttributeNameConstants.NAME);
            String localSheetId = xpp.getAttribute(AttributeNameConstants.LOCAL_SHEET_ID);
            String comment = xpp.getAttribute(AttributeNameConstants.COMMENT);
            String range = xpp.getTextInsideElement();
            repo.addRangeOfName(name, range);
            this.xlsxt.addDefinedName(name, range, localSheetId, comment);
        }
    }

    private void parseSheetsEndNode() {
    }

    private void pareWorkbooProtectionNode() throws XLSXException {
        String workbookPassword = xpp.getAttribute(AttributeNameConstants.WORKBOOK_PASSWORD);
        String revisionsPassword = xpp.getAttribute(AttributeNameConstants.REVISIONS_PASSWORD);
        String workbookHashValue = xpp.getAttribute(AttributeNameConstants.WORKBOOK_HASHVALUE);
        String revisionsHashValue = xpp.getAttribute(AttributeNameConstants.REVISIONS_HASHVALUE);

        if(workbookPassword != null || revisionsPassword != null || workbookHashValue != null || revisionsHashValue != null) {
            this.xlsxt.setIsWorkbookPasswordProtected(true);
        }
    }

    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                switch (nodeName) {
                    case XLSXNamespace.X+ElementNameConstants.SHEET:
                    case ElementNameConstants.SHEET:
                        parseSheetsEndNode();
                        break;
                }
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case "calcPr":
                        this.xlsxt.set_fullCalcOnLoad(XLSXParserUtility.isEqualsTrue(xpp.getAttribute("fullCalcOnLoad")));
                        break;
                    case XLSXNamespace.X+ElementNameConstants.SHEET:
                    case ElementNameConstants.SHEET:
                        parseSheetNode();
                        break;
                    case XLSXNamespace.X+ElementNameConstants.DEFINENAME:
                    case ElementNameConstants.DEFINENAME:
                        parseDefinedNameNode();
                        break;
                    case XLSXNamespace.X+ElementNameConstants.WORKBOOK_PROTECTION:
                    case ElementNameConstants.WORKBOOK_PROTECTION:
                        pareWorkbooProtectionNode();
                        break;
                    case XLSXNamespace.X+ElementNameConstants.WORKBOOK_PR:
                    case ElementNameConstants.WORKBOOK_PR:
                        parseWorkbooPrNode();
                        break;
                }
                break;
        }

    }

    private void parseWorkbooPrNode() {
        String date104 = xpp.getAttribute(AttributeNameConstants.DATE1904);
        if (XLSXParserUtility.isEqualsTrue(date104)) {
            this.xlsxt.set1904DateSystem(true);
        }
    }

}
