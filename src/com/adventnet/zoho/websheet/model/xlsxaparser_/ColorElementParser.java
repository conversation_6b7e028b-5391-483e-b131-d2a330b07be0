//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import java.util.List;

import org.xmlpull.v1.XmlPullParser;

/**
 *
 * <AUTHOR>
 */
class ColorElementParser extends XMLElementParser implements XMLParser {
    
    private final XLSXColorBean color = new XLSXColorBean();
    
    ColorElementParser(XMLPullParserWrapper xmlPullParser, List<XLSXException> xlsxException) {
        super(xmlPullParser, xlsxException);
    }
    
    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.FGCOLOR:
                    case ElementNameConstants.BGCOLOR:
                    case ElementNameConstants.COLOR:
                    case ElementNameConstants.TABCOLOR:
                    case ElementNameConstants.BORDER_COLOR:
                    case ElementNameConstants.NEGATIVE_FILL_COLOR:
                    case ElementNameConstants.NEGATIVE_BORDER_COLOR:
                    case ElementNameConstants.AXIS_COLOR:
                        parseColorNode();
                        break;
                }
                break;
        }
    }
    
    private void parseColorNode() {
        String auto = xpp.getAttribute(AttributeNameConstants.AUTO);
        String indexed = xpp.getAttribute(AttributeNameConstants.INDEXED);
        String rgb = xpp.getAttribute(AttributeNameConstants.RGB);
        String theme = xpp.getAttribute(AttributeNameConstants.THEME);
        String tint = xpp.getAttribute(AttributeNameConstants.TINT);
        if (XLSXParserUtility.isEqualsTrue(auto)) {

                color.setIsAuto(true);

        }
        if (indexed != null) {
            color.setIndexed(Integer.valueOf(indexed));
        }
        if (rgb != null) {
            color.setRgb(rgb);
        }
        if (theme != null) {
            color.setTheme(Integer.valueOf(theme));
        }
        if (tint != null) {
            color.setTint(tint);
        }
    }
    
    XLSXColorBean getXLSXColorBean() {
        return this.color;
    }

    //    private static String tintAppliedHex(String hex, float tintValue) {
//        if(hex == null) {
//            return null;
//        }
//        String hexResult = "";
//        for (int i = 0; i < 5; i += 2) {
//            int hls = Integer.parseInt(hex.substring(i, i + 2), 16);
//            int tintAppliedHls = applyTint(hls, tintValue);
//            hexResult += Integer.toHexString(tintAppliedHls);
//        }
//        return hexResult;
//    }
//    
//    private static int applyTint(int hlsValue, float tintValue) {
//        if (tintValue < 0) {
//            hlsValue = (int) (hlsValue * (1.0 + tintValue));
//        } else {
//            hlsValue = (int) (hlsValue * (1.0 - tintValue) + 255 - (int) (255 * (1.0 - tintValue)));
//        }
//        return hlsValue;
//    }


    private static String tintAppliedHex1(String hex, Double tintValue) {
        if(hex == null) {
            return null;
        }
        String hexResult = "";
        for (int i = 0; i < 5; i += 2) {
            int hls = Integer.parseInt(hex.substring(i, i + 2), 16);
            int tintAppliedHls = applyTint1(hls, tintValue);
            hexResult += Integer.toHexString(tintAppliedHls);
        }
        return hexResult;
    }
    
    private static int applyTint1(int hlsValue, Double tintValue) {

        return (int) Math.round((tintValue * (double)hlsValue) + (1.0d - tintValue)*255.0d);
    }



        @Override
    public void beforeParse() throws XLSXException {
    }

    @Override
    public void afterParse() {
    }
}
