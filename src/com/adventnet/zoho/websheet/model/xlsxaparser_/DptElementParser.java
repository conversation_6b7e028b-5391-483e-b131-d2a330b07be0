//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.zschart.ChartProperties;
import com.adventnet.zoho.websheet.model.zschart.Series;
import java.util.List;
import java.util.logging.Level;
import org.xmlpull.v1.XmlPullParser;

/**
 *
 * <AUTHOR>
 */
class DptElementParser extends XMLElementParser implements XMLParser {
    private final Series series;
    private ChartProperties chartProperties = null;
    
    DptElementParser(XMLPullParserWrapper xpp, Series series, List<XLSXException> xlsxException) {
        super(xpp, xlsxException);
        this.series = series;
    }
    
    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.C_IDX:
                        String val = xpp.getAttribute(AttributeNameConstants.VAL);
                        int index = Integer.valueOf(val);
                        chartProperties = series.getDataPointFromParser(index).getChartStyleFromParser().getChartPropertiesFromParser();
                        break;
                    case ElementNameConstants.C_MARKER:
//                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        XMLParser markerElementParser = new MarkerElementParser(xpp, chartProperties, xlsxException);
                        XMLParserAgent xmlpa = new XMLParserAgent(xpp, markerElementParser);
                         {
                            try {
                                xmlpa.parseNodeTree(true);
                            } catch (XLSXException ex) {
                                this.xlsxException.add(ex);
                                ex.setLevel(Level.SEVERE);
                            }
                        }
                        break;
                    case ElementNameConstants.C_BUBBLE3D:
//                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        break;
                    case ElementNameConstants.C_EXPLOSION:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        chartProperties.setPieOffset(Integer.valueOf(val));
                        break;
                }
                break;
        }
    }

    @Override
    public void beforeParse() throws XLSXException {
    }

    @Override
    public void afterParse() {
    }
    
}
