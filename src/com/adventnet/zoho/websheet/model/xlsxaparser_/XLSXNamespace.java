//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

/**
 *
 * <AUTHOR>
 */
public class XLSXNamespace {
    public static final String C = "c:";//No I18N
    public static final String A = "a:";//No I18N
    public static final String XDR = "xdr:";//No I18N
    public static final String X14 = "x14:";//No I18N
    public static final String X15 = "x15:";//No I18N
    public static final String XM = "xm:";//No I18N
    public static final String X = "x:";//No I18N
    public static final String SSML = "ssml:";//No I18N
    public static final String CP = "cp:";//No I18N
    public static final String DCTERMS = "dcterms:";//No I18N
    public static final String XMLNS = "xmlns:";//No I18N
    public static final String XSI = "xsi:";//No I18N
    public static final String SLE = "sle:";//No I18N
    public static final String TSLE = "tsle:";//No I18N
    public static final String MC = "mc:";//No I18N
}
