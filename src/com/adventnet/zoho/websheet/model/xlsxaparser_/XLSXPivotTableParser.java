//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import com.adventnet.zoho.websheet.model.filter.operator.FilterOperator;
import com.adventnet.zoho.websheet.model.pivot.PivotFilterInfo;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.adventnet.zoho.websheet.model.xlsxaparser_.xlsxpivottablebeans.*;
import org.xmlpull.v1.XmlPullParser;

/**
 *
 * <AUTHOR> Zoho-Sheet don't support Customized Filter of
 * Pivot-Fields
 */
class XLSXPivotTableParser extends XMLFileParser implements XMLParser {
    private boolean isRowField;

    private XLSXPivotTableRepo repo;
    private final PivotCacheDefinition pivotCacheDefinition;
    private PivotField pivotField;
    private PageField pageField;
    private Filter filter;
    List<Integer> hiddenItems;

    XLSXPivotTableParser(XMLFile xLSXFile, XMLPullParserWrapper xpp, PivotCacheDefinition pivotCacheDefinition, List<XLSXException> xlsxException) {
        super(xLSXFile, xpp, xlsxException);
        this.pivotCacheDefinition = pivotCacheDefinition;
    }
    
    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                switch (nodeName) {
                    case ElementNameConstants.PIVOTFIELD:
                        parsePivotFieldEndNode();
                        break;
                    case ElementNameConstants.PAGEFIELD:
                        parsePageFieldEndNode();
                        break;
                    case ElementNameConstants.FILTER:
                        parseFilterEndNode();
                        break;
                }
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.PIVOTTABLEDEFINITION:
                        parsePivotTableDefinitionNode();
                        break;
                    case ElementNameConstants.LOCATION:
                        parseLocationNode();
                        break;
                    case ElementNameConstants.PIVOTFIELD:
                        parsePivotFieldNode();
                        break;
                    case ElementNameConstants.ITEMS:
                        parseItems();
                        break;
                    case ElementNameConstants.ITEM:
                        parseItem();
                        break;
                    case ElementNameConstants.ROWFIELDS:
                        parseRowFieldsNode();
                        break;
                    case ElementNameConstants.FIELD:
                        parseFieldNode();
                        break;
                    case ElementNameConstants.PAGEFIELD:
                        parsePageFieldNode();
                        break;
                    case ElementNameConstants.COLFIELDS:
                        parseColFieldsNode();
                        break;
                    case ElementNameConstants.DATAFIELDS:
                        parseDataFieldsNode();
                        break;
                    case ElementNameConstants.DATAFIELD:
                        parseDataFieldNode();
                        break;
                    case ElementNameConstants.FILTER:
                        parseFilterNode();
                        break;
                    case ElementNameConstants.CUSTOMFILTER:
                        parseCustomFilterNode();
                        break;
                    case ElementNameConstants.REPEATLABELS:
                        parseRepeatLabelNode();
                }
                break;
        }

    }

    private void parsePageFieldEndNode() {
        this.pageField = null;
    }

    @Override
    public void beforeParse() throws XLSXException {
        repo = (XLSXPivotTableRepo) this.xmlFile.getXlsxRepo();
    }

    @Override
    public void afterParse() {
    }

    private void parseLocationNode() {
        String ref = xpp.getAttribute(AttributeNameConstants.REF);//J4:O15
        repo.getPivotTableDefinition().setRef(ref);
    }
    private void parseRepeatLabelNode(){
        String repeatLabel = xpp.getAttribute(AttributeNameConstants.REPEATLABELS);
        this.repo.getPivotTableDefinition().setRepeatLabel(XLSXParserUtility.isEqualsFalse(repeatLabel));
    }
    private void parsePivotTableDefinitionNode() {
        String name = xpp.getAttribute(AttributeNameConstants.NAME);
        String colGrandTotal = xpp.getAttribute(AttributeNameConstants.COLGRANDTOTALS);//default : true
        String rowGrandTotal = xpp.getAttribute(AttributeNameConstants.ROWGRANDTOTALS);//default : true 

        this.repo.getPivotTableDefinition()
                .setName(name)
                .setColGrandTotal(XLSXParserUtility.isEqualsFalse(colGrandTotal))
                .setRowGrandTotal(XLSXParserUtility.isEqualsFalse(rowGrandTotal));
    }

    private void parsePivotFieldNode() {
        String sortType = xpp.getAttribute(AttributeNameConstants.SORTTYPE);
        String axis = xpp.getAttribute(AttributeNameConstants.AXIS);


        this.pivotField = new PivotField();
        this.repo.getPivotTableDefinition().getPivotFields().add(this.pivotField);
        this.pivotField.setSortType(sortType)
                .setAxis(axis);
        if (XLSXParserUtility.isEqualsFalse(xpp.getAttribute(AttributeNameConstants.DEFAULTSUBTOTAL))) {
            this.pivotField.disableSubtotal();
        }
        //we don't support subtotal function yet
//        else {
//            addPivotSubtotals(this.pivotField);
//        }
    }

    private void parsePivotFieldEndNode() {

        if(!this.hiddenItems.isEmpty())
        {
            Filter filter = new Filter();
            filter.setHiddenItems(hiddenItems);
            this.pivotField.setFilter(filter);
        }
        // Checking if the current pivotField is Slicer Field.
        if(pivotField.getAxis() == null)
        {
            this.repo.getPivotTableDefinition().getOtherFields().add(this.repo.getPivotTableDefinition().getPivotFields().size() - 1);
        }

        this.pivotField = null;
        this.hiddenItems = null;
    }

    private void parseRowFieldsNode() {
        this.isRowField = true;
    }

    private void parseColFieldsNode() {
        this.isRowField = false;
    }

    private void parseDataFieldsNode() {

    }

    private void parseFieldNode() {
        int x = Integer.parseInt(xpp.getAttribute(AttributeNameConstants.X));
        if(this.isRowField) {
            this.repo.getPivotTableDefinition()
                    .getRowFields()
                    .add(x);
        } else {
            this.repo.getPivotTableDefinition()
                    .getColFields()
                    .add(x);
        }
    }

    private void parseDataFieldNode() {
        /*
         row, column and page are maintained in PivotField but
         data PivotField is maintained in DataField
         ---data field won't be created while parsing 'pivotCacheDefinition([0-9]*).xml'---
         */

        int fld = Integer.parseInt(xpp.getAttribute(AttributeNameConstants.FLD));
        String subTotal = xpp.getAttribute(AttributeNameConstants.SUBTOTAL);
        String showDataAs = xpp.getAttribute(AttributeNameConstants.SHOWDATAAS);
        this.repo.getPivotTableDefinition()
                .getDataFields()
                .add(new DataField().setFld(fld).setSubtotal(subTotal).setShowDataAs(showDataAs));
    }

    private void parseItems() throws XLSXException {
        this.hiddenItems = new ArrayList<>();
//        xpp.escapeNode();
    }

    private void parseItem() {
        if (XLSXParserUtility.isEqualsTrue(xpp.getAttribute(AttributeNameConstants.H))) {
            String xString = xpp.getAttribute(AttributeNameConstants.X);
            if (xString != null) {
                int x = Integer.valueOf(xString);
                if(this.pivotField != null) {
                    this.hiddenItems.add(x);
                }
                /*if(this.pageField != null) {
                    this.pageField.getHiddenItems().add(x);
                }*/
            }
        }
    }

    private void addPivotSubtotals(PivotField pivotField) {
        for (String subTotalAttribute : XLSXMLDefaults.SUB_TOTAL_ATTRIBUTES) {
            String subTotal = xpp.getAttribute(subTotalAttribute);
            if (XLSXParserUtility.isEqualsTrue(subTotal)) {
                pivotField.getSubtotals().add(subTotalAttribute);
            }
        }
    }

    private void parsePageFieldNode() {
        int x = Integer.parseInt(xpp.getAttribute(AttributeNameConstants.FLD));
        int hier = Integer.parseInt(xpp.getAttribute(AttributeNameConstants.HIER));
        this.pageField = new PageField().setFld(x).setHier(hier);
        this.repo.getPivotTableDefinition()
                .getPageFields()
                .add(pageField);
    }

    private void parseFilterNode()
    {
        String filterCondition = xpp.getAttribute(AttributeNameConstants.TYPE);

        // parsing filter, only if it is custom date Filter now.
        if(filterCondition != null && filterCondition.startsWith("date"))
        {
            filterCondition = FilterOperator.getFilterOperatorFromXLSXValue(filterCondition);

            int fieldIndex = Integer.parseInt(xpp.getAttribute(AttributeNameConstants.FLD));
            this.filter = new Filter();
            this.filter.setCondition(filterCondition);
            this.filter.setFilterType(PivotFilterInfo.PivotFilterType.LABEL);
            this.repo.getPivotTableDefinition().getPivotFields().get(fieldIndex).setFilter(filter);
        }
    }

    private void parseCustomFilterNode()
    {
        if(this.filter != null) {
            String val = xpp.getAttribute(AttributeNameConstants.VAL);
            String operator = xpp.getAttribute(AttributeNameConstants.OPERATOR);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MMM-dd HH:mm:ss");
            val = formatter.format(DateUtil.convertNumberToDate(Double.parseDouble(val)));
            if(this.filter.getCondition().equals(FilterOperator.BETWEEN.toString()))
            {
                if(operator.equals("greaterThanOrEqual")) {
                    this.filter.setValue1(val);
                }else if(operator.equals("lessThanOrEqual")){
                    this.filter.setValue2(val);
                }
            }
            else {
                this.filter.setValue1(val);
            }
        }
    }

    private void parseFilterEndNode() {
        this.filter = null;
    }
}
