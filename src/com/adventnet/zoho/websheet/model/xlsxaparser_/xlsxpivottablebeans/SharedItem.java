/* $Id$ */
package com.adventnet.zoho.websheet.model.xlsxaparser_.xlsxpivottablebeans;

public class SharedItem {

    /*
       <xsd:element name="m" type="CT_Missing"/>
       <xsd:element name="n" type="CT_Number"/>
       <xsd:element name="b" type="CT_Boolean"/>
       <xsd:element name="e" type="CT_Error"/>
       <xsd:element name="s" type="CT_String"/>
       <xsd:element name="d" type="CT_DateTime"/>
     */
    public enum SharedItemType{
        M,N,B,E,S,D
    }

    private String val;
    private SharedItemType type;

    public String getVal() {
        return val;
    }

    public SharedItem setVal(String val) {
        this.val = val;
        return this;
    }

    public SharedItemType getType() {
        return type;
    }

    public SharedItem setType(SharedItemType type) {
        this.type = type;
        return this;
    }
}
