/* $Id$ */
package com.adventnet.zoho.websheet.model.xlsxaparser_.xlsxpivottablebeans;

public class CacheField {
    /*
    <xsd:complexType name="CT_CacheField">
    <xsd:sequence>
       <xsd:element name="sharedItems" type="CT_SharedItems" minOccurs="0" maxOccurs="1"/>
       <xsd:element name="fieldGroup" minOccurs="0" type="CT_FieldGroup"/>
       <xsd:element name="mpMap" minOccurs="0" maxOccurs="unbounded" type="CT_X"/>
       <xsd:element name="extLst" minOccurs="0" type="CT_ExtensionList"/>
    </xsd:sequence>
    <xsd:attribute name="name" type="s:ST_Xstring" use="required"/>
    <xsd:attribute name="caption" type="s:ST_Xstring" use="optional"/>
    <xsd:attribute name="propertyName" type="s:ST_Xstring" use="optional"/>
    <xsd:attribute name="serverField" type="xsd:boolean" use="optional" default="false"/>
    <xsd:attribute name="uniqueList" type="xsd:boolean" use="optional" default="true"/>
    <xsd:attribute name="numFmtId" type="ST_NumFmtId" use="optional"/>
    <xsd:attribute name="formula" type="s:ST_Xstring" use="optional"/>
    <xsd:attribute name="sqlType" type="xsd:int" use="optional" default="0"/>
    <xsd:attribute name="hierarchy" type="xsd:int" use="optional" default="0"/>
    <xsd:attribute name="level" type="xsd:unsignedInt" use="optional" default="0"/>
    <xsd:attribute name="databaseField" type="xsd:boolean" default="true"/>
    <xsd:attribute name="mappingCount" type="xsd:unsignedInt" use="optional"/>
    <xsd:attribute name="memberPropertyField" type="xsd:boolean" use="optional" default="false"/>
    </xsd:complexType>
     */
    private SharedItems sharedItems = new SharedItems();
    private String name;
    private FieldGroup fieldGroup;
    private boolean databaseField = true; //A value of 0 or false indicates the field was created by the application.
    private String numFumtId;

    public boolean isDatabaseField() {
        return databaseField;
    }

    public void setNotADatabaseField() {
        this.databaseField = false;
    }

    public SharedItems getSharedItems() {
        return sharedItems;
    }

    public String getName() {
        return name;
    }

    public CacheField setName(String name) {
        this.name = name;
        return this;
    }

    public FieldGroup getFieldGroup() {
        return fieldGroup;
    }

    public void setFieldGroup(FieldGroup fieldGroup) {
        this.fieldGroup = fieldGroup;
    }

    public String getNumFmtId() {
        return this.numFumtId;
    }

    public void setNumFumtId(String numFumtId) {
        this.numFumtId = numFumtId;
    }
}
