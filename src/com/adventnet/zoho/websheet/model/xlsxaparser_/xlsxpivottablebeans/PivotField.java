/* $Id$ */
package com.adventnet.zoho.websheet.model.xlsxaparser_.xlsxpivottablebeans;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class PivotField {
    private String sortType;
    private String axis;
    private String subtotalTop;//todo parse
    private boolean defaultSubtotal = true;
    private Filter filter;

    public boolean isSubtotalEnabled() {
        return defaultSubtotal;
    }

    public PivotField disableSubtotal() {
        this.defaultSubtotal = false;
        return this;
    }

    private List<String> subtotals = new ArrayList<>();

    public String getSortType() {
        return sortType;
    }

    public PivotField setSortType(String sortType) {
        this.sortType = sortType;
        return this;
    }

    public String getAxis() {
        return axis;
    }

    public PivotField setAxis(String axis) {
        this.axis = axis;
        return this;
    }

    public String getSubtotalTop() {
        return subtotalTop;
    }

    public PivotField setSubtotalTop(String subtotalTop) {
        this.subtotalTop = subtotalTop;
        return this;
    }

    public List<String> getSubtotals() {
        return subtotals;
    }

    public Filter getFilter() {
        return this.filter;
    }

    public void setFilter(Filter filter) {
        this.filter = filter;
    }
}
