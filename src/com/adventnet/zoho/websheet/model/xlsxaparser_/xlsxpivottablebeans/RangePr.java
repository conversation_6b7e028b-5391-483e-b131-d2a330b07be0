/* $Id$ */
package com.adventnet.zoho.websheet.model.xlsxaparser_.xlsxpivottablebeans;


public class RangePr {
    /*
    <xsd:complexType name="CT_RangePr">
        <xsd:attribute name="autoStart" type="xsd:boolean" default="true"/>
        <xsd:attribute name="autoEnd" type="xsd:boolean" default="true"/>
        <xsd:attribute name="groupBy" type="ST_GroupBy" default="range"/>
        <xsd:attribute name="startNum" type="xsd:double"/>
        <xsd:attribute name="endNum" type="xsd:double"/>
        <xsd:attribute name="startDate" type="xsd:dateTime"/>
        <xsd:attribute name="endDate" type="xsd:dateTime"/>
        <xsd:attribute name="groupInterval" type="xsd:double" default="1"/>
    </xsd:complexType>
    <xsd:simpleType name="ST_GroupBy">
        <xsd:restriction base="xsd:string">
           <xsd:enumeration value="range"/>
           <xsd:enumeration value="seconds"/>
           <xsd:enumeration value="minutes"/>
           <xsd:enumeration value="hours"/>
           <xsd:enumeration value="days"/>
           <xsd:enumeration value="months"/>
           <xsd:enumeration value="quarters"/>
           <xsd:enumeration value="years"/>
        </xsd:restriction>
    </xsd:simpleType>
     */
    public enum GroupBy {
        RANGE,SECONDS,MINUTES,HOURS,DAYS,MONTHS,QUARTERS,YEARS
    }
    private GroupBy groupBy;
    private String endDate;
    private String startDate;

    private Double startNum;
    private Double endNum;
    private Double groupInterval;

    private boolean autoStart;
    private boolean autoEnd;

    public RangePr setStartNum(Double startNum) {
        this.startNum = startNum;
        return this;
    }

    public RangePr setEndNum(Double endNum) {
        this.endNum = endNum;
        return this;
    }

    public RangePr setGroupInterval(Double groupInterval) {
        this.groupInterval = groupInterval;
        return this;
    }

    public RangePr setAutoStart(boolean autoStart) {
        this.autoStart = autoStart;
        return this;
    }

    public RangePr setAutoEnd(boolean autoEnd) {
        this.autoEnd = autoEnd;
        return this;
    }

    public Double getStartNum() {
        return startNum;
    }

    public Double getEndNum() {
        return endNum;
    }

    public Double getGroupInterval() {
        return groupInterval;
    }

    public boolean isAutoStart() {
        return autoStart;
    }

    public boolean isAutoEnd() {
        return autoEnd;
    }

    public GroupBy getGroupBy() {
        return groupBy;
    }

    public void setGroupBy(GroupBy groupBy) {
        this.groupBy = groupBy;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
}
