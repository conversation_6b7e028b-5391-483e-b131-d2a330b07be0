/* $Id$ */
package com.adventnet.zoho.websheet.model.xlsxaparser_.xlsxpivottablebeans;

import java.util.ArrayList;
import java.util.List;

public class PivotTableDefinition {
    //pivotTableDefinition
    private String name;
    private boolean colGrandTotalDisable = false;
    private boolean rowGrandTotalDisable = false;
    private boolean repeatLabelDisabled = true;

    //location
    private String ref;

    //pivotFields
    private List<PivotField> pivotFields = new ArrayList<>();

    private List<Integer> rowFields = new ArrayList<>();
    private List<Integer> colFields = new ArrayList<>();
    private List<PageField> pageFields = new ArrayList<>();
    private List<DataField> dataFields = new ArrayList<>();
    private List<Integer> otherFields = new ArrayList<>();// Fields with No Orientation

    public PivotTableDefinition setName(String name) {
        this.name = name;
        return this;
    }

    public PivotTableDefinition setColGrandTotal(boolean colGrandTotal) {
        this.colGrandTotalDisable = colGrandTotal;
        return this;
    }

    public PivotTableDefinition setRowGrandTotal(boolean rowGrandTotal) {
        this.rowGrandTotalDisable = rowGrandTotal;
        return this;
    }
    public PivotTableDefinition setRepeatLabel(boolean repeatLabel) {
        this.repeatLabelDisabled = repeatLabel;
        return this;
    }
    public PivotTableDefinition setRef(String ref) {
        this.ref = ref;
        return this;
    }

    public String getName() {
        return name;
    }

    public boolean isColGrandTotalDisable() {
        return colGrandTotalDisable;
    }

    public boolean isRowGrandTotalDisabled() {
        return rowGrandTotalDisable;
    }
    public boolean isRepeatLabelDisabled() {
        return repeatLabelDisabled;
    }

    public String getRef() {
        return ref;
    }

    public List<PivotField> getPivotFields() {
        return pivotFields;
    }

    public List<Integer> getRowFields() {
        return rowFields;
    }

    public List<Integer> getColFields() {
        return colFields;
    }

    public List<PageField> getPageFields() {
        return pageFields;
    }

    public List<DataField> getDataFields() {
        return dataFields;
    }

    public List<Integer> getOtherFields() {
        return otherFields;
    }
}
