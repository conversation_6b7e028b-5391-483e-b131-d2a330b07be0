package com.adventnet.zoho.websheet.model.xlsxaparser_.xlsxpivottablebeans;

import com.adventnet.zoho.websheet.model.pivot.PivotFilterInfo;

import java.util.List;

public class Filter {
    // custom filter
    private String condition;
    private String value1;
    private  String value2;
    private PivotFilterInfo.PivotFilterType filterType;

    // normal filter
    private List<Integer> hiddenItems;

    public List<Integer> getHiddenItems() {
        return this.hiddenItems;
    }

    public String getCondition() {
        return this.condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getValue1() {
        return this.value1;
    }

    public void setValue1(String value1) {
        this.value1 = value1;
    }

    public String getValue2() {
        return this.value2;
    }

    public void setValue2(String value2) {
        this.value2 = value2;
    }

    public PivotFilterInfo.PivotFilterType getFilterType() {
        return this.filterType;
    }

    public void setFilterType(PivotFilterInfo.PivotFilterType filterType) {
        this.filterType = filterType;
    }

    public void setHiddenItems(List<Integer> hiddenItems) {
        this.hiddenItems = hiddenItems;
    }
}
