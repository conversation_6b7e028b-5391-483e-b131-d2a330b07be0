//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;


import com.adventnet.zoho.websheet.model.SpreadsheetSettings;
import com.adventnet.zoho.websheet.model.style.datastyle.*;
import org.xmlpull.v1.XmlPullParser;

/**
 * TODO : Currency/Accounting formats should be parsed as Currency/Accounting format, instead of parsing them as CustomFormat.
 * <AUTHOR>
 */
class NumFmtElementParser extends XMLElementParser implements XMLParser {
    private String numFmtId = null;
    private ZSPattern pattern = null;
    private final Locale locale;
    private final SpreadsheetSettings spreadsheetSettings;

    NumFmtElementParser(XMLPullParserWrapper xmlPullParser, Locale locale, List<XLSXException> xlsxException, SpreadsheetSettings spreadsheetSettings) {
        super(xmlPullParser, xlsxException);
        this.locale = locale;
        this.spreadsheetSettings = spreadsheetSettings;
    }

    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.NUMFMT:
                        parseNumFmtNode();
                        break;
                }
                break;
        }
    }

    private void parseNumFmtNode() {
        numFmtId = xpp.getAttribute(AttributeNameConstants.NUMFMTID);
        String formatCode = xpp.getAttribute(AttributeNameConstants.FOMRATCODE);
        pattern = getPatternFromNumFmt(formatCode, locale, true, spreadsheetSettings);
    }

    Object[] getNumFmt() {
        return new Object[]{numFmtId, pattern};
    }

    static ZSPattern getPatternFromNumFmt(String numFmt, Locale formatLocale, boolean isCustomPattern, SpreadsheetSettings spreadsheetSettings) {
        if (numFmt != null ? numFmt.equalsIgnoreCase(XLSXConstants.NUMFMT_GENERAL) : true) {
            return DataStyleConstants.EMPTY_PATTERN;
        }
        ZSPattern pattern = null;
        try {
            if(XLSXConstants.NUMFMT_GENERAL.equals(numFmt.trim())) {
                return DataStyleConstants.EMPTY_PATTERN;
            }
            if(numFmt.equals("mm-dd-yy")) {
                /*
                Excel saves default date format as "mm-dd-yy" (num format id 14) in XML.
                We need to parse this as default format, instead of custom format.
                If it's parsed as custom format, it won't change according to locale.
                 */
                return DefaultPatternUtil.getDefaultDatePattern(SpreadsheetSettings.defaultSpreadsheetSettings.getLocale(), "MM/dd/yyyy");//No I18N
            }
            numFmt = replaceXlsxLocaleCodeWithLocale(numFmt);
//            numFmt = replaceUnderscoreFmt_WithWhiteSpace(numFmt);
            numFmt = numFmt.replace("\\ ", " ");
            pattern = new ZSPattern(numFmt, SpreadsheetSettings.getInstance(formatLocale), isCustomPattern, spreadsheetSettings);
        } catch (Exception e) {
            XLSXException xlsxe = new XLSXException(e);
            xlsxe.setIsFeatureLost(true);
            xlsxe.setFeature(XLSXException.FEATURE.PATTERN);
            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.XLSX_NUMFMT, numFmt));
            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.PATTERN, numFmt));
            xlsxe.setLevel(Level.WARNING);
            xlsxe.log(Logger.getLogger(NumFmtElementParser.class.getName()));
        }
        return pattern;
    }

    private static String replaceUnderscoreFmt_WithWhiteSpace(String format) {
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("([\"'])(?:(?=(\\\\?))\\2.)*?\\1");
        Matcher matcher = pattern.matcher(format);

        List<Integer[]> indexOfQuotation = new ArrayList<>();
        while (matcher.find()) {
            indexOfQuotation.add(new Integer[]{matcher.start(), matcher.end()});
        }

        StringBuilder stringBuilder = new StringBuilder();

        int startOf_OutSide_Quotation = 0;

        for (Integer[] integers : indexOfQuotation) {
            int startOf_Inside_Quotation = integers[0];
            int endOf_Inside_Quotation = integers[1];


            String outSide_Quotation = format.substring(startOf_OutSide_Quotation, startOf_Inside_Quotation);
            String inside_Quotation = format.substring(startOf_Inside_Quotation, endOf_Inside_Quotation);

            stringBuilder.append(outSide_Quotation.replaceAll("_.", " "));
            stringBuilder.append(inside_Quotation);

            startOf_OutSide_Quotation = endOf_Inside_Quotation;
        }

        String outSide_Quotation = format.substring(startOf_OutSide_Quotation);
        stringBuilder.append(outSide_Quotation.replaceAll("_.", " "));

        return stringBuilder.toString();
    }

    private static Locale getCurrencyLocale(String formatCode) {
        Locale locale = null;
        String xlsxLocaleCode = getXlsxLocaleCode(formatCode);
        if (xlsxLocaleCode != null) {
            try {
                locale = XLSXMLDefaults.getLocale(xlsxLocaleCode);
            } catch (XLSXException ex) {
//                Logger.getLogger(NumFmtElementParser.class.getName()).log(Level.WARNING, ex.getMessage());
                ex.setIsFeatureLost(true);
                ex.log(Logger.getLogger(NumFmtElementParser.class.getName()));
                return null;
            }
        }
        return locale;
    }

    private static String getXlsxLocaleCode(String formatCode) {
        int leftBracketIndex = formatCode.indexOf("[");
        int rightBracketIndex = formatCode.indexOf("]");

        String xlsxLocaleCode = null;

        if (leftBracketIndex != -1 && rightBracketIndex != -1) {
            String langCode = formatCode.substring(leftBracketIndex + 1, rightBracketIndex);

            int dollarIndex = langCode.indexOf("$");
            int dashIndex = langCode.indexOf("-");

            if (dollarIndex != -1) {//[RED]
                if (dashIndex != -1) {
                    if ((dashIndex - dollarIndex) != 1) {//[$-45c] is used in time fromat 
                        xlsxLocaleCode = langCode.substring(dashIndex + 1);
                    }
                } else {//[$GEM] this is a currency format
                    xlsxLocaleCode = "";
                }
            }

        }
        return xlsxLocaleCode;
    }

    private static String replaceXlsxLocaleCodeWithLocale(String formatCode)
    {
        Locale locale = getCurrencyLocale(formatCode);
        if (locale != null && locale.getLanguage() != null && locale.getCountry() != null)
        {
            String zsLocaleCode = "[\\$" + locale.getLanguage() + "-" + locale.getCountry() + "]";
            formatCode = formatCode.replaceAll("\\[\\$[\\p{Sc}\\-A-Z0-9]+\\]", zsLocaleCode);
        } else {
            int leftBracketIndex = formatCode.indexOf("[");
            int currentIndex = -1;

            while (currentIndex < leftBracketIndex) {
                currentIndex = leftBracketIndex;
                formatCode = replaceFirstXlsxLocaleCodeWithCurrencySymbol(formatCode, currentIndex);
                leftBracketIndex = formatCode.indexOf("[", currentIndex + 1);
            }
        }

        return formatCode;
    }

    private static String replaceFirstXlsxLocaleCodeWithCurrencySymbol(String formatCode, int fromIndex) {
        int leftBracketIndex = formatCode.indexOf("[",fromIndex);
        int rightBracketIndex = formatCode.indexOf("]",fromIndex);

        if (leftBracketIndex != -1 && rightBracketIndex != -1) {
            String langCode = formatCode.substring(leftBracketIndex + 1, rightBracketIndex);

            int dollarIndex = langCode.indexOf("$");
            int dashIndex = langCode.indexOf("-");
            String currencySymbol = null;

            if (dollarIndex != -1) {
                if (dashIndex != -1) {
                    currencySymbol = langCode.substring(dollarIndex + 1, dashIndex);
                } else {
                    currencySymbol = langCode.substring(dollarIndex + 1);
                }
            }
            if (currencySymbol != null) {
                formatCode = formatCode.substring(0, leftBracketIndex) + "\""+ currencySymbol + "\""+ formatCode.substring(rightBracketIndex + 1);
            }
        }
        return formatCode;
    }

    @Override
    public void beforeParse() throws XLSXException {
    }

    @Override
    public void afterParse() {
    }

    public static void main(String[] args) {
//        AppResources.setProperty("server.home", "/Users/<USER>/ZohoSheet/Default/builds/latest/AdventNet/Sas/tomcat/webapps/ROOT/WEB-INF");
//        String numFmt = "[\"USD\"] #,##0;[\"USD\"] -#,##0";
//        numFmt = "\"BDT\"\\ * #,##0\\ ;\\ \"BDT\"\\ * \\(#,##0\\);\\ \"BDT\"\\ * \"-\"\\ ;";
////        numFmt = "_([$$-409]* #,##0.00_);_([$$-409]* \\(#,##0.00\\);_([$$-409]* &quot;-&quot;??_);_(@_)";
//        numFmt = "_([$USD]\\ * #,##0.00_);_([$USD]\\ * \\(#,##0.00\\);_([$USD]\\ * &quot;-&quot;??_);_(@_)";
//        String alteredNumFmt = null;
//        Locale formatLocale = Locale.getDefault();
//        try {
//            alteredNumFmt = numFmt.replaceAll("\\\\","");
//            if(XLSXConstants.NUMFMT_GENERAL.equals(alteredNumFmt.trim())) {
//                System.out.println("null");
//            }
//            alteredNumFmt = replaceXlsxLocaleCodeWithCurrencySymbol(alteredNumFmt);
//            alteredNumFmt = replaceUnderscoreFmt_WithWhiteSpace(alteredNumFmt);
//
//            System.out.println("transormed Num Fmt: "+alteredNumFmt);
//
//            ZSPattern pattern = ZSPattern.getCustomPattern(alteredNumFmt, formatLocale, false);
//            System.out.println(pattern);
//
//        } catch (Exception e) {
//            XLSXException xlsxe = new XLSXException(e);
//            xlsxe.setIsFeatureLost(true);
//            xlsxe.setFeature(XLSXException.FEATURE.PATTERN);
//            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.XLSX_NUMFMT, numFmt));
//            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.PATTERN, alteredNumFmt));
//            xlsxe.log(Logger.getLogger(NumFmtElementParser.class.getName()), Level.SEVERE);
//        }
    }
}
