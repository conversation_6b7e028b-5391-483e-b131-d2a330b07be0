//$Id$
package com.adventnet.zoho.websheet.model.xlsxaparser_;


import java.util.logging.Level;
import java.util.logging.Logger;

public class XlsxLimitException extends RuntimeException {
    private final int zfsng_constant;
    private static final Logger LOGGER = Logger.getLogger(XlsxLimitException.class.getName());
    public XlsxLimitException(String message, int zfsng_constant){
        super(message);
        this.zfsng_constant = zfsng_constant;
    }

    public int getZFSNGConstants(){
        return zfsng_constant;
    }
}
