//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.zschart.ChartProperties;
import com.adventnet.zoho.websheet.model.zschart.RegressionCurve;
import java.util.List;
import org.xmlpull.v1.XmlPullParser;

/**
 *
 * <AUTHOR>
 */
class TrendLineElementParser extends XMLElementParser implements XMLParser {
    private final RegressionCurve regressionCurve;
    private final ChartProperties rgCvChartProperties;

    TrendLineElementParser(XMLPullParserWrapper xpp, RegressionCurve regressionCurve, List<XLSXException> xlsxException) {
        super(xpp, xlsxException);
        rgCvChartProperties = regressionCurve.getChartStyleFromParser().getChartPropertiesFromParser();
        this.regressionCurve = regressionCurve;
    }
    
    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.C_NAME:
                        parseNameNode();
                        break;
                    case ElementNameConstants.C_TRENDLINETYPE:
                        parseTrendLineTypeNode();
                        break;
                    case ElementNameConstants.C_ORDER:
                        parseOrderNode();
                        break;
                    case ElementNameConstants.C_PERIOD:
                        parsePeriodNode();
                        break;
                    case ElementNameConstants.C_FORWARD:
                        parseForwardNode();
                        break;
                    case ElementNameConstants.C_BACKWARD:
                        parseBackWardNode();
                        break;
                    case ElementNameConstants.C_INTERCEPT:
                        parseInterceptNode();
                        break;
                    case ElementNameConstants.C_DISPRSQR:
                        parseDisprsqrNode();
                        break;
                    case ElementNameConstants.C_DISPEQ:
                        parseDispeqNode();
                        break;
                }
                break;
        }

    }
    
    private void parseNameNode() {
//        String text = xpp.getTextInsideElement();
//        rgCvChartProperties.setRegressionName(text);
    }

    private void parseTrendLineTypeNode() {
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        {
            ChartProperties.REGRESSION_TYPE type = null;
            try {
                type = ChartOdsEquivalent.eqlTrendLineType(val);
            } catch (XLSXException ex) {
//                logger.log(Level.WARNING, null, ex);
                this.xlsxException.add(ex);
            }
            rgCvChartProperties.setRegressionType(type);
        }
    }

    private void parseOrderNode() {
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        rgCvChartProperties.setRegressionMaxDegree(Byte.valueOf(val));
    }

    private void parsePeriodNode() {
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        rgCvChartProperties.setRegressionPeriod(Byte.valueOf(val));
    }

    private void parseForwardNode() {
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        rgCvChartProperties.setRegressionExtrapolateForward(Float.valueOf(val));
    }

    private void parseBackWardNode() {
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        rgCvChartProperties.setRegressionExtrapolateBackward(Float.valueOf(val));
    }

    private void parseInterceptNode() {
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        rgCvChartProperties.setRegressionForceIntercept(true);
        rgCvChartProperties.setRegressionInterceptValue(Float.valueOf(val));
    }

    private void parseDisprsqrNode() {
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        if (XLSXParserUtility.isEqualsTrue(val)) {
            regressionCurve.getEquationFromParser().setDisplayRSquare(true);
        }
    }

    private void parseDispeqNode() {
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        if (XLSXParserUtility.isEqualsTrue(val)) {
            regressionCurve.getEquationFromParser().setDisplayRSquare(true);
        }
    }

    @Override
    public void beforeParse() {
    }
    
    @Override
    public void afterParse() {
        
    }
}
