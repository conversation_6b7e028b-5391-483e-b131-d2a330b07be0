//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

/**
 *
 * <AUTHOR>
 */
public class XLSXDrawingProperties {
    
    private long x;
    private long y;
    private long height;
    private long width;
    
    private long column;
    private long row;
    private long columnDiff;
    private long rowDiff;

    /**
     * @return the x
     */
    long getX() {
        return x;
    }

    /**
     * @param x the x to set
     */
    void setX(long x) {
        this.x = x;
    }

    /**
     * @return the y
     */
    long getY() {
        return y;
    }

    /**
     * @param y the y to set
     */
    void setY(long y) {
        this.y = y;
    }

    /**
     * @return the height
     */
    long getHeight() {
        return height;
    }

    /**
     * @param height the height to set
     */
    void setHeight(long height) {
        this.height = height;
    }

    /**
     * @return the width
     */
    long getWidth() {
        return width;
    }

    /**
     * @param width the width to set
     */
    void setWidth(long width) {
        this.width = width;
    }

    /**
     * @return the column
     */
    long getColumn() {
        return column;
    }

    /**
     * @param column the column to set
     */
    void setColumn(long column) {
        this.column = column;
    }

    /**
     * @return the row
     */
    long getRow() {
        return row;
    }

    /**
     * @param row the row to set
     */
    void setRow(long row) {
        this.row = row;
    }

    /**
     * @return the columnDiff
     */
    long getColumnDiff() {
        return columnDiff;
    }

    /**
     * @param columnDiff the columnDiff to set
     */
    void setColumnDiff(long columnDiff) {
        this.columnDiff = columnDiff;
    }

    /**
     * @return the rowDiff
     */
    long getRowDiff() {
        return rowDiff;
    }

    /**
     * @param rowDiff the rowDiff to set
     */
    void setRowDiff(long rowDiff) {
        this.rowDiff = rowDiff;
    }

    
    
    
}
