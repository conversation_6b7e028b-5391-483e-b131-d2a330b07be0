//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.zschart.Axis;
import com.adventnet.zoho.websheet.model.zschart.ChartProperties;
import com.adventnet.zoho.websheet.model.zschart.Grid;
import com.adventnet.zoho.websheet.model.zschart.Title;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import org.xmlpull.v1.XmlPullParser;

/**
 *
 * <AUTHOR>
 */
class AxisElementParser extends XMLElementParser implements XMLParser {
    
    private final Map<String, Axis> axIdAxisMap;
    private Axis axis = null;
    private ChartProperties chartProperties = null;
    private boolean isDelete = false;
    private String axId = null;

    AxisElementParser(XMLPullParserWrapper xpp, Map<String, Axis> axIdAxisMap,List<XLSXException> xlsxException) {
        super(xpp, xlsxException);
        this.axIdAxisMap = axIdAxisMap;
    }

    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.C_AXID:
                        axId = xpp.getAttribute(AttributeNameConstants.VAL);
                        axis = axIdAxisMap.get(getAxId());
                        chartProperties = getAxis().getChartStyleFromParser().getChartPropertiesFromParser();
                        break;

                    case ElementNameConstants.C_DELETE:
                        String val = xpp.getAttribute(AttributeNameConstants.VAL);
                        if (XLSXParserUtility.isEqualsTrue(val)) {
                            isDelete = true;
                        }
                        break;

                    case ElementNameConstants.C_LOGBASE:
                        if (getAxis() != null && chartProperties != null) {
                            val = xpp.getAttribute(AttributeNameConstants.VAL);
                            chartProperties.setLogarithmic(true);
                        }
                        break;
                    case ElementNameConstants.C_ORIENTATION:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        if ("maxMin".equals(val)) {
                            chartProperties.setReverseDirectioin(true);
                        }
                        break;

                    case ElementNameConstants.C_MAX:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        chartProperties.setMaximum(Float.valueOf(val));
                        break;
                    case ElementNameConstants.C_MIN:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        chartProperties.setMinimum(Float.valueOf(val));
                        break;
                    case ElementNameConstants.C_AXPOS:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);

                        break;
                    case ElementNameConstants.C_MAJORGRIDLINES:
                        Grid grid = new Grid();
                        grid.setClass_(Grid.CLASS.MAJOR);
                        getAxis().getGridsFromParser().add(grid);
                        break;
                    case ElementNameConstants.C_MINORGRIDLINES:
                        grid = new Grid();
                        grid.setClass_(Grid.CLASS.MINOR);
                        getAxis().getGridsFromParser().add(grid);
                        break;
                    case ElementNameConstants.C_TITLE:
                        String titleString = getTitleFromTitleNode();
                        if (titleString == null) {
                            titleString = XLSXConstants.DEFAULT_CHART_AXIS_TITLE;
                        }
                        Title title = new Title();
                        title.setTitle(titleString);
                        getAxis().setTitle(title);
                        break;
                    case ElementNameConstants.C_MAJORTICKMARK:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        if ("in".equals(val)) {
                            chartProperties.setTickMarksMajorInner(true);
                        }
                        if ("out".equals(val)) {
                            chartProperties.setTickMarksMajorOuter(true);
                        }
                        break;
                    case ElementNameConstants.C_MINORTICKMARK:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        if ("in".equals(val)) {
                            chartProperties.setTickMarksMinorInner(true);
                        }
                        if ("out".equals(val)) {
                            chartProperties.setTickMarksMinorOuter(true);
                        }
                        break;
                    case ElementNameConstants.C_TICKLBLPOS:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        ChartProperties.AXIS_LABEL_POSITION axisLabelPosition = null;
                        try {
                            axisLabelPosition = ChartOdsEquivalent.eqlAxisLabelPos(val);
                        } catch (XLSXException ex) {

//                            LostFeature lostFeature = new LostFeature();
//                            lostFeature.setFeature(LostFeature.FEATURE.CHART_LABEL_POSITION);
//                            lostFeature.setCauseType(LostFeature.CAUSETYPE.NOTSUPPORTED);
//                            lostFeature.addIdentity(new LostFeature.Identity(LostFeature.Identity.TYPE.CHART_LABEL_POSITION, val));
//                            XLSXParserUtility.addLostFeatures(lostFeature);
//                            logger.log(Level.WARNING, null, ex)
                            this.xlsxException.add(ex);
                        }
                        chartProperties.setAxisLabelPosition(axisLabelPosition);
                        break;
                    case ElementNameConstants.C_CROSSES:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        ChartProperties.AXIS_POSITION axisPos = null;
                        try {
                            axisPos = ChartOdsEquivalent.eqlAxisPos(val);
                        } catch (XLSXException ex) {

//                            LostFeature lostFeature = new LostFeature();
//                            lostFeature.setFeature(LostFeature.FEATURE.CHART_AXIS_POSITION);
//                            lostFeature.setCauseType(LostFeature.CAUSETYPE.NOTSUPPORTED);
//                            lostFeature.addIdentity(new LostFeature.Identity(LostFeature.Identity.TYPE.CHART_AXIS_POSITION, val));
//                            XLSXParserUtility.addLostFeatures(lostFeature);
//                            Logger.getLogger(XLSXChartParser.class.getName()).log(Level.SEVERE, null, ex);
                            this.xlsxException.add(ex);
                            ex.setLevel(Level.SEVERE);
                        }
                        chartProperties.setAxisPosition(axisPos);
                        break;
                    case ElementNameConstants.C_CROSSESAT:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        chartProperties.setAxisPosition_(Float.valueOf(val));
                        break;
                    case ElementNameConstants.C_MAJORUNIT:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        chartProperties.setIntervalMajor(Float.valueOf(val));
                        break;
                    case ElementNameConstants.C_MINORUNIT:
                        val = xpp.getAttribute(AttributeNameConstants.VAL);
                        float minorUnit = Float.valueOf(val);
                        float majorUnit = chartProperties.getIntervalMajor();
                        if (majorUnit == 0.0f) {
                            int intervalMinorDivisor = Math.round(majorUnit / minorUnit);
                            chartProperties.setIntervalMinorDivisor(intervalMinorDivisor);
                        }
                        break;
                }
                break;
        }
    }

    private String getTitleFromTitleNode() {
        String titleString = null;
        try {
            titleString = xpp.getTextInsideElement(ElementNameConstants.A_T);
        } catch (XLSXException e) {
            this.xlsxException.add(e);
        }
        return titleString;
    }

    /**
     * @return the axis
     */
    public Axis getAxis() {
        return axis;
    }

    /**
     * @return the isDelete
     */
    public boolean isIsDelete() {
        return isDelete;
    }

    /**
     * @return the axId
     */
    public String getAxId() {
        return axId;
    }

    @Override
    public void beforeParse() throws XLSXException {
    }

    @Override
    public void afterParse() {
    }

}
