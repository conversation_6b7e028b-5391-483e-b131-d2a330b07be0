//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

/**
 *
 * <AUTHOR>
 */
class Relationship {

    private String id;
    private XMLFile target;
    private String targetString;
    private boolean isTargetModeExternal = false;
    
    Relationship(String id, XMLFile target,String targetString, boolean isTargetModeExternal) {
        this.targetString = targetString;
        this.id = id;
        this.target = target;
        this.isTargetModeExternal = isTargetModeExternal;
    }

    String getId() {
        return id;
    }

    XMLFile getTarget() throws XLSXException {
        if(target == null) {
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.TARGET_PATH, targetString));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.EXTERNAL_SOURCE);
                throw xlsxe;
        }
        return target;
    }

    String getType() {
        if(target != null) {
            return target.getType();
        }
        return null;
    }

    String getTargetString() {
        return targetString;
    }
    

    boolean isIsTargetModeExternal() {
        return isTargetModeExternal;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("id :");//No I18N
        sb.append(id);
        sb.append(";target-filename :");//No I18N
        sb.append(target);
        sb.append(";targetstring :").append(targetString);//No I18N
        return sb.toString();
    }

}
