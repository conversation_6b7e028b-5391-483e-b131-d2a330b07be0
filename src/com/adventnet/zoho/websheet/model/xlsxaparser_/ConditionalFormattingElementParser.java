//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.FontFace;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.IconSetConstants;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.logging.Level;
import org.xmlpull.v1.XmlPullParser;

/**
 *
 * <AUTHOR>
 */
class ConditionalFormattingElementParser extends XMLElementParser implements XMLParser {

    private final List<String> indexedColors;
    private String formula_one;
    private String formula_two;
    private String cfType;
    private String cfOperator;
    private String cfTimePeriod;
    private String cfText;
    private String priority;

    private List<String> clrScheme = null;
    private CellStyle cellStyle = null;
//    private List<String> conditions = null;
//    private List<Integer> priortyList = null;
    private String styleName = null;
//    private List<String> styleNameList = null;
    private int x14DxfCount;

    private List<Object[]> conditionalFormatEntrys;
    private Object[] conditionalFormatEntry = null;//[0:type,1:val,2:color,3:gte]
    private List<String[]> iconSet_temp = null;//[0:iconSet,1:iconId]
    private int colorIndex = 0;

    private String sqref;
    private final List<Object[]> colorScales = new ArrayList<>();//{0:colorScale,1:priority}
    private final List<Object[]> iconSets = new ArrayList<>();//{0:iconSet,1:priority}
    
    private final List<Object[]> dataBars = new ArrayList<>();//{0:dataBar,1:priority}
    private final List<String[]> mapStyleHelper = new ArrayList<>();//{0:condition, 1:styleName, 2:priority}
    private final List<CellStyle> cellStyles = new ArrayList<>();
    private final Map<String, ZSPattern> patterns = new HashMap<>();
    private final Locale locale;

    private String iconSetName;
    private boolean isReverseIconSet = false;
    private boolean isHideIconSetValue = false;
    private Map<String, FontFace> fontFaceMap = new HashMap<>();
    private String extID = null;

    private final SpreadsheetSettings spreadsheetSettings;

    ConditionalFormattingElementParser(XMLPullParserWrapper xpp, List<String> clrScheme, Locale locale, List<XLSXException> xlsxException, List<String> indexedColors, SpreadsheetSettings spreadsheetSettings) {
        super(xpp, xlsxException);
        this.locale = locale;
        this.clrScheme = clrScheme;
        this.indexedColors = indexedColors;
        this.spreadsheetSettings =spreadsheetSettings;
    }

    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                switch (nodeName) {
                    case ElementNameConstants.CONDITIONALFORMATTING:
                        parseConditionalFormattingEndNode();
                        break;
                    case ElementNameConstants.CFRULE:
                        parseCfRuleEndNode();
                        break;
                    case ElementNameConstants.DXF:
                        parsDxfEndNode();
                        break;
                    case ElementNameConstants.COLORSCALE:
                        parseColorScaleEndNode();
                        break;
                    case ElementNameConstants.ICONSET:
                        parseIconSetEndNode();
                        break;
                    case ElementNameConstants.CFVO:
                        parseCfvoEndNode();
                        break;
                    case ElementNameConstants.CFICON:
                        parsecfIconEndNode();
                        break;
                    case ElementNameConstants.COLOR:
                        parseColorEndNode();
                        break;
                }
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.CONDITIONALFORMATTING:
                        parseConditionalFormattingNode();
                        break;
                    case ElementNameConstants.CFRULE:
                        parsCfRuleNode();
                        break;
                    case ElementNameConstants.DXF:
                        parsDxfNode();
                        break;
                    case ElementNameConstants.XM_F:
                    case ElementNameConstants.FORMULA:
                        parseFormulaNode();
                        break;
                    case ElementNameConstants.XM_SQREF:
                        parseXmSqrfNode();
                        break;
                    case ElementNameConstants.COLORSCALE:
                        parseColorScaleNode();
                        break;
                    case ElementNameConstants.DATABAR:
                        parseDataBarNode();
                        break;
                    case ElementNameConstants.ICONSET:
                        parseIconSetNode();
                        break;
                    case ElementNameConstants.EXTLST:
                        parseExtLstNode();
                        break;
                    case ElementNameConstants.ID:
                        parseId();
                        break;
                    case ElementNameConstants.CFVO:
                        parseCfvoNode();
                        break;
                    case ElementNameConstants.CFICON:
                        parsecfIconNode();
                        break;
                    case ElementNameConstants.COLOR:
                        parseColorNode();
                        break;
                }
                break;
        }
    }

    private void parseConditionalFormattingNode() {
        this.sqref = xpp.getAttribute(AttributeNameConstants.SQREF);
//        this.conditions = new ArrayList<>();
//        this.priortyList = new ArrayList<>();
//        this.styleNameList = new ArrayList<>();
//        this.colorScales = new ArrayList<>();
    }

    private void parsCfRuleNode() {
        this.formula_one = null;
        this.formula_two = null;

        String dxfId = xpp.getAttribute(AttributeNameConstants.DXFID);
        this.cfType = xpp.getAttribute(AttributeNameConstants.TYPE);
        this.cfOperator = xpp.getAttribute(AttributeNameConstants.OPERATOR);
        this.cfTimePeriod = xpp.getAttribute(AttributeNameConstants.TIMEPERIOD);
        String text = xpp.getAttribute(AttributeNameConstants.TEXT);
        if(text != null)
        {
            text = "\""+text+"\"";
        }
        this.cfText = text;
        this.priority = xpp.getAttribute(AttributeNameConstants.PRIORITY);

        if (dxfId != null) {
            this.styleName = XLSXConstants.CELLSTYLE_DXF_PREFIX.concat(dxfId);
        } else {
            CellStyle cellStyle2 = new CellStyle();
            cellStyle2.setProperty(TextStyle.Property.COLOR,ZSColor.getColor(null, 0, 0));
            this.cellStyles.add(cellStyle2);
            this.styleName = cellStyle2.getStyleName();
        }

        this.extID = xpp.getAttribute(AttributeNameConstants.ID);
    }

    private void parseFormulaNode() throws XLSXException {
        String excelFormula = xpp.getTextInsideElement();
        String formula = XLSXParserUtility.convertXlsxFormulaToZS(excelFormula);
        if (this.formula_one == null) {
            this.formula_one = formula;
        } else {
            this.formula_two = formula;
        }
    }

    private void parseXmSqrfNode() throws XLSXException {
        this.sqref = xpp.getTextInsideElement();
    }

    private void parsDxfNode() {
        DxfElementParser dxfElementParser = new DxfElementParser(xpp, clrScheme, locale, this.xlsxException, indexedColors, spreadsheetSettings);
        XMLParserAgent xmlpa = new XMLParserAgent(xpp, dxfElementParser);
        xmlpa.addNameSpaceToDiscard(XLSXNamespace.X);
        xmlpa.addNameSpaceToDiscard(XLSXNamespace.X14);        
        try {
            xmlpa.parseNodeTree(true);
        } catch (XLSXException ex) {
            this.xlsxException.add(ex);
            ex.setLevel(Level.SEVERE);
        } 

        this.cellStyle = dxfElementParser.getCellStyle();

        this.styleName = XLSXConstants.CELLSTYLE_X14DXF_PREFIX.concat(String.valueOf(this.x14DxfCount));

        this.cellStyle.setStyleName(this.styleName);

//        transformer.addCellStyleToWorkbook(cellStyle);
//        this.styleNameList.add(this.styleName);
        this.cellStyles.add(cellStyle);

        Object[] pattern = dxfElementParser.getNumFmtMap();
        if (pattern[0] != null) {
            patterns.put((String) pattern[0], (ZSPattern) pattern[1]);
        }
        this.fontFaceMap.putAll(dxfElementParser.getFontFaceMap());
        this.x14DxfCount++;

        parsDxfEndNode();
    }

    private void setCondition() {
        String condition = null;
        XLSXMLDefaults.CFRuleType cFRuleType = null;
        try {
            cFRuleType = XLSXMLDefaults.getCFRuleTypeOfString(this.cfType);
        } catch (XLSXException ex) {
            this.xlsxException.add(ex);
            ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_ONE, formula_one));
            ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_TWO, formula_two));
            ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.REF, sqref));
            ex.setLevel(Level.SEVERE);
            return;
        }
        /*
         we don't support all type of 'cft' like "top10" ...
         */
//        if (cFRuleType == null) {
//            return;
//        }
        if (XLSXMLDefaults.CFRuleType.COLORSCALE.equals(cFRuleType) || XLSXMLDefaults.CFRuleType.ICONSET.equals(cFRuleType)) {
            return;
        }
        if (XLSXMLDefaults.CFRuleType.CELLIS.equals(cFRuleType)) {

            condition = getConditionalCellIsOperator(this.cfOperator);
            if (condition != null) {
                if (this.cfOperator.toLowerCase().contains("between")) {
                    condition += "(" + this.formula_one + "," + this.formula_two + ")";
                } else {
                    condition += this.formula_one;
                }
            }
        } else if ((XLSXMLDefaults.CFRuleType.TIMEPERIOD.equals(cFRuleType))) {

            try {
                condition = OdsEquivalent.eqlConditionalTimePeriodOperator(this.cfTimePeriod);
            } catch (XLSXException ex) {
                this.xlsxException.add(ex);
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_ONE, formula_one));
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_TWO, formula_two));
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.REF, sqref));
            }
        } else if ((XLSXMLDefaults.CFRuleType.CONTAINSTEXT.equals(cFRuleType))
                || (XLSXMLDefaults.CFRuleType.NOTCONTAINSTEXT.equals(cFRuleType))
                || (XLSXMLDefaults.CFRuleType.BEGINSWITH.equals(cFRuleType))
                || (XLSXMLDefaults.CFRuleType.ENDSWITH.equals(cFRuleType))) {

            condition = getConditionalCellIsOperator(this.cfOperator);
            if (condition != null) {
                if(this.cfText == null && this.formula_two != null) {
                    condition += "(" + this.formula_two + ")";
                } else {
                    condition += "(" + this.cfText + ")";
                }
            }
        } else if ((XLSXMLDefaults.CFRuleType.CONTAINSERRORS.equals(cFRuleType))
                || (XLSXMLDefaults.CFRuleType.NOTCONTAINSERRORS.equals(cFRuleType))) {

            condition = getConditionalCellIsOperator(this.cfType);
        } else if ((XLSXMLDefaults.CFRuleType.CONTAINSBLANKS.equals(cFRuleType))
                || (XLSXMLDefaults.CFRuleType.NOTCONTAINSBLANKS.equals(cFRuleType))
                || (XLSXMLDefaults.CFRuleType.EXPRESSION.equals(cFRuleType))) {

            condition = getConditionalCellIsOperator(this.cfType);
            if (condition != null) {
                condition += "(" + this.formula_one + ")";
            }
        } else if(XLSXMLDefaults.CFRuleType.DUPLICATEVALUES.equals(cFRuleType)) {
            condition = "ISDUPLICATE()";//No I18N
        } else if(XLSXMLDefaults.CFRuleType.UNIQUEVALUES.equals(cFRuleType)) {
            condition = "ISUNIQUE()";//No I18N
        }

        if (condition != null) {
            this.mapStyleHelper.add(new String[]{condition, this.styleName, this.priority});
        }
    }

    private String getConditionalCellIsOperator(String key) {
        String condition = null;
        try {
            condition = OdsEquivalent.eqlConditionalCellIsOperator(key);
        } catch (XLSXException ex) {
            this.xlsxException.add(ex);
            ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.TYPE, cfType));
            ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_ONE, formula_one));
            ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_TWO, formula_two));
            ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.REF, sqref));
        }
        return condition;
    }

    private void parseCfRuleEndNode() {
        if(ElementNameConstants.DATABAR.equals(this.cfType)) {
            Object[] objects = this.getDataBars().get(this.getDataBars().size() - 1);//databar, priority, hideValue, extID
            objects[1] = this.priority;
            objects[3] = this.extID;
            this.extID = null;
            return;
        }
        setCondition();
    }

    private void parsDxfEndNode() {

    }

    private void parseConditionalFormattingEndNode() {
//            for (String[] condition : this.conditions) {
//                transformer.addConditionalFmtForCellIs(this.sqref, condition, this.styleName);
//            }
//            for(ColorScale colorScale: this.colorScales) {
//                
//            }
//        transformer.addConditionalFormatting(this.sqref, this.conditions, this.priortyList, this.colorScales, this.styleNameList);
    }

    private void parseColorScaleNode() {
        this.conditionalFormatEntrys = new ArrayList<>();
        this.colorIndex = 0;
    }

    private void parseCfvoNode() {
        this.conditionalFormatEntry = new Object[4];
        String type = xpp.getAttribute(AttributeNameConstants.TYPE);
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        String gte = xpp.getAttribute(AttributeNameConstants.GTE);
        if (gte == null) {
            gte = "1";
        }
        this.conditionalFormatEntry[0] = type;
        this.conditionalFormatEntry[1] = val;
        this.conditionalFormatEntry[3] = gte;
    }

    private void parsecfIconNode() {
        if(this.iconSet_temp == null) {
            this.iconSet_temp = new ArrayList<>();
        }
        this.iconSet_temp.add(new String[]{xpp.getAttribute(AttributeNameConstants.ICONSET), xpp.getAttribute(AttributeNameConstants.ICONID)});
    }

    private void parseColorNode() {
        ZSColor cseColor = getColor();
        this.conditionalFormatEntrys.get(this.colorIndex)[2] = cseColor;
        parseColorEndNode();
    }

    private ZSColor getColor() {
        ColorElementParser colorElementParser = new ColorElementParser(xpp,xlsxException);
        XMLParserAgent xmlpa = new XMLParserAgent(xpp, colorElementParser);
        xmlpa.addNameSpaceToDiscard(XLSXNamespace.X);
        xmlpa.addNameSpaceToDiscard(XLSXNamespace.X14);
        try {
            xmlpa.parseNodeTree(true);
        } catch (XLSXException ex) {
            this.xlsxException.add(ex);
        }

        XLSXColorBean color = colorElementParser.getXLSXColorBean();
        ZSColor cseColor = null;
        if(color != null){
            cseColor = color.getColor(this.clrScheme, this.indexedColors, false);
        }
        return cseColor;
    }

    private void parseCfvoEndNode() {
        this.conditionalFormatEntrys.add(conditionalFormatEntry);
        if (this.formula_one != null && this.conditionalFormatEntry != null && this.conditionalFormatEntry[1] == null) {
            this.conditionalFormatEntry[1] = this.formula_one;
        }
        this.formula_one = null;
        this.formula_two = null;

    }

    private void parsecfIconEndNode() {

    }

    private void parseColorEndNode() {
        this.colorIndex++;
    }

    private void parseColorScaleEndNode() {
        List<ConditionalFormatEntry> cScaleEntrys = new ArrayList<>();
        for (Object[] cScaleEntry : this.conditionalFormatEntrys) {
            String type = (String)cScaleEntry[0];
            String val = (String)cScaleEntry[1];
            ZSColor color = (ZSColor)cScaleEntry[2];
            ConditionalFormatEntry.Entry_Type colorScaleEntry_Type = null;
            try {
                colorScaleEntry_Type = OdsEquivalent.eqlColorScaleEntryType(type);
            } catch (XLSXException ex) {
                this.xlsxException.add(ex);
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.TYPE, cfType));
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_ONE, formula_one));
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_TWO, formula_two));
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.REF, sqref));
            }
            cScaleEntrys.add(new ConditionalFormatEntry(colorScaleEntry_Type, val, color));
        }

        this.getColorScales().add(new Object[]{new ColorScale(cScaleEntrys), this.priority});
    }

    private void parseIconSetEndNode() {
        if (this.iconSetName == null) {
            this.iconSetName = "3TrafficLights1";
        } else {
            this.iconSetName = replaceUnSupportedIconSetName(this.iconSetName, this.conditionalFormatEntrys.size());
        }

        List<ConditionalFormatEntry> iconsetEntrys = new ArrayList<>();
        for (int i = 0; i < this.conditionalFormatEntrys.size(); i++) {
            Object[] iconsetEntry = this.conditionalFormatEntrys.get(i);
            String type = (String)iconsetEntry[0];
            String val = (String)iconsetEntry[1];
            String gte = (String)iconsetEntry[3];
            ConditionalFormatEntry.Entry_Type iconsetEntry_Type = null;
            try {
                iconsetEntry_Type = OdsEquivalent.eqlColorScaleEntryType(type);
            } catch (XLSXException ex) {
                this.xlsxException.add(ex);
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.TYPE, cfType));
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_ONE, formula_one));
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_TWO, formula_two));
                ex.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.REF, sqref));
            }
            String iconCriteria = "gteq";
//            if(!gte.equals(iconCriteria)) {
//                iconCriteria = "gt";
//            }
            if (XLSXParserUtility.isEqualsFalse(gte)) {
                iconCriteria = "gt";
            }

            String iconSetName;
            int iconId;
            if(this.iconSet_temp == null || this.iconSet_temp.size() <= i || this.iconSet_temp.get(i)[0] == null) {
                String[] mod_iconSetName = unSupportedIconSetEntry(this.iconSetName, String.valueOf(i));
                iconSetName = mod_iconSetName[0];
                iconId = Integer.parseInt(mod_iconSetName[1]);
            } else {
                String[] mod_iconSetName = unSupportedIconSetEntry(this.iconSet_temp.get(i)[0],this.iconSet_temp.get(i)[1]);
                iconSetName = mod_iconSetName[0];
                iconId = Integer.parseInt(mod_iconSetName[1]);
            }
            iconsetEntrys.add(new ConditionalFormatEntry(iconsetEntry_Type, val, iconSetName, iconId, iconCriteria));
        }

//        this.isReverseIconSet = true;

        this.getIconSets().add(new Object[]{new IconSet(this.iconSetName, iconsetEntrys, false, this.isReverseIconSet), this.priority, this.isHideIconSetValue});
        this.iconSet_temp = null;
    }

    private static String[] unSupportedIconSetEntry(String iconSetName, String iconId) {
        if(iconSetName == null || iconId == null) {
            return new String[]{iconSetName, iconId};
        }
        switch (iconSetName) {
            case IconSetConstants.REDTOBLACK4:
                switch (iconId) {
                    case "1":iconSetName = IconSetConstants.TRAFFICLIGHTS5;
                }
                break;
            case IconSetConstants.IN_3TRAFFICLIGHTS1:
                iconSetName =  IconSetConstants.TRAFFICLIGHTS5;
                switch (iconId) {
                    case "0": iconId = "2"; break;
                    case "1": iconId = "3"; break;
                    case "2": iconId = "4"; break;
                }
                break;
        }
        return new String[]{iconSetName, iconId};
    }

    /**
     * @return the sqref
     */
    String getSqref() {
        return sqref;
    }

    /**
     * @return the colorScales
     */
    List<Object[]> getColorScales() {
        return colorScales;
    }

    List<Object[]> getIconSets() {
        return iconSets;
    }

    /**
     * @return the mapStyleHelper
     */
    List<String[]> getMapStyleHelper() {
        return mapStyleHelper;
    }

    /**
     * @return the cellStyles
     */
    List<CellStyle> getCellStyles() {
        return cellStyles;
    }

    /**
     * @return the patterns
     */
    public Map<String, ZSPattern> getPatterns() {
        return patterns;
    }

    private void parseDataBarNode() throws XLSXException {
        this.conditionalFormatEntrys = new ArrayList<>();
        this.colorIndex = 0;

        DataBar.Type fill_type = DataBar.Type.GRADIENT;
        if("0".equals(xpp.getAttribute(AttributeNameConstants.GRADIENT))) {
            fill_type = DataBar.Type.SOLID;
        }
//        String minLength = xpp.getAttribute("minLength");
//        String maxLength = xpp.getAttribute("maxLength");

        DataBar.Direction db_direction = DataBar.Direction.CONTEXT;
        String direction = xpp.getAttribute(AttributeNameConstants.DIRECTION);
        if(direction != null) {
            switch (direction) {
                case "leftToRight": db_direction = DataBar.Direction.LEFT_TO_RIGHT;break;
                case "rightToLeft": db_direction = DataBar.Direction.RIGHT_TO_LEFT;break;
            }
        }

        String axisPosition = xpp.getAttribute(AttributeNameConstants.AXISPOSITION);
        DataBar.Axis axis_position = DataBar.Axis.AUTOMATIC;
        if(axisPosition != null) {
            switch (axisPosition) {
                case "middle": axis_position = DataBar.Axis.MIDPOINT; break;
                case "none": axis_position = DataBar.Axis.NONE; break;
            }
        }

        DataBar.Type border_type = DataBar.Type.NONE;
        if("1".equals(xpp.getAttribute(AttributeNameConstants.BORDER))) {
            border_type = DataBar.Type.SOLID;
        }

        String showValue = xpp.getAttribute(AttributeNameConstants.SHOW_VALUE);
        boolean hideValue = false;
        if("0".equals(showValue)) {
            hideValue = true;
        }

        try {
            List<ConditionalFormatEntry> dataBarEntries = new ArrayList<>();
            ZSColor color = null;ZSColor borderColor = null;ZSColor negativeFillColor = null;ZSColor negativeBorderColor = null;ZSColor axisColor = null;
            for(String element = removeNameSpace(xpp.nextNodeName()); !element.equals(ElementNameConstants.DATABAR); element = removeNameSpace(xpp.nextNodeName())) {
                if(xpp.getEventType() == XmlPullParser.START_TAG) {
                    switch (element) {
                        case ElementNameConstants.CFVO:
                            ConditionalFormatEntry.Entry_Type type = null;
                            Object value = null;
                            switch (xpp.getAttribute(AttributeNameConstants.TYPE)) {
                                case "num": type = ConditionalFormatEntry.Entry_Type.NUMBER; value = getCFVOValue();break;
                                case "percent": type = ConditionalFormatEntry.Entry_Type.PERCENT; value = getCFVOValue();break;
                                case "formula": type = ConditionalFormatEntry.Entry_Type.FORMULA; value = getCFVOValue();break;
                                case "percentile": type = ConditionalFormatEntry.Entry_Type.PERCENTILE; value = getCFVOValue();break;

                                case "max": type = ConditionalFormatEntry.Entry_Type.MAXIMUM;break;
                                case "min": type = ConditionalFormatEntry.Entry_Type.MINIMUM;break;
                                case "autoMax": type = ConditionalFormatEntry.Entry_Type.AUTO_MAXIMUM;break;
                                case "autoMin": type = ConditionalFormatEntry.Entry_Type.AUTO_MINIMUM;break;
                            }
                            dataBarEntries.add(new ConditionalFormatEntry(type, value));
                            break;
                        case ElementNameConstants.COLOR: color = getColor();break;
                        case ElementNameConstants.BORDER_COLOR: borderColor = getColor();break;
                        case ElementNameConstants.NEGATIVE_FILL_COLOR: negativeFillColor = getColor();break;
                        case ElementNameConstants.NEGATIVE_BORDER_COLOR: negativeBorderColor = getColor();break;
                        case ElementNameConstants.AXIS_COLOR: axisColor = getColor();break;
                    }
                }
            }
            DataBar dataBar = new DataBar(dataBarEntries, db_direction, fill_type, color, negativeFillColor, border_type, borderColor, negativeBorderColor, axis_position, axisColor);
            Object objects[] = new Object[]{dataBar, null, hideValue, null};//databar, priority, hideValue, extID
            this.getDataBars().add(objects);
        } catch (XLSXException xlsxe) {
            xlsxe.setFeature(XLSXException.FEATURE.DATA_BAR);
            xlsxe.setIsFeatureLost(true);
            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.REF, sqref));
            throw xlsxe;
        }
    }

    private Object getCFVOValue() throws XLSXException {
        String val = xpp.getAttribute(AttributeNameConstants.VAL);
        if(val != null) {
            return val;
        }

        xpp.nextNodeName();
        val = xpp.getTextInsideElement();
        return val;
    }

    private static String removeNameSpace(String name) {
        int index = name.indexOf(":");
        if(index != -1) {
            return name.substring(index+1);
        }
        return name;
    }
    private void parseIconSetNode() {
        this.conditionalFormatEntrys = new ArrayList<>();
        this.colorIndex = 0;

        this.iconSetName = xpp.getAttribute(AttributeNameConstants.ICONSET);

        String reverse = xpp.getAttribute(AttributeNameConstants.REVERSE);
        if (XLSXParserUtility.isEqualsTrue(reverse)) {
            this.isReverseIconSet = true;
        } else {
            this.isReverseIconSet = false;
        }

        String showValue = xpp.getAttribute(AttributeNameConstants.SHOW_VALUE);
        if (XLSXParserUtility.isEqualsFalse(showValue)) {
            this.isHideIconSetValue = true;
        } else {
            this.isHideIconSetValue = false;
        }
    }

    private void parseExtLstNode() throws XLSXException {
    }

    private void parseId() throws XLSXException {
        this.extID = xpp.getTextInsideElement();
    }

    private String replaceUnSupportedIconSetName(String iconName, int size) {
        if(XLSXParserAgent.VERBOSE_LOG) {
            logger.log(Level.OFF, "~~icon-name-before :" + iconName + " , size :" + size);
        }
        switch (iconName) {
            case IconSetConstants.LOVE2:
            case IconSetConstants.LIKES2:
//            case IconSetConstants.STARS3:
//            case IconSetConstants.TRIANGLES3:
            case IconSetConstants.INDICATOR3:
            case IconSetConstants.HEARTRATING3:
                return IconSetConstants.ARROWS3;
            case IconSetConstants.SMILEYS3:
                return "3Smilies"; //No I18N
            case IconSetConstants.SMILEYS5:
            case IconSetConstants.TRAFFICLIGHTS5:
            case IconSetConstants.WEATHER5:
                return IconSetConstants.ARROWS5;
            case IconSetConstants.TRAFFICLIGHTS3:
                return "3TrafficLights1"; //No I18N
            case "3Symbols2":
                return IconSetConstants.SYMBOLS3;
            case IconSetConstants.BOXES5:
            case IconSetConstants.RATINGS5:
            case "5Rating":
            case "5Boxes":
            case "custom":
                switch (size) {
                    case 2:
                    case 3:
                        return IconSetConstants.ARROWS3;
                    case 4:
                        return IconSetConstants.ARROWS4;
                    case 5:
                        return IconSetConstants.ARROWS5;
                }
        }
        if(XLSXParserAgent.VERBOSE_LOG) {
            logger.log(Level.OFF, "~~icon-name-after :" + iconName + " , size :" + size);
        }
        return iconName;
    }

    @Override
    public void beforeParse() throws XLSXException {
    }

    @Override
    public void afterParse() {
    }

    Map<String, FontFace> getFontFaceMap() {
        return fontFaceMap;
    }

    public List<Object[]> getDataBars() {
        return this.dataBars;
    }
}
