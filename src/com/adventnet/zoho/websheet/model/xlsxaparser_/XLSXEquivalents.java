//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

/**
 *
 * <AUTHOR>
 */
class XLSXEquivalents {
    
//    private static Map<String, ChartProperties.REGRESSION_TYPE> odsEqlTrendLineType = null;
//    private static void initOdsEqlTrendLineType() {
//        odsEqlTrendLineType.put("exp", ChartProperties.REGRESSION_TYPE.EXPONENTIAL);
//        odsEqlTrendLineType.put("linear", ChartProperties.REGRESSION_TYPE.LINEAR);
//        odsEqlTrendLineType.put("log", ChartProperties.REGRESSION_TYPE.LOGARITHMIC);
//        odsEqlTrendLineType.put("movingAvg", ChartProperties.REGRESSION_TYPE.MOVING_AVERAGE);
//        odsEqlTrendLineType.put("poly", ChartProperties.REGRESSION_TYPE.POLYNOMIAL);
//        odsEqlTrendLineType.put("power", ChartProperties.REGRESSION_TYPE.POWER);
//    }
//    public static ChartProperties.REGRESSION_TYPE getOdsEqlTrendLineType(String key) {
//        if(odsEqlTrendLineType == null) {
//            odsEqlTrendLineType = new HashMap<>();
//            initOdsEqlTrendLineType();
//        }
//        return odsEqlTrendLineType.get(key);
//    }
    
//    private static Map<String, ChartProperties.LABEL_POSITION> odsEqlDataLabelPos = null;
//    private static void initOdsEqlDataLabelPos() {
//        odsEqlDataLabelPos.put("b", ChartProperties.LABEL_POSITION.BOTTOM);
//        odsEqlDataLabelPos.put("bestFit", ChartProperties.LABEL_POSITION.AVOID_OVERLAP);
//        odsEqlDataLabelPos.put("ctr", ChartProperties.LABEL_POSITION.CENTER);
//        odsEqlDataLabelPos.put("inBase", ChartProperties.LABEL_POSITION.NEAR_ORIGIN);
//        odsEqlDataLabelPos.put("inEnd", ChartProperties.LABEL_POSITION.INSIDE);
//        odsEqlDataLabelPos.put("l", ChartProperties.LABEL_POSITION.LEFT);
//        odsEqlDataLabelPos.put("outEnd", ChartProperties.LABEL_POSITION.OUTSIDE);
//        odsEqlDataLabelPos.put("r", ChartProperties.LABEL_POSITION.RIGHT);
//        odsEqlDataLabelPos.put("t", ChartProperties.LABEL_POSITION.TOP);
//    }
//    public static ChartProperties.LABEL_POSITION getOdsEqlDataLabelPos(String key) {
//        if(odsEqlDataLabelPos == null) {
//            odsEqlDataLabelPos = new HashMap<>();
//            initOdsEqlDataLabelPos();
//        }
//        return odsEqlDataLabelPos.get(key);
//    }
    
    
//    private static Map<String, ChartProperties.AXIS_LABEL_POSITION> odsEqlAxisLabelPos = null;
//    private static void initOdsEqlAxisLabelPos() {
//        odsEqlAxisLabelPos.put("high", ChartProperties.AXIS_LABEL_POSITION.OUTSIDE_END);
//        odsEqlAxisLabelPos.put("low", ChartProperties.AXIS_LABEL_POSITION.OUTSIDE_START);
//        odsEqlAxisLabelPos.put("nextTo", ChartProperties.AXIS_LABEL_POSITION.NEAR_AXIS);
//        odsEqlAxisLabelPos.put("none", null);
//    }
//    public static ChartProperties.AXIS_LABEL_POSITION getOdsEqlAxisLabelPos(String key) {
//        if(odsEqlAxisLabelPos == null) {
//            odsEqlAxisLabelPos = new HashMap<>();
//            initOdsEqlAxisLabelPos();
//        }
//        return odsEqlAxisLabelPos.get(key);
//    }
    
//    private static Map<String, ChartProperties.AXIS_POSITION> odsEqlAxisPos = null;
//    private static void initOdsEqlAxisPos() {
//        odsEqlAxisPos.put("autoZero", ChartProperties.AXIS_POSITION.START);
//        odsEqlAxisPos.put("max", ChartProperties.AXIS_POSITION.END);
//        odsEqlAxisPos.put("min", ChartProperties.AXIS_POSITION.START);
//    }
//    public static ChartProperties.AXIS_POSITION getOdsEqlAxisPos(String key) {
//        if(odsEqlAxisPos == null) {
//            odsEqlAxisPos = new HashMap<>();
//            initOdsEqlAxisPos();
//        }
//        return odsEqlAxisPos.get(key);
//    }
//    
//    private static Map<String, Legend.LEGEND_POSITION> odsEqlLegendPos = null;
//    private static void initOdsEqlLegendPos() {
//        odsEqlLegendPos.put("b", Legend.LEGEND_POSITION.BOTTOM);
//        odsEqlLegendPos.put("l", Legend.LEGEND_POSITION.START);
//        odsEqlLegendPos.put("r", Legend.LEGEND_POSITION.END);
//        odsEqlLegendPos.put("t", Legend.LEGEND_POSITION.TOP);
//        odsEqlLegendPos.put("tr", Legend.LEGEND_POSITION.TOPEND);
//    }
//    public static Legend.LEGEND_POSITION getOdsEqlLegendPos(String key) {
//        if(odsEqlLegendPos == null) {
//            odsEqlLegendPos = new HashMap<>();
//            initOdsEqlLegendPos();
//        }
//        return odsEqlLegendPos.get(key);
//    }
    
//    private static Map<String, ChartProperties.SOLID_TYPE> odsEqlSolidType = null;
//    private static void initOdsEqlSolidType() {
//        odsEqlSolidType.put("cone", ChartProperties.SOLID_TYPE.CONE);
//        odsEqlSolidType.put("coneToMax", ChartProperties.SOLID_TYPE.CONE);
//        odsEqlSolidType.put("box", ChartProperties.SOLID_TYPE.CUBOID);
//        odsEqlSolidType.put("cylinder", ChartProperties.SOLID_TYPE.CYLINDER);
//        odsEqlSolidType.put("pyramid", ChartProperties.SOLID_TYPE.PYRAMID);
//        odsEqlSolidType.put("pyramidToMax", ChartProperties.SOLID_TYPE.PYRAMID);
//    }
//    public static ChartProperties.SOLID_TYPE getOdsEqlSolidType(String key) {
//        if(odsEqlSolidType == null) {
//            odsEqlSolidType = new HashMap<>();
//            initOdsEqlSolidType();
//        }
//        return odsEqlSolidType.get(key);
//    }
    
//    private static Map<String, Axis.DIMENSION> odsEqlErrDir = null;
//    private static void initOdsEqlErrDir() {
//        odsEqlErrDir.put("x", Axis.DIMENSION.X);
//        odsEqlErrDir.put("y", Axis.DIMENSION.Y);
//    }
//    public static Axis.DIMENSION getOdsEqlErrDir(String key) {
//        if(odsEqlErrDir == null) {
//            odsEqlErrDir = new HashMap<>();
//            initOdsEqlErrDir();
//        }
//        return odsEqlErrDir.get(key);
//    }
    
//    private static Map<String, ChartProperties.ERROR_CATEGORY> odsEqlErrValueType = null;
//    private static void initOdsEqlErrValueType() {
//        odsEqlErrValueType.put("cust", ChartProperties.ERROR_CATEGORY.CELL_RANGE);
//        odsEqlErrValueType.put("fixedVal", ChartProperties.ERROR_CATEGORY.CONSTANT);
//        odsEqlErrValueType.put("percentage", ChartProperties.ERROR_CATEGORY.PERCENTAGE);
//        odsEqlErrValueType.put("stdDev", ChartProperties.ERROR_CATEGORY.STANDARD_DEVIATION);
//        odsEqlErrValueType.put("stdErr", ChartProperties.ERROR_CATEGORY.STANDARD_ERROR);
//    }
//    public static ChartProperties.ERROR_CATEGORY getOdsEqlErrValueType(String key) {
//        if(odsEqlErrValueType == null) {
//            odsEqlErrValueType = new HashMap<>();
//            initOdsEqlErrValueType();
//        }
//        return odsEqlErrValueType.get(key);
//    }
    
//    private static Map<String, ChartProperties.TREAT_EMPTY_CELLS> odsEqlDisplayBlankAs = null;
//    private static void initOdsEqlDisplayBlankAs() {
//        odsEqlDisplayBlankAs.put("span", ChartProperties.TREAT_EMPTY_CELLS.IGNORE);
//        odsEqlDisplayBlankAs.put("gap", ChartProperties.TREAT_EMPTY_CELLS.LEAVE_GAP);
//        odsEqlDisplayBlankAs.put("zero", ChartProperties.TREAT_EMPTY_CELLS.USE_ZERO);
//    }
//    public static ChartProperties.TREAT_EMPTY_CELLS getOdsEqlDisplayBlankAs(String key) {
//        if(odsEqlDisplayBlankAs == null) {
//            odsEqlDisplayBlankAs = new HashMap<>();
//            initOdsEqlDisplayBlankAs();
//        }
//        return odsEqlDisplayBlankAs.get(key);
//    }
//    
//    private static Map<String, ChartProperties.SYMBOL_NAME> odsEqlMarkerStyle = null;
//    private static void initOdsEqlMarkerStyle() {
//        odsEqlMarkerStyle.put("circle", ChartProperties.SYMBOL_NAME.CIRCLE);
//        odsEqlMarkerStyle.put("dash", ChartProperties.SYMBOL_NAME.HORIZONTAL_BAR);
//        odsEqlMarkerStyle.put("diamond", ChartProperties.SYMBOL_NAME.DIAMOND);
//        odsEqlMarkerStyle.put("dot", ChartProperties.SYMBOL_NAME.HOURGLASS);
//        odsEqlMarkerStyle.put("none", null);
//        odsEqlMarkerStyle.put("picture", null);
//        odsEqlMarkerStyle.put("plus", ChartProperties.SYMBOL_NAME.PLUS);
//        odsEqlMarkerStyle.put("square", ChartProperties.SYMBOL_NAME.SQUARE);
//        odsEqlMarkerStyle.put("star", ChartProperties.SYMBOL_NAME.STAR);
//        odsEqlMarkerStyle.put("triangle", ChartProperties.SYMBOL_NAME.ARROW_UP);
//        odsEqlMarkerStyle.put("x", ChartProperties.SYMBOL_NAME.X);
//        odsEqlMarkerStyle.put("auto", null);
//    }
//    public static ChartProperties.SYMBOL_NAME getOdsEqlMarkerStyle(String key) {
//        if(odsEqlMarkerStyle == null) {
//            odsEqlMarkerStyle = new HashMap<>();
//            initOdsEqlMarkerStyle();
//        }
//        return odsEqlMarkerStyle.get(key);
//    }
    
//    private static HashMap<String, String> odsEqlNumberFormat = null;
//    private static void initOdsEqlNumberFormat() {
//        odsEqlNumberFormat.put("0", "General");
//        odsEqlNumberFormat.put("1", "0");
//        odsEqlNumberFormat.put("2", "0.00");
//        odsEqlNumberFormat.put("3", "#,##0");
//        odsEqlNumberFormat.put("4", "#,##0.00");
//        odsEqlNumberFormat.put("9", "0%");
//        odsEqlNumberFormat.put("10", "0.00%");
//        odsEqlNumberFormat.put("11", "0.00E+00");
//        odsEqlNumberFormat.put("12", "# ?/?");
//        odsEqlNumberFormat.put("13", "# ??/??");
//        odsEqlNumberFormat.put("14", "mm-dd-yy");
//        odsEqlNumberFormat.put("15", "d-mmm-yy");
//        odsEqlNumberFormat.put("16", "d-mmm");
//        odsEqlNumberFormat.put("17", "mmm-yy");
//        odsEqlNumberFormat.put("18", "h:mm AM/PM");
//        odsEqlNumberFormat.put("19", "h:mm:ss AM/PM");
//        odsEqlNumberFormat.put("20", "h:mm");
//        odsEqlNumberFormat.put("21", "h:mm:ss");
//        odsEqlNumberFormat.put("22", "m/d/yy h:mm");
//        odsEqlNumberFormat.put("37", "#,##0 ;(#,##0)");
//        odsEqlNumberFormat.put("38", "#,##0 ;[Red](#,##0)");
//        odsEqlNumberFormat.put("39", "#,##0.00;(#,##0.00)");
//        odsEqlNumberFormat.put("40", "#,##0.00;[Red](#,##0.00)");
//        odsEqlNumberFormat.put("45", "mm:ss");
//        odsEqlNumberFormat.put("46", "[h]:mm:ss");
//        odsEqlNumberFormat.put("47", "mmss.0");
//        odsEqlNumberFormat.put("48", "##0.0E+0");
//        odsEqlNumberFormat.put("49", "@");
//    }
//    public static String getOdsEqlNumberFormat(String key) {
//        if(odsEqlNumberFormat == null) {
//            odsEqlNumberFormat = new HashMap<>();
//            initOdsEqlNumberFormat();
//        }
//        return odsEqlNumberFormat.get(key);
//    }
    
//    static String getodsEqlFormulaString(String excelFormulaString, Map<String, String> specilCharHandledNameOfName) {
//        StringBuilder sb = new StringBuilder();
//        String[] splitBySingleQuote = excelFormulaString.split("\'");
//
//        if (specilCharHandledNameOfName == null) {
//            specilCharHandledNameOfName = new HashMap<>();
//        }
//
//        boolean singleQF = true;
//        for (String splitBySingleQuote1 : splitBySingleQuote) {
//            if (singleQF) {
//                boolean doubleQF = true;
//                String[] splitByDoubleQuote = splitBySingleQuote1.split("\"");
//                for (String splitByDoubleQuote1 : splitByDoubleQuote) {
//                    if (doubleQF) {
//                        for (String name : specilCharHandledNameOfName.keySet()) {
//                            splitByDoubleQuote1 = splitByDoubleQuote1.replace(name, specilCharHandledNameOfName.get(name));
//                        }
//                        sb.append(splitByDoubleQuote1.replace("!", ".").replace(",", ";"));
//                    } else {
//                        sb.append("\"").append(splitByDoubleQuote1).append("\"");
//                    }
//                    doubleQF = !doubleQF;
//                }
//            } else {
//                if (specilCharHandledNameOfName.containsKey(splitBySingleQuote1)) {
//                    splitBySingleQuote1 = specilCharHandledNameOfName.get(splitBySingleQuote1);
//                }
//                sb.append("'").append(splitBySingleQuote1).append("'");
//            }
//            singleQF = !singleQF;
//        }
//        return sb.toString();
//    }
    
//    private static Map<String, String> cellIsOperators;
//    private static void initCellIsOperators(){
//        cellIsOperators.put("greaterThan", ">");
//        cellIsOperators.put("equal", "=");
//        cellIsOperators.put("lessThan", "<");
//        cellIsOperators.put("greaterThanOrEqual", ">=");
//        cellIsOperators.put("lessThanOrEqual", "<=");
//        cellIsOperators.put("notEqual", "!=");
//        cellIsOperators.put("between", "between");
//        cellIsOperators.put("notBetween", "not-between");
//        //containsText
//        cellIsOperators.put("containsText", "contains-text");
//        cellIsOperators.put("notContains", "not-contains-text");
//        cellIsOperators.put("beginsWith", "begins-with");
//        cellIsOperators.put("endsWith", "ends-with");
//        //errors
//        cellIsOperators.put("containsErrors", "is-error");
//        cellIsOperators.put("notContainsErrors", "is-no-error");
//        //blanks
//        cellIsOperators.put("containsBlanks", "formula-is");
//        cellIsOperators.put("notContainsBlanks", "formula-is");
//        //formula
//        cellIsOperators.put("expression", "formula-is");
//    }
//    static String odsEqlConditionalCellIsOperator(String operator) {
//        if(cellIsOperators == null) {
//            cellIsOperators = new HashMap<>();
//            initCellIsOperators();
//        }
//        return cellIsOperators.get(operator);
//    }
    
//    private static Map<String, String> timePeriodOperators;
//    private static void initTimePeriodOperators() {
//        timePeriodOperators.put("yesterday", "yesterday");
//        timePeriodOperators.put("today", "today");
//        timePeriodOperators.put("tomorrow", "tomorrow");
//        timePeriodOperators.put("last7Days", "last-7-days");
//        timePeriodOperators.put("lastWeek", "last-week");
//        timePeriodOperators.put("thisWeek", "this-week");
//        timePeriodOperators.put("nextWeek", "next-week");
//        timePeriodOperators.put("lastMonth", "last-month");
//        timePeriodOperators.put("thisMonth", "this-month");
//        timePeriodOperators.put("nextMonth", "next-month");
//    }
//    static String odsEqlConditionalTimePeriodOperator(String operator) {
//        if(timePeriodOperators == null) {
//            timePeriodOperators = new HashMap<>();
//            initTimePeriodOperators();
//        }
//        return timePeriodOperators.get(operator);
//    }
    
//    private static Map<String, String> odsEqlDataValidationType;
//    private static void initEqlDataValidationType() {
//        //        odsEqlDataValidationType.put("none", "");
//        odsEqlDataValidationType.put("whole", "cell-content-is-whole-number()");
//        odsEqlDataValidationType.put("decimal", "cell-content-is-decimal-number()");
//        odsEqlDataValidationType.put("date", "cell-content-is-date()");
//        odsEqlDataValidationType.put("time", "cell-content-is-time()");
//        odsEqlDataValidationType.put("textLength", "cell-content-text-length");
//        odsEqlDataValidationType.put("list", "cell-content-is-in-list");
//        //        odsEqlDataValidationType.put("custom", "");
//    }
//    private static String getEqlDataValidationType(String type) {
//        if(odsEqlDataValidationType == null) {
//            odsEqlDataValidationType = new HashMap<>();
//            initEqlDataValidationType();
//        }
//        return odsEqlDataValidationType.get(type);
//    }
    
//    static String odsEqlConditionStringForDataValidation(String type, String operator, String formula1, String formula2) {
//        
//        String conditionString;
//        String odsEqlType = getEqlDataValidationType(type);
//        if (odsEqlType == null) {
//            return null;
//        } else {
//            conditionString = odsEqlType;
//        }
//        if (type.equals("list")) {
//            if(formula1.contains(",")) {
//                formula1 = formula1.replaceAll("\"", "");//temperary fix need to remove this -- quotes will create problem getValueObjectListFromValueStringForNonFormulaType
//                formula1 = formula1.replaceAll(",", "\";\"");
//                formula1 = "\""+formula1+"\"";
//            }
//            conditionString += "(" + formula1 + ")";
//        } else if (type.equals("textLength")) {
//            if (operator == null) {
//                operator = "between";//No I18N
//            }
//            if (operator.equals("between") || operator.equals("notBetween")) {
//                conditionString += "-is-" + odsEqlConditionalCellIsOperator(operator) + "(" + formula1 + "," + formula2 + ")";//No I18N
//            } else {
//                conditionString += "()" + odsEqlConditionalCellIsOperator(operator) + formula1;
//            }
//        } else {
//            if (operator == null) {
//                operator = "between";//No I18N
//            }
//            if (operator.equals("between") || operator.equals("notBetween")) {
//                conditionString += " and cell-content-is-" + odsEqlConditionalCellIsOperator(operator) + "(" + formula1 + "," + formula2 + ")";//No I18N
//            } else {
//                conditionString += " and cell-content()" + odsEqlConditionalCellIsOperator(operator) + formula1;//No I18N
//            }    
//        }
//        return conditionString;
//    }
    
//    private static HashMap<String, Integer> odsEqlHierarchy;
//    private static void initEqlHierarchy() {
//        //not in excel-pivot but in zohosheet-pivot
//            //DAY_OF_WEEK, QUARTERBYYEAR, MONTHBYYEAR
//        odsEqlHierarchy.put(null, -1);
//        odsEqlHierarchy.put("years", 1);
//        odsEqlHierarchy.put("quarters", 2);
//        odsEqlHierarchy.put("months", 3);
//        odsEqlHierarchy.put("days", 4);
//        //not in zohosheet-pivot
//        odsEqlHierarchy.put("hours", -1);
//        odsEqlHierarchy.put("range", -1);
//        odsEqlHierarchy.put("seconds", -1);
//        odsEqlHierarchy.put("minutes", -1);
//    }
//    static int getEqlHierarchy(String key) {
//        if(odsEqlHierarchy == null) {
//            odsEqlHierarchy = new HashMap<>();
//            initEqlHierarchy();
//        }
//        return odsEqlHierarchy.get(key);
//    }
    
//    static PivotSortInfo.Order getSortInfoOrder(String sortType) {
//        PivotSortInfo.Order order = (sortType.equals("descending")) ? PivotSortInfo.Order.DESCENDING : PivotSortInfo.Order.ASCENDING;
//        return order;
//    }
//    
//    private static Map<String, String> odsEqlPivotFucntion;
//    static String getEqlPivotFieldFunction(String subTotal) {
//        if(odsEqlPivotFucntion == null) {
//            odsEqlPivotFucntion = new HashMap<>();
//            odsEqlPivotFucntion.put("countNums", "countnums");
//            odsEqlPivotFucntion.put("stdDev", "stdev");
//            odsEqlPivotFucntion.put("stdDevp", "stdevp");
//        }
//        if(subTotal == null) {
//            subTotal = "sum";//No I18N
//        }
//        if(odsEqlPivotFucntion.containsKey(subTotal)) {
//            subTotal = odsEqlPivotFucntion.get(subTotal);
//        }
//        return subTotal;
//    }
    
    //ods equivalent value for textwrap
//    static String odsEqlWrapOption(String xlsxWrapOption) {
//        return (xlsxWrapOption == null) ? null : (xlsxWrapOption.equals("1")) ? "wrap" : "no-wrap";
//    }
//
//    static String odsEqlShrinkToFit(String xlsxShrinkToFit) {
//        return (xlsxShrinkToFit == null) ? null : (xlsxShrinkToFit.equals("1")) ? "true" : "false";
//    }
//
//    static String odsEqlVerticalAlign(String xlsxVerticalAlign) {
//        //in xlsx default vertical-alignment is bottom
//        return (xlsxVerticalAlign == null) ? "bottom" : xlsxVerticalAlign.equals("center") ? "middle" : xlsxVerticalAlign;//No I18N
//    }
//
//    static String odsEqlTextAlignSource(String xlsxTextAlignSource) {
//        return (xlsxTextAlignSource == null) ? "value-type" : "fix";//No I18N
//    }
//
//    static String odsEqlHorizontalAlign(String xlsxHorizontalAlign) {
//        //xlsx center, centerContinuous, distributed has no equivalent in ods
//        String textAlign;
//        if (xlsxHorizontalAlign == null) {
//            textAlign = null;
//        } else if (xlsxHorizontalAlign.equalsIgnoreCase("left")) {
//            textAlign = "start";//No I18N
//        } else if (xlsxHorizontalAlign.equalsIgnoreCase("center")) {
//            textAlign = xlsxHorizontalAlign;
//        } else if (xlsxHorizontalAlign.equalsIgnoreCase("right")) {
//            textAlign = "end";//No I18N
//        } else if (xlsxHorizontalAlign.equalsIgnoreCase("justify")) {
//            textAlign = xlsxHorizontalAlign;
//        } else if (xlsxHorizontalAlign.equalsIgnoreCase("fill")) {
//            textAlign = "start";//No I18N
//        } else {
//            textAlign = null;
//        }
//        return textAlign;
//    }
//
//    static String odsEqlRepeatContent(String xlsxHorizontalAlign) {
//        return (xlsxHorizontalAlign == null) ? null : xlsxHorizontalAlign.equals("fill") ? "true" : "false";
//    }

//    static PivotField.Orientation odsEqlPivotFieldOrientation(String axis) {
//        PivotField.Orientation orientation = PivotField.Orientation.HIDDEN;
//        
//        if(axis != null)
//        {
//            if("axisRow".equals(axis))
//            {
//                orientation = PivotField.Orientation.ROW;
//            }
//            else if("axisCol".equals(axis))
//            {
//                orientation = PivotField.Orientation.COLUMN;
//            }
//            else if("axisValues".equals(axis))
//            {
//                orientation = PivotField.Orientation.DATA;
//            }
//            else if("axisPage".equals(axis))
//            {
//                orientation = PivotField.Orientation.PAGE;
//            }
//            
//        }
//        
//        return orientation;
//    }
    
//    private static final String FORMULA = "formula";//No I18N
//    private static final String MAX = "max";//No I18N
//    private static final String MIN = "min";//No I18N
//    private static final String NUM = "num";//No I18N
//    private static final String PERCENT = "percent";//No I18N
//    private static final String PERCENTILE = "percentile";//No I18N
//    static ConditionalFormat.ColorScaleEntry_Type getEqlColorScaleEntryType(String type) {
//        switch(type) {
//            case FORMULA:
//                return ConditionalFormat.ColorScaleEntry_Type.FORMULA;
//            case MAX:
//                return ConditionalFormat.ColorScaleEntry_Type.MAXIMUM;
//            case MIN:
//                return ConditionalFormat.ColorScaleEntry_Type.MINIMUM;
//            case NUM:
//                return ConditionalFormat.ColorScaleEntry_Type.NUMBER;
//            case PERCENT:
//                return ConditionalFormat.ColorScaleEntry_Type.PERCENT;
//            case PERCENTILE:
//                return ConditionalFormat.ColorScaleEntry_Type.PERCENTILE;
//        }
//        return null;
//    }

}
