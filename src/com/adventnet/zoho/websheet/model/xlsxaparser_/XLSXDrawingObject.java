//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

/**
 *
 * <AUTHOR>
 */
class XLSXDrawingObject extends XLSXDrawing {

    private XLSXCoordinate a_off = new XLSXCoordinate(0, 0);
    private XLSXCoordinate a_ext = new XLSXCoordinate(0, 0);
    private XLSXCoordinate a_chOff = new XLSXCoordinate(0, 0);
    private XLSXCoordinate a_chExt = new XLSXCoordinate(0, 0);
    private XLSXDrawing anchor;
    private XLSXDrawing parent;
    private String rId;
    private String filePath;
    
    XLSXDrawingObject(XLSXDrawing anchcor, XLSXDrawing parent) {
        this.anchor = anchcor;
        this.parent = parent;
    }

    XLSXCoordinate getA_off() {
        return a_off;
    }

    void setA_off(XLSXCoordinate a_off) {
        this.a_off = a_off;
    }

    void setA_offFromParser(long x, long y) {
        /*
         if drawing-object is part of a group, then it's positon and size is effected by group's position and size
         To-do : roation and flip is not considered for the below transformation ref 'page no-4847'
         */
        if (this.parent != null && this.anchor != null) {
            if (this.parent != this.anchor) {
                double b = ((XLSXDrawingObject) this.parent).getA_chOff().getX();
                double b_ = ((XLSXDrawingObject) this.parent).getA_off().getX();
                double d = ((XLSXDrawingObject) this.parent).getA_chExt().getX();
                double d_ = ((XLSXDrawingObject) this.parent).getA_ext().getX();

                x = (long) (((d_ * ((double) x)) / d) + (b_ - ((d_ * b) / d)));

                b = ((XLSXDrawingObject) this.parent).getA_chOff().getY();
                b_ = ((XLSXDrawingObject) this.parent).getA_off().getY();
                d = ((XLSXDrawingObject) this.parent).getA_chExt().getY();
                d_ = ((XLSXDrawingObject) this.parent).getA_ext().getY();

                y = (long) (((d_ * ((double) y)) / d) + (b_ - ((d_ * b) / d)));
            }
        }
        this.a_off = new XLSXCoordinate(x, y);
    }

    XLSXCoordinate getA_chOff() {
        return a_chOff;
    }

    void setA_chOff(XLSXCoordinate a_chOff) {
        this.a_chOff = a_chOff;
    }

    void setA_ChoffFromParser(long x, long y) {
        this.a_chOff = new XLSXCoordinate(x, y);
    }

    XLSXCoordinate getA_chExt() {
        return a_chExt;
    }

    void setA_chExt(XLSXCoordinate a_chExt) {
        this.a_chExt = a_chExt;
    }

    void setA_chExtFromParser(long x, long y) {
        this.a_chExt = new XLSXCoordinate(x, y);
    }

    XLSXCoordinate getA_ext() {
        return a_ext;
    }

    void setA_ext(XLSXCoordinate a_ext) {
        this.a_ext = a_ext;
    }

    void setA_extFromParser(long x, long y) {
        if (this.parent != null && this.anchor != null) {
            if (this.parent != this.anchor) {
                x = (long) Math.ceil((double) (x * ((XLSXDrawingObject) this.parent).getA_ext().getX()) / (double) (((XLSXDrawingObject) this.parent).getA_chExt().getX()));
                y = (long) Math.ceil((double) (y * ((XLSXDrawingObject) this.parent).getA_ext().getY()) / (double) (((XLSXDrawingObject) this.parent).getA_chExt().getY()));

//                long nA_offX = this.a_off.getX();
//                long nA_offY = this.a_off.getY();
//                
//                nA_offX = (long) Math.ceil((double)(nA_offX*((XLSXDrawingObject)this.parent).getA_ext().getX())/(double)(((XLSXDrawingObject)this.parent).getA_chExt().getX()));
//                nA_offY = (long) Math.ceil((double)(nA_offY*((XLSXDrawingObject)this.parent).getA_ext().getY())/(double)(((XLSXDrawingObject)this.parent).getA_chExt().getY()));
//                
//                this.a_off= (new Coordinate(nA_offX, nA_offY));
            }
        }
        this.a_ext = new XLSXCoordinate(x, y);
    }

    XLSXDrawing getAnchor() {
        return this.anchor;
    }

    XLSXDrawing getParent() {
        return this.parent;
    }

    String getrId() {
        return rId;
    }

    void setrId(String rId) {
        this.rId = rId;
    }
    
    void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    String getFilePath() {
        return this.filePath;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("\n a_off :").append(this.a_off);//No I18N
        sb.append(";a_ext :").append(this.a_ext);//No I18N
        sb.append(";anchor :").append((XLSXAnchor) this.anchor);//No I18N
        if (this.anchor != this.parent) {
            sb.append(";parent :").append((XLSXDrawingObject) this.parent);//No I18N
        } else {
            sb.append(";parent :").append((XLSXAnchor) this.anchor);//No I18N
        }
        sb.append(";rId :").append(this.rId);//No I18N
        return sb.toString();
    }

}
