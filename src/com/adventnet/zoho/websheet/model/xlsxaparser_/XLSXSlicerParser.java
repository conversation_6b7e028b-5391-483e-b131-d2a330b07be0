package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.util.EngineConstants;
import org.xmlpull.v1.XmlPullParser;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;

public class XLSXSlicerParser extends XMLFileParser implements XMLParser {

    private final XLSXTransformer xlsxTransformer;
    private String cacheName;
    private String fieldName;

    int sortOrder;

    private List<Integer> sheetIndices = new ArrayList<>();
    private List<String> pivotTableNames = new ArrayList<>();
    XLSXSlicerParser(XMLFile xLSXFile, XLSXTransformer xlsxTransformer, XMLPullParserWrapper xpp, List<XLSXException> xlsxExceptions) throws XLSXException{
        super(xLSXFile, xpp, xlsxExceptions);
        this.xlsxTransformer = xlsxTransformer;
    }

    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType())
        {
            case XmlPullParser.END_TAG:
                switch (nodeName) {
                    case ElementNameConstants.SLICERCACHEDEFINITION:
                    case ElementNameConstants.TIMELINE_CACHE_DEFINITION:
                        parseSlicerCacheDefinitionEndNode();
                        break;
                }
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.SLICERCACHEDEFINITION:
                    case ElementNameConstants.TIMELINE_CACHE_DEFINITION:
                        parseSlicerCacheDefinitionNode();
                        break;
                    case ElementNameConstants.PIVOTTABLE:
                        parsePivotTableNode();
                        break;
                    case ElementNameConstants.TABULAR:
                        parseTabularNode();
                        break;
                    case ElementNameConstants.SLICER:
                        parseSlicerNode();
                        break;
                    case ElementNameConstants.TIMELINE:
                        parseTimelineNode();
                        break;
                }
                break;
        }
    }

    private void parseSlicerCacheDefinitionNode() {
        cacheName = xpp.getAttribute(AttributeNameConstants.NAME);
        fieldName = xpp.getAttribute(AttributeNameConstants.SOURCENAME);
    }

    private void parsePivotTableNode() {
        int  sheetIndex = Integer.parseInt(xpp.getAttribute(AttributeNameConstants.TAB_ID));
        String tableName = xpp.getAttribute(AttributeNameConstants.NAME);
        this.sheetIndices.add(sheetIndex);
        this.pivotTableNames.add(tableName);
    }

    private void parseTabularNode() {
        sortOrder = xpp.getAttribute(AttributeNameConstants.SORT_ORDER) != null &&  xpp.getAttribute(AttributeNameConstants.SORT_ORDER).equals("descending") ?  1 : 0;
    }
    private void parseSlicerCacheDefinitionEndNode() {
        SlicerCache slicerCache = new SlicerCache(cacheName, fieldName, sortOrder, sheetIndices, pivotTableNames);
        this.xlsxTransformer.addSlicerCache(slicerCache);
    }

    private void parseSlicerNode() {
        String cacheName = this.xpp.getAttribute(AttributeNameConstants.CACHE);
        String name = this.xpp.getAttribute(AttributeNameConstants.NAME);
        String caption = this.xpp.getAttribute(AttributeNameConstants.CAPTION);
        int rowHeight = this.xpp.getAttribute(AttributeNameConstants.ROWHEIGHT) == null ? EngineConstants.DEFAULT_SLICER_BUTTON_HEIGHT : Integer.parseInt(this.xpp.getAttribute(AttributeNameConstants.ROWHEIGHT)) / 9525;
        String styleName = this.xpp.getAttribute(AttributeNameConstants.STYLE);
        if(styleName == null)
        {
            styleName = xlsxTransformer.getXlsxParserStyleContainer().getDefaultSlicerStyle();
        }
        boolean showCaption = this.xpp.getAttribute(AttributeNameConstants.SHOWCAPTION) == null || XLSXParserUtility.isEqualsTrue(this.xpp.getAttribute(AttributeNameConstants.SHOWCAPTION));
        int columns = this.xpp.getAttribute(AttributeNameConstants.COLUMNS) == null ? 1 : Integer.parseInt(this.xpp.getAttribute(AttributeNameConstants.COLUMNS));
        int startItem = this.xpp.getAttribute(AttributeNameConstants.START_ITEM) == null ? 0 : Integer.parseInt(this.xpp.getAttribute(AttributeNameConstants.START_ITEM));

        SlicerDefinition slicerDefinition = new SlicerDefinition(name,cacheName, caption, columns, styleName, rowHeight, showCaption, startItem);
        this.xlsxTransformer.addSlicerDefinition(slicerDefinition);
    }

    private void parseTimelineNode() {
        String cacheName = this.xpp.getAttribute(AttributeNameConstants.CACHE);
        String name = this.xpp.getAttribute(AttributeNameConstants.NAME);
        String caption = this.xpp.getAttribute(AttributeNameConstants.CAPTION);
        String styleName = this.xpp.getAttribute(AttributeNameConstants.STYLE);
        if (styleName == null) {
            styleName = xlsxTransformer.getXlsxParserStyleContainer().getDefaultTimelineStyle();
        }

        int timeLevel = Integer.parseInt(this.xpp.getAttribute(AttributeNameConstants.LEVEL));
        boolean showHeader = this.xpp.getAttribute(AttributeNameConstants.SHOW_HEADER) == null || XLSXParserUtility.isEqualsTrue(this.xpp.getAttribute(AttributeNameConstants.SHOW_HEADER));
        boolean showSelectionLabel = this.xpp.getAttribute(AttributeNameConstants.SHOW_SELECTION_LABEL) == null || XLSXParserUtility.isEqualsTrue(this.xpp.getAttribute(AttributeNameConstants.SHOW_SELECTION_LABEL));
        boolean showTimeLevel = this.xpp.getAttribute(AttributeNameConstants.SHOW_TIME_LEVEL) == null || XLSXParserUtility.isEqualsTrue(this.xpp.getAttribute(AttributeNameConstants.SHOW_TIME_LEVEL));

        String scrollPositionStr = this.xpp.getAttribute(AttributeNameConstants.SCROLL_POSITION);
        Date scrollPosition = null;
        if(scrollPositionStr != null) {
            try {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                scrollPosition = formatter.parse(scrollPositionStr);
            } catch (ParseException e) {
                logger.log(Level.INFO, "unable to parse timeline scroll position");
            }
        }

        TimeLineDefinition timeLineDefinition = new TimeLineDefinition(name, cacheName, caption, styleName, timeLevel, scrollPosition, showHeader, showTimeLevel, showSelectionLabel);
        this.xlsxTransformer.addSlicerDefinition(timeLineDefinition);
    }

    @Override
    public void beforeParse() throws XLSXException {
    }

    @Override
    public void afterParse() throws XLSXException {
    }


}
