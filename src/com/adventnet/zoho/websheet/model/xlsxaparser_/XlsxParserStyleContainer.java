//$Id$
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.FontFace;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;

import java.util.*;

public class XlsxParserStyleContainer {
    private Map<String, FontFace> fontFaceMap = new HashMap<>();
    private List<ZSColor> dxf_fg_colors = new ArrayList<>();
    private Map<String, ZSPattern> patterns = new HashMap<>();
    private Map<String, ZSPattern> numFmtIdPatternMap = new HashMap<>();
    private Set<String> protectionEnableStyleNames = new HashSet<>();
    private XLSXTableStylesBean xlsxTableStylesBean = new XLSXTableStylesBean();
    private final Map<String, String> styleName_builtInNumFmtId_map = new HashMap<>();
    private final Set<String> styleNameWithApostrophe = new HashSet<>();
    private final List<CellStyle> cellStyles = new ArrayList<>();
    private final List<String> clrScheme = new ArrayList<>();
    private List<String> indexedColors = Arrays.asList(XLSXMLDefaults.INDEXEDCOLOR);

    private String defaultSlicerStyle;
    private String defaultTimelineStyle;
    private final Map<String, XLSXSlicerStyleContainer> xlsxSlicerStyleMap = new HashMap<>();
    private final Map<String, XLSXTimelineStyleContainer> xlsxTimelineStyleMap = new HashMap<>();
    private final Map<String, CellStyle> named_cell_styles = new HashMap<>();

    public Map<String, CellStyle> getNamed_cell_styles() {
        return named_cell_styles;
    }

    public Map<String, FontFace> getFontFaceMap() {
        return fontFaceMap;
    }

    public List<ZSColor> getDxf_fg_colors() {
        return dxf_fg_colors;
    }

    public Map<String, ZSPattern> getPatterns() {
        return patterns;
    }

    public Set<String> getProtectionEnableStyleNames() {
        return protectionEnableStyleNames;
    }

    public XLSXTableStylesBean getXlsxTableStylesBean() {
        return xlsxTableStylesBean;
    }

    public Map<String, String> getStyleName_builtInNumFmtId_map() {
        return styleName_builtInNumFmtId_map;
    }

    public Set<String> getStyleNameWithApostrophe() {
        return styleNameWithApostrophe;
    }

    public List<CellStyle> getCellStyles() {
        return cellStyles;
    }

    public List<String> getClrScheme() {
        return clrScheme;
    }

    public List<String> getIndexedColors() {
        return indexedColors;
    }

    public void setIndexedColors(List<String> colors) {
        this.indexedColors = colors;
    }

    public Map<String, ZSPattern> getNumFmtIdPatternMap() {
        return numFmtIdPatternMap;
    }

    public XLSXSlicerStyleContainer getSlicerStyles(String styleName) { return xlsxSlicerStyleMap.get(styleName);}
    public XLSXTimelineStyleContainer getTimelineStyles(String styleName) { return xlsxTimelineStyleMap.get(styleName);}

    public void addSlicerStyle(String styleName, XLSXSlicerStyleContainer xlsxSlicerStyleContainer) {
        this.xlsxSlicerStyleMap.put(styleName, xlsxSlicerStyleContainer);
    }

    public void addTimelineStyle(String styleName, XLSXTimelineStyleContainer xlsxTimelineStyleContainer) {
        this.xlsxTimelineStyleMap.put(styleName, xlsxTimelineStyleContainer);
    }

    public void setDefaultSlicerStyle(String defaultSlicerStyle) {
        this.defaultSlicerStyle = defaultSlicerStyle;
    }

    public String getDefaultSlicerStyle() {
        return defaultSlicerStyle;
    }

    public void setDefaultTimelineStyle(String defaultTimelineStyle) {
        this.defaultTimelineStyle = defaultTimelineStyle;
    }

    public String getDefaultTimelineStyle() {
        return defaultTimelineStyle;
    }
}
