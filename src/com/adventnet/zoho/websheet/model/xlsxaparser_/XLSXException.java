//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class XLSXException extends Exception {

    private FEATURE feature;
    private final List<Identity> ids = new ArrayList<>();
    private CAUSETYPE causeType;
    private String cause;
    private String xmlPositionDescription = null;
    private boolean isFeatureLost = false;
    private Level level = Level.WARNING;

    XLSXException() {
        super();
    }

    XLSXException(Exception ex) {
        this.initCause(ex);
    }

    XLSXException(String message) {
        super(message);
    }
    /**
     * @param xmlPositionDescription the xmlPositionDescription to set
     */
    void setXmlPositionDescription(String xmlPositionDescription) {
        this.xmlPositionDescription = xmlPositionDescription;
    }
    
    static enum FEATURE {

        CTRL_PROP("Ctrl-Prop"),//No I18N
        DATAVALIDATION("DataValidation"),//No I18N
        CONDITIONAL_FORMATTING("Conditional-Formatting"),//No I18N
        FREEZE_PANE("Freeze-Pane"),//No I18N
        MERGE_CELLS("Merge-Cells"),//No I18N
        DRAWING("Drawing"),//No I18N
        WORKBOOK("Workbook"),//No I18N
        PIVOT_CACHE_DEFINITION("Pivot-Cache-Definition"),//No I18N
        PIVOT_TABLE("Pivot-Table"),//No I18N
        CELL("Cell"),//No I18N
        CHART("Chart"),//No I18N
        IMAGE("Image"),//No I18N
        CELL_IMAGE("CellImage"),//No I18N
        FILTER("Filter"),//No I18N
        DATA_BAR("Data-Bar"),//No I18N
        ICON_SET("Icon-Set"),//No I18N
        COMMENT("Comment"),//No I18N
        PATTERN("Pattern"),//No I18N
        TABLE("TABLE"),//No I18N
        STYLES("Styles"),//No I18N
        SHARED_STRINGS("SharedStrings"),//No I18N
        THEMES("Themes"),//No I18N
        COLUMN("Column"),//No I18N
        HYPERLINK("HyperLink"),//No I18N
        ROW("Rows"),//No I18N
        CHART_LABEL_POSITION("Lable-Position"),//No I18N
        CHART_AXIS_POSITION("Chart-Axis-Position"),//No I18N
        CONTROLS("Controls"),//No I18N
        HEADERFOTTER("Header-Footer"),//No I18N
        OLEOBJECTS("Ole-Objects"),//No I18N
        SHEETPROTECTION("Sheet-Protection"),//No I18N
        WORKBOOKROTECTION("Workbook-Protection"),//No I18N
        CELL_FORMULA("Cell-Formula"),//No I18N
        NAMED_EXPRESSION("Named-Expression"),//No I18N
        SHEET("Sheet"),//No I18N
        SLICER("slicer"),//No I18N
        SPARKLINE("sparklineGroups"), FONT("font"), DATE_VALUE("DATE_VALUE");//No I18N

        private final String feature;

        private FEATURE(String feature) {
            this.feature = feature;
        }

        @Override
        public String toString() {
            return feature;
        }
    };

    public enum CAUSETYPE {

        SHEETS_LIMIT_EXCEEDED("Sheets-Limit-Exceeded"),//No I18N
        ROW_LIMIT_EXCEEDED("Row-Limit-Exceeded"),//No I18N
        COLUMN_LIMIT_EXCEEDED("Column-Limit-Exceeded"),//No I18N
        CELLS_LIMIT_EXCEEDED("Cells-limit-Exceeded"),//No I18N
        FOMRMULA_LIMIT_EXCEEDED("Formulas-limit-Exceeded"),//No I18N
        PROTECTION("Protection Found In Sheet"),//No I18N
        UNKNOWN("Unknown"),//No I18N
        REPOSITORY_NULL("Repository not intialized"),//No I18N
        ARGUMENT_NULL("Argument null"),//No I18N
        INVALID_RANGE("Invalid Range"),//No I18N
        EXTERNAL_SOURCE("Source External"),//No I18N
        NO_PIVOT_FIELD("All Pivot Fields are Empty"),//No I18N
        FILE_CORRUPTED("File Corrupted/Misssing File"),//No I18N
        NOTSUPPORTED("Not-Supported"),//No I18N
        XMLPULLPARSER_EXCEPTION("XML-PULLPARSER-EXCEPTION"),ERROR_NODE("formulas not parsed, as they were Error Nodes"),//No I18N
        STRING_LIMIT_EXCEEDED("String-Limit-Exceeded"), IO_EXCEPTION("IO_EXCEPTION"), NUM_FMT_EXCEPTION("NUM_FMT_EXCEPTION"), TIME_OUT("TIME_OUT"); //No I18N

        private final String type;

        private CAUSETYPE(String type) {
            this.type = type;
        }

        @Override
        public String toString() {
            return type;
        }
    };

    static class Identity {

        private TYPE type;
        private String value;

        static enum TYPE {

            NAME("Name"),//No I18N
            STYLE_NAME("Style-Name"),//No I18N
            VALUE("Value"),//No I18N
            TYPE("Type"),//No I18N
            FORMULA_ONE("Formula-One"),//No I18N
            FORMULA_TWO("Formula-Two"),//No I18N
            REF("Reference"),//No I18N
            HYPERLINK_DISPLAY("HyperLink-Display"),//No I18N
            HYPERLINK_LOCATION("HyperLink-Location"),//No I18N
            CHART_LABEL_POSITION("Chart-Label-Position"),//No I18N
            CHART_AXIS_POSITION("Chart-Axis-Position"),//No I18N
            AXIS_LABEL_POSITION("Axis-Label-Position"),//No I18N
            CHART_CLASS("Chart-Class"),//No I18N
            CHART_LEGEND_POSITOIN("Chart-Legend-Position"),//No I18N
            CHART_SOLIDTYPE("Chart-Solid-Type"),//No I18N
            TRENDLINE_TYPE("TrendLine-Type"),//No I18N
            AXIS_DIMENSION("Axis-Dimension"),//No I18N
            ERROR_CATEGORY("Error-Category"),//No I18N
            MARKER_STYLE("Marker-Style"),//No I18N
            CHART_DISPLAY_BLANK_TYPE("Chart-Display-Blank-Type"),//No I18N
            CONDITIONAL_CELLIS_OPERATOR("Conditional-CellIs-Operator"),//No I18N
            CONDITIONAL_TIMEPERIOD_OPERATOR("Conditional-TimePeriod-Operator"),//No I18N
            DATAVALIDATION_TYPE("DataValidation-Type"),//No I18N
            PIVOTFIELD_FUNCTION("Pivot-Field-Function"),//No I18N
            VERTICAL_ALIGNMENT("Vertical-Alignment"),//No I18N
            HORIZONTAL_ALIGNMENT("Horizontal-Alignment"),//No I18N
            PIOVT_FIELD_ORIENTATION("Pivot-Field-Orientation"),//No I18N
            COLORSCALE_ENTRY_TYPE("ColorScale-Entry-Type"),//No I18N
            BORDER_STYLE("Border-Style"),//No I18N
            UNDERLINE_TYPE("UnderLine-Type"),//No I18N
            TEXT_POSITION("Text-Position"),//No I18N
            SOURCE_REF("Source-Reference"),//No I18N
            TARGET_REF("Target-Reference"),//No I18N
            SHEET_NAME("Sheet-Name"),//No I18N
            TITEL("Title"),//No I18N
            PATTERN("Pattern"),//No I18N
            XLSX_NUMFMT("Xlsx-Original-NumFmt"),//No I18N
            XMLFile("Xml-File"),//No I18N
            REPO_FILE("Repository-File"),//No I18N
            ANCHOR_TYPE("Anchor-Type"),//No I18N
            CFRULE_TYPE("Conditional-Formatting-Rule-Type"),//No I18N
            LOCALE_CODE("Locale-Code"),//No I18N
            CURRETN_PATH("Current-Directory"),//No I18N
            TARGET_PATH("Target-Path"),//No I18N
            COLUMN("Column"),//No I18N
            ROW("Row"),//No I18N
            UNKNOWN("Unknown");//No I18N

            private TYPE() {
                this.type = null;
            }

            private final String type;

            private TYPE(String type) {
                this.type = type;
            }

            @Override
            public String toString() {
                return type;
            }
        };

        Identity(TYPE type, String value) {
            this.type = type;
            this.value = value;
        }

        /**
         * @return the idType
         */
        TYPE getType() {
            return type;
        }

        /**
         * @param idType the idType to set
         */
        void setType(TYPE idType) {
            this.type = idType;
        }

        /**
         * @return the id
         */
        String getValue() {
            return value;
        }

        /**
         * @param id the id to set
         */
        void setValue(String id) {
            this.value = id;
        }

        @Override
        public String toString() {
            StringBuilder stringBuilder = new StringBuilder();
            if (getType() != null && getValue() != null) {
                stringBuilder.append(getType());
                stringBuilder.append(":");
                if (getValue() == null) {
                    stringBuilder.append("NULL");//No I18N
                } else {
                    stringBuilder.append(getValue());
                }
            }
            return stringBuilder.toString();
        }
    }

    /**
     * @param feature the feature to set
     */
    void setFeature(FEATURE feature) {
        this.feature = feature;
    }

    /**
     * @return the causeType
     */
    public CAUSETYPE getCauseType() {
        return causeType;
    }

    /**
     * @param causeType the causeType to set
     */
    void setCauseType(CAUSETYPE causeType) {
        if(this.causeType == null) {//original cause-type should not be replaced
            this.causeType = causeType;
        }
    }

    /**
     * @return the isFeatureLost
     */
    boolean isIsFeatureLost() {
        return isFeatureLost;
    }

    /**
     * @param isFeatureLost the isFeatureLost to set
     */
    void setIsFeatureLost(boolean isFeatureLost) {
        this.isFeatureLost = isFeatureLost;
    }

    /**
     * @param cause the cause to set
     */
    void setCause(String cause) {
        this.cause = cause;
    }

    /**
     * @param id the id to set
     */
    void addIdentity(Identity id) {
        this.ids.add(id);
    }

    String toStringForClient() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("Lost Property:");//NO I18N
        if (feature != null) {
            stringBuilder.append(feature);
        }

        int idsSize = ids.size();
        if (idsSize != 0) {
            stringBuilder.append("(");
            for (int i = 0; i < idsSize; i++) {
                String id = ids.get(i).toString();
                if (id.length() != 0) {
                    stringBuilder.append(id);
                    if (i != idsSize - 1) {
                        stringBuilder.append(", ");
                    }
                }
            }
            stringBuilder.append(")");
        }

        if (getCauseType() != null) {
            stringBuilder.append(", ");
            stringBuilder.append(getCauseType());
            if (cause != null) {
                stringBuilder.append("(");
                stringBuilder.append(cause);
                stringBuilder.append(")");
            }
        }
        if(this.xmlPositionDescription != null) {
            stringBuilder.append("\n[XML-POSITION-DESCRIPTION]:");//NO I18N
            stringBuilder.append(this.xmlPositionDescription);
        }
        return stringBuilder.toString();
    }

    void setLevel(Level level) {
        this.level = level;
    }

    void log(Logger logger) {
        XLSXParserAgent.putToFEATURES_NOT_PARSED(this.feature, this.causeType == null ? CAUSETYPE.UNKNOWN : this.causeType);
        if(!XLSXParserAgent.VERBOSE_LOG) {
            return;
        }
        StringJoiner exceptionStackTrace = new StringJoiner("\n");//No I18N
        exceptionStackTrace.add(getLoggableStackTrace(this));
        Throwable throwable = this.getCause();
        while(throwable != null) {
            exceptionStackTrace.add("Caused By: "+getLoggableStackTrace((Exception) throwable));//No I18N
            throwable = throwable.getCause();
        }
        logger.log(this.level, "[XLSX-EXCEPTION]{0}", new Object[]{exceptionStackTrace.toString()});
    }

    private String getLoggableStackTrace(Exception exception) {
        StringBuilder exceptionStackTrace = new StringBuilder();

        int logLimit = 7;
        if(exception instanceof XLSXException) {
            XLSXException xlsxException = (XLSXException) exception;
            if(xlsxException.level.intValue() > Level.WARNING.intValue()) {
                logLimit = 5;
            } else {
                logLimit = 1;
            }
            exceptionStackTrace.append(xlsxException.toStringForClient());
        } else {
            exceptionStackTrace.append(exception.getClass().getCanonicalName());
            if (exception.getMessage() != null) {
                exceptionStackTrace.append(" : ").append(exception.getMessage());
            }
        }

        if(logLimit > 0) {
            exceptionStackTrace.append("\n");//No I18N
            StringJoiner stringJoiner = new StringJoiner("\n\t","\t","");//No I18N
            StackTraceElement[] stackTraceElements = exception.getStackTrace();
            for (int i = 0; i < logLimit && i < stackTraceElements.length; i++) {
                StackTraceElement stackTraceElement = stackTraceElements[i];
                stringJoiner.add(stackTraceElement.toString());
            }
            exceptionStackTrace.append(stringJoiner);
        }
        return exceptionStackTrace.toString();
    }

}
