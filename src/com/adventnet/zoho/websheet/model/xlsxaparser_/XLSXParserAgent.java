//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.image.Image;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.DocumentUtils;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.zoho.sheet.chart.ChartUtils;
//import com.zoho.sheet.conversion.ConnectionPool;
//import com.zoho.sheet.conversion.ConnectionPool.ConnectionObject;
import com.zoho.sheet.chartengine.ChartEngineManager;
import com.zoho.sheet.conversion.ImportExportUtil;
import com.zoho.sheet.conversion.LiboImageConverter;
import com.zoho.sheet.knitcharts.chart.Chart;
import com.zoho.sheet.knitcharts.chart.ProtoChart;
import com.zoho.sheet.knitcharts.api.ChartsAPIEndpoint;
import com.zoho.sheet.knitcharts.chart.sheetchart.api.SheetChartAPI;
import com.zoho.sheet.util.ImageUtils;
import com.zoho.sheet.util.RemoteUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
public class XLSXParserAgent {

    private static final Logger LOGGER = Logger.getLogger(XLSXParserAgent.class.getName());
    private final Workbook workbook;
    private final Map<String, String> sheetNameMap = new LinkedHashMap<>();//sheet-rename depends upon the order
    private XLSXParser xlsxp;
    private XMLFile thumbnail;

    public static final ThreadLocal<Map<XLSXException.FEATURE, XLSXException.CAUSETYPE>> FEATURES_NOT_PARSED = new ThreadLocal<>();
    public static final boolean VERBOSE_LOG = false;
    private JSONArrayWrapper imagesJSONArray = new JSONArrayWrapper();

    public XLSXParserAgent(Workbook workbook) {
        FEATURES_NOT_PARSED.set(new HashMap<>());
        this.workbook = workbook;
    }

    public void parseXlsxDocument(byte[] xlsx_bytes) throws Exception {
        long start = System.currentTimeMillis();

        setDefaultLocaleAndCalculationSettingsForXLSX(this.workbook);

        XMLFileAgent xmlfa = new XMLFileAgent(xlsx_bytes);

        this.thumbnail = xmlfa.getXMLFileByName(XLSXFileUtility.THUMBNAIL);

        xlsxp = new XLSXParser(xmlfa, workbook, new ArrayList<>());

        try {
            xlsxp.parse();
            initSheetNameMap();
        } catch (Exception e) {
            LOGGER.log(Level.OFF, "[XLSX-PARSER-FAIL]" + (e instanceof XLSXException ? ((XLSXException)e).toStringForClient() : "") + "\n[XML-FILES]"+  xmlfa.getXmlFiles().toString(), e);
            throw e;
        } finally {
            long end = System.currentTimeMillis();
            LOGGER.log(Level.OFF, "[XLSX-PARSER-UTILITY-TIME-STATS] parsing xml-files :{0}ms", new Object[]{(end - start)});
        }
//        catch (XLSXException ex) {
//            ex.setLevel(Level.OFF);
//            if (ex.getCauseType() != null) {
//                switch (ex.getCauseType()) {
//                    case COLUMN_LIMIT_EXCEEDED:
//                        ex.log(LOGGER);
//                        return ZFSNGConstants.CONVERSION_FAILURE_NO_OF_COLUMNS_EXCEEDED;
//                    case ROW_LIMIT_EXCEEDED:
//                        ex.log(LOGGER);
//                        return ZFSNGConstants.CONVERSION_FAILURE_NO_OF_ROWS_EXCEEDED;
//                    case CELLS_LIMIT_EXCEEDED:
//                        ex.log(LOGGER);
//                        return ZFSNGConstants.CONVERSION_FAILURE_NO_OF_CELLS_EXCEEDED;
//                    case PROTECTION:
//                        ex.log(LOGGER);
//                        return ZFSNGConstants.CONVERSION_FAILURE_PASS_PROTECTED;
//                    case SHEETS_LIMIT_EXCEEDED:
//                        ex.log(LOGGER);
//                        return ZFSNGConstants.CONVERSION_FAILURE_PASS_PROTECTED;
//                }
//            }
//            throw new SheetEngineException("[XLSX-PARSER-FAIL]" + ex.toStringForClient() + "\n[XML-FILES]"+  xmlfa.getXmlFiles().toString(), ex);//No I18N
//        }catch (Exception e) {
//            XLSXException ex = new XLSXException(e);
//            ex.setLevel(Level.OFF);
//            ex.log(LOGGER);
//            throw new SheetEngineException("[XLSX-PARSER-FAIL]" + ex.toStringForClient() + "\n[XML-FILES]"+  xmlfa.getXmlFiles().toString(), ex);//No I18N
//        }
    }

    public List<String> getSheetNames() {
        List<String> sheetNames = new ArrayList<>();
        for (String sheetName : this.sheetNameMap.values()) {
            sheetNames.add(sheetName);
        }
        return sheetNames;
    }

    /**
     * make db entries for sheet-name, charts, images
     */
    public void makeDbEntries(WorkbookContainer container, Map<String, String> existingXmlImagePathToUrlMap, boolean addCharts) {
        String resourceKey = container.getResourceKey();
        long start = System.currentTimeMillis();
        /*
         sheet-rename should be done before saving WorkbookContainer
         because saving saves workbook data into xml-fragments
         */
        try {
            updateSheetNames(container.getDocId(), container.getDocOwner());
        } catch (Exception e) {
            XLSXException ex = new XLSXException(e);
            ex.setFeature(XLSXException.FEATURE.SHEET);
            ex.setLevel(Level.SEVERE);
            ex.log(LOGGER);
        }

        long end = System.currentTimeMillis();
        if(XLSXParserAgent.VERBOSE_LOG) {
            LOGGER.log(Level.OFF, "[XLSX-PARSER-UTILITY-TIME-STATS] sheet-rename :{0}ms, resource-key:{1}", new Object[]{(end - start), resourceKey});
        }

        {//rename sheet in chart dataRange
            Set<String> changedOldSheetNames = sheetNameMap.keySet().stream()
                    .filter(k -> !sheetNameMap.get(k).equals(k))
                    .collect(Collectors.toSet());
            for (XLSXChart xlsxChart : xlsxp.getXlsxCharts()) {
                SheetChartAPI.updateDataSources(
                        xlsxChart.getProtoChart().sheetMeta,
                        SheetChartAPI.getDataSources(xlsxChart.getProtoChart().sheetMeta)
                                .stream()
                                .map(range -> {
                                    String escapeCharReplacedChartDataRange = range.replace("''", "'");
                                    Optional<String> any = changedOldSheetNames.stream().filter(osn -> escapeCharReplacedChartDataRange.startsWith("'" + osn + "'")).findAny();
                                    if(any.isPresent()) {
                                        return range.replace(any.get(), sheetNameMap.get(any.get()));
                                    }
                                    return range;
                                })
                                .collect(Collectors.toList()));
            }
        }

        if(addCharts) {
            start = end;
            try {
                addCharts(xlsxp.getXlsxCharts(), container);
            } catch (Exception e) {
                XLSXException ex = new XLSXException(e);
                ex.setFeature(XLSXException.FEATURE.CHART);
                ex.setLevel(Level.SEVERE);
                ex.log(LOGGER);
            }

            end = System.currentTimeMillis();
            if(XLSXParserAgent.VERBOSE_LOG) {
                LOGGER.log(Level.OFF, "[XLSX-PARSER-UTILITY-TIME-STATS] adding charts to DB :{0}ms, resource-key:{1}", new Object[]{(end - start), resourceKey});
            }
        }


        try {
            addXlsxImagesToWorkbookContainer(xlsxp.getXlsxImages(), container, existingXmlImagePathToUrlMap);
        } catch (Exception e) {
            XLSXException ex = new XLSXException(e);
            ex.setFeature(XLSXException.FEATURE.IMAGE);
            ex.setLevel(Level.SEVERE);
            ex.log(LOGGER);
        }
        end = System.currentTimeMillis();
        if(XLSXParserAgent.VERBOSE_LOG) {
            LOGGER.log(Level.OFF, "[XLSX-PARSER-UTILITY-TIME-STATS] adding images to DB :{0}ms, resource-key:{1}", new Object[]{(end - start), resourceKey});
        }
        start = end;

//            chunkContainer.setWorkbook(workbook, true, false);
        String activeSheetName = workbook.getActiveSheetName();
        
        Sheet activeSheet = null;

        if (activeSheetName != null) {
            activeSheet = workbook.getSheet(activeSheetName);
        }

        if (activeSheet == null) {
            activeSheet = workbook.getSheet(0);
        }

        if (activeSheet != null) {
            container.setCurrCursorPosSheetCodeName(activeSheet.getAssociatedName());
        }

        if(XLSXParserAgent.VERBOSE_LOG) {
            LOGGER.log(Level.SEVERE, "[XLSX-PARSER-SUCCESS]");
        }
    }

    private void addCharts(List<XLSXChart> xlsxCharts, WorkbookContainer container) {
        for (XLSXChart xlsxChart : xlsxCharts) {
            try {
                ProtoChart protoChart = xlsxChart.getProtoChart();
                protoChart.sheetName = xlsxChart.getSheet().getName();
                protoChart.sheetID = xlsxChart.getSheet().getAssociatedName();

                ChartEngineManager.getChartEngine(container).addNewChart(container, xlsxChart.getSheet(), protoChart);
            } catch (Exception e) {
                XLSXException xlsxe = new XLSXException(e);
                xlsxe.setFeature(XLSXException.FEATURE.CHART);
                xlsxe.setIsFeatureLost(true);
                Sheet sheet = xlsxChart.getSheet();
                String sheetName = null;
                if (sheet != null) {
                    sheetName = sheet.getName();
                }
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.SHEET_NAME, sheetName));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.UNKNOWN);
                xlsxe.setCause(e.getMessage());
                xlsxe.setLevel(Level.SEVERE);
                xlsxe.log(LOGGER);

            }

        }
    }

    private void initSheetNameMap() throws XLSXException {
        Sheet[] sheets = workbook.getSheets();

        for (Sheet sheet : sheets) {
            sheetNameMap.put(sheet.getName(), sheet.getName());
        }
//        try {
        modifySheetNamesWithSpecilaChar(sheetNameMap);

    }

    private void updateSheetNames(String docId, String docOwner) {
        Sheet[] sheets = workbook.getSheets();

        for (Sheet sheet : sheets) {
            String oldName = sheet.getName();
            String newName = sheetNameMap.get(oldName);
            if (!oldName.equals(newName)) {

                if (oldName.equals(workbook.getActiveSheetName())) {
                    workbook.setActiveSheetName(newName);
                }

                sheet.rename(newName);
                DocumentUtils.renameSheetInDB(docId, docOwner, oldName, newName);
                if(XLSXParserAgent.VERBOSE_LOG) {
                    LOGGER.log(Level.OFF, "[XLSX-PARSER] sheetRename old:{0} new:{1}", new Object[]{oldName, sheet.getName()});
                }
            }
        }
    }

    public static void modifySheetNamesWithSpecilaChar(Map<String, String> newSheetNameForOldSheetName) throws XLSXException {
        String[] oldSheetNames = newSheetNameForOldSheetName.keySet().toArray(new String[newSheetNameForOldSheetName.size()]);
        try {
            String[] newSheetNames = ImportExportUtil.replaceSpecialCharactersinSheetNames(oldSheetNames);
            for (int i = 0; i < oldSheetNames.length; i++) {
                String oldName = oldSheetNames[i];
                String newName = newSheetNames[i];
                if (!oldName.equals(newName)) {
                    newSheetNameForOldSheetName.put(oldName, newName);
                }
            }
        } catch (Exception ex) {
            XLSXException xlsxe = new XLSXException();
            xlsxe.initCause(ex);
            throw xlsxe;
        }
    }

    private void addXlsxImagesToWorkbookContainer(List<XLSXImage> xLSXImages, WorkbookContainer container, Map<String, String> existingXmlImagePathToUrlMap) throws Exception {
        long time = System.currentTimeMillis();
        String resourceKey = container.getResourceKey();

        try {
            convertUnsupportedImages(xLSXImages);
        } catch (Exception e) {
            XLSXException ex = new XLSXException(e);
            ex.setFeature(XLSXException.FEATURE.IMAGE);
            ex.setCause("converseion unsuccessful");
            ex.setLevel(Level.SEVERE);
            ex.log(LOGGER);
        }

        if(XLSXParserAgent.VERBOSE_LOG) {
            LOGGER.log(Level.OFF, "[XLSX-PARSER-UTILITY-TIME-STATS] converting emf images :{0}ms, resource-key:{1}", new Object[]{(System.currentTimeMillis() - time), resourceKey});
        }

        ImageBook imageBook = new ImageBook();
        if (!xLSXImages.isEmpty()) {
            String docId = container.getDocId();
            String docOwner = container.getDocOwner();
            String ownerZUID = container.getDocOwnerZUID();
            Boolean isRemoteView = container.isRemoteMode();//(workbookContainer.getResourceId() == null);
            if (isRemoteView) {    // remote view // isRemoteMode not login  // + isAuthRemote check needed ? verify with imagedisplayactio
                if (RemoteUtils.isRemoteDocument(docOwner)) {
                    ownerZUID = docOwner;
                }
            }
                writeImagesInParallel(xLSXImages, docId, docOwner, ownerZUID, imageBook, existingXmlImagePathToUrlMap);
        }
//        container.setImageBook(imageBook);
        imageBook.setIsReadyToRender(true);
        imageBook.setIsImagesChanged(false);

        this.imagesJSONArray = ImageUtils.getImagesJSONArray(imageBook);
    }

    private void writeImagesInParallel(List<XLSXImage> xLSXImages, String docId, String docOwner, String ownerZUID, ImageBook imageBook, Map<String, String> existingXmlImagePathToUrlMap) {
        List<XLSXImage> xlsxImagesToBeWrittenToDFS = new ArrayList<>();
        {
            Set<XMLFile> xmlImageFileToBeWrittenToDFS = new HashSet<>();
            for (XLSXImage xLSXImage : xLSXImages) {
                XMLFile xmlImageFile = xLSXImage.getXmlf();
                if (!xmlImageFileToBeWrittenToDFS.contains(xmlImageFile)) {
                    xmlImageFileToBeWrittenToDFS.add(xmlImageFile);
                    xlsxImagesToBeWrittenToDFS.add(xLSXImage);
                }
            }
        }

        Map<XMLFile, String> urlOfxmlImageFile = new ConcurrentHashMap<>();
        {
            if(xlsxImagesToBeWrittenToDFS.size() >= EngineConstants.MIN_IMAGES_TO_WRITE_IN_PARALLEL_FOR_XLSX_PARSER) {
                if(XLSXParserAgent.VERBOSE_LOG) {
                    LOGGER.log(Level.OFF, "[XLSX-PARSER-UTILITY-TIME-STATS] writeImageToSheetImagesDFSStore in Parallel");
                }
                ExecutorService executorService = Executors.newFixedThreadPool(Math.min(xlsxImagesToBeWrittenToDFS.size(), EngineConstants.IMAGES_WRITE_THREADS_FOR_XLSX_PARSER));
                try {
                    for (XLSXImage xLSXImage : xlsxImagesToBeWrittenToDFS) {
                        Runnable runnable = () -> writeImageToSheetImagesDFSStore(docId, docOwner, ownerZUID, xLSXImage, urlOfxmlImageFile, existingXmlImagePathToUrlMap);
                        executorService.execute(runnable);
                    }

                    executorService.shutdown();
                    executorService.awaitTermination(EngineConstants.IMAGES_WRITE_THREADS_AWAIT_TIME_IN_SEC_FOR_XLSX_PARSER, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    if(XLSXParserAgent.VERBOSE_LOG) {
                        LOGGER.log(Level.OFF, "[XLSX-PARSER-UTILITY-TIME-STATS] writeImageToSheetImagesDFSStore in Parallel, exception", e);
                    }
                } finally {
                    executorService.shutdownNow();
                }
            } else {
                for (XLSXImage xLSXImage : xlsxImagesToBeWrittenToDFS) {
                    writeImageToSheetImagesDFSStore(docId, docOwner, ownerZUID, xLSXImage, urlOfxmlImageFile, existingXmlImagePathToUrlMap);
                }
            }
        }

        Map<String, Integer> urlImageIdMap = new HashMap<>();
        for (XLSXImage xLSXImage : xLSXImages) {
            String url = urlOfxmlImageFile.get(xLSXImage.getXmlf());
            if (url != null) {
                int imageId = addImage(imageBook, xLSXImage.getSheet(), xLSXImage, url, urlImageIdMap.get(url));
                urlImageIdMap.put(url, imageId);
            }
        }
    }

    private void writeImageToSheetImagesDFSStore(String docId, String docOwner, String ownerZUID, XLSXImage xLSXImage, Map<XMLFile, String> urlOfxmlImageFile, Map<String, String> existingXmlImagePathToUrlMap) {
        String imageExtn = xLSXImage.getImageExtension();
        try {
            XMLFile xmlImageFile = xLSXImage.getXmlf();
            byte[] bytes = xmlImageFile.getBytes();
            long time = System.currentTimeMillis();
            String url;
            url = existingXmlImagePathToUrlMap.get(xmlImageFile.getFilePath());
            if(url == null) {
                url = "&u_n=" + ImageUtils.writeImageToSheetImagesDFSStore(docId, docOwner, ownerZUID, null, bytes, imageExtn);//NO I18N
                existingXmlImagePathToUrlMap.put(xmlImageFile.getFilePath(), url);
            }
            urlOfxmlImageFile.put(xmlImageFile, url);
            if(XLSXParserAgent.VERBOSE_LOG) {
                LOGGER.log(Level.OFF, "[XLSX-PARSER-UTILITY-TIME-STATS] writeImageToSheetImagesDFSStore :{0}ms, image-name:{1}", new Object[]{(System.currentTimeMillis() - time), xLSXImage.getAssociativeImageName()});
            }
        } catch (Exception e) {
            XLSXException xlsxe = new XLSXException(e);
            xlsxe.setFeature(XLSXException.FEATURE.IMAGE);
            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.NAME, xLSXImage.getAssociativeImageName()));
            xlsxe.setIsFeatureLost(true);
            xlsxe.log(LOGGER);

        }
    }

    private void convertUnsupportedImages(List<XLSXImage> xlsxImages) {
        List<XLSXImage> unSupportedXlsxImages = new ArrayList<>();
        List<LiboImageConverter.LiboImageConverterHelper> unsupportedImages = new ArrayList<>();
        Set<XMLFile> xMLFiles = new HashSet<>();
        for (XLSXImage xlsxi : xlsxImages) {
            try {
                if (".emf".equals(xlsxi.getImageExtension()) || ".tiff".equals(xlsxi.getImageExtension())) {
                    XMLFile xmlImageFile = xlsxi.getXmlf();
                    /**
                     * copied-images should not be converted-twice.
                     * toDo:Height/width given to LiboImageConverter is of the
                     * first image, it should be more appropriate.
                     */
                    if (!xMLFiles.contains(xmlImageFile)) {
                        xMLFiles.add(xmlImageFile);

                        LiboImageConverter.LiboImageConverterHelper image = new LiboImageConverter.LiboImageConverterHelper();
                        image.setExtension(xlsxi.getImageExtension());
                        image.setBytes(xmlImageFile.getBytes());
                        image.setName(xlsxi.getAssociativeImageName());

                        XLSXDrawingProperties xlsxdp = xlsxi.getxLSXDrawingProperties();
                        int height = ImageUtils.setActualValue((int) xlsxdp.getHeight(), "height");
                        int width = ImageUtils.setActualValue((int) xlsxdp.getHeight(), "width");

                        image.setHeight(height);
                        image.setWidth(width);

                        unSupportedXlsxImages.add(xlsxi);
                        unsupportedImages.add(image);
                    }
                }
            } catch (Exception e) {
                XLSXException xlsxe = new XLSXException(e);
                xlsxe.setFeature(XLSXException.FEATURE.IMAGE);
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.NAME, xlsxi.getAssociativeImageName()));
                xlsxe.setIsFeatureLost(true);
                xlsxe.log(LOGGER);

            }
        }

        try {
            if (!unsupportedImages.isEmpty()) {
            	LOGGER.info("TODO:Conversion handle this case XLSXParserAgent convertUnsupportedImages:");
//            	ConnectionObject connectionObject = ConnectionPool.getRemoteDesktop();
//                LiboImageConverter liboImageConverter = new LiboImageConverter(connectionObject, EngineConstants.EMPTYODSFILEPATH);
//
//                long time = System.currentTimeMillis();
////            int noOfUnsupportedImages = unSupportedImages.size();
//                liboImageConverter.convertImages(unsupportedImages);
            }
//            LOGGER.log(Level.OFF,"[XLSX][IMAGE-CONVERSION][XLSX-TIME-STAMP] :{0}ms , no-of-images-converted:{1}", new Object[]{(System.currentTimeMillis()-time), noOfUnsupportedImages});
        } catch (Exception e) {
            XLSXException xlsxe = new XLSXException(e);
            xlsxe.setFeature(XLSXException.FEATURE.IMAGE);
            xlsxe.setIsFeatureLost(true);
            xlsxe.log(LOGGER);
        }

        for (int i = 0; i < unsupportedImages.size(); i++) {
            LiboImageConverter.LiboImageConverterHelper image = unsupportedImages.get(i);
            XLSXImage xlsxImage = unSupportedXlsxImages.get(i);
            xlsxImage.getXmlf().setBytes((image.getBytes()));
            xlsxImage.setImageExtension(image.getExtension());
        }

    }

    private int addImage(ImageBook imageBook, Sheet sheet, XLSXImage xlsxi, String url, Integer imageID)
    {
        double height = -1;
        double width = -1;
        int rowIndex = -1;
        int columnIndex = -1;
        double rowDiff = 0;
        double columnDiff = 0;

        String name = xlsxi.getAssociativeImageName();

        XLSXDrawingProperties xlsxdp = xlsxi.getxLSXDrawingProperties();

        if(xlsxdp != null) {
            height = xlsxdp.getHeight();
            width = xlsxdp.getWidth();
            rowIndex = (int) xlsxdp.getRow();
            columnIndex = (int) xlsxdp.getColumn();
            rowDiff = (double) xlsxdp.getRowDiff();
            columnDiff = (double) xlsxdp.getColumnDiff();
        }


        if(imageID == null) {
            Image image = new Image(name, url, height, width);
            imageID = imageBook.addImageObject(image);
        }

        if(xlsxi.getCells().isEmpty()) {
            sheet.addSheetImage(imageID, height, width, rowIndex, columnIndex, rowDiff, columnDiff);
        } else {
            for(Cell cell : xlsxi.getCells()) {
                if(cell.isFormula()) {
                    ((CellImpl) cell).setValueFromParser(new ExpressionValue(cell.getExpression(), new ImageValue(ImageDisplayMode.FIT, imageID)));

                } else {
                    ((CellImpl) cell).setValueFromParser(new ImageValue(ImageDisplayMode.FIT, imageID));
                }
            }
        }

        return imageID;
    }

    public void addProtectionToRangesAndSheet(Protection protection) {
        List<XLSXProtectionBean> xlsxProtectionBeans = this.xlsxp.getXlsxProtectionBeans();
        for(XLSXProtectionBean xlsxProtectionBean : xlsxProtectionBeans) {
            if(xlsxProtectionBean.isEntireSheetLocked()) {
                xlsxProtectionBean.getSheet().lock(protection);
            } else {
                for(Range range: xlsxProtectionBean.getProtectedRanges()) {
                    List<DataRange> ranges = range.getSheet().getProtectionRangeMap().get(protection);

                    if(ranges == null)
                    {
                        ranges = new ArrayList<>();
                        range.getSheet().getProtectionRangeMap().put(protection, ranges);
                    }
                    ranges.add(range.toDataRange());
                }
            }
        }
    }

    public XMLFile getThumbnail() {
        return this.thumbnail;
    }
    public static void putToFEATURES_NOT_PARSED(XLSXException.FEATURE feature, XLSXException.CAUSETYPE causetype) {
        Map<XLSXException.FEATURE, XLSXException.CAUSETYPE> featurecausetypeMap = FEATURES_NOT_PARSED.get();
        if(featurecausetypeMap == null) {
            LOGGER.log(Level.OFF, "ThreadLocal not set");
            return;
        }
        featurecausetypeMap.put(feature, causetype);
    }

    public JSONArrayWrapper getImagesJSONArray() {
        return imagesJSONArray;
    }

    private static void setDefaultLocaleAndCalculationSettingsForXLSX(Workbook workBook) {
        WorkbookAdditionalInfo additionalInfo = WorkbookAdditionalInfo.getInstance(EngineConstants.DEFAULT_LOCALE);
        workBook.setAccountsLocale(EngineConstants.DEFAULT_LOCALE);
        workBook.setUserTimezone(additionalInfo.getTimeZone().getID());
        workBook.setSpreadsheetSettings(additionalInfo.getSpreadsheetSettings());
        workBook.setView(additionalInfo.getView());
        workBook.setDictionaryLocale(additionalInfo.getDictionaryLocale());
        if(additionalInfo.isIterativeCalc())
        {
            workBook.setCalculationSettings(additionalInfo.getCalculationSettings());
        }
        workBook.setPatternSetting(additionalInfo.getPatternSetting());
    }
}
