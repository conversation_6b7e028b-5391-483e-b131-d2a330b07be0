//$Id$
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.TableStyle;

public class XLSXTableStyleElementBean {
    private String dxfId = XLSXConstants.DEFAULT_TABLES_STYLE+"0";
    private int size = 1;
    private TableStyle.TableStylePropertyKey tableStyleElementKey;
    private CellStyle cellStyle;

    public TableStyle.TableStylePropertyKey getTableStyleElementKey() {
        return tableStyleElementKey;
    }

    public String getDxfId() {
        return dxfId;
    }

    public void setDxfId(String dxfId) {
        this.dxfId = XLSXConstants.CELLSTYLE_DXF_PREFIX+dxfId;
    }

    public void setType(TableStyle.TableStylePropertyKey type) {
        this.tableStyleElementKey = type;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public CellStyle getCellStyle() {
        return cellStyle;
    }

    public void setCellStyle(CellStyle cellStyle) {
        this.cellStyle = cellStyle;
    }
}
