package com.adventnet.zoho.websheet.model.xlsxaparser_;

public class SlicerDefinition extends SlicerBodyDefinition{
    private final int columns;
    private final int rowHeight;
    private final boolean showCaption;
    private final int startItem;

    SlicerDefinition(String name, String cacheName, String caption, int columns, String style, int rowHeight, boolean showCaption, int startItem)
    {
        super(name, cacheName, caption, style);
        this.columns = columns;
        this.rowHeight = rowHeight;
        this.showCaption = showCaption;
        this.startItem = startItem;
    }

    public int getColumns() {
        return columns;
    }

    public int getRowHeight() {
        return rowHeight;
    }

    public boolean isShowCaption() {
        return showCaption;
    }

    public int getStartItem() {
        return startItem;
    }
}
