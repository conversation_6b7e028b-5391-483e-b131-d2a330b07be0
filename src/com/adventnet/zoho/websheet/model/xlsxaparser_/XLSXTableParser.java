//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.style.TableStyle;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.adventnet.zoho.websheet.model.util.TableUtil;
import org.xmlpull.v1.XmlPullParser;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
class XLSXTableParser extends XMLFileParser implements XMLParser {
    
    private final XLSXTransformer xlsxt;

    private XLSXTableRepo repo;
    private Sheet sheet;
    private TableStyle emptyStyle;

    private String tableHeader;
    private final List<TableColumn> tableColumns = new ArrayList<>();

    private boolean includeHeaderRows = true;
    private boolean includeFooterRows = false;
    private String ref;
    private Table table;
    private String tableColumnHeader;
    private String columnStyleName;
    private String columnHeaderStyleName;
    private String columnFooterStyleName;
    private Expression tableColumnCalculatedExpression;
    private String tableColumnFooterLabel;
    private Expression tableColumnFooterExpression;

    private String styleName;
    private String showFirstColumn;
    private String showLastColumn;
    private String showRowStripes;
    private String showColumnStripes;

    private String headerStyleName;
    private ZSPattern headerPattern;
    private String footerStyleName;
    private ZSPattern footerPattern;
    private String tableDataStyleName;
    private ZSPattern tableDataPattern;

    XLSXTableParser(XMLFile xLSXFile, XLSXTransformer xlsxt, XMLPullParserWrapper xpp, List<XLSXException> xlsxException) {
        super(xLSXFile, xpp, xlsxException);
        this.xlsxt = xlsxt;
    }

    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                switch (nodeName) {
                    case ElementNameConstants.TABLECOLUMN:
                        parseTableColumnEndNode();
                        break;
                }
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.TABLE:
                        parseTableNode();
                        break;
                    case ElementNameConstants.TABLECOLUMNS:
                        parseTableColumnsNode();
                        break;
                    case ElementNameConstants.TABLECOLUMN:
                        parseTableColumnNode();
                        break;
                    case ElementNameConstants.CALCULATED_COLUMN_FORMULA:
                        parseCalculatedColumnFormulaNode();
                        break;
                    case ElementNameConstants.TABLE_TOTALS_ROW_FORMULA:
                        parseTableTotalsRowFormulaNode();
                        break;
                    case ElementNameConstants.TABLE_STYLEINFO:
                        parseTableStyleInfoNode();
                        break;
                    case ElementNameConstants.AUTOFILTER:
                        parseAutoFilterNode();
                        break;
                }
                break;
        }
    }

    @Override
    public void beforeParse() throws XLSXException {
        repo = (XLSXTableRepo) this.xmlFile.getXlsxRepo();
        this.sheet = repo.getSheet();
    }

    @Override
    public void afterParse() throws XLSXException {
        xlsxt.addXlsxFileOfTableName(repo.getName(), xmlFile);

        try{
            creatTable();
        } catch(XlsxLimitException e) {
            XLSXParserAgent.putToFEATURES_NOT_PARSED(XLSXException.FEATURE.TABLE, XLSXException.CAUSETYPE.INVALID_RANGE);
            if(XLSXParserAgent.VERBOSE_LOG) {
                logger.log(Level.OFF, "", e);
            }
        }

//        XLSXException xlsxe = new XLSXException();
////        this.xlsxException.add(xlsxe);
//        xlsxe.setFeature(XLSXException.FEATURE.TABLE);
//        xlsxe.setIsFeatureLost(true);
//        xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.NAME, repo.getName()));
//        xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.XMLFile, this.xmlFile.getFilePath()));
//        if (this.sheet != null) {
//            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.SHEET_NAME, this.sheet.getName()));
//        }
//        xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.REF, repo.getRef()));
//        xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
//        xlsxe.setLevel(Level.INFO);
    }

    private void parseTableNode() throws XLSXException {
        String name = xpp.getAttribute(AttributeNameConstants.NAME);
        ref = xpp.getAttribute(AttributeNameConstants.REF);


        repo.setRef(ref);

        String tableType = xpp.getAttribute(AttributeNameConstants.TABLE_TYPE); // queryTable, worksheet(default), xml
        if (tableType != null && !"worksheet".equals(tableType)) {
            XLSXException xlsxe = new XLSXException();
            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.NAME, name));
            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.REF, ref));
            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.XMLFile, xmlFile.getFilePath()));
            if (this.sheet != null) {
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.SHEET_NAME, this.sheet.getName()));
            }
            xlsxe.setFeature(XLSXException.FEATURE.TABLE);
            xlsxe.setIsFeatureLost(true);
            throw xlsxe;
        }

        String headerRowCount = xpp.getAttribute(AttributeNameConstants.HEADER_ROW_COUNT);
        if (XLSXParserUtility.isEqualsFalse(headerRowCount)) {
            includeHeaderRows = false;
            repo.setIncludeHeaderRows(false);
        }

        String displayName = xpp.getAttribute(AttributeNameConstants.DISPLAY_NAME);

        String totalsRowCount = xpp.getAttribute(AttributeNameConstants.TOTALS_ROW_COUNT);
        if (totalsRowCount != null && Integer.parseInt(totalsRowCount) > 0) {
            includeFooterRows = true;
        }

        String headerRowCellStyle = xpp.getAttribute(AttributeNameConstants.HEADER_ROW_CELLSTYLE);
        String headerDxfId = xpp.getAttribute(AttributeNameConstants.HEADERDXFID);
        this.headerStyleName = headerDxfId != null ? (XLSXConstants.CELLSTYLE_DXF_PREFIX + headerDxfId) : headerRowCellStyle;

        if(this.headerStyleName != null) {
            XlsxParserStyleContainer styleContainer = xlsxt.getXlsxParserStyleContainer();
            Map<String, ZSPattern> patternMap = styleContainer.getPatterns();
            this.headerPattern = patternMap.get(this.headerStyleName);
        }

        String totalRowDxfId = xpp.getAttribute(AttributeNameConstants.FOOTERDXFID);
        String totalRowCellStyle = xpp.getAttribute(AttributeNameConstants.FOOTER_CELLSTYLE);
        this.footerStyleName = totalRowDxfId != null ? (XLSXConstants.CELLSTYLE_DXF_PREFIX + totalRowDxfId) : totalRowCellStyle;
        if(footerStyleName != null) {
            XlsxParserStyleContainer styleContainer = xlsxt.getXlsxParserStyleContainer();
            Map<String, ZSPattern> patternMap = styleContainer.getPatterns();
            this.footerPattern = patternMap.get(this.footerStyleName);
        }

        String tableDataDxfId = xpp.getAttribute(AttributeNameConstants.DATADXFID);
        String tableDataCellStyle = xpp.getAttribute(AttributeNameConstants.DATACELLSTYLE);
        this.tableDataStyleName = tableDataDxfId != null ? (XLSXConstants.CELLSTYLE_DXF_PREFIX + tableDataDxfId) : tableDataCellStyle;
        if(this.tableDataStyleName != null) {
            XlsxParserStyleContainer styleContainer = xlsxt.getXlsxParserStyleContainer();
            Map<String, ZSPattern> patternMap = styleContainer.getPatterns();
            this.tableDataPattern = patternMap.get(this.tableDataStyleName);
        }

        tableHeader = displayName;
        repo.setName(this.tableHeader);
//        String id;
//        String comment;
//        String insertRow;
//        String insertRowShift;
//        String published;
//        String headerRowBorderDxfId;
//        String tableBorderDxfId;
//        String totalsRowBorderDxfId;
//        String connectionId;
    }

    private void parseAutoFilterNode() {
        AutoFilterElementParser afep = new AutoFilterElementParser(xpp, this.sheet, xlsxException, this.xlsxt.getXlsxParserStyleContainer(), true, repo.getHiddenRowsBitSet());
        XMLParserAgent xmlpa = new XMLParserAgent(xpp, afep);
        xmlpa.addNameSpaceToDiscard(XLSXNamespace.X);
        try {
            xmlpa.parseNodeTree(true);
            if(afep.getTable() != null) {
                this.table = afep.getTable();
            }
        } catch (XLSXException ex) {
            this.xlsxException.add(ex);
        }
    }

    private void parseTableColumnsNode() {
//        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    private void parseTableColumnNode() {
//        String id = xpp.getAttribute(AttributeNameConstants.ID);
//        String uniqueName;
//
//        String queryTableFieldId;

//        int intId = Integer.valueOf(id) - 1; // zoho uses 0 based index, excel uses 1 based index
//        TableColumnId tableColumnId = new TableColumnId(intId);
        String name = xpp.getAttribute(AttributeNameConstants.NAME);
        name = XMLPullParserWrapper.decodeUnSupportedXMLCharacters(name);
        String styleDfxId = xpp.getAttribute(AttributeNameConstants.DATADXFID);
        String dataCellStyle = xpp.getAttribute(AttributeNameConstants.DATACELLSTYLE);
        String headerDxfId = xpp.getAttribute(AttributeNameConstants.HEADERDXFID);
        String headerCellStyle = xpp.getAttribute(AttributeNameConstants.HEADER_ROW_CELLSTYLE);
        String footerDxfId = xpp.getAttribute(AttributeNameConstants.FOOTERDXFID);
        String footerCellStyle = xpp.getAttribute(AttributeNameConstants.FOOTER_CELLSTYLE);

        if(dataCellStyle != null) {
            this.columnStyleName = dataCellStyle;
        }

        if(styleDfxId != null) {
            this.columnStyleName = XLSXConstants.CELLSTYLE_DXF_PREFIX + styleDfxId;
        }

        this.columnHeaderStyleName = headerDxfId != null ? (XLSXConstants.CELLSTYLE_DXF_PREFIX + headerDxfId) : headerCellStyle;

        this.columnFooterStyleName = footerDxfId != null ? (XLSXConstants.CELLSTYLE_DXF_PREFIX + footerDxfId) : footerCellStyle;

        this.tableColumnHeader = name;

        String totalsRowFunction = xpp.getAttribute(AttributeNameConstants.TOTALS_ROW_FUNCTION);
        String totalsRowLabel = xpp.getAttribute(AttributeNameConstants.TOTALS_ROW_LABEL);

        if (totalsRowLabel != null) {
            tableColumnFooterLabel = totalsRowLabel;
        }

        if (totalsRowFunction != null) {
            if (!totalsRowFunction.equals("custom")) {
                int functionNumber = 109;
                switch(totalsRowFunction)
                {
                    case "average": functionNumber = 101;break;
                    case "count": functionNumber = 102;break;
                    case "countNums": functionNumber = 103;break;
                    case "max": functionNumber = 104;break;
                    case "min": functionNumber = 105;break;
                    case "stdDev": functionNumber = 108;break;
                    case "sum": functionNumber = 109;break;
                    case "var": functionNumber = 110;break;
                    case "none": functionNumber = 109;break;
                }
                String formula = "=SUBTOTAL("+functionNumber+";[" + name + "])"; //No I18N
                this.tableColumnFooterExpression = new ExpressionImpl(this.sheet.getWorkbook(), formula, 0, 0, false, CellReference.ReferenceMode.A1);
            }
        }

    }

    private void parseTableColumnEndNode() {
        TableColumn tableColumn = new TableColumn();
        tableColumn.setColumnHeader(this.tableColumnHeader);
        tableColumn.setStyleName(this.columnStyleName);
        tableColumn.setHeaderStyleName(this.columnHeaderStyleName);
        tableColumn.setFooterStyleName(this.columnFooterStyleName);
        tableColumn.setFooterLabel(this.tableColumnFooterLabel);
        tableColumn.setFooterExpression(this.tableColumnFooterExpression);
        tableColumn.setCalculatedExpression(this.tableColumnCalculatedExpression);
        tableColumns.add(tableColumn);

        this.tableColumnHeader = null;
        this.columnStyleName = null;
        this.columnHeaderStyleName = null;
        this.columnFooterStyleName = null;
        this.tableColumnCalculatedExpression = null;
        this.tableColumnFooterLabel = null;
        this.tableColumnFooterExpression = null;
    }

    private void parseCalculatedColumnFormulaNode() throws XLSXException {
        //to-do: initializing CalculatedColumnFormula ('totalRowFunction') of tableColumnFormula
        //this.tableColumnFormula = new TableColumnFormula();
        String expressionString = this.xpp.getTextInsideElement();
        int[] range = XLSXParserUtility.getBoundedIndexes(ref);
        int startRow = range[0];
        int startCol = range[1];
        int rowIndex = startRow + (this.includeHeaderRows ? 1 : 0);
        int colIndex = startCol + tableColumns.size(); //Current table column is added to tableColumns list only after completing end node
        this.tableColumnCalculatedExpression = new ExpressionImpl(this.sheet.getWorkbook(), expressionString, rowIndex, colIndex, false, CellReference.ReferenceMode.A1);
    }

    private void parseTableStyleInfoNode() {
        this.styleName = xpp.getAttribute(AttributeNameConstants.NAME);
        this.showFirstColumn = xpp.getAttribute(AttributeNameConstants.SHOW_FIRST_COLUMN);
        this.showLastColumn = xpp.getAttribute(AttributeNameConstants.SHOW_LAST_COLUMN);
        this.showRowStripes = xpp.getAttribute(AttributeNameConstants.SHOW_ROW_STRIPES);
        this.showColumnStripes = xpp.getAttribute(AttributeNameConstants.SHOW_COLUMN_STRIPES);
    }

    private void parseTableTotalsRowFormulaNode() throws XLSXException {
        String array;
        String expressionString = this.xpp.getTextInsideElement();
        this.tableColumnFooterExpression = new ExpressionImpl(this.sheet.getWorkbook(), expressionString, 0, 0, false, CellReference.ReferenceMode.A1);
    }

    private Boolean maskNullToFalse(String value) {
        if (value == null || XLSXParserUtility.isEqualsFalse(value)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private TableStyle getEmptyTableStyle() {
        if(this.emptyStyle == null) {
            this.emptyStyle = TableUtil.checkAndAddStyle(sheet.getWorkbook(), new TableStyle(sheet.getWorkbook().getUniqueTableStyleName(EngineConstants.EMPTY_TABLESTYLE), false));
        }
        return this.emptyStyle;
    }

    private void creatTable() throws XLSXException {
        //todo : tableStyleInfo initialization in Table Object
        if(table == null) {
            int[] range = XLSXParserUtility.getBoundedIndexes(ref);
            int startRow = range[0];
            int startCol = range[1];
            int endRow = range[2];
            int id = xlsxt.getWorkbook().registerAndGetNewTableID();
            String styleName = this.styleName == null ? getEmptyTableStyle().getName() : this.styleName;
            this.table = new Table(id, this.tableHeader, startRow, endRow, startCol, this.includeHeaderRows, this.includeFooterRows, this.tableColumns, styleName, true, true, null, null);
            this.sheet.addTable(this.table);
        }
        else {
            if(this.table.getName() != null) {
                this.table.getSheet().removeTable(this.table.getName(), false);
            }
            int[] range = XLSXParserUtility.getBoundedIndexes(ref);
            this.table.changeRange(new Range(this.sheet, new CellReference(this.sheet.getCell(range[0], range[1]), false, false), new CellReference(this.sheet.getCell(range[2], range[3]), false, false)));
            if(this.tableColumns != null) {
                for(int i = 0; i < this.tableColumns.size(); i++) {
                    this.table.addColumn(i, this.tableColumns.get(i));
                }
                int size = this.table.getTableColumns().size();
                for(int i = this.tableColumns.size(); i < size; i++) {
                    this.table.deleteColumn(this.table.getTableColumns().size()-1);
                }
            }
            this.table.setName(this.tableHeader);
            this.sheet.addTableFromParser(this.table);
            this.table.setHeaderRowShown(this.includeHeaderRows);
            this.table.setFooterRowShown(this.includeFooterRows);
            String styleName = this.styleName == null ? getEmptyTableStyle().getName() : this.styleName;
            this.table.setTableStyleName(styleName, false);
        }
        this.table.setShowFirstColumn(maskNullToFalse(this.showFirstColumn));
        this.table.setShowLastColumn(maskNullToFalse(this.showLastColumn));
        this.table.setShowRowStripes(maskNullToFalse(this.showRowStripes));
        this.table.setShowColumnStripes(maskNullToFalse(this.showColumnStripes));
        this.table.setHeaderStyleName(this.headerStyleName);
        this.table.setDataStyleName(this.tableDataStyleName);
        this.table.setFooterStyleName(this.footerStyleName);
    }
}
