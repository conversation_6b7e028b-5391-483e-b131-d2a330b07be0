//$Id$
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import org.xmlpull.v1.XmlPullParser;

import java.util.ArrayList;
import java.util.List;

public class XlsxColorsElementParser extends XMLElementParser implements XMLParser {

    private List<String> colors = new ArrayList<>();

    XlsxColorsElementParser(XMLPullParserWrapper xpp, List<XLSXException> xLSXExceptions) {
        super(xpp, xLSXExceptions);
    }

    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.RGBCOLOR:
                        parseRgbColorElement();
                        break;
                }
                break;
        }

    }

    private void parseRgbColorElement() {
        String rgb = xpp.getAttribute(AttributeNameConstants.RGB);
        colors.add(rgb.substring(2));
    }

    @Override
    public void beforeParse() throws XLSXException {

    }

    @Override
    public void afterParse() throws XLSXException {

    }

    public List<String> getColors() {
        return colors;
    }
}
