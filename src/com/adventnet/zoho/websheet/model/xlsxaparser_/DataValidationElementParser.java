//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.DVErrorMessage;
import com.adventnet.zoho.websheet.model.DVHelpMessage;
import com.adventnet.zoho.websheet.model.ErrorCode;
import java.util.List;
import org.xmlpull.v1.XmlPullParser;

/**
 *
 * <AUTHOR>
 */
class DataValidationElementParser extends XMLElementParser implements XMLParser{
    
    private DVHelpMessage dVHelpMessage = null;
    private DVErrorMessage dVErrorMessage = null;
    private String formulaOne = null;
    private String formulaTwo = null;
    private String type;
    private String operator;
    private String sqRef;
    private String condition;
    private boolean isAllowEmptyCell = true;
    
    DataValidationElementParser(XMLPullParserWrapper xpp, List<XLSXException> xlsxException) {
        super(xpp, xlsxException);
    }

    @Override
    public void parseNode(String nodeName) throws XLSXException {
        switch (xpp.getEventType()) {
            case XmlPullParser.END_TAG:
                switch (nodeName) {
                    case ElementNameConstants.DATAVALIDATION:
                        parseDataValidationEndNode();
                        break;
                }
                break;
            case XmlPullParser.START_TAG:
                switch (nodeName) {
                    case ElementNameConstants.DATAVALIDATION:
                        parseDataValidationNode();
                        break;
                    case ElementNameConstants.FORMULA1:
                        parseFormula1Node();
                        break;
                    case ElementNameConstants.FORMULA2:
                        parseFormula2Node();
                        break;
                    case ElementNameConstants.XM_SQREF:
                        parseSqrfNode();
                        break;
                    
                }
                break;
        }
    }
    
    private void parseDataValidationNode() {

        String promptTitle = xpp.getAttribute(AttributeNameConstants.PROMPTTITLE);
        String prompt = xpp.getAttribute(AttributeNameConstants.PROMPT);

        String errorStyle = xpp.getAttribute(AttributeNameConstants.ERRORSTYLE);
        String errorTitle = xpp.getAttribute(AttributeNameConstants.ERRORTITLE);
        String error = xpp.getAttribute(AttributeNameConstants.ERROR);

        if (promptTitle != null || prompt != null) {
            if(promptTitle == null) {
                promptTitle = "";
            }
            if(prompt == null) {
                prompt = "";
            }
            dVHelpMessage = new DVHelpMessage(true, promptTitle, prompt);
        }

        if (errorStyle == null) {
            errorStyle = "stop";//No I18N
        }
        
        ErrorCode.MsgType msgType = ErrorCode.MsgType.valueOf(errorStyle.toUpperCase());
        if (error == null) {
            error = "The value entered violates data validation rules set in cell";//No I18N
        }

        if(errorTitle == null) {
            errorTitle = "The value you entered is not valid.";//No I18N
        }

        dVErrorMessage = new DVErrorMessage(msgType, true, errorTitle, error);

        String allowBlank = xpp.getAttribute(AttributeNameConstants.ALLOWBLANK);
        this.isAllowEmptyCell = !XLSXParserUtility.isEqualsFalse(allowBlank);
        

        this.type = xpp.getAttribute(AttributeNameConstants.TYPE);
        this.operator = xpp.getAttribute(AttributeNameConstants.OPERATOR);
        this.sqRef = xpp.getAttribute(AttributeNameConstants.SQREF);

        if (operator == null) {
            operator = "between";//No I18N
        }


    }

    private void parseDataValidationEndNode() throws XLSXException {
        if(type != null) {
            try {
                this.condition = OdsEquivalent.eqlConditionStringForDataValidation(type, operator, formulaOne, formulaTwo);
            } catch (XLSXException e) {
                e.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.TYPE, type));
                e.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_ONE, formulaOne));
                e.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.FORMULA_TWO, formulaTwo));
                e.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.REF, sqRef));
                throw e;
            }
            
        }
    }

    private void parseFormula1Node() throws XLSXException {
            this.formulaOne = xpp.getTextInsideElement();
    }

    private void parseFormula2Node() throws XLSXException {
        this.formulaTwo = xpp.getTextInsideElement();
    }

    /**
     * @return the dVHelpMessage
     */
    public DVHelpMessage getdVHelpMessage() {
        return dVHelpMessage;
    }

    /**
     * @return the dVErrorMessage
     */
    public DVErrorMessage getdVErrorMessage() {
        return dVErrorMessage;
    }

    /**
     * @return the sqRef
     */
    public String getSqRef() {
        return sqRef;
    }

    /**
     * @return the condition
     */
    public String getCondition() {
        return condition;
    }

    /**
     * @return the isAllowEmptyCell
     */
    public boolean isIsAllowEmptyCell() {
        return isAllowEmptyCell;
    }

    private void parseSqrfNode() throws XLSXException {
        this.sqRef = xpp.getTextInsideElement();
    }

    @Override
    public void beforeParse() throws XLSXException {
    }

    @Override
    public void afterParse() {
    }
}
