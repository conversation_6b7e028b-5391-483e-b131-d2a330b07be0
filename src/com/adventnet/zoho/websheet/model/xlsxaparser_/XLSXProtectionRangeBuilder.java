//$Id$

package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.util.Utility;

import java.util.*;

class XLSXProtectionRangeBuilder {

    /**
     * Iteration Types: Cell-Iteration (Most-Expensive), Row/Column-Iteration(Less-Expensive), No-Iteration(Least-Expensive)
     *
     * Entire Area can be divided into 2, based on usability(contains LOCKED/NOT-LOCKED entity) : 1. cells used area 2. cell not-used area (2.a. row/col used area + 2.b. row/col unused area)
     * Now for (1) we need to iterate cells to identify locked-ranges
     *     for (2.a) we only need to iterate rows/columns to identify locked-ranges
     *     for (2.b) we don't need to iterate at all (by default it will be Locked)
     * Also (1) can be identified by maintaining (Start-Cell, End-Cell)
     *      (2.a) can be identified by maintaining (Start-Row,End-Row)/(Start-Col,End-Col)
     *      (2.b) is area other then (1) and (2.a)
     *
     * Entire Area (Row-wise or Column-wise) can be divided into 3, based on their lock-property:
     *  a. Inherit-Lock-Area
     *  b. Lock-Area
     *  c. Not-Lock-Area
     * It is difficult to identify (b) and (c) type of areas, as it need iteration over rows and columns. But we can
     * identify the following group: (i) (a) ,(ii) (a) + (b) ,(iii) (a) + (c) ,(iv) (a) + (b) + (c) for rows and columns
     * Further,
     *  (row-(i) or (ii)) intersection (col-(i) or (ii)) = (2.b), this derivation saves iterations
     *
     */
    static final int LOCKED = -1;
    static final int INHERIT_LOCK = 0;
    static final int NOT_LOCKED = 1;

    private final int mAX_ROW_INDEX = Utility.MAXNUMOFROWS - 1;
    private final int mAX_COL_INDEX = Utility.MAXNUMOFCOLS - 1;

    private LockStateArray colsLockPpt = new LockStateArray();
    private LockStateArray rowsLockPpt = new LockStateArray();
    private CellLockStateArray cellsLockPpt = new CellLockStateArray();

    private final int sTART_INDEX = 0;
    private final int sND_INDEX = 1;

    private final int sTART_ROW_INDEX = 0;
    private final int sTART_COL_INDEX = 1;
    private final int eND_ROW_INDEX = 2;
    private final int eND_COL_INDEX = 3;

    private final int[] lockedHtlBelt = new int[]{Utility.MAXNUMOFROWS, -1};
    private final int[] notLockedHtlBelt = lockedHtlBelt.clone();
    private final int[] lockedVtlBelt = new int[]{Utility.MAXNUMOFCOLS, -1};
    private final int[] notLockedVtlBelt = lockedVtlBelt.clone();

    private final int[] usedArea = new int[]{Utility.MAXNUMOFROWS, Utility.MAXNUMOFCOLS, -1, -1};

    private List<Range> lockedRanges = new ArrayList<>();

    private static class Belt {
        private int[] beltIndexes;
        private int type;
    }

    void addColsLockPpt(int start_ColIndex, int end_ColIndex, int lockPpt) {
        extendVtlBelt(start_ColIndex, lockPpt);
        extendVtlBelt(end_ColIndex, lockPpt);
        colsLockPpt.set(start_ColIndex, end_ColIndex+1, getLockState(lockPpt));
    }

    void addRowLockPpt(int rowIndex, int lockPpt) {
        extendHtlBelt(rowIndex, lockPpt);
        rowsLockPpt.set(rowIndex, getLockState(lockPpt));
    }

    void addCellLockPpt(int rowIndex, int colIndex, int lockPpt) {
        if(!isCellLockStatusEqualsInheritLockStatus(rowIndex, colIndex, lockPpt)) {
            extendHtlUsedArea(rowIndex);
            extendVtlUsedArea(colIndex);
            cellsLockPpt.set(rowIndex,colIndex, getLockState(lockPpt));
        }
    }

    boolean isEntireSheetLocked() {
        boolean isUsed_Start_Col_NotChanged = (usedArea[sTART_COL_INDEX] == Utility.MAXNUMOFCOLS);
        boolean isUsed_Start_Row_NotChanged = usedArea[sTART_ROW_INDEX] == Utility.MAXNUMOFROWS;
        boolean isUsed_End_Col_NotChanged = usedArea[eND_COL_INDEX] == -1;
        boolean isUsed_End_Row_NotChanged = usedArea[eND_ROW_INDEX] == -1;
        boolean isLockedHtlBelt_NotChanged = lockedHtlBelt[sTART_INDEX] == Utility.MAXNUMOFROWS && lockedHtlBelt[sND_INDEX] == -1;
        boolean isLockedVtlBelt_NotChanged = lockedVtlBelt[sTART_INDEX] == Utility.MAXNUMOFCOLS && lockedVtlBelt[sND_INDEX] == -1;
        boolean isNotLockedHtlBelt_NotChanged = notLockedHtlBelt[sTART_INDEX] == Utility.MAXNUMOFROWS && notLockedHtlBelt[sND_INDEX] == -1;
        boolean isNotLockedVtlBelt_NotChanged = notLockedVtlBelt[sTART_INDEX] == Utility.MAXNUMOFCOLS && notLockedVtlBelt[sND_INDEX] == -1;

        if(isLockedHtlBelt_NotChanged
                && isLockedVtlBelt_NotChanged
                && isNotLockedHtlBelt_NotChanged
                && isNotLockedVtlBelt_NotChanged

                && isUsed_Start_Row_NotChanged
                && isUsed_Start_Col_NotChanged
                && isUsed_End_Row_NotChanged
                && isUsed_End_Col_NotChanged) {
            return true;
        } else {
            return false;
        }
    }

    List<Range> getLockedRanges(Sheet sheet) {

        List<Belt> htlBelts = buildBelts(lockedHtlBelt, notLockedHtlBelt, 0, Utility.MAXNUMOFROWS - 1);
        if(isLineInBetween(0, mAX_ROW_INDEX, usedArea[sTART_ROW_INDEX]) && isLineInBetween(0, mAX_ROW_INDEX, usedArea[eND_ROW_INDEX])) {
            addBelt(htlBelts, usedArea[sTART_ROW_INDEX], false);
            addBelt(htlBelts, usedArea[eND_ROW_INDEX], true);
        }

        List<Belt> vtlBelts = buildBelts(lockedVtlBelt, notLockedVtlBelt, 0, Utility.MAXNUMOFCOLS - 1);
        if(isLineInBetween(0, mAX_COL_INDEX, usedArea[sTART_COL_INDEX]) && isLineInBetween(0, mAX_COL_INDEX, usedArea[eND_COL_INDEX])) {
            addBelt(vtlBelts, usedArea[sTART_COL_INDEX], false);
            addBelt(vtlBelts, usedArea[eND_COL_INDEX], true);
        }

        for(Belt htlBelt: htlBelts) {
            for(Belt vtlBelt: vtlBelts) {
                int[] areaOfIntersection =  new int[4];
                areaOfIntersection[sTART_ROW_INDEX] = htlBelt.beltIndexes[sTART_INDEX];
                areaOfIntersection[sTART_COL_INDEX] = vtlBelt.beltIndexes[sTART_INDEX];
                areaOfIntersection[eND_ROW_INDEX] = htlBelt.beltIndexes[sND_INDEX];
                areaOfIntersection[eND_COL_INDEX] = vtlBelt.beltIndexes[sND_INDEX];

                if(!isAreaEnclosed(areaOfIntersection, usedArea)) {
                    if(htlBelt.type == LOCKED) {
                        lockedRanges.add(new Range(sheet, areaOfIntersection[sTART_ROW_INDEX], areaOfIntersection[sTART_COL_INDEX], areaOfIntersection[eND_ROW_INDEX], areaOfIntersection[eND_COL_INDEX]));
                    } else if(htlBelt.type == INHERIT_LOCK) {
                        if(vtlBelt.type == INHERIT_LOCK || vtlBelt.type == LOCKED) {
                            lockedRanges.add(new Range(sheet, areaOfIntersection[sTART_ROW_INDEX], areaOfIntersection[sTART_COL_INDEX], areaOfIntersection[eND_ROW_INDEX], areaOfIntersection[eND_COL_INDEX]));
                        } else if(vtlBelt.type == NOT_LOCKED) {
                            //iterate only cols
                            int numberOfRows = areaOfIntersection[eND_ROW_INDEX] - areaOfIntersection[sTART_ROW_INDEX] + 1;
                            addLockedRangesOfVtlBlts(sheet, areaOfIntersection[sTART_ROW_INDEX], areaOfIntersection[sTART_COL_INDEX], areaOfIntersection[eND_COL_INDEX], numberOfRows);
                        }
                    } else if(htlBelt.type == NOT_LOCKED) {
                        //iterate row and col
                        addLockedRangesOfFreeArea(sheet, areaOfIntersection[sTART_ROW_INDEX], areaOfIntersection[sTART_COL_INDEX], areaOfIntersection[eND_ROW_INDEX], areaOfIntersection[eND_COL_INDEX]);
                    }
                }
                mergeAdjacentRanges();
            }
        }

        addLockedRangesOfUsedArea(sheet);

        return lockedRanges;
    }

    private void mergeAdjacentRanges() {
        if(lockedRanges.size() > 1) {
            Range leftRange = lockedRanges.get(lockedRanges.size() - 1);
            Range leftOfTopRange = lockedRanges.get(lockedRanges.size() - 2);

            boolean isColContinuous = leftOfTopRange.getEndColIndex() + 1 == leftRange.getStartColIndex();
            boolean isStartRowEquals = leftRange.getStartRowIndex() == leftOfTopRange.getStartRowIndex();
            boolean isEndRowEqulas = leftRange.getEndRowIndex() == leftOfTopRange.getEndRowIndex();

            if(isColContinuous && isStartRowEquals && isEndRowEqulas) {
                Range mergedRange = new Range(leftOfTopRange.getSheet(), leftOfTopRange.getStartRowIndex(), leftOfTopRange.getStartColIndex(), leftOfTopRange.getEndRowIndex(), leftRange.getEndColIndex());
                lockedRanges.remove(lockedRanges.size() - 1);
                lockedRanges.remove(lockedRanges.size() - 1);
                lockedRanges.add(mergedRange);
            }
        }
    }

    private boolean isAreaEnclosed(final int[] innerArea, int[] outerArea) {
        if(innerArea[eND_ROW_INDEX] <= outerArea[eND_ROW_INDEX] && innerArea[sTART_ROW_INDEX] >= outerArea[sTART_ROW_INDEX] && innerArea[eND_COL_INDEX] <= outerArea[eND_COL_INDEX] && innerArea[sTART_COL_INDEX] >= outerArea[sTART_COL_INDEX]
                && innerArea[eND_ROW_INDEX] >= innerArea[sTART_ROW_INDEX] && innerArea[eND_COL_INDEX] >= innerArea[sTART_COL_INDEX]) {
            return true;
        } else {
            return false;
        }
    }


    private boolean isBeltEnclosed(final int[] outerBelt, final int[] innerBelt) {
        return outerBelt[sTART_INDEX] <= outerBelt[sND_INDEX] && outerBelt[sND_INDEX] >= innerBelt[sND_INDEX] && outerBelt[sTART_INDEX] <= innerBelt[sTART_INDEX] && innerBelt[sTART_INDEX] <= innerBelt[sND_INDEX];
    }

    private boolean isBeltOverlap(final int[] oneDRegion1, final  int[] oneDRegion2) {
        return !(oneDRegion1[sND_INDEX] <= oneDRegion2[sTART_INDEX] || oneDRegion2[sND_INDEX] <= oneDRegion1[sTART_INDEX]);
    }

    private boolean isFirstBeltOnTop(final int[] first, final int[] second) {
        return first[sTART_INDEX] < second[sTART_INDEX];
    }

    private boolean isLineInBetween(int topLine, int bottomLine, int line) {
        //enclosed
        return (line >= topLine && line <= bottomLine);
    }

    private void addBelt(final List<Belt> belts, int lineIndex, boolean isIncludeAtEnd) {

        for(int i = 0; i < belts.size(); i++) {
            Belt belt = belts.get(i);
            boolean isLineOnTop = belt.beltIndexes[sTART_INDEX] == lineIndex;
            boolean isLineOnBottom = belt.beltIndexes[sND_INDEX] == lineIndex;
            boolean isLineInside = belt.beltIndexes[sTART_INDEX] < lineIndex && belt.beltIndexes[sND_INDEX] > lineIndex;
            if(isLineInside) {
                Belt belt1 = new Belt();
                belt1.type = belt.type;
                if(isIncludeAtEnd) {
                    belt1.beltIndexes = new int[]{belt.beltIndexes[sTART_INDEX], lineIndex};
                    belt.beltIndexes[sTART_INDEX] = lineIndex + 1;
                    belts.add(i, belt1);
                } else {
                    belt1.beltIndexes = new int[]{lineIndex, belt.beltIndexes[sND_INDEX]};
                    belt.beltIndexes[sND_INDEX] = lineIndex - 1;
                    belts.add(i+1, belt1);
                }

                break;
            } else if(isLineOnTop && isLineOnBottom) {
                break;
            } else if(isLineOnTop) {
                Belt belt1 = new Belt();
                belt1.beltIndexes = new int[]{lineIndex,lineIndex};
                belt1.type = belt.type;

                belt.beltIndexes[sTART_INDEX] += 1;
                belts.add(i, belt1);
                break;
            } else if(isLineOnBottom) {
                Belt belt1 = new Belt();
                belt1.beltIndexes = new int[]{lineIndex,lineIndex};
                belt1.type = belt.type;

                belt.beltIndexes[sND_INDEX] -= 1;
                belts.add(i+1, belt1);
                break;
            }
        }
    }

    private List<Belt> getBelts(int[] innerBelt, int[] outerBelt, int lOCK_PPT) {

        List<Belt> belts = new ArrayList<>();

        boolean isStartsAt0 = innerBelt[sTART_INDEX] == outerBelt[sTART_INDEX];
        boolean isEndsAtMax = innerBelt[sND_INDEX] == outerBelt[sND_INDEX];

        Belt innerBeltObj = new Belt();
        innerBeltObj.type = lOCK_PPT;
        innerBeltObj.beltIndexes = innerBelt;

        if(isStartsAt0 && isEndsAtMax) {
            belts.add(innerBeltObj);
        } else if(isStartsAt0 && !isEndsAtMax) {
            belts.add(innerBeltObj);

            Belt belt = new Belt();
            belt.type = INHERIT_LOCK;
            belt.beltIndexes = new int[]{innerBelt[sND_INDEX] + 1, outerBelt[sND_INDEX]};
            belts.add(belt);

        } else if(!isStartsAt0 && isEndsAtMax) {
            Belt belt = new Belt();
            belt.type = INHERIT_LOCK;
            belt.beltIndexes = new int[]{outerBelt[sTART_INDEX], innerBelt[sTART_INDEX] - 1};
            belts.add(belt);

            belts.add(innerBeltObj);

        } else {
            Belt belt = new Belt();
            belt.type = INHERIT_LOCK;
            belt.beltIndexes = new int[]{outerBelt[sTART_INDEX], innerBelt[sTART_INDEX] - 1};
            belts.add(belt);

            belts.add(innerBeltObj);

            belt = new Belt();
            belt.type = INHERIT_LOCK;
            belt.beltIndexes = new int[]{innerBelt[sND_INDEX] + 1, outerBelt[sND_INDEX]};
            belts.add(belt);
        }

        return belts;
    }

    private List<Belt> buildBelts(final int[] lockedBelt, final int[] notLockedBelt, final int minINDEX, final int maxINDEX) {

        //horizontal_regions : INHERITED, LOCKED, NOT_LOCKED, LOCKED_OR_NOTLOCKED
        /*
        REGION_EXISTENCES_STATUS: LOCKED_REGION_EXISTS, NOT_LOCKED_REGION_EXISTS
        if both doesn't exist - Inherit
        if locked_region exist - Combination(INHERIT,LOCKED) - (I,L),(L,I),(I,L,I),L
        if unlocked_region exist - Combination(NOT_LOCKED,INHERIT) - (I,N), (N,I), (I,N,I) , N
        if both_regions exist - Combination(INHERIT,LOCKED, NOT_LOCKED)
            if both_region don't overlap - (L,N),(N,L),(I,L,N),(I,N,L),(I,L,N,I),(I,N,L,I), (I,L,I,N),(I,N,I,L), (I,N,I,L,I), (I,L,I,N,I)
            if both_region overlaps -
                locked_region_on_top -> (L,N)
                not_locked_region_on_top -> (N,L)
                locked_region_inside -> can't do anything for this
                not_locked_region_inside -> (L,N,L)
        */

        final List<Belt> belts = new ArrayList<>();

        boolean isLockedRegionExist = isBeltEnclosed(new int[]{minINDEX, maxINDEX}, lockedBelt);
        boolean isNotLockedRegionExist = isBeltEnclosed(new int[]{minINDEX, maxINDEX},notLockedBelt);

        if(isLockedRegionExist && isNotLockedRegionExist) {
            boolean isBothOverlap = isBeltOverlap(lockedBelt, notLockedBelt);
            if(isBothOverlap) {
                boolean isLockedRegionOnTop = isFirstBeltOnTop(lockedBelt, notLockedBelt);
                boolean isNotLockedRegionOnTop = isFirstBeltOnTop(notLockedBelt, lockedBelt);
                boolean isLockedRegionInside = isBeltEnclosed(notLockedBelt, lockedBelt);
                boolean isNotLockedRegionInside = isBeltEnclosed(lockedBelt, notLockedBelt);

                boolean isRegionExistAtTop = false;
                boolean isRegionExistAtBottom = false;

                int topIndex = Math.min(lockedBelt[sTART_INDEX], notLockedBelt[sTART_INDEX]);
                int bottomIndex = Math.max(lockedBelt[sND_INDEX], notLockedBelt[sND_INDEX]);

                if(isLockedRegionInside) {
                    //?,N,?
                    Belt belt = new Belt();
                    belt.type = NOT_LOCKED;
                    belt.beltIndexes = notLockedBelt;
                    belts.add(belt);

                    isRegionExistAtTop = notLockedBelt[sTART_INDEX] != minINDEX;
                    isRegionExistAtBottom = notLockedBelt[sND_INDEX] != maxINDEX;
                } else if(isNotLockedRegionInside) {
                    //?,L,N,L,?
                    Belt belt = new Belt();
                    belt.type = LOCKED;
                    belt.beltIndexes = new int[]{lockedBelt[sTART_INDEX], notLockedBelt[sTART_INDEX] - 1};
                    belts.add(belt);

                    belt = new Belt();
                    belt.type = NOT_LOCKED;
                    belt.beltIndexes = notLockedBelt;
                    belts.add(belt);

                    belt = new Belt();
                    belt.type = LOCKED;
                    belt.beltIndexes = new int[]{notLockedBelt[sND_INDEX]+1, lockedBelt[sTART_INDEX]};
                    belts.add(belt);

                    isRegionExistAtTop = lockedBelt[sTART_INDEX] != minINDEX;
                    isRegionExistAtBottom = lockedBelt[sND_INDEX] != maxINDEX;
                } else if(isLockedRegionOnTop) {
                    //?,L,N,?
                    Belt topBelt = new Belt();
                    topBelt.type = LOCKED;
                    topBelt.beltIndexes = new int[]{lockedBelt[sTART_INDEX], notLockedBelt[sTART_INDEX] - 1};

                    Belt bottomBelt = new Belt();
                    bottomBelt.type = NOT_LOCKED;
                    bottomBelt.beltIndexes = new int[]{lockedBelt[sND_INDEX] + 1, notLockedBelt[sND_INDEX]};

                    belts.add(topBelt);
                    belts.add(bottomBelt);

                    isRegionExistAtTop = lockedBelt[sTART_INDEX] != minINDEX;
                    isRegionExistAtBottom = notLockedBelt[sND_INDEX] != maxINDEX;
                } else if(isNotLockedRegionOnTop) {
                    //?,N,L,?

                    Belt topBelt = new Belt();
                    topBelt.type = NOT_LOCKED;
                    topBelt.beltIndexes = new int[]{notLockedBelt[sTART_INDEX], lockedBelt[sTART_INDEX] - 1};

                    Belt bottomBelt = new Belt();
                    bottomBelt.type = LOCKED;
                    bottomBelt.beltIndexes = new int[]{notLockedBelt[sND_INDEX] + 1, lockedBelt[sND_INDEX]};

                    belts.add(topBelt);
                    belts.add(bottomBelt);

                    isRegionExistAtTop = notLockedBelt[sTART_INDEX] != minINDEX;
                    isRegionExistAtBottom = lockedBelt[sND_INDEX] != maxINDEX;
                }

                if(isRegionExistAtTop) {
                    //I,?,?,?,?
                    Belt belt = new Belt();
                    belt.type = INHERIT_LOCK;
                    belt.beltIndexes = new int[]{minINDEX, topIndex - 1};
                    belts.add(0,belt);
                }
                if(isRegionExistAtBottom) {
                    //?,?,?,?,I
                    Belt belt = new Belt();
                    belt.type = INHERIT_LOCK;
                    belt.beltIndexes = new int[]{bottomIndex + 1, maxINDEX};
                }
            } else {
                boolean isLockedRegionOnTop = isFirstBeltOnTop(lockedBelt, notLockedBelt);
                boolean isRegionExistOnTop = false;
                boolean isRegionExistAtBottom = false;
                boolean isRegionExistBetweenBoth = false;

                Belt lockedBeltObject = new Belt();
                lockedBeltObject.type = LOCKED;
                lockedBeltObject.beltIndexes = lockedBelt;

                Belt notLockedBeltObject = new Belt();
                notLockedBeltObject.type = NOT_LOCKED;
                notLockedBeltObject.beltIndexes = notLockedBelt;

                if(isLockedRegionOnTop) {
                    //?,L,?,?,?
                    belts.add(lockedBeltObject);
                    belts.add(notLockedBeltObject);
                    isRegionExistOnTop = lockedBelt[sTART_INDEX] != minINDEX;
                    isRegionExistAtBottom = notLockedBelt[sND_INDEX] != maxINDEX;
                    isRegionExistBetweenBoth = (lockedBelt[sND_INDEX] + 1 != notLockedBelt[sTART_INDEX]);
                } else {
                    //?,N,?,?,?
                    belts.add(notLockedBeltObject);
                    belts.add(lockedBeltObject);
                    isRegionExistOnTop = notLockedBelt[sTART_INDEX] != minINDEX;
                    isRegionExistAtBottom = lockedBelt[sND_INDEX] != maxINDEX;
                    isRegionExistBetweenBoth = (notLockedBelt[sND_INDEX] + 1 != lockedBelt[sTART_INDEX]);
                }
                if(isRegionExistOnTop) {
                    //I,?,?,?,?
                    Belt beltObject = new Belt();
                    beltObject.type = INHERIT_LOCK;

                    if(isLockedRegionOnTop) {
                        beltObject.beltIndexes = new int[]{minINDEX, lockedBelt[sTART_INDEX] - 1};
                    } else {
                        beltObject.beltIndexes = new int[]{minINDEX, notLockedBelt[sTART_INDEX] - 1};
                    }
                }
                if(isRegionExistAtBottom) {
                    //?,?,?,?,I
                    Belt beltObject = new Belt();
                    beltObject.type = INHERIT_LOCK;

                    if(isLockedRegionOnTop) {
                        beltObject.beltIndexes = new int[]{lockedBelt[sTART_INDEX] + 1, maxINDEX};
                    } else {
                        beltObject.beltIndexes = new int[]{notLockedBelt[sTART_INDEX] + 1, maxINDEX};
                    }
                }
                if(isRegionExistBetweenBoth) {
                    //?,?,I,?,?
                    Belt beltObject = new Belt();
                    beltObject.type = INHERIT_LOCK;

                    if(isLockedRegionOnTop) {
                        beltObject.beltIndexes = new int[]{lockedBelt[sND_INDEX] + 1, notLockedBelt[sTART_INDEX] - 1};
                    } else {
                        beltObject.beltIndexes = new int[]{notLockedBelt[sND_INDEX] + 1, lockedBelt[sTART_INDEX] - 1};
                    }
                }
            }
        } else if(isLockedRegionExist) {
            belts.addAll(getBelts(lockedBelt, new int[]{minINDEX, maxINDEX}, LOCKED));
        } else if(isNotLockedRegionExist) {
            belts.addAll(getBelts(notLockedBelt, new int[]{minINDEX, maxINDEX}, NOT_LOCKED));
        } else {
            Belt belt = new Belt();
            belt.beltIndexes = new int[]{minINDEX, maxINDEX};
            belt.type = INHERIT_LOCK;
            belts.add(belt);
        }

        return belts;
    }

    private void extendVtlUsedArea(int colIndex) {
        usedArea[sTART_COL_INDEX] = Math.min(usedArea[sTART_COL_INDEX], colIndex);
        usedArea[eND_COL_INDEX] = Math.max(usedArea[eND_COL_INDEX], colIndex);
    }

    private void extendHtlUsedArea(int rowIndex) {
        usedArea[sTART_ROW_INDEX] = Math.min(usedArea[sTART_ROW_INDEX], rowIndex);
        usedArea[eND_ROW_INDEX] = Math.max(usedArea[eND_ROW_INDEX], rowIndex);
    }

    private void extendVtlBelt(int colIndex, int lockPpt) {
        if(lockPpt == LOCKED) {
            lockedVtlBelt[sTART_INDEX] = Math.min(lockedVtlBelt[sTART_INDEX], colIndex);
            lockedVtlBelt[sND_INDEX] = Math.max(lockedVtlBelt[sND_INDEX], colIndex);
        } else if(lockPpt == NOT_LOCKED) {
            notLockedVtlBelt[sTART_INDEX] = Math.min(notLockedVtlBelt[sTART_INDEX], colIndex);
            notLockedVtlBelt[sND_INDEX] = Math.max(notLockedVtlBelt[sND_INDEX], colIndex);
        }
    }

    private void extendHtlBelt(int rowIndex, int lockPpt) {
        if(lockPpt == LOCKED) {
            lockedHtlBelt[sTART_INDEX] = Math.min(lockedHtlBelt[sTART_INDEX], rowIndex);
            lockedHtlBelt[sND_INDEX] = Math.max(lockedHtlBelt[sND_INDEX], rowIndex);
        } else if(lockPpt == NOT_LOCKED) {
            notLockedHtlBelt[sTART_INDEX] = Math.min(notLockedHtlBelt[sTART_INDEX], rowIndex);
            notLockedHtlBelt[sND_INDEX] = Math.max(notLockedHtlBelt[sND_INDEX], rowIndex);
        }
    }

    private void addLockedRangesOfUsedArea(Sheet sheet) {
        for(int i = usedArea[sTART_ROW_INDEX]; i <= usedArea[eND_ROW_INDEX]; i++) {
            for(int j = usedArea[sTART_COL_INDEX]; j <= usedArea[eND_COL_INDEX]; j++) {
                int continuousColSize = getContinuousColSize(i,j, usedArea[eND_COL_INDEX]);
                if(continuousColSize != 0) {
                    int continuousRowSize = getContinuousRowSize(i, usedArea[eND_ROW_INDEX],j,continuousColSize);
                    lockedRanges.add(new Range(sheet, i, j, i+continuousRowSize-1,  j+continuousColSize-1));
                    setUnlockedCells(i, j, continuousRowSize, continuousColSize);
                    j += continuousColSize - 1;
                }
            }
        }
    }

    private int getContinuousColSize(int rowIndex, int startColIndex, int endColIndex) {
        int continuousColSize = 0;
        while ((startColIndex <= endColIndex) && isCellLocked(rowIndex, startColIndex)) {
            startColIndex++;
            continuousColSize++;
        }
        return continuousColSize;
    }

    private int getContinuousRowSize(int startRowIndex, int endRowIndex, int colIndex, int continuousTopColSize) {
        int continuousRowSize = 1;
        for (int i = startRowIndex + 1; i <= endRowIndex; i++) {
            int continuousColSize = getContinuousColSize(i, colIndex, colIndex + continuousTopColSize - 1);
            if(continuousColSize == continuousTopColSize) {
                continuousRowSize++;
            } else {
                break;
            }
        }
        return continuousRowSize;
    }

    private void setUnlockedCells(int rowIndex, int colIndex, int continuousRowSize, int continuousColSize) {
        for(int i = rowIndex; i < (rowIndex+continuousRowSize); i++) {
            for(int j = colIndex; j < (colIndex+continuousColSize); j++) {
                cellsLockPpt.set(i, j, LockStateArray.State.NOT_LOCKED);
            }
        }
    }

    private boolean isCellLockStatusEqualsInheritLockStatus(int rowIndex, int colIndex, int status) {
        if(rowsLockPpt.get(rowIndex) == LockStateArray.State.INHERIT_LOCK) {
            return getLockState(status) == colsLockPpt.get(colIndex);
        } else {
            return getLockState(status) == rowsLockPpt.get(rowIndex);
        }
    }

    private boolean isCellLocked(int rowIndex, int colIndex) {
        if(cellsLockPpt.get(rowIndex,colIndex) == LockStateArray.State.LOCKED) {
            return true;
        } else if(cellsLockPpt.get(rowIndex,colIndex) == LockStateArray.State.NOT_LOCKED) {
            return false;
        } else {
            if(rowsLockPpt.get(rowIndex) == LockStateArray.State.LOCKED) {
                return true;
            } else if(rowsLockPpt.get(rowIndex) == LockStateArray.State.NOT_LOCKED) {
                return false;
            } else {
                if(colsLockPpt.get(colIndex) == LockStateArray.State.NOT_LOCKED) {
                    return false;
                } else {
                    //col will inherit lock from sheet
                    return  true;
                }
            }
        }
    }

    private void addLockedRangesOfFreeArea(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) {
        if(((endRowIndex - startRowIndex + 1) < 1) || ((endColIndex - startColIndex + 1) < 1)) {
            return;
        }
        for(int rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {
            int continuousLockedRowSize = getContinuousLockedRowSize(rowIndex, endRowIndex);

            if(continuousLockedRowSize == 0) {
                int continuousInheritLockRowSize = getContinuousInheritLockRowSize(rowIndex, endRowIndex);
                if(continuousInheritLockRowSize != 0) {
                    addLockedRangesOfVtlBlts(sheet, rowIndex, startColIndex, endColIndex, continuousInheritLockRowSize);

                    rowIndex += continuousInheritLockRowSize - 1;
                }
            } else {
                lockedRanges.add(new Range(sheet, rowIndex, startColIndex, rowIndex + continuousLockedRowSize - 1, endColIndex));
                rowIndex += continuousLockedRowSize - 1;
            }
        }
    }

    private void addLockedRangesOfVtlBlts(Sheet sheet, int rowIndex, int startColIndex, int endColIndex, int continuousInheritLockRowSize) {
        for (int colIndex = startColIndex; colIndex <= endColIndex; colIndex++) {
            int continuousColSize = getContinuousColSize(colIndex, endColIndex);
            if (continuousColSize != 0) {
                lockedRanges.add(new Range(sheet, rowIndex, colIndex, rowIndex + continuousInheritLockRowSize - 1, colIndex + continuousColSize - 1));
                colIndex += continuousColSize - 1;
            }
        }
    }

    private int getContinuousLockedRowSize(int start_RowIndex, int end_RowIndex) {
        int continuousRowSize = 0;
        while (start_RowIndex <= end_RowIndex && (rowsLockPpt.get(start_RowIndex) == LockStateArray.State.LOCKED)) {
            start_RowIndex++;
            continuousRowSize++;
        }
        return continuousRowSize;
    }

    private int getContinuousInheritLockRowSize(int start_RowIndex, int end_RowIndex) {
        int continuousRowSize = 0;
        while (start_RowIndex <= end_RowIndex && (rowsLockPpt.get(start_RowIndex) == LockStateArray.State.INHERIT_LOCK)) {
            start_RowIndex++;
            continuousRowSize++;
        }
        return continuousRowSize;
    }

    private int getContinuousColSize(int start_ColIndex, int end_ColIndex) {
        int continuousColSize = 0;
        while (start_ColIndex <= end_ColIndex && (colsLockPpt.get(start_ColIndex) == LockStateArray.State.LOCKED || colsLockPpt.get(start_ColIndex) == LockStateArray.State.INHERIT_LOCK)) {
            start_ColIndex++;
            continuousColSize++;
        }
        return continuousColSize;
    }

    public static void main(String[] args) {
        CellLockStateArray lockStateArray = new CellLockStateArray();
        lockStateArray.set(0,0, LockStateArray.State.LOCKED);
//        lockStateArray.set(5, LockStateArray.State.LOCKED);
//        lockStateArray.set(5, LockStateArray.State.NOT_LOCKED);
        lockStateArray.set(5,1, LockStateArray.State.NOT_LOCKED);
//        System.out.println(lockStateArray.get(0,0));
//        System.out.println(lockStateArray.get(1,1));
//        System.out.println(lockStateArray.get(5,1));
//        System.out.println(lockStateArray.get(5,0));
//        System.out.println(lockStateArray.get(6,1));

        LockStateArray lockStateArray1 = new LockStateArray();
        lockStateArray1.set(0,3, LockStateArray.State.LOCKED);
//        System.out.println(lockStateArray1.get(0));
//        System.out.println(lockStateArray1.get(1));
//        System.out.println(lockStateArray1.get(2));
//        System.out.println(lockStateArray1.get(3));
//        System.out.println(lockStateArray1.get(4));
    }

     private static class LockStateArray {
        enum State {LOCKED, NOT_LOCKED, INHERIT_LOCK}
        private BitSet lock = new BitSet();
        private BitSet not_lock = new BitSet();

        void set(int index,  State status) {
            switch(status){
                case LOCKED:lock.set(index);break;
                case NOT_LOCKED:not_lock.set(index);break;
            }
        }
        void set(int start, int end, State status) {
            switch(status){
                case LOCKED:lock.set(start,end);break;
                case NOT_LOCKED:not_lock.set(start, end);break;
            }
        }
        State get(int index) {
            if(lock.get(index)){
                return State.LOCKED;
            }
            if(not_lock.get(index)){
                return State.NOT_LOCKED;
            }
            return State.INHERIT_LOCK;
        }
    }

    private static class CellLockStateArray {
        private LockStateArray lockStateArray = new LockStateArray();
        void set(int row, int col, LockStateArray.State status) {
            lockStateArray.set(row*Utility.MAXNUMOFCOLS+col, status);
        }
        LockStateArray.State get(int row, int col) {
            return lockStateArray.get(row*Utility.MAXNUMOFCOLS+col);
        }
    }

    private static LockStateArray.State getLockState(int status) {
        switch(status) {
            case LOCKED:return LockStateArray.State.LOCKED;
            case NOT_LOCKED:return LockStateArray.State.NOT_LOCKED;
        }
        return LockStateArray.State.INHERIT_LOCK;
    }
}
