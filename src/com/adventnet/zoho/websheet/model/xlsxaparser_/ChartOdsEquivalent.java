//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.zschart.Axis;
import com.adventnet.zoho.websheet.model.zschart.ChartProperties;
import com.adventnet.zoho.websheet.model.zschart.Legend;

/**
 *
 * <AUTHOR>
 */
class ChartOdsEquivalent {
    
    static ChartProperties.REGRESSION_TYPE eqlTrendLineType(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "exp"://No I18N
                return ChartProperties.REGRESSION_TYPE.EXPONENTIAL;
            case "linear"://No I18N
                return ChartProperties.REGRESSION_TYPE.LINEAR;
            case "log"://No I18N
                return ChartProperties.REGRESSION_TYPE.LOGARITHMIC;
            case "movingAvg"://No I18N
                return ChartProperties.REGRESSION_TYPE.MOVING_AVERAGE;
            case "poly"://No I18N
                return ChartProperties.REGRESSION_TYPE.POLYNOMIAL;
            case "power"://No I18N
                return ChartProperties.REGRESSION_TYPE.POWER;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.TRENDLINE_TYPE, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
        }
    }
    
    static ChartProperties.LABEL_POSITION eqlDataLabelPos(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "b"://No I18N
                return ChartProperties.LABEL_POSITION.BOTTOM;
            case "bestFit"://No I18N
                return ChartProperties.LABEL_POSITION.AVOID_OVERLAP;
            case "ctr"://No I18N
                return ChartProperties.LABEL_POSITION.CENTER;
            case "inBase"://No I18N
                return ChartProperties.LABEL_POSITION.NEAR_ORIGIN;
            case "inEnd"://No I18N
                return ChartProperties.LABEL_POSITION.INSIDE;
            case "l"://No I18N
                return ChartProperties.LABEL_POSITION.LEFT;
            case "outEnd"://No I18N
                return ChartProperties.LABEL_POSITION.OUTSIDE;
            case "r"://No I18N
                return ChartProperties.LABEL_POSITION.RIGHT;
            case "t"://No I18N
                return ChartProperties.LABEL_POSITION.TOP;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.CHART_LABEL_POSITION, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
                
        }
    }
    
    static ChartProperties.AXIS_LABEL_POSITION eqlAxisLabelPos(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "high"://No I18N
                return ChartProperties.AXIS_LABEL_POSITION.OUTSIDE_END;
            case "low"://No I18N
                return ChartProperties.AXIS_LABEL_POSITION.OUTSIDE_START;
            case "nextTo"://No I18N
                return ChartProperties.AXIS_LABEL_POSITION.NEAR_AXIS;
            case "none"://No I18N
                return null;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.AXIS_LABEL_POSITION, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
        }
    }
    
    static ChartProperties.AXIS_POSITION eqlAxisPos(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "autoZero"://No I18N
                return ChartProperties.AXIS_POSITION.START;
            case "max"://No I18N
                return ChartProperties.AXIS_POSITION.END;
            case "min"://No I18N
                return ChartProperties.AXIS_POSITION.START;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.CHART_AXIS_POSITION, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
        }
    }
    
    static Legend.LEGEND_POSITION getOdsEqlLegendPos(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "b"://No I18N
                return Legend.LEGEND_POSITION.BOTTOM;
            case "l"://No I18N
                return Legend.LEGEND_POSITION.START;
            case "r"://No I18N
                return Legend.LEGEND_POSITION.END;
            case "t"://No I18N
                return Legend.LEGEND_POSITION.TOP;
            case "tr"://No I18N
                return Legend.LEGEND_POSITION.TOPEND;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.CHART_LEGEND_POSITOIN, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
        }
    }
    
    static ChartProperties.SOLID_TYPE eqlSolidType(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "cone"://No I18N
                return ChartProperties.SOLID_TYPE.CONE;
            case "coneToMax"://No I18N
                return ChartProperties.SOLID_TYPE.CONE;
            case "box"://No I18N
                return ChartProperties.SOLID_TYPE.CUBOID;
            case "cylinder"://No I18N
                return ChartProperties.SOLID_TYPE.CYLINDER;
            case "pyramid"://No I18N
                return ChartProperties.SOLID_TYPE.PYRAMID;
            case "pyramidToMax"://No I18N
                return ChartProperties.SOLID_TYPE.PYRAMID;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.CHART_SOLIDTYPE, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
        }
    }
    
    static Axis.DIMENSION eqlErrDir(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "x"://No I18N
                return Axis.DIMENSION.X;
            case "y"://No I18N
                return Axis.DIMENSION.Y;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.AXIS_DIMENSION, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
        }
    }
    
    static ChartProperties.ERROR_CATEGORY eqlErrValueType(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "cust"://No I18N
                return ChartProperties.ERROR_CATEGORY.CELL_RANGE;
            case "fixedVal"://No I18N
                return ChartProperties.ERROR_CATEGORY.CONSTANT;
            case "percentage"://No I18N
                return ChartProperties.ERROR_CATEGORY.PERCENTAGE;
            case "stdDev"://No I18N
                return ChartProperties.ERROR_CATEGORY.STANDARD_DEVIATION;
            case "stdErr"://No I18N
                return ChartProperties.ERROR_CATEGORY.STANDARD_ERROR;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.ERROR_CATEGORY, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
        }
    }
    
    static ChartProperties.TREAT_EMPTY_CELLS eqlDisplayBlankAs(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "span"://No I18N
                return ChartProperties.TREAT_EMPTY_CELLS.IGNORE;
            case "gap"://No I18N
                return ChartProperties.TREAT_EMPTY_CELLS.LEAVE_GAP;
            case "zero"://No I18N
                return ChartProperties.TREAT_EMPTY_CELLS.USE_ZERO;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.CHART_DISPLAY_BLANK_TYPE, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
        }
    }
    
    static ChartProperties.SYMBOL_NAME eqlMarkerStyle(String xlsx) throws XLSXException {
        switch (xlsx) {
            case "circle"://No I18N
                return ChartProperties.SYMBOL_NAME.CIRCLE;
            case "dash"://No I18N
                return ChartProperties.SYMBOL_NAME.HORIZONTAL_BAR;
            case "diamond"://No I18N
                return ChartProperties.SYMBOL_NAME.DIAMOND;
            case "dot"://No I18N
                return ChartProperties.SYMBOL_NAME.HOURGLASS;
            case "none"://No I18N
                return null;
            case "picture"://No I18N
                return null;
            case "plus"://No I18N
                return ChartProperties.SYMBOL_NAME.PLUS;
            case "square"://No I18N
                return ChartProperties.SYMBOL_NAME.SQUARE;
            case "star"://No I18N
                return ChartProperties.SYMBOL_NAME.STAR;
            case "triangle"://No I18N
                return ChartProperties.SYMBOL_NAME.ARROW_UP;
            case "x"://No I18N
                return ChartProperties.SYMBOL_NAME.X;
            case "auto"://No I18N
                return null;
            default:
                XLSXException xlsxe = new XLSXException();
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.MARKER_STYLE, xlsx));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                throw xlsxe;
        }
    }

    
}
