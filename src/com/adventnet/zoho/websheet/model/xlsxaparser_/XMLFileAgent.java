//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.xlsxaparser_;

import com.adventnet.zoho.websheet.model.util.Utility;
import com.adventnet.zoho.websheet.model.zs.ZSZipInputStream;
import com.zoho.zfsng.constants.ZFSNGConstants;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.apache.commons.compress.utils.SeekableInMemoryByteChannel;
import org.apache.commons.io.IOUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;

/**
 *
 * <AUTHOR>
 */
class XMLFileAgent {

    private static final Logger LOGGER = Logger.getLogger(XMLFileAgent.class.getName());
    private final Map<String, XMLFile> xlsxfileOfPath = new HashMap<>();
    private final Map<String, List<XMLFile>> xlsxfilesOfContentType = new LinkedHashMap<>();

    private final static String[] REQUIARED_CONTENT_TYPE = new String[]{
        //        FilePathConstants.CONTENTTYPES,
//        FilePathConstants.WORKBOOK,
        //        FilePathConstants.STYLES,
        FilePathConstants.SHEET
    };
    private final byte[] xlsx_bytes;

    private Map<String, XMLFile> xmlFileByName = new HashMap<>();

    public byte[] getXlsx_bytes() {
        return xlsx_bytes;
    }

    XMLFileAgent(byte[] xlsx_bytes) throws IOException, XLSXException {
        this.xlsx_bytes = xlsx_bytes;
        setxlsxfilesOfContentType();
        createXmlFiles(xlsx_bytes);
        checkCompulsoryFiles();
        if(xlsxfilesOfContentType.get(FilePathConstants.SHEET).size() > Utility.MAXNUMOFSHEETS) {
            XLSXParserAgent.putToFEATURES_NOT_PARSED(XLSXException.FEATURE.WORKBOOK, XLSXException.CAUSETYPE.SHEETS_LIMIT_EXCEEDED);
            throw new XlsxLimitException(XLSXException.CAUSETYPE.SHEETS_LIMIT_EXCEEDED.toString(), ZFSNGConstants.CONVERSION_FAILURE);
        }
    }

    private void setxlsxfilesOfContentType() {
        /*
         parser is dependent on the order files are parsed
         */

//        xlsxfilesOfContentType.put(FilePathConstants.IMAGE, null);
        xlsxfilesOfContentType.put(FilePathConstants.CONTENTTYPES, null);
        xlsxfilesOfContentType.put(FilePathConstants.RELS_WORKBOOK, null);
        xlsxfilesOfContentType.put(FilePathConstants.WORKBOOK, null);
        xlsxfilesOfContentType.put(FilePathConstants.THEME, null);
        xlsxfilesOfContentType.put(FilePathConstants.SHAREDSTRINGS, null);
        xlsxfilesOfContentType.put(FilePathConstants.STYLES, null);

        xlsxfilesOfContentType.put(FilePathConstants.PIVOTCACHEDEFINITION, null);
        xlsxfilesOfContentType.put(FilePathConstants.RELS_PIVOTTABLE, null);
        xlsxfilesOfContentType.put(FilePathConstants.PIVOTTABLE, null);

        xlsxfilesOfContentType.put(FilePathConstants.RELS_RICHVALUE, null);

        xlsxfilesOfContentType.put(FilePathConstants.CTRL_PROP, null);

        xlsxfilesOfContentType.put(FilePathConstants.RELS_SHEET, null);
        xlsxfilesOfContentType.put(FilePathConstants.RELS_CHARTSHEET, null);
        xlsxfilesOfContentType.put(FilePathConstants.COMMENTS, null);
        xlsxfilesOfContentType.put(FilePathConstants.RELS_DRAWING, null);
        xlsxfilesOfContentType.put(FilePathConstants.DRAWING, null);
        xlsxfilesOfContentType.put(FilePathConstants.CHART, null);
        xlsxfilesOfContentType.put(FilePathConstants.SHEET, null);
        xlsxfilesOfContentType.put(FilePathConstants.CHARTSHEET, null);
        xlsxfilesOfContentType.put(FilePathConstants.TABLE, null);
        xlsxfilesOfContentType.put(FilePathConstants.APP, null);
        xlsxfilesOfContentType.put(FilePathConstants.SLICERCACHE, null);
        xlsxfilesOfContentType.put(FilePathConstants.SLICER, null);
        xlsxfilesOfContentType.put(FilePathConstants.TIMELINE_CACHE, null);
        xlsxfilesOfContentType.put(FilePathConstants.TIMELINE, null);
    }

    private void createXmlFiles(byte[] xlsx_bytes) throws IOException {
        try (ZSZipInputStream zipInputStream = ZSZipInputStream.getInstance(xlsx_bytes)) {
            ZipEntry zipEntry;
            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                String filePath = zipEntry.getName();
                XMLFile xlsxf = new XMLFile(filePath);
                xlsxf.setZipBytes(xlsx_bytes);
                xlsxfileOfPath.put(xlsxf.getFilePath(), xlsxf);
                this.xmlFileByName.put(XLSXFileUtility.getFileName(filePath, XLSXFileUtility.FILENAME.WITHOUT_EXT), xlsxf);
                if (xlsxfilesOfContentType.containsKey(xlsxf.getType())) {
                    List<XMLFile> xlsxfs = xlsxfilesOfContentType.get(xlsxf.getType());
                    if (xlsxfs == null) {
                        xlsxfs = new ArrayList<>();
                        xlsxfilesOfContentType.put(xlsxf.getType(), xlsxfs);
                    }
                    xlsxfs.add(xlsxf);
                }
            }
        } catch(ZipException zipException) {
            LOGGER.log(Level.OFF, "trying with org.apache.commons.compress.archivers.zip.ZipFile", zipException);

            this.xlsxfileOfPath.clear();
            this.xmlFileByName.clear();
            this.xlsxfilesOfContentType.clear();
            setxlsxfilesOfContentType();
            ZSZipInputStream.testZipSafeUsingSecurityApis(xlsx_bytes);
            ZipFile zipFile = new ZipFile(new SeekableInMemoryByteChannel(xlsx_bytes));
            Enumeration<ZipArchiveEntry> entries=zipFile.getEntries();
            while(entries.hasMoreElements()){
                ZipArchiveEntry zipArchiveEntry=entries.nextElement();
                String filePath = zipArchiveEntry.getName();
                XMLFile xlsxf = new XMLFile(filePath);
                xlsxf.setBytes(IOUtils.toByteArray(zipFile.getInputStream(zipArchiveEntry)));
                xlsxfileOfPath.put(xlsxf.getFilePath(), xlsxf);
                this.xmlFileByName.put(XLSXFileUtility.getFileName(filePath, XLSXFileUtility.FILENAME.WITHOUT_EXT), xlsxf);
                if (xlsxfilesOfContentType.containsKey(xlsxf.getType())) {
                    List<XMLFile> xlsxfs = xlsxfilesOfContentType.get(xlsxf.getType());
                    if (xlsxfs == null) {
                        xlsxfs = new ArrayList<>();
                        xlsxfilesOfContentType.put(xlsxf.getType(), xlsxfs);
                    }
                    xlsxfs.add(xlsxf);
                }
            }
        }
    }

    private void checkCompulsoryFiles() throws XLSXException {
        for (String content_type : REQUIARED_CONTENT_TYPE) {
            if (xlsxfilesOfContentType.get(content_type) == null) {
                String message = content_type.concat(" not found");
                message = message.concat("\n xml-files :").concat(getXmlFiles().toString());//NO I18N
                XLSXException xlsxe = new XLSXException(message);
                xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.NAME, content_type));
                xlsxe.setCauseType(XLSXException.CAUSETYPE.FILE_CORRUPTED);
                throw xlsxe;
            }
        }
    }

    private byte[] getXmlFileBytes(InputStream inputStream) throws IOException {
        byte[] xmlFileBytes = null;
        int BUFF_LEN = 8192;
        byte[] bytes = new byte[BUFF_LEN];
        int n;
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            while ((n = inputStream.read(bytes, 0, BUFF_LEN)) > -1) {
                outputStream.write(bytes, 0, n);
            }
            xmlFileBytes = outputStream.toByteArray();
        }
        return xmlFileBytes;
    }

    private void checkEmfImage() throws XLSXException {
        List<XMLFile> imageXMLFiles = this.xlsxfilesOfContentType.get(FilePathConstants.IMAGE);
        if (imageXMLFiles != null) {
            for (XMLFile xmlf : imageXMLFiles) {
                String filePath = xmlf.getFilePath();
                if (filePath.contains(".emf")) {
                    XLSXException xlsxe = new XLSXException();
                    xlsxe.setFeature(XLSXException.FEATURE.DRAWING);
                    xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.XMLFile, filePath));
                    xlsxe.setIsFeatureLost(true);
                    xlsxe.setCauseType(XLSXException.CAUSETYPE.NOTSUPPORTED);
                    throw xlsxe;
                }
            }
        }
    }

    List<XMLFile> getXLSXFilesByCType(String content_type) {
        return xlsxfilesOfContentType.get(content_type);
    }

    List<XMLFile> getXmlFiles() {
        List<XMLFile> xmlfs = new ArrayList<>();
        for (List<XMLFile> xmlfs1 : xlsxfilesOfContentType.values()) {
            if (xmlfs1 != null) {
                xmlfs.addAll(xmlfs1);
            }
        }
        return xmlfs;
    }

    XMLFile getXLSXFile(String fileNmae) throws XLSXException {
        XMLFile xmlf = xlsxfileOfPath.get(fileNmae);
        if (xmlf == null) {
            XLSXException xlsxe = new XLSXException();
            xlsxe.addIdentity(new XLSXException.Identity(XLSXException.Identity.TYPE.NAME, fileNmae));
            xlsxe.setCauseType(XLSXException.CAUSETYPE.FILE_CORRUPTED);
            throw xlsxe;
        }
        return xmlf;
    }

    XLSXRepository getXLSXRepository(String fileName) throws XLSXException {
        XMLFile xmlf = getXLSXFile(fileName);
        return xmlf.getXlsxRepo();
    }

    XLSXRelationshipRepo getXLSXRelsRepository(String fileName) throws XLSXException {
        String relFilePath = XLSXFileUtility.getRelsFilPath(fileName);
        XLSXRepository xlsxrepo = getXLSXRepository(relFilePath);
        return (XLSXRelationshipRepo) xlsxrepo;
    }

    XLSXRelationshipRepo getXLSXRelsRepository(XMLFile xmlFile) throws XLSXException {
        return getXLSXRelsRepository(xmlFile.getFilePath());
    }

    public XMLFile getXMLFileByName(String name) {
        return this.xmlFileByName.get(name);
    }
}
