//$Id$
package com.adventnet.zoho.websheet.model.iterator;

import com.adventnet.zoho.websheet.model.ReadOnlyRow;
import com.adventnet.zoho.websheet.model.Row;
import com.adventnet.zoho.websheet.model.RowVisibility;
import com.adventnet.zoho.websheet.model.Sheet;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;

import static com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFROWS;
import static java.lang.Math.min;

/**
 * The class {@link #RowIterator} iterates the read only row from the given {@link #startRowIndex} and {@link #endRowIndex} in the {@link Sheet}.
 * <br><br>
 * <p>It iterates from the given {@link #startRowIndex} till the {@link #endRowIndex} possibly till the nearest repeated rows. Also, this doesn't truncate any of the rows repeated with the given {@link #endRowIndex}. The rows are restricted to maximum permitted limit i.e A new empty row is created and repeated is restricted to maximum limit.
 * However, the row which was repeated more than permitted limit will stay unchanged in object.
 * <br><br>
 * This is to ensure for the client to imagine that rows are created within the maximum permitted limit skipping the exceeded rows (Usually, occurs during an import).
 * </p>
 * You can use {@link #visibility} to filter the rows based on the visibility.
 * <br><br>
 * <b>Note:<b>
 * <br><br>
 * 1. This iterator will work only on current filter view only.
 * <br><br>
 * 2. This class doesn't create any rows. Instead, it provides read-only rows only for further manipulation of rows, response generation.
 * <AUTHOR> N J ( ZT-0049 )
 *
 */
public class RowIterator implements Iterator<ReadOnlyRow>
{
	private final Sheet sheet;
	private ReadOnlyRow nextRow;
	private int currentIndex;
	private final int startRowIndex;
	private final int endRowIndex;
	private final RowVisibility visibility;

	/**
	 * Option to invert the visibility check
	 */
	private Boolean invert = false;

	/**
	 *
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @param visibility - filter the rows based on the visibility.
	 */
	private RowIterator(Sheet sheet, int startRowIndex, int endRowIndex, RowVisibility visibility)
	{
		this.sheet = sheet;
		this.startRowIndex = startRowIndex;
		this.endRowIndex = min(endRowIndex, MAXNUMOFROWS - 1);
		this.currentIndex = startRowIndex;
		this.visibility = visibility;
	}

	/**
	 *
	 * @param sheet
	 * @param startRowIndex
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}
	 */
	private RowIterator(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		this(sheet, startRowIndex, endRowIndex, null);
	}

	/**
	 * Creates an instance of {@link RowIterator}. You can use this instance if you want to write your own custom iterator in future.
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @return
	 */
	public static RowIterator getInstance(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		return new RowIterator(sheet, startRowIndex, endRowIndex);
	}

	/**
	 * Creates an instance of {@link RowIterator}. You can use this instance if you want to write your own custom iterator in future.
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @param visibility
	 * @return
	 */
	public static RowIterator getInstance(Sheet sheet, int startRowIndex, int endRowIndex, RowVisibility visibility)
	{
		return new RowIterator(sheet, startRowIndex, endRowIndex, visibility);
	}

	/**
	 *
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @param visibility
	 * @return
	 */
	public static Iterator<ReadOnlyRow> getIterator(Sheet sheet, int startRowIndex, int endRowIndex, RowVisibility visibility)
	{
		return new RowIterator(sheet, startRowIndex, endRowIndex, visibility);
	}

	/**
	 *
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @return
	 */
	public static Iterator<ReadOnlyRow> getIterator(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		return new RowIterator(sheet, startRowIndex, endRowIndex);
	}

	/**
	 * Creates an iterator for visible rows
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @return
	 */
	public static Iterator<ReadOnlyRow> getVisibleRows(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		return new RowIterator(sheet, startRowIndex, endRowIndex, RowVisibility.VISIBLE);
	}

	/**
	 * Creates an iterator for hidden rows
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @return
	 */
	public static Iterator<ReadOnlyRow> getHiddenRows(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		return new RowIterator(sheet, startRowIndex, endRowIndex, RowVisibility.COLLAPSE);
	}

	/**
	 * Creates an iterator for hidden rows
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @return
	 */
	public static Iterator<ReadOnlyRow> getUnHiddenRows(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		return new RowIterator(sheet, startRowIndex, endRowIndex, RowVisibility.UN_HIDDEN);
	}

	/**
	 * Creates an iterator for filtered rows
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @return
	 */
	public static Iterator<ReadOnlyRow> getFilteredRows(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		return new RowIterator(sheet, startRowIndex, endRowIndex, RowVisibility.FILTER);
	}

	/**
	 * Creates an iterator for un-filtered rows
	 * @param sheet - Sheet
	 * @param startRowIndex - start index of the row.
	 * @param endRowIndex - Takes the minimum of {@code endRowIndex} and {@code Utility.MAXNUMBEROFROWS}.
	 * @return
	 */
	public static Iterator<ReadOnlyRow> getUnFilteredRows(Sheet sheet, int startRowIndex, int endRowIndex)
	{
		return new RowIterator(sheet, startRowIndex, endRowIndex, RowVisibility.UN_FILTERED);
	}

	/**
	 *
	 * @return {@link Iterator} for iterating the rows.
	 */
	public Iterator<ReadOnlyRow> iterator()
	{
		return this;
	}

	private Sheet getSheet()
	{
		return sheet;
	}

	public void setNextRow(ReadOnlyRow nextRow)
	{
		this.nextRow = nextRow;
	}

	private int getStartRowIndex()
	{
		return startRowIndex;
	}

	private int getEndRowIndex()
	{
		return endRowIndex;
	}

	public int getCurrentIndex()
	{
		return currentIndex;
	}

	private void setCurrentIndex(int currentIndex)
	{
		this.currentIndex = currentIndex;
	}

	private RowVisibility getVisibility()
	{
		return visibility;
	}

	private int getNextCurrentIndex(int currentIndex, ReadOnlyRow row)
	{
		return currentIndex + row.getRowsRepeated();
	}

	private void updateCurrentIndex(ReadOnlyRow row)
	{
		int currentIndex = this.getCurrentIndex();
		this.setCurrentIndex(this.getNextCurrentIndex(currentIndex, row));
	}

	private ReadOnlyRow restrictRowsRepeated(ReadOnlyRow row)
	{
		int rowsRepeated = row.getRowsRepeated();
		int rowIndex = row.getRowIndex();
		if(rowsRepeated + rowIndex > MAXNUMOFROWS)
		{
			rowsRepeated = rowsRepeated + (MAXNUMOFROWS - (rowsRepeated + rowIndex));
			row.setProperties(this.getSheet(), row.getRow(), row.getRowIndex(), -1, rowsRepeated);
		}
		return row;
	}

	/**
	 * Restricts the read-only row repeated's to maximum permitted limit i.e A new empty row is created and repeated is restricted to maximum limit.
	 * However, the row which was repeated more than permitted limit will stay unchanged.
	 * @param row
	 */
	private ReadOnlyRow updateRowsRepeated(ReadOnlyRow row)
	{
//		Sheet sheet = this.getSheet();
//		BitSet bitSets = new BitSet();
//		int currentIndex = this.getCurrentIndex();
//		int nextIndex = currentIndex + row.getRowsRepeated();
//		if(this.getVisibility() == null)
//		{
//			return restrictRowsRepeated(row);
//		}
//		switch(this.getVisibility())
//		{
//			case VISIBLE 	: 	bitSets = (BitSet)sheet.getHiddenRows().clone();
//								if(sheet.getFilterView() != null)
//								{
//									bitSets.or(sheet.getFilterView().getFilteredRowsSet());
//								}
//								break;
//
//			case COLLAPSE 	:	if(sheet.getHiddenRows().length() > 0)
//								{
//									bitSets = new BitSet(sheet.getHiddenRows().length() - 1);
//									bitSets.set(0, sheet.getHiddenRows().length() + 1);
//									bitSets.and(sheet.getHiddenRows());
//								}
//								break;
//
//			case UN_HIDDEN	: 	bitSets = (BitSet)sheet.getHiddenRows().clone();
//								break;
//
//			case FILTER		: 	FilterView filterView = this.getSheet().getFilterView();
//								if(filterView != null)
//								{
//									bitSets = new BitSet(filterView.getRange().getRowSize() - 1);
//									bitSets.set(this.getStartRowIndex(), filterView.getEndRowIndex() + 1);
//									bitSets.and(filterView.getFilteredRowsSet());
//								}
//								break;
//
//			case UN_FILTERED : 	bitSets = (BitSet)sheet.getFilterView().getFilteredRowsSet().clone();
//								break;
//		}
//		OptionalInt bitValue = bitSets.stream().filter(bit -> bit>=currentIndex && bit<nextIndex).sorted().findFirst();
//		if(bitValue.isPresent())
//		{
//			row = new ReadOnlyRow(this.getSheet(), row.getRow(), row.getRowIndex(), bitValue.getAsInt() - currentIndex + 1);
//		}
		return restrictRowsRepeated(row);
	}

	private ReadOnlyRow getRow(int index)
	{
		ReadOnlyRow readOnlyRow = this.getSheet().getReadOnlyRow(index);
		if(index >= this.getSheet().getRowNum())
		{
			readOnlyRow.setProperties(sheet, readOnlyRow.getRow(), index, -1, this.endRowIndex - index + 1);
		}
		return this.updateRowsRepeated(readOnlyRow);
	}

	private Boolean isRowMatch(ReadOnlyRow row)
	{
		if(this.getVisibility() == null)
		{
			return true;
		}
		Boolean condition = false;
		int rowIndex = row.getRowIndex();
		Sheet sheet = this.getSheet();
		switch(this.getVisibility())
		{
			case VISIBLE : condition = sheet.isRowVisible(rowIndex);
				break;
			case COLLAPSE : condition = sheet.isRowHidden(rowIndex);
				break;
			case FILTER : condition = sheet.isFilteredRowsOnly(rowIndex);
				break;
			case UN_HIDDEN : condition = !sheet.isRowHidden(rowIndex);
				break;
			case UN_FILTERED : condition = !sheet.isFilteredRow(rowIndex);
				break;
		}
		return condition;
	}

	private Boolean isNextRowAvailable()
	{
		while(currentIndex <= this.getEndRowIndex())
		{
			ReadOnlyRow row = this.getRow(currentIndex);
			Boolean isAvailable = this.isRowMatch(row);
			if(isAvailable)
			{
				this.setCurrentIndex(currentIndex);
				this.setNextRow(row);
				return true;
			}
			currentIndex = this.getNextCurrentIndex(currentIndex, row);
		}
		return false;
	}
	
	/**
	 * Checks if the current index hasn't crossed the end row index.
	 * @return true - if {@code currentIndex} is greater than {@code endRowIndex} or false otherwise
	 */
	private Boolean isCurrentIndexOutOfBound()
	{
		return this.getCurrentIndex() > this.getEndRowIndex();
	}
	
	/**
	 * Computes the next available row based on {@code visibility}
	 * @return null - if no row is matched
     * @throws NoSuchElementException if the iteration has no more elements i.e current row index has reached the end row index.
	 */
	private ReadOnlyRow getNextRow() throws NoSuchElementException
	{
		if(this.isCurrentIndexOutOfBound())
		{
			throw new NoSuchElementException("Next row in the iterator is not available.");	// No I18N 
		}
		ReadOnlyRow row = this.nextRow;
		this.updateCurrentIndex(row);
		return isRowMatch(row) ? row : null;
	}
	
	/**
	 * @return true - if current row index is within the end row index or false otherwise
	 */
	@Override
	public boolean hasNext()
	{
		return !this.isCurrentIndexOutOfBound() && this.isNextRowAvailable();
	}
	
	/**
	 * Returns the next row matching the given visibility.
	 * @return {@link Row}, null - if iterator has reached the end of the index or no match is found.
     * @throws NoSuchElementException if the iteration has no more elements i.e current row index has reached the end row index.
	 */
	@Override
	public ReadOnlyRow next() throws NoSuchElementException
	{
		return this.getNextRow();
	}
	
	/**
	 * Creates and returns a list of rows instead of iterating it.
	 * <br><br>
	 * <b>Note: Use this method only if you need to return the list instead of doing any computations.
	 * Usage of {@link #iterator()} is highly recommended.<b>  
	 * @return
	 */
	public List<ReadOnlyRow> getRows()
	{
		List<ReadOnlyRow> rows = new ArrayList<ReadOnlyRow>();
		while(this.hasNext())
		{
			rows.add(this.next());
		}
		return rows;
	}
}
