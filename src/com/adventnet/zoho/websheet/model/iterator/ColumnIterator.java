/* $Id$ */
package com.adventnet.zoho.websheet.model.iterator;

import com.adventnet.zoho.websheet.model.Column;
import com.adventnet.zoho.websheet.model.ColumnVisibility;
import com.adventnet.zoho.websheet.model.ReadOnlyColumnHeader;
import com.adventnet.zoho.websheet.model.Sheet;

import java.util.Iterator;
import java.util.NoSuchElementException;

import static com.adventnet.zoho.websheet.model.util.Utility.MAXNUMOFCOLS;
import static java.lang.Math.min;

/**
 * <AUTHOR> N J (ZT-0049)
 * <AUTHOR>
 */
public class ColumnIterator implements Iterator<ReadOnlyColumnHeader>
{
    private Sheet sheet;
    private int startColumnIndex;
    private int endColumnIndex;
    private int currentIndex;
    private ColumnVisibility visibility;
    private ReadOnlyColumnHeader nextColumnHeader;

    public Sheet getSheet() {
        return sheet;
    }

    public void setSheet(Sheet sheet) {
        this.sheet = sheet;
    }

    public int getStartColumnIndex() {
        return startColumnIndex;
    }

    public void setStartColumnIndex(int startColumnIndex) {
        this.startColumnIndex = startColumnIndex;
    }

    public int getEndColumnIndex() {
        return endColumnIndex;
    }

    public void setEndColumnIndex(int endColumnIndex)
    {
        this.endColumnIndex = min(endColumnIndex, MAXNUMOFCOLS - 1);
    }

    public int getCurrentIndex() {
        return currentIndex;
    }

    public void setCurrentIndex(int currentIndex) {
        this.currentIndex = currentIndex;
    }

    public ColumnVisibility getVisibility() {
        return visibility;
    }

    public void setVisibility(ColumnVisibility visibility) {
        this.visibility = visibility;
    }

    public ReadOnlyColumnHeader getNextColumnHeader() {
        return nextColumnHeader;
    }

    public void setNextColumnHeader(ReadOnlyColumnHeader nextColumnHeader) {
        this.nextColumnHeader = nextColumnHeader;
    }

    public ColumnIterator(Sheet sheet, int startColumnIndex, int endColumnIndex) {
        this(sheet, startColumnIndex, endColumnIndex, null);
    }

    public ColumnIterator(Sheet sheet, int startColumnIndex, int endColumnIndex, ColumnVisibility visibility) {
        this.sheet = sheet;
        this.startColumnIndex = startColumnIndex;
        this.setEndColumnIndex(endColumnIndex);
        this.currentIndex = startColumnIndex;
        this.visibility = visibility;
    }

    public static ColumnIterator getInstance(Sheet sheet, int startColumnIndex, int endColumnIndex)
    {
        return new ColumnIterator(sheet, startColumnIndex, endColumnIndex);
    }

    public static ColumnIterator getInstance(Sheet sheet, int startColumnIndex, int endColumnIndex, ColumnVisibility visibility)
    {
        return new ColumnIterator(sheet, startColumnIndex, endColumnIndex, visibility);
    }

    public Iterator<ReadOnlyColumnHeader> iterator()
    {
        return this;
    }

    private int getNextCurrentIndex(int currentIndex, ReadOnlyColumnHeader columnHeader)
    {
        return currentIndex + columnHeader.getColsRepeated();
    }

    private void updateCurrentIndex(ReadOnlyColumnHeader col)
    {
        int currentIndex = this.getCurrentIndex();
        this.setCurrentIndex(this.getNextCurrentIndex(currentIndex, col));
    }

    private ReadOnlyColumnHeader restrictColsRepeated(ReadOnlyColumnHeader columnHeader)
    {
        int colsRepeated = columnHeader.getColsRepeated();
        int colIndex = columnHeader.getColIndex();
        if(colsRepeated + colIndex > MAXNUMOFCOLS)
        {
            colsRepeated = colsRepeated + (MAXNUMOFCOLS - (colsRepeated + colIndex));
            ReadOnlyColumnHeader readOnlyColumnHeader = new ReadOnlyColumnHeader(this.getSheet(), columnHeader.getColumnHeader(), columnHeader.getColIndex(), colsRepeated);
            return readOnlyColumnHeader;
        }
        return columnHeader;
    }

    /**
     * Restricts the read-only col repeated's to maximum permitted limit i.e A new empty col is created and repeated is restricted to maximum limit.
     * However, the col which was repeated more than permitted limit will stay unchanged.
     * @param columnHeader
     */
    private ReadOnlyColumnHeader updateColsRepeated(ReadOnlyColumnHeader columnHeader)
    {
        return restrictColsRepeated(columnHeader);
    }

    private ReadOnlyColumnHeader getColumnHeader(int index)
    {
        ReadOnlyColumnHeader readOnlyColumnHeader = this.getSheet().getReadOnlyColumnHeader(index);
        if(index >= this.getSheet().getColNum())
        {
            readOnlyColumnHeader = new ReadOnlyColumnHeader(sheet, readOnlyColumnHeader.getColumnHeader(), index, this.endColumnIndex - index + 1);
        }
        return this.updateColsRepeated(readOnlyColumnHeader);
    }

    private Boolean isColMatch(ReadOnlyColumnHeader columnHeader)
    {
        if(this.getVisibility() == null)
        {
            return true;
        }
        Boolean condition = false;
        int colIndex = columnHeader.getColIndex();
        Sheet sheet = this.getSheet();
        switch(this.getVisibility())
        {
            case VISIBLE : condition = sheet.isColumnVisible(colIndex);
                break;
            case COLLAPSE : condition = sheet.isColumnHidden(colIndex);
                break;
        }
        return condition;
    }

    private Boolean isNextColAvailable()
    {
        while(currentIndex <= this.getEndColumnIndex())
        {
            ReadOnlyColumnHeader columnHeader = this.getColumnHeader(currentIndex);
            Boolean isAvailable = this.isColMatch(columnHeader);
            if(isAvailable)
            {
                this.setCurrentIndex(currentIndex);
                this.setNextColumnHeader(columnHeader);
                return true;
            }
            currentIndex = this.getNextCurrentIndex(currentIndex, columnHeader);
        }
        return false;
    }

    /**
     * Checks if the current index hasn't crossed the end col index.
     * @return true - if {@code currentIndex} is greater than {@code endColIndex} or false otherwise
     */
    private Boolean isCurrentIndexOutOfBound()
    {
        return this.getCurrentIndex() > this.getEndColumnIndex();
    }

    /**
     * Computes the next available col based on {@code visibility}
     * @return null - if no col is matched
     * @throws NoSuchElementException if the iteration has no more elements i.e current col index has reached the end col index.
     */
    private ReadOnlyColumnHeader getNextCol() throws NoSuchElementException
    {
        if(this.isCurrentIndexOutOfBound())
        {
            throw new NoSuchElementException("Next Col in the iterator is not available.");	// No I18N
        }
        ReadOnlyColumnHeader columnHeader = this.nextColumnHeader;
        this.updateCurrentIndex(columnHeader);
        return isColMatch(columnHeader) ? columnHeader : null;
    }

    /**
     * @return true - if current col index is within the end col index or false otherwise
     */

    @Override
    public boolean hasNext()
    {
        return !this.isCurrentIndexOutOfBound() && this.isNextColAvailable();
    }

    /**
     * Returns the next col matching the given visibility.
     * @return {@link Column}, null - if iterator has reached the end of the index or no match is found.
     * @throws NoSuchElementException if the iteration has no more elements i.e current col index has reached the end col index.
     */
    @Override
    public ReadOnlyColumnHeader next() throws NoSuchElementException
    {
        return this.getNextCol();
    }
}
