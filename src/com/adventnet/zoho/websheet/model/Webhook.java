//$Id$
package com.adventnet.zoho.websheet.model;

import com.adventnet.ds.query.Table;
import com.adventnet.ds.query.*;
import com.adventnet.persistence.DataObject;
import com.adventnet.persistence.Persistence;
import com.adventnet.persistence.Row;
import com.adventnet.zoho.websheet.model.ext.IntegralMap;
import com.adventnet.zoho.websheet.model.util.*;
import com.zoho.sheet.api.dataapi.DataAPIResponse;
import com.zoho.sheet.authorization.util.AuthorizationUtil;
import com.zoho.sheet.util.SheetPersistenceUtils;
import com.zoho.zfsng.client.ResourcePermissionInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ShareConstants;
import org.json.JSONObject;

import java.io.DataOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class Webhook
{
    private static final Logger LOGGER = Logger.getLogger(Webhook.class.getName());
    private static final int MAX_DELAY_TIME = 60*1000;
    private long id; //webhook id
    private Event event;
    private String url;
    private DataRange webhookRange = null; //default null. It is not required for event like new sheet or new workbook
    private String webhookDetails = null;
    private String serviceName = null;
    private long createdBy; //zuid
    private int lastRecordedIndex = -1;
    private boolean isSaveRequired = false;
    private Map<Integer, Long> delayTimerMap = null;
    //private volatile RowStateKeeper rowStatesKeeper;
    
    //TODO: No. of thread to be added in conf
    private static final ExecutorService EXECUTOR_SERVICE = Executors.newFixedThreadPool(EngineConstants.WEBHOOK_THREAD_POOL_SIZE);
    
    public enum Event
    {
        NEW_WORKBOOK,
        NEW_WORKSHEET,
        //UPDATE_RANGE,
        NEW_ROW,
        UPDATE_WORKSHEET,
        UPDATE_COLUMNS,
        ADD_COMMENT,
        DELETE_COMMENT,
        EDIT_COMMENT,
        RESOLVE_COMMENT,
        REPLY_COMMENT
    }
    
    public enum RequestType
    {
        POST,
        DELETE
    }
    
    private static enum RowState    //used for update_worksheet, and new_row
    {
        INCOMPLETE,
        NEWLY_ADDED,
        EDITED
    }
    
    //public Webhook(Event event, String url, DataRange webhookRange, String serviceName, RowStateKeeper initialRowStateKeeper)
    public Webhook(Event event, String url, DataRange webhookRange, String serviceName, long createdBy, int lastRecordedIndex)
    {
        this(-1, event, url, webhookRange, serviceName, createdBy, lastRecordedIndex);
    }
    
    //public Webhook(long id, Event event, String url, DataRange webhookRange, String serviceName, RowStateKeeper initialRowStateKeeper)
    public Webhook(long id, Event event, String url, DataRange webhookRange, String serviceName, long createdBy, int lastRecordedIndex)
    {
        this.id = id;
        this.event = event;
        this.url = url;
        this.serviceName = serviceName;
        this.createdBy = createdBy;
        this.lastRecordedIndex = lastRecordedIndex;
        if(webhookRange != null)
        {
            this.webhookRange = webhookRange;
//            this.webhookDetails = this.webhookRange.getDataRangeAsStringForWebhook()+" : "+this.lastRecordedIndex;
        }
//        if(initialRowStateKeeper != null) {
//            this.rowStatesKeeper = initialRowStateKeeper;
//        }
    }
    
    public Webhook(Event event, String url, String webhookDetails, String serviceName, long createdBy) 
    {
    	this(-1, event, url, webhookDetails, serviceName, createdBy);
    }
    
    public Webhook(long id, Event event, String url, String webhookDetails, String serviceName, long createdBy)
    {
    	this(id, event, url, null, serviceName, createdBy, -1);
    	this.webhookDetails = webhookDetails;
    }

    
    public Webhook(String url, String serviceName, long createdBy)
    {
        this.id = -1;
        this.event = Event.NEW_WORKBOOK;
        this.url = url;
        this.serviceName = serviceName;
        this.createdBy = createdBy;
//        this.webhookDetails = "";
    }
    
    public Event getEvent()
    {
        return this.event;
    }
    
    public String getUrl()
    {
        return this.url;
    }
    
    public String getServiceName()
    {
        return this.serviceName;
    }
    
    public DataRange getWebhookRange()
    {
        return this.webhookRange;
    }
    
    public String getWebhookDetails()
    {
    	return this.webhookDetails;
    }
    
    public long getId()
    {
        return this.id;
    }
    
    public int getLastRecordedIndex()
    {
        return this.lastRecordedIndex;
    }
    
    public void setLastRecordedIndex(int lastRecordedIndex)
    {
        this.lastRecordedIndex = lastRecordedIndex;
    }
    
    public boolean getIsSaveRequired()
    {
        return this.isSaveRequired;
    }
    
    public void setIsSaveRequired(boolean isSaveRequired)
    {
        this.isSaveRequired = isSaveRequired;
    }
    
    public Map getDelayTimerMap()
    {
        return this.delayTimerMap;
    }
    
    public void setDelayTimer(int startRow, int endRow, long time)
    {
        LOGGER.log(Level.INFO, "setting delay timer for webhook :: id : {0} :: start row : {1} :: end row : {2}", new Object[]{this.getId(), startRow, endRow});
        if(startRow <= endRow)
        {
            if(this.delayTimerMap == null)
            {
                delayTimerMap = new HashMap();
            }
            for(int i = startRow; i <= endRow; i++)
            {
                delayTimerMap.put(i, time);
            }
        }
    }
    
    public long getCreatedBy()
    {
        return this.createdBy;
    }
    
    public long subscribe(String docOwner, String rid)
    {
        try
        {
            Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
            DataObject dObj = persistence.constructDataObject();
            Row row = new Row("Webhooks");      //No I18N
            row.set("EVENT", event.toString()); //No I18N
            row.set("WEBHOOK_URL", url);        //No I18N
            row.set("RESOURCE_ID", rid);        //No I18N
            row.set("WEBHOOK_DETAILS", getWebhookDetailsForDB());    //No I18N
            //row.set("WEBHOOK_DETAILS", webhookDetails);    //No I18N
            row.set("SERVICE_NAME", serviceName);   //No I18N
            row.set("CREATED_BY", createdBy);   //No I18N
            row.set("STATUS", 1);               //No I18N
            row.set("CREATED_TIME", System.currentTimeMillis());    //No I18N
            dObj.addRow(row);
            persistence.add(dObj);
            this.id = (Long)row.get("WEBHOOK_ID");
            // The call to subscribe can be from an API and the response with status code will be sent via API only.
        }
        catch(Exception e)
        {
            LOGGER.log(Level.WARNING, "Unable to subscribe webhook", e);
            return -1;
        }
        return this.id;  //It returns the newly created webhooks id
    }
    
    private String getWebhookDetailsForDB() {
    	String webhookDetails;
    	switch(event) {
    		case ADD_COMMENT:
    		case DELETE_COMMENT:
    		case REPLY_COMMENT:
    		case RESOLVE_COMMENT:
    		case EDIT_COMMENT:
    			webhookDetails = this.webhookDetails;
    			break;
    		default:
    			webhookDetails = (this.webhookRange == null) ? null : this.webhookRange.getDataRangeAsStringForWebhook()+" : "+this.lastRecordedIndex;    //No I18N
				break;
    	}
		return webhookDetails;
	}

	public void save(String docOwner)
    {
        try
        {
            Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
            UpdateQuery uq = new UpdateQueryImpl("Webhooks"); // No I18N
            uq.setUpdateColumn("WEBHOOK_DETAILS", getWebhookDetailsForDB()); // No I18N
            uq.setCriteria(new Criteria(new com.adventnet.ds.query.Column("Webhooks", "WEBHOOK_ID"), this.id, QueryConstants.EQUAL));
            persistence.update(uq);
        }
        catch(Exception e)
        {
            LOGGER.log(Level.WARNING, "Unable to save workbook", e);
        }
    }
    
    public void reverseUnsubscribe(String docOwner) throws Exception
    {
        // This method is required if the document or the sheet registered for update event is deleted. New document webhooks must not be deleted with this
        unsubscribeUsingID(docOwner);
        this.execute(RequestType.DELETE, null);
        LOGGER.log(Level.INFO, "reverse unsubscribe call as the sheet id matches : id : {0}", id);
    }
    
    //public void executeDelay(WorkbookContainer container) throws Exception
    public boolean executeDelay(WorkbookContainer container) throws Exception
    {
        if(delayTimerMap != null)
        {
            Workbook workbook = container.getWorkbook(null);
            Long currTime = System.currentTimeMillis();

            Set keySet = delayTimerMap.keySet();
            List<Integer> keyList = new ArrayList<>(keySet);
            Collections.sort(keyList);
            Collections.reverse(keyList);

            Iterator<Integer> itr = keyList.iterator();
            boolean isWebhook = false;
            int completeRowIndex = getLastRecordedIndex();
            while(itr.hasNext())
            {
                int lastIndex = itr.next();
                if(isWebhook || lastIndex <= completeRowIndex)
                {
                    delayTimerMap.remove(lastIndex);
                    continue;
                }
                if(currTime - delayTimerMap.get(lastIndex) > MAX_DELAY_TIME)
                {
                    boolean isPermitted = true;
                    boolean isLocked = false;   //check in check out case
                    String webhookCreatorZUID = String.valueOf(createdBy);
                    
                    if(createdBy == -1)
                    {
                        isPermitted = true;
                    }
                    else    //need to check permission and lock info if createdBy is available
                    {
                        try
                        {
                            isPermitted = isWebhookPermitted(container, webhookCreatorZUID);
                        }
                        catch(SocketTimeoutException ste)
                        {
                            LOGGER.log(Level.INFO, "continue due to socket timeout error in execute delay");
                            continue;
                        }
                        isLocked = !AuthorizationUtil.isLockOwner(container, webhookCreatorZUID);
                    }

                    isWebhook = true;
                    delayTimerMap.remove(lastIndex);

                    // fire webhook
                    if(isPermitted)
                    {
                        if(!isLocked)   //webhook should not be disabled if locked
                        {
                            String payload = "";
                            DataRange updateRange = new DataRange(webhookRange.getAssociatedSheetName(), completeRowIndex + 1, webhookRange.getStartColIndex(), lastIndex, webhookRange.getEndColIndex());
                            LOGGER.log(Level.INFO, "Webhook fire from scheduler :: range : {0} :: webhook id : {1} :: update range size : {2}", new Object[]{updateRange.getDataRangeAsStringForWebhook(), this.getId(), updateRange.getSize()});
                            JSONObject payloadObj = (updateRange.getSize() < 10000) ? DataAPIResponse.getWebhookPayloadForUpdate(workbook, updateRange, webhookRange.getStartRowIndex(), completeRowIndex) : new JSONObject();
                            DataAPIResponse.addWebhookDetails(payloadObj, getId(), getServiceName(), getEvent().toString().toLowerCase());
                            payload = payloadObj.toString();
                            setLastRecordedIndex(lastIndex);

                            //Note : cannot set isSaveRequired as true, and instead save is called, as play back action will not execute the scheduler service and webhook will fire again if the document is not saved.
                            this.save(container.getDocOwner());
                            this.execute(RequestType.POST, payload);
                            LOGGER.log(Level.INFO, "Executing webhook from executeDelay method : id : {0}", id);
                        }
                        else
                        {
                            LOGGER.log(Level.INFO, "webhook not executed from executeDelay method as the resocure is locked: id : {0}", id);
                        }
                    }
                    else
                    {
                        LOGGER.log(Level.INFO, "permission not granted for exeuting webhook in execute delay : id : {0}", id);
                        return false;
                        //unsubscrite it (check if need to unsubscribe)
                    }
                }
            }

            keyList = null;
        }
        //else{System.out.println("delay timer map is null");}
        return true;
    }
    
    public void execute(RequestType requestType, String payload)
    {
        if(payload == null)
        {
            payload = "";
        }
        LOGGER.log(Level.INFO, "Webhook trigger :: id : {0}", id);
        Runnable webhookExecutor = new WebhookExecutor(url, requestType, payload);
        EXECUTOR_SERVICE.execute(webhookExecutor);
        //executorService.shutdown();
    }
    
    private static class WebhookExecutor implements Runnable
    {
        String url;
        RequestType requestType;
        String payload;
        
        WebhookExecutor(String url, RequestType requestType, String payload)
        {
            this.url = url;
            this.requestType = requestType;
            this.payload = payload;
        }
        
        @Override
        public void run()
        {
            //LOGGER.log(Level.INFO, "********************************");
            LOGGER.log(Level.INFO, "webhook executor run: {0}", payload.length());
            HttpURLConnection connection = null;
            DataOutputStream paramStream = null;
            try
            {
                //Create connection
                URL urlObj = new URL(url);
                connection = (HttpURLConnection) urlObj.openConnection();
                connection.setRequestMethod(requestType.toString());
                connection.setRequestProperty("Content-type", "application/json");   //No I18N

                connection.setDoInput(true);
                connection.setDoOutput(true);
                connection.setRequestProperty("Content-length", String.valueOf(payload.length()));   //No I18N

                connection.setReadTimeout(6000);
                connection.setConnectTimeout(6000);

                //Send request
                if(!payload.equals(""))
                {
                    paramStream = new DataOutputStream(connection.getOutputStream());
                    //paramStream.writeBytes(payload);
                    paramStream.write(payload.getBytes("UTF-8"));   //No I18N
                    paramStream.close();
                }
                int responseCode = connection.getResponseCode();
                LOGGER.log(Level.INFO, "responseCode: {0}", responseCode);
            }
            catch (Exception e)
            {
                //LOGGER.log(Level.WARNING, "connection error while sending webhook :: url : {0}", url);
                LOGGER.log(Level.INFO, "!!!!!!!!!!!!exception in sendWebhook : " + e.getMessage(), e);
            }
            finally
            {
                if (connection != null)
                {
                    connection.disconnect();
                }
                if (paramStream != null)
                {
                    try
                    {
                        paramStream.close();
                    }
                    catch(IOException e){/*Need not handle*/}
                }
            }
        }
    }
    
//        @Override
//        public void run()
//        {
//            try
//            {
//                System.out.println("webhook run start : "+url);
//                ZSConnection zsConn = (new ZSConnectionFactory(url, "POST")).getConnection();//No I18N
//                zsConn.setContentLength(String.valueOf(payload.length()));
//                zsConn.setContentType("application/json");//No I18N
//                zsConn.setConnectionTimeout(2000);
//                zsConn.setReadTimeout(2000);
//
//                //Send request
//                if(!payload.equals(""))
//                {
//                	  zsConn.setWrittingBytes(payload);
//                }
//                int responseCode = zsConn.getResponseCode();
//                System.out.println("*********************************************************************************");
//                System.out.println("response code : "+responseCode);
//            }
//            catch (Exception e)
//            {
//                LOGGER.log(Level.INFO, "!!!!!!!!!!!!exception in sendWebhook : " + e.getMessage(), e);
//            }
//        }
//    }
    
    public static boolean unsubscribeNewDocWebhook(String userName, long id, String url) throws Exception
    {
        LOGGER.log(Level.INFO, "unsubscribeNewDocWebhook username : {0} :: id : {1} :: url : {2}", new Object[]{userName, id, url});
        boolean isSuccess = false;
        //boolean isMoreWebhook = false;
        //JSONObject jObj = new JSONObject();
        SelectQueryImpl sql = new SelectQueryImpl(new Table("Webhooks")); //No I18N
        sql.addSelectColumn(new com.adventnet.ds.query.Column("Webhooks", "EVENT")); //No I18N
        sql.addSelectColumn(new com.adventnet.ds.query.Column("Webhooks", "WEBHOOK_ID")); //No I18N
        Criteria crt1 = new Criteria(new com.adventnet.ds.query.Column("Webhooks", (id == -1) ? "WEBHOOK_URL" : "WEBHOOK_ID"), (id == -1) ? url : Long.valueOf(id), QueryConstants.EQUAL);
        Criteria crt2 = new Criteria(new com.adventnet.ds.query.Column("Webhooks", "EVENT"), "NEW_WORKBOOK", QueryConstants.EQUAL);
        Criteria crt = crt1.and(crt2);
        sql.setCriteria(crt);
        Persistence persistence = SheetPersistenceUtils.getPersistence(userName);
        DataObject dataObj = persistence.get(sql);
        if(!dataObj.isEmpty())
        {
            isSuccess = true;
            persistence.delete(crt);
        }
        return isSuccess;
    }
    
    public void unsubscribeUsingID(String docOwner) throws Exception
    {
        Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
        Criteria criteria = new Criteria(new com.adventnet.ds.query.Column("Webhooks", "WEBHOOK_ID"), this.id, QueryConstants.EQUAL);
        persistence.delete(criteria);
        // This method also can be called through an API or from reverseUnsubscribe method with id representing the webhook id.
    }
    
    public static void unsubscribeUsingUrl(String docOwner, String url) throws Exception
    {
        Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
        Criteria crt1 = new Criteria(new com.adventnet.ds.query.Column("Webhooks", "WEBHOOK_URL"), url, QueryConstants.EQUAL);
        Criteria crt2 = new Criteria(new com.adventnet.ds.query.Column("Webhooks", "EVENT"), "NEW_WORKBOOK", QueryConstants.NOT_EQUAL);   // must not delete new workbook webhooks. This method can be accessed by shared user unsubscribe webhook api
        Criteria criteria = crt1.and(crt2);
        persistence.delete(criteria);
    }
    
    public static void enableDisableWebhook(String docOwner, long webhookId, boolean isDisable) throws Exception
    {
        //Date date = new Date(System.currentTimeMillis());
        LOGGER.log(Level.INFO, "enabledisable webhook call : webhookId : {0}", webhookId);
        Persistence persistence = SheetPersistenceUtils.getPersistence(docOwner);
        
        UpdateQuery uq = new UpdateQueryImpl("Webhooks"); // No I18N
        uq.setUpdateColumn("STATUS", (isDisable) ? 0 : 1); // No I18N
        uq.setUpdateColumn("STATUS_CHANGE_TIME", System.currentTimeMillis()); // No I18N
        Criteria updateCri = new Criteria(new com.adventnet.ds.query.Column("Webhooks", "WEBHOOK_ID"), webhookId, QueryConstants.EQUAL); // No I18N
        uq.setCriteria(updateCri);
        persistence.update(uq);
    }
    
//    public static void unsubscribe(String rid, long id, String url) throws Exception
//    {
//        String docOwner = DocumentUtils.getDocOwnerFromRid(rid);
//        unsubscribeUsingDocOwner(docOwner, id, url);
//        LOGGER.log(Level.INFO, "webhook unsubscribed : rid : {0} webhook id : {1}  url : {2}", new Object[]{rid, id, url});
//    }
    
//    public static void checkAndExecuteUpdateEvent(Workbook workbook, List<Range> ranges)
//    {
//        //below for loop for update event. Need different logic for new event
//        for(Webhook webhook : workbook.getWebhooks())
//        {
//            if(webhook.rowStatesKeeper != null) {
//                List<DataRange> dataRanges = ranges.stream().map(Range::toDataRange).collect(Collectors.toList());
//                SortedMap<Integer, RowStateKeeper.State> newStatesMap = webhook.rowStatesKeeper.notifyEditAt(workbook, dataRanges);
//                // TODO : send payload for newStates accordingly
//            }
//            
//            DataRange webhookRange = webhook.getWebhookRange();
//            //if(webhookRange != null && range != null && webhookRange.isInRange(range.toDataRange()))
//            for(Range range : ranges)
//            {
//                if(webhookRange != null && range != null)
//                {
//                    DataRange intersectionRange = RangeUtil.intersection(webhookRange, range.toDataRange());
//                    if(intersectionRange != null)
//                    {
//                        try
//                        {
//                            String payload = "";
//                            if(intersectionRange.getSize() < 1000)
//                            {
//                                    //TODO: if there is any change in the header row, probabl I should not trigger for any change in the header row.
//                                    //payload = DataAPIResponse.getRangeAsArray(workbook.getSheetByAssociatedName(intersectionRange.getAssociatedSheetName()), intersectionRange.getStartRowIndex(), intersectionRange.getStartColIndex(), intersectionRange.getEndRowIndex(), intersectionRange.getEndColIndex()).toString();
//                                    JSONObject payloadObj = DataAPIResponse.getWebhookPayloadForUpdate(workbook.getSheetByAssociatedName(intersectionRange.getAssociatedSheetName()), webhookRange.getStartRowIndex(), intersectionRange.getStartRowIndex(), webhookRange.getStartColIndex(), intersectionRange.getEndRowIndex(), webhookRange.getEndColIndex());
//                                    DataAPIResponse.addWebhookDetails(payloadObj, webhook.getId(), webhook.getServiceName(), webhook.getEvent().toString().toLowerCase());
//                                    payload = payloadObj.toString();
//                            }
//                            webhook.execute(RequestType.POST, payload);
//                        }
//                        catch(Exception e)
//                        {
//                            LOGGER.log(Level.WARNING, "could not execute update event due to an exception: ", e);
//                        }
//                    }
//                }
//            }
//        }
//    }
    
    //public static void checkAndExecuteUpdateEvent(Workbook workbook, List<Range> ranges, boolean isPlayBack)
    public static void checkAndExecuteUpdateEvent(WorkbookContainer container, List<Range> ranges, boolean isPlayBack)
    {
        Workbook workbook = container.getWorkbookForSave();
        if(workbook != null)
        {
            //for(Webhook webhook : workbook.getWebhooks())
            List<Webhook> webhooks = workbook.getWebhooks();
            if(!(webhooks == null || webhooks.isEmpty()))
            {
                Iterator<Webhook> itr = webhooks.iterator();
                Webhook webhook;
                while(itr.hasNext())
                {
                    webhook = itr.next();
                    DataRange webhookRange = webhook.getWebhookRange();
                    if(webhookRange != null)
                    {
                        for(Range range : ranges)
                        {
                            if(range != null)
                            {
                                DataRange intersectionRange = RangeUtil.intersection(webhookRange, range.toDataRange());
                                if(intersectionRange != null)
                                {
                                    Event event = webhook.getEvent();
                                    int lastCompletedRowIndex = -1;
                                    int lastIncompleteRowIndex = -1;
                                    if(!event.equals(Event.UPDATE_COLUMNS))
                                    {
                                        if(intersectionRange.getEndRowIndex() > webhook.getLastRecordedIndex())
                                        {
                                            IntegralMap<RowState> rowState = getStatesMapForRange(workbook, new DataRange(webhookRange.getAssociatedSheetName(), webhook.getLastRecordedIndex() + 1, webhookRange.getStartColIndex(), intersectionRange.getEndRowIndex(), webhookRange.getEndColIndex()));
                                            Map<Integer, RowState> map = rowState.toMap();

                                            for(Map.Entry<Integer, RowState> entry : map.entrySet())
                                            {
                                                RowState state = entry.getValue();
                                                if(state.equals(RowState.NEWLY_ADDED))
                                                {
                                                    lastCompletedRowIndex = entry.getKey();
                                                }
                                                else if(state.equals(RowState.INCOMPLETE))
                                                {
                                                    lastIncompleteRowIndex = entry.getKey();
                                                }
                                            }
                                        }
                                    }

                                    if(!isPlayBack)
                                    {
                                        DataRange updateRange = null;

                                        switch(event)
                                        {
                                            case NEW_ROW:
                                                updateRange = (lastCompletedRowIndex == -1) ? null : new DataRange(intersectionRange.getAssociatedSheetName(), webhook.getLastRecordedIndex() + 1, webhookRange.getStartColIndex(), lastCompletedRowIndex, webhookRange.getEndColIndex());
                                                break;
                                            case UPDATE_WORKSHEET:
                                                updateRange = (lastCompletedRowIndex == -1 && intersectionRange.getStartRowIndex() > webhook.getLastRecordedIndex()) ? null : new DataRange(intersectionRange.getAssociatedSheetName(), intersectionRange.getStartRowIndex(), webhookRange.getStartColIndex(), (lastCompletedRowIndex == -1) ? Math.min(intersectionRange.getEndRowIndex(), webhook.getLastRecordedIndex()) : lastCompletedRowIndex, webhookRange.getEndColIndex());
                                                break;
                                            case UPDATE_COLUMNS:
                                                JSONObject colsJson = WebhookUtils.getStartAndEndColOfRecord(range.getSheet(), webhookRange.getStartRowIndex(), webhookRange.getStartColIndex(), webhookRange.getEndColIndex());
                                                updateRange = new DataRange(intersectionRange.getAssociatedSheetName(), intersectionRange.getStartRowIndex(), colsJson.getInt(JSONConstants.START_COLUMN), intersectionRange.getEndRowIndex(), colsJson.getInt(JSONConstants.END_COLUMN));
                                                break;
                                        }

                                        if(updateRange != null)
                                        {
                                            try
                                            {
                                                String payload = "";
                                                boolean isPermitted = false;
                                                boolean isLocked = false;   //check in check out case
                                                long createdBy = webhook.getCreatedBy();
                                                String webhookCreatorZUID = String.valueOf(createdBy);
                                                
                                                if(createdBy == -1)
                                                {
                                                    isPermitted = true;
                                                }
                                                else    //need to check permission and lock info if createdBy is available
                                                {
                                                    try
                                                    {
                                                        isPermitted = isWebhookPermitted(container, webhookCreatorZUID);
                                                    }
                                                    catch(SocketTimeoutException ste)
                                                    {
                                                        return;
                                                    }
                                                    isLocked = !AuthorizationUtil.isLockOwner(container, webhookCreatorZUID);
                                                }
                                                if(isPermitted)
                                                {
                                                    if(!isLocked)   //webhook should not be disabled if locked
                                                    {
                                                        //TODO: if there is any change in the header row, probabl I should not trigger for any change in the header row.
                                                        JSONObject payloadObj = (updateRange.getSize() < 10000) ? DataAPIResponse.getWebhookPayloadForUpdate(workbook, updateRange, webhookRange.getStartRowIndex(), webhook.getLastRecordedIndex(), event) : new JSONObject();
                                                        DataAPIResponse.addWebhookDetails(payloadObj, webhook.getId(), webhook.getServiceName(), webhook.getEvent().toString().toLowerCase());
                                                        payload = payloadObj.toString();
                                                        webhook.execute(RequestType.POST, payload);
                                                        LOGGER.log(Level.INFO, "Executing webhook from checkAndExecuteUpdateEvent method :: range : {0} :: id : {1} :: update range size : {2}", new Object[]{updateRange.getDataRangeAsStringForWebhook(), webhook.getId(), updateRange.getSize()});
                                                    }
                                                    else    //not continuing in else as last complete row index will increase
                                                    {
                                                        LOGGER.log(Level.INFO, "webhook not executed from checkAndExecuteUpdateEvent method as the resocure is locked: id : {0}", webhook.getId());
                                                    }
                                                }
                                                else
                                                {
                                                    Webhook.enableDisableWebhook(container.getDocOwner(), webhook.getId(), true);
                                                    itr.remove();
                                                    continue;
                                                }
                                            }
                                            catch(Exception e)
                                            {
                                                LOGGER.log(Level.WARNING, "could not execute update event due to an exception: ", e);
                                            }
                                        }
                                    }

                                    if(lastCompletedRowIndex != -1)
                                    {
                                        webhook.setLastRecordedIndex(lastCompletedRowIndex);
                                        webhook.setIsSaveRequired(true);
                                    }
                                    if(lastIncompleteRowIndex != -1 && lastIncompleteRowIndex > webhook.getLastRecordedIndex())
                                    {
                                        webhook.setDelayTimer(Math.max(webhook.getLastRecordedIndex() + 1, intersectionRange.getStartRowIndex()), lastIncompleteRowIndex, System.currentTimeMillis());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    public static void checkAndExecuteCommentEvent(WorkbookContainer container, JSONObject commentJson)
    {
    	if(!commentJson.isEmpty())
    	{
    		int action = commentJson.getInt(JSONConstants.ACTION);
    		Workbook workbook = container.getWorkbookForSave();
	        if(workbook != null)
	        {
	        	String docOwner = container.getDocOwner();
				String ownerZUID = container.getDocOwnerZUID();
                String docsSpaceId = container.getDocsSpaceId();
				String rid = container.getResourceId();
				List<Webhook> webhooks = workbook.getWebhooks();
	            if(!(webhooks == null || webhooks.isEmpty()))
	            {
	                Iterator<Webhook> itr = webhooks.iterator();
	                Webhook webhook;
	                String payload = "";
	                while(itr.hasNext())
	                {
	                    webhook = itr.next();
	                    Event event = webhook.getEvent();
	                    
	                    if((event.equals(Event.ADD_COMMENT) && action == ActionConstants.DISCUSSION_ADD) || (event.equals(Event.DELETE_COMMENT) && (action == ActionConstants.DISCUSSION_DELETE || action == ActionConstants.DISCUSSION_DELETE_ALL)) || (event.equals(Event.EDIT_COMMENT) && action == ActionConstants.DISCUSSION_UPDATE_RANGE_CONTENT) || (event.equals(Event.RESOLVE_COMMENT) && action == ActionConstants.DISCUSSION_RESOLVE) || (event.equals(Event.REPLY_COMMENT) && action == ActionConstants.DISCUSSION_ADD_REPLY))
	                    {
	                    	String sheetName = webhook.getWebhookDetails();
	                    	if(sheetName == null || action == ActionConstants.DISCUSSION_DELETE_ALL || sheetName.equalsIgnoreCase(commentJson.getString(JSONConstants.SHEET_NAME)))
	                        {
		                        boolean isPermitted = false;
		                        boolean isLocked = false;   //check in check out case
		                        long createdBy = webhook.getCreatedBy();
		                        String webhookCreatorZUID = String.valueOf(createdBy);
		                        
		                        if(createdBy == -1)
		                        {
		                            isPermitted = true;
		                        }
		                        else    //need to check permission and lock info if createdBy is available
		                        {
		                            try
		                            {
		                                isPermitted = isWebhookPermitted(ownerZUID, webhookCreatorZUID, rid);
		                            }
		                            catch(SocketTimeoutException ste)
		                            {
		                                return;
		                            }
		                            isLocked = !AuthorizationUtil.isLockOwner(container, webhookCreatorZUID);
		                        }
		                        try
		                        {
		                            if(isPermitted)
		                            {
		                                if(!isLocked)   //webhook should not be disabled if locked
		                                {
		                                	JSONObject payloadObj = DataAPIResponse.getWebhookPayloadForComment(commentJson);
		                                    DataAPIResponse.addWebhookDetails(payloadObj, webhook.getId(), webhook.getServiceName(), event.toString().toLowerCase());
		                                    payload = payloadObj.toString();
		                                    webhook.execute(RequestType.POST, payload);
		                                    LOGGER.log(Level.INFO, "Executing webhook from checkAndExecuteCommentEvent method : id : {0}", webhook.getId());
		                                }
		                                else
		                                {
		                                    LOGGER.log(Level.INFO, "webhook not executed from checkAndExecuteCommentEvent method as the resource is locked: id : {0}", webhook.getId());
		                                }
		                            }
		                            else
		                            {
		                                Webhook.enableDisableWebhook(docOwner, webhook.getId(), true);
		                                itr.remove();
		                            }
		                        }
		                        catch(Exception e)
		                        {
		                            LOGGER.log(Level.WARNING, "could not execute comment event due to an exception: ", e);
		                        }
	                        }
	                    }
	                }
	            }
	        }
    	}
    }
    
//    public static class RowStateKeeper {
//        public static enum State {
//            INCOMPLETE,
//            NEWLY_ADDED,
//            EDITED
//        }
//
//        private final String asn;
//        private final int startColIndex;
//        private final int endColIndex;
//        private final IntegralMap<State> rowStates;
//
//        private RowStateKeeper(String asn, int startColIndex, int endColIndex, IntegralMap<State> rowStates) {
//            this.asn = asn;
//            this.startColIndex = startColIndex;
//            this.endColIndex = endColIndex;
//            this.rowStates = rowStates;
//        }
//        
//        public static RowStateKeeper getFor(Workbook workbook, DataRange range) {
//            //TODO: iterate over the range and initialise the map
//                IntegralMap<RowStateKeeper.State> states = getStatesMapForRange(workbook, range);
//            return new RowStateKeeper(range.getAssociatedSheetName(), range.getStartColIndex(), range.getEndColIndex(), states);
//                    }
//
//        public SortedMap<Integer, State> notifyEditAt(Workbook workbook, Collection<DataRange> affectedRanges) {
//            Collection<DataRange> intersectingAffectedRanges = new LinkedList<>();
//            for(DataRange affectedRange: affectedRanges) {
//                DataRange intersectingAffectedRange = RangeUtil.intersection(affectedRange, getRange());
//                if(intersectingAffectedRange != null) {
//                    intersectingAffectedRanges.add(intersectingAffectedRange);
//                }
//            }
//
//            IntegralSet affectedRows = new TreeBasedIntegralSet();
//            for(DataRange affectedRange: intersectingAffectedRanges) {
//                affectedRows.add(affectedRange.getStartRowIndex(), affectedRange.getRowSize());
//            }
//
//            SortedMap<Integer, State> delta = new TreeMap<>();
//            for(int affectedRowIndex: affectedRows.toSet()) {
//                State affectedRowStateOld = this.rowStates.get(affectedRowIndex).get();
//                State affectedRowState;
//                switch(affectedRowStateOld) {
//                    case INCOMPLETE:
//                        affectedRowState = getStatesMapForRange(workbook, getRange()).get(affectedRowIndex).get();
//                        break;
//                    default:
//                        affectedRowState = State.EDITED;
//                }
//
//                if(State.INCOMPLETE.equals(affectedRowStateOld) && !State.INCOMPLETE.equals(affectedRowState)
//                        || State.NEWLY_ADDED.equals(affectedRowStateOld) && State.EDITED.equals(affectedRowState)) {
//                    this.rowStates.put(affectedRowIndex, affectedRowState, 1);
//                    delta.put(affectedRowIndex, affectedRowState);
//                }
//            }
//
//            return delta;
//        }
//
//    //    public void insertCellShiftDown(Workbook workbook, Collection<DataRange> insertedRanges) {
//    //        for(DataRange insertedRange: insertedRanges) {
//    //            if(this.asn.equals(insertedRange.getAssociatedSheetName())) {
//    //                if(insertedRange.getStartColIndex() <= this.startColIndex && this.endColIndex <= insertedRange.getEndColIndex()) {
//    //                    DataRange observingRange = getRange();
//    //                    int numRows = insertedRange.getRowSize();
//    //                    if(insertedRange.getStartRowIndex() <= observingRange.getStartRowIndex()) {
//    //                        this.rowStates.shiftAllBy(numRows);
//    //                    } else {
//    //                        this.rowStates.insertEmpty(insertedRange.getStartRowIndex(), numRows);
//    //                        this.rowStates.put(insertedRange.getStartRowIndex(), State.EDIT, numRows);
//    //                    }
//    //                }
//    //            }
//    //        }
//    //    }
//
//        private DataRange getRange() {
//            return new DataRange(this.asn, this.rowStates.getMinKey().getAsInt(), this.startColIndex, this.rowStates.getMaxKey().getAsInt(), this.endColIndex);
//        }
//
//        private static IntegralMap<State> getStatesMapForRange(Workbook workbook, DataRange dataRange) {
//            Sheet sheet = workbook.getSheetByAssociatedName(dataRange.getAssociatedSheetName());
//            IntegralMap<State> statesMap = new IntegralMap<>();
//            for(int rowIndex = dataRange.getStartRowIndex(); rowIndex <= dataRange.getEndRowIndex(); rowIndex++) {
//                boolean rowHasEmptyCells = false;
//                RangeIterator rangeIterator = new RangeIterator(sheet, rowIndex, dataRange.getStartColIndex(), rowIndex, dataRange.getEndColIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false);
//                while(rangeIterator.hasNext()) {
//                    ReadOnlyCell roCell = rangeIterator.next();
//                    if(isEmpty(roCell)) {
//                        rowHasEmptyCells = true;
//                    }
//                }
//                statesMap.put(rowIndex, rowHasEmptyCells ? State.INCOMPLETE : State.NEWLY_ADDED, 1);
//            }
//            return statesMap;
//        }
//
//        private static boolean isEmpty(ReadOnlyCell roCell) {
//            Cell cell = roCell.getCell();
//            return cell == null || Value.EMPTY_VALUE.equals(cell.getValue());
//        }
//    }

    //public static void checkAndExecuteNewWorksheetEvent(Workbook workbook, Sheet sheet)
    public static void checkAndExecuteNewWorksheetEvent(WorkbookContainer container, Sheet sheet)
    {
        checkAndExecuteNewWorksheetEvent(container.getWorkbookForSave(), sheet, container.getDocOwner(), container.getDocOwnerZUID(), container.getDocsSpaceId(), container.getResourceId());
    }
    
    public static void checkAndExecuteNewWorksheetEvent(Workbook workbook, Sheet sheet, String docOwner, String ownerZUID, String docsSpaceId, String rid)
    {
        List<Sheet> sheets = new ArrayList<>();
        sheets.add(sheet);
        checkAndExecuteNewWorksheetEvent(workbook, sheets, docOwner, ownerZUID, docsSpaceId,  rid);
    }
    
    public static void checkAndExecuteNewWorksheetEvent(Workbook workbook, List<Sheet> sheets, String docOwner, String ownerZUID, String docsSpaceId, String rid)
    {
        if(workbook != null)
        {
            //if(workbook.getWebhooks() != null)
            List<Webhook> webhooks = workbook.getWebhooks();
            if(!(webhooks == null || webhooks.isEmpty()))
            {
                Iterator<Webhook> itr = webhooks.iterator();
                Webhook webhook;
                String payload = "";
                //for(Webhook webhook : workbook.getWebhooks())
                while(itr.hasNext())
                {
                    webhook = itr.next();
                    if(webhook.getEvent().toString().equals("NEW_WORKSHEET"))
                    {
                        boolean isPermitted = false;
                        boolean isLocked = false;   //check in check out case
                        long createdBy = webhook.getCreatedBy();
                        String webhookCreatorZUID = String.valueOf(createdBy);
                        
                        if(createdBy == -1)
                        {
                            isPermitted = true;
                        }
                        else    //need to check permission and lock info if createdBy is available
                        {
                            try
                            {
                                isPermitted = isWebhookPermitted(ownerZUID, webhookCreatorZUID, rid);
                            }
                            catch(SocketTimeoutException ste)
                            {
                                return;
                            }
                            isLocked = AuthorizationUtil.isLockOwner(rid, webhookCreatorZUID);
                        }
                        try
                        {
                            if(isPermitted)
                            {
                                if(!isLocked)   //webhook should not be disabled if locked
                                {
                                    for(Sheet sheet : sheets)
                                    {
                                        if(sheet != null)
                                        {
                                            JSONObject payloadObj = DataAPIResponse.getWebhookPayloadForNewWorksheet(sheet.getName(), sheet.getAssociatedName());
                                            DataAPIResponse.addWebhookDetails(payloadObj, webhook.getId(), webhook.getServiceName(), webhook.getEvent().toString().toLowerCase());
                                            payload = payloadObj.toString();
                                            webhook.execute(RequestType.POST, payload);
                                            LOGGER.log(Level.INFO, "Executing webhook from checkAndExecuteNewWorksheetEvent method : id : {0}", webhook.getId());
                                            //LOGGER.log(Level.INFO, "payloadObj : {0}", payload);
                                        }
                                    }
                                }
                                else
                                {
                                    LOGGER.log(Level.INFO, "webhook not executed from checkAndExecuteNewWorksheetEvent method as the resocure is locked: id : {0}", webhook.getId());
                                }
                            }
                            else
                            {
                                Webhook.enableDisableWebhook(docOwner, webhook.getId(), true);
                                itr.remove();
                            }
                        }
                        catch(Exception e)
                        {
                            LOGGER.log(Level.WARNING, "could not execute new_worksheet event due to an exception: ", e);
                        }
                    }
                }
            }
        }
    }
        
    public static int getlastRecordedIndex(Sheet sheet, DataRange dataRange)
    {
        for(int rowIndex = sheet.getUsedRowIndex(); rowIndex > dataRange.getStartRowIndex(); rowIndex--)
        {
            boolean rowHasEmptyCells = false;
            RangeIterator rangeIterator = new RangeIterator(sheet, rowIndex, dataRange.getStartColIndex(), rowIndex, dataRange.getEndColIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, false, true);
            while(rangeIterator.hasNext())
            {
                ReadOnlyCell roCell = rangeIterator.next();
                if(isEmpty(roCell.getCell()))
                {
                    rowHasEmptyCells = true;
                    break;  //break from while
                }
            }
            if(!rowHasEmptyCells)
            {
                return rowIndex;
            }
        }
        return dataRange.getStartRowIndex();    //by default return the first row
    }
    
    private static IntegralMap<RowState> getStatesMapForRange(Workbook workbook, DataRange dataRange)
    {
        LOGGER.log(Level.INFO, "getStatesMapForRange :: dataRange : {0}", dataRange.toString());
        Sheet sheet = workbook.getSheetByAssociatedName(dataRange.getAssociatedSheetName());
        IntegralMap<RowState> statesMap = new IntegralMap<>();

        int emptyRowsCount = 0;
        for(int rowIndex = dataRange.getStartRowIndex(); rowIndex <= dataRange.getEndRowIndex(); )
        {
            ReadOnlyRow rRow = sheet.getReadOnlyRowFromShell(rowIndex);
            int repeated = rRow.getRowsRepeated();
            if(RangeUtil.isBlankRange(sheet, rowIndex, dataRange.getStartColIndex(), rowIndex, dataRange.getEndRowIndex(), false))
            {
                emptyRowsCount += repeated;
                rowIndex += repeated;
                continue;
            }

            boolean rowHasEmptyCells = false;

            RangeIterator rangeIterator = new RangeIterator(sheet, rowIndex, dataRange.getStartColIndex(), rowIndex, dataRange.getEndColIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, false, false, true);
            while(rangeIterator.hasNext())
            {
                ReadOnlyCell roCell = rangeIterator.next();
                if(isEmpty(roCell.getCell()))
                {
                    rowHasEmptyCells = true;
                    break;
                }
            }

            for (int i = 0; i < emptyRowsCount+1; i++)
            {
                //statesMap.put(rowIndex, rowHasEmptyCells || emptyRowsCount > 0 ? RowState.INCOMPLETE : RowState.NEWLY_ADDED, 1);
                statesMap.put(rowIndex - (emptyRowsCount-i), rowHasEmptyCells || emptyRowsCount > 0 ? RowState.INCOMPLETE : RowState.NEWLY_ADDED, 1);
            }

            emptyRowsCount = 0;
            rowIndex += repeated;
        }
        return statesMap;
    }

    private static boolean isEmpty(Cell cell)
    {
        return cell == null || Value.EMPTY_VALUE.equals(cell.getValue());
    }
    
    private static boolean isWebhookPermitted(WorkbookContainer container, String webhookCreatorZUID) throws SocketTimeoutException
    {
        return isWebhookPermitted(container.getDocOwnerZUID(), webhookCreatorZUID, container.getResourceId());
    }
            
	private static boolean isWebhookPermitted(String ownerZUID, String webhookCreatorZUID, String rid) throws SocketTimeoutException
    {
        //String webhookCreatorZUID = String.valueOf(createdBy);
        //if(createdBy == -1 || ownerZUID.equals(webhookCreatorZUID))
//        if(createdBy == -1)
//        {
//            //Note : created by -1 represents no information about who has created the webhook (previously created webhook)
//            return true;
//        }
        try
        {
            ResourcePermissionInfo permissionInfo = AuthorizationUtil.getResourcePermissions(webhookCreatorZUID, rid);
            if(permissionInfo == null)
            {
                return false;
            }
            int maxPermission = new JSONObject(String.valueOf(permissionInfo)).getInt("maxpermission"); // NO I18N
            boolean canRead = (maxPermission & ShareConstants.PERMISSION_READ) == ShareConstants.PERMISSION_READ;
            return canRead;
        }
        catch(SocketTimeoutException ste)
        {
            LOGGER.log(Level.WARNING, "Socket timeout exception in isWebhookPermitted method", ste);
            throw ste;
        }
        catch(Exception e)
        {
            LOGGER.log(Level.WARNING, "Exception in isWebhookPermitted method", e);
            return false;
        }
    }
    
    private static boolean isResourceLockedByOtherUser(String docsSpaceId, String webhookCreatorZUID, String rid)
    {
        try
        {
            HashMap<String, Object> lockedInfo = ZohoFS.getLockedResourceInfo(docsSpaceId, rid);
            LOGGER.log(Level.INFO, "getLockedInfo :: ownerZUID : {0} :: resourceId : {1} :: lockedInfo : {2}", new Object[]{docsSpaceId, rid, lockedInfo});
            if (lockedInfo != null)
            {
                String lockedBy = (String) lockedInfo.get("LOCKED_BY");
                if(!webhookCreatorZUID.equals(lockedBy))
                {
                    LOGGER.log(Level.INFO, "webhook will not trigger as the resouce is locked by other user :: lockedBy : {0} :: webhookCreatorZUID : {1}", new Object[]{lockedBy, webhookCreatorZUID});
                    return true;
                }
            }
        }
        catch (Exception e)
        {
            LOGGER.log(Level.INFO, "Exception while getting resource locked info : ", e);
            return false;
        }
        return false;
    }
}
