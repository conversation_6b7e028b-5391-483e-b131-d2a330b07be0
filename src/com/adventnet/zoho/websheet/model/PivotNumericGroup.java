//$Id$
/**
 * 
 */
package com.adventnet.zoho.websheet.model;

import java.util.logging.Logger;
import com.singularsys.jep.functions.Round;

/**
 * <AUTHOR>
 *
 */
public class PivotNumericGroup extends GroupValue{
	
	public static Logger logger = Logger.getLogger(PivotNumericGroup.class.getName());
	
	public PivotNumericGroup(Number start, Number end, Number step, Object value, Boolean isDec ) {
		
		super(start, end, step, value, isDec);
	}
	
//	public String getGroupedValue() throws Exception{
//
//		String result = "";
//		if((Double)this.value < (Double)this.start) {
//			result = " < "+(int)Math.floor((Double)this.start);
//		}else if( (Double)this.value > (Double)this.end) {
//			result = " > "+(int)Math.floor((Double)this.end);
//		}else if( isDec ) {
//			Boolean isStepDec = true;
//			if(step instanceof Integer) {
//				isStepDec = false;
//			}else if((Double)step == Math.floor((Double)step)) {
//				isStepDec = false;
//			}
//			boolean isStartValDec = true;
//			if(start instanceof Integer) {
//				isStartValDec = false;
//			}else if((Double)start == Math.floor((Double)start)) {
//				isStartValDec = false;
//			}
//			double newI = ((Double)this.value) - ((Double)this.start);
//
//			Divide divFun = new Divide();
//			Multiply mulFun = new Multiply();
//
//			int quotient = FunctionUtil.objectToNumber(divFun.div((Number)newI, (Number)step)).intValue();
//			Number product = FunctionUtil.objectToNumber(mulFun.mul(quotient, (Number)this.step));
//			double startVal = 0;
//			double endVal = 0;
//
//			//logger.info("newI: "+newI+" quotient: "+quotient+" product: "+product);
//
//			if(product.doubleValue() < (Integer)step) {
//				startVal = (Double)start;
//				 endVal =  (Double)start + ((Integer)step);
//			}else if(Math.floor(product.doubleValue()) == (Double)newI ){
//				  //startVal =  Math.floor(product.doubleValue())  + ((Double)start - (Integer)step);
//				  //endVal = (product.doubleValue() + ((Double)start));
//				startVal = (product.doubleValue() + ((Double)start));
//				endVal = startVal + ((Integer)step);
//			 }else if(Math.floor(product.doubleValue()) < newI ){
//					 startVal = Math.floor(product.doubleValue()) + (Double)(start);
//					 endVal = startVal + ((Integer)step);
//				 }
//
//
//			if(isStepDec || isStartValDec) {
//				 result = startVal +" - " + endVal;
//			 }else {
//				 result = (int)Math.floor(startVal) +" - " + (int)Math.floor(endVal);
//			 }
//		}else {
//			 Number newI = ((Double)this.value) - ((Double)this.start-1);
//			 Divide divFun = new Divide();
//			 Multiply mulFun = new Multiply();
//			 int quotient = FunctionUtil.objectToNumber(divFun.div((Number)newI, (Number)step)).intValue();
//			 Number product = FunctionUtil.objectToNumber(mulFun.mul(quotient, (Number)this.step));
//			 double startVal = 0;
//			 double endVal = 0;
//			 if(product.doubleValue() < (Integer)step){
//				 startVal = (Double)start;
//				 endVal =  (Double)start + ((Integer)step - 1);
//			 }else if(product.doubleValue() == newI.doubleValue()) {
//				 startVal = product.doubleValue() + (Double)start-1 - ((Integer)step - 1);
//				 endVal = (product.doubleValue() + ((Double)start-1));
//			 }else if(newI.doubleValue() > product.doubleValue()){
//				 startVal = product.doubleValue() + ((Double)start);
//				 endVal = startVal + ((Integer)step-1);
//			 }
//			 if(this.isDec) {
//				 result = startVal +" - " + endVal;
//			 }else {
//				 result = (int)Math.floor(startVal) +" - " + (int)Math.floor(endVal);
//			 }
//		}
//		return result;
//	}

	public String getGroupedValue(){
		if((Double)this.value < (Double)this.start) {
			return " < "+ this.start;
		}else if( (Double)this.value > (Double)this.end) {
			return " > " + this.end;
		}

		boolean isStepDec = ((Double)this.step)%1 !=0;
		boolean isValueDec = (Double) this.value%1 != 0;
		boolean isStartDec = (Double) this.start%1 != 0;

		//Note : Round is used to avoid precision loss.
		int dp = 5; // As of now we supporting 5 dp only.
		double sub = (isValueDec || isStartDec) ? Round.round((Double) this.value - (Double) this.start, dp) : ((Double) this.value - (Double) this.start);
		long q = (long)(isStepDec ? Round.round(sub/(Double)this.step, dp) : sub/(Double)this.step);
		double p = isStepDec ? Round.round(q * (Double) this.step, dp) : (q * (Double) this.step);
		double start = isStartDec ? Round.round((Double) this.start + p, dp) : (Double) this.start + p;
		double end = isStepDec ? Round.round(start + (Double) this.step, dp) : start + (this.isDec ? (Double) this.step : ((Double) this.step - 1));

		if (this.isDec && ((Double) this.value).doubleValue() == ((Double) this.end).doubleValue() && (Double) this.value == start) {
			//This splHandling is to get EXCEL'S Behavior.
			end = start;
			start = isStepDec ? Round.round((Double) start - (Double) this.step, dp) : (Double) start - (Double) this.step;
		}
		end = Math.min(end,(Double)this.end);
		if(this.isDec || isStepDec || start%1 != 0 || end%1 != 0) {
			return "'"+start +" - " + end;
		}else {
			return "'"+((Double)start).longValue() +" - " +((Double)end).longValue();
		}
	}
}
/*
 ....................
 logger.info("start: "+start+" end: "+end+" step: "+step+" value: "+value);
		
		String result = "";
		if(((Number)this.value).intValue() < (int)this.start) {
			result = " < "+start;
		}else if( ((Number)this.value).intValue() > (int)this.end) {
			result = " > "+end;
		}else {
			int newI = ((Number)this.value).intValue() - ((int)this.start-1);
			Divide divFun = new Divide();
			 Multiply mulFun = new Multiply();
			 int quotient = FunctionUtil.objectToNumber(divFun.div((Number)newI, (Number)step)).intValue();
			 int product = FunctionUtil.objectToNumber(mulFun.mul(quotient, (Number)this.step)).intValue();
			 logger.info("newI: "+newI+" quotient: "+quotient+" product: "+product);
			 if(product < (Integer)step){
				// int startVal =  (Integer)step - ((Integer)step - 1);
				 int endVal =  (Integer)start + ((Integer)step - 1);
				 result = start +" - " + endVal;
				 
				// result = startVal +" - " + this.step;
			 }else if(product == newI) {
				
				 int startVal =  (Integer)product + (int)start-1 - ((Integer)step - 1);
				 result = startVal +" - " + (product + ((int)start-1));
				 
			 }else if(newI > product){
				 int startVal = product + ((int)start);
				 int endVal = startVal + ((Integer)step-1);
				 result = startVal +" - " + endVal;
			 }
		}
		
			logger.info("result: "+result);
		return result;
	}
 
 ...............
 String result = "";
		int firstElem = 1;
		int lastElem = 36;
		for(int i=firstElem;i<=lastElem;i++) {
			//System.out.print(i);
			if(firstElem != start && i < start) {
				System.out.println(" < "+start);
			}else if( i > end) {
				System.out.println(" > "+end);
			}else {
				int newI = i - (start-1);
				int quotient = (newI / step);
				int product = quotient* step;
				
				System.out.print(i+" :: "+newI+" quotient: "+quotient+" product: "+product);
				if(product < (Integer)step){
					 int endVal =  (Integer)start + ((Integer)step - 1);
					 result = start +" - " + endVal;
				 }else if(product == newI) {
					 int startVal =  (Integer)product + (start-1) - ((Integer)step - 1);
					 result = startVal +" - " + (product + (start-1));
				 }else if(newI > product){
					 int startVal = product + (start);
					 int endVal = startVal + ((Integer)step-1);
					 result = startVal +" - " + endVal;
				 }
				System.out.println("  "+result);
			}
			
		} 
 * */
 