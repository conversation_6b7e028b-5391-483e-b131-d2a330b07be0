// $Id$
package com.adventnet.zoho.websheet.model;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PlaceHolder {
    private String description;
    private List<Range> ranges = new ArrayList<>();

    /**
     *
     * @param range
     * @return false, if this range conflicts with existing ranges
     */
    public boolean addRange(Range range) {
        for(Range existingRange: this.ranges) {
            boolean conflicts = existingRange.getSheet().getAssociatedName().equals(range.getSheet().getAssociatedName()) &&
                    existingRange.getStartRowIndex() <= range.getEndRowIndex() && range.getStartRowIndex() <= existingRange.getEndRowIndex()
                    && existingRange.getStartColIndex() <= range.getEndColIndex() && range.getStartColIndex() <= existingRange.getEndColIndex();
            if(conflicts) {
                return false;
            }
        }
        this.ranges.add(range);
        return true;
    }

    public List<Range> getRanges() {
        return Collections.unmodifiableList(this.ranges);
    }

    /**
     *
     * @param range
     * @return
     */
    public boolean removeRange(Range range) {
        return this.ranges.remove(range);
    }

    public void clearRanges() {
        this.ranges.clear();
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
