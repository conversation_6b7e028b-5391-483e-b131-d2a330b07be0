// $Id:$
package com.adventnet.zoho.websheet.model.ext;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;

import java.util.Locale;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>zt276 on  17/10/19
 */
public class ParsedValue {
    private final Value value;
    private final Cell.Type patternType;
    private Locale currencyLocale;

    private final ParsedDate.DateProperties dateProperties;
    private final ParsedDate.TimeProperties timeProperties;

    public ParsedValue(Value value, Cell.Type patternType, Locale currencyLocale, ParsedDate.DateProperties dateProperties, ParsedDate.TimeProperties timeProperties) {
        this.value = value;
        this.patternType = patternType;
        this.currencyLocale = currencyLocale;
        this.dateProperties = dateProperties;
        this.timeProperties = timeProperties;
    }

    public Value getValue() {
        return value;
    }

    public Cell.Type getPatternType() {
        return patternType;
    }

    public Locale getCurrencyLocale() {
        return currencyLocale;
    }

    public ParsedDate.TimeProperties getTimeProperties() {
        return timeProperties;
    }

    public ParsedDate.DateProperties getDateProperties() {
        return dateProperties;
    }

    public void setCurrencyLocale(Locale currencyLocale) {
        this.currencyLocale = currencyLocale;
    }
}
