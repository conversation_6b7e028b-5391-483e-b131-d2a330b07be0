//$Id$
/*
 * PriceMat.java
 *
 * Created on May 27, 2008, 2:43 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import java.util.Date;

public class PriceMat extends SecurityMaturity
{
    
    /** Creates a new instance of PriceMat */
    public PriceMat()
    {
        numberOfParameters = -1;
    }
    
    @Override
    public double getResult(Date settlement, Date maturity, Date issue, double rate, double yield,int basis) 
     {
         return getPriceMat(settlement, maturity, issue, rate, yield, basis);
     }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        return (index == 4);
    }
}
