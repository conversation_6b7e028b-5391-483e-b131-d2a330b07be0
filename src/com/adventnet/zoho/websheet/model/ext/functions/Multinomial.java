//$Id$
/*
 * Multinomial.java
 *
 * Created on August 3, 2009, 6:32 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import java.util.Stack;

import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.MathematicsFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.NonExclusiveFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Multinomial extends PostfixMathCommand implements MathematicsFunctionI, NonExclusiveFunctionI, CallbackEvaluationI
{
    
    Factorial fact = new Factorial();
    
    /** Creates a new instance of Multinomial */
    public Multinomial()
    {
        numberOfParameters = -1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if(numChildren < 1)
        {
            throw Error.REF.getThrowableObject();
        }

        List<Number> nums = new ArrayList<>();

        long sum = 0;

        for(int i = 0; i < numChildren; i++)
        {
            Node child = node.jjtGetChild(i);
            Object param = ((ZSEvaluator) pv).evaluate(child, (Cell) data, false, false);

            if(param instanceof  Stack) {
                for(Object result: ((Stack)param)) {
                    sum = eval(result, data, nums, sum);
                }

            } else if(param instanceof ZArrayI) {
                ZArrayI zArray = (ZArrayI)param;
                for(int rowIndex = 0; rowIndex < zArray.getRowSize(); rowIndex++) {
                    for(int colIndex = 0; colIndex < zArray.getColSize(); colIndex++) {
                        Object result = zArray.getValue(rowIndex, colIndex);
                        sum = eval(result, data, nums, sum);
                    }
                }
            } else {
                sum = eval(param, data, nums, sum);
            }

        }

        double result = fact.getFactorial(sum);

        for(int i = 0; i < nums.size(); i++)
        {
            result /= fact.getFactorial(nums.get(i).longValue());
        }

        return Value.getInstance(Type.FLOAT, result);

    }

    public long eval(Object param, Object data, List<Number> nums, long sum) throws EvaluationException {
        if(param instanceof Value)
        {
            param = ((Value)param).getValue();
        }

        if(param == null || param instanceof ASTEmptyNode)
        {
            return sum;
        }

        if(param instanceof EvaluationException) {
    throw (EvaluationException)param;
}
if(param instanceof Throwable) {
    throw Error.getError(((Throwable)param).getMessage()).getThrowableObject();
}

        if(param instanceof String)
        {
            param = Value.getInstance((String)param, ((Cell)data).getSpreadsheetSettings()).getValue();

            if(param == null || param instanceof String || param instanceof Throwable)
            {
                throw Error.VALUE.getThrowableObject();
            }
        }

        long arg = FunctionUtil.objectToNumber(param).longValue();

        if(arg < 0)
        {
            throw Error.NUM.getThrowableObject();
        }
        sum += arg;
        nums.add(arg);

        return sum;
    }

    /** OLD CODE
     *
     *
    public void run(Stack<Object> inStack) throws EvaluationException 
    {
        checkStack(inStack); // check the stack
        
        List<Number> nums = new ArrayList<Number>();
        
        for(int i = 0; i < curNumberOfParameters; i++)
        {
            Object param = inStack.pop();
            
            if(param instanceof Value)
            {
                param = ((Value)param).getValue();
            }
            
            if(param == null || param instanceof ASTEmptyNode)
            {
                continue;
            }
            
            if(param instanceof Throwable)
            {
                throw new EvaluationException(((Throwable)param).getMessage());
            }
            
            if(param instanceof String)
            {
                param = new Value((String)param,  book.getFunctionLocale()).getValue();
                
                if(param == null || param instanceof String || param instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }
            
            double arg = FunctionUtil.objectToNumber(param).doubleValue();
            
            if(arg < 0)
            {
                throw Error.NUM.getThrowableObject();
            }
            
            nums.add(arg);
        }
        
        try
        {
            inStack.push(new Value(Type.FLOAT, multinomial(nums)));
        }
        catch(IllegalArgumentException e)
        {
            throw Error.VALUE.getThrowableObject();
        }
        catch(ClassCastException e)
        {
            throw Error.VALUE.getThrowableObject();
        }
    }
     */
    
    /*
     * Returns the number equivalent to the time given as String.
     *
     *    MADE TO DO ALL CALCULATIONS IN METHOD RUN ITSELF.
    public double multinomial(List<Number> nums) throws EvaluationException
    { 
        long sum = 0;
                
        for(int i = 0; i < nums.size(); i++)
        {
            sum += nums.get(i).longValue();
        }
        
        double result = fact.getFactorial(sum);
        
        for(int i = 0; i < nums.size(); i++)
        {
            result /= fact.getFactorial(nums.get(i).longValue());
        }
        
        return result;
    }  
     */
}
