//$Id$
package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.ImageDisplayMode;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.InformationFunctionI;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;

/**
 * Created by krishnan-zt276 on  21/10/20
 */

//IMAGE(url, [mode], [height], [width])
public class Image extends PostfixMathCommand implements ScalarFunctionI, InformationFunctionI, CallbackEvaluationI {

    public static Logger logger = Logger.getLogger(Image.class.getName());
    @Override
    public boolean checkNumberOfParameters(int n) {
        return n < 3;//n < 5;
    }

    public Image() {
        this.numberOfParameters = -1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {

        int num = node.jjtGetNumChildren();

        if(!checkNumberOfParameters(num))
        {
            throw Cell.Error.NAME.getThrowableObject();
        }

        String url = "";
        ImageDisplayMode mode = ImageDisplayMode.FIT;
        Double imageHeight = null;
        Double imageWidth = null;

        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);
            Object temp = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            if(temp instanceof Value)
            {
                temp = ((Value) temp).getValue();
            }

            if(i == 0){
                if(temp == null || !(temp instanceof String) || ((String)temp).isEmpty())
                {
                    throw Cell.Error.VALUE.getThrowableObject();
                }
                url = (String) temp;
            }else {
                try
                {
                    temp = FunctionUtil.objectToNumber(temp);
                }
                catch (EvaluationException e)
                {
                    throw Cell.Error.VALUE.getThrowableObject();
                }

                if (i == 1)
                {
                    int id = ((Number) temp).intValue();
                    if(id < 1 || id > 3)//id > 4)
                    {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }
                    mode = getImageDisplayMode(id);
                }
                else if (i == 2)
                {
                    imageHeight = ((Number) temp).doubleValue();
                }
                else if (i == 3)
                {
                    imageWidth = ((Number) temp).doubleValue();
                }
            }
        }

        if(mode == ImageDisplayMode.CUSTOM ? (imageHeight == null || imageWidth == null) : (imageHeight != null || imageWidth != null)){
            throw Cell.Error.NA.getThrowableObject();
        }

        return ((Cell)data).getRow().getSheet().getWorkbook().getImageFormulaCache().getValue(((Cell)data), mode, url, imageHeight == null ? -1 : imageHeight, imageWidth == null ? -1 : imageWidth);

    }

    private static ImageDisplayMode getImageDisplayMode(int id){
        switch (id){
            case 1:
                return ImageDisplayMode.FIT;
            case 2:
                return ImageDisplayMode.STRETCH;
            case 3:
                return ImageDisplayMode.ORIGINAL;
            case 4:
                return ImageDisplayMode.CUSTOM;
            default:
                throw new IllegalArgumentException("Invalid ImageDisplayMode id"); // No I18N
        }
    }

    @Override
    public boolean isNonExportableFunction() {
        return true;
    }
}
