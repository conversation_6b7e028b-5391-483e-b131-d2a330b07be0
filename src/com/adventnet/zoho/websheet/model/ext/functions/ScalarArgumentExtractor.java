// $Id$
package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.parser.Node;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>-zt276 on  06/01/20
 */
public class ScalarArgumentExtractor implements ArgumentExtractor {

    public static final Logger LOGGER = Logger.getLogger(ScalarArgumentExtractor.class.getName());
    enum ArgumentType{
        NUMBER,
        STRING,
        NUMBER_AND_CONVERTED_NUMBER,
        STRING_AND_CONVERTED_STRING,
    }

    private final ArgumentType argumentType;
    private final Object emptyNodeValue;
    private final Object nullValue;

    public ScalarArgumentExtractor(ArgumentType argumentType) throws EvaluationException {
        this(argumentType, null, null);
    }

    public ScalarArgumentExtractor(ArgumentType argumentType,  Object nullValue) throws EvaluationException {
        this(argumentType, null, nullValue);
    }

    public ScalarArgumentExtractor(ArgumentType argumentType, Object emptyNodeValue, Object nullValue) throws EvaluationException {
        this.argumentType = argumentType;
        if(ArgumentType.NUMBER == argumentType || ArgumentType.NUMBER_AND_CONVERTED_NUMBER == argumentType){
            if(!(emptyNodeValue instanceof  Number || nullValue instanceof Number)){
                LOGGER.log(Level.WARNING, ">>> Type Mismatch Argument Number Excepted"); //No I18N
                throw Cell.Error.VALUE.getThrowableObject();
            }
        }else if(ArgumentType.STRING == argumentType || ArgumentType.STRING_AND_CONVERTED_STRING == argumentType){
            if(!(emptyNodeValue instanceof  String || nullValue instanceof String)){
                 LOGGER.log(Level.WARNING, ">>> Type Mismatch Argument String Excepted"); //No I18N
                throw Cell.Error.VALUE.getThrowableObject();
            }
        }
        this.emptyNodeValue = emptyNodeValue;
        this.nullValue = nullValue;
    }

    @Override
    public Object getArgument(Node node, Cell data, ZSEvaluator zsEvaluator) throws EvaluationException {
        Object val = zsEvaluator.evaluate(node, data, true, false);

        ////////
//        if(val instanceof ZArrayI)
//        {
//            if(val instanceof Range)
//            {
//                val = ((ZArrayI)val).getValue(data.getRowIndex(),data.getColumnIndex());
//            }
//            else // ZArray
//            {
//                val = ((ZArrayI)val).getValue(0,0);
//            }
//        }
        ////////

        if(val instanceof Value){
            val = ((Value) val).getValue();
        }else if(val instanceof ASTEmptyNode){
            if(emptyNodeValue != null){
                return emptyNodeValue;
            }else {
                throw Cell.Error.VALUE.getThrowableObject();
            }
        }

        if(val == null){
            if(nullValue != null){
                return nullValue;
            }else {
                throw Cell.Error.VALUE.getThrowableObject();
            }
        }

        try {
            switch (argumentType){
                case NUMBER_AND_CONVERTED_NUMBER:
                    if(val instanceof String){
                        val = Value.getInstance((String)val, data.getSpreadsheetSettings()).getValue();
                    }
                case NUMBER:
                        return FunctionUtil.objectToNumber(val);
                case STRING:
                    if(!(val instanceof String)){
                        throw Cell.Error.VALUE.getThrowableObject();
                    }
                case STRING_AND_CONVERTED_STRING:
                    val = FunctionUtil.objectToNumber(val);
                    val = Value.getInstance(Cell.Type.FLOAT, val).getValueString(data.getSpreadsheetSettings());
                    return val;
                default:
                    throw Cell.Error.VALUE.getThrowableObject();
            }
        }catch (EvaluationException e){
            throw Cell.Error.VALUE.getThrowableObject();
        }
    }
}
