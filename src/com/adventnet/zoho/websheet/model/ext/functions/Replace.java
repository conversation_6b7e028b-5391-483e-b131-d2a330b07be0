//$Id$
/*
 * Replace.java
 *
 * Created on July 3, 2008, 10:56 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.TextFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class Replace extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, TextFunctionI
{
	public static Logger logger = Logger.getLogger(Replace.class.getName());
    /** Creates a new instance of Replace */
    public Replace()
    {
        numberOfParameters = 4;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        Object param[] = new Object[num];

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
	for(int i = 1; i <= num; i++)
	{
	    Node child = node.jjtGetChild(i - 1);
        Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            if((i == 2 || i == 3) && (o == null || o instanceof ASTEmptyNode))
            {
                //logger.info("neither of first two arguments can be null.");
                throw Error.NUM.getThrowableObject();
            }

////            if(i == 1 || i == 4)
////            {
////                if(!(o instanceof String))
////                {
//////                    o = FunctionUtil.objectToNumber(o);
////
//////                    if((((Number)o).doubleValue() - ((Number)o).longValue()) == 0)
//////                    {
//////                        o = ((Number)o).longValue();
//////                    }
////                }
////            }
////            else
            if(i == 2 || i == 3)
            {
                if(o != null)
                {
                    if(o instanceof String)
                    {
                        o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                        if(o == null || o instanceof String || o instanceof Throwable)
                        {
                            throw Error.VALUE.getThrowableObject();
                        }
                    }

//                    o = FunctionUtil.objectToNumber(o);
                }
            }

            param[i - 1] = o;//FunctionUtil.objectToNumber(o);
	}

        String text = "";
        if(param[0] instanceof String)
        {
            text = (String)param[0];
        }
        else if(param[0] != null)
        {
            text = FunctionUtil.objectToNumberString(param[0]);
        }

        int start = FunctionUtil.objectToNumber(param[1]).intValue();
        int len = FunctionUtil.objectToNumber(param[2]).intValue();

        String new_Text = "";
        if(param[3] instanceof String)
        {
            new_Text = (String)param[3];
        }
        else if(param[3] != null)
        {
            new_Text = FunctionUtil.objectToNumberString(param[3]);
        }

        try {
    return Value.getInstance(Type.STRING, replace(text, start, len, new_Text));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    public static String replace(String text, int start, int len, String newText)
    {
        if(text == null || start < 1 || len < 0)
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.VALUE));
        }
        StringBuffer sb = new StringBuffer(text);

        if(start > sb.length())
        {
            sb.append(newText);
            return sb.toString();
        }
        
        return sb.replace(start - 1, start + len - 1, newText).toString();
    }
}
