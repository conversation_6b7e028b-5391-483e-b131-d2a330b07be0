//$Id:$
/*
 * True.java
 *
 * Created on August 22, 2008, 7:49 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.InformationFunctionI;
import com.singularsys.jep.parser.Node;

/**
 *
 * <AUTHOR>
 */
public class True extends PostfixMathCommand implements InformationFunctionI, CallbackEvaluationI
{
    
    /** Creates a new instance of True */
    public True()
    {
        numberOfParameters = 0;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator parserVisitor) throws EvaluationException {
        return Value.getInstance(Type.BOOLEAN, getTrue());
    }

    // the date returned should be shown in Date & Time format
    /*
     * returns the system time.
     */
    public boolean getTrue()
    {
        return true;
    }
    
}
