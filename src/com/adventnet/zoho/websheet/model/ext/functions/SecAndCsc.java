package com.adventnet.zoho.websheet.model.ext.functions;

import com.singularsys.jep.EvaluationException;

public class <PERSON>c<PERSON>nd<PERSON>c extends TrignometricUnary{
    public static final int SEC = 0;
    public static final int SECH = 1;
    public static final int CSC = 2;
    public static final int CSCH = 3;
    private final int id;

    public SecAndCsc(int id) {
        this.id = id;
        numberOfParameters = 1;
    }

    @Override
    public Number getResult(Number arg) throws EvaluationException {
        if (id == SEC) {
            return getSec(arg.doubleValue());
        } else if (id  == SECH) {
            return getSecH(arg.doubleValue());
        } else if (id == CSC) {
            return getCsc(arg.doubleValue());
        } else {
            return getCscH(arg.doubleValue());
        }
    }

    @Override
    public boolean isExcelFutureFunction() {
        return true;
    }
}
