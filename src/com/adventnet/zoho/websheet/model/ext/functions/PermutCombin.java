//$Id$
/*
 * PermutCombin.java
 *
 * Created on July 23, 2009, 1:51 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.StatisticalFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.functions.Round;
import com.singularsys.jep.parser.Node;

/**
 *
 * <AUTHOR>
 */
public class PermutCombin extends PostfixMathCommand implements ScalarFunctionI, StatisticalFunctionI, CallbackEvaluationI
{
    public static final int PERMUTATION = 1;
    public static final int COMBINATION = 2;
    public static final int PERMUTATIONA = 3;
    public static final int COMBINATIONA = 4;
    
    
    private int id ;
        
    /** Creates a new instance of PermutCombin
     * @param id */
    public PermutCombin(int id)
    {
        this.id = id;
        numberOfParameters = 2;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if(numChildren != 2)
        {
            throw Error.NAME.getThrowableObject();
        }

        int number = 0, choice = 0;

        for(int i = 0; i < numChildren; i++)
        {
            Node child = node.jjtGetChild(i);
            Object param = ((ZSEvaluator) pv).evaluate(child, (Cell) data, true, false);

            if(param instanceof Value)
            {
                param = ((Value)param).getValue();
            }

            if(param instanceof EvaluationException) {
    throw (EvaluationException)param;
}
if(param instanceof Throwable) {
    throw Error.getError(((Throwable)param).getMessage()).getThrowableObject();
}

            if(param != null)
            {
                if(param instanceof ASTEmptyNode)
                {
                    param = 0;
                }
                if(param instanceof String)
                {
                    param = Value.getInstance((String)param, ((Cell)data).getSpreadsheetSettings()).getValue();

                    if(param == null || param instanceof String || param instanceof Throwable)
                    {
                        throw Error.VALUE.getThrowableObject();
                    }
                }

                if(i == 0)
                {
                    number = FunctionUtil.objectToNumber(param).intValue();
                }
                else
                {
                    choice = FunctionUtil.objectToNumber(param).intValue();
                }
            }
        }

        return Value.getInstance(Type.FLOAT, getResult(number, choice));

    }

    /*
     * Returns the code foor the first char in the given string.
     *
     */
    public double getResult(int number, int choice) throws EvaluationException
    {
        if(choice > number || number < 0 || choice < 0)//for combin
        {
            throw Error.NUM.getThrowableObject();
        }

        if(id == PERMUTATIONA)
        {
            return Math.pow(number,choice);
        }

        if(id == COMBINATIONA)
        {
            number += (choice - 1);
        }
        
        double result = 1;
        int t = (id == COMBINATION || id == COMBINATIONA) ? Math.min((number - choice), choice) : choice;
        for(; t > 0; t--, number--)
        {
            result *= (double)number;
            if(id == COMBINATION || id == COMBINATIONA)
            {
                result /= t;
            }
        }

        if(Double.isInfinite(result) || Double.isNaN(result))
        {
            throw Error.NUM.getThrowableObject();
        }
        
        return Round.round(result, 0);
    }
    
    public static void main(String[] args) throws Exception
    {
        //System.out.println(new PermutCombin(PermutCombin.PERMUTATION).getResult(10, 4));
        //System.out.println(new PermutCombin(PermutCombin.COMBINATION).getResult(10, 4));
    }

    @Override
    public boolean isExcelFutureFunction() {
        return true;
    }
}
