//$Id$
/*
 * Percent.java
 *
 * Created on November 3, 2008, 11:36 AM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.CellImpl;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;

/**
 *
 * <AUTHOR>
 */
public class SpilledRangeOperator extends PostfixMathCommand implements ScalarFunctionI, CallbackEvaluationI
{

    /** Creates a new instance of Percent */
    public SpilledRangeOperator()
    {
        numberOfParameters = 1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if(numChildren != 1)
        {
            throw Error.VALUE.getThrowableObject();
        }

        Node child = node.jjtGetChild(0);

        Object object = ((ZSEvaluator)pv).evaluate(child, (Cell) data, true, true);
        
        if(object instanceof Range) {
            Range range = (Range)object;
            Cell cell = range.getTopLeft().getCell();
            if(((CellImpl)cell).isAutoArrayError()){
                throw Error.SPILL.getThrowableObject();
            }
            if(cell.isFormula()) {
                int rowSpan = ((CellImpl)cell).getAutoArrayRowSpan();
                int colSpan = ((CellImpl)cell).getAutoArrayColSpan();
                return new Range(cell.getRow().getSheet(), cell.getRowIndex(),cell.getColumnIndex(), cell.getRowIndex() + rowSpan-1, cell.getColumnIndex() + colSpan-1);
            }
        }
        
        throw Cell.Error.REF.getThrowableObject();
    }

}
