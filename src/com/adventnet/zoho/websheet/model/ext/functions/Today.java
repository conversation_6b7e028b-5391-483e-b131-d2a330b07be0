//$Id$
/*
 * Today.java
 *
 * Created on April 7, 2008, 11:53 AM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.DateTimeFunctionI;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.parser.Node;
import java.util.TimeZone;
import java.util.Calendar;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class Today extends PostfixMathCommand implements CallbackEvaluationI, DateTimeFunctionI
{
	public static Logger logger = Logger.getLogger(Today.class.getName());
    /** Creates a new instance of Today */
    public Today()
    {
        numberOfParameters = 0;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        TimeZone tz = ((Cell)data).getRow().getSheet().getWorkbook().getUserTimezone();
        return  Value.getInstance(Type.DATE, today(tz));
    }

    /*
     * returns todays date alone.(time will be 12 am)
     */
    public java.util.Date today(TimeZone tz)
    {
        Calendar cal = Calendar.getInstance();

        long time = cal.getTimeInMillis();
        long defoffset = cal.getTimeZone().getOffset(time);
        long offset = tz.getOffset(time);
        cal.setTimeInMillis(time - (defoffset - offset));

        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTime();
    }
    
        @Override
    public boolean isDynamicFunction()
    {
        return true;
    }
}
