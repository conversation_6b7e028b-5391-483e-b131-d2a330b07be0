//$Id$
/*
 * IsRef.java
 *
 * Created on February 27, 2009, 5:48 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;


import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
 import com.adventnet.zoho.websheet.model.ext.functions.Categories.InformationFunctionI;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.parser.Node;

/**
 *
 * <AUTHOR>
 */
public class IsRef extends PostfixMathCommand implements CallbackEvaluationI, InformationFunctionI
{
    
    /** Creates a new instance of IsRef */
    public IsRef()
    {
        numberOfParameters = 1;
    }


    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException

    {
        Node child = node.jjtGetChild(0);

        Object object = ((ZSEvaluator)pv).evaluate(child, (Cell)data, false, true);

        if(object instanceof Range) {
            return Value.getInstance(Type.BOOLEAN, true);
        } else {
            return Value.getInstance(Type.BOOLEAN, false);
        }

    }
}
