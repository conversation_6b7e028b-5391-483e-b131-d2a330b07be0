//$Id$
/*
 * Oct2Bin.java
 *
 * Created on July 7, 2008, 4:04 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.EngineeringFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.Stack;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class Oct2Bin extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, EngineeringFunctionI
{
 	public static Logger logger = Logger.getLogger(Oct2Bin.class.getName());
    /** Creates a new instance of Oct2Bin */
    public Oct2Bin()
    {
        numberOfParameters = -1;
    }

         @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        if(num > 2 || num < 1)
        {
            throw Error.NAME.getThrowableObject();
        }

        Stack valueStack = new Stack();

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
	for(int i = 0; i < num; i++)
	{
	    Node child = node.jjtGetChild(i);
        Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            valueStack.push(o);
	}

        Object param2 = null;

        if(num == 2)
        {
            param2 = valueStack.pop();

            if(param2 instanceof Value)
            {
                param2 = ((Value)param2).getValue();
            }

            if(param2 instanceof EvaluationException) {
    throw (EvaluationException)param2;
}
if(param2 instanceof Throwable) {
    throw Error.getError(((Throwable)param2).getMessage()).getThrowableObject();
}

            if(param2 == null)
            {
                throw Error.NUM.getThrowableObject();
            }
            else
            {
                if(param2 instanceof String)
                {
                    param2 = Value.getInstance((String)param2, ((Cell)data).getSpreadsheetSettings()).getValue();

                    if(param2 == null || param2 instanceof String || param2 instanceof Throwable)
                    {
                        throw Error.VALUE.getThrowableObject();
                    }
                }
            }
        }

        Object param1 = valueStack.pop();

        if(param1 instanceof Value)
        {
            param1 = ((Value)param1).getValue();
        }

        if(param1 instanceof EvaluationException) {
    throw (EvaluationException)param1;
}
if(param1 instanceof Throwable) {
    throw Error.getError(((Throwable)param1).getMessage()).getThrowableObject();
}


        String oct = "0";
        Integer places = param2 != null ? FunctionUtil.objectToNumber(param2).intValue() : null;

        if(param1 != null)
        {
            if(param1 instanceof String)
            {
                Value v = Value.getInstance((String)param1, ((Cell)data).getSpreadsheetSettings());

                if(v.getType() != Type.FLOAT)
                {
                    logger.info("Should not give string arguments for Oct2Dec.");
                    throw Error.NUM.getThrowableObject();
                }

                param1 = v.getValue();
            }

            oct = String.valueOf(FunctionUtil.objectToNumber(param1).longValue());
        }

        try {
    return Value.getInstance(Type.STRING, oct2Bin(oct, places));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    public static String oct2Bin(String oct, Integer places)
    {
        long dec = Oct2Dec.oct2Dec(oct);

        return Dec2Bin.dec2Bin(dec, places);
    }

}
