//$Id$
/*
 * DB.java
 *
 * Created on March 20, 2008, 12:22 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.*;
import com.singularsys.jep.parser.Node;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.logging.Logger;
import java.util.logging.Level;


/**
 *
 * <AUTHOR>
 */
public class DB extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, FinancialFunctionI
{
	public static Logger logger = Logger.getLogger(DB.class.getName());
    /** Creates a new instance of DB */
    public DB()
    {
        numberOfParameters = -1;
    }

    /*
     * Checks the number of parameters of the call.
     *
     */
        @Override
    public boolean checkNumberOfParameters(int n)
    {
        return (n == 4 || n == 5);
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        if(!checkNumberOfParameters(num))
        {
            throw Error.NAME.getThrowableObject();
        }

        //Stack valueStack = new Stack();
        Object param[] = new Object[num];

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);

            Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            // must pass values for all required fields, else error will be returned.
            if((i == 0 || i == 2 || i == 3) && (o == null || o instanceof ASTEmptyNode))
            {
                throw Error.NA.getThrowableObject();
//                throw Error.getError("must specify cost, life and period").getThrowableObject();
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            // when a cell containing a number as string is referred no need to change it.
            // but should be converted when given directly as arguments of the function years("1/1/2007",...)
            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof String || o instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            param[i] = o;
        }

        double cost = FunctionUtil.objectToNumber(param[0]).doubleValue();
        double salvage = 0;//default value.
        double life = FunctionUtil.objectToNumber(param[2]).doubleValue();
        int period = FunctionUtil.objectToNumber(param[3]).intValue();
        int months = 12;  // default vaalue.

        // if salvage is null take default salage = 0.
        if(param[1] != null && !(param[1] instanceof ASTEmptyNode))
        {
            salvage = FunctionUtil.objectToNumber(param[1]).doubleValue();
        }

        if(num == 5 && param[4] != null)
        {
            months = FunctionUtil.objectToNumber(param[4]).intValue();
        }

        try {
    return Value.getInstance(Type.CURRENCY, db(cost, salvage, life, period, months));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    // calculates the constant rate of depreciation for each perod
    private static double calculateRate(double cost, double salvage, double life)
    {
        Number num1 = salvage/cost;
        Number num2 = 1/life;

//        Power power = new Power();
//        double temp = ((Number)power.power(num1, num2)).doubleValue();
        double temp = Math.pow(num1.doubleValue(), num2.doubleValue());

        double rate = 1.0 - temp;

        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(3);
        //nf.setMaximumFractionDigits(4); //Rate should be rounded to 3 decimal places

        try
        {
            rate = nf.parse(nf.format(rate)).doubleValue();
        }catch(ParseException e)
        {
			logger.log(Level.WARNING,null,e);
        }
        return rate;
    }

    // to get the total depreciation till the beginning of the given period.
    private static double getPriorDepreciation(double cost, double rate, int period, int months)
    {
        double depreciation = 0;

        for(int i = 1; i < period; i++)
        {
            depreciation += cost * rate;
            cost = cost -depreciation;
        }

        return depreciation;
    }

    /*
     * Cost   :   the initial cost of the asset.
     *
     * salvage:   the value at the end of the depreciation.
     *
     * life   :   the number of periods over which the asset is being
     *            depreciated.
     *
     * period :   the period for which you want to calculate the depreciation.
     *            Period must use the same units as life.
     *
     * return :   the depreciation of an asset for a given , single period using
     *            the fixed-declning balance method.
     *
     */
    public static double db(double cost, double salvage, double life, int period, int months) throws IllegalArgumentException
    {
        if(cost < 0 || salvage < 0 || salvage > cost
             || life < 0 || period < 0 || period > life + 1
             || months < 0 || months > 12)
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        double priorDepr = 0;// total depreciation from prior periods.

        double rate = calculateRate(cost, salvage, life);// constant rate of depreciation.

        // prior depreciation
        for(int i = 1; i < period; i++)
        {
            int tempMonth = (i == 1) ? months : 12;

            priorDepr = cost * rate * tempMonth / 12;

            cost -= priorDepr;
        }

        months = (period == 1) ? months : ((period == life + 1) ? (12 - months) : 12);

        return cost * rate * months/12;



//        if(cost < 0 || salvage < 0 || salvage > cost
//             || life < 0 || period < 0 || period > life + 1
//             || months < 0 || months > 12)
//        {
//            throw new IllegalArgumentException("function has one or more illegal arguments");
//        }
//
//        double rate = calculateRate(cost, salvage, life);
//
//        // for the first period the depreciation will be only for the gien number of months.
//        if(period == 1)
//        {
//            return cost * rate * months / 12;
//        }
//
//        // total depreciation from prior periods.
//        double priorDepr = getPriorDepreciation(cost, rate, period, months);
//
//        // depreciation for the last period
//        if(period == life + 1)
//        {
//            return ((cost - priorDepr) * rate * (12 - months)) / 12;
//        }
//
//        return (cost - priorDepr) * rate;
    }

    public static void main(String args[]) throws Exception
    {
        DB db = new DB();

        for(int i = 1; i < 12; i++)
        {
            double depr = DB.db(10000, 100, 10, i, 4);
            logger.info(i+"  :  "+depr);
        }
    }
}
