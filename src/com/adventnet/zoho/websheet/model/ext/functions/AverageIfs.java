//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.StatisticalFunctionI;

/**
 *
 * <AUTHOR>
 */
public class AverageIfs extends CountIf implements StatisticalFunctionI {

/** Creates a new instance of SumIf */
    public AverageIfs()
    {
        super(AVERAGEIFS, -1);
    }

    @Override
    public boolean isScalarArgument(int index)
    {
        return (index != 0 && (index % 2) == 0) ;
    }

}
