//$Id$
/*
 * CumPrinc.java
 *
 * Created on March 21, 2008, 11:49 AM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class CumPrinc extends PostfixMathCommand implements ScalarFunctionI, FinancialFunctionI, PartiallyExclusiveFunctionI, CallbackEvaluationI
{
	public static Logger logger = Logger.getLogger(CumPrinc.class.getName());
    public static final int CUMPRINC = 1;
    public static final int CUMPRINC_ADD = 2;

    private int id;

    /** Creates a new instance of CumIPMT */
    public CumPrinc()
    {
        this(CUMPRINC);
    }

    public CumPrinc(int id)
    {
        this.id = id;
        numberOfParameters = 6;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator parserVisitor) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if (numChildren != 6) {
            throw Error.NAME.getThrowableObject();
        }

        Object[] param = new Object[numChildren];

        for(int i = 0; i < numChildren; i++)
        {
            Node child = node.jjtGetChild(i);
            Object o = ((ZSEvaluator) parserVisitor).evaluate(child, (Cell) data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            if(param[i] instanceof EvaluationException) {
    throw (EvaluationException)param[i];
}
if(param[i] instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            if((o == null || o instanceof ASTEmptyNode))
            {
                throw Error.NUM.getThrowableObject();
            }

            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o instanceof String)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            param[i] = o;
        }

        double rate  = FunctionUtil.objectToNumber(param[0]).doubleValue();
        double    nper  = FunctionUtil.objectToNumber(param[1]).doubleValue();
        double pv    = FunctionUtil.objectToNumber(param[2]).doubleValue();
        int    start = FunctionUtil.objectToNumber(param[3]).intValue();
        int    end   = FunctionUtil.objectToNumber(param[4]).intValue();
        int    type  = FunctionUtil.objectToNumber(param[5]).intValue();

        if(id == CUMPRINC_ADD && !(type == 0 || type == 1))
        {
            throw Error.NUM.getThrowableObject();
        }

        try
        {
            Type dataType;
            if(id == CUMPRINC)
            {
                dataType = Type.FLOAT;
            }
            else
            {
                dataType = Type.CURRENCY;
            }

            return Value.getInstance(dataType, cumPrinc(rate, nper, pv, start, end, type));
        }catch(IllegalArgumentException e)
        {
            throw Cell.Error.VALUE.getThrowableObject();
//            throw Error.getError(e.getMessage()).getThrowableObject();
        }

    }

    /*
     * rate   :   rate of interest / period.
     *
     * nper   :   number of periods.
     *
     * pv     :   the present value of the money.
     *
     * start  :   the period from which the interet paid to be found.
     *
     * end    :   the period upto which the interest pais is to be found.
     *
     * type   :   says whether the payment is due at the beginning or end of each period.
     *
     * answer :   the principal repaid in the gien period.
     *
     */
    public double cumPrinc(double rate, double nper, double pv, int start, int end, int type) throws IllegalArgumentException
    {
        if(rate <= 0 || nper <= 0 || pv <= 0
                || start < 1 || start > nper
                || end < 0 || end < start ||end > nper
                || (type != 0 && type != 1))
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        // Calculate the constant periodic payment of annuity.
        // This will include interest and principal.
        double pmt = ((Number)PMT.pmt(rate, nper, pv, 0, type)).doubleValue();

        double temp = pv;
        double cumPrinc = 0;
        double tempInt = 0;

        for(int i = 1; i <= end; i++)
        {
            // if type == 1, PMT will be paid at the beginning of every period.
            // Therefore the first PMT will not have any interest in it.
            if(type == 1)
            {
                if(i != 1)
                {
                    tempInt = -(rate * temp);
                }
            }
            else
            {
                tempInt = -(rate * temp);
            }

            double tempPrinc = pmt - tempInt;

            temp += tempPrinc;

            if(i >= start && i <= end)
            {
                cumPrinc += tempPrinc;
            }
        }

        return cumPrinc;
    }

    public static void main(String args[])
    {
        long s = System.currentTimeMillis();
        logger.info(Double.toString((new CumPrinc()).cumPrinc(0.1, 15, 1000, 2, 7, 0)));
        long e = System.currentTimeMillis();
        logger.info("Time Taken  :  "+(e - s));
    }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        return (index == 2);
    }

}
