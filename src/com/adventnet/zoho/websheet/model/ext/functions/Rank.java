//$Id$
/*
 * Rank.java
 *
 * Created on July 23, 2009, 6:24 AM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.StatisticalFunctionI;
import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.parser.Node;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class Rank extends PostfixMathCommand implements CallbackEvaluationI,PartialScalarFunctionI, StatisticalFunctionI
{
	public static Logger logger = Logger.getLogger(Rank.class.getName());
    /** Creates a new instance of Rank */
    public Rank()
    {
        numberOfParameters = -1;
    }

        @Override
    public boolean isScalarArgument(int index)
    {
        return (index != 1);
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        List<Double> range = new ArrayList<>();//new Object[size - 1];
        Number n = 1;
        double type = 0;

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
	for(int i = 0; i < num; i++)
	{
            Node child = node.jjtGetChild(i);
            NonScalarObjectIterator stack = new NonScalarObjectIterator(((ZSEvaluator)pv).evaluate(child, (Cell)data, isScalarArgument(i), false));

            if((i == 0 || i == 2) && stack.size() != 1)
            {
                throw Cell.Error.VALUE.getThrowableObject();
            }

            List<Double> temp = new ArrayList<>();
            while(stack.hasNext())
            {
                Object o = stack.next();

                if(o instanceof Value)
                {
                    o = ((Value)o).getValue();
                }

                if(o == null || o instanceof String)
                {

                    continue;
                }
                
                if(o instanceof ASTEmptyNode)
                {
                    if(i == 2)
                    {
                        o = 0;
                    }
                    else
                    {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }
                }

                if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

                temp.add(FunctionUtil.objectToNumber(o).doubleValue());
                //temp.push(o);
            }

            if(temp.isEmpty())
            {
                if(i ==1)
                {
                   throw Cell.Error.NA.getThrowableObject();
                }
                else
                {
                    throw Cell.Error.VALUE.getThrowableObject();
                }

            }

            if(i == 0)
            {
                n = FunctionUtil.objectToNumber(temp.get(0));
            }
            else if(i == 1)
            {
                range = temp;
            }
            else
            {
                type = ((Number)FunctionUtil.objectToNumber(temp.get(0))).doubleValue();
            }
        }

        return Value.getInstance(Type.FLOAT, getResult(n.doubleValue(), range, type));
    }

    public int getResult(double num, List<Double> range, double type) throws EvaluationException
    {
        Collections.sort(range);
        int result;
        if(type == 0)
        {
            result = range.lastIndexOf(num);
            if(result == -1)
            {
                throw Error.NA.getThrowableObject();
            }
            return (range.size() - result);
        }
        else
        {
            result = range.indexOf(num);
            if(result == -1)
            {
                throw Error.NA.getThrowableObject();
            }
            return result + 1;
        }
    }
}
