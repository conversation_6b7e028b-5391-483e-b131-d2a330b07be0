//$Id$
/*
 * TimeValue.java
 *
 * Created on April 7, 2008, 12:13 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.SpreadsheetSettings;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import java.text.ParseException;
import java.util.Date;


/**
 *
 * <AUTHOR>
 */
public class TimeValue extends DateValue//PostfixMathCommand implements ScalarFunctionI
{
	//public static final Logger LOGGER = Logger.getLogger(TimeValue.class.getName());
    /** Creates a new instance of TimeValue */
    public TimeValue()
    {
        numberOfParameters = 1;
    }

    /*
    public void run(Stack<Object> inStack) throws EvaluationException
    {
        if(curNumberOfParameters != 1)
        {
            throw Error.VALUE.getThrowableObject();
        }

        checkStack(inStack); // check the stack

        Object param = inStack.pop();

        if(param instanceof Value)
        {
            param = ((Value)param).getValue();
        }

        if(param == null)
        {
            throw Error.VALUE.getThrowableObject();
        }
        else if(param instanceof Throwable)
        {
            throw new EvaluationException(((Throwable)param).getMessage());
        }
        // param can take up only String values.
        else if(!(param instanceof String))
        {
            throw Error.VALUE.getThrowableObject(); // Excel Behavior
        }

        try
        {
            inStack.push(new Value(Type.FLOAT, timeValue((String)param)));
        }
        catch(IllegalArgumentException e)
        {
            throw Error.VALUE.getThrowableObject();
        }
        catch(ClassCastException e)
        {
            throw Error.VALUE.getThrowableObject();
        }
    }
     *
     */

    @Override
    protected double getResult(String dateStr, SpreadsheetSettings spreadsheetSettings) throws IllegalArgumentException
    {
        return timeValue(dateStr, spreadsheetSettings);
    }

    /*
     * Returns the number equivalent to the time given as String.
     *
     */
    public static double timeValue(String time, SpreadsheetSettings spreadsheetSettings)throws IllegalArgumentException
    {
        Date date;
        try
        {
           if(DateUtil.isTime(time, spreadsheetSettings))
           {
                date = DateUtil.parseTime(time, spreadsheetSettings);
           }
           else
           {
               try
               {
                    date =DateUtil.parseDate(time, spreadsheetSettings);
               }
               catch(Exception e)
               {
                   throw new IllegalArgumentException(CellUtil.getErrorString(Cell.Error.VALUE));
               }
           }

           if(date == null)
           {
               throw new IllegalArgumentException(CellUtil.getErrorString(Cell.Error.VALUE));
           }
            //return DateUtil.convertDateToNumber(date).doubleValue();
        }
        catch(Exception e)
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Cell.Error.VALUE));
            //return DateUtil.convertDateToNumber(date).doubleValue();
        }

        Number days = DateUtil.convertDateToNumber(date);
        return days.doubleValue() - days.longValue();
    }

    public static void main(String[] args) throws Exception
    {
        //logger.info(DateUtil.isTime("3"));
    }

}
