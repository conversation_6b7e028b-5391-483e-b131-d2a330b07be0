//$Id$
/*
 * ZDate.java
 *
 * Created on April 4, 2008, 4:46 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.DateTimeFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ZDate extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, DateTimeFunctionI
{
	public static final Logger LOGGER = Logger.getLogger(ZDate.class.getName());
    /**
     * Creates a new instance of ZDate
     */
    public ZDate()
    {
        numberOfParameters = 3;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        //Stack valueStack = new Stack();
        Object[] param = new Object[3];

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
	for(int i = 0; i < num; i++)
	{
	    Node child = node.jjtGetChild(i);
        Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof Throwable || o instanceof String)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

	    //valueStack.push(o);
            param[i] = o;
	}

        //Object[] param = new Object[3];

//        for(int i = num; i > 0; i--)
//        {
//            param[i - 1] = valueStack.pop();
//        }

//        for(int i = 0; i < num; i++)
//        {
//            Object o = param[i];
//
//            if(o instanceof String)
//            {
//                o = new Value((String)o,  book.getFunctionLocale()).getValue();
//
//                if(o == null || o instanceof Throwable || o instanceof String)
//                {
//                    throw Error.VALUE.getThrowableObject();
//                }
//            }
//
//            param[i] = o;
//        }

        int year = 0;
        int month = 0;
        int day = 0;

        if(param[0] != null)
        {
            year = FunctionUtil.objectToNumber(param[0]).intValue();
        }

        if(param[1] != null && !(param[1] instanceof ASTEmptyNode))
        {
            month = FunctionUtil.objectToNumber(param[1]).intValue();
        }

        if(param[2] != null)
        {
            day = FunctionUtil.objectToNumber(param[2]).intValue();
        }

        try {
    return Value.getInstance(Type.DATE, date(year, month, day));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }


    /*
     * Returns the date according to the given year, month & day.
     *
     */
    public static java.util.Date date(int year, int month, int day) throws IllegalArgumentException
    {
        if(year < 0)
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        Calendar date = new GregorianCalendar();

        date.setTime(DateUtil.convertNumberToDate(0));

        if(year < 1900)
        {
            date.add(Calendar.YEAR, year + 1);
        }
        else
        {
            date.set(Calendar.YEAR, year);
        }

        date.set(Calendar.MONTH, month - 1);
        date.set(Calendar.DATE, day);

        return date.getTime();
    }
}
