//$Id$
/*
 * Code.java
 *
 * Created on July 3, 2008, 9:56 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.TextFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class Code extends PostfixMathCommand implements ScalarFunctionI, TextFunctionI, CallbackEvaluationI
{
	public static Logger logger = Logger.getLogger(Code.class.getName());
    /** Creates a new instance of Code */
    public Code()
    {
        numberOfParameters = 1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if (numChildren != 1) {
            throw Cell.Error.VALUE.getThrowableObject();
        }

        Node child = node.jjtGetChild(0);
        Object param = ((ZSEvaluator) pv).evaluate(child, (Cell) data, true, false);

        if(param instanceof Value)
        {
            param = ((Value)param).getValue();
        }

        if(param instanceof EvaluationException) {
    throw (EvaluationException)param;
}
if(param instanceof Throwable) {
    throw Cell.Error.getError(((Throwable)param).getMessage()).getThrowableObject();
}

        Object result = 0;

        if(param != null)
        {
            if(!(param instanceof String))
            {
                param = FunctionUtil.objectToNumber(param);
            }

            result = code(String.valueOf(param));
        }

        return Value.getInstance(Type.FLOAT, result);
    }

    /*
     * Returns the code foor the first char in the given string.
     *
     */
    public static int code(String str)
    {
        if("".equals(str))
        {
            return 0;
        }

        return str.charAt(0);
    }

    public static void main(String[] args) throws Exception
    {
        logger.info(Integer.toString(code("12")));
    }
}
