//// $Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.style.datastyle.ZSPattern;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.TextFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class Text extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, TextFunctionI
{
    public static Logger logger = Logger.getLogger(Text.class.getName());
    public Text()
    {
        numberOfParameters = 2;
    }
    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        Object value = null;
        String format = null;
        int num = node.jjtGetNumChildren();
        if(num != 2)
        {
            throw Error.VALUE.getThrowableObject();
        }        
        
        for(int i=0; i< num; i++)
        {
            Node child = node.jjtGetChild(i);
            Object param = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);
            if(param instanceof Value)
            {
                param = ((Value)param).getValue();
            }
            if(i == 1)
            {
                if(param instanceof Boolean)
                {
                    throw Cell.Error.VALUE.getThrowableObject();
                }
                if(param != null && !(param instanceof String))
                {
                    param = String.valueOf(FunctionUtil.objectToNumber(param));
                }
                format = (String)param;
            }
            else
            {
                value = param;
            }
        }

        try
        {
            return Value.getInstance(Type.STRING, text(((Cell)data).getRow().getSheet().getWorkbook(), value, format));
        }catch(IllegalArgumentException e)
        {
            throw Cell.Error.VALUE.getThrowableObject();
//            throw Error.getError(e.getMessage()).getThrowableObject();
        }
    }

    private static String text(Workbook workbook, Object value, String format) throws IllegalArgumentException
    {
        if(value instanceof String)
        {
            if("".equals(value))
            {
                return "";
            }
            Value tempValue = Value.getInstance((String)value, workbook.getSpreadsheetSettings());
            value = ((Value)tempValue).getValue();
        }

        if(value == null)
        {
            value = 0;
        }
        
        if(value instanceof Throwable)
        {
    throw new IllegalArgumentException(((Throwable)value).getMessage());
}

        try
        {
            //String formatText = Utility.validateFormatString(format);
            //com.adventnet.zoho.websheet.model.style.Pattern pattern = new com.adventnet.zoho.websheet.model.style.Pattern(format, locale);
             ZSPattern pattern = ZSPattern.getCustomPattern(format, workbook.getSpreadsheetSettings().getLocale(), workbook.getSpreadsheetSettings());
            Value valueObj = (value instanceof String) ? Value.getInstance(Type.STRING, value): Value.getInstance(Type.FLOAT, FunctionUtil.objectToNumber(value));
            return pattern.formatValue(workbook, valueObj).getContent();
        }
        catch(IllegalArgumentException e)
        {
            
        }
        catch(EvaluationException e)
        {
            
        }

        throw new IllegalArgumentException(CellUtil.getErrorString(Error.VALUE));
    }     
}
