//$Id$
package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.parser.Node;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.StatisticalFunctionI;

public class Prob extends PostfixMathCommand implements CallbackEvaluationI,PartialScalarFunctionI,ArrayFunctionI, StatisticalFunctionI
{

    public Prob()
    {
        numberOfParameters = -1;
    }

    @Override
    public boolean isScalarArgument(int index)
    {
        return ((index != 0 && index != 1));
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren(); //3

        if ((num != 3) && (num != 4))
        {
            throw Cell.Error.NAME.getThrowableObject();
        }

        double probSum = 0;
        double start = 0;
        double end = 0;
        double values[] = null;
        double probability[] = null;

        for (int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);
            NonScalarObjectIterator stack = new NonScalarObjectIterator(((ZSEvaluator)pv).evaluate(child, (Cell) data, isScalarArgument(i), false));

            if ((i == 2 || i == 3) && stack.size() != 1)
            {
                throw Cell.Error.VALUE.getThrowableObject();
            }
            int size = stack.size();
            double[] val = new double[size];
            for (int j = 0; j < size; j++)
            {
                Object o = stack.next();
                if (o instanceof Value)
                {
                    o = ((Value) o).getValue();
                }

                if (o == null || o instanceof ASTEmptyNode)
                {
                    if (i == 0 || i == 1)
                    {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }
                    else
                    {
                        o = 0;
                    }
                }
                else if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Cell.Error.getError(((Throwable) o).getMessage()).getThrowableObject();
}

                if (o instanceof String)
                {
                    if (i == 0 || i == 1)
                    {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }
                    else
                    {
                        o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                        if (o == null || o instanceof String || o instanceof Throwable)
                        {
                            throw Cell.Error.VALUE.getThrowableObject();
                        }
                    }
                }

                val[j] = FunctionUtil.objectToNumber(o).doubleValue();
                if (i == 1)
                {
                    if(val[j] < 0 || val[j] > 1)
                    {
                        throw Cell.Error.NUM.getThrowableObject();
                    }
                    probSum = probSum + val[j];
                }
            }

            if (i == 0)
            {
                values = val;
            }
            else if (i == 1)
            {
                if (probSum != 1)
                {
                    throw Cell.Error.NUM.getThrowableObject();
                }
                
                probability = val;
            }
            else if (i == 2)
            {
                start = val[0];
                end = start;
            }
            else
            {
                end = val[0];
            }
        }

        if (probability.length != values.length)
        {
            throw Cell.Error.NA.getThrowableObject();
        }
        
        return Value.getInstance(Cell.Type.FLOAT, prob(values, probability, start, end));
    }
    

    private double prob(double values[], double probability[], double start, double end)
    {
        double result = 0;
        if (end < start)
        {
            double temp = start;
            start = end;
            end = temp;
        }
        for (int i = 0; i < values.length; i++)
        {
            if ((values[i] <= end) && (values[i] >= start))
            {
                result = result + probability[i];
            }

        }
        
        return result;
    }

}
