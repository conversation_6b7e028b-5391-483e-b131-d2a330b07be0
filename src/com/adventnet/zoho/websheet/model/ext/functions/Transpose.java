// $Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.ReferenceFunctionI;
import com.singularsys.jep.parser.Node;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Transpose extends PostfixMathCommand implements CallbackEvaluationI, ReferenceFunctionI
{
    public Transpose()
    {
        numberOfParameters = 1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        //int num = node.jjtGetNumChildren();

//        Sheet sheet = ((Cell)data).getRow().getSheet();
        ZArrayI arrayOrRange = null;

        Node child = node.jjtGetChild(0);
        Object evaluate = ((ZSEvaluator)pv).evaluate(child, (Cell)data, false, true);

        if(evaluate instanceof ZArrayI) {
            arrayOrRange = (ZArrayI)evaluate;
        }
        else
        {
            NonScalarObjectIterator stack = new NonScalarObjectIterator(evaluate);
            if(stack.size() != 1)
            {
                throw Cell.Error.VALUE.getThrowableObject();
            }
            Object o = stack.next();
            List cons_List = new ArrayList();
            cons_List.add(o);
            arrayOrRange = new ZArray(cons_List,1,1);
        }

        return transpose_New(arrayOrRange);
    }
    public static ZArray transpose(ZArrayI arrayOrRange)
    {
        int length = arrayOrRange.getRowSize();
        int breadth = arrayOrRange.getColSize();
        List result_List = new ArrayList();
        int startRowIndex = 0;
        int startColIndex = 0;
        if(arrayOrRange instanceof Range)
        {
            startRowIndex = ((Range)arrayOrRange).getStartRowIndex();
            startColIndex = ((Range)arrayOrRange).getStartColIndex();
        }

        for (int matrixColIndex = 0; matrixColIndex < breadth; matrixColIndex++)
        {
            for (int matrixRowIndex = 0; matrixRowIndex < length; matrixRowIndex++)
            {
                result_List.add(arrayOrRange.getValue(matrixRowIndex, matrixColIndex));
            }
        }        
        return (new ZArray(result_List,breadth,length));
    }

    public static ZArrayI transpose_New(ZArrayI arrayOrRange)
    {
        return new TransposedZArrayI(arrayOrRange);
    }
}
