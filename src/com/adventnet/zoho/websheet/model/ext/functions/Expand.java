package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;

import java.util.ArrayList;
import java.util.List;

public class Expand extends PostfixMathCommand implements CallbackEvaluationI {

    public Expand() {
        this.numberOfParameters = -1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int noOfChild = node.jjtGetNumChildren();
        if (noOfChild < 2 || noOfChild > 4) {
            throw Cell.Error.NAME.getThrowableObject();
        }

        ZArrayI array = null;
        int row = 0;
        int col = 0;
        Value padWith = null;
        for (int i = 0; i < noOfChild; i++) {
            Object value = ((ZSEvaluator) pv).evaluate(node.jjtGetChild(i), (Cell) data, false, false);

            if (value instanceof ASTEmptyNode) {
                if (i == 0) {
                    throw Cell.Error.VALUE.getThrowableObject();
                }
            }

            if (value instanceof ZArrayI) {
                if (i == 0) {
                    array = (ZArrayI) value;
                    row = array.getRowSize();
                    col = array.getColSize();
                } else {
                    if (((ZArrayI) value).getSize() == 1) {
                        value = ((ZArrayI) value).getValue(0, 0);
                    } else {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }
                }
            }

            if (value instanceof Value) {
                if (i == 0) {
                    if (value.equals(Value.EMPTY_VALUE)) {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }
                    List<Object> temp = new ArrayList<>();
                    temp.add(value);
                    array = new ZArray(temp, 1, 1);
                    row = 1;
                    col = 1;
                } else if (i == 1 || i == 2) {
                    if (value.equals(Value.EMPTY_VALUE)) {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }
                    int tempValue = FunctionUtil.objectToNumber(value).intValue();

                    if (tempValue < (i == 1 ? array.getRowSize() : array.getColSize())) {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }

                    if (i == 1) {
                        row = tempValue;
                    } else {
                        col = tempValue;
                    }
                } else {
                    padWith = (Value) value;
                }
            }
        }
        return new ExpandedZArray(array, row, col, padWith);
    }
}
