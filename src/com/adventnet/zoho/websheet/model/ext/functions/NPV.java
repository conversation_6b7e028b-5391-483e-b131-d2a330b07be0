//$Id$
/*
 * NPV.java
 *
 * Created on March 17, 2008, 6:01 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.functions.*;
import java.util.*;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.parser.Node;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class NPV extends PostfixMathCommand implements CallbackEvaluationI,PartialScalarFunctionI, FinancialFunctionI, PartiallyExclusiveFunctionI
{
 	public static Logger logger = Logger.getLogger(NPV.class.getName());
    /** Creates a new instance of NPV */
    public NPV()
    {
        numberOfParameters = -1;
    }

         @Override
    public boolean isScalarArgument(int index)
    {
        return (index == 0);
    }

         @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

         if(num < 2)
         {
             throw Error.NAME.getThrowableObject();
         }

         Stack valueStack = new Stack();

         // Evaluate each node and get the value,
         // For this function each argument but 3rd, denotesr a unique field so the stack we get must not have more than one element.
         // else throw error.
	 for(int i = 0; i < num; i++)
	 {
             Node child = node.jjtGetChild(i);
	     NonScalarObjectIterator stack = new NonScalarObjectIterator(((ZSEvaluator)pv).evaluate(child, (Cell)data, isScalarArgument(i), false));

             int size = stack.size();

             // Only the third argument can be a Range for this function.
	     if(i == 0 &&  size != 1)
	     {
                 throw Cell.Error.VALUE.getThrowableObject();
	     }

             while (stack.hasNext())
             {
                 Object o = stack.next();

                 if(o instanceof Value)
                 {
                     o = ((Value)o).getValue();
                 }

                 if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

                 if(o == null || o instanceof ASTEmptyNode)
                 {
                     o = 0;
                 }

                 // Should change any string representing a number or date etc to corresponding object
                 // ie. when a cell containing a number as string is referred no need to change it.
                 // but should be converted when given directly as arguments of the function years("1/1/2007",...)
                 if(o instanceof String)
                 {
                     if (((String) o).trim().isEmpty()) {
                         o = 0;
                     } else {
                         o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                         if (o == null || o instanceof String || o instanceof Throwable) {
                             throw Error.VALUE.getThrowableObject();
                         }
                     }
                 }

                 valueStack.push(o);
             }
	 }

         int size = valueStack.size();

         double[] arptr = new double[size - 1];

         for(int i = size; i > 1; i--)
         {
             arptr[i - 2] = FunctionUtil.objectToNumber(valueStack.pop()).doubleValue();
         }

         double rate = FunctionUtil.objectToNumber(valueStack.pop()).doubleValue();

         return Value.getInstance(Type.CURRENCY, npv(rate, arptr));
    }

    /*
     * arptr   :  array of Doubles that specifies the name of an existing array
     *            of cash flow values. Rule for <casharray>: Array must contain
     *            at least one positive value (receipt) and one negative value
     *            (payment)
     *
     * rate    :  the rate of discount over the length of one period.
     *
     * answer  :  the net present value of a varying series of periodic cash flows,
     *            both positive and negative, at a given interest rate
     *
     * Remarks
     *
     * The NPV investment begins one period before the date of the value1 cash
     * flow and ends with the last cash flow in the list. The NPV calculation is
     * based on future cash flows. If your first cash flow occurs at the
     * beginning of the first period, the first value must be added to the NPV
     * result, not included in the values arguments. For more information, see
     * the examples below. If n the number of cash flows in the list of values,
     * the formula for NPV is:
     *
     *
     * NPV is similar to the PV function (present value). The primary difference
     * between PV and NPV is that PV allows cash flows to begin either at the
     * end or at the beginning of the period. Unlike the variable NPV cash flow
     * values, PV cash flows must be constant throughout the investment. For
     * information about annuities and financial functions, see PV. NPV is also
     * related to the IRR function (internal rate of return). IRR is the rate
     * for which NPV equals zero: NPV(IRR(...), ...) = 0.
     */
    public static double npv(double rate, double[] arptr) throws IllegalArgumentException
    {
        if (arptr == null)
        {
            throw new IllegalArgumentException("There exists illegal parameter:" + arptr);
        }
        double npv = 0;
        double r1 = 1 + rate;
        double trate = r1;
        for ( int i = 0; i < arptr.length; i++ )
        {
            npv += arptr[i] / trate;
            trate = trate * r1;
        }

        return npv;
    }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        return (index > 0);
    }
}
