//$Id$
/*
 * Current.java
 *
 * Created on March 31, 2009, 4:39 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;


import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.InformationFunctionI;
import com.singularsys.jep.parser.ASTFunNode;
import com.singularsys.jep.parser.ASTStart;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class Current extends PostfixMathCommand implements CallbackEvaluationI, InformationFunctionI
{
	public static Logger logger = Logger.getLogger(Current.class.getName());
    /** Creates a new instance of Current */
    public Current()
    {
        numberOfParameters = 0;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        Node parent = node.jjtGetParent();
        Node child = parent.jjtGetChild(0);

        //while((parent instanceof ASTFunNode && !(parent instanceof ASTOpNode)) || (child instanceof ASTFunNode && ((ASTFunNode)child).getName().equals(((ASTFunNode)node).getName())))
        while(parent instanceof ASTFunNode && child instanceof ASTFunNode && child == node)
        {
            node = parent;
            parent = parent.jjtGetParent();
            child = parent.jjtGetChild(0);
        }


        if(parent instanceof ASTStart)
        {
            throw Error.VALUE.getThrowableObject();
        }
        else if(parent instanceof ASTFunNode)
        {
            // For formulae like =sum(1;2;3;current()) and  current should take the
            // last argument value before itself.
            int size = parent.jjtGetNumChildren();

            for(int i = 1; i < size; i++)
            {
                Node tempChild = parent.jjtGetChild(i);

                if(tempChild == node)
                {
                    child = parent.jjtGetChild(i - 1);
                    break;
                }
            }
        }

        NonScalarObjectIterator stack = new NonScalarObjectIterator(((ZSEvaluator)pv).evaluate(child, (Cell)data, false, false));

        if(stack.size() > 1)
        {
            throw Cell.Error.VALUE.getThrowableObject();
        }

        return stack.next();
    }

}
