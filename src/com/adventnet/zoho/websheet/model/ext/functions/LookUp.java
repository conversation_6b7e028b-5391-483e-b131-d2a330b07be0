//$Id$



/*
 * LookUp.java
 *
 * Created on April 6, 2009, 4:18 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.ZArray;
import com.adventnet.zoho.websheet.model.ZArrayI;
import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.LookupFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class LookUp extends PostfixMathCommand implements CallbackEvaluationI,PartialScalarFunctionI, LookupFunctionI
{
    public static final Logger LOGGER = Logger.getLogger(LookUp.class.getName());
    public static final int DEFAULT = 0;
    public static final int VERTICAL = 1;
    public static final int HORIZANTAL = 2;
    public static final int MATCH = 3;

    protected int id = 0;
    protected boolean isMatchRegex = true;

    public LookUp(int id)
    {
        this.id = id;
        numberOfParameters = -1;
    }

    public LookUp()
    {
        this.id = LookUp.DEFAULT;
        numberOfParameters = -1;
    }

    @Override
    public boolean isScalarArgument(int index) {
        //LOOKUP(lookupvalue; searchtable; resulttable)
        //HLOOKUP(lookupvalue; datatable; rowindex; mode)
        //VLOOKUP(lookupvalue; datatable; columnindex; mode)
        //MATCH(searchitem; searchregion; matchtype)

        switch (id) {
            case DEFAULT:
                return index == 0;
            case MATCH:
            case HORIZANTAL:
            case VERTICAL:
                return index != 1;
            default:
                return index == 0;
        }
    }

    @Override
    public boolean checkNumberOfParameters(int n)
    {
        if(id == VERTICAL || id == HORIZANTAL)
        {
            return (n == 3 || n == 4);
        }
        else
        {
            return (n == 2 || n == 3);
        }
    }


    // Second node is the range to look up so should handle in a different manner.
    // Third is also a Range for LOOKUP function.


    /*
     * Given a LookUp-Value, a range, a lookUp-Column
     * Checks in the first column for the row having,
     * the greatest number less than or equal to the lookUp-Value
     * Then returns the value in the cell wat the found row and given lookUp Column.
     *
     */
    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        if(!checkNumberOfParameters(num))
        {
            throw Error.NAME.getThrowableObject();
        }

        Object lookUpValue = null;
        ZArrayI lookUp_Range = null;
        ZArrayI result_Range = null;
        int result_ColOrRow = 1;
        int comparatorType = 1;

        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);

            if((id == DEFAULT) ? (i != 1 && i != 2) : (i != 1))
            {
                NonScalarObjectIterator stack = new NonScalarObjectIterator(((ZSEvaluator)pv).evaluate(child, (Cell)data, isScalarArgument(i), false));

                if(stack.size() != 1)
                {
                    LOGGER.log(Level.INFO, "Given arguments is not proper Stack : {0}", stack);
                    throw Cell.Error.VALUE.getThrowableObject();
                }

                Object temp = stack.next();

                if(temp instanceof Value)
                {
                    temp = ((Value)temp).getValue();
                }

                if(temp instanceof ASTEmptyNode)
                {
                    if(i == 3){
                        continue;
                    }
                    throw Error.VALUE.getThrowableObject();
                }

                if (temp instanceof EvaluationException) {
                    throw (EvaluationException) temp;
                }

                if(temp instanceof Throwable) {
                    throw Error.getError(((Throwable)temp).getMessage()).getThrowableObject();
                }

                if(temp == null)
                {
//                    if(i == 0)//lookup value can not be an empty cell.
//                    {
//                        throw throw Error.NA.getThrowableObject();;
//                    }
                    temp = 0;
                }

                if(i == 0)
                {
                    lookUpValue = temp;
                }
                else if(i == 3)
                {
                    try
                    {
                        comparatorType = FunctionUtil.objectToNumber(temp).intValue();
                    }
                    catch (EvaluationException e)
                    {
                        throw Error.VALUE.getThrowableObject();
                    }
                }
                else if(i == 2)
                {
                    int tempNum;
                    try
                    {
                        tempNum = FunctionUtil.objectToNumber(temp).intValue();
                    }
                    catch (EvaluationException e)
                    {
                        throw Error.VALUE.getThrowableObject();
                    }

                    if(id == MATCH)
                    {
                        comparatorType = tempNum;
                    }
                    else // VLOOKUP AND HLOOKUP
                    {
                        result_ColOrRow = tempNum;
                        if(result_ColOrRow < 1)
                        {
                            throw Error.VALUE.getThrowableObject();
                        }else if(id == VERTICAL ? result_ColOrRow > lookUp_Range.getColSize() : result_ColOrRow > lookUp_Range.getRowSize()){
                            throw Error.REF.getThrowableObject();
                        }
                    }
                }
            }
            else
            {
                ZArrayI tempArray = null;

                Object result = ((ZSEvaluator)pv).evaluate(child, (Cell)data, isScalarArgument(i), false);

                if(result instanceof ZArrayI) {
                    tempArray = (ZArrayI)result;
                } else if(child instanceof ASTEmptyNode) {
                    throw Error.VALUE.getThrowableObject();
                } else {
                    List list = new ArrayList();
                    list.add(result);
                    tempArray = new ZArray(list, 1, 1);
                }


                if(i == 1)
                {
                    if(id == MATCH && tempArray.getRowSize() != 1 && tempArray.getColSize() != 1){
                        throw Error.VALUE.getThrowableObject();
                    }
                    lookUp_Range = tempArray;
                }
                else // i == 2 for look up
                {
                    if(tempArray.getRowSize() != 1 && tempArray.getColSize() != 1){
                        throw Error.VALUE.getThrowableObject();
                    }
                    result_Range = tempArray;
                }
            }
        }

        XLookUp.Lookup_Type lookupType;
        XLookUp.Match_Type matchType;
        boolean isVertical = (id == VERTICAL || id != HORIZANTAL && lookUp_Range.getRowSize() >= lookUp_Range.getColSize());

        switch (comparatorType) {
            case 0:
                lookupType = XLookUp.Lookup_Type.UNSORTED_FIRST_TO_LAST;
                matchType = XLookUp.Match_Type.MATCH;
                break;
            case 2:
                lookupType = XLookUp.Lookup_Type.UNSORTED_FIRST_TO_LAST;
                matchType = XLookUp.Match_Type.EXACT;
                break;
            case -1:
                lookupType = XLookUp.Lookup_Type.DESCENDING_SORTED;
                matchType = XLookUp.Match_Type.EXACT_OR_NEXT_LARGER;
                break;
            case 1:
            default:
                lookupType = XLookUp.Lookup_Type.ASCENDING_SORTED;
                matchType = XLookUp.Match_Type.EXACT_OR_NEXT_SMALLER;
        }

        int resultPos = XMatch.xMatch(((Cell)data).getRow().getSheet().getWorkbook(), lookUpValue, lookUp_Range, matchType, lookupType, isVertical);
        if(id == MATCH){
            return resultPos;
        }else {
            // VLOOKUP, HLOOKUP cases
            if(result_Range == null)
            {
                result_Range = lookUp_Range;
            }
            ////////
            return isVertical ? result_Range.getValueWithPattern(resultPos - 1, result_ColOrRow - 1) : result_Range.getValueWithPattern(result_ColOrRow - 1, resultPos - 1);
        }
    }

}
