//$Id$
/*
 * ERF.java
 *
 * Created on September 15, 2009, 4:32 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.EngineeringFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class ErrorFn extends PostfixMathCommand implements ScalarFunctionI, EngineeringFunctionI, CallbackEvaluationI
{
	public static Logger logger = Logger.getLogger(ErrorFn.class.getName());
    public static final int ERF = 1;
    public static final int ERFC = 2;

    private int id;
    /** Creates a new instance of ERF
     * @param id */
    public ErrorFn(int id)
    {
        if(id == ERFC)
        {
            numberOfParameters = 1;
        }
        else
        {
            numberOfParameters = -1;
        }
        this.id = id;
    }

        @Override
    public boolean checkNumberOfParameters(int n)
    {
	return id == ERFC ? n == 1 : (n == 1 || n == 2);
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();
        if(!checkNumberOfParameters(numChildren))
        {
            throw Error.NAME.getThrowableObject();
        }

        double[] vals = new double[numChildren];

        for(int i = 0; i < numChildren; i++)
        {
            Node child = node.jjtGetChild(i);
            Object o = ((ZSEvaluator) pv).evaluate(child, (Cell) data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            if(o instanceof ASTEmptyNode)
            {
                throw Error.VALUE.getThrowableObject();
            }

            if(o == null)
            {
                o = 0;
            }
            else if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof String || o instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            vals[i] = FunctionUtil.objectToNumber(o).doubleValue();
        }

        double a = 0;
        int i = 0;
        if(vals.length == 2)
        {
            a = vals[i++];
        }
        double b = vals[i];

        try {
    return Value.getInstance(Type.FLOAT, id == ERFC ? erfc(b) : erf(a, b));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    public double erf(double a, double b) throws EvaluationException
    {
        try
        {
            double erf_B = org.apache.commons.math.special.Erf.erf(b);
            double erf_A = org.apache.commons.math.special.Erf.erf(a);

            return erf_B - erf_A;
        }
        catch(org.apache.commons.math.MathException e)
        {
            throw Error.VALUE.getThrowableObject();
        }
    }

    public double erfc(double a) throws EvaluationException
    {
        return (1 - erf(0,a));
    }

    public static void main(String[] args) throws Exception
    {
        logger.info(Double.toString(org.apache.commons.math.special.Erf.erf(1.3)));
    }
}
