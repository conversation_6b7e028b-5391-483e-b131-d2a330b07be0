//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.singularsys.jep.EvaluationException;

public class GammaDist extends Distribution implements PartiallyExclusiveFunctionI
{
    public GammaDist()
    {
        numberOfParameters = 4;
    }

    @Override
    public boolean checkNumberOfParameters(int n)
    {
        return n == 4;
    }

    @Override
    public double getResult(Number[] values ) throws EvaluationException
    {
        return Distribution.getGammaDist(values[0].doubleValue(),values[1].doubleValue(),values[2].doubleValue(),values[3].doubleValue()!=0);
    }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        return index == 0;
    }
}
