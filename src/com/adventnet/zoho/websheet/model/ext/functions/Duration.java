//$Id$


package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.singularsys.jep.functions.StrictNaturalLogarithm;
import com.singularsys.jep.parser.Node;

import java.util.Locale;


public class Duration extends PostfixMathCommand implements ScalarFunctionI, FinancialFunctionI, CallbackEvaluationI
{
    public Duration()
    {
        numberOfParameters = 3;
    }

    @Override
    public boolean checkNumberOfParameters(int n)
    {
        return n == 3;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if(!checkNumberOfParameters(numChildren))
        {
            throw Cell.Error.VALUE.getThrowableObject();
        }

        Number[] vals = new Number[numChildren];

        for(int i=0; i < numChildren; i++ )
        {
            Node child = node.jjtGetChild(i);
            Object param = ((ZSEvaluator) pv).evaluate(child, (Cell) data, true, false);

            if(param instanceof Value)
            {
                param = ((Value)param).getValue();
            }

            if(param == null || param instanceof ASTEmptyNode)
            {
                throw Cell.Error.NUM.getThrowableObject();

            }

            if(param instanceof EvaluationException) {
    throw (EvaluationException)param;
}
if(param instanceof Throwable) {
    throw Cell.Error.getError(((Throwable)param).getMessage()).getThrowableObject();
}

            if(param instanceof String)
            {
                throw Cell.Error.NUM.getThrowableObject();
            }


            vals[i] = FunctionUtil.objectToNumber(param);
        }

        Object result = getDuration(vals[0].doubleValue(), vals[1].doubleValue(), vals[2].doubleValue(), ((Cell)data).getFunctionLocale());

        Value value = Value.getInstance(Cell.Type.FLOAT, result);

        return value;
    }


    public Object getDuration(double rate, double present, double future, Locale locale) throws EvaluationException
    {
        StrictNaturalLogarithm log = new StrictNaturalLogarithm();
        return log.ln((future/present),(1+rate), locale);

    }

    @Override
    public boolean isExcelFutureFunction() {
        return true;
    }
}
