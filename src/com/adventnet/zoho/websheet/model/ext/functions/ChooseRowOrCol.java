package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.ReferenceFunctionI;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;

import java.util.*;

public class ChooseRowOrCol extends PostfixMathCommand implements CallbackEvaluationI, ReferenceFunctionI {
    public static final int ROW = 1;
    public static final int COL = 2;

    private final int functionID;

    public ChooseRowOrCol(int id){
        functionID = id;
        this.numberOfParameters = -1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int noOfChild = node.jjtGetNumChildren();
        if(noOfChild == 0 || noOfChild >= 255){
            throw Cell.Error.NAME.getThrowableObject();
        }

        ZArrayI array = null;
        StackedZArray.Type stackType = null;
        List<ZArrayI> arraysToStack = new ArrayList<>();
        for(int i = 0; i < noOfChild; i++){
            Object value = ((ZSEvaluator)pv).evaluate(node.jjtGetChild(i), (Cell) data, false, true);
            if(i == 0) {
                if (value instanceof ZArrayI) {
                    array = (ZArrayI) value;
                } else {
                    List<Object> valueList = new ArrayList<>();
                    valueList.add(value);
                    array = new ZArray(valueList, 1, 1);
                }
            }else {
                ZArrayI temp;
                if(value instanceof Value){
                    List<Object> valueList = new ArrayList<>();
                    valueList.add(value);
                    temp = new ZArray(valueList, 1, 1);
                } else if (value instanceof ZArrayI) {
                    temp = (ZArrayI) value;
                    if(temp.getSize() != 1) {
                        if (temp.getRowSize() == 1) {
                            if (stackType == null) {
                                stackType = StackedZArray.Type.H_STACK;
                            }
                        } else if (temp.getColSize() == 1) {
                            if (stackType == null) {
                                stackType = StackedZArray.Type.V_STACK;
                            }
                        } else {
                            throw Cell.Error.VALUE.getThrowableObject();
                        }
                    }
                } else{
                    throw Cell.Error.VALUE.getThrowableObject();
                }

                arraysToStack.add(temp);
            }
        }

        StackedZArray indicesArray = new StackedZArray(arraysToStack, stackType == null ? StackedZArray.Type.H_STACK : stackType);
        BitSet indices = new BitSet();
        Stack stack = new Stack();
        ((ZArrayI) indicesArray).addArrayValues(stack);
        List<Integer> indicesToOrder = new ArrayList<>();

        //Update the bitset and add chosen indices into indicesToOrder
        for (Object tempValue : stack){
            if(tempValue instanceof Value){
                tempValue = ((Value) tempValue).getValue();
            }
            int index = FunctionUtil.objectToNumber(tempValue).intValue() - 1;
            if(index >= 0 && index < (this.functionID == ROW ?  array.getRowSize() : array.getColSize())) {
                indices.set(index);
                indicesToOrder.add(index);
            }
            else{
                throw Cell.Error.VALUE.getThrowableObject();
            }
        }
        if(indices.cardinality() == 0){
            throw Cell.Error.VALUE.getThrowableObject();
        }

        BitSet other = new BitSet();
        other.set(0, (this.functionID == ROW ?  array.getColSize() : array.getRowSize()), true);
        FilteredZArray filteredZArray = new FilteredZArray(array, this.functionID == ROW ? indices : other, this.functionID == COL ? indices : other);

        if(isOrder(indicesToOrder)){
            return filteredZArray;
        }else{
            return new OrderedZArray(filteredZArray, getIndicesForOrder(indices, indicesToOrder), this.functionID);
        }
    }

    private boolean isOrder(List<Integer> indicesToOrder) {
        int temp = -1;
        for(Integer index : indicesToOrder){
            if(index <= temp){
                return false;
            }
            temp = index;
        }
        return true;
    }

    private int[] getIndicesForOrder(BitSet indices, List<Integer> indicesToOrder) {
        int[] result = new int[indicesToOrder.size()];
        for(int i = 0; i < indicesToOrder.size(); i++){
            result[i] = indices.get(0, indicesToOrder.get(i)).cardinality();
        }
        return result;
    }

    public boolean isExcelFutureFunction() {
        return true;
    }
}
