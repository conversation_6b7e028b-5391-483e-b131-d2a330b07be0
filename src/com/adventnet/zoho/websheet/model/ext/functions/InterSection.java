//$Id$


package com.adventnet.zoho.websheet.model.ext.functions;



import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.parser.ASTOpNode;

import java.util.logging.Level;
import java.util.logging.Logger;



import com.singularsys.jep.Evaluator;

import com.singularsys.jep.EvaluationException;

import com.singularsys.jep.functions.CallbackEvaluationI;

import com.singularsys.jep.functions.PostfixMathCommand;

import com.singularsys.jep.parser.Node;


public class InterSection extends PostfixMathCommand implements CallbackEvaluationI {

	public static Logger logger = Logger.getLogger(InterSection.class.getName());


	/**

	 *

	 */

	public InterSection() {

		super();

		numberOfParameters = 2;

	}




    @Override
	public boolean checkNumberOfParameters(int n) {

		return (n == 2);

	}



	/**

	 *
     * @throws com.singularsys.jep.EvaluationException
	 */

        @Override
	public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException

	{
	    logger.info("Inside Intersection call ::: ");

		int num = node.jjtGetNumChildren();

		if( !checkNumberOfParameters(num))
		{
			throw Cell.Error.NA.getThrowableObject();
//			throw Cell.Error.getError("InterSection operator must have 2 arguments.").getThrowableObject(); //No I18N
		}


		// get value of argument



		for(int i=0; i<num; i++)
		{
		    Node node0 = node.jjtGetChild(i);
		    logger.log(Level.INFO, "Node here InterSection ::::::  {0}", node0);
		    if(node0 instanceof ASTOpNode)
		    {
			//Object val = pv.eval(node0, data);
                        NonScalarObjectIterator stack = new NonScalarObjectIterator(((ZSEvaluator)pv).evaluate(node0, (Cell)data, false, false));

                        if(stack.size() != 1)
                        {
                            throw Cell.Error.VALUE.getThrowableObject();
                        }

                        Object val = stack.next();
			logger.log(Level.INFO, "VAlue ::: {0}", val);
		    }
		}

		//Object condVal = pv.eval(node0, data);


		return null;

	}


}

