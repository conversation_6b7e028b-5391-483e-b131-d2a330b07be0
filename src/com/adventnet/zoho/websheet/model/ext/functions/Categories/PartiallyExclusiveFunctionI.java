//$Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.ext.functions.Categories;

/**
 *
 * <AUTHOR>
 * 
 * NonExclusive function which has exclusive arguments.
 * Function return type will dependent upon nonExclusive arguments.
 * 
 */
public interface PartiallyExclusiveFunctionI {
     /**
     * every argument of the function may not be exclusive or nonExclusive.
     * example - SUMIF( range, criteria, [sum_range] )
     *           here, only [sum_range] is nonExclusive and return type should depend on it. 
     * @param index
     * @param numberOfArguments
     * @return 
     */
    public boolean isNonExclusiveArgument(int index, int numberOfArguments);
}
