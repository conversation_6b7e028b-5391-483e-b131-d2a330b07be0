//$Id$
/*
 * Mode.java
 *
 * Created on August 3, 2009, 11:47 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ZArray;
import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.NonExclusiveFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.StatisticalFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.ASTConstant;
import com.singularsys.jep.parser.Node;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class MedianOrMode extends PostfixMathCommand implements CallbackEvaluationI, StatisticalFunctionI, NonExclusiveFunctionI
{      
//    public static final int MEDIAN = 1;
//    public static final int MODE = 2;
//
//    private int id;
//
//    /** Creates a new instance of Mode */
//    public MedianOrMode(int id)
//    {
//        this.id = id;
//        numberOfParameters = -1;
//    }
//
    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {    
        int size = node.jjtGetNumChildren();
        
        List<Double> nums = new ArrayList<>();
        for(int i = 0; i < size; i++)
        {
            Node child = node.jjtGetChild(i);
                        
//            if(child instanceof ASTEmptyNode)
//            {
//                throw Cell.Error.VALUE.getThrowableObject();
//            }
            
            NonScalarObjectIterator stack = new NonScalarObjectIterator(((ZSEvaluator)pv).evaluate(child, (Cell)data, false, false));
            while(stack.hasNext())
            {
                Object o = stack.next();
                
                if(o instanceof Value)
                {
                    o = ((Value)o).getValue();
                }
                
                if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Cell.Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}
                
                if(o == null || o instanceof Boolean)
                {
                    continue;
                }
                if(o instanceof ASTEmptyNode)
                {
                    o = 0;
                }
                if(o instanceof String)
                {
                    if(child instanceof ASTConstant)
                    {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }
                    else
                    {
                        continue;
                    }
                }
                
                nums.add(FunctionUtil.objectToNumber(o).doubleValue());
            }
        }     
        
        /////////////////////// CHNAGE VALUE HERE
        Object result = getResult(nums);
        if(result instanceof List) {
            nums = (List<Double>) result;
            return new ZArray(nums, nums.size(), 1);
        }
        return Value.getInstance(Cell.Type.FLOAT, result);
    }
    public abstract Object getResult(List<Double> nums) throws EvaluationException;
//    public double getResult(List<Double> nums) throws EvaluationException
//    {
//        if(id == MEDIAN)
//        {
//            if(nums == null || nums.isEmpty())
//            {
//                throw Cell.Error.NUM.getThrowableObject();
//            }
//
//            Collections.sort(nums);
//            int size = nums.size();
//            if(size % 2 == 0)
//            {
//                return (nums.get(size/2 - 1).doubleValue()+nums.get(size/2).doubleValue())/2.0;
//            }
//            else
//            {
//                return nums.get(size/2).doubleValue();
//            }
//        }
//        else if(id == MODE)
//        {
//            if(nums == null || nums.isEmpty())
//            {
//                //throw Cell.Error.NA.getThrowableObject();
//                throw Cell.Error.VALUE.getThrowableObject(); // OO behavior
//            }
//
//            int size = nums.size();
//            Number mode = null;
//            int repeated = 0;
//            while(!nums.isEmpty())
//            {
//                List temp = new ArrayList();
//                Number res = nums.get(size - 1);
//                temp.add(res);
//                nums.removeAll(temp);
//
//                int count = size - nums.size();
//
//                if(count > 1 && count > repeated)
//                {
//                    mode = res;
//                    repeated = count;
//                }
//
//                size = nums.size();
//            }
//
//            if(mode != null)
//            {
//                return mode.doubleValue();
//            }
//        }
//
//        throw Cell.Error.VALUE.getThrowableObject();// OO behavior
//    }


    public List<Double> getMode(List<Double> nums) throws EvaluationException
    {
        if(nums == null || nums.isEmpty())
        {
            throw Cell.Error.NA.getThrowableObject();// Excel behavior
            //throw Cell.Error.VALUE.getThrowableObject(); // OO behavior
        }
        int size = nums.size();
        List<Double> modes = new ArrayList<>();
        int repeated = 0;
        while(!nums.isEmpty())
        {
            List temp = new ArrayList();
            //Number res = nums.get(size - 1);
            Double res = nums.get(0);
            temp.add(res);
            nums.removeAll(temp);

            int count = size - nums.size();

            if(count == repeated) {
                modes.add(res);
            }
            if(count > 1 && count > repeated)
            {
                modes = new ArrayList<>();
                modes.add(res);
                repeated = count;
            }

            size = nums.size();
        }
        if(!modes.isEmpty())
        {
            return modes;
        }
        throw Cell.Error.NA.getThrowableObject();// Excel behavior
        //throw Cell.Error.VALUE.getThrowableObject();// OO behavior
    }

    public double getMedian(List<Double> nums) throws EvaluationException
    {
        if(nums == null || nums.isEmpty())
        {
            throw Cell.Error.NUM.getThrowableObject();
        }

        Collections.sort(nums);
        int size = nums.size();
        if(size % 2 == 0)
        {
            return (nums.get(size/2 - 1)+nums.get(size/2))/2.0;
        }
        else
        {
            return nums.get(size/2);
        }
    }

}
