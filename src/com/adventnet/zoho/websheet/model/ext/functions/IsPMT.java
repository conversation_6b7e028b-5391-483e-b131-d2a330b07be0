//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.singularsys.jep.parser.Node;

public class IsPMT extends PostfixMathCommand implements ScalarFunctionI, FinancialFunctionI, PartiallyExclusiveFunctionI, CallbackEvaluationI
{

    public IsPMT()
    {
        numberOfParameters = 4;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();


        if (!checkNumberOfParameters(numChildren))
        {
            throw Cell.Error.VALUE.getThrowableObject();
        }

        Number[] vals = new Number[numChildren];

        for (int i = 0; i < numChildren; i++)
        {
            Node child = node.jjtGetChild(i);
            Object param = ((ZSEvaluator) pv).evaluate(child, (Cell) data, true, false);

            if (param instanceof Value)
            {
                param = ((Value) param).getValue();
            }

            if (param == null || param instanceof ASTEmptyNode)
            {
                if (i == 2)
                {
                    throw Cell.Error.VALUE.getThrowableObject();
                }
                param = 0;

            }

            if(param instanceof EvaluationException) {
    throw (EvaluationException)param;
}
if(param instanceof Throwable) {
    throw Cell.Error.getError(((Throwable) param).getMessage()).getThrowableObject();
}

            if (param instanceof String)
            {
                param = Value.getInstance((String)param, ((Cell)data).getSpreadsheetSettings()).getValue();

                if (param == null || param instanceof String || param instanceof Throwable)
                {
                    throw Cell.Error.VALUE.getThrowableObject();
                }
            }


            vals[i] = FunctionUtil.objectToNumber(param);
        }
        double result = IsPMT(vals[0].doubleValue(), vals[1].doubleValue(), vals[2].doubleValue(), vals[3].doubleValue());

        Value value = Value.getInstance(Cell.Type.FLOAT, result);

        return value;

    }

    @Override
    public boolean checkNumberOfParameters(int n)
    {
        return (n == 4);
    }

    public double IsPMT(double rate, double period, double numPeriod, double principle) throws EvaluationException
    {
        double num = (principle - (period * (principle / numPeriod)));
        return (-(rate * num));
    }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        return (index == 3);
    }
}
