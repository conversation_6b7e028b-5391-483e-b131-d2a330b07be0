//$Id$
/*
 * CoupPCD.java
 *
 * Created on April 22, 2008, 11:17 AM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.singularsys.jep.parser.Node;
import java.util.Calendar;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class CoupPCD extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, FinancialFunctionI
{
	public static Logger logger = Logger.getLogger(CoupPCD.class.getName());
    /** Creates a new instance of CoupPCD */
    public CoupPCD()
    {
        numberOfParameters = -1;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        if(!(num ==3 || num == 4))
        {
            throw Error.NAME.getThrowableObject();
        }

        Object[] param = new Object[num];

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
	for(int i = 0; i < num; i++)
	{
	    Node child = node.jjtGetChild(i);

            Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            if((i != 4 && o == null) || o instanceof ASTEmptyNode)
            {
                throw Error.NUM.getThrowableObject();
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            // Should change any string representing a number or date etc to corresponding object
            // only when it is a constant node
            // ie. when a cell containing a number as string is referred no need to change it.
            // but should be converted when given directly as arguments of the function years("1/1/2007",...)
            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof String || o instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

	    //valueStack.push(o);
            param[i] = o;
	}

        // initializing the arguments.
        java.util.Date settlement = FunctionUtil.objectToDate(FunctionUtil.objectToNumber(param[0]).intValue());
        java.util.Date maturity = FunctionUtil.objectToDate(FunctionUtil.objectToNumber(param[1]).intValue());
        int frequency = FunctionUtil.objectToNumber(param[2]).intValue();
        int basis = 0; // default value.

        // set Basis
        if(num == 4 && param[3] != null)
        {
            basis = FunctionUtil.objectToNumber(param[3]).intValue();
        }

        try {
    return Value.getInstance(Type.FLOAT, coupPCD(settlement, maturity, frequency, basis));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    /*
     * returns the last coupon date before the settlement date.
     *
     *
     */
     public static long coupPCD(java.util.Date settlement, java.util.Date maturity, int frequency, int basis)
    {
        if(maturity.before(settlement)
               || basis < 0 || basis > 4 ||
               !(frequency == 1 || frequency ==2 || frequency == 4))
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        //TimeZone tz = TimeZone.getTimeZone("GMT");

        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(settlement);

        // set time to 0.
        cal1.set(Calendar.HOUR_OF_DAY, 0);
        cal1.set(Calendar.MINUTE, 0);
        cal1.set(Calendar.SECOND, 0);
        //logger.info("ca1 : "+cal1.getTime());
        //logger.info(cal1.getTime());
        // construct a calendar object for coupon date before settlement date, Using maturity date.
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(maturity);

        // set time to 0.
        cal2.set(Calendar.HOUR_OF_DAY, 0);
        cal2.set(Calendar.MINUTE, 0);
        cal2.set(Calendar.SECOND, 0);

        // stores whether the calendars date is lat day of the month or not.
        boolean isLastDayOfMonth = cal2.getActualMaximum(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DATE);
        int maturity_date = cal2.get(Calendar.DATE);
        int monthsPerCoupon = 12 / frequency;

        // constructing last coupon date before settlementdate.
        while(cal2.after(cal1))
        {
            cal2.add(Calendar.MONTH, -monthsPerCoupon);
            if(isLastDayOfMonth)
            {
                cal2.set(Calendar.DATE, cal2.getActualMaximum(Calendar.DAY_OF_MONTH));
            }
            else if(maturity_date < cal2.getActualMaximum(Calendar.DAY_OF_MONTH))
            {
                cal2.set(Calendar.DATE, maturity_date);
            }
        }

        // This should be done inside the above while block as
        // resetting the date to laste day of month may satisfy the condition of WHILE again.
        // if the matirity date is last day of month other than 31 (eg 30)
        // the resulting coupPCD will be of the same date instead of becoming the last day of that month
        // so make it last day of month.
//        if(isLastDayOfMonth)
//        {
//            cal2.set(cal2.DATE, cal2.getActualMaximum(cal2.DAY_OF_MONTH));
//        }
        //logger.info("cal2 : "+cal2.getTime());
        // set timezone as GMT.
        // cal2.setTimeZone(TimeZone.getTimeZone("GMT"));

        return DateUtil.convertDateToNumber(cal2.getTime()).longValue();
    }

//    public static void main(String args[])
//    {
//        Calendar c1 = new GregorianCalendar(2008, 0, 1);
//        Calendar c2 = new GregorianCalendar(2045, 5, 30);
////        long l = coupPCD(c1.getTime(), c2.getTime(), 2, 0) * DateUtil.MILLI_SECS_PER_DAY;
////        java.util.Date date = new java.util.Date(l);
////        logger.info(date);
//
//        long l = coupPCD(c1.getTime(), c2.getTime(), 2, 0);
//
//        logger.info(l);
//        logger.info(DateUtil.converNumberToDate(l));
////        TimeZone tz = TimeZone.getTimeZone("GMT");
////        Calendar c = Calendar.getInstance(tz);
////        c.setTime(DateUtil.converNumberToDate(l));
////        logger.info(c.getTime());
//    }
}
