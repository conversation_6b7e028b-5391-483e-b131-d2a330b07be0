package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;

public class BitAndOrXor extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI{
    public static final int BITAND = 1;
    public static final int BITOR = 2;
    public static final int BITXOR = 3;
    private final int funID;
    public BitAndOrXor(int id){
        this.funID = id;
        this.numberOfParameters = -1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int num = node.jjtGetNumChildren();

        if(num != 2){
            throw Cell.Error.NAME.getThrowableObject();
        }

        Object param1 = null;
        Object param2 = null;
        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);
            Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            if(o instanceof ASTEmptyNode) {
                return Value.getInstance(Cell.Type.FLOAT, 0);
            }

            if(o instanceof Value){
                o = ((Value) o).getValue();
            }

            if(o instanceof EvaluationException) {
                throw (EvaluationException)o;
            }
            if(o instanceof Throwable) {
                throw Cell.Error.getError(((Throwable)o).getMessage()).getThrowableObject();
            }

            try {
                o = o instanceof String ? Long.parseLong((String) o) : FunctionUtil.objectToNumber(o);
            } catch (Exception e) {
                throw Cell.Error.VALUE.getThrowableObject();
            }


            if(i == 0){
                param1 = o;
            } else {
                param2 = o;
            }
        }

        String paramBin1 = null;
        String paramBin2 = null;
        try {
            paramBin1 = Dec2Bin.dec2Bin(((Number)param1).longValue(), null);
            paramBin2 = Dec2Bin.dec2Bin(((Number)param2).longValue(), null);
        } catch (IllegalArgumentException e) {
            throw Cell.Error.VALUE.getThrowableObject();
        }
        int pLength1 = paramBin1.length();
        int pLength2 = paramBin2.length();
        int len = Math.min(pLength1, pLength2);

        int result = 0;
        for(int i = 0; i < len; i++) {
            boolean b1 = paramBin1.charAt(pLength1 - i - 1) == '1';
            boolean b2 = paramBin2.charAt(pLength2 - i - 1) == '1';
            if(this.funID == BitAndOrXor.BITAND && (b1 && b2)) {
                result += Math.pow(2, i);
            }
            if(this.funID == BitAndOrXor.BITOR && (b1 || b2)) {
                result += Math.pow(2, i);
            }
            if(this.funID == BitAndOrXor.BITXOR && (b1 != b2)) {
                result += Math.pow(2, i);
            }
        }

        // to handle extra length of chars. Ex: 3 - 11 and 5 - 101. This code will add result for first remaining char in 5(1)
        if(this.funID != BitAndOrXor.BITAND && pLength1 != pLength2) {
            for(int i = len; i < Math.max(pLength1, pLength2); i++) {
                char char1 = pLength1 > pLength2 ? paramBin1.charAt(pLength1 - i - 1) : paramBin2.charAt(pLength2 - i - 1);

                if (char1 == '1') {
                    result += Math.pow(2, i);
                }
            }
        }

        return Value.getInstance(Cell.Type.FLOAT, result);
    }

}
