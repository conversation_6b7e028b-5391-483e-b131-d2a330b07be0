//$Id$
/*
 * Exact.java
 *
 * Created on July 3, 2008, 10:01 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.TextFunctionI;
import com.singularsys.jep.parser.Node;

import java.text.DecimalFormat;

/**
 *
 * <AUTHOR>
 */
public class Exact extends PostfixMathCommand implements ScalarFunctionI, TextFunctionI, CallbackEvaluationI
{
    
    /** Creates a new instance of Exact */
    public Exact()
    {
        numberOfParameters = 2;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if (!checkNumberOfParameters(numChildren)) {
            throw Cell.Error.VALUE.getThrowableObject();
        }

        Node child = node.jjtGetChild(0);
        Object param1 = ((ZSEvaluator) pv).evaluate(child, (Cell) data, true, false);

        child = node.jjtGetChild(1);
        Object param2 = ((ZSEvaluator) pv).evaluate(child, (Cell) data, true, false);

        if(param1 instanceof Value)
        {
            param1 = ((Value)param1).getValue();
        }

        if(param2 instanceof Value)
        {
            param2 = ((Value)param2).getValue();
        }
        // Evan when the arguments are Error cells should compare them.
//        if(param1 instanceof Throwable)
//        {
//            inStack.push(param1);
//        }
//
//        if(param2 instanceof Throwable)
//        {
//            inStack.push(param2);
//        }
        if(param1 instanceof EvaluationException) {
    throw (EvaluationException)param1;
}
if(param1 instanceof Throwable) {
    throw Error.getError(((Throwable)param1).getMessage()).getThrowableObject();
}
        if(param2 instanceof EvaluationException) {
    throw (EvaluationException)param2;
}
if(param2 instanceof Throwable) {
    throw Error.getError(((Throwable)param2).getMessage()).getThrowableObject();
}

       return Value.getInstance(Type.BOOLEAN, exact(param1, param2));
    }

    public static boolean exact(Object ob1, Object ob2) throws EvaluationException
    {
        if(ob1 != null && !(ob1 instanceof String))
        {
            ob1 = FunctionUtil.objectToNumber(ob1);
	    DecimalFormat df = new DecimalFormat();
	    df.setMinimumFractionDigits(0);
	    df.setMaximumFractionDigits(10);
	    ob1 = df.format(ob1);
        }  
        
        if(ob2 != null && !(ob2 instanceof String))
        {
	    ob2 = FunctionUtil.objectToNumber(ob2);
	    DecimalFormat df = new DecimalFormat();
	    df.setMinimumFractionDigits(0);
	    df.setMaximumFractionDigits(10);
	    ob2 = df.format(ob2);
        }
        
        if(ob1 == null && ob2 == null)
        {
            return true;
        }
        else if(ob1 != null)
        {
            return ob1.equals(ob2);
        }
        else
        {
            return false;
        }
    }
    
//    public static void main(String args[])
//    {
//        //System.out.println(exact(null, null));
//    }
    
}
