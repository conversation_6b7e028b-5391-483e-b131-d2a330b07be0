//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.singularsys.jep.EvaluationException;

public class ExponDist extends Distribution implements PartiallyExclusiveFunctionI
{
    public ExponDist()
    {
        numberOfParameters = 3;
    }

    @Override
    public boolean checkNumberOfParameters(int n)
    {
        return (n == 3);
    }

    @Override
    public double getResult(Number[] values ) throws EvaluationException
    {
        return Distribution.getExponDist(values[0].doubleValue(), values[1].doubleValue(), values[2].doubleValue()!=0); //third argument shud be made boolean
        //return Distribution.getExponDist(values[0].doubleValue(), values[1].doubleValue(), values[2].intValue()!=0);
       
    }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        return index == 0;
    }
}
