//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.StatisticalFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.Average;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Vector;

/**
 *
 * <AUTHOR>
 */
public class Estimation extends PostfixMathCommand implements CallbackEvaluationI,PartialScalarFunctionI, StatisticalFunctionI, PartiallyExclusiveFunctionI
{
    public static final int LINEST = 0;
    public static final int LOGEST = 1;
    protected int id = 0;
    public Estimation(int id)
    {
        this.id = id;
        numberOfParameters = -1;
    }

    @Override
    public boolean isScalarArgument(int index)
    {
        return ((index == 2 || index == 3));
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();
        if(num < 1 || num > 4)
        {
            throw Cell.Error.NAME.getThrowableObject();
        }
        Sheet sheet = ((Cell) data).getRow().getSheet();
        int allow_const = 1;
        int stats = 0;
        ZArrayI yValuesI = null;
        ZArrayI xValuesI = null;
        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);
            if(child instanceof ASTEmptyNode)
            {
                continue;
            }
            if(i < 2)   // for y and x values Range
            {
                Object result = ((ZSEvaluator)pv).evaluate(child, (Cell)data, isScalarArgument(i), true);

                ZArrayI arrayOrRange = null;
                if(result instanceof ZArrayI) {
                    arrayOrRange = (ZArrayI)result;
                }
                else
                {
                    Object o = result;
                    if(o instanceof Value)
                    {
                        o = ((Value)o).getValue();
                    }
                    List cons_List = new ArrayList();
                    cons_List.add(o);
                    arrayOrRange = new ZArray(cons_List,1,1);
                }
                if(i == 0)
                {
                    yValuesI = arrayOrRange;
                }
                else if(i == 1)
                {
                    xValuesI = arrayOrRange;
                }

            }
            else
            {
                Object param = ((ZSEvaluator)pv).evaluate(child, (Cell)data, isScalarArgument(i), false);
                int temp = 0;
                if(param instanceof Value)
                {
                    param = ((Value)param).getValue();
                }
                if(param == null)
                {
                    param = 0;
                }
                if(!(param instanceof Throwable || param instanceof String))
                {
                    temp = FunctionUtil.objectToNumber(param).intValue();
                }
                else
                {
                    throw Cell.Error.VALUE.getThrowableObject();
                }
                if(i == 2)
                {
                    allow_const = temp;
                }
                else
                {
                    stats = temp;
                }
            }
        }
        if(id == LINEST)
        {
            return linest(yValuesI, xValuesI, allow_const, stats, ((Cell)data).getSpreadsheetSettings());
        }
        else if(id == LOGEST)
        {
            return logest(yValuesI, xValuesI, allow_const, stats, ((Cell)data).getSpreadsheetSettings());
        }
        else
        {
            throw Cell.Error.getError("ILLEGAL ID IN ESTIMATION......").getThrowableObject();//No I18N
        }
    }
    public static ZArray linest(ZArrayI yValuesI, SpreadsheetSettings spreadsheetSettings) throws EvaluationException
    {
        return linest(yValuesI, null, spreadsheetSettings);
    }
    public static ZArray linest(ZArrayI yValuesI, ZArrayI xValuesI, SpreadsheetSettings spreadsheetSettings) throws EvaluationException
    {
        return linest(yValuesI, xValuesI, 1, spreadsheetSettings);
    }
    public static ZArray linest(ZArrayI yValuesI, ZArrayI xValuesI, int allow_const, SpreadsheetSettings spreadsheetSettings) throws EvaluationException
    {
        return linest(yValuesI, xValuesI, allow_const, 0, spreadsheetSettings);
    }

    public static ZArray logest(ZArrayI yValuesI, SpreadsheetSettings spreadsheetSettings) throws EvaluationException
    {
        return logest(yValuesI, null, spreadsheetSettings);
    }
    public static ZArray logest(ZArrayI yValuesI, ZArrayI xValuesI, SpreadsheetSettings spreadsheetSettings) throws EvaluationException
    {
            return logest(yValuesI, xValuesI, 1, spreadsheetSettings);
    }
        public static ZArray logest(ZArrayI yValuesI, ZArrayI xValuesI, int allow_const, SpreadsheetSettings spreadsheetSettings) throws EvaluationException
    {
            return logest(yValuesI, xValuesI, allow_const, 0, spreadsheetSettings);
    }
    public static ZArray linest(ZArrayI yValuesI, ZArrayI xValuesI, int allow_const, int stats, SpreadsheetSettings spreadsheetSettings)throws EvaluationException
    {
        return linestLogest(yValuesI, xValuesI, allow_const,stats, Estimation.LINEST, spreadsheetSettings);
    }
    public static ZArray logest(ZArrayI yValuesI, ZArrayI xValuesI, int allow_const, int stats, SpreadsheetSettings spreadsheetSettings)throws EvaluationException
    {
        return linestLogest(yValuesI, xValuesI, allow_const, stats, Estimation.LOGEST, spreadsheetSettings);
    }

    private static ZArray linestLogest(ZArrayI yValuesI, ZArrayI xValuesI, int allow_const, int stats,int id, SpreadsheetSettings spreadsheetSettings) throws EvaluationException
    {
        if(yValuesI == null)
        {
            throw Cell.Error.VALUE.getThrowableObject();
        }
        ZArray yValues = validate(yValuesI);
        List<Double> yValueList = yValues.getArray();
        int yRowSize = yValues.getRowSize();
        int yColSize = yValues.getColSize();        
        if(id == LOGEST)
        {
            for(int i = 0; i < yValueList.size(); i++)
            {
                double yValue = yValueList.get(i);
                if(yValue <= 0)
                {
                    throw Cell.Error.NUM.getThrowableObject();
                }
                yValueList.set(i, Math.log(yValue));
            }
        }
        ZArray xValues;
        if(xValuesI == null)
        {
            ArrayList<Double> tempList = new ArrayList();
            for(int i = 1; i <= yValueList.size(); i++)
            {
                tempList.add(new Double(i));
            }
            xValues = new ZArray(tempList, yRowSize, yColSize);
        }
        else
        {
            xValues = validate(xValuesI);
        }
        int x_RowSize = xValues.getRowSize();
        int x_ColSize = xValues.getColSize();
        if(!(yRowSize == 1 || yColSize == 1))
        {
            List xValueList = xValues.getArray();
            if(yValueList.size() != xValueList.size())
            {
                throw Cell.Error.REF.getThrowableObject();
            }
            else
            {
                if(yColSize >= yRowSize)
                {
                    yValues = new ZArray(yValueList, 1, yValueList.size());
                }
                else
                {
                    yValues = new ZArray(yValueList, yValueList.size(), 1);
                }
                if(x_ColSize >= x_RowSize)
                {
                    xValues = new ZArray(xValueList, 1, xValueList.size());
                }
                else
                {
                    xValues = new ZArray(xValueList, xValueList.size(), 1);
                }
            }
        }
        if((yColSize >= yRowSize && yColSize != x_ColSize) || (yColSize < yRowSize && yRowSize != x_RowSize))
        {
            throw Cell.Error.REF.getThrowableObject();
        }
        if(yValues.getRowSize() == 1)
        {
            yValues = new ZArray(yValueList, yValues.getColSize(), yValues.getRowSize());
        }
        if(xValues.getColSize() >= xValues.getRowSize())
        {
            xValues = Transpose.transpose(xValues);
        }
        List xValueList = xValues.getArray();
        x_RowSize = xValues.getRowSize();
        x_ColSize = xValues.getColSize();
        ZArray newXValues;
        int noOfGroup;
        if(allow_const == 0)
        {
            noOfGroup = x_ColSize - 1;
            newXValues = xValues;
        }
        else
        {
            noOfGroup = x_ColSize;
            for(int i = 0; i < x_RowSize; i++)
            {
                xValueList.add(i*x_ColSize+i, new Double(1));
            }
            newXValues = new ZArray(xValueList, x_RowSize, x_ColSize+1);
        }        
        ZArray slopeArray;
        ZArray xT_XInverse;
        if(allow_const == 1 && yValues.getRowSize() == 1 && yValues.getColSize() == 1)
        {
            List slopeList = new ArrayList();
            slopeList.add(new Double(yValueList.get(0)));
            slopeList.add(new Double(0));
            slopeArray = new ZArray(slopeList, 2 ,1);
            List xT_XInverseList = new ArrayList();
            for(int i=0; i<4; i++)
            {
                xT_XInverseList.add(new Double(1));
            }
            xT_XInverse = new ZArray(xT_XInverseList, 2, 2);
        }
        else
        {
            ZArray xTranspose = Transpose.transpose(newXValues);
            ZArray xT_x = MMult.mMult(xTranspose, newXValues);
            xT_XInverse = MInverse.mInverse(xT_x, spreadsheetSettings);
            ZArray xT_y = MMult.mMult(xTranspose, yValues);
            slopeArray = MMult.mMult(xT_XInverse, xT_y);
        }                       
        List resultList = new ArrayList<>();
        List slopeList = slopeArray.getArray();
        resultList.addAll(slopeList);
        Collections.reverse(resultList);
        if(allow_const == 0)
        {
            resultList.add(new Double(0));
        }
        if(id == LOGEST)
        {
            for(int i = 0; i < resultList.size(); i++)
            {
                double slope = Math.exp(FunctionUtil.objectToNumber(resultList.get(i)).doubleValue());
                resultList.set(i, slope);
            }
        }
//        for(int i=0;i<resultList.size();i++)
//        {
//            System.out.println("result               "+resultList.get(i));
//        }
        if(stats == 0)
        {
            return new ZArray(resultList, 1, resultList.size());
        }
        ZArray y_EstimateArray = MMult.mMult(newXValues, slopeArray);
        List<Double> y_EstimateList = (y_EstimateArray).getArray();
        double sumOfRes = 0;
        double sumOfTot = 0;
        List<Double> y_ValueList = yValues.getArray();
        Vector y_Vector = new Vector(y_ValueList);
        double yAvg = Average.average(y_Vector, spreadsheetSettings );
        for(int i = 0; i < x_RowSize; i++)
        {
            double yValue = y_ValueList.get(i);
            double yEstimate = y_EstimateList.get(i);
            sumOfRes = sumOfRes + (yValue - yEstimate) * (yValue - yEstimate);
            if(allow_const == 0)
            {
                sumOfTot = sumOfTot + yValue * yValue;
            }
            else
            {
                sumOfTot = sumOfTot + (yValue - yAvg) * (yValue - yAvg);
            }
        }
        double sumOfReg = sumOfTot - sumOfRes;
        double rSquare;
        //Todo:for case sumOfReg=0 and sumOfTot =0;
        if(sumOfReg == sumOfTot)
        {
            rSquare = 1;
        }
        else
        {
            rSquare = sumOfReg / sumOfTot;
        }
        int degOfFreedom = x_RowSize - noOfGroup - 1;
        double deviation;
        if(degOfFreedom < 0)
        {
            degOfFreedom = 0;
        }
        if(degOfFreedom == 0)
        {
            deviation = 0;
        }
        else
        {
            deviation = sumOfRes / degOfFreedom;
        }
        ArrayList<Double> coVarianceList = new ArrayList<>();        
       
        //List<Double> xT_XInverseList = xT_XInverse.getArray();
        for(int i = 0; i < xT_XInverse.getRowSize(); i++)
        {
            for(int j = 0; j < xT_XInverse.getColSize(); j++)
            {
                if(i == j)
                {
                    coVarianceList.add(Math.sqrt(deviation * (Double)xT_XInverse.getValue(i , j).getValue()));
                }
            }
        }
        
        Collections.reverse(coVarianceList);
        resultList.addAll(coVarianceList);
        if(allow_const == 0)
        {
            resultList.add(Cell.Error.NA.getThrowableObject());
        }
        resultList.add(rSquare);
        resultList.add(Math.sqrt(deviation));
        double msr = sumOfReg / x_ColSize;
        if(deviation == 0)
        {
            resultList.add(Cell.Error.NUM.getThrowableObject());
        }
        else
        {
            resultList.add(msr/deviation);
        }
        resultList.add(degOfFreedom);
        resultList.add(sumOfReg);
        resultList.add(sumOfRes);
        for(int i = 2; i < 5; i++)
        {
            for(int j = 2; j < x_ColSize+1; j++)
            {
                resultList.add((i*(x_ColSize+1))+j, Cell.Error.NA.getThrowableObject());
            }
        }
//        for(int i=0;i<resultList.size();i++)
//        {
//            System.out.println("result               "+resultList.get(i));
//        }
        return new ZArray(resultList, 5, x_ColSize+1);
    }
    private static ZArray validate(ZArrayI values) throws EvaluationException
    {
        List<Double> valueList = new ArrayList<>();
        for(int i = 0; i < values.getRowSize(); i++)
        {
            for(int j = 0; j < values.getColSize(); j++)
            {
                Object param = values.getValue(i, j);
                if(param== null || param instanceof String || param instanceof Throwable || param instanceof Boolean)//Excel Behaviour
                {
                    throw Cell.Error.VALUE.getThrowableObject();
                }
                valueList.add(FunctionUtil.objectToNumber(param).doubleValue());
            }
        }
        return new ZArray(valueList, values.getRowSize(), values.getColSize());
    }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        return (index == 0 || index == 1);
    }
}
