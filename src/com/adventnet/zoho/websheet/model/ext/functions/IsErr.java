//$Id$
/*
 * IsErr.java
 *
 * Created on July 14, 2008, 3:33 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.InformationFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class IsErr extends PostfixMathCommand implements ScalarFunctionI, InformationFunctionI, CallbackEvaluationI
{
	public static Logger logger = Logger.getLogger(IsErr.class.getName());

    /** Creates a new instance of IsErr */
    public IsErr()
    {
        numberOfParameters = 1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if(numChildren > 1)
        {
            //no need for this check, so don't need to return this
            return Value.getInstance(Type.BOOLEAN, true);
        }

        Node child = node.jjtGetChild(0);
        Object param = ((ZSEvaluator) pv).evaluate(child, (Cell) data, true, false);

        if(param instanceof Value)
        {
            param = ((Value)param).getValue();
        }

        if(param instanceof Throwable && !((Throwable)param).getMessage().equals(CellUtil.getErrorString(Error.NA)))
        {
            return Value.getInstance(Type.BOOLEAN, true);
        }
        else
        {
            return Value.getInstance(Type.BOOLEAN, false);
        }
    }

}
