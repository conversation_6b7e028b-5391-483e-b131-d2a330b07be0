//$Id$
/*
 * FV.java
 *
 * Created on March 14, 2008, 3:10 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.functions.*;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.parser.Node;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class FV extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, FinancialFunctionI, PartiallyExclusiveFunctionI
{
	public static Logger logger = Logger.getLogger(FV.class.getName());
    /** Creates a new instance of FV */
    public FV()
    {
        numberOfParameters = -1;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pve) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        if(!checkNumberOfParameters(num))
        {
            throw Error.NAME.getThrowableObject();
        }

        //Stack valueStack = new Stack();
        Object param[] = new Object[num];

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);

            Object o = ((ZSEvaluator)pve).evaluate(child, (Cell)data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            // must pass values for all required fields, else error will be returned.
            if(o == null || o instanceof ASTEmptyNode)
            {
                o = 0;
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            // when a cell containing a number as string is referred no need to change it.
            // but should be converted when given directly as arguments of the function years("1/1/2007",...)
            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof String || o instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            param[i] = o;
        }

        double rate = FunctionUtil.objectToNumber(param[0]).doubleValue();
        double nPer = FunctionUtil.objectToNumber(param[1]).doubleValue();
        double pmt = FunctionUtil.objectToNumber(param[2]).doubleValue();
        double pv = 0;
        int due = 0;

        if(num > 3)
        {
            pv = FunctionUtil.objectToNumber(param[3]).doubleValue();

            if(num > 4)
            {
                due = FunctionUtil.objectToNumber(param[4]).intValue();
            }
        }

        try {
    return Value.getInstance(Type.CURRENCY, fv(rate, nPer, pmt, pv, due));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    /*
     * Checks the number of parameters of the call.
     *
     */
        @Override
    public boolean checkNumberOfParameters(int n)
    {
        return (n == 3 || n == 4 || n == 5);
    }


    /*
     * rate     : the interest rate per period.
     *
     * nPer     : the total number of payment periods.
     *
     * pmt      : the payment made in each period; pmt must be a constant.
     *            pmt = a part of principal + interest.
     *
     * pv       : the present value of the money.
     *            (Either pv or pmt is required to find fv)
     *
     * due      : the number 0 or 1 and indicates when payment is due at beginning or end of a period.
     *            If type is omitted, it is assumed to be 0.
     *
     * answer   : the future value of an annuity based on periodic , constant
     *            payments,and on a constant interest rate.
     *
     * Note     : (i) The units of rate and nper must be the same.
     *                i.e. if the payment is made per quater then the rate given mut be oer quarter.
     *
     *            (ii) pmt should be
     *                 negative, if you are paying the amount and
     *                 positive, when you receive the amount.
     *
     */
    public static double fv( double rate, double nPer, double pmt, double pv, int due ) throws IllegalArgumentException
    {

        if (rate <= 0 || (due != 1 && due != 0))
        {
            throw (new IllegalArgumentException(CellUtil.getErrorString(Error.NUM)));
        }

        //f=-p(1+r)^n - m[((1+r)^n -1)/r](1+rt)
        Power pow = new Power();
        Object tempPowerTerm = pow.power(1 + rate, nPer);
        if (!(tempPowerTerm instanceof Number))
        {
            throw (new IllegalArgumentException(CellUtil.getErrorString(Error.NUM)));
        }
        double powerTerm = ((Number) tempPowerTerm).doubleValue();
        //double powerTerm = Value.roundTo15DP((Number)tempPowerTerm);
        double fv = -(pv * powerTerm) - (pmt * ((powerTerm - 1) / rate) * (1 + (rate * due)));
        return fv;
    }
    ////////////////////// Made Changes in Logic ////////////////////////
//    public static double fv( double rate, int nPer, double pmt, double pv, int due ) throws IllegalArgumentException
//    {
//        if (rate < 0 || (due != 1 && due != 0))
//        {
//                throw (new IllegalArgumentException(CellUtil.getErrorString(Error.NUM)));
//        }
//
//        int tempNPER = Math.abs(nPer);
//
//        double fv = 0;
//        int start = 0;
//        int end = 0;
//
//        switch ( due )
//        {
//                case 1 :
//                        start = 1;
//                        end = tempNPER;
//                        break;
//                case 0 :
//                        start = 0;
//                        end = tempNPER - 1;
//                        break;
//                default :
//                        throw ( new IllegalArgumentException("There exists illegal parameter" + due ));
//        }
//
//        for ( int i = start; i <= end; i++ )
//        {
//            fv += Math.pow( ( 1 + rate ), i );
//        }
//
//        fv = fv * pmt;
//
//        if ( pv != 0 )
//        {
//                fv += pv * Math.pow((1 + rate), new Integer(tempNPER).doubleValue());
//        }
//
//        if(nPer < 0)
//        {
//            return fv;
//        }
//
//        return (0 - fv);
//    }

//    public static void main(String[] args)
//    {
//        logger.info(fv(.07, 1, 00, 10000, 0));
//    }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        if(numberOfArguments == 5) {
            return (index == 2 || index == 3);
        }
        return (index == 2);
    }
    }
