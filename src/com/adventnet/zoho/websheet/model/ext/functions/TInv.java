//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.singularsys.jep.EvaluationException;


public class TInv extends Distribution implements PartiallyExclusiveFunctionI
{
    public TInv()
    {
        numberOfParameters = 2;
    }

    @Override
    public boolean checkNumberOfParameters(int n)
    {
        return (n==2);
    }

    @Override
    public double getResult(Number[] values ) throws EvaluationException
    {
       return Distribution.getTInv(values[0].doubleValue(), values[1].doubleValue());
    }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        return (index == 0);
    }
}
