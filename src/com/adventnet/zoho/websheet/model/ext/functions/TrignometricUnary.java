//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.TrigonometryFunctionI;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;

public abstract  class TrignometricUnary extends PostfixMathCommand implements ScalarFunctionI, TrigonometryFunctionI, CallbackEvaluationI
{
    @Override
    public Object evaluate(Node node, Object data, Evaluator parserVisitor) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if(numChildren != 1)
        {
            throw Cell.Error.VALUE.getThrowableObject();
        }

        Node child = node.jjtGetChild(0);
        Object param = ((ZSEvaluator) parserVisitor).evaluate(child, (Cell) data, true, false);

        if(param instanceof Value)
        {
            param = ((Value)param).getValue();
        }

        if(param == null)
        {
            param = 0;
        }

        if(param instanceof EvaluationException) {
    throw (EvaluationException)param;
}
if(param instanceof Throwable) {
    throw Cell.Error.getError(((Throwable)param).getMessage()).getThrowableObject();
}
        if(param instanceof String)
        {
            param = Value.getInstance((String)param, ((Cell)data).getSpreadsheetSettings()).getValue();

            if(param == null || param instanceof String || param instanceof Throwable)
            {
                throw Cell.Error.VALUE.getThrowableObject();
            }
        }

        Number result = getResult(FunctionUtil.objectToNumber(param));

        if(Double.isNaN((Double)result))
        {
            // throw the VALUE error
            throw Cell.Error.NUM.getThrowableObject();
        }

        Value value = Value.getInstance(Cell.Type.FLOAT, result);

        return value;

    }

    public abstract Number getResult(Number arg) throws EvaluationException;

    public static double getCot(double param)throws EvaluationException
    {
         return 1.0 / Math.tan(param);
    }

    public static double getCoth(double param) throws EvaluationException
    {
            double num1 = Math.exp(param);
            double num2 = Math.exp(-param);
            return ((num1+num2)/(num1-num2));
    }

    public static double getACot(double param)// throws EvaluationException
    {
         return ((Math.PI/2)-Math.atan(param));
    }


    public static double getACotH(double param)// throws EvaluationException
    {
        return (Math.log((param + 1d) / (param - 1d)) * (1d / 2d));
    }

    public static double getCos(double param)// throws EvaluationException
    {
        return (Math.cos(param));
    }

    public static double getCosh(double param)// throws EvaluationException
    {
        return (Math.cosh(param));
    }

    public static double getACos(double param)
    {
        return Math.acos(param);
	}

    public static double getACosH(double param)throws EvaluationException
    {
        if(param >= 1)
        {
            return Math.log(param+Math.sqrt(param*param-1));
        }
        else
        {
            throw Cell.Error.NUM.getThrowableObject();
        }
	}

    public static double getTanH(double param)
    {
        return Math.tanh(param);
	}

    public static double getTan(double param)
    {
        return Math.tan(param);
	}

    public static double getATanH(double param)throws EvaluationException
    {
        if(param > -1.0 && param < 1)
        {
            return Math.log((1+param)/(1-param))/2;
        }
        else
        {
            throw Cell.Error.NUM.getThrowableObject();
        }
    }

    public static double getATan(double param)
    {
        return Math.atan(param);
	}

    public static double getASin(double param)
    {
        return Math.asin(param);
	}

    public static double getASinH(double param)
    {
        return Math.log(param+Math.sqrt(param*param+1));
	}

    public static double getSin(double param)
    {
        return Math.sin(param);
	}

    public static double getSinH(double param)
    {
        return Math.sinh(param);
	}

    public static double getSec(double param)
    {
        return 1.0 / Math.cos(param);
    }

    public static double getSecH(double param)
    {
        return 1.0 / Math.cosh(param);
    }

    public static double getCsc(double param)
    {
        return 1.0 / Math.sin(param);
    }

    public static double getCscH(double param)
    {
        return 1.0 / Math.sinh(param);
    }
}
