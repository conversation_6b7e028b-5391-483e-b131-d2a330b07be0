//$Id$
/*
 * Rand.java
 *
 * Created on June 5, 2008, 7:20 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.MathematicsFunctionI;
import com.singularsys.jep.parser.Node;

import java.security.SecureRandom;

/**
 *
 * <AUTHOR>
 */
public class Rand extends PostfixMathCommand implements CallbackEvaluationI, MathematicsFunctionI
{
    
    /** Creates a new instance of Rand */
    public Rand()
    {
        numberOfParameters = 0;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int numOfChildren = node.jjtGetNumChildren();
        if(numOfChildren != 0)
        {
            throw Cell.Error.NAME.getThrowableObject();
        }
        return rand();
    }


    public static double rand()
    {
        SecureRandom rand = new SecureRandom();
        
        return rand.nextDouble();
    }
    
//    public static void main(String[] args)
//    {
//        System.out.println(rand());
//    }
    
    @Override
    public boolean isDynamicFunction()
    {
        return true;
    }
    
}
