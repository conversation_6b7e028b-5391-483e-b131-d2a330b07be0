package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.SpreadsheetSettings;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.TextFunctionI;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;

public class NumberValue extends PostfixMathCommand implements ScalarFunctionI, TextFunctionI, CallbackEvaluationI {

    public NumberValue(){
        numberOfParameters = -1;
    }
    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int noOfChildren = node.jjtGetNumChildren();
        if(noOfChildren == 0 || noOfChildren > 3){
            throw Cell.Error.NAME.getThrowableObject();
        }

        String text = null;
        SpreadsheetSettings spreadsheetSettings = ((Cell) data).getSpreadsheetSettings();
        Character decimalSeparator = spreadsheetSettings.getDecimalSeparator();
        Character groupingSeparator = spreadsheetSettings.getThousandSeparator();
        for(int i = 0; i < noOfChildren; i++){
            Object object = ((ZSEvaluator) pv).evaluate(node.jjtGetChild(i), (Cell) data, true, false);

            if(object instanceof EvaluationException) {
                throw (EvaluationException)object;
            }
            if(object instanceof Throwable) {
                throw Cell.Error.getError(((Throwable)object).getMessage()).getThrowableObject();
            }
            if(object instanceof Value){
                object = ((Value) object).getValue();
            }
            String value = FunctionUtil.objectToString(object, ((Cell) data).getSpreadsheetSettings());

            if(i == 0){
                if(value == "" || value == null){//No I18N
                    return 0;
                }
                text = value;
            }else{
                Character separator = value.length() != 0 ? value.charAt(0) : null;
                if(separator == null){
                    throw Cell.Error.VALUE.getThrowableObject();
                }
                if(i == 1){
                    decimalSeparator = separator;
                }else{
                    groupingSeparator = separator;
                }
            }
        }

        if(decimalSeparator.equals(groupingSeparator)){
            throw Cell.Error.VALUE.getThrowableObject();
        }
        text = text.replace(" ", "");

        char[] splitText = text.toCharArray();
        for(int i = 0; i < splitText.length; i++){
            char c = splitText[i];
            if(decimalSeparator.equals(c)){
                splitText[i] = spreadsheetSettings.getDecimalSeparator();
            }else if(groupingSeparator.equals(c)){
                splitText[i] = spreadsheetSettings.getThousandSeparator();
            }
        }
        text = new String(splitText);

//            if (!groupingSeparator.equals(spreadsheetSettings.getThousandSeparator())) {
//                text = text.replace(groupingSeparator.toString(), String.valueOf(spreadsheetSettings.getThousandSeparator()));
//            }
//            if (!decimalSeparator.equals(spreadsheetSettings.getDecimalSeparator())) {
//                text = text.replace(decimalSeparator.toString(), String.valueOf(spreadsheetSettings.getDecimalSeparator()));
//            }

        Value value = Value.getInstance(text, spreadsheetSettings);
        if(!(value.getValue() instanceof Number)){
            throw Cell.Error.VALUE.getThrowableObject();
        }

        return value;
    }
}
