//$Id$
/*
 * T.java
 *
 * Created on July 14, 2008, 2:21 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.TextFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class T extends PostfixMathCommand implements ScalarFunctionI, TextFunctionI, CallbackEvaluationI
{
 public static Logger logger = Logger.getLogger(T.class.getName());
    /** Creates a new instance of T */
    public T()
    {
        numberOfParameters = 1;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator parserVisitor) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if(numChildren != 1)
        {
            throw Error.VALUE.getThrowableObject();
        }

        Node child = node.jjtGetChild(0);
        Object o = ((ZSEvaluator) parserVisitor).evaluate(child, (Cell) data, true, false);

        if(o instanceof Value)
        {
            o = ((Value)o).getValue();
        }

        if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

        return Value.getInstance(Type.STRING, t(o));

    }

    public static String t(Object value)
    {
        if(value instanceof String)
        {
            return String.valueOf(value);
        }

        return "";
    }

}
