//$Id$
/*
 * Rept.java
 *
 * Created on July 3, 2008, 11:05 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.adventnet.zoho.websheet.model.util.EngineConstants;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.TextFunctionI;
import com.singularsys.jep.parser.Node;
import java.util.GregorianCalendar;
import java.util.Stack;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class Rept extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, TextFunctionI
{
	public static Logger logger = Logger.getLogger(Rept.class.getName());
    /** Creates a new instance of Rept */
    public Rept()
    {
        numberOfParameters = 2;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        Stack valueStack = new Stack();

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
	for(int i = 0; i < num; i++)
	{
	    Node child = node.jjtGetChild(i);
        Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

	    valueStack.push(o);
	}

        Object param2 = null;

        if(num == 2)
        {
            param2 = valueStack.pop();
        }

        Object param1 = valueStack.pop();

        if(param1 instanceof Value)
        {
            param1 = ((Value)param1).getValue();
        }

        if(param2 instanceof Value)
        {
            param2 = ((Value)param2).getValue();
        }

        if(param1 instanceof EvaluationException) {
    throw (EvaluationException)param1;
}
if(param1 instanceof Throwable) {
    throw Error.getError(((Throwable)param1).getMessage()).getThrowableObject();
}

        if(param2 instanceof EvaluationException) {
    throw (EvaluationException)param2;
}
if(param2 instanceof Throwable) {
    throw Error.getError(((Throwable)param2).getMessage()).getThrowableObject();
}

        String text = "";
        int len = 0;

        if(param1 != null)
        {
            if(!(param1 instanceof String))
            {
                param1 = FunctionUtil.objectToNumber(param1);

                if((((Number)param1).doubleValue() - ((Number)param1).longValue()) == 0)
                {
                    param1 = ((Number)param1).longValue();
                }
            }

            text = param1.toString();
        }

        if(param2 != null)
        {
            if(param2 instanceof String)
            {
                param2 = Value.getInstance((String)param2, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(param2 == null || param2 instanceof String  || param2 instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            len = FunctionUtil.objectToNumber(param2).intValue();
        }

        try {
    return Value.getInstance(Type.STRING, rept(text, len));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    public static String rept(String text, int num) throws IllegalArgumentException
    {
        if(num < 0)
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.VALUE));
        }

        if("".equals(text))
        {
            return "";
        }

        // MS limit is 32767 characters.
        if((text.length() * num) >= EngineConstants.CELLVALUE_LIMIT)
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.VALUE));
        }

        String temp = "";
        for(int i = 0; i < num; i++)
        {
            temp = temp + text;
        }

        return temp;
    }

}
