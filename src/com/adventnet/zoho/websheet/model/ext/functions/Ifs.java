//$Id$

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.LogicalFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;

/**
 * The IFS function checks whether one or more conditions are met and returns a value that corresponds to the first TRUE condition.
 * IFS can take the place of multiple nested IF statements, and is much easier to read with multiple conditions.
 *
 * IFS(logical_test1, value_if_true1, [logical_test2, value_if_true2], [logical_test3, value_if_true3],…)
 *
 * logical_test1 -> required , if empty returns #N/A
 * value_if_true1 -> required, if empty return #N/A
 * logical_test2 -> optional , if empty returns #N/A
 * value_if_true1 -> required if logical_test2 is given, if empty return #N/A
 *
 * If none of the condition matched, it will return #N/A
 *
 * ??
 * 1. maximum number of conditions supported?
 * 2. we handle special case for If in ZSEvaluator, how that case will be handled for Ifs.
 * ??
 */
public class Ifs extends PostfixMathCommand implements CallbackEvaluationI, ScalarFunctionI, LogicalFunctionI {

    private static final long serialVersionUID = 300L;

    public Ifs() {
        super();
        numberOfParameters = -1;
    }

    public boolean checkNumberOfParameters(int n) {
        return (n >= 2 && n % 2 == 0);
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int num = node.jjtGetNumChildren();

        if (!checkNumberOfParameters(num))
        {
            throw Cell.Error.NA.getThrowableObject();
//            throw Cell.Error.getError("IFS function should have 2, 4, 6, ... arguments").getThrowableObject();//No I18N
        }

        for(int i = 0; i < num; i+=2) {
            Node conditionNode = node.jjtGetChild(i);

            double val = Xor.booleanEquivalent(conditionNode, data, pv);

            if (val > 0.0) {
                //equivalent to TRUE
                boolean isEvaluateAsScalar = ((ZSEvaluator) pv).isEvaluateAsArrayFormula();
                Node valueNode = node.jjtGetChild(i+1);
                Object result = ((ZSEvaluator)pv).evaluate(valueNode, (Cell)data, isEvaluateAsScalar, false);
                return result instanceof ASTEmptyNode ? Value.EMPTY_VALUE: result;
            }
        }

        return Value.getInstance(Cell.Type.ERROR, Cell.Error.NA);
    }

    @Override
    public boolean isExcelFutureFunction() {
        return true;
    }
}
