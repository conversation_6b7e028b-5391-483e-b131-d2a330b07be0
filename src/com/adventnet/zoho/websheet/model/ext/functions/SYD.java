//$Id$
/*
 * SYD.java
 *
 * Created on March 17, 2008, 4:07 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.functions.*;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.parser.Node;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class SYD extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, FinancialFunctionI
{
	public static Logger logger = Logger.getLogger(SYD.class.getName());
    /** Creates a new instance of SYD */
    public SYD()
    {
        numberOfParameters = 4;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        Object[] param = new Object[num];

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
	for(int i = 0; i < num; i++)
	{
	    Node child = node.jjtGetChild(i);
        Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            // ie. when a cell containing a number as string is referred no need to change it.
            // but should be converted when given directly as arguments of the function years("1/1/2007",...)
            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof String || o instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            if(o == null || o instanceof ASTEmptyNode)
            {
                o = 0;
            }

            param[i] = o;
	}

        double cost = FunctionUtil.objectToNumber(param[0]).doubleValue();
        double salvage = 0; // default value
        double life = FunctionUtil.objectToNumber(param[2]).doubleValue();
        int period = FunctionUtil.objectToNumber(param[3]).intValue();

//        if(param[1] != null && !(param[1] instanceof ASTEmptyNode))
//        {
            salvage = FunctionUtil.objectToNumber(param[1]).doubleValue();
//        }

        try {
    return Value.getInstance(Type.CURRENCY, syd(cost, salvage, life, period));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    /*
     * cost   :   the initial cost of the asset.
     *
     * salvage:   the value at the end of the depreciation.
     *
     * life   :   the number of periods over which the asset is depreciated.
     *
     * period :   the period and must use the same units as life.
     *
     * answer :   sum-of-years'-digits deprciation of an asset for a specified
     *            period Remark.
     *
     * SYD = (cost-salvage)*(life-per+1)*2/(life*(life+1))
     *
     */
    public static double syd( double cost, double salvage, double life, double period ) throws IllegalArgumentException
    {
        //(originalcost - salvagevalue)*(lifetime+1 - year)*2 / [(lifetime+1)*lifetime]
        if(life < 1 || period < 1 || period > life || salvage < 0)
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        return (cost - salvage)* (life -period + 1) * 2 / (life * (life + 1));
    }
}
