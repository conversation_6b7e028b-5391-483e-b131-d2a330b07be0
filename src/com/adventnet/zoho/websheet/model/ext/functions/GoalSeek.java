//$Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.Cell.Type;
//import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.util.JSONConstants;
//import com.adventnet.zoho.websheet.model.Workbook;
//import com.adventnet.zoho.websheet.model.parser.ODSWorkbookParser;
//import com.adventnet.zoho.websheet.model.parser.ODSWorkbookTransformer;
import com.singularsys.jep.functions.Round;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class GoalSeek {
    public static Logger logger = Logger.getLogger(GoalSeek.class.getName());

    private static int findNoOfDigit(double d) {
        d = Math.abs(d);
        int num = 0;
        while(d>=1) {
            num++;
            d = d/10;
        }
        return num;
    }

 /*   private static double roundingOff(double var, int roundTo) {
        double temp = (double)Math.pow(10,roundTo);
        var = var * temp;
        double temp2 = Math.round(var);
        return (double)temp2/temp;        
    }
  *
  */

    private static int findNoOfDecimalPlaces(double d) {
        String strNumber = Double.toString(d);
        String[] temp = new String[2];
        temp = strNumber.split("\\.");
        return temp[1].length();
    }

    private static JSONObjectWrapper evalute(Cell targetCell, Cell variableCell, double desiredValue) throws Exception {
        JSONObjectWrapper jObj = new JSONObjectWrapper();
        List<Cell> cells = new ArrayList<>();
        cells.add(variableCell);
        boolean pDirection = true;
        boolean nDirection = true;
        double targetCellValue;
        double pDirectionValue, nDirectionValue, pDirectionTargetValue, nDirectionTargetValue, pDirectionDiff = 0.0, nDirectionDiff = 0.0, pDirectionPrevDiff, nDirectionPrevDiff;
        double currentValue, pDirectionPrevValue, nDirectionPrevValue;
        int n=0;
        int noOfDecimalPlaces = 0;
        Type type = variableCell.getType();
        if(type == Type.STRING || type == Type.UNDEFINED) {
            variableCell.setValue(Value.getInstance(Type.FLOAT, 0));
            ReEvaluate.getReEvaluateDependents(null, Arrays.asList(variableCell), true);
        }

        if(!(variableCell.getValue().getValue() instanceof Double)) {
            jObj.put("message", "GoalSeek.Msg.NotSuccess");
            return jObj;
        }
        currentValue = (Double)variableCell.getValue().getValue();


        int noOfDigits = findNoOfDigit(currentValue);
        if(noOfDigits == 0) {
            noOfDecimalPlaces = findNoOfDecimalPlaces(currentValue);
        }
        int ppower = noOfDigits - 1;
        int npower = ppower;
        double linearIncrement = Math.pow(10, ppower);
        double linearDecrement = linearIncrement;
        pDirectionPrevValue = nDirectionPrevValue = currentValue;
        pDirectionValue = Round.round((currentValue - currentValue%Math.pow(10,ppower)+linearIncrement),noOfDecimalPlaces);
        nDirectionValue = Round.round((currentValue - currentValue%Math.pow(10,ppower)),noOfDecimalPlaces);
        if(!(targetCell.getValue().getType() == Type.ERROR)) {
            if(!(targetCell.getValue().getValue() instanceof Double)) {
                jObj.put("message", "GoalSeek.Msg.NotSuccess");
                return jObj;
            }
            targetCellValue = (Double)targetCell.getValue().getValue();
            pDirectionPrevDiff = nDirectionPrevDiff = targetCellValue - desiredValue;
            if(Math.abs(targetCellValue - desiredValue)<0.0001) {
                jObj.put("message", "GoalSeek.Msg.Success");
                //jObj.put("solution", String.valueOf(currentValue));
                jObj.put("solution", variableCell.getContent());
                //jObj.put("TargetCellValue",String.valueOf(targetCellValue));
                jObj.put("TargetCellValue", targetCell.getContent());
                jObj.put("n",n);
                return jObj;
            }
        } else {
            jObj.put("message", "GoalSeek.Msg.FormulaError");
            return jObj;
        }

        type = (variableCell.getType() != Type.PERCENTAGE) ? Type.FLOAT : Type.PERCENTAGE;
        while((Math.abs(targetCellValue-desiredValue)>0.0001)&&(n<200)) {
            if(pDirection) {
                variableCell.setValue(Value.getInstance(type, pDirectionValue));
                ReEvaluate.getReEvaluateDependents(null, Arrays.asList(variableCell), true);
                if(targetCell.getValue().getType() == Type.ERROR) {
                    if(Math.abs(pDirectionValue) == Math.pow(10, ppower+1)){
                        ppower++;
                        linearIncrement = Math.pow(10,ppower);
                    }
                    pDirectionValue = (ppower>0)?(pDirectionValue - linearIncrement):Round.round((pDirectionValue - linearIncrement),-ppower);
                    n++;
                    continue;
                }
                if(!(targetCell.getValue().getValue() instanceof Double)) {
                    jObj.put("message", "GoalSeek.Msg.NotSuccess");
                    return jObj;
                }
                pDirectionTargetValue = (Double)targetCell.getValue().getValue();
                pDirectionDiff = pDirectionTargetValue - desiredValue;
                if(Math.abs(pDirectionDiff)<0.0001) {
                    jObj.put("message", "GoalSeek.Msg.Success");
                    //jObj.put("solution", String.valueOf(pDirectionValue));
                    jObj.put("solution", variableCell.getContent());
                    pDirectionTargetValue = (ppower>=0)?pDirectionTargetValue:Round.round(pDirectionTargetValue,-ppower-1);
                    //jObj.put("TargetCellValue",String.valueOf(pDirectionTargetValue));
                    jObj.put("TargetCellValue", targetCell.getContent());
                    jObj.put("n", n);
                    return jObj;
                }else if((pDirectionPrevDiff>0&&pDirectionDiff<0)||(pDirectionPrevDiff<0&&pDirectionDiff>0)) {
                    nDirection = false;
                    ppower--;
                    pDirectionValue = (ppower>0)?(pDirectionPrevValue - pDirectionPrevValue%Math.pow(10, ppower)+Math.pow(10, ppower)):Round.round(pDirectionPrevValue+Math.pow(10, ppower),-ppower);
                    linearIncrement = Math.pow(10, ppower);
                } else if(Math.abs(pDirectionPrevDiff)<Math.abs(pDirectionDiff)) {
                    pDirection = false;
                    nDirection = true;
                    pDirectionPrevValue = pDirectionValue;
                    if(Math.abs(pDirectionValue) == Math.pow(10, ppower+1)){
                        ppower++;
                        linearIncrement = Math.pow(10,ppower);
                    }
                    pDirectionValue = (ppower>0)?(pDirectionValue+linearIncrement):Round.round((pDirectionValue + linearIncrement),-ppower);
                } else {
                    pDirectionPrevValue = pDirectionValue;
                    if(Math.abs(pDirectionValue) == Math.pow(10, ppower+1)){
                        ppower++;
                        linearIncrement = Math.pow(10,ppower);
                    }
                    pDirectionValue = (ppower>0)?(pDirectionValue+linearIncrement):Round.round((pDirectionValue + linearIncrement),-ppower);
                }
            }
            if(nDirection) {
                variableCell.setValue(Value.getInstance(type, nDirectionValue));
                ReEvaluate.getReEvaluateDependents(null, Arrays.asList(variableCell), true);
                if(targetCell.getValue().getType() == Type.ERROR) {
                    if(Math.abs(nDirectionValue) == Math.pow(10, npower+1)){
                        npower++;
                        linearDecrement = Math.pow(10,npower);
                    }
                    nDirectionValue = (npower>0)?(nDirectionValue - linearDecrement):Round.round((nDirectionValue - linearDecrement),-npower);
                    n++;
                    continue;
                }
                if(!(targetCell.getValue().getValue() instanceof Double)) {
                    jObj.put("message", "GoalSeek.Msg.NotSuccess");
                    return jObj;
                }
                nDirectionTargetValue = (Double)targetCell.getValue().getValue();
                nDirectionDiff = nDirectionTargetValue - desiredValue;

                if(Math.abs(nDirectionDiff)<0.0001) {
                    jObj.put("message", "GoalSeek.Msg.Success");
                    //jObj.put("solution", String.valueOf(nDirectionValue));
                    jObj.put("solution", variableCell.getContent());
                    nDirectionTargetValue = (npower>=0)?nDirectionTargetValue:Round.round(nDirectionTargetValue,-npower-1);
                    //jObj.put("TargetCellValue",String.valueOf(nDirectionTargetValue));
                    jObj.put("TargetCellValue", targetCell.getContent());
                    jObj.put("n", n);
                    return jObj;
                }else if((nDirectionPrevDiff>0&&nDirectionDiff<0)||(nDirectionPrevDiff<0&&nDirectionDiff>0)) {
                    pDirection = false;
                    npower--;
                    nDirectionValue = (npower>0)?(nDirectionPrevValue - nDirectionPrevValue%Math.pow(10, npower) - Math.pow(10, npower)):Round.round(nDirectionPrevValue - Math.pow(10, npower),-npower);
                    linearDecrement = Math.pow(10, npower);
                } else if(Math.abs(nDirectionPrevDiff)<Math.abs(nDirectionDiff)) {
                    nDirection = false;
                    pDirection = true;
                    nDirectionPrevValue = nDirectionValue;
                    if(Math.abs(nDirectionValue) == Math.pow(10, npower+1)){
                        npower++;
                        linearDecrement = Math.pow(10,npower);
                    }
                    nDirectionValue = (npower>0)?(nDirectionValue - linearDecrement):Round.round((nDirectionValue - linearDecrement),-npower);
                } else {

                    nDirectionPrevValue = nDirectionValue;
                    if(Math.abs(nDirectionValue) == Math.pow(10, npower+1)){
                        npower++;
                        linearDecrement = Math.pow(10,npower);
                    }
                    nDirectionValue = (npower>0)?(nDirectionValue - linearDecrement):Round.round((nDirectionValue - linearDecrement),-npower);
                }
            }

            //need to be more optimistic here, we need to find the depends in the particular tree only and not all the dependants
            //this can be done by finding the targate cells from the tree and then putting them in a list and then calculating the targate cells one by one
            //targateCell.evaluate();
            //it is also need to the reevalute dependency at the end but not during the iteration
            n++;
            if(n>200) {
                jObj.put("message", "GoalSeek.Msg.NotSuccess");
                return jObj;
            }
        }
        jObj.put("message", "GoalSeek.Msg.NotSuccess");
        return jObj;
    }


    private static List getListOfDepedentCells(Cell variableCell, Cell targetCell) {
        List<Cell> cells = new ArrayList<>();
        cells.add(variableCell);
        List<Cell> depedentCellList = new ArrayList<>();
        depedentCellList = ReEvaluate.getReEvaluateDependents(null, cells, false);
        if(!depedentCellList.contains(targetCell)) {
            depedentCellList.clear();
        }
        return depedentCellList;
    }

    public static JSONObjectWrapper goalSeek(Cell targetCell, Cell variableCell, double desiredValue) throws Exception {
        JSONObjectWrapper jObj = new JSONObjectWrapper();
        if(!(targetCell.isFormula()||((CellImpl)targetCell).isArrayCell())) {
            jObj.put("message", "GoalSeek.Msg.NoFormula");
            jObj.put(JSONConstants.FOCUS_TYPE, "targetCell");
            return jObj;
        }
        if(variableCell.isFormula()||((CellImpl)variableCell).isArrayCell()) {
            jObj.put("message", "GoalSeek.Msg.Formula");
            jObj.put(JSONConstants.FOCUS_TYPE, "variableCell");
            return jObj;
        }
        List<Cell> listOfDepedentCells = new ArrayList<>();
        listOfDepedentCells = getListOfDepedentCells(variableCell, targetCell);
        if(listOfDepedentCells.isEmpty()) {
            jObj.put("message", "GoalSeek.Msg.NotLinkedCell");
            jObj.put(JSONConstants.FOCUS_TYPE, "variableCell");
            return jObj;
        }
        if(listOfDepedentCells.size()>5000) {
            logger.log(Level.INFO, "Goal seek method cannot be completed because of too large number of dependencies");
            jObj.put("message", "GoalSeek.Msg.LargeDependencies");
            return jObj;
        }
        List<Cell> cells = new ArrayList<>();
        cells.add(variableCell);
        Value value = variableCell.getValue();
        try {
            jObj = evalute(targetCell, variableCell, desiredValue);
        } catch(Exception e) {
            logger.log(Level.WARNING,e.getMessage());
            jObj.put("message", "GoalSeek.Msg.NotSuccess");
            return jObj;
        }
        jObj.put("rowIndex", variableCell.getRowIndex());
        jObj.put("columnIndex", variableCell.getColumnIndex());
        if(!(jObj.has("solution"))) {
            variableCell.setValue(value);
        } else {
            logger.log(Level.INFO,"No of iteration in goal seek: "+jObj.getInt("n"));
        }
        ReEvaluate.getReEvaluateDependents(null, cells, true);
        return jObj;
    }

/*
    public static void main(String[] args) {
        String odsFile = "D:\\test_ods_files\\goal.ods";
        //File ods = new File(odsFile);
        try
        {
            ODSWorkbookTransformer transformer = new ODSWorkbookTransformer();
            ODSWorkbookParser parser = new ODSWorkbookParser(transformer);
            transformer.constructWorkbook(null);
            parser.setWorkbookName("Test.ods");
            parser.parse(odsFile);
            transformer.endWorkbook();
            Workbook book = transformer.transform();
            book.updateCellDependencies();
            Sheet sheet1 = book.getSheet("Sheet5");
            Sheet sheet2 = book.getSheet("Sheet5");
            Cell cell1 = sheet1.getCell(7,1);
            //System.out.println(cell1.getCellRef());
            Cell cell2 = sheet2.getCell(0,0);
            //Cell cell = sheet.getCell(2, 2);

            long startTime = System.currentTimeMillis();
            //GoalSeek g = new GoalSeek();
            try {
                System.out.println("Possible value: "+GoalSeek.goalSeek(cell1, cell2, 18));
            } catch(Exception e) {
                System.out.println(e.getMessage());
                e.printStackTrace();
            }
            long endTime = System.currentTimeMillis();
            System.out.println("total time: "+(endTime - startTime));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
 *
 */
}