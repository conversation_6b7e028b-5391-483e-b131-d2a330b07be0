//$Id$
/*
 * Dec2Oct.java
 *
 * Created on July 7, 2008, 3:43 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.EngineeringFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class Dec2Oct extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, EngineeringFunctionI
{
	public static Logger logger = Logger.getLogger(Dec2Oct.class.getName());
    /** Creates a new instance of Dec2Oct */
    public Dec2Oct()
    {
        numberOfParameters = -1;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        if(num >2 || num < 1)
        {
            throw Error.NAME.getThrowableObject();
        }

        //Stack valueStack = new Stack();
        Object param[] = new Object[num];

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);

            Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            if(i == 1 && o == null)
            {
                throw Cell.Error.NUM.getThrowableObject();
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            // when a cell containing a number as string is referred no need to change it.
            // but should be converted when given directly as arguments of the function years("1/1/2007",...)
            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof String || o instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            param[i] = o;
        }

        long dec = 0;

        if(param[0] != null)
        {
            dec = FunctionUtil.objectToNumber(param[0]).longValue();
        }

        Integer places = null;

        if(num == 2)
        {
            places = FunctionUtil.objectToNumber(param[1]).intValue();
        }

        try {
    return Value.getInstance(Type.STRING, dec2Oct(dec, places));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    public static String dec2Oct(long num, Integer places)
    {
        if(num > 536870911 || num < -536870912 || (places != null && places <= 0))
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        if(num < 0)
        {
            if(places != null && places > 10)
            {
                throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
            }

            num = Math.abs(num) - 1;

            String text = Base.base(num, 8, 10);

            text = FunctionUtil.getComplement(text, 8);

            return text;
        }
        else
        {
            String text = Base.base(num, 8, places == null ? 0 : places.intValue());

            if( places != null && places.intValue() < text.length())
            {
                throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
            }

            return text;
        }


        /*
        String text = Base.base(num, 8, places == null ? 0 : places.intValue());

        if(places != null && places.intValue() < text.length())
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        return text;
         */
    }


    public static void main(String args[])
    {
        logger.info(dec2Oct(-12,10));
    }

}
