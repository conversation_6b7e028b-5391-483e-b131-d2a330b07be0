//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.TextFunctionI;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.DateUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;
import java.util.Date;

public class ArrayToText extends PostfixMathCommand implements CallbackEvaluationI, PartialScalarFunctionI, TextFunctionI {
    public ArrayToText() {
        this.numberOfParameters = -1;
    }

    public boolean isScalarArgument(int index) {
        return index == 1;
    }

    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int noOfChildren = node.jjtGetNumChildren();
        if (noOfChildren == 0 || noOfChildren > 2) {
            throw Cell.Error.NAME.getThrowableObject();
        }

        Node textNode = node.jjtGetChild(0);
        Object textObjects = ((ZSEvaluator) pv).evaluate(textNode, (Cell) data, this.isScalarArgument(0), false);
        NonScalarObjectIterator textObjectIterator = new NonScalarObjectIterator(textObjects);
        int format = 0;
        if (noOfChildren == 2) {
            Object formatObject = ((ZSEvaluator) pv).evaluate(node.jjtGetChild(1), (Cell) data, this.isScalarArgument(1), false);
            if (formatObject instanceof Throwable || !(formatObject instanceof Value) || !(((Value) formatObject).getValue() instanceof Number)) {
                throw Cell.Error.VALUE.getThrowableObject();
            }

            if (formatObject instanceof Value) {
                Number numberObj = FunctionUtil.objectToNumber(formatObject);
                if (!numberObj.equals(0.0D) && !numberObj.equals(1.0D)) {
                    throw Cell.Error.VALUE.getThrowableObject();
                }
                format = numberObj.intValue();
            }
        }

        int colSize = textObjects instanceof Range ? ((Range) textObjects).getColSize() : 1;
        int breakCount = colSize;
        String result = ""; //No I18N
        String delimiter = ";"; //No I18N

        while (textObjectIterator.hasNext()) {
            Object textObject = textObjectIterator.next();
            Object textValue = ((Value) textObject).getValue();

            delimiter = format == 1 && breakCount == 1 ? "|" : ";"; //No I18N
            if (textValue != null && !textValue.equals("")) { //No I18N
                if (format == 1 && textValue instanceof String) {
                    String string = textValue.toString();
                    result = result + "\"" + string + "\""; //No I18N
                } else if (textValue instanceof Number) {
                    result = result + ((Double) textValue % 1.0D == 0.0D ? ((Double) textValue).intValue() : textValue);
                } else if (textValue instanceof Date) {
                    result = result + DateUtil.convertDateToNumber((Date) textValue).intValue();
                } else {
                    result = result + textValue;
                }
            } else {
                result = result + ""; //No I18N
            }

            if (textObjectIterator.hasNext()) {
                result = result + delimiter;
                if(format == 0){
                    result = result + " "; //NO I18N
                }
            } else {
                return format == 1 ? "{" + result + "}" : result; //No I18N
            }
            if (colSize > 1) {
                breakCount = breakCount == 1 ? colSize : breakCount - 1;
            }
        }

        throw Cell.Error.VALUE.getThrowableObject();
    }

    public boolean isExcelFutureFunction() {
        return true;
    }
}
