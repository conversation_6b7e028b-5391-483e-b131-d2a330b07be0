//$Id$
/*
 * IPMT.java
 *
 * Created on March 17, 2008, 4:19 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.functions.*;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.parser.Node;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class IPMT extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, FinancialFunctionI, PartiallyExclusiveFunctionI
{
	public static Logger logger = Logger.getLogger(IPMT.class.getName());
    /** Creates a new instance of IPMT */
    public IPMT()
    {
        numberOfParameters = -1;
    }

    /*
     * Checks the number of parameters of the call.
     *
     */
        @Override
    public boolean checkNumberOfParameters(int n)
    {
        return (n == 4 || n == 5 || n == 6);
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pve) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        if(!checkNumberOfParameters(num))
        {
            throw Error.NAME.getThrowableObject();
        }

        //Stack valueStack = new Stack();
        Object param[] = new Object[num];

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);
            Object o = ((ZSEvaluator)pve).evaluate(child, (Cell)data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            // must pass values for all required fields, else error will be returned.
            if(o == null || o instanceof ASTEmptyNode)
            {
                if(i == 1 || i == 2)
                {
                    throw Error.NUM.getThrowableObject();
                }
                else
                {
                    o = 0;
                }
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            // when a cell containing a number as string is referred no need to change it.
            // but should be converted when given directly as arguments of the function years("1/1/2007",...)
            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof String || o instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            param[i] = o;
        }

        double rate = FunctionUtil.objectToNumber(param[0]).doubleValue();
        int period = FunctionUtil.objectToNumber(param[1]).intValue();
        double nPer = FunctionUtil.objectToNumber(param[2]).doubleValue();
        double pv = FunctionUtil.objectToNumber(param[3]).doubleValue();
        double fv = 0;
        int due = 0;

        if(num > 4)
        {
            fv = FunctionUtil.objectToNumber(param[4]).doubleValue();

            if(num > 5)
            {
                due = FunctionUtil.objectToNumber(param[5]).intValue();
            }
        }

        try {
    return Value.getInstance(Type.CURRENCY, ipmt(rate, period, nPer, pv, fv, due));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }


    /*
     * rate   :   the interest rate per period.
     *
     * nPer   :   the total number of payment periods in an annuity.
     *
     * pv     :   the present value, or the lump-sum amount that a series of
     *            future payments is worth right now. If pv is omitted, it is
     *            assumed to be 0 (zero), and you must include the pmt argument.
     *
     * due    :   the number 0 or 1 and indicates when payments are due. If type
     *            is omitted, it is assumed to be 0.
     *
     * per    :   the period for which you want to find the interest and must be
     *            in the range 1 to nper.
     *
     * fv     :   the future value, or a cash balance you want to attain after
     *            the last payment is made. If fv is omitted, it is assumed to
     *            be 0 (the future value of a loan, for example, is 0).
     *
     * answer :   the interest payment for a given period of an annuity, based on
     *            periodic , constant payments, and on an unvarying interest rate
     *
     * Rate and nper must be of same units.
     *
     * Eg : If you make monthly payments on a four-year loan at 12 percent annual interest,
     * use 12%/12 for rate and 4*12 for nper. If you make annual payments on the same loan,
     * use 12% for rate and 4 for nper.
     *
     * Cash you pay out is represented by negative numbers
     * cash you receive is represented by positive numbers
     *
     */
    public static double ipmt( double rate, int per, double nPer, double pv, double fv, int due ) throws IllegalArgumentException
    {
        if (rate < 0 || nPer < 0 || per > nPer || (due != 1 && due != 0))
        {
                throw (new IllegalArgumentException(CellUtil.getErrorString(Error.NUM)));
        }

        double pmt;
        double ipmt = 0;
        double principal;
        double ppmt = 0;

        pmt = Math.abs( PMT.pmt( rate, nPer, pv, fv, due ) );

        principal = Math.abs( pv );

        for ( int curper = 1; curper <= per; curper++ )
        {
            if ( curper != 1 || due == 0 )
            {
                ipmt = rate * principal;
            }
            ppmt = pmt - ipmt;
            principal = principal - ppmt;
        }

        if ( pv > 0 || ( pv == 0 && fv > 0 ) )
        {
            ipmt *= -1;
        }

        return ipmt;
    }

    @Override
    public boolean isNonExclusiveArgument(int index, int numberOfArguments) {
        return (index == 3);
    }
}
