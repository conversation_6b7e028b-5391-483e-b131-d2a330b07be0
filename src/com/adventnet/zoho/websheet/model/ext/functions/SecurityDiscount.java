// $Id$
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.PartiallyExclusiveFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.singularsys.jep.parser.Node;
import java.util.Date;
import java.util.logging.Logger;


public abstract class SecurityDiscount extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, FinancialFunctionI, PartiallyExclusiveFunctionI
{
	public static Logger logger = Logger.getLogger(SecurityDiscount.class.getName());
        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {

        int num = node.jjtGetNumChildren();

        if(num < 4 || num > 5)
        {
            throw Error.NAME.getThrowableObject();
        }

        Object[] param = new Object[num];

        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);
            Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

                if(o instanceof Value)
                {
                    o = ((Value)o).getValue();
                }

                if(o == null || o instanceof ASTEmptyNode)
                {
                    o = 0;
                }

                if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

                if(o instanceof String)
                {
                    o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                    if(o == null || o instanceof String || o instanceof Throwable)
                    {
                        throw Error.VALUE.getThrowableObject();
                    }
                }

                param[i] = o;
        }

        Date settlement = FunctionUtil.objectToDate(param[0]);
        Date maturity = FunctionUtil.objectToDate(param[1]);
        double price = FunctionUtil.objectToNumber(param[2]).doubleValue();
        double redemption = FunctionUtil.objectToNumber(param[3]).doubleValue();
        int basis = 0;
        if(num > 4 && param[4] != null)
        {
            basis = FunctionUtil.objectToNumber(param[4]).intValue();
        }

        try {
    return getResult( settlement,maturity, price, redemption,basis);
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    public abstract double getResult(Date settlement, Date maturity, double priceOrDiscount, double redemption,int basis);

    public static double getYieldDisc(Date settlement, Date maturity, double price, double redemption,int basis)
    {
        if(price <= 0 || redemption <= 0 || basis < 0 || basis > 4 || !settlement.before(maturity))
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }
        double num = (redemption/price - 1);
        double den = YearFrac.yearFrac(settlement, maturity, basis);
        return (num/den);

        //(1 - price/redemption) / YEARFRAC(settlementdate; maturitydate; basis)
    }

    public static double getPriceDisc(Date settlement, Date maturity, double discount, double redemption, int basis)
    {
        if(discount <= 0 || redemption <= 0 || basis < 0 || basis > 4 || !(settlement.before(maturity)))
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        double priceDisc = redemption - (discount * redemption * YearFrac.yearFrac(settlement, maturity, basis));

        return priceDisc;
    }
    
}
