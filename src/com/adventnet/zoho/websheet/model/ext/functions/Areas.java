// $Id$

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.ReferenceFunctionI;
import com.singularsys.jep.parser.Node;
import java.util.List;

public class Areas extends PostfixMathCommand implements CallbackEvaluationI, ReferenceFunctionI
{
    public Areas()
    {
        numberOfParameters = 1;
    }
    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        Node child = node.jjtGetChild(0);
        int result = 0;

        Object evalResult = ((ZSEvaluator)pv).evaluate(child, (Cell) data, false, true);

        if(evalResult instanceof List) {
            List<Range> list = (List<Range>) evalResult;
            result = list.size();
        } else if(evalResult instanceof Range) {
            result = 1;
        } else {
            throw Cell.Error.VALUE.getThrowableObject();
        }

        return result;

    }

}
