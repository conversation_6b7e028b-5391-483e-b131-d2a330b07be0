//$Id$
/*
 * DollarDE.java
 *
 * Created on March 26, 2008, 2:58 PM
 *
 * To change this template, choose <PERSON><PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.FinancialFunctionI;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.*;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class DollarDE extends PostfixMathCommand implements ScalarFunctionI, FinancialFunctionI, CallbackEvaluationI
{
	public static Logger logger = Logger.getLogger(DollarDE.class.getName());
    /** Creates a new instance of DollarDE */
    public DollarDE()
    {
        numberOfParameters = 2;
    }

    @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException {
        int numChildren = node.jjtGetNumChildren();

        if (numChildren != 2) {
            throw Error.VALUE.getThrowableObject();
        }



        Object param1 = null, param2 = null;

        for(int i = 0; i < numChildren; i++)
        {
            Node child = node.jjtGetChild(i);
            Object o = ((ZSEvaluator) pv).evaluate(child, (Cell) data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            if(o == null)
            {
                o = 0;
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            // when a cell containing a number as string is referred no need to change it.
            // but should be converted when given directly as arguments of the function years("1/1/2007",...)
            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof String || o instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            if(i == 0)
            {
                param1 = o;
            }
            else
            {
                param2 = o;
            }
        }

        double fracDollar = FunctionUtil.objectToNumber(param1).doubleValue();
        int fraction = FunctionUtil.objectToNumber(param2).intValue(); // any double value given for fraction will get truncated to int.

        try {
    return Value.getInstance(Type.FLOAT, dollarDE(fracDollar, fraction));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    /*
     * fracDollar :   the currency value in fraction
     *
     * fraction   :   the denominator of the fracDollar.
     *
     * converts the fracDollar to a value equiivalent to denominator 10.
     * ie., converts a fraction to decimal.
     */
    public double dollarDE(double fracDollar, int fraction) throws IllegalArgumentException
    {
        if(fraction < 0)
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        if(fraction == 0)
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.DIV));
        }

        long longVal =(long)fracDollar;

        double numerator = (fracDollar - longVal);

        double denom = (double)fraction;

        while(!(denom > 0 && denom <= 1))
        {
            denom = denom / 10;
        }

        return longVal + (numerator / denom);
    }

    public static void main(String[] args)
    {
        double d = (new DollarDE()).dollarDE(12.05,1000);

        logger.info(Double.toString(d));
    }

}
