//$Id$
/*
 * Dec2Hex.java
 *
 * Created on July 7, 2008, 3:44 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.adventnet.zoho.websheet.model.util.CellUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.EngineeringFunctionI;
import com.singularsys.jep.parser.Node;

import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class Dec2Hex extends PostfixMathCommand implements CallbackEvaluationI,ScalarFunctionI, EngineeringFunctionI
{
	public static final Logger LOGGER = Logger.getLogger(Dec2Hex.class.getName());
    /** Creates a new instance of Dec2Hex */
    public Dec2Hex()
    {
        numberOfParameters = -1;
    }

        @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        if(num >2 || num < 1)
        {
            throw Error.NAME.getThrowableObject();
        }

        //Stack valueStack = new Stack();
        Object param[] = new Object[num];

        // Evaluate each node and get the value,
        // For this function each argument is for a unique field so the stack we get must not have more than one element.
        // else throw error.
        for(int i = 0; i < num; i++)
        {
            Node child = node.jjtGetChild(i);

            Object o = ((ZSEvaluator)pv).evaluate(child, (Cell)data, true, false);

            if(o instanceof Value)
            {
                o = ((Value)o).getValue();
            }

            if(i == 1 && o == null)
            {
                throw Cell.Error.NUM.getThrowableObject();
            }

            if(o instanceof EvaluationException) {
    throw (EvaluationException)o;
}
if(o instanceof Throwable) {
    throw Error.getError(((Throwable)o).getMessage()).getThrowableObject();
}

            // when a cell containing a number as string is referred no need to change it.
            // but should be converted when given directly as arguments of the function years("1/1/2007",...)
            if(o instanceof String)
            {
                o = Value.getInstance((String)o, ((Cell)data).getSpreadsheetSettings()).getValue();

                if(o == null || o instanceof String || o instanceof Throwable)
                {
                    throw Error.VALUE.getThrowableObject();
                }
            }

            param[i] = o;
        }

        long dec = 0;

        if(param[0] != null)
        {
            dec = FunctionUtil.objectToNumber(param[0]).longValue();
        }

        Integer places = null;

        if(num == 2)
        {
            places = FunctionUtil.objectToNumber(param[1]).intValue();
        }

        try {
    return Value.getInstance(Type.STRING, dec2Hex(dec, places));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
    }

    public static String dec2Hex(long num, Integer places)
    {
        if(num > 549755813887L || num < -549755813888L || (places != null && places <= 0))
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        if(num < 0)
        {
            if(places != null && places > 10)
            {
                throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
            }

            num = Math.abs(num) - 1;

            String text = Base.base(num, 16, 10);

            text = FunctionUtil.getComplement(text, 16);

            return text;
        }
        else
        {
            String text = Base.base(num, 16, places == null ? 0 : places);

            if( places != null && places < text.length())
            {
                throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
            }

            return text;
        }

        /*
        String text = Base.base(num, 16, places == null ? 0 : places.intValue());

        if(places != null && places.intValue() < text.length())
        {
            throw new IllegalArgumentException(CellUtil.getErrorString(Error.NUM));
        }

        return text;
         */
    }

    public static void main(String args[])
    {
        LOGGER.info(dec2Hex(-12,10));
    }

}
