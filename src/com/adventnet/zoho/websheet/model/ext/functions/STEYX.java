//$Id$
/*
 * STEYX.java
 *
 * Created on August 10, 2009, 4:15 PM
 *
 * To change this template, choose Too<PERSON> | Template Manager
 * and open the template in the editor.
 */

package com.adventnet.zoho.websheet.model.ext.functions;

import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.Cell.Error;
import com.adventnet.zoho.websheet.model.SpreadsheetSettings;
import com.adventnet.zoho.websheet.model.Value;
import com.adventnet.zoho.websheet.model.ext.NonScalarObjectIterator;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.NonExclusiveFunctionI;
import com.adventnet.zoho.websheet.model.ext.parser.ASTEmptyNode;
import com.adventnet.zoho.websheet.model.ext.standard.ZSEvaluator;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Evaluator;
import com.singularsys.jep.functions.Average;
import com.singularsys.jep.functions.CallbackEvaluationI;
import com.singularsys.jep.functions.PostfixMathCommand;
import com.adventnet.zoho.websheet.model.ext.functions.Categories.StatisticalFunctionI;
import com.singularsys.jep.parser.Node;
import java.util.Vector;
import java.util.logging.Logger;


/**
 *
 * <AUTHOR>
 */
public class STEYX extends PostfixMathCommand implements CallbackEvaluationI, ArrayFunctionI, StatisticalFunctionI, NonExclusiveFunctionI
{
 public static Logger logger = Logger.getLogger(STEYX.class.getName());
    /** Creates a new instance of STEYX */
        public STEYX()
    {
        numberOfParameters = 2;
    }

    /*
     * Checks the number of parameters of the call.
     *
     */
 @Override
    public boolean checkNumberOfParameters(int n)
    {
        return (n == 2);
    }

 @Override
    public Object evaluate(Node node, Object data, Evaluator pv) throws EvaluationException
    {
        int num = node.jjtGetNumChildren();

        if(!checkNumberOfParameters(num))
        {
            throw Error.NAME.getThrowableObject();
        }

        Node child_1 = node.jjtGetChild(0);
        Node child_2 = node.jjtGetChild(1);

            NonScalarObjectIterator stack_1 = new NonScalarObjectIterator(((ZSEvaluator)pv).evaluate(child_1, (Cell)data, false, false));
            NonScalarObjectIterator stack_2 = new NonScalarObjectIterator(((ZSEvaluator)pv).evaluate(child_2, (Cell)data, false, false));
            if(stack_1.size() != stack_2.size())

            {
                throw Cell.Error.NA.getThrowableObject();
            }
            else
            {
                Vector<Number> array_1 = new Vector<>();
                Vector<Number> array_2 = new Vector<>();

                while(stack_1.hasNext())
                {
                    Object param_1 = stack_1.next();
                    Object param_2 = stack_2.next();

                    if(param_1 instanceof Value)
                    {
                        param_1 = ((Value)param_1).getValue();
                    }

                    if(param_2 instanceof Value)
                    {
                        param_2 = ((Value)param_2).getValue();
                    }

                    if(param_1 instanceof Value)

                    {
                        param_1 = ((Value)param_1).getValue();

                    }

                    if(param_2 instanceof Value)

                    {
                        param_2 = ((Value)param_2).getValue();
                    }

                    if(param_1 == null || param_1 instanceof String || param_2 == null || param_2 instanceof String)
                    {
                        continue;
                    }
                    else if(param_1 instanceof EvaluationException) {
    throw (EvaluationException)param_1;
}
if(param_1 instanceof Throwable) {
    throw Error.getError(((Throwable)param_1).getMessage()).getThrowableObject();
}
                    if(param_2 instanceof EvaluationException) {
                        throw (EvaluationException)param_2;
                    }
                    if(param_2 instanceof Throwable) {
                        throw Error.getError(((Throwable)param_2).getMessage()).getThrowableObject();
                    }
                    if(param_1 instanceof ASTEmptyNode || param_2 instanceof ASTEmptyNode)
                    {
                        throw Cell.Error.VALUE.getThrowableObject();
                    }

                    array_1.add(FunctionUtil.objectToNumber(param_1));
                    array_2.add(FunctionUtil.objectToNumber(param_2));
                }

                try {
    return Value.getInstance(Cell.Type.FLOAT, getResult(array_1, array_2, ((Cell)data).getSpreadsheetSettings()));
}
catch(IllegalArgumentException e)
{
    throw Cell.Error.VALUE.getThrowableObject();
}
            }
//        }
    }

    public double getResult(Vector<Number> array_1, Vector<Number> array_2, SpreadsheetSettings spreadsheetSettings) throws EvaluationException
    {
        if(array_1.size() == 1)
        {
            throw Cell.Error.DIV.getThrowableObject();
        }

        double avg_1 = Average.average(array_1, spreadsheetSettings);
        double avg_2 = Average.average(array_2, spreadsheetSettings);
        double productSum = 0;
        double squareSum1 = 0;
        double squareSum2 = 0;
        for(int i = 0; i < array_1.size(); i++)
        {
            double d1 = array_1.get(i).doubleValue();
            double d2 = array_2.get(i).doubleValue();

            d1 -= avg_1;
            d2 -= avg_2;

            productSum += (d1*d2);
            squareSum1 += (d2*d2); //x
            squareSum2 += (d1*d1); //y


        }
        double result = productSum*productSum;

        result /= squareSum1;

        result = (squareSum2 - result) / (array_1.size() - 2);

        result = Math.sqrt(result);

        return result;
    }
}
