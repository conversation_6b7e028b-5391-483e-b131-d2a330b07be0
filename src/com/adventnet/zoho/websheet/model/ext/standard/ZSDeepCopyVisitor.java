//// $Id$
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.adventnet.zoho.websheet.model.ext.standard;

import com.adventnet.zoho.websheet.model.Table;
import com.adventnet.zoho.websheet.model.ext.parser.*;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Jep;
import com.singularsys.jep.JepException;
import com.singularsys.jep.ParseException;
import com.singularsys.jep.parser.ASTConstant;
import com.singularsys.jep.parser.ASTFunNode;
import com.singularsys.jep.parser.ASTOpNode;
import com.singularsys.jep.parser.ASTVarNode;
import com.singularsys.jep.parser.JccParserTreeConstants;
import com.singularsys.jep.parser.Node;
import com.singularsys.jep.walkers.DeepCopyVisitor;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ZSDeepCopyVisitor extends DeepCopyVisitor{
    
    private boolean isWIP= false;
    private Map<String, Table> oldName_newTableMap = null;

    public ZSDeepCopyVisitor(Jep j) {super(j); }
    
    private void reset(){
        isWIP = false;
        this.oldName_newTableMap = null;
    }
    
    /** Creates a deepCopy of a Node **/	
    @Override
    public Node deepCopy(Node node) throws JepException{
        
        if(isWIP)
        {
            return new ZSDeepCopyVisitor(jep).deepCopy(node);
        }
        
        try
        {
            isWIP = true;
            Node res = visitNode(node,null);
            return res;
        }
        catch(JepException e)
        {
            throw e;
        }
        finally
        {
            reset();
        }
    }

    public Node deepCopy(Node node, Map<String, Table> oldName_newTableMap) throws JepException{

        if(isWIP)
        {
            return new ZSDeepCopyVisitor(jep).deepCopy(node, oldName_newTableMap);
        }

        try
        {
            isWIP = true;
            this.oldName_newTableMap = oldName_newTableMap;
            Node res = visitNode(node, null);
            return res;
        }
        catch(JepException e)
        {
            throw e;
        }
        finally
        {
            reset();
        }
    }
	
    @Override
    public Object visit(ASTConstant node, Object data) throws ParseException {
        ASTConstant clonedNode= nf.buildConstantNode(node);
        clonedNode.setIsPercentage(node.isPercentage());
        return clonedNode;
    }
    
    @Override
    public Object visit(ASTFunNode node, Object data) throws JepException {
        Node[] children=visitChildren(node,data);
        return nf.buildFunctionNode(node,children);
    }
    
    @Override
    public Object visit(ASTOpNode node, Object data) throws JepException {
        Node[] children=visitChildren(node,data);
        return nf.buildOperatorNode(node.getOperator(),children);
    }
    
    @Override
    public Object visit(ASTVarNode node, Object data) throws ParseException {
        ASTVarNode clonedNode= nf.buildVariableNode(node);
        clonedNode.setFrom(node);
        return clonedNode;
    }
    
    @Override
    public Object visit(ASTRangeNode node, Object data) throws JepException{
        Node[] children= visitChildren(node, data);
        return this.buildRangeNode(node, children);
    }
    
    @Override
    public Object visit(ASTArrayNode node, Object data) throws JepException{
        Node[] children= visitChildren(node, data);
        ASTArrayNode clonedNode= this.buildArrayNode(node, children);
        return clonedNode;
    }
    
    @Override
    public Object visit(ASTEmptyNode node, Object data){
        return this.buildEmptyNode();
    }
    
    @Override
    public Object visit(ASTParseErrorNode node, Object data){
        ASTParseErrorNode clonedNode= this.buildParseErrorNode(node);
        clonedNode.setNonParsableFormulaString(node.getNonParsableFormulaString());
        return clonedNode;
    }

    @Override
    public Object visit(ASTStructuredReferenceNode node, Object data) throws JepException {
        return node.deepCopy(oldName_newTableMap);
    }
    
    
    protected ASTRangeNode buildRangeNode(ASTRangeNode node, Node[] arguments){
        return this.buildRangeNode(arguments);
    }
    
    protected ASTRangeNode buildRangeNode(Node[] arguments){
        ASTRangeNode res= new ASTRangeNode(JccParserTreeConstants.JJTRANGENODE);
        nf.copyChildren(res, arguments);
        return res;
    }
    
    
    protected ASTArrayNode buildArrayNode(ASTArrayNode node, Node[] arguments) throws EvaluationException{
        return this.buildArrayNode(node.getArray(), node.getRowDimension(), node.getColDimension(), arguments);
    }
    
    protected ASTArrayNode buildArrayNode(List array, int rowDimension, int colDimension, Node[] arguments){
        ASTArrayNode res= new ASTArrayNode(JccParserTreeConstants.JJTARRAYNODE);
        res.setArray(array);
        res.setRowDimension(rowDimension);
        res.setColDimension(colDimension);
        nf.copyChildren(res, arguments);
        return res;
    }
    
    
    protected ASTEmptyNode buildEmptyNode(){
        return new ASTEmptyNode(JccParserTreeConstants.JJTEMPTYNODE);
    }
    
    
    protected ASTParseErrorNode buildParseErrorNode(ASTParseErrorNode node){
        return new ASTParseErrorNode(node.getNonParsableFormulaString());
    }
}
