//// $Id$

package com.adventnet.zoho.websheet.model.analyse;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @param <R> type of the raw input value objects (String or Value or Cell or Object , etc.) which have to be tested on
 */
public class TypeDetecter <R> {
    private final Set<DataType<R>> dataTypeSet;
    private final Map<DataType<R>, Set<DataType<R>>> exclusionSets;
    private final Map<DataType<R>, Set<DataType<R>>> inclusionSets;
    private final Map<DataType<R>, Integer> priorities;
    private final Set<DiscreteDataType<R, ?>> acceptOnlyWithDuplicates;
    private final Map<DataType<R>, Integer> sufficientValuesSizes;
    private final Predicate<R> emptyPredicate;
    
    public TypeDetecter(Set<DataType<R>> dataTypeSet, Map<DataType<R>, Set<DataType<R>>> exclusionSets, Map<DataType<R>, Set<DataType<R>>> inclusionSets, Map<DataType<R>, Integer> sufficientValuesSizes, Map<DataType<R>, Integer> priorities, Set<DiscreteDataType<R, ?>> acceptOnlyWithDuplicates, Predicate<R> emptyPredicate){
        List<DataType<R>> tempDataTypeList = new ArrayList<>(dataTypeSet);
        tempDataTypeList.sort((dataType0, dataType1) -> -(Integer.compare(inclusionSets.getOrDefault(dataType0, new HashSet<>()).size() + exclusionSets.getOrDefault(dataType0, new HashSet<>()).size(),
                                                                          inclusionSets.getOrDefault(dataType1, new HashSet<>()).size() + exclusionSets.getOrDefault(dataType1, new HashSet<>()).size())));
        
        this.dataTypeSet = new LinkedHashSet<>(tempDataTypeList);
        this.exclusionSets = exclusionSets;
        this.inclusionSets = inclusionSets;
        this.sufficientValuesSizes = sufficientValuesSizes;
        this.priorities = priorities;
        this.acceptOnlyWithDuplicates = acceptOnlyWithDuplicates;
        this.emptyPredicate = emptyPredicate;
    }
    
    public DataType detectType(Iterator<R> rawValues){
        Set<DataType> reducedDataTypeSet = new LinkedHashSet<>(dataTypeSet);
        
        int valuesCounter = 0;
        while(rawValues.hasNext()){
            R rawValue= rawValues.next();
            if(isEmpty(rawValue)) {
                continue;
            }
            valuesCounter++;
            int maxAvailableSufficientValuesSize = reducedDataTypeSet.stream().mapToInt(dataType -> sufficientValuesSizes.getOrDefault(dataType, Integer.MAX_VALUE)).max().orElse(Integer.MAX_VALUE);
            if(reducedDataTypeSet.isEmpty() || valuesCounter > maxAvailableSufficientValuesSize){
                break;
            }
            
            Set<DataType> acceptedDataTypeSet = new HashSet<>();
            Set<DataType> excludedDataTypeSet = new HashSet<>();
            for(DataType currDataType: reducedDataTypeSet){
                if(!excludedDataTypeSet.contains(currDataType)){
                    boolean isAcceptedAndSwallowedCurrentDataType;
                    if(acceptedDataTypeSet.contains(currDataType)){
                        isAcceptedAndSwallowedCurrentDataType = currDataType.swallow(rawValue);
                    }
                    else{
                        isAcceptedAndSwallowedCurrentDataType = currDataType.acceptAndSwallow(rawValue);
                    }
                    if(isAcceptedAndSwallowedCurrentDataType){
                        Set<DataType<R>> exclusionSet = exclusionSets.get(currDataType);
                        if(exclusionSet!=null){
                            excludedDataTypeSet.addAll(exclusionSet);
                        }
                        acceptedDataTypeSet.add(currDataType);
                        Set<DataType<R>> inclusionSet = inclusionSets.get(currDataType);
                        if(inclusionSet!=null){
                            acceptedDataTypeSet.addAll(inclusionSet);
                        }
                    }
                    else{
                        excludedDataTypeSet.add(currDataType);
                    }
                }
            }
            reducedDataTypeSet.removeAll(excludedDataTypeSet);
        }
        
        reducedDataTypeSet = reducedDataTypeSet.stream().filter(dataType -> {
            if(dataType instanceof DiscreteDataType) {
                DiscreteDataType<R, ?> discreteDataType = (DiscreteDataType<R, ?>) dataType;
                if(acceptOnlyWithDuplicates.contains(discreteDataType)) {
                    return discreteDataType.hasDuplicates();
                }
            }
            return true;
        }).collect(Collectors.toSet());
        
        DataType dataTypeFinal = reducedDataTypeSet.stream().max((dataType0, dataType1) -> Integer.compare(priorities.getOrDefault(dataType0, Integer.MIN_VALUE), priorities.getOrDefault(dataType1, Integer.MIN_VALUE))).orElse(null);
        
        dataTypeFinal = dataTypeFinal instanceof DiscreteDataType ? new DiscreteDataType((DiscreteDataType)dataTypeFinal) : dataTypeFinal;
        
        for(DataType dataType : this.dataTypeSet){
            if(dataType instanceof DiscreteDataType){
                ((DiscreteDataType) dataType).reInitValueSet();
            }
        }
        
        return dataTypeFinal;
    }
    
    private boolean isEmpty(R value) {
        return emptyPredicate.test(value);
    }
}
