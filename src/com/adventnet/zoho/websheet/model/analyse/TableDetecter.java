//// $Id$
package com.adventnet.zoho.websheet.model.analyse;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.Utility;
import com.google.common.collect.ImmutableList;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 *
 * <AUTHOR>
 */
public class TableDetecter {
    
    public static class Column {
        private final String header;
        private final List<Column> children;
        
        private Column(String header) {
            this.header = header;
            this.children = new LinkedList<>();
        }
        
        public String getHeader() {
            return this.header;
        }
        
        private void addChildren(Collection<Column> childrenNew) {
            this.children.addAll(childrenNew);
        }
        
        public ImmutableList<Column> getChildren() {
            return ImmutableList.copyOf(this.children);
        }
    }
    
    private final int maxHeaderDepth;
    private final Predicate<ReadOnlyCell> headerCellPredicate;
    private final ColumnDetectionType columnDetectionType;
    
    private DataCell seedCell;
    private DataRange tableRange;
    private Column columnsTree;

    private TableDetecter(DataCell seedCell, int maxHeaderDepth, Predicate<ReadOnlyCell> headerCellPredicate, ColumnDetectionType columnDetectionType) {
        this.seedCell = seedCell;
        this.maxHeaderDepth = maxHeaderDepth;
        this.headerCellPredicate = headerCellPredicate;
        this.columnDetectionType = columnDetectionType;
    }
    
    private TableDetecter(DataRange tableRange, int maxHeaderDepth, Predicate<ReadOnlyCell> headerCellPredicate, ColumnDetectionType columnDetectionType) {
        this.tableRange = tableRange;
        this.maxHeaderDepth = maxHeaderDepth;
        this.headerCellPredicate = headerCellPredicate;
        this.columnDetectionType = columnDetectionType;
    }
    
    public static TableDetecter getInstance(String seedCellASN, int seedCellRowIndex, int seedCellColIndex, int maxHeaderDepth, Predicate<ReadOnlyCell> headerCellPredicate, ColumnDetectionType columnDetectionType) {
        DataCell seedCell = new DataCell(seedCellASN, seedCellRowIndex, seedCellColIndex);
        return new TableDetecter(seedCell, maxHeaderDepth, headerCellPredicate, columnDetectionType);
    }
    
    public static TableDetecter getInstance(DataRange tableRange, int maxHeaderDepth, Predicate<ReadOnlyCell> headerCellPredicate, ColumnDetectionType columnDetectionType) {
        return new TableDetecter(tableRange, maxHeaderDepth, headerCellPredicate, columnDetectionType);
    }
    
    public DataRange getTableRange(Workbook workbook) {
        if(this.tableRange == null) {
            this.tableRange = detectTableRange(workbook);
        }
        
        return this.tableRange;
    }
    
    private DataRange detectTableRange(Workbook workbook) {
        DataRange seedRange = this.seedCell.toDataRange();
        DataRange expandedRange = seedRange;
        DataRange oldRange;
        do {
            oldRange = expandedRange;
            expandedRange = visitAroundOnce(workbook, oldRange);
        } while(!oldRange.equals(expandedRange));
        return expandedRange;
    }
    
    private static DataRange visitAroundOnce(Workbook workbook, DataRange range) {
        Sheet sheet = workbook.getSheetByAssociatedName(range.getAssociatedSheetName());
        
        // Right
        for(int rowIndex = range.getStartRowIndex(); rowIndex <= range.getEndRowIndex(); ) {
            if(range.getEndColIndex() >= Utility.MAXNUMOFCOLS - 1) {
                break;
            }
            ReadOnlyCell roCell = RangeIterator.getReadOnlyCellAtSheetFromShell(sheet, rowIndex, range.getEndColIndex() + 1, false);
            if(!isValueEmpty(roCell.getCell())) {
                int endColIndexNew = roCell.getColIndex() + roCell.getColsRepeated() - 1;
                range = new DataRange(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), endColIndexNew);
            }
            rowIndex += roCell.getRowsRepeated();
        }
        
        // Left
        for(int rowIndex = range.getStartRowIndex(); rowIndex <= range.getEndRowIndex(); ) {
            if(range.getStartColIndex() <= 0) {
                break;
            }
            ReadOnlyCell roCell = RangeIterator.getReadOnlyCellAtSheetFromShell(sheet, rowIndex, range.getStartColIndex() - 1, false);
            if(!isValueEmpty(roCell.getCell())) {
                int startColIndexNew = roCell.getColIndex(); //i.e dataRange.getStartColIndex() - 1
                range = new DataRange(range.getAssociatedSheetName(), range.getStartRowIndex(), startColIndexNew, range.getEndRowIndex(), range.getEndColIndex());
            }
            rowIndex += roCell.getRowsRepeated();
        }
        
        // Bottom + Bottom Left Diagonal + Bottom Right Diagonal
        for(int colIndex = Math.max(0, range.getStartColIndex() - 1); colIndex <= Math.min(Utility.MAXNUMOFCOLS - 1, range.getEndColIndex() + 1); ) {
            if(range.getEndRowIndex() >= Utility.MAXNUMOFROWS - 1) {
                break;
            }
            ReadOnlyCell roCell = RangeIterator.getReadOnlyCellAtSheetFromShell(sheet, range.getEndRowIndex() + 1, colIndex, false);
            if(!isValueEmpty(roCell.getCell())) {
                int endRowIndexNew = roCell.getRowIndex() + roCell.getRowsRepeated() - 1;
                range = new DataRange(range.getAssociatedSheetName(), range.getStartRowIndex(), range.getStartColIndex(), endRowIndexNew, range.getEndColIndex());
            }
            colIndex += roCell.getColsRepeated();
        }
        
        // Top + Top Left Diagonal + Top Right Diagonal
        for(int colIndex = Math.max(0, range.getStartColIndex() - 1); colIndex <= Math.min(Utility.MAXNUMOFCOLS - 1, range.getEndColIndex() + 1); ) {
            if(range.getStartRowIndex() <= 0) {
                break;
            }
            ReadOnlyCell roCell = RangeIterator.getReadOnlyCellAtSheetFromShell(sheet, range.getStartRowIndex() - 1, colIndex, false);
            if(!isValueEmpty(roCell.getCell())) {
                int startRowIndexNew = roCell.getRowIndex();
                range = new DataRange(range.getAssociatedSheetName(), startRowIndexNew, range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex());
            }
            colIndex += roCell.getColsRepeated();
        }
        
        return range;
    }
    
    public Column getColumnsTree(Workbook workbook) {
        if(this.columnsTree == null) {
            this.getTableRange(workbook);
            Column rootTableColumn = new Column(null);
            boolean shouldReturnEmptyColumns;
            if(this.columnDetectionType == ColumnDetectionType.AUTO) {
                shouldReturnEmptyColumns = !detectIfTableRangeHasHeadersForAutoHeadersDetection(workbook, this.tableRange, this.headerCellPredicate);
            }
            else {
                shouldReturnEmptyColumns = ColumnDetectionType.TABLE_RANGE_DOESNT_HAVE_HEADERS.equals(this.columnDetectionType);
            }

            if(shouldReturnEmptyColumns) {
                return rootTableColumn;
    //            int tableRangeWidth = this.tableRange.getColSize();
    //            IntStream.range(0, tableRangeWidth).forEach(i -> {
    //                Column column = new Column(null);
    //                rootTableColumn.addChild(column);
    //            });
            }
            else {
                int maxHeadersDepthConsolidated = Math.max(1, Math.min(maxHeaderDepth, this.tableRange.getRowSize() - 1));
                List<Column> firstLevelColumns = detectChildren(workbook.getSheetByAssociatedName(tableRange.getAssociatedSheetName()), tableRange.getStartRowIndex(), tableRange.getStartColIndex(), tableRange.getEndColIndex(), maxHeadersDepthConsolidated);
                rootTableColumn.addChildren(firstLevelColumns);
            }
            this.columnsTree = rootTableColumn;
        }
        return this.columnsTree;
    }
    
    private boolean detectIfTableRangeHasHeadersForAutoHeadersDetection(Workbook workbook, DataRange tableRange, Predicate<ReadOnlyCell> headerCellPredicate) {
        if(tableRange.getRowSize() == 1) {
            return false;
        }
        RangeIterator topRowRangeIterator = new RangeIterator(workbook.getSheetByAssociatedName(tableRange.getAssociatedSheetName()), tableRange.getStartRowIndex(), tableRange.getStartColIndex(), tableRange.getStartRowIndex(), tableRange.getEndColIndex(), RangeIterator.IterationStartPositionEnum.TOP_LEFT, true, false, false, false, false, true);
        while(topRowRangeIterator.hasNext()) {
            ReadOnlyCell roCell = topRowRangeIterator.next();
            if(headerCellPredicate.test(roCell)) {
                return true;
            }
        }
        return false;
    }
    
    private static List<Column> detectChildren(Sheet sheet, int rowIndex, int startColumnIndex, int endColumnIndex, int depthRemaining) {
        SortedMap<Integer, Column> startColumnIndexToColumn = new TreeMap<>();
        for(int columnIndex = startColumnIndex; columnIndex <= endColumnIndex; columnIndex++) {
            Cell cell = sheet.getReadOnlyCellFromShell(rowIndex, columnIndex).getCell();
            if(isValueEmpty(cell)){
                if(startColumnIndexToColumn.isEmpty()) {
                    Column childColumn = new Column(null);
                    startColumnIndexToColumn.put(columnIndex, childColumn);
                }
            } else {
                Column childColumn = getColumnFromHeaderCell(cell);
                startColumnIndexToColumn.put(columnIndex, childColumn);
            }
        }
        
        if(isSplitIntoUnits(startColumnIndexToColumn, endColumnIndex)) {
            return new ArrayList<>(startColumnIndexToColumn.values());
        }
        
        if(depthRemaining == 1){
            splitIntoUnits(startColumnIndexToColumn, endColumnIndex);
            return new ArrayList<>(startColumnIndexToColumn.values());
        }
        
        for(Map.Entry<Integer, Column> entry: startColumnIndexToColumn.entrySet()) {
            Column childColumn = entry.getValue();
            int childStartColumnIndex = entry.getKey();
            int childEndColumnIndex;
            SortedMap<Integer, Column> tailMap = startColumnIndexToColumn.tailMap(childStartColumnIndex + 1);
            if(tailMap.isEmpty()) {
                childEndColumnIndex = endColumnIndex;
            }
            else{
                childEndColumnIndex = tailMap.firstKey() - 1;
            }
            Collection<Column> grandChildrenColumns = detectChildren(sheet, rowIndex + 1, childStartColumnIndex, childEndColumnIndex, depthRemaining - 1);
            childColumn.addChildren(grandChildrenColumns);
        }
        return new ArrayList<>(startColumnIndexToColumn.values());
    }
    
    private static boolean isSplitIntoUnits(SortedMap<Integer, Column> startColumnIndexToColumn, int endColumnIndex) {
        int minColumnIndex = startColumnIndexToColumn.firstKey();
        int maxColumnIndex = endColumnIndex;
        return IntStream.range(minColumnIndex, maxColumnIndex + 1).allMatch(columnIndex -> startColumnIndexToColumn.containsKey(columnIndex));
    }
    
    private static void splitIntoUnits(SortedMap<Integer, Column> startColumnIndexToColumn, int endColumnIndex) {
        int minColumnIndex = startColumnIndexToColumn.firstKey();
        int maxColumnIndex = endColumnIndex;
        IntStream.range(minColumnIndex, maxColumnIndex + 1).forEach(columnIndex -> startColumnIndexToColumn.putIfAbsent(columnIndex, new Column(null)));
    }
    
    private static Column getColumnFromHeaderCell(Cell cell) {
        Value value = cell == null ? null : cell.getValue();
        if(value == null || Value.EMPTY_VALUE.equals(value)) {
            return new Column(null);
        }
        SpreadsheetSettings spreadsheetSettings = cell.getRow().getSheet().getWorkbook().getSpreadsheetSettings();
        String columnHeader = value.getValueString(spreadsheetSettings);
        return new Column(columnHeader);
    }
    
    public List<String> getFlattenedColumnHeaders(Workbook workbook) {
        Column columnTree = this.getColumnsTree(workbook);
        List<String> flattenedColumnHeaders = new LinkedList<>();
        for(Column column: columnTree.getChildren()) {
            List<String> flattenedDescendantColumnHeaders = getFlattenedColumnHeader(column);
            flattenedColumnHeaders.addAll(flattenedDescendantColumnHeaders);
        }
        return flattenedColumnHeaders;
    }
    
    private static List<String> getFlattenedColumnHeader(Column column) {
        List<String> flattenedColumnHeaders = new LinkedList<>();
        for(Column child: column.getChildren()) {
            List<String> flattenedDescendantColumnHeaders = getFlattenedColumnHeader(child);
            flattenedColumnHeaders.addAll(flattenedDescendantColumnHeaders);
        }
        if(column.getChildren().isEmpty()) {
            flattenedColumnHeaders.add(column.getHeader() == null ? "" : column.getHeader().replace("/", "//"));
        } else {
            String columnHeader = (column.getHeader() == null ? "" : column.getHeader().replace("/", "//")) + "/";
            flattenedColumnHeaders = flattenedColumnHeaders
                                        .stream()
                                        .map(incompleteFlattenedColumnHeader -> columnHeader + incompleteFlattenedColumnHeader)
                                        .collect(Collectors.toList());
        }
        return flattenedColumnHeaders;
    }
    
    private static boolean isValueEmpty(Cell cell) {
        return cell == null || cell.getValue() == null || cell.getValue().equals(Value.EMPTY_VALUE);
    }
    
    private static class DataCell {
        private final String asn;
        private final int rowIndex;
        private final int columnIndex;

        public DataCell(String asn, int rowIndex, int columnIndex) {
            this.asn = asn;
            this.rowIndex = rowIndex;
            this.columnIndex = columnIndex;
        }

        public String getAsn() {
            return asn;
        }

        public int getRowIndex() {
            return rowIndex;
        }

        public int getColumnIndex() {
            return columnIndex;
        }
        
        public DataRange toDataRange() {
            return new DataRange(this.asn, this.rowIndex, this.columnIndex, this.rowIndex, this.columnIndex);
        }
    }
    
    public enum ColumnDetectionType {
        TABLE_RANGE_HAS_HEADERS,
        TABLE_RANGE_DOESNT_HAVE_HEADERS,
        AUTO
    }
}
