//// $Id$

package com.adventnet.zoho.websheet.model.analyse;

import java.util.function.Predicate;

/**
 *
 * <AUTHOR>
 * @param <R> type of the raw input value objects (String or Value or Cell or Object , etc.) on which the acceptancePredicate can act
 */
public class DataType <R> {
    private final String name;
    private final Predicate<R> acceptancePredicate;
    
    public DataType(String name, Predicate<R> acceptancePredicate){
        this.name = name;
        this.acceptancePredicate = acceptancePredicate;
    }
    
    public DataType(DataType dataType){
        this(dataType.name, dataType.acceptancePredicate);
    }
    
    public boolean acceptAndSwallow(R rawValue){
        return acceptancePredicate.test(rawValue) && this.swallow(rawValue);
    }
    
    public boolean swallow(R rawValue){
        return true;
    }
    
    public String getName(){
        return this.name;
    }

    @Override
    public String toString() {
        return "DataType{" + " name = " + this.getName() + " }"; //No I18N
    }
}
